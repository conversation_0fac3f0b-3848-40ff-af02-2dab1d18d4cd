# Spec values outside of the 'envs' parent isn't associated with any environment
# it is used for tasks like build, which can't have any specific environmental information
apps:
  #this is in the shared spec but not cluster def?
  __base__:
    __anchor__: app
    __name__: null
  ibizaml:
    type: resources
    deploymentGroups:
      - "provisioning_test"
envs:
  services-all-base:
    spec:
      deploy:
        namespace: ibizaml-{{env.__name__}}
        namespaceSetting:
          annotations:
          - key: allowCleanup
            value: "false"
        azure:
          group: "{{deploy.azure.syndicationVaultResourceGroup}}"
          keyvault: "{{deploy.azure.syndicationVault}}"
      #-----apps
      apps:
        __base__: # the __base__ app gets inherited by all other apps
          __name__: ~ # this instructs conf to inject the app name as app.__name__
          k8s: {}
        ibizaml:
          azure:
            storage:
              viennamlstor:
                  __anchor__: storage
                  __name__: ~ # should be overriden when inherited
                  name: "{{storage.__name__}}"
  ussec-base:
    spec:
      apps:
        ibizaml:
          azure:
            storage:
              viennamlstor:
                name: "ussecamlhostsvc"
  usnat-base:
    spec:
      apps:
        ibizaml:
          azure:
            storage:
              viennamlstor:
                name: "usnatamlhostsvc"
