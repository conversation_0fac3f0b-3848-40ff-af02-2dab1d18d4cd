param storage_name string

resource storage 'Microsoft.Storage/storageAccounts@2019-06-01' = {
    name: storage_name
    location: '{{deploy.azure.region}}'
    properties: {
        supportsHttpsTrafficOnly: true
        minimumTlsVersion: 'TLS1_2'
    }
    sku: {
        name: 'Standard_RAGRS'
    }
    kind: 'StorageV2'
    resource storage_blob 'blobServices@2019-06-01' = {
        name: 'default'
        resource storage_blob_container 'containers@2019-06-01' = {
            name: 'extension'
            properties: {
                publicAccess: 'Container'
            }
        }
    }
}

var keyVaultName = '{{deploy.azure.keyvault}}'
resource kvConnectionString 'Microsoft.KeyVault/vaults/secrets@2018-02-14' = {
    name: '${keyVaultName}/AmlAccountConnectionString'
    properties: {
        value: 'DefaultEndpointsProtocol=https;AccountName=${storage_name};AccountKey=${listKeys(storage.id, storage.apiVersion).keys[0].value};EndpointSuffix=${environment().suffixes.storage}'
    }
    tags: {
        'Type': 'Storage'
        'Source': storage.id
    }
}
