# Create an "ibiza"/"releases"/"target" branch.
# This essentially "snapshots" the source branch into the "ibiza"/"releases"/"target" branch.
param (
    [Parameter()]
    [ValidateNotNullOrEmpty()]
    [string]$repoUrl=$(throw "repoUrl is required"),

    [Parameter()]
    [string]$releaseBranch = "ibiza/releases/"
)

if ([string]::IsNullOrEmpty($sourceBranch))
{
    $sourceBranch = "origin/master"
}

if ([string]::IsNullOrEmpty($currentDate))
{
    $currentDate = Get-Date -format "yyyy-MMM-dd";
}

$releaseBranch = $releaseBranch + $currentDate;

if ([string]::CompareOrdinal($releaseBranch, "master") -eq 0)
{
    throw "This script will not overwrite master"
}


# CAUTION don't print $repoUrl as it contains an access token for write access to the repo.
Write-Host "Forking to release branch. Source = $sourceBranch, Ibiza/Releases/Target branch = $releaseBranch"

git config --global core.autocrlf false
git config --global push.default simple

Write-Host "Create and Check out $releaseBranch"
git checkout -b $releaseBranch
Write-Host "Resetting hard against $sourceBranch"
git reset --hard $sourceBranch

# Version tag is pushed before release head is to avoid race condition
# as builds triggered by release branch changes will also try to
# set the version if it is missing (to just a new rev number).
if (!$NoVersionTag)
{
    $tag = "Ibiza-Master-Cut-" + $env:BUILD_BUILDNUMBER
    Write-Host "Tagging branch with tag: $tag"
    git tag $tag
    git push $repoUrl $tag
}

Write-Host "Force pushing to remote repo, not printed due to secrets"
git push --force -q --repo=$repoUrl
