#!/usr/bin/env bash
set -ex

PARENT_DIR=$(realpath `dirname $0`/..)

[ ! -d /tmp/aml-venv ] && python -m venv /tmp/aml-venv
source /tmp/aml-venv/bin/activate
wget -O - https://aka.ms/msftkube-bootstrapper.sh | bash

cd $PARENT_DIR

# don't use the shared spec. We already have it
# remove the line below if used outside the msftkube repo
# export VIENNA_ENV_FETCH_URI=https://msftkube.blob.core.windows.net/public/vienna/empty
msftkube "$@"
