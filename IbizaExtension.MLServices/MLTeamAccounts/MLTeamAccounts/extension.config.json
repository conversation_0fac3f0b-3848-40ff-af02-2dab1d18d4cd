{"$schema": "./Client/ReactViews/node_modules/@microsoft/azureportal-build/lib/definitions/extension.config.schema.json", "name": "Microsoft_Azure_MLTeamAccounts", "hostingServiceName": "ext", "version": "1.0.0.$(Date:yyMMdd-HHmm)", "sourceDirectory": "./Client/ReactViews", "outputDirectory": "./Output", "hasKnockoutContent": true, "environment": {"canary.portal.azure.com": {"webWorkspace2BaseUrl": "https://eastus2euap.ml.azure.com", "armEndpoint": "https://management.azure.com/", "apiSuffix": "api.azureml.ms", "cogServicesAccountsApiVersion": "2024-10-01", "isPublicCloud": "true", "argbrowseoptions": {"MachineLearningServices": "Force", "AIStudio": "Force"}, "assettypeoptions": {"AIStudio": {"options": "HideAssetType"}, "AIStudioCreate": {"options": "HideAssetType"}, "MachineLearningServicesCreate": {"options": "HideAssetType"}, "Llama2": {"options": "HideAssetType"}, "Mistral": {"options": "HideAssetType"}, "Cohere": {"options": "HideAssetType"}, "Nixtla": {"options": "HideAssetType"}, "Core42": {"options": "HideAssetType"}, "ModelProvider": {"options": "HideAssetType"}}, "flights": {"optionalAppInsights": false}}, "*": {"appInsightsApiVersion": "2015-05-01", "argbrowseoptions": {"MachineLearningServices": "Force", "AIStudio": "Force"}, "armApiVersion": "2014-04-01", "armResourcesApiVersion": "2019-09-01", "armResourceGraphApiVersion": "2021-03-01", "cogServicesApiVersion": "2023-05-01", "cogServicesAccountsApiVersion": "2024-10-01", "armEndpoint": "https://management.azure.com/", "classicStorageApiVersion": "2023-01-01", "containerRegistryApiVersion": "2017-10-01", "databricksApiVersion": "2018-04-01", "enablePortalLogging": "true", "featureCrossLocationComputeSubscriptionAllowList": "dfdb3164-4098-4867-ba41-d826dba2f587,2251f94a-6bc9-4e5e-b51d-3ccaed92d69d,428ad92a-9a7a-47f9-8383-399a14629c48,0c884238-7905-43a8-a14f-bed5334a4fa1,4aaa645c-5ae2-4ae9-a17a-84b9023bc56a,9388cd44-16b4-4465-888e-5a0f3d01224f,11c46bd2-732e-4dd9-9a81-2c826296ee99,096d94cf-53ad-4a5c-a338-5b4343565f66,f65868c8-7b94-4030-b536-1284e4450aad,23991b2f-de27-4f36-a48a-3dddd1f82cfc,964a24a8-8835-43c1-9633-7d78841facf1,a0a92bea-0220-4755-a110-7d1b3f4fd24b,25d09c71-4331-4822-ba70-03f5a1d09bdc,a369405a-18ff-4489-8c5b-aef0a2cc981e,619f1ab9-764e-4308-8360-da885b1c9eaa,b2e05423-937d-4f29-85e7-525ffb334997,154f8fe6-ae93-4e43-a3f0-de54d735024c,c93cf9f1-e764-4d70-b47d-c2a065e64826,2a57aa3e-853b-4efc-a58b-a8f0a7e9eccc,16844f2d-2024-4dd7-b16b-f3e216df0bba,2cd9f619-1356-456c-abf4-668703e986cd,c0cfd37a-a3de-43d0-a2aa-f375949abb81,0f64a630-b912-4501-ad57-262a9e12637c", "keyVaultApiVersion": "2019-09-01", "locationsApiVersion": "2019-11-01", "featuresProviderApiVersion": "2021-07-01", "machineLearningRegistryApiVersion": "2022-10-01-preview", "machineLearningServiceOverviewDocumentationUrl": "https://go.microsoft.com/fwlink/?linkid=2112881", "machineLearningServicesApiVersion": "2024-10-01", "machineLearningServicesApiVersionPreview": "2024-10-01-preview", "machineLearningServicesAppInsightsUrl": "https://go.microsoft.com/fwlink/?linkid=2002901", "machineLearningServicesContainerRegistryUrl": "https://go.microsoft.com/fwlink/?linkid=2003001", "machineLearningServicesDocumentationUrl": "https://go.microsoft.com/fwlink/?linkid=872674", "aiStudioDocumentationUrl": "https://aka.ms/aistudio/docs", "aiStudioModelCatalogUrl": "https://aka.ms/aistudio/landing/model_catalog", "machineLearningServicesEnterpriseEditionUrl": "https://azure.microsoft.com/pricing/details/machine-learning/", "machineLearningServicesForumUrl": "https://aka.ms/aml-forum-service", "machineLearningServicesGitHubUrl": "https://aka.ms/aml-notebooks", "machineLearningServicesKeyVaultUrl": "https://go.microsoft.com/fwlink/?linkid=2003000", "machineLearningServicesNotebookUrl": "https://notebooks.azure.com/azureml/libraries/azureml-getting-started", "machineLearningServicesManageFeatureStoreUrl": "https://aka.ms/azureml-registries-manage", "machineLearningServicesManageRegistryUrl": "https://aka.ms/azureml-registries-manage", "machineLearningServicesShareRegistryUrl": "https://aka.ms/azureml-registries-share", "machineLearningServicesSoftDeleteDocUrl": "https://aka.ms/azureml-soft-delete", "machineLearningServicesStorageUrl": "https://go.microsoft.com/fwlink/?linkid=2002900", "machineLearningServicesStudioUrl": "https://studio.azureml.com", "machineLearningServicesWarningUrl": "https://go.microsoft.com/fwlink/?linkid=2002209", "modelManagementApiVersion": "2020-09-01-preview", "onlineEndpointsApiVersion": "2022-05-01", "providerCheckApiVersion": "2017-05-01", "quotaUsageApiVersion": "2020-04-01", "showPublicLinks": true, "softDeleteApiVersion": "2022-10-01-preview", "storageApiVersion": "2019-04-01", "studioDocumentationUrl": "https://aka.ms/aml/documents", "trustedDomains": ["*.api.azureml-test.ms", "*.api.azureml.ms", "*.azure.com"], "updateQuotasApiVersion": "2020-04-01", "vmSizesApiVersion": "2018-11-19", "webWorkspace2BaseUrl": "https://ml.azure.com", "webAIStudioBaseUrl": "https://ai.azure.com", "assettypeoptions": {"AIStudio": {"options": "HideAssetType"}, "AIStudioCreate": {"options": "HideAssetType"}, "MachineLearningServicesCreate": {"options": "HideAssetType"}, "Llama2": {"options": "HideAssetType"}, "Mistral": {"options": "HideAssetType"}, "Cohere": {"options": "HideAssetType"}, "Nixtla": {"options": "HideAssetType"}, "Core42": {"options": "HideAssetType"}, "ModelProvider": {"options": "HideAssetType"}}, "flights": {"optionalAppInsights": false}}, "df.onecloud.azure-test.net": {"webWorkspace2BaseUrl": "https://master.ml.azure.com", "armEndpoint": "https://api-dogfood.resources.windows-int.net", "isPublicCloud": "true", "cogServicesAccountsApiVersion": "2024-10-01", "argbrowseoptions": {"MachineLearningServices": "Force", "AIStudio": "Force"}, "assettypeoptions": {"AIStudio": {"options": "HideAssetType"}, "AIStudioCreate": {"options": "HideAssetType"}, "MachineLearningServicesCreate": {"options": "HideAssetType"}, "Llama2": {"options": "HideAssetType"}, "Mistral": {"options": "HideAssetType"}, "Cohere": {"options": "HideAssetType"}, "Nixtla": {"options": "HideAssetType"}, "Core42": {"options": "HideAssetType"}, "ModelProvider": {"options": "HideAssetType"}}, "flights": {"optionalAppInsights": false}}, "ms.portal.azure.com": {"webWorkspace2BaseUrl": "https://ml.azure.com", "armEndpoint": "https://management.azure.com/", "apiSuffix": "api.azureml.ms", "cogServicesAccountsApiVersion": "2024-10-01", "workspaceBasedAppInsightsLocations": ["westus3", "jioindiawest", "canadaeast", "swedencentral"], "argbrowseoptions": {"MachineLearningServices": "Force", "AIStudio": "Force"}, "assettypeoptions": {"AIStudio": {"options": "HideAssetType"}, "AIStudioCreate": {"options": "HideAssetType"}, "MachineLearningServicesCreate": {"options": "HideAssetType"}, "Llama2": {"options": "HideAssetType"}, "Mistral": {"options": "HideAssetType"}, "Cohere": {"options": "HideAssetType"}, "Nixtla": {"options": "HideAssetType"}, "Core42": {"options": "HideAssetType"}, "ModelProvider": {"options": "HideAssetType"}}, "flights": {"optionalAppInsights": false}}, "portal.azure.cn": {"webWorkspace2BaseUrl": "https://studio.ml.azure.cn", "webAIStudioBaseUrl": "https://ai.azure.cn", "armEndpoint": "https://management.chinacloudapi.cn/", "apiSuffix": "api.ml.azure.cn", "cogServicesAccountsApiVersion": "2024-10-01", "privateDnsZoneNames": "privatelink.api.ml.azure.cn,privatelink.notebooks.chinacloudapi.cn", "disableADSLGen2": "true", "argbrowseoptions": {"MachineLearningServices": "Force", "AIStudio": "Force"}, "assettypeoptions": {"AIStudioCreate": {"options": "HideAssetType"}, "MachineLearningServicesCreate": {"options": "HideAssetType"}, "Llama2": {"options": "HideAssetType"}, "Mistral": {"options": "HideAssetType"}, "Cohere": {"options": "HideAssetType"}, "Nixtla": {"options": "HideAssetType"}, "Core42": {"options": "HideAssetType"}, "ModelProvider": {"options": "HideAssetType"}, "MachineLearningServices": {"kinds": {"Hub": {"options": "HideInstances"}, "Project": {"options": "HideInstances"}, "Lean": {"options": "HideInstances"}}}}, "trustedDomains": ["*.ml.azure.cn", "*.chinacloudapi.cn"], "flights": {"optionalAppInsights": false}}, "portal.azure.com": {"webWorkspace2BaseUrl": "https://ml.azure.com", "webAIStudioBaseUrl": "https://ai.azure.com", "armEndpoint": "https://management.azure.com/", "apiSuffix": "api.azureml.ms", "cogServicesAccountsApiVersion": "2024-10-01", "workspaceBasedAppInsightsLocations": ["westus3", "jioindiawest", "canadaeast", "swedencentral"], "aDLSGen2AllowListSubscriptions": ["2d385bf4-0756-4a76-aa95-28bf9ed3b625", "4568b512-4f4b-4ddb-82ba-cd0e95c8b9c4", "581e7eaa-56a3-4ab6-83a1-0d03be113a83", "867f5804-e06c-428c-84cc-74e3ed746034", "dfdb3164-4098-4867-ba41-d826dba2f587", "2562d4a5-865a-48c9-96cc-df30f1782d37", "e629e317-4df0-4251-81cb-ad8f6872a22c", "422895b2-0fda-4709-9ab8-7df1295fedd7", "4c115fd1-4c93-48e7-993e-43599a768bf9", "791ee308-e8b6-43f1-bde2-b9303a8f7faf", "65e18d6f-da4c-47f0-b852-c40badb81a82", "9c0f91b8-eb2f-484c-979c-15848c098a6b", "8570a647-78f9-4c73-8d5d-a764122bb6e8", "a7a0d39b-934a-49e7-bdcb-caa0a29ca3d2", "cea4f76a-b178-419d-97b7-eecd0ffa6d7c", "bbeb1a02-a478-48da-9456-77e01d809999"], "argbrowseoptions": {"MachineLearningServices": "Force", "AIStudio": "Force"}, "assettypeoptions": {"AIStudio": {"options": "HideAssetType"}, "AIStudioCreate": {"options": "HideAssetType"}, "MachineLearningServicesCreate": {"options": "HideAssetType"}, "Llama2": {"options": "HideAssetType"}, "Mistral": {"options": "HideAssetType"}, "Cohere": {"options": "HideAssetType"}, "Nixtla": {"options": "HideAssetType"}, "Core42": {"options": "HideAssetType"}, "ModelProvider": {"options": "HideAssetType"}}, "flights": {"optionalAppInsights": false}}, "portal.azure.eaglex.ic.gov": {"webWorkspace2BaseUrl": "https://ml.azure.eaglex.ic.gov", "webAIStudioBaseUrl": "https://ai.azure.eaglex.ic.gov", "armEndpoint": "https://management.azure.eaglex.ic.gov/", "apiSuffix": "api.ml.azure.eaglex.ic.gov", "cogServicesAccountsApiVersion": "2024-10-01", "privateDnsZoneNames": "privatelink.api.ml.azure.eaglex.ic.gov,privatelink.notebookapis.azure.eaglex.ic.gov", "showPublicLinks": false, "machineLearningServiceOverviewDocumentationUrl": "https://docs.eaglex.ic.gov/en-us/azure/machine-learning/overview-what-is-azure-machine-learning?view=azureml-api-2", "machineLearningServicesDocumentationUrl": "https://docs.eaglex.ic.gov/en-us/azure/machine-learning?view=azureml-api-2", "aiStudioDocumentationUrl": "https://aka.eaglex.ic.gov/aistudio/docs", "aiStudioModelCatalogUrl": "https://aka.eaglex.ic.gov/aistudio/landing/model_catalog", "machineLearningServicesManageFeatureStoreUrl": "https://aka.eaglex.ic.gov/azureml-registries-manage", "machineLearningServicesManageRegistryUrl": "https://aka.eaglex.ic.gov/azureml-registries-manage", "machineLearningServicesShareRegistryUrl": "https://aka.eaglex.ic.gov/azureml-registries-share", "machineLearningServicesSoftDeleteDocUrl": "https://aka.eaglex.ic.gov/azureml-soft-delete", "machineLearningServicesWarningUrl": "https://docs.eaglex.ic.gov/en-us/azure/azure-resource-manager/troubleshooting/error-register-resource-provider?tabs=azure-cli#solution-3---azure-portal", "trustedDomains": ["*.azure.eaglex.ic.gov", "*.ml.azure.eaglex.ic.gov", "*.api.ml.azure.eaglex.ic.gov"], "argbrowseoptions": {"MachineLearningServices": "Force", "AIStudio": "Force"}, "assettypeoptions": {"AIStudio": {"variant": "usnat"}, "AIStudioCreate": {"options": "HideAssetType"}, "MachineLearningServicesCreate": {"options": "HideAssetType"}, "Llama2": {"options": "HideAssetType"}, "Mistral": {"options": "HideAssetType"}, "Cohere": {"options": "HideAssetType"}, "Nixtla": {"options": "HideAssetType"}, "Core42": {"options": "HideAssetType"}, "ModelProvider": {"options": "HideAssetType"}}, "flights": {"optionalAppInsights": false}}, "portal.azure.microsoft.scloud": {"webWorkspace2BaseUrl": "https://ml.azure.microsoft.scloud", "webAIStudioBaseUrl": "https://ai.azure.microsoft.scloud", "armEndpoint": "https://management.azure.microsoft.scloud/", "apiSuffix": "api.ml.azure.microsoft.scloud", "cogServicesAccountsApiVersion": "2024-10-01", "privateDnsZoneNames": "privatelink.api.ml.azure.microsoft.scloud,privatelink.notebookapis.azure.microsoft.scloud", "showPublicLinks": false, "machineLearningServiceOverviewDocumentationUrl": "https://docs.microsoft.scloud/en-us/azure/machine-learning/overview-what-is-azure-machine-learning?view=azureml-api-2", "machineLearningServicesDocumentationUrl": "https://docs.microsoft.scloud/en-us/azure/machine-learning?view=azureml-api-2", "aiStudioDocumentationUrl": "https://aka.microsoft.scloud/aistudio/docs", "aiStudioModelCatalogUrl": "https://aka.microsoft.scloud/aistudio/landing/model_catalog", "machineLearningServicesManageFeatureStoreUrl": "https://aka.microsoft.scloud/azureml-registries-manage", "machineLearningServicesManageRegistryUrl": "https://aka.microsoft.scloud/azureml-registries-manage", "machineLearningServicesShareRegistryUrl": "https://aka.microsoft.scloud/azureml-registries-share", "machineLearningServicesSoftDeleteDocUrl": "https://aka.microsoft.scloud/azureml-soft-delete", "machineLearningServicesWarningUrl": "https://docs.microsoft.scloud/en-us/azure/azure-resource-manager/troubleshooting/error-register-resource-provider?tabs=azure-cli#solution-3---azure-portal", "trustedDomains": ["*.azure.microsoft.scloud", "*.ml.azure.microsoft.scloud", "*.api.ml.azure.microsoft.scloud"], "argbrowseoptions": {"MachineLearningServices": "Force", "AIStudio": "Force"}, "assettypeoptions": {"AIStudio": {"variant": "ussec"}, "AIStudioCreate": {"options": "HideAssetType"}, "MachineLearningServicesCreate": {"options": "HideAssetType"}, "Llama2": {"options": "HideAssetType"}, "Mistral": {"options": "HideAssetType"}, "Cohere": {"options": "HideAssetType"}, "Nixtla": {"options": "HideAssetType"}, "Core42": {"options": "HideAssetType"}, "ModelProvider": {"options": "HideAssetType"}}, "flights": {"optionalAppInsights": false}}, "portal.azure.us": {"webWorkspace2BaseUrl": "https://studio.ml.azure.us", "webAIStudioBaseUrl": "https://ai.azure.us", "armEndpoint": "https://management.usgovcloudapi.net/", "apiSuffix": "api.ml.azure.us", "cogServicesAccountsApiVersion": "2024-10-01", "privateDnsZoneNames": "privatelink.api.ml.azure.us,privatelink.notebooks.usgovcloudapi.net", "disableADSLGen2": "true", "argbrowseoptions": {"MachineLearningServices": "Force", "AIStudio": "Force"}, "assettypeoptions": {"AIStudio": {"variant": "fairfax"}, "AIStudioCreate": {"options": "HideAssetType"}, "MachineLearningServicesCreate": {"options": "HideAssetType"}, "Llama2": {"options": "HideAssetType"}, "Mistral": {"options": "HideAssetType"}, "Cohere": {"options": "HideAssetType"}, "Nixtla": {"options": "HideAssetType"}, "Core42": {"options": "HideAssetType"}, "ModelProvider": {"options": "HideAssetType"}}, "trustedDomains": ["*.ml.azure.us", "*.usgovcloudapi.net"], "flights": {"optionalAppInsights": false}}}, "build": {"fluent": {"allowedVersions": {"Pages/AppInsights/ChangeAppInsightsPane.ReactView": "fluent1And2", "Pages/AssociatedWorkspaces/AssociatedWorkspaces.ReactView": "fluent1And2", "Pages/ContainerRegistry/ChangeContainerRegistryPane.ReactView": "fluent1And2", "Pages/DeleteHub/DeleteHubPane.ReactView": "fluent1And2", "Pages/DeleteHub/DeleteHubStatusPane.ReactView": "fluent1And2", "Pages/EncryptionSettings/EncryptionSettings.ReactView": "fluent1And2", "Pages/KeysAndEndpoints/KeysAndEndpoints.ReactView": "fluent1And2", "Pages/Overview/Overview.ReactView": "fluent1And2"}}}, "deployment": {"generationType": "all", "routePrefix": "mlserviceworkspaces", "packageOutputDirectory": "./Output"}}