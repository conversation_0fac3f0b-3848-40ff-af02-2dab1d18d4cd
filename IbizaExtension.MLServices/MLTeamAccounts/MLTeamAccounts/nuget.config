<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <config>
    <!-- package restore path for nuget.exe -->
    <add key="repositoryPath" value="../packages" />
    <!-- used by dotnet restore as dotnet restore does not respect respositoryPath -->
    <add key="globalPackagesFolder" value="../packages" />
  </config>
  <packageSources>
    <clear />
    <add key="Vienna" value="https://pkgs.dev.azure.com/msdata/_packaging/Vienna/nuget/v3/index.json" />
  </packageSources>
</configuration>