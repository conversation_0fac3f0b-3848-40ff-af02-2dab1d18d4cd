﻿{
  "$schema": "https://gallery.azure.com/schemas/2014-09-01/manifest.json#",
  "name": "OnlineEndpoint",
  "publisher": "Microsoft",
  "version": "1.0.0-preview",
  "displayName": "ms-resource:displayName",
  "publisherDisplayName": "ms-resource:publisherDisplayName",
  "publisherLegalName": "ms-resource:publisherDisplayName",
  "summary": "ms-resource:summary",
  "description": "ms-resource:description",
  "longSummary": "ms-resource:longSummary",
  "longDescription": "ms-resource:description",
  "properties": [],
  "artifacts": [
    {
      "name": "CreateResource",
      "type": "Template",
      "path": "DeploymentTemplates\\CreateResource.json",
      "isDefault": true
    }
  ],
  "links": [
    {
      "id": "0",
      "displayName": "ms-resource:documentation",
      "uri": "https://go.microsoft.com/fwlink/?linkid=2102624"
    }
  ],
  "icons": {
    "Small": "Icons\\Small.png",
    "Medium": "Icons\\Medium.png",
    "Large": "Icons\\Large.png",
    "Wide": "Icons\\Wide.png"
  },
  "products": [],
  "screenshots": [
    "Screenshots\\Image01.png"
  ],
  "categories": [
    "analytics",
    "intelligence"
  ],
  "keywords": [
    "Machine Learning",
    "machine",
    "learning",
    "workspace",
    "Azure Machine Learning",
    "train models",
    "deploy models",
    "machine learning frameworks",
    "data science",
    "deep learning",
    "machine learning service workspace",
    "azure machine learning service workspace"
  ],
  "uiDefinition": {
    "path": "UIDefinition.json"
  },
  "filters": [
    {
      "type": "HideKey",
      "value": "MLAppEnabled"
    }
  ]
}