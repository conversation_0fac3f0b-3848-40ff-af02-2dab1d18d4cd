/**
 * When a consuming module re-exports a member of a namespace, it must use the legacy import assignment syntax,
 * to be able to acquire the deep member without coercing it to a different type.
 * Preserving the original type is especially important for re-exported const enums, because they are a special "type"
 * that can be used as both a value and a type, and this encapsulated information must not be lost.
 * But when using the legacy import assignment syntax, the following problem exists:
 * During transpilation, TypeScript performs import elision and removes unnecessary module dependencies for type imports,
 * including const enum imports. But if preserveConstEnums is set, TypeScript should not perform import elision for const enums,
 * and should keep the original module dependencies.
 * However, the legacy import assignment syntax has a bug, and it performs import elision nevertheless.
 * This causes a runtime error, where the module containing the preserved enum is not loaded as a dependency.
 * In order to enforce a non-type dependency which will resist removal by the legacy import assigment syntax,
 * this exported dummy constant can be imported by consuming modules.
 * See https://github.com/microsoft/TypeScript/issues/35701.
 */
export declare const __internal_ExplicitModuleDependency = 0;
/**
 * Helper type for determining if a return / parameter type is "Proxyable" across the postMessage boundary.
 */
export type Proxyable = PrimitiveObject | ProxyablePrimitive | (PrimitiveObject | ProxyablePrimitive)[];
/**
 * Helper type for determining if the supplied function map correctly declares Return and Parameter types for each function
 */
export type ValidatedFunctionMap<T> = {
    [Key in keyof T]: ValidatedEntry<T[Key]>;
};
/**
 * Obsolete
 */
export interface Obsolete {
    /**
     * Obsolete
     */
    Obsolete: "true";
}
/**
 * This interface provides typings for the FunctionProxy, which allows communication between your ReactModel and corresponding ReactView.
 * This can be useful for migration based scenarios, and support bi-directional function calling (you can invoke a function from the model or the view).
 * It provides strong type safety via the FunctionMap generic, which expects the shape of the function map to be:
 * ```typescript
 *     interface MyFunctionMap {
 *         functionName: {
 *             parameters: number;
 *             returns: string;
 *         };
 *         functionName2: {
 *             returns: Promise<{someReturnType: boolearn}>;
 *         };
 *     }
 *     // This can be invoked/registered with the name "functionName":
 *     invoke("functionName", 7).then(i => console.log(i))
 *     register("functionName", (num) => "test")
 * ```
 * Note: This crosses the post message boundary, and will not work if non-serializable data is passed for either return or parameter types,
 * correctly utilizing the function map, though slightly involved will catch these errors at build time (and will aid in future refactors).
 */
export interface FunctionProxy<FunctionMap extends ValidatedFunctionMap<FunctionMap>> {
    /**
     * Invoke a function that has been registered.
     *
     * @param name The name of the function that has been registered
     * @param parameters The parameters to pass to the function, null if you need to skip it
     * @param ignoreResponse Skip receiving a response, and ignore any errors thrown
     * @returns A promise that resolves to the response of the registered function, Promises are unwrapped by one level
     */
    invoke<T extends keyof JustParameterless<FunctionMap>>(name: T): DetermineReturnType<JustParameterless<FunctionMap>, T>;
    invoke<T extends keyof JustParameters<FunctionMap>>(name: T, parameters: FunctionMap[T] extends HasParameters ? FunctionMap[T]["parameters"] : never): DetermineReturnType<JustParameters<FunctionMap>, T>;
    invoke<T extends keyof JustParameterless<FunctionMap>>(name: T, parameters: null, ignoreResponse: true): DetermineReturnType<JustParameterless<FunctionMap>, T>;
    invoke<T extends keyof JustParameters<FunctionMap>>(name: T, parameters: FunctionMap[T] extends HasParameters ? FunctionMap[T]["parameters"] : never, ignoreResponse: true): DetermineReturnType<JustParameters<FunctionMap>, T>;
    /**
     * Register so it can be called from the other side. Promises returned will be unwrapped by a single layer. e.g returning "Promise<void>"" will still be Promise<void> on the caller side. Returning "void" will return "Promise<void>" on the caller side.
     *
     * @param name The name of the function to register as (must be a key of the FunctionMap)
     * @param handler The handler to call when the function is "invoked"
     */
    register<T extends keyof JustParameters<FunctionMap>, FuncInfo extends JustParameters<FunctionMap>[T] = JustParameters<FunctionMap>[T], HandlerReturnType extends FuncInfo extends ReturnsSomething ? FuncInfo["returns"] : void = FuncInfo extends ReturnsSomething ? FuncInfo["returns"] : void>(name: T, handler: (parameters: FuncInfo extends HasParameters ? FuncInfo["parameters"] : never) => HandlerReturnType | Promise<HandlerReturnType>): void;
    register<T extends keyof JustParameterless<FunctionMap>, FuncInfo extends JustParameterless<FunctionMap>[T] = JustParameterless<FunctionMap>[T], HandlerReturnType extends FuncInfo extends ReturnsSomething ? FuncInfo["returns"] : void = FuncInfo extends ReturnsSomething ? FuncInfo["returns"] : void>(name: T, handler: () => HandlerReturnType | Promise<HandlerReturnType>): void;
}
export type ProxyablePrimitive = number | string | Date | boolean | void | undefined;
export interface PrimitiveObject {
    [key: string]: PrimitiveObject | ProxyablePrimitive | (ProxyablePrimitive | PrimitiveObject)[] | readonly (ProxyablePrimitive | PrimitiveObject)[];
}
export type ReturnsSomething<T = Proxyable | Promise<Proxyable>> = {
    returns: T;
};
export type HasParameters<T = Proxyable> = {
    parameters: T;
};
export type JustParameters<T> = Pick<T, {
    [P in keyof T]: T[P] extends HasParameters ? P : never;
}[keyof T]>;
export type JustParameterless<T> = Pick<T, {
    [P in keyof T]: T[P] extends HasParameters ? never : P;
}[keyof T]>;
export type ValidatedEntry<T> = T extends HasParameters & ReturnsSomething ? HasParameters<any> & ReturnsSomething<any> extends T ? T : never : T extends HasParameters ? HasParameters<any> extends T ? T : never : T extends ReturnsSomething ? ReturnsSomething<any> extends T ? T : never : T extends {} ? {} extends T ? T : never : never;
export type DetermineReturnType<FunctionMap, T extends keyof FunctionMap> = FunctionMap[T] extends ReturnsSomething ? FunctionMap[T]["returns"] extends Promise<any> ? FunctionMap[T]["returns"] : Promise<FunctionMap[T]["returns"]> : void;
export type AtLeastOne<T, U = {
    [K in keyof T]: Pick<T, K>;
}> = Partial<T> & U[keyof U];
export type StringMap<T> = Record<string, T>;
export type ReadonlyStringMap<T> = Readonly<Record<string, T>>;
export interface SimpleBladeReference {
    /**
     * The name of the extension that contains the Blade.
     */
    readonly extensionName: string;
    /**
     * The name of the Blade.
     */
    readonly bladeName: string;
    /**
     * A map of parameters to be passed to the Blade.
     */
    readonly parameters?: StringMap<any>;
}
/**
 * Primitive type which has numbers, strings, Dates, and booleans
 */
export type Primitive = number | string | Date | boolean;
/**
 * An object which only contains primitives, arrays of primitives or objects which contain the same.
 */
export interface StringMapPrimitive extends Record<string, StringMapRecursive | Primitive | (Primitive | StringMapRecursive)[]> {
}
export type StringMapRecursive = StringMapPrimitive;
/**
 * The interface that represents an Arm event
 */
export interface ArmEvent {
    /**
     * The operation name of the event.
     */
    operationName: string;
    /**
     * The source to match. Has to be an exact match.
     */
    source: string;
    /**
     * The status to match.
     */
    status: string;
    /**
     * The sub status to match.
     */
    subStatus?: string;
    /**
     * The event correlation id.
     */
    correlationId: string;
    /**
     * The asset id.
     */
    assetId?: string;
    /**
     * The event timestamp.
     */
    timestamp: Date;
    /**
     * Additional event properties
     */
    properties?: Record<string, string>;
    /**
     * Set to true if this event is generated locally
     */
    isLocalEvent?: boolean;
    /**
     * Email id of user in case of server event.
     */
    caller?: string;
}
/**
 * Types related to the ReactView query APIs
 */
export declare namespace ReactQuery {
    /**
     * Scope at which data is cached, either for the entire user session or just the current ReactView.
     */
    type QueryEntryScope = "session" | "view";
    /**
     * Options when adding a cache entry
     */
    type QueryEntryOptions<TResult = unknown> = {
        /**
         * Number of seconds before the cache entry is invalid and removed from the cache.
         * Defaults to "never" if not specified.
         *
         * If a function is provided, it should return the Date timestamp when the cache entry should expire (not the number of seconds until expiration).
         * The value provided to the function will be undefined if the query is resolving for an rejected operation.
         */
        readonly expires?: number | ((value: TResult | undefined) => number);
        /**
         * Scope at which to store this entry (for the entire user session or just the current ReactView).
         * Defaults to "session" if not specified.
         */
        readonly scope?: QueryEntryScope;
        /**
         * Optional list of extensions that this cache entry can be shared with.
         */
        readonly sharedWith?: string[];
        /**
         * If true, don't apply structural sharing to resolved values before doing comparisons
         * and issuing callbacks. By default, structural sharing is applied so that identical
         * properties of subsequent results are re-used so that they can be effectively memoized.
         *
         * There is some cost to structural sharing which is usually negligible, but if the data
         * set is particularly large, it could become a bottleneck and you might consider setting
         * this option to true in that case.
         */
        readonly skipStructuralSharing?: boolean;
        /**
         * Number of seconds after which the query result is considered stale. The default is 5 seconds.
         * A newly bound query will by default be refetched (in the background) if its cached
         * value is stale. If this is an expensive query, this can be set to a large value
         * (or Number.MAX_SAFE_INTEGER) to avoid re-fetches unless refresh is explicitly called.
         *
         * Difference between expires and staleAfter:
         * - expires: after a cache entry has expired, it is thrown out of the cache. Any newly mounted
         *   queries will issue the query without having any initial/cached value.
         * - staleAfter: if a cache entry is stale (but hasn't expired yet), then newly mounted queries
         *   will initially render with the cached entry but will still issue a request in the background
         *   and re-render if the newer value is different from the cached entry.
         *
         * If a function is provided, it should return the Date timestamp when the cache entry is considered stale (not the number of seconds until it is stale).
         * The value provided to the function will be undefined if the query is resolving for an rejected operation.
         */
        readonly staleAfter?: number | ((value: TResult | undefined) => number);
    };
    /**
     * An entry for the ReactView Query store
     */
    type QueryEntry<TResult = unknown, TError = unknown> = {
        /**
         * Timestamp when the query completed
         */
        readonly completed: number;
        /**
         * Timestamp at which point the entry should be thrown out
         */
        readonly expires: number;
        /**
         * Sequence number that can be used as a tiebreaker if two entries from the same frame have the same timestamp.
         * (for example, if multiple operations are issued sequentially, like clear+get, on the same "tick")
         */
        readonly sequenceId: number;
        /**
         * If this entry is shared with other extensions, this is the extension that populated the entry.
         */
        readonly sharedBy?: string;
        /**
         * True if the data is particularly large and structural sharing should NOT be applied.
         */
        readonly skipStructuralSharing?: boolean;
        /**
         * Timestamp at which point the entry should be considered stale (and potentially refreshed)
         */
        readonly staleAfter: number;
        /**
         * Timestamp when the query started
         */
        readonly started: number;
    } & QueryEntryData<TResult, TError>;
    /**
     * The data from a ReactView Query
     */
    type QueryEntryData<TResult, TError = unknown> = QueryEntryResolvedData<TResult, TError> | {
        /**
         * The resolution state of the query
         */
        state: "unset";
    };
    /**
     * The resulting/resolved data from a ReactView Query
     */
    type QueryEntryResolvedData<TResult = unknown, TError = unknown> = {
        /**
         * The (successful) result of the query
         */
        readonly data: TResult;
        /**
         * The resolution state of the query
         */
        readonly state: "resolved";
    } | {
        /**
         * The error thrown by the query operation
         */
        readonly error: TError;
        /**
         * The resolution state of the query
         */
        readonly state: "rejected";
    };
    /**
     * Payload fired when a query entry is updated
     */
    type QueryEntryUpdateData<TResult = unknown, TError = unknown> = {
        /**
         * Unique id for the query entry
         */
        readonly id: string;
        /**
         * The updated query entry for this key
         */
        readonly entry: QueryEntry<TResult, TError>;
        /**
         * The scope of the entry
         */
        readonly scope: QueryEntryScope;
        /**
         * Optional list of extensions that this cache entry can be shared with.
         */
        readonly sharedWith?: string[];
    };
}
export declare namespace Image {
    /**
     * Custom image
     */
    interface FxCustomImage {
        /**
         * Icon type (custom)
         */
        type: 0;
        /**
         * Custom icon data
         */
        data: string;
    }
    /**
     * Framework image
     */
    interface FxImage {
        /**
         * Icon type
         */
        type: number;
    }
}
export declare namespace Images {
    /**
     * Data type used for rendering images's.
     */
    type Image = {
        /**
         * Stores the type of image (custom SVG/image or a built in SVG).
         */
        readonly type: number;
        /**
         * Stores the SVG element, or URI to image file.
         */
        readonly data?: string;
        /**
         * Stores the palette of the element.
         */
        readonly palette?: number;
        /**
         * Stores the options of the element.
         */
        readonly options?: ImageOptions;
    };
    type ImageOptions = {
        /**
         * Stores the palette of the element.
         */
        palette?: number;
        /**
         * Title attribute of the svg.
         */
        title?: string;
        /**
         * Description of the svg.
         */
        description?: string;
        /**
         * Badge
         */
        badge?: ImageBadge;
        /**
         * Adds a single custom class, must start with msportalfx or the extension prefix for CSS: "ext-".
         */
        customClass?: string;
    };
    /**
     * Data type used for rendering a images's badge.
     */
    type ImageBadge = {
        /**
         * Badge icon.
         */
        image: Image;
        /**
         * Override the default width, must be in a percentage ie (width: 10).
         */
        width?: number;
    };
}
export declare namespace ResourceManagement {
    /**
     * Data contract for a single location.
     */
    interface Location {
        /**
         * The display name of the location.
         */
        displayName: string;
        /**
         * The fully qualified ID of the location.
         */
        id?: string;
        /**
         * The normalized name of the location.
         */
        name: string;
        /**
         * The display name of the location and its region.
         */
        regionalDisplayName: string;
        /**
         * Location metadata information
         */
        metadata: {
            /**
             * The geography group of the location.
             */
            geographyGroup?: string;
            /**
             * The latitude of the location.
             */
            latitude?: number | string;
            /**
             * The longitude of the location.
             */
            longitude?: number | string;
            /**
             * The physical location of the location.
             */
            physicalLocation?: string;
            /**
             * The region category of the location.
             */
            regionCategory: keyof typeof RegionSegment;
            /**
             * The region type of the location.
             */
            regionType: "Manifest" | "Physical" | "Logical";
            /**
             * The paired region of the location.
             */
            pairedRegion?: {
                /**
                 * The id of the paired location.
                 */
                id: string;
                /**
                 * The normalized name of the paired location.
                 */
                name: string;
            }[];
        };
    }
    /**
     * The enum for which recommended group a location should appear in
     */
    const enum RegionSegment {
        /**
         * Service Provided
         */
        ServiceProvided = "ServiceProvided",
        /**
         * The first group and largest type of locations with the most resource types supported.
         */
        Recommended = "Recommended",
        /**
         * Other locations including RP specific locations
         */
        Other = "Other"
    }
    /**
     * Data contract for a single resource group.
     */
    interface ResourceGroup {
        /**
         * Resource group id.
         */
        readonly id: string;
        /**
         * Resource group location.
         */
        readonly location: string;
        /**
         * Resource group name.
         */
        readonly name: string;
        /**
         * Resource group tags.
         */
        readonly tags?: ReadonlyStringMap<string>;
        /**
         * Resource group properties.
         */
        readonly properties?: {
            /**
             * Resource group provisioning state.
             */
            readonly provisioningState: string;
        };
        /**
         * The ID of the resource that manages this resource group.
         */
        readonly managedBy?: string;
    }
    /**
     * An ARM resource.
     */
    interface ArmResource {
        /**
         * Resource id.
         */
        readonly id: string;
        /**
         * Resource location.
         */
        readonly location: string;
        /**
         * Resource name.
         */
        readonly name: string;
        /**
         * Resource tags.
         */
        readonly tags?: ArmResourceTags;
        /**
         * Resource properties.
         */
        readonly properties?: ArmResourcePropertyBag;
        /**
         * Resource group ID.
         */
        readonly resourceGroup?: string;
        /**
         * Resource type.
         */
        readonly type: string;
        /**
         * Resource kind.
         */
        readonly kind?: string;
        /**
         * Zone properties.
         */
        readonly zones?: readonly string[];
        /**
         * Plan properties.
         */
        readonly plan?: ArmResourcePropertyBag;
        /**
         * Managed by property.
         */
        readonly managedBy?: string;
        /**
         * SKU properties.
         */
        readonly sku?: ArmResourcePropertyBag;
        /**
         * Identity properties.
         */
        readonly identity?: ArmResourcePropertyBag;
    }
    /**
     * ARM resource tags.
     */
    type ArmResourceTags = Readonly<Record<string, string>>;
    /**
     * ARM resource properties.
     */
    type ArmResourcePropertyBag = Readonly<Record<string, any>>;
    /**
     * A resource that was recently accessed by the current user
     */
    interface RecentResourceEntry {
        /**
         * The name of the entry.
         */
        readonly name: string;
        /**
         * The resource ID of the entry.
         */
        readonly resourceId: string;
        /**
         * The optional resource kind of the entry.
         */
        readonly resourceKind?: string;
        /**
         * The timestamp of the entry.
         */
        readonly timeStamp: number;
    }
    /**
     * The ARM ID kind enumeration.
     */
    const enum ArmIdKind {
        /**
         * Invalid ARM ID.
         */
        Invalid = 0,
        /**
         * Subscription ID.
         * eg: `/subscriptions/{subscription}`
         */
        Subscription = 1,
        /**
         * Subscription provider ID.
         * This is a provider at the subscription level.
         * eg: `/subscriptions/{subscription}/providers/{provider}`
         */
        SubscriptionProvider = 2,
        /**
         * Subscription resource ID. Can have nested type/ID pairs.
         * This is a resource at the subscription level.
         * eg: `/subscriptions/{subscription}/providers/{provider}/{resourceTypes[n]}/{resourceIds[n]}`
         */
        SubscriptionResource = 3,
        /**
         * Resource group ID.
         * eg: `/subscriptions/{subscription}/resourceGroups/{resourceGroup}`
         */
        ResourceGroup = 4,
        /**
         * Provider ID.
         * This is a provider at the resource group level.
         * eg: `/subscriptions/{subscription}/resourceGroups/{resourceGroup}/providers/{provider}`
         */
        Provider = 5,
        /**
         * Resource ID. Can have nested type/ID pairs.
         * This is a resource at the resource group level.
         * eg: `/subscriptions/{subscription}/resourceGroups/{resourceGroup}/providers/{provider}/{resourceTypes[n]}/{resourceIds[n]}`
         */
        Resource = 6,
        /**
         * Tenant provider ID.
         * This is a provider at the tenant level.
         * eg: `/providers/{provider}`
         */
        TenantProvider = 7,
        /**
         * Tenant resource ID. Can have nested type/ID pairs.
         * This is a resource at the tenant level.
         * eg: `/providers/{provider}/{resourceTypes[n]}/{resourceIds[n]}`
         */
        TenantResource = 8,
        /**
         * Subscription tag ID.
         * eg: `/subscriptions/{subscription}/tagNames/{tagName}`
         */
        SubscriptionTag = 9,
        /**
         * Subscription tag value ID.
         * eg: `/subscriptions/{subscription}/tagNames/{tagName}/tagValues/{tagValue}`
         */
        SubscriptionTagValue = 10,
        /**
         * Location ID.
         * eg: `/subscriptions/{subscription}/locations/{location}`
         */
        Location = 11
    }
    /**
     * The ARM ID interface.
     */
    interface ArmId {
        /**
         * The kind of ARM ID.
         */
        readonly kind: ArmIdKind;
        /**
         * The subscription for the ARM ID.
         * Valid/required for these kinds:
         *      Subscription,
         *      SubscriptionProvider,
         *      SubscriptionResource,
         *      ResourceGroup,
         *      Provider,
         *      Location,
         *      Resource,
         *      SubscriptionTag,
         *      SubscriptionTagValue
         */
        readonly subscription: string;
        /**
         * The resource group for the ARM ID.
         * Valid/required for these kinds:
         *      ResourceGroup,
         *      Provider,
         *      Resource
         */
        readonly resourceGroup: string;
        /**
         * The tag name for the ARM ID.
         * Valid/required for these kinds:
         *      SubscriptionTag,
         *      SubscriptionTagValue
         */
        readonly tagName: string;
        /**
         * The tag value for the ARM ID.
         * Valid/required for these kinds:
         *      SubscriptionTagValue
         */
        readonly tagValue: string;
        /**
         * The provider (namespace) for the ARM ID.
         * Valid/required for these kinds:
         *      SubscriptionProvider,
         *      SubscriptionResource,
         *      Provider,
         *      Resource,
         *      TenantProvider,
         *      TenantResource
         */
        readonly provider: string;
        /**
         * The location for the ARM ID.
         * Valid/required for these kinds:
         *      Location
         */
        readonly location?: string;
        /**
         * The collection of resource IDs for the ARM ID.
         * Valid/required for these kinds:
         *      SubscriptionResource,
         *      Resource,
         *      TenantResource
         *
         * @deprecated ArmId.resourceIds 05/06/2022 - Please use the ArmId.getResourceNames(armId) function instead.
         *
         * recipe: fullName = MsPortalFx.last(armId.resourceIds)
         *
         * becomes: fullName = MsPortalFx.last(ArmId.getResourceNamers(armId))
         *
         * Please refer to https://aka.ms/portalfx/breaking for more details.
         */
        readonly resourceIds?: readonly string[];
        /**
         * The collection of resource types for the ARM ID.
         * Valid/required for these kinds:
         *      SubscriptionResource,
         *      Resource,
         *      TenantResource
         *
         * @deprecated ArmId.resourceTypes 05/06/2022 - Please use the ArmId.getResourceTypes(armId) function instead.
         *
         * recipe: fullType = MsPortalFx.last(armId.resourceTypes)
         *
         * becomes: fullType = MsPortalFx.last(ArmId.getResourceTypes(armId))
         *
         * Please refer to https://aka.ms/portalfx/breaking for more details.
         */
        readonly resourceTypes?: readonly string[];
        /**
         * The collection of resource types for the ARM ID in nested form.
         * Valid/required for these kinds:
         *      SubscriptionResource,
         *      Resource,
         *      TenantResource
         */
        readonly nestedResourceTypes?: readonly {
            readonly provider: string;
            readonly resourceTypes: readonly string[];
        }[];
        /**
         * Flag which indicates that the resource type is a nested resource type.
         * Valid/required for these kinds:
         *      SubscriptionResource,
         *      Resource,
         *      TenantResource
         */
        readonly isNestedResourceType?: boolean;
        /**
         * The full resource name for the ARM ID.
         * Valid for these kinds:
         *      SubscriptionResource,
         *      Resource,
         *      TenantResource
         */
        readonly resourceName: string;
        /**
         * The fully qualified resource type for the ARM ID which includes the namespace (provider).
         * Valid for these kinds:
         *      SubscriptionResource,
         *      Resource,
         *      TenantResource
         *
         * IMPORTANT: This property includes the provider for the resource type as a prefix, eg: Microsoft.Sql/servers/databases
         *            To get the resource type without the provider, please use:
         *                     ArmId.getResourceTypeWithoutProvider(armId);
         *            which will return a string in the form like this (no provider): servers/databases
         */
        readonly resourceType: string;
    }
    /**
     * The browse blade reference options for getBrowseBladeReference API.
     */
    interface BrowseBladeReferenceOptions {
        /**
         * The resource type for the browse blade reference.
         */
        resourceType: string;
        /**
         * Optional resource type kind for the browse blade reference.
         */
        kind?: string;
        /**
         * Optional flag to indicate the browse blade reference should be for an in-menu blade browse.
         */
        inMenu?: boolean;
        /**
         * Optional flag to ignore the browse deep link supplied by the extension and to force navigation to the FX/default browse blade.
         */
        ignoreDeepLink?: boolean;
    }
    /**
     * The dynamic dx blade reference options for the getDynamicDxBladeReference API.
     */
    interface DynamicDxBladeReferenceOptions {
        /**
         * The blade name suffix for the view.
         */
        bladeNameSuffix: string;
    }
    /**
     * The dynamic dx blade reference for getDynamicDxBladeReference() API.
     */
    interface DynamicDxBladeReference {
        /**
         * The blade name.
         */
        readonly bladeName: string;
        /**
         * The extension name for the blade
         */
        readonly extensionName: string;
    }
    /**
     * Represents types of resource move operations supported in the system.
     */
    const enum MoveType {
        /**
         * Move resources across subscriptions and resource groups excluding current subscription.
         */
        Subscription = 0,
        /**
         * Move resources across resource groups only.
         */
        ResourceGroup = 1,
        /**
         * Move resources across subscriptions and resource groups including current subscription.
         */
        SubscriptionAndResourceGroup = 3,
        /**
         * Move resource is not supported.
         */
        None = 4
    }
    /**
     * The resource type blade reference options for getResourceTypeBladeReference API.
     */
    interface ResourceTypeBladeReferenceOptions {
        /**
         * The resource type for the blade reference.
         */
        readonly resourceType: string;
        /**
         * The referenced blade kind.
         */
        readonly kind: ResourceTypeBladeKind;
        /**
         * The parameters for the referenced blade.
         */
        readonly parameters: ResourceTypeBladeParameters[ResourceTypeBladeKind];
    }
    interface ResourceTypeBaseParameters {
        /**
         * Resource id of an ARM resource.
         */
        readonly id: string;
    }
    /**
     * Represents the resource type blade parameters for given blade reference kinds.
     */
    interface ResourceTypeBladeParameters {
        /**
         * Properties Blade Kind.
         */
        readonly properties: ResourceTypeBaseParameters;
        /**
         * Overview Blade Kind.
         */
        readonly overview: ResourceTypeBaseParameters;
        /**
         *  CLI/PS Blade Kind.
         */
        readonly apiExplorer: ResourceTypeBaseParameters;
        /**
         *  Monitoring Blade Kind.
         */
        readonly monitoring: ResourceTypeBaseParameters;
    }
    /**
     * The resource type blade reference kind
     */
    type ResourceTypeBladeKind = keyof ResourceTypeBladeParameters;
    /**
     * ResourceInfo contains needed information for the essentials with resource id.
     */
    interface ResourceInfo {
        /**
         * Resource group name.
         */
        readonly resourceGroupName: string;
        /**
         * Resource group Id.
         */
        readonly resourceGroupId: string;
        /**
         * Subscription Name.
         */
        readonly subscriptionName: string;
        /**
         * Subscription Id.
         */
        readonly subscriptionId: string;
        /**
         * Location.
         */
        readonly location: string;
        /**
         * Zones.
         */
        readonly zones?: readonly string[];
        /**
         * Tags.
         */
        readonly tags: TagInfo[];
        /**
         * Move resource options.
         */
        readonly moveOptions: {
            readonly resourceGroup: boolean;
            readonly subscription: boolean;
            readonly location: boolean;
        };
    }
    /**
     * Tags for a resource.
     */
    interface TagInfo {
        /**
         * Key for the tag
         */
        readonly key: string;
        /**
         * Value for the tag
         */
        readonly value: string;
    }
}
export declare namespace Policy {
    /**
     * Reason for policy restriciton being applied to field
     */
    interface PolicyRestrictionsResult {
        /**
         * A policy has denied this field as a valid value
         */
        Deny: "Deny";
        /**
         * A policy will remove this field on creation
         */
        Removed: "Removed";
        /**
         * A policy has required that this field be set
         */
        Required: "Required";
    }
    /**
     * The contract with the policy insights API endpoint
     */
    interface PolicyCheckRequest {
        /**
         * The scope at which the policy check is being requested
         */
        scope?: string;
        /**
         * The property of the resource which will potentially be created
         */
        resourceDetails: {
            /**
             * The property bag for the resource being created
             */
            resourceContent?: {
                /**
                 * The location of the resource being created
                 * ***THIS MUST BE SUPPLIED BEFORE THE POLICY REQUEST IS MADE***
                 */
                location?: string;
                /**
                 * The type of the resource being created
                 * ***THIS MUST BE SUPPLIED BEFORE THE POLICY REQUEST IS MADE***
                 */
                type?: string;
                /**
                 * The name of the resource being created
                 */
                name?: string;
            } & StringMapPrimitive;
            /**
             * The api version of the resource being created
             * ***THIS MUST BE SUPPLIED BEFORE THE POLICY REQUEST IS MADE***
             */
            apiVersion?: string;
            /**
             * The scope at which the resource is being created
             * ***THIS MUST BE SUPPLIED BEFORE THE POLICY REQUEST IS MADE***
             */
            scope?: string;
        };
        /**
         * This is used for known possible values of different fields within the resourceContent
         * e.g. dropdown options to be disabled or marked
         */
        pendingFields: {
            /**
             * Maps to the fields inside of resourceContent
             */
            field: string;
            /**
             * The possible string values of those fields
             */
            values?: string[];
        }[];
    }
    /**
     * The response for a policy check request.
     */
    interface PolicyCheckResponse {
        /**
         * Array of restrictions that policy will place on properties of the resource.
         * This will include fields provided in the request's `pendingFields` AND
         * any properties that will be added/replaced/removed by `modify` and `append` effect policies.
         */
        readonly fieldRestrictions: {
            /**
             * The name of the field that is being restricted.
             * Tags will always be in the form `tags.{tagName}`.
             * The tag name may contain additional `.` (period) characters.
             */
            readonly field: string;
            /**
             * The restrictions placed on the field
             */
            readonly restrictions: {
                /**
                 * `Deny` indicates a subset of values will be denied by policy.
                 * `Removed` indicates the field will be removed by policy. If the field is `tags` this indicates ALL tags will be removed.
                 * `Required` indicates the field is required by policy.
                 * This can either mean policy will provide a `defaultValue` for the field if it is not user provided OR that policy will replace any provided value with
                 * the value in `fieldRestrictions[*].restrictions[*].values
                 */
                readonly result: keyof PolicyRestrictionsResult;
                /**
                 * This is only provided in conjunction with a `result` of `Required`.
                 * This is the value that policy will set on the field if the user does not provide their own value.
                 */
                readonly defaultValue?: string;
                /**
                 * For a `result` of `Required` this indicates what values policy requires the field to be.
                 * For a `result` of `Deny` this indicates what values will be denied by policy.
                 */
                readonly values: string[];
                /**
                 * Contains the resource IDs of the [policy entities](https://learn.microsoft.com/azure/governance/policy/overview#azure-policy-objects)
                 * that are responsible for the restriction. REST APIs related to policy entities are described [here](https://learn.microsoft.com/rest/api/resources/policyassignments).
                 */
                readonly policy: {
                    readonly policyDefinitionId: string;
                    readonly policyAssignmentId: string;
                    readonly policySetDefinitionId?: string;
                    readonly policyDefinitionReferenceId?: string;
                };
            }[];
        }[];
        /**
         * Array of `deny` effect policies that will deny the creation of the `resourceContent` provided in the request.
         * This indicates that the known (i.e. non-pending) fields will cause the resource to be denied.
         */
        readonly contentEvaluationResult: {
            readonly policyEvaluations: {
                /**
                 * Details of the policy that the resource is not compliant with.
                 */
                readonly policyInfo: {
                    readonly policyAssignmentId: string;
                    readonly policyDefinitionId: string;
                    readonly policySetDefinitionId?: string;
                    readonly policyDefinitionReferenceId?: string;
                    readonly policySetDefinitionName: string;
                    readonly policySetDefinitionVersion?: string;
                    readonly policyDefinitionName: string;
                    readonly policyDefinitionVersion?: string;
                    readonly policyDefinitionEffect?: string;
                    readonly policyAssignmentName: string;
                    readonly policyAssignmentVersion?: string;
                    readonly policyAssignmentScope?: string;
                };
                /**
                 * This will always be `NonCompliant` in the `checkPolicyRestrictions` API since it only returns policies that will deny the `resourceContent`.
                 */
                readonly evaluationResult: string;
                /**
                 * Details about what conditions in the policy rule evaluated to `True`.
                 * The structure of this object matches what is available in the `Microsoft.PolicyInsights/policyStates`
                 * API described [here](https://learn.microsoft.com/rest/api/policy-insights/policystates/listqueryresultsforresource#policyevaluationdetails).
                 */
                readonly evaluationDetails?: {
                    readonly result: boolean;
                    readonly expressionKind: string;
                    readonly expression: string;
                    readonly path: string;
                    readonly expressionValue: string;
                    readonly targetValue: string;
                    readonly operator: string;
                };
            }[];
        };
    }
    /**
     * The option for converting values to fields for policy field validation
     * Contains a field that is optional if the value is a string
     */
    interface ValueToField<TVal> {
        /**
         * Map control's value to the string being placed in the resource properties
         * If the control's value is set to string, then this will default to an identity function
         */
        valueToField?: (value: TVal) => string;
    }
    /**
     * Contract for the PendingValues options for creating a policy field validation
     */
    type PendingValues<TVal> = {
        /**
         * The field which the pending values will be evaluated as
         */
        field: string;
        /**
         * Localize field name and value to put in validation string
         */
        fieldToDisplay?: (field: string, value: TVal, reason: keyof PolicyRestrictionsResult) => {
            displayField: string;
            displayValue?: string;
        };
        /**
         * A function which returns the possible values for the field
         * This will run on validation and if any observables accessed in the values function passed in are modified
         *
         * @default values returns an array that contains just the current value of the control
         */
        values?: () => TVal[];
    } & (TVal extends string ? ValueToField<TVal> : Required<ValueToField<TVal>>);
    /**
     * Contract for the PendingValues options for creating a policy field validation
     */
    type PendingValuesGenerator<TVal> = (value: TVal) => PendingValues<TVal>[];
    /**
     * The contact for the policy validation factory to create a validation for a particular field
     */
    interface FieldValidationOptions<TVal> {
        /**
         * A function which will be added to a sequence of functions that will create the resource details of the policy check request.
         * Upon validation, this will be run with the current value of the control to which this validation has been attached
         *
         * @param details The details which are the result of any previous buildResourceDetail functions
         * @param value The current value of the control to which this validation is attached
         * @returns The details which are a result of this function's modifications of the details passed in
         */
        buildResourceDetails: (details: PolicyCheckRequest["resourceDetails"], value: TVal) => PolicyCheckRequest["resourceDetails"] | {
            /**
             *  The resulting resource details
             */
            resourceDetails: PolicyCheckRequest["resourceDetails"];
            /**
             * The scope at which the policy check request should be made
             * Useful in the case that the resource group doesn't exists yet
             * but this resource will be created a the resource group level
             */
            requestScope: string;
        };
        /**
         * A function which will be added to a sequence of functions that will create the pending fields of the policy check request
         */
        pendingValues: PendingValues<TVal>;
        /**
         * A function which will be added to a sequence of functions that will create the pending fields of the policy check request
         */
        pendingValuesGenerator: PendingValuesGenerator<TVal>;
    }
    /**
     * The contact for the policy validation factory to create a validation for a particular field
     */
    type CreateFieldValidationOptions<TVal> = AtLeastOne<FieldValidationOptions<TVal>>;
    /**
     * The contact for the policy validation factory to create a validation for a particular field
     */
    type CreateFieldValidationWithTriggerOptions<TVal> = AtLeastOne<FieldValidationOptions<TVal>> & {
        /**
         * A callback to update validation of a control when other resource values
         * have changed and policy's might no longer be violated by this field.
         */
        triggerValidation: () => void;
    };
    /**
     * Options to supply to the policy validation factory
     */
    interface Options {
        /**
         * A last step callback to modify anything about the policy check request
         *
         * @param policyCheckRequest The result of all previous functions creating the policy check request
         * @returns The finalized policy check request
         */
        customizeRequest?: (policyCheckRequest: PolicyCheckRequest) => PolicyCheckRequest;
        /**
         * A callback to recieve the response from the policy check api.
         *
         * @param policyCheckResponse The result of the call to the policy check api
         */
        onResponse?: (policyCheckResponse: PolicyCheckResponse) => void;
        /**
         * A callback to recieve the error response from the policy check api.
         *
         * @param policyCheckError The result of the call to the policy check api
         */
        onError?: (policyCheckError: any) => void;
    }
    /**
     * Options for creating a child validation factory for another resource
     */
    interface ChildFactoryOptions {
        /**
         * Should the existing build resource details be copied over
         * e.g. If the resource will be created under the same scope and you've already created
         * Subscription and ResourceGroup validations, set to true.
         */
        copyBuildResourceDetails?: boolean;
    }
}
export declare namespace PolicyDataCoreModels {
    /**
     * Value types supported by the Policy API.
     * For most cases, the value's item type will be string.
     * If you are using the 'ResourceTypeMetadataItemWithSubItem' type (the 'resourceTypeSubProperty' property),
     * then you would most likely be supplying a JSON object as a value, which would be parsed using the 'resourceTypeProperty' (propertyPath to the JSON obj) passed in.
     */
    type ValueItemType = string | Record<string, any>;
    /**
     * Common properties across all ResourceTypeMetadata items
     */
    type ResourceTypeMetadataItemCommon = {
        /**
         * The resource type
         * e.g."Microsoft.Storage/storageAccounts"
         */
        readonly resourceType: string;
        /**
         * API Version of the resource type
         * e.g. for storage it could be "2019-01-01"
         */
        readonly apiVersion: string;
        /**
         * The property of the resource type, for which the values are being supplied.
         * "properties.supportsHttpsTrafficOnly"
         */
        readonly resourceTypeProperty: string;
    };
    /**
     * ResourceTypeMetadata item
     */
    type ResourceTypeMetadataItem = ResourceTypeMetadataItemCommon & {
        /**
         * 1. [Optional = Set to null/undefined/""] for root level properties documented here https://learn.microsoft.com/azure/governance/policy/concepts/definition-structure#fields
         *      i.e. name / tags / location / identity.type
         *      (Optional unless your resource type has alias defined for a root level property).
         *
         * 2. [Required] for nested properties not in the #1 list
         *      If this is not set, an expensive getAliases network call will be made to resolve the same.
         *      i.e. for "Microsoft.Storage/storageAccounts" properties.supportsHttpsTrafficOnly
         *      e.g.
         *          resourceTypePropertyAlias: "Microsoft.Storage/storageAccounts/supportsHttpsTrafficOnly"
         *
         *  https://learn.microsoft.com/azure/governance/policy/concepts/definition-structure#aliases
         */
        readonly resourceTypePropertyAlias: string;
    };
    /**
     * ResourceTypeMetadata Item with sub item
     * Sample:
     * ```json
     * [
     *     {
     *         resourceType: "Microsoft.Compute/virtualMachines",
     *         resourceTypeProperty: "properties.storageProfile.ImageReference",
     *         apiVersion: "2020-06-01",
     *         resourceTypePropertyAlias: [
     *             {
     *                 resourceTypeSubProperty: "publisher",
     *                 resourceTypePropertyAlias: "Microsoft.Compute/virtualMachines/imagePublisher",
     *             },
     *             {
     *                 resourceTypeSubProperty: "sku",
     *                 resourceTypePropertyAlias: "Microsoft.Compute/virtualMachines/imageSku",
     *             },
     *             {
     *                 resourceTypeSubProperty: "offer",
     *                 resourceTypePropertyAlias: "Microsoft.Compute/virtualMachines/imageOffer",
     *             },
     *             {
     *                 resourceTypeSubProperty: "version",
     *                 resourceTypePropertyAlias: "Microsoft.Compute/virtualMachines/imageVersion",
     *             },
     *         ],
     *     },
     * ]
     * ```
     */
    type ResourceTypeMetadataItemWithSubItem = ResourceTypeMetadataItemCommon & {
        /**
         * Subitem information
         * Sample:
         * ```json
         * [
         *     {
         *         resourceTypeSubProperty: "publisher",
         *         resourceTypePropertyAlias: "Microsoft.Compute/virtualMachines/imagePublisher",
         *     },
         *     {
         *         resourceTypeSubProperty: "sku",
         *         resourceTypePropertyAlias: "Microsoft.Compute/virtualMachines/imageSku",
         *     },
         * ]
         * ```
         */
        readonly resourceTypePropertyAlias: ResourceTypeMetadataSubItem[];
    };
    /**
     * Subitem information
     * Sample:
     * ```json
     * [
     *     {
     *         resourceTypeSubProperty: "publisher",
     *         resourceTypePropertyAlias: "Microsoft.Compute/virtualMachines/imagePublisher",
     *     },
     *     {
     *         resourceTypeSubProperty: "sku",
     *         resourceTypePropertyAlias: "Microsoft.Compute/virtualMachines/imageSku",
     *     },
     * ]
     * ```
     */
    type ResourceTypeMetadataSubItem = {
        /**
         * The property path to the JSON object passed in as value
         */
        readonly resourceTypeSubProperty: string;
        /**
         * The corresponding alias to the property path passed in.
         */
        readonly resourceTypePropertyAlias: string;
    };
    /**
     * Options for the Evaluator APIs
     * For most cases, the Validator and Selector APIs would be used.
     * However, in some cases, when you have a custom scenario, for e.g. VMSizeSelector grid,
     * this API provides more flexibility by returning the raw Policy result for the UI to do more work, with more flexibility.
     */
    type PolicyDataCoreOptions<PolicyItemType extends ValueItemType> = PolicyDataCoreOptionsCommon & {
        /**
         * Scope at which the policy checks should be performed
         * Provide either scopeId or resourceScopeId.
         */
        readonly scope: Scope;
        /**
         * Values the policy checks will be performed over
         */
        readonly resolvedValuesToField: PolicyItemType[];
        /**
         * Blade name to include in associated telemetry
         */
        readonly bladeName: string;
    };
    /**
     * The common set of properties across all policy APIs. (Validator, Selector and Evaluator)
     */
    type PolicyDataCoreOptionsCommon = {
        /**
         * The resource type metadata information
         */
        readonly resourceTypeMetadata: ResourceTypeMetadataItem | ResourceTypeMetadataItem[] | ResourceTypeMetadataItemWithSubItem;
        /**
         * To be set to true in cases where we would like to get the 'required' policy values.
         * i.e. pass in null and get what is required by policy in that environment, if any.
         * Default: false
         */
        readonly allowEmptyValues?: boolean;
        /**
         * Any additional metadata to be included in telemetry
         */
        readonly additionalTelemetry?: Record<string, any>;
    };
    /**
     * Scope at which the policy checks should be performed
     * Provide either scopeId or resourceScopeId.
     */
    type Scope = {
        /**
         * In cases where the resource is not yet present, i.e. Creates, supply as 'scopeId' the scope of the deployment.
         * For e.g. `/subscriptions/${subscriptionId}/resourceGroups/${resourceGroupName}`
         */
        readonly scopeId?: string;
        /**
         * In cases where the resource is already present, i.e. Managed, supply as 'resourceScopeId' the resource scope.
         * For e.g. `/subscriptions/${subscriptionId}/resourceGroups/${resourceGroupName}/providers/Microsoft.Storage/storageAccounts/${storageAccountName}`
         */
        readonly resourceScopeId?: string;
    };
    /**
     * Internal types (Used for function proxy typings only, not for consumption by extension authors)
     */
    namespace Internal {
        /**
         * The result type from the PolicyDataCore Module.
         */
        type PolicyDataCoreResult<PolicyItemType extends ValueItemType> = {
            /**
             * Array of error results per Policy Assignment, and/or Policy Definition (in case of Policy Initiatives).
             */
            readonly allErrorResults: PolicyDataCoreErrorResult[];
            /**
             * Values 'denied' by the checkPolicy API (deny restrictions).
             */
            readonly deniedValues: PolicyItemType[];
            /**
             * Values 'allowed' by the checkPolicy API (required restrictions).
             */
            readonly allowedValues: PolicyItemType[];
        };
        /**
         * Error result per Policy Assignment, and/or Policy Definition (in case of Policy Initiatives).
         */
        type PolicyDataCoreErrorResult = {
            /**
             * ARM Id of the policy assignment that threw a policy restriction on the values passed in.
             */
            readonly policyAssignmentId: string;
            /**
             * ARM Id of the policy definition (normally used for Policy Initiatives) that threw a policy restriction on the values passed in.
             */
            readonly policyDefinitionReferenceId?: string;
            /**
             * String that represents the non-complaince reason property on the Policy Assignment.
             * (For Policy Initiatives, we also use the referenceId to find an exact match, if multiple noncomplaince reasons are present).
             * If one is not present, this is defaulted to the display name of the Policy Assignment.
             */
            readonly reason: string;
        };
        type PolicyDataCoreOptionsType = PolicyDataCoreOptions<string>;
        type PolicyDataCoreResultType = PolicyDataCoreResult<string>;
    }
}
export declare namespace Ajax {
    /**
     * These interfaces are a way of creating a maintainable or string type
     * in combination with "keyof". This produces intellisense for apis.
     * e.g. "GET" | "HEAD" | "POST" | "PUT" | "DELETE"
     */
    interface BatchHttpMethods {
        GET: void;
        HEAD: void;
        POST: void;
        PUT: void;
        DELETE: void;
        PATCH: void;
    }
    /**
     * Http methods for batch ajax calls
     */
    type BatchHttpMethod = keyof BatchHttpMethods;
    /**
     * The request options.
     */
    const enum RequestOptions {
        /**
         * Default behavior.
         *    - Defaults to foreground request
         *    - Calls are sent directly to ARM without waiting for other requests
         *    - Any ServerTimeout (503) failures for foreground GET requests
         *      are automatically retried by calling the API directly without batch
         *    - Responses are not cached
         *    - Sending requests without delaying has been observed to improve performance,
         *      so requests no longer wait for other requests before executing
         */
        None = 0,
        /**
         * Make the batch call on the next tick.
         * DebounceNextTick takes precedence over Debounce100Ms.
         */
        DebounceNextTick = 1,
        /**
         * Include the request in a batch call that is made after a 100ms delay.
         * Debounce100Ms takes precedence over DebounceOneMinute
         */
        Debounce100ms = 2,
        /**
         * Sets this request to run in the background.
         * Background requests are batched every 60 seconds.
         */
        DebounceOneMinute = 4,
        /**
         * Forces a retry for any failure returned (statusCode >= 400) regardless of the HTTP method.
         */
        RetryForce = 8,
        /**
         * Skips the default retry.
         * SkipRetry takes precedence over ForceRetry if both are set.
         */
        RetrySkip = 16,
        /**
         * Caches the response for GET requests for 10 seconds.
         */
        ResponseCacheEnabled = 32,
        /**
         * Skips caching the response for GET requests.
         */
        ResponseCacheSkip = 64,
        /**
         * Skips retry when a forbidden gateway error is received.
         */
        RetrySkipOnForbidden = 128,
        /**
         * Sends the request directly to ARM without delaying.
         * DebounceImmediately takes precedence over DebounceNextTick
         */
        DebounceImmediately = 256
    }
    /**
     * Endpoints used by most extensions.
     */
    type Endpoints = {
        /**
         * The AAD endpoint with a trailing slash.
         */
        readonly aadAuthority: string;
        /**
         * The ARM/CSM endpoint with a trailing slash.
         */
        readonly arm: string;
        /**
         * The absolute endpoint of the ARM/CSM batch endpoint with the API version included.
         */
        readonly armBatch: string;
        /**
         * The Graph endpoint with a trailing slash.
         */
        readonly graph: string;
        /**
         * The Microsoft Graph endpoint with a trailing slash.
         */
        readonly msGraph: string;
    };
    /**
     * The settings for the batch call.
     */
    type BatchSettings = {
        /**
         * The request options.
         */
        options?: RequestOptions | number;
        /**
         * An object to exclude or include status codes to force retry on the validate template ajax call.
         * If provided, options will have `RequestOptions.RetryForce` included.
         */
        retryStatusCodes?: {
            exclude: Ajax.BatchResponseItem<any>["httpStatusCode"][];
        } | {
            include: Ajax.BatchResponseItem<any>["httpStatusCode"][];
        };
        /**
         * The custom headers for a batch request
         */
        requestHeaderDictionary?: Record<string, string[]>;
        /**
         * The telemetry header to set.
         */
        setTelemetryHeader?: string;
        /**
         * The http method to use. Defaults to GET.
         */
        type?: BatchHttpMethod;
        /**
         * The URI to call.
         */
        uri: string;
        /**
         * Optional content to set for the requests.
         */
        content?: any;
        /**
         * Optional. If provided and true, a `responseHeaders` property will be included in the response as an instance of the browser Headers class.
         * DO NOT USE if the result will be proxied to another frame.
         */
        readonly includeResponseHeaders?: boolean;
    };
    /**
     * The contract for the batch settings.
     */
    type BatchMultipleSettings = {
        /**
         * The list of batch requests. All URIs have to be relative URIs in the request.
         */
        readonly batchRequests: readonly BatchRequest[];
        /**
         * The endpoint to make the request to.
         * If not specified, will use the ARM endpoint.
         */
        readonly endpoint?: string;
        /**
         * Determines whether the ajax request is part of a background task.
         * If true the batch request will be pushed on to the background queue.
         */
        readonly isBackgroundTask?: boolean;
        /**
         * Determines whether to append a telemetry header for the ARM calls.
         *
         * Set to a non-empty string to append the header. The value should be 60 characters or less and will be trimmed
         * if longer.
         */
        readonly telemetryHeader?: string;
        /**
         * Optionally include a `responseHeaders` property in the individual batch responses as instances of the browser Headers class.
         * DO NOT USE if the result will be proxied to another frame.
         */
        readonly includeResponseHeaders?: boolean;
    };
    /**
     * Response for a request within a batch.
     */
    type BatchResponseItem<T> = {
        /**
         * The response content. Can be success or failure.
         */
        readonly content: T;
        /**
         * The response headers as a simple object.
         */
        readonly headers: Record<string, string>;
        /**
         * The response headers as an instance of the browser Headers class. Only populared if `includeResponseHeaders` was true in batch options.
         */
        readonly responseHeaders?: Headers;
        /**
         * The response status code.
         */
        readonly httpStatusCode: number;
        /**
         * The name provided in the request.
         */
        readonly name?: string;
    };
    /**
     * Batch response item error.
     */
    type BatchResponseItemError<T> = Error & {
        /**
         * The response content for failure.
         */
        readonly content: T;
        /**
         * The response headers as a simple object.
         */
        readonly headers: Record<string, string>;
        /**
         * The response status code.
         */
        readonly httpStatusCode: number;
        /**
         * The response headers as an instance of the browser Headers class.
         */
        readonly responseHeaders: Headers;
    };
    /**
     * Batch response.
     */
    type BatchResponse = {
        /**
         * The success response from ARM.
         */
        readonly responses: readonly BatchResponseItem<any>[];
    };
    /**
     * Individual batch request.
     */
    type BatchRequest = {
        /**
         * The URI to call.
         */
        readonly uri: string;
        /**
         * The http method for the call. Defaults to GET
         */
        readonly httpMethod?: BatchHttpMethod;
        /**
         * Optional request details.
         */
        readonly requestHeaderDetails?: {
            /**
             * The command name.
             */
            readonly commandName?: string;
        };
        /**
         * The custom headers for a batch request
         */
        readonly requestHeaderDictionary?: Record<string, string[]>;
        /**
         * The content to set on the request.
         */
        readonly content?: any;
    };
    /**
     * Options for the getClientIP API.
     */
    type GetClientIPOptions = {
        /**
         * Whether to fallback to IPv4 addresses when IPv6 is not available.
         */
        readonly fallbackToIPv4: boolean;
    };
    /**
     * The HTTP status codes matches the .NET HttpStatusCode enumeration.
     */
    const enum HttpStatusCode {
        Continue = 100,
        SwitchingProtocols = 101,
        Ok = 200,
        Created = 201,
        Accepted = 202,
        NonAuthoritativeInformation = 203,
        NoContent = 204,
        ResetContent = 205,
        PartialContent = 206,
        Ambiguous = 300,
        MultipleChoices = 300,
        Moved = 301,
        MovedPermanently = 301,
        Found = 302,
        Redirect = 302,
        RedirectMethod = 303,
        SeeOther = 303,
        NotModified = 304,
        UseProxy = 305,
        Unused = 306,
        RedirectKeepVerb = 307,
        TemporaryRedirect = 307,
        BadRequest = 400,
        Unauthorized = 401,
        PaymentRequired = 402,
        Forbidden = 403,
        NotFound = 404,
        MethodNotAllowed = 405,
        NotAcceptable = 406,
        ProxyAuthenticationRequired = 407,
        RequestTimeout = 408,
        Conflict = 409,
        Gone = 410,
        LengthRequired = 411,
        PreconditionFailed = 412,
        RequestEntityTooLarge = 413,
        RequestUriTooLong = 414,
        UnsupportedMediaType = 415,
        RequestedRangeNotSatisfiable = 416,
        ExpectationFailed = 417,
        UpgradeRequired = 426,
        TooManyRequests = 429,
        InternalServerError = 500,
        NotImplemented = 501,
        BadGateway = 502,
        ServiceUnavailable = 503,
        GatewayTimeout = 504,
        HttpVersionNotSupported = 505
    }
}
export declare namespace Marketplace {
    /**
     * Contains special metadata used by the CreateLauncher to return alternate create flows or notice blades.
     *
     * use `BladeReferences.forCreateAssetType` instead and update your asset types to point to a create blade without a marketplace id
     *             for more information see https://eng.ms/docs/products/azure-portal-framework-ibizafx/reactviews/migrating/migrating-to-sdkv2#31-createmarketplace-migration-guidance
     */
    type AlternateCreateFlowOptions = Obsolete;
    /**
     * The context from which a marketplace create is kicked off.
     *
     * use `BladeReferences.forCreateAssetType` instead and update your asset types to point to a create blade without a marketplace id
     *             for more information see https://eng.ms/docs/products/azure-portal-framework-ibizafx/reactviews/migrating/migrating-to-sdkv2#31-createmarketplace-migration-guidance
     */
    type LaunchingContext = Obsolete;
    /**
     * Blade reference options for provisioning blades
     *
     * use `BladeReferences.forCreateAssetType` instead and update your asset types to point to a create blade without a marketplace id
     *             for more information see https://eng.ms/docs/products/azure-portal-framework-ibizafx/reactviews/migrating/migrating-to-sdkv2#31-createmarketplace-migration-guidance
     */
    type MarketplaceOptions = Obsolete;
    /**
     * Marketplace offer plan.
     *
     * fetch from catalog api directly
     *             for more information see https://eng.ms/docs/products/azure-portal-framework-ibizafx/reactviews/migrating/migrating-to-sdkv2#31-createmarketplace-migration-guidance
     */
    type OfferPlan = Obsolete;
    /**
     * Marketplace offer pricing details model.
     * Used to retrieve the pricing information for a Marketplace offer.
     *
     * fetch from catalog api directly
     *             for more information see https://eng.ms/docs/products/azure-portal-framework-ibizafx/reactviews/migrating/migrating-to-sdkv2#31-createmarketplace-migration-guidance
     */
    type OfferPricingDetails = Obsolete;
    /**
     * Marketplace product (offer).
     *
     * fetch from catalog api directly
     *             for more information see https://eng.ms/docs/products/azure-portal-framework-ibizafx/reactviews/migrating/migrating-to-sdkv2#31-createmarketplace-migration-guidance
     */
    type Product = Obsolete;
    /**
     * Marketplace artifact.
     *
     * use `referenceType.marketplaceId` instead
     *             for more information see https://eng.ms/docs/products/azure-portal-framework-ibizafx/reactviews/migrating/migrating-to-sdkv2#31-createmarketplace-migration-guidance
     */
    type Artifact = Obsolete;
    /**
     * Marketplace item.
     *
     * use `referenceType.marketplaceId` instead
     *             for more information see https://eng.ms/docs/products/azure-portal-framework-ibizafx/reactviews/migrating/migrating-to-sdkv2#31-createmarketplace-migration-guidance
     */
    type Item = Obsolete;
    /**
     * The interface of context supplied by marketplace
     *
     */
    interface Context<_TUIMetaData extends Obsolete = Obsolete> {
        /**
         * The telemetry id; a GUID unique to each instance of the provisioning flow initiated by
         * the user (i.e. unique to each instance when the blade is launched). The same id is used
         * when the 'CreateFlowLaunched, 'ProvisioningStart/Ended' and 'CreateDeploymentStart/End'
         * events are logged. Adding this telemetry id to the telemetry logged on the blade will
         * help you connect all the data points for a given provisioning instance.
         */
        readonly telemetryId: string;
        /**
         * The Marketplace item invoking the blade. Will be undefined if 'requiresMarketplaceId'
         * is set to false on the @DoesProvisioning decorator options.
         *
         * use `referenceType.marketplaceId` instead and fetch from catalog api directly
         *             for more information see https://eng.ms/docs/products/azure-portal-framework-ibizafx/reactviews/migrating/migrating-to-sdkv2#31-createmarketplace-migration-guidance
         */
        readonly marketplaceItem?: Obsolete;
        /**
         * The resource group name passed into the gallery when new is selected from a resource group
         */
        readonly resourceGroupName?: string;
    }
}
export declare namespace Provisioning {
    interface LaunchingContext {
        /**
         * The telemetry id for the provisioning blade launched
         */
        telemetryId: string;
        /**
         * The gallery item id for the provisioning blade launched
         *
         * @deprecated use referenceType.marketplaceId instead
         */
        galleryItemId?: string;
        /**
         * The name of the create blade.
         */
        createBlade?: string;
    }
    interface ReferenceType {
        /**
         * The marketplace id invoking the blade.
         */
        readonly marketplaceId: string;
        /**
         * The extension of the asset type invoking the blade.
         */
        readonly extensionName?: string;
        /**
         * The asset type invoking the blade.
         */
        readonly assetType: string;
        /**
         * The asset kind invoking the blade.
         */
        readonly assetKind?: string;
        /**
         * The telemetry id for the provisioning blade launched
         */
        readonly telemetryId?: string;
    }
    /**
     * The template output.
     */
    interface TemplateOutput {
        /**
         * The type of the output.
         */
        type: string;
        /**
         * The value of the output.
         */
        value: any;
    }
    /**
     * The template resource.
     */
    interface TemplateResource {
        /**
         * The name of the resource.
         */
        name: string;
        /**
         * The type of the resource.
         */
        type: string;
        /**
         * The API version of the resource.
         */
        apiVersion: string;
        /**
         * The location of the resource.
         */
        location: string;
        /**
         * The resource properties.
         */
        properties?: StringMap<any>;
        /**
         * The dependencies for this resource.
         */
        dependsOn?: string[];
        /**
         * The tags on the resource.
         */
        tags?: StringMap<string>;
        /**
         * Comments on the resource.
         */
        comments?: string;
        /**
         * The child resources.
         */
        resources?: TemplateResource[];
        /**
         * The resource id. Only includes in the validation response.
         */
        id?: string;
    }
    /**
     * The response that ARM returns when a template validate call succeeds.
     */
    interface TemplateValidationResponse {
        /**
         * Deployment id.
         */
        id: string;
        /**
         * Deployment name.
         */
        name: string;
        /**
         * Deployment properties.
         */
        properties: {
            /**
             * Correlation id associated with the validate call.
             */
            correlationId: string;
            /**
             * Duration of validation.
             */
            duration: string;
            /**
             * Deployment mode.
             */
            mode: string;
            /**
             * Parameters passed to the validate call.
             */
            parameters: StringMap<TemplateOutput>;
            /**
             * Correlation id associated with the validate call.
             */
            provisioningState: string;
            /**
             * The timestamp.
             */
            timestamp: string;
            /**
             * The list of resources that are in the template.
             */
            validatedResources: TemplateResource[];
        };
    }
    /**
     * The template deployment operation mode.
     */
    /**
     * Initial values for form initialization. Use those values to initialize the subscription,
     * resource group, and location drop down controls.
     */
    interface InitialValues {
        /**
         * The list of subscription ids last used by the user.
         */
        readonly subscriptionIds?: string[];
        /**
         * The list of location names last used by the user.
         */
        readonly locationNames?: string[];
        /**
         * The list of resource group names last used by the user.
         */
        readonly resourceGroupNames?: string[];
    }
    /**
     * Options for validating the form prior to sending the preflight validation request to ARM.
     */
    interface FormValidationOptions {
        /**
         * Explicitly prevent form validation.
         */
        readonly validateForm?: boolean;
        /**
         * Focus the first invalid control on the form. Defaults to false.
         */
        readonly focusOnFirstInvalid?: boolean;
        /**
         * Whether or not to validate hidden controls on the form. Defaults to true.
         */
        readonly validateHidden?: boolean;
    }
    /**
     * Options for the DeployTemplate method on provisioning context
     */
    interface DeployTenantLevelTemplateOptions {
        /**
         * The deployment name.
         */
        deploymentName: string;
        /**
         * The resource id. Supply this to link the notifications to the asset or if the deployment
         * results in a startboard part.
         */
        resourceId?: string;
        /**
         * The asset type associated with the deployment. Used to customize the post-provisioning experience.
         */
        readonly assetType?: string;
        /**
         * The context from which a gallery create is kicked off. Used for telemetry logging.
         */
        launchingContext?: Marketplace.LaunchingContext;
        /**
         * Debug info.
         */
        debug?: string;
        /**
         * An array of the resource providers to be registered for the subscription.
         */
        resourceProviders: string[];
        /**
         * The parameters for the template deployment (name and value pairs).
         */
        parameters?: StringMapPrimitive;
        /**
         * The reference parameters for the template deployment.
         */
        referenceParameters?: StringMap<StringMapPrimitive>;
        /**
         * The URI for the parameters file. Use this to link to an existing parameters file. Specify
         * this or the parameters and/or referenceParameters properties, but not both.
         */
        parametersLinkUri?: string;
        /**
         * The URI for the ARM template. Specify this or the templateJson property, but not both.
         * This will be deprecated soon, use templateLink.uri
         */
        templateLinkUri?: string;
        /**
         * The link to the ARM template. Specify this or the templateJson property, but not both.
         */
        templateLink?: {
            /**
             * The URI of the template to deploy. Use either the uri or id property, but not both.
             */
            uri?: string;
            /**
             * The resource id of a Template Spec. Use either the id or uri property, but not both.
             */
            id?: string;
            /**
             * Applicable only if this template link references a Template Spec.
             * This relativePath property can optionally be used to reference a
             * Template Spec artifact by path.
             */
            relativePath?: string;
        };
        /**
         * The inline deployment template JSON. Specify this or the templateLinkUri property, but not both.
         */
        templateJson?: string;
        /**
         * The template deployment operation mode. Defaults to 'RequestDeploymentOnly'.
         */
        deploymentMode?: TemplateDeploymentMode;
        /**
         * Flag indicating that we should run ARM's preflight validation before submitting the template
         * deployment request to ARM. Defaults to false.
         */
        validateTemplate?: boolean;
        /**
         * An object to exclude or include httpStatusCodes for retry
         */
        validateRetry?: {
            exclude: Ajax.BatchResponseItem<any>["httpStatusCode"][];
        } | {
            include: Ajax.BatchResponseItem<any>["httpStatusCode"][];
        };
        /**
         * The result of validating the template with ARM.
         */
        validationResult?: TemplateValidationResponse;
        /**
         * Options for validating the form before ARM validation.
         * This validation is enabled by default and can be disabled by setting validateForm = false in this property.
         */
        formValidationOptions?: FormValidationOptions;
        /**
         * The marketplaceId of the resource.
         */
        readonly marketplaceItemId?: string;
        /**
         * A key or hash that encodes or corresponds to information about the provisioning request.
         */
        readonly provisioningHash?: string;
        /**
         * Function to provide a part reference based on the resourceId of a deployment.
         * Defaults to the part reference provided by the marketplace UI.Definition file
         * or null if no marketplace item was provided to this provisioning blade.
         *
         * @param resourceId The resourceId of the resource created
         */
        supplyPartReference?(resourceId: string): any;
    }
    /**
     * Defines how resources deployed by the deployment stack are locked.
     */
    const enum DenySettingsMode {
        /**
         * Authorized users are able to read and modify the resources, but cannot delete.
         */
        DenyDelete = "denyDelete",
        /**
         * Authorized users can only read from a resource, but cannot modify or delete it.
         */
        DenyWriteAndDelete = "denyWriteAndDelete",
        /**
         * No deny assignments have been applied.
         */
        None = "none"
    }
    const enum StackDeploymentFlag {
        /**
         * Validate the stack.
         */
        validate = 4,
        /**
         * Deploy the stack.
         */
        deploy = 8,
        /**
         * Validate and deploy the stack.
         */
        validateAndDeploy = 12
    }
    /**
     * Options for the deployStack method on provisioning context
     */
    interface DeployStackOptions {
        /**
         * The deployment name.
         */
        readonly stackName: string;
        /**
         * The scope at which the stack will be deployed
         */
        readonly scope: string;
        /**
         * The resource id. Supply this to link the notifications to the asset or if the deployment results in a startboard part.
         */
        readonly primaryResourceId: string;
        /**
         * An array of the resource providers to be registered for the subscription.
         */
        readonly resourceProviders: string[];
        /**
         * Defines the behavior of resources removed in incremental deployments of the stack.
         *
         * @deprecated Use actionOnUnmanage
         */
        readonly updateBehavior?: StackUpdateBehavior;
        /**
         * Defines the behavior of resources that are not managed immediately after the stack is updated.
         */
        readonly actionOnUnmanage?: Record<"resources" | "resourceGroups" | "managementGroups", ActionOnUnmanage>;
        /**
         * The location/region name to set as the last used location, and will be used as a default in subsequent
         * create experiences for the user. This is optional in a resource group level deployment, but required
         * for all other deployment scopes. Will also be used to set location for any resource groups created in this
         * deployment if resourceGroupLocation is not provided.
         */
        readonly location: string;
        /**
         * Determines if the deployment stack should validate, deploy, or both, defaults to validateAndDeploy.
         */
        readonly deploymentMode?: StackDeploymentFlag;
        /**
         * The marketplaceId of the resource.
         */
        readonly marketplaceItemId?: string;
        /**
         * A key or hash that encodes or corresponds to information about the provisioning request.
         */
        readonly provisioningHash?: string;
        /**
         * The asset type associated with the deployment. Used to customize the post-provisioning experience.
         */
        readonly assetType?: string;
        /**
         * "The scope at which the initial deployment should be created.
         * If a scope is not specified, it will default to the scope of the deployment stack.
         * Valid scopes are: management group (format: `/providers/Microsoft.Management/managementGroups/{managementGroupId}`),
         * subscription (format: `/subscriptions/{subscriptionId}`),
         * resource group (format: `/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}`)."
         */
        readonly deploymentScope?: string;
        /**
         * Deployment stack description.
         */
        readonly description?: string;
        /**
         * Flag to bypass service errors that indicate the stack resource list is not correctly synchronized.
         */
        readonly bypassStackOutOfSyncError?: boolean;
        /**
         * The parameters for the deny settings to define how resources deployed by the deployment stack are locked.
         */
        readonly denySettings?: {
            /**
             * Defines how resources deployed by the deployment stack are locked.
             */
            readonly mode: DenySettingsMode;
            /**
             * List of AAD object IDs excluded from the deny assignments lock. Up to 5 principals are permitted.
             */
            readonly excludedPrincipals?: string[];
            /**
             * List of role-based management operations that are excluded from the deny settings. Up to 200 actions are permitted.
             * If the denySettings mode is set to 'denyWriteAndDelete', then the following actions are automatically appended to 'excludedActions': '*\/read' and 'Microsoft.Authorization/locks/delete'.
             * If the denySettings mode is set to 'denyDelete', then the following actions are automatically appended to 'excludedActions': 'Microsoft.Authorization/locks/delete'.
             * Duplicate actions will be removed.
             */
            readonly excludedActions?: string[];
            /**
             * Indicates whether the deny settings will be applied to child scopes.
             */
            readonly applyToChildScopes?: boolean;
        };
        /**
         * The context from which a gallery create is kicked off. Used for telemetry logging.
         */
        launchingContext?: Provisioning.LaunchingContext;
        /**
         * The parameters for the template deployment (name and value pairs).
         */
        parameters: {
            /**
             * The URI of the template to deploy. Use either the uri or id property, but not both.
             */
            readonly uri: string;
        } | {
            /**
             * The parameters which will be sent as json
             */
            readonly json: StringMapPrimitive;
        };
        /**
         * The link to the ARM template or the template used for
         */
        template: {
            /**
             * The URI of the template to deploy.
             */
            readonly uri: string;
        } | {
            /**
             * The resource id of a Template Spec.
             */
            readonly id: string;
        } | {
            /**
             * The template which will be sent as json
             */
            readonly json: StringMapPrimitive;
        };
    }
    /**
     * Options for the DeployTemplate method at resource group level on provisioning context
     */
    type DeployTemplateOptions = DeployTenantLevelTemplateOptions & {
        /**
         * The subscription id.
         */
        subscriptionId: string;
        /**
         * The resource group name.
         */
        resourceGroupName: string;
    } & AtLeastOne<{
        /**
         * The location/region to deploy a resource group being created for this deployment.
         * This will also be set as the user's last used location if location is not provided.
         */
        resourceGroupLocation: string;
        /**
         * The location/region name to set as the last used location, and will be used as a default in subsequent
         * create experiences for the user. This is optional in a resource group level deployment, but required
         * for all other deployment scopes. Will also be used to set location for any resource groups created in this
         * deployment if resourceGroupLocation is not provided.
         */
        location: string;
    }>;
    /**
     * Options for the DeployTemplate method at subscription level on provisioning context
     */
    interface DeploySubscriptionLevelTemplateOptions extends DeployTenantLevelTemplateOptions {
        /**
         * The subscription id.
         */
        subscriptionId: string;
        /**
         * The location/region if this is a subscription level resource.
         */
        location: string;
    }
    /**
     * Options for the DeployTemplate method at subscription level on provisioning context
     */
    interface DeployManagementGroupLevelTemplateOptions extends DeployTenantLevelTemplateOptions {
        /**
         * The managementGroup id.
         */
        managementGroupId: string;
        /**
         * The location/region if this is a managementGroup level resource.
         */
        location: string;
    }
    type AllDeployTemplateOptions = DeployTemplateOptions | DeploySubscriptionLevelTemplateOptions | DeployTenantLevelTemplateOptions;
    /**
     * ARM template deployment operation.
     */
    interface TemplateDeploymentOperationProperties {
        /**
         * The resource being operated upon.
         */
        targetResource: StringMap<string>;
        /**
         * The timestamp when the operation was completed.
         */
        timestamp: string;
        /**
         * The unique id for this deployment operation.
         */
        trackingId: string;
        /**
         * The status of the operation.
         */
        statusCode: string;
        /**
         * The detailed status message for the operation returned by the resource provider.
         */
        statusMessage: string;
    }
    /**
     * ARM template deployment operation.
     */
    interface TemplateDeploymentOperation {
        /**
         * The URI for the deployed entity.
         */
        id: string;
        /**
         * The operation id.
         */
        operationId: string;
        /**
         * The operation properties.
         */
        properties: TemplateDeploymentOperationProperties;
    }
    interface BaseDeployResults {
        /**
         * The deployment status code.
         */
        deploymentStatusCode: DeploymentStatusCode;
        /**
         * The correlation id (aka tracking id).
         */
        correlationId: string;
        /**
         * The provisioning state.
         */
        provisioningState: string;
        /**
         * The timestamp when the operation was completed.
         */
        timestamp: Date;
        /**
         * The list of deployment operations.
         */
        operations?: TemplateDeploymentOperation[];
        /**
         * Timestamp when the deployment request was initiated.
         */
        requestTimestamp?: Date;
    }
    type DeployTemplateResults<TOptions extends AllDeployTemplateOptions> = BaseDeployResults & TOptions;
    type DeployStackResults<TOptions extends DeployStackOptions> = BaseDeployResults & TOptions;
    /**
     * Options for the DeployCustom method on provisioning context
     */
    interface DeployCustomOptions<TResult> {
        /**
         * A promise for when provisioning has finished
         */
        provisioningPromise: Promise<TResult>;
    }
    interface Provisioning<_TUIMetadata extends Obsolete = Obsolete> extends Marketplace.Context {
        /**
         * Initial values for form initialization. Use those values to initialize the subscription, resource group, and location drop down controls.
         */
        readonly initialValues: InitialValues;
        /**
         * Reference type signifying what marketplaceId or assetType the blade was launched with.
         * If this is empty, the blade was launched directly via a blade reference.
         * If it contains both, then the marketplace item references the assetType.
         */
        readonly referenceType?: ReferenceType;
        /**
         * Validates an ARM template and returns a promise for the validation result.
         *
         * @param options Template deployment options
         */
        validateTemplate<TOptions extends AllDeployTemplateOptions = DeployTemplateOptions>(options: TOptions): Promise<TOptions>;
        /**
         * Deploy a template to ARM and receive a promise for a deployment result
         *
         * @param options Template deployment options
         */
        deployTemplate<TOptions extends AllDeployTemplateOptions = DeployTemplateOptions>(options: TOptions): Promise<DeployTemplateResults<TOptions>>;
        /**
         * Deploy a template to ARM and receive a promise for a deployment result
         *
         * @param options Template deployment options
         */
        deployStack(options: DeployStackOptions): Promise<DeployStackResults<DeployStackOptions>>;
        /**
         * Deploy a template to ARM and receive a promise for a deployment result
         *
         * @param options Template deployment options
         * @returns a promise that resolves to the requested marketplace item
         */
        setMarketplaceId(options: string): Obsolete;
        /**
         * Get a blade reference to the template viewer blade
         *
         * @param options Template deployment options
         */
        getAutomationBladeReference<TOptions extends AllDeployTemplateOptions = DeployTemplateOptions>(options: TOptions): Promise<SimpleBladeReference>;
        /**
         * Get a blade reference to the Arm Errors blade
         *
         * @param bladeParameters Parameters passed to the arm errors blade
         */
        getArmErrorsBladeReference(bladeParameters: ArmErrorsBladeParameters): Promise<SimpleBladeReference>;
        /**
         * Deploy a template to ARM and receive a promise for a deployment result
         *
         * @param options Custom deployment options
         */
        deployCustom<TResult>(options: DeployCustomOptions<TResult>): Promise<TResult>;
    }
}
export type DeployTemplateOptions = Provisioning.DeployTemplateOptions;
export type TemplateOutput = Provisioning.TemplateOutput;
export type TemplateResource = Provisioning.TemplateResource;
export type TemplateValidationResponse = Provisioning.TemplateValidationResponse;
export type InitialValues = Provisioning.InitialValues;
export type ReferenceType = Provisioning.ReferenceType;
export type FormValidationOptions = Provisioning.FormValidationOptions;
export type DeployTenantLevelTemplateOptions = Provisioning.DeployTenantLevelTemplateOptions;
export type DeployStackOptions = Provisioning.DeployStackOptions;
export type DeploySubscriptionLevelTemplateOptions = Provisioning.DeploySubscriptionLevelTemplateOptions;
export type DeployManagementGroupLevelTemplateOptions = Provisioning.DeployManagementGroupLevelTemplateOptions;
export type TemplateDeploymentOperationProperties = Provisioning.TemplateDeploymentOperationProperties;
export type TemplateDeploymentOperation = Provisioning.TemplateDeploymentOperation;
export type BaseDeployResults = Provisioning.BaseDeployResults;
export type DeployCustomOptions<TResults> = Provisioning.DeployCustomOptions<TResults>;
export type Provisioning<TUIMetadata extends Obsolete = Obsolete> = Provisioning.Provisioning<TUIMetadata>;
export type AllDeployTemplateOptions = Provisioning.AllDeployTemplateOptions;
export type DeployTemplateResults<TOptions extends Provisioning.AllDeployTemplateOptions> = Provisioning.DeployTemplateResults<TOptions>;
export type DeployStackResults<TOptions extends Provisioning.DeployStackOptions> = Provisioning.DeployStackResults<TOptions>;
/**
 * Defines the behavior of resources that are not managed immediately after the stack is updated.
 *
 * @deprecated Use ActionOnUnmanage
 */
export declare const enum StackUpdateBehavior {
    /**
     * Remove the resource from the stack but keep the resource in Azure
     */
    DetachResources = "DetachResources",
    /**
     * Remove the resource from the stack and remove the resource from Azure
     */
    PurgeResources = "PurgeResources"
}
/**
 * Defines the behavior of resources that are not managed immediately after the stack is updated.
 */
export declare const enum ActionOnUnmanage {
    /**
     * Remove the resource from the stack but keep the resource in Azure
     */
    Delete = "delete",
    /**
     * Remove the resource from the stack and remove the resource from Azure
     */
    Detach = "detach"
}
/**
 * The template deployment operation mode. Defaults to 'RequestDeploymentOnly'.
 */
export declare const enum TemplateDeploymentMode {
    /**
     * Submit a deployment request to ARM only (this does not wait till the resources are provisioned).
     * The 'deployTemplate' API will return a promise that resolves with ARM's response to the request.
     */
    RequestDeploymentOnly = 1,
    /**
     * Submit a deployment request to ARM and wait till provisioning the resources has completed
     * (silent polling). The 'deployTemplate' API will return a promise that reports progress only
     * once, when the request is accepted. The promise resolves when provisioning the resources
     * has completed.
     */
    DeployAndAwaitCompletion = 2,
    /**
     * Submit a deployment request to ARM and wait till provisioning the resources has completed,
     * while reporting all updates from ARM. The 'deployTemplate' API will return a promise that
     * reports progress when the request is accepted, followed by all ARM operations on every poll.
     * The promise resolves when provisioning the resources has completed.
     */
    DeployAndGetAllOperations = 3,
    /**
     * Execute all the deployment preflight actions without submitting the deployment request
     * (sanity check, provisioning the resource group, registering the resource providers,
     * getting a valid deployment name, and running ARM's preflight validation).
     */
    PreflightOnly = 4
}
/**
 * Status codes for the deployment in being provisioned.
 */
export declare const enum DeploymentStatusCode {
    /**
     * Template preflight, validation or deployment failure (based on the operation performed).
     */
    Failure = -1,
    /**
     * Deployment was accepted or successful (based on the operation performed).
     */
    Success = 0,
    /**
     * ARM rejected the deployment request.
     */
    DeploymentRequestFailed = 1,
    /**
     * Deployment failed.
     */
    DeploymentFailed = 2,
    /**
     * Deployment status unknown.
     */
    DeploymentStatusUnknown = 3,
    /**
     * An unexpected error occurred while provisioning the resource group.
     */
    ErrorProvisioningResourceGroup = 4,
    /**
     * An unexpected error occurred while submitting the deployment request.
     */
    ErrorSubmittingDeploymentRequest = 5,
    /**
     * An unexpected error occurred while getting the deployment status.
     */
    ErrorGettingDeploymentStatus = 6,
    /**
     * Invalid arguments.
     */
    InvalidArgs = 7,
    /**
     * An unexpected error occurred while registering the resource providers.
     */
    ErrorRegisteringResourceProviders = 8,
    /**
     * Deployment canceled.
     */
    DeploymentCanceled = 9,
    /**
     * Unknown error.
     */
    UnknownError = 10
}
/**
 * Troubleshooting links for the arm errors blade
 */
export declare const enum TroubleshootingLinks {
    /**
     * Common Azure deployment errors
     */
    CommonDeploymentErrors = 0,
    /**
     * Move resources documentation
     */
    ResourceMoveDocs = 1,
    /**
     * Create ARM template documents
     */
    CreateArmTemplateDocs = 2
}
/**
 * The input parameters for the arm errors blade.
 */
export interface ArmErrorsBladeParameters {
    /**
     * The errors object from ARM.
     */
    readonly errors: any;
    /**
     * The subscriptionId for the resource with an ARM error.
     * This is used to create a link to the quotas for the subscription.
     */
    readonly subscriptionId?: string;
    /**
     * The array of links to display in the "Troubleshooting links" section.
     */
    readonly troubleshootingLinks?: readonly TroubleshootingLinks[];
}
export declare namespace AssetTypes {
    /**
     * Asset type options for the AssetType 'options' property.
     */
    const enum AssetTypeOptions {
        /**
         * The asset type does not provide any options.
         */
        NoOptions = 0,
        /**
         * The asset type is hidden from the browse menu, search and the resource filter types.
         */
        HideAssetType = 1,
        /**
         * The asset type's instances (actual resources) are by default hidden from the browse resource list and search results.
         */
        HideInstances = 2,
        /**
         * The asset type is shown in the browse menu, search and the resource filter types. This is used for kinds to override the parent asset type.
         */
        ShowAssetType = 4,
        /**
         * The asset type's instances (actual resources) are by default shown from the browse resource list and search results. This is used for kinds to override the parent asset type.
         */
        ShowInstances = 8,
        /**
         * The asset type's is disabled.
         */
        DisableAssetType = 16
    }
    /**
     * Browse type for the AssetType 'browseType' property.
     */
    const enum BrowseType {
        /**
         * Use the service view model (browse V1).
         *
         * @deprecated Fx.Assets.BrowseType.ServiceViewModel Browse V1 is no longer supported and at this point is not used by any teams
         *
         * Please refer to https://aka.ms/portalfx/breaking for more details.
         */
        ServiceViewModel = 0,
        /**
         * Use the resource type for browse.
         */
        ResourceType = 1,
        /**
         * Use the asset type blade for browse.
         */
        AssetTypeBlade = 2,
        /**
         * Service link into the classic portal for a service.
         */
        ServiceLink = 3,
        /**
         * Instance link into the classic portal for an instance of a service.
         *
         * @deprecated Fx.Assets.BrowseType.InstanceLink Instance link browse is no longer supported and at this point is not used by any teams
         *
         * Please refer to https://aka.ms/portalfx/breaking for more details.
         */
        InstanceLink = 4,
        /**
         * The asset type does not support browse (used only in typescript).
         */
        NoBrowse = -1
    }
    /**
     * The routing type for the AssetType 'routingType' property.
     */
    enum ResourceRoutingType {
        /**
         * Default routing type, resource-group level tracked resources.
         */
        Default = 0,
        /**
         * Tenant (provider) based resources.
         */
        Tenant = 1,
        /**
         * Extension based resources.
         */
        Extension = 2,
        /**
         * Provider proxy (untracked) based resources.
         */
        ProviderProxy = 3
    }
    /**
     * The resource type kind metadata.
     */
    interface ResourceTypeKind {
        /**
         * The name of the resource type kind.
         */
        readonly name: string;
        /**
         * The is default flag of the resource type kind.
         */
        readonly isDefault?: boolean;
        /**
         * Optional browse type which indicates the type of browse for the asset type.
         */
        readonly options?: AssetTypeOptions;
        /**
         * The singular display name of the resource type kind.
         */
        readonly singularDisplayName?: string;
        /**
         * The plural display name of the resource type kind.
         */
        readonly pluralDisplayName?: string;
        /**
         * The lowercase singular display name of the resource type kind.
         */
        readonly lowerSingularDisplayName?: string;
        /**
         * The lowercase plural display name of the resource type kind.
         */
        readonly lowerPluralDisplayName?: string;
        /**
         * The service display name of the resource type kind.
         */
        readonly serviceDisplayName?: string;
        /**
         * The blade associated with the resource type kind.
         */
        readonly bladeName?: string;
        /**
         * The blade extension associated with the resource type kind.
         */
        readonly bladeExtensionName?: string;
        /**
         * Name of part to use for pinning
         */
        readonly partName?: string;
        /**
         * Name of the extension that contains the part
         */
        readonly partExtensionName?: string;
        /**
         * The icon of the resource type kind.
         * FxImage format.
         */
        readonly icon?: Image.FxCustomImage | Image.FxImage;
        /**
         * If this flag is true visual artifacts are displayed in the asset's parts and blades to indicate the functionality is preview.
         */
        readonly isPreview?: boolean;
        /**
         * If this flag is true visual artifacts are displayed in the asset's parts and blades to indicate the functionality is disabled by policy.
         */
        readonly isDisable?: boolean;
        /**
         * The is use resource menu flag of the resource type kind.
         */
        readonly useResourceMenu?: boolean;
        /**
         * The list of keywords.
         * NOTE: comma separated list.
         */
        readonly keywords?: string;
        /**
         * The kinds (for filtering) for the kind.
         */
        readonly kinds?: readonly string[];
        /**
         * The filter map for the kind.
         */
        readonly filterMap?: ReadonlyStringMap<boolean>;
    }
    /**
     * ARM browse options for the AssetType 'argBrowseOptions' property.
     */
    const enum ArmBrowseOptions {
        /**
         * Allows ARM browse fallback (ARM browse when no ARG support, ARG fallback blade, ARG simplified view blade).
         */
        AllowFallback = 0,
        /**
         * Disables ARM browse fallback (ARM browse when no ARG support, ARG fallback blade, ARG simplified view blade).
         */
        NoFallback = 1
    }
    /**
     * ARG browse options for the AssetType 'argBrowseOptions' property.
     */
    const enum ArgBrowseOptions {
        /**
         * @deprecated It has the same effect as "Force" and is not used anymore.
         */
        AllowOptIn = 1,
        /**
         * @deprecated It has the same effect as "Force" and is not used anymore.
         */
        ForceOptIn = 2,
        /**
         * This will force users to the ARG browse experience.
         * This is the default options for types with no special routing type.
         */
        Force = 3,
        /**
         * This will force users to the ARM browse experience.
         */
        Disable = 4
    }
    /**
     * The asset type describes the asset type metadata for a given asset type.
     */
    interface AssetType {
        /**
         * The extension name for the fully qualified asset type.
         */
        readonly extensionName: string;
        /**
         * The asset type for the fully qualified asset type.
         */
        readonly assetType: string;
        /**
         * The asset type's icon image.
         * FxImage format.
         */
        readonly icon: Image.FxCustomImage | Image.FxImage;
        /**
         * The asset type's display name.
         */
        readonly compositeDisplayName: {
            /**
             * The singular, formal-cased display name.
             */
            readonly singular: string;
            /**
             * The plural, formal-cased display name.
             */
            readonly plural: string;
            /**
             * The singular, lower-cased display name.
             */
            readonly lowerSingular: string;
            /**
             * The plural, lower-cased display name.
             */
            readonly lowerPlural: string;
            /**
             * The service display name.
             */
            readonly service?: string;
        };
        /**
         * The options for the asset type.
         */
        readonly options?: AssetTypeOptions;
        /**
         * The browse type for the asset type.
         */
        readonly browseType: BrowseType;
        /**
         * The contracts flag for which contracts the asset view model supports.
         */
        readonly contracts: Assets.AssetTypeContracts;
        /**
         * Optional flag to indicate the asset type is from a preview extension.
         * If this flag is not specified, the asset type is NOT from a preview.
         */
        readonly isPreview?: boolean;
        /**
         * Optional flag to indicate the asset type is disabled by policy.
         * If this flag is not specified, the asset type is NOT disabled.
         */
        readonly isDisabled?: boolean;
        /**
         * The list of keywords.
         */
        readonly keywords?: readonly string[];
        /**
         * The description of asset type.
         */
        readonly description?: string;
        /**
         * The documentation links for asset type.
         */
        readonly links?: readonly {
            /**
             * The link title for the asset type.
             */
            readonly title: string;
            /**
             * The link uri for the asset type.
             */
            readonly uri: string;
        }[];
        /**
         * The list of hidden commands for the asset type.
         */
        readonly hiddenCommands?: readonly string[];
        /**
         * The resource type.
         */
        readonly resourceType: string;
        /**
         * The API resource type for ARM.
         * Only valid if resourceType is not undefined.
         * This is only valid for tenant-routing resources.
         */
        readonly topLevelTenantAlias?: string;
        /**
         * The API resource type for ARM.
         * Only valid if resourceType is not undefined.
         * This is only valid for end-point-routing resources.
         */
        readonly topLevelResourceTypeAlias?: string;
        /**
         * The ARM API version to use for this resource type.
         * Only valid if resourceType is not undefined.
         */
        readonly apiVersion?: string;
        /**
         * The routing type for the resource type.
         * Only valid if resourceType is not undefined.
         * If this is not specified, the 'Default' routing type will be used.
         */
        readonly routingType?: ResourceRoutingType;
        /**
         * The blade associated with the resource type.
         */
        readonly bladeName?: string;
        /**
         * The blade extension associated with the resource type.
         */
        readonly bladeExtensionName?: string;
        /**
         * The part associated with the resource type.
         */
        readonly partName?: string;
        /**
         * The part extension that contains the resource part.
         */
        readonly partExtensionName?: string;
        /**
         * The optional array of kinds for this resource type.
         */
        readonly kinds?: readonly ResourceTypeKind[];
        /**
         * The option for ARG browse.
         */
        readonly argBrowseOption?: ArgBrowseOptions;
        /**
         * The option for ARM browse.
         */
        readonly armBrowseOption?: ArmBrowseOptions;
        /**
         * The optional deep link for browse.
         */
        readonly browseDeepLink?: string;
        /**
         * The flag to show / hide columns for ARG browse.
         */
        readonly showArgColumns?: ReadonlyStringMap<boolean>;
        /**
         * Lower case copy of the resource type for quick lookup.
         */
        readonly lowerCaseResourceType: string;
        /**
         * Lower case copy of the asset type for quick lookup.
         */
        readonly lowerCaseAssetType: string;
        /**
         * Lower case copy of the extension name for quick lookup.
         */
        readonly lowerCaseExtension: string;
        /**
         * Map of kind name to resource type kind for quick lookup.
         * In the form of kindName => ResourceTypeKind
         */
        readonly kindMap?: ReadonlyStringMap<ResourceTypeKind>;
        /**
         * Optional name of the default kind for quick lookup.
         */
        readonly defaultKind?: string;
        /**
         * Optional variant for the asset type.
         */
        readonly variant?: string;
    }
    /**
     * The asset type variant.
     */
    interface AssetTypeVariant {
        /**
         * Gets or sets the property name for this binding
         */
        readonly name: string;
        /**
         * Gets or sets the composite display name
         */
        readonly compositeDisplayName: {
            /**
             * The singular, formal-cased display name.
             */
            readonly singular: string;
            /**
             * The plural, formal-cased display name.
             */
            readonly plural: string;
            /**
             * The singular, lower-cased display name.
             */
            readonly lowerSingular: string;
            /**
             * The plural, lower-cased display name.
             */
            readonly lowerPlural: string;
            /**
             * The service display name.
             */
            readonly service?: string;
        };
        /**
         * Gets or sets the name of the view model for this type
         */
        readonly viewModel?: string;
        /**
         * Gets or sets the name of the extension that contains the view model. Used for declarative views.
         */
        readonly viewModelExtensionName?: string;
        /**
         * Gets or sets the contracts supported on the asset type view model
         */
        readonly contracts?: Assets.AssetTypeContracts;
        /**
         * Gets or sets the list of values which are bound from/to the property
         */
        readonly partName?: string;
        /**
         * Gets or sets the extension that contains the part, optional
         */
        readonly partExtensionName?: string;
        /**
         * Gets or sets the blade name which this asset type is associated with.
         */
        readonly bladeName?: string;
        /**
         * Gets or sets the blade extension name which this asset type is associated with.
         */
        readonly bladeExtensionName?: string;
        /**
         * Gets or sets the asset type options bitmask.
         */
        readonly options?: AssetTypeOptions;
        /**
         * Gets or sets a value indicating the browse type.
         */
        readonly browseType?: BrowseType;
        /**
         * Gets or sets the browse link URI for ServiceLink and Instance browse type.
         */
        readonly browseLinkUri?: string;
        /**
         * Gets or sets a value indicating whether the asset type requires RDFE access to show up in browse.
         */
        readonly browseRequiresRdfe?: boolean;
        /**
         * The resource type.
         */
        readonly resourceType: string;
        /**
         * The API resource type for ARM.
         * This is only valid for tenant-routing resources.
         */
        readonly topLevelTenantAlias?: string;
        /**
         * The API resource type for ARM.
         * This is only valid for end-point-routing resources.
         */
        readonly topLevelResourceTypeAlias?: string;
        /**
         * The ARM API version to use for this resource type.
         * If this is not specified, the latest API version will be used from the ARM manifest file.
         */
        readonly apiVersion?: string;
        /**
         * The routing type for the resource type.
         * If this is not specified, the 'Default' routing type will be used.
         */
        readonly routingType?: ResourceRoutingType;
        /**
         * The optional array of kinds for this resource type.
         */
        readonly kinds?: readonly ResourceTypeKind[];
        /**
         * Gets or sets the resource key which contains the icon for this type
         */
        readonly icon?: Images.Image;
        /**
         * Gets the NoPdlCreateBlade for an asset definition
         */
        readonly noPdlCreateBlade?: string;
        /**
         * Gets the NoPdlCreateExtension for an asset definition
         */
        readonly noPdlCreateExtension?: string;
        /**
         * Gets the NoPdlCreateParameters for an asset definition
         */
        readonly noPdlCreateParameters?: Record<string, any>;
        /**
         * Gets or sets the PostCreateBlade for an asset definition
         */
        readonly postCreateBlade?: Assets.PostCreateBladeReference;
        /**
         * Gets or sets the market place item.
         */
        readonly marketplaceItemId?: string;
        /**
         * Gets or sets the market place category.
         */
        readonly marketplaceMenuItemId?: string;
        /**
         * Gets or sets the Gets boolean indicating that this asset type is in preview.
         */
        readonly isPreview?: boolean;
        /**
         * Gets or sets the list of keywords.
         */
        readonly keywords?: string;
        /**
         * Gets or sets the service menu blade that this asset type is associated with.
         */
        readonly serviceMenuBlade?: string;
        /**
         * Gets or sets the extension for the service menu blade that this asset type is associated with.
         */
        readonly serviceMenuBladeExtension?: string;
        /**
         * Gets or sets the service menu ID for the service menu blade.
         */
        readonly serviceMenuItemId?: string;
        /**
         * Gets or sets the description of the asset type.
         */
        readonly description?: string;
        /**
         * Gets or sets the documentation links of the asset type.
         */
        readonly links?: readonly {
            /**
             * The link title for the asset type.
             */
            readonly title: string;
            /**
             * The link uri for the asset type.
             */
            readonly uri: string;
        }[];
        /**
         * Gets or sets the optional browse deep link used for browse to replace the standard browse blade launched from all services.
         * The deep link must start with a hash '#' symbol.
         */
        readonly browseDeepLink?: string;
    }
    /**
     * Options passed to 'getAllAssetTypes'.
     */
    interface GetAllAssetTypesOptions {
        /**
         * Include the browse manifest metadata.
         */
        includeBrowseManifest?: boolean;
        /**
         * Include the variants metadata.
         */
        includeVariants?: boolean;
    }
    /**
     * The asset type metadata with optional browse manifest data.
     */
    type AssetTypeWithOptions = AssetType & {
        browseManifest?: Assets.BrowseManifest;
        variants?: AssetTypeVariant[];
    };
    type AssetTypeWithBrowseManifest = AssetTypeWithOptions;
}
/**
 * Type definitions related to service menu.
 */
export declare namespace ServiceMenus {
    /**
     * Represents the service menu. Internal use only.
     */
    interface ServiceMenu {
        /**
         * The global unique id for the service menu.
         */
        readonly globalId: string;
        /**
         * The service menu manifest after applying visibility conditions.
         */
        readonly serviceMenuManifest: BrowseMenuManifest;
        /**
         * The unprocessed service menu manifest.
         */
        readonly rawServiceMenuManifest: BrowseMenuManifest;
        /**
         * Which extension the service menu is in.
         */
        readonly extension: string;
    }
    /**
     * Represents the manifest for a browse menu.
     */
    interface BrowseMenuManifest extends MenuManifestBase {
    }
    /**
     * Represents the manifest for a generic menu.
     */
    interface GenericMenuManifest extends MenuManifestBase {
    }
    /**
     * Base class from menu item.
     */
    interface DxMenuItemBase {
        /**
         * The unique id of the menu item.
         */
        readonly id: string;
        /**
         * The display text of the menu item.
         */
        readonly display: string;
        /**
         * The icon of the menu item.
         */
        readonly icon: Image.FxCustomImage | Image.FxImage;
        /**
         * The keywords of the menu item.
         */
        readonly keywords: string;
        /**
         * The tooltip of the menu item.
         */
        readonly tooltip: string;
    }
    /**
     * Base class from menu items with feature and experiment conditionals. (we exclude afec)
     */
    interface DxMenuFxSchemaItem<T> extends ExtensionManifest.MenuFxSchemaItem<T> {
    }
    /**
     * Represents the manifest for a menu definition.
     */
    interface MenuManifestBase {
        /**
         * The unique name of the menu.
         */
        readonly name: string;
        /**
         * The display name of the menu.
         */
        readonly display: string;
        /**
         * The description of the menu.
         */
        readonly description?: string;
        /**
         * The keywords of the menu.
         */
        readonly keywords?: string;
        /**
         * The icon of the menu.
         */
        readonly icon: Image.FxCustomImage | Image.FxImage;
        /**
         * The overview item for the menu.
         */
        readonly overview: DxMenuItem;
        /**
         * The quick access menu items for the menu.
         */
        readonly items: DxMenuItem[];
        /**
         * The groups in the menu.
         */
        readonly groups: DxMenuGroup[];
    }
    /**
     * Represents a menu group.
     */
    interface DxMenuGroup {
        /**
         * Menu group id.
         */
        readonly id: string;
        /**
         * Menu group display text.
         */
        readonly display: string;
        /**
         * Menu group keywords.
         */
        readonly keywords: string;
        /**
         * Menu group menu items.
         */
        readonly items: DxMenuItem[];
    }
    /**
     * Type that represents a menu item.
     */
    type DxMenuItem = MenuBladeReferenceItem | MenuBrowseReferenceItem | MenuServiceReferenceItem | MenuTabItemReference;
    /**
     * Represents menu item with a blade reference.
     */
    interface MenuBladeReferenceItem extends DxMenuItemBase, DxMenuFxSchemaItem<MenuBladeReferenceItem> {
        /**
         * The blade reference.
         */
        readonly blade: ExtensionManifest.BladeReference;
    }
    /**
     * Represents menu item with a browse reference.
     */
    interface MenuBrowseReferenceItem extends DxMenuItemBase, DxMenuFxSchemaItem<MenuBrowseReferenceItem> {
        /**
         * The browse reference.
         */
        readonly browse: MenuBrowseReference;
    }
    /**
     * Represents a menu item with a portal service reference.
     */
    interface MenuServiceReferenceItem extends DxMenuItemBase, DxMenuFxSchemaItem<MenuServiceReferenceItem> {
        /**
         * The portal service reference.
         */
        readonly portalService: PortalServiceReference;
    }
    /**
     * Represents a menu item with tab items.
     */
    interface MenuTabItemReference extends DxMenuItemBase, DxMenuFxSchemaItem<MenuTabItemReference> {
        /**
         * The tab items.
         */
        readonly tabItems: MenuTabItem[];
    }
    /**
     * Menu tab items definition.
     */
    type MenuTabItem = TabItemBladeReference | TabItemBrowseReference | TabItemServiceReference;
    /**
     * Represents a menu tab item with a blade reference.
     */
    interface TabItemBladeReference extends Omit<DxMenuItemBase, "icon" | "tooltip">, DxMenuFxSchemaItem<TabItemBladeReference> {
        /**
         * The blade reference.
         */
        readonly blade: ExtensionManifest.BladeReference;
    }
    /**
     * Represents a menu tab item with a browse reference.
     */
    interface TabItemBrowseReference extends Omit<DxMenuItemBase, "icon" | "tooltip">, DxMenuFxSchemaItem<TabItemBrowseReference> {
        /**
         * The browse reference.
         */
        readonly browse: MenuBrowseReference;
    }
    /**
     * Represents a menu tab item with a portal service reference.
     */
    interface TabItemServiceReference extends Omit<DxMenuItemBase, "icon" | "tooltip">, DxMenuFxSchemaItem<TabItemServiceReference> {
        /**
         * The portal service reference.
         */
        readonly portalService: PortalServiceReference;
    }
    /**
     * Represents a browse reference item.
     */
    type MenuBrowseReference = {
        /**
         * The resource type.
         */
        readonly resourceType: string;
        /**
         * (Optional) The kind for the resource type.
         */
        readonly kind?: string;
        /**
         * (Optional) Extra parameters for the browse entry.
         */
        readonly parameters?: StringMap<any>;
    };
    /**
     * Represents a portal service reference.
     */
    interface PortalServiceReference {
        /**
         * Portal service name.
         */
        readonly name: string;
        /**
         * (Optional) the extension name, otherwise it is assumed to be in the same extension.
         */
        readonly extension?: string;
    }
}
/**
 * Type definitions related to extension manifest.
 */
export declare namespace ExtensionManifest {
    /**
     * Manifest declaration for applying a feature flag to an object.
     * Note: The type declaration should include 'extends T' but this isn't supported in TS
     */
    interface MenuFxSchemaItem<T> {
        /**
         * The feature declaration for the item.
         */
        readonly "fx.feature"?: Feature | FeatureReplacement<T>;
        /**
         * The experiment declaration for the item.
         */
        readonly "fx.experiment"?: Experiment | ExperimentReplacement<T>;
    }
    /**
     * Manifest declaration for including an object based on a feature flag.
     */
    interface Feature {
        /**
         * The name of the feature.
         */
        readonly name: string;
        /**
         * The condition to include the parent object.
         */
        readonly includeWhen?: FeatureWhen;
        /**
         * The value of the feature required to include the parent object.
         */
        readonly value?: string;
    }
    /**
     * The Feature conditions used to manipulate objects with feature flags.
     */
    const enum FeatureWhen {
        /**
         * The condition is when the feature is enabled.
         */
        Enabled = "Enabled",
        /**
         * The condition is when the feature is disabled.
         */
        Disabled = "Disabled",
        /**
         * The condition is when the feature value is equal to the given value.
         */
        ValueEquals = "ValueEquals",
        /**
         * The condition is when the feature value is not equal to the given value.
         */
        ValueNotEquals = "ValueNotEquals"
    }
    /**
     * Manifest declaration for including an object based on a experiment flag.
     */
    type Experiment = Feature;
    /**
     * Manifest declaration for replacing an object based on a feature flag.
     */
    interface FeatureReplacement<T> {
        /**
         * The name of the feature.
         */
        readonly name: string;
        /**
         * The condition to replace the parent object.
         */
        readonly replaceWhen?: FeatureWhen;
        /**
         * The value of the feature required to replace the parent object.
         */
        readonly value?: string;
        /**
         * The replacement object used to replace the parent.
         */
        readonly replacement?: T;
    }
    /**
     * Manifest declaration for replacing an object based on a experiment flag.
     */
    type ExperimentReplacement<T> = FeatureReplacement<T>;
    /**
     * Represents a blade reference.
     */
    interface BladeReference {
        /**
         * Blade extension name.
         */
        readonly name: string;
        /**
         * (Optional) the extension name of the blade, otherwise the blade is assumed to be in the same extension.
         */
        readonly extension?: string;
        /**
         * (Optional) the parameters to pass to blade.
         */
        readonly parameters?: StringMap<any>;
    }
}
export declare namespace FeedbackControls {
    /**
     * The list of possible Policy Names within the Admin Feedback Controls.
     *
     * connectedExperiences:
     * Allow the use of connected experiences in Office (ie. AI analysis on feedback).
     *
     * policyAllowFeedback:
     * Currently applied to CES/CVA and Send-a-Smile (SaS) surveys.
     * This controls if the end user can submit feedback to Microsoft.
     * All controls in survey are disabled and a notification shows that admins have disabled feedback.
     *
     * policyAllowSurvey:
     * Currently applied to NPS and HaTS surveys.
     * This controls whether Microsoft can issue survey prompts to the end user.
     * Never launches the surveys to begin with.
     *
     * policyAllowScreenshot:
     * Allow users to include screenshots, screen recordings, and attachments when they submit feedback to Microsoft.
     *
     * policyAllowContact:
     * Currently applied to NPS, HaTS, CES/CVA and Send-a-Smile (SaS) surveys.
     * This controls if Microsoft can follow up on feedback submitted by the end user.
     * Checkbox allowing Microsoft to contact user is automatically unselected and disabled.
     *
     * policyAllowContent:
     * This controls if the end user can include log files and content samples with their feedback.
     *
     * policyEmailCollectionDefault:
     * This controls whether email collection is selected by default when the user launches feedback.
     *
     * policyScreenshotDefault:
     * This controls whether the screenshot checkbox is selected by default when the user launches feedback.
     *
     * policyContentSamplesDefault:
     * This controls whether the content samples checkbox is selected by default when the user launches feedback.
     */
    type AdminFeedbackControlPolicy = "connectedExperiences" | "policyAllowFeedback" | "policyAllowSurvey" | "policyAllowScreenshot" | "policyAllowContact" | "policyAllowContent" | "policyEmailCollectionDefault" | "policyScreenshotDefault" | "policyContentSamplesDefault";
    /**
     * Feedback control settings assigned by the user's Enterprise-level admin.
     */
    type AdminFeedbackPolicySettings = Record<AdminFeedbackControlPolicy, boolean>;
}
export declare namespace Notifications {
    /**
     * Names an instance of some AssetType.
     */
    type AssetDescriptor = {
        /**
         * The extension name where the AssetType is defined.
         */
        readonly extensionName: string;
        /**
         * The AssetType.
         */
        readonly assetType: string;
        /**
         * The asset ID.
         */
        readonly assetId: string;
    };
    /**
     * Minimum required information to fetch an auth token to be used in ajax call on non-ARM endpoint during polling.
     */
    type PollingAuthDetails = {
        /**
         * The name of the extension that published the notification.
         */
        readonly extensionName: string;
        /**
         * The auth header resource name fetched from reasource access.
         */
        readonly resourceName: string;
        /**
         * The AAD recognized audience fetched from reasource access. Can be a guid or a uri.
         */
        readonly audience: string;
    };
    /**
     * Describes an HTML view, defined in terms of an HTML template string later converted to HTML.
     */
    type HtmlContent = {
        /**
         * The HTML template to display for the notification's description area.
         */
        readonly htmlTemplate: string;
    };
    /**
     * Defines the arguments for opening a blade from an actionable notification.
     */
    type OpenBladeArgs = {
        /**
         * The blade name.
         */
        readonly blade: string;
        /**
         * The extension name.
         */
        readonly extension: string;
        /**
         * The inputs to the blade. Defaults to an empty object.
         */
        readonly parameters?: Record<string, any>;
        /**
         * Defines whether the blade should be launched in a context pane. Defaults to false.
         */
        readonly openInContextPane?: boolean;
    };
    /**
     * Defines the arguments for pinning a dashboard part from an actionable notification.
     */
    type PinToDashboardArgs = {
        /**
         * Specifies the type of part to be pinned.
         */
        readonly partName: string;
        /**
         * The name of the extension for the pinned part.
         */
        readonly extension: string;
        /**
         * The parameters for the pinned part.
         */
        readonly parameters: Record<string, any>;
    };
    /**
     * Notification options needed to open a blade reference.
     */
    type LinkToBladeReference = SimpleBladeReference & Pick<OpenBladeArgs, "openInContextPane">;
    /**
     * Notification contract returned when a notification is published, and has no following actions to be called.
     */
    type Notification = {
        /**
         * The notification's unique id.
         * Can be used to follow the notification's lifecycle in telemetry.
         */
        readonly id: string;
    };
    /**
     * Defines an actionable notification's action.
     */
    type Action = string | PinToDashboardArgs | LinkToBladeReference;
    /**
     * Defines an actionable notification button. Will appear below the description area of the notification.
     */
    type Button = {
        /**
         * Optional button id which will be logged in telemetry (non-localized string).
         */
        id?: string;
        /**
         * Button label (should be localized).
         */
        readonly label: string;
        /**
         * Determines what clicking the button will do.
         *
         * Can be a string, a valid BladeReference, or the params for a pin to dashboard action.
         * If a fully qualified uri as a string, it will open a new tab. If a deeplink, it will navigate within same tab.
         *
         * @example
         * (As deepLink)
         *  action: '#/blade/myextensionname/mybladename'
         * @example
         * (As externalLink)
         *  action: 'https://www.bing.com/'
         * @example
         * (As BladeReference)
         *  action: {
         *      blade: 'MyBladeName',
         *      extension: 'MyExtensionName',
         *      parameters: { ...MyBladeParameters },
         *      openContextPane: true|false
         *  }
         * @example
         * (As PinToDashboard)
         *  action: {
         *      partName: 'MyPartName',
         *      extension: 'MyExtensionName',
         *      parameters: { ...MyPartParameters }
         *  }
         */
        readonly action: Action;
        /**
         * Styles the button as a secondary button. Defaults to false.
         */
        readonly isSecondary?: boolean;
    };
    /**
     * Defines the base options required to create a notification.
     */
    type CommonNotificationOptions = {
        /**
         * List of Click To Actions (CTAs) available on this notification.
         * Currently the only supported CTAs are buttons, for a maximum of 3 buttons in total.
         */
        readonly actions?: readonly Button[];
        /**
         * Will be deprecated soon. Currently ignored.
         * The event correlation ids associated with the notification.
         */
        readonly correlationIds?: readonly string[];
        /**
         * The notification's description.
         */
        readonly description: string | HtmlContent;
        /**
         * Optionally, render the notification description as HTML.
         */
        readonly descriptionAsHtml?: boolean;
        /**
         * The notification's title.
         */
        readonly title: string;
        /**
         * A string URI that the notification links to or the BladeReference used to open a blade with.
         *
         * @example
         * (As deepLink)
         *  action: '#/blade/myextensionname/mybladename'
         * @example
         * (As externalLink)
         *  action: 'https://www.bing.com/'
         * @example
         * (As BladeReference)
         *  action: {
         *      blade: 'MyBladeName',
         *      extension: 'MyExtensionName',
         *      parameters: { ...MyBladeParameters },
         *      openContextPane: true|false
         *  }
         */
        readonly linkTo?: string | LinkToBladeReference | AssetDescriptor;
    };
    /**
     * Defines the options required to create and publish a one time notification.
     */
    type NotificationOptions = CommonNotificationOptions & {
        /**
         * The notification status - Information, Warning, Error, Success.
         */
        readonly status: CompletedStatus | Exclude<Status, Status.InProgress>;
    };
    /**
     * Notification options for a pending notification.
     */
    type PendingNotificationOptions = CommonNotificationOptions & {
        /**
         * The percentage of operation completed. If this value exists, a deterministic progress bar is shown.
         * If not, a non-deterministic progress icon is shown with a description.
         */
        readonly percentComplete?: number;
    };
    /**
     * Defines the base options required to create a polling notification.
     */
    type CommonPollingNotificationOptions = {
        /**
         * List of Click To Actions (CTAs) available on this notification.
         * Currently the only supported CTAs are buttons, for a maximum of 3 buttons in total.
         */
        readonly actions?: readonly Button[];
        /**
         * The notification's description.
         */
        readonly description: string | HtmlContent;
        /**
         * The notification's title.
         */
        readonly title: string;
        /**
         * A string URI that the notification links to or the BladeReference used to open a blade with.
         *
         * @example
         * (As deepLink)
         *  action: '#/blade/myextensionname/mybladename'
         * @example
         * (As externalLink)
         *  action: 'https://www.bing.com/'
         * @example
         * (As BladeReference)
         *  action: {
         *      blade: 'MyBladeName',
         *      extension: 'MyExtensionName',
         *      parameters: { ...MyBladeParameters },
         *      openContextPane: true|false
         *  }
         */
        readonly linkTo?: string | LinkToBladeReference | AssetDescriptor;
    };
    /**
     * Defines the options required to create and publish a polling notification.
     */
    type PollingNotificationOptions = CommonPollingNotificationOptions & {
        /**
         * Defines the options necessary for the Portal to poll on behalf of the notification creator,
         * and how to update the initial notification once the poll attempts return.
         */
        readonly pollingDetails: PollingDetails;
    };
    /**
     * Defines the options required to use the executeAzureAsyncOperation api.
     */
    type ExecuteAzureAsyncPollingNotificationOptions = CommonPollingNotificationOptions & {
        /**
         * Defines the options necessary for the Portal to poll on behalf of the notification creator,
         * and how to update the initial notification once the poll attempts return.
         */
        readonly pollingDetails: ExecuteAzureAsyncPollingDetails;
    };
    /**
     * Defines the options required to create and publish a polling notification via the executeAzureAsyncOperation api.
     */
    type AzureAsyncPollingNotificationOptions = CommonPollingNotificationOptions & {
        /**
         * Defines the options necessary for the Portal to poll on behalf of the notification creator,
         * and how to update the initial notification once the poll attempts return.
         */
        readonly pollingDetails: AzureAsyncPollingDetails;
    };
    /**
     * Options to update pending notification
     */
    type PendingUpdateOptions = Omit<PendingNotificationOptions, "title" | "description"> & Partial<Pick<PendingNotificationOptions, "title" | "description">>;
    /**
     * Options to complete pending notification
     */
    type PendingCompleteOptions = Omit<PendingUpdateOptions, "percentComplete"> & {
        /**
         * Status accompanying a finished pending notification.
         */
        readonly status: CompletedStatus | Exclude<Status, Status.InProgress>;
    };
    /**
     * Methods of interacting with a pending notification
     */
    type PendingNotification = {
        /**
         * The notification's unique id.
         * Can be used to follow the notification's lifecycle in telemetry.
         */
        readonly id: string;
        /**
         * Method to update pending notification
         *
         * @param updateOptions: Options to update pending notification
         */
        update: (updateOptions: PendingUpdateOptions) => void;
        /**
         * Method to complete pending notification with failure or success
         *
         * @param completeOptions: Options to complete pending notification
         */
        complete: (completeOptions: PendingCompleteOptions) => void;
    };
    /**
     * The acceptable properties that an polling inprogress can have updated in default failure cases (ie. defaultFailureNotification).
     */
    type PollingFailedOptions = Omit<CommonPollingNotificationOptions, "title" | "description"> & Partial<Pick<CommonPollingNotificationOptions, "title" | "description">>;
    /**
     * Defines the options required to create and publish a one time notification.
     */
    type PollingCompleteOptions = PollingFailedOptions & {
        /**
         * The notification status - Information, Warning, Error, Success.
         */
        readonly status: CompletedStatus | Exclude<Status, Status.InProgress>;
    };
    /**
     * Properties common to all types of operations involving polling details.
     */
    type CommonPollingDetails = {
        /**
         * The notification "update" options to update the original polling notification for when the failure circumstances aren't known.
         */
        readonly defaultFailureNotification: PollingFailedOptions;
        /**
         * The minimum amount of time in milliseconds to wait between each polling.
         * NOTE: default time between polling attempts starts out small and increases with time.
         * A custom delay will not shorten the space between polls, but will delay it when customDelayInMs is longer than the default.
         */
        readonly customDelayInMs?: number;
        /**
         * Amount of time in milliseconds to continue polling before falling back to a provided timeout notification.
         */
        readonly pollingTimeout?: TimeoutDetails;
        /**
         * Telemetry name for logging to the calling extension's table.
         * Used when "defaultFailureNotification" definition above is used to complete this notification.
         * Provides partners with the ability to track the same metrics they could have with shorter running processes.
         * NOTE: Always be cautious about over-logging.
         */
        readonly defaultTelemetryName?: string;
    };
    /**
     * The record of notification instructions for updating an initial polling notification based on the polled response.
     * NOTE: Any non-empty string value is permitted as a record key, but "Succeeded", "Failed", and "Canceled" are required as per ARM docs.
     */
    type ResponseNotificationByStatus = {
        Succeeded: PollingResponseDetails | string;
        Failed: PollingResponseDetails | string;
        Canceled: PollingResponseDetails | string;
        [key: string]: PollingResponseDetails | string;
    };
    /**
     * This property provides the polling notification api instructions unique to calling the executeAzureAsyncOperation api.
     * NOTE: If the responseNotificationByStatus and keepPollingStatuses don't have the status received,
     * polling will stop, and the defaultFailureNotification will be used to update the original notification.
     */
    type ExecuteAzureAsyncPollingDetails = CommonPollingDetails & {
        /**
         * List of AzureAsyncOperation statuses that are the anticipated responses signaling api to keep polling.
         */
        readonly keepPollingStatuses: readonly string[];
        /**
         * Details for updates to the original polling notification based on each of a number of possible
         * response content statuses received from the batch promise. Whether a success or failure,
         * the notification details are associated with a specific response content status.
         * NOTE: "Succeeded", "Failed", and "Canceled" are the minimum required keys according to ARM docs for terminal provisioningStates/statuses.
         *
         * @example
         * When a response content status of "Failed" is received, the details within "notification"
         * will be used to update the original notification.
         *  {
         *      "Failed": {
         *          notification: {...}
         *      }
         *  }
         * @example
         * When a response content status of "Canceled" is received, it will redirect to the "Failed" status and
         * the details within that "notification" will be used to update the original notification.
         *  {
         *      "Failed": {
         *          notification: {...}
         *      },
         *      "Canceled": "Failed"
         *  }
         */
        readonly responseNotificationByStatus: ResponseNotificationByStatus;
    };
    /**
     * This property provides the instructions on what to poll and
     * what notification properties to use in updating original polling notification for a given response to the polled url
     * received during the execution of an executeAzureAsyncOperation api call.
     * NOTE: If the responseNotificationByStatus and keepPollingStatuses don't have the status received,
     * polling will stop, and the defaultFailureNotification will be used to update the original notification.
     */
    type AzureAsyncPollingDetails = ExecuteAzureAsyncPollingDetails & {
        /**
         * The ARM/ARG uri the notification is expected to poll against.
         */
        readonly uri: string;
    };
    /**
     * This property provides the instructions on what to poll and
     * what notification properties to use in updating original polling notification for a given response to the polled url.
     * NOTE: If the responseNotificationByStatusCode and keepPollingCodes don't have the status code received,
     * polling will stop, and the defaultFailureNotification will be used to update the original notification.
     */
    type PollingDetails = CommonPollingDetails & {
        /**
         * List of HTTP status codes that are the anticipated responses signaling api to keep polling.
         */
        readonly keepPollingCodes: readonly number[];
        /**
         * Details for updates to the original polling notification based on each of a number of possible
         * HTTP response codes received from the polled uri. Whether a success or failure,
         * the notification details are associated with a specific HTTP Status Code.
         *
         * @example
         * When a response code of 200 is received, the details within "notification"
         * will be used to update the original notification.
         *  {
         *      "200": {
         *          notification: {...}
         *      }
         *  }
         * @example
         * When a response code of 202 is received, it will redirect to the "200" code and
         * the details within that "notification" will be used to update the original notification.
         *  {
         *      "200": {
         *          notification: {...}
         *      },
         *      "202": "200"
         *  }
         * @example
         * When any response code inclusively between 200 and 299 is received,
         * the details within that "notification" will be used to update the original notification.
         *  {
         *      "200-299": {
         *          notification: {...}
         *      }
         *  }
         * @example
         * When a response code of 300 is received, it will redirect to the ranged key "200-299" and
         * the details within that "notification" will be used to update the original notification.
         *  {
         *      "200-299": {
         *          notification: {...}
         *      },
         *      "300": "200-299"
         *  }
         */
        readonly responseNotificationByStatusCode: Record<string, PollingResponseDetails | string>;
        /**
         * The uri the notification is expected to poll against.
         * NOTE: Only valid ARM/ARG uris can be relative uris. All non-ARM/ARG uris must be absolute uris.
         */
        readonly uri: string;
    };
    /**
     * Basic instructions for a given polling response scenario.
     * HTTP Status code matches the scenario to the given response,
     * and the notification provides the what-to-do when the scenario arises.
     * Will update the original polling notification with details contained in "notification" property.
     */
    type PollingResponseDetails = {
        /**
         * The notification "update" options to update the original polling notification.
         */
        readonly notification: PollingCompleteOptions;
        /**
         * Requires that a specific value be present in the polled response content.
         * If the value is not present, or is different than expected,
         * the provided failureNotification will be used to update the original polling notification.
         * If not provided, polling will continue.
         */
        readonly requiredResponseValue?: RequiredResponseValue;
        /**
         * Telemetry name for logging to the calling extension's table.
         * Used when "notification" definition above is used to complete this notification.
         * Provides partners with the ability to track the same metrics they could have with shorter running processes.
         * NOTE: Always be cautious about over-logging.
         */
        readonly telemetryName?: string;
    };
    /**
     * Requires that a specific value be present in the polled response content.
     * If the value is not present, or is different than expected,
     * the provided failureNotification will be used. If not provided, polling will continue.
     */
    type RequiredResponseValue = {
        /**
         * The notification options to update the original polling notification,
         * If left undefined, polling will continue.
         */
        readonly failureNotification?: PollingFailedOptions;
        /**
         * Telemetry name for logging to the calling extension's table when:
         * A) failureNotification is defined, and B) failureNotification is used to update notification.
         * Provides partners with the ability to track the same metrics they could have with shorter running processes.
         * NOTE: Always be cautious about over-logging.
         */
        readonly telemetryName?: string;
        /**
         * List of JSON-friendly keys pointing to the location within polled response content where
         * the required property value can be found.
         *
         * @example
         *  exampleResponse1.content: {
         *      "name": "testName",
         *      "properties": {
         *          "desiredProp": "desiredValue"
         *      }
         *  };
         *
         *  exampleResponse2.content: {
         *      "name": "testName",
         *      "properties": {
         *          "desiredProp": "keep polling value"
         *      }
         *  };
         *
         *  requiredResponseValue = {
         *      location: [ "properties", "desiredProp" ],
         *      value: "desiredValue",
         *      pollingValues: ["keep polling value", "alternate polling value"],
         *  };
         *
         *  example content #1: Will find "desiredValue" from response.content using location keys and first compare it against "pollingValues".
         *  When it doesn't match, it will compare it against the "value". Since both contain "desiredValue", it passes and uses the "notification" to update.
         *
         *  example content #2: Will find "keep polling value" from response.content using location keys and first compare it against elements in "pollingValues".
         *  When it does match, the api will continue to poll.
         *
         *  If the location keys fail to find a matching "value" or "pollingValues", the contents of "failureNotification"
         *  are used to update the original polling notification and polling stops.
         *  If "failureNotification" is not provided, polling will continue until a match is found or the timeout condition is reached.
         */
        readonly location: readonly string[];
        /**
         * The required value to compare against the value to be found within the polling response content.
         * Can be set to anything other than undefined.
         */
        readonly value: any;
        /**
         * The values to compare against the value found within the polling response content, and if match is found, signals the api to keep polling.
         */
        readonly pollingValues?: (string | number | boolean)[];
    };
    /**
     * Optional amount of time to allow polling before timing out,
     * and the notification details to use in updating the original polling notification.
     */
    type TimeoutDetails = {
        /**
         * The notification "update" options to update the original polling notification when polling times out.
         */
        readonly failureNotification: PollingFailedOptions;
        /**
         * Amount of time in milliseconds to continue polling before falling back to the failureNotification.
         * The default is 24 hours, and only a pollingTimeout of less than 24 hours will be honored.
         */
        readonly timeoutInMs: number;
        /**
         * Telemetry name for logging to the calling extension's table.
         * Used when "failureNotification" definition above is used to complete this notification.
         * Provides partners with the ability to track the same metrics they could have with shorter running processes.
         * NOTE: Always be cautious about over-logging.
         */
        readonly telemetryName?: string;
    };
    /**
     * Status accompanying a notification.
     */
    const enum Status {
        /**
         * An Information notification with icon containing lowercase white i on a blue circular background.
         */
        Information = 0,
        /**
         * A Warning notification with icon containing white ! on a yellow triangular background.
         */
        Warning = 1,
        /**
         * An Error notification with icon containing white ! on a red circular background.
         */
        Error = 2,
        /**
         * An in progress notification with animated ellipsis icon.
         */
        InProgress = 3,
        /**
         * A Success notification with icon containing white checkmark on a green circular background.
         */
        Success = 4
    }
    /**
     * Status accompanying a notification with no remaining updates.
     */
    const enum CompletedStatus {
        /**
         * An Information notification with icon containing lowercase white i on a blue circular background.
         */
        Information = 0,
        /**
         * A Warning notification with icon containing white ! on a yellow triangular background.
         */
        Warning = 1,
        /**
         * An Error notification with icon containing white ! on a red circular background.
         */
        Error = 2,
        /**
         * A Success notification with icon containing white checkmark on a green circular background.
         */
        Success = 4
    }
    /**
     * Notification toast duration.
     */
    const enum ToastDuration {
        /**
         * Toast stays for 5 seconds.
         */
        Short = 0,
        /**
         * Toast stays for 30 seconds.
         */
        Long = 1,
        /**
         * Toast stays till the user manually dismisses it.
         */
        Sticky = 2,
        /**
         * Suppress toast notification.
         */
        Suppress = 3,
        /**
         * Modal toast notification. By default this option won't be allowed unless white listed.
         * If you need to onboard a modal notification, please contact the Portal team.
         */
        Modal = 4
    }
    namespace IrisNotifications {
        /**
         * Placement information for requesting Iris content.
         */
        interface Placement {
            /**
             * The placement id.
             */
            readonly placementId: string;
            /**
             * The max number of message to receive for the placement.
             */
            readonly messageLimit: number;
        }
        /**
         * Represents a single Iris error.
         */
        interface Error {
            /**
             * The error code from Iris (not HTTP status code). Not used.
             */
            readonly code: number;
            /**
             * Human-readable error message.
             */
            readonly msg: string;
        }
        /**
         * Iris type singular message item.
         */
        interface Message<T = any> {
            /**
             * Body of the message that represents the data contract for a particular campaign template. This is a customizable dynamic data structure pre-defined between Iris and requesters.
             */
            readonly ad: T;
            /**
             * Wrapper containing the uri for broadcasting impression beacons. Impression beacons should be sent when the message is activated/shown to the target user so the message frequency can be capped.
             */
            readonly prm: {
                readonly _imp: string;
            };
            /**
             * Wrapper containing the uri for broadcasting actions beacon (can be any telemetry action such as message received signals or user interactions).
             */
            readonly tracking: {
                readonly baseUri: string;
            };
        }
        /**
         * Represents the processed Iris response for downstream consumption.
         * Maps placement id to the items and/or errors. Arrays could be empty.
         */
        type IrisContent = ReadonlyStringMap<{
            /**
             * The Iris messages for the placement.
             */
            readonly items?: readonly Message[];
            /**
             * The Iris errors for the placement.
             */
            readonly errors?: readonly Error[];
        }>;
        /**
         * Arguments for calling the getIrisContent API.
         */
        interface GetIrisContentOptions {
            /**
             * List of placements to fetch content.
             */
            readonly placements: Placement[];
        }
        /**
         * Signal information to send to Iris.
         */
        interface SignalOptions {
            /**
             * The iris message that the signal will be sent to.
             */
            readonly message: Message;
            /**
             * If specified, action signals will be sent. Otherwise impression signals will be sent.
             */
            readonly action?: ActionOptions;
        }
        /**
         * Action information to send to Iris.
         */
        interface ActionOptions {
            /**
             * The action name, such as click, like, hover.
             * A list of standard Iris action can be found here: https://www.osgwiki.com/wiki/Iris_Insights_Beacons#Standard_Iris_actions.
             */
            readonly name: string;
            /**
             * The additional action metadata to send through URL parameters.
             */
            readonly parameters?: ReadonlyStringMap<string>;
        }
    }
}
/**
 * Declarations for various Azure Portal surveys.
 */
export declare namespace Surveys {
    /**
     * Declarations for the In Product Feedback blade (CES/CVA Survey).
     */
    namespace InProductFeedbackBlade {
        /**
         * The parameters for the blade.
         */
        interface Parameters {
            /**
             * The blade on which the survey is instantiated.
             */
            readonly bladeName: string;
            /**
             * The question string to be displayed for the customer effort score section.
             */
            readonly cesQuestion: string;
            /**
             * The question string to be displayed for the customer value add section.
             */
            readonly cvaQuestion: string;
            /**
             * By default the survey has a click to get support infobox, but this flag allows the survey creator to disable/hide the infobox.
             */
            readonly disableSupportInfoBox?: boolean;
            /**
             * The extension instantiating this blade on which the survey is launched from.
             */
            readonly extensionName: string;
            /**
             * The name of the feature the extension is surveying for.
             */
            readonly featureName: string;
            /**
             * The id for the specific survey.
             */
            readonly surveyId: string;
        }
    }
}
export declare namespace Units {
    const higherByteOffset = 8;
    const perTimeBit = 128;
    const enum UnitType {
        None = 0,
        Bytes = 1,
        Decimal = 2,
        Time = 3,
        BytesPerTime = 4,
        DecimalPerTime = 5,
        Bytes_SI = 6,
        BytesPerTime_SI = 7
    }
    /**
     * Defines units.
     */
    const enum Unit {
        None = 0,// UnitType.None << higherByteOffset
        Percentage = 1,// None + 1,
        Bytes = 256,// UnitType.Bytes << higherByteOffset,
        Kilobytes = 257,// Bytes + 1,
        Megabytes = 258,// Bytes + 2,
        Gigabytes = 259,// Bytes + 3,
        Terabytes = 260,// Bytes + 4,
        Petabytes = 261,// Bytes + 5,
        BytesPerDay = 1152,// (UnitType.BytesPerTime << higherByteOffset) | perTimeBit
        BytesPerHour = 1153,// BytesPerDay + 1,
        BytesPerMinute = 1154,// BytesPerDay + 2,
        BytesPerSecond = 1155,// BytesPerDay + 3,
        KilobytesPerSecond = 1156,// BytesPerDay + 4,
        MegabytesPerSecond = 1157,// BytesPerDay + 5,
        GigabytesPerSecond = 1158,// BytesPerDay + 6,
        TerabytesPerSecond = 1159,// BytesPerDay + 7,
        PetabytesPerSecond = 1160,// BytesPerDay + 8,
        Count = 512,// UnitType.Decimal << higherByteOffset
        Thousand = 513,// Count + 1,
        Million = 514,// Count + 2,
        Billion = 515,// Count + 3,
        Trillion = 516,// Count + 4,
        MicroSeconds = 768,//UnitType.Time << higherByteOffset
        MilliSeconds = 769,// MicroSeconds + 1,
        Seconds = 770,// MicroSeconds + 2,
        Minutes = 771,// MicroSeconds + 3,
        Hours = 772,// MicroSeconds + 4,
        Days = 773,// MicroSeconds + 5,
        CountPerDay = 1408,// (UnitType.DecimalPerTime << higherByteOffset) | perTimeBit
        CountPerHour = 1409,// CountPerDay + 1,
        CountPerMinute = 1410,// CountPerDay + 2,
        CountPerSecond = 1411,// CountPerDay + 3,
        ThousandPerSecond = 1412,// CountPerDay + 4,
        MillionPerSecond = 1413,// CountPerDay + 5,
        BillionPerSecond = 1414,// CountPerDay + 6,
        TrillionPerSecond = 1415,// CountPerDay + 7,
        Bytes_SI = 1536,// UnitType.Bytes_SI << higherByteOffset
        Kilobytes_SI = 1537,// Bytes_SI + 1,
        Megabytes_SI = 1538,// Bytes_SI + 2,
        Gigabytes_SI = 1539,// Bytes_SI + 3,
        Terabytes_SI = 1540,// Bytes_SI + 4,
        Petabytes_SI = 1541,// Bytes_SI + 5,
        BytesPerDay_SI = 1920,// (UnitType.BytesPerTime_SI << higherByteOffset) | perTimeBit
        BytesPerHour_SI = 1921,// BytesPerDay_SI + 1,
        BytesPerMinute_SI = 1922,// BytesPerDay_SI + 2,
        BytesPerSecond_SI = 1923,// BytesPerDay_SI + 3,
        KilobytesPerSecond_SI = 1924,// BytesPerDay_SI + 4,
        MegabytesPerSecond_SI = 1925,// BytesPerDay_SI + 5,
        GigabytesPerSecond_SI = 1926,// BytesPerDay_SI + 6,
        TerabytesPerSecond_SI = 1927,// BytesPerDay_SI + 7,
        PetabytesPerSecond_SI = 1928
    }
}
export declare namespace Assets {
    /**
     * Defines the locations in portal where a given command can be shown.
     */
    const enum CommandVisibility {
        /**
         * Allows a command to appear on browse toolbar.
         */
        BrowseToolbar = 1,
        /**
         * Allows a command to appear in browse context menu.
         *
         * NOTE: Only selection based commands with minSelection === 1 support this option.
         *       Menu commands do not support this option.
         */
        BrowseContextMenu = 2,// BrowseToolbar << 1,
        /**
         * Allows a command to appear on empty browse view.
         */
        BrowseEmptyView = 4,// BrowseContextMenu << 1,
        /**
         * Allows a command to appear on resource hover card.
         */
        ResourceHoverCard = 8,// BrowseEmptyView << 1,
        /**
         * Allows a command to be hidden by default.
         *
         * NOTE: This is useful if you are experimenting with command bar layout and wish to only show a command via experimentation.
         */
        HiddenByDefault = 16,// ResourceHoverCard << 1,
        /**
         * Allows a command to replace default "create" button on a service hover card.
         *
         * NOTE: Only one command with this flag is supported per asset type.
         */
        ServiceHoverCard = 32
    }
    /**
     * The blade reference options for open blade command.
     */
    type BladeReferenceOptions = {
        /**
         * The blade name.
         */
        readonly blade: string;
        /**
         * The flag indicating whether blade supports provisioning.
         * Defaults to false.
         */
        readonly doesProvisioning?: boolean;
        /**
         * The extension name for the blade
         */
        readonly extension?: string;
        /**
         * The flag indicating whether blade needs to be opened as a context pane.
         * Defaults to false.
         */
        readonly inContextPane?: boolean;
        /**
         * The blade parameters.
         */
        readonly parameters?: StringMap<any>;
    };
    /**
     * Interface for resource selection in browse commands.
     */
    type BrowseResourceSelection = {
        /**
         * The max number of selected resources supported by the command operation.
         */
        readonly maxSelectedItems?: number;
        /**
         * The min number of selected resources supported by the command operation.
         */
        readonly minSelectedItems?: number;
        /**
         * The message shown when user tries to select more than supported items by the command operation.
         */
        readonly disabledMessage?: string;
    };
    /**
     * The interface for command execution confirmation options.
     */
    type ConfirmationOptionsManifest = {
        /**
         * The confirmation title.
         */
        readonly title?: string;
        /**
         * The confirmation message.
         */
        readonly message: string;
        /**
         * The confirmation text input.
         * User needs to enter this text in order to confirm command execution.
         */
        readonly explicitConfirmationText?: string;
    };
    /**
     * Various opt-in configs to describe how long running ARM operations needs to be polled and results processed for Bulk commanding.
     */
    type AsyncOperationOptions = {
        /**
         * By default when http Accepted (202) status code is received, the Location header will be looked up for polling uri to get the status of long running operation.
         * A different response header can be specified with the pollingHeaderOverride value.
         */
        readonly pollingHeaderOverride?: string;
        /**
         * A property path to look for status in the response body.
         * By default 'status' property will be looked up to see if it has "Succeeded", "Failed", "InProgress" or "Canceled".
         */
        readonly statusPath?: string;
    };
    /**
     * Command definition for ARM operation.
     */
    type ArmCommandDefinition = {
        /**
         * Http method POST/DELETE/PATCH etc. By default POST will be used.
         */
        readonly httpMethodType?: string;
        /**
         * ARM uri for the command operation. Currently only ARM operations are supported.
         * Uri should be a relative uri with the fixed format - `{resourceid}/placeholder`.
         * Eg. `{resourceid}/start?api-version=2016-03-30`
         */
        readonly uri: string;
        /**
         * ARM command operation can be long running operation. asyncOperation property specifies how to poll the status for completion of long running operation.
         */
        readonly asyncOperation?: AsyncOperationOptions;
        /**
         * The list of resource-specific ARM error codes that should be retried.
         */
        readonly retryableArmCodes?: readonly string[];
        /**
         * The list of resource-specific ARM error codes that shouldn't be retried.
         * This helps optimize network calls and improve bulk operation performance.
         */
        readonly nonRetryableArmCodes?: readonly string[];
    };
    /**
     * Interface for ARM command options.
     */
    type ArmCommandOptionsManifest = {
        /**
         * The ARM bulk command definitions.
         */
        readonly definitions?: StringMap<ArmCommandDefinition>;
        /**
         * The flag indicating whether to launch Fx bulk delete confirmation blade for bulk delete operations.
         *
         * NOTE: All ARM bulk delete commands should set this flag to true for consistent bulk delete confirmation experience.
         */
        readonly isDelete?: boolean;
    };
    /**
     * Interface for asset type command manifest.
     */
    type CommandManifest = {
        /**
         * Specify a unique identifier or friendly command name (non localized string). This will be used for telemetry.
         */
        readonly id: string;
        /**
         * Command label shown in the toolbar.
         */
        readonly label: string;
        /**
         * Command icon shown in the toolbar.
         */
        readonly icon: Images.Image;
        /**
         * Command aria label.
         */
        readonly ariaLabel?: string;
        /**
         * Command tooltip shown on hover.
         */
        readonly toolTip?: string;
        /**
         * Content used for displaying subtitle for the menu command items only.
         */
        readonly content?: string;
        /**
         * The command visibility options.
         * Specify one or more options in the format: `CommandVisibility.BrowseToolbar | CommandVisibility.BrowseEmptyView`.
         */
        readonly visibility?: CommandVisibility;
        /**
         * The blade reference.
         */
        readonly bladeReference?: BladeReferenceOptions;
        /**
         * The marketplaceItemId to open create flows.
         */
        readonly marketplaceItemId?: string;
        /**
         * The asset type to open create flows.
         */
        readonly createAssetType?: {
            /**
             * The extension of the asset type invoking the blade.
             */
            readonly extensionName: string;
            /**
             * The asset type invoking the blade.
             */
            readonly assetType: string;
            /**
             * The asset kind invoking the blade.
             */
            readonly assetKind?: string;
            /**
             * The marketplace id invoking the blade.
             */
            readonly marketplaceId?: string;
            /**
             * The parameters to pass to the asset type create blade.
             */
            readonly parameters?: StringMapPrimitive;
        };
        /**
         * The browse grid selection model.
         */
        readonly selection?: BrowseResourceSelection;
        /**
         * The command execution confirmation options.
         */
        readonly confirmation?: ConfirmationOptionsManifest;
        /**
         * The ARM bulk command definition options.
         */
        readonly armCommandDefinitionOptions?: ArmCommandOptionsManifest;
        /**
         * The list of commands.
         */
        readonly commands?: readonly CommandManifest[];
    };
    /**
     * The interface for ARM bulk commands.
     */
    type ArmStaticCommand = CommandManifest & {
        /**
         * The ARM bulk command definitions.
         */
        readonly armCommandDefinitions?: StringMap<ArmCommandDefinition>;
        /**
         * The flag indicating whether to launch Fx bulk delete confirmation blade for bulk delete operations.
         *
         * NOTE: All ARM bulk delete commands should set this flag to true for consistent bulk delete confirmation experience.
         */
        readonly isBulkDeleteOperation?: boolean;
    };
    /**
     * This represents an asset type display name in it's four forms.
     */
    type CompositeDisplayName = {
        /**
         * The singular, formal-cased display name.
         */
        singular: string;
        /**
         * The plural, formal-cased display name.
         */
        plural: string;
        /**
         * The singular, lower-cased display name.
         */
        lowerSingular: string;
        /**
         * The plural, lower-cased display name.
         */
        lowerPlural: string;
        /**
         * The service display name.
         */
        service?: string;
    };
    /**
     * Represents a set of flags for the contracts supported by the asset view model.
     */
    const enum AssetTypeContracts {
        /**
         * No contracts supported by the asset type.
         */
        None = 0,
        /**
         * OBSOLETE - do not remove, do not use.
         */
        ObsoleteAssetInfo = 1,// 1
        /**
         * The asset type supports the browse config contract.
         */
        BrowseConfig = 2,// ObsoleteAssetInfo << 1, // 1 << 1
        /**
         * The asset type supports the supplemental data contract.
         */
        SupplementalData = 4,// BrowseConfig << 1, // 1 << 2
        /**
         * The asset type supports the resource menu config contract.
         */
        ResourceMenuConfig = 8,// SupplementalData << 1, // 1 << 3
        /**
         * The asset type supports the resource menu config contract but only for kinds marked as use resource menu.
         */
        KindResourceMenuConfig = 16,// ResourceMenuConfig << 1, // 1 << 4
        /**
         * The asset type supports static resource menu overview.
         */
        StaticResourceMenuOverview = 32,// KindResourceMenuConfig << 1, // 1 << 5
        /**
         * The asset type supports providing resources contract.
         */
        ProvidesResources = 64,// StaticResourceMenuOverview << 1, // 1 << 6
        /**
         * The asset type's resource menu blade can load the resource provided by the extension.
         */
        ExtensionSuppliesResourceForResourceMenu = 128,// ProvidesResources << 1, // 1 << 7
        /**
         * The asset type's resource menu blade can ignore the resource.
         */
        NoResourceForResourceMenu = 256,// ExtensionSuppliesResourceForResourceMenu << 1, // 1 << 8
        /**
         * The asset type has the kind override flags.
         */
        HasKindOverrideFlags = 512,// NoResourceForResourceMenu << 1, // 1 << 9
        /**
         * The asset type has a kind which overrides the use resource menu flag.
         */
        HasKindWhichOverridesUseResourceMenu = 1024,// HasKindOverrideFlags << 1, // 1 << 10
        /**
         * The asset type has a kind which overrides the display name.
         */
        HasKindWhichOverridesDisplayName = 2048,// HasKindWhichOverridesUseResourceMenu << 1, // 1 << 11
        /**
         * The asset type has a kind which overrides the blade.
         */
        HasKindWhichOverridesBlade = 4096,// HasKindWhichOverridesDisplayName << 1, // 1 << 12
        /**
         * The asset type has a kind which overrides the icon.
         */
        HasKindWhichOverridesIcon = 8192,// HasKindWhichOverridesBlade << 1, // 1 << 13
        /**
         * The asset type supports browse using a query from PDL.
         */
        SupportsBrowseQuery = 16384,// HasKindWhichOverridesIcon << 1, // 1 << 14
        /**
         * The asset type supports a declarative menu.
         */
        SupportsAssetMenu = 32768,// SupportsBrowseQuery << 1, // 1 << 15
        /**
         * The asset type source language is Dx. This is not needed at runtime, only for reporting purposes.
         * Using the last (non sign) bit for easy deprecation.
         */
        DxSourceFile = 1073741824
    }
    /**
     * The documentation link for the asset type.
     */
    type Link = {
        /**
         * The link title for the asset type.
         */
        title: string;
        /**
         * The link uri for the asset type.
         */
        uri: string;
    };
    /**
     * The browse command experiments for the asset type.
     */
    type AssetTypeBrowseCommandLayout = {
        /**
         * The array of non selection commands.
         */
        readonly commands?: readonly string[];
        /**
         * The array of selection commands.
         */
        readonly selectionCommands?: readonly string[];
    };
    const enum ProxyRoutingFilter {
        /**
         * Text proxy routing filter.
         */
        TextFilter = 1,
        /**
         * Resource group proxy routing filter.
         */
        ResourceGroupFilter = 2,
        /**
         * Location proxy routing filter.
         */
        LocationFilter = 3
    }
    /**
     * Pre-defined columns for resource types.
     */
    const enum ResourceColumnIds {
        /**
         * The name resource column.
         */
        Name = 1,
        /**
         * The kind resource column.
         */
        Kind = 2,
        /**
         * The resource group resource column.
         */
        ResourceGroup = 3,
        /**
         * The location resource column.
         */
        Location = 4,
        /**
         * The location ID resource column.
         */
        LocationId = 5,
        /**
         * The resource ID resource column.
         */
        ResourceId = 6,
        /**
         * The resource group ID resource column.
         */
        ResourceGroupId = 7,
        /**
         * The resource type resource column.
         */
        ResourceType = 8,
        /**
         * The subscription resource column.
         */
        Subscription = 9,
        /**
         * The subscription ID resource column.
         */
        SubscriptionId = 10,
        /**
         * The asset type resource column.
         */
        AssetType = 11,
        /**
         * The tags resource column.
         */
        Tags = 12,
        /**
         * The edge zone resource column.
         */
        EdgeZone = 13
    }
    const enum ColumnFormat {
        /**
         * The column has no format (used only in typescript).
         */
        NoFormat = 0,
        /**
         * Simple string column direct from ARG results.
         */
        String = 1,
        /**
         * Resource column which is an ARM ID from ARG results.
         */
        Resource = 2,
        /**
         * Simple date column direct from ARG results.
         */
        Date = 3,
        /**
         * The result from ARG will be mapped as a number based on the user's current locale.
         */
        Number = 4,
        /**
         * The result from ARG will be mapped to a location display name.
         */
        Location = 5,
        /**
         * Simple string column direct from ARG results that will launch a blade using the Blade property of the column.
         */
        BladeLink = 6,
        /**
         * The result from ARG will be mapped to a tenant display name.
         */
        Tenant = 7,
        /**
         * The result from ARG will be the text for the column representing the resource status.
         */
        Status = 8,
        /**
         * Object column direct from ARG results that will launch a deep link.
         */
        DeepLink = 9,
        /**
         * Object column direct from ARG results that will launch a blade.
         */
        QueryBladeLink = 10,
        /**
         * Object column direct from ARG results that will launch an external link in a new tab.
         */
        ExternalLink = 11
    }
    const enum SummaryVisualizations {
        /**
         * The summary has no visualizations.
         */
        NoVisualizations = 0,
        /**
         * The summary is available as a map visualization (must be location summary).
         */
        Map = 1,
        /**
         * The summary is available as a bar chart visualization.
         */
        BarChart = 2,
        /**
         * The summary is available as a donut chart visualization.
         */
        DonutChart = 4,
        /**
         * The summary is available as a grid (list) visualization.
         */
        Grid = 8,
        /**
         * The result from ARG will be mapped as a number based on the user's current locale.
         */
        Default = 2147483646,
        /**
         * The result from ARG will be mapped to a location display name.
         */
        DefaultWithMap = 2147483647
    }
    type BrowseColumnManifest = {
        /**
         * Name of the column.
         */
        name: string;
        /**
         * Optional name of the column used for sorting the column.
         */
        sortColumn?: string | ResourceColumnIds;
        /**
         * Localized display name of the column.
         */
        displayName: string;
        /**
         * Localized lowercase display name of the column.
         */
        lowerDisplayName?: string;
        /**
         * Optional description localized string of the column.
         */
        description?: string;
        /**
         * Columns format of the column.
         */
        format: ColumnFormat;
        /**
         * Optional width of the column (in grid units).
         */
        width?: string;
        /**
         * Optional source units the number column.
         */
        sourceUnits?: Units.Unit;
        /**
         * Optional maximum fraction digits the number column.
         */
        maximumFractionDigits?: number;
        /**
         * Optional blade name for BladeLink columns.
         */
        bladeName?: string;
        /**
         * Optional blade extension name for BladeLink columns.
         */
        bladeExtensionName?: string;
        /**
         * Optional name of the column for the blade parameters for BladeLink columns.
         */
        bladeParameterColumn?: string | ResourceColumnIds;
        /**
         * Optional flag to launch the blade in a context pane for BladeLink and QueryBladeLink columns.
         */
        openBladeAsContextPane?: boolean;
        /**
         * Optional name of the column for the icon for Status columns.
         */
        iconColumn?: string | ResourceColumnIds;
        /**
         * Optional flag to prevent using this column for the summary columns.
         */
        preventSummary?: boolean;
        /**
         * Optional query for the column used for the summary for this column. This is used for summary drill down when
         * the summary produces a new ARG column. This query should extend the new ARG column into the results and the
         * `summaryQuery` is used to summarize over that column.
         *
         * @example
         * - A resource type 'lenses' produces a new ARG column 'focalLength' which is based on a double value
         * 'focalValue' and the summary will be for the 'focalLength' string:
         *
         * The query for `columnQueryForSummary` must produce (extend) the column:
         *
         * ```
         * extend focalLength = case(
         *     (focalValue < 50), 'wide',
         *     (focalValue < 120), 'normal',
         *     'telephoto')
         * ```
         *
         * The query for `summaryQuery` can then use the produced column:
         *
         * ```
         * summarize focalLengthCount=count() by focalLength
         * ```
         *
         * The value of `summaryColumn` then points to the 'focalLength' column.
         */
        columnQueryForSummary?: string;
        /**
         * Optional query for the summarization for this column. If the `columnQueryForSummary` is provided, that is
         * prepended to this query for the summarization to produce any required ARG columns needed by the
         * `summaryQuery`.
         *
         * @see `columnQueryForSummary`
         */
        summaryQuery?: string;
        /**
         * Optional name of the summary column for the column that is produced by the `summaryQuery`.
         *
         * @see `columnQueryForSummary` and `summaryQuery`
         */
        summaryColumn?: string;
        /**
         * Optional summary visualizations for the column. If not set, standard bar and donut charts along with grid (list) are used.
         */
        summaryVisualizations?: SummaryVisualizations;
    };
    type MergedResourceTypeManifest = {
        /**
         * Name of the resource type.
         */
        resourceTypeName: string;
        /**
         * Name of the resource type kind.
         */
        resourceKindName?: string;
        /**
         * Optional flag indicating whether the merged resource type is selected by default.
         */
        selected?: boolean;
        /**
         * Additional kinds if the resourceKindName is set. This is used for merged kinds where the resourceKindName is used
         * for the display name but the resourceKindName and the additionalKinds are combined for the query filtering.
         */
        additionalKinds?: string[];
    };
    /**
     * Asset type command definitions including Browse bulk commanding.
     */
    type CommandSet = {
        /**
         * List of kinds that support given commands.
         */
        readonly kinds?: readonly string[];
        /**
         * Command definitions generic operations.
         */
        readonly commands?: readonly CommandManifest[];
        /**
         * Command definitions for resource selection based operations.
         */
        readonly selectionCommands?: readonly CommandManifest[];
    };
    const enum BrowseInfoBoxStyle {
        /**
         * Info style.
         */
        Info = 1,
        /**
         * Upsell style.
         */
        Upsell = 2,
        /**
         * Success style.
         */
        Success = 3,
        /**
         * Warning style.
         */
        Warning = 4,
        /**
         * Error style.
         */
        Error = 5
    }
    type BrowseInfoBoxManifest = {
        /**
         * The display string for the info box.
         */
        display: string;
        /**
         * The style of the info box.
         */
        style: BrowseInfoBoxStyle;
        /**
         * Optional flag to hide the info box (can be overridden in config).
         */
        hidden?: boolean;
        /**
         * Optional URI for a link for the info box.
         */
        linkUri?: string;
        /**
         * Optional target for a link for the info box.
         */
        linkTarget?: string;
        /**
         * Optional blade name for a blade link for the info box.
         */
        bladeName?: string;
        /**
         * Optional extension name for a blade link for the info box.
         */
        bladeExtensionName?: string;
        /**
         * Optional flag to open blade in context pane for a blade link for the info box.
         */
        openBladeAsContextPane?: boolean;
    };
    type BrowseManifest = {
        /**
         * Name of the asset type of the browse definition.
         */
        name: string;
        /**
         * Browse query for ARG browse.
         */
        browseQuery: string;
        /**
         * Array of default columns for the browse (either a column name or a built-in column).
         */
        defaultColumns: (string | ResourceColumnIds)[];
        /**
         * The array of column IDs for additional filter pills displayed by default.
         * Filter pills for columns specified here will be displayed with initial "all selected" value.
         */
        defaultFilters?: (string | ResourceColumnIds)[];
        /**
         * Array of columns to exclude from the browse.
         */
        excludeColumns?: (ResourceColumnIds.SubscriptionId | ResourceColumnIds.ResourceGroup | ResourceColumnIds.Location | ResourceColumnIds.Tags)[];
        /**
         * Array of column definitions.
         */
        columns: BrowseColumnManifest[];
        /**
         * Optional column which contains the resource status to display in the resource part.
         */
        statusColumn?: string;
        /**
         * Array of merged resource type definitions.
         */
        mergedResourceTypes?: MergedResourceTypeManifest[];
        /**
         * Optional flag which indicates whether to honor the selected flag on the merged resource types.
         */
        honorSelectedMergedResourceTypes?: boolean;
        /**
         * Array of asset type command definitions.
         */
        commands?: CommandSet;
        /**
         * Map of kind level command definitions.
         */
        kindCommands?: ReadonlyStringMap<CommandSet>;
        /**
         * Map of kind name to the key in kindCommands map.
         *
         * This is generated in order to avoid duplicating commands for merged kinds
         */
        kindCommandNameMapping?: ReadonlyStringMap<string>;
        /**
         * Optional parent resource type for browse.
         *
         * The parent resource type if defined will determine the display name of the browse blade as well as the
         * resource type used for browse blade view management.
         */
        browseParentResourceType?: string;
        /**
         * Optional info box for browse.
         */
        infoBox?: BrowseInfoBoxManifest;
        /**
         * Optional flag to indicate that edge zones are supported by the query supplied in browseQuery.
         */
        edgeZonesSupported?: boolean;
        /**
         * Optional feature cards which are enabled for browse.
         */
        featureCards?: BrowseFeatureCardReference[];
    };
    /**
     * Represents a feature card reference for browse.
     */
    type BrowseFeatureCardReference = {
        /**
         * The feature card ID.
         */
        readonly id: string;
        /**
         * The extension that owns the feature card.
         */
        readonly extension: string;
        /**
         * The enabled value (boolean or experiment name) for the feature card.
         */
        readonly enabled: boolean | string;
    };
    /**
     * Represents a feature card from an extension manifest.
     */
    type FeatureCardManifest = {
        /**
         * ID of the feature card.
         */
        readonly id: string;
        /**
         * Label of the feature card.
         */
        readonly label: string;
        /**
         * Tooltip of the feature card.
         */
        readonly tooltip: string;
        /**
         * Icon of the feature card.
         */
        readonly icon: Images.Image;
        /**
         * Count query for the feature card.
         */
        readonly countQuery: string;
        /**
         * Name for the target blade of the feature card.
         */
        readonly bladeName: string;
        /**
         * Extension for the target blade of the feature card.
         */
        readonly bladeExtensionName: string;
        /**
         * Blade parameters for the target blade of the feature card.
         */
        readonly bladeParameters: any;
        /**
         * The resource type filter of the feature card.
         */
        readonly resourceTypes: ReadonlyStringMap<boolean | string>;
    };
    /**
     * The feature card extension and manifest pair.
     */
    type FeatureCard = {
        /**
         * The extension that owns the feature card manifest.
         */
        readonly extension: string;
        /**
         * The manifest for the feature card.
         */
        readonly manifest: Assets.FeatureCardManifest;
    };
    /**
     * Represents a reference to a blade to launch upon initiating creation of a resource through the framework provisioner.
     */
    type PostCreateBladeReference = {
        /**
         * The blade name.
         */
        readonly bladeName: string;
        /**
         * The extension name. If not provided, this defaults to the current extension name.
         */
        readonly extensionName: string;
        /**
         * Am optional flag indicating the blade should be used based on an experiment treatment.
         */
        readonly useFlighting?: boolean;
    };
}
export declare namespace Browse {
    /**
     * The browse command item for extensible commands.
     */
    type BrowseCommandItem = {
        /**
         * The type of the command item.
         */
        readonly itemType: "openBlade" | "openMarketplace" | "openCreateAssetType" | "menu" | "selectionBulkDelete" | "selectionOpenBlade" | "selectionArmBulkCommand" | "selectionMenu";
        /**
         * The command definition manifest.
         */
        readonly commandDefinition: Assets.CommandManifest;
        /**
         * The asset type disabled flag.
         */
        readonly isDisabled?: boolean;
        /**
         * Child command items for menu command items.
         */
        readonly commandItems?: BrowseCommandItem[];
    };
    /**
     * The browse data for the resource type.
     */
    type BrowseData = {
        /**
         * The resource type the browse data was requested for.
         */
        readonly resourceType: string;
        /**
         * The resource type defaults for the browse resource type.
         */
        readonly resourceTypeDetails: BrowseTypeInformation;
        /**
         * The browse prereqs for the browse resource type.
         */
        readonly browsePrereqs: BrowsePrereqs;
        /**
         * Dictionary mapping column type enum to column name.
         */
        readonly columnIdToStringMap: Record<Assets.ResourceColumnIds, string>;
        /**
         * Only override columns different than in columnIdToStringMap.
         */
        readonly columnIdToArgColumn: Partial<Record<Assets.ResourceColumnIds, string>>;
        /**
         * The empty browse link from FX.
         */
        readonly emptyBrowseLink: string;
    };
    /**
     * Browse filter value including the filter, the operator, the values and a flag to indicate all values.
     */
    type BrowseFilterValue = {
        /**
         * The filter item (filter column).
         */
        readonly filter: FilterItem;
        /**
         * The operator for the filter.
         */
        readonly operator: string;
        /**
         * The array of filter values.
         */
        readonly values: FilterValue[];
        /**
         * Flag which indicates all the values are selected.
         */
        readonly isAllValues?: boolean;
    };
    /**
     * Base type of the item displayed in React Browse Grid.
     */
    type BrowseItemBase = {
        /**
         * The resource ID for the recent resource.
         */
        resourceId: string;
        /**
         * The display name of the resource if it's not directly extractable from the resource ID.
         */
        name?: string;
        /**
         * The location as is returned by ARM.
         */
        location?: string;
        /**
         * The resource kind returned by ARM.
         */
        resourceKind?: string;
        /**
         * Resource tags
         */
        tags?: Record<string, string>;
    };
    /**
     * The browse location data which is a subset of the ARM location (MsPortalFx.Azure.Location).
     */
    type BrowseLocation = {
        /**
         * The display name of the location.
         */
        displayName: string;
        /**
         * The latitude of the location.
         */
        latitude?: number | string;
        /**
         * The longitude of the location.
         */
        longitude?: number | string;
    };
    /**
     * The resource columns interface for persistence to user settings.
     */
    type ResourceColumnSettings = {
        /**
         * The column ID.
         */
        readonly id: string;
        /**
         * The column visibility.
         */
        readonly visible: boolean;
    };
    /**
     * Data model for a tenant.
     */
    type Tenant = {
        /**
         * Tenant id.
         */
        readonly id: string;
        /**
         * The tenant domain name.
         */
        readonly domainName: string;
        /**
         * The tenant display name.
         */
        readonly displayName: string;
        /**
         * The tenant category.
         */
        readonly tenantCategory: string;
    };
    /**
     * The browse prereq's for ARG browse.
     */
    type BrowsePrereqs = {
        /**
         * The query manifest.
         */
        readonly browseQueryManifest: Assets.BrowseManifest;
        /**
         * The location map from ID to location data.
         */
        readonly locations: StringMap<BrowseLocation>;
        /**
         * The asset type map from resource type to resource type details.
         */
        readonly assetTypes: StringMap<ArgInterfacesCore.ResourceTypeDetails>;
        /**
         * Optional column settings if requested.
         */
        readonly columnSettings?: StringMap<ResourceColumnSettings[]>;
        /**
         * Optional favorite view ID.
         */
        readonly favoriteViewId?: string;
        /**
         * Optional directories map if specified columns contain a tenant column.
         */
        readonly directoriesMap?: StringMap<Tenant>;
        /**
         * Optional collection of feature card manifests.
         */
        readonly featureCards?: Assets.FeatureCard[];
    };
    /**
     * Browse type information provides the base information required for the browse type.
     */
    type BrowseTypeInformation = {
        readonly assetType?: string;
        /**
         * The marketplace ids that are being redirected into this noPdlCreateBlade
         */
        readonly claimedMarketplaceIds?: string[];
        /**
         * The name of the extension with the create blade.
         */
        readonly noPdlCreateExtension?: string;
        /**
         * The parameters to the create blade.
         */
        readonly noPdlCreateParameters?: any;
        /**
         * The create blade name.
         */
        readonly noPdlCreateBlade?: string;
        /**
         * The composite display name (partial values).
         */
        readonly compositeDisplayName: {
            /**
             * The plural name for the browse type.
             */
            readonly plural: string;
            /**
             * The lowercase plural name for the browse type.
             */
            readonly lowerPlural: string;
            /**
             * The lowercase singular name for the browse type.
             */
            readonly lowerSingular: string;
            /**
             * The optional service name for the browse type.
             */
            readonly service?: string;
        };
        /**
         * The extension name for the fully qualified asset type.
         */
        readonly extensionName: string;
        /**
         * The flag to hide (or show) the browse info box (overrides PDL BrowseInfoBox).
         */
        readonly hideBrowseInfoBox?: boolean;
        /**
         * The kind map which the display name values needed.
         */
        readonly kindMap?: StringMap<{
            /**
             * This is an internal-only flag to pass config information to the browse query manifest to override this flag.
             */
            readonly hideBrowseInfoBox?: boolean;
            /**
             * The optional service name for the browse type's kind.
             */
            readonly serviceDisplayName?: string;
            /**
             * The optional plural name for the browse type's kind.
             */
            readonly pluralDisplayName?: string;
            /**
             * The optional lowercase plural name for the browse type's kind.
             */
            readonly lowerPluralDisplayName?: string;
            /**
             * The name of the extension with the create blade.
             */
            readonly noPdlCreateExtension?: string;
            /**
             * The parameters to the create blade.
             */
            readonly noPdlCreateParameters?: any;
            /**
             * The create blade name.
             */
            readonly noPdlCreateBlade?: string;
            /**
             * The marketplace ids that are being redirected into this noPdlCreateBlade
             */
            readonly claimedMarketplaceIds?: string[];
            /**
             * The market place item.
             */
            readonly marketplaceItemId?: string;
            /**
             * The market place category.
             */
            readonly marketplaceMenuItemId?: string;
        }>;
        /**
         * The resource type.
         */
        readonly resourceType?: string;
        /**
         * The routing type for the resource type.
         */
        readonly routingType?: RoutingType;
    };
    /**
     * Interface for Saved browse view.
     */
    type BrowseView = BrowseViewDescription & {
        /**
         * The filter string.
         */
        readonly filter?: string;
        /**
         * The list of filter facets.
         * The FacetFilter is used only for back compatibility with old views, new views should use the BrowseFilterValue.
         */
        readonly filterFacets?: (BrowseFilterValue | FacetFilter<FacetValue[] | string>)[];
        /**
         * The group by value.
         */
        readonly groupByValue?: string;
        /**
         * Optional sort format for the group by column.
         */
        readonly groupByFormat?: SortFormat;
        /**
         * The list of sorted columns.
         */
        readonly sortedColumns?: SortedColumn[];
        /**
         * The flag indicating if all values are selected.
         */
        readonly showAll?: boolean;
        /**
         * The list of grid columns.
         */
        readonly gridColumns?: {
            /**
             * The column ID.
             */
            readonly id: string;
            /**
             * The column name.
             */
            readonly name: string;
            /**
             * The column visibility.
             */
            readonly visible?: boolean;
        }[];
        /**
         * The grid column widths.
         *
         * TODO andrewfo : initial view = NOT DONE
         *                    save view = NOT DONE
         *                    load view = NOT DONE
         */
        readonly gridColumnWidths?: Record<string, string>;
        /**
         * The optional subscription IDs for the saved view.
         */
        readonly subscriptionIds?: string[];
        /**
         * The optional selected subscription IDs for the saved view.
         * This is only valid when subscriptionIds is valid.
         */
        readonly selectedSubscriptionIds?: string[];
    };
    /**
     * Saved browse view description.
     * This is the smallest amount of data required for the menu and blade.
     */
    type BrowseViewDescription = {
        /**
         * The version of the view. This should be the value "1.0".
         */
        readonly version: BrowseViewVersion;
        /**
         * The ID of the view.
         */
        readonly id?: string;
        /**
         * The name of the view (display string).
         */
        readonly name?: string;
    };
    /**
     * Browse view version.
     */
    type BrowseViewVersion = "1.0";
    /**
     * Resource types with special handling in Browse blades
     */
    const enum BuiltInResourceTypes {
        AllResources = "microsoft.resources/resources",
        Subscriptions = "microsoft.resources/subscriptions",
        ResourceGroups = "microsoft.resources/subscriptions/resourcegroups",
        TagResources = "microsoft.resources/resourceswithtag",
        ManagementGroups = "microsoft.management/managementgroups"
    }
    /**
     * Column Data
     */
    type ColumnData = {
        /**
         * The name of the column (the data source column name).
         */
        readonly name: string;
        /**
         * The display name of the column (what is shown on the column header).
         */
        readonly displayName: string;
        /**
         * The data format of the column (used for rendering, sorting, filtering, etc.).
         */
        readonly format: Assets.ColumnFormat;
        /**
         * Optional name of the column to use for sorting (default is to use the name).
         * Property applicable only to ARG-specific data sources and handlers as well as Framework-handled extension manifest columns.
         * Don't use in the components.
         */
        readonly sortColumn?: string;
        /**
         * Optional special override for the column type for sorting (like resourceType, location, etc).
         */
        readonly sortType?: ColumnType;
    };
    /**
     * Columns Data
     */
    type ColumnsData = {
        /**
         * The resource type the columns data was requested for.
         */
        resourceType: string;
        /**
         * Map of resource column ids
         */
        columnIdToResourceColumnMap: Record<string, number>;
        /**
         * Map of manifest columns
         */
        manifestColumnsMap: Record<string, Assets.BrowseColumnManifest>;
        /**
         * Visible columns
         */
        visibleColumns: ResourceColumn[];
        /**
         * Default visible columns
         */
        defaultVisibleColumns: {
            id: string;
            name: string;
            description?: string;
            visible: boolean;
            index: number;
        }[];
        columnDataMap: Record<string, ColumnData>;
    };
    /**
     * Column type for sorting
     */
    type ColumnType = "resourceType" | "location" | "edgeZone" | "subscription" | "tenant";
    /**
     * Enum representation for the count types returned from ARM.
     */
    const enum CountTypeEnum {
        /**
         * No-op.
         */
        None = 0,
        /**
         * The accurate number of total subscriptions.
         */
        Total = 1,
        /**
         * An inaccurate number of total subscriptions.
         */
        Partial = 2,
        /**
         * Indicates an error in getting a count of all subscriptions.
         */
        NotAbleToCalculate = 4
    }
    /**
     * Interface for facet filter.
     */
    type FacetFilter<T> = {
        /**
         * The key (column) for the facet filter.
         */
        readonly key: string;
        /**
         * The display string.
         */
        readonly display?: string;
        /**
         * The facet operator.
         */
        readonly operator: FacetOperator;
        /**
         * The value.
         */
        readonly value?: T;
        /**
         * The flag indicating if all values are selected or not.
         */
        readonly isAllValues?: boolean;
        /**
         * The facet kind.
         */
        readonly facetKind: FacetKind;
        /**
         * The list of tag names.
         */
        readonly tagNames?: string[];
        /**
         * The flag indicating if expansion is used or not.
         */
        readonly useExpansion?: true;
    };
    /**
     * Constants for facet kinds.
     */
    const enum FacetKind {
        /**
         * Do not use.
         */
        DoNotUse = 0,
        /**
         * Column kind.
         */
        Column = 1,
        /**
         * Tag kind.
         */
        Tag = 2
    }
    /**
     * Constants for facet operators.
     */
    const enum FacetOperator {
        /**
         * Do not use.
         */
        DoNotUse = 0,
        /**
         * Equals operator.
         */
        Equals = 1,
        /**
         * Not equals operator.
         */
        NotEquals = 2,
        /**
         * Contains operator.
         */
        Contains = 3,
        /**
         * Not contains operator.
         */
        NotContains = 4,
        /**
         * Starts with operator.
         */
        StartsWith = 5,
        /**
         * Not starts with operator.
         */
        NotStartsWith = 6,
        /**
         * Ends with operator.
         */
        EndsWith = 7,
        /**
         * Not ends with operator.
         */
        NotEndsWith = 8
    }
    /**
     * Interface for facet value.
     */
    type FacetValue = {
        /**
         * The name of the facet.
         */
        readonly name: string;
        /**
         * The display name.
         */
        readonly displayName?: string;
    };
    /**
     * Filter item which includes the key, the data and the text as well as the column format.
     */
    type FilterItem = FilterValue & {
        /**
         * The column format for the value.
         */
        readonly format: Assets.ColumnFormat;
        /**
         * Flag indicating if the operator should be hidden.
         */
        readonly hideOperator?: boolean;
    };
    /**
     * Filter value which include the key, the data and the text.
     */
    type FilterValue = {
        /**
         * The key (lowercase data).
         */
        readonly key: string;
        /**
         * The display string for the item.
         */
        readonly text: string;
        /**
         * The value for the item.
         */
        readonly value: string;
    };
    /**
     * Represents a panel in the zero subs experience in React View.
     */
    type Panel = {
        /**
         * Image for the panel.
         */
        readonly image: Images.Image;
        /**
         * Title
         */
        readonly title: string;
        /**
         * Description
         */
        readonly description: string;
        /**
         * Button
         */
        readonly button: {
            /**
             * Button label
             */
            readonly label: string;
            /**
             * Button link
             */
            readonly link: string;
        };
        /**
         * Free service link
         */
        readonly freeServiceLink?: string;
        /**
         * Learn more link
         */
        readonly learnMoreLink?: string;
    };
    /**
     * Tenant information for redirect.
     */
    type RedirectTenant = {
        /**
         * The tenant ID.
         */
        readonly tenantId: string;
        /**
         * The tenant domain name.
         */
        readonly domainName: string;
    };
    type ZeroSubsTenantAndSubResult = {
        /**
         * Whether to show the refresh page for zero subs.
         */
        readonly showRefreshPage: boolean;
        /**
         * Whether to show the tenant switch view for zero subs.
         */
        readonly showTenantSwitchView: boolean;
        /**
         * The tenant to switch to in the switch tenant view.
         */
        readonly tenantSwitchingTo: RedirectTenant;
    };
    /**
     * The resource columns type.
     */
    type ResourceColumn = {
        /**
         * The column ID.
         */
        id: string;
        /**
         * The column name.
         */
        name: string;
        /**
         * Description of the column.
         */
        description?: string;
        /**
         * The column visibility.
         */
        visible: boolean;
        /**
         * The column index.
         */
        index?: number;
        /**
         * The column item key.
         */
        itemKey?: string;
    };
    /**
     * Resource routing type
     */
    const enum RoutingType {
        Default = 0,
        Tenant = 1,
        Extension = 2,
        ProviderProxy = 3
    }
    /**
     * The sort order.
     */
    const enum SortOrder {
        /**
         * Do not use.
         */
        Unused = 0,
        /**
         * Sort in ascending order.
         */
        Ascending = 1,
        /**
         * Sort in descending order.
         */
        Descending = 2
    }
    /**
     * Interface for a sorted column.
     */
    type SortedColumn = {
        /**
         * The column ID to sort by.
         */
        readonly sortBy: string;
        /**
         * The sort order.
         */
        readonly order: SortOrder;
        /**
         * The sort format.
         */
        readonly sortFormat: SortFormat;
    };
    /**
     * Sort format
     */
    type SortFormat = "string" | "number" | "date";
    /**
     * Saved View constants
     */
    const enum ViewStringConstants {
        DefaultSavedViewId = "_default_"
    }
    /**
     * The getting started parameters for the browse blade.
     */
    type GettingStarted = {
        /**
         * Optional item ID for in-menu browse where getting started is a menu switch.
         */
        readonly itemId?: string;
        /**
         * Optional blade reference for browse where getting started is a blade reference.
         */
        readonly blade?: {
            /**
             * The name of the extension that contains the blade.
             */
            readonly extensionName: string;
            /**
             * The name of the blade.
             */
            readonly bladeName: string;
            /**
             * A map of parameters to be passed to the blade.
             */
            readonly bladeParameters?: StringMap<any>;
        };
    };
}
export declare namespace BrowseV1 {
    /**
     * Browse V1 column type
     */
    export type ColumnV1 = {
        /**
         * The column ID.
         */
        readonly id: string;
        /**
         * The column name.
         */
        readonly name: string;
        /**
         * The column visibility.
         */
        readonly visible: boolean;
        /**
         * Description of the column that is shown in the info balloon of the column chooser blade
         */
        readonly description?: string;
        /**
         * The format for the column.
         */
        readonly format?: FormatV1;
        /**
         * The format options for the column.
         */
        readonly formatOptions?: FormatOptions;
        /**
         * The column item key.
         */
        readonly itemKey?: string;
    };
    /**
     * Browse V1 column format
     */
    export enum FormatV1 {
        /**
         * HTML format, raw HTML is allowed.
         */
        Html = 1,
        /**
         * Text format, HTML gets encoded.
         * The value can also be simple text or a KnockoutObservable object.
         */
        Text = 2,
        /**
         * Short time format, outputs something similar to 11:20 AM.
         * The value can be a number (milliseconds since 1970), text understood by new Date(text) or a Date.
         */
        ShortTime = 100,
        /**
         * Long time format, outputs something similar to 11:20:19 AM.
         * The value can be a number (milliseconds since 1970), text understood by new Date(text) or a Date.
         */
        LongTime = 101,
        /**
         * Short date format, outputs something similar to 7/18/2013.
         * The value can be a number (milliseconds since 1970), text understood by new Date(text) or a Date.
         */
        ShortDate = 102,
        /**
         * Long date format, outputs something similar to Thursday, July 18, 2013.
         * The value can be a number (milliseconds since 1970), text understood by new Date(text) or a Date.
         */
        LongDate = 103,
        /**
         * Month and day format, outputs something similar to July 18.
         * The value can be a number (milliseconds since 1970), text understood by new Date(text) or a Date.
         */
        MonthDay = 104,
        /**
         * Year and month format, outputs something similar to July, 2013.
         * The value can be a number (milliseconds since 1970), text understood by new Date(text) or a Date.
         */
        YearMonth = 105,
        /**
         * Custom date format, outputs the format based on formatOptions.dateFormat
         * The value can be a number (milliseconds since 1970), text understood by new Date(text) or a Date.
         */
        CustomDate = 106,
        /**
         * URI format, outputs a clickable URI.
         * The value can be an URI, or this format: { uri: string; text: string; target?: string } .
         */
        Uri = 107,
        /**
         * Icon format, outputs an icon with optionally text beside it.
         * The value can be an icon URI, or this format: { uri: string; text?: string; } .
         */
        Icon = 108,
        /**
         * Icon Lookup format, outputs an icon based on a dictionary you provide in formatOptions.iconLookup.
         * The value is the key that matches the iconLookup dictionary.
         * The iconLookup dictionary can be { key: iconUri, ... } or { key: { uri: string, text?: string }, ... } .
         * Optionally, you can have a key called "##DEFAULT##" to display any value not mapped by your dictionary.
         */
        IconLookup = 109,
        /**
         * Text Lookup format, outputs text based on a dictionary you provide in formatOptions.textLookup.
         * The value is the key that matches the textLookup dictionary.
         * The textLookup dictionary is { key: text, ... } .
         */
        TextLookup = 110,
        /**
         * Svg icon format, outputs an svg icon with optional text beside it.
         * The value can be an SVG, or this format: { svg: MsPortalFx.Base.Image; text?: string; } .
         */
        SvgIcon = 111,
        /**
         * Svg icon lookup format, outputs an icon based on a dictionary you provide in formatOptions.svgIconLookup.
         * The value is the key that matches the svgIconLookup dictionary.
         * The svgIconLookup dictionary can be { key: MsPortalFx.Base.Image, ... } or { key: { svg: MsPortalFx.Base.Image, text?: string }, ... } .
         * Optionally, you can have a key called "##DEFAULT##" to display any value not mapped by your dictionary.
         */
        SvgIconLookup = 112,
        /**
         * Visually represents a percentage (values between 0-100) with a bar.
         */
        PercentageBar = 113,
        /**
         * Displays globalized numbers
         */
        Number = 114
    }
    type FormatOptions = {
        /**
         * Dictionary with key matching the value of the itemKey.
         * The value can be either the iconUri or an object as such { uri: string, text?: string } .
         */
        iconLookup?: any;
        /**
         * Dictionary with key matching the value of the itemKey.
         * The value is the text mapped to the value.
         */
        textLookup?: any;
        /**
         * Dictionary with key matching the value of the itemKey.
         * The value can be either the MsPortalFx.Base.Image or an object as such { uri: MsPortalFx.Base.Image, text?: string } .
         * The svgIconLookup dictionary can be { key: MsPortalFx.Base.Image, ... } or { key: { svg: MsPortalFx.Base.Image, text?: string }, ... } .
         * Optionally, you can have a key called "##DEFAULT##" to display any value not mapped by your dictionary.
         */
        svgIconLookup?: any;
    };
    export {};
}
export declare namespace ResourceMenu {
    /**
     * The options of the resource menu config.
     */
    interface ResourceMenuOptions {
        /**
         * Disables the automation tasks option from appearing in the automation group.
         */
        disableAutomationTasks?: boolean;
        /**
         * Disables the resource explorer option from appearing in the automation group.
         */
        disableResourceExplorer?: boolean;
        /**
         * Enables the setting exporting a template from resources and resource groups.
         */
        enableExportTemplate?: boolean;
        /**
         * Enables the settings for roles and users.
         */
        enableRbac?: boolean;
        /**
         * Enables the settings for help request support.
         */
        enableSupportHelpRequest?: boolean;
        /**
         * Enables the settings for troubleshoot support.
         */
        enableSupportTroubleshootV2?: boolean;
        /**
         * Enables the settings for resource health support.
         */
        enableSupportResourceHealth?: boolean;
        /**
         * Enables the settings for the event logs.
         */
        enableSupportEventLogs?: boolean;
        /**
         * Enables the setting for tags.
         */
        enableTags?: boolean;
        /**
         * Enables the setting for resource visualizer.
         */
        enableResourceVisualizer?: boolean;
        /**
         * Enables the setting for standard properties blade.
         */
        enableProperties?: boolean;
        /**
         * Enables the settings for alerts.
         */
        enableAlerts?: boolean;
        /**
         * Enables the settings for diagnostics.
         */
        enableDiagnostics?: boolean;
        /**
         * Enables the settings for metrics.
         */
        enableMetrics?: boolean;
        /**
         * Enables the settings for log analytics.
         */
        enableLogAnalytics?: boolean;
        /**
         * Enables the settings for log search. Disabled if enable logs is enabled.
         */
        enableLogSearch?: boolean;
        /**
         * Enables the settings for locks.
         */
        enableLocks?: boolean;
        /**
         * Enables the settings for resource advisor support.
         */
        enableSupportResourceAdvisor?: boolean;
        /**
         * Enables the settings for Event Grid Publisher support.
         */
        enableEventGridPublisher?: boolean;
        /**
         * Enables workbooks blade in the resource ToC
         */
        enableWorkbooks?: boolean;
        /**
         * Enables analytics blade for logs in the resource ToC
         */
        enableLogs?: boolean;
        /**
         * Enables insights blade in the resource ToC
         * TODO, 12938313, Mark this member deprecated during the next breaking change window.
         * Please see https://aka.ms/azmonwog to enable insights
         */
        enableInsights?: boolean;
        /**
         * Enables view for CLI/Powershell blade in the resource ToC
         */
        enableAutomationApis?: boolean;
    }
}
export declare namespace ArgInterfacesCore {
    /**
     * The blade details for the resource type or its kind.
     */
    type ResourceTypeOrKindBladeDetails = {
        /**
         * The name of the blade.
         */
        readonly bladeName: string;
        /**
         * The extension to load the blade from.
         */
        readonly extensionName: string;
    };
    /**
     * The part details for the resource type or its kind.
     */
    type ResourceTypeOrKindPartDetails = {
        /**
         * The name of the part.
         */
        readonly partName: string;
        /**
         * The extension for the part.
         */
        readonly extensionName: string;
    };
    /**
     * Details about the resource type or its kind.
     */
    type ResourceTypeOrKindDetails = {
        /**
         * The description for the asset type/kind.
         */
        readonly description: string;
        /**
         * The display name.
         */
        readonly displayName: Readonly<Assets.CompositeDisplayName>;
        /**
         * The icon.
         */
        readonly icon: Images.Image;
        /**
         * The options for the asset type or the kind.
         */
        readonly options: AssetTypes.AssetTypeOptions;
        /**
         * The blade details for each item. This is not set if the resource type
         * is set to use the Hubs resource menu blade.
         */
        readonly bladeDetails?: Readonly<ResourceTypeOrKindBladeDetails>;
        /**
         * The part details for each item. If this is not set
         * the default properties part from HubsExtension is used.
         */
        readonly partDetails?: Readonly<ResourceTypeOrKindPartDetails>;
        /**
         * The flag to hide (or show) the browse info box (overrides PDL BrowseInfoBox).
         */
        readonly hideBrowseInfoBox?: boolean;
        /**
         * Flag to indicate if the type or kind is marked as preview.
         */
        readonly isPreview?: boolean;
        /**
         * Flag to indicate if the type or kind is marked as disabled by policy.
         */
        readonly isDisabled?: boolean;
    };
    /**
     * Details about the resource type kind.
     */
    type ResourceKindDetails = ResourceTypeOrKindDetails & {
        /**
         * The array of kinds which is represented by this kind.
         */
        readonly kinds?: readonly string[];
    };
    /**
     * The details of a resource type which includes its kinds.
     */
    type ResourceTypeDetails = ResourceTypeOrKindDetails & {
        /**
         * The asset id in the form extension_assettype.
         */
        readonly assetId: string;
        /**
         * The ARG browse option that the extension has chosen for this resource/asset type.
         */
        readonly browseOption?: AssetTypes.ArgBrowseOptions;
        /**
         * The ARM browse option that the extension has chosen for this resource/asset type.
         */
        readonly armBrowseOption?: AssetTypes.ArmBrowseOptions;
        /**
         * A map representing the various kinds supported by this resource type.
         */
        readonly kindMap?: ReadonlyStringMap<ResourceKindDetails>;
        /**
         * The default kind, if there is one.
         */
        readonly defaultKind?: Readonly<ResourceKindDetails>;
        /**
         * The contracts for the asset type.
         */
        readonly contracts: Assets.AssetTypeContracts;
        /**
         * The browse type for the asset type.
         */
        readonly browseType: AssetTypes.BrowseType;
        /**
         * The create blade associated with the asset
         */
        readonly noPdlCreateBlade?: string;
        /**
         * The extension with the create blade associated with the asset
         */
        readonly noPdlCreateExtension?: string;
        /**
         * The create blade parameters associated with the asset
         */
        readonly noPdlCreateParameters?: any;
        /**
         * The marketplace ids that are being redirected into this noPdlCreateBlade
         */
        readonly claimedMarketplaceIds?: string[];
        /**
         * The deployment blade associated with the asset
         */
        readonly postCreateBlade?: Assets.PostCreateBladeReference;
        /**
         * The marketplace item id associated with the asset.
         */
        readonly marketplaceItemId?: string;
        /**
         * The marketplace menu item id associated with the asset.
         */
        readonly marketplaceMenuItemId?: string;
        /**
         * The documentation links for asset type.
         */
        readonly links?: readonly Assets.Link[];
        /**
         * The hidden commands list for asset type.
         */
        readonly hiddenCommands?: readonly string[];
        /**
         * The browse command layout experiments.
         */
        readonly browseCommandExperiments?: ReadonlyStringMap<Assets.AssetTypeBrowseCommandLayout>;
        /**
         * The routing type for the resource type.
         */
        readonly routingType?: number;
        /**
         * The API resource type for ARM.
         * This is only valid for end-point-routing resources.
         */
        readonly topLevelResourceTypeAlias?: string;
        /**
         * The ARM API version to use for this resource type.
         */
        readonly apiVersion?: string;
        /**
         * The optional array of proxy routing filters for this resource type.
         */
        readonly proxyRoutingFilters?: Assets.ProxyRoutingFilter[];
        /**
         * The API resource type for ARM.
         * This is only valid for tenant-routing resources.
         */
        readonly topLevelTenantAlias?: string;
        /**
         * The optional map of command ids to api-versions per resource type for a given asset type.
         * This is used to override default api-versions in extensible ARM bulk command definitions.
         */
        readonly extensibleCommandsApiVersions?: ReadonlyStringMap<ReadonlyStringMap<string>>;
    };
}
export declare namespace ArgInterfaces {
    /**
     * A data point in a response from ART.
     */
    type ARTDataPoint = string | number | object;
    /**
     * A data row in a response from ART.
     */
    type ARTDataRow = readonly ARTDataPoint[];
    /**
     * The column response from ART.
     */
    interface ARTColumnResponse {
        /**
         * The name of the column.
         */
        readonly name: string;
        /**
         * The type of the column.
         */
        readonly type: string;
    }
    /**
     * The data response from ART.
     */
    interface ARTDataResponse {
        /**
         * ART data rows.
         */
        readonly rows: readonly ARTDataRow[];
        /**
         * The list of columns.
         */
        readonly columns: readonly ARTColumnResponse[];
    }
    /**
     * The facet response from ART.
     */
    interface ARTFacetResponse {
        /**
         * Flag suggesting if the facet expression specified in the query is valid or not.
         */
        readonly valid: boolean;
        /**
         * The facet expression.
         */
        readonly expression: string;
        /**
         * Data including rows and columns.
         */
        readonly data: ARTDataResponse;
        /**
         * The count of facets returned.
         */
        readonly count: number;
        /**
         * The total number of records (if the facets are filtered, this is the total number of filtered facets,
         * otherwise the total number).
         */
        readonly totalRecords: number;
    }
    /**
     * The ART response.
     */
    interface ARTResponse {
        /**
         * Data including rows.
         */
        readonly data: ARTDataResponse;
        /**
         * Boolean indicating if paging is enabled.
         */
        readonly pagingEnabled?: boolean;
        /**
         * ART data facets.
         */
        readonly facets?: ARTFacetResponse[];
        /**
         * Total records matching the specified query.
         */
        readonly totalRecords?: number;
        /**
         * The number of records returned in the current query.
         */
        readonly count?: number;
        /**
         * The offset of the items in the current page.
         */
        readonly offset?: number;
        /**
         * The skip token if paging is enabled.
         */
        readonly $skipToken?: string;
        /**
         * The correlation ID for the call to the backend.
         */
        readonly correlationId?: string;
        /**
         * A flag indicating that the results returned in the `data` property are ordered indeterministically and paginated results are not guaranteed to be accurate.
         */
        readonly resultTruncated?: "true" | "false";
        /**
         * The http status code of the response from the backend.
         */
        readonly httpStatusCode?: Ajax.HttpStatusCode;
    }
}
export declare namespace Experimentation {
    interface SetExtensionFlightsOptions {
        readonly flights: readonly string[];
        readonly assignmentContext: string;
        readonly variables: Record<string, string | number | boolean>;
        [key: string]: any;
    }
    interface ExperimentationProvider {
        _data: SetExtensionFlightsOptions;
    }
    interface Experimentation {
        getAssignments(): Promise<ExperimentationProvider>;
    }
}
export declare namespace Authentication {
    /**
     * The parameters callers may provide that will be passed to the authentication provider.
     */
    type AllowedSignInParameters = "amr_values" | "acr_values" | "claims";
    /**
     * The options for forcing the user to sign in again.
     */
    interface ForceSignInOptions {
        /**
         * The reason for forcing the user to sign in.
         */
        readonly reason: string;
        /**
         * The blade to launch upon successful sign in.
         */
        readonly bladeReference?: SimpleBladeReference;
        /**
         * Optional map of query parameters and values to pass to the authentication provider.
         */
        readonly signInParameters?: Partial<Record<AllowedSignInParameters, string>>;
        /**
         * The tenant ID or directory name to sign into.
         */
        readonly tenantId?: string;
    }
    /**
     * Interface for authentication tokens received by the extension.
     */
    type AuthorizationToken = {
        /**
         * The authorization header needed to make API calls to a service.
         */
        readonly header: string;
        /**
         * The time at which the token expires.
         */
        readonly expiresAt: number;
    };
    /**
     * Interface for authentication tokens received by the extension.
     */
    type AuthorizationTokenForCloudshell = AuthorizationToken & {
        /**
         * The token type as determined by AAD.
         */
        readonly tokenType?: string;
        /**
         * The error message returned by AAD.
         */
        readonly errorMessage?: string;
    };
    /**
     * Interface for requesting authentication tokens for a claims challenge.
     */
    type GetAuthorizationTokenForChallengeArgs = {
        /**
         * The authorization header from the request that failed with the claims challenge. This should be the exact string with the 'Bearer' prefix.
         */
        readonly currentAuthorizationHeader: string;
        /**
         * The content of the WWW-Authenticate header returned on unauthorized responses due to continuous access evaluation policy revocations.
         * This includes the base64-encoded, serialized set of claims needed in the resulting token.
         */
        readonly wwwAuthHeader: string;
    };
    /**
     * Options supplied to 'getAuthorizationToken'.
     */
    type GetCloudshellTokenArgs = ({
        /**
         * Type of telemetry data to display.
         */
        readonly resourceName: string;
        /**
         * Telemetry data to display.
         */
        readonly audience?: never;
    } | {
        /**
         * Type of telemetry data to display.
         */
        readonly resourceName?: never;
        /**
         * Telemetry data to display.
         */
        readonly audience: string;
    }) & {
        /**
         * The JWK and KID values required for fetching SSH tokens from AAD.
         */
        readonly sshTokenData?: {
            /**
             * The JSON web key is a JSON representation of a public key.
             */
            readonly jwk: string;
            /**
             * The key id, used to identify a key.
             */
            readonly kid: string;
        };
    };
    /**
     * Options supplied to 'getPopToken'.
     */
    type GetPopTokenOptions = {
        /**
         * The scopes required on the PoP token.
         */
        readonly scopes: string[];
        /**
         * The all-caps name of the HTTP method of the request that will use the signed token (GET, POST, PUT, etc.)
         */
        readonly resourceRequestMethod: string;
        /**
         * The URL of the protected resource for which the access token is being issued
         */
        readonly resourceRequestUri: string;
        /**
         * A stringified JSON object containing custom client claims to be added to the PoP token.
         */
        readonly shrClaims?: string;
        /**
         * A server-generated, signed timestamp that is Base64URL encoded as a string. This nonce is used to
         * mitigate clock-skew and time-travel attacks meant to enable PoP token pre-generation.
         */
        readonly shrNonce?: string;
    };
    /**
     * Options supplied to 'GetSshCertToken'.
     */
    type GetSshCertTokenOptions = {
        /**
         * The AAD resource URL or GUID.
         */
        readonly audience: string;
        /**
         * The JSON web key is a JSON representation of a public key.
         */
        readonly jwk: string;
        /**
         * The key id, used to identify a key.
         */
        readonly kid: string;
    };
}
/**
 * Internal implementation use.
 * Note: When you add a key to this type, please update 'LighthouseMetricsWatcher' if the associated operation is async and should be treated like a network request re: Lighthouse.
 */
export interface ProxiedFx {
    ___ajax___getEndpoints: {
        returns: Ajax.Endpoints;
    };
    ___ajax___batch: {
        parameters: Ajax.BatchSettings;
        /**
         * 'responseHeaders' is of a class type and isn't serializable. It will be recreated by the caller.
         */
        returns: Omit<Ajax.BatchResponseItem<any>, "responseHeaders">;
    };
    ___ajax___batchMultiple: {
        parameters: Ajax.BatchMultipleSettings;
        /**
         * 'responseHeaders' is of a class type and isn't serializable. It will be recreated by the caller.
         */
        returns: Omit<Ajax.BatchResponse, "responses"> & {
            responses: readonly Omit<Ajax.BatchResponseItem<any>, "responseHeaders">[];
        };
    };
    ___ajax___ajax: {
        parameters: any;
        returns: any;
    };
    ___auth___getAuthorizationTokenForChallenge: {
        parameters: Authentication.GetAuthorizationTokenForChallengeArgs;
        returns: Authentication.AuthorizationToken;
    };
    ___auth___getAuthorizationTokenForCloudshell: {
        parameters: Authentication.GetCloudshellTokenArgs;
        returns: Authentication.AuthorizationTokenForCloudshell;
    };
    ___resources___getContentUri: {
        parameters: string;
        returns: string;
    };
    ___resources___getAbsoluteUri: {
        parameters: string;
        returns: string;
    };
    ___policyDataCore___validatePolicy: {
        parameters: PolicyDataCoreModels.Internal.PolicyDataCoreOptionsType;
        returns: PolicyDataCoreModels.Internal.PolicyDataCoreResultType;
    };
    ___ajax___trace: {
        parameters: {
            request: {
                uri: string;
                type?: string;
                headers?: ReadonlyStringMap<any>;
                dataSentBytes: number;
                data?: string;
            };
            response: {
                status?: number;
                dataReceivedBytes?: number;
                isError?: boolean;
                responseCorrelationId?: string;
                responseText?: string;
            };
            startTime: number;
            endTime: number;
            bladeContext: {
                id: string;
                instanceId: string;
            };
        };
    };
    ___ajax___isTrustedDomain: {
        parameters: string;
        returns: boolean;
    };
    ___getClientIP___getClientIPv4: {
        returns: string;
    };
    ___getClientIP___getClientIPv6: {
        parameters: Ajax.GetClientIPOptions;
        returns: string;
    };
    ___feedbackPolicies___fetchFeedbackPolicySettings: {
        returns: FeedbackControls.AdminFeedbackPolicySettings;
    };
    ___notifications___getAuthDetails: {
        parameters: string;
        returns: Notifications.PollingAuthDetails;
    };
    ___diagnostics___trace: {
        parameters: {
            events: readonly {
                timestamp: number;
                source: string;
                action: string;
                actionModifier?: string;
                duration?: number;
                name?: string;
                data?: any;
            }[];
            telemetryContext: Partial<Record<"Blade" | "Part", {
                id: string;
                instanceId: string;
            }>>;
        };
    };
    ___diagnostics___log: {
        parameters: {
            entries: readonly {
                timestamp: number;
                level: number;
                area: string;
                entryType?: string;
                message: string;
                code?: number;
                args?: readonly any[];
            }[];
        };
    };
    ___ajax___isTrustedDomainOverride: {
        parameters: string;
        returns: boolean;
    };
    ___metrics___record: {
        parameters: {
            timestamp: number;
            extension: string;
            name: string;
            value: number;
            dimensions?: Record<string, string>;
            lowPriority?: boolean;
        };
    };
}
/**
 * The type of Command Line Interface for CloudShell.
 */
export type CliType = "bash" | "powershell";
/**
 * Options to configure CloudShell
 *
 */
export interface CliOptions {
    /**
     * Set true to use the CloudShell without a Storage Account and without saving any information.
     */
    readonly ephemeral: boolean;
    /**
     * Subscription ID in the context of which the command is executed.
     */
    readonly subscriptionID: string;
}
/**
 * Type that contains Command Line Interface (CLI) options along with CLI type.
 */
export interface CliTypeWithOptions {
    /**
     * The type of Command Line Interface for CloudShell
     */
    readonly cliType: CliType;
    /**
     * Options to configure CloudShell
     */
    readonly cliOptions?: CliOptions;
}
/**
 * Argument for CloudShell commands
 */
export interface CloudShellCommandArg {
    /**
     * Name of the argument
     */
    prop: string;
    /**
     * Value of the argument
     */
    value?: string;
}
/**
 * Command that will be run on CloudShell
 */
export interface CloudShellCommand {
    /**
     * Name of the command
     */
    name: string;
    /**
     * Argument or argument list for the command
     */
    args?: CloudShellCommandArg | CloudShellCommandArg[];
}
/**
 * Options for validating required permissions for an ARM resource.
 */
export interface ArmRequiredPermissions {
    /**
     * The list of required actions/permissions.
     */
    actions: string[];
    /**
     * The message to show in case validation fails.
     */
    message?: string;
}
export declare namespace TelemetryInsights {
    /**
     * Base result structure of a Kusto query for Telemetry Insights.
     */
    interface InsightBase {
        /**
         * Blade name.
         */
        readonly Blade: string;
        /**
         * Name of telemetry item.
         */
        readonly Name: string;
        /**
         * Number of users.
         */
        readonly Users: number;
        /**
         * Number of sessions.
         */
        readonly Sessions: number;
        /**
         * Number of tenants.
         */
        readonly Tenants: number;
        /**
         * Percent of total users.
         */
        readonly PctOfTotalUsers: number;
        /**
         * Percent of total sessions.
         */
        readonly PctOfTotalSessions: number;
        /**
         * Percent of total tenants.
         */
        readonly PctOfTotalTenants: number;
        /**
         * Percentage class to determine heatmap color.
         */
        readonly percentageClass: string;
    }
    /**
     * Result of the CommandSummary Kusto Query
     */
    interface CommandBarInsight extends InsightBase {
        /**
         * Button clicks.
         */
        readonly Clicks: number;
        /**
         * Percent of total button clicks.
         */
        readonly PctOfTotalClicks: number;
        /**
         * Total clicks.
         */
        readonly TotalClicks: number;
    }
    /**
     * Result of the MenuSummary Kusto Query
     */
    interface MenuInsight extends InsightBase {
        /**
         * Resource type.
         */
        readonly Type: string;
        /**
         * Menu item loads.
         */
        readonly Loads: number;
        /**
         * Total loads.
         */
        readonly TotalLoads: number;
        /**
         * Percent of total menu item loads.
         */
        readonly PctOfTotalLoads: number;
    }
    /**
     * Result of the Tabs Kusto Query
     */
    interface TabInsight {
        /**
         * Timestamp.
         */
        readonly TimeStamp: Date;
        /**
         * Source e.g. the full blade name.
         */
        readonly Source: string;
        /**
         * Tab index.
         */
        readonly TabIndex: number;
        /**
         * Tab name.
         */
        readonly Name: string;
        /**
         * Tab loads.
         */
        readonly Loads: number;
        /**
         * Percentage of total loads.
         */
        readonly PctOfTotalLoads: number;
        /**
         * Percentage class to determine heatmap color.
         */
        readonly percentageClass: string;
    }
    /**
     * Result of the InBlade Kusto Query
     */
    interface InBladeInsight {
        /**
         * Element selector.
         */
        readonly selector: string;
        /**
         * Telemetry target name.
         */
        readonly name: string;
        /**
         * Number of clicks.
         */
        readonly Clicks: number;
        /**
         * Percent of total clicks.
         */
        readonly PctClicks: number;
        /**
         * Total number of clicks.
         */
        readonly TotalClicks: number;
        /**
         * Percentage class to determine heatmap color.
         */
        readonly percentageClass: string;
    }
    /**
     * result of the Comment Kusto Query
     */
    interface CommentInsight {
        /**
         * Timestamp.
         */
        readonly TimeStamp: Date;
        /**
         * Extension name
         */
        readonly Extension: string;
        /**
         * Blade name
         */
        readonly Blade: string;
        /**
         * CES value.
         */
        readonly CESValue: number;
        /**
         * CVA value.
         */
        readonly CVAValue: number;
        /**
         * Comments.
         */
        readonly Comments: string;
        /**
         * Feature name.
         */
        readonly FeatureName: string;
    }
    /**
     * Type of telemetry data to be sent to Matrix.
     */
    const enum InsightsTelemetryType {
        CMD = "commandbar",
        TAB = "tab",
        BLADE = "blade"
    }
    /**
     * Telemetry data and type to be sent to Matrix.
     */
    type InsightsTelemetry = ({
        /**
         * Type of telemetry data to display.
         */
        readonly type: InsightsTelemetryType.CMD;
        /**
         * Telemetry data to display.
         */
        readonly data: CommandBarInsight[];
    } | {
        /**
         * Type of telemetry data to display.
         */
        readonly type: InsightsTelemetryType.TAB;
        /**
         * Telemetry data to display.
         */
        readonly data: TabInsight[];
    } | {
        /**
         * Type of telemetry data to display.
         */
        readonly type: InsightsTelemetryType.BLADE;
        /**
         * Telemetry data to display.
         */
        readonly data: InBladeInsight[];
    }) & {
        /**
         * Kusto query for the telemetry data.
         */
        readonly query: string;
    };
}
export declare namespace Internal {
    /**
     * Internal types for TourGuide (not for consumption by extension authors)
     */
    namespace TourGuide {
        /**
         * Interface for an extension's tour data
         */
        interface ExtensionTourData {
            /**
             * When the extension tour data was clean.
             */
            lastCleanDate: string;
            /**
             * An extension's tours.
             */
            tours: ExtensionTours;
        }
        /**
         * Key-value pair for a tour and its stored data.
         */
        type ExtensionTours = Record<string, TourStoreData>;
        /**
         * Interface for TourGuide store data.
         */
        interface TourStoreData {
            /**
             * Boolean value for whether tour has been acknowledged.
             */
            readonly acknowledged: boolean;
            /**
             * Number of times tour has been dismissed.
             */
            readonly dismissCount: number;
            /**
             * When tour will expire.
             */
            readonly expirationDate: string;
        }
        /**
         * Interface for Tour data.
         */
        interface Tour {
            /**
             * The tour id.
             */
            readonly tourId: string;
            /**
             * The blade name.
             */
            readonly bladeName?: string;
            /**
             * The data for tour's teaching bubbles.
             */
            readonly content: TeachingBubbleOptions[];
            /**
             * The tour expiration date.
             */
            readonly expirationDate: string;
            /**
             * The experiment name.
             */
            readonly experiment?: string;
            /**
             * The user stored data for specific tours.
             */
            readonly storeData?: Map<string, ExtensionTourData>;
        }
        /**
         * Parameters for showTours
         */
        interface ShowToursOptions {
            /**
             * Tours to display.
             */
            tours: TourGuide.Tour[];
            /**
             * Count of activated tours.
             */
            activatedToursCount: number;
            /**
             * Store data for TourGuide
             */
            storeData: Map<string, TourGuide.ExtensionTourData>;
        }
        /**
         * Interface for TourGuide TeachingBubbles
         */
        interface TeachingBubbleOptions {
            /**
             * Title shown on the teaching bubble
             */
            readonly title: string;
            /**
             * Description shown on the teaching bubble
             */
            readonly description: string;
            /**
             * Target element or CSS selector for the element that the teaching bubble displays beside
             */
            readonly targetElement: HTMLElement | string;
            /**
             * Direction of the teaching bubble triangle points to
             */
            readonly directionalHint?: "up" | "left" | "right" | "down";
            /**
             * Used to adjust vertical position of the teaching bubble
             */
            readonly verticalOffset?: number;
            /**
             * Used to adjust horizontal position of the teaching bubble
             */
            readonly horizontalOffset?: number;
            /**
             * The link to learn more about the feature
             */
            readonly learnMoreLink?: string;
            /**
             * The text to show on the action button. If not provided, the default text will be used.
             */
            readonly actionButtonText?: string;
            /**
             * The width of the teaching bubble
             */
            readonly width?: number;
            /**
             * The icon to show for the title
             */
            readonly titleIcon?: Images.Image;
            /**
             * Show dismiss button instead of close icon
             */
            readonly showDismissButton?: boolean;
            /**
             * Show feedback button
             */
            readonly showFeedbackButton?: boolean;
            /**
             * Callback when next button is clicked on the teaching bubble
             */
            readonly onNextClickCallback?: () => void;
            /**
             * Extension name for tour guide.
             */
            readonly extension?: string;
            /**
             * Blade name for tour guide.
             */
            readonly bladeName?: string;
        }
    }
}
/**
 * @deprecated Please use "@microsoft/azureportal-copilot-ko" and "@microsoft/azureportal-copilot" packages instead
 */
export declare namespace Copilot {
    /**
     * List of supported Copilot interactions.
     */
    const enum CopilotInteractions {
        ShowMessage = "ShowMessage",
        ShowMessageAndAwait = "ShowMessageAndAwait",
        ShowProgress = "ShowProgress",
        ShowResources = "ShowResources",
        GetContext = "GetContext",
        InferResourceIds = "InferResourceIds",
        InvokeARM = "InvokeARM",
        SelectResources = "SelectResources",
        GetBladeDeepLink = "GetBladeDeepLink"
    }
    type GetBladeDeepLinkOptions = {
        readonly bladeReference: SimpleBladeReference;
    };
    /**
     * Combined type of all Copilot interaction options.
     */
    type CopilotInteractionOptions = CopilotInteractionOption<CopilotInteractions.ShowMessage, ShowMessageOptions> | CopilotInteractionOption<CopilotInteractions.ShowMessageAndAwait, ShowMessageWithActionsOptions> | CopilotInteractionOption<CopilotInteractions.ShowProgress, string> | CopilotInteractionOption<CopilotInteractions.ShowResources, ShowResourcesOptions> | CopilotInteractionOption<CopilotInteractions.GetContext> | CopilotInteractionOption<CopilotInteractions.InferResourceIds, InferResourceIdsOptions> | CopilotInteractionOption<CopilotInteractions.InvokeARM, InvokeARMOptions> | CopilotInteractionOption<CopilotInteractions.SelectResources, SelectResourcesOptions> | CopilotInteractionOption<CopilotInteractions.GetBladeDeepLink, GetBladeDeepLinkOptions>;
    /**
     * The generic type to defined copilot interactions for different runtime (KO, ReactView, etc)
     */
    type CopilotInteractionsType = {
        /**
         * Show a message in the copilot pane
         *
         * @param options One of the:
         *   - string
         *   - Adaptive card to format (https://adaptivecards.io/explorer/)
         * @returns A Promise that resolves once the message has been shown
         */
        showMessage(options: ShowMessageOptions): Promise<void>;
        /**
         * Show a message in the copilot pane and await a response from user
         *
         * @param options One of the:
         *   - Simplified confirmatin options
         *      1. Either predefined:
         *          `{ type: "OkCancel" | "YesNo" }`
         *      2. Or custom:
         *          `{ type: "Custom", actions: { id: string; title: string }[] }`
         *   - Adaptive card to format (https://adaptivecards.io/explorer/)
         * @returns A Promise, once user clicked on action it resolves to:
         *   - Object that contains id of the selected action as result and optional data
         *     `{ result: string; data?: unknown; }`
         *   - or string id of the selected action
         */
        showMessageAndAwait(options: ShowMessageWithActionsOptions): Promise<string | ConfirmationResult>;
        /**
         * Show a progress indication, either
         *   - Create a new progress card with message, if the last card is not the progress one
         *   - or Append message to the existing progreess card
         *
         * @param text string
         * @returns A Promise that resolves once the message has been shown
         */
        showProgress(text: string): Promise<void>;
        /**
         * Show list of resources
         *
         * @param options Options to show resources, contains list of resource ids and optional configuration to show details
         * @returns
         *   - In case wait for selection flag is set or optional card contains Submit actions
         *      A promise, once user clicks on action it resolves to an object that contains id of the selected action as result and list of resource ids with selection
         *   - Otherwise a Promise that resolves once the message has been shown
         */
        showResources(options: ShowResourcesOptions): Promise<void | ConfirmationResult>;
        /**
         * Get current conversation context
         *
         * @returns A promise that resolves to the conversation history and current view (if present)
         */
        getContext(): Promise<ConversationContext>;
        /**
         * Look resource ids in the context:
         * - conversation
         * - current blade paramters
         * - ask user to select a resource (if enabled by the method options)
         *
         * @param options Options to show the resource picker in case resource ids were not found in the context
         * @returns A promise that resolves to the list of resource ids.
         */
        inferResourceIds(options?: InferResourceIdsOptions): Promise<string[]>;
        /**
         * Invoke the ARM request
         *
         * @param options Parameters for the ARM request, like resource id, version, http method and content
         * @returns A promise that resolves to the result of the ARM request
         */
        invokeARM(options: InvokeARMOptions): Promise<unknown>;
        /**
         * Show resource picker
         *
         * @param options Options to show resoure picker
         * @returns A promise that resolves to the list of selected resource ids.
         */
        selectResources(options: SelectResourcesOptions): Promise<string[]>;
        /**
         * This method returns a url based on the value based on the blade reference (along with parameters provided).
         *
         * @param options The blade reference.
         * @returns A promise that resolves to the url for opening the blade.
         */
        getBladeDeepLink(options: GetBladeDeepLinkOptions): Promise<string>;
    };
    type NudgePrompt = Readonly<{
        /**
         * Prompt to initiate conversation
         */
        prompt: string;
        /**
         * (Display) Prompt to initiate conversation
         * [This is only for display purposes, and is not passed to the orchestrator]
         */
        displayPrompt?: string;
    }>;
    type NudgePromptStarters = readonly {
        /**
         * Prompt to initiate conversation
         */
        prompt: string;
    }[];
    /**
     * Nudge Copilot options
     */
    type NudgeOptions = NudgePrompt | NudgePromptStarters;
    /**
     * Nudge Copilot result
     */
    type NudgeResult = Readonly<{
        /**
         * - Ok, in case there was no active conversation
         * - Busy, otherwise
         */
        result: "Ok" | "Busy";
    }>;
    /**
     * Add viewContext additionalDetails [Copilot] options
     */
    type AddViewContextAdditionalDetailsOptions = Readonly<{
        /**
         * Additional details to be added to the view context for the copilot to be aware of
         */
        additionalDetails: Record<string, any>;
    }>;
    /**
     * Conversation context
     */
    type ConversationContext = Readonly<{
        /**
         * History of the current conversation
         */
        history: HistoryEntry[];
        /**
         * Optional. The view context if any view is opened at the current moment
         */
        viewContext?: Readonly<{
            bladeReference?: Readonly<{
                bladeName: string;
                extensionName: string;
                parameters?: StringMap<unknown>;
            }>;
        }>;
    }>;
    /**
     * Conversation history entry
     */
    type HistoryEntry = Readonly<{
        /**
         * Source of the message
         */
        source: "user" | "copilot" | "clientHandler";
        /**
         * Message with all attachments that either may contains content
         */
        message: Readonly<{
            text: string;
            attachments?: unknown[];
        }>;
        /**
         * Optional. In case source is clientHandler it contains the handler id
         */
        handlerId?: string;
    }>;
    /**
     * Show message options
     */
    type ShowMessageOptions = Readonly<string | IAdaptiveCard>;
    /**
     * Show confirmation options
     */
    type ShowMessageWithActionsOptions = Readonly<ConfirmationOptions | IAdaptiveCard>;
    /**
     * Show resources options
     */
    type ShowResourcesOptions = Readonly<{
        /**
         * List of resource ids to show
         */
        resourceIds: string[];
        /**
         * Flag that list need to show selection
         */
        awaitForSelection?: boolean;
        /**
         * Card that can provide additional messages and actions
         * Will be displayed as
         *  card.base
         *  generated resource table
         *  card.actions (if present) or generated actions (in case awaitForSelection is set)
         */
        card?: IAdaptiveCard;
        /**
         * Optional configuration to show resource details
         * Default value is
         * ```json
         * {
         *    subscription: true;
         *    resourceGroup: true;
         *    resourceType: false;
         * }
         * ```
         */
        showDetails?: {
            /**
             * show subscription
             */
            subscription?: boolean;
            /**
             * show resource group
             */
            resourceGroup?: boolean;
            /**
             * show resource type
             */
            resourceType?: boolean;
        };
    }>;
    /**
     * Confirmation options
     */
    type ConfirmationOptions = Readonly<{
        /**
         * Text to show
         */
        text: string;
    } & (PredefinedConfirmationType | CustomConfirmationType)>;
    /**
     * Perdefined confirmation type
     */
    type PredefinedConfirmationType = {
        /**
         * Predefined: "YesNo", "OkCancel" - use predefined list of actions
         */
        type: "YesNo" | "OkCancel";
    };
    /**
     * Custom confirmation type
     */
    type CustomConfirmationType = {
        /**
         * Custom: use custom list of actions
         */
        type: "Custom";
        /**
         * Custom actions
         */
        actions?: Readonly<{
            id: string;
            title: string;
        }>[];
    };
    /**
     * Result of message with actions
     */
    type ConfirmationResult = {
        /**
         * Id of the selected action
         */
        result: string;
        /**
         * (Optional) Additional data (inputs, selections, etc...)
         */
        data?: unknown;
    };
    type InferResourceIdsOptionsBase = Readonly<{
        /**
         * Force to use current context as source even in case resource ids alrady collected from history
         */
        skipContext?: boolean;
    }>;
    type InferResourceIdsOptionsResource = InferResourceIdsOptionsBase & Readonly<{
        /**
         * Infer resource ids from different levels: Tenant, Subscription, ResourceGroup
         * Used by default
         */
        scope?: "Resource";
        /**
         * Use Resource Picker in case there is no resource ids collected from history or current context
         */
        useResourcePicker?: boolean;
        /**
         * Filters for resource ids to return only resource ids that fit specified filters
         */
        filters?: {
            /**
             * Filter by resource type
             */
            resourceTypes?: string[];
        };
    }>;
    type InferResourceIdsOptionsContainer = InferResourceIdsOptionsBase & Readonly<{
        /**
         * Infer either subscription or resource group ids
         */
        scope: "Subscription" | "ResourceGroup";
    }>;
    /**
     * Infer resource ids options
     */
    type InferResourceIdsOptions = InferResourceIdsOptionsResource | InferResourceIdsOptionsContainer;
    type InvokeARMResourceOptions = {
        resourceId: string;
        version: string;
        httpMethod: "GET" | "HEAD" | "POST" | "PUT" | "DELETE" | "PATCH";
        content?: unknown;
    };
    type InvokeARMUriOptions = {
        uri: string;
        httpMethod: "GET" | "HEAD" | "POST" | "PUT" | "DELETE" | "PATCH";
        content?: unknown;
    };
    type InvokeARMOptions = Readonly<InvokeARMResourceOptions | InvokeARMUriOptions>;
    type HandlerInfoOptions = {
        handlerInfo?: HandlerInfo;
    };
    const enum OrchestrationRuntime {
        /**
         * The original orchestrator
         */
        Legacy = 0,
        /**
         * The Sydney orchestrator
         */
        Sydney = 1
    }
    type HandlerInfo = {
        /**
         * Client handler id
         */
        handlerId: string;
        /**
         * Current conversation id
         */
        conversationId: string;
        /**
         * Current user question activity id
         */
        correlationId?: string;
        /**
         * Current server activity id
         */
        activityId?: string;
        /**
         * Optional client handler arguments
         */
        handlerArguments?: any;
        /**
         * Optional current orchestrator
         */
        runtime?: OrchestrationRuntime;
    };
    type CopilotInteractionOption<I extends CopilotInteractions, O = void> = {
        interaction: I;
        options: HandlerInfoOptions & {
            options: O;
        };
    };
    /**
     * Filter configuration for the Select Resources options
     */
    type FilterConfiguration = {
        /**
         * List of values that are automatically selected in the filter (default: all).
         */
        readonly preselectedValues?: readonly string[];
        /**
         * Flag hiding the filter control (default: false).
         */
        readonly hideFilter?: boolean;
        /**
         * If specified, only these values will show up in the filter (default: all).
         */
        readonly includedValues?: readonly string[];
        /**
         * List of values that are always excluded in the query and don't show up in the filter (default: none).
         */
        readonly excludedValues?: readonly string[];
    };
    /**
     * Select resources options
     */
    type SelectResourcesOptions = {
        /**
         * Title of the blade (default: "Select resources").
         */
        readonly title?: string;
        /**
         * Text displayed on the submit button (default: "Select").
         */
        readonly submitButtonText?: string;
        /**
         * Optional text or HTML to display in the info box on top.
         */
        readonly infoBoxContent?: string;
        /**
         * Maximum number of selected items (default: 1000).
         */
        readonly maxSelectedItems?: number;
        /**
         * Properties to control filters.
         * All of them are optional.
         * If not provided, a filter will keep the default behavior:
         * - All filter values available
         * - All filter values initially selected
         * - Filter visible and available for user interaction
         */
        readonly filters?: {
            /**
             * Resource types filter. Provide resource type ids (i.e. "microsoft.compute/virtualmachinescalesets").
             */
            readonly resourceTypeIds?: FilterConfiguration;
            /**
             * Locations filter. Provide location ids (i.e. "westus").
             */
            readonly locationIds?: FilterConfiguration;
            /**
             * Resource groups filter. Provide resrouce group NAMES (not full ids).
             */
            readonly resourceGroupNames?: FilterConfiguration;
        };
        /**
         * Optional list of subscription ids. If provided, subscription filter will not be displayed.
         */
        readonly subscriptionIds?: readonly string[];
        /**
         * ARG query fragment to be appended at the end (i.e. to exclude selected resources).
         */
        readonly customQuerySnippet?: string;
        /**
         * List of resources that show up as selected (default: none).
         */
        readonly preselectedResourceIds?: readonly string[];
        /**
         * Optional value to be selected by default in the groupBy dropdown.
         */
        readonly defaultGroupByValue?: "subscription" | "resourceGroup" | "assetType" | "location" | "kind";
    };
    /**
     * Provides a way to display data in a tabular form.
     */
    interface ITable extends ICardElement {
        /**
         * Must be "Table".
         */
        type: "Table";
        /**
         * 	Defines the number of columns in the table, their sizes, and more.
         */
        columns?: ITableColumn[];
        /**
         * Defines the rows of the table.
         */
        rows?: ITableRow[];
        /**
         * Specifies whether the first row of the table should be treated as a header row, and be announced as such by accessibility software.
         */
        firstRowAsHeader?: boolean;
        /**
         * Specifies whether grid lines should be displayed.
         */
        showGridLines?: boolean;
        /**
         * Defines the style of the grid. This property currently only controls the grid’s color.
         */
        gridStyle?: ContainerStyle;
        /**
         * Controls how the content of all cells is horizontally aligned by default. When not specified, horizontal alignment is defined on a per-cell basis.
         */
        horizontalCellContentAlignment?: HorizontalAlignment;
        /**
         * Controls how the content of all cells is vertically aligned by default. When not specified, vertical alignment is defined on a per-cell basis.
         */
        verticalCellContentAlignment?: VerticalAlignment;
    }
    /**
     * Represents a column within a Table element.
     */
    interface ITableColumn {
        /**
         * "auto", "stretch", a number representing relative width of the column in the column group, or in version 1.1 and higher, a specific pixel width, like "50px".
         */
        width: number | string;
    }
    /**
     * Represents a row of cells within a Table element.
     */
    interface ITableRow {
        /**
         * Must be "TableCell".
         */
        type: "TableRow";
        /**
         * Defines the cells of the row.
         */
        cells?: ITableCell[];
        /**
         * Style hint for TableRow.
         */
        style?: ContainerStyle;
    }
    /**
     * Represents a cell within a row of a Table element.
     */
    interface ITableCell {
        /**
         * Must be "TableCell".
         */
        type: "TableCell";
        /**
         * The card elements to render inside the TableCell.
         */
        items?: CardElement[];
        /**
         * Style hint for TableCell.
         */
        style?: ContainerStyle;
        /**
         * Defines how the content should be aligned vertically within the container. When not specified, the value of verticalContentAlignment is inherited from the parent container. If no parent container has verticalContentAlignment set, it defaults to Top.
         */
        verticalContentAlignment?: VerticalAlignment;
        /**
         * Determines whether the element should bleed through its parent’s padding.
         */
        bleed?: boolean;
        /**
         * Specifies the minimum height of the container in pixels, like "80px".
         */
        minHeight?: string;
        /**
         * When true content in this container should be presented right to left.
         * When ‘false’ content in this container should be presented left to right.
         * When unset layout direction will inherit from parent container or column.
         * If unset in all ancestors, the default platform behavior will apply.
         */
        rtl?: boolean;
    }
    type CodeBlock = Readonly<ICardElement & {
        type: "CodeBlock";
        text: string;
        language?: "argquery" | "azurecli" | "azurepowershell-interactive" | "bash" | "csharp" | "js" | "json" | "powershell" | "ts" | "other";
    }>;
    interface IAdaptiveCard extends ICardElement {
        type?: "AdaptiveCard";
        version?: IVersion | string;
        backgroundImage?: IBackgroundImage | string;
        body?: CardElement[];
        actions?: (ISubmitAction | IOpenUrlAction)[];
        speak?: string;
    }
    type CardInput = IChoiceSetInput | IDateInput | INumberInput | ITextInput | ITimeInput | IToggleInput;
    type CardElement = IColumnSet | IContainer | IFactSet | IImage | IImageSet | ITextBlock | CodeBlock | ITable | CardInput;
    type Size = "auto" | "stretch" | "small" | "medium" | "large";
    type TextSize = "small" | "default" | "medium" | "large" | "extraLarge";
    type HorizontalAlignment = "left" | "center" | "right";
    type VerticalAlignment = "top" | "center" | "bottom";
    type Spacing = "none" | "small" | "default" | "medium" | "large" | "extraLarge" | "padding";
    type TextWeight = "lighter" | "default" | "bolder";
    type TextColor = "default" | "dark" | "light" | "accent" | "good" | "warning" | "attention";
    type ContainerStyle = "default" | "emphasis" | "accent" | "attention" | "good" | "warning";
    type ImageStyle = "default" | "person";
    type ActionStyle = "default" | "positive" | "destructive";
    type TextInputStyle = "text" | "tel" | "url" | "email" | "password";
    interface IAction {
        id: string;
        title?: string;
        style?: ActionStyle;
    }
    interface ISubmitAction extends IAction {
        type: "Action.Submit";
        data?: any;
    }
    interface IOpenUrlAction extends IAction {
        type: "Action.OpenUrl";
        url: string;
    }
    interface ICardElement {
        id?: string;
        speak?: string;
        horizontalAlignment?: HorizontalAlignment;
        spacing?: Spacing;
        separator?: boolean;
        height?: "auto" | "stretch";
    }
    interface IBackgroundImage {
        url: string;
    }
    interface ITextBlock extends ICardElement {
        type: "TextBlock";
        size?: TextSize;
        weight?: TextWeight;
        color?: TextColor;
        text: string;
        isSubtle?: boolean;
        wrap?: boolean;
        maxLines?: number;
    }
    interface IContainer extends ICardElement {
        type: "Container";
        backgroundImage?: IBackgroundImage | string;
        style?: ContainerStyle;
        verticalContentAlignment?: VerticalAlignment;
        selectAction?: IAction;
        items?: CardElement[];
    }
    interface IColumn extends ICardElement {
        backgroundImage?: IBackgroundImage | string;
        style?: ContainerStyle;
        verticalContentAlignment?: VerticalAlignment;
        selectAction?: IAction;
        items?: CardElement[];
        width?: number | "auto" | "stretch";
    }
    interface IColumnSet extends ICardElement {
        type: "ColumnSet";
        columns: IColumn[];
    }
    interface IFact {
        title: string;
        value: string;
        speak?: string;
    }
    interface IFactSet extends ICardElement {
        type: "FactSet";
        facts: IFact[];
    }
    interface IImage extends ICardElement {
        type: "Image";
        altText?: string;
        selectAction?: IAction;
        size?: Size;
        style?: ImageStyle;
        url: string;
    }
    interface IImageSet extends ICardElement {
        type: "ImageSet";
        images: IImage[];
        size?: Size;
    }
    interface IInput extends ICardElement {
        id: string;
        value?: string;
        label?: string;
    }
    interface IDateInput extends IInput {
        type: "Input.Date";
        min?: string;
        max?: string;
        placeholder?: string;
    }
    interface ITimeInput extends IInput {
        type: "Input.Time";
        min?: string;
        max?: string;
        placeholder?: string;
    }
    interface INumberInput extends IInput {
        type: "Input.Number";
        min?: number;
        max?: number;
        placeholder?: string;
    }
    interface ITextInput extends IInput {
        type: "Input.Text";
        isMultiline?: boolean;
        maxLength?: number;
        placeholder?: string;
        style?: TextInputStyle;
    }
    interface IToggleInput extends IInput {
        type: "Input.Toggle";
        title: string;
        valueOn?: string;
        valueOff?: string;
    }
    interface IChoice {
        title: string;
        value: string;
    }
    interface IChoiceSetInput extends IInput {
        type: "Input.ChoiceSet";
        isMultiSelect?: boolean;
        style?: "expanded" | "compact";
        placeholder?: string;
        choices: IChoice[];
    }
    interface IVersion {
        major: number;
        minor: number;
    }
}
//# sourceMappingURL=FxReactCommon.Modules.d.ts.map
