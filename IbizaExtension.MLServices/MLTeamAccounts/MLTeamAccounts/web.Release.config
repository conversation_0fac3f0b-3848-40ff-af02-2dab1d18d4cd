<?xml version="1.0" encoding="utf-8"?>

<!-- For more information on using web.config transformation visit http://go.microsoft.com/fwlink/?LinkId=125889 -->

<configuration xmlns:xdt="http://schemas.microsoft.com/XML-Document-Transform">
  <appSettings>
    <add key="Microsoft.Portal.Extensions.Microsoft_Azure_MLTeamAccounts.ApplicationConfiguration.IsDevelopmentMode" value="false" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="Microsoft.Portal.Framework.FrameworkConfiguration.AllowedParentFrame" value="['rc.portal.azure.com','portal.azure.com', 'ms.portal.azure.com', 'onecloud.azure-test.net', 'df.onecloud.azure-test.net']" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
  </appSettings>
  <!--
    In the example below, the "SetAttributes" transform will change the value of
    "connectionString" to use "ReleaseSQLServer" only when the "Match" locator
    finds an attribute "name" that has a value of "MyDB".

    <connectionStrings>
      <add name="MyDB"
        connectionString="Data Source=ReleaseSQLServer;Initial Catalog=MyReleaseDB;Integrated Security=True"
        xdt:Transform="SetAttributes" xdt:Locator="Match(name)"/>
    </connectionStrings>
  -->
</configuration>