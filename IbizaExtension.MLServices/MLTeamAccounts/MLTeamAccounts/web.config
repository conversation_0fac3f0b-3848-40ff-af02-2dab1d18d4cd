﻿<?xml version="1.0" encoding="utf-8"?>
<!--
  For more information on how to configure your ASP.NET application, please visit
  http://go.microsoft.com/fwlink/?LinkId=169433
  -->
<configuration>
  <configSections>
    <sectionGroup name="system.web.webPages.razor" type="System.Web.WebPages.Razor.Configuration.RazorWebSectionGroup, System.Web.WebPages.Razor, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35">
      <section name="host" type="System.Web.WebPages.Razor.Configuration.HostSection, System.Web.WebPages.Razor, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" requirePermission="false" />
      <section name="pages" type="System.Web.WebPages.Razor.Configuration.RazorPagesSection, System.Web.WebPages.Razor, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" requirePermission="false" />
    </sectionGroup>
  </configSections>
  <system.web.webPages.razor>
    <host factoryType="System.Web.Mvc.MvcWebRazorHostFactory, System.Web.Mvc, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" />
    <pages pageBaseType="System.Web.Mvc.WebViewPage">
      <namespaces>
        <add namespace="System.Web.Mvc" />
        <add namespace="System.Web.Mvc.Ajax" />
        <add namespace="System.Web.Mvc.Html" />
        <add namespace="System.Web.Routing" />
      </namespaces>
    </pages>
  </system.web.webPages.razor>
  <appSettings>
    <add key="webpages:Version" value="*******" />
    <add key="webpages:Enabled" value="false" />
    <add key="PreserveLoginUrl" value="true" />
    <add key="ClientValidationEnabled" value="true" />
    <add key="UnobtrusiveJavaScriptEnabled" value="true" />
    <add key="MsPortalFx:SettingsReader" value="[     'Microsoft.Portal.Framework.CloudSettingsReader, Microsoft.Portal.Azure',     'Microsoft.Portal.Framework.ConfigurationFileReader, Microsoft.Portal.Core'     ]" />
    <add key="Microsoft.Portal.Extensions.Microsoft_Azure_MLTeamAccounts.ApplicationConfiguration.IsDevelopmentMode" value="true" />
    <add key="Microsoft.Portal.Extensions.Microsoft_Azure_MLTeamAccounts.ApplicationConfiguration.CdnPrefix" value="" />
    <!-- Dogfood <add key="Microsoft.Portal.Extensions.Microsoft_Azure_MLTeamAccounts.ApplicationConfiguration.ArmEndpoint" value="https://api-dogfood.resources.windows-int.net/" />-->
    <add key="Microsoft.Portal.Extensions.Microsoft_Azure_MLTeamAccounts.ApplicationConfiguration.ArmEndpoint" value="https://management.azure.com/" />
    <add key="Microsoft.Portal.Extensions.Microsoft_Azure_MLTeamAccounts.ApplicationConfiguration.ArmApiVersion" value="2023-01-01" />
    <add key="Microsoft.Portal.Framework.DecryptionConfiguration.Provider" value="Microsoft.Portal.Framework.EnvelopedCmsConfigurationSettingDecryptionProvider, Microsoft.Portal.Core" />
    <add key="Microsoft.Portal.Framework.FrameworkConfiguration.CamelCasedSerializedPropertyNames" value="true" />
    <add key="Microsoft.Portal.Framework.FrameworkConfiguration.ServicePointManager" value="{         ConnectionLimitPerProcessor: 500,         UseNagleAlgorithm: false,         Expect100Continue: false       }" />
    <add key="Microsoft.Portal.Framework.FrameworkConfiguration.ThreadPoolConfiguration" value="{           MinWorkerThreads: 96,           MinIoThreads: 96       }" />
    <add key="Microsoft.Portal.Framework.FrameworkConfiguration.ExpectedHttpErrorsCodes" value="" />
    <add key="Microsoft.Portal.Framework.ServerIdConfiguration.ServerId" value="localhost" />
    <!--Dogfood <add key="Microsoft.Portal.Security.AzureActiveDirectory.AadAuthenticationConfiguration.Authority" value="https://login.windows-ppe.net/" />-->
    <add key="Microsoft.Portal.Security.AzureActiveDirectory.AadAuthenticationConfiguration.Authority" value="https://login.windows.net/" />
    <add key="Microsoft.Portal.Security.AzureActiveDirectory.AadAuthenticationConfiguration.TenantId" value="common" />
    <add key="Microsoft.Portal.Security.AzureActiveDirectory.AadAuthenticationConfiguration.AllowedAudiences" value="['https://management.core.windows.net/']" />
    <add key="Microsoft.Portal.Security.AzureActiveDirectory.AadAuthenticationConfiguration.MinValidationCertUpdateInterval" value="PT05M" />
    <add key="Microsoft.Portal.Security.AzureActiveDirectory.AadAuthenticationConfiguration.MaxValidationCertUpdateInterval" value="PT24H" />
    <add key="Microsoft.Portal.Security.AzureActiveDirectory.AadAuthenticationConfiguration.ForwardDecryptedAuthorizationTokens" value="false" />
    <add key="Microsoft.Portal.Security.AzureActiveDirectory.AadAuthenticationConfiguration.MaxValidationClockSkewInterval" value="PT0S" />
    <add key="Microsoft.Portal.Framework.FrameworkConfiguration.AllowedParentFrame" value="['rc.portal.azure.com','portal.azure.com', 'dev.windows.azure-test.net', 'localhost', 'onestb.cloudapp.net','onecloud.azure-test.net', 'df.onecloud.azure-test.net']" />
    <add key="Microsoft.Portal.Extensions.Microsoft_Azure_MLTeamAccounts.ApplicationConfiguration.TestInProdEnvironment" value="https://portal.azure.com" />
    <!-- Api versions -->
    <add key="Microsoft.Portal.Extensions.Microsoft_Azure_MLTeamAccounts.ApplicationConfiguration.ContainerRegistryApiVersion" value="2017-10-01" />
    <add key="Microsoft.Portal.Extensions.Microsoft_Azure_MLTeamAccounts.ApplicationConfiguration.MachineLearningServicesApiVersion" value="2023-04-01" />
    <add key="Microsoft.Portal.Extensions.Microsoft_Azure_MLTeamAccounts.ApplicationConfiguration.MachineLearningRegistryApiVersion" value="2022-10-01-preview" />
    <add key="Microsoft.Portal.Extensions.Microsoft_Azure_MLTeamAccounts.ApplicationConfiguration.MLPublicAccessApiVersion" value="2021-10-01" />
    <add key="Microsoft.Portal.Extensions.Microsoft_Azure_MLTeamAccounts.ApplicationConfiguration.StorageApiVersion" value="2019-04-01" />
    <add key="Microsoft.Portal.Extensions.Microsoft_Azure_MLTeamAccounts.ApplicationConfiguration.AppInsightsApiVersion" value="2015-05-01" />
    <add key="Microsoft.Portal.Extensions.Microsoft_Azure_MLTeamAccounts.ApplicationConfiguration.KeyVaultApiVersion" value="2019-09-01" />
    <add key="Microsoft.Portal.Extensions.Microsoft_Azure_MLTeamAccounts.ApplicationConfiguration.ProviderCheckApiVersion" value="2017-05-01" />
    <add key="Microsoft.Portal.Extensions.Microsoft_Azure_MLTeamAccounts.ApplicationConfiguration.ClassicStorageApiVersion" value="2014-04-01" />
    <add key="Microsoft.Portal.Extensions.Microsoft_Azure_MLTeamAccounts.ApplicationConfiguration.MachineLearningServicesDocumentationUrl" value="https://go.microsoft.com/fwlink/?linkid=872674" />
    <add key="Microsoft.Portal.Extensions.Microsoft_Azure_MLTeamAccounts.ApplicationConfiguration.machineLearningServiceOverviewDocumentationUrl" value="https://go.microsoft.com/fwlink/?linkid=2112881" />
    <add key="Microsoft.Portal.Extensions.Microsoft_Azure_MLTeamAccounts.ApplicationConfiguration.MachineLearningServicesGitHubUrl" value="https://aka.ms/aml-notebooks" />
    <add key="Microsoft.Portal.Extensions.Microsoft_Azure_MLTeamAccounts.ApplicationConfiguration.MachineLearningServicesForumUrl" value="https://aka.ms/aml-forum-service" />
    <add key="Microsoft.Portal.Extensions.Microsoft_Azure_MLTeamAccounts.ApplicationConfiguration.MachineLearningServicesNotebookUrl" value="https://notebooks.azure.com/azureml/libraries/azureml-getting-started" />
    <add key="Microsoft.Portal.Extensions.Microsoft_Azure_MLTeamAccounts.ApplicationConfiguration.MachineLearningServicesManageRegistryUrl" value="https://aka.ms/azureml-registries-manage" />
    <add key="Microsoft.Portal.Extensions.Microsoft_Azure_MLTeamAccounts.ApplicationConfiguration.MachineLearningServicesShareRegistryUrl" value="https://aka.ms/azureml-registries-share" />
    <add key="Microsoft.Portal.Extensions.Microsoft_Azure_MLTeamAccounts.ApplicationConfiguration.WebWorkspaceApiVersion" value="2023-04-01" />
    <add key="Microsoft.Portal.Extensions.Microsoft_Azure_MLTeamAccounts.ApplicationConfiguration.OnlineEndpointsApiVersion" value="2020-12-01-preview" />
    <add key="Microsoft.Portal.Extensions.Microsoft_Azure_MLTeamAccounts.ApplicationConfiguration.ModelManagementApiVersion" value="2020-09-01-preview" />
    <!-- Urls -->
    <add key="Microsoft.Portal.Extensions.Microsoft_Azure_MLTeamAccounts.ApplicationConfiguration.MachineLearningServicesContainerRegistryUrl" value="https://go.microsoft.com/fwlink/?linkid=2003001" />
    <add key="Microsoft.Portal.Extensions.Microsoft_Azure_MLTeamAccounts.ApplicationConfiguration.MachineLearningServicesStorageUrl" value="https://go.microsoft.com/fwlink/?linkid=2002900" />
    <add key="Microsoft.Portal.Extensions.Microsoft_Azure_MLTeamAccounts.ApplicationConfiguration.MachineLearningServicesAppInsightsUrl" value="https://go.microsoft.com/fwlink/?linkid=2002901" />
    <add key="Microsoft.Portal.Extensions.Microsoft_Azure_MLTeamAccounts.ApplicationConfiguration.MachineLearningServicesKeyVaultUrl" value="https://go.microsoft.com/fwlink/?linkid=2003000" />
    <add key="Microsoft.Portal.Extensions.Microsoft_Azure_MLTeamAccounts.ApplicationConfiguration.MachineLearningServicesWarningUrl" value="https://go.microsoft.com/fwlink/?linkid=2002209" />
    <add key="Microsoft.Portal.Extensions.Microsoft_Azure_MLTeamAccounts.ApplicationConfiguration.WebWorkspaceFrameUri" value="https://mlworkspace.azureml-test.net" />
    <add key="Microsoft.Portal.Extensions.Microsoft_Azure_MLTeamAccounts.ApplicationConfiguration.WebWorkspace2BaseUrl" value="https://master.ml.azure.com" />
    <add key="Microsoft.Portal.Extensions.Microsoft_Azure_MLTeamAccounts.ApplicationConfiguration.StudioDocumentationUrl" value="https://aka.ms/aml/documents" />
    <add key="Microsoft.Portal.Extensions.Microsoft_Azure_MLTeamAccounts.ApplicationConfiguration.MachineLearningServicesEnterpriseEditionUrl" value="https://azure.microsoft.com/pricing/details/machine-learning/" />
    <add key="Microsoft.Portal.Extensions.Microsoft_Azure_MLTeamAccounts.ApplicationConfiguration.MachineLearningServicesStudioUrl" value="https://studio.azureml.com" />
    <add key="Microsoft.Portal.Extensions.Microsoft_Azure_MLTeamAccounts.ApplicationConfiguration.LocationsApiVersion" value="2019-11-01" />
    <add key="Microsoft.Portal.Extensions.Microsoft_Azure_MLTeamAccounts.ApplicationConfiguration.VMSizesApiVersion" value="2018-11-19" />
    <add key="Microsoft.Portal.Extensions.Microsoft_Azure_MLTeamAccounts.ApplicationConfiguration.QuotaUsageApiVersion" value="2020-04-01" />
    <add key="Microsoft.Portal.Extensions.Microsoft_Azure_MLTeamAccounts.ApplicationConfiguration.UpdateQuotasApiVersion" value="2020-04-01" />
    <add key="Microsoft.Portal.Extensions.Microsoft_Azure_MLTeamAccounts.ApplicationConfiguration.DatabricksApiVersion" value="2018-04-01" />
    <!-- Feature Flags -->
    <add key="Microsoft.Portal.Extensions.Microsoft_Azure_MLTeamAccounts.ApplicationConfiguration.FeatureCrossLocationComputeSubscriptionAllowList" value="428ad92a-9a7a-47f9-8383-399a14629c48" />
  </appSettings>
  <!--
    For a description of web.config changes see http://go.microsoft.com/fwlink/?LinkId=235367.

    The following attributes can be set on the <httpRuntime> tag.
      <system.Web>
        <httpRuntime targetFramework="4.7.2" />
      </system.Web>
  -->
  <system.web>
    <authentication mode="None">
      <forms requireSSL="true" />
    </authentication>
    <customErrors mode="RemoteOnly" />
    <httpRuntime targetFramework="4.5" enableVersionHeader="false" />
    <anonymousIdentification cookieRequireSSL="true" cookieProtection="All" cookieSlidingExpiration="false" />
    <roleManager cookieRequireSSL="true" cookieProtection="All" cookieSlidingExpiration="false" />
    <httpCookies httpOnlyCookies="true" requireSSL="true" />
    <sessionState mode="Off" regenerateExpiredSessionId="true" />
    <compilation debug="true" targetFramework="4.7.2" />
    <pages validateRequest="true" pageParserFilterType="System.Web.Mvc.ViewTypeParserFilter, System.Web.Mvc, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" pageBaseType="System.Web.Mvc.ViewPage, System.Web.Mvc, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" userControlBaseType="System.Web.Mvc.ViewUserControl, System.Web.Mvc, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35">
      <namespaces>
        <add namespace="System.Web.Helpers" />
        <add namespace="System.Web.Mvc" />
        <add namespace="System.Web.Mvc.Ajax" />
        <add namespace="System.Web.Mvc.Html" />
        <add namespace="System.Web.Routing" />
        <add namespace="System.Web.WebPages" />
      </namespaces>
      <controls>
        <add assembly="System.Web.Mvc, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" namespace="System.Web.Mvc" tagPrefix="mvc" />
      </controls>
    </pages>
    <httpHandlers>
      <add path="Views" verb="*" type="System.Web.HttpNotFoundHandler" />
    </httpHandlers>
  </system.web>
  <system.webServer>
    <validation validateIntegratedModeConfiguration="false" />
    <handlers>
      <remove name="ExtensionlessUrlHandler-ISAPI-4.0_32bit" />
      <remove name="ExtensionlessUrlHandler-ISAPI-4.0_64bit" />
      <remove name="BlockViewHandler" />
      <add name="BlockViewHandler" path="Views" verb="*" preCondition="integratedMode" type="System.Web.HttpNotFoundHandler" />
      <add name="ExtensionlessUrlHandler-ISAPI-4.0_32bit" path="*." verb="GET,HEAD,POST,DEBUG,PUT,DELETE,PATCH,OPTIONS" modules="IsapiModule" scriptProcessor="%windir%\Microsoft.NET\Framework\v4.0.30319\aspnet_isapi.dll" preCondition="classicMode,runtimeVersionv4.0,bitness32" responseBufferLimit="0" />
      <add name="ExtensionlessUrlHandler-ISAPI-4.0_64bit" path="*." verb="GET,HEAD,POST,DEBUG,PUT,DELETE,PATCH,OPTIONS" modules="IsapiModule" scriptProcessor="%windir%\Microsoft.NET\Framework64\v4.0.30319\aspnet_isapi.dll" preCondition="classicMode,runtimeVersionv4.0,bitness64" responseBufferLimit="0" />
      <remove name="ExtensionlessUrlHandler-Integrated-4.0" />
      <remove name="OPTIONSVerbHandler" />
      <remove name="TRACEVerbHandler" />
      <add name="ExtensionlessUrlHandler-Integrated-4.0" path="*." verb="*" type="System.Web.Handlers.TransferRequestHandler" preCondition="integratedMode,runtimeVersionv4.0" />
    </handlers>
    <modules runAllManagedModulesForAllRequests="true">
      <!-- WebDAV can interfere with Web API PUT requests, so disable WebDAV -->
      <remove name="WebDAVModule" />
    </modules>
    <httpProtocol>
      <customHeaders>
        <clear />
        <add name="X-UA-Compatible" value="IE=edge" />
      </customHeaders>
    </httpProtocol>
  </system.webServer>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Mvc" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Net.Http.Formatting" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Http.WebHost" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Http" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Data.Services.Client" culture="neutral" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Data.OData" culture="neutral" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Data.Edm" culture="neutral" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Spatial" culture="neutral" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="0.0.0.0-6.4.0.0" newVersion="6.4.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Helpers" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="1.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.WebPages" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="1.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
</configuration>