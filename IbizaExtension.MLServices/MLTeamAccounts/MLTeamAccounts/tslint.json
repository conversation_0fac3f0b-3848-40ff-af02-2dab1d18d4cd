{"defaultSeverity": "warning", "rules": {"member-access": [true, "check-accessor", "check-parameter-property"], "member-ordering": [true, {"order": ["private-static-field", "protected-static-field", "public-static-field", "private-static-method", "protected-static-method", "public-static-method", "private-instance-field", "protected-instance-field", "public-instance-field", "private-constructor", "protected-constructor", "public-constructor", "public-instance-method", "protected-instance-method", "private-instance-method"], "alphabetize": false}], "no-inferrable-types": [true, "ignore-params", "ignore-properties"], "no-internal-module": true, "no-unnecessary-type-assertion": true, "curly": true, "no-return-await": true, "no-string-throw": true, "no-var-keyword": true, "prefer-object-spread": true, "prefer-const": true, "comment-format": [true, "check-space"], "no-angle-bracket-type-assertion": true, "no-boolean-literal-compare": true, "ordered-imports": true}, "jsRules": {}, "rulesDirectory": []}