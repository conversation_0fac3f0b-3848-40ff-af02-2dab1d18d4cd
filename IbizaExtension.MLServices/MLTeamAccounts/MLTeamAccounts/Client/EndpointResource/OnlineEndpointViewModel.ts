import { BladeReferences } from "Fx/Composition";
import * as endpointDataModels from "./DataModels/OnlineEndpointDataModel";
import { ResourceMenuBladeIds, MenuItemIds } from "../Shared/Constants";
import { Icons } from "../Shared/Icons";
import { DataContext } from "./EndpointResourceArea";
import * as Di from "Fx/DependencyInjection";
import * as ClientResources from "Resx/ClientResources";
import { Logging } from "Shared/Logging";
import FxAssets = MsPortalFx.Assets;
import FxBase = MsPortalFx.Base;
import AssetsApi = MsPortalFx.Assets;
import ResourceTypesService = MsPortalFx.ViewModels.Services.ResourceTypes;

module BrowseColumns {
    "use strict";

    export var endpointId = "endpointId";
    export var scoringUri = "scoringUri";
}

/**
 * ResourceAssetType that implements Resource Menu and Browse Contract
 * Learn more about Browse: https://aka.ms/portalfx/browse
 * Learn more about Resource Menu: https://aka.ms/portalfx/resourcemenu
 */
@Di.Class("viewModel")
export class OnlineEndpointViewModel implements FxAssets.ResourceMenuWithCallerSuppliedResourceContract,
    FxAssets.BrowseConfigContract, MsPortalFx.Assets.SupplementalDataContract {
    /**
     * The container contains APIs you can call to interact with the shell.
     */
    private _container: MsPortalFx.ViewModels.ContainerContract;

    /**
     * The context property contains APIs you can call to interact with the shell.
     * Learn more about: https://aka.ms/portalfx/datacontext
     */
    private _runningSupplementalDataFetch: Promise<unknown>;
    private _dataContext: DataContext;
    private _supplementalDataView: MsPortalFx.Data.QueryView<endpointDataModels.OnlineEndpoint, any>;

    public supplementalDataStream = ko.observableArray<AssetsApi.SupplementalData>([]);

    /**
     * Constructor for Asset Type
     * @param container
     * @param initialState
     * @param EndpointDataContext
     */
    constructor(container: MsPortalFx.ViewModels.ContainerContract, dataContext: DataContext) {
        this._container = container;
        this._dataContext = dataContext;
    }

    /**
     * Specifies the Browse configuration such as columns
     */
    public getBrowseConfig(): Promise<FxAssets.BrowseConfig> {
        return Q({
            columns: [
                {
                    id: BrowseColumns.endpointId,
                    name: ko.observable<string>(ClientResources.endpointId),
                    itemKey: BrowseColumns.endpointId
                },
                {
                    id: BrowseColumns.scoringUri,
                    name: ko.observable<string>(ClientResources.endpointScoringUriLabel),
                    itemKey: BrowseColumns.scoringUri
                }
            ],
            defaultColumns: [
                "resourceGroup",
                "location"
            ]
        });
    }

    /**
     * The menu config for the Resource menu blade
     * @param resourceInfo The resource informaiton
     */
    public getMenuConfig(resourceInfo: FxAssets.ResourceInformation): Promise<FxAssets.ResourceMenuConfig> {
        const { resourceId } = resourceInfo;

        const overviewItem: FxAssets.MenuItem = {
            id: ResourceMenuBladeIds.overview, // menu item IDs must be unique, must not be localized, should not contain spaces and should be lowercase
            displayText: ClientResources.overview,
            enabled: ko.observable(true),
            keywords: ClientResources.overviewKeywords,
            icon: Icons.cloudService,
            supplyBladeReference: () => {
                return BladeReferences.forBlade("OnlineEndpointOverviewBlade").createReference({
                    parameters: {
                        id: resourceId
                    }
                });
            },
        };

        const propertiesItem: FxAssets.MenuItem = {
            id: MenuItemIds.properties,
            displayText: ClientResources.labelProperties,
            enabled: ko.observable(true),
            keywords: "properties",
            icon: MsPortalFx.Base.Images.Polychromatic.Controls(),
            supplyBladeReference: () => {
                return BladeReferences.forBlade("OnlineEndpointPropertiesBlade").createReference({
                    parameters: {
                        id: resourceId
                    }
                });
            }
        };

        const managedIdentitiesItem: FxAssets.MenuItem = {
            id: MenuItemIds.managedIdentities,
            displayText: ClientResources.ManagedIdentities.menuText,
            enabled: ko.observable(true),
            keywords: "managed service identities",
            icon: Icons.managedIdnetitiesIcon,
            supplyBladeReference: () => {
                return BladeReferences.forExtension("Microsoft_Azure_ManagedServiceIdentity").forBlade("AzureResourceIdentitiesBladeV2").createReference({
                    parameters: {
                        resourceId,
                        apiVersion: MsPortalFx.getEnvironmentValue("onlineEndpointsApiVersion"),
                        systemAssignedStatus: AzureResourceIdentitiesBladeV2Params.IdentityStatus.Preview,
                        userAssignedStatus: AzureResourceIdentitiesBladeV2Params.IdentityStatus.Preview
                    }
                });
            },
        };

        const settingsGroup: FxAssets.MenuGroup = {
            id: FxAssets.ManagementGroupId,
            displayText: ClientResources.titleSettings,
            items: [propertiesItem, managedIdentitiesItem]
        };

        const supportGroup: FxAssets.MenuGroup = {
            id: "support_group",
            displayText: ClientResources.titleSupport,
            items: []
        };

        const deploymentsItem: FxAssets.MenuItem = {
            id: 'deployments',
            displayText: ClientResources.labelDeployments,
            enabled: ko.observable(true),
            keywords: "deployments",
            icon: MsPortalFx.Base.Images.Polychromatic.WebSlots(),
            supplyBladeReference: () => {
                return BladeReferences.forBlade("DeploymentsBlade").createReference({
                    parameters: {
                        id: resourceId
                    }
                });
            },
        };

        const deploymentsGroup: FxAssets.MenuGroup = {
            id: "deployments_group",
            displayText: ClientResources.titleDeployments,
            items: [deploymentsItem]
        };

        // build the resource menu config.
        const menuConfig: FxAssets.ResourceMenuConfig = {
            overview: overviewItem,
            options: {
                // resourceMenuOptions information: https://github.com/Azure/portaldocs/blob/master/portal-sdk/generated/portalfx-resourcemenu-api.md
                enableRbac: true,
                enableTags: true,
                enableMetrics: true,
                enableDiagnostics: true,
                enableAlerts: true,
                enableLogs: true,
                enableSupportTroubleshootV2: true,
                enableSupportResourceHealth: false,
                enableSupportHelpRequest: true,
                enableSupportEventLogs: true,
                enableEventGridPublisher: true
            },
            groups: [settingsGroup, deploymentsGroup, supportGroup].filter(g => g.items.length > 0)
        };

        return Q(menuConfig);
    }

    public getSupplementalData(resourceIds: string[], columns: string[]): Promise<unknown> {
        this._supplementalDataView = this._supplementalDataView || this._dataContext.endpointData.onlineEndpointsQuery.createView(this._container);

        // connect the view to the supplemental data stream
        AssetsApi.SupplementalDataStreamHelper.ConnectView(
            this._container,
            this._supplementalDataView,
            this.supplementalDataStream,
            (endpoint: endpointDataModels.OnlineEndpoint) => {
                return resourceIds.some((resourceId) => {
                    return ResourceTypesService.compareResourceId(resourceId, endpoint.id());
                });
            },
            (endpoint: endpointDataModels.OnlineEndpoint) => {

                // save the resource id so Browse knows which row to update
                var supplementalData = <AssetsApi.SupplementalData>{ resourceId: endpoint.id() };

                if (columns.indexOf(BrowseColumns.endpointId) !== -1) {
                    supplementalData[BrowseColumns.endpointId] = endpoint.id();
                }

                if (columns.indexOf(BrowseColumns.scoringUri) !== -1) {
                    supplementalData[BrowseColumns.scoringUri] = endpoint.properties().scoringUri();
                }

                return supplementalData;
            });

        const latencyTelemetryEvent = Logging.StartLatencyTelemetryEvent("OnlineEndpointViewModel",
            "getSupplementalData", { resourceIds: resourceIds });
        const supplementalDataPromise = this._runFetchForSupplementalData(resourceIds);
        supplementalDataPromise.then(() => {
            Logging.EndLatencyTelemetryEvent(latencyTelemetryEvent);
        });

        return supplementalDataPromise;
    }

    private _runFetchForSupplementalData(resourceIds: string[]): Promise<unknown> {
        // make sure multiple fetches don't step on each other.
        if (!this._runningSupplementalDataFetch) {
            this._runningSupplementalDataFetch = this._supplementalDataView.fetch(resourceIds).then(() => {
                this._runningSupplementalDataFetch = null;
            });
        }
        return this._runningSupplementalDataFetch;
    }
}
