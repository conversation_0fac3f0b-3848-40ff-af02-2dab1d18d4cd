﻿/// <amd-bundling generated="typemetadata" />
/// <amd-dependency path="./AMLComputeOnlineEnpointProperties" />
import { OnlineEnpointProperties, OnlineEnpointPropertiesMetadata } from "./OnlineEndpointProperties";

export interface OnlineEndpoint {
    id: KnockoutObservable<string>;
    name: KnockoutObservable<string>;
    identity: KnockoutObservable<string>;
    location: KnockoutObservable<string>;
    type: KnockoutObservable<string>;
    tags: KnockoutObservable<string>;
    kind: KnockoutObservable<string>;
    properties: KnockoutObservable<OnlineEnpointProperties>;
}

export const OnlineEnpointMetadata: MsPortalFx.Data.Metadata.Metadata = {
    "name": "Microsoft_Azure_MLTeamAccounts.DataModels.OnlineEnpoint",
    "properties": {
        "properties": {
            "itemType": OnlineEnpointPropertiesMetadata.name
        },
        "id": {},
        "name": {},
        "identity": {},
        "location": {},
        "type": {},
        "tags": {},
        "kind": {}
    },
    "idProperties": [
        "id"
    ],
    "entityType": true,
    "hasGloballyUniqueId": true
};

MsPortalFx.Data.Metadata.setTypeMetadata(OnlineEnpointMetadata.name, OnlineEnpointMetadata);
