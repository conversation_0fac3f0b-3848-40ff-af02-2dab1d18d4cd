export interface OnlineDeployment {
    id: string;
    name: string;
    type: string;
    location: string;
    kind: string;
    properties: {
        description: string;
        type: string;
        environmentId: string;
        codeConfiguration: {
            codeId: string;
            scoringScript: string;
        };
        provisioningState: DeploymentProvisioningState;
        scaleSettings: {
            minInstances: string;
            maxInstances: string;
            instanceCount: string;
            scaleType: DeploymentScaleTypeMode;
        };
        environmentOverrides: {[key: string] : string};
        appInsightsEnabled: boolean;
    };
}

export type DeploymentScaleTypeMode =
    "Automatic" | "Manual" | "None";

export type DeploymentProvisioningState =
    "Creating" | "Deleting" | "Scaling" | "Updating" | "Succeeded" | "Failed";
