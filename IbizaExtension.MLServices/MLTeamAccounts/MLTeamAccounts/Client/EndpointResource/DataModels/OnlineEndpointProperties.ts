﻿/// <amd-bundling generated="typemetadata" />

export interface AuthKeys {
    primaryKey: KnockoutObservable<string>;
    secondaryKey: KnockoutObservable<string>;
}

export const AuthKeysMetadata: MsPortalFx.Data.Metadata.Metadata = {
    "name": "Microsoft_Azure_MLTeamAccounts.DataModels.AuthKeysMetadata",
    "properties": {
        "primaryKey": {},
        "secondaryKey": {}
    },
    "idProperties": [],
    "entityType": true,
    "hasGloballyUniqueId": true
};

MsPortalFx.Data.Metadata.setTypeMetadata(AuthKeysMetadata.name, AuthKeysMetadata);

export interface OnlineEnpointProperties {
    description: KnockoutObservable<string>;
    provisioningState: KnockoutObservable<string>;
    scoringUri: KnockoutObservable<string>;
    traffic: KnockoutObservable<StringMap<string>>;
    authMode: KnockoutObservable<string>;
    keys: KnockoutObservable<AuthKeys>;
    target: KnockoutObservable<string>;
    swaggerUri: KnockoutObservable<string>;
}

export const OnlineEnpointPropertiesMetadata: MsPortalFx.Data.Metadata.Metadata = {
    "name": "Microsoft_Azure_MLTeamAccounts.DataModels.OnlineEnpointPropertiesMetadata",
    "properties": {
        "description": {},
        "traffic": {},
        "target": {},
        "provisioningState": {},
        "scoringUri": {},
        "swaggerUri": {},
        "authMode": {},
        "keys": {
            "itemType": AuthKeysMetadata.name
        }
    },
    "idProperties": [],
    "entityType": true,
    "hasGloballyUniqueId": true
};

MsPortalFx.Data.Metadata.setTypeMetadata(OnlineEnpointPropertiesMetadata.name, OnlineEnpointPropertiesMetadata);
