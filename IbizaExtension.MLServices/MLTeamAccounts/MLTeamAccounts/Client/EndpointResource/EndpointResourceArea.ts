import * as Di from "Fx/DependencyInjection";

import { MachineLearningServicesData } from "../Resource/Data/MachineLearningServicesData";
import { ModelManagementData } from "./Data/ModelManagementData";
import { OnlineEndpointData } from "./Data/OnlineEndpointData";

@Di.Class()
export class DataContext {
    public endpointData: OnlineEndpointData;
    public machineLearningServicesData: MachineLearningServicesData;
    public modelManagementData: ModelManagementData;

    constructor(
        machineLearningServicesData: MachineLearningServicesData,
        endpointData: OnlineEndpointData,
        modelManagementData: ModelManagementData
    ) {
        this.machineLearningServicesData = machineLearningServicesData;
        this.endpointData = endpointData;
        this.modelManagementData = modelManagementData;
    }
}
