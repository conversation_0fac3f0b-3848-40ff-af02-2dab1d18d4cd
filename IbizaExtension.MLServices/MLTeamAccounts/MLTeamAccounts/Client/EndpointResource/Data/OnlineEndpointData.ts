import * as Di from "Fx/DependencyInjection";

import { AssetTypeNames } from "../../_generated/ExtensionDefinition";
import { Workspace } from "../../MLServicesDataModels/Workspace.types";
import { OnlineEndpoints, machineLearningServicesResourcesProvider, machineLearningWorkspaces } from "../../Shared/Constants";
import { Logging, ActionModifier } from "../../Shared/Logging";
import Utilities from "../../Shared/Utilities";
import { OnlineEndpoint } from "../DataModels/OnlineEndpointDataModel";
import { OnlineDeployment } from "../DataModels/DeploymentDataModel";

@Di.Class()
export class OnlineEndpointData {
    private static DataModelsOnlineEndpointType = "Microsoft_Azure_MLTeamAccounts.OnlineEndpoint";
    private static DataModelsMachineLearningServicesType = "Microsoft_Azure_MLTeamAccounts.MachineLearningService";

    private armEndpoint = MsPortalFx.getEnvironmentValue("armEndpoint");
    private endpointApiVersion = `api-version=${MsPortalFx.getEnvironmentValue("onlineEndpointsApiVersion")}`;
    private workspaceApiVersion = `api-version=${MsPortalFx.getEnvironmentValue("machineLearningServicesApiVersion")}`;

    public workspaceEndpointsQuery = new MsPortalFx.Data.QueryCache<OnlineEndpoint, any>({
        entityTypeName: OnlineEndpointData.DataModelsOnlineEndpointType,
        sourceUri: MsPortalFx.Data.uriFormatter(
            this.armEndpoint +
            `/subscriptions/{subscriptonId}/providers/${machineLearningServicesResourcesProvider}/${machineLearningWorkspaces}/{workspaceId}/${OnlineEndpoints.onlineEndpointsResourceName}/{endpointId}?` +
            this.endpointApiVersion,
            false
        ),
        supplyData: (httpMethod: string, uri: string, headers?: StringMap<any>, data?: any, params?: any) => {
            return Q(
                MsPortalFx.Base.Net2.ajax({
                    uri: uri,
                    type: httpMethod || "GET",
                    dataType: "json",
                    headers: headers,
                    contentType: "application/json",
                    setAuthorizationHeader: true,
                    invokeApi: "api/invoke",
                    data: data
                }).catch((failureResponse: MsPortalFx.Base.Net2.ErrorResponse<any>) => {
                    Logging.LogAjaxFailure(failureResponse.jqXHR, "workspaceEndpointsQuery");
                })
            );
        },
        processServerResponse: response => {
            return {
                data: response && response.value
            };
        }
    });

    public subscriptionWorkspacesQuery = new MsPortalFx.Data.QueryCache<Workspace, any>({
        entityTypeName: OnlineEndpointData.DataModelsMachineLearningServicesType,
        sourceUri: MsPortalFx.Data.uriFormatter(
            this.armEndpoint +
            `/subscriptions/{id}/providers/${machineLearningServicesResourcesProvider}/${machineLearningWorkspaces}?` +
            this.workspaceApiVersion,
            false
        ),
        supplyData: (httpMethod: string, uri: string, headers?: StringMap<any>, data?: any, params?: any) => {
            return Q(
                MsPortalFx.Base.Net2.ajax({
                    uri: uri,
                    type: httpMethod || "GET",
                    dataType: "json",
                    headers: headers,
                    contentType: "application/json",
                    setAuthorizationHeader: true,
                    invokeApi: "api/invoke",
                    data: data
                }).catch((failureResponse: MsPortalFx.Base.Net2.ErrorResponse<any>) => {
                    Logging.LogAjaxFailure(failureResponse.jqXHR, "subscriptionWorkspacesQuery");
                })
            );
        },
        processServerResponse: response => {
            return {
                data: response && response.value
            };
        }
    });

    public onlineEndpointsQuery = new MsPortalFx.Data.QueryCache<OnlineEndpoint, any>({
        entityTypeName: OnlineEndpointData.DataModelsOnlineEndpointType,
        sourceUri: (params: any) => {
            return params;
        },
        supplyData: (
            httpMethod: string,
            uri: string,
            headers: StringMap<any>,
            data: any,
            params: any,
            entryLifetime: MsPortalFx.Base.LifetimeManager
        ) => {
            const resourceIds: string[] = params;
            if (!resourceIds || resourceIds.length === 0) {
                return Q([]);
            }

            const subscriptionIds = this.getSubscriptionsFromResourceIds(resourceIds);
            const defer = Q.defer();
            Q.all(
                subscriptionIds.map(subscriptionId => {
                    const subscriptionWorkspacesQueryView = this.subscriptionWorkspacesQuery.createView(entryLifetime);
                    return Q(subscriptionWorkspacesQueryView.fetch(subscriptionId)).then(() => {
                        return ko.toJS(subscriptionWorkspacesQueryView.items());
                    });
                })
            ).then(
                value => {
                    const allTeamAccounts = [].concat.apply([], value);
                    defer.resolve(allTeamAccounts);
                },
                reason => {
                    defer.reject(reason);
                }
            );
            return defer.promise;
        },
        poll: true
    });

    public onlineEndpointEntity = new MsPortalFx.Data.EntityCache<OnlineEndpoint, string>({
        entityTypeName: OnlineEndpointData.DataModelsOnlineEndpointType,
        sourceUri: MsPortalFx.Data.uriFormatter(this.armEndpoint + "{id}?" + this.endpointApiVersion, false),
        supplyData: (httpMethod: string, uri: string, headers?: StringMap<any>, data?: any, params?: any) => {
            const resourceId = params;

            return Utilities.armGetRequest(uri).then(
                response => response,
                (failureResponse: MsPortalFx.Base.Net2.ErrorResponse<any>) => {
                    if (failureResponse.jqXHR.status === MsPortalFx.Base.Net2.HttpStatusCode.NotFound) {
                        MsPortalFx.UI.AssetManager.notifyAssetDeleted(AssetTypeNames.machineLearningServices, resourceId);
                    } else {
                        Logging.LogAjaxFailure(failureResponse.jqXHR, "onlineEndpointEntity");
                    }
                    return failureResponse;
                }
            );
        }
    });

    public getEndpointDeployments(resourceId: string): Q.Promise<OnlineDeployment[]> {
        const key = Logging.StartTrace("OnlineEndpointData", "getEndpointDeployments", ActionModifier.Start);
        const uri = `${this.armEndpoint}/${resourceId}/deployments?${this.endpointApiVersion}`;
        const deferred = Q.defer<any>();

        Utilities.armGetRequest(uri).then((response: any) => {
            Logging.EndTrace(key, ActionModifier.Success);
            deferred.resolve(<OnlineDeployment[]>response.value);
        }, (failureResponse: MsPortalFx.Base.Net2.ErrorResponse<any>) => {
            Logging.EndTrace(key, ActionModifier.Failed);
            Logging.LogAjaxFailure(failureResponse.jqXHR, "getEndpointDeployments");
            deferred.reject(failureResponse);
        });

        return deferred.promise;
    }

    public deleteEndpointDeployment(resourceId: string): Promise<any> {
        const key = Logging.StartTrace("OnlineEndpointData", "deleteEndpointDeployment", ActionModifier.Start);
        const uri = `${this.armEndpoint}/${resourceId}?${this.endpointApiVersion}`;
        const deferred = Q.defer<any>();

        Utilities.armGetRequest(uri).then((response: any) => {
            Logging.EndTrace(key, ActionModifier.Success);
            deferred.resolve(<OnlineDeployment[]>response.value);
        }, (failureResponse: MsPortalFx.Base.Net2.ErrorResponse<any>) => {
            Logging.EndTrace(key, ActionModifier.Failed);
            Logging.LogAjaxFailure(failureResponse.jqXHR, "deleteEndpointDeployment");
            deferred.reject(failureResponse);
        });

        return deferred.promise;
    }

    public getSubscriptionsFromResourceIds(resourceIds: string[]): string[] {
        return resourceIds.reduce((a, b) => {
            const subscriptionId = MsPortalFx.ViewModels.Services.ResourceTypes.parseResourceDescriptor(b).subscription;
            if (a.indexOf(subscriptionId) < 0) {
                a.push(subscriptionId);
            }
            return a;
        }, []);
    }
}
