import * as DropDown from "Fx/Controls/DropDown";
import * as ResourceGroupDropDown from "Fx/Controls/ResourceGroupDropDown";

import { Workspace } from "../../../MLServicesDataModels/Workspace.types";
import { ModelManagementData } from "../../Data/ModelManagementData";
import { IModelResource } from "../Models/CreateModels";

import * as ClientResources from "Resx/ClientResources";

export interface IModelPickerOptions {
    isNested?: boolean;
    model: KnockoutObservableBase<IModelResource>;
    modelData: ModelManagementData;
    resourceGroup: KnockoutObservableBase<ResourceGroupDropDown.Value>;
    subscription: KnockoutObservableBase<MsPortalFx.Azure.Subscription>;
    workspace: KnockoutObservableBase<Workspace>;
}

function mapModelToItem(models: IModelResource[]): DropDown.Item<IModelResource>[] {
    return models.map(md => {
        return {
            text: md.name,
            value: md
        };
    });
}

export function create(container: MsPortalFx.Base.LifetimeManager, options: IModelPickerOptions): DropDown.Contract<any> {
    const { isNested, model, modelData, resourceGroup, subscription, workspace } = options;

    const loading = ko.observable(false);
    const statusMessage = ko.observable<DropDown.HtmlContent>(null);
    const showStatusMessage = ko.observable(false);
    const modelItems = ko.observableArray<DropDown.Item<IModelResource>>([]);

    workspace.subscribe(container, async ws => {
        if (ws) {
            showStatusMessage(false);
            loading(true);

            try {
                const models = await modelData.getWorkspaceModels(subscription().subscriptionId, resourceGroup().value.name, ws.name);
                modelItems(mapModelToItem(models));
            } catch (e) {
                showStatusMessage(true);

                let errorMessage = e.message;
                if (e.jqXHR && e.jqXHR.responseJSON && e.jqXHR.responseJSON.error) {
                    errorMessage = e.jqXHR.responseJSON.error.message;
                }

                statusMessage({
                    htmlTemplate: "<span class='msportalfx-text-error' data-bind='text: errorMessage'></span>",
                    viewModel: { errorMessage }
                });

                modelItems([]);
            } finally {
                loading(false);
            }
        } else {
            modelItems([]);
        }
    });

    return DropDown.create(container, {
        cssClass: isNested ? "msportalfx-nested-control" : "",
        filter: true,
        infoBalloonContent: "Info balloon content",
        infoBalloonAriaLabel: "Info ballon aria label",
        items: modelItems,
        label: ClientResources.OnlineEndpoints.Create.modelLabel,
        loading,
        placeholder: ClientResources.OnlineEndpoints.Create.modelPlaceholder,
        statusMessage,
        showStatusMessage,
        validations: [new MsPortalFx.ViewModels.RequiredValidation()],
        value: model
    });
}
