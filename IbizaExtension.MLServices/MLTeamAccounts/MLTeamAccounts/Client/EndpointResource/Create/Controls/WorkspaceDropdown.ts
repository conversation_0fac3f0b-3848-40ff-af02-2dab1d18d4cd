import * as DropDown from "Fx/Controls/DropDown";
import * as ResourceGroupDropDown from "Fx/Controls/ResourceGroupDropDown";

import { Workspace } from "../../../MLServicesDataModels/Workspace.types";
import { MachineLearningServicesData } from "../../../Resource/Data/MachineLearningServicesData";

import * as ClientResources from "Resx/ClientResources";

export interface IWorkspaceDropdownOptions {
    machineLearningData: MachineLearningServicesData;
    resourceGroup: KnockoutObservableBase<ResourceGroupDropDown.Value>;
    subscription: KnockoutObservableBase<MsPortalFx.Azure.Subscription>;
    workspace: KnockoutObservableBase<Workspace>;
}

function mapWorkspaceToItem(workspaces: Workspace[]): DropDown.Item<Workspace>[] {
    return workspaces.map(ws => {
        return {
            text: ws.name,
            value: ws
        };
    });
}

export function create(container: MsPortalFx.Base.LifetimeManager, options: IWorkspaceDropdownOptions): DropDown.Contract<Workspace> {
    const { machineLearningData, resourceGroup, subscription, workspace } = options;

    const loading = ko.observable(false);
    const statusMessage = ko.observable<DropDown.HtmlContent>(null);
    const showStatusMessage = ko.observable(false);
    const workspaceItems = ko.observableArray<DropDown.Item<Workspace>>([]);

    async function loadWorkspaces(subscriptionId: string, resourceGroupName: string): Promise<void> {
        showStatusMessage(false);
        loading(true);
        try {
            const workspaces = await machineLearningData.getResourceGroupWorkspaces(subscriptionId, resourceGroupName);
            workspaceItems(mapWorkspaceToItem(workspaces));
        } catch (e) {
            showStatusMessage(true);

            let errorMessage = e.message;
            if (e.jqXHR && e.jqXHR.responseJSON && e.jqXHR.responseJSON.error) {
                errorMessage = e.jqXHR.responseJSON.error.message;
            }

            statusMessage({
                htmlTemplate: "<span class='msportalfx-text-error' data-bind='text: errorMessage'></span>",
                viewModel: { errorMessage }
            });

            workspaceItems([]);
        } finally {
            loading(false);
        }
    }

    subscription.subscribe(container, async subscription => {
        const resourceGroupName = resourceGroup()?.value.name;
        if (resourceGroupName && subscription) {
            await loadWorkspaces(subscription.subscriptionId, resourceGroupName);
        } else {
            workspaceItems([]);
        }
    });

    resourceGroup.subscribe(container, async resourceGroup => {
        const subscriptionId = subscription().subscriptionId;
        if (subscriptionId && resourceGroup) {
            await loadWorkspaces(subscriptionId, resourceGroup.value.name);
        } else {
            workspaceItems([]);
        }
    });

    return DropDown.create(container, {
        filter: true,
        infoBalloonContent: "Info balloon content",
        infoBalloonAriaLabel: "Info ballon aria label",
        items: workspaceItems,
        label: ClientResources.OnlineEndpoints.Create.workspaceLabel,
        loading,
        placeholder: ClientResources.OnlineEndpoints.Create.workspacePlaceholder,
        statusMessage,
        showStatusMessage,
        validations: [new MsPortalFx.ViewModels.RequiredValidation()],
        value: workspace
    });
}
