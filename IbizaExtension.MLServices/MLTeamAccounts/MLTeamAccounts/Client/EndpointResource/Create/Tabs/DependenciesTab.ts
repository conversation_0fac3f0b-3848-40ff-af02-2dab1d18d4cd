import * as FileUpload from "Fx/Controls/FileUpload";
import * as Section from "Fx/Controls/Section";
import * as Validations from "Fx/Controls/Validations";

import { Tab } from "../../../Resource/Create/Models/ControlModels";
import * as NoPdlUiHelpers from "../../../Resource/Create/Utilities/NoPdlUiHelpers";
import { getSasUriCommand } from "../Models/EntryScript";
import { WizardContext } from "../ViewModels/CreateOnlineEndpointModels";

import * as ClientResources from "Resx/ClientResources";

function fileUploadValidator(uploadTasks: FileUpload.AsyncUploadTaskContract[], emptyMessage: string): Q.Promise<Validations.ValidationResult> {
    let message = "";
    let valid = true;
    if (uploadTasks.length > 0) {
        const uploadTask = uploadTasks[0];
        const uploadStatus = uploadTask.uploadInfo.status();
        switch (uploadStatus) {
            case FileUpload.UploadStatus.Pending:
            case FileUpload.UploadStatus.Uploading:
            case FileUpload.UploadStatus.Complete:
                break;

            default:
                message = uploadTask.uploadInfo.errorMessage();
                valid = false;
        }
    } else {
        message = emptyMessage;
        valid = false;
    }

    return Q.resolve({ message, valid });
}

export class DependenciesTab implements Tab {
    private _wizardContext: WizardContext;
    public id = "dependenciesTab";
    public name = ClientResources.OnlineEndpoints.Create.Dependencies.tabName;
    public section: Section.Contract;
    public showNameIfInvalid?: boolean;

    public constructor(wizardContext: WizardContext) {
        this._wizardContext = wizardContext;

        const sectionChildren: any[] = [];
        sectionChildren.push(NoPdlUiHelpers.createInfoSectionHeader(ClientResources.OnlineEndpoints.Create.Dependencies.description));
        sectionChildren.push(...this._createDependencyControls());

        this.section = NoPdlUiHelpers.createTab(wizardContext, this.name, [NoPdlUiHelpers.createSection(wizardContext, null, sectionChildren)]);
    }

    public triggerValidation(): Promise<boolean> {
        return this.section.triggerValidation();
    }

    private _createDependencyControls(): any[] {
        const { container, dataModel } = this._wizardContext;
        const { basics } = dataModel;
        const { condaDependenciesContent, entryScriptUploadTask, workspace } = basics;

        const entryScriptUpload = FileUpload.createBlobStoreUpload(container, {
            label: ClientResources.OnlineEndpoints.Create.Dependencies.entryScriptFileLabel,
            uploadContext: new FileUpload.BlobStoreFileUploadContext({
                contentType: FileUpload.ContentType.Text,
                disableNotifications: true,
                sasUriCommand: ko.observable(getSasUriCommand(workspace()))
            })
        });

        entryScriptUpload.validations([
            new MsPortalFx.ViewModels.CustomValidation("", () =>
                fileUploadValidator(entryScriptUpload.uploadTasks(), ClientResources.OnlineEndpoints.Create.Dependencies.selectEntryScriptFileMessage)
            )
        ]);

        basics.workspace.subscribe(container, newWorkspace => {
            entryScriptUpload.uploadContext().sasUriCommand(getSasUriCommand(newWorkspace));
        });

        entryScriptUpload.uploadTasks.subscribe(container, tasks => {
            entryScriptUploadTask(tasks[0]);
        });

        const condaDependenciesUpload = FileUpload.createFullUpload(container, {
            label: ClientResources.OnlineEndpoints.Create.Dependencies.condaDependenciesFileLabel,
            uploadContext: new FileUpload.FullFileUploadContext({ contentType: FileUpload.ContentType.Text, disableNotifications: true })
        });

        condaDependenciesUpload.validations([
            new MsPortalFx.ViewModels.CustomValidation("", () =>
                fileUploadValidator(
                    condaDependenciesUpload.uploadTasks(),
                    ClientResources.OnlineEndpoints.Create.Dependencies.selectCondaDependenciesFile
                )
            )
        ]);

        condaDependenciesUpload.uploadTasks.subscribe(container, async tasks => {
            const task = tasks[0];
            if (task) {
                const status = task.uploadInfo.status();
                if (status === FileUpload.UploadStatus.Pending) {
                    await task.resume();
                    const bytes = await task.getNextChunk();
                    condaDependenciesContent(bytes.content);
                }
            }
        });

        return [entryScriptUpload, condaDependenciesUpload];
    }
}
