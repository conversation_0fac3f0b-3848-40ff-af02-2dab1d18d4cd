import { Workspace } from "../../../MLServicesDataModels/Workspace.types";

export namespace EntryScriptConstants {
    export const datastoreName = "modelsample";
    export const blobContainer = "localuploads";
    export const pathInContainer = "EntryScripts";
    export const scriptName = "score.py";
    export const codeAssetName = "scoringAsset1";
    export const codeAssetVersion = "1";
}

class SasUriCommand implements MsPortalFx.ViewModels.Commands.Command<{}> {
    private storageAccountId: string;
    private container: string;
    private filePath: string;

    public canExecute: KnockoutObservableBase<boolean>;

    constructor(storageAccountId?: string, container?: string, filePath?: string) {
        this.canExecute = ko.observable(!!this.storageAccountId && !!this.container && !!this.filePath);
        this.storageAccountId = storageAccountId;
        this.container = container;
        this.filePath = filePath;
    }

    public execute(): Promise<unknown> {
        return MsPortalFx.Services.Rpc.invokeCallback("Microsoft_Azure_Storage", "getSasUri", this.storageAccountId, this.container, this.filePath);
    }
}

export function getSasUriCommand(workspace?: Workspace): SasUriCommand {
    return new SasUriCommand(
        workspace?.properties?.storageAccount,
        EntryScriptConstants.blobContainer,
        `${EntryScriptConstants.pathInContainer}/${EntryScriptConstants.scriptName}`
    );
}
