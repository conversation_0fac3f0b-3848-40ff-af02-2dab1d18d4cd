import { BladeLink } from "Fx/Composition";
import * as TemplateBlade from "Fx/Composition/TemplateBlade";
import * as CustomHtml from "Fx/Controls/CustomHtml";
import * as DropDown from "Fx/Controls/DropDown";
import * as TextBox from "Fx/Controls/TextBox";
import * as Toolbar from "Fx/Controls/Toolbar";
import * as ClientResources from "Resx/ClientResources";
import * as Commands from "./DeploymentsCommands";
import { DataContext } from "../../EndpointResourceArea";
import { OnlineDeployment,  DeploymentProvisioningState} from "../../DataModels/DeploymentDataModel";

import Grid = MsPortalFx.ViewModels.Controls.Lists.Grid;
import ResourceTypes = MsPortalFx.ViewModels.Services.ResourceTypes;

const PrivateEndPointsResources = ClientResources.PrivateEndpoints;
const GridResources = PrivateEndPointsResources.Grid;

interface Options {
    id: string;
    traffic: StringMap<string>;
}

interface GridItem {
    id: string;
    name: string;
    location: string;
    deploymentLink: BladeLink;
    traffic: string;
    status: string;
    description: string;
}

export function create(container: TemplateBlade.Container, model: DataContext, options: Options) {
    const deployments = ko.observableArray<OnlineDeployment>();

    const filterTextBox = TextBox.create(container, {
        ariaLabel: PrivateEndPointsResources.filterByName,
        placeHolderText: PrivateEndPointsResources.filterByName,
        suppressDirtyBehavior: true
    });

    const statusDropDown: DropDown.Contract<DeploymentProvisioningState> = DropDown.create<DeploymentProvisioningState>(container, {
        ariaLabel: PrivateEndPointsResources.filterByStatus,
        multiItemDisplayText: ko.pureComputed(() => {
            const selectedStatuses: DeploymentProvisioningState[] = statusDropDown.value() as any;
            const selectedCount = (selectedStatuses || []).length;
            const totalCount = (statusDropDown.items() || []).length;
            return !selectedCount || selectedCount === totalCount
                ? GridResources.StatusFilter.all
                : selectedStatuses.join(", ");
        }),
        suppressDirtyBehavior: true,
        multiselect: true,
        selectAll: true,
        showSearchIcon: true
    });

    ko.pureComputed(() => {
        const statusCounts: StringMap<number> = {};
        deployments().forEach(item => {
            const status = item.properties.provisioningState;
            statusCounts[status] = (statusCounts[status] || 0) + 1;
        });
        const allStates = Object.keys(statusCounts) as DeploymentProvisioningState[];
        MsPortalFx.pushUnique(allStates, []);
        return allStates.map(key => {
            return {
                text: GridResources.StatusFilter.itemFormat.format(
                    (key as DeploymentProvisioningState),
                    statusCounts[key] || 0),
                value: key
            };
        });
    }).subscribeAndRun(container, statusDropDown.items);

    const gridItems = ko.observableArray<GridItem>();
    deployments.filter(container, item => {
        const allowedStatuses: string[] = statusDropDown.value() as any;
        return !(allowedStatuses && allowedStatuses.length) ||
            allowedStatuses.includes(item.properties.provisioningState);
    }).map(container, (itemLifetime, item): GridItem => {
        const deploymentDescriptor = ResourceTypes.parseResourceDescriptor(item.id);
        const deploymentId = item.id;
        const status = item.properties.provisioningState;
        const traffic = !MsPortalFx.isNullOrUndefined(options.traffic) ? options.traffic[item.name] : "0";

        return {
            id: item.id,
            name: deploymentDescriptor.resource,
            location: item.location,
            deploymentLink: {
                bladeReference: new FxImpl.Composition.Selectable.PdlBladeReference<any, void>(
                    "ResourceMenuBlade",
                    "HubsExtension",
                    {
                        parameters: {
                            id: deploymentId
                        }
                    })
            },
            traffic,
            status,
            description: item.properties.description
        };
    }).subscribeAndRun(container, gridItems);

    const grid = new Grid.ViewModel<GridItem, GridItem>(
        container,
        gridItems,
        Grid.Extensions.Filterable | Grid.Extensions.SelectableRow,
        {
            filterable: {
                queryString: filterTextBox.value,
                valueUpdateDelayTimeout: ko.observable(500),
                searchBoxVisible: ko.observable(false)
            },
            selectableRow: {
                selectionMode: Grid.RowSelectionMode.MultipleToggle
            }
        });

    grid.ariaLabel(ClientResources.titleDeployments);
    grid.columns([
        {
            itemKey: "name",
            name: ko.observable(ClientResources.MLAppDeployments.GridColumns.name),
            width: ko.observable("20%"),
            format: Grid.Format.HtmlBindings,
            formatOptions: {
                htmlBindingsTemplate: `<a data-bind="fxclick: settings.item.deploymentLink, text: value"></a>`,
            }
        },
        {
            itemKey: "status",
            name: ko.observable(ClientResources.MLAppDeployments.GridColumns.status),
            width: ko.observable("20%")
        },
        {
            itemKey: "traffic",
            name: ko.observable(ClientResources.MLAppDeployments.GridColumns.traffic),
            width: ko.observable("20%")
        },
    ]);

    const refresh = (model: DataContext) => {
        grid.loading(true);
        return model.endpointData.getEndpointDeployments(options.id).then(items => {
            deployments(items);
        }).finally(() => {
            grid.loading(false);
        });
    };

    const commands = Commands.create(container, model, {
        hasConnection: ko.computed(container, () => gridItems().length > 0),
        selectedItems: grid.selectableData.selectedItems,
        refreshItems: refresh
    });

    const refreshCommand = Toolbar.ToolbarItems.createBasicButton(container, {
        label: ClientResources.PrivateEndpoints.Toolbar.refresh,
        icon: MsPortalFx.Base.Images.Refresh(),
        onClick: () => refresh(model),
        disabled: grid.loading
    });

    const toolbarItems = [
        commands.deleteCommand,
        refreshCommand
    ];

    const toolbar = Toolbar.create(container, {
        items: toolbarItems,
        showLabels: true
    });

    const control = CustomHtml.create(container, {
        htmlTemplate: `
            <div style="display: flex; margin-top: 15px;">
                <div data-bind="pcControl: filterTextBox" style="width: 220px; margin-right: 15px;"></div>
                <div data-bind="pcControl: statusDropDown" style="width: 220px;"></div>
            </div>
            <div data-bind="pcControl: grid"></div>`,
        innerViewModel: {
            toolbar,
            filterTextBox,
            statusDropDown,
            grid
        }
    });

    return {
        control,
        load: refresh,
        toolbar
    };
}
