import * as Dialog from "Fx/Composition/Dialog";
import * as TemplateBlade from "Fx/Composition/TemplateBlade";
import * as TextBox from "Fx/Controls/TextBox";
import * as Toolbar from "Fx/Controls/Toolbar";
import * as ClientResources from "Resx/ClientResources";
import Utilities from "../../../Shared/Utilities";
import { DataContext } from "../../EndpointResourceArea";
import { DeploymentProvisioningState } from "../../DataModels/DeploymentDataModel";

import HubsNotifications = MsPortalFx.Hubs.Notifications;
const CommandResources = ClientResources.PrivateEndpoints.Commands;

export interface ConnectionItem {
    id: string;
    name: string;
    description: string;
    status: string;
}

export interface Options {
    hasConnection: KnockoutComputed<boolean>;
    selectedItems: KnockoutObservableArray<ConnectionItem> | KnockoutReadOnlyObservableArray<ConnectionItem>;
    refreshItems?: (model: DataContext) => void;
}

export interface Contract {
    addCommand: Toolbar.ToolbarItems.BasicButtonContract;
    approveCommand: Toolbar.ToolbarItems.BasicButtonContract;
    rejectCommand: Toolbar.ToolbarItems.BasicButtonContract;
    deleteCommand: Toolbar.ToolbarItems.BasicButtonContract;
}

export function create(container: TemplateBlade.Container, model: DataContext, options: Options) {
    const { selectedItems } = options;
    const refreshItems = options.refreshItems || MsPortalFx.noop;

    const deleteCommand = createCommand(
        container,
        selectedItems,
        "deleteEndpointDeploymentsFromGrid",
        CommandResources.remove,
        MsPortalFx.Base.Images.Delete(),
        CommandResources.Remove,
        selectedItems => {
            trackBatchedQuery(
                selectedItems.map(item => model.endpointData.deleteEndpointDeployment(item.id)),
                CommandResources.DeleteNotifications,
                selectedItems.map(item => item.name)).finally(() => {
                    refreshItems(model);
                });
        },
        false,
        ["Creating", "Succeeded", "Failed", "Scaling"]);

    return {
        deleteCommand
    };
}

function createCommand(
    container: TemplateBlade.Container,
    selectedItemsObs: KnockoutReadOnlyObservableArray<ConnectionItem>,
    telemetryName: string,
    label: string,
    icon: MsPortalFx.Base.Image,
    dialogStrings: {
        title: string;
        messageSingular: string;
        messagePlural: string;
    },
    action: (items: ConnectionItem[], description: string) => void,
    showDescription: boolean,
    allowedStatuses: DeploymentProvisioningState[]
) {
    const allowedStatusesMap = MsPortalFx.convertArrayToMap(allowedStatuses, MsPortalFx.identity);
    return Toolbar.ToolbarItems.createBasicButton(container, {
        label,
        icon,
        disabled: ko.pureComputed(() => !selectedItemsObs().length || selectedItemsObs().some(item => !allowedStatusesMap[item.status])),
        onClick: () => {
            const selectedItems = selectedItemsObs();
            const isMultiple = selectedItems.length > 1;
            const message = isMultiple
                ? dialogStrings.messagePlural.format(selectedItems.length)
                : dialogStrings.messageSingular.format(selectedItems[0].name);
            const descriptionTextBox = TextBox.create(container, {
                label: CommandResources.description,
                value: !isMultiple && selectedItems[0].description || ""
            });
            container.openDialog({
                telemetryName: telemetryName,
                title: dialogStrings.title,
                content: showDescription
                    ? {
                        htmlTemplate: `<div data-bind="text: message" style="margin-bottom: 10px"></div><div data-bind="pcControl: description"></div>`,
                        viewModel: {
                            message,
                            description: descriptionTextBox
                        }
                    } : message,
                buttons: Dialog.DialogButtons.YesNo,
                onClosed: result => {
                    if (result.button === Dialog.DialogButton.Yes) {
                        const description = descriptionTextBox.value();
                        action(selectedItems, description);
                    }
                }
            });
        }
    });
}

export interface BatchMessages {
    // We expect the failure message to contain a {0} and {1} for number of failures / calls respectively.
    // Example, {0} out of {1} calls failed unexpectedly.
    Failure: {
        message: string;
        title: string;
    };
    // We expect the in progress messsage to contain a {0} for number of calls.
    // Example, Attempting to delete {0} storage queues.
    InProgress: {
        message: string;
        title: string;
    };
    // We expect the success message to contain a {0} for the number of calls.
    // Example, Successfully deleted {0} storage queues.
    Success: {
        message: string;
        title: string;
    };
}


function trackBatchedQuery(
    promises: Promise<unknown>[],
    messages: BatchMessages,
    promiseDisplayNames?: string[]): Promise<unknown> {

    // Show in progress to let user know notifications have been kicked off.
    const notification = new HubsNotifications.ClientNotification({
        title: messages.InProgress.title,
        description: messages.InProgress.message.format(promises.length),
        status: HubsNotifications.NotificationStatus.InProgress
    });

    notification.publish();

    // Alert the user if all suceeded or all / some failed
    return Q.allSettled(promises).then((promiseStates) => {
        const failedIndices = promiseStates.map((promiseState, index) => {
            return promiseState.state === "rejected" ? index : -1;
        }).filter(index => index >= 0);

        if (failedIndices.length) {
            notification.title = messages.Failure.title;
            notification.description = messages.Failure.message.format(failedIndices.length, promises.length);
            notification.status = HubsNotifications.NotificationStatus.Error;
            const errors = failedIndices
                .map(index => {
                    let errorMessage: string = Utilities.extractErrorMessage(promiseStates[index].reason.jqXHR);
                    if (errorMessage === ClientResources.PrivateEndpoints.genericErrorMessage) {
                        errorMessage = null;
                    }

                    if (promiseDisplayNames) {
                        const promiseName = promiseDisplayNames[index];
                        errorMessage = errorMessage
                            ? ClientResources.PrivateEndpoints.Create.validationErrorFormat.format(promiseName, errorMessage)
                            : promiseName;
                    }

                    return errorMessage;
                }).filter(error => !!error);
            if (errors.length) {
                notification.description += '\n' + errors.join('\n');
            }
        } else {
            notification.title = messages.Success.title;
            notification.description = messages.Success.message.format(promises.length);
            notification.status = HubsNotifications.NotificationStatus.Success;
        }

        notification.publish();
    });
}
