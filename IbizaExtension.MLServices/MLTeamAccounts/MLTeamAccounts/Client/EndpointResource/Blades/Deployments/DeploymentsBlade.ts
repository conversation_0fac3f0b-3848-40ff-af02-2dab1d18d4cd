import * as TemplateBlade from "Fx/Composition/TemplateBlade";
import * as CustomHtml from "Fx/Controls/CustomHtml";
import * as ClientResources from "Resx/ClientResources";
import * as DeploymentsControl from "./DeploymentsControl";
import { DataContext } from "../../EndpointResourceArea";

import endpointDataModels = require("../../DataModels/OnlineEndpointDataModel");

export interface Parameters {
    id: string;
}

@TemplateBlade.Decorator({
    htmlTemplate:
        `<div class="msportalfx-padding">
            <div data-bind="pcControl: control"></div>
        </div>`
})
@TemplateBlade.InjectableModel.Decorator(DataContext)
export class DeploymentsBlade {
    public title: string;
    public subtitle = ClientResources.PrivateEndpoints.menuText;
    public context: TemplateBlade.Context<Parameters, DataContext>;
    public control: CustomHtml.Contract;
    private _endpointEntityView: MsPortalFx.Data.EntityView<endpointDataModels.OnlineEndpoint, string>;
    private _traffic: StringMap<string> = null;

    public onInitialize() {
        const { container, model, parameters } = this.context;
        const descriptor = MsPortalFx.ViewModels.Services.ResourceTypes.parseResourceDescriptor(parameters.id);
        this.title = descriptor.resource;
        this._endpointEntityView = model.endpointData.onlineEndpointEntity.createView(container);
        
        return this._loadOnlineEndpoint(parameters.id).then(item => {
            var connectionsControl = DeploymentsControl.create(container, model, {
                id: parameters.id,
                traffic: item
            });

            this.control = connectionsControl.control;
            container.commandBar = connectionsControl.toolbar;
            return connectionsControl.load(model);
        });
    }
    
    private _loadOnlineEndpoint(resourceId: string): Q.Promise<StringMap<string>> {
        const deferred = Q.defer<StringMap<string>>();

        const endpointPromise = this._endpointEntityView.fetch(resourceId);

        Q.all([endpointPromise]).then(async () => {
            const onlineEndpoint = this._endpointEntityView.item();
            if (!MsPortalFx.isNullOrUndefined(onlineEndpoint)
                && !MsPortalFx.isNullOrUndefined(onlineEndpoint.properties()))
            {
                deferred.resolve(onlineEndpoint.properties().traffic());
            }
        }, (reason) => {
            deferred.reject(reason);
        });
        return deferred.promise;
    }
}