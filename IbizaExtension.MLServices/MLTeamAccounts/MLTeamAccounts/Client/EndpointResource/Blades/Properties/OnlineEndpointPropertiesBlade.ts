﻿import * as TemplateBlade from "Fx/Composition/TemplateBlade";
import * as ClientResources from "Resx/ClientResources";
import * as Section from "Fx/Controls/Section";
import * as TextBlock from "Fx/Controls/TextBlock";


import { DataContext } from "../../EndpointResourceArea";
import Constants = require("../../../Shared/Constants");

import ExtensionDefinition = require("../../../_generated/ExtensionDefinition");

import { OnlineEndpoint } from "../../DataModels/OnlineEndpointDataModel";

import * as CopyableLabel from "Fx/Controls/CopyableLabel";

/**
 * Contract for parameters that will be passed to parameters blade.
 */
export interface Parameters {
    readonly id: string;
}

@TemplateBlade.Decorator({
    htmlTemplate:
    `
    <div class='msportalfx-padding' data-bind='pcControl: contentSection'></div>
    `,
    styleSheets: ["../../../Shared/Styles/PropertiesStyles.css"],
    isPinnable: false,
    forAsset: { assetType: ExtensionDefinition.AssetTypeNames.onlineEndpoint, assetIdParameter: "id" }
})
@TemplateBlade.InjectableModel.Decorator(DataContext)
export class OnlineEndpointPropertiesBlade  {
    public title = "";
    public subtitle = "";
    public context: TemplateBlade.Context<Parameters, DataContext>;

    public contentSection: Section.Contract;

    private _onlineEndpointEntityView: MsPortalFx.Data.EntityView<OnlineEndpoint, string>;
    private _onlineEndpoint: OnlineEndpoint = null;
    private _onlineEndpointScoringUri: KnockoutObservable<string> = ko.observable<string>("");
    private _onlineEndpointState: KnockoutObservable<string> = ko.observable<string>("");
    private _onlineEndpointSubscriptionName: KnockoutObservable<string> = ko.observable<string>("");
    private _onlineEndpointResourceId: KnockoutObservable<string> = ko.observable<string>("");

    public onInitialize() {
        const { parameters } = this.context;
        this._setInitialState();
        this._initializeContent();
        return this._loadMachineLearningServices(parameters.id);
    }

    private _setInitialState(): void {
        const { model, container } = this.context;
        this._onlineEndpointEntityView = model.endpointData.onlineEndpointEntity.createView(container);
    }

    private _loadMachineLearningServices(resourceId: string): Q.Promise<any> {
        const { container } = this.context;
        const deferred = Q.defer();

        const machineLearningServicesPromise = this._onlineEndpointEntityView.fetch(resourceId);
        const permissionsPromise = MsPortalFx.Base.Security.hasPermission(resourceId, [ExtensionDefinition.AssetTypes.MachineLearningServices.Permissions.readServices]);

        Q.all([permissionsPromise, machineLearningServicesPromise]).then((values) => {
            const hasAccess = values[0];

            this._onlineEndpoint = this._onlineEndpointEntityView.item();
            this._onlineEndpointResourceId(this._onlineEndpoint.id());
            this._onlineEndpointScoringUri(this._onlineEndpoint.properties().scoringUri());
            this._onlineEndpointState(this._onlineEndpoint.properties().provisioningState());

            this.title = this._onlineEndpoint.name();

            if (!hasAccess) {
                container.unauthorized();
            } else {
                deferred.resolve();
            }
        }, (reason) => {
            deferred.reject(reason);
        });
        return deferred.promise;
    }

    // Initializes properties
    private _initializeContent(): void {
        const { container, parameters } = this.context;
        const resourceId = parameters.id;
        const resource = MsPortalFx.ViewModels.Services.ResourceTypes.parseResourceDescriptor(resourceId);

        const resourceIdLabel = this._createCopyableLabel(container, ClientResources.propertiesBladeResourceIdLabel,
            this._onlineEndpointResourceId);

        const subscriptionIdLabelOptions = {
            label: ClientResources.propertiesBladeSubscriptionIdLabel,
            value: ko.computed(container, () => {
                return resource.subscription;
            }),
            readOnly: true,
        };
        const subscriptionIdLabel = CopyableLabel.create(container, subscriptionIdLabelOptions);

        const subscriptionNameLabelOptions = {
            label: ClientResources.propertiesBladeSubscriptionNameLabel,
            value: ko.computed(container, () => {
                resource.subscription
                MsPortalFx.Azure.getSubscriptionInfo(resource.subscription).then(
                    (subscription: MsPortalFx.Azure.Subscription) => {
                        this._onlineEndpointSubscriptionName(subscription ? subscription.displayName : "");
                    });
                return this._onlineEndpointSubscriptionName();
            }),
            readOnly: true,
        };
        const subscriptionNameLabel = CopyableLabel.create(container, subscriptionNameLabelOptions);

        const resourceGroupNameLabelOptions = {
            label: ClientResources.propertiesBladeResourceGroupLabel,
            value: ko.computed(container, () => {
                return resource.resourceGroup;
            }),
            readOnly: true,
        };
        const resourceGroupNameLabel = CopyableLabel.create(container, resourceGroupNameLabelOptions);

        const workspaceNameLabelOptions = {
            label: ClientResources.propertiesBladeWorkspaceIdLabel,
            value: ko.computed(container, () => {
                return resource.resources[0];
            }),
            readOnly: true,
        };
        const workspaceNameLabel = CopyableLabel.create(container, workspaceNameLabelOptions);

        const stateLabelText = this._createTextBlockFromText(container, ClientResources.endpointProvisioningStateLabel)
        const stateLabelValue = this._createTextBlockFromObservable(container, this._onlineEndpointState);
        const scoringUriLabelText = this._createTextBlockFromText(container, ClientResources.endpointScoringUriLabel)
        const scoringUriLabelValue = this._createTextBlockFromObservable(container, this._onlineEndpointScoringUri);

        this.contentSection = Section.create(container,
            {
                children: [
                    this._createSectionFromElements(container, [stateLabelText, stateLabelValue]),
                    this._createSectionFromElements(container, [scoringUriLabelText, scoringUriLabelValue]),
                    this._createSectionFromElements(container, [subscriptionIdLabel]),
                    this._createSectionFromElements(container, [subscriptionNameLabel]),
                    this._createSectionFromElements(container, [resourceGroupNameLabel]),
                    this._createSectionFromElements(container, [workspaceNameLabel]),
                    this._createSectionFromElements(container, [resourceIdLabel]),
                ]
            });
    }

    private _createCopyableLabel(container: TemplateBlade.Container, labelText: string, observableValue: KnockoutObservable<string>): CopyableLabel.Contract {
        const properties = {
            label: labelText,
            value: ko.computed(container, () => {
                return !MsPortalFx.isNullOrUndefined(observableValue()) && observableValue() !== "" ?
                    observableValue() :
                    Constants.pendingStringValue;
            }),
            readOnly: true,
        };

        return CopyableLabel.create(container, properties);
    }

    private _createSectionFromElements(container: TemplateBlade.Container, elements: MsPortalFx.Control[]) {
        return Section.create(container,
            {
                children: elements,
                cssClass: 'ext-caption-text-pair-section'
            })
    }

    private _createTextBlockFromObservable(container: TemplateBlade.Container, observableValue: KnockoutObservable<string>): TextBlock.Contract {
        return TextBlock.create(container, {
            text: ko.computed(container, () => {
                return !MsPortalFx.isNullOrUndefined(observableValue()) && observableValue() !== "" ?
                    observableValue() :
                    Constants.pendingStringValue;
            })
        });
    }

    private _createTextBlockFromText(container: TemplateBlade.Container, textValue: string): TextBlock.Contract {
        return TextBlock.create(container, {
            text: textValue,
        });
    }
}