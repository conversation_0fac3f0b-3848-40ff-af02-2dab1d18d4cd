import { ClickableLink } from "Fx/Composition";
import * as TemplateBlade from "Fx/Composition/TemplateBlade";
import * as CustomHtml from "Fx/Controls/CustomHtml";
import * as Essentials from "Fx/Controls/Essentials";
import { ResourceLayoutContract } from "Fx/Controls/Essentials";
import * as MonitorChartV2 from "Fx/Controls/MonitorChartV2";
import * as Section from "Fx/Controls/Section";
import * as ClientResources from "Resx/ClientResources";
import Card from "../../../Shared/Cards";
import { Logging } from "../../../Shared/Logging";
import { DataContext } from "../../EndpointResourceArea";

import Constants = require("../../../Shared/Constants");
import Icons = require("../../../Shared/Icons");

import ExtensionDefinition = require("../../../_generated/ExtensionDefinition");

import endpointDataModels = require("../../DataModels/OnlineEndpointDataModel");
import Toolbars = MsPortalFx.ViewModels.Toolbars;
import Images = MsPortalFx.Base.Images;


/**
 * Overview blade provides the overview of resource on resource menu.
 * Learn more about decorator based blades at: https://aka.ms/portalfx/nopdl
 */
@TemplateBlade.Decorator({
    htmlTemplate:
        `
    <div data-bind='pcControl: essentialsViewModel'></div>
    <div class='msportalfx-padding'>
        <div class='ext-ml-chart'>
            <div data-bind='pcControl: requestLatencyChart'></div>
        </div>
        <div class='ext-ml-chart'>
            <div data-bind='pcControl: requestsPerMinuteChart'></div>
        </div>
    </div>
    <div class='msportalfx-padding'>
        <div class='msportalfx-form ext-ml-section-header' data-bind='pcControl: infoSection'></div>
        <div class='ext-ml-card'>
            <div data-bind='pcControl: mlsDocumentationCard'></div>
        </div>
        <div class='ext-ml-card'>
            <div data-bind='pcControl: mlsForumCard'></div>
        </div>
    </div>
    `,
    styleSheets: ["../../../Shared/Styles/CustomStyles.css"],
    isPinnable: false,
    forAsset: { assetType: ExtensionDefinition.AssetTypeNames.machineLearningServices, assetIdParameter: "id" },
    forExport: true
})
@TemplateBlade.InjectableModel.Decorator(DataContext)
export class OnlineEndpointOverviewBlade {
    public title = "";
    public subtitle = "";
    public context: TemplateBlade.Context<OnlineEndpointBlade.OnlineEndpointBladeParameters, DataContext>;

    public essentialsViewModel: KnockoutObservable<ResourceLayoutContract>;
    public infoSection: Section.Contract;

    public mlsDocumentationCard: CustomHtml.Contract;
    public mlsForumCard: CustomHtml.Contract;

    // Chart UI elements
    public requestLatencyChart: MonitorChartV2.Contract;
    public requestsPerMinuteChart: MonitorChartV2.Contract;

    private _endpointEntityView: MsPortalFx.Data.EntityView<endpointDataModels.OnlineEndpoint, string>;
    private _onlineEndpoint: endpointDataModels.OnlineEndpoint = null;

    private _downloadCommandButton: MsPortalFx.ViewModels.Toolbars.FileDownloadButton;

    private _scoringUri: Essentials.Item = null;
    private _swaggerUri: Essentials.Item = null;
    private _provisioningState: Essentials.Item = null;
    private _authMode: Essentials.Item = null;

    public onInitialize() {
        const { container, parameters } = this.context;

        this._setInitialState();

        this.infoSection = Section.create(container, {
            name: ClientResources.titleMonitoringLens,
            children: []
        });

        return this._loadOnlineEndpoint(parameters.id);
    }

    private _setInitialState(): void {
        const { model, container } = this.context;
        this._endpointEntityView = model.endpointData.onlineEndpointEntity.createView(container);
    }

    private _loadOnlineEndpoint(resourceId: string): Q.Promise<void> {
        const { container, model } = this.context;
        const deferred = Q.defer();

        const endpointPromise = this._endpointEntityView.fetch(resourceId);
        const permissionsPromise = MsPortalFx.Base.Security.hasPermission(resourceId, [ExtensionDefinition.AssetTypes.MachineLearningServices.Permissions.readServices]);

        Q.all([permissionsPromise, endpointPromise]).then(async (values) => {
            const hasAccess = values[0];

            this._onlineEndpoint = this._endpointEntityView.item();

            this.title = this._onlineEndpoint.name();
            this._initializeCommandBar();
            this._initializeEssentials();
            await this._initializeLinks();

            if (!hasAccess) {
                container.unauthorized();
            } else {
                deferred.resolve();
            }
        }, (reason) => {
            deferred.reject(reason);
        });
        return deferred.promise;
    }

    // Initializes properties for the essentials part
    private _initializeEssentials(): void {
        const { container, parameters } = this.context;
        const resourceId = parameters.id;
        const workspaceId = parameters.id.split("/".concat(Constants.OnlineEndpoints.onlineEndpointsResourceName),1)[0];

        if (!MsPortalFx.isNullOrUndefined(this._onlineEndpoint)
            && !MsPortalFx.isNullOrUndefined(this._onlineEndpoint.properties()))
        {
            this._scoringUri = {
                label: ClientResources.endpointScoringUriLabel,
                value: ko.observable<string>(
                    this._onlineEndpoint.properties().scoringUri() !== ""
                        ? this._onlineEndpoint.properties().scoringUri()
                        : Constants.pendingStringValue
                )
            };

            this._provisioningState = {
                label: ClientResources.endpointProvisioningStateLabel,
                value: ko.observable<string>(
                    this._onlineEndpoint.properties().provisioningState() !== ""
                        ? this._onlineEndpoint.properties().provisioningState()
                        : Constants.pendingStringValue
                )
            };

            this._swaggerUri = {
                label: ClientResources.endpointSwaggerLabel,
                value: ko.observable<string>(
                    this._onlineEndpoint.properties().swaggerUri() !== ""
                        ? this._onlineEndpoint.properties().swaggerUri()
                        : Constants.pendingStringValue
                )
            };

            this._authMode = {
                label: ClientResources.endpointAuthModeLabel,
                value: ko.observable<string>(
                    this._onlineEndpoint.properties().authMode() !== ""
                        ? this._onlineEndpoint.properties().authMode()
                        : Constants.pendingStringValue
                )
            };
        }

        const workspaceProperty = {
            label: ClientResources.onlineEndpointWorkspaceName,
            value: ko.computed(container, () => {
                return !MsPortalFx.isNullOrUndefined(workspaceId) && workspaceId !== "" ?
                    MsPortalFx.ViewModels.Services.ResourceTypes.parseResourceDescriptor(workspaceId).resource :
                    Constants.pendingStringValue;
            }),
            onClick: () => {
                const bladeReference = ko.computed(container, () => {
                    return !MsPortalFx.isNullOrUndefined(workspaceId) && workspaceId !== "" ?
                        MsPortalFx.Assets.mapResourceIdToBladeReference(workspaceId) :
                        null;
                });
                container.openBladeAsync(Q(bladeReference()));
            }
        };

        const leftItems: (Essentials.BuiltInType | Essentials.Item)[] = [
                Essentials.BuiltInType.ResourceGroup,
                Essentials.BuiltInType.Location,
                Essentials.BuiltInType.SubscriptionName,
                Essentials.BuiltInType.SubscriptionId,
                workspaceProperty
            ];

        const essentialsOptions: Essentials.CustomResourceLayoutOptions = {
            resourceId: resourceId,
            left: leftItems,
            right: [
                this._scoringUri,
                this._provisioningState,
                this._swaggerUri,
                this._authMode
            ],
            hiddenChangeLink: [
                Essentials.BuiltInType.ResourceGroup,
                Essentials.BuiltInType.SubscriptionName
            ]
        };

        const essentialsVM = Essentials.createCustomResourceLayout(container, essentialsOptions);
        this.essentialsViewModel = ko.observable(essentialsVM);
    }

    private async _initializeLinks(): Promise<void> {
        const { container, menu, parameters } = this.context;

        const machineLearningServicesDocumentationUrl = MsPortalFx.getEnvironmentValue("machineLearningServicesDocumentationUrl");
        const mlsDocumentationClickableLink = new ClickableLink(machineLearningServicesDocumentationUrl, '_blank', undefined, (keypress) => {
            Logging.LogInfoBoxUrlItemClick("machineLearningServicesDocumentation", machineLearningServicesDocumentationUrl);
        });

        const machineLearningServicesForumUrl = MsPortalFx.getEnvironmentValue("machineLearningServicesForumUrl");
        const mlsForumClickableLink = new ClickableLink(machineLearningServicesForumUrl, '_blank', undefined, (keypress) => {
            Logging.LogInfoBoxUrlItemClick("machineLearningForum", machineLearningServicesForumUrl);
        });

        const requestLatencyMetricOptions: MonitorChartV2.Metric.Options = {
            resourceMetadata: { id: parameters.id },
            name: "RequestLatency",
            aggregationType: MonitorChartV2.Metric.AggregationType.Avg,
        };

        const requestsPerMinuteMetricOptions: MonitorChartV2.Metric.Options = {
            resourceMetadata: { id: parameters.id },
            name: "RequestsPerMinute",
            aggregationType: MonitorChartV2.Metric.AggregationType.Avg,
        };

        this.mlsDocumentationCard = Card.createCard(container, {
            clickableLink: mlsDocumentationClickableLink,
            header: ClientResources.textMachineLearningServicesDocumentationLinkHeader,
            headerAriaLabel: `${ClientResources.quickLinkUnderOverviewBladeAriaLabel} ${ClientResources.textMachineLearningServicesDocumentationLinkHeader}.`,
            body: ClientResources.textMachineLearningServicesDocumentationLinkBody,
            bodyAriaDescription: ClientResources.textMachineLearningServicesDocumentationLinkBody,
            icon: Icons.Icons.documentationIcon
        });
        this.mlsForumCard = Card.createCard(container, {
            clickableLink: mlsForumClickableLink,
            header: ClientResources.textMachineLearningServicesForumLinkHeader,
            headerAriaLabel: `${ClientResources.quickLinkUnderOverviewBladeAriaLabel} ${ClientResources.textMachineLearningServicesForumLinkHeader}.`,
            body: ClientResources.textMachineLearningServicesForumLinkBody,
            bodyAriaDescription: ClientResources.textMachineLearningServicesForumLinkBody,
            icon: Icons.Icons.forumIcon
        });

        defaultChartInputs.metrics = [requestLatencyMetricOptions];
        defaultChartInputs.title = ClientResources.endpointRequestLatencyMetric;
        this.requestLatencyChart = MonitorChartV2.create(container, defaultChartInputs);

        defaultChartInputs.metrics = [requestsPerMinuteMetricOptions];
        defaultChartInputs.title = ClientResources.endpointRequestsPerMinuteMetric;
        this.requestsPerMinuteChart = MonitorChartV2.create(container, defaultChartInputs);
    }

    private _createDownloadConfigCommand(): Toolbars.FileDownloadButton {
        const { parameters } = this.context;

        const resourceDescriptor = MsPortalFx.ViewModels.Services.ResourceTypes.parseResourceDescriptor(parameters.id);
        const workspaceConfig = {
            subscription_id: resourceDescriptor.subscription,
            resource_group: resourceDescriptor.resourceGroup,
            workspace_name: resourceDescriptor.resources[0],
            online_endpoint: resourceDescriptor.resources
        };

        return new Toolbars.FileDownloadButton({
            label: ClientResources.commandDownloadConfig,
            tooltip: ClientResources.commandDownloadConfigTooltip,
            icon: Images.Download(),
            targetUri: ko.observable<string>("data:application/json," + encodeURIComponent(JSON.stringify(workspaceConfig, null, 4))),
            targetFileName: ko.observable<string>("config.json")
        });
    }

    private _initializeCommandBar(): void {
        const { container } = this.context;
        const commandBar = new Toolbars.Toolbar(container);

        this._downloadCommandButton = this._createDownloadConfigCommand();

        commandBar.setItems([
            this._downloadCommandButton,
        ]);
        container.commandBar = commandBar;
    }
}

const defaultChartInputs: MonitorChartV2.Options = {
    metrics: [],
    openBladeOnClick: {       // Specify no click behavior
        openBlade: false,
    },
    timespan: {
        relative: {
            duration: 1 * 24 * 60 * 60 * 1000
        },
    },
};