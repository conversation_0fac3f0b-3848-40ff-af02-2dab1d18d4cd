﻿import { MachineLearningRegistryLifecycleEvents } from "../Shared/Enums"

export interface MachineLearningRegistryServices {
    properties: KnockoutObservable<MachineLearningRegistryProperties>;
    etag: KnockoutObservable<string>;
    id: KnockoutObservable<string>;
    location: KnockoutObservable<string>;
    name: KnockoutObservable<string>;
    type: KnockoutObservable<string>;
}

export interface MachineLearningRegistry {
    properties: MachineLearningRegistryProperties;
    id: string;
    location: string;
    name: string;
    tags: {[key: string] : string};
}

export interface MachineLearningRegistryProperties {
    mlFlowRegistryUri: string;
    managedResourceGroup? : () => {
        resourceId? : () =>  string
    };
}

export interface MachineLearningRegistryServicesExtended extends MachineLearningRegistryServices {
    lifeCycleEvent: KnockoutObservable<MachineLearningRegistryLifecycleEvents>;
}
