﻿/// <amd-bundling generated="typemetadata" />

export interface ResourceBase {
    etag: KnockoutObservable<string>;
    id: KnockoutObservable<string>;
    location: KnockoutObservable<string>;
    name: KnockoutObservable<string>;
    type: KnockoutObservable<string>;
}

export const ResourceBaseMetadata: MsPortalFx.Data.Metadata.Metadata = {
    "name": "Microsoft_Azure_MLTeamAccounts.DataModels.ResourceBase",
    "properties": {
        "etag": {},
        "id": {},
        "location": {},
        "name": {},
        "type": {}
    },
    "idProperties": [
        "id"
    ],
    "entityType": false,
    "hasGloballyUniqueId": true
};

MsPortalFx.Data.Metadata.setTypeMetadata(ResourceBaseMetadata.name, ResourceBaseMetadata);
