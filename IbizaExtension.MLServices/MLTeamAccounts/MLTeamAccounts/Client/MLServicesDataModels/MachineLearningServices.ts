﻿/// <amd-bundling generated="typemetadata" />
/// <amd-dependency path="./MachineLearningServicesProperties" />
import {
    MachineLearningServicesProperties,
    MachineLearningServicesPropertiesMetadata,
    MachineLearningServicesSkuProperties
} from "./MachineLearningServicesProperties";

import { SystemDataIdentityTypeEnum } from "./Common.types";

import { MachineLearningServicesLifecycleEvents } from "../Shared/Enums";

export interface MachineLearningServicesExtended extends MachineLearningServices {
    lifeCycleEvent: KnockoutObservable<MachineLearningServicesLifecycleEvents>;
}

export interface MachineLearningServices {
    properties: KnockoutObservable<MachineLearningServicesProperties>;
    etag: KnockoutObservable<string>;
    id: KnockoutObservable<string>;
    location: KnockoutObservable<string>;
    name: KnockoutObservable<string>;
    type: KnockoutObservable<string>;
    kind?: KnockoutObservable<string>;
    sku: KnockoutObservable<MachineLearningServicesSkuProperties>;
}

export interface MachineLearningServicesSystemDataProperties {
    createdAt?: Date;
    createdBy?: string;
    createdByType?: SystemDataIdentityTypeEnum;
    lastModifiedAt?: Date;
    lastModifiedBy?: string;
    lastModifiedByType?: SystemDataIdentityTypeEnum;
}

export interface UpgradedMachineLearningWorkspace {
    sku: {
        name: string;
        tier: string;
    };
}

export const MachineLearningServicesMetadata: MsPortalFx.Data.Metadata.Metadata = {
    name: "Microsoft_Azure_MLTeamAccounts.DataModels.MachineLearningServices",
    properties: {
        properties: {
            itemType: MachineLearningServicesPropertiesMetadata.name
        },
        etag: {},
        id: {},
        location: {},
        name: {},
        type: {},
        sku: {}
    },
    idProperties: ["id"],
    entityType: true,
    hasGloballyUniqueId: true
};

MsPortalFx.Data.Metadata.setTypeMetadata(MachineLearningServicesMetadata.name, MachineLearningServicesMetadata);
