
export enum ProvisioningState {
    NotStarted = "NotStarted",
    Failed = "Failed",
    Creating = "Creating",
    Updating = "Updating",
    Succeeded = "Succeeded",
    Deleting = "Deleting",
    InProgress = "InProgress",
    Scaling = "Scaling",
}

export enum SystemDataIdentityTypeEnum {
    Application = "Application",
    Key = "Key",
    ManagedIdentity = "ManagedIdentity",
    User = "User"
}

export interface SystemData {
    createdAt?: Date;
    createdBy?: string;
    createdByType?: SystemDataIdentityTypeEnum;
    lastModifiedAt?: Date;
    lastModifiedBy?: string;
    lastModifiedByType?: SystemDataIdentityTypeEnum;
}

export interface ResourceIdentityInArm {
    /**
     *
     * @type {string}
     * @memberof ResourceIdentityInArm
     */
    type?: string;
    /**
     *
     * @type {string}
     * @memberof ResourceIdentityInArm
     */
    principalId?: string;
    /**
     *
     * @type {string}
     * @memberof ResourceIdentityInArm
     */
    tenantId?: string;
    /**
     *
     * @type {{ [key: string]: UserAssignedIdentityMeta; }}
     * @memberof ResourceIdentityInArm
     */
    userAssignedIdentities?: { [key: string]: UserAssignedIdentityMeta };
}

export interface UserAssignedIdentityMeta {
    /**
     *
     * @type {string}
     * @memberof UserAssignedIdentityMeta
     */
    principalId?: string;
    /**
     *
     * @type {string}
     * @memberof UserAssignedIdentityMeta
     */
    clientId?: string;
    /**
     *
     * @type {string}
     * @memberof UserAssignedIdentityMeta
     */
    clientSecretUrl?: string;
    /**
     *
     * @type {string}
     * @memberof UserAssignedIdentityMeta
     */
    resourceId?: string;
    /**
     *
     * @type {string}
     * @memberof UserAssignedIdentityMeta
     */
    tenantId?: string;
}