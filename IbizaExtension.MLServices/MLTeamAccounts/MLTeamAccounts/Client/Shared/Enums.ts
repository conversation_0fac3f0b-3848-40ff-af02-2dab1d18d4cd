﻿export enum EntityDeleteNotificationEvents {
    None,
    Deleting,
    Deleted,
    DeleteFailed
}

export enum MachineLearningServicesLifecycleEvents {
    None,
    DeleteWorkspaceExecuting,
    DeleteWorkspaceError,
    DeleteWorkspaceSucceeded
}

export enum SKUTypes {
    Basic = "Basic",
    Enterprise = "Enterprise"
}

export enum MachineLearningRegistryLifecycleEvents {
    None,
    DeleteRegistryExecuting,
    DeleteRegistryError,
    DeleteRegistrySucceeded
}

/**
 * The machine learning services ARM resource has multiple "kinds".
 * Default for the "full feature" workspace resource kind.
 * Project for workspaces that are the "spokes" in the "hub and spoke" design for workspaces. Holds individual resources, to be shared in hubs.
 * Hub for workspaces that are the "hub" in the "hub and spoke" design for workspaces. Links to, and shares resources for, "Project" workspaces.
 * FeatureStore for specialized workspaces used to share features and that have limited access to other workspace features like compute.
 */
export enum MachineLearningServicesKind {
    Default = "Default",
    Project = "Project",
    Lean = "Lean", // Aka Project
    Hub = "Hub",
    FeatureStore = "FeatureStore"
}

/**
 * Internal kind reference to use, derived from the raw kind value of the workspace
 * Lean -> Project,
 * Project -> Project,
 * Hub -> Hub,
 * Default -> Default,
 * \* -> Default
 */
export enum WorkspaceMappedKind {
    Default = "Default",
    Project = "Project",
    Hub = "Hub",
    FeatureStore = "FeatureStore"
}
