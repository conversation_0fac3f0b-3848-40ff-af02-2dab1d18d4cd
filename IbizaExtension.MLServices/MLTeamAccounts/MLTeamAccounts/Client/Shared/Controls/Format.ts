import * as CustomHtml from "Fx/Controls/CustomHtml";

export interface IFormatOptions {
    children?: CustomHtml.Options[];
    format: string;
}

export function create(lifetimeManager: MsPortalFx.Base.LifetimeManager, options: IFormatOptions): CustomHtml.Contract {
    const { children = [], format } = options;

    const htmlTemplateParts: string[] = [];
    let finalViewModel: any = {};
    if (format) {
        // This splits the format string into a sequence of [string,item#,string,item#,string]
        // For example, if format is "a {0} b {2} c {1} d", the split output will be ["a ", "0", " b ", "2", " c ", "1", " d"]
        const formatSegments = format.split(/(?:\{)([0-9]+)(?:\})/);

        // This is used to check the recurring indices
        const indicesUsed = new Map<number, boolean>();

        for (let segmentIndex = 0; segmentIndex < formatSegments.length; ++segmentIndex) {
            const segment = formatSegments[segmentIndex];

            // Every other entry is either a format index or a string. The first and last are strings; odd entries will be indices.
            if (segmentIndex & 1) {
                const childIndex = parseInt(segment, 10);

                // Unlike string.format(), we don't let you use the same index more than once. If we let you use a format string
                // like "foo {0} bar {0} baz {0}...", the same child component instance would appear at multiple places in the DOM.
                // That would be bad. Throw an error to let the consumer know if an index is specified more than once.
                if (!indicesUsed.has(childIndex)) {
                    const childElement = children[childIndex];

                    // Make sure a child element exists for the specified index in the format
                    if (!childElement) {
                        throw new Error(`No element found for the specified index ${childIndex}`);
                    }

                    htmlTemplateParts.push(childElement.htmlTemplate);
                    finalViewModel = { ...finalViewModel, ...childElement.innerViewModel };
                    indicesUsed.set(childIndex, true);
                } else {
                    // An index in the format is specified more than once, let the consumer know by throwing an error
                    throw new Error(`Index in the format can only be used once, recurring index is ${childIndex}`);
                }
            } else {
                htmlTemplateParts.push(segment);
            }
        }
    }

    return CustomHtml.create(lifetimeManager, {
        htmlTemplate: htmlTemplateParts.join(""),
        innerViewModel: finalViewModel
    });
}
