﻿/**
 * Contains common commands to be used in template blade
 */
"use strict";

import * as ClientResources from "Resx/ClientResources";

import FxToolbars = MsPortalFx.ViewModels.Toolbars;
import FxImages = MsPortalFx.Base.Images;

export class RefreshCommandButton extends FxToolbars.CommandButton<any> {
    constructor(callback: () => Q.Promise<any> | Promise<any>) {
        super();

        this.label(ClientResources.labelCommandButtonRefresh);
        this.ariaLabel(ClientResources.labelCommandButtonRefresh);
        this.icon(FxImages.Refresh());
        this.commandContext({});

        this.command = {
            canExecute: ko.observable(true),
            execute: (context: any): Promise<unknown> => {
                this.disabled(true);
                this.icon(FxImages.Loading.Spinner());

                return callback().finally(() => {
                    this.disabled(false);
                    this.icon(FxImages.Refresh());
                });
            }
        };
    }
}
