export const sdkResourceProvider = "Microsoft.PortalSdk";
export const rootResource = "rootResources";

export const machineLearningStorageResourceProvider = "Microsoft.Storage";
export const machineLearningServicesResourcesProvider = "Microsoft.MachineLearningServices";
export const machineLearningWorkspaces = "workspaces";
export const machineLearningWorkspaceType = `${machineLearningServicesResourcesProvider}/${machineLearningWorkspaces}`;
export const machineLearningRegistries = "registries";
export const machineLearningRegistryType = `${machineLearningServicesResourcesProvider}/${machineLearningRegistries}`;
export const cogServicesProvider = "Microsoft.CognitiveServices";
export const cogServicesAccounts = "accounts";
export const storage = "storageAccounts";
export const machineLearningContainerRegistryProvider = "Microsoft.ContainerRegistry";
export const containerRegistry = "registries";
export const machineLearningKeyVaultProvider = "Microsoft.KeyVault";
export const databricksWorkspaces = "workspaces";
export const keyVault = "vaults";
export const machineLearningAppInsightsProvider = "Microsoft.Insights";
export const machineLearningComputeResourceProvider = "Microsoft.Compute";
export const databricksResourceProvider = "Microsoft.Databricks";

export const machineLearningWorkspacesComputeResourceType = "workspaces/computes";

export const appInsights = "components";
export const validMachineLearningServicesWorkspaceRegexStr = "^[a-zA-Z0-9][\\w\\-]{2,32}$";
export const storageAccountTier = "Standard_LRS";

export const newline = "\n";
export const emptyString = "";
export const pendingStringValue = "...";
export const registeredString = "Registered";
export const registeringString = "Registering";

export const armPollingDelayDefault = 30; // seconds

/**
 * Duration (milliseconds) of blade contentState (success/error completion) highlight presence before it disappears.
 * The short duration to be used for informational events, that are not duplicated with the notifications.
 */
export const notificationHighlightDuration = 3000;
export const bladeFinalContentStateHighlightDurationShort = notificationHighlightDuration / 3;

export const containerRegistryTiers = {
    Basic: "Basic",
    Standard: "Standard",
    Premium: "Premium"
};

/*
For workspace creation, this map contains keys for workspace region to application insights region value mapping.
If application insights is not available in given key region, the mapped region will be used.
*/
export const appInsightsRegionUnavailabilityMap = {
    eastus2euap: "eastus",
    centraluseuap: "eastus",
    westcentralus: "westus2",
    chinanorth: "chinaeast2"
};

export const appInsightsKind = {
    web: "web"
};

export const storageAccountKind = {
    Storage: "Storage",
    StorageV2: "StorageV2"
};

export const appInsightsType = {
    web: "web"
};

export namespace Links {
    export const learnMoreConsistency = "https://aka.ms/portalfx/designpatterns";
    export const learnMorePortalDocs = "https://aka.ms/portalfx/browse";
    export const esentialsAdditionalRightLink1 = "http://www.bing.com";
}

// menu group IDs must be unique, must not be localized, should not contain spaces and should be lowercase
// for the standard menus - use the constants defined in MsPortalFx.Assets naming convention is <name>GroupId, like MsPortalFx.Assets.SupportGroupId
export namespace ResourceMenuGroupIds {
    export const resourceSpecificGroup = "myresourcespecific_group";
}

// menu IDs must be unique, must not be localized, should not contain spaces and should be lowercase
// for the standard menu items - use the constants defined in MsPortalFx.Assets naming convention is <name>ItemId, like MsPortalFx.Assets.PropertiesItemId
export namespace ResourceMenuBladeIds {
    export const overview = "overview";
    export const mRSGItem1 = "mrsg_item1";
    export const mRSGItem2 = "mrsg_item2";
    export const mRSGSettingItem1 = "mrsg_settings_item1";
}

export const headers = {
    xmsRequestId: "x-ms-request-id"
};

export const dateFormatWithTime = {
    year: "numeric",
    month: "numeric",
    day: "numeric",
    hour: "numeric",
    minute: "numeric"
};

export const MenuItemIds = {
    properties: "properties",
    quotaUsage: "quotaUsage",
    networkSettings: "networkSettings",
    managedIdentities: "managedIdentities",
    security: "security",
    projects: "projects",
    encryption: "encryption",
    keysAndEndpoints: "keysAndEndpoints",
    insights: "insights"
};

export namespace StorageAccount {
    export const nameRegex = "^[a-z0-9]{3,24}$";
}

export namespace KeyVault {
    export const nameRegex = "^[A-Za-z]([A-Za-z0-9]|-[A-Za-z0-9])+$";
    export const nameMinLength = 3;
    export const nameMaxLength = 24;
}

export namespace AppInsights {
    export const invalidCharsRegex = /[*;/?:@&=+$,<>#%\ "{}|^'`\\\[\]]/g;
    export const nameMinLength = 1;
    export const nameMaxLength = 255;
}

export namespace ContainerRegistry {
    export const nameRegex = "^[A-Za-z0-9]{5,50}$";
}

export namespace OnlineEndpoints {
    export const onlineEndpointsResourceName = "onlineEndpoints";
    export const onlineEndpointsResourceType = `${machineLearningServicesResourcesProvider}/${machineLearningWorkspaces}/${onlineEndpointsResourceName}`;
    export const deploymentSourceName = "CreateOnlineEndpointBlade";
    export const deploymentsResourceName = "deployments";
}

export namespace WorkspaceOutboundRules {
    export const AMLWorkspacesResourceType = "Microsoft.MachineLearningServices/workspaces";
    export const AMLRegistryResourceType = "Microsoft.MachineLearningServices/registries";
    export const KeyVaultResourceType = "Microsoft.KeyVault/vaults";
    export const StorageResourceType = "Microsoft.Storage/storageAccounts";
    export const ContainerRegistryResourceType = "Microsoft.ContainerRegistry/registries";
    export const CognitiveServicesResourceType = "Microsoft.CognitiveServices/accounts";
    export const SqlResourceType = "Microsoft.Sql/servers";
    export const DataFactoryResourceType = "Microsoft.DataFactory/factories";
    export const CosmosDBResourceType = "Microsoft.AzureCosmosDB/databaseAccounts";
    export const DocumentDBResourceType = "Microsoft.DocumentDB/databaseAccounts";
    export const EventHubResourceType = "Microsoft.EventHub/namespaces";
    export const RedisCacheResourceType = "Microsoft.Cache/Redis";
    export const DatabricksResourceType = "Microsoft.Databricks/workspaces";
    export const MariaDBResourceType = "Microsoft.DBforMariaDB/servers";
    export const MySQLResourceType = "Microsoft.DBforMySQL/servers";
    export const MySQLFlexibleServerResourceType = "Microsoft.DBforMySQL/flexibleServers";
    export const PostgreSQLSingleServerResourceType = "Microsoft.DBforPostgreSQL/servers";
    export const PostgreSQLFlexibleServerResourceType = "Microsoft.DBforPostgreSQL/flexibleServers";
    export const PostgreSeverGroupsV2ResourceType = "Microsoft.DBforPostgreSQL/serverGroupsv2";
    export const PurviewResourceType = "Microsoft.Purview/accounts";
    export const SearchResourceType = "Microsoft.Search/searchServices";
    export const ApiManagementResourceType = "Microsoft.ApiManagement/service";
    export const AzureAppServiceResourceType = "Microsoft.Web/sites";
    export const AzureStaticWebAppsResourceType = "Microsoft.Web/staticSites";
    export const AzureMonitorPrivateLinkScopeResourceType = "Microsoft.Insights/privateLinkScopes";
    export const ApplicationGatewayResourceType = "Microsoft.Network/applicationGateways";

    export const AllowedResourceTypes: string[] = [
        AMLWorkspacesResourceType,
        AMLRegistryResourceType,
        KeyVaultResourceType,
        StorageResourceType,
        ContainerRegistryResourceType,
        CognitiveServicesResourceType,
        SqlResourceType,
        DataFactoryResourceType,
        CosmosDBResourceType,
        DocumentDBResourceType,
        EventHubResourceType,
        RedisCacheResourceType,
        DatabricksResourceType,
        MariaDBResourceType,
        MySQLResourceType,
        MySQLFlexibleServerResourceType,
        PostgreSQLSingleServerResourceType,
        PostgreSQLFlexibleServerResourceType,
        PostgreSeverGroupsV2ResourceType,
        PurviewResourceType,
        SearchResourceType,
        ApiManagementResourceType,
        AzureAppServiceResourceType,
        AzureStaticWebAppsResourceType,
        AzureMonitorPrivateLinkScopeResourceType,
        ApplicationGatewayResourceType
    ];

    export const ResourceTypeSubResourcesMapping: Record<string, string[]> = {
        [AMLWorkspacesResourceType]: ["amlworkspace"],
        [AMLRegistryResourceType]: ["amlregistry"],
        [KeyVaultResourceType]: ["vault"],
        [StorageResourceType]: ["blob", "table", "queue", "file", "web", "dfs"],
        [ContainerRegistryResourceType]: ["registry"],
        [CognitiveServicesResourceType]: ["account"],
        [SqlResourceType]: ["sqlServer"],
        [DataFactoryResourceType]: ["dataFactory"],
        [CosmosDBResourceType]: ["Sql", "MongoDB", "Cassandra", "Gremlin", "Table"],
        [DocumentDBResourceType]: ["Sql", "MongoDB", "Cassandra", "Gremlin", "Table"],
        [EventHubResourceType]: ["namespace"],
        [RedisCacheResourceType]: ["redisCache"],
        [DatabricksResourceType]: ["databricks_ui_api", "browser_authentication"],
        [MariaDBResourceType]: ["mariadbServer"],
        [MySQLResourceType]: ["mysqlServer"],
        [MySQLFlexibleServerResourceType]: ["mysqlServer"],
        [PostgreSQLSingleServerResourceType]: ["postgresqlServer"],
        [PostgreSQLFlexibleServerResourceType]: ["postgresqlServer"],
        [PostgreSeverGroupsV2ResourceType]: ["coordinator"],
        [PurviewResourceType]: ["account", "portal"],
        [SearchResourceType]: ["searchService"],
        [ApiManagementResourceType]: ["Gateway"],
        [AzureAppServiceResourceType]: ["sites"],
        [AzureStaticWebAppsResourceType]: ["staticSites"],
        [AzureMonitorPrivateLinkScopeResourceType]: ["azuremonitor"]
    };

    export const ServiceTagProtocols = ["Any", "TCP", "UDP", "ICMP"];

    export const OutboundServiceTags: string[] = [
        "AppConfiguration",
        "AppService",
        "AzureActiveDirectory",
        "AzureAdvancedThreatProtection",
        "AzureArcInfrastructure",
        "AzureAttestation",
        "AzureBackup",
        "AzureBotService",
        "AzureContainerRegistry",
        "AzureCosmosDB",
        "AzureDataLake",
        "AzureDevSpaces",
        "AzureInformationProtection",
        "AzureIoTHub",
        "AzureKeyVault",
        "AzureManagedGrafana",
        "AzureMonitor",
        "AzureOpenDatasets",
        "AzurePlatformDNS",
        "AzurePlatformIMDS",
        "AzurePlatformLKM",
        "AzureResourceManager",
        "AzureSignalR",
        "AzureSiteRecovery",
        "AzureSpringCloud",
        "AzureStack",
        "AzureUpdateDelivery",
        "DataFactoryManagement",
        "EventHub",
        "GuestAndHybridManagement",
        "M365ManagementActivityApi",
        "MicrosoftAzureFluidRelay",
        "MicrosoftCloudAppSecurity",
        "MicrosoftContainerRegistry",
        "PowerPlatformInfra",
        "ServiceBus",
        "Sql",
        "Storage",
        "WindowsAdminCenter",
        "AppServiceManagement",
        "AutonomousDevelopmentPlatform",
        "AzureActiveDirectoryDomainServices",
        "AzureCloud",
        "AzureConnectors",
        "AzureContainerAppsService",
        "AzureDatabricks",
        "AzureDeviceUpdate",
        "AzureEventGrid",
        "AzureFrontDoor.Frontend",
        "AzureFrontDoor.Backend",
        "AzureFrontDoor.FirstParty",
        "AzureHealthcareAPIs",
        "AzureLoadBalancer",
        "AzureMachineLearning",
        "AzureSphere",
        "AzureWebPubSub",
        "BatchNodeManagement",
        "ChaosStudio",
        "CognitiveServicesFrontend",
        "CognitiveServicesManagement",
        "DataFactory",
        "Dynamics365ForMarketingEmail",
        "Dynamics365BusinessCentral",
        "EOPExternalPublishedIPs",
        "Internet",
        "LogicApps",
        "Marketplace",
        "MicrosoftDefenderForEndpoint",
        "PowerBI",
        "PowerQueryOnline",
        "ServiceFabric",
        "SqlManagement",
        "StorageSyncService",
        "WindowsVirtualDesktop",
        "VirtualNetwork"
    ];
}
