﻿.ext-ml-narrow-width {
    width: 220px;
}

.ext-ml-infobox {
    width: 46%;
    display: inline-block;
    padding: 0px 10px 0px 10px;
    vertical-align: top;
}

/*
    Special style for the SelectionCards component which extends the Fx/Controls/RadioButton component.
    These styles transform the horizontal radio button list into a three column wide row of cards.
*/
.ext-ml-selection-cards > * > * > * > ul {
    display: flex;
    grid-gap: 10px;
    flex-flow: row wrap;
}

/*
    Remove inline table style from radio button li element so the child element can grow to fill the row height
*/
.ext-ml-selection-cards > * > * > * > ul > li {
    display: unset;
    flex: 1 1 auto;
    flex-basis: min-content;
    margin: 0;
}

/*
    Targets the visual circle representing the radio button input state.
    Moves the radio button to the top right of the card.
*/
.ext-ml-selection-cards > * > * > * > ul > li > span:nth-child(2) {
    left: unset;
    top: 14px;
    right: 9px;
}

/*
    Targets the custom text html template of the radio button.
    Removes padding that used to be reserved for radio button circle,
    and use display block + height 100 to fill row height.
 */
.ext-ml-selection-cards > * > * > * > ul > li > span:nth-child(3) {
    display: block;
    height: 100%;
    padding-left: 0;
}

/*
    Need to recreate the ul/li styling without using ul/li tags because the Fx/Controls/RadioButton
    will bind event listeners to any ul/li child elements within itself.
    Since these wouldn't be the ul/li it's expecting it would throw an error.
*/
.ext-ml-selection-cards_ul {
    margin: 5px 0;
    padding-left: 1rem;
}

.ext-ml-selection-cards_li {
    margin-bottom: 5px;
}

.ext-ml-selection-cards_li:before {
    /* unicode for dot */
    content: "\22C5";
    position: absolute;
    margin-left: -0.7rem;
    font-size: 24px;
}

.ext-ml-card {
    width: 46%;
    display: inline-block;
    padding: 0px 10px;
    vertical-align: top;
}

.ext-ml-card .ext-ml-card-container {
    display: flex;
    margin: 19px 0px;
    border: 1px solid;
    cursor: pointer;
}
/** Styling for Icon**/
.ext-ml-card .ext-ml-card-icon-container {
    display: flex;
    min-width: 50px;
}

.ext-ml-card .ext-ml-card-icon {
    width: 36px;
    height: 36px;
    margin: auto auto;
}

.ext-ml-card .ext-ml-card-container .ext-ml-card-text {
    display: block;
    border-left: solid 1px;
    padding: 10px 10px 10px 14px;
    width: 100%;
    overflow: hidden;
    font-size: 11px;
    line-height: 15px;
    min-height: 60px;
}

.ext-ml-card .ext-ml-card-container .ext-ml-card-text .ext-ml-card-header {
    font-size: 15px;
    font-weight: bold;
    margin-top: 5px;
    margin-bottom: 10px;
}

.ext-ml-card .ext-ml-card-hyperlink-icon {
    width: 12px;
    height: 12px;
    right: 10px;
    margin-top: 7px;
    position: relative;
}

.ext-ml-card-container {
    border-color: #CCCCCC;
}

.ext-mode-dark .ext-ml-card-container {
    border-color: #605E5C;
}

.ext-ml-card .ext-ml-card-text {
    border-color: #CCCCCC;
}

.ext-mode-dark .ext-ml-card .ext-ml-card-text {
    border-color: #605E5C;
}
/** Handle Hover highlighting: light -> high contrast -> dark -> dark high contrast **/
.ext-mode-light .ext-ml-card .ext-ml-card-container:hover,
.ext-mode-light .ext-ml-card .ext-ml-card-container:focus {
    background-color: rgba(85, 179, 255, 0.1);
}

.ext-mode-highcontrast .ext-ml-card .ext-ml-card-container:hover,
.ext-mode-highcontrast .ext-ml-card .ext-ml-card-container:focus,
.ext-mode-highcontrast .ext-ml-card .ext-ml-card-container:hover .ext-ml-hyperlink,
.ext-mode-highcontrast .ext-ml-card .ext-ml-card-container:focus .ext-ml-hyperlink {
    color: #fff;
    fill: #fff;
    background-color: purple;
}

.ext-mode-dark .ext-ml-card .ext-ml-card-container:hover,
.ext-mode-dark .ext-ml-card .ext-ml-card-container:focus {
    background-color: rgba(128,128,128,.3);
}

.ext-highcontrast-dark .ext-ml-card .ext-ml-card-container:hover,
.ext-highcontrast-dark .ext-ml-card .ext-ml-card-container:focus,
.ext-highcontrast-dark .ext-ml-card .ext-ml-card-container:hover .ext-ml-hyperlink,
.ext-highcontrast-dark .ext-ml-card .ext-ml-card-container:focus .ext-ml-hyperlink {
    color: #000;
    fill: #000;
    background-color: #0ff;
}

.ext-ml-infobox-large {
    width: 94%;
    display: inline-block;
    padding: 0px 10px 0px 10px;
    vertical-align: top;
}

.ext-ml-infobox-header {
    font-size: 15px;
    font-weight: bold;
    margin-top: 5px;
    margin-bottom: 10px
}

.ext-ml-section-header {
    font-size: 18px;
    font-weight: bold;
}

.ext-ml-numbered-section code{
    padding-left: 20px;
    display: block;
}

.ext-ml-left-margin {
    margin-left: 35%;
    margin-top: 20px;
}

.ext-ml-ws2-banner-container {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-flow: row nowrap;
    padding: 24px 0;
    width: 100%;
}

.ext-ml-ws2-banner {
    display: flex;
    flex-flow: row nowrap;
    color: black;
    text-align: left;
    justify-content: flex-start;
    align-items: center;
    box-sizing: border-box;
    max-width: 874px;
    width: 90%;
    min-height: 204px;
    border: 1px solid #E2E2E2;
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
    overflow: hidden;
}

.ext-mode-dark .ext-ml-ws2-banner {
    color: #FFF;
    background-color: #111;
    border: 2px solid #666;
    box-shadow: none;
}

.ext-highcontrast-dark .ext-ml-ws2-banner {
    color: #FF0;
    background-color: #000;
    border: 2px solid #FF0;
    box-shadow: none;
}

.ext-highcontrast-light .ext-ml-ws2-banner {
    color: #000;
    background-color: #FFF;
    border: 2px solid #000;
    box-shadow: none;
}

.ext-ml-ws2-banner-icon {
    height: 100px;
    width: 100px;
    padding-top: 20px;
    padding-bottom: 20px;
    opacity: 0.5;
}

.ext-ml-ws2-banner-content {
    flex-shrink: 0;
    flex-grow: 0;
    max-width: 565px;
    width: 100%;
    padding: 10px;
}

.ext-ml-ws2-notice-icon {
    margin: 8px 18px 0 8px;
    padding-top: 4px;
}

.ext-ml-ws2-banner-title {
  font-size: 24px;
  margin-bottom: 24px;
  font-weight: 700;
}

.ext-ml-ws2-banner-body {
  font-size: 18px;
  line-height: 21px;
  margin-bottom: 18px;
}

.ext-ml-ws2-banner-buttons {
  justify-content: flex-start;
  align-items: center;
  font-size: 13px;
  display: flex;
}

.ext-ml-ws2-banner-button {
  margin-right: 18px;
}


.ext-ml-ws2-banner-link {
  color: #0078D4;
  flex-grow: 1;
}

.ext-highcontrast-dark .ext-ml-ws2-banner-link {
  color: #FF0;
}

.ext-highcontrast-dark .ext-ml-ws2-banner-link:hover,
.ext-highcontrast-dark .ext-ml-ws2-banner-link:focus {
    color: #0FF;
}
.ext-ml-ws2-banner-link-icon {
  width: 12px;
  height: 12px;
  border: none;
  margin-left: 0.5em;
  vertical-align: baseline;
  display: inline-block;
}

.ext-highcontrast-dark .ext-ml-ws2-banner-link-icon {
    display: none;  /* suppress this ornament that will be the wrong color anyway during high-C dark mode */
}


.ext-ml-ws2-notice-outer {
  display: flex;
  padding-bottom: 32px;
  width: 100%;
}

.ext-ml-ws2-notice-middle {
  background:#F0F6FF;
  display: flex;
  overflow: hidden;
  padding: 8px;
}


.ext-mode-dark .ext-ml-ws2-notice-middle {
  border: 1px solid #0089fa;
  background: #000;
}

.ext-highcontrast-dark .ext-ml-ws2-notice-middle {
  border: 2px solid #ffff00;
  background: #000;
}

.ext-highcontrast-light .ext-ml-ws2-notice-middle {
  border: 2px solid #000;
  background: #fff;
}

.ext-ml-full-width {
    width: 100%;
}


.ext-ml-ws2-notice-inner {
  display: flex;
  flex-flow: column nowrap;
  width: 100%;
}

.ext-ml-ws2-notice-message {
  font-size: 12px;
  margin: 8px 8px 12px 0;
}

.ext-mode-dark .ext-ml-ws2-notice-message {
  color: #fff;
}

.ext-highcontrast-dark .ext-ml-ws2-notice-message {
  color: #ff0;
}

.ext-ml-ws2-notice-icon {
    margin: 8px 18px 0 8px;
    padding-top: 4px;
    width: 30px;
    height: 30px;
}

.ext-ml-ws2-notice-icon-container {
    fill: #015cda;
}

.ext-ml-ws2-notice-icon-content {
    fill: #fff;
}

.ext-mode-dark .ext-ml-ws2-notice-icon-container {
    fill: #0089fa;
}

.ext-highcontrast-dark .ext-ml-ws2-notice-icon-container {
    fill: #ff0;
}

.ext-highcontrast-dark .ext-ml-ws2-notice-icon-content {
    fill: #000;
}

.ext-ml-ws2-notice-launch-button {
  margin-bottom: 4px;
  padding: 4px 8px;
  background-color:#015CDA;
  border: none;
  font-size: 11px;
  font-weight: 700;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #FFF;
  width: 115px;
  cursor: pointer;
}

.ext-mode-dark .ext-ml-ws2-notice-launch-button {
  background-color: #0089fa;
  color: #FFF;
}

.ext-highcontrast-dark .ext-ml-ws2-notice-launch-button {
  background-color: #FF0;
  color: #000;
}

.ext-highcontrast-light .ext-ml-ws2-notice-launch-button {
  background-color: #000;
  color: #FFF;
}

.ext-ml-ws2-notice-close-icon {
    width: 14px;
    height: 14px;
    cursor: pointer;
}

.ext-ml-ws2-notice-pricing-link {
    text-decoration: underline;
    color: inherit;
}

.ext-ml-ws2-new-banner-container, .ext-ml-quota-link-container {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-flow: row nowrap;
    padding: 24px 0;
    width: 100%;
}

.ext-ml-ws2-new-banner, .ext-ml-quota-link-banner {
    display: flex;
    flex-flow: column;
    justify-content: center;
    align-items: center;
    box-sizing: border-box;
    overflow: hidden;
}

.ext-ml-ws2-new-banner-title, .ext-ml-quota-link-title {
    font-size: 20px;
    font-weight: 600;
    line-height: 28px;
    margin-bottom: 8px;
    margin-top: 16px;
}

.ext-ml-ws2-new-banner-description, .ext-ml-quota-link-description {
    max-width: 720px;
    text-align: center;
}

.ext-ml-ws2-new-banner-button, .ext-ml-quota-link-button {
    margin-top: 14px;
    margin-bottom: 10px;
}

.ext-ml-ws2-new-banner-link-icon {
    width: 12px;
    height: 12px;
    border: none;
    margin-left: 4px;
    vertical-align: baseline;
    display: inline-block;
}

.ext-associated-resource-settings {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.ext-associated-resource-settings-form {
    flex-grow: 1;
}

.ext-associated-resource-settings-buttons > * {
    margin-right: 8px;
}

.ext-ml-chart {
    width: 46%;
    display: inline-block;
    padding: 0px 10px;
    vertical-align: top;
}

.ext-ml-chart .ext-ml-chart-container {
    border-width: 1px;
    border-style: solid;
    display: flex;
    margin: 19px 0px;
    cursor: pointer;
}

.ext-ml-chart .ext-ml-chart-container .ext-ml-chart-text {
    display: block;
    border-left: solid 1px;
    padding: 10px 10px 10px 14px;
    width: 100%;
    overflow: hidden;
    font-size: 11px;
    line-height: 15px;
    min-height: 60px;
}

.ext-ml-chart .ext-ml-chart-container .ext-ml-chart-text .ext-ml-chart-header {
    font-size: 15px;
    font-weight: bold;
    margin-top: 5px;
    margin-bottom: 10px;
}

/*
CSS layout utilities

references:
Azure Portal SDK Toolkit https://www.figma.com/file/Bwn8rmUOYtnPRwA3JoQTBn/Azure-Portal-SDK-Toolkit?node-id=3383-393118&t=IzglPIbUXYsoIlK2-0
https://github.com/Azure/portaldocs/blob/main/portal-sdk/generated/top-design-responsive.md#fixing-content-for-responsiveness
https://msazure.visualstudio.com/One/_git/AzureUX-PortalFx?path=%2Fsrc%2FSDK%2FWebsite%2FTypeScript%2FMsPortalImpl%2FCssLibrary%2FCustomPart.less&_a=contents&version=GBdev
*/

.ext-flexcontainer {
    display: flex;
    width: 100%;
}

.ext-flexcontainer--column {
    flex-direction: column;
}

.ext-flex {
    flex: 1 1 auto;
    width: 100%;
    min-width: 0;
}

.ext-flex--width-min-content {
    width: min-content;
}

.ext-flex--align-end {
    align-self: flex-end;
}

.ext-dropshadow1 {
    box-shadow: 0 1.6px 3.6px rgba(0, 0, 0, 0.132), 0px 0.3px 0.9px, rgba(0, 0, 0, 0.108);
    border-radius: 2px;
}

.ext-dropshadow2 {
    box-shadow: 0px 3.2px 7.2px rgba(0, 0, 0, 0.132), 0px 0.6px 1.8px rgba(0, 0, 0, 0.108);
    border-radius: 2px;
}

.ext-mode-dark .ext-dropshadow1 {
    box-shadow: 0 1.6px 3.6px rgba(255, 255, 255, 0.132), 0px 0.3px 0.9px, rgba(255, 255, 255, 0.108);
}

.ext-mode-dark .ext-dropshadow2 {
    box-shadow: 0px 3.2px 7.2px rgba(255, 255, 255, 0.132), 0px 0.6px 1.8px rgba(255, 255, 255, 0.108);
    border-radius: 2px;
}

.ext-dropshadow--border {
    /*#CCCCCC === rgb(204, 204, 204)*/
    border: 1px rgba(204, 204, 204, 0.5);
}

.ext-mode-dark .ext-dropshadow--border {
    border-color: #605E5C
}

/***
Launch Resource styling
***/

.ext-ml-launch-resource{
    font-family: "Segoe UI", "Segoe UI Web (West European)", "Segoe UI", -apple-system, BlinkMacSystemFont, Roboto, "Helvetica Neue", sans-serif;
    -webkit-font-smoothing: antialiased;
    font-size: 13px;
    font-weight: 400;
    flex-basis: auto;
    height: auto;
    width: auto;
    flex-shrink: 1;
}

.ext-ml-launch-resource .ext-ml-launch-icon-container {
    width: auto;
    height: auto;
    display: flex;
    justify-content: center;
}

.ext-ml-launch-resource .ext-ml-launch-icon {
    display: block;
    opacity: 0.5;
    width: 100px;
    height: 100px;
    padding-top: 20px;
    padding-bottom: 20px;
    filter: grayscale(100%);
}

.ext-ml-launch-resource .ext-ml-launch-header-container {
    width: auto;
    height: auto;
    display: flex;
    justify-content: center;
}

.ext-ml-launch-resource .ext-ml-launch-header{
    font-size: 20px;
    font-weight: 600;
    line-height: 28px;
    margin-bottom: 8px;
    margin-top: 16px;
}

.ext-ml-launch-resource .ext-ml-launch-description-container {
    text-align: center;
    width: 50%;
    padding-left: 25%;
}

.ext-ml-launch-resource .ext-ml-launch-description {
    max-width: 720px;
    text-align: center;
}

.ext-ml-launch-resource .ext-ml-launch-button {
    background-color: rgb(0, 120, 212);
    border: 1px solid rgb(0, 120, 212);
    color: rgb(255, 255, 255);
    margin-top: 14px;
    margin-bottom: 10px;
    width: 25%;
    height: 22px;
}

.ext-ml-launch-resource .ext-ml-launch-button:hover {
    background-color: rgb(16, 110, 190);
    border: 1px solid rgb(16, 110, 190);
    color: rgb(255, 255, 255);
    margin-top: 14px;
    margin-bottom: 10px;
    cursor: pointer;
}

.ext-ml-launch-resource .ext-ml-launch-button-container {
    display: flex;
    justify-content: center;
}

.ext-ml-launch-resource .ext-ml-card-hyperlink-icon-container {
    display: inline-block;
    padding-left: 12px;
}

.ext-ml-launch-resource .ext-ml-launch-hyperlink-icon {
    width: 12px;
    height: 12px;
    right: 10px;
    margin-top: 7px;
    position: relative;
}

.ext-ml-no-padding {
    padding: 0px !important;
}