// This file needs to remain "view agnostic", as it is used in ReactView and Knockout builds. Only add code here which does not rely on APIs specific to Knockout or ReactView views systems.
// i.e. No 'MsPortal' types or 'Fx/...' imports or 'azureportal-reactview' imports etc...

import { MachineLearningServicesKind, WorkspaceMappedKind } from "../Enums";

function isKnownKind(kind?: string): kind is MachineLearningServicesKind {
    return (
        !!kind &&
        Object.keys(MachineLearningServicesKind)
            .map(key => key.toLowerCase())
            .includes(kind.toLowerCase())
    );
}

/**
 * Kinds are an open ended enum. New kinds might be created and old kinds deprecated/subsumed.
 * When differentiating UX based on kind, use this mapped kind, to ensure we're handing the aforementioned scenarios.
 * see `WorkspaceMappedKind` type for kind mapping.
 * @returns WorkspaceMappedKind
 */
export function asMappedKind(kind: string, fallback?: WorkspaceMappedKind): WorkspaceMappedKind {
    if (!isKnownKind(kind)) {
        return fallback || WorkspaceMappedKind.Default;
    } else if (kind === MachineLearningServicesKind.Lean) {
        return WorkspaceMappedKind.Project;
    }
    // cast MachineLearningServicesKind to their equivalent union of literals, then cast back to an enum.
    return kind as `${WorkspaceMappedKind}` as WorkspaceMappedKind;
}

const hideBannerForKinds: string[] = [MachineLearningServicesKind.FeatureStore];
export function shouldShowBannerForKind(kind: string): boolean {
    return !hideBannerForKinds.includes(kind);
}

function generateRandomDigit(): string {
    return Math.floor(Math.random() * 10).toString();
}

/**
 * @param value String with placeholders to replace. e.g. "intro - {0} {1}"
 * @param replacements Values to use in placeholders. e.g. "hello", "world"
 * @returns formatted string, e.g. "intro - hello world".
 */
export function strFormat(value: string, ...replacements: Object[]): string {
    let finalValue = value;
    for (let i = 0; i < replacements.length; i++) {
        const pattern = new RegExp(`\\{${i.toString()}\\}`, "g");
        finalValue = finalValue.replace(pattern, replacements[i].toString());
    }
    return finalValue;
}

export function generateRandomNumberString(length: number): string {
    let result = "";
    for (var i = 0; i < length; i++) {
        result += generateRandomDigit();
    }
    return result;
}

/**
 * Take original value, filter out non alpha-numeric characters and append random number
 * @param value Original value
 * @param valueLength Where to cut off value if needed
 * @param numberLength Length of the random number
 */
export function appendRandomNumberToValue(value: string, valueLength: number, numberLength: number): string {
    return strFormat(
        "{0}{1}",
        value
            .toLocaleLowerCase()
            .replace(/[^a-z0-9]/g, "")
            .substring(0, valueLength),
        generateRandomNumberString(numberLength)
    );
}

/**
 * Helper function to generate an associated resource name based on the provided workspace name.
 */
export function generateName(workspaceName: string, valueLength = 14, numberLength = 10): string {
    return strFormat(
        "{0}{1}",
        workspaceName
            .toLocaleLowerCase()
            .replace(/[^a-z0-9]/g, "")
            .substring(0, valueLength),
        generateRandomNumberString(numberLength)
    );
}

export function friendifyWorkspaceName(workspaceName?: string): string {
    if (!workspaceName) {
        return "";
    }
    const formattedName = workspaceName.replace(/(\_)|(\-)/g, " ");
    return `${formattedName.charAt(0).toLocaleUpperCase()}${formattedName.slice(1)}`;
}

export function dedupe(values: string[]): string[] {
    const refs = new Set<string>();

    return values.filter(s => {
        if (!s || refs.has(s)) {
            return false;
        }
        refs.add(s);
        return s;
    });
}

function splitAndTakeLast(
    value?: string,
    options?: {
        /**
         * Defaults to /.
         */
        separator?: string;
        /**
         * Ensure the input is split into at least this many elements.
         */
        minElements?: number;
        /**
         * Take the element at this index, moving back from the end of the array.
         * Zero based. e.g. 0 = last element, 1 = second to last element.
         */
        offsetFromEnd?: number;
    }
): string | undefined {
    const { separator = "/", minElements = 1, offsetFromEnd = 0 } = options || {};
    const valueSplit = value?.split(separator);
    if (!Array.isArray(valueSplit) || valueSplit.length < minElements) {
        return undefined;
    }
    return valueSplit[valueSplit.length - (offsetFromEnd + 1)];
}

export function getVaultNameFromId(vaultId?: string): string | undefined {
    // No min elements, returns self or the last element.
    return splitAndTakeLast(vaultId);
}

export function getKeyVersionFromUrl(keyUrl?: string): string | undefined {
    // Split into at least two elements and take the last one.
    return splitAndTakeLast(keyUrl, {
        minElements: 2
    });
}

export function getKeyNameFromUrl(keyUrl?: string): string | undefined {
    // Split into at least three elements and take the second to last one.
    return splitAndTakeLast(keyUrl, {
        minElements: 3,
        offsetFromEnd: 1
    });
}

export function isAIStudioKind(kind: WorkspaceMappedKind): boolean {
    return kind === WorkspaceMappedKind.Hub || kind === WorkspaceMappedKind.Project;
}

type GenericError = { status?: unknown; httpStatusCode?: unknown };
function tryGetStatusCode(error: GenericError): number | undefined {
    if (typeof error.status === "number" && Number.isSafeInteger(error.status)) {
        return error.status;
    }
    if (typeof error.status === "string") {
        const status = parseInt(error.status, 10);
        if (Number.isSafeInteger(status)) {
            return status;
        }
    }
    if (typeof error.httpStatusCode === "number" && Number.isSafeInteger(error.httpStatusCode)) {
        return error.httpStatusCode;
    }
    if (typeof error.httpStatusCode === "string") {
        const status = parseInt(error.httpStatusCode, 10);
        if (Number.isSafeInteger(status)) {
            return status;
        }
    }
    return undefined;
}

export function getHttpStatusCode(response?: unknown): number | undefined {
    if (response && typeof response === "object" && ("status" in response || "httpStatusCode" in response)) {
        return tryGetStatusCode(response);
    }
    return undefined;
}

export function isUserError(httpStatusCode?: number): boolean {
    if (!httpStatusCode) {
        return false;
    }
    const UserErrors = [400, 401, 403, 404, 409, 429];
    return UserErrors.includes(httpStatusCode);
}

export function isNotFound(httpStatusCode?: number): boolean {
    return httpStatusCode === 404;
}

export function isUnauthorized(httpStatusCode?: number): boolean {
    if (!httpStatusCode) {
        return false;
    }
    const UnauthorizedErrors = [401, 403];
    return UnauthorizedErrors.includes(httpStatusCode);
}

/**
 * Attempt to infer the error message from an unknown error source.
 * Source must be an object. If the object contains a responseText property and a responseJSON property the responseJSON property will be preferred, and reponseText will be parsed as JSON as fallback.
 * If responseText is not present, then the content property will be checked as an object for the presence of 'error.message' property as a string.
 * Inteded to work with client error interfaces such as BatchResponseItem and JQueryXHR.
 * @param error Error object containing either any of responseText, responseJSON or content properties.
 * @returns
 */
export function extractHttpClientErrorMessage(
    error: unknown
): string | undefined {
    if (!(error && typeof error === "object")) {
        return;
    }
    let message;
    if ("responseText" in error) {
        let responseTextObject = "responseJSON" in error && typeof error.responseJSON === "object" ? error.responseJSON : undefined;
        if (typeof error.responseText === "string") {
            responseTextObject = responseTextObject ?? JSON.parse(error.responseText);
        }
        if (responseTextObject && "error" in responseTextObject) {
            if (
                typeof responseTextObject.error === "object" &&
                responseTextObject.error &&
                "message" in responseTextObject.error &&
                typeof responseTextObject.error.message === "string"
            ) {
                message = responseTextObject.error.message;
            }
        }
    } else if (
        "content" in error &&
        typeof error.content === "object" &&
        error.content &&
        "error" in error.content &&
        typeof error.content.error === "object" &&
        error.content.error &&
        "message" in error.content.error &&
        typeof error.content.error.message === "string"
    ) {
        message = error.content.error.message;
    }
    return message;
}

const offerLinkMap: Record<string, string> = {
    "llama-2-70b-offer": "https://aka.ms/aistudio/landing/Llama-2-70b",
    "llama-2-70b-chat": "https://aka.ms/aistudio/landing/Llama-2-70b-chat",
    "llama-2-13b-offer": "https://aka.ms/aistudio/landing/Llama-2-13b",
    "llama-2-13b-chat": "https://aka.ms/aistudio/landing/Llama-2-13b-chat",
    "llama-2-7b-offer": "https://aka.ms/aistudio/landing/Llama-2-7b",
    "llama-2-7b-chat": "https://aka.ms/aistudio/landing/Llama-2-7b-chat",
    "meta-llama-3-8b-offer": "https://aka.ms/aistudio/landing/meta-llama-3-8b-base",
    "meta-llama-3-8b-chat-offer": "https://aka.ms/aistudio/landing/meta-llama-3-8b-chat",
    "meta-llama-3-70b-offer" : "https://aka.ms/aistudio/landing/meta-llama-3-70b-base",
    "meta-llama-3-70b-chat-offer" : "https://aka.ms/aistudio/landing/meta-llama-3-70b-chat",
    "mistral-large-offer": "https://aka.ms/aistudio/landing/mistral-large",
    "mistral-ai-large-offer": "https://aka.ms/aistudio/landing/mistral-large",
    "mistral-ai-small-offer": "https://aka.ms/aistudio/landing/mistral-small",
    "cohere-ai-command-r-v1-offer" : "https://aka.ms/aistudio/landing/cohere-command-r",
    "cohere-command-r-plus-offer" : "https://aka.ms/aistudio/landing/cohere-command-r-plus",
    "cohere-embed-v3-english-offer" : "https://aka.ms/aistudio/landing/cohere-embed-v3-english",
    "cohere-embed-v3-multilingual-offer" : "https://aka.ms/aistudio/landing/cohere-embed-v3-multilingual",
    "core42-jais30b-v3-chat-offer" : "https://aka.ms/aistudio/landing/jais-30b-chat",
    "nixtlatimegen1" : "https://aka.ms/aistudio/landing/nixtlatimegen1",
    "mistral-ai-open-mixtral-8x7b" : "https://aka.ms/aistudio/landing/mixtral-8x7B",
    "mistral-ai-open-mistral-7b" : "https://aka.ms/aistudio/landing/mistral-7B",
    "ai21-jamba-instruct-offer" : "https://aka.ms/aistudio/landing/ai21-labs-jamba-instruct",
    "meta-llama-3-1-405b-instruct-offer": "https://aka.ms/aistudio/landing/meta-llama-3.1-405B-instruct",
    "meta-llama-3-1-8b-instruct-offer" : "https://aka.ms/aistudio/landing/meta-llama-3.1-8B-instruct",
    "meta-llama-3-1-70b-instruct-offer" : "https://aka.ms/aistudio/landing/meta-llama-3.1-70B-instruct",
    "mistral-nemo-12b-2407" : "https://aka.ms/aistudio/landing/mistral-nemo-12b",
    "mistral-ai-large-2407-offer" : "https://aka.ms/aistudio/landing/mistral-large-2407",
    "Cohere-rerank-3-english-offer" : "https://aka.ms/aistudio/landing/cohere-rerank-3-english",
    "cohere-rerank-3-multilingual" : "https://aka.ms/aistudio/landing/cohere-rerank-3-multilingual",
    "ai21-jamba-1-5-large-offer" : "https://aka.ms/aistudio/landing/ai21-jamba-1.5-large",
    "ai21-jamba-1-5-offer" : "https://aka.ms/aistudio/landing/ai21-jamba-1.5",
    "nttdata-tsuzumi-1-1-instruct-offer" : "https://aka.ms/aistudio/landingpage/tsuzumi",
    "mistral-ai-codestral-offer" : "https://aka.ms/aistudio/landing/codestral",
    "cohere-command-r-08-2024-offer" : "https://aka.ms/azureai/landing/Cohere-command-r-08-2024",
    "cohere-command-r-plus-08-2024-offer" : "https://aka.ms/azureai/landing/Cohere-command-r-plus-08-2024",
    "meta-llama-3-2-11b-vision-instruct-offer" : "https://aka.ms/aistudio/landing/llama-3.2-11B-vision-instruct",
    "meta-llama-3-2-90b-vision-instruct-offer" : "https://aka.ms/aistudio/landing/llama-3.2-90B-vision-instruct",
    "ministral-8b-2410-offer" : "https://aka.ms/azureai/landing/Ministral-8B",
    "ministral-3b-2410-offer" : "https://aka.ms/azureai/landing/Ministral-3B",
    "bria_image_generation_model_offer" : "https://aka.ms/aistudio/landing/bria-image-generation",
    "mistral-large-2411" : "https://aka.ms/aistudio/landing/mistral-large-2411",
    "llama-3-3-70b-instruct-offer" : "https://aka.ms/aifoundry/landing/llama-3.3-70b-instruct",
    "gretel-navigator-tabular-v1-offer" : "https://aka.ms/aistudio/landing/gretel-navigator-tabular-v1",
    "mistral-codestral-2501" : "https://aka.ms/aistudio/landing/mistral-codestral-2501",
    "stabilityai-sic" : "https://aka.ms/aistudio/landing/stabilityai-image-core",
    "stabilityai-siu" : "https://aka.ms/aistudio/landing/stabilityai-image-ultra",
    "stabilityai-sd35l" : "https://aka.ms/aistudio/landing/stability-ai-diffusion-35",
    "cohere-rerank-v3-5-offer" : "https://aka.ms/aistudio/landing/cohere-rerank-v3.5",
    "mistral-small-2503" : "https://aka.ms/MistralSmall2503",
    "nvidia-nims": "https://aka.ms/foundry/landing/nvidia-nims",
    "mistral-ocr-2503" : "https://aka.ms/MistralOCR25.03",
    "cohere-command-a" : "https://aka.ms/aistudio/landing/cohere-command-a",
    "cohere-embed-4-offer" : "https://aka.ms/aistudio/landing/cohere-embed-4",
    "mistral-medium-2505": "https://aka.ms/aistudio/landing/mistral-medium-2505",
    "paige-virchow-2g-offer": "https://aka.ms/aistudio/landing/paige-virchow2g",
    "paige-virchow-2g-mini-offer": "https://aka.ms/aistudio/landing/paige-virchow2g-mini",
}
export function getLinkForOffer(offerIds: string[]): string | undefined {
    const offersList = offerIds.filter(id => (typeof id === "string" && id.length > 0)).map(id => id.toLowerCase());
    const key = Object.keys(offerLinkMap).find((key) => {
        return offersList.some(o => o.includes(key.toLowerCase()));
    });
    if (key) {
        return offerLinkMap[key];
    }
    return undefined;
}

export function createDynamicOfferLink(offer: string | undefined): string | undefined {
    if (typeof offer !== "string" || offer.trim().length === 0) {
        return undefined;
    }
    return `https://aka.ms/aistudio/landing/${encodeURIComponent(offer.trim().toLowerCase())}`;
}
