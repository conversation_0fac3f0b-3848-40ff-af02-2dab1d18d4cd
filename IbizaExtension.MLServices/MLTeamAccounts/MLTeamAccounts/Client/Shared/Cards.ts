﻿import Image = MsPortalFx.Base.Image;
import { ClickableLink } from "Fx/Composition";
import * as CustomHtml from "Fx/Controls/CustomHtml";

export interface ICard {
    icon: Image;
    onClick?: () => void;
    header: string;
    body: string;
    clickableLink?: ClickableLink;
    headerAriaLabel?: string;
    bodyAriaDescription?: string;
}

export default class Card {
    public static createCard(lifetimeManager: MsPortalFx.Base.LifetimeManager, cardInfo: ICard): CustomHtml.Contract {
        if (!!cardInfo.clickableLink) {
            const viewModel = { ...cardInfo, externalLinkIcon: MsPortalFx.Base.Images.Hyperlink({ customClass: 'ext-ml-hyperlink' }) };
            return CustomHtml.create(lifetimeManager, {
                innerViewModel: viewModel,
                htmlTemplate: `
                    <div class='ext-ml-card-container' data-bind='fxclick: clickableLink'>
                        <div class='ext-ml-card-icon-container msportalfx-bg-default'>
                            <div class='ext-ml-card-icon msportalfx-fill' data-bind='image: icon'></div>
                        </div>
                        <div class='ext-ml-card-text'>
                            <div class='ext-ml-card-header'  data-bind='text: header, attr: {"aria-label": headerAriaLabel}'>Header</div>
                            <div class='ext-ml-card-body'  data-bind='text: body, attr:{ "aria-label": bodyAriaDescription}'>Body</div>
                        </div>
                        <div class='ext-ml-card-hyperlink-icon' data-bind='image: externalLinkIcon'/div>
                    </div>`
            });
        }
        if (cardInfo.onClick) {
            return CustomHtml.create(lifetimeManager, {
                innerViewModel: cardInfo,
                htmlTemplate: `
                    <div class='ext-ml-card-container' data-bind='fxclick: onClick'>
                        <div class='ext-ml-card-icon-container msportalfx-bg-default'>
                            <div class='ext-ml-card-icon' data-bind='image: icon'></div>
                        </div>
                        <div class='ext-ml-card-text'>
                            <div class='ext-ml-card-header' data-bind='text: header, attr: { "aria-label": headerAriaLabel }' aria-role="button">Header</div>
                            <div class='ext-ml-card-body' data-bind='text: body, attr: { "aria-label": bodyAriaDescription }' aria-role="button">Body</div>
                        </div>
                    </div>`
            });
        }
    }
}