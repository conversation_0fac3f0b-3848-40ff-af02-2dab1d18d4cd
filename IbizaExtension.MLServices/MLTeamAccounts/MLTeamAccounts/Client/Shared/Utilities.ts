import * as FxAjax from "Fx/Ajax";
import * as FxSelectable2 from "Fx/Composition/Selectable";
import { ArmId } from "Fx/ResourceManagement";
import * as ExtensionDefinition from "../_generated/ExtensionDefinition";
import SupportReferences = ExtensionDefinition.External.Microsoft_Azure_Support;
import ClientResources = require("Resx/ClientResources");
import Constants = require("./Constants");
import { WorkspaceMappedKind } from "./Enums";
import { asMappedKind, isAIStudioKind } from "./ViewAgnostic/Utilities";
import { Icons } from "./Icons";

export default class Utilities {
    public static extractErrorMessage(error: JQueryXHR<any> | FxAjax.BatchResponseItem<any>): string {
        let result = Constants.emptyString;
        if (!MsPortalFx.isNullOrUndefined(error)) {
            if ("responseText" in error && (error.responseJSON || !MsPortalFx.isNullOrUndefined(error.responseText))) {
                try {
                    const responseTextObject = error.responseJSON || JSON.parse(error.responseText);

                    if (!MsPortalFx.isNullOrUndefined(responseTextObject) && !MsPortalFx.isNullOrUndefined(responseTextObject.error)) {
                        result = responseTextObject.error.message || Constants.emptyString;
                    }
                } catch (exception) {
                    if (exception instanceof SyntaxError) {
                        MsPortalFx.Base.Diagnostics.Log.writeEntry(
                            MsPortalFx.Base.Diagnostics.LogEntryLevel.Error,
                            "extractErrorMessage",
                            exception.message
                        );
                    }
                    result = ClientResources.errorJsonParsingException;
                }
            }
            else if ("content" in error && error.content && error.content.error) {
                result = error.content.error.message;
            }
        }

        return result;
    }

    public static getErrorStatusCode(error: JQueryXHR<any> | FxAjax.BatchResponseItem<any>): number {
        let statusCode = -1;
        if (!MsPortalFx.isNullOrUndefined(error)) {
            if ("status" in error) {
                statusCode = error.status;
            }
            else if ("httpStatusCode" in error) {
                statusCode = error.httpStatusCode;
            }
        }

        return statusCode;
    }

    public static isUserError(error: JQueryXHR<any> | FxAjax.BatchResponseItem<any>): boolean {
        const UserErrors = [400, 401, 403, 404, 409, 429];
        return error && UserErrors.includes(this.getErrorStatusCode(error));
    }

    public static isNotFound(error: JQueryXHR<any> | FxAjax.BatchResponseItem<any>): boolean {
        return error && this.getErrorStatusCode(error) === 404;
    }

    public static getStartboardPartIcon(machineLearningServicesKind: string): MsPortalFx.Base.Image {
        const mappedKind = asMappedKind(machineLearningServicesKind);
        if (isAIStudioKind(mappedKind)) {
            return Icons.aiStudioIcon;
        }
        return Icons.webWorkspaceIcon;
    }

    public static getMachineLearningServicesDisplayNameSingular(machineLearningServicesKind: string): string {
        const mappedKind = asMappedKind(machineLearningServicesKind);
        if (mappedKind === WorkspaceMappedKind.FeatureStore) {
            return ClientResources.AssetTypeNames.MachineLearningServices.FeatureStore.singular;
        }
        if (isAIStudioKind(mappedKind)) {
            return ClientResources.AssetType.AIStudio.Names.singular;
        }
        return ClientResources.AssetTypeNames.MachineLearningServices.Default.singular;
    }

    public static getAppInsightsLocation(workspaceLocation: string): string {
        // canonicalize location since Azure guidelines indicate we need to be robust to spaces and casing
        workspaceLocation = workspaceLocation.replace(/\s/g, '').toLowerCase();
        return (<any>Constants.appInsightsRegionUnavailabilityMap)[workspaceLocation] !== undefined ?
            (<any>Constants.appInsightsRegionUnavailabilityMap)[workspaceLocation] :
            workspaceLocation;
    }

    public static extractXmsRequestId(error: JQueryXHR<any> | FxAjax.BatchResponseItem<any>) {
        if (!MsPortalFx.isNullOrUndefined(error)) {
            if ("getResponseHeader" in error) {
                const responseHeader = error.getResponseHeader(Constants.headers.xmsRequestId);
                if (responseHeader) {
                    return responseHeader;
                }
            }
            else if ("headers" in error) {
                return error.headers[Constants.headers.xmsRequestId];
            }
        }

        return Constants.emptyString;
    }

    public static getRequest(uri: string) {
        const ajaxSettings = this._getGenericAjaxSettings<any>(uri, "GET", null, false, false);
        return FxAjax.ajax(ajaxSettings);
    }

    public static putRequestWithPoll(uri: string) {
        const ajaxSettings = this._getGenericAjaxSettings<any>(uri, "PUT", null, false, false);
        return this._poll(ajaxSettings);
    }

    public static postRequestWithPoll(uri: string, payload: any) {
        const ajaxSettings = this._getGenericAjaxSettings<any>(uri, "POST", payload, false, false);
        return this._poll(ajaxSettings);
    }

    public static deleteRequestWithPoll(uri: string) {
        const ajaxSettings = this._getGenericAjaxSettings<any>(uri, "DELETE", null, false, false);
        return this._poll(ajaxSettings);
    }

    private static _poll(ajaxSettings: FxAjax.NetAjaxSettings<any>, useArm: boolean = true): Promise<MsPortalFx.Base.Net2.Response<any>> {
        return FxAjax.ajaxExtended(ajaxSettings).then(response => {
            if (response.jqXHR.status === FxAjax.HttpStatusCode.Accepted) {
                const pollLocationHeader = response.jqXHR.getResponseHeader("Location");
                const retryAfterHeader = response.jqXHR.getResponseHeader("Retry-After");
                const retryAfter = this.tryParseRetryAfter(retryAfterHeader, 10);

                let hostNameARM = "";
                if (useArm) {
                    // replace FQDN with ARM
                    const pollURL = new URL(pollLocationHeader);
                    try {
                        const armEndpointUrl = new URL(MsPortalFx.getEnvironmentValue("armEndpoint"));
                        hostNameARM = armEndpointUrl.hostname;
                    } catch { }
                    if (hostNameARM && !pollLocationHeader.includes(hostNameARM)) {
                        pollURL.hostname = hostNameARM;
                        // remove 'rp/workspaces' path
                        pollURL.pathname = pollURL.pathname.replace(/\/rp\/workspaces/g, "");
                        return this._pollAsyncStatus<any>(pollURL.href, retryAfter);
                    }
                }
                return this._pollAsyncStatus<any>(pollLocationHeader, retryAfter);
            }
            return response;
        })
    }

    public static armHeadRequest(uri: string) {
        return this._armGenericRequest<any>(uri, "HEAD");
    }

    public static armGetRequest(uri: string, accumulateResult: boolean = false): Promise<any> {
        if (accumulateResult) {
            const deferred = Q.defer<any>();
            let resources: any[] = [];
            const fetchResources = (uri: string) => {
                this._armGenericRequest<any>(uri, "GET").then((response: any) => {
                    resources = resources.concat(response.value);
                    if (response.nextLink) {
                        fetchResources(response.nextLink);
                    } else {
                        deferred.resolve({
                            value: resources
                        });
                    }
                }, (failureResponse: MsPortalFx.Base.Net2.ErrorResponse<any>) => {
                    deferred.reject(failureResponse);
                });
            };
            fetchResources(uri);
            return deferred.promise;
        }
        return this._armGenericRequest<any>(uri, "GET");
    }

    public static armPostRequest(uri: string, payload: any) {
        return this._armGenericRequest<any>(uri, "POST", payload);
    }

    public static armPutRequest(uri: string, payload: any) {
        return this._armGenericRequest<any>(uri, "PUT", payload);
    }

    public static armDeleteRequest(uri: string) {
        const ajaxSettings = this._getGenericAjaxSettings<any>(uri, "DELETE");
        return FxAjax.ajaxExtended(ajaxSettings).then(response => {
            if (response.jqXHR.status === FxAjax.HttpStatusCode.Accepted) {
                const pollLocationHeader = response.jqXHR.getResponseHeader("Location");
                const retryAfterHeader = response.jqXHR.getResponseHeader("Retry-After");
                const retryAfter = this.tryParseRetryAfter(retryAfterHeader);
                return this._pollAsyncStatus<any>(pollLocationHeader, retryAfter);
            }
            return response;
        })
    }

    public static armPatchRequest(uri: string, payload: any) {
        return this._armGenericRequest<any>(uri, "PATCH", payload);
    }

    public static armUpdateRequestWithPoll(uri: string, payload: any, updateType: "PUT" | "PATCH" = "PATCH") {
        const ajaxSettings = this._getGenericAjaxSettings(uri, updateType, payload, true);

        return FxAjax.ajaxExtended(ajaxSettings).then(response => {
            if (response.jqXHR.status === FxAjax.HttpStatusCode.Accepted) {
                const pollLocationHeader = response.jqXHR.getResponseHeader("Location");
                const retryAfterHeader = response.jqXHR.getResponseHeader("Retry-After");
                const retryAfter = this.tryParseRetryAfter(retryAfterHeader, 2);
                return this._pollAsyncStatus<any>(pollLocationHeader, retryAfter);
            }

            return response;
        });
    }

    private static _getGenericAjaxSettings<T>(uri: string, requestType: string, payload: any = null, emptyResponseExpected = false, invokeApi = true): FxAjax.NetAjaxSettings<T> {
        const ajaxSettings: FxAjax.NetAjaxSettings<T> = {
            uri: uri,
            type: requestType,
            traditional: true,
            contentType: "application/json",
            setAuthorizationHeader: true,
            invokeApi: invokeApi ? "api/invoke" : undefined
        };

        if (!MsPortalFx.isNullOrUndefined(payload)) {
            if (!emptyResponseExpected) {
                ajaxSettings['dataType'] = 'json';
            }
            ajaxSettings['data'] = payload;
        }

        return ajaxSettings;
    }

    private static tryParseRetryAfter(retryAfterHeader: string, defaultPollDelaySeconds = Constants.armPollingDelayDefault) {
        let retryAfter: number;
        try {
            retryAfter = Number.parseInt(retryAfterHeader, 10);
            if (!Number.isSafeInteger(retryAfter)) { // ensure retry-after header is valid number
                retryAfter = defaultPollDelaySeconds;
            }
        } catch (e) {
            retryAfter = defaultPollDelaySeconds;
        }
        return retryAfter;
    }

    // https://github.com/Azure/azure-resource-manager-rpc/blob/master/v1.0/Addendum.md#202-accepted-and-location-headers
    private static _pollAsyncStatus<T>(
        pollLocation: string,
        delaySeconds: number,
        useARM: boolean = true,
    ): Q.Promise<MsPortalFx.Base.Net2.Response<T>> {
        const ajaxSettings = this._getGenericAjaxSettings<T>(pollLocation, "GET", undefined, false, useARM);
        return Q.delay(delaySeconds * 1000).then(() => FxAjax.ajaxExtended(ajaxSettings)).then((response) => {
            if (response.jqXHR.status === FxAjax.HttpStatusCode.Ok || response.jqXHR.status === FxAjax.HttpStatusCode.NoContent) {
                return response;
            }
            const pollLocationHeader = response.jqXHR.getResponseHeader("Location");
            const retryAfterHeader = response.jqXHR.getResponseHeader("Retry-After");
            const retryAfter = this.tryParseRetryAfter(retryAfterHeader, delaySeconds);
            return this._pollAsyncStatus<T>(pollLocationHeader, retryAfter);
        })
    }

    private static _armGenericRequest<T>(uri: string, requestType: string, payload: any = null) {
        const ajaxSettings = this._getGenericAjaxSettings<T>(uri, requestType, payload);

        return FxAjax.ajax(ajaxSettings);
    }

    public static getQuotaSupportRequestBladeReference(subscriptionId: string) {
        // topicId is the SAP id of MachineLearningService
        return new FxSelectable2.PdlBladeReference<any, void>(
            SupportReferences.BladeNames.NewSupportRequestV3Blade,
            ExtensionDefinition.ExtensionNames.Microsoft_Azure_Support,
            {
                parameters: {
                    issueType: "quota",
                    subscriptionId: subscriptionId,
                    topicId: "c8800fe7-23e4-8cca-0d90-2379734c0006"
                }
            }
        );
    }

    /**
     * Create the necessary querystring parameters to load a workspace in the studio (e.g tid, and wsid)
     */
    private static async getWorkspaceQuerystringParams(id: ArmId): Promise<string> {
        const tenantId = (await MsPortalFx.Azure.getSubscriptionInfo(id.subscription)).tenantId;
        return `tid=${tenantId}&wsid=/subscriptions/${id.subscription}/resourcegroups/${id.resourceGroup}/providers/${id.provider}/workspaces/${id.resourceName}`;
    }

    public static async generateWs2Url(parameters: MachineLearningOverviewBlade.MachineLearningOverviewBladeParameters): Promise<string> {
        const baseUrl = MsPortalFx.getEnvironmentValue("webWorkspace2BaseUrl");
        try {
            const workspaceArmId = ArmId.parse(parameters.id);
            const params = await this.getWorkspaceQuerystringParams(workspaceArmId);
            return `${baseUrl}/?${params}`;
        } catch {
            return baseUrl;
        }
    }

    public static generateStudioQuotaLink(subscriptionId?: string, location?: string): string {
        const baseUrl = MsPortalFx.getEnvironmentValue("webWorkspace2BaseUrl")
        if (!subscriptionId) {
            return `${baseUrl}/quota`
        }
        return `${baseUrl}/quota/${subscriptionId}/${location || ""}`;
    }

    public static async generateFeatureStoreUrl(parameters: MachineLearningOverviewBlade.MachineLearningOverviewBladeParameters): Promise<string> {
        const baseUrl = MsPortalFx.getEnvironmentValue("webWorkspace2BaseUrl");
        try {
            const workspaceArmId = ArmId.parse(parameters.id);
            const params = await this.getWorkspaceQuerystringParams(workspaceArmId);
            return `${baseUrl}/featureStore/${workspaceArmId.resourceName}/?${params}`;
        } catch {
            return baseUrl;
        }
    }

    public static async generateRegistryUrl(resourceName: string): Promise<string> {
        const baseUrl = MsPortalFx.getEnvironmentValue("webWorkspace2BaseUrl");
        return `${baseUrl}/registries/${resourceName}`;
    }

    public static updateDiscoveryURL(url: string) {
        const modifiedURL = url.replace(/\/discovery.*/, "");
        // modify PL workspace discovery url
        if (modifiedURL.includes(".workspace.")) {
            return modifiedURL.replace(/\/\/.*\.workspace\./, "//");
        }
        return modifiedURL;
    }

    public static constructRegionalAPIEndpoint(region: string): string {
        const apiSuffix = MsPortalFx.getEnvironmentValue("apiSuffix");

        if (region === "centraluseuap") {
            return `https://int.api.azureml-test.ms`;
        }
        return `https://${region}.${apiSuffix}`;
    }

    public static generateManagedResourceGroupUrl(resourceId: string) {
        const baseUrl = MsPortalFx.getEnvironmentValue("trustedParentOrigin");
        return `${baseUrl}#resource/${resourceId}/overview;`
    }

    public static isRegistry(armId: string): boolean {
        return Constants.machineLearningRegistries === ArmId.getResourceTypes(ArmId.parse(armId))[0];
    }

    public static formatString(template: string, ...values: string[]): string {
        return template.replace(/{(\d+)}/g, (match, number) => {
            return typeof values[number] !== 'undefined' ? values[number] : match;
        });
    }
}
