export enum DocLinkIds {
    ComputeTargets = "azure/machine-learning/concept-compute-target",
    CrossLocationQuotas = "azure/machine-learning/how-to-manage-quotas",
    ServiceSideDataEncryption = "azure/machine-learning/concept-customer-managed-keys#preview-service-side-encryption-of-metadata",
    DataLakeStorageGen2 = "azure/storage/blobs/data-lake-storage-introduction",
    ManageQuotas = "azure/machine-learning/how-to-manage-quotas#azure-machine-learning-compute",
    StorageAccountAccessType = "azure/machine-learning/how-to-identity-based-data-access",
    StorageAccountReplication = "azure/storage/common/storage-redundancy",
    StorageIdentityAccessLearnMoreAboutRBACForHub = "azure/ai-studio/how-to/disable-local-auth?tabs=portal#scenarios-for-hub-storage-account-role-assignments",
    StorageIdentityAccessLearnMoreAboutRBACForAML = "azure/machine-learning/how-to-disable-local-auth-storage?view=azureml-api-2&tabs=portal#scenarios-for-role-assignments",
    PublicAccess = "azure/machine-learning/how-to-configure-private-link#enable-public-access",
    ResourceGroups = "azure/azure-resource-manager/management/manage-resource-groups-portal",
    MachineLearningWorkspace = "azure/machine-learning/concept-workspace",
    CMK = "azure/machine-learning/how-to-setup-customer-managed-keys",
    SoftDelete = "azure/machine-learning/concept-soft-delete",
    ManagedNetworkIsolation = "azure/machine-learning/how-to-managed-network",
    ManagedNetworkIsolationAiResource = "azure/ai-studio/how-to/configure-managed-network",
    ManagedNetworkArchitecture = "azure/machine-learning/how-to-managed-network#managed-virtual-network-architecture",
    VirtualNetworkManagedByAMLLearnMore = "azure/private-link/private-endpoint-overview",
    ManagedNetworkIsolationRequiredTarget = "azure/machine-learning/how-to-managed-network#list-of-required-rules",
    HubManagedNetworkIsolationRequiredTarget = "azure/ai-studio/how-to/configure-managed-network?tabs=portal#list-of-required-rules",
    ManagedNetworkIsolationRecommendedTarget = "azure/machine-learning/how-to-managed-network#list-of-recommended-outbound-rules",
    ManagedNetworkApprovedOutboundRules = "azure/machine-learning/how-to-managed-network?view=azureml-api-2&tabs=azure-cli#configure-a-managed-virtual-network-to-allow-only-approved-outbound",
    GrantIpBasedAccess = "azure/machine-learning/how-to-configure-private-link#enable-public-access-only-from-internet-ip-ranges-preview",
    AzureFirewallSkuAML = "/azure/machine-learning/how-to-managed-network?view=azureml-api-2&tabs=azure-cli#pricing",
    AzureFirewallSkuAIStudio = "/azure/ai-studio/how-to/configure-managed-network?tabs=portal#pricing",
    ManagedKeyVault = "/azure/ai-studio/how-to/create-azure-ai-resource?tabs=portal#choose-how-credentials-are-stored"
}

export function getDocumentUrl(docId: DocLinkIds): string {
    return `https://docs.microsoft.com/${docId}`;
}