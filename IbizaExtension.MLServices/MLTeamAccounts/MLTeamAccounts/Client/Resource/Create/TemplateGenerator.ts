import { ArmId } from "Fx/ResourceManagement";
import * as Provisioning from "Fx/ResourceManagement/Provisioning";

import * as Constants from "../../Shared/Constants";
import { SKUTypes, WorkspaceMappedKind } from "../../Shared/Enums";
import { getPrivateDnsZoneName } from "../../Shared/NetworkUtilities";
import { TelemetryLoggingOptions } from "./Controls/CreateFormControl";
import { defaultStorageAccessType, ManagedKeys, WizardContext } from "./ViewModels/CreateMachineLearningModels";

import { ConnectivityMethod, PrivateEndpointDetails } from "../Common/NetworkingDataModel";

const appInsightsLocationCondition = `[
  if(
    or(
      equals(parameters('applicationInsightsLocation'),'westcentralus'),
      equals(parameters('applicationInsightsLocation'),'eastus2euap'),
      equals(parameters('applicationInsightsLocation'),'centraluseuap')),
      'southcentralus',
      parameters('applicationInsightsLocation'
    )
  )
]`;

interface IResource {
  type: string;
  properties: any;
  resources?: IResource[];
  condition?: string;
  apiVersion?: string;
  name?: string;
  dependsOn?: string[];
}

const provisioningHashUniqueString = 'iwuh-wccbe-tt0-2enb-ioes-849e-tircx-guw6';

export function getTemplateDeploymentOptions(context: WizardContext): Provisioning.DeployTemplateOptions {
  const { dataModel, workspaceKind } = context;
  const { advanced, basics, networking, options } = dataModel;
  const {
    highBusinessImpactChecked,
    managedKeys,
    identityType,
    systemDatastoresAuthMode,
    userAssignedIdentityName,
    userAssignedIdentityResourceGroup,
    disableSharedKeyAccess,
    managedKeyVaultType
  } = advanced;

  const {
    appInsights,
    appInsightsName,
    aiServices,
    aiServicesName,
    containerRegistry,
    hubConfigDefaultResourceGroup,
    hubConfigDefaultResourceGroupSameAs,
    keyVault,
    keyVaultName,
    location,
    resourceGroup,
    sku,
    storageAccount,
    storageAccountName,
    subscription,
    workspaceName: wsName,
    friendlyName,
    description,
    hub
  } = basics;

  // Basics
  const subscriptionId = subscription().subscriptionId;
  const resourceGroupName = resourceGroup().value;
  const locationName = location().name;
  const workspaceName = wsName();
  const skuType = sku() ? sku() : SKUTypes.Basic;
  const hbiWorkspace = highBusinessImpactChecked();
  const customerManagedKeyVault = managedKeys() === ManagedKeys.customer;

  const parameters: StringMap<any> = {
    kind: workspaceKind,
    location: locationName,
    workspaceName: workspaceName,
    friendlyName: friendlyName(),
    resourceGroupName: resourceGroupName.name,
    sku: skuType,
    description: description(),
    tagValues: generateTagsMap(context),
    keyVaultName: keyVaultName(),
    storageAccountName: storageAccountName(),
    appInsightsLogWorkspaceName: basics.appInsightsLogWorkspaceName(),
    applicationInsightsName: appInsightsName(),
    managedKeyVaultType: managedKeyVaultType()
  };

  if (storageAccount()) {
    parameters["storageAccountOption"] = storageAccount().isNew ? "new" : "existing";
    parameters["storageAccountName"] = storageAccount().name;
    parameters["storageAccountLocation"] = storageAccount().location;
    parameters["storageAccountType"] = storageAccount().sku;
    parameters["storageAccountHnsEnabled"] = Boolean(storageAccount().isHnsEnabled);

    const storageAccountResourceGroupName = storageAccount().resourceGroupName ? storageAccount().resourceGroupName : null;
    if (storageAccountResourceGroupName) {
      parameters["storageAccountResourceGroupName"] = storageAccountResourceGroupName;
    }
  }

  if (keyVault()) {
    parameters["keyVaultOption"] = keyVault().isNew ? "new" : "existing";
    parameters["keyVaultName"] = keyVault().name;
    parameters["keyVaultLocation"] = keyVault().location;

    const keyVaultResourceGroupName = keyVault().resourceGroupName ? keyVault().resourceGroupName : null;
    if (keyVaultResourceGroupName) {
      parameters["keyVaultResourceGroupName"] = keyVaultResourceGroupName;
    }
  }

  if (appInsights()) {
    parameters["applicationInsightsOption"] = appInsights().isNew ? "new" : "existing";
    parameters["applicationInsightsName"] = appInsights().name;
    parameters["applicationInsightsLocation"] = appInsights().location;

    const applicationInsightsResourceGroupName = appInsights().resourceGroupName ? appInsights().resourceGroupName : null;
    if (applicationInsightsResourceGroupName) {
      parameters["applicationInsightsResourceGroupName"] = applicationInsightsResourceGroupName;
    }
  }

  if (workspaceKind === WorkspaceMappedKind.Hub) {
    if (hubConfigDefaultResourceGroupSameAs()) {
      parameters["defaultProjectResourceGroup"] = resourceGroupName.resourceId;
    } else {
      parameters["defaultProjectResourceGroup"] = hubConfigDefaultResourceGroup()?.value?.name ? hubConfigDefaultResourceGroup()?.value?.resourceId : "";
    }
    parameters["aiServicesName"] = aiServicesName();
    if (aiServices()) {
      const aiServicesKind = aiServices().kind;
      parameters["aiServicesOption"] = aiServices().isNew ? "new" : "existing";
      parameters["aiServicesName"] = aiServices().name;
      parameters["aiServicesLocation"] = aiServices().location;
      parameters["aiServicesKind"] = aiServicesKind && ["OpenAI", "AIServices"].includes(aiServicesKind) ? aiServicesKind : "AIServices";
      parameters["endpointOption"] = "none";

      const aiServicesResourceGroupName = aiServices().resourceGroupName ? aiServices().resourceGroupName : null;
      if (aiServicesResourceGroupName) {
        parameters["aiServicesResourceGroupName"] = aiServicesResourceGroupName;
      }
    }
  }

  if (containerRegistry()) {
    parameters["containerRegistryOption"] = containerRegistry().isNew ? "new" : "existing";
    parameters["containerRegistryName"] = containerRegistry().name;
    parameters["containerRegistrySku"] = containerRegistry().sku;
    parameters["containerRegistryLocation"] = containerRegistry().location;

    const containerRegistryResourceGroupName = containerRegistry().resourceGroupName ? containerRegistry().resourceGroupName : null;
    if (containerRegistryResourceGroupName) {
      parameters["containerRegistryResourceGroupName"] = containerRegistryResourceGroupName;
    }
  }

  const privateEndpointDetails = networking.getPrivateEndpointDetails();
  if (privateEndpointDetails) {
    parameters["privateEndpointName"] = privateEndpointDetails.name;
    parameters["subnetOption"] = "existing";
    parameters["subnetName"] = privateEndpointDetails.subnetName;
    parameters["vnetOption"] = "existing";
    parameters["vnetName"] = privateEndpointDetails.vnetName;
    parameters["vnetResourceGroupName"] = ArmId.parse(privateEndpointDetails.vnetId).resourceGroup;
    parameters["privateEndpointType"] = "AutoApproval";
    parameters["privateEndpointResourceGroupName"] = privateEndpointDetails.resourceGroupName;
    parameters["privateEndpointSubscription"] = privateEndpointDetails.subscription;
  }

  const managedNetwork = networking.getManagedNetwork();

  parameters["managedNetwork"] = managedNetwork;
  parameters["provisionNetworkNow"] = networking.provisionManagedNetwork() ? "true" : "false";

  if(networking.delegatedSubnet()) {
    parameters["delegateSubnetId"] = networking.delegatedSubnet().id;
    parameters["delegateSubnetName"] = networking.delegatedSubnet().name.replace(/[\s\-_:]+/g, "").toLowerCase();
  } else {
    parameters["delegateSubnetId"] = "none";
    parameters["delegateSubnetName"] = "";
  }

  if (customerManagedKeyVault) {
    parameters["encryption_status"] = "Enabled";
    parameters["cmk_keyvault"] = dataModel.advanced.keyVaultArmId();
    parameters["resource_cmk_uri"] = dataModel.advanced.keyUri();
    parameters["enableServiceSideCMKEncryption"] = dataModel.advanced.serviceSideEncryption();
  }

  if (hbiWorkspace) {
    parameters["confidential_data"] = hbiWorkspace.toString();
  }

  parameters["identityType"] = identityType();
  if (identityType() === "userAssigned") {
    parameters["primaryUserAssignedIdentityName"] = userAssignedIdentityName();
    parameters["primaryUserAssignedIdentityResourceGroup"] = userAssignedIdentityResourceGroup();
  }

  if (systemDatastoresAuthMode() !== defaultStorageAccessType) {
    parameters["systemDatastoresAuthMode"] = systemDatastoresAuthMode();
  }

  parameters["allowSharedKeyAccess"] = disableSharedKeyAccess() ? 'false' : 'true';

  // Azure Databricks (ADB) integration
  // Write ADB parameters into the ARM template so the Azure Machine Learning
  // workspace can be associated with the ADB workspace
  const adbWorkspaceLocation = dataModel.basics.adbWorkspaceLocation();
  const adbWorkspaceId = dataModel.basics.adbWorkspaceId();
  if (adbWorkspaceId && adbWorkspaceLocation) {
    parameters["adbWorkspaceId"] = adbWorkspaceId;
  }

  const publicNetworkAccess = (
    networking.connectivityMethod() == ConnectivityMethod.PrivateEndpoint
  ) ? "Disabled" : "Enabled";
  parameters["publicNetworkAccess"] = publicNetworkAccess;

  const providersPath = `/subscriptions/${subscriptionId}/resourcegroups/${resourceGroupName.name}/providers`;
  const resourceIdFormattedString = `${providersPath}/${Constants.machineLearningWorkspaceType}/${workspaceName}`;

  if (workspaceKind === WorkspaceMappedKind.Project) {    
    // Projects use a different template, so we need a different parameter map
    const projectParameters: StringMap<any> = {
      name: workspaceName,
      friendlyName: friendlyName(),
      hubResourceGroup: hub().resourceGroupName,
      hubResourceName: hub().name,
      hubResourceId: hub().id,
      hubSubscriptionId: ArmId.parse(hub().id).subscription,
      location: locationName,
      tagValues: generateTagsMap(context),
      identityType: parameters["identityType"],
      primaryUserAssignedIdentityName: parameters["primaryUserAssignedIdentityName"],
      primaryUserAssignedIdentityResourceGroup: parameters["primaryUserAssignedIdentityResourceGroup"],
      systemDatastoresAuthMode: parameters["systemDatastoresAuthMode"],
    };
    return {
      subscriptionId,
      resourceGroupName: resourceGroupName.name,
      resourceGroupLocation: locationName,
      parameters: projectParameters,
      deploymentName: Constants.machineLearningServicesResourcesProvider,
      resourceProviders: [Constants.machineLearningServicesResourcesProvider],
      resourceId: resourceIdFormattedString,
      templateJson: getProjectResourceTemplateJson(),
      provisioningHash: provisioningHashUniqueString,
    };
  } else {
    return {
      subscriptionId,
      resourceGroupName: resourceGroupName.name,
      resourceGroupLocation: locationName,
      parameters,
      deploymentName: Constants.machineLearningServicesResourcesProvider,
      resourceProviders: [Constants.machineLearningServicesResourcesProvider],
      resourceId: resourceIdFormattedString,
      templateJson: getResourceTemplateJsonV2({
        kind: workspaceKind,
        customerManagedKeyVault,
        hasAdbWorkspace: !!(adbWorkspaceId && adbWorkspaceLocation),
        hbiWorkspace,
        privateEndpointDetails,
        CMKServiceSideEncryptionFlightEnabled: options.isCMKServiceSideEncryptionEnabled() === 2 || (options.isCMKServiceSideEncryptionEnabled() === 1 && workspaceKind === WorkspaceMappedKind.Default)
      }),
      provisioningHash: provisioningHashUniqueString,
    };
  }
}

export function getTelemetryLoggingOptions(context: WizardContext, deploymentOptions: Provisioning.DeployTemplateOptions): TelemetryLoggingOptions {
  const { dataModel } = context;
  const { basics, networking } = dataModel;

  const { parameters = {} } = deploymentOptions;
  const optionsToLog: StringMap<any> = {};
  optionsToLog["storageAccount"] = parameters["storageAccountOption"] ?? "new";
  optionsToLog["keyVault"] = parameters["keyVaultOption"] ?? "new";
  optionsToLog["applicationInsights"] = parameters["applicationInsightsOption"] ?? "none";
  optionsToLog["createLogWorkspace"] = parameters["applicationInsightsOption"] ?? "new";
  optionsToLog["containerRegistry"] = parameters["containerRegistryOption"] ?? "none";
  optionsToLog["hasPrivateEndpoint"] = Boolean(networking.getPrivateEndpointDetails());
  optionsToLog["hasManagedNetwork"] = networking.networkIsolationMode() !== "Disabled";
  optionsToLog["managedKeyVaultType"] = parameters["managedKeyVaultType"] ?? "none";

  return {
    optionsToLog,
    parametersToLog: ["kind"]
  }
}


interface IWorkspaceTemplateOptions {
  customerManagedKeyVault: boolean;
  hasAdbWorkspace: boolean;
  hbiWorkspace: boolean;
  kind: WorkspaceMappedKind;
  privateEndpointDetails?: PrivateEndpointDetails;
  CMKServiceSideEncryptionFlightEnabled: boolean;
}

function getResourceTemplateJsonV2(options: IWorkspaceTemplateOptions): string {
  const {
    customerManagedKeyVault,
    hasAdbWorkspace,
    hbiWorkspace,
    privateEndpointDetails,
    kind,
    CMKServiceSideEncryptionFlightEnabled
  } = options;

  let createWorkspaceTemplate: any;
  createWorkspaceTemplate = {
    "$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#",
    "contentVersion": "*******",
    "parameters": {
      "workspaceName": {
        "type": "string",
        "metadata": {
          "description": "Specifies the name of the Azure Machine Learning workspace."
        }
      },
      "friendlyName": {
        "type": "string",
        "defaultValue": "[parameters('workspaceName')]"
      },
      "description": {
        "type": "string",
        "defaultValue": ""
      },
      "kind": {
        "type": "string",
        "defaultValue": "Default",
        "allowedValues": [
          "Default",
          "FeatureStore",
          "Hub",
          "Project"
        ]
      },
      "location": {
        "type": "string",
        "metadata": {
          "description": "Specifies the location for all resources."
        }
      },
      "resourceGroupName": {
        "type": "string",
        "metadata": {
          "description": "Specifies the resource group name of the Azure Machine Learning workspace."
        }
      },
      "appInsightsLogWorkspaceName": {
        "type": "string",
        "defaultValue": "[concat('ai', uniqueString(parameters('resourceGroupName'), parameters('workspaceName')))]",
        "metadata": {
          "description": "Specifies log workspace name of the log workspace created for the Application Insights."
        }
      },
      "sku": {
        "type": "string",
        "defaultValue": "Basic",
        "allowedValues": [
          "Basic",
          "Enterprise"
        ],
        "metadata": {
          "description": "Specifies the sku, also referred as 'edition' of the Azure Machine Learning workspace."
        }
      },
      "identityType": {
        "type": "string",
        "defaultValue": "systemAssigned",
        "allowedValues": [
          "systemAssigned",
          "userAssigned"
        ],
        "metadata": {
          "description": "Specifies the identity type of the Azure Machine Learning workspace."
        }
      },
      "primaryUserAssignedIdentityResourceGroup": {
        "type": "string",
        "defaultValue": "[parameters('resourceGroupName')]",
        "metadata": {
          "description": "Specifies the resource group of user assigned identity that represents the Azure Machine Learing workspace."
        }
      },
      "primaryUserAssignedIdentityName": {
        "type": "string",
        "defaultValue": "",
        "metadata": {
          "description": "Specifies the name of user assigned identity that represents the Azure Machine Learing workspace."
        }
      },
      "storageAccountOption": {
        "type": "string",
        "defaultValue": "new",
        "allowedValues": [
          "new",
          "existing"
        ],
        "metadata": {
          "description": "Determines whether or not a new storage should be provisioned."
        }
      },
      "storageAccountName": {
        "type": "string",
        "defaultValue": "[concat('sa', uniqueString(parameters('resourceGroupName'), parameters('workspaceName')))]",
        "metadata": {
          "description": "Name of the storage account."
        }
      },
      "storageAccountType": {
        "type": "string",
        "defaultValue": "Standard_LRS",
        "allowedValues": [
          "Standard_LRS",
          "Standard_GRS",
          "Standard_RAGRS",
          "Standard_ZRS",
          "Standard_GZRS",
          "Standard_RAGZRS"
        ]
      },
      "storageAccountBehindVNet": {
        "type": "string",
        "defaultValue": "false",
        "allowedValues": [
          "true",
          "false"
        ],
        "metadata": {
          "description": "Determines whether or not to put the storage account behind VNet"
        }
      },
      "storageAccountResourceGroupName": {
        "type": "string",
        "defaultValue": "[parameters('resourceGroupName')]"
      },
      "storageAccountLocation": {
        "type": "string",
        "defaultValue": "[parameters('location')]"
      },
      "storageAccountHnsEnabled": {
        "type": "bool",
        "defaultValue": false
      },
      "enableServiceSideCMKEncryption": {
        "type": "bool",
        "defaultValue": false
      },
      "managedKeyVaultType": {
        "type": "string",
        "defaultValue": "managedKeyVault",
        "allowedValues": [
          "managedKeyVault",
          "byoKeyVault"
        ],
        "metadata": {
          "description": "Specifies whether or not managed key vault is used."
        }
      },
      "keyVaultOption": {
        "type": "string",
        "defaultValue": "new",
        "allowedValues": [
          "new",
          "existing"
        ],
        "metadata": {
          "description": "Determines whether or not a new key vault should be provisioned."
        }
      },
      "keyVaultName": {
        "type": "string",
        "defaultValue": "[concat('kv', uniqueString(parameters('resourceGroupName'), parameters('workspaceName')))]",
        "metadata": {
          "description": "Name of the key vault."
        }
      },
      "keyVaultBehindVNet": {
        "type": "string",
        "defaultValue": "false",
        "allowedValues": [
          "true",
          "false"
        ],
        "metadata": {
          "description": "Determines whether or not to put the storage account behind VNet"
        }
      },
      "keyVaultResourceGroupName": {
        "type": "string",
        "defaultValue": "[parameters('resourceGroupName')]"
      },
      "keyVaultLocation": {
        "type": "string",
        "defaultValue": "[parameters('location')]"
      },
      "applicationInsightsOption": {
        "type": "string",
        "defaultValue": "none",
        "allowedValues": [
          "new",
          "existing",
          "none"
        ],
        "metadata": {
          "description": "Determines whether or not new ApplicationInsights should be provisioned."
        }
      },
      "applicationInsightsName": {
        "type": "string",
        "defaultValue": "[concat('ai', uniqueString(parameters('resourceGroupName'), parameters('workspaceName')))]",
        "metadata": {
          "description": "Name of ApplicationInsights."
        }
      },
      "applicationInsightsResourceGroupName": {
        "type": "string",
        "defaultValue": "[parameters('resourceGroupName')]"
      },
      "applicationInsightsLocation": {
        "type": "string",
        "defaultValue": "[parameters('location')]"
      },
      "containerRegistryOption": {
        "type": "string",
        "defaultValue": "none",
        "allowedValues": [
          "new",
          "existing",
          "none"
        ],
        "metadata": {
          "description": "Determines whether or not a new container registry should be provisioned."
        }
      },
      "containerRegistryName": {
        "type": "string",
        "defaultValue": "[concat('cr', uniqueString(parameters('resourceGroupName'), parameters('workspaceName')))]",
        "metadata": {
          "description": "The container registry bind to the workspace."
        }
      },
      "containerRegistrySku": {
        "type": "string",
        "defaultValue": "Standard",
        "allowedValues": [
          "Basic",
          "Standard",
          "Premium"
        ]
      },
      "containerRegistryResourceGroupName": {
        "type": "string",
        "defaultValue": "[parameters('resourceGroupName')]"
      },
      "containerRegistryBehindVNet": {
        "type": "string",
        "defaultValue": "false",
        "allowedValues": [
          "true",
          "false"
        ],
        "metadata": {
          "description": "Determines whether or not to put container registry behind VNet."
        }
      },
      "containerRegistryLocation": {
        "type": "string",
        "defaultValue": "[parameters('location')]"
      },
      "vnetOption": {
        "type": "string",
        "defaultValue": "[if(equals(parameters('privateEndpointType'), 'none'), 'none', 'new')]",
        "allowedValues": [
          "new",
          "existing",
          "none"
        ],
        "metadata": {
          "description": "Determines whether or not a new VNet should be provisioned."
        }
      },
      "vnetName": {
        "type": "string",
        "defaultValue": "[concat('vn',uniqueString(parameters('resourceGroupName'), parameters('workspaceName')))]",
        "metadata": {
          "description": "Name of the VNet"
        }
      },
      "vnetResourceGroupName": {
        "type": "string",
        "defaultValue": "[parameters('resourceGroupName')]"
      },
      "delegateSubnetId": {
        "type": "string",
        "defaultValue": "[parameters('delegateSubnetId')]"
      },
      "delegateSubnetName": {
        "type": "string",
        "defaultValue": "[parameters('delegateSubnetName')]"
      },
      "addressPrefixes": {
        "type": "array",
        "defaultValue": [
          "10.0.0.0/16"
        ],
        "metadata": {
          "description": "Address prefix of the virtual network"
        }
      },
      "subnetOption": {
        "type": "string",
        "defaultValue": "[if(or(not(equals(parameters('privateEndpointType'), 'none')), equals(parameters('vnetOption'), 'new')), 'new', 'none')]",
        "allowedValues": [
          "new",
          "existing",
          "none"
        ],
        "metadata": {
          "description": "Determines whether or not a new subnet should be provisioned."
        }
      },
      "subnetName": {
        "type": "string",
        "defaultValue": "[concat('sn',uniqueString(parameters('resourceGroupName'), parameters('workspaceName')))]",
        "metadata": {
          "description": "Name of the subnet"
        }
      },
      "subnetPrefix": {
        "type": "string",
        "defaultValue": "10.0.0.0/24",
        "metadata": {
          "description": "Subnet prefix of the virtual network"
        }
      },
      "adbWorkspace": {
        "type": "string",
        "defaultValue": "",
        "metadata": {
          "description": "Azure Databrick workspace to be linked to the workspace"
        }
      },
      "confidential_data": {
        "type": "string",
        "defaultValue": "false",
        "allowedValues": [
          "false",
          "true"
        ],
        "metadata": {
          "description": "Specifies that the Azure Machine Learning workspace holds highly confidential data."
        }
      },
      "encryption_status": {
        "type": "string",
        "defaultValue": "Disabled",
        "allowedValues": [
          "Enabled",
          "Disabled"
        ],
        "metadata": {
          "description": "Specifies if the Azure Machine Learning workspace should be encrypted with customer managed key."
        }
      },
      "cmk_keyvault": {
        "type": "string",
        "defaultValue": "",
        "metadata": {
          "description": "Specifies the customer managed keyVault arm id."
        }
      },
      "resource_cmk_uri": {
        "type": "string",
        "defaultValue": "",
        "metadata": {
          "description": "Specifies if the customer managed keyvault key uri."
        }
      },
      "privateEndpointType": {
        "type": "string",
        "defaultValue": "none",
        "allowedValues": [
          "AutoApproval",
          "ManualApproval",
          "none"
        ]
      },
      "tagValues": {
        "type": "object"
      },
      "privateEndpointName": {
        "type": "string",
        "defaultValue": "pe",
        "metadata": {
          "description": "Name of the private end point added to the workspace"
        }
      },
      "privateEndpointResourceGroupName": {
        "type": "string",
        "defaultValue": "[parameters('resourceGroupName')]",
        "metadata": {
          "description": "Name of the resource group where the private end point is added to"
        }
      },
      "privateEndpointSubscription": {
        "type": "string",
        "defaultValue": "[subscription().subscriptionId]",
        "metadata": {
          "description": "Id of the subscription where the private end point is added to"
        }
      },
      "systemDatastoresAuthMode": {
        "type": "string",
        "defaultValue": defaultStorageAccessType,
        "metadata": {
          "description": "Identity type of storage account services."
        }
      },
      "allowSharedKeyAccess": {
        "type": "string",
        "defaultValue": "false",
        "allowedValues": [
          "true",
          "false"
        ],
        "metadata": {
          "description": "Determines whether or not to disable shared key based access to storage account"
        }
      },
      "managedNetwork": {
        "type": "object",
        "defaultValue": {
          "isolationMode": "Disabled"
        },
        "metadata": {
          "description": "Managed network settings to be used for the workspace. If not specified, isolation mode Disabled is the default"
        }
      },
      "provisionNetworkNow": {
        "type": "String",
        "defaultValue": "false",
        "allowedValues": [
            "true",
            "false"
        ],
        "metadata": {
            "description": "Set to trigger the provisioning of managed vnet when creating a workspace"
        }
      },
    },
    "variables": {
      "tenantId": "[subscription().tenantId]",
      "storageAccount": "[resourceId(parameters('storageAccountResourceGroupName'), 'Microsoft.Storage/storageAccounts', parameters('storageAccountName'))]",
      "keyVault": "[resourceId(parameters('keyVaultResourceGroupName'), 'Microsoft.KeyVault/vaults', parameters('keyVaultName'))]",
      "containerRegistry": "[resourceId(parameters('containerRegistryResourceGroupName'), 'Microsoft.ContainerRegistry/registries', parameters('containerRegistryName'))]",
      "applicationInsights": "[resourceId(parameters('applicationInsightsResourceGroupName'), 'Microsoft.Insights/components', parameters('applicationInsightsName'))]",
      "vnet": "[resourceId(parameters('privateEndpointSubscription'), parameters('vnetResourceGroupName'), 'Microsoft.Network/virtualNetworks', parameters('vnetName'))]",
      "subnet": "[resourceId(parameters('privateEndpointSubscription'), parameters('vnetResourceGroupName'), 'Microsoft.Network/virtualNetworks/subnets', parameters('vnetName'), parameters('subnetName'))]",
      "networkRuleSetBehindVNet": {
        "defaultAction": "deny",
        "virtualNetworkRules": [
          {
            "action": "Allow",
            "id": "[variables('subnet')]"
          }
        ]
      },
      "privateEndpointSettings": {
        "name": "[concat(parameters('workspaceName'), '-PrivateEndpoint')]",
        "properties": {
          "privateLinkServiceId": "[resourceId('Microsoft.MachineLearningServices/workspaces', parameters('workspaceName'))]",
          "groupIds": [
            "amlworkspace"
          ]
        }
      },
      "defaultPEConnections": "[array(variables('privateEndpointSettings'))]",
      "privateEndpointDeploymentName": "[concat('DeployPrivateEndpoint-', uniqueString(parameters('privateEndpointName')))]",
      "userAssignedIdentities": {
        "[variables('primaryUserAssignedIdentity')]": {}
      },
      "primaryUserAssignedIdentity": "[resourceId(parameters('primaryUserAssignedIdentityResourceGroup'), 'Microsoft.ManagedIdentity/userAssignedIdentities', parameters('primaryUserAssignedIdentityName'))]"
    },
    "resources": [
      {
        "condition": "[equals(parameters('storageAccountOption'), 'new')]",
        "type": "Microsoft.Storage/storageAccounts",
        "apiVersion": "2019-04-01",
        "name": "[parameters('storageAccountName')]",
        "tags": "[parameters('tagValues')]",
        "location": "[parameters('storageAccountLocation')]",
        "sku": {
          "name": "[parameters('storageAccountType')]"
        },
        "kind": "StorageV2",
        "properties": {
          "encryption": {
            "services": {
              "blob": {
                "enabled": true
              },
              "file": {
                "enabled": true
              }
            },
            "keySource": "Microsoft.Storage"
          },
          "supportsHttpsTrafficOnly": true,
          "allowBlobPublicAccess": false,
          "networkAcls": "[if(equals(parameters('storageAccountBehindVNet'), 'true'), variables('networkRuleSetBehindVNet'), json('null'))]",
          "isHnsEnabled": "[parameters('storageAccountHnsEnabled')]",
          "allowSharedKeyAccess": "[parameters('allowSharedKeyAccess')]",
          "minimumTlsVersion": "TLS1_2"
        }
      },
      {
        "condition": "[and(equals(parameters('managedKeyVaultType'), 'byoKeyVault'), equals(parameters('keyVaultOption'), 'new'))]",
        "type": "Microsoft.KeyVault/vaults",
        "apiVersion": "2019-09-01",
        "tags": "[parameters('tagValues')]",
        "name": "[parameters('keyVaultName')]",
        "location": "[parameters('keyVaultLocation')]",
        "properties": {
          "tenantId": "[variables('tenantId')]",
          "sku": {
            "name": "standard",
            "family": "A"
          },
          "accessPolicies": [],
          "networkAcls": "[if(equals(parameters('keyVaultBehindVNet'), 'true'), variables('networkRuleSetBehindVNet'), json('null'))]"
        }
      },
      {
        "condition": "[equals(parameters('containerRegistryOption'), 'new')]",
        "type": "Microsoft.ContainerRegistry/registries",
        "apiVersion": "2019-05-01",
        "tags": "[parameters('tagValues')]",
        "name": "[parameters('containerRegistryName')]",
        "location": "[parameters('containerRegistryLocation')]",
        "sku": {
          "name": "[parameters('containerRegistrySku')]"
        },
        "properties": {
          "adminUserEnabled": true,
          "networkRuleSet": "[if(equals(parameters('containerRegistryBehindVNet'), 'true'), variables('networkRuleSetBehindVNet'), json('null'))]"
        }
      },
      {
        "condition": "[equals(parameters('applicationInsightsOption'), 'new')]",
        "type": "Microsoft.OperationalInsights/workspaces",
        "tags": "[parameters('tagValues')]",
        "apiVersion": "2020-08-01",
        "name": "[parameters('appInsightsLogWorkspaceName')]",
        "location": appInsightsLocationCondition
      },
      {
        "condition": "[equals(parameters('applicationInsightsOption'), 'new')]",
        "type": "Microsoft.Insights/components",
        "tags": "[parameters('tagValues')]",
        "apiVersion": "2020-02-02-preview",
        "location": appInsightsLocationCondition,
        "name": "[parameters('applicationInsightsName')]",
        "dependsOn": [
          "[resourceId('Microsoft.OperationalInsights/workspaces', parameters('appInsightsLogWorkspaceName'))]"
        ],
        "properties": {
          "ApplicationId": "[parameters('applicationInsightsName')]",
          "Application_Type": "web",
          "Flow_Type": "Redfield",
          "Request_Source": "IbizaMachineLearningExtension",
          "WorkspaceResourceId": "[resourceId('Microsoft.OperationalInsights/workspaces', parameters('appInsightsLogWorkspaceName'))]"
        }
      },
      {
        "type": "Microsoft.MachineLearningServices/workspaces",
        "apiVersion": "2024-10-01-preview",
        "tags": "[parameters('tagValues')]",
        "name": "[parameters('workspaceName')]",
        "location": "[parameters('location')]",
        "kind": "[parameters('kind')]",
        "dependsOn": [
          "[resourceId('Microsoft.Storage/storageAccounts', parameters('storageAccountName'))]",
          "[resourceId('Microsoft.KeyVault/vaults', parameters('keyVaultName'))]",
          "[resourceId('Microsoft.Insights/components', parameters('applicationInsightsName'))]",
          "[resourceId('Microsoft.ContainerRegistry/registries', parameters('containerRegistryName'))]"
        ],
        "identity": {
          "type": "[parameters('identityType')]",
          "userAssignedIdentities": "[if(equals(parameters('identityType'), 'userAssigned'), variables('userAssignedIdentities'), json('null'))]"
        },
        "sku": {
          "tier": "[parameters('sku')]",
          "name": "[parameters('sku')]"
        },
        "properties": {
          "friendlyName": "[parameters('friendlyName')]",
          "description": "[parameters('description')]",
          "storageAccount": "[variables('storageAccount')]",
          "keyVault": "[if(equals(parameters('managedKeyVaultType'), 'byoKeyVault'), variables('keyVault'), json('null'))]",
          "applicationInsights": "[if(not(equals(parameters('applicationInsightsOption'), 'none')), variables('applicationInsights'), json('null'))]",
          "containerRegistry": "[if(not(equals(parameters('containerRegistryOption'), 'none')), variables('containerRegistry'), json('null'))]",
          "primaryUserAssignedIdentity": "[if(equals(parameters('identityType'), 'userAssigned'), variables('primaryUserAssignedIdentity'), json('null'))]",
          "systemDatastoresAuthMode": "[if(not(equals(parameters('systemDatastoresAuthMode'), '" + defaultStorageAccessType + "')), parameters('systemDatastoresAuthMode'), json('null'))]",
          "managedNetwork": "[parameters('managedNetwork')]",
          "provisionNetworkNow": "[parameters('provisionNetworkNow')]"
        }
      },
      {
        "condition": "[not(equals(parameters('delegateSubnetId'), 'none'))]",
        "dependsOn": [
          "[resourceId('Microsoft.MachineLearningServices/workspaces', parameters('workspaceName'))]"
        ],
        "type": "Microsoft.MachineLearningServices/workspaces/capabilityHosts",
        "apiVersion": "2024-10-01-preview",
        "name": "[concat(parameters('workspaceName'), '/', parameters('delegateSubnetName'))]",
        "location": "[parameters('location')]",
        "properties": {
          "customerSubnet": "[parameters('delegateSubnetId')]"
        }
      },
      {
        "condition": "[not(equals(parameters('privateEndpointType'), 'none'))]",
        "type": "Microsoft.Resources/deployments",
        "apiVersion": "2020-06-01",
        "name": "[variables('privateEndpointDeploymentName')]",
        "resourceGroup": "[parameters('privateEndpointResourceGroupName')]",
        "subscriptionId": "[parameters('privateEndpointSubscription')]",
        "dependsOn": [
          "[resourceId('Microsoft.MachineLearningServices/workspaces', parameters('workspaceName'))]"
        ],
        "properties": {
          "mode": "Incremental",
          "template": {
            "$schema": "https://schema.management.azure.com/schemas/2015-01-01/deploymentTemplate.json#",
            "contentVersion": "*******",
            "resources": [
              {
                "apiVersion": "2020-06-01",
                "name": "[parameters('privateEndpointName')]",
                "type": "Microsoft.Network/privateEndpoints",
                "location": "[parameters('location')]",
                "tags": "[parameters('tagValues')]",
                "properties": {
                  "privateLinkServiceConnections": "[if(equals(parameters('privateEndpointType'), 'AutoApproval'), variables('defaultPEConnections'), json('null'))]",
                  "manualPrivateLinkServiceConnections": "[if(equals(parameters('privateEndpointType'), 'ManualApproval'), variables('defaultPEConnections'), json('null'))]",
                  "subnet": {
                    "id": "[variables('subnet')]"
                  }
                }
              }
            ]
          }
        }
      }
    ],
    "outputs": {
    }
  };

  const workspaceResource = (createWorkspaceTemplate.resources as IResource[]).filter(r => r.type === "Microsoft.MachineLearningServices/workspaces")[0];

  // If there are private endpoints, add private dns zone related entries.
  // They needed to be added dynamically since they are different by public and private clouds.
  if (privateEndpointDetails) {
    createWorkspaceTemplate.resources.push(...getPrivateDnsZoneDeployments(privateEndpointDetails));
  }

  if (kind === WorkspaceMappedKind.Hub) {
    createWorkspaceTemplate.parameters["defaultProjectResourceGroup"] = {
      "type": "string",
      "defaultValue": "",
      "metadata": {
        "description": "Default resource group for projects added to this Azure AI hub"
      }
    };
    createWorkspaceTemplate.parameters["aiServicesOption"] = {
      "type": "string",
      "defaultValue": "new",
      "allowedValues": [
        "new",
        "existing"
      ],
      "metadata": {
        "description": "Determines whether or not new AI Services should be provisioned."
      }
    };
    createWorkspaceTemplate.parameters["aiServicesKind"] = {
      "type": "string",
      "allowedValues": [
        "OpenAI",
        "AIServices"
      ]
    };
    createWorkspaceTemplate.parameters["endpointOption"] = {
      "type": "string",
      "allowedValues": [
        "create",
        "none"
      ]
    };
    createWorkspaceTemplate.parameters["aiServicesName"] = {
      "type": "string",
      "defaultValue": "[concat('aoai', uniqueString(parameters('resourceGroupName'), parameters('workspaceName')))]",
      "metadata": {
        "description": "Name of AI Services."
      }
    };
    createWorkspaceTemplate.parameters["aiServicesLocation"] = {
      "type": "string",
      "defaultValue": "[parameters('location')]"
    },
    createWorkspaceTemplate.parameters["aiServicesResourceGroupName"] = {
      "type": "string",
      "defaultValue": "[parameters('resourceGroupName')]"
    },
    createWorkspaceTemplate.variables["aiServices"] = "[resourceId(parameters('aiServicesResourceGroupName'), 'Microsoft.CognitiveServices/accounts', parameters('aiServicesName'))]";
    const cogServicesAccountsApiVersion = MsPortalFx.getEnvironmentValue("cogServicesAccountsApiVersion");
    createWorkspaceTemplate.resources.push({
      "condition": "[equals(parameters('aiServicesOption'), 'new')]",
      "type": "Microsoft.CognitiveServices/accounts",
      "apiVersion": cogServicesAccountsApiVersion,
      "name": "[parameters('aiServicesName')]",
      "location": "[parameters('aiServicesLocation')]",
      "kind": "[parameters('aiServicesKind')]",
      "sku": {
        "name": "S0"
      },
      "properties": {
        "customSubDomainName": "[toLower(parameters('aiServicesName'))]",
        "apiProperties": {}
      }
    });
    if (workspaceResource) {
      workspaceResource.properties.workspaceHubConfig = {
        defaultWorkspaceResourceGroup: "[if(empty(parameters('defaultProjectResourceGroup')), null(), parameters('defaultProjectResourceGroup'))]"
      };
    }
  }

  if (hasAdbWorkspace) {
    createWorkspaceTemplate.parameters["adbWorkspaceId"] = {
      "type": "string",
      "defaultValue": "",
      "metadata": {
        "description": "Azure Databrick workspace to be linked to the workspace"
      }
    };

    if (workspaceResource) {
      workspaceResource.properties.adbWorkspace = "[parameters('adbWorkspaceId')]";
    }
  }

  if (hbiWorkspace) {
    createWorkspaceTemplate.parameters["confidential_data"] = {
      "type": "string",
      "defaultValue": "false",
      "allowedValues": [
        "false",
        "true"
      ],
      "metadata": {
        "description": "Specifies that the Azure Machine Learning workspace holds highly confidential data."
      }
    };

    if (workspaceResource) {
      workspaceResource.properties.hbiWorkspace = "[parameters('confidential_data')]";
    }
  }

  if (customerManagedKeyVault) {
    createWorkspaceTemplate.parameters["encryption_status"] = {
      "type": "string",
      "defaultValue": "Disabled",
      "allowedValues": [
        "Enabled",
        "Disabled"
      ],
      "metadata": {
        "description": "Specifies if the Azure Machine Learning workspace should be encrypted with customer managed key."
      }
    };

    createWorkspaceTemplate.parameters["cmk_keyvault"] = {
      "type": "string",
      "defaultValue": "",
      "metadata": {
        "description": "Specifies the customer managed keyVault arm id."
      }
    };

    createWorkspaceTemplate.parameters["resource_cmk_uri"] = {
      "type": "string",
      "defaultValue": "",
      "metadata": {
        "description": "Specifies if the customer managed keyvault key uri."
      }
    };

    if (workspaceResource) {
      workspaceResource.properties.encryption = {
        "status": "[parameters('encryption_status')]",
        "keyVaultProperties": {
          "keyVaultArmId": "[parameters('cmk_keyvault')]",
          "keyIdentifier": "[parameters('resource_cmk_uri')]"
        }
      };
      if (CMKServiceSideEncryptionFlightEnabled) {
        workspaceResource.properties.enableServiceSideCMKEncryption = "[parameters('enableServiceSideCMKEncryption')]"
      }
    }
  }

  createWorkspaceTemplate.parameters["publicNetworkAccess"] = {
    "type": "string",
    "defaultValue": "Enabled",
    "metadata": {
      "description": "Specifies whether the workspace can be accessed by public networks or not."
    }
  }

  if (workspaceResource) {
    workspaceResource.properties.publicNetworkAccess = "[parameters('publicNetworkAccess')]";
  }

  return JSON.stringify(createWorkspaceTemplate);
}

function getProjectResourceTemplateJson(): string {
  let createProjectTemplate: any;
  createProjectTemplate = {
    "$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#",
    "contentVersion": "*******",
    "metadata": {
      "_generator": {
        "name": "bicep",
        "version": "0.13.1.58284",
        "templateHash": "17277793070204216233"
      }
    },
    "parameters": {
      "name": {
        "type": "string",
        "metadata": {
          "description": "Specifies the name of the deployment."
        }
      },
      "friendlyName": {
        "type": "string",
        "metadata": {
          "description": "Specifies the friendly name of the deployment."
        }
      },
      "hubResourceGroup": {
        "type": "string"
      },
      "hubSubscriptionId": {
        "type": "string"
      },
      "hubResourceName": {
        "type": "string"
      },
      "hubResourceId": {
        "type": "string"
      },      
      "location": {
        "type": "string",
        "metadata": {
          "description": "Specifies the location of the Azure Machine Learning workspace and dependent resources."
        }
      },
      "tagValues": {
        "type": "object"
      },
      "identityType": {
        "type": "string",
        "defaultValue": "systemAssigned",
        "allowedValues": [
          "systemAssigned",
          "userAssigned"
        ],
        "metadata": {
          "description": "Specifies the identity type of the Azure Machine Learning workspace."
        }
      },
      "primaryUserAssignedIdentityResourceGroup": {
        "type": "string",
        "defaultValue": "[parameters('hubResourceGroup')]",
        "metadata": {
          "description": "Specifies the resource group of user assigned identity that represents the Azure Machine Learning workspace."
        }
      },
      "primaryUserAssignedIdentityName": {
        "type": "string",
        "defaultValue": "",
        "metadata": {
          "description": "Specifies the name of user assigned identity that represents the Azure Machine Learing workspace."
        }
      },
      "systemDatastoresAuthMode": {
        "type": "string",
        "defaultValue": defaultStorageAccessType,
        "metadata": {
          "description": "Identity type of storage account services."
        }
      },
    },
    "variables": {
      "tenantId": "[subscription().tenantId]",
      "workspaceName": "[parameters('name')]",
      "workspaceNameFriendly": "[parameters('friendlyName')]",
      "hubResource": "[resourceId(parameters('hubSubscriptionId'), parameters('hubResourceGroup'), 'Microsoft.MachineLearningServices/workspaces', parameters('hubResourceName'))]",
      "userAssignedIdentities": {
        "[variables('primaryUserAssignedIdentity')]": {}
      },
      "primaryUserAssignedIdentity": "[resourceId(parameters('primaryUserAssignedIdentityResourceGroup'), 'Microsoft.ManagedIdentity/userAssignedIdentities', parameters('primaryUserAssignedIdentityName'))]"
    },
    "resources": [
      {
        "type": "Microsoft.MachineLearningServices/workspaces",
        "apiVersion": "2023-02-01-preview",
        "name": "[variables('workspaceName')]",
        "identity": {
          "type": "[parameters('identityType')]",
          "userAssignedIdentities": "[if(equals(parameters('identityType'), 'userAssigned'), variables('userAssignedIdentities'), json('null'))]"
        },        
        "location": "[parameters('location')]",
        "tags": "[parameters('tagValues')]",
        "kind": "Project",
        "properties": {
          "friendlyName": "[variables('workspaceNameFriendly')]",
          "hubResourceId": "[variables('hubResource')]",
          "primaryUserAssignedIdentity": "[if(equals(parameters('identityType'), 'userAssigned'), variables('primaryUserAssignedIdentity'), json('null'))]",
          "systemDatastoresAuthMode": "[if(not(equals(parameters('systemDatastoresAuthMode'), '" + defaultStorageAccessType + "')), parameters('systemDatastoresAuthMode'), json('null'))]"
        }
      }
    ]
  };
  return JSON.stringify(createProjectTemplate);
}

function generateTagsMap(context: WizardContext): ReadonlyStringMap<string> {
  const { dataModel } = context;
  const result: StringMap<string> = {};
  const keyMap: StringMap<string> = {};
  dataModel.tags.tags().forEach(item => {
    result[item.key] = item.value;
    keyMap[item.key.toLowerCase()] = item.key;
  });

  if (dataModel.tags.clientTag) {
    const clientKeyInResult = keyMap["client"];
    if (clientKeyInResult) {
      delete result[clientKeyInResult];
    }

    result["client"] = dataModel.tags.clientTag;
  }

  return result;
}

function getPrivateDnsZoneDeployments(privateEndpointDetails: PrivateEndpointDetails): any[] {
  const deployments: any[] = [];
  const { integrateWithPrivateDnsZones, privateDnsZones } = privateEndpointDetails;
  if (integrateWithPrivateDnsZones) {
    // See whether we need to create new zones or we need to use existing ones
    const newPrivateDnsZones = (privateDnsZones || []).filter(dns => dns.isNew);
    if (newPrivateDnsZones.length > 0) {
      const deployDnsZonesResources: any[] = [];
      for (const dnsZone of newPrivateDnsZones) {
        const dnsZoneName = getPrivateDnsZoneName(dnsZone);
        const dnsZoneArmId = ArmId.parse(dnsZone.id);
        const resources: any[] = [];

        resources.push({
          "condition": "[equals(parameters('privateEndpointType'), 'AutoApproval')]",
          "type": "Microsoft.Network/privateDnsZones",
          "apiVersion": "2018-09-01",
          "name": dnsZoneName,
          "tags": "[parameters('tagValues')]",
          "location": "global",
          "properties": {}
        });

        resources.push({
          "condition": "[equals(parameters('privateEndpointType'), 'AutoApproval')]",
          "type": "Microsoft.Network/privateDnsZones/virtualNetworkLinks",
          "apiVersion": "2018-09-01",
          "name": "[concat('" + dnsZoneName + "', '/', uniqueString(resourceId('Microsoft.Network/virtualNetworks', parameters('vnetName'))))]",
          "location": "global",
          "tags": "[parameters('tagValues')]",
          "dependsOn": [
            dnsZoneName
          ],
          "properties": {
            "virtualNetwork": {
              "id": "[variables('vnet')]"
            },
            "registrationEnabled": false
          }
        });

        deployDnsZonesResources.push({
          "type": "Microsoft.Resources/deployments",
          "apiVersion": "2020-06-01",
          "name": "[concat('DeployPrivateDnsZones-', uniqueString('" + dnsZone.id + "'))]",
          "subscriptionId": dnsZoneArmId.subscription,
          "resourceGroup": dnsZoneArmId.resourceGroup,
          "properties": {
            "mode": "Incremental",
            "template": {
              "$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#",
              "contentVersion": "*******",
              "resources": [...resources]
            }
          }
        });
      }

      deployments.push({
        "type": "Microsoft.Resources/deployments",
        "apiVersion": "2020-06-01",
        "name": "[concat('DeployPrivateDnsZones-', uniqueString(parameters('privateEndpointName')))]",
        "dependsOn": [
          "[variables('privateEndpointDeploymentName')]"
        ],
        "properties": {
          "mode": "Incremental",
          "template": {
            "$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#",
            "contentVersion": "*******",
            "resources": [...deployDnsZonesResources]
          }
        }
      });
    }

    // We need to add dns zone group for the PE in the same resource group of private endpoint
    const privateDnsZoneList: { id: string; name: string }[] = (privateDnsZones || []).map(dns => { return { name: getPrivateDnsZoneName(dns), id: dns.id } });
    if (privateDnsZoneList.length > 0) {
      deployments.push({
        "condition": "[equals(parameters('privateEndpointType'), 'AutoApproval')]",
        "type": "Microsoft.Resources/deployments",
        "apiVersion": "2020-06-01",
        "name": "[concat('DeployPrivateDnsZonesGroup-', uniqueString(parameters('privateEndpointName')))]",
        "resourceGroup": "[parameters('privateEndpointResourceGroupName')]",
        "subscriptionId": "[parameters('privateEndpointSubscription')]",
        "dependsOn": newPrivateDnsZones.length > 0
          ? ["[concat('DeployPrivateDnsZones-', uniqueString(parameters('privateEndpointName')))]"]
          : ["[variables('privateEndpointDeploymentName')]"],
        "properties": {
          "mode": "Incremental",
          "template": {
            "$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#",
            "contentVersion": "*******",
            "resources": [{
              "type": "Microsoft.Network/privateEndpoints/privateDnsZoneGroups",
              "apiVersion": "2020-03-01",
              "name": "[concat(parameters('privateEndpointName'), '/', 'default')]",
              "location": "[parameters('location')]",
              "properties": {
                "privateDnsZoneConfigs": [
                  ...privateDnsZoneList.map(dnsZone => {
                    return {
                      "name": dnsZone.name.replace(/\./g, "-"),
                      "properties": {
                        "privateDnsZoneId": dnsZone.id
                      }
                    };
                  })
                ]
              }
            }]
          }
        }
      });
    }
  }

  return deployments;
}
