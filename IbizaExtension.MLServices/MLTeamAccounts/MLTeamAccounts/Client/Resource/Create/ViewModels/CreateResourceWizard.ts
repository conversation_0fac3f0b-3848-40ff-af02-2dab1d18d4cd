import { DisposableLifetimeManager, LifetimeManager } from 'Fx/Lifetime';
import * as Constants from '../../../Shared/Constants';
import { WorkspaceMappedKind } from '../../../Shared/Enums';
import Utilities from '../../../Shared/Utilities';
import * as LoadableTab from '../Controls/LoadableTab';
import { Tab } from '../Models/ControlModels';
import { BasicsTab } from '../Tabs/BasicsTab';
import { EncryptionTab } from '../Tabs/EncryptionTab';
import { IdentityTab } from '../Tabs/IdentityTab';
import { StorageTab } from '../Tabs/StorageTab';
import * as SummaryTab from '../Tabs/SummaryTab';
import { WizardContext } from './CreateMachineLearningModels';
import { NetworkingInboundAccessTab } from '../Tabs/NetworkingInboundAccessTab';
import { NetworkingOutboundAccessTab } from '../Tabs/NetworkingOutboundAccessTab';

export class CreateResourceWizard {
    private _wizardContext: WizardContext;
    private _tabsLifetimeManager: DisposableLifetimeManager;
    private _workspaceKind: KnockoutObservableBase<WorkspaceMappedKind>;
    public _tabs: KnockoutObservableArray<Tab> = ko.observableArray([]);

    public constructor(props: {
        deploymentSourceName: string;
        wizardContext: WizardContext;
        workspaceKind: KnockoutObservableBase<WorkspaceMappedKind>;
    }) {
        this._wizardContext = props.wizardContext;
        this._workspaceKind = props.workspaceKind;
    }

    public initializeTabs(): void {
        // Re-initialize the set of tabs for the wizard any time the workspace kind changes
        // NOTE: must be done after createForm, so that the wizard context will have the validateForm callback (set during
        // createForm construction). This validateForm callback needs to be on the wizard context so when the tab contexts are created they
        // get a copy of it.
        this._workspaceKind.subscribeAndRun(this._wizardContext.lifetimeManager, (newKind) => {
            this.initTabsForKind(newKind)
        });
    }

    private initTabsForKind(kind: WorkspaceMappedKind): void {
        const tabsLifetimeManager = this._newTabsLifetimeManager();
        const { dataModel } = this._wizardContext;
        const { tags } = dataModel;
        const { resourceTypes } = tags;

        const mapping: StringMap<string> = {};
        mapping[Constants.machineLearningWorkspaceType] = Utilities.getMachineLearningServicesDisplayNameSingular(kind);
        resourceTypes(mapping);

        const tabs: Tab[] = [];

        const tabContext = {
            ...this._wizardContext,
            lifetimeManager: tabsLifetimeManager,
        };

        tabs.push(
            new BasicsTab(tabContext)
        );
        if (kind === WorkspaceMappedKind.Hub) {
            tabs.push(new StorageTab(tabContext))
        }
        // Project inherits Networking, Encryption properties from selected hub
        if (kind !== WorkspaceMappedKind.Project){
            tabs.push(
                new NetworkingInboundAccessTab(tabContext)
            );
            tabs.push(
                new NetworkingOutboundAccessTab(tabContext)
            );
            tabs.push(
                new EncryptionTab(tabContext)
            );
        }
        tabs.push(
            new IdentityTab(tabContext)
        )
        tabs.push(
            LoadableTab.createTagsTab({
                context: tabContext,
                tagResourceTypes: resourceTypes,
                disableTags: tags.disableTagsGrid,
                tagsByResource: tags.tagsByResource
            })
        );

        tabs.push(LoadableTab.createSummaryTab<typeof SummaryTab, WizardContext>("../Tabs/SummaryTab", require, tabContext));

        this._tabs(tabs);
    }

    private _newTabsLifetimeManager(): LifetimeManager {
        if (this._tabsLifetimeManager) {
            this._tabsLifetimeManager.dispose();
        }

        this._tabsLifetimeManager = this._wizardContext.container.createChildLifetime();

        return this._tabsLifetimeManager;
    }
}
