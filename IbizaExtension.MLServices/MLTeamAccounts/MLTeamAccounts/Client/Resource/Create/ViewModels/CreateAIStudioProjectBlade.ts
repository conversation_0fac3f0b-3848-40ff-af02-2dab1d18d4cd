import * as TemplateBlade from "Fx/Composition/TemplateBlade";
import { Experimentation } from "Fx/Experimentation";
import { PostProvisioningContent, PostProvisioningOptions } from "Fx/ResourceManagement/Deployments";

import { DataContext } from "../../ResourceArea";

import ClientResources = require("Resx/ClientResources");
import { WorkspaceMappedKind } from "../../../Shared/Enums";
import { CreateResourceBase } from "./CreateResourceBase";

@TemplateBlade.Decorator({
    htmlTemplate: `./CreateTemplate.html`,
    forExport: true,
    styleSheets: ["./NoPdlCreateStyles.css", "../../../Shared/Styles/CustomStyles.css"]
})
@TemplateBlade.DoesProvisioning.Decorator({ requiresMarketplaceId: false })
@TemplateBlade.InjectableModel.Decorator(DataContext)
export class CreateAIStudioProjectBlade extends CreateResourceBase {
    public title = ClientResources.createBladeProjectTitle;
    public subtitle = ClientResources.createBladeProjectSubtitle;

    constructor(experimentation: Experimentation) {
        super({ experimentation });
    }

    public async onInitialize(): Promise<void> {
        await super.onInitialize({ defaultKind: WorkspaceMappedKind.Project, deploymentSourceName: "CreateAIStudioProjectBlade" });
    }
};
