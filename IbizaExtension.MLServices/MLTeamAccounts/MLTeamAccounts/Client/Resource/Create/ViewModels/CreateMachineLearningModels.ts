import * as ResourceGroupDropDown from 'Fx/Controls/ResourceGroupDropDown';
import * as TagsByResource from 'Fx/Controls/TagsByResource';
import { DataContext } from 'Resource/ResourceArea';
import { CreateContext, Tag } from '../Models/CreateModels';
import { SKUTypes, WorkspaceMappedKind } from '../../../Shared/Enums';
import { IAppInsightsTemplateResource, ICognitiveServiceAccountTemplateResource, IContainerRegistryTemplateResource, IHubTemplateResource, IKeyvaultTemplateResource, IStorageAccountTemplateResource } from '../Create.Types';
import { ArmId } from "Fx/ResourceManagement";

import { NetworkingDataModel } from '../../Common/NetworkingDataModel';

export interface WizardContext extends CreateContext<DataModel> {
    dataContext: DataContext;
    /**
     * Tabs and their components should not attempt to observe changes to workspace kind.
     * Instead, tabs are disposed and re-created if workspace kind changes.
     */
    workspaceKind: WorkspaceMappedKind;
    resourceProviders: KnockoutObservableArray<string>;
    disableCreateButton: KnockoutObservable<boolean>;
    /**
     * Changing the workspace kind will dispose and re-create the tab controls
     * in the wizard blade.
     */
    changeWorkspaceKind(this: void, kind: WorkspaceMappedKind): void;
}

export interface CreateUiDefOptions { }

export type ManagedIdentityType = "systemAssigned" | "userAssigned";
export type StorageAccountAccessType = "accessKey" | "identity";
export const defaultStorageAccessType: StorageAccountAccessType = "accessKey";
export type ManagedKeyVaultType = "managedKeyVault" | "byoKeyVault";

export enum ManagedKeys {
    microsoft = "microsoft",
    customer = "customer"
}

export class DataModel {
    public basics: BasicsDataModel;
    public networking: NetworkingDataModel;
    public tags: TagsDataModel;
    public advanced = new AdvancedDataModel();

    /**
     * Use the options data model to control
     * enablement of conditional features in UI,
     * like whether or not HNS (aka AdlsGen2) is supported for
     * storage account creation for the currently selected subscription.
     */
    public options = new OptionsDataModel();

    public constructor(params: {
        azureDatabricks?: {
            id: string;
            location: string;
        },
        clientTag?: string,
        hubResourceId?: string,
    }) {
        const { azureDatabricks, clientTag, hubResourceId } = params;
        this.basics = new BasicsDataModel(azureDatabricks, hubResourceId);
        this.networking = new NetworkingDataModel();
        this.tags = new TagsDataModel(clientTag);
    }
}

export class OptionsDataModel {
    public readonly isADSLGen2FlightEnabledForLocation = ko.observable(false);
    public readonly isHnsSelectionFlightEnabled = ko.observable(false);
    public readonly isHnsCreateOptionFlightEnabled = ko.observable(false);
    public readonly isAppInsightsOptionalEnabled = ko.observable(false);
    public readonly isCMKServiceSideEncryptionEnabled = ko.observable(0);
    /**
     * Hns selection is supported if either isHnsSelectionFlightEnabled or isADSLGen2FlightEnabledForLocation is true.
     *
     * This is a calculated option, it is read only (do not attempt to update directly).
    */
    public readonly isHnsSelectionSupported: KnockoutComputed<boolean>;
    /**
     * Hns create option is supported if either isHnsCreateOptionFlightEnabled or isADSLGen2FlightEnabledForLocation is true.
     *
     * This is a calculated option, it is read only (do not attempt to update directly).
    */
    public readonly isHnsCreateOptionSupported: KnockoutComputed<boolean>;

    public readonly isDelegateSubnetEnabled = ko.observable(false);

    constructor() {
        this.isHnsSelectionSupported = ko.pureComputed(() => this.isHnsSelectionFlightEnabled() || this.isADSLGen2FlightEnabledForLocation());
        this.isHnsCreateOptionSupported = ko.pureComputed(() => this.isHnsCreateOptionFlightEnabled() || this.isADSLGen2FlightEnabledForLocation());
    }
}

export class BasicsDataModel {
    // Basics
    public subscription = ko.observable<MsPortalFx.Azure.Subscription>();
    public resourceGroup = ko.observable<ResourceGroupDropDown.Value>();
    public location = ko.observable<MsPortalFx.Azure.Location>();

    // ML specific
    public workspaceName = ko.observable<string>();
    public friendlyName = ko.observable<string>();
    public description = ko.observable<string>();
    public storageAccount = ko.observable<IStorageAccountTemplateResource>();
    public storageAccountName = ko.observable<string>();
    public keyVault = ko.observable<IKeyvaultTemplateResource>();
    public keyVaultName = ko.observable<string>();
    public appInsights = ko.observable<IAppInsightsTemplateResource>();
    public appInsightsName = ko.observable<string>();
    public appInsightsLogWorkspaceName = ko.observable<string>();
    public containerRegistry = ko.observable<IContainerRegistryTemplateResource>();
    public containerRegistryName = ko.observable<string>();
    public hubConfigDefaultResourceGroup = ko.observable<ResourceGroupDropDown.Value>();
    public hubConfigDefaultResourceGroupSameAs = ko.observable<boolean>(false);
    public aiServices = ko.observable<ICognitiveServiceAccountTemplateResource>();
    public aiServicesName = ko.observable<string>();
    public sku = ko.observable<string>(SKUTypes.Basic);
    public hub = ko.observable<IHubTemplateResource>();

    // Databricks id
    public adbWorkspaceId = ko.observable<string>();
    public adbWorkspaceLocation = ko.observable<string>();

    public constructor(azureDatabricks: {
        id: string;
        location: string;
    } | undefined,
    hubResourceId: string) {
        this.adbWorkspaceId(azureDatabricks?.id);
        this.adbWorkspaceLocation(azureDatabricks?.location);
        if (hubResourceId) {
            this.hub({isNew: false, id: hubResourceId, name: ArmId.parse(hubResourceId).resourceName});
        }
    }
}

export class AdvancedDataModel {
    public highBusinessImpactChecked = ko.observable<boolean>(false);
    public managedKeys = ko.observable<ManagedKeys>(ManagedKeys.microsoft);
    public serviceSideEncryption = ko.observable<boolean>(false);
    public keyVaultArmId = ko.observable<string>();
    public keyUri = ko.observable<string>();
    public identityType = ko.observable<ManagedIdentityType>("systemAssigned");
    public userAssignedIdentityResourceGroup = ko.observable<string>();
    public userAssignedIdentityName = ko.observable<string>();
    public systemDatastoresAuthMode = ko.observable<StorageAccountAccessType>(defaultStorageAccessType);
    public disableSharedKeyAccess = ko.observable<boolean>(false);
    public managedKeyVaultType = ko.observable<ManagedKeyVaultType>("byoKeyVault");
}

export class TagsDataModel {
    public tags = ko.observableArray<Tag>();
    public tagsByResource = ko.observableArray<TagsByResource.TaggedResource>();
    public resourceTypes = ko.observable<StringMap<string>>();
    public disableTagsGrid = ko.observable<boolean>(false);

    constructor(public clientTag?: string) {
    }
}
