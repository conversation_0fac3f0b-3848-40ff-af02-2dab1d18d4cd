﻿<div class='msportalfx-docking'>
    <div class='msportalfx-docking-body msportalfx-padding'>
        <div class='msportalfx-form' data-bind='pcControl: createForm.tabControl'></div>
    </div>
    <div class='msportalfx-docking-footer msportalfx-padding'>
        <div class='msportalfx-layoutChildren-horizontal-float'>
            <div class='msportalfx-layoutChildren-child' data-bind='pcControl: createForm.createButton' style='margin: 10px 100px 0px 0px'></div>
            <div class='msportalfx-layoutChildren-child' data-bind='pcControl: createForm.prevButton' style='margin: 10px 10px 0px 0px'></div>
            <div class='msportalfx-layoutChildren-child' data-bind='pcControl: createForm.nextButton' style='margin: 10px 20px 0px 0px'></div>
            <a class='msportalfx-layoutChildren-child' data-bind='fxclick: createForm.openAutomationBlade, text: createForm.automationLink, visible: createForm.showAutomationLink' style='margin-top: 10px'></a>
        </div>
    </div>
</div>