import * as TemplateBlade from "Fx/Composition/TemplateBlade";
import { Experimentation } from "Fx/Experimentation";
import * as Provisioning from "Fx/ResourceManagement/Provisioning";
import Constants = require("../../../Shared/Constants");
import { WorkspaceMappedKind } from "../../../Shared/Enums";
import { asMappedKind } from "../../../Shared/ViewAgnostic/Utilities";
import * as CreateFormControl from "../Controls/CreateFormControl";
import { DataContext } from "../../ResourceArea";
import { DoesProvisioning as ModelDoesProvisioning } from "../Models/CreateModels";
import * as TemplateGenerator from "../TemplateGenerator";
import { DataModel, WizardContext } from "./CreateMachineLearningModels";
import { CreateResourceWizard } from "./CreateResourceWizard";

export abstract class CreateResourceBase {
    protected _createResourceWizard: CreateResourceWizard;
    protected _workspaceKind: KnockoutObservableBase<WorkspaceMappedKind>;
    protected _experimentation: Experimentation;
    protected _wizardContext: WizardContext;
    protected _postDeploymentCallback?: (wizardContext: WizardContext) => Promise<void>;

    public context: TemplateBlade.Context<CreateMachineLearningServicesBlade.BladeParameters, DataContext> &
        TemplateBlade.DoesProvisioning.Context &
        ModelDoesProvisioning;

    public createForm: CreateFormControl.Contract;

    public constructor(args: { experimentation: Experimentation }) {
        const { experimentation } = args;
        this._experimentation = experimentation;
    }

    public async onInitialize(init: { defaultKind: WorkspaceMappedKind; deploymentSourceName: string }): Promise<void> {
        const { defaultKind, deploymentSourceName } = init;
        const { container, form, parameters, provisioning, model } = this.context;
        const { adbWorkspace, client, kind, hubResourceId } = parameters;

        this._workspaceKind = ko.observable(asMappedKind(kind, defaultKind));
        this._wizardContext = {
            container,
            lifetimeManager: container,
            form,
            workspaceKind: this._workspaceKind(),
            changeWorkspaceKind: (kind: WorkspaceMappedKind) => this._workspaceKind(kind),
            dataModel: new DataModel({
                azureDatabricks: adbWorkspace,
                clientTag: client,
                hubResourceId
            }),
            dataContext: model,
            provisioning,
            disableCreateButton: ko.observable(false),
            deploymentName: Constants.machineLearningServicesResourcesProvider,
            providerAndResourceName: Constants.machineLearningServicesResourcesProvider,
            originalTelemetryId: null,
            resourceProviders: ko.observableArray([Constants.machineLearningStorageResourceProvider])
        };

        this._loadAssignments(); // don't wait on this

        this._createResourceWizard = new CreateResourceWizard({
            wizardContext: this._wizardContext,
            deploymentSourceName,
            workspaceKind: this._workspaceKind,
        });

        this.createForm = CreateFormControl.create({
            context: this._wizardContext,
            deploymentSourceName: deploymentSourceName,
            initializeTabs: () => {
                return this._createResourceWizard._tabs;
            },
            getTemplateGenerator: () => {
                return Q({
                    getTemplateDeploymentOptions: () => {
                        return Q(TemplateGenerator.getTemplateDeploymentOptions(this._wizardContext));
                    },
                    getTelemetryLoggingOptions: (deploymentOptions: Provisioning.DeployTemplateOptions) => {
                        return TemplateGenerator.getTelemetryLoggingOptions(this._wizardContext, deploymentOptions);
                    }
                });
            },
            postDeploymentCallback: () => {
                this._postDeploymentCallback?.(this._wizardContext);
            }
        });

        this._createResourceWizard.initializeTabs();
    }

    private async _loadAssignments(): Promise<void> {
        const flightConfig = MsPortalFx.getEnvironmentValue("flights");
        this._wizardContext.dataModel.options.isAppInsightsOptionalEnabled(
            flightConfig?.optionalAppInsights === "true" || flightConfig?.optionalAppInsights === true
        );
        await this._experimentation.extensionAssignments.whenAvailable();
        this._wizardContext.dataModel.options.isHnsCreateOptionFlightEnabled(
            !!this._experimentation.extensionAssignments.getBooleanValue("azureportal_hnsoption", "AzurePortal")
        );
        this._wizardContext.dataModel.options.isHnsSelectionFlightEnabled(
            !!this._experimentation.extensionAssignments.getBooleanValue("azureportal_hnsselect", "AzurePortal")
        );
        this._wizardContext.dataModel.options.isCMKServiceSideEncryptionEnabled(
            this._experimentation.extensionAssignments.getNumberValue("azureportal_cmkservicesideencryption", "AzurePortal") || 0
        );
        const optionalAppInsightsAssignment = this._experimentation.extensionAssignments.getBooleanValue("optionalAppInsights", "AzurePortal");
        if (typeof optionalAppInsightsAssignment === "boolean") {
            this._wizardContext.dataModel.options.isAppInsightsOptionalEnabled(optionalAppInsightsAssignment);
        }
        this._wizardContext.dataModel.options.isDelegateSubnetEnabled(
            !!this._experimentation.extensionAssignments.getBooleanValue("azureportal_delegatesubnet", "AzurePortal")
        );
    }
}
