import * as BladeBase from 'Fx/Composition/BladeBase';
import { Container } from 'Fx/Composition/TemplateBlade';
import { HtmlContent } from 'Fx/Controls/Pill';
import * as ResourceGroupDropDown from 'Fx/Controls/ResourceGroupDropDown';
import * as TagsByResource from 'Fx/Controls/TagsByResource';
import { LifetimeManager } from 'Fx/Lifetime';

/**
 * This interface is provided for convenience. In most cases, your create's context should implement it,
 * but it is optional. If you are using a subset of the provided create functions, you may not need to use this.
 */
export interface CreateContext<TDataModel extends CreateDataModel> extends CommonContext {
    /**
     * The container, assign a reference from the decorator's context.
     */
    readonly container: Container;
    /**
     * Lifetime manager to correctly dispose of knockout subscriptions.
     * Typically this will be the same as the template blade container, but it may also be a child lifetime
     * to allow managing the lifetime of controls separately from the wizard lifetime (e.g. adding/removing tabs).
     */
    readonly lifetimeManager: LifetimeManager;
    /**
     * The data model, must implement CreateDataModel.
     */
    readonly dataModel: TDataModel;
    /**
     * The deployment name that should be used as part of the deployment template and to log in telemetry.
     * Unless you have a special case, you probably want to generate this by using
     * '.../NoPdlUi/Utilities/NoPdlUiHelpers.getDefaultDeploymentName(provisioning)'.
     */
    readonly deploymentName: string;
    /**
     * The provisioner, assign a reference from the decorator's context.
     */
    readonly provisioning: BladeBase.DoesProvisioning.Provisioning;
    /**
     * An API a TemplateBlade can use to interact with the Form Fields on the Blade.
     */
    readonly form: BladeBase.FormManagement;
    /**
     * The provider and name of the resource being created. This helps differentiate telemetry between multiple
     * creates that share the same gallery package, or a create the spawns a child expereience (perhaps ARM --> Classic).
     */
    readonly providerAndResourceName: string;
    /**
     * Optional. By default, the create button is appropriately enabled and disabled when the user validates for
     * deployment, or is currently deploying. If you want to control the state of the button in some custom cases,
     * you can specify this observable and toggle it as you wish, it will not interfere with existing logic.
     */
    readonly disableCreateButton?: KnockoutObservableBase<boolean>;
    /**
     * Optional. The observable is set to true when the deployment has been successfully submitted.
     */
    readonly deploymentSubmitted?: KnockoutObservableBase<boolean>;
    /**
     * Optional. If your create experience spawns a child experience, you should pass the telemetry ID from the provisioning
     * object to the child blade and set the "originalTelemetryId" with it. This will allow the parent to be correlated with
     * the child in telemetry.
     */
    readonly originalTelemetryId?: string;
}

/**
 * Several create functions expect a datamodel that has these properties at a minimum.
 */
export interface Basics {
    subscription: KnockoutObservableBase<MsPortalFx.Azure.Subscription>;
    resourceGroup: KnockoutObservableBase<ResourceGroupDropDown.Value>;
    location: KnockoutObservableBase<MsPortalFx.Azure.Location>;
}

/**
 * The expected format of Tag.
 */
export interface Tag {
    resourceType: string;
    value: string;
    key: string;
}

/**
 * The CreateTagsTab expects a datamodel that has these properties.
 */
export interface Tags {
    tags: KnockoutObservableArray<Tag>;
    tagsByResource: KnockoutObservableArray<TagsByResource.TaggedResource>
}

/**
 * Create data models should implement this interface.
 */
export interface CreateDataModel {
    basics: Basics;
    tags: Tags;
}


/**
 * Used to share common properties across create functions. The union of all *required* properties expected
 * within the contexts required by the CreateFormControl, CreateBasics, CreateTags, CreateSummary,
 * and create controls, maps to this interface. If your context implements this interface it will be
 * compatible with all .../NoPdlUi/Create*.ts functions.
 */
export interface CommonContext {
    container?: Container;
    lifetimeManager?: LifetimeManager;
    dataModel?: {
        basics?: {
            subscription?: KnockoutObservableBase<MsPortalFx.Azure.Subscription>;
            resourceGroup?: KnockoutObservableBase<ResourceGroupDropDown.Value>;
            location?: KnockoutObservableBase<MsPortalFx.Azure.Location>;
        },
        tags?: {
            tags: KnockoutObservableArray<Tag>;
            tagsByResource: KnockoutObservableArray<TagsByResource.TaggedResource>
        }
    };
    deploymentName?: string;
    disableCreateButton?: KnockoutObservableBase<boolean>;
    deploymentSubmitted?: KnockoutObservableBase<boolean>;
    provisioning?: BladeBase.DoesProvisioning.Provisioning;
    validateForm?: () => Q.Promise<boolean>;
    providerAndResourceName?: string;
    originalTelemetryId?: string;
}

/**
 * This interface can be used to eliminate intellisense errors on the provisioning property provided by
 * PortalFx's context. Optional.
 */
export interface DoesProvisioning {
    provisioning: BladeBase.DoesProvisioning.Provisioning;
}

/**
 * Used to standardize options for controls.
 */
export interface CanOverrideLabel {
    overrideLabel?: string | KnockoutObservableBase<string>;
}

/**
 * Used to standardize options for controls.
 */
export interface CommonControl extends CanOverrideLabel {
    overrideInfoBalloonContent?: string | KnockoutObservableBase<string> | HtmlContent;
    customValidations?: Array<MsPortalFx.ViewModels.CustomValidation | MsPortalFx.ViewModels.RequiredValidation>;
}