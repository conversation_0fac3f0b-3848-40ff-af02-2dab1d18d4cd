/**
 * Copyright (c) Microsoft Corporation.  All rights reserved.
 */

import * as Section from "Fx/Controls/Section";

/**
 * All create tabs must implement this interface at a minimum.
 */
export interface Tab {
    id: string;
    name: string;
    section: Section.Contract;
    triggerValidation?: () => Promise<boolean>;
    onLoad?: () => void | Promise<void>;
    /**
     * Portal bug #4355345: Framework does not show red circle next to tab if validation failed on the optional fields.
     * Adding this property as a workaround. If this is set, the tab invalid message will show tab name.
     */
    showNameIfInvalid?: boolean;
}