/**
 * Copyright (c) Microsoft Corporation.  All rights reserved.
 */

import * as FxComposition from "Fx/Composition";
import { HtmlContent } from "Fx/Controls/ControlsBase";
import * as InfoBox from "Fx/Controls/InfoBox";
import * as PricingControl from "Fx/Controls/PricingControl";
import * as CheckBoxButtons from "Fx/Controls/CheckBox";
import * as Section from "Fx/Controls/Section";

import * as Format from "../../../Shared/Controls/Format";
import { DocLinkIds, getDocumentUrl } from "../../../Shared/DocLinks";
import { Tab } from "../Models/ControlModels";
import { getKeyNameFromUrl, getKeyVersionFromUrl, getVaultNameFromId } from "../../../Shared/ViewAgnostic/Utilities";
import * as NoPdlUiConstants from "../Utilities/NoPdlUiConstants";
import * as NoPdlUiHelpers from "../Utilities/NoPdlUiHelpers";
import { ManagedKeys, WizardContext } from "../ViewModels/CreateMachineLearningModels";
import Resources = require("Resx/ClientResources");
import { WorkspaceMappedKind } from "Shared/Enums";

export class EncryptionTab implements Tab {
    // Sections
    private _encryptionTypeSection: Section.Contract;

    // Controls
    private _encryptionTypeCheckboxButtons: CheckBoxButtons.Contract;
    private _serviceSideEncryption: CheckBoxButtons.Contract;
    private _encryptionCmkSelectedWarning: InfoBox.Contract;
    private _isLoading = ko.observable<boolean>(true);
    private _keyVaultControl: PricingControl.Contract<KeyVaultPickerV2.KeyPickerV2Outputs>;
    private _keyVaults = ko.observableArray<any>([]);
    private _context: WizardContext;
    public id: string = NoPdlUiConstants.EncryptionTab.tabId;
    public name: string = Resources.EncryptionTab.title;
    public section: Section.Contract;

    constructor(context: WizardContext) {
        this._context = context;
        const { lifetimeManager } = context;
        const children: any[] = [];

        this._createDataEncryptionControls();

        const headerText = Format.create(lifetimeManager, {
            format: Resources.AdvancedTab.Section.DataEncryption.header,
            children: [
                {
                    htmlTemplate: `<a href="${getDocumentUrl(DocLinkIds.CMK)}">${Resources.AdvancedTab.Section.DataEncryption.linkText}</a>`
                }
            ]
        })
        this._encryptionTypeSection = Section.create(lifetimeManager, {
            children: [
                NoPdlUiHelpers.createSectionHeader(Resources.AdvancedTab.Section.DataEncryption.title, true, true),
                NoPdlUiHelpers.createInfoSectionHeader(headerText),
                this._encryptionTypeCheckboxButtons,
                this._serviceSideEncryption,
                this._keyVaultControl,
                this._encryptionCmkSelectedWarning
            ],
            visible: ko.observable(true),
            cssClass: "ext-nopdlcreate-section"
        });

        children.push(this._encryptionTypeSection);

        this.section = Section.create(lifetimeManager, {
            name: Resources.EncryptionTab.title,
            children,
            cssClass: "ext-focus-container"
        });

        this._initializeHandlers();
    }

    public onLoad(): void {
        this._loadKeyVaults();
        this._isLoading(false);
        this._encryptionTypeCheckboxButtons.triggerValidation();
    }

    public triggerValidation(): Q.Promise<boolean> {
        const { lifetimeManager } = this._context;
        return Q.all([
            NoPdlUiHelpers.validateSection(lifetimeManager, this._encryptionTypeSection)
        ]).then(
            results => results.every(r => r),
            () => false
        );
    }

    private _initializeHandlers(): void {
        const { lifetimeManager, dataModel } = this._context;
        const { basics, advanced } = dataModel;

        basics.subscription.subscribe(lifetimeManager, (subscription) => {
            this._resetKeyVaultData();
        });

        basics.location.subscribe(lifetimeManager, () => {
            this._resetKeyVaultData();
        });
    }

    private _loadKeyVaults(): void {
        const { dataModel } = this._context;
        const subscriptionId = dataModel.basics.subscription()?.subscriptionId ?? "";
        this._context.dataContext.keyVaultData.getVaults(subscriptionId).then((response) => {
            this._keyVaults(response);
        });
    }

    private _createDataEncryptionControls(): void {
        const { lifetimeManager, dataModel, workspaceKind } = this._context;
        const { advanced, options } = dataModel;

        const encTypeCheckboxValue = ko.computed(lifetimeManager, [advanced.managedKeys], {
            read: () => {
                return advanced.managedKeys() === ManagedKeys.customer;
            },
            write: (newVal) => {
                advanced.managedKeys(newVal ? ManagedKeys.customer : ManagedKeys.microsoft);
            }
        });

        this._encryptionTypeCheckboxButtons = CheckBoxButtons.create(lifetimeManager, {
            label: Resources.AdvancedTab.Section.DataEncryption.Type.label,
            value: encTypeCheckboxValue
        });

        const serviceSideEncryptionFormattedLabel = Format.create(lifetimeManager, {
            format: Resources.AdvancedTab.Section.DataEncryption.ServiceSide.label,
            children: []
        });

        this._serviceSideEncryption = CheckBoxButtons.create(lifetimeManager, {
            label: {
                htmlTemplate: `<div data-bind="pcControl: serviceSideEncryptionFormattedLabel"></div>`,
                viewModel: { serviceSideEncryptionFormattedLabel }
            },
            visible: ko.pureComputed(() => advanced.managedKeys() === ManagedKeys.customer && (options.isCMKServiceSideEncryptionEnabled() === 2 || (options.isCMKServiceSideEncryptionEnabled() === 1 && workspaceKind === WorkspaceMappedKind.Default))),
            value: advanced.serviceSideEncryption
        });

        const formattedMessage = Format.create(lifetimeManager, {
            format: Resources.AdvancedTab.Section.DataEncryption.warningMessage,
            children: []
        });

        this._encryptionCmkSelectedWarning = InfoBox.create(lifetimeManager, {
            style: InfoBox.Style.Warning,
            text: {
                htmlTemplate: `<div data-bind="pcControl: formattedMessage"></div>`,
                viewModel: { formattedMessage }
            },
        });

        this._createKeyPickerControl();
    }

    private _createKeyPickerControl(): void {
        const { container, lifetimeManager, dataModel } = this._context;
        const { advanced } = dataModel;

        const options: PricingControl.Options<KeyVaultPickerV2.KeyPickerV2Outputs> = {
            label: Resources.AdvancedTab.Key.KeyVaultChangeControl.label,
            subText: "",
            linkText: Resources.AdvancedTab.Key.KeyVaultChangeControl.action,
            barColor: NoPdlUiConstants.ChangeControlBarColor,
            visible: ko.pureComputed(() => advanced.managedKeys() === ManagedKeys.customer),
            validations: [
                new MsPortalFx.ViewModels.CustomValidation(
                    Resources.validationWorkspaceNameAlreadyInUse,
                    (keyVaultPicker: any): Q.Promise<MsPortalFx.ViewModels.ValidationResult> => {
                        return Q(this._validateKeyVaultSelection());
                    }
                )
            ],
            onLinkClick: (): void => {
                const parameters: KeyVaultPickerV2.KeyPickerV2Parameters = {
                    subscriptionId: dataModel.basics.subscription().subscriptionId || "",
                    location: dataModel.basics.location().name || "",
                    showSubscriptionDropdown: false,
                    showCreateNew: false,
                    showVersionPicker: true,
                    keyAndVersionDropdownOptional: false,
                    requiredKeyTypes: [
                        KeyVaultPickerV2.KeyType.RSA,
                        KeyVaultPickerV2.KeyType.RSAHSM,
                        KeyVaultPickerV2.KeyType.EC,
                        KeyVaultPickerV2.KeyType.ECHSM
                    ]
                };

                const onClosed: FxComposition.BladeClosedWithDataHandler<KeyVaultPickerV2.KeyPickerV2Outputs> = (
                    reason: FxComposition.BladeClosedReason,
                    data: KeyVaultPickerV2.KeyPickerV2Outputs
                ) => {
                    if (
                        reason === FxComposition.BladeClosedReason.ChildClosedSelf &&
                        data &&
                        data.vaultResourceId &&
                        data.keyId &&
                        data.keyVersionId
                    ) {
                        dataModel.advanced.keyVaultArmId(data.vaultResourceId);
                        dataModel.advanced.keyUri(data.keyVersionId);

                        this._keyVaultControl.value(data);
                        this._keyVaultControl.subText(this._getPricingControlSubText());
                    }
                };

                const kvBladeReference = FxComposition.BladeReferences.forExtension("Microsoft_Azure_KeyVault")
                    .forBlade("KeyPickerV2ViewModel")
                    .createReference({ parameters, onClosed });
                container.openContextPane(kvBladeReference);
            },
            disabled: ko.pureComputed(() => !dataModel.basics.location())
        } as PricingControl.Options<KeyVaultPickerV2.KeyPickerV2Outputs>;

        this._keyVaultControl = PricingControl.create(lifetimeManager, options);
    }

    private _getPricingControlSubText(): HtmlContent {
        const { dataModel } = this._context;
        const { advanced } = dataModel;
        const version = getKeyVersionFromUrl(advanced.keyUri()) || Resources.noContent;
        const keyValutName = getVaultNameFromId(advanced.keyVaultArmId()) || Resources.noContent;
        const keyName = getKeyNameFromUrl(advanced.keyUri()) || Resources.noContent;

        const htmlContent: HtmlContent = {
            htmlTemplate: `<div data-bind="text: keyVault"></div>
            <div data-bind="text: key"></div>
            <div data-bind="text: version"></div>`,
            viewModel: {
                keyVault: Resources.AdvancedTab.Key.KeyVaultChangeControl.keyVault.format({ keyvault: keyValutName }),
                key: Resources.AdvancedTab.Key.KeyVaultChangeControl.key.format({ key: keyName }),
                version: Resources.AdvancedTab.Key.KeyVaultChangeControl.version.format({ version: version })
            }
        };
        return htmlContent;
    }

    private _resetKeyVaultData(): void {
        const { dataModel } = this._context;
        const { advanced } = dataModel;

        advanced.keyVaultArmId("");
        advanced.keyUri("");

        this._keyVaultControl.value(undefined);
        this._keyVaultControl.subText("");
    }

    private _validateKeyVaultSelection(): MsPortalFx.ViewModels.ValidationResult {
        const { dataModel } = this._context;
        const { advanced } = dataModel;

        if (!(advanced.keyUri() && advanced.keyVaultArmId())) {
            return {
                valid: false,
                message: Resources.AdvancedTab.Key.KeyVaultChangeControl.required
            };
        }

        const keyVaults = this._keyVaults();
        if (!keyVaults) {
            return { valid: true, message: "" };
        }

        const selectedVault = keyVaults.find((vault) => vault.id === advanced.keyVaultArmId());
        if (!selectedVault) {
            return {
                valid: false,
                message: Resources.AdvancedTab.Key.KeyVaultChangeControl.required
            };
        }

        if (!selectedVault.properties?.enablePurgeProtection) {
            return {
                valid: false,
                message: Resources.AdvancedTab.Key.KeyVaultChangeControl.purgeProtectionRequired
            };
        }

        return { valid: true, message: "" };
    }
}
