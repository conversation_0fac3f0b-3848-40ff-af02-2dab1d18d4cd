import * as ResourceGroupDropDown from "Fx/Controls/ResourceGroupDropDown";
import * as Section from "Fx/Controls/Section";
import Resources = require("Resx/ClientResources");

import { Tab } from "../Models/ControlModels";
import { WizardContext } from "../ViewModels/CreateMachineLearningModels";
import { machineLearningServicesResourcesProvider, machineLearningWorkspaces } from "../../../Shared/Constants";
import { NetworkingSection } from "../../Common/NetworkingSection";

const NetworkingResources = Resources.CreateBlade.Network;


export class NetworkingTab implements Tab {
    public id: string = "networkingTab";
    public name: string = NetworkingResources.title;
    public section: Section.Contract;
    public showNameIfInvalid = true;
    private _isLoading = ko.observable<boolean>(true);

    private _networkingSection: NetworkingSection;

    constructor(context: WizardContext) {
        const { form, dataModel, dataContext, workspaceKind } = context;
        this._networkingSection = new NetworkingSection(context, {
            form,
            networkingDataModel: dataModel.networking,
            dataContext: dataContext,
            workspaceKind,
            resourceDetails: {
                subscription: dataModel.basics.subscription,
                resourceGroup: dataModel.basics.resourceGroup,
                location: dataModel.basics.location,
                workspaceName: dataModel.basics.workspaceName,
                storageAccount: dataModel.basics.storageAccount,
                keyVault: dataModel.basics.keyVault,
                appInsights: dataModel.basics.appInsights,
                containerRegistry: dataModel.basics.containerRegistry
            },
            isCreationFlow: true,
            isLoading: this._isLoading,
            isDelegateSubnetEnabled: context.dataModel.options.isDelegateSubnetEnabled,
            advancedDataModel: dataModel.advanced,
        });

        this.section = this._networkingSection.section;
    }

    public onLoad(): void {
        this._isLoading(false);
        this._networkingSection.onLoad();
    }

    public triggerValidation(): Q.Promise<boolean> {
        return this._networkingSection.triggerValidation();
    }
}

export function calculateInUseVirtualNetworkIds(
    group: CreatePrivateEndpointContextBlade.SubResourceDetails,
    existingConfigurations: CreatePrivateEndpointContextBlade.Output[]
): string[] {
    let result: string[] = [];

    if (existingConfigurations) {
        existingConfigurations.forEach(configuration => {
            if (
                MsPortalFx.localeCompareIgnoreCase(
                    configuration.privateEndpointConfiguration.subResource.expectedPrivateDnsZoneName,
                    group.expectedPrivateDnsZoneName
                ) === 0
            ) {
                result.push(configuration.networkingConfiguration.virtualNetworkValue.id);
            }
        });

        if (result.length) {
            result = MsPortalFx.unique(result, (a, b) => {
                return MsPortalFx.localeCompareIgnoreCase(a, b) === 0;
            });
        }
    }

    return result;
}

export function createResourceId(subscription: MsPortalFx.Azure.Subscription, resourceGroup: ResourceGroupDropDown.Value, name: string) {
    return `/subscriptions/${subscription && subscription.subscriptionId}/resourceGroups/${resourceGroup && resourceGroup.value && resourceGroup.value.name
        }/providers/${machineLearningServicesResourcesProvider}/${machineLearningWorkspaces}/${name}`;
}
