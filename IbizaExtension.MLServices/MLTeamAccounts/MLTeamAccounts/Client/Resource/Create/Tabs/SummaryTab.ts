/**
 * Copyright (c) Microsoft Corporation.  All rights reserved.
 */

import * as Summary from "Fx/Controls/Summary";
import * as ResourceGroupDropDown from 'Fx/Controls/ResourceGroupDropDown';
import { ArmId } from "Fx/ResourceManagement";
import { RuleCategory } from "../../../Resource/Data/Data.Types";
import { WorkspaceMappedKind } from "../../../Shared/Enums";
import { getPrivateDnsZoneName, getPrivateDnsZones } from "../../../Shared/NetworkUtilities";
import { IGenericTemplateResource } from "../Create.Types";
import { getTemplateDeploymentOptions } from "../TemplateGenerator";
import { ManagedKeys, WizardContext } from "../ViewModels/CreateMachineLearningModels";
import * as SummaryTab from "./Create/CreateSummaryTab";
import Resources = require("Resx/ClientResources");
import { getConnectivityMethodDisplayName, getNetworkIsolationDisplayName, ConnectivityMethod } from "../../Common/NetworkingDataModel";


const RuleCategorySortOrder: Record<RuleCategory, number> = {
    UserDefined: 0,
    Recommended: 1,
    Required: 2,
    Dependency: 3
};

interface ILocalState {
    _location?: Summary.Item;
}

export function create(context: WizardContext) {
    const localState: ILocalState = {};
    const getCustomizeGroupsOnLoadWithState = getCustomizeGroupsOnLoad.bind(localState);
    const getCustomizeBasicsSummaryItemsOnLoadWithState = getCustomizeBasicsSummaryItemsOnLoad.bind(localState);
    return SummaryTab.create({
        context,
        generateDeployTemplateOptions: () => Q.resolve(getTemplateDeploymentOptions(context)),
        customizeGroupsOnLoad: getCustomizeGroupsOnLoadWithState(context),
        customizeBasicsSummaryItemsOnLoad: getCustomizeBasicsSummaryItemsOnLoadWithState(context),
        locationOptions: {
            overrideLabel: Resources.createLocationLabelRegion
        },
        capitalizeNew: true
    });
}

function getCustomizeGroupsOnLoad(this: ILocalState, context: WizardContext) {
    return (groups: Summary.Group[]) => {
        const { dataModel, workspaceKind } = context;
        const { basics, networking, tags, advanced } = dataModel;
        const { resourceTypes } = tags;

        // Add networking details to Review + Create blade
        const networkingItems: Summary.Item[] = [];
        const connectivityMethod = networking.connectivityMethod();
        const managedNetwork = networking.getManagedNetwork();
        const isolationMode = managedNetwork.isolationMode;

        if (workspaceKind === WorkspaceMappedKind.Hub) {
            const resourcesSection: Summary.Item[] = [];
            if (this._location) {
                resourcesSection.push(this._location);
            }
            addAssociatedResourceItem(resourcesSection, Resources.AIServices.SummaryTab.label, basics.aiServices(), true);
            addAssociatedResourceItem(resourcesSection, Resources.StorageAccount.SummaryTab.label, basics.storageAccount());
            addAssociatedResourceItem(resourcesSection, Resources.Keyvault.SummaryTab.label, basics.keyVault(), false, advanced.managedKeyVaultType() === "managedKeyVault");
            addAssociatedResourceItem(resourcesSection, Resources.AppInsights.SummaryTab.label, basics.appInsights(), true);
            addAssociatedResourceItem(resourcesSection, Resources.ContainerRegistry.SummaryTab.label, basics.containerRegistry(), true);
            groups.push(SummaryTab.createSummaryTabSection(resourcesSection, Resources.CreateBlade.Advanced.Review.Resources.title));
        }

        networkingItems.push(
            SummaryTab.createSummaryTabItem(Resources.CreateBlade.Network.connectivityMethod, getConnectivityMethodDisplayName(connectivityMethod))
        );

        const isolationModeSummaryItem = SummaryTab.createSummaryTabItem(
            Resources.CreateBlade.Network.networkIsolationTitle,
            getNetworkIsolationDisplayName(connectivityMethod, isolationMode)
        );
        networkingItems.push(isolationModeSummaryItem);

        if (isolationMode === "AllowOnlyApprovedOutbound") {
            const azureFirewallSummaryItem = SummaryTab.createSummaryTabItem(
                Resources.CreateBlade.Network.azureFirewallSku,
                managedNetwork.firewallSku
            );
            networkingItems.push(azureFirewallSummaryItem);
        }

        const provisionManagedNetwork = networking.getProvisionManagedNetwork();
        if (provisionManagedNetwork) {
            const managedNetworkSummaryItem = SummaryTab.createSummaryTabItem(
                Resources.CreateBlade.Network.WorkspaceOutboundAccess.provisionManagedVirtualNetworkSummaryTitle,
                provisionManagedNetwork ? Resources.CreateBlade.Advanced.Review.enabled : Resources.CreateBlade.Advanced.Review.disabled
            );
            networkingItems.push(managedNetworkSummaryItem);
        }

        if (connectivityMethod === ConnectivityMethod.PrivateEndpoint) {
            const configurations = networking.privateEndpointConfigurations();
            if (configurations) {
                const PrivateEndpointResources = Resources.CreateBlade.Network.PrivateEndpoints;
                configurations.forEach(configuration => {
                    const privateDnsZones = getPrivateDnsZones(configuration);
                    if (
                        configuration.privateDnsZoneConfiguration &&
                        configuration.privateDnsZoneConfiguration.integrateWithPrivateDnsZone &&
                        privateDnsZones.length > 0
                    ) {
                        networkingItems.push(
                            SummaryTab.createSummaryTabItem(
                                PrivateEndpointResources.type,
                                PrivateEndpointResources.nameSubResourceAndDns.format(
                                    configuration.privateEndpointConfiguration.privateEndpoint.name,
                                    configuration.privateEndpointConfiguration.subResource.subResourceDisplayName.toLocaleLowerCase(),
                                    privateDnsZones
                                        .map(dns =>
                                            dns.isNew
                                                ? Resources.newResourceFormatCaps.format(getPrivateDnsZoneName(dns))
                                                : getPrivateDnsZoneName(dns)
                                        )
                                        .join(", ")
                                ),
                                true,
                                true
                            )
                        );
                    } else {
                        networkingItems.push(
                            SummaryTab.createSummaryTabItem(
                                PrivateEndpointResources.type,
                                PrivateEndpointResources.nameAndSubResource.format(
                                    configuration.privateEndpointConfiguration.privateEndpoint.name,
                                    configuration.privateEndpointConfiguration.subResource.subResourceDisplayName.toLocaleLowerCase()
                                ),
                                true,
                                true
                            )
                        );
                    }
                });
            }
        }
        if(workspaceKind === WorkspaceMappedKind.Hub && dataModel.options.isDelegateSubnetEnabled()) {
            networkingItems.push(
                SummaryTab.createSummaryTabItem(
                    Resources.CreateBlade.Network.SubnetDelegate.title,
                    dataModel.networking.delegatedSubnet()?.name,
                ),
            );
        }

        const PrivateEndpointResources = Resources.CreateBlade.Network.PrivateEndpoints;
        const activeRules = networking.getActiveOutboundAccessRules() || {};
        Object.entries(activeRules)
            .sort(([_leftName, leftRule], [_rightName, rightRule]) => {
                const leftWeight = RuleCategorySortOrder[leftRule.category];
                const rightWeight = RuleCategorySortOrder[rightRule.category];
                if (leftWeight === rightWeight) {
                    return 0;
                }
                return leftWeight > rightWeight ? 1 : -1;
            })
            .forEach(([name, rule]) => {
                networkingItems.push(
                    SummaryTab.createSummaryTabItem(
                        "Outbound access rule",
                        PrivateEndpointResources.nameAndSubResource.format(`${rule.category}: ${rule.type}`, name)
                    )
                );
            });

        // Projects inherit from hubs, so hiding unnecessary sections
        if (workspaceKind !== WorkspaceMappedKind.Project) {
            groups.push(SummaryTab.createSummaryTabSection(networkingItems, Resources.CreateBlade.Network.title));

            // Add advanced details to Review + Create blade
            const encryptionSection = createEncryptionSection(context);
            groups.push(encryptionSection);
        }

        const identitySection = createIdentitySection(context);
        groups.push(identitySection);

        const currentResourceTypes = resourceTypes();
        const tagItems = tags.tags().map(item => {
            return SummaryTab.createSummaryTabItemWithResourceType(item.key, item.value, currentResourceTypes[item.resourceType]);
        });

        if (tagItems.length > 0) {
            groups.push(SummaryTab.createSummaryTabSection(tagItems, Resources.tags));
        }
    };
}

function createEncryptionSection(context: WizardContext): Summary.Group {
    const items: Summary.Item[] = [];
    const advanced = context.dataModel.advanced;
    const { keyUri, keyVaultArmId, managedKeys } = advanced;

    const isCustomerManagedKeys = managedKeys() === ManagedKeys.customer;
    items.push(
        SummaryTab.createSummaryTabItem(
            Resources.CreateBlade.Advanced.Review.encryptionType,
            isCustomerManagedKeys
                ? Resources.CreateBlade.Advanced.Review.customerManagedKeys
                : Resources.CreateBlade.Advanced.Review.microsoftManagedKeys
        )
    );

    if (isCustomerManagedKeys) {
        if (keyVaultArmId()) {
            const resourceDescriptor = ArmId.parse(keyVaultArmId());
            items.push(SummaryTab.createSummaryTabItem(Resources.CreateBlade.Advanced.Review.keyVault, resourceDescriptor.resourceName));

            items.push(SummaryTab.createSummaryTabItem(Resources.CreateBlade.Advanced.Review.keyURI, keyUri()));
        }
    }

    return SummaryTab.createSummaryTabSection(items, Resources.CreateBlade.Advanced.Review.Encryption.title);
}

function createIdentitySection(context: WizardContext): Summary.Group {
    const items: Summary.Item[] = [];
    const advanced = context.dataModel.advanced;

    const userAssigned = context.dataModel.advanced.identityType() === "userAssigned";
    items.push(
        SummaryTab.createSummaryTabItem(
            Resources.SummaryTab.Advanced.ManagedIdentity.typeText,
            userAssigned ? Resources.SummaryTab.Advanced.ManagedIdentity.userAssigned : Resources.SummaryTab.Advanced.ManagedIdentity.systemAssigned
        )
    );

    if (userAssigned) {
        items.push(
            SummaryTab.createSummaryTabItem(Resources.SummaryTab.Advanced.ManagedIdentity.userIdentityName, advanced.userAssignedIdentityName())
        );

        items.push(
            SummaryTab.createSummaryTabItem(
                Resources.SummaryTab.Advanced.ManagedIdentity.userIdentityResourceGroup,
                advanced.userAssignedIdentityResourceGroup()
            )
        );
    }

    if (context.workspaceKind !== WorkspaceMappedKind.Hub && context.workspaceKind !== WorkspaceMappedKind.Project) {
        items.push(
            SummaryTab.createSummaryTabItem(
                Resources.CreateBlade.Advanced.Review.enableHBIFlag,
                advanced.highBusinessImpactChecked() ? Resources.CreateBlade.Advanced.Review.enabled : Resources.CreateBlade.Advanced.Review.disabled
            )
        );
    }

    items.push(
        SummaryTab.createSummaryTabItem(
            Resources.CreateBlade.Advanced.Review.storageAccountAccessType,
            advanced.systemDatastoresAuthMode() === "accessKey"
                ? Resources.CreateBlade.Advanced.Review.credentialBasedStorageAccountAccess
                : Resources.CreateBlade.Advanced.Review.identityBasedStorageAccountAccess
        )
    );

    if (context.workspaceKind !== WorkspaceMappedKind.Project) {
        items.push(
            SummaryTab.createSummaryTabItem(
                Resources.CreateBlade.Advanced.Review.sharedKeyAccess,
                advanced.disableSharedKeyAccess() ? Resources.CreateBlade.Advanced.Review.disabled : Resources.CreateBlade.Advanced.Review.enabled
            )
        );
    }

    return SummaryTab.createSummaryTabSection(items, Resources.CreateBlade.Advanced.Review.Identity.title);
}

function getCustomizeBasicsSummaryItemsOnLoad(this: ILocalState, context: WizardContext) {
    return (basicsSummaryItems: Summary.Item[]) => {
        const { dataModel, workspaceKind } = context;
        const { basics } = dataModel;

        const workspaceNameItem = SummaryTab.createSummaryTabItem(Resources.createBladeWorkspaceNameLabel, basics.workspaceName());

        if (workspaceKind === WorkspaceMappedKind.Hub) {
            // Remove the "region" item, we'll be moving this to resources section for hubs
            this._location = basicsSummaryItems.splice(2, 1)[0];
            basicsSummaryItems.push(workspaceNameItem);
            const defaultProjectResourceGroup = basics.hubConfigDefaultResourceGroupSameAs() ? basics.resourceGroup() : basics.hubConfigDefaultResourceGroup();
            if (defaultProjectResourceGroup && defaultProjectResourceGroup.value?.name) {
                basicsSummaryItems.push(
                    SummaryTab.createSummaryTabItem(Resources.HubConfigDefaultResourceGroup.SummaryTab.label,
                        defaultProjectResourceGroup.value.name,
                        defaultProjectResourceGroup.mode === ResourceGroupDropDown.Mode.CreateNew,
                        true
                    )
                );
            }
            const description = basics.description();
            if (description) {
                basicsSummaryItems.push(SummaryTab.createSummaryTabItem(Resources.createBladeDescriptionLabel, description));
            }
            return basicsSummaryItems;
        }
        if (workspaceKind === WorkspaceMappedKind.Project) {
            // Remove the "region" item, as project region follows the hub region
            this._location = basicsSummaryItems.splice(2, 1)[0];
            basicsSummaryItems.push(workspaceNameItem);

            const workspaceFriendlyNameItem = SummaryTab.createSummaryTabItem(Resources.createBladeFriendlyNameLabel, basics.friendlyName());
            basicsSummaryItems.push(workspaceFriendlyNameItem);

            const hubNameItem = SummaryTab.createSummaryTabItem(Resources.createBladeHubNameLabel, basics.hub().name);
            basicsSummaryItems.push(hubNameItem);

            return basicsSummaryItems;
        }

        basicsSummaryItems.push(workspaceNameItem);

        addAssociatedResourceItem(basicsSummaryItems, Resources.StorageAccount.SummaryTab.label, basics.storageAccount());
        addAssociatedResourceItem(basicsSummaryItems, Resources.Keyvault.SummaryTab.label, basics.keyVault());
        addAssociatedResourceItem(basicsSummaryItems, Resources.AppInsights.SummaryTab.label, basics.appInsights(), true);
        addAssociatedResourceItem(basicsSummaryItems, Resources.ContainerRegistry.SummaryTab.label, basics.containerRegistry(), true);
    };
}

function addAssociatedResourceItem(
    summaryItems: Summary.Item[],
    resourceLabel: string,
    resource?: IGenericTemplateResource,
    addNoneIfNotExists: boolean = false,
    microsoftManaged: boolean = false
): void {
    if (microsoftManaged) {
        summaryItems.push(SummaryTab.createSummaryTabItem(resourceLabel, Resources.SummaryTab.Resource.microsoftManaged));
    } else if (resource) {
        summaryItems.push(
            SummaryTab.createSummaryTabItem(
                resourceLabel,
                resource.isNew
                    ? Resources.SummaryTab.Resource.newFormat.format(resource.name)
                    : Resources.SummaryTab.Resource.existingFormat.format(resource.name, resource.resourceGroupName)
            )
        );
    } else if (addNoneIfNotExists) {
        summaryItems.push(SummaryTab.createSummaryTabItem(resourceLabel, Resources.SummaryTab.Resource.none));
    }
}
