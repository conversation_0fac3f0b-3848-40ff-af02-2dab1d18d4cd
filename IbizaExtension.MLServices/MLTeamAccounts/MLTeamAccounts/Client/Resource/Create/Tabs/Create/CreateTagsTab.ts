/**
 * Copyright (c) Microsoft Corporation.  All rights reserved.
 */

import { Container } from 'Fx/Composition/TemplateBlade';
import { HtmlContent } from 'Fx/Controls/ControlsBase';
import * as TagsByResource from 'Fx/Controls/TagsByResource';
import { LifetimeManager } from 'Fx/Lifetime';

import * as LoadableTab from '../../Controls/LoadableTab';
import { Tab } from '../../Models/ControlModels';
import { CommonContext, Tag, Tags } from '../../Models/CreateModels';
import { TagsTab } from '../../Utilities/NoPdlUiConstants';
import { createLink } from '../../Utilities/NoPdlUiHelpers';

import Resources = require("Resx/ClientResources");
export interface Context extends CommonContext {
    container: Container;
    lifetimeManager: LifetimeManager;
    dataModel: {
        basics: {
            subscription: KnockoutObservableBase<MsPortalFx.Azure.Subscription>;
        };
        tags: {
            tags: KnockoutObservableArray<Tag>;
            tagsByResource: KnockoutObservableArray<TagsByResource.TaggedResource>
        }
    };
}

export interface Options {
    context: Context;
    /**
     * A mapping of resource types. If you do not specify this, the resource types column will not be shown.
     */
    tagResourceTypes: StringMap<string> | KnockoutObservableBase<StringMap<string>>;
    /**
     * In some cases, you may wish to dynamically disable the tags grid when opening the tags tab
     * One relevant case is if your experience dynamically allows classic resources.
     */
    disableTags?: KnockoutObservableBase<boolean>;
    /**
     * Specify a custom message to show in the status bar when disabling tags. The default message
     * expects that the reason is that the deployment model is set to classic.
     */
    disableTagsCustomStatusBarMessage?: string | KnockoutObservableBase<string>;
    tagsByResource?: KnockoutObservableArray<TagsByResource.TaggedResource>
}

export interface Contract extends LoadableTab.TabContent {
}

/**
 * This function will create a tags tab with common controls used in the no-pdl create pattern.
 * The resulting contract is compatible with CreateFormControl, but it may be used by itself.
 *
 * Note: A blade which calls this method must define 'ext-nopdlcreate-section' within the
 * TemplateBlade's CSS styleSheet.
 */
export function create(options: Options): Contract {
    const context = options.context;
    const { container } = context;
    const tagsControl = createControl(options);
    const controls: HtmlContent[] = [
        createLink({
            text: Resources.createTagsTabIntro,
            linkUri: TagsTab.learnMoreLink,
            linkText: Resources.createTagsTabLearnMoreText,
        }),
        {
            htmlTemplate: TagsTab.intro,
            viewModel: {
                notice: Resources.createTagsTabUpdateNotice
            }
        },
        {
            htmlTemplate: `<div data-bind="pcControl: tagsControl, visible: showTagsControl"></div>`,
            viewModel: {
                tagsControl,
                showTagsControl: true,
                disableTags: options.disableTags
            }
        }
    ];

    return {
        controls,
        onLoad: () => {
            container.setFocus(".ext-nopdlcreate-section");
        },
        triggerValidation: () => {
            if (ko.unwrap(options.disableTags)) {
                return Q(true);
            }

            return Q(tagsControl.triggerValidation());
        }
    };
}

function createControl(options: Options) {
    const context = options.context;
    const { dataModel, lifetimeManager } = context;

    const targetResources = ko.observableArray<TagsByResource.TargetItem>();
    const hideResourceColumn = ko.observable(true);
    MsPortalFx.initObservable(options.tagResourceTypes).subscribeAndRun(lifetimeManager, resourceTypeNameMap => {
        resourceTypeNameMap = resourceTypeNameMap || {};
        const resourceItems = Object.keys(resourceTypeNameMap).map(resourceType => {
            return {
                id: resourceType,
                displayName: resourceTypeNameMap[resourceType],
                count: 1
            };
        });

        targetResources(resourceItems.length
            ? resourceItems
            : [
                {
                    resourceType: "*",
                    count: 1
                }
            ]);
    });

    const customSubscriptionIds = ko.observableArray<string>();
    dataModel.basics.subscription.subscribeAndRun(lifetimeManager, subscription => {
        customSubscriptionIds(subscription ? [subscription.subscriptionId] : []);
    });

    const tagsControl = TagsByResource.create(lifetimeManager, {
        resources: targetResources,
        hideResourceColumn,
        customSubscriptionIds,
        value: dataModel.tags.tagsByResource
    });

    ko.computed(lifetimeManager, () => {
        tagsControl.disabled(options.disableTags());
    });

    const delayedValue = ko.pureComputed(tagsControl.value).extend({ rateLimit: { method: "notifyWhenChangesStop", timeout: 500 } });
    delayedValue.subscribe(lifetimeManager, taggedResources => {
        const updatedTags: Tag[] = MsPortalFx.mapMany(taggedResources || [], item => {
            return item.tags.map(tagItem => {
                return {
                    resourceType: item.id,
                    key: tagItem.name,
                    value: tagItem.value
                };
            });
        });
        dataModel.tags.tags(updatedTags);
    });

    return tagsControl;
}
