import Resources = require("Resx/ClientResources");
import * as DropDown from "Fx/Controls/DropDown";
import * as InfoBox from "Fx/Controls/InfoBox";
import * as RadioButtons from "Fx/Controls/RadioButtons";
import * as ResourceGroupDropDown from "Fx/Controls/ResourceGroupDropDown";
import * as Section from "Fx/Controls/Section";
import * as NoPdlUiHelpers from "../Utilities/NoPdlUiHelpers";
import * as LocationDropDown from "Fx/Controls/LocationDropDown";
import { ArmId } from "Fx/ResourceManagement";

import * as Constants from "../../../Shared/Constants";
import { DocLinkIds, getDocumentUrl } from "../../../Shared/DocLinks";
import { appendRandomNumberToValue } from "../../../Shared/ViewAgnostic/Utilities";
import { Tab } from "../Models/ControlModels";
import { NoPdlUiHelpers as NoPdlUiHelperConstants } from "../Utilities/NoPdlUiConstants";
import { ManagedKeyVaultType, WizardContext } from "../ViewModels/CreateMachineLearningModels";
import * as AIServicesDropdown from "../Controls/Dropdowns/AIServicesDropdown";
import * as CreateBasicsTab from "./Create/CreateBasicsTab";
import * as StorageAccountDropdown from "../Controls/Dropdowns/StorageAccountDropdown";
import * as KeyvaultDropdown from "../Controls/Dropdowns/KeyvaultDropdown";
import * as AppInsightsDropdown from "../Controls/Dropdowns/AppInsightsDropdown";
import * as ContainerRegistryDropdown from "../Controls/Dropdowns/ContainerRegistryDropdown";
import * as HubDropdown from "../Controls/Dropdowns/HubDropdown";
import { IAssociatedResource, AssociatedResourceKind } from "../Controls/Dropdowns/AssociatedResourceDropdown";
import * as WorkspaceNameControl from "../Controls/CreateWorkspaceNameControl";
import * as FriendlyNameControl from "../Controls/CreateFriendlyNameControl";
import * as CreateHubConfigDefaultResourceGroupControl from "../Controls/CreateHubConfigDefaultResourceGroup";
import {
    IAppInsightsTemplateResource,
    ICognitiveServiceAccountTemplateResource,
    IContainerRegistryTemplateResource,
    IKeyvaultTemplateResource,
    IStorageAccountTemplateResource,
} from "../Create.Types";

import { Workspace } from "../../../MLServicesDataModels/Workspace.types";
import { IAppInsightsResource, ICognitiveServiceAccountResource, IContainerRegistryResource, IKeyvaultResource, IStorageAccountResource} from "../../../Resource/Data/Data.Types";
import { Flighting } from "../../../Shared/Flighting";
import { WorkspaceMappedKind } from "../../../Shared/Enums";

interface ProviderRegistrationData {
    [provider: string]: boolean;
}
const defaultWorkspaceLocation = "eastus2";

const keyVaultPrefixWhenStartingWithDigit = "a";
const appendedRandomNumberLength = 10;
const maximumWorkspaceNamePrefixLength = 14;

const resourceTypes = [Constants.machineLearningWorkspaceType];

export class BasicsTab implements Tab {
    public id: string;
    public name: string;
    public section: Section.Contract;
    private _subscriptionProviderRegStatusCache: { [subscriptionId: string]: ProviderRegistrationData } = {};

    private _basicsTab: CreateBasicsTab.Contract;
    private _context: WizardContext;

    // info boxes
    private _additionalResourcesInfoBox: InfoBox.Contract;
    private _permissionWarningBox: InfoBox.Contract;

    private _showAssociatedResources: KnockoutObservableBase<boolean>;
    private _allowedAIServiceLocations: KnockoutObservable<string[]>;

    public constructor(context: WizardContext) {
        this._context = context;

        this._showAssociatedResources = ko.observable(true);
        this._allowedAIServiceLocations = ko.observable<string[]>([]);

        this._initializeCreateTabInfoBoxes();

        const basicsOptions = this._createOptions(this._context.workspaceKind);
        this._basicsTab = CreateBasicsTab.create(basicsOptions);

        this.id = this._basicsTab.id;
        this.name = this._basicsTab.name;
        this.section = this._basicsTab.section;

        this._initializeHandlers();
    }

    public triggerValidation(): Promise<boolean> {
        return this._basicsTab.triggerValidation();
    }

    private _createOptions(kind: WorkspaceMappedKind): CreateBasicsTab.Options {
        switch (kind) {
            case WorkspaceMappedKind.Hub: {
                return this._createHubOptions();
            }
            case WorkspaceMappedKind.Project: {
                return this._createProjectOptions();
            }
            default: {
                return this._createDefaultOptions();
            }
        }
    }

    private _createHubOptions(): CreateBasicsTab.Options {
        const context = this._context;
        const { resourceProviders } = context;

        const adbWorkspaceLocation = context.dataModel.basics.adbWorkspaceLocation;
        const initialLocationNameValue = [
            adbWorkspaceLocation(),
            ...context.provisioning.initialValues.locationNames.reverse(),
            defaultWorkspaceLocation
        ].filter(l => !!l); // Eliminate null locations

        return {
            context,
            detailsOptions: {
                overrideTitle: Resources.BasicsTab.organization,
                text: Resources.basicsBladeHubDetailsIntro,
                createControls: (locationDropDown: LocationDropDown.Contract) => {
                    const controls: any[] = [];
                    controls.push(locationDropDown);
                    return controls;
                }
            },
            instanceOptions: {
                overrideTitle: Resources.BasicsTab.workspaceHubDetails,
                createControls: () => {
                    const controls: any[] = [];
                    controls.push(this._createWorkspaceNameControl({ infoBalloonContent: Resources.BasicsTab.HubName.balloonContent, validationWorkspaceNameAlreadyInUse: Resources.validationHubNameAlreadyInUse }));
                    controls.push(FriendlyNameControl.create(context));
                    controls.push(CreateHubConfigDefaultResourceGroupControl.create(context));

                    controls.push(NoPdlUiHelpers.createSectionHeader(Resources.BasicsTab.aIServiceSectionHeader));
                    controls.push(this._createAIServiceDropdown());

                    controls.push(this._permissionWarningBox);
                    return controls;
                }
            },
            subscriptionOptions: {
                resourceProviders: resourceProviders,
                overrideInfoBalloonContent: Resources.BasicsTab.Subscription.balloonContent
            },
            resourceGroupOptions: {
                initialResourceGroupName: context.provisioning.initialValues.resourceGroupNames,
                overrideInfoBalloonContent: Resources.BasicsTab.ResourceGroup.balloonContent
            },
            locationOptions: {
                overrideLabel: Resources.createLocationLabelRegion,
                resourceTypes,
                initialLocationName: initialLocationNameValue,
                allowedLocations: this._allowedAIServiceLocations,
                overrideInfoBalloonContent: {
                    htmlTemplate: `<div>
                                    <p>${Resources.BasicsTab.Region.balloonContent}</p>
                                    <div><a href='${getDocumentUrl(DocLinkIds.ComputeTargets)}' target='_blank'> ${
                        Resources.BasicsTab.Region.learnMoreComputeTargets
                    }</a></div>
                                    <div><a href='https://azure.microsoft.com/en-us/global-infrastructure/services/?products=virtual-machines' target='_blank'> ${
                                        Resources.BasicsTab.Region.viewAvailableVirtualMachines
                                    }</a></div>
                                </div>`
                }
            }
        };
    }

    private _createProjectOptions(): CreateBasicsTab.Options {
        const context = this._context;
        const { resourceProviders } = context;

        return {
            context,
            detailsOptions: {
                overrideTitle: Resources.BasicsTab.organization,
                text: Resources.basicsBladeProjectDetailsIntro,
                createControls: (_locationDropDown: LocationDropDown.Contract) => {
                    const controls: any[] = [];
                    return controls;
                }
            },
            instanceOptions: {
                overrideTitle: Resources.BasicsTab.workspaceHubDetails,
                createControls: () => {
                    const controls: any[] = [];
                    controls.push(this._createWorkspaceNameControl({ infoBalloonContent: Resources.BasicsTab.ProjectName.balloonContent, validationWorkspaceNameAlreadyInUse: Resources.validationProjectNameAlreadyInUse }));
                    controls.push(FriendlyNameControl.create(context));

                    controls.push(NoPdlUiHelpers.createSectionHeader(Resources.BasicsTab.hubSectionHeader));
                    controls.push(NoPdlUiHelpers.createTextContent(Resources.BasicsTab.hubSectionContent, true));
                    controls.push(this._createHubDropdown());

                    controls.push(this._permissionWarningBox);
                    return controls;
                }
            },
            subscriptionOptions: {
                resourceProviders: resourceProviders,
                overrideInfoBalloonContent: Resources.BasicsTab.Subscription.balloonContent
            },
            resourceGroupOptions: {
                initialResourceGroupName: context.provisioning.initialValues.resourceGroupNames,
                overrideInfoBalloonContent: Resources.BasicsTab.ResourceGroup.balloonContent
            }
        };
    }

    private _createDefaultOptions(): CreateBasicsTab.Options {
        const context = this._context;
        const { resourceProviders } = context;

        const adbWorkspaceLocation = this._context.dataModel.basics.adbWorkspaceLocation;
        const initialLocationNameValue = [
            adbWorkspaceLocation(),
            ...this._context.provisioning.initialValues.locationNames.reverse(),
            defaultWorkspaceLocation
        ].filter(l => !!l); // Eliminate null locations

        return {
            context,
            detailsOptions: {
                text: Resources.basicsBladeDetailsIntro,
                link: getDocumentUrl(DocLinkIds.ResourceGroups),
                linkText: Resources.basicsBladeDetailsIntroLearnMore
            },
            instanceOptions: {
                createControls: locationDropDown => {
                    const controls: any[] = [];

                    controls.push(this._createWorkspaceNameControl({ infoBalloonContent: Resources.BasicsTab.WorkspaceName.balloonContent, validationWorkspaceNameAlreadyInUse: Resources.validationWorkspaceNameAlreadyInUse }));
                    controls.push(locationDropDown);

                    controls.push(this._createStorageAccountDropdown());
                    controls.push(this._createManagedKeyVaultRadioButtons());
                    controls.push(this._createKeyvaultDropdown());
                    controls.push(this._createAppInsightsDropdown());
                    controls.push(this._createContainerRegistryDropdown());

                    controls.push(this._permissionWarningBox);

                    return controls;
                },
                text: Resources.basicsBladeInstanceIntro,
                link: getDocumentUrl(DocLinkIds.MachineLearningWorkspace),
                linkText: Resources.basicsBladeInstanceIntroLearnMore,
                overrideTitle: Resources.BasicsTab.workspaceDetails
            },
            subscriptionOptions: {
                resourceProviders: resourceProviders,
                overrideInfoBalloonContent: Resources.BasicsTab.Subscription.balloonContent
            },
            resourceGroupOptions: {
                initialResourceGroupName: this._context.provisioning.initialValues.resourceGroupNames,
                overrideInfoBalloonContent: Resources.BasicsTab.ResourceGroup.balloonContent
            },
            locationOptions: {
                overrideLabel: Resources.createLocationLabelRegion,
                resourceTypes,
                initialLocationName: initialLocationNameValue,
                overrideInfoBalloonContent: {
                    htmlTemplate: `<div>
                                    <p>${Resources.BasicsTab.Region.balloonContent}</p>
                                    <div><a href='${getDocumentUrl(DocLinkIds.ComputeTargets)}' target='_blank'> ${
                        Resources.BasicsTab.Region.learnMoreComputeTargets
                    }</a></div>
                                    <div><a href='https://azure.microsoft.com/en-us/global-infrastructure/services/?products=virtual-machines' target='_blank'> ${
                                        Resources.BasicsTab.Region.viewAvailableVirtualMachines
                                    }</a></div>
                                </div>`
                },
            }
        };
    }

    private _initializeCreateTabInfoBoxes(): void {
        const { lifetimeManager } = this._context;
        this._additionalResourcesInfoBox = InfoBox.create(lifetimeManager, {
            text: {
                htmlTemplate: NoPdlUiHelperConstants.infoText,
                viewModel: {
                    infoText: Resources.BasicsTab.InfoText.additionalResourceInfoText,
                    storageLinkText: Resources.BasicsTab.InfoText.azureStorage,
                    storageLinkUri: MsPortalFx.getEnvironmentValue("machineLearningServicesStorageUrl"),
                    insightsLinkText: Resources.BasicsTab.InfoText.azureApplicationInsights,
                    insightsLinkUri: MsPortalFx.getEnvironmentValue("machineLearningServicesAppInsightsUrl"),
                    vaultLinkText: Resources.BasicsTab.InfoText.azureKeyVault,
                    vaultLinkUri: MsPortalFx.getEnvironmentValue("machineLearningServicesKeyVaultUrl")
                }
            },
            style: InfoBox.Style.Info,
            visible: false
        });

        this._permissionWarningBox = InfoBox.create(lifetimeManager, {
            text: {
                htmlTemplate: NoPdlUiHelperConstants.permissionWarningText,
                viewModel: {
                    warningText: Resources.BasicsTab.WarningText.workspaceErrorMessageText,
                    learnMoreLinkText: Resources.learnMore,
                    learnMoreLinkUri: MsPortalFx.getEnvironmentValue("machineLearningServicesWarningUrl")
                }
            },
            style: InfoBox.Style.Warning,
            visible: false
        });
    }

    private _initializeHandlers(): void {
        const { dataModel, lifetimeManager, workspaceKind, dataContext } = this._context;
        const { basics } = dataModel;

        basics.subscription.subscribe(lifetimeManager, newSubscriptionValue => {
            if (newSubscriptionValue && newSubscriptionValue.subscriptionId) {
                const locations: string[] = [];
                dataContext.aiServicesData.listAIServicesSkus(newSubscriptionValue.subscriptionId).then(skus => {
                    skus.forEach(sku => {
                        if (sku.kind === "AIServices" && sku.locations) {
                            locations.push(...sku.locations.map(loc => loc.toLowerCase()))
                        }
                    });
                    this._allowedAIServiceLocations(locations);
                }).catch(() => {});

                const providersToCheck = [
                    this._checkProviderRegistered(newSubscriptionValue.subscriptionId, Constants.machineLearningStorageResourceProvider),
                    this._checkProviderRegistered(newSubscriptionValue.subscriptionId, Constants.machineLearningKeyVaultProvider),
                    this._checkProviderRegistered(newSubscriptionValue.subscriptionId, Constants.machineLearningAppInsightsProvider),
                    this._checkProviderRegistered(newSubscriptionValue.subscriptionId, Constants.machineLearningContainerRegistryProvider),
                    this._checkProviderRegistered(newSubscriptionValue.subscriptionId, Constants.machineLearningComputeResourceProvider),
                    this._checkProviderRegistered(newSubscriptionValue.subscriptionId, Constants.machineLearningServicesResourcesProvider)
                ];
                if (workspaceKind === WorkspaceMappedKind.Hub) {
                    providersToCheck.push(this._checkProviderRegistered(newSubscriptionValue.subscriptionId, Constants.cogServicesProvider));
                }
                Q.allSettled(providersToCheck).then(([storageSupported, keyVaultSupported, appInsightSupported, registrySupported, _computeSupported, mlservicesSupported, cogServicesSupported]) => {
                    if (
                        storageSupported.value &&
                        keyVaultSupported.value &&
                        appInsightSupported.value &&
                        registrySupported.value &&
                        mlservicesSupported.value &&
                        (!cogServicesSupported || cogServicesSupported.value)
                    ) {
                        this._displayAdditionalResourcesInfoBox();
                    } else {
                        this._hideAllInfoBoxes();
                        this._tryRegisteringProvider(newSubscriptionValue.subscriptionId);
                    }
                });
                this._context.dataModel.options.isADSLGen2FlightEnabledForLocation(Flighting.ADSLGen2.isEnabled(newSubscriptionValue.subscriptionId));
            } else {
                this._displayAdditionalResourcesInfoBox();
            }
        });
    }

    private _tryRegisteringProvider(subscriptionId: string) {
        const { dataContext, workspaceKind } = this._context;
        const providersToRegister = [
            dataContext.providerRegistrationData.registerProvider(subscriptionId, Constants.machineLearningStorageResourceProvider),
            dataContext.providerRegistrationData.registerProvider(subscriptionId, Constants.machineLearningKeyVaultProvider),
            dataContext.providerRegistrationData.registerProvider(subscriptionId, Constants.machineLearningAppInsightsProvider),
            dataContext.providerRegistrationData.registerProvider(subscriptionId, Constants.machineLearningContainerRegistryProvider),
            dataContext.providerRegistrationData.registerProvider(subscriptionId, Constants.machineLearningComputeResourceProvider),
            dataContext.providerRegistrationData.registerProvider(subscriptionId, Constants.machineLearningServicesResourcesProvider)
        ];
        if (workspaceKind === WorkspaceMappedKind.Hub) {
            providersToRegister.push(dataContext.providerRegistrationData.registerProvider(subscriptionId, Constants.cogServicesProvider));
        }
        Q.all(providersToRegister)
            .then(() => {
                this._displayAdditionalResourcesInfoBox();
            })
            .catch(([err]) => {
                this._displayWarningInfoBox();
            });
    }

    private _createWorkspaceNameControl(stringsResources: { infoBalloonContent: string, validationWorkspaceNameAlreadyInUse: string }): Section.Contract {
        return WorkspaceNameControl.create(this._context, {
            infoBalloonContent: stringsResources.infoBalloonContent,
            validationWorkspaceNameAlreadyInUse: stringsResources.validationWorkspaceNameAlreadyInUse,
            checkMachineLearningWorkspaceAvailability: this._checkMachineLearningWorkspaceAvailability.bind(this),
            updateDependentResourcesValuesOnExistingWorkspaceUpdate: this._updateDependentResourcesValuesOnExistingWorkspaceUpdate.bind(this),
            updateDependentResourcesValuesOnNewWorkspaceFieldUpdate: this._updateDependentResourcesValuesOnNewWorkspaceFieldUpdate.bind(this),
            machineLearningServicesData: this._context.dataContext.machineLearningServicesData,
            setShowAssociatedResources: (value: boolean) => this._showAssociatedResources(value)
        });
    }

    private _createAIServiceDropdown(): DropDown.Contract<IAssociatedResource> {
        const { lifetimeManager, dataContext, dataModel } = this._context;

        const aiServicesDropdown = AIServicesDropdown.create(this._context, {
            AIServicesData: dataContext.aiServicesData,
            resourceGroup: dataModel.basics.resourceGroup,
            subscription: dataModel.basics.subscription,
            workspaceName: dataModel.basics.workspaceName
        });

        aiServicesDropdown.value.subscribe(lifetimeManager, (newValue: IAssociatedResource) => {
            let aiServicesResource: ICognitiveServiceAccountTemplateResource = null;

            if (newValue) {
                if (newValue.kind === AssociatedResourceKind.existing) {
                    const aiServices = newValue.data as ICognitiveServiceAccountResource;
                    aiServicesResource = {
                        isNew: false,
                        location: aiServices?.location,
                        kind: aiServices?.kind,
                        name: newValue.name,
                        resourceGroupName: newValue.resourceGroupName,
                        id: aiServices?.id
                    };
                } else if (newValue.kind === AssociatedResourceKind.new || newValue.kind === AssociatedResourceKind.autoGenerated) {
                    aiServicesResource = {
                        isNew: true,
                        name: newValue.name,
                        kind: "AIServices"
                    };
                }
            }

            dataModel.basics.aiServices(aiServicesResource);
        });

        return aiServicesDropdown;
    }

    private _createStorageAccountDropdown(): DropDown.Contract<any> {
        const { dataContext, lifetimeManager, dataModel } = this._context;
        const storageAccountDropdown = StorageAccountDropdown.create(this._context, {
            location: dataModel.basics.location,
            storageAccountData: dataContext.storageAccountData,
            subscription: dataModel.basics.subscription,
            visible: ko.computed(lifetimeManager, () => this._showAssociatedResources()),
            workspaceName: dataModel.basics.workspaceName,
            isHnsCreateOptionSupported: this._context.dataModel.options.isHnsCreateOptionSupported,
            isHnsSelectionSupported: this._context.dataModel.options.isHnsSelectionSupported
        });

        storageAccountDropdown.value.subscribe(lifetimeManager, (newValue: IAssociatedResource) => {
            let storageAccountResource: IStorageAccountTemplateResource = null;

            if (newValue) {
                const storageAccount = newValue.data as IStorageAccountResource;
                if (newValue.kind === AssociatedResourceKind.existing) {
                    storageAccountResource = {
                        isNew: false,
                        location: storageAccount?.location,
                        name: newValue.name,
                        resourceGroupName: newValue.resourceGroupName,
                        sku: storageAccount?.sku?.name,
                        id: storageAccount?.id
                    };
                } else if (newValue.kind === AssociatedResourceKind.new || newValue.kind === AssociatedResourceKind.autoGenerated) {
                    storageAccountResource = {
                        isNew: true,
                        name: newValue.name,
                        sku: storageAccount?.sku?.name,
                        isHnsEnabled: storageAccount?.properties?.isHnsEnabled === true
                    };
                }
            }

            dataModel.basics.storageAccount(storageAccountResource);
        });

        return storageAccountDropdown;
    }

    private _createManagedKeyVaultRadioButtons(): RadioButtons.Contract<any> {
        const { lifetimeManager, dataModel } = this._context;
        const { advanced, options } = dataModel;
        const managedKeyVaultTypeRadioButtons = RadioButtons.create(lifetimeManager, {
            items: [
                { text: Resources.Keyvault.RadioButton.managedKeyVault, value: "managedKeyVault" },
                { text: Resources.Keyvault.RadioButton.byoKeyVault, value: "byoKeyVault" }
            ],
            value: "byoKeyVault",
            label: Resources.Keyvault.RadioButton.label,
            singleItemPerLine: true,
            validations: [
                new MsPortalFx.ViewModels.RequiredValidation()
            ],
            visible: false // hide managed key vault selection for default workspace type for now
        });
        managedKeyVaultTypeRadioButtons.value.subscribe(lifetimeManager, (newValue: ManagedKeyVaultType) => {
            advanced.managedKeyVaultType(newValue);
            if (newValue === "managedKeyVault") {
                dataModel.basics.keyVault(null);
            }
        });
        return managedKeyVaultTypeRadioButtons;
    }

    private _createKeyvaultDropdown(): DropDown.Contract<any> {
        const { lifetimeManager, dataContext, dataModel } = this._context;

        const keyvaultDropdown = KeyvaultDropdown.create(this._context, {
            location: dataModel.basics.location,
            keyvaultData: dataContext.keyVaultData,
            subscription: dataModel.basics.subscription,
            visible: ko.computed(lifetimeManager, () => this._showAssociatedResources()),
            workspaceName: dataModel.basics.workspaceName
        });

        keyvaultDropdown.value.subscribe(lifetimeManager, (newValue: IAssociatedResource) => {
            let keyvaultResource: IKeyvaultTemplateResource = null;

            if (newValue) {
                if (newValue.kind === AssociatedResourceKind.existing) {
                    const keyVault = newValue.data as IKeyvaultResource;
                    keyvaultResource = {
                        isNew: false,
                        location: keyVault?.location,
                        name: newValue.name,
                        resourceGroupName: newValue.resourceGroupName,
                        id: keyVault?.id
                    };
                } else if (newValue.kind === AssociatedResourceKind.new || newValue.kind === AssociatedResourceKind.autoGenerated) {
                    keyvaultResource = {
                        isNew: true,
                        name: newValue.name
                    };
                }
            }

            dataModel.basics.keyVault(keyvaultResource);
        });

        return keyvaultDropdown;
    }

    private _createAppInsightsDropdown(): DropDown.Contract<IAssociatedResource> {
        const { lifetimeManager, dataContext, dataModel } = this._context;

        const appInsightsDropdown = AppInsightsDropdown.create(this._context, {
            appInsightsData: dataContext.appInsightsData,
            logWorkspaceName: dataModel.basics.appInsightsLogWorkspaceName,
            subscription: dataModel.basics.subscription,
            resourceGroup: dataModel.basics.resourceGroup,
            visible: ko.computed(lifetimeManager, () => this._showAssociatedResources()),
            workspaceName: dataModel.basics.workspaceName
        });

        appInsightsDropdown.value.subscribe(lifetimeManager, (newValue: IAssociatedResource) => {
            let appInsightsResource: IAppInsightsTemplateResource = null;

            if (newValue) {
                if (newValue.kind === AssociatedResourceKind.existing) {
                    const appInsights = newValue.data as IAppInsightsResource;
                    appInsightsResource = {
                        isNew: false,
                        location: appInsights?.location,
                        name: newValue.name,
                        resourceGroupName: newValue.resourceGroupName,
                        id: appInsights?.id
                    };
                } else if (newValue.kind === AssociatedResourceKind.new || newValue.kind === AssociatedResourceKind.autoGenerated) {
                    appInsightsResource = {
                        isNew: true,
                        name: newValue.name
                    };
                }
            }

            dataModel.basics.appInsights(appInsightsResource);
        });

        return appInsightsDropdown;
    }

    private _createContainerRegistryDropdown(): DropDown.Contract<IAssociatedResource> {
        const { lifetimeManager, dataContext, dataModel } = this._context;

        const containerRegistryDropdown = ContainerRegistryDropdown.create(this._context, {
            containerRegistryData: dataContext.containerRegistryData,
            subscription: dataModel.basics.subscription,
            visible: ko.computed(lifetimeManager, () => this._showAssociatedResources()),
            workspaceName: dataModel.basics.workspaceName
        });

        containerRegistryDropdown.value.subscribe(lifetimeManager, (newValue: IAssociatedResource) => {
            let containerRegistryResource: IContainerRegistryTemplateResource = null;

            if (newValue) {
                const containerRegistry = newValue.data as IContainerRegistryResource;
                if (newValue.kind === AssociatedResourceKind.existing) {
                    containerRegistryResource = {
                        isNew: false,
                        location: containerRegistry?.location,
                        name: newValue.name,
                        resourceGroupName: newValue.resourceGroupName,
                        sku: containerRegistry?.sku?.name,
                        id: containerRegistry?.id
                    };
                } else if (newValue.kind === AssociatedResourceKind.new || newValue.kind === AssociatedResourceKind.autoGenerated) {
                    containerRegistryResource = {
                        isNew: true,
                        name: newValue.name,
                        sku: containerRegistry?.sku?.name
                    };
                }
            }

            dataModel.basics.containerRegistry(containerRegistryResource);
        });

        return containerRegistryDropdown;
    }


    private _createHubDropdown(): DropDown.Contract<any> {
        const { dataContext, lifetimeManager, dataModel} = this._context;
        const initialHub = dataModel.basics.hub();
        const hubDropdown =  HubDropdown.create(this._context, {
            MachineLearningServicesData: dataContext.machineLearningServicesData,
            required: true,
            subscription: dataModel.basics.subscription,
            visible: ko.computed(lifetimeManager, () => this._showAssociatedResources()),
            workspaceName: dataModel.basics.workspaceName,
            initialValue: initialHub ? {
                name: initialHub.name,
                kind: AssociatedResourceKind.existing,
                resourceGroupName : ArmId.parse(initialHub.id).resourceGroup,
                subscriptionId: ArmId.parse(initialHub.id).subscription,
            } : null
        });

        hubDropdown.value.subscribe(lifetimeManager, (newValue: IAssociatedResource) => {
            if (newValue) {
                const hub = newValue.data as Workspace;
                this._updateDependentResourcesValuesOnExistingWorkspaceUpdate(hub);
                const hubResource = {
                    isNew: false,  // New hubs cannot be created directly in blade, so no need to handle new hub condition
                    name: hub.name,
                    id: hub.id,
                    location: hub.location ?? ArmId.parse(hub.id).location,
                    resourceGroupName: ArmId.parse(hub.id).resourceGroup
                }
                dataModel.basics.hub(hubResource)
                dataModel.basics.location({ name: hub.location, displayName: hub.location });
            }
        });

        return hubDropdown;
    }

    private _updateDependentResourcesValuesOnExistingWorkspaceUpdate(selectedValue?: Workspace): void {
        const { dataModel } = this._context;
        if (selectedValue) {
            const storageAccountResource = ArmId.parse(selectedValue.properties.storageAccount);
            if (storageAccountResource.kind === ArmId.Kind.Resource) {
                dataModel.basics.storageAccount({
                    isNew: false,
                    location: dataModel.basics.location()?.name,
                    name: storageAccountResource.resourceName,
                    resourceGroupName: storageAccountResource.resourceGroup
                });
            }

            if (selectedValue.properties.keyVault === null) {
                // hub has managed key vault scenario
                dataModel.basics.keyVault(null);
                dataModel.advanced.managedKeyVaultType("managedKeyVault");
            } else {
                const keyVaultResource = ArmId.parse(selectedValue.properties.keyVault);
                if (keyVaultResource.kind === ArmId.Kind.Resource) {
                    dataModel.basics.keyVault({
                        isNew: false,
                        location: dataModel.basics.location()?.name,
                        name: keyVaultResource.resourceName,
                        resourceGroupName: keyVaultResource.resourceGroup
                    });
                    dataModel.advanced.managedKeyVaultType("byoKeyVault");
                }
            }

            const appInsightsResource = ArmId.parse(selectedValue.properties.applicationInsights);
            if (appInsightsResource.kind === ArmId.Kind.Resource) {
                dataModel.basics.appInsights({
                    isNew: false,
                    location: dataModel.basics.location()?.name,
                    name: appInsightsResource.resourceName,
                    resourceGroupName: appInsightsResource.resourceGroup
                });
            }

            const containerRegistryResource = ArmId.parse(selectedValue.properties.containerRegistry);
            if (containerRegistryResource.kind === ArmId.Kind.Resource) {
                dataModel.basics.containerRegistry({
                    isNew: false,
                    location: dataModel.basics.location()?.name,
                    name: containerRegistryResource.resourceName,
                    resourceGroupName: containerRegistryResource.resourceGroup
                });
            }
        }
    }

    private _checkMachineLearningWorkspaceAvailability(workspaceName: string): Q.Promise<MsPortalFx.ViewModels.ValidationResult> {
        const subscriptionId = this._context.dataModel.basics.subscription().subscriptionId;
        const resourceGroup = this._context.dataModel.basics.resourceGroup();
        const isNewResourceGroup = resourceGroup.value ? resourceGroup.mode === ResourceGroupDropDown.Mode.CreateNew : false;

        if (!subscriptionId || !resourceGroup || isNewResourceGroup) {
            return Q({ valid: true, message: "" });
        }
        return this._checkProviderRegistered(subscriptionId, Constants.machineLearningServicesResourcesProvider).then(registered => {
            if (!registered || !resourceGroup.value.name) {
                return Q({ valid: true, message: "" });
            } else {
                return this._context.dataContext.machineLearningServicesData.checkWorkspacesAvailability(
                    subscriptionId,
                    resourceGroup.value.name,
                    workspaceName
                );
            }
        });
    }

    private _updateDependentResourcesValuesOnNewWorkspaceFieldUpdate(workspaceName: string): void {
        const { dataModel } = this._context;
        if (workspaceName) {
            dataModel.basics.aiServicesName(
                appendRandomNumberToValue(workspaceName.toLocaleLowerCase(), maximumWorkspaceNamePrefixLength, appendedRandomNumberLength)
            );
            dataModel.basics.appInsightsName(
                appendRandomNumberToValue(workspaceName.toLocaleLowerCase(), maximumWorkspaceNamePrefixLength, appendedRandomNumberLength)
            );
            dataModel.basics.storageAccountName(
                appendRandomNumberToValue(workspaceName.toLocaleLowerCase(), maximumWorkspaceNamePrefixLength, appendedRandomNumberLength)
            );

            // Key vault cannot start with digit so in this case we will add a prefix
            const keyVaultPrefix = /^\d.*/.test(workspaceName.toLocaleLowerCase()) ? keyVaultPrefixWhenStartingWithDigit : "";
            dataModel.basics.keyVaultName(
                appendRandomNumberToValue(
                    keyVaultPrefix + workspaceName.toLocaleLowerCase(),
                    maximumWorkspaceNamePrefixLength,
                    appendedRandomNumberLength
                )
            );

            dataModel.basics.containerRegistryName(
                appendRandomNumberToValue(workspaceName.toLocaleLowerCase(), maximumWorkspaceNamePrefixLength, appendedRandomNumberLength)
            );
        }
    }

    private _checkProviderRegistered(subscriptionId: string, provider: string): Q.Promise<boolean> {
        if (!this._subscriptionProviderRegStatusCache.hasOwnProperty(subscriptionId)) {
            this._subscriptionProviderRegStatusCache[subscriptionId] = {};
        }

        return this._context.dataContext.providerRegistrationData
            .getProviderRegStatus(subscriptionId, provider)
            .then((registrationState: string) => {
                if (registrationState === Constants.registeredString || registrationState === Constants.registeringString) {
                    this._subscriptionProviderRegStatusCache[subscriptionId][provider] = true;
                } else {
                    this._subscriptionProviderRegStatusCache[subscriptionId][provider] = false;
                }
            })
            .catch(() => {
                this._subscriptionProviderRegStatusCache[subscriptionId][provider] = false;
            })
            .then(() => {
                return this._subscriptionProviderRegStatusCache[subscriptionId][provider];
            });
    }

    private _displayWarningInfoBox() {
        this._permissionWarningBox.visible(true);
        this._additionalResourcesInfoBox.visible(false);
    }

    private _displayAdditionalResourcesInfoBox() {
        this._permissionWarningBox.visible(false);
        this._additionalResourcesInfoBox.visible(true);
    }

    private _hideAllInfoBoxes() {
        this._permissionWarningBox.visible(false);
        this._additionalResourcesInfoBox.visible(false);
    }
}
