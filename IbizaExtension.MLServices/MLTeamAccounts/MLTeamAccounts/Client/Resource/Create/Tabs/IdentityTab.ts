/**
 * Copyright (c) Microsoft Corporation.  All rights reserved.
 */

import * as FxComposition from "Fx/Composition";
import * as CheckBox from "Fx/Controls/CheckBox";
import * as InfoBox from "Fx/Controls/InfoBox";
import * as PricingControl from "Fx/Controls/PricingControl";
import * as RadioButtons from "Fx/Controls/RadioButtons";
import * as Section from "Fx/Controls/Section";
import * as Summary from "Fx/Controls/Summary";
import { ArmId } from "Fx/ResourceManagement";

import { WorkspaceMappedKind } from "../../../Shared/Enums";
import * as Format from "../../../Shared/Controls/Format";
import { AkaKey } from "Shared/AkaKey";
import { Tab } from "../Models/ControlModels";
import * as NoPdlUiConstants from "../Utilities/NoPdlUiConstants";
import * as NoPdlUiHelpers from "../Utilities/NoPdlUiHelpers";
import { ManagedIdentityType, StorageAccountAccessType, WizardContext } from "../ViewModels/CreateMachineLearningModels";

import Resources = require("Resx/ClientResources");
import Utilities from "Shared/Utilities";

export class IdentityTab implements Tab {
    // Sections
    private _managedIdentitySection: Section.Contract;
    private _storageAccountAccessSection: Section.Contract;
    private _dataImpactSection: Section.Contract;

    // Controls
    private _identityTypeRadioButtons: RadioButtons.Contract<ManagedIdentityType>;
    private _identityTypeDisabledWarning: InfoBox.Contract;
    private _userIdentitySelectedWarning: InfoBox.Contract;
    private _storageAccountAccessTypeRadioButtons: RadioButtons.Contract<StorageAccountAccessType>;
    private _disabledSharedKeyAccessCheckbox: CheckBox.Contract;
    private _disabledSharedKeyAccessWarningMessage: InfoBox.Contract;
    private _hbiCheckbox: CheckBox.Contract;
    private _isLoading = ko.observable<boolean>(true);
    private _userIdentityPicker: PricingControl.Contract<Microsoft_Azure_ManagedServiceIdentity.UserAssignedIdentityPickerBlade.BladeOutputParameters>;
    private _context: WizardContext;
    public id: string = NoPdlUiConstants.IdentityTab.tabId;
    public name: string = Resources.IdentityTab.title;
    public section: Section.Contract;

    constructor(context: WizardContext) {
        this._context = context;
        const { lifetimeManager, dataModel } = context;
        const children: any[] = [];

        this._createManagedIdentityControls();
        this._createStorageAccountAccessControls();
        this._createDataImpactControls();

        this._managedIdentitySection = Section.create(lifetimeManager, {
            children: [
                NoPdlUiHelpers.createSectionHeader(Resources.AdvancedTab.Section.ManagedIdentity.header, true, true),
                NoPdlUiHelpers.createInfoSectionHeader(Resources.AdvancedTab.Section.ManagedIdentity.description),
                this._identityTypeRadioButtons,
                this._userIdentityPicker,
                this._identityTypeDisabledWarning,
                this._userIdentitySelectedWarning
            ],
            cssClass: "ext-nopdlcreate-section"
        });

        const identityBasedAccessWarningText = NoPdlUiHelpers.createLink({
            text: Utilities.formatString(
                Resources.AdvancedTab.Section.StorageAccountAccess.identityBasedWarningText,
                `<strong>${Resources.AdvancedTab.Section.StorageAccountAccess.important}:</strong>`,
                "<strong>Storage Blob Data Contributor</strong>",
                "<strong>Storage File Privileged Contributor</strong>",
                `<strong>${Resources.AdvancedTab.Section.StorageAccountAccess.individualUsers}</strong>`
            ),
            linkText: Resources.AdvancedTab.Section.StorageAccountAccess.identityBasedWarningLearnMore,
            linkUri:
                this._context.workspaceKind === WorkspaceMappedKind.Hub
                    ? `https://aka.ms/${AkaKey.AiHubStorageAccountRoleAssignments}`
                    : `https://aka.ms/${AkaKey.AMLStorageAccountRoleAssignments}`
        });

        const identityBasedAccessWarning = InfoBox.create(lifetimeManager, {
            style: InfoBox.Style.Warning,
            text: identityBasedAccessWarningText,
            visible: ko.computed(lifetimeManager, () => dataModel.advanced.systemDatastoresAuthMode() === "identity")
        });

        this._storageAccountAccessSection = Section.create(lifetimeManager, {
            children: [
                NoPdlUiHelpers.createSectionHeader(Resources.AdvancedTab.Section.StorageAccountAccess.header, true, true),
                NoPdlUiHelpers.createTextContent(Resources.AdvancedTab.Section.StorageAccountAccess.description, true),
                this._storageAccountAccessTypeRadioButtons,
                identityBasedAccessWarning,
                this._disabledSharedKeyAccessCheckbox,
                this._disabledSharedKeyAccessWarningMessage
            ],
            visible: true,
            cssClass: "ext-nopdlcreate-section"
        });

        this._dataImpactSection = Section.create(lifetimeManager, {
            children: [
                NoPdlUiHelpers.createSectionHeader(Resources.AdvancedTab.Section.DataImpact.title, true, true),
                NoPdlUiHelpers.createInfoSectionHeader(Resources.AdvancedTab.Section.DataImpact.header),
                this._hbiCheckbox
            ],
            visible: ko.observable(true),
            cssClass: "ext-nopdlcreate-section"
        });

        children.push(...this._childrenForKind(this._context.workspaceKind));

        this.section = Section.create(lifetimeManager, {
            name: Resources.IdentityTab.title,
            children,
            cssClass: "ext-focus-container"
        });

        this._initializeHandlers();
    }

    public onLoad(): void {
        this._isLoading(false);
        this._managedIdentitySection.triggerValidation();
    }

    public triggerValidation(): Q.Promise<boolean> {
        const { lifetimeManager } = this._context;
        return Q.all([NoPdlUiHelpers.validateSection(lifetimeManager, this._managedIdentitySection)]).then(
            results => results.every(r => r),
            () => false
        );
    }

    private _initializeHandlers(): void {
        const { lifetimeManager, dataModel } = this._context;
        const { basics, advanced } = dataModel;

        basics.subscription.subscribe(lifetimeManager, subscription => {
            this._resetUserIdentityData();
        });
    }

    private _childrenForKind(kind: WorkspaceMappedKind): any[] {
        switch (kind) {
            case WorkspaceMappedKind.Hub: {
                return [this._managedIdentitySection, this._storageAccountAccessSection];
            }
            case WorkspaceMappedKind.Project: {
                return [this._managedIdentitySection, this._storageAccountAccessSection];
            }
            default: {
                return [this._managedIdentitySection, this._storageAccountAccessSection, this._dataImpactSection];
            }
        }
    }

    private _userIdentityAllowed(): boolean {
        const { dataModel } = this._context;
        const { basics, advanced } = dataModel;
        // Managed user identity is allowed only if bring your own storage account, key vault and
        // container registry resources are used.
        // Because the selected managed user identity needs to have some permissions on those resources
        // in order to get the workspace created and functioned properly.
        // This is not possible for newly created resources and we should go with system identity in that case.
        // Managed key vault is an exception and should allow UAI selection.
        const keyVault = basics.keyVault();
        const managedKeyVaultType = advanced.managedKeyVaultType();
        if (managedKeyVaultType === "byoKeyVault" && (!keyVault || keyVault.isNew)) {
            return false;
        }

        const storageAccount = basics.storageAccount();
        if (!storageAccount || storageAccount.isNew) {
            return false;
        }

        // Container registry is optional. If it's null, that means
        // no container registry is associated with the workspace
        // and managed user identity can be allowed since there is no
        // permission required on non-existing resource.
        const containerRegistry = basics.containerRegistry();
        if (containerRegistry && containerRegistry.isNew) {
            return false;
        }

        return true;
    }

    private _createManagedIdentityControls(): void {
        const { container, lifetimeManager, dataModel } = this._context;
        const { basics, advanced } = dataModel;

        this._identityTypeRadioButtons = RadioButtons.create(lifetimeManager, {
            items: [
                { text: Resources.AdvancedTab.Section.ManagedIdentity.systemAssignedText, value: "systemAssigned" },
                { text: Resources.AdvancedTab.Section.ManagedIdentity.userAssignedText, value: "userAssigned" }
            ],
            value: "systemAssigned",
            label: Resources.AdvancedTab.Section.ManagedIdentity.radioGroupTitle,
            singleItemPerLine: true,
            disabled: ko.computed(lifetimeManager, () => !this._userIdentityAllowed())
        });

        this._identityTypeDisabledWarning = InfoBox.create(lifetimeManager, {
            style: InfoBox.Style.Warning,
            text: ko.observable(Resources.AdvancedTab.Section.ManagedIdentity.disabledWarning),
            visible: ko.observable(!this._userIdentityAllowed())
        });
        const messageChildren = MsPortalFx.getEnvironmentValue("showPublicLinks")
            ? [
                  {
                      htmlTemplate: `<a href="https://github.com/Azure/azure-quickstart-templates/tree/master/quickstarts/microsoft.machinelearningservices/machine-learning-dependencies-role-assignment">${Resources.AdvancedTab.Section.ManagedIdentity.permissionWarningLinkText}</a>`
                  }
              ]
            : [
                  {
                      htmlTemplate: `${Resources.AdvancedTab.Section.ManagedIdentity.permissionWarningLinkText}`
                  }
              ];
        const formattedMessage = Format.create(lifetimeManager, {
            format: Resources.AdvancedTab.Section.ManagedIdentity.permissionWarning,
            children: messageChildren
        });

        this._userIdentitySelectedWarning = InfoBox.create(lifetimeManager, {
            style: InfoBox.Style.Warning,
            text: {
                htmlTemplate: `<div data-bind="pcControl: formattedMessage"></div>`,
                viewModel: { formattedMessage }
            },
            visible: ko.observable(false)
        });

        const options: PricingControl.Options<Microsoft_Azure_ManagedServiceIdentity.UserAssignedIdentityPickerBlade.BladeOutputParameters> = {
            label: Resources.AdvancedTab.Section.ManagedIdentity.userAssignedPickerLabel,
            subText: "",
            linkText: Resources.AdvancedTab.Section.ManagedIdentity.userAssignedPickerText,
            barColor: NoPdlUiConstants.ChangeControlBarColor,
            validations: [
                new MsPortalFx.ViewModels.CustomValidation(Resources.validationWorkspaceNameAlreadyInUse, _ => {
                    let result: MsPortalFx.ViewModels.ValidationResult = {
                        valid: true,
                        message: ""
                    };

                    // Make sure an identity is selected
                    if (!(advanced.userAssignedIdentityName() && advanced.userAssignedIdentityResourceGroup())) {
                        result = {
                            valid: false,
                            message: Resources.AdvancedTab.Section.ManagedIdentity.userAssignedPickerRequiredMessage
                        };
                    }

                    return Q(result);
                })
            ],
            onLinkClick: (): void => {
                const parameters: Microsoft_Azure_ManagedServiceIdentity.UserAssignedIdentityPickerBlade.BladeParameters = {
                    selectedIdentityIds: [],
                    enableSingleSelect: true
                };

                const onClosed: FxComposition.BladeClosedWithDataHandler<
                    Microsoft_Azure_ManagedServiceIdentity.UserAssignedIdentityPickerBlade.BladeOutputParameters
                > = (
                    reason: FxComposition.BladeClosedReason,
                    data: Microsoft_Azure_ManagedServiceIdentity.UserAssignedIdentityPickerBlade.BladeOutputParameters
                ) => {
                    if (reason === FxComposition.BladeClosedReason.ChildClosedSelf && data && data.identityIds && data.identityIds.length > 0) {
                        const userIdentityId = data.identityIds[0];
                        const userIdentityArmId = ArmId.parse(userIdentityId);
                        if (MsPortalFx.localeCompareIgnoreCase(userIdentityArmId.subscription, basics.subscription().subscriptionId) !== 0) {
                            advanced.userAssignedIdentityName(null);
                            advanced.userAssignedIdentityResourceGroup(null);

                            this._userIdentityPicker.value(null);
                            this._userIdentityPicker.subText({
                                htmlTemplate: `<div data-bind="pcControl: subscriptionWarning"></div>`,
                                viewModel: {
                                    subscriptionWarning: InfoBox.createInline(lifetimeManager, {
                                        style: InfoBox.Style.Warning,
                                        text: Resources.AdvancedTab.Section.ManagedIdentity.userAssignedPickerSubscriptionMessage
                                    })
                                }
                            });
                        } else {
                            advanced.userAssignedIdentityName(userIdentityArmId.resourceName);
                            advanced.userAssignedIdentityResourceGroup(userIdentityArmId.resourceGroup);

                            this._userIdentityPicker.value(data);
                            this._userIdentityPicker.subText({
                                htmlTemplate: `<div data-bind="pcControl: userIdentity">`,
                                viewModel: {
                                    userIdentity: Summary.create(lifetimeManager, {
                                        children: [
                                            {
                                                isMultiColumn: false,
                                                children: [
                                                    {
                                                        label: Resources.AdvancedTab.Section.ManagedIdentity.userIdentityNameLabel,
                                                        value: userIdentityArmId.resourceName
                                                    },
                                                    {
                                                        label: Resources.AdvancedTab.Section.ManagedIdentity.userIdentityResourceGroupLabel,
                                                        value: userIdentityArmId.resourceGroup
                                                    }
                                                ]
                                            }
                                        ]
                                    })
                                }
                            });
                        }
                    }
                };

                const bladeReference = FxComposition.BladeReferences.forExtension("Microsoft_Azure_ManagedServiceIdentity")
                    .forBlade("UserAssignedIdentityPickerBlade")
                    .createReference({ parameters, onClosed });
                container.openContextPane(bladeReference);
            }
        };

        this._userIdentityPicker = PricingControl.create(lifetimeManager, options);
        this._userIdentityPicker.visible(false);

        this._identityTypeRadioButtons.value.subscribe(lifetimeManager, newValue => {
            advanced.identityType(this._userIdentityAllowed() ? newValue : "systemAssigned");

            const userAssigned = newValue === "userAssigned";
            this._userIdentityPicker.visible(userAssigned);
            this._userIdentitySelectedWarning.visible(userAssigned);
        });

        this._identityTypeRadioButtons.disabled.subscribe(lifetimeManager, newValue => {
            if (newValue) {
                this._identityTypeRadioButtons.value("systemAssigned");
                this._userIdentityPicker.visible(false);
            }

            this._identityTypeDisabledWarning.visible(newValue);
        });
    }

    private _createStorageAccountAccessControls(): void {
        const { lifetimeManager, dataModel, workspaceKind } = this._context;
        const { advanced } = dataModel;

        this._storageAccountAccessTypeRadioButtons = RadioButtons.create(lifetimeManager, {
            items: [
                { text: Resources.AdvancedTab.Section.StorageAccountAccess.credentialBasedText, value: "accessKey" },
                { text: Resources.AdvancedTab.Section.StorageAccountAccess.identityBasedText, value: "identity" }
            ],
            value: "accessKey",
            label: Resources.AdvancedTab.Section.StorageAccountAccess.radioGroupTitle,
            singleItemPerLine: true
        });

        this._storageAccountAccessTypeRadioButtons.value.subscribe(lifetimeManager, newValue => {
            advanced.systemDatastoresAuthMode(newValue);

            if (newValue !== "identity") {
                advanced.disableSharedKeyAccess(false);
            }
        });

        this._disabledSharedKeyAccessCheckbox = CheckBox.create(lifetimeManager, {
            label: Resources.AdvancedTab.Section.StorageAccountAccess.sharedKeyAccessCheckboxLabel,
            value: advanced.disableSharedKeyAccess,
            visible: ko.computed(lifetimeManager, () =>
                workspaceKind !== WorkspaceMappedKind.Project // Projects do not have the option to disable shared key access
                    ? advanced.systemDatastoresAuthMode() === "identity"
                    : false
            )
        });

        const docLink =
            workspaceKind === WorkspaceMappedKind.Hub
                ? "https://learn.microsoft.com/en-us/azure/ai-studio/how-to/disable-local-auth"
                : "https://learn.microsoft.com/en-us/azure/machine-learning/how-to-disable-local-auth-storage";

        const formattedMessage = Format.create(lifetimeManager, {
            format: Resources.AdvancedTab.Section.StorageAccountAccess.sharedKeyAccessDisableWarning,
            children: [
                {
                    htmlTemplate: MsPortalFx.getEnvironmentValue("showPublicLinks")
                        ? `<a href="https://azure.microsoft.com/en-us/support/legal/preview-supplemental-terms/">${Resources.AdvancedTab.Section.StorageAccountAccess.previewLinkText}</a>`
                        : `${Resources.AdvancedTab.Section.StorageAccountAccess.previewLinkText}`
                },
                {
                    htmlTemplate: `<a href="${docLink}">${Resources.AdvancedTab.Section.StorageAccountAccess.sharedKeyAccessDisableDocumentationLinkText}</a>`
                }
            ]
        });

        this._disabledSharedKeyAccessWarningMessage = InfoBox.create(lifetimeManager, {
            style: InfoBox.Style.Warning,
            text: {
                htmlTemplate: `<div data-bind="pcControl: formattedMessage"></div>`,
                viewModel: { formattedMessage }
            },
            visible: ko.computed(lifetimeManager, () => advanced.systemDatastoresAuthMode() === "identity" && advanced.disableSharedKeyAccess())
        });
    }

    private _createDataImpactControls(): void {
        const { lifetimeManager, dataModel } = this._context;
        const { advanced } = dataModel;

        this._hbiCheckbox = CheckBox.create(lifetimeManager, {
            label: Resources.AdvancedTab.Section.DataImpact.HBI.label,
            value: advanced.highBusinessImpactChecked
        });
    }

    private _resetUserIdentityData(): void {
        const { dataModel } = this._context;
        const { advanced } = dataModel;

        advanced.identityType();
        advanced.userAssignedIdentityName(null);
        advanced.userAssignedIdentityResourceGroup(null);

        this._userIdentityPicker.value(null);
        this._userIdentityPicker.subText("");
    }
}
