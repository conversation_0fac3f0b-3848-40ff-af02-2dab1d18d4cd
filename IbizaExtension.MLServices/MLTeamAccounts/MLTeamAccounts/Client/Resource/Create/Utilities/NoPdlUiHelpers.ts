import * as Accordion from "Fx/Controls/Accordion";
import { HtmlContent } from "Fx/Controls/ControlsBase";
import * as CustomHtml from "Fx/Controls/CustomHtml";
import * as EditableGrid from "Fx/Controls/EditableGrid";
import * as Section from "Fx/Controls/Section";
import Resources = require("Resx/ClientResources");
import { NoPdlUiHelpers } from "./NoPdlUiConstants";
import { LifetimeManager } from "Fx/Lifetime";

export function validateSection(lifetimeManager: MsPortalFx.Base.LifetimeManager, section: Section.Contract): Q.Promise<boolean> {
    const sectionObj = section as any;
    if (ko.isObservable(sectionObj.visible) && !sectionObj.visible()) {
        return Q(true);
    }

    const childControls = section.children();
    const controlsInAccordion = MsPortalFx.mapMany(
        childControls.filter(item => item.controlType === MsPortalFx.ViewModels.ControlType.Accordion),
        item => {
            const accordion = item as Accordion.Contract;
            return accordion.sections().map(section => section.content());
        }
    );
    childControls.push(...controlsInAccordion);
    const childPromises: Promise<boolean>[] = childControls
        .filter(item => {
            return (!ko.isObservable(item.visible) || item.visible()) && (!ko.isObservable(item.disabled) || !item.disabled());
        })
        .map(item => {
            const control = item as MsPortalFx.Control;
            if (control.controlType === MsPortalFx.ViewModels.ControlType.DynamicSection) {
                return validateSection(lifetimeManager, item);
            } else if (control.controlType === MsPortalFx.ViewModels.ControlType.EditableGrid) {
                const grid = item as EditableGrid.Contract<any>;
                return grid.validation.triggerValidation();
            } else if (item.triggerValidation) {
                return waitUntilControlLoaded(lifetimeManager, item).then(() => {
                    return item.triggerValidation();
                });
            } else {
                return Q(true);
            }
        });

    return Q.all(childPromises).then(results => {
        return results.every(isValid => isValid);
    });
}

function waitUntilControlLoaded(
    lifetimeManager: MsPortalFx.Base.LifetimeManager,
    control: { controlType: MsPortalFx.ViewModels.ControlType; loading: KnockoutObservableBase<boolean> }
) {
    // Resource group dropdown stays in loading state in Create New mode
    if (
        !ko.isObservable(control.loading) ||
        !ko.unwrap(control.loading) ||
        control.controlType === MsPortalFx.ViewModels.ControlType.ResourceDropDown
    ) {
        return Q();
    }

    const deferred = Q.defer<void>();
    const subscription = control.loading.subscribe(lifetimeManager, loading => {
        if (!loading) {
            deferred.resolve();
            subscription.dispose();
        }
    });
    return deferred.promise;
}

export function triggerValidationIfNotEmpty<T>(
    control: { triggerValidation: () => Promise<boolean>; value: KnockoutObservableBase<T> },
    isValueEmpty?: (value: T) => boolean
) {
    if (isValueEmpty ? !isValueEmpty(control.value()) : control.value()) {
        return control.triggerValidation();
    }

    return Q(true);
}

export interface BaseOptions {
    /**
     * Text preceeding the link.
     */
    text?: string | KnockoutObservableBase<string>;
    /**
     * The text to show as a clickable link. Defaults to 'Learn more', which should be used in most cases.
     */
    linkText?: string | KnockoutObservableBase<string>;
    /**
     * Aria label for the link. Must give context about what content the link will
     * reveal. As of 4/21/2020 the standard is back to "Learn more" visible text,
     * with aria giving context about what the link concerns. 'ariaLabel' will be
     * required for most links.
     */
    ariaLabel?: string | KnockoutObservableBase<string>;
    /**
     * The URI. Note that 'linkText' must be non-empty for the link to be visible.
     */
    linkUri?: string | KnockoutObservableBase<string>;
    /**
     * A click handler function. Typically used for diagnostic logging.
     */
    clickHandler?: Function;
    /**
     * Simplifies finding the link in test automation.
     */
    linkClassName?: string;
}

export interface LinkOptions extends BaseOptions {
    /**
     * 'linkUri' is required for 'LinkOptions'.
     */
    linkUri: string | KnockoutObservableBase<string>;
    /**
     * Add a 20px margin to the bottom of the resulting HtmlContent, consistent
     * with marketplace create experience text+link intros.
     */
    useIntroStyling?: boolean;
}

/**
 * Create a link to external content with optional preceeding text.
 *
 * @param options Options for creating the link. See 'LinkOptions'.
 */
export function createLink(options: LinkOptions): HtmlContent {
    const { text, linkText, useIntroStyling, clickHandler, linkUri, linkClassName, ariaLabel } = options;
    let htmlTemplate = "";
    const className = linkClassName || "ext-extensioncore-externallink-createlink";

    if (text) {
        htmlTemplate = `<span data-bind='html: text, visible: text' style='margin-right: 5px; white-space: normal'></span>`;
    }

    const learnMoreLinkText = linkText || Resources.learnMore;
    const ariaLabelText = ariaLabel || learnMoreLinkText;

    // HTML defined in single line to avoid some formatting problems when incorporated into an infobox.
    if (linkUri) {
        if (clickHandler) {
            htmlTemplate = `${htmlTemplate}<span style='white-space: pre-line; display: inline-block' data-bind='visible: learnMoreLinkText'><a class='${className}' role='link' data-bind='text: learnMoreLinkText, fxclick: clickHandler, attr: { aria-label: ariaLabelText, href: linkUri }' target='_blank' style='margin-right: 5px'></a><span data-bind='image: hyperLinkIcon' style='width: 0.8em; height: 0.8em; display: inline-block'></span></span>`;
        } else {
            htmlTemplate = `${htmlTemplate}<span style='white-space: pre-line; display: inline-block' data-bind='visible: learnMoreLinkText'><a class='${className}' role='link' data-bind='text: learnMoreLinkText, attr: { aria-label: ariaLabelText, href: linkUri }' target='_blank' style='margin-right: 5px'></a><span data-bind='image: hyperLinkIcon' style='width: 0.8em; height: 0.8em; display: inline-block'></span></span>`;
        }
    }

    if (useIntroStyling) {
        htmlTemplate = `<div style='margin-bottom: 20px'>${htmlTemplate}</div>`;
    }

    return {
        htmlTemplate: htmlTemplate,
        viewModel: {
            text: text,
            learnMoreLinkText: learnMoreLinkText,
            linkUri: linkUri,
            clickHandler: clickHandler,
            hyperLinkIcon: MsPortalFx.Base.Images.Hyperlink({ palette: MsPortalFx.Base.ImagePalette.Blue }),
            ariaLabelText: ariaLabelText
        }
    };
}

export function createSection(
    context: { lifetimeManager: LifetimeManager },
    name?: string | KnockoutObservableBase<string>,
    children?: any[] | KnockoutObservableArray<any>,
    visible?: boolean | KnockoutObservableBase<boolean>,
    includeHeaderBottomMargin = true
): Section.Contract {
    const { lifetimeManager } = context;
    if (name) {
        children = children || [];
        const header = createSectionHeader(name, visible, includeHeaderBottomMargin);
        children.unshift(header);
    }

    return Section.create(lifetimeManager, {
        children: children,
        visible: visible
    });
}

export function createSectionHeader(
    header: string | KnockoutObservableBase<string>,
    visible?: boolean | KnockoutObservableBase<boolean>,
    includeBottomMargin = true
): HtmlContent {
    return {
        htmlTemplate: includeBottomMargin ? NoPdlUiHelpers.sectionHeader : NoPdlUiHelpers.sectionHeaderNoBottomMargin,
        viewModel: {
            name: header,
            visible: MsPortalFx.isNullOrUndefined(visible) ? true : visible
        }
    };
}

export function createInfoSectionHeader(text: string | CustomHtml.Contract): HtmlContent {
    return {
        htmlTemplate: typeof text === "string" ? NoPdlUiHelpers.introText : NoPdlUiHelpers.introTextCustom,
        viewModel: {
            name: text
        }
    };
}

export function createTextContent(text: string | KnockoutObservableBase<string>, useIntroStyling: boolean = false): HtmlContent {
    return {
        htmlTemplate: `<div class='msportalfx-text-default' ${
            useIntroStyling ? "style='margin-bottom: 20px'" : ""
        } data-bind='text: textContent'></div>`,
        viewModel: {
            textContent: text
        }
    };
}

export function createTab(context: { lifetimeManager: LifetimeManager }, name: string, children?: any[], cssClass?: string) {
    const { lifetimeManager } = context;

    return Section.create(lifetimeManager, {
        name: name,
        children: children,
        cssClass: cssClass || "ext-nopdlcreate-section"
    });
}
