/**
 * Copyright (c) Microsoft Corporation.  All rights reserved.
 */

import * as BladeBase from 'Fx/Composition/BladeBase';
import { Container } from 'Fx/Composition/TemplateBlade';
import * as ResourceGroupDropDown from 'Fx/Controls/ResourceGroupDropDown';
import { LifetimeManager } from 'Fx/Lifetime';

import { CommonContext, CommonControl } from '../Models/CreateModels';
import { triggerValidationIfNotEmpty } from '../Utilities/NoPdlUiHelpers';

import Resources = require("Resx/ClientResources");
export interface Context extends CommonContext {
    container: Container;
    lifetimeManager: LifetimeManager;
    dataModel: {
        basics: {
            subscription: KnockoutObservableBase<MsPortalFx.Azure.Subscription>;
            resourceGroup: KnockoutObservableBase<ResourceGroupDropDown.Value>;
        };
    };
    provisioning: BladeBase.DoesProvisioning.Provisioning;
}

export interface BasicOptions extends CommonControl {
    initialResourceGroupName?: KnockoutObservableBase<string> | string[];
}

export interface Options extends BasicOptions {
    isTopLevel?: boolean;
}

export function create(context: Context, createOptions?: Options): ResourceGroupDropDown.Contract {
    const { container, lifetimeManager, dataModel } = context;
    // IMPORTANT: the resource group dropdown contract is inaccurate. The contract requires a LifetimeManager, however
    // in actuality the runtime code expects a "container", because it attempts to use the "openDialog" function of a container.
    // A blade container is also a LifetimeManager. A LifetimeManager is not also a container.
    // extendedLifetimeManager is a workaround to allow us to use the provided LifetimeManager and also fulfill the "openDialog" expectation
    const extendedLifetimeManager = MsPortalFx.extend(lifetimeManager, { openDialog: container.openDialog.bind(container) });
    const subscriptionObs = dataModel.basics.subscription;
    const subscriptionId = ko.pureComputed(() => subscriptionObs() && subscriptionObs().subscriptionId || "");
    const validations = (<Array<MsPortalFx.ViewModels.CustomValidation | MsPortalFx.ViewModels.RequiredValidation>>[
        new MsPortalFx.ViewModels.RequiredValidation()]).concat(
            createOptions?.customValidations || []);

    const isTopLevel = createOptions?.isTopLevel;
    const resourceGroupDropDown = ResourceGroupDropDown.create(
        extendedLifetimeManager,
        MsPortalFx.extend({
            label: Resources.createResourceGroup,
            infoBalloonContent: createOptions?.overrideInfoBalloonContent,
            value: dataModel.basics.resourceGroup,
            subscriptionId,
            validations,
            initialResourceGroupName: createOptions?.initialResourceGroupName,
            placeholder: ''
        }, { nested: !isTopLevel }));
    if (!isTopLevel) {
        const cssClass: KnockoutObservableBase<string> = (resourceGroupDropDown as any).cssClass;
        if (ko.isObservable(cssClass)) {
            cssClass("msportalfx-nested-control");
        }
    }

    subscriptionObs.subscribe(lifetimeManager, () => {
        triggerValidationIfNotEmpty(resourceGroupDropDown, value => !value || !value.value || !value.value.name);
    });

    return resourceGroupDropDown;
}
