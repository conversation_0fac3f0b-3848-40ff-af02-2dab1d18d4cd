import * as Section from "Fx/Controls/Section";
import * as DropDown from "Fx/Controls/DropDown";
import * as OptionsGroup from 'Fx/Controls/OptionsGroup';
import * as TextBox from 'Fx/Controls/TextBox';
import { Container } from 'Fx/Composition/TemplateBlade';
import Resources = require("Resx/ClientResources");
import { CommonContext, CommonControl } from '../Models/CreateModels';
import * as NoPdlUiHelpers from "../Utilities/NoPdlUiHelpers";
import * as Constants from '../../../Shared/Constants';

import { Workspace } from "../../../MLServicesDataModels/Workspace.types";
import { LifetimeManager } from "Fx/Lifetime";
import { MachineLearningServicesData } from "Resource/Data/MachineLearningServicesData";

export const newWorkspaceOptionValue = "new";
export const existingWorkspaceOptionValue = "existing";

interface INameTextBoxOptions {
    label: string | KnockoutObservableBase<string>;
    value: string | KnockoutObservableBase<string>;
    infoBalloonContent: string;
    validationWorkspaceNameAlreadyInUse: string;
    checkMachineLearningWorkspaceAvailability(this: void, workspaceName: string): Q.Promise<MsPortalFx.ViewModels.ValidationResult>;
    updateDependentResourcesValuesOnNewWorkspaceFieldUpdate(this: void, name: string): void;
}

function createNameTextBox(lifetime: MsPortalFx.Base.LifetimeManager, options: INameTextBoxOptions): TextBox.Contract {
    const { value, label, infoBalloonContent, validationWorkspaceNameAlreadyInUse, checkMachineLearningWorkspaceAvailability, updateDependentResourcesValuesOnNewWorkspaceFieldUpdate } = options;
    const textBox = TextBox.create(lifetime, {
        label,
        infoBalloonContent,
        value: value,
        validations: [
            new MsPortalFx.ViewModels.RequiredValidation(Resources.resourceNameRequired),
            new MsPortalFx.ViewModels.RegExMatchValidation(Constants.validMachineLearningServicesWorkspaceRegexStr, Resources.validationWorkspaceNameInvalid),
            new MsPortalFx.Azure.ReservedResourceNameValidator(Constants.machineLearningWorkspaceType),
            new MsPortalFx.ViewModels.CustomValidation(validationWorkspaceNameAlreadyInUse, (workspaceName: string): Q.Promise<MsPortalFx.ViewModels.ValidationResult> => {
                return checkMachineLearningWorkspaceAvailability(workspaceName);
            })
        ]
    });

    textBox.value.subscribe(lifetime, (name: string) => {
        if (name) {
            updateDependentResourcesValuesOnNewWorkspaceFieldUpdate(name);
        }
    });

    return textBox;
}

export interface Context extends CommonContext {
    container: Container;
    lifetimeManager: LifetimeManager;
    dataModel: {
        basics: CommonContext["dataModel"]["basics"] & {
            workspaceName: KnockoutObservableBase<string>;
            adbWorkspaceId: KnockoutObservableBase<string>;
        };
        tags: CommonContext["dataModel"]["tags"] & {
            disableTagsGrid: KnockoutObservableBase<boolean>;
        }
    };
}

export interface Options extends CommonControl {
    machineLearningServicesData: MachineLearningServicesData;
    infoBalloonContent: string;
    validationWorkspaceNameAlreadyInUse: string;
    checkMachineLearningWorkspaceAvailability(this: void, workspaceName: string): Q.Promise<MsPortalFx.ViewModels.ValidationResult>;
    updateDependentResourcesValuesOnNewWorkspaceFieldUpdate(this: void, name: string): void;
    updateDependentResourcesValuesOnExistingWorkspaceUpdate(this: void, name: Workspace): void;
    setShowAssociatedResources(this: void, value: boolean): void;
}

export function create(context: Context, options: Options): Section.Contract {
    const dataModel = context.dataModel;
    const lifetimeManager = context.lifetimeManager;
    const machineLearningServicesData = options.machineLearningServicesData;
    const { infoBalloonContent, validationWorkspaceNameAlreadyInUse, checkMachineLearningWorkspaceAvailability, updateDependentResourcesValuesOnNewWorkspaceFieldUpdate, updateDependentResourcesValuesOnExistingWorkspaceUpdate, setShowAssociatedResources } = options;

    const workspaceNameTextBox = createNameTextBox(lifetimeManager, {
        label: Resources.createBladeWorkspaceNameLabel,
        value: context.dataModel.basics.workspaceName,
        infoBalloonContent,
        validationWorkspaceNameAlreadyInUse,
        checkMachineLearningWorkspaceAvailability,
        updateDependentResourcesValuesOnNewWorkspaceFieldUpdate
    });

    if (!dataModel.basics.adbWorkspaceId()) {
        return NoPdlUiHelpers.createSection(context, undefined, [
            workspaceNameTextBox
        ]);
    }

    /*
     * Create elements needed for Azure Databricks to select existing workspace
     * or create new one that includes Radio Group with new/existing buttons,
     * drop down list with existing workspaces and text box for new ones
     */
    const azureDatabricksWorkspaceSectionWorkspacesListDropDown = DropDown.create<Workspace>(lifetimeManager, {
        label: Resources.createBladeWorkspaceNameLabel,
        items: ko.observableArray([]),
        visible: false
    });

    const azureDatabricksWorkspaceSectionNewOrExistingOption = OptionsGroup.create(lifetimeManager, {
        items: [
            {
                text: Resources.textAzureDatabricksOptionCreateNew,
                value: newWorkspaceOptionValue
            },
            {
                text: Resources.textAzureDatabricksOptionUseExisting,
                value: existingWorkspaceOptionValue
            }
        ],
        value: newWorkspaceOptionValue,
        label: "Workspace type",
        validations: [
            new MsPortalFx.ViewModels.RequiredValidation(Resources.resourceNameRequired),
        ]
    });

    const updateTagValues = (): void => {
        dataModel.tags.tags.removeAll();
        dataModel.tags.tagsByResource.removeAll();

        const tagsPropertyFromSelectedWorkspace =
            azureDatabricksWorkspaceSectionWorkspacesListDropDown &&
            azureDatabricksWorkspaceSectionWorkspacesListDropDown.value() &&
            azureDatabricksWorkspaceSectionWorkspacesListDropDown.value().tags;
        if (
            tagsPropertyFromSelectedWorkspace &&
            azureDatabricksWorkspaceSectionNewOrExistingOption.value() === existingWorkspaceOptionValue
        ) {
            var taggedResourcesTags: { name: string; value: string }[] = [];
            for (const key in tagsPropertyFromSelectedWorkspace) {
                dataModel.tags.tags.push({
                    key: key,
                    value: tagsPropertyFromSelectedWorkspace[key],
                    resourceType: Constants.machineLearningWorkspaceType
                });

                taggedResourcesTags.push({
                    name: key,
                    value: tagsPropertyFromSelectedWorkspace[key]
                });
            }

            if (taggedResourcesTags.length > 0) {
                dataModel.tags.tagsByResource.push({
                    id: Constants.machineLearningWorkspaceType,
                    tags: taggedResourcesTags
                });
            }
        }
    }

    const populateAzureDatabricksWorkspaceSectionWorkspacesListDropDown = (): void => {
        const azureDatabricksId = dataModel.basics.adbWorkspaceId();
        const subscriptionId = dataModel.basics.subscription().subscriptionId;
        const resourceGroup = dataModel.basics.resourceGroup().value.name;
        const location = dataModel.basics.location() ? dataModel.basics.location().name : undefined;

        if (azureDatabricksId && subscriptionId && resourceGroup && location) {
            // Clean up list in case there is error later, we don't want to have old items
            azureDatabricksWorkspaceSectionWorkspacesListDropDown.items([]);
            const getResourceGroupWorkspaces = machineLearningServicesData.getResourceGroupWorkspaces(
                subscriptionId,
                resourceGroup
            );
            getResourceGroupWorkspaces.then((machineLearningServices: Workspace[]) => {
                const workspaces: Workspace[] = machineLearningServices || [];
                const workspaceDropDownItems: DropDown.Item<Workspace>[] = workspaces
                    .map((workspaceObj: Workspace) => {
                        return {
                            text: ko.observable(workspaceObj.name),
                            value: workspaceObj
                        };
                    })
                    .filter(value => {
                        return value.value.location === location && !value.value.properties.adbWorkspace;
                    });
                azureDatabricksWorkspaceSectionWorkspacesListDropDown.items(workspaceDropDownItems);
                if (workspaceDropDownItems.length > 0) {
                    azureDatabricksWorkspaceSectionWorkspacesListDropDown.value(workspaceDropDownItems[0].value);
                }

                return workspaceDropDownItems;
            });
        }
    }

    dataModel.basics.location.subscribe(lifetimeManager, () => {
        populateAzureDatabricksWorkspaceSectionWorkspacesListDropDown();
    });

    dataModel.basics.resourceGroup.subscribe(lifetimeManager, () => {
        populateAzureDatabricksWorkspaceSectionWorkspacesListDropDown();
    });

    // Toggle between existing workspaces list and new workspace text box
    ko.reactor(lifetimeManager, [azureDatabricksWorkspaceSectionWorkspacesListDropDown.value, azureDatabricksWorkspaceSectionNewOrExistingOption.value], () => {
        if (azureDatabricksWorkspaceSectionNewOrExistingOption.value() === existingWorkspaceOptionValue) {
            workspaceNameTextBox.visible(false);
            azureDatabricksWorkspaceSectionWorkspacesListDropDown.visible(true);
            dataModel.tags.disableTagsGrid(true);

            const workspace = azureDatabricksWorkspaceSectionWorkspacesListDropDown.value();
            if (workspace) {
                dataModel.basics.workspaceName(workspace.name);
                updateDependentResourcesValuesOnExistingWorkspaceUpdate(workspace);
                updateTagValues();
            }

            setShowAssociatedResources(false);
        } else if (azureDatabricksWorkspaceSectionNewOrExistingOption.value() === newWorkspaceOptionValue) {
            workspaceNameTextBox.visible(true);
            azureDatabricksWorkspaceSectionWorkspacesListDropDown.visible(false);
            dataModel.tags.disableTagsGrid(false);

            // Reset name so that all resources are reset
            workspaceNameTextBox.value("");

            setShowAssociatedResources(true);
        }
    });

    return NoPdlUiHelpers.createSection(context, undefined, [azureDatabricksWorkspaceSectionNewOrExistingOption, workspaceNameTextBox, azureDatabricksWorkspaceSectionWorkspacesListDropDown]);
}