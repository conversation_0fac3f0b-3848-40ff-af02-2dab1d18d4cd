import { BladeClosedReason, BladeReferences } from "Fx/Composition";
import * as TemplateBlade from "Fx/Composition/TemplateBlade";
import * as DropDown from "Fx/Controls/DropDown";
import { LifetimeManager } from "Fx/Lifetime";

import { IStorageAccountResource } from "../../../../Resource/Data/Data.Types";
import { StorageAccountData } from "../../../../Resource/Data/StorageAccountData";
import { generateName } from "../../../../Shared/ViewAgnostic/Utilities";
import { Flighting } from "../../../../Shared/Flighting";
import { Parameters, ReturnData } from "../../../Blades/AssociatedResources/StorageAccountSettingsBlade";
import { CommonContext } from '../../Models/CreateModels';
import {
    AssociatedResourceKind,
    create as resourceDropdownCreate,
    IAssociatedResource,
    IAssociatedResourceDropdownOptions,
    requiresNameValidation
} from "./AssociatedResourceDropdown";

import * as ClientResources from "Resx/ClientResources";

export interface Context extends CommonContext {
    container: TemplateBlade.Container;
    lifetimeManager: LifetimeManager;
}

export interface IStorageAccountDropdownOptions {
    allowCreate?: boolean;
    infoBalloonContent?: string;
    isHnsSelectionSupported?: KnockoutObservableBase<boolean>;
    isHnsCreateOptionSupported?: KnockoutObservableBase<boolean>;
    label?: string;
    location: KnockoutObservableBase<MsPortalFx.Azure.Location>;
    required?: boolean;
    storageAccountData: StorageAccountData;
    subscription: KnockoutObservableBase<MsPortalFx.Azure.Subscription>;
    visible?: boolean | KnockoutObservableBase<boolean>;
    workspaceName: KnockoutObservableBase<string>;
}

export function create(context: Context, options: IStorageAccountDropdownOptions): DropDown.Contract<IAssociatedResource> {
    const { container } = context;
    const {
        allowCreate = true,
        infoBalloonContent,
        isHnsCreateOptionSupported,
        isHnsSelectionSupported,
        label,
        location,
        required = true,
        storageAccountData,
        subscription,
        visible,
        workspaceName
    } = options;

    const associatedResource = ko.observable<IAssociatedResource>();
    const associatedResourceDropdownOptions: IAssociatedResourceDropdownOptions = {
        createNewAriaLabel: ClientResources.StorageAccount.Dropdown.createNewAriaLabel,
        infoBalloonContent: infoBalloonContent || ClientResources.StorageAccount.Dropdown.info,
        label: label || ClientResources.StorageAccount.Dropdown.label,
        onCreateNewClick: allowCreate ? (name: string) => {
            container.openContextPane(
                BladeReferences.forBlade("StorageAccountSettingsBlade").createReference({
                    parameters: {
                        location: location()?.name,
                        name,
                        subscriptionId: subscription().subscriptionId,
                        hnsCreateOptionSupported: isHnsCreateOptionSupported()
                    } as Parameters,
                    onClosed: (_: BladeClosedReason, data: ReturnData) => {
                        if (data) {
                            associatedResource({
                                data: { sku: { name: data.sku }, properties: { isHnsEnabled: data.hnsEnabled } } as IStorageAccountResource,
                                kind: AssociatedResourceKind.new,
                                name: data.name
                            });
                        }
                    }
                })
            );
        } : undefined,
        onGenerateName: allowCreate ? workspaceName => generateName(workspaceName) : undefined,
        onSubscriptionChange: async subscriptionId => {
            if (subscriptionId) {
                return storageAccountData.getStorageAccounts(subscriptionId, false).then((sas: IStorageAccountResource[][]) => {
                    const storageAccounts = sas[0] || [];
                    const premiumStorageEnabled = Flighting.PremiumStorageAccount.isEnabled(subscriptionId);
                    storageAccounts.forEach(sa => {
                        if (sa.sku?.tier === "Premium" && !premiumStorageEnabled) {
                            sa.disabled = true;
                            sa.text = ClientResources.StorageAccount.Dropdown.SettingsBlade.premiumNotSupportedFormat.format(sa.name);
                        } else if (sa.properties?.isHnsEnabled && !isHnsSelectionSupported()) {
                            sa.disabled = true;
                            sa.text = ClientResources.StorageAccount.Dropdown.SettingsBlade.hnsNotSupportedFormat.format(sa.name);
                        }
                    });

                    return storageAccounts;
                });
            } else {
                return Promise.resolve([]);
            }
        },
        resource: associatedResource,
        subscription,
        supportsNone: !required,
        validations: allowCreate ? [
            new MsPortalFx.ViewModels.RequiredValidation(),
            new MsPortalFx.ViewModels.CustomValidation("", (newValue: IAssociatedResource): Q.Promise<MsPortalFx.ViewModels.ValidationResult> => {
                const newStorageAccountName = requiresNameValidation(newValue) ? newValue.name : undefined;
                const subscriptionId = subscription()?.subscriptionId;

                return !newStorageAccountName || !subscriptionId
                    ? Q({ valid: true, message: "" })
                    : storageAccountData.checkStorageAccountAvailability(subscriptionId, newStorageAccountName);
            })
        ] : undefined,
        visible,
        workspaceName
    };

    return resourceDropdownCreate(context, associatedResourceDropdownOptions);
}
