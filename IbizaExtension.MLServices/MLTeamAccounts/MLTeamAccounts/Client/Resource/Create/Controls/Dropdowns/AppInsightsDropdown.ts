import { BladeClosedReason, BladeReferences } from "Fx/Composition";
import * as TemplateBlade from "Fx/Composition/TemplateBlade";
import * as DropDown from "Fx/Controls/DropDown";
import * as ResourceGroupDropDown from "Fx/Controls/ResourceGroupDropDown";
import { LifetimeManager } from "Fx/Lifetime";

import { generateName } from "../../../../Shared/ViewAgnostic/Utilities";
import { Parameters, ReturnData } from "../../../Blades/AssociatedResources/AppInsightsSettingsBlade";
import { AppInsightsData } from "../../../Data/AppInsightsData";
import { CommonContext } from '../../Models/CreateModels';
import {
    AssociatedResourceKind,
    create as resourceDropdownCreate,
    IAssociatedResource,
    IAssociatedResourceDropdownOptions,
    requiresNameValidation
} from "./AssociatedResourceDropdown";

import * as ClientResources from "Resx/ClientResources";

export interface Context extends CommonContext {
    container: TemplateBlade.Container;
    lifetimeManager: LifetimeManager;
}

export interface IAppInsightsDropdownOptions extends Pick<IAssociatedResourceDropdownOptions, "supportsNone" | "supportsNoneDefault"> {
    appInsightsData: AppInsightsData;
    infoBalloonContent?: string | KnockoutObservableBase<string>;
    logWorkspaceName: KnockoutObservableBase<string>;
    resourceGroup: KnockoutObservableBase<ResourceGroupDropDown.Value>;
    subscription: KnockoutObservableBase<MsPortalFx.Azure.Subscription>;
    visible?: boolean | KnockoutObservableBase<boolean>;
    workspaceName: KnockoutObservableBase<string>;
}

export function create(context: Context, options: IAppInsightsDropdownOptions): DropDown.Contract<IAssociatedResource> {
    const { container } = context;
    const { appInsightsData, infoBalloonContent, logWorkspaceName, resourceGroup, subscription, visible, workspaceName, supportsNone, supportsNoneDefault } = options;

    const validations = ko.observableArray([]);
    const supportsNoneObservable = ko.isObservable(supportsNone) ? supportsNone : ko.observable(supportsNone);
    supportsNoneObservable.subscribeAndRun(context.lifetimeManager, (supportsNoneValue) => {
        let newValidations = [];
        if (!supportsNoneValue) {
            newValidations.push(new MsPortalFx.ViewModels.RequiredValidation());
        }
        newValidations.push(new MsPortalFx.ViewModels.CustomValidation(
            "",
            (newValue: IAssociatedResource): Q.Promise<MsPortalFx.ViewModels.ValidationResult> => {
                const newAppInsightsName = requiresNameValidation(newValue) ? newValue.name : undefined;
                const subscriptionId = subscription()?.subscriptionId;
                const resourceGroupExists = resourceGroup()?.mode !== ResourceGroupDropDown.Mode.CreateNew;
                const resourceGroupName = resourceGroup()?.value?.name;

                return !newAppInsightsName || !subscriptionId || !resourceGroupName || !resourceGroupExists
                    ? Q({ valid: true, message: "" })
                    : appInsightsData.checkAppInsightsAvailabilityNew(subscriptionId, resourceGroupName, newAppInsightsName);
            }
        ));
        validations(newValidations);
    });

    const associatedResource = ko.observable<IAssociatedResource>();
    const associatedResourceDropdownOptions: IAssociatedResourceDropdownOptions = {
        createNewAriaLabel: ClientResources.AppInsights.Dropdown.createNewAriaLabel,
        infoBalloonContent: infoBalloonContent || ClientResources.AppInsights.Dropdown.info,
        label: ClientResources.AppInsights.Dropdown.label,
        supportsNone,
        supportsNoneDefault,
        onCreateNewClick: (name: string) => {
            container.openContextPane(
                BladeReferences.forBlade("AppInsightsSettingsBlade").createReference({
                    parameters: {
                        name,
                        resourceGroupName: resourceGroup()?.mode === ResourceGroupDropDown.Mode.UseExisting && resourceGroup().value?.name,
                        subscriptionId: subscription().subscriptionId
                    } as Parameters,
                    onClosed: (_: BladeClosedReason, data: ReturnData) => {
                        if (data) {
                            associatedResource({ kind: AssociatedResourceKind.new, name: data.name });
                        }
                    }
                })
            );
        },
        onGenerateName: workspaceName => {
            logWorkspaceName(generateName(workspaceName));
            return generateName(workspaceName)
        },
        onSubscriptionChange: async subscriptionId => {
            if (subscriptionId) {
                return appInsightsData.getComponents(subscriptionId);
            } else {
                return Promise.resolve([]);
            }
        },
        resource: associatedResource,
        subscription,
        validations,
        visible,
        workspaceName
    };

    return resourceDropdownCreate(context, associatedResourceDropdownOptions);
}
