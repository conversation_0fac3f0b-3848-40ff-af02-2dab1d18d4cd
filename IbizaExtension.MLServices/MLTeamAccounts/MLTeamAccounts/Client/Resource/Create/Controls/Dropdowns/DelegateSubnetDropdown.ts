import { LifetimeManager } from "Fx/Lifetime";
import { Container } from "Fx/Composition/TemplateBlade";
import * as DropDown from "Fx/Controls/DropDown";
import { create as resourceDropdownCreate, IAssociatedResource } from "./AssociatedResourceDropdown";
import { IGenericResource } from "../../../Data/Data.Types";
import { DelegatedSubnetData } from "../../../Data/DelegatedSubnetData";

interface Context {
    lifetimeManager: LifetimeManager;
    container: Container;
}
interface Options {
    subscription?: KnockoutObservableBase<MsPortalFx.Azure.Subscription>;
    workspaceName?: KnockoutObservableBase<string>;
    onSelectionChanged?: (resource: IAssociatedResource) => void;
    visible: KnockoutObservableBase<boolean>;
}

export type Contract = DropDown.Contract<IAssociatedResource>

export const create = (context: Context, options: Options): Contract => {
    const resource = ko.observable<IAssociatedResource>();
    const subnetData = new DelegatedSubnetData();
const dropdown = resourceDropdownCreate(context, {
    resource,
    subscription: options.subscription,
    supportsNone: true,
    workspaceName: options.workspaceName,
    onSubscriptionChange: function (subscriptionId: string): Promise<IGenericResource[]> {
        if (subscriptionId) {
            return subnetData.listDelegateSubnets(subscriptionId);
        } else {
            return Promise.resolve([]);
        }
    },
    visible: options.visible
});
    dropdown.value.subscribe(context.container, (newValue) => {
        if (options.onSelectionChanged) {
            options.onSelectionChanged(newValue);
        }
    });

    return dropdown;
};