import * as DropDown from "Fx/Controls/DropDown";
import { LifetimeManager } from "Fx/Lifetime";
import * as ClientResources from "Resx/ClientResources";
import { ISearchServiceResource } from "../../../../Resource/Data/Data.Types";
import { CognitiveSearchData } from "../../../Data/CognitiveSearchData";
import { CommonContext } from '../../Models/CreateModels';
import { create as resourceDropdownCreate, IAssociatedResource, IAssociatedResourceDropdownOptions } from "./AssociatedResourceDropdown";

export interface Context extends CommonContext {
    lifetimeManager: LifetimeManager;
}

export interface ICosmosDBDropdownOptions {
    searchData: CognitiveSearchData;
    infoBalloonContent?: string;
    label: string;
    subscription: KnockoutObservableBase<MsPortalFx.Azure.Subscription>;
    visible?: boolean | KnockoutObservableBase<boolean>;
    workspaceName: KnockoutObservableBase<string>;
}

export function create(context: Context, options: ICosmosDBDropdownOptions): DropDown.Contract<IAssociatedResource> {
    const {
        infoBalloonContent,
        label,
        searchData,
        subscription,
        visible,
        workspaceName
    } = options;

    const associatedResource = ko.observable<IAssociatedResource>();
    const associatedResourceDropdownOptions: IAssociatedResourceDropdownOptions = {
        infoBalloonContent: infoBalloonContent || ClientResources.StorageAccount.Dropdown.info,
        label,
        onSubscriptionChange: async subscriptionId => {
            if (subscriptionId) {
                return searchData.listSearchServices(subscriptionId).then((searchServices: ISearchServiceResource[]) => {
                    const services = [...searchServices];
                    services.forEach(s => {
                        const skuNotAllowed =
                            MsPortalFx.localeCompareIgnoreCase(s.sku?.name, "basic") === 0 ||
                            MsPortalFx.localeCompareIgnoreCase(s.sku?.name, "free") === 0;

                        if (skuNotAllowed) {
                            s.disabled = true;
                            s.text = ClientResources.SearchService.Dropdown.standardSupportedFormat.format(s.name, s.sku?.name);
                        }
                    });
                    return services;
                });
            } else {
                return Promise.resolve([]);
            }
        },
        resource: associatedResource,
        subscription,
        supportsNone: true,
        visible,
        workspaceName
    };

    return resourceDropdownCreate(context, associatedResourceDropdownOptions);
}
