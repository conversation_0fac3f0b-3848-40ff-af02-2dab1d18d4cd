import * as DropDown from "Fx/Controls/DropDown";
import { LifetimeManager } from "Fx/Lifetime";
import { DocumentDBData } from "../../../Data/DocumentDBData";
import { CommonContext } from '../../Models/CreateModels';
import { create as resourceDropdownCreate, IAssociatedResource, IAssociatedResourceDropdownOptions } from "./AssociatedResourceDropdown";

export interface Context extends CommonContext {
    lifetimeManager: LifetimeManager;
}

export interface ICosmosDBDropdownOptions {
    documentDBData: DocumentDBData;
    infoBalloonContent?: string;
    label: string;
    subscription: KnockoutObservableBase<MsPortalFx.Azure.Subscription>;
    visible?: boolean | KnockoutObservableBase<boolean>;
    workspaceName: KnockoutObservableBase<string>;
}

export function create(context: Context, options: ICosmosDBDropdownOptions): DropDown.Contract<IAssociatedResource> {
    const {
        documentDBData,
        infoBalloonContent,
        label,
        subscription,
        visible,
        workspaceName
    } = options;

    const associatedResource = ko.observable<IAssociatedResource>();
    const associatedResourceDropdownOptions: IAssociatedResourceDropdownOptions = {
        infoBalloonContent,
        label,
        onSubscriptionChange: async subscriptionId => {
            if (subscriptionId) {
                return documentDBData.listDatabaseAccounts(subscriptionId);
            } else {
                return Promise.resolve([]);
            }
        },
        resource: associatedResource,
        subscription,
        supportsNone: true,
        visible,
        workspaceName
    };

    return resourceDropdownCreate(context, associatedResourceDropdownOptions);
}
