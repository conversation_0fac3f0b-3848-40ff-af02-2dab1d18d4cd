import { BladeClosedReason, BladeReferences } from "Fx/Composition";
import * as TemplateBlade from "Fx/Composition/TemplateBlade";
import * as DropDown from "Fx/Controls/DropDown";
import { LifetimeManager } from "Fx/Lifetime";

import { IContainerRegistryResource } from "../../../../Resource/Data/Data.Types";
import { Parameters, ReturnData } from "../../../Blades/AssociatedResources/ContainerRegistrySettingsBlade";
import { ContainerRegistryData } from "../../../Data/ContainerRegistryData";
import { CommonContext } from '../../Models/CreateModels';
import {
    AssociatedResourceKind,
    create as resourceDropdownCreate,
    IAssociatedResource,
    IAssociatedResourceDropdownOptions,
    requiresNameValidation
} from "./AssociatedResourceDropdown";

import * as ClientResources from "Resx/ClientResources";

export interface Context extends CommonContext {
    container: TemplateBlade.Container;
    lifetimeManager: LifetimeManager;
}

export interface IContainerRegistryDropdownOptions {
    containerRegistryData: ContainerRegistryData;
    subscription: KnockoutObservableBase<MsPortalFx.Azure.Subscription>;
    visible?: boolean | KnockoutObservableBase<boolean>;
    workspaceName: KnockoutObservableBase<string>;
}

export function create(context: Context, options: IContainerRegistryDropdownOptions): DropDown.Contract<IAssociatedResource> {
    const { container } = context;
    const { containerRegistryData, subscription, visible, workspaceName } = options;

    const associatedResource = ko.observable<IAssociatedResource>();
    const associatedResourceDropdownOptions: IAssociatedResourceDropdownOptions = {
        createNewAriaLabel: ClientResources.ContainerRegistry.Dropdown.createNewAriaLabel,
        infoBalloonContent: ClientResources.ContainerRegistry.Dropdown.info,
        label: ClientResources.ContainerRegistry.Dropdown.label,
        onCreateNewClick: (name: string) => {
            container.openContextPane(
                BladeReferences.forBlade("ContainerRegistrySettingsBlade").createReference({
                    parameters: { name, subscriptionId: subscription().subscriptionId } as Parameters,
                    onClosed: (_: BladeClosedReason, data: ReturnData) => {
                        if (data) {
                            associatedResource({
                                data: { sku: { name: data.sku } } as IContainerRegistryResource,
                                kind: AssociatedResourceKind.new,
                                name: data.name
                            });
                        }
                    }
                })
            );
        },
        onSubscriptionChange: async subscriptionId => {
            if (subscriptionId) {
                return containerRegistryData.getContainerRegistries(subscriptionId);
            } else {
                return Promise.resolve([]);
            }
        },
        resource: associatedResource,
        subscription,
        supportsNone: true,
        validations: [
            new MsPortalFx.ViewModels.CustomValidation(
                "",
                (newValue: IAssociatedResource): Q.Promise<MsPortalFx.ViewModels.ValidationResult> => {
                    const newContainerRegistryName = requiresNameValidation(newValue) ? newValue.name : undefined;
                    const subscriptionId = subscription()?.subscriptionId;

                    return !newContainerRegistryName || !subscriptionId
                        ? Q({ valid: true, message: "" })
                        : containerRegistryData.checkContainerRegistryAvailability(subscriptionId, newContainerRegistryName);
                }
            )
        ],
        visible,
        workspaceName
    };

    return resourceDropdownCreate(context, associatedResourceDropdownOptions);
}
