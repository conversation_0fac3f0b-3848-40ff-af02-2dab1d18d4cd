import { BladeClosedReason, BladeReferences } from "Fx/Composition";
import * as TemplateBlade from "Fx/Composition/TemplateBlade";
import * as DropDown from "Fx/Controls/DropDown";
import { LifetimeManager } from "Fx/Lifetime";
import { KeyVaultData } from "../../../../Resource/Data/KeyVaultData";
import { generateName } from "../../../../Shared/ViewAgnostic/Utilities";
import { Parameters, ReturnData } from "../../../Blades/AssociatedResources/KeyvaultSettingsBlade";
import { CommonContext } from '../../Models/CreateModels';
import {
    AssociatedResourceKind,
    create as resourceDropdownCreate,
    IAssociatedResource,
    IAssociatedResourceDropdownOptions,
    requiresNameValidation
} from "./AssociatedResourceDropdown";

import * as ClientResources from "Resx/ClientResources";
import { ManagedKeyVaultType } from "Resource/Create/ViewModels/CreateMachineLearningModels";

const keyvaultPrefixWhenStartingWithDigit = "a";

export interface Context extends CommonContext {
    container: TemplateBlade.Container;
    lifetimeManager: LifetimeManager;
}

export interface IKeyvaultDropdownOptions {
    location: KnockoutObservableBase<MsPortalFx.Azure.Location>;
    infoBalloonContent?: string | KnockoutObservableBase<string>;
    keyvaultData: KeyVaultData;
    subscription: KnockoutObservableBase<MsPortalFx.Azure.Subscription>;
    visible?: boolean | KnockoutObservableBase<boolean>;
    workspaceName: KnockoutObservableBase<string>;
    disabled?: KnockoutObservableBase<boolean>;
    isNested?: boolean | KnockoutObservableBase<boolean>;
    managedKeyVaultType?: KnockoutObservableBase<ManagedKeyVaultType>;
}

export function create(context: Context, options: IKeyvaultDropdownOptions): DropDown.Contract<IAssociatedResource> {
    const { container, lifetimeManager } = context;
    const { keyvaultData, location, subscription, visible, workspaceName, infoBalloonContent, disabled, isNested, managedKeyVaultType } = options;

    const associatedResource = ko.observable<IAssociatedResource>();
    const associatedResourceDropdownOptions: IAssociatedResourceDropdownOptions = {
        createNewAriaLabel: ClientResources.Keyvault.Dropdown.createNewAriaLabel,
        infoBalloonContent: infoBalloonContent || ClientResources.Keyvault.Dropdown.info,
        label: ClientResources.Keyvault.Dropdown.label,
        onCreateNewClick: (name: string) => {
            container.openContextPane(
                BladeReferences.forBlade("KeyvaultSettingsBlade").createReference({
                    parameters: { location: location()?.name, name, subscriptionId: subscription().subscriptionId } as Parameters,
                    onClosed: (_: BladeClosedReason, data: ReturnData) => {
                        if (data) {
                            associatedResource({ kind: AssociatedResourceKind.new, name: data.name });
                        }
                    }
                })
            );
        },
        onGenerateName: workspaceName => {
            // Key vault cannot start with digit so in this case we will add a prefix
            const keyvaultPrefix = /^\d.*/.test(workspaceName.toLocaleLowerCase()) ? keyvaultPrefixWhenStartingWithDigit : "";
            return generateName(keyvaultPrefix + workspaceName);
        },
        onSubscriptionChange: async subscriptionId => {
            if (subscriptionId) {
                return keyvaultData.getVaults(subscriptionId);
            } else {
                return Promise.resolve([]);
            }
        },
        resource: associatedResource,
        subscription,
        validations: [
            new MsPortalFx.ViewModels.RequiredValidation(),
            new MsPortalFx.ViewModels.CustomValidation(
                "",
                (newValue: IAssociatedResource): Q.Promise<MsPortalFx.ViewModels.ValidationResult> => {
                    const newKeyVaultName = requiresNameValidation(newValue) ? newValue.name : undefined;
                    const subscriptionId = subscription()?.subscriptionId;

                    return !newKeyVaultName || !subscriptionId
                        ? Q({ valid: true, message: "" })
                        : keyvaultData.checkKeyVaultAvailabilityNew(subscriptionId, newKeyVaultName);
                }
            )
        ],
        visible,
        workspaceName,
        disabled,
        isNested
    };

    if (managedKeyVaultType) {
        managedKeyVaultType.subscribe(lifetimeManager, newManagedKeyVaultType => {
            if (newManagedKeyVaultType === "managedKeyVault") {
                associatedResource({ kind: AssociatedResourceKind.none, name: null });
            }
        });
    }

    return resourceDropdownCreate(context, associatedResourceDropdownOptions);
}
