import { BladeClosedReason, BladeReferences } from "Fx/Composition";
import * as DropDown from "Fx/Controls/DropDown";
import { LifetimeManager } from "Fx/Lifetime";
import * as ClientResources from "Resx/ClientResources";
import { AIServicesData } from "../../../Data/AIServicesData";
import { CommonContext } from '../../Models/CreateModels';
import { generateName } from "../../../../Shared/ViewAgnostic/Utilities";
import * as ResourceGroupDropDown from "Fx/Controls/ResourceGroupDropDown";
import { Parameters, ReturnData } from "../../../Blades/AssociatedResources/AIServicesSettingsBlade";
import { create as resourceDropdownCreate, IAssociatedResource, IAssociatedResourceDropdownOptions, AssociatedResourceKind, requiresNameValidation } from "./AssociatedResourceDropdown";

export interface Context extends CommonContext {
    lifetimeManager: LifetimeManager;
}

export interface IAIServicesDropdownOptions {
    AIServicesData: AIServicesData;
    infoBalloonContent?: string;
    subscription: KnockoutObservableBase<MsPortalFx.Azure.Subscription>;
    visible?: boolean | KnockoutObservableBase<boolean>;
    resourceGroup: KnockoutObservableBase<ResourceGroupDropDown.Value>;
    workspaceName: KnockoutObservableBase<string>;
}

export function create(context: Context, options: IAIServicesDropdownOptions): DropDown.Contract<IAssociatedResource> {
    const { container } = context;
    const {
        infoBalloonContent,
        AIServicesData,
        subscription,
        visible,
        resourceGroup,
        workspaceName
    } = options;

    const associatedResource = ko.observable<IAssociatedResource>();
    const associatedResourceDropdownOptions: IAssociatedResourceDropdownOptions = {
        createNewAriaLabel: ClientResources.CreateBladeHubAIServices.ariaLabel,
        infoBalloonContent: infoBalloonContent || ClientResources.CreateBladeHubAIServices.info,
        label: ClientResources.CreateBladeHubAIServices.label,
        onCreateNewClick: (name: string) => {
            container.openContextPane(
                BladeReferences.forBlade("AIServicesSettingsBlade").createReference({
                    parameters: {
                        name,
                        resourceGroupName: resourceGroup()?.mode === ResourceGroupDropDown.Mode.UseExisting && resourceGroup().value?.name,
                        subscriptionId: subscription().subscriptionId
                    } as Parameters,
                    onClosed: (_: BladeClosedReason, data: ReturnData) => {
                        if (data) {
                            associatedResource({ kind: AssociatedResourceKind.new, name: data.name });
                        }
                    }
                })
            );
        },
        onGenerateName: workspaceName => {
            return generateName(workspaceName);
        },
        onSubscriptionChange: async subscriptionId => {
            if (subscriptionId) {
                return AIServicesData.listAIServices(subscriptionId)
            } else {
                return Promise.resolve([]);
            }
        },
        resource: associatedResource,
        subscription,
        validations: [
            new MsPortalFx.ViewModels.RequiredValidation(),
            new MsPortalFx.ViewModels.CustomValidation(
                "",
                (newValue: IAssociatedResource): Q.Promise<MsPortalFx.ViewModels.ValidationResult> => {
                    const newAIServicesName = requiresNameValidation(newValue) ? newValue.name : undefined;
                    const subscriptionId = subscription()?.subscriptionId;
                    const resourceGroupName = resourceGroup()?.value?.name;

                    return !newAIServicesName || resourceGroup().mode === ResourceGroupDropDown.Mode.CreateNew || !resourceGroupName || !subscriptionId
                        ? Q({ valid: true, message: "" })
                        : AIServicesData.checkAIServicesAvailabilityNew(subscriptionId, resourceGroupName, newAIServicesName);
                }
            )
        ],
        visible,
        nameMetadata: "location",
        groupBy: "kind",
        noneText: ClientResources.CreateBladeHubAIServices.skipText,
        workspaceName
    };

    return resourceDropdownCreate(context, associatedResourceDropdownOptions);
}
