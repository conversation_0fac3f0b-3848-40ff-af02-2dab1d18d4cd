import { BladeClosedReason, BladeReferences } from "Fx/Composition";
import * as TemplateBlade from "Fx/Composition/TemplateBlade";
import * as DropDown from "Fx/Controls/DropDown";
import { LifetimeManager } from "Fx/Lifetime";

import { CommonContext } from '../../Models/CreateModels';
import {
    create as resourceDropdownCreate,
    IAssociatedResource,
    IAssociatedResourceDropdownOptions,
} from "./AssociatedResourceDropdown";

import * as ClientResources from "Resx/ClientResources";
import { MachineLearningServicesData } from "Resource/Data/MachineLearningServicesData";

export interface Context extends CommonContext {
    container: TemplateBlade.Container;
    lifetimeManager: LifetimeManager;
}

export interface IHubDropdownOptions {
    MachineLearningServicesData: MachineLearningServicesData;
    subscription: KnockoutObservableBase<MsPortalFx.Azure.Subscription>;
    workspaceName: KnockoutObservableBase<string>;
    required?: boolean;
    visible?: boolean | KnockoutObservableBase<boolean>;
    disabled?: KnockoutObservableBase<boolean>;
    initialValue?: IAssociatedResource;
}

export function create(context: Context, options: IHubDropdownOptions): DropDown.Contract<IAssociatedResource> {
    const { container } = context;
    const {
        MachineLearningServicesData,
        workspaceName,
        required = true,
        subscription,
        visible,
        disabled,
        initialValue
    } = options;

    const associatedResource = ko.observable<IAssociatedResource>();
    const associatedResourceDropdownOptions: IAssociatedResourceDropdownOptions = {
        createNewAriaLabel: ClientResources.AssetType.AIStudio.Hub.Dropdown.createNewAriaLabel,
        infoBalloonContent: ClientResources.AssetType.AIStudio.Hub.Dropdown.info,
        label: ClientResources.AssetType.AIStudio.Hub.Dropdown.label,
        onCreateNewClick: () => {
            container.openBlade(
                BladeReferences.forCreateAssetType().createReference({
                    assetType: "AIStudioCreate",
                    parameters: {
                        kind: "Hub",
                    }
                })
            )
        },
        onSubscriptionChange: async subscriptionId => {
            if (subscriptionId) {
                return await MachineLearningServicesData.getHubs();
            } else {
                return Promise.resolve([]);
            }
        },
        nameMetadata: "location",
        resource: associatedResource,
        subscription,
        supportsNone: !required,
        validations: [new MsPortalFx.ViewModels.RequiredValidation()],
        visible,
        disabled,
        workspaceName,
        groupBy: "resourceGroupWithSubscription",
        initialValue
    };

    return resourceDropdownCreate(context, associatedResourceDropdownOptions);
}