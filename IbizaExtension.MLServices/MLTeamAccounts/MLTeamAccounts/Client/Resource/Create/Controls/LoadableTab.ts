import { Container, ShieldType } from "Fx/Composition/TemplateBlade";
import { Tab } from "../Models/ControlModels";
import { CommonContext } from "../Models/CreateModels";
import * as CreateSummaryTab from "../Tabs/Create/CreateSummaryTab";
import * as CreateTagsTab from "../Tabs/Create/CreateTagsTab";
import * as NoPdlUiConstants from "../Utilities/NoPdlUiConstants";
import { SummaryTab } from "../Utilities/NoPdlUiConstants";
import * as CreateHelpers from "../Utilities/NoPdlUiHelpers";

import Resources = require("Resx/ClientResources");
import { LifetimeManager } from "Fx/Lifetime";

const log = MsPortalFx.Base.Diagnostics.createLog(require);
const enum ErrorCodes {
    InitializeTabContentFailed = 1
}

export interface Context extends CommonContext {
    lifetimeManager: LifetimeManager;
    container: Container;
}

export interface Options {
    context: Context;
    id: string;
    name: string;
    initializeTabContent: () => Promise<TabContent>;
    sectionCssClass?: string;
}

export interface Contract extends Tab {
    load: () => Promise<TabContent>;
}

export interface TabContent {
    triggerValidation?: () => Q.Promise<boolean>;
    onLoad?: () => void | Q.Promise<void>;
    controls: any[] | KnockoutObservableArray<any> | KnockoutReadOnlyObservableArray<any>;
}

export function create(options: Options): Contract {
    const { context, id, name, initializeTabContent } = options;
    const section = CreateHelpers.createTab(context, name, [], options.sectionCssClass);
    let promise: Promise<TabContent>;
    const loadTabContent = () => {
        if (!promise) {
            promise = initializeTabContent()
                .then(tabContent => {
                    if (ko.isObservable(tabContent.controls)) {
                        (tabContent.controls as KnockoutObservableArray<any>).subscribeAndRun(context.lifetimeManager, section.children);
                    } else {
                        section.children(tabContent.controls as any[]);
                    }

                    const formSection = section as any;
                    if (formSection.tabHasBeenLoaded && !formSection.tabHasBeenLoaded()) {
                        formSection.tabSection(formSection);
                        formSection.tabHasBeenLoaded(true);
                    }

                    return tabContent;
                })
                .catch(reason => {
                    log.error(reason, ErrorCodes.InitializeTabContentFailed);
                    promise = null;
                    throw reason;
                });
        }

        return promise;
    };

    return {
        id,
        name,
        section,
        load: loadTabContent,
        triggerValidation: () => {
            return loadTabContent().then(tabContent => {
                const tabContentValidation = tabContent.triggerValidation ? tabContent.triggerValidation() : Q(true);
                return tabContentValidation.then(isValid => {
                    if (isValid) {
                        return CreateHelpers.validateSection(context.lifetimeManager, section);
                    }

                    return isValid;
                });
            });
        },
        onLoad: () => {
            const promise = loadTabContent().then(tabContent => {
                return tabContent.onLoad && tabContent.onLoad();
            });

            context.container.operations.add(promise, {
                blockUi: true,
                shieldType: ShieldType.Translucent
            });

            return promise;
        }
    };
}

export function createTagsTab(options: CreateTagsTab.Options): Contract {
    return create({
        context: options.context,
        id: NoPdlUiConstants.TagsTab.tabId,
        name: Resources.createTagsTabTitle,
        initializeTabContent: () => {
            return MsPortalFx.require<typeof CreateTagsTab>("../Tabs/Create/CreateTagsTab", require).then(amdModule => {
                return amdModule.create(options);
            });
        }
    });
}

export function createSummaryTab<TModule extends { create: (context: TContext) => CreateSummaryTab.Contract }, TContext extends Context>(
    moduleId: string,
    localRequire: LocalRequire,
    context: TContext
): Contract {
    return create({
        context,
        id: SummaryTab.tabId,
        name: Resources.summaryTabTitle,
        initializeTabContent: () => {
            return MsPortalFx.require<TModule>(moduleId, localRequire).then(module => {
                return module.create(context);
            });
        }
    });
}
