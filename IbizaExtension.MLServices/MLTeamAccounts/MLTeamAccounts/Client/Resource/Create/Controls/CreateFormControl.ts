/**
 * Copyright (c) Microsoft Corporation.  All rights reserved.
 */

import * as BladeBase from "Fx/Composition/BladeBase";
import { Container } from "Fx/Composition/TemplateBlade";
import * as Button from "Fx/Controls/Button";
import * as ResourceGroupDropDown from "Fx/Controls/ResourceGroupDropDown";
import * as TabControl from "Fx/Controls/TabControl";
import * as Provisioning from "Fx/ResourceManagement/Provisioning";
import * as Resources from "Resx/ClientResources";

import * as LoadableTabControl from "../Controls/LoadableTabControl";
import { Tab } from "../Models/ControlModels";
import { CommonContext } from "../Models/CreateModels";
import { FormControl } from "../Utilities/NoPdlUiConstants";
import * as NoPdlUiHelpers from "../Utilities/NoPdlUiHelpers";
import { LifetimeManager } from "Fx/Lifetime";

const log = MsPortalFx.Base.Diagnostics.createLog(require);
const enum ErrorCodes {
    ValidateFrom = 1
}

export interface TelemetryLoggingOptions {
    /**
     * Corresponds to the names of parameters in your template that should be logged in telemetry.
     */
    parametersToLog: string[];
    /**
     * Corresponds to custom information you want to log in telemetry, in key value pairs.
     */
    optionsToLog: StringMap<any>;
}

export interface TemplateGenerator {
    /**
     * Define a function that will return DeployTemplateOptions.
     */
    getTemplateDeploymentOptions: () => Q.Promise<Provisioning.DeployTemplateOptions>;
    /**
     * Given deployment options, return TelemetryLoggingOptions, which will enable consistent telemetry for your experience.
     */
    getTelemetryLoggingOptions: (deploymentOptions: Provisioning.DeployTemplateOptions) => TelemetryLoggingOptions;
}

export interface Context extends CommonContext {
    container: Container;
    lifetimeManager: LifetimeManager;
    form: BladeBase.FormManagement;
    providerAndResourceName: string;
    provisioning: BladeBase.DoesProvisioning.Provisioning;
}

export interface CreateButtonText {
    /**
     * Text for create button when there is no next tab
     */
    noNextTab: string;
    /**
     * Text for create button when there is a next tab
     */
    nextTabExists: string;
}

export interface Options {
    context: Context;
    /**
     * Used in logging. Typically the name of the class which is utilizing the CreateFormControl.
     */
    deploymentSourceName: string;
    /**
     * Create the tabs that will be present in the experience. Usage of CreateBasicsTab & similar helpers are optional.
     */
    initializeTabs: () => Tab[] | KnockoutObservableArray<Tab>;
    /**
     * Define a function that will return TemplateGenerator.
     */
    getTemplateGenerator?: () => Q.Promise<TemplateGenerator>;

    /**
     * Define a function that will return a pre-deployment task. Deployment will not start
     * until this task gets finished successfully.
     */
    getPreDeploymentTask?: () => Q.Promise<any>;

    /**
     * Set isWizard to true for blocking behavior of the tabs. default is false.
     * When wizard mode is enabled, clicking next the tab would actually validate itself and if found invalid would not navigate to next blade.
     */
    isWizard?: boolean;
    /**
     * Define custom text for create button.
     */
    customCreateButtonText?: CreateButtonText;
    /**
     * Define callback function for creating a resource
     */
    createCallBack?: () => void;
    /**
     * Define a function for validating and kicking-off a deployment independent of the 'create' button
     */
    deployTemplate?: () => void;
    /**
     * Define a function for post deployment callback
     */
    postDeploymentCallback?: () => void;
}

export interface Contract {
    tabControl: TabControl.Contract;
    tabs: Tab[] | KnockoutObservableArray<Tab>;
    createButton: Button.Contract;
    prevButton: Button.Contract;
    nextButton: Button.Contract;
    openAutomationBlade: () => void;
    automationLink: string;
    showAutomationLink: KnockoutObservableBase<boolean>;
    deployTemplate: () => void;
}

interface TabOpenTelemetry {
    telemetryId: string;
    totalTabCount: number;
    tabPattern: string[];
    details: TabOpenTelemetryItem[];
    providerAndResourceName: string;
    originalTelemetryId: string;
    currentTab?: string;
}

interface TabOpenTelemetryItem {
    tabIndex: number;
    visitOrder: number;
    name: string;
    tabOpenedTime: Date;
    timeOnTab?: number;
}

/**
 * This function will create common controls used in the no-pdl create pattern.
 * The resulting Contract is compatible with the CreateBasicsTab, CreateTagsTab, and
 * CreateSummaryTab, but they are not required, you may define your own!
 *
 * Note: A blade which calls this method must define 'ext-nopdlcreate-section' within the
 * TemplateBlade's CSS styleSheet.
 */
export function create(options: Options): Contract {
    return new CreateFormControl(options);
}

class CreateFormControl implements Contract {
    private _options: Options;
    private _createButtonText: CreateButtonText;
    private _isValidating = ko.observable(false);
    private _isDeploying = ko.observable(false);
    public tabControl: TabControl.Contract;
    public tabs: Tab[] | KnockoutObservableArray<Tab>;
    public createButton: Button.Contract;
    public prevButton: Button.Contract;
    public nextButton: Button.Contract;
    public automationLink = Resources.automationLink;
    public openAutomationBlade: () => void;
    public showAutomationLink = ko.observable(false);
    public deployTemplate: () => void;

    public constructor(options: Options) {
        this._options = options;
        this._initializeTabControl();
        this._initializeButtons();
        this._initializeAutomationBlade();
        this._initializeDeployTemplate();

        if (options.customCreateButtonText) {
            this._createButtonText = {
                noNextTab: options.customCreateButtonText.noNextTab,
                nextTabExists: options.customCreateButtonText.nextTabExists
            };
        } else {
            this._createButtonText = {
                noNextTab: Resources.buttonCreate,
                nextTabExists: Resources.buttonReviewCreate
            };
        }
        options.context.validateForm = () => this._validateForm();
    }

    private _initializeAutomationBlade(): void {
        const { context, getTemplateGenerator } = this._options;
        const { container, provisioning } = context;

        this.openAutomationBlade = () => {
            this._validateForm().then(isValid => {
                if (isValid) {
                    getTemplateGenerator()
                        .then(templateGenerator => {
                            return templateGenerator.getTemplateDeploymentOptions();
                        })
                        .then(deploymentOptions => {
                            return container.openBlade(provisioning.getAutomationBladeReference(deploymentOptions));
                        });
                }
            });
        };
    }

    private _initializeTabControl(): void {
        const { lifetimeManager, container } = this._options.context;
        this.tabs = this._options.initializeTabs();
        this.tabControl = LoadableTabControl.create(lifetimeManager, {
            tabs: this.tabs,
            container,
        });
    }

    private _initializeButtons(): void {
        const { container, lifetimeManager, provisioning, providerAndResourceName, originalTelemetryId } = this._options.context;

        let prevTab: Tab;
        let nextTab: Tab;
        const disabledCreate = ko.observable(false);
        this.createButton = Button.create(lifetimeManager, {
            text: "",
            disabled: ko.pureComputed({
                read: () => {
                    const statusBar = container.statusBar();
                    const isFinalStep = this.showAutomationLink();
                    const finalStepHasError =
                        (statusBar && statusBar.state === MsPortalFx.ViewModels.ContentState.Error) ||
                        (this._options.context.disableCreateButton && this._options.context.disableCreateButton());
                    return this._isValidating() || this._isDeploying() || (isFinalStep && finalStepHasError) || disabledCreate() || false;
                },
                write: value => {
                    disabledCreate(value);
                }
            }),
            onClick: () => {
                container.closeContextBlade();
                const lastTab = MsPortalFx.last(ko.unwrap(this.tabs)).section;
                if (nextTab) {
                    this.tabControl.activeTab(lastTab);
                } else {
                    this._deploy(true);
                }
            }
        });

        this.prevButton = Button.create(lifetimeManager, {
            text: Resources.buttonPrevious,
            style: Button.Style.Secondary,
            onClick: () => {
                container.closeContextBlade();
                this.tabControl.activeTab(prevTab.section, true);
                container.setFocus(".ext-nopdlcreate-section").finally(() => {
                    container.setFocus(".ext-focus-container");
                });
            }
        });

        this.nextButton = Button.create(lifetimeManager, {
            text: "",
            style: Button.Style.Secondary,
            onClick: () => {
                container.closeContextBlade();
                if (this._options.isWizard) {
                    this._validateCurrentTab().then(result => {
                        if (result) {
                            nextTab.section.disabled(false);
                            this.tabControl.activeTab(nextTab.section, true);
                            container.setFocus(".ext-nopdlcreate-section").finally(() => {
                                container.setFocus(".ext-focus-container");
                            });
                        }
                    });
                } else {
                    this.tabControl.activeTab(nextTab.section);
                    container.setFocus(".ext-nopdlcreate-section").finally(() => {
                        container.setFocus(".ext-focus-container");
                    });
                }
            }
        });

        const tabOpenTelemetry: TabOpenTelemetry = {
            telemetryId: provisioning.telemetryId,
            totalTabCount: this.tabs.length,
            providerAndResourceName: providerAndResourceName,
            tabPattern: [],
            details: [],
            originalTelemetryId: originalTelemetryId || null
        };

        this.tabControl.activeTab.subscribeAndRun(lifetimeManager, activeTab => {
            if (!activeTab) {
                return;
            }

            container.closeChildBlade();
            container.closeContextBlade();

            const tabs = ko.unwrap(this.tabs);

            const visibleTabs = tabs.filter(item => ko.unwrap(item.section.visible));
            const visibleTabIndex = visibleTabs.map(item => item.section).indexOf(activeTab as any);

            prevTab = visibleTabIndex > 0 && visibleTabs[visibleTabIndex - 1];
            nextTab = visibleTabIndex < visibleTabs.length - 1 && visibleTabs[visibleTabIndex + 1];
            this._options.isWizard && nextTab && this._disableNextTabs(tabs, visibleTabIndex);

            this.createButton.text(nextTab ? this._createButtonText.nextTabExists : this._createButtonText.noNextTab);
            this.prevButton.disabled(!prevTab);
            this.nextButton.text(nextTab ? Resources.buttonNext.format(nextTab.name) : Resources.buttonNextPlaceholder);
            this.nextButton.disabled(!nextTab);
            this.showAutomationLink(!nextTab);

            // Tab open telemetry
            const tabIndex = tabs.map(item => item.section).indexOf(activeTab as any);
            const previousTab = MsPortalFx.last(tabOpenTelemetry.details);
            tabOpenTelemetry.currentTab = tabs[tabIndex].id;
            const currentTime = new Date();
            tabOpenTelemetry.details.push({
                tabIndex,
                visitOrder: tabOpenTelemetry.details.length,
                name: tabOpenTelemetry.currentTab,
                tabOpenedTime: currentTime
            });

            tabOpenTelemetry.tabPattern.push(tabs[tabIndex].id);

            if (previousTab) {
                previousTab.timeOnTab = currentTime.getTime() - previousTab.tabOpenedTime.getTime();
            }

            MsPortalFx.Base.Diagnostics.Telemetry.trace({
                action: "TabOpened",
                source: this._options.deploymentSourceName,
                data: tabOpenTelemetry
            });
        });
    }

    private _disableNextTabs(tabs: Tab[], currentTabIndex: number) {
        const targetIndex = currentTabIndex + 1;
        if (tabs && tabs.length > targetIndex) {
            for (let i = targetIndex; i < tabs.length; i++) {
                tabs[i].section.disabled(true);
            }
        }
    }

    private _validateCurrentTab(): Promise<boolean> {
        const currentTab = ko.unwrap(this.tabs)[this.tabControl.activeTabIndex()];
        return currentTab.triggerValidation().catch(reason => {
            log.error(reason, ErrorCodes.ValidateFrom);
            return false;
        });
    }

    private _validateForm(): Q.Promise<boolean> {
        const { container, lifetimeManager } = this._options.context;

        this._isValidating(true);
        this.tabControl.showValidationStatusOnTabs(false);
        container.statusBar(null);
        const tabsToValidate = ko.unwrap(this.tabs).slice(0, -1).filter(item => ko.unwrap(item.section.visible));
        const promise = Q.all(
            tabsToValidate.map(item => {
                return item.triggerValidation ? item.triggerValidation() : NoPdlUiHelpers.validateSection(lifetimeManager, item.section);
            })
        )
            .then(results => {
                const isValid = results.every(MsPortalFx.identity);
                if (!isValid) {
                    let validationMessage = Resources.tabValidationErrors;

                    // Portal bug #4355345: Framework only shows red circle next to tab if the validation failed due to required fields.
                    const checkValidManually = MsPortalFx.find(tabsToValidate, tab => tab.showNameIfInvalid);
                    if (checkValidManually) {
                        const failedTabs = results.map((result, idx) => (!result ? tabsToValidate[idx].name : "")).filter(tab => !!tab);

                        validationMessage =
                            failedTabs.length > 1
                                ? Resources.multipleInvalidTabErrorMessage.format(failedTabs.join())
                                : Resources.singleInvalidTabErrorMessage.format(failedTabs.join());
                    }

                    container.statusBar({
                        state: MsPortalFx.ViewModels.ContentState.Error,
                        text: validationMessage
                    });
                }

                return isValid;
            })
            .catch(reason => {
                log.error(reason, ErrorCodes.ValidateFrom);
                return false;
            })
            .finally(() => {
                this.tabControl.showValidationStatusOnTabs(true);
                this._isValidating(false);
            });

        container.operations.add(promise, {
            blockUi: true,
            shieldType: MsPortalFx.ViewModels.ShieldType.Translucent
        });

        return promise;
    }

    private _initializeDeployTemplate(): void {
        this.deployTemplate = () => this._deploy(false);
    }

    // enableCreateCallback: false: starts deployment. true: calls createCallback if configured.
    private _deploy(enableCreateCallback: boolean): void {
        const { context } = this._options;
        const { container } = context;

        this._isDeploying(true);
        const promise = this._validateForm()
            .then(isValid => {
                if (isValid) {
                    if (this._options.context.deploymentSubmitted) {
                        this._options.context.deploymentSubmitted(true);
                    }

                    if (enableCreateCallback && this._options.createCallBack) {
                        return this._options.createCallBack();
                    }

                    return this._deployTemplate().then(
                        () => {
                            this._options.postDeploymentCallback && this._options.postDeploymentCallback();
                            container.closeCurrentBlade();
                        },
                        error => {
                            container.statusBar({
                                state: MsPortalFx.ViewModels.ContentState.Error,
                                text: error.message
                            });
                        }
                    );
                }

                return Q();
            })
            .finally(() => {
                this._isDeploying(false);
            });

        container.operations.add(promise, {
            blockUi: true,
            shieldType: MsPortalFx.ViewModels.ShieldType.Translucent
        });
    }

    private _deployTemplate(): Q.Promise<Provisioning.DeployTemplateResults<Provisioning.DeployTemplateOptions>> {
        const { getPreDeploymentTask, getTemplateGenerator } = this._options;
        const predeploymentTasks = getPreDeploymentTask ? getPreDeploymentTask() : Q.resolve({});

        return predeploymentTasks.then(() => {
            return getTemplateGenerator().then(templateGenerator => {
                return templateGenerator.getTemplateDeploymentOptions().then(deploymentOptions => {
                    const { provisioning, dataModel, providerAndResourceName, originalTelemetryId } = this._options.context;
                    const { parametersToLog, optionsToLog } = templateGenerator.getTelemetryLoggingOptions(deploymentOptions);
                    const { basics } = dataModel;
                    const resourceGroup = basics.resourceGroup();

                    const parameterValues: StringMap<any> = {};
                    Object.keys(deploymentOptions.parameters)
                        .filter(item => parametersToLog.indexOf(item) !== -1)
                        .forEach(item => (parameterValues[item] = deploymentOptions.parameters[item]));

                    MsPortalFx.Base.Diagnostics.Telemetry.trace({
                        action: FormControl.deploymentSubmitted,
                        source: this._options.deploymentSourceName,
                        data: {
                            telemetryId: provisioning.telemetryId,
                            originalTelemetryId: originalTelemetryId || null,
                            providerAndResourceName: providerAndResourceName,
                            subscriptionId: deploymentOptions.subscriptionId,
                            deploymentName: deploymentOptions.deploymentName,
                            resourceGroupName: deploymentOptions.resourceGroupName,
                            resourceGroupLocation: deploymentOptions.resourceGroupLocation,
                            resourceGroupIsNew: resourceGroup && resourceGroup.mode === ResourceGroupDropDown.Mode.CreateNew,
                            parameters: parameterValues,
                            options: optionsToLog
                        }
                    });

                    // TODO: Workaround Fx bug that requires a gallery item
                    // https://stackoverflow.microsoft.com/questions/106151/no-pdl-create-without-gallery-item-wont-start-provisioning
                    if (!provisioning.referenceType?.marketplaceId) {
                        // Notification never completes unless deploymentMode is DeployAndAwaitCompletion
                        // However, we want to close the blade immediately instead of waiting on the entire deployment
                        deploymentOptions.deploymentMode = Provisioning.TemplateDeploymentMode.DeployAndAwaitCompletion;
                        provisioning.deployTemplate(deploymentOptions);
                        return Q.resolve<Provisioning.DeployTemplateResults<Provisioning.DeployTemplateOptions>>(null);
                    }

                    return provisioning.deployTemplate(deploymentOptions);
                });
            });
        });
    }
}
