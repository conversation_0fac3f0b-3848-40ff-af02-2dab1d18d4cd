import { Container } from 'Fx/Composition/TemplateBlade';
import { LifetimeManager } from "Fx/Lifetime";
import * as Resources from "Resx/ClientResources";
import * as ResourceGroupDropDown from 'Fx/Controls/ResourceGroupDropDown';
import * as CheckBox from 'Fx/Controls/CheckBox';
import * as Section from "Fx/Controls/Section";
import { CommonContext } from '../Models/CreateModels';
export interface Context extends CommonContext {
    container: Container;
    lifetimeManager: LifetimeManager;
    dataModel: {
        basics: CommonContext["dataModel"]["basics"] & {
            hubConfigDefaultResourceGroup: KnockoutObservableBase<ResourceGroupDropDown.Value>;
            hubConfigDefaultResourceGroupSameAs: KnockoutObservableBase<boolean>;
        };
    };
}


export function create(context: Context): Section.Contract {
    const { container, lifetimeManager, dataModel } = context;
    const extendedLifetimeManager = MsPortalFx.extend(lifetimeManager, { openDialog: container.openDialog.bind(container) });

    const input = ResourceGroupDropDown.create(
        extendedLifetimeManager,
        {
            label: Resources.CreateBladeHubConfigDefaultResourceGroup.label,
            infoBalloonContent: Resources.CreateBladeHubConfigDefaultResourceGroup.info,
            value: dataModel.basics.hubConfigDefaultResourceGroup,
            subscriptionId: dataModel.basics.subscription,
            placeholder: '',
            allowedMode: ResourceGroupDropDown.Mode.UseExisting,
        });
        context.dataModel.basics.resourceGroup.subscribeAndRun(context.lifetimeManager, rg => {
            if (!input.dirty()) {
                if (rg?.mode === ResourceGroupDropDown.Mode.UseExisting) {
                    context.dataModel.basics.hubConfigDefaultResourceGroup(rg);
                } else {
                    context.dataModel.basics.hubConfigDefaultResourceGroup(null);
                }
            }
        });
    const sameAsHubCheckbox = CheckBox.create(lifetimeManager,
        {
            label: Resources.CreateBladeHubConfigDefaultResourceGroup.sameAsHubLabel,
            labelOnRight: false,
            value: dataModel.basics.hubConfigDefaultResourceGroupSameAs,
            visible: true,
            cssClass: "msportalfx-nested-control"
        });
    // Automatically enable checkbox once a resource group is selected
    context.dataModel.basics.resourceGroup.subscribe(context.lifetimeManager, () => {
        if (!sameAsHubCheckbox.dirty() && !input.dirty()) {
            context.dataModel.basics.hubConfigDefaultResourceGroupSameAs(true);
        }
    });
    // Automatically disable checkbox when once a default project resource group is selected
    context.dataModel.basics.hubConfigDefaultResourceGroup.subscribe(context.lifetimeManager, () => {
        context.dataModel.basics.hubConfigDefaultResourceGroupSameAs(false);
    });
    return Section.create(container,
        {
            children: [
                input, sameAsHubCheckbox
            ],
            smartAlignLabel: true,
        });
}
