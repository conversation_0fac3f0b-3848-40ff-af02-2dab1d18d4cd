/**
 * Copyright (c) Microsoft Corporation.  All rights reserved.
 */

import * as BladeBase from 'Fx/Composition/BladeBase';
import { Container } from 'Fx/Composition/TemplateBlade';
import * as SubscriptionDropDown from 'Fx/Controls/SubscriptionDropDown';
import { LifetimeManager } from 'Fx/Lifetime';

import { CommonContext, CommonControl } from '../Models/CreateModels';

import Resources = require("Resx/ClientResources");

export interface Context extends CommonContext {
    container: Container;
    lifetimeManager: LifetimeManager;
    dataModel: {
        basics: {
            subscription: KnockoutObservableBase<MsPortalFx.Azure.Subscription>;
        };
    };
    provisioning: BladeBase.DoesProvisioning.Provisioning;
}

export interface BasicOptions extends CommonControl {
    resourceProviders?: string[] | KnockoutObservableBase<string[]>;
    preventAutomaticChange?: boolean;
}

export interface Options extends BasicOptions {
}

export function create(context: Context, createOptions?: Options): SubscriptionDropDown.Contract {
    const { lifetimeManager, dataModel, provisioning } = context;

    const subscriptionIds = provisioning && provisioning.initialValues && provisioning.initialValues.subscriptionIds;
    const validations = (<Array<MsPortalFx.ViewModels.CustomValidation | MsPortalFx.ViewModels.RequiredValidation>>[
        new MsPortalFx.ViewModels.RequiredValidation()]).concat(
            createOptions?.customValidations || []);

    const subscriptionDropDown = SubscriptionDropDown.create(lifetimeManager, {
        label: Resources.createSubscriptionLabel,
        infoBalloonContent: createOptions?.overrideInfoBalloonContent,
        value: dataModel.basics.subscription,
        initialSubscriptionId: subscriptionIds,
        resourceProviders: createOptions?.resourceProviders,
        validations: validations,
    });

    return subscriptionDropDown;
}