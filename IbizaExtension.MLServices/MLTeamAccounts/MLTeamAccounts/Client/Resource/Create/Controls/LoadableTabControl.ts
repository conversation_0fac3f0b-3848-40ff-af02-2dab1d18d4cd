/**
 * Copyright (c) Microsoft Corporation.  All rights reserved.
 */

import * as Section from 'Fx/Controls/Section';
import * as TabControl from 'Fx/Controls/TabControl';

import { Tab } from '../Models/ControlModels';
import { LifetimeManager } from 'Fx/Lifetime';
import { Container } from "Fx/Composition/TemplateBlade";

export interface Options {
    /**
     * Tabs to show within the control.
     */
    readonly tabs: Tab[] | KnockoutObservableArray<Tab>;
    readonly container: Container;
}

export function create(lifetimeManager: LifetimeManager, options?: Options): TabControl.Contract {
    const { tabs, container } = options;
    const currentTabs = tabs || [];
    const tabControl = TabControl.create(lifetimeManager, {
        smartAlignLabel: true
    });

    // Workaround since TabControl doesn't respect section visibility
    ko.pureComputed(() => ko.unwrap(currentTabs).map(item => item.section).filter(item => ko.unwrap(item.visible) !== false)).subscribeAndRun(lifetimeManager, tabControl.tabs);

    const findTabBySection = ko.pureComputed(() => {
        return (section: Section.Contract) => section && ko.unwrap(currentTabs).find(item => item.section === section)
    });
    tabControl.activeTab.subscribe(lifetimeManager, (value) => {
        const tab = findTabBySection()(value);
        tab && tab.onLoad && tab.onLoad();
        container.announce(tab.name);
    });

    return tabControl;
}
