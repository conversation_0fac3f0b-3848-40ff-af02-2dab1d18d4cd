﻿import * as TemplatePart from "Fx/Composition/TemplatePart";
import { BladeReferences } from "Fx/Composition";
import { ArmId } from "Fx/ResourceManagement";
import ExtensionDefinition = require("../_generated/ExtensionDefinition");
import Icons = require("../Shared/Icons");
import { DataContext } from "./ResourceArea";
import Utilities from "../Shared/Utilities";

/**
 * Resource Part that implements pinned part for Overview blade
 * Learn more about decorator based parts at: https://aka.ms/portalfx/nopdl
 */
@TemplatePart.Decorator({
    htmlTemplate:
        "<div class='msportalfx-padding'>" +
        "  <div style='height: 20px; width: 20px;' data-bind='image: customIcon'></div>" +
        "</div>",
    styleSheets: ["../Shared/Styles/CustomStyles.css"],
    resizeMode: TemplatePart.ResizeMode.User,
    initialSize: TemplatePart.Size.Custom,
    initialHeight: 1,
    initialWidth: 2
})
@TemplatePart.InjectableModel.Decorator(DataContext)
export class MachineLearningServicesPart {

    public title: string;
    public subtitle: string;
    public customIcon: MsPortalFx.Base.Image;

    /**
     * The context property contains APIs you can call to interact with the shell.
     * It will be populated for you by the framework before your onInitialize() function is called.
     *   https://aka.ms/portalfx/nopdl/context
     */
    public context: TemplatePart.Context<MachineLearningOverviewBlade.MachineLearningOverviewBladeParameters, DataContext>;

    public onInitialize() {
        const { model, container } = this.context;
        const entityView = model.machineLearningServicesData.machineLearningServicesEntity.createView(container);
        const resourceId = this.context.parameters.id;
        const machineLearningServicesPromise = entityView.fetch(resourceId);
        const permissionsPromise = MsPortalFx.Base.Security.hasPermission(resourceId, [
            ExtensionDefinition.AssetTypes.MachineLearningServices.Permissions.readServices
        ]);

        this.title = ArmId.parse(resourceId).resourceName;

        this.customIcon = Icons.Icons.webWorkspaceIcon;
        return Q.all([permissionsPromise, machineLearningServicesPromise]).then(
            async values => {
                const hasAccess = values[0];
                if (!hasAccess) {
                    container.unauthorized();
                    return;
                }
                const machineLearningServiceResource = entityView.item();
                this.title = machineLearningServiceResource.name();
                this.subtitle = Utilities.getMachineLearningServicesDisplayNameSingular(machineLearningServiceResource.kind?.());
                this.customIcon = Utilities.getStartboardPartIcon(machineLearningServiceResource.kind?.());
            },
            async reason => {
                throw reason;
            }
        );
    }

    public onClick() {
        const { container, parameters } = this.context;
        return container.openBlade(BladeReferences.forBlade("Overview.ReactView").createReference({ parameters }));
    }
}