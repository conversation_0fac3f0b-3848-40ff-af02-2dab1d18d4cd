import AppInsightsData = require("./Data/AppInsightsData");
import CognitiveSearchData = require("./Data/CognitiveSearchData");
import ContainerRegistryData = require("./Data/ContainerRegistryData");
import DocumentDBData = require("./Data/DocumentDBData");
import KeyVaultData = require("./Data/KeyVaultData");
import MachineLearningServicesData = require("./Data/MachineLearningServicesData");
import MLComputeQuotaData = require("./Data/MLComputeQuotaData");
import PrivateEndpointConnectionsData = require("./Data/PrivateEndpointConnectionsData");
import ProviderRegistrationData = require("./Data/ProviderRegistrationData");
import ResourceManagementData = require("./Data/ResourceManagementData");
import StorageAccountData = require("./Data/StorageAccountData");
import WorkspaceOutboundAccessData = require("./Data/WorkspaceOutboundAccessData");
import AIServicesData = require("./Data/AIServicesData");
import WorkspaceConnectionData = require("./Data/WorkspaceConnectionData");

import * as Di from "Fx/DependencyInjection";

@Di.Class()
export class DataContext {
    public machineLearningServicesData: MachineLearningServicesData.MachineLearningServicesData;
    public providerRegistrationData: ProviderRegistrationData.ProviderRegistrationData;
    public storageAccountData: StorageAccountData.StorageAccountData;
    public containerRegistryData: ContainerRegistryData.ContainerRegistryData;
    public appInsightsData: AppInsightsData.AppInsightsData;
    public keyVaultData: KeyVaultData.KeyVaultData;
    public privateEndpointConnectionsData: PrivateEndpointConnectionsData.PrivateEndpointConnectionsData;
    public mlComputeQuotaData: MLComputeQuotaData.MLComputeQuotaData;
    public resourceManagementData: ResourceManagementData.ResourceManagementData;
    public documentDBData: DocumentDBData.DocumentDBData;
    public cognitiveSearchData: CognitiveSearchData.CognitiveSearchData;
    public workspaceOutboundAccessData: WorkspaceOutboundAccessData.WorkspaceOutboundAccessData;
    public aiServicesData: AIServicesData.AIServicesData;
    public workspaceConnectionData: WorkspaceConnectionData.WorkspaceConnectionData;

    constructor(
        machineLearningServicesData: MachineLearningServicesData.MachineLearningServicesData,
        providerRegistrationData: ProviderRegistrationData.ProviderRegistrationData,
        storageAccountData: StorageAccountData.StorageAccountData,
        containerRegistryData: ContainerRegistryData.ContainerRegistryData,
        appInsightsData: AppInsightsData.AppInsightsData,
        keyVaultData: KeyVaultData.KeyVaultData,
        privateEndpointConnectionsData: PrivateEndpointConnectionsData.PrivateEndpointConnectionsData,
        mlComputeQuotaData: MLComputeQuotaData.MLComputeQuotaData,
        resourceManagementData: ResourceManagementData.ResourceManagementData,
        documentDBData: DocumentDBData.DocumentDBData,
        cognitiveSearchData: CognitiveSearchData.CognitiveSearchData,
        workspaceOutboundAccessData: WorkspaceOutboundAccessData.WorkspaceOutboundAccessData,
        aiServicesData: AIServicesData.AIServicesData,
        workspaceConnectionData: WorkspaceConnectionData.WorkspaceConnectionData
    ) {
        this.machineLearningServicesData = machineLearningServicesData;
        this.providerRegistrationData = providerRegistrationData;
        this.storageAccountData = storageAccountData;
        this.containerRegistryData = containerRegistryData;
        this.appInsightsData = appInsightsData;
        this.keyVaultData = keyVaultData;
        this.privateEndpointConnectionsData = privateEndpointConnectionsData;
        this.mlComputeQuotaData = mlComputeQuotaData;
        this.resourceManagementData = resourceManagementData;
        this.documentDBData = documentDBData;
        this.cognitiveSearchData = cognitiveSearchData;
        this.workspaceOutboundAccessData = workspaceOutboundAccessData;
        this.aiServicesData = aiServicesData;
        this.workspaceConnectionData = workspaceConnectionData;
    }
}