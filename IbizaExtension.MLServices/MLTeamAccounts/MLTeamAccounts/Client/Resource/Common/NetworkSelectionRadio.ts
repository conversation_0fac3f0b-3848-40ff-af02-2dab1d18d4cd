import { LifetimeManager } from "Fx/Lifetime";
import * as Resources from "Resx/ClientResources";
import * as RadioButton from "Fx/Controls/RadioButtons";
import { createSelectableCardItemText } from "../../Shared/SelectionCards";
import { WorkspaceNetworkSelectionOption } from "./NetworkingDataModel";

import FxViewModels = MsPortalFx.ViewModels;

const NetworkingResources = Resources.CreateBlade.Network;

export interface INetworkSelectionRadioOptions {
    workspaceNetworkSelectionOption: KnockoutObservableBase<WorkspaceNetworkSelectionOption>;
    isLoading: KnockoutObservableBase<boolean>;
    containerRegistrySku?: KnockoutObservableBase<string | undefined>;
}

export function createNetworkSelectionRadio(lifetimeManager: LifetimeManager, options: INetworkSelectionRadioOptions) {
    const { workspaceNetworkSelectionOption, isLoading, containerRegistrySku } = options;
    const radioButton = RadioButton.create<WorkspaceNetworkSelectionOption>(lifetimeManager, {
        label: undefined,
        items: [
            {
                text: createSelectableCardItemText({
                    header: NetworkingResources.NetworkIsolation.Public.title,
                    listItems: [
                        NetworkingResources.NetworkIsolation.Public.descriptionItemTwo,
                        NetworkingResources.NetworkIsolation.Public.descriptionItemThree
                    ]
                }),
                value: WorkspaceNetworkSelectionOption.Public
            },
            {
                text: createSelectableCardItemText({
                    header: NetworkingResources.NetworkIsolation.PrivateInternetOutbound.title,
                    listItems: [
                        NetworkingResources.NetworkIsolation.PrivateInternetOutbound.descriptionItemTwo,
                        NetworkingResources.NetworkIsolation.PrivateInternetOutbound.descriptionItemThree
                    ]
                }),
                value: WorkspaceNetworkSelectionOption.Private
            },
            {
                text: createSelectableCardItemText({
                    header: NetworkingResources.NetworkIsolation.PrivateApprovedOutbound.title,
                    listItems: [
                        NetworkingResources.NetworkIsolation.PrivateApprovedOutbound.descriptionItemTwo,
                        NetworkingResources.NetworkIsolation.PrivateApprovedOutbound.descriptionItemThree
                    ]
                }),
                value: WorkspaceNetworkSelectionOption.PrivateApprovedOutbound
            }
        ],
        value: workspaceNetworkSelectionOption,
        singleItemPerLine: false,
        cssClass: "ext-ml-selection-cards",
        disabled: isLoading,
        validations: [
            new FxViewModels.RequiredValidation(),
            new MsPortalFx.ViewModels.CustomValidation(NetworkingResources.ConnectivityMethod.skuPrivateEndpointErrorMessage, () => {
                if (
                    (workspaceNetworkSelectionOption() === WorkspaceNetworkSelectionOption.Private ||
                        workspaceNetworkSelectionOption() === WorkspaceNetworkSelectionOption.PrivateApprovedOutbound) &&
                    containerRegistrySku?.() &&
                    containerRegistrySku?.() !== "Premium"
                ) {
                    return Q({ valid: false, message: NetworkingResources.ConnectivityMethod.skuPrivateEndpointErrorMessage });
                } else {
                    return Q({ valid: true, message: "" });
                }
            })
        ]
    });

    if (containerRegistrySku) {
        containerRegistrySku.subscribeAndRun(lifetimeManager, () => {
            radioButton.triggerValidation();
        });
    }

    return radioButton;
}
