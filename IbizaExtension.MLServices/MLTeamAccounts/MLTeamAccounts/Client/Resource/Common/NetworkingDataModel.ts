import * as ResourceGroupDropDown from "Fx/Controls/ResourceGroupDropDown";
import {
    FirewallSku,
    IGenericResource,
    ManagedNetwork,
    NetworkIsolationMode,
    OutboundRule,
    PrivateEndpointDestination,
    PrivateEndpointOutboundRule,
    RuleCategory
} from "Resource/Data/Data.Types";
import { getPrivateDnsZones } from "../../Shared/NetworkUtilities";
import {
    IAppInsightsTemplateResource,
    IContainerRegistryTemplateResource,
    IKeyvaultTemplateResource,
    IStorageAccountTemplateResource
} from "../Create/Create.Types";

import Resources = require("Resx/ClientResources");
import { getOutboundRuleDestination } from "./NetworkingUtilities";

const NetworkingResources = Resources.CreateBlade.Network;

const log = MsPortalFx.Base.Diagnostics.createLog(require);
const enum ErrorCodes {
    GetConnectivityMethodDisplayName = 1,
    ValidateStorageAccountNameForCreate = 2
}

interface BasicsOptional {
    subscription?: MsPortalFx.Azure.Subscription | KnockoutObservableBase<MsPortalFx.Azure.Subscription>;
    resourceGroup?: ResourceGroupDropDown.Value | KnockoutObservableBase<ResourceGroupDropDown.Value>;
    location?: MsPortalFx.Azure.Location | KnockoutObservableBase<MsPortalFx.Azure.Location>;
}

export interface ResourceDetails {
    subscription?: KnockoutObservableBase<MsPortalFx.Azure.Subscription>;
    subscriptionId?: KnockoutObservableBase<string>;
    resourceGroup?: KnockoutObservableBase<ResourceGroupDropDown.Value>;
    resourceGroupName?: KnockoutObservableBase<string>;
    workspaceName?: KnockoutObservableBase<string>;
    location?: KnockoutObservableBase<MsPortalFx.Azure.Location>;
    locationName?: KnockoutObservableBase<string>;
    storageAccount?: KnockoutObservableBase<IStorageAccountTemplateResource>;
    storageAccountId?: KnockoutObservableBase<string>;
    keyVault?: KnockoutObservableBase<IKeyvaultTemplateResource>;
    keyVaultId?: KnockoutObservableBase<string>;
    appInsights?: KnockoutObservableBase<IAppInsightsTemplateResource>;
    appInsightsId?: KnockoutObservableBase<string>;
    containerRegistry?: KnockoutObservableBase<IContainerRegistryTemplateResource>;
    containerRegistryId?: KnockoutObservableBase<string>;
}

export const enum ConnectivityMethod {
    PublicEndpointAllNetworks = "PublicEndpointAllNetworks",
    PrivateEndpoint = "PrivateEndpoint"
}

export const enum WorkspaceNetworkSelectionOption {
    Public = "Public",
    Private = "Private",
    PrivateApprovedOutbound = "PrivateApprovedOutbound"
}

export const enum ManagedNetworkIsolationMode {
    Disabled = "Disabled",
    AllowInternetOutbound = "AllowInternetOutbound",
    AllowOnlyApprovedOutbound = "AllowOnlyApprovedOutbound"
}

export interface PrivateEndpointDetails {
    name: string;
    vnetId: string;
    vnetName: string;
    subnetId: string;
    subnetName: string;
    resourceGroupName?: string;
    subscription?: string;
    integrateWithPrivateDnsZones: boolean;
    privateDnsZones: CreatePrivateEndpointMultiDnsContextBlade.PrivateDnsZoneValue[];
}

export interface ResourceGroupToCreate {
    name: string;
    location: string;
    subscriptionId?: string;
}

export interface ResourceGroupInfo extends ResourceGroupToCreate {
    id: string;
    isNew: boolean;
}

export interface ManagedNetworkRuleItem {
    name: string;
    destinationType: OutboundRule["type"];
    destination: string;
    ruleCategory: RuleCategory;
    outboundRule: OutboundRule;
    active: KnockoutObservable<boolean>;
    parentRuleNames?: string;
    fqdns?: string;
    isExisting?: boolean;
}

export interface ServiceEndpointConfiguration {
    subscription: KnockoutObservable<MsPortalFx.Azure.Subscription>;
    resourceGroup: KnockoutObservable<ResourceGroupDropDown.Value>;
    virtualNetwork: KnockoutObservable<CreatePrivateEndpointContextBlade.VirtualNetworkValue>;
    subnets: KnockoutObservable<CreatePrivateEndpointContextBlade.SubnetValue[]>;
}

export interface InitialNetworkSettings {
    connectivityMethod: ConnectivityMethod;
    managedNetwork?: ManagedNetwork;
}

export interface InitialData {
    managedNetworkIsolationMode: ManagedNetworkIsolationMode;
    outboundRules: KnockoutObservableArray<ManagedNetworkRuleItem>;
    allowedNetworkSelectionOptions: ManagedNetworkIsolationMode[];
    firewallSku: FirewallSku;
}

/**
 * This class represents the networking view model which is used in the create networking tab and overview networking properties.
 */
export class NetworkingDataModel {
    private _initialNetworkSettings: KnockoutObservableBase<InitialNetworkSettings>;

    public connectivityMethod = ko.observable<ConnectivityMethod>(ConnectivityMethod.PublicEndpointAllNetworks);
    public networkIsolationMode = ko.observable<NetworkIsolationMode>("Disabled");
    public delegatedSubnet = ko.observable<IGenericResource>(undefined);

    public privateEndpointConfigurations = ko.observableArray<CreatePrivateEndpointMultiDnsContextBlade.Output>([]);
    public managedNetworkOutboundAccessRules = ko.observableArray<ManagedNetworkRuleItem>([]);
    public firewallSku = ko.observable<FirewallSku>("Standard");
    public provisionManagedNetwork = ko.observable<boolean>(false);
    public newResourceGroupsInfo: KnockoutObservableBase<ResourceGroupInfo[]>;
    public serviceEndpointConfiguration: ServiceEndpointConfiguration = {
        subscription: ko.observable<MsPortalFx.Azure.Subscription>(),
        resourceGroup: ko.observable<ResourceGroupDropDown.Value>(),
        virtualNetwork: ko.observable<CreatePrivateEndpointContextBlade.VirtualNetworkValue>(),
        subnets: ko.observable<CreatePrivateEndpointContextBlade.SubnetValue[]>([])
    };
    public initialData: KnockoutObservableBase<InitialData>;
    /**
     * Compares current state to initial state to determine if the values have been modified.
     * If no initial state was provided then it defaults to true.
     */
    public isDirty: KnockoutComputed<boolean>;

    public constructor(options?: { initialSettings?: KnockoutObservableBase<InitialNetworkSettings> }) {
        this._initialNetworkSettings = options?.initialSettings;

        this.initialData = ko.pureComputed(() => {
            let networkIsolationMode = ManagedNetworkIsolationMode.Disabled;
            let allowedNetworkSelectionOptions: ManagedNetworkIsolationMode[] = [];
            let outboundRules: KnockoutObservableArray<ManagedNetworkRuleItem> = ko.observableArray([]);
            let firewallSku: FirewallSku = "Standard";
            if (this._initialNetworkSettings?.()) {
                const isolationMode = this._initialNetworkSettings()?.managedNetwork?.isolationMode;
                if (isolationMode === "AllowInternetOutbound") {
                    networkIsolationMode = ManagedNetworkIsolationMode.AllowInternetOutbound;
                } else if (isolationMode === "AllowOnlyApprovedOutbound") {
                    networkIsolationMode = ManagedNetworkIsolationMode.AllowOnlyApprovedOutbound;
                    firewallSku = this._initialNetworkSettings()?.managedNetwork?.firewallSku ?? "Standard";
                }

                const changeableIsolationModes = this._initialNetworkSettings()?.managedNetwork?.changeableIsolationModes;
                if (changeableIsolationModes) {
                    changeableIsolationModes.forEach(mode => {
                        if (mode === "AllowInternetOutbound") {
                            allowedNetworkSelectionOptions.push(ManagedNetworkIsolationMode.AllowInternetOutbound);
                        } else if (mode === "AllowOnlyApprovedOutbound") {
                            allowedNetworkSelectionOptions.push(ManagedNetworkIsolationMode.AllowOnlyApprovedOutbound);
                        }
                    });
                }
                outboundRules(mapOutboundRulesToManagedNetworkRuleItems(this._initialNetworkSettings()?.managedNetwork?.outboundRules));
            }
            // current isolation mode should be allowed
            if (!allowedNetworkSelectionOptions.includes(networkIsolationMode)) {
                allowedNetworkSelectionOptions.push(networkIsolationMode);
            }

            return {
                managedNetworkIsolationMode: networkIsolationMode,
                outboundRules: outboundRules,
                allowedNetworkSelectionOptions: allowedNetworkSelectionOptions,
                firewallSku: firewallSku
            };
        });

        this.setInitialNetworkSettings();

        this.newResourceGroupsInfo = ko.pureComputed(() => {
            const result: ResourceGroupInfo[] = [];
            const connectivity = this.connectivityMethod();
            if (connectivity === ConnectivityMethod.PrivateEndpoint) {
                const privateEndpoints = this.privateEndpointConfigurations();
                if (privateEndpoints) {
                    privateEndpoints.forEach(output => {
                        const info = parseResourceGroupInfo(output.privateEndpointConfiguration);
                        if (info) {
                            result.push(info);
                        }
                    });
                }
            }

            return MsPortalFx.unique(result, (a, b) => a.id.toLowerCase() === b.id.toLowerCase());
        });

        this.isDirty = ko.pureComputed(() => {
            if (!this._initialNetworkSettings) {
                return true;
            }

            // check if the rules have changed
            const initRules = this.initialData()
                .outboundRules()
                .sort((rule, rule2) => rule.name.localeCompare(rule2.name));
            const currentRules = this.managedNetworkOutboundAccessRules().sort((rule, rule2) => rule.name.localeCompare(rule2.name));
            if (currentRules.length !== initRules.length) {
                return true;
            }

            if (!currentRules.every((rule, idx) => ruleIsEqual(rule, initRules[idx]))) {
                return true;
            }

            // check if the network isolation mode has changed
            const initIsolationMode = this._initialNetworkSettings?.()?.managedNetwork?.isolationMode;
            const currentIsolationMode = this.networkIsolationMode();
            if (initIsolationMode !== currentIsolationMode) {
                return true;
            }

            // check if the connectivity method has changed
            const initConnectivityMethod = this._initialNetworkSettings?.().connectivityMethod;
            const currentConnectivityMethod = this.connectivityMethod();
            if (initConnectivityMethod !== currentConnectivityMethod) {
                return true;
            }

            // check if firewall sku has changed
            const initFirewallSku = this.initialData().firewallSku;
            const currentFirewallSku = this.firewallSku();
            if (initFirewallSku !== currentFirewallSku) {
                return true;
            }

            return false;
        });
    }

    public setInitialNetworkSettings(): void {
        const initialSettings = this._initialNetworkSettings?.();
        if (initialSettings) {
            const { connectivityMethod, managedNetwork } = initialSettings;
            if (connectivityMethod) {
                this.connectivityMethod(connectivityMethod);
            }
            if (managedNetwork?.isolationMode) {
                this.networkIsolationMode(managedNetwork.isolationMode);
            }
        } else {
            this.connectivityMethod(ConnectivityMethod.PublicEndpointAllNetworks);
            this.networkIsolationMode("Disabled");
        }
    }

    public reset(): void {
        this.setInitialNetworkSettings();
        this.managedNetworkOutboundAccessRules([]);
        this.initialData().outboundRules(mapOutboundRulesToManagedNetworkRuleItems(this._initialNetworkSettings?.()?.managedNetwork?.outboundRules));
        this.firewallSku(this._initialNetworkSettings?.()?.managedNetwork?.firewallSku ?? "Standard");
    }

    public getPrivateEndpointDetails(): PrivateEndpointDetails | null {
        const connectivityMethod = this.connectivityMethod();
        if (connectivityMethod === ConnectivityMethod.PrivateEndpoint) {
            const privateEndpointConfigs = this.privateEndpointConfigurations();
            if (privateEndpointConfigs && privateEndpointConfigs.length > 0) {
                const defaultConfig = privateEndpointConfigs[0];
                if (
                    defaultConfig.privateEndpointConfiguration &&
                    defaultConfig.privateEndpointConfiguration.privateEndpoint &&
                    defaultConfig.networkingConfiguration &&
                    defaultConfig.networkingConfiguration.subnetValue &&
                    defaultConfig.networkingConfiguration.virtualNetworkValue
                ) {
                    return {
                        name: defaultConfig.privateEndpointConfiguration.privateEndpoint.name,
                        subnetId: defaultConfig.networkingConfiguration.subnetValue.id,
                        subnetName: defaultConfig.networkingConfiguration.subnetValue.model.name,
                        vnetId: defaultConfig.networkingConfiguration.virtualNetworkValue.id,
                        vnetName: defaultConfig.networkingConfiguration.virtualNetworkValue.model.name,
                        resourceGroupName: defaultConfig.privateEndpointConfiguration.resourceGroup?.value?.name,
                        subscription: defaultConfig.privateEndpointConfiguration.subscription?.subscriptionId,
                        integrateWithPrivateDnsZones:
                            defaultConfig.privateDnsZoneConfiguration &&
                            defaultConfig.privateDnsZoneConfiguration.integrateWithPrivateDnsZone === true,
                        privateDnsZones: getPrivateDnsZones(defaultConfig)
                    };
                }
            }
        }

        return null;
    }

    public getInitialSettings(): InitialNetworkSettings | undefined {
        return this._initialNetworkSettings?.();
    }

    public getManagedNetwork(): ManagedNetwork {
        return {
            isolationMode: this.networkIsolationMode(),
            outboundRules: this.getActiveOutboundAccessRules(false),
            firewallSku: this.networkIsolationMode() === "AllowOnlyApprovedOutbound" ? this.firewallSku() : undefined
        };
    }

    public getProvisionManagedNetwork(): boolean | undefined {
        if (this.networkIsolationMode() === "Disabled") {
            return undefined;
        }
        return this.provisionManagedNetwork();
    }

    public getManagedNetworkUpdatePayload(): ManagedNetwork {
        const activeRules = this.getActiveOutboundAccessRules(false) ?? ({} as Record<string, OutboundRule>);
        const deletedRules = this.initialData()
            .outboundRules()
            .filter(rule => rule.ruleCategory != "Required" && rule.ruleCategory != "Dependency" && !(rule.name in activeRules))
            .reduce<Record<string, OutboundRule>>((acc, rule) => {
                acc[rule.name] = null;
                return acc;
            }, {});
        return {
            isolationMode: this.networkIsolationMode(),
            outboundRules: { ...activeRules, ...deletedRules },
            firewallSku: this.networkIsolationMode() === "AllowOnlyApprovedOutbound" ? this.firewallSku() : undefined
        };
    }

    /**
     * Get only outbound rules which are active and mandatory (Required or Dependency rules) depending on input.
     * Will be empty if network isolation mode is disabled.
     * @param includeMandatory default true
     * @returns ManagedNetworkRuleItem[]
     */
    public getActiveOutboundAccessRules(includeMandatory: boolean = true): Record<string, OutboundRule> | undefined {
        if (this.networkIsolationMode() === "Disabled") {
            return undefined;
        }
        const activeRules = this.managedNetworkOutboundAccessRules().filter(
            rule => rule.active() && (includeMandatory || (rule.ruleCategory !== "Required" && rule.ruleCategory !== "Dependency"))
        );
        if (!activeRules.length) {
            return undefined;
        }
        return activeRules.reduce<Record<string, OutboundRule>>((acc, rule) => {
            acc[rule.name] = rule.outboundRule;
            return acc;
        }, {});
    }
}

export function getConnectivityMethodDisplayName(connectivityMethod: ConnectivityMethod, toLower = false): string {
    let result: string;

    switch (connectivityMethod) {
        case ConnectivityMethod.PublicEndpointAllNetworks:
            result = NetworkingResources.ConnectivityMethod.allNetworks;
            break;
        case ConnectivityMethod.PrivateEndpoint:
            result = NetworkingResources.ConnectivityMethod.private;
            break;
        default:
            log.error(
                `Did not find a matching display name for connectivity method, defaulting to '${NetworkingResources.ConnectivityMethod.allNetworks}'`,
                ErrorCodes.GetConnectivityMethodDisplayName,
                connectivityMethod
            );

            result = NetworkingResources.ConnectivityMethod.allNetworks;
            break;
    }

    if (toLower && result) {
        result = result.toLocaleLowerCase();
    }

    return result;
}

export function getNetworkIsolationDisplayName(connectivityMethod: ConnectivityMethod, isolationMode: NetworkIsolationMode, toLower = false): string {
    let result: string;
    switch (isolationMode) {
        case "Disabled":
            result = NetworkingResources.NetworkIsolation.Public.title;
            break;
        case "AllowInternetOutbound":
            result = NetworkingResources.NetworkIsolation.PrivateInternetOutbound.title;
            break;
        case "AllowOnlyApprovedOutbound":
            result = NetworkingResources.NetworkIsolation.PrivateApprovedOutbound.title;
            break;
        default:
            log.error(
                `Did not find a matching display name for network isolation mode, defaulting to '${NetworkingResources.NetworkIsolation.Public.title}'`,
                ErrorCodes.GetConnectivityMethodDisplayName,
                isolationMode
            );

            result = NetworkingResources.NetworkIsolation.Public.title;
            break;
    }

    if (toLower && result) {
        result = result.toLocaleLowerCase();
    }

    return result;
}

function parseResourceGroupInfo(basics: BasicsOptional): ResourceGroupInfo {
    const { subscription, resourceGroup, location } = basics;
    let resourceGroupName: string;

    const resourceGroupValue = resourceGroup && ko.unwrap(resourceGroup);
    if (resourceGroupValue && resourceGroupValue.value && resourceGroupValue.mode === ResourceGroupDropDown.Mode.CreateNew) {
        resourceGroupName = resourceGroupValue.value.name;

        const subscriptionValue = subscription && ko.unwrap(subscription);
        const subscriptionId = subscriptionValue && subscriptionValue.subscriptionId;
        const locationValue = location && ko.unwrap(location);
        const locationId = locationValue && locationValue.name;

        if (subscriptionId && resourceGroupName) {
            return {
                id: `/subscriptions/${subscriptionId}/resourcegroups/${resourceGroupName}`,
                isNew: true,
                location: locationId,
                subscriptionId: subscriptionId,
                name: resourceGroupName
            };
        }
    }

    return null;
}

function mapOutboundRulesToManagedNetworkRuleItems(outboundRules?: Record<string, OutboundRule>): ManagedNetworkRuleItem[] {
    if (!outboundRules) {
        return [];
    }
    return Object.entries(outboundRules).map(([name, rule]) => ({
        name,
        active: ko.observable(true),
        destination: getOutboundRuleDestination(rule),
        destinationType: rule.type,
        outboundRule: rule,
        ruleCategory: rule.category,
        parentRuleNames: rule.type === "PrivateEndpoint" ? (rule as PrivateEndpointOutboundRule).parentRuleNames?.join(",") : "",
        fqdns: rule.type === "PrivateEndpoint" ? (rule as PrivateEndpointOutboundRule).fqdns?.join(",") : "",
        isExisting: true
    }));
}

// TODO: better way to check equality? lodash deep equal or something?
function ruleIsEqual(rule: ManagedNetworkRuleItem, rule2: ManagedNetworkRuleItem): boolean {
    return (
        rule.name === rule2.name &&
        rule.active() === rule2.active() &&
        rule.destination === rule2.destination &&
        rule.destinationType === rule2.destinationType &&
        rule.ruleCategory === rule2.ruleCategory &&
        rule.parentRuleNames === rule2.parentRuleNames &&
        rule.fqdns === rule2.fqdns &&
        rule.isExisting === rule2.isExisting &&
        (rule.destinationType != "PrivateEndpoint" ||
            ((rule.outboundRule.destination as PrivateEndpointDestination).sparkEnabled ===
                (rule2.outboundRule.destination as PrivateEndpointDestination).sparkEnabled &&
                areFqdnsEqual((rule.outboundRule as PrivateEndpointOutboundRule).fqdns, (rule2.outboundRule as PrivateEndpointOutboundRule).fqdns)))
    );
}

function areFqdnsEqual(fqdns: string[], fqdns2: string[]): boolean {
    if (!fqdns && !fqdns2) {
        return true;
    }
    if (fqdns.length !== fqdns2.length) {
        return false;
    }
    return fqdns.every(fqdn => fqdns2.includes(fqdn));
}
