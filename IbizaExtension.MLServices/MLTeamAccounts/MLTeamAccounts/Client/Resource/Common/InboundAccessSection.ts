import { DataContext } from "Resource/ResourceArea";
import * as InfoBox from "Fx/Controls/InfoBox";
import * as RadioButton from "Fx/Controls/RadioButtons";
import * as Section from "Fx/Controls/Section";
import { LifetimeManager } from "Fx/Lifetime";
import { Container } from "Fx/Composition/TemplateBlade";
import Resources = require("Resx/ClientResources");

import { DocLinkIds, getDocumentUrl } from "../../Shared/DocLinks";
import { createSectionHeader, createLink } from "../Create/Utilities/NoPdlUiHelpers";

import { WorkspaceMappedKind } from "../../Shared/Enums";
import * as Format from "../../Shared/Controls/Format";
import { ConnectivityMethod, NetworkingDataModel, ResourceDetails, WorkspaceNetworkSelectionOption } from "./NetworkingDataModel";
import { WorkspaceInboundAccessControl } from "./WorkspaceInboundAccessControl";
import { createNetworkingSection } from "./NetworkingUtilities";
import * as DelegateSubnetDropdown from "../Create/Controls/Dropdowns/DelegateSubnetDropdown";
import { AdvancedDataModel } from "Resource/Create/ViewModels/CreateMachineLearningModels";
import ClientResources = require("Resx/ClientResources");
import { Icons } from "Shared/Icons";

const NetworkingResources = Resources.CreateBlade.Network;

interface Context {
    lifetimeManager: LifetimeManager;
    container: Container;
}

export interface Options {
    workspaceKind: WorkspaceMappedKind
    form: MsPortalFx.ViewModels.FormProperties;
    networkingDataModel: NetworkingDataModel;
    advancedDataModel: AdvancedDataModel;
    resourceDetails: ResourceDetails;
    dataContext: DataContext;
    isLoading: KnockoutObservable<boolean>;
    isCreationFlow: boolean;
    resourceId?: string;
    isDelegateSubnetEnabled: KnockoutObservable<boolean>;
}

export class InboundAccessSection {
    public section: Section.Contract;

    private _workspaceKind: WorkspaceMappedKind;
    private _networkingDataModel: NetworkingDataModel;
    private _context: Context;
    private _resourceDetails: ResourceDetails;
    private _workspaceInboundAccessControl: WorkspaceInboundAccessControl;
    private _isCreateFlow: boolean;
    private _publicNetworkAccessRadioButtons: RadioButton.Contract<ConnectivityMethod>;
    private _workspaceNetworkSelectionOption = ko.observable<WorkspaceNetworkSelectionOption>(WorkspaceNetworkSelectionOption.Public);
    private _alertConfiguration: KnockoutObservableBase<MsPortalFx.ViewModels.AlertConfiguration>;
    private _sectionChildren: KnockoutObservableArray<any> = ko.observableArray<any>([]);
    private _delegateSubnet: DelegateSubnetDropdown.Contract;

    constructor(context: Context, options: Options) {
        const { form, networkingDataModel, isCreationFlow } = options;
        const { lifetimeManager } = context;
        this._context = context;
        this._networkingDataModel = networkingDataModel;
        this._workspaceKind = options.workspaceKind;
        this._resourceDetails = options.resourceDetails;
        this._isCreateFlow = isCreationFlow;
        if (!this._isCreateFlow) {
            this._alertConfiguration = ko.observable<MsPortalFx.ViewModels.AlertConfiguration>({
                showAlert: networkingDataModel.isDirty()
            });
            networkingDataModel.isDirty.subscribe(lifetimeManager, (isDirty: boolean) => {
                this._alertConfiguration({
                    showAlert: isDirty
                });
            });
            form.configureAlertOnClose(this._alertConfiguration);
        }

        this.initializeSection(options);

        this.section = Section.create(lifetimeManager, {
            name: isCreationFlow ? NetworkingResources.inboundAccessTabTitle : undefined,
            children: this._sectionChildren,
            cssClass: "ext-focus-container"
        });
    }

    public initializeSection(options: Options): void {
        const { lifetimeManager } = this._context;
        if(this._delegateSubnet === undefined &&  this._workspaceKind === WorkspaceMappedKind.Hub) {
            this._delegateSubnet = DelegateSubnetDropdown.create(this._context, {
                 subscription: this._resourceDetails.subscription,
                 workspaceName: this._resourceDetails.workspaceName,
                 onSelectionChanged: (resource) => {
                    this._networkingDataModel.delegatedSubnet(resource?.data);
                 },
                 visible: options.isDelegateSubnetEnabled
            });
        }

        const children: any[] = [];
        this._workspaceInboundAccessControl = new WorkspaceInboundAccessControl(this._context, {
            ...options,
            workspaceNetworkSelectionOption: this._workspaceNetworkSelectionOption
        });

        this._createPublicNetworkAccessRadioButtons(lifetimeManager);
        children.push(
            createNetworkingSection(
                lifetimeManager,
                [
                    createSectionHeader(NetworkingResources.SubnetDelegate.title),
                    createLink({
                        text: NetworkingResources.SubnetDelegate.message,
                    linkText: NetworkingResources.networkIsolationLearnMore,
                    linkUri: "https://aka.ms/azure-ai/agents/delegate-subnet"
                    }),
                    this._delegateSubnet,
                ],
                ko.pureComputed(() => { return options.isDelegateSubnetEnabled() && this._workspaceKind === WorkspaceMappedKind.Hub; }),
                "ext-nopdlcreate-section"
            ),
        );

        const publicAccessDescription = Format.create(lifetimeManager, {
            format: `<div style="padding: 0; margin-top: 10px">${ClientResources.Networking.PublicAccess.description}</div>`, children: [
                {
                    htmlTemplate: `<a href="${getDocumentUrl(DocLinkIds.PublicAccess)}">${ClientResources.Networking.PublicAccess.learnMoreText}<div class='ext-ml-ws2-new-banner-link-icon' data-bind='image: linkIcon'></div></a>`,
                    innerViewModel: {
                        linkIcon: Icons.hyperlinkIcon
                    }
                }
            ]
        });

        children.push(
            createNetworkingSection(
                lifetimeManager,
                [
                    publicAccessDescription,
                    this._publicNetworkAccessRadioButtons,
                    ko.pureComputed(() => {
                        return (
                            InfoBox.createInline(lifetimeManager, {
                                style: InfoBox.Style.Info,
                                text:
                                this._publicNetworkAccessRadioButtons.value()
                                        ? ClientResources.Networking.PublicAccess.allNetworksInfoText
                                        : ClientResources.Networking.PublicAccess.disabledInfoText
                            })
                        );
                    }),
                    ...this._workspaceInboundAccessControl.getWorkspaceInboundAccessItems(),
                ]
            )
        );

        this._sectionChildren(children);
    }

    public load(): Q.Promise<any> {
        return Q.all([this._workspaceInboundAccessControl.load()]);
    }

    public onLoad(): void {
        this._workspaceInboundAccessControl.onLoad();
    }

    public triggerValidation(): Q.Promise<boolean> {
        return this._workspaceInboundAccessControl.triggerValidation();
    }

    private _createPublicNetworkAccessRadioButtons(lifetimeManager: LifetimeManager): void {
        this._publicNetworkAccessRadioButtons = RadioButton.create(lifetimeManager, {
            items: [
                { text: "Disabled", value: ConnectivityMethod.PrivateEndpoint },
                { text: "All networks", value: ConnectivityMethod.PublicEndpointAllNetworks }
            ],
            value: ConnectivityMethod.PublicEndpointAllNetworks,
            label: "Public network access",
            singleItemPerLine: true,
            validations: [
                new MsPortalFx.ViewModels.RequiredValidation()
            ],
        });

        this._publicNetworkAccessRadioButtons.value.subscribeAndRun(lifetimeManager, (value: ConnectivityMethod) => {
            this._networkingDataModel.connectivityMethod(value);
        });
    }

}
