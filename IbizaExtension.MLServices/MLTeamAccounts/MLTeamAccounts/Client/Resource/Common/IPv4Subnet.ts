/**
* Code Reference: https://msazure.visualstudio.com/One/_git/AzureUX-IaaSExp?path=/src/src/Ux/ExtensionCommon/TypeScript/ExtensionCommon/Network/IPv4Subnet.ts
*/

import { IPv4Address } from "./IPv4Address";
import * as ClientResources from "Resx/ClientResources";

const NetworkingPublicAccessResources = ClientResources.Networking.PublicAccess;

export class IPv4Subnet extends IPv4Address {
    /**
    * The first private IP address ranges in string form for each private IP address group.
    */
   public static firstPrivateAddressA = "10.0.0.0";
   public static firstPrivateAddressB = "**********";
   public static firstPrivateAddressC = "***********";

    /**
    * The IPv4 Subnet Prefix.
    * Should be a number between 1 and 32
    */
    public prefix: number;

    /**
    * The number representaiton of the actual byte value of the subnet Net mask
    */
    public subnetNetMaskBytes: number;

    /**
    * The number representaiton of the actual byte value of the subnet mask
    */
    public subnetMaskBytes: number;

    /**
    * The number representaiton of the actual byte value of the subnet's broadcast address
    */
    public subnetBroadcastAddressBytes: number;

    /**
    * Creates a new instance of the IPv4Subnet class
    * This class is used to validate and work with IPv4 Subnets
    * @param subnetCidr, a string of the subnet to create the class with. Accepted values are IP, or IP/Prefix
    * @param minPrefix, optional minimum prefix value
    * @param maxPrefix, optional maximum prefix value
    */
    constructor(subnetCidr: string, minPrefix = 1, maxPrefix = 32, allowFirstOctetZero = false) {
        if (!subnetCidr) {
            throw new Error(NetworkingPublicAccessResources.FirewallValidation.nonNullSubnet);
        }

        const subnetTokens = subnetCidr.split("/");

        super(subnetTokens[0], allowFirstOctetZero);

        // Assume the user passed in a simple IP Address. Give them a prefix of 32 because we're nice
        if (subnetTokens.length === 1) {
            this.prefix = 32;
        } else if (subnetTokens.length > 2) { // Too many slashes!
            throw new Error(NetworkingPublicAccessResources.FirewallValidation.malformedSubnet.format(subnetCidr));
        } else {
            // If they don't give us a proper number, set to -1 so _validatePrefix throws.
            this.prefix = this._isNumeric(subnetTokens[1]) ? parseInt(subnetTokens[1], 10) : -1;
        }

        this._validatePrefix(minPrefix, maxPrefix);
        this._calculateSubnetByteValues();
    }

    /**
    * Prints a friendly representation of the IP Subnet
    * @returns string of the entire subnet, including the prefix
    */
    public toString(): string {
        return "{0}/{1}".format(super.toString(), this.prefix);
    }

    /**
    * Prints a friendly representation of the IP Address
    * @returns string of the IPAddress segment of the Subnet (no prefix)
    */
    public toAddressString(): string {
        return super.toString();
    }

    /*
    * Determines if another subnet is completely contained in this subnet
    * @param subnet IPv4Subnet the other subnet to test
    * @returns boolean. true if the other subnet is completely contained in this subnet. false otherwise.
    */
    public isOtherSubnetContained(subnet: IPv4Subnet): boolean {
        return (this.prefix <= subnet.prefix) && ((this.subnetNetMaskBytes & subnet.subnetBroadcastAddressBytes) >>> 0) === this.subnetMaskBytes;
    }

    /**
    * Determines if another subnet overlaps this one
    * @param subnet IPv4Subnet the other subnet to test
    * @returns boolean. true if the two subnets overlap with eachother. false otherwise
    */
    public doesSubnetOverlap(subnet: IPv4Subnet): boolean {
        return this.isOtherSubnetContained(subnet) || subnet.isOtherSubnetContained(this);
    }

    /**
    * Returns a string representation of the first IP in this range, exlucding the offset
    * @param offset optional offset for the beginning of the range. This is a count of addresses not included in the range
    * @returns string of the starting IP address in the range plus the offset
    */
    public getFirstAddressInRange(offset?: number): string {
        return IPv4Address.bytesToAddressString(((this.subnetBytes & this.subnetNetMaskBytes) >>> 0) + (offset ? offset : 0));
    }

    /**
    * Returns a string representation of the last IP in this range, exlucding the offset
    * @param offset optional offset for the end of the range. This is a count of addresses not included in the range
    * @returns string of the last IP address in the range less the offset
    */
    public getLastAddressInRange(offset?: number): string {
        return IPv4Address.bytesToAddressString(this.subnetBroadcastAddressBytes - (offset ? offset : 0));
    }

    /**
    * Checks that this IP is in a subnet.
    * @param subnet, the subnet to check that this IP is in
    * @returns true if this IP is in the subnet
    */
    public doesContainIp(ip: IPv4Address): boolean {
        const thisIp = new IPv4Subnet(ip.toString());
        return (this.isOtherSubnetContained(thisIp));
    }

    /**
    * Returns a string representation of the IP Range for the subnet
    * @param beginOffset optional offset for the beginning range. This is a count of addresses not included in the range
    * @param endOffset optional offset for the end of the range. This is the count of address not included in the range
    * @returns string in the format of [StartingAddress + beginOffset] - [EndingAddress - endOffset]
    */
    public getSubnetRange(beginOffset?: number, endOffset?: number): string {
        return "{0} - {1}".format(this.getFirstAddressInRange(beginOffset), this.getLastAddressInRange(endOffset));
    }

    /**
    * Returns a number of usable ip addresses for this subnet
    * @param beginOffset optional offset for the beginning range. This is a count of addresses not included in the range
    * @param endOffset optional offset for the end of the range. This is the count of address not included in the range
    * @returns number of usable address in the range, not including the offsets.
    */
    public getUsableIpAddressCount(beginOffset?: number, endOffset?: number): number {
        // We take the entire range not counting the broadcast address
        // Then remove the begin offset and the end offset
        beginOffset = beginOffset ? beginOffset : 0;
        endOffset = endOffset ? endOffset : 0;
        return this.subnetBroadcastAddressBytes - ((this.subnetBytes & this.subnetNetMaskBytes) >>> 0) - beginOffset - endOffset + 1;
    }

    /**
    * Gets a child subnet that is contained in this subnet of the given size
    * @param prefix The prefix to create the child subnet. Uses the parent address space prefix if none is provided
    * @returns A child IPv4Subnet that is contained in the parent subnet
    */
    public getChildSubnet(prefix?: number, otherSubnets?: IPv4Subnet[]): IPv4Subnet;
    public getChildSubnet(prefix?: string, otherSubnets?: IPv4Subnet[]): IPv4Subnet;
    public getChildSubnet(prefix?: any, otherSubnets?: IPv4Subnet[]): IPv4Subnet {
        const prefixToUse = this._getUsablePrefix(prefix, true);

        // If the user supplied other subnets, the new child subnet must not overlap
        if (otherSubnets && otherSubnets.length) {
            otherSubnets.sort((subnet1, subnet2) => {
                return subnet1.subnetBytes < subnet2.subnetBytes ? -1 : 1;
            });
            // Start with the first possible subnet. Loop through other subnets until you find one that overlaps
            // Get the next subnet from the overlapping one and continue until you find a subnet that fits. Return null if not found.
            let subnetToTry = new IPv4Subnet(this.getFirstAddressInRange() + "/" + prefixToUse);
            let continueTesting = true;

            while (continueTesting) {
                continueTesting = false;
                for (let i = 0; i < otherSubnets.length; i++) {
                    const otherSubnet = otherSubnets[i];
                    if (subnetToTry.doesSubnetOverlap(otherSubnet)) {
                        subnetToTry = otherSubnet.getNextSubnet(prefix);
                        continueTesting = true;
                        break;
                    }
                }
            }

            if (this.isOtherSubnetContained(subnetToTry)) {
                return subnetToTry;
            } else {
                // A subnet was found, but it lies outside the containing address space.
                return null;
            }
        } else {
            return new IPv4Subnet(this.getFirstAddressInRange() + "/" + prefixToUse);
        }
    }

    /**
    * Gets the nth IP Address in this subnet
    * @param offset The offset of how many ip addresses to give. 0 by default
    * @returns A string of the ip address
    */
    public getIpAddress(offset?: number): string {
        const offsetToUse = offset ? offset : 0;

        if (this.getUsableIpAddressCount() < offsetToUse) {
            // throw new Error("offset", "The requested IP Address is not in this subnet");
            throw new Error()
        }

        return IPv4Address.bytesToAddressString(this.subnetBytes + offsetToUse);
    }

    /**
    * Gets a new subnet that immediately follows the current subnet
    * @param prefix The prefix to create the new subnet. Uses the this prefix if none is provided
    * @returns The next IPv4Subnet that follows this subnet
    */
    public getNextSubnet(prefix?: number): IPv4Subnet;
    public getNextSubnet(prefix?: string): IPv4Subnet;
    public getNextSubnet(prefix?: any): IPv4Subnet {
        const prefixToUse = this._getUsablePrefix(prefix, false);

        // Find the next IP address after this range
        let address: string;
        if (prefixToUse < this.prefix) {
            const tempSubnet = new IPv4Subnet(this.toAddressString() + "/" + prefixToUse);
            address = IPv4Address.bytesToAddressString(tempSubnet.subnetBroadcastAddressBytes + 1);
        } else {
            address = IPv4Address.bytesToAddressString(this.subnetBroadcastAddressBytes + 1);
        }

        return new IPv4Subnet(address + "/" + prefixToUse);
    }

    public findContainingAddressSpace(addressSpaces: string[]): IPv4Subnet;
    public findContainingAddressSpace(addressSpaces: IPv4Subnet[]): IPv4Subnet;
    public findContainingAddressSpace(addressSpaces: any[]): IPv4Subnet {
        if (!addressSpaces || addressSpaces.length === undefined) {
            // throw new ArgumentError("addressSpaces", "The list of address spaces must be an array");
            throw new Error();
        }

        if (addressSpaces[0] !== null && typeof addressSpaces[0] === "string") {
            addressSpaces = addressSpaces.map((space: string) => {
                return new IPv4Subnet(space);
            });
        }

        const addressSpacesToTest = <IPv4Subnet[]>addressSpaces;

        for (let i = 0; i < addressSpacesToTest.length; i++) {
            const addressSpace = addressSpacesToTest[i];

            if (addressSpace?.isOtherSubnetContained(this)) {
                return addressSpace;
            }
        }

        return null;
    }

    protected _validateOctet(octet: number): void {
        if (isNaN(this.address[octet]) || this.address[octet] < 0 || this.address[octet] > 255) {
            throw new Error(NetworkingPublicAccessResources.FirewallValidation.octet.format(octet + 1, this.address[octet], 0, 255));
        }
    }

    private _getUsablePrefix(prefix: any, checkLength: boolean): number {
        let prefixToUse = this.prefix;
        if (prefix) {
            if (typeof prefix === "string") {
                prefix = parseInt(prefix, 10);
            }

            if (checkLength && prefix < this.prefix) {
                // throw new ArgumentError("prefix", "The supplied prefix was smaller than the parent. It must be greater than or equal to the parent");
                throw new Error();
            }

            prefixToUse = prefix;
        }

        return prefixToUse;
    }

    /**
    * Given a list of existing Subnet ranges, finds a subnet with the desired prefix that does not overlap with any of them
    * @param existingSubnets an array of strings in the format address/prefix. This is the existing space you wish not to overlap with
    * @param desiredPrefix the size of the gap you wish to find. Valid prefixes are 1-32
    * @param privateOnly optional boolean. If true, it will limit the result to only Subnets in the private space
    * @returns an IPv4Subnet class of the desired size, which does not overlap any of the existing subnets. Null if no gap was found
    */
    public static findSubnetGap(existingSubnets: string[], desiredPrefix: number, privateOnly?: boolean): IPv4Subnet;
    /**
    * Given a list of existing Subnet ranges, finds a subnet with the desired prefix that does not overlap with any of them
    * @param existingSubnets an array of IPv4Subnets. This is the existing space you wish not to overlap with
    * @param desiredPrefix the size of the gap you wish to find. Valid prefixes are 1-32
    * @param privateOnly optional boolean. If true, it will limit the result to only Subnets in the private space
    * @returns an IPv4Subnet class of the desired size, which does not overlap any of the existing subnets. Null if no gap was found
    */
    public static findSubnetGap(existingSubnets: IPv4Subnet[], desiredPrefix: number, privateOnly?: boolean): IPv4Subnet;
    public static findSubnetGap(existingSubnets: string[] | IPv4Subnet[], desiredPrefix: number, privateOnly?: boolean): IPv4Subnet {
        if (!existingSubnets) {
            throw new Error("VirtualNetworkControl.IPv4.IPv4Subnet.findSubnetGap: existingSubnets must not be null");
        }

        if (desiredPrefix < 1 || desiredPrefix > 32) {
            // throw new ArgumentError(`VirtualNetworkControl.IPv4.IPv4Subnet.findSubnetGap: The prefix must be between 1 and 32, the supplied prefix was '${desiredPrefix}'.`);
            throw new Error();
        }

        // If there are no other subnets to find a gap for, return the default space using the desired prefix
        if (existingSubnets.length === 0) {
            return new IPv4Subnet("10.0.0.0/" + desiredPrefix.toString());
        }

        let subnets: IPv4Subnet[] = [];
        // IF they sent in a string array, convert them all to IPv4Subnets
        if (typeof (existingSubnets[0]) === "string") {
            (<string[]>existingSubnets).forEach((address) => {
                const subnet = safeGetIpv4Subnet(address);
                if (subnet) {
                    subnets.push(subnet);
                }
            });
        } else {
            subnets = <IPv4Subnet[]>existingSubnets;
        }

        // Sort the subnets by their broadcast address
        subnets.sort((left, right) => {
            return left.subnetBroadcastAddressBytes < right.subnetBroadcastAddressBytes ? -1 : 1;
        });

        const eliminatedIndices: number[] = [];
        for (let i = 0; i < subnets.length; i++) {
            const isIndexEliminated = MsPortalFx.find(eliminatedIndices, (val) => {
                return val === i;
            });

            if (!isIndexEliminated) {
                for (let j = 0; j < subnets.length; j++) {
                    if (i !== j && subnets[i].isOtherSubnetContained(subnets[j])) {
                        eliminatedIndices.push(j);
                    }
                }
            }
        }

        const filteredSubnets = subnets.filter((s, index) => {
            const eliminated = MsPortalFx.find(eliminatedIndices, (val) => {
                return val === index;
            });

            return eliminated === undefined;
        });

        return IPv4Subnet.findSubnetGapInternal(
            filteredSubnets,
            desiredPrefix,
            [IPv4Subnet.firstPrivateAddressA, IPv4Subnet.firstPrivateAddressB, IPv4Subnet.firstPrivateAddressC],
            privateOnly);
   }

    private static findSubnetGapInternal(filteredSubnets: IPv4Subnet[], desiredPrefix: number, startingIpAddresses: string[], privateOnly: boolean): IPv4Subnet {

        // See if the requested gap fits when starting with the "startingIpAddress".
        if (startingIpAddresses && startingIpAddresses.length > 0) {
            const testSubnet = new IPv4Subnet(`${startingIpAddresses[0]}/${desiredPrefix}`);
            if (testSubnet.subnetBroadcastAddressBytes < filteredSubnets[0].subnetBytes) {
                return testSubnet;
            }
        }

        let result: IPv4Subnet;
        const privateA = new IPv4Subnet(`${IPv4Subnet.firstPrivateAddressA}/8`);
        const privateB = new IPv4Subnet(`${IPv4Subnet.firstPrivateAddressB}/12`);
        const privateC = new IPv4Subnet(`${IPv4Subnet.firstPrivateAddressC}/16`);

        for (let i = 0; i < filteredSubnets.length; i++) {
            const leftSubnet = filteredSubnets[i].prefix > desiredPrefix ? new IPv4Subnet(filteredSubnets[i].toAddressString() + "/" + desiredPrefix.toString()) : filteredSubnets[i];
            const rightSubnet = i + 1 < filteredSubnets.length ? filteredSubnets[i + 1] : null;

            if ((rightSubnet === null) || (rightSubnet.subnetBytes - leftSubnet.subnetBroadcastAddressBytes >= Math.pow(2, 32 - desiredPrefix))) {
                result = new IPv4Subnet(IPv4Address.bytesToAddressString(leftSubnet.subnetBroadcastAddressBytes + 1) + "/" + desiredPrefix);

                // If we only want private, we need to add another check, and cycle if it doesnt fit
                if (privateOnly) {
                    if (privateA.isOtherSubnetContained(result) || privateB.isOtherSubnetContained(result) || privateC.isOtherSubnetContained(result)) {
                        return result;
                    } else {
                        if (!rightSubnet) {
                            // If we are at the last address, we should still try to find an existing item within the private space
                            // If we are less than A, shift to A
                            if (result.subnetBroadcastAddressBytes < privateA.subnetBytes) {
                                if (desiredPrefix >= 8) {
                                    return new IPv4Subnet(`${IPv4Subnet.firstPrivateAddressA}/${desiredPrefix}`);
                                } else {
                                    return null;
                                }
                            }
                            // If we are between A and B, shift to B
                            if (result.subnetBytes > privateA.subnetBroadcastAddressBytes && result.subnetBroadcastAddressBytes < privateB.subnetBytes) {
                                if (desiredPrefix >= 12) {
                                    return new IPv4Subnet(`${IPv4Subnet.firstPrivateAddressB}/${desiredPrefix}`);
                                } else {
                                    return null;
                                }
                            }

                            // If we are between B and C, shift to C
                            if (result.subnetBytes > privateB.subnetBroadcastAddressBytes && result.subnetBroadcastAddressBytes < privateC.subnetBytes) {
                                if (desiredPrefix >= 16) {
                                    return new IPv4Subnet(`${IPv4Subnet.firstPrivateAddressC}/${desiredPrefix}`);
                                } else {
                                    return null;
                                }
                            }
                        } else if (startingIpAddresses.length > 1) {
                            // We need to start looking at the start of the next group of private IP addresses.
                            filteredSubnets = filteredSubnets.slice(i + 1); // stop testing the addresses we already looked through. i+1 must exist because 'rightSubnet' is not null.
                            startingIpAddresses.shift(); // remove the starting address we just used.
                            return IPv4Subnet.findSubnetGapInternal(filteredSubnets, desiredPrefix, startingIpAddresses, privateOnly);
                        }

                        // Otherwise, there is nowhere left to look for a valid private address, return null.
                    }
                } else {
                    return result;
                }
            }
        }

        return null;
    }

    private _validatePrefix(minPrefix: number, maxPrefix: number): void {
        if (this.prefix < minPrefix || this.prefix > maxPrefix) {
            throw new Error(NetworkingPublicAccessResources.FirewallValidation.formattedPrefix.format(minPrefix, maxPrefix));
        }
    }

    private _calculateSubnetByteValues(): void {
        // If the prefix is 0, set the subnetNetMaskBytes to 0 because javascript cannot accurately left bit shift by 32 due to numbers being 32 bits
        this.subnetNetMaskBytes = this.prefix === 0 ? 0 : (0xffffffff << (32 - this.prefix)) >>> 0;
        this.subnetMaskBytes = (this.subnetNetMaskBytes & this.subnetBytes) >>> 0;
        this.subnetBroadcastAddressBytes = (((this.subnetNetMaskBytes ^ 0xffffffff) >>> 0) | this.subnetBytes) >>> 0;
    }
}

export function safeGetIpv4Subnet(addressPrefix: string): IPv4Subnet {
    if (addressPrefix) {
        try {
            return new IPv4Subnet(addressPrefix);
        } catch (ex) { }
    }

    return null;
}