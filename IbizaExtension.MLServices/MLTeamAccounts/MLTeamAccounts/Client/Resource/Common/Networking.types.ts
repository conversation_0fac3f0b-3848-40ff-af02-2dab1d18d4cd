import * as CustomHtml from "Fx/Controls/CustomHtml";
import * as Section from "Fx/Controls/Section";
import * as TabControl from "Fx/Controls/TabControl";
import * as Toolbar from "Fx/Controls/Toolbar";
import { DataContext } from "../ResourceArea";

export interface INetworkingControl {
    control: CustomHtml.Contract | Section.Contract | TabControl.Contract;
    load: (model: DataContext) => Promise<any>;
    toolbar?: Toolbar.Contract;
}

export type WorkspaceConnectivity = "public" | "private";

export const enum NetworkAccessOption {
    AllNetworks = "AllNetworks",
    IPBasedAccess = "IPBasedAccess",
    Disabled = "Disabled"
}

export class IpRuleModel {
    value: KnockoutObservable<string>;
}

export const ExternalIpRange = {
    maxCidr: 32,
    minCidr: 0,
};
