import * as Toolbar from "Fx/Controls/Toolbar";
import * as ClientResources from "Resx/ClientResources";
import { LifetimeManager } from "Fx/Lifetime";
import { FeedbackToolbarButton } from "Shared/FeedbackToolbarButton";
import { Container } from "Fx/Composition/TemplateBlade";
import { WorkspaceMappedKind } from "Shared/Enums";

interface Options {
    isLoading: KnockoutObservable<boolean>;
    isDirty: KnockoutObservableBase<boolean>;
    isValidSave: KnockoutObservableBase<boolean>;
    saving: KnockoutObservable<boolean>;
    refresh: () => void;
    discard: () => void;
    save: () => void;
    kind: WorkspaceMappedKind;
}

export function create(lifetimeManager: LifetimeManager,  options: Options, container: Container): Toolbar.Contract {
    const { isLoading, isDirty, isValidSave, refresh, discard, save, saving } = options;

    const toolbarItems = [
        Toolbar.ToolbarItems.createBasicButton(lifetimeManager, {
            label: ko.computed(lifetimeManager, () => saving()
                ? ClientResources.Networking.PublicAccess.Toolbar.savingText
                : ClientResources.Networking.PublicAccess.Toolbar.saveText),
            icon: MsPortalFx.Base.Images.Save(),
            disabled: ko.computed(lifetimeManager, () => isLoading() || !isValidSave() ||  !isDirty() || saving()),
            onClick: save
        }),
        Toolbar.ToolbarItems.createBasicButton(lifetimeManager, {
            label: ClientResources.Networking.PublicAccess.Toolbar.discardChangesText,
            icon: MsPortalFx.Base.Images.Close(),
            disabled: ko.computed(lifetimeManager, () => isLoading() || !isDirty() || saving()),
            onClick: discard
        }),
        Toolbar.ToolbarItems.createBasicButton(lifetimeManager, {
            label: ClientResources.Networking.PublicAccess.Toolbar.refreshText,
            icon: MsPortalFx.Base.Images.Refresh(),
            disabled: ko.computed(lifetimeManager, () => isLoading() || saving()),
            onClick: refresh
        }),
        FeedbackToolbarButton.create({
            container: container,
            lifetimeManager: lifetimeManager,
            bladeName: "NetworkSettingsBlade",
            featureName: `${options.kind}_Networking_ManagedWorkspaceOutbound`,
            cesQuestion: ClientResources.CES.ManagedWorkspaceOutbound.question,
            cvaQuestion:  ClientResources.CVA.ManagedWorkspaceOutbound.question,
        })

    ];

    return Toolbar.create(lifetimeManager, { items: toolbarItems, showLabels: true })
}