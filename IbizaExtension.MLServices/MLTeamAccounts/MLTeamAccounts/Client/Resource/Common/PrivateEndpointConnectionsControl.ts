import { BladeLink } from "Fx/Composition";
import { Container } from "Fx/Composition/TemplateBlade";
import * as CustomHtml from "Fx/Controls/CustomHtml";
import * as DropDown from "Fx/Controls/DropDown";
import * as InfoBox from "Fx/Controls/InfoBox";
import * as TextBox from "Fx/Controls/TextBox";
import * as Toolbar from "Fx/Controls/Toolbar";
import { LifetimeManager } from "Fx/Lifetime";
import * as ClientResources from "Resx/ClientResources";
import { PrivateEndpointConnection, PrivateLinkServiceConnectionStateStatus } from "../Data/PrivateEndpointConnectionsData";
import Utilities from "../../Shared/Utilities";
import { DataContext } from "../ResourceArea";
import { INetworkingControl } from "./Networking.types";
import * as PrivateEndpointConnectionCommands from "./PrivateEndpointConnectionCommands";
import {FeedbackToolbarButton} from "../../Shared/FeedbackToolbarButton";

import Grid = MsPortalFx.ViewModels.Controls.Lists.Grid;
import ResourceTypes = MsPortalFx.ViewModels.Services.ResourceTypes;
import { WorkspaceMappedKind } from "Shared/Enums";

const PrivateEndPointsResources = ClientResources.PrivateEndpoints;
const GridResources = PrivateEndPointsResources.Grid;

const log = MsPortalFx.Base.Diagnostics.createLog(require);
const enum ErrorCodes {
    UnknownStatus = 1
}

interface Options {
    id: string;
    kind: WorkspaceMappedKind;
}

interface GridItem {
    id: string;
    name: string;
    privateEndpointName: string;
    privateEndpointLink: BladeLink;
    statusDisplayName: string;
    status: PrivateLinkServiceConnectionStateStatus;
    description: string;
}

interface Context {
    container: Container;
    lifetimeManager: LifetimeManager;
}

export function create(context: Context, model: DataContext, options: Options): INetworkingControl {
    const connections = ko.observableArray<PrivateEndpointConnection>();

    const filterTextBox = TextBox.create(context.lifetimeManager, {
        ariaLabel: PrivateEndPointsResources.filterByName,
        placeHolderText: PrivateEndPointsResources.filterByName,
        suppressDirtyBehavior: true
    });

    const statusDropDown: DropDown.Contract<PrivateLinkServiceConnectionStateStatus> = DropDown.create<PrivateLinkServiceConnectionStateStatus>(context.lifetimeManager, {
        ariaLabel: PrivateEndPointsResources.filterByStatus,
        multiItemDisplayText: ko.pureComputed(() => {
            const selectedStatuses: PrivateLinkServiceConnectionStateStatus[] = statusDropDown.value() as any;
            const selectedCount = (selectedStatuses || []).length;
            const totalCount = (statusDropDown.items() || []).length;
            return !selectedCount || selectedCount === totalCount
                ? GridResources.StatusFilter.all
                : selectedStatuses.map(getStatusDisplayName).join(", ");
        }),
        suppressDirtyBehavior: true,
        multiselect: true,
        selectAll: true,
        showSearchIcon: true
    });

    const errorMessage = InfoBox.create(context.lifetimeManager, {
        style: InfoBox.Style.Error,
        visible: ko.observable(false),
        text: ko.observable("")
    });

    ko.pureComputed(() => {
        const statusCounts: StringMap<number> = {};
        connections().forEach(item => {
            const status = item.properties.privateLinkServiceConnectionState.status;
            statusCounts[status] = (statusCounts[status] || 0) + 1;
        });
        const allStatues = Object.keys(statusCounts) as PrivateLinkServiceConnectionStateStatus[];
        MsPortalFx.pushUnique(allStatues, ["Pending", "Approved", "Rejected", "Disconnected"]);
        return allStatues.map(key => {
            return {
                text: GridResources.StatusFilter.itemFormat.format(
                    getStatusDisplayName(key),
                    statusCounts[key] || 0),
                value: key
            };
        });
    }).subscribeAndRun(context.lifetimeManager, statusDropDown.items);

    const gridItems = ko.observableArray<GridItem>();
    connections.filter(context.lifetimeManager, item => {
        const allowedStatuses: string[] = statusDropDown.value() as any;
        return !(allowedStatuses && allowedStatuses.length) ||
            allowedStatuses.includes(item.properties.privateLinkServiceConnectionState.status);
    }).map(context.lifetimeManager, (itemLifetime, item): GridItem => {
        const connectionDescriptor = ResourceTypes.parseResourceDescriptor(item.id);
        const endpointId = item.properties.privateEndpoint.id;
        const endpointDescriptor = ResourceTypes.parseResourceDescriptor(endpointId);
        const status = item.properties.privateLinkServiceConnectionState.status;

        return {
            id: item.id,
            name: connectionDescriptor.resource,
            privateEndpointName: endpointDescriptor.resource,
            privateEndpointLink: {
                bladeReference: new FxImpl.Composition.Selectable.PdlBladeReference<any, void>(
                    "ResourceMenuBlade",
                    "HubsExtension",
                    {
                        parameters: {
                            id: endpointId
                        }
                    })
            },
            status,
            statusDisplayName: getStatusDisplayName(status),
            description: item.properties.privateLinkServiceConnectionState.description
        };
    }).subscribeAndRun(context.lifetimeManager, gridItems);

    const grid = new Grid.ViewModel<GridItem, GridItem>(
        context.lifetimeManager,
        gridItems,
        Grid.Extensions.Filterable | Grid.Extensions.SelectableRow,
        {
            filterable: {
                queryString: filterTextBox.value,
                valueUpdateDelayTimeout: ko.observable(500),
                searchBoxVisible: ko.observable(false)
            },
            selectableRow: {
                selectionMode: Grid.RowSelectionMode.MultipleToggle
            }
        });

    grid.ariaLabel(PrivateEndPointsResources.label);
    grid.columns([
        {
            itemKey: "name",
            name: ko.observable(PrivateEndPointsResources.GridColumns.name),
            width: ko.observable("20%")
        },
        {
            itemKey: "statusDisplayName",
            name: ko.observable(PrivateEndPointsResources.GridColumns.status),
            width: ko.observable("20%")
        },
        {
            itemKey: "privateEndpointName",
            name: ko.observable(PrivateEndPointsResources.GridColumns.privateEndpoint),
            width: ko.observable("20%"),
            format: Grid.Format.HtmlBindings,
            formatOptions: {
                htmlBindingsTemplate: `<a data-bind="fxclick: settings.item.privateEndpointLink, text: value"></a>`,
            }
        },
        {
            itemKey: "description",
            name: ko.observable(PrivateEndPointsResources.GridColumns.description)
        },
    ]);

    const refresh = (model: DataContext) => {
        errorMessage.visible(false);
        errorMessage.text("");

        grid.loading(true);
        return model.privateEndpointConnectionsData.getPrivateEndpointConnections(options.id).then(items => {
            connections(items);
        }).catch(error => {
            errorMessage.visible(true);
            errorMessage.text(Utilities.extractErrorMessage(error));
        }).finally(() => {
            grid.loading(false);
        });
    };

    const commands = PrivateEndpointConnectionCommands.create(context, model, {
        resourceId: options.id,
        selectedItems: grid.selectableData.selectedItems,
        refreshItems: refresh
    });

    const refreshCommand = Toolbar.ToolbarItems.createBasicButton(context.lifetimeManager, {
        label: ClientResources.PrivateEndpoints.Toolbar.refresh,
        icon: MsPortalFx.Base.Images.Refresh(),
        onClick: () => refresh(model),
        disabled: grid.loading
    });

    const feedbackCommand = FeedbackToolbarButton.create({
        container: context.container,
        lifetimeManager: context.lifetimeManager,
        bladeName: "NetworkSettingsBlade",
        featureName: `${options.kind}_Networking_PrivateEndpoints`,
        cesQuestion: ClientResources.CES.PrivateEndpoints.question,
        cvaQuestion:  ClientResources.CVA.PrivateEndpoints.question,
    });

    const toolbarItems = [
        commands.addEndpointCommand,
        commands.approveCommand,
        commands.rejectCommand,
        commands.deleteCommand,
        refreshCommand,
        feedbackCommand
    ];

    const toolbar = Toolbar.create(context.lifetimeManager, {
        items: toolbarItems,
        showLabels: true
    });

    const control = CustomHtml.create(context.lifetimeManager, {
        htmlTemplate: `
            <div data-bind="pcControl: errorMessage"></div>
            <div style="display: flex; margin-top: 15px;">
                <div data-bind="pcControl: filterTextBox" style="width: 220px; margin-right: 15px;"></div>
                <div data-bind="pcControl: statusDropDown" style="width: 220px;"></div>
            </div>
            <div class='ext-private-endpoint-grid' data-bind="pcControl: grid"></div>`,
        innerViewModel: {
            toolbar,
            filterTextBox,
            statusDropDown,
            errorMessage,
            grid
        }
    });

    return {
        control,
        load: refresh,
        toolbar
    };
}

const StatusResources = ClientResources.PrivateEndpoints.Status;
const statusDisplayNameMap: StringMap<string> = {
    Approved: StatusResources.approved,
    Pending: StatusResources.pending,
    Rejected: StatusResources.rejected,
    Disconnected: StatusResources.disconnected
};

function getStatusDisplayName(status: PrivateLinkServiceConnectionStateStatus): string {
    const displayName = statusDisplayNameMap[status];
    if (displayName) {
        return displayName;
    }

    log.error(status, ErrorCodes.UnknownStatus);
    return status;
}