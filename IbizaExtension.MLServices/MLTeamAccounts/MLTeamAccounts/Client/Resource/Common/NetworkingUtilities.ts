
import * as Section from "Fx/Controls/Section";
import { LifetimeManager } from "Fx/Lifetime";


import { PrivateEndpointDestination, OutboundRule, ServiceTagDestination, PrivateEndpointOutboundRule } from "Resource/Data/Data.Types";


export function createNetworkingSection(container: LifetimeManager, children: any[], visible?: KnockoutObservableBase<boolean>, css?: string): Section.Contract {
    return Section.create(container, {
        children: children,
        visible: visible,
        cssClass: css
    });
}

export function getOutboundRuleDestination(outboundRule: OutboundRule): string {
    if (outboundRule.type === "PrivateEndpoint") {
        const destination = outboundRule.destination as PrivateEndpointDestination;
        return `${destination.subresourceTarget}://${destination.serviceResourceId.startsWith("/") ? destination.serviceResourceId.substring(1) : destination.serviceResourceId
            }`;
    } else if (outboundRule.type === "ServiceTag") {
        const destination = outboundRule.destination as ServiceTagDestination;
        return `${destination.serviceTag}; Protocol: ${destination.protocol}; Port range: ${destination.portRanges};`;
    } else if (outboundRule.type === "FQDN") {
        return outboundRule.destination as string;
    }

    return "";
}

export function getOutboundRuleParentRuleName(outboundRule: OutboundRule): string {
    if (outboundRule.type === "PrivateEndpoint") {
        return (outboundRule as PrivateEndpointOutboundRule).parentRuleNames?.join(", ") || "";
    }
    return "";
}
