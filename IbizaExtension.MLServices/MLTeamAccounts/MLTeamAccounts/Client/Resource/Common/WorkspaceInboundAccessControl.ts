import { BladeClosedReason, BladeClosedWithDataHandler, BladeReferences } from "Fx/Composition";
import { ResourceGroup } from "Fx/Controls/BaseResourceDropDown";
import { DataContext } from "Resource/ResourceArea";
import * as DataGrid from "Fx/Controls/DataGrid";
import * as InfoBox from "Fx/Controls/InfoBox";
import * as RadioButton from "Fx/Controls/RadioButtons";
import * as Toolbar from "Fx/Controls/Toolbar";
import { Control, HtmlContent } from "Fx/Controls/ControlsBase";
import { LifetimeManager } from "Fx/Lifetime";
import { Container } from "Fx/Composition/TemplateBlade";
import Resources = require("Resx/ClientResources");

import { safeGet } from "../Create/Models/SafeGet";
import { createTextContent, createSectionHeader } from "../Create/Utilities/NoPdlUiHelpers";
import { machineLearningServicesResourcesProvider, machineLearningWorkspaces } from "../../Shared/Constants";
import { getPrivateDnsZoneName, getPrivateDnsZones } from "../../Shared/NetworkUtilities";

import ResourceTypes = MsPortalFx.ViewModels.Services.ResourceTypes;
import { NetworkingDataModel, ResourceDetails, WorkspaceNetworkSelectionOption } from "./NetworkingDataModel";
import * as PrivateEndpointsControl from "./PrivateEndpointConnectionsControl";
import { INetworkingControl } from "./Networking.types";
import { WorkspaceMappedKind } from "Shared/Enums";

const NetworkingResources = Resources.CreateBlade.Network;

const sort = (a: { id: string }, b: { id: string }) => {
    return MsPortalFx.localeCompareIgnoreCase(a.id, b.id);
};

function getAmlWorkspaceSubResource(): CreatePrivateEndpointMultiDnsContextBlade.SubResourceDetails {
    const defaultPrivateDnsZoneNames = "privatelink.api.azureml.ms,privatelink.notebooks.azure.net";
    const privateDnsZoneNames = (MsPortalFx.getEnvironmentValue("privateDnsZoneNames") || defaultPrivateDnsZoneNames).split(",");

    return {
        groupId: "amlworkspace",
        expectedPrivateDnsZoneNames: privateDnsZoneNames,
        subResourceDisplayName: "azuremlworkspace"
    };
}

interface PrivateEndpointConfigurationDataGridItem {
    displayName: string;
    displaySubscription: string;
    displayResourceGroup: string;
    displayRegion: string;
    displayTargetResourceType: string;
    displaySubnet: string;
    displayPrivateDnsZone: string;
    configuration: CreatePrivateEndpointMultiDnsContextBlade.Output;
}

export interface Options {
    networkingDataModel: NetworkingDataModel;
    resourceDetails: ResourceDetails;
    dataContext: DataContext;
    workspaceNetworkSelectionOption: KnockoutObservable<WorkspaceNetworkSelectionOption>;
    resourceId?: string;
    isCreationFlow?: boolean;
    isLoading: KnockoutObservable<boolean>;
    workspaceKind: WorkspaceMappedKind;
}

interface Context {
    container: Container;
    lifetimeManager: LifetimeManager;
}

export class WorkspaceInboundAccessControl {
    private _networkSelectionRadioButton: RadioButton.Contract<WorkspaceNetworkSelectionOption>;
    private _hasLocationError = ko.observable<boolean>(false);
    private _hasSKUError = ko.observable<boolean>(false);
    private _locationErrorMessage = ko.observable<string>("");
    private _SKUErrorMessage = ko.observable<string>("");
    private _grid: DataGrid.Contract<PrivateEndpointConfigurationDataGridItem>;
    private _eligibleSubResources: KnockoutObservableBase<CreatePrivateEndpointMultiDnsContextBlade.SubResourceDetails[]>;
    private _workspaceNetworkSelectionOption: KnockoutObservable<WorkspaceNetworkSelectionOption>;
    private _networkingDataModel: NetworkingDataModel;
    private _context: Context;
    private _dataContext: DataContext;
    private _resourceDetails: ResourceDetails;
    private _sectionItems: Array<Control | HtmlContent>;
    private _privateEndpointsControl: INetworkingControl;
    private _isCreationFlow: boolean;
    private _isLoading: KnockoutObservable<boolean>;

    constructor(context: Context, options: Options) {
        const { lifetimeManager } = context;
        const { networkingDataModel, dataContext, resourceId, isCreationFlow, isLoading } = options;
        this._context = context;
        this._workspaceNetworkSelectionOption = options.workspaceNetworkSelectionOption;
        this._networkingDataModel = networkingDataModel;
        this._isCreationFlow = isCreationFlow;
        this._isLoading = isLoading;
        this._dataContext = dataContext;
        this._resourceDetails = options.resourceDetails;

        this._eligibleSubResources = ko.pureComputed(() => [getAmlWorkspaceSubResource()]);

        this._sectionItems = [];

        this._sectionItems.push(createSectionHeader(NetworkingResources.workspaceInboundAccessTitle));

        if (this._isCreationFlow) {
            this._grid = this._createPrivateEndpointsDataGrid();
            this._sectionItems.push(this._createPrivateEndpointLocationError());
            this._sectionItems.push(this._grid);
            this._sectionItems.push(this._createPrivateEndpointsToolbar());
        } else {
            this._privateEndpointsControl = PrivateEndpointsControl.create(this._context, dataContext, { id: resourceId, kind: options.workspaceKind });
            this._sectionItems.push(this._privateEndpointsControl.toolbar);
            this._sectionItems.push(this._privateEndpointsControl.control);
        }
    }

    public getWorkspaceInboundAccessItems(): Array<Control | HtmlContent> {
        return this._sectionItems;
    }

    public load(): Q.Promise<any> {
        return Q.all([this._privateEndpointsControl?.load(this._dataContext)]);
    }

    public onLoad(): void {
        if (this._isCreationFlow) {
            this.validatePrivateEndpointLocation();
        }
    }

    public triggerValidation(): Q.Promise<boolean> {
        this.validatePrivateEndpointLocation();
        this._validateSKUPrivateEndpoint();
        return Q.resolve(!this._hasLocationError()) && Q.resolve(!this._hasSKUError());
    }

    private _createPrivateEndpointLocationError(): InfoBox.Contract {
        const { privateEndpointConfigurations } = this._networkingDataModel;

        const locationError = InfoBox.create(this._context.lifetimeManager, {
            style: InfoBox.Style.Error,
            text: this._locationErrorMessage,
            visible: this._hasLocationError
        });

        privateEndpointConfigurations.subscribe(this._context.lifetimeManager, () => {
            this.validatePrivateEndpointLocation();
        });

        return locationError;
    }

    private _createPrivateEndpointsDataGrid(): DataGrid.Contract<PrivateEndpointConfigurationDataGridItem> {
        const { privateEndpointConfigurations } = this._networkingDataModel;

        const columns: DataGrid.ColumnDefinition<PrivateEndpointConfigurationDataGridItem>[] = [
            DataGrid.createCustomLinkColumn({
                id: "displayName",
                header: NetworkingResources.PrivateEndpoints.name,
                cell: {
                    customLink: (item: PrivateEndpointConfigurationDataGridItem) => {
                        return {
                            text: item.displayName
                        };
                    },
                    onClick: item => {
                        this._context.container.closeContextBlade().finally(() => {
                            this._openCreatePrivateEndpoints(item.configuration);
                        });
                    }
                },
                width: "13%"
            }),
            DataGrid.createTextColumn({
                id: "displaySubscription",
                header: NetworkingResources.displaySubscription,
                cell: {
                    text: (item: PrivateEndpointConfigurationDataGridItem) => {
                        return item.displaySubscription;
                    }
                },
                width: "24%"
            }),
            DataGrid.createTextColumn({
                id: "displayResourceGroup",
                header: NetworkingResources.displayResourceGroup,
                cell: {
                    text: (item: PrivateEndpointConfigurationDataGridItem) => {
                        return item.displayResourceGroup;
                    }
                },
                width: "13%"
            }),
            DataGrid.createTextColumn({
                id: "displayRegion",
                header: NetworkingResources.displayRegion,
                cell: {
                    text: (item: PrivateEndpointConfigurationDataGridItem) => {
                        return item.displayRegion;
                    }
                },
                width: "13%"
            }),
            DataGrid.createTextColumn({
                id: "displaySubnet",
                header: NetworkingResources.displaySubnet,
                cell: {
                    text: (item: PrivateEndpointConfigurationDataGridItem) => {
                        return item.displaySubnet;
                    }
                },
                width: "13%"
            }),
            DataGrid.createTextColumn({
                id: "displayPrivateDnsZone",
                header: NetworkingResources.displayPrivateDnsZone,
                cell: {
                    text: (item: PrivateEndpointConfigurationDataGridItem) => {
                        return item.displayPrivateDnsZone;
                    }
                },
                width: "24%"
            })
        ];

        const grid = DataGrid.create<PrivateEndpointConfigurationDataGridItem>(this._context.lifetimeManager, {
            header: {
                visible: true
            },
            noDataMessage: {
                htmlTemplate: `<div class='ext-networkingtab-privateendpointclicktoadd' data-bind='text: text'></div>`,
                viewModel: {
                    text: NetworkingResources.PrivateEndpoints.clickToAdd
                }
            },
            ariaLabel: NetworkingResources.PrivateEndpoints.title,
            columns: columns,
            selection: {
                selectionMode: DataGrid.SelectionMode.Single
            },
            dataSource: () => {
                const endpoints = privateEndpointConfigurations();
                const gridItems = endpoints.map(endpoint => {
                    return buildGridItem(endpoint);
                });

                gridItems.sort(sort);

                return gridItems;
            },
            contextMenu: {
                supplyButtonCommands: (lifetime, row, grid) => {
                    return [this._createGridItemDeleteCommand(lifetime, row, grid)];
                },
                canShowMenu: () => false
            }
        });

        return grid;
    }

    private _createGridItemDeleteCommand(
        lifetime: MsPortalFx.Base.LifetimeManager,
        row: DataGrid.Row<PrivateEndpointConfigurationDataGridItem>,
        grid: DataGrid.Contract<PrivateEndpointConfigurationDataGridItem>
    ): Toolbar.ToolbarItems.BasicButtonContract {
        const { privateEndpointConfigurations } = this._networkingDataModel;

        return Toolbar.ToolbarItems.createBasicButton(lifetime, {
            label: Resources.Commands.delete,
            icon: MsPortalFx.Base.Images.Delete(),
            onClick: () => {
                const configuration = safeGet(row, "item", "configuration");
                if (configuration) {
                    privateEndpointConfigurations.remove(configuration);
                    grid.refresh();
                }
            }
        });
    }

    private _createPrivateEndpointsToolbar(): Toolbar.Contract {
        const { privateEndpointConfigurations } = this._networkingDataModel;

        return Toolbar.create(this._context.lifetimeManager, {
            showLabels: true,
            items: [
                Toolbar.ToolbarItems.createBasicButton(this._context.lifetimeManager, {
                    label: NetworkingResources.PrivateEndpoints.add,
                    icon: MsPortalFx.Base.Images.Add(),
                    onClick: () => {
                        return this._openCreatePrivateEndpoints();
                    },
                    disabled: ko.pureComputed(() => {
                        const privateEndpoints = privateEndpointConfigurations() || [];
                        const groups = this._eligibleSubResources() || [];

                        // For now, we limit private endpoints to 1
                        return privateEndpoints.length > 0 || !groups.length || this._isLoading();
                    })
                })
            ]
        });
    }

    private _openCreatePrivateEndpoints(configuration?: CreatePrivateEndpointMultiDnsContextBlade.Output): Promise<boolean> {
        const parameters = this._getCreatePrivateEndpointParameters(configuration);
        const onClosed = this._getCreatePrivateEndpointOnClosed(configuration);

        return this._context.container.openContextPane(
            BladeReferences.forExtension("Microsoft_Azure_Network").forBlade("CreatePrivateEndpointMultiDnsContextBlade").createReference({
                parameters,
                onClosed
            })
        );
    }

    private _getCreatePrivateEndpointParameters(
        configuration?: CreatePrivateEndpointMultiDnsContextBlade.Output
    ): CreatePrivateEndpointMultiDnsContextBlade.Parameters {
        const { privateEndpointConfigurations } = this._networkingDataModel;
        const { subscription, location, resourceGroup, workspaceName } = this._resourceDetails;

        const otherPrivateEndpointIds: string[] = [];

        const idToIgnore = safeGet(configuration, "privateEndpointConfiguration", "privateEndpoint", "id");
        const existingConfigurations = privateEndpointConfigurations() || [];
        existingConfigurations.forEach(endpoint => {
            const privateEndpointId = safeGet(endpoint, "privateEndpointConfiguration", "privateEndpoint", "id");
            if (privateEndpointId && !(idToIgnore?.toLowerCase() === privateEndpointId?.toLowerCase())) {
                otherPrivateEndpointIds.push(privateEndpointId);
            }
        });

        const groups = this._eligibleSubResources();
        const parameters: CreatePrivateEndpointMultiDnsContextBlade.Parameters = {
            subResources: groups,
            subResourceLabel: NetworkingResources.PrivateEndpoints.subResourceLabel,
            subResourceHelp: NetworkingResources.PrivateEndpoints.subResourceHelp,
            otherPrivateEndpointIds,
            privateLinkServiceId: createResourceId(subscription().subscriptionId, resourceGroup().value.name, workspaceName())
        };

        if (configuration) {
            if (configuration.privateEndpointConfiguration) {
                parameters.privateEndpointConfiguration = configuration.privateEndpointConfiguration;
            }

            if (configuration.networkingConfiguration) {
                parameters.networkingConfiguration = configuration.networkingConfiguration;
            }

            if (configuration.privateDnsZoneConfiguration) {
                parameters.privateDnsZoneConfiguration = configuration.privateDnsZoneConfiguration;
            }
        } else {
            parameters.privateEndpointConfiguration = {
                subscription: subscription?.(),
                location: <CommonNetworkTypes.Location>location?.(),
                resourceGroup: resourceGroup?.(),
                subResource: getAmlWorkspaceSubResource()
            };
        }

        return parameters;
    }

    private _getCreatePrivateEndpointOnClosed(
        configuration?: CreatePrivateEndpointMultiDnsContextBlade.Output
    ): BladeClosedWithDataHandler<CreatePrivateEndpointMultiDnsContextBlade.Output> {
        const { privateEndpointConfigurations } = this._networkingDataModel;

        return (reason: BladeClosedReason, data: CreatePrivateEndpointMultiDnsContextBlade.Output) => {
            if (
                reason === BladeClosedReason.ChildClosedSelf &&
                data &&
                data.networkingConfiguration &&
                data.privateEndpointConfiguration &&
                data.privateDnsZoneConfiguration
            ) {
                privateEndpointConfigurations.remove(configuration);
                privateEndpointConfigurations.push(data);
            }

            this._grid.refresh({ reset: true });
            this._networkSelectionRadioButton.triggerValidation();
        };
    }

    public validatePrivateEndpointLocation(): void {
        if (!this._isCreationFlow) {
            return;
        }
        const { location } = this._resourceDetails;
        const { privateEndpointConfigurations } = this._networkingDataModel;

        let errorMessage = "";
        let isValid = false;
        if (
            !(
                this._workspaceNetworkSelectionOption() === WorkspaceNetworkSelectionOption.Private ||
                this._workspaceNetworkSelectionOption() === WorkspaceNetworkSelectionOption.PrivateApprovedOutbound
            ) ||
            !location() ||
            privateEndpointConfigurations().length === 0
        ) {
            isValid = true;
        } else {
            const inValidPrivateEndpoint = privateEndpointConfigurations().filter(pe => {
                if (pe.privateEndpointConfiguration && pe.privateEndpointConfiguration.location) {
                    return MsPortalFx.localeCompareIgnoreCase(pe.privateEndpointConfiguration.location.name, location().name) !== 0;
                }

                return true;
            })[0];

            if (inValidPrivateEndpoint) {
                isValid = false;
                errorMessage = NetworkingResources.PrivateEndpoints.locationMismatch.format(
                    location().name,
                    inValidPrivateEndpoint.privateEndpointConfiguration!.location!.name
                );
            } else {
                isValid = true;
            }
        }

        this._hasLocationError(!isValid);
        this._locationErrorMessage(errorMessage);
    }

    private _validateSKUPrivateEndpoint(): void {
        const { containerRegistry } = this._resourceDetails;

        let errorMessage = "";
        let isValid = true;
        if (
            (this._workspaceNetworkSelectionOption() === WorkspaceNetworkSelectionOption.Private ||
                this._workspaceNetworkSelectionOption() === WorkspaceNetworkSelectionOption.PrivateApprovedOutbound) &&
            containerRegistry() &&
            containerRegistry().sku &&
            containerRegistry().sku !== "Premium"
        ) {
            isValid = false;
            errorMessage = NetworkingResources.ConnectivityMethod.skuPrivateEndpointErrorMessage;
        }

        this._hasSKUError(!isValid);
        this._SKUErrorMessage(errorMessage);
    }
}

function buildGridItem(
    configuration: CreatePrivateEndpointMultiDnsContextBlade.Output
): DataGrid.DataNode<PrivateEndpointConfigurationDataGridItem, string> {
    const privateEndpointConfiguration = safeGet(configuration, "privateEndpointConfiguration");
    const networkingConfiguration = safeGet(configuration, "networkingConfiguration");
    const privateDnsZoneConfiguration = safeGet(configuration, "privateDnsZoneConfiguration");
    const resourceGroup = safeGet(privateEndpointConfiguration, "resourceGroup");
    const resourceGroupName = safeGet(resourceGroup, "value", "name");
    const subResourceDetails = safeGet(privateEndpointConfiguration, "subResource");

    const dnsZones = getPrivateDnsZones(configuration);
    const dnsZoneName = dnsZones
        .map(dns => (dns.isNew ? Resources.newResourceFormatCaps.format(getPrivateDnsZoneName(dns)) : getPrivateDnsZoneName(dns)))
        .join(", ");

    const gridItem: PrivateEndpointConfigurationDataGridItem = {
        displayName: safeGet(privateEndpointConfiguration, "privateEndpoint", "name"),
        displaySubscription: safeGet(privateEndpointConfiguration, "subscription", "displayName"),
        displayResourceGroup:
            resourceGroup.mode === ResourceGroup.Mode.CreateNew ? Resources.newResourceFormatCaps.format(resourceGroupName) : resourceGroupName,
        displayRegion: safeGet(privateEndpointConfiguration, "location", "displayName"),
        displayTargetResourceType: safeGet(subResourceDetails, "subResourceDisplayName") || safeGet(subResourceDetails, "groupId"),
        displaySubnet: safeGet(networkingConfiguration, "subnetValue", "text"),
        displayPrivateDnsZone: safeGet(privateDnsZoneConfiguration, "integrateWithPrivateDnsZone")
            ? dnsZoneName
            : NetworkingResources.PrivateEndpoints.noContent,
        configuration: configuration
    };

    return {
        id: configuration.privateEndpointConfiguration.privateEndpoint.id,
        item: gridItem
    };
}

function createResourceId(subscriptionId: string, resourceGroupName: string, name: string) {
    return `/subscriptions/${subscriptionId}/resourceGroups/${resourceGroupName}/providers/${machineLearningServicesResourcesProvider}/${machineLearningWorkspaces}/${name}`;
}
