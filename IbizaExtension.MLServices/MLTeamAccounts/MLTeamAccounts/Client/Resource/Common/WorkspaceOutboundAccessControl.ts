import { BladeClosedReason, BladeReferences } from "Fx/Composition";
import { DataContext } from "Resource/ResourceArea";
import * as CheckBox from "Fx/Controls/CheckBox";
import * as DataGrid from "Fx/Controls/DataGrid";
import * as InfoBox from "Fx/Controls/InfoBox";
import * as InfoBalloon from "Fx/Controls/InfoBalloon";
import * as RadioButton from "Fx/Controls/RadioButtons";
import * as Section from "Fx/Controls/Section";
import * as TextBlock from "Fx/Controls/TextBlock";
import * as Toggle from "Fx/Controls/Toggle";
import * as Toolbar from "Fx/Controls/Toolbar";
import { LifetimeManager } from "Fx/Lifetime";
import Resources = require("Resx/ClientResources");

import { DocLinkIds, getDocumentUrl } from "../../Shared/DocLinks";
import { createSectionHeader, createLink } from "../Create/Utilities/NoPdlUiHelpers";

import FxViewModels = MsPortalFx.ViewModels;
import { Control, HtmlContent } from "Fx/Controls/ControlsBase";
import { ManagedNetworkDefaultRulesResponse } from "Resource/Data/WorkspaceOutboundAccessData";
import { RuleCategory, NetworkIsolationMode, FirewallSku } from "Resource/Data/Data.Types";
import {
    OutboundAccessRuleSettingsBladeParameters,
    OutboundAccessRuleSettingsBladeReturnData
} from "Resource/Blades/OutboundAccessRules/OutboundAccessRuleSettingsBlade";
import { ManagedNetworkRuleItem, NetworkingDataModel, ResourceDetails, ManagedNetworkIsolationMode } from "./NetworkingDataModel";
import MsPortalFxTemplateBlade = require("Fx/Composition/TemplateBlade");
import { WorkspaceMappedKind } from "../../Shared/Enums";
import { createNetworkingSection, getOutboundRuleDestination } from "./NetworkingUtilities";
import { getUniqueId } from "Fx";
import { AdvancedDataModel } from "Resource/Create/ViewModels/CreateMachineLearningModels";

const NetworkingResources = Resources.CreateBlade.Network;

const sort = (a: { id: string }, b: { id: string }) => {
    return MsPortalFx.localeCompareIgnoreCase(a.id, b.id);
};

interface Context {
    lifetimeManager: LifetimeManager;
    container: MsPortalFxTemplateBlade.Container;
}

export interface Options {
    networkingDataModel: NetworkingDataModel;
    resourceDetails: ResourceDetails;
    dataContext: DataContext;
    advancedDataModel?: AdvancedDataModel;
    managedNetworkIsolationMode: KnockoutObservable<ManagedNetworkIsolationMode>;
    isCreationFlow?: boolean;
    loadApiRules?: boolean;
    workspaceKind?: WorkspaceMappedKind;
    isLoading: KnockoutObservable<boolean>;
    setErrorMessage?: (message: string) => void;
}

export class WorkspaceOutboundAccessControl {
    private _managedNetworkDefaultRulesResponse = ko.observable<ManagedNetworkDefaultRulesResponse>();
    private _managedNetworkDefaultRulesLoaded = ko.observable<boolean>(false);
    private _managedNetworkIsolationMode: KnockoutObservable<ManagedNetworkIsolationMode>;
    private _workspaceOutboundAccessGrid: DataGrid.Contract<ManagedNetworkRuleItem>;
    private _networkingDataModel: NetworkingDataModel;
    private _dataContext: DataContext;
    private _resourceDetails: ResourceDetails;
    private _controls: Array<Control | HtmlContent>;
    private _isLoading: KnockoutObservable<boolean>;
    private _isCreationFlow: boolean;
    private _loadExistingRulesOnly: KnockoutComputed<boolean>;
    private _context: Context;
    private _workspaceKind: WorkspaceMappedKind;
    private _advancedDataModel: AdvancedDataModel;
    private _showEnabledColumn: KnockoutObservable<boolean> = ko.observable(false);
    private _setErrorMessage: (message: string) => void;

    public workspaceOutboundAccessGrid: DataGrid.Contract<ManagedNetworkRuleItem>;

    // tslint:disable:next-line
    private _rulesObserver: KnockoutComputed<unknown>;

    constructor(context: Context, options: Options) {
        const { lifetimeManager } = context;
        const { networkingDataModel, isLoading, isCreationFlow, workspaceKind, advancedDataModel } = options;
        this._context = context;
        this._workspaceKind = workspaceKind;
        this._networkingDataModel = networkingDataModel;
        this._dataContext = options.dataContext;
        this._resourceDetails = options.resourceDetails;
        this._isLoading = isLoading;
        this._isCreationFlow = isCreationFlow;
        this._managedNetworkIsolationMode = options.managedNetworkIsolationMode;
        this._advancedDataModel = advancedDataModel;
        this._setErrorMessage = options.setErrorMessage;

        this._loadExistingRulesOnly = ko.pureComputed(() => {
            if (this._isCreationFlow) {
                return false;
            }
            return this._managedNetworkIsolationMode() === this._networkingDataModel.initialData().managedNetworkIsolationMode;
        });

        this._managedNetworkIsolationMode.subscribe(lifetimeManager, () => this._updateNetworkingConnectivityModel());
        this._workspaceOutboundAccessGrid = this._createWorkspaceOutboundAccessDataGrid();
        networkingDataModel.managedNetworkOutboundAccessRules.subscribe(lifetimeManager, rules => {
            this._workspaceOutboundAccessGrid.refresh({
                reset: true,
                params: {
                    rules
                }
            });
        });

        // This computed function is used to subscribe to multiple observables which may affect the rules state
        // It is intended to be similar to using useEffect/setState in react.
        // Assigned to _rulesObserver simply so it is not garbage collected.
        this._rulesObserver = ko.explicitComputed(
            lifetimeManager,
            [
                this._managedNetworkDefaultRulesResponse,
                networkingDataModel.networkIsolationMode,
                this._loadExistingRulesOnly,
                networkingDataModel.initialData().outboundRules,
                networkingDataModel.managedNetworkOutboundAccessRules
            ],
            (response, isolationMode, loadExistingRulesOnly, _initialRules, _accessRules) => {
                // update outboundAccessRules based on api rules response, existingOutboundAccessRules and isolation mode
                const existingOutboundAccessRules = networkingDataModel.initialData().outboundRules();
                const currentOutboundRules = networkingDataModel.managedNetworkOutboundAccessRules();
                this._showEnabledColumn(existingOutboundAccessRules.some(rule => rule.ruleCategory === "Recommended"));
                const rules = refreshOutboundAccessRulesFromResponse(
                    response,
                    existingOutboundAccessRules,
                    currentOutboundRules,
                    isolationMode,
                    loadExistingRulesOnly
                );
                networkingDataModel.managedNetworkOutboundAccessRules(rules);
            }
        );

        this.workspaceOutboundAccessGrid = this._workspaceOutboundAccessGrid;
        this._loadManagedNetworkApiRules();

        this._controls = [];
        if (this._isCreationFlow) {
            this._controls.push(
                ...[
                    createSectionHeader(NetworkingResources.workspaceOutboundAccessTitle),
                    this._createInternetOutboundInfoSection()
                ]
            );
        }
        this._controls.push(...[this._createAzureFirewallSkuSection(), this._createApprovedOutboundInfoSection()]);

        if (this._isCreationFlow) {
            this._controls.push(this._createProvisionManageNetworkCheckBox());
        }

        this._controls.push(this._createManagedNetworkOutboundAccessRulesSection());
    }

    public getControls(): Array<Control | HtmlContent> {
        return this._controls;
    }

    private _updateNetworkingConnectivityModel() {
        // Update network connectivity method and isolation mode states
        if (this._managedNetworkIsolationMode() === ManagedNetworkIsolationMode.AllowOnlyApprovedOutbound) {
            // we force isolation mode to "AllowOnlyApprovedOutbound" regardless of _workspaceInternetOutboundAccessType value
            this._networkingDataModel.networkIsolationMode("AllowOnlyApprovedOutbound");
        } else if (this._managedNetworkIsolationMode() === ManagedNetworkIsolationMode.AllowInternetOutbound) {
            this._networkingDataModel.networkIsolationMode("AllowInternetOutbound");
        } else {
            // we force isolation mode to "Disabled" regardless of _workspaceInternetOutboundAccessType value.
            this._networkingDataModel.networkIsolationMode("Disabled");
        }
    }

    private _openOutboundRuleSettingsBlade(
        parameters: OutboundAccessRuleSettingsBladeParameters = {
            networkIsolationMode: this._managedNetworkIsolationMode()
        }
    ): Promise<boolean> {
        return this._context.container.openContextPane(
            BladeReferences.forBlade("OutboundAccessRuleSettingsBlade").createReference({
                parameters,
                onClosed: (_: BladeClosedReason, data: OutboundAccessRuleSettingsBladeReturnData) => {
                    if (data) {
                        const rules = this._networkingDataModel.managedNetworkOutboundAccessRules();
                        let updatedRules = [...rules];
                        let indexOfEditedRule = updatedRules.findIndex(rule => rule.name === parameters.name);
                        if (indexOfEditedRule !== -1) {
                            updatedRules[indexOfEditedRule] = {
                                ...updatedRules[indexOfEditedRule],
                                name: data.name,
                                outboundRule: data.outboundRule,
                                destinationType: data.outboundRule.type,
                                destination: getOutboundRuleDestination(data.outboundRule)
                            };
                        } else {
                            updatedRules.push({
                                active: ko.observable(true),
                                name: data.name,
                                outboundRule: data.outboundRule,
                                destinationType: data.outboundRule.type,
                                destination: getOutboundRuleDestination(data.outboundRule),
                                ruleCategory: "UserDefined"
                            });
                        }
                        this._networkingDataModel.managedNetworkOutboundAccessRules(updatedRules);
                    }
                }
            })
        );
    }

    private _createProvisionManageNetworkCheckBox(): Section.Contract {
        const options: CheckBox.Options = {
            label: NetworkingResources.WorkspaceOutboundAccess.provisionManagedVirtualNetwork,
            labelOnRight: true,
            value: this._networkingDataModel.provisionManagedNetwork,
            infoBalloonContent: NetworkingResources.WorkspaceOutboundAccess.provisionManagedVirtualNetworkInfo
        };

        const checkbox = CheckBox.create(this._context.container, options);

        return createNetworkingSection(
            this._context.lifetimeManager,
            [checkbox],
            ko.pureComputed(
                () =>
                    this._managedNetworkIsolationMode() == ManagedNetworkIsolationMode.AllowInternetOutbound ||
                    this._managedNetworkIsolationMode() == ManagedNetworkIsolationMode.AllowOnlyApprovedOutbound
            )
        );
    }

    private _createManagedNetworkOutboundAccessRulesSection(): Section.Contract {
        const rulesLoaded = ko.pureComputed(() => !!this._managedNetworkDefaultRulesResponse());

        const showConfigurationMessage = ko.pureComputed(() => {
            if (!this._isCreationFlow) {
                return false;
            } else {
                if (this._advancedDataModel && this._advancedDataModel.managedKeyVaultType() === "managedKeyVault") {
                    return false;
                }
                return !this._managedNetworkDefaultRulesResponse();
            }
        });

        return createNetworkingSection(
            this._context.lifetimeManager,
            [
                createNetworkingSection(
                    this._context.lifetimeManager,
                    [this._createWorkspaceOutboundAccessToolbarSection(), this._workspaceOutboundAccessGrid],
                    this._managedNetworkDefaultRulesLoaded
                ),
                createNetworkingSection(
                    this._context.lifetimeManager,
                    [
                        InfoBox.createInline(this._context.lifetimeManager, {
                            text: NetworkingResources.outboundRulesNotLoadedMessage,
                            style: InfoBox.Style.Error
                        })
                    ],
                    ko.pureComputed(() => showConfigurationMessage())
                )
            ],
            ko.pureComputed(
                () =>
                    this._managedNetworkIsolationMode() == ManagedNetworkIsolationMode.AllowInternetOutbound ||
                    this._managedNetworkIsolationMode() == ManagedNetworkIsolationMode.AllowOnlyApprovedOutbound
            )
        );
    }

    private _createInternetOutboundInfoSection(): Section.Contract {
        return createNetworkingSection(
            this._context.lifetimeManager,
            [
                createLink({
                    text: NetworkingResources.WorkspaceInternetOutbound.targetDescription,
                    linkUri: getDocumentUrl(DocLinkIds.VirtualNetworkManagedByAMLLearnMore),
                    linkText: NetworkingResources.WorkspaceInternetOutbound.targetLearnMoreLink,
                    useIntroStyling: true
                })
            ],
            ko.pureComputed(() => {
                return (
                    this._managedNetworkIsolationMode() === ManagedNetworkIsolationMode.AllowInternetOutbound &&
                    this._workspaceKind == WorkspaceMappedKind.Hub
                );
            })
        );
    }

    private _createApprovedOutboundInfoSection(): Section.Contract {
        return createNetworkingSection(
            this._context.lifetimeManager,
            [
                createLink({
                    text:
                        this._workspaceKind === WorkspaceMappedKind.Hub
                            ? NetworkingResources.WorkspaceApprovedOutbound.OutboundAccess.hubRequiredTargetDescription
                            : NetworkingResources.WorkspaceApprovedOutbound.OutboundAccess.requiredTargetDescription,
                    linkUri: getDocumentUrl(
                        this._workspaceKind === WorkspaceMappedKind.Hub
                            ? DocLinkIds.HubManagedNetworkIsolationRequiredTarget
                            : DocLinkIds.ManagedNetworkIsolationRequiredTarget
                    ),
                    linkText: NetworkingResources.WorkspaceApprovedOutbound.OutboundAccess.requiredTargetLearnMore
                }),
                createLink({
                    text:
                        this._workspaceKind === WorkspaceMappedKind.Hub
                            ? NetworkingResources.WorkspaceApprovedOutbound.OutboundAccess.hubRecommendedTargetDescription
                            : NetworkingResources.WorkspaceApprovedOutbound.OutboundAccess.recommendedTargetDescription,
                    linkUri: getDocumentUrl(DocLinkIds.ManagedNetworkIsolationRecommendedTarget),
                    linkText: NetworkingResources.WorkspaceApprovedOutbound.OutboundAccess.recommendedTargetLearnMore,
                    useIntroStyling: true
                })
            ],
            ko.pureComputed(() => {
                return this._managedNetworkIsolationMode() === ManagedNetworkIsolationMode.AllowOnlyApprovedOutbound;
            })
        );
    }

    private _createAzureFirewallSkuSection(): Section.Contract {
        const items: RadioButton.Item<FirewallSku>[] = [
            {
                text: "Standard",
                value: "Standard"
            },
            {
                text: "Basic",
                value: "Basic"
            }
        ];

        const firewallSkuRadioButton = RadioButton.create<FirewallSku>(this._context.lifetimeManager, {
            items: ko.observableArray(items),
            value: this._networkingDataModel.firewallSku,
            singleItemPerLine: false,
            disabled: ko.pureComputed(() => {
                return this._isLoading();
            }),
            suppressDirtyBehavior: false,
            validations: [new FxViewModels.RequiredValidation()]
        });

        return createNetworkingSection(
            this._context.lifetimeManager,
            [
                createSectionHeader(
                    NetworkingResources.WorkspaceApprovedOutbound.OutboundAccess.azureFirewallSku,
                    ko.pureComputed(() => {
                        return this._managedNetworkIsolationMode() === ManagedNetworkIsolationMode.AllowOnlyApprovedOutbound;
                    }),
                    false
                ),
                createLink({
                    text: NetworkingResources.WorkspaceApprovedOutbound.OutboundAccess.azureFirewallSkuDescription,
                    linkUri:
                        this._workspaceKind === WorkspaceMappedKind.Hub
                            ? getDocumentUrl(DocLinkIds.AzureFirewallSkuAIStudio)
                            : getDocumentUrl(DocLinkIds.AzureFirewallSkuAML),
                    linkText: NetworkingResources.WorkspaceApprovedOutbound.OutboundAccess.azureFirewallSkuPricingText,
                    useIntroStyling: true
                }),
                firewallSkuRadioButton
            ],
            ko.pureComputed(() => {
                return this._managedNetworkIsolationMode() === ManagedNetworkIsolationMode.AllowOnlyApprovedOutbound;
            }),
            "ext-azure-firewall-section"
        );
    }

    private _createWorkspaceOutboundAccessToolbarSection(): Section.Contract {
        const toolbar = Toolbar.create(this._context.lifetimeManager, {
            showLabels: true,
            items: [
                Toolbar.ToolbarItems.createBasicButton(this._context.lifetimeManager, {
                    label: NetworkingResources.WorkspaceOutboundAccess.addUserDefinedOutboundRules,
                    icon: MsPortalFx.Base.Images.Add(),
                    onClick: () => {
                        return this._openOutboundRuleSettingsBlade({
                            gridRules: this._networkingDataModel.managedNetworkOutboundAccessRules().map(rule => rule.name),
                            networkIsolationMode: this._managedNetworkIsolationMode()
                        });
                    }
                })
            ]
        });

        return createNetworkingSection(
            this._context.lifetimeManager,
            [toolbar],
            ko.pureComputed<boolean>(
                () =>
                    this._managedNetworkIsolationMode() === ManagedNetworkIsolationMode.AllowInternetOutbound ||
                    this._managedNetworkIsolationMode() === ManagedNetworkIsolationMode.AllowOnlyApprovedOutbound
            )
        );
    }

    private _createWorkspaceOutboundAccessDataGrid(): DataGrid.Contract<ManagedNetworkRuleItem, RuleCategory> {
        const columns: DataGrid.ColumnDefinition<ManagedNetworkRuleItem>[] = [
            DataGrid.createCustomLinkColumn({
                id: "displayName",
                header: NetworkingResources.WorkspaceOutboundAccess.connectionName,
                cell: {
                    customLink: (item: ManagedNetworkRuleItem) => {
                        return {
                            text: item.name
                        };
                    },
                    onClick: item => {
                        this._context.container.closeContextBlade().finally(() => {
                            this._openOutboundRuleSettingsBlade({
                                name: item.name,
                                outboundRule: item.outboundRule,
                                ruleCategory: item.ruleCategory,
                                networkIsolationMode: this._managedNetworkIsolationMode(),
                                isExisting: item.isExisting
                            });
                        });
                    }
                },
                width: "20%"
            }),
            DataGrid.createTemplateColumn({
                id: "activeToggle",
                header: "Enabled",
                cell: {
                    template: `<div data-bind='pcControl: toggle' />`,
                    viewModel: (item, lifeTime) => {
                        return {
                            toggle: Toggle.create(lifeTime, {
                                value: item.active,
                                disabled: item.ruleCategory !== "Recommended"
                            })
                        };
                    }
                },
                width: "10%",
                visible: this._showEnabledColumn
            }),
            DataGrid.createTemplateColumn({
                id: "displayStatus",
                header: NetworkingResources.WorkspaceOutboundAccess.status,
                cell: {
                    template: `<div>
                                <span data-bind="pcControl: statusText" style="display: inline"></span>
                                <span data-bind="pcControl: statusInfoBallon" style="display: inline-block"></span>
                              </div>`,
                    viewModel: (item, lifeTime) => {
                        return {
                            statusText: TextBlock.create(lifeTime, {
                                text: item.outboundRule.status ?? "Inactive"
                            }),
                            statusInfoBallon: InfoBalloon.create(lifeTime, {
                                content:
                                    item.outboundRule.status === "Active"
                                        ? NetworkingResources.WorkspaceOutboundAccess.activeStatusMessage
                                        : item.destinationType === "PrivateEndpoint"
                                        ? NetworkingResources.WorkspaceOutboundAccess.inactivePrivateEndpointStatusMessage
                                        : NetworkingResources.WorkspaceOutboundAccess.inactiveStatusMessage
                            })
                        };
                    }
                },
                width: "10%"
            }),
            DataGrid.createTextColumn({
                id: "parentRules",
                header: NetworkingResources.WorkspaceOutboundAccess.parentRules,
                cell: {
                    text: (item: ManagedNetworkRuleItem) => {
                        return item.parentRuleNames ?? "";
                    }
                },
                width: "10%",
                visible: ko.pureComputed(() => {
                    return !this._isCreationFlow;
                })
            }),
            DataGrid.createTextColumn({
                id: "displayDestinationType",
                header: NetworkingResources.WorkspaceOutboundAccess.destinationType,
                cell: {
                    text: (item: ManagedNetworkRuleItem) => {
                        return item.destinationType;
                    }
                },
                width: "15%"
            }),
            DataGrid.createTextColumn({
                id: "displayDestination",
                header: NetworkingResources.WorkspaceOutboundAccess.destination,
                cell: {
                    text: (item: ManagedNetworkRuleItem) => {
                        return item.destination;
                    }
                },
                width: "40%"
            })
        ];

        const grid = DataGrid.create<ManagedNetworkRuleItem, RuleCategory>(this._context.lifetimeManager, {
            header: {
                visible: true
            },
            noDataMessage:
                this._managedNetworkIsolationMode() === ManagedNetworkIsolationMode.AllowInternetOutbound
                    ? NetworkingResources.outboundRulesPublicInternetEnabledMessage
                    : NetworkingResources.outboundRulesGridEmptyMessage,
            ariaLabel: NetworkingResources.workspaceOutboundAccessTitle,
            columns: columns,
            visible: ko.pureComputed<boolean>(
                () =>
                    this._managedNetworkIsolationMode() === ManagedNetworkIsolationMode.AllowInternetOutbound ||
                    this._managedNetworkIsolationMode() === ManagedNetworkIsolationMode.AllowOnlyApprovedOutbound
            ),
            disabled: ko.pureComputed(() => {
                return this._isLoading();
            }),
            hierarchy: {
                supplyInitialExpansionForGroup: (cachedExpanded: boolean, defaultExpanded: boolean, group: RuleCategory) => {
                    return group === "UserDefined" || group === "Recommended";
                }
            },
            rowGroup: {
                content: this._getRowGroup
            },
            dataSource: ({ params }: { params: { rules: ManagedNetworkRuleItem[] } }) => {
                const nodes = buildOutboundAccessRulesGridNodes(params.rules);
                nodes.sort(sort);
                return nodes;
            },
            contextMenu: {
                supplyButtonCommands: (lifetime, row, grid) => {
                    const deleteButton = this._createWorkspaceOutboundAccessGridItemDeleteCommand(lifetime, row, grid);
                    return deleteButton ? [deleteButton] : [];
                },
                canShowMenu: () => false
            }
        });

        return grid;
    }

    private _createWorkspaceOutboundAccessGridItemDeleteCommand(
        lifetime: MsPortalFx.Base.LifetimeManager,
        row: DataGrid.Row<ManagedNetworkRuleItem>,
        grid: DataGrid.Contract<ManagedNetworkRuleItem, RuleCategory>
    ): Toolbar.ToolbarItems.BasicButtonContract {
        if (row.item.ruleCategory === "UserDefined") {
            return Toolbar.ToolbarItems.createBasicButton(lifetime, {
                label: Resources.Commands.delete,
                icon: MsPortalFx.Base.Images.Delete(),
                onClick: () => {
                    const updatedRules = this._networkingDataModel.managedNetworkOutboundAccessRules().filter(rule => rule.name !== row.item.name);
                    this._networkingDataModel.managedNetworkOutboundAccessRules(updatedRules);
                }
            });
        }
        return null;
    }

    private _getRowGroup(category: RuleCategory, _rowGroupLifetime: MsPortalFx.Base.LifetimeManager): string {
        return category === "Required"
            ? NetworkingResources.WorkspacePrivateOutbound.requiredOutboundRules
            : category === "Recommended"
            ? NetworkingResources.WorkspacePrivateOutbound.recommendedOutboundRules
            : category === "Dependency"
            ? NetworkingResources.WorkspacePrivateOutbound.dependencyOutboundRules
            : NetworkingResources.WorkspacePrivateOutbound.userDefinedOutboundRules;
    }

    private _loadManagedNetworkApiRules(): void {
        const managedNetworkRuleParams = ko
            .computed(this._context.lifetimeManager, () => {
                return {
                    workspaceName: this._resourceDetails.workspaceName(),
                    subscriptionId: this._resourceDetails.subscription?.()?.subscriptionId || this._resourceDetails.subscriptionId?.(),
                    resourceGroupName: this._resourceDetails.resourceGroup?.()?.value?.name || this._resourceDetails.resourceGroupName?.(),
                    region: this._resourceDetails.location?.()?.name || this._resourceDetails.locationName?.(),
                    storageAccountId: this._resourceDetails.storageAccount?.()?.id || this._resourceDetails.storageAccountId?.(),
                    keyVaultId: this._resourceDetails.keyVault?.()?.id || this._resourceDetails.keyVaultId?.(),
                    appInsightsId: this._resourceDetails.appInsights?.()?.id || this._resourceDetails.appInsightsId?.(),
                    containerRegistryId: this._resourceDetails.containerRegistry?.()?.id || this._resourceDetails.containerRegistryId?.(),
                    storageAccountName: this._resourceDetails.storageAccount?.()?.name,
                    keyVaultName: this._resourceDetails.keyVault?.()?.name,
                    appInsightsName: this._resourceDetails.appInsights?.()?.name,
                    containerRegistryName: this._resourceDetails.containerRegistry?.()?.name
                };
            })
            .extend({ throttle: 500 }); // throttle to avoid too many requests

        managedNetworkRuleParams.subscribeAndRun(this._context.lifetimeManager, newParams => {
            const {
                subscriptionId,
                resourceGroupName,
                workspaceName,
                region,
                storageAccountId,
                keyVaultId,
                appInsightsId,
                containerRegistryId,
                storageAccountName,
                keyVaultName,
                appInsightsName,
                containerRegistryName
            } = newParams;
            if (
                this._advancedDataModel &&
                this._advancedDataModel.managedKeyVaultType &&
                this._advancedDataModel.managedKeyVaultType() === "byoKeyVault" &&
                !keyVaultId &&
                !keyVaultName
            ) {
                this._managedNetworkDefaultRulesResponse(undefined);
                return;
            }

            if (subscriptionId && resourceGroupName && workspaceName && region && (storageAccountId || storageAccountName)) {
                const loading = Q.defer();
                this._workspaceOutboundAccessGrid.displayLoadingMessage(loading.promise);

                const storageAccountResourceId =
                    storageAccountId ||
                    `/subscriptions/${subscriptionId}/resourceGroups/${resourceGroupName}/providers/Microsoft.Storage/storageAccounts/${storageAccountName}`;

                const keyVaultResourceId =
                    keyVaultId ||
                    (keyVaultName
                        ? `/subscriptions/${subscriptionId}/resourceGroups/${resourceGroupName}/providers/Microsoft.KeyVault/vaults/${keyVaultName}`
                        : "");
                const appInsightsResourceId = appInsightsId
                    ? appInsightsId
                    : appInsightsName
                    ? `/subscriptions/${subscriptionId}/resourceGroups/${resourceGroupName}/providers/Microsoft.Insights/components/${appInsightsName}`
                    : undefined;

                const containerRegistryResourceId = containerRegistryId
                    ? containerRegistryId
                    : containerRegistryName
                    ? `/subscriptions/${subscriptionId}/resourceGroups/${resourceGroupName}/providers/Microsoft.ContainerRegistry/registries/${containerRegistryName}`
                    : undefined;

                const managedNetworkRuleResponse = this._dataContext.workspaceOutboundAccessData.getDefaultManagedNetworkRules({
                    subscriptionId,
                    resourceGroupName,
                    workspaceName,
                    region,
                    storageAccountResourceId,
                    keyVaultResourceId,
                    appInsightsResourceId,
                    containerRegistryResourceId
                });

                managedNetworkRuleResponse
                    .then(
                        (data: any) => {
                            this._managedNetworkDefaultRulesResponse(data);
                        },
                        (error: any) => {
                            this._managedNetworkDefaultRulesResponse(undefined);
                            this._setErrorMessage(error?.message);
                        }
                    )
                    .finally(() => {
                        this._managedNetworkDefaultRulesLoaded(true);
                        loading.resolve();
                    });
            } else {
                this._managedNetworkDefaultRulesResponse(undefined);
            }
        });
    }
}

/**
 * This function will create the ManagedNetworkRuleItem for required and recommended rules which come from RP service.
 * If required and recommended rules were already fetched, the latest result will replace them while maintaining the "active" state
 * of the prior item if it appears in the new set.
 */
function refreshOutboundAccessRulesFromResponse(
    response: ManagedNetworkDefaultRulesResponse | undefined,
    existingOutboundAccessRules: ManagedNetworkRuleItem[],
    currentOutboundRules: ManagedNetworkRuleItem[],
    isolationMode: NetworkIsolationMode,
    loadExistingRulesOnly: boolean
): ManagedNetworkRuleItem[] {
    if (isolationMode === "Disabled") {
        return [];
    }

    const recommendedRules: ManagedNetworkRuleItem[] = [];
    const requiredRules: ManagedNetworkRuleItem[] = [];
    const existingOutboundRules = [];

    // User can delete the user defined rules, if there are current rules, we will stick to them
    if (currentOutboundRules.length > 0) {
        const currentUserDefinedRules = currentOutboundRules.filter(rule => rule.ruleCategory === "UserDefined");
        for (const rule of currentUserDefinedRules) {
            existingOutboundRules.push(rule);
        }

        existingOutboundAccessRules = existingOutboundAccessRules.filter(rule => rule.ruleCategory !== "UserDefined");
    }

    const currentRecommendedRules = currentOutboundRules.filter(rule => rule.ruleCategory === "Recommended");
    if (currentRecommendedRules.length > 0) {
        const existingRecommendedRules = existingOutboundAccessRules.filter(rule => rule.ruleCategory === "Recommended");
        for (const existingRule of existingRecommendedRules) {
            const currentRule = currentRecommendedRules.find(rule => rule.name === existingRule.name);
            if (currentRule) {
                existingOutboundRules.push(currentRule);
            } else {
                existingOutboundRules.push(existingRule);
            }
        }
        existingOutboundAccessRules = existingOutboundAccessRules.filter(rule => rule.ruleCategory !== "Recommended");
    }

    for (const rule of existingOutboundAccessRules) {
        const newRule = {
            ...rule,
            active: ko.observable(true)
        };
        existingOutboundRules.push(newRule);
    }

    if (loadExistingRulesOnly) {
        return existingOutboundRules;
    }

    const apiRules = isolationMode === "AllowInternetOutbound" ? response?.allowInternetOutboundRules : response?.allowOnlyApprovedOutboundRules;

    const existingRules = existingOutboundRules.reduce<{
        userDefinedRules: Record<string, ManagedNetworkRuleItem>;
        recommendedRules: Record<string, ManagedNetworkRuleItem>;
        requiredRules: Record<string, ManagedNetworkRuleItem>;
        dependencyRules: Record<string, ManagedNetworkRuleItem>;
    }>(
        (acc, rule) => {
            if (rule.ruleCategory === "Recommended") {
                acc.recommendedRules[rule.name] = rule;
            } else if (rule.ruleCategory === "Required") {
                acc.requiredRules[rule.name] = rule;
            } else if (rule.ruleCategory === "Dependency") {
                acc.dependencyRules[rule.name] = rule;
            } else {
                acc.userDefinedRules[rule.name] = rule;
            }
            return acc;
        },
        { userDefinedRules: {}, recommendedRules: {}, requiredRules: {}, dependencyRules: {} }
    );

    if (apiRules?.recommendedRules) {
        Object.keys(apiRules.recommendedRules).forEach(key => {
            const outboundRule = apiRules.recommendedRules[key];
            const existingRule = existingRules.recommendedRules[key];
            const active = existingRule?.active() ?? true;
            recommendedRules.push({
                name: key,
                destinationType: outboundRule.type,
                destination: getOutboundRuleDestination(outboundRule),
                outboundRule: outboundRule,
                active: ko.observable(active),
                ruleCategory: "Recommended"
            });
        });
    }

    Object.entries(existingRules.recommendedRules).forEach(([key, ruleItem]) => {
        if (apiRules?.recommendedRules && !apiRules.recommendedRules[key]) {
            recommendedRules.push({
                name: key,
                destinationType: ruleItem.outboundRule.type,
                destination: getOutboundRuleDestination(ruleItem.outboundRule),
                outboundRule: ruleItem.outboundRule,
                active: ko.observable(ruleItem.active()),
                ruleCategory: "Recommended"
            });
        }
    });

    if (apiRules?.requiredRules) {
        Object.keys(apiRules.requiredRules).forEach(key => {
            const outboundRule = apiRules.requiredRules[key];
            const existingRule = existingRules.requiredRules[key];
            const active = existingRule?.active() ?? true;
            requiredRules.push({
                name: key,
                destinationType: outboundRule.type,
                destination: getOutboundRuleDestination(outboundRule),
                outboundRule: outboundRule,
                active: ko.observable(active),
                ruleCategory: "Required"
            });
        });
    }

    return [...Object.values(existingRules.userDefinedRules), ...recommendedRules, ...requiredRules, ...Object.values(existingRules.dependencyRules)];
}

/**
 * This function will build the grid nodes from the current set of ManagedNetworkRuleItem items.
 */
function buildOutboundAccessRulesGridNodes(
    managedNetworkOutboundAccessRules: ManagedNetworkRuleItem[]
): DataGrid.GroupNode<RuleCategory, ManagedNetworkRuleItem, string>[] {
    const nodes: DataGrid.DataNode<ManagedNetworkRuleItem, string>[] = managedNetworkOutboundAccessRules.map(rule => ({
        id: rule.name,
        item: rule
    }));
    const groupNodeList: DataGrid.GroupNode<RuleCategory, ManagedNetworkRuleItem, string>[] = [];
    const userDefinedRules = nodes.filter(node => node.item.ruleCategory === "UserDefined");
    if (userDefinedRules.length > 0) {
        groupNodeList.push({
            id: getUniqueId(),
            group: "UserDefined",
            children: userDefinedRules
        });
    }
    const recommendedRules = nodes.filter(node => node.item.ruleCategory === "Recommended");
    if (recommendedRules.length > 0) {
        groupNodeList.push({
            id: getUniqueId(),
            group: "Recommended",
            children: recommendedRules
        });
    }
    const requiredRules = nodes.filter(node => node.item.ruleCategory === "Required");
    if (requiredRules.length > 0) {
        groupNodeList.push({
            id: getUniqueId(),
            group: "Required",
            children: requiredRules
        });
    }
    const dependencyRules = nodes.filter(node => node.item.ruleCategory === "Dependency");
    if (dependencyRules.length > 0) {
        groupNodeList.push({
            id: getUniqueId(),
            group: "Dependency",
            children: dependencyRules
        });
    }
    return groupNodeList;
}
