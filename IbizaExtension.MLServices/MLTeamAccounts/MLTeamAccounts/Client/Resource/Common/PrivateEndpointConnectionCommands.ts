import { BladeReferences } from "Fx/Composition";
import * as Dialog from "Fx/Composition/Dialog";
import { Container } from "Fx/Composition/TemplateBlade";
import * as TextBox from "Fx/Controls/TextBox";
import * as Toolbar from "Fx/Controls/Toolbar";
import { LifetimeManager } from "Fx/Lifetime";
import * as ClientResources from "Resx/ClientResources";
import Utilities from "../../Shared/Utilities";
import { DataContext } from "../ResourceArea";
import { PrivateLinkServiceConnectionStateStatus } from "../Data/PrivateEndpointConnectionsData";

import HubsNotifications = MsPortalFx.Hubs.Notifications;

const CommandResources = ClientResources.PrivateEndpoints.Commands;

export interface ConnectionItem {
    id: string;
    name: string;
    description: string;
    status: PrivateLinkServiceConnectionStateStatus;
}

export interface Options {
    resourceId: string;
    selectedItems: KnockoutObservableArray<ConnectionItem> | KnockoutReadOnlyObservableArray<ConnectionItem>;
    openInContextPane?: boolean;
    refreshItems?: (model: DataContext) => void;
}

export interface Contract {
    addCommand: Toolbar.ToolbarItems.BasicButtonContract;
    approveCommand: Toolbar.ToolbarItems.BasicButtonContract;
    rejectCommand: Toolbar.ToolbarItems.BasicButtonContract;
    deleteCommand: Toolbar.ToolbarItems.BasicButtonContract;
}

interface Context {
    container: Container;
    lifetimeManager: LifetimeManager;
}

export function create(context: Context, model: DataContext, options: Options) {
    const { container, lifetimeManager } = context;
    const { resourceId, selectedItems, openInContextPane } = options;
    const refreshItems = options.refreshItems || MsPortalFx.noop;

    const addEndpointCommand = Toolbar.ToolbarItems.createBasicButton(lifetimeManager, {
        telemetryName: "AddPrivateEndpointFromConnectionsGrid",
        label: CommandResources.addPrivateEndpoint,
        icon: MsPortalFx.Base.Images.Add(),
        onClick: () => {
            // tODO: Remove casing when new PDE is available
            if (openInContextPane) {
                container.openContextPane(
                    BladeReferences.forExtension("Microsoft_Azure_Network").forBlade("CreatePrivateEndpointBlade").createReference({
                        doesProvisioning: true,
                        parameters: { resourceId }
                    })
                );
            } else {
                container.openBlade(
                    BladeReferences.forExtension("Microsoft_Azure_Network").forBlade("CreatePrivateEndpointBlade").createReference({
                        doesProvisioning: true,
                        parameters: { resourceId }
                    })
                );
            }
        }
    });

    const approveCommand = createCommand(
        context,
        selectedItems,
        "ApprovePrivateEndpointConnectionFromGrid",
        CommandResources.approve,
        MsPortalFx.Base.Images.Check(),
        CommandResources.Approve,
        (selectedItems, description) => {
            trackBatchedQuery(
                selectedItems.map(item => model.privateEndpointConnectionsData.updatePrivateEndpointConnectionState(item.id, "Approved", description)),
                CommandResources.ApproveNotifications,
                selectedItems.map(item => item.name)).finally(() => {
                    refreshItems(model);
                });
        },
        true,
        ["Pending"]);

    const rejectCommand = createCommand(
        context,
        selectedItems,
        "RejectPrivateEndpointConnectionFromGrid",
        CommandResources.reject,
        MsPortalFx.Base.Images.Discard(),
        CommandResources.Reject,
        (selectedItems, description) => {
            trackBatchedQuery(
                selectedItems.map(item => model.privateEndpointConnectionsData.updatePrivateEndpointConnectionState(item.id, "Rejected", description)),
                CommandResources.RejectNotifications,
                selectedItems.map(item => item.name)).finally(() => {
                    refreshItems(model);
                });
        },
        true,
        ["Pending", "Approved"]);

    const deleteCommand = createCommand(
        context,
        selectedItems,
        "DeletePrivateEndpointConnectionFromGrid",
        CommandResources.remove,
        MsPortalFx.Base.Images.Delete(),
        CommandResources.Remove,
        selectedItems => {
            trackBatchedQuery(
                selectedItems.map(item => model.privateEndpointConnectionsData.deletePrivateEndpointConnection(item.id)),
                CommandResources.DeleteNotifications,
                selectedItems.map(item => item.name)).finally(() => {
                    refreshItems(model);
                });
        },
        false,
        ["Pending", "Approved", "Rejected"]);

    return {
        addEndpointCommand, approveCommand, rejectCommand, deleteCommand
    };
}

function createCommand(
    context: Context,
    selectedItemsObs: KnockoutReadOnlyObservableArray<ConnectionItem>,
    telemetryName: string,
    label: string,
    icon: MsPortalFx.Base.Image,
    dialogStrings: {
        title: string;
        messageSingular: string;
        messagePlural: string;
    },
    action: (items: ConnectionItem[], description: string) => void,
    showDescription: boolean,
    allowedStatuses: PrivateLinkServiceConnectionStateStatus[]
) {
    const { container, lifetimeManager } = context;
    const allowedStatusesMap = MsPortalFx.convertArrayToMap(allowedStatuses, MsPortalFx.identity);
    return Toolbar.ToolbarItems.createBasicButton(container, {
        label,
        icon,
        disabled: ko.pureComputed(() => !selectedItemsObs().length || selectedItemsObs().some(item => !allowedStatusesMap[item.status])),
        onClick: () => {
            const selectedItems = selectedItemsObs();
            const isMultiple = selectedItems.length > 1;
            const message = isMultiple
                ? dialogStrings.messagePlural.format(selectedItems.length)
                : dialogStrings.messageSingular.format(selectedItems[0].name);
            const descriptionTextBox = TextBox.create(lifetimeManager, {
                label: CommandResources.description,
                value: !isMultiple && selectedItems[0].description || ""
            });
            container.openDialog({
                telemetryName: telemetryName,
                title: dialogStrings.title,
                content: showDescription
                    ? {
                        htmlTemplate: `<div data-bind="text: message" style="margin-bottom: 10px"></div><div data-bind="pcControl: description"></div>`,
                        viewModel: {
                            message,
                            description: descriptionTextBox
                        }
                    } : message,
                buttons: Dialog.DialogButtons.YesNo,
                onClosed: result => {
                    if (result.button === Dialog.DialogButton.Yes) {
                        const description = descriptionTextBox.value();
                        action(selectedItems, description);
                    }
                }
            });
        }
    });
}

export interface BatchMessages {
    // We expect the failure message to contain a {0} and {1} for number of failures / calls respectively.
    // Example, {0} out of {1} calls failed unexpectedly.
    Failure: {
        message: string;
        title: string;
    };
    // We expect the in progress messsage to contain a {0} for number of calls.
    // Example, Attempting to delete {0} storage queues.
    InProgress: {
        message: string;
        title: string;
    };
    // We expect the success message to contain a {0} for the number of calls.
    // Example, Successfully deleted {0} storage queues.
    Success: {
        message: string;
        title: string;
    };
}


function trackBatchedQuery(
    promises: Promise<unknown>[],
    messages: BatchMessages,
    promiseDisplayNames?: string[]): Promise<unknown> {

    // Show in progress to let user know notifications have been kicked off.
    const notification = new HubsNotifications.ClientNotification({
        title: messages.InProgress.title,
        description: messages.InProgress.message.format(promises.length),
        status: HubsNotifications.NotificationStatus.InProgress
    });

    notification.publish();

    // Alert the user if all suceeded or all / some failed
    return Q.allSettled(promises).then((promiseStates) => {
        const failedIndices = promiseStates.map((promiseState, index) => {
            return promiseState.state === "rejected" ? index : -1;
        }).filter(index => index >= 0);

        if (failedIndices.length) {
            notification.title = messages.Failure.title;
            notification.description = messages.Failure.message.format(failedIndices.length, promises.length);
            notification.status = HubsNotifications.NotificationStatus.Error;
            const errors = failedIndices
                .map(index => {
                    let errorMessage: string = Utilities.extractErrorMessage(promiseStates[index].reason.jqXHR);
                    if (errorMessage === ClientResources.PrivateEndpoints.genericErrorMessage) {
                        errorMessage = null;
                    }

                    if (promiseDisplayNames) {
                        const promiseName = promiseDisplayNames[index];
                        errorMessage = errorMessage
                            ? ClientResources.PrivateEndpoints.Create.validationErrorFormat.format(promiseName, errorMessage)
                            : promiseName;
                    }

                    return errorMessage;
                }).filter(error => !!error);
            if (errors.length) {
                notification.description += '\n' + errors.join('\n');
            }
        } else {
            notification.title = messages.Success.title;
            notification.description = messages.Success.message.format(promises.length);
            notification.status = HubsNotifications.NotificationStatus.Success;
        }

        notification.publish();
    });
}
