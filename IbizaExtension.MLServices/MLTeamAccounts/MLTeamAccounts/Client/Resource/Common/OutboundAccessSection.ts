import { DataContext } from "Resource/ResourceArea";
import * as DataGrid from "Fx/Controls/DataGrid";
import * as RadioButton from "Fx/Controls/RadioButtons";
import * as Section from "Fx/Controls/Section";
import { LifetimeManager } from "Fx/Lifetime";
import { Container } from "Fx/Composition/TemplateBlade";
import Resources = require("Resx/ClientResources");

import { DocLinkIds, getDocumentUrl } from "../../Shared/DocLinks";
import { createSectionHeader, createLink } from "../Create/Utilities/NoPdlUiHelpers";

import { WorkspaceMappedKind } from "../../Shared/Enums";
import { ManagedNetworkIsolationMode, NetworkingDataModel, ResourceDetails, WorkspaceNetworkSelectionOption } from "./NetworkingDataModel";
import { WorkspaceOutboundAccessControl } from "./WorkspaceOutboundAccessControl";
import { createNetworkingSection } from "./NetworkingUtilities";
import { createNetworkSelectionRadio } from "./NetworkSelectionRadio";
import { AdvancedDataModel } from "Resource/Create/ViewModels/CreateMachineLearningModels";

const NetworkingResources = Resources.CreateBlade.Network;

interface Context {
    lifetimeManager: LifetimeManager;
    container: Container;
}

export interface Options {
    workspaceKind: WorkspaceMappedKind
    form: MsPortalFx.ViewModels.FormProperties;
    networkingDataModel: NetworkingDataModel;
    advancedDataModel: AdvancedDataModel;
    resourceDetails: ResourceDetails;
    dataContext: DataContext;
    isLoading: KnockoutObservable<boolean>;
    isCreationFlow: boolean;
    resourceId?: string;
    isDelegateSubnetEnabled: KnockoutObservable<boolean>;
}

export class OutboundAccessSection {
    public section: Section.Contract;

        private _workspaceKind: WorkspaceMappedKind;
        private _networkSelectionRadioButton: RadioButton.Contract<WorkspaceNetworkSelectionOption>;
        private _networkingDataModel: NetworkingDataModel;
        private _context: Context;
        private _resourceDetails: ResourceDetails;
        private _workspaceOutboundAccessControl: WorkspaceOutboundAccessControl;
        private _isLoading: KnockoutObservable<boolean>;
        private _isCreateFlow: boolean;
        private _workspaceNetworkSelectionOption = ko.observable<WorkspaceNetworkSelectionOption>(WorkspaceNetworkSelectionOption.Public);
        private _managedNetworkIsolationMode = ko.observable<ManagedNetworkIsolationMode>(ManagedNetworkIsolationMode.Disabled);
        private _alertConfiguration: KnockoutObservableBase<MsPortalFx.ViewModels.AlertConfiguration>;
        private _sectionChildren: KnockoutObservableArray<any> = ko.observableArray<any>([]);

        constructor(context: Context, options: Options) {
            const { form, networkingDataModel, isCreationFlow, isLoading } = options;
            const { lifetimeManager } = context;
            this._context = context;
            this._networkingDataModel = networkingDataModel;
            this._workspaceKind = options.workspaceKind;
            this._resourceDetails = options.resourceDetails;
            this._isLoading = isLoading;
            this._isCreateFlow = isCreationFlow;
            if (!this._isCreateFlow) {
                this._alertConfiguration = ko.observable<MsPortalFx.ViewModels.AlertConfiguration>({
                    showAlert: networkingDataModel.isDirty()
                });
                networkingDataModel.isDirty.subscribe(lifetimeManager, (isDirty: boolean) => {
                    this._alertConfiguration({
                        showAlert: isDirty
                    });
                });
                form.configureAlertOnClose(this._alertConfiguration);
            }

            this.initializeSection(options);

            this.section = Section.create(lifetimeManager, {
                name: isCreationFlow ? NetworkingResources.outboundAccessTabTitle : undefined,
                children: this._sectionChildren,
                cssClass: "ext-focus-container"
            });
        }

        public initializeSection(options: Options): void {

            const children: any[] = [];

            this._workspaceOutboundAccessControl = new WorkspaceOutboundAccessControl(this._context, {
                ...options,
                managedNetworkIsolationMode: this._managedNetworkIsolationMode
            });

            this._createNetworkSelectionRadioButton();
            children.push(
                createNetworkingSection(
                    this._context.lifetimeManager,
                    [
                        createSectionHeader(NetworkingResources.networkIsolationTitle),
                        this._createManagedNetworkDescription(),
                        this._networkSelectionRadioButton
                    ],
                    null,
                    "ext-nopdlcreate-section"
                )
            );

            children.push(
                createNetworkingSection(
                    this._context.lifetimeManager,
                    [
                        ...this._workspaceOutboundAccessControl.getControls()
                    ],
                    ko.pureComputed(() => {
                        return (
                            this._workspaceNetworkSelectionOption() === WorkspaceNetworkSelectionOption.Private ||
                            this._workspaceNetworkSelectionOption() === WorkspaceNetworkSelectionOption.PrivateApprovedOutbound
                        );
                    })
                )
            );

            this._sectionChildren(children);
        }

        public load(): void {
        }

        public onLoad(): void {
        }

        public triggerValidation(): void {
        }

        private _createManagedNetworkDescription(): DataGrid.HtmlContent {
            return createLink({
                text: this._workspaceKind === WorkspaceMappedKind.Hub
                    ? NetworkingResources.Hub.networkIsolationDescription
                    : NetworkingResources.networkIsolationDescription,
                linkText: NetworkingResources.networkIsolationLearnMore,
                linkUri: this._workspaceKind === WorkspaceMappedKind.Hub
                    ? getDocumentUrl(DocLinkIds.ManagedNetworkIsolationAiResource)
                    : getDocumentUrl(DocLinkIds.ManagedNetworkIsolation),
                useIntroStyling: true
            });
        }

        private _createNetworkSelectionRadioButton(): void {
            this._networkSelectionRadioButton = createNetworkSelectionRadio(this._context.lifetimeManager, {
                workspaceNetworkSelectionOption: this._workspaceNetworkSelectionOption,
                isLoading: this._isLoading,
                containerRegistrySku: ko.pureComputed(() => this._resourceDetails.containerRegistry?.()?.sku)
            });

            this._networkingDataModel.privateEndpointConfigurations.subscribe(this._context.lifetimeManager, () => {
                this._networkSelectionRadioButton.triggerValidation();
            });

            this._workspaceNetworkSelectionOption.subscribe(this._context.lifetimeManager, selectedOption => {
                if (selectedOption === WorkspaceNetworkSelectionOption.Private) {
                    this._managedNetworkIsolationMode(ManagedNetworkIsolationMode.AllowInternetOutbound);
                } else if (selectedOption === WorkspaceNetworkSelectionOption.PrivateApprovedOutbound) {
                    this._managedNetworkIsolationMode(ManagedNetworkIsolationMode.AllowOnlyApprovedOutbound);
                } else {
                    this._managedNetworkIsolationMode(ManagedNetworkIsolationMode.Disabled);
                }
            });
        }
    }
