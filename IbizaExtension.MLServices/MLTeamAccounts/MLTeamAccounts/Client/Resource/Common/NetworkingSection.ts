import { DataContext } from "Resource/ResourceArea";
import * as DataGrid from "Fx/Controls/DataGrid";
import * as RadioButton from "Fx/Controls/RadioButtons";
import * as Section from "Fx/Controls/Section";
import { LifetimeManager } from "Fx/Lifetime";
import { Container } from "Fx/Composition/TemplateBlade";
import Resources = require("Resx/ClientResources");

import { DocLinkIds, getDocumentUrl } from "../../Shared/DocLinks";
import { createTextContent, createSectionHeader, createLink } from "../Create/Utilities/NoPdlUiHelpers";

import { WorkspaceMappedKind } from "../../Shared/Enums";
import { ManagedNetworkIsolationMode, NetworkingDataModel, ResourceDetails, WorkspaceNetworkSelectionOption } from "./NetworkingDataModel";
import { WorkspaceInboundAccessControl } from "./WorkspaceInboundAccessControl";
import { WorkspaceOutboundAccessControl } from "./WorkspaceOutboundAccessControl";
import { createNetworkingSection } from "./NetworkingUtilities";
import { createNetworkSelectionRadio } from "./NetworkSelectionRadio";
import * as DelegateSubnetDropdown from "../Create/Controls/Dropdowns/DelegateSubnetDropdown";
import { AdvancedDataModel } from "Resource/Create/ViewModels/CreateMachineLearningModels";

const NetworkingResources = Resources.CreateBlade.Network;

interface Context {
    lifetimeManager: LifetimeManager;
    container: Container;
}

export interface Options {
    workspaceKind: WorkspaceMappedKind
    form: MsPortalFx.ViewModels.FormProperties;
    networkingDataModel: NetworkingDataModel;
    advancedDataModel: AdvancedDataModel;
    resourceDetails: ResourceDetails;
    dataContext: DataContext;
    isLoading: KnockoutObservable<boolean>;
    isCreationFlow: boolean;
    resourceId?: string;
    isDelegateSubnetEnabled: KnockoutObservable<boolean>;
}

export class NetworkingSection {
    public section: Section.Contract;

    private _workspaceKind: WorkspaceMappedKind;
    private _networkSelectionRadioButton: RadioButton.Contract<WorkspaceNetworkSelectionOption>;
    private _networkingDataModel: NetworkingDataModel;
    private _context: Context;
    private _resourceDetails: ResourceDetails;
    private _workspaceInboundAccessControl: WorkspaceInboundAccessControl;
    private _workspaceOutboundAccessControl: WorkspaceOutboundAccessControl;
    private _isLoading: KnockoutObservable<boolean>;
    private _isCreateFlow: boolean;
    private _workspaceNetworkSelectionOption = ko.observable<WorkspaceNetworkSelectionOption>(WorkspaceNetworkSelectionOption.Public);
    private _managedNetworkIsolationMode = ko.observable<ManagedNetworkIsolationMode>(ManagedNetworkIsolationMode.Disabled);
    private _alertConfiguration: KnockoutObservableBase<MsPortalFx.ViewModels.AlertConfiguration>;
    private _sectionChildren: KnockoutObservableArray<any> = ko.observableArray<any>([]);
    private _delegateSubnet: DelegateSubnetDropdown.Contract;

    constructor(context: Context, options: Options) {
        const { form, networkingDataModel, isCreationFlow, isLoading } = options;
        const { lifetimeManager } = context;
        this._context = context;
        this._networkingDataModel = networkingDataModel;
        this._workspaceKind = options.workspaceKind;
        this._resourceDetails = options.resourceDetails;
        this._isLoading = isLoading;
        this._isCreateFlow = isCreationFlow;
        if (!this._isCreateFlow) {
            this._alertConfiguration = ko.observable<MsPortalFx.ViewModels.AlertConfiguration>({
                showAlert: networkingDataModel.isDirty()
            });
            networkingDataModel.isDirty.subscribe(lifetimeManager, (isDirty: boolean) => {
                this._alertConfiguration({
                    showAlert: isDirty
                });
            });
            form.configureAlertOnClose(this._alertConfiguration);
        }

        this.initializeSection(options);

        this.section = Section.create(lifetimeManager, {
            name: isCreationFlow ? NetworkingResources.title : undefined,
            children: this._sectionChildren,
            cssClass: "ext-focus-container"
        });
    }

    public initializeSection(options: Options): void {
        if(this._delegateSubnet === undefined &&  this._workspaceKind === WorkspaceMappedKind.Hub) {
            this._delegateSubnet = DelegateSubnetDropdown.create(this._context, {
                 subscription: this._resourceDetails.subscription,
                 workspaceName: this._resourceDetails.workspaceName,
                 onSelectionChanged: (resource) => {
                    this._networkingDataModel.delegatedSubnet(resource?.data);
                 },
                 visible: options.isDelegateSubnetEnabled
            });
        }

        const children: any[] = [];
        this._workspaceInboundAccessControl = new WorkspaceInboundAccessControl(this._context, {
            ...options,
            workspaceNetworkSelectionOption: this._workspaceNetworkSelectionOption
        });
        this._workspaceOutboundAccessControl = new WorkspaceOutboundAccessControl(this._context, {
            ...options,
            managedNetworkIsolationMode: this._managedNetworkIsolationMode
        });

        this._createNetworkSelectionRadioButton();
        children.push(
            createNetworkingSection(
                this._context.lifetimeManager,
                [
                    createSectionHeader(NetworkingResources.SubnetDelegate.title),
                    createLink({
                        text: NetworkingResources.SubnetDelegate.message,
                    linkText: NetworkingResources.networkIsolationLearnMore,
                    linkUri: "https://aka.ms/azure-ai/agents/delegate-subnet"
                    }),
                    this._delegateSubnet,
                ],
                ko.pureComputed(() => { return options.isDelegateSubnetEnabled() && this._workspaceKind === WorkspaceMappedKind.Hub; }),
                "ext-nopdlcreate-section"
            ),
            createNetworkingSection(
                this._context.lifetimeManager,
                [
                    createSectionHeader(NetworkingResources.networkIsolationTitle),
                    this._createManagedNetworkDescription(),
                    this._networkSelectionRadioButton
                ],
                null,
                "ext-nopdlcreate-section"
            )
        );

        children.push(
            createNetworkingSection(
                this._context.lifetimeManager,
                [
                    createSectionHeader(
                        ko.pureComputed<string>(() =>
                            this._workspaceNetworkSelectionOption() === WorkspaceNetworkSelectionOption.Private
                                ? NetworkingResources.WorkspaceInternetOutbound.PrivateNetworkSettings.title
                                : NetworkingResources.WorkspaceApprovedOutbound.PrivateNetworkSettings.title
                        )
                    ),
                    createTextContent(
                        ko.pureComputed(() =>
                            this._workspaceNetworkSelectionOption() === WorkspaceNetworkSelectionOption.Private
                                ? NetworkingResources.WorkspaceInternetOutbound.PrivateNetworkSettings.description
                                : NetworkingResources.WorkspaceApprovedOutbound.PrivateNetworkSettings.description
                        ),
                        true
                    ),
                    ...this._workspaceInboundAccessControl.getWorkspaceInboundAccessItems(),
                    ...this._workspaceOutboundAccessControl.getControls()
                ],
                ko.pureComputed(() => {
                    return (
                        this._workspaceNetworkSelectionOption() === WorkspaceNetworkSelectionOption.Private ||
                        this._workspaceNetworkSelectionOption() === WorkspaceNetworkSelectionOption.PrivateApprovedOutbound
                    );
                })
            )
        );

        this._sectionChildren(children);
    }

    public load(): Q.Promise<any> {
        return Q.all([this._workspaceInboundAccessControl.load()]);
    }

    public onLoad(): void {
        this._workspaceInboundAccessControl.onLoad();
    }

    public triggerValidation(): Q.Promise<boolean> {
        return this._workspaceInboundAccessControl.triggerValidation();
    }

    private _createManagedNetworkDescription(): DataGrid.HtmlContent {
        return createLink({
            text: this._workspaceKind === WorkspaceMappedKind.Hub
                ? NetworkingResources.Hub.networkIsolationDescription
                : NetworkingResources.networkIsolationDescription,
            linkText: NetworkingResources.networkIsolationLearnMore,
            linkUri: this._workspaceKind === WorkspaceMappedKind.Hub
                ? getDocumentUrl(DocLinkIds.ManagedNetworkIsolationAiResource)
                : getDocumentUrl(DocLinkIds.ManagedNetworkIsolation),
            useIntroStyling: true
        });
    }

    private _createNetworkSelectionRadioButton(): void {
        this._networkSelectionRadioButton = createNetworkSelectionRadio(this._context.lifetimeManager, {
            workspaceNetworkSelectionOption: this._workspaceNetworkSelectionOption,
            isLoading: this._isLoading,
            containerRegistrySku: ko.pureComputed(() => this._resourceDetails.containerRegistry?.()?.sku)
        });

        this._networkingDataModel.privateEndpointConfigurations.subscribe(this._context.lifetimeManager, () => {
            this._networkSelectionRadioButton.triggerValidation();
        });

        this._workspaceNetworkSelectionOption.subscribe(this._context.lifetimeManager, selectedOption => {
            if (selectedOption === WorkspaceNetworkSelectionOption.Private) {
                this._managedNetworkIsolationMode(ManagedNetworkIsolationMode.AllowInternetOutbound);
            } else if (selectedOption === WorkspaceNetworkSelectionOption.PrivateApprovedOutbound) {
                this._managedNetworkIsolationMode(ManagedNetworkIsolationMode.AllowOnlyApprovedOutbound);
            } else {
                this._managedNetworkIsolationMode(ManagedNetworkIsolationMode.Disabled);
            }

            this._workspaceInboundAccessControl.validatePrivateEndpointLocation();
        });
    }
}
