import Utilities from "../../Shared/Utilities";
import { Logging } from "../../Shared/Logging";
import * as Di from "Fx/DependencyInjection";
import { IResourceGroup } from "./Data.Types";

@Di.Class()
export class ResourceManagementData {
    public armEndpoint = MsPortalFx.getEnvironmentValue("armEndpoint");
    public resourceManagementApiVersion = "api-version=2021-04-01";


    public checkResourceGroup(subscriptionId: string, resourceGroupName: string): Q.Promise<{ exists: boolean }> {
        const uri = `${this.armEndpoint}/subscriptions/${subscriptionId}/resourceGroups/${resourceGroupName}?${this.resourceManagementApiVersion}`;
        const deferred = Q.defer<{ exists: boolean }>();

        Utilities.armHeadRequest(uri).then(
            _ => {
                deferred.resolve({ exists: true })
            },
            reason => {
                const error = reason.jqXHR;
                if (!Utilities.isUserError(error)) {
                    Logging.LogAjaxFailure(error, "checkResourceGroup");
                }

                if (Utilities.isNotFound(error)) {
                    deferred.resolve({ exists: false })
                }
                else {
                    deferred.reject(error);
                }
            }
        );

        return deferred.promise;
    }

    public createResourceGroup(subscriptionId: string, resourceGroupName: string, location: string): Q.Promise<IResourceGroup> {
        const uri = `${this.armEndpoint}/subscriptions/${subscriptionId}/resourceGroups/${resourceGroupName}?${this.resourceManagementApiVersion}`;
        const deferred = Q.defer<IResourceGroup>();

        Utilities.armPutRequest(uri, JSON.stringify({ location })).then(
            result => {
                deferred.resolve(result)
            },
            reason => {
                const error = reason.jqXHR;
                if (!Utilities.isUserError(error)) {
                    Logging.LogAjaxFailure(error, "createResourceGroup");
                }

                deferred.reject(error);
            }
        );

        return deferred.promise;
    }
}