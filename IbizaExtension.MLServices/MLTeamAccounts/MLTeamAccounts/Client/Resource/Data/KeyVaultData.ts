﻿import Utilities from "../../Shared/Utilities";
import { Logging } from "../../Shared/Logging";
import Constants = require("../../Shared/Constants");
import ClientResources = require("Resx/ClientResources");
import * as Di from "Fx/DependencyInjection";

@Di.Class()
export class KeyVaultData {
    public armEndpoint: string = MsPortalFx.getEnvironmentValue("armEndpoint");
    public graphApiVersion = MsPortalFx.getEnvironmentValue("armResourceGraphApiVersion");
    public keyVaultApiVersion: string = "api-version=" + MsPortalFx.getEnvironmentValue("keyVaultApiVersion");

    public checkKeyVaultAvailability(subscriptionId: string, resourceGroup: string, keyVaultName: string): Q.Promise<MsPortalFx.ViewModels.ValidationResult> {
        const uri = `${this.armEndpoint}/subscriptions/${subscriptionId}/resourcegroups/${resourceGroup}/providers/${Constants.machineLearningKeyVaultProvider}/${Constants.keyVault}/${keyVaultName}?${this.keyVaultApiVersion}`;
        const deferred = Q.defer<MsPortalFx.ViewModels.ValidationResult>();
        Utilities.armGetRequest(uri).then((response: any) => {
            deferred.resolve({
                valid: false,
                message: ClientResources.validationKeyVaultNameAlreadyInUse
            });
        }, (failureResponse: MsPortalFx.Base.Net2.ErrorResponse<any>) => {
            if (failureResponse.jqXHR.status !== 404) {
                Logging.LogAjaxFailure(failureResponse.jqXHR, "checkKeyVaultAvailability");
            }
            deferred.resolve({
                valid: true,
                message: ""
            });
        });
        return deferred.promise;
    }

    public checkKeyVaultAvailabilityNew(subscriptionId: string, keyVaultName: string): Q.Promise<MsPortalFx.ViewModels.ValidationResult> {
        const uri = `${this.armEndpoint}/subscriptions/${subscriptionId}/providers/${Constants.machineLearningKeyVaultProvider}/checkNameAvailability?${this.keyVaultApiVersion}`;
        const deferred = Q.defer<MsPortalFx.ViewModels.ValidationResult>();

        var payload = JSON.stringify({
            name: keyVaultName,
            type: `${Constants.machineLearningKeyVaultProvider}/${Constants.keyVault}`
        });

        Utilities.armPostRequest(uri, payload).then((response: any) => {
            if (response.nameAvailable) {
                deferred.resolve({
                    valid: true,
                    message: ""
                });
            }
            else {
                deferred.resolve({
                    valid: false,
                    message: ClientResources.validationKeyVaultNameAlreadyInUse
                });
            }
        }, (failureResponse: MsPortalFx.Base.Net2.ErrorResponse<any>) => {
            Logging.LogAjaxFailure(failureResponse.jqXHR, "checkKeyVaultAvailability");
            deferred.resolve({
                valid: false,
                message: ClientResources.validationError
            });
        });
        return deferred.promise;
    }

    public getVaults(subscriptionId: string): Promise<any> {
        const apiParam = `api-version=${this.graphApiVersion}`
        const uri = `${this.armEndpoint}/providers/Microsoft.ResourceGraph/resources?${apiParam}`;
        const query = `resources | where type == "microsoft.keyvault/vaults"`;
        const payload = JSON.stringify({
            query,
            subscriptions: [subscriptionId]
        });
        return Utilities.armPostRequest(uri, payload).then(response => (response.data || []), (failureResponse: MsPortalFx.Base.Net2.ErrorResponse<any>) => {
            Logging.LogAjaxFailure(failureResponse.jqXHR, "getVaults");
        });
    }

    public checkKeyVaultExists(resourceId: string): Q.Promise<boolean> {
        const uri = `${this.armEndpoint}/${resourceId}?${this.keyVaultApiVersion}`;
        const deferred = Q.defer<boolean>();
        Utilities.armGetRequest(uri).then((response: any) => {
            deferred.resolve(true);
        }, (failureResponse: MsPortalFx.Base.Net2.ErrorResponse<any>) => {
            if (failureResponse.jqXHR.status === MsPortalFx.Base.Net2.HttpStatusCode.NotFound) {
                deferred.reject(failureResponse);
            } else if (failureResponse.jqXHR.status === MsPortalFx.Base.Net2.HttpStatusCode.Forbidden) {
                // If we get a 403, the user doesn't have read access to the keyvault
                // but it does exist so we resolve the promise as true
                deferred.resolve(true);
            } else {
                Logging.LogAjaxFailure(failureResponse.jqXHR, "checkKeyVaultExists");
                deferred.reject(failureResponse);
            }
        });
        return deferred.promise;
    }
}