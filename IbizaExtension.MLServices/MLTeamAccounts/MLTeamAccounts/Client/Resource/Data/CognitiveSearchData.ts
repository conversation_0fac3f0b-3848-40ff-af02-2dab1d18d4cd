import * as Di from "Fx/DependencyInjection";
import { Logging } from "../../Shared/Logging";
import Utilities from "../../Shared/Utilities";
import { IGenericResource } from "./Data.Types";

@Di.Class()
export class CognitiveSearchData {
    public armEndpoint = MsPortalFx.getEnvironmentValue("armEndpoint");
    public searchServicesApiVersion = "api-version=2020-08-01";

    public listSearchServices(subscriptionId: string): Promise<IGenericResource[]> {
        const uri = `${this.armEndpoint}/subscriptions/${subscriptionId}/providers/Microsoft.Search/searchServices?${this.searchServicesApiVersion}`;
        return Utilities.armGetRequest(uri).then(
            response => (response.value || []),
            reason => {
                const error = reason.jqXHR;
                if (!Utilities.isUserError(error)) {
                    Logging.LogAjaxFailure(error, "getSearchServices");
                }

                throw reason;
            }
        );
    }
}