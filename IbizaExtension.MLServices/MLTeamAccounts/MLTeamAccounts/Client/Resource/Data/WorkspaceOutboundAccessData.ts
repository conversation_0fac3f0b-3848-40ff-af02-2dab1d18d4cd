import * as Di from "Fx/DependencyInjection";

import { Logging } from "../../Shared/Logging";
import Utilities from "../../Shared/Utilities";
import { OutboundRule } from "./Data.Types";
import { ArmResource } from "Fx/ResourceManagement";

interface OutboundRules {
    recommendedRules: Record<string, OutboundRule>;
    requiredRules: Record<string, OutboundRule>;
}

export interface ManagedNetworkDefaultRulesResponse {
    allowInternetOutboundRules: OutboundRules;
    allowOnlyApprovedOutboundRules: OutboundRules;
}

export interface DefaultManagedRulesOptions {
    subscriptionId: string;
    resourceGroupName: string;
    workspaceName: string;
    region: string;
    appInsightsResourceId?: string;
    containerRegistryResourceId?: string;
    keyVaultResourceId?: string;
    storageAccountResourceId?: string;
}

@Di.Class()
export class WorkspaceOutboundAccessData {
    public armEndpoint: string = MsPortalFx.getEnvironmentValue("armEndpoint");
    public managedVNetApiVersion: string = "api-version=" + MsPortalFx.getEnvironmentValue("machineLearningServicesApiVersion");
    public armResourcesApiVersion: string = "api-version=" + MsPortalFx.getEnvironmentValue("armResourcesApiVersion");
    public machineLearningServicesApiVersion: string = "api-version=" + MsPortalFx.getEnvironmentValue("machineLearningServicesApiVersion");

    public getDefaultManagedNetworkRules(options: DefaultManagedRulesOptions): Promise<any> {
        const apiEndpoint = Utilities.constructRegionalAPIEndpoint(options.region);

        const uri =
            `${apiEndpoint}/rp/workspaces/subscriptions/${options.subscriptionId}/resourceGroups/${options.resourceGroupName}/providers/Microsoft.MachineLearningServices/GetDefaultRules?` +
            this.managedVNetApiVersion;
        const request = {
            WorkspaceName: options.workspaceName,
            Region: options.region,
            StorageAccountResourceId: options.storageAccountResourceId,
            ApplicationInsightsResourceId: options.appInsightsResourceId,
            ContainerRegistryResourceId: options.containerRegistryResourceId,
            ...(options.keyVaultResourceId && { KeyVaultResourceId: options.keyVaultResourceId })
        };
        const payload = JSON.stringify(request);

        const deferred = Q.defer<ManagedNetworkDefaultRulesResponse>();
        Utilities.postRequestWithPoll(uri, payload).then(
            (response: any) => {
                deferred.resolve(response.content);
            },
            (failureResponse: MsPortalFx.Base.Net2.ErrorResponse<any>) => {
                Logging.LogAjaxFailure(failureResponse.jqXHR, "getDefaultManagedNetworkRules");
                deferred.reject(failureResponse);
            }
        );

        return deferred.promise;
    }

    public getResources(subscriptionId: string, resourceGroup: string): Q.Promise<any> {
        const uri = `${this.armEndpoint}/subscriptions/${subscriptionId}/resourceGroups/${resourceGroup}/resources?${this.armResourcesApiVersion}`;
        const deferred = Q.defer<ArmResource[]>();
        Utilities.armGetRequest(uri, true).then((response: any) => {
            deferred.resolve(response.value);
        }, (failureResponse: MsPortalFx.Base.Net2.ErrorResponse<any>) => {
            Logging.LogAjaxFailure(failureResponse.jqXHR, "getResources");
            deferred.reject(failureResponse);
        });
        return deferred.promise;
    }

    public deleteOutboundRules(resourceId: string, rules: string[]): Q.Promise<any> {
        var deferred = Q.defer<any>();

        Q.all(rules.map((rule) => {
            this._deleteOutboundRule(resourceId, rule);
        })).then(
            (response: any) => {
                deferred.resolve(response);
            },
            (failureResponse: MsPortalFx.Base.Net2.ErrorResponse<any>) => {
                deferred.reject(failureResponse);
            }
        );

        return deferred.promise;
    }

    private _deleteOutboundRule(resourceId: string, rule: string): Q.Promise<any> {
        const uri = `${this.armEndpoint}/${resourceId}/outboundRules/${rule}?${this.machineLearningServicesApiVersion}`;
        var deferred = Q.defer<any>();

        Utilities.armDeleteRequest(uri).then(
            (response: any) => {
                deferred.resolve(response);
            },
            (failureResponse: MsPortalFx.Base.Net2.ErrorResponse<any>) => {
                Logging.LogAjaxFailure(failureResponse.jqXHR, "deleteOutboundRule");
                deferred.reject(failureResponse);
            }
        );

        return deferred.promise;
    }

    public getProviders(subscriptionId: string): Q.Promise<any> {
        const uri = `${this.armEndpoint}/subscriptions/${subscriptionId}/providers?${this.armResourcesApiVersion}`;
        const deferred = Q.defer<any>();
        Utilities.armGetRequest(uri).then((response: any) => {
            deferred.resolve(response.value);
        }, (failureResponse: MsPortalFx.Base.Net2.ErrorResponse<any>) => {
            Logging.LogAjaxFailure(failureResponse.jqXHR, "getProviders");
            deferred.reject(failureResponse);
        });
        return deferred.promise;
    }

    public getSubResourceTypes(resourceArmId: string, apiVersion: string): Q.Promise<any> {
        const uri = `${this.armEndpoint}${resourceArmId}/privateLinkResources?api-version=${apiVersion}`;
        const deferred = Q.defer<any>();
        Utilities.armGetRequest(uri).then((response: any) => {
            deferred.resolve(response.value);
        }, (failureResponse: MsPortalFx.Base.Net2.ErrorResponse<any>) => {
            Logging.LogAjaxFailure(failureResponse.jqXHR, "getSubResourceTypes");
            deferred.reject(failureResponse);
        });
        return deferred.promise;
    }
}
