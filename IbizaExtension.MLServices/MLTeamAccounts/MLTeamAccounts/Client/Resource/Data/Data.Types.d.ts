interface IGenericResource {
    readonly id: string;
    readonly name: string;
    readonly type: string;
    readonly location: string;
    disabled?: boolean;
    text?: string;
    kind?: string;
    subscriptionId?: string;
    subscriptionName?: string;
}

/**
 * Defines values for SkuName.
 * Possible values include: 'Standard_LRS', 'Standard_GRS', 'Standard_RAGRS', 'Standard_ZRS',
 * 'Premium_LRS', 'Premium_ZRS', 'Standard_GZRS', 'Standard_RAGZRS'
 * @readonly
 * @enum {string}
 */
export type StorageSkuName =
    | "Standard_LRS"
    | "Standard_GRS"
    | "Standard_RAGRS"
    | "Standard_ZRS"
    | "Premium_LRS"
    | "Premium_ZRS"
    | "Standard_GZRS"
    | "Standard_RAGZRS";

/**
 * Defines values for SkuTier.
 * Possible values include: 'Standard', 'Premium'
 * @readonly
 * @enum {string}
 */
export type StorageSkuTier = "Standard" | "Premium";

/**
 * Defines values for Kind.
 * Possible values include: 'Storage', 'StorageV2', 'BlobStorage', 'FileStorage',
 * 'BlockBlobStorage'
 * @readonly
 * @enum {string}
 */
export type StorageKind = "Storage" | "StorageV2" | "BlobStorage" | "FileStorage" | "BlockBlobStorage";

export interface IStorageSku {
    readonly kind?: StorageKind;
    readonly locations?: string[];
    readonly name: StorageSkuName;
    readonly tier?: StorageSkuTier;
}

export interface IStorageAccountResource extends IGenericResource {
    readonly sku?: IStorageSku;
    readonly properties?: {
        isHnsEnabled?: boolean;
    };
}

export interface ICognitiveServiceAccountResource extends IGenericResource {
    id: string;
    identity?: {
        principalId: string;
        tenantId: string;
        type: string;
    };
    kind: string;
    location: string;
    managedBy?: string;
    name: string;
    sku: {
        name: string;
    };
    properties: {
        // other properties omitted
        endpoint: string;
        provisioningState: string; // "Succeeded"
        publicNetworkAccess: string; // "Enabled"
    };
    resourceGroup: string;
    subscriptionId: string;
    tenantId: string;
    type: string;

    // plan?: string;
    // extendedLocation?: string;
}

export interface ICognitiveServicesSku {
    readonly resourceType?: string,
    readonly name?: string;
    readonly tier?: string;
    readonly kind?: string;
    readonly locations?: string[];
}

export interface ISearchServiceResource extends IGenericResource {
    readonly sku?: {
        name?: string;
    };
}

export interface IKeyvaultResource extends IGenericResource {}

export interface IAppInsightsResource extends IGenericResource {}

export interface IContainerRegistrySku {
    name: string;
}

export interface IContainerRegistryResource extends IGenericResource {
    readonly sku?: IContainerRegistrySku;
}

export interface IResourceGroup extends IGenericResource {}

export type NetworkIsolationMode = "Disabled" | "AllowInternetOutbound" | "AllowOnlyApprovedOutbound";
export type RuleType = "PrivateEndpoint" | "ServiceTag" | "FQDN";
export type RuleCategory = "Required" | "Recommended" | "UserDefined" | "Dependency";
export type RuleStatus = "Inactive" | "Active";

export interface PrivateEndpointDestination {
    serviceResourceId: string;
    subresourceTarget: string;
    sparkEnabled: boolean;
    sparkStatus?: RuleStatus;
}

export interface ServiceTagDestination {
    serviceTag: string;
    protocol: string;
    portRanges: string;
}

export interface PrivateEndpointOutboundRule {
    type: "PrivateEndpoint";
    destination: PrivateEndpointDestination;
    category: RuleCategory;
    status?: RuleStatus;
    parentResourceId?: string;
    parentRuleNames?: string[];
    fqdns?: string[];
}

export interface ServiceTagOutboundRule {
    type: "ServiceTag";
    destination: ServiceTagDestination;
    status?: RuleStatus;
    category: RuleCategory;
}

export interface FQDNOutboundRule {
    type: "FQDN";
    destination: string;
    status?: RuleStatus;
    category: RuleCategory;
}

export type OutboundRule = PrivateEndpointOutboundRule | ServiceTagOutboundRule | FQDNOutboundRule;

/**
 * Firewall Sku used for FQDN Rules.
 * Possible values include: 'Standard', 'Basic'
 * @readonly
 * @enum {string}
 */
export type FirewallSku = "Standard" | "Basic";

export interface ManagedNetwork {
    isolationMode: NetworkIsolationMode;
    outboundRules?: Record<string, OutboundRule>;
    changeableIsolationModes?: NetworkIsolationMode[];
    firewallSku?: FirewallSku;
}

export type AsObservable<T> = T extends Array<infer S>
    ? KnockoutObservableArray<AsObservable<S>>
    : T extends object
    ? KnockoutObservable<{
          [key in keyof T]: AsObservable<T[key]>;
      }>
    : KnockoutObservable<T>;
