import * as <PERSON> from "Fx/Ajax";
import * as Di from "Fx/DependencyInjection";
import { Logging } from "../../Shared/Logging";
import Utilities from "../../Shared/Utilities";

export type PrivateLinkServiceConnectionStateStatus =
    "Pending" | "Approved" | "Rejected" | "Disconnected";


export interface PrivateEndpointConnection {
    id: string;
    properties: {
        privateEndpoint: {
            id: string;
        };
        privateLinkServiceConnectionState: {
            actionsRequired: string;
            description: string;
            status: PrivateLinkServiceConnectionStateStatus;
        }
    };
}

interface ResourceWithConnections {
    properties: {
        privateEndpointConnections: PrivateEndpointConnection[]
    };
}

function trimTrailingSlash(url: string): string {
    if (url && url.endsWith("/")) {
        return url.substr(0, url.length - 1);
    }

    return url;
}

@Di.Class()
export class PrivateEndpointConnectionsData {
    public armEndpoint: string = trimTrailingSlash(MsPortalFx.getEnvironmentValue("armEndpoint"));
    public workspaceApiVersion: string = ("api-version=" + MsPortalFx.getEnvironmentValue("machineLearningServicesApiVersion"));

    public getPrivateEndpointConnections(workspaceId: string): Promise<PrivateEndpointConnection[]> {
        const uri = `${this.armEndpoint}${workspaceId}?${this.workspaceApiVersion}`;

        return Ajax.batch<ResourceWithConnections>({ uri }).then(
            resource => resource.content.properties.privateEndpointConnections || [],
            reason => {
                if (!Utilities.isUserError(reason)) {
                    Logging.LogAjaxFailure(reason, "getPrivateEndpointConnections");
                }

                throw reason;
            });
    }

    public deletePrivateEndpointConnection(connectionId: string): Promise<any> {
        const uri = `${this.armEndpoint}${connectionId}?${this.workspaceApiVersion}`;

        return Utilities.armDeleteRequest(uri).then(
            result => result,
            reason => {
                if (!Utilities.isUserError(reason)) {
                    Logging.LogAjaxFailure(reason, "deletePrivateEndpointConnection");
                }

                throw reason;
            }
        );
    }

    public updatePrivateEndpointConnectionState(connectionId: string, status: PrivateLinkServiceConnectionStateStatus, description: string): Promise<any> {
        const uri = `${this.armEndpoint}${connectionId}?${this.workspaceApiVersion}`;
        return Utilities.armPutRequest(uri, ko.toJSON({
            connectionId,
            properties: {
                privateLinkServiceConnectionState: {
                    status,
                    description
                }
            }
        })).then(
            result => result,
            reason => {
                if (!Utilities.isUserError(reason)) {
                    Logging.LogAjaxFailure(reason, "updatePrivateEndpointConnectionState");
                }

                throw reason;
            }
        );
    }
}