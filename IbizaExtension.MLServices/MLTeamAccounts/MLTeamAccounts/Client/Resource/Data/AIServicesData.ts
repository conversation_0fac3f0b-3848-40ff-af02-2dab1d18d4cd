import * as Di from "Fx/DependencyInjection";
import { Logging } from "../../Shared/Logging";
import Utilities from "../../Shared/Utilities";
import { ICognitiveServicesSku, IGenericResource } from "./Data.Types";
import ClientResources = require("Resx/ClientResources");

@Di.Class()
export class AIServicesData {
    public armEndpoint = MsPortalFx.getEnvironmentValue("armEndpoint");
    public graphApiVersion = MsPortalFx.getEnvironmentValue("armResourceGraphApiVersion");
    public cogServicesApiVersion = MsPortalFx.getEnvironmentValue("cogServicesApiVersion");

    public listAIServices(subscriptionId: string): Promise<IGenericResource[]> {
        const apiParam = `api-version=${this.graphApiVersion}`
        const uri = `${this.armEndpoint}/providers/Microsoft.ResourceGraph/resources?${apiParam}`;
        const query = `resources | where type == "microsoft.cognitiveservices/accounts" | where kind in ("OpenAI", "AIServices") | order by ['name'] asc`;
        const payload = JSON.stringify({
            query,
            subscriptions: [subscriptionId]
        });
        return Utilities.armPostRequest(uri, payload).then(
            response => (response.data || []),
            reason => {
                const error = reason.jqXHR;
                if (!Utilities.isUserError(error)) {
                    Logging.LogAjaxFailure(error, "getAIServices");
                }

                throw reason;
            }
        );
    }

    public listAIServicesSkus(subscriptionId: string): Promise<ICognitiveServicesSku[]> {
        const apiParam = `api-version=${this.cogServicesApiVersion}`
        const uri = `${this.armEndpoint}/subscriptions/${subscriptionId}/providers/Microsoft.CognitiveServices/skus?${apiParam}`;
        return Utilities.armGetRequest(uri).then(
            response => (response.value || []),
            reason => {
                const error = reason.jqXHR;
                if (!Utilities.isUserError(error)) {
                    Logging.LogAjaxFailure(error, "getAIServicesSkus");
                }

                throw reason;
            }
        );
    }

    public checkAIServicesAvailabilityNew(subscriptionId: string, resourceGroupName: string, aiServicesName: string): Q.Promise<MsPortalFx.ViewModels.ValidationResult> {
        const apiParam = `api-version=${this.cogServicesApiVersion}`
        const uri = MsPortalFx.Data.uriFormatter(this.armEndpoint + "/subscriptions/{sid}/resourceGroups/{rg}/providers/Microsoft.CognitiveServices/accounts/{aiServicesName}?" + apiParam, false)({
            sid: subscriptionId,
            rg: resourceGroupName,
            aiServicesName
        });
        const deferred = Q.defer<MsPortalFx.ViewModels.ValidationResult>();

        Utilities.armGetRequest(uri).then((response: any) => {
            deferred.resolve({
                valid: !response,
                message: response ? ClientResources.validationAIServicesNameAlreadyInUse : ""
            });
        }, (failureResponse: MsPortalFx.Base.Net2.ErrorResponse<any>) => {
            if (failureResponse.jqXHR && failureResponse.jqXHR.status === 404) {
                deferred.resolve({
                    valid: true,
                    message: ""
                });
            }
            else {
                Logging.LogAjaxFailure(failureResponse.jqXHR, "checkAIServicesAvailability");
                deferred.resolve({
                    valid: false,
                    message: ClientResources.validationError
                });
            }
        });

        return deferred.promise;
    }

    public getKeyListForCogServices(subscriptionId: string, resourceGroupName: string, aiServicesName: string): Promise<any> {
        const apiParam = `api-version=${this.cogServicesApiVersion}`
        const uri = MsPortalFx.Data.uriFormatter(this.armEndpoint + "/subscriptions/{sid}/resourceGroups/{rg}/providers/Microsoft.CognitiveServices/accounts/{aiServicesName}/listKeys?" + apiParam, false)({
            sid: subscriptionId,
            rg: resourceGroupName,
            aiServicesName
        });
        return Utilities.armPostRequest(uri, "{}").then(
            response => response,
            reason => {
                Logging.LogAjaxFailure(reason.jqXHR, "getKeyListForCogServices");
                throw reason;
            }
        );
    }
}