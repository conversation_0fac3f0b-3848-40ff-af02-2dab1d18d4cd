﻿import Utilities from "../../Shared/Utilities";
import { Logging } from "../../Shared/Logging";
import Constants = require("../../Shared/Constants");
import ClientResources = require("Resx/ClientResources");
import * as Di from "Fx/DependencyInjection";

@Di.Class()
export class ContainerRegistryData {
    public armEndpoint: string = MsPortalFx.getEnvironmentValue("armEndpoint");
    public containerRegistryApiVersion: string = "api-version=" + MsPortalFx.getEnvironmentValue("containerRegistryApiVersion");

    public getContainerRegistries(subscriptionId: string): Q.Promise<any> {
        const uri = `${this.armEndpoint}/subscriptions/${subscriptionId}/providers/${Constants.machineLearningContainerRegistryProvider}/${Constants.containerRegistry}?${this.containerRegistryApiVersion}`;
        const deferred = Q.defer<any>();
        Utilities.armGetRequest(uri).then((response: any) => {
            deferred.resolve(response.value);
        }, (failureResponse: MsPortalFx.Base.Net2.ErrorResponse<any>) => {
            Logging.LogAjaxFailure(failureResponse.jqXHR, "getContainerRegistries");
            deferred.reject(failureResponse);
        });

        return deferred.promise;
    }

    public checkContainerRegistryAvailability(subscriptionId: string, containerRegistryName: string): Q.Promise<MsPortalFx.ViewModels.ValidationResult> {
        const uri = `${this.armEndpoint}/subscriptions/${subscriptionId}/providers/${Constants.machineLearningContainerRegistryProvider}/checkNameAvailability?${this.containerRegistryApiVersion}`;
        const payload = JSON.stringify({
            "name": containerRegistryName,
            "type": `${Constants.machineLearningContainerRegistryProvider}/${Constants.containerRegistry}`
        });
        const deferred = Q.defer<MsPortalFx.ViewModels.ValidationResult>();

        Utilities.armPostRequest(uri, payload).then((response: any) => {
            if (response.nameAvailable) {
                deferred.resolve({
                    valid: true,
                    message: ""
                });
            } else {
                deferred.resolve({
                    valid: false,
                    message: ClientResources.validationContainerRegistryNameAlreadyInUse
                });
            }
        }, (failureResponse: MsPortalFx.Base.Net2.ErrorResponse<any>) => {
            Logging.LogAjaxFailure(failureResponse.jqXHR, "checkContainerRegistryAvailability");
            deferred.resolve({
                valid: false,
                message: ClientResources.validationError
            });
        });

        return deferred.promise;
    }

    public checkContainerRegistryExists(resourceId: string): Q.Promise<boolean> {
        const uri = `${this.armEndpoint}${resourceId}?${this.containerRegistryApiVersion}`;
        const deferred = Q.defer<boolean>();
        Utilities.armGetRequest(uri).then((_response: any) => {
            deferred.resolve(true);
        }, (failureResponse: MsPortalFx.Base.Net2.ErrorResponse<any>) => {
            if (failureResponse.jqXHR.status === MsPortalFx.Base.Net2.HttpStatusCode.NotFound) {
                deferred.reject(failureResponse);
            } else if (failureResponse.jqXHR.status === MsPortalFx.Base.Net2.HttpStatusCode.Forbidden) {
                // If we get a 403, the user doesn't have read access to the container registry
                // but it does exist so we resolve the promise as true
                deferred.resolve(true);
            } else {
                Logging.LogAjaxFailure(failureResponse.jqXHR, "checkContainerRegistryExists");
                deferred.reject(failureResponse);
            }
        });

        return deferred.promise
    }

    public getContainerRegistry(resourceId: string): Q.Promise<any> {
        const uri = `${this.armEndpoint}${resourceId}?${this.containerRegistryApiVersion}`;
        const deferred = Q.defer<any>();
        Utilities.armGetRequest(uri).then((response: any) => {
            deferred.resolve(response);
        }, (failureResponse: MsPortalFx.Base.Net2.ErrorResponse<any>) => {
            Logging.LogAjaxFailure(failureResponse.jqXHR, "getContainerRegistry");
            deferred.reject(failureResponse);
        });

        return deferred.promise;
    }
}