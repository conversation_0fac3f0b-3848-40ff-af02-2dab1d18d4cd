import * as TemplateBlade from "Fx/Composition/TemplateBlade";
import * as CustomHtml from "Fx/Controls/CustomHtml";
import * as InfoBox from "Fx/Controls/InfoBox";
import * as Button from "Fx/Controls/Button";
import * as DataGrid from "Fx/Controls/DataGrid";
import * as DropDown from "Fx/Controls/DropDown";
import { Workspace } from "../../../MLServicesDataModels/Workspace.types";
import { DataContext } from "../../ResourceArea";
import * as SubscriptionsDropDown from "Fx/Controls/SubscriptionDropDown";
import * as ClientResources from "Resx/ClientResources";
import * as Constants from "../../../Shared/Constants";
import * as DeletedWorkspacesGrid from "./DeletedWorkspacesGrid";
import * as Format from "../../../Shared/Controls/Format";
import Images = MsPortalFx.Base.Images;
import RequiredValidation = MsPortalFx.ViewModels.RequiredValidation;
import { SoftDeletedWorkspace } from "Resx/ClientResources";
import Utilities from "Shared/Utilities";
import HubsNotifications = MsPortalFx.Hubs.Notifications;
import { Experimentation, VariantAssignments } from "Fx/Experimentation";

export interface Parameters {
    assetView?: "AIStudio" | "AML";
}

export interface ReturnData {}

interface SubscriptionWorkspacesResult {
    value?: Workspace[];
}

interface IRecentlyDeletedWorkspacesState {
    error?: string;
    loading?: boolean;
    workspaces?: Workspace[];
}

const CommandResources = SoftDeletedWorkspace.WorkspacesBlade.Commands;
@TemplateBlade.Decorator({
    htmlTemplate: `
    <div class="msportalfx-padding">
        <div data-bind="pcControl: errorMessage"></div>
        <div data-bind="pcControl: subscriptionsDropDown"></div>
        <div class="ext-ml-location-dropdown" data-bind="pcControl: locationsDropDown"></div>
        <div style="display:flex; flex-direction:column">
            <div class ="ext-ml-deleted-workspaces-toolbar ext-layout-docked-top">
                <div data-bind="pcControl: toolbar"></div>
            </div>
            <div class="ext-ml-deleted-workspaces-loading-container msportalfx-overflowY ext-layout-docked-content">
                <div data-bind="pcControl: loadingContainer"></div>
            </div>
            <div class="ext-ml-deleted-workspaces-grid-container msportalfx-overflowY ext-layout-docked-content">
                <div style="height:50vh" data-bind="pcControl: deletedWorkspacesGrid"></div>
            </div>
            <div class="ext-ml-deleted-workspaces-footer-commands ext-layout-docked-bottom">
                <div data-bind="pcControl: footerCommands"></div>
            </div>
        </div>
    </div>
    `,
    styleSheets: ["../../../Shared/Styles/CustomStyles.css", "./RecentlyDeletedWorkspacesBlade.css"]
})
@TemplateBlade.ForContextPane.Decorator({
    width: TemplateBlade.ForContextPane.Width.Large
})
@TemplateBlade.InjectableModel.Decorator(DataContext)
export class RecentlyDeletedWorkspacesBlade {
    public errorMessage: InfoBox.Contract;
    public loadingContainer: CustomHtml.Contract;
    public deletedWorkspacesGrid: DataGrid.Contract<DeletedWorkspacesGrid.DeletedWorkspaceItem>;
    public subscriptionsDropDown: SubscriptionsDropDown.Contract;
    public locationsDropDown: DropDown.ViewModel<string>;
    public footerCommands: CustomHtml.Contract;
    public toolbar: CustomHtml.Contract;
    public title = SoftDeletedWorkspace.Blade.title;
    public subtitle = SoftDeletedWorkspace.Blade.subTitle;
    public context: TemplateBlade.Context<Parameters, DataContext>;
    private _workspaces = ko.observableArray<Workspace>([]);
    private _loadingWorkspaces = ko.observable<boolean>(false);
    private _recentlyDeletedWorkspacesState: IRecentlyDeletedWorkspacesState;

    private _locations = ko.pureComputed(() => this.locationsDropDown.value() as unknown as string[]);
    private _subscriptionId = ko.pureComputed(() => {
        const sub = this.subscriptionsDropDown.value();
        return sub && sub.subscriptionId;
    });

    private _assignments: VariantAssignments;

    constructor(private readonly _experimentation: Experimentation) {}

    public async onInitialize() {
        const { container } = this.context;

        this._initializeSubscriptionsDropdown();
        this._initializeLocationsDropdown();
        this._initializeLoadingContainer();
        this._initializeErrorMessages();
        this._initializeToolbar();
        this._initializeGrid();
        this._initializeFooterCommands();

        const deferred = Q.defer<any>();
        this.subscriptionsDropDown.value.fxOnceAndRun(container, () => {
            this._fetchLocations().then(() => {
                this.subscriptionsDropDown.value.extend({ rateLimit: 500 }).subscribe(container, this._onSubscriptionChange.bind(this));
                this.locationsDropDown.value.extend({ rateLimit: 500 }).subscribe(container, this._fetchSoftDeletedWorkspaces.bind(this));
                deferred.resolve();
            });
        });

        MsPortalFx.Base.Diagnostics.Telemetry.trace({
            action: "RecentlyDeletedWorkspacesBladeOpened",
            source: "RecentlyDeletedWorkspacesBlade"
        });

    }

    private _onSubscriptionChange(): void {
        this._fetchSoftDeletedWorkspaces();
    }

    private _fetchLocations(): Promise<void> {
        const { model } = this.context;
        return Q.all([
            model.machineLearningServicesData.getSubscriptionLocations(this._subscriptionId()),
            model.machineLearningServicesData.getComputeAvailableLocations(this._subscriptionId())
        ])
            .then(([SubscriptionLocationsResult, ComputeLocationsResult]) => {
                const locations = SubscriptionLocationsResult.value || [];
                const availableLocations = ComputeLocationsResult || [];

                let availableLocationObjs = locations.filter((locationObj: any) => availableLocations.indexOf(locationObj.displayName) !== -1);
                if (!availableLocationObjs || availableLocationObjs.length === 0) {
                    availableLocationObjs = locations;
                }

                const locationDropDownItems: DropDown.Item<string>[] = availableLocationObjs.map((location: any) => {
                    return {
                        text: ko.observable(location.displayName),
                        value: location.name
                    };
                });

                this.locationsDropDown.value(null);
                locationDropDownItems.sort((a: DropDown.Item<string>, b: DropDown.Item<string>) => a.value.localeCompare(b.value));
                this.locationsDropDown.items(locationDropDownItems);
            })
            .catch(() => {
                this.locationsDropDown.items([]);
                this.locationsDropDown.value(null);
            });
    }

    private _fetchSoftDeletedWorkspaces(): Promise<void> {
        const { model } = this.context;

        if (!this._subscriptionId()) {
            this.subscriptionsDropDown.triggerValidation();
            return;
        }

        if (!this._locations() || this._locations().length === 0) {
            this.locationsDropDown.triggerValidation();
            return;
        }

        const locations = this._locations();
        const subscriptionId = this._subscriptionId();
        this._setRecentlyDeletedWorkspacesState({ loading: true });

        const getSoftDeletedWorkspaces: Q.Promise<any>[] = [];

        locations.forEach(location =>
            getSoftDeletedWorkspaces.push(
                model.machineLearningServicesData.getSoftDeletedWorkspaces(subscriptionId, Utilities.constructRegionalAPIEndpoint(location))
            )
        );

        return Q.allSettled(getSoftDeletedWorkspaces).then((results: any[]) => {
            const workspaces: Workspace[] = [];
            for (const result of results) {
                const workspacesAvailable = result.state === "fulfilled";
                if (workspacesAvailable) {
                    const workspacesResult = result.value ? (result.value as SubscriptionWorkspacesResult).value || [] : [];
                    workspaces.push(...workspacesResult);
                } else {
                    const errors: string[] = [];
                    if (!workspacesAvailable && result.reason) {
                        errors.push(Utilities.extractErrorMessage(result.reason));
                    }
                    if (errors.length > 0) {
                        this._setRecentlyDeletedWorkspacesState({
                            error: ClientResources.SoftDeletedWorkspace.Blade.errorMessage
                        });
                    }
                }
            }
            this._setRecentlyDeletedWorkspacesState({ loading: false, workspaces });
            this.deletedWorkspacesGrid.refresh();
        });
    }

    private _setRecentlyDeletedWorkspacesState(state: IRecentlyDeletedWorkspacesState): void {
        this._recentlyDeletedWorkspacesState = state;
        this._loadView(state);
    }

    private _loadView(state: IRecentlyDeletedWorkspacesState) {
        const { error, loading, workspaces = [] } = state;

        this.loadingContainer.visible(false);
        this.errorMessage.visible(false);
        this._loadingWorkspaces(loading);

        if (loading) {
            this.loadingContainer.visible(true);
        } else if (error) {
            this.errorMessage.text(error);
            this.errorMessage.visible(true);
        } else {
            this._workspaces(workspaces);
        }
    }

    private _initializeSubscriptionsDropdown(): void {
        const { container } = this.context;

        const subscriptionsDropDownOptions: SubscriptionsDropDown.Options = {
            initialSubscriptionId: "",
            validations: ko.observableArray([new MsPortalFx.ViewModels.RequiredValidation(ClientResources.selectSubscription)]),
            resourceProviders: ko.observable([Constants.machineLearningServicesResourcesProvider]),
            suppressDirtyBehavior: true
        };

        this.subscriptionsDropDown = SubscriptionsDropDown.create(container, subscriptionsDropDownOptions);
    }

    private _initializeLocationsDropdown(): void {
        const { container } = this.context;

        this.locationsDropDown = new DropDown.ViewModel<string>(container, {
            label: ko.observable(ClientResources.resourceLocationColumn),
            items: ko.observableArray([]),
            validations: ko.observableArray([new RequiredValidation(ClientResources.selectLocation)]),
            multiselect: true,
            disabled: ko.observable(false)
        });
    }

    private _initializeLoadingContainer() {
        const { container } = this.context;

        this.loadingContainer = CustomHtml.create(container, {
            htmlTemplate: `<div>${SoftDeletedWorkspace.Blade.loadingText}</div>`,
            visible: this._loadingWorkspaces
        });
    }

    private _initializeErrorMessages(): void {
        const { container } = this.context;

        this.errorMessage = InfoBox.create(container, {
            style: InfoBox.Style.Error,
            text: ko.observable(""),
            visible: ko.observable(false)
        });
    }

    private _initializeToolbar(): void {
        const { container } = this.context;

        const headerText = Format.create(container, { format: this.context.parameters.assetView === "AIStudio" ? SoftDeletedWorkspace.Blade.AIStudio.ToolBar.header : SoftDeletedWorkspace.Blade.ToolBar.header });

        const refreshButton = Button.create(container, {
            ariaLabel: SoftDeletedWorkspace.Blade.ToolBar.Buttons.refresh,
            icon: {
                image: Images.Refresh(),
                position: Button.IconPosition.Left
            },
            style: Button.Style.Secondary,
            text: SoftDeletedWorkspace.Blade.ToolBar.Buttons.refresh,
            onClick: () => {
                this._fetchSoftDeletedWorkspaces();
            },
            disabled: false
        });

        this.toolbar = CustomHtml.create(container, {
            htmlTemplate: `
            <div class="ext-ml-toolbar-container">
                <div data-bind="pcControl: headerText"> </div>
                <div data-bind="pcControl: refreshButton" style="margin-left:auto"> </div>
            </div>
            `,
            innerViewModel: {
                headerText,
                refreshButton
            },
            visible: true
        });
    }

    private _initializeGrid(): void {
        const { container } = this.context;
        this.deletedWorkspacesGrid = DeletedWorkspacesGrid.create(container, {
            assetView: this.context.parameters.assetView,
            workspaces: this._workspaces,
            loading: this._loadingWorkspaces,
        });
    }

    private _initializeFooterCommands(): void {
        const { container } = this.context;

        const recoverButton = Button.create(container, {
            ariaLabel: SoftDeletedWorkspace.Blade.Footer.Buttons.recover,
            disabled: ko.computed(container, () => this.deletedWorkspacesGrid.selection.selectedItems().length === 0),
            style: Button.Style.Primary,
            text: SoftDeletedWorkspace.Blade.Footer.Buttons.recover,
            onClick: async () => {
                const selectedItems = this.deletedWorkspacesGrid.selection.selectedItems();
                const recoverWorkspaces = await this._recoverWorkspaces(selectedItems);
                const recoverNotificationMessages: BatchMessages = {
                    Failure: [{ error: { ...CommandResources.RecoverNotifications.Failure }, includeResponseErrorMessage: true }],
                    Success: { ...CommandResources.RecoverNotifications.Success },
                    InProgress: { ...CommandResources.RecoverNotifications.InProgress }
                };
                MsPortalFx.Base.Diagnostics.Telemetry.trace({
                    action: "RecoverSoftDeletedWorkspaces",
                    source: "RecentlyDeletedWorkspacesBlade",
                    data: {
                        workspacesCount: selectedItems.length,
                        subscriptionId: this._subscriptionId()
                    }
                });
                trackBatchedQuery(recoverWorkspaces, recoverNotificationMessages, undefined, { action: "Recover" });
                container.closeCurrentBlade();
            }
        });

        const purgeButton = Button.create(container, {
            ariaLabel: SoftDeletedWorkspace.Blade.Footer.Buttons.purge,
            disabled: ko.computed(container, () => this.deletedWorkspacesGrid.selection.selectedItems().length === 0),
            style: Button.Style.Danger,
            text: SoftDeletedWorkspace.Blade.Footer.Buttons.purge,
            onClick: async () => {
                const selectedItems = this.deletedWorkspacesGrid.selection.selectedItems();
                const purgeWorkspaces = await this._purgeWorkspaces(selectedItems);
                const purgeNotificationMessages: BatchMessages = {
                    Failure: [{ error: { ...CommandResources.PurgeNotifications.Failure }, includeResponseErrorMessage: true }],
                    Success: { ...CommandResources.PurgeNotifications.Success },
                    InProgress: { ...CommandResources.PurgeNotifications.InProgress }
                };
                MsPortalFx.Base.Diagnostics.Telemetry.trace({
                    action: "PurgeSoftDeletedWorkspaces",
                    source: "RecentlyDeletedWorkspacesBlade",
                    data: {
                        workspacesCount: selectedItems.length,
                        subscriptionId: this._subscriptionId()
                    }
                });
                trackBatchedQuery(purgeWorkspaces, purgeNotificationMessages, undefined, { action: "Purge" });
                container.closeCurrentBlade();
            }
        });

        const cancelButton = Button.create(container, {
            ariaLabel: SoftDeletedWorkspace.Blade.Footer.Buttons.cancel,
            disabled: this._loadingWorkspaces,
            style: Button.Style.Secondary,
            text: SoftDeletedWorkspace.Blade.Footer.Buttons.cancel,
            onClick: () => {
                container.closeCurrentBlade();
            }
        });

        this.footerCommands = CustomHtml.create(container, {
            htmlTemplate: `
            <div>
                <div data-bind="pcControl: recoverButton"> </div>
                <div data-bind="pcControl: purgeButton"> </div>
                <div data-bind="pcControl: cancelButton"> </div>
            </div>
            `,
            innerViewModel: {
                recoverButton,
                purgeButton,
                cancelButton
            }
        });
    }

    private async _purgeWorkspaces(selectedWorkspaces: DeletedWorkspacesGrid.DeletedWorkspaceItem[]): Promise<any> {
        const { model } = this.context;
        return selectedWorkspaces.map(item => {
            return model.machineLearningServicesData.purgeWorkspace(
                this._subscriptionId(),
                item.resourceGroup,
                Utilities.updateDiscoveryURL(item.properties.discoveryUrl),
                item.properties.SoftDeleteUniqueId
            );
        });
    }

    private async _recoverWorkspaces(selectedWorkspaces: DeletedWorkspacesGrid.DeletedWorkspaceItem[]): Promise<any> {
        const { model } = this.context;

        const getValidatedWorkspaces = async () => {
            const validWorkspaces: DeletedWorkspacesGrid.DeletedWorkspaceItem[] = [];

            for (const ws of selectedWorkspaces) {
                const validateWorkspacePromises: Promise<boolean>[] = [];
                const workspaceName = ws.name;

                // Validation - include accompanying error message for each resource check
                const validationFailureTitle = CommandResources.ValidateResources.Failure.title.format(workspaceName);
                const validatingBatchMessages: BatchMessages = {
                    Failure: [],
                    InProgress: {
                        message: `${CommandResources.ValidateResources.InProgress.message}`.format("{0}", workspaceName),
                        title: CommandResources.ValidateResources.InProgress.title
                    },
                    Success: {
                        message: `${CommandResources.ValidateResources.Success.message}`.format(workspaceName),
                        title: CommandResources.ValidateResources.Success.title
                    }
                };

                // Validate Storage Account
                validateWorkspacePromises.push(model.storageAccountData.checkStorageAccountExists(`${ws.properties.storageAccount}`));
                validatingBatchMessages.Failure.push({
                    error: {
                        title: validationFailureTitle,
                        message: CommandResources.ValidateResources.StorageAccount.Failure.message.format(ws.properties.storageAccount)
                    }
                });

                // Validate Key Vault
                validateWorkspacePromises.push(model.keyVaultData.checkKeyVaultExists(`${ws.properties.keyVault}`));
                validatingBatchMessages.Failure.push({
                    error: {
                        title: validationFailureTitle,
                        message: CommandResources.ValidateResources.KeyVault.Failure.message.format(ws.properties.keyVault)
                    }
                });

                // Validate Application Insights
                validateWorkspacePromises.push(model.appInsightsData.checkComponentExists(`${ws.properties.applicationInsights}`));
                validatingBatchMessages.Failure.push({
                    error: {
                        title: validationFailureTitle,
                        message: CommandResources.ValidateResources.AppInsights.Failure.message.format(ws.properties.applicationInsights)
                    }
                });

                await trackBatchedQuery(
                    validateWorkspacePromises,
                    validatingBatchMessages,
                    () => {
                        validWorkspaces.push(ws);
                    },
                    { action: "Validation" }
                );
            }
            return validWorkspaces;
        };

        return getValidatedWorkspaces().then(workspaces =>
            workspaces.map(item =>
                model.machineLearningServicesData.recoverWorkspace(
                    this._subscriptionId(),
                    item.resourceGroup,
                    Utilities.updateDiscoveryURL(item.properties.discoveryUrl),
                    item.properties.SoftDeleteUniqueId
                )
            )
        );
    }
}

interface IMessage {
    message: string;
    title: string;
}

interface IErrorMessage {
    error: IMessage;
    includeResponseErrorMessage?: boolean;
}

interface BatchMessages {
    Failure: IErrorMessage[];
    InProgress: IMessage;
    Success: IMessage;
}

function trackBatchedQuery(
    promises: Promise<any>[],
    messages: BatchMessages,
    onSuccess?: () => void,
    telemetryData?: { action: string }
): Promise<unknown> {
    if (promises.length === 0) {
        return;
    }

    // Show in progress to let user know notifications have been kicked off.
    const notification = new HubsNotifications.ClientNotification({
        title: messages.InProgress.title,
        description: messages.InProgress.message.format(promises.length),
        status: HubsNotifications.NotificationStatus.InProgress
    });

    notification.publish();

    // Alert the user if all suceeded or all / some failed
    return Q.allSettled(promises).then(promiseStates => {
        const failedIndices = promiseStates.map((promiseState, ind) => (promiseState.state === "rejected" ? ind : -1)).filter(ind => ind >= 0);
        if (failedIndices.length && messages.Failure.length) {
            failedIndices.forEach(ind => {
                notification.title = messages.Failure[ind].error.title;
                notification.description = messages.Failure[ind].error.message.format(failedIndices.length, promises.length);
                notification.status = HubsNotifications.NotificationStatus.Error;
                const errors = failedIndices
                    .map(index => {
                        let errorMessage: string = Utilities.extractErrorMessage(promiseStates[index].reason.jqXHR);
                        if (errorMessage === CommandResources.Generic.Error.message) {
                            errorMessage = null;
                        }
                        return errorMessage;
                    })
                    .filter(error => !!error);
                if (errors.length && messages.Failure[ind].includeResponseErrorMessage) {
                    notification.description += "\n" + errors.join("\n");
                }
                if (telemetryData) {
                    MsPortalFx.Base.Diagnostics.Telemetry.trace({
                        action: `${telemetryData.action}Failure`,
                        source: "RecentlyDeletedWorkspacesBlade",
                        data: {
                            errorDescription: notification.description
                        }
                    });
                }
            });
        } else {
            notification.title = messages.Success.title;
            notification.description = messages.Success.message.format(promises.length);
            notification.status = HubsNotifications.NotificationStatus.Success;
            if (onSuccess) {
                onSuccess();
            }
            if (telemetryData) {
                MsPortalFx.Base.Diagnostics.Telemetry.trace({
                    action: `${telemetryData.action}Success`,
                    source: "RecentlyDeletedWorkspacesBlade",
                    data: {
                        successCount: promises.length - failedIndices.length
                    }
                });
            }
        }
        notification.publish();
    });
}
