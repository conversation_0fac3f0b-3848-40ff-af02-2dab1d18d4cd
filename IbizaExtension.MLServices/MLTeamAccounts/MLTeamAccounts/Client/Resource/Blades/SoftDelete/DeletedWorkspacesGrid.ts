import * as TemplateBlade from "Fx/Composition/TemplateBlade";
import * as DataGrid from "Fx/Controls/DataGrid";
import ClientResources = require("Resx/ClientResources");
import { Identity, Workspace, WorkspaceProperties } from "../../../MLServicesDataModels/Workspace.types";
import Icons = require("../../../Shared/Icons");
import * as Format from "../../../Shared/Controls/Format";
import { ArmId } from "Fx/ResourceManagement";
import { asMappedKind, isAIStudioKind } from "Shared/ViewAgnostic/Utilities";

export interface IDeletedWorkspacesGridOptions {
    assetView?: "AIStudio" | "AML";
    loading?: KnockoutObservable<boolean>;
    workspaces?: KnockoutObservableArray<Workspace>;
    onError?: (errorMessage: string) => void;
}

export interface DeletedWorkspaceItem {
    deletedDate: Date|string;
    id: string;
    name: string;
    purgeDate: Date|string;
    identity: Identity;
    properties: WorkspaceProperties;
    location: string;
    resourceGroup: string;
    kind: string;
}

enum Columns {
    DeletedDate = "deletedDate",
    Name = "name",
    PurgeDate = "purgeDate",
    ResourceGroup = "resourceGroup"
}

export function create(container: TemplateBlade.Container, options: IDeletedWorkspacesGridOptions): DataGrid.Contract<DeletedWorkspaceItem> {
    const { loading, workspaces: unfiltered } = options;
    const workspaces = ko.computed(container, () => {
        const assetView = options.assetView;
        if (assetView) {
            const filterByAssetView = assetView === "AIStudio" ? (ws: Workspace) => isAIStudioKind(asMappedKind(ws.kind)) : (ws: Workspace) => !isAIStudioKind(asMappedKind(ws.kind));
            return unfiltered().filter(filterByAssetView);
        }
        return unfiltered();
    });

    const grid = DataGrid.create<DeletedWorkspaceItem>(container, {
        header: {
            visible: true
        },
        noDataMessage: ClientResources.SoftDeletedWorkspace.Grid.noWorkspacesFound,
        ariaLabel: ClientResources.SoftDeletedWorkspace.Grid.ariaLabel,
        columns: getGridColumns(),
        selection: {
            selectionMode: DataGrid.SelectionMode.Multiple,
            canSelectAllItems: () => true
        },
        dataSource: params => {
            const deletedWorkspaces = workspaces().map(ws => buildGridItem(ws));

            const sortOrder = params.sortOrder;
            if (sortOrder?.length && deletedWorkspaces?.length) {
                return deletedWorkspaces.sort((nodeA, nodeB): number => {
                    let comparison = 0;
                    sortOrder.some(({ id, direction }) => {
                        let a;
                        let b;
                        switch (id) {
                            case Columns.PurgeDate:
                                a = new Date(nodeA.item.purgeDate);
                                b = new Date(nodeB.item.purgeDate);
                                break;
                            case Columns.DeletedDate:
                                a = new Date(nodeA.item.deletedDate);
                                b = new Date(nodeB.item.deletedDate);
                                break;
                            default:
                                a = nodeA.item.name;
                                b = nodeB.item.name;
                                break;
                        }
                        comparison = MsPortalFx.compare(a, b);
                        comparison = direction * comparison;
                        return comparison !== 0;
                    });

                    return comparison;
                });
            }
            return deletedWorkspaces;
        },
        visible: ko.computed(container, () => {
            return !loading()
        }),
        disabled: ko.computed(container, () => false)
    });

    return grid;
}

function buildGridItem(workspace: Workspace): DataGrid.DataNode<DeletedWorkspaceItem, string> {
        const deletedDate = new Date(workspace.properties.softDeletedAt);
        const purgeDate = new Date(workspace.properties.scheduledPurgeDate);
        const gridItem: DeletedWorkspaceItem = {
            id: workspace.id,
            name: workspace.name,
            deletedDate: deletedDate.toLocaleDateString(),
            purgeDate: purgeDate.toLocaleDateString(),
            identity: workspace.identity,
            location: workspace.location,
            properties: workspace.properties,
            resourceGroup: ArmId.parse(workspace.id).resourceGroup,
            kind: workspace.kind
        };
        return {
            id: workspace.id,
            item: gridItem
        };
}

function getGridColumns(): DataGrid.ColumnDefinition<DeletedWorkspaceItem>[] {
    return [
        {
            canSort: true,
            cell: {
                template: `
                <div style="display:flex">
                    <div data-bind="image: workspaceIconViewModel" style="width:16px;height:16px;"></div>
                    <div data-bind='pcControl: workspaceNameViewModel' style="margin-left:1em"></div>
                </div>
                `,
                viewModel: (item: DeletedWorkspaceItem, container) => {
                    const workspaceIconViewModel = isAIStudioKind(asMappedKind(item.kind)) ? Icons.Icons.aiStudioIcon : Icons.Icons.workspacesIcon;
                    const workspaceNameViewModel = Format.create(container, { format: item.name });
                    return {
                        workspaceIconViewModel,
                        workspaceNameViewModel
                    };
                }
            },
            header: ClientResources.SoftDeletedWorkspace.Grid.Columns.name,
            id: Columns.Name,
            sortDirection: DataGrid.SortDirection.Unsorted,
            type: "Template",
            width: "45%"
        },
        {
            canSort: true,
            cell: {
                text: (item: DeletedWorkspaceItem) => {
                    return item.resourceGroup;
                }
            },
            header: ClientResources.SoftDeletedWorkspace.Grid.Columns.resourceGroup,
            id: Columns.ResourceGroup,
            sortDirection: DataGrid.SortDirection.Unsorted,
            type: "Text",
            width: "30%"
        },
        {
            canSort: true,
            cell: {
                text: (item: DeletedWorkspaceItem) => {
                    return item.deletedDate;
                }
            },
            header: ClientResources.SoftDeletedWorkspace.Grid.Columns.deletedDate,
            id: Columns.DeletedDate,
            sortDirection: DataGrid.SortDirection.Descending,
            type: "Text",
            width: "20%"
        },
        {
            canSort: true,
            cell: {
                text: (item: DeletedWorkspaceItem) => {
                    return item.purgeDate;
                }
            },
            header: ClientResources.SoftDeletedWorkspace.Grid.Columns.purgeDate,
            id: Columns.PurgeDate,
            sortDirection: DataGrid.SortDirection.Unsorted,
            type: "Text",
            width: "40%"
        }
    ];
}