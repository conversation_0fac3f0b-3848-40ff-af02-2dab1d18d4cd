﻿import * as TemplateBlade from "Fx/Composition/TemplateBlade";
import * as ClientResources from "Resx/ClientResources";
import * as Section from "Fx/Controls/Section";
import * as CustomHtml from "Fx/Controls/CustomHtml";
import * as TextBlock from "Fx/Controls/TextBlock";
import * as InfoBox from "Fx/Controls/InfoBox";
import { WorkspaceMappedKind } from "../../../Shared/Enums";

import { DataContext } from "../../ResourceArea";
import Constants = require("../../../Shared/Constants");
import { asMappedKind, dedupe } from "../../../Shared/ViewAgnostic/Utilities";

import ExtensionDefinition = require("../../../_generated/ExtensionDefinition");

import MLServicesDataModels = require("../../../MLServicesDataModels/MachineLearningServices");
import * as PropertiesToolbar from "./PropertiesToolbar";

import * as CopyableLabel from "Fx/Controls/CopyableLabel";
import { ArmId } from "Fx/ResourceManagement";
import { BladeReferences } from "Fx/Composition";
import Utilities from "Shared/Utilities";
import * as NoPdlUiHelpers from "Resource/Create/Utilities/NoPdlUiHelpers";
import { AkaKey } from "Shared/AkaKey";
import { StorageAccountAccessType } from "Resource/Create/ViewModels/CreateMachineLearningModels";
import * as RadioButtons from "Fx/Controls/RadioButtons";

/**
 * Contract for parameters that will be passed to parameters blade.
 */
export interface Parameters {
    readonly id: string;
}

@TemplateBlade.Decorator({
    htmlTemplate: `
    <div class='msportalfx-padding' data-bind='pcControl: contentSection'></div>
    `,
    styleSheets: ["./PropertiesStyles.css"],
    isPinnable: false,
    forAsset: { assetType: ExtensionDefinition.AssetTypeNames.machineLearningServices, assetIdParameter: "id" }
})
@TemplateBlade.InjectableModel.Decorator(DataContext)
export class PropertiesBlade {
    public title = "";
    public subtitle = "";
    public context: TemplateBlade.Context<Parameters, DataContext>;

    public contentSection: Section.Contract;

    private _machineLearningServicesEntityView: MsPortalFx.Data.EntityView<MLServicesDataModels.MachineLearningServices, string>;
    private _machineLearningServices: MLServicesDataModels.MachineLearningServices = null;
    private _kind: KnockoutObservable<WorkspaceMappedKind> = ko.observable<WorkspaceMappedKind>(WorkspaceMappedKind.Default);
    private _machineLearningStorageAccountId: KnockoutObservable<string> = ko.observable<string>("");
    private _machineLearningStorageAccountIds: KnockoutObservableArray<string> = ko.observableArray<string>([]);
    private _machineLearningAppInsightsId: KnockoutObservable<string> = ko.observable<string>("");
    private _machineLearningKeyVaultId: KnockoutObservable<string> = ko.observable<string>("");
    private _machineLearningContainerRegistryId: KnockoutObservable<string> = ko.observable<string>("");
    private _machineLearningWorkspaceId: KnockoutObservable<string> = ko.observable<string>("");
    private _machineLearningWorkspaceHubId: KnockoutObservable<string> = ko.observable<string>("");
    private _machineLearningLocation: KnockoutObservable<string> = ko.observable<string>("");
    private _machineLearningWorkspaceCreationDate: KnockoutObservable<string> = ko.observable<string>("");
    private _machineLearningSubscriptionName: KnockoutObservable<string> = ko.observable<string>("");
    private _machineLearningResourceId: KnockoutObservable<string> = ko.observable<string>("");
    private _saving: KnockoutObservable<boolean> = ko.observable(false);
    private _errorMessage: InfoBox.Contract;
    private _storageAccountAccessSection: Section.Contract;
    private _storageAccountAccessTypeRadioButtons: RadioButtons.Contract<StorageAccountAccessType>;
    private _systemDatastoresAuthMode: KnockoutObservable<StorageAccountAccessType> = ko.observable<StorageAccountAccessType>("accessKey");
    private _authModeMapping: Record<string, StorageAccountAccessType> = {
        accesskey: "accessKey",
        identity: "identity"
    };

    public onInitialize() {
        const { parameters } = this.context;
        this._setInitialState();
        this._initializeContent();
        return this._loadMachineLearningServices(parameters.id);
    }

    private _setInitialState(): void {
        const { model, container } = this.context;
        this._machineLearningServicesEntityView = model.machineLearningServicesData.machineLearningServicesEntity.createView(container);
    }

    private _loadMachineLearningServices(resourceId: string): Q.Promise<any> {
        const { container } = this.context;
        const deferred = Q.defer();

        const machineLearningServicesPromise = this._machineLearningServicesEntityView.fetch(resourceId);
        const permissionsPromise = MsPortalFx.Base.Security.hasPermission(resourceId, [
            ExtensionDefinition.AssetTypes.MachineLearningServices.Permissions.readServices
        ]);

        Q.all([permissionsPromise, machineLearningServicesPromise]).then(
            values => {
                const hasAccess = values[0];

                this._machineLearningServices = this._machineLearningServicesEntityView.item();
                this._kind(asMappedKind(this._machineLearningServices.kind?.() || ""));
                this._machineLearningStorageAccountId(this._machineLearningServices.properties().storageAccount());
                this._machineLearningStorageAccountIds(this._machineLearningServices.properties().storageAccounts?.() || []);
                this._machineLearningContainerRegistryId(this._machineLearningServices.properties().containerRegistry?.() || "");
                this._machineLearningKeyVaultId(this._machineLearningServices.properties().keyVault());
                this._machineLearningAppInsightsId(this._machineLearningServices.properties().applicationInsights());
                this._machineLearningWorkspaceId(this._machineLearningServices.properties().workspaceId());
                this._machineLearningWorkspaceHubId(this._machineLearningServices.properties().hubResourceId?.());
                this._machineLearningResourceId(this._machineLearningServices.id());
                this._machineLearningLocation(this._machineLearningServices.location());
                this._systemDatastoresAuthMode(this._authModeMapping[this._machineLearningServices.properties().systemDatastoresAuthMode()]);
                if (this._machineLearningServices.properties().creationTime) {
                    this._machineLearningWorkspaceCreationDate(this._machineLearningServices.properties().creationTime().toString());
                }

                this.title = this._machineLearningServices.name();

                if (!hasAccess) {
                    container.unauthorized();
                } else {
                    deferred.resolve();
                }
            },
            reason => {
                deferred.reject(reason);
            }
        );
        return deferred.promise;
    }

    private _update(model: DataContext, id: string): Promise<any> {
        this._saving(true);
        this._errorMessage.visible(false);
        return model.machineLearningServicesData.updateWorkspace(id, { systemDatastoresAuthMode: this._systemDatastoresAuthMode() }).then(
            () => {
                this._errorMessage.visible(false);
                this._saving(false);
            },
            error => {
                this._errorMessage.text(Utilities.extractErrorMessage(error.jqXHR));
                this._errorMessage.visible(true);
                this._saving(false);
            }
        );
    }
    private _createStorageAccountAccessControls(): void {
        const { container } = this.context;
        this._storageAccountAccessTypeRadioButtons = RadioButtons.create(container, {
            items: [
                { text: ClientResources.AdvancedTab.Section.StorageAccountAccess.credentialBasedText, value: "accessKey" },
                { text: ClientResources.AdvancedTab.Section.StorageAccountAccess.identityBasedText, value: "identity" }
            ],
            value: this._systemDatastoresAuthMode,
            label: ClientResources.AdvancedTab.Section.StorageAccountAccess.radioGroupTitle,
            singleItemPerLine: true,
            suppressDirtyBehavior: true
        });

        this._storageAccountAccessTypeRadioButtons.value.subscribe(container, newValue => {
            this._systemDatastoresAuthMode(newValue);
        });

        const identityBasedAccessWarningText = NoPdlUiHelpers.createLink({
            text: Utilities.formatString(
                ClientResources.AdvancedTab.Section.StorageAccountAccess.identityBasedWarningText,
                `<strong>${ClientResources.AdvancedTab.Section.StorageAccountAccess.important}:</strong>`,
                "<strong>Storage Blob Data Contributor</strong>",
                "<strong>Storage File Privileged Contributor</strong>",
                `<strong>${ClientResources.AdvancedTab.Section.StorageAccountAccess.individualUsers}</strong>`
            ),
            linkText: ClientResources.AdvancedTab.Section.StorageAccountAccess.identityBasedWarningLearnMore,
            linkUri: ko.computed(container, () =>
                this._kind() === WorkspaceMappedKind.Hub
                    ? `https://aka.ms/${AkaKey.AiHubStorageAccountRoleAssignments}`
                    : `https://aka.ms/${AkaKey.AMLStorageAccountRoleAssignments}`
            )
        });

        const identityBasedAccessWarning = InfoBox.create(container, {
            style: InfoBox.Style.Warning,
            text: identityBasedAccessWarningText,
            visible: ko.computed(container, () => this._systemDatastoresAuthMode() === "identity")
        });

        this._storageAccountAccessSection = Section.create(container, {
            children: [
                NoPdlUiHelpers.createSectionHeader(ClientResources.AdvancedTab.Section.StorageAccountAccess.header, true, true),
                NoPdlUiHelpers.createTextContent(ClientResources.AdvancedTab.Section.StorageAccountAccess.description, true),
                this._storageAccountAccessTypeRadioButtons,
                identityBasedAccessWarning
            ],
            leftLabelPosition: true,
            visible: ko.computed(container, () => [WorkspaceMappedKind.Default, WorkspaceMappedKind.Hub].includes(this._kind()))
        });
    }

    private _refresh = async () => {
        await this._machineLearningServicesEntityView.refresh();
        await this.onInitialize();
    };

    // Initializes properties
    private _initializeContent(): void {
        const { container, parameters, model } = this.context;
        const resourceId = parameters.id;
        const resource = ArmId.parse(resourceId);

        const storageAccountIds = ko.pureComputed<string>(() => {
            const storageId = this._machineLearningStorageAccountId();
            const associatedStorageIds = this._machineLearningStorageAccountIds();
            return dedupe([storageId, ...associatedStorageIds]).join(",");
        });

        const storageAccountIdLabel = this._createCopyableLabel(container, ClientResources.propertiesBladeStorageAccountIdLabel, storageAccountIds);

        const appInsightsIdLabel = this._createCopyableLabel(
            container,
            ClientResources.propertiesBladeApplicationInsightsLabel,
            this._machineLearningAppInsightsId
        );

        const changeAppInsightsButton = CustomHtml.create(container, {
            htmlTemplate: `<div><a role="button" data-bind="fxclick: onChange, text: text, attr: { aria-label: ariaLabel }"></a></div>`,
            innerViewModel: {
                ariaLabel: ClientResources.AssociatedResource.AppInsights.Properties.changeText,
                onChange: () => {
                    // TODO: check if this is a hub and redirect to the hub resource
                    container.openContextPane(
                        BladeReferences.forBlade("ChangeAppInsightsPane.ReactView").createReference({
                            parameters: {
                                id: resourceId
                            },
                            onClosed: (...args) => {
                                const updatedAppInsightsId = (args as any[])[1];
                                if (updatedAppInsightsId) {
                                    this._machineLearningAppInsightsId(updatedAppInsightsId);
                                }
                            }
                        })
                    );
                },
                text: ClientResources.AssociatedResource.AppInsights.Properties.changeText
            }
        });

        const changeContainerRegistryButton = CustomHtml.create(container, {
            htmlTemplate: `<div><a role="button" data-bind="fxclick: onChange, text: text, attr: { aria-label: ariaLabel }"></a></div>`,
            innerViewModel: {
                ariaLabel: ClientResources.AssociatedResource.ContainerRegistry.Properties.changeText,
                onChange: () => {
                    // TODO: check if this is a hub and redirect to the hub resource
                    container.openContextPane(
                        BladeReferences.forBlade("ChangeContainerRegistryPane.ReactView").createReference({
                            parameters: {
                                id: resourceId
                            },
                            onClosed: (...args) => {
                                const updatedContainerRegistryId = (args as any[])[1];
                                if (updatedContainerRegistryId) {
                                    this._machineLearningContainerRegistryId(updatedContainerRegistryId);
                                }
                            }
                        })
                    );
                },
                text: ClientResources.AssociatedResource.ContainerRegistry.Properties.changeText
            }
        });

        this._errorMessage = InfoBox.create(this.context.container, {
            style: InfoBox.Style.Error,
            text: ko.observable(""),
            visible: ko.observable(false)
        });

        const keyVaultIdLabel = this._createCopyableLabel(container, ClientResources.propertiesBladeKeyVaultIdLabel, this._machineLearningKeyVaultId);

        const containerRegistryIdLabel = this._createCopyableLabel(
            container,
            ClientResources.propertiesBladeContainerRegistryIdLabel,
            this._machineLearningContainerRegistryId
        );

        const workspaceIdLabel = this._createCopyableLabel(
            container,
            ClientResources.propertiesBladeWorkspaceIdLabel,
            this._machineLearningWorkspaceId
        );
        // When workspace is a project, we keep workspaceId as "Workspace Id" and add a separate label for "Workspace hub Id", pointing to hubResourceId
        const workspaceHubIdLabel = this._createCopyableLabel(
            container,
            ClientResources.propertiesBladeWorkspaceHubIdLabel,
            this._machineLearningWorkspaceHubId
        );

        const resourceIdLabel = this._createCopyableLabel(container, ClientResources.propertiesBladeResourceIdLabel, this._machineLearningResourceId);

        const subscriptionIdLabelOptions = {
            label: ClientResources.propertiesBladeSubscriptionIdLabel,
            value: ko.computed(container, () => {
                return resource.subscription;
            }),
            readOnly: true
        };
        const subscriptionIdLabel = CopyableLabel.create(container, subscriptionIdLabelOptions);

        const subscriptionNameLabelOptions = {
            label: ClientResources.propertiesBladeSubscriptionNameLabel,
            value: ko.computed(container, () => {
                resource.subscription;
                MsPortalFx.Azure.getSubscriptionInfo(resource.subscription).then((subscription: MsPortalFx.Azure.Subscription) => {
                    this._machineLearningSubscriptionName(subscription ? subscription.displayName : "");
                });
                return this._machineLearningSubscriptionName();
            }),
            readOnly: true
        };
        const subscriptionNameLabel = CopyableLabel.create(container, subscriptionNameLabelOptions);

        const resourceGroupNameLabelOptions = {
            label: ClientResources.propertiesBladeResourceGroupLabel,
            value: ko.computed(container, () => {
                return resource.resourceGroup;
            }),
            readOnly: true
        };
        const resourceGroupNameLabel = CopyableLabel.create(container, resourceGroupNameLabelOptions);

        const toolbar = this._createSectionFromElements(
            container,
            [
                PropertiesToolbar.create(this.context, {
                    saving: this._saving,
                    refresh: async () => {
                        await this._refresh();
                    },
                    save: async () => {
                        await this._update(model, parameters.id);
                        if (!this._errorMessage.visible()) {
                            await this._refresh();
                        }
                    },
                    discard: async () => {
                        await this._refresh();
                    }
                })
            ],
            ko.computed(container, () => [WorkspaceMappedKind.Default, WorkspaceMappedKind.Hub].includes(this._kind()))
        );

        this._createStorageAccountAccessControls();

        const dateLabelText = this._createTextBlockFromText(container, ClientResources.propertiesBladeCreationDateLabel);
        const dateLabelValue = this._createTextBlockFromObservable(container, this._machineLearningWorkspaceCreationDate);
        const locationLabelText = this._createTextBlockFromText(container, ClientResources.propertiesBladeLocationLabel);
        const locationLabelValue = this._createTextBlockFromObservable(container, this._machineLearningLocation);
        const workspaceHubIdLabelSection = this._createSectionFromElements(
            container,
            [workspaceHubIdLabel],
            ko.computed(container, () => this._kind() === WorkspaceMappedKind.Project)
        );
        const appInsightsIdLabelSectionHub = this._createSectionFromElements(
            container,
            [appInsightsIdLabel, changeAppInsightsButton],
            ko.computed(container, () => this._kind() === WorkspaceMappedKind.Hub)
        );
        const appInsightsIdLabelSectionProject = this._createSectionFromElements(
            container,
            [appInsightsIdLabel],
            ko.computed(container, () => this._kind() === WorkspaceMappedKind.Project)
        );
        const containerRegistryIdLabelSectionHub = this._createSectionFromElements(
            container,
            [containerRegistryIdLabel, changeContainerRegistryButton],
            ko.computed(container, () => this._kind() === WorkspaceMappedKind.Hub)
        );
        const containerRegistryIdLabelSectionProject = this._createSectionFromElements(
            container,
            [containerRegistryIdLabel],
            ko.computed(container, () => this._kind() === WorkspaceMappedKind.Project)
        );

        this.contentSection = Section.create(container, {
            children: [
                this._createSectionFromElements(container, [this._errorMessage]),
                this._createSectionFromElements(container, [toolbar]),
                this._createSectionFromElements(container, [dateLabelText, dateLabelValue]),
                this._createSectionFromElements(container, [locationLabelText, locationLabelValue]),
                this._createSectionFromElements(container, [workspaceIdLabel]),
                workspaceHubIdLabelSection,
                this._createSectionFromElements(container, [subscriptionIdLabel]),
                this._createSectionFromElements(container, [subscriptionNameLabel]),
                this._createSectionFromElements(container, [resourceGroupNameLabel]),
                this._createSectionFromElements(container, [resourceIdLabel]),
                this._createSectionFromElements(container, [storageAccountIdLabel]),
                containerRegistryIdLabelSectionHub,
                containerRegistryIdLabelSectionProject,
                this._createSectionFromElements(container, [keyVaultIdLabel]),
                appInsightsIdLabelSectionHub,
                appInsightsIdLabelSectionProject,
                this._storageAccountAccessSection
            ]
        });
    }

    private _createCopyableLabel(
        container: TemplateBlade.Container,
        labelText: string | KnockoutObservable<string> | KnockoutComputed<string>,
        observableValue: KnockoutObservable<string> | KnockoutComputed<string>
    ): CopyableLabel.Contract {
        const properties = {
            label: labelText,
            value: ko.computed(container, () => {
                return !MsPortalFx.isNullOrUndefined(observableValue()) && observableValue() !== ""
                    ? observableValue()
                    : Constants.pendingStringValue;
            }),
            readOnly: true
        };

        return CopyableLabel.create(container, properties);
    }

    private _createSectionFromElements(
        container: TemplateBlade.Container,
        elements: MsPortalFx.Control[],
        visible?: boolean | KnockoutObservableBase<boolean>
    ) {
        return Section.create(container, {
            children: elements,
            cssClass: "ext-caption-text-pair-section",
            visible
        });
    }

    private _createTextBlockFromObservable(container: TemplateBlade.Container, observableValue: KnockoutObservable<string>): TextBlock.Contract {
        return TextBlock.create(container, {
            text: ko.computed(container, () => {
                return !MsPortalFx.isNullOrUndefined(observableValue()) && observableValue() !== ""
                    ? observableValue()
                    : Constants.pendingStringValue;
            })
        });
    }

    private _createTextBlockFromText(container: TemplateBlade.Container, textValue: string): TextBlock.Contract {
        return TextBlock.create(container, {
            text: textValue
        });
    }
}
