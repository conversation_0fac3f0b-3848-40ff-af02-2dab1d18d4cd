import * as Toolbar from "Fx/Controls/Toolbar";
import { LifetimeManager } from "Fx/Lifetime";
import * as ClientResources from "Resx/ClientResources";

interface Context {
    container: LifetimeManager;
}

interface Options {
    saving: KnockoutObservable<boolean>;
    refresh: () => void;
    discard: () => void;
    save: () => void;
}

export function create(context: Context, options: Options): Toolbar.Contract {
    const { refresh, discard, save, saving} = options;
    const toolbarItems = [
        Toolbar.ToolbarItems.createBasicButton(context.container, {
            label: ko.computed(context.container, () => saving()
                ? ClientResources.Properties.saving
                : ClientResources.Properties.save),
            icon: MsPortalFx.Base.Images.Save(),
            disabled: ko.computed(context.container, () => saving()),
            onClick: save
        }),
        Toolbar.ToolbarItems.createBasicButton(context.container, {
            label: ClientResources.Properties.discard,
            icon: MsPortalFx.Base.Images.Close(),
            disabled: ko.computed(context.container, () => saving()),
            onClick: discard
        }),
        Toolbar.ToolbarItems.createBasicButton(context.container, {
            label: ClientResources.Properties.refresh,
            icon: MsPortalFx.Base.Images.Refresh(),
            disabled: saving,
            onClick: refresh
        })];

    return Toolbar.create(context.container, { items: toolbarItems, showLabels: true })
}