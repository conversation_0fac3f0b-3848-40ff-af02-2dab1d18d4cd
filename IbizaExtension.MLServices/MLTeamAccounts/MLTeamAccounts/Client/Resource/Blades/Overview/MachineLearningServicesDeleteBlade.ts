import * as TemplateBlade from "Fx/Composition/TemplateBlade";
import * as Button from "Fx/Controls/Button";
import * as Section from "Fx/Controls/Section";
import * as TextBox from "Fx/Controls/TextBox";
import * as InfoBox from "Fx/Controls/InfoBox";
import * as CheckBox from "Fx/Controls/CheckBox";
import * as Format from "../../../Shared/Controls/Format";
import { HtmlContent } from "Fx/Controls/ControlsBase";
import { DataContext } from "../../ResourceArea";
import * as ClientResources from "Resx/ClientResources";
import { WorkspaceMappedKind } from "Shared/Enums";
import { asMappedKind, isAIStudioKind } from "Shared/ViewAgnostic/Utilities";
export interface Parameters {
    id: string;
    isCMKWorkspace: boolean;
    workspaceName: string;
    workspaceKind?: string;
}

export interface ReturnData {
    confirmDelete: boolean;
    permanentlyDelete?: boolean;
}

@TemplateBlade.Decorator({
    htmlTemplate: `
    <div class='ext-associated-resource-settings msportalfx-padding'>
        <div data-bind='pcControl: messageSection' class='ext-associated-resource-settings-form msportalfx-form'></div>
        <div data-bind='pcControl: formSection' style='margin-bottom:2em'></div>
        <div data-bind='pcControl: buttonsSection' class='ext-associated-resource-settings-buttons'></div>
    </div>`,
    styleSheets: ["../../../Shared/Styles/CustomStyles.css"]
})
@TemplateBlade.ForContextPane.Decorator({
    width: TemplateBlade.ForContextPane.Width.Medium
})
@TemplateBlade.ReturnsData.Decorator()
@TemplateBlade.InjectableModel.Decorator(DataContext)
export class MachineLearningServicesDeleteBlade {
    public context: TemplateBlade.Context<Parameters, DataContext> & TemplateBlade.ReturnsData.Context<ReturnData>;
    public formSection: Section.Contract;
    public buttonsSection: Section.Contract;
    public messageSection: Section.Contract;
    public title: string;
    public subtitle: string;
    private _permanentlyDeleteEnabled: KnockoutObservable<boolean> = ko.observable(false);
    private _workspaceKind: WorkspaceMappedKind;

    public async onInitialize(): Promise<void> {
        const { container, parameters } = this.context;

        this._workspaceKind = asMappedKind(parameters.workspaceKind);

        this.title = ClientResources.SoftDeletedWorkspace.DeleteBlade.title;
        this.subtitle = parameters.workspaceName;

        const formattedMessage = Format.create(container, {
            format: ClientResources.SoftDeletedWorkspace.DeleteBlade.Message.text,
            children: [
                {
                    htmlTemplate: `<a href=${MsPortalFx.getEnvironmentValue("machineLearningServicesSoftDeleteDocUrl")}>${ClientResources.SoftDeletedWorkspace.DeleteBlade.Message.link}</a>`
                }
            ]
        });

        const supportsSoftDelete = !isAIStudioKind(this._workspaceKind);
        this._permanentlyDeleteEnabled(!supportsSoftDelete);

        // Message section
        const nonSoftDeleteMessage = InfoBox.create(container, {
            style: InfoBox.Style.Warning,
            text: ClientResources.SoftDeletedWorkspace.DeleteBlade.Message.AIStudio.text.format(parameters.workspaceName)
        });

        const nonSoftDeleteMessageSection = Section.create(container, {
            children: [nonSoftDeleteMessage]
        });

        const cmkMessage = Format.create(container, {
            format: ClientResources.SoftDeletedWorkspace.DeleteBlade.Message.cMKtext,
            children: [
                {
                    htmlTemplate: `<a href=${MsPortalFx.getEnvironmentValue("machineLearningServicesSoftDeleteDocUrl")}>${ClientResources.SoftDeletedWorkspace.DeleteBlade.Message.link}</a>`
                }
            ]
        });

        const message = InfoBox.create(container, {
            style: InfoBox.Style.Warning,
            text: {
                htmlTemplate: `<div data-bind="pcControl: formattedMessage"></div>`,
                viewModel: { formattedMessage }
            },
            visible: true
        });

        const cmkMessageWarning = InfoBox.create(container, {
            style: InfoBox.Style.Warning,
            text: {
                htmlTemplate: `<div data-bind="pcControl: cmkMessage"></div>`,
                viewModel: { cmkMessage }
            },
            visible: parameters.isCMKWorkspace
        });

        const softDeleteMessageSection = Section.create(container, {
            children: [message, cmkMessageWarning]
        });

        this.messageSection = !supportsSoftDelete ? nonSoftDeleteMessageSection : softDeleteMessageSection;

        // Form section
        const verticalSpace: HtmlContent = {
            htmlTemplate: `<div style='margin:1em 0'> </div>`
        };

        const permanentDeleteHeader: HtmlContent = {
            htmlTemplate: `<div data-bind='text: header' style='margin-bottom:4px'> </div>`,
            viewModel: { header:  ClientResources.SoftDeletedWorkspace.DeleteBlade.FormControls.PermanentDelete.header },
        };

        const permanentDeleteCheckbox = CheckBox.create(container, {
            label: ClientResources.SoftDeletedWorkspace.DeleteBlade.FormControls.PermanentDelete.checkbox,
            labelOnRight: true,
            value: this._permanentlyDeleteEnabled,
            visible: true
        });

        permanentDeleteCheckbox.value.subscribe(container, newValue => {
            this._permanentlyDeleteEnabled(newValue);
        });

        const permanentlyDeleteControls = Section.create(container, {
            children: [
                permanentDeleteHeader, permanentDeleteCheckbox, verticalSpace
            ],
            visible: supportsSoftDelete
        });

        const containerName = TextBox.create(container, {
            disabled: ko.computed(container, () => !this._permanentlyDeleteEnabled()),
            visible: ko.computed(container, () => this._permanentlyDeleteEnabled()),
            label: ClientResources.SoftDeletedWorkspace.DeleteBlade.FormControls.Confirm.label,
            placeHolderText: ClientResources.SoftDeletedWorkspace.DeleteBlade.FormControls.Confirm.placeholder,
            validations: ko.observableArray<MsPortalFx.ViewModels.FormValidation>([
                new MsPortalFx.ViewModels.RequiredValidation(),
                new MsPortalFx.ViewModels.CustomValidation("", crName => {
                    if (crName !== parameters.workspaceName) {
                        return Q({ valid: false, message: ClientResources.SoftDeletedWorkspace.DeleteBlade.FormControls.Confirm.errorMessage});
                    }

                    return Q({ valid: true });
                })
            ]),
            value: ko.observable("")
        });

        this.formSection = Section.create(container, {
            children: [permanentlyDeleteControls, containerName],
            leftLabelPosition: false
        });

        // Delete and cancel buttons
        const deleteButton = Button.create(container, {
            disabled: ko.computed(container, () => {
                if (this._permanentlyDeleteEnabled()) {
                    if (containerName.value() !== parameters.workspaceName) {
                        return true;
                    }
                    return !containerName.valid();
                } else {
                    return false;
                }
            }),
            style: ko.computed(container, () => {
                return this._permanentlyDeleteEnabled() ? Button.Style.Danger : Button.Style.Primary;
            }),
            text: ClientResources.SoftDeletedWorkspace.DeleteBlade.FormControls.Buttons.delete,
            onClick: async () => {
                const allValid = (await Q.allResolved([containerName.triggerValidation()])).every(promise => !!promise.valueOf());
                if (allValid && this._permanentlyDeleteEnabled()) {
                    container.closeCurrentBlade({ confirmDelete: true, permanentlyDelete: this._permanentlyDeleteEnabled() });
                } else if (!this._permanentlyDeleteEnabled()) {
                    container.closeCurrentBlade({ confirmDelete: true });
                }
            }
        });

        const cancelButton = Button.create(container, {
            style: Button.Style.Secondary,
            text: ClientResources.SoftDeletedWorkspace.DeleteBlade.FormControls.Buttons.cancel,
            onClick: () => {
                container.closeCurrentBlade();
            }
        });

        this.buttonsSection = Section.create(container, {
            children: [deleteButton, cancelButton]
        });
    }
}
