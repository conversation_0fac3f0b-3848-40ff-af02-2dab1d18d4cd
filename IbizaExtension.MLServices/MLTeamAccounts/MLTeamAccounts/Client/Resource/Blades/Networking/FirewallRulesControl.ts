/**
* Code Reference: https://msazure.visualstudio.com/One/_git/AzureUX-IaaSExp?path=/src/src/Ux/Extensions/Storage/Client/AccessControl/ViewModels/FirewallControl.ts
*/
import * as EditableGrid from "Fx/Controls/EditableGrid";
import * as Section from "Fx/Controls/Section";
import * as TextBox from "Fx/Controls/TextBox";
import * as CheckBox from "Fx/Controls/CheckBox";
import * as DataGrid from "Fx/Controls/DataGrid";
import { getClientIPv4 } from "Fx/GetClientIP";
import * as ClientResources from "Resx/ClientResources";
import { Control, HtmlContent } from "Fx/Controls/ControlsBase";
import { LifetimeManager } from "Fx/Lifetime";
import { Container } from "Fx/Composition/TemplateBlade";
import { createSectionHeader, createLink } from "../../Create/Utilities/NoPdlUiHelpers";
import { DocLinkIds, getDocumentUrl } from "../../../Shared/DocLinks";
import { CustomV } from "Fx/Controls/Validations";
import { IPv4Address, safeGetIpv4Address } from "../../Common/IPv4Address";
import { IPv4Subnet, safeGetIpv4Subnet } from "../../Common/IPv4Subnet";
import { ValidationResult } from "Fx/Controls/Validations";
import { ExternalIpRange, IpRuleModel } from "Resource/Common/Networking.types";

const NetworkingPublicAccessResources = ClientResources.Networking.PublicAccess;

export interface FirewallRulesControlOptions {
    ipRules: KnockoutObservableArray<IpRuleModel>;
    saving: KnockoutObservable<boolean>;
}

interface Context {
    lifetimeManager: LifetimeManager;
    container: Container;
}

interface CidrValidationOptions {
    maxCidr?: number;
    minCidr?: number;
    prefixRequired?: boolean;
    cidrBlockValidationRequired?: boolean;
    validateEmptyCidrString?: boolean;
}

const emptyIpAddress = "0.0.0.0";
const maxAddressRangeCount = 200;

export class FirewallRulesControl {
    public control: Section.Contract;
    public valid: KnockoutObservableBase<boolean>;
    private _controls: Array<Control | HtmlContent>;
    private _context: Context;
    private _ipRules: KnockoutObservableArray<IpRuleModel>;
    private _saving: KnockoutObservable<boolean>;
    private _addClientIpAddress: KnockoutObservable<boolean>;
    private _clientIpAddressLabel = ko.observable<string>();
    private _clientIpAddress = ko.observable<string>();
    private _firewallEditableGrid: EditableGrid.Contract<IpRuleModel>

    public constructor(context: Context, options: FirewallRulesControlOptions) {
        this._context = context;
        this._ipRules = options.ipRules;
        this._saving = options.saving;
        this._addClientIpAddress = ko.observable<boolean>(false);
        const clientIpCheckBoxDisabled = ko.pureComputed(() => {
            return this._saving() || this._ipRules().length >= maxAddressRangeCount;
        });
        const canEdit = ko.pureComputed(() => !this._saving());
        this._firewallEditableGrid = this._createFirewallEditableGrid(canEdit);

        this.control = Section.create(this._context.lifetimeManager, {
            children: [
                createSectionHeader(NetworkingPublicAccessResources.firewallHeader),
                this._createFirewallDescription(),
                this._createClientIpCheckBox(clientIpCheckBoxDisabled),
                this._firewallEditableGrid
            ]
        })

        this._addClientIpAddress.subscribe(this._context.lifetimeManager, () => {
            if (this._addClientIpAddress()) {
                const ipRules = this._ipRules();
                const clientIp = this._clientIpAddress();
                const index = MsPortalFx.findIndex(ipRules, (ipRule) => {
                    return ipRule.value && ipRule.value() === clientIp;
                });
                if (index < 0) {
                    this._ipRules.push({
                        value: ko.observable<string>(this._clientIpAddress())
                    });
                }
            } else {
                const ipRules = this._ipRules();
                const clientIp = this._clientIpAddress();
                const index = MsPortalFx.findIndex(ipRules, (ipRule) => {
                    return ipRule.value && ipRule.value() === clientIp;
                });
                if (index >= 0) {
                    this._ipRules.splice(index, 1);
                }
            }
        });

        this._ipRules.subscribeAndRun(this._context.lifetimeManager, () => {
            if (this._hasClientIpRule()) {
                this._addClientIpAddress(true);
            } else {
                this._addClientIpAddress(false);
            }
        });

        this.valid = ko.pureComputed(() => {
            return this._firewallEditableGrid.validation.valid();
        });
    }

    public async load(): Promise<void> {
        return this._loadClientIp();
    }

    public getControls(): Array<Control | HtmlContent> {
        return this._controls;
    }

    private _createFirewallDescription(): DataGrid.HtmlContent {
        return createLink({
            text: NetworkingPublicAccessResources.firewallDescription,
            linkText: NetworkingPublicAccessResources.firewallLearnMore,
            linkUri: getDocumentUrl(DocLinkIds.GrantIpBasedAccess)
        });
    }

    private _createClientIpCheckBox(addClientIpCheckBoxDisabled: KnockoutComputed<boolean>): CheckBox.Contract {
        const options: CheckBox.Options = {
            label: this._clientIpAddressLabel,
            labelOnRight: true,
            value: this._addClientIpAddress,
            disabled: addClientIpCheckBoxDisabled,
            infoBalloonContent: NetworkingPublicAccessResources.addClientIpAddressInfo,
            cssClass: "ext-firewall-client-ip-checkbox",
        };

        return CheckBox.create(this._context.container, options);
    }

    private _loadClientIp(): Promise<void> {
        return getClientIPv4()
            .then((ip) => {
                const clientAddress = safeGetIpv4Address(ip);
                if (clientAddress) {
                    const clientIpCIDRNotation = this._convertToCIDR(clientAddress.toString());
                    this._clientIpAddress(clientIpCIDRNotation.toString());
                    this._clientIpAddressLabel(
                        NetworkingPublicAccessResources.addClientIpAddressLabel.format(this._clientIpAddress())
                    );
                }
            })
            .catch(() => {
                // If we can't get the client IP, no-op.
            });
    }

    private _createFirewallEditableGrid(
        canEdit: KnockoutObservableBase<boolean>
    ): EditableGrid.Contract<IpRuleModel> {
        return EditableGrid.create<IpRuleModel>(
            this._context.container,
            {
                ariaLabel: "Firewall Rules",
                items: this._ipRules,
                addition: {
                    canAddRow: () => {
                        return canEdit() && this._ipRules().length < maxAddressRangeCount
                    },
                    createItem: () => {
                        return {
                            value: ko.observable<string>(),
                        };
                    },
                },
                contextMenu: {
                    canShowMenu: () => {
                        return false;
                    },
                    maxButtonCommands: 1,
                },
                focus: {
                    onExitRow: (_, grid) => {
                        grid.validation.triggerValidation();
                    },
                },
                editing: {
                    canEditRow: () => {
                        return canEdit();
                    },
                },
                deletion: {
                    canDeleteRows: () => {
                        return canEdit();
                    },
                },
                selection: {
                    selectionMode: EditableGrid.SelectionMode.Single,
                },
                columns: [
                    {
                        id: "FirewallRuleIpAddress",
                        header: NetworkingPublicAccessResources.addressRange,
                        defineCell: (_, item) => {
                            return item.value;
                        },
                        createCellEditor: (editorLifetime, item) => {
                            const textbox = TextBox.create(editorLifetime, {
                                value: item.value,
                                validations: ko.observableArray([
                                    new MsPortalFx.ViewModels.RequiredValidation(),
                                    this._getFirewallClientIpExceptionValidator(),
                                    this._getNoFirewallRuleDuplicatesValidation(
                                        this._ipRules
                                    ),
                                    this._getPublicIpRuleValidation()
                                ]),
                                placeHolderText: NetworkingPublicAccessResources.addressRangePlaceHolder,
                            });
                            return textbox;
                        },
                    },
                ],
                cssClass: "ext-firewall-rule-grid",
            }
        );
    }

    private _getFirewallClientIpExceptionValidator(): CustomV<string> {
        return new CustomV<string>("", (value) => {
            if (value) {
                const valueSplit = value.split('/');
                if (valueSplit.length === 1 && !this._getIPv4andIPv6AddressValidator(value, ko.observable(false), false)) {
                    // An IP address is expected
                    return Q({ valid: false, message: NetworkingPublicAccessResources.FirewallValidation.invalidIPAddress });
                } else if (valueSplit.length > 1 && valueSplit[1]?.length === 0) {
                    // An IP address with a terminating '/' is expected.
                    return Q({ valid: false, message: NetworkingPublicAccessResources.FirewallValidation.invalidCidr });
                } else if (valueSplit.length > 1) {
                    // A CIDR is expected
                    return Q(this._performCidrValidation({
                        prefixRequired: false,
                        cidrBlockValidationRequired: true,
                        minCidr: ExternalIpRange.minCidr,
                        maxCidr: ExternalIpRange.maxCidr
                    },
                        value));
                }
            }

            return Q({ valid: true, message: null });
        });

    }

    // This method validates the IP address is Ipv4 or Ipv6 without prefix value based on flag
    private _getIPv4andIPv6AddressValidator(value: string, isIPv6Enabled?: KnockoutObservableBase<boolean>, ignoreEmptyIPAddress?: boolean): boolean {
        const isIPv6Unwrapped = ko.unwrap(isIPv6Enabled);
        const isIPv6FlagEnabled = MsPortalFx.isNullOrUndefined(isIPv6Unwrapped) ? true : isIPv6Unwrapped;

        if (isIPv6FlagEnabled) {
            const range = this._getPrefixAndIPAddressFromRange(value);
            const isValidIpv4Value = !range.hasPrefix && this.isValidIPv4String(range.ipAddress);
            const isValidIpv6Value = !range.hasPrefix && this.isValidIPv6String(range.ipAddress);
            return isValidIpv4Value || isValidIpv6Value;
        } else if (!!value && (!ignoreEmptyIPAddress || !this._equals(emptyIpAddress, value))) {
            return this.validateIpAddress(value).valid;
        }
        return true;
    }

    private _getPrefixAndIPAddressFromRange(ipAddressRange: string) {
        const hasPrefix = !!ipAddressRange && ipAddressRange.split("/").length === 2;
        const cidrNotation = hasPrefix && ipAddressRange.split("/");

        return {
            hasPrefix: hasPrefix,
            prefix: hasPrefix ? cidrNotation[1] : null,
            ipAddress: hasPrefix ? cidrNotation[0] : ipAddressRange
        };
    }

    private isValidIPv4String(ipString: string) {
        const isValidIPv4Regex = /^(0?[0-9]?[0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])\.(0?[0-9]?[0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])\.(0?[0-9]?[0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])\.(0?[0-9]?[0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])$/;
        return isValidIPv4Regex.test(ipString);
    }

    private isValidIPv6String(ipString: string) {
        const isValidIPv6Regex = /^s*((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]d|1dd|[1-9]?d)(.(25[0-5]|2[0-4]d|1dd|[1-9]?d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]d|1dd|[1-9]?d)(.(25[0-5]|2[0-4]d|1dd|[1-9]?d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]d|1dd|[1-9]?d)(.(25[0-5]|2[0-4]d|1dd|[1-9]?d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]d|1dd|[1-9]?d)(.(25[0-5]|2[0-4]d|1dd|[1-9]?d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]d|1dd|[1-9]?d)(.(25[0-5]|2[0-4]d|1dd|[1-9]?d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]d|1dd|[1-9]?d)(.(25[0-5]|2[0-4]d|1dd|[1-9]?d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]d|1dd|[1-9]?d)(.(25[0-5]|2[0-4]d|1dd|[1-9]?d)){3}))|:)))(%.+)?s*$/;
        return isValidIPv6Regex.test(ipString);
    }

    private validateIpAddress(value: string): MsPortalFx.ViewModels.ValidationResult {
        const result: MsPortalFx.ViewModels.ValidationResult = {
            valid: true,
            message: undefined
        };

        try {
            // ReSharper disable once WrongExpressionStatement
            // Unfortunately this uses exceptions for flow control.
            new IPv4Address(value);

            (value || "").split(".").forEach((octet: string) => {
                if (octet.length > 1 && this._equals(octet[0], "0")) {
                    result.valid = false;
                    result.message = NetworkingPublicAccessResources.FirewallValidation.leadingZerosIpAddress.format(octet, value);
                }
            });
        } catch (error) {
            result.valid = false;
            result.message = NetworkingPublicAccessResources.FirewallValidation.invalidIPAddress;
        }

        return result;
    }

    private _equals(str1: string, str2: string): boolean {
        if (typeof str1 !== "string" || typeof str2 !== "string") {
            return false;
        }

        str1 = str1.toUpperCase();
        str2 = str2.toUpperCase();

        return str1 === str2;
    }

    // This method will validate CIDR notation as well if the entered value is a full valid CIDR block
    private _performCidrValidation(options: CidrValidationOptions, value: string): ValidationResult {
        const result: ValidationResult = {
            valid: true,
            message: ""
        };
        let subnet: IPv4Subnet;
        const hasExplicitPrefix = !!value && value.split("/").length === 2;
        if (options.validateEmptyCidrString) {
            try {
                // Ensure proper ip address formatting
                subnet = new IPv4Subnet(value, options.minCidr);
            } catch (error) {
                result.valid = false;
                result.message = error.message;
                return result;
            }
        }
        if (!!value) {
            try {
                // Ensure proper ip address formatting
                subnet = new IPv4Subnet(value, options.minCidr);
            } catch (error) {
                result.valid = false;
                result.message = error.message && error.message != "" ? error.message : NetworkingPublicAccessResources.FirewallValidation.invalidIPAddress;
                return result;
            }

            // Validate CIDR block
            if (options.cidrBlockValidationRequired) {
                const validIpForCidrPrefixInBytes = subnet.subnetMaskBytes;
                const validIpForCidrPrefix = IPv4Address.bytesToAddressString(validIpForCidrPrefixInBytes);
                const isValidCidrBlock = this._equals(validIpForCidrPrefix, subnet.getIpAddress());

                if (!isValidCidrBlock) {
                    result.valid = false;
                    result.message = NetworkingPublicAccessResources.FirewallValidation.invalidCIDRBlockWithSuggestion.format(value, validIpForCidrPrefix, subnet.prefix);
                    return result;
                }
            }

            // Validate prefix if it has to be equal to a particular value
            if (hasExplicitPrefix) {
                if (options.maxCidr && options.minCidr && options.maxCidr === options.minCidr) {
                    if (subnet.prefix !== options.maxCidr) {
                        result.valid = false;
                        result.message = NetworkingPublicAccessResources.FirewallValidation.exactPrefix.format(options.maxCidr);
                    }
                } else {
                    // Else check min and max prefix conditions individually
                    if (options.maxCidr && subnet.prefix > options.maxCidr) {
                        result.valid = false;
                        result.message = NetworkingPublicAccessResources.FirewallValidation.maxPrefix.format(options.maxCidr);
                    }

                    if (options.minCidr && subnet.prefix < options.minCidr) {
                        result.valid = false;
                        result.message = NetworkingPublicAccessResources.FirewallValidation.minPrefix.format(options.minCidr);
                    }
                }
            }
        }

        return result;
    }

    private _getPublicIpRuleValidation(): CustomV<string> {
        return new CustomV<string>("", (value) => {
            const result: ValidationResult = {
                valid: true,
                message: null,
            };

            // ACLs IP rules must be public IPs. See https://www.iana.org/assignments/ipv4-address-space/ipv4-address-space.xhtml
            // Verified that the following ranges are also blocked by the server.
            const blockedSubnets = [
                new IPv4Subnet("0.0.0.0/8"), // Local identification
                new IPv4Subnet("10.0.0.0/8"), // Private IPs
                new IPv4Subnet("**********/10"), // Shared address space
                new IPv4Subnet("*********/8"), // Loopback
                new IPv4Subnet("***********/16"), // Link local
                new IPv4Subnet("**********/12"), // Private IPs
                new IPv4Subnet("*********/24"), // IETF Protocol Assignments
                new IPv4Subnet("*********/24"), // TEST-NET-1
                new IPv4Subnet("***********/24"), // 6to4 Relay Anycast
                new IPv4Subnet("***********/16"), // Private IPs
                new IPv4Subnet("**********/15"), // Network Interconnect Device Benchmark Testing
                new IPv4Subnet("************/24"), // TEST-NET-2
                new IPv4Subnet("***********/24"), // TEST-NET-3
                new IPv4Subnet("*********/4"), // Multicast
                new IPv4Subnet("***********/24"), // MCAST-TEST-NET
                new IPv4Subnet("*********/3"), // ********* - *************** (Multicast & "Future use")
                new IPv4Subnet("240.0.0.0/4"), // Reserved for future use
                new IPv4Subnet("***************/32"), // Limited Broadcast
            ];

            const address = safeGetIpv4Subnet(value);
            if (address) {
                blockedSubnets.forEach((blockedSubnet) => {
                    if (blockedSubnet.doesSubnetOverlap(address)) {
                        result.valid = false;
                        result.message = NetworkingPublicAccessResources.FirewallValidation.publicIpRuleValidation;
                    }
                });
            }

            return Q(result);
        });
    }

    private _getNoFirewallRuleDuplicatesValidation(
        currentRules: KnockoutObservableArray<IpRuleModel>
    ): CustomV<string> {
        return new CustomV<string>("", (value) => {
            const result: ValidationResult = {
                valid: true,
                message: null,
            };

            let match = false;
            const rules = currentRules();
            rules.forEach((rule) => {
                if (
                    this._equals(
                        rule.value(),
                        value
                    )
                ) {
                    if (match) {
                        result.valid = false;
                        result.message = NetworkingPublicAccessResources.FirewallValidation.duplicateIpAddresses;
                        return;
                    }

                    match = true;
                }
            });

            return Q(result);
        });
    }

    private _hasClientIpRule(): boolean {
        const ipRules = this._ipRules();
        const clientIp = this._clientIpAddress();
        return clientIp && ipRules && MsPortalFx.findIndex(ipRules, (ipRule) => {
            return ipRule.value && ipRule.value() === clientIp;
        }) >= 0;
    }

    private _convertToCIDR(ipAddress: string): string {
        const ipRegex = /^\d+\.\d+\.\d+\.\d+$/;
        if (ipRegex.test(ipAddress)) {
            // Append /32 to the IPv4 address to convert it to CIDR notation
            // Need to chagne this to /128 for IPv6 support
            return ipAddress + '/32';
        }
        return ipAddress;
    }
}
