import { DataContext } from "Resource/ResourceArea";
import * as RadioButton from "Fx/Controls/RadioButtons";
import * as Section from "Fx/Controls/Section";
import * as Toolbar from "Fx/Controls/Toolbar";
import * as InfoBox from "Fx/Controls/InfoBox";
import { Container } from "Fx/Composition/TemplateBlade";
import Resources = require("Resx/ClientResources");
import * as ExtensionDefinition from "../../../_generated/ExtensionDefinition";
import * as NetworkingSectionToolbar from "Resource/Common/NetworkingSectionToolbar";

import FxViewModels = MsPortalFx.ViewModels;
import { createSelectableCardItemText } from "Shared/SelectionCards";
import Utilities from "Shared/Utilities";
import { ConnectivityMethod, InitialNetworkSettings, ManagedNetworkIsolationMode, NetworkingDataModel } from "../../Common/NetworkingDataModel";
import { WorkspaceOutboundAccessControl } from "../../Common/WorkspaceOutboundAccessControl";
import { createNetworkingSection } from "../../Common/NetworkingUtilities";
import { ArmId } from "Fx/ResourceManagement";
import { INetworkingControl } from "Resource/Common/Networking.types";
import { LifetimeManager } from "Fx/Lifetime";
import { createLink } from "Resource/Create/Utilities/NoPdlUiHelpers";
import { AkaKey, getAkaUrl } from "Shared/AkaKey";
import { WorkspaceMappedKind } from "Shared/Enums";

const NetworkingResources = Resources.CreateBlade.Network;

interface Context {
    lifetimeManager: LifetimeManager;
    container: Container;
}

export interface Options {
    form: MsPortalFx.ViewModels.FormProperties;
    model: DataContext;
    resourceId: string;
    kind: WorkspaceMappedKind;
}

export class ManagedWorkspaceOutboundControl implements INetworkingControl {
    public control: Section.Contract;
    public toolbar: Toolbar.Contract;
    private _resourceId: string;
    private _networkingDataModel: NetworkingDataModel;
    private _context: Context;
    private _model: DataContext;
    private _workspaceOutboundAccessControl: WorkspaceOutboundAccessControl;
    private _networkIsolationRadioButton: RadioButton.Contract<ManagedNetworkIsolationMode>;
    private _managedNetworkIsolationMode = ko.observable<ManagedNetworkIsolationMode>(ManagedNetworkIsolationMode.AllowInternetOutbound);
    private _alertConfiguration: KnockoutObservableBase<MsPortalFx.ViewModels.AlertConfiguration>;

    private _errorMessage: InfoBox.Contract;
    private _machineLearningWorkspaceName: KnockoutObservable<string> = ko.observable<string>("");
    private _machineLearningStorageAccountId: KnockoutObservable<string> = ko.observable<string>("");
    private _machineLearningAppInsightsId: KnockoutObservable<string> = ko.observable<string>("");
    private _machineLearningKeyVaultId: KnockoutObservable<string> = ko.observable<string>("");
    private _machineLearningContainerRegistryId: KnockoutObservable<string> = ko.observable<string>("");
    private _machineLearningContainerRegistrySku: KnockoutObservable<string> = ko.observable<string>("");
    private _machineLearningWorkspaceId: KnockoutObservable<string> = ko.observable<string>("");
    private _machineLearningLocation: KnockoutObservable<string> = ko.observable<string>("");
    private _machineLearningSubscriptionId: KnockoutObservable<string> = ko.observable<string>("");
    private _machineLearningResourceGroupName: KnockoutObservable<string> = ko.observable<string>("");
    private _isLoading = ko.observable<boolean>(true);
    private _saving = ko.observable<boolean>(false);
    private _initialNetworkSettings = ko.observable<InitialNetworkSettings>(undefined);

    constructor(context: Context, options: Options) {
        const { lifetimeManager } = context;
        const { form, model, resourceId } = options;
        this._context = context;
        this._model = model;
        this._resourceId = resourceId;

        const resource = ArmId.parse(resourceId);
        this._machineLearningSubscriptionId(resource.subscription);
        this._machineLearningResourceGroupName(resource.resourceGroup);

        const errorMessage = InfoBox.create(context.lifetimeManager, {
            style: InfoBox.Style.Error,
            text: ko.observable(""),
            visible: ko.observable(false)
        });
        this._errorMessage = errorMessage;

        this._networkingDataModel = new NetworkingDataModel({
            initialSettings: this._initialNetworkSettings
        });
        this._alertConfiguration = ko.observable<MsPortalFx.ViewModels.AlertConfiguration>({
            showAlert: this._networkingDataModel.isDirty()
        });
        this._networkingDataModel.isDirty.subscribe(lifetimeManager, (isDirty: boolean) => {
            this._alertConfiguration({
                showAlert: isDirty
            });
        });
        form.configureAlertOnClose(this._alertConfiguration);

        const children: any[] = [errorMessage];


        children.push(InfoBox.createInline(this._context.lifetimeManager, {
            text: createLink({
                text: NetworkingResources.WorkspaceOutboundAccess.disabledManagedVnetMessage,
                linkText: NetworkingResources.WorkspaceOutboundAccess.disabledManagedVnetMessageLearnMore,
                linkUri: getAkaUrl(AkaKey.AiHubManagedVnetLearnMore)
            }),
            style: InfoBox.Style.Info,
            visible: ko.pureComputed(() => this._managedNetworkIsolationMode() === ManagedNetworkIsolationMode.Disabled)
        }));

        this._createNetworkSelectionRadioButton();
        children.push(createNetworkingSection(lifetimeManager, [this._networkIsolationRadioButton], null, "ext-isolation-mode-section"));

        this._workspaceOutboundAccessControl = new WorkspaceOutboundAccessControl(context, {
            networkingDataModel: this._networkingDataModel,
            workspaceKind: options.kind,
            resourceDetails: {
                workspaceName: this._machineLearningWorkspaceName,
                subscriptionId: this._machineLearningSubscriptionId,
                appInsightsId: this._machineLearningAppInsightsId,
                containerRegistryId: this._machineLearningContainerRegistryId,
                keyVaultId: this._machineLearningKeyVaultId,
                locationName: this._machineLearningLocation,
                resourceGroupName: this._machineLearningResourceGroupName,
                storageAccountId: this._machineLearningStorageAccountId
            },
            dataContext: this._model,
            managedNetworkIsolationMode: this._managedNetworkIsolationMode,
            loadApiRules: false,
            isLoading: this._isLoading,
            setErrorMessage: (message: string) => {
                this._errorMessage.text(message);
                this._errorMessage.visible(true);
            }
        });

        children.push(
            createNetworkingSection(
                lifetimeManager,
                this._workspaceOutboundAccessControl.getControls(),
                ko.pureComputed(() => {
                    return (
                        this._managedNetworkIsolationMode() === ManagedNetworkIsolationMode.AllowInternetOutbound ||
                        this._managedNetworkIsolationMode() === ManagedNetworkIsolationMode.AllowOnlyApprovedOutbound
                    );
                })
            )
        );

        this.control = Section.create(lifetimeManager, {
            name: undefined,
            children: children
        });

        const isValidSave = ko.observable(true);
        this.toolbar = NetworkingSectionToolbar.create(lifetimeManager, {
            isLoading: this._isLoading,
            isDirty: this._networkingDataModel.isDirty,
            isValidSave,
            saving: this._saving,
            discard: () => this._discard(),
            refresh: () => this._refresh(),
            save: () => this._save(resourceId),
            kind: options.kind,
        }, context.container);
        this._networkIsolationRadioButton.valid.subscribeAndRun(this._context.lifetimeManager, isValidSave);
    }

    public async load(): Promise<any> {
        await this._loadMachineLearningServices();
        await this._loadContainerRegistry();
        this._resetToInitialSettings();
        this._isLoading(false);
    }

    private _setMachineLearningResourcesInitialState(): void {
        this._machineLearningWorkspaceName("");
        this._machineLearningStorageAccountId("");
        this._machineLearningAppInsightsId("");
        this._machineLearningKeyVaultId("");
        this._machineLearningContainerRegistryId("");
        this._machineLearningWorkspaceId("");
        this._machineLearningLocation("");
        this._machineLearningContainerRegistrySku("");
    }

    private async _refresh(): Promise<any> {
        this._errorMessage.visible(false);
        this._isLoading(true);
        this._setMachineLearningResourcesInitialState();
        return this.load();
    }

    private _discard(): void {
        this._errorMessage.visible(false);
        this._isLoading(true);
        this._resetToInitialSettings();
        this._isLoading(false);
    }

    private async _save(resourceId: string): Promise<any> {
        this._saving(true);
        this._isLoading(true);
        const managedNetwork = this._networkingDataModel.getManagedNetworkUpdatePayload();
        const notification = MsPortalFx.Hubs.Notifications.ClientNotification.publish({
            title: Resources.Network.NetworkIsolation.inProgressWorkspaceManagedOutboundAccessNotificationTitle,
            description: Resources.Network.NetworkIsolation.inProgressWorkspaceManagedOutboundAccessNotificationDescription,
            status: MsPortalFx.Hubs.Notifications.NotificationStatus.InProgress
        });
        this._errorMessage.visible(false);
        await this._model.machineLearningServicesData
            .updateWorkspace(resourceId, {
                managedNetwork: managedNetwork
            })
            .then(
                _values => {
                    this._saving(false);
                    notification.title = Resources.Network.NetworkIsolation.successWorkspaceManagedOutboundAccessNotificationTitle,
                    notification.description = Resources.Network.NetworkIsolation.successWorkspaceManagedOutboundAccessNotificationDescription,
                    notification.status = MsPortalFx.Hubs.Notifications.NotificationStatus.Success;
                    notification.publish();
                },
                reason => {
                    this._errorMessage.text(Utilities.extractErrorMessage(reason.jqXHR));
                    this._errorMessage.visible(true);
                    this._saving(false);
                    this._isLoading(false);
                    notification.title = Resources.Network.NetworkIsolation.errorWorkspaceManagedOutboundAccessNotificationTitle,
                    notification.description = Resources.Network.NetworkIsolation.errorWorkspaceManagedOutboundAccessNotificationTitle,
                    notification.status = MsPortalFx.Hubs.Notifications.NotificationStatus.Error;
                    notification.publish();
                }
            );

        if (this._errorMessage.visible()) {
            return Promise.reject();
        } else {
            return this._refresh();
        }
    }

    private _loadMachineLearningServices(): Q.Promise<any> {
        const deferred = Q.defer();

        const loadWorkspacePromise = this._model.machineLearningServicesData.getWorkspace(this._resourceId, true);
        const permissionsPromise = MsPortalFx.Base.Security.hasPermission(this._resourceId, [
            ExtensionDefinition.AssetTypes.MachineLearningServices.Permissions.readServices
        ]);

        Q.all([permissionsPromise, loadWorkspacePromise]).then(
            values => {
                const hasAccess = values[0];
                const workspace = values[1];

                this._machineLearningStorageAccountId(workspace.properties.storageAccount);
                this._machineLearningContainerRegistryId(workspace.properties.containerRegistry);
                this._machineLearningKeyVaultId(workspace.properties.keyVault);
                this._machineLearningAppInsightsId(workspace.properties.applicationInsights);
                this._machineLearningWorkspaceId(workspace.properties.workspaceId);
                this._machineLearningLocation(workspace.location);
                this._machineLearningWorkspaceName(workspace.name);

                this._initialNetworkSettings({
                    managedNetwork: workspace.properties?.managedNetwork ?? { isolationMode: "Disabled" },
                    connectivityMethod:
                        workspace.properties.publicNetworkAccess === "Enabled"
                            ? ConnectivityMethod.PublicEndpointAllNetworks
                            : ConnectivityMethod.PrivateEndpoint
                });

                if (!hasAccess) {
                    this._context.container.unauthorized();
                } else {
                    deferred.resolve();
                }
            },
            reason => {
                deferred.reject(reason);
            }
        );
        return deferred.promise;
    }

    private _loadContainerRegistry(): Q.Promise<any> {
        const deferred = Q.defer();
        const containerRegistryId = this._machineLearningContainerRegistryId();
        if (containerRegistryId) {
            this._model.containerRegistryData.getContainerRegistry(containerRegistryId).then(
                containerRegistry => {
                    this._machineLearningContainerRegistrySku(containerRegistry.sku.tier);
                    deferred.resolve();
                },
                reason => {
                    deferred.reject(reason);
                }
            );
        } else {
            deferred.resolve();
        }
        return deferred.promise;
    }

    public _resetToInitialSettings(): void {
        this._networkingDataModel.reset();
        this._managedNetworkIsolationMode(this._networkingDataModel.initialData().managedNetworkIsolationMode);
    }

    private _createNetworkSelectionRadioButton(): void {
        const managedVNetItems: Array<RadioButton.Item<ManagedNetworkIsolationMode>> = [
            {
                text: createSelectableCardItemText({
                    header: NetworkingResources.NetworkIsolation.Disabled.title,
                    listItems: [
                        NetworkingResources.NetworkIsolation.Public.descriptionItemTwo,
                        NetworkingResources.NetworkIsolation.Public.descriptionItemThree
                    ]
                }),
                value: ManagedNetworkIsolationMode.Disabled,
                disabled: ko.pureComputed(() => {
                    return !this._networkingDataModel.initialData().allowedNetworkSelectionOptions.includes(ManagedNetworkIsolationMode.Disabled);
                })
            },
            {
                text: createSelectableCardItemText({
                    header: NetworkingResources.NetworkIsolation.AllowInternetOutbound.title,
                    listItems: [
                        NetworkingResources.NetworkIsolation.PrivateInternetOutbound.descriptionItemTwo,
                        NetworkingResources.NetworkIsolation.PrivateInternetOutbound.descriptionItemThree
                    ]
                }),
                value: ManagedNetworkIsolationMode.AllowInternetOutbound,
                disabled: ko.pureComputed(() => {
                    return !this._networkingDataModel
                        .initialData()
                        .allowedNetworkSelectionOptions.includes(ManagedNetworkIsolationMode.AllowInternetOutbound);
                })
            },
            {
                text: createSelectableCardItemText({
                    header: NetworkingResources.NetworkIsolation.AllowOnlyApprovedOutbound.title,
                    listItems: [
                        NetworkingResources.NetworkIsolation.PrivateApprovedOutbound.descriptionItemTwo,
                        NetworkingResources.NetworkIsolation.PrivateApprovedOutbound.descriptionItemThree
                    ]
                }),
                value: ManagedNetworkIsolationMode.AllowOnlyApprovedOutbound,
                disabled: ko.pureComputed(() => {
                    return !this._networkingDataModel
                        .initialData()
                        .allowedNetworkSelectionOptions.includes(ManagedNetworkIsolationMode.AllowOnlyApprovedOutbound);
                })
            }
        ];

        this._networkIsolationRadioButton = RadioButton.create<ManagedNetworkIsolationMode>(this._context.lifetimeManager, {
            label: undefined,
            items: managedVNetItems,
            value: this._managedNetworkIsolationMode,
            singleItemPerLine: false,
            cssClass: "ext-ml-selection-cards",
            disabled: ko.pureComputed(() => {
                return this._isLoading();
            }),
            validations: [
                new FxViewModels.RequiredValidation(),
                new MsPortalFx.ViewModels.CustomValidation(NetworkingResources.NetworkIsolation.skuErrorMessage, () => {
                    if (
                        (this._managedNetworkIsolationMode() === ManagedNetworkIsolationMode.AllowInternetOutbound ||
                            this._managedNetworkIsolationMode() === ManagedNetworkIsolationMode.AllowOnlyApprovedOutbound) &&
                        this._machineLearningContainerRegistrySku() &&
                        this._machineLearningContainerRegistrySku() !== "Premium"
                    ) {
                        return Q({ valid: false, message: NetworkingResources.NetworkIsolation.skuErrorMessage });
                    } else {
                        return Q({ valid: true, message: "" });
                    }
                })
            ]
        });
    }
}
