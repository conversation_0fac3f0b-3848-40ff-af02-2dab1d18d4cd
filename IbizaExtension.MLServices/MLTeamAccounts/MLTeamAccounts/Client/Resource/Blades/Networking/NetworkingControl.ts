import { Container } from "Fx/Composition/TemplateBlade";
import * as Section from "Fx/Controls/Section";
import * as TabControl from "Fx/Controls/TabControl";
import { LifetimeManager } from "Fx/Lifetime";
import { ArmId } from "Fx/ResourceManagement";
import * as ClientResources from "Resx/ClientResources";
import { WorkspaceMappedKind } from "../../../Shared/Enums";
import { DataContext } from "../../ResourceArea";
import { machineLearningWorkspaces } from "../../../Shared/Constants"
import { INetworkingControl } from "../../Common/Networking.types";
import * as PrivateEndpointsControl from "../../Common/PrivateEndpointConnectionsControl";
import * as PublicAccessControl from "./PublicAccessControl";
import { ManagedWorkspaceOutboundControl } from "./ManagedWorkspaceOutboundControl";

interface Context {
    lifetimeManager: LifetimeManager;
    container: Container;
}

interface Options {
    id: string;
    ipBasedAccessEnabled: boolean;
    kind: WorkspaceMappedKind;
}

export function create(context: Context, form: MsPortalFx.ViewModels.FormProperties, model: DataContext, options: Options): INetworkingControl {
    const { id, kind, ipBasedAccessEnabled } = options;
    const publicAccessControl = PublicAccessControl.create(context, model, { id, ipBasedAccessEnabled, kind });
    const privateEndpointsControl = PrivateEndpointsControl.create(context, model, options);
    const resourceType = ArmId.getResourceTypes(ArmId.parse(id))[0];
    const managedOutboundAccessControl = new ManagedWorkspaceOutboundControl(context, {
        form,
        model,
        resourceId: id,
        kind,
    });

    const tabs = [];
    // Public network access tab
    if (kind !== WorkspaceMappedKind.Project) {
        tabs.push(
            Section.create(context.lifetimeManager, {
                name: ClientResources.Networking.PublicAccess.tabText,
                leftLabelPosition: true,
                children: [publicAccessControl.toolbar, publicAccessControl.control]
            })
        );
    }
    // Private endpoints tab
    tabs.push(
        Section.create(context.lifetimeManager, {
            name: ClientResources.Networking.PrivateEndpointConnections.tabText,
            leftLabelPosition: true,
            children: [privateEndpointsControl.toolbar, privateEndpointsControl.control]
        })
    );
    // Managed network tab
    if (kind !== WorkspaceMappedKind.Project && resourceType === machineLearningWorkspaces) {
        tabs.push(
            Section.create(context.lifetimeManager, {
                name: ClientResources.Networking.WorkspaceManagedOutboundAccess.tabText,
                leftLabelPosition: true,
                children: [managedOutboundAccessControl.toolbar, managedOutboundAccessControl.control]
            })
        );
    }

    const control = TabControl.create(context.lifetimeManager, {
        tabs: tabs
    });

    return {
        control,
        load: model => {
            const loadPromises = [publicAccessControl.load(model), privateEndpointsControl.load(model), managedOutboundAccessControl.load()];
            return Promise.all(loadPromises);
        }
    };
}
