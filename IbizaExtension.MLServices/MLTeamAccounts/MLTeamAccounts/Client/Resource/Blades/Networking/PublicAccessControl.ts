import * as RadioButtons from "Fx/Controls/RadioButtons";
import * as Section from "Fx/Controls/Section";
import * as InfoBox from "Fx/Controls/InfoBox";
import { LifetimeManager } from "Fx/Lifetime";
import * as ClientResources from "Resx/ClientResources";
import * as Format from "../../../Shared/Controls/Format";
import { DocLinkIds, getDocumentUrl } from "../../../Shared/DocLinks";
import { Icons } from "../../../Shared/Icons";
import Utilities from "../../../Shared/Utilities";
import { DataContext } from "../../ResourceArea";
import { INetworkingControl, IpRuleModel, NetworkAccessOption } from "../../Common/Networking.types";
import * as PublicAccessControlToolbar from "./PublicAccessControlToolbar";
import { FirewallRulesControl } from "./FirewallRulesControl";
import { Container } from "Fx/Composition/TemplateBlade";
import { WorkspaceMappedKind } from "Shared/Enums";

interface IWorkspace {
    properties: {
        publicNetworkAccess: string;
    }
}

interface Context {
    lifetimeManager: LifetimeManager,
    container: Container;
}

interface Options {
    id: string;
    ipBasedAccessEnabled: boolean;
    kind: WorkspaceMappedKind;
}

export function create(context: Context, model: DataContext, options: Options): INetworkingControl {
    const { id, ipBasedAccessEnabled } = options;

    const saving = ko.observable(false);
    const initialNetworkAccessValue = ko.observable<NetworkAccessOption>(NetworkAccessOption.AllNetworks);
    const networkAccess = ko.observable<NetworkAccessOption>(initialNetworkAccessValue());
    const initialIpAllowList = ko.observableArray<string>();
    const ipRules = ko.observableArray<IpRuleModel>();
    const publicNetworkAccess = ko.computed(context.lifetimeManager, () => {
        if (networkAccess() === NetworkAccessOption.AllNetworks ||
            networkAccess() === NetworkAccessOption.IPBasedAccess) {
            return "Enabled";
        } else {
            return "Disabled";
        }
    });

    const errorMessage = InfoBox.create(context.lifetimeManager, {
        style: InfoBox.Style.Error,
        text: ko.observable(""),
        visible: ko.observable(false)
    });

    const infoMessage = InfoBox.createInline(context.lifetimeManager, {
        style: InfoBox.Style.Info,
        text: ko.computed(context.lifetimeManager, () =>
            networkAccess() === NetworkAccessOption.AllNetworks
                ? ClientResources.Networking.PublicAccess.allNetworksInfoText
                : networkAccess() === NetworkAccessOption.IPBasedAccess
                    ? ClientResources.Networking.PublicAccess.enabledFromSelectedIpInfoText
                    : ClientResources.Networking.PublicAccess.disabledInfoText)
    });

    const description = Format.create(context.lifetimeManager, {
        format: `<div style="padding: 0; margin-top: 10px">${ClientResources.Networking.PublicAccess.description}</div>`, children: [
            {
                htmlTemplate: `<a href="${getDocumentUrl(DocLinkIds.PublicAccess)}">${ClientResources.Networking.PublicAccess.learnMoreText}<div class='ext-ml-ws2-new-banner-link-icon' data-bind='image: linkIcon'></div></a>`,
                innerViewModel: {
                    linkIcon: Icons.hyperlinkIcon
                }
            }
        ]
    });

    const connectivityItems: RadioButtons.Item<NetworkAccessOption>[] = [];
    if (ipBasedAccessEnabled) {
        connectivityItems.push({
            text: ClientResources.Networking.PublicAccess.radioEnabledFromAllNetworks,
            value: NetworkAccessOption.AllNetworks
        });
        connectivityItems.push({
            text: ClientResources.Networking.PublicAccess.radioEnabledFromSelectedIp,
            value: NetworkAccessOption.IPBasedAccess
        });
        connectivityItems.push({
            text: ClientResources.Networking.PublicAccess.radioDisabledText,
            value: NetworkAccessOption.Disabled
        });
    } else {
        connectivityItems.push({
            text: ClientResources.Networking.PublicAccess.radioDisabledText,
            value: NetworkAccessOption.Disabled
        });
        connectivityItems.push({
            text: ClientResources.Networking.PublicAccess.radioAllNetworksText,
            value: NetworkAccessOption.AllNetworks
        });
    }

    const networkAccessRadioButtons = RadioButtons.create<NetworkAccessOption>(context.lifetimeManager, {
        label: ClientResources.Networking.PublicAccess.radioLabel,
        items: ko.observableArray(connectivityItems),
        value: networkAccess,
        singleItemPerLine: true,
        disabled: ko.computed(context.lifetimeManager, () => saving()),
        suppressDirtyBehavior: true
    });

    const firewallRulesControl = new FirewallRulesControl(context, {
        ipRules,
        saving
    });

    const children: any[] = [errorMessage, description, networkAccessRadioButtons, infoMessage];

    if (ipBasedAccessEnabled) {
        const firewallRulesSectionControl = Section.create(context.lifetimeManager, {
            children: [firewallRulesControl.control],
            visible: ko.computed(context.lifetimeManager, () => networkAccess() === NetworkAccessOption.IPBasedAccess)
        });
        children.push(firewallRulesSectionControl);
    }

    const control = Section.create(context.lifetimeManager, {
        children: children,
        leftLabelPosition: true,
        smartAlignLabel: true
    });

    const dirty = ko.computed(context.lifetimeManager,
        () => networkAccess() !== initialNetworkAccessValue() ||
            initialIpAllowList().length !== ipRules().length ||
            initialIpAllowList().some((ipRule, index) => ipRule !== ipRules()[index].value()
            ));

    const valid = ko.computed(context.lifetimeManager,
        () => networkAccess() === NetworkAccessOption.IPBasedAccess
            ? ipRules().length > 0 && firewallRulesControl.valid()
            : true
    );

    const toolbar = PublicAccessControlToolbar.create(context, {
        valid,
        dirty,
        saving,
        refresh: async () => {
            await load(model);
        },
        save: async () => {
            if (Utilities.isRegistry(id)) {
                await updateRegistry(model);
            } else {
                await update(model);
            }
            if (!errorMessage.visible()) {
                await load(model);
            }
        },
        discard,
        kind: options.kind,
    });

    function load(model: DataContext): Promise<any> {
        errorMessage.visible(false);
        initialIpAllowList([]);
        const machineLearningServicesData = model.machineLearningServicesData.getWorkspace(id, false);
        const firewallRulesPromise = ipBasedAccessEnabled ? firewallRulesControl.load() : Promise.resolve();
        return Q.all([machineLearningServicesData, firewallRulesPromise]).then(
            (values) => {
                const workspace = values[0];
                const disabled = MsPortalFx.localeCompareIgnoreCase(workspace.properties.publicNetworkAccess, "Disabled") === 0;
                if (disabled) {
                    initialNetworkAccessValue(NetworkAccessOption.Disabled);
                } else {
                    if (ipBasedAccessEnabled && workspace.properties?.ipAllowlist && workspace.properties.ipAllowlist.length > 0) {
                        initialNetworkAccessValue(NetworkAccessOption.IPBasedAccess);
                        initialIpAllowList(workspace.properties.ipAllowlist);
                    } else {
                        initialNetworkAccessValue(NetworkAccessOption.AllNetworks);
                    }
                }
                networkAccess(initialNetworkAccessValue());
                ipRules(initialIpAllowList().map((ip: string) => {
                    return {
                        value: ko.observable(ip)
                    };
                }));
                errorMessage.visible(false);
            }, error => {
                errorMessage.text(Utilities.extractErrorMessage(error.jqXHR));
                errorMessage.visible(true);
            });
    }

    function update(model: DataContext): Promise<any> {
        saving(true);
        const notification = MsPortalFx.Hubs.Notifications.ClientNotification.publish({
            title: ClientResources.Networking.PublicAccess.inprogressPublicAccesNetworkNotificationTitle,
            description: ClientResources.Networking.PublicAccess.inprogressPublicAccesNetworkNotificationDescription,
            status: MsPortalFx.Hubs.Notifications.NotificationStatus.InProgress
        });
        errorMessage.visible(false);
        const ipList = networkAccess() === NetworkAccessOption.IPBasedAccess ? ipRules().map(ipRule => ipRule.value()) : [];
        return model.machineLearningServicesData
            .updateWorkspace(id, { publicNetworkAccess: publicNetworkAccess(), ipAllowlist: ipList })
            .then(
                () => {
                    initialNetworkAccessValue(networkAccess());
                    errorMessage.visible(false);
                    saving(false);
                    notification.title = ClientResources.Networking.PublicAccess.successPublicAccesNetworkNotificationTitle,
                    notification.description = ClientResources.Networking.PublicAccess.successPublicAccesNetworkNotificationDescription,
                    notification.status = MsPortalFx.Hubs.Notifications.NotificationStatus.Success;
                    notification.publish();
                },
                error => {
                    errorMessage.text(Utilities.extractErrorMessage(error.jqXHR));
                    errorMessage.visible(true);
                    saving(false);
                    notification.title = ClientResources.Networking.PublicAccess.failurePublicAccesNetworkNotificationTitle,
                    notification.description = ClientResources.Networking.PublicAccess.failurePublicAccesNetworkNotificationDescription,
                    notification.status = MsPortalFx.Hubs.Notifications.NotificationStatus.Error;
                    notification.publish();
                }
            );
    }

    async function updateRegistry(model: DataContext): Promise<any> {
        saving(true);
        errorMessage.visible(false);
        const registry = await model.machineLearningServicesData.getWorkspace(id, false);
        registry.properties.publicNetworkAccess = publicNetworkAccess();
        return model.machineLearningServicesData
            .updateWorkspace(id, registry, "PUT")
            .then(
                () => {
                    initialNetworkAccessValue(networkAccess());
                    errorMessage.visible(false);
                    saving(false);
                },
                error => {
                    errorMessage.text(Utilities.extractErrorMessage(error.jqXHR));
                    errorMessage.visible(true);
                    saving(false);
                }
            );
    }

    function discard(): void {
        networkAccess(initialNetworkAccessValue());
        ipRules(initialIpAllowList().map((ip: string) => {
            return {
                value: ko.observable(ip)
            };
        }));
    }

    return {
        control, load, toolbar
    }
}