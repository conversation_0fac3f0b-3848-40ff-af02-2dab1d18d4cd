import * as TemplateBlade from "Fx/Composition/TemplateBlade";
import * as CustomHtml from "Fx/Controls/CustomHtml";
import * as TabControl from "Fx/Controls/TabControl";
import { ArmId } from "Fx/ResourceManagement";
import * as ClientResources from "Resx/ClientResources";
import { WorkspaceMappedKind } from "../../../Shared/Enums";
import { DataContext } from "../../ResourceArea";
import * as NetworkingControl from "./NetworkingControl";

export interface Parameters {
    id: string;
    ipBasedAccessEnabled: boolean;
    kind: WorkspaceMappedKind;
}

@TemplateBlade.Decorator({
    htmlTemplate:
        `<div class="msportalfx-padding">
            <div data-bind="pcControl: control"></div>
        </div>`,
    styleSheets: ["../../Common/NetworkingSection.css", "../../../Shared/Styles/CustomStyles.css"]
})
@TemplateBlade.InjectableModel.Decorator(DataContext)
export class NetworkSettingsBlade {
    public title: string;
    public subtitle = ClientResources.PrivateEndpoints.menuText;
    public context: TemplateBlade.Context<Parameters, DataContext>;
    public control: CustomHtml.Contract | TabControl.Contract;

    public async onInitialize() {
        const { container, form, model, parameters } = this.context;
        const { id, ipBasedAccessEnabled, kind } = parameters;

        const armId = ArmId.parse(id);
        this.title = armId.resourceName;

        const connectionsControl = NetworkingControl.create({
            container,
            lifetimeManager: container
        }, form, model, { id, ipBasedAccessEnabled, kind });

        this.control = connectionsControl.control as TabControl.Contract;
        return connectionsControl.load(model);
    }
}
