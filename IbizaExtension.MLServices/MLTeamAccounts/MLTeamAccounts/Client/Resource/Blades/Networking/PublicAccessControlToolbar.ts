import * as Toolbar from "Fx/Controls/Toolbar";
import { LifetimeManager } from "Fx/Lifetime";
import * as ClientResources from "Resx/ClientResources";
import { FeedbackToolbarButton } from "Shared/FeedbackToolbarButton";
import { Container } from "Fx/Composition/TemplateBlade";
import { WorkspaceMappedKind } from "Shared/Enums";

interface Context {
    container: Container;
    lifetimeManager: LifetimeManager;
}

interface Options {
    valid: KnockoutObservableBase<boolean>;
    dirty: KnockoutObservableBase<boolean>;
    saving: KnockoutObservable<boolean>;
    refresh: () => void;
    discard: () => void;
    save: () => void;
    kind: WorkspaceMappedKind;
}

export function create(context: Context, options: Options): Toolbar.Contract {
    const { refresh, discard, save, saving, valid, dirty} = options;
    const toolbarItems = [
        Toolbar.ToolbarItems.createBasicButton(context.lifetimeManager, {
            label: ko.computed(context.lifetimeManager, () => saving()
                ? ClientResources.Networking.PublicAccess.Toolbar.savingText
                : ClientResources.Networking.PublicAccess.Toolbar.saveText),
            icon: MsPortalFx.Base.Images.Save(),
            disabled: ko.computed(context.lifetimeManager, () => !(dirty() && valid()) || saving()),
            onClick: save
        }),
        Toolbar.ToolbarItems.createBasicButton(context.lifetimeManager, {
            label: ClientResources.Networking.PublicAccess.Toolbar.discardChangesText,
            icon: MsPortalFx.Base.Images.Close(),
            disabled: ko.computed(context.lifetimeManager, () => !dirty() || saving()),
            onClick: discard
        }),
        Toolbar.ToolbarItems.createBasicButton(context.lifetimeManager, {
            label: ClientResources.Networking.PublicAccess.Toolbar.refreshText,
            icon: MsPortalFx.Base.Images.Refresh(),
            disabled: saving,
            onClick: refresh
        }),
        FeedbackToolbarButton.create({
            container: context.container,
            lifetimeManager: context.lifetimeManager,
            bladeName: "NetworkSettingsBlade",
            featureName: `${options.kind}_Networking_PublicAccess`,
            cesQuestion: ClientResources.CES.PublicAccess.question,
            cvaQuestion:  ClientResources.CVA.PublicAccess.question,
        })
    ];

    return Toolbar.create(context.lifetimeManager, { items: toolbarItems, showLabels: true })
}