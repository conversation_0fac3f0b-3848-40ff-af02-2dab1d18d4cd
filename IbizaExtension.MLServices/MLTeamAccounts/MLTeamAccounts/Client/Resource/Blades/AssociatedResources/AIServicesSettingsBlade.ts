import * as TemplateBlade from "Fx/Composition/TemplateBlade";
import * as Button from "Fx/Controls/Button";
import * as Section from "Fx/Controls/Section";
import * as TextBox from "Fx/Controls/TextBox";

import { DataContext } from "../../ResourceArea";
import { BladeParameters, BladeReturnData, formOptions } from "./Common";

import * as ClientResources from "Resx/ClientResources";

export interface Parameters extends BladeParameters {
    resourceGroupName?: string;
}

export interface ReturnData extends BladeReturnData { }

@TemplateBlade.Decorator({
    htmlTemplate: `
<div class='ext-associated-resource-settings msportalfx-padding'>
    <div data-bind='pcControl: formSection' class='ext-associated-resource-settings-form msportalfx-form'></div>
    <div data-bind='pcControl: buttonsSection' class='ext-associated-resource-settings-buttons'></div>
</div>`,
    styleSheets: ["../../../Shared/Styles/CustomStyles.css"]
})
@TemplateBlade.ForContextPane.Decorator({
    width: TemplateBlade.ForContextPane.Width.Medium
})
@TemplateBlade.ReturnsData.Decorator()
@TemplateBlade.InjectableModel.Decorator(DataContext)
export class AIServicesSettingsBlade {
    public title = ClientResources.AssetType.AIStudio.Hub.AIServices.Settings.title;
    public subtitle: string;
    public formSection: Section.Contract;
    public buttonsSection: Section.Contract;
    public context: TemplateBlade.Context<Parameters, DataContext> & TemplateBlade.ReturnsData.Context<ReturnData>;

    public async onInitialize(): Promise<void> {
        const { container, model, parameters } = this.context;

        const aiServicesName = TextBox.create(container, {
            label: ClientResources.AssetType.AIStudio.Hub.AIServices.Settings.title,
            validations: ko.observableArray<MsPortalFx.ViewModels.FormValidation>([
                new MsPortalFx.ViewModels.RequiredValidation(),
                new MsPortalFx.ViewModels.CustomValidation("", aiName => {
                    if (!aiName) {
                        return Q({ valid: true, message: null });
                    }

                    if (!parameters.resourceGroupName) {
                        return Q({ valid: true, message: null });
                    }

                    return model.aiServicesData.checkAIServicesAvailabilityNew(parameters.subscriptionId, parameters.resourceGroupName, aiName);
                })
            ]),
            value: ko.observable(parameters.name)
        });

        this.formSection = Section.create(container, {
            ...formOptions,
            children: [aiServicesName]
        });

        // Save and discard buttons
        const saveButton = Button.create(container, {
            disabled: ko.computed(container, () => !aiServicesName.valid()),
            text: ClientResources.AssociatedResource.Dropdown.SettingsBlade.saveButtonText,
            style: Button.Style.Primary,
            onClick: async () => {
                const allValid = await aiServicesName.triggerValidation();
                if (allValid) {
                    container.closeCurrentBlade({
                        name: aiServicesName.value()
                    });
                }
            }
        });

        const discardButton = Button.create(container, {
            text: ClientResources.AssociatedResource.Dropdown.SettingsBlade.discardButtonText,
            style: Button.Style.Secondary,
            onClick: () => {
                container.closeCurrentBlade();
            }
        });

        this.buttonsSection = Section.create(container, {
            children: [saveButton, discardButton]
        });
    }
}
