import * as TemplateBlade from "Fx/Composition/TemplateBlade";
import * as Button from "Fx/Controls/Button";
import * as Section from "Fx/Controls/Section";
import * as TextBox from "Fx/Controls/TextBox";

import { KeyVault } from "../../../Shared/Constants";
import { DataContext } from "../../ResourceArea";
import { BladeParameters, BladeReturnData, formOptions } from "./Common";

import * as ClientResources from "Resx/ClientResources";

export interface Parameters extends BladeParameters { }

export interface ReturnData extends BladeReturnData { }

@TemplateBlade.Decorator({
    htmlTemplate: `
<div class='ext-associated-resource-settings msportalfx-padding'>
    <div data-bind='pcControl: formSection' class='ext-associated-resource-settings-form msportalfx-form'></div>
    <div data-bind='pcControl: buttonsSection' class='ext-associated-resource-settings-buttons'></div>
</div>`,
    styleSheets: ["../../../Shared/Styles/CustomStyles.css"]
})
@TemplateBlade.ForContextPane.Decorator({
    width: TemplateBlade.ForContextPane.Width.Medium
})
@TemplateBlade.ReturnsData.Decorator()
@TemplateBlade.InjectableModel.Decorator(DataContext)
export class KeyvaultSettingsBlade {
    public title = ClientResources.Keyvault.Dropdown.SettingsBlade.title;
    public subtitle: string;
    public formSection: Section.Contract;
    public buttonsSection: Section.Contract;
    public context: TemplateBlade.Context<Parameters, DataContext> & TemplateBlade.ReturnsData.Context<ReturnData>;

    public async onInitialize(): Promise<void> {
        const { container, model, parameters } = this.context;

        const keyvaultName = TextBox.create(container, {
            infoBalloonContent: {
                htmlTemplate: `
    <div>${ClientResources.Keyvault.Dropdown.SettingsBlade.Name.infoDescription}</div>
    <ul>
        <li>${ClientResources.Keyvault.Dropdown.SettingsBlade.Name.infoItem1}</li>
        <li>${ClientResources.Keyvault.Dropdown.SettingsBlade.Name.infoItem2}</li>
        <li>${ClientResources.Keyvault.Dropdown.SettingsBlade.Name.infoItem3}</li>
        <li>${ClientResources.Keyvault.Dropdown.SettingsBlade.Name.infoItem4}</li>
    </ul>`
            },
            label: ClientResources.Keyvault.Dropdown.SettingsBlade.nameLabel,
            validations: ko.observableArray<MsPortalFx.ViewModels.FormValidation>([
                new MsPortalFx.ViewModels.RequiredValidation(),
                new MsPortalFx.ViewModels.LengthRangeValidation(KeyVault.nameMinLength, KeyVault.nameMaxLength),
                new MsPortalFx.ViewModels.CustomValidation("", kvName => {
                    if (!kvName || kvName.length < KeyVault.nameMinLength || kvName.length > KeyVault.nameMaxLength) {
                        return Q({ valid: true, message: null });
                    }

                    const keyvaultRegex = new RegExp(KeyVault.nameRegex);
                    if (!keyvaultRegex.test(kvName)) {
                        return Q({ valid: false, message: ClientResources.Keyvault.Dropdown.SettingsBlade.nameInvalidMessage });
                    }

                    return model.keyVaultData.checkKeyVaultAvailabilityNew(parameters.subscriptionId, kvName);
                })
            ]),
            value: ko.observable(parameters.name)
        });

        this.formSection = Section.create(container, {
            ...formOptions,
            children: [keyvaultName]
        });

        // Save and discard buttons
        const saveButton = Button.create(container, {
            disabled: ko.computed(container, () => !keyvaultName.valid()),
            text: ClientResources.AssociatedResource.Dropdown.SettingsBlade.saveButtonText,
            style: Button.Style.Primary,
            onClick: async () => {
                const allValid = await keyvaultName.triggerValidation();
                if (allValid) {
                    container.closeCurrentBlade({
                        name: keyvaultName.value()
                    });
                }
            }
        });

        const discardButton = Button.create(container, {
            text: ClientResources.AssociatedResource.Dropdown.SettingsBlade.discardButtonText,
            style: Button.Style.Secondary,
            onClick: () => {
                container.closeCurrentBlade();
            }
        });

        this.buttonsSection = Section.create(container, {
            children: [saveButton, discardButton]
        });
    }
}
