import * as TemplateBlade from "Fx/Composition/TemplateBlade";
import * as Button from "Fx/Controls/Button";
import * as Section from "Fx/Controls/Section";
import * as TextBox from "Fx/Controls/TextBox";

import { AppInsights } from "../../../Shared/Constants";
import { DataContext } from "../../ResourceArea";
import { BladeParameters, BladeReturnData, formOptions } from "./Common";

import * as ClientResources from "Resx/ClientResources";

export interface Parameters extends BladeParameters {
    resourceGroupName?: string;
}

export interface ReturnData extends BladeReturnData { }

@TemplateBlade.Decorator({
    htmlTemplate: `
<div class='ext-associated-resource-settings msportalfx-padding'>
    <div data-bind='pcControl: formSection' class='ext-associated-resource-settings-form msportalfx-form'></div>
    <div data-bind='pcControl: buttonsSection' class='ext-associated-resource-settings-buttons'></div>
</div>`,
    styleSheets: ["../../../Shared/Styles/CustomStyles.css"]
})
@TemplateBlade.ForContextPane.Decorator({
    width: TemplateBlade.ForContextPane.Width.Medium
})
@TemplateBlade.ReturnsData.Decorator()
@TemplateBlade.InjectableModel.Decorator(DataContext)
export class AppInsightsSettingsBlade {
    public title = ClientResources.AppInsights.Dropdown.SettingsBlade.title;
    public subtitle: string;
    public formSection: Section.Contract;
    public buttonsSection: Section.Contract;
    public context: TemplateBlade.Context<Parameters, DataContext> & TemplateBlade.ReturnsData.Context<ReturnData>;

    public async onInitialize(): Promise<void> {
        const { container, model, parameters } = this.context;

        const appInsightsName = TextBox.create(container, {
            infoBalloonContent: {
                htmlTemplate: `
    <div>${ClientResources.AppInsights.Dropdown.SettingsBlade.Name.infoDescription}</div>
    <ul>
        <li>${ClientResources.AppInsights.Dropdown.SettingsBlade.Name.infoItem1}</li>
        <li>${ClientResources.AppInsights.Dropdown.SettingsBlade.Name.infoItem2}</li>
        <li>${ClientResources.AppInsights.Dropdown.SettingsBlade.Name.infoItem3}</li>
        <li>${ClientResources.AppInsights.Dropdown.SettingsBlade.Name.infoItem4}</li>
    </ul>`
            },
            label: ClientResources.AppInsights.Dropdown.SettingsBlade.nameLabel,
            validations: ko.observableArray<MsPortalFx.ViewModels.FormValidation>([
                new MsPortalFx.ViewModels.RequiredValidation(),
                new MsPortalFx.ViewModels.CustomValidation("", aiName => {
                    if (!aiName) {
                        return Q({ valid: true, message: null });
                    }

                    if (
                        aiName.length < AppInsights.nameMinLength ||
                        aiName.length > AppInsights.nameMaxLength ||
                        aiName.charAt(aiName.length - 1) === "." ||
                        aiName.charAt(aiName.length - 1) === " " ||
                        aiName.charAt(0) === " " ||
                        aiName.match(AppInsights.invalidCharsRegex)
                    ) {
                        return Q({ valid: false, message: ClientResources.AppInsights.Dropdown.SettingsBlade.nameInvalidMessage });
                    }

                    if (!parameters.resourceGroupName) {
                        return Q({ valid: true, message: null });
                    }

                    return model.appInsightsData.checkAppInsightsAvailabilityNew(parameters.subscriptionId, parameters.resourceGroupName, aiName);
                })
            ]),
            value: ko.observable(parameters.name)
        });

        this.formSection = Section.create(container, {
            ...formOptions,
            children: [appInsightsName]
        });

        // Save and discard buttons
        const saveButton = Button.create(container, {
            disabled: ko.computed(container, () => !appInsightsName.valid()),
            text: ClientResources.AssociatedResource.Dropdown.SettingsBlade.saveButtonText,
            style: Button.Style.Primary,
            onClick: async () => {
                const allValid = await appInsightsName.triggerValidation();
                if (allValid) {
                    container.closeCurrentBlade({
                        name: appInsightsName.value()
                    });
                }
            }
        });

        const discardButton = Button.create(container, {
            text: ClientResources.AssociatedResource.Dropdown.SettingsBlade.discardButtonText,
            style: Button.Style.Secondary,
            onClick: () => {
                container.closeCurrentBlade();
            }
        });

        this.buttonsSection = Section.create(container, {
            children: [saveButton, discardButton]
        });
    }
}
