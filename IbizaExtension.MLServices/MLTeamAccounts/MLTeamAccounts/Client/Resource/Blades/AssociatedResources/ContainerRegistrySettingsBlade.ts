import * as TemplateBlade from "Fx/Composition/TemplateBlade";
import * as Button from "Fx/Controls/Button";
import * as DropDown from "Fx/Controls/DropDown";
import * as Section from "Fx/Controls/Section";
import * as TextBox from "Fx/Controls/TextBox";

import { ContainerRegistry } from "../../../Shared/Constants";
import { DataContext } from "../../ResourceArea";

import * as ClientResources from "Resx/ClientResources";
import { BladeParameters, BladeReturnData, formOptions } from "./Common";

const skus = [
    { text: ClientResources.ContainerRegistry.Dropdown.SettingsBlade.skuBasic, value: "Basic" },
    { text: ClientResources.ContainerRegistry.Dropdown.SettingsBlade.skuStandard, value: "Standard", isDefault: true },
    { text: ClientResources.ContainerRegistry.Dropdown.SettingsBlade.skuPremium, value: "Premium" }
];

export interface Parameters extends BladeParameters { }

export interface ReturnData extends BladeReturnData {
    sku: string;
}

@TemplateBlade.Decorator({
    htmlTemplate: `
<div class='ext-associated-resource-settings msportalfx-padding'>
    <div data-bind='pcControl: formSection' class='ext-associated-resource-settings-form msportalfx-form'></div>
    <div data-bind='pcControl: buttonsSection' class='ext-associated-resource-settings-buttons'></div>
</div>`,
    styleSheets: ["../../../Shared/Styles/CustomStyles.css"]
})
@TemplateBlade.ForContextPane.Decorator({
    width: TemplateBlade.ForContextPane.Width.Medium
})
@TemplateBlade.ReturnsData.Decorator()
@TemplateBlade.InjectableModel.Decorator(DataContext)
export class ContainerRegistrySettingsBlade {
    public title = ClientResources.ContainerRegistry.Dropdown.SettingsBlade.title;
    public subtitle: string;
    public formSection: Section.Contract;
    public buttonsSection: Section.Contract;
    public context: TemplateBlade.Context<Parameters, DataContext> & TemplateBlade.ReturnsData.Context<ReturnData>;

    public async onInitialize(): Promise<void> {
        const { container, model, parameters } = this.context;

        const containerName = TextBox.create(container, {
            infoBalloonContent: {
                htmlTemplate: `
    <div>${ClientResources.ContainerRegistry.Dropdown.SettingsBlade.Name.infoDescription}</div>
    <ul>
        <li>${ClientResources.ContainerRegistry.Dropdown.SettingsBlade.Name.infoItem1}</li>
        <li>${ClientResources.ContainerRegistry.Dropdown.SettingsBlade.Name.infoItem2}</li>
        <li>${ClientResources.ContainerRegistry.Dropdown.SettingsBlade.Name.infoItem3}</li>
    </ul>`
            },
            label: ClientResources.ContainerRegistry.Dropdown.SettingsBlade.nameLabel,
            validations: ko.observableArray<MsPortalFx.ViewModels.FormValidation>([
                new MsPortalFx.ViewModels.RequiredValidation(),
                new MsPortalFx.ViewModels.CustomValidation("", crName => {
                    if (!crName) {
                        return Q({ valid: true, message: null });
                    }

                    const containerNameRegex = new RegExp(ContainerRegistry.nameRegex);
                    if (!containerNameRegex.test(crName)) {
                        return Q({ valid: false, message: ClientResources.ContainerRegistry.Dropdown.SettingsBlade.nameInvalidMessage });
                    }

                    return model.containerRegistryData.checkContainerRegistryAvailability(parameters.subscriptionId, crName);
                })
            ]),
            value: ko.observable(parameters.name)
        });

        const sku = DropDown.create(container, {
            infoBalloonContent: {
                htmlTemplate: `
    <div>${ClientResources.ContainerRegistry.Dropdown.SettingsBlade.Sku.info}</div>
    <div><a href='https://azure.microsoft.com/en-us/pricing/details/container-registry'>${ClientResources.ContainerRegistry.Dropdown.SettingsBlade.Sku.infoLearnMore}</a></div>
            `},
            items: [...skus],
            label: ClientResources.ContainerRegistry.Dropdown.SettingsBlade.skuLabel,
            validations: [new MsPortalFx.ViewModels.RequiredValidation()],
            value: skus.filter(sku => sku.isDefault)[0]?.value
        });

        this.formSection = Section.create(container, {
            ...formOptions,
            children: [containerName, sku]
        });

        // Save and discard buttons
        const saveButton = Button.create(container, {
            disabled: ko.computed(container, () => !containerName.valid()),
            text: ClientResources.AssociatedResource.Dropdown.SettingsBlade.saveButtonText,
            style: Button.Style.Primary,
            onClick: async () => {
                const allValid = (await Q.allResolved([containerName.triggerValidation(), sku.triggerValidation()])).every(promise => !!promise.valueOf());
                if (allValid) {
                    container.closeCurrentBlade({
                        name: containerName.value(),
                        sku: sku.value()
                    });
                }
            }
        });

        const discardButton = Button.create(container, {
            text: ClientResources.AssociatedResource.Dropdown.SettingsBlade.discardButtonText,
            style: Button.Style.Secondary,
            onClick: () => {
                container.closeCurrentBlade();
            }
        });

        this.buttonsSection = Section.create(container, {
            children: [saveButton, discardButton]
        });
    }
}
