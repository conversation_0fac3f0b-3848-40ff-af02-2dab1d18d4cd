import * as TemplateBlade from "Fx/Composition/TemplateBlade";
import * as Button from "Fx/Controls/Button";
import * as CheckBox from "Fx/Controls/CheckBox";
import * as CustomHtml from "Fx/Controls/CustomHtml";
import * as DropDown from "Fx/Controls/DropDown";
import * as OptionsGroup from "Fx/Controls/OptionsGroup";
import * as Section from "Fx/Controls/Section";
import * as TextBox from "Fx/Controls/TextBox";

import { IStorageSku } from "../../../Resource/Data/Data.Types";
import { StorageAccount } from "../../../Shared/Constants";
import * as Format from "../../../Shared/Controls/Format";
import { DocLinkIds, getDocumentUrl } from "../../../Shared/DocLinks";
import { Flighting } from "../../../Shared/Flighting";
import { DataContext } from "../../ResourceArea";
import { BladeParameters, BladeReturnData } from "./Common";

import * as ClientResources from "Resx/ClientResources";

export interface Parameters extends BladeParameters {
    location?: string;
    hnsCreateOptionSupported?: boolean;
}

export interface ReturnData extends BladeReturnData {
    sku: string;
    hnsEnabled: boolean;
}

@TemplateBlade.Decorator({
    htmlTemplate: `
<div class='ext-associated-resource-settings msportalfx-padding'>
    <div data-bind='pcControl: formSection' class='ext-associated-resource-settings-form msportalfx-form'></div>
    <div data-bind='pcControl: buttonsSection' class='ext-associated-resource-settings-buttons'></div>
</div>`,
    styleSheets: ["../../../Shared/Styles/CustomStyles.css"]
})
@TemplateBlade.ForContextPane.Decorator({
    width: TemplateBlade.ForContextPane.Width.Medium
})
@TemplateBlade.ReturnsData.Decorator()
@TemplateBlade.InjectableModel.Decorator(DataContext)
export class StorageAccountSettingsBlade {
    public title = ClientResources.StorageAccount.Dropdown.SettingsBlade.title;
    public subtitle: string;
    public formSection: Section.Contract;
    public buttonsSection: Section.Contract;
    public context: TemplateBlade.Context<Parameters, DataContext> & TemplateBlade.ReturnsData.Context<ReturnData>;

    public async onInitialize(): Promise<void> {
        const { container, parameters, model } = this.context;

        const storageName = TextBox.create(container, {
            infoBalloonContent: {
                htmlTemplate: `
    <div>${ClientResources.StorageAccount.Dropdown.SettingsBlade.Name.infoDescription}</div>
    <ul>
        <li>${ClientResources.StorageAccount.Dropdown.SettingsBlade.Name.infoItem1}</li>
        <li>${ClientResources.StorageAccount.Dropdown.SettingsBlade.Name.infoItem2}</li>
        <li>${ClientResources.StorageAccount.Dropdown.SettingsBlade.Name.infoItem3}</li>
    </ul>`
            },
            label: ClientResources.StorageAccount.Dropdown.SettingsBlade.nameLabel,
            validations: ko.observableArray<MsPortalFx.ViewModels.FormValidation>([
                new MsPortalFx.ViewModels.RequiredValidation(),
                new MsPortalFx.ViewModels.CustomValidation("", saName => {
                    if (!saName) {
                        return Q({ valid: true, message: null });
                    }

                    const storageAccountNameRegex = new RegExp(StorageAccount.nameRegex);
                    if (!storageAccountNameRegex.test(saName)) {
                        return Q({ valid: false, message: ClientResources.StorageAccount.Dropdown.SettingsBlade.nameInvalidMessage });
                    }

                    return model.storageAccountData.checkStorageAccountAvailability(parameters.subscriptionId, saName);
                })
            ]),
            value: ko.observable(parameters.name)
        });

        // Load skus - performance + replication denote SKU
        let availableSkus: Array<IStorageSku> = [];
        const performanceItems = [
            { text: ClientResources.StorageAccount.Dropdown.SettingsBlade.performanceStandard, value: "Standard" },
            { text: ClientResources.StorageAccount.Dropdown.SettingsBlade.performancePremium, value: "Premium" }
        ];
        const performanceValue = ko.observable(performanceItems[0].value);
        const replicationItems = ko.observableArray<DropDown.Item<string>>([]);
        const replicationLoading = ko.observable(true);
        const replicationValue = ko.observable("");
        const replicationSubLabel = ko.observable("");
        model.storageAccountData.getStorageSKUs(parameters.subscriptionId).then(
            skus => {
                if (parameters.location) {
                    availableSkus = [
                        ...skus.filter(
                            sku =>
                                MsPortalFx.localeCompareIgnoreCase(sku.kind, "StorageV2") === 0 &&
                                sku.locations &&
                                sku.locations.findIndex(l => MsPortalFx.localeCompareIgnoreCase(l, parameters.location) === 0) >= 0
                        )
                    ];
                }

                this._populateReplicationItems(availableSkus, performanceValue(), replicationItems, replicationValue);
                replicationLoading(false);
            },
            reason => {
                replicationSubLabel(reason.message);
                replicationLoading(false);
            }
        );

        const performance = OptionsGroup.create(container, {
            infoBalloonContent: ClientResources.StorageAccount.Dropdown.SettingsBlade.Performance.info,
            items: performanceItems,
            label: ClientResources.StorageAccount.Dropdown.SettingsBlade.performanceLabel,
            validations: [new MsPortalFx.ViewModels.RequiredValidation()],
            value: performanceValue,
            visible: Flighting.PremiumStorageAccount.isEnabled(parameters.subscriptionId)
        });

        performance.value.subscribe(container, newPerformance => {
            this._populateReplicationItems(availableSkus, newPerformance, replicationItems, replicationValue);
        });

        const replication = DropDown.create(container, {
            infoBalloonContent: {
                htmlTemplate: `
    <div>${ClientResources.StorageAccount.Dropdown.SettingsBlade.Replication.info}</div>
    <div><a href='${getDocumentUrl(DocLinkIds.StorageAccountReplication)}'>${
                    ClientResources.StorageAccount.Dropdown.SettingsBlade.Replication.infoLearnMore
                }</a></div>
            `
            },
            items: replicationItems,
            label: ClientResources.StorageAccount.Dropdown.SettingsBlade.replicationLabel,
            loading: replicationLoading,
            subLabel: replicationSubLabel,
            validations: [new MsPortalFx.ViewModels.RequiredValidation()],
            value: replicationValue
        });

        const hnsDescriptionTitle = CustomHtml.create(container, {
            htmlTemplate: `<h2 class="az-text-header2">${ClientResources.StorageAccount.Dropdown.SettingsBlade.hnsDescriptionTitle}</h2>`
        });

        const hnsDescriptionText = Format.create(container, {
            format: ClientResources.StorageAccount.Dropdown.SettingsBlade.hnsDescriptionText,
            children: [
                {
                    htmlTemplate: `<a href="${getDocumentUrl(DocLinkIds.DataLakeStorageGen2)}" role="link">${
                        ClientResources.StorageAccount.Dropdown.SettingsBlade.learnMoreText
                    }</a>`
                }
            ]
        });

        const hnsEnabled = CheckBox.create(container, {
            label: ClientResources.StorageAccount.Dropdown.SettingsBlade.hnsCheckLabel,
            value: ko.observable(false)
        });

        const children: any[] = [storageName, performance, replication];
        if (parameters.hnsCreateOptionSupported) {
            children.push(...[hnsDescriptionTitle, hnsDescriptionText, hnsEnabled]);
        }

        this.formSection = Section.create(container, { leftLabelPosition: true, children });

        // Save and discard buttons
        const saveButton = Button.create(container, {
            disabled: ko.computed(container, () => !storageName.valid()),
            text: ClientResources.AssociatedResource.Dropdown.SettingsBlade.saveButtonText,
            style: Button.Style.Primary,
            onClick: async () => {
                const allValid = (
                    await Q.allResolved([storageName.triggerValidation(), performance.triggerValidation(), replication.triggerValidation()])
                ).every(promise => !!promise.valueOf());

                if (allValid) {
                    container.closeCurrentBlade({
                        name: storageName.value(),
                        sku: replication.value(),
                        hnsEnabled: hnsEnabled.value()
                    });
                }
            }
        });

        const discardButton = Button.create(container, {
            text: ClientResources.AssociatedResource.Dropdown.SettingsBlade.discardButtonText,
            style: Button.Style.Secondary,
            onClick: () => {
                container.closeCurrentBlade();
            }
        });

        this.buttonsSection = Section.create(container, {
            children: [saveButton, discardButton]
        });
    }

    private _populateReplicationItems(
        availableSkus: Array<IStorageSku>,
        performance: string,
        replicationItems: KnockoutObservableArray<DropDown.Item<string>>,
        replicationValue: KnockoutObservableBase<string>
    ): void {
        const currentReplicationItems = availableSkus
            .filter(sku => MsPortalFx.localeCompareIgnoreCase(sku.tier, performance) === 0)
            .map(sku => {
                const skuReplication = this._getSkuReplication(sku.name);
                return {
                    text: skuReplication?.text,
                    value: skuReplication?.value
                };
            });
        replicationItems(currentReplicationItems);

        const defaultItem = currentReplicationItems.filter(ri => MsPortalFx.localeCompareIgnoreCase(ri.value, "Standard_LRS") === 0)[0];

        replicationValue(defaultItem ? defaultItem.value : currentReplicationItems.length > 0 ? currentReplicationItems[0].value : "");
    }

    private _getSkuReplication(skuName: string): { text: string; value: string } | undefined {
        const skuParts = skuName.split("_");
        const replication = (skuParts[1] || "").toUpperCase();
        switch (replication) {
            case "LRS":
                return { text: ClientResources.StorageAccount.Dropdown.SettingsBlade.replicationLRS, value: skuName };
            case "GRS":
                return { text: ClientResources.StorageAccount.Dropdown.SettingsBlade.replicationGRS, value: skuName };
            case "ZRS":
                return { text: ClientResources.StorageAccount.Dropdown.SettingsBlade.replicationZRS, value: skuName };
            case "GZRS":
                return { text: ClientResources.StorageAccount.Dropdown.SettingsBlade.replicationGZRS, value: skuName };
            case "RAGRS":
                return { text: ClientResources.StorageAccount.Dropdown.SettingsBlade.replicationRAGRS, value: skuName };
            case "RAGZRS":
                return { text: ClientResources.StorageAccount.Dropdown.SettingsBlade.replicationRAGZRS, value: skuName };
        }

        return undefined;
    }
}
