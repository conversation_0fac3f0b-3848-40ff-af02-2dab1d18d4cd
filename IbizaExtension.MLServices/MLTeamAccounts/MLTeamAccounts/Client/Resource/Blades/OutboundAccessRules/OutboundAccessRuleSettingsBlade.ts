import * as TemplateBlade from "Fx/Composition/TemplateBlade";
import * as EditableGrid from "Fx/Controls/EditableGrid";
import * as Button from "Fx/Controls/Button";
import * as DropDown from "Fx/Controls/DropDown";
import * as Section from "Fx/Controls/Section";
import * as TextBox from "Fx/Controls/TextBox";
import * as CheckBox from "Fx/Controls/CheckBox";
import * as InfoBalloon from "Fx/Controls/InfoBalloon";
import * as InfoBox from "Fx/Controls/InfoBox";
import * as SubscriptionsDropDown from "Fx/Controls/SubscriptionDropDown";
import * as ResourceGroupDropDown from "Fx/Controls/ResourceGroupDropDown";
import * as Constants from "../../../Shared/Constants";
import { DataContext } from "../../ResourceArea";
import { DocLinkIds, getDocumentUrl } from "../../../Shared/DocLinks";
import * as ClientResources from "Resx/ClientResources";
import { OutboundRule, RuleCategory, RuleType } from "Resource/Data/Data.Types";
import { ArmResource } from "Fx/ResourceManagement";
import { ManagedNetworkIsolationMode } from "Resource/Common/NetworkingDataModel";
import { CustomV, ValidationResult } from "Fx/Controls/Validations";

export interface OutboundAccessRuleSettingsBladeParameters {
    name?: string;
    outboundRule?: OutboundRule;
    ruleCategory?: RuleCategory;
    gridRules?: string[];
    networkIsolationMode: ManagedNetworkIsolationMode;
    isExisting?: boolean;
}

export interface OutboundAccessRuleSettingsBladeReturnData {
    name: string;
    outboundRule: OutboundRule;
}

interface SubResourceNameDetail {
    name: string;
    displayName: string;
}

interface DestinationDetailsParam {
    subscriptionId: string;
    resourceGroupName: string;
    resourceType: string;
    resourceName: string;
    subResourceName: string;
    resourceId: string;
    sparkEnabled: boolean;
    serviceTag: string;
    protocol: string;
    portRange: string;
    fqdnDestination: string;
    status: string;
    sparkStatus: string;
}

class FqdnModel {
    value: KnockoutObservable<string>;
}

const StorageAccountResourceType = "Microsoft.Storage/storageAccounts";
const RegistriesResourceType = "Microsoft.MachineLearningServices/registries";
const ApplicationGatewayResourceType = "Microsoft.Network/applicationGateways";

@TemplateBlade.Decorator({
    htmlTemplate: `
<div class='ext-associated-resource-settings msportalfx-padding'>
    <div data-bind='pcControl: formSection' class='ext-associated-resource-settings-form msportalfx-form'></div>
    <div data-bind='pcControl: buttonsSection' class='ext-associated-resource-settings-buttons'></div>
</div>`,
    styleSheets: ["../../../Shared/Styles/CustomStyles.css", "../../../Shared/Styles/PropertiesStyles.css"]
})
@TemplateBlade.ForContextPane.Decorator({
    width: TemplateBlade.ForContextPane.Width.Medium
})
@TemplateBlade.ReturnsData.Decorator()
@TemplateBlade.InjectableModel.Decorator(DataContext)
export class OutboundAccessRuleSettingsBlade {
    public title = ClientResources.OutboundAccessRule.SettingsBlade.title;
    public subtitle: string;
    public formSection: Section.Contract;
    public buttonsSection: Section.Contract;
    public context: TemplateBlade.Context<OutboundAccessRuleSettingsBladeParameters, DataContext> &
        TemplateBlade.ReturnsData.Context<OutboundAccessRuleSettingsBladeReturnData>;

    private _isReadOnly: boolean;
    private _isExisting: boolean;
    private _appGatewayFqdnRule: KnockoutObservableBase<boolean>;
    private _appGatewayFqdnTableRowsValid: KnockoutObservableBase<boolean>;
    private _showAppGatewayFqdnTableRequiredWarning: KnockoutObservableBase<boolean>;
    private _appGatewayFqdnsRequiredError: KnockoutObservableBase<boolean>;
    private _viewModel: {
        sparkEnabled: KnockoutObservableBase<boolean>;
        name: KnockoutObservable<string>;
        type: KnockoutObservable<RuleType>;
        resourceType: KnockoutObservable<string>;
        subResourceName: KnockoutObservable<string>;
        resourceId: KnockoutObservable<string>;
        resourceName: KnockoutObservable<string>;
        serviceTag: KnockoutObservable<string>;
        protocol: KnockoutObservable<string>;
        portRange: KnockoutObservable<string>;
        fqdnDestination: KnockoutObservable<string>;
        fqdns: KnockoutObservableArray<FqdnModel>;
    };
    private _armResourceList = ko.observableArray<ArmResource>([]);
    private _resourceNames = ko.observableArray<string>([]);
    private _subResourceNameDetails = ko.observableArray<SubResourceNameDetail>([]);
    private _resourceTypeApiVersionMapping = ko.observable<{ [resourceType: string]: string }>({});
    private _providersLoading = ko.observable<boolean>(false);
    private _resourceLoading = ko.observable<boolean>(false);
    private _subResourceLoading = ko.observable<boolean>(false);

    public async onInitialize(): Promise<void> {
        const { container, parameters, model } = this.context;
        this._isExisting = !!parameters.isExisting;
        this._isReadOnly =
            parameters.ruleCategory === "Required" ||
            parameters.ruleCategory === "Recommended" ||
            parameters.ruleCategory === "Dependency" ||
            (parameters.outboundRule?.type === "PrivateEndpoint" && this._isExisting);
        const parameterDetails = this._getDestinationDetails(parameters.outboundRule);
        const resourceTypeDropDownItems = ko.observableArray<DropDown.Item<string>>([]);
        const resourceNameDropDownItems = ko.observableArray<DropDown.Item<string>>([]);
        const subResourceDropDownItems = ko.observableArray<DropDown.Item<string>>([]);
        this._updateResourceTypeApiVersionMapping(model, parameterDetails.subscriptionId);
        this._updateArmResources(model, parameterDetails.subscriptionId, parameterDetails.resourceGroupName);
        this._viewModel = {
            name: ko.observable(parameters.name || ""),
            type: ko.observable(parameters.outboundRule?.type || "PrivateEndpoint"),
            resourceType: ko.observable(parameterDetails.resourceType),
            resourceName: ko.observable(parameterDetails.resourceName),
            subResourceName: ko.observable(parameterDetails.subResourceName),
            resourceId: ko.observable(parameterDetails.resourceId),
            sparkEnabled: ko.observable(parameterDetails.sparkEnabled),
            serviceTag: ko.observable(parameterDetails.serviceTag),
            protocol: ko.observable(parameterDetails.protocol),
            portRange: ko.observable(parameterDetails.portRange),
            fqdnDestination: ko.observable(parameterDetails.fqdnDestination),
            fqdns: ko.observableArray<FqdnModel>(
                parameters.outboundRule?.type === "PrivateEndpoint"
                    ? parameters.outboundRule?.fqdns?.map(fqdn => {
                          return { value: ko.observable<string>(fqdn) };
                      }) || []
                    : []
            )
        };
        this._appGatewayFqdnRule = ko.pureComputed(() => {
            return this._viewModel.type() === "PrivateEndpoint" && this._viewModel.resourceType() === ApplicationGatewayResourceType;
        });
        this._appGatewayFqdnsRequiredError = ko.pureComputed(() => {
            return this._appGatewayFqdnRule() && this._viewModel.fqdns().length === 0;
        });
        this._showAppGatewayFqdnTableRequiredWarning = ko.observable<boolean>(false);

        const ruleName = TextBox.create(container, {
            infoBalloonContent: ClientResources.OutboundAccessRule.SettingsBlade.ruleNameBalloonContent,
            label: ClientResources.OutboundAccessRule.SettingsBlade.ruleNameLabel,
            validations: ko.observableArray<MsPortalFx.ViewModels.FormValidation>([
                new MsPortalFx.ViewModels.RequiredValidation(),
                new MsPortalFx.ViewModels.CustomValidation("", ruleName => {
                    if (parameters.gridRules?.includes(ruleName)) {
                        return Q({ valid: false, message: ClientResources.OutboundAccessRule.SettingsBlade.ruleNameAlreadyExists });
                    }

                    return Q({ valid: true, message: null });
                })
            ]),
            value: this._viewModel.name,
            disabled: this._isReadOnly || this._isExisting
        });

        const destinationItems = [
            {
                value: "PrivateEndpoint",
                text: "Private Endpoint"
            }
        ];
        if (parameters.networkIsolationMode === ManagedNetworkIsolationMode.AllowOnlyApprovedOutbound) {
            destinationItems.push(
                ...[
                    {
                        value: "ServiceTag",
                        text: "Service Tag"
                    },
                    {
                        value: "FQDN",
                        text: "FQDN"
                    }
                ]
            );
        }
        const destinationType = DropDown.create<RuleType>(container, {
            infoBalloonContent: ClientResources.OutboundAccessRule.SettingsBlade.destinationTypeBalloonContent,
            items: destinationItems,
            label: ClientResources.OutboundAccessRule.SettingsBlade.destinationTypeLabel,
            validations: [new MsPortalFx.ViewModels.RequiredValidation()],
            value: this._viewModel.type,
            disabled: this._isReadOnly,
            cssClass: ko.pureComputed(() => {
                return this._viewModel.type() === "FQDN" ? "ext-ml-no-padding" : "";
            })
        });

        const subscriptionsDropDownOptions: SubscriptionsDropDown.Options = {
            infoBalloonContent: ClientResources.OutboundAccessRule.SettingsBlade.subscriptionBalloonContent,
            initialSubscriptionId: parameterDetails.subscriptionId || "",
            validations: ko.observableArray([new MsPortalFx.ViewModels.RequiredValidation(ClientResources.selectSubscription)]),
            resourceProviders: ko.observable([Constants.machineLearningServicesResourcesProvider]),
            visible: ko.pureComputed(() => {
                return this._viewModel.type() === "PrivateEndpoint" && !this._isReadOnly;
            })
        };
        const subscriptionsDropDown = SubscriptionsDropDown.create(container, subscriptionsDropDownOptions);

        const subscriptionTextBox = TextBox.create(container, {
            infoBalloonContent: ClientResources.OutboundAccessRule.SettingsBlade.subscriptionBalloonContent,
            label: ClientResources.OutboundAccessRule.SettingsBlade.subscriptionLabel,
            value: ko.pureComputed(() => {
                return subscriptionsDropDown.value()?.displayName || "";
            }),
            disabled: this._isReadOnly,
            visible: ko.pureComputed(() => {
                return this._viewModel.type() === "PrivateEndpoint" && this._isReadOnly;
            })
        });

        subscriptionsDropDown.value.subscribe(container, subscription => {
            this._updateResourceTypeApiVersionMapping(model, subscription.subscriptionId);
        });

        const resourceGroupDropDownOptions: ResourceGroupDropDown.Options = {
            infoBalloonContent: ClientResources.OutboundAccessRule.SettingsBlade.resourceGroupBalloonContent,
            initialResourceGroupName: parameterDetails.resourceGroupName || "",
            subscriptionId: subscriptionsDropDown.value,
            allowedMode: ResourceGroupDropDown.Mode.UseExisting,
            validations: ko.observableArray([new MsPortalFx.ViewModels.RequiredValidation()]),
            visible: ko.pureComputed(() => {
                return this._viewModel.type() === "PrivateEndpoint" && !this._isReadOnly;
            })
        };
        const resourceGroupDropDown = ResourceGroupDropDown.create(container, resourceGroupDropDownOptions);

        const resourceGroupTextBox = TextBox.create(container, {
            infoBalloonContent: ClientResources.OutboundAccessRule.SettingsBlade.resourceGroupBalloonContent,
            label: ClientResources.OutboundAccessRule.SettingsBlade.resourceGroupLabel,
            value: ko.pureComputed(() => {
                let initialResourceGroupName: string = "";
                if (typeof resourceGroupDropDownOptions?.initialResourceGroupName === "string") {
                    initialResourceGroupName = resourceGroupDropDownOptions.initialResourceGroupName;
                } else if (Array.isArray(resourceGroupDropDownOptions?.initialResourceGroupName)) {
                    initialResourceGroupName = resourceGroupDropDownOptions.initialResourceGroupName[0] || "";
                }
                return resourceGroupDropDown.value()?.value.name || initialResourceGroupName || "";
            }),
            disabled: this._isReadOnly,
            visible: ko.pureComputed(() => {
                return this._viewModel.type() === "PrivateEndpoint" && this._isReadOnly;
            })
        });

        resourceGroupDropDown.value.subscribe(container, resourceGroup => {
            if (this._isReadOnly) {
                return;
            }
            this._updateArmResources(model, subscriptionsDropDown.value().subscriptionId, resourceGroup.value.name);
        });

        this._armResourceList.subscribe(container, armResourceList => {
            if (armResourceList.length === 0) {
                this._resourceLoading(false);
                resourceTypeDropDownItems([]);
                return;
            }
            this._resourceLoading(true);
            resourceTypeDropDownItems([]);
            const types = new Set<string>();
            armResourceList.forEach((item: ArmResource) => {
                types.add(item.type);
            });
            resourceTypeDropDownItems(this._createDropdownItems(Array.from(types)));
            const selectedType = Array.from(types).find(type => type.toLocaleLowerCase() === this._viewModel.resourceType().toLocaleLowerCase());
            if (selectedType) {
                this._viewModel.resourceType(selectedType);
            } else if (!this._isReadOnly) {
                this._viewModel.resourceType(resourceTypeDropDownItems()[0].value);
            }
            this._resourceLoading(false);
        });

        this._resourceTypeApiVersionMapping.subscribe(container, _resourceTypeApiVersionMapping => {
            if (this._isReadOnly) {
                return;
            }
            this._updateSubResources(model);
        });

        const resourceType = this._isReadOnly
            ? TextBox.create(container, {
                  infoBalloonContent: ClientResources.OutboundAccessRule.SettingsBlade.resourceTypeBalloonContent,
                  label: ClientResources.OutboundAccessRule.SettingsBlade.resourceTypeLabel,
                  value: ko.pureComputed(() => {
                      return this._viewModel.resourceType() || "";
                  }),
                  disabled: this._isReadOnly,
                  visible: ko.pureComputed(() => {
                      return this._viewModel.type() === "PrivateEndpoint" && this._isReadOnly;
                  })
              })
            : DropDown.create<string>(container, {
                  infoBalloonContent: ClientResources.OutboundAccessRule.SettingsBlade.resourceTypeBalloonContent,
                  items: resourceTypeDropDownItems,
                  label: ClientResources.OutboundAccessRule.SettingsBlade.resourceTypeLabel,
                  validations: ko.observableArray<MsPortalFx.ViewModels.FormValidation>([new MsPortalFx.ViewModels.RequiredValidation()]),
                  value: this._viewModel.resourceType,
                  visible: ko.pureComputed(() => {
                      return this._viewModel.type() === "PrivateEndpoint" && !this._isReadOnly;
                  }),
                  loading: this._resourceLoading
              });

        resourceType.value.subscribe(container, () => {
            if (this._isReadOnly) {
                return;
            }
            this._updateResourceNames();
        });

        this._resourceNames.subscribe(container, resourceNames => {
            resourceNameDropDownItems(this._createDropdownItems(resourceNames));
            if (resourceNames.length == 0) {
                return;
            }
            if (resourceNames.includes(this._viewModel.resourceName())) {
                this._viewModel.resourceName(this._viewModel.resourceName());
            } else if (!this._isReadOnly) {
                this._viewModel.resourceName(resourceNames[0]);
            }
        });

        this._subResourceNameDetails.subscribe(container, subResourceNameDetails => {
            subResourceDropDownItems(this._getSubResourceDropdownItems(subResourceNameDetails));
            if (subResourceNameDetails.length > 0) {
                const selectedSubResource = subResourceNameDetails.find(
                    item => item.name.toLocaleLowerCase() === this._viewModel.subResourceName().toLocaleLowerCase()
                );
                if (selectedSubResource) {
                    this._viewModel.subResourceName(selectedSubResource.name);
                } else if (!this._isReadOnly) {
                    this._viewModel.subResourceName(subResourceNameDetails[0].name);
                }
            }
        });

        const resourceName = this._isReadOnly
            ? TextBox.create(container, {
                  infoBalloonContent: ClientResources.OutboundAccessRule.SettingsBlade.resourceNameBalloonContent,
                  label: ClientResources.OutboundAccessRule.SettingsBlade.resourceNameLabel,
                  value: ko.pureComputed(() => {
                      return this._viewModel.resourceName() || "";
                  }),
                  disabled: this._isReadOnly,
                  visible: ko.pureComputed(() => {
                      return this._viewModel.type() === "PrivateEndpoint";
                  })
              })
            : DropDown.create<string>(container, {
                  infoBalloonContent: ClientResources.OutboundAccessRule.SettingsBlade.resourceNameBalloonContent,
                  items: resourceNameDropDownItems,
                  label: ClientResources.OutboundAccessRule.SettingsBlade.resourceNameLabel,
                  validations: ko.observableArray<MsPortalFx.ViewModels.FormValidation>([new MsPortalFx.ViewModels.RequiredValidation()]),
                  value: this._viewModel.resourceName,
                  visible: ko.pureComputed(() => {
                      return this._viewModel.type() === "PrivateEndpoint" && !this._isReadOnly;
                  }),
                  loading: this._resourceLoading
              });

        this._viewModel.resourceName.subscribeAndRun(container, _name => {
            if (this._isReadOnly) {
                return;
            }
            this._updateResourceId();
            this._updateSubResources(model);
        });

        const subResource = this._isReadOnly
            ? TextBox.create(container, {
                  infoBalloonContent: ClientResources.OutboundAccessRule.SettingsBlade.subResourceBalloonContent,
                  label: ClientResources.OutboundAccessRule.SettingsBlade.subResourceLabel,
                  value: ko.pureComputed(() => {
                      return this._viewModel.subResourceName() || "";
                  }),
                  disabled: this._isReadOnly,
                  visible: ko.pureComputed(() => {
                      return this._viewModel.type() === "PrivateEndpoint";
                  })
              })
            : DropDown.create<string>(container, {
                  infoBalloonContent: ClientResources.OutboundAccessRule.SettingsBlade.subResourceBalloonContent,
                  items: subResourceDropDownItems,
                  label: ClientResources.OutboundAccessRule.SettingsBlade.subResourceLabel,
                  validations: ko.observableArray<MsPortalFx.ViewModels.FormValidation>([
                      new MsPortalFx.ViewModels.RequiredValidation(),
                      new MsPortalFx.ViewModels.CustomValidation("", subResourceName => {
                          if (this._viewModel.resourceType() === ApplicationGatewayResourceType) {
                              if (!subResourceName) {
                                  return Q({
                                      valid: false,
                                      message: ClientResources.OutboundAccessRule.SettingsBlade.AppGateway.SubResource.errorMessage
                                  });
                              }
                          }

                          return Q({ valid: true, message: null });
                      })
                  ]),
                  value: this._viewModel.subResourceName,
                  visible: ko.pureComputed(() => {
                      return this._viewModel.type() === "PrivateEndpoint";
                  }),
                  disabled: this._isReadOnly,
                  loading: this._subResourceLoading
              });

        const sparkEnabledCheckbox = CheckBox.create(container, {
            infoBalloonContent: ClientResources.OutboundAccessRule.SettingsBlade.sparkEnabledBalloonContent,
            label: ClientResources.OutboundAccessRule.SettingsBlade.sparkEnabledLabel,
            value: this._viewModel.sparkEnabled,
            labelOnRight: true,
            disabled: this._isReadOnly,
            visible: ko.pureComputed(() => {
                return this._viewModel.type() === "PrivateEndpoint";
            }),
            cssClass: ko.pureComputed(() => {
                return this._viewModel.type() === "PrivateEndpoint" && this._viewModel.resourceType() === RegistriesResourceType
                    ? "ext-ml-no-padding"
                    : "";
            })
        });

        const amlRegistryDependencyRulesInfoBox = InfoBox.create(container, {
            style: InfoBox.Style.Info,
            text: ClientResources.OutboundAccessRule.SettingsBlade.amlRegistryPEDependencyRulesWarning,
            visible: ko.pureComputed(() => {
                return this._viewModel.type() === "PrivateEndpoint" && this._viewModel.resourceType() === RegistriesResourceType;
            })
        });

        const sparkStatus = TextBox.create(container, {
            infoBalloonContent: ClientResources.OutboundAccessRule.SettingsBlade.sparkStatusBalloonContent,
            label: ClientResources.OutboundAccessRule.SettingsBlade.sparkStatusLabel,
            value: ko.pureComputed(() => {
                return parameterDetails.sparkStatus;
            }),
            disabled: true,
            visible: ko.pureComputed(() => {
                return (
                    this._isReadOnly && this._viewModel.type() === "PrivateEndpoint" && this._viewModel.resourceType() === StorageAccountResourceType
                );
            })
        });

        const appGatewayFqdnTable = EditableGrid.create<FqdnModel>(container, {
            ariaLabel: "Fqdns",
            items: this._viewModel.fqdns,
            addition: {
                canAddRow: () => {
                    return true;
                },
                createItem: () => {
                    return {
                        value: ko.observable<string>()
                    };
                }
            },
            contextMenu: {
                canShowMenu: () => {
                    return false;
                },
                maxButtonCommands: 1
            },
            focus: {
                onExitRow: (_, grid) => {
                    this._showAppGatewayFqdnTableRequiredWarning(true);
                    grid.validation.triggerValidation();
                }
            },
            editing: {
                canEditRow: () => {
                    return true;
                }
            },
            deletion: {
                canDeleteRows: () => {
                    return true;
                }
            },
            selection: {
                selectionMode: EditableGrid.SelectionMode.Single
            },
            columns: [
                {
                    id: "fqdnColumn",
                    header: {
                        htmlTemplate: `<div>
                                        <span>${ClientResources.OutboundAccessRule.SettingsBlade.fqdnsLabel}</span>
                                        <span data-bind='pcControl: infoBalloonContent' class='ext-caption-info-ballon-margin'></span>
                                       </div>`,
                        viewModel: {
                            infoBalloonContent: InfoBalloon.create(container, {
                                content: ClientResources.OutboundAccessRule.SettingsBlade.applicationGatewayFqdnInfoBallon
                            })
                        }
                    },
                    defineCell: (_, item) => {
                        return item.value;
                    },
                    createCellEditor: (editorLifetime, item) => {
                        const textBox = TextBox.create(editorLifetime, {
                            value: item.value,
                            validations: ko.observableArray([new MsPortalFx.ViewModels.RequiredValidation(), this._getFqdnFormatValidation()])
                        });

                        return textBox;
                    }
                }
            ],
            visible: ko.pureComputed(() => {
                return this._viewModel.type() === "PrivateEndpoint" && this._viewModel.resourceType() === ApplicationGatewayResourceType;
            }),
            cssClass: ko.pureComputed(() => (this._appGatewayFqdnsRequiredError() ? undefined : "ext-fqdns-table-bottom-margin"))
        });

        const appGatewayFqdnRequired = InfoBox.createInline(container, {
            style: InfoBox.Style.Error,
            text: ClientResources.OutboundAccessRule.SettingsBlade.FqdnValidation.applicationGatewayFqdnRequired,
            visible: ko.pureComputed(() => {
                return this._showAppGatewayFqdnTableRequiredWarning() && this._appGatewayFqdnsRequiredError();
            })
        });

        this._appGatewayFqdnTableRowsValid = ko.pureComputed(() => {
            return appGatewayFqdnTable.validation.valid();
        });

        let serviceTagDropDownItems = this._getServiceTagDropDownItems();
        const serviceTag = this._isReadOnly
            ? TextBox.create(container, {
                  infoBalloonContent: ClientResources.OutboundAccessRule.SettingsBlade.serviceTagBalloonContent,
                  label: ClientResources.OutboundAccessRule.SettingsBlade.serviceTagLabel,
                  value: ko.pureComputed(() => {
                      return this._viewModel.serviceTag() || "";
                  }),
                  disabled: this._isReadOnly,
                  visible: ko.pureComputed(() => {
                      return this._viewModel.type() === "ServiceTag";
                  })
              })
            : DropDown.create<string>(container, {
                  infoBalloonContent: ClientResources.OutboundAccessRule.SettingsBlade.serviceTagBalloonContent,
                  items: serviceTagDropDownItems,
                  label: ClientResources.OutboundAccessRule.SettingsBlade.serviceTagLabel,
                  validations: [new MsPortalFx.ViewModels.RequiredValidation()],
                  value: this._viewModel.serviceTag,
                  visible: ko.pureComputed(() => {
                      return this._viewModel.type() === "ServiceTag";
                  }),
                  disabled: this._isReadOnly
              });

        const protocol = DropDown.create<string>(container, {
            infoBalloonContent: ClientResources.OutboundAccessRule.SettingsBlade.protocolBalloonContent,
            items: [
                {
                    value: "*",
                    text: "Any"
                },
                {
                    value: "TCP",
                    text: "TCP"
                },
                {
                    value: "UDP",
                    text: "UDP"
                },
                {
                    value: "ICMP",
                    text: "ICMP"
                }
            ],
            label: ClientResources.OutboundAccessRule.SettingsBlade.protocolLabel,
            validations: [new MsPortalFx.ViewModels.RequiredValidation()],
            value: this._viewModel.protocol,
            visible: ko.pureComputed(() => {
                return this._viewModel.type() === "ServiceTag";
            }),
            disabled: this._isReadOnly
        });

        const portRange = TextBox.create(container, {
            infoBalloonContent: ClientResources.OutboundAccessRule.SettingsBlade.portRangeBalloonContent,
            label: ClientResources.OutboundAccessRule.SettingsBlade.portRangeLabel,
            validations: ko.observableArray<MsPortalFx.ViewModels.FormValidation>([new MsPortalFx.ViewModels.RequiredValidation()]),
            value: this._viewModel.portRange,
            placeHolderText: "eg: 443",
            visible: ko.pureComputed(() => {
                return this._viewModel.type() === "ServiceTag";
            }),
            disabled: this._isReadOnly
        });

        const fqdnCostWarning = {
            htmlTemplate: `<div><span data-bind='text: note, visible:true' style='margin-right: 5px; font-weight: bold'></span><span data-bind='text: fqdnCostInfoText, visible:true'></span><span style='white-space: pre-line; display: inline-block; margin-right: 5px' data-bind='visible: true'><a role='link' data-bind='text: manangedNetworkIsolationText, attr: { aria-label: manangedNetworkIsolationText, href: managedNetworkIsolationUri }' target='_blank'></a></span><span data-bind='image: hyperLinkIcon' style='width: 0.8em; height: 0.8em; display: inline-block; margin-right: 5px'></span><span data-bind='text: moreInformationLinkText, visible:true'></span><span style='white-space: pre-line; display: inline-block; margin-right: 5px' data-bind='visible: true'><a role='link' data-bind='text: pricingLinkText, attr: { aria-label: pricingLinkText, href: pricingUri }' target='_blank'></a></span><span data-bind='image: hyperLinkIcon' style='width: 0.8em; height: 0.8em; display: inline-block'></span></div>`,
            viewModel: {
                note: `${ClientResources.OutboundAccessRule.SettingsBlade.note}:`,
                fqdnCostInfoText: ClientResources.OutboundAccessRule.SettingsBlade.FqdnCostInfo.text,
                manangedNetworkIsolationText: ClientResources.OutboundAccessRule.SettingsBlade.FqdnCostInfo.managedNetworkIsolationLinkText,
                managedNetworkIsolationUri: getDocumentUrl(DocLinkIds.ManagedNetworkApprovedOutboundRules),
                moreInformationLinkText: ClientResources.OutboundAccessRule.SettingsBlade.FqdnCostInfo.moreInformationText,
                pricingLinkText: ClientResources.OutboundAccessRule.SettingsBlade.FqdnCostInfo.pricingLinkText,
                pricingUri: "https://azure.microsoft.com/pricing/details/azure-firewall",
                hyperLinkIcon: MsPortalFx.Base.Images.Hyperlink({ palette: MsPortalFx.Base.ImagePalette.Blue })
            }
        };

        const fqdnCostWarningInfoBox = InfoBox.create(container, {
            style: InfoBox.Style.Info,
            text: fqdnCostWarning,
            visible: ko.pureComputed(() => {
                return this._viewModel.type() === "FQDN";
            })
        });

        const fqdnDestination = TextBox.create(container, {
            infoBalloonContent: ClientResources.OutboundAccessRule.SettingsBlade.fqdnDestinationBalloonContent,
            label: ClientResources.OutboundAccessRule.SettingsBlade.fqdnDestinationLabel,
            validations: ko.observableArray<MsPortalFx.ViewModels.FormValidation>([new MsPortalFx.ViewModels.RequiredValidation()]),
            value: this._viewModel.fqdnDestination,
            visible: ko.pureComputed(() => {
                return this._viewModel.type() === "FQDN";
            })
        });

        const status = TextBox.create(container, {
            infoBalloonContent: ClientResources.OutboundAccessRule.SettingsBlade.statusBalloonContent,
            label: ClientResources.OutboundAccessRule.SettingsBlade.statusLabel,
            value: ko.pureComputed(() => {
                return parameterDetails.status;
            }),
            disabled: true,
            visible: this._isReadOnly
        });

        this.formSection = Section.create(container, {
            children: [
                ruleName,
                destinationType,
                subscriptionsDropDown,
                subscriptionTextBox,
                resourceGroupDropDown,
                resourceGroupTextBox,
                resourceType,
                resourceName,
                subResource,
                sparkEnabledCheckbox,
                appGatewayFqdnTable,
                appGatewayFqdnRequired,
                amlRegistryDependencyRulesInfoBox,
                sparkStatus,
                serviceTag,
                protocol,
                portRange,
                fqdnCostWarningInfoBox,
                fqdnDestination,
                status
            ]
        });

        const saveButton = Button.create(container, {
            text: "Save",
            style: Button.Style.Primary,
            disabled: ko.pureComputed(() => {
                if (this._isReadOnly && !this._appGatewayFqdnRule()) {
                    return true;
                }
                if (!ruleName.valid() || !destinationType.valid()) {
                    return true;
                }
                if (this._viewModel.type() === "PrivateEndpoint") {
                    return (
                        !subscriptionsDropDown.valid() ||
                        !resourceGroupDropDown.valid() ||
                        !resourceType.valid() ||
                        !resourceName.valid() ||
                        !subResource.valid() ||
                        !this._appGatewayFqdnTableRowsValid()
                    );
                }
                if (this._viewModel.type() === "ServiceTag") {
                    return !serviceTag.valid() || !protocol.valid() || !portRange.valid();
                }
                if (this._viewModel.type() === "FQDN") {
                    return !fqdnDestination.valid();
                }
                return false;
            }),
            onClick: async () => {
                const allValid = (
                    await Q.allResolved([
                        ruleName.triggerValidation(),
                        destinationType.triggerValidation(),
                        subscriptionsDropDown.triggerValidation(),
                        resourceGroupDropDown.triggerValidation(),
                        resourceType.triggerValidation(),
                        subResource.triggerValidation(),
                        serviceTag.triggerValidation(),
                        protocol.triggerValidation(),
                        portRange.triggerValidation(),
                        fqdnDestination.triggerValidation()
                    ])
                ).every(promise => !!promise.valueOf());
                if (this._appGatewayFqdnRule() && this._viewModel.fqdns().length === 0) {
                    this._showAppGatewayFqdnTableRequiredWarning(true);
                    return;
                }
                if (allValid) {
                    container.closeCurrentBlade({
                        name: this._viewModel.name(),
                        outboundRule: this._getSubmittedOutboundRule(
                            subscriptionsDropDown.value()?.subscriptionId,
                            resourceGroupDropDown.value()?.value.name
                        )
                    });
                }
            }
        });

        const discardButton = Button.create(container, {
            text: "Cancel",
            style: Button.Style.Secondary,
            onClick: () => {
                container.closeCurrentBlade();
            }
        });

        this.buttonsSection = Section.create(container, {
            children: [saveButton, discardButton],
            visible: ko.pureComputed(() => {
                return !this._isReadOnly || this._appGatewayFqdnRule();
            })
        });
    }

    private _updateResourceTypeApiVersionMapping(model: DataContext, subscriptionId: string) {
        if (!subscriptionId) {
            return;
        }
        this._providersLoading(true);
        model.workspaceOutboundAccessData.getProviders(subscriptionId).then(
            providers => {
                const mappings: { [resourceType: string]: string } = {};
                for (const provider of providers) {
                    for (const element of provider.resourceTypes) {
                        mappings[`${provider.namespace}/${element.resourceType}`.toLowerCase()] = element.apiVersions[0];
                    }
                }
                this._resourceTypeApiVersionMapping(mappings);
                this._providersLoading(false);
            },
            (_error: any) => {
                this._providersLoading(false);
            }
        );
    }

    private _updateArmResources(model: DataContext, subscriptionId: string, resourceGroup: string) {
        this._armResourceList([]);
        this._resourceNames([]);
        this._subResourceNameDetails([]);
        if (subscriptionId && resourceGroup) {
            this._resourceLoading(true);
            this._subResourceLoading(true);
            model.workspaceOutboundAccessData.getResources(subscriptionId, resourceGroup).then(
                (data: ArmResource[]) => {
                    const allowedResourceTypesLower = Constants.WorkspaceOutboundRules.AllowedResourceTypes.map(item => item.toLowerCase());
                    const filterData = data.filter(item => allowedResourceTypesLower.includes(item.type.toLowerCase()));
                    if (filterData.length === 0) {
                        this._viewModel.resourceName("");
                        this._viewModel.resourceId("");
                    }
                    this._armResourceList(filterData);
                    this._updateResourceNames();
                    this._updateResourceId();
                    this._updateSubResources(model);
                },
                (_error: any) => {
                    this._resourceLoading(false);
                    this._subResourceLoading(false);
                }
            );
        }
    }

    private _updateResourceNames() {
        const resourceNames: string[] = [];
        this._armResourceList()
            .filter((item: ArmResource) => item.type === this._viewModel.resourceType())
            .forEach((item: ArmResource) => resourceNames.push(item.name));
        this._resourceNames(resourceNames);
    }

    private _updateResourceId() {
        if (!this._viewModel.resourceName()) {
            this._viewModel.resourceId("");
            return;
        }
        const armResource = this._armResourceList().find(
            (item: ArmResource) => item.type === this._viewModel.resourceType() && item.name === this._viewModel.resourceName()
        );
        if (armResource) {
            this._viewModel.resourceId(armResource.id);
        }
    }

    private _updateSubResources(model: DataContext) {
        this._subResourceNameDetails([]);
        if (!this._viewModel.resourceName()) {
            this._viewModel.subResourceName("");
            this._subResourceLoading(false);
            return;
        }
        if (!this._viewModel.resourceId()) {
            this._viewModel.subResourceName("");
            this._subResourceLoading(false);
            return;
        }
        if (this._viewModel.resourceType()) {
            this._subResourceLoading(true);
        }
        const apiVersion = this._resourceTypeApiVersionMapping()[this._viewModel.resourceType().toLowerCase()];
        if (apiVersion && this._viewModel.resourceId()) {
            model.workspaceOutboundAccessData.getSubResourceTypes(this._viewModel.resourceId(), apiVersion).then(
                (values: ArmResource[]) => {
                    const subResourceNameDetails: SubResourceNameDetail[] = values.map((item: ArmResource) => {
                        return {
                            name: item.properties?.groupId ?? item.name,
                            displayName: item.properties?.groupId ?? item.name
                        };
                    });

                    // Some of the resource types have sub resources which are not returned by the API.
                    // Adding them manually if they are not already present.
                    // Handle case insensitive matching for resource type.
                    const resourceTypeInMapping = Constants.WorkspaceOutboundRules.AllowedResourceTypes.find(
                        item => item.toLowerCase() === this._viewModel.resourceType().toLowerCase()
                    );
                    if (resourceTypeInMapping in Constants.WorkspaceOutboundRules.ResourceTypeSubResourcesMapping) {
                        for (const value of Constants.WorkspaceOutboundRules.ResourceTypeSubResourcesMapping[resourceTypeInMapping]) {
                            if (!subResourceNameDetails.find(item => item.name === value)) {
                                subResourceNameDetails.push({
                                    name: value,
                                    displayName: value
                                });
                            }
                        }
                    }

                    if (subResourceNameDetails.length === 0) {
                        this._viewModel.subResourceName("");
                    }
                    this._subResourceNameDetails(subResourceNameDetails);
                    this._subResourceLoading(false);
                },
                _error => {
                    this._viewModel.subResourceName("");
                    this._subResourceLoading(false);
                }
            );
        } else {
            if (!this._providersLoading()) {
                this._viewModel.subResourceName("");
                this._subResourceLoading(false);
            }
        }
    }

    private _createDropdownItems(items: string[]): DropDown.Item<string>[] {
        const list = [];
        for (let i = 0; i < items.length; i++) {
            var value = items[i];
            var item = {
                value: value,
                text: value
            };
            list.push(item);
        }
        return list;
    }

    private _getSubResourceDropdownItems(items: SubResourceNameDetail[]): DropDown.Item<string>[] {
        const list = [];
        for (let i = 0; i < items.length; i++) {
            var item = {
                value: items[i].name,
                text: items[i].displayName
            };
            list.push(item);
        }
        return list;
    }

    private _getServiceTagDropDownItems(): DropDown.Item<string>[] {
        const serviceTags = Constants.WorkspaceOutboundRules.OutboundServiceTags;
        const items = [];
        for (let i = 0; i < serviceTags.length; i++) {
            var value = serviceTags[i];
            var item = {
                value: value,
                text: value
            };
            items.push(item);
        }
        return items;
    }

    private _getDestinationDetails(outboundRule?: OutboundRule): DestinationDetailsParam {
        let subscriptionId = "";
        let resourceGroupName = "";
        let resourceType = "";
        let resourceName = "";
        let subResourceName = "";
        let resourceId = "";
        let sparkEnabled = false;
        let sparkStatus = "";
        let serviceTag = "";
        let protocol = "";
        let portRange = "";
        let fqdnDestination = "";

        if (outboundRule) {
            if (outboundRule.type === "PrivateEndpoint") {
                subscriptionId = this._getSubscriptionIdFromResourceId(outboundRule.destination.serviceResourceId);
                resourceGroupName = this._getResourceGroupNameFromResourceId(outboundRule.destination.serviceResourceId);
                resourceType = this._getResourceTypeFromResourceId(outboundRule.destination.serviceResourceId);
                resourceName = this._getResourceNameFromResourceId(outboundRule.destination.serviceResourceId);
                resourceId = outboundRule.destination.serviceResourceId;
                subResourceName = outboundRule.destination.subresourceTarget;
                sparkEnabled = outboundRule.destination.sparkEnabled;
                sparkStatus = outboundRule.destination.sparkStatus;
            } else if (outboundRule.type === "ServiceTag") {
                serviceTag = outboundRule.destination.serviceTag;
                protocol = outboundRule.destination.protocol;
                portRange = outboundRule.destination.portRanges;
            } else if (outboundRule.type === "FQDN") {
                fqdnDestination = outboundRule.destination;
            }
        }

        return {
            subscriptionId,
            resourceGroupName,
            resourceType,
            subResourceName,
            resourceName,
            resourceId,
            sparkEnabled,
            sparkStatus,
            serviceTag,
            protocol,
            portRange,
            fqdnDestination,
            status: outboundRule?.status || ""
        };
    }

    private _getSubscriptionIdFromResourceId(serviceResourceId: string): string {
        const resourceIdParts = serviceResourceId.split("/");
        return resourceIdParts[2];
    }

    private _getResourceGroupNameFromResourceId(serviceResourceId: string): string {
        const resourceIdParts = serviceResourceId.split("/");
        return resourceIdParts[4];
    }

    private _getResourceTypeFromResourceId(serviceResourceId: string): string {
        const resourceIdParts = serviceResourceId.split("/");
        return `${resourceIdParts[6]}/${resourceIdParts[7]}`;
    }

    private _getResourceNameFromResourceId(serviceResourceId: string): string {
        const resourceIdParts = serviceResourceId.split("/");
        return resourceIdParts[8];
    }

    private _getSubmittedOutboundRule(subscriptionId?: string, resourceGroupName?: string): OutboundRule {
        const type = this._viewModel.type();
        const ruleCategory: RuleCategory = "UserDefined";
        if (type === "PrivateEndpoint") {
            const subResourceName = this._viewModel.subResourceName();
            const serviceResourceId = this._viewModel.resourceId();
            const destination = {
                serviceResourceId: serviceResourceId,
                subresourceTarget: subResourceName,
                sparkEnabled: this._viewModel.sparkEnabled()
            };
            const fqdns = this._viewModel.fqdns().map(fqdn => fqdn.value());
            const outboundRule: OutboundRule = {
                type: "PrivateEndpoint",
                category: ruleCategory,
                destination: destination,
                fqdns: fqdns
            };
            return outboundRule;
        } else if (type === "ServiceTag") {
            const serviceTag = this._viewModel.serviceTag();
            const protocol = this._viewModel.protocol();
            const portRange = this._viewModel.portRange();
            const destination = {
                serviceTag: serviceTag,
                protocol: protocol,
                portRanges: portRange
            };
            const outboundRule: OutboundRule = {
                type: "ServiceTag",
                category: ruleCategory,
                destination: destination
            };
            return outboundRule;
        } else if (type === "FQDN") {
            const fqdnDestination = this._viewModel.fqdnDestination();
            const outboundRule: OutboundRule = {
                type: "FQDN",
                category: ruleCategory,
                destination: fqdnDestination
            };
            return outboundRule;
        }
    }

    private _getFqdnFormatValidation(): CustomV<string> {
        return new CustomV<string>("", value => {
            const result: ValidationResult = {
                valid: true,
                message: null
            };

            const labels = value.split(".");
            if (labels.length < 3) {
                result.valid = false;
                result.message = ClientResources.OutboundAccessRule.SettingsBlade.FqdnValidation.invalidFormatMessage;
            }
            labels.forEach(label => {
                if (label.trim() === "") {
                    result.valid = false;
                    result.message = ClientResources.OutboundAccessRule.SettingsBlade.FqdnValidation.emptyLabelMessage;
                }
            });
            return Q(result);
        });
    }
}
