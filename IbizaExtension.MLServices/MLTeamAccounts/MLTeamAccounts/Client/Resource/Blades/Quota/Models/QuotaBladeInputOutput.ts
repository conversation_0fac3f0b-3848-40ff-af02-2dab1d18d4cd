﻿/**
 * This defines the contract for the Quota request blade's input and output received from the 'New support request' framework
 *
 * Contract location : https://support-docs.azurewebsites.net/docs/articles/onboarding/Quota/overview.html
 * (Remember to replace the type of the 'payload' property from 'any' to BatchAI specific payload)
 */

import { QuotaRequestPayload } from "./QuotaRequestModel";

/*
 * Shared contract for Quota blade input data
 */
export interface QuotaBladeInput {
    /*
    * Subscription ID
    */
    subscriptionId?: string;

    /*
    * Resource ID
    */
    resourceId?: string;

    /*
    * Dynamic object with quota information
    */
    payload?: QuotaRequestPayload;
}

/*
 * Shared contract for Quota blade output data
 */
export interface QuotaBladeOutput {
    /*
     * Status. Possible values: "completed"
     */
    status: string;

    /*
     * Dynamic object with quota information
     */
    payload: QuotaRequestPayload;

    /*
     * Information about quota request Recommended format [e.g. "Quota request for Batch, current value: 100/requested value: 200"]
     */
    quotaRequest: string;

    /*
     * This is the text that will be presented back to the customer, in a table format, in the case submission blade
     */
    formattedQuotaDetails: QuotaDetailsGridEntry[];
}

export interface QuotaDetailsGridEntry {
    /**
     * Quota details including sku, location, deployment stack, description
     */
    requestDetails: string;

    /**
     * Current Quota. Return empty string if the value is zero.
     */
    currentQuota: string;

    /**
     * Requested Quota. Return empty string if the value is zero.
     */
    newQuota: string;
}
