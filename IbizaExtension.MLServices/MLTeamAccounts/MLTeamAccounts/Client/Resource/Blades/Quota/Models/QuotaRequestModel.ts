﻿/**
 * This defines the model for the Quota request payload that is sent to CloudEs or QMS
 *
 * BatchAI payload contract : https://microsoft.sharepoint.com/teams/cloudweb/mcio/cem/stcm/SitePages/BatchAI%20Contract.aspx
 * Batch payload contract   : https://microsoft.sharepoint.com/teams/cloudweb/mcio/cem/stcm/SitePages/Batch%20Contracts.aspxs
 */

export const quotaScopingDetailsSchema = "BatchAIQuotaV1.1";

export interface QuotaRequestPayload {
    quotaScopingDetailsSchema: string;
    quotaScopingDetails: QuotaScopingDetails;
    uxSpecificDetails: UXSpecificDetails;
}

export interface QuotaScopingDetails {
    subscriptionId: string;
    region: string;
    comment?: string;
    newBatchAIQuotaRequest: BatchAIQuotaRequest;
}

export interface BatchAIQuotaRequest {
    requestedDedicatedCoreQuota?: { [key: string]: number };
    currentDedicatedCoreQuota?: { [key: string]: number };
    requestedLowPriorityCoreQuota?: number;
    currentLowPriorityCoreQuota?: number;
    totalClusters?: number;
}

export interface UXSpecificDetails {
    selectedVMFamilies?: string[];
    existingQuotaGridValues?: { [vmFamilyDisplayName: string]: number; };
}
