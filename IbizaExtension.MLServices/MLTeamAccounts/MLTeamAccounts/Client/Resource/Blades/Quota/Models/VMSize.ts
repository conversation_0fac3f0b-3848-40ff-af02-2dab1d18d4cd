﻿export interface VMSize {
    name: string;
    family: string;
    vCPUs: string;
    osVhdSizeMB: number;
    maxResourceVolumeMB: number;
    memoryGB: number;
    lowPriorityCapable: boolean;
    premiumIO: boolean;
}

export interface VMSizesResult {
    amlCompute: VMSize[];
}

export class VMFamilies {
    private families: Map<string, string> = new Map();

    constructor(vmSizes: VMSize[]) {
        for (const vm of vmSizes) {
            const lowerFamilyName = vm.family.toLowerCase();
            if (!this.families.has(lowerFamilyName)) {
                this.families.set(lowerFamilyName, vm.family);
            }
        }
    }

    public contains(family: string): boolean {
        return this.families.has(family.toLowerCase());
    }
}
