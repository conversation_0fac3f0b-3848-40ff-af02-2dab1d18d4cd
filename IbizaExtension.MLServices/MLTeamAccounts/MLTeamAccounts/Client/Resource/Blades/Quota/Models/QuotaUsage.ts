﻿export interface QuotaUsage {
    id?: string;
    type?: string;
    unit: string;
    currentValue: number;
    limit: number;
    name: {
        localizedValue: string;
        value: string;
    };
}

export interface QuotaUsageResult {
    value?: QuotaUsage[];
}

export interface QuotaSettingBase {
    id?: string;
    type?: string;
    limit?: number;
    location?: string;
    unit?: string;
    name?: {
        localizedValue: string;
        value: string;
    };
}

export interface QuotaSetting extends QuotaSettingBase {
    status?: string;
}

export interface QuotaSettingResult {
    value?: QuotaSetting[];
}

export const QUOTA_POST_SUCCESS = "success";
export const QUOTA_POST_FAILURE = "failure";
export const QUOTA_POST_SAVING = "saving";
export const QUOTA_POST_INVALID_QUOTA_BELOW_CLUSTER_MINIMUM = "invalidquotabelowclusterminimum";
export const QUOTA_POST_INVALID_QUOTA_EXCEEDS_SUBSCRIPTION_LIMIT = "invalidquotaexceedssubscriptionlimit";
export const QUOTA_POST_INVALID_VMFAMILY_NAME = "invalidvmfamilyname";
export const QUOTA_POST_UNDEFINED = "undefined";
export const QUOTA_POST_INSUFFICIENT_PERMISSIONS = "insufficientpermissions";
