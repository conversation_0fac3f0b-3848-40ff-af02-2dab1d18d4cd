.ext-ml-quota {
    padding: 10px 25px 25px;
}

.ext-ml-infobox {
    width: 30%;
    padding: 0 20px 20px 0;
}

.ext-ml-quota-btn {
    margin-top: 30px;
}

.ext-ml-quota-container {
    display: flex;
    flex-direction: row;
}

.ext-ml-workspace-link, .ext-ml-compute-link {
    cursor: pointer;
}

.ext-ml-workspace-link:hover, .ext-ml-compute-link:hover {
    text-decoration: underline;
}

.ext-ml-totalcores-name {
    font-weight: 600;
}

.ext-ml-newlimit {
    margin-right: 0px;
    padding: 5px;
    width: 90%;
}

.ext-ml-save-icon {
    width: 17px;
    height: 17px;
    padding: 8px;
    margin-left: 0px;
}

.ext-ml-status-container {
    display: flex;
    flex-direction: row;
    height: 16px;
    width:16px;
}
.ext-ml-success-icon {
    width: 100%;
    height: 15px;
}


.ext-current-limit-updated {
    font-weight:bolder;
}