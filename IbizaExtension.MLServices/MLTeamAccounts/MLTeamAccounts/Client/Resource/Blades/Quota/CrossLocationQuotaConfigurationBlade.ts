import * as TemplateBlade from "Fx/Composition/TemplateBlade";
import * as CustomHtml from "Fx/Controls/CustomHtml";
import * as InfoBox from "Fx/Controls/InfoBox";
import * as Toggle from "Fx/Controls/Toggle";
import { ArmId } from "Fx/ResourceManagement";

import Utilities from "Shared/Utilities";
import { Workspace } from "../../../MLServicesDataModels/Workspace.types";
import { SubscriptionWorkspacesResult } from "../../../Resource/Data/MLComputeQuotaData";
import { DataContext } from "../../ResourceArea";
import * as QuotaConfigurationTree from "./CrossLocation/Controls/QuotaConfigurationTree";
import { QuotaSetting, QuotaSettingResult } from "./Models/QuotaUsage";
import { VMSize, VMSizesResult } from "./Models/VMSize";

import * as ClientResources from "Resx/ClientResources";

export interface Parameters {
    location: string;
    subscriptionId: string;
}

export interface ReturnData {}

@TemplateBlade.Decorator({
    htmlTemplate: `<div class="ext-ml-quota-configuration-blade msportalfx-padding">
        <div data-bind="pcControl: errorMessage"></div>
        <div data-bind="pcControl: allWorkspacesWarning"></div>
        <!-- ko if: hasNoQuotaError -->
            <div style="display: flex; justify-content: flex-end">
                <div data-bind="pcControl: crossLocationQuotaToggle"></div>
            </div>
            <div class="ext-ml-quota-configuration-tree-container">
                <div data-bind="pcControl: quotaTree" style="flex-grow:1"></div>
                <div data-bind="pcControl: quotaTreeCrossLocation" style="flex-grow:1"></div>
            </div>
        <!-- /ko -->
    </div>`,
    styleSheets: ["../../../Shared/Styles/CustomStyles.css", "./CrossLocationQuotaConfigurationBlade.css"]
})
@TemplateBlade.ForContextPane.Decorator({
    width: TemplateBlade.ForContextPane.Width.XLarge
})
@TemplateBlade.ReturnsData.Decorator()
@TemplateBlade.InjectableModel.Decorator(DataContext)
export class CrossLocationQuotaConfigurationBlade {
    public errorMessage: InfoBox.Contract;
    public allWorkspacesWarning: InfoBox.Contract;
    public crossLocationQuotaToggle: Toggle.Contract;
    public quotaTree: CustomHtml.Contract;
    public quotaTreeCrossLocation: CustomHtml.Contract;

    public title = ClientResources.CrossRegionComputeQuotas.QuotaConfigurationBlade.title;
    public subtitle = ClientResources.CrossRegionComputeQuotas.QuotaConfigurationBlade.subTitle;
    public context: TemplateBlade.Context<Parameters, DataContext> & TemplateBlade.ReturnsData.Context<ReturnData>;
    public hasNoQuotaError = ko.observable(false);

    public async onInitialize(): Promise<void> {
        const { model, parameters } = this.context;
        const { location, subscriptionId } = parameters;

        this._initializeErrorMessages();

        return Q.allSettled([
            model.mlComputeQuotaData.getSubscriptionWorkspaces(subscriptionId),
            model.mlComputeQuotaData.getQuotas(subscriptionId, location),
            model.mlComputeQuotaData.getSupportedVMSizes(subscriptionId, location)
        ]).then(([allWorkspacesResult, quotasResult, vmSizesResult]) => {
            const allWorkspacesAvailable = allWorkspacesResult.state === "fulfilled";
            const quotasAvailable = quotasResult.state === "fulfilled";
            const vmSizesAvailable = vmSizesResult.state === "fulfilled";
            if (quotasAvailable && vmSizesAvailable) {
                const allWorkspaces = allWorkspacesResult.value ? (allWorkspacesResult.value as SubscriptionWorkspacesResult).value || [] : [];
                const quotas = quotasResult.value ? (quotasResult.value as QuotaSettingResult).value || [] : [];
                const vmSizes = vmSizesResult.value ? (vmSizesResult.value as VMSizesResult).amlCompute || [] : [];
                this._initializeCrossLocationQuotaToggle(allWorkspacesAvailable);
                this._initializeQuotaSettingGrids(quotas, vmSizes, allWorkspaces, allWorkspacesAvailable);

                if (allWorkspacesResult.reason) {
                    this.allWorkspacesWarning.text(
                        ClientResources.CrossRegionComputeQuotas.QuotaConfigurationBlade.workspacesNotAvailable.format(
                            Utilities.extractErrorMessage(allWorkspacesResult.reason)
                        )
                    );
                    this.allWorkspacesWarning.visible(true);
                }

                this.hasNoQuotaError(true);
            } else {
                const errors: string[] = [];
                if (!allWorkspacesAvailable && allWorkspacesResult.reason) {
                    errors.push(Utilities.extractErrorMessage(allWorkspacesResult.reason));
                }

                if (!quotasAvailable && quotasResult.reason) {
                    errors.push(Utilities.extractErrorMessage(quotasResult.reason));
                }

                if (!vmSizesResult && vmSizesResult.reason) {
                    errors.push(Utilities.extractErrorMessage(vmSizesResult.reason));
                }

                if (errors.length > 0) {
                    this.errorMessage.text(
                        ClientResources.CrossRegionComputeQuotas.QuotaConfigurationBlade.quotaConfigNotAvailable.format(errors.join(". "))
                    );
                    this.errorMessage.visible(true);
                }
            }
        });
    }

    private _initializeErrorMessages(): void {
        const { container } = this.context;

        this.errorMessage = InfoBox.create(container, {
            style: InfoBox.Style.Error,
            text: ko.observable(""),
            visible: ko.observable(false)
        });

        this.allWorkspacesWarning = InfoBox.create(container, {
            canClose: true,
            style: InfoBox.Style.Warning,
            text: ko.observable(""),
            visible: ko.observable(false),
            onCloseClickCallback: () => {
                this.allWorkspacesWarning.text("");
                this.allWorkspacesWarning.visible(false);
            }
        });
    }

    private _initializeQuotaSettingGrids(
        quotaSettings: QuotaSetting[],
        vmSizes: VMSize[],
        allWorkspaces: Workspace[],
        allWorkspacesAvailable: boolean
    ): void {
        const { container, parameters } = this.context;
        const { location } = parameters;

        this.quotaTree = QuotaConfigurationTree.create(container, {
            location,
            quotaSettings,
            vmSizes,
            workspaces: allWorkspaces
                .filter(w => MsPortalFx.localeCompareIgnoreCase(w.location, location) === 0)
                .map(w => ({
                    armId: ArmId.parse(w.id),
                    name: w.name,
                    location: w.location,
                    isCrossLocation: false
                })),
            visible: ko.computed(container, () => !allWorkspacesAvailable || !this.crossLocationQuotaToggle.value()),
            onError: errorMessage => {
                this.errorMessage.text(errorMessage || "");
                this.errorMessage.visible(!!errorMessage);
            },
            onUpdateQuotaLimit: (quotaId, quotaType, newLimit, workspaceLocation) => {
                return this._onUpdateQuotaLimit(quotaId, quotaType, newLimit, workspaceLocation);
            }
        });

        this.quotaTreeCrossLocation = allWorkspacesAvailable
            ? QuotaConfigurationTree.create(container, {
                  location,
                  quotaSettings,
                  vmSizes,
                  workspaces: allWorkspaces.map(w => ({
                      armId: ArmId.parse(w.id),
                      name: w.name,
                      location: w.location,
                      isCrossLocation: MsPortalFx.localeCompareIgnoreCase(w.location, location) !== 0
                  })),
                  visible: ko.computed(container, () => this.crossLocationQuotaToggle.value()),
                  onError: errorMessage => {
                      this.errorMessage.text(errorMessage || "");
                      this.errorMessage.visible(!!errorMessage);
                  },
                  onUpdateQuotaLimit: (quotaId, quotaType, newLimit, workspaceLocation) => {
                      return this._onUpdateQuotaLimit(quotaId, quotaType, newLimit, workspaceLocation);
                  }
              })
            : CustomHtml.create(container, { htmlTemplate: "<div></div>" });
    }

    private _initializeCrossLocationQuotaToggle(allWorkspacesAvailable: boolean): void {
        const { container } = this.context;

        this.crossLocationQuotaToggle = Toggle.create(container, {
            cssClass: "ext-ml-cross-location-toggle",
            ariaLabel: ClientResources.CrossRegionComputeQuotas.toggleText,
            disabled: !allWorkspacesAvailable,
            suppressDirtyBehavior: true,
            valueText: {
                offText: ClientResources.CrossRegionComputeQuotas.toggleText,
                onText: ClientResources.CrossRegionComputeQuotas.toggleText
            }
        });
    }

    private _onUpdateQuotaLimit(quotaId: string, quotaType: string, newLimit: number, workspaceLocation: string): Promise<any> {
        const { form, model, parameters } = this.context;
        const { location, subscriptionId } = parameters;

        const postData: QuotaSetting = {
            id: quotaId,
            type: quotaType,
            limit: newLimit,
            unit: "Count",
            location: MsPortalFx.localeCompareIgnoreCase(location, workspaceLocation) !== 0 ? workspaceLocation : undefined
        };

        const postDataArray: QuotaSetting[] = [postData];
        const postDataBulk = JSON.stringify({ value: postDataArray });

        return model.mlComputeQuotaData
            .updateQuotas(subscriptionId, location, postDataBulk)
            .then(response => response)
            .finally(() => {
                form.configureAlertOnClose(TemplateBlade.AlertLevel.None);
            });
    }
}
