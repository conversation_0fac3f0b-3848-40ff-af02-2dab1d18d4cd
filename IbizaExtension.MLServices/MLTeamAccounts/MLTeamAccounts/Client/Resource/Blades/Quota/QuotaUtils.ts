import { QuotaUsage, QuotaSetting } from "./Models/QuotaUsage";

export enum QuotaUsageType {
  VMFamily = "VMFamily",
  Workspace = "Workspace",
  Compute = "Compute",
  TotalCores = "TotalCores",
  TotalDedicatedCores = "TotalDedicatedCores",
  TotalLowPriorityCores = "TotalLowPriorityCores",
  CrossLocationComputeInfo = "CrossLocationComputeInfo"
}

// Contains values returned by the backend /usages and /quotas APIs for various fields in 'QuotaUsage' interface
export const BackendQuotaUsageValues = {
  name: {
    value: {
      totalCores: "TotalCores",
      totalDedicatedCores: "TotalDedicatedCores",
      totalLowPriorityCores: "TotalLowPriorityCores",
      vmFamilyRegExp: /^standard(.+)Family$/i
    }
  },
  type: {
    vmFamilyDedicatedUsage: "Microsoft.MachineLearningServices/vmFamily/dedicatedCores/usages",
    vmFamilyLowPriorityUsage: "Microsoft.MachineLearningServices/vmFamily/lowPriorityCores/usages",
    workspaceDedicatedUsage: "Microsoft.MachineLearningServices/workspaces/dedicatedCores/usages",
    workspaceLowPriorityUsage: "Microsoft.MachineLearningServices/workspaces/lowPriorityCores/usages",
    computeDedicatedUsage: "Microsoft.MachineLearningServices/workspaces/computes/dedicatedCores/usages",
    computeLowPriorityUsage: "Microsoft.MachineLearningServices/workspaces/computes/lowPriorityCores/usages",
    totalCoresUsage: "Microsoft.MachineLearningServices/totalCores/usages",
    totalDedicatedCoresUsage: "Microsoft.MachineLearningServices/totalDedicatedCores/usages",
    totalLowPriorityCoresUsage: "Microsoft.MachineLearningServices/totalLowPriorityCores/usages",
    vmFamilyDedicatedQuotas: "Microsoft.MachineLearningServices/vmFamily/dedicatedCores/quotas",
    workspaceDedicatedQuotas: "Microsoft.MachineLearningServices/workspaces/dedicatedCores/quotas"
  },
  typeRegExp: {
    lowPriorityCores: /lowPriorityCores/i,
    dedicatedCores: /dedicatedCores/i
  },
  idRegExp: {
    workspaceName: /\/workspaces\/[^\/]+/g,
    computeName: /\/computes\/[^\/]+/g,
    resourceGroupName: /\/resourceGroups\/[^\/]+/g,
    subscriptionId: /\/subscriptions\/[^\/]+/g,
    vmFamilyUsages: /\/usages\/[^\/]+/g,
    vmFamilyQuotas: /\/quotas\/[^\/]+/g
  }
};

export class QuotaUtils {
  public static getQuotaUsageType(quotaUsageOrSetting: QuotaUsage | QuotaSetting): QuotaUsageType | undefined {
    let resourceType: QuotaUsageType | undefined;
    switch (quotaUsageOrSetting.type) {
      case BackendQuotaUsageValues.type.vmFamilyDedicatedUsage:
      case BackendQuotaUsageValues.type.vmFamilyLowPriorityUsage:
      case BackendQuotaUsageValues.type.vmFamilyDedicatedQuotas:
        resourceType = QuotaUsageType.VMFamily;
        break;
      case BackendQuotaUsageValues.type.workspaceDedicatedUsage:
      case BackendQuotaUsageValues.type.workspaceLowPriorityUsage:
      case BackendQuotaUsageValues.type.workspaceDedicatedQuotas:
        resourceType = QuotaUsageType.Workspace;
        break;
      case BackendQuotaUsageValues.type.computeDedicatedUsage:
      case BackendQuotaUsageValues.type.computeLowPriorityUsage:
        resourceType = QuotaUsageType.Compute;
        break;
      case BackendQuotaUsageValues.type.totalCoresUsage:
        resourceType = QuotaUsageType.TotalCores;
        break;
      case BackendQuotaUsageValues.type.totalDedicatedCoresUsage:
        resourceType = QuotaUsageType.TotalDedicatedCores;
        break;
      case BackendQuotaUsageValues.type.totalLowPriorityCoresUsage:
        resourceType = QuotaUsageType.TotalLowPriorityCores;
        break;
      default:
        resourceType = undefined;
    }
    return resourceType;
  }

  public static isQuotaUsageDedicated(quotaUsageOrSetting: QuotaUsage | QuotaSetting): boolean {
    if (BackendQuotaUsageValues.typeRegExp.dedicatedCores.test(quotaUsageOrSetting.type)) {
      return true;
    } else if (BackendQuotaUsageValues.typeRegExp.lowPriorityCores.test(quotaUsageOrSetting.type)) {
      return false;
    } else {
      return false;
    }
  }

  // Returns "Standard DSv2 Family vCPUs" given "standardDSv2Family"
  public static getVMFamilyDisplayNameUsagesBlade(coreVMFamilyName: string): string {
    var result = coreVMFamilyName.match(BackendQuotaUsageValues.name.value.vmFamilyRegExp);
    if (result && result.length >= 2 && result[1]) {
      return `Standard ${result[1]} Family vCPUs`;
    }
    return coreVMFamilyName;
  }

  // Returns "DSv2 Series" given "standardDSv2Family"
  public static getVMFamilyDisplayNameRequestBlade(coreVMFamilyName: string): string {
    if (coreVMFamilyName === BackendQuotaUsageValues.name.value.totalLowPriorityCores) {
      return "Low Priority vCPUs";
    }

    var result = coreVMFamilyName.match(BackendQuotaUsageValues.name.value.vmFamilyRegExp);
    if (result && result.length >= 2 && result[1]) {
      return `${result[1]} Series`;
    }
    return coreVMFamilyName;
  }
}