﻿"use strict";

import * as TemplateBlade from "Fx/Composition/TemplateBlade";
import * as Button from "Fx/Controls/Button";
import * as CustomHtml from "Fx/Controls/CustomHtml";
import * as DropDown from "Fx/Controls/DropDown";
import * as InfoBox from "Fx/Controls/InfoBox";
import * as NumericTextBox from "Fx/Controls/NumericTextBox";
import * as Section from "Fx/Controls/Section";
import * as ClientResources from "Resx/ClientResources";

import Constants = require("../../../Shared/Constants");
import { DocLinkIds, getDocumentUrl } from "../../../Shared/DocLinks";
import { DataContext } from "../../ResourceArea";
import { getFamilyDisplayName } from "./CrossLocation/ComputeQuotaUtils";
import { QuotaBladeInput, QuotaBladeOutput, QuotaDetailsGridEntry } from "./Models/QuotaBladeInputOutput";
import { QuotaRequestPayload, quotaScopingDetailsSchema } from "./Models/QuotaRequestModel";
import { QuotaUsage } from "./Models/QuotaUsage";
import { VMSize } from "./Models/VMSize";
import { BackendQuotaUsageValues, QuotaUsageType, QuotaUtils } from "./QuotaUtils";

import Grid = MsPortalFx.ViewModels.Controls.Lists.Grid;
import RequiredValidation = MsPortalFx.ViewModels.RequiredValidation;

type VmType = "dedicated" | "low-priority";

interface IVmQuotaUsages {
    dedicated: { [coreVMFamilyName: string]: QuotaUsage[] };
    lowPriority?: QuotaUsage;
}

const globalRateLimit = 500;

export interface BaseQuotaGridRowModel {
    displayName: KnockoutObservableBase<string>;
    currentLimit: KnockoutObservableBase<string>;
    newLimit: NumericTextBox.Contract;
    vmFamily: string;
}

export interface QuotaGridRowModel extends BaseQuotaGridRowModel {
    deleteBtn: KnockoutObservableBase<any>;
}

@TemplateBlade.Decorator({
    htmlTemplate: `
        <div class="msportalfx-docking">
            <div class="msportalfx-docking-body msportalfx-padding">
                <div class="msportalfx-form msportalfx-form-regular">
                    <div data-bind="pcControl: locationsDropDown" class="ext-ml-locations-dropdown"></div>
                    <!-- ko if: locationsDropDown.value() -->
                        <div data-bind="pcControl: typesDropDown" class="ext-ml-types-dropdown"></div>
                        <!-- ko if: dedicatedSelected -->
                            <div data-bind="pcControl: dedicatedSection"></div>
                        <!-- /ko -->
                        <!-- ko if: lowPrioritySelected -->
                            <div data-bind="pcControl: lowPrioritySection"></div>
                        <!-- /ko -->
                    <!-- /ko -->
                    <!-- ko if: errorInfoBox -->
                        <p data-bind="pcControl: errorInfoBox"></p>
                    <!-- /ko -->
                    <p data-bind="pcControl: documentationInfoBox"></p>
                </div>
            </div>
            <div class="msportalfx-docking-footer msportalfx-padding">
                <div data-bind="pcControl: submitBtn" style="width: 175px"></div>
            </div>
        </div>
    `,
    styleSheets: ["./MachineLearningServicesQuotaRequestBlade.css"]
})
@TemplateBlade.LegacyFeatures.Decorator({ width: TemplateBlade.LegacyFeatures.Width.Medium })
@TemplateBlade.ForContextPane.Decorator({ width: TemplateBlade.LegacyFeatures.Width.Medium })
@TemplateBlade.ReturnsData.Decorator()
@TemplateBlade.InjectableModel.Decorator(DataContext)
export class MachineLearningServicesQuotaRequestBladeNoPdl {
    private _subscriptionId = ko.observable<string>();
    private _resourceId = ko.observable<string>();
    private _location = ko.pureComputed(() => this.locationsDropDown.value());
    private _quotaRequestMessage = ko.observable("");
    private _formattedQuotaDetails = ko.observableArray<QuotaDetailsGridEntry>();
    private _quotaUsages: IVmQuotaUsages;
    private _initialLoad = true;
    private _totalClustersCount: number;
    private _supportedVMFamilySizesMap: { [publicSKUNameLowerCased: string]: string[] } = {};

    private _vmFamilyMultiDropDown: DropDown.ViewModel<string>;
    private _dedicatedQuotaGrid: Grid.ViewModel<QuotaGridRowModel, QuotaGridRowModel>;
    private _lowPriorityQuotaGrid: Grid.ViewModel<BaseQuotaGridRowModel, BaseQuotaGridRowModel>;

    public title = ClientResources.quotaRequestTitle;
    public subtitle = "";

    public context: TemplateBlade.Context<QuotaBladeInput, DataContext> & TemplateBlade.ReturnsData.Context<QuotaBladeOutput>;

    public locationsDropDown: DropDown.ViewModel<string>;
    public typesDropDown: DropDown.Contract<VmType>;
    public lowPrioritySection: Section.Contract;
    public dedicatedSection: Section.Contract;
    public submitBtn: Button.Contract;
    public documentationInfoBox: InfoBox.Contract = null;
    public errorInfoBox: InfoBox.Contract = null;

    public dedicatedSelected = ko.pureComputed(() => this.typesDropDown.value().indexOf("dedicated") >= 0);
    public lowPrioritySelected = ko.pureComputed(() => this.typesDropDown.value().indexOf("low-priority") >= 0);

    public onInitialize() {
        const { container, model, parameters } = this.context;

        if (!parameters.subscriptionId && !parameters.payload) {
            container.notFound();
            return;
        }

        this._subscriptionId(parameters.subscriptionId);
        this._resourceId(parameters.resourceId);

        this._initLocationsDropdown();
        this._initTypesDropDown();
        this._initDedicatedSection();
        this._initLowPrioritySection();
        this._initDocumentationInfoBox();
        this._initButton();

        return this._checkProviderRegistration(this._subscriptionId()).then(() => {
            this._fetchLocations();
        });
    }

    private _checkProviderRegistration(subscriptionId: string): Q.Promise<void> {
        const { model, container } = this.context;

        return model.providerRegistrationData
            .getProviderRegStatus(subscriptionId, Constants.machineLearningServicesResourcesProvider)
            .then((registrationState: string) => {
                if (registrationState === Constants.registeredString) {
                    // no-op
                    this.errorInfoBox = null;
                } else if (registrationState === Constants.registeringString) {
                    this.errorInfoBox = InfoBox.create(container, {
                        style: InfoBox.Style.Info,
                        text: ClientResources.quotaProviderRegisteringErrorMsg.format(
                            subscriptionId,
                            Constants.machineLearningServicesResourcesProvider
                        )
                    });
                } else {
                    model.providerRegistrationData
                        .registerProvider(subscriptionId, Constants.machineLearningServicesResourcesProvider)
                        .then(() => {
                            // no-op
                            this.errorInfoBox = null;
                        })
                        .catch(error => {
                            this.errorInfoBox = InfoBox.create(container, {
                                style: InfoBox.Style.Error,
                                text: ClientResources.quotaProviderNotRegisteredErrorMsg.format(
                                    subscriptionId,
                                    Constants.machineLearningServicesResourcesProvider
                                )
                            });
                        });
                }
            })
            .catch(error => {
                this.errorInfoBox = InfoBox.create(container, {
                    style: InfoBox.Style.Error,
                    text: ClientResources.quotaProviderNotRegisteredErrorMsg.format(
                        subscriptionId,
                        Constants.machineLearningServicesResourcesProvider
                    )
                });
            });
    }

    private _initLocationsDropdown(): void {
        const { container, parameters } = this.context;

        this.locationsDropDown = new DropDown.ViewModel<string>(container, {
            label: ko.observable(ClientResources.resourceLocationColumn),
            ariaLabel: ko.observable(ClientResources.resourceLocationColumn),
            items: ko.observableArray([]),
            validations: ko.observableArray([new RequiredValidation(ClientResources.selectLocation)])
        });

        this.locationsDropDown.value.extend({ rateLimit: globalRateLimit }).subscribe(container, () => {
            this._vmFamilyMultiDropDown.items([]);
            this._vmFamilyMultiDropDown.value([]);
            this._supportedVMFamilySizesMap = {};

            const selectedVMFamilies = parameters.payload ? parameters.payload.uxSpecificDetails.selectedVMFamilies : null;
            const existingQuotaGridValues = parameters.payload ? parameters.payload.uxSpecificDetails.existingQuotaGridValues : null;

            this._getQuotaUsage().then(() => {
                this._mapIncomingQuota(selectedVMFamilies, existingQuotaGridValues);
            });
        });
    }

    private _initTypesDropDown(): void {
        const { container } = this.context;

        this.typesDropDown = DropDown.create<VmType>(container, {
            label: ClientResources.RequestQuota.vmTypesLabel,
            ariaLabel: "",
            items: [
                { text: ClientResources.RequestQuota.vmTypeDedicated, value: "dedicated" },
                { text: ClientResources.RequestQuota.vmTypeLowPriority, value: "low-priority" }
            ],
            multiselect: true,
            validations: [new RequiredValidation()],
            value: ko.observable(["dedicated"])
        });
    }

    private _initDedicatedSection(): void {
        const { container } = this.context;

        this._initVMFamilyMultiDropDown();
        this._initDedicatedQuotaGrid();

        this.dedicatedSection = Section.create(container, {
            smartAlignLabel: true,
            name: ClientResources.RequestQuota.dedicatedSectionLabel,
            children: [
                this._vmFamilyMultiDropDown,
                CustomHtml.create(container, {
                    htmlTemplate: `
                    <!-- ko if: gridVisible -->
                        <div data-bind="pcControl: quotaGrid"></div>
                    <!-- /ko -->`,
                    innerViewModel: {
                        quotaGrid: this._dedicatedQuotaGrid,
                        gridVisible: ko.computed(container, () => {
                            const selectedVMFamilies = this._vmFamilyMultiDropDown.value();
                            return Array.isArray(selectedVMFamilies) && selectedVMFamilies.length > 0;
                        })
                    }
                })
            ]
        });
    }

    private _initLowPrioritySection(): void {
        const { container } = this.context;

        this._initLowPriorityQuotaGrid();

        this.lowPrioritySection = Section.create(container, {
            cssClass: "ext-ml-lowpri-section",
            smartAlignLabel: true,
            name: ClientResources.RequestQuota.lowPrioritySectionLabel,
            children: [this._lowPriorityQuotaGrid]
        });
    }

    private _initLowPriorityQuotaGrid(): void {
        const { container } = this.context;

        this._lowPriorityQuotaGrid = new Grid.ViewModel<QuotaGridRowModel, QuotaGridRowModel>(container, null);
        const lowPriorityQuotaColumns: Grid.Column[] = [
            {
                itemKey: "displayName",
                name: ko.observable<string>(ClientResources.quotaRequestResourceName)
            },
            {
                itemKey: "currentLimit",
                name: ko.observable<string>(ClientResources.quotaRequestCurrentLimit),
                format: Grid.Format.HtmlBindings,
                formatOptions: {
                    htmlBindingsTemplate: "<div data-bind='text: settings.item.currentLimit'></div>"
                },
                width: ko.observable("25%")
            },
            {
                itemKey: "newLimit",
                name: ko.observable<string>(ClientResources.quotaRequestNewLimit),
                format: Grid.Format.HtmlBindings,
                formatOptions: {
                    htmlBindingsTemplate: `<div class="ext-ml-newlimit" data-bind='pcControl: settings.item.newLimit'></div></div>`
                },
                width: ko.observable("25%")
            }
        ];
        this._lowPriorityQuotaGrid.columns = ko.observableArray<Grid.Column>(lowPriorityQuotaColumns);
        this._lowPriorityQuotaGrid.showHeader = true;
        this._lowPriorityQuotaGrid.items([]);
    }

    private _fetchLocations(): Q.Promise<any> {
        const { model, parameters } = this.context;

        return Q.all([
            model.machineLearningServicesData.getSubscriptionLocations(this._subscriptionId()),
            model.machineLearningServicesData.getComputeAvailableLocations(this._subscriptionId()),
            model.machineLearningServicesData.getWorkspace(this._resourceId())
        ])
            .then((responses: any[]) => {
                const locationObjs = (responses[0] && responses[0].value) || [];
                const availableLocations = responses[1] || [];
                const workspace = responses[2];

                let availableLocationObjs = locationObjs.filter((locationObj: any) => availableLocations.indexOf(locationObj.displayName) !== -1);
                if (!availableLocationObjs || availableLocationObjs.length === 0) {
                    availableLocationObjs = locationObjs;
                }

                const locationDropDownItems: DropDown.Item<string>[] = availableLocationObjs.map((locationObj: any) => {
                    return {
                        text: ko.observable(locationObj.regionalDisplayName),
                        value: locationObj.name
                    };
                });

                let location: string;
                if (parameters.payload && parameters.payload.quotaScopingDetails.region) {
                    location = parameters.payload.quotaScopingDetails.region;
                } else if (workspace) {
                    location = workspace.location;
                }

                if (location) {
                    if (MsPortalFx.find(locationDropDownItems, (item: DropDown.Item<string>) => item.value === location)) {
                        this.locationsDropDown.value(location);
                    } else {
                        locationDropDownItems.push({
                            text: ko.observable(location),
                            value: location
                        });
                        this.locationsDropDown.value(location);
                    }
                } else {
                    this.locationsDropDown.value(null);
                }

                locationDropDownItems.sort((a: DropDown.Item<string>, b: DropDown.Item<string>) => {
                    const aText = a.text as KnockoutObservableBase<string>;
                    const bText = b.text as KnockoutObservableBase<string>;
                    return aText().localeCompare(bText());
                });
                this.locationsDropDown.items(locationDropDownItems);
            })
            .catch(err => {
                this.locationsDropDown.items([]);
                this.locationsDropDown.value(null);
            });
    }

    private _initVMFamilyMultiDropDown() {
        const { container } = this.context;
        this._vmFamilyMultiDropDown = new DropDown.ViewModel<string>(container, {
            label: ko.observable(ClientResources.quotaRequestVMFamily),
            ariaLabel: ko.observable(ClientResources.quotaRequestVMFamily),
            items: ko.observableArray<DropDown.Item<string>>([]),
            validations: ko.observableArray([new RequiredValidation()]),
            multiselect: true,
            selectAll: true
        });

        this._vmFamilyMultiDropDown.value.subscribe(container, (vmFamilies: string[]) => {
            let listToUse: QuotaGridRowModel[] = [];
            vmFamilies.forEach(vmFamily => {
                const existingGridEntries: QuotaGridRowModel[] = [];
                this._dedicatedQuotaGrid.items().forEach((gridEntry: QuotaGridRowModel) => {
                    if (gridEntry.vmFamily === vmFamily) {
                        existingGridEntries.push(gridEntry);
                    }
                });

                if (existingGridEntries.length > 0) {
                    listToUse = listToUse.concat(existingGridEntries);
                } else {
                    const quotaUsages: QuotaUsage[] = this._quotaUsages.dedicated[vmFamily];
                    if (quotaUsages) {
                        quotaUsages.forEach((quotaUsage: QuotaUsage) => {
                            const coreVMFamilyName = quotaUsage.name.value;
                            const isDedicated = QuotaUtils.isQuotaUsageDedicated(quotaUsage);
                            const quotaGridRowDisplayName = QuotaUtils.getVMFamilyDisplayNameRequestBlade(coreVMFamilyName);

                            const row: QuotaGridRowModel = {
                                displayName: ko.observable(quotaGridRowDisplayName),
                                currentLimit: ko.observable(quotaUsage.limit.toString()),
                                newLimit: NumericTextBox.create(container, {
                                    min: ko.observable(0),
                                    validations: ko.observableArray([new RequiredValidation()]),
                                    ariaLabel: coreVMFamilyName + " " + ClientResources.quotaRequestNewLimit,
                                    cssClass: `ext-ml-newlimit-${coreVMFamilyName}`
                                }),
                                deleteBtn: ko.observable(() => this._onDeleteButtonClick(vmFamily)),
                                vmFamily: coreVMFamilyName
                            };
                            listToUse.push(row);
                        });
                    }
                }
            });
            this._dedicatedQuotaGrid.items(listToUse);
        });
    }

    private _onDeleteButtonClick(clickedRowVMFamily: string): void {
        const currentRows = this._dedicatedQuotaGrid.items();
        const newRows: QuotaGridRowModel[] = [];
        currentRows.forEach((currentRow: QuotaGridRowModel) => {
            if (currentRow.vmFamily !== clickedRowVMFamily) {
                newRows.push(currentRow);
            }
        });

        this._dedicatedQuotaGrid.items(newRows);

        let removeVMFamily = true;
        newRows.forEach(newRow => {
            if (newRow.vmFamily === clickedRowVMFamily) {
                removeVMFamily = false;
            }
        });

        if (removeVMFamily) {
            const currentVMFamilies = [].concat(this._vmFamilyMultiDropDown.value());
            MsPortalFx.remove(currentVMFamilies, clickedRowVMFamily);
            this._vmFamilyMultiDropDown.value(currentVMFamilies);
        }
    }

    private _initDedicatedQuotaGrid() {
        const { container } = this.context;

        this._dedicatedQuotaGrid = new Grid.ViewModel<QuotaGridRowModel, QuotaGridRowModel>(container, null);
        const accountColumns: Grid.Column[] = [
            {
                itemKey: "displayName",
                name: ko.observable<string>(ClientResources.quotaRequestResourceName)
            },
            {
                itemKey: "currentLimit",
                name: ko.observable<string>(ClientResources.quotaRequestCurrentLimit),
                format: Grid.Format.HtmlBindings,
                formatOptions: {
                    htmlBindingsTemplate: "<div data-bind='text: settings.item.currentLimit'></div>"
                },
                width: ko.observable("25%")
            },
            {
                itemKey: "newLimit",
                name: ko.observable<string>(ClientResources.quotaRequestNewLimit),
                format: Grid.Format.HtmlBindings,
                formatOptions: {
                    htmlBindingsTemplate: `
                        <div>
                            <div class="ext-ml-newlimit" data-bind='pcControl: settings.item.newLimit'></div>
                            <a class="ext-ml-delete-btn"
                               data-bind="image: MsPortalFx.Base.Images.Discard(),
                               fxclick: settings.item.deleteBtn()"></a>
                        </div>`
                },
                width: ko.observable("25%")
            }
        ];
        this._dedicatedQuotaGrid.columns = ko.observableArray<Grid.Column>(accountColumns);
        this._dedicatedQuotaGrid.showHeader = true;
        this._dedicatedQuotaGrid.items([]);
    }

    private _getQuotaUsage(): Q.Promise<any> {
        const { container, model } = this.context;
        if (!this._location()) {
            return Q();
        }

        this._vmFamilyMultiDropDown.loading(true);
        this._lowPriorityQuotaGrid.loading(true);

        return Q.all([
            model.machineLearningServicesData.getSupportedVMSizes(this._subscriptionId(), this._location()),
            model.machineLearningServicesData.getQuotaUsage(this._subscriptionId(), this._location(), false)
        ])
            .then((responses: any) => {
                const vmSizesResponse =
                    responses[0] && responses[0].amlCompute && Array.isArray(responses[0].amlCompute) ? responses[0].amlCompute : [];
                const quotaUsageResponse = responses[1] ? responses[1] : [];

                this._vmFamilyMultiDropDown.loading(false);
                this._lowPriorityQuotaGrid.loading(false);

                vmSizesResponse.forEach((vmSize: VMSize) => {
                    const vmFamilyNameLowerCased = vmSize.family.toLowerCase();
                    if (!this._supportedVMFamilySizesMap[vmFamilyNameLowerCased]) {
                        this._supportedVMFamilySizesMap[vmFamilyNameLowerCased] = [];
                    } else {
                        this._supportedVMFamilySizesMap[vmFamilyNameLowerCased].push(vmSize.name);
                    }
                });

                if (!quotaUsageResponse || !Array.isArray(quotaUsageResponse)) {
                    return Q();
                }

                this._quotaUsages = this._getQuotaUsageByVMFamily(quotaUsageResponse);
                const vmFamilyDropDownItems: DropDown.Item<string>[] = Object.keys(this._quotaUsages.dedicated).map((coreVMFamily: string) => {
                    return {
                        text: ko.observable(getFamilyDisplayName(coreVMFamily, ClientResources.CrossRegionComputeQuotas.requestBladeFamilyNameFormat)),
                        value: coreVMFamily
                    };
                }).sort((family1, family2) => family1.text().localeCompare(family2.text()));

                this._vmFamilyMultiDropDown.items(vmFamilyDropDownItems);

                if (this._quotaUsages.lowPriority) {
                    const coreVMFamilyName = this._quotaUsages.lowPriority.name.value;
                    const quotaGridRowDisplayName = QuotaUtils.getVMFamilyDisplayNameRequestBlade(coreVMFamilyName);

                    this._lowPriorityQuotaGrid.items([
                        {
                            displayName: ko.observable(quotaGridRowDisplayName),
                            currentLimit: ko.observable(this._quotaUsages.lowPriority.limit.toString()),
                            newLimit: NumericTextBox.create(container, {
                                min: ko.observable(0),
                                validations: ko.observableArray([new RequiredValidation()]),
                                ariaLabel: coreVMFamilyName + " " + ClientResources.quotaRequestNewLimit,
                                cssClass: `ext-ml-newlimit-${coreVMFamilyName}`
                            }),
                            vmFamily: coreVMFamilyName
                        }
                    ]);
                } else {
                    this._lowPriorityQuotaGrid.items([]);
                }
            })
            .catch(err => {
                this._vmFamilyMultiDropDown.items([{ text: ClientResources.quotaRequestNotFound, value: null }]);
                this._vmFamilyMultiDropDown.value([]);
                this._vmFamilyMultiDropDown.valid(false);
                this._vmFamilyMultiDropDown.loading(false);

                this._lowPriorityQuotaGrid.items([]);
                this._lowPriorityQuotaGrid.loading(false);
            })
            .finally(() => {
                this._vmFamilyMultiDropDown.loading(false);
                this._lowPriorityQuotaGrid.loading(false);
            });
    }

    private _getQuotaUsageByVMFamily(quotaResponse: QuotaUsage[]): IVmQuotaUsages {
        const quotaInfo: IVmQuotaUsages = { dedicated: {} };

        let totalClustersCount = 0;
        quotaResponse.forEach((quotaUsage: QuotaUsage) => {
            const coreVMFamilyName = quotaUsage.name.value;
            const usageType = QuotaUtils.getQuotaUsageType(quotaUsage);

            if (usageType === QuotaUsageType.TotalCores) {
                totalClustersCount = quotaUsage.currentValue;
                return;
            }

            if (usageType === QuotaUsageType.TotalLowPriorityCores) {
                quotaInfo.lowPriority = quotaUsage;
                return;
            }

            // Check if VM family is supported and if not, whether its has usages
            if (!this._supportedVMFamilySizesMap[coreVMFamilyName.toLowerCase()]) {
                return;
            }

            if (usageType === QuotaUsageType.TotalDedicatedCores || !QuotaUtils.isQuotaUsageDedicated(quotaUsage)) {
                return;
            }

            if (!quotaInfo.dedicated[coreVMFamilyName]) {
                quotaInfo.dedicated[coreVMFamilyName] = [];
            }

            quotaInfo.dedicated[coreVMFamilyName].push(quotaUsage);
        });

        if (totalClustersCount !== 0) {
            this._totalClustersCount = totalClustersCount;
        }

        return quotaInfo;
    }

    private _mapIncomingQuota(selectedVMFamilies: string[], existingQuotaGridValues: { [displayName: string]: number }) {
        if (this._initialLoad) {
            if (!selectedVMFamilies || !existingQuotaGridValues) {
                return;
            }

            const dedicatedVmFamilies = selectedVMFamilies.filter(
                f => MsPortalFx.localeCompareIgnoreCase(f, QuotaUsageType.TotalLowPriorityCores) !== 0
            );

            const lowPriorityVmFamilies = selectedVMFamilies.filter(
                f => MsPortalFx.localeCompareIgnoreCase(f, QuotaUsageType.TotalLowPriorityCores) === 0
            );

            const vmTypes: VmType[] = [];
            const hasDedicated = dedicatedVmFamilies.length > 0;
            const hasLowPriority = lowPriorityVmFamilies.length > 0;

            if (hasDedicated) {
                vmTypes.push("dedicated");
            }

            if (hasLowPriority) {
                vmTypes.push("low-priority");
            }

            this.typesDropDown.value(vmTypes);

            if (hasDedicated) {
                this._vmFamilyMultiDropDown.value(dedicatedVmFamilies);

                for (const row of this._dedicatedQuotaGrid.items()) {
                    row.newLimit.value(existingQuotaGridValues[row.vmFamily]);
                }
            }

            if (hasLowPriority) {
                for (const row of this._lowPriorityQuotaGrid.items()) {
                    row.newLimit.value(existingQuotaGridValues[row.vmFamily]);
                }
            }

            this._initialLoad = false;
        }
    }

    private _initDocumentationInfoBox() {
        this.documentationInfoBox = InfoBox.create(this.context.container, {
            style: InfoBox.Style.Info,
            text: ClientResources.quotaRequestDocumentationInfoBox,
            onClick: {
                uri: ko.observable(getDocumentUrl(DocLinkIds.ManageQuotas)),
                target: ko.observable("_blank"),
                onLinkOpened: () => { }
            }
        });
    }

    private _initButton() {
        this.submitBtn = Button.create(this.context.container, {
            text: ClientResources.quotaRequestSubmit,
            ariaLabel: ClientResources.quotaRequestSubmit,
            onClick: () => this._submitQuota()
        } as Button.Options);
    }

    private _submitQuota(): Promise<any> {
        const { container } = this.context;
        if (!this._isValid()) {
            return Q();
        }

        const payload = this._toJson();

        return container.closeCurrentBlade({
            status: "completed",
            payload,
            quotaRequest: this._quotaRequestMessage(),
            formattedQuotaDetails: this._formattedQuotaDetails()
        } as QuotaBladeOutput);
    }

    private _isValid(): boolean {
        const { container } = this.context;

        if (!this.locationsDropDown.valid() || !this.locationsDropDown.value()) {
            this.locationsDropDown.triggerValidation();
            container.setFocus(".ext-ml-locations-dropdown");
            return false;
        }

        const selectedTypes = this.typesDropDown.value();
        if (!this.typesDropDown.valid() || !selectedTypes || (Array.isArray(selectedTypes) && selectedTypes.length === 0)) {
            this.typesDropDown.triggerValidation();
            container.setFocus(".ext-ml-types-dropdown");
            return false;
        }

        if (this.lowPrioritySelected()) {
            let isFormValid = true;

            this._lowPriorityQuotaGrid.items().forEach((row: QuotaGridRowModel) => {
                if (!row.newLimit.valid() || MsPortalFx.isNullOrUndefined(row.newLimit.value())) {
                    row.newLimit.triggerValidation();

                    if (isFormValid) {
                        container.setFocus(`.ext-ml-newlimit-${row.vmFamily}`);
                    }

                    isFormValid = false;
                }
            });

            if (!isFormValid) {
                return false;
            }
        }

        if (this.dedicatedSelected()) {
            const selectedFamilies = this._vmFamilyMultiDropDown.value();
            if (!this._vmFamilyMultiDropDown.valid() || !selectedFamilies || (Array.isArray(selectedFamilies) && selectedFamilies.length === 0)) {
                this._vmFamilyMultiDropDown.triggerValidation();
                container.setFocus(".ext-ml-vmfamily-dropdown");
                return false;
            }

            let isFormValid = true;
            this._dedicatedQuotaGrid.items().forEach((row: QuotaGridRowModel) => {
                if (!row.newLimit.valid() || MsPortalFx.isNullOrUndefined(row.newLimit.value())) {
                    row.newLimit.triggerValidation();

                    if (isFormValid) {
                        container.setFocus(`.ext-ml-newlimit-${row.vmFamily}`);
                    }

                    isFormValid = false;
                }
            });

            if (!isFormValid) {
                return false;
            }
        }

        return true;
    }

    private _rowToPayload(
        row: QuotaGridRowModel,
        payload: QuotaRequestPayload,
        dedicated: boolean,
        selectedLocationDisplayName: string,
        messagesDetail: string[]
    ): void {
        const newBatchAIQuotaRequest = payload.quotaScopingDetails.newBatchAIQuotaRequest;

        if (dedicated) {
            newBatchAIQuotaRequest.requestedDedicatedCoreQuota[row.vmFamily] = row.newLimit.value();
            newBatchAIQuotaRequest.currentDedicatedCoreQuota[row.vmFamily] = parseInt(row.currentLimit(), 10);
        } else if (row.vmFamily === BackendQuotaUsageValues.name.value.totalLowPriorityCores) {
            newBatchAIQuotaRequest.requestedLowPriorityCoreQuota = row.newLimit.value();
            newBatchAIQuotaRequest.currentLowPriorityCoreQuota = parseInt(row.currentLimit(), 10);
        }

        payload.uxSpecificDetails.existingQuotaGridValues[row.vmFamily] = row.newLimit.value();

        messagesDetail.push(ClientResources.quotaRequestLogOutputMessageDetail.format(row.displayName(), row.currentLimit(), row.newLimit.value()));

        this._formattedQuotaDetails().push({
            requestDetails: row.displayName() + (selectedLocationDisplayName ? `, ${selectedLocationDisplayName}` : ""),
            currentQuota: row.currentLimit(),
            newQuota: row.newLimit.value().toString()
        });

        if (this._totalClustersCount) {
            payload.quotaScopingDetails.newBatchAIQuotaRequest.totalClusters = this._totalClustersCount;
        }
    }

    private _toJson(): QuotaRequestPayload {
        const payload: QuotaRequestPayload = {
            quotaScopingDetailsSchema: quotaScopingDetailsSchema,
            quotaScopingDetails: {
                subscriptionId: this._subscriptionId(),
                region: this.locationsDropDown.value(),
                newBatchAIQuotaRequest: {
                    // These values are filled below
                    requestedDedicatedCoreQuota: {},
                    currentDedicatedCoreQuota: {},
                    currentLowPriorityCoreQuota: 0,
                    requestedLowPriorityCoreQuota: 0,
                    totalClusters: 0
                }
            },
            uxSpecificDetails: {
                selectedVMFamilies: [].concat(this._vmFamilyMultiDropDown.value()),
                existingQuotaGridValues: {
                    // These values are filled below
                }
            }
        };

        const messagesDetail: string[] = [];
        this._formattedQuotaDetails([]);

        const selectedLocationItem = MsPortalFx.find(
            this.locationsDropDown.items(),
            (item: DropDown.Item<string>) => item.value === this.locationsDropDown.value()
        );
        const selectedLocationDisplayName = selectedLocationItem ? (selectedLocationItem.text as KnockoutObservableBase<string>)() : "";

        if (this.dedicatedSelected()) {
            this._dedicatedQuotaGrid.items().forEach((row: QuotaGridRowModel) => {
                this._rowToPayload(row, payload, true, selectedLocationDisplayName, messagesDetail);
            });
        }

        if (this.lowPrioritySelected()) {
            this._lowPriorityQuotaGrid.items().forEach((row: QuotaGridRowModel) => {
                this._rowToPayload(row, payload, false, selectedLocationDisplayName, messagesDetail);
            });

            payload.uxSpecificDetails.selectedVMFamilies.push(QuotaUsageType.TotalLowPriorityCores);
        }

        this._quotaRequestMessage(`Quota request for Batch AI (Location: ${selectedLocationDisplayName}) :\n` + messagesDetail.join("\n"));

        return payload;
    }
}
