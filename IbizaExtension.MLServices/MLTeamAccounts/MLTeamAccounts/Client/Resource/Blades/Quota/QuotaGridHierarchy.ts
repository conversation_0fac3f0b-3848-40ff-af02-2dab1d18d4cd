﻿import * as CustomHtml from "Fx/Controls/CustomHtml";
import * as NumericTextBox from "Fx/Controls/NumericTextBox";
import * as ProgressBar from "Fx/Controls/ProgressBar";
import { QuotaUsageType } from "./QuotaUtils";

import Grid = MsPortalFx.ViewModels.Controls.Lists.Grid;

export interface QuotaRowItemBase extends Grid.HierarchicalItem {
    resourceId: string;
    resourceName: string;
    displayName: string;
    resourceType: QuotaUsageType;
    parentResourceName?: string;
    parentResourceId?: string;
    rootParentResourceName?: string;
    displayNameLink?: string;
}

export interface QuotaRowItem extends QuotaRowItemBase {
    percent?: number;
    percentText?: string;
    percentColor?: string;
    percentControl?: ProgressBar.Contract;
    dedicatedPercent: number;
    dedicatedPercentLabel: string;
    dedicatedPercentColor: string;
    lowPriorityPercent: number;
    lowPriorityPercentLabel: string;
    lowPriorityPercentColor: string;
    currentValue?: number;
    currentLimit?: number;
    usageText?: string;
    totalUsageText?: string;
    crossLocationText?: CustomHtml.Contract;
}

export interface QuotaSettingRowItem extends QuotaRowItemBase {
    currentLimit?: KnockoutObservableBase<number>;
    newLimit?: NumericTextBox.Contract;
    saveButton?: KnockoutObservableBase<any>;
    status?: KnockoutObservableBase<string>;
    placeHolderText?: KnockoutObservableBase<string>;
    showSaveButton?: KnockoutObservableBase<boolean>;
    workspaceLocation?: string;
}

export const FIRST_LEVEL = 0;
export const SECOND_LEVEL = 1;
export const THIRD_LEVEL = 2;

export class QuotaGridHierarchy implements Grid.Hierarchy<QuotaRowItemBase> {
    public items: KnockoutObservable<QuotaRowItemBase[]>;
    public _firstLevelItemsMap: { [firstLevelItemKey: string]: QuotaRowItemBase } = {};
    public _secondLevelItemsMap: { [firstLevelItemKey: string]: { [secondLevelItemKey: string]: QuotaRowItemBase } } = {};
    public _thirdLevelItemsMap: {
        [firstLevelItemKey: string]: { [secondLevelItemKey: string]: { [thirdLevelItemKey: string]: QuotaRowItemBase } };
    } = {};

    constructor(
        firstLevelItems: { [firstLevelItemKey: string]: QuotaRowItemBase },
        secondLevelItemsMap: { [firstLevelItemKey: string]: { [secondLevelItemKey: string]: QuotaRowItemBase } },
        thirdLevelItemsMap: { [firstLevelItemKey: string]: { [secondLevelItemKey: string]: { [thirdLevelItemKey: string]: QuotaRowItemBase } } }
    ) {
        this._firstLevelItemsMap = firstLevelItems;
        this._secondLevelItemsMap = secondLevelItemsMap;
        this._thirdLevelItemsMap = thirdLevelItemsMap;
        this.items = ko.observable<QuotaRowItemBase[]>();
    }

    public expand = (quotaRowItem: QuotaRowItemBase) => {
        if (!quotaRowItem.expanded()) {
            quotaRowItem.expanded(true);
            const spliceIndex = this.items().indexOf(quotaRowItem);

            if (quotaRowItem.depth() === FIRST_LEVEL) {
                const newItems: QuotaRowItemBase[] = [];
                const resourceName = quotaRowItem.resourceName;
                if (this._secondLevelItemsMap[resourceName]) {
                    Object.keys(this._secondLevelItemsMap[resourceName]).forEach(secondLevelItemKey => {
                        this._secondLevelItemsMap[resourceName][secondLevelItemKey].expanded(false);
                        newItems.push(this._secondLevelItemsMap[resourceName][secondLevelItemKey]);
                    });
                }
                newItems.sort(sortUsageItems);
                const copyItems = this.items.peek();
                copyItems.splice(spliceIndex + 1, 0, ...newItems);
                this.items(copyItems);
            } else if (quotaRowItem.depth() === SECOND_LEVEL) {
                const newItems: QuotaRowItemBase[] = [];
                const resourceName = quotaRowItem.resourceName;
                const parentResourceName = quotaRowItem.parentResourceName;
                if (this._thirdLevelItemsMap[parentResourceName] && this._thirdLevelItemsMap[parentResourceName][resourceName]) {
                    Object.keys(this._thirdLevelItemsMap[parentResourceName][resourceName]).forEach(thirdLevelItemKey => {
                        newItems.push(this._thirdLevelItemsMap[parentResourceName][resourceName][thirdLevelItemKey]);
                    });
                }
                newItems.sort(sortUsageItems);
                const copyItems = this.items.peek();
                copyItems.splice(spliceIndex + 1, 0, ...newItems);
                this.items(copyItems);
            }
        }
    };

    public collapse = (quotaRowItem: QuotaRowItemBase) => {
        quotaRowItem.expanded(false);
        let newItems: QuotaRowItemBase[] = [];

        if (quotaRowItem.depth() === FIRST_LEVEL) {
            newItems = this.items()
                .map(rowItem => {
                    if (rowItem.depth() === SECOND_LEVEL && rowItem.parentResourceName === quotaRowItem.resourceName) {
                        return null;
                    }
                    if (rowItem.depth() === THIRD_LEVEL) {
                        if (rowItem.rootParentResourceName === quotaRowItem.resourceName) {
                            return null;
                        }
                    }
                    return rowItem;
                })
                .filter(rowItem => rowItem !== null);
        } else if (quotaRowItem.depth() === SECOND_LEVEL) {
            newItems = this.items()
                .map(rowItem => {
                    if (
                        rowItem.depth() > quotaRowItem.depth() &&
                        rowItem.parentResourceName === quotaRowItem.resourceName &&
                        rowItem.rootParentResourceName === quotaRowItem.parentResourceName
                    ) {
                        return null;
                    }
                    return rowItem;
                })
                .filter(rowItem => rowItem !== null);
        }
        this.items(newItems);
    };

    public collapseAll = () => { };

    public expandAll = () => { };

    public filterSecondLevelItems(filter: string): void {
        const newItems: QuotaRowItemBase[] = [];
        const firstLevelItems: Array<{ key: string, item: QuotaRowItemBase }> = [];
        let anyWorkspaceFound = false;
        for (const firstLevelKey of Object.keys(this._firstLevelItemsMap || {})) {
            const firstLevelRowItem = this._firstLevelItemsMap[firstLevelKey];
            if (firstLevelRowItem) {
                firstLevelItems.push({ key: firstLevelKey, item: firstLevelRowItem });
            }
        }


        for (const firstLevelItem of firstLevelItems.sort((item1, item2) => MsPortalFx.localeCompareIgnoreCase(item1.item.displayName, item2.item.displayName))) {
            const firstLevelRowItem = firstLevelItem.item;
            newItems.push(firstLevelRowItem);
            firstLevelRowItem.expandable(false);

            const secondLevelItemsMap = this._secondLevelItemsMap[firstLevelItem.key] || {};
            for (const secondLevelKey of Object.keys(secondLevelItemsMap)) {
                const secondLevelRowItem = secondLevelItemsMap[secondLevelKey];
                if (secondLevelRowItem.displayName.indexOf(filter) >= 0) {
                    anyWorkspaceFound = true;
                    newItems.push(secondLevelRowItem);
                }
            }
        }

        this.items(anyWorkspaceFound ? newItems : []);
    }

    public resetFilter(): void {
        const newItems: QuotaRowItemBase[] = [];
        for (const firstLevelKey of Object.keys(this._firstLevelItemsMap || {})) {
            const firstLevelRowItem = this._firstLevelItemsMap[firstLevelKey];
            newItems.push(firstLevelRowItem);
            firstLevelRowItem.expandable(true);
            firstLevelRowItem.expanded(false);
        }

        this.items(newItems.sort((item1, item2) => MsPortalFx.localeCompareIgnoreCase(item1.displayName, item2.displayName)));
    }
}

export function sortUsageItems(a: QuotaRowItemBase, b: QuotaRowItemBase): number {
    if (a.resourceType === QuotaUsageType.CrossLocationComputeInfo) {
        return 1;
    }

    if (b.resourceType === QuotaUsageType.CrossLocationComputeInfo) {
        return -1;
    }

    return a.displayName.localeCompare(b.displayName);
}
