import * as TemplateBlade from "Fx/Composition/TemplateBlade";
import * as CustomHtml from "Fx/Controls/CustomHtml";
import * as Button from "Fx/Controls/Button";
import * as TextBlock from "Fx/Controls/TextBlock";
import { DataContext } from "../../ResourceArea";
import * as ClientResources from "Resx/ClientResources";
import { Icons } from "Shared/Icons";
import Utilities from "Shared/Utilities";

export interface Parameters {
    location?: string;
    subscriptionId?: string;
}

@TemplateBlade.Decorator({
    htmlTemplate: `
    <div class="ext-ml-quota-section msportalfx-docking-body">
        <div data-bind='pcControl: studioQuotaBanner' style="height:80%; overflow:hidden;"></div>
    </div>
    `,
    styleSheets: ["../../../Shared/Styles/CustomStyles.css", ""]
})
@TemplateBlade.InjectableModel.Decorator(DataContext)
export class StudioQuotaLinkBlade {
    public title = ClientResources.quotaBladeTitle;
    public subtitle = ClientResources.quotaBladeTitle;
    public context: TemplateBlade.Context<Parameters, DataContext>;

    public studioQuotaBanner: CustomHtml.Contract;

    public async onInitialize(): Promise<void> {
        const { container, parameters } = this.context;
        container.icon(MsPortalFx.Base.Images.Polychromatic.ControlsHorizontal());

        const linkIcon = Icons.hyperlinkIcon;
        const launchButtonUrl = Utilities.generateStudioQuotaLink(parameters.subscriptionId, parameters.location);
        const QuotaResources = ClientResources.Quota.Link;


        const bannerTitle = TextBlock.create(container, {
            cssClass: "ext-ml-quota-link-title",
            text: QuotaResources.bladeTitle
        });

        const bannerDescriptionTemplate = QuotaResources.bladeDescription.format(
        );

        const bannerDescription = CustomHtml.create(container, {
            cssClass: "ext-ml-quota-link-description",
            htmlTemplate: bannerDescriptionTemplate
        });

        const launchButton = Button.create(container, {
            cssClass: "ext-ml-quota-link-button",
            onClick: {
                uri: ko.observable(launchButtonUrl),
                target: ko.observable("_blank"),
                onLinkOpened: () => {
                    MsPortalFx.Base.Diagnostics.Telemetry.trace({
                        action: "LaunchQuota",
                        source: "StudioQuotaLinkBlade",
                        data: {
                            subscriptionId: parameters.subscriptionId
                        }
                    });
                }
            },
            text: QuotaResources.button
        } as Button.Options);
        this.studioQuotaBanner = CustomHtml.create(container, {
            htmlTemplate: `
                <div class='ext-ml-quota-link-container' style='height:100%'>
                    <div class='ext-ml-quota-link-banner'>
                        <div class='ext-ml-ws2-banner-icon msportalfx-svg-disabled' data-bind='image: bannerIcon'></div>
                        <h1 data-bind='pcControl: bannerTitle'></h1>
                        <h2 data-bind='pcControl: bannerDescription'></h2>
                        <div data-bind='pcControl: launchButton'></div>
                    </div>
                </div>`,
            innerViewModel: {
                bannerIcon: Icons.aiStudioIcon,
                bannerDescription,
                bannerTitle,
                launchButton,
                linkIcon
            }
        });
    }
}