import { BladeReferences } from "Fx/Composition";
import * as TemplateBlade from "Fx/Composition/TemplateBlade";
import * as Button from "Fx/Controls/Button";
import * as CustomHtml from "Fx/Controls/CustomHtml";
import * as InfoBox from "Fx/Controls/InfoBox";
import * as ProgressBar from "Fx/Controls/ProgressBar";

import { LifetimeManager } from "Fx/Lifetime";
import { ArmId } from "Fx/ResourceManagement";
import * as Globalization from "MsPortalFx/Globalization";

import * as Format from "../../../../../Shared/Controls/Format";
import Utilities from "../../../../../Shared/Utilities";
import { QuotaUsage } from "../../Models/QuotaUsage";
import { VMFamilies, VMSize } from "../../Models/VMSize";
import { FIRST_LEVEL, QuotaGridHierarchy, QuotaRowItem, QuotaRowItemBase, SECOND_LEVEL, sortUsageItems, THIRD_LEVEL } from "../../QuotaGridHierarchy";
import { QuotaUsageType, QuotaUtils } from "../../QuotaUtils";
import { ComputeQuotaUsageType, getFamilyDisplayName, getFullComputeName, getFullWorkspaceName, getQuotaUsageType } from "../ComputeQuotaUtils";
import * as TotalUsagesText from "./TotalQuotaUsagesText";

import * as ClientResources from "Resx/ClientResources";

import Grid = MsPortalFx.ViewModels.Controls.Lists.Grid;
import ExtensionsOptions = MsPortalFx.ViewModels.Controls.Lists.Grid.ExtensionsOptions;

export type QuotaUsageGroupingType = "computeFamily" | "workspace";
export const enum QuotaUsageCoresType {
    Dedicated = 0,
    LowPriority = 1
}

export interface IQuotaUsagesTreeOptions {
    /**
     * Determines which cores type to use.
     * @default "dedicated"
     */
    coresType?: QuotaUsageCoresType;

    /**
     * Determines which grouping type to use.
     * @default "computeFamily"
     */
    groupingType?: QuotaUsageGroupingType;

    /**
     * Currently selected location.
     */
    location: KnockoutObservableBase<string>;

    /**
     * Determines whether to show cross location computes or not.
     */
    showCrossLocationComputes: KnockoutObservableBase<boolean>;

    /**
     * Currently selected subscription.
     */
    subscriptionId: KnockoutObservableBase<string>;

    /**
     * Currently active tenant id.
     */
    tenantId: KnockoutObservableBase<string>;

    /**
     * All cores usage for this subscription.
     */
    usages: QuotaUsage[];

    /**
     * All available vm sizes in this subscription.
     */
    vmSizes: VMSize[];

    /**
     * Locations of all workspaces.
     */
    workspaceLocations: Map<string, string>;
}

export function create(container: TemplateBlade.Container, options: IQuotaUsagesTreeOptions): CustomHtml.Contract {
    const { coresType = QuotaUsageCoresType.Dedicated, groupingType = "computeFamily", location, subscriptionId } = options;

    const totalCoresText = ko.observable("");
    const totalCores = TotalUsagesText.create(container, {
        totalLabel:
            coresType === QuotaUsageCoresType.Dedicated
                ? ClientResources.CrossRegionComputeQuotas.totalDedicatedCoresLabel
                : ClientResources.CrossRegionComputeQuotas.totalLowPriorityCoresLabel,
        totalText: totalCoresText
    });

    const totalClustersText = ko.observable("");
    const totalClusters = TotalUsagesText.create(container, {
        totalLabel: ClientResources.CrossRegionComputeQuotas.totalClustersLabel,
        totalText: totalClustersText
    });

    const treeColumns = getTreeColumns(coresType, groupingType);
    const treeItems = buildTreeItems(container, options);

    // Please see this stack overflow Q&A for more info on Hierarchical Grids in Ibiza
    // https://stackoverflow.microsoft.com/questions/127763/how-to-implement-hierarchical-grid
    const usageItems = MsPortalFx.thunkArray(container, treeItems.usageHierarchy.items);

    const usageTree = new Grid.ViewModel<QuotaRowItemBase, any>(container, usageItems, Grid.Extensions.Hierarchical, {
        hierarchical: {
            hierarchy: treeItems.usageHierarchy
        }
    } as ExtensionsOptions<QuotaRowItemBase, any>);

    usageTree.columns(treeColumns);
    usageTree.showHeader = true;
    usageTree.noRowsMessage(ClientResources.quotaTableNoData);

    if (treeItems.coresTotal) {
        totalCoresText(
            ClientResources.CrossRegionComputeQuotas.totalCoresTextFormat.format(
                treeItems.coresTotal.currentValue,
                treeItems.coresTotal.limit - treeItems.coresTotal.currentValue
            )
        );

        totalCores.visible(true);
    }

    if (treeItems.clustersTotal) {
        totalClustersText(
            ClientResources.CrossRegionComputeQuotas.totalClustersTextFormat.format(
                treeItems.clustersTotal.currentValue,
                treeItems.clustersTotal.limit - treeItems.clustersTotal.currentValue
            )
        );

        totalClusters.visible(true);
    }

    return CustomHtml.create(container, {
        htmlTemplate: `
    <div class="ext-ml-quota-view">
        <div class="ext-ml-quota-view-actionbar">
            <div class="ext-ml-quota-view-actionbar-info">
                <div class="ext-ml-quota-view-core-quota" data-bind="pcControl: totalCores"></div>
                <div class="ext-ml-quota-view-cluster-quota" data-bind="pcControl: totalClusters"></div>
            </div>
            <div class="ext-ml-quota-view-actionbar-action">
                <div class="ext-ml-quota-view-request-quota-button" data-bind="pcControl: requestQuotaButton"></div>
                <div class="ext-ml-quota-view-configure-quota-button" data-bind="pcControl: configureQuotaButton"></div>
            </div>
        </div>
        <div data-bind="pcControl: usageWarning"></div>
        <div class="ext-ml-quota-view-tree-container">
            <div data-bind="pcControl: usageGrid"></div>
        </div>
    </div>`,
        innerViewModel: {
            totalCores,
            totalClusters,
            usageGrid: usageTree,
            configureQuotaButton: Button.create(container, {
                text: ClientResources.CrossRegionComputeQuotas.configureQuotaButtonText,
                style: Button.Style.Secondary,
                onClick: () => {
                    const bladeReference = BladeReferences.forBlade("CrossLocationQuotaConfigurationBlade").createReference({
                        parameters: {
                            location: location(),
                            subscriptionId: subscriptionId()
                        },
                        onClosed: () => {}
                    });
                    container.openContextPane(bladeReference);
                }
            }),
            requestQuotaButton: Button.create(container, {
                text: ClientResources.CrossRegionComputeQuotas.requestQuotaButtonText,
                style: Button.Style.Secondary,
                onClick: () => {
                    const bladeReference = Utilities.getQuotaSupportRequestBladeReference(subscriptionId());
                    container.openBlade(bladeReference);
                }
            }),
            usageWarning: InfoBox.createInline(container, {
                style: InfoBox.Style.Info,
                text: ClientResources.CrossRegionComputeQuotas.lowPriorityUsageInfoText,
                visible: coresType === QuotaUsageCoresType.LowPriority
            })
        }
    });
}

function getTreeColumns(coresType: QuotaUsageCoresType, groupingType: QuotaUsageGroupingType): Grid.Column[] {
    const columns: Grid.Column[] = [];

    columns.push({
        itemKey: "displayName",
        name: ko.observable(
            groupingType === "computeFamily"
                ? ClientResources.CrossRegionComputeQuotas.UsageGrid.vmFamiliesColumnText
                : ClientResources.CrossRegionComputeQuotas.UsageGrid.workspacesColumnText
        ),
        width: ko.observable("40%"),
        cssClass: "ext-ml-displayname-column",
        hierarchical: true,
        format: Grid.Format.HtmlBindings,
        formatOptions: {
            htmlBindingsTemplate: `
                <div>
                    <!-- ko if: settings.item.resourceType === "VMFamily" -->
                        <div data-bind="text: settings.item.displayName"></div>
                    <!-- /ko -->
                    <!-- ko if: settings.item.resourceType === "Workspace" -->
                        <a data-bind="html: settings.item.displayName, attr: { href: settings.item.displayNameLink, title: settings.item.displayNameLink }" target="_blank" class="ext-ml-workspace-link"></a>
                    <!-- /ko -->
                    <!-- ko if: settings.item.resourceType === "Compute" -->
                        <a data-bind="text: settings.item.displayName, attr: { href: settings.item.displayNameLink, title: settings.item.displayNameLink }" target="_blank" class="ext-ml-compute-link"></a>
                    <!-- /ko -->
                    <!-- ko if: settings.item.resourceType === "TotalCores" -->
                        <div class="ext-ml-totalcores-name" data-bind="text: settings.item.displayName"></div>
                    <!-- /ko -->
                    <!-- ko if: settings.item.resourceType === "CrossLocationComputeInfo" -->
                        <div class="ext-ml-crosslocation-info msportalfx-text-error" data-bind="pcControl: settings.item.crossLocationText"></div>
                    <!-- /ko -->
                </div>`
        }
    });

    const isDedicated = coresType === QuotaUsageCoresType.Dedicated;
    if (isDedicated) {
        columns.push({
            itemKey: "percent",
            name: ko.observable(ClientResources.CrossRegionComputeQuotas.UsageGrid.usageColumnText),
            width: ko.observable("30%"),
            format: Grid.Format.HtmlBindings,
            formatOptions: {
                htmlBindingsTemplate: `
                <div>
                    <!-- ko if: settings.item.resourceType === "TotalCores" -->
                        <div data-bind="text: settings.item.totalUsageText"></div>
                    <!-- /ko -->
                    <!-- ko if: settings.item.resourceType !== "TotalCores" -->
                        <div class="ext-ml-quota-view-progress" data-bind="pcControl: settings.item.percentControl"></div>
                    <!-- /ko -->
                </div>`
            }
        });

        columns.push({
            itemKey: "percentText",
            name: ko.observable(ClientResources.CrossRegionComputeQuotas.UsageGrid.usagePercentageColumnText),
            width: ko.observable("1%"),
            format: Grid.Format.HtmlBindings,
            formatOptions: {
                htmlBindingsTemplate: `<div aria-hidden="true" data-bind="text: settings.item.percentText"></div>`
            }
        });

        columns.push({
            itemKey: "usageText",
            name: ko.observable(ClientResources.CrossRegionComputeQuotas.UsageGrid.coreUtilizationColumnText),
            width: ko.observable("29%"),
            format: Grid.Format.Text
        });
    } else {
        columns.push({
            itemKey: "usageText",
            name: ko.observable(ClientResources.CrossRegionComputeQuotas.UsageGrid.usageColumnText),
            width: ko.observable("60%"),
            format: Grid.Format.Text
        });
    }

    return columns;
}

function buildTreeItems(
    container: TemplateBlade.Container,
    options: IQuotaUsagesTreeOptions
): { coresTotal?: QuotaUsage; clustersTotal?: QuotaUsage; usageHierarchy: QuotaGridHierarchy } {
    const {
        coresType = QuotaUsageCoresType.Dedicated,
        groupingType = "computeFamily",
        location,
        showCrossLocationComputes,
        tenantId,
        usages,
        vmSizes,
        workspaceLocations
    } = options;

    let dedicatedCoresTotal: QuotaUsage | undefined;
    let lowPriorityCoresTotal: QuotaUsage | undefined;
    let clustersTotal: QuotaUsage | undefined;

    const topFamilyMap: { [familyName: string]: QuotaRowItem } = {};
    const topWorkspaceMap: { [workspaceName: string]: QuotaRowItem } = {};
    const secondLevelMap: { [firstLevelKey: string]: { [secondLevelKey: string]: QuotaRowItem } } = {};
    const thirdLevelMap: { [firstLevelKey: string]: { [secondLevelKey: string]: { [thirdLevelKey: string]: QuotaRowItem } } } = {};
    const crossLocationComputeCounts: { [familyName: string]: number } = {};

    let defaultLimit = -1;
    let maxFamilyLimit = -1;
    const vmFamilies = new VMFamilies(vmSizes);
    const sortedUsages = [...usages].sort(compareUsageSpecificity);
    const coresTypeUsages = sortedUsages.filter(u => {
        const oppositeUsageType = coresType === QuotaUsageCoresType.Dedicated ? ComputeQuotaUsageType.LowPriority : ComputeQuotaUsageType.Dedicated;
        const computeUsageType = getQuotaUsageType(u.type);
        return (computeUsageType & oppositeUsageType) === 0;
    });

    for (const usage of coresTypeUsages) {
        const usageType = QuotaUtils.getQuotaUsageType(usage);
        switch (usageType) {
            case QuotaUsageType.TotalCores: {
                clustersTotal = usage;
                break;
            }
            case QuotaUsageType.TotalDedicatedCores: {
                dedicatedCoresTotal = usage;
                defaultLimit = usage.limit;
                break;
            }
            case QuotaUsageType.TotalLowPriorityCores: {
                lowPriorityCoresTotal = usage;
                defaultLimit = usage.limit;
                break;
            }
            case QuotaUsageType.VMFamily: {
                const familyName = usage.name.value;
                const displayName = getFamilyDisplayName(familyName);
                maxFamilyLimit = Math.max(usage.limit, maxFamilyLimit);

                if (!vmFamilies.contains(familyName) && usage.currentValue === 0) {
                    // Skip processing this VM Family because its not supported for this location
                    break;
                }

                let familyRowItem = topFamilyMap[familyName];
                if (!familyRowItem) {
                    familyRowItem = generateBuildTreeItem(usage.id, familyName, displayName, usageType, FIRST_LEVEL);
                    topFamilyMap[familyName] = familyRowItem;
                }

                setUsage(container, usage.currentValue, usage.limit, familyRowItem, defaultLimit);

                break;
            }
            case QuotaUsageType.Workspace: {
                const familyName = usage.name.value;
                const displayName = QuotaUtils.getVMFamilyDisplayNameUsagesBlade(familyName);
                const familyRowItem = topFamilyMap[familyName];

                // Process this workspace only if we have processed its VM Family entry
                if (familyRowItem) {
                    const usageArmId = ArmId.parse(usage.id);
                    const workspaceName = usageArmId.resourceIds[0];
                    const resourceGroupName = usageArmId.resourceGroup;
                    const subscriptionId = usageArmId.subscription;
                    const fullWorkspaceName = getFullWorkspaceName(resourceGroupName, workspaceName);
                    let workspaceDisplayName = ClientResources.CrossRegionComputeQuotas.UsageGrid.workspaceDisplayText.format(
                        workspaceName,
                        resourceGroupName
                    );
                    const workspaceDisplayNameLink = getWorkspaceDisplayNameLink(subscriptionId, resourceGroupName, workspaceName);

                    // Check whether compute is cross-location or not
                    let isCrossLocation = false;
                    if (workspaceLocations.has(fullWorkspaceName)) {
                        isCrossLocation = MsPortalFx.localeCompareIgnoreCase(workspaceLocations.get(fullWorkspaceName), location()) !== 0;
                    }

                    if (!isCrossLocation || showCrossLocationComputes()) {
                        if (isCrossLocation) {
                            workspaceDisplayName = ClientResources.CrossRegionComputeQuotas.UsageGrid.workspaceCrossLocationDisplayText.format(
                                workspaceName,
                                resourceGroupName,
                                `<b>${workspaceLocations.get(fullWorkspaceName)}</b>`
                            );
                        }

                        if (groupingType === "computeFamily") {
                            let familyWorkspaceMap = secondLevelMap[familyName];
                            if (!familyWorkspaceMap) {
                                familyWorkspaceMap = {};
                                secondLevelMap[familyName] = familyWorkspaceMap;
                            }

                            let familyWorkspaceRowItem = familyWorkspaceMap[fullWorkspaceName];
                            if (!familyWorkspaceRowItem) {
                                familyWorkspaceRowItem = generateBuildTreeItem(
                                    usage.id,
                                    fullWorkspaceName,
                                    workspaceDisplayName,
                                    QuotaUsageType.Workspace,
                                    SECOND_LEVEL,
                                    familyName,
                                    familyName,
                                    familyName,
                                    workspaceDisplayNameLink
                                );
                                familyWorkspaceMap[fullWorkspaceName] = familyWorkspaceRowItem;
                            }

                            setUsage(container, usage.currentValue, usage.limit, familyWorkspaceRowItem, defaultLimit);

                            // Make family row item expandable
                            familyRowItem.expandable(true);
                            familyRowItem.expanded(false);
                        } else {
                            let workspaceRowItem = topWorkspaceMap[fullWorkspaceName];
                            if (!workspaceRowItem) {
                                workspaceRowItem = generateBuildTreeItem(
                                    usage.id,
                                    fullWorkspaceName,
                                    workspaceDisplayName,
                                    usageType,
                                    FIRST_LEVEL,
                                    null,
                                    null,
                                    null,
                                    workspaceDisplayNameLink
                                );

                                topWorkspaceMap[fullWorkspaceName] = workspaceRowItem;
                            }

                            let workspaceFamilyMap = secondLevelMap[fullWorkspaceName];
                            if (!workspaceFamilyMap) {
                                workspaceFamilyMap = {};
                                secondLevelMap[fullWorkspaceName] = workspaceFamilyMap;
                            }

                            let workspaceFamilyRowItem = workspaceFamilyMap[familyName];
                            if (!workspaceFamilyRowItem) {
                                workspaceFamilyRowItem = generateBuildTreeItem(
                                    familyRowItem.resourceId,
                                    familyRowItem.resourceName,
                                    displayName,
                                    QuotaUsageType.VMFamily,
                                    SECOND_LEVEL,
                                    fullWorkspaceName,
                                    fullWorkspaceName,
                                    fullWorkspaceName
                                );

                                workspaceFamilyMap[familyName] = workspaceFamilyRowItem;
                            }

                            setUsage(
                                container,
                                usage.currentValue + workspaceRowItem.currentValue,
                                Math.min(workspaceRowItem.currentLimit + usage.limit, maxFamilyLimit),
                                workspaceRowItem,
                                defaultLimit
                            );
                            setUsage(container, usage.currentValue, usage.limit, workspaceFamilyRowItem, defaultLimit);

                            // Make workspace row item expandable
                            workspaceRowItem.expandable(true);
                            workspaceRowItem.expanded(false);
                        }
                    }
                }
                break;
            }
            case QuotaUsageType.Compute: {
                const familyName = usage.name.value;
                const usageArmId = ArmId.parse(usage.id);
                const workspaceName = usageArmId.resourceIds[0];
                const resourceGroupName = usageArmId.resourceGroup;
                const subscriptionId = usageArmId.subscription;
                const computeName = usageArmId.resourceIds[1];
                const fullWorkspaceName = getFullWorkspaceName(resourceGroupName, workspaceName);
                const fullComputeName = getFullComputeName(fullWorkspaceName, computeName);

                // See whether this particular usage has endpoint/deployment information
                let computeDisplayName: string;
                let computeDisplayNameLink: string;
                const endpointIndex = usageArmId.resourceTypes.findIndex(r => MsPortalFx.localeCompareIgnoreCase(r, "endpoints") === 0);
                const deploymentIndex = usageArmId.resourceTypes.findIndex(r => MsPortalFx.localeCompareIgnoreCase(r, "deployments") === 0);
                if (endpointIndex > 0 && deploymentIndex > 0) {
                    const endpointName = usageArmId.resourceIds[endpointIndex];
                    const deploymentName = usageArmId.resourceIds[deploymentIndex];
                    computeDisplayName = `${endpointName}/${deploymentName}`;
                    computeDisplayNameLink = getOnlineEndpointDeploymentLink(
                        subscriptionId,
                        resourceGroupName,
                        workspaceName,
                        endpointName,
                        deploymentName
                    );
                } else {
                    computeDisplayName = computeName;
                    computeDisplayNameLink = getComputeDisplayNameLink(subscriptionId, resourceGroupName, workspaceName, computeName, tenantId());
                }

                // Check whether compute is cross-location or not
                let isCrossLocation = false;
                if (workspaceLocations.has(fullWorkspaceName)) {
                    isCrossLocation = MsPortalFx.localeCompareIgnoreCase(workspaceLocations.get(fullWorkspaceName), location()) !== 0;
                }

                if (isCrossLocation) {
                    let crossLocationCount = crossLocationComputeCounts[familyName] || 0;
                    crossLocationCount += usage.currentValue;
                    crossLocationComputeCounts[familyName] = crossLocationCount;
                }

                if (!isCrossLocation || showCrossLocationComputes()) {
                    if (groupingType === "computeFamily") {
                        const familyRowItem = topFamilyMap[familyName];
                        const familyWorkspaceRowItem = secondLevelMap[familyName] ? secondLevelMap[familyName][fullWorkspaceName] : undefined;
                        if (familyRowItem && familyWorkspaceRowItem) {
                            let familyMap = thirdLevelMap[familyName];
                            if (!familyMap) {
                                familyMap = {};
                                thirdLevelMap[familyName] = familyMap;
                            }

                            let familyWorkspaceMap = familyMap[fullWorkspaceName];
                            if (!familyWorkspaceMap) {
                                familyWorkspaceMap = {};
                                familyMap[fullWorkspaceName] = familyWorkspaceMap;
                            }

                            let familyWorkspaceComputeRowItem = familyWorkspaceMap[fullComputeName];
                            if (!familyWorkspaceComputeRowItem) {
                                familyWorkspaceComputeRowItem = generateBuildTreeItem(
                                    usage.id,
                                    fullComputeName,
                                    computeDisplayName,
                                    usageType,
                                    THIRD_LEVEL,
                                    fullWorkspaceName,
                                    familyWorkspaceRowItem.resourceId,
                                    familyRowItem.resourceName,
                                    computeDisplayNameLink
                                );
                                familyWorkspaceMap[fullComputeName] = familyWorkspaceComputeRowItem;
                            }

                            setUsage(container, usage.currentValue, usage.limit, familyWorkspaceComputeRowItem, defaultLimit);

                            // Make workspace row under family row expandable
                            familyWorkspaceRowItem.expandable(true);
                            familyWorkspaceRowItem.expanded(false);
                        }
                    } else {
                        const workspaceRowItem = topWorkspaceMap[fullWorkspaceName];
                        const workspaceFamilyRowItem = secondLevelMap[fullWorkspaceName] ? secondLevelMap[fullWorkspaceName][familyName] : undefined;
                        if (workspaceRowItem && workspaceFamilyRowItem) {
                            let workspaceMap = thirdLevelMap[fullWorkspaceName];
                            if (!workspaceMap) {
                                workspaceMap = {};
                                thirdLevelMap[fullWorkspaceName] = workspaceMap;
                            }

                            let workspaceFamilyMap = workspaceMap[familyName];
                            if (!workspaceFamilyMap) {
                                workspaceFamilyMap = {};
                                workspaceMap[familyName] = workspaceFamilyMap;
                            }

                            let workspaceFamilyComputeRowItem = workspaceFamilyMap[fullComputeName];
                            if (!workspaceFamilyComputeRowItem) {
                                workspaceFamilyComputeRowItem = generateBuildTreeItem(
                                    usage.id,
                                    fullComputeName,
                                    computeDisplayName,
                                    usageType,
                                    THIRD_LEVEL,
                                    familyName,
                                    workspaceFamilyRowItem.resourceId,
                                    fullWorkspaceName,
                                    computeDisplayNameLink
                                );

                                workspaceFamilyMap[fullComputeName] = workspaceFamilyComputeRowItem;
                            }

                            setUsage(container, usage.currentValue, usage.limit, workspaceFamilyComputeRowItem, defaultLimit);

                            // Make family row under workspace row expandable
                            workspaceFamilyRowItem.expandable(true);
                            workspaceFamilyRowItem.expanded(false);
                        }
                    }
                }
                break;
            }
        }
    }

    const firstLevelMap = groupingType === "computeFamily" ? topFamilyMap : topWorkspaceMap;

    const usageHierarchy = new QuotaGridHierarchy(firstLevelMap, secondLevelMap, thirdLevelMap);
    const firstLevelItems = Object.keys(firstLevelMap).map(key => firstLevelMap[key]);

    if (!showCrossLocationComputes()) {
        if (groupingType === "computeFamily") {
            for (const familyRowItem of firstLevelItems) {
                const crossLocationCount = crossLocationComputeCounts[familyRowItem.resourceName];
                if (crossLocationCount > 0) {
                    const crossLocationRowItem = generateBuildTreeItem(
                        "",
                        "",
                        "",
                        QuotaUsageType.CrossLocationComputeInfo,
                        SECOND_LEVEL,
                        familyRowItem.resourceName
                    );

                    const line1 = ClientResources.CrossRegionComputeQuotas.UsageGrid.crossLocationWarningLine1;
                    const line2 = ClientResources.CrossRegionComputeQuotas.UsageGrid.crossLocationWarningLine2;
                    crossLocationRowItem.crossLocationText = Format.create(container, {
                        format: `<div>${line1}</div><div>${line2}</div>`,
                        children: [
                            {
                                htmlTemplate: `<b>${ClientResources.CrossRegionComputeQuotas.UsageGrid.crossLocationWarningCoresFormat.format(
                                    crossLocationCount
                                )}</b>`
                            },
                            {
                                htmlTemplate: `<a href='#' data-bind='click: enableCrossLocation'>${ClientResources.CrossRegionComputeQuotas.UsageGrid.crossLocationEnableText}</a>`,
                                innerViewModel: {
                                    enableCrossLocation: () => {
                                        showCrossLocationComputes(true);
                                    }
                                }
                            }
                        ]
                    });

                    secondLevelMap[familyRowItem.resourceName]["$$$cross-location-compute-info$$$"] = crossLocationRowItem;
                }
            }
        }
    }

    firstLevelItems.sort(sortUsageItems);

    const isDedicated = coresType === QuotaUsageCoresType.Dedicated;
    const coresTotal = isDedicated ? dedicatedCoresTotal : lowPriorityCoresTotal;
    const coresTotalRowItem = generateBuildTreeItem(
        QuotaUsageType.TotalCores,
        ClientResources.quotaTableTotalSubscriptionQuota,
        ClientResources.quotaTableTotalSubscriptionQuota,
        QuotaUsageType.TotalCores,
        FIRST_LEVEL
    );
    setUsageForTotal(coresTotal.currentValue, coresTotal.limit, coresTotalRowItem, defaultLimit, isDedicated);

    firstLevelItems.push(coresTotalRowItem);

    usageHierarchy.items = ko.observable<QuotaRowItem[]>(firstLevelItems);

    return { clustersTotal, coresTotal, usageHierarchy };
}

function generateBuildTreeItem(
    resourceId: string,
    resourceName: string,
    displayName: string,
    resourceType: QuotaUsageType,
    depth: number,
    parentResourceName?: string,
    parentResourceId?: string,
    rootParentResourceName?: string,
    displayNameLink?: string
): QuotaRowItem {
    const treeItem: QuotaRowItem = {
        resourceId,
        resourceName,
        resourceType,
        displayName,
        expandable: ko.observable(false),
        expanded: ko.observable(false),
        depth: ko.observable(depth),
        currentValue: 0,
        currentLimit: 0,
        dedicatedPercent: 0,
        dedicatedPercentColor: "",
        dedicatedPercentLabel: "",
        lowPriorityPercent: 0,
        lowPriorityPercentColor: "",
        lowPriorityPercentLabel: ""
    };

    if (parentResourceName) {
        treeItem.parentResourceName = parentResourceName;
    }

    if (parentResourceId) {
        treeItem.parentResourceId = parentResourceId;
    }

    if (rootParentResourceName) {
        treeItem.rootParentResourceName = rootParentResourceName;
    }

    if (displayNameLink) {
        treeItem.displayNameLink = displayNameLink;
    }

    return treeItem;
}

function calculatePercent(currentValue: number, limit: number): number {
    let percent: number;
    if (currentValue === null || limit === null) {
        percent = 0;
    } else if (limit === 0) {
        percent = -1; // So that this appears last while sorting in the grid
    } else {
        percent = Math.floor((currentValue * 100) / limit);
    }

    return percent;
}

function calculatePercentColor(percent: number, currentValue: number, limit: number): string {
    let percentColor: string;
    if (currentValue === null || limit === null) {
        percentColor = "";
    } else {
        percentColor = percent < 50 ? "i0t1" : percent < 75 ? "a0t1" : "c0t1";
    }

    return percentColor;
}

function setUsage(container: LifetimeManager, currentValue: number, limit: number, quotaRowItem: QuotaRowItem, defaultLimit: number): void {
    if (limit < 0 || limit === null || limit === undefined) {
        limit = defaultLimit;
    }

    const percent = calculatePercent(currentValue, limit);
    const percentColor = calculatePercentColor(percent, currentValue, limit);
    const percentText = Globalization.NumberFormat.create({ style: "percent" }).format(Math.max(percent, 0) / 100);

    if (!quotaRowItem.percentControl) {
        quotaRowItem.percentControl = ProgressBar.create(container, { valuePercentage: ko.observable(percent), ariaLabel: percentText });
    } else {
        quotaRowItem.percentControl.valuePercentage(percent);
    }
    quotaRowItem.percentText = percentText;
    quotaRowItem.percentColor = percentColor;

    quotaRowItem.currentValue = currentValue;
    quotaRowItem.currentLimit = limit;
    quotaRowItem.usageText = ClientResources.CrossRegionComputeQuotas.UsageGrid.coresUsageTextFormat.format(currentValue, limit);
}

function setUsageForTotal(currentValue: number, limit: number, quotaRowItem: QuotaRowItem, defaultLimit: number, isDedicated: boolean): void {
    if (limit < 0 || limit === null || limit === undefined) {
        limit = defaultLimit;
    }
    quotaRowItem.currentValue = currentValue;
    quotaRowItem.currentLimit = limit;

    const usageText = ClientResources.CrossRegionComputeQuotas.UsageGrid.totalCoresTextFormat.format(
        currentValue,
        limit,
        Globalization.NumberFormat.create({
            style: "percent",
            minimumFractionDigits: 2
        }).format(limit === 0 ? 0 : currentValue / limit)
    );

    if (isDedicated) {
        quotaRowItem.totalUsageText = usageText;
    } else {
        quotaRowItem.usageText = usageText;
    }
}

/**
 * Compares usages by looking at its specificity and returns the more generic one.
 * Like total over workspace or workspace over compute.
 *
 * @param u1 First usage to compare.
 * @param u2 Second usage to compare.
 */
function compareUsageSpecificity(u1: QuotaUsage, u2: QuotaUsage): number {
    const usageType1 = getQuotaUsageType(u1.type);
    const usageType2 = getQuotaUsageType(u2.type);

    if ((usageType1 & ComputeQuotaUsageType.Total) > 0) {
        return -1;
    }

    if ((usageType2 & ComputeQuotaUsageType.Total) > 0) {
        return 1;
    }

    if ((usageType1 & ComputeQuotaUsageType.Family) > 0) {
        return -1;
    }

    if ((usageType2 & ComputeQuotaUsageType.Family) > 0) {
        return 1;
    }

    if ((usageType1 & ComputeQuotaUsageType.Workspace) > 0) {
        return -1;
    }

    if ((usageType2 & ComputeQuotaUsageType.Workspace) > 0) {
        return 1;
    }

    if ((usageType1 & ComputeQuotaUsageType.Compute) > 0) {
        return -1;
    }

    if ((usageType2 & ComputeQuotaUsageType.Compute) > 0) {
        return 1;
    }

    return 0;
}

function getWorkspaceDisplayNameLink(subscriptionId: string, resourceGroupName: string, workspaceName: string): string {
    const hostName = MsPortalFx.getEnvironmentValue("trustedParentOrigin") || "https://portal.azure.com";
    const link = `${hostName}/#resource/subscriptions/${subscriptionId}/resourceGroups/${resourceGroupName}/providers/Microsoft.MachineLearningServices/workspaces/${workspaceName}`;
    return link;
}

function getOnlineEndpointDeploymentLink(
    subscriptionId: string,
    resourceGroupName: string,
    workspaceName: string,
    endpointName: string,
    deploymentName: string
): string {
    const hostName = MsPortalFx.getEnvironmentValue("trustedParentOrigin") || "https://portal.azure.com";
    const link = `${hostName}/#resource/subscriptions/${subscriptionId}/resourceGroups/${resourceGroupName}/providers/Microsoft.MachineLearningServices/workspaces/${workspaceName}/onlineEndpoints/${endpointName}/deployments/${deploymentName}`;
    return link;
}

function getComputeDisplayNameLink(
    subscriptionId: string,
    resourceGroupName: string,
    workspaceName: string,
    computeName: string,
    tenantId: string
): string {
    const hostName = MsPortalFx.getEnvironmentValue("webWorkspace2BaseUrl") || "https://ml.azure.com";
    const link = `${hostName}/compute/${computeName}/details?wsid=/subscriptions/${subscriptionId}/resourceGroups/${resourceGroupName}/providers/Microsoft.MachineLearningServices/workspaces/${workspaceName}&tid=${tenantId}`;
    return link;
}
