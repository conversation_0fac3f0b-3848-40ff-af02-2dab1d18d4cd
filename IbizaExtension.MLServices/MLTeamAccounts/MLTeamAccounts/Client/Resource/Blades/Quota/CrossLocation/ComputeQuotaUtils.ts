import * as ClientResources from "Resx/ClientResources";

export const enum ComputeQuotaUsageType {
    None = 0,
    Total = 1,
    Family = 2,
    Workspace = 4,
    Compute = 8,
    Dedicated = 16,
    TotalDedicated = 17,
    LowPriority = 32,
    TotalLowPriority = 33
}

const usageTypes = new Map<string, ComputeQuotaUsageType>();

usageTypes.set("microsoft.machinelearningservices/totalcores/usages", ComputeQuotaUsageType.Total);
usageTypes.set("microsoft.machinelearningservices/totaldedicatedcores/usages", ComputeQuotaUsageType.Total | ComputeQuotaUsageType.Dedicated);
usageTypes.set("microsoft.machinelearningservices/totallowprioritycores/usages", ComputeQuotaUsageType.Total | ComputeQuotaUsageType.LowPriority);
usageTypes.set("microsoft.machinelearningservices/vmfamily/dedicatedcores/usages", ComputeQuotaUsageType.Family | ComputeQuotaUsageType.Dedicated);
usageTypes.set(
    "microsoft.machinelearningservices/vmfamily/lowprioritycores/usages",
    ComputeQuotaUsageType.Family | ComputeQuotaUsageType.LowPriority
);
usageTypes.set(
    "microsoft.machinelearningservices/workspaces/dedicatedcores/usages",
    ComputeQuotaUsageType.Workspace | ComputeQuotaUsageType.Dedicated
);
usageTypes.set(
    "microsoft.machinelearningservices/workspaces/lowprioritycores/usages",
    ComputeQuotaUsageType.Workspace | ComputeQuotaUsageType.LowPriority
);
usageTypes.set(
    "microsoft.machinelearningservices/workspaces/computes/dedicatedcores/usages",
    ComputeQuotaUsageType.Compute | ComputeQuotaUsageType.Dedicated
);
usageTypes.set(
    "microsoft.machinelearningservices/workspaces/computes/lowprioritycores/usages",
    ComputeQuotaUsageType.Compute | ComputeQuotaUsageType.LowPriority
);

export const enum ComputeQuotaSettingType {
    None = 0,
    Family = 1,
    Workspace = 2,
    Dedicated = 4,
    LowPriority = 8,
    FamilyDedicated = 5,
    WorkspaceDedicated = 6
}

const settingTypes = new Map<string, ComputeQuotaSettingType>();

settingTypes.set(
    "microsoft.machinelearningservices/vmfamily/dedicatedcores/quotas",
    ComputeQuotaSettingType.Family | ComputeQuotaSettingType.Dedicated
);
settingTypes.set(
    "microsoft.machinelearningservices/workspaces/dedicatedcores/quotas",
    ComputeQuotaSettingType.Workspace | ComputeQuotaSettingType.Dedicated
);

export function getQuotaUsageType(usageType: string): ComputeQuotaUsageType {
    const usageTypeLower = usageType.toLowerCase();
    if (usageTypes.has(usageTypeLower)) {
        return usageTypes.get(usageTypeLower);
    }

    return ComputeQuotaUsageType.None;
}

export function getQuotaSettingType(settingType: string): ComputeQuotaSettingType {
    const settingTypeLower = settingType.toLowerCase();
    if (settingTypes.has(settingTypeLower)) {
        return settingTypes.get(settingTypeLower);
    }

    return ComputeQuotaSettingType.None;
}

export function getFamilyDisplayName(familyName: string, familyNameFormat = ClientResources.CrossRegionComputeQuotas.UsageGrid.familyNameFormat): string {
    const regexResult = familyName.match(/^standard(.+)family$/i);
    if (regexResult && regexResult.length >= 2 && regexResult[1]) {
        return familyNameFormat.format(regexResult[1].trim());
    }
    return familyName;
}

export function getFullWorkspaceName(resourceGroup: string, workspaceName: string): string {
    return `${resourceGroup}-${workspaceName}`;
}

export function getFullFamilyWorkspaceName(familyName: string, resourceGroup: string, workspaceName: string): string {
    return `${familyName}-${resourceGroup}-${workspaceName}`;
}

export function getFullComputeName(fullWorkspaceName: string, computeName: string): string {
    return `${fullWorkspaceName}-${computeName}`;
}
