import * as TemplateBlade from "Fx/Composition/TemplateBlade";
import * as CustomHtml from "Fx/Controls/CustomHtml";
import * as NumericTextBox from "Fx/Controls/NumericTextBox";
import { LifetimeManager } from "Fx/Lifetime";
import { ArmId } from "Fx/ResourceManagement";

import Utilities from "../../../../../Shared/Utilities";
import {
    QUOTA_POST_FAILURE,
    QUOTA_POST_INSUFFICIENT_PERMISSIONS,
    QUOTA_POST_INVALID_QUOTA_BELOW_CLUSTER_MINIMUM,
    QUOTA_POST_INVALID_QUOTA_EXCEEDS_SUBSCRIPTION_LIMIT,
    QUOTA_POST_INVALID_VMFAMILY_NAME,
    QUOTA_POST_SAVING,
    QUOTA_POST_SUCCESS,
    QUOTA_POST_UNDEFINED,
    QuotaSetting
} from "../../Models/QuotaUsage";
import { VMFamilies, VMSize } from "../../Models/VMSize";
import { QuotaGridHierarchy, QuotaRowItemBase, QuotaSettingRowItem } from "../../QuotaGridHierarchy";
import { BackendQuotaUsageValues, QuotaUsageType, QuotaUtils } from "../../QuotaUtils";
import {
    ComputeQuotaSettingType,
    getFamilyDisplayName,
    getFullFamilyWorkspaceName,
    getFullWorkspaceName,
    getQuotaSettingType
} from "../ComputeQuotaUtils";

import * as ClientResources from "Resx/ClientResources";

import Grid = MsPortalFx.ViewModels.Controls.Lists.Grid;
import ExtensionsOptions = MsPortalFx.ViewModels.Controls.Lists.Grid.ExtensionsOptions;
import NumericValidation = MsPortalFx.ViewModels.NumericValidation;

export class HierarchicalDataNavigator extends MsPortalFx.Data.LocalDataNavigator<QuotaRowItemBase> {
    private _hierarchy: QuotaGridHierarchy;
    private _filterText = "";

    constructor(lifetimeManager: LifetimeManager, hierarchy: QuotaGridHierarchy) {
        super(lifetimeManager, hierarchy.items);
        this._hierarchy = hierarchy;
    }

    public loadBySkipTake(skip: number, take: number, filter: string): Promise<unknown> {
        const filterText = (filter || "").trim();
        if (this._filterText !== filterText) {
            if (filterText) {
                if (filterText.length >= 3) {
                    this._hierarchy.filterSecondLevelItems(filterText);
                }
            } else {
                this._hierarchy.resetFilter();
            }
        }

        this._filterText = filterText;

        return super.loadBySkipTake(skip, take, filter);
    }
}

type HelpTextDetails = {
    unallocatedCore: number;
    subscriptionMax: number;
    placeHolderText: KnockoutObservableBase<string>;
};

interface IQuotaConfigWorkspace {
    armId: ArmId;
    name: string;
    location: string;
    isCrossLocation: boolean;
}

export interface IQuotaConfigurationTreeOptions {
    location: string;
    quotaSettings: QuotaSetting[];
    visible?: boolean | KnockoutObservableBase<boolean>;
    vmSizes: VMSize[];
    workspaces?: IQuotaConfigWorkspace[];
    onError: (errorMessage: string) => void;
    onUpdateQuotaLimit: (settingId: string, settingType: string, newLimit: number, workspaceLocation: string) => Promise<any>;
}

export function create(container: TemplateBlade.Container, options: IQuotaConfigurationTreeOptions): CustomHtml.Contract {
    const { onError, onUpdateQuotaLimit, visible } = options;

    const helpTextNosMap: { [familyName: string]: HelpTextDetails } = {};

    function onUpdateQuota(item: QuotaSettingRowItem): void {
        if (item.newLimit.value() !== null && item.newLimit.valid()) {
            item.status(QUOTA_POST_SAVING);

            onUpdateQuotaLimit(
                item.resourceId,
                BackendQuotaUsageValues.type.workspaceDedicatedQuotas,
                item.newLimit && item.newLimit.value() && parseInt(item.newLimit.value(), 10),
                item.workspaceLocation
            )
                .then(response => {
                    if (!Array.isArray(response)) {
                        if (response === QUOTA_POST_INSUFFICIENT_PERMISSIONS) {
                            onError(getErrorMessage(response));
                        } else {
                            onError(Utilities.extractErrorMessage(response.jqXHR));
                        }
                        item.status(QUOTA_POST_FAILURE);
                        return;
                    }

                    let newItem: QuotaSettingRowItem = null;
                    response.forEach(quotaSetting => {
                        const currentItems = treeItems.quotaHierarchy.items();
                        const newItems = currentItems.map((item: QuotaSettingRowItem) => {
                            if (item.resourceId === quotaSetting.id) {
                                if (quotaSetting.status === QUOTA_POST_SUCCESS) {
                                    // Update the current value and clear the new limit value and set status
                                    item.status(quotaSetting.status);
                                    container.announce(ClientResources.CrossRegionComputeQuotas.quotaUpdateSucceeded);
                                    onError(getErrorMessage(quotaSetting.status));
                                    item.currentLimit(quotaSetting.limit);
                                } else {
                                    item.status(QUOTA_POST_FAILURE);
                                    const errorMessage = getErrorMessage(quotaSetting.status);
                                    container.announce(errorMessage);
                                    onError(errorMessage);
                                }
                                newItem = item;
                            }
                            return item;
                        });

                        treeItems.quotaHierarchy.items(newItems);

                        Object.keys(treeItems.quotaHierarchy._secondLevelItemsMap).forEach(vmFamilyName => {
                            Object.keys(treeItems.quotaHierarchy._secondLevelItemsMap[vmFamilyName]).forEach(workspace => {
                                const quotaSettingRowItem: QuotaSettingRowItem =
                                    treeItems.quotaHierarchy._secondLevelItemsMap[vmFamilyName][workspace];
                                if (newItem !== null && quotaSettingRowItem.resourceId === newItem.resourceId) {
                                    treeItems.quotaHierarchy._secondLevelItemsMap[vmFamilyName][workspace] = newItem;
                                }
                            });
                        });

                        updatePlaceHolderTexts(treeItems.quotaHierarchy, helpTextNosMap, extractVMFamilyNameFromQuotaResourceId(newItem.resourceId));
                    });
                })
                .catch(err => {
                    treeItems.quotaHierarchy.items([]);
                });
        }
    }

    const treeColumns = getTreeColumns();
    const treeItems = buildTreeItems(container, options, onUpdateQuota);
    const dataNavigator = new HierarchicalDataNavigator(container, treeItems.quotaHierarchy);

    const quotaTree = new Grid.ViewModel<QuotaRowItemBase, any>(
        container,
        null,
        Grid.Extensions.Filterable | Grid.Extensions.Hierarchical | Grid.Extensions.Scrollable,
        {
            filterable: {
                serverFilter: ko.observable(true),
                searchBoxCloseButtonVisible: ko.observable(false),
                searchBoxPlaceholder: ko.observable(ClientResources.CrossRegionComputeQuotas.QuotaConfigurationBlade.filterText)
            },
            hierarchical: { hierarchy: treeItems.quotaHierarchy },
            scrollable: { dataNavigator }
        } as ExtensionsOptions<QuotaRowItemBase, any>
    );

    quotaTree.columns(treeColumns);
    quotaTree.showHeader = true;
    quotaTree.noRowsMessage(ClientResources.CrossRegionComputeQuotas.QuotaConfigurationBlade.noItemsText);

    Object.keys(treeItems.quotaHierarchy._firstLevelItemsMap).forEach(familyName => {
        updatePlaceHolderTexts(treeItems.quotaHierarchy, helpTextNosMap, familyName);
    });

    return CustomHtml.create(container, {
        htmlTemplate: `<div data-bind="pcControl: quotaTree"></div>`,
        innerViewModel: { quotaTree },
        visible
    });
}

function getTreeColumns(): Grid.Column[] {
    return [
        {
            itemKey: "displayName",
            width: ko.observable("45%"),
            format: Grid.Format.HtmlBindings,
            formatOptions: {
                htmlBindingsTemplate: `
                <div>
                <!-- ko if: settings.item.resourceType === "VMFamily" -->
                    <div data-bind="text: settings.item.displayName"></div>
                <!-- /ko -->
                <!-- ko if: settings.item.resourceType === "Workspace" -->
                    <div data-bind="html: settings.item.displayName"></div>
                <!-- /ko -->
                </div>`
            },
            name: ko.observable<string>(ClientResources.quotaRequestResourceName),
            hierarchical: true
        },
        {
            itemKey: "currentLimit",
            name: ko.observable<string>(ClientResources.quotaRequestCurrentLimit),
            format: Grid.Format.HtmlBindings,
            formatOptions: {
                htmlBindingsTemplate: `<div data-bind="text: settings.item.currentLimit"></div>`
            },
            width: ko.observable("10%")
        },
        {
            itemKey: "newLimit",
            name: ko.observable<string>(ClientResources.quotaRequestNewLimit),
            format: Grid.Format.HtmlBindings,
            formatOptions: {
                htmlBindingsTemplate: `
                    <div class="ext-ml-quota-container">
                        <!-- ko if: settings.item.resourceType === "Workspace" -->
                            <div class="ext-ml-newlimit" data-bind='pcControl: settings.item.newLimit'></div>
                        <!-- /ko -->

                         <div class="ext-ml-save-icon">
                                <a class="ext-ml" role='button' data-bind="image: MsPortalFx.Base.Images.Save(),
                                    fxclick: settings.item.saveButton(),
                                    visible: settings.item.showSaveButton()"></a>
                         </div>
                    </div>`
            },
            width: ko.observable("40%")
        },
        {
            itemKey: "statusCol",
            name: ko.observable(""),
            format: Grid.Format.HtmlBindings,
            formatOptions: {
                htmlBindingsTemplate: `<div class="ext-ml-status-container">
                    <!-- ko if: settings.item.status() === "success" -->
                            <a class="ext-ml-status-icon"
                            data-bind="image: MsPortalFx.Base.Images.StatusBadge.Success()"></a>
                    <!-- /ko -->
                    <!-- ko if: settings.item.status() === "failure" -->
                            <a class="ext-ml-status-icon"
                                data-bind="image: MsPortalFx.Base.Images.StatusBadge.Error()"></a>
                    <!-- /ko -->
                    <!-- ko if: settings.item.status() === "saving" -->
                        <a class="ext-ml-status-icon"
                            data-bind="image: MsPortalFx.Base.Images.Loading.Loader()"></a>
                    <!-- /ko -->
                    </div>`
            },
            width: ko.observable("5%")
        }
    ];
}

function buildTreeItems(
    container: LifetimeManager,
    options: IQuotaConfigurationTreeOptions,
    onUpdateQuota: (quotaSetting: QuotaSettingRowItem) => void
): {
    quotaHierarchy: QuotaGridHierarchy;
} {
    const { location, quotaSettings, vmSizes, workspaces = [] } = options;

    const sortedWorkspaces = [...workspaces].sort((w1, w2) => MsPortalFx.localeCompareIgnoreCase(w1.name, w2.name));

    const vmFamilies = new VMFamilies(vmSizes);
    const familyMap: { [familyName: string]: QuotaSettingRowItem } = {};
    const workspaceMap: { [familyName: string]: { [workspaceName: string]: QuotaSettingRowItem } } = {};
    const sortedSettings = [...quotaSettings].sort(compareSettingSpecificity);
    const explicitWorkspaceQuotaSettings = new Map<string, number>();

    for (const quotaSetting of sortedSettings) {
        const usageType = QuotaUtils.getQuotaUsageType(quotaSetting);
        const familyName = quotaSetting.name.value;
        if (usageType === QuotaUsageType.VMFamily) {
            if (!vmFamilies.contains(familyName)) {
                // Skip processing this VM Family because its not supported for this location
                continue;
            }

            const displayName = getFamilyDisplayName(familyName);
            if (!familyMap[familyName]) {
                familyMap[familyName] = generateBuildTreeItem(
                    container,
                    quotaSetting.id,
                    familyName,
                    displayName,
                    QuotaUsageType.VMFamily,
                    0,
                    onUpdateQuota,
                    quotaSetting.limit
                );
            }
        } else if (usageType === QuotaUsageType.Workspace) {
            if (familyMap[familyName]) {
                const quotaSettingArmId = ArmId.parse(quotaSetting.id);
                const workspaceName = quotaSettingArmId.resourceIds[0];
                const resourceGroupName = quotaSettingArmId.resourceGroup;
                explicitWorkspaceQuotaSettings.set(
                    getFullFamilyWorkspaceName(familyName, resourceGroupName, workspaceName).toLocaleLowerCase(),
                    quotaSetting.limit || 0
                );
            }
        }
    }

    const quotaHierarchy = new QuotaGridHierarchy(familyMap, workspaceMap, {});
    const firstLevelItems = Object.keys(familyMap).map(key => familyMap[key]);
    firstLevelItems.sort((i1, i2) => i1.displayName.localeCompare(i2.displayName));

    for (const familyRowItem of firstLevelItems) {
        const familyName = familyRowItem.resourceName;
        const familyLimit = familyRowItem.currentLimit();

        for (const workspace of sortedWorkspaces) {
            const workspaceName = workspace.armId.resourceIds[0];
            const resourceGroupName = workspace.armId.resourceGroup;
            const fullWorkspaceName = getFullWorkspaceName(resourceGroupName, workspaceName);
            const quotaKey = getFullFamilyWorkspaceName(familyName, resourceGroupName, workspaceName).toLocaleLowerCase();
            const workspaceQuota = explicitWorkspaceQuotaSettings.has(quotaKey) ? explicitWorkspaceQuotaSettings.get(quotaKey) : familyLimit;

            const quotaSettingId = ArmId.stringify({
                subscription: workspace.armId.subscription,
                resourceGroup: resourceGroupName,
                provider: workspace.armId.provider,
                resourceTypes: ["workspaces", "quotas"],
                resourceIds: [workspaceName, familyName],
                kind: ArmId.Kind.Resource
            });

            let displayName = ClientResources.CrossRegionComputeQuotas.UsageGrid.workspaceDisplayText.format(workspaceName, resourceGroupName);
            if (MsPortalFx.localeCompareIgnoreCase(workspace.location, location) !== 0) {
                // Cross location workspace, add location info
                displayName = ClientResources.CrossRegionComputeQuotas.UsageGrid.workspaceCrossLocationDisplayText.format(
                    workspaceName,
                    resourceGroupName,
                    `<b>${workspace.location}</b>`
                );
            }

            let familyWorkspaceMap = workspaceMap[familyName];
            if (!familyWorkspaceMap) {
                familyWorkspaceMap = {};
                workspaceMap[familyName] = familyWorkspaceMap;
            }

            if (!familyWorkspaceMap[fullWorkspaceName]) {
                const workspaceRowItem = generateBuildTreeItem(
                    container,
                    quotaSettingId,
                    fullWorkspaceName,
                    displayName,
                    QuotaUsageType.Workspace,
                    1,
                    onUpdateQuota,
                    workspaceQuota,
                    "",
                    familyName,
                    familyRowItem.resourceId,
                    familyRowItem.resourceId
                );
                workspaceRowItem.workspaceLocation = workspace.location;
                familyWorkspaceMap[fullWorkspaceName] = workspaceRowItem;
            }

            familyRowItem.expandable(true);
        }
    }

    quotaHierarchy.items = ko.observable<QuotaSettingRowItem[]>(firstLevelItems);

    return { quotaHierarchy };
}

function generateBuildTreeItem(
    container: LifetimeManager,
    resourceId: string,
    resourceName: string,
    displayName: string,
    resourceType: QuotaUsageType,
    depth: number,
    onUpdateQuota: (item: QuotaSettingRowItem) => void,
    currentQuotaLimit?: number,
    placeHolderText?: string,
    parentResourceName?: string,
    parentResourceId?: string,
    rootParentResourceName?: string
): QuotaSettingRowItem {
    const numericValidation = new NumericValidation();
    numericValidation.isEmptyValid = ko.observable(true);
    const quotaSettingItem: QuotaSettingRowItem = {
        resourceId,
        resourceName,
        displayName,
        resourceType,
        depth: ko.observable(depth),
        expandable: ko.observable(false),
        expanded: ko.observable(false),
        newLimit: NumericTextBox.create(container, {
            min: ko.observable(0),
            validations: ko.observableArray([numericValidation]),
            allowEmpty: true,
            ariaLabel: displayName + " " + ClientResources.quotaRequestNewLimit,
            cssClass: `ext-ml-newlimit`,
            decimalPoint: ko.observable(0),
            hideValidationCheck: true,
            onEnterPressed: () => onUpdateQuota(quotaSettingItem),
            placeHolderText: ko.observable("")
        }),
        status: ko.observable(""),
        saveButton: ko.observable(() => onUpdateQuota(quotaSettingItem)),
        showSaveButton: ko.observable(false),
        placeHolderText: ko.observable(placeHolderText || "")
    };

    if (currentQuotaLimit >= 0) {
        quotaSettingItem.currentLimit = ko.observable(currentQuotaLimit);
    }

    if (parentResourceName) {
        quotaSettingItem.parentResourceName = parentResourceName;
    }

    if (parentResourceId) {
        quotaSettingItem.parentResourceId = parentResourceId;
    }

    if (rootParentResourceName) {
        quotaSettingItem.rootParentResourceName = rootParentResourceName;
    }

    quotaSettingItem.newLimit.value.subscribeAndRun(container, newValue => {
        quotaSettingItem.status("");
        quotaSettingItem.showSaveButton(false);
        if (newValue !== null && (newValue === 0 || newValue.toString().length > 0)) {
            quotaSettingItem.showSaveButton(true);
        }
    });

    return quotaSettingItem;
}

function getErrorMessage(quotaStatusCode: string): string {
    const lowerCaseQuotaStatusCode = quotaStatusCode.toLowerCase();
    switch (lowerCaseQuotaStatusCode) {
        case QUOTA_POST_INVALID_QUOTA_BELOW_CLUSTER_MINIMUM:
            return ClientResources.quotaWorkspaceQuotaLessThanMinimumClusterCores;
        case QUOTA_POST_INVALID_QUOTA_EXCEEDS_SUBSCRIPTION_LIMIT:
            return ClientResources.quotaWorkspaceQuotaExceedSubscriptionLimit;
        case QUOTA_POST_INVALID_VMFAMILY_NAME:
            return ClientResources.quotaWorkspaceQuotaInvalidVMFamilyName;
        case QUOTA_POST_INSUFFICIENT_PERMISSIONS:
            return ClientResources.quotaWorkspaceQuotaInsufficientPermissions;
        case QUOTA_POST_FAILURE:
        case QUOTA_POST_UNDEFINED:
            return ClientResources.quotaWorkspaceQuotaUnknownError;
        default:
            return "";
    }
}

function updatePlaceHolderTexts(
    quotaHierarchy: QuotaGridHierarchy,
    helpTextNosMap: { [familyName: string]: HelpTextDetails },
    coreVMFamilyName: string
): void {
    const workspacesMap = quotaHierarchy._secondLevelItemsMap[coreVMFamilyName];

    if (workspacesMap) {
        let totalUsedCores = 0;
        let subMax = 0;
        let placeHolderText = "";
        if (!helpTextNosMap[coreVMFamilyName]) {
            const familyMap = quotaHierarchy._firstLevelItemsMap;
            const familyRowItem = familyMap[coreVMFamilyName] as QuotaSettingRowItem;
            placeHolderText = ClientResources.quotaWorkspaceQuotaPlaceHolder.format(0, (familyRowItem && familyRowItem.currentLimit()) || 0);
            helpTextNosMap[coreVMFamilyName] = {
                unallocatedCore: 0,
                subscriptionMax: (familyRowItem && familyRowItem.currentLimit()) || 0,
                placeHolderText: ko.observable(placeHolderText)
            };
        }
        subMax = helpTextNosMap[coreVMFamilyName].subscriptionMax;
        totalUsedCores = 0;
        Object.keys(quotaHierarchy._secondLevelItemsMap[coreVMFamilyName]).forEach(workspaceKey => {
            const workspaceRowItem = quotaHierarchy._secondLevelItemsMap[coreVMFamilyName][workspaceKey] as QuotaSettingRowItem;
            totalUsedCores = totalUsedCores + workspaceRowItem.currentLimit();
        });
        Object.keys(quotaHierarchy._secondLevelItemsMap[coreVMFamilyName]).forEach(workspaceKey => {
            const workspaceRowItem = quotaHierarchy._secondLevelItemsMap[coreVMFamilyName][workspaceKey] as QuotaSettingRowItem;
            const unAllocatedCores = subMax - totalUsedCores > 0 ? subMax - totalUsedCores : 0;
            placeHolderText = ClientResources.quotaWorkspaceQuotaPlaceHolder.format(unAllocatedCores, subMax);
            helpTextNosMap[coreVMFamilyName]["unallocatedCore"] = unAllocatedCores;
            helpTextNosMap[coreVMFamilyName]["placeHolderText"](placeHolderText);
            workspaceRowItem.newLimit.placeHolderText(placeHolderText);
        });
    }
    const newItems = quotaHierarchy.items().map((item: QuotaSettingRowItem) => {
        if (item.resourceType === QuotaUsageType.Workspace) {
            const familyRowItem = item.parentResourceName;
            if (helpTextNosMap[familyRowItem]) {
                item.newLimit.placeHolderText(helpTextNosMap[familyRowItem]["placeHolderText"]());
            }
        }
        return item;
    });

    quotaHierarchy.items(newItems);
}

function extractVMFamilyNameFromQuotaResourceId(id: string): string {
    const vmFamily = id && id !== null ? id.match(BackendQuotaUsageValues.idRegExp.vmFamilyQuotas) : null;
    return vmFamily !== null ? vmFamily[0].substr(vmFamily[0].lastIndexOf("/") + 1) : "";
}

/**
 * Compares quota settings by looking at its specificity and returns the more generic one.
 * Like vm family over workspace
 *
 * @param s1 First setting to compare.
 * @param s2 Second setting to compare.
 */
function compareSettingSpecificity(s1: QuotaSetting, s2: QuotaSetting): number {
    const settingType1 = getQuotaSettingType(s1.type);
    const settingType2 = getQuotaSettingType(s2.type);

    if ((settingType1 & ComputeQuotaSettingType.Family) > 0) {
        return -1;
    }

    if ((settingType2 & ComputeQuotaSettingType.Family) > 0) {
        return 1;
    }

    return MsPortalFx.localeCompareIgnoreCase(s1.type, s2.type);
}
