import * as CustomHtml from "Fx/Controls/CustomHtml";

export interface ITotalQuotaUsagesTextOptions {
    initiallyVisible?: boolean;
    totalLabel: string | KnockoutObservableBase<string>;
    totalText: string | KnockoutObservableBase<string>;
}

export function create(container: MsPortalFx.Base.LifetimeManager, options: ITotalQuotaUsagesTextOptions): CustomHtml.Contract {
    const { initiallyVisible, totalLabel, totalText } = options;

    return CustomHtml.create(container, {
        htmlTemplate: `
            <div class="ext-ml-quota-view-total-cores">
                <div class="ext-ml-quota-view-total-cores-label" data-bind="text: totalLabel"></div>
                <div class="ext-ml-quota-view-total-cores-text" data-bind="text: totalText"></div>
            </div>
        `,
        innerViewModel: { totalLabel, totalText },
        visible: ko.observable(!!initiallyVisible)
    });
}
