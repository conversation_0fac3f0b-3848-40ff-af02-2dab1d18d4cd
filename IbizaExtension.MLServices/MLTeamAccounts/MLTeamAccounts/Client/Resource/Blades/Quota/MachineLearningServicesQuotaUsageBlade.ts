﻿import * as TemplateBlade from "Fx/Composition/TemplateBlade";
import * as ClientResources from "Resx/ClientResources";
import * as DropDown from "Fx/Controls/DropDown";
import * as TextBox from "Fx/Controls/TextBox";
import * as Button from "Fx/Controls/Button";
import * as SubscriptionsDropDown from "Fx/Controls/SubscriptionDropDown";

import { DataContext } from "../../ResourceArea";
import { RefreshCommandButton } from "../../../Shared/CommandButtons";
import Constants = require("../../../Shared/Constants");
import Utilities from "../../../Shared/Utilities";
import { QuotaUsage, QuotaSetting, QUOTA_POST_SUCCESS, QUOTA_POST_INVALID_QUOTA_BELOW_CLUSTER_MINIMUM, QUOTA_POST_INVALID_VMFAMILY_NAME, QUOTA_POST_UNDEFINED, QUOTA_POST_FAILURE, QUOTA_POST_INVALID_QUOTA_EXCEEDS_SUBSCRIPTION_LIMIT, QUOTA_POST_INSUFFICIENT_PERMISSIONS, QUOTA_POST_SAVING } from "./Models/QuotaUsage";
import { MachineLearningServicesQuotaUsageBladeParameters } from "./MachineLearningServicesQuotaUsageBladeParameters"
import { getFamilyDisplayName } from "./CrossLocation/ComputeQuotaUtils";

import Toolbars = MsPortalFx.ViewModels.Toolbars;
import Grid = MsPortalFx.ViewModels.Controls.Lists.Grid;
import ExtensionsOptions = MsPortalFx.ViewModels.Controls.Lists.Grid.ExtensionsOptions;
import RequiredValidation = MsPortalFx.ViewModels.RequiredValidation;
import NumericValidation = MsPortalFx.ViewModels.NumericValidation;
import * as NumericTextBox from "Fx/Controls/NumericTextBox";
import * as CustomHtml from "Fx/Controls/CustomHtml";
import * as InfoBox from "Fx/Controls/InfoBox";
import * as Sections from "Fx/Controls/Section";
import * as Tabs from "Fx/Controls/TabControl";
import { QuotaGridHierarchy, QuotaRowItem, FIRST_LEVEL, SECOND_LEVEL, THIRD_LEVEL, QuotaSettingRowItem, QuotaRowItemBase } from "./QuotaGridHierarchy";
import { QuotaUtils, QuotaUsageType, BackendQuotaUsageValues } from "./QuotaUtils";
import { VMSize } from "./Models/VMSize";

const globalRateLimit = 500;

@TemplateBlade.Decorator({
    htmlTemplate:
        `
    <div class="ext-ml-quota">
        <div>
            <div class="ext-ml-infobox" data-bind="pcControl: subscriptionsDropDown"></div>
            <div class="ext-ml-infobox" data-bind="pcControl: resourceTextBox"></div>
            <div class="ext-ml-infobox" data-bind="pcControl: locationsDropDown"></div>
        </div>
        <div data-bind="pcControl: tabControl"></div>
        <div data-bind="pcControl: requestQuotaButton" class="ext-ml-quota-btn"></div>
    </div>
    `,
    styleSheets: ["../../../Shared/Styles/CustomStyles.css", "./MachineLearningServicesQuotaUsageBlade.css"]
})
@TemplateBlade.InjectableModel.Decorator(DataContext)
export class MachineLearningServicesQuotaUsageBlade {
    public title = ClientResources.quotaBladeTitle;
    public subtitle = ClientResources.quotaBladeTitle;
    public context: TemplateBlade.Context<MachineLearningServicesQuotaUsageBladeParameters, DataContext>;

    public subscriptionsDropDown: SubscriptionsDropDown.Contract;
    public resourceTextBox: TextBox.Contract;
    public locationsDropDown: DropDown.ViewModel<string>;
    private _vmsHGrid: Grid.ViewModel<QuotaRowItemBase, any>;
    private _wsHGrid: Grid.ViewModel<QuotaRowItemBase, any>;
    private _quotaSettingGrid: Grid.ViewModel<QuotaRowItemBase, any>;
    private _quotaPostErrorBox: InfoBox.Contract;
    private _quotaErrorMessage: KnockoutObservableBase<string>;
    private _quotaSubscriptionViewHelpBox: CustomHtml.Contract;
    private _quotaWorkspaceViewHelpBox: CustomHtml.Contract;
    private _quotaSettingHelpBox: CustomHtml.Contract;
    private _quotaErrorMessageVisible: KnockoutObservableBase<boolean>;
    public tabControl: Tabs.Contract;
    public requestQuotaButton: Button.Contract;
    private _totalLowPrioClusterLimit: number = 0;
    private _vmGridHierarchy: QuotaGridHierarchy;
    private _wsGridHierarchy: QuotaGridHierarchy;
    private _quotaSettingHierarchy: QuotaGridHierarchy;
    private _supportedVMFamilySizesMap: { [publicSKUNameLowerCased: string]: string[] } = {};
    private _workspaceQuotaSettingSection: Sections.Contract;

    private _vmsGridVMFamilyMap: { [vmFamilyName: string]: QuotaRowItem } = {};
    private _vmsGridVMFamilyWorkspaceMap: { [vmFamilyName: string]: { [workspaceName: string]: QuotaRowItem } } = {};
    private _vmsGridVMFamilyWorkspaceClusterMap: { [vmFamilyName: string]: { [workspaceName: string]: { [clusterName: string]: QuotaRowItem } } } = {};

    private _wsGridWorkspaceMap: { [workspaceName: string]: QuotaRowItem } = {};
    private _wsGridWorkspaceVMFamilyMap: { [workspaceName: string]: { [vmFamilyName: string]: QuotaRowItem } } = {};
    private _wsGridWorkspaceVMFamilyClusterMap: { [workspaceName: string]: { [vmFamilyName: string]: { [clusterName: string]: QuotaRowItem } } } = {};

    private _qsGridVMFamilyMap: { [vmFamilyName: string]: QuotaSettingRowItem } = {};
    private _qsGridVMFamilyWorkspaceMap: { [vmFamilyName: string]: { [workspaceName: string]: QuotaSettingRowItem } } = {};
    private _qsGridVMFamilyHelpTextNosMap: { [vmFamilyName: string]: { unallocatedCore: number, subscriptionMax: number, placeHolderText: KnockoutObservableBase<string> } } = {};

    private _subscriptionId = ko.pureComputed(() => {
        const sub = this.subscriptionsDropDown.value();
        return sub && sub.subscriptionId;
    });

    private _location = ko.pureComputed(() => this.locationsDropDown.value());

    public onInitialize() {
        this.context.container.icon(MsPortalFx.Base.Images.Polychromatic.ControlsHorizontal());

        this._initializeCommandBar();
        this._initializeSubscriptionsDropdown();
        this._initializeResourceTextBox();
        this._initializeLocationsDropdown();
        this._initializeUsagesGrids();
        this._initializeQuotaSettingGrid();
        this._initializeTabs();
        this._initializeRequestQuotaButton();


        const deferred = Q.defer<any>();
        this.subscriptionsDropDown.value.fxOnceAndRun(this.context.container, () => {
            this._fetchLocations().then(() => {
                this._fetchQuota().then(() => {
                    this.subscriptionsDropDown.value.extend({ rateLimit: globalRateLimit }).subscribe(this.context.container, this._fetchQuota.bind(this));
                    this.locationsDropDown.value.extend({ rateLimit: globalRateLimit }).subscribe(this.context.container, this._fetchQuota.bind(this));
                    deferred.resolve();
                });
            });
        });

        return deferred.promise;
    }

    private _initializeCommandBar() {
        const { container } = this.context;
        const commandBar = new Toolbars.Toolbar(container);

        const refreshButton = new RefreshCommandButton(
            () => this._fetchQuota()
        );

        commandBar.setItems([refreshButton]);
        container.commandBar = commandBar;
    }

    private _initializeSubscriptionsDropdown() {
        const { container, model, parameters } = this.context;

        const subscriptionsDropDownOptions: SubscriptionsDropDown.Options = {
            initialSubscriptionId: ko.observableArray([parameters.subscriptionId]),
            validations: ko.observableArray([
                new MsPortalFx.ViewModels.RequiredValidation(ClientResources.selectSubscription)
            ]),
            resourceProviders: ko.observable([Constants.machineLearningServicesResourcesProvider])
        };

        this.subscriptionsDropDown = SubscriptionsDropDown.create(container, subscriptionsDropDownOptions);
    }

    private _initializeResourceTextBox() {
        const { container } = this.context;

        this.resourceTextBox = TextBox.create(container, {
            label: ClientResources.labelResource,
            ariaLabel: ClientResources.labelResource,
            value: ClientResources.machineLearningCompute,
            disabled: true
        });
    }

    private _initializeLocationsDropdown() {
        const { container } = this.context;

        this.locationsDropDown = new DropDown.ViewModel<string>(container, {
            label: ko.observable(ClientResources.resourceLocationColumn),
            items: ko.observableArray([]),
            validations: ko.observableArray([
                new RequiredValidation(ClientResources.selectLocation)
            ])
        });
    }

    private _fetchLocations(): Q.Promise<any> {
        const { model, parameters } = this.context;

        return Q.all([
            model.machineLearningServicesData.getSubscriptionLocations(this._subscriptionId()),
            model.machineLearningServicesData.getComputeAvailableLocations(this._subscriptionId())
        ]).then((responses: any[]) => {
            const locationObjs = (responses[0] && responses[0].value) || [];
            const availableLocations = responses[1] || [];

            let availableLocationObjs = locationObjs.filter((locationObj: any) => availableLocations.indexOf(locationObj.displayName) !== -1);
            if (!availableLocationObjs || availableLocationObjs.length === 0) {
                availableLocationObjs = locationObjs;
            }

            const locationDropDownItems: DropDown.Item<string>[] = availableLocationObjs.map((locationObj: any) => {
                return {
                    text: ko.observable(locationObj.displayName),
                    value: locationObj.name
                };
            });

            let location: string;
            if (parameters.location) {
                location = parameters.location;
            }

            if (location) {
                if (MsPortalFx.find(locationDropDownItems, (item: DropDown.Item<string>) => item.value === location)) {
                    this.locationsDropDown.value(location);
                } else {
                    locationDropDownItems.push({
                        text: ko.observable(location),
                        value: location
                    });
                    this.locationsDropDown.value(location);
                }
            } else {
                this.locationsDropDown.value(null);
            }

            locationDropDownItems.sort((a: DropDown.Item<string>, b: DropDown.Item<string>) => a.value.localeCompare(b.value));
            this.locationsDropDown.items(locationDropDownItems);
        }).catch(() => {
            this.locationsDropDown.items([]);
            this.locationsDropDown.value(null);
        });
    }

    private _initializeUsagesGrids() {
        const { container } = this.context;

        var columns: Grid.Column[] = [
            {
                itemKey: "displayName",
                name: ko.observable(ClientResources.quotaTableHeaderQuota),
                width: ko.observable("30%"),
                hierarchical: true,
                format: Grid.Format.HtmlBindings,
                formatOptions: {
                    htmlBindingsTemplate: `
                        <div>
                            <!-- ko if: settings.item.resourceType === "VMFamily" -->
                                <div data-bind="text: settings.item.displayName"></div>
                            <!-- /ko -->
                            <!-- ko if: settings.item.resourceType === "Workspace" -->
                                <a data-bind="text: settings.item.displayName, attr: { href: settings.item.displayNameLink, title: settings.item.displayNameLink }" target="_blank" class="ext-ml-workspace-link"></a>
                            <!-- /ko -->
                            <!-- ko if: settings.item.resourceType === "Compute" -->
                                <a data-bind="text: settings.item.displayName, attr: { href: settings.item.displayNameLink, title: settings.item.displayNameLink }" target="_blank" class="ext-ml-compute-link"></a>
                            <!-- /ko -->
                            <!-- ko if: settings.item.resourceType === "TotalCores" -->
                                <div class="ext-ml-totalcores-name" data-bind="text: settings.item.displayName"></div>
                            <!-- /ko -->
                        </div>`
                }
            },
            {
                itemKey: "dedicatedPercent",
                name: ko.observable(ClientResources.quotaTableHeaderDedicated),
                width: ko.observable("35%"),
                format: Grid.Format.PercentageBar,
                formatOptions: {
                    percentageBar: {
                        barColorKey: "dedicatedPercentColor",
                        textKey: "dedicatedPercentLabel",
                        textLocation: Grid.PercentageBarTextLocation.Above,
                        backgroundColor: "k2"
                    }
                }
            },
            {
                itemKey: "lowPriorityPercent",
                name: ko.observable(ClientResources.quotaTableHeaderLowPriority),
                width: ko.observable("35%"),
                format: Grid.Format.PercentageBar,
                formatOptions: {
                    percentageBar: {
                        barColorKey: "lowPriorityPercentColor",
                        textKey: "lowPriorityPercentLabel",
                        textLocation: Grid.PercentageBarTextLocation.Above,
                        backgroundColor: "k2"
                    }
                }
            }
        ];
        this._vmGridHierarchy = new QuotaGridHierarchy(this._vmsGridVMFamilyMap, this._vmsGridVMFamilyWorkspaceMap, this._vmsGridVMFamilyWorkspaceClusterMap);
        this._vmGridHierarchy.items = ko.observable<QuotaRowItem[]>([]);
        // Please see this stack overflow Q&A for more info on Hierarchical Grids in Ibiza
        // https://stackoverflow.microsoft.com/questions/127763/how-to-implement-hierarchical-grid
        const vmGridItems = MsPortalFx.thunkArray(container, this._vmGridHierarchy.items);

        this._wsGridHierarchy = new QuotaGridHierarchy(this._wsGridWorkspaceMap, this._wsGridWorkspaceVMFamilyMap, this._wsGridWorkspaceVMFamilyClusterMap);
        this._wsGridHierarchy.items = ko.observable<QuotaRowItem[]>([]);

        const wsGridItems = MsPortalFx.thunkArray(container, this._wsGridHierarchy.items);


        this._vmsHGrid = new Grid.ViewModel<QuotaRowItemBase, any>(container, vmGridItems, Grid.Extensions.Hierarchical, <ExtensionsOptions<QuotaRowItemBase, any>>{
            hierarchical: {
                hierarchy: this._vmGridHierarchy,
            },
        });

        this._wsHGrid = new Grid.ViewModel<QuotaRowItemBase, any>(container, wsGridItems, Grid.Extensions.Hierarchical, <ExtensionsOptions<QuotaRowItemBase, any>>{
            hierarchical: {
                hierarchy: this._wsGridHierarchy,
            },
        });

        this._vmsHGrid.columns = ko.observableArray<Grid.Column>(columns);
        this._vmsHGrid.showHeader = true;
        this._vmsHGrid.noRowsMessage(ClientResources.quotaTableNoData);

        this._wsHGrid.columns = ko.observableArray<Grid.Column>(columns);
        this._wsHGrid.showHeader = true;
        this._wsHGrid.noRowsMessage(ClientResources.quotaTableNoData);

    }

    private _initializeQuotaSettingGrid() {
        const { container } = this.context;

        const quotaSettingColumns: Grid.Column[] = [
            {
                itemKey: "displayName",
                width: ko.observable("45%"),
                format: Grid.Format.HtmlBindings,
                formatOptions: {
                    htmlBindingsTemplate: `<div data-bind="text: settings.item.displayName"></div>`
                },
                name: ko.observable<string>(ClientResources.quotaRequestResourceName),
                hierarchical: true
            },
            {
                itemKey: "currentLimit",
                name: ko.observable<string>(ClientResources.quotaRequestCurrentLimit),
                format: Grid.Format.HtmlBindings,
                formatOptions: {
                    htmlBindingsTemplate: `<div data-bind="text: settings.item.currentLimit"></div>`
                },
                width: ko.observable("10%")
            },
            {
                itemKey: "newLimit",
                name: ko.observable<string>(ClientResources.quotaRequestNewLimit),
                format: Grid.Format.HtmlBindings,
                formatOptions: {
                    htmlBindingsTemplate: `
                        <div class="ext-ml-quota-container">
                            <!-- ko if: settings.item.resourceType === "Workspace" -->
                                <div class="ext-ml-newlimit" data-bind='pcControl: settings.item.newLimit'></div>
                            <!-- /ko -->

                             <div class="ext-ml-save-icon">
                                    <a class="ext-ml" role='button' data-bind="image: MsPortalFx.Base.Images.Save(),
                                        fxclick: settings.item.saveButton(),
                                        visible: settings.item.showSaveButton()"></a>
                             </div>
                        </div>`
                },
                width: ko.observable("25%")
            },
            {
                itemKey: "statusCol",
                name: ko.observable(""),
                format: Grid.Format.HtmlBindings,
                formatOptions: {
                    htmlBindingsTemplate:
                        `<div class="ext-ml-status-container">
                        <!-- ko if: settings.item.status() === "success" -->
                                <a class="ext-ml-success-icon"
                                data-bind="image: MsPortalFx.Base.Images.StatusBadge.Success()"></a>
                        <!-- /ko -->
                        <!-- ko if: settings.item.status() === "failure" -->
                                <a class="ext-ml-success-icon"
                                    data-bind="image: MsPortalFx.Base.Images.StatusBadge.Error()"></a>
                        <!-- /ko -->
                        <!-- ko if: settings.item.status() === "saving" -->
                            <a class="ext-ml-success-icon"
                                data-bind="image: MsPortalFx.Base.Images.Loading.Loader()"></a>
                        <!-- /ko -->
                        </div>`
                },
                width: ko.observable("15%")
            }
        ];
        this._quotaSettingHierarchy = new QuotaGridHierarchy(this._qsGridVMFamilyMap, this._qsGridVMFamilyWorkspaceMap, {});
        this._quotaSettingHierarchy.items = ko.observable<QuotaSettingRowItem[]>([]);
        // Please see this stack overflow Q&A for more info on Hierarchical Grids in Ibiza
        // https://stackoverflow.microsoft.com/questions/127763/how-to-implement-hierarchical-grid
        const quotaSettingItems = MsPortalFx.thunkArray(container, this._quotaSettingHierarchy.items);

        this._quotaSettingGrid = new Grid.ViewModel<QuotaRowItemBase, any>(container, quotaSettingItems, Grid.Extensions.Hierarchical, <ExtensionsOptions<QuotaRowItemBase, any>>{
            hierarchical: {
                hierarchy: this._quotaSettingHierarchy,
            },
        });

        this._quotaSettingGrid.columns(quotaSettingColumns);
        this._quotaSettingGrid.showHeader = true;
        this._quotaSettingGrid.noRowsMessage(ClientResources.quotaTableNoData);

        this._quotaSettingHelpBox = CustomHtml.create(container,
            {
                innerViewModel: {
                    noteText: ClientResources.quotaNote,
                    newLimitText: "1) " + ClientResources.quotaWorkspaceQuotaNewLimitHelpText,
                    resourceNameText: "2) " + ClientResources.quotaWorkspaceQuotaResourceNameHelpText,

                },
                htmlTemplate: "<div> <div data-bind='html: noteText' style='padding: 5px; margin-top: 5px'></div> <div data-bind='html: newLimitText' style='padding: 5px; margin-top: 2px'></div><div data-bind='html: resourceNameText' style='padding: 5px; margin-top: 2px'></div>  </div>",
            });

        this._quotaErrorMessage = ko.observable("");
        this._quotaErrorMessageVisible = ko.observable(false);
        this._quotaPostErrorBox = InfoBox.create(container, {
            style: ko.observable(InfoBox.Style.Error),
            text: this._quotaErrorMessage,
            visible: this._quotaErrorMessageVisible
        });
    }

    private _initializeTabs() {
        const { container } = this.context;

        this._quotaSubscriptionViewHelpBox = CustomHtml.create(container,
            {
                innerViewModel: {
                    noteText: ClientResources.quotaNote,
                    resourceNameText: "1) " + ClientResources.quotaSubscriptionViewResourceNameHelptext,
                    clusterText: "2) " + ClientResources.quotaSubscriptionViewClusterHelptext,
                    usageText: "3) " + ClientResources.quotaSubscriptionViewUsageHelptext
                },
                htmlTemplate: "<div>  <div data-bind='html: noteText' style='padding: 5px; margin-top: 5px'></div> <div data-bind='html: resourceNameText' style='padding: 5px; margin-top: 2px'></div>  <div data-bind='html: clusterText' style='padding: 5px; margin-top: 2px'></div> <div data-bind='html: usageText' style='padding: 5px; margin-top: 2px'></div> </div>"
            });
        const subscriptionViewSection = Sections.create(container, {
            children: [this._vmsHGrid, this._quotaSubscriptionViewHelpBox],
            name: ClientResources.quotaSubscriptionViewTabHeader
        });

        this._quotaWorkspaceViewHelpBox = CustomHtml.create(container, {
            innerViewModel: {
                noteText: ClientResources.quotaNote,
                resourceNameText: "1) " + ClientResources.quotaWorkspaceViewResourceNameHelptext,
                clusterText: "2) " + ClientResources.quotaWorkspaceViewClusterHelptext,
                usageText: "3) " + ClientResources.quotaWorkspaceViewUsageHelptext

            },
            htmlTemplate: "<div> <div data-bind='html: noteText' style='padding: 5px; margin-top: 5px'></div>  <div data-bind='html: resourceNameText' style='padding: 5px; margin-top: 2px'></div> <div data-bind='html: clusterText' style='padding: 5px; margin-top: 2px'></div> <div data-bind='html: usageText' style='padding: 5px; margin-top: 2px'></div> </div>"
        });
        const workspaceViewSection = Sections.create(container, {
            children: [this._wsHGrid, this._quotaWorkspaceViewHelpBox],
            name: ClientResources.quotaWorkspaceViewTabHeader
        });

        this.tabControl = Tabs.create(container, {
            tabs: [subscriptionViewSection, workspaceViewSection],
            cssClass: "msportalfx-form",
        });
    }

    private _initializeRequestQuotaButton(): void {
        const { container, parameters } = this.context;

        this.requestQuotaButton = Button.create(container, <Button.Options>{
            text: ClientResources.labelRequestQuota,
            onClick: () => {
                const bladeReference = Utilities.getQuotaSupportRequestBladeReference(this._subscriptionId());
                container.openBlade(bladeReference);
            }
        });
    }

    private _fetchQuota(): Q.Promise<any> {
        const { model, parameters, container } = this.context;

        if (!this._subscriptionId()) {
            this.subscriptionsDropDown.triggerValidation();
            return Q();
        }

        if (!this._location()) {
            this.locationsDropDown.triggerValidation();
            return Q();
        }

        this._vmsHGrid.loading(true);
        this._wsHGrid.loading(true);
        this._quotaSettingGrid.loading(true);

        this._setErrorMessageBox(QUOTA_POST_SUCCESS);

        return Q.all([
            model.machineLearningServicesData.getSupportedVMSizes(this._subscriptionId(), this._location()),
            model.machineLearningServicesData.getQuotaUsage(this._subscriptionId(), this._location(), true),
            model.machineLearningServicesData.getQuotas(this._subscriptionId(), this._location()),
            MsPortalFx.Azure.getSubscriptionInfo(this._subscriptionId())
        ]).then((responses: any) => {
            const vmSizesResult = responses[0] && responses[0].amlCompute && Array.isArray(responses[0].amlCompute) ? responses[0].amlCompute : [];
            let usagesResponse = responses[1] ? responses[1] : [];
            const getQuotasResult = responses[2] && Array.isArray(responses[2]) ? responses[2] : null;
            const tenantId = responses[3] && responses[3].tenantId;

            this._supportedVMFamilySizesMap = {};
            vmSizesResult.forEach((vmSize: VMSize) => {
                const vmFamilyNameLowerCased = vmSize.family.toLowerCase();
                const vmSizeName = vmSize.name;
                if (!this._supportedVMFamilySizesMap[vmFamilyNameLowerCased]) {
                    this._supportedVMFamilySizesMap[vmFamilyNameLowerCased] = [];
                } else {
                    this._supportedVMFamilySizesMap[vmFamilyNameLowerCased].push(vmSizeName);
                }
            });

            let quotaGridItemsResult = null;
            let vmGridItems: QuotaRowItem[] = [];
            let wsGridItems: QuotaRowItem[] = [];
            if (Array.isArray(usagesResponse) && usagesResponse.length >= 1) {
                usagesResponse.forEach((usage: QuotaUsage) => {
                    const quotaResourceType = QuotaUtils.getQuotaUsageType(usage);
                    if (quotaResourceType === QuotaUsageType.TotalLowPriorityCores) {
                        this._totalLowPrioClusterLimit = usage.limit;
                    }
                });

                quotaGridItemsResult = this._getQuotaGridRowItems(usagesResponse, tenantId);
                vmGridItems = Array.isArray(quotaGridItemsResult) && quotaGridItemsResult.length === 2 && quotaGridItemsResult[0] && Array.isArray(quotaGridItemsResult[0]) ? quotaGridItemsResult[0] : [];
                wsGridItems = Array.isArray(quotaGridItemsResult) && quotaGridItemsResult.length === 2 && quotaGridItemsResult[1] && Array.isArray(quotaGridItemsResult[1]) ? quotaGridItemsResult[1] : [];
            }

            this._vmGridHierarchy._firstLevelItemsMap = this._vmsGridVMFamilyMap;
            this._vmGridHierarchy._secondLevelItemsMap = this._vmsGridVMFamilyWorkspaceMap;
            this._vmGridHierarchy._thirdLevelItemsMap = this._vmsGridVMFamilyWorkspaceClusterMap;
            this._vmGridHierarchy.items(vmGridItems);

            this._wsGridHierarchy._firstLevelItemsMap = this._wsGridWorkspaceMap;
            this._wsGridHierarchy._secondLevelItemsMap = this._wsGridWorkspaceVMFamilyMap;
            this._wsGridHierarchy._thirdLevelItemsMap = this._wsGridWorkspaceVMFamilyClusterMap;
            this._wsGridHierarchy.items(wsGridItems);

            if (this._vmsHGrid.items().length === 0) {
                this._vmsHGrid.noRowsMessage(ClientResources.quotaTableNoData);
            }

            if (this._wsHGrid.items().length === 0) {
                this._wsHGrid.noRowsMessage(ClientResources.quotaTableNoData);
            }

            // Parse get quotas response
            let quotaSettingsItems: QuotaSettingRowItem[] = [];
            if (getQuotasResult !== null) {
                quotaSettingsItems = this._getQuotaSettingGridItems(getQuotasResult);
                /// TODO : Remove this conditional add of workspace quota tab after backend available in all regions
                if (this.tabControl && this.tabControl.tabs && this.tabControl.tabs().length == 2) {
                    this._workspaceQuotaSettingSection = Sections.create(container, {
                        children: [this._quotaPostErrorBox, this._quotaSettingGrid, this._quotaSettingHelpBox],
                        name: ClientResources.quotaQuotaSettingTabHeader,
                        visible: ko.observable(false)
                    })
                    this.tabControl.tabs().push(this._workspaceQuotaSettingSection);
                }
            }

            const workspaceResourceId = parameters.resourceId;
            model.machineLearningServicesData.getWorkspace(workspaceResourceId).then((response) => {
                this._workspaceQuotaSettingSection.visible(true);
            }).catch(() => {
                this._workspaceQuotaSettingSection.visible(false);
            });

            this._quotaSettingHierarchy._firstLevelItemsMap = this._qsGridVMFamilyMap;
            this._quotaSettingHierarchy._secondLevelItemsMap = this._qsGridVMFamilyWorkspaceMap;
            this._quotaSettingHierarchy._thirdLevelItemsMap = {};
            this._quotaSettingHierarchy.items(quotaSettingsItems);
            Object.keys(this._qsGridVMFamilyMap).forEach((coreVMFamilyName) => {
                this._updatePlaceHolderTexts(coreVMFamilyName);
            })
            if (this._quotaSettingHierarchy.items().length === 0) {
                this._quotaSettingGrid.noRowsMessage(ClientResources.quotaTableNoData);
            }

        }).catch((err: any) => {
            this._vmsHGrid.items([]);
            this._vmsHGrid.noRowsMessage(ClientResources.quotaTableServerError);
            this._wsHGrid.items([]);
            this._wsHGrid.noRowsMessage(ClientResources.quotaTableNoData);
            this._vmsHGrid.loading(false);
            this._wsHGrid.loading(false);
            this._quotaSettingGrid.noRowsMessage(ClientResources.quotaTableNoData);
            this._quotaSettingGrid.loading(false);
        }).finally(() => {
            this._vmsHGrid.loading(false);
            this._wsHGrid.loading(false);
            this._quotaSettingGrid.loading(false);
        });
    }

    private _getQuotaGridRowItems(quotaUsageResponse: QuotaUsage[], tenantId: string): Array<Array<QuotaRowItem>> {
        const result: Array<Array<QuotaRowItem>> = [];
        this._vmsGridVMFamilyMap = {};
        this._vmsGridVMFamilyWorkspaceMap = {};
        this._vmsGridVMFamilyWorkspaceClusterMap = {};
        this._wsGridWorkspaceMap = {};
        this._wsGridWorkspaceVMFamilyMap = {};
        this._wsGridWorkspaceVMFamilyClusterMap = {};

        quotaUsageResponse.forEach((quotaUsage: QuotaUsage) => {
            const usageType = QuotaUtils.getQuotaUsageType(quotaUsage);

            if (usageType === QuotaUsageType.VMFamily) {
                const coreVMFamilyName = quotaUsage.name.value;
                const isDedicated = QuotaUtils.isQuotaUsageDedicated(quotaUsage);
                const displayName = getFamilyDisplayName(coreVMFamilyName);

                if (!this._supportedVMFamilySizesMap[coreVMFamilyName.toLowerCase()] && quotaUsage.currentValue === 0) {
                    // Skip processing this VM Family because its not supported for this location
                    return;
                }

                if (!this._vmsGridVMFamilyMap[coreVMFamilyName]) {
                    this._vmsGridVMFamilyMap[coreVMFamilyName] = this._generateQuotaRowItem(quotaUsage.id, coreVMFamilyName, displayName, usageType, FIRST_LEVEL);
                    this._sanitizeUsagePercent(this._vmsGridVMFamilyMap[coreVMFamilyName]);
                }

                const quotaRowItem = this._vmsGridVMFamilyMap[coreVMFamilyName];
                this._calculateAndSetUsagePercent(quotaUsage.currentValue, quotaUsage.limit, quotaRowItem, isDedicated);

            } else if (usageType === QuotaUsageType.Workspace) {
                const coreVMFamilyName = quotaUsage.name.value;
                const isDedicated = QuotaUtils.isQuotaUsageDedicated(quotaUsage);
                const displayName = getFamilyDisplayName(coreVMFamilyName);

                // Process this workspace only if we have processed its VM Family entry
                if (this._vmsGridVMFamilyMap[coreVMFamilyName]) {
                    const vmFamilyRowItem = this._vmsGridVMFamilyMap[coreVMFamilyName];
                    const workspaceName = this._extractWorkspaceNameFromResourceID(quotaUsage.id);
                    const resourceGroupName = this._extractResourceGroupNameFromResourceID(quotaUsage.id);
                    const subscriptionId = this._extractSubscriptionIdFromResourceID(quotaUsage.id);
                    const fullWorkspaceName = workspaceName + " - " + resourceGroupName;
                    const workspaceDisplayName = workspaceName + " (" + resourceGroupName + ")";
                    const workspaceDisplayNameLink = this._getWorkspaceDisplayNameLink(subscriptionId, resourceGroupName, workspaceName);

                    if (!this._wsGridWorkspaceMap[fullWorkspaceName]) {
                        this._wsGridWorkspaceMap[fullWorkspaceName] = this._generateQuotaRowItem(quotaUsage.id, fullWorkspaceName, workspaceDisplayName, usageType, FIRST_LEVEL, null, null, null, workspaceDisplayNameLink);
                        this._sanitizeUsagePercent(this._wsGridWorkspaceMap[fullWorkspaceName]);
                    }

                    if (!this._wsGridWorkspaceVMFamilyMap[fullWorkspaceName]) {
                        this._wsGridWorkspaceVMFamilyMap[fullWorkspaceName] = {};
                    }

                    if (!this._wsGridWorkspaceVMFamilyMap[fullWorkspaceName][coreVMFamilyName]) {
                        this._wsGridWorkspaceVMFamilyMap[fullWorkspaceName][coreVMFamilyName] = this._generateQuotaRowItem(vmFamilyRowItem.resourceId, vmFamilyRowItem.resourceName, displayName, QuotaUsageType.VMFamily, SECOND_LEVEL, fullWorkspaceName, fullWorkspaceName, fullWorkspaceName);
                        this._sanitizeUsagePercent(this._wsGridWorkspaceVMFamilyMap[fullWorkspaceName][coreVMFamilyName]);
                    }

                    if (!this._vmsGridVMFamilyWorkspaceMap[coreVMFamilyName]) {
                        this._vmsGridVMFamilyWorkspaceMap[coreVMFamilyName] = {};
                    }

                    if (!this._vmsGridVMFamilyWorkspaceMap[coreVMFamilyName][fullWorkspaceName]) {
                        this._vmsGridVMFamilyWorkspaceMap[coreVMFamilyName][fullWorkspaceName] = this._generateQuotaRowItem(quotaUsage.id, fullWorkspaceName, workspaceDisplayName, QuotaUsageType.Workspace, SECOND_LEVEL, coreVMFamilyName, coreVMFamilyName, coreVMFamilyName, workspaceDisplayNameLink);
                        this._sanitizeUsagePercent(this._vmsGridVMFamilyWorkspaceMap[coreVMFamilyName][fullWorkspaceName]);
                    }

                    const workspaceGridItem = this._wsGridWorkspaceMap[fullWorkspaceName];
                    const workspaceVMFamilyItem = this._wsGridWorkspaceVMFamilyMap[fullWorkspaceName][coreVMFamilyName];
                    const vmFamilyWorkspaceItem = this._vmsGridVMFamilyWorkspaceMap[coreVMFamilyName][fullWorkspaceName];


                    this._calculateAndSetUsagePercent(quotaUsage.currentValue + workspaceGridItem.currentValue, quotaUsage.limit + workspaceGridItem.currentLimit, workspaceGridItem, isDedicated, true);
                    this._calculateAndSetUsagePercent(quotaUsage.currentValue, quotaUsage.limit, workspaceVMFamilyItem, isDedicated);
                    this._calculateAndSetUsagePercent(quotaUsage.currentValue, quotaUsage.limit, vmFamilyWorkspaceItem, isDedicated);

                    workspaceGridItem.expandable(true);
                    workspaceGridItem.expanded(false);
                    vmFamilyRowItem.expandable(true);
                    vmFamilyRowItem.expanded(false);
                }

            } else if (usageType === QuotaUsageType.Compute) {
                const isDedicated = QuotaUtils.isQuotaUsageDedicated(quotaUsage);
                const coreVMFamilyName = quotaUsage.name.value;
                const workspaceName = this._extractWorkspaceNameFromResourceID(quotaUsage.id);
                const resourceGroupName = this._extractResourceGroupNameFromResourceID(quotaUsage.id);
                const fullWorkspaceName = workspaceName + " - " + resourceGroupName;

                if (this._vmsGridVMFamilyMap[coreVMFamilyName] && this._wsGridWorkspaceMap[fullWorkspaceName] &&
                    this._vmsGridVMFamilyWorkspaceMap[coreVMFamilyName] && this._vmsGridVMFamilyWorkspaceMap[coreVMFamilyName][fullWorkspaceName] &&
                    this._wsGridWorkspaceVMFamilyMap[fullWorkspaceName] && this._wsGridWorkspaceVMFamilyMap[fullWorkspaceName][coreVMFamilyName]) {

                    const workspaceName = this._extractWorkspaceNameFromResourceID(quotaUsage.id);
                    const resourceGroupName = this._extractResourceGroupNameFromResourceID(quotaUsage.id);
                    const subscriptionId = this._extractSubscriptionIdFromResourceID(quotaUsage.id);
                    const computeName = this._extractComputeNameFromResourceID(quotaUsage.id);
                    const fullComputeName = computeName + " - " + fullWorkspaceName;
                    const computeDisplayName = computeName;
                    const computeDisplayNameLink = this._getComputeDisplayNameLink(subscriptionId, resourceGroupName, workspaceName, computeName, tenantId);

                    const vmFamilyGridItem = this._vmsGridVMFamilyMap[coreVMFamilyName];
                    const workspaceGridItem = this._wsGridWorkspaceMap[fullWorkspaceName];

                    const workspaceVMFamilyGridItem = this._wsGridWorkspaceVMFamilyMap[fullWorkspaceName][coreVMFamilyName];
                    const vmFamilyWorkspaceGridItem = this._vmsGridVMFamilyWorkspaceMap[coreVMFamilyName][fullWorkspaceName];

                    if (!this._vmsGridVMFamilyWorkspaceClusterMap[coreVMFamilyName]) {
                        this._vmsGridVMFamilyWorkspaceClusterMap[coreVMFamilyName] = {}
                    }
                    if (!this._vmsGridVMFamilyWorkspaceClusterMap[coreVMFamilyName][fullWorkspaceName]) {
                        this._vmsGridVMFamilyWorkspaceClusterMap[coreVMFamilyName][fullWorkspaceName] = {};
                    }
                    if (!this._vmsGridVMFamilyWorkspaceClusterMap[coreVMFamilyName][fullWorkspaceName][fullComputeName]) {
                        this._vmsGridVMFamilyWorkspaceClusterMap[coreVMFamilyName][fullWorkspaceName][fullComputeName] =
                            this._generateQuotaRowItem(quotaUsage.id, fullComputeName, computeDisplayName, usageType, THIRD_LEVEL, fullWorkspaceName, workspaceGridItem.resourceId, vmFamilyGridItem.resourceName, computeDisplayNameLink);
                        this._sanitizeUsagePercent(this._vmsGridVMFamilyWorkspaceClusterMap[coreVMFamilyName][fullWorkspaceName][fullComputeName]);
                    }

                    if (!this._wsGridWorkspaceVMFamilyClusterMap[fullWorkspaceName]) {
                        this._wsGridWorkspaceVMFamilyClusterMap[fullWorkspaceName] = {};
                    }
                    if (!this._wsGridWorkspaceVMFamilyClusterMap[fullWorkspaceName][coreVMFamilyName]) {
                        this._wsGridWorkspaceVMFamilyClusterMap[fullWorkspaceName][coreVMFamilyName] = {};
                    }
                    if (!this._wsGridWorkspaceVMFamilyClusterMap[fullWorkspaceName][coreVMFamilyName][fullComputeName]) {
                        this._wsGridWorkspaceVMFamilyClusterMap[fullWorkspaceName][coreVMFamilyName][fullComputeName] = this._generateQuotaRowItem(quotaUsage.id, fullComputeName, computeDisplayName, usageType, THIRD_LEVEL, coreVMFamilyName, workspaceVMFamilyGridItem.resourceId, fullWorkspaceName, computeDisplayNameLink);
                        this._sanitizeUsagePercent(this._wsGridWorkspaceVMFamilyClusterMap[fullWorkspaceName][coreVMFamilyName][fullComputeName]);
                    }

                    workspaceVMFamilyGridItem.expandable(true);
                    workspaceVMFamilyGridItem.expanded(false);
                    vmFamilyWorkspaceGridItem.expandable(true);
                    vmFamilyWorkspaceGridItem.expanded(false);

                    const vmGridWorkspaceClusterItem = this._vmsGridVMFamilyWorkspaceClusterMap[coreVMFamilyName][fullWorkspaceName][fullComputeName];
                    const wsGridVMFamilyClusterItem = this._wsGridWorkspaceVMFamilyClusterMap[fullWorkspaceName][coreVMFamilyName][fullComputeName];
                    this._calculateAndSetUsagePercent(quotaUsage.currentValue, quotaUsage.limit, vmGridWorkspaceClusterItem, isDedicated);
                    this._calculateAndSetUsagePercent(quotaUsage.currentValue, quotaUsage.limit, wsGridVMFamilyClusterItem, isDedicated);

                }
            }

        });

        const totalCoresQuotaRowItem = this._generateQuotaRowItem(QuotaUsageType.TotalCores, ClientResources.quotaTableTotalSubscriptionQuota, ClientResources.quotaTableTotalSubscriptionQuota, QuotaUsageType.TotalCores, FIRST_LEVEL);
        this._sanitizeUsagePercent(totalCoresQuotaRowItem);
        const totalDedicatedCoresUsage = quotaUsageResponse.find((quotaUsage: QuotaUsage) => { return QuotaUtils.getQuotaUsageType(quotaUsage) === QuotaUsageType.TotalDedicatedCores; });
        const totalLowPriorityCoresUsage = quotaUsageResponse.find((quotaUsage: QuotaUsage) => { return QuotaUtils.getQuotaUsageType(quotaUsage) === QuotaUsageType.TotalLowPriorityCores; });
        if (totalDedicatedCoresUsage) {
            this._calculateAndSetUsagePercent(totalDedicatedCoresUsage.currentValue, totalDedicatedCoresUsage.limit, totalCoresQuotaRowItem, true);
        }
        if (totalLowPriorityCoresUsage) {
            this._calculateAndSetUsagePercent(totalLowPriorityCoresUsage.currentValue, totalLowPriorityCoresUsage.limit, totalCoresQuotaRowItem, false);
        }

        const vmGridItems = Object.keys(this._vmsGridVMFamilyMap).map(key => this._vmsGridVMFamilyMap[key]);
        const wsGridItems = Object.keys(this._wsGridWorkspaceMap).map(key => this._wsGridWorkspaceMap[key]);

        vmGridItems.sort((a: QuotaRowItem, b: QuotaRowItem) => a.displayName.localeCompare(b.displayName));
        wsGridItems.sort((a: QuotaRowItem, b: QuotaRowItem) => a.displayName.localeCompare(b.displayName));

        vmGridItems.push(totalCoresQuotaRowItem);
        wsGridItems.push(totalCoresQuotaRowItem);

        result.push(vmGridItems);
        result.push(wsGridItems);
        return result;
    }

    private _getQuotaSettingGridItems(getQuotasResponse: QuotaSetting[]): QuotaSettingRowItem[] {
        this._qsGridVMFamilyMap = {};
        this._qsGridVMFamilyWorkspaceMap = {};
        this._qsGridVMFamilyHelpTextNosMap = {};
        this._quotaErrorMessage("");
        this._quotaErrorMessageVisible(false);
        getQuotasResponse.forEach((quotaSetting: QuotaSetting) => {
            const usageType = QuotaUtils.getQuotaUsageType(quotaSetting);
            if (usageType === QuotaUsageType.VMFamily) {
                const coreVMFamilyName = quotaSetting.name.value;
                const displayName = getFamilyDisplayName(coreVMFamilyName);

                if (!this._supportedVMFamilySizesMap[coreVMFamilyName.toLowerCase()]) {
                    // Skip processing this VM Family because its not supported for this location
                    return;
                }

                if (!this._qsGridVMFamilyMap[coreVMFamilyName]) {
                    this._qsGridVMFamilyMap[coreVMFamilyName] = this._generateQuotaSettingItem(quotaSetting.id, coreVMFamilyName, displayName, QuotaUsageType.VMFamily, 0, quotaSetting.limit);
                }
            } else if (usageType === QuotaUsageType.Workspace) {
                const coreVMFamilyName = quotaSetting.name.value;
                if (this._qsGridVMFamilyMap[coreVMFamilyName]) {
                    const workspaceName = this._extractWorkspaceNameFromResourceID(quotaSetting.id);
                    const resourceGroupName = this._extractResourceGroupNameFromResourceID(quotaSetting.id);
                    const fullWorkspacName = workspaceName + "-" + resourceGroupName;
                    const displayName = workspaceName + " (" + resourceGroupName + ")";
                    const parentVMFamilyItem = this._qsGridVMFamilyMap[coreVMFamilyName];
                    if (!this._qsGridVMFamilyWorkspaceMap[coreVMFamilyName]) {
                        this._qsGridVMFamilyWorkspaceMap[coreVMFamilyName] = {};
                    }
                    if (!this._qsGridVMFamilyWorkspaceMap[coreVMFamilyName][fullWorkspacName]) {
                        this._qsGridVMFamilyWorkspaceMap[coreVMFamilyName][fullWorkspacName] = this._generateQuotaSettingItem(quotaSetting.id, fullWorkspacName, displayName, QuotaUsageType.Workspace, 1, quotaSetting.limit, "", coreVMFamilyName, parentVMFamilyItem.resourceId, parentVMFamilyItem.resourceId);
                    }
                    parentVMFamilyItem.expandable(true);
                }
            }
        });
        const result = Object.keys(this._qsGridVMFamilyWorkspaceMap).map(key => this._qsGridVMFamilyMap[key]);
        return result;
    }
    private _calculatePercent(currentValue: number, limit: number): number {

        if (limit === -1 || limit === null) {
            limit = this._totalLowPrioClusterLimit;
        }
        let percent: number;
        if (currentValue === null || limit === null) {
            percent = 0;
        } else if (limit === 0) {
            percent = -1; // So that this appears last while sorting in the grid
        } else {
            percent = Math.floor(currentValue * 100 / limit);
        }

        return percent;
    }

    private _calculatePercentLabel(percent: number, currentValue: number, limit: number, hideDenominator: boolean = false): string {

        let percentLabel: string;


        if (limit === -1 || limit === null) {
            limit = this._totalLowPrioClusterLimit;
        }
        let denominator = `/${limit}`;
        if (hideDenominator) {
            denominator = "";
        }
        if (currentValue === null || limit === null) {
            percentLabel = ClientResources.textNotAvailable;
        } else if (limit === 0) {
            percentLabel = `${currentValue}${denominator} (${ClientResources.textNotAvailable})`;
        } else {
            percentLabel = `${currentValue}${denominator} (${percent} %)`;
        }

        return percentLabel;
    }

    private _calculatePercentColor(percent: number, currentValue: number, limit: number): string {
        let percentColor: string;
        if (currentValue === null || limit === null) {
            percentColor = "";
        } else {
            percentColor = percent < 50 ? "i0t1" : (percent < 75 ? "a0t1" : "c0t1");
        }

        return percentColor;
    }

    private _extractVMFamilyNameFromQuotaResourceID(resourceID: string): string {
        const vmFamily = resourceID && resourceID !== null ? resourceID.match(BackendQuotaUsageValues.idRegExp.vmFamilyQuotas) : null;
        return vmFamily !== null ? vmFamily[0].substr(vmFamily[0].lastIndexOf("/") + 1) : '';
    }

    private _extractWorkspaceNameFromResourceID(resourceID: string): string {
        const workspaceName = resourceID && resourceID !== null ? resourceID.match(BackendQuotaUsageValues.idRegExp.workspaceName) : null;
        return workspaceName !== null ? workspaceName[0].substr(workspaceName[0].lastIndexOf("/") + 1) : '';
    }

    private _extractComputeNameFromResourceID(resourceID: string): string {
        const computeName = resourceID && resourceID !== null ? resourceID.match(BackendQuotaUsageValues.idRegExp.computeName) : null;
        return computeName !== null ? computeName[0].substr(computeName[0].lastIndexOf("/") + 1) : '';
    }

    private _extractResourceGroupNameFromResourceID(resourceID: string): string {
        const resourceGroupName = resourceID && resourceID !== null ? resourceID.match(BackendQuotaUsageValues.idRegExp.resourceGroupName) : null;
        return resourceGroupName !== null ? resourceGroupName[0].substr(resourceGroupName[0].lastIndexOf("/") + 1) : '';
    }

    private _extractSubscriptionIdFromResourceID(resourceID: string): string {
        const subscriptionId = resourceID && resourceID !== null ? resourceID.match(BackendQuotaUsageValues.idRegExp.subscriptionId) : null;
        return subscriptionId !== null ? subscriptionId[0].substr(subscriptionId[0].lastIndexOf("/") + 1) : '';
    }

    private _generateQuotaRowItem(resourceId: string, resourceName: string,
        displayName: string, resourceType: QuotaUsageType, depth: number,
        parentResourceName?: string,
        parentResourceId?: string,
        rootParentResourceName?: string,
        displayNameLink?: string): QuotaRowItem {
        const quotaRowItem: QuotaRowItem = {
            resourceId: resourceId,
            resourceName: resourceName,
            displayName: displayName,
            resourceType: resourceType,
            dedicatedPercent: 0,
            dedicatedPercentColor: '',
            dedicatedPercentLabel: '',
            lowPriorityPercent: 0,
            lowPriorityPercentColor: '',
            lowPriorityPercentLabel: '',
            expandable: ko.observable(false),
            expanded: ko.observable(false),
            depth: ko.observable(depth),
            currentValue: 0
        };
        if (parentResourceName) {
            quotaRowItem.parentResourceName = parentResourceName;
        }
        if (parentResourceId) {
            quotaRowItem.parentResourceId = parentResourceId;
        }
        if (rootParentResourceName) {
            quotaRowItem.rootParentResourceName = rootParentResourceName;
        }
        if (displayNameLink) {
            quotaRowItem.displayNameLink = displayNameLink;
        }
        return quotaRowItem;
    }

    private _generateQuotaSettingItem(resourceId: string, resourceName: string,
        displayName: string, resourceType: QuotaUsageType, depth: number,
        currentQuotaLimit?: number, placeHolderText?: string,
        parentResourceName?: string,
        parentResourceId?: string, rootParentResourceName?: string): QuotaSettingRowItem {
        const { container } = this.context;
        const numericValidation = new NumericValidation()
        numericValidation.isEmptyValid = ko.observable(true);
        const quotaSettingItem: QuotaSettingRowItem = {
            resourceId: resourceId,
            resourceName: resourceName,
            displayName: displayName,
            resourceType: resourceType,
            depth: ko.observable(depth),
            expandable: ko.observable(false),
            expanded: ko.observable(false),
            newLimit: NumericTextBox.create(container, {
                min: ko.observable(0),
                validations: ko.observableArray([
                    numericValidation
                ]),
                allowEmpty: true,
                ariaLabel: displayName + " " + ClientResources.quotaRequestNewLimit,
                cssClass: `ext-ml-newlimit`,
                decimalPoint: ko.observable(0),
                hideValidationCheck: true,
                onEnterPressed: () => this._onSaveButtonClick(quotaSettingItem),
                placeHolderText: ko.observable("")
            }),
            status: ko.observable(""),
            saveButton: ko.observable(() => this._onSaveButtonClick(quotaSettingItem)),
            showSaveButton: ko.observable(false),
            placeHolderText: ko.observable(placeHolderText || "")
        }

        if (currentQuotaLimit >= 0) {
            quotaSettingItem.currentLimit = ko.observable(currentQuotaLimit);
        }
        if (parentResourceName) {
            quotaSettingItem.parentResourceName = parentResourceName;
        }
        if (parentResourceId) {
            quotaSettingItem.parentResourceId = parentResourceId;
        }
        if (rootParentResourceName) {
            quotaSettingItem.rootParentResourceName = rootParentResourceName;
        }

        quotaSettingItem.newLimit.value.subscribeAndRun(container, (newValue) => {
            quotaSettingItem.status("");
            quotaSettingItem.showSaveButton(false);
            if (newValue !== null && (newValue === 0 || newValue.toString().length > 0)) {
                quotaSettingItem.showSaveButton(true);
            }
        });

        return quotaSettingItem;
    }

    private _onSaveButtonClick(quotaSettingItem: QuotaSettingRowItem) {
        const { model } = this.context;
        if (quotaSettingItem.newLimit.value() !== null && quotaSettingItem.newLimit.valid()) {
            quotaSettingItem.status(QUOTA_POST_SAVING);
            const postData: QuotaSetting = {
                id: quotaSettingItem.resourceId,
                type: BackendQuotaUsageValues.type.workspaceDedicatedQuotas,
                limit: quotaSettingItem.newLimit && quotaSettingItem.newLimit.value() && parseInt(quotaSettingItem.newLimit.value(), 10),
                unit: "Count"
            };
            const postDataArray: QuotaSetting[] = [postData];
            const postDataBulk = JSON.stringify({
                "value": postDataArray
            });
            model.machineLearningServicesData.updateQuotas(this._subscriptionId(), this._location(), postDataBulk).then((response) => {

                if (!Array.isArray(response)) {
                    if (response === QUOTA_POST_INSUFFICIENT_PERMISSIONS) {
                        this._setErrorMessageBox(response);
                    } else {
                        this._setErrorMessageBox(QUOTA_POST_UNDEFINED);
                    }
                    quotaSettingItem.status(QUOTA_POST_FAILURE);
                    return;
                }

                let newItem: QuotaSettingRowItem = null;
                response.forEach((quotaSetting) => {
                    const currentItems = this._quotaSettingHierarchy.items();
                    const newItems = currentItems.map((item: QuotaSettingRowItem) => {
                        if (item.resourceId === quotaSetting.id) {
                            if (quotaSetting.status === QUOTA_POST_SUCCESS) {
                                // Update the current value and clear the new limit value and set status
                                item.status(quotaSetting.status);
                                this._setErrorMessageBox(quotaSetting.status);
                                item.currentLimit(quotaSetting.limit);
                            } else {
                                this._setErrorMessageBox(quotaSetting.status);
                                item.status(QUOTA_POST_FAILURE);
                                this._quotaErrorMessageVisible(true);
                            }
                            newItem = item;
                        }
                        return item;
                    });
                    this._quotaSettingHierarchy.items(newItems);

                    Object.keys(this._quotaSettingHierarchy._secondLevelItemsMap).forEach((vmFamilyName) => {
                        Object.keys(this._quotaSettingHierarchy._secondLevelItemsMap[vmFamilyName]).forEach((workspace) => {
                            const quotaSettingRowItem: QuotaSettingRowItem = this._quotaSettingHierarchy._secondLevelItemsMap[vmFamilyName][workspace];
                            if (newItem !== null && quotaSettingRowItem.resourceId === newItem.resourceId) {
                                this._quotaSettingHierarchy._secondLevelItemsMap[vmFamilyName][workspace] = newItem;
                            }
                        });
                    });
                    this._qsGridVMFamilyWorkspaceMap = this._quotaSettingHierarchy._secondLevelItemsMap;
                    this._updatePlaceHolderTexts(this._extractVMFamilyNameFromQuotaResourceID(newItem.resourceId));
                });
            }).catch(err => {
                this._quotaSettingHierarchy.items([]);
            }).finally(() => {
                this.context.form.configureAlertOnClose(TemplateBlade.AlertLevel.None);
            });
        }
    }

    private _updatePlaceHolderTexts(coreVMFamilyName: string): void {
        const workspaces = this._quotaSettingHierarchy._secondLevelItemsMap[coreVMFamilyName];

        if (workspaces) {
            let totalUsedCores = 0;
            let subMax = 0;
            let placeHolderText = "";
            if (!this._qsGridVMFamilyHelpTextNosMap[coreVMFamilyName]) {
                const parentVMFamilyItem = this._qsGridVMFamilyMap[coreVMFamilyName];
                placeHolderText = ClientResources.quotaWorkspaceQuotaPlaceHolder.format(0, (parentVMFamilyItem && parentVMFamilyItem.currentLimit()) || 0);
                this._qsGridVMFamilyHelpTextNosMap[coreVMFamilyName] = {
                    unallocatedCore: 0,
                    subscriptionMax: (parentVMFamilyItem && parentVMFamilyItem.currentLimit()) || 0,
                    placeHolderText: ko.observable(placeHolderText)
                };
            }
            subMax = this._qsGridVMFamilyHelpTextNosMap[coreVMFamilyName].subscriptionMax;
            totalUsedCores = 0;
            Object.keys(this._quotaSettingHierarchy._secondLevelItemsMap[coreVMFamilyName]).forEach((workspaceKey) => {
                const workspaceItem = <QuotaSettingRowItem>this._quotaSettingHierarchy._secondLevelItemsMap[coreVMFamilyName][workspaceKey]
                totalUsedCores = totalUsedCores + workspaceItem.currentLimit();
            });
            Object.keys(this._quotaSettingHierarchy._secondLevelItemsMap[coreVMFamilyName]).forEach((workspaceKey) => {
                const workspaceItem = <QuotaSettingRowItem>this._quotaSettingHierarchy._secondLevelItemsMap[coreVMFamilyName][workspaceKey];
                const unAllocatedCores = (subMax - totalUsedCores > 0) ? subMax - totalUsedCores : 0;
                placeHolderText = ClientResources.quotaWorkspaceQuotaPlaceHolder.format(unAllocatedCores, subMax);
                this._qsGridVMFamilyHelpTextNosMap[coreVMFamilyName]["unallocatedCore"] = unAllocatedCores;
                this._qsGridVMFamilyHelpTextNosMap[coreVMFamilyName]["placeHolderText"](placeHolderText);
                workspaceItem.newLimit.placeHolderText(placeHolderText);
            });
        }
        const newItems = this._quotaSettingHierarchy.items().map((item: QuotaSettingRowItem) => {
            if (item.resourceType === QuotaUsageType.Workspace) {
                const parentVMFamily = item.parentResourceName;
                if (this._qsGridVMFamilyHelpTextNosMap[parentVMFamily]) {
                    item.newLimit.placeHolderText(this._qsGridVMFamilyHelpTextNosMap[parentVMFamily]["placeHolderText"]());
                }
            }
            return item;
        });
        this._quotaSettingHierarchy.items(newItems);
    }

    private _setErrorMessageBox(quotaStatusCode: string = "") {
        const lowerCaseQuotaStatusCode = quotaStatusCode.toLowerCase();
        switch (lowerCaseQuotaStatusCode) {
            case QUOTA_POST_INVALID_QUOTA_BELOW_CLUSTER_MINIMUM:
                this._quotaErrorMessage(ClientResources.quotaWorkspaceQuotaLessThanMinimumClusterCores);
                this._quotaErrorMessageVisible(true);
                break;
            case QUOTA_POST_INVALID_QUOTA_EXCEEDS_SUBSCRIPTION_LIMIT:
                this._quotaErrorMessage(ClientResources.quotaWorkspaceQuotaExceedSubscriptionLimit);
                this._quotaErrorMessageVisible(true);
                break;
            case QUOTA_POST_INVALID_VMFAMILY_NAME:
                this._quotaErrorMessage(ClientResources.quotaWorkspaceQuotaInvalidVMFamilyName);
                this._quotaErrorMessageVisible(true);
                break;
            case QUOTA_POST_SUCCESS:
                this._quotaErrorMessage("");
                this._quotaErrorMessageVisible(false);
                break;
            case QUOTA_POST_INSUFFICIENT_PERMISSIONS:
                this._quotaErrorMessage(ClientResources.quotaWorkspaceQuotaInsufficientPermissions);
                this._quotaErrorMessageVisible(true);
                break;
            case QUOTA_POST_FAILURE:
            case QUOTA_POST_UNDEFINED:
                this._quotaErrorMessage(ClientResources.quotaWorkspaceQuotaUnknownError);
                this._quotaErrorMessageVisible(true);
                break;
            default:
                this._quotaErrorMessage("");
                this._quotaErrorMessageVisible(false);
                break;
        }
    }

    private _calculateAndSetUsagePercent(currentValue: number, limit: number, quotaRowItem: QuotaRowItem, isDedicated: boolean, hideDenominator: boolean = false): void {

        const percent = this._calculatePercent(currentValue, limit);
        const percentLabel = this._calculatePercentLabel(percent, currentValue, limit, hideDenominator);
        const percentColor = this._calculatePercentColor(percent, currentValue, limit);

        if (isDedicated) {
            quotaRowItem.dedicatedPercent = percent;
            quotaRowItem.dedicatedPercentLabel = percentLabel;
            quotaRowItem.dedicatedPercentColor = percentColor;
        } else {
            quotaRowItem.lowPriorityPercent = percent;
            quotaRowItem.lowPriorityPercentLabel = percentLabel;
            quotaRowItem.lowPriorityPercentColor = percentColor;
        }
        quotaRowItem.currentValue = currentValue;
        quotaRowItem.currentLimit = limit;
    }

    private _sanitizeUsagePercent(quotaRowItem: QuotaRowItem): void {
        this._calculateAndSetUsagePercent(0, 0, quotaRowItem, true, true);
        this._calculateAndSetUsagePercent(0, 0, quotaRowItem, false, true);
    }

    private _getWorkspaceDisplayNameLink(subscriptionId: string, resourceGroupName: string, workspaceName: string): string {
        const hostName = MsPortalFx.getEnvironmentValue("trustedParentOrigin") || "https://portal.azure.com";
        const link = `${hostName}/#resource/subscriptions/${subscriptionId}/resourceGroups/${resourceGroupName}/providers/Microsoft.MachineLearningServices/workspaces/${workspaceName}`;
        return link;
    }

    private _getComputeDisplayNameLink(subscriptionId: string, resourceGroupName: string, workspaceName: string, computeName: string, tenantId: string): string {
        const hostName = MsPortalFx.getEnvironmentValue("webWorkspace2BaseUrl") || "https://ml.azure.com";
        const link = `${hostName}/compute/${computeName}/details?wsid=/subscriptions/${subscriptionId}/resourceGroups/${resourceGroupName}/providers/Microsoft.MachineLearningServices/workspaces/${workspaceName}&tid=${tenantId}`;
        return link;
    }
}
