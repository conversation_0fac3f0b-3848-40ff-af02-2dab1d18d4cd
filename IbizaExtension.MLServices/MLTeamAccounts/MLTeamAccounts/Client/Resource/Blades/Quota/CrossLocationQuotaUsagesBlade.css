.ext-ml-quota-section {
    padding: 10px 25px;
}

.ext-ml-quota-section-tab {
    padding: 0 20px 10px 20px;
}

.ext-ml-infobox {
    width: 30%;
    padding: 0 20px 20px 0;
}

.ext-divider {
    border-bottom-style: solid;
    border-bottom-width: 1px;
    margin-right: 20px;
    margin-left: 20px;
}

.ext-ml-quota-view-container {
    display: flex;
    flex-direction: row;
}

.ext-ml-quota-views {
    flex-grow: 1;
}

.ext-ml-quota-view {
    display: flex;
    flex-direction: column;
}

.ext-ml-quota-view-actionbar {
    display: flex;
    margin-bottom: 8px;
}

.ext-ml-quota-view-actionbar-info {
    flex-grow: 1;
}

.ext-ml-quota-view-actionbar-action {
    display: flex;
    align-items: center;
}

.ext-ml-quota-view-tree-container {
    flex-grow: 1;
}

.ext-ml-workspace-link,
.ext-ml-compute-link {
    cursor: pointer;
}

.ext-ml-workspace-link:hover,
.ext-ml-compute-link:hover {
    text-decoration: underline;
}

.ext-ml-totalcores-name {
    font-weight: 600;
}

.ext-ml-quota-view-total-cores {
    display: flex;
    margin-bottom: 8px;
}

.ext-ml-quota-view-total-cores-label {
    min-width: 140px;
    font-weight: bold;
}

.ext-ml-quota-view-request-quota-button {
    margin-right: 12px;
}

.ext-ml-quota-view-progress {
    height: 8px;
    border-radius: 4px;
}

.ext-ml-crosslocation-info {
    white-space: break-spaces;
}
