import * as TemplateBlade from "Fx/Composition/TemplateBlade";
import * as CustomHtml from "Fx/Controls/CustomHtml";
import * as DropDown from "Fx/Controls/DropDown";
import * as InfoBox from "Fx/Controls/InfoBox";
import * as RadioButtons from "Fx/Controls/RadioButtons";
import * as Section from "Fx/Controls/Section";
import * as SubscriptionsDropDown from "Fx/Controls/SubscriptionDropDown";
import * as TabControl from "Fx/Controls/TabControl";
import * as Toggle from "Fx/Controls/Toggle";
import { ArmId } from "Fx/ResourceManagement";

import { SubscriptionWorkspacesResult } from "../../../Resource/Data/MLComputeQuotaData";
import { RefreshCommandButton } from "../../../Shared/CommandButtons";
import * as Constants from "../../../Shared/Constants";
import { DocLinkIds, getDocumentUrl } from "../../../Shared/DocLinks";
import Utilities from "../../../Shared/Utilities";
import { DataContext } from "../../ResourceArea";
import { getFullWorkspaceName } from "./CrossLocation/ComputeQuotaUtils";
import * as QuotaUsagesTree from "./CrossLocation/Controls/QuotaUsagesTree";
import { MachineLearningServicesQuotaUsageBladeParameters } from "./MachineLearningServicesQuotaUsageBladeParameters";
import { QuotaUsage, QuotaUsageResult } from "./Models/QuotaUsage";
import { VMSize, VMSizesResult } from "./Models/VMSize";

import * as ClientResources from "Resx/ClientResources";

interface IComputeUsagesState {
    loading?: boolean;
    error?: string;
    canShowUsages?: boolean;
    usages?: QuotaUsage[];
    vmSizes?: VMSize[];
    workspaceLocations?: Map<string, string>;
}

const globalRateLimit = 500;

@TemplateBlade.Decorator({
    htmlTemplate: `
    <div class="ext-ml-quota-section">
        <div>
            <div class="ext-ml-infobox" data-bind="pcControl: subscriptionsDropDown"></div>
            <div class="ext-ml-infobox" data-bind="pcControl: locationsDropDown"></div>
        </div>
    </div>
    <div class="ext-divider msportalfx-border"></div>
    <div class="ext-ml-quota-section">
        <div class="ext-ml-quota-view-container">
            <div class="ext-ml-quota-views" data-bind="pcControl: viewsRadioButton"></div>
            <div data-bind="pcControl: crossLocationQuotaToggleSubscription"></div>
            <div data-bind="pcControl: crossLocationQuotaToggleWorkspace"></div>
        </div>
        <div data-bind="pcControl: quotaInfo"></div>
    </div>
    <div class="ext-ml-quota-section-tab">
        <div data-bind="pcControl: loadingText"></div>
        <div data-bind="pcControl: errorMessage"></div>
        <div data-bind="pcControl: crossLocationWarning"></div>
        <div data-bind="pcControl: subscriptionUsageTabs"></div>
        <div data-bind="pcControl: workspaceUsageTabs"></div>
    </div>
    `,
    styleSheets: ["../../../Shared/Styles/CustomStyles.css", "./CrossLocationQuotaUsagesBlade.css"]
})
@TemplateBlade.InjectableModel.Decorator(DataContext)
export class CrossLocationQuotaUsagesBlade {
    private _location = ko.pureComputed(() => this.locationsDropDown.value());
    private _subscriptionId = ko.pureComputed(() => {
        const sub = this.subscriptionsDropDown.value();
        return sub && sub.subscriptionId;
    });
    private _tenantId = ko.pureComputed(() => {
        const sub = this.subscriptionsDropDown.value();
        return sub && sub.tenantId;
    });
    private _crossLocationInfoAvailable = ko.observable(false);

    private _computeUsagesState: IComputeUsagesState;

    public title = ClientResources.quotaBladeTitle;
    public subtitle = ClientResources.quotaBladeTitle;
    public context: TemplateBlade.Context<MachineLearningServicesQuotaUsageBladeParameters, DataContext>;

    public subscriptionsDropDown: SubscriptionsDropDown.Contract;
    public locationsDropDown: DropDown.ViewModel<string>;
    public viewsRadioButton: RadioButtons.Contract<QuotaUsagesTree.QuotaUsageGroupingType>;
    public crossLocationQuotaToggleSubscription: Toggle.Contract;
    public crossLocationQuotaToggleWorkspace: Toggle.Contract;
    public loadingText: CustomHtml.Contract;
    public errorMessage: InfoBox.Contract;
    public crossLocationWarning: InfoBox.Contract;
    public subscriptionUsageTabs: TabControl.Contract;
    public workspaceUsageTabs: TabControl.Contract;
    public quotaInfo: InfoBox.Contract;

    public onInitialize(): Q.Promise<any> {
        const { container, parameters } = this.context;

        container.icon(MsPortalFx.Base.Images.Polychromatic.ControlsHorizontal());

        this._initializeCommandBar();
        this._initializeSubscriptionsDropdown();
        this._initializeLocationsDropdown();
        this._initializeViewsRadioButton();
        this._initializeCrossLocationQuotaToggles();
        this._initializeLoadingText();
        this._initializeErrorMessages();
        this._initializeTabControls();
        this._initializeQuotaInfo();

        const deferred = Q.defer<any>();
        this.subscriptionsDropDown.value.fxOnceAndRun(container, () => {
            this._fetchLocations().then(() => {
                this._fetchQuota().then(() => {
                    this.subscriptionsDropDown.value
                        .extend({ rateLimit: globalRateLimit })
                        .subscribe(container, this._onSubscriptionChange.bind(this));
                    this.locationsDropDown.value.extend({ rateLimit: globalRateLimit }).subscribe(container, this._onLocationChange.bind(this));
                    deferred.resolve();
                });
            });
        });

        return deferred.promise;
    }
    private _initializeQuotaInfo(): void {
        const { container } = this.context;
        const learnMoreLink = `<a href="${getDocumentUrl(DocLinkIds.CrossLocationQuotas)}" target="_blank">${ClientResources.CrossRegionComputeQuotas.QuotaUsageBlade.quotaInfoLearnMore
            }</a>`;
        this.quotaInfo = InfoBox.create(container, {
            text: {
                htmlTemplate: `<div>${ClientResources.CrossRegionComputeQuotas.QuotaUsageBlade.quotaInfoText} ${learnMoreLink}</div>`
            }
        });
    }

    private _initializeCommandBar(): void {
        const { container } = this.context;
        const commandBar = new MsPortalFx.ViewModels.Toolbars.Toolbar(container);
        const refreshButton = new RefreshCommandButton(() => this._fetchQuota(true));

        commandBar.setItems([refreshButton]);
        container.commandBar = commandBar;
    }

    private _initializeSubscriptionsDropdown(): void {
        const { container, parameters } = this.context;

        const subscriptionsDropDownOptions: SubscriptionsDropDown.Options = {
            initialSubscriptionId: ko.observableArray([parameters.subscriptionId]),
            validations: ko.observableArray([new MsPortalFx.ViewModels.RequiredValidation(ClientResources.selectSubscription)]),
            resourceProviders: ko.observable([Constants.machineLearningServicesResourcesProvider]),
            suppressDirtyBehavior: true
        };

        this.subscriptionsDropDown = SubscriptionsDropDown.create(container, subscriptionsDropDownOptions);
    }

    private _initializeLocationsDropdown(): void {
        const { container } = this.context;

        this.locationsDropDown = new DropDown.ViewModel<string>(container, {
            label: ko.observable(ClientResources.resourceLocationColumn),
            items: ko.observableArray([]),
            validations: ko.observableArray([new MsPortalFx.ViewModels.RequiredValidation(ClientResources.selectLocation)]),
            suppressDirtyBehavior: true
        });
    }

    private _initializeViewsRadioButton(): void {
        const { container } = this.context;

        this.viewsRadioButton = RadioButtons.create(container, {
            items: [
                { text: ClientResources.CrossRegionComputeQuotas.subscriptionViewText, value: "computeFamily" },
                { text: ClientResources.CrossRegionComputeQuotas.workspaceViewText, value: "workspace" }
            ],
            singleItemPerLine: false,
            suppressDirtyBehavior: true,
            value: ko.observable("computeFamily")
        });

        this.viewsRadioButton.value.subscribe(container, _ => this._loadView(this._computeUsagesState));
    }

    private _initializeCrossLocationQuotaToggles(): void {
        const { container } = this.context;

        this.crossLocationQuotaToggleSubscription = Toggle.create(container, {
            ariaLabel: ClientResources.CrossRegionComputeQuotas.toggleText,
            disabled: ko.computed(container, () => !this._crossLocationInfoAvailable()),
            suppressDirtyBehavior: true,
            valueText: {
                offText: ClientResources.CrossRegionComputeQuotas.toggleText,
                onText: ClientResources.CrossRegionComputeQuotas.toggleText
            },
            visible: ko.computed(container, () => this.viewsRadioButton.value() === "computeFamily")
        });

        this.crossLocationQuotaToggleSubscription.value.subscribe(container, _ => this._loadView(this._computeUsagesState));

        this.crossLocationQuotaToggleWorkspace = Toggle.create(container, {
            ariaLabel: ClientResources.CrossRegionComputeQuotas.toggleText,
            disabled: true,
            suppressDirtyBehavior: true,
            value: true,
            valueText: {
                offText: ClientResources.CrossRegionComputeQuotas.toggleText,
                onText: ClientResources.CrossRegionComputeQuotas.toggleText
            },
            visible: ko.computed(container, () => this.viewsRadioButton.value() === "workspace")
        });
    }

    private _initializeLoadingText(): void {
        const { container } = this.context;

        this.loadingText = CustomHtml.create(container, {
            htmlTemplate: `<div>${ClientResources.CrossRegionComputeQuotas.loadingText}</div>`,
            visible: ko.observable(false)
        });
    }

    private _initializeErrorMessages(): void {
        const { container } = this.context;

        this.errorMessage = InfoBox.create(container, {
            style: InfoBox.Style.Error,
            text: ko.observable(""),
            visible: ko.observable(false)
        });

        this.crossLocationWarning = InfoBox.create(container, {
            canClose: true,
            style: InfoBox.Style.Warning,
            text: ko.observable(""),
            visible: ko.observable(false),
            onCloseClickCallback: () => {
                this.crossLocationWarning.text("");
                this.crossLocationWarning.visible(false);
            }
        });
    }

    private _initializeTabControls(): void {
        const { container } = this.context;

        this.subscriptionUsageTabs = TabControl.create(container, {
            tabs: [
                Section.create(container, {
                    name: ClientResources.CrossRegionComputeQuotas.dedicatedCoresSectionLabel,
                    leftLabelPosition: true,
                    children: ko.observableArray([])
                }),
                Section.create(container, {
                    name: ClientResources.CrossRegionComputeQuotas.lowPriorityCoresSectionLabel,
                    leftLabelPosition: true,
                    children: ko.observableArray([])
                })
            ]
        });

        this.workspaceUsageTabs = TabControl.create(container, {
            tabs: [
                Section.create(container, {
                    name: ClientResources.CrossRegionComputeQuotas.dedicatedCoresSectionLabel,
                    leftLabelPosition: true,
                    children: ko.observableArray([])
                }),
                Section.create(container, {
                    name: ClientResources.CrossRegionComputeQuotas.lowPriorityCoresSectionLabel,
                    leftLabelPosition: true,
                    children: ko.observableArray([])
                })
            ]
        });
    }

    private _loadView(state: IComputeUsagesState): void {
        const { container } = this.context;
        const { canShowUsages, error, loading, usages = [], vmSizes = [], workspaceLocations } = state;

        this.loadingText.visible(false);
        this.errorMessage.visible(false);
        this.subscriptionUsageTabs.visible(false);
        this.workspaceUsageTabs.visible(false);

        if (loading) {
            this.loadingText.visible(true);
        } else if (error && !canShowUsages) {
            this.errorMessage.text(error);
            this.errorMessage.visible(true);
        } else {
            if (error && canShowUsages) {
                this.crossLocationWarning.text(error);
                this.crossLocationWarning.visible(true);
            }
            const viewType = this.viewsRadioButton.value();
            const isSubscriptionView = viewType === "computeFamily";
            const usageTab = isSubscriptionView ? this.subscriptionUsageTabs : this.workspaceUsageTabs;
            const tabs = usageTab.tabs();
            const dedicatedTab = tabs[QuotaUsagesTree.QuotaUsageCoresType.Dedicated];
            dedicatedTab.children([
                QuotaUsagesTree.create(container, {
                    coresType: QuotaUsagesTree.QuotaUsageCoresType.Dedicated,
                    groupingType: viewType,
                    location: this._location,
                    showCrossLocationComputes: isSubscriptionView ? this.crossLocationQuotaToggleSubscription.value : ko.observable(true),
                    subscriptionId: this._subscriptionId,
                    tenantId: this._tenantId,
                    usages,
                    vmSizes,
                    workspaceLocations
                })
            ]);

            const lowPriorityTab = tabs[QuotaUsagesTree.QuotaUsageCoresType.LowPriority];
            lowPriorityTab.children([
                QuotaUsagesTree.create(container, {
                    coresType: QuotaUsagesTree.QuotaUsageCoresType.LowPriority,
                    groupingType: viewType,
                    location: this._location,
                    showCrossLocationComputes: isSubscriptionView ? this.crossLocationQuotaToggleSubscription.value : ko.observable(true),
                    subscriptionId: this._subscriptionId,
                    tenantId: this._tenantId,
                    usages,
                    vmSizes,
                    workspaceLocations
                })
            ]);

            usageTab.visible(true);
        }
    }

    private async _fetchLocations(): Promise<void> {
        const { model, parameters } = this.context;
        const { machineLearningServicesData } = model;

        try {
            const responses = await Promise.all([
                machineLearningServicesData.getSubscriptionLocations(this._subscriptionId()),
                machineLearningServicesData.getComputeAvailableLocations(this._subscriptionId())
            ]);
            const locations = (responses[0] && responses[0].value) || [];
            const availableLocations = responses[1] || [];

            let availableLocationObjs = locations.filter((locationObj: any) => availableLocations.indexOf(locationObj.displayName) !== -1);
            if (!availableLocationObjs || availableLocationObjs.length === 0) {
                availableLocationObjs = locations;
            }

            const locationDropDownItems: DropDown.Item<string>[] = availableLocationObjs.map((l: any) => {
                return {
                    text: ko.observable(l.displayName),
                    value: l.name
                };
            });

            let location: string;
            if (parameters.location) {
                location = parameters.location;
            }

            if (location) {
                if (MsPortalFx.find(locationDropDownItems, (item: DropDown.Item<string>) => item.value === location)) {
                    this.locationsDropDown.value(location);
                } else {
                    locationDropDownItems.push({
                        text: ko.observable(location),
                        value: location
                    });
                    this.locationsDropDown.value(location);
                }
            } else {
                this.locationsDropDown.value(null);
            }

            locationDropDownItems.sort((a: DropDown.Item<string>, b: DropDown.Item<string>) => a.value.localeCompare(b.value));
            this.locationsDropDown.items(locationDropDownItems);
        } catch (e) {
            this.locationsDropDown.items([]);
            this.locationsDropDown.value(null);
        }
    }

    private _onSubscriptionChange(): void {
        this._fetchQuota();
    }

    private _onLocationChange(): void {
        this._fetchQuota();
    }

    private _fetchQuota(resetWorkspacesCache: boolean = false): Promise<void> {
        const { model } = this.context;

        if (!this._subscriptionId()) {
            this.subscriptionsDropDown.triggerValidation();
            return;
        }

        if (!this._location()) {
            this.locationsDropDown.triggerValidation();
            return;
        }

        const subscriptionId = this._subscriptionId();
        const location = this._location();

        this._setComputeUsageState({ loading: true });

        return Q.allSettled([
            model.mlComputeQuotaData.getSubscriptionWorkspaces(subscriptionId, resetWorkspacesCache),
            model.mlComputeQuotaData.getSupportedVMSizes(subscriptionId, location),
            model.mlComputeQuotaData.getQuotaUsage(subscriptionId, location, true)
        ]).then(([allWorkspacesResult, vmSizesResult, usagesResult]) => {
            const allWorkspacesAvailable = allWorkspacesResult.state === "fulfilled";
            const vmSizesAvailable = vmSizesResult.state === "fulfilled";
            const usagesAvailable = usagesResult.state === "fulfilled";

            if (allWorkspacesAvailable) {
                this._crossLocationInfoAvailable(true);
            }

            if (usagesAvailable && vmSizesAvailable) {
                const allWorkspaces = allWorkspacesResult.value ? (allWorkspacesResult.value as SubscriptionWorkspacesResult).value || [] : [];
                const usages = usagesResult.value ? (usagesResult.value as QuotaUsageResult).value || [] : [];
                const vmSizes = vmSizesResult.value ? (vmSizesResult.value as VMSizesResult).amlCompute || [] : [];

                const workspaceLocations = new Map<string, string>();
                for (const workspace of allWorkspaces) {
                    const workspaceArmId = ArmId.parse(workspace.id);
                    const fullWorkspaceName = getFullWorkspaceName(workspaceArmId.resourceGroup, workspaceArmId.resourceIds[0]);
                    if (!workspaceLocations.has(fullWorkspaceName)) {
                        workspaceLocations.set(fullWorkspaceName, workspace.location);
                    }
                }

                let error: string | undefined;
                if (!allWorkspacesAvailable && allWorkspacesResult.reason) {
                    error = ClientResources.CrossRegionComputeQuotas.QuotaUsageBlade.crossLocationUsagesNotAvailable.format(
                        Utilities.extractErrorMessage(allWorkspacesResult.reason)
                    );
                }

                this._setComputeUsageState({ usages, vmSizes, workspaceLocations, error, canShowUsages: true });
            } else {
                const errors: string[] = [];
                if (!allWorkspacesAvailable && allWorkspacesResult.reason) {
                    errors.push(Utilities.extractErrorMessage(allWorkspacesResult.reason));
                }

                if (!usagesAvailable && usagesResult.reason) {
                    errors.push(Utilities.extractErrorMessage(usagesResult.reason));
                }

                if (!vmSizesResult && vmSizesResult.reason) {
                    errors.push(Utilities.extractErrorMessage(vmSizesResult.reason));
                }

                if (errors.length > 0) {
                    this._setComputeUsageState({
                        error: ClientResources.CrossRegionComputeQuotas.QuotaUsageBlade.usagesNotAvailable.format(errors.join(". "))
                    });
                }
            }
        });
    }

    private _setComputeUsageState(state: IComputeUsagesState): void {
        this._computeUsagesState = state;
        this._loadView(state);
    }
}
