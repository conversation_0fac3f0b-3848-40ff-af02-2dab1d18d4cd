import { ClickableLink } from "Fx/Composition";
import * as TemplateBlade from "Fx/Composition/TemplateBlade";
import * as CustomHtml from "Fx/Controls/CustomHtml";
import * as Essentials from "Fx/Controls/Essentials";
import { ResourceLayoutContract } from "Fx/Controls/Essentials";
import * as Section from "Fx/Controls/Section";
import * as ClientResources from "Resx/ClientResources";
import Card from "../../../Shared/Cards";
import LaunchResource from "../../../Shared/LaunchResource"
import { Logging } from "../../../Shared/Logging";
import { DataContext } from "../../RegistryResourceArea";
import Utilities from "../../../Shared/Utilities";
import Icons = require("../../../Shared/Icons");
import { ArmId } from "Fx/ResourceManagement";
import * as Toolbar from "Fx/Controls/Toolbar";
import Images = MsPortalFx.Base.Images;
import { DialogButton } from "Fx/Composition/Dialog";
import Actions from "../../../Shared/Actions";

import Enums = require("../../../Shared/Enums");
import MLRegistryDataModels = require("../../../MLServicesDataModels/MachineLearningRegistry");
import ExtensionDefinition = require("../../../_generated/ExtensionDefinition");
import { getEnvironmentValue } from "Fx";

/**
 * Overview blade provides the overview of resource on resource menu.
 * Learn more about decorator based blades at: https://aka.ms/portalfx/nopdl
 */
@TemplateBlade.Decorator({
    htmlTemplate:
        `
    <div data-bind='pcControl: essentialsViewModel'></div>
    <div class='msportalfx-padding'>
    </div>
    <div class='msportalfx-padding'>
        <div class='msportalfx-form ext-ml-section-header' data-bind='pcControl: infoSection'></div>
        <div class='ext-ml-card'>
            <div data-bind='pcControl: mlsRegistryManageCard'></div>
        </div>
        <div class='ext-ml-card'>
            <div data-bind='pcControl: mlsRegistryShareCard'></div>
        </div>
    </div>
    <div class='msportalfx-padding'>
        <div data-bind='pcControl: registryPortalNavigation'></div>
    </div>
    `,
    styleSheets: ["../../../Shared/Styles/CustomStyles.css"],
    isPinnable: false,
    forAsset: { assetType: ExtensionDefinition.AssetTypeNames.registry, assetIdParameter: "id" },
    forExport: true
})
@TemplateBlade.InjectableModel.Decorator(DataContext)
export class RegistryOverviewBlade {
    public title = "";
    public subtitle = "";
    public context: TemplateBlade.Context<RegistryOverviewBlade.RegistryOverviewBladeParameters, DataContext>;
    private _machineLearningRegistryEntityView: MsPortalFx.Data.EntityView<MLRegistryDataModels.MachineLearningRegistryServicesExtended, string>;
    private _machineLearningRegistry: MLRegistryDataModels.MachineLearningRegistryServicesExtended = null;

    public essentialsViewModel: KnockoutObservable<ResourceLayoutContract>;
    public infoSection: Section.Contract;

    public mlsRegistryManageCard: CustomHtml.Contract;
    public mlsRegistryShareCard: CustomHtml.Contract;
    public registryPortalNavigation: CustomHtml.Contract;

    private _deleteCommandButton: Toolbar.ToolbarItems.BasicButtonContract;

    public onInitialize() {
        const { container, parameters } = this.context;

        this._setInitialState();

        this.infoSection = Section.create(container, {
            name: ClientResources.titleMonitoringLens,
            children: []
        });

        return this._loadRegistry(parameters.id);
    }

    private _setInitialState(): void {
        const { model, container } = this.context;
        this._machineLearningRegistryEntityView = model.registryData.machineLearningRegistryEntityExtended.createView(container);
    }

    private _loadRegistry(resourceId: string): Q.Promise<void> {
        const { container, model } = this.context;
        const deferred = Q.defer();

        const machineLearningRegistryPromise = this._machineLearningRegistryEntityView.fetch(resourceId);

        const permissionsPromise = MsPortalFx.Base.Security.hasPermission(resourceId, [ExtensionDefinition.AssetTypes.MachineLearningServices.Permissions.readServices]);

        Q.all([permissionsPromise, machineLearningRegistryPromise]).then(async (values) => {
            const hasAccess = values[0];
            this._machineLearningRegistry = this._machineLearningRegistryEntityView.item();
            this._initializeCommandBar();
            this._initializeLifeCycleStateObserver();

            await this._initializeEssentials();
            await this._initializeLinks();

            if (!hasAccess) {
                container.unauthorized();
            } else {
                deferred.resolve();
            }
        }, (reason) => {
            deferred.reject(reason);
        });
        return deferred.promise;
    }

    // Initializes properties for the essentials part
    private async _initializeEssentials(): Promise<void> {
        const { container, parameters } = this.context;
        const resourceId = parameters.id;
        const workspaceArmId = ArmId.parse(parameters.id);
        const launchButtonUrl = await Utilities.generateRegistryUrl(workspaceArmId.resourceName);
        const managedResourceGroupId = this._machineLearningRegistry.properties?.().managedResourceGroup?.().resourceId?.() || "";
        const managedResourceGroupArray = managedResourceGroupId.split('/');
        const managedResourceGroupUrl = await Utilities.generateManagedResourceGroupUrl(managedResourceGroupId);
        const studioWebURLProperty: Essentials.Item = {
            label: ClientResources.Overview.Essentials.registryWebURLText,
            value: launchButtonUrl,
            onClick: new ClickableLink(launchButtonUrl, "_blank", undefined, () => {
                MsPortalFx.Base.Diagnostics.Telemetry.trace({
                    action: "LaunchRegistryLinkClicked",
                    source: "RegistryOverviewBlade",
                    data: {
                        registryName: workspaceArmId.resourceName
                    }
                });
            })
        };

        const mlFlowURLProperty: Essentials.Item = {
            label: ClientResources.Overview.Essentials.mlFlowWebURLText,
            value: this._machineLearningRegistry.properties().mlFlowRegistryUri
        };

        const managedResourceGroupPorperty: Essentials.Item = {
            label: ClientResources.Overview.Essentials.managedResourceGroup,
            value:  managedResourceGroupArray[managedResourceGroupArray.length - 1],
            onClick: new ClickableLink(managedResourceGroupUrl, "_blank", undefined, () => {
                MsPortalFx.Base.Diagnostics.Telemetry.trace({
                    action: "LaunchRegistryLinkClicked",
                    source: "RegistryOverviewBlade",
                    data: {
                        registryName: workspaceArmId.resourceName
                    }
                });
            })
        };

        const leftItems: (Essentials.BuiltInType | Essentials.Item)[] = [
                Essentials.BuiltInType.ResourceGroup,
                Essentials.BuiltInType.Location,
                Essentials.BuiltInType.SubscriptionName,
                Essentials.BuiltInType.SubscriptionId
        ];

        const rightItems: (Essentials.Item)[] = [
            studioWebURLProperty,
            mlFlowURLProperty
        ];

        if(managedResourceGroupPorperty.value) {
            rightItems.push(managedResourceGroupPorperty)
        };

        const essentialsOptions: Essentials.CustomResourceLayoutOptions = {
            resourceId: resourceId,
            left: leftItems,
            right: rightItems,
            hiddenChangeLink: [
                Essentials.BuiltInType.ResourceGroup,
                Essentials.BuiltInType.SubscriptionName
            ]
        };

        const essentialsVM = Essentials.createCustomResourceLayout(container, essentialsOptions);
        this.essentialsViewModel = ko.observable(essentialsVM);
    }

    private async _initializeLinks(): Promise<void> {
        const { container, menu, parameters } = this.context;
        const workspaceArmId = ArmId.parse(parameters.id);
        const launchButtonUrl = await Utilities.generateRegistryUrl(workspaceArmId.resourceName);

        const machineLearningServicesManageRegistryUrl = MsPortalFx.getEnvironmentValue("machineLearningServicesManageRegistryUrl");
        const mlsDocumentationClickableLink = new ClickableLink(machineLearningServicesManageRegistryUrl, '_blank', undefined, (keypress) => {
            Logging.LogInfoBoxUrlItemClick("machineLearningServicesManageRegistryUrl", machineLearningServicesManageRegistryUrl);
        });

        const machineLearningServicesShareRegistryUrl = MsPortalFx.getEnvironmentValue("machineLearningServicesShareRegistryUrl");
        const mlsForumClickableLink = new ClickableLink(machineLearningServicesShareRegistryUrl, '_blank', undefined, (keypress) => {
            Logging.LogInfoBoxUrlItemClick("machineLearningServicesShareRegistryUrl", machineLearningServicesShareRegistryUrl);
        });
        const launchRegistryPortalClickableLink = new ClickableLink(launchButtonUrl, "_blank", undefined, () => {
            MsPortalFx.Base.Diagnostics.Telemetry.trace({
                action: "LaunchRegistryLinkClicked",
                source: "RegistryOverviewBlade",
                data: {
                    registryName: workspaceArmId.resourceName
                }
            });
        })

        this.mlsRegistryManageCard = Card.createCard(container, {
            clickableLink: mlsDocumentationClickableLink,
            header: ClientResources.textMachineLearningServicesManageRegistryLinkHeader,
            headerAriaLabel: `${ClientResources.quickLinkUnderOverviewBladeAriaLabel} ${ClientResources.textMachineLearningServicesManageRegistryLinkHeader}.`,
            body: ClientResources.textMachineLearningServicesManageRegistryLinkBody,
            bodyAriaDescription: ClientResources.textMachineLearningServicesManageRegistryLinkBody,
            icon: Icons.Icons.documentationIcon
        });
        this.mlsRegistryShareCard = Card.createCard(container, {
            clickableLink: mlsForumClickableLink,
            header: ClientResources.textMachineLearningServicesShareRegistryLinkHeader,
            headerAriaLabel: `${ClientResources.quickLinkUnderOverviewBladeAriaLabel} ${ClientResources.textMachineLearningServicesShareRegistryLinkHeader}.`,
            body: ClientResources.textMachineLearningServicesShareRegistryLinkBody,
            bodyAriaDescription: ClientResources.textMachineLearningServicesShareRegistryLinkBody,
            icon: Icons.Icons.documentationIcon
        });

        this.registryPortalNavigation = LaunchResource.createLaunchResource(container, {
            clickableLink: launchRegistryPortalClickableLink,
            header: ClientResources.Overview.Banner.Registry.title,
            body:  ClientResources.Overview.Banner.Description.label.replace("{}", ""),
            icon: Icons.Icons.webWorkspaceIcon,
            launchText: ClientResources.Overview.Banner.Registry.launchButtonText,
            documentationUrl: getEnvironmentValue("machineLearningServicesDocumentationUrl"),
            urlHelpText : ClientResources.Overview.Banner.Description.learnMoreText
        });
    }

    private _initializeCommandBar(): void {
        const { container } = this.context;
        const commandBar = Toolbar.create(container, {items: []});

        this._deleteCommandButton = this._createDeleteCommand();

        commandBar.setItems([this._deleteCommandButton]);

        container.commandBar = commandBar;
    }

    private _createDeleteCommand(): Toolbar.ToolbarItems.BasicButtonContract {
        const { model, container } = this.context;
        return Toolbar.ToolbarItems.createBasicButton(container, {
            label: ClientResources.commandDelete,
            icon: Images.Delete(),
            disabled: false,
            onClick: env => {
                container.openDialog({
                    target: env.target,
                    buttons: [
                        {
                            button: DialogButton.Delete,
                            displayText: ClientResources.commandDelete
                        },
                        { button: DialogButton.Cancel }
                    ],
                    telemetryName: "DeleteMachineLearningRegistryButton",
                    title: ClientResources.titleMachineLearningServicesRegistryDeleteConfirmationMessageBox,
                    content: ClientResources.confirmationMachineLearningRegistryDelete.format(this._machineLearningRegistry.name()),
                    onClosed: dialogResult => {
                        if (dialogResult.button === DialogButton.Delete) {
                            // kickoff the delete async operation
                            // don't wait for it to complete (allow dialog to close)
                            // notifications will show delete state
                            Actions.deleteMachineLearningRegistry({
                                dataContext: model,
                                machineLearningRegistry: this._machineLearningRegistry
                            });
                        }
                    }
                });
            }
        });
    }

    private _initializeLifeCycleStateObserver(): void {
        const { container } = this.context;
        ko.reactor(container, () => {
            const lifeCycleEvent = this._machineLearningRegistry.lifeCycleEvent();
            let newStatusBar: TemplateBlade.StatusBar = undefined;
            switch (lifeCycleEvent) {
                case Enums.MachineLearningRegistryLifecycleEvents.DeleteRegistryExecuting:
                    newStatusBar = {
                        text: ClientResources.progressMachineLearningRegistryDeleteExecuting,
                        state: TemplateBlade.ContentState.Info
                    };
                    this._deleteCommandButton.disabled(true);
                    break;
                case Enums.MachineLearningRegistryLifecycleEvents.DeleteRegistrySucceeded:
                    newStatusBar = {
                        text: ClientResources.progressNotificationMachineLearningRegistryDeleteSuccessTitle,
                        state: TemplateBlade.ContentState.Info
                    };
                    this._deleteCommandButton.disabled(true);
                    break;
                case Enums.MachineLearningRegistryLifecycleEvents.DeleteRegistryError:
                    newStatusBar = {
                        text: ClientResources.progressMachineLearningRegistryDeleteError,
                        state: TemplateBlade.ContentState.Error
                    };
                    this._deleteCommandButton.disabled(false);
                    break;
                default:
                    this._deleteCommandButton.disabled(false);
                    break;
            }
            container.statusBar(newStatusBar);
        });
    }
};