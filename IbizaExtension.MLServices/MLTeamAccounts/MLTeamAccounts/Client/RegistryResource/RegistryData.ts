import RegistryDataModel = require("./DataModels/RegistryDataModel");
import * as Di from "Fx/DependencyInjection";
import MLRegistryDataModels = require("../MLServicesDataModels/MachineLearningRegistry");
import Utilities from "../Shared/Utilities";
import { MachineLearningServicesLifecycleEvents } from "Shared/Enums";
import { Logging, ActionModifier } from "../Shared/Logging";
import Constants = require("../Shared/Constants");
import ClientResources = require("Resx/ClientResources");
import ExtensionDefinition = require("../_generated/ExtensionDefinition");

@Di.Class()
export class RegistryData {
    public armEndpoint: string = MsPortalFx.getEnvironmentValue("armEndpoint");
    private static RegistryType = "Microsoft_Azure_MLTeamAccounts.DataModels.Registry";
    private static source = "RegistryData";
    public registryApiVersion: string = "api-version=" + MsPortalFx.getEnvironmentValue("machineLearningRegistryApiVersion");

    public registryQuery = new MsPortalFx.Data.QueryCache<RegistryDataModel.Registry, any>({
        entityTypeName: RegistryData.RegistryType,
        sourceUri: (params: any) => { return params; },
        supplyData: (httpMethod: string, uri: string, headers: StringMap<any>, data: any, params: any, entryLifetime: MsPortalFx.Base.LifetimeManager) => {
                return Q([]);
        },
        poll: true
    });

    public machineLearningRegistryEntity = new MsPortalFx.Data.EntityCache<MLRegistryDataModels.MachineLearningRegistryServices, string>({
        sourceUri: MsPortalFx.Data.uriFormatter(this.armEndpoint + "{id}?" + this.registryApiVersion, false),
        supplyData: (httpMethod: string, uri: string, headers?: StringMap<any>, data?: any, params?: any) => {
            return Utilities.armGetRequest(uri);
        }
    });

    public machineLearningRegistryEntityExtended = new MsPortalFx.Data.EntityCache<MLRegistryDataModels.MachineLearningRegistryServicesExtended, string>({
        sourceUri: MsPortalFx.Data.uriFormatter(this.armEndpoint + "{id}?" + this.registryApiVersion, false),
        supplyData: (httpMethod: string, uri: string, headers: StringMap<any>, data: any, params: any, entryLifetime: MsPortalFx.Base.LifetimeManager) => {
            const resourceId: string = params;
            const machineLearningServicesEntityView = this.machineLearningRegistryEntity.createView(entryLifetime);
            return machineLearningServicesEntityView.fetch(resourceId).then(() => {
                // extend the machine learning service entity with additional properties
                const entity = ko.toJS(machineLearningServicesEntityView.item());
                entity.lifeCycleEvent = MachineLearningServicesLifecycleEvents.None;
                return entity;
            });
        },
        extendEntryLifetimes: true
    });
    public machineLearningRegistryApiVersion: string = "api-version=" + MsPortalFx.getEnvironmentValue("machineLearningRegistryApiVersion");

    public locationsApiVersion: string = "api-version=2022-05-01";

    public checkRegistryAvailability(subscriptionId: string, resourceGroup: string, registryName: string): Q.Promise<MsPortalFx.ViewModels.ValidationResult> {
        const key = Logging.StartTrace(RegistryData.source, "checkRegistryAvailability", ActionModifier.Start);
        const uri = `${this.armEndpoint}/subscriptions/${subscriptionId}/resourcegroups/${resourceGroup}/providers/${Constants.machineLearningServicesResourcesProvider}/${Constants.machineLearningRegistries}/${registryName}?${this.machineLearningRegistryApiVersion}`;
        const deferred = Q.defer<MsPortalFx.ViewModels.ValidationResult>();
        Utilities.armGetRequest(uri).then((response: any) => {
            Logging.EndTrace(key, ActionModifier.Success);
            let message = ClientResources.validationRegistryNameAlreadyInUse;
            deferred.resolve({
                valid: false,
                message
            });
        }, (failureResponse: MsPortalFx.Base.Net2.ErrorResponse<any>) => {
            if (failureResponse.jqXHR.status !== 404) {
                Logging.LogAjaxFailure(failureResponse.jqXHR, "checkRegistryAvailability");
                Logging.EndTrace(key, ActionModifier.Failed);
            } else {
                Logging.EndTrace(key, ActionModifier.UserError);
            }

            deferred.resolve({
                valid: true,
                message: ""
            });
        });
        return deferred.promise;
    }

    public getResourceGroup(subscriptionId: string, resourceGroupName: string): Q.Promise<HubsExtension.AzureContracts.ResourceGroup> {
        const key = Logging.StartTrace(RegistryData.source, "getSelectedResourceGroupTags", ActionModifier.Start);
        const uri = `${this.armEndpoint}/subscriptions/${subscriptionId}/resourcegroups/${resourceGroupName}?api-version=2021-04-01`
        const deferred = Q.defer<any>();
        Utilities.armGetRequest(uri).then((response: HubsExtension.AzureContracts.ResourceGroup) => {
            Logging.EndTrace(key, ActionModifier.Success);
            deferred.resolve(response);
        }, (failureResponse: MsPortalFx.Base.Net2.ErrorResponse<any>) => {
            if (failureResponse.jqXHR.status !== 404) {
                Logging.LogAjaxFailure(failureResponse.jqXHR, "getSelectedResourceGroupTags");
                Logging.EndTrace(key, ActionModifier.Failed);
            } else {
                Logging.EndTrace(key, ActionModifier.UserError);
            }

            deferred.resolve(failureResponse);
        });
        return deferred.promise;
    }

    public deleteMachineLearningRegistry(machineLearningRegistry: MLRegistryDataModels.MachineLearningRegistryServices): Q.Promise<boolean> {
        const key = Logging.StartTrace(RegistryData.source, "deleteMachineLearningRegistry", ActionModifier.Start);
        const uri = `${this.armEndpoint}${machineLearningRegistry.id()}?${this.machineLearningRegistryApiVersion}`;
        const defer = Q.defer<boolean>();
        Utilities.armDeleteRequest(uri).then(() => {
            Logging.EndTrace(key, ActionModifier.Success);
            MsPortalFx.UI.AssetManager.notifyAssetDeleted(ExtensionDefinition.AssetTypeNames.registry, machineLearningRegistry.id());
            this.registryQuery.applyChanges((params, dataSet) => {
                dataSet.removeItem(machineLearningRegistry);
            });
            defer.resolve(true);
        }, (errorResponse: MsPortalFx.Base.Net2.ErrorResponse<any>) => {
            Logging.EndTrace(key, ActionModifier.Failed);
            Logging.LogAjaxFailure(errorResponse.jqXHR, "deleteMachineLearningRegistry");
            defer.reject(errorResponse);
        });
        return defer.promise;
    }
}