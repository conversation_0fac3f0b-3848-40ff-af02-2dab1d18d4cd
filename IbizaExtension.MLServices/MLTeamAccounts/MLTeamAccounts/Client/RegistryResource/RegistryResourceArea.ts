import Data = require("./RegistryData");
import ProviderRegistrationData = require("../Resource/Data/ProviderRegistrationData");
import * as Di from "Fx/DependencyInjection";

@Di.Class()
export class DataContext {
    public registryData: Data.RegistryData;
    public providerRegistrationData: ProviderRegistrationData.ProviderRegistrationData;

    constructor(
        registryData: Data.RegistryData,
        providerRegistrationData: ProviderRegistrationData.ProviderRegistrationData,
    ) {
        this.registryData = registryData;
        this.providerRegistrationData = providerRegistrationData;
    }
}