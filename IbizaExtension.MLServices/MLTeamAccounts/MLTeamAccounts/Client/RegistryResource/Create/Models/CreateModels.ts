import * as ResourceGroupDropDown from 'Fx/Controls/ResourceGroupDropDown';

/**
 * Several create functions expect a datamodel that has these properties at a minimum.
 */
export interface Basics {
    subscription: KnockoutObservableBase<MsPortalFx.Azure.Subscription>;
    resourceGroup: KnockoutObservableBase<ResourceGroupDropDown.Value>;
    location: KnockoutObservableBase<MsPortalFx.Azure.Location>;
    locations: KnockoutObservableArray<MsPortalFx.Azure.Location>;
}