/**
 * Copyright (c) Microsoft Corporation.  All rights reserved.
 */

import * as BladeBase from 'Fx/Composition/BladeBase';
import { Container } from 'Fx/Composition/TemplateBlade';
import * as LocationDropDown from 'Fx/Controls/LocationDropDown';

import { CommonContext, CommonControl } from '../../../Resource/Create/Models/CreateModels';

import * as NoPdlUiHelpers from '../../../Resource/Create/Utilities/NoPdlUiHelpers';

import Constants = require("../../../Shared/Constants");

import Resources = require("Resx/ClientResources");

export interface Context extends CommonContext {
    container: Container;
    dataModel: {
        basics: {
            subscription: KnockoutObservableBase<MsPortalFx.Azure.Subscription>;
            location: KnockoutObservableBase<MsPortalFx.Azure.Location>;
            locations: KnockoutObservableArray<MsPortalFx.Azure.Location>;
        };
    };
    provisioning: BladeBase.DoesProvisioning.Provisioning;
}

export interface BasicOptions extends CommonControl {
    initialLocationName: string[];
    resourceTypes: string[] | KnockoutObservableBase<string[]>;
}

export interface Options extends BasicOptions {
}

export function create(context: Context, createOptions?: Options): LocationDropDown.Contract {
    const { dataModel, provisioning } = context;
    const subscriptionObs = dataModel.basics.subscription;
    const subscriptionId = ko.pureComputed(() => subscriptionObs() && subscriptionObs().subscriptionId || "");

    const validations = (<Array<MsPortalFx.ViewModels.CustomValidation | MsPortalFx.ViewModels.RequiredValidation>>[
            new MsPortalFx.ViewModels.RequiredValidation()]).concat(
            createOptions && createOptions.customValidations || []);

    const locationNames = createOptions.initialLocationName || (provisioning.initialValues && provisioning.initialValues.locationNames);
    const initialLocationName = locationNames;
    const locationDropDown = LocationDropDown.create(context.container, {
        label: createOptions && createOptions.overrideLabel || Resources.createLocationLabelDefault,
        subscriptionId: subscriptionId,
        initialLocationName: initialLocationName,
        value: dataModel.basics.location,
        resourceTypes: ko.observable([Constants.machineLearningWorkspaceType]),
        validations: validations,
        infoBalloonContent: createOptions.overrideInfoBalloonContent
    });

    dataModel.basics.location.subscribe(context.container, value => {
        if (value && value.name) {
            const locations = locationDropDown.fetchedValues();
            const existingItem = MsPortalFx.find(locations, item => item.name === value.name);
            if (existingItem && existingItem !== value) {
                dataModel.basics.location(existingItem);
            }

            dataModel.basics.locations = ko.observableArray(locations);
        }
    });

    return locationDropDown;
}
