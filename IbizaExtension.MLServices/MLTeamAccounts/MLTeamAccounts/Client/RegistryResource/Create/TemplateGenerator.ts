import Resources = require("Resx/ClientResources");

import * as Provisioning from "Fx/ResourceManagement/Provisioning";
import * as Constants from "../../Shared/Constants";

import { ArmId } from "Fx/ResourceManagement";
import { TelemetryLoggingOptions } from "../../Resource/Create/Controls/CreateFormControl";
import { RegionsDataModel, WizardContext } from "./ViewModels/RegistryModels";
import { getPrivateDnsZoneName } from "../../Shared/NetworkUtilities";
import { ConnectivityMethod, PrivateEndpointDetails } from "../../Resource/Common/NetworkingDataModel";

const provisioningHashUniqueString = 'iwuh-wccbe-tt0-2enb-ioes-849e-tircx-guw6';

export function getTemplateDeploymentOptions(context: WizardContext): Q.Promise<Provisioning.DeployTemplateOptions> {
  const { dataModel, dataContext } = context;
  const { basics, regions, networking } = dataModel;
  const { registryData } = dataContext;

  const {
    location,
    resourceGroup,
    subscription,
    registryName: regName,
    registryDecription: regDescription
  } = basics;

  // Basics
  const subscriptionId = subscription().subscriptionId;
  const resourceGroupName = resourceGroup().value;
  const locationName = location().name;
  const registryName = regName();
  const registryDescription = regDescription();

  //Regions
  const storageRegions = getStorageRegions(locationName, regions);

  const customerCreatedTags = generateTagsMap(context, registryDescription);

  //Managed RG tags
  var currentResourceGroupSelected = registryData.getResourceGroup(subscriptionId, resourceGroupName.name);
  return currentResourceGroupSelected.then(selectedResourceGroup => {
    const emptyStringMap: StringMap<any> = {};
    const managedResourceGroupTags = selectedResourceGroup.tags ?? emptyStringMap ;
    const parameters: StringMap<any> = {
      registryName: registryName,
      locations: storageRegions,
      resourceTags: customerCreatedTags,
      primaryLocation: locationName,
      managedResourceGroupTags: managedResourceGroupTags,
      resourceGroupName: resourceGroupName.name
    };

    parameters["publicNetworkAccess"] = networking.connectivityMethod() == ConnectivityMethod.PrivateEndpoint ? "Disabled" : "Enabled";
    const privateEndpointDetails = networking.getPrivateEndpointDetails();
    if (privateEndpointDetails) {
      parameters["privateEndpointName"] = privateEndpointDetails.name;
      parameters["subnetOption"] = "existing";
      parameters["subnetName"] = privateEndpointDetails.subnetName;
      parameters["vnetOption"] = "existing";
      parameters["vnetName"] = privateEndpointDetails.vnetName;
      parameters["vnetResourceGroupName"] = ArmId.parse(privateEndpointDetails.vnetId).resourceGroup;
      parameters["privateEndpointType"] = "AutoApproval";
      parameters["privateEndpointResourceGroupName"] = privateEndpointDetails.resourceGroupName;
      parameters["privateEndpointSubscription"] = privateEndpointDetails.subscription;
    }

    const providersPath = `/subscriptions/${subscriptionId}/resourcegroups/${resourceGroupName.name}/providers`;
    const resourceIdFormattedString = `${providersPath}/${Constants.machineLearningRegistryType}/${registryName}`;
    return Q.Promise((resolve, reject) => {
      resolve({
        subscriptionId: subscriptionId,
        resourceGroupName: resourceGroupName.name,
        resourceGroupLocation: locationName,
        parameters: parameters,
        deploymentName: Constants.machineLearningServicesResourcesProvider,
        resourceProviders: [Constants.machineLearningServicesResourcesProvider],
        resourceId: resourceIdFormattedString,
        provisioningHash: provisioningHashUniqueString,
        templateJson: getResourceTemplateJsonV2(privateEndpointDetails),
      });
    });
  });
}

function getStorageRegions(locationName: string, regions: RegionsDataModel) {
  var result:string[] = [locationName];
  if (regions.regionsList() && regions.regionsList().length > 0) {
    return result.concat(regions.regionsList());
  }

  return result;
}

export function getTelemetryLoggingOptions(context: WizardContext, deploymentOptions: Provisioning.DeployTemplateOptions): TelemetryLoggingOptions {
  const { parameters = {} } = deploymentOptions;
  const optionsToLog: StringMap<any> = {};

  return {
    optionsToLog,
    parametersToLog: []
  }
}

function generateTagsMap(context: WizardContext, registryDescription: string): ReadonlyStringMap<string> {
  const { dataModel } = context;
  const result: StringMap<string> = {};
  const keyMap: StringMap<string> = {};
  dataModel.tags.tags().forEach(item => {
    result[item.key] = item.value;
    keyMap[item.key.toLowerCase()] = item.key;
  });

  if (dataModel.tags.clientTag) {
    const clientKeyInResult = keyMap["client"];
    if (clientKeyInResult) {
      delete result[clientKeyInResult];
    }

    result["client"] = dataModel.tags.clientTag;
  }

  if (!keyMap[Resources.createBladeRegistryDescriptionKey])
  {
    result[Resources.createBladeRegistryDescriptionKey] = registryDescription;
  }

  return result;
}

interface IRegistryTemplateOptions {
}

function getResourceTemplateJsonV2(privateEndpointDetails: PrivateEndpointDetails): string {
  let createRegistryTemplate: any;
  createRegistryTemplate = {
    "$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#",
    "contentVersion": "*******",
    "parameters": {
      "primaryLocation": {
        "type": "string"
      },
      "registryName": {
        "type": "string"
      },
      "resourceGroupName": {
        "type": "string"
      },
      "locations": {
        "type": "array"
      },
      "resourceTags": {
        "type": "object"
      },
      "managedResourceGroupTags": {
        "type": "object"
      },
      "privateEndpointName": {
        "type": "string",
        "defaultValue": "pe",
        "metadata": {
          "description": "Name of the private end point added to the registry"
        }
      },
      "privateEndpointResourceGroupName": {
        "type": "string",
        "defaultValue": "[parameters('resourceGroupName')]",
        "metadata": {
          "description": "Name of the resource group where the private end point is added to"
        }
      },
      "privateEndpointSubscription": {
        "type": "string",
        "defaultValue": "[subscription().subscriptionId]",
        "metadata": {
          "description": "Id of the subscription where the private end point is added to"
        }
      },
      "privateEndpointType": {
        "type": "string",
        "defaultValue": "none",
        "allowedValues": [
          "AutoApproval",
          "ManualApproval",
          "none"
        ]
      },
      "vnetResourceGroupName": {
        "type": "string",
        "defaultValue": "[parameters('resourceGroupName')]"
      },
      "vnetOption": {
        "type": "string",
        "defaultValue": "[if(equals(parameters('privateEndpointType'), 'none'), 'none', 'new')]",
        "allowedValues": [
          "new",
          "existing",
          "none"
        ],
        "metadata": {
          "description": "Determines whether or not a new VNet should be provisioned."
        }
      },
      "vnetName": {
        "type": "string",
        "defaultValue": "[concat('vn',uniqueString(parameters('resourceGroupName'), parameters('registryName')))]",
        "metadata": {
          "description": "Name of the VNet"
        }
      },
      "subnetOption": {
        "type": "string",
        "defaultValue": "[if(or(not(equals(parameters('privateEndpointType'), 'none')), equals(parameters('vnetOption'), 'new')), 'new', 'none')]",
        "allowedValues": [
          "new",
          "existing",
          "none"
        ],
        "metadata": {
          "description": "Determines whether or not a new subnet should be provisioned."
        }
      },
      "subnetName": {
        "type": "string",
        "defaultValue": "[concat('sn',uniqueString(parameters('resourceGroupName'), parameters('registryName')))]",
        "metadata": {
          "description": "Name of the subnet"
        }
      },
      "publicNetworkAccess": {
        "type": "string",
        "defaultValue": "Enabled",
        "metadata": {
          "description": "Specifies whether the workspace can be accessed by public networks or not."
        },
        "allowedValues": [
          "Enabled",
          "Disabled"
        ]
      }
    },
    "variables": {
      "vnet": "[resourceId(parameters('privateEndpointSubscription'), parameters('vnetResourceGroupName'), 'Microsoft.Network/virtualNetworks', parameters('vnetName'))]",
      "subnet": "[resourceId(parameters('privateEndpointSubscription'), parameters('vnetResourceGroupName'), 'Microsoft.Network/virtualNetworks/subnets', parameters('vnetName'), parameters('subnetName'))]",
      "enablePE": true,
      "networkRuleSetBehindVNet": {
        "defaultAction": "deny",
        "virtualNetworkRules": [
          {
            "action": "Allow",
            "id": "[variables('subnet')]"
          }
        ]
      },
      "privateEndpointSettings": {
        "name": "[concat(parameters('registryName'), '-PrivateEndpoint')]",
        "properties": {
          "privateLinkServiceId": "[resourceId('Microsoft.MachineLearningServices/registries', parameters('registryName'))]",
          "groupIds": [
            "amlregistry"
          ]
        }
      },
      "defaultPEConnections": "[array(variables('privateEndpointSettings'))]",
      "privateEndpointDeploymentName": "[concat('DeployPrivateEndpoint-', uniqueString(parameters('privateEndpointName')))]"
    },
    "resources": [
      {
        "type": "Microsoft.MachineLearningServices/registries",
        "apiVersion": "2022-10-01-preview",
        "name": "[parameters('registryName')]",
        "location": "[parameters('primaryLocation')]",
        "identity": {
          "type": "SystemAssigned"
        },
        "properties": {
          "publicNetworkAccess": "[parameters('publicNetworkAccess')]",
          "copy": [
            {
              "name": "regionDetails",
              "count": "[length(range(0, length(parameters('locations'))))]",
              "input": {
                "location": "[parameters('locations')[copyIndex('regionDetails')]]",
                "storageAccountDetails": [
                  {
                    "systemCreatedStorageAccount": {}
                  }
                ],
                "acrDetails" : [
                  {
                    "systemCreatedAcrAccount": {
                      "acrAccountSku": "Premium"
                    }
                  }
                ]
              }
            }
          ],
          "managedResourceGroupTags": "[parameters('managedResourceGroupTags')]"
        },
        "tags": "[parameters('resourceTags')]",
      },
      {
        "condition": "[and(variables('enablePE'), not(equals(parameters('privateEndpointType'), 'none')))]",
        "type": "Microsoft.Resources/deployments",
        "apiVersion": "2020-06-01",
        "name": "[variables('privateEndpointDeploymentName')]",
        "resourceGroup": "[parameters('privateEndpointResourceGroupName')]",
        "subscriptionId": "[parameters('privateEndpointSubscription')]",
        "dependsOn": [
          "[resourceId('Microsoft.MachineLearningServices/registries', parameters('registryName'))]"
        ],
        "properties": {
          "mode": "Incremental",
          "template": {
            "$schema": "https://schema.management.azure.com/schemas/2015-01-01/deploymentTemplate.json#",
            "contentVersion": "*******",
            "resources": [
              {
                "apiVersion": "2020-06-01",
                "name": "[parameters('privateEndpointName')]",
                "type": "Microsoft.Network/privateEndpoints",
                "location": "[parameters('primaryLocation')]",
                "tags": "[parameters('resourceTags')]",
                "properties": {
                  "privateLinkServiceConnections": "[if(equals(parameters('privateEndpointType'), 'AutoApproval'), variables('defaultPEConnections'), json('null'))]",
                  "manualPrivateLinkServiceConnections": "[if(equals(parameters('privateEndpointType'), 'ManualApproval'), variables('defaultPEConnections'), json('null'))]",
                  "subnet": {
                    "id": "[variables('subnet')]"
                  }
                }
              }
            ]
          }
        }
      }
    ]
  }

  // If there are private endpoints, add private dns zone related entries.
  // They needed to be added dynamically since they are different by public and private clouds.
  if (privateEndpointDetails) {
    createRegistryTemplate.resources.push(...getPrivateDnsZoneDeployments(privateEndpointDetails));
  }

  return JSON.stringify(createRegistryTemplate);
}

function getPrivateDnsZoneDeployments(privateEndpointDetails: PrivateEndpointDetails): any[] {
  const deployments: any[] = [];
  const { integrateWithPrivateDnsZones, privateDnsZones } = privateEndpointDetails;
  if (integrateWithPrivateDnsZones) {
    // See whether we need to create new zones or we need to use existing ones
    const newPrivateDnsZones = (privateDnsZones || []).filter(dns => dns.isNew);
    if (newPrivateDnsZones.length > 0) {
      const deployDnsZonesResources: any[] = [];
      for (const dnsZone of newPrivateDnsZones) {
        const dnsZoneName = getPrivateDnsZoneName(dnsZone);
        const dnsZoneArmId = ArmId.parse(dnsZone.id);
        const resources: any[] = [];

        resources.push({
          "condition": "[and(variables('enablePE'), equals(parameters('privateEndpointType'), 'AutoApproval'))]",
          "type": "Microsoft.Network/privateDnsZones",
          "apiVersion": "2018-09-01",
          "name": dnsZoneName,
          "tags": "[parameters('tagValues')]",
          "location": "global",
          "properties": {}
        });

        resources.push({
          "condition": "[and(variables('enablePE'), equals(parameters('privateEndpointType'), 'AutoApproval'))]",
          "type": "Microsoft.Network/privateDnsZones/virtualNetworkLinks",
          "apiVersion": "2018-09-01",
          "name": "[concat('" + dnsZoneName + "', '/', uniqueString(resourceId('Microsoft.Network/virtualNetworks', parameters('vnetName'))))]",
          "location": "global",
          "tags": "[parameters('tagValues')]",
          "dependsOn": [
            dnsZoneName
          ],
          "properties": {
            "virtualNetwork": {
              "id": "[variables('vnet')]"
            },
            "registrationEnabled": false
          }
        });

        deployDnsZonesResources.push({
          "type": "Microsoft.Resources/deployments",
          "apiVersion": "2020-06-01",
          "name": "[concat('DeployPrivateDnsZones-', uniqueString('" + dnsZone.id + "'))]",
          "subscriptionId": dnsZoneArmId.subscription,
          "resourceGroup": dnsZoneArmId.resourceGroup,
          "properties": {
            "mode": "Incremental",
            "template": {
              "$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#",
              "contentVersion": "*******",
              "resources": [...resources]
            }
          }
        });
      }

      deployments.push({
        "type": "Microsoft.Resources/deployments",
        "apiVersion": "2020-06-01",
        "name": "[concat('DeployPrivateDnsZones-', uniqueString(parameters('privateEndpointName')))]",
        "dependsOn": [
          "[variables('privateEndpointDeploymentName')]"
        ],
        "properties": {
          "mode": "Incremental",
          "template": {
            "$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#",
            "contentVersion": "*******",
            "resources": [...deployDnsZonesResources]
          }
        }
      });
    }

    // We need to add dns zone group for the PE in the same resource group of private endpoint
    const privateDnsZoneList: { id: string; name: string }[] = (privateDnsZones || []).map(dns => { return { name: getPrivateDnsZoneName(dns), id: dns.id } });
    if (privateDnsZoneList.length > 0) {
      deployments.push({
        "condition": "[and(variables('enablePE'), equals(parameters('privateEndpointType'), 'AutoApproval'))]",
        "type": "Microsoft.Resources/deployments",
        "apiVersion": "2020-06-01",
        "name": "[concat('DeployPrivateDnsZonesGroup-', uniqueString(parameters('privateEndpointName')))]",
        "resourceGroup": "[parameters('privateEndpointResourceGroupName')]",
        "subscriptionId": "[parameters('privateEndpointSubscription')]",
        "dependsOn": newPrivateDnsZones.length > 0
          ? ["[concat('DeployPrivateDnsZones-', uniqueString(parameters('privateEndpointName')))]"]
          : ["[variables('privateEndpointDeploymentName')]"],
        "properties": {
          "mode": "Incremental",
          "template": {
            "$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#",
            "contentVersion": "*******",
            "resources": [{
              "type": "Microsoft.Network/privateEndpoints/privateDnsZoneGroups",
              "apiVersion": "2020-03-01",
              "name": "[concat(parameters('privateEndpointName'), '/', 'default')]",
              "location": "[parameters('primaryLocation')]",
              "properties": {
                "privateDnsZoneConfigs": [
                  ...privateDnsZoneList.map(dnsZone => {
                    return {
                      "name": dnsZone.name.replace(/\./g, "-"),
                      "properties": {
                        "privateDnsZoneId": dnsZone.id
                      }
                    };
                  })
                ]
              }
            }]
          }
        }
      });
    }
  }

  return deployments;
}
