import * as ResourceGroupDropDown from 'Fx/Controls/ResourceGroupDropDown';
import * as TagsByResource from 'Fx/Controls/TagsByResource';
import { DataContext } from '../../RegistryResourceArea';
import { CreateContext } from '../../../Resource/Create/Models/CreateModels';
import { Tag } from "../../../Resource/Create/Models/CreateModels";
import { NetworkingDataModel, ConnectivityMethod } from "../../../Resource/Common/NetworkingDataModel"

import Resources = require("Resx/ClientResources");

const NetworkingResources = Resources.CreateBlade.Network;
const log = MsPortalFx.Base.Diagnostics.createLog(require);
const enum ErrorCodes {
    GetConnectivityMethodDisplayName = 1,
    ValidateStorageAccountNameForCreate = 2
}

export interface WizardContext extends CreateContext<DataModel> {
    dataContext: DataContext;
    resourceProviders: KnockoutObservableArray<string>;
    disableCreateButton: KnockoutObservable<boolean>;
}

export interface CreateUiDefOptions { }


export class DataModel {
    public basics: BasicsDataModel;
    public tags: TagsDataModel;
    public regions: RegionsDataModel;
    public networking: NetworkingDataModel;

    public constructor(client?: string) {
        this.basics = new BasicsDataModel();
        this.tags = new TagsDataModel(client);
        this.regions = new RegionsDataModel();
        this.networking = new NetworkingDataModel();
    }
}

export class BasicsDataModel {
    // Basics
    public subscription = ko.observable<MsPortalFx.Azure.Subscription>();
    public resourceGroup = ko.observable<ResourceGroupDropDown.Value>();
    public location = ko.observable<MsPortalFx.Azure.Location>();
    public locations = ko.observableArray<MsPortalFx.Azure.Location>();
    // ML specific
    public registryName = ko.observable<string>();
    public registryDecription = ko.observable<string>();
}

export class RegionsDataModel {
    public regionsList = ko.observable<Array<string>>();
}

export class TagsDataModel {
    public tags = ko.observableArray<Tag>();
    public tagsByResource = ko.observableArray<TagsByResource.TaggedResource>();
    public resourceTypes = ko.observable<StringMap<string>>();
    public disableTagsGrid = ko.observable<boolean>(false);

    constructor(public clientTag?: string) {
    }
}

export function getConnectivityMethodDisplayName(connectivityMethod: ConnectivityMethod, toLower = false): string {
    let result: string;

    switch (connectivityMethod) {
        case ConnectivityMethod.PublicEndpointAllNetworks:
            result = NetworkingResources.ConnectivityMethod.allNetworks;
            break;
        case ConnectivityMethod.PrivateEndpoint:
            result = NetworkingResources.ConnectivityMethod.private;
            break;
        default:
            log.error(`Did not find a matching display name for connectivity method, defaulting to '${NetworkingResources.ConnectivityMethod.allNetworks}'`,
                ErrorCodes.GetConnectivityMethodDisplayName,
                connectivityMethod);

            result = NetworkingResources.ConnectivityMethod.allNetworks;
            break;
    }

    if (toLower && result) {
        result = result.toLocaleLowerCase();
    }

    return result;
}