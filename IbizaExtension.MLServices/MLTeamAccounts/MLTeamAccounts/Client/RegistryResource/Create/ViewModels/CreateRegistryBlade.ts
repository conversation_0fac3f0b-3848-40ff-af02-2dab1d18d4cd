﻿﻿import * as TemplateBlade from 'Fx/Composition/TemplateBlade';
import { Experimentation, VariantAssignments } from 'Fx/Experimentation';
import { PostProvisioningContent, PostProvisioningOptions } from 'Fx/ResourceManagement/Deployments';
import * as Provisioning from 'Fx/ResourceManagement/Provisioning';

import { DataContext } from '../../RegistryResourceArea';
import * as CreateFormControl from '../../../Resource/Create/Controls/CreateFormControl';
import * as LoadableTab from '../../../Resource/Create/Controls/LoadableTab';
import { Tab } from "../../../Resource/Create/Models/ControlModels";
import { DoesProvisioning as ModelDoesProvisioning } from '../../../Resource/Create/Models/CreateModels';
import { RegionsTab } from '../Tabs/RegionsTab';
import { BasicsTab } from '../Tabs/BasicsTab';
import * as SummaryTab from '../Tabs/SummaryTab';
import * as TemplateGenerator from '../TemplateGenerator';
import { DataModel, WizardContext } from './RegistryModels';
import { NetworkingTab } from "../Tabs/NetworkingTab";

import ClientResources = require("Resx/ClientResources");
import Constants = require("../../../Shared/Constants");

export interface ProviderRegistrations {
    [provider: string]: boolean;
}

@TemplateBlade.Decorator({
    htmlTemplate: `./CreateTemplate.html`,
    forExport: true,
    styleSheets: ["./NoPdlCreateStyles.css"],
})
@TemplateBlade.DoesProvisioning.Decorator({ requiresMarketplaceId: false })
@TemplateBlade.InjectableModel.Decorator(DataContext)
export class CreateRegistryBlade {
    private _wizardContext: WizardContext;

    public context: TemplateBlade.Context<CreateMachineLearningServicesBlade.BladeParameters, DataContext> &
        TemplateBlade.DoesProvisioning.Context &
        ModelDoesProvisioning;

    public title = ClientResources.createBladeTitle;
    public subtitle = ClientResources.createBladeRegistrySubtitle;
    public createForm: CreateFormControl.Contract;

    public async onInitialize() {
        this._initializeContext();

        this.createForm = CreateFormControl.create({
            context: this._wizardContext,
            deploymentSourceName: "CreateMachineLearningBlade",
            initializeTabs: () => {
                return this._initializeTabs();
            },
            getTemplateGenerator: () => {
                return Q({
                    getTemplateDeploymentOptions: () => {
                        return Q(TemplateGenerator.getTemplateDeploymentOptions(this._wizardContext));
                    },
                    getTelemetryLoggingOptions: (deploymentOptions: Provisioning.DeployTemplateOptions) => {
                        return TemplateGenerator.getTelemetryLoggingOptions(this._wizardContext, deploymentOptions);
                    }
                });
            }
        });
    }

    private _initializeTabs(): Tab[] {
        const { dataModel } = this._wizardContext;
        const { tags } = dataModel;
        const { resourceTypes } = tags;

        const mapping: StringMap<string> = {};
        mapping[Constants.machineLearningWorkspaceType] = ClientResources.AssetTypeNames.MachineLearningServices.singular;
        resourceTypes(mapping);

        const tabs: Tab[] = [];

        tabs.push(
            new BasicsTab(this._wizardContext)
        );

        tabs.push(
            new RegionsTab(this._wizardContext)
        );

        tabs.push(new NetworkingTab({ wizardContext: this._wizardContext }));

        tabs.push(
            LoadableTab.createTagsTab({
                context: this._wizardContext,
                tagResourceTypes: resourceTypes,
                disableTags: tags.disableTagsGrid,
                tagsByResource: tags.tagsByResource
            })
        );

        tabs.push(LoadableTab.createSummaryTab<typeof SummaryTab, WizardContext>("../Tabs/SummaryTab", require, this._wizardContext));

        return tabs;
    }

    private _initializeContext(): void {
        const { container, form, parameters, provisioning, model } = this.context;
        const { client } = parameters;

        this.title = ClientResources.createBladeTitle;

        this._wizardContext = {
            container,
            lifetimeManager: container,
            form,
            dataModel: new DataModel(client),
            dataContext: model,
            provisioning,
            disableCreateButton: ko.observable(false),
            deploymentName: Constants.machineLearningServicesResourcesProvider,
            providerAndResourceName: Constants.machineLearningServicesResourcesProvider,
            originalTelemetryId: null,
            resourceProviders: ko.observableArray([Constants.machineLearningStorageResourceProvider])
        };
    }
}