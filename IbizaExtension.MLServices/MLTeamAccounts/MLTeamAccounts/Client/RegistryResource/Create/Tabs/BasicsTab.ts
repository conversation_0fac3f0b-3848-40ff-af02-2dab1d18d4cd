
import Resources = require("Resx/ClientResources");

import * as TemplateBlade from 'Fx/Composition/TemplateBlade';
import * as InfoBox from 'Fx/Controls/InfoBox';
import * as ResourceGroupDropDown from 'Fx/Controls/ResourceGroupDropDown';
import * as Section from 'Fx/Controls/Section';
import * as TextBox from 'Fx/Controls/TextBox';
import * as MultiLineTextBox from 'Fx/Controls/MultiLineTextBox';

import * as Constants from '../../../Shared/Constants';
import { DocLinkIds, getDocumentUrl } from "../../../Shared/DocLinks";
import { Tab } from "../../../Resource/Create/Models/ControlModels";
import { NoPdlUiHelpers } from '../../../Resource/Create/Utilities/NoPdlUiConstants';
import { WizardContext } from '../ViewModels/RegistryModels';
import * as CreateBasicsTab from './Create/CreateBasicsTab';

interface ProviderRegistrationData {
    [provider: string]: boolean;
}
export class BasicsTab implements Tab {
    public id: string;
    public name: string;
    public section: Section.Contract;
    private _subscriptionProviderRegStatusCache: { [subscriptionId: string]: ProviderRegistrationData } = {};


    private _basicsTab: CreateBasicsTab.Contract;
    private _wizardContext: WizardContext;

    // info boxes
    private _additionalResourcesInfoBox: InfoBox.Contract;
    private _permissionWarningBox: InfoBox.Contract;

    public constructor(wizardContext: WizardContext) {
        const { resourceProviders } = wizardContext;
        this._wizardContext = wizardContext;

        this._initializeCreateTabInfoBoxes();

        this._basicsTab = CreateBasicsTab.create({
            context: wizardContext,
            detailsOptions: {
                text: Resources.basicsRegistryBladeDetailsIntro,
                link: getDocumentUrl(DocLinkIds.ResourceGroups),
                linkText: Resources.basicsBladeDetailsIntroLearnMore,
            },
            instanceOptions: {
                createControls: () => {
                    const controls: any[] = [];

                    controls.push(...this._createRegistryNameControls());
                    controls.push(...this._createRegistryDescriptionControls());

                    controls.push(this._permissionWarningBox);

                    return controls;
                },
                text: Resources.basicsBladeRegistryInstanceIntro,
                link: getDocumentUrl(DocLinkIds.MachineLearningWorkspace),
                linkText: Resources.basicsBladeInstanceIntroLearnMore,
                overrideTitle: Resources.BasicsTab.registryDetails
            },
            subscriptionOptions: {
                resourceProviders: resourceProviders,
                overrideInfoBalloonContent: Resources.BasicsTab.Subscription.balloonContent
            },
            resourceGroupOptions: {
                initialResourceGroupName: this._wizardContext.provisioning.initialValues.resourceGroupNames,
                overrideInfoBalloonContent: Resources.BasicsTab.ResourceGroup.balloonContent
            }
        });

        this.id = this._basicsTab.id;
        this.name = this._basicsTab.name;
        this.section = this._basicsTab.section;

        this._initializeHandlers();
    }

    public triggerValidation(): Promise<boolean> {
        return this._basicsTab.triggerValidation();
    }

    private _initializeCreateTabInfoBoxes(): void {
        const { container } = this._wizardContext;

        this._additionalResourcesInfoBox = InfoBox.create(container,
            {
                text: {
                    htmlTemplate: NoPdlUiHelpers.infoText,
                    viewModel: {
                        infoText: Resources.BasicsTab.InfoText.additionalResourceInfoText,
                    }
                },
                style: InfoBox.Style.Info,
                visible: false
            }
        );

        this._permissionWarningBox = InfoBox.create(container,
            {
                text: {
                    htmlTemplate: NoPdlUiHelpers.permissionWarningText,
                    viewModel: {
                        warningText: Resources.BasicsTab.WarningText.workspaceErrorMessageText,
                        learnMoreLinkText: Resources.learnMore,
                        learnMoreLinkUri: MsPortalFx.getEnvironmentValue("machineLearningServicesWarningUrl")
                    }
                },
                style: InfoBox.Style.Warning,
                visible: false
            }
        );
    }

    private _initializeHandlers(): void {
        const { container, dataModel } = this._wizardContext;
        const { basics } = dataModel;

        basics.subscription.subscribe(container, (newSubscriptionValue) => {
            if (newSubscriptionValue && newSubscriptionValue.subscriptionId) {
                Q.allSettled([
                    this._checkProviderRegistered(newSubscriptionValue.subscriptionId, Constants.machineLearningStorageResourceProvider),
                    this._checkProviderRegistered(newSubscriptionValue.subscriptionId, Constants.machineLearningContainerRegistryProvider),
                    this._checkProviderRegistered(newSubscriptionValue.subscriptionId, Constants.machineLearningServicesResourcesProvider)
                ]).then(([storageSupported, registrySupported, mlservicesSupported]) => {
                    if (
                        storageSupported.value &&
                        registrySupported.value &&
                        mlservicesSupported.value
                    ) {
                        this._displayAdditionalResourcesInfoBox();
                    } else {
                        this._hideAllInfoBoxes();
                        this._tryRegisteringProvider(newSubscriptionValue.subscriptionId);
                    }
                });
            } else {
                this._displayAdditionalResourcesInfoBox();
            }
        });
    }

    private _tryRegisteringProvider(subscriptionId: string) {
        const { dataContext } = this._wizardContext;
        Q.all([
            dataContext.providerRegistrationData.registerProvider(subscriptionId, Constants.machineLearningStorageResourceProvider),
            dataContext.providerRegistrationData.registerProvider(subscriptionId, Constants.machineLearningContainerRegistryProvider),
            dataContext.providerRegistrationData.registerProvider(subscriptionId, Constants.machineLearningServicesResourcesProvider)])
            .then(() => {
                this._displayAdditionalResourcesInfoBox();
            })
            .catch(([err]) => {
                this._displayWarningInfoBox();
            });
    }

    private _createRegistryNameControls(): any[] {
        const { container, dataModel } = this._wizardContext;

        const nameTextBoxForCreateTab = this._createNameTextBoxForCreateTab(container, Resources.createBladeRegistryNameLabel, dataModel.basics.registryName);

        return [nameTextBoxForCreateTab];
    }

    private _createNameTextBoxForCreateTab(container: TemplateBlade.Container, labelValue: string, value: KnockoutObservable<string>): TextBox.Contract {
        var nameTextBoxForCreateTab = TextBox.create(container, {
            label: labelValue,
            infoBalloonContent: Resources.BasicsTab.RegistryName.balloonContent,
            value: value,
            validations: [
                new MsPortalFx.ViewModels.RequiredValidation(Resources.resourceNameRequired),
                new MsPortalFx.ViewModels.RegExMatchValidation(Constants.validMachineLearningServicesWorkspaceRegexStr, Resources.validationRegistryNameInvalid),
                new MsPortalFx.Azure.ReservedResourceNameValidator(Constants.machineLearningRegistryType),
                new MsPortalFx.ViewModels.CustomValidation(Resources.validationRegistryNameAlreadyInUse, (registryName: string): Q.Promise<MsPortalFx.ViewModels.ValidationResult> => {
                    return this._checkMachineLearningRegistryAvailability(registryName);
                })
            ]
        });

        return nameTextBoxForCreateTab;
    }

    private _createRegistryDescriptionControls(): any[] {
        const { container, dataModel } = this._wizardContext;

        const descriptionTextBoxForCreateTab = this._createDescriptionTextBoxForCreateTab(container, Resources.createBladeRegistryDescriptionLabel, dataModel.basics.registryDecription);

        return [descriptionTextBoxForCreateTab];
    }

    private _createDescriptionTextBoxForCreateTab(container: TemplateBlade.Container, labelValue: string, value: KnockoutObservable<string>): MultiLineTextBox.Contract {
        var nameTextBoxForCreateTab = MultiLineTextBox.create(container, {
            label: labelValue,
            value: value,
            validations: [
                new MsPortalFx.ViewModels.CustomValidation(Resources.validationRegistryDescriptionTooLarge, (registryDescription: string): Q.Promise<MsPortalFx.ViewModels.ValidationResult> => {
                    if (registryDescription && registryDescription.length > 256)
                    {
                        return Q({ valid: false, message: "" })
                    }

                    return Q({ valid: true, message: "" });
                })
            ]
        });

        return nameTextBoxForCreateTab;
    }

    private _checkMachineLearningRegistryAvailability(registryName: string): Q.Promise<MsPortalFx.ViewModels.ValidationResult> {

        const subscriptionId = this._wizardContext.dataModel.basics.subscription().subscriptionId;
        const resourceGroup = this._wizardContext.dataModel.basics.resourceGroup();
        const isNewResourceGroup = resourceGroup.value ? resourceGroup.mode === ResourceGroupDropDown.Mode.CreateNew : false;

        if (!subscriptionId || !resourceGroup || isNewResourceGroup) {
            return Q({ valid: true, message: "" });
        }
        return this._checkProviderRegistered(subscriptionId, Constants.machineLearningServicesResourcesProvider).then((registered) => {
            if (!registered || !resourceGroup.value.name) {
                return Q({ valid: true, message: "" });
            } else {
                return this._wizardContext.dataContext.registryData.checkRegistryAvailability(subscriptionId, resourceGroup.value.name, registryName);
            }
        });
    }

    private _checkProviderRegistered(subscriptionId: string, provider: string): Q.Promise<boolean> {
        if (!this._subscriptionProviderRegStatusCache.hasOwnProperty(subscriptionId)) {
            this._subscriptionProviderRegStatusCache[subscriptionId] = {};
        }

        return this._wizardContext.dataContext.providerRegistrationData.getProviderRegStatus(subscriptionId, provider)
            .then((registrationState: string) => {
                if (registrationState === Constants.registeredString || registrationState === Constants.registeringString) {
                    this._subscriptionProviderRegStatusCache[subscriptionId][provider] = true;
                } else {
                    this._subscriptionProviderRegStatusCache[subscriptionId][provider] = false;
                }
            })
            .catch(() => {
                this._subscriptionProviderRegStatusCache[subscriptionId][provider] = false;
            })
            .then(() => {
                return this._subscriptionProviderRegStatusCache[subscriptionId][provider];
            });
    }

    private _displayWarningInfoBox() {
        this._permissionWarningBox.visible(true);
        this._additionalResourcesInfoBox.visible(false);
    }

    private _displayAdditionalResourcesInfoBox() {
        this._permissionWarningBox.visible(false);
        this._additionalResourcesInfoBox.visible(true);
    }

    private _hideAllInfoBoxes() {
        this._permissionWarningBox.visible(false);
        this._additionalResourcesInfoBox.visible(false);
    }
}