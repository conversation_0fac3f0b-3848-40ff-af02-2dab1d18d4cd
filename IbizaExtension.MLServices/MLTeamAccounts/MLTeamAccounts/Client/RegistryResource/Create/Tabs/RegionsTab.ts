/**
 * Copyright (c) Microsoft Corporation.  All rights reserved.
 */

import * as Section from "Fx/Controls/Section";
import { Tab } from "../../../Resource/Create/Models/ControlModels";
import * as NoPdlUiConstants from "../Utilities/NoPdlUiConstants";
import * as NoPdlUiHelpers from '../../../Resource/Create/Utilities/NoPdlUiHelpers';
import * as DropDown from "Fx/Controls/DropDown";
import * as LocationControl from "../Controls/CreateLocationControl";
import { safeGet } from "../../../Resource/Create/Models/SafeGet";
import { WizardContext } from "../ViewModels/RegistryModels";
import Resources = require("Resx/ClientResources");
import ClientResources = require("Resx/ClientResources");
import * as Constants from '../../../Shared/Constants';

const defaultWorkspaceLocation = "eastus2";

export class RegionsTab implements Tab {
    // Sections
    private _regionSelectionSection: Section.Contract;

    // Controls
    private _regionSelectionDropdown: DropDown.Contract<string>;

    private _resourceTypes = ko.observableArray<string>([Constants.machineLearningWorkspaceType]);

    private _isLoading = ko.observable<boolean>(true);
    private _wizardContext: WizardContext;
    public id: string = NoPdlUiConstants.RegionsTab.tabId;
    public name: string = Resources.RegionsTab.title;
    public section: Section.Contract;

    constructor(wizardContext: WizardContext) {
        this._wizardContext = wizardContext;
        const { container } = this._wizardContext;
        const children: any[] = [];

        const initialLocationNameValue = [
            ...this._wizardContext.provisioning.initialValues.locationNames.reverse(),
            defaultWorkspaceLocation]
            .filter(l => !!l); // Eliminate null locations

        const locationDropDown = LocationControl.create(wizardContext, {
            overrideLabel: Resources.createLocationLabelPrimaryRegion,
            resourceTypes: this._resourceTypes,
            initialLocationName: initialLocationNameValue
        });

        const updateLocationOnResourceGroupUpdate = MsPortalFx.isFeatureEnabled("updatelocationonrgchange");
        if (updateLocationOnResourceGroupUpdate) {
            this._wizardContext.dataModel.basics.resourceGroup.subscribe(wizardContext.container, newValue => {
                const resourceGroupLocation = safeGet(newValue, "value", "location");
                const availableLocations = ko.unwrap(locationDropDown.fetchedValues);
                const location =
                    availableLocations &&
                    availableLocations.find(location => MsPortalFx.localeCompareIgnoreCase(location.name, resourceGroupLocation) === 0);
                if (location) {
                    locationDropDown.value(location);
                }
                locationDropDown.triggerValidation();
            });
        }

        this._createMultiRegionSelection();

        this._regionSelectionSection = Section.create(container, {
            children: [
                NoPdlUiHelpers.createLink({
                    text: Resources.RegionsTab.description,
                    linkUri: null,
                    useIntroStyling: true
                }),
                locationDropDown,
                this._regionSelectionDropdown
            ],
            cssClass: "ext-nopdlcreate-section"
        });

        children.push(this._regionSelectionSection);

        this.section = Section.create(container, {
            name: Resources.RegionsTab.title,
            children
        });

        this._initializeHandlers();
    }

    public onLoad(): void {
        this._isLoading(false);
    }

    private _initializeHandlers(): void {
        const { container, dataModel } = this._wizardContext;

        this._regionSelectionDropdown.value.subscribe(container, (values: string[]) => {
            dataModel.regions.regionsList = ko.observableArray(values);
        });

        dataModel.basics.location.subscribe(container, (newValue: MsPortalFx.Azure.Location) => {
            this._regionSelectionDropdown.value([]);
        });
    }

    public triggerValidation(): Promise<boolean> {
        return this._regionSelectionDropdown.triggerValidation();
    }

    private _createMultiRegionSelection(): void {
        const { container, dataModel } = this._wizardContext;
        const { regions } = dataModel;

        this._regionSelectionDropdown = DropDown.create(container,
            {
                items: [],
                label: Resources.RegionsTab.additionRegionLabel,
                filter: true,
                multiselect: true,
    	        selectAll: true
            });

        dataModel.basics.location.subscribe(container, () => {
            const locationMapping = dataModel.basics.locations().map(location => {
                return { text: location.displayName, value: location.name }
            });
            locationMapping.sort((a, b) => a.text.localeCompare(b.text));
            var locationsMinusPrimaryRegion = MsPortalFx.remove(locationMapping, (location) => {
                return location.value != dataModel.basics.location().name;
            });

            ko.pureComputed(() => {
                return locationsMinusPrimaryRegion;
            }).subscribeAndRun(container, this._regionSelectionDropdown.items);

         });
    }
}
