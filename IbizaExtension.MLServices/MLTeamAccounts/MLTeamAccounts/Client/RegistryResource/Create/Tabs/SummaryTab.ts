/**
 * Copyright (c) Microsoft Corporation.  All rights reserved.
 */

import * as Summary from 'Fx/Controls/Summary';
import { WizardContext } from '../ViewModels/RegistryModels';
import * as SummaryTab from './Create/CreateSummaryTab';
import Resources = require("Resx/ClientResources");
import { getTemplateDeploymentOptions } from '../TemplateGenerator';
import * as Provisioning from 'Fx/ResourceManagement/Provisioning';
import { data } from '_generated/Svg/icon_automl.svg';

export function create(context: WizardContext) {
    return SummaryTab.create({
        context: context,
        generateDeployTemplateOptions: getGenerateDeployTemplateOptions(context),
        customizeGroupsOnLoad: getCustomizeGroupsOnLoad(context),
        customizeBasicsSummaryItemsOnLoad: getCustomizeBasicsSummaryItemsOnLoad(context),
        locationOptions: {
            overrideLabel: Resources.createLocationLabelRegion
        },
        capitalizeNew: true
    });
}

function getGenerateDeployTemplateOptions(context: WizardContext): () => Q.Promise<Provisioning.DeployTemplateOptions> {
    return () => {
        return getTemplateDeploymentOptions(context)
    };
}

function getCustomizeGroupsOnLoad(context: WizardContext) {
    return (groups: Summary.Group[]) => {
        const { dataModel } = context;
        const { tags, regions, basics } = dataModel;

        // Add region details to Review + Create blade
        var primaryRegion: Summary.Item[] = [SummaryTab.createSummaryTabItemWithResourceType(basics.location().name, "")];
        var additionalRegions: Summary.Item[] = [];
        if (regions.regionsList() && regions.regionsList().length > 0) {
            additionalRegions = regions.regionsList().map(region => {
                return SummaryTab.createSummaryTabItemWithResourceType(region, "");
            });
        }
        groups.push(SummaryTab.createSummaryTabSection(primaryRegion.concat(additionalRegions), Resources.RegionsTab.title));

        const tagItems = tags.tags().map(item => {
            return SummaryTab.createSummaryTabItemWithResourceType(item.key, item.value);
        });

        if (tagItems.length > 0) {
            groups.push(SummaryTab.createSummaryTabSection(tagItems, Resources.tags));
        }
    };
}

function getCustomizeBasicsSummaryItemsOnLoad(context: WizardContext) {
    return (basicsSummaryItems: Summary.Item[]) => {
        const { dataModel } = context;
        const { basics } = dataModel;

        basicsSummaryItems.push(
            SummaryTab.createSummaryTabItem(
                Resources.createBladeRegistryNameLabel,
                basics.registryName()));
    };
}