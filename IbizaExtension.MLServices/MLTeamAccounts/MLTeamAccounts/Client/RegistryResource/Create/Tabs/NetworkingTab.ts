import { BladeClosedReason, BladeClosedWithDataHandler, BladeReferences } from "Fx/Composition";
import { ResourceGroup } from "Fx/Controls/BaseResourceDropDown";
import * as DataGrid from "Fx/Controls/DataGrid";
import * as InfoBox from "Fx/Controls/InfoBox";
import * as RadioButton from "Fx/Controls/RadioButtons";
import * as ResourceGroupDropDown from "Fx/Controls/ResourceGroupDropDown";
import * as Section from "Fx/Controls/Section";
import * as Toolbar from "Fx/Controls/Toolbar";
import Resources = require("Resx/ClientResources");

import { Tab } from "../../../Resource/Create/Models/ControlModels";
import { safeGet } from "../../../Resource/Create/Models/SafeGet";
import { createTextContent, createSectionHeader } from "../../../Resource/Create/Utilities/NoPdlUiHelpers";
import { getConnectivityMethodDisplayName, WizardContext } from "../ViewModels/RegistryModels";
import { ConnectivityMethod } from "../../../Resource/Common/NetworkingDataModel";
import { machineLearningServicesResourcesProvider, machineLearningRegistries } from "../../../Shared/Constants";
import { getPrivateDnsZoneName, getPrivateDnsZones } from "../../../Shared/NetworkUtilities";

import ResourceTypes = MsPortalFx.ViewModels.Services.ResourceTypes;
import FxViewModels = MsPortalFx.ViewModels;
import { createNetworkingSection } from "Resource/Common/NetworkingUtilities";

const NetworkingResources = Resources.CreateBlade.Network;

const sort = (a: { id: string }, b: { id: string }) => {
    return MsPortalFx.localeCompareIgnoreCase(a.id, b.id);
};

function getAmlRegistrySubResource(): CreatePrivateEndpointMultiDnsContextBlade.SubResourceDetails {
    // TODO: get the private link DNS for Registries
    const defaultPrivateDnsZoneNames = "privatelink.api.azureml.ms";
    const privateDnsZoneNames = (MsPortalFx.getEnvironmentValue("privateDnsZoneNames") || defaultPrivateDnsZoneNames).split(",");

    return {
        groupId: "amlregistry",
        expectedPrivateDnsZoneNames: privateDnsZoneNames,
        subResourceDisplayName: "amlregistry"
    };
}

interface PrivateEndpointConfigurationDataGridItem {
    displayName: string;
    displaySubscription: string;
    displayResourceGroup: string;
    displayRegion: string;
    displayTargetResourceType: string;
    displaySubnet: string;
    displayPrivateDnsZone: string;
    configuration: CreatePrivateEndpointMultiDnsContextBlade.Output;
}

export interface Options {
    wizardContext: WizardContext;
}

export class NetworkingTab implements Tab {
    public id: string = "networkingTab";
    public name: string = NetworkingResources.title;
    public section: Section.Contract;
    public showNameIfInvalid = true;

    private _wizardContext: WizardContext;
    private _connectivityMethodRadioButton: RadioButton.Contract<ConnectivityMethod>;
    private _isLoading = ko.observable<boolean>(true);
    private _hasLocationError = ko.observable<boolean>(false);
    private _locationErrorMessage = ko.observable<string>("");
    private _grid: DataGrid.Contract<PrivateEndpointConfigurationDataGridItem>;
    private _eligibleSubResources: KnockoutObservableBase<CreatePrivateEndpointMultiDnsContextBlade.SubResourceDetails[]>;

    constructor(options: Options) {
        this._wizardContext = options.wizardContext;
        const { container, dataModel } = this._wizardContext;
        const { networking } = dataModel;
        const { connectivityMethod } = networking;

        const children: any[] = [];

        this._eligibleSubResources = ko.pureComputed(() => [getAmlRegistrySubResource()]);

        this._createConnectivityRadioButton();
        children.push(
            createNetworkingSection(
                container,
                [
                    createSectionHeader(NetworkingResources.overviewTitle),
                    createTextContent(NetworkingResources.registryOverviewDescription, true),
                    this._connectivityMethodRadioButton
                ],
                null,
                "ext-nopdlcreate-section"
            )
        );

        this._grid = this._createPrivateEndpointsDataGrid();
        children.push(
            createNetworkingSection(
                container,
                [
                    createSectionHeader(NetworkingResources.privateEndpointTitle),
                    createTextContent(NetworkingResources.privateEndpointDescription, true),
                    this._createPrivateEndpointLocationError(),
                    this._grid,
                    this._createPrivateEndpointsToolbar()
                ],
                ko.pureComputed(() => {
                    return connectivityMethod() === ConnectivityMethod.PrivateEndpoint;
                })
            )
        );

        this.section = Section.create(container, {
            name: NetworkingResources.title,
            children: children
        });
    }

    public onLoad(): void {
        this._isLoading(false);
        this._validatePrivateEndpointLocation();
    }

    public triggerValidation(): Q.Promise<boolean> {
        this._validatePrivateEndpointLocation();
        return Q.resolve(!this._hasLocationError());
    }

    private _createConnectivityRadioButton(): void {
        const { container, dataModel } = this._wizardContext;
        const { networking } = dataModel;
        const { connectivityMethod, privateEndpointConfigurations } = networking;

        const items: RadioButton.Item<ConnectivityMethod>[] = [
            {
                text: getConnectivityMethodDisplayName(ConnectivityMethod.PublicEndpointAllNetworks),
                value: ConnectivityMethod.PublicEndpointAllNetworks
            },
            {
                text: getConnectivityMethodDisplayName(ConnectivityMethod.PrivateEndpoint),
                value: ConnectivityMethod.PrivateEndpoint
            }
        ];

        this._connectivityMethodRadioButton = RadioButton.create<ConnectivityMethod>(container, {
            label: NetworkingResources.connectivityMethod,
            items: ko.observableArray(items),
            value: connectivityMethod,
            singleItemPerLine: true,
            disabled: ko.pureComputed(() => {
                return this._isLoading();
            }),
            validations: [new FxViewModels.RequiredValidation()]
        });

        privateEndpointConfigurations.subscribe(container, () => {
            this._connectivityMethodRadioButton.triggerValidation();
        });

        connectivityMethod.subscribe(container, () => {
            this._validatePrivateEndpointLocation();
        });
    }

    private _createPrivateEndpointLocationError(): InfoBox.Contract {
        const { container, dataModel } = this._wizardContext;
        const { networking } = dataModel;
        const { privateEndpointConfigurations } = networking;

        const locationError = InfoBox.create(container, {
            style: InfoBox.Style.Error,
            text: this._locationErrorMessage,
            visible: this._hasLocationError
        });

        privateEndpointConfigurations.subscribe(container, () => {
            this._validatePrivateEndpointLocation();
        });

        return locationError;
    }

    private _createPrivateEndpointsDataGrid(): DataGrid.Contract<PrivateEndpointConfigurationDataGridItem> {
        const { container, dataModel } = this._wizardContext;
        const { networking } = dataModel;
        const { privateEndpointConfigurations } = networking;

        const columns: DataGrid.ColumnDefinition<PrivateEndpointConfigurationDataGridItem>[] = [
            DataGrid.createCustomLinkColumn({
                id: "displayName",
                header: NetworkingResources.PrivateEndpoints.name,
                cell: {
                    customLink: (item: PrivateEndpointConfigurationDataGridItem) => {
                        return {
                            text: item.displayName
                        };
                    },
                    onClick: item => {
                        container.closeContextBlade().finally(() => {
                            this._openCreatePrivateEndpoints(item.configuration);
                        });
                    }
                },
                width: "13%"
            }),
            DataGrid.createTextColumn({
                id: "displaySubscription",
                header: NetworkingResources.displaySubscription,
                cell: {
                    text: (item: PrivateEndpointConfigurationDataGridItem) => {
                        return item.displaySubscription;
                    }
                },
                width: "24%"
            }),
            DataGrid.createTextColumn({
                id: "displayResourceGroup",
                header: NetworkingResources.displayResourceGroup,
                cell: {
                    text: (item: PrivateEndpointConfigurationDataGridItem) => {
                        return item.displayResourceGroup;
                    }
                },
                width: "13%"
            }),
            DataGrid.createTextColumn({
                id: "displayRegion",
                header: NetworkingResources.displayRegion,
                cell: {
                    text: (item: PrivateEndpointConfigurationDataGridItem) => {
                        return item.displayRegion;
                    }
                },
                width: "13%"
            }),
            DataGrid.createTextColumn({
                id: "displaySubnet",
                header: NetworkingResources.displaySubnet,
                cell: {
                    text: (item: PrivateEndpointConfigurationDataGridItem) => {
                        return item.displaySubnet;
                    }
                },
                width: "13%"
            }),
            DataGrid.createTextColumn({
                id: "displayPrivateDnsZone",
                header: NetworkingResources.displayPrivateDnsZone,
                cell: {
                    text: (item: PrivateEndpointConfigurationDataGridItem) => {
                        return item.displayPrivateDnsZone;
                    }
                },
                width: "24%"
            })
        ];

        const grid = DataGrid.create<PrivateEndpointConfigurationDataGridItem>(container, {
            header: {
                visible: true
            },
            noDataMessage: {
                htmlTemplate: `<div class='ext-networkingtab-privateendpointclicktoadd' data-bind='text: text'></div>`,
                viewModel: {
                    text: NetworkingResources.PrivateEndpoints.clickToAdd
                }
            },
            ariaLabel: NetworkingResources.PrivateEndpoints.title,
            columns: columns,
            selection: {
                selectionMode: DataGrid.SelectionMode.Single
            },
            dataSource: () => {
                const endpoints = privateEndpointConfigurations();
                const gridItems = endpoints.map(endpoint => {
                    return buildGridItem(endpoint);
                });

                gridItems.sort(sort);

                return gridItems;
            },
            contextMenu: {
                supplyButtonCommands: (lifetime, row, grid) => {
                    return [this._createGridItemDeleteCommand(lifetime, row, grid)];
                },
                canShowMenu: () => false
            }
        });

        return grid;
    }

    private _createGridItemDeleteCommand(
        lifetime: MsPortalFx.Base.LifetimeManager,
        row: DataGrid.Row<PrivateEndpointConfigurationDataGridItem>,
        grid: DataGrid.Contract<PrivateEndpointConfigurationDataGridItem>
    ): Toolbar.ToolbarItems.BasicButtonContract {
        const { dataModel } = this._wizardContext;
        const { networking } = dataModel;
        const { privateEndpointConfigurations } = networking;

        return Toolbar.ToolbarItems.createBasicButton(lifetime, {
            label: Resources.Commands.delete,
            icon: MsPortalFx.Base.Images.Delete(),
            onClick: () => {
                const configuration = safeGet(row, "item", "configuration");
                if (configuration) {
                    privateEndpointConfigurations.remove(configuration);
                    grid.refresh();
                }
            }
        });
    }

    private _createPrivateEndpointsToolbar(): Toolbar.Contract {
        const { container, dataModel } = this._wizardContext;
        const { networking } = dataModel;
        const { privateEndpointConfigurations } = networking;

        return Toolbar.create(container, {
            showLabels: true,
            items: [
                Toolbar.ToolbarItems.createBasicButton(container, {
                    label: NetworkingResources.PrivateEndpoints.add,
                    icon: MsPortalFx.Base.Images.Add(),
                    onClick: () => {
                        return this._openCreatePrivateEndpoints();
                    },
                    disabled: ko.pureComputed(() => {
                        const privateEndpoints = privateEndpointConfigurations() || [];
                        const groups = this._eligibleSubResources() || [];

                        // For now, we limit private endpoints to 1
                        return privateEndpoints.length > 0 || !groups.length || this._isLoading();
                    })
                })
            ]
        });
    }

    private _openCreatePrivateEndpoints(configuration?: CreatePrivateEndpointMultiDnsContextBlade.Output): Promise<boolean> {
        const { container } = this._wizardContext;
        const parameters = this._getCreatePrivateEndpointParameters(configuration);
        const onClosed = this._getCreatePrivateEndpointOnClosed(configuration);

        return container.openContextPane(
            BladeReferences.forExtension("Microsoft_Azure_Network").forBlade("CreatePrivateEndpointMultiDnsContextBlade").createReference({
                parameters,
                onClosed
            })
        );
    }

    private _getCreatePrivateEndpointParameters(
        configuration?: CreatePrivateEndpointMultiDnsContextBlade.Output
    ): CreatePrivateEndpointMultiDnsContextBlade.Parameters {
        const { dataModel } = this._wizardContext;
        const { basics, networking } = dataModel;
        const { privateEndpointConfigurations } = networking;
        const { subscription, location, resourceGroup, registryName } = basics;

        const subscriptionValue = subscription();
        const locationValue = location();
        const resourceGroupValue = resourceGroup();
        const otherPrivateEndpointIds: string[] = [];

        const idToIgnore = safeGet(configuration, "privateEndpointConfiguration", "privateEndpoint", "id");
        const existingConfigurations = privateEndpointConfigurations() || [];
        existingConfigurations.forEach(endpoint => {
            const privateEndpointId = safeGet(endpoint, "privateEndpointConfiguration", "privateEndpoint", "id");
            if (privateEndpointId && !ResourceTypes.compareResourceId(idToIgnore, privateEndpointId)) {
                otherPrivateEndpointIds.push(privateEndpointId);
            }
        });

        const groups = this._eligibleSubResources();
        const parameters: CreatePrivateEndpointMultiDnsContextBlade.Parameters = {
            subResources: groups,
            subResourceLabel: NetworkingResources.PrivateEndpoints.registrySubResourceLabel,
            subResourceHelp: NetworkingResources.PrivateEndpoints.registrySubResourceHelp,
            otherPrivateEndpointIds,
            privateLinkServiceId: createResourceId(subscriptionValue, resourceGroupValue, registryName())
        };

        if (configuration) {
            if (configuration.privateEndpointConfiguration) {
                parameters.privateEndpointConfiguration = configuration.privateEndpointConfiguration;
            }

            if (configuration.networkingConfiguration) {
                parameters.networkingConfiguration = configuration.networkingConfiguration;
            }

            if (configuration.privateDnsZoneConfiguration) {
                parameters.privateDnsZoneConfiguration = configuration.privateDnsZoneConfiguration;
            }
        } else {
            parameters.privateEndpointConfiguration = {
                subscription: subscriptionValue,
                location: <CommonNetworkTypes.Location>locationValue,
                resourceGroup: resourceGroupValue,
                subResource: getAmlRegistrySubResource()
            };
        }

        return parameters;
    }

    private _getCreatePrivateEndpointOnClosed(
        configuration?: CreatePrivateEndpointMultiDnsContextBlade.Output
    ): BladeClosedWithDataHandler<CreatePrivateEndpointMultiDnsContextBlade.Output> {
        const { dataModel } = this._wizardContext;
        const { networking } = dataModel;
        const { privateEndpointConfigurations } = networking;

        return (reason: BladeClosedReason, data: CreatePrivateEndpointMultiDnsContextBlade.Output) => {
            if (
                reason === BladeClosedReason.ChildClosedSelf &&
                data &&
                data.networkingConfiguration &&
                data.privateEndpointConfiguration &&
                data.privateDnsZoneConfiguration
            ) {
                privateEndpointConfigurations.remove(configuration);
                privateEndpointConfigurations.push(data);
            }

            this._grid.refresh({ reset: true });
            this._connectivityMethodRadioButton.triggerValidation();
        };
    }

    private _validatePrivateEndpointLocation(): void {
        const { dataModel } = this._wizardContext;
        const { basics, networking } = dataModel;
        const { location } = basics;
        const { connectivityMethod, privateEndpointConfigurations } = networking;

        let errorMessage = "";
        let isValid = false;
        if (connectivityMethod() !== ConnectivityMethod.PrivateEndpoint || !location() || privateEndpointConfigurations().length === 0) {
            isValid = true;
        } else {
            const inValidPrivateEndpoint = privateEndpointConfigurations().filter(pe => {
                if (pe.privateEndpointConfiguration && pe.privateEndpointConfiguration.location) {
                    return MsPortalFx.localeCompareIgnoreCase(pe.privateEndpointConfiguration.location.name, location().name) !== 0;
                }

                return true;
            })[0];

            if (inValidPrivateEndpoint) {
                isValid = false;
                errorMessage = NetworkingResources.PrivateEndpoints.locationMismatch.format(
                    location().name,
                    inValidPrivateEndpoint.privateEndpointConfiguration!.location!.name
                );
            } else {
                isValid = true;
            }
        }

        this._hasLocationError(!isValid);
        this._locationErrorMessage(errorMessage);
    }
}

function buildGridItem(
    configuration: CreatePrivateEndpointMultiDnsContextBlade.Output
): DataGrid.DataNode<PrivateEndpointConfigurationDataGridItem, string> {
    const privateEndpointConfiguration = safeGet(configuration, "privateEndpointConfiguration");
    const networkingConfiguration = safeGet(configuration, "networkingConfiguration");
    const privateDnsZoneConfiguration = safeGet(configuration, "privateDnsZoneConfiguration");
    const resourceGroup = safeGet(privateEndpointConfiguration, "resourceGroup");
    const resourceGroupName = safeGet(resourceGroup, "value", "name");
    const subResourceDetails = safeGet(privateEndpointConfiguration, "subResource");

    const dnsZones = getPrivateDnsZones(configuration);
    const dnsZoneName = dnsZones
        .map(dns => (dns.isNew ? Resources.newResourceFormatCaps.format(getPrivateDnsZoneName(dns)) : getPrivateDnsZoneName(dns)))
        .join(", ");

    const gridItem: PrivateEndpointConfigurationDataGridItem = {
        displayName: safeGet(privateEndpointConfiguration, "privateEndpoint", "name"),
        displaySubscription: safeGet(privateEndpointConfiguration, "subscription", "displayName"),
        displayResourceGroup:
            resourceGroup.mode === ResourceGroup.Mode.CreateNew ? Resources.newResourceFormatCaps.format(resourceGroupName) : resourceGroupName,
        displayRegion: safeGet(privateEndpointConfiguration, "location", "displayName"),
        displayTargetResourceType: safeGet(subResourceDetails, "subResourceDisplayName") || safeGet(subResourceDetails, "groupId"),
        displaySubnet: safeGet(networkingConfiguration, "subnetValue", "text"),
        displayPrivateDnsZone: safeGet(privateDnsZoneConfiguration, "integrateWithPrivateDnsZone")
            ? dnsZoneName
            : NetworkingResources.PrivateEndpoints.noContent,
        configuration: configuration
    };

    return {
        id: configuration.privateEndpointConfiguration.privateEndpoint.id,
        item: gridItem
    };
}

export function calculateInUseVirtualNetworkIds(
    group: CreatePrivateEndpointContextBlade.SubResourceDetails,
    existingConfigurations: CreatePrivateEndpointContextBlade.Output[]
): string[] {
    let result: string[] = [];

    if (existingConfigurations) {
        existingConfigurations.forEach(configuration => {
            if (
                MsPortalFx.localeCompareIgnoreCase(
                    configuration.privateEndpointConfiguration.subResource.expectedPrivateDnsZoneName,
                    group.expectedPrivateDnsZoneName
                ) === 0
            ) {
                result.push(configuration.networkingConfiguration.virtualNetworkValue.id);
            }
        });

        if (result.length) {
            result = MsPortalFx.unique(result, (a, b) => {
                return MsPortalFx.localeCompareIgnoreCase(a, b) === 0;
            });
        }
    }

    return result;
}

export function createResourceId(subscription: MsPortalFx.Azure.Subscription, resourceGroup: ResourceGroupDropDown.Value, name: string) {
    return `/subscriptions/${subscription && subscription.subscriptionId}/resourceGroups/${resourceGroup && resourceGroup.value && resourceGroup.value.name
        }/providers/${machineLearningServicesResourcesProvider}/${machineLearningRegistries}/${name}`;
}
