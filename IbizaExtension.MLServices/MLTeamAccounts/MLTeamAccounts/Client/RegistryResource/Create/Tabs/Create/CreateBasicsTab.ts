/**
 * Copyright (c) Microsoft Corporation.  All rights reserved.
 */

import * as BladeBase from "Fx/Composition/BladeBase";
import { Container } from "Fx/Composition/TemplateBlade";
import * as CustomHtml from "Fx/Controls/CustomHtml";
import * as ResourceGroupDropDown from "Fx/Controls/ResourceGroupDropDown";
import * as SubscriptionDropDown from "Fx/Controls/SubscriptionDropDown";
import { LifetimeManager } from "Fx/Lifetime";
import * as ResourceGroupControl from "../../../../Resource/Create/Controls/CreateResourceGroupControl";
import * as SubscriptionControl from "../../../../Resource/Create/Controls/CreateSubscriptionControl";
import { Tab } from "../../../../Resource/Create/Models/ControlModels";
import { CommonContext } from "../../../../Resource/Create/Models/CreateModels";
import { Basics } from "../../Models/CreateModels";
import { BasicsTab } from '../../../../Resource/Create/Utilities/NoPdlUiConstants';

import * as NoPdlUiHelpers from '../../../../Resource/Create/Utilities/NoPdlUiHelpers';

import Resources = require("Resx/ClientResources");
export interface Context extends CommonContext {
    container: Container;
    lifetimeManager: LifetimeManager;
    dataModel: {
        basics: Basics;
    };
    provisioning: BladeBase.DoesProvisioning.Provisioning;
}

export interface HeaderOptionsBase {
    overrideTitle?: string;
    text?: string | string[];
    link?: string;
    linkText?: string;
    clickHandler?: Function;
    linkAriaLabel?: string;
}

export interface HeaderOptions extends HeaderOptionsBase {
    createControls?: () => any[];
}

export interface HeaderOptionsWithLocation extends HeaderOptionsBase {
    createControls?: () => any[];
}

export interface BasicsTabResourceGroupOptions extends ResourceGroupControl.BasicOptions {
    resourceGroupLabelValue?: KnockoutObservableBase<string>;
    hideResourceGroup?: KnockoutObservable<boolean>;
    resourceGroupReadOnlyInfoBalloonContent?: KnockoutObservableBase<string>;
    showCreateNewPlaceholder?: KnockoutObservable<boolean>;
}

export interface Options {
    context: Context;
    detailsOptions?: HeaderOptions;
    instanceOptions?: HeaderOptionsWithLocation;
    subscriptionOptions?: SubscriptionControl.BasicOptions;
    resourceGroupOptions?: BasicsTabResourceGroupOptions;
    triggerValidation?: () => Q.Promise<boolean>;
}

export interface Contract extends Tab {
    subscriptionDropDown: SubscriptionDropDown.Contract;
    resourceGroupDropDown: ResourceGroupDropDown.Contract | CustomHtml.Contract;
}

/**
 * This function will create a basics tab with common controls used in the no-pdl create pattern.
 * This class is compatible with CreateFormControl, but it may be used by itself.
 *
 * Note: A blade which calls this method must define 'ext-nopdlcreate-section' within the
 * TemplateBlade's CSS styleSheet.
 */
export function create(options: Options): Contract {
    const { context, resourceGroupOptions, subscriptionOptions } = options;
    const { container } = context;

    const subscriptionDropDown = SubscriptionControl.create(context, subscriptionOptions);
    let resourceGroupControl: ResourceGroupDropDown.Contract | CustomHtml.Contract;

    resourceGroupControl = ResourceGroupControl.create(context, resourceGroupOptions);

    const sectionChildren: any[] = [];

    // Details
    const detailsOptions = options.detailsOptions || {};
    sectionChildren.push(NoPdlUiHelpers.createSectionHeader(detailsOptions.overrideTitle || Resources.detailsLabel));

    if (detailsOptions.text) {
        if (typeof detailsOptions.text === "string") {
            if (detailsOptions.link && detailsOptions.linkText) {
                sectionChildren.push(NoPdlUiHelpers.createLink({
                    linkUri: detailsOptions.link,
                    linkText: detailsOptions.linkText,
                    text: detailsOptions.text,
                    useIntroStyling: true
                }));
            }
            else {
                sectionChildren.push(NoPdlUiHelpers.createInfoSectionHeader(detailsOptions.text));
            }
        } else {
            detailsOptions.text.forEach(text => {
                sectionChildren.push(NoPdlUiHelpers.createInfoSectionHeader(text));
            });
        }
    }

    sectionChildren.push(subscriptionDropDown, resourceGroupControl);

    if (detailsOptions.createControls) {
        sectionChildren.push(...detailsOptions.createControls());
    }

    // Instance
    const instanceOptions = options.instanceOptions || {};
    sectionChildren.push(NoPdlUiHelpers.createSectionHeader(instanceOptions.overrideTitle || Resources.instanceLabel));

    if (instanceOptions.text) {
        if (typeof instanceOptions.text === "string") {
            if (instanceOptions.link && instanceOptions.linkText) {
                sectionChildren.push(NoPdlUiHelpers.createLink({
                    linkUri: instanceOptions.link,
                    linkText: instanceOptions.linkText,
                    text: instanceOptions.text,
                    useIntroStyling: true
                }));
            }
            else {
                sectionChildren.push(NoPdlUiHelpers.createInfoSectionHeader(instanceOptions.text));
            }
        } else {
            instanceOptions.text.forEach(text => {
                sectionChildren.push(NoPdlUiHelpers.createInfoSectionHeader(text));
            });
        }
    }
    if (instanceOptions.createControls) {
        sectionChildren.push(...instanceOptions.createControls());
    }

    const tabTitle = Resources.basicsTabTitle;
    const section = NoPdlUiHelpers.createTab(context, tabTitle, [NoPdlUiHelpers.createSection(context, null, sectionChildren)]);

    return {
        id: BasicsTab.tabId,
        name: tabTitle,
        section: section,
        subscriptionDropDown: subscriptionDropDown,
        resourceGroupDropDown: resourceGroupControl,
        triggerValidation: () => {
            return NoPdlUiHelpers.validateSection(container, section).then(valid => {
                if (valid && !!options.triggerValidation) {
                    return options.triggerValidation();
                }

                return valid;
            });
        }
    };
}
