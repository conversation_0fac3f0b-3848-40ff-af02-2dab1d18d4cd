/**
 * Copyright (c) Microsoft Corporation.  All rights reserved.
 */


import * as BladeBase from 'Fx/Composition/BladeBase';
import { PdlBladeReference } from 'Fx/Composition/Selectable';
import { Container } from 'Fx/Composition/TemplateBlade';
import * as ResourceGroupDropDown from 'Fx/Controls/ResourceGroupDropDown';
import * as Summary from 'Fx/Controls/Summary';
import * as Provisioning from 'Fx/ResourceManagement/Provisioning';

import * as LoadableTab from '../../../../Resource/Create/Controls/LoadableTab';
import { Basics } from '../../Models/CreateModels';
import { CommonContext, CanOverrideLabel } from "../../../../Resource/Create/Models/CreateModels";
import * as NoPdlUiConstants from '../../../../Resource/Create/Utilities/NoPdlUiConstants';
import { IResourceGroup } from '../../../Data/Data.Types';

import Resources = require("Resx/ClientResources");
import Arm = MsPortalFx.Azure.ResourceManager;

const writeEntry = MsPortalFx.Base.Diagnostics.Log.writeEntry;
const log = MsPortalFx.Base.Diagnostics.createLog(require);
const enum ErrorCodes {
    CreateResourceGroup = 1,
    GenerateTemplate = 2
}

export interface Context extends CommonContext {
    container: Container;
    dataModel: {
        basics: Basics
    };
    provisioning: BladeBase.DoesProvisioning.Provisioning;
    providerAndResourceName: string;
}

export interface ResourceGroupToCreate {
    name: string;
    location: string;
    subscriptionId?: string;
}

export interface Options {
    context: Context;
    /**
     * A function that asynchronously returns DeployTemplateOptions for usage in template validation.
     * Typically, a template generator function or class creates DeployTemplateOptions.
     */
    generateDeployTemplateOptions: () => Q.Promise<Provisioning.DeployTemplateOptions>;
    /**
     * A function that returns an array of resource groups that need to be created before deployment validation.
     */
    getResourceGroupCreatePromises?: () => Q.Promise<IResourceGroup>[];
    /**
     * Add additional groups to the summary (usually 1 section with a title per tab). The section at
     * index 0 is the basics section.
     */
    customizeGroupsOnLoad?: (groups: Summary.Group[]) => void;
    /**
     * Add additional summary items to the basics section. The subscription, resource group, and region
     * summary items are at index 0-2.
     */
    customizeBasicsSummaryItemsOnLoad?: (basicsSummaryItems: Summary.Item[]) => void;
    /**
     * Customize the label of the location summary item.
     */
    locationOptions?: CanOverrideLabel;
    /**
     * Capitalize 'New' when added to resource names.
     */
    capitalizeNew?: boolean;
}

export interface Contract extends LoadableTab.TabContent {
}

/**
 * This function will create a summary tab with common controls used in the no-pdl create pattern.
 * The resulting contract is compatible with CreateFormControl, but it may be used by itself.
 *
 * Note: A blade which calls this method must define 'ext-nopdlcreate-section' within the
 * TemplateBlade's CSS styleSheet.
 */
export function create(options: Options): Contract {
    const { context } = options;
    const { container } = context;

    const controls = ko.observableArray<any>();

    return {
        controls,
        onLoad: () => {
            const groups: Summary.Group[] = [
                createBasicsGroup(options)
            ];

            if (options.customizeGroupsOnLoad) {
                options.customizeGroupsOnLoad(groups);
            }

            const summaryControl = Summary.create(container, { children: groups });
            controls([
                summaryControl
            ]);

            context.validateForm().then(isValid => {
                if (isValid) {

                    options.generateDeployTemplateOptions().then((deployTemplateOptions) => {
                        validateDeployment(options.context, deployTemplateOptions, options.getResourceGroupCreatePromises && options.getResourceGroupCreatePromises());
                    }).catch(error => {
                        log.error(error, ErrorCodes.GenerateTemplate);
                        options.context.container.statusBar({
                            state: MsPortalFx.ViewModels.ContentState.Error,
                            text: error.message
                        });
                    });
                }
            });
        }
    };
}

export function createSummaryTabItemWithResourceType(label: string, value: string, isNew?: boolean, capitalizeNew?: boolean): Summary.Item {
    return createSummaryTabItem(label, value, isNew, capitalizeNew);
}

export function createSummaryTabItem(label: string, value: string, isNew?: boolean, capitalizeNew?: boolean): Summary.Item {
    let valueString: string;
    if (value) {
        valueString = isNew ? capitalizeNew ? Resources.newResourceCapitalized.format(value) : Resources.newResource.format(value) : value;
    } else {
        valueString = "";
    }
    return {
        label,
        value: valueString
    };
}

export function createSummaryTabSection(children: Summary.Item[], header?: string): Summary.Group {
    return {
        name: {
            htmlTemplate: NoPdlUiConstants.SummaryTab.summaryGroupLabelTemplate,
            viewModel: {
                header
            }
        },
        children
    };
}

function createBasicsGroup(options: Options): Summary.Group {
    const { context, customizeBasicsSummaryItemsOnLoad, locationOptions, capitalizeNew } = options;
    const { dataModel } = context;

    const basicsModel = dataModel.basics;
    const resourceGroup = basicsModel.resourceGroup();
    const basicsItems: Summary.Item[] = [
        createSummaryTabItem(
            Resources.createBladeSubscriptionLabel,
            basicsModel.subscription().displayName),
        createSummaryTabItem(
            Resources.createResourceGroupTitle,
            resourceGroup && resourceGroup.value.name, resourceGroup && resourceGroup.mode === ResourceGroupDropDown.Mode.CreateNew,
            capitalizeNew),
        createSummaryTabItem(
            locationOptions && ko.unwrap(locationOptions.overrideLabel) || Resources.createLocationLabelRegion,
            basicsModel.location().displayName)
    ];

    if (customizeBasicsSummaryItemsOnLoad) {
        customizeBasicsSummaryItemsOnLoad(basicsItems);
    }

    return createSummaryTabSection(basicsItems, Resources.basics);
}

export function validateDeployment(context: Context, deployTemplateOptions: Provisioning.DeployTemplateOptions, getResourceGroupsCreatePromises?: Q.Promise<IResourceGroup>[]): Q.Promise<void> {
    const { container, provisioning, providerAndResourceName, originalTelemetryId } = context;

    container.statusBar({
        state: MsPortalFx.ViewModels.ContentState.Info,
        text: Resources.createTemplateValidationInProgress
    });

    const createResourceGroupPromise = Q.all(getResourceGroupsCreatePromises || []);

    const validationPromise = createResourceGroupPromise.then(() => {
        return Arm.Deployments.validateTemplate(deployTemplateOptions as Arm.TemplateDeploymentOptions).then(() => {
            container.statusBar({
                state: MsPortalFx.ViewModels.ContentState.Complete,
                text: Resources.createTemplateValidationSuccess
            });
            MsPortalFx.Base.Diagnostics.Telemetry.trace({
                action: "DeploymentValidation",
                source: deployTemplateOptions.deploymentName,
                data: {
                    telemetryId: provisioning.telemetryId,
                    originalTelemetryId: originalTelemetryId || null,
                    providerAndResourceName: providerAndResourceName,
                    status: "Succeeded"
                }
            });
        }).catch((error: Arm.TemplateValidationError) => {
            container.statusBar({
                state: MsPortalFx.ViewModels.ContentState.Error,
                text: Resources.createTemplateValidationError,
                onClick: () => {
                    container.openContextPane(new PdlBladeReference(
                        "ArmErrorsBlade",
                        MsPortalFx.Base.Constants.ExtensionNames.Hubs,
                        {
                            parameters: {
                                errors: (error as any).details || error,
                                subscriptionId: deployTemplateOptions.subscriptionId
                            }
                        }
                    ));
                }
            });

            MsPortalFx.Base.Diagnostics.Telemetry.trace({
                action: "DeploymentValidation",
                source: deployTemplateOptions.deploymentName,
                data: {
                    telemetryId: provisioning.telemetryId,
                    originalTelemetryId: originalTelemetryId || null,
                    providerAndResourceName: providerAndResourceName,
                    status: "Failed",
                    errors: error
                }
            });

            // Catch network failures.
            if (error.status === 0) {
                writeEntry(
                    MsPortalFx.Base.Diagnostics.LogEntryLevel.Error,
                    "CreateSummaryTab.deploymentValidation-{0}".format(provisioning.telemetryId),
                    "A network error occurred. Error details: {0}".format(MsPortalFx.getLogFriendlyMessage(error)));
            }
        });
    }, rgError => {
        container.statusBar({
            state: MsPortalFx.ViewModels.ContentState.Error,
            text: rgError.message
        });
    });

    container.operations.add(validationPromise, {
        blockUi: true,
        shieldType: MsPortalFx.ViewModels.ShieldType.Translucent
    });

    return validationPromise;
}