﻿/// <amd-bundling generated="typemetadata" />
/// <amd-dependency path="./RegistryProperties" />
import { RegistryProperties, RegistryPropertiesMetadata } from "./RegistryProperties";

export interface Registry {
    id: KnockoutObservable<string>;
    name: KnockoutObservable<string>;
    type: KnockoutObservable<string>;
    location: KnockoutObservable<string>;
    kind: KnockoutObservable<string>;
    properties: KnockoutObservable<RegistryProperties>;
}

export const RegistryMetadata: MsPortalFx.Data.Metadata.Metadata = {
    "name": "Microsoft_Azure_MLTeamAccounts.DataModels.Registry",
    "properties": {
        "properties": {
            "itemType": RegistryPropertiesMetadata.name
        },
        "id": {},
        "name": {},
        "type": {},
        "location": {},
        "kind": {}
    },
    "idProperties": [
        "id"
    ],
    "entityType": true,
    "hasGloballyUniqueId": true
};

MsPortalFx.Data.Metadata.setTypeMetadata(RegistryMetadata.name, RegistryMetadata);
