﻿/// <amd-bundling generated="typemetadata" />

export interface RegistryProperties {
    description: KnockoutObservable<string>;
    type: KnockoutObservable<string>;
    environmentId: KnockoutObservable<string>;
    provisioningState: KnockoutObservable<string>;
    mlFlowRegistryUri: KnockoutObservable<string>;
}

export const RegistryPropertiesMetadata: MsPortalFx.Data.Metadata.Metadata = {
    "name": "Microsoft_Azure_MLTeamAccounts.DataModels.RegistryPropertiesMetadata",
    "properties": {
        "description": {},
        "type": {},
        "environmentId": {},
        "provisioningState": {},
        "mlFlowRegistryUri": {}
    },
    "idProperties": [],
    "entityType": true,
    "hasGloballyUniqueId": true
};

MsPortalFx.Data.Metadata.setTypeMetadata(RegistryPropertiesMetadata.name, RegistryPropertiesMetadata);
