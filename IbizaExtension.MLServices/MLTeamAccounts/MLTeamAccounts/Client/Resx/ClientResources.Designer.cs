﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Microsoft.Portal.Extensions.Microsoft_Azure_MLTeamAccounts.Client.Resx {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class ClientResources {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal ClientResources() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Microsoft.Portal.Extensions.Microsoft_Azure_MLTeamAccounts.Client.Resx.ClientReso" +
                            "urces", typeof(ClientResources).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deprecated.
        /// </summary>
        public static string AccountPart_deprecated {
            get {
                return ResourceManager.GetString("AccountPart_deprecated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The resource is no longer available. Please use Machine Learning Services Workspace Extension..
        /// </summary>
        public static string AccountPart_deprecatedLongMessage {
            get {
                return ResourceManager.GetString("AccountPart_deprecatedLongMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Use Machine Learning Services Extension..
        /// </summary>
        public static string AccountPart_deprecatedShortMessage {
            get {
                return ResourceManager.GetString("AccountPart_deprecatedShortMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to For your convenience, these resources are added automatically to the workspace, if regionally available: &lt;a href={0} target=&quot;_blank&quot;&gt;Azure storage&lt;/a&gt;, &lt;a href={1} target=&quot;_blank&quot;&gt;Azure Application Insights&lt;/a&gt; and &lt;a href={2} target=&quot;_blank&quot;&gt;Azure Key Vault&lt;/a&gt;..
        /// </summary>
        public static string AdditionalResourceInfo {
            get {
                return ResourceManager.GetString("AdditionalResourceInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Version: {version}.
        /// </summary>
        public static string AdvancedTab__Key_KeyVaultChangeControl_version {
            get {
                return ResourceManager.GetString("AdvancedTab__Key_KeyVaultChangeControl_version", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Click to select key.
        /// </summary>
        public static string AdvancedTab_Key_KeyVaultChangeControl_action {
            get {
                return ResourceManager.GetString("AdvancedTab_Key_KeyVaultChangeControl_action", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Key: {key}.
        /// </summary>
        public static string AdvancedTab_Key_KeyVaultChangeControl_key {
            get {
                return ResourceManager.GetString("AdvancedTab_Key_KeyVaultChangeControl_key", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Key vault: {keyvault}.
        /// </summary>
        public static string AdvancedTab_Key_KeyVaultChangeControl_keyVault {
            get {
                return ResourceManager.GetString("AdvancedTab_Key_KeyVaultChangeControl_keyVault", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Key vault.
        /// </summary>
        public static string AdvancedTab_Key_KeyVaultChangeControl_label {
            get {
                return ResourceManager.GetString("AdvancedTab_Key_KeyVaultChangeControl_label", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to When bringing your own encryption key, your Azure Key Vault must have purge protection enabled to protect against accidental loss of data access..
        /// </summary>
        public static string AdvancedTab_Key_KeyVaultChangeControl_purgeProtectionRequired {
            get {
                return ResourceManager.GetString("AdvancedTab_Key_KeyVaultChangeControl_purgeProtectionRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Key is required.
        /// </summary>
        public static string AdvancedTab_Key_KeyVaultChangeControl_required {
            get {
                return ResourceManager.GetString("AdvancedTab_Key_KeyVaultChangeControl_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Key vault and key.
        /// </summary>
        public static string AdvancedTab_Key_KeyVaultChangeControl_title {
            get {
                return ResourceManager.GetString("AdvancedTab_Key_KeyVaultChangeControl_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Version: {version}.
        /// </summary>
        public static string AdvancedTab_Key_KeyVaultChangeControl_version {
            get {
                return ResourceManager.GetString("AdvancedTab_Key_KeyVaultChangeControl_version", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Optionally use pre-created resources for storing encrypted workspace data. Using your own resources for encryption, allows for enhanced configuration of these resources in compliance with your organization’s IT and security requirements, but implies additional management actions by you..
        /// </summary>
        public static string AdvancedTab_Section_DataEncryption_checkboxByoInfo {
            get {
                return ResourceManager.GetString("AdvancedTab_Section_DataEncryption_checkboxByoInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bring existing resources for storing encrypted data (preview).
        /// </summary>
        public static string AdvancedTab_Section_DataEncryption_checkboxByoLabel {
            get {
                return ResourceManager.GetString("AdvancedTab_Section_DataEncryption_checkboxByoLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enable encryption using a Customer Managed Key.
        /// </summary>
        public static string AdvancedTab_Section_DataEncryption_checkboxLabel {
            get {
                return ResourceManager.GetString("AdvancedTab_Section_DataEncryption_checkboxLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select a pre-created Azure Cosmos DB resource. If you select &apos;None&apos;, Azure ML will create a Cosmos DB resource in a new resource group in your subscription that will be managed by Microsoft. Using your own resources for encryption implies additional management responsibility by you..
        /// </summary>
        public static string AdvancedTab_Section_DataEncryption_cosmosInfo {
            get {
                return ResourceManager.GetString("AdvancedTab_Section_DataEncryption_cosmosInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cosmos DB.
        /// </summary>
        public static string AdvancedTab_Section_DataEncryption_cosmosLabel {
            get {
                return ResourceManager.GetString("AdvancedTab_Section_DataEncryption_cosmosLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer-managed keys.
        /// </summary>
        public static string AdvancedTab_Section_DataEncryption_customerManaged {
            get {
                return ResourceManager.GetString("AdvancedTab_Section_DataEncryption_customerManaged", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your data is encrypted by default using Microsoft-managed keys. For additional control over your data, you may choose to bring your own key for encryption. {0}.
        /// </summary>
        public static string AdvancedTab_Section_DataEncryption_header {
            get {
                return ResourceManager.GetString("AdvancedTab_Section_DataEncryption_header", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Azure machine learning service stores metrics and metadata in an Azure Cosmos DB instance where all data is encrypted ​at rest. By default, the data is encrypted with Microsoft-managed keys. You may choose to bring your own (customer-managed) keys. {0}.
        /// </summary>
        public static string AdvancedTab_Section_DataEncryption_infoText {
            get {
                return ResourceManager.GetString("AdvancedTab_Section_DataEncryption_infoText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Learn more.
        /// </summary>
        public static string AdvancedTab_Section_DataEncryption_learnMoreText {
            get {
                return ResourceManager.GetString("AdvancedTab_Section_DataEncryption_learnMoreText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Learn more about customer-managed key encryption..
        /// </summary>
        public static string AdvancedTab_Section_DataEncryption_LinkText {
            get {
                return ResourceManager.GetString("AdvancedTab_Section_DataEncryption_LinkText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Microsoft-managed keys.
        /// </summary>
        public static string AdvancedTab_Section_DataEncryption_microsoftManaged {
            get {
                return ResourceManager.GetString("AdvancedTab_Section_DataEncryption_microsoftManaged", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select a pre-created Azure Search resource. If you select &apos;None&apos;, Azure ML will create a Search resource in a new resource group in your subscription that will be managed by Microsoft. Using your own resources for encryption implies additional management responsibility by you..
        /// </summary>
        public static string AdvancedTab_Section_DataEncryption_searchInfo {
            get {
                return ResourceManager.GetString("AdvancedTab_Section_DataEncryption_searchInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Search.
        /// </summary>
        public static string AdvancedTab_Section_DataEncryption_searchLabel {
            get {
                return ResourceManager.GetString("AdvancedTab_Section_DataEncryption_searchLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Azure machine learning service stores metrics and metadata in an Azure Cosmos DB instance where all data is encrypted ​at rest. By default, the data is encrypted with Microsoft-managed keys. You may choose to bring your own (customer-managed) keys. {0}.
        /// </summary>
        public static string AdvancedTab_Section_DataEncryption_ServiceSide_infoText {
            get {
                return ResourceManager.GetString("AdvancedTab_Section_DataEncryption_ServiceSide_infoText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Use service-side encryption ({0}).
        /// </summary>
        public static string AdvancedTab_Section_DataEncryption_ServiceSide_label {
            get {
                return ResourceManager.GetString("AdvancedTab_Section_DataEncryption_ServiceSide_label", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select a pre-created Azure Storage resource. If you select &apos;None&apos;, Azure ML will create a Storage resource in a new resource group in your subscription that will be managed by Microsoft. Using your own resources for encryption implies additional management responsibility by you..
        /// </summary>
        public static string AdvancedTab_Section_DataEncryption_storageInfo {
            get {
                return ResourceManager.GetString("AdvancedTab_Section_DataEncryption_storageInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Storage.
        /// </summary>
        public static string AdvancedTab_Section_DataEncryption_storageLabel {
            get {
                return ResourceManager.GetString("AdvancedTab_Section_DataEncryption_storageLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Data encryption.
        /// </summary>
        public static string AdvancedTab_Section_DataEncryption_title {
            get {
                return ResourceManager.GetString("AdvancedTab_Section_DataEncryption_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Encrypt data using a customer managed key.
        /// </summary>
        public static string AdvancedTab_Section_DataEncryption_Type_label {
            get {
                return ResourceManager.GetString("AdvancedTab_Section_DataEncryption_Type_label", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to After workspace creation, you cannot change encryption key type between Microsoft-managed keys  and Customer-managed keys..
        /// </summary>
        public static string AdvancedTab_Section_DataEncryption_WarningMessage {
            get {
                return ResourceManager.GetString("AdvancedTab_Section_DataEncryption_WarningMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to High business impact workspace.
        /// </summary>
        public static string AdvancedTab_Section_DataImpact_HBI_label {
            get {
                return ResourceManager.GetString("AdvancedTab_Section_DataImpact_HBI_label", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to If your workspace contains sensitive data, you can specify a high business impact workspace. This will control the amount of data Microsoft collects for diagnostic purposes and enables additional encryption in Microsoft managed environments.​.
        /// </summary>
        public static string AdvancedTab_Section_DataImpact_header {
            get {
                return ResourceManager.GetString("AdvancedTab_Section_DataImpact_header", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Data impact.
        /// </summary>
        public static string AdvancedTab_Section_DataImpact_title {
            get {
                return ResourceManager.GetString("AdvancedTab_Section_DataImpact_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to A managed identity enables Azure resources to authenticate to cloud services without storing credentials in code. Once enabled, all necessary permissions can be granted via Azure role-based access control. A workspace can be given either a system assigned identity or a user assigned identity..
        /// </summary>
        public static string AdvancedTab_Section_ManagedIdentity_description {
            get {
                return ResourceManager.GetString("AdvancedTab_Section_ManagedIdentity_description", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The managed user assigned identity option is only supported if an existing storage account, key vault, and container registry are used..
        /// </summary>
        public static string AdvancedTab_Section_ManagedIdentity_disabledWarning {
            get {
                return ResourceManager.GetString("AdvancedTab_Section_ManagedIdentity_disabledWarning", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Managed identity.
        /// </summary>
        public static string AdvancedTab_Section_ManagedIdentity_header {
            get {
                return ResourceManager.GetString("AdvancedTab_Section_ManagedIdentity_header", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to If you bring your own associated resources, instead of having Azure Machine Learning service create them, you must grant the managed identity roles on those resources. Use the {0} to make the assignments..
        /// </summary>
        public static string AdvancedTab_Section_ManagedIdentity_permissionWarning {
            get {
                return ResourceManager.GetString("AdvancedTab_Section_ManagedIdentity_permissionWarning", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to role assignment ARM template.
        /// </summary>
        public static string AdvancedTab_Section_ManagedIdentity_permissionWarningLinkText {
            get {
                return ResourceManager.GetString("AdvancedTab_Section_ManagedIdentity_permissionWarningLinkText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Identity type.
        /// </summary>
        public static string AdvancedTab_Section_ManagedIdentity_radioGroupTitle {
            get {
                return ResourceManager.GetString("AdvancedTab_Section_ManagedIdentity_radioGroupTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to System assigned identity.
        /// </summary>
        public static string AdvancedTab_Section_ManagedIdentity_systemAssignedText {
            get {
                return ResourceManager.GetString("AdvancedTab_Section_ManagedIdentity_systemAssignedText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User assigned identity.
        /// </summary>
        public static string AdvancedTab_Section_ManagedIdentity_userAssignedPickerLabel {
            get {
                return ResourceManager.GetString("AdvancedTab_Section_ManagedIdentity_userAssignedPickerLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User identity is required.
        /// </summary>
        public static string AdvancedTab_Section_ManagedIdentity_userAssignedPickerRequiredMessage {
            get {
                return ResourceManager.GetString("AdvancedTab_Section_ManagedIdentity_userAssignedPickerRequiredMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Workspace and selected user identity must be in the same subscription..
        /// </summary>
        public static string AdvancedTab_Section_ManagedIdentity_userAssignedPickerSubscriptionMessage {
            get {
                return ResourceManager.GetString("AdvancedTab_Section_ManagedIdentity_userAssignedPickerSubscriptionMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Click to select identity.
        /// </summary>
        public static string AdvancedTab_Section_ManagedIdentity_userAssignedPickerText {
            get {
                return ResourceManager.GetString("AdvancedTab_Section_ManagedIdentity_userAssignedPickerText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User assigned identity.
        /// </summary>
        public static string AdvancedTab_Section_ManagedIdentity_userAssignedText {
            get {
                return ResourceManager.GetString("AdvancedTab_Section_ManagedIdentity_userAssignedText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User identity name.
        /// </summary>
        public static string AdvancedTab_Section_ManagedIdentity_userIdentityNameLabel {
            get {
                return ResourceManager.GetString("AdvancedTab_Section_ManagedIdentity_userIdentityNameLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User identity resource group.
        /// </summary>
        public static string AdvancedTab_Section_ManagedIdentity_userIdentityResourceGroupLabel {
            get {
                return ResourceManager.GetString("AdvancedTab_Section_ManagedIdentity_userIdentityResourceGroupLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Credential-based access.
        /// </summary>
        public static string AdvancedTab_Section_StorageAccountAccess_credentialBasedText {
            get {
                return ResourceManager.GetString("AdvancedTab_Section_StorageAccountAccess_credentialBasedText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Azure machine learning allows you to choose between credential-based or identity-based access when connecting to the default storage account..
        /// </summary>
        public static string AdvancedTab_Section_StorageAccountAccess_description {
            get {
                return ResourceManager.GetString("AdvancedTab_Section_StorageAccountAccess_description", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Storage account access.
        /// </summary>
        public static string AdvancedTab_Section_StorageAccountAccess_header {
            get {
                return ResourceManager.GetString("AdvancedTab_Section_StorageAccountAccess_header", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Identity-based access.
        /// </summary>
        public static string AdvancedTab_Section_StorageAccountAccess_identityBasedText {
            get {
                return ResourceManager.GetString("AdvancedTab_Section_StorageAccountAccess_identityBasedText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to learn more about RBAC settings.
        /// </summary>
        public static string AdvancedTab_Section_StorageAccountAccess_identityBasedWarningLearnMore {
            get {
                return ResourceManager.GetString("AdvancedTab_Section_StorageAccountAccess_identityBasedWarningLearnMore", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} When using identity-based authentication, &quot;{1}&quot; and &quot;{2}&quot; roles must be granted to {3} that need access on the storage account. Contact your admin for help or.
        /// </summary>
        public static string AdvancedTab_Section_StorageAccountAccess_identityBasedWarningText {
            get {
                return ResourceManager.GetString("AdvancedTab_Section_StorageAccountAccess_identityBasedWarningText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Important.
        /// </summary>
        public static string AdvancedTab_Section_StorageAccountAccess_Important {
            get {
                return ResourceManager.GetString("AdvancedTab_Section_StorageAccountAccess_Important", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to individual users.
        /// </summary>
        public static string AdvancedTab_Section_StorageAccountAccess_IndividualUsers {
            get {
                return ResourceManager.GetString("AdvancedTab_Section_StorageAccountAccess_IndividualUsers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Learn more.
        /// </summary>
        public static string AdvancedTab_Section_StorageAccountAccess_learnMoreText {
            get {
                return ResourceManager.GetString("AdvancedTab_Section_StorageAccountAccess_learnMoreText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to (preview).
        /// </summary>
        public static string AdvancedTab_Section_StorageAccountAccess_previewLinkText {
            get {
                return ResourceManager.GetString("AdvancedTab_Section_StorageAccountAccess_previewLinkText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Storage account access type.
        /// </summary>
        public static string AdvancedTab_Section_StorageAccountAccess_radioGroupTitle {
            get {
                return ResourceManager.GetString("AdvancedTab_Section_StorageAccountAccess_radioGroupTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Disable shared key access.
        /// </summary>
        public static string AdvancedTab_Section_StorageAccountAccess_sharedKeyAccessCheckboxLabel {
            get {
                return ResourceManager.GetString("AdvancedTab_Section_StorageAccountAccess_sharedKeyAccessCheckboxLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Learn more.
        /// </summary>
        public static string AdvancedTab_Section_StorageAccountAccess_sharedKeyAccessDisableDocumentationLinkText {
            get {
                return ResourceManager.GetString("AdvancedTab_Section_StorageAccountAccess_sharedKeyAccessDisableDocumentationLinkT" +
                        "ext", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Disable shared key access option {0} disables key access at the storage account, any requests to the storage account that are authorized with shared key, including shared access signatures (SAS), will be denied. This option might require additional role configuration to individual users for certain use cases. {1} about disabling shared key access for your workspace&apos;s storage account.
        /// </summary>
        public static string AdvancedTab_Section_StorageAccountAccess_sharedKeyAccessDisableWarning {
            get {
                return ResourceManager.GetString("AdvancedTab_Section_StorageAccountAccess_sharedKeyAccessDisableWarning", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AI Services.
        /// </summary>
        public static string AIServices_SummaryTab_label {
            get {
                return ResourceManager.GetString("AIServices_SummaryTab_label", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create new application insights.
        /// </summary>
        public static string AppInsights_Dropdown_createNewAriaLabel {
            get {
                return ResourceManager.GetString("AppInsights_Dropdown_createNewAriaLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The AI hub uses Azure Application Insights to store monitoring information about your deployed models. You may create a new Azure Application Insights resource or select an existing one in your subscription..
        /// </summary>
        public static string AppInsights_Dropdown_Hub_info {
            get {
                return ResourceManager.GetString("AppInsights_Dropdown_Hub_info", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The workspace uses Azure Application Insights to store monitoring information about your deployed models. You may create a new Azure Application Insights resource or select an existing one in your subscription..
        /// </summary>
        public static string AppInsights_Dropdown_info {
            get {
                return ResourceManager.GetString("AppInsights_Dropdown_info", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Application insights.
        /// </summary>
        public static string AppInsights_Dropdown_label {
            get {
                return ResourceManager.GetString("AppInsights_Dropdown_label", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The name must meet the following requirements:.
        /// </summary>
        public static string AppInsights_Dropdown_SettingsBlade_Name_infoDescription {
            get {
                return ResourceManager.GetString("AppInsights_Dropdown_SettingsBlade_Name_infoDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unique across the resource group.
        /// </summary>
        public static string AppInsights_Dropdown_SettingsBlade_Name_infoItem1 {
            get {
                return ResourceManager.GetString("AppInsights_Dropdown_SettingsBlade_Name_infoItem1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Between 1 and 255 characters long.
        /// </summary>
        public static string AppInsights_Dropdown_SettingsBlade_Name_infoItem2 {
            get {
                return ResourceManager.GetString("AppInsights_Dropdown_SettingsBlade_Name_infoItem2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Only contain alphanumeric characters, periods, underscores, hyphens, and parenthesis.
        /// </summary>
        public static string AppInsights_Dropdown_SettingsBlade_Name_infoItem3 {
            get {
                return ResourceManager.GetString("AppInsights_Dropdown_SettingsBlade_Name_infoItem3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cannot end with a period.
        /// </summary>
        public static string AppInsights_Dropdown_SettingsBlade_Name_infoItem4 {
            get {
                return ResourceManager.GetString("AppInsights_Dropdown_SettingsBlade_Name_infoItem4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The name must contain between 1 to 255 characters inclusive. The name only allows alphanumeric characters, periods, underscores, hyphens and parenthesis and cannot end in a period..
        /// </summary>
        public static string AppInsights_Dropdown_SettingsBlade_nameInvalidMessage {
            get {
                return ResourceManager.GetString("AppInsights_Dropdown_SettingsBlade_nameInvalidMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Name.
        /// </summary>
        public static string AppInsights_Dropdown_SettingsBlade_nameLabel {
            get {
                return ResourceManager.GetString("AppInsights_Dropdown_SettingsBlade_nameLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create new application insights.
        /// </summary>
        public static string AppInsights_Dropdown_SettingsBlade_title {
            get {
                return ResourceManager.GetString("AppInsights_Dropdown_SettingsBlade_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Application insights.
        /// </summary>
        public static string AppInsights_SummaryTab_label {
            get {
                return ResourceManager.GetString("AppInsights_SummaryTab_label", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create.
        /// </summary>
        public static string AssetType_AIStudio_Browse_Commands_CreateAzureAI {
            get {
                return ResourceManager.GetString("AssetType_AIStudio_Browse_Commands_CreateAzureAI", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New Azure AI hub.
        /// </summary>
        public static string AssetType_AIStudio_Browse_Commands_CreateAzureAICommandBar {
            get {
                return ResourceManager.GetString("AssetType_AIStudio_Browse_Commands_CreateAzureAICommandBar", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hub.
        /// </summary>
        public static string AssetType_AIStudio_Browse_Commands_CreateAzureAIHub {
            get {
                return ResourceManager.GetString("AssetType_AIStudio_Browse_Commands_CreateAzureAIHub", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create Hub.
        /// </summary>
        public static string AssetType_AIStudio_Browse_Commands_CreateAzureAIHubLabel {
            get {
                return ResourceManager.GetString("AssetType_AIStudio_Browse_Commands_CreateAzureAIHubLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create Azure AI Foundry.
        /// </summary>
        public static string AssetType_AIStudio_Browse_Commands_CreateAzureAILabel {
            get {
                return ResourceManager.GetString("AssetType_AIStudio_Browse_Commands_CreateAzureAILabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Project.
        /// </summary>
        public static string AssetType_AIStudio_Browse_Commands_CreateAzureAIProject {
            get {
                return ResourceManager.GetString("AssetType_AIStudio_Browse_Commands_CreateAzureAIProject", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create Project.
        /// </summary>
        public static string AssetType_AIStudio_Browse_Commands_CreateAzureAIProjectLabel {
            get {
                return ResourceManager.GetString("AssetType_AIStudio_Browse_Commands_CreateAzureAIProjectLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Grouping container for projects. Provides security, connectivity, and compute management..
        /// </summary>
        public static string AssetType_AIStudio_Browse_Description_CreateAzureAIHub {
            get {
                return ResourceManager.GetString("AssetType_AIStudio_Browse_Description_CreateAzureAIHub", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Collaborate, organize, and track work to build AI apps..
        /// </summary>
        public static string AssetType_AIStudio_Browse_Description_CreateAzureAIProject {
            get {
                return ResourceManager.GetString("AssetType_AIStudio_Browse_Description_CreateAzureAIProject", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your platform to build generative AI solutions and custom copilots.
        /// </summary>
        public static string AssetType_AIStudio_Description {
            get {
                return ResourceManager.GetString("AssetType_AIStudio_Description", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create new Azure AI Services.
        /// </summary>
        public static string AssetType_AIStudio_Hub_AIServices_Settings_title {
            get {
                return ResourceManager.GetString("AssetType_AIStudio_Hub_AIServices_Settings_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create new Hub.
        /// </summary>
        public static string AssetType_AIStudio_Hub_Dropdown_createNewAriaLabel {
            get {
                return ResourceManager.GetString("AssetType_AIStudio_Hub_Dropdown_createNewAriaLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Grouping container for projects. Provides security, connectivity, and compute management..
        /// </summary>
        public static string AssetType_AIStudio_Hub_Dropdown_info {
            get {
                return ResourceManager.GetString("AssetType_AIStudio_Hub_Dropdown_info", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hub.
        /// </summary>
        public static string AssetType_AIStudio_Hub_Dropdown_label {
            get {
                return ResourceManager.GetString("AssetType_AIStudio_Hub_Dropdown_label", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AI Services provider.
        /// </summary>
        public static string AssetType_AIStudio_Hub_Essentials_AIServices_label {
            get {
                return ResourceManager.GetString("AssetType_AIStudio_Hub_Essentials_AIServices_label", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Project resource group (default).
        /// </summary>
        public static string AssetType_AIStudio_Hub_Essentials_DefaultProjectResourceGroup_label {
            get {
                return ResourceManager.GetString("AssetType_AIStudio_Hub_Essentials_DefaultProjectResourceGroup_label", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your Azure AI hub provides enterpise-grade security, and a collaborative environment to build AI solutions. Centrally audit usage and cost, and set up connections to your company resources that all projects can use. {}.
        /// </summary>
        public static string AssetType_AIStudio_Hub_Overview_Banner_Description_label {
            get {
                return ResourceManager.GetString("AssetType_AIStudio_Hub_Overview_Banner_Description_label", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to learn more about the Azure AI Foundry.
        /// </summary>
        public static string AssetType_AIStudio_Hub_Overview_Banner_Description_learnMoreText {
            get {
                return ResourceManager.GetString("AssetType_AIStudio_Hub_Overview_Banner_Description_learnMoreText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Launch Azure AI Foundry.
        /// </summary>
        public static string AssetType_AIStudio_Hub_Overview_Banner_launchButtonText {
            get {
                return ResourceManager.GetString("AssetType_AIStudio_Hub_Overview_Banner_launchButtonText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Govern the environment for your team in AI Foundry.
        /// </summary>
        public static string AssetType_AIStudio_Hub_Overview_Banner_title {
            get {
                return ResourceManager.GetString("AssetType_AIStudio_Hub_Overview_Banner_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Regenerate Key 1.
        /// </summary>
        public static string AssetType_AIStudio_KeysAndEndpoints_Command_regenKey1 {
            get {
                return ResourceManager.GetString("AssetType_AIStudio_KeysAndEndpoints_Command_regenKey1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Regenerate Key 2.
        /// </summary>
        public static string AssetType_AIStudio_KeysAndEndpoints_Command_regenKey2 {
            get {
                return ResourceManager.GetString("AssetType_AIStudio_KeysAndEndpoints_Command_regenKey2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Endpoint.
        /// </summary>
        public static string AssetType_AIStudio_KeysAndEndpoints_endpoint {
            get {
                return ResourceManager.GetString("AssetType_AIStudio_KeysAndEndpoints_endpoint", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hide keys.
        /// </summary>
        public static string AssetType_AIStudio_KeysAndEndpoints_hideKeys {
            get {
                return ResourceManager.GetString("AssetType_AIStudio_KeysAndEndpoints_hideKeys", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to KEY 1.
        /// </summary>
        public static string AssetType_AIStudio_KeysAndEndpoints_key1 {
            get {
                return ResourceManager.GetString("AssetType_AIStudio_KeysAndEndpoints_key1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to KEY 2.
        /// </summary>
        public static string AssetType_AIStudio_KeysAndEndpoints_key2 {
            get {
                return ResourceManager.GetString("AssetType_AIStudio_KeysAndEndpoints_key2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to These keys are used to access your Azure AI services API. Do not share your keys. Store them securely- for example, using Azure Key Vault. We also recommend regenerating these keys regularly. Only one key is necessary to make an API call. When regenerating the first key, you can use the second key for continued access to the service..
        /// </summary>
        public static string AssetType_AIStudio_KeysAndEndpoints_message {
            get {
                return ResourceManager.GetString("AssetType_AIStudio_KeysAndEndpoints_message", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Regenerate keys.
        /// </summary>
        public static string AssetType_AIStudio_KeysAndEndpoints_regenerateKeys {
            get {
                return ResourceManager.GetString("AssetType_AIStudio_KeysAndEndpoints_regenerateKeys", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Show keys.
        /// </summary>
        public static string AssetType_AIStudio_KeysAndEndpoints_showKeys {
            get {
                return ResourceManager.GetString("AssetType_AIStudio_KeysAndEndpoints_showKeys", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Keys and Endpoints.
        /// </summary>
        public static string AssetType_AIStudio_KeysAndEndpoints_title {
            get {
                return ResourceManager.GetString("AssetType_AIStudio_KeysAndEndpoints_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AI, Hub, AI Studio, Azure AI Studio, AI Foundry, Azure AI Foundry, AI Hub, AI Project, AIStudio, Artificial Intelligence, Preview, Deep Learning, Analytics, Data, NLP, Natural Language Processing, CNN, Neural Network, Train, Computer Vision, Models, Data Science, Classification, Regression, Reinforcement Learning, LLM, Chatbot.
        /// </summary>
        public static string AssetType_AIStudio_Keywords {
            get {
                return ResourceManager.GetString("AssetType_AIStudio_Keywords", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Azure AI Foundry.
        /// </summary>
        public static string AssetType_AIStudio_Names_lowerPlural {
            get {
                return ResourceManager.GetString("AssetType_AIStudio_Names_lowerPlural", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Azure AI Foundry.
        /// </summary>
        public static string AssetType_AIStudio_Names_lowerSingular {
            get {
                return ResourceManager.GetString("AssetType_AIStudio_Names_lowerSingular", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Azure AI Foundry.
        /// </summary>
        public static string AssetType_AIStudio_Names_plural {
            get {
                return ResourceManager.GetString("AssetType_AIStudio_Names_plural", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Azure AI Foundry.
        /// </summary>
        public static string AssetType_AIStudio_Names_singular {
            get {
                return ResourceManager.GetString("AssetType_AIStudio_Names_singular", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Jumpstart your AI solution development with pre-built templates and work on your project either in code or in the studio..
        /// </summary>
        public static string AssetType_AIStudio_Project_Overview_Banner_Description_label {
            get {
                return ResourceManager.GetString("AssetType_AIStudio_Project_Overview_Banner_Description_label", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Launch studio.
        /// </summary>
        public static string AssetType_AIStudio_Project_Overview_Banner_launchButtonText {
            get {
                return ResourceManager.GetString("AssetType_AIStudio_Project_Overview_Banner_launchButtonText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Start building in Azure AI Foundry.
        /// </summary>
        public static string AssetType_AIStudio_Project_Overview_Banner_title {
            get {
                return ResourceManager.GetString("AssetType_AIStudio_Project_Overview_Banner_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cohere.
        /// </summary>
        public static string AssetType_cohere_Names_lowerPlural {
            get {
                return ResourceManager.GetString("AssetType_cohere_Names_lowerPlural", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cohere.
        /// </summary>
        public static string AssetType_cohere_Names_lowerSingular {
            get {
                return ResourceManager.GetString("AssetType_cohere_Names_lowerSingular", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cohere.
        /// </summary>
        public static string AssetType_cohere_Names_plural {
            get {
                return ResourceManager.GetString("AssetType_cohere_Names_plural", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cohere.
        /// </summary>
        public static string AssetType_cohere_Names_singular {
            get {
                return ResourceManager.GetString("AssetType_cohere_Names_singular", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Continue, and you&apos;ll be redirected to the model catalog in Azure AI Foundry where you can finish subscribing and start using this model..
        /// </summary>
        public static string AssetType_cohere_Page_Description_body {
            get {
                return ResourceManager.GetString("AssetType_cohere_Page_Description_body", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Thank you for your interest..
        /// </summary>
        public static string AssetType_cohere_Page_Description_intro {
            get {
                return ResourceManager.GetString("AssetType_cohere_Page_Description_intro", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Continue to Azure AI Foundry model catalog.
        /// </summary>
        public static string AssetType_cohere_Page_primaryButton {
            get {
                return ResourceManager.GetString("AssetType_cohere_Page_primaryButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Continue to Azure AI Foundry to use this offer.
        /// </summary>
        public static string AssetType_cohere_Page_title {
            get {
                return ResourceManager.GetString("AssetType_cohere_Page_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Core42.
        /// </summary>
        public static string AssetType_Core42_Names_lowerPlural {
            get {
                return ResourceManager.GetString("AssetType_Core42_Names_lowerPlural", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Core42.
        /// </summary>
        public static string AssetType_Core42_Names_lowerSingular {
            get {
                return ResourceManager.GetString("AssetType_Core42_Names_lowerSingular", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Core42.
        /// </summary>
        public static string AssetType_Core42_Names_plural {
            get {
                return ResourceManager.GetString("AssetType_Core42_Names_plural", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Core42.
        /// </summary>
        public static string AssetType_Core42_Names_singular {
            get {
                return ResourceManager.GetString("AssetType_Core42_Names_singular", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Continue, and you&apos;ll be redirected to the model catalog in Azure AI Foundry where you can finish subscribing and start using this model..
        /// </summary>
        public static string AssetType_Core42_Page_Description_body {
            get {
                return ResourceManager.GetString("AssetType_Core42_Page_Description_body", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Thank you for your interest..
        /// </summary>
        public static string AssetType_Core42_Page_Description_intro {
            get {
                return ResourceManager.GetString("AssetType_Core42_Page_Description_intro", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Continue to Azure AI Foundry model catalog.
        /// </summary>
        public static string AssetType_Core42_Page_primaryButton {
            get {
                return ResourceManager.GetString("AssetType_Core42_Page_primaryButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Continue to Azure AI Foundry to use this offer.
        /// </summary>
        public static string AssetType_Core42_Page_title {
            get {
                return ResourceManager.GetString("AssetType_Core42_Page_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Workspaces are where you manage all the models, assets, and data related to your machine learning projects. Create one now to start using Azure Machine Learning..
        /// </summary>
        public static string AssetType_description {
            get {
                return ResourceManager.GetString("AssetType_description", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ML, AML, Machine Learning, AI, Artificial Intelligence, Preview, Deep Learning, Analytics, Data, NLP, Natural Language Processing, CNN, Neural Network, Workbench, Train, Notebooks, AutoML, Designer, Computer Vision, Models, Data Science, Classification, Regression, Reinforcement Learning.
        /// </summary>
        public static string AssetType_keywords {
            get {
                return ResourceManager.GetString("AssetType_keywords", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Llama2.
        /// </summary>
        public static string AssetType_Llama2_Names_lowerPlural {
            get {
                return ResourceManager.GetString("AssetType_Llama2_Names_lowerPlural", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Llama2.
        /// </summary>
        public static string AssetType_Llama2_Names_lowerSingular {
            get {
                return ResourceManager.GetString("AssetType_Llama2_Names_lowerSingular", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Llama2.
        /// </summary>
        public static string AssetType_Llama2_Names_plural {
            get {
                return ResourceManager.GetString("AssetType_Llama2_Names_plural", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Llama2.
        /// </summary>
        public static string AssetType_Llama2_Names_singular {
            get {
                return ResourceManager.GetString("AssetType_Llama2_Names_singular", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Continue, and you&apos;ll be redirected to the model catalog in Azure AI Foundry where you can finish subscribing and start using this model..
        /// </summary>
        public static string AssetType_Llama2_Page_Description_body {
            get {
                return ResourceManager.GetString("AssetType_Llama2_Page_Description_body", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Thank you for your interest..
        /// </summary>
        public static string AssetType_Llama2_Page_Description_intro {
            get {
                return ResourceManager.GetString("AssetType_Llama2_Page_Description_intro", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Continue to Azure AI Foundry model catalog.
        /// </summary>
        public static string AssetType_Llama2_Page_primaryButton {
            get {
                return ResourceManager.GetString("AssetType_Llama2_Page_primaryButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Continue to Azure AI Foundry to use this offer.
        /// </summary>
        public static string AssetType_Llama2_Page_title {
            get {
                return ResourceManager.GetString("AssetType_Llama2_Page_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mistral.
        /// </summary>
        public static string AssetType_Mistral_Names_lowerPlural {
            get {
                return ResourceManager.GetString("AssetType_Mistral_Names_lowerPlural", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mistral.
        /// </summary>
        public static string AssetType_Mistral_Names_lowerSingular {
            get {
                return ResourceManager.GetString("AssetType_Mistral_Names_lowerSingular", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mistral.
        /// </summary>
        public static string AssetType_Mistral_Names_plural {
            get {
                return ResourceManager.GetString("AssetType_Mistral_Names_plural", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mistral.
        /// </summary>
        public static string AssetType_Mistral_Names_singular {
            get {
                return ResourceManager.GetString("AssetType_Mistral_Names_singular", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Continue, and you&apos;ll be redirected to the model catalog in Azure AI Foundry where you can finish subscribing and start using this model..
        /// </summary>
        public static string AssetType_Mistral_Page_Description_body {
            get {
                return ResourceManager.GetString("AssetType_Mistral_Page_Description_body", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Thank you for your interest..
        /// </summary>
        public static string AssetType_Mistral_Page_Description_intro {
            get {
                return ResourceManager.GetString("AssetType_Mistral_Page_Description_intro", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Continue to Azure AI Foundry model catalog.
        /// </summary>
        public static string AssetType_Mistral_Page_primaryButton {
            get {
                return ResourceManager.GetString("AssetType_Mistral_Page_primaryButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Continue to Azure AI Foundry to use this offer.
        /// </summary>
        public static string AssetType_Mistral_Page_title {
            get {
                return ResourceManager.GetString("AssetType_Mistral_Page_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Model Provider.
        /// </summary>
        public static string AssetType_ModelProvider_Names_lowerPlural {
            get {
                return ResourceManager.GetString("AssetType_ModelProvider_Names_lowerPlural", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Model Provider.
        /// </summary>
        public static string AssetType_ModelProvider_Names_lowerSingular {
            get {
                return ResourceManager.GetString("AssetType_ModelProvider_Names_lowerSingular", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Model Provider.
        /// </summary>
        public static string AssetType_ModelProvider_Names_plural {
            get {
                return ResourceManager.GetString("AssetType_ModelProvider_Names_plural", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Model Provider.
        /// </summary>
        public static string AssetType_ModelProvider_Names_singular {
            get {
                return ResourceManager.GetString("AssetType_ModelProvider_Names_singular", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Continue, and you&apos;ll be redirected to the model catalog in Azure AI Foundry where you can finish subscribing and start using this model..
        /// </summary>
        public static string AssetType_ModelProvider_Page_Description_body {
            get {
                return ResourceManager.GetString("AssetType_ModelProvider_Page_Description_body", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Thank you for your interest..
        /// </summary>
        public static string AssetType_ModelProvider_Page_Description_intro {
            get {
                return ResourceManager.GetString("AssetType_ModelProvider_Page_Description_intro", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Continue to Azure AI Foundry model catalog.
        /// </summary>
        public static string AssetType_ModelProvider_Page_primaryButton {
            get {
                return ResourceManager.GetString("AssetType_ModelProvider_Page_primaryButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Continue to Azure AI Foundry to use this offer.
        /// </summary>
        public static string AssetType_ModelProvider_Page_title {
            get {
                return ResourceManager.GetString("AssetType_ModelProvider_Page_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Nixtla.
        /// </summary>
        public static string AssetType_Nixtla_Names_lowerPlural {
            get {
                return ResourceManager.GetString("AssetType_Nixtla_Names_lowerPlural", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Nixtla.
        /// </summary>
        public static string AssetType_Nixtla_Names_lowerSingular {
            get {
                return ResourceManager.GetString("AssetType_Nixtla_Names_lowerSingular", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Nixtla.
        /// </summary>
        public static string AssetType_Nixtla_Names_plural {
            get {
                return ResourceManager.GetString("AssetType_Nixtla_Names_plural", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Nixtla.
        /// </summary>
        public static string AssetType_Nixtla_Names_singular {
            get {
                return ResourceManager.GetString("AssetType_Nixtla_Names_singular", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Continue, and you&apos;ll be redirected to the model catalog in Azure AI Foundry where you can finish subscribing and start using this model..
        /// </summary>
        public static string AssetType_Nixtla_Page_Description_body {
            get {
                return ResourceManager.GetString("AssetType_Nixtla_Page_Description_body", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Thank you for your interest..
        /// </summary>
        public static string AssetType_Nixtla_Page_Description_intro {
            get {
                return ResourceManager.GetString("AssetType_Nixtla_Page_Description_intro", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Continue to Azure AI Foundry model catalog.
        /// </summary>
        public static string AssetType_Nixtla_Page_primaryButton {
            get {
                return ResourceManager.GetString("AssetType_Nixtla_Page_primaryButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Continue to Azure AI Foundry to use this offer.
        /// </summary>
        public static string AssetType_Nixtla_Page_title {
            get {
                return ResourceManager.GetString("AssetType_Nixtla_Page_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Retired - Machine Learning Experimentation.
        /// </summary>
        public static string assetTypeNames_MachineLearningExperimentationAccount_singular {
            get {
                return ResourceManager.GetString("assetTypeNames_MachineLearningExperimentationAccount_singular", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to workspaces.
        /// </summary>
        public static string AssetTypeNames_MachineLearningServices_Default_lowerPlural {
            get {
                return ResourceManager.GetString("AssetTypeNames_MachineLearningServices_Default_lowerPlural", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to workspace.
        /// </summary>
        public static string AssetTypeNames_MachineLearningServices_Default_lowerSingular {
            get {
                return ResourceManager.GetString("AssetTypeNames_MachineLearningServices_Default_lowerSingular", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Azure Machine Learning workspaces.
        /// </summary>
        public static string AssetTypeNames_MachineLearningServices_Default_plural {
            get {
                return ResourceManager.GetString("AssetTypeNames_MachineLearningServices_Default_plural", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Azure Machine Learning workspace.
        /// </summary>
        public static string AssetTypeNames_MachineLearningServices_Default_singular {
            get {
                return ResourceManager.GetString("AssetTypeNames_MachineLearningServices_Default_singular", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to feature stores.
        /// </summary>
        public static string AssetTypeNames_MachineLearningServices_FeatureStore_lowerPlural {
            get {
                return ResourceManager.GetString("AssetTypeNames_MachineLearningServices_FeatureStore_lowerPlural", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to feature store.
        /// </summary>
        public static string AssetTypeNames_MachineLearningServices_FeatureStore_lowerSingular {
            get {
                return ResourceManager.GetString("AssetTypeNames_MachineLearningServices_FeatureStore_lowerSingular", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Azure Machine Learning feature stores.
        /// </summary>
        public static string AssetTypeNames_MachineLearningServices_FeatureStore_plural {
            get {
                return ResourceManager.GetString("AssetTypeNames_MachineLearningServices_FeatureStore_plural", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Azure Machine Learning feature store.
        /// </summary>
        public static string AssetTypeNames_MachineLearningServices_FeatureStore_singular {
            get {
                return ResourceManager.GetString("AssetTypeNames_MachineLearningServices_FeatureStore_singular", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Azure AI hubs.
        /// </summary>
        public static string AssetTypeNames_MachineLearningServices_Hub_lowerPlural {
            get {
                return ResourceManager.GetString("AssetTypeNames_MachineLearningServices_Hub_lowerPlural", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Azure AI hub.
        /// </summary>
        public static string AssetTypeNames_MachineLearningServices_Hub_lowerSingular {
            get {
                return ResourceManager.GetString("AssetTypeNames_MachineLearningServices_Hub_lowerSingular", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Azure AI hubs.
        /// </summary>
        public static string AssetTypeNames_MachineLearningServices_Hub_plural {
            get {
                return ResourceManager.GetString("AssetTypeNames_MachineLearningServices_Hub_plural", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Azure AI hub.
        /// </summary>
        public static string AssetTypeNames_MachineLearningServices_Hub_singular {
            get {
                return ResourceManager.GetString("AssetTypeNames_MachineLearningServices_Hub_singular", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to workspaces.
        /// </summary>
        public static string AssetTypeNames_MachineLearningServices_lowerPlural {
            get {
                return ResourceManager.GetString("AssetTypeNames_MachineLearningServices_lowerPlural", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to workspace.
        /// </summary>
        public static string AssetTypeNames_MachineLearningServices_lowerSingular {
            get {
                return ResourceManager.GetString("AssetTypeNames_MachineLearningServices_lowerSingular", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Azure Machine Learning.
        /// </summary>
        public static string AssetTypeNames_MachineLearningServices_plural {
            get {
                return ResourceManager.GetString("AssetTypeNames_MachineLearningServices_plural", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Azure AI projects.
        /// </summary>
        public static string AssetTypeNames_MachineLearningServices_Project_lowerPlural {
            get {
                return ResourceManager.GetString("AssetTypeNames_MachineLearningServices_Project_lowerPlural", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Azure AI project.
        /// </summary>
        public static string AssetTypeNames_MachineLearningServices_Project_lowerSingular {
            get {
                return ResourceManager.GetString("AssetTypeNames_MachineLearningServices_Project_lowerSingular", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Azure AI projects.
        /// </summary>
        public static string AssetTypeNames_MachineLearningServices_Project_plural {
            get {
                return ResourceManager.GetString("AssetTypeNames_MachineLearningServices_Project_plural", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Azure AI project.
        /// </summary>
        public static string AssetTypeNames_MachineLearningServices_Project_singular {
            get {
                return ResourceManager.GetString("AssetTypeNames_MachineLearningServices_Project_singular", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Azure Machine Learning workspace.
        /// </summary>
        public static string AssetTypeNames_MachineLearningServices_singular {
            get {
                return ResourceManager.GetString("AssetTypeNames_MachineLearningServices_singular", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to machine learning online endpoints.
        /// </summary>
        public static string AssetTypeNames_MLApp_lowerPlural {
            get {
                return ResourceManager.GetString("AssetTypeNames_MLApp_lowerPlural", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to machine learning online endpoint.
        /// </summary>
        public static string AssetTypeNames_MLApp_lowerSingular {
            get {
                return ResourceManager.GetString("AssetTypeNames_MLApp_lowerSingular", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Machine learning online endpoints.
        /// </summary>
        public static string AssetTypeNames_MLApp_plural {
            get {
                return ResourceManager.GetString("AssetTypeNames_MLApp_plural", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Machine learning online endpoint.
        /// </summary>
        public static string AssetTypeNames_MLApp_singular {
            get {
                return ResourceManager.GetString("AssetTypeNames_MLApp_singular", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to machine learning online deployments.
        /// </summary>
        public static string AssetTypeNames_MLAppDeployment_lowerPlural {
            get {
                return ResourceManager.GetString("AssetTypeNames_MLAppDeployment_lowerPlural", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to machine learning online deployment.
        /// </summary>
        public static string AssetTypeNames_MLAppDeployment_lowerSingular {
            get {
                return ResourceManager.GetString("AssetTypeNames_MLAppDeployment_lowerSingular", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Machine learning online deployments.
        /// </summary>
        public static string AssetTypeNames_MLAppDeployment_plural {
            get {
                return ResourceManager.GetString("AssetTypeNames_MLAppDeployment_plural", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Machine learning online deployment.
        /// </summary>
        public static string AssetTypeNames_MLAppDeployment_singular {
            get {
                return ResourceManager.GetString("AssetTypeNames_MLAppDeployment_singular", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to azure machine learning registries.
        /// </summary>
        public static string AssetTypeNames_MLRegistry_lowerPlural {
            get {
                return ResourceManager.GetString("AssetTypeNames_MLRegistry_lowerPlural", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to azure machine learning registry.
        /// </summary>
        public static string AssetTypeNames_MLRegistry_lowerSingular {
            get {
                return ResourceManager.GetString("AssetTypeNames_MLRegistry_lowerSingular", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Azure Machine Learning registries.
        /// </summary>
        public static string AssetTypeNames_MLRegistry_plural {
            get {
                return ResourceManager.GetString("AssetTypeNames_MLRegistry_plural", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Azure Machine Learning registry.
        /// </summary>
        public static string AssetTypeNames_MLRegistry_singular {
            get {
                return ResourceManager.GetString("AssetTypeNames_MLRegistry_singular", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select an application insights.
        /// </summary>
        public static string AssociatedResource_AppInsights_ChangeBlade_title {
            get {
                return ResourceManager.GetString("AssociatedResource_AppInsights_ChangeBlade_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Change application insights.
        /// </summary>
        public static string AssociatedResource_AppInsights_Properties_changeText {
            get {
                return ResourceManager.GetString("AssociatedResource_AppInsights_Properties_changeText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Application insights.
        /// </summary>
        public static string AssociatedResource_AppInsights_ReactView_ChangeAppInsights_appInsightsLabel {
            get {
                return ResourceManager.GetString("AssociatedResource_AppInsights_ReactView_ChangeAppInsights_appInsightsLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Application insights updating....
        /// </summary>
        public static string AssociatedResource_AppInsights_ReactView_ChangeAppInsights_appInsightsUpdatingSpinner {
            get {
                return ResourceManager.GetString("AssociatedResource_AppInsights_ReactView_ChangeAppInsights_appInsightsUpdatingSpi" +
                        "nner", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to An error occurred while updating the application insights for this resource..
        /// </summary>
        public static string AssociatedResource_AppInsights_ReactView_ChangeAppInsights_error {
            get {
                return ResourceManager.GetString("AssociatedResource_AppInsights_ReactView_ChangeAppInsights_error", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error updating application insights.
        /// </summary>
        public static string AssociatedResource_AppInsights_ReactView_ChangeAppInsights_errorTitle {
            get {
                return ResourceManager.GetString("AssociatedResource_AppInsights_ReactView_ChangeAppInsights_errorTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Discard.
        /// </summary>
        public static string AssociatedResource_AppInsights_ReactView_ChangeAppInsights_footerCancel {
            get {
                return ResourceManager.GetString("AssociatedResource_AppInsights_ReactView_ChangeAppInsights_footerCancel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save.
        /// </summary>
        public static string AssociatedResource_AppInsights_ReactView_ChangeAppInsights_footerSave {
            get {
                return ResourceManager.GetString("AssociatedResource_AppInsights_ReactView_ChangeAppInsights_footerSave", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The resource being updated could not be found..
        /// </summary>
        public static string AssociatedResource_AppInsights_ReactView_ChangeAppInsights_notFoundError {
            get {
                return ResourceManager.GetString("AssociatedResource_AppInsights_ReactView_ChangeAppInsights_notFoundError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You do not have permission to update the application insights for this resource..
        /// </summary>
        public static string AssociatedResource_AppInsights_ReactView_ChangeAppInsights_unauthorizedError {
            get {
                return ResourceManager.GetString("AssociatedResource_AppInsights_ReactView_ChangeAppInsights_unauthorizedError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No application insights resources found.
        /// </summary>
        public static string AssociatedResource_AppInsights_ReactView_Dropdown_emptyMessage {
            get {
                return ResourceManager.GetString("AssociatedResource_AppInsights_ReactView_Dropdown_emptyMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error loading application insights.
        /// </summary>
        public static string AssociatedResource_AppInsights_ReactView_Dropdown_errorLoadingMessage {
            get {
                return ResourceManager.GetString("AssociatedResource_AppInsights_ReactView_Dropdown_errorLoadingMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No results matching {0}.
        /// </summary>
        public static string AssociatedResource_AppInsights_ReactView_Dropdown_noMatchMessage {
            get {
                return ResourceManager.GetString("AssociatedResource_AppInsights_ReactView_Dropdown_noMatchMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select an application insights.
        /// </summary>
        public static string AssociatedResource_AppInsights_ReactView_Dropdown_placeholder {
            get {
                return ResourceManager.GetString("AssociatedResource_AppInsights_ReactView_Dropdown_placeholder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select a container registry.
        /// </summary>
        public static string AssociatedResource_ContainerRegistry_ChangeBlade_title {
            get {
                return ResourceManager.GetString("AssociatedResource_ContainerRegistry_ChangeBlade_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Change container registry.
        /// </summary>
        public static string AssociatedResource_ContainerRegistry_Properties_changeText {
            get {
                return ResourceManager.GetString("AssociatedResource_ContainerRegistry_Properties_changeText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Container registry.
        /// </summary>
        public static string AssociatedResource_ContainerRegistry_ReactView_ChangeContainerRegistry_containerRegistryLabel {
            get {
                return ResourceManager.GetString("AssociatedResource_ContainerRegistry_ReactView_ChangeContainerRegistry_containerR" +
                        "egistryLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Container registry updating....
        /// </summary>
        public static string AssociatedResource_ContainerRegistry_ReactView_ChangeContainerRegistry_containerRegistryUpdatingSpinner {
            get {
                return ResourceManager.GetString("AssociatedResource_ContainerRegistry_ReactView_ChangeContainerRegistry_containerR" +
                        "egistryUpdatingSpinner", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to An error occurred while updating the container registry for this resource..
        /// </summary>
        public static string AssociatedResource_ContainerRegistry_ReactView_ChangeContainerRegistry_error {
            get {
                return ResourceManager.GetString("AssociatedResource_ContainerRegistry_ReactView_ChangeContainerRegistry_error", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error updating container registry.
        /// </summary>
        public static string AssociatedResource_ContainerRegistry_ReactView_ChangeContainerRegistry_errorTitle {
            get {
                return ResourceManager.GetString("AssociatedResource_ContainerRegistry_ReactView_ChangeContainerRegistry_errorTitle" +
                        "", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Discard.
        /// </summary>
        public static string AssociatedResource_ContainerRegistry_ReactView_ChangeContainerRegistry_footerCancel {
            get {
                return ResourceManager.GetString("AssociatedResource_ContainerRegistry_ReactView_ChangeContainerRegistry_footerCanc" +
                        "el", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save.
        /// </summary>
        public static string AssociatedResource_ContainerRegistry_ReactView_ChangeContainerRegistry_footerSave {
            get {
                return ResourceManager.GetString("AssociatedResource_ContainerRegistry_ReactView_ChangeContainerRegistry_footerSave" +
                        "", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The resource being updated could not be found..
        /// </summary>
        public static string AssociatedResource_ContainerRegistry_ReactView_ChangeContainerRegistry_notFoundError {
            get {
                return ResourceManager.GetString("AssociatedResource_ContainerRegistry_ReactView_ChangeContainerRegistry_notFoundEr" +
                        "ror", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You do not have permission to update the container registry for this resource..
        /// </summary>
        public static string AssociatedResource_ContainerRegistry_ReactView_ChangeContainerRegistry_unauthorizedError {
            get {
                return ResourceManager.GetString("AssociatedResource_ContainerRegistry_ReactView_ChangeContainerRegistry_unauthoriz" +
                        "edError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No container registry resources found.
        /// </summary>
        public static string AssociatedResource_ContainerRegistry_ReactView_Dropdown_emptyMessage {
            get {
                return ResourceManager.GetString("AssociatedResource_ContainerRegistry_ReactView_Dropdown_emptyMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error loading container registry.
        /// </summary>
        public static string AssociatedResource_ContainerRegistry_ReactView_Dropdown_errorLoadingMessage {
            get {
                return ResourceManager.GetString("AssociatedResource_ContainerRegistry_ReactView_Dropdown_errorLoadingMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No results matching {0}.
        /// </summary>
        public static string AssociatedResource_ContainerRegistry_ReactView_Dropdown_noMatchMessage {
            get {
                return ResourceManager.GetString("AssociatedResource_ContainerRegistry_ReactView_Dropdown_noMatchMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select a container registry.
        /// </summary>
        public static string AssociatedResource_ContainerRegistry_ReactView_Dropdown_placeholder {
            get {
                return ResourceManager.GetString("AssociatedResource_ContainerRegistry_ReactView_Dropdown_placeholder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create new.
        /// </summary>
        public static string AssociatedResource_Dropdown_createNewLinkText {
            get {
                return ResourceManager.GetString("AssociatedResource_Dropdown_createNewLinkText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select existing....
        /// </summary>
        public static string AssociatedResource_Dropdown_filterPlaceholder {
            get {
                return ResourceManager.GetString("AssociatedResource_Dropdown_filterPlaceholder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to (new) {0}.
        /// </summary>
        public static string AssociatedResource_Dropdown_newResourceText {
            get {
                return ResourceManager.GetString("AssociatedResource_Dropdown_newResourceText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to None.
        /// </summary>
        public static string AssociatedResource_Dropdown_none {
            get {
                return ResourceManager.GetString("AssociatedResource_Dropdown_none", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Discard.
        /// </summary>
        public static string AssociatedResource_Dropdown_SettingsBlade_discardButtonText {
            get {
                return ResourceManager.GetString("AssociatedResource_Dropdown_SettingsBlade_discardButtonText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save.
        /// </summary>
        public static string AssociatedResource_Dropdown_SettingsBlade_saveButtonText {
            get {
                return ResourceManager.GetString("AssociatedResource_Dropdown_SettingsBlade_saveButtonText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Download a template for automation.
        /// </summary>
        public static string automationLink {
            get {
                return ResourceManager.GetString("automationLink", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Basics.
        /// </summary>
        public static string basics {
            get {
                return ResourceManager.GetString("basics", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Every workspace must be assigned to an Azure subscription, which is where billing happens. You use resource groups like folders to organize and manage resources, including the workspace you&apos;re about to create..
        /// </summary>
        public static string basicsBladeDetailsIntro {
            get {
                return ResourceManager.GetString("basicsBladeDetailsIntro", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Learn more about Azure resource groups.
        /// </summary>
        public static string basicsBladeDetailsIntroLearnMore {
            get {
                return ResourceManager.GetString("basicsBladeDetailsIntroLearnMore", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select the subscription to manage deployed resources and costs. Use resource groups like folders to organize and manage all your resources. An AI hub is a collaboration environment for a team to share project work, model endpoints, compute, (data) connections, security settings, govern usage..
        /// </summary>
        public static string basicsBladeHubDetailsIntro {
            get {
                return ResourceManager.GetString("basicsBladeHubDetailsIntro", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Configure your basic workspace settings like its storage connection, authentication, container, and more..
        /// </summary>
        public static string basicsBladeInstanceIntro {
            get {
                return ResourceManager.GetString("basicsBladeInstanceIntro", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Learn more.
        /// </summary>
        public static string basicsBladeInstanceIntroLearnMore {
            get {
                return ResourceManager.GetString("basicsBladeInstanceIntroLearnMore", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select a subscription and resource group to organize this and other resources, inherit access, and attribute cost..
        /// </summary>
        public static string basicsBladeProjectDetailsIntro {
            get {
                return ResourceManager.GetString("basicsBladeProjectDetailsIntro", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Configure your basic registry settings like its name and description..
        /// </summary>
        public static string basicsBladeRegistryInstanceIntro {
            get {
                return ResourceManager.GetString("basicsBladeRegistryInstanceIntro", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Every registry must be assigned to an Azure subscription, which is where billing happens. You use resource groups like folders to organize and manage resources, including the registry you&apos;re about to create..
        /// </summary>
        public static string basicsRegistryBladeDetailsIntro {
            get {
                return ResourceManager.GetString("basicsRegistryBladeDetailsIntro", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Azure AI services base models.
        /// </summary>
        public static string BasicsTab_AIServiceSectionHeader {
            get {
                return ResourceManager.GetString("BasicsTab_AIServiceSectionHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Name of the AI hub.
        /// </summary>
        public static string BasicsTab_HubName_BalloonContent {
            get {
                return ResourceManager.GetString("BasicsTab_HubName_BalloonContent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Projects are grouped by a hub, which provides security configurations, pre-configured connectivity with other Azure resources, compute, storage, and quota..
        /// </summary>
        public static string BasicsTab_hubSectionContent {
            get {
                return ResourceManager.GetString("BasicsTab_hubSectionContent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Share security, connectivity, compute.
        /// </summary>
        public static string BasicsTab_hubSectionHeader {
            get {
                return ResourceManager.GetString("BasicsTab_hubSectionHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to For your convenience, these resources are added automatically to the workspace, if regionally available: .
        /// </summary>
        public static string BasicsTab_InfoText_AdditionalResourceInfoText {
            get {
                return ResourceManager.GetString("BasicsTab_InfoText_AdditionalResourceInfoText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Azure Application Insights.
        /// </summary>
        public static string BasicsTab_InfoText_AzureApplicationInsights {
            get {
                return ResourceManager.GetString("BasicsTab_InfoText_AzureApplicationInsights", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Azure Key Vault.
        /// </summary>
        public static string BasicsTab_InfoText_AzureKeyVault {
            get {
                return ResourceManager.GetString("BasicsTab_InfoText_AzureKeyVault", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Azure Storage.
        /// </summary>
        public static string BasicsTab_InfoText_AzureStorage {
            get {
                return ResourceManager.GetString("BasicsTab_InfoText_AzureStorage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Organization.
        /// </summary>
        public static string BasicsTab_Organization {
            get {
                return ResourceManager.GetString("BasicsTab_Organization", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Name of the AI project.
        /// </summary>
        public static string BasicsTab_ProjectName_BalloonContent {
            get {
                return ResourceManager.GetString("BasicsTab_ProjectName_BalloonContent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Compute targets can only be created in the same region as the workspace. Ensure the selected region has the virtual machine series needed for your workspace compute targets..
        /// </summary>
        public static string BasicsTab_Region_BalloonContent {
            get {
                return ResourceManager.GetString("BasicsTab_Region_BalloonContent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Learn more about compute targets.
        /// </summary>
        public static string BasicsTab_Region_LearnMoreComputeTargets {
            get {
                return ResourceManager.GetString("BasicsTab_Region_LearnMoreComputeTargets", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to View available virtual machines series by region.
        /// </summary>
        public static string BasicsTab_Region_ViewAvailableVirtualMachines {
            get {
                return ResourceManager.GetString("BasicsTab_Region_ViewAvailableVirtualMachines", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Registry details.
        /// </summary>
        public static string BasicsTab_RegistryDetails {
            get {
                return ResourceManager.GetString("BasicsTab_RegistryDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The registry name must be unique within your resource group..
        /// </summary>
        public static string BasicsTab_RegistryName_BalloonContent {
            get {
                return ResourceManager.GetString("BasicsTab_RegistryName_BalloonContent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to A resource group is a collection of resources that share the same life cycle, permissions, and policies..
        /// </summary>
        public static string BasicsTab_ResourceGroup_BalloonContent {
            get {
                return ResourceManager.GetString("BasicsTab_ResourceGroup_BalloonContent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to All resources in an Azure subscription are billed together..
        /// </summary>
        public static string BasicsTab_Subscription_BalloonContent {
            get {
                return ResourceManager.GetString("BasicsTab_Subscription_BalloonContent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The selected subscription doesn’t have permissions to register the resource provider..
        /// </summary>
        public static string BasicsTab_WarningText_WorkspaceErrorMessageText {
            get {
                return ResourceManager.GetString("BasicsTab_WarningText_WorkspaceErrorMessageText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Workspace details.
        /// </summary>
        public static string BasicsTab_WorkspaceDetails {
            get {
                return ResourceManager.GetString("BasicsTab_WorkspaceDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Basic.
        /// </summary>
        public static string BasicsTab_WorkspaceEdition_Basic {
            get {
                return ResourceManager.GetString("BasicsTab_WorkspaceEdition_Basic", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enterprise.
        /// </summary>
        public static string BasicsTab_WorkspaceEdition_Enterprise {
            get {
                return ResourceManager.GetString("BasicsTab_WorkspaceEdition_Enterprise", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to View full pricing details.
        /// </summary>
        public static string BasicsTab_WorkspaceEdition_ViewFullPricingDetails {
            get {
                return ResourceManager.GetString("BasicsTab_WorkspaceEdition_ViewFullPricingDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Resource details.
        /// </summary>
        public static string BasicsTab_WorkspaceHubDetails {
            get {
                return ResourceManager.GetString("BasicsTab_WorkspaceHubDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The workspace name must be unique within your resource group..
        /// </summary>
        public static string BasicsTab_WorkspaceName_BalloonContent {
            get {
                return ResourceManager.GetString("BasicsTab_WorkspaceName_BalloonContent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Basics.
        /// </summary>
        public static string basicsTabTitle {
            get {
                return ResourceManager.GetString("basicsTabTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New workspace.
        /// </summary>
        public static string Browse_addMachineLearningLabel {
            get {
                return ResourceManager.GetString("Browse_addMachineLearningLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New registry.
        /// </summary>
        public static string Browse_addMachineLearningRegistryLabel {
            get {
                return ResourceManager.GetString("Browse_addMachineLearningRegistryLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create.
        /// </summary>
        public static string buttonCreate {
            get {
                return ResourceManager.GetString("buttonCreate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Next : {0}.
        /// </summary>
        public static string buttonNext {
            get {
                return ResourceManager.GetString("buttonNext", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Next &gt;.
        /// </summary>
        public static string buttonNextPlaceholder {
            get {
                return ResourceManager.GetString("buttonNextPlaceholder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &lt; Previous.
        /// </summary>
        public static string buttonPrevious {
            get {
                return ResourceManager.GetString("buttonPrevious", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Review + create.
        /// </summary>
        public static string buttonReviewCreate {
            get {
                return ResourceManager.GetString("buttonReviewCreate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to How easy or difficult was it to manage workspace outbound network settings on this page?.
        /// </summary>
        public static string CES_ManagedWorkspaceOutbound_question {
            get {
                return ResourceManager.GetString("CES_ManagedWorkspaceOutbound_question", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to How easy or difficult was it to manage private endpoints connections on this page?.
        /// </summary>
        public static string CES_PrivateEndpoints__question {
            get {
                return ResourceManager.GetString("CES_PrivateEndpoints__question", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to How easy or difficult was it to manage public access networking settings on this page?.
        /// </summary>
        public static string CES_PublicAccess__question {
            get {
                return ResourceManager.GetString("CES_PublicAccess__question", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Created on.
        /// </summary>
        public static string ColumnCreationTime {
            get {
                return ResourceManager.GetString("ColumnCreationTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Workspace ID.
        /// </summary>
        public static string ColumnMachineLearningWorkspaceId {
            get {
                return ResourceManager.GetString("ColumnMachineLearningWorkspaceId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cancel.
        /// </summary>
        public static string CommandCancel {
            get {
                return ResourceManager.GetString("CommandCancel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create project.
        /// </summary>
        public static string CommandCreateProject {
            get {
                return ResourceManager.GetString("CommandCreateProject", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete.
        /// </summary>
        public static string CommandDelete {
            get {
                return ResourceManager.GetString("CommandDelete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Download config.json.
        /// </summary>
        public static string CommandDownloadConfig {
            get {
                return ResourceManager.GetString("CommandDownloadConfig", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Use this file to load the workspace configuration in your Azure ML SDK notebook or Python script.
        /// </summary>
        public static string CommandDownloadConfigTooltip {
            get {
                return ResourceManager.GetString("CommandDownloadConfigTooltip", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete.
        /// </summary>
        public static string Commands_delete {
            get {
                return ResourceManager.GetString("Commands_delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete Workspace {0}?.
        /// </summary>
        public static string ConfirmationMachineLearningDelete {
            get {
                return ResourceManager.GetString("ConfirmationMachineLearningDelete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete Registry {0}?.
        /// </summary>
        public static string ConfirmationMachineLearningRegistryDelete {
            get {
                return ResourceManager.GetString("ConfirmationMachineLearningRegistryDelete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create new container registry.
        /// </summary>
        public static string ContainerRegistry_Dropdown_createNewAriaLabel {
            get {
                return ResourceManager.GetString("ContainerRegistry_Dropdown_createNewAriaLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to A container registry is used to register docker images used in training and deployments. To minimize costs, a new Azure Container Registry resource is created only after you build your first image. Alternatively, you may choose to create the resource now or select an existing one in your subscription..
        /// </summary>
        public static string ContainerRegistry_Dropdown_info {
            get {
                return ResourceManager.GetString("ContainerRegistry_Dropdown_info", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Container registry.
        /// </summary>
        public static string ContainerRegistry_Dropdown_label {
            get {
                return ResourceManager.GetString("ContainerRegistry_Dropdown_label", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The name must meet the following requirements:.
        /// </summary>
        public static string ContainerRegistry_Dropdown_SettingsBlade_Name_infoDescription {
            get {
                return ResourceManager.GetString("ContainerRegistry_Dropdown_SettingsBlade_Name_infoDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unique across all container registries in Azure.
        /// </summary>
        public static string ContainerRegistry_Dropdown_SettingsBlade_Name_infoItem1 {
            get {
                return ResourceManager.GetString("ContainerRegistry_Dropdown_SettingsBlade_Name_infoItem1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Between 5 and 50 characters long.
        /// </summary>
        public static string ContainerRegistry_Dropdown_SettingsBlade_Name_infoItem2 {
            get {
                return ResourceManager.GetString("ContainerRegistry_Dropdown_SettingsBlade_Name_infoItem2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Only contain alphanumeric characters.
        /// </summary>
        public static string ContainerRegistry_Dropdown_SettingsBlade_Name_infoItem3 {
            get {
                return ResourceManager.GetString("ContainerRegistry_Dropdown_SettingsBlade_Name_infoItem3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Resource names may contain alpha numeric characters only and must be between 5 and 50 characters..
        /// </summary>
        public static string ContainerRegistry_Dropdown_SettingsBlade_nameInvalidMessage {
            get {
                return ResourceManager.GetString("ContainerRegistry_Dropdown_SettingsBlade_nameInvalidMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Name.
        /// </summary>
        public static string ContainerRegistry_Dropdown_SettingsBlade_nameLabel {
            get {
                return ResourceManager.GetString("ContainerRegistry_Dropdown_SettingsBlade_nameLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to All SKUs provide the same programmatic capabilities. Choosing a higher SKU will provide more performance and scale..
        /// </summary>
        public static string ContainerRegistry_Dropdown_SettingsBlade_Sku_info {
            get {
                return ResourceManager.GetString("ContainerRegistry_Dropdown_SettingsBlade_Sku_info", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Learn more.
        /// </summary>
        public static string ContainerRegistry_Dropdown_SettingsBlade_Sku_infoLearnMore {
            get {
                return ResourceManager.GetString("ContainerRegistry_Dropdown_SettingsBlade_Sku_infoLearnMore", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Basic.
        /// </summary>
        public static string ContainerRegistry_Dropdown_SettingsBlade_skuBasic {
            get {
                return ResourceManager.GetString("ContainerRegistry_Dropdown_SettingsBlade_skuBasic", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SKU.
        /// </summary>
        public static string ContainerRegistry_Dropdown_SettingsBlade_skuLabel {
            get {
                return ResourceManager.GetString("ContainerRegistry_Dropdown_SettingsBlade_skuLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Premium.
        /// </summary>
        public static string ContainerRegistry_Dropdown_SettingsBlade_skuPremium {
            get {
                return ResourceManager.GetString("ContainerRegistry_Dropdown_SettingsBlade_skuPremium", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Standard.
        /// </summary>
        public static string ContainerRegistry_Dropdown_SettingsBlade_skuStandard {
            get {
                return ResourceManager.GetString("ContainerRegistry_Dropdown_SettingsBlade_skuStandard", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create new container registry.
        /// </summary>
        public static string ContainerRegistry_Dropdown_SettingsBlade_title {
            get {
                return ResourceManager.GetString("ContainerRegistry_Dropdown_SettingsBlade_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Container registry.
        /// </summary>
        public static string ContainerRegistry_SummaryTab_label {
            get {
                return ResourceManager.GetString("ContainerRegistry_SummaryTab_label", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create.
        /// </summary>
        public static string Create_CreateButton_label {
            get {
                return ResourceManager.GetString("Create_CreateButton_label", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create Azure Machine Learning.
        /// </summary>
        public static string Create_Wizard_title {
            get {
                return ResourceManager.GetString("Create_Wizard_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cosmos DB for customer managed keys.
        /// </summary>
        public static string CreateBlade_Advanced_Review_byoCosmosText {
            get {
                return ResourceManager.GetString("CreateBlade_Advanced_Review_byoCosmosText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Search service for customer managed keys.
        /// </summary>
        public static string CreateBlade_Advanced_Review_byoSearchText {
            get {
                return ResourceManager.GetString("CreateBlade_Advanced_Review_byoSearchText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Storage account for customer managed keys.
        /// </summary>
        public static string CreateBlade_Advanced_Review_byoStorageText {
            get {
                return ResourceManager.GetString("CreateBlade_Advanced_Review_byoStorageText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} ({1}).
        /// </summary>
        public static string CreateBlade_Advanced_Review_byoValueFormat {
            get {
                return ResourceManager.GetString("CreateBlade_Advanced_Review_byoValueFormat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Credential-based.
        /// </summary>
        public static string CreateBlade_Advanced_Review_credentialBasedStorageAccountAccess {
            get {
                return ResourceManager.GetString("CreateBlade_Advanced_Review_credentialBasedStorageAccountAccess", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer-managed keys.
        /// </summary>
        public static string CreateBlade_Advanced_Review_customerManagedKeys {
            get {
                return ResourceManager.GetString("CreateBlade_Advanced_Review_customerManagedKeys", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Disabled.
        /// </summary>
        public static string CreateBlade_Advanced_Review_disabled {
            get {
                return ResourceManager.GetString("CreateBlade_Advanced_Review_disabled", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enabled.
        /// </summary>
        public static string CreateBlade_Advanced_Review_enabled {
            get {
                return ResourceManager.GetString("CreateBlade_Advanced_Review_enabled", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enable HBI Flag.
        /// </summary>
        public static string CreateBlade_Advanced_Review_enableHBIFlag {
            get {
                return ResourceManager.GetString("CreateBlade_Advanced_Review_enableHBIFlag", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Encryption.
        /// </summary>
        public static string CreateBlade_Advanced_Review_Encryption_title {
            get {
                return ResourceManager.GetString("CreateBlade_Advanced_Review_Encryption_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Encryption type.
        /// </summary>
        public static string CreateBlade_Advanced_Review_encryptionType {
            get {
                return ResourceManager.GetString("CreateBlade_Advanced_Review_encryptionType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Identity.
        /// </summary>
        public static string CreateBlade_Advanced_Review_Identity_title {
            get {
                return ResourceManager.GetString("CreateBlade_Advanced_Review_Identity_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Identity-based.
        /// </summary>
        public static string CreateBlade_Advanced_Review_identityBasedStorageAccountAccess {
            get {
                return ResourceManager.GetString("CreateBlade_Advanced_Review_identityBasedStorageAccountAccess", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Key URI.
        /// </summary>
        public static string CreateBlade_Advanced_Review_keyURI {
            get {
                return ResourceManager.GetString("CreateBlade_Advanced_Review_keyURI", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Key vault.
        /// </summary>
        public static string CreateBlade_Advanced_Review_keyVault {
            get {
                return ResourceManager.GetString("CreateBlade_Advanced_Review_keyVault", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Microsoft-managed keys.
        /// </summary>
        public static string CreateBlade_Advanced_Review_microsoftManagedKeys {
            get {
                return ResourceManager.GetString("CreateBlade_Advanced_Review_microsoftManagedKeys", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Resources.
        /// </summary>
        public static string CreateBlade_Advanced_Review_Resources_title {
            get {
                return ResourceManager.GetString("CreateBlade_Advanced_Review_Resources_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shared key access.
        /// </summary>
        public static string CreateBlade_Advanced_Review_sharedKeyAccess {
            get {
                return ResourceManager.GetString("CreateBlade_Advanced_Review_sharedKeyAccess", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Storage account access type.
        /// </summary>
        public static string CreateBlade_Advanced_Review_storageAccountAccessType {
            get {
                return ResourceManager.GetString("CreateBlade_Advanced_Review_storageAccountAccessType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Azure Firewall SKU.
        /// </summary>
        public static string CreateBlade_Network_azureFirewallSku {
            get {
                return ResourceManager.GetString("CreateBlade_Network_azureFirewallSku", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Connectivity method.
        /// </summary>
        public static string CreateBlade_Network_connectivityMethod {
            get {
                return ResourceManager.GetString("CreateBlade_Network_connectivityMethod", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enable public access from all networks.
        /// </summary>
        public static string CreateBlade_Network_ConnectivityMethod_allNetworks {
            get {
                return ResourceManager.GetString("CreateBlade_Network_ConnectivityMethod_allNetworks", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Disable public access and use private endpoints.
        /// </summary>
        public static string CreateBlade_Network_ConnectivityMethod_private {
            get {
                return ResourceManager.GetString("CreateBlade_Network_ConnectivityMethod_private", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Private with Internet Outbound and Private with Approved Outbound requires premium SKU container registry for having a private endpoint connection. You can create a new premium container registry or select an existing premium container registry in the Basics tab to use with this private workspace..
        /// </summary>
        public static string CreateBlade_Network_ConnectivityMethod_skuPrivateEndpointErrorMessage {
            get {
                return ResourceManager.GetString("CreateBlade_Network_ConnectivityMethod_skuPrivateEndpointErrorMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Private DNS Zone.
        /// </summary>
        public static string CreateBlade_Network_displayPrivateDnsZone {
            get {
                return ResourceManager.GetString("CreateBlade_Network_displayPrivateDnsZone", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Region.
        /// </summary>
        public static string CreateBlade_Network_displayRegion {
            get {
                return ResourceManager.GetString("CreateBlade_Network_displayRegion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Resource group.
        /// </summary>
        public static string CreateBlade_Network_displayResourceGroup {
            get {
                return ResourceManager.GetString("CreateBlade_Network_displayResourceGroup", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Subnet.
        /// </summary>
        public static string CreateBlade_Network_displaySubnet {
            get {
                return ResourceManager.GetString("CreateBlade_Network_displaySubnet", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Subscription.
        /// </summary>
        public static string CreateBlade_Network_displaySubscription {
            get {
                return ResourceManager.GetString("CreateBlade_Network_displaySubscription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Target resource type.
        /// </summary>
        public static string CreateBlade_Network_displayTargetResourceType {
            get {
                return ResourceManager.GetString("CreateBlade_Network_displayTargetResourceType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Projects associated to an Azure AI hub share network, and can access resources in your virtual network without additional configuration. Choose the type of network isolation you need, from not isolated at all to an entirely separate virtual network managed by Azure Machine Learning..
        /// </summary>
        public static string CreateBlade_Network_Hub_networkIsolationDescription {
            get {
                return ResourceManager.GetString("CreateBlade_Network_Hub_networkIsolationDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow Internet Outbound.
        /// </summary>
        public static string CreateBlade_Network_NetworkIsolation_AllowInternetOutbound_title {
            get {
                return ResourceManager.GetString("CreateBlade_Network_NetworkIsolation_AllowInternetOutbound_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow Only Approved Outbound.
        /// </summary>
        public static string CreateBlade_Network_NetworkIsolation_AllowOnlyApprovedOutbound_title {
            get {
                return ResourceManager.GetString("CreateBlade_Network_NetworkIsolation_AllowOnlyApprovedOutbound_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Disabled.
        /// </summary>
        public static string CreateBlade_Network_NetworkIsolation_Disabled_title {
            get {
                return ResourceManager.GetString("CreateBlade_Network_NetworkIsolation_Disabled_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Workspace is accessed via private endpoint.
        /// </summary>
        public static string CreateBlade_Network_NetworkIsolation_PrivateApprovedOutbound_descriptionItemOne {
            get {
                return ResourceManager.GetString("CreateBlade_Network_NetworkIsolation_PrivateApprovedOutbound_descriptionItemOne", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Outbound data movement is restricted to approved targets.
        /// </summary>
        public static string CreateBlade_Network_NetworkIsolation_PrivateApprovedOutbound_descriptionItemThree {
            get {
                return ResourceManager.GetString("CreateBlade_Network_NetworkIsolation_PrivateApprovedOutbound_descriptionItemThree" +
                        "", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Compute can access allowlisted resources only.
        /// </summary>
        public static string CreateBlade_Network_NetworkIsolation_PrivateApprovedOutbound_descriptionItemTwo {
            get {
                return ResourceManager.GetString("CreateBlade_Network_NetworkIsolation_PrivateApprovedOutbound_descriptionItemTwo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Learn more about data exfiltration protection.
        /// </summary>
        public static string CreateBlade_Network_NetworkIsolation_PrivateApprovedOutbound_learnMore {
            get {
                return ResourceManager.GetString("CreateBlade_Network_NetworkIsolation_PrivateApprovedOutbound_learnMore", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Private with Approved Outbound.
        /// </summary>
        public static string CreateBlade_Network_NetworkIsolation_PrivateApprovedOutbound_title {
            get {
                return ResourceManager.GetString("CreateBlade_Network_NetworkIsolation_PrivateApprovedOutbound_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Workspace is accessed via private endpoint.
        /// </summary>
        public static string CreateBlade_Network_NetworkIsolation_PrivateInternetOutbound_descriptionItemOne {
            get {
                return ResourceManager.GetString("CreateBlade_Network_NetworkIsolation_PrivateInternetOutbound_descriptionItemOne", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Outbound data movement is unrestricted.
        /// </summary>
        public static string CreateBlade_Network_NetworkIsolation_PrivateInternetOutbound_descriptionItemThree {
            get {
                return ResourceManager.GetString("CreateBlade_Network_NetworkIsolation_PrivateInternetOutbound_descriptionItemThree" +
                        "", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Compute can access private resources.
        /// </summary>
        public static string CreateBlade_Network_NetworkIsolation_PrivateInternetOutbound_descriptionItemTwo {
            get {
                return ResourceManager.GetString("CreateBlade_Network_NetworkIsolation_PrivateInternetOutbound_descriptionItemTwo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Learn more about private networks.
        /// </summary>
        public static string CreateBlade_Network_NetworkIsolation_PrivateInternetOutbound_learnMore {
            get {
                return ResourceManager.GetString("CreateBlade_Network_NetworkIsolation_PrivateInternetOutbound_learnMore", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Private with Internet Outbound.
        /// </summary>
        public static string CreateBlade_Network_NetworkIsolation_PrivateInternetOutbound_title {
            get {
                return ResourceManager.GetString("CreateBlade_Network_NetworkIsolation_PrivateInternetOutbound_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Workspace is accessed via public endpoint.
        /// </summary>
        public static string CreateBlade_Network_NetworkIsolation_Public_descriptionItemOne {
            get {
                return ResourceManager.GetString("CreateBlade_Network_NetworkIsolation_Public_descriptionItemOne", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Outbound data movement is unrestricted.
        /// </summary>
        public static string CreateBlade_Network_NetworkIsolation_Public_descriptionItemThree {
            get {
                return ResourceManager.GetString("CreateBlade_Network_NetworkIsolation_Public_descriptionItemThree", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Compute can access public resources.
        /// </summary>
        public static string CreateBlade_Network_NetworkIsolation_Public_descriptionItemTwo {
            get {
                return ResourceManager.GetString("CreateBlade_Network_NetworkIsolation_Public_descriptionItemTwo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Learn more about public networks.
        /// </summary>
        public static string CreateBlade_Network_NetworkIsolation_Public_learnMore {
            get {
                return ResourceManager.GetString("CreateBlade_Network_NetworkIsolation_Public_learnMore", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Public.
        /// </summary>
        public static string CreateBlade_Network_NetworkIsolation_Public_title {
            get {
                return ResourceManager.GetString("CreateBlade_Network_NetworkIsolation_Public_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow Internet Outbound and Allow Only Approved Outbound requires premium SKU container registry for having a private endpoint connection..
        /// </summary>
        public static string CreateBlade_Network_NetworkIsolation_skuErrorMessage {
            get {
                return ResourceManager.GetString("CreateBlade_Network_NetworkIsolation_skuErrorMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Choose the type of network isolation you need for your workspace, from not isolated at all to an entirely separate virtual network managed by Azure Machine Learning..
        /// </summary>
        public static string CreateBlade_Network_networkIsolationDescription {
            get {
                return ResourceManager.GetString("CreateBlade_Network_networkIsolationDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Learn more about managed network isolation.
        /// </summary>
        public static string CreateBlade_Network_networkIsolationLearnMore {
            get {
                return ResourceManager.GetString("CreateBlade_Network_networkIsolationLearnMore", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Network isolation.
        /// </summary>
        public static string CreateBlade_Network_networkIsolationTitle {
            get {
                return ResourceManager.GetString("CreateBlade_Network_networkIsolationTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Missing required outbound access rules..
        /// </summary>
        public static string CreateBlade_Network_outboundRulesGridEmptyMessage {
            get {
                return ResourceManager.GetString("CreateBlade_Network_outboundRulesGridEmptyMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Configure basic settings in order to create outbound rules..
        /// </summary>
        public static string CreateBlade_Network_outboundRulesNotLoadedMessage {
            get {
                return ResourceManager.GetString("CreateBlade_Network_outboundRulesNotLoadedMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No required outbound rules while the outbound access to the public internet is enabled..
        /// </summary>
        public static string CreateBlade_Network_outboundRulesPublicInternetEnabledMessage {
            get {
                return ResourceManager.GetString("CreateBlade_Network_outboundRulesPublicInternetEnabledMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You can connect to your workspace either publicly or privately using a private endpoint..
        /// </summary>
        public static string CreateBlade_Network_overviewDescription {
            get {
                return ResourceManager.GetString("CreateBlade_Network_overviewDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Network connectivity.
        /// </summary>
        public static string CreateBlade_Network_overviewTitle {
            get {
                return ResourceManager.GetString("CreateBlade_Network_overviewTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create a private endpoint to allow a private connection to this resource..
        /// </summary>
        public static string CreateBlade_Network_privateEndpointDescription {
            get {
                return ResourceManager.GetString("CreateBlade_Network_privateEndpointDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add.
        /// </summary>
        public static string CreateBlade_Network_PrivateEndpoints_add {
            get {
                return ResourceManager.GetString("CreateBlade_Network_PrivateEndpoints_add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Click on add to create a private endpoint.
        /// </summary>
        public static string CreateBlade_Network_PrivateEndpoints_clickToAdd {
            get {
                return ResourceManager.GetString("CreateBlade_Network_PrivateEndpoints_clickToAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Workspace ({0}) and private endpoint connection ({1}) must be in the same location in order to get compute instance and clusters accessed properly in the virtual network..
        /// </summary>
        public static string CreateBlade_Network_PrivateEndpoints_locationMismatch {
            get {
                return ResourceManager.GetString("CreateBlade_Network_PrivateEndpoints_locationMismatch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Name.
        /// </summary>
        public static string CreateBlade_Network_PrivateEndpoints_name {
            get {
                return ResourceManager.GetString("CreateBlade_Network_PrivateEndpoints_name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} ({1}).
        /// </summary>
        public static string CreateBlade_Network_PrivateEndpoints_nameAndSubResource {
            get {
                return ResourceManager.GetString("CreateBlade_Network_PrivateEndpoints_nameAndSubResource", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} ({1}) ({2}).
        /// </summary>
        public static string CreateBlade_Network_PrivateEndpoints_nameSubResourceAndDns {
            get {
                return ResourceManager.GetString("CreateBlade_Network_PrivateEndpoints_nameSubResourceAndDns", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to -.
        /// </summary>
        public static string CreateBlade_Network_PrivateEndpoints_noContent {
            get {
                return ResourceManager.GetString("CreateBlade_Network_PrivateEndpoints_noContent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This is the specific sub-resource for the new registry that this private endpoint will be able to access..
        /// </summary>
        public static string CreateBlade_Network_PrivateEndpoints_registrySubResourceHelp {
            get {
                return ResourceManager.GetString("CreateBlade_Network_PrivateEndpoints_registrySubResourceHelp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Registry sub-resource.
        /// </summary>
        public static string CreateBlade_Network_PrivateEndpoints_registrySubResourceLabel {
            get {
                return ResourceManager.GetString("CreateBlade_Network_PrivateEndpoints_registrySubResourceLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This is the specific sub-resource for the new workspace that this private endpoint will be able to access..
        /// </summary>
        public static string CreateBlade_Network_PrivateEndpoints_subResourceHelp {
            get {
                return ResourceManager.GetString("CreateBlade_Network_PrivateEndpoints_subResourceHelp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Workspace sub-resource.
        /// </summary>
        public static string CreateBlade_Network_PrivateEndpoints_subResourceLabel {
            get {
                return ResourceManager.GetString("CreateBlade_Network_PrivateEndpoints_subResourceLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Private endpoints.
        /// </summary>
        public static string CreateBlade_Network_PrivateEndpoints_title {
            get {
                return ResourceManager.GetString("CreateBlade_Network_PrivateEndpoints_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Private Endpoint.
        /// </summary>
        public static string CreateBlade_Network_PrivateEndpoints_type {
            get {
                return ResourceManager.GetString("CreateBlade_Network_PrivateEndpoints_type", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Private endpoint.
        /// </summary>
        public static string CreateBlade_Network_privateEndpointTitle {
            get {
                return ResourceManager.GetString("CreateBlade_Network_privateEndpointTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You can connect to your registry either publicly or privately using a private endpoint..
        /// </summary>
        public static string CreateBlade_Network_registryOverviewDescription {
            get {
                return ResourceManager.GetString("CreateBlade_Network_registryOverviewDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to For more information, see.
        /// </summary>
        public static string CreateBlade_Network_subnetDelegate_learnMore {
            get {
                return ResourceManager.GetString("CreateBlade_Network_subnetDelegate_learnMore", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Follows different management boundaries than your hub and projects, injected into your Azure VNET..
        /// </summary>
        public static string CreateBlade_Network_SubnetDelegate_message {
            get {
                return ResourceManager.GetString("CreateBlade_Network_SubnetDelegate_message", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delegate Azure virtual network subnet for agents.
        /// </summary>
        public static string CreateBlade_Network_SubnetDelegate_title {
            get {
                return ResourceManager.GetString("CreateBlade_Network_SubnetDelegate_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Networking.
        /// </summary>
        public static string CreateBlade_Network_title {
            get {
                return ResourceManager.GetString("CreateBlade_Network_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Azure Firewall SKU.
        /// </summary>
        public static string CreateBlade_Network_WorkspaceApprovedOutbound_OutboundAccess_azureFirewallSku {
            get {
                return ResourceManager.GetString("CreateBlade_Network_WorkspaceApprovedOutbound_OutboundAccess_azureFirewallSku", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select from Basic or Standard SKU for the Azure Firewall deployment. For more information on Azure Firewall, see.
        /// </summary>
        public static string CreateBlade_Network_WorkspaceApprovedOutbound_OutboundAccess_azureFirewallSkuDescription {
            get {
                return ResourceManager.GetString("CreateBlade_Network_WorkspaceApprovedOutbound_OutboundAccess_azureFirewallSkuDesc" +
                        "ription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pricing.
        /// </summary>
        public static string CreateBlade_Network_WorkspaceApprovedOutbound_OutboundAccess_azureFirewallSkuPricingText {
            get {
                return ResourceManager.GetString("CreateBlade_Network_WorkspaceApprovedOutbound_OutboundAccess_azureFirewallSkuPric" +
                        "ingText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to There are several optional outbound targets recommended for your hub in scenarios such as VS Code, Prompt Flow, and more. You can modify or delete them..
        /// </summary>
        public static string CreateBlade_Network_WorkspaceApprovedOutbound_OutboundAccess_hubRecommendedTargetDescription {
            get {
                return ResourceManager.GetString("CreateBlade_Network_WorkspaceApprovedOutbound_OutboundAccess_hubRecommendedTarget" +
                        "Description", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to There are a few outbound targets added by Azure AI Foundry that are required for your workspace to access things like storage, key vault, and more..
        /// </summary>
        public static string CreateBlade_Network_WorkspaceApprovedOutbound_OutboundAccess_hubRequiredTargetDescription {
            get {
                return ResourceManager.GetString("CreateBlade_Network_WorkspaceApprovedOutbound_OutboundAccess_hubRequiredTargetDes" +
                        "cription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to There are several optional outbound targets recommended for your workspace in scenarios such as AutoML and Data Labeling. You can modify or delete them,.
        /// </summary>
        public static string CreateBlade_Network_WorkspaceApprovedOutbound_OutboundAccess_recommendedTargetDescription {
            get {
                return ResourceManager.GetString("CreateBlade_Network_WorkspaceApprovedOutbound_OutboundAccess_recommendedTargetDes" +
                        "cription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Learn more about recommended targets.
        /// </summary>
        public static string CreateBlade_Network_WorkspaceApprovedOutbound_OutboundAccess_recommendedTargetLearnMore {
            get {
                return ResourceManager.GetString("CreateBlade_Network_WorkspaceApprovedOutbound_OutboundAccess_recommendedTargetLea" +
                        "rnMore", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to There are a few outbound targets added by Azure Machine Learning that are required for your workspace to access things like storage,  notebooks, and deployment environments..
        /// </summary>
        public static string CreateBlade_Network_WorkspaceApprovedOutbound_OutboundAccess_requiredTargetDescription {
            get {
                return ResourceManager.GetString("CreateBlade_Network_WorkspaceApprovedOutbound_OutboundAccess_requiredTargetDescri" +
                        "ption", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Learn more about required targets.
        /// </summary>
        public static string CreateBlade_Network_WorkspaceApprovedOutbound_OutboundAccess_requiredTargetLearnMore {
            get {
                return ResourceManager.GetString("CreateBlade_Network_WorkspaceApprovedOutbound_OutboundAccess_requiredTargetLearnM" +
                        "ore", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add private endpoints people can use to access your workspace, and manage the outbound targets to which the workspace can access.
        /// </summary>
        public static string CreateBlade_Network_workspaceApprovedOutbound_PrivateNetworkSettings_description {
            get {
                return ResourceManager.GetString("CreateBlade_Network_workspaceApprovedOutbound_PrivateNetworkSettings_description", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Private network and data exfiltration settings.
        /// </summary>
        public static string CreateBlade_Network_workspaceApprovedOutbound_PrivateNetworkSettings_title {
            get {
                return ResourceManager.GetString("CreateBlade_Network_workspaceApprovedOutbound_PrivateNetworkSettings_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Workspace Inbound access.
        /// </summary>
        public static string CreateBlade_Network_workspaceInboundAccessTitle {
            get {
                return ResourceManager.GetString("CreateBlade_Network_workspaceInboundAccessTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Use my own virtual network.
        /// </summary>
        public static string CreateBlade_Network_WorkspaceInternetOutbound_OutboundAccess_useMyOwnVirtualNetwork {
            get {
                return ResourceManager.GetString("CreateBlade_Network_WorkspaceInternetOutbound_OutboundAccess_useMyOwnVirtualNetwo" +
                        "rk", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Use a virtual network managed by Azure Machine Learning.
        /// </summary>
        public static string CreateBlade_Network_WorkspaceInternetOutbound_OutboundAccess_virtualNetworkManagedByAML {
            get {
                return ResourceManager.GetString("CreateBlade_Network_WorkspaceInternetOutbound_OutboundAccess_virtualNetworkManage" +
                        "dByAML", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Private endpoints are required for your workspace to access private resources like storage, notebooks, and deployment environments. You can also add your additional private link targets here for your custom scenarios..
        /// </summary>
        public static string CreateBlade_Network_WorkspaceInternetOutbound_OutboundAccess_virtualNetworkManagedByAMLDescription {
            get {
                return ResourceManager.GetString("CreateBlade_Network_WorkspaceInternetOutbound_OutboundAccess_virtualNetworkManage" +
                        "dByAMLDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Learn more about required private link targets.
        /// </summary>
        public static string CreateBlade_Network_WorkspaceInternetOutbound_OutboundAccess_virtualNetworkManagedByAMLLearnMore {
            get {
                return ResourceManager.GetString("CreateBlade_Network_WorkspaceInternetOutbound_OutboundAccess_virtualNetworkManage" +
                        "dByAMLLearnMore", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add private endpoints people can use to access your workspace, and choose how to manage outbound access from your workspace to things like Storage Accounts, Key Vaults and Registries..
        /// </summary>
        public static string CreateBlade_Network_WorkspaceInternetOutbound_PrivateNetworkSettings_description {
            get {
                return ResourceManager.GetString("CreateBlade_Network_WorkspaceInternetOutbound_PrivateNetworkSettings_description", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Private network settings.
        /// </summary>
        public static string CreateBlade_Network_WorkspaceInternetOutbound_PrivateNetworkSettings_title {
            get {
                return ResourceManager.GetString("CreateBlade_Network_WorkspaceInternetOutbound_PrivateNetworkSettings_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to There are a few private endpoints required for your workspace to access private resources like storage. You can also add your additional private link targets here for your custom scenarios..
        /// </summary>
        public static string CreateBlade_Network_WorkspaceInternetOutbound_targetDescription {
            get {
                return ResourceManager.GetString("CreateBlade_Network_WorkspaceInternetOutbound_targetDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Learn more about required private link target.
        /// </summary>
        public static string CreateBlade_Network_WorkspaceInternetOutbound_targetLearnMoreLink {
            get {
                return ResourceManager.GetString("CreateBlade_Network_WorkspaceInternetOutbound_targetLearnMoreLink", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The rule has been applied and effective..
        /// </summary>
        public static string CreateBlade_Network_WorkspaceOutboundAccess_activeStatusMessage {
            get {
                return ResourceManager.GetString("CreateBlade_Network_WorkspaceOutboundAccess_activeStatusMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add user-defined outbound rules.
        /// </summary>
        public static string CreateBlade_Network_WorkspaceOutboundAccess_addUserDefinedOutboundRules {
            get {
                return ResourceManager.GetString("CreateBlade_Network_WorkspaceOutboundAccess_addUserDefinedOutboundRules", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Connection Name.
        /// </summary>
        public static string CreateBlade_Network_WorkspaceOutboundAccess_connectionName {
            get {
                return ResourceManager.GetString("CreateBlade_Network_WorkspaceOutboundAccess_connectionName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete user-defined outbound rules.
        /// </summary>
        public static string CreateBlade_Network_WorkspaceOutboundAccess_deleteUserDefinedOutboundRules {
            get {
                return ResourceManager.GetString("CreateBlade_Network_WorkspaceOutboundAccess_deleteUserDefinedOutboundRules", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Destination.
        /// </summary>
        public static string CreateBlade_Network_WorkspaceOutboundAccess_destination {
            get {
                return ResourceManager.GetString("CreateBlade_Network_WorkspaceOutboundAccess_destination", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Destination Type.
        /// </summary>
        public static string CreateBlade_Network_WorkspaceOutboundAccess_destinationType {
            get {
                return ResourceManager.GetString("CreateBlade_Network_WorkspaceOutboundAccess_destinationType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Securing your workspace with a managed network provides network isolation for outbound access from the Hub and managed computes. Once you enable managed virtual network isolation of your Azure AI, you can&apos;t disable it..
        /// </summary>
        public static string CreateBlade_Network_WorkspaceOutboundAccess_disabledManagedVnetMessage {
            get {
                return ResourceManager.GetString("CreateBlade_Network_WorkspaceOutboundAccess_disabledManagedVnetMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Learn more.
        /// </summary>
        public static string CreateBlade_Network_WorkspaceOutboundAccess_disabledManagedVnetMessageLearnMore {
            get {
                return ResourceManager.GetString("CreateBlade_Network_WorkspaceOutboundAccess_disabledManagedVnetMessageLearnMore", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The rule will become active when managed network is provisioned, otherwise please check if approval is pending for the target resource..
        /// </summary>
        public static string CreateBlade_Network_WorkspaceOutboundAccess_inactivePrivateEndpointStatusMessage {
            get {
                return ResourceManager.GetString("CreateBlade_Network_WorkspaceOutboundAccess_inactivePrivateEndpointStatusMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The rule will become active when managed network is provisioned..
        /// </summary>
        public static string CreateBlade_Network_WorkspaceOutboundAccess_inactiveStatusMessage {
            get {
                return ResourceManager.GetString("CreateBlade_Network_WorkspaceOutboundAccess_inactiveStatusMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Parent Rules.
        /// </summary>
        public static string CreateBlade_Network_WorkspaceOutboundAccess_parentRules {
            get {
                return ResourceManager.GetString("CreateBlade_Network_WorkspaceOutboundAccess_parentRules", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Provision managed virtual network.
        /// </summary>
        public static string CreateBlade_Network_WorkspaceOutboundAccess_provisionManagedVirtualNetwork {
            get {
                return ResourceManager.GetString("CreateBlade_Network_WorkspaceOutboundAccess_provisionManagedVirtualNetwork", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Managed virtual network will be provisioned at workspace creation. Charges will be incurred for network resources, such as private endpoint..
        /// </summary>
        public static string CreateBlade_Network_WorkspaceOutboundAccess_provisionManagedVirtualNetworkInfo {
            get {
                return ResourceManager.GetString("CreateBlade_Network_WorkspaceOutboundAccess_provisionManagedVirtualNetworkInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Provision managed virtual network.
        /// </summary>
        public static string CreateBlade_Network_WorkspaceOutboundAccess_provisionManagedVirtualNetworkSummaryTitle {
            get {
                return ResourceManager.GetString("CreateBlade_Network_WorkspaceOutboundAccess_provisionManagedVirtualNetworkSummary" +
                        "Title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Status.
        /// </summary>
        public static string CreateBlade_Network_WorkspaceOutboundAccess_status {
            get {
                return ResourceManager.GetString("CreateBlade_Network_WorkspaceOutboundAccess_status", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Workspace Outbound access.
        /// </summary>
        public static string CreateBlade_Network_workspaceOutboundAccessTitle {
            get {
                return ResourceManager.GetString("CreateBlade_Network_workspaceOutboundAccessTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You can also add your own outbound targets here for your custom scenarios..
        /// </summary>
        public static string CreateBlade_Network_WorkspacePrivateOutbound_addUserDefinedOutboundRuleText {
            get {
                return ResourceManager.GetString("CreateBlade_Network_WorkspacePrivateOutbound_addUserDefinedOutboundRuleText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dependency outbound rules.
        /// </summary>
        public static string CreateBlade_Network_WorkspacePrivateOutbound_dependencyOutboundRules {
            get {
                return ResourceManager.GetString("CreateBlade_Network_WorkspacePrivateOutbound_dependencyOutboundRules", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Recommended outbound rules.
        /// </summary>
        public static string CreateBlade_Network_WorkspacePrivateOutbound_recommendedOutboundRules {
            get {
                return ResourceManager.GetString("CreateBlade_Network_WorkspacePrivateOutbound_recommendedOutboundRules", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Required outbound rules.
        /// </summary>
        public static string CreateBlade_Network_WorkspacePrivateOutbound_requiredOutboundRules {
            get {
                return ResourceManager.GetString("CreateBlade_Network_WorkspacePrivateOutbound_requiredOutboundRules", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User-defined outbound rules.
        /// </summary>
        public static string CreateBlade_Network_WorkspacePrivateOutbound_userDefinedOutboundRules {
            get {
                return ResourceManager.GetString("CreateBlade_Network_WorkspacePrivateOutbound_userDefinedOutboundRules", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Workspce type.
        /// </summary>
        public static string CreateBlade_Workspace_Type {
            get {
                return ResourceManager.GetString("CreateBlade_Workspace_Type", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create.
        /// </summary>
        public static string CreateBladeCreateButtonName {
            get {
                return ResourceManager.GetString("CreateBladeCreateButtonName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Description.
        /// </summary>
        public static string CreateBladeDescriptionLabel {
            get {
                return ResourceManager.GetString("CreateBladeDescriptionLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Display name of the AI hub that will be displayed in AI Foundry.
        /// </summary>
        public static string CreateBladeFriendlyNameInfo {
            get {
                return ResourceManager.GetString("CreateBladeFriendlyNameInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Friendly name.
        /// </summary>
        public static string CreateBladeFriendlyNameLabel {
            get {
                return ResourceManager.GetString("CreateBladeFriendlyNameLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Connect AI Services, including OpenAI.
        /// </summary>
        public static string CreateBladeHubAIServices_ariaLabel {
            get {
                return ResourceManager.GetString("CreateBladeHubAIServices_ariaLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Provider of Microsoft-maintained base models. Managed in your Azure subscription as a separate resource..
        /// </summary>
        public static string CreateBladeHubAIServices_info {
            get {
                return ResourceManager.GetString("CreateBladeHubAIServices_info", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Connect AI Services incl. OpenAI.
        /// </summary>
        public static string CreateBladeHubAIServices_label {
            get {
                return ResourceManager.GetString("CreateBladeHubAIServices_label", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Skip connecting AI services.
        /// </summary>
        public static string CreateBladeHubAIServices_skipText {
            get {
                return ResourceManager.GetString("CreateBladeHubAIServices_skipText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to When a user does not specify a resource group for their project, this resource group will be used by default. The project creator gets granted an Azure RBAC owner role assignment on the project instance.
        /// </summary>
        public static string CreateBladeHubConfigDefaultResourceGroup_info {
            get {
                return ResourceManager.GetString("CreateBladeHubConfigDefaultResourceGroup_info", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Default project resource group.
        /// </summary>
        public static string CreateBladeHubConfigDefaultResourceGroup_label {
            get {
                return ResourceManager.GetString("CreateBladeHubConfigDefaultResourceGroup_label", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Same as hub resource group.
        /// </summary>
        public static string CreateBladeHubConfigDefaultResourceGroup_sameAsHubLabel {
            get {
                return ResourceManager.GetString("CreateBladeHubConfigDefaultResourceGroup_sameAsHubLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hub.
        /// </summary>
        public static string CreateBladeHubNameLabel {
            get {
                return ResourceManager.GetString("CreateBladeHubNameLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create an Azure AI hub resource.
        /// </summary>
        public static string CreateBladeHubSubtitle {
            get {
                return ResourceManager.GetString("CreateBladeHubSubtitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Azure AI hub.
        /// </summary>
        public static string CreateBladeHubTitle {
            get {
                return ResourceManager.GetString("CreateBladeHubTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Location.
        /// </summary>
        public static string CreateBladeLocationLabel {
            get {
                return ResourceManager.GetString("CreateBladeLocationLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Main.
        /// </summary>
        public static string CreateBladeMainTabTitle {
            get {
                return ResourceManager.GetString("CreateBladeMainTabTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to How to use MLflow with Azure ML.
        /// </summary>
        public static string createBladeNextStepsMLFlowAzureMLDescription {
            get {
                return ResourceManager.GetString("createBladeNextStepsMLFlowAzureMLDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Organize and track work, collaborate with others and upload data. Access your work in Azure AI Foundry or Azure Machine Learning Studio..
        /// </summary>
        public static string CreateBladeProjectSubtitle {
            get {
                return ResourceManager.GetString("CreateBladeProjectSubtitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Azure AI project.
        /// </summary>
        public static string CreateBladeProjectTitle {
            get {
                return ResourceManager.GetString("CreateBladeProjectTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to description.
        /// </summary>
        public static string CreateBladeRegistryDescriptionKey {
            get {
                return ResourceManager.GetString("CreateBladeRegistryDescriptionKey", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Description.
        /// </summary>
        public static string CreateBladeRegistryDescriptionLabel {
            get {
                return ResourceManager.GetString("CreateBladeRegistryDescriptionLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Name.
        /// </summary>
        public static string CreateBladeRegistryNameLabel {
            get {
                return ResourceManager.GetString("CreateBladeRegistryNameLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create a machine learning registry.
        /// </summary>
        public static string CreateBladeRegistrySubtitle {
            get {
                return ResourceManager.GetString("CreateBladeRegistrySubtitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Resource Group.
        /// </summary>
        public static string CreateBladeResourceGroupLabel {
            get {
                return ResourceManager.GetString("CreateBladeResourceGroupLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Review + Create.
        /// </summary>
        public static string CreateBladeReviewCreateButtonName {
            get {
                return ResourceManager.GetString("CreateBladeReviewCreateButtonName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Review.
        /// </summary>
        public static string CreateBladeReviewTabTitle {
            get {
                return ResourceManager.GetString("CreateBladeReviewTabTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Subscription Id.
        /// </summary>
        public static string CreateBladeSubscriptionIdLabel {
            get {
                return ResourceManager.GetString("CreateBladeSubscriptionIdLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Subscription.
        /// </summary>
        public static string CreateBladeSubscriptionLabel {
            get {
                return ResourceManager.GetString("CreateBladeSubscriptionLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create a machine learning workspace.
        /// </summary>
        public static string CreateBladeSubtitle {
            get {
                return ResourceManager.GetString("CreateBladeSubtitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tags.
        /// </summary>
        public static string CreateBladeTagTabTitle {
            get {
                return ResourceManager.GetString("CreateBladeTagTabTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Template Error.
        /// </summary>
        public static string CreateBladeTemplateErrorText {
            get {
                return ResourceManager.GetString("CreateBladeTemplateErrorText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Azure Machine Learning.
        /// </summary>
        public static string CreateBladeTitle {
            get {
                return ResourceManager.GetString("CreateBladeTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Workspace creation in progress.
        /// </summary>
        public static string CreateBladeWorkspaceCreationProgressNotification {
            get {
                return ResourceManager.GetString("CreateBladeWorkspaceCreationProgressNotification", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Workspace deployment Error.
        /// </summary>
        public static string CreateBladeWorkspaceDeploymentErrorText {
            get {
                return ResourceManager.GetString("CreateBladeWorkspaceDeploymentErrorText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Workspace details.
        /// </summary>
        public static string createBladeWorkspaceDetails {
            get {
                return ResourceManager.GetString("createBladeWorkspaceDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Name.
        /// </summary>
        public static string CreateBladeWorkspaceNameLabel {
            get {
                return ResourceManager.GetString("CreateBladeWorkspaceNameLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Location.
        /// </summary>
        public static string createLocationLabelDefault {
            get {
                return ResourceManager.GetString("createLocationLabelDefault", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Primary Region.
        /// </summary>
        public static string createLocationLabelPrimaryRegion {
            get {
                return ResourceManager.GetString("createLocationLabelPrimaryRegion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Region.
        /// </summary>
        public static string createLocationLabelRegion {
            get {
                return ResourceManager.GetString("createLocationLabelRegion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Resource group.
        /// </summary>
        public static string createResourceGroup {
            get {
                return ResourceManager.GetString("createResourceGroup", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string createResourceGroupCreateNewPlaceholder {
            get {
                return ResourceManager.GetString("createResourceGroupCreateNewPlaceholder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Resource group.
        /// </summary>
        public static string createResourceGroupTitle {
            get {
                return ResourceManager.GetString("createResourceGroupTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Subscription.
        /// </summary>
        public static string createSubscriptionLabel {
            get {
                return ResourceManager.GetString("createSubscriptionLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tags are name/value pairs that enable you to categorize resources and view consolidated billing by applying the same tag to multiple resources and resource groups..
        /// </summary>
        public static string createTagsTabIntro {
            get {
                return ResourceManager.GetString("createTagsTabIntro", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Learn more about tags.
        /// </summary>
        public static string createTagsTabLearnMoreText {
            get {
                return ResourceManager.GetString("createTagsTabLearnMoreText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tags.
        /// </summary>
        public static string createTagsTabTitle {
            get {
                return ResourceManager.GetString("createTagsTabTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Note that if you create tags and then change resource settings on other tabs, your tags will be automatically updated..
        /// </summary>
        public static string createTagsTabUpdateNotice {
            get {
                return ResourceManager.GetString("createTagsTabUpdateNotice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Validation failed. Click here to view details..
        /// </summary>
        public static string createTemplateValidationError {
            get {
                return ResourceManager.GetString("createTemplateValidationError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Running final validation....
        /// </summary>
        public static string createTemplateValidationInProgress {
            get {
                return ResourceManager.GetString("createTemplateValidationInProgress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Validation passed.
        /// </summary>
        public static string createTemplateValidationSuccess {
            get {
                return ResourceManager.GetString("createTemplateValidationSuccess", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Configure quota.
        /// </summary>
        public static string CrossRegionComputeQuotas_configureQuotaButtonText {
            get {
                return ResourceManager.GetString("CrossRegionComputeQuotas_configureQuotaButtonText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dedicated cores usage.
        /// </summary>
        public static string CrossRegionComputeQuotas_dedicatedCoresSectionLabel {
            get {
                return ResourceManager.GetString("CrossRegionComputeQuotas_dedicatedCoresSectionLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Loading....
        /// </summary>
        public static string CrossRegionComputeQuotas_loadingText {
            get {
                return ResourceManager.GetString("CrossRegionComputeQuotas_loadingText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Low priority cores usage.
        /// </summary>
        public static string CrossRegionComputeQuotas_lowPriorityCoresSectionLabel {
            get {
                return ResourceManager.GetString("CrossRegionComputeQuotas_lowPriorityCoresSectionLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please note that the number of low-priority cores per subscription is single value accross VM families..
        /// </summary>
        public static string CrossRegionComputeQuotas_lowPriorityUsageInfoText {
            get {
                return ResourceManager.GetString("CrossRegionComputeQuotas_lowPriorityUsageInfoText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter at least 3 characters to search for workspaces....
        /// </summary>
        public static string CrossRegionComputeQuotas_QuotaConfigurationBlade_filterText {
            get {
                return ResourceManager.GetString("CrossRegionComputeQuotas_QuotaConfigurationBlade_filterText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No workspaces found to display..
        /// </summary>
        public static string CrossRegionComputeQuotas_QuotaConfigurationBlade_noItemsText {
            get {
                return ResourceManager.GetString("CrossRegionComputeQuotas_QuotaConfigurationBlade_noItemsText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quotas cannot be configured due to following error: {0}.
        /// </summary>
        public static string CrossRegionComputeQuotas_QuotaConfigurationBlade_quotaConfigNotAvailable {
            get {
                return ResourceManager.GetString("CrossRegionComputeQuotas_QuotaConfigurationBlade_quotaConfigNotAvailable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Configure your quota across your subscription here.
        /// </summary>
        public static string CrossRegionComputeQuotas_QuotaConfigurationBlade_subTitle {
            get {
                return ResourceManager.GetString("CrossRegionComputeQuotas_QuotaConfigurationBlade_subTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Configure quota.
        /// </summary>
        public static string CrossRegionComputeQuotas_QuotaConfigurationBlade_title {
            get {
                return ResourceManager.GetString("CrossRegionComputeQuotas_QuotaConfigurationBlade_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You can only configure quota for workspaces which have been configured before since the list of workspaces of this subscription cannot be reached due to following error: {0}.
        /// </summary>
        public static string CrossRegionComputeQuotas_QuotaConfigurationBlade_workspacesNotAvailable {
            get {
                return ResourceManager.GetString("CrossRegionComputeQuotas_QuotaConfigurationBlade_workspacesNotAvailable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quota update failed.
        /// </summary>
        public static string CrossRegionComputeQuotas_quotaUpdateFailed {
            get {
                return ResourceManager.GetString("CrossRegionComputeQuotas_quotaUpdateFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quota successfully updated.
        /// </summary>
        public static string CrossRegionComputeQuotas_quotaUpdateSucceeded {
            get {
                return ResourceManager.GetString("CrossRegionComputeQuotas_quotaUpdateSucceeded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cross location workspace information (whether the workspace owning a compute is in a different location than the selected location) cannot be displayed due to following error but you can still see all the usages: {0}.
        /// </summary>
        public static string CrossRegionComputeQuotas_QuotaUsageBlade_crossLocationUsagesNotAvailable {
            get {
                return ResourceManager.GetString("CrossRegionComputeQuotas_QuotaUsageBlade_crossLocationUsagesNotAvailable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Learn more.
        /// </summary>
        public static string CrossRegionComputeQuotas_QuotaUsageBlade_quotaInfoLearnMore {
            get {
                return ResourceManager.GetString("CrossRegionComputeQuotas_QuotaUsageBlade_quotaInfoLearnMore", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Azure Machine Learning quota is preconfigured limits, which specifies the maximum number of cores you can use at any given moment..
        /// </summary>
        public static string CrossRegionComputeQuotas_QuotaUsageBlade_quotaInfoText {
            get {
                return ResourceManager.GetString("CrossRegionComputeQuotas_QuotaUsageBlade_quotaInfoText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Compute usages cannot be displayed do to following error: {0}.
        /// </summary>
        public static string CrossRegionComputeQuotas_QuotaUsageBlade_usagesNotAvailable {
            get {
                return ResourceManager.GetString("CrossRegionComputeQuotas_QuotaUsageBlade_usagesNotAvailable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} Series.
        /// </summary>
        public static string CrossRegionComputeQuotas_requestBladeFamilyNameFormat {
            get {
                return ResourceManager.GetString("CrossRegionComputeQuotas_requestBladeFamilyNameFormat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Request quota increase.
        /// </summary>
        public static string CrossRegionComputeQuotas_requestQuotaButtonText {
            get {
                return ResourceManager.GetString("CrossRegionComputeQuotas_requestQuotaButtonText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Subscription view.
        /// </summary>
        public static string CrossRegionComputeQuotas_subscriptionViewText {
            get {
                return ResourceManager.GetString("CrossRegionComputeQuotas_subscriptionViewText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Show workspaces across all locations (cross-location compute).
        /// </summary>
        public static string CrossRegionComputeQuotas_toggleText {
            get {
                return ResourceManager.GetString("CrossRegionComputeQuotas_toggleText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cluster quota:.
        /// </summary>
        public static string CrossRegionComputeQuotas_totalClustersLabel {
            get {
                return ResourceManager.GetString("CrossRegionComputeQuotas_totalClustersLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} clusters and CIs used | {1} remaining.
        /// </summary>
        public static string CrossRegionComputeQuotas_totalClustersTextFormat {
            get {
                return ResourceManager.GetString("CrossRegionComputeQuotas_totalClustersTextFormat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} cores used | {1} cores remaining.
        /// </summary>
        public static string CrossRegionComputeQuotas_totalCoresTextFormat {
            get {
                return ResourceManager.GetString("CrossRegionComputeQuotas_totalCoresTextFormat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dedicated quota:.
        /// </summary>
        public static string CrossRegionComputeQuotas_totalDedicatedCoresLabel {
            get {
                return ResourceManager.GetString("CrossRegionComputeQuotas_totalDedicatedCoresLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Low priority quota:.
        /// </summary>
        public static string CrossRegionComputeQuotas_totalLowPriorityCoresLabel {
            get {
                return ResourceManager.GetString("CrossRegionComputeQuotas_totalLowPriorityCoresLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} of {1} cores utilized.
        /// </summary>
        public static string CrossRegionComputeQuotas_UsageGrid_coresUsageTextFormat {
            get {
                return ResourceManager.GetString("CrossRegionComputeQuotas_UsageGrid_coresUsageTextFormat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cores utilization.
        /// </summary>
        public static string CrossRegionComputeQuotas_UsageGrid_coreUtilizationColumnText {
            get {
                return ResourceManager.GetString("CrossRegionComputeQuotas_UsageGrid_coreUtilizationColumnText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enable cross location compute toggle.
        /// </summary>
        public static string CrossRegionComputeQuotas_UsageGrid_crossLocationEnableText {
            get {
                return ResourceManager.GetString("CrossRegionComputeQuotas_UsageGrid_crossLocationEnableText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} cores.
        /// </summary>
        public static string CrossRegionComputeQuotas_UsageGrid_crossLocationWarningCoresFormat {
            get {
                return ResourceManager.GetString("CrossRegionComputeQuotas_UsageGrid_crossLocationWarningCoresFormat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You are using {0} in other locations..
        /// </summary>
        public static string CrossRegionComputeQuotas_UsageGrid_crossLocationWarningLine1 {
            get {
                return ResourceManager.GetString("CrossRegionComputeQuotas_UsageGrid_crossLocationWarningLine1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {1} to view them..
        /// </summary>
        public static string CrossRegionComputeQuotas_UsageGrid_crossLocationWarningLine2 {
            get {
                return ResourceManager.GetString("CrossRegionComputeQuotas_UsageGrid_crossLocationWarningLine2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Standard {0} Family vCPUs.
        /// </summary>
        public static string CrossRegionComputeQuotas_UsageGrid_familyNameFormat {
            get {
                return ResourceManager.GetString("CrossRegionComputeQuotas_UsageGrid_familyNameFormat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} out of {1} ({2}).
        /// </summary>
        public static string CrossRegionComputeQuotas_UsageGrid_totalCoresTextFormat {
            get {
                return ResourceManager.GetString("CrossRegionComputeQuotas_UsageGrid_totalCoresTextFormat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Usage.
        /// </summary>
        public static string CrossRegionComputeQuotas_UsageGrid_usageColumnText {
            get {
                return ResourceManager.GetString("CrossRegionComputeQuotas_UsageGrid_usageColumnText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Usage percentage.
        /// </summary>
        public static string CrossRegionComputeQuotas_UsageGrid_usagePercentageColumnText {
            get {
                return ResourceManager.GetString("CrossRegionComputeQuotas_UsageGrid_usagePercentageColumnText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to VM Families.
        /// </summary>
        public static string CrossRegionComputeQuotas_UsageGrid_vmFamiliesColumnText {
            get {
                return ResourceManager.GetString("CrossRegionComputeQuotas_UsageGrid_vmFamiliesColumnText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} ({1}, {2}).
        /// </summary>
        public static string CrossRegionComputeQuotas_UsageGrid_workspaceCrossLocationDisplayText {
            get {
                return ResourceManager.GetString("CrossRegionComputeQuotas_UsageGrid_workspaceCrossLocationDisplayText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} ({1}).
        /// </summary>
        public static string CrossRegionComputeQuotas_UsageGrid_workspaceDisplayText {
            get {
                return ResourceManager.GetString("CrossRegionComputeQuotas_UsageGrid_workspaceDisplayText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Workspaces.
        /// </summary>
        public static string CrossRegionComputeQuotas_UsageGrid_workspacesColumnText {
            get {
                return ResourceManager.GetString("CrossRegionComputeQuotas_UsageGrid_workspacesColumnText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Workspace view.
        /// </summary>
        public static string CrossRegionComputeQuotas_workspaceViewText {
            get {
                return ResourceManager.GetString("CrossRegionComputeQuotas_workspaceViewText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to How valuable was this experience for managing workspace outbound network settings?.
        /// </summary>
        public static string CVA_ManagedWorkspaceOutbound_question {
            get {
                return ResourceManager.GetString("CVA_ManagedWorkspaceOutbound_question", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to How valuable was this experience for managing private endpoints connections?.
        /// </summary>
        public static string CVA_PrivateEndpoints_question {
            get {
                return ResourceManager.GetString("CVA_PrivateEndpoints_question", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to How valuable was this experience for managing public access network settings?.
        /// </summary>
        public static string CVA_PublicAccess_question {
            get {
                return ResourceManager.GetString("CVA_PublicAccess_question", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Name.
        /// </summary>
        public static string DeleteHubBlade_AssociatedResourcesListColumn_name {
            get {
                return ResourceManager.GetString("DeleteHubBlade_AssociatedResourcesListColumn_name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Resource Group.
        /// </summary>
        public static string DeleteHubBlade_AssociatedResourcesListColumn_resourceGroup {
            get {
                return ResourceManager.GetString("DeleteHubBlade_AssociatedResourcesListColumn_resourceGroup", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Type.
        /// </summary>
        public static string DeleteHubBlade_AssociatedResourcesListColumn_type {
            get {
                return ResourceManager.GetString("DeleteHubBlade_AssociatedResourcesListColumn_type", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cancel.
        /// </summary>
        public static string DeleteHubBlade_Buttons_cancel {
            get {
                return ResourceManager.GetString("DeleteHubBlade_Buttons_cancel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete.
        /// </summary>
        public static string DeleteHubBlade_Buttons_delete {
            get {
                return ResourceManager.GetString("DeleteHubBlade_Buttons_delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Confirm delete.
        /// </summary>
        public static string DeleteHubBlade_ConfirmDeleteSection_label {
            get {
                return ResourceManager.GetString("DeleteHubBlade_ConfirmDeleteSection_label", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Type the resource name.
        /// </summary>
        public static string DeleteHubBlade_ConfirmDeleteSection_placeholder {
            get {
                return ResourceManager.GetString("DeleteHubBlade_ConfirmDeleteSection_placeholder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The value must not be empty.
        /// </summary>
        public static string DeleteHubBlade_ConfirmDeleteSection_ValidationErrorMessage_emptyString {
            get {
                return ResourceManager.GetString("DeleteHubBlade_ConfirmDeleteSection_ValidationErrorMessage_emptyString", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Name does not match.
        /// </summary>
        public static string DeleteHubBlade_ConfirmDeleteSection_ValidationErrorMessage_nameMismatch {
            get {
                return ResourceManager.GetString("DeleteHubBlade_ConfirmDeleteSection_ValidationErrorMessage_nameMismatch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to deployed model(s).
        /// </summary>
        public static string DeleteHubBlade_Description_deployment {
            get {
                return ResourceManager.GetString("DeleteHubBlade_Description_deployment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hub to be deleted: .
        /// </summary>
        public static string DeleteHubBlade_Description_hubName {
            get {
                return ResourceManager.GetString("DeleteHubBlade_Description_hubName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to project(s).
        /// </summary>
        public static string DeleteHubBlade_Description_project {
            get {
                return ResourceManager.GetString("DeleteHubBlade_Description_project", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your hub with {0} and {1} will be permanently deleted..
        /// </summary>
        public static string DeleteHubBlade_Description_section1 {
            get {
                return ResourceManager.GetString("DeleteHubBlade_Description_section1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Connected resources may still store hub data and are not automatically deleted. If you delete these resources you may break other existing Azure deployments..
        /// </summary>
        public static string DeleteHubBlade_Description_section2 {
            get {
                return ResourceManager.GetString("DeleteHubBlade_Description_section2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Choose connected resources you&apos;d like to additionally delete:.
        /// </summary>
        public static string DeleteHubBlade_Description_section3 {
            get {
                return ResourceManager.GetString("DeleteHubBlade_Description_section3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deployment.
        /// </summary>
        public static string DeleteHubBlade_ProjectListColumn_deployment {
            get {
                return ResourceManager.GetString("DeleteHubBlade_ProjectListColumn_deployment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Project.
        /// </summary>
        public static string DeleteHubBlade_ProjectListColumn_project {
            get {
                return ResourceManager.GetString("DeleteHubBlade_ProjectListColumn_project", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You are missing Azure RBAC delete permission on one or more connected resources. Delete these resources later from Azure portal..
        /// </summary>
        public static string DeleteHubBlade_RBAC_message {
            get {
                return ResourceManager.GetString("DeleteHubBlade_RBAC_message", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AI Services.
        /// </summary>
        public static string DeleteHubBlade_ResourceType_aiServices {
            get {
                return ResourceManager.GetString("DeleteHubBlade_ResourceType_aiServices", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Application Insights.
        /// </summary>
        public static string DeleteHubBlade_ResourceType_applicationInsights {
            get {
                return ResourceManager.GetString("DeleteHubBlade_ResourceType_applicationInsights", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cognitive Service.
        /// </summary>
        public static string DeleteHubBlade_ResourceType_cognitiveService {
            get {
                return ResourceManager.GetString("DeleteHubBlade_ResourceType_cognitiveService", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Computer Vision.
        /// </summary>
        public static string DeleteHubBlade_ResourceType_computerVision {
            get {
                return ResourceManager.GetString("DeleteHubBlade_ResourceType_computerVision", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Container Registry.
        /// </summary>
        public static string DeleteHubBlade_ResourceType_containerRegistry {
            get {
                return ResourceManager.GetString("DeleteHubBlade_ResourceType_containerRegistry", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Content Safety.
        /// </summary>
        public static string DeleteHubBlade_ResourceType_contentSafety {
            get {
                return ResourceManager.GetString("DeleteHubBlade_ResourceType_contentSafety", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Document Intelligence.
        /// </summary>
        public static string DeleteHubBlade_ResourceType_formRecognizer {
            get {
                return ResourceManager.GetString("DeleteHubBlade_ResourceType_formRecognizer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Key Vault.
        /// </summary>
        public static string DeleteHubBlade_ResourceType_keyVault {
            get {
                return ResourceManager.GetString("DeleteHubBlade_ResourceType_keyVault", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open AI.
        /// </summary>
        public static string DeleteHubBlade_ResourceType_openAI {
            get {
                return ResourceManager.GetString("DeleteHubBlade_ResourceType_openAI", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Search Service.
        /// </summary>
        public static string DeleteHubBlade_ResourceType_searchService {
            get {
                return ResourceManager.GetString("DeleteHubBlade_ResourceType_searchService", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Speech Services.
        /// </summary>
        public static string DeleteHubBlade_ResourceType_speechServices {
            get {
                return ResourceManager.GetString("DeleteHubBlade_ResourceType_speechServices", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Storage Account.
        /// </summary>
        public static string DeleteHubBlade_ResourceType_storageAccount {
            get {
                return ResourceManager.GetString("DeleteHubBlade_ResourceType_storageAccount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Language.
        /// </summary>
        public static string DeleteHubBlade_ResourceType_textAnalytics {
            get {
                return ResourceManager.GetString("DeleteHubBlade_ResourceType_textAnalytics", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Translator.
        /// </summary>
        public static string DeleteHubBlade_ResourceType_textTranslation {
            get {
                return ResourceManager.GetString("DeleteHubBlade_ResourceType_textTranslation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete resource.
        /// </summary>
        public static string DeleteHubBlade_Title {
            get {
                return ResourceManager.GetString("DeleteHubBlade_Title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Resource deletion canceled.
        /// </summary>
        public static string DeleteHubNotification_Cancel_title {
            get {
                return ResourceManager.GetString("DeleteHubNotification_Cancel_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to An error occurred while deleting hub &apos;{workspaceName}&apos; and its associated resources..
        /// </summary>
        public static string DeleteHubNotification_Error_description {
            get {
                return ResourceManager.GetString("DeleteHubNotification_Error_description", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Resource deletion error.
        /// </summary>
        public static string DeleteHubNotification_Error_title {
            get {
                return ResourceManager.GetString("DeleteHubNotification_Error_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hub &apos;{workspaceName}&apos; and its associated resources deletion in progress....
        /// </summary>
        public static string DeleteHubNotification_InProgress_description {
            get {
                return ResourceManager.GetString("DeleteHubNotification_InProgress_description", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deleting resource....
        /// </summary>
        public static string DeleteHubNotification_InProgress_title {
            get {
                return ResourceManager.GetString("DeleteHubNotification_InProgress_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hub &apos;{workspaceName}&apos; and its associated resources were deleted successfully..
        /// </summary>
        public static string DeleteHubNotification_Success_description {
            get {
                return ResourceManager.GetString("DeleteHubNotification_Success_description", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Successfully deleted.
        /// </summary>
        public static string DeleteHubNotification_Success_title {
            get {
                return ResourceManager.GetString("DeleteHubNotification_Success_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Resource.
        /// </summary>
        public static string DeleteHubStatusBlade_ResourceStatusListColumn_resource {
            get {
                return ResourceManager.GetString("DeleteHubStatusBlade_ResourceStatusListColumn_resource", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Status.
        /// </summary>
        public static string DeleteHubStatusBlade_ResourceStatusListColumn_status {
            get {
                return ResourceManager.GetString("DeleteHubStatusBlade_ResourceStatusListColumn_status", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete resource status.
        /// </summary>
        public static string DeleteHubStatusBlade_title {
            get {
                return ResourceManager.GetString("DeleteHubStatusBlade_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deployment Capacity.
        /// </summary>
        public static string deploymentCapacityMetric {
            get {
                return ResourceManager.GetString("deploymentCapacityMetric", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cpu Utilization.
        /// </summary>
        public static string deploymentCpuUtilizationMetric {
            get {
                return ResourceManager.GetString("deploymentCpuUtilizationMetric", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Disk Utilization.
        /// </summary>
        public static string deploymentDiskUtilizationMetric {
            get {
                return ResourceManager.GetString("deploymentDiskUtilizationMetric", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Environment.
        /// </summary>
        public static string deploymentEnvironmentId {
            get {
                return ResourceManager.GetString("deploymentEnvironmentId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deployment Type.
        /// </summary>
        public static string deploymentKind {
            get {
                return ResourceManager.GetString("deploymentKind", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Memory Utilization.
        /// </summary>
        public static string deploymentMemoryUtilizationMetric {
            get {
                return ResourceManager.GetString("deploymentMemoryUtilizationMetric", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Model Id.
        /// </summary>
        public static string deploymentModelIdLabel {
            get {
                return ResourceManager.GetString("deploymentModelIdLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deployment Name.
        /// </summary>
        public static string deploymentName {
            get {
                return ResourceManager.GetString("deploymentName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Provisioning State.
        /// </summary>
        public static string deploymentProvisioningStateLabel {
            get {
                return ResourceManager.GetString("deploymentProvisioningStateLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Centrally configure Azure AI hubs.
        /// </summary>
        public static string Description_createAzureMachineLearningHubResource {
            get {
                return ResourceManager.GetString("Description_createAzureMachineLearningHubResource", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to For sharing ML assets across workspaces.
        /// </summary>
        public static string Description_CreateAzureMachineLearningRegistryResource {
            get {
                return ResourceManager.GetString("Description_CreateAzureMachineLearningRegistryResource", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to For ML projects and teams.
        /// </summary>
        public static string Description_CreateAzureMachineLearningResource {
            get {
                return ResourceManager.GetString("Description_CreateAzureMachineLearningResource", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Resource details.
        /// </summary>
        public static string detailsLabel {
            get {
                return ResourceManager.GetString("detailsLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Download a template for automation.
        /// </summary>
        public static string downloadTemplateLinkText {
            get {
                return ResourceManager.GetString("downloadTemplateLinkText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Discard.
        /// </summary>
        public static string EncryptionSettings_Command_Discard {
            get {
                return ResourceManager.GetString("EncryptionSettings_Command_Discard", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Close.
        /// </summary>
        public static string EncryptionSettings_Command_DismissAriaLabel {
            get {
                return ResourceManager.GetString("EncryptionSettings_Command_DismissAriaLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error updating key.
        /// </summary>
        public static string EncryptionSettings_Command_ResultError {
            get {
                return ResourceManager.GetString("EncryptionSettings_Command_ResultError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Updating....
        /// </summary>
        public static string EncryptionSettings_Command_ResultLoading {
            get {
                return ResourceManager.GetString("EncryptionSettings_Command_ResultLoading", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Success.
        /// </summary>
        public static string EncryptionSettings_Command_ResultSuccess {
            get {
                return ResourceManager.GetString("EncryptionSettings_Command_ResultSuccess", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save.
        /// </summary>
        public static string EncryptionSettings_Command_Save {
            get {
                return ResourceManager.GetString("EncryptionSettings_Command_Save", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to See more.
        /// </summary>
        public static string EncryptionSettings_Command_SeeMore {
            get {
                return ResourceManager.GetString("EncryptionSettings_Command_SeeMore", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You have unsaved changes..
        /// </summary>
        public static string EncryptionSettings_DirtyFormWarning {
            get {
                return ResourceManager.GetString("EncryptionSettings_DirtyFormWarning", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to data encryption.
        /// </summary>
        public static string EncryptionSettings_Header1_Replacement1 {
            get {
                return ResourceManager.GetString("EncryptionSettings_Header1_Replacement1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to customer-managed key encryption.
        /// </summary>
        public static string EncryptionSettings_Header1_Replacement2 {
            get {
                return ResourceManager.GetString("EncryptionSettings_Header1_Replacement2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your data is encrypted by default using Microsoft-managed keys. For additional control over your data, you may choose to bring your own key for encryption. To learn more, see {} and {}..
        /// </summary>
        public static string EncryptionSettings_Header1_Template {
            get {
                return ResourceManager.GetString("EncryptionSettings_Header1_Template", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to After a workspace is deployed, you can rotate the encryption key but not change the encryption type from Microsoft-managed key to Customer-managed key..
        /// </summary>
        public static string EncryptionSettings_Header2 {
            get {
                return ResourceManager.GetString("EncryptionSettings_Header2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Key.
        /// </summary>
        public static string EncryptionSettings_SelectedKey_KeyLabel {
            get {
                return ResourceManager.GetString("EncryptionSettings_SelectedKey_KeyLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Selected key vault and key.
        /// </summary>
        public static string EncryptionSettings_SelectedKey_Title {
            get {
                return ResourceManager.GetString("EncryptionSettings_SelectedKey_Title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to KeyVault.
        /// </summary>
        public static string EncryptionSettings_SelectedKey_VaultLabel {
            get {
                return ResourceManager.GetString("EncryptionSettings_SelectedKey_VaultLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Version.
        /// </summary>
        public static string EncryptionSettings_SelectedKey_VersionLabel {
            get {
                return ResourceManager.GetString("EncryptionSettings_SelectedKey_VersionLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Encryption selection.
        /// </summary>
        public static string EncryptionSettings_SelectionSection_Title {
            get {
                return ResourceManager.GetString("EncryptionSettings_SelectionSection_Title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Encryption type.
        /// </summary>
        public static string EncryptionSettings_SelectionSection_TypeLabel {
            get {
                return ResourceManager.GetString("EncryptionSettings_SelectionSection_TypeLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Key vault and key.
        /// </summary>
        public static string EncryptionSettings_SelectionSection_VaultAndKeyLabel {
            get {
                return ResourceManager.GetString("EncryptionSettings_SelectionSection_VaultAndKeyLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select a key vault and key.
        /// </summary>
        public static string EncryptionSettings_SelectionSection_VaultAndKeySelect {
            get {
                return ResourceManager.GetString("EncryptionSettings_SelectionSection_VaultAndKeySelect", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Encryption.
        /// </summary>
        public static string EncryptionSettings_Title {
            get {
                return ResourceManager.GetString("EncryptionSettings_Title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Encryption.
        /// </summary>
        public static string EncryptionTab_Title {
            get {
                return ResourceManager.GetString("EncryptionTab_Title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Auth Mode.
        /// </summary>
        public static string endpointAuthModeLabel {
            get {
                return ResourceManager.GetString("endpointAuthModeLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Endpoint Id.
        /// </summary>
        public static string endpointId {
            get {
                return ResourceManager.GetString("endpointId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Provisioning State.
        /// </summary>
        public static string endpointProvisioningStateLabel {
            get {
                return ResourceManager.GetString("endpointProvisioningStateLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Request Latency.
        /// </summary>
        public static string endpointRequestLatencyMetric {
            get {
                return ResourceManager.GetString("endpointRequestLatencyMetric", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Request Per Minute.
        /// </summary>
        public static string endpointRequestsPerMinuteMetric {
            get {
                return ResourceManager.GetString("endpointRequestsPerMinuteMetric", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Scoring Uri.
        /// </summary>
        public static string endpointScoringUriLabel {
            get {
                return ResourceManager.GetString("endpointScoringUriLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Swagger Uri.
        /// </summary>
        public static string endpointSwaggerLabel {
            get {
                return ResourceManager.GetString("endpointSwaggerLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to An unexpected parsing error occurred..
        /// </summary>
        public static string ErrorJsonParsingException {
            get {
                return ResourceManager.GetString("ErrorJsonParsingException", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 
        ///      Associated projects of Azure AI hub with ID &quot;{0}&quot; could not be loaded.
        ///    .
        /// </summary>
        public static string Hub_Projects_LoadFailure {
            get {
                return ResourceManager.GetString("Hub_Projects_LoadFailure", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 
        ///      Projects
        ///    .
        /// </summary>
        public static string Hub_Projects_Title {
            get {
                return ResourceManager.GetString("Hub_Projects_Title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 
        ///      Add
        ///    .
        /// </summary>
        public static string Hub_Projects_Toolbar_Add {
            get {
                return ResourceManager.GetString("Hub_Projects_Toolbar_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 
        ///      Refresh
        ///    .
        /// </summary>
        public static string Hub_Projects_Toolbar_Refresh {
            get {
                return ResourceManager.GetString("Hub_Projects_Toolbar_Refresh", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Default project resource group.
        /// </summary>
        public static string HubConfigDefaultResourceGroup_SummaryTab_label {
            get {
                return ResourceManager.GetString("HubConfigDefaultResourceGroup_SummaryTab_label", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Identity.
        /// </summary>
        public static string IdentityTab_Title {
            get {
                return ResourceManager.GetString("IdentityTab_Title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Instance details.
        /// </summary>
        public static string instanceLabel {
            get {
                return ResourceManager.GetString("instanceLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create new key vault.
        /// </summary>
        public static string Keyvault_Dropdown_createNewAriaLabel {
            get {
                return ResourceManager.GetString("Keyvault_Dropdown_createNewAriaLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to A key vault is used to store secrets and other sensitive information that is needed by the AI hub. You may create a new Azure Key Vault resource or select an existing one in your subscription..
        /// </summary>
        public static string Keyvault_Dropdown_Hub_info {
            get {
                return ResourceManager.GetString("Keyvault_Dropdown_Hub_info", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to A key vault is used to store secrets and other sensitive information that is needed by the workspace. You may create a new Azure Key Vault resource or select an existing one in your subscription..
        /// </summary>
        public static string Keyvault_Dropdown_info {
            get {
                return ResourceManager.GetString("Keyvault_Dropdown_info", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Key vault.
        /// </summary>
        public static string Keyvault_Dropdown_label {
            get {
                return ResourceManager.GetString("Keyvault_Dropdown_label", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The name must meet the following requirements:.
        /// </summary>
        public static string Keyvault_Dropdown_SettingsBlade_Name_infoDescription {
            get {
                return ResourceManager.GetString("Keyvault_Dropdown_SettingsBlade_Name_infoDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unique across all existing key vaults in Azure.
        /// </summary>
        public static string Keyvault_Dropdown_SettingsBlade_Name_infoItem1 {
            get {
                return ResourceManager.GetString("Keyvault_Dropdown_SettingsBlade_Name_infoItem1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Between 3 and 24 characters long.
        /// </summary>
        public static string Keyvault_Dropdown_SettingsBlade_Name_infoItem2 {
            get {
                return ResourceManager.GetString("Keyvault_Dropdown_SettingsBlade_Name_infoItem2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Only contain alphanumeric characters and hyphens.
        /// </summary>
        public static string Keyvault_Dropdown_SettingsBlade_Name_infoItem3 {
            get {
                return ResourceManager.GetString("Keyvault_Dropdown_SettingsBlade_Name_infoItem3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cannot start with a number.
        /// </summary>
        public static string Keyvault_Dropdown_SettingsBlade_Name_infoItem4 {
            get {
                return ResourceManager.GetString("Keyvault_Dropdown_SettingsBlade_Name_infoItem4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vault name must be between 3-24 alphanumeric characters. The name must begin with a letter, end with a letter or digit, and not contain consecutive hyphens..
        /// </summary>
        public static string Keyvault_Dropdown_SettingsBlade_nameInvalidMessage {
            get {
                return ResourceManager.GetString("Keyvault_Dropdown_SettingsBlade_nameInvalidMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Name.
        /// </summary>
        public static string Keyvault_Dropdown_SettingsBlade_nameLabel {
            get {
                return ResourceManager.GetString("Keyvault_Dropdown_SettingsBlade_nameLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create new key vault.
        /// </summary>
        public static string Keyvault_Dropdown_SettingsBlade_title {
            get {
                return ResourceManager.GetString("Keyvault_Dropdown_SettingsBlade_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Azure key vault.
        /// </summary>
        public static string Keyvault_RadioButton_byoKeyVault {
            get {
                return ResourceManager.GetString("Keyvault_RadioButton_byoKeyVault", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Credential store.
        /// </summary>
        public static string Keyvault_RadioButton_label {
            get {
                return ResourceManager.GetString("Keyvault_RadioButton_label", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Microsoft-managed (preview).
        /// </summary>
        public static string Keyvault_RadioButton_managedKeyVault {
            get {
                return ResourceManager.GetString("Keyvault_RadioButton_managedKeyVault", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Preview: secrets are stored in Microsoft-managed credential store. Secret data lifecycle follows your hub, projects, connections and compute..
        /// </summary>
        public static string Keyvault_RadioButton_ManagedKeyVault_infoIcon {
            get {
                return ResourceManager.GetString("Keyvault_RadioButton_ManagedKeyVault_infoIcon", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Key vault.
        /// </summary>
        public static string Keyvault_SummaryTab_label {
            get {
                return ResourceManager.GetString("Keyvault_SummaryTab_label", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Alert.
        /// </summary>
        public static string Keyword_alert {
            get {
                return ResourceManager.GetString("Keyword_alert", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Audit.
        /// </summary>
        public static string Keyword_audit {
            get {
                return ResourceManager.GetString("Keyword_audit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Instance.
        /// </summary>
        public static string Keyword_instance {
            get {
                return ResourceManager.GetString("Keyword_instance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Log.
        /// </summary>
        public static string Keyword_log {
            get {
                return ResourceManager.GetString("Keyword_log", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rules.
        /// </summary>
        public static string Keyword_rules {
            get {
                return ResourceManager.GetString("Keyword_rules", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Scale.
        /// </summary>
        public static string Keyword_scale {
            get {
                return ResourceManager.GetString("Keyword_scale", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Scaling.
        /// </summary>
        public static string Keyword_scaling {
            get {
                return ResourceManager.GetString("Keyword_scaling", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Automated machine learning.
        /// </summary>
        public static string LabelAutoMLMenuItem {
            get {
                return ResourceManager.GetString("LabelAutoMLMenuItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Refresh.
        /// </summary>
        public static string labelCommandButtonRefresh {
            get {
                return ResourceManager.GetString("labelCommandButtonRefresh", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Compute.
        /// </summary>
        public static string LabelComputeMenuItem {
            get {
                return ResourceManager.GetString("LabelComputeMenuItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Data Labeling.
        /// </summary>
        public static string labelDataLabeling {
            get {
                return ResourceManager.GetString("labelDataLabeling", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Data (Preview).
        /// </summary>
        public static string LabelDataMenuItem {
            get {
                return ResourceManager.GetString("LabelDataMenuItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deployments.
        /// </summary>
        public static string LabelDeployments {
            get {
                return ResourceManager.GetString("LabelDeployments", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deployments.
        /// </summary>
        public static string LabelDeploymentsMenuItem {
            get {
                return ResourceManager.GetString("LabelDeploymentsMenuItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Images.
        /// </summary>
        public static string LabelImagesMenuItem {
            get {
                return ResourceManager.GetString("LabelImagesMenuItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Application Insights.
        /// </summary>
        public static string LabelInsights {
            get {
                return ResourceManager.GetString("LabelInsights", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Key Vault.
        /// </summary>
        public static string LabelKeyVault {
            get {
                return ResourceManager.GetString("LabelKeyVault", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MLflow tracking URI.
        /// </summary>
        public static string LabelMlFlowUri {
            get {
                return ResourceManager.GetString("LabelMlFlowUri", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Visual interface.
        /// </summary>
        public static string labelMLStudioLauncher {
            get {
                return ResourceManager.GetString("labelMLStudioLauncher", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Models.
        /// </summary>
        public static string LabelModelsMenuItem {
            get {
                return ResourceManager.GetString("LabelModelsMenuItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pipelines.
        /// </summary>
        public static string LabelPipelinesMenuItem {
            get {
                return ResourceManager.GetString("LabelPipelinesMenuItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Azure AI hub.
        /// </summary>
        public static string LabelProjectHubResource {
            get {
                return ResourceManager.GetString("LabelProjectHubResource", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Experiments.
        /// </summary>
        public static string LabelProjectsMenuItem {
            get {
                return ResourceManager.GetString("LabelProjectsMenuItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Properties.
        /// </summary>
        public static string LabelProperties {
            get {
                return ResourceManager.GetString("LabelProperties", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Usage + quotas.
        /// </summary>
        public static string labelQuotaUsage {
            get {
                return ResourceManager.GetString("labelQuotaUsage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Container Registry.
        /// </summary>
        public static string LabelRegistry {
            get {
                return ResourceManager.GetString("LabelRegistry", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Request Quota.
        /// </summary>
        public static string labelRequestQuota {
            get {
                return ResourceManager.GetString("labelRequestQuota", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Resource.
        /// </summary>
        public static string labelResource {
            get {
                return ResourceManager.GetString("labelResource", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Storage.
        /// </summary>
        public static string LabelStorage {
            get {
                return ResourceManager.GetString("LabelStorage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Activities.
        /// </summary>
        public static string LabelTasksMenuItem {
            get {
                return ResourceManager.GetString("LabelTasksMenuItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notebook VMs.
        /// </summary>
        public static string labelWorkstationsMenuItem {
            get {
                return ResourceManager.GetString("labelWorkstationsMenuItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Learn more.
        /// </summary>
        public static string learnMore {
            get {
                return ResourceManager.GetString("learnMore", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Location.
        /// </summary>
        public static string location {
            get {
                return ResourceManager.GetString("location", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Managed Compute.
        /// </summary>
        public static string machineLearningCompute {
            get {
                return ResourceManager.GetString("machineLearningCompute", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Identity.
        /// </summary>
        public static string ManagedIdentities_menuText {
            get {
                return ResourceManager.GetString("ManagedIdentities_menuText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Microsoft-managed.
        /// </summary>
        public static string ManagedKeyVault {
            get {
                return ResourceManager.GetString("ManagedKeyVault", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to All provisioning states.
        /// </summary>
        public static string MLAppDeployments_Grid_StatusFilter_all {
            get {
                return ResourceManager.GetString("MLAppDeployments_Grid_StatusFilter_all", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} ({1}).
        /// </summary>
        public static string MLAppDeployments_Grid_StatusFilter_itemFormat {
            get {
                return ResourceManager.GetString("MLAppDeployments_Grid_StatusFilter_itemFormat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} connection states selected.
        /// </summary>
        public static string MLAppDeployments_Grid_StatusFilter_some {
            get {
                return ResourceManager.GetString("MLAppDeployments_Grid_StatusFilter_some", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deployment name.
        /// </summary>
        public static string MLAppDeployments_GridColumns_name {
            get {
                return ResourceManager.GetString("MLAppDeployments_GridColumns_name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Provisioning state.
        /// </summary>
        public static string MLAppDeployments_GridColumns_status {
            get {
                return ResourceManager.GetString("MLAppDeployments_GridColumns_status", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Traffic %.
        /// </summary>
        public static string MLAppDeployments_GridColumns_traffic {
            get {
                return ResourceManager.GetString("MLAppDeployments_GridColumns_traffic", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Drag-n-Drop to build machine learning models.
        /// </summary>
        public static string mlStudioCapability1 {
            get {
                return ResourceManager.GetString("mlStudioCapability1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No limit to data size or compute capacity for model training.
        /// </summary>
        public static string mlStudioCapability2 {
            get {
                return ResourceManager.GetString("mlStudioCapability2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Intrinsic and powerful Python support.
        /// </summary>
        public static string mlStudioCapability3 {
            get {
                return ResourceManager.GetString("mlStudioCapability3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to One click to deploy your web service.
        /// </summary>
        public static string mlStudioCapability4 {
            get {
                return ResourceManager.GetString("mlStudioCapability4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rich and fast-growing modules support.
        /// </summary>
        public static string mlStudioCapability5 {
            get {
                return ResourceManager.GetString("mlStudioCapability5", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Launch visual interface.
        /// </summary>
        public static string mlStudioLaunchLabel {
            get {
                return ResourceManager.GetString("mlStudioLaunchLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to What&apos;s possible with visual interface.
        /// </summary>
        public static string mlStudioSubtitle {
            get {
                return ResourceManager.GetString("mlStudioSubtitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Visual interface (preview).
        /// </summary>
        public static string mlStudioTitle {
            get {
                return ResourceManager.GetString("mlStudioTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Validation failed for the following tabs: {0}. Required information is missing or not valid..
        /// </summary>
        public static string multipleInvalidTabErrorMessage {
            get {
                return ResourceManager.GetString("multipleInvalidTabErrorMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to An error occurred while saving workspace managed outbound access settings. Please try again or contact support if the issue persists..
        /// </summary>
        public static string Network_NetworkIsolation_ErrorWorkspaceManagedOutboundAccessNotificationDescription {
            get {
                return ResourceManager.GetString("Network_NetworkIsolation_ErrorWorkspaceManagedOutboundAccessNotificationDescripti" +
                        "on", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed to Save Workspace Managed Outbound Access Settings.
        /// </summary>
        public static string Network_NetworkIsolation_ErrorWorkspaceManagedOutboundAccessNotificationTitle {
            get {
                return ResourceManager.GetString("Network_NetworkIsolation_ErrorWorkspaceManagedOutboundAccessNotificationTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your changes to workspace managed outbound access settings are being saved..
        /// </summary>
        public static string Network_NetworkIsolation_InProgressWorkspaceManagedOutboundAccessNotificationDescription {
            get {
                return ResourceManager.GetString("Network_NetworkIsolation_InProgressWorkspaceManagedOutboundAccessNotificationDesc" +
                        "ription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Saving Workspace Managed Outbound Access Settings.
        /// </summary>
        public static string Network_NetworkIsolation_InProgressWorkspaceManagedOutboundAccessNotificationTitle {
            get {
                return ResourceManager.GetString("Network_NetworkIsolation_InProgressWorkspaceManagedOutboundAccessNotificationTitl" +
                        "e", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your changes to workspace managed outbound access settings have been successfully saved..
        /// </summary>
        public static string Network_NetworkIsolation_SuccessWorkspaceManagedOutboundAccessNotificationDescription {
            get {
                return ResourceManager.GetString("Network_NetworkIsolation_SuccessWorkspaceManagedOutboundAccessNotificationDescrip" +
                        "tion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Workspace Managed Outbound Access Settings Saved.
        /// </summary>
        public static string Network_NetworkIsolation_SuccessWorkspaceManagedOutboundAccessNotificationTitle {
            get {
                return ResourceManager.GetString("Network_NetworkIsolation_SuccessWorkspaceManagedOutboundAccessNotificationTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Private endpoint connections.
        /// </summary>
        public static string Networking_PrivateEndpointConnections_TabText {
            get {
                return ResourceManager.GetString("Networking_PrivateEndpointConnections_TabText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You may not wish to add your client IP address if the network you are using the azure portal from is atypical (home vs. work environment for example)..
        /// </summary>
        public static string Networking_PublicAccess_addClientIpAddressInfo {
            get {
                return ResourceManager.GetString("Networking_PublicAccess_addClientIpAddressInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add your client IP address (&apos;{0}&apos;).
        /// </summary>
        public static string Networking_PublicAccess_addClientIpAddressLabel {
            get {
                return ResourceManager.GetString("Networking_PublicAccess_addClientIpAddressLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Address range.
        /// </summary>
        public static string Networking_PublicAccess_addressRange {
            get {
                return ResourceManager.GetString("Networking_PublicAccess_addressRange", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IP address or CIDR.
        /// </summary>
        public static string Networking_PublicAccess_addressRangePlaceHolder {
            get {
                return ResourceManager.GetString("Networking_PublicAccess_addressRangePlaceHolder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to All networks, including the internet, can access this resource..
        /// </summary>
        public static string Networking_PublicAccess_AllNetworksInfoText {
            get {
                return ResourceManager.GetString("Networking_PublicAccess_AllNetworksInfoText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Public network access allows access to this resource through the internet using a public IP address. An application or resource that is granted access with the following network rules still requires proper authorization to access this resource. {0}.
        /// </summary>
        public static string Networking_PublicAccess_Description {
            get {
                return ResourceManager.GetString("Networking_PublicAccess_Description", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No public network can access this resource..
        /// </summary>
        public static string Networking_PublicAccess_DisabledInfoText {
            get {
                return ResourceManager.GetString("Networking_PublicAccess_DisabledInfoText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow access from public IP you specified below..
        /// </summary>
        public static string Networking_PublicAccess_EnabledFromSelectedIpInfoText {
            get {
                return ResourceManager.GetString("Networking_PublicAccess_EnabledFromSelectedIpInfoText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to An error occurred while saving public access settings. Please try again or contact support if the issue persists..
        /// </summary>
        public static string Networking_PublicAccess_FailurePublicAccesNetworkNotificationDescription {
            get {
                return ResourceManager.GetString("Networking_PublicAccess_FailurePublicAccesNetworkNotificationDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed to Save Public Access Settings.
        /// </summary>
        public static string Networking_PublicAccess_FailurePublicAccesNetworkNotificationTitle {
            get {
                return ResourceManager.GetString("Networking_PublicAccess_FailurePublicAccesNetworkNotificationTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add IP ranges to allow access from the internet or your on-premises networks..
        /// </summary>
        public static string Networking_PublicAccess_firewallDescription {
            get {
                return ResourceManager.GetString("Networking_PublicAccess_firewallDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Firewall.
        /// </summary>
        public static string Networking_PublicAccess_firewallHeader {
            get {
                return ResourceManager.GetString("Networking_PublicAccess_firewallHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Learn more.
        /// </summary>
        public static string Networking_PublicAccess_firewallLearnMore {
            get {
                return ResourceManager.GetString("Networking_PublicAccess_firewallLearnMore", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Two identical address ranges have been specified. Address ranges must be unique..
        /// </summary>
        public static string Networking_PublicAccess_FirewallValidation_duplicateIpAddresses {
            get {
                return ResourceManager.GetString("Networking_PublicAccess_FirewallValidation_duplicateIpAddresses", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The subnet prefix must be equal to {0}..
        /// </summary>
        public static string Networking_PublicAccess_FirewallValidation_exactPrefix {
            get {
                return ResourceManager.GetString("Networking_PublicAccess_FirewallValidation_exactPrefix", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The prefix must be between {0} and {1}..
        /// </summary>
        public static string Networking_PublicAccess_FirewallValidation_formattedPrefix {
            get {
                return ResourceManager.GetString("Networking_PublicAccess_FirewallValidation_formattedPrefix", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Specify an IP address or CIDR..
        /// </summary>
        public static string Networking_PublicAccess_FirewallValidation_invalidCidr {
            get {
                return ResourceManager.GetString("Networking_PublicAccess_FirewallValidation_invalidCidr", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} is not a valid CIDR block. Use {1}/{2} instead..
        /// </summary>
        public static string Networking_PublicAccess_FirewallValidation_invalidCIDRBlockWithSuggestion {
            get {
                return ResourceManager.GetString("Networking_PublicAccess_FirewallValidation_invalidCIDRBlockWithSuggestion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid IP address..
        /// </summary>
        public static string Networking_PublicAccess_FirewallValidation_invalidIPAddress {
            get {
                return ResourceManager.GetString("Networking_PublicAccess_FirewallValidation_invalidIPAddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The octet &apos;{0}&apos; in IP address &apos;{1}&apos; contains a leading zero..
        /// </summary>
        public static string Networking_PublicAccess_FirewallValidation_leadingZerosIpAddress {
            get {
                return ResourceManager.GetString("Networking_PublicAccess_FirewallValidation_leadingZerosIpAddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Malformed address range. Address was {0}..
        /// </summary>
        public static string Networking_PublicAccess_FirewallValidation_malformedSubnet {
            get {
                return ResourceManager.GetString("Networking_PublicAccess_FirewallValidation_malformedSubnet", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The prefix must be smaller than or equal to {0}..
        /// </summary>
        public static string Networking_PublicAccess_FirewallValidation_maxPrefix {
            get {
                return ResourceManager.GetString("Networking_PublicAccess_FirewallValidation_maxPrefix", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The prefix must be greater than or equal to {0}..
        /// </summary>
        public static string Networking_PublicAccess_FirewallValidation_minPrefix {
            get {
                return ResourceManager.GetString("Networking_PublicAccess_FirewallValidation_minPrefix", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to A non-null address range is required..
        /// </summary>
        public static string Networking_PublicAccess_FirewallValidation_nonNullSubnet {
            get {
                return ResourceManager.GetString("Networking_PublicAccess_FirewallValidation_nonNullSubnet", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Octet {0} with value {1} is invalid. It must be between {2} and {3}..
        /// </summary>
        public static string Networking_PublicAccess_FirewallValidation_octet {
            get {
                return ResourceManager.GetString("Networking_PublicAccess_FirewallValidation_octet", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IP rules support public IP addresses only..
        /// </summary>
        public static string Networking_PublicAccess_FirewallValidation_publicIpRuleValidation {
            get {
                return ResourceManager.GetString("Networking_PublicAccess_FirewallValidation_publicIpRuleValidation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your changes to public access settings are being saved..
        /// </summary>
        public static string Networking_PublicAccess_InprogressPublicAccesNetworkNotificationDescription {
            get {
                return ResourceManager.GetString("Networking_PublicAccess_InprogressPublicAccesNetworkNotificationDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Saving Public Access Settings.
        /// </summary>
        public static string Networking_PublicAccess_InprogressPublicAccesNetworkNotificationTitle {
            get {
                return ResourceManager.GetString("Networking_PublicAccess_InprogressPublicAccesNetworkNotificationTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Learn more.
        /// </summary>
        public static string Networking_PublicAccess_LearnMoreText {
            get {
                return ResourceManager.GetString("Networking_PublicAccess_LearnMoreText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to All networks.
        /// </summary>
        public static string Networking_PublicAccess_RadioAllNetworksText {
            get {
                return ResourceManager.GetString("Networking_PublicAccess_RadioAllNetworksText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Disabled.
        /// </summary>
        public static string Networking_PublicAccess_RadioDisabledText {
            get {
                return ResourceManager.GetString("Networking_PublicAccess_RadioDisabledText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enabled from all networks.
        /// </summary>
        public static string Networking_PublicAccess_RadioEnabledFromAllNetworks {
            get {
                return ResourceManager.GetString("Networking_PublicAccess_RadioEnabledFromAllNetworks", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enabled from selected IP addresses.
        /// </summary>
        public static string Networking_PublicAccess_RadioEnabledFromSelectedIp {
            get {
                return ResourceManager.GetString("Networking_PublicAccess_RadioEnabledFromSelectedIp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Public network access.
        /// </summary>
        public static string Networking_PublicAccess_RadioLabel {
            get {
                return ResourceManager.GetString("Networking_PublicAccess_RadioLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your changes to public access settings have been successfully saved..
        /// </summary>
        public static string Networking_PublicAccess_SuccessPublicAccesNetworkNotificationDescription {
            get {
                return ResourceManager.GetString("Networking_PublicAccess_SuccessPublicAccesNetworkNotificationDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Public Access Settings Saved.
        /// </summary>
        public static string Networking_PublicAccess_SuccessPublicAccesNetworkNotificationTitle {
            get {
                return ResourceManager.GetString("Networking_PublicAccess_SuccessPublicAccesNetworkNotificationTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Public access.
        /// </summary>
        public static string Networking_PublicAccess_TabText {
            get {
                return ResourceManager.GetString("Networking_PublicAccess_TabText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Discard changes.
        /// </summary>
        public static string Networking_PublicAccess_Toolbar_DiscardChangesText {
            get {
                return ResourceManager.GetString("Networking_PublicAccess_Toolbar_DiscardChangesText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Refresh.
        /// </summary>
        public static string Networking_PublicAccess_Toolbar_RefreshText {
            get {
                return ResourceManager.GetString("Networking_PublicAccess_Toolbar_RefreshText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save.
        /// </summary>
        public static string Networking_PublicAccess_Toolbar_SaveText {
            get {
                return ResourceManager.GetString("Networking_PublicAccess_Toolbar_SaveText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Saving....
        /// </summary>
        public static string Networking_PublicAccess_Toolbar_SavingText {
            get {
                return ResourceManager.GetString("Networking_PublicAccess_Toolbar_SavingText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Workspace managed outbound access.
        /// </summary>
        public static string Networking_WorkspaceManagedOutboundAccess_tabText {
            get {
                return ResourceManager.GetString("Networking_WorkspaceManagedOutboundAccess_tabText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to (new) {0}.
        /// </summary>
        public static string newResource {
            get {
                return ResourceManager.GetString("newResource", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to (New) {0}.
        /// </summary>
        public static string newResourceCapitalized {
            get {
                return ResourceManager.GetString("newResourceCapitalized", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to (New) {0}.
        /// </summary>
        public static string newResourceFormatCaps {
            get {
                return ResourceManager.GetString("newResourceFormatCaps", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No content.
        /// </summary>
        public static string noContent {
            get {
                return ResourceManager.GetString("noContent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to None.
        /// </summary>
        public static string none {
            get {
                return ResourceManager.GetString("none", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Machine learning online endpoint.
        /// </summary>
        public static string onlineEndpointName {
            get {
                return ResourceManager.GetString("onlineEndpointName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Conda dependencies file.
        /// </summary>
        public static string OnlineEndpoints_Create_Dependencies_condaDependenciesFileLabel {
            get {
                return ResourceManager.GetString("OnlineEndpoints_Create_Dependencies_condaDependenciesFileLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customize your model deployment into inference app..
        /// </summary>
        public static string OnlineEndpoints_Create_Dependencies_description {
            get {
                return ResourceManager.GetString("OnlineEndpoints_Create_Dependencies_description", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Entry script file.
        /// </summary>
        public static string OnlineEndpoints_Create_Dependencies_entryScriptFileLabel {
            get {
                return ResourceManager.GetString("OnlineEndpoints_Create_Dependencies_entryScriptFileLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select a conda dependency file.
        /// </summary>
        public static string OnlineEndpoints_Create_Dependencies_selectCondaDependenciesFile {
            get {
                return ResourceManager.GetString("OnlineEndpoints_Create_Dependencies_selectCondaDependenciesFile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select an entry script file.
        /// </summary>
        public static string OnlineEndpoints_Create_Dependencies_selectEntryScriptFileMessage {
            get {
                return ResourceManager.GetString("OnlineEndpoints_Create_Dependencies_selectEntryScriptFileMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dependencies.
        /// </summary>
        public static string OnlineEndpoints_Create_Dependencies_tabName {
            get {
                return ResourceManager.GetString("OnlineEndpoints_Create_Dependencies_tabName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Azure Machine Learning Inference apps enable you to quickly build, deploy and scale enterprise-grade machine learning models running on any platform. Use any open source machine learning framework like TensorFlow, PyTorch, SciKit-Learn, ONNX and more. Use our “no code deployment” to accelerate your productivity or customize your Inference app with your own docker container and/or model scoring code..
        /// </summary>
        public static string OnlineEndpoints_Create_introText {
            get {
                return ResourceManager.GetString("OnlineEndpoints_Create_introText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Model.
        /// </summary>
        public static string OnlineEndpoints_Create_modelLabel {
            get {
                return ResourceManager.GetString("OnlineEndpoints_Create_modelLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select a model.
        /// </summary>
        public static string OnlineEndpoints_Create_modelPlaceholder {
            get {
                return ResourceManager.GetString("OnlineEndpoints_Create_modelPlaceholder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Model version.
        /// </summary>
        public static string OnlineEndpoints_Create_modelVersionLabel {
            get {
                return ResourceManager.GetString("OnlineEndpoints_Create_modelVersionLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select a model version.
        /// </summary>
        public static string OnlineEndpoints_Create_modelVersionPlaceholder {
            get {
                return ResourceManager.GetString("OnlineEndpoints_Create_modelVersionPlaceholder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Name.
        /// </summary>
        public static string OnlineEndpoints_Create_nameLabel {
            get {
                return ResourceManager.GetString("OnlineEndpoints_Create_nameLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create an ML App.
        /// </summary>
        public static string OnlineEndpoints_Create_subTitle {
            get {
                return ResourceManager.GetString("OnlineEndpoints_Create_subTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Machine learning online endpoint.
        /// </summary>
        public static string OnlineEndpoints_Create_title {
            get {
                return ResourceManager.GetString("OnlineEndpoints_Create_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Workspace.
        /// </summary>
        public static string OnlineEndpoints_Create_workspaceLabel {
            get {
                return ResourceManager.GetString("OnlineEndpoints_Create_workspaceLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select a workspace.
        /// </summary>
        public static string OnlineEndpoints_Create_workspacePlaceholder {
            get {
                return ResourceManager.GetString("OnlineEndpoints_Create_workspacePlaceholder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Workspace.
        /// </summary>
        public static string onlineEndpointWorkspaceName {
            get {
                return ResourceManager.GetString("onlineEndpointWorkspaceName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dependency outbound rules will be created for all dependency resources under AzureML registry. View these rules under Dependency outbound rules..
        /// </summary>
        public static string OutboundAccessRule_SettingsBlade_amlRegistryPEDependencyRulesWarning {
            get {
                return ResourceManager.GetString("OutboundAccessRule_SettingsBlade_amlRegistryPEDependencyRulesWarning", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No private link config is found in resource. Set it up before creating PE outbound rule..
        /// </summary>
        public static string OutboundAccessRule_SettingsBlade_appGateway_subResource_errorMessage {
            get {
                return ResourceManager.GetString("OutboundAccessRule_SettingsBlade_appGateway_subResource_errorMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to FQDNs resolve to the private IP of the Application Gateway private endpoint inside the workspace&apos;s managed network. FQDNs are editable as needed. Please save after editing the fields..
        /// </summary>
        public static string OutboundAccessRule_SettingsBlade_applicationGatewayFqdnInfoBallon {
            get {
                return ResourceManager.GetString("OutboundAccessRule_SettingsBlade_applicationGatewayFqdnInfoBallon", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Type of the outbound destination, FQDN, Private Endpoint, Service Tag..
        /// </summary>
        public static string OutboundAccessRule_SettingsBlade_destinationTypeBalloonContent {
            get {
                return ResourceManager.GetString("OutboundAccessRule_SettingsBlade_destinationTypeBalloonContent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Destination type.
        /// </summary>
        public static string OutboundAccessRule_SettingsBlade_destinationTypeLabel {
            get {
                return ResourceManager.GetString("OutboundAccessRule_SettingsBlade_destinationTypeLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Managed Network Isolation..
        /// </summary>
        public static string OutboundAccessRule_SettingsBlade_FqdnCostInfo_managedNetworkIsolationLinkText {
            get {
                return ResourceManager.GetString("OutboundAccessRule_SettingsBlade_FqdnCostInfo_managedNetworkIsolationLinkText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to For more information on Azure Firewall, see .
        /// </summary>
        public static string OutboundAccessRule_SettingsBlade_FqdnCostInfo_moreInformationText {
            get {
                return ResourceManager.GetString("OutboundAccessRule_SettingsBlade_FqdnCostInfo_moreInformationText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pricing..
        /// </summary>
        public static string OutboundAccessRule_SettingsBlade_FqdnCostInfo_pricingLinkText {
            get {
                return ResourceManager.GetString("OutboundAccessRule_SettingsBlade_FqdnCostInfo_pricingLinkText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to FQDN outbound rules are implemented using Azure Firewall. If you use outbound FQDN rules, charges for Azure Firewall are included in your billing. To learn more about outbound rules, see .
        /// </summary>
        public static string OutboundAccessRule_SettingsBlade_FqdnCostInfo_text {
            get {
                return ResourceManager.GetString("OutboundAccessRule_SettingsBlade_FqdnCostInfo_text", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fully Qualified Domain Name to allow for outbound traffic..
        /// </summary>
        public static string OutboundAccessRule_SettingsBlade_fqdnDestinationBalloonContent {
            get {
                return ResourceManager.GetString("OutboundAccessRule_SettingsBlade_fqdnDestinationBalloonContent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to FQDN destination.
        /// </summary>
        public static string OutboundAccessRule_SettingsBlade_fqdnDestinationLabel {
            get {
                return ResourceManager.GetString("OutboundAccessRule_SettingsBlade_fqdnDestinationLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to FQDNs.
        /// </summary>
        public static string OutboundAccessRule_SettingsBlade_fqdnsLabel {
            get {
                return ResourceManager.GetString("OutboundAccessRule_SettingsBlade_fqdnsLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to FQDN is required..
        /// </summary>
        public static string OutboundAccessRule_SettingsBlade_FqdnValidation_applicationGatewayFqdnRequired {
            get {
                return ResourceManager.GetString("OutboundAccessRule_SettingsBlade_FqdnValidation_applicationGatewayFqdnRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Domain label should not be empty..
        /// </summary>
        public static string OutboundAccessRule_SettingsBlade_FqdnValidation_emptyLabelMessage {
            get {
                return ResourceManager.GetString("OutboundAccessRule_SettingsBlade_FqdnValidation_emptyLabelMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Specify a valid FQDN with at least three labels for Application Gateway access..
        /// </summary>
        public static string OutboundAccessRule_SettingsBlade_FqdnValidation_invalidFormatMessage {
            get {
                return ResourceManager.GetString("OutboundAccessRule_SettingsBlade_FqdnValidation_invalidFormatMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Note.
        /// </summary>
        public static string OutboundAccessRule_SettingsBlade_Note {
            get {
                return ResourceManager.GetString("OutboundAccessRule_SettingsBlade_Note", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Provide a single port, such as 80; a port range, such as 1024-655535; or a comma-separated list of single ports and/or port ranges, such as 80,1024-655535. This specifies on which ports traffic will be allowed by this rule. Provide an asterisk(*) to allow traffic on any port..
        /// </summary>
        public static string OutboundAccessRule_SettingsBlade_portRangeBalloonContent {
            get {
                return ResourceManager.GetString("OutboundAccessRule_SettingsBlade_portRangeBalloonContent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Port ranges.
        /// </summary>
        public static string OutboundAccessRule_SettingsBlade_portRangeLabel {
            get {
                return ResourceManager.GetString("OutboundAccessRule_SettingsBlade_portRangeLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Network protocol to allow, TCP, UDP, ICMP or Any.
        /// </summary>
        public static string OutboundAccessRule_SettingsBlade_protocolBalloonContent {
            get {
                return ResourceManager.GetString("OutboundAccessRule_SettingsBlade_protocolBalloonContent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Potocol.
        /// </summary>
        public static string OutboundAccessRule_SettingsBlade_protocolLabel {
            get {
                return ResourceManager.GetString("OutboundAccessRule_SettingsBlade_protocolLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Resource group containing the target resource for the private endpoint..
        /// </summary>
        public static string OutboundAccessRule_SettingsBlade_resourceGroupBalloonContent {
            get {
                return ResourceManager.GetString("OutboundAccessRule_SettingsBlade_resourceGroupBalloonContent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Resource group.
        /// </summary>
        public static string OutboundAccessRule_SettingsBlade_resourceGroupLabel {
            get {
                return ResourceManager.GetString("OutboundAccessRule_SettingsBlade_resourceGroupLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Name of the target resource for the private endpoint..
        /// </summary>
        public static string OutboundAccessRule_SettingsBlade_resourceNameBalloonContent {
            get {
                return ResourceManager.GetString("OutboundAccessRule_SettingsBlade_resourceNameBalloonContent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Resource name.
        /// </summary>
        public static string OutboundAccessRule_SettingsBlade_resourceNameLabel {
            get {
                return ResourceManager.GetString("OutboundAccessRule_SettingsBlade_resourceNameLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Type of the Azure resource that supports Private Link..
        /// </summary>
        public static string OutboundAccessRule_SettingsBlade_resourceTypeBalloonContent {
            get {
                return ResourceManager.GetString("OutboundAccessRule_SettingsBlade_resourceTypeBalloonContent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Resource type.
        /// </summary>
        public static string OutboundAccessRule_SettingsBlade_resourceTypeLabel {
            get {
                return ResourceManager.GetString("OutboundAccessRule_SettingsBlade_resourceTypeLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rule name already exists.
        /// </summary>
        public static string OutboundAccessRule_SettingsBlade_ruleNameAlreadyExists {
            get {
                return ResourceManager.GetString("OutboundAccessRule_SettingsBlade_ruleNameAlreadyExists", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Name of the outbound rule that is unique in the workspace..
        /// </summary>
        public static string OutboundAccessRule_SettingsBlade_ruleNameBalloonContent {
            get {
                return ResourceManager.GetString("OutboundAccessRule_SettingsBlade_ruleNameBalloonContent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rule name.
        /// </summary>
        public static string OutboundAccessRule_SettingsBlade_ruleNameLabel {
            get {
                return ResourceManager.GetString("OutboundAccessRule_SettingsBlade_ruleNameLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Predefined identifiers that represent a category of IP addresses..
        /// </summary>
        public static string OutboundAccessRule_SettingsBlade_serviceTagBalloonContent {
            get {
                return ResourceManager.GetString("OutboundAccessRule_SettingsBlade_serviceTagBalloonContent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Service tag.
        /// </summary>
        public static string OutboundAccessRule_SettingsBlade_serviceTagLabel {
            get {
                return ResourceManager.GetString("OutboundAccessRule_SettingsBlade_serviceTagLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Check to enable an additional private endpoint to be used by jobs running on Spark..
        /// </summary>
        public static string OutboundAccessRule_SettingsBlade_sparkEnabledBalloonContent {
            get {
                return ResourceManager.GetString("OutboundAccessRule_SettingsBlade_sparkEnabledBalloonContent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Spark enabled.
        /// </summary>
        public static string OutboundAccessRule_SettingsBlade_sparkEnabledLabel {
            get {
                return ResourceManager.GetString("OutboundAccessRule_SettingsBlade_sparkEnabledLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Indicates whether managed network is provisioned for Spark jobs..
        /// </summary>
        public static string OutboundAccessRule_SettingsBlade_sparkStatusBalloonContent {
            get {
                return ResourceManager.GetString("OutboundAccessRule_SettingsBlade_sparkStatusBalloonContent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Spark status.
        /// </summary>
        public static string OutboundAccessRule_SettingsBlade_sparkStatusLabel {
            get {
                return ResourceManager.GetString("OutboundAccessRule_SettingsBlade_sparkStatusLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Status is read-only, when Active, indicates the managed network is provisioned and ready. When Inactive, indicates it has not provisioned..
        /// </summary>
        public static string OutboundAccessRule_SettingsBlade_statusBalloonContent {
            get {
                return ResourceManager.GetString("OutboundAccessRule_SettingsBlade_statusBalloonContent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Status.
        /// </summary>
        public static string OutboundAccessRule_SettingsBlade_statusLabel {
            get {
                return ResourceManager.GetString("OutboundAccessRule_SettingsBlade_statusLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sub resource to connect for the private endpoint..
        /// </summary>
        public static string OutboundAccessRule_SettingsBlade_subResourceBalloonContent {
            get {
                return ResourceManager.GetString("OutboundAccessRule_SettingsBlade_subResourceBalloonContent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sub resource.
        /// </summary>
        public static string OutboundAccessRule_SettingsBlade_subResourceLabel {
            get {
                return ResourceManager.GetString("OutboundAccessRule_SettingsBlade_subResourceLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Subscription containing the target resource for the private endpoint..
        /// </summary>
        public static string OutboundAccessRule_SettingsBlade_subscriptionBalloonContent {
            get {
                return ResourceManager.GetString("OutboundAccessRule_SettingsBlade_subscriptionBalloonContent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Subscription.
        /// </summary>
        public static string OutboundAccessRule_SettingsBlade_subscriptionLabel {
            get {
                return ResourceManager.GetString("OutboundAccessRule_SettingsBlade_subscriptionLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Workspace outbound rules.
        /// </summary>
        public static string OutboundAccessRule_SettingsBlade_title {
            get {
                return ResourceManager.GetString("OutboundAccessRule_SettingsBlade_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Overview.
        /// </summary>
        public static string Overview {
            get {
                return ResourceManager.GetString("Overview", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Azure Machine Learning Studio is a web app where you can build, train, test, and deploy ML models. Launch it now to start exploring, or {}.
        /// </summary>
        public static string Overview_Banner_Description_label {
            get {
                return ResourceManager.GetString("Overview_Banner_Description_label", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to learn more about the Azure Machine Learning studio.
        /// </summary>
        public static string Overview_Banner_Description_learnMoreText {
            get {
                return ResourceManager.GetString("Overview_Banner_Description_learnMoreText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Launch studio.
        /// </summary>
        public static string Overview_Banner_launchButtonText {
            get {
                return ResourceManager.GetString("Overview_Banner_launchButtonText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Launch registry in studio.
        /// </summary>
        public static string Overview_Banner_Registry_launchButtonText {
            get {
                return ResourceManager.GetString("Overview_Banner_Registry_launchButtonText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Work with your registry in Azure Machine Learning Studio.
        /// </summary>
        public static string Overview_Banner_Registry_title {
            get {
                return ResourceManager.GetString("Overview_Banner_Registry_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Work with your models in Azure Machine Learning Studio.
        /// </summary>
        public static string Overview_Banner_title {
            get {
                return ResourceManager.GetString("Overview_Banner_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to edit.
        /// </summary>
        public static string Overview_Essentials_edit {
            get {
                return ResourceManager.GetString("Overview_Essentials_edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Managed resource group.
        /// </summary>
        public static string Overview_Essentials_managedResourceGroup {
            get {
                return ResourceManager.GetString("Overview_Essentials_managedResourceGroup", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MLFlow tracking URI.
        /// </summary>
        public static string Overview_Essentials_mlFlowWebURLText {
            get {
                return ResourceManager.GetString("Overview_Essentials_mlFlowWebURLText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Project ID.
        /// </summary>
        public static string Overview_Essentials_ProjectID {
            get {
                return ResourceManager.GetString("Overview_Essentials_ProjectID", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Registry web URL.
        /// </summary>
        public static string Overview_Essentials_registryWebURLText {
            get {
                return ResourceManager.GetString("Overview_Essentials_registryWebURLText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Studio web URL.
        /// </summary>
        public static string Overview_Essentials_studioWebURLText {
            get {
                return ResourceManager.GetString("Overview_Essentials_studioWebURLText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Summary Home.
        /// </summary>
        public static string OverviewKeywords {
            get {
                return ResourceManager.GetString("OverviewKeywords", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Private endpoint.
        /// </summary>
        public static string PrivateEndpoints_Commands_addPrivateEndpoint {
            get {
                return ResourceManager.GetString("PrivateEndpoints_Commands_addPrivateEndpoint", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Approve.
        /// </summary>
        public static string PrivateEndpoints_Commands_approve {
            get {
                return ResourceManager.GetString("PrivateEndpoints_Commands_approve", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Do you want to approve the {0} selected connections?.
        /// </summary>
        public static string PrivateEndpoints_Commands_Approve_messagePlural {
            get {
                return ResourceManager.GetString("PrivateEndpoints_Commands_Approve_messagePlural", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Do you want to approve the connection &apos;{0}&apos;?.
        /// </summary>
        public static string PrivateEndpoints_Commands_Approve_messageSingular {
            get {
                return ResourceManager.GetString("PrivateEndpoints_Commands_Approve_messageSingular", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Approve connection.
        /// </summary>
        public static string PrivateEndpoints_Commands_Approve_title {
            get {
                return ResourceManager.GetString("PrivateEndpoints_Commands_Approve_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed to approve {0} out of {1} private endpoint connections.
        /// </summary>
        public static string PrivateEndpoints_Commands_ApproveNotifications_Failure_message {
            get {
                return ResourceManager.GetString("PrivateEndpoints_Commands_ApproveNotifications_Failure_message", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed to approve private endpoint connections.
        /// </summary>
        public static string PrivateEndpoints_Commands_ApproveNotifications_Failure_title {
            get {
                return ResourceManager.GetString("PrivateEndpoints_Commands_ApproveNotifications_Failure_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Approving {0} private endpoint connections.
        /// </summary>
        public static string PrivateEndpoints_Commands_ApproveNotifications_InProgress_message {
            get {
                return ResourceManager.GetString("PrivateEndpoints_Commands_ApproveNotifications_InProgress_message", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Approving private endpoint connections....
        /// </summary>
        public static string PrivateEndpoints_Commands_ApproveNotifications_InProgress_title {
            get {
                return ResourceManager.GetString("PrivateEndpoints_Commands_ApproveNotifications_InProgress_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Successfully approved {0} private endpoint connections..
        /// </summary>
        public static string PrivateEndpoints_Commands_ApproveNotifications_Success_message {
            get {
                return ResourceManager.GetString("PrivateEndpoints_Commands_ApproveNotifications_Success_message", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Successfully approved private endpoint connections.
        /// </summary>
        public static string PrivateEndpoints_Commands_ApproveNotifications_Success_title {
            get {
                return ResourceManager.GetString("PrivateEndpoints_Commands_ApproveNotifications_Success_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed to delete {0} out of {1} private endpoint connections.
        /// </summary>
        public static string PrivateEndpoints_Commands_DeleteNotifications_Failure_message {
            get {
                return ResourceManager.GetString("PrivateEndpoints_Commands_DeleteNotifications_Failure_message", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed to delete private endpoint connections.
        /// </summary>
        public static string PrivateEndpoints_Commands_DeleteNotifications_Failure_title {
            get {
                return ResourceManager.GetString("PrivateEndpoints_Commands_DeleteNotifications_Failure_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deleting {0} private endpoint connections.
        /// </summary>
        public static string PrivateEndpoints_Commands_DeleteNotifications_InProgress_message {
            get {
                return ResourceManager.GetString("PrivateEndpoints_Commands_DeleteNotifications_InProgress_message", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deleting private endpoint connections....
        /// </summary>
        public static string PrivateEndpoints_Commands_DeleteNotifications_InProgress_title {
            get {
                return ResourceManager.GetString("PrivateEndpoints_Commands_DeleteNotifications_InProgress_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Successfully deleted {0} private endpoint connections..
        /// </summary>
        public static string PrivateEndpoints_Commands_DeleteNotifications_Success_message {
            get {
                return ResourceManager.GetString("PrivateEndpoints_Commands_DeleteNotifications_Success_message", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Successfully deleted private endpoint connections.
        /// </summary>
        public static string PrivateEndpoints_Commands_DeleteNotifications_Success_title {
            get {
                return ResourceManager.GetString("PrivateEndpoints_Commands_DeleteNotifications_Success_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Description.
        /// </summary>
        public static string PrivateEndpoints_Commands_description {
            get {
                return ResourceManager.GetString("PrivateEndpoints_Commands_description", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reject.
        /// </summary>
        public static string PrivateEndpoints_Commands_reject {
            get {
                return ResourceManager.GetString("PrivateEndpoints_Commands_reject", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Do you want to reject the {0} selected connections?.
        /// </summary>
        public static string PrivateEndpoints_Commands_Reject_messagePlural {
            get {
                return ResourceManager.GetString("PrivateEndpoints_Commands_Reject_messagePlural", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Do you want to reject the connection &apos;{0}&apos;?.
        /// </summary>
        public static string PrivateEndpoints_Commands_Reject_messageSingular {
            get {
                return ResourceManager.GetString("PrivateEndpoints_Commands_Reject_messageSingular", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reject connection.
        /// </summary>
        public static string PrivateEndpoints_Commands_Reject_title {
            get {
                return ResourceManager.GetString("PrivateEndpoints_Commands_Reject_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed to reject {0} out of {1} private endpoint connections.
        /// </summary>
        public static string PrivateEndpoints_Commands_RejectNotifications_Failure_message {
            get {
                return ResourceManager.GetString("PrivateEndpoints_Commands_RejectNotifications_Failure_message", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed to reject private endpoint connections.
        /// </summary>
        public static string PrivateEndpoints_Commands_RejectNotifications_Failure_title {
            get {
                return ResourceManager.GetString("PrivateEndpoints_Commands_RejectNotifications_Failure_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rejecting {0} private endpoint connections.
        /// </summary>
        public static string PrivateEndpoints_Commands_RejectNotifications_InProgress_message {
            get {
                return ResourceManager.GetString("PrivateEndpoints_Commands_RejectNotifications_InProgress_message", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rejecting private endpoint connections....
        /// </summary>
        public static string PrivateEndpoints_Commands_RejectNotifications_InProgress_title {
            get {
                return ResourceManager.GetString("PrivateEndpoints_Commands_RejectNotifications_InProgress_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Successfully rejected {0} private endpoint connections..
        /// </summary>
        public static string PrivateEndpoints_Commands_RejectNotifications_Success_message {
            get {
                return ResourceManager.GetString("PrivateEndpoints_Commands_RejectNotifications_Success_message", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Successfully rejected private endpoint connections.
        /// </summary>
        public static string PrivateEndpoints_Commands_RejectNotifications_Success_title {
            get {
                return ResourceManager.GetString("PrivateEndpoints_Commands_RejectNotifications_Success_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Remove.
        /// </summary>
        public static string PrivateEndpoints_Commands_remove {
            get {
                return ResourceManager.GetString("PrivateEndpoints_Commands_remove", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Do you want to delete the {0} selected connections?.
        /// </summary>
        public static string PrivateEndpoints_Commands_Remove_messagePlural {
            get {
                return ResourceManager.GetString("PrivateEndpoints_Commands_Remove_messagePlural", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Do you want to delete the connection &apos;{0}&apos;?.
        /// </summary>
        public static string PrivateEndpoints_Commands_Remove_messageSingular {
            get {
                return ResourceManager.GetString("PrivateEndpoints_Commands_Remove_messageSingular", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete connection.
        /// </summary>
        public static string PrivateEndpoints_Commands_Remove_title {
            get {
                return ResourceManager.GetString("PrivateEndpoints_Commands_Remove_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0}: {1}.
        /// </summary>
        public static string PrivateEndpoints_Create_validationErrorFormat {
            get {
                return ResourceManager.GetString("PrivateEndpoints_Create_validationErrorFormat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Filter by name....
        /// </summary>
        public static string PrivateEndpoints_filterByName {
            get {
                return ResourceManager.GetString("PrivateEndpoints_filterByName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Filter by connection state....
        /// </summary>
        public static string PrivateEndpoints_filterByStatus {
            get {
                return ResourceManager.GetString("PrivateEndpoints_filterByStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to There was an error processing your request. Try again in a few moments..
        /// </summary>
        public static string PrivateEndpoints_genericErrorMessage {
            get {
                return ResourceManager.GetString("PrivateEndpoints_genericErrorMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to All connection states.
        /// </summary>
        public static string PrivateEndpoints_Grid_StatusFilter_all {
            get {
                return ResourceManager.GetString("PrivateEndpoints_Grid_StatusFilter_all", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} ({1}).
        /// </summary>
        public static string PrivateEndpoints_Grid_StatusFilter_itemFormat {
            get {
                return ResourceManager.GetString("PrivateEndpoints_Grid_StatusFilter_itemFormat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} connection states selected.
        /// </summary>
        public static string PrivateEndpoints_Grid_StatusFilter_some {
            get {
                return ResourceManager.GetString("PrivateEndpoints_Grid_StatusFilter_some", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Description.
        /// </summary>
        public static string PrivateEndpoints_GridColumns_description {
            get {
                return ResourceManager.GetString("PrivateEndpoints_GridColumns_description", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Connection name.
        /// </summary>
        public static string PrivateEndpoints_GridColumns_name {
            get {
                return ResourceManager.GetString("PrivateEndpoints_GridColumns_name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Private endpoint.
        /// </summary>
        public static string PrivateEndpoints_GridColumns_privateEndpoint {
            get {
                return ResourceManager.GetString("PrivateEndpoints_GridColumns_privateEndpoint", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Connection state.
        /// </summary>
        public static string PrivateEndpoints_GridColumns_status {
            get {
                return ResourceManager.GetString("PrivateEndpoints_GridColumns_status", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Private endpoint connections.
        /// </summary>
        public static string PrivateEndpoints_label {
            get {
                return ResourceManager.GetString("PrivateEndpoints_label", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Networking.
        /// </summary>
        public static string PrivateEndpoints_menuText {
            get {
                return ResourceManager.GetString("PrivateEndpoints_menuText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Approved.
        /// </summary>
        public static string PrivateEndpoints_Status_approved {
            get {
                return ResourceManager.GetString("PrivateEndpoints_Status_approved", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Disconnected.
        /// </summary>
        public static string PrivateEndpoints_Status_disconnected {
            get {
                return ResourceManager.GetString("PrivateEndpoints_Status_disconnected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pending.
        /// </summary>
        public static string PrivateEndpoints_Status_pending {
            get {
                return ResourceManager.GetString("PrivateEndpoints_Status_pending", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rejected.
        /// </summary>
        public static string PrivateEndpoints_Status_rejected {
            get {
                return ResourceManager.GetString("PrivateEndpoints_Status_rejected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Refresh.
        /// </summary>
        public static string PrivateEndpoints_Toolbar_refresh {
            get {
                return ResourceManager.GetString("PrivateEndpoints_Toolbar_refresh", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The resource could not be deleted..
        /// </summary>
        public static string ProgressMachineLearningDeleteError {
            get {
                return ResourceManager.GetString("ProgressMachineLearningDeleteError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Registry could not be deleted..
        /// </summary>
        public static string ProgressMachineLearningRegistryDeleteError {
            get {
                return ResourceManager.GetString("ProgressMachineLearningRegistryDeleteError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deleting the Registry.
        /// </summary>
        public static string ProgressMachineLearningRegistryDeleteExecuting {
            get {
                return ResourceManager.GetString("ProgressMachineLearningRegistryDeleteExecuting", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deleting resource.
        /// </summary>
        public static string ProgressMachineLearningServicesDeleteExecuting {
            get {
                return ResourceManager.GetString("ProgressMachineLearningServicesDeleteExecuting", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to An error occurred while deleting the Registry name &apos;{registryName}&apos;.{lineBreak}Error details:{lineBreak}{errorMessage}..
        /// </summary>
        public static string ProgressNotificationMachineLearningRegistryDeleteError {
            get {
                return ResourceManager.GetString("ProgressNotificationMachineLearningRegistryDeleteError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Registry deletion error.
        /// </summary>
        public static string ProgressNotificationMachineLearningRegistryDeleteErrorTitle {
            get {
                return ResourceManager.GetString("ProgressNotificationMachineLearningRegistryDeleteErrorTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Registry &apos;{registryName}&apos; deletion in progress....
        /// </summary>
        public static string ProgressNotificationMachineLearningRegistryDeleteExecuting {
            get {
                return ResourceManager.GetString("ProgressNotificationMachineLearningRegistryDeleteExecuting", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deleting Registry....
        /// </summary>
        public static string ProgressNotificationMachineLearningRegistryDeleteExecutingTitle {
            get {
                return ResourceManager.GetString("ProgressNotificationMachineLearningRegistryDeleteExecutingTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Registry &apos;{registryName}&apos; was deleted successfully..
        /// </summary>
        public static string ProgressNotificationMachineLearningRegistryDeleteSuccess {
            get {
                return ResourceManager.GetString("ProgressNotificationMachineLearningRegistryDeleteSuccess", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Registry deleted.
        /// </summary>
        public static string ProgressNotificationMachineLearningRegistryDeleteSuccessTitle {
            get {
                return ResourceManager.GetString("ProgressNotificationMachineLearningRegistryDeleteSuccessTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Resource deletion canceled.
        /// </summary>
        public static string ProgressNotificationMachineLearningServicesDeleteCancelTitle {
            get {
                return ResourceManager.GetString("ProgressNotificationMachineLearningServicesDeleteCancelTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to An error occurred while deleting the Workspace name &apos;{workspaceName}&apos;.{lineBreak}Error details:{lineBreak}{errorMessage}..
        /// </summary>
        public static string ProgressNotificationMachineLearningServicesDeleteError {
            get {
                return ResourceManager.GetString("ProgressNotificationMachineLearningServicesDeleteError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Resource deletion error.
        /// </summary>
        public static string ProgressNotificationMachineLearningServicesDeleteErrorTitle {
            get {
                return ResourceManager.GetString("ProgressNotificationMachineLearningServicesDeleteErrorTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Resource &apos;{workspaceName}&apos; deletion in progress....
        /// </summary>
        public static string ProgressNotificationMachineLearningServicesDeleteExecuting {
            get {
                return ResourceManager.GetString("ProgressNotificationMachineLearningServicesDeleteExecuting", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deleting resource....
        /// </summary>
        public static string ProgressNotificationMachineLearningServicesDeleteExecutingTitle {
            get {
                return ResourceManager.GetString("ProgressNotificationMachineLearningServicesDeleteExecutingTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Resource &apos;{workspaceName}&apos; was deleted successfully..
        /// </summary>
        public static string ProgressNotificationMachineLearningServicesDeleteSuccess {
            get {
                return ResourceManager.GetString("ProgressNotificationMachineLearningServicesDeleteSuccess", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Succesfully deleted.
        /// </summary>
        public static string ProgressNotificationMachineLearningServicesDeleteSuccessTitle {
            get {
                return ResourceManager.GetString("ProgressNotificationMachineLearningServicesDeleteSuccessTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Discard changes.
        /// </summary>
        public static string Properties_Discard {
            get {
                return ResourceManager.GetString("Properties_Discard", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Refresh.
        /// </summary>
        public static string Properties_Refresh {
            get {
                return ResourceManager.GetString("Properties_Refresh", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save.
        /// </summary>
        public static string Properties_Save {
            get {
                return ResourceManager.GetString("Properties_Save", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Saving....
        /// </summary>
        public static string Properties_Saving {
            get {
                return ResourceManager.GetString("Properties_Saving", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Application Insights.
        /// </summary>
        public static string PropertiesBladeApplicationInsightsLabel {
            get {
                return ResourceManager.GetString("PropertiesBladeApplicationInsightsLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Container Registry ID.
        /// </summary>
        public static string PropertiesBladeContainerRegistryIdLabel {
            get {
                return ResourceManager.GetString("PropertiesBladeContainerRegistryIdLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Created on.
        /// </summary>
        public static string PropertiesBladeCreationDateLabel {
            get {
                return ResourceManager.GetString("PropertiesBladeCreationDateLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Key Vault ID.
        /// </summary>
        public static string PropertiesBladeKeyVaultIdLabel {
            get {
                return ResourceManager.GetString("PropertiesBladeKeyVaultIdLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Location.
        /// </summary>
        public static string PropertiesBladeLocationLabel {
            get {
                return ResourceManager.GetString("PropertiesBladeLocationLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Resource Group.
        /// </summary>
        public static string PropertiesBladeResourceGroupLabel {
            get {
                return ResourceManager.GetString("PropertiesBladeResourceGroupLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Resource ID.
        /// </summary>
        public static string PropertiesBladeResourceIdLabel {
            get {
                return ResourceManager.GetString("PropertiesBladeResourceIdLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Storage account ID.
        /// </summary>
        public static string PropertiesBladeStorageAccountIdLabel {
            get {
                return ResourceManager.GetString("PropertiesBladeStorageAccountIdLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Subscription ID.
        /// </summary>
        public static string PropertiesBladeSubscriptionIdLabel {
            get {
                return ResourceManager.GetString("PropertiesBladeSubscriptionIdLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Subscription Name.
        /// </summary>
        public static string PropertiesBladeSubscriptionNameLabel {
            get {
                return ResourceManager.GetString("PropertiesBladeSubscriptionNameLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AI hub ID.
        /// </summary>
        public static string PropertiesBladeWorkspaceHubIdLabel {
            get {
                return ResourceManager.GetString("PropertiesBladeWorkspaceHubIdLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Machine Learning workspace ID.
        /// </summary>
        public static string PropertiesBladeWorkspaceIdLabel {
            get {
                return ResourceManager.GetString("PropertiesBladeWorkspaceIdLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quick link under Overview blade..
        /// </summary>
        public static string quickLinkUnderOverviewBladeAriaLabel {
            get {
                return ResourceManager.GetString("quickLinkUnderOverviewBladeAriaLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to View quota by subscription and region, and request quota directly from the studio..
        /// </summary>
        public static string Quota_Link_BladeDescription {
            get {
                return ResourceManager.GetString("Quota_Link_BladeDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Request and view quota in Azure AI Foundry.
        /// </summary>
        public static string Quota_Link_BladeTitle {
            get {
                return ResourceManager.GetString("Quota_Link_BladeTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to View quota.
        /// </summary>
        public static string Quota_Link_Button {
            get {
                return ResourceManager.GetString("Quota_Link_Button", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Usage + quotas.
        /// </summary>
        public static string quotaBladeTitle {
            get {
                return ResourceManager.GetString("quotaBladeTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Note:.
        /// </summary>
        public static string quotaNote {
            get {
                return ResourceManager.GetString("quotaNote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your subscription {0} is not registered with the &apos;{1}&apos; resource provider. Please create a &apos;Machine Learning service&apos; workspace to auto-register and retry submitting the support request..
        /// </summary>
        public static string quotaProviderNotRegisteredErrorMsg {
            get {
                return ResourceManager.GetString("quotaProviderNotRegisteredErrorMsg", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Registering your subscription {0} with the &apos;{1}&apos; resource provider..
        /// </summary>
        public static string quotaProviderRegisteringErrorMsg {
            get {
                return ResourceManager.GetString("quotaProviderRegisteringErrorMsg", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Configure quotas.
        /// </summary>
        public static string quotaQuotaSettingTabHeader {
            get {
                return ResourceManager.GetString("quotaQuotaSettingTabHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Current vCPU limit.
        /// </summary>
        public static string quotaRequestCurrentLimit {
            get {
                return ResourceManager.GetString("quotaRequestCurrentLimit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Click to learn more about Compute (cores/vCPUs) quota increase requests..
        /// </summary>
        public static string quotaRequestDocumentationInfoBox {
            get {
                return ResourceManager.GetString("quotaRequestDocumentationInfoBox", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter the limit for any resource(s) you are requesting:.
        /// </summary>
        public static string quotaRequestHeader {
            get {
                return ResourceManager.GetString("quotaRequestHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} - current value: {1} / requested value: {2}.
        /// </summary>
        public static string quotaRequestLogOutputMessageDetail {
            get {
                return ResourceManager.GetString("quotaRequestLogOutputMessageDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New vCPU limit.
        /// </summary>
        public static string quotaRequestNewLimit {
            get {
                return ResourceManager.GetString("quotaRequestNewLimit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No quota resources found for given location.
        /// </summary>
        public static string quotaRequestNotFound {
            get {
                return ResourceManager.GetString("quotaRequestNotFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Resource name.
        /// </summary>
        public static string quotaRequestResourceName {
            get {
                return ResourceManager.GetString("quotaRequestResourceName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save and continue.
        /// </summary>
        public static string quotaRequestSubmit {
            get {
                return ResourceManager.GetString("quotaRequestSubmit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quota details.
        /// </summary>
        public static string quotaRequestTitle {
            get {
                return ResourceManager.GetString("quotaRequestTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to VM series.
        /// </summary>
        public static string quotaRequestVMFamily {
            get {
                return ResourceManager.GetString("quotaRequestVMFamily", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to For a cluster we show your currently allocated cores and maximum cores it can scale to..
        /// </summary>
        public static string quotaSubscriptionViewClusterHelptext {
            get {
                return ResourceManager.GetString("quotaSubscriptionViewClusterHelptext", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Expand each VM family to view your quota allocation and resource usage by workspace and further to view your clusters and instances..
        /// </summary>
        public static string quotaSubscriptionViewResourceNameHelptext {
            get {
                return ResourceManager.GetString("quotaSubscriptionViewResourceNameHelptext", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Subscription view.
        /// </summary>
        public static string quotaSubscriptionViewTabHeader {
            get {
                return ResourceManager.GetString("quotaSubscriptionViewTabHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to For an instance it continues to use quota even in Stopped state so you can restart it at any time..
        /// </summary>
        public static string quotaSubscriptionViewUsageHelptext {
            get {
                return ResourceManager.GetString("quotaSubscriptionViewUsageHelptext", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dedicated cores usage.
        /// </summary>
        public static string quotaTableHeaderDedicated {
            get {
                return ResourceManager.GetString("quotaTableHeaderDedicated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Low priority cores usage.
        /// </summary>
        public static string quotaTableHeaderLowPriority {
            get {
                return ResourceManager.GetString("quotaTableHeaderLowPriority", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Resource name.
        /// </summary>
        public static string quotaTableHeaderQuota {
            get {
                return ResourceManager.GetString("quotaTableHeaderQuota", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No data to display.
        /// </summary>
        public static string quotaTableNoData {
            get {
                return ResourceManager.GetString("quotaTableNoData", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The server encountered an error processing current request. Please refresh the table again..
        /// </summary>
        public static string quotaTableServerError {
            get {
                return ResourceManager.GetString("quotaTableServerError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total subscription quota.
        /// </summary>
        public static string quotaTableTotalSubscriptionQuota {
            get {
                return ResourceManager.GetString("quotaTableTotalSubscriptionQuota", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Workspace level quota cannot exceed the subscription level quota limit..
        /// </summary>
        public static string quotaWorkspaceQuotaExceedSubscriptionLimit {
            get {
                return ResourceManager.GetString("quotaWorkspaceQuotaExceedSubscriptionLimit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You are not authorized to set quota at the workspace level. Please reach out to your subscription admin to help allocate resources between workspaces..
        /// </summary>
        public static string quotaWorkspaceQuotaInsufficientPermissions {
            get {
                return ResourceManager.GetString("quotaWorkspaceQuotaInsufficientPermissions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please specify a VM family that is supported in the $region region and you have subscription level quota for..
        /// </summary>
        public static string quotaWorkspaceQuotaInvalidVMFamilyName {
            get {
                return ResourceManager.GetString("quotaWorkspaceQuotaInvalidVMFamilyName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Workspace level quota cannot be less than the cores required to support the minimum nodes of this workspace’s clusters and instances..
        /// </summary>
        public static string quotaWorkspaceQuotaLessThanMinimumClusterCores {
            get {
                return ResourceManager.GetString("quotaWorkspaceQuotaLessThanMinimumClusterCores", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allocate subscription level quota between workspaces by setting it here. To change the all-up subscription level quota, use the request quota button at the bottom. Note that you need to be a subscription owner to modify these values..
        /// </summary>
        public static string quotaWorkspaceQuotaNewLimitHelpText {
            get {
                return ResourceManager.GetString("quotaWorkspaceQuotaNewLimitHelpText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unallocated cores: {0}, Maximum: {1}.
        /// </summary>
        public static string quotaWorkspaceQuotaPlaceHolder {
            get {
                return ResourceManager.GetString("quotaWorkspaceQuotaPlaceHolder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Expand each VM size to view and allocate subscription level quota between workspaces..
        /// </summary>
        public static string quotaWorkspaceQuotaResourceNameHelpText {
            get {
                return ResourceManager.GetString("quotaWorkspaceQuotaResourceNameHelpText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unknown error.
        /// </summary>
        public static string quotaWorkspaceQuotaUnknownError {
            get {
                return ResourceManager.GetString("quotaWorkspaceQuotaUnknownError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to For a cluster we show your currently allocated cores and maximum cores it can scale to..
        /// </summary>
        public static string quotaWorkspaceViewClusterHelptext {
            get {
                return ResourceManager.GetString("quotaWorkspaceViewClusterHelptext", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Expand each workspace to view your quota allocation and resource usage by VM family and further to view your clusters and instances..
        /// </summary>
        public static string quotaWorkspaceViewResourceNameHelptext {
            get {
                return ResourceManager.GetString("quotaWorkspaceViewResourceNameHelptext", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Workspace view.
        /// </summary>
        public static string quotaWorkspaceViewTabHeader {
            get {
                return ResourceManager.GetString("quotaWorkspaceViewTabHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to For an instance it continues to use quota even in Stopped state so you can restart it at any time..
        /// </summary>
        public static string quotaWorkspaceViewUsageHelptext {
            get {
                return ResourceManager.GetString("quotaWorkspaceViewUsageHelptext", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Recently deleted.
        /// </summary>
        public static string RecentlyDeletedWorkspaces {
            get {
                return ResourceManager.GetString("RecentlyDeletedWorkspaces", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Additonal regions.
        /// </summary>
        public static string RegionsTab_AdditionRegionLabel {
            get {
                return ResourceManager.GetString("RegionsTab_AdditionRegionLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select regions in which you currently have or plan to create AzureML workspaces and use assets from this registry. You can choose to add more regions to the registry later..
        /// </summary>
        public static string RegionsTab_Description {
            get {
                return ResourceManager.GetString("RegionsTab_Description", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Regions.
        /// </summary>
        public static string RegionsTab_Title {
            get {
                return ResourceManager.GetString("RegionsTab_Title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dedicated.
        /// </summary>
        public static string RequestQuota_dedicatedSectionLabel {
            get {
                return ResourceManager.GetString("RequestQuota_dedicatedSectionLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Low priority.
        /// </summary>
        public static string RequestQuota_lowPrioritySectionLabel {
            get {
                return ResourceManager.GetString("RequestQuota_lowPrioritySectionLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dedicated.
        /// </summary>
        public static string RequestQuota_vmTypeDedicated {
            get {
                return ResourceManager.GetString("RequestQuota_vmTypeDedicated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Low priority.
        /// </summary>
        public static string RequestQuota_vmTypeLowPriority {
            get {
                return ResourceManager.GetString("RequestQuota_vmTypeLowPriority", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to VM Types.
        /// </summary>
        public static string RequestQuota_vmTypesLabel {
            get {
                return ResourceManager.GetString("RequestQuota_vmTypesLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Location.
        /// </summary>
        public static string ResourceLocationColumn {
            get {
                return ResourceManager.GetString("ResourceLocationColumn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The resource name is required.
        /// </summary>
        public static string ResourceNameRequired {
            get {
                return ResourceManager.GetString("ResourceNameRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Scaling.
        /// </summary>
        public static string Scaling {
            get {
                return ResourceManager.GetString("Scaling", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} (SKU: {1}) - Standard SKU is needed at the minimum.
        /// </summary>
        public static string SearchService_Dropdown_standardSupportedFormat {
            get {
                return ResourceManager.GetString("SearchService_Dropdown_standardSupportedFormat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Security.
        /// </summary>
        public static string Security_menuText {
            get {
                return ResourceManager.GetString("Security_menuText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select a location.
        /// </summary>
        public static string SelectLocation {
            get {
                return ResourceManager.GetString("SelectLocation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select a subscription.
        /// </summary>
        public static string SelectSubscription {
            get {
                return ResourceManager.GetString("SelectSubscription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Validation failed for the following tab: {0}. Required information is missing or not valid..
        /// </summary>
        public static string singleInvalidTabErrorMessage {
            get {
                return ResourceManager.GetString("singleInvalidTabErrorMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AI Foundry resources.
        /// </summary>
        public static string SoftDeletedWorkspace_Blade_AIStudio_ToolBar_Header {
            get {
                return ResourceManager.GetString("SoftDeletedWorkspace_Blade_AIStudio_ToolBar_Header", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error loading soft deleted resources.
        /// </summary>
        public static string SoftDeletedWorkspace_Blade_ErrorMessage {
            get {
                return ResourceManager.GetString("SoftDeletedWorkspace_Blade_ErrorMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cancel.
        /// </summary>
        public static string SoftDeletedWorkspace_Blade_Footer_Buttons_Cancel {
            get {
                return ResourceManager.GetString("SoftDeletedWorkspace_Blade_Footer_Buttons_Cancel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Permanently delete.
        /// </summary>
        public static string SoftDeletedWorkspace_Blade_Footer_Buttons_Purge {
            get {
                return ResourceManager.GetString("SoftDeletedWorkspace_Blade_Footer_Buttons_Purge", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Recover.
        /// </summary>
        public static string SoftDeletedWorkspace_Blade_Footer_Buttons_Recover {
            get {
                return ResourceManager.GetString("SoftDeletedWorkspace_Blade_Footer_Buttons_Recover", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Loading....
        /// </summary>
        public static string SoftDeletedWorkspace_Blade_LoadingText {
            get {
                return ResourceManager.GetString("SoftDeletedWorkspace_Blade_LoadingText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Recover or permanently delete resources.
        /// </summary>
        public static string SoftDeletedWorkspace_Blade_SubTitle {
            get {
                return ResourceManager.GetString("SoftDeletedWorkspace_Blade_SubTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Recently deleted resources.
        /// </summary>
        public static string SoftDeletedWorkspace_Blade_Title {
            get {
                return ResourceManager.GetString("SoftDeletedWorkspace_Blade_Title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Refresh.
        /// </summary>
        public static string SoftDeletedWorkspace_Blade_ToolBar_Buttons_Refresh {
            get {
                return ResourceManager.GetString("SoftDeletedWorkspace_Blade_ToolBar_Buttons_Refresh", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Machine Learning workspaces.
        /// </summary>
        public static string SoftDeletedWorkspace_Blade_ToolBar_Header {
            get {
                return ResourceManager.GetString("SoftDeletedWorkspace_Blade_ToolBar_Header", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cancel.
        /// </summary>
        public static string SoftDeletedWorkspace_DeleteBlade_FormControls_Buttons_cancel {
            get {
                return ResourceManager.GetString("SoftDeletedWorkspace_DeleteBlade_FormControls_Buttons_cancel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete.
        /// </summary>
        public static string SoftDeletedWorkspace_DeleteBlade_FormControls_Buttons_delete {
            get {
                return ResourceManager.GetString("SoftDeletedWorkspace_DeleteBlade_FormControls_Buttons_delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Name does not match.
        /// </summary>
        public static string SoftDeletedWorkspace_DeleteBlade_FormControls_Confirm_errorMessage {
            get {
                return ResourceManager.GetString("SoftDeletedWorkspace_DeleteBlade_FormControls_Confirm_errorMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Confirm delete.
        /// </summary>
        public static string SoftDeletedWorkspace_DeleteBlade_FormControls_Confirm_label {
            get {
                return ResourceManager.GetString("SoftDeletedWorkspace_DeleteBlade_FormControls_Confirm_label", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Type the resource name.
        /// </summary>
        public static string SoftDeletedWorkspace_DeleteBlade_FormControls_Confirm_placeholder {
            get {
                return ResourceManager.GetString("SoftDeletedWorkspace_DeleteBlade_FormControls_Confirm_placeholder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete this resource permanently.
        /// </summary>
        public static string SoftDeletedWorkspace_DeleteBlade_FormControls_PermanentDelete_checkbox {
            get {
                return ResourceManager.GetString("SoftDeletedWorkspace_DeleteBlade_FormControls_PermanentDelete_checkbox", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Permanently delete.
        /// </summary>
        public static string SoftDeletedWorkspace_DeleteBlade_FormControls_PermanentDelete_header {
            get {
                return ResourceManager.GetString("SoftDeletedWorkspace_DeleteBlade_FormControls_PermanentDelete_header", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Permanently delete this resource &quot;{0}&quot;?.
        /// </summary>
        public static string SoftDeletedWorkspace_DeleteBlade_Message_AIStudio_text {
            get {
                return ResourceManager.GetString("SoftDeletedWorkspace_DeleteBlade_Message_AIStudio_text", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This resource uses a customer-managed key (CMK) for data encryption. When soft-deleted, dependent resources for storing data will not be deleted and will incur cost until this resource is hard-deleted. {0}.
        /// </summary>
        public static string SoftDeletedWorkspace_DeleteBlade_Message_CMKtext {
            get {
                return ResourceManager.GetString("SoftDeletedWorkspace_DeleteBlade_Message_CMKtext", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Learn more.
        /// </summary>
        public static string SoftDeletedWorkspace_DeleteBlade_Message_link {
            get {
                return ResourceManager.GetString("SoftDeletedWorkspace_DeleteBlade_Message_link", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to When you delete this resource, it is temporarily placed in a ‘soft-delete’ state that allows you to recover it. Deletion of your data is postponed until your resource is permanently deleted by you, or until the expiry of the soft-delete data retention period of fourteen (14) days. You can override the soft-delete behavior and permanently delete your resource immediately. {0}.
        /// </summary>
        public static string SoftDeletedWorkspace_DeleteBlade_Message_text {
            get {
                return ResourceManager.GetString("SoftDeletedWorkspace_DeleteBlade_Message_text", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete resource.
        /// </summary>
        public static string SoftDeletedWorkspace_DeleteBlade_Title {
            get {
                return ResourceManager.GetString("SoftDeletedWorkspace_DeleteBlade_Title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deleted resources grid.
        /// </summary>
        public static string SoftDeletedWorkspace_Grid_AriaLabel {
            get {
                return ResourceManager.GetString("SoftDeletedWorkspace_Grid_AriaLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deleted date.
        /// </summary>
        public static string SoftDeletedWorkspace_Grid_Columns_DeletedDate {
            get {
                return ResourceManager.GetString("SoftDeletedWorkspace_Grid_Columns_DeletedDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Name.
        /// </summary>
        public static string SoftDeletedWorkspace_Grid_Columns_Name {
            get {
                return ResourceManager.GetString("SoftDeletedWorkspace_Grid_Columns_Name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Scheduled permanent deletion date.
        /// </summary>
        public static string SoftDeletedWorkspace_Grid_Columns_PurgeDate {
            get {
                return ResourceManager.GetString("SoftDeletedWorkspace_Grid_Columns_PurgeDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Resource group.
        /// </summary>
        public static string SoftDeletedWorkspace_Grid_Columns_ResourceGroup {
            get {
                return ResourceManager.GetString("SoftDeletedWorkspace_Grid_Columns_ResourceGroup", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No resources found to display.
        /// </summary>
        public static string SoftDeletedWorkspace_Grid_NoWorkspacesFound {
            get {
                return ResourceManager.GetString("SoftDeletedWorkspace_Grid_NoWorkspacesFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The soft delete feature has been enabled on this resource. After you soft delete,  this resource data remains available. It will get purged after the retention period. You may purge it sooner, or recover the resource.
        /// </summary>
        public static string SoftDeletedWorkspace_OverviewBlade_DeleteMessage {
            get {
                return ResourceManager.GetString("SoftDeletedWorkspace_OverviewBlade_DeleteMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to There was an error processing your request. Try again in a few moments.
        /// </summary>
        public static string SoftDeletedWorkspace_WorkspacesBlade_Commands_Generic_Error_Message {
            get {
                return ResourceManager.GetString("SoftDeletedWorkspace_WorkspacesBlade_Commands_Generic_Error_Message", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed to permanently delete {0} out of {1} resources.
        /// </summary>
        public static string SoftDeletedWorkspace_WorkspacesBlade_Commands_PurgeNotifications_Failure_message {
            get {
                return ResourceManager.GetString("SoftDeletedWorkspace_WorkspacesBlade_Commands_PurgeNotifications_Failure_message", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed to delete resources.
        /// </summary>
        public static string SoftDeletedWorkspace_WorkspacesBlade_Commands_PurgeNotifications_Failure_title {
            get {
                return ResourceManager.GetString("SoftDeletedWorkspace_WorkspacesBlade_Commands_PurgeNotifications_Failure_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Permanently deleting {0} resources.
        /// </summary>
        public static string SoftDeletedWorkspace_WorkspacesBlade_Commands_PurgeNotifications_InProgress_message {
            get {
                return ResourceManager.GetString("SoftDeletedWorkspace_WorkspacesBlade_Commands_PurgeNotifications_InProgress_messa" +
                        "ge", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deleting resources ....
        /// </summary>
        public static string SoftDeletedWorkspace_WorkspacesBlade_Commands_PurgeNotifications_InProgress_title {
            get {
                return ResourceManager.GetString("SoftDeletedWorkspace_WorkspacesBlade_Commands_PurgeNotifications_InProgress_title" +
                        "", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Successfully deleted {0} resource(s) permanently.
        /// </summary>
        public static string SoftDeletedWorkspace_WorkspacesBlade_Commands_PurgeNotifications_Success_message {
            get {
                return ResourceManager.GetString("SoftDeletedWorkspace_WorkspacesBlade_Commands_PurgeNotifications_Success_message", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Successfully deleted resources.
        /// </summary>
        public static string SoftDeletedWorkspace_WorkspacesBlade_Commands_PurgeNotifications_Success_title {
            get {
                return ResourceManager.GetString("SoftDeletedWorkspace_WorkspacesBlade_Commands_PurgeNotifications_Success_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed to recover {0} out of {1} resources.
        /// </summary>
        public static string SoftDeletedWorkspace_WorkspacesBlade_Commands_RecoverNotifications_Failure_message {
            get {
                return ResourceManager.GetString("SoftDeletedWorkspace_WorkspacesBlade_Commands_RecoverNotifications_Failure_messag" +
                        "e", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed to recover resources.
        /// </summary>
        public static string SoftDeletedWorkspace_WorkspacesBlade_Commands_RecoverNotifications_Failure_title {
            get {
                return ResourceManager.GetString("SoftDeletedWorkspace_WorkspacesBlade_Commands_RecoverNotifications_Failure_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Recovering {0} resource(s).
        /// </summary>
        public static string SoftDeletedWorkspace_WorkspacesBlade_Commands_RecoverNotifications_InProgress_message {
            get {
                return ResourceManager.GetString("SoftDeletedWorkspace_WorkspacesBlade_Commands_RecoverNotifications_InProgress_mes" +
                        "sage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Recovering resources ....
        /// </summary>
        public static string SoftDeletedWorkspace_WorkspacesBlade_Commands_RecoverNotifications_InProgress_title {
            get {
                return ResourceManager.GetString("SoftDeletedWorkspace_WorkspacesBlade_Commands_RecoverNotifications_InProgress_tit" +
                        "le", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Successfully recovered {0} resource(s).
        /// </summary>
        public static string SoftDeletedWorkspace_WorkspacesBlade_Commands_RecoverNotifications_Success_message {
            get {
                return ResourceManager.GetString("SoftDeletedWorkspace_WorkspacesBlade_Commands_RecoverNotifications_Success_messag" +
                        "e", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Successfully recovered resources.
        /// </summary>
        public static string SoftDeletedWorkspace_WorkspacesBlade_Commands_RecoverNotifications_Success_title {
            get {
                return ResourceManager.GetString("SoftDeletedWorkspace_WorkspacesBlade_Commands_RecoverNotifications_Success_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Recovery error: the associated Application Insights resource &quot;{0}&quot; could not be found. It may have been deleted. Recover or recreate the application insights resource under its former name &quot;{0}&quot;. You may restore your application insights data by recovering the associated log analytics workspace first.&quot;.
        /// </summary>
        public static string SoftDeletedWorkspace_WorkspacesBlade_Commands_ValidateResources_AppInsights_Failure_message {
            get {
                return ResourceManager.GetString("SoftDeletedWorkspace_WorkspacesBlade_Commands_ValidateResources_AppInsights_Failu" +
                        "re_message", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Recovery error: the associated Container Registry resource &quot;{0}&quot; could not be found. A soft-deleted cannot be recovered without a Container Registry as a dependency..
        /// </summary>
        public static string SoftDeletedWorkspace_WorkspacesBlade_Commands_ValidateResources_ContainerRegistry_Failure_message {
            get {
                return ResourceManager.GetString("SoftDeletedWorkspace_WorkspacesBlade_Commands_ValidateResources_ContainerRegistry" +
                        "_Failure_message", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed to validate resource {0}.
        /// </summary>
        public static string SoftDeletedWorkspace_WorkspacesBlade_Commands_ValidateResources_Failure_title {
            get {
                return ResourceManager.GetString("SoftDeletedWorkspace_WorkspacesBlade_Commands_ValidateResources_Failure_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Validating {0} resources for {1}.
        /// </summary>
        public static string SoftDeletedWorkspace_WorkspacesBlade_Commands_ValidateResources_InProgress_message {
            get {
                return ResourceManager.GetString("SoftDeletedWorkspace_WorkspacesBlade_Commands_ValidateResources_InProgress_messag" +
                        "e", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Validating resources ....
        /// </summary>
        public static string SoftDeletedWorkspace_WorkspacesBlade_Commands_ValidateResources_InProgress_title {
            get {
                return ResourceManager.GetString("SoftDeletedWorkspace_WorkspacesBlade_Commands_ValidateResources_InProgress_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Recovery error: the associated Azure Key Vault resource &quot;{0}&quot; could not be found. A soft-deleted resource cannot be recovered without its previously attached Key Vault. The required Azure Key Vault may still be recoverable, see `Manage deleted vaults`..
        /// </summary>
        public static string SoftDeletedWorkspace_WorkspacesBlade_Commands_ValidateResources_KeyVault_Failure_message {
            get {
                return ResourceManager.GetString("SoftDeletedWorkspace_WorkspacesBlade_Commands_ValidateResources_KeyVault_Failure_" +
                        "message", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Recovery error: the associated Storage Account resource &quot;{0}&quot; could not be found. If the storage account was previously soft-deleted, recover it first before recovering this resource..
        /// </summary>
        public static string SoftDeletedWorkspace_WorkspacesBlade_Commands_ValidateResources_StorageAccount_Failure_message {
            get {
                return ResourceManager.GetString("SoftDeletedWorkspace_WorkspacesBlade_Commands_ValidateResources_StorageAccount_Fa" +
                        "ilure_message", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Successfully validated resources for {0}.
        /// </summary>
        public static string SoftDeletedWorkspace_WorkspacesBlade_Commands_ValidateResources_Success_message {
            get {
                return ResourceManager.GetString("SoftDeletedWorkspace_WorkspacesBlade_Commands_ValidateResources_Success_message", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Successfully validated.
        /// </summary>
        public static string SoftDeletedWorkspace_WorkspacesBlade_Commands_ValidateResources_Success_title {
            get {
                return ResourceManager.GetString("SoftDeletedWorkspace_WorkspacesBlade_Commands_ValidateResources_Success_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create new storage account.
        /// </summary>
        public static string StorageAccount_Dropdown_createNewAriaLabel {
            get {
                return ResourceManager.GetString("StorageAccount_Dropdown_createNewAriaLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to A storage account is used as the default datastore for the AI hub. You may create a new Azure Storage resource or select an existing one in your subscription..
        /// </summary>
        public static string StorageAccount_Dropdown_Hub_info {
            get {
                return ResourceManager.GetString("StorageAccount_Dropdown_Hub_info", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to A storage account is used as the default datastore for the workspace. You may create a new Azure Storage resource or select an existing one in your subscription..
        /// </summary>
        public static string StorageAccount_Dropdown_info {
            get {
                return ResourceManager.GetString("StorageAccount_Dropdown_info", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Storage account.
        /// </summary>
        public static string StorageAccount_Dropdown_label {
            get {
                return ResourceManager.GetString("StorageAccount_Dropdown_label", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enable hierarchical namespace (preview).
        /// </summary>
        public static string StorageAccount_Dropdown_SettingsBlade_hnsCheckLabel {
            get {
                return ResourceManager.GetString("StorageAccount_Dropdown_SettingsBlade_hnsCheckLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Data Lake Storage Gen2 hierarchical namespace accelerates big data analytics workloads, enables faster and more reliable file operations, and enables file-level access control lists (ACLs). {0}.
        /// </summary>
        public static string StorageAccount_Dropdown_SettingsBlade_hnsDescriptionText {
            get {
                return ResourceManager.GetString("StorageAccount_Dropdown_SettingsBlade_hnsDescriptionText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Data Lake Storage Gen2 (preview).
        /// </summary>
        public static string StorageAccount_Dropdown_SettingsBlade_hnsDescriptionTitle {
            get {
                return ResourceManager.GetString("StorageAccount_Dropdown_SettingsBlade_hnsDescriptionTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} - Hierarchical namespace is not supported.
        /// </summary>
        public static string StorageAccount_Dropdown_SettingsBlade_hnsNotSupportedFormat {
            get {
                return ResourceManager.GetString("StorageAccount_Dropdown_SettingsBlade_hnsNotSupportedFormat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Learn more.
        /// </summary>
        public static string StorageAccount_Dropdown_SettingsBlade_learnMoreText {
            get {
                return ResourceManager.GetString("StorageAccount_Dropdown_SettingsBlade_learnMoreText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The name must meet the following requirements:.
        /// </summary>
        public static string StorageAccount_Dropdown_SettingsBlade_Name_infoDescription {
            get {
                return ResourceManager.GetString("StorageAccount_Dropdown_SettingsBlade_Name_infoDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unique across all existing storage account names in Azure.
        /// </summary>
        public static string StorageAccount_Dropdown_SettingsBlade_Name_infoItem1 {
            get {
                return ResourceManager.GetString("StorageAccount_Dropdown_SettingsBlade_Name_infoItem1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Between 3 and 24 characters long.
        /// </summary>
        public static string StorageAccount_Dropdown_SettingsBlade_Name_infoItem2 {
            get {
                return ResourceManager.GetString("StorageAccount_Dropdown_SettingsBlade_Name_infoItem2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Only contain lowercase letters and numbers.
        /// </summary>
        public static string StorageAccount_Dropdown_SettingsBlade_Name_infoItem3 {
            get {
                return ResourceManager.GetString("StorageAccount_Dropdown_SettingsBlade_Name_infoItem3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Storage name must be between 3 and 24 characters and may only contain lowercase letters and numbers..
        /// </summary>
        public static string StorageAccount_Dropdown_SettingsBlade_nameInvalidMessage {
            get {
                return ResourceManager.GetString("StorageAccount_Dropdown_SettingsBlade_nameInvalidMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Name.
        /// </summary>
        public static string StorageAccount_Dropdown_SettingsBlade_nameLabel {
            get {
                return ResourceManager.GetString("StorageAccount_Dropdown_SettingsBlade_nameLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Standard storage accounts are backed by magnetic drives and provide the lowest cost per GB. Premium storage accounts are backed by solid state drives and offer consistent, low-latency performance..
        /// </summary>
        public static string StorageAccount_Dropdown_SettingsBlade_Performance_info {
            get {
                return ResourceManager.GetString("StorageAccount_Dropdown_SettingsBlade_Performance_info", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Performance.
        /// </summary>
        public static string StorageAccount_Dropdown_SettingsBlade_performanceLabel {
            get {
                return ResourceManager.GetString("StorageAccount_Dropdown_SettingsBlade_performanceLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Premium.
        /// </summary>
        public static string StorageAccount_Dropdown_SettingsBlade_performancePremium {
            get {
                return ResourceManager.GetString("StorageAccount_Dropdown_SettingsBlade_performancePremium", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Standard.
        /// </summary>
        public static string StorageAccount_Dropdown_SettingsBlade_performanceStandard {
            get {
                return ResourceManager.GetString("StorageAccount_Dropdown_SettingsBlade_performanceStandard", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} - Premium storage account is not supported.
        /// </summary>
        public static string StorageAccount_Dropdown_SettingsBlade_premiumNotSupportedFormat {
            get {
                return ResourceManager.GetString("StorageAccount_Dropdown_SettingsBlade_premiumNotSupportedFormat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The data in your storage account is always replicated to ensure durability and high availability. Choose a replication strategy that best matches you requirements..
        /// </summary>
        public static string StorageAccount_Dropdown_SettingsBlade_Replication_info {
            get {
                return ResourceManager.GetString("StorageAccount_Dropdown_SettingsBlade_Replication_info", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Learn more.
        /// </summary>
        public static string StorageAccount_Dropdown_SettingsBlade_Replication_infoLearnMore {
            get {
                return ResourceManager.GetString("StorageAccount_Dropdown_SettingsBlade_Replication_infoLearnMore", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Geo-redundant storage (GRS).
        /// </summary>
        public static string StorageAccount_Dropdown_SettingsBlade_replicationGRS {
            get {
                return ResourceManager.GetString("StorageAccount_Dropdown_SettingsBlade_replicationGRS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Geo-zone-redundant storage (GZRS).
        /// </summary>
        public static string StorageAccount_Dropdown_SettingsBlade_replicationGZRS {
            get {
                return ResourceManager.GetString("StorageAccount_Dropdown_SettingsBlade_replicationGZRS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Replication.
        /// </summary>
        public static string StorageAccount_Dropdown_SettingsBlade_replicationLabel {
            get {
                return ResourceManager.GetString("StorageAccount_Dropdown_SettingsBlade_replicationLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Locally-redundant storage (LRS).
        /// </summary>
        public static string StorageAccount_Dropdown_SettingsBlade_replicationLRS {
            get {
                return ResourceManager.GetString("StorageAccount_Dropdown_SettingsBlade_replicationLRS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Read-access geo-redundant storage (RA-GRS).
        /// </summary>
        public static string StorageAccount_Dropdown_SettingsBlade_replicationRAGRS {
            get {
                return ResourceManager.GetString("StorageAccount_Dropdown_SettingsBlade_replicationRAGRS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Read-access geo-zone-redundant storage (RA-GZRS).
        /// </summary>
        public static string StorageAccount_Dropdown_SettingsBlade_replicationRAGZRS {
            get {
                return ResourceManager.GetString("StorageAccount_Dropdown_SettingsBlade_replicationRAGZRS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Zone-redundant storage (ZRS).
        /// </summary>
        public static string StorageAccount_Dropdown_SettingsBlade_replicationZRS {
            get {
                return ResourceManager.GetString("StorageAccount_Dropdown_SettingsBlade_replicationZRS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create new storage account.
        /// </summary>
        public static string StorageAccount_Dropdown_SettingsBlade_title {
            get {
                return ResourceManager.GetString("StorageAccount_Dropdown_SettingsBlade_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Storage account.
        /// </summary>
        public static string StorageAccount_SummaryTab_label {
            get {
                return ResourceManager.GetString("StorageAccount_SummaryTab_label", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to As users work in Azure AI Foundry, uploaded data, stored credentials and generated artifacts like logs are stored..
        /// </summary>
        public static string StorageTab_Description {
            get {
                return ResourceManager.GetString("StorageTab_Description", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Configure how your data is stored.
        /// </summary>
        public static string StorageTab_Header {
            get {
                return ResourceManager.GetString("StorageTab_Header", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Logs and docker images.
        /// </summary>
        public static string StorageTab_LogsAndDockerHeader {
            get {
                return ResourceManager.GetString("StorageTab_LogsAndDockerHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Storage.
        /// </summary>
        public static string StorageTab_Title {
            get {
                return ResourceManager.GetString("StorageTab_Title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Configure compute for deploying and managing models.
        /// </summary>
        public static string subtitleEnvironmentInfoBlade {
            get {
                return ResourceManager.GetString("subtitleEnvironmentInfoBlade", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to System assigned.
        /// </summary>
        public static string SummaryTab_Advanced_ManagedIdentity_systemAssigned {
            get {
                return ResourceManager.GetString("SummaryTab_Advanced_ManagedIdentity_systemAssigned", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Identity type.
        /// </summary>
        public static string SummaryTab_Advanced_ManagedIdentity_typeText {
            get {
                return ResourceManager.GetString("SummaryTab_Advanced_ManagedIdentity_typeText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User assigned.
        /// </summary>
        public static string SummaryTab_Advanced_ManagedIdentity_userAssigned {
            get {
                return ResourceManager.GetString("SummaryTab_Advanced_ManagedIdentity_userAssigned", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User identity name.
        /// </summary>
        public static string SummaryTab_Advanced_ManagedIdentity_userIdentityName {
            get {
                return ResourceManager.GetString("SummaryTab_Advanced_ManagedIdentity_userIdentityName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User identity resource group.
        /// </summary>
        public static string SummaryTab_Advanced_ManagedIdentity_userIdentityResourceGroup {
            get {
                return ResourceManager.GetString("SummaryTab_Advanced_ManagedIdentity_userIdentityResourceGroup", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error creating resource group when creating default log analytics workspace: {0}.
        /// </summary>
        public static string SummaryTab_createResourceGroupErrorText {
            get {
                return ResourceManager.GetString("SummaryTab_createResourceGroupErrorText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error getting log workspaces for creating default log analytics workspace: {0}.
        /// </summary>
        public static string SummaryTab_gettingLogWorkspacesErrorText {
            get {
                return ResourceManager.GetString("SummaryTab_gettingLogWorkspacesErrorText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} ({1}).
        /// </summary>
        public static string SummaryTab_Resource_existingFormat {
            get {
                return ResourceManager.GetString("SummaryTab_Resource_existingFormat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Microsoft-managed.
        /// </summary>
        public static string SummaryTab_Resource_microsoftManaged {
            get {
                return ResourceManager.GetString("SummaryTab_Resource_microsoftManaged", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to (new) {0}.
        /// </summary>
        public static string SummaryTab_Resource_newFormat {
            get {
                return ResourceManager.GetString("SummaryTab_Resource_newFormat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to None.
        /// </summary>
        public static string SummaryTab_Resource_none {
            get {
                return ResourceManager.GetString("SummaryTab_Resource_none", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Review + create.
        /// </summary>
        public static string summaryTabTitle {
            get {
                return ResourceManager.GetString("summaryTabTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Validation failed. Required information is missing or not valid..
        /// </summary>
        public static string tabValidationErrors {
            get {
                return ResourceManager.GetString("tabValidationErrors", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tags.
        /// </summary>
        public static string tags {
            get {
                return ResourceManager.GetString("tags", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create New.
        /// </summary>
        public static string textAzureDatabricksOptionCreateNew {
            get {
                return ResourceManager.GetString("textAzureDatabricksOptionCreateNew", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Use Existing.
        /// </summary>
        public static string textAzureDatabricksOptionUseExisting {
            get {
                return ResourceManager.GetString("textAzureDatabricksOptionUseExisting", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Explore your Machine Learning workspace to run and track experiments, compare model performance, and deploy models..
        /// </summary>
        public static string TextLaunchWebWorkspace {
            get {
                return ResourceManager.GetString("TextLaunchWebWorkspace", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Explore your Azure Machine Learning workspace.
        /// </summary>
        public static string TextLaunchWebWorkspaceHeader {
            get {
                return ResourceManager.GetString("TextLaunchWebWorkspaceHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Automatically create a model from your existing data..
        /// </summary>
        public static string textMachineLearningServicesAutoMLLinkBody {
            get {
                return ResourceManager.GetString("textMachineLearningServicesAutoMLLinkBody", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create a new Automated Machine Learning Model (Preview).
        /// </summary>
        public static string textMachineLearningServicesAutoMLLinkHeader {
            get {
                return ResourceManager.GetString("textMachineLearningServicesAutoMLLinkHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Learn how to use Azure Machine Learning..
        /// </summary>
        public static string TextMachineLearningServicesDocumentationLinkBody {
            get {
                return ResourceManager.GetString("TextMachineLearningServicesDocumentationLinkBody", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to View Documentation.
        /// </summary>
        public static string TextMachineLearningServicesDocumentationLinkHeader {
            get {
                return ResourceManager.GetString("TextMachineLearningServicesDocumentationLinkHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Join the discussion of Azure Machine Learning. .
        /// </summary>
        public static string TextMachineLearningServicesForumLinkBody {
            get {
                return ResourceManager.GetString("TextMachineLearningServicesForumLinkBody", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to View Forum.
        /// </summary>
        public static string TextMachineLearningServicesForumLinkHeader {
            get {
                return ResourceManager.GetString("TextMachineLearningServicesForumLinkHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create and Manage.
        /// </summary>
        public static string TextMachineLearningServicesManageFeatoreStoreLinkHeader {
            get {
                return ResourceManager.GetString("TextMachineLearningServicesManageFeatoreStoreLinkHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Learn how to create and manage Machine Learning registries..
        /// </summary>
        public static string TextMachineLearningServicesManageFeatureStoreLinkBody {
            get {
                return ResourceManager.GetString("TextMachineLearningServicesManageFeatureStoreLinkBody", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Learn how to create and manage Machine Learning registries..
        /// </summary>
        public static string TextMachineLearningServicesManageRegistryLinkBody {
            get {
                return ResourceManager.GetString("TextMachineLearningServicesManageRegistryLinkBody", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create and Manage.
        /// </summary>
        public static string TextMachineLearningServicesManageRegistryLinkHeader {
            get {
                return ResourceManager.GetString("TextMachineLearningServicesManageRegistryLinkHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quickly get started with the Python SDK and run sample experiments with Azure Machine Learning Notebook VMs..
        /// </summary>
        public static string textMachineLearningServicesNotebookVMLinkBody {
            get {
                return ResourceManager.GetString("textMachineLearningServicesNotebookVMLinkBody", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Get Started with Sample Notebooks (Preview).
        /// </summary>
        public static string textMachineLearningServicesNotebookVMLinkHeader {
            get {
                return ResourceManager.GetString("textMachineLearningServicesNotebookVMLinkHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Learn how to share Machine Learning assets using registries..
        /// </summary>
        public static string TextMachineLearningServicesShareRegistryLinkBody {
            get {
                return ResourceManager.GetString("TextMachineLearningServicesShareRegistryLinkBody", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Share models, components and environments.
        /// </summary>
        public static string TextMachineLearningServicesShareRegistryLinkHeader {
            get {
                return ResourceManager.GetString("TextMachineLearningServicesShareRegistryLinkHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Drag and drop existing components to create new models..
        /// </summary>
        public static string textMachineLearningServicesVisualInterfaceLinkBody {
            get {
                return ResourceManager.GetString("textMachineLearningServicesVisualInterfaceLinkBody", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Build a model using the Visual Interface (Preview).
        /// </summary>
        public static string textMachineLearningServicesVisualInterfaceLinkHeader {
            get {
                return ResourceManager.GetString("textMachineLearningServicesVisualInterfaceLinkHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to N/A.
        /// </summary>
        public static string textNotAvailable {
            get {
                return ResourceManager.GetString("textNotAvailable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Get inspired by a large collection of machine learning examples..
        /// </summary>
        public static string TextViennaGitHubLinkBody {
            get {
                return ResourceManager.GetString("TextViennaGitHubLinkBody", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to View more samples at GitHub.
        /// </summary>
        public static string TextViennaGitHubLinkHeader {
            get {
                return ResourceManager.GetString("TextViennaGitHubLinkHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Assets.
        /// </summary>
        public static string titleAssetsGroup {
            get {
                return ResourceManager.GetString("titleAssetsGroup", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Authoring (Preview).
        /// </summary>
        public static string titleAuthoringGroup {
            get {
                return ResourceManager.GetString("titleAuthoringGroup", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deployments.
        /// </summary>
        public static string titleDeployments {
            get {
                return ResourceManager.GetString("titleDeployments", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Machine Learning Compute.
        /// </summary>
        public static string titleEnvironmentInfoBlade {
            get {
                return ResourceManager.GetString("titleEnvironmentInfoBlade", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error.
        /// </summary>
        public static string TitleError {
            get {
                return ResourceManager.GetString("TitleError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Give feedback.
        /// </summary>
        public static string TitleGiveFeedback {
            get {
                return ResourceManager.GetString("TitleGiveFeedback", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Insights (preview).
        /// </summary>
        public static string titleInsights {
            get {
                return ResourceManager.GetString("titleInsights", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Workspace.
        /// </summary>
        public static string TitleMachineLearningServicesDeleteConfirmationMessageBox {
            get {
                return ResourceManager.GetString("TitleMachineLearningServicesDeleteConfirmationMessageBox", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Registry.
        /// </summary>
        public static string TitleMachineLearningServicesRegistryDeleteConfirmationMessageBox {
            get {
                return ResourceManager.GetString("TitleMachineLearningServicesRegistryDeleteConfirmationMessageBox", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Getting Started.
        /// </summary>
        public static string TitleMonitoringLens {
            get {
                return ResourceManager.GetString("TitleMonitoringLens", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Settings.
        /// </summary>
        public static string titleSettings {
            get {
                return ResourceManager.GetString("titleSettings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Support + troubleshooting.
        /// </summary>
        public static string titleSupport {
            get {
                return ResourceManager.GetString("titleSupport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Machine Learning.
        /// </summary>
        public static string TitleWebWorkspaceBlade {
            get {
                return ResourceManager.GetString("TitleWebWorkspaceBlade", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Feedback.
        /// </summary>
        public static string Toolbar_feedback {
            get {
                return ResourceManager.GetString("Toolbar_feedback", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This AI Services name already exists.
        /// </summary>
        public static string ValidationAIServicesNameAlreadyInUse {
            get {
                return ResourceManager.GetString("ValidationAIServicesNameAlreadyInUse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This application insights name already exists.
        /// </summary>
        public static string ValidationAppInsightsNameAlreadyInUse {
            get {
                return ResourceManager.GetString("ValidationAppInsightsNameAlreadyInUse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This container registry name already exists.
        /// </summary>
        public static string ValidationContainerRegistryNameAlreadyInUse {
            get {
                return ResourceManager.GetString("ValidationContainerRegistryNameAlreadyInUse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You don&apos;t have the required permissions ({0}) to create an account under the selected resource group.
        /// </summary>
        public static string ValidationCreateWorkspacePermission {
            get {
                return ResourceManager.GetString("ValidationCreateWorkspacePermission", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dependent resources with this name already exist.
        /// </summary>
        public static string ValidationDependentResourcesAlreadyInUse {
            get {
                return ResourceManager.GetString("ValidationDependentResourcesAlreadyInUse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to There was an error while attempting to validate the resource.
        /// </summary>
        public static string ValidationError {
            get {
                return ResourceManager.GetString("ValidationError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This AI hub name already exists.
        /// </summary>
        public static string ValidationHubNameAlreadyInUse {
            get {
                return ResourceManager.GetString("ValidationHubNameAlreadyInUse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This key vault name already exists.
        /// </summary>
        public static string ValidationKeyVaultNameAlreadyInUse {
            get {
                return ResourceManager.GetString("ValidationKeyVaultNameAlreadyInUse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please ensure the primary region is one of the selected values.
        /// </summary>
        public static string ValidationPrimaryRegionNotSelected {
            get {
                return ResourceManager.GetString("ValidationPrimaryRegionNotSelected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This AI project name already exists.
        /// </summary>
        public static string ValidationProjectNameAlreadyInUse {
            get {
                return ResourceManager.GetString("ValidationProjectNameAlreadyInUse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select at least one region.
        /// </summary>
        public static string ValidationRegionNotSelected {
            get {
                return ResourceManager.GetString("ValidationRegionNotSelected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This registry description has a maximum length of 256 characters..
        /// </summary>
        public static string ValidationRegistryDescriptionTooLarge {
            get {
                return ResourceManager.GetString("ValidationRegistryDescriptionTooLarge", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This registry name already exists.
        /// </summary>
        public static string ValidationRegistryNameAlreadyInUse {
            get {
                return ResourceManager.GetString("ValidationRegistryNameAlreadyInUse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Registry name must be between 3 and 33 characters long. Its first character has to be alphanumeric, and the rest may contain hyphens and underscores. No whitespace is allowed..
        /// </summary>
        public static string ValidationRegistryNameInvalid {
            get {
                return ResourceManager.GetString("ValidationRegistryNameInvalid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This storage account name already exists.
        /// </summary>
        public static string ValidationStorageAccountNameAlreadyInUse {
            get {
                return ResourceManager.GetString("ValidationStorageAccountNameAlreadyInUse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This workspace name already exists.
        /// </summary>
        public static string ValidationWorkspaceNameAlreadyInUse {
            get {
                return ResourceManager.GetString("ValidationWorkspaceNameAlreadyInUse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This workspace name already exists, or is being reserved by a workspace which was previously soft deleted. Please use a different name.
        /// </summary>
        public static string ValidationWorkspaceNameAlreadyInUseAndSoftDeleted {
            get {
                return ResourceManager.GetString("ValidationWorkspaceNameAlreadyInUseAndSoftDeleted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Resource name must be between 3 and 33 characters long. Its first character has to be alphanumeric, and the rest may contain hyphens and underscores. No whitespace is allowed..
        /// </summary>
        public static string ValidationWorkspaceNameInvalid {
            get {
                return ResourceManager.GetString("ValidationWorkspaceNameInvalid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This workspace name is reserved.
        /// </summary>
        public static string ValidationWorkspaceNameReserved {
            get {
                return ResourceManager.GetString("ValidationWorkspaceNameReserved", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 
        ///      Cancel
        ///    .
        /// </summary>
        public static string workspaceCancelUpgradeButtonText {
            get {
                return ResourceManager.GetString("workspaceCancelUpgradeButtonText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 
        ///      Confirm Upgrade
        ///    .
        /// </summary>
        public static string workspaceConfirmUpgradeButtonText {
            get {
                return ResourceManager.GetString("workspaceConfirmUpgradeButtonText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 
        ///      View full pricing details
        ///    .
        /// </summary>
        public static string workspaceCreateSKUPricingDetails {
            get {
                return ResourceManager.GetString("workspaceCreateSKUPricingDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 
        ///      The Enterprise SKU (preview) boosts productivity for all ML skill levels and offers robust MLOps capabilities to manage the complete ML lifecycle. THe Basic SKU workspace allows for open source development and a code-first experience.
        ///    .
        /// </summary>
        public static string workspaceCreateSKUTooltip {
            get {
                return ResourceManager.GetString("workspaceCreateSKUTooltip", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The selected subscription doesn’t have permissions to register the resource provider. For more information, &lt;a href = {0} target = &quot;_blank&quot;&gt;click here&lt;/a&gt;..
        /// </summary>
        public static string WorkspaceErrorMessage {
            get {
                return ResourceManager.GetString("WorkspaceErrorMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 
        ///      Workspace with ID &quot;{0}&quot; could not be loaded.
        ///    .
        /// </summary>
        public static string workspaceLoadFailure {
            get {
                return ResourceManager.GetString("workspaceLoadFailure", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 
        ///      Workspace edition
        ///    .
        /// </summary>
        public static string workspaceSKUPropertyLabel {
            get {
                return ResourceManager.GetString("workspaceSKUPropertyLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 
        ///      Introducing a new immersive experience (preview) for managing the end-to-end machine learning lifecycle.
        ///    .
        /// </summary>
        public static string workspaceTwoBannerBasicSKUBody {
            get {
                return ResourceManager.GetString("workspaceTwoBannerBasicSKUBody", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 
        ///      Launch Preview Now
        ///    .
        /// </summary>
        public static string workspaceTwoBannerBasicSKUTitle {
            get {
                return ResourceManager.GetString("workspaceTwoBannerBasicSKUTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 
        ///      An immersive experience for managing the end-to-end machine learning lifecycle.
        ///    .
        /// </summary>
        public static string workspaceTwoBannerBody {
            get {
                return ResourceManager.GetString("workspaceTwoBannerBody", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 
        ///      Introducing a new immersive experience (preview) for managing the end-to-end machine learning lifecycle.
        ///    .
        /// </summary>
        public static string workspaceTwoBannerBodyPreview {
            get {
                return ResourceManager.GetString("workspaceTwoBannerBodyPreview", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 
        ///      Launch now
        ///    .
        /// </summary>
        public static string workspaceTwoBannerButton {
            get {
                return ResourceManager.GetString("workspaceTwoBannerButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 
        ///      Learn more
        ///    .
        /// </summary>
        public static string workspaceTwoBannerLink {
            get {
                return ResourceManager.GetString("workspaceTwoBannerLink", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 
        ///      Launch now
        ///    .
        /// </summary>
        public static string workspaceTwoNoticeButton {
            get {
                return ResourceManager.GetString("workspaceTwoNoticeButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 
        ///      Contents of this page will be moving to a new immersive experience for
        ///      managing the end-to-end machine learning lifecycle. Compute targets will
        ///      be manageable from both locations. Features provided in preview are
        ///      offered at no additional charge but may not remain so after general
        ///      availability.
        ///    .
        /// </summary>
        public static string workspaceTwoNoticeMessage {
            get {
                return ResourceManager.GetString("workspaceTwoNoticeMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 
        ///      about our pricing
        ///    .
        /// </summary>
        public static string workspaceUpgradeAboutOurPricing {
            get {
                return ResourceManager.GetString("workspaceUpgradeAboutOurPricing", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 
        ///      You currently do not have permissions to upgrade your workspace. Please contact your IT administrator.
        ///    .
        /// </summary>
        public static string workspaceUpgradeAuthorizationFailed {
            get {
                return ResourceManager.GetString("workspaceUpgradeAuthorizationFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Upgrade this workspace to Enterprise edition (preview) to use visual machine learning, advanced automated machine learning, and to manage quota..
        /// </summary>
        public static string workspaceUpgradeBannerText {
            get {
                return ResourceManager.GetString("workspaceUpgradeBannerText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 
        ///      You need an Enterprise edition (preview) workspace to view and set quotas on Enterprise workspaces.
        ///    .
        /// </summary>
        public static string workspaceUpgradeBulletPoint {
            get {
                return ResourceManager.GetString("workspaceUpgradeBulletPoint", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 
        ///      Upgrade
        ///    .
        /// </summary>
        public static string workspaceUpgradeButtonText {
            get {
                return ResourceManager.GetString("workspaceUpgradeButtonText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 
        ///      Upgrade workspace to access Enterprise (preview) capabilities. Visit our {0} for more information.
        ///    .
        /// </summary>
        public static string workspaceUpgradeConfirmationBoxContent {
            get {
                return ResourceManager.GetString("workspaceUpgradeConfirmationBoxContent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 
        ///      Confirm workspace upgrade
        ///    .
        /// </summary>
        public static string workspaceUpgradeConfirmationBoxTitle {
            get {
                return ResourceManager.GetString("workspaceUpgradeConfirmationBoxTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 
        ///      Learn more
        ///    .
        /// </summary>
        public static string workspaceUpgradeLearnMore {
            get {
                return ResourceManager.GetString("workspaceUpgradeLearnMore", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 
        ///      pricing page
        ///    .
        /// </summary>
        public static string workspaceUpgradePricingPage {
            get {
                return ResourceManager.GetString("workspaceUpgradePricingPage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 
        ///      Upgrade this workspace to Enterprise edition (preview) to use UI-based tools for all skill levels and more.
        ///    .
        /// </summary>
        public static string workspaceUpgradeQuickLinkBannerText {
            get {
                return ResourceManager.GetString("workspaceUpgradeQuickLinkBannerText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 
        ///      Learn about Enterprise Edition (preview)
        ///    .
        /// </summary>
        public static string workspaceUpgradeQuickLinkLearnAboutEnterpriseEdition {
            get {
                return ResourceManager.GetString("workspaceUpgradeQuickLinkLearnAboutEnterpriseEdition", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 
        ///      Use the Enterprise edition (preview) to access UI-based tools for all skill levels, built-in MLOps and more
        ///    .
        /// </summary>
        public static string workspaceUpgradeQuickLinkPostUpgradeBannerText {
            get {
                return ResourceManager.GetString("workspaceUpgradeQuickLinkPostUpgradeBannerText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 
        ///      This feature is disabled for you. Please upgrade to an Enterprise edition (preview) workspace to configure quotas.
        ///    .
        /// </summary>
        public static string workspaceUpgradeSetQuotaOperationNotAllowed {
            get {
                return ResourceManager.GetString("workspaceUpgradeSetQuotaOperationNotAllowed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 
        ///      Your workspace {0} upgraded successfully.
        ///    .
        /// </summary>
        public static string workspaceUpgradeUpgradeCompleteNotificationContent {
            get {
                return ResourceManager.GetString("workspaceUpgradeUpgradeCompleteNotificationContent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 
        ///      Workspace Upgrade Complete
        ///    .
        /// </summary>
        public static string workspaceUpgradeUpgradeCompleteNotificationTitle {
            get {
                return ResourceManager.GetString("workspaceUpgradeUpgradeCompleteNotificationTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 
        ///      An unexpected error occurred, please try upgrading your workspace again, or submitting a support ticket.
        ///    .
        /// </summary>
        public static string workspaceUpgradeUpgradeFailed {
            get {
                return ResourceManager.GetString("workspaceUpgradeUpgradeFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 
        ///      Your workspace {0} did not upgrade successfully.
        ///    .
        /// </summary>
        public static string workspaceUpgradeUpgradeFailedNotificationContent {
            get {
                return ResourceManager.GetString("workspaceUpgradeUpgradeFailedNotificationContent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 
        ///      Workspace Upgrade Failed
        ///    .
        /// </summary>
        public static string workspaceUpgradeUpgradeFailedNotificationTitle {
            get {
                return ResourceManager.GetString("workspaceUpgradeUpgradeFailedNotificationTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 
        ///      Upgrade Azure Machine Learning workspace {0} succeeded.
        ///    .
        /// </summary>
        public static string workspaceUpgradeUpgradeSucceeded {
            get {
                return ResourceManager.GetString("workspaceUpgradeUpgradeSucceeded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 
        ///      Upgrading Azure Machine Learning workspace {0}
        ///    .
        /// </summary>
        public static string workspaceUpgradeUpgrading {
            get {
                return ResourceManager.GetString("workspaceUpgradeUpgrading", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 
        ///      Your workspace {0} is upgrading from Basic to Enterprise
        ///    .
        /// </summary>
        public static string workspaceUpgradeUpgradingNotificationContent {
            get {
                return ResourceManager.GetString("workspaceUpgradeUpgradingNotificationContent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 
        ///      Workspace is currently upgrading
        ///    .
        /// </summary>
        public static string workspaceUpgradeUpgradingNotificationTitle {
            get {
                return ResourceManager.GetString("workspaceUpgradeUpgradingNotificationTitle", resourceCulture);
            }
        }
    }
}
