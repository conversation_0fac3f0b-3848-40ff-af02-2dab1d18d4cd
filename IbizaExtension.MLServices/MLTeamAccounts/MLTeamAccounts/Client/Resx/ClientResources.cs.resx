﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="ResourceNameRequired" xml:space="preserve">
    <value>Název prostředku je povinný.</value>
    <comment>Required field validation text for create textbox</comment>
  </data>
  <data name="CreateBladeTitle" xml:space="preserve">
    <value>Machine Learning</value>
    <comment>PLEASE DO NOT TRANSLATE</comment>
  </data>
  <data name="CreateBladeHubTitle" xml:space="preserve">
    <value>Centrum Azure AI</value>
  </data>
  <data name="CreateBladeHubNameLabel" xml:space="preserve">
    <value>Hub</value>
  </data>
  <data name="CreateBladeProjectTitle" xml:space="preserve">
    <value>Azure AI project</value>
  </data>
  <data name="CreateBladeSubtitle" xml:space="preserve">
    <value>Vytvořit pracovní prostor Machine Learning</value>
  </data>
  <data name="CreateBladeHubSubtitle" xml:space="preserve">
    <value>Vytvoření prostředku centra Azure AI</value>
  </data>
  <data name="CreateBladeProjectSubtitle" xml:space="preserve">
    <value>Organize and track work, collaborate with others and upload data. Access your work in Azure AI Foundry or Azure Machine Learning Studio.</value>
  </data>
  <data name="CreateBladeRegistrySubtitle" xml:space="preserve">
    <value>Vytvořit registr strojového učení</value>
  </data>
  <data name="CreateBladeWorkspaceNameLabel" xml:space="preserve">
    <value>Název</value>
  </data>
  <data name="CreateBladeRegistryNameLabel" xml:space="preserve">
    <value>Název</value>
  </data>
  <data name="CreateBladeRegistryDescriptionLabel" xml:space="preserve">
    <value>Popis</value>
  </data>
  <data name="CreateBladeRegistryDescriptionKey" xml:space="preserve">
    <value>popis</value>
  </data>
  <data name="CreateBladeFriendlyNameLabel" xml:space="preserve">
    <value>Popisný název</value>
  </data>
  <data name="CreateBladeFriendlyNameInfo" xml:space="preserve">
    <value>Display name of the AI hub that will be displayed in AI Foundry</value>
  </data>
  <data name="CreateBladeHubConfigDefaultResourceGroup_label" xml:space="preserve">
    <value>Výchozí skupina prostředků projektu</value>
  </data>
  <data name="CreateBladeHubConfigDefaultResourceGroup_info" xml:space="preserve">
    <value>Pokud uživatel pro svůj projekt nezadá skupinu prostředků, použije se tato skupina prostředků ve výchozím nastavení. Tvůrci projektu se v instanci projektu udělí přiřazení role vlastníka Azure RBAC (řízení přístupu na základě role).</value>
  </data>
  <data name="CreateBladeHubConfigDefaultResourceGroup_sameAsHubLabel" xml:space="preserve">
    <value>Same as hub resource group</value>
  </data>
  <data name="CreateBladeHubAIServices_ariaLabel" xml:space="preserve">
    <value>Připojení služeb AI, včetně OpenAI</value>
  </data>
  <data name="CreateBladeHubAIServices_label" xml:space="preserve">
    <value>Připojení služeb AI, vč. OpenAI</value>
  </data>
  <data name="CreateBladeHubAIServices_info" xml:space="preserve">
    <value>Poskytovatel základních modelů spravovaných Microsoftem. Spravuje se ve vašem předplatném Azure jako samostatný prostředek.</value>
  </data>
  <data name="CreateBladeHubAIServices_skipText" xml:space="preserve">
    <value>Přeskočit připojení služeb AI</value>
  </data>
  <data name="CreateBladeDescriptionLabel" xml:space="preserve">
    <value>Popis</value>
  </data>
  <data name="CreateBladeSubscriptionLabel" xml:space="preserve">
    <value>Předplatné</value>
  </data>
  <data name="CreateBladeSubscriptionIdLabel" xml:space="preserve">
    <value>ID předplatného</value>
  </data>
  <data name="CreateBladeResourceGroupLabel" xml:space="preserve">
    <value>Skupina prostředků</value>
  </data>
  <data name="CreateBladeLocationLabel" xml:space="preserve">
    <value>Umístění</value>
  </data>
  <data name="CreateBladeMainTabTitle" xml:space="preserve">
    <value>Hlavní</value>
  </data>
  <data name="CreateBladeTagTabTitle" xml:space="preserve">
    <value>Značky</value>
  </data>
  <data name="CreateBladeReviewTabTitle" xml:space="preserve">
    <value>Zkontrolovat</value>
  </data>
  <data name="CreateBladeCreateButtonName" xml:space="preserve">
    <value>Vytvořit</value>
  </data>
  <data name="CreateBladeReviewCreateButtonName" xml:space="preserve">
    <value>Zkontrolovat a vytvořit</value>
  </data>
  <data name="CreateBladeWorkspaceDeploymentErrorText" xml:space="preserve">
    <value>Chyba nasazení pracovního prostoru</value>
  </data>
  <data name="CreateBladeTemplateErrorText" xml:space="preserve">
    <value>Chyba šablony</value>
  </data>
  <data name="downloadTemplateLinkText" xml:space="preserve">
    <value>Stáhnout šablonu pro automatizaci</value>
  </data>
  <data name="AssetType_description" xml:space="preserve">
    <value>Pracovní prostory jsou místo, kde spravujete všechny modely, prostředky a data související s vašimi projekty strojového učení. Pokud chcete začít používat Azure Machine Learning, vytvořte si pracovní prostor.</value>
    <comment>PLEASE DO NOT TRANSLATE "Azure Machine Learning"</comment>
  </data>
  <data name="AssetType_keywords" xml:space="preserve">
    <value>ML, AML, strojové učení, AI, umělá inteligence, náhled, hluboké učení, analýza, data, NLP, zpracování přirozeného jazyka, CNN, neurální síť, Workbench, trénování, Notebooks, AutoML, Designer, Počítačové zpracování obrazu, modely, datové vědy, klasifikace, regrese, posílení učení</value>
  </data>
  <data name="Overview" xml:space="preserve">
    <value>Přehled</value>
  </data>
  <data name="OverviewKeywords" xml:space="preserve">
    <value>Domovská stránka souhrnu</value>
  </data>
  <data name="ValidationWorkspaceNameAlreadyInUse" xml:space="preserve">
    <value>Tento název pracovního prostoru už existuje</value>
  </data>
  <data name="ValidationHubNameAlreadyInUse" xml:space="preserve">
    <value>Tento název centra AI už existuje.</value>
  </data>
  <data name="ValidationProjectNameAlreadyInUse" xml:space="preserve">
    <value>This AI project name already exists</value>
  </data>
  <data name="ValidationRegistryNameAlreadyInUse" xml:space="preserve">
    <value>Tento název registru už existuje.</value>
  </data>
  <data name="ValidationRegistryDescriptionTooLarge" xml:space="preserve">
    <value>Tento popis registru má maximální délku 256 znaků.</value>
  </data>
  <data name="ValidationWorkspaceNameAlreadyInUseAndSoftDeleted" xml:space="preserve">
    <value>Tento název pracovního prostoru už existuje nebo je vyhrazen pracovním prostorem, který byl dříve obnovitelně odstraněn. Použijte prosím jiný název.</value>
  </data>
  <data name="AdditionalResourceInfo" xml:space="preserve">
    <value>Do pracovního prostoru pro váš komfort automaticky následující prostředky (pokud jsou v oblasti k dispozici): &lt;a href={0} target="_blank"&gt;Azure Storage&lt;/a&gt;, &lt;a href={1} target="_blank"&gt;Azure Application Insights&lt;/a&gt; and &lt;a href={2} target="_blank"&gt;Azure Key Vault&lt;/a&gt;.</value>
  </data>
  <data name="ValidationWorkspaceNameInvalid" xml:space="preserve">
    <value>Název prostředku musí mít 3 až 33 znaků. Jeho první znak musí být alfanumerický a zbytek může obsahovat spojovníky a podtržítka. Nepovolují se žádné prázdné znaky.</value>
  </data>
  <data name="ValidationRegistryNameInvalid" xml:space="preserve">
    <value>Název registru musí být dlouhý od 3 do 33 znaků. Jeho první znak musí být alfanumerický a zbytek může obsahovat spojovníky a podtržítka. Nepovolují se žádné prázdné znaky.</value>
  </data>
  <data name="ValidationWorkspaceNameReserved" xml:space="preserve">
    <value>Tento název pracovního prostoru je rezervovaný</value>
  </data>
  <data name="ValidationStorageAccountNameAlreadyInUse" xml:space="preserve">
    <value>Tento název účtu úložiště už existuje.</value>
  </data>
  <data name="ValidationError" xml:space="preserve">
    <value>Při pokusu o ověření prostředku došlo k chybě.</value>
  </data>
  <data name="ValidationContainerRegistryNameAlreadyInUse" xml:space="preserve">
    <value>Tento název registru kontejneru už existuje</value>
  </data>
  <data name="ValidationDependentResourcesAlreadyInUse" xml:space="preserve">
    <value>Prostředky závislé buňky s tímto názvem už existují</value>
  </data>
  <data name="ValidationAppInsightsNameAlreadyInUse" xml:space="preserve">
    <value>Tento název Application Insights už existuje.</value>
  </data>
  <data name="ValidationAIServicesNameAlreadyInUse" xml:space="preserve">
    <value>Tento název AI služeb už existuje.</value>
  </data>
  <data name="ValidationKeyVaultNameAlreadyInUse" xml:space="preserve">
    <value>Tento název trezoru klíčů už existuje.</value>
  </data>
  <data name="ValidationRegionNotSelected" xml:space="preserve">
    <value>Vyberte prosím aspoň jednu oblast.</value>
  </data>
  <data name="ValidationPrimaryRegionNotSelected" xml:space="preserve">
    <value>Ujistěte se prosím, že primární oblast je jednou z vybraných hodnot.</value>
  </data>
  <data name="WorkspaceErrorMessage" xml:space="preserve">
    <value>Vybrané předplatné nemá oprávnění k registraci poskytovatele prostředků. Další informace získáte &lt;a href = {0} target = "_blank"&gt;kliknutím sem&lt;/a&gt;.</value>
  </data>
  <data name="AssetTypeNames_MachineLearningServices_lowerPlural" xml:space="preserve">
    <value>pracovní prostory</value>
  </data>
  <data name="AssetTypeNames_MachineLearningServices_lowerSingular" xml:space="preserve">
    <value>pracovní prostor</value>
  </data>
  <data name="AssetTypeNames_MachineLearningServices_plural" xml:space="preserve">
    <value>Machine Learning</value>
    <comment>PLEASE DO NOT TRANSLATE</comment>
  </data>
  <data name="AssetTypeNames_MachineLearningServices_singular" xml:space="preserve">
    <value>Pracovní prostor Azure Machine Learning</value>
    <comment>PLEASE DO NOT TRANSLATE "Azure Machine Learning"</comment>
  </data>
  <data name="ColumnMachineLearningWorkspaceId" xml:space="preserve">
    <value>ID pracovního prostoru</value>
  </data>
  <data name="ColumnCreationTime" xml:space="preserve">
    <value>Vytvořené</value>
  </data>
  <data name="LabelComputeMenuItem" xml:space="preserve">
    <value>Compute</value>
  </data>
  <data name="LabelProjectsMenuItem" xml:space="preserve">
    <value>Experimenty</value>
  </data>
  <data name="LabelTasksMenuItem" xml:space="preserve">
    <value>Aktivity</value>
  </data>
  <data name="LabelDeploymentsMenuItem" xml:space="preserve">
    <value>Nasazení</value>
  </data>
  <data name="LabelImagesMenuItem" xml:space="preserve">
    <value>Obrázky</value>
  </data>
  <data name="LabelModelsMenuItem" xml:space="preserve">
    <value>Modely</value>
  </data>
  <data name="LabelPipelinesMenuItem" xml:space="preserve">
    <value>Kanály</value>
  </data>
  <data name="LabelProperties" xml:space="preserve">
    <value>Vlastnosti</value>
  </data>
  <data name="TitleWebWorkspaceBlade" xml:space="preserve">
    <value>Machine Learning</value>
  </data>
  <data name="LabelAutoMLMenuItem" xml:space="preserve">
    <value>Automatizované strojové učení</value>
  </data>
  <data name="TitleMonitoringLens" xml:space="preserve">
    <value>Začínáme</value>
  </data>
  <data name="TitleGiveFeedback" xml:space="preserve">
    <value>Poslat názor</value>
  </data>
  <data name="LabelStorage" xml:space="preserve">
    <value>Úložiště</value>
  </data>
  <data name="LabelRegistry" xml:space="preserve">
    <value>Registr kontejneru</value>
  </data>
  <data name="LabelInsights" xml:space="preserve">
    <value>Application Insights</value>
  </data>
  <data name="LabelKeyVault" xml:space="preserve">
    <value>Key Vault</value>
  </data>
  <data name="ManagedKeyVault" xml:space="preserve">
    <value>Microsoft-managed</value>
  </data>
  <data name="LabelProjectHubResource" xml:space="preserve">
    <value>Centrum Azure AI</value>
  </data>
  <data name="TitleMachineLearningServicesDeleteConfirmationMessageBox" xml:space="preserve">
    <value>Odstranit pracovní prostor</value>
  </data>
  <data name="TitleMachineLearningServicesRegistryDeleteConfirmationMessageBox" xml:space="preserve">
    <value>Odstranit registr</value>
  </data>
  <data name="TitleError" xml:space="preserve">
    <value>Chyba</value>
  </data>
  <data name="CommandDelete" xml:space="preserve">
    <value>Odstranit</value>
  </data>
  <data name="CommandCancel" xml:space="preserve">
    <value>Zrušit</value>
  </data>
  <data name="RecentlyDeletedWorkspaces" xml:space="preserve">
    <value>Nedávno odstraněno</value>
  </data>
  <data name="ProgressMachineLearningServicesDeleteExecuting" xml:space="preserve">
    <value>Odstraňování prostředku</value>
  </data>
  <data name="ProgressMachineLearningRegistryDeleteExecuting" xml:space="preserve">
    <value>Odstraňuje se registr.</value>
  </data>
  <data name="ProgressMachineLearningDeleteError" xml:space="preserve">
    <value>Prostředek se nepovedlo odstranit.</value>
  </data>
  <data name="ProgressMachineLearningRegistryDeleteError" xml:space="preserve">
    <value>Registr se nepovedlo odstranit.</value>
  </data>
  <data name="TextViennaGitHubLinkBody" xml:space="preserve">
    <value>Nechte se inspirovat velkou kolekcí příkladů strojového učení.</value>
  </data>
  <data name="TextViennaGitHubLinkHeader" xml:space="preserve">
    <value>Další ukázky najdete na GitHubu</value>
  </data>
  <data name="TextMachineLearningServicesManageRegistryLinkBody" xml:space="preserve">
    <value>Zjistěte, jak vytvářet a spravovat registry řešení Machine Learning.</value>
  </data>
  <data name="TextMachineLearningServicesManageRegistryLinkHeader" xml:space="preserve">
    <value>Vytvořit a spravovat</value>
  </data>
  <data name="TextMachineLearningServicesManageFeatureStoreLinkBody" xml:space="preserve">
    <value>Zjistěte, jak vytvářet a spravovat registry řešení Machine Learning.</value>
  </data>
  <data name="TextMachineLearningServicesManageFeatoreStoreLinkHeader" xml:space="preserve">
    <value>Vytvořit a spravovat</value>
  </data>
  <data name="TextMachineLearningServicesShareRegistryLinkBody" xml:space="preserve">
    <value>Zjistěte, jak sdílet prostředky pro Machine Learning pomocí registrů.</value>
  </data>
  <data name="TextMachineLearningServicesShareRegistryLinkHeader" xml:space="preserve">
    <value>Sdílení modelů, komponent a prostředí</value>
  </data>
  <data name="TextMachineLearningServicesDocumentationLinkBody" xml:space="preserve">
    <value>Naučte se používat Azure Machine Learning.</value>
  </data>
  <data name="TextMachineLearningServicesDocumentationLinkHeader" xml:space="preserve">
    <value>Zobrazit dokumentaci</value>
  </data>
  <data name="TextMachineLearningServicesForumLinkBody" xml:space="preserve">
    <value>Připojte se k diskuzi o Azure Machine Learning. </value>
  </data>
  <data name="TextMachineLearningServicesForumLinkHeader" xml:space="preserve">
    <value>Zobrazit fórum</value>
  </data>
  <data name="TextLaunchWebWorkspace" xml:space="preserve">
    <value>Pokud chcete spouštět a sledovat experimenty, porovnávat výkon modelu a nasazovat modely, prozkoumejte svůj pracovní prostor služby Machine Learning.</value>
  </data>
  <data name="TextLaunchWebWorkspaceHeader" xml:space="preserve">
    <value>Prozkoumat pracovní prostor Azure Machine Learning</value>
  </data>
  <data name="ConfirmationMachineLearningDelete" xml:space="preserve">
    <value>Opravdu chcete odstranit pracovní prostor {0}?</value>
  </data>
  <data name="ConfirmationMachineLearningRegistryDelete" xml:space="preserve">
    <value>Určitě chcete odstranit registr {0}?</value>
  </data>
  <data name="ProgressNotificationMachineLearningServicesDeleteError" xml:space="preserve">
    <value>Došlo k chybě při odstraňování pracovního prostoru s názvem {workspaceName}.{lineBreak}Podrobnosti o chybě:{lineBreak}{errorMessage}.</value>
  </data>
  <data name="ProgressNotificationMachineLearningServicesDeleteErrorTitle" xml:space="preserve">
    <value>Chyba odstranění prostředku</value>
  </data>
  <data name="ProgressNotificationMachineLearningServicesDeleteCancelTitle" xml:space="preserve">
    <value>Odstranění prostředku se zrušilo</value>
  </data>
  <data name="ProgressNotificationMachineLearningRegistryDeleteError" xml:space="preserve">
    <value>Při odstraňování registru s názvem {registryName} došlo k chybě.{lineBreak}Podrobnosti o chybě:{lineBreak}{errorMessage}.</value>
  </data>
  <data name="ProgressNotificationMachineLearningRegistryDeleteErrorTitle" xml:space="preserve">
    <value>Chyba při odstraňování registru</value>
  </data>
  <data name="ProgressNotificationMachineLearningServicesDeleteExecuting" xml:space="preserve">
    <value>Prostředek „{workspaceName}“ se odstraňuje...</value>
  </data>
  <data name="ProgressNotificationMachineLearningServicesDeleteExecutingTitle" xml:space="preserve">
    <value>Odstraňuje se prostředek…</value>
  </data>
  <data name="ProgressNotificationMachineLearningRegistryDeleteExecuting" xml:space="preserve">
    <value>Odstraňuje se registr {registryName}…</value>
  </data>
  <data name="ProgressNotificationMachineLearningRegistryDeleteExecutingTitle" xml:space="preserve">
    <value>Odstraňuje se registr…</value>
  </data>
  <data name="ProgressNotificationMachineLearningServicesDeleteSuccess" xml:space="preserve">
    <value>Prostředek „{workspaceName}“ se úspěšně odstranil.</value>
  </data>
  <data name="ProgressNotificationMachineLearningServicesDeleteSuccessTitle" xml:space="preserve">
    <value>Úspěšně odstraněno</value>
  </data>
  <data name="ProgressNotificationMachineLearningRegistryDeleteSuccess" xml:space="preserve">
    <value>Registr {registryName} se úspěšně odstranil.</value>
  </data>
  <data name="ProgressNotificationMachineLearningRegistryDeleteSuccessTitle" xml:space="preserve">
    <value>Registr se odstranil</value>
  </data>
  <data name="ErrorJsonParsingException" xml:space="preserve">
    <value>Došlo k neočekávané chybě parsování.</value>
  </data>
  <data name="CommandDownloadConfig" xml:space="preserve">
    <value>Stáhnout config.json</value>
  </data>
  <data name="CommandDownloadConfigTooltip" xml:space="preserve">
    <value>Tento soubor použijte k načtení konfigurace pracovního prostoru v poznámkovém bloku Azure ML SDK nebo skriptu Python</value>
  </data>
  <data name="labelCommandButtonRefresh" xml:space="preserve">
    <value>Aktualizovat</value>
  </data>
  <data name="CommandCreateProject" xml:space="preserve">
    <value>Create project</value>
  </data>
  <data name="textNotAvailable" xml:space="preserve">
    <value>Není k dispozici</value>
    <comment>short for 'Not Available'</comment>
  </data>
  <data name="quotaBladeTitle" xml:space="preserve">
    <value>Využití a kvóty</value>
  </data>
  <data name="labelResource" xml:space="preserve">
    <value>Prostředek</value>
  </data>
  <data name="machineLearningCompute" xml:space="preserve">
    <value>Spravované výpočetní prostředky</value>
  </data>
  <data name="quotaTableHeaderQuota" xml:space="preserve">
    <value>Název prostředku</value>
  </data>
  <data name="quotaTableHeaderDedicated" xml:space="preserve">
    <value>Využití vyhrazených jader</value>
  </data>
  <data name="quotaTableHeaderLowPriority" xml:space="preserve">
    <value>Využití jader s nízkou prioritou</value>
  </data>
  <data name="quotaTableNoData" xml:space="preserve">
    <value>Neexistují žádná data, která by bylo možné zobrazit.</value>
  </data>
  <data name="quotaTableTotalSubscriptionQuota" xml:space="preserve">
    <value>Celková kvóta předplatného</value>
  </data>
  <data name="labelRequestQuota" xml:space="preserve">
    <value>Kvóta žádostí</value>
  </data>
  <data name="quotaTableServerError" xml:space="preserve">
    <value>Na serveru došlo k chybě při zpracování aktuálního požadavku. Aktualizujte prosím tabulku znovu.</value>
  </data>
  <data name="quotaSubscriptionViewTabHeader" xml:space="preserve">
    <value>Zobrazení předplatného</value>
  </data>
  <data name="quotaSubscriptionViewResourceNameHelptext" xml:space="preserve">
    <value>Rozbalte každou řadu virtuálních počítačů, abyste zobrazili přidělení kvóty, využití prostředků podle pracovních prostorů, clustery a instance.</value>
  </data>
  <data name="quotaSubscriptionViewClusterHelptext" xml:space="preserve">
    <value>U clusteru zobrazíme vaše aktuálně přidělená jádra a maximální počet jader, na který se může škálovat.</value>
  </data>
  <data name="quotaSubscriptionViewUsageHelptext" xml:space="preserve">
    <value>Pro instanci nadále využívá kvótu i ve stavu Zastaveno, proto je možné kdykoli provést restart.</value>
  </data>
  <data name="quotaWorkspaceViewTabHeader" xml:space="preserve">
    <value>Zobrazení pracovního prostoru</value>
  </data>
  <data name="quotaWorkspaceViewResourceNameHelptext" xml:space="preserve">
    <value>Rozbalte každý pracovní prostor, abyste viděli přidělení kvóty, využití prostředků podle řady virtuálních počítačů, clustery a instance.</value>
  </data>
  <data name="quotaWorkspaceViewClusterHelptext" xml:space="preserve">
    <value>U clusteru zobrazíme vaše aktuálně přidělená jádra a maximální počet jader, na který se může škálovat.</value>
  </data>
  <data name="quotaWorkspaceViewUsageHelptext" xml:space="preserve">
    <value>Pro instanci nadále využívá kvótu i ve stavu Zastaveno, proto je možné kdykoli provést restart.</value>
  </data>
  <data name="quotaQuotaSettingTabHeader" xml:space="preserve">
    <value>Konfigurace kvót</value>
  </data>
  <data name="quotaWorkspaceQuotaExceedSubscriptionLimit" xml:space="preserve">
    <value>Kvóta úrovně pracovního prostoru nemůže překročit limit kvóty úrovně předplatného.</value>
  </data>
  <data name="quotaWorkspaceQuotaInvalidVMFamilyName" xml:space="preserve">
    <value>Zadejte rodinu virtuálních počítačů, která je v oblasti $region podporována a pro kterou máte kvótu úrovně předplatného.</value>
  </data>
  <data name="quotaWorkspaceQuotaLessThanMinimumClusterCores" xml:space="preserve">
    <value>Kvóta úrovně pracovního prostoru nemůže být nižší než počet jader vyžadovaných pro podporu minima uzlů clusterů a instancí tohoto pracovního prostoru.</value>
  </data>
  <data name="quotaWorkspaceQuotaInsufficientPermissions" xml:space="preserve">
    <value>Nemáte oprávnění nastavit kvótu na úrovni pracovního prostoru. Pokud chcete pomoci přidělit prostředky mezi pracovní prostory, obraťte se na svého správce předplatného.</value>
  </data>
  <data name="quotaWorkspaceQuotaNewLimitHelpText" xml:space="preserve">
    <value>Tady můžete nastavit přidělení kvóty úrovně předplatného mezi pracovní prostory. Pokud chcete změnit celkovou kvótu úrovně předplatného, použijte tlačítko žádosti o kvótu dole. Poznámka: Tyto hodnoty může měnit jedině vlastník předplatného.</value>
  </data>
  <data name="quotaWorkspaceQuotaResourceNameHelpText" xml:space="preserve">
    <value>Rozbalte každou velikost virtuálního počítače, abyste zobrazili a přidělili kvótu úrovně předplatného mezi pracovní prostory.</value>
  </data>
  <data name="quotaWorkspaceQuotaUnknownError" xml:space="preserve">
    <value>Neznámá chyba</value>
  </data>
  <data name="quotaWorkspaceQuotaPlaceHolder" xml:space="preserve">
    <value>Nepřidělené jádra: {0}, maximum: {1}</value>
  </data>
  <data name="quotaNote" xml:space="preserve">
    <value>Poznámka:</value>
  </data>
  <data name="labelQuotaUsage" xml:space="preserve">
    <value>Využití a kvóty</value>
  </data>
  <data name="titleSupport" xml:space="preserve">
    <value>Podpora a řešení potíží</value>
  </data>
  <data name="titleSettings" xml:space="preserve">
    <value>Nastavení</value>
  </data>
  <data name="titleInsights" xml:space="preserve">
    <value>Insights (preview)</value>
  </data>
  <data name="quotaRequestTitle" xml:space="preserve">
    <value>Podrobnosti o kvótě</value>
  </data>
  <data name="quotaRequestHeader" xml:space="preserve">
    <value>Zadejte prosím limit pro prostředky, o které žádáte:</value>
  </data>
  <data name="quotaProviderRegisteringErrorMsg" xml:space="preserve">
    <value>Registruje se vaše předplatné {0} u poskytovatele prostředků {1}.</value>
  </data>
  <data name="quotaProviderNotRegisteredErrorMsg" xml:space="preserve">
    <value>Vaše předplatné {0} není registrováno u poskytovatele prostředků {1}. Vytvořte pracovní prostor služby Machine Learning, aby se provedla automatická registrace, a zkuste žádost o podporu odeslat znovu.</value>
  </data>
  <data name="quotaRequestVMFamily" xml:space="preserve">
    <value>Řada virtuálních počítačů</value>
  </data>
  <data name="quotaRequestNewLimit" xml:space="preserve">
    <value>Nový limit vCPU</value>
  </data>
  <data name="quotaRequestResourceName" xml:space="preserve">
    <value>Název prostředku</value>
  </data>
  <data name="quotaRequestCurrentLimit" xml:space="preserve">
    <value>Aktuální limit vCPU</value>
  </data>
  <data name="quotaRequestNotFound" xml:space="preserve">
    <value>Pro dané umístění se nenašly žádné prostředky kvót.</value>
  </data>
  <data name="quotaRequestSubmit" xml:space="preserve">
    <value>Uložit a pokračovat</value>
  </data>
  <data name="quotaRequestLogOutputMessageDetail" xml:space="preserve">
    <value>{0} – aktuální hodnota: {1} / požadovaná hodnota: {2}</value>
  </data>
  <data name="quotaRequestDocumentationInfoBox" xml:space="preserve">
    <value>Pro další informace o žádostech o navýšení kvóty výpočetních prostředků (jader a vCPU) klikněte sem.</value>
  </data>
  <data name="Quota_Link_BladeTitle" xml:space="preserve">
    <value>Request and view quota in Azure AI Foundry</value>
    <comment>PLEASE DO NOT TRANSLATE "Azure AI"</comment>
  </data>
  <data name="Quota_Link_BladeDescription" xml:space="preserve">
    <value>Umožňuje zobrazit kvótu podle předplatného a oblasti a požádat o kvótu přímo ze Studia.</value>
  </data>
  <data name="Quota_Link_Button" xml:space="preserve">
    <value>Zobrazit kvótu</value>
  </data>
  <data name="ResourceLocationColumn" xml:space="preserve">
    <value>Umístění</value>
  </data>
  <data name="SelectLocation" xml:space="preserve">
    <value>Vyberte umístění.</value>
  </data>
  <data name="SelectSubscription" xml:space="preserve">
    <value>Vyberte předplatné.</value>
  </data>
  <data name="ValidationCreateWorkspacePermission" xml:space="preserve">
    <value>Nemáte požadovaná oprávnění ({0}) k vytvoření účtu v rámci vybrané skupiny prostředků</value>
  </data>
  <data name="CreateBladeWorkspaceCreationProgressNotification" xml:space="preserve">
    <value>Vytváří se pracovní prostor</value>
  </data>
  <data name="LabelDataMenuItem" xml:space="preserve">
    <value>Data (náhled)</value>
  </data>
  <data name="labelMLStudioLauncher" xml:space="preserve">
    <value>Vizuální rozhraní</value>
  </data>
  <data name="subtitleEnvironmentInfoBlade" xml:space="preserve">
    <value>Konfigurovat výpočetní prostředky k nasazení a správě modelů</value>
  </data>
  <data name="titleEnvironmentInfoBlade" xml:space="preserve">
    <value>Výpočetní prostředky služby Machine Learning</value>
  </data>
  <data name="mlStudioLaunchLabel" xml:space="preserve">
    <value>Spustit vizuální rozhraní</value>
  </data>
  <data name="mlStudioTitle" xml:space="preserve">
    <value>Vizuální rozhraní (preview)</value>
  </data>
  <data name="mlStudioSubtitle" xml:space="preserve">
    <value>Co je možné s vizuálním rozhraním</value>
  </data>
  <data name="mlStudioCapability1" xml:space="preserve">
    <value>Sestavení modelů strojového učení přetahováním položek</value>
  </data>
  <data name="mlStudioCapability2" xml:space="preserve">
    <value>Žádné omezení velikosti dat nebo vypočetní kapacity pro cvičení modelu</value>
  </data>
  <data name="mlStudioCapability3" xml:space="preserve">
    <value>Skutečná a výkonná podpora Pythonu</value>
  </data>
  <data name="mlStudioCapability4" xml:space="preserve">
    <value>Nasazení vaší webové služby jedním klepnutím</value>
  </data>
  <data name="mlStudioCapability5" xml:space="preserve">
    <value>Bohatá a stále se rozšiřující podpora modulů</value>
  </data>
  <data name="PropertiesBladeStorageAccountIdLabel" xml:space="preserve">
    <value>ID účtu úložiště</value>
  </data>
  <data name="PropertiesBladeApplicationInsightsLabel" xml:space="preserve">
    <value>Application Insights</value>
  </data>
  <data name="PropertiesBladeContainerRegistryIdLabel" xml:space="preserve">
    <value>ID Container Registry</value>
  </data>
  <data name="PropertiesBladeKeyVaultIdLabel" xml:space="preserve">
    <value>ID služby Key Vault</value>
  </data>
  <data name="PropertiesBladeResourceGroupLabel" xml:space="preserve">
    <value>Skupina prostředků</value>
  </data>
  <data name="PropertiesBladeResourceIdLabel" xml:space="preserve">
    <value>ID prostředku</value>
  </data>
  <data name="Properties_Save" xml:space="preserve">
    <value>Uložit</value>
  </data>
  <data name="Properties_Saving" xml:space="preserve">
    <value>Ukládání…</value>
  </data>
  <data name="Properties_Discard" xml:space="preserve">
    <value>Zahodit změny</value>
  </data>
  <data name="Properties_Refresh" xml:space="preserve">
    <value>Aktualizovat</value>
  </data>
  <data name="PropertiesBladeSubscriptionNameLabel" xml:space="preserve">
    <value>Název předplatného</value>
  </data>
  <data name="PropertiesBladeSubscriptionIdLabel" xml:space="preserve">
    <value>ID předplatného</value>
  </data>
  <data name="PropertiesBladeWorkspaceIdLabel" xml:space="preserve">
    <value>ID pracovního prostoru Machine Learning</value>
  </data>
  <data name="PropertiesBladeWorkspaceHubIdLabel" xml:space="preserve">
    <value>ID centra AI</value>
  </data>
  <data name="PropertiesBladeCreationDateLabel" xml:space="preserve">
    <value>Vytvořené</value>
  </data>
  <data name="PropertiesBladeLocationLabel" xml:space="preserve">
    <value>Umístění</value>
  </data>
  <data name="labelWorkstationsMenuItem" xml:space="preserve">
    <value>Notebookové virtuální počítače</value>
  </data>
  <data name="titleAuthoringGroup" xml:space="preserve">
    <value>Vytváření (preview)</value>
  </data>
  <data name="titleAssetsGroup" xml:space="preserve">
    <value>Aktiva</value>
  </data>
  <data name="labelDataLabeling" xml:space="preserve">
    <value>Popisování dat</value>
  </data>
  <data name="textMachineLearningServicesAutoMLLinkBody" xml:space="preserve">
    <value>Automaticky vytvoří model z vašich existujících dat.</value>
  </data>
  <data name="textMachineLearningServicesAutoMLLinkHeader" xml:space="preserve">
    <value>Vytvoření nového automatizovaného modelu strojového učení (preview)</value>
  </data>
  <data name="textMachineLearningServicesNotebookVMLinkBody" xml:space="preserve">
    <value>Rychle začněte se sadou SDK Python. Spusťte si ukázkové experimenty s notebookovými virtuálními počítači Azure Machine Learning.</value>
  </data>
  <data name="quickLinkUnderOverviewBladeAriaLabel" xml:space="preserve">
    <value>Rychlý odkaz v okně Přehled</value>
  </data>
  <data name="textMachineLearningServicesNotebookVMLinkHeader" xml:space="preserve">
    <value>Začínáme se Sample Notebooks (Preview)</value>
  </data>
  <data name="textMachineLearningServicesVisualInterfaceLinkBody" xml:space="preserve">
    <value>Vytvářejte nové modely přetažením existujících komponent.</value>
  </data>
  <data name="textMachineLearningServicesVisualInterfaceLinkHeader" xml:space="preserve">
    <value>Vytvoření modelu pomocí vizuálního rozhraní (preview)</value>
  </data>
  <data name="createBladeNextStepsMLFlowAzureMLDescription" xml:space="preserve">
    <value>Jak používat MLflow s Azure ML</value>
  </data>
  <data name="textAzureDatabricksOptionUseExisting" xml:space="preserve">
    <value>Použít existující</value>
  </data>
  <data name="textAzureDatabricksOptionCreateNew" xml:space="preserve">
    <value>Vytvořit nové</value>
  </data>
  <data name="workspaceTwoNoticeMessage" xml:space="preserve">
    <value>
      Contents of this page will be moving to a new immersive experience for
      managing the end-to-end machine learning lifecycle. Compute targets will
      be manageable from both locations. Features provided in preview are
      offered at no additional charge but may not remain so after general
      availability.
    </value>
  </data>
  <data name="workspaceTwoNoticeButton" xml:space="preserve">
    <value>
      Launch now
    </value>
  </data>
  <data name="workspaceTwoBannerButton" xml:space="preserve">
    <value>
      Launch now
    </value>
  </data>
  <data name="workspaceTwoBannerLink" xml:space="preserve">
    <value>
      Learn more
    </value>
  </data>
  <data name="workspaceTwoBannerBody" xml:space="preserve">
    <value>
      An immersive experience for managing the end-to-end machine learning lifecycle.
    </value>
  </data>
  <data name="workspaceTwoBannerBodyPreview" xml:space="preserve">
    <value>
      Introducing a new immersive experience (preview) for managing the end-to-end machine learning lifecycle.
    </value>
  </data>
  <data name="workspaceTwoBannerBasicSKUTitle" xml:space="preserve">
    <value>
      Launch Preview Now
    </value>
  </data>
  <data name="workspaceTwoBannerBasicSKUBody" xml:space="preserve">
    <value>
      Introducing a new immersive experience (preview) for managing the end-to-end machine learning lifecycle.
    </value>
  </data>
  <data name="Hub_Projects_Title" xml:space="preserve">
    <value>
      Projects
    </value>
  </data>
  <data name="Hub_Projects_Toolbar_Refresh" xml:space="preserve">
    <value>
      Refresh
    </value>
  </data>
  <data name="Hub_Projects_Toolbar_Add" xml:space="preserve">
    <value>
      Add
    </value>
  </data>
  <data name="Hub_Projects_LoadFailure" xml:space="preserve">
    <value>
      Associated projects of Azure AI hub with ID "{0}" could not be loaded.
    </value>
  </data>
  <data name="workspaceSKUPropertyLabel" xml:space="preserve">
    <value>
      Workspace edition
    </value>
  </data>
  <data name="workspaceUpgradeBannerText" xml:space="preserve">
    <value>Pokud chcete používat vizuální strojové učení, rozšířené automatizované strojové učení a správu kvót, upgradujte tento pracovní prostor na edici Enterprise (Preview).</value>
  </data>
  <data name="workspaceUpgradeQuickLinkBannerText" xml:space="preserve">
    <value>
      Upgrade this workspace to Enterprise edition (preview) to use UI-based tools for all skill levels and more.
    </value>
  </data>
  <data name="workspaceUpgradeQuickLinkPostUpgradeBannerText" xml:space="preserve">
    <value>
      Use the Enterprise edition (preview) to access UI-based tools for all skill levels, built-in MLOps and more
    </value>
  </data>
  <data name="workspaceUpgradeButtonText" xml:space="preserve">
    <value>
      Upgrade
    </value>
  </data>
  <data name="workspaceCancelUpgradeButtonText" xml:space="preserve">
    <value>
      Cancel
    </value>
  </data>
  <data name="workspaceConfirmUpgradeButtonText" xml:space="preserve">
    <value>
      Confirm Upgrade
    </value>
  </data>
  <data name="workspaceUpgradeLearnMore" xml:space="preserve">
    <value>
      Learn more
    </value>
  </data>
  <data name="workspaceUpgradeAboutOurPricing" xml:space="preserve">
    <value>
      about our pricing
    </value>
  </data>
  <data name="workspaceUpgradeConfirmationBoxTitle" xml:space="preserve">
    <value>
      Confirm workspace upgrade
    </value>
  </data>
  <data name="workspaceUpgradeConfirmationBoxContent" xml:space="preserve">
    <value>
      Upgrade workspace to access Enterprise (preview) capabilities. Visit our {0} for more information.
    </value>
  </data>
  <data name="workspaceUpgradePricingPage" xml:space="preserve">
    <value>
      pricing page
    </value>
  </data>
  <data name="workspaceUpgradeUpgrading" xml:space="preserve">
    <value>
      Upgrading Azure Machine Learning workspace {0}
    </value>
  </data>
  <data name="workspaceUpgradeUpgradeSucceeded" xml:space="preserve">
    <value>
      Upgrade Azure Machine Learning workspace {0} succeeded.
    </value>
  </data>
  <data name="workspaceUpgradeUpgradeFailed" xml:space="preserve">
    <value>
      An unexpected error occurred, please try upgrading your workspace again, or submitting a support ticket.
    </value>
  </data>
  <data name="workspaceUpgradeAuthorizationFailed" xml:space="preserve">
    <value>
      You currently do not have permissions to upgrade your workspace. Please contact your IT administrator.
    </value>
  </data>
  <data name="workspaceUpgradeUpgradingNotificationTitle" xml:space="preserve">
    <value>
      Workspace is currently upgrading
    </value>
  </data>
  <data name="workspaceUpgradeUpgradingNotificationContent" xml:space="preserve">
    <value>
      Your workspace {0} is upgrading from Basic to Enterprise
    </value>
  </data>
  <data name="workspaceUpgradeUpgradeCompleteNotificationTitle" xml:space="preserve">
    <value>
      Workspace Upgrade Complete
    </value>
  </data>
  <data name="workspaceUpgradeUpgradeCompleteNotificationContent" xml:space="preserve">
    <value>
      Your workspace {0} upgraded successfully.
    </value>
  </data>
  <data name="workspaceUpgradeUpgradeFailedNotificationTitle" xml:space="preserve">
    <value>
      Workspace Upgrade Failed
    </value>
  </data>
  <data name="workspaceUpgradeUpgradeFailedNotificationContent" xml:space="preserve">
    <value>
      Your workspace {0} did not upgrade successfully.
    </value>
  </data>
  <data name="workspaceCreateSKUPricingDetails" xml:space="preserve">
    <value>
      View full pricing details
    </value>
  </data>
  <data name="workspaceCreateSKUTooltip" xml:space="preserve">
    <value>
      The Enterprise SKU (preview) boosts productivity for all ML skill levels and offers robust MLOps capabilities to manage the complete ML lifecycle. THe Basic SKU workspace allows for open source development and a code-first experience.
    </value>
  </data>
  <data name="workspaceUpgradeQuickLinkLearnAboutEnterpriseEdition" xml:space="preserve">
    <value>
      Learn about Enterprise Edition (preview)
    </value>
  </data>
  <data name="workspaceUpgradeBulletPoint" xml:space="preserve">
    <value>
      You need an Enterprise edition (preview) workspace to view and set quotas on Enterprise workspaces.
    </value>
  </data>
  <data name="workspaceUpgradeSetQuotaOperationNotAllowed" xml:space="preserve">
    <value>
      This feature is disabled for you. Please upgrade to an Enterprise edition (preview) workspace to configure quotas.
    </value>
  </data>
  <data name="workspaceLoadFailure" xml:space="preserve">
    <value>
      Workspace with ID "{0}" could not be loaded.
    </value>
  </data>
  <data name="automationLink" xml:space="preserve">
    <value>Stáhnout šablonu pro automatizaci</value>
  </data>
  <data name="basics" xml:space="preserve">
    <value>Základy</value>
  </data>
  <data name="basicsBladeHubDetailsIntro" xml:space="preserve">
    <value>Pokud chcete spravovat nasazené prostředky a náklady, vyberte předplatné. Ve skupinách prostředků, jako jsou složky, můžete uspořádat a spravovat všechny prostředky. Centrum AI je prostředí pro spolupráci, ve které tým může sdílet práci na projektu, koncové body modelu, výpočetní připojení, (datová) připojení či nastavení zabezpečení a řídit využití.</value>
  </data>
  <data name="basicsBladeProjectDetailsIntro" xml:space="preserve">
    <value>Select a subscription and resource group to organize this and other resources, inherit access, and attribute cost.</value>
  </data>
  <data name="basicsBladeDetailsIntro" xml:space="preserve">
    <value>Každý pracovní prostor musí být přiřazený k předplatnému Azure, což je místo, kde probíhá fakturace. Skupiny prostředků, jako jsou složky, se používají k uspořádání a správě prostředků, včetně pracovního prostoru, který se chystáte vytvořit.</value>
  </data>
  <data name="basicsRegistryBladeDetailsIntro" xml:space="preserve">
    <value>Každý registr musí být přiřazený k předplatnému Azure, což je místo, kde probíhá fakturace. Skupiny prostředků, jako jsou složky, se používají k uspořádání a správě prostředků, včetně registru, který se chystáte vytvořit.</value>
  </data>
  <data name="basicsBladeInstanceIntro" xml:space="preserve">
    <value>Nakonfigurujte základní nastavení pracovního prostoru, jako jsou připojení k úložišti, ověřování, kontejner a další.</value>
  </data>
  <data name="basicsBladeRegistryInstanceIntro" xml:space="preserve">
    <value>Nakonfigurujte základní nastavení registru, jako je jeho název a popis.</value>
  </data>
  <data name="basicsTabTitle" xml:space="preserve">
    <value>Základy</value>
  </data>
  <data name="buttonCreate" xml:space="preserve">
    <value>Vytvořit</value>
  </data>
  <data name="buttonNext" xml:space="preserve">
    <value>Další: {0}</value>
    <comment>0: step name</comment>
  </data>
  <data name="buttonNextPlaceholder" xml:space="preserve">
    <value>Další &gt;</value>
  </data>
  <data name="buttonPrevious" xml:space="preserve">
    <value>&lt; Předchozí</value>
  </data>
  <data name="buttonReviewCreate" xml:space="preserve">
    <value>Zkontrolovat a vytvořit</value>
  </data>
  <data name="createBladeWorkspaceDetails" xml:space="preserve">
    <value>Podrobnosti pracovního prostoru</value>
  </data>
  <data name="CreateBlade_Workspace_Type" xml:space="preserve">
    <value>Typ pracovního prostoru</value>
  </data>
  <data name="createLocationLabelDefault" xml:space="preserve">
    <value>Lokalita</value>
  </data>
  <data name="createLocationLabelRegion" xml:space="preserve">
    <value>Oblast</value>
  </data>
  <data name="createLocationLabelPrimaryRegion" xml:space="preserve">
    <value>Primární oblast</value>
  </data>
  <data name="createResourceGroup" xml:space="preserve">
    <value>Skupina prostředků</value>
  </data>
  <data name="createResourceGroupCreateNewPlaceholder" xml:space="preserve">
    <value />
  </data>
  <data name="createResourceGroupTitle" xml:space="preserve">
    <value>Skupina prostředků</value>
  </data>
  <data name="createSubscriptionLabel" xml:space="preserve">
    <value>Předplatné</value>
  </data>
  <data name="createTagsTabIntro" xml:space="preserve">
    <value>Značky jsou dvojice název-hodnota, které umožňují kategorizovat prostředky a zobrazovat souhrnnou fakturaci. Stačí k tomu u několika prostředků a skupin prostředků použít stejnou značku.</value>
  </data>
  <data name="createTagsTabLearnMoreText" xml:space="preserve">
    <value>Další informace o značkách</value>
  </data>
  <data name="createTagsTabTitle" xml:space="preserve">
    <value>Značky</value>
  </data>
  <data name="createTagsTabUpdateNotice" xml:space="preserve">
    <value>Poznámka: Pokud vytvoříte značky a pak na jiných kartách změníte nastavení prostředku, značky se automaticky aktualizují.</value>
  </data>
  <data name="createTemplateValidationError" xml:space="preserve">
    <value>Ověření neproběhlo úspěšně. Kliknutím sem zobrazíte podrobnosti.</value>
  </data>
  <data name="createTemplateValidationInProgress" xml:space="preserve">
    <value>Probíhá konečné ověření...</value>
  </data>
  <data name="createTemplateValidationSuccess" xml:space="preserve">
    <value>Ověření proběhlo úspěšně.</value>
  </data>
  <data name="detailsLabel" xml:space="preserve">
    <value>Podrobnosti o prostředku</value>
  </data>
  <data name="instanceLabel" xml:space="preserve">
    <value>Podrobnosti o instancích</value>
  </data>
  <data name="learnMore" xml:space="preserve">
    <value>Další informace</value>
  </data>
  <data name="location" xml:space="preserve">
    <value>Umístění</value>
  </data>
  <data name="multipleInvalidTabErrorMessage" xml:space="preserve">
    <value>Následující karty se nepovedlo ověřit: {0}. Chybí potřebné informace, nebo nejsou platné.</value>
  </data>
  <data name="newResource" xml:space="preserve">
    <value>(nový) {0}</value>
  </data>
  <data name="newResourceCapitalized" xml:space="preserve">
    <value>(Nové) {0}</value>
  </data>
  <data name="none" xml:space="preserve">
    <value>Žádná</value>
  </data>
  <data name="singleInvalidTabErrorMessage" xml:space="preserve">
    <value>Následující kartu se nepovedlo ověřit: {0}. Chybí potřebné informace, nebo nejsou platné.</value>
  </data>
  <data name="summaryTabTitle" xml:space="preserve">
    <value>Zkontrolovat a vytvořit</value>
  </data>
  <data name="tabValidationErrors" xml:space="preserve">
    <value>Ověřování nebylo úspěšné. Buď chybí požadované informace, nebo nejsou platné.</value>
  </data>
  <data name="tags" xml:space="preserve">
    <value>Značky</value>
  </data>
  <data name="PrivateEndpoints_filterByName" xml:space="preserve">
    <value>Filtrovat podle názvu...</value>
  </data>
  <data name="PrivateEndpoints_label" xml:space="preserve">
    <value>Připojení privátních koncových bodů</value>
  </data>
  <data name="PrivateEndpoints_Toolbar_refresh" xml:space="preserve">
    <value>Aktualizovat</value>
  </data>
  <data name="PrivateEndpoints_GridColumns_name" xml:space="preserve">
    <value>Název připojení</value>
  </data>
  <data name="PrivateEndpoints_GridColumns_privateEndpoint" xml:space="preserve">
    <value>Privátní koncový bod</value>
  </data>
  <data name="PrivateEndpoints_GridColumns_status" xml:space="preserve">
    <value>Stav připojení</value>
  </data>
  <data name="PrivateEndpoints_GridColumns_description" xml:space="preserve">
    <value>Popis</value>
  </data>
  <data name="PrivateEndpoints_Status_pending" xml:space="preserve">
    <value>Čeká na dokončení</value>
  </data>
  <data name="PrivateEndpoints_Status_approved" xml:space="preserve">
    <value>Schváleno</value>
  </data>
  <data name="PrivateEndpoints_Status_rejected" xml:space="preserve">
    <value>Odmítnuto</value>
  </data>
  <data name="PrivateEndpoints_Status_disconnected" xml:space="preserve">
    <value>Odpojeno</value>
  </data>
  <data name="PrivateEndpoints_menuText" xml:space="preserve">
    <value>Sítě</value>
  </data>
  <data name="PrivateEndpoints_Commands_addPrivateEndpoint" xml:space="preserve">
    <value>Privátní koncový bod</value>
  </data>
  <data name="PrivateEndpoints_Commands_approve" xml:space="preserve">
    <value>Schválit</value>
  </data>
  <data name="PrivateEndpoints_Commands_ApproveNotifications_Failure_message" xml:space="preserve">
    <value>Nepovedlo se schválit {0} z celkového počtu {1} připojení privátních koncových bodů.</value>
  </data>
  <data name="PrivateEndpoints_Commands_ApproveNotifications_Failure_title" xml:space="preserve">
    <value>Nepovedlo se schválit připojení privátních koncových bodů</value>
  </data>
  <data name="PrivateEndpoints_Commands_ApproveNotifications_InProgress_message" xml:space="preserve">
    <value>Schvaluje se tento počet připojení privátních koncových bodů: {0}</value>
  </data>
  <data name="PrivateEndpoints_Commands_ApproveNotifications_InProgress_title" xml:space="preserve">
    <value>Schvalují se připojení privátních koncových bodů...</value>
  </data>
  <data name="PrivateEndpoints_Commands_ApproveNotifications_Success_message" xml:space="preserve">
    <value>Tento počet připojení privátních koncových bodů se úspěšně schválil: {0}</value>
  </data>
  <data name="PrivateEndpoints_Commands_ApproveNotifications_Success_title" xml:space="preserve">
    <value>Připojení privátních koncových bodů se úspěšně schválila</value>
  </data>
  <data name="PrivateEndpoints_Commands_Approve_messagePlural" xml:space="preserve">
    <value>Chcete schválit tento počet vybraných připojení: {0}?</value>
  </data>
  <data name="PrivateEndpoints_Commands_Approve_messageSingular" xml:space="preserve">
    <value>Chcete schválit připojení {0}?</value>
  </data>
  <data name="PrivateEndpoints_Commands_Approve_title" xml:space="preserve">
    <value>Schválit připojení</value>
  </data>
  <data name="PrivateEndpoints_Commands_DeleteNotifications_Failure_message" xml:space="preserve">
    <value>Nepovedlo se odstranit {0} z celkového počtu {1} připojení privátních koncových bodů.</value>
  </data>
  <data name="PrivateEndpoints_Commands_DeleteNotifications_Failure_title" xml:space="preserve">
    <value>Nepovedlo se odstranit připojení privátních koncových bodů</value>
  </data>
  <data name="PrivateEndpoints_Commands_DeleteNotifications_InProgress_message" xml:space="preserve">
    <value>Odstraňuje se tento počet připojení privátních koncových bodů: {0}</value>
  </data>
  <data name="PrivateEndpoints_Commands_DeleteNotifications_InProgress_title" xml:space="preserve">
    <value>Odstraňují se připojení privátních koncových bodů...</value>
  </data>
  <data name="PrivateEndpoints_Commands_DeleteNotifications_Success_message" xml:space="preserve">
    <value>Tento počet připojení privátních koncových bodů se úspěšně odstranil: {0}</value>
  </data>
  <data name="PrivateEndpoints_Commands_DeleteNotifications_Success_title" xml:space="preserve">
    <value>Připojení privátních koncových bodů se úspěšně odstranila</value>
  </data>
  <data name="PrivateEndpoints_Commands_description" xml:space="preserve">
    <value>Popis</value>
  </data>
  <data name="PrivateEndpoints_Commands_reject" xml:space="preserve">
    <value>Odmítnout</value>
  </data>
  <data name="PrivateEndpoints_Commands_RejectNotifications_Failure_message" xml:space="preserve">
    <value>Nepovedlo se zamítnout {0} z celkového počtu {1} připojení privátních koncových bodů.</value>
  </data>
  <data name="PrivateEndpoints_Commands_RejectNotifications_Failure_title" xml:space="preserve">
    <value>Nepovedlo se zamítnout připojení privátních koncových bodů</value>
  </data>
  <data name="PrivateEndpoints_Commands_RejectNotifications_InProgress_message" xml:space="preserve">
    <value>Zamítá se tento počet připojení privátních koncových bodů: {0}</value>
  </data>
  <data name="PrivateEndpoints_Commands_RejectNotifications_InProgress_title" xml:space="preserve">
    <value>Zamítají se připojení privátních koncových bodů...</value>
  </data>
  <data name="PrivateEndpoints_Commands_RejectNotifications_Success_message" xml:space="preserve">
    <value>Tento počet připojení privátních koncových bodů se úspěšně zamítl: {0}</value>
  </data>
  <data name="PrivateEndpoints_Commands_RejectNotifications_Success_title" xml:space="preserve">
    <value>Připojení privátních koncových bodů se úspěšně zamítla</value>
  </data>
  <data name="PrivateEndpoints_Commands_Reject_messagePlural" xml:space="preserve">
    <value>Chcete zamítnout tento počet vybraných připojení: {0}?</value>
  </data>
  <data name="PrivateEndpoints_Commands_Reject_messageSingular" xml:space="preserve">
    <value>Chcete zamítnout připojení {0}?</value>
  </data>
  <data name="PrivateEndpoints_Commands_Reject_title" xml:space="preserve">
    <value>Odmítnout připojení</value>
  </data>
  <data name="PrivateEndpoints_Commands_remove" xml:space="preserve">
    <value>Odebrat</value>
  </data>
  <data name="PrivateEndpoints_Commands_Remove_messagePlural" xml:space="preserve">
    <value>Chcete odstranit tento počet vybraných připojení: {0}?</value>
  </data>
  <data name="PrivateEndpoints_Commands_Remove_messageSingular" xml:space="preserve">
    <value>Chcete odstranit připojení {0}?</value>
  </data>
  <data name="PrivateEndpoints_Commands_Remove_title" xml:space="preserve">
    <value>Odstranit připojení</value>
  </data>
  <data name="PrivateEndpoints_Create_validationErrorFormat" xml:space="preserve">
    <value>{0}: {1}</value>
    <comment>0: error code, 1: error message</comment>
  </data>
  <data name="PrivateEndpoints_filterByStatus" xml:space="preserve">
    <value>Filtrovat podle stavu připojení...</value>
  </data>
  <data name="PrivateEndpoints_genericErrorMessage" xml:space="preserve">
    <value>Při zpracování vaší žádosti došlo k chybě. Zkuste to za chvíli znovu.</value>
  </data>
  <data name="PrivateEndpoints_Grid_StatusFilter_all" xml:space="preserve">
    <value>Všechny stavy připojení</value>
  </data>
  <data name="PrivateEndpoints_Grid_StatusFilter_itemFormat" xml:space="preserve">
    <value>{0} ({1})</value>
    <comment>0: status name, 1: count</comment>
  </data>
  <data name="PrivateEndpoints_Grid_StatusFilter_some" xml:space="preserve">
    <value>Počet vybraných stavů připojení: {0}</value>
    <comment>0: a number</comment>
  </data>
  <data name="BasicsTab_InfoText_AdditionalResourceInfoText" xml:space="preserve">
    <value>Tyto prostředky se pro vás do pracovního prostoru přidávají automaticky, pokud jsou k dispozici v dané oblasti: </value>
  </data>
  <data name="BasicsTab_InfoText_AzureApplicationInsights" xml:space="preserve">
    <value>Azure Application Insights</value>
  </data>
  <data name="BasicsTab_InfoText_AzureKeyVault" xml:space="preserve">
    <value>Azure Key Vault</value>
  </data>
  <data name="BasicsTab_InfoText_AzureStorage" xml:space="preserve">
    <value>Azure Storage</value>
  </data>
  <data name="BasicsTab_Region_BalloonContent" xml:space="preserve">
    <value>Cílové výpočetní objekty se dají vytvořit jen ve stejné oblasti, v jaké se nachází pracovní prostor. Ujistěte se, že vybraná oblast má řady virtuálních počítačů potřebné pro cílové výpočetní objekty pracovního prostoru.</value>
  </data>
  <data name="BasicsTab_Region_LearnMoreComputeTargets" xml:space="preserve">
    <value>Další informace o cílových výpočetních objektech</value>
  </data>
  <data name="BasicsTab_Region_ViewAvailableVirtualMachines" xml:space="preserve">
    <value>Zobrazit řady virtuálních počítačů dostupné podle oblasti</value>
  </data>
  <data name="BasicsTab_ResourceGroup_BalloonContent" xml:space="preserve">
    <value>Skupina prostředků je kolekce prostředků, které sdílejí stejný životní cyklus, oprávnění a zásady.</value>
  </data>
  <data name="BasicsTab_Subscription_BalloonContent" xml:space="preserve">
    <value>Všechny prostředky v předplatném Azure se fakturují dohromady.</value>
  </data>
  <data name="BasicsTab_WarningText_WorkspaceErrorMessageText" xml:space="preserve">
    <value>Vybrané předplatné nemá oprávnění k registraci tohoto poskytovatele prostředků.</value>
  </data>
  <data name="BasicsTab_WorkspaceDetails" xml:space="preserve">
    <value>Podrobnosti pracovního prostoru</value>
  </data>
  <data name="BasicsTab_WorkspaceHubDetails" xml:space="preserve">
    <value>Podrobnosti o prostředku</value>
  </data>
  <data name="BasicsTab_RegistryDetails" xml:space="preserve">
    <value>Podrobnosti o registru</value>
  </data>
  <data name="BasicsTab_Organization" xml:space="preserve">
    <value>Organizace</value>
  </data>
  <data name="BasicsTab_WorkspaceEdition_ViewFullPricingDetails" xml:space="preserve">
    <value>Zobrazit všechny podrobnosti o cenách</value>
  </data>
  <data name="BasicsTab_WorkspaceName_BalloonContent" xml:space="preserve">
    <value>Název pracovního prostoru musí být ve skupině prostředků jedinečný.</value>
  </data>
  <data name="BasicsTab_HubName_BalloonContent" xml:space="preserve">
    <value>Název centra AI</value>
  </data>
  <data name="BasicsTab_ProjectName_BalloonContent" xml:space="preserve">
    <value>Name of the AI project</value>
  </data>
  <data name="BasicsTab_RegistryName_BalloonContent" xml:space="preserve">
    <value>Název registru musí být ve skupině prostředků jedinečný.</value>
  </data>
  <data name="BasicsTab_AIServiceSectionHeader" xml:space="preserve">
    <value>Základní modely Azure AI Služeb</value>
  </data>
  <data name="BasicsTab_hubSectionHeader" xml:space="preserve">
    <value>Share security, connectivity, compute</value>
  </data>
  <data name="BasicsTab_hubSectionContent" xml:space="preserve">
    <value>Projects are grouped by a hub, which provides security configurations, pre-configured connectivity with other Azure resources, compute, storage, and quota.</value>
  </data>
  <data name="CreateBlade_Network_connectivityMethod" xml:space="preserve">
    <value>Metoda připojení</value>
  </data>
  <data name="CreateBlade_Network_ConnectivityMethod_allNetworks" xml:space="preserve">
    <value>Povolit veřejný přístup ze všech sítí</value>
  </data>
  <data name="CreateBlade_Network_title" xml:space="preserve">
    <value>Sítě</value>
  </data>
  <data name="StorageTab_Title" xml:space="preserve">
    <value>Úložiště</value>
  </data>
  <data name="StorageTab_Header" xml:space="preserve">
    <value>Konfigurace způsobu ukládání dat</value>
  </data>
  <data name="StorageTab_LogsAndDockerHeader" xml:space="preserve">
    <value>Protokoly a image Dockeru</value>
  </data>
  <data name="StorageTab_Description" xml:space="preserve">
    <value>As users work in Azure AI Foundry, uploaded data, stored credentials and generated artifacts like logs are stored.</value>
  </data>
  <data name="EncryptionTab_Title" xml:space="preserve">
    <value>Šifrování</value>
  </data>
  <data name="IdentityTab_Title" xml:space="preserve">
    <value>Identita</value>
  </data>
  <data name="AdvancedTab_Key_KeyVaultChangeControl_action" xml:space="preserve">
    <value>Kliknutím vyberte klíč.</value>
  </data>
  <data name="AdvancedTab_Key_KeyVaultChangeControl_key" xml:space="preserve">
    <value>Klíč: {key}</value>
  </data>
  <data name="AdvancedTab_Key_KeyVaultChangeControl_keyVault" xml:space="preserve">
    <value>Trezor klíčů: {keyvault}</value>
  </data>
  <data name="AdvancedTab_Key_KeyVaultChangeControl_label" xml:space="preserve">
    <value>Trezor klíčů</value>
  </data>
  <data name="AdvancedTab_Key_KeyVaultChangeControl_purgeProtectionRequired" xml:space="preserve">
    <value>Při používání vlastního šifrovacího klíče musí mít vaše Azure Key Vault povolenou ochranu před vymazáním, aby se chránila před náhodnou ztrátou přístupu k datům.</value>
  </data>
  <data name="AdvancedTab_Key_KeyVaultChangeControl_required" xml:space="preserve">
    <value>Klíč je povinný.</value>
  </data>
  <data name="AdvancedTab_Key_KeyVaultChangeControl_title" xml:space="preserve">
    <value>Trezor klíčů a klíč</value>
  </data>
  <data name="AdvancedTab_Section_DataEncryption_customerManaged" xml:space="preserve">
    <value>Klíče spravované zákazníkem</value>
  </data>
  <data name="AdvancedTab_Section_DataEncryption_header" xml:space="preserve">
    <value>Vaše data se ve výchozím nastavení šifrují pomocí klíčů spravovaných Microsoftem. Pokud chcete mít nad svými daty větší kontrolu, můžete použít vlastní klíč pro šifrování. {0}</value>
  </data>
  <data name="AdvancedTab_Section_DataEncryption_microsoftManaged" xml:space="preserve">
    <value>Klíče spravované Microsoftem</value>
  </data>
  <data name="AdvancedTab_Section_DataEncryption_title" xml:space="preserve">
    <value>Šifrování dat</value>
  </data>
  <data name="AdvancedTab_Section_DataEncryption_Type_label" xml:space="preserve">
    <value>Šifrovat data pomocí klíče spravovaného zákazníkem</value>
  </data>
  <data name="AdvancedTab_Section_DataEncryption_ServiceSide_label" xml:space="preserve">
    <value>Použít šifrování na straně služby ({0})</value>
  </data>
  <data name="AdvancedTab_Section_DataEncryption_WarningMessage" xml:space="preserve">
    <value>Po vytvoření pracovního prostoru nelze změnit typ šifrovacího klíče z klíče spravovaného společností Microsoft na klíč spravovaný zákazníkem a naopak.</value>
  </data>
  <data name="AdvancedTab_Section_DataEncryption_LinkText" xml:space="preserve">
    <value>Přečtěte si další informace o šifrování klíčů spravovaných zákazníkem.</value>
  </data>
  <data name="AdvancedTab_Section_DataImpact_HBI_label" xml:space="preserve">
    <value>Pracovní prostor s vysokým dopadem na firmu</value>
  </data>
  <data name="AdvancedTab_Section_DataImpact_header" xml:space="preserve">
    <value>Pokud váš pracovní prostor obsahuje citlivá data, můžete určit pracovní prostor s vysokým dopadem na firmu. To umožňuje řídit množství dat, která Microsoft shromažďuje pro diagnostické účely, a povolit další šifrování v prostředích spravovaných Microsoftem.</value>
  </data>
  <data name="AdvancedTab_Section_DataImpact_title" xml:space="preserve">
    <value>Dopad dat</value>
  </data>
  <data name="AdvancedTab__Key_KeyVaultChangeControl_version" xml:space="preserve">
    <value>Verze: {version}</value>
  </data>
  <data name="RegionsTab_Title" xml:space="preserve">
    <value>Oblasti</value>
  </data>
  <data name="RegionsTab_Description" xml:space="preserve">
    <value>Vyberte oblasti, ve kterých aktuálně máte nebo plánujete vytvářet pracovní prostory AzureML a používat prostředky z tohoto registru. Další oblasti můžete do registru přidat později.</value>
  </data>
  <data name="RegionsTab_AdditionRegionLabel" xml:space="preserve">
    <value>Další oblasti</value>
  </data>
  <data name="noContent" xml:space="preserve">
    <value>Žádný obsah</value>
  </data>
  <data name="BasicsTab_WorkspaceEdition_Basic" xml:space="preserve">
    <value>Basic</value>
  </data>
  <data name="BasicsTab_WorkspaceEdition_Enterprise" xml:space="preserve">
    <value>Enterprise</value>
  </data>
  <data name="Commands_delete" xml:space="preserve">
    <value>Odstranit</value>
  </data>
  <data name="CreateBlade_Network_displayPrivateDnsZone" xml:space="preserve">
    <value>Zóna Privátního DNS</value>
  </data>
  <data name="CreateBlade_Network_displayRegion" xml:space="preserve">
    <value>Oblast</value>
  </data>
  <data name="CreateBlade_Network_displayResourceGroup" xml:space="preserve">
    <value>Skupina prostředků</value>
  </data>
  <data name="CreateBlade_Network_displaySubnet" xml:space="preserve">
    <value>Podsíť</value>
  </data>
  <data name="CreateBlade_Network_displaySubscription" xml:space="preserve">
    <value>Předplatné</value>
  </data>
  <data name="CreateBlade_Network_displayTargetResourceType" xml:space="preserve">
    <value>Typ cílového prostředku</value>
  </data>
  <data name="CreateBlade_Network_overviewDescription" xml:space="preserve">
    <value>Ke svému pracovnímu prostoru se můžete připojit buď veřejně, nebo privátně pomocí privátního koncového bodu.</value>
  </data>
  <data name="CreateBlade_Network_networkIsolationDescription" xml:space="preserve">
    <value>Zvolte typ izolace sítě, kterou potřebujete pro svůj pracovní prostor, od neizolované vůbec po zcela samostatnou virtuální síť spravovanou Azure Machine Learning.</value>
  </data>
  <data name="CreateBlade_Network_Hub_networkIsolationDescription" xml:space="preserve">
    <value>Projekty přidružené k centru Azure AI sdílejí síť a mají přístup k prostředkům ve vaší virtuální síti bez další konfigurace. Zvolte typ izolace sítě, kterou potřebujete, od vůbec neizolované po zcela samostatnou virtuální síť spravovanou Azure Machine Learning.</value>
  </data>
  <data name="CreateBlade_Network_networkIsolationLearnMore" xml:space="preserve">
    <value>Další informace o izolaci spravované sítě</value>
  </data>
  <data name="CreateBlade_Network_registryOverviewDescription" xml:space="preserve">
    <value>K registru se můžete připojit buď veřejně, nebo privátně pomocí privátního koncového bodu.</value>
  </data>
  <data name="CreateBlade_Network_overviewTitle" xml:space="preserve">
    <value>Připojení k síti</value>
  </data>
  <data name="CreateBlade_Network_privateEndpointDescription" xml:space="preserve">
    <value>Vytvořte privátní koncový bod a povolíte privátní připojení k tomuto prostředku.</value>
  </data>
  <data name="CreateBlade_Network_PrivateEndpoints_add" xml:space="preserve">
    <value>Přidat</value>
  </data>
  <data name="CreateBlade_Network_PrivateEndpoints_clickToAdd" xml:space="preserve">
    <value>Pokud chcete vytvořit privátní koncový bod, klikněte na přidat</value>
  </data>
  <data name="CreateBlade_Network_PrivateEndpoints_locationMismatch" xml:space="preserve">
    <value>Pracovní prostor ({0}) a připojení privátního koncového bodu ({1}) musí být ve stejném umístění, aby bylo možné ve virtuální síti správně přistupovat k instanci výpočtu a clusterům.</value>
    <comment>0: location, 1: location</comment>
  </data>
  <data name="CreateBlade_Network_PrivateEndpoints_name" xml:space="preserve">
    <value>Název</value>
  </data>
  <data name="CreateBlade_Network_PrivateEndpoints_nameAndSubResource" xml:space="preserve">
    <value>{0} ({1})</value>
    <comment>0 = the private endpoint name, 1 = the sub resource name.</comment>
  </data>
  <data name="CreateBlade_Network_PrivateEndpoints_nameSubResourceAndDns" xml:space="preserve">
    <value>{0} ({1}) ({2})</value>
    <comment>0 = the private endpoint name, 1 = the sub resource name, 2 = the dns zone name.</comment>
  </data>
  <data name="CreateBlade_Network_PrivateEndpoints_subResourceHelp" xml:space="preserve">
    <value>Toto je konkrétní dílčí prostředek pro nový pracovní prostor, ke kterému bude mít tento privátní koncový bod přístup.</value>
  </data>
  <data name="CreateBlade_Network_PrivateEndpoints_registrySubResourceHelp" xml:space="preserve">
    <value>Toto je konkrétní dílčí prostředek pro nový registr, ke kterému bude mít tento privátní koncový bod přístup.</value>
  </data>
  <data name="CreateBlade_Network_PrivateEndpoints_subResourceLabel" xml:space="preserve">
    <value>Dílčí prostředek pracovního prostoru</value>
  </data>
  <data name="CreateBlade_Network_PrivateEndpoints_registrySubResourceLabel" xml:space="preserve">
    <value>Dílčí prostředek registru</value>
  </data>
  <data name="CreateBlade_Network_PrivateEndpoints_title" xml:space="preserve">
    <value>Privátní koncové body</value>
  </data>
  <data name="CreateBlade_Network_PrivateEndpoints_type" xml:space="preserve">
    <value>Privátní koncový bod</value>
  </data>
  <data name="CreateBlade_Network_privateEndpointTitle" xml:space="preserve">
    <value>Privátní koncový bod</value>
  </data>
  <data name="CreateBlade_Network_WorkspaceInternetOutbound_PrivateNetworkSettings_title" xml:space="preserve">
    <value>Nastavení privátní sítě</value>
  </data>
  <data name="CreateBlade_Network_WorkspaceInternetOutbound_PrivateNetworkSettings_description" xml:space="preserve">
    <value>Přidejte privátní koncové body, které uživatelé můžou používat pro přístup k vašemu pracovnímu prostoru, a zvolte, jak spravovat odchozí přístup z pracovního prostoru k věcem, jako jsou účty úložiště, trezory klíčů a registry.</value>
  </data>
  <data name="CreateBlade_Network_WorkspaceInternetOutbound_OutboundAccess_virtualNetworkManagedByAML" xml:space="preserve">
    <value>Použití virtuální sítě spravované Azure Machine Learning</value>
  </data>
  <data name="CreateBlade_Network_WorkspaceInternetOutbound_OutboundAccess_virtualNetworkManagedByAMLDescription" xml:space="preserve">
    <value>Pro přístup k privátním prostředkům, jako jsou úložiště, poznámkové bloky a prostředí nasazení, se vyžadují privátní koncové body. Můžete sem také přidat další cíle privátních propojení pro vlastní scénáře.</value>
  </data>
  <data name="CreateBlade_Network_WorkspaceInternetOutbound_OutboundAccess_virtualNetworkManagedByAMLLearnMore" xml:space="preserve">
    <value>Další informace o požadovaných cílech privátních propojení</value>
  </data>
  <data name="CreateBlade_Network_WorkspaceInternetOutbound_OutboundAccess_useMyOwnVirtualNetwork" xml:space="preserve">
    <value>Použít vlastní virtuální síť</value>
  </data>
  <data name="CreateBlade_Network_WorkspaceApprovedOutbound_OutboundAccess_azureFirewallSku" xml:space="preserve">
    <value>Azure Firewall SKU</value>
  </data>
  <data name="CreateBlade_Network_WorkspaceApprovedOutbound_OutboundAccess_azureFirewallSkuDescription" xml:space="preserve">
    <value>Select from Basic or Standard SKU for the Azure Firewall deployment. For more information on Azure Firewall, see</value>
  </data>
  <data name="CreateBlade_Network_WorkspaceApprovedOutbound_OutboundAccess_azureFirewallSkuPricingText" xml:space="preserve">
    <value>Pricing</value>
  </data>
  <data name="CreateBlade_Network_WorkspaceOutboundAccess_provisionManagedVirtualNetwork" xml:space="preserve">
    <value>Provision managed virtual network</value>
  </data>
  <data name="CreateBlade_Network_WorkspaceOutboundAccess_provisionManagedVirtualNetworkSummaryTitle" xml:space="preserve">
    <value>Provision managed virtual network</value>
  </data>
  <data name="CreateBlade_Network_WorkspaceOutboundAccess_provisionManagedVirtualNetworkInfo" xml:space="preserve">
    <value>Managed virtual network will be provisioned at workspace creation. Charges will be incurred for network resources, such as private endpoint.</value>
  </data>
  <data name="CreateBlade_Network_WorkspaceApprovedOutbound_OutboundAccess_requiredTargetDescription" xml:space="preserve">
    <value>K dispozici je několik odchozích cílů přidaných Azure Machine Learning, které jsou potřeba pro přístup k pracovnímu prostoru, jako jsou úložiště, poznámkové bloky a prostředí nasazení.</value>
  </data>
  <data name="CreateBlade_Network_WorkspaceApprovedOutbound_OutboundAccess_requiredTargetLearnMore" xml:space="preserve">
    <value>Další informace o požadovaných cílech</value>
  </data>
  <data name="CreateBlade_Network_WorkspaceApprovedOutbound_OutboundAccess_recommendedTargetDescription" xml:space="preserve">
    <value>Pro váš pracovní prostor se doporučuje několik volitelných odchozích cílů ve scénářích, jako jsou AutoML a Popisky dat. Můžete je upravit nebo odstranit,</value>
  </data>
  <data name="CreateBlade_Network_WorkspaceApprovedOutbound_OutboundAccess_recommendedTargetLearnMore" xml:space="preserve">
    <value>Další informace o doporučených cílech</value>
  </data>
  <data name="CreateBlade_Network_WorkspaceInternetOutbound_targetDescription" xml:space="preserve">
    <value>Aby měl váš pracovní prostor přístup k privátním prostředkům, jako je například úložiště, je vyžadováno několik privátních koncových bodů. Můžete sem také přidat další cíle privátních propojení pro vlastní scénáře.</value>
  </data>
  <data name="CreateBlade_Network_WorkspaceInternetOutbound_targetLearnMoreLink" xml:space="preserve">
    <value>Další informace o požadovaném cíli privátního propojení</value>
  </data>
  <data name="CreateBlade_Network_outboundRulesGridEmptyMessage" xml:space="preserve">
    <value>Chybí požadovaná pravidla odchozího přístupu.</value>
  </data>
  <data name="CreateBlade_Network_outboundRulesPublicInternetEnabledMessage" xml:space="preserve">
    <value>Pokud je povolený odchozí přístup k veřejnému internetu, nejsou nutná žádná odchozí pravidla.</value>
  </data>
  <data name="CreateBlade_Network_outboundRulesNotLoadedMessage" xml:space="preserve">
    <value>Pokud chcete vytvořit odchozí pravidla, nakonfigurujte základní nastavení.</value>
  </data>
  <data name="CreateBlade_Network_WorkspacePrivateOutbound_addUserDefinedOutboundRuleText" xml:space="preserve">
    <value>Můžete sem také přidat vlastní odchozí cíle pro vlastní scénáře.</value>
  </data>
  <data name="CreateBlade_Network_WorkspacePrivateOutbound_userDefinedOutboundRules" xml:space="preserve">
    <value>Uživatelem definovaná odchozí pravidla</value>
  </data>
  <data name="CreateBlade_Network_WorkspacePrivateOutbound_recommendedOutboundRules" xml:space="preserve">
    <value>Doporučená odchozí pravidla</value>
  </data>
  <data name="CreateBlade_Network_WorkspacePrivateOutbound_requiredOutboundRules" xml:space="preserve">
    <value>Požadovaná odchozí pravidla</value>
  </data>
  <data name="CreateBlade_Network_WorkspacePrivateOutbound_dependencyOutboundRules" xml:space="preserve">
    <value>Pravidla odchozích přenosů závislostí</value>
  </data>
  <data name="CreateBlade_Network_workspaceApprovedOutbound_PrivateNetworkSettings_title" xml:space="preserve">
    <value>Nastavení exfiltrace privátní sítě a dat</value>
  </data>
  <data name="CreateBlade_Network_workspaceApprovedOutbound_PrivateNetworkSettings_description" xml:space="preserve">
    <value>Přidání privátních koncových bodů, pomocí kterých můžou uživatelé přistupovat k vašemu pracovnímu prostoru a spravovat odchozí cíle, ke kterým má pracovní prostor přístup</value>
  </data>
  <data name="CreateBlade_Network_workspaceInboundAccessTitle" xml:space="preserve">
    <value>Příchozí přístup k pracovnímu prostoru</value>
  </data>
  <data name="CreateBlade_Network_workspaceOutboundAccessTitle" xml:space="preserve">
    <value>Odchozí přístup pracovního prostoru</value>
  </data>
  <data name="CreateBlade_Network_WorkspaceOutboundAccess_connectionName" xml:space="preserve">
    <value>Název připojení</value>
  </data>
  <data name="CreateBlade_Network_WorkspaceOutboundAccess_parentRules" xml:space="preserve">
    <value>Nadřazená pravidla</value>
  </data>
  <data name="CreateBlade_Network_WorkspaceOutboundAccess_destinationType" xml:space="preserve">
    <value>Cílový typ</value>
  </data>
  <data name="CreateBlade_Network_WorkspaceOutboundAccess_disabledManagedVnetMessage" xml:space="preserve">
    <value>Securing your workspace with a managed network provides network isolation for outbound access from the Hub and managed computes. Once you enable managed virtual network isolation of your Azure AI, you can't disable it.</value>
  </data>
  <data name="CreateBlade_Network_WorkspaceOutboundAccess_disabledManagedVnetMessageLearnMore" xml:space="preserve">
    <value>Learn more</value>
  </data>
  <data name="CreateBlade_Network_WorkspaceOutboundAccess_status" xml:space="preserve">
    <value>Stav</value>
  </data>
  <data name="CreateBlade_Network_WorkspaceOutboundAccess_activeStatusMessage" xml:space="preserve">
    <value>Pravidlo bylo použito a bylo účinné.</value>
  </data>
  <data name="CreateBlade_Network_WorkspaceOutboundAccess_inactiveStatusMessage" xml:space="preserve">
    <value>Když se zřídí spravovaná síť, pravidlo se aktivuje.</value>
  </data>
  <data name="CreateBlade_Network_WorkspaceOutboundAccess_inactivePrivateEndpointStatusMessage" xml:space="preserve">
    <value>Pravidlo se aktivuje po zřízení spravované sítě, jinak zkontrolujte, jestli čeká na schválení cílového prostředku.</value>
  </data>
  <data name="CreateBlade_Network_WorkspaceOutboundAccess_destination" xml:space="preserve">
    <value>Cíl</value>
  </data>
  <data name="CreateBlade_Network_WorkspaceOutboundAccess_addUserDefinedOutboundRules" xml:space="preserve">
    <value>Přidání uživatelem definovaných pravidel odchozích přenosů</value>
  </data>
  <data name="CreateBlade_Network_WorkspaceOutboundAccess_deleteUserDefinedOutboundRules" xml:space="preserve">
    <value>Odstranit uživatelem definovaná odchozí pravidla</value>
  </data>
  <data name="CreateBlade_Network_networkIsolationTitle" xml:space="preserve">
    <value>Izolace sítě</value>
  </data>
  <data name="CreateBlade_Network_azureFirewallSku" xml:space="preserve">
    <value>Azure Firewall SKU</value>
  </data>
  <data name="CreateBlade_Network_NetworkIsolation_Public_title" xml:space="preserve">
    <value>Veřejné</value>
  </data>
  <data name="CreateBlade_Network_NetworkIsolation_Public_descriptionItemOne" xml:space="preserve">
    <value>K pracovnímu prostoru se přistupuje přes veřejný koncový bod.</value>
  </data>
  <data name="CreateBlade_Network_NetworkIsolation_Public_descriptionItemTwo" xml:space="preserve">
    <value>Výpočetní prostředky můžou přistupovat k veřejným prostředkům</value>
  </data>
  <data name="CreateBlade_Network_NetworkIsolation_Public_descriptionItemThree" xml:space="preserve">
    <value>Přesun odchozích dat je neomezený.</value>
  </data>
  <data name="CreateBlade_Network_NetworkIsolation_Disabled_title" xml:space="preserve">
    <value>Zakázáno</value>
  </data>
  <data name="CreateBlade_Network_NetworkIsolation_AllowInternetOutbound_title" xml:space="preserve">
    <value>Povolit odchozí připojení k internetu</value>
  </data>
  <data name="CreateBlade_Network_NetworkIsolation_AllowOnlyApprovedOutbound_title" xml:space="preserve">
    <value>Povolit pouze schválené odchozí</value>
  </data>
  <data name="CreateBlade_Network_NetworkIsolation_Public_learnMore" xml:space="preserve">
    <value>Další informace o veřejných sítích</value>
  </data>
  <data name="CreateBlade_Network_NetworkIsolation_PrivateInternetOutbound_title" xml:space="preserve">
    <value>Privátní s odchozím internetem</value>
  </data>
  <data name="CreateBlade_Network_NetworkIsolation_PrivateInternetOutbound_descriptionItemOne" xml:space="preserve">
    <value>K pracovnímu prostoru se přistupuje přes privátní koncový bod.</value>
  </data>
  <data name="CreateBlade_Network_NetworkIsolation_PrivateInternetOutbound_descriptionItemTwo" xml:space="preserve">
    <value>Výpočetní prostředky mají přístup k privátním prostředkům</value>
  </data>
  <data name="CreateBlade_Network_NetworkIsolation_PrivateInternetOutbound_descriptionItemThree" xml:space="preserve">
    <value>Přesun odchozích dat je neomezený.</value>
  </data>
  <data name="CreateBlade_Network_NetworkIsolation_PrivateInternetOutbound_learnMore" xml:space="preserve">
    <value>Další informace o privátních sítích</value>
  </data>
  <data name="CreateBlade_Network_NetworkIsolation_PrivateApprovedOutbound_title" xml:space="preserve">
    <value>Soukromá se schváleným odchozím</value>
  </data>
  <data name="CreateBlade_Network_NetworkIsolation_PrivateApprovedOutbound_descriptionItemOne" xml:space="preserve">
    <value>K pracovnímu prostoru se přistupuje přes privátní koncový bod.</value>
  </data>
  <data name="CreateBlade_Network_NetworkIsolation_PrivateApprovedOutbound_descriptionItemTwo" xml:space="preserve">
    <value>Výpočetní prostředky můžou přistupovat jen k povoleným prostředkům</value>
  </data>
  <data name="CreateBlade_Network_NetworkIsolation_PrivateApprovedOutbound_descriptionItemThree" xml:space="preserve">
    <value>Přesun odchozích dat je omezený na schválené cíle.</value>
  </data>
  <data name="CreateBlade_Network_NetworkIsolation_PrivateApprovedOutbound_learnMore" xml:space="preserve">
    <value>Další informace o ochraně před exfiltrací dat</value>
  </data>
  <data name="newResourceFormatCaps" xml:space="preserve">
    <value>(Nové) {0}</value>
    <comment>0 = the name of the resource</comment>
  </data>
  <data name="CreateBlade_Network_PrivateEndpoints_noContent" xml:space="preserve">
    <value>-</value>
  </data>
  <data name="AdvancedTab_Key_KeyVaultChangeControl_version" xml:space="preserve">
    <value>Verze: {version}</value>
  </data>
  <data name="CreateBlade_Advanced_Review_customerManagedKeys" xml:space="preserve">
    <value>Klíče spravované zákazníkem</value>
  </data>
  <data name="CreateBlade_Advanced_Review_disabled" xml:space="preserve">
    <value>Zakázáno</value>
  </data>
  <data name="CreateBlade_Advanced_Review_enabled" xml:space="preserve">
    <value>Povoleno</value>
  </data>
  <data name="CreateBlade_Advanced_Review_identityBasedStorageAccountAccess" xml:space="preserve">
    <value>Na základě identity</value>
  </data>
  <data name="CreateBlade_Advanced_Review_credentialBasedStorageAccountAccess" xml:space="preserve">
    <value>Na základě přihlašovacích údajů</value>
  </data>
  <data name="CreateBlade_Advanced_Review_enableHBIFlag" xml:space="preserve">
    <value>Povolit příznak HBI</value>
  </data>
  <data name="CreateBlade_Advanced_Review_storageAccountAccessType" xml:space="preserve">
    <value>Typ přístupu účtu úložiště</value>
  </data>
  <data name="CreateBlade_Advanced_Review_sharedKeyAccess" xml:space="preserve">
    <value>Přístup pomocí sdíleného klíče</value>
  </data>
  <data name="CreateBlade_Advanced_Review_encryptionType" xml:space="preserve">
    <value>Typ šifrování</value>
  </data>
  <data name="CreateBlade_Advanced_Review_keyURI" xml:space="preserve">
    <value>Identifikátor URI klíče</value>
  </data>
  <data name="CreateBlade_Advanced_Review_keyVault" xml:space="preserve">
    <value>Trezor klíčů</value>
  </data>
  <data name="CreateBlade_Advanced_Review_microsoftManagedKeys" xml:space="preserve">
    <value>Klíče spravované Microsoftem</value>
  </data>
  <data name="CreateBlade_Advanced_Review_Resources_title" xml:space="preserve">
    <value>Prostředky</value>
  </data>
  <data name="CreateBlade_Advanced_Review_Encryption_title" xml:space="preserve">
    <value>Šifrování</value>
  </data>
  <data name="CreateBlade_Advanced_Review_Identity_title" xml:space="preserve">
    <value>Identita</value>
  </data>
  <data name="AccountPart_deprecated" xml:space="preserve">
    <value>Zastaralé</value>
  </data>
  <data name="AccountPart_deprecatedLongMessage" xml:space="preserve">
    <value>Prostředek už není k dispozici. Použijte prosím rozšíření pracovního prostoru Machine Learning Services.</value>
  </data>
  <data name="AccountPart_deprecatedShortMessage" xml:space="preserve">
    <value>Použijte rozšíření Machine Learning Services.</value>
  </data>
  <data name="assetTypeNames_MachineLearningExperimentationAccount_singular" xml:space="preserve">
    <value>Vyřazeno – Experimentování ve službě Machine Learning</value>
  </data>
  <data name="Overview_Banner_Description_label" xml:space="preserve">
    <value>Azure Machine Learning Studio je webová aplikace, ve které můžete vytvářet, cvičit, testovat a nasazovat modely ML. Spusťte ho a začněte ho zkoumat nebo {}</value>
    <comment>{}: learnMoreText</comment>
  </data>
  <data name="Overview_Banner_Description_learnMoreText" xml:space="preserve">
    <value>další informace o studiu Azure Machine Learning</value>
  </data>
  <data name="Overview_Banner_launchButtonText" xml:space="preserve">
    <value>Spustit studio</value>
  </data>
  <data name="Overview_Banner_title" xml:space="preserve">
    <value>Pracujte s modely v Azure Machine Learning Studiu</value>
  </data>
  <data name="Overview_Banner_Registry_title" xml:space="preserve">
    <value>Pracujte s registry v Azure Machine Learning Studiu</value>
  </data>
  <data name="Overview_Banner_Registry_launchButtonText" xml:space="preserve">
    <value>Spustit registr ve studiu</value>
  </data>
  <data name="Overview_Essentials_studioWebURLText" xml:space="preserve">
    <value>Webová adresa URL studia</value>
  </data>
  <data name="Overview_Essentials_registryWebURLText" xml:space="preserve">
    <value>Adresa URL webu registru</value>
  </data>
  <data name="Overview_Essentials_mlFlowWebURLText" xml:space="preserve">
    <value>Identifikátor URI pro sledování toku MLflow</value>
  </data>
  <data name="Overview_Essentials_managedResourceGroup" xml:space="preserve">
    <value>Spravovaná skupina prostředků</value>
  </data>
  <data name="Overview_Essentials_edit" xml:space="preserve">
    <value>upravit</value>
  </data>
  <data name="Overview_Essentials_ProjectID" xml:space="preserve">
    <value>ID projektu</value>
  </data>
  <data name="AssetTypeNames_MLApp_lowerPlural" xml:space="preserve">
    <value>koncové body strojového učení online</value>
  </data>
  <data name="AssetTypeNames_MLApp_lowerSingular" xml:space="preserve">
    <value>koncový bod strojového učení online</value>
  </data>
  <data name="AssetTypeNames_MLApp_plural" xml:space="preserve">
    <value>Koncové body strojového učení online</value>
  </data>
  <data name="AssetTypeNames_MLApp_singular" xml:space="preserve">
    <value>Koncový bod strojového učení online</value>
  </data>
  <data name="endpointScoringUriLabel" xml:space="preserve">
    <value>Identifikátor URI bodování</value>
  </data>
  <data name="endpointSwaggerLabel" xml:space="preserve">
    <value>Identifikátor URI Swaggeru</value>
  </data>
  <data name="endpointProvisioningStateLabel" xml:space="preserve">
    <value>Stav zřizování</value>
  </data>
  <data name="endpointAuthModeLabel" xml:space="preserve">
    <value>Režim ověřování</value>
  </data>
  <data name="endpointId" xml:space="preserve">
    <value>ID koncového bodu</value>
  </data>
  <data name="onlineEndpointWorkspaceName" xml:space="preserve">
    <value>Pracovní prostor</value>
  </data>
  <data name="onlineEndpointName" xml:space="preserve">
    <value>Koncový bod strojového učení online</value>
  </data>
  <data name="titleDeployments" xml:space="preserve">
    <value>Nasazení</value>
  </data>
  <data name="LabelDeployments" xml:space="preserve">
    <value>Nasazení</value>
  </data>
  <data name="endpointRequestLatencyMetric" xml:space="preserve">
    <value>Latence požadavku</value>
  </data>
  <data name="endpointRequestsPerMinuteMetric" xml:space="preserve">
    <value>Požadavky za minutu</value>
  </data>
  <data name="AssetTypeNames_MLAppDeployment_lowerPlural" xml:space="preserve">
    <value>nasazení strojového učení online</value>
  </data>
  <data name="AssetTypeNames_MLAppDeployment_lowerSingular" xml:space="preserve">
    <value>nasazení strojového učení online</value>
  </data>
  <data name="AssetTypeNames_MLAppDeployment_plural" xml:space="preserve">
    <value>Nasazení strojového učení online</value>
  </data>
  <data name="AssetTypeNames_MLAppDeployment_singular" xml:space="preserve">
    <value>Nasazení strojového učení online</value>
  </data>
  <data name="AssetTypeNames_MLRegistry_lowerPlural" xml:space="preserve">
    <value>registry azure machine learning</value>
  </data>
  <data name="AssetTypeNames_MLRegistry_lowerSingular" xml:space="preserve">
    <value>registr azure machine learning</value>
  </data>
  <data name="AssetTypeNames_MLRegistry_plural" xml:space="preserve">
    <value>Registry Azure Machine Learning</value>
  </data>
  <data name="AssetTypeNames_MLRegistry_singular" xml:space="preserve">
    <value>Registr Azure Machine Learning</value>
  </data>
  <data name="deploymentModelIdLabel" xml:space="preserve">
    <value>ID modelu</value>
  </data>
  <data name="deploymentProvisioningStateLabel" xml:space="preserve">
    <value>Stav zřizování</value>
  </data>
  <data name="deploymentName" xml:space="preserve">
    <value>Název nasazení</value>
  </data>
  <data name="deploymentKind" xml:space="preserve">
    <value>Typ nasazení</value>
  </data>
  <data name="deploymentEnvironmentId" xml:space="preserve">
    <value>Prostředí</value>
  </data>
  <data name="deploymentCpuUtilizationMetric" xml:space="preserve">
    <value>Využití procesoru</value>
  </data>
  <data name="deploymentCapacityMetric" xml:space="preserve">
    <value>Kapacita nasazení</value>
  </data>
  <data name="deploymentDiskUtilizationMetric" xml:space="preserve">
    <value>Využití disku</value>
  </data>
  <data name="deploymentMemoryUtilizationMetric" xml:space="preserve">
    <value>Využití paměti</value>
  </data>
  <data name="AssociatedResource_AppInsights_Properties_changeText" xml:space="preserve">
    <value>Změnit Application Insights</value>
  </data>
  <data name="AssociatedResource_ContainerRegistry_Properties_changeText" xml:space="preserve">
    <value>Změnit registr kontejneru</value>
  </data>
  <data name="AssociatedResource_AppInsights_ChangeBlade_title" xml:space="preserve">
    <value>Vyberte Application Insights</value>
  </data>
  <data name="AssociatedResource_AppInsights_ReactView_Dropdown_placeholder" xml:space="preserve">
    <value>Vyberte Application Insights</value>
  </data>
  <data name="AssociatedResource_AppInsights_ReactView_Dropdown_noMatchMessage" xml:space="preserve">
    <value>Žádné výsledky, které by odpovídaly: {0}</value>
  </data>
  <data name="AssociatedResource_AppInsights_ReactView_Dropdown_emptyMessage" xml:space="preserve">
    <value>Nenašly se žádné prostředky Application Insights</value>
  </data>
  <data name="AssociatedResource_AppInsights_ReactView_Dropdown_errorLoadingMessage" xml:space="preserve">
    <value>Chyba při načítání Application Insights</value>
  </data>
  <data name="AssociatedResource_ContainerRegistry_ReactView_Dropdown_placeholder" xml:space="preserve">
    <value>Vybrat registr kontejneru</value>
  </data>
  <data name="AssociatedResource_ContainerRegistry_ReactView_Dropdown_noMatchMessage" xml:space="preserve">
    <value>Žádné výsledky, které by odpovídaly: {0}</value>
  </data>
  <data name="AssociatedResource_ContainerRegistry_ReactView_Dropdown_emptyMessage" xml:space="preserve">
    <value>Nenašly se žádné prostředky registru kontejneru.</value>
  </data>
  <data name="AssociatedResource_ContainerRegistry_ReactView_Dropdown_errorLoadingMessage" xml:space="preserve">
    <value>Chyba při načítání registru kontejneru</value>
  </data>
  <data name="AssociatedResource_AppInsights_ReactView_ChangeAppInsights_unauthorizedError" xml:space="preserve">
    <value>Nemáte oprávnění aktualizovat Application Insights pro tento prostředek.</value>
  </data>
  <data name="AssociatedResource_AppInsights_ReactView_ChangeAppInsights_notFoundError" xml:space="preserve">
    <value>Prostředek, který se aktualizuje, se nepovedlo najít.</value>
  </data>
  <data name="AssociatedResource_AppInsights_ReactView_ChangeAppInsights_error" xml:space="preserve">
    <value>Při aktualizaci Application Insights pro tento prostředek došlo k chybě.</value>
  </data>
  <data name="AssociatedResource_AppInsights_ReactView_ChangeAppInsights_errorTitle" xml:space="preserve">
    <value>Chyba při aktualizaci Application Insights</value>
  </data>
  <data name="AssociatedResource_AppInsights_ReactView_ChangeAppInsights_appInsightsLabel" xml:space="preserve">
    <value>Application Insights</value>
  </data>
  <data name="AssociatedResource_AppInsights_ReactView_ChangeAppInsights_appInsightsUpdatingSpinner" xml:space="preserve">
    <value>Aktualizuje se Application Insights…</value>
  </data>
  <data name="AssociatedResource_AppInsights_ReactView_ChangeAppInsights_footerSave" xml:space="preserve">
    <value>Uložit</value>
  </data>
  <data name="AssociatedResource_AppInsights_ReactView_ChangeAppInsights_footerCancel" xml:space="preserve">
    <value>Zahodit</value>
  </data>
  <data name="AssociatedResource_ContainerRegistry_ReactView_ChangeContainerRegistry_unauthorizedError" xml:space="preserve">
    <value>Nemáte oprávnění k aktualizaci registru kontejneru pro tento prostředek.</value>
  </data>
  <data name="AssociatedResource_ContainerRegistry_ReactView_ChangeContainerRegistry_notFoundError" xml:space="preserve">
    <value>Prostředek, který se aktualizuje, se nepovedlo najít.</value>
  </data>
  <data name="AssociatedResource_ContainerRegistry_ReactView_ChangeContainerRegistry_error" xml:space="preserve">
    <value>Při aktualizaci registru kontejneru pro tento prostředek došlo k chybě.</value>
  </data>
  <data name="AssociatedResource_ContainerRegistry_ReactView_ChangeContainerRegistry_errorTitle" xml:space="preserve">
    <value>Chyba při aktualizaci registru kontejneru</value>
  </data>
  <data name="AssociatedResource_ContainerRegistry_ReactView_ChangeContainerRegistry_containerRegistryLabel" xml:space="preserve">
    <value>Registr kontejneru</value>
  </data>
  <data name="AssociatedResource_ContainerRegistry_ReactView_ChangeContainerRegistry_containerRegistryUpdatingSpinner" xml:space="preserve">
    <value>Aktualizuje se registr kontejneru...</value>
  </data>
  <data name="AssociatedResource_ContainerRegistry_ReactView_ChangeContainerRegistry_footerSave" xml:space="preserve">
    <value>Uložit</value>
  </data>
  <data name="AssociatedResource_ContainerRegistry_ReactView_ChangeContainerRegistry_footerCancel" xml:space="preserve">
    <value>Zahodit</value>
  </data>
  <data name="AssociatedResource_ContainerRegistry_ChangeBlade_title" xml:space="preserve">
    <value>Vybrat registr kontejneru</value>
  </data>
  <data name="AssociatedResource_Dropdown_createNewLinkText" xml:space="preserve">
    <value>Vytvořit nový</value>
  </data>
  <data name="AssociatedResource_Dropdown_filterPlaceholder" xml:space="preserve">
    <value>Vybrat existující...</value>
  </data>
  <data name="AssociatedResource_Dropdown_newResourceText" xml:space="preserve">
    <value>(nový) {0}</value>
    <comment>0: name of a resource</comment>
  </data>
  <data name="AssociatedResource_Dropdown_SettingsBlade_discardButtonText" xml:space="preserve">
    <value>Zahodit</value>
  </data>
  <data name="AssociatedResource_Dropdown_SettingsBlade_saveButtonText" xml:space="preserve">
    <value>Uložit</value>
  </data>
  <data name="Keyvault_Dropdown_label" xml:space="preserve">
    <value>Trezor klíčů</value>
  </data>
  <data name="Keyvault_Dropdown_SettingsBlade_nameLabel" xml:space="preserve">
    <value>Název</value>
  </data>
  <data name="Keyvault_Dropdown_SettingsBlade_title" xml:space="preserve">
    <value>Vytvořit nový trezor klíčů</value>
  </data>
  <data name="StorageAccount_Dropdown_label" xml:space="preserve">
    <value>Účet úložiště</value>
  </data>
  <data name="StorageAccount_Dropdown_SettingsBlade_nameInvalidMessage" xml:space="preserve">
    <value>Název úložiště musí být dlouhý 3 až 24 znaků a může obsahovat jen malá písmena a číslice.</value>
  </data>
  <data name="StorageAccount_Dropdown_SettingsBlade_nameLabel" xml:space="preserve">
    <value>Název</value>
  </data>
  <data name="StorageAccount_Dropdown_SettingsBlade_performanceLabel" xml:space="preserve">
    <value>Výkon</value>
  </data>
  <data name="StorageAccount_Dropdown_SettingsBlade_performancePremium" xml:space="preserve">
    <value>Premium</value>
  </data>
  <data name="StorageAccount_Dropdown_SettingsBlade_performanceStandard" xml:space="preserve">
    <value>Standard</value>
  </data>
  <data name="StorageAccount_Dropdown_SettingsBlade_replicationGRS" xml:space="preserve">
    <value>Geograficky redundantní úložiště (GRS)</value>
  </data>
  <data name="StorageAccount_Dropdown_SettingsBlade_replicationGZRS" xml:space="preserve">
    <value>Geograficky a zónově redundantní úložiště (GZRS)</value>
  </data>
  <data name="StorageAccount_Dropdown_SettingsBlade_replicationLabel" xml:space="preserve">
    <value>Replikace</value>
  </data>
  <data name="StorageAccount_Dropdown_SettingsBlade_replicationLRS" xml:space="preserve">
    <value>Místně redundantní úložiště (LRS)</value>
  </data>
  <data name="StorageAccount_Dropdown_SettingsBlade_replicationRAGRS" xml:space="preserve">
    <value>Geograficky redundantní úložiště s přístupem pro čtení (RA-GRS)</value>
  </data>
  <data name="StorageAccount_Dropdown_SettingsBlade_replicationRAGZRS" xml:space="preserve">
    <value>Geograficky a zónově redundantní úložiště s přístupem pro čtení (RA-GZRS)</value>
  </data>
  <data name="StorageAccount_Dropdown_SettingsBlade_replicationZRS" xml:space="preserve">
    <value>Zónově redundantní úložiště (ZRS)</value>
  </data>
  <data name="StorageAccount_Dropdown_SettingsBlade_title" xml:space="preserve">
    <value>Vytvořit nový účet úložiště</value>
  </data>
  <data name="Keyvault_Dropdown_SettingsBlade_nameInvalidMessage" xml:space="preserve">
    <value>Název trezoru musí mít 3 až 24 abecedních znaků. Název musí začínat písmenem, končit písmenem nebo číslicí a nesmí obsahovat několik pomlček po sobě.</value>
  </data>
  <data name="CreateBlade_Network_ConnectivityMethod_private" xml:space="preserve">
    <value>Zakázat veřejný přístup a použít soukromé koncové body</value>
  </data>
  <data name="CreateBlade_Network_ConnectivityMethod_skuPrivateEndpointErrorMessage" xml:space="preserve">
    <value>Privátní s odchozími přenosy a privátní se schváleným odchozími přenosy vyžaduje pro připojení privátního koncového bodu registr kontejneru se skladovou položkou Premium. Můžete vytvořit nový registr kontejneru úrovně Premium nebo vybrat existující registr kontejneru úrovně Premium na kartě Základy, který chcete použít s tímto privátním pracovním prostorem.</value>
  </data>
  <data name="CreateBlade_Network_NetworkIsolation_skuErrorMessage" xml:space="preserve">
    <value>Aby bylo možné povolit odchozí přenosy a povolit jen schválené odchozí přenosy, vyžaduje registr kontejneru se skladovou položkou Premium, aby bylo možné připojení privátního koncového bodu.</value>
  </data>
  <data name="AppInsights_Dropdown_label" xml:space="preserve">
    <value>Application Insights</value>
  </data>
  <data name="AppInsights_Dropdown_SettingsBlade_nameInvalidMessage" xml:space="preserve">
    <value>Název může obsahovat 1 až 255 znaků. Může se skládat jen z alfanumerických znaků, teček, podtržítek, pomlček a závorek a nesmí končit tečkou.</value>
  </data>
  <data name="AppInsights_Dropdown_SettingsBlade_nameLabel" xml:space="preserve">
    <value>Název</value>
  </data>
  <data name="AppInsights_Dropdown_SettingsBlade_title" xml:space="preserve">
    <value>Vytvořit novou službu Application Insights</value>
  </data>
  <data name="AssociatedResource_Dropdown_none" xml:space="preserve">
    <value>Žádný</value>
  </data>
  <data name="ContainerRegistry_Dropdown_label" xml:space="preserve">
    <value>Registr kontejneru</value>
  </data>
  <data name="ContainerRegistry_Dropdown_SettingsBlade_nameInvalidMessage" xml:space="preserve">
    <value>Názvy prostředků můžou obsahovat jen alfanumerické znaky a musí mít od 5 do 50 znaků.</value>
  </data>
  <data name="ContainerRegistry_Dropdown_SettingsBlade_nameLabel" xml:space="preserve">
    <value>Název</value>
  </data>
  <data name="ContainerRegistry_Dropdown_SettingsBlade_skuBasic" xml:space="preserve">
    <value>Basic</value>
  </data>
  <data name="ContainerRegistry_Dropdown_SettingsBlade_skuLabel" xml:space="preserve">
    <value>Skladová položka</value>
  </data>
  <data name="ContainerRegistry_Dropdown_SettingsBlade_skuPremium" xml:space="preserve">
    <value>Premium</value>
  </data>
  <data name="ContainerRegistry_Dropdown_SettingsBlade_skuStandard" xml:space="preserve">
    <value>Standard</value>
  </data>
  <data name="ContainerRegistry_Dropdown_SettingsBlade_title" xml:space="preserve">
    <value>Vytvořit nový registr kontejneru</value>
  </data>
  <data name="AppInsights_Dropdown_Hub_info" xml:space="preserve">
    <value>Centrum AI ukládá informace o monitorování nasazených modelů pomocí Azure Application Insights. Můžete vytvořit nový prostředek Azure Application Insights nebo vybrat existující prostředek v předplatném.</value>
  </data>
  <data name="AppInsights_Dropdown_info" xml:space="preserve">
    <value>Pracovní prostor ukládá informace o monitorování nasazených modelů pomocí Azure Application Insights. Můžete vytvořit nový prostředek Azure Application Insights, nebo vybrat existující prostředek v předplatném.</value>
  </data>
  <data name="AppInsights_Dropdown_SettingsBlade_Name_infoDescription" xml:space="preserve">
    <value>Název musí splňovat tyto požadavky:</value>
  </data>
  <data name="AppInsights_Dropdown_SettingsBlade_Name_infoItem1" xml:space="preserve">
    <value>Jedinečné mezi skupinami prostředků</value>
    <comment>Item1 of AppInsights_Dropdown_SettingsBlade_Name_infoDescription</comment>
  </data>
  <data name="AppInsights_Dropdown_SettingsBlade_Name_infoItem2" xml:space="preserve">
    <value>Délka mezi 1 a 255 znaky</value>
    <comment>Item2 of AppInsights_Dropdown_SettingsBlade_Name_infoDescription</comment>
  </data>
  <data name="AppInsights_Dropdown_SettingsBlade_Name_infoItem3" xml:space="preserve">
    <value>Obsahuje jen alfanumerické znaky, tečky, podtržítka, spojovníky a závorky.</value>
    <comment>Item3 of AppInsights_Dropdown_SettingsBlade_Name_infoDescription</comment>
  </data>
  <data name="AppInsights_Dropdown_SettingsBlade_Name_infoItem4" xml:space="preserve">
    <value>Nemůže končit tečkou.</value>
    <comment>Item4 of AppInsights_Dropdown_SettingsBlade_Name_infoDescription</comment>
  </data>
  <data name="ContainerRegistry_Dropdown_info" xml:space="preserve">
    <value>Registr kontejneru se používá k registraci imagí Dockeru, které se používají při cvičení a nasazeních. Aby se minimalizovaly náklady, nový prostředek Azure Container Registry se vytváří až po vytvoření první image. Kromě toho se můžete rozhodnout vytvořit nový prostředek hned, nebo vybrat nějaký existující prostředek v předplatném.</value>
  </data>
  <data name="ContainerRegistry_Dropdown_SettingsBlade_Name_infoDescription" xml:space="preserve">
    <value>Název musí splňovat tyto požadavky:</value>
  </data>
  <data name="ContainerRegistry_Dropdown_SettingsBlade_Name_infoItem1" xml:space="preserve">
    <value>Jedinečné ve všech registrech kontejnerů v Azure</value>
    <comment>Item1 of ContainerRegistry_Dropdown_SettingsBlade_Name_infoDescription</comment>
  </data>
  <data name="ContainerRegistry_Dropdown_SettingsBlade_Name_infoItem2" xml:space="preserve">
    <value>Délka mezi 5 a 50 znaky</value>
    <comment>Item2 of ContainerRegistry_Dropdown_SettingsBlade_Name_infoDescription</comment>
  </data>
  <data name="ContainerRegistry_Dropdown_SettingsBlade_Name_infoItem3" xml:space="preserve">
    <value>Obsahuje jen alfanumerické znaky.</value>
    <comment>Item3 of ContainerRegistry_Dropdown_SettingsBlade_Name_infoDescription</comment>
  </data>
  <data name="ContainerRegistry_Dropdown_SettingsBlade_Sku_info" xml:space="preserve">
    <value>Všechny skladové položky nabízejí stejné programové funkce. Volba vyšší skladové položky zajistí vyšší výkon a škálu.</value>
  </data>
  <data name="ContainerRegistry_Dropdown_SettingsBlade_Sku_infoLearnMore" xml:space="preserve">
    <value>Další informace</value>
  </data>
  <data name="Keyvault_Dropdown_Hub_info" xml:space="preserve">
    <value>Trezor klíčů se používá k ukládání tajných kódů a dalších citlivých informací, které centrum AI potřebuje. Můžete vytvořit nový prostředek Azure Key Vault nebo vybrat nějaký existující prostředek v předplatném.</value>
  </data>
  <data name="Keyvault_Dropdown_info" xml:space="preserve">
    <value>Trezor klíčů se používá k ukládání tajných kódů a dalších citlivých informací, které pracovní prostor potřebuje. Můžete vytvořit nový prostředek Azure Key Vault, nebo vybrat nějaký existující prostředek v předplatném.</value>
  </data>
  <data name="Keyvault_Dropdown_SettingsBlade_Name_infoDescription" xml:space="preserve">
    <value>Název musí splňovat tyto požadavky:</value>
  </data>
  <data name="Keyvault_Dropdown_SettingsBlade_Name_infoItem1" xml:space="preserve">
    <value>Jedinečné ve všech existujících trezorech klíčů v Azure</value>
    <comment>Item1 of Keyvault_Dropdown_SettingsBlade_Name_infoDescription</comment>
  </data>
  <data name="Keyvault_Dropdown_SettingsBlade_Name_infoItem2" xml:space="preserve">
    <value>Délka mezi 3 a 24 znaky</value>
    <comment>Item2 of Keyvault_Dropdown_SettingsBlade_Name_infoDescription</comment>
  </data>
  <data name="Keyvault_Dropdown_SettingsBlade_Name_infoItem3" xml:space="preserve">
    <value>Obsahuje jen alfanumerické znaky a spojovníky</value>
    <comment>Item3 of Keyvault_Dropdown_SettingsBlade_Name_infoDescription</comment>
  </data>
  <data name="Keyvault_Dropdown_SettingsBlade_Name_infoItem4" xml:space="preserve">
    <value>Nemůže začínat číslicí.</value>
    <comment>Item4 of Keyvault_Dropdown_SettingsBlade_Name_infoDescription</comment>
  </data>
  <data name="Keyvault_RadioButton_label" xml:space="preserve">
    <value>Credential store</value>
  </data>
  <data name="Keyvault_RadioButton_managedKeyVault" xml:space="preserve">
    <value>Microsoft-managed (preview)</value>
  </data>
  <data name="Keyvault_RadioButton_ManagedKeyVault_infoIcon" xml:space="preserve">
    <value>Preview: secrets are stored in Microsoft-managed credential store. Secret data lifecycle follows your hub, projects, connections and compute.</value>
  </data>
  <data name="Keyvault_RadioButton_byoKeyVault" xml:space="preserve">
    <value>Azure key vault</value>
  </data>
  <data name="StorageAccount_Dropdown_Hub_info" xml:space="preserve">
    <value>Účet úložiště se používá jako výchozí úložiště dat pro centrum AI. Můžete vytvořit nový prostředek Azure Storage nebo vybrat nějaký existující prostředek v předplatném.</value>
  </data>
  <data name="StorageAccount_Dropdown_info" xml:space="preserve">
    <value>Účet úložiště se používá jako výchozí úložiště dat pro pracovní prostor. Můžete vytvořit nový prostředek Azure Storage, nebo vybrat nějaký existující prostředek v předplatném.</value>
  </data>
  <data name="StorageAccount_Dropdown_SettingsBlade_Name_infoDescription" xml:space="preserve">
    <value>Název musí splňovat tyto požadavky:</value>
  </data>
  <data name="StorageAccount_Dropdown_SettingsBlade_Name_infoItem1" xml:space="preserve">
    <value>Jedinečné mezi všemi existujícími názvy účtů úložiště v Azure</value>
    <comment>Item1 of StorageAccount_Dropdown_SettingsBlade_Name_infoDescription</comment>
  </data>
  <data name="StorageAccount_Dropdown_SettingsBlade_Name_infoItem2" xml:space="preserve">
    <value>Délka mezi 3 a 24 znaky</value>
    <comment>Item2 of StorageAccount_Dropdown_SettingsBlade_Name_infoDescription</comment>
  </data>
  <data name="StorageAccount_Dropdown_SettingsBlade_Name_infoItem3" xml:space="preserve">
    <value>Obsahuje jen malá písmena a číslice.</value>
    <comment>Item3 of StorageAccount_Dropdown_SettingsBlade_Name_infoDescription</comment>
  </data>
  <data name="StorageAccount_Dropdown_SettingsBlade_Performance_info" xml:space="preserve">
    <value>Účty úložiště Standard se zálohují na magnetické jednotky a cena za GB je u nich nejnižší. Účty Premium Storage se zálohují na jednotky SSD (solid-state drive) a nabízejí konzistentní výkon s nízkou latencí.</value>
  </data>
  <data name="StorageAccount_Dropdown_SettingsBlade_Replication_info" xml:space="preserve">
    <value>Data ve vašem účtu úložiště se vždy replikují, aby se zajistila odolnost a vysoká dostupnost. Zvolte strategii replikace, která nejlépe odpovídá vašim požadavkům.</value>
  </data>
  <data name="StorageAccount_Dropdown_SettingsBlade_Replication_infoLearnMore" xml:space="preserve">
    <value>Další informace</value>
  </data>
  <data name="AIServices_SummaryTab_label" xml:space="preserve">
    <value>Služby umělé inteligence</value>
  </data>
  <data name="HubConfigDefaultResourceGroup_SummaryTab_label" xml:space="preserve">
    <value>Výchozí skupina prostředků projektu</value>
  </data>
  <data name="AppInsights_SummaryTab_label" xml:space="preserve">
    <value>Application Insights</value>
  </data>
  <data name="ContainerRegistry_SummaryTab_label" xml:space="preserve">
    <value>Registr kontejneru</value>
  </data>
  <data name="Keyvault_SummaryTab_label" xml:space="preserve">
    <value>Trezor klíčů</value>
  </data>
  <data name="StorageAccount_SummaryTab_label" xml:space="preserve">
    <value>Účet úložiště</value>
  </data>
  <data name="SummaryTab_Resource_existingFormat" xml:space="preserve">
    <value>{0} ({1})</value>
    <comment>0: resource name, 1: resource group name</comment>
  </data>
  <data name="SummaryTab_Resource_newFormat" xml:space="preserve">
    <value>(nové) {0}</value>
    <comment>0: resource name</comment>
  </data>
  <data name="SummaryTab_Resource_none" xml:space="preserve">
    <value>Žádný</value>
  </data>
  <data name="SummaryTab_Resource_microsoftManaged" xml:space="preserve">
    <value>Microsoft-managed</value>
  </data>
  <data name="OnlineEndpoints_Create_introText" xml:space="preserve">
    <value>Aplikace pro odvozování Azure Machine Learning umožňují rychle vytvářet, nasazovat a škálovat modely strojového učení na úrovni podniků spuštěné na libovolné platformě. Můžete použít jakékoli opensourcové rozhraní strojového učení, třeba TensorFlow, PyTorch, SciKit-Learn, ONNX a další. Díky našemu nasazování bez kódu můžete urychlit nebo přizpůsobit aplikaci pro odvozování vlastním kontejnerem Docker nebo kódem pro hodnocení modelu.</value>
  </data>
  <data name="OnlineEndpoints_Create_modelLabel" xml:space="preserve">
    <value>Model</value>
  </data>
  <data name="OnlineEndpoints_Create_modelPlaceholder" xml:space="preserve">
    <value>Vyberte model.</value>
  </data>
  <data name="OnlineEndpoints_Create_modelVersionLabel" xml:space="preserve">
    <value>Verze modelu</value>
  </data>
  <data name="OnlineEndpoints_Create_modelVersionPlaceholder" xml:space="preserve">
    <value>Výběr verze modelu</value>
  </data>
  <data name="OnlineEndpoints_Create_nameLabel" xml:space="preserve">
    <value>Název</value>
  </data>
  <data name="OnlineEndpoints_Create_subTitle" xml:space="preserve">
    <value>Vytvořit aplikaci ML</value>
  </data>
  <data name="OnlineEndpoints_Create_title" xml:space="preserve">
    <value>Koncový bod strojového učení online</value>
  </data>
  <data name="OnlineEndpoints_Create_workspaceLabel" xml:space="preserve">
    <value>Pracovní prostor</value>
  </data>
  <data name="OnlineEndpoints_Create_workspacePlaceholder" xml:space="preserve">
    <value>Vybrat pracovní prostor</value>
  </data>
  <data name="MLAppDeployments_GridColumns_name" xml:space="preserve">
    <value>Název nasazení</value>
  </data>
  <data name="MLAppDeployments_GridColumns_status" xml:space="preserve">
    <value>Stav zřizování</value>
  </data>
  <data name="MLAppDeployments_GridColumns_traffic" xml:space="preserve">
    <value>Procento provozu</value>
  </data>
  <data name="MLAppDeployments_Grid_StatusFilter_all" xml:space="preserve">
    <value>Všechny stavy zřizování</value>
  </data>
  <data name="MLAppDeployments_Grid_StatusFilter_itemFormat" xml:space="preserve">
    <value>{0} ({1})</value>
    <comment>0: status name, 1: count</comment>
  </data>
  <data name="MLAppDeployments_Grid_StatusFilter_some" xml:space="preserve">
    <value>Počet vybraných stavů připojení: {0}</value>
    <comment>0: a number</comment>
  </data>
  <data name="StorageAccount_Dropdown_SettingsBlade_hnsNotSupportedFormat" xml:space="preserve">
    <value>{0} – Hierarchický obor názvů se nepodporuje.</value>
    <comment>{0}: Storage account name</comment>
  </data>
  <data name="StorageAccount_Dropdown_SettingsBlade_premiumNotSupportedFormat" xml:space="preserve">
    <value>{0} – Účet úložiště Premium se nepodporuje.</value>
    <comment>{0}: Storage account name</comment>
  </data>
  <data name="OnlineEndpoints_Create_Dependencies_condaDependenciesFileLabel" xml:space="preserve">
    <value>Soubor závislostí Conda</value>
  </data>
  <data name="OnlineEndpoints_Create_Dependencies_description" xml:space="preserve">
    <value>Přizpůsobte svůj model nasazení na aplikaci pro vyvozování.</value>
  </data>
  <data name="OnlineEndpoints_Create_Dependencies_entryScriptFileLabel" xml:space="preserve">
    <value>Soubor zaváděcího skriptu</value>
  </data>
  <data name="OnlineEndpoints_Create_Dependencies_selectCondaDependenciesFile" xml:space="preserve">
    <value>Vyberte soubor závislostí Conda.</value>
  </data>
  <data name="OnlineEndpoints_Create_Dependencies_selectEntryScriptFileMessage" xml:space="preserve">
    <value>Vyberte soubor vstupního skriptu.</value>
  </data>
  <data name="OnlineEndpoints_Create_Dependencies_tabName" xml:space="preserve">
    <value>Závislosti</value>
  </data>
  <data name="CrossRegionComputeQuotas_loadingText" xml:space="preserve">
    <value>Načítání...</value>
  </data>
  <data name="CrossRegionComputeQuotas_UsageGrid_usageColumnText" xml:space="preserve">
    <value>Využití</value>
  </data>
  <data name="CrossRegionComputeQuotas_UsageGrid_vmFamiliesColumnText" xml:space="preserve">
    <value>Řady virtuálních počítačů</value>
  </data>
  <data name="CrossRegionComputeQuotas_UsageGrid_workspaceDisplayText" xml:space="preserve">
    <value>{0} ({1})</value>
    <comment>0: workspace name, 1: resource group name</comment>
  </data>
  <data name="CrossRegionComputeQuotas_UsageGrid_workspacesColumnText" xml:space="preserve">
    <value>Pracovní prostory</value>
  </data>
  <data name="CrossRegionComputeQuotas_configureQuotaButtonText" xml:space="preserve">
    <value>Nakonfigurovat kvótu</value>
  </data>
  <data name="CrossRegionComputeQuotas_dedicatedCoresSectionLabel" xml:space="preserve">
    <value>Využití vyhrazených jader</value>
  </data>
  <data name="CrossRegionComputeQuotas_lowPriorityCoresSectionLabel" xml:space="preserve">
    <value>Využití jader s nízkou prioritou</value>
  </data>
  <data name="CrossRegionComputeQuotas_QuotaConfigurationBlade_subTitle" xml:space="preserve">
    <value>Tady nakonfigurujte kvótu v rámci svého předplatného</value>
  </data>
  <data name="CrossRegionComputeQuotas_QuotaConfigurationBlade_title" xml:space="preserve">
    <value>Nakonfigurovat kvótu</value>
  </data>
  <data name="CrossRegionComputeQuotas_requestQuotaButtonText" xml:space="preserve">
    <value>Požádat o navýšení kvóty</value>
  </data>
  <data name="CrossRegionComputeQuotas_subscriptionViewText" xml:space="preserve">
    <value>Zobrazení předplatného</value>
  </data>
  <data name="CrossRegionComputeQuotas_toggleText" xml:space="preserve">
    <value>Zobrazit pracovní prostory napříč všemi umístěními (výpočetní prostředky napříč lokalitami)</value>
  </data>
  <data name="CrossRegionComputeQuotas_totalClustersLabel" xml:space="preserve">
    <value>Kvóta clusteru:</value>
  </data>
  <data name="CrossRegionComputeQuotas_totalClustersTextFormat" xml:space="preserve">
    <value>Počet využitých clusterů a CI: {0} | Zbývá: {1}</value>
    <comment>0: number of used, 1: number of remaining</comment>
  </data>
  <data name="CrossRegionComputeQuotas_totalCoresTextFormat" xml:space="preserve">
    <value>Počet využitých jader: {0} | Počet zbývajících jader: {1}</value>
    <comment>0: number of used, 1: number of remaining</comment>
  </data>
  <data name="CrossRegionComputeQuotas_totalDedicatedCoresLabel" xml:space="preserve">
    <value>Vyhrazená kvóta:</value>
  </data>
  <data name="CrossRegionComputeQuotas_totalLowPriorityCoresLabel" xml:space="preserve">
    <value>Kvóta pro nízkou prioritu:</value>
  </data>
  <data name="CrossRegionComputeQuotas_UsageGrid_coresUsageTextFormat" xml:space="preserve">
    <value>{0} z {1} využitých jader</value>
    <comment>0: number of used, 1: number of limit</comment>
  </data>
  <data name="CrossRegionComputeQuotas_UsageGrid_totalCoresTextFormat" xml:space="preserve">
    <value>{0} z {1} ({2})</value>
    <comment>0: number of used, 1: number of limit, 2: usage in percent</comment>
  </data>
  <data name="CrossRegionComputeQuotas_workspaceViewText" xml:space="preserve">
    <value>Zobrazení pracovního prostoru</value>
  </data>
  <data name="CrossRegionComputeQuotas_UsageGrid_crossLocationWarningCoresFormat" xml:space="preserve">
    <value>počet jader: {0}</value>
    <comment>0: number of cores</comment>
  </data>
  <data name="CrossRegionComputeQuotas_UsageGrid_crossLocationWarningLine1" xml:space="preserve">
    <value>V jiných umístěních používáte: {0}.</value>
    <comment>0: {0} cores (see CrossRegionComputeQuotas_UsageGrid_crossLocationWarningCoresFormat</comment>
  </data>
  <data name="CrossRegionComputeQuotas_UsageGrid_familyNameFormat" xml:space="preserve">
    <value>Jednotky vCPU standardní řady {0}</value>
    <comment>0: family name</comment>
  </data>
  <data name="CrossRegionComputeQuotas_UsageGrid_workspaceCrossLocationDisplayText" xml:space="preserve">
    <value>{0} ({1}, {2})</value>
    <comment>0: workspace name, 1: resource group name, 2: location</comment>
  </data>
  <data name="CrossRegionComputeQuotas_QuotaConfigurationBlade_filterText" xml:space="preserve">
    <value>Zadejte nejméně 3 znaky pro vyhledání pracovních prostorů...</value>
  </data>
  <data name="CrossRegionComputeQuotas_QuotaConfigurationBlade_noItemsText" xml:space="preserve">
    <value>Nenašly se žádné pracovní prostory, které by bylo možné zobrazit.</value>
  </data>
  <data name="ManagedIdentities_menuText" xml:space="preserve">
    <value>Identita</value>
  </data>
  <data name="CrossRegionComputeQuotas_QuotaConfigurationBlade_quotaConfigNotAvailable" xml:space="preserve">
    <value>Kvóty nelze nakonfigurovat kvůli následující chybě: {0}.</value>
    <comment>0: error details</comment>
  </data>
  <data name="CrossRegionComputeQuotas_QuotaConfigurationBlade_workspacesNotAvailable" xml:space="preserve">
    <value>Kvóta se dá nakonfigurovat jen pro pracovní prostory, které už jsou nakonfigurované, protože kvůli následující chybě nelze získat seznam pracovních prostorů tohoto předplatného: {0}</value>
    <comment>0: error details</comment>
  </data>
  <data name="CrossRegionComputeQuotas_QuotaUsageBlade_crossLocationUsagesNotAvailable" xml:space="preserve">
    <value>Informace o pracovním prostoru napříč lokalitami (jestli je pracovní prostor vlastnící výpočetní prostředek v jiném než vybraném umístění) nelze zobrazit kvůli následující chybě, ale i tak si můžete zobrazit všechna využití: {0}.</value>
    <comment>0: error details</comment>
  </data>
  <data name="CrossRegionComputeQuotas_QuotaUsageBlade_usagesNotAvailable" xml:space="preserve">
    <value>Využití výpočetních prostředků nelze zobrazit kvůli následující chybě: {0}.</value>
    <comment>0: error details</comment>
  </data>
  <data name="CrossRegionComputeQuotas_UsageGrid_crossLocationEnableText" xml:space="preserve">
    <value>Povolením přepínače pro výpočetní prostředky napříč lokalitami</value>
  </data>
  <data name="CrossRegionComputeQuotas_UsageGrid_crossLocationWarningLine2" xml:space="preserve">
    <value>{1} je zobrazíte.</value>
    <comment>1: enable text (CrossRegionComputeQuotas_UsageGrid_crossLocationEnableText)</comment>
  </data>
  <data name="AdvancedTab_Section_ManagedIdentity_description" xml:space="preserve">
    <value>Spravovaná identita umožňuje ověřování prostředků Azure v cloudových službách bez uložení přihlašovacích údajů v kódu. Po povolení tohoto nastavení bude možné všechna potřebná oprávnění udělovat prostřednictvím řízení přístupu na základě role v Azure. Pracovní prostor může mít identitu přiřazenou systémem nebo uživatelem.</value>
  </data>
  <data name="AdvancedTab_Section_ManagedIdentity_header" xml:space="preserve">
    <value>Spravovaná identita</value>
  </data>
  <data name="AdvancedTab_Section_ManagedIdentity_radioGroupTitle" xml:space="preserve">
    <value>Typ identity</value>
  </data>
  <data name="AdvancedTab_Section_ManagedIdentity_systemAssignedText" xml:space="preserve">
    <value>Identita přiřazená systémem</value>
  </data>
  <data name="AdvancedTab_Section_StorageAccountAccess_sharedKeyAccessCheckboxLabel" xml:space="preserve">
    <value>Zakázat přístup pomocí sdílených klíčů</value>
  </data>
  <data name="AdvancedTab_Section_StorageAccountAccess_sharedKeyAccessDisableDocumentationLinkText" xml:space="preserve">
    <value>Další informace</value>
  </data>
  <data name="AdvancedTab_Section_StorageAccountAccess_previewLinkText" xml:space="preserve">
    <value>(Preview)</value>
  </data>
  <data name="AdvancedTab_Section_StorageAccountAccess_sharedKeyAccessDisableWarning" xml:space="preserve">
    <value>Možnost Zakázat přístup ke sdílenému klíči {0} zakáže přístup ke klíči v účtu úložiště. Všechny požadavky na účet úložiště, které jsou autorizované pomocí sdíleného klíče, včetně sdílených přístupových podpisů (SAS), se zamítnou. Tato možnost může vyžadovat další konfiguraci role pro jednotlivé uživatele pro určité případy použití. {1} o zakázání přístupu ke sdíleným klíčům pro účet úložiště pracovního prostoru</value>
  </data>
  <data name="AdvancedTab_Section_ManagedIdentity_userAssignedPickerLabel" xml:space="preserve">
    <value>Identita přiřazená uživatelem</value>
  </data>
  <data name="AdvancedTab_Section_ManagedIdentity_userAssignedPickerRequiredMessage" xml:space="preserve">
    <value>Identita uživatele se vyžaduje.</value>
  </data>
  <data name="AdvancedTab_Section_ManagedIdentity_userAssignedPickerSubscriptionMessage" xml:space="preserve">
    <value>Pracovní prostor a vybraná identita uživatele musejí být ve stejném předplatném.</value>
  </data>
  <data name="AdvancedTab_Section_ManagedIdentity_userAssignedPickerText" xml:space="preserve">
    <value>Kliknutím vyberte identitu.</value>
  </data>
  <data name="AdvancedTab_Section_ManagedIdentity_userAssignedText" xml:space="preserve">
    <value>Identita přiřazená uživatelem</value>
  </data>
  <data name="AdvancedTab_Section_ManagedIdentity_userIdentityNameLabel" xml:space="preserve">
    <value>Název identity uživatele</value>
  </data>
  <data name="AdvancedTab_Section_ManagedIdentity_userIdentityResourceGroupLabel" xml:space="preserve">
    <value>Skupina prostředků s identitou uživatele</value>
  </data>
  <data name="SummaryTab_Advanced_ManagedIdentity_systemAssigned" xml:space="preserve">
    <value>Přiřazeno systémem</value>
  </data>
  <data name="SummaryTab_Advanced_ManagedIdentity_typeText" xml:space="preserve">
    <value>Typ identity</value>
  </data>
  <data name="SummaryTab_Advanced_ManagedIdentity_userAssigned" xml:space="preserve">
    <value>Přiřazeno uživatelem</value>
  </data>
  <data name="SummaryTab_Advanced_ManagedIdentity_userIdentityName" xml:space="preserve">
    <value>Název identity uživatele</value>
  </data>
  <data name="SummaryTab_Advanced_ManagedIdentity_userIdentityResourceGroup" xml:space="preserve">
    <value>Skupina prostředků s identitou uživatele</value>
  </data>
  <data name="RequestQuota_dedicatedSectionLabel" xml:space="preserve">
    <value>Vyhrazeno</value>
  </data>
  <data name="RequestQuota_lowPrioritySectionLabel" xml:space="preserve">
    <value>Nízká priorita</value>
  </data>
  <data name="RequestQuota_vmTypeDedicated" xml:space="preserve">
    <value>Vyhrazeno</value>
  </data>
  <data name="RequestQuota_vmTypeLowPriority" xml:space="preserve">
    <value>Nízká priorita</value>
  </data>
  <data name="RequestQuota_vmTypesLabel" xml:space="preserve">
    <value>Typy virtuálních počítačů</value>
  </data>
  <data name="CrossRegionComputeQuotas_lowPriorityUsageInfoText" xml:space="preserve">
    <value>Mějte prosím na paměti, že počet jader s nízkou prioritou v daném předplatném představuje jedinou hodnotu napříč řadami virtuálních počítačů.</value>
  </data>
  <data name="CrossRegionComputeQuotas_QuotaUsageBlade_quotaInfoLearnMore" xml:space="preserve">
    <value>Další informace</value>
  </data>
  <data name="CrossRegionComputeQuotas_QuotaUsageBlade_quotaInfoText" xml:space="preserve">
    <value>Kvóta Azure Machine Learning je předem nakonfigurovaná limity, které určují maximální počet jader, která můžete v daném okamžiku použít.</value>
  </data>
  <data name="CrossRegionComputeQuotas_requestBladeFamilyNameFormat" xml:space="preserve">
    <value>Řada {0}</value>
    <comment>0: family name</comment>
  </data>
  <data name="AppInsights_Dropdown_createNewAriaLabel" xml:space="preserve">
    <value>Vytvořit novou službu Application Insights</value>
  </data>
  <data name="ContainerRegistry_Dropdown_createNewAriaLabel" xml:space="preserve">
    <value>Vytvořit nový registr kontejneru</value>
  </data>
  <data name="CrossRegionComputeQuotas_UsageGrid_coreUtilizationColumnText" xml:space="preserve">
    <value>Využití jader</value>
  </data>
  <data name="CrossRegionComputeQuotas_UsageGrid_usagePercentageColumnText" xml:space="preserve">
    <value>Procento využití</value>
  </data>
  <data name="Keyvault_Dropdown_createNewAriaLabel" xml:space="preserve">
    <value>Vytvořit nový trezor klíčů</value>
  </data>
  <data name="StorageAccount_Dropdown_createNewAriaLabel" xml:space="preserve">
    <value>Vytvořit nový účet úložiště</value>
  </data>
  <data name="AdvancedTab_Section_ManagedIdentity_disabledWarning" xml:space="preserve">
    <value>Možnost spravované identity přiřazené uživatelem se podporuje jenom v případě, že se používá existující účet úložiště, Key Vault a registr kontejnerů.</value>
  </data>
  <data name="AdvancedTab_Section_ManagedIdentity_permissionWarning" xml:space="preserve">
    <value>Pokud přinesete vlastní přidružené prostředky, místo toho, abyste je vytvořili ve službě Azure Machine Learning Service, musíte na těchto prostředcích udělit spravované role identit. Pomocí {0} proveďte přiřazení.</value>
    <comment>{0}: role assignment ARM template</comment>
  </data>
  <data name="AdvancedTab_Section_ManagedIdentity_permissionWarningLinkText" xml:space="preserve">
    <value>šablona ARM pro přiřazení role</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_title" xml:space="preserve">
    <value>Pravidla odchozích přenosů pracovního prostoru</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_ruleNameLabel" xml:space="preserve">
    <value>Název pravidla</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_ruleNameBalloonContent" xml:space="preserve">
    <value>Název odchozího pravidla, které je v pracovním prostoru jedinečné.</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_ruleNameAlreadyExists" xml:space="preserve">
    <value>Název pravidla už existuje.</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_appGateway_subResource_errorMessage" xml:space="preserve">
    <value>V prostředku se nenašla žádná konfigurace private link. Nastavte ho před vytvořením pravidla odchozích přenosů PE.</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_destinationTypeLabel" xml:space="preserve">
    <value>Typ cíle</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_destinationTypeBalloonContent" xml:space="preserve">
    <value>Typ odchozího cíle, plně kvalifikovaného názvu domény, privátního koncového bodu a značky služby.</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_subscriptionLabel" xml:space="preserve">
    <value>Předplatné</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_subscriptionBalloonContent" xml:space="preserve">
    <value>Předplatné obsahující cílový prostředek pro privátní koncový bod.</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_resourceGroupLabel" xml:space="preserve">
    <value>Skupina prostředků</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_resourceGroupBalloonContent" xml:space="preserve">
    <value>Skupina prostředků obsahující cílový prostředek pro privátní koncový bod.</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_resourceTypeLabel" xml:space="preserve">
    <value>Typ prostředku</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_resourceTypeBalloonContent" xml:space="preserve">
    <value>Typ prostředku Azure, který podporuje Private Link.</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_resourceNameLabel" xml:space="preserve">
    <value>Název prostředku</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_resourceNameBalloonContent" xml:space="preserve">
    <value>Název cílového prostředku pro privátní koncový bod.</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_fqdnsLabel" xml:space="preserve">
    <value>Plně kvalifikované názvy domény</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_statusLabel" xml:space="preserve">
    <value>Stav</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_statusBalloonContent" xml:space="preserve">
    <value>Stav je jen pro čtení, pokud je aktivní, znamená to, že spravovaná síť je zřízená a připravená. Pokud je neaktivní, znamená to, že není zřízená.</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_subResourceLabel" xml:space="preserve">
    <value>Dílčí prostředek</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_subResourceBalloonContent" xml:space="preserve">
    <value>Dílčí prostředek, který se má připojit pro privátní koncový bod.</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_sparkEnabledLabel" xml:space="preserve">
    <value>Spark povolen</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_sparkEnabledBalloonContent" xml:space="preserve">
    <value>Zaškrtněte, pokud chcete povolit použití dalšího privátního koncového bodu úlohami spuštěnými ve Sparku.</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_sparkStatusLabel" xml:space="preserve">
    <value>Stav Sparku</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_sparkStatusBalloonContent" xml:space="preserve">
    <value>Určuje, jestli je pro úlohy Sparku zřízená spravovaná síť.</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_serviceTagLabel" xml:space="preserve">
    <value>Značka služby</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_serviceTagBalloonContent" xml:space="preserve">
    <value>Předdefinované identifikátory, které představují kategorii IP adres.</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_protocolLabel" xml:space="preserve">
    <value>Protokol</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_protocolBalloonContent" xml:space="preserve">
    <value>Síťový protokol, který se má povolit, TCP, UDP, ICMP nebo Jakýkoli</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_portRangeLabel" xml:space="preserve">
    <value>Rozsahy portů</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_portRangeBalloonContent" xml:space="preserve">
    <value>Zadejte jeden port, třeba 80, rozsah portů, třeba 1024–655535, nebo seznam čárkami oddělených samostatných portů a/nebo rozsahů portů, třeba 80,1024-655535. Tím se určí porty, pro které toto pravidlo povolí přenosy. Pokud chcete povolit přenosy přes libovolný port, zadejte hvězdičku (*).</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_Note" xml:space="preserve">
    <value>Poznámka</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_FqdnCostInfo_managedNetworkIsolationLinkText" xml:space="preserve">
    <value>Spravovaná izolace sítě</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_FqdnCostInfo_text" xml:space="preserve">
    <value>Pravidla odchozích přenosů plně kvalifikovaného názvu domény se implementují pomocí Azure Firewall. Pokud budete používat pravidla odchozích přenosů plně kvalifikovaných názvů domén, budou do fakturace zahrnuty poplatky za Azure Firewall. Další informace o pravidlech odchozích přenosů najdete tady: </value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_FqdnCostInfo_pricingLinkText" xml:space="preserve">
    <value>Ceny</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_applicationGatewayFqdnInfoBallon" xml:space="preserve">
    <value>FQDNs resolve to the private IP of the Application Gateway private endpoint inside the workspace's managed network. FQDNs are editable as needed. Please save after editing the fields.</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_FqdnValidation_invalidFormatMessage" xml:space="preserve">
    <value>Specify a valid FQDN with at least three labels for Application Gateway access.</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_FqdnValidation_emptyLabelMessage" xml:space="preserve">
    <value>Domain label should not be empty.</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_FqdnValidation_applicationGatewayFqdnRequired" xml:space="preserve">
    <value>FQDN is required.</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_FqdnCostInfo_moreInformationText" xml:space="preserve">
    <value>Další informace o Azure Firewall najdete tady: </value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_fqdnDestinationLabel" xml:space="preserve">
    <value>Cíl plně kvalifikovaného názvu domény</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_fqdnDestinationBalloonContent" xml:space="preserve">
    <value>Plně kvalifikovaný název domény, který povoluje odchozí provoz.</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_amlRegistryPEDependencyRulesWarning" xml:space="preserve">
    <value>Pro všechny prostředky závislostí v registru AzureML se vytvoří pravidla odchozích závislostí. Tato pravidla si můžete zobrazit v části Pravidla odchozích přenosů závislostí.</value>
  </data>
  <data name="StorageAccount_Dropdown_SettingsBlade_hnsCheckLabel" xml:space="preserve">
    <value>Povolit hierarchický obor názvů (Preview)</value>
  </data>
  <data name="StorageAccount_Dropdown_SettingsBlade_hnsDescriptionText" xml:space="preserve">
    <value>Hierarchický obor názvů Data Lake Storage Gen2 urychluje úlohy analýzy velkých objemů dat, umožňuje rychlejší a spolehlivější operace se soubory a povoluje seznamy řízení přístupu (ACL) na úrovni souborů. {0}</value>
    <comment>0: learn more</comment>
  </data>
  <data name="StorageAccount_Dropdown_SettingsBlade_hnsDescriptionTitle" xml:space="preserve">
    <value>Data Lake Storage Gen2 (Preview)</value>
  </data>
  <data name="StorageAccount_Dropdown_SettingsBlade_learnMoreText" xml:space="preserve">
    <value>Další informace</value>
  </data>
  <data name="AdvancedTab_Section_StorageAccountAccess_credentialBasedText" xml:space="preserve">
    <value>Přístup na základě přihlašovacích údajů</value>
  </data>
  <data name="AdvancedTab_Section_StorageAccountAccess_description" xml:space="preserve">
    <value>Azure machine learning allows you to choose between credential-based or identity-based access when connecting to the default storage account.</value>
  </data>
  <data name="AdvancedTab_Section_StorageAccountAccess_header" xml:space="preserve">
    <value>Přístup k účtu úložiště</value>
  </data>
  <data name="AdvancedTab_Section_StorageAccountAccess_identityBasedText" xml:space="preserve">
    <value>Přístup na základě identity</value>
  </data>
  <data name="AdvancedTab_Section_StorageAccountAccess_identityBasedWarningText" xml:space="preserve">
    <value>{0} When using identity-based authentication, "{1}" and "{2}" roles must be granted to {3} that need access on the storage account. Contact your admin for help or</value>
    <comment>0: Important, 1: Storage Blob Data Contributor, 2: Storage File Privileged Contributor, 3: individual users</comment>
  </data>
  <data name="AdvancedTab_Section_StorageAccountAccess_identityBasedWarningLearnMore" xml:space="preserve">
    <value>learn more about RBAC settings</value>
  </data>
  <data name="AdvancedTab_Section_StorageAccountAccess_Important" xml:space="preserve">
    <value>Important</value>
  </data>
  <data name="AdvancedTab_Section_StorageAccountAccess_IndividualUsers" xml:space="preserve">
    <value>individual users</value>
  </data>
  <data name="AdvancedTab_Section_StorageAccountAccess_learnMoreText" xml:space="preserve">
    <value>Další informace</value>
  </data>
  <data name="AdvancedTab_Section_StorageAccountAccess_radioGroupTitle" xml:space="preserve">
    <value>Typ přístupu účtu úložiště</value>
  </data>
  <data name="Security_menuText" xml:space="preserve">
    <value>Zabezpečení</value>
  </data>
  <data name="Keyword_alert" xml:space="preserve">
    <value>Upozornění</value>
  </data>
  <data name="Keyword_audit" xml:space="preserve">
    <value>Audit</value>
  </data>
  <data name="Keyword_instance" xml:space="preserve">
    <value>Instance</value>
  </data>
  <data name="Keyword_log" xml:space="preserve">
    <value>Protokol</value>
  </data>
  <data name="Keyword_rules" xml:space="preserve">
    <value>Pravidla</value>
  </data>
  <data name="Keyword_scale" xml:space="preserve">
    <value>Škálovat</value>
  </data>
  <data name="Keyword_scaling" xml:space="preserve">
    <value>Škálování</value>
  </data>
  <data name="Scaling" xml:space="preserve">
    <value>Škálování</value>
  </data>
  <data name="SummaryTab_createResourceGroupErrorText" xml:space="preserve">
    <value>Při vytváření výchozího pracovního prostoru služby Log Analytics došlo k chybě: {0}</value>
    <comment>0: error details</comment>
  </data>
  <data name="SummaryTab_gettingLogWorkspacesErrorText" xml:space="preserve">
    <value>Při získávání pracovních prostorů protokolů a vytváření výchozího pracovního prostoru služby Log Analytics došlo k chybě: {0}</value>
    <comment>0: error details</comment>
  </data>
  <data name="CrossRegionComputeQuotas_quotaUpdateFailed" xml:space="preserve">
    <value>Aktualizace kvóty se nezdařila.</value>
  </data>
  <data name="CrossRegionComputeQuotas_quotaUpdateSucceeded" xml:space="preserve">
    <value>Kvóta se úspěšně aktualizovala.</value>
  </data>
  <data name="LabelMlFlowUri" xml:space="preserve">
    <value>Identifikátor URI pro sledování toku MLflow</value>
  </data>
  <data name="Networking_PrivateEndpointConnections_TabText" xml:space="preserve">
    <value>Připojení privátních koncových bodů</value>
  </data>
  <data name="Networking_WorkspaceManagedOutboundAccess_tabText" xml:space="preserve">
    <value>Odchozí přístup spravovaný pracovním prostorem</value>
  </data>
  <data name="Networking_PublicAccess_AllNetworksInfoText" xml:space="preserve">
    <value>K tomuto prostředku mají přístup všechny sítě včetně internetu.</value>
  </data>
  <data name="Networking_PublicAccess_Description" xml:space="preserve">
    <value>Přístup k veřejné síti umožňuje přístup k tomuto prostředku přes internet pomocí veřejné IP adresy. Aplikace nebo prostředek, pro které se udělil přístup s následujícími pravidly sítě, stále vyžadují pro přístup k tomuto prostředku správnou autorizaci. {0}</value>
    <comment>0: learn more link</comment>
  </data>
  <data name="Networking_PublicAccess_DisabledInfoText" xml:space="preserve">
    <value>K tomuto prostředku nemá přístup žádná veřejná síť.</value>
  </data>
  <data name="Networking_PublicAccess_EnabledFromSelectedIpInfoText" xml:space="preserve">
    <value>Povolte přístup z veřejné IP adresy, kterou jste zadali níže.</value>
  </data>
  <data name="Networking_PublicAccess_LearnMoreText" xml:space="preserve">
    <value>Další informace</value>
  </data>
  <data name="Networking_PublicAccess_RadioAllNetworksText" xml:space="preserve">
    <value>Všechny sítě</value>
  </data>
  <data name="Networking_PublicAccess_RadioEnabledFromAllNetworks" xml:space="preserve">
    <value>Povoleno ze všech sítí</value>
  </data>
  <data name="Networking_PublicAccess_RadioEnabledFromSelectedIp" xml:space="preserve">
    <value>Povoleno z vybraných IP adres</value>
  </data>
  <data name="Networking_PublicAccess_RadioDisabledText" xml:space="preserve">
    <value>Zakázáno</value>
  </data>
  <data name="Networking_PublicAccess_RadioLabel" xml:space="preserve">
    <value>Přístup k veřejné síti</value>
  </data>
  <data name="Networking_PublicAccess_addressRange" xml:space="preserve">
    <value>Rozsah adres</value>
  </data>
  <data name="Networking_PublicAccess_addressRangePlaceHolder" xml:space="preserve">
    <value>IP adresa nebo CIDR</value>
  </data>
  <data name="Networking_PublicAccess_firewallHeader" xml:space="preserve">
    <value>Brána firewall</value>
  </data>
  <data name="Networking_PublicAccess_firewallDescription" xml:space="preserve">
    <value>Přidáním rozsahů IP adres umožníte přístup z internetu nebo místních sítí.</value>
  </data>
  <data name="Networking_PublicAccess_firewallLearnMore" xml:space="preserve">
    <value>Další informace</value>
  </data>
  <data name="Networking_PublicAccess_addClientIpAddressLabel" xml:space="preserve">
    <value>Přidat IP adresu klienta ({0})</value>
    <comment>0 = the client's ip address</comment>
  </data>
  <data name="Networking_PublicAccess_addClientIpAddressInfo" xml:space="preserve">
    <value>Pokud není síť, ze které používáte portál Azure Portal, obvyklá (třeba domácí nebo pracovní prostředí), možná budete chtít nepřidávat IP adresu svého klienta.</value>
  </data>
  <data name="Networking_PublicAccess_FirewallValidation_invalidIPAddress" xml:space="preserve">
    <value>Neplatná IP adresa.</value>
  </data>
  <data name="Networking_PublicAccess_FirewallValidation_duplicateIpAddresses" xml:space="preserve">
    <value>Byly zadány dva identické rozsahy adres. Rozsahy adres musí být jedinečné.</value>
  </data>
  <data name="Networking_PublicAccess_FirewallValidation_publicIpRuleValidation" xml:space="preserve">
    <value>Pravidla IP podporují jen veřejné IP adresy.</value>
  </data>
  <data name="Networking_PublicAccess_FirewallValidation_invalidCidr" xml:space="preserve">
    <value>Zadejte IP adresu nebo rozsah CIDR.</value>
  </data>
  <data name="Networking_PublicAccess_FirewallValidation_invalidCIDRBlockWithSuggestion" xml:space="preserve">
    <value>{0} není platný blok CIDR. Použijte místo něj {1}/{2}.</value>
    <comment>0: cidrValue, 1: validIpForCidrPrefix, 2: prefix</comment>
  </data>
  <data name="Networking_PublicAccess_FirewallValidation_maxPrefix" xml:space="preserve">
    <value>Předpona musí být menší než nebo rovna {0}.</value>
    <comment>0: maxPrefix</comment>
  </data>
  <data name="Networking_PublicAccess_FirewallValidation_minPrefix" xml:space="preserve">
    <value>Předpona musí být větší než nebo rovna {0}.</value>
    <comment>0: minPrefix</comment>
  </data>
  <data name="Networking_PublicAccess_FirewallValidation_nonNullSubnet" xml:space="preserve">
    <value>Rozsah adres musí být jiný než null.</value>
  </data>
  <data name="Networking_PublicAccess_FirewallValidation_octet" xml:space="preserve">
    <value>Oktet {0} s hodnotou {1} je neplatný. Musí být mezi {2} a {3}.</value>
  </data>
  <data name="Networking_PublicAccess_FirewallValidation_malformedSubnet" xml:space="preserve">
    <value>Chybný rozsah adres. Adresa byla {0}.</value>
  </data>
  <data name="Networking_PublicAccess_FirewallValidation_formattedPrefix" xml:space="preserve">
    <value>Předpona musí být v rozsahu {0} až {1}.</value>
    <comment>0 - min prefix, 1 - max prefix</comment>
  </data>
  <data name="Networking_PublicAccess_FirewallValidation_exactPrefix" xml:space="preserve">
    <value>Předpona podsítě se musí rovnat {0}.</value>
  </data>
  <data name="Networking_PublicAccess_FirewallValidation_leadingZerosIpAddress" xml:space="preserve">
    <value>Oktet {0} v IP adrese {1} obsahuje na začátku nulu.</value>
    <comment>0 = the octet with a leading zero, 1 = the ip address containing the octet</comment>
  </data>
  <data name="Networking_PublicAccess_TabText" xml:space="preserve">
    <value>Veřejný přístup</value>
  </data>
  <data name="Networking_PublicAccess_Toolbar_DiscardChangesText" xml:space="preserve">
    <value>Zahodit změny</value>
  </data>
  <data name="Networking_PublicAccess_Toolbar_RefreshText" xml:space="preserve">
    <value>Aktualizovat</value>
  </data>
  <data name="Networking_PublicAccess_Toolbar_SaveText" xml:space="preserve">
    <value>Uložit</value>
  </data>
  <data name="AdvancedTab_Section_DataEncryption_checkboxByoInfo" xml:space="preserve">
    <value>Volitelně můžete k ukládání šifrovaných dat pracovního prostoru použít předem vytvořené prostředky. Použití vašich vlastních zdrojů pro šifrování umožňuje vylepšenou konfiguraci těchto zdrojů v souladu s IT a bezpečnostními požadavky vaší organizace, ale vyžaduje další akce vaší správy.</value>
  </data>
  <data name="AdvancedTab_Section_DataEncryption_checkboxByoLabel" xml:space="preserve">
    <value>Použít existující prostředky pro ukládání šifrovaných dat (Preview)</value>
  </data>
  <data name="AdvancedTab_Section_DataEncryption_checkboxLabel" xml:space="preserve">
    <value>Povolit šifrování pomocí klíče spravovaného zákazníkem</value>
  </data>
  <data name="AdvancedTab_Section_DataEncryption_cosmosInfo" xml:space="preserve">
    <value>Vyberte předem vytvořený prostředek Azure Cosmos DB. Když vyberete možnost Žádný, Azure ML vytvoří prostředek služby Cosmos DB v nové skupině prostředků v předplatném, kterou bude spravovat Microsoft. Když k šifrování použijete své vlastní prostředky, bude to pro vás znamenat další odpovědnost na správu.</value>
  </data>
  <data name="AdvancedTab_Section_DataEncryption_cosmosLabel" xml:space="preserve">
    <value>Cosmos DB</value>
  </data>
  <data name="AdvancedTab_Section_DataEncryption_searchInfo" xml:space="preserve">
    <value>Vyberte předem vytvořený prostředek Azure Search. Když vyberete možnost Žádný, Azure ML vytvoří prostředek služby Search v nové skupině prostředků v předplatném, kterou bude spravovat Microsoft. Když k šifrování použijete své vlastní prostředky, bude to pro vás znamenat další odpovědnost za správu.</value>
  </data>
  <data name="AdvancedTab_Section_DataEncryption_searchLabel" xml:space="preserve">
    <value>Vyhledat</value>
  </data>
  <data name="AdvancedTab_Section_DataEncryption_storageInfo" xml:space="preserve">
    <value>Vyberte předem vytvořený prostředek Azure Storage. Když vyberete možnost Žádný, Azure ML vytvoří prostředek služby Storage v nové skupině prostředků v předplatném, kterou bude spravovat Microsoft. Když k šifrování použijete své vlastní prostředky, bude to pro vás znamenat další odpovědnost za správu.</value>
  </data>
  <data name="AdvancedTab_Section_DataEncryption_storageLabel" xml:space="preserve">
    <value>Úložiště</value>
  </data>
  <data name="AdvancedTab_Section_DataEncryption_infoText" xml:space="preserve">
    <value>Služba Azure Machine Learning ukládá metriky a metadata do instance Azure Cosmos DB, kde se všechna data šifrují ​při nečinnosti. Standardně se data šifrují pomocí klíčů spravovaných Microsoftem. Můžete ale použít i své vlastní (zákazníkem spravované) klíče. {0}</value>
  </data>
  <data name="AdvancedTab_Section_DataEncryption_ServiceSide_infoText" xml:space="preserve">
    <value>Služba Azure Machine Learning ukládá metriky a metadata do instance Azure Cosmos DB, kde se všechna data šifrují ​při nečinnosti. Standardně se data šifrují pomocí klíčů spravovaných Microsoftem. Můžete ale použít i své vlastní (zákazníkem spravované) klíče. {0}</value>
  </data>
  <data name="AdvancedTab_Section_DataEncryption_learnMoreText" xml:space="preserve">
    <value>Další informace</value>
  </data>
  <data name="EncryptionSettings_Title" xml:space="preserve">
    <value>Šifrování</value>
  </data>
  <data name="EncryptionSettings_Header1_Template" xml:space="preserve">
    <value>Vaše data se ve výchozím nastavení šifrují pomocí klíčů spravovaných Microsoftem. Pokud chcete mít nad svými daty větší kontrolu, můžete použít vlastní klíč pro šifrování. Další informace najdete v tématech {} a {}.</value>
  </data>
  <data name="EncryptionSettings_Header1_Replacement1" xml:space="preserve">
    <value>šifrování dat</value>
  </data>
  <data name="EncryptionSettings_Header1_Replacement2" xml:space="preserve">
    <value>Šifrování klíčů spravovaných zákazníkem</value>
  </data>
  <data name="EncryptionSettings_Header2" xml:space="preserve">
    <value>Po nasazení pracovního prostoru můžete šifrovací klíč otočit, ale nemůžete změnit typ šifrování z klíče spravovaného Microsoftem na klíč spravovaný zákazníkem.</value>
  </data>
  <data name="EncryptionSettings_Command_Save" xml:space="preserve">
    <value>Uložit</value>
  </data>
  <data name="EncryptionSettings_Command_Discard" xml:space="preserve">
    <value>Zahodit</value>
  </data>
  <data name="EncryptionSettings_Command_ResultSuccess" xml:space="preserve">
    <value>Povedlo se</value>
  </data>
  <data name="EncryptionSettings_Command_ResultError" xml:space="preserve">
    <value>Chyba při aktualizaci klíče</value>
  </data>
  <data name="EncryptionSettings_Command_ResultLoading" xml:space="preserve">
    <value>Aktualizuji...</value>
  </data>
  <data name="EncryptionSettings_Command_DismissAriaLabel" xml:space="preserve">
    <value>Zavřít</value>
  </data>
  <data name="EncryptionSettings_Command_SeeMore" xml:space="preserve">
    <value>Zobrazit více</value>
  </data>
  <data name="EncryptionSettings_DirtyFormWarning" xml:space="preserve">
    <value>Máte neuložené změny.</value>
  </data>
  <data name="EncryptionSettings_SelectionSection_Title" xml:space="preserve">
    <value>Výběr šifrování</value>
  </data>
  <data name="EncryptionSettings_SelectionSection_TypeLabel" xml:space="preserve">
    <value>Typ šifrování</value>
  </data>
  <data name="EncryptionSettings_SelectionSection_VaultAndKeyLabel" xml:space="preserve">
    <value>Trezor klíčů a klíč</value>
  </data>
  <data name="EncryptionSettings_SelectionSection_VaultAndKeySelect" xml:space="preserve">
    <value>Vybrat trezor klíčů a klíč</value>
  </data>
  <data name="EncryptionSettings_SelectedKey_Title" xml:space="preserve">
    <value>Vybraný trezor klíčů a klíč</value>
  </data>
  <data name="EncryptionSettings_SelectedKey_VaultLabel" xml:space="preserve">
    <value>KeyVault</value>
  </data>
  <data name="EncryptionSettings_SelectedKey_KeyLabel" xml:space="preserve">
    <value>Klíč</value>
  </data>
  <data name="EncryptionSettings_SelectedKey_VersionLabel" xml:space="preserve">
    <value>Verze</value>
  </data>
  <data name="CreateBlade_Advanced_Review_byoCosmosText" xml:space="preserve">
    <value>Cosmos DB pro klíče spravované zákazníky</value>
  </data>
  <data name="CreateBlade_Advanced_Review_byoSearchText" xml:space="preserve">
    <value>Search pro klíče spravované zákazníkem</value>
  </data>
  <data name="CreateBlade_Advanced_Review_byoStorageText" xml:space="preserve">
    <value>Účet úložiště pro klíče spravované zákazníkem</value>
  </data>
  <data name="CreateBlade_Advanced_Review_byoValueFormat" xml:space="preserve">
    <value>{0} ({1})</value>
    <comment>0: resource name, 1: resource group name</comment>
  </data>
  <data name="SearchService_Dropdown_standardSupportedFormat" xml:space="preserve">
    <value>{0} (SKU: {1}) – Jako minimum se vyžaduje SKU Standard.</value>
    <comment>0: search service name, 1: sku name</comment>
  </data>
  <data name="Networking_PublicAccess_Toolbar_SavingText" xml:space="preserve">
    <value>Probíhá ukládání…</value>
  </data>
  <data name="basicsBladeDetailsIntroLearnMore" xml:space="preserve">
    <value>Další informace o skupinách prostředků Azure</value>
  </data>
  <data name="basicsBladeInstanceIntroLearnMore" xml:space="preserve">
    <value>Další informace</value>
  </data>
  <data name="SoftDeletedWorkspace_Grid_AriaLabel" xml:space="preserve">
    <value>Mřížka odstraněných prostředků</value>
  </data>
  <data name="SoftDeletedWorkspace_Grid_Columns_Name" xml:space="preserve">
    <value>Název</value>
  </data>
  <data name="SoftDeletedWorkspace_Grid_Columns_DeletedDate" xml:space="preserve">
    <value>Datum odstranění</value>
  </data>
  <data name="SoftDeletedWorkspace_Grid_Columns_PurgeDate" xml:space="preserve">
    <value>Naplánované datum trvalého odstranění</value>
  </data>
  <data name="SoftDeletedWorkspace_Grid_Columns_ResourceGroup" xml:space="preserve">
    <value>Skupina prostředků</value>
  </data>
  <data name="SoftDeletedWorkspace_Grid_NoWorkspacesFound" xml:space="preserve">
    <value>Nenašly se žádné prostředky, které by se daly zobrazit</value>
  </data>
  <data name="SoftDeletedWorkspace_Blade_Title" xml:space="preserve">
    <value>Nedávno odstraněné prostředky</value>
  </data>
  <data name="SoftDeletedWorkspace_Blade_SubTitle" xml:space="preserve">
    <value>Obnovení nebo trvalé odstranění prostředků</value>
  </data>
  <data name="SoftDeletedWorkspace_Blade_ErrorMessage" xml:space="preserve">
    <value>Chyba při načítání obnovitelně odstraněných prostředků</value>
  </data>
  <data name="SoftDeletedWorkspace_Blade_ToolBar_Header" xml:space="preserve">
    <value>Pracovní prostory Machine Learning</value>
  </data>
  <data name="SoftDeletedWorkspace_Blade_AIStudio_ToolBar_Header" xml:space="preserve">
    <value>AI Foundry resources</value>
  </data>
  <data name="SoftDeletedWorkspace_Blade_ToolBar_Buttons_Refresh" xml:space="preserve">
    <value>Aktualizovat</value>
  </data>
  <data name="SoftDeletedWorkspace_Blade_Footer_Buttons_Recover" xml:space="preserve">
    <value>Obnovit</value>
  </data>
  <data name="SoftDeletedWorkspace_Blade_Footer_Buttons_Purge" xml:space="preserve">
    <value>Trvale odstranit</value>
  </data>
  <data name="SoftDeletedWorkspace_Blade_Footer_Buttons_Cancel" xml:space="preserve">
    <value>Zrušit</value>
  </data>
  <data name="SoftDeletedWorkspace_Blade_LoadingText" xml:space="preserve">
    <value>Načítá se...</value>
  </data>
  <data name="SoftDeletedWorkspace_OverviewBlade_DeleteMessage" xml:space="preserve">
    <value>U tohoto prostředku byla povolena funkce obnovitelného odstranění. Po obnovitelném odstranění zůstanou tato data prostředků dostupná. Po uplynutí doby uchovávání se vyprázdní. Prostředek můžete vyprázdnit dříve nebo ho obnovit</value>
  </data>
  <data name="SoftDeletedWorkspace_WorkspacesBlade_Commands_RecoverNotifications_Failure_message" xml:space="preserve">
    <value>Obnovení {0} z(e) {1} prostředků se nezdařilo.</value>
  </data>
  <data name="SoftDeletedWorkspace_WorkspacesBlade_Commands_RecoverNotifications_Failure_title" xml:space="preserve">
    <value>Nepovedlo se obnovit prostředky</value>
  </data>
  <data name="SoftDeletedWorkspace_WorkspacesBlade_Commands_RecoverNotifications_InProgress_message" xml:space="preserve">
    <value>Obnovování {0} prostředků</value>
  </data>
  <data name="SoftDeletedWorkspace_WorkspacesBlade_Commands_RecoverNotifications_InProgress_title" xml:space="preserve">
    <value>Obnovují se prostředky...</value>
  </data>
  <data name="SoftDeletedWorkspace_WorkspacesBlade_Commands_RecoverNotifications_Success_message" xml:space="preserve">
    <value>Počet úspěšně obnovených prostředků: {0}.</value>
  </data>
  <data name="SoftDeletedWorkspace_WorkspacesBlade_Commands_RecoverNotifications_Success_title" xml:space="preserve">
    <value>Prostředky se úspěšně obnovily</value>
  </data>
  <data name="SoftDeletedWorkspace_WorkspacesBlade_Commands_PurgeNotifications_Failure_message" xml:space="preserve">
    <value>Nepovedlo se trvale odstranit {0} z(e) {1} prostředků</value>
  </data>
  <data name="SoftDeletedWorkspace_WorkspacesBlade_Commands_PurgeNotifications_Failure_title" xml:space="preserve">
    <value>Nepovedlo se odstranit prostředky</value>
  </data>
  <data name="SoftDeletedWorkspace_WorkspacesBlade_Commands_PurgeNotifications_InProgress_message" xml:space="preserve">
    <value>Trvalé odstraňování {0} prostředků</value>
  </data>
  <data name="SoftDeletedWorkspace_WorkspacesBlade_Commands_PurgeNotifications_InProgress_title" xml:space="preserve">
    <value>Odstraňují se prostředky...</value>
  </data>
  <data name="SoftDeletedWorkspace_WorkspacesBlade_Commands_PurgeNotifications_Success_message" xml:space="preserve">
    <value>Úspěšně se trvale odstranil tento počet prostředků: {0}.</value>
  </data>
  <data name="SoftDeletedWorkspace_WorkspacesBlade_Commands_PurgeNotifications_Success_title" xml:space="preserve">
    <value>Prostředky se úspěšně odstranily</value>
  </data>
  <data name="SoftDeletedWorkspace_WorkspacesBlade_Commands_Generic_Error_Message" xml:space="preserve">
    <value>Při zpracování vašeho požadavku se stala chyba. Zkuste to za chvíli znova.</value>
  </data>
  <data name="SoftDeletedWorkspace_WorkspacesBlade_Commands_ValidateResources_KeyVault_Failure_message" xml:space="preserve">
    <value>Chyba obnovení: Přidružený prostředek Azure Key Vault „{0}“ se nedalo najít. Obnovitelně odstraněný prostředek nelze obnovit bez dříve připojených Key Vault. Požadované Azure Key Vault můžou být stále obnovitelné, přečtěte si téma „Správa odstraněných trezorů“.</value>
  </data>
  <data name="SoftDeletedWorkspace_WorkspacesBlade_Commands_ValidateResources_StorageAccount_Failure_message" xml:space="preserve">
    <value>Chyba obnovení: Přidružený prostředek účtu úložiště „{0}“ se nedalo najít. Pokud byl účet úložiště dříve obnovitelně odstraněn, před obnovením tohoto prostředku ho obnovte.</value>
  </data>
  <data name="SoftDeletedWorkspace_WorkspacesBlade_Commands_ValidateResources_AppInsights_Failure_message" xml:space="preserve">
    <value>Chyba obnovení: Přidružený prostředek Application Insights „{0}“ nebyl nalezen. Je možné, že byl odstraněn. Obnovte nebo znovu vytvořte prostředek Application Insights pod jeho dřívějším názvem „{0}“. Data Application Insights můžete obnovit tak, že nejdříve obnovíte přidružený pracovní prostor log analytics."</value>
  </data>
  <data name="SoftDeletedWorkspace_WorkspacesBlade_Commands_ValidateResources_ContainerRegistry_Failure_message" xml:space="preserve">
    <value>Chyba obnovení: Přidružený prostředek Container Registry „{0}“ nebyl nalezen. Obnovitelně odstraněný objekt nelze obnovit bez Container Registry jako závislosti.</value>
  </data>
  <data name="SoftDeletedWorkspace_WorkspacesBlade_Commands_ValidateResources_Failure_title" xml:space="preserve">
    <value>Nepovedlo se ověřit prostředek {0}</value>
  </data>
  <data name="SoftDeletedWorkspace_WorkspacesBlade_Commands_ValidateResources_InProgress_message" xml:space="preserve">
    <value>Ověřování {0} prostředků pro {1}</value>
  </data>
  <data name="SoftDeletedWorkspace_WorkspacesBlade_Commands_ValidateResources_InProgress_title" xml:space="preserve">
    <value>Ověřování prostředků...</value>
  </data>
  <data name="SoftDeletedWorkspace_WorkspacesBlade_Commands_ValidateResources_Success_message" xml:space="preserve">
    <value>Prostředky pro {0} se úspěšně ověřily.</value>
  </data>
  <data name="SoftDeletedWorkspace_WorkspacesBlade_Commands_ValidateResources_Success_title" xml:space="preserve">
    <value>Úspěšně ověřeno</value>
  </data>
  <data name="SoftDeletedWorkspace_DeleteBlade_Title" xml:space="preserve">
    <value>Odstranit prostředek</value>
  </data>
  <data name="SoftDeletedWorkspace_DeleteBlade_Message_text" xml:space="preserve">
    <value>Když tento prostředek odstraníte, dočasně se umístí do stavu „obnovitelného odstranění“, který vám umožní ho obnovit. Odstranění vašich dat se odloží, dokud váš prostředek sami trvale neodstraníte, nebo do vypršení platnosti období uchovávání dat obnovitelného odstranění po dobu čtrnácti (14) dnů. Chování obnovitelného odstranění můžete přepsat a váš prostředek tak okamžitě trvale odstranit. {0}</value>
  </data>
  <data name="SoftDeletedWorkspace_DeleteBlade_Message_AIStudio_text" xml:space="preserve">
    <value>Trvale odstranit tento prostředek „{0}“?</value>
  </data>
  <data name="SoftDeletedWorkspace_DeleteBlade_Message_CMKtext" xml:space="preserve">
    <value>Tento prostředek používá k šifrování dat klíč spravovaný zákazníkem (CMK). Při obnovitelném odstranění se závislé prostředky pro ukládání dat neodstraní a budou se za ně účtovat poplatky, dokud se tento prostředek trvale neodstraní. {0}</value>
  </data>
  <data name="SoftDeletedWorkspace_DeleteBlade_Message_link" xml:space="preserve">
    <value>Další informace</value>
  </data>
  <data name="SoftDeletedWorkspace_DeleteBlade_FormControls_PermanentDelete_header" xml:space="preserve">
    <value>Trvale odstranit</value>
  </data>
  <data name="SoftDeletedWorkspace_DeleteBlade_FormControls_PermanentDelete_checkbox" xml:space="preserve">
    <value>Trvale odstranit tento prostředek</value>
  </data>
  <data name="SoftDeletedWorkspace_DeleteBlade_FormControls_Confirm_label" xml:space="preserve">
    <value>Potvrdit odstranění</value>
  </data>
  <data name="SoftDeletedWorkspace_DeleteBlade_FormControls_Confirm_placeholder" xml:space="preserve">
    <value>Zadejte název prostředku</value>
  </data>
  <data name="SoftDeletedWorkspace_DeleteBlade_FormControls_Confirm_errorMessage" xml:space="preserve">
    <value>Název neodpovídá.</value>
  </data>
  <data name="SoftDeletedWorkspace_DeleteBlade_FormControls_Buttons_delete" xml:space="preserve">
    <value>Odstranit</value>
  </data>
  <data name="SoftDeletedWorkspace_DeleteBlade_FormControls_Buttons_cancel" xml:space="preserve">
    <value>Zrušit</value>
  </data>
  <data name="DeleteHubBlade_Title" xml:space="preserve">
    <value>Delete resource</value>
  </data>
  <data name="DeleteHubBlade_Description_hubName" xml:space="preserve">
    <value>Hub to be deleted: </value>
  </data>
  <data name="DeleteHubBlade_Description_project" xml:space="preserve">
    <value>project(s)</value>
  </data>
  <data name="DeleteHubBlade_Description_deployment" xml:space="preserve">
    <value>deployed model(s)</value>
  </data>
  <data name="DeleteHubBlade_Description_section1" xml:space="preserve">
    <value>Your hub with {0} and {1} will be permanently deleted.</value>
  </data>
  <data name="DeleteHubBlade_Description_section2" xml:space="preserve">
    <value>Connected resources may still store hub data and are not automatically deleted. If you delete these resources you may break other existing Azure deployments.</value>
  </data>
  <data name="DeleteHubBlade_Description_section3" xml:space="preserve">
    <value>Choose connected resources you'd like to additionally delete:</value>
  </data>
  <data name="DeleteHubBlade_AssociatedResourcesListColumn_name" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="DeleteHubBlade_AssociatedResourcesListColumn_type" xml:space="preserve">
    <value>Type</value>
  </data>
  <data name="DeleteHubBlade_AssociatedResourcesListColumn_resourceGroup" xml:space="preserve">
    <value>Resource Group</value>
  </data>
  <data name="DeleteHubBlade_ProjectListColumn_project" xml:space="preserve">
    <value>Project</value>
  </data>
  <data name="DeleteHubBlade_ProjectListColumn_deployment" xml:space="preserve">
    <value>Deployment</value>
  </data>
  <data name="DeleteHubBlade_RBAC_message" xml:space="preserve">
    <value>You are missing Azure RBAC delete permission on one or more connected resources. Delete these resources later from Azure portal.</value>
  </data>
  <data name="DeleteHubBlade_ResourceType_keyVault" xml:space="preserve">
    <value>Key Vault</value>
  </data>
  <data name="DeleteHubBlade_ResourceType_storageAccount" xml:space="preserve">
    <value>Storage Account</value>
  </data>
  <data name="DeleteHubBlade_ResourceType_applicationInsights" xml:space="preserve">
    <value>Application Insights</value>
  </data>
  <data name="DeleteHubBlade_ResourceType_containerRegistry" xml:space="preserve">
    <value>Container Registry</value>
  </data>
  <data name="DeleteHubBlade_ResourceType_openAI" xml:space="preserve">
    <value>Open AI</value>
  </data>
  <data name="DeleteHubBlade_ResourceType_aiServices" xml:space="preserve">
    <value>AI Services</value>
  </data>
  <data name="DeleteHubBlade_ResourceType_searchService" xml:space="preserve">
    <value>Search Service</value>
  </data>
  <data name="DeleteHubBlade_ResourceType_contentSafety" xml:space="preserve">
    <value>Content Safety</value>
  </data>
  <data name="DeleteHubBlade_ResourceType_speechServices" xml:space="preserve">
    <value>Speech Services</value>
  </data>
  <data name="DeleteHubBlade_ResourceType_computerVision" xml:space="preserve">
    <value>Computer Vision</value>
  </data>
  <data name="DeleteHubBlade_ResourceType_textAnalytics" xml:space="preserve">
    <value>Language</value>
  </data>
  <data name="DeleteHubBlade_ResourceType_textTranslation" xml:space="preserve">
    <value>Translator</value>
  </data>
  <data name="DeleteHubBlade_ResourceType_formRecognizer" xml:space="preserve">
    <value>Document Intelligence</value>
  </data>
  <data name="DeleteHubBlade_ResourceType_cognitiveService" xml:space="preserve">
    <value>Cognitive Service</value>
  </data>
  <data name="DeleteHubBlade_ConfirmDeleteSection_label" xml:space="preserve">
    <value>Confirm delete</value>
  </data>
  <data name="DeleteHubBlade_ConfirmDeleteSection_placeholder" xml:space="preserve">
    <value>Type the resource name</value>
  </data>
  <data name="DeleteHubBlade_ConfirmDeleteSection_ValidationErrorMessage_emptyString" xml:space="preserve">
    <value>The value must not be empty</value>
  </data>
  <data name="DeleteHubBlade_ConfirmDeleteSection_ValidationErrorMessage_nameMismatch" xml:space="preserve">
    <value>Name does not match</value>
  </data>
  <data name="DeleteHubBlade_Buttons_delete" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="DeleteHubBlade_Buttons_cancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="DeleteHubNotification_InProgress_description" xml:space="preserve">
    <value>Hub '{workspaceName}' and its associated resources deletion in progress...</value>
  </data>
  <data name="DeleteHubNotification_InProgress_title" xml:space="preserve">
    <value>Deleting resource...</value>
  </data>
  <data name="DeleteHubNotification_Success_description" xml:space="preserve">
    <value>Hub '{workspaceName}' and its associated resources were deleted successfully.</value>
  </data>
  <data name="DeleteHubNotification_Success_title" xml:space="preserve">
    <value>Successfully deleted</value>
  </data>
  <data name="DeleteHubNotification_Error_description" xml:space="preserve">
    <value>An error occurred while deleting hub '{workspaceName}' and its associated resources.</value>
  </data>
  <data name="DeleteHubNotification_Error_title" xml:space="preserve">
    <value>Resource deletion error</value>
  </data>
  <data name="DeleteHubNotification_Cancel_title" xml:space="preserve">
    <value>Resource deletion canceled</value>
  </data>
  <data name="DeleteHubStatusBlade_ResourceStatusListColumn_resource" xml:space="preserve">
    <value>Resource</value>
  </data>
  <data name="DeleteHubStatusBlade_ResourceStatusListColumn_status" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="DeleteHubStatusBlade_title" xml:space="preserve">
    <value>Delete resource status</value>
  </data>
  <data name="Browse_addMachineLearningLabel" xml:space="preserve">
    <value>Nový pracovní prostor</value>
  </data>
  <data name="Description_CreateAzureMachineLearningResource" xml:space="preserve">
    <value>Pro projekty a týmy ML</value>
  </data>
  <data name="Description_createAzureMachineLearningHubResource" xml:space="preserve">
    <value>Centrální konfigurace center Azure AI</value>
  </data>
  <data name="Browse_addMachineLearningRegistryLabel" xml:space="preserve">
    <value>Nový registr</value>
  </data>
  <data name="Description_CreateAzureMachineLearningRegistryResource" xml:space="preserve">
    <value>Pro sdílení prostředků ML mezi pracovními prostory</value>
  </data>
  <data name="Create_CreateButton_label" xml:space="preserve">
    <value>Vytvořit</value>
  </data>
  <data name="Create_Wizard_title" xml:space="preserve">
    <value>Vytvořit Azure Machine Learning</value>
    <comment>PLEASE DO NOT TRANSLATE "Azure Machine Learning"</comment>
  </data>
  <data name="AssetTypeNames_MachineLearningServices_Default_plural" xml:space="preserve">
    <value>Pracovní prostory Azure Machine Learning</value>
    <comment>PLEASE DO NOT TRANSLATE "Azure Machine Learning"</comment>
  </data>
  <data name="AssetTypeNames_MachineLearningServices_Default_singular" xml:space="preserve">
    <value>Pracovní prostor Azure Machine Learning</value>
    <comment>PLEASE DO NOT TRANSLATE "Azure Machine Learning"</comment>
  </data>
  <data name="AssetTypeNames_MachineLearningServices_Default_lowerSingular" xml:space="preserve">
    <value>pracovní prostor</value>
  </data>
  <data name="AssetTypeNames_MachineLearningServices_FeatureStore_plural" xml:space="preserve">
    <value>Azure Machine Learning úložiště funkcí</value>
    <comment>PLEASE DO NOT TRANSLATE "Azure Machine Learning"</comment>
  </data>
  <data name="AssetTypeNames_MachineLearningServices_FeatureStore_singular" xml:space="preserve">
    <value>Azure Machine Learning úložiště funkcí</value>
    <comment>PLEASE DO NOT TRANSLATE "Azure Machine Learning"</comment>
  </data>
  <data name="AssetTypeNames_MachineLearningServices_FeatureStore_lowerPlural" xml:space="preserve">
    <value>úložiště funkcí</value>
  </data>
  <data name="AssetTypeNames_MachineLearningServices_FeatureStore_lowerSingular" xml:space="preserve">
    <value>úložiště funkcí</value>
  </data>
  <data name="AssetTypeNames_MachineLearningServices_Hub_plural" xml:space="preserve">
    <value>Centra Azure AI</value>
  </data>
  <data name="AssetTypeNames_MachineLearningServices_Hub_singular" xml:space="preserve">
    <value>Centrum Azure AI</value>
  </data>
  <data name="AssetTypeNames_MachineLearningServices_Hub_lowerPlural" xml:space="preserve">
    <value>Centra Azure AI</value>
  </data>
  <data name="AssetTypeNames_MachineLearningServices_Hub_lowerSingular" xml:space="preserve">
    <value>Centrum Azure AI</value>
  </data>
  <data name="AssetTypeNames_MachineLearningServices_Project_plural" xml:space="preserve">
    <value>Projekty Azure AI</value>
  </data>
  <data name="AssetTypeNames_MachineLearningServices_Project_singular" xml:space="preserve">
    <value>Azure AI projekt</value>
  </data>
  <data name="AssetTypeNames_MachineLearningServices_Project_lowerPlural" xml:space="preserve">
    <value>Projekty Azure AI</value>
  </data>
  <data name="AssetTypeNames_MachineLearningServices_Project_lowerSingular" xml:space="preserve">
    <value>Azure AI projekt</value>
  </data>
  <data name="AssetType_Llama2_Names_plural" xml:space="preserve">
    <value>Llama2</value>
  </data>
  <data name="AssetType_Llama2_Names_singular" xml:space="preserve">
    <value>Llama2</value>
  </data>
  <data name="AssetType_Llama2_Names_lowerPlural" xml:space="preserve">
    <value>Llama2</value>
  </data>
  <data name="AssetType_Llama2_Names_lowerSingular" xml:space="preserve">
    <value>Llama2</value>
  </data>
  <data name="AssetType_Llama2_Page_title" xml:space="preserve">
    <value>Continue to Azure AI Foundry to use this offer</value>
  </data>
  <data name="AssetType_Llama2_Page_Description_intro" xml:space="preserve">
    <value>Ceníme si vašeho zájmu.</value>
  </data>
  <data name="AssetType_Llama2_Page_Description_body" xml:space="preserve">
    <value>Select Continue, and you'll be redirected to the model catalog in Azure AI Foundry where you can finish subscribing and start using this model.</value>
  </data>
  <data name="AssetType_Llama2_Page_primaryButton" xml:space="preserve">
    <value>Continue to Azure AI Foundry model catalog</value>
  </data>
  <data name="AssetType_cohere_Names_plural" xml:space="preserve">
    <value>Cohere</value>
  </data>
  <data name="AssetType_cohere_Names_singular" xml:space="preserve">
    <value>Cohere</value>
  </data>
  <data name="AssetType_cohere_Names_lowerPlural" xml:space="preserve">
    <value>Cohere</value>
  </data>
  <data name="AssetType_cohere_Names_lowerSingular" xml:space="preserve">
    <value>Cohere</value>
  </data>
  <data name="AssetType_cohere_Page_title" xml:space="preserve">
    <value>Continue to Azure AI Foundry to use this offer</value>
  </data>
  <data name="AssetType_cohere_Page_Description_intro" xml:space="preserve">
    <value>Ceníme si vašeho zájmu.</value>
  </data>
  <data name="AssetType_cohere_Page_Description_body" xml:space="preserve">
    <value>Select Continue, and you'll be redirected to the model catalog in Azure AI Foundry where you can finish subscribing and start using this model.</value>
  </data>
  <data name="AssetType_cohere_Page_primaryButton" xml:space="preserve">
    <value>Continue to Azure AI Foundry model catalog</value>
  </data>
  <data name="AssetType_Mistral_Names_plural" xml:space="preserve">
    <value>Mistral</value>
  </data>
  <data name="AssetType_Mistral_Names_singular" xml:space="preserve">
    <value>Mistral</value>
  </data>
  <data name="AssetType_Mistral_Names_lowerPlural" xml:space="preserve">
    <value>Mistral</value>
  </data>
  <data name="AssetType_Mistral_Names_lowerSingular" xml:space="preserve">
    <value>Mistral</value>
  </data>
  <data name="AssetType_Mistral_Page_title" xml:space="preserve">
    <value>Continue to Azure AI Foundry to use this offer</value>
  </data>
  <data name="AssetType_Mistral_Page_Description_intro" xml:space="preserve">
    <value>Ceníme si vašeho zájmu.</value>
  </data>
  <data name="AssetType_Mistral_Page_Description_body" xml:space="preserve">
    <value>Select Continue, and you'll be redirected to the model catalog in Azure AI Foundry where you can finish subscribing and start using this model.</value>
  </data>
  <data name="AssetType_Mistral_Page_primaryButton" xml:space="preserve">
    <value>Continue to Azure AI Foundry model catalog</value>
  </data>
  <data name="AssetType_Nixtla_Names_plural" xml:space="preserve">
    <value>Nixtla</value>
  </data>
  <data name="AssetType_Nixtla_Names_singular" xml:space="preserve">
    <value>Nixtla</value>
  </data>
  <data name="AssetType_Nixtla_Names_lowerPlural" xml:space="preserve">
    <value>Nixtla</value>
  </data>
  <data name="AssetType_Nixtla_Names_lowerSingular" xml:space="preserve">
    <value>Nixtla</value>
  </data>
  <data name="AssetType_Nixtla_Page_title" xml:space="preserve">
    <value>Continue to Azure AI Foundry to use this offer</value>
  </data>
  <data name="AssetType_Nixtla_Page_Description_intro" xml:space="preserve">
    <value>Ceníme si vašeho zájmu.</value>
  </data>
  <data name="AssetType_Nixtla_Page_Description_body" xml:space="preserve">
    <value>Select Continue, and you'll be redirected to the model catalog in Azure AI Foundry where you can finish subscribing and start using this model.</value>
  </data>
  <data name="AssetType_Nixtla_Page_primaryButton" xml:space="preserve">
    <value>Continue to Azure AI Foundry model catalog</value>
  </data>
  <data name="AssetType_Core42_Names_plural" xml:space="preserve">
    <value>Core42</value>
  </data>
  <data name="AssetType_Core42_Names_singular" xml:space="preserve">
    <value>Core42</value>
  </data>
  <data name="AssetType_Core42_Names_lowerPlural" xml:space="preserve">
    <value>Core42</value>
  </data>
  <data name="AssetType_Core42_Names_lowerSingular" xml:space="preserve">
    <value>Core42</value>
  </data>
  <data name="AssetType_Core42_Page_title" xml:space="preserve">
    <value>Continue to Azure AI Foundry to use this offer</value>
  </data>
  <data name="AssetType_Core42_Page_Description_intro" xml:space="preserve">
    <value>Ceníme si vašeho zájmu.</value>
  </data>
  <data name="AssetType_Core42_Page_Description_body" xml:space="preserve">
    <value>Select Continue, and you'll be redirected to the model catalog in Azure AI Foundry where you can finish subscribing and start using this model.</value>
  </data>
  <data name="AssetType_Core42_Page_primaryButton" xml:space="preserve">
    <value>Continue to Azure AI Foundry model catalog</value>
  </data>
  <data name="AssetType_ModelProvider_Names_plural" xml:space="preserve">
    <value>Zprostředkovatel modelu</value>
  </data>
  <data name="AssetType_ModelProvider_Names_singular" xml:space="preserve">
    <value>Zprostředkovatel modelu</value>
  </data>
  <data name="AssetType_ModelProvider_Names_lowerPlural" xml:space="preserve">
    <value>Zprostředkovatel modelu</value>
  </data>
  <data name="AssetType_ModelProvider_Names_lowerSingular" xml:space="preserve">
    <value>Zprostředkovatel modelu</value>
  </data>
  <data name="AssetType_ModelProvider_Page_title" xml:space="preserve">
    <value>Continue to Azure AI Foundry to use this offer</value>
  </data>
  <data name="AssetType_ModelProvider_Page_Description_intro" xml:space="preserve">
    <value>Ceníme si vašeho zájmu.</value>
  </data>
  <data name="AssetType_ModelProvider_Page_Description_body" xml:space="preserve">
    <value>Select Continue, and you'll be redirected to the model catalog in Azure AI Foundry where you can finish subscribing and start using this model.</value>
  </data>
  <data name="AssetType_ModelProvider_Page_primaryButton" xml:space="preserve">
    <value>Continue to Azure AI Foundry model catalog</value>
  </data>
  <data name="AssetType_AIStudio_Names_plural" xml:space="preserve">
    <value>Azure AI Foundry</value>
  </data>
  <data name="AssetType_AIStudio_Names_singular" xml:space="preserve">
    <value>Azure AI Foundry</value>
  </data>
  <data name="AssetType_AIStudio_Names_lowerPlural" xml:space="preserve">
    <value>Azure AI Foundry</value>
  </data>
  <data name="AssetType_AIStudio_Names_lowerSingular" xml:space="preserve">
    <value>Azure AI Foundry</value>
  </data>
  <data name="AssetType_AIStudio_Keywords" xml:space="preserve">
    <value>AI, Hub, AI Studio, Azure AI Studio, AI Foundry, Azure AI Foundry, AI Hub, AI Project, AIStudio, Artificial Intelligence, Preview, Deep Learning, Analytics, Data, NLP, Natural Language Processing, CNN, Neural Network, Train, Computer Vision, Models, Data Science, Classification, Regression, Reinforcement Learning, LLM, Chatbot</value>
  </data>
  <data name="AssetType_AIStudio_Description" xml:space="preserve">
    <value>Vaše platforma pro vytváření řešení generativní AI a vlastních kopilotů</value>
  </data>
  <data name="AssetType_AIStudio_Browse_Commands_CreateAzureAI" xml:space="preserve">
    <value>Create</value>
  </data>
  <data name="AssetType_AIStudio_Browse_Commands_CreateAzureAICommandBar" xml:space="preserve">
    <value>New Azure AI hub</value>
  </data>
  <data name="AssetType_AIStudio_Browse_Commands_CreateAzureAILabel" xml:space="preserve">
    <value>Create Azure AI Foundry</value>
  </data>
  <data name="AssetType_AIStudio_Browse_Commands_CreateAzureAIProject" xml:space="preserve">
    <value>Project</value>
  </data>
  <data name="AssetType_AIStudio_Browse_Commands_CreateAzureAIProjectLabel" xml:space="preserve">
    <value>Create Project</value>
  </data>
  <data name="AssetType_AIStudio_Browse_Commands_CreateAzureAIHub" xml:space="preserve">
    <value>Hub</value>
  </data>
  <data name="AssetType_AIStudio_Browse_Commands_CreateAzureAIHubLabel" xml:space="preserve">
    <value>Create Hub</value>
  </data>
  <data name="AssetType_AIStudio_Browse_Description_CreateAzureAIProject" xml:space="preserve">
    <value>Collaborate, organize, and track work to build AI apps.</value>
  </data>
  <data name="AssetType_AIStudio_Browse_Description_CreateAzureAIHub" xml:space="preserve">
    <value>Grouping container for projects. Provides security, connectivity, and compute management.</value>
  </data>
  <data name="AssetType_AIStudio_KeysAndEndpoints_showKeys" xml:space="preserve">
    <value>Zobrazit klíče</value>
  </data>
  <data name="AssetType_AIStudio_KeysAndEndpoints_hideKeys" xml:space="preserve">
    <value>Skrýt klíče</value>
  </data>
  <data name="AssetType_AIStudio_KeysAndEndpoints_regenerateKeys" xml:space="preserve">
    <value>Znovu vygenerovat klíče</value>
  </data>
  <data name="AssetType_AIStudio_KeysAndEndpoints_title" xml:space="preserve">
    <value>Klíče a koncové body</value>
  </data>
  <data name="AssetType_AIStudio_KeysAndEndpoints_message" xml:space="preserve">
    <value>Tyto klíče slouží k přístupu k rozhraní API služeb Azure AI. Své klíče s nikým nesdílejte. Ukládejte je bezpečně – například pomocí Azure Key Vault. Doporučujeme je také pravidelně obnovovat. K volání rozhraní API se vyžaduje jen jeden klíč. Během obnovování prvního klíče můžete pro nepřerušený přístup ke službě použít druhý klíč.</value>
  </data>
  <data name="AssetType_AIStudio_KeysAndEndpoints_key1" xml:space="preserve">
    <value>KLÍČ 1</value>
  </data>
  <data name="AssetType_AIStudio_KeysAndEndpoints_key2" xml:space="preserve">
    <value>KLÍČ 2</value>
  </data>
  <data name="AssetType_AIStudio_KeysAndEndpoints_endpoint" xml:space="preserve">
    <value>Koncový bod</value>
  </data>
  <data name="AssetType_AIStudio_KeysAndEndpoints_Command_regenKey1" xml:space="preserve">
    <value>Znovu vygenerovat klíč 1</value>
  </data>
  <data name="AssetType_AIStudio_KeysAndEndpoints_Command_regenKey2" xml:space="preserve">
    <value>Znovu vygenerovat klíč 2</value>
  </data>
  <data name="AssetType_AIStudio_Hub_Essentials_AIServices_label" xml:space="preserve">
    <value>Poskytovatel služeb AI</value>
  </data>
  <data name="AssetType_AIStudio_Hub_Essentials_DefaultProjectResourceGroup_label" xml:space="preserve">
    <value>Skupina prostředků projektu (výchozí)</value>
  </data>
  <data name="AssetType_AIStudio_Hub_Overview_Banner_title" xml:space="preserve">
    <value>Govern the environment for your team in AI Foundry</value>
  </data>
  <data name="AssetType_AIStudio_Hub_Overview_Banner_Description_label" xml:space="preserve">
    <value>Vaše centrum Azure AI poskytuje zabezpečení na podnikové úrovni a prostředí pro spolupráci při vytváření řešení AI. Centrálně auditujte využití a náklady a nastavte připojení k prostředkům vaší společnosti, které můžou používat všechny projekty. {}</value>
    <comment>{}: learnMoreText</comment>
  </data>
  <data name="AssetType_AIStudio_Hub_Overview_Banner_Description_learnMoreText" xml:space="preserve">
    <value>learn more about the Azure AI Foundry</value>
  </data>
  <data name="AssetType_AIStudio_Hub_Overview_Banner_launchButtonText" xml:space="preserve">
    <value>Launch Azure AI Foundry</value>
  </data>
  <data name="AssetType_AIStudio_Hub_AIServices_Settings_title" xml:space="preserve">
    <value>Vytvořit nové Azure AI služby</value>
  </data>
  <data name="AssetType_AIStudio_Hub_Dropdown_label" xml:space="preserve">
    <value>Hub</value>
  </data>
  <data name="AssetType_AIStudio_Hub_Dropdown_info" xml:space="preserve">
    <value>Grouping container for projects. Provides security, connectivity, and compute management.</value>
  </data>
  <data name="AssetType_AIStudio_Hub_Dropdown_createNewAriaLabel" xml:space="preserve">
    <value>Create new Hub</value>
  </data>
  <data name="AssetType_AIStudio_Project_Overview_Banner_title" xml:space="preserve">
    <value>Start building in Azure AI Foundry</value>
  </data>
  <data name="AssetType_AIStudio_Project_Overview_Banner_Description_label" xml:space="preserve">
    <value>Zprovozněte vývoj řešení AI s využitím předem připravených šablon a pracujte na projektu v kódu nebo ve studiu.</value>
  </data>
  <data name="AssetType_AIStudio_Project_Overview_Banner_launchButtonText" xml:space="preserve">
    <value>Spustit studio</value>
  </data>
  <data name="CreateBlade_Network_SubnetDelegate_title" xml:space="preserve">
    <value>Delegate Azure virtual network subnet for agents</value>
  </data>
  <data name="CreateBlade_Network_SubnetDelegate_message" xml:space="preserve">
    <value>Follows different management boundaries than your hub and projects, injected into your Azure VNET.</value>
  </data>
  <data name="CreateBlade_Network_subnetDelegate_learnMore" xml:space="preserve">
    <value>For more information, see</value>
  </data>
  <data name="Networking_PublicAccess_FailurePublicAccesNetworkNotificationDescription" xml:space="preserve">
    <value>An error occurred while saving public access settings. Please try again or contact support if the issue persists.</value>
  </data>
  <data name="Networking_PublicAccess_FailurePublicAccesNetworkNotificationTitle" xml:space="preserve">
    <value>Failed to Save Public Access Settings</value>
  </data>
  <data name="Networking_PublicAccess_InprogressPublicAccesNetworkNotificationDescription" xml:space="preserve">
    <value>Your changes to public access settings are being saved.</value>
  </data>
  <data name="Networking_PublicAccess_InprogressPublicAccesNetworkNotificationTitle" xml:space="preserve">
    <value>Saving Public Access Settings</value>
  </data>
  <data name="Networking_PublicAccess_SuccessPublicAccesNetworkNotificationDescription" xml:space="preserve">
    <value>Your changes to public access settings have been successfully saved.</value>
  </data>
  <data name="Networking_PublicAccess_SuccessPublicAccesNetworkNotificationTitle" xml:space="preserve">
    <value>Public Access Settings Saved</value>
  </data>
  <data name="Network_NetworkIsolation_ErrorWorkspaceManagedOutboundAccessNotificationDescription" xml:space="preserve">
    <value>An error occurred while saving workspace managed outbound access settings. Please try again or contact support if the issue persists.</value>
  </data>
  <data name="Network_NetworkIsolation_ErrorWorkspaceManagedOutboundAccessNotificationTitle" xml:space="preserve">
    <value>Failed to Save Workspace Managed Outbound Access Settings</value>
  </data>
  <data name="Network_NetworkIsolation_InProgressWorkspaceManagedOutboundAccessNotificationDescription" xml:space="preserve">
    <value>Your changes to workspace managed outbound access settings are being saved.</value>
  </data>
  <data name="Network_NetworkIsolation_InProgressWorkspaceManagedOutboundAccessNotificationTitle" xml:space="preserve">
    <value>Saving Workspace Managed Outbound Access Settings</value>
  </data>
  <data name="Network_NetworkIsolation_SuccessWorkspaceManagedOutboundAccessNotificationDescription" xml:space="preserve">
    <value>Your changes to workspace managed outbound access settings have been successfully saved.</value>
  </data>
  <data name="Network_NetworkIsolation_SuccessWorkspaceManagedOutboundAccessNotificationTitle" xml:space="preserve">
    <value>Workspace Managed Outbound Access Settings Saved</value>
  </data>
  <data name="AssetTypeNames_MachineLearningServices_Default_lowerPlural" xml:space="preserve">
    <value>pracovní prostory</value>
  </data>
  <data name="CES_ManagedWorkspaceOutbound_question" xml:space="preserve">
    <value>How easy or difficult was it to manage workspace outbound network settings on this page?</value>
  </data>
  <data name="CES_PrivateEndpoints__question" xml:space="preserve">
    <value>How easy or difficult was it to manage private endpoints connections on this page?</value>
  </data>
  <data name="CES_PublicAccess__question" xml:space="preserve">
    <value>How easy or difficult was it to manage public access networking settings on this page?</value>
  </data>
  <data name="CVA_ManagedWorkspaceOutbound_question" xml:space="preserve">
    <value>How valuable was this experience for managing workspace outbound network settings?</value>
  </data>
  <data name="CVA_PrivateEndpoints_question" xml:space="preserve">
    <value>How valuable was this experience for managing private endpoints connections?</value>
  </data>
  <data name="CVA_PublicAccess_question" xml:space="preserve">
    <value>How valuable was this experience for managing public access network settings?</value>
  </data>
  <data name="Toolbar_feedback" xml:space="preserve">
    <value>Feedback</value>
  </data>
  <data name="CreateBlade_Network_WorkspaceApprovedOutbound_OutboundAccess_hubRecommendedTargetDescription" xml:space="preserve">
    <value>There are several optional outbound targets recommended for your hub in scenarios such as VS Code, Prompt Flow, and more. You can modify or delete them.</value>
  </data>
  <data name="CreateBlade_Network_WorkspaceApprovedOutbound_OutboundAccess_hubRequiredTargetDescription" xml:space="preserve">
    <value>There are a few outbound targets added by Azure AI Foundry that are required for your workspace to access things like storage, key vault, and more.</value>
  </data>
</root>