﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="ResourceNameRequired" xml:space="preserve">
    <value>資源名稱為必要項</value>
    <comment>Required field validation text for create textbox</comment>
  </data>
  <data name="CreateBladeTitle" xml:space="preserve">
    <value>Azure Machine Learning</value>
    <comment>PLEASE DO NOT TRANSLATE</comment>
  </data>
  <data name="CreateBladeHubTitle" xml:space="preserve">
    <value>Azure AI 中樞</value>
  </data>
  <data name="CreateBladeHubNameLabel" xml:space="preserve">
    <value>Hub</value>
  </data>
  <data name="CreateBladeProjectTitle" xml:space="preserve">
    <value>Azure AI project</value>
  </data>
  <data name="CreateBladeSubtitle" xml:space="preserve">
    <value>建立機器學習工作區</value>
  </data>
  <data name="CreateBladeHubSubtitle" xml:space="preserve">
    <value>建立 Azure AI 中樞資源</value>
  </data>
  <data name="CreateBladeProjectSubtitle" xml:space="preserve">
    <value>Organize and track work, collaborate with others and upload data. Access your work in Azure AI Foundry or Azure Machine Learning Studio.</value>
  </data>
  <data name="CreateBladeRegistrySubtitle" xml:space="preserve">
    <value>建立機器學習登錄</value>
  </data>
  <data name="CreateBladeWorkspaceNameLabel" xml:space="preserve">
    <value>名稱</value>
  </data>
  <data name="CreateBladeRegistryNameLabel" xml:space="preserve">
    <value>名稱</value>
  </data>
  <data name="CreateBladeRegistryDescriptionLabel" xml:space="preserve">
    <value>描述</value>
  </data>
  <data name="CreateBladeRegistryDescriptionKey" xml:space="preserve">
    <value>描述</value>
  </data>
  <data name="CreateBladeFriendlyNameLabel" xml:space="preserve">
    <value>易記名稱</value>
  </data>
  <data name="CreateBladeFriendlyNameInfo" xml:space="preserve">
    <value>Display name of the AI hub that will be displayed in AI Foundry</value>
  </data>
  <data name="CreateBladeHubConfigDefaultResourceGroup_label" xml:space="preserve">
    <value>預設專案資源群組</value>
  </data>
  <data name="CreateBladeHubConfigDefaultResourceGroup_info" xml:space="preserve">
    <value>當使用者未指定其專案的資源群組時，預設即會使用這個資源群組。專案建立者在專案執行個體上獲得 Azure RBAC 擁有者角色指派</value>
  </data>
  <data name="CreateBladeHubConfigDefaultResourceGroup_sameAsHubLabel" xml:space="preserve">
    <value>Same as hub resource group</value>
  </data>
  <data name="CreateBladeHubAIServices_ariaLabel" xml:space="preserve">
    <value>連線 AI 服務，包括 OpenAI</value>
  </data>
  <data name="CreateBladeHubAIServices_label" xml:space="preserve">
    <value>連線 AI 服務，包括 OpenAI</value>
  </data>
  <data name="CreateBladeHubAIServices_info" xml:space="preserve">
    <value>Microsoft 維護的基礎模型提供者。在您的 Azure 訂用帳戶中管理為另一個資源。</value>
  </data>
  <data name="CreateBladeHubAIServices_skipText" xml:space="preserve">
    <value>略過連線 AI 服務</value>
  </data>
  <data name="CreateBladeDescriptionLabel" xml:space="preserve">
    <value>描述</value>
  </data>
  <data name="CreateBladeSubscriptionLabel" xml:space="preserve">
    <value>訂用帳戶</value>
  </data>
  <data name="CreateBladeSubscriptionIdLabel" xml:space="preserve">
    <value>訂用帳戶識別碼</value>
  </data>
  <data name="CreateBladeResourceGroupLabel" xml:space="preserve">
    <value>資源群組</value>
  </data>
  <data name="CreateBladeLocationLabel" xml:space="preserve">
    <value>位置</value>
  </data>
  <data name="CreateBladeMainTabTitle" xml:space="preserve">
    <value>主要</value>
  </data>
  <data name="CreateBladeTagTabTitle" xml:space="preserve">
    <value>標籤</value>
  </data>
  <data name="CreateBladeReviewTabTitle" xml:space="preserve">
    <value>檢閱</value>
  </data>
  <data name="CreateBladeCreateButtonName" xml:space="preserve">
    <value>建立</value>
  </data>
  <data name="CreateBladeReviewCreateButtonName" xml:space="preserve">
    <value>評論及建立</value>
  </data>
  <data name="CreateBladeWorkspaceDeploymentErrorText" xml:space="preserve">
    <value>工作區部署錯誤</value>
  </data>
  <data name="CreateBladeTemplateErrorText" xml:space="preserve">
    <value>範本錯誤</value>
  </data>
  <data name="downloadTemplateLinkText" xml:space="preserve">
    <value>下載自動化的範本</value>
  </data>
  <data name="AssetType_description" xml:space="preserve">
    <value>您可在工作區管理與機器學習專案相關的所有模型、資產與資料。立即建立一個工作區，以開始使用 Azure Machine Learning。</value>
    <comment>PLEASE DO NOT TRANSLATE "Azure Machine Learning"</comment>
  </data>
  <data name="AssetType_keywords" xml:space="preserve">
    <value>ML, AML, Machine Learning, AI, 人工智慧, 預覽, 深度學習, 分析, 資料, NLP, 自然語言處理, CNN, 神經網路, Workbench, 定型, Notebooks, AutoML, 設計工具, 電腦視覺, 模型, 資料科學, 分類, 迴歸, 強化學習</value>
  </data>
  <data name="Overview" xml:space="preserve">
    <value>概觀</value>
  </data>
  <data name="OverviewKeywords" xml:space="preserve">
    <value>摘要首頁</value>
  </data>
  <data name="ValidationWorkspaceNameAlreadyInUse" xml:space="preserve">
    <value>此工作區名稱已存在</value>
  </data>
  <data name="ValidationHubNameAlreadyInUse" xml:space="preserve">
    <value>AI 中樞名稱已存在</value>
  </data>
  <data name="ValidationProjectNameAlreadyInUse" xml:space="preserve">
    <value>This AI project name already exists</value>
  </data>
  <data name="ValidationRegistryNameAlreadyInUse" xml:space="preserve">
    <value>這個登錄名稱已經存在</value>
  </data>
  <data name="ValidationRegistryDescriptionTooLarge" xml:space="preserve">
    <value>此登錄描述具有長度為 256 個字元的上限。</value>
  </data>
  <data name="ValidationWorkspaceNameAlreadyInUseAndSoftDeleted" xml:space="preserve">
    <value>本工作區名稱正在使用中，或仍由先前已虛刪除的工作區所保留。請使用其他名稱。</value>
  </data>
  <data name="AdditionalResourceInfo" xml:space="preserve">
    <value>為您的方便起見，下列資源會自動新增至工作區 (視區域可用性): &lt;a href={0} target="_blank"&gt;Azure 儲存體&lt;/a&gt;、&lt;a href={1} target="_blank"&gt;Azure Application Insights&lt;/a&gt; 和 &lt;a href={2} target="_blank"&gt;Azure Key Vault&lt;/a&gt;。</value>
  </data>
  <data name="ValidationWorkspaceNameInvalid" xml:space="preserve">
    <value>資源名稱必須介於 3 到 33 個字元之間。第一個字元必須是英數字元，而其餘的字元可包含連字號及底線。不允許空白字元。</value>
  </data>
  <data name="ValidationRegistryNameInvalid" xml:space="preserve">
    <value>登錄名稱的長度必須介於 3 到 33 個字元之間。第一個字元必須是英數字元，而其餘字元可包含連字號及底線。不允許空白字元。</value>
  </data>
  <data name="ValidationWorkspaceNameReserved" xml:space="preserve">
    <value>這個工作區名稱會保留</value>
  </data>
  <data name="ValidationStorageAccountNameAlreadyInUse" xml:space="preserve">
    <value>此儲存體帳戶名稱已經存在</value>
  </data>
  <data name="ValidationError" xml:space="preserve">
    <value>嘗試驗證資源時發生錯誤</value>
  </data>
  <data name="ValidationContainerRegistryNameAlreadyInUse" xml:space="preserve">
    <value>此容器登錄名稱已存在</value>
  </data>
  <data name="ValidationDependentResourcesAlreadyInUse" xml:space="preserve">
    <value>具有此名稱的相依資源已存在</value>
  </data>
  <data name="ValidationAppInsightsNameAlreadyInUse" xml:space="preserve">
    <value>此應用程式見解名稱已經存在</value>
  </data>
  <data name="ValidationAIServicesNameAlreadyInUse" xml:space="preserve">
    <value>此 AI 服務名稱已存在</value>
  </data>
  <data name="ValidationKeyVaultNameAlreadyInUse" xml:space="preserve">
    <value>此金鑰保存庫名稱已經存在</value>
  </data>
  <data name="ValidationRegionNotSelected" xml:space="preserve">
    <value>請至少選取一個區域。</value>
  </data>
  <data name="ValidationPrimaryRegionNotSelected" xml:space="preserve">
    <value>請確認主要區域是其中一個選取的值</value>
  </data>
  <data name="WorkspaceErrorMessage" xml:space="preserve">
    <value>選取的訂用帳戶無權註冊資源提供者。如需詳細資訊，&lt;a href = {0} target = "_blank"&gt;請按一下這裡&lt;/a&gt;。</value>
  </data>
  <data name="AssetTypeNames_MachineLearningServices_lowerPlural" xml:space="preserve">
    <value>工作區</value>
  </data>
  <data name="AssetTypeNames_MachineLearningServices_lowerSingular" xml:space="preserve">
    <value>工作區</value>
  </data>
  <data name="AssetTypeNames_MachineLearningServices_plural" xml:space="preserve">
    <value>Azure Machine Learning</value>
    <comment>PLEASE DO NOT TRANSLATE</comment>
  </data>
  <data name="AssetTypeNames_MachineLearningServices_singular" xml:space="preserve">
    <value>Azure Machine Learning 工作區</value>
    <comment>PLEASE DO NOT TRANSLATE "Azure Machine Learning"</comment>
  </data>
  <data name="ColumnMachineLearningWorkspaceId" xml:space="preserve">
    <value>工作區 ID</value>
  </data>
  <data name="ColumnCreationTime" xml:space="preserve">
    <value>建立日期</value>
  </data>
  <data name="LabelComputeMenuItem" xml:space="preserve">
    <value>計算</value>
  </data>
  <data name="LabelProjectsMenuItem" xml:space="preserve">
    <value>實驗</value>
  </data>
  <data name="LabelTasksMenuItem" xml:space="preserve">
    <value>活動</value>
  </data>
  <data name="LabelDeploymentsMenuItem" xml:space="preserve">
    <value>部署</value>
  </data>
  <data name="LabelImagesMenuItem" xml:space="preserve">
    <value>影像</value>
  </data>
  <data name="LabelModelsMenuItem" xml:space="preserve">
    <value>模型</value>
  </data>
  <data name="LabelPipelinesMenuItem" xml:space="preserve">
    <value>管線</value>
  </data>
  <data name="LabelProperties" xml:space="preserve">
    <value>屬性</value>
  </data>
  <data name="TitleWebWorkspaceBlade" xml:space="preserve">
    <value>機器學習</value>
  </data>
  <data name="LabelAutoMLMenuItem" xml:space="preserve">
    <value>自動化機器學習</value>
  </data>
  <data name="TitleMonitoringLens" xml:space="preserve">
    <value>快速入門</value>
  </data>
  <data name="TitleGiveFeedback" xml:space="preserve">
    <value>提供意見反應</value>
  </data>
  <data name="LabelStorage" xml:space="preserve">
    <value>儲存空間</value>
  </data>
  <data name="LabelRegistry" xml:space="preserve">
    <value>Azure Container Registry</value>
  </data>
  <data name="LabelInsights" xml:space="preserve">
    <value>Application Insights</value>
  </data>
  <data name="LabelKeyVault" xml:space="preserve">
    <value>金鑰保存庫</value>
  </data>
  <data name="ManagedKeyVault" xml:space="preserve">
    <value>Microsoft-managed</value>
  </data>
  <data name="LabelProjectHubResource" xml:space="preserve">
    <value>Azure AI 中樞</value>
  </data>
  <data name="TitleMachineLearningServicesDeleteConfirmationMessageBox" xml:space="preserve">
    <value>刪除工作區</value>
  </data>
  <data name="TitleMachineLearningServicesRegistryDeleteConfirmationMessageBox" xml:space="preserve">
    <value>刪除登錄</value>
  </data>
  <data name="TitleError" xml:space="preserve">
    <value>錯誤</value>
  </data>
  <data name="CommandDelete" xml:space="preserve">
    <value>刪除</value>
  </data>
  <data name="CommandCancel" xml:space="preserve">
    <value>取消</value>
  </data>
  <data name="RecentlyDeletedWorkspaces" xml:space="preserve">
    <value>最近刪除</value>
  </data>
  <data name="ProgressMachineLearningServicesDeleteExecuting" xml:space="preserve">
    <value>正在刪除資源</value>
  </data>
  <data name="ProgressMachineLearningRegistryDeleteExecuting" xml:space="preserve">
    <value>正在刪除登錄</value>
  </data>
  <data name="ProgressMachineLearningDeleteError" xml:space="preserve">
    <value>無法刪除資源。</value>
  </data>
  <data name="ProgressMachineLearningRegistryDeleteError" xml:space="preserve">
    <value>無法刪除登錄。</value>
  </data>
  <data name="TextViennaGitHubLinkBody" xml:space="preserve">
    <value>從機器學習範例的大集錦中獲得啟發。</value>
  </data>
  <data name="TextViennaGitHubLinkHeader" xml:space="preserve">
    <value>在 GitHub 檢視更多範例</value>
  </data>
  <data name="TextMachineLearningServicesManageRegistryLinkBody" xml:space="preserve">
    <value>了解如何建立與管理 Machine Learning 登錄。</value>
  </data>
  <data name="TextMachineLearningServicesManageRegistryLinkHeader" xml:space="preserve">
    <value>建立及管理</value>
  </data>
  <data name="TextMachineLearningServicesManageFeatureStoreLinkBody" xml:space="preserve">
    <value>了解如何建立與管理 Machine Learning 登錄。</value>
  </data>
  <data name="TextMachineLearningServicesManageFeatoreStoreLinkHeader" xml:space="preserve">
    <value>建立及管理</value>
  </data>
  <data name="TextMachineLearningServicesShareRegistryLinkBody" xml:space="preserve">
    <value>了解如何使用登錄共用 Machine Learning 資產。</value>
  </data>
  <data name="TextMachineLearningServicesShareRegistryLinkHeader" xml:space="preserve">
    <value>共用模型、元件和環境</value>
  </data>
  <data name="TextMachineLearningServicesDocumentationLinkBody" xml:space="preserve">
    <value>了解如何使用 Azure Machine Learning。</value>
  </data>
  <data name="TextMachineLearningServicesDocumentationLinkHeader" xml:space="preserve">
    <value>檢視文件</value>
  </data>
  <data name="TextMachineLearningServicesForumLinkBody" xml:space="preserve">
    <value>加入 Azure Machine Learning 的討論。 </value>
  </data>
  <data name="TextMachineLearningServicesForumLinkHeader" xml:space="preserve">
    <value>檢視論壇</value>
  </data>
  <data name="TextLaunchWebWorkspace" xml:space="preserve">
    <value>探索您的 Machine Learning 工作區，以執行及追蹤實驗、比較模型效能，以及部署模型。</value>
  </data>
  <data name="TextLaunchWebWorkspaceHeader" xml:space="preserve">
    <value>探索您的 Azure Machine Learning 工作區</value>
  </data>
  <data name="ConfirmationMachineLearningDelete" xml:space="preserve">
    <value>確定要刪除工作區 {0} 嗎?</value>
  </data>
  <data name="ConfirmationMachineLearningRegistryDelete" xml:space="preserve">
    <value>是否確定要刪除登錄 {0}?</value>
  </data>
  <data name="ProgressNotificationMachineLearningServicesDeleteError" xml:space="preserve">
    <value>嘗試刪除工作區名稱 '{workspaceName}' 時發生錯誤。{lineBreak}錯誤詳細資料:{lineBreak}{errorMessage}。</value>
  </data>
  <data name="ProgressNotificationMachineLearningServicesDeleteErrorTitle" xml:space="preserve">
    <value>資源刪除錯誤</value>
  </data>
  <data name="ProgressNotificationMachineLearningServicesDeleteCancelTitle" xml:space="preserve">
    <value>資源刪除已取消</value>
  </data>
  <data name="ProgressNotificationMachineLearningRegistryDeleteError" xml:space="preserve">
    <value>刪除登錄名稱 '{workspaceName}' 時發生錯誤。{lineBreak}錯誤詳細資料:{lineBreak}{errorMessage}。</value>
  </data>
  <data name="ProgressNotificationMachineLearningRegistryDeleteErrorTitle" xml:space="preserve">
    <value>登錄刪除錯誤</value>
  </data>
  <data name="ProgressNotificationMachineLearningServicesDeleteExecuting" xml:space="preserve">
    <value>刪除資源 '{workspaceName}' 進行中...</value>
  </data>
  <data name="ProgressNotificationMachineLearningServicesDeleteExecutingTitle" xml:space="preserve">
    <value>正在刪除資源...</value>
  </data>
  <data name="ProgressNotificationMachineLearningRegistryDeleteExecuting" xml:space="preserve">
    <value>登錄 '{registryName}' 刪除進行中...</value>
  </data>
  <data name="ProgressNotificationMachineLearningRegistryDeleteExecutingTitle" xml:space="preserve">
    <value>正在刪除登錄...</value>
  </data>
  <data name="ProgressNotificationMachineLearningServicesDeleteSuccess" xml:space="preserve">
    <value>已成功刪除資源 '{workspaceName}'。</value>
  </data>
  <data name="ProgressNotificationMachineLearningServicesDeleteSuccessTitle" xml:space="preserve">
    <value>成功刪除</value>
  </data>
  <data name="ProgressNotificationMachineLearningRegistryDeleteSuccess" xml:space="preserve">
    <value>已成功刪除登錄 '{registryName}'。</value>
  </data>
  <data name="ProgressNotificationMachineLearningRegistryDeleteSuccessTitle" xml:space="preserve">
    <value>登錄已刪除</value>
  </data>
  <data name="ErrorJsonParsingException" xml:space="preserve">
    <value>發生未預期的剖析錯誤。</value>
  </data>
  <data name="CommandDownloadConfig" xml:space="preserve">
    <value>下載 config.json</value>
  </data>
  <data name="CommandDownloadConfigTooltip" xml:space="preserve">
    <value>使用此檔案來載入您 Azure ML SDK 筆記本或 Python 指令碼中的工作區設定</value>
  </data>
  <data name="labelCommandButtonRefresh" xml:space="preserve">
    <value>重新整理</value>
  </data>
  <data name="CommandCreateProject" xml:space="preserve">
    <value>Create project</value>
  </data>
  <data name="textNotAvailable" xml:space="preserve">
    <value>無</value>
    <comment>short for 'Not Available'</comment>
  </data>
  <data name="quotaBladeTitle" xml:space="preserve">
    <value>使用量 + 配額</value>
  </data>
  <data name="labelResource" xml:space="preserve">
    <value>資源</value>
  </data>
  <data name="machineLearningCompute" xml:space="preserve">
    <value>受控計算</value>
  </data>
  <data name="quotaTableHeaderQuota" xml:space="preserve">
    <value>資源名稱</value>
  </data>
  <data name="quotaTableHeaderDedicated" xml:space="preserve">
    <value>專用核心使用量</value>
  </data>
  <data name="quotaTableHeaderLowPriority" xml:space="preserve">
    <value>低優先順序核心使用量</value>
  </data>
  <data name="quotaTableNoData" xml:space="preserve">
    <value>無任何可顯示的資料</value>
  </data>
  <data name="quotaTableTotalSubscriptionQuota" xml:space="preserve">
    <value>訂用帳戶配額總計</value>
  </data>
  <data name="labelRequestQuota" xml:space="preserve">
    <value>要求配額</value>
  </data>
  <data name="quotaTableServerError" xml:space="preserve">
    <value>伺服器在處理目前的要求時，發生錯誤。請再次重新整理該資料表。</value>
  </data>
  <data name="quotaSubscriptionViewTabHeader" xml:space="preserve">
    <value>訂用帳戶檢視</value>
  </data>
  <data name="quotaSubscriptionViewResourceNameHelptext" xml:space="preserve">
    <value>擴充每個 VM 系列，依工作區檢視配額的配置及資源使用量，並進一步檢視叢集與執行個體。</value>
  </data>
  <data name="quotaSubscriptionViewClusterHelptext" xml:space="preserve">
    <value>針對叢集，我們會顯示您目前配置的核心數及其可縮放的最大核心數。</value>
  </data>
  <data name="quotaSubscriptionViewUsageHelptext" xml:space="preserve">
    <value>若為執行個體，即使是「已停止」狀態也會繼續使用配額，因此您可以隨時重新啟動。</value>
  </data>
  <data name="quotaWorkspaceViewTabHeader" xml:space="preserve">
    <value>工作區檢視</value>
  </data>
  <data name="quotaWorkspaceViewResourceNameHelptext" xml:space="preserve">
    <value>擴充每個工作區，依 VM 系列檢視配額的配置及資源使用量，並進一步檢視叢集與執行個體。</value>
  </data>
  <data name="quotaWorkspaceViewClusterHelptext" xml:space="preserve">
    <value>針對叢集，我們會顯示您目前配置的核心數及其可縮放的最大核心數。</value>
  </data>
  <data name="quotaWorkspaceViewUsageHelptext" xml:space="preserve">
    <value>若為執行個體，即使是「已停止」狀態也會繼續使用配額，因此您可以隨時重新啟動。</value>
  </data>
  <data name="quotaQuotaSettingTabHeader" xml:space="preserve">
    <value>設定配額</value>
  </data>
  <data name="quotaWorkspaceQuotaExceedSubscriptionLimit" xml:space="preserve">
    <value>工作區層級配額不能超過訂用帳戶層級配額限制。</value>
  </data>
  <data name="quotaWorkspaceQuotaInvalidVMFamilyName" xml:space="preserve">
    <value>請指定 $region 區域支援且您訂用帳戶層級配額所屬的 VM 家族。</value>
  </data>
  <data name="quotaWorkspaceQuotaLessThanMinimumClusterCores" xml:space="preserve">
    <value>工作區層級配額不能少於支援此工作區叢集及執行個體最小節點數所需的核心數。</value>
  </data>
  <data name="quotaWorkspaceQuotaInsufficientPermissions" xml:space="preserve">
    <value>未授權您在工作區層級設定配額。請連絡您的訂用帳戶系統管理員，以協助配置工作區間的資源。</value>
  </data>
  <data name="quotaWorkspaceQuotaNewLimitHelpText" xml:space="preserve">
    <value>在此設定以配置工作區間的訂用帳戶層級配額。若要變更所有訂用帳戶層級配額，請使用底部的 [要求配額] 按鈕。請注意，您必須是訂用帳戶擁有者才能修改這些值。</value>
  </data>
  <data name="quotaWorkspaceQuotaResourceNameHelpText" xml:space="preserve">
    <value>擴充每個 VM 大小，以檢視及配置工作區間的訂用帳戶層級配額。</value>
  </data>
  <data name="quotaWorkspaceQuotaUnknownError" xml:space="preserve">
    <value>未知的錯誤</value>
  </data>
  <data name="quotaWorkspaceQuotaPlaceHolder" xml:space="preserve">
    <value>未配置的核心數: {0}，最大值: {1}</value>
  </data>
  <data name="quotaNote" xml:space="preserve">
    <value>注意:</value>
  </data>
  <data name="labelQuotaUsage" xml:space="preserve">
    <value>使用量 + 配額</value>
  </data>
  <data name="titleSupport" xml:space="preserve">
    <value>支援與疑難排解</value>
  </data>
  <data name="titleSettings" xml:space="preserve">
    <value>設定</value>
  </data>
  <data name="titleInsights" xml:space="preserve">
    <value>Insights (preview)</value>
  </data>
  <data name="quotaRequestTitle" xml:space="preserve">
    <value>配額詳細資料</value>
  </data>
  <data name="quotaRequestHeader" xml:space="preserve">
    <value>請輸入您要求之任何資源的限制:</value>
  </data>
  <data name="quotaProviderRegisteringErrorMsg" xml:space="preserve">
    <value>正在向 '{1}' 資源提供者註冊您的訂用帳戶 {0}。</value>
  </data>
  <data name="quotaProviderNotRegisteredErrorMsg" xml:space="preserve">
    <value>您的訂用帳戶 {0} 未向 '{1}' 資源提供者註冊。請建立要自動註冊的「Machine Learning 服務」工作區，然後重試提交支援要求。</value>
  </data>
  <data name="quotaRequestVMFamily" xml:space="preserve">
    <value>VM 系列</value>
  </data>
  <data name="quotaRequestNewLimit" xml:space="preserve">
    <value>新的 vCPU 限制</value>
  </data>
  <data name="quotaRequestResourceName" xml:space="preserve">
    <value>資源名稱</value>
  </data>
  <data name="quotaRequestCurrentLimit" xml:space="preserve">
    <value>目前的 vCPU 限制</value>
  </data>
  <data name="quotaRequestNotFound" xml:space="preserve">
    <value>在指定位置找不到任何配額資源</value>
  </data>
  <data name="quotaRequestSubmit" xml:space="preserve">
    <value>儲存並繼續</value>
  </data>
  <data name="quotaRequestLogOutputMessageDetail" xml:space="preserve">
    <value>{0} - 目前的值: {1} / 要求的值: {2}</value>
  </data>
  <data name="quotaRequestDocumentationInfoBox" xml:space="preserve">
    <value>按一下以深入了解計算 (核心/vCPU) 配額增加要求。</value>
  </data>
  <data name="Quota_Link_BladeTitle" xml:space="preserve">
    <value>Request and view quota in Azure AI Foundry</value>
    <comment>PLEASE DO NOT TRANSLATE "Azure AI"</comment>
  </data>
  <data name="Quota_Link_BladeDescription" xml:space="preserve">
    <value>依訂用帳戶和地區檢視配額，並直接從工作室要求配額。</value>
  </data>
  <data name="Quota_Link_Button" xml:space="preserve">
    <value>檢視配額</value>
  </data>
  <data name="ResourceLocationColumn" xml:space="preserve">
    <value>位置</value>
  </data>
  <data name="SelectLocation" xml:space="preserve">
    <value>選取位置</value>
  </data>
  <data name="SelectSubscription" xml:space="preserve">
    <value>選取訂閱</value>
  </data>
  <data name="ValidationCreateWorkspacePermission" xml:space="preserve">
    <value>您不具必要的權限 ({0})，無法在選取的資源群組下建立帳戶</value>
  </data>
  <data name="CreateBladeWorkspaceCreationProgressNotification" xml:space="preserve">
    <value>正在建立工作區</value>
  </data>
  <data name="LabelDataMenuItem" xml:space="preserve">
    <value>資料 (預覽)</value>
  </data>
  <data name="labelMLStudioLauncher" xml:space="preserve">
    <value>視覺化介面</value>
  </data>
  <data name="subtitleEnvironmentInfoBlade" xml:space="preserve">
    <value>設定用於部署及管理模型的計算</value>
  </data>
  <data name="titleEnvironmentInfoBlade" xml:space="preserve">
    <value>Machine Learning Compute</value>
  </data>
  <data name="mlStudioLaunchLabel" xml:space="preserve">
    <value>啟動視覺化介面</value>
  </data>
  <data name="mlStudioTitle" xml:space="preserve">
    <value>視覺化介面 (預覽)</value>
  </data>
  <data name="mlStudioSubtitle" xml:space="preserve">
    <value>視覺化介面的功能</value>
  </data>
  <data name="mlStudioCapability1" xml:space="preserve">
    <value>拖放功能可建置機器學習模型</value>
  </data>
  <data name="mlStudioCapability2" xml:space="preserve">
    <value>模型定型沒有資料大小或計算容量的限制</value>
  </data>
  <data name="mlStudioCapability3" xml:space="preserve">
    <value>功能強大的內建 Python 支援</value>
  </data>
  <data name="mlStudioCapability4" xml:space="preserve">
    <value>按一下即可部署 Web 服務</value>
  </data>
  <data name="mlStudioCapability5" xml:space="preserve">
    <value>豐富且成長快速的模組支援</value>
  </data>
  <data name="PropertiesBladeStorageAccountIdLabel" xml:space="preserve">
    <value>儲存體帳戶識別碼</value>
  </data>
  <data name="PropertiesBladeApplicationInsightsLabel" xml:space="preserve">
    <value>Application Insights</value>
  </data>
  <data name="PropertiesBladeContainerRegistryIdLabel" xml:space="preserve">
    <value>容器登錄識別碼</value>
  </data>
  <data name="PropertiesBladeKeyVaultIdLabel" xml:space="preserve">
    <value>Key Vault 識別碼</value>
  </data>
  <data name="PropertiesBladeResourceGroupLabel" xml:space="preserve">
    <value>資源群組</value>
  </data>
  <data name="PropertiesBladeResourceIdLabel" xml:space="preserve">
    <value>資源識別碼</value>
  </data>
  <data name="Properties_Save" xml:space="preserve">
    <value>儲存</value>
  </data>
  <data name="Properties_Saving" xml:space="preserve">
    <value>正在儲存...</value>
  </data>
  <data name="Properties_Discard" xml:space="preserve">
    <value>捨棄變更</value>
  </data>
  <data name="Properties_Refresh" xml:space="preserve">
    <value>重新整理</value>
  </data>
  <data name="PropertiesBladeSubscriptionNameLabel" xml:space="preserve">
    <value>訂用帳戶名稱</value>
  </data>
  <data name="PropertiesBladeSubscriptionIdLabel" xml:space="preserve">
    <value>訂用帳戶 ID</value>
  </data>
  <data name="PropertiesBladeWorkspaceIdLabel" xml:space="preserve">
    <value>Machine Learning 工作區識別碼</value>
  </data>
  <data name="PropertiesBladeWorkspaceHubIdLabel" xml:space="preserve">
    <value>AI 中樞識別碼</value>
  </data>
  <data name="PropertiesBladeCreationDateLabel" xml:space="preserve">
    <value>建立時間</value>
  </data>
  <data name="PropertiesBladeLocationLabel" xml:space="preserve">
    <value>位置</value>
  </data>
  <data name="labelWorkstationsMenuItem" xml:space="preserve">
    <value>筆記本 VM</value>
  </data>
  <data name="titleAuthoringGroup" xml:space="preserve">
    <value>製作 (預覽)</value>
  </data>
  <data name="titleAssetsGroup" xml:space="preserve">
    <value>資產</value>
  </data>
  <data name="labelDataLabeling" xml:space="preserve">
    <value>資料標記</value>
  </data>
  <data name="textMachineLearningServicesAutoMLLinkBody" xml:space="preserve">
    <value>自動從現有的資料建立模型。</value>
  </data>
  <data name="textMachineLearningServicesAutoMLLinkHeader" xml:space="preserve">
    <value>建立新的自動化機器學習模型 (預覽)</value>
  </data>
  <data name="textMachineLearningServicesNotebookVMLinkBody" xml:space="preserve">
    <value>快速開始使用 Python SDK，並使用 Azure Machine Learning 筆記本 VM 執行範例實驗。</value>
  </data>
  <data name="quickLinkUnderOverviewBladeAriaLabel" xml:space="preserve">
    <value>[概觀] 刀鋒視窗下的快速連結。</value>
  </data>
  <data name="textMachineLearningServicesNotebookVMLinkHeader" xml:space="preserve">
    <value>開始使用範例 Notebook (預覽)</value>
  </data>
  <data name="textMachineLearningServicesVisualInterfaceLinkBody" xml:space="preserve">
    <value>拖放現有元件以建立新的模型。</value>
  </data>
  <data name="textMachineLearningServicesVisualInterfaceLinkHeader" xml:space="preserve">
    <value>使用視覺化介面建置模型 (預覽)</value>
  </data>
  <data name="createBladeNextStepsMLFlowAzureMLDescription" xml:space="preserve">
    <value>如何搭配 Azure ML 使用 MLflow</value>
  </data>
  <data name="textAzureDatabricksOptionUseExisting" xml:space="preserve">
    <value>使用現有的</value>
  </data>
  <data name="textAzureDatabricksOptionCreateNew" xml:space="preserve">
    <value>新建</value>
  </data>
  <data name="workspaceTwoNoticeMessage" xml:space="preserve">
    <value>
      Contents of this page will be moving to a new immersive experience for
      managing the end-to-end machine learning lifecycle. Compute targets will
      be manageable from both locations. Features provided in preview are
      offered at no additional charge but may not remain so after general
      availability.
    </value>
  </data>
  <data name="workspaceTwoNoticeButton" xml:space="preserve">
    <value>
      Launch now
    </value>
  </data>
  <data name="workspaceTwoBannerButton" xml:space="preserve">
    <value>
      Launch now
    </value>
  </data>
  <data name="workspaceTwoBannerLink" xml:space="preserve">
    <value>
      Learn more
    </value>
  </data>
  <data name="workspaceTwoBannerBody" xml:space="preserve">
    <value>
      An immersive experience for managing the end-to-end machine learning lifecycle.
    </value>
  </data>
  <data name="workspaceTwoBannerBodyPreview" xml:space="preserve">
    <value>
      Introducing a new immersive experience (preview) for managing the end-to-end machine learning lifecycle.
    </value>
  </data>
  <data name="workspaceTwoBannerBasicSKUTitle" xml:space="preserve">
    <value>
      Launch Preview Now
    </value>
  </data>
  <data name="workspaceTwoBannerBasicSKUBody" xml:space="preserve">
    <value>
      Introducing a new immersive experience (preview) for managing the end-to-end machine learning lifecycle.
    </value>
  </data>
  <data name="Hub_Projects_Title" xml:space="preserve">
    <value>
      Projects
    </value>
  </data>
  <data name="Hub_Projects_Toolbar_Refresh" xml:space="preserve">
    <value>
      Refresh
    </value>
  </data>
  <data name="Hub_Projects_Toolbar_Add" xml:space="preserve">
    <value>
      Add
    </value>
  </data>
  <data name="Hub_Projects_LoadFailure" xml:space="preserve">
    <value>
      Associated projects of Azure AI hub with ID "{0}" could not be loaded.
    </value>
  </data>
  <data name="workspaceSKUPropertyLabel" xml:space="preserve">
    <value>
      Workspace edition
    </value>
  </data>
  <data name="workspaceUpgradeBannerText" xml:space="preserve">
    <value>請將此工作區升級為企業版 (預覽)，以使用視覺化的機器學習、進階自動化機器學習及管理配額。</value>
  </data>
  <data name="workspaceUpgradeQuickLinkBannerText" xml:space="preserve">
    <value>
      Upgrade this workspace to Enterprise edition (preview) to use UI-based tools for all skill levels and more.
    </value>
  </data>
  <data name="workspaceUpgradeQuickLinkPostUpgradeBannerText" xml:space="preserve">
    <value>
      Use the Enterprise edition (preview) to access UI-based tools for all skill levels, built-in MLOps and more
    </value>
  </data>
  <data name="workspaceUpgradeButtonText" xml:space="preserve">
    <value>
      Upgrade
    </value>
  </data>
  <data name="workspaceCancelUpgradeButtonText" xml:space="preserve">
    <value>
      Cancel
    </value>
  </data>
  <data name="workspaceConfirmUpgradeButtonText" xml:space="preserve">
    <value>
      Confirm Upgrade
    </value>
  </data>
  <data name="workspaceUpgradeLearnMore" xml:space="preserve">
    <value>
      Learn more
    </value>
  </data>
  <data name="workspaceUpgradeAboutOurPricing" xml:space="preserve">
    <value>
      about our pricing
    </value>
  </data>
  <data name="workspaceUpgradeConfirmationBoxTitle" xml:space="preserve">
    <value>
      Confirm workspace upgrade
    </value>
  </data>
  <data name="workspaceUpgradeConfirmationBoxContent" xml:space="preserve">
    <value>
      Upgrade workspace to access Enterprise (preview) capabilities. Visit our {0} for more information.
    </value>
  </data>
  <data name="workspaceUpgradePricingPage" xml:space="preserve">
    <value>
      pricing page
    </value>
  </data>
  <data name="workspaceUpgradeUpgrading" xml:space="preserve">
    <value>
      Upgrading Azure Machine Learning workspace {0}
    </value>
  </data>
  <data name="workspaceUpgradeUpgradeSucceeded" xml:space="preserve">
    <value>
      Upgrade Azure Machine Learning workspace {0} succeeded.
    </value>
  </data>
  <data name="workspaceUpgradeUpgradeFailed" xml:space="preserve">
    <value>
      An unexpected error occurred, please try upgrading your workspace again, or submitting a support ticket.
    </value>
  </data>
  <data name="workspaceUpgradeAuthorizationFailed" xml:space="preserve">
    <value>
      You currently do not have permissions to upgrade your workspace. Please contact your IT administrator.
    </value>
  </data>
  <data name="workspaceUpgradeUpgradingNotificationTitle" xml:space="preserve">
    <value>
      Workspace is currently upgrading
    </value>
  </data>
  <data name="workspaceUpgradeUpgradingNotificationContent" xml:space="preserve">
    <value>
      Your workspace {0} is upgrading from Basic to Enterprise
    </value>
  </data>
  <data name="workspaceUpgradeUpgradeCompleteNotificationTitle" xml:space="preserve">
    <value>
      Workspace Upgrade Complete
    </value>
  </data>
  <data name="workspaceUpgradeUpgradeCompleteNotificationContent" xml:space="preserve">
    <value>
      Your workspace {0} upgraded successfully.
    </value>
  </data>
  <data name="workspaceUpgradeUpgradeFailedNotificationTitle" xml:space="preserve">
    <value>
      Workspace Upgrade Failed
    </value>
  </data>
  <data name="workspaceUpgradeUpgradeFailedNotificationContent" xml:space="preserve">
    <value>
      Your workspace {0} did not upgrade successfully.
    </value>
  </data>
  <data name="workspaceCreateSKUPricingDetails" xml:space="preserve">
    <value>
      View full pricing details
    </value>
  </data>
  <data name="workspaceCreateSKUTooltip" xml:space="preserve">
    <value>
      The Enterprise SKU (preview) boosts productivity for all ML skill levels and offers robust MLOps capabilities to manage the complete ML lifecycle. THe Basic SKU workspace allows for open source development and a code-first experience.
    </value>
  </data>
  <data name="workspaceUpgradeQuickLinkLearnAboutEnterpriseEdition" xml:space="preserve">
    <value>
      Learn about Enterprise Edition (preview)
    </value>
  </data>
  <data name="workspaceUpgradeBulletPoint" xml:space="preserve">
    <value>
      You need an Enterprise edition (preview) workspace to view and set quotas on Enterprise workspaces.
    </value>
  </data>
  <data name="workspaceUpgradeSetQuotaOperationNotAllowed" xml:space="preserve">
    <value>
      This feature is disabled for you. Please upgrade to an Enterprise edition (preview) workspace to configure quotas.
    </value>
  </data>
  <data name="workspaceLoadFailure" xml:space="preserve">
    <value>
      Workspace with ID "{0}" could not be loaded.
    </value>
  </data>
  <data name="automationLink" xml:space="preserve">
    <value>下載自動化的範本</value>
  </data>
  <data name="basics" xml:space="preserve">
    <value>基本</value>
  </data>
  <data name="basicsBladeHubDetailsIntro" xml:space="preserve">
    <value>請選取要用於管理所部署之資源與費用的訂用帳戶。使用資料夾之類的資源群組來組織和管理您所有的資源。AI 中樞是可供小組共用專案工作、模型端點、計算、(資料) 連線、安全性設定、控管使用方式的共同作業環境。</value>
  </data>
  <data name="basicsBladeProjectDetailsIntro" xml:space="preserve">
    <value>Select a subscription and resource group to organize this and other resources, inherit access, and attribute cost.</value>
  </data>
  <data name="basicsBladeDetailsIntro" xml:space="preserve">
    <value>每個工作區都必須指派給 Azure 訂用帳戶，這是計費的發生位置。您可以使用資料夾之類的資源群組來組織和管理資源，包括您即將建立的工作區。</value>
  </data>
  <data name="basicsRegistryBladeDetailsIntro" xml:space="preserve">
    <value>每個登錄工作區都必須指派給 Azure 訂用帳戶 (計費的發生位置)。您可以使用資料夾之類的資源群組來組織和管理資源，包括您即將建立的工作區。</value>
  </data>
  <data name="basicsBladeInstanceIntro" xml:space="preserve">
    <value>設定基本工作區設定，例如其儲存體連線、驗證、容器等。</value>
  </data>
  <data name="basicsBladeRegistryInstanceIntro" xml:space="preserve">
    <value>設定您的基本登錄設定，例如其名稱和描述。</value>
  </data>
  <data name="basicsTabTitle" xml:space="preserve">
    <value>基本</value>
  </data>
  <data name="buttonCreate" xml:space="preserve">
    <value>建立</value>
  </data>
  <data name="buttonNext" xml:space="preserve">
    <value>下一步: {0}</value>
    <comment>0: step name</comment>
  </data>
  <data name="buttonNextPlaceholder" xml:space="preserve">
    <value>下一步 &gt;</value>
  </data>
  <data name="buttonPrevious" xml:space="preserve">
    <value>&lt; 上一步</value>
  </data>
  <data name="buttonReviewCreate" xml:space="preserve">
    <value>檢閱 + 建立</value>
  </data>
  <data name="createBladeWorkspaceDetails" xml:space="preserve">
    <value>工作區詳細資料</value>
  </data>
  <data name="CreateBlade_Workspace_Type" xml:space="preserve">
    <value>工作區類型</value>
  </data>
  <data name="createLocationLabelDefault" xml:space="preserve">
    <value>位置</value>
  </data>
  <data name="createLocationLabelRegion" xml:space="preserve">
    <value>區域</value>
  </data>
  <data name="createLocationLabelPrimaryRegion" xml:space="preserve">
    <value>主要區域</value>
  </data>
  <data name="createResourceGroup" xml:space="preserve">
    <value>資源群組</value>
  </data>
  <data name="createResourceGroupCreateNewPlaceholder" xml:space="preserve">
    <value />
  </data>
  <data name="createResourceGroupTitle" xml:space="preserve">
    <value>資源群組</value>
  </data>
  <data name="createSubscriptionLabel" xml:space="preserve">
    <value>訂用帳戶</value>
  </data>
  <data name="createTagsTabIntro" xml:space="preserve">
    <value>標籤為成對的名稱和數值，可讓您透過將相同標籤套用至多個資源與資源群組，進而分類資源並檢視合併的帳單。</value>
  </data>
  <data name="createTagsTabLearnMoreText" xml:space="preserve">
    <value>深入了解標籤</value>
  </data>
  <data name="createTagsTabTitle" xml:space="preserve">
    <value>標籤</value>
  </data>
  <data name="createTagsTabUpdateNotice" xml:space="preserve">
    <value>請注意，若您在建立標籤後變更其他索引標籤上的資源設定，您的標籤將會自動更新。</value>
  </data>
  <data name="createTemplateValidationError" xml:space="preserve">
    <value>驗證失敗。按一下這裡以檢視詳細資料。</value>
  </data>
  <data name="createTemplateValidationInProgress" xml:space="preserve">
    <value>正在執行最後驗證...</value>
  </data>
  <data name="createTemplateValidationSuccess" xml:space="preserve">
    <value>已通過驗證</value>
  </data>
  <data name="detailsLabel" xml:space="preserve">
    <value>資源詳細資料</value>
  </data>
  <data name="instanceLabel" xml:space="preserve">
    <value>執行個體詳細資料</value>
  </data>
  <data name="learnMore" xml:space="preserve">
    <value>深入了解</value>
  </data>
  <data name="location" xml:space="preserve">
    <value>位置</value>
  </data>
  <data name="multipleInvalidTabErrorMessage" xml:space="preserve">
    <value>下列索引標籤的驗證失敗: {0}。缺少必要資訊或其無效。</value>
  </data>
  <data name="newResource" xml:space="preserve">
    <value>(新增) {0}</value>
  </data>
  <data name="newResourceCapitalized" xml:space="preserve">
    <value>(新增) {0}</value>
  </data>
  <data name="none" xml:space="preserve">
    <value>無</value>
  </data>
  <data name="singleInvalidTabErrorMessage" xml:space="preserve">
    <value>下列索引標籤的驗證失敗: {0}。缺少必要資訊或其無效。</value>
  </data>
  <data name="summaryTabTitle" xml:space="preserve">
    <value>檢閱 + 建立</value>
  </data>
  <data name="tabValidationErrors" xml:space="preserve">
    <value>驗證失敗。缺少必要資訊或其無效。</value>
  </data>
  <data name="tags" xml:space="preserve">
    <value>標籤</value>
  </data>
  <data name="PrivateEndpoints_filterByName" xml:space="preserve">
    <value>依名稱篩選...</value>
  </data>
  <data name="PrivateEndpoints_label" xml:space="preserve">
    <value>私人端點連線</value>
  </data>
  <data name="PrivateEndpoints_Toolbar_refresh" xml:space="preserve">
    <value>重新整理</value>
  </data>
  <data name="PrivateEndpoints_GridColumns_name" xml:space="preserve">
    <value>連線名稱</value>
  </data>
  <data name="PrivateEndpoints_GridColumns_privateEndpoint" xml:space="preserve">
    <value>私人端點</value>
  </data>
  <data name="PrivateEndpoints_GridColumns_status" xml:space="preserve">
    <value>連線狀態</value>
  </data>
  <data name="PrivateEndpoints_GridColumns_description" xml:space="preserve">
    <value>描述</value>
  </data>
  <data name="PrivateEndpoints_Status_pending" xml:space="preserve">
    <value>暫止</value>
  </data>
  <data name="PrivateEndpoints_Status_approved" xml:space="preserve">
    <value>已核准</value>
  </data>
  <data name="PrivateEndpoints_Status_rejected" xml:space="preserve">
    <value>已拒絕</value>
  </data>
  <data name="PrivateEndpoints_Status_disconnected" xml:space="preserve">
    <value>已中斷連線</value>
  </data>
  <data name="PrivateEndpoints_menuText" xml:space="preserve">
    <value>網路</value>
  </data>
  <data name="PrivateEndpoints_Commands_addPrivateEndpoint" xml:space="preserve">
    <value>私人端點</value>
  </data>
  <data name="PrivateEndpoints_Commands_approve" xml:space="preserve">
    <value>核准</value>
  </data>
  <data name="PrivateEndpoints_Commands_ApproveNotifications_Failure_message" xml:space="preserve">
    <value>無法核准 {0}/{1} 個私人端點連線</value>
  </data>
  <data name="PrivateEndpoints_Commands_ApproveNotifications_Failure_title" xml:space="preserve">
    <value>無法核准私人端點連線</value>
  </data>
  <data name="PrivateEndpoints_Commands_ApproveNotifications_InProgress_message" xml:space="preserve">
    <value>正在核准 {0} 個私人端點連線</value>
  </data>
  <data name="PrivateEndpoints_Commands_ApproveNotifications_InProgress_title" xml:space="preserve">
    <value>正在核准私人端點連線...</value>
  </data>
  <data name="PrivateEndpoints_Commands_ApproveNotifications_Success_message" xml:space="preserve">
    <value>已成功核准 {0} 個私人端點連線。</value>
  </data>
  <data name="PrivateEndpoints_Commands_ApproveNotifications_Success_title" xml:space="preserve">
    <value>已成功核准私人端點連線</value>
  </data>
  <data name="PrivateEndpoints_Commands_Approve_messagePlural" xml:space="preserve">
    <value>要核准 {0} 個選取的連線嗎?</value>
  </data>
  <data name="PrivateEndpoints_Commands_Approve_messageSingular" xml:space="preserve">
    <value>要核准連線 '{0}' 嗎?</value>
  </data>
  <data name="PrivateEndpoints_Commands_Approve_title" xml:space="preserve">
    <value>核准連線</value>
  </data>
  <data name="PrivateEndpoints_Commands_DeleteNotifications_Failure_message" xml:space="preserve">
    <value>無法刪除 {0}/{1} 個私人端點連線</value>
  </data>
  <data name="PrivateEndpoints_Commands_DeleteNotifications_Failure_title" xml:space="preserve">
    <value>無法刪除私人端點連線</value>
  </data>
  <data name="PrivateEndpoints_Commands_DeleteNotifications_InProgress_message" xml:space="preserve">
    <value>正在刪除 {0} 個私人端點連線</value>
  </data>
  <data name="PrivateEndpoints_Commands_DeleteNotifications_InProgress_title" xml:space="preserve">
    <value>正在刪除私人端點連線...</value>
  </data>
  <data name="PrivateEndpoints_Commands_DeleteNotifications_Success_message" xml:space="preserve">
    <value>已成功刪除 {0} 個私人端點連線。</value>
  </data>
  <data name="PrivateEndpoints_Commands_DeleteNotifications_Success_title" xml:space="preserve">
    <value>已成功刪除私人端點連線</value>
  </data>
  <data name="PrivateEndpoints_Commands_description" xml:space="preserve">
    <value>描述</value>
  </data>
  <data name="PrivateEndpoints_Commands_reject" xml:space="preserve">
    <value>拒絕</value>
  </data>
  <data name="PrivateEndpoints_Commands_RejectNotifications_Failure_message" xml:space="preserve">
    <value>無法拒絕 {0}/{1} 個私人端點連線</value>
  </data>
  <data name="PrivateEndpoints_Commands_RejectNotifications_Failure_title" xml:space="preserve">
    <value>無法拒絕私人端點連線</value>
  </data>
  <data name="PrivateEndpoints_Commands_RejectNotifications_InProgress_message" xml:space="preserve">
    <value>正在拒絕 {0} 個私人端點連線</value>
  </data>
  <data name="PrivateEndpoints_Commands_RejectNotifications_InProgress_title" xml:space="preserve">
    <value>正在拒絕私人端點連線...</value>
  </data>
  <data name="PrivateEndpoints_Commands_RejectNotifications_Success_message" xml:space="preserve">
    <value>已成功拒絕 {0} 個私人端點連線。</value>
  </data>
  <data name="PrivateEndpoints_Commands_RejectNotifications_Success_title" xml:space="preserve">
    <value>已成功拒絕私人端點連線</value>
  </data>
  <data name="PrivateEndpoints_Commands_Reject_messagePlural" xml:space="preserve">
    <value>要拒絕 {0} 個選取的連線嗎?</value>
  </data>
  <data name="PrivateEndpoints_Commands_Reject_messageSingular" xml:space="preserve">
    <value>要拒絕連線 '{0}' 嗎?</value>
  </data>
  <data name="PrivateEndpoints_Commands_Reject_title" xml:space="preserve">
    <value>拒絕連線</value>
  </data>
  <data name="PrivateEndpoints_Commands_remove" xml:space="preserve">
    <value>移除</value>
  </data>
  <data name="PrivateEndpoints_Commands_Remove_messagePlural" xml:space="preserve">
    <value>要刪除 {0} 個選取的連線嗎?</value>
  </data>
  <data name="PrivateEndpoints_Commands_Remove_messageSingular" xml:space="preserve">
    <value>要刪除連接 '{0}' 嗎?</value>
  </data>
  <data name="PrivateEndpoints_Commands_Remove_title" xml:space="preserve">
    <value>刪除連線</value>
  </data>
  <data name="PrivateEndpoints_Create_validationErrorFormat" xml:space="preserve">
    <value>{0}: {1}</value>
    <comment>0: error code, 1: error message</comment>
  </data>
  <data name="PrivateEndpoints_filterByStatus" xml:space="preserve">
    <value>依連線狀態篩選...</value>
  </data>
  <data name="PrivateEndpoints_genericErrorMessage" xml:space="preserve">
    <value>處理您的要求時發生錯誤。請稍後再試一次。</value>
  </data>
  <data name="PrivateEndpoints_Grid_StatusFilter_all" xml:space="preserve">
    <value>所有連線狀態</value>
  </data>
  <data name="PrivateEndpoints_Grid_StatusFilter_itemFormat" xml:space="preserve">
    <value>{0} ({1})</value>
    <comment>0: status name, 1: count</comment>
  </data>
  <data name="PrivateEndpoints_Grid_StatusFilter_some" xml:space="preserve">
    <value>已選取 {0} 個連線狀態</value>
    <comment>0: a number</comment>
  </data>
  <data name="BasicsTab_InfoText_AdditionalResourceInfoText" xml:space="preserve">
    <value>為方便起見，如果當地可以使用，這些資源就會自動新增到工作區: </value>
  </data>
  <data name="BasicsTab_InfoText_AzureApplicationInsights" xml:space="preserve">
    <value>Azure Application Insights</value>
  </data>
  <data name="BasicsTab_InfoText_AzureKeyVault" xml:space="preserve">
    <value>Azure Key Vault</value>
  </data>
  <data name="BasicsTab_InfoText_AzureStorage" xml:space="preserve">
    <value>Azure 儲存體</value>
  </data>
  <data name="BasicsTab_Region_BalloonContent" xml:space="preserve">
    <value>計算目標只能在與工作區相同的區域中建立。請確認所選區域具有工作區計算目標所需的虛擬機器系列。</value>
  </data>
  <data name="BasicsTab_Region_LearnMoreComputeTargets" xml:space="preserve">
    <value>深入了解計算目標</value>
  </data>
  <data name="BasicsTab_Region_ViewAvailableVirtualMachines" xml:space="preserve">
    <value>依區域檢視可用的虛擬機器系列</value>
  </data>
  <data name="BasicsTab_ResourceGroup_BalloonContent" xml:space="preserve">
    <value>資源群組是資源的集合，擁有相同的生命週期、權限及原則。</value>
  </data>
  <data name="BasicsTab_Subscription_BalloonContent" xml:space="preserve">
    <value>Azure 訂用帳戶中的所有資源皆會一併計費。</value>
  </data>
  <data name="BasicsTab_WarningText_WorkspaceErrorMessageText" xml:space="preserve">
    <value>選取的訂用帳戶無權註冊資源提供者。</value>
  </data>
  <data name="BasicsTab_WorkspaceDetails" xml:space="preserve">
    <value>工作區詳細資料</value>
  </data>
  <data name="BasicsTab_WorkspaceHubDetails" xml:space="preserve">
    <value>資源詳細資料</value>
  </data>
  <data name="BasicsTab_RegistryDetails" xml:space="preserve">
    <value>登錄詳細資料</value>
  </data>
  <data name="BasicsTab_Organization" xml:space="preserve">
    <value>組織</value>
  </data>
  <data name="BasicsTab_WorkspaceEdition_ViewFullPricingDetails" xml:space="preserve">
    <value>檢視完整定價詳細資料</value>
  </data>
  <data name="BasicsTab_WorkspaceName_BalloonContent" xml:space="preserve">
    <value>資源群組中的工作區名稱不得重複。</value>
  </data>
  <data name="BasicsTab_HubName_BalloonContent" xml:space="preserve">
    <value>AI 中樞的名稱</value>
  </data>
  <data name="BasicsTab_ProjectName_BalloonContent" xml:space="preserve">
    <value>Name of the AI project</value>
  </data>
  <data name="BasicsTab_RegistryName_BalloonContent" xml:space="preserve">
    <value>資源群組中的登錄名稱不得重複。</value>
  </data>
  <data name="BasicsTab_AIServiceSectionHeader" xml:space="preserve">
    <value>Azure AI 服務基礎模型</value>
  </data>
  <data name="BasicsTab_hubSectionHeader" xml:space="preserve">
    <value>Share security, connectivity, compute</value>
  </data>
  <data name="BasicsTab_hubSectionContent" xml:space="preserve">
    <value>Projects are grouped by a hub, which provides security configurations, pre-configured connectivity with other Azure resources, compute, storage, and quota.</value>
  </data>
  <data name="CreateBlade_Network_connectivityMethod" xml:space="preserve">
    <value>連線方法</value>
  </data>
  <data name="CreateBlade_Network_ConnectivityMethod_allNetworks" xml:space="preserve">
    <value>在所有網路啟用公用存取</value>
  </data>
  <data name="CreateBlade_Network_title" xml:space="preserve">
    <value>網路</value>
  </data>
  <data name="StorageTab_Title" xml:space="preserve">
    <value>儲存體</value>
  </data>
  <data name="StorageTab_Header" xml:space="preserve">
    <value>設定資料的儲存方式</value>
  </data>
  <data name="StorageTab_LogsAndDockerHeader" xml:space="preserve">
    <value>記錄和 Docker 映像</value>
  </data>
  <data name="StorageTab_Description" xml:space="preserve">
    <value>As users work in Azure AI Foundry, uploaded data, stored credentials and generated artifacts like logs are stored.</value>
  </data>
  <data name="EncryptionTab_Title" xml:space="preserve">
    <value>加密</value>
  </data>
  <data name="IdentityTab_Title" xml:space="preserve">
    <value>身分識別</value>
  </data>
  <data name="AdvancedTab_Key_KeyVaultChangeControl_action" xml:space="preserve">
    <value>按一下以選取金鑰</value>
  </data>
  <data name="AdvancedTab_Key_KeyVaultChangeControl_key" xml:space="preserve">
    <value>金鑰: {key}</value>
  </data>
  <data name="AdvancedTab_Key_KeyVaultChangeControl_keyVault" xml:space="preserve">
    <value>金鑰保存庫: {keyvault}</value>
  </data>
  <data name="AdvancedTab_Key_KeyVaultChangeControl_label" xml:space="preserve">
    <value>金鑰保存庫</value>
  </data>
  <data name="AdvancedTab_Key_KeyVaultChangeControl_purgeProtectionRequired" xml:space="preserve">
    <value>當您自備的加密金鑰時，您的 Azure Key Vault 必須啟用清除保護，以防止意外遺失資料存取。</value>
  </data>
  <data name="AdvancedTab_Key_KeyVaultChangeControl_required" xml:space="preserve">
    <value>需要金鑰</value>
  </data>
  <data name="AdvancedTab_Key_KeyVaultChangeControl_title" xml:space="preserve">
    <value>金鑰保存庫與金鑰</value>
  </data>
  <data name="AdvancedTab_Section_DataEncryption_customerManaged" xml:space="preserve">
    <value>客戶管理的金鑰</value>
  </data>
  <data name="AdvancedTab_Section_DataEncryption_header" xml:space="preserve">
    <value>預設會使用 Microsoft 受控金鑰加密您的資料。若要進一步控制您的資料，您可以選擇攜帶您自己的金鑰進行加密。{0}</value>
  </data>
  <data name="AdvancedTab_Section_DataEncryption_microsoftManaged" xml:space="preserve">
    <value>Microsoft 管理的金鑰</value>
  </data>
  <data name="AdvancedTab_Section_DataEncryption_title" xml:space="preserve">
    <value>資料加密</value>
  </data>
  <data name="AdvancedTab_Section_DataEncryption_Type_label" xml:space="preserve">
    <value>使用客戶自控金鑰加密資料</value>
  </data>
  <data name="AdvancedTab_Section_DataEncryption_ServiceSide_label" xml:space="preserve">
    <value>使用服務端加密 ({0})</value>
  </data>
  <data name="AdvancedTab_Section_DataEncryption_WarningMessage" xml:space="preserve">
    <value>建立工作區之後，您無法在 Microsoft 受控金鑰與客戶自控金鑰之間變更加密金鑰類型。</value>
  </data>
  <data name="AdvancedTab_Section_DataEncryption_LinkText" xml:space="preserve">
    <value>深入了解客戶自控金鑰加密。</value>
  </data>
  <data name="AdvancedTab_Section_DataImpact_HBI_label" xml:space="preserve">
    <value>高業務影響工作區</value>
  </data>
  <data name="AdvancedTab_Section_DataImpact_header" xml:space="preserve">
    <value>如果您的工作區包含敏感性資料，您可以指定高業務影響工作區。這會控制 Microsoft 出於診斷目的而收集的資料量，並在 Microsoft 受控環境中啟用額外的加密。</value>
  </data>
  <data name="AdvancedTab_Section_DataImpact_title" xml:space="preserve">
    <value>資料影響</value>
  </data>
  <data name="AdvancedTab__Key_KeyVaultChangeControl_version" xml:space="preserve">
    <value>版本: {version}</value>
  </data>
  <data name="RegionsTab_Title" xml:space="preserve">
    <value>區域</value>
  </data>
  <data name="RegionsTab_Description" xml:space="preserve">
    <value>選取您目前擁有或計畫要建立 AzureML 工作區的區域，並使用此登錄中的資產。您可以選擇稍後再新增更多區域到登錄。</value>
  </data>
  <data name="RegionsTab_AdditionRegionLabel" xml:space="preserve">
    <value>其他區域</value>
  </data>
  <data name="noContent" xml:space="preserve">
    <value>沒有內容</value>
  </data>
  <data name="BasicsTab_WorkspaceEdition_Basic" xml:space="preserve">
    <value>基本</value>
  </data>
  <data name="BasicsTab_WorkspaceEdition_Enterprise" xml:space="preserve">
    <value>企業</value>
  </data>
  <data name="Commands_delete" xml:space="preserve">
    <value>刪除</value>
  </data>
  <data name="CreateBlade_Network_displayPrivateDnsZone" xml:space="preserve">
    <value>私人 DNS 區域</value>
  </data>
  <data name="CreateBlade_Network_displayRegion" xml:space="preserve">
    <value>區域</value>
  </data>
  <data name="CreateBlade_Network_displayResourceGroup" xml:space="preserve">
    <value>資源群組</value>
  </data>
  <data name="CreateBlade_Network_displaySubnet" xml:space="preserve">
    <value>子網路</value>
  </data>
  <data name="CreateBlade_Network_displaySubscription" xml:space="preserve">
    <value>訂用帳戶</value>
  </data>
  <data name="CreateBlade_Network_displayTargetResourceType" xml:space="preserve">
    <value>目標資源類型</value>
  </data>
  <data name="CreateBlade_Network_overviewDescription" xml:space="preserve">
    <value>您可以公開連線到工作區，或使用私人端點私下連線。</value>
  </data>
  <data name="CreateBlade_Network_networkIsolationDescription" xml:space="preserve">
    <value>選擇您的工作區所需的網路隔離類型，從完全不隔離到由 Azure Machine Learning 管理之完全分開的虛擬網路。</value>
  </data>
  <data name="CreateBlade_Network_Hub_networkIsolationDescription" xml:space="preserve">
    <value>與 Azure AI 中樞共用網路相關聯的專案，而且不需要其他設定即可存取您的虛擬網路中的資源。選擇您需要的網路隔離類型，從完全不隔離到由 Azure Machine Learning 管理的完全分開的虛擬網路。</value>
  </data>
  <data name="CreateBlade_Network_networkIsolationLearnMore" xml:space="preserve">
    <value>深入了解受管理的網路隔離</value>
  </data>
  <data name="CreateBlade_Network_registryOverviewDescription" xml:space="preserve">
    <value>您可以公開連線到登錄，或使用私人端點私下連線。</value>
  </data>
  <data name="CreateBlade_Network_overviewTitle" xml:space="preserve">
    <value>網路連線</value>
  </data>
  <data name="CreateBlade_Network_privateEndpointDescription" xml:space="preserve">
    <value>請建立私人端點以允許私人連線連到此資源。</value>
  </data>
  <data name="CreateBlade_Network_PrivateEndpoints_add" xml:space="preserve">
    <value>新增</value>
  </data>
  <data name="CreateBlade_Network_PrivateEndpoints_clickToAdd" xml:space="preserve">
    <value>按一下新增來建立私人端點</value>
  </data>
  <data name="CreateBlade_Network_PrivateEndpoints_locationMismatch" xml:space="preserve">
    <value>工作區 ({0}) 和私人端點連線 ({1}) 必須位於相同位置，才能讓計算執行個體和叢集在虛擬網路中正確存取。</value>
    <comment>0: location, 1: location</comment>
  </data>
  <data name="CreateBlade_Network_PrivateEndpoints_name" xml:space="preserve">
    <value>名稱</value>
  </data>
  <data name="CreateBlade_Network_PrivateEndpoints_nameAndSubResource" xml:space="preserve">
    <value>{0} ({1})</value>
    <comment>0 = the private endpoint name, 1 = the sub resource name.</comment>
  </data>
  <data name="CreateBlade_Network_PrivateEndpoints_nameSubResourceAndDns" xml:space="preserve">
    <value>{0} ({1}) ({2})</value>
    <comment>0 = the private endpoint name, 1 = the sub resource name, 2 = the dns zone name.</comment>
  </data>
  <data name="CreateBlade_Network_PrivateEndpoints_subResourceHelp" xml:space="preserve">
    <value>這是新工作區的特定子資源，可供此私人端點存取。</value>
  </data>
  <data name="CreateBlade_Network_PrivateEndpoints_registrySubResourceHelp" xml:space="preserve">
    <value>此為新登錄的特定子資源，可供此私人端點存取。</value>
  </data>
  <data name="CreateBlade_Network_PrivateEndpoints_subResourceLabel" xml:space="preserve">
    <value>工作區子資源</value>
  </data>
  <data name="CreateBlade_Network_PrivateEndpoints_registrySubResourceLabel" xml:space="preserve">
    <value>登錄子資源</value>
  </data>
  <data name="CreateBlade_Network_PrivateEndpoints_title" xml:space="preserve">
    <value>私人端點</value>
  </data>
  <data name="CreateBlade_Network_PrivateEndpoints_type" xml:space="preserve">
    <value>私人端點</value>
  </data>
  <data name="CreateBlade_Network_privateEndpointTitle" xml:space="preserve">
    <value>私人端點</value>
  </data>
  <data name="CreateBlade_Network_WorkspaceInternetOutbound_PrivateNetworkSettings_title" xml:space="preserve">
    <value>私人網路設定</value>
  </data>
  <data name="CreateBlade_Network_WorkspaceInternetOutbound_PrivateNetworkSettings_description" xml:space="preserve">
    <value>新增私人端點人員可用來存取您的工作區，並選擇如何從您的工作區管理儲存體帳戶、Key Vault 和登錄等的輸出存取。</value>
  </data>
  <data name="CreateBlade_Network_WorkspaceInternetOutbound_OutboundAccess_virtualNetworkManagedByAML" xml:space="preserve">
    <value>使用由 Azure Machine Learning 管理的虛擬網路</value>
  </data>
  <data name="CreateBlade_Network_WorkspaceInternetOutbound_OutboundAccess_virtualNetworkManagedByAMLDescription" xml:space="preserve">
    <value>您的工作區需要私人端點，才能存取儲存空間、筆記本和部署環境等私人資源。您也可以在此新增自訂案例的其他私人連結目標。</value>
  </data>
  <data name="CreateBlade_Network_WorkspaceInternetOutbound_OutboundAccess_virtualNetworkManagedByAMLLearnMore" xml:space="preserve">
    <value>深入了解必要的私人連結目標</value>
  </data>
  <data name="CreateBlade_Network_WorkspaceInternetOutbound_OutboundAccess_useMyOwnVirtualNetwork" xml:space="preserve">
    <value>使用我自己的虛擬網路</value>
  </data>
  <data name="CreateBlade_Network_WorkspaceApprovedOutbound_OutboundAccess_azureFirewallSku" xml:space="preserve">
    <value>Azure Firewall SKU</value>
  </data>
  <data name="CreateBlade_Network_WorkspaceApprovedOutbound_OutboundAccess_azureFirewallSkuDescription" xml:space="preserve">
    <value>Select from Basic or Standard SKU for the Azure Firewall deployment. For more information on Azure Firewall, see</value>
  </data>
  <data name="CreateBlade_Network_WorkspaceApprovedOutbound_OutboundAccess_azureFirewallSkuPricingText" xml:space="preserve">
    <value>Pricing</value>
  </data>
  <data name="CreateBlade_Network_WorkspaceOutboundAccess_provisionManagedVirtualNetwork" xml:space="preserve">
    <value>Provision managed virtual network</value>
  </data>
  <data name="CreateBlade_Network_WorkspaceOutboundAccess_provisionManagedVirtualNetworkSummaryTitle" xml:space="preserve">
    <value>Provision managed virtual network</value>
  </data>
  <data name="CreateBlade_Network_WorkspaceOutboundAccess_provisionManagedVirtualNetworkInfo" xml:space="preserve">
    <value>Managed virtual network will be provisioned at workspace creation. Charges will be incurred for network resources, such as private endpoint.</value>
  </data>
  <data name="CreateBlade_Network_WorkspaceApprovedOutbound_OutboundAccess_requiredTargetDescription" xml:space="preserve">
    <value>您的工作區需要 Azure Machine Learning 新增一些輸出目標，才能存取儲存空間、筆記本和部署環境等專案。</value>
  </data>
  <data name="CreateBlade_Network_WorkspaceApprovedOutbound_OutboundAccess_requiredTargetLearnMore" xml:space="preserve">
    <value>深入了解必要的目標</value>
  </data>
  <data name="CreateBlade_Network_WorkspaceApprovedOutbound_OutboundAccess_recommendedTargetDescription" xml:space="preserve">
    <value>在 AutoML 和資料標籤等案例中，您的工作區建議有數個選擇性輸出目標。您可以修改或刪除它們，</value>
  </data>
  <data name="CreateBlade_Network_WorkspaceApprovedOutbound_OutboundAccess_recommendedTargetLearnMore" xml:space="preserve">
    <value>深入了解建議的目標</value>
  </data>
  <data name="CreateBlade_Network_WorkspaceInternetOutbound_targetDescription" xml:space="preserve">
    <value>工作區需要一些私人端點，才能存取儲存空間等私人資源。您也可以在此新增自訂案例的其他私人連結目標。</value>
  </data>
  <data name="CreateBlade_Network_WorkspaceInternetOutbound_targetLearnMoreLink" xml:space="preserve">
    <value>深入了解必要的私人連結目標</value>
  </data>
  <data name="CreateBlade_Network_outboundRulesGridEmptyMessage" xml:space="preserve">
    <value>遺漏必要的輸出存取規則。</value>
  </data>
  <data name="CreateBlade_Network_outboundRulesPublicInternetEnabledMessage" xml:space="preserve">
    <value>啟用公用網際網路的輸出存取時，不需要輸出規則。</value>
  </data>
  <data name="CreateBlade_Network_outboundRulesNotLoadedMessage" xml:space="preserve">
    <value>設定基本設定以建立輸出規則。</value>
  </data>
  <data name="CreateBlade_Network_WorkspacePrivateOutbound_addUserDefinedOutboundRuleText" xml:space="preserve">
    <value>您也可以在此為自訂案例新增自己的輸出目標。</value>
  </data>
  <data name="CreateBlade_Network_WorkspacePrivateOutbound_userDefinedOutboundRules" xml:space="preserve">
    <value>使用者定義的輸出規則</value>
  </data>
  <data name="CreateBlade_Network_WorkspacePrivateOutbound_recommendedOutboundRules" xml:space="preserve">
    <value>建議的輸出規則</value>
  </data>
  <data name="CreateBlade_Network_WorkspacePrivateOutbound_requiredOutboundRules" xml:space="preserve">
    <value>必要的輸出規則</value>
  </data>
  <data name="CreateBlade_Network_WorkspacePrivateOutbound_dependencyOutboundRules" xml:space="preserve">
    <value>相依性輸出規則</value>
  </data>
  <data name="CreateBlade_Network_workspaceApprovedOutbound_PrivateNetworkSettings_title" xml:space="preserve">
    <value>私人網路和資料外流設定</value>
  </data>
  <data name="CreateBlade_Network_workspaceApprovedOutbound_PrivateNetworkSettings_description" xml:space="preserve">
    <value>新增私人端點人員可用來存取您的工作區，以及管理工作區可存取的輸出目標</value>
  </data>
  <data name="CreateBlade_Network_workspaceInboundAccessTitle" xml:space="preserve">
    <value>工作區輸入存取</value>
  </data>
  <data name="CreateBlade_Network_workspaceOutboundAccessTitle" xml:space="preserve">
    <value>工作區輸出存取</value>
  </data>
  <data name="CreateBlade_Network_WorkspaceOutboundAccess_connectionName" xml:space="preserve">
    <value>連線名稱</value>
  </data>
  <data name="CreateBlade_Network_WorkspaceOutboundAccess_parentRules" xml:space="preserve">
    <value>父代規則</value>
  </data>
  <data name="CreateBlade_Network_WorkspaceOutboundAccess_destinationType" xml:space="preserve">
    <value>目的地類型</value>
  </data>
  <data name="CreateBlade_Network_WorkspaceOutboundAccess_disabledManagedVnetMessage" xml:space="preserve">
    <value>Securing your workspace with a managed network provides network isolation for outbound access from the Hub and managed computes. Once you enable managed virtual network isolation of your Azure AI, you can't disable it.</value>
  </data>
  <data name="CreateBlade_Network_WorkspaceOutboundAccess_disabledManagedVnetMessageLearnMore" xml:space="preserve">
    <value>Learn more</value>
  </data>
  <data name="CreateBlade_Network_WorkspaceOutboundAccess_status" xml:space="preserve">
    <value>狀態</value>
  </data>
  <data name="CreateBlade_Network_WorkspaceOutboundAccess_activeStatusMessage" xml:space="preserve">
    <value>規則已套用且有效。</value>
  </data>
  <data name="CreateBlade_Network_WorkspaceOutboundAccess_inactiveStatusMessage" xml:space="preserve">
    <value>當佈建受控網路時，規則會變成使用中。</value>
  </data>
  <data name="CreateBlade_Network_WorkspaceOutboundAccess_inactivePrivateEndpointStatusMessage" xml:space="preserve">
    <value>當佈建受控網路時，規則會變成使用中，否則請檢查目標資源的核准是否擱置中。</value>
  </data>
  <data name="CreateBlade_Network_WorkspaceOutboundAccess_destination" xml:space="preserve">
    <value>目的地</value>
  </data>
  <data name="CreateBlade_Network_WorkspaceOutboundAccess_addUserDefinedOutboundRules" xml:space="preserve">
    <value>新增使用者定義的輸出規則</value>
  </data>
  <data name="CreateBlade_Network_WorkspaceOutboundAccess_deleteUserDefinedOutboundRules" xml:space="preserve">
    <value>刪除使用者定義的輸出規則</value>
  </data>
  <data name="CreateBlade_Network_networkIsolationTitle" xml:space="preserve">
    <value>網路隔離</value>
  </data>
  <data name="CreateBlade_Network_azureFirewallSku" xml:space="preserve">
    <value>Azure Firewall SKU</value>
  </data>
  <data name="CreateBlade_Network_NetworkIsolation_Public_title" xml:space="preserve">
    <value>公用</value>
  </data>
  <data name="CreateBlade_Network_NetworkIsolation_Public_descriptionItemOne" xml:space="preserve">
    <value>工作區是透過私人端點存取</value>
  </data>
  <data name="CreateBlade_Network_NetworkIsolation_Public_descriptionItemTwo" xml:space="preserve">
    <value>計算可以存取公用資源</value>
  </data>
  <data name="CreateBlade_Network_NetworkIsolation_Public_descriptionItemThree" xml:space="preserve">
    <value>輸出資料移動不受限制</value>
  </data>
  <data name="CreateBlade_Network_NetworkIsolation_Disabled_title" xml:space="preserve">
    <value>已停用</value>
  </data>
  <data name="CreateBlade_Network_NetworkIsolation_AllowInternetOutbound_title" xml:space="preserve">
    <value>允許網際網路輸出</value>
  </data>
  <data name="CreateBlade_Network_NetworkIsolation_AllowOnlyApprovedOutbound_title" xml:space="preserve">
    <value>只允許核准的輸出</value>
  </data>
  <data name="CreateBlade_Network_NetworkIsolation_Public_learnMore" xml:space="preserve">
    <value>深入了解公用網路</value>
  </data>
  <data name="CreateBlade_Network_NetworkIsolation_PrivateInternetOutbound_title" xml:space="preserve">
    <value>有網際網路輸出的私人</value>
  </data>
  <data name="CreateBlade_Network_NetworkIsolation_PrivateInternetOutbound_descriptionItemOne" xml:space="preserve">
    <value>工作區是透過私人端點存取</value>
  </data>
  <data name="CreateBlade_Network_NetworkIsolation_PrivateInternetOutbound_descriptionItemTwo" xml:space="preserve">
    <value>計算可以存取私人資源</value>
  </data>
  <data name="CreateBlade_Network_NetworkIsolation_PrivateInternetOutbound_descriptionItemThree" xml:space="preserve">
    <value>輸出資料移動不受限制</value>
  </data>
  <data name="CreateBlade_Network_NetworkIsolation_PrivateInternetOutbound_learnMore" xml:space="preserve">
    <value>深入了解私人連結</value>
  </data>
  <data name="CreateBlade_Network_NetworkIsolation_PrivateApprovedOutbound_title" xml:space="preserve">
    <value>具有核准輸出的私人項目</value>
  </data>
  <data name="CreateBlade_Network_NetworkIsolation_PrivateApprovedOutbound_descriptionItemOne" xml:space="preserve">
    <value>工作區是透過私人端點存取</value>
  </data>
  <data name="CreateBlade_Network_NetworkIsolation_PrivateApprovedOutbound_descriptionItemTwo" xml:space="preserve">
    <value>計算只能存取允許清單的資源</value>
  </data>
  <data name="CreateBlade_Network_NetworkIsolation_PrivateApprovedOutbound_descriptionItemThree" xml:space="preserve">
    <value>輸出資料移動限制為核准的目標</value>
  </data>
  <data name="CreateBlade_Network_NetworkIsolation_PrivateApprovedOutbound_learnMore" xml:space="preserve">
    <value>深入了解資料外流保護</value>
  </data>
  <data name="newResourceFormatCaps" xml:space="preserve">
    <value>(新增) {0}</value>
    <comment>0 = the name of the resource</comment>
  </data>
  <data name="CreateBlade_Network_PrivateEndpoints_noContent" xml:space="preserve">
    <value>-</value>
  </data>
  <data name="AdvancedTab_Key_KeyVaultChangeControl_version" xml:space="preserve">
    <value>版本: {version}</value>
  </data>
  <data name="CreateBlade_Advanced_Review_customerManagedKeys" xml:space="preserve">
    <value>客戶管理的金鑰</value>
  </data>
  <data name="CreateBlade_Advanced_Review_disabled" xml:space="preserve">
    <value>停用</value>
  </data>
  <data name="CreateBlade_Advanced_Review_enabled" xml:space="preserve">
    <value>啟用</value>
  </data>
  <data name="CreateBlade_Advanced_Review_identityBasedStorageAccountAccess" xml:space="preserve">
    <value>以身分識別為基礎</value>
  </data>
  <data name="CreateBlade_Advanced_Review_credentialBasedStorageAccountAccess" xml:space="preserve">
    <value>以認證為基礎</value>
  </data>
  <data name="CreateBlade_Advanced_Review_enableHBIFlag" xml:space="preserve">
    <value>啟用 HBI 旗標</value>
  </data>
  <data name="CreateBlade_Advanced_Review_storageAccountAccessType" xml:space="preserve">
    <value>儲存體帳戶存取類型</value>
  </data>
  <data name="CreateBlade_Advanced_Review_sharedKeyAccess" xml:space="preserve">
    <value>共用金鑰存取</value>
  </data>
  <data name="CreateBlade_Advanced_Review_encryptionType" xml:space="preserve">
    <value>加密類型</value>
  </data>
  <data name="CreateBlade_Advanced_Review_keyURI" xml:space="preserve">
    <value>金鑰 URI</value>
  </data>
  <data name="CreateBlade_Advanced_Review_keyVault" xml:space="preserve">
    <value>金鑰保存庫</value>
  </data>
  <data name="CreateBlade_Advanced_Review_microsoftManagedKeys" xml:space="preserve">
    <value>Microsoft 管理的金鑰</value>
  </data>
  <data name="CreateBlade_Advanced_Review_Resources_title" xml:space="preserve">
    <value>資源</value>
  </data>
  <data name="CreateBlade_Advanced_Review_Encryption_title" xml:space="preserve">
    <value>加密</value>
  </data>
  <data name="CreateBlade_Advanced_Review_Identity_title" xml:space="preserve">
    <value>身分識別</value>
  </data>
  <data name="AccountPart_deprecated" xml:space="preserve">
    <value>已淘汰</value>
  </data>
  <data name="AccountPart_deprecatedLongMessage" xml:space="preserve">
    <value>資源已無法再使用。請使用機器學習服務工作區延伸模組。</value>
  </data>
  <data name="AccountPart_deprecatedShortMessage" xml:space="preserve">
    <value>請使用機器學習服務延伸模組。</value>
  </data>
  <data name="assetTypeNames_MachineLearningExperimentationAccount_singular" xml:space="preserve">
    <value>已淘汰 - Machine Learning 測試</value>
  </data>
  <data name="Overview_Banner_Description_label" xml:space="preserve">
    <value>Azure Machine Learning 工作室是一種 Web 應用程式，您可以在此建置、訓練、測試及部署 ML 模型。立即啟動以開始探索，或 {}</value>
    <comment>{}: learnMoreText</comment>
  </data>
  <data name="Overview_Banner_Description_learnMoreText" xml:space="preserve">
    <value>深入了解 Azure Machine Learning 工作室</value>
  </data>
  <data name="Overview_Banner_launchButtonText" xml:space="preserve">
    <value>啟動工作室</value>
  </data>
  <data name="Overview_Banner_title" xml:space="preserve">
    <value>在 Azure Machine Learning Studio 中使用您的模型</value>
  </data>
  <data name="Overview_Banner_Registry_title" xml:space="preserve">
    <value>在 Azure Machine Learning Studio 中使用您的登錄</value>
  </data>
  <data name="Overview_Banner_Registry_launchButtonText" xml:space="preserve">
    <value>在 Studio 中啟動登錄</value>
  </data>
  <data name="Overview_Essentials_studioWebURLText" xml:space="preserve">
    <value>工作室 Web URL</value>
  </data>
  <data name="Overview_Essentials_registryWebURLText" xml:space="preserve">
    <value>登錄 Web URL</value>
  </data>
  <data name="Overview_Essentials_mlFlowWebURLText" xml:space="preserve">
    <value>MLFlow 追蹤 URI</value>
  </data>
  <data name="Overview_Essentials_managedResourceGroup" xml:space="preserve">
    <value>受控資源群組</value>
  </data>
  <data name="Overview_Essentials_edit" xml:space="preserve">
    <value>編輯</value>
  </data>
  <data name="Overview_Essentials_ProjectID" xml:space="preserve">
    <value>專案識別碼</value>
  </data>
  <data name="AssetTypeNames_MLApp_lowerPlural" xml:space="preserve">
    <value>機器學習線上端點</value>
  </data>
  <data name="AssetTypeNames_MLApp_lowerSingular" xml:space="preserve">
    <value>機器學習線上端點</value>
  </data>
  <data name="AssetTypeNames_MLApp_plural" xml:space="preserve">
    <value>機器學習線上端點</value>
  </data>
  <data name="AssetTypeNames_MLApp_singular" xml:space="preserve">
    <value>機器學習線上端點</value>
  </data>
  <data name="endpointScoringUriLabel" xml:space="preserve">
    <value>評分 URI</value>
  </data>
  <data name="endpointSwaggerLabel" xml:space="preserve">
    <value>Swagger URI</value>
  </data>
  <data name="endpointProvisioningStateLabel" xml:space="preserve">
    <value>佈建狀態</value>
  </data>
  <data name="endpointAuthModeLabel" xml:space="preserve">
    <value>驗證模式</value>
  </data>
  <data name="endpointId" xml:space="preserve">
    <value>端點識別碼</value>
  </data>
  <data name="onlineEndpointWorkspaceName" xml:space="preserve">
    <value>工作區</value>
  </data>
  <data name="onlineEndpointName" xml:space="preserve">
    <value>機器學習線上端點</value>
  </data>
  <data name="titleDeployments" xml:space="preserve">
    <value>部署</value>
  </data>
  <data name="LabelDeployments" xml:space="preserve">
    <value>部署</value>
  </data>
  <data name="endpointRequestLatencyMetric" xml:space="preserve">
    <value>要求延遲</value>
  </data>
  <data name="endpointRequestsPerMinuteMetric" xml:space="preserve">
    <value>每分鐘的要求數</value>
  </data>
  <data name="AssetTypeNames_MLAppDeployment_lowerPlural" xml:space="preserve">
    <value>機器學習線上部署</value>
  </data>
  <data name="AssetTypeNames_MLAppDeployment_lowerSingular" xml:space="preserve">
    <value>機器學習線上部署</value>
  </data>
  <data name="AssetTypeNames_MLAppDeployment_plural" xml:space="preserve">
    <value>機器學習線上部署</value>
  </data>
  <data name="AssetTypeNames_MLAppDeployment_singular" xml:space="preserve">
    <value>機器學習線上部署</value>
  </data>
  <data name="AssetTypeNames_MLRegistry_lowerPlural" xml:space="preserve">
    <value>Azure Machine Learning 登錄</value>
  </data>
  <data name="AssetTypeNames_MLRegistry_lowerSingular" xml:space="preserve">
    <value>Azure Machine Learning 登錄</value>
  </data>
  <data name="AssetTypeNames_MLRegistry_plural" xml:space="preserve">
    <value>Azure Machine Learning 登錄</value>
  </data>
  <data name="AssetTypeNames_MLRegistry_singular" xml:space="preserve">
    <value>Azure Machine Learning 登錄</value>
  </data>
  <data name="deploymentModelIdLabel" xml:space="preserve">
    <value>模型識別碼</value>
  </data>
  <data name="deploymentProvisioningStateLabel" xml:space="preserve">
    <value>佈建狀態</value>
  </data>
  <data name="deploymentName" xml:space="preserve">
    <value>部署名稱</value>
  </data>
  <data name="deploymentKind" xml:space="preserve">
    <value>部署類型</value>
  </data>
  <data name="deploymentEnvironmentId" xml:space="preserve">
    <value>環境</value>
  </data>
  <data name="deploymentCpuUtilizationMetric" xml:space="preserve">
    <value>CPU 使用率</value>
  </data>
  <data name="deploymentCapacityMetric" xml:space="preserve">
    <value>部署容量</value>
  </data>
  <data name="deploymentDiskUtilizationMetric" xml:space="preserve">
    <value>磁碟使用率</value>
  </data>
  <data name="deploymentMemoryUtilizationMetric" xml:space="preserve">
    <value>記憶體使用量</value>
  </data>
  <data name="AssociatedResource_AppInsights_Properties_changeText" xml:space="preserve">
    <value>變更 Application Insights</value>
  </data>
  <data name="AssociatedResource_ContainerRegistry_Properties_changeText" xml:space="preserve">
    <value>變更容器登錄</value>
  </data>
  <data name="AssociatedResource_AppInsights_ChangeBlade_title" xml:space="preserve">
    <value>選取一個 Application Insights</value>
  </data>
  <data name="AssociatedResource_AppInsights_ReactView_Dropdown_placeholder" xml:space="preserve">
    <value>選取一個 Application Insights</value>
  </data>
  <data name="AssociatedResource_AppInsights_ReactView_Dropdown_noMatchMessage" xml:space="preserve">
    <value>沒有符合 {0} 的結果</value>
  </data>
  <data name="AssociatedResource_AppInsights_ReactView_Dropdown_emptyMessage" xml:space="preserve">
    <value>找不到任何 Application Insights 資源。</value>
  </data>
  <data name="AssociatedResource_AppInsights_ReactView_Dropdown_errorLoadingMessage" xml:space="preserve">
    <value>載入 Application Insights 時發生錯誤</value>
  </data>
  <data name="AssociatedResource_ContainerRegistry_ReactView_Dropdown_placeholder" xml:space="preserve">
    <value>選取容器登錄</value>
  </data>
  <data name="AssociatedResource_ContainerRegistry_ReactView_Dropdown_noMatchMessage" xml:space="preserve">
    <value>沒有符合 {0} 的結果</value>
  </data>
  <data name="AssociatedResource_ContainerRegistry_ReactView_Dropdown_emptyMessage" xml:space="preserve">
    <value>找不到容器登錄資源</value>
  </data>
  <data name="AssociatedResource_ContainerRegistry_ReactView_Dropdown_errorLoadingMessage" xml:space="preserve">
    <value>載入容器登錄時發生錯誤</value>
  </data>
  <data name="AssociatedResource_AppInsights_ReactView_ChangeAppInsights_unauthorizedError" xml:space="preserve">
    <value>您沒有更新此資源的 Application Insights 的權限。</value>
  </data>
  <data name="AssociatedResource_AppInsights_ReactView_ChangeAppInsights_notFoundError" xml:space="preserve">
    <value>找不到要更新的資源。</value>
  </data>
  <data name="AssociatedResource_AppInsights_ReactView_ChangeAppInsights_error" xml:space="preserve">
    <value>更新此資源的 Application Insights 時發生錯誤。</value>
  </data>
  <data name="AssociatedResource_AppInsights_ReactView_ChangeAppInsights_errorTitle" xml:space="preserve">
    <value>更新 Application Insights 時發生錯誤</value>
  </data>
  <data name="AssociatedResource_AppInsights_ReactView_ChangeAppInsights_appInsightsLabel" xml:space="preserve">
    <value>Application Insights</value>
  </data>
  <data name="AssociatedResource_AppInsights_ReactView_ChangeAppInsights_appInsightsUpdatingSpinner" xml:space="preserve">
    <value>Application Insights 更新中...</value>
  </data>
  <data name="AssociatedResource_AppInsights_ReactView_ChangeAppInsights_footerSave" xml:space="preserve">
    <value>儲存</value>
  </data>
  <data name="AssociatedResource_AppInsights_ReactView_ChangeAppInsights_footerCancel" xml:space="preserve">
    <value>捨棄</value>
  </data>
  <data name="AssociatedResource_ContainerRegistry_ReactView_ChangeContainerRegistry_unauthorizedError" xml:space="preserve">
    <value>您沒有為此資源更新容器登錄的權限。</value>
  </data>
  <data name="AssociatedResource_ContainerRegistry_ReactView_ChangeContainerRegistry_notFoundError" xml:space="preserve">
    <value>找不到要更新的資源。</value>
  </data>
  <data name="AssociatedResource_ContainerRegistry_ReactView_ChangeContainerRegistry_error" xml:space="preserve">
    <value>更新此資源的容器登錄時發生錯誤。</value>
  </data>
  <data name="AssociatedResource_ContainerRegistry_ReactView_ChangeContainerRegistry_errorTitle" xml:space="preserve">
    <value>更新容器登錄時發生錯誤</value>
  </data>
  <data name="AssociatedResource_ContainerRegistry_ReactView_ChangeContainerRegistry_containerRegistryLabel" xml:space="preserve">
    <value>容器登錄</value>
  </data>
  <data name="AssociatedResource_ContainerRegistry_ReactView_ChangeContainerRegistry_containerRegistryUpdatingSpinner" xml:space="preserve">
    <value>正在更新容器登錄...</value>
  </data>
  <data name="AssociatedResource_ContainerRegistry_ReactView_ChangeContainerRegistry_footerSave" xml:space="preserve">
    <value>儲存</value>
  </data>
  <data name="AssociatedResource_ContainerRegistry_ReactView_ChangeContainerRegistry_footerCancel" xml:space="preserve">
    <value>捨棄</value>
  </data>
  <data name="AssociatedResource_ContainerRegistry_ChangeBlade_title" xml:space="preserve">
    <value>選取容器登錄</value>
  </data>
  <data name="AssociatedResource_Dropdown_createNewLinkText" xml:space="preserve">
    <value>新建</value>
  </data>
  <data name="AssociatedResource_Dropdown_filterPlaceholder" xml:space="preserve">
    <value>選取現有的...</value>
  </data>
  <data name="AssociatedResource_Dropdown_newResourceText" xml:space="preserve">
    <value>(新增) {0}</value>
    <comment>0: name of a resource</comment>
  </data>
  <data name="AssociatedResource_Dropdown_SettingsBlade_discardButtonText" xml:space="preserve">
    <value>捨棄</value>
  </data>
  <data name="AssociatedResource_Dropdown_SettingsBlade_saveButtonText" xml:space="preserve">
    <value>儲存</value>
  </data>
  <data name="Keyvault_Dropdown_label" xml:space="preserve">
    <value>金鑰保存庫</value>
  </data>
  <data name="Keyvault_Dropdown_SettingsBlade_nameLabel" xml:space="preserve">
    <value>名稱</value>
  </data>
  <data name="Keyvault_Dropdown_SettingsBlade_title" xml:space="preserve">
    <value>建立新的金鑰保存庫</value>
  </data>
  <data name="StorageAccount_Dropdown_label" xml:space="preserve">
    <value>儲存體帳戶</value>
  </data>
  <data name="StorageAccount_Dropdown_SettingsBlade_nameInvalidMessage" xml:space="preserve">
    <value>儲存體名稱長度應介於 3 到 24 個字元之間，而且只可包含小寫字母與數字。</value>
  </data>
  <data name="StorageAccount_Dropdown_SettingsBlade_nameLabel" xml:space="preserve">
    <value>名稱</value>
  </data>
  <data name="StorageAccount_Dropdown_SettingsBlade_performanceLabel" xml:space="preserve">
    <value>效能</value>
  </data>
  <data name="StorageAccount_Dropdown_SettingsBlade_performancePremium" xml:space="preserve">
    <value>進階</value>
  </data>
  <data name="StorageAccount_Dropdown_SettingsBlade_performanceStandard" xml:space="preserve">
    <value>標準</value>
  </data>
  <data name="StorageAccount_Dropdown_SettingsBlade_replicationGRS" xml:space="preserve">
    <value>異地備援儲存體 (GRS)</value>
  </data>
  <data name="StorageAccount_Dropdown_SettingsBlade_replicationGZRS" xml:space="preserve">
    <value>異地區域備援儲存體 (GZRS)</value>
  </data>
  <data name="StorageAccount_Dropdown_SettingsBlade_replicationLabel" xml:space="preserve">
    <value>複寫</value>
  </data>
  <data name="StorageAccount_Dropdown_SettingsBlade_replicationLRS" xml:space="preserve">
    <value>本地備援儲存體 (LRS)</value>
  </data>
  <data name="StorageAccount_Dropdown_SettingsBlade_replicationRAGRS" xml:space="preserve">
    <value>讀取權限異地備援儲存體 (RA-GRS)</value>
  </data>
  <data name="StorageAccount_Dropdown_SettingsBlade_replicationRAGZRS" xml:space="preserve">
    <value>讀取權限異地區域備援儲存體 (RA-GZRS)</value>
  </data>
  <data name="StorageAccount_Dropdown_SettingsBlade_replicationZRS" xml:space="preserve">
    <value>區域備援儲存體 (ZRS)</value>
  </data>
  <data name="StorageAccount_Dropdown_SettingsBlade_title" xml:space="preserve">
    <value>建立新的儲存體帳戶</value>
  </data>
  <data name="Keyvault_Dropdown_SettingsBlade_nameInvalidMessage" xml:space="preserve">
    <value>保存庫名稱必須介於 3 到 24 個英數字元之間。名稱必須以字母開頭、以字母或數字作為結尾，且不包含連續的連字號。</value>
  </data>
  <data name="CreateBlade_Network_ConnectivityMethod_private" xml:space="preserve">
    <value>停用公用存取並使用私人端點</value>
  </data>
  <data name="CreateBlade_Network_ConnectivityMethod_skuPrivateEndpointErrorMessage" xml:space="preserve">
    <value>[有網際網路輸出的私人專案] 和 [具有核准輸出的私人專案] 需要進階 SKU 容器登錄才能擁有私人端點連線。您可以建立新的進階容器登錄，或在 [基本] 索引標籤中選取現有的進階容器登錄，以用於此私人工作區。</value>
  </data>
  <data name="CreateBlade_Network_NetworkIsolation_skuErrorMessage" xml:space="preserve">
    <value>[允許網際網路輸出] 和 [僅允許核准的輸出] 需要進階 SKU 容器登錄才能有私人端點連線。</value>
  </data>
  <data name="AppInsights_Dropdown_label" xml:space="preserve">
    <value>Application Insights</value>
  </data>
  <data name="AppInsights_Dropdown_SettingsBlade_nameInvalidMessage" xml:space="preserve">
    <value>名稱長度必須介於 1 到 255 個字元 (含) 之間。名稱只可包含英數字元、句號、底線、連字號及括號，而且不能以句號結尾。</value>
  </data>
  <data name="AppInsights_Dropdown_SettingsBlade_nameLabel" xml:space="preserve">
    <value>名稱</value>
  </data>
  <data name="AppInsights_Dropdown_SettingsBlade_title" xml:space="preserve">
    <value>建立新的 Application Insights</value>
  </data>
  <data name="AssociatedResource_Dropdown_none" xml:space="preserve">
    <value>無</value>
  </data>
  <data name="ContainerRegistry_Dropdown_label" xml:space="preserve">
    <value>容器登錄</value>
  </data>
  <data name="ContainerRegistry_Dropdown_SettingsBlade_nameInvalidMessage" xml:space="preserve">
    <value>資源名稱只能包含英數字元，而且長度必須介於 5 到 50 個字元之間。</value>
  </data>
  <data name="ContainerRegistry_Dropdown_SettingsBlade_nameLabel" xml:space="preserve">
    <value>名稱</value>
  </data>
  <data name="ContainerRegistry_Dropdown_SettingsBlade_skuBasic" xml:space="preserve">
    <value>基本</value>
  </data>
  <data name="ContainerRegistry_Dropdown_SettingsBlade_skuLabel" xml:space="preserve">
    <value>SKU</value>
  </data>
  <data name="ContainerRegistry_Dropdown_SettingsBlade_skuPremium" xml:space="preserve">
    <value>進階</value>
  </data>
  <data name="ContainerRegistry_Dropdown_SettingsBlade_skuStandard" xml:space="preserve">
    <value>標準</value>
  </data>
  <data name="ContainerRegistry_Dropdown_SettingsBlade_title" xml:space="preserve">
    <value>建立新的容器登錄</value>
  </data>
  <data name="AppInsights_Dropdown_Hub_info" xml:space="preserve">
    <value>AI 中樞會使用 Azure Application Insights 儲存已部署模型的監視資訊。您可以建立新的 Azure Application Insights 資源，或在訂用帳戶中選取現有的資源。</value>
  </data>
  <data name="AppInsights_Dropdown_info" xml:space="preserve">
    <value>工作區使用 Azure Application Insights 儲存已部署模型的監視資訊。您可以建立新的 Azure Application Insights 資源，或在訂用帳戶中選取現有的資源。</value>
  </data>
  <data name="AppInsights_Dropdown_SettingsBlade_Name_infoDescription" xml:space="preserve">
    <value>名稱必須符合下列要求:</value>
  </data>
  <data name="AppInsights_Dropdown_SettingsBlade_Name_infoItem1" xml:space="preserve">
    <value>在資源群組中為唯一的</value>
    <comment>Item1 of AppInsights_Dropdown_SettingsBlade_Name_infoDescription</comment>
  </data>
  <data name="AppInsights_Dropdown_SettingsBlade_Name_infoItem2" xml:space="preserve">
    <value>長度介於 1 到 255 個字元之間</value>
    <comment>Item2 of AppInsights_Dropdown_SettingsBlade_Name_infoDescription</comment>
  </data>
  <data name="AppInsights_Dropdown_SettingsBlade_Name_infoItem3" xml:space="preserve">
    <value>只包含英數字元、句點、底線、連字號及括弧</value>
    <comment>Item3 of AppInsights_Dropdown_SettingsBlade_Name_infoDescription</comment>
  </data>
  <data name="AppInsights_Dropdown_SettingsBlade_Name_infoItem4" xml:space="preserve">
    <value>不得以句號結尾</value>
    <comment>Item4 of AppInsights_Dropdown_SettingsBlade_Name_infoDescription</comment>
  </data>
  <data name="ContainerRegistry_Dropdown_info" xml:space="preserve">
    <value>容器登錄用於登錄訓練和部署所使用的 Docker 映像。為了將成本降至最低，只有在建置第一個映像之後，才會建立新的 Azure Container Registry 資源。您也可以選擇立即建立資源，或在訂用帳戶中選取現有的資源。</value>
  </data>
  <data name="ContainerRegistry_Dropdown_SettingsBlade_Name_infoDescription" xml:space="preserve">
    <value>名稱必須符合下列要求:</value>
  </data>
  <data name="ContainerRegistry_Dropdown_SettingsBlade_Name_infoItem1" xml:space="preserve">
    <value>在 Azure 中的所有容器登錄為唯一的</value>
    <comment>Item1 of ContainerRegistry_Dropdown_SettingsBlade_Name_infoDescription</comment>
  </data>
  <data name="ContainerRegistry_Dropdown_SettingsBlade_Name_infoItem2" xml:space="preserve">
    <value>長度介於 5 到 50 個字元之間</value>
    <comment>Item2 of ContainerRegistry_Dropdown_SettingsBlade_Name_infoDescription</comment>
  </data>
  <data name="ContainerRegistry_Dropdown_SettingsBlade_Name_infoItem3" xml:space="preserve">
    <value>只包含英數字元</value>
    <comment>Item3 of ContainerRegistry_Dropdown_SettingsBlade_Name_infoDescription</comment>
  </data>
  <data name="ContainerRegistry_Dropdown_SettingsBlade_Sku_info" xml:space="preserve">
    <value>所有 SKU 都提供相同的程式設計功能。選擇較高的 SKU 可提供較高的效能和規模。</value>
  </data>
  <data name="ContainerRegistry_Dropdown_SettingsBlade_Sku_infoLearnMore" xml:space="preserve">
    <value>深入了解</value>
  </data>
  <data name="Keyvault_Dropdown_Hub_info" xml:space="preserve">
    <value>金鑰保存庫會用於儲存 AI 中樞所需的祕密及其他敏感性資訊。您可以建立新的 Azure Key Vault 資源，或在訂用帳戶中選取現有的資源。</value>
  </data>
  <data name="Keyvault_Dropdown_info" xml:space="preserve">
    <value>金鑰保存庫用於儲存工作區所需的祕密及其他敏感資訊。您可以建立新的 Azure Key Vault 資源，或在訂用帳戶中選取現有的資源。</value>
  </data>
  <data name="Keyvault_Dropdown_SettingsBlade_Name_infoDescription" xml:space="preserve">
    <value>名稱必須符合下列要求:</value>
  </data>
  <data name="Keyvault_Dropdown_SettingsBlade_Name_infoItem1" xml:space="preserve">
    <value>在 Azure 中所有現有金鑰保存庫中為唯一的</value>
    <comment>Item1 of Keyvault_Dropdown_SettingsBlade_Name_infoDescription</comment>
  </data>
  <data name="Keyvault_Dropdown_SettingsBlade_Name_infoItem2" xml:space="preserve">
    <value>長度介於 3 到 24 個字元之間</value>
    <comment>Item2 of Keyvault_Dropdown_SettingsBlade_Name_infoDescription</comment>
  </data>
  <data name="Keyvault_Dropdown_SettingsBlade_Name_infoItem3" xml:space="preserve">
    <value>只能包含英數字元與連字號</value>
    <comment>Item3 of Keyvault_Dropdown_SettingsBlade_Name_infoDescription</comment>
  </data>
  <data name="Keyvault_Dropdown_SettingsBlade_Name_infoItem4" xml:space="preserve">
    <value>不得以數字開頭</value>
    <comment>Item4 of Keyvault_Dropdown_SettingsBlade_Name_infoDescription</comment>
  </data>
  <data name="Keyvault_RadioButton_label" xml:space="preserve">
    <value>Credential store</value>
  </data>
  <data name="Keyvault_RadioButton_managedKeyVault" xml:space="preserve">
    <value>Microsoft-managed (preview)</value>
  </data>
  <data name="Keyvault_RadioButton_ManagedKeyVault_infoIcon" xml:space="preserve">
    <value>Preview: secrets are stored in Microsoft-managed credential store. Secret data lifecycle follows your hub, projects, connections and compute.</value>
  </data>
  <data name="Keyvault_RadioButton_byoKeyVault" xml:space="preserve">
    <value>Azure key vault</value>
  </data>
  <data name="StorageAccount_Dropdown_Hub_info" xml:space="preserve">
    <value>儲存體帳戶作為 AI 中樞的預設資料存放區使用。您可以建立新的 Azure 儲存體資源，或在您的訂用帳戶中選取現有的資源。</value>
  </data>
  <data name="StorageAccount_Dropdown_info" xml:space="preserve">
    <value>儲存體帳戶作為工作區的預設資料存放區使用。您可以建立新的 Azure 儲存體資源，或在訂用帳戶中選取現有的資源。</value>
  </data>
  <data name="StorageAccount_Dropdown_SettingsBlade_Name_infoDescription" xml:space="preserve">
    <value>名稱必須符合下列要求:</value>
  </data>
  <data name="StorageAccount_Dropdown_SettingsBlade_Name_infoItem1" xml:space="preserve">
    <value>在 Azure 中所有現有儲存體帳戶名稱中為唯一的</value>
    <comment>Item1 of StorageAccount_Dropdown_SettingsBlade_Name_infoDescription</comment>
  </data>
  <data name="StorageAccount_Dropdown_SettingsBlade_Name_infoItem2" xml:space="preserve">
    <value>長度介於 3 到 24 個字元之間</value>
    <comment>Item2 of StorageAccount_Dropdown_SettingsBlade_Name_infoDescription</comment>
  </data>
  <data name="StorageAccount_Dropdown_SettingsBlade_Name_infoItem3" xml:space="preserve">
    <value>只能包含小寫字母與數字</value>
    <comment>Item3 of StorageAccount_Dropdown_SettingsBlade_Name_infoDescription</comment>
  </data>
  <data name="StorageAccount_Dropdown_SettingsBlade_Performance_info" xml:space="preserve">
    <value>磁碟機支援標準儲存體帳戶，而且每 GB 的費用最低。固態硬碟支援進階儲存體帳戶，能持續提供一致而且低延遲的效能。</value>
  </data>
  <data name="StorageAccount_Dropdown_SettingsBlade_Replication_info" xml:space="preserve">
    <value>儲存體帳戶中的資料一律會加以複寫，以確保其持久性與高可用性。請選擇最符合您需求的複寫策略。</value>
  </data>
  <data name="StorageAccount_Dropdown_SettingsBlade_Replication_infoLearnMore" xml:space="preserve">
    <value>深入了解</value>
  </data>
  <data name="AIServices_SummaryTab_label" xml:space="preserve">
    <value>AI 服務</value>
  </data>
  <data name="HubConfigDefaultResourceGroup_SummaryTab_label" xml:space="preserve">
    <value>預設專案資源群組</value>
  </data>
  <data name="AppInsights_SummaryTab_label" xml:space="preserve">
    <value>應用程式見解</value>
  </data>
  <data name="ContainerRegistry_SummaryTab_label" xml:space="preserve">
    <value>容器登錄</value>
  </data>
  <data name="Keyvault_SummaryTab_label" xml:space="preserve">
    <value>金鑰保存庫</value>
  </data>
  <data name="StorageAccount_SummaryTab_label" xml:space="preserve">
    <value>儲存體帳戶</value>
  </data>
  <data name="SummaryTab_Resource_existingFormat" xml:space="preserve">
    <value>{0} ({1})</value>
    <comment>0: resource name, 1: resource group name</comment>
  </data>
  <data name="SummaryTab_Resource_newFormat" xml:space="preserve">
    <value>(新增) {0}</value>
    <comment>0: resource name</comment>
  </data>
  <data name="SummaryTab_Resource_none" xml:space="preserve">
    <value>無</value>
  </data>
  <data name="SummaryTab_Resource_microsoftManaged" xml:space="preserve">
    <value>Microsoft-managed</value>
  </data>
  <data name="OnlineEndpoints_Create_introText" xml:space="preserve">
    <value>Azure Machine Learning 推斷應用程式可讓您快速地在任何平台上建置、部署企業層級的機器學習模型，也可讓您調整這些模型的規模。您可以使用任何開放原始碼的機器學習架構，例如 TensorFlow、PyTorch、SciKit-Learn、ONNX 等等。使用我們的無程式碼部署來提升您的產能，或是使用您自有的 Docker 容器及 (或) 模型計分程式碼，自訂您的推斷應用程式。</value>
  </data>
  <data name="OnlineEndpoints_Create_modelLabel" xml:space="preserve">
    <value>模型</value>
  </data>
  <data name="OnlineEndpoints_Create_modelPlaceholder" xml:space="preserve">
    <value>選取模型</value>
  </data>
  <data name="OnlineEndpoints_Create_modelVersionLabel" xml:space="preserve">
    <value>模型版本</value>
  </data>
  <data name="OnlineEndpoints_Create_modelVersionPlaceholder" xml:space="preserve">
    <value>選取模型版本</value>
  </data>
  <data name="OnlineEndpoints_Create_nameLabel" xml:space="preserve">
    <value>名稱</value>
  </data>
  <data name="OnlineEndpoints_Create_subTitle" xml:space="preserve">
    <value>建立 ML 應用程式</value>
  </data>
  <data name="OnlineEndpoints_Create_title" xml:space="preserve">
    <value>機器學習線上端點</value>
  </data>
  <data name="OnlineEndpoints_Create_workspaceLabel" xml:space="preserve">
    <value>工作區</value>
  </data>
  <data name="OnlineEndpoints_Create_workspacePlaceholder" xml:space="preserve">
    <value>選取工作區</value>
  </data>
  <data name="MLAppDeployments_GridColumns_name" xml:space="preserve">
    <value>部署名稱</value>
  </data>
  <data name="MLAppDeployments_GridColumns_status" xml:space="preserve">
    <value>佈建狀態</value>
  </data>
  <data name="MLAppDeployments_GridColumns_traffic" xml:space="preserve">
    <value>流量 %</value>
  </data>
  <data name="MLAppDeployments_Grid_StatusFilter_all" xml:space="preserve">
    <value>所有佈建狀態</value>
  </data>
  <data name="MLAppDeployments_Grid_StatusFilter_itemFormat" xml:space="preserve">
    <value>{0} ({1})</value>
    <comment>0: status name, 1: count</comment>
  </data>
  <data name="MLAppDeployments_Grid_StatusFilter_some" xml:space="preserve">
    <value>已選取 {0} 個連線狀態</value>
    <comment>0: a number</comment>
  </data>
  <data name="StorageAccount_Dropdown_SettingsBlade_hnsNotSupportedFormat" xml:space="preserve">
    <value>{0} - 不支援階層命名空間</value>
    <comment>{0}: Storage account name</comment>
  </data>
  <data name="StorageAccount_Dropdown_SettingsBlade_premiumNotSupportedFormat" xml:space="preserve">
    <value>{0} - 不支援進階儲存體帳戶</value>
    <comment>{0}: Storage account name</comment>
  </data>
  <data name="OnlineEndpoints_Create_Dependencies_condaDependenciesFileLabel" xml:space="preserve">
    <value>Conda 相依性檔案</value>
  </data>
  <data name="OnlineEndpoints_Create_Dependencies_description" xml:space="preserve">
    <value>將您的模型部署自訂為推斷應用程式。</value>
  </data>
  <data name="OnlineEndpoints_Create_Dependencies_entryScriptFileLabel" xml:space="preserve">
    <value>輸入腳本檔案</value>
  </data>
  <data name="OnlineEndpoints_Create_Dependencies_selectCondaDependenciesFile" xml:space="preserve">
    <value>選取 Conda 相依性檔案</value>
  </data>
  <data name="OnlineEndpoints_Create_Dependencies_selectEntryScriptFileMessage" xml:space="preserve">
    <value>選取輸入腳本檔案</value>
  </data>
  <data name="OnlineEndpoints_Create_Dependencies_tabName" xml:space="preserve">
    <value>相依性</value>
  </data>
  <data name="CrossRegionComputeQuotas_loadingText" xml:space="preserve">
    <value>正在載入...</value>
  </data>
  <data name="CrossRegionComputeQuotas_UsageGrid_usageColumnText" xml:space="preserve">
    <value>使用量</value>
  </data>
  <data name="CrossRegionComputeQuotas_UsageGrid_vmFamiliesColumnText" xml:space="preserve">
    <value>VM 系列</value>
  </data>
  <data name="CrossRegionComputeQuotas_UsageGrid_workspaceDisplayText" xml:space="preserve">
    <value>{0} ({1})</value>
    <comment>0: workspace name, 1: resource group name</comment>
  </data>
  <data name="CrossRegionComputeQuotas_UsageGrid_workspacesColumnText" xml:space="preserve">
    <value>工作區</value>
  </data>
  <data name="CrossRegionComputeQuotas_configureQuotaButtonText" xml:space="preserve">
    <value>設定配額</value>
  </data>
  <data name="CrossRegionComputeQuotas_dedicatedCoresSectionLabel" xml:space="preserve">
    <value>專用核心使用量</value>
  </data>
  <data name="CrossRegionComputeQuotas_lowPriorityCoresSectionLabel" xml:space="preserve">
    <value>低優先順序核心使用量</value>
  </data>
  <data name="CrossRegionComputeQuotas_QuotaConfigurationBlade_subTitle" xml:space="preserve">
    <value>在此設定您的訂用帳戶配額</value>
  </data>
  <data name="CrossRegionComputeQuotas_QuotaConfigurationBlade_title" xml:space="preserve">
    <value>設定配額</value>
  </data>
  <data name="CrossRegionComputeQuotas_requestQuotaButtonText" xml:space="preserve">
    <value>要求配額增加</value>
  </data>
  <data name="CrossRegionComputeQuotas_subscriptionViewText" xml:space="preserve">
    <value>訂用帳戶檢視</value>
  </data>
  <data name="CrossRegionComputeQuotas_toggleText" xml:space="preserve">
    <value>顯示所有位置的工作區 (跨位置計算)</value>
  </data>
  <data name="CrossRegionComputeQuotas_totalClustersLabel" xml:space="preserve">
    <value>叢集配額:</value>
  </data>
  <data name="CrossRegionComputeQuotas_totalClustersTextFormat" xml:space="preserve">
    <value>已使用 {0} 個叢集和 CI | 剩餘 {1} 個</value>
    <comment>0: number of used, 1: number of remaining</comment>
  </data>
  <data name="CrossRegionComputeQuotas_totalCoresTextFormat" xml:space="preserve">
    <value>已使用 {0} 個核心 | 剩餘 {1} 個核心</value>
    <comment>0: number of used, 1: number of remaining</comment>
  </data>
  <data name="CrossRegionComputeQuotas_totalDedicatedCoresLabel" xml:space="preserve">
    <value>專用配額:</value>
  </data>
  <data name="CrossRegionComputeQuotas_totalLowPriorityCoresLabel" xml:space="preserve">
    <value>低優先順序配額:</value>
  </data>
  <data name="CrossRegionComputeQuotas_UsageGrid_coresUsageTextFormat" xml:space="preserve">
    <value>已使用 {0} 個核心 (共 {1} 個)</value>
    <comment>0: number of used, 1: number of limit</comment>
  </data>
  <data name="CrossRegionComputeQuotas_UsageGrid_totalCoresTextFormat" xml:space="preserve">
    <value>{0} / {1} ({2})</value>
    <comment>0: number of used, 1: number of limit, 2: usage in percent</comment>
  </data>
  <data name="CrossRegionComputeQuotas_workspaceViewText" xml:space="preserve">
    <value>工作區檢視</value>
  </data>
  <data name="CrossRegionComputeQuotas_UsageGrid_crossLocationWarningCoresFormat" xml:space="preserve">
    <value>{0} 個核心</value>
    <comment>0: number of cores</comment>
  </data>
  <data name="CrossRegionComputeQuotas_UsageGrid_crossLocationWarningLine1" xml:space="preserve">
    <value>您正在使用其他位置中的 {0}。</value>
    <comment>0: {0} cores (see CrossRegionComputeQuotas_UsageGrid_crossLocationWarningCoresFormat</comment>
  </data>
  <data name="CrossRegionComputeQuotas_UsageGrid_familyNameFormat" xml:space="preserve">
    <value>標準 {0} 系列 vCPU</value>
    <comment>0: family name</comment>
  </data>
  <data name="CrossRegionComputeQuotas_UsageGrid_workspaceCrossLocationDisplayText" xml:space="preserve">
    <value>{0} ({1}，{2})</value>
    <comment>0: workspace name, 1: resource group name, 2: location</comment>
  </data>
  <data name="CrossRegionComputeQuotas_QuotaConfigurationBlade_filterText" xml:space="preserve">
    <value>輸入至少 3 個字元以搜尋工作區...</value>
  </data>
  <data name="CrossRegionComputeQuotas_QuotaConfigurationBlade_noItemsText" xml:space="preserve">
    <value>找不到任何可顯示的工作區。</value>
  </data>
  <data name="ManagedIdentities_menuText" xml:space="preserve">
    <value>身分識別</value>
  </data>
  <data name="CrossRegionComputeQuotas_QuotaConfigurationBlade_quotaConfigNotAvailable" xml:space="preserve">
    <value>因為發生下列錯誤，所以無法設定配額: {0}</value>
    <comment>0: error details</comment>
  </data>
  <data name="CrossRegionComputeQuotas_QuotaConfigurationBlade_workspacesNotAvailable" xml:space="preserve">
    <value>由於發生下列錯誤，您無法設定之前已設定之工作區的配額，因為無法到達此訂閱的工作區清單: {0}</value>
    <comment>0: error details</comment>
  </data>
  <data name="CrossRegionComputeQuotas_QuotaUsageBlade_crossLocationUsagesNotAvailable" xml:space="preserve">
    <value>因為發生下列錯誤，所以無法顯示跨位置工作區資訊 (擁有計算的工作區是否與所選位置處於不同位置)，但您仍可看到所有使用量: {0}</value>
    <comment>0: error details</comment>
  </data>
  <data name="CrossRegionComputeQuotas_QuotaUsageBlade_usagesNotAvailable" xml:space="preserve">
    <value>因為發生下列錯誤，所以無法顯示計算使用量: {0}</value>
    <comment>0: error details</comment>
  </data>
  <data name="CrossRegionComputeQuotas_UsageGrid_crossLocationEnableText" xml:space="preserve">
    <value>啟用跨位置計算切換</value>
  </data>
  <data name="CrossRegionComputeQuotas_UsageGrid_crossLocationWarningLine2" xml:space="preserve">
    <value>{1}，加以檢視。</value>
    <comment>1: enable text (CrossRegionComputeQuotas_UsageGrid_crossLocationEnableText)</comment>
  </data>
  <data name="AdvancedTab_Section_ManagedIdentity_description" xml:space="preserve">
    <value>Azure 資源可在程式碼中沒有儲存認證的情況下，利用受控識別對雲端服務進行驗證。啟用之後，即可透過 Azure 角色型存取控制，授與所有必要的權限。可以為工作區指定系統指派的身分識別或是使用者指派的身分識別。</value>
  </data>
  <data name="AdvancedTab_Section_ManagedIdentity_header" xml:space="preserve">
    <value>受控識別</value>
  </data>
  <data name="AdvancedTab_Section_ManagedIdentity_radioGroupTitle" xml:space="preserve">
    <value>身分識別類型</value>
  </data>
  <data name="AdvancedTab_Section_ManagedIdentity_systemAssignedText" xml:space="preserve">
    <value>系統指派的身分識別</value>
  </data>
  <data name="AdvancedTab_Section_StorageAccountAccess_sharedKeyAccessCheckboxLabel" xml:space="preserve">
    <value>停用共用金鑰存取</value>
  </data>
  <data name="AdvancedTab_Section_StorageAccountAccess_sharedKeyAccessDisableDocumentationLinkText" xml:space="preserve">
    <value>深入了解</value>
  </data>
  <data name="AdvancedTab_Section_StorageAccountAccess_previewLinkText" xml:space="preserve">
    <value>(預覽)</value>
  </data>
  <data name="AdvancedTab_Section_StorageAccountAccess_sharedKeyAccessDisableWarning" xml:space="preserve">
    <value>停用共用金鑰存取選項 {0} 後，即會停用儲存體帳戶的金鑰存取，因此任何對儲存體帳戶發出且經共用金鑰授權的要求，包含共用存取簽章 (SAS)，皆會遭到拒絕。針對特定使用案例，此選項可能需要為個別使用者進行額外角色設定。{1}如何為您工作區的儲存體帳戶停用共用金鑰存取</value>
  </data>
  <data name="AdvancedTab_Section_ManagedIdentity_userAssignedPickerLabel" xml:space="preserve">
    <value>使用者指派的身分識別</value>
  </data>
  <data name="AdvancedTab_Section_ManagedIdentity_userAssignedPickerRequiredMessage" xml:space="preserve">
    <value>需要使用者識別</value>
  </data>
  <data name="AdvancedTab_Section_ManagedIdentity_userAssignedPickerSubscriptionMessage" xml:space="preserve">
    <value>工作區與選取的使用者識別必須位於相同的訂用帳戶內。</value>
  </data>
  <data name="AdvancedTab_Section_ManagedIdentity_userAssignedPickerText" xml:space="preserve">
    <value>按一下即可選取身分識別</value>
  </data>
  <data name="AdvancedTab_Section_ManagedIdentity_userAssignedText" xml:space="preserve">
    <value>使用者指派的身分識別</value>
  </data>
  <data name="AdvancedTab_Section_ManagedIdentity_userIdentityNameLabel" xml:space="preserve">
    <value>使用者識別名稱</value>
  </data>
  <data name="AdvancedTab_Section_ManagedIdentity_userIdentityResourceGroupLabel" xml:space="preserve">
    <value>使用者識別資源群組</value>
  </data>
  <data name="SummaryTab_Advanced_ManagedIdentity_systemAssigned" xml:space="preserve">
    <value>系統指派</value>
  </data>
  <data name="SummaryTab_Advanced_ManagedIdentity_typeText" xml:space="preserve">
    <value>身分識別類型</value>
  </data>
  <data name="SummaryTab_Advanced_ManagedIdentity_userAssigned" xml:space="preserve">
    <value>使用者指派</value>
  </data>
  <data name="SummaryTab_Advanced_ManagedIdentity_userIdentityName" xml:space="preserve">
    <value>使用者識別名稱</value>
  </data>
  <data name="SummaryTab_Advanced_ManagedIdentity_userIdentityResourceGroup" xml:space="preserve">
    <value>使用者識別資源群組</value>
  </data>
  <data name="RequestQuota_dedicatedSectionLabel" xml:space="preserve">
    <value>專用</value>
  </data>
  <data name="RequestQuota_lowPrioritySectionLabel" xml:space="preserve">
    <value>低優先順序</value>
  </data>
  <data name="RequestQuota_vmTypeDedicated" xml:space="preserve">
    <value>專用</value>
  </data>
  <data name="RequestQuota_vmTypeLowPriority" xml:space="preserve">
    <value>低優先順序</value>
  </data>
  <data name="RequestQuota_vmTypesLabel" xml:space="preserve">
    <value>VM 類型</value>
  </data>
  <data name="CrossRegionComputeQuotas_lowPriorityUsageInfoText" xml:space="preserve">
    <value>請注意，每個訂閱的低優先順序核心數在 VM 系列中都是單一值。</value>
  </data>
  <data name="CrossRegionComputeQuotas_QuotaUsageBlade_quotaInfoLearnMore" xml:space="preserve">
    <value>深入了解</value>
  </data>
  <data name="CrossRegionComputeQuotas_QuotaUsageBlade_quotaInfoText" xml:space="preserve">
    <value>Azure Machine Learning 配額是預先設定的限制，指定了在任何指定時間可使用的核心數目上限。</value>
  </data>
  <data name="CrossRegionComputeQuotas_requestBladeFamilyNameFormat" xml:space="preserve">
    <value>{0} 系列</value>
    <comment>0: family name</comment>
  </data>
  <data name="AppInsights_Dropdown_createNewAriaLabel" xml:space="preserve">
    <value>建立新的 Application Insights</value>
  </data>
  <data name="ContainerRegistry_Dropdown_createNewAriaLabel" xml:space="preserve">
    <value>建立新的容器登錄</value>
  </data>
  <data name="CrossRegionComputeQuotas_UsageGrid_coreUtilizationColumnText" xml:space="preserve">
    <value>核心使用率</value>
  </data>
  <data name="CrossRegionComputeQuotas_UsageGrid_usagePercentageColumnText" xml:space="preserve">
    <value>使用量百分比</value>
  </data>
  <data name="Keyvault_Dropdown_createNewAriaLabel" xml:space="preserve">
    <value>建立新的金鑰保存庫</value>
  </data>
  <data name="StorageAccount_Dropdown_createNewAriaLabel" xml:space="preserve">
    <value>建立新儲存體帳戶</value>
  </data>
  <data name="AdvancedTab_Section_ManagedIdentity_disabledWarning" xml:space="preserve">
    <value>只有在使用現有的儲存體帳戶、金鑰保存庫和容器登錄時，才支援受管理使用者指派的身分識別選項。</value>
  </data>
  <data name="AdvancedTab_Section_ManagedIdentity_permissionWarning" xml:space="preserve">
    <value>如果您有自己的相關資源，而不是讓 Azure Machine Learning 服務建立，您必須在這些資源授與受控識別角色。請使用 {0} 進行指派。</value>
    <comment>{0}: role assignment ARM template</comment>
  </data>
  <data name="AdvancedTab_Section_ManagedIdentity_permissionWarningLinkText" xml:space="preserve">
    <value>角色指派 ARM 範本</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_title" xml:space="preserve">
    <value>工作區輸出規則</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_ruleNameLabel" xml:space="preserve">
    <value>規則名稱</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_ruleNameBalloonContent" xml:space="preserve">
    <value>工作區中唯一的輸出規則名稱。</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_ruleNameAlreadyExists" xml:space="preserve">
    <value>規則名稱已存在</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_appGateway_subResource_errorMessage" xml:space="preserve">
    <value>在資源中找不到私人連結設定。在建立 PE 輸出規則之前進行設定。</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_destinationTypeLabel" xml:space="preserve">
    <value>目的地類型</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_destinationTypeBalloonContent" xml:space="preserve">
    <value>輸出目的地的類型，FQDN，私人端點，服務標籤。</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_subscriptionLabel" xml:space="preserve">
    <value>訂用帳戶</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_subscriptionBalloonContent" xml:space="preserve">
    <value>包含私人端點之目標資源的訂用帳戶。</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_resourceGroupLabel" xml:space="preserve">
    <value>資源群組</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_resourceGroupBalloonContent" xml:space="preserve">
    <value>包含私人端點之目標資源的資源群組。</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_resourceTypeLabel" xml:space="preserve">
    <value>資源類型</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_resourceTypeBalloonContent" xml:space="preserve">
    <value>支援 Private Link 的 Azure 資源類型。</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_resourceNameLabel" xml:space="preserve">
    <value>資源名稱</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_resourceNameBalloonContent" xml:space="preserve">
    <value>私人端點的目標資源名稱。</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_fqdnsLabel" xml:space="preserve">
    <value>FQDN</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_statusLabel" xml:space="preserve">
    <value>狀態</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_statusBalloonContent" xml:space="preserve">
    <value>使用中時，狀態為唯讀，表示已佈建受控網路且已就緒。非使用中時，表示尚未佈建。</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_subResourceLabel" xml:space="preserve">
    <value>子資源</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_subResourceBalloonContent" xml:space="preserve">
    <value>用於連線私人端點的子資源。</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_sparkEnabledLabel" xml:space="preserve">
    <value>Spark 已啟用</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_sparkEnabledBalloonContent" xml:space="preserve">
    <value>檢查以啟用額外的私人端點，供在 Spark 上執行的作業使用。</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_sparkStatusLabel" xml:space="preserve">
    <value>Spark 狀態</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_sparkStatusBalloonContent" xml:space="preserve">
    <value>指出是否已為 Spark 作業佈建受控網路。</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_serviceTagLabel" xml:space="preserve">
    <value>服務標籤</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_serviceTagBalloonContent" xml:space="preserve">
    <value>代表 IP 位址的類別的已預先定義識別碼。</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_protocolLabel" xml:space="preserve">
    <value>通訊協定</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_protocolBalloonContent" xml:space="preserve">
    <value>要允許的網路通訊協定，TCP、UDP、ICMP 或任何</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_portRangeLabel" xml:space="preserve">
    <value>連接埠範圍</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_portRangeBalloonContent" xml:space="preserve">
    <value>請提供單一連接埠 (例如 80)、連接埠範圍 (例如 1024-655535) 或以逗點分隔的單一連接埠及/或連接埠範圍的清單 (例如 80,1024-655535)。這會指定此規則將允許或拒絕哪些連接埠上的流量。提供星號 (*) 則代表允許任何連接埠上的流量。</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_Note" xml:space="preserve">
    <value>備註</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_FqdnCostInfo_managedNetworkIsolationLinkText" xml:space="preserve">
    <value>受管理的網路隔離。</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_FqdnCostInfo_text" xml:space="preserve">
    <value>FQDN 輸出規則是使用 Azure 防火牆實作。如果您使用輸出 FQDN 規則，Azure 防火牆費用會包含在帳單中。如需深入了解輸出規則，請參閱 </value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_FqdnCostInfo_pricingLinkText" xml:space="preserve">
    <value>價格。</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_applicationGatewayFqdnInfoBallon" xml:space="preserve">
    <value>FQDNs resolve to the private IP of the Application Gateway private endpoint inside the workspace's managed network. FQDNs are editable as needed. Please save after editing the fields.</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_FqdnValidation_invalidFormatMessage" xml:space="preserve">
    <value>Specify a valid FQDN with at least three labels for Application Gateway access.</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_FqdnValidation_emptyLabelMessage" xml:space="preserve">
    <value>Domain label should not be empty.</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_FqdnValidation_applicationGatewayFqdnRequired" xml:space="preserve">
    <value>FQDN is required.</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_FqdnCostInfo_moreInformationText" xml:space="preserve">
    <value>如需有關 Azure 防火牆的詳細資訊，請參閱 </value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_fqdnDestinationLabel" xml:space="preserve">
    <value>FQDN 目的地</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_fqdnDestinationBalloonContent" xml:space="preserve">
    <value>允許輸出流量的完整網域名稱。</value>
  </data>
  <data name="OutboundAccessRule_SettingsBlade_amlRegistryPEDependencyRulesWarning" xml:space="preserve">
    <value>將針對 AzureML 登錄下的所有相依性資源建立相依性輸出規則。在相依性輸出規則下檢視這些規則。</value>
  </data>
  <data name="StorageAccount_Dropdown_SettingsBlade_hnsCheckLabel" xml:space="preserve">
    <value>啟用階層命名空間 (預覽)</value>
  </data>
  <data name="StorageAccount_Dropdown_SettingsBlade_hnsDescriptionText" xml:space="preserve">
    <value>Data Lake Storage Gen2 階層式命名空間可加速巨量資料分析工作負載、啟用更快且更可靠的檔案作業，以及啟用檔案層級存取控制清單 (ACL)。 {0}</value>
    <comment>0: learn more</comment>
  </data>
  <data name="StorageAccount_Dropdown_SettingsBlade_hnsDescriptionTitle" xml:space="preserve">
    <value>Data Lake Storage Gen2 (預覽)</value>
  </data>
  <data name="StorageAccount_Dropdown_SettingsBlade_learnMoreText" xml:space="preserve">
    <value>深入了解</value>
  </data>
  <data name="AdvancedTab_Section_StorageAccountAccess_credentialBasedText" xml:space="preserve">
    <value>認證型存取</value>
  </data>
  <data name="AdvancedTab_Section_StorageAccountAccess_description" xml:space="preserve">
    <value>Azure machine learning allows you to choose between credential-based or identity-based access when connecting to the default storage account.</value>
  </data>
  <data name="AdvancedTab_Section_StorageAccountAccess_header" xml:space="preserve">
    <value>儲存體帳戶存取</value>
  </data>
  <data name="AdvancedTab_Section_StorageAccountAccess_identityBasedText" xml:space="preserve">
    <value>以身分識別為基礎的存取</value>
  </data>
  <data name="AdvancedTab_Section_StorageAccountAccess_identityBasedWarningText" xml:space="preserve">
    <value>{0} When using identity-based authentication, "{1}" and "{2}" roles must be granted to {3} that need access on the storage account. Contact your admin for help or</value>
    <comment>0: Important, 1: Storage Blob Data Contributor, 2: Storage File Privileged Contributor, 3: individual users</comment>
  </data>
  <data name="AdvancedTab_Section_StorageAccountAccess_identityBasedWarningLearnMore" xml:space="preserve">
    <value>learn more about RBAC settings</value>
  </data>
  <data name="AdvancedTab_Section_StorageAccountAccess_Important" xml:space="preserve">
    <value>Important</value>
  </data>
  <data name="AdvancedTab_Section_StorageAccountAccess_IndividualUsers" xml:space="preserve">
    <value>individual users</value>
  </data>
  <data name="AdvancedTab_Section_StorageAccountAccess_learnMoreText" xml:space="preserve">
    <value>深入了解</value>
  </data>
  <data name="AdvancedTab_Section_StorageAccountAccess_radioGroupTitle" xml:space="preserve">
    <value>儲存體帳戶存取類型</value>
  </data>
  <data name="Security_menuText" xml:space="preserve">
    <value>安全性</value>
  </data>
  <data name="Keyword_alert" xml:space="preserve">
    <value>警示</value>
  </data>
  <data name="Keyword_audit" xml:space="preserve">
    <value>稽核</value>
  </data>
  <data name="Keyword_instance" xml:space="preserve">
    <value>執行個體</value>
  </data>
  <data name="Keyword_log" xml:space="preserve">
    <value>記錄</value>
  </data>
  <data name="Keyword_rules" xml:space="preserve">
    <value>規則</value>
  </data>
  <data name="Keyword_scale" xml:space="preserve">
    <value>縮放</value>
  </data>
  <data name="Keyword_scaling" xml:space="preserve">
    <value>正在擴充</value>
  </data>
  <data name="Scaling" xml:space="preserve">
    <value>正在調整規模</value>
  </data>
  <data name="SummaryTab_createResourceGroupErrorText" xml:space="preserve">
    <value>建立預設記錄分析工作區時，建立資源群組時發生錯誤: {0}</value>
    <comment>0: error details</comment>
  </data>
  <data name="SummaryTab_gettingLogWorkspacesErrorText" xml:space="preserve">
    <value>取得用於建立預設 Log Analytics 工作區的記錄工作區時發生錯誤: {0}</value>
    <comment>0: error details</comment>
  </data>
  <data name="CrossRegionComputeQuotas_quotaUpdateFailed" xml:space="preserve">
    <value>配額更新失敗</value>
  </data>
  <data name="CrossRegionComputeQuotas_quotaUpdateSucceeded" xml:space="preserve">
    <value>已成功更新配額</value>
  </data>
  <data name="LabelMlFlowUri" xml:space="preserve">
    <value>MLflow 追蹤 URI</value>
  </data>
  <data name="Networking_PrivateEndpointConnections_TabText" xml:space="preserve">
    <value>私人端點連線</value>
  </data>
  <data name="Networking_WorkspaceManagedOutboundAccess_tabText" xml:space="preserve">
    <value>工作區受控輸出存取</value>
  </data>
  <data name="Networking_PublicAccess_AllNetworksInfoText" xml:space="preserve">
    <value>所有網路 (包括網際網路) 皆可存取此資源。</value>
  </data>
  <data name="Networking_PublicAccess_Description" xml:space="preserve">
    <value>公用網路存取可讓您使用公用 IP 位址透過網際網路存取此資源。使用下列網路規則授與存取權的應用程式或資源仍需要適當的授權，才能存取此資源。{0}</value>
    <comment>0: learn more link</comment>
  </data>
  <data name="Networking_PublicAccess_DisabledInfoText" xml:space="preserve">
    <value>沒有可以存取此資源的公用網路。</value>
  </data>
  <data name="Networking_PublicAccess_EnabledFromSelectedIpInfoText" xml:space="preserve">
    <value>允許從您在下方指定的公用 IP 存取。</value>
  </data>
  <data name="Networking_PublicAccess_LearnMoreText" xml:space="preserve">
    <value>深入了解</value>
  </data>
  <data name="Networking_PublicAccess_RadioAllNetworksText" xml:space="preserve">
    <value>所有網路</value>
  </data>
  <data name="Networking_PublicAccess_RadioEnabledFromAllNetworks" xml:space="preserve">
    <value>已從所有網路啟用</value>
  </data>
  <data name="Networking_PublicAccess_RadioEnabledFromSelectedIp" xml:space="preserve">
    <value>從選取的 IP 位址啟用</value>
  </data>
  <data name="Networking_PublicAccess_RadioDisabledText" xml:space="preserve">
    <value>已停用</value>
  </data>
  <data name="Networking_PublicAccess_RadioLabel" xml:space="preserve">
    <value>公用網路存取</value>
  </data>
  <data name="Networking_PublicAccess_addressRange" xml:space="preserve">
    <value>位址範圍</value>
  </data>
  <data name="Networking_PublicAccess_addressRangePlaceHolder" xml:space="preserve">
    <value>IP 位址或 CIDR</value>
  </data>
  <data name="Networking_PublicAccess_firewallHeader" xml:space="preserve">
    <value>防火牆</value>
  </data>
  <data name="Networking_PublicAccess_firewallDescription" xml:space="preserve">
    <value>新增 IP 範圍，以允許來自網際網路或您內部部署網路的存取。</value>
  </data>
  <data name="Networking_PublicAccess_firewallLearnMore" xml:space="preserve">
    <value>深入了解</value>
  </data>
  <data name="Networking_PublicAccess_addClientIpAddressLabel" xml:space="preserve">
    <value>新增您的用戶端 IP 位址 ('{0}')</value>
    <comment>0 = the client's ip address</comment>
  </data>
  <data name="Networking_PublicAccess_addClientIpAddressInfo" xml:space="preserve">
    <value>如果您並非透過慣用網路 (例如家用與工作環境) 使用 Azure 入口網站，您可能不希望新增您的用戶端 IP 位址。</value>
  </data>
  <data name="Networking_PublicAccess_FirewallValidation_invalidIPAddress" xml:space="preserve">
    <value>IP 位址無效。</value>
  </data>
  <data name="Networking_PublicAccess_FirewallValidation_duplicateIpAddresses" xml:space="preserve">
    <value>指定了兩個相同的位址範圍。位址範圍必須為唯一。</value>
  </data>
  <data name="Networking_PublicAccess_FirewallValidation_publicIpRuleValidation" xml:space="preserve">
    <value>IP 規則僅支援公用 IP 位址。</value>
  </data>
  <data name="Networking_PublicAccess_FirewallValidation_invalidCidr" xml:space="preserve">
    <value>請指定 IP 位址或 CIDR。</value>
  </data>
  <data name="Networking_PublicAccess_FirewallValidation_invalidCIDRBlockWithSuggestion" xml:space="preserve">
    <value>{0} 不是有效的 CIDR 區塊。請改用 {1}/{2}。</value>
    <comment>0: cidrValue, 1: validIpForCidrPrefix, 2: prefix</comment>
  </data>
  <data name="Networking_PublicAccess_FirewallValidation_maxPrefix" xml:space="preserve">
    <value>首碼必須小於或等於 {0}。</value>
    <comment>0: maxPrefix</comment>
  </data>
  <data name="Networking_PublicAccess_FirewallValidation_minPrefix" xml:space="preserve">
    <value>首碼必須大於或等於 {0}。</value>
    <comment>0: minPrefix</comment>
  </data>
  <data name="Networking_PublicAccess_FirewallValidation_nonNullSubnet" xml:space="preserve">
    <value>需要非 null 的位址範圍。</value>
  </data>
  <data name="Networking_PublicAccess_FirewallValidation_octet" xml:space="preserve">
    <value>值為 {1} 的八位元 {0} 無效。此值必須介於 {2} 與 {3} 之間。</value>
  </data>
  <data name="Networking_PublicAccess_FirewallValidation_malformedSubnet" xml:space="preserve">
    <value>位址範圍格式錯誤。位址為 {0}。</value>
  </data>
  <data name="Networking_PublicAccess_FirewallValidation_formattedPrefix" xml:space="preserve">
    <value>首碼必須介於 {0} 到 {1} 之間。</value>
    <comment>0 - min prefix, 1 - max prefix</comment>
  </data>
  <data name="Networking_PublicAccess_FirewallValidation_exactPrefix" xml:space="preserve">
    <value>子網路首碼必須等於 {0}。</value>
  </data>
  <data name="Networking_PublicAccess_FirewallValidation_leadingZerosIpAddress" xml:space="preserve">
    <value>IP 位址 '{1}' 中的八位元 '{0}' 包含前置字元零。</value>
    <comment>0 = the octet with a leading zero, 1 = the ip address containing the octet</comment>
  </data>
  <data name="Networking_PublicAccess_TabText" xml:space="preserve">
    <value>公用存取</value>
  </data>
  <data name="Networking_PublicAccess_Toolbar_DiscardChangesText" xml:space="preserve">
    <value>捨棄變更</value>
  </data>
  <data name="Networking_PublicAccess_Toolbar_RefreshText" xml:space="preserve">
    <value>重新整理</value>
  </data>
  <data name="Networking_PublicAccess_Toolbar_SaveText" xml:space="preserve">
    <value>儲存</value>
  </data>
  <data name="AdvancedTab_Section_DataEncryption_checkboxByoInfo" xml:space="preserve">
    <value>選擇性地使用預先建立的資源來儲存加密的工作區資料。使用您自己的資源進行加密，可讓您根據組織的 IT 和安全性需求來增強這些資源的設定，但代表您需採取其他管理動作。</value>
  </data>
  <data name="AdvancedTab_Section_DataEncryption_checkboxByoLabel" xml:space="preserve">
    <value>將儲存加密資料的現有資源 (預覽)</value>
  </data>
  <data name="AdvancedTab_Section_DataEncryption_checkboxLabel" xml:space="preserve">
    <value>使用客戶自控金鑰啟用加密</value>
  </data>
  <data name="AdvancedTab_Section_DataEncryption_cosmosInfo" xml:space="preserve">
    <value>選取預先建立的 Azure Cosmos DB 資源。若選取 [無]，Azure ML 會在您的訂用帳戶中由 Microsoft 管理的新資源群組中建立 Cosmos DB 資源。使用您自己的資源進行加密，即表示您必須承擔額外的管理責任。</value>
  </data>
  <data name="AdvancedTab_Section_DataEncryption_cosmosLabel" xml:space="preserve">
    <value>Cosmos DB</value>
  </data>
  <data name="AdvancedTab_Section_DataEncryption_searchInfo" xml:space="preserve">
    <value>選取預先建立的 Azure 搜尋資源。若選取 [無]，Azure ML 會在您的訂用帳戶中由 Microsoft 管理的新資源群組中建立搜尋資源。使用您自己的資源進行加密，即表示您必須承擔額外的管理責任。</value>
  </data>
  <data name="AdvancedTab_Section_DataEncryption_searchLabel" xml:space="preserve">
    <value>搜尋</value>
  </data>
  <data name="AdvancedTab_Section_DataEncryption_storageInfo" xml:space="preserve">
    <value>選取預先建立的 Azure 儲存體資源。若選取 [無]，Azure ML 會在您的訂用帳戶中由 Microsoft 管理的新資源群組中建立儲存體資源。使用您自己的資源進行加密，即表示您必須承擔額外的管理責任。</value>
  </data>
  <data name="AdvancedTab_Section_DataEncryption_storageLabel" xml:space="preserve">
    <value>儲存體</value>
  </data>
  <data name="AdvancedTab_Section_DataEncryption_infoText" xml:space="preserve">
    <value>Azure Machine Learning 服務會將計量和中繼資料儲存在 Azure Cosmos DB 執行個體中，其中資料均經過​待用加密。根據預設，資料會使用 Microsoft 管理的金鑰加密，而您可以選擇攜帶自己的 (客戶管理的) 金鑰。{0}</value>
  </data>
  <data name="AdvancedTab_Section_DataEncryption_ServiceSide_infoText" xml:space="preserve">
    <value>Azure Machine Learning 服務會將計量和中繼資料儲存在 Azure Cosmos DB 執行個體中，其中資料均經過​待用加密。根據預設，資料會使用 Microsoft 管理的金鑰加密，而您可以選擇攜帶自己的 (客戶管理的) 金鑰。{0}</value>
  </data>
  <data name="AdvancedTab_Section_DataEncryption_learnMoreText" xml:space="preserve">
    <value>深入了解</value>
  </data>
  <data name="EncryptionSettings_Title" xml:space="preserve">
    <value>加密</value>
  </data>
  <data name="EncryptionSettings_Header1_Template" xml:space="preserve">
    <value>預設會使用 Microsoft 受控金鑰加密您的資料。若要進一步控制您的資料，您可以選擇自備金鑰進行加密。若要深入了解，請參閱 {} 和 {}。</value>
  </data>
  <data name="EncryptionSettings_Header1_Replacement1" xml:space="preserve">
    <value>資料加密</value>
  </data>
  <data name="EncryptionSettings_Header1_Replacement2" xml:space="preserve">
    <value>客戶自控金鑰加密</value>
  </data>
  <data name="EncryptionSettings_Header2" xml:space="preserve">
    <value>部署工作區之後，您可以旋轉加密金鑰，但無法將加密類型從 Microsoft 受控金鑰變更為客戶自控金鑰。</value>
  </data>
  <data name="EncryptionSettings_Command_Save" xml:space="preserve">
    <value>儲存</value>
  </data>
  <data name="EncryptionSettings_Command_Discard" xml:space="preserve">
    <value>捨棄</value>
  </data>
  <data name="EncryptionSettings_Command_ResultSuccess" xml:space="preserve">
    <value>成功</value>
  </data>
  <data name="EncryptionSettings_Command_ResultError" xml:space="preserve">
    <value>更新金鑰時發生錯誤</value>
  </data>
  <data name="EncryptionSettings_Command_ResultLoading" xml:space="preserve">
    <value>正在更新...</value>
  </data>
  <data name="EncryptionSettings_Command_DismissAriaLabel" xml:space="preserve">
    <value>關閉</value>
  </data>
  <data name="EncryptionSettings_Command_SeeMore" xml:space="preserve">
    <value>查看更多</value>
  </data>
  <data name="EncryptionSettings_DirtyFormWarning" xml:space="preserve">
    <value>您有未儲存的變更。</value>
  </data>
  <data name="EncryptionSettings_SelectionSection_Title" xml:space="preserve">
    <value>加密選取項目</value>
  </data>
  <data name="EncryptionSettings_SelectionSection_TypeLabel" xml:space="preserve">
    <value>加密類型</value>
  </data>
  <data name="EncryptionSettings_SelectionSection_VaultAndKeyLabel" xml:space="preserve">
    <value>金鑰保存庫與金鑰</value>
  </data>
  <data name="EncryptionSettings_SelectionSection_VaultAndKeySelect" xml:space="preserve">
    <value>選取金鑰保存庫與金鑰</value>
  </data>
  <data name="EncryptionSettings_SelectedKey_Title" xml:space="preserve">
    <value>選取的金鑰保存庫與金鑰</value>
  </data>
  <data name="EncryptionSettings_SelectedKey_VaultLabel" xml:space="preserve">
    <value>KeyVault</value>
  </data>
  <data name="EncryptionSettings_SelectedKey_KeyLabel" xml:space="preserve">
    <value>金鑰</value>
  </data>
  <data name="EncryptionSettings_SelectedKey_VersionLabel" xml:space="preserve">
    <value>版本</value>
  </data>
  <data name="CreateBlade_Advanced_Review_byoCosmosText" xml:space="preserve">
    <value>客戶管理金鑰的 Cosmos DB</value>
  </data>
  <data name="CreateBlade_Advanced_Review_byoSearchText" xml:space="preserve">
    <value>客戶管理金鑰的搜尋服務</value>
  </data>
  <data name="CreateBlade_Advanced_Review_byoStorageText" xml:space="preserve">
    <value>客戶管理金鑰的儲存體帳戶</value>
  </data>
  <data name="CreateBlade_Advanced_Review_byoValueFormat" xml:space="preserve">
    <value>{0} ({1})</value>
    <comment>0: resource name, 1: resource group name</comment>
  </data>
  <data name="SearchService_Dropdown_standardSupportedFormat" xml:space="preserve">
    <value>{0} (SKU: {1}) - 最小需要標準 SKU</value>
    <comment>0: search service name, 1: sku name</comment>
  </data>
  <data name="Networking_PublicAccess_Toolbar_SavingText" xml:space="preserve">
    <value>正在儲存...</value>
  </data>
  <data name="basicsBladeDetailsIntroLearnMore" xml:space="preserve">
    <value>深入了解 Azure 資源群組</value>
  </data>
  <data name="basicsBladeInstanceIntroLearnMore" xml:space="preserve">
    <value>深入了解</value>
  </data>
  <data name="SoftDeletedWorkspace_Grid_AriaLabel" xml:space="preserve">
    <value>相關的資源方格</value>
  </data>
  <data name="SoftDeletedWorkspace_Grid_Columns_Name" xml:space="preserve">
    <value>名稱</value>
  </data>
  <data name="SoftDeletedWorkspace_Grid_Columns_DeletedDate" xml:space="preserve">
    <value>刪除日期</value>
  </data>
  <data name="SoftDeletedWorkspace_Grid_Columns_PurgeDate" xml:space="preserve">
    <value>已排程的永久刪除日期</value>
  </data>
  <data name="SoftDeletedWorkspace_Grid_Columns_ResourceGroup" xml:space="preserve">
    <value>資源群組</value>
  </data>
  <data name="SoftDeletedWorkspace_Grid_NoWorkspacesFound" xml:space="preserve">
    <value>找不到可顯示的資源</value>
  </data>
  <data name="SoftDeletedWorkspace_Blade_Title" xml:space="preserve">
    <value>最近刪除的資源</value>
  </data>
  <data name="SoftDeletedWorkspace_Blade_SubTitle" xml:space="preserve">
    <value>復原或永久刪除資源</value>
  </data>
  <data name="SoftDeletedWorkspace_Blade_ErrorMessage" xml:space="preserve">
    <value>載入虛刪除的資源時發生錯誤</value>
  </data>
  <data name="SoftDeletedWorkspace_Blade_ToolBar_Header" xml:space="preserve">
    <value>機器學習工作區</value>
  </data>
  <data name="SoftDeletedWorkspace_Blade_AIStudio_ToolBar_Header" xml:space="preserve">
    <value>AI Foundry resources</value>
  </data>
  <data name="SoftDeletedWorkspace_Blade_ToolBar_Buttons_Refresh" xml:space="preserve">
    <value>重新整理</value>
  </data>
  <data name="SoftDeletedWorkspace_Blade_Footer_Buttons_Recover" xml:space="preserve">
    <value>復原</value>
  </data>
  <data name="SoftDeletedWorkspace_Blade_Footer_Buttons_Purge" xml:space="preserve">
    <value>永久刪除</value>
  </data>
  <data name="SoftDeletedWorkspace_Blade_Footer_Buttons_Cancel" xml:space="preserve">
    <value>取消</value>
  </data>
  <data name="SoftDeletedWorkspace_Blade_LoadingText" xml:space="preserve">
    <value>正在載入...</value>
  </data>
  <data name="SoftDeletedWorkspace_OverviewBlade_DeleteMessage" xml:space="preserve">
    <value>已在此資源上啟用虛刪除功能。虛刪除之後，資源資料仍可使用。會在保留期限之後清除。您可以提早清除它，或復原資源</value>
  </data>
  <data name="SoftDeletedWorkspace_WorkspacesBlade_Commands_RecoverNotifications_Failure_message" xml:space="preserve">
    <value>無法復原 {0} 個資源 (共 {1} 個)</value>
  </data>
  <data name="SoftDeletedWorkspace_WorkspacesBlade_Commands_RecoverNotifications_Failure_title" xml:space="preserve">
    <value>無法復原資源</value>
  </data>
  <data name="SoftDeletedWorkspace_WorkspacesBlade_Commands_RecoverNotifications_InProgress_message" xml:space="preserve">
    <value>正在復原 {0} 個資源</value>
  </data>
  <data name="SoftDeletedWorkspace_WorkspacesBlade_Commands_RecoverNotifications_InProgress_title" xml:space="preserve">
    <value>正在復原資源...</value>
  </data>
  <data name="SoftDeletedWorkspace_WorkspacesBlade_Commands_RecoverNotifications_Success_message" xml:space="preserve">
    <value>已成功復原 {0} 個資源</value>
  </data>
  <data name="SoftDeletedWorkspace_WorkspacesBlade_Commands_RecoverNotifications_Success_title" xml:space="preserve">
    <value>已成功復原資源</value>
  </data>
  <data name="SoftDeletedWorkspace_WorkspacesBlade_Commands_PurgeNotifications_Failure_message" xml:space="preserve">
    <value>無法永久刪除 {0} 個資源 (共 {1} 個)</value>
  </data>
  <data name="SoftDeletedWorkspace_WorkspacesBlade_Commands_PurgeNotifications_Failure_title" xml:space="preserve">
    <value>無法刪除資源</value>
  </data>
  <data name="SoftDeletedWorkspace_WorkspacesBlade_Commands_PurgeNotifications_InProgress_message" xml:space="preserve">
    <value>正在永久刪除 {0} 資源</value>
  </data>
  <data name="SoftDeletedWorkspace_WorkspacesBlade_Commands_PurgeNotifications_InProgress_title" xml:space="preserve">
    <value>正在刪除資源 ...</value>
  </data>
  <data name="SoftDeletedWorkspace_WorkspacesBlade_Commands_PurgeNotifications_Success_message" xml:space="preserve">
    <value>已成功永久刪除 {0} 個資源</value>
  </data>
  <data name="SoftDeletedWorkspace_WorkspacesBlade_Commands_PurgeNotifications_Success_title" xml:space="preserve">
    <value>已成功刪除資源</value>
  </data>
  <data name="SoftDeletedWorkspace_WorkspacesBlade_Commands_Generic_Error_Message" xml:space="preserve">
    <value>處理要求時發生錯誤。請幾分鐘後再試一次</value>
  </data>
  <data name="SoftDeletedWorkspace_WorkspacesBlade_Commands_ValidateResources_KeyVault_Failure_message" xml:space="preserve">
    <value>復原錯誤: 找不到相關聯的 Azure Key Vault 資源 "{0}"。若沒有之前連結的 Key Vault，則無法復原虛刪除的資源。必要的 Azure Key Vault 仍有可能復原，方法請參閱 「管理刪除的保存庫」。</value>
  </data>
  <data name="SoftDeletedWorkspace_WorkspacesBlade_Commands_ValidateResources_StorageAccount_Failure_message" xml:space="preserve">
    <value>復原錯誤: 找不到相關聯的儲存體帳戶資源 "{0}"。如果儲存體帳戶先前已被虛刪除，請先復原它，再復原此資源。</value>
  </data>
  <data name="SoftDeletedWorkspace_WorkspacesBlade_Commands_ValidateResources_AppInsights_Failure_message" xml:space="preserve">
    <value>復原錯誤: 找不到相關聯的 Application Insights 資源 "{0}"。它可能已遭刪除。復原或重新建立其原名稱為 "{0}" 的 Application Insights 資源。您可以先復原相關聯的 Log Analytics 工作區，以還原您的 Application Insights 資料。」</value>
  </data>
  <data name="SoftDeletedWorkspace_WorkspacesBlade_Commands_ValidateResources_ContainerRegistry_Failure_message" xml:space="preserve">
    <value>復原錯誤: 找不到相關聯的 ACR 資源 "{0}"。若未以 ACR 作為相依性，則無法復原虛刪除。</value>
  </data>
  <data name="SoftDeletedWorkspace_WorkspacesBlade_Commands_ValidateResources_Failure_title" xml:space="preserve">
    <value>無法驗證資源 {0}</value>
  </data>
  <data name="SoftDeletedWorkspace_WorkspacesBlade_Commands_ValidateResources_InProgress_message" xml:space="preserve">
    <value>正在驗證 {1} 的 {0} 個資源</value>
  </data>
  <data name="SoftDeletedWorkspace_WorkspacesBlade_Commands_ValidateResources_InProgress_title" xml:space="preserve">
    <value>正在驗證資源 ...</value>
  </data>
  <data name="SoftDeletedWorkspace_WorkspacesBlade_Commands_ValidateResources_Success_message" xml:space="preserve">
    <value>已成功驗證 {0} 的資源</value>
  </data>
  <data name="SoftDeletedWorkspace_WorkspacesBlade_Commands_ValidateResources_Success_title" xml:space="preserve">
    <value>已成功驗證</value>
  </data>
  <data name="SoftDeletedWorkspace_DeleteBlade_Title" xml:space="preserve">
    <value>刪除資源</value>
  </data>
  <data name="SoftDeletedWorkspace_DeleteBlade_Message_text" xml:space="preserve">
    <value>當您刪除此資源時，它暫時處於可讓您復原的「虛刪除」狀態。資料的刪除會延遲到資源被您永久刪除，或直到十四 (14) 天虛刪除資料保留期到期為止。您可以覆寫虛刪除行為，並立即永久刪除您的資源。{0}</value>
  </data>
  <data name="SoftDeletedWorkspace_DeleteBlade_Message_AIStudio_text" xml:space="preserve">
    <value>要永久刪除此資源 "{0}" 嗎?</value>
  </data>
  <data name="SoftDeletedWorkspace_DeleteBlade_Message_CMKtext" xml:space="preserve">
    <value>此資源使用客戶自控金鑰 (CMK) 進行資料加密。虛刪除時，儲存資料的相依資源將不會刪除，而且在實刪除此資源之前會產生成本。{0}</value>
  </data>
  <data name="SoftDeletedWorkspace_DeleteBlade_Message_link" xml:space="preserve">
    <value>深入了解</value>
  </data>
  <data name="SoftDeletedWorkspace_DeleteBlade_FormControls_PermanentDelete_header" xml:space="preserve">
    <value>永久刪除</value>
  </data>
  <data name="SoftDeletedWorkspace_DeleteBlade_FormControls_PermanentDelete_checkbox" xml:space="preserve">
    <value>永久刪除此資源</value>
  </data>
  <data name="SoftDeletedWorkspace_DeleteBlade_FormControls_Confirm_label" xml:space="preserve">
    <value>確認刪除</value>
  </data>
  <data name="SoftDeletedWorkspace_DeleteBlade_FormControls_Confirm_placeholder" xml:space="preserve">
    <value>輸入資源名稱</value>
  </data>
  <data name="SoftDeletedWorkspace_DeleteBlade_FormControls_Confirm_errorMessage" xml:space="preserve">
    <value>名稱不相符</value>
  </data>
  <data name="SoftDeletedWorkspace_DeleteBlade_FormControls_Buttons_delete" xml:space="preserve">
    <value>刪除</value>
  </data>
  <data name="SoftDeletedWorkspace_DeleteBlade_FormControls_Buttons_cancel" xml:space="preserve">
    <value>取消</value>
  </data>
  <data name="DeleteHubBlade_Title" xml:space="preserve">
    <value>Delete resource</value>
  </data>
  <data name="DeleteHubBlade_Description_hubName" xml:space="preserve">
    <value>Hub to be deleted: </value>
  </data>
  <data name="DeleteHubBlade_Description_project" xml:space="preserve">
    <value>project(s)</value>
  </data>
  <data name="DeleteHubBlade_Description_deployment" xml:space="preserve">
    <value>deployed model(s)</value>
  </data>
  <data name="DeleteHubBlade_Description_section1" xml:space="preserve">
    <value>Your hub with {0} and {1} will be permanently deleted.</value>
  </data>
  <data name="DeleteHubBlade_Description_section2" xml:space="preserve">
    <value>Connected resources may still store hub data and are not automatically deleted. If you delete these resources you may break other existing Azure deployments.</value>
  </data>
  <data name="DeleteHubBlade_Description_section3" xml:space="preserve">
    <value>Choose connected resources you'd like to additionally delete:</value>
  </data>
  <data name="DeleteHubBlade_AssociatedResourcesListColumn_name" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="DeleteHubBlade_AssociatedResourcesListColumn_type" xml:space="preserve">
    <value>Type</value>
  </data>
  <data name="DeleteHubBlade_AssociatedResourcesListColumn_resourceGroup" xml:space="preserve">
    <value>Resource Group</value>
  </data>
  <data name="DeleteHubBlade_ProjectListColumn_project" xml:space="preserve">
    <value>Project</value>
  </data>
  <data name="DeleteHubBlade_ProjectListColumn_deployment" xml:space="preserve">
    <value>Deployment</value>
  </data>
  <data name="DeleteHubBlade_RBAC_message" xml:space="preserve">
    <value>You are missing Azure RBAC delete permission on one or more connected resources. Delete these resources later from Azure portal.</value>
  </data>
  <data name="DeleteHubBlade_ResourceType_keyVault" xml:space="preserve">
    <value>Key Vault</value>
  </data>
  <data name="DeleteHubBlade_ResourceType_storageAccount" xml:space="preserve">
    <value>Storage Account</value>
  </data>
  <data name="DeleteHubBlade_ResourceType_applicationInsights" xml:space="preserve">
    <value>Application Insights</value>
  </data>
  <data name="DeleteHubBlade_ResourceType_containerRegistry" xml:space="preserve">
    <value>Container Registry</value>
  </data>
  <data name="DeleteHubBlade_ResourceType_openAI" xml:space="preserve">
    <value>Open AI</value>
  </data>
  <data name="DeleteHubBlade_ResourceType_aiServices" xml:space="preserve">
    <value>AI Services</value>
  </data>
  <data name="DeleteHubBlade_ResourceType_searchService" xml:space="preserve">
    <value>Search Service</value>
  </data>
  <data name="DeleteHubBlade_ResourceType_contentSafety" xml:space="preserve">
    <value>Content Safety</value>
  </data>
  <data name="DeleteHubBlade_ResourceType_speechServices" xml:space="preserve">
    <value>Speech Services</value>
  </data>
  <data name="DeleteHubBlade_ResourceType_computerVision" xml:space="preserve">
    <value>Computer Vision</value>
  </data>
  <data name="DeleteHubBlade_ResourceType_textAnalytics" xml:space="preserve">
    <value>Language</value>
  </data>
  <data name="DeleteHubBlade_ResourceType_textTranslation" xml:space="preserve">
    <value>Translator</value>
  </data>
  <data name="DeleteHubBlade_ResourceType_formRecognizer" xml:space="preserve">
    <value>Document Intelligence</value>
  </data>
  <data name="DeleteHubBlade_ResourceType_cognitiveService" xml:space="preserve">
    <value>Cognitive Service</value>
  </data>
  <data name="DeleteHubBlade_ConfirmDeleteSection_label" xml:space="preserve">
    <value>Confirm delete</value>
  </data>
  <data name="DeleteHubBlade_ConfirmDeleteSection_placeholder" xml:space="preserve">
    <value>Type the resource name</value>
  </data>
  <data name="DeleteHubBlade_ConfirmDeleteSection_ValidationErrorMessage_emptyString" xml:space="preserve">
    <value>The value must not be empty</value>
  </data>
  <data name="DeleteHubBlade_ConfirmDeleteSection_ValidationErrorMessage_nameMismatch" xml:space="preserve">
    <value>Name does not match</value>
  </data>
  <data name="DeleteHubBlade_Buttons_delete" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="DeleteHubBlade_Buttons_cancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="DeleteHubNotification_InProgress_description" xml:space="preserve">
    <value>Hub '{workspaceName}' and its associated resources deletion in progress...</value>
  </data>
  <data name="DeleteHubNotification_InProgress_title" xml:space="preserve">
    <value>Deleting resource...</value>
  </data>
  <data name="DeleteHubNotification_Success_description" xml:space="preserve">
    <value>Hub '{workspaceName}' and its associated resources were deleted successfully.</value>
  </data>
  <data name="DeleteHubNotification_Success_title" xml:space="preserve">
    <value>Successfully deleted</value>
  </data>
  <data name="DeleteHubNotification_Error_description" xml:space="preserve">
    <value>An error occurred while deleting hub '{workspaceName}' and its associated resources.</value>
  </data>
  <data name="DeleteHubNotification_Error_title" xml:space="preserve">
    <value>Resource deletion error</value>
  </data>
  <data name="DeleteHubNotification_Cancel_title" xml:space="preserve">
    <value>Resource deletion canceled</value>
  </data>
  <data name="DeleteHubStatusBlade_ResourceStatusListColumn_resource" xml:space="preserve">
    <value>Resource</value>
  </data>
  <data name="DeleteHubStatusBlade_ResourceStatusListColumn_status" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="DeleteHubStatusBlade_title" xml:space="preserve">
    <value>Delete resource status</value>
  </data>
  <data name="Browse_addMachineLearningLabel" xml:space="preserve">
    <value>新增工作區</value>
  </data>
  <data name="Description_CreateAzureMachineLearningResource" xml:space="preserve">
    <value>適用於 ML 專案和小組</value>
  </data>
  <data name="Description_createAzureMachineLearningHubResource" xml:space="preserve">
    <value>集中設定 Azure AI 中樞</value>
  </data>
  <data name="Browse_addMachineLearningRegistryLabel" xml:space="preserve">
    <value>新增登錄</value>
  </data>
  <data name="Description_CreateAzureMachineLearningRegistryResource" xml:space="preserve">
    <value>適用於跨工作區共用 ML 資產</value>
  </data>
  <data name="Create_CreateButton_label" xml:space="preserve">
    <value>建立</value>
  </data>
  <data name="Create_Wizard_title" xml:space="preserve">
    <value>建立 Azure Machine Learning</value>
    <comment>PLEASE DO NOT TRANSLATE "Azure Machine Learning"</comment>
  </data>
  <data name="AssetTypeNames_MachineLearningServices_Default_plural" xml:space="preserve">
    <value>Azure Machine Learning 工作區</value>
    <comment>PLEASE DO NOT TRANSLATE "Azure Machine Learning"</comment>
  </data>
  <data name="AssetTypeNames_MachineLearningServices_Default_singular" xml:space="preserve">
    <value>Azure Machine Learning 工作區</value>
    <comment>PLEASE DO NOT TRANSLATE "Azure Machine Learning"</comment>
  </data>
  <data name="AssetTypeNames_MachineLearningServices_Default_lowerSingular" xml:space="preserve">
    <value>工作區</value>
  </data>
  <data name="AssetTypeNames_MachineLearningServices_FeatureStore_plural" xml:space="preserve">
    <value>Azure Machine Learning 功能存放區</value>
    <comment>PLEASE DO NOT TRANSLATE "Azure Machine Learning"</comment>
  </data>
  <data name="AssetTypeNames_MachineLearningServices_FeatureStore_singular" xml:space="preserve">
    <value>Azure Machine Learning 功能存放區</value>
    <comment>PLEASE DO NOT TRANSLATE "Azure Machine Learning"</comment>
  </data>
  <data name="AssetTypeNames_MachineLearningServices_FeatureStore_lowerPlural" xml:space="preserve">
    <value>功能存放區</value>
  </data>
  <data name="AssetTypeNames_MachineLearningServices_FeatureStore_lowerSingular" xml:space="preserve">
    <value>功能存放區</value>
  </data>
  <data name="AssetTypeNames_MachineLearningServices_Hub_plural" xml:space="preserve">
    <value>Azure AI 中樞</value>
  </data>
  <data name="AssetTypeNames_MachineLearningServices_Hub_singular" xml:space="preserve">
    <value>Azure AI 中樞</value>
  </data>
  <data name="AssetTypeNames_MachineLearningServices_Hub_lowerPlural" xml:space="preserve">
    <value>Azure AI 中樞</value>
  </data>
  <data name="AssetTypeNames_MachineLearningServices_Hub_lowerSingular" xml:space="preserve">
    <value>Azure AI 中樞</value>
  </data>
  <data name="AssetTypeNames_MachineLearningServices_Project_plural" xml:space="preserve">
    <value>Azure AI 專案</value>
  </data>
  <data name="AssetTypeNames_MachineLearningServices_Project_singular" xml:space="preserve">
    <value>Azure AI 專案</value>
  </data>
  <data name="AssetTypeNames_MachineLearningServices_Project_lowerPlural" xml:space="preserve">
    <value>Azure AI 專案</value>
  </data>
  <data name="AssetTypeNames_MachineLearningServices_Project_lowerSingular" xml:space="preserve">
    <value>Azure AI 專案</value>
  </data>
  <data name="AssetType_Llama2_Names_plural" xml:space="preserve">
    <value>Llama2</value>
  </data>
  <data name="AssetType_Llama2_Names_singular" xml:space="preserve">
    <value>Llama2</value>
  </data>
  <data name="AssetType_Llama2_Names_lowerPlural" xml:space="preserve">
    <value>Llama2</value>
  </data>
  <data name="AssetType_Llama2_Names_lowerSingular" xml:space="preserve">
    <value>Llama2</value>
  </data>
  <data name="AssetType_Llama2_Page_title" xml:space="preserve">
    <value>Continue to Azure AI Foundry to use this offer</value>
  </data>
  <data name="AssetType_Llama2_Page_Description_intro" xml:space="preserve">
    <value>感謝您的青睞。</value>
  </data>
  <data name="AssetType_Llama2_Page_Description_body" xml:space="preserve">
    <value>Select Continue, and you'll be redirected to the model catalog in Azure AI Foundry where you can finish subscribing and start using this model.</value>
  </data>
  <data name="AssetType_Llama2_Page_primaryButton" xml:space="preserve">
    <value>Continue to Azure AI Foundry model catalog</value>
  </data>
  <data name="AssetType_cohere_Names_plural" xml:space="preserve">
    <value>Cohere</value>
  </data>
  <data name="AssetType_cohere_Names_singular" xml:space="preserve">
    <value>Cohere</value>
  </data>
  <data name="AssetType_cohere_Names_lowerPlural" xml:space="preserve">
    <value>Cohere</value>
  </data>
  <data name="AssetType_cohere_Names_lowerSingular" xml:space="preserve">
    <value>Cohere</value>
  </data>
  <data name="AssetType_cohere_Page_title" xml:space="preserve">
    <value>Continue to Azure AI Foundry to use this offer</value>
  </data>
  <data name="AssetType_cohere_Page_Description_intro" xml:space="preserve">
    <value>感謝您的青睞。</value>
  </data>
  <data name="AssetType_cohere_Page_Description_body" xml:space="preserve">
    <value>Select Continue, and you'll be redirected to the model catalog in Azure AI Foundry where you can finish subscribing and start using this model.</value>
  </data>
  <data name="AssetType_cohere_Page_primaryButton" xml:space="preserve">
    <value>Continue to Azure AI Foundry model catalog</value>
  </data>
  <data name="AssetType_Mistral_Names_plural" xml:space="preserve">
    <value>Mistral</value>
  </data>
  <data name="AssetType_Mistral_Names_singular" xml:space="preserve">
    <value>Mistral</value>
  </data>
  <data name="AssetType_Mistral_Names_lowerPlural" xml:space="preserve">
    <value>Mistral</value>
  </data>
  <data name="AssetType_Mistral_Names_lowerSingular" xml:space="preserve">
    <value>Mistral</value>
  </data>
  <data name="AssetType_Mistral_Page_title" xml:space="preserve">
    <value>Continue to Azure AI Foundry to use this offer</value>
  </data>
  <data name="AssetType_Mistral_Page_Description_intro" xml:space="preserve">
    <value>感謝您的青睞。</value>
  </data>
  <data name="AssetType_Mistral_Page_Description_body" xml:space="preserve">
    <value>Select Continue, and you'll be redirected to the model catalog in Azure AI Foundry where you can finish subscribing and start using this model.</value>
  </data>
  <data name="AssetType_Mistral_Page_primaryButton" xml:space="preserve">
    <value>Continue to Azure AI Foundry model catalog</value>
  </data>
  <data name="AssetType_Nixtla_Names_plural" xml:space="preserve">
    <value>Nixtla</value>
  </data>
  <data name="AssetType_Nixtla_Names_singular" xml:space="preserve">
    <value>Nixtla</value>
  </data>
  <data name="AssetType_Nixtla_Names_lowerPlural" xml:space="preserve">
    <value>Nixtla</value>
  </data>
  <data name="AssetType_Nixtla_Names_lowerSingular" xml:space="preserve">
    <value>Nixtla</value>
  </data>
  <data name="AssetType_Nixtla_Page_title" xml:space="preserve">
    <value>Continue to Azure AI Foundry to use this offer</value>
  </data>
  <data name="AssetType_Nixtla_Page_Description_intro" xml:space="preserve">
    <value>感謝您的青睞。</value>
  </data>
  <data name="AssetType_Nixtla_Page_Description_body" xml:space="preserve">
    <value>Select Continue, and you'll be redirected to the model catalog in Azure AI Foundry where you can finish subscribing and start using this model.</value>
  </data>
  <data name="AssetType_Nixtla_Page_primaryButton" xml:space="preserve">
    <value>Continue to Azure AI Foundry model catalog</value>
  </data>
  <data name="AssetType_Core42_Names_plural" xml:space="preserve">
    <value>Core42</value>
  </data>
  <data name="AssetType_Core42_Names_singular" xml:space="preserve">
    <value>Core42</value>
  </data>
  <data name="AssetType_Core42_Names_lowerPlural" xml:space="preserve">
    <value>Core42</value>
  </data>
  <data name="AssetType_Core42_Names_lowerSingular" xml:space="preserve">
    <value>Core42</value>
  </data>
  <data name="AssetType_Core42_Page_title" xml:space="preserve">
    <value>Continue to Azure AI Foundry to use this offer</value>
  </data>
  <data name="AssetType_Core42_Page_Description_intro" xml:space="preserve">
    <value>感謝您的青睞。</value>
  </data>
  <data name="AssetType_Core42_Page_Description_body" xml:space="preserve">
    <value>Select Continue, and you'll be redirected to the model catalog in Azure AI Foundry where you can finish subscribing and start using this model.</value>
  </data>
  <data name="AssetType_Core42_Page_primaryButton" xml:space="preserve">
    <value>Continue to Azure AI Foundry model catalog</value>
  </data>
  <data name="AssetType_ModelProvider_Names_plural" xml:space="preserve">
    <value>模型提供者</value>
  </data>
  <data name="AssetType_ModelProvider_Names_singular" xml:space="preserve">
    <value>模型提供者</value>
  </data>
  <data name="AssetType_ModelProvider_Names_lowerPlural" xml:space="preserve">
    <value>模型提供者</value>
  </data>
  <data name="AssetType_ModelProvider_Names_lowerSingular" xml:space="preserve">
    <value>模型提供者</value>
  </data>
  <data name="AssetType_ModelProvider_Page_title" xml:space="preserve">
    <value>Continue to Azure AI Foundry to use this offer</value>
  </data>
  <data name="AssetType_ModelProvider_Page_Description_intro" xml:space="preserve">
    <value>感謝您的青睞。</value>
  </data>
  <data name="AssetType_ModelProvider_Page_Description_body" xml:space="preserve">
    <value>Select Continue, and you'll be redirected to the model catalog in Azure AI Foundry where you can finish subscribing and start using this model.</value>
  </data>
  <data name="AssetType_ModelProvider_Page_primaryButton" xml:space="preserve">
    <value>Continue to Azure AI Foundry model catalog</value>
  </data>
  <data name="AssetType_AIStudio_Names_plural" xml:space="preserve">
    <value>Azure AI Foundry</value>
  </data>
  <data name="AssetType_AIStudio_Names_singular" xml:space="preserve">
    <value>Azure AI Foundry</value>
  </data>
  <data name="AssetType_AIStudio_Names_lowerPlural" xml:space="preserve">
    <value>Azure AI Foundry</value>
  </data>
  <data name="AssetType_AIStudio_Names_lowerSingular" xml:space="preserve">
    <value>Azure AI Foundry</value>
  </data>
  <data name="AssetType_AIStudio_Keywords" xml:space="preserve">
    <value>AI, Hub, AI Studio, Azure AI Studio, AI Foundry, Azure AI Foundry, AI Hub, AI Project, AIStudio, Artificial Intelligence, Preview, Deep Learning, Analytics, Data, NLP, Natural Language Processing, CNN, Neural Network, Train, Computer Vision, Models, Data Science, Classification, Regression, Reinforcement Learning, LLM, Chatbot</value>
  </data>
  <data name="AssetType_AIStudio_Description" xml:space="preserve">
    <value>您建置生成式 AI 解決方案和自訂副手的平台</value>
  </data>
  <data name="AssetType_AIStudio_Browse_Commands_CreateAzureAI" xml:space="preserve">
    <value>Create</value>
  </data>
  <data name="AssetType_AIStudio_Browse_Commands_CreateAzureAICommandBar" xml:space="preserve">
    <value>New Azure AI hub</value>
  </data>
  <data name="AssetType_AIStudio_Browse_Commands_CreateAzureAILabel" xml:space="preserve">
    <value>Create Azure AI Foundry</value>
  </data>
  <data name="AssetType_AIStudio_Browse_Commands_CreateAzureAIProject" xml:space="preserve">
    <value>Project</value>
  </data>
  <data name="AssetType_AIStudio_Browse_Commands_CreateAzureAIProjectLabel" xml:space="preserve">
    <value>Create Project</value>
  </data>
  <data name="AssetType_AIStudio_Browse_Commands_CreateAzureAIHub" xml:space="preserve">
    <value>Hub</value>
  </data>
  <data name="AssetType_AIStudio_Browse_Commands_CreateAzureAIHubLabel" xml:space="preserve">
    <value>Create Hub</value>
  </data>
  <data name="AssetType_AIStudio_Browse_Description_CreateAzureAIProject" xml:space="preserve">
    <value>Collaborate, organize, and track work to build AI apps.</value>
  </data>
  <data name="AssetType_AIStudio_Browse_Description_CreateAzureAIHub" xml:space="preserve">
    <value>Grouping container for projects. Provides security, connectivity, and compute management.</value>
  </data>
  <data name="AssetType_AIStudio_KeysAndEndpoints_showKeys" xml:space="preserve">
    <value>顯示金鑰</value>
  </data>
  <data name="AssetType_AIStudio_KeysAndEndpoints_hideKeys" xml:space="preserve">
    <value>隱藏金鑰</value>
  </data>
  <data name="AssetType_AIStudio_KeysAndEndpoints_regenerateKeys" xml:space="preserve">
    <value>重新產生金鑰</value>
  </data>
  <data name="AssetType_AIStudio_KeysAndEndpoints_title" xml:space="preserve">
    <value>金鑰與端點</value>
  </data>
  <data name="AssetType_AIStudio_KeysAndEndpoints_message" xml:space="preserve">
    <value>這些金鑰是用來存取您的 Azure AI 服務 API。請勿分享您的金鑰。安全地儲存它們，例如，使用 Azure Key Vault。我們也建議您定期重新產生這些密鑰。進行 API 呼叫只需要一個金鑰。重新產生第一個金鑰時，您可以使用第二個金鑰繼續存取服務。</value>
  </data>
  <data name="AssetType_AIStudio_KeysAndEndpoints_key1" xml:space="preserve">
    <value>金鑰 1</value>
  </data>
  <data name="AssetType_AIStudio_KeysAndEndpoints_key2" xml:space="preserve">
    <value>金鑰 2</value>
  </data>
  <data name="AssetType_AIStudio_KeysAndEndpoints_endpoint" xml:space="preserve">
    <value>端點</value>
  </data>
  <data name="AssetType_AIStudio_KeysAndEndpoints_Command_regenKey1" xml:space="preserve">
    <value>重新產生金鑰 1</value>
  </data>
  <data name="AssetType_AIStudio_KeysAndEndpoints_Command_regenKey2" xml:space="preserve">
    <value>重新產生金鑰 2</value>
  </data>
  <data name="AssetType_AIStudio_Hub_Essentials_AIServices_label" xml:space="preserve">
    <value>AI 服務提供者</value>
  </data>
  <data name="AssetType_AIStudio_Hub_Essentials_DefaultProjectResourceGroup_label" xml:space="preserve">
    <value>專案資源群組 (預設)</value>
  </data>
  <data name="AssetType_AIStudio_Hub_Overview_Banner_title" xml:space="preserve">
    <value>Govern the environment for your team in AI Foundry</value>
  </data>
  <data name="AssetType_AIStudio_Hub_Overview_Banner_Description_label" xml:space="preserve">
    <value>您的 Azure AI 中樞可提供企業級安全性與共同作業環境，以建置 AI 解決方案。集中稽核使用方式和成本，並設定所有專案都可使用的公司資源連結。{}</value>
    <comment>{}: learnMoreText</comment>
  </data>
  <data name="AssetType_AIStudio_Hub_Overview_Banner_Description_learnMoreText" xml:space="preserve">
    <value>learn more about the Azure AI Foundry</value>
  </data>
  <data name="AssetType_AIStudio_Hub_Overview_Banner_launchButtonText" xml:space="preserve">
    <value>Launch Azure AI Foundry</value>
  </data>
  <data name="AssetType_AIStudio_Hub_AIServices_Settings_title" xml:space="preserve">
    <value>建立新的 Azure AI 服務</value>
  </data>
  <data name="AssetType_AIStudio_Hub_Dropdown_label" xml:space="preserve">
    <value>Hub</value>
  </data>
  <data name="AssetType_AIStudio_Hub_Dropdown_info" xml:space="preserve">
    <value>Grouping container for projects. Provides security, connectivity, and compute management.</value>
  </data>
  <data name="AssetType_AIStudio_Hub_Dropdown_createNewAriaLabel" xml:space="preserve">
    <value>Create new Hub</value>
  </data>
  <data name="AssetType_AIStudio_Project_Overview_Banner_title" xml:space="preserve">
    <value>Start building in Azure AI Foundry</value>
  </data>
  <data name="AssetType_AIStudio_Project_Overview_Banner_Description_label" xml:space="preserve">
    <value>使用預建的範本來啟動您的 AI 解決方案開發，並以程式碼或在工作室中開發您的專案。</value>
  </data>
  <data name="AssetType_AIStudio_Project_Overview_Banner_launchButtonText" xml:space="preserve">
    <value>啟動工作室</value>
  </data>
  <data name="CreateBlade_Network_SubnetDelegate_title" xml:space="preserve">
    <value>Delegate Azure virtual network subnet for agents</value>
  </data>
  <data name="CreateBlade_Network_SubnetDelegate_message" xml:space="preserve">
    <value>Follows different management boundaries than your hub and projects, injected into your Azure VNET.</value>
  </data>
  <data name="CreateBlade_Network_subnetDelegate_learnMore" xml:space="preserve">
    <value>For more information, see</value>
  </data>
  <data name="Networking_PublicAccess_FailurePublicAccesNetworkNotificationDescription" xml:space="preserve">
    <value>An error occurred while saving public access settings. Please try again or contact support if the issue persists.</value>
  </data>
  <data name="Networking_PublicAccess_FailurePublicAccesNetworkNotificationTitle" xml:space="preserve">
    <value>Failed to Save Public Access Settings</value>
  </data>
  <data name="Networking_PublicAccess_InprogressPublicAccesNetworkNotificationDescription" xml:space="preserve">
    <value>Your changes to public access settings are being saved.</value>
  </data>
  <data name="Networking_PublicAccess_InprogressPublicAccesNetworkNotificationTitle" xml:space="preserve">
    <value>Saving Public Access Settings</value>
  </data>
  <data name="Networking_PublicAccess_SuccessPublicAccesNetworkNotificationDescription" xml:space="preserve">
    <value>Your changes to public access settings have been successfully saved.</value>
  </data>
  <data name="Networking_PublicAccess_SuccessPublicAccesNetworkNotificationTitle" xml:space="preserve">
    <value>Public Access Settings Saved</value>
  </data>
  <data name="Network_NetworkIsolation_ErrorWorkspaceManagedOutboundAccessNotificationDescription" xml:space="preserve">
    <value>An error occurred while saving workspace managed outbound access settings. Please try again or contact support if the issue persists.</value>
  </data>
  <data name="Network_NetworkIsolation_ErrorWorkspaceManagedOutboundAccessNotificationTitle" xml:space="preserve">
    <value>Failed to Save Workspace Managed Outbound Access Settings</value>
  </data>
  <data name="Network_NetworkIsolation_InProgressWorkspaceManagedOutboundAccessNotificationDescription" xml:space="preserve">
    <value>Your changes to workspace managed outbound access settings are being saved.</value>
  </data>
  <data name="Network_NetworkIsolation_InProgressWorkspaceManagedOutboundAccessNotificationTitle" xml:space="preserve">
    <value>Saving Workspace Managed Outbound Access Settings</value>
  </data>
  <data name="Network_NetworkIsolation_SuccessWorkspaceManagedOutboundAccessNotificationDescription" xml:space="preserve">
    <value>Your changes to workspace managed outbound access settings have been successfully saved.</value>
  </data>
  <data name="Network_NetworkIsolation_SuccessWorkspaceManagedOutboundAccessNotificationTitle" xml:space="preserve">
    <value>Workspace Managed Outbound Access Settings Saved</value>
  </data>
  <data name="AssetTypeNames_MachineLearningServices_Default_lowerPlural" xml:space="preserve">
    <value>工作區</value>
  </data>
  <data name="CES_ManagedWorkspaceOutbound_question" xml:space="preserve">
    <value>How easy or difficult was it to manage workspace outbound network settings on this page?</value>
  </data>
  <data name="CES_PrivateEndpoints__question" xml:space="preserve">
    <value>How easy or difficult was it to manage private endpoints connections on this page?</value>
  </data>
  <data name="CES_PublicAccess__question" xml:space="preserve">
    <value>How easy or difficult was it to manage public access networking settings on this page?</value>
  </data>
  <data name="CVA_ManagedWorkspaceOutbound_question" xml:space="preserve">
    <value>How valuable was this experience for managing workspace outbound network settings?</value>
  </data>
  <data name="CVA_PrivateEndpoints_question" xml:space="preserve">
    <value>How valuable was this experience for managing private endpoints connections?</value>
  </data>
  <data name="CVA_PublicAccess_question" xml:space="preserve">
    <value>How valuable was this experience for managing public access network settings?</value>
  </data>
  <data name="Toolbar_feedback" xml:space="preserve">
    <value>Feedback</value>
  </data>
  <data name="CreateBlade_Network_WorkspaceApprovedOutbound_OutboundAccess_hubRecommendedTargetDescription" xml:space="preserve">
    <value>There are several optional outbound targets recommended for your hub in scenarios such as VS Code, Prompt Flow, and more. You can modify or delete them.</value>
  </data>
  <data name="CreateBlade_Network_WorkspaceApprovedOutbound_OutboundAccess_hubRequiredTargetDescription" xml:space="preserve">
    <value>There are a few outbound targets added by Azure AI Foundry that are required for your workspace to access things like storage, key vault, and more.</value>
  </data>
</root>