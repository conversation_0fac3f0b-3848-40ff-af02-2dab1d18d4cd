declare module "Resx/ClientResources" {
    export = ClientResources;
    const ClientResources: {
        readonly AIServices: {
            readonly SummaryTab: {
                /**
                 * The text "AI Services"
                 */
                readonly label: string;
            };
        };
        readonly AccountPart: {
            /**
             * The text "Deprecated"
             */
            readonly deprecated: string;
            /**
             * The text "The resource is no longer available. Please use Machine Learning Services Workspace Extension."
             */
            readonly deprecatedLongMessage: string;
            /**
             * The text "Use Machine Learning Services Extension."
             */
            readonly deprecatedShortMessage: string;
        };
        readonly AdvancedTab: {
            readonly Key: {
                readonly KeyVaultChangeControl: {
                    /**
                     * The text "Click to select key"
                     */
                    readonly action: string;
                    /**
                     * The text "Key: {key}"
                     */
                    readonly key: string;
                    /**
                     * The text "Key vault: {keyvault}"
                     */
                    readonly keyVault: string;
                    /**
                     * The text "Key vault"
                     */
                    readonly label: string;
                    /**
                     * The text "When bringing your own encryption key, your Azure Key Vault must have purge protection enabled to protect against accidental loss of data access."
                     */
                    readonly purgeProtectionRequired: string;
                    /**
                     * The text "Key is required"
                     */
                    readonly required: string;
                    /**
                     * The text "Key vault and key"
                     */
                    readonly title: string;
                    /**
                     * The text "Version: {version}"
                     */
                    readonly version: string;
                };
            };
            readonly Section: {
                readonly DataEncryption: {
                    readonly ServiceSide: {
                        /**
                         * The text "Azure machine learning service stores metrics and metadata in an Azure Cosmos DB instance where all data is encrypted ​at rest. By default, the data is encrypted with Microsoft-managed keys. You may choose to bring your own (customer-managed) keys. {0}"
                         */
                        readonly infoText: string;
                        /**
                         * The text "Use service-side encryption"
                         */
                        readonly label: string;
                    };
                    readonly Type: {
                        /**
                         * The text "Encrypt data using a customer managed key"
                         */
                        readonly label: string;
                    };
                    /**
                     * The text "Optionally use pre-created resources for storing encrypted workspace data. Using your own resources for encryption, allows for enhanced configuration of these resources in compliance with your organization’s IT and security requirements, but implies additional management actions by you."
                     */
                    readonly checkboxByoInfo: string;
                    /**
                     * The text "Bring existing resources for storing encrypted data (preview)"
                     */
                    readonly checkboxByoLabel: string;
                    /**
                     * The text "Enable encryption using a Customer Managed Key"
                     */
                    readonly checkboxLabel: string;
                    /**
                     * The text "Select a pre-created Azure Cosmos DB resource. If you select 'None', Azure ML will create a Cosmos DB resource in a new resource group in your subscription that will be managed by Microsoft. Using your own resources for encryption implies additional management responsibility by you."
                     */
                    readonly cosmosInfo: string;
                    /**
                     * The text "Cosmos DB"
                     */
                    readonly cosmosLabel: string;
                    /**
                     * The text "Customer-managed keys"
                     */
                    readonly customerManaged: string;
                    /**
                     * The text "Your data is encrypted by default using Microsoft-managed keys. For additional control over your data, you may choose to bring your own key for encryption. {0}"
                     */
                    readonly header: string;
                    /**
                     * The text "Azure machine learning service stores metrics and metadata in an Azure Cosmos DB instance where all data is encrypted ​at rest. By default, the data is encrypted with Microsoft-managed keys. You may choose to bring your own (customer-managed) keys. {0}"
                     */
                    readonly infoText: string;
                    /**
                     * The text "Learn more"
                     */
                    readonly learnMoreText: string;
                    /**
                     * The text "Learn more about customer-managed key encryption."
                     */
                    readonly linkText: string;
                    /**
                     * The text "Microsoft-managed keys"
                     */
                    readonly microsoftManaged: string;
                    /**
                     * The text "Select a pre-created Azure Search resource. If you select 'None', Azure ML will create a Search resource in a new resource group in your subscription that will be managed by Microsoft. Using your own resources for encryption implies additional management responsibility by you."
                     */
                    readonly searchInfo: string;
                    /**
                     * The text "Search"
                     */
                    readonly searchLabel: string;
                    /**
                     * The text "Select a pre-created Azure Storage resource. If you select 'None', Azure ML will create a Storage resource in a new resource group in your subscription that will be managed by Microsoft. Using your own resources for encryption implies additional management responsibility by you."
                     */
                    readonly storageInfo: string;
                    /**
                     * The text "Storage"
                     */
                    readonly storageLabel: string;
                    /**
                     * The text "Data encryption"
                     */
                    readonly title: string;
                    /**
                     * The text "After workspace creation, you cannot change encryption key type between Microsoft-managed keys  and Customer-managed keys."
                     */
                    readonly warningMessage: string;
                };
                readonly DataImpact: {
                    readonly HBI: {
                        /**
                         * The text "High business impact workspace"
                         */
                        readonly label: string;
                    };
                    /**
                     * The text "If your workspace contains sensitive data, you can specify a high business impact workspace. This will control the amount of data Microsoft collects for diagnostic purposes and enables additional encryption in Microsoft managed environments.​"
                     */
                    readonly header: string;
                    /**
                     * The text "Data impact"
                     */
                    readonly title: string;
                };
                readonly ManagedIdentity: {
                    /**
                     * The text "A managed identity enables Azure resources to authenticate to cloud services without storing credentials in code. Once enabled, all necessary permissions can be granted via Azure role-based access control. A workspace can be given either a system assigned identity or a user assigned identity."
                     */
                    readonly description: string;
                    /**
                     * The text "The managed user assigned identity option is only supported if an existing storage account, key vault, and container registry are used."
                     */
                    readonly disabledWarning: string;
                    /**
                     * The text "Managed identity"
                     */
                    readonly header: string;
                    /**
                     * The text "If you bring your own associated resources, instead of having Azure Machine Learning service create them, you must grant the managed identity roles on those resources. Use the {0} to make the assignments."
                     */
                    readonly permissionWarning: string;
                    /**
                     * The text "role assignment ARM template"
                     */
                    readonly permissionWarningLinkText: string;
                    /**
                     * The text "Identity type"
                     */
                    readonly radioGroupTitle: string;
                    /**
                     * The text "System assigned identity"
                     */
                    readonly systemAssignedText: string;
                    /**
                     * The text "User assigned identity"
                     */
                    readonly userAssignedPickerLabel: string;
                    /**
                     * The text "User identity is required"
                     */
                    readonly userAssignedPickerRequiredMessage: string;
                    /**
                     * The text "Workspace and selected user identity must be in the same subscription."
                     */
                    readonly userAssignedPickerSubscriptionMessage: string;
                    /**
                     * The text "Click to select identity"
                     */
                    readonly userAssignedPickerText: string;
                    /**
                     * The text "User assigned identity"
                     */
                    readonly userAssignedText: string;
                    /**
                     * The text "User identity name"
                     */
                    readonly userIdentityNameLabel: string;
                    /**
                     * The text "User identity resource group"
                     */
                    readonly userIdentityResourceGroupLabel: string;
                };
                readonly StorageAccountAccess: {
                    /**
                     * The text "Credential-based access"
                     */
                    readonly credentialBasedText: string;
                    /**
                     * The text "Azure machine learning allows you to choose between credential-based or identity-based access when connecting to the default storage account."
                     */
                    readonly description: string;
                    /**
                     * The text "Storage account access"
                     */
                    readonly header: string;
                    /**
                     * The text "Identity-based access"
                     */
                    readonly identityBasedText: string;
                    /**
                     * The text "learn more about RBAC settings"
                     */
                    readonly identityBasedWarningLearnMore: string;
                    /**
                     * The text "{0} When using identity-based authentication, "{1}" and "{2}" roles must be granted to {3} that need access on the storage account. Contact your admin for help or"
                     */
                    readonly identityBasedWarningText: string;
                    /**
                     * The text "Important"
                     */
                    readonly important: string;
                    /**
                     * The text "individual users"
                     */
                    readonly individualUsers: string;
                    /**
                     * The text "Learn more"
                     */
                    readonly learnMoreText: string;
                    /**
                     * The text "(preview)"
                     */
                    readonly previewLinkText: string;
                    /**
                     * The text "Storage account access type"
                     */
                    readonly radioGroupTitle: string;
                    /**
                     * The text "Disable shared key access"
                     */
                    readonly sharedKeyAccessCheckboxLabel: string;
                    /**
                     * The text "Learn more"
                     */
                    readonly sharedKeyAccessDisableDocumentationLinkText: string;
                    /**
                     * The text "Disable shared key access option {0} disables key access at the storage account, any requests to the storage account that are authorized with shared key, including shared access signatures (SAS), will be denied. This option might require additional role configuration to individual users for certain use cases. {1} about disabling shared key access for your workspace's storage account"
                     */
                    readonly sharedKeyAccessDisableWarning: string;
                };
            };
        };
        readonly AppInsights: {
            readonly Dropdown: {
                readonly Hub: {
                    /**
                     * The text "The AI hub uses Azure Application Insights to store monitoring information about your deployed models. You may create a new Azure Application Insights resource or select an existing one in your subscription."
                     */
                    readonly info: string;
                };
                readonly SettingsBlade: {
                    readonly Name: {
                        /**
                         * The text "The name must meet the following requirements:"
                         */
                        readonly infoDescription: string;
                        /**
                         * The text "Unique across the resource group"
                         */
                        readonly infoItem1: string;
                        /**
                         * The text "Between 1 and 255 characters long"
                         */
                        readonly infoItem2: string;
                        /**
                         * The text "Only contain alphanumeric characters, periods, underscores, hyphens, and parenthesis"
                         */
                        readonly infoItem3: string;
                        /**
                         * The text "Cannot end with a period"
                         */
                        readonly infoItem4: string;
                    };
                    /**
                     * The text "The name must contain between 1 to 255 characters inclusive. The name only allows alphanumeric characters, periods, underscores, hyphens and parenthesis and cannot end in a period."
                     */
                    readonly nameInvalidMessage: string;
                    /**
                     * The text "Name"
                     */
                    readonly nameLabel: string;
                    /**
                     * The text "Create new application insights"
                     */
                    readonly title: string;
                };
                /**
                 * The text "Create new application insights"
                 */
                readonly createNewAriaLabel: string;
                /**
                 * The text "The workspace uses Azure Application Insights to store monitoring information about your deployed models. You may create a new Azure Application Insights resource or select an existing one in your subscription."
                 */
                readonly info: string;
                /**
                 * The text "Application insights"
                 */
                readonly label: string;
            };
            readonly SummaryTab: {
                /**
                 * The text "Application insights"
                 */
                readonly label: string;
            };
        };
        readonly AssetType: {
            readonly AIStudio: {
                readonly Browse: {
                    readonly Commands: {
                        /**
                         * The text "Create"
                         */
                        readonly createAzureAI: string;
                        /**
                         * The text "New Azure AI hub"
                         */
                        readonly createAzureAICommandBar: string;
                        /**
                         * The text "Hub"
                         */
                        readonly createAzureAIHub: string;
                        /**
                         * The text "Create Hub"
                         */
                        readonly createAzureAIHubLabel: string;
                        /**
                         * The text "Create Azure AI Foundry"
                         */
                        readonly createAzureAILabel: string;
                        /**
                         * The text "Project"
                         */
                        readonly createAzureAIProject: string;
                        /**
                         * The text "Create Project"
                         */
                        readonly createAzureAIProjectLabel: string;
                    };
                    readonly Description: {
                        /**
                         * The text "Grouping container for projects. Provides security, connectivity, and compute management."
                         */
                        readonly createAzureAIHub: string;
                        /**
                         * The text "Collaborate, organize, and track work to build AI apps."
                         */
                        readonly createAzureAIProject: string;
                    };
                };
                readonly Hub: {
                    readonly AIServices: {
                        readonly Settings: {
                            /**
                             * The text "Create new Azure AI Services"
                             */
                            readonly title: string;
                        };
                    };
                    readonly Dropdown: {
                        /**
                         * The text "Create new Hub"
                         */
                        readonly createNewAriaLabel: string;
                        /**
                         * The text "Grouping container for projects. Provides security, connectivity, and compute management."
                         */
                        readonly info: string;
                        /**
                         * The text "Hub"
                         */
                        readonly label: string;
                    };
                    readonly Essentials: {
                        readonly AIServices: {
                            /**
                             * The text "AI Services provider"
                             */
                            readonly label: string;
                        };
                        readonly DefaultProjectResourceGroup: {
                            /**
                             * The text "Project resource group (default)"
                             */
                            readonly label: string;
                        };
                    };
                    readonly Overview: {
                        readonly Banner: {
                            readonly Description: {
                                /**
                                 * The text "Your Azure AI hub provides enterpise-grade security, and a collaborative environment to build AI solutions. Centrally audit usage and cost, and set up connections to your company resources that all projects can use. {}"
                                 */
                                readonly label: string;
                                /**
                                 * The text "learn more about the Azure AI Foundry"
                                 */
                                readonly learnMoreText: string;
                            };
                            /**
                             * The text "Launch Azure AI Foundry"
                             */
                            readonly launchButtonText: string;
                            /**
                             * The text "Govern the environment for your team in AI Foundry"
                             */
                            readonly title: string;
                        };
                    };
                };
                readonly KeysAndEndpoints: {
                    readonly Command: {
                        /**
                         * The text "Regenerate Key 1"
                         */
                        readonly regenKey1: string;
                        /**
                         * The text "Regenerate Key 2"
                         */
                        readonly regenKey2: string;
                    };
                    /**
                     * The text "Endpoint"
                     */
                    readonly endpoint: string;
                    /**
                     * The text "Hide keys"
                     */
                    readonly hideKeys: string;
                    /**
                     * The text "KEY 1"
                     */
                    readonly key1: string;
                    /**
                     * The text "KEY 2"
                     */
                    readonly key2: string;
                    /**
                     * The text "These keys are used to access your Azure AI services API. Do not share your keys. Store them securely- for example, using Azure Key Vault. We also recommend regenerating these keys regularly. Only one key is necessary to make an API call. When regenerating the first key, you can use the second key for continued access to the service."
                     */
                    readonly message: string;
                    /**
                     * The text "Regenerate keys"
                     */
                    readonly regenerateKeys: string;
                    /**
                     * The text "Show keys"
                     */
                    readonly showKeys: string;
                    /**
                     * The text "Keys and Endpoints"
                     */
                    readonly title: string;
                };
                readonly Names: {
                    /**
                     * The text "Azure AI Foundry"
                     */
                    readonly lowerPlural: string;
                    /**
                     * The text "Azure AI Foundry"
                     */
                    readonly lowerSingular: string;
                    /**
                     * The text "Azure AI Foundry"
                     */
                    readonly plural: string;
                    /**
                     * The text "Azure AI Foundry"
                     */
                    readonly singular: string;
                };
                readonly Project: {
                    readonly Overview: {
                        readonly Banner: {
                            readonly Description: {
                                /**
                                 * The text "Jumpstart your AI solution development with pre-built templates and work on your project either in code or in the studio."
                                 */
                                readonly label: string;
                            };
                            /**
                             * The text "Launch studio"
                             */
                            readonly launchButtonText: string;
                            /**
                             * The text "Start building in Azure AI Foundry"
                             */
                            readonly title: string;
                        };
                    };
                };
                /**
                 * The text "Your platform to build generative AI solutions and custom copilots"
                 */
                readonly description: string;
                /**
                 * The text "AI, Hub, AI Studio, Azure AI Studio, AI Foundry, Azure AI Foundry, AI Hub, AI Project, AIStudio, Artificial Intelligence, Preview, Deep Learning, Analytics, Data, NLP, Natural Language Processing, CNN, Neural Network, Train, Computer Vision, Models, Data Science, Classification, Regression, Reinforcement Learning, LLM, Chatbot"
                 */
                readonly keywords: string;
            };
            readonly Cohere: {
                readonly Names: {
                    /**
                     * The text "Cohere"
                     */
                    readonly lowerPlural: string;
                    /**
                     * The text "Cohere"
                     */
                    readonly lowerSingular: string;
                    /**
                     * The text "Cohere"
                     */
                    readonly plural: string;
                    /**
                     * The text "Cohere"
                     */
                    readonly singular: string;
                };
                readonly Page: {
                    readonly Description: {
                        /**
                         * The text "Select Continue, and you'll be redirected to the model catalog in Azure AI Foundry where you can finish subscribing and start using this model."
                         */
                        readonly body: string;
                        /**
                         * The text "Thank you for your interest."
                         */
                        readonly intro: string;
                    };
                    /**
                     * The text "Continue to Azure AI Foundry model catalog"
                     */
                    readonly primaryButton: string;
                    /**
                     * The text "Continue to Azure AI Foundry to use this offer"
                     */
                    readonly title: string;
                };
            };
            readonly Core42: {
                readonly Names: {
                    /**
                     * The text "Core42"
                     */
                    readonly lowerPlural: string;
                    /**
                     * The text "Core42"
                     */
                    readonly lowerSingular: string;
                    /**
                     * The text "Core42"
                     */
                    readonly plural: string;
                    /**
                     * The text "Core42"
                     */
                    readonly singular: string;
                };
                readonly Page: {
                    readonly Description: {
                        /**
                         * The text "Select Continue, and you'll be redirected to the model catalog in Azure AI Foundry where you can finish subscribing and start using this model."
                         */
                        readonly body: string;
                        /**
                         * The text "Thank you for your interest."
                         */
                        readonly intro: string;
                    };
                    /**
                     * The text "Continue to Azure AI Foundry model catalog"
                     */
                    readonly primaryButton: string;
                    /**
                     * The text "Continue to Azure AI Foundry to use this offer"
                     */
                    readonly title: string;
                };
            };
            readonly Llama2: {
                readonly Names: {
                    /**
                     * The text "Llama2"
                     */
                    readonly lowerPlural: string;
                    /**
                     * The text "Llama2"
                     */
                    readonly lowerSingular: string;
                    /**
                     * The text "Llama2"
                     */
                    readonly plural: string;
                    /**
                     * The text "Llama2"
                     */
                    readonly singular: string;
                };
                readonly Page: {
                    readonly Description: {
                        /**
                         * The text "Select Continue, and you'll be redirected to the model catalog in Azure AI Foundry where you can finish subscribing and start using this model."
                         */
                        readonly body: string;
                        /**
                         * The text "Thank you for your interest."
                         */
                        readonly intro: string;
                    };
                    /**
                     * The text "Continue to Azure AI Foundry model catalog"
                     */
                    readonly primaryButton: string;
                    /**
                     * The text "Continue to Azure AI Foundry to use this offer"
                     */
                    readonly title: string;
                };
            };
            readonly Mistral: {
                readonly Names: {
                    /**
                     * The text "Mistral"
                     */
                    readonly lowerPlural: string;
                    /**
                     * The text "Mistral"
                     */
                    readonly lowerSingular: string;
                    /**
                     * The text "Mistral"
                     */
                    readonly plural: string;
                    /**
                     * The text "Mistral"
                     */
                    readonly singular: string;
                };
                readonly Page: {
                    readonly Description: {
                        /**
                         * The text "Select Continue, and you'll be redirected to the model catalog in Azure AI Foundry where you can finish subscribing and start using this model."
                         */
                        readonly body: string;
                        /**
                         * The text "Thank you for your interest."
                         */
                        readonly intro: string;
                    };
                    /**
                     * The text "Continue to Azure AI Foundry model catalog"
                     */
                    readonly primaryButton: string;
                    /**
                     * The text "Continue to Azure AI Foundry to use this offer"
                     */
                    readonly title: string;
                };
            };
            readonly ModelProvider: {
                readonly Names: {
                    /**
                     * The text "Model Provider"
                     */
                    readonly lowerPlural: string;
                    /**
                     * The text "Model Provider"
                     */
                    readonly lowerSingular: string;
                    /**
                     * The text "Model Provider"
                     */
                    readonly plural: string;
                    /**
                     * The text "Model Provider"
                     */
                    readonly singular: string;
                };
                readonly Page: {
                    readonly Description: {
                        /**
                         * The text "Select Continue, and you'll be redirected to the model catalog in Azure AI Foundry where you can finish subscribing and start using this model."
                         */
                        readonly body: string;
                        /**
                         * The text "Thank you for your interest."
                         */
                        readonly intro: string;
                    };
                    /**
                     * The text "Continue to Azure AI Foundry model catalog"
                     */
                    readonly primaryButton: string;
                    /**
                     * The text "Continue to Azure AI Foundry to use this offer"
                     */
                    readonly title: string;
                };
            };
            readonly Nixtla: {
                readonly Names: {
                    /**
                     * The text "Nixtla"
                     */
                    readonly lowerPlural: string;
                    /**
                     * The text "Nixtla"
                     */
                    readonly lowerSingular: string;
                    /**
                     * The text "Nixtla"
                     */
                    readonly plural: string;
                    /**
                     * The text "Nixtla"
                     */
                    readonly singular: string;
                };
                readonly Page: {
                    readonly Description: {
                        /**
                         * The text "Select Continue, and you'll be redirected to the model catalog in Azure AI Foundry where you can finish subscribing and start using this model."
                         */
                        readonly body: string;
                        /**
                         * The text "Thank you for your interest."
                         */
                        readonly intro: string;
                    };
                    /**
                     * The text "Continue to Azure AI Foundry model catalog"
                     */
                    readonly primaryButton: string;
                    /**
                     * The text "Continue to Azure AI Foundry to use this offer"
                     */
                    readonly title: string;
                };
            };
            /**
             * The text "Workspaces are where you manage all the models, assets, and data related to your machine learning projects. Create one now to start using Azure Machine Learning."
             */
            readonly description: string;
            /**
             * The text "ML, AML, Machine Learning, AI, Artificial Intelligence, Preview, Deep Learning, Analytics, Data, NLP, Natural Language Processing, CNN, Neural Network, Workbench, Train, Notebooks, AutoML, Designer, Computer Vision, Models, Data Science, Classification, Regression, Reinforcement Learning"
             */
            readonly keywords: string;
        };
        readonly AssetTypeNames: {
            readonly MLApp: {
                /**
                 * The text "machine learning online endpoints"
                 */
                readonly lowerPlural: string;
                /**
                 * The text "machine learning online endpoint"
                 */
                readonly lowerSingular: string;
                /**
                 * The text "Machine learning online endpoints"
                 */
                readonly plural: string;
                /**
                 * The text "Machine learning online endpoint"
                 */
                readonly singular: string;
            };
            readonly MLAppDeployment: {
                /**
                 * The text "machine learning online deployments"
                 */
                readonly lowerPlural: string;
                /**
                 * The text "machine learning online deployment"
                 */
                readonly lowerSingular: string;
                /**
                 * The text "Machine learning online deployments"
                 */
                readonly plural: string;
                /**
                 * The text "Machine learning online deployment"
                 */
                readonly singular: string;
            };
            readonly MLRegistry: {
                /**
                 * The text "azure machine learning registries"
                 */
                readonly lowerPlural: string;
                /**
                 * The text "azure machine learning registry"
                 */
                readonly lowerSingular: string;
                /**
                 * The text "Azure Machine Learning registries"
                 */
                readonly plural: string;
                /**
                 * The text "Azure Machine Learning registry"
                 */
                readonly singular: string;
            };
            readonly MachineLearningExperimentationAccount: {
                /**
                 * The text "Retired - Machine Learning Experimentation"
                 */
                readonly singular: string;
            };
            readonly MachineLearningServices: {
                readonly Default: {
                    /**
                     * The text "workspaces"
                     */
                    readonly lowerPlural: string;
                    /**
                     * The text "workspace"
                     */
                    readonly lowerSingular: string;
                    /**
                     * The text "Azure Machine Learning workspaces"
                     */
                    readonly plural: string;
                    /**
                     * The text "Azure Machine Learning workspace"
                     */
                    readonly singular: string;
                };
                readonly FeatureStore: {
                    /**
                     * The text "feature stores"
                     */
                    readonly lowerPlural: string;
                    /**
                     * The text "feature store"
                     */
                    readonly lowerSingular: string;
                    /**
                     * The text "Azure Machine Learning feature stores"
                     */
                    readonly plural: string;
                    /**
                     * The text "Azure Machine Learning feature store"
                     */
                    readonly singular: string;
                };
                readonly Hub: {
                    /**
                     * The text "Azure AI hubs"
                     */
                    readonly lowerPlural: string;
                    /**
                     * The text "Azure AI hub"
                     */
                    readonly lowerSingular: string;
                    /**
                     * The text "Azure AI hubs"
                     */
                    readonly plural: string;
                    /**
                     * The text "Azure AI hub"
                     */
                    readonly singular: string;
                };
                readonly Project: {
                    /**
                     * The text "Azure AI projects"
                     */
                    readonly lowerPlural: string;
                    /**
                     * The text "Azure AI project"
                     */
                    readonly lowerSingular: string;
                    /**
                     * The text "Azure AI projects"
                     */
                    readonly plural: string;
                    /**
                     * The text "Azure AI project"
                     */
                    readonly singular: string;
                };
                /**
                 * The text "workspaces"
                 */
                readonly lowerPlural: string;
                /**
                 * The text "workspace"
                 */
                readonly lowerSingular: string;
                /**
                 * The text "Azure Machine Learning"
                 */
                readonly plural: string;
                /**
                 * The text "Azure Machine Learning workspace"
                 */
                readonly singular: string;
            };
        };
        readonly AssociatedResource: {
            readonly AppInsights: {
                readonly ChangeBlade: {
                    /**
                     * The text "Select an application insights"
                     */
                    readonly title: string;
                };
                readonly Properties: {
                    /**
                     * The text "Change application insights"
                     */
                    readonly changeText: string;
                };
                readonly ReactView: {
                    readonly ChangeAppInsights: {
                        /**
                         * The text "Application insights"
                         */
                        readonly appInsightsLabel: string;
                        /**
                         * The text "Application insights updating..."
                         */
                        readonly appInsightsUpdatingSpinner: string;
                        /**
                         * The text "An error occurred while updating the application insights for this resource."
                         */
                        readonly error: string;
                        /**
                         * The text "Error updating application insights"
                         */
                        readonly errorTitle: string;
                        /**
                         * The text "Discard"
                         */
                        readonly footerCancel: string;
                        /**
                         * The text "Save"
                         */
                        readonly footerSave: string;
                        /**
                         * The text "The resource being updated could not be found."
                         */
                        readonly notFoundError: string;
                        /**
                         * The text "You do not have permission to update the application insights for this resource."
                         */
                        readonly unauthorizedError: string;
                    };
                    readonly Dropdown: {
                        /**
                         * The text "No application insights resources found"
                         */
                        readonly emptyMessage: string;
                        /**
                         * The text "Error loading application insights"
                         */
                        readonly errorLoadingMessage: string;
                        /**
                         * The text "No results matching {0}"
                         */
                        readonly noMatchMessage: string;
                        /**
                         * The text "Select an application insights"
                         */
                        readonly placeholder: string;
                    };
                };
            };
            readonly ContainerRegistry: {
                readonly ChangeBlade: {
                    /**
                     * The text "Select a container registry"
                     */
                    readonly title: string;
                };
                readonly Properties: {
                    /**
                     * The text "Change container registry"
                     */
                    readonly changeText: string;
                };
                readonly ReactView: {
                    readonly ChangeContainerRegistry: {
                        /**
                         * The text "Container registry"
                         */
                        readonly containerRegistryLabel: string;
                        /**
                         * The text "Container registry updating..."
                         */
                        readonly containerRegistryUpdatingSpinner: string;
                        /**
                         * The text "An error occurred while updating the container registry for this resource."
                         */
                        readonly error: string;
                        /**
                         * The text "Error updating container registry"
                         */
                        readonly errorTitle: string;
                        /**
                         * The text "Discard"
                         */
                        readonly footerCancel: string;
                        /**
                         * The text "Save"
                         */
                        readonly footerSave: string;
                        /**
                         * The text "The resource being updated could not be found."
                         */
                        readonly notFoundError: string;
                        /**
                         * The text "You do not have permission to update the container registry for this resource."
                         */
                        readonly unauthorizedError: string;
                    };
                    readonly Dropdown: {
                        /**
                         * The text "No container registry resources found"
                         */
                        readonly emptyMessage: string;
                        /**
                         * The text "Error loading container registry"
                         */
                        readonly errorLoadingMessage: string;
                        /**
                         * The text "No results matching {0}"
                         */
                        readonly noMatchMessage: string;
                        /**
                         * The text "Select a container registry"
                         */
                        readonly placeholder: string;
                    };
                };
            };
            readonly Dropdown: {
                readonly SettingsBlade: {
                    /**
                     * The text "Discard"
                     */
                    readonly discardButtonText: string;
                    /**
                     * The text "Save"
                     */
                    readonly saveButtonText: string;
                };
                /**
                 * The text "Create new"
                 */
                readonly createNewLinkText: string;
                /**
                 * The text "Select existing..."
                 */
                readonly filterPlaceholder: string;
                /**
                 * The text "(new) {0}"
                 */
                readonly newResourceText: string;
                /**
                 * The text "None"
                 */
                readonly none: string;
            };
        };
        readonly BasicsTab: {
            readonly HubName: {
                /**
                 * The text "Name of the AI hub"
                 */
                readonly balloonContent: string;
            };
            readonly InfoText: {
                /**
                 * The text "For your convenience, these resources are added automatically to the workspace, if regionally available: "
                 */
                readonly additionalResourceInfoText: string;
                /**
                 * The text "Azure Application Insights"
                 */
                readonly azureApplicationInsights: string;
                /**
                 * The text "Azure Key Vault"
                 */
                readonly azureKeyVault: string;
                /**
                 * The text "Azure Storage"
                 */
                readonly azureStorage: string;
            };
            readonly ProjectName: {
                /**
                 * The text "Name of the AI project"
                 */
                readonly balloonContent: string;
            };
            readonly Region: {
                /**
                 * The text "Compute targets can only be created in the same region as the workspace. Ensure the selected region has the virtual machine series needed for your workspace compute targets."
                 */
                readonly balloonContent: string;
                /**
                 * The text "Learn more about compute targets"
                 */
                readonly learnMoreComputeTargets: string;
                /**
                 * The text "View available virtual machines series by region"
                 */
                readonly viewAvailableVirtualMachines: string;
            };
            readonly RegistryName: {
                /**
                 * The text "The registry name must be unique within your resource group."
                 */
                readonly balloonContent: string;
            };
            readonly ResourceGroup: {
                /**
                 * The text "A resource group is a collection of resources that share the same life cycle, permissions, and policies."
                 */
                readonly balloonContent: string;
            };
            readonly Subscription: {
                /**
                 * The text "All resources in an Azure subscription are billed together."
                 */
                readonly balloonContent: string;
            };
            readonly WarningText: {
                /**
                 * The text "The selected subscription doesn’t have permissions to register the resource provider."
                 */
                readonly workspaceErrorMessageText: string;
            };
            readonly WorkspaceEdition: {
                /**
                 * The text "Basic"
                 */
                readonly basic: string;
                /**
                 * The text "Enterprise"
                 */
                readonly enterprise: string;
                /**
                 * The text "View full pricing details"
                 */
                readonly viewFullPricingDetails: string;
            };
            readonly WorkspaceName: {
                /**
                 * The text "The workspace name must be unique within your resource group."
                 */
                readonly balloonContent: string;
            };
            /**
             * The text "Azure AI services base models"
             */
            readonly aIServiceSectionHeader: string;
            /**
             * The text "Projects are grouped by a hub, which provides security configurations, pre-configured connectivity with other Azure resources, compute, storage, and quota."
             */
            readonly hubSectionContent: string;
            /**
             * The text "Share security, connectivity, compute"
             */
            readonly hubSectionHeader: string;
            /**
             * The text "Organization"
             */
            readonly organization: string;
            /**
             * The text "Registry details"
             */
            readonly registryDetails: string;
            /**
             * The text "Workspace details"
             */
            readonly workspaceDetails: string;
            /**
             * The text "Resource details"
             */
            readonly workspaceHubDetails: string;
        };
        readonly Browse: {
            /**
             * The text "New workspace"
             */
            readonly addMachineLearningLabel: string;
            /**
             * The text "New registry"
             */
            readonly addMachineLearningRegistryLabel: string;
        };
        readonly CES: {
            readonly ManagedWorkspaceOutbound: {
                /**
                 * The text "How easy or difficult was it to manage workspace outbound network settings on this page?"
                 */
                readonly question: string;
            };
            readonly PrivateEndpoints: {
                /**
                 * The text "How easy or difficult was it to manage private endpoints connections on this page?"
                 */
                readonly question: string;
            };
            readonly PublicAccess: {
                /**
                 * The text "How easy or difficult was it to manage public access networking settings on this page?"
                 */
                readonly question: string;
            };
        };
        readonly CVA: {
            readonly ManagedWorkspaceOutbound: {
                /**
                 * The text "How valuable was this experience for managing workspace outbound network settings?"
                 */
                readonly question: string;
            };
            readonly PrivateEndpoints: {
                /**
                 * The text "How valuable was this experience for managing private endpoints connections?"
                 */
                readonly question: string;
            };
            readonly PublicAccess: {
                /**
                 * The text "How valuable was this experience for managing public access network settings?"
                 */
                readonly question: string;
            };
        };
        readonly Commands: {
            /**
             * The text "Delete"
             */
            readonly delete: string;
        };
        readonly ContainerRegistry: {
            readonly Dropdown: {
                readonly SettingsBlade: {
                    readonly Name: {
                        /**
                         * The text "The name must meet the following requirements:"
                         */
                        readonly infoDescription: string;
                        /**
                         * The text "Unique across all container registries in Azure"
                         */
                        readonly infoItem1: string;
                        /**
                         * The text "Between 5 and 50 characters long"
                         */
                        readonly infoItem2: string;
                        /**
                         * The text "Only contain alphanumeric characters"
                         */
                        readonly infoItem3: string;
                    };
                    readonly Sku: {
                        /**
                         * The text "All SKUs provide the same programmatic capabilities. Choosing a higher SKU will provide more performance and scale."
                         */
                        readonly info: string;
                        /**
                         * The text "Learn more"
                         */
                        readonly infoLearnMore: string;
                    };
                    /**
                     * The text "Resource names may contain alpha numeric characters only and must be between 5 and 50 characters."
                     */
                    readonly nameInvalidMessage: string;
                    /**
                     * The text "Name"
                     */
                    readonly nameLabel: string;
                    /**
                     * The text "Basic"
                     */
                    readonly skuBasic: string;
                    /**
                     * The text "SKU"
                     */
                    readonly skuLabel: string;
                    /**
                     * The text "Premium"
                     */
                    readonly skuPremium: string;
                    /**
                     * The text "Standard"
                     */
                    readonly skuStandard: string;
                    /**
                     * The text "Create new container registry"
                     */
                    readonly title: string;
                };
                /**
                 * The text "Create new container registry"
                 */
                readonly createNewAriaLabel: string;
                /**
                 * The text "A container registry is used to register docker images used in training and deployments. To minimize costs, a new Azure Container Registry resource is created only after you build your first image. Alternatively, you may choose to create the resource now or select an existing one in your subscription."
                 */
                readonly info: string;
                /**
                 * The text "Container registry"
                 */
                readonly label: string;
            };
            readonly SummaryTab: {
                /**
                 * The text "Container registry"
                 */
                readonly label: string;
            };
        };
        readonly Create: {
            readonly CreateButton: {
                /**
                 * The text "Create"
                 */
                readonly label: string;
            };
            readonly Wizard: {
                /**
                 * The text "Create Azure Machine Learning"
                 */
                readonly title: string;
            };
        };
        readonly CreateBlade: {
            readonly Advanced: {
                readonly Review: {
                    readonly Encryption: {
                        /**
                         * The text "Encryption"
                         */
                        readonly title: string;
                    };
                    readonly Identity: {
                        /**
                         * The text "Identity"
                         */
                        readonly title: string;
                    };
                    readonly Resources: {
                        /**
                         * The text "Resources"
                         */
                        readonly title: string;
                    };
                    /**
                     * The text "Cosmos DB for customer managed keys"
                     */
                    readonly byoCosmosText: string;
                    /**
                     * The text "Search service for customer managed keys"
                     */
                    readonly byoSearchText: string;
                    /**
                     * The text "Storage account for customer managed keys"
                     */
                    readonly byoStorageText: string;
                    /**
                     * The text "{0} ({1})"
                     */
                    readonly byoValueFormat: string;
                    /**
                     * The text "Credential-based"
                     */
                    readonly credentialBasedStorageAccountAccess: string;
                    /**
                     * The text "Customer-managed keys"
                     */
                    readonly customerManagedKeys: string;
                    /**
                     * The text "Disabled"
                     */
                    readonly disabled: string;
                    /**
                     * The text "Enable HBI Flag"
                     */
                    readonly enableHBIFlag: string;
                    /**
                     * The text "Enabled"
                     */
                    readonly enabled: string;
                    /**
                     * The text "Encryption type"
                     */
                    readonly encryptionType: string;
                    /**
                     * The text "Identity-based"
                     */
                    readonly identityBasedStorageAccountAccess: string;
                    /**
                     * The text "Key URI"
                     */
                    readonly keyURI: string;
                    /**
                     * The text "Key vault"
                     */
                    readonly keyVault: string;
                    /**
                     * The text "Microsoft-managed keys"
                     */
                    readonly microsoftManagedKeys: string;
                    /**
                     * The text "Shared key access"
                     */
                    readonly sharedKeyAccess: string;
                    /**
                     * The text "Storage account access type"
                     */
                    readonly storageAccountAccessType: string;
                };
            };
            readonly Network: {
                readonly ConnectivityMethod: {
                    /**
                     * The text "Enable public access from all networks"
                     */
                    readonly allNetworks: string;
                    /**
                     * The text "Disable public access and use private endpoints"
                     */
                    readonly private: string;
                    /**
                     * The text "Private with Internet Outbound and Private with Approved Outbound requires premium SKU container registry for having a private endpoint connection. You can create a new premium container registry or select an existing premium container registry in the Basics tab to use with this private workspace."
                     */
                    readonly skuPrivateEndpointErrorMessage: string;
                };
                readonly Hub: {
                    /**
                     * The text "Projects associated to an Azure AI hub share network, and can access resources in your virtual network without additional configuration. Choose the type of network isolation you need, from not isolated at all to an entirely separate virtual network managed by Azure Machine Learning."
                     */
                    readonly networkIsolationDescription: string;
                };
                readonly NetworkIsolation: {
                    readonly AllowInternetOutbound: {
                        /**
                         * The text "Allow Internet Outbound"
                         */
                        readonly title: string;
                    };
                    readonly AllowOnlyApprovedOutbound: {
                        /**
                         * The text "Allow Only Approved Outbound"
                         */
                        readonly title: string;
                    };
                    readonly Disabled: {
                        /**
                         * The text "Disabled"
                         */
                        readonly title: string;
                    };
                    readonly PrivateApprovedOutbound: {
                        /**
                         * The text "Workspace is accessed via private endpoint"
                         */
                        readonly descriptionItemOne: string;
                        /**
                         * The text "Outbound data movement is restricted to approved targets"
                         */
                        readonly descriptionItemThree: string;
                        /**
                         * The text "Compute can access allowlisted resources only"
                         */
                        readonly descriptionItemTwo: string;
                        /**
                         * The text "Learn more about data exfiltration protection"
                         */
                        readonly learnMore: string;
                        /**
                         * The text "Allow Only Approved Outbound"
                         */
                        readonly title: string;
                    };
                    readonly PrivateInternetOutbound: {
                        /**
                         * The text "Workspace is accessed via private endpoint"
                         */
                        readonly descriptionItemOne: string;
                        /**
                         * The text "Outbound data movement is unrestricted"
                         */
                        readonly descriptionItemThree: string;
                        /**
                         * The text "Compute can access private resources"
                         */
                        readonly descriptionItemTwo: string;
                        /**
                         * The text "Learn more about private networks"
                         */
                        readonly learnMore: string;
                        /**
                         * The text "Allow Internet Outbound"
                         */
                        readonly title: string;
                    };
                    readonly Public: {
                        /**
                         * The text "Workspace is accessed via public endpoint"
                         */
                        readonly descriptionItemOne: string;
                        /**
                         * The text "Outbound data movement is unrestricted"
                         */
                        readonly descriptionItemThree: string;
                        /**
                         * The text "Compute can access public resources"
                         */
                        readonly descriptionItemTwo: string;
                        /**
                         * The text "Learn more about public networks"
                         */
                        readonly learnMore: string;
                        /**
                         * The text "Public"
                         */
                        readonly title: string;
                    };
                    /**
                     * The text "Allow Internet Outbound and Allow Only Approved Outbound requires premium SKU container registry for having a private endpoint connection."
                     */
                    readonly skuErrorMessage: string;
                };
                readonly PrivateEndpoints: {
                    /**
                     * The text "Add"
                     */
                    readonly add: string;
                    /**
                     * The text "Click on add to create a private endpoint"
                     */
                    readonly clickToAdd: string;
                    /**
                     * The text "Workspace ({0}) and private endpoint connection ({1}) must be in the same location in order to get compute instance and clusters accessed properly in the virtual network."
                     */
                    readonly locationMismatch: string;
                    /**
                     * The text "Name"
                     */
                    readonly name: string;
                    /**
                     * The text "{0} ({1})"
                     */
                    readonly nameAndSubResource: string;
                    /**
                     * The text "{0} ({1}) ({2})"
                     */
                    readonly nameSubResourceAndDns: string;
                    /**
                     * The text "-"
                     */
                    readonly noContent: string;
                    /**
                     * The text "This is the specific sub-resource for the new registry that this private endpoint will be able to access."
                     */
                    readonly registrySubResourceHelp: string;
                    /**
                     * The text "Registry sub-resource"
                     */
                    readonly registrySubResourceLabel: string;
                    /**
                     * The text "This is the specific sub-resource for the new workspace that this private endpoint will be able to access."
                     */
                    readonly subResourceHelp: string;
                    /**
                     * The text "Workspace sub-resource"
                     */
                    readonly subResourceLabel: string;
                    /**
                     * The text "Private endpoints"
                     */
                    readonly title: string;
                    /**
                     * The text "Private Endpoint"
                     */
                    readonly type: string;
                };
                readonly SubnetDelegate: {
                    /**
                     * The text "For more information, see"
                     */
                    readonly learnMore: string;
                    /**
                     * The text "Follows different management boundaries than your hub and projects, injected into your Azure VNET."
                     */
                    readonly message: string;
                    /**
                     * The text "Delegate Azure virtual network subnet for agents"
                     */
                    readonly title: string;
                };
                readonly WorkspaceApprovedOutbound: {
                    readonly OutboundAccess: {
                        /**
                         * The text "Azure Firewall SKU"
                         */
                        readonly azureFirewallSku: string;
                        /**
                         * The text "Select from Basic or Standard SKU for the Azure Firewall deployment. For more information on Azure Firewall, see"
                         */
                        readonly azureFirewallSkuDescription: string;
                        /**
                         * The text "Pricing"
                         */
                        readonly azureFirewallSkuPricingText: string;
                        /**
                         * The text "There are several optional outbound targets recommended for your hub in scenarios such as VS Code, Prompt Flow, and more. You can modify or delete them."
                         */
                        readonly hubRecommendedTargetDescription: string;
                        /**
                         * The text "There are a few outbound targets added by Azure AI Foundry that are required for your workspace to access things like storage, key vault, and more."
                         */
                        readonly hubRequiredTargetDescription: string;
                        /**
                         * The text "There are several optional outbound targets recommended for your workspace in scenarios such as AutoML and Data Labeling. You can modify or delete them,"
                         */
                        readonly recommendedTargetDescription: string;
                        /**
                         * The text "Learn more about recommended targets"
                         */
                        readonly recommendedTargetLearnMore: string;
                        /**
                         * The text "There are a few outbound targets added by Azure Machine Learning that are required for your workspace to access things like storage,  notebooks, and deployment environments."
                         */
                        readonly requiredTargetDescription: string;
                        /**
                         * The text "Learn more about required targets"
                         */
                        readonly requiredTargetLearnMore: string;
                    };
                    readonly PrivateNetworkSettings: {
                        /**
                         * The text "Add private endpoints people can use to access your workspace, and manage the outbound targets to which the workspace can access"
                         */
                        readonly description: string;
                        /**
                         * The text "Private network and data exfiltration settings"
                         */
                        readonly title: string;
                    };
                };
                readonly WorkspaceInternetOutbound: {
                    readonly PrivateNetworkSettings: {
                        /**
                         * The text "Add private endpoints people can use to access your workspace, and choose how to manage outbound access from your workspace to things like Storage Accounts, Key Vaults and Registries."
                         */
                        readonly description: string;
                        /**
                         * The text "Private network settings"
                         */
                        readonly title: string;
                    };
                    /**
                     * The text "There are a few private endpoints required for your workspace to access private resources like storage. You can also add your additional private link targets here for your custom scenarios."
                     */
                    readonly targetDescription: string;
                    /**
                     * The text "Learn more about required private link target"
                     */
                    readonly targetLearnMoreLink: string;
                };
                readonly WorkspaceOutboundAccess: {
                    /**
                     * The text "The rule has been applied and effective."
                     */
                    readonly activeStatusMessage: string;
                    /**
                     * The text "Add user-defined outbound rules"
                     */
                    readonly addUserDefinedOutboundRules: string;
                    /**
                     * The text "Connection Name"
                     */
                    readonly connectionName: string;
                    /**
                     * The text "Delete user-defined outbound rules"
                     */
                    readonly deleteUserDefinedOutboundRules: string;
                    /**
                     * The text "Destination"
                     */
                    readonly destination: string;
                    /**
                     * The text "Destination Type"
                     */
                    readonly destinationType: string;
                    /**
                     * The text "Securing your workspace with a managed network provides network isolation for outbound access from the Hub and managed computes. Once you enable managed virtual network isolation of your Azure AI, you can't disable it."
                     */
                    readonly disabledManagedVnetMessage: string;
                    /**
                     * The text "Learn more"
                     */
                    readonly disabledManagedVnetMessageLearnMore: string;
                    /**
                     * The text "The rule will become active when managed network is provisioned, otherwise please check if approval is pending for the target resource."
                     */
                    readonly inactivePrivateEndpointStatusMessage: string;
                    /**
                     * The text "The rule will become active when managed network is provisioned."
                     */
                    readonly inactiveStatusMessage: string;
                    /**
                     * The text "Parent Rules"
                     */
                    readonly parentRules: string;
                    /**
                     * The text "Provision managed virtual network"
                     */
                    readonly provisionManagedVirtualNetwork: string;
                    /**
                     * The text "Managed virtual network will be provisioned at workspace creation. Charges will be incurred for network resources, such as private endpoint."
                     */
                    readonly provisionManagedVirtualNetworkInfo: string;
                    /**
                     * The text "Provision managed virtual network"
                     */
                    readonly provisionManagedVirtualNetworkSummaryTitle: string;
                    /**
                     * The text "Status"
                     */
                    readonly status: string;
                };
                readonly WorkspacePrivateOutbound: {
                    /**
                     * The text "You can also add your own outbound targets here for your custom scenarios."
                     */
                    readonly addUserDefinedOutboundRuleText: string;
                    /**
                     * The text "Dependency outbound rules"
                     */
                    readonly dependencyOutboundRules: string;
                    /**
                     * The text "Recommended outbound rules"
                     */
                    readonly recommendedOutboundRules: string;
                    /**
                     * The text "Required outbound rules"
                     */
                    readonly requiredOutboundRules: string;
                    /**
                     * The text "User-defined outbound rules"
                     */
                    readonly userDefinedOutboundRules: string;
                };
                /**
                 * The text "Azure Firewall SKU"
                 */
                readonly azureFirewallSku: string;
                /**
                 * The text "Connectivity method"
                 */
                readonly connectivityMethod: string;
                /**
                 * The text "Private DNS Zone"
                 */
                readonly displayPrivateDnsZone: string;
                /**
                 * The text "Region"
                 */
                readonly displayRegion: string;
                /**
                 * The text "Resource group"
                 */
                readonly displayResourceGroup: string;
                /**
                 * The text "Subnet"
                 */
                readonly displaySubnet: string;
                /**
                 * The text "Subscription"
                 */
                readonly displaySubscription: string;
                /**
                 * The text "Target resource type"
                 */
                readonly displayTargetResourceType: string;
                /**
                 * The text "Inbound Access"
                 */
                readonly inboundAccessTabTitle: string;
                /**
                 * The text "Choose the type of network isolation you need for your workspace, from not isolated at all to an entirely separate virtual network managed by Azure Machine Learning."
                 */
                readonly networkIsolationDescription: string;
                /**
                 * The text "Learn more about managed network isolation"
                 */
                readonly networkIsolationLearnMore: string;
                /**
                 * The text "Network isolation"
                 */
                readonly networkIsolationTitle: string;
                /**
                 * The text "Outbound Access"
                 */
                readonly outboundAccessTabTitle: string;
                /**
                 * The text "Missing required outbound access rules."
                 */
                readonly outboundRulesGridEmptyMessage: string;
                /**
                 * The text "Configure basic settings in order to create outbound rules."
                 */
                readonly outboundRulesNotLoadedMessage: string;
                /**
                 * The text "No required outbound rules while the outbound access to the public internet is enabled."
                 */
                readonly outboundRulesPublicInternetEnabledMessage: string;
                /**
                 * The text "You can connect to your workspace either publicly or privately using a private endpoint."
                 */
                readonly overviewDescription: string;
                /**
                 * The text "Network connectivity"
                 */
                readonly overviewTitle: string;
                /**
                 * The text "Create a private endpoint to allow a private connection to this resource."
                 */
                readonly privateEndpointDescription: string;
                /**
                 * The text "Private endpoint"
                 */
                readonly privateEndpointTitle: string;
                /**
                 * The text "You can connect to your registry either publicly or privately using a private endpoint."
                 */
                readonly registryOverviewDescription: string;
                /**
                 * The text "Networking"
                 */
                readonly title: string;
                /**
                 * The text "Workspace Inbound access"
                 */
                readonly workspaceInboundAccessTitle: string;
                /**
                 * The text "Workspace Outbound access"
                 */
                readonly workspaceOutboundAccessTitle: string;
            };
            readonly Workspace: {
                /**
                 * The text "Workspce type"
                 */
                readonly type: string;
            };
        };
        readonly CreateBladeHubAIServices: {
            /**
             * The text "Connect AI Services, including OpenAI"
             */
            readonly ariaLabel: string;
            /**
             * The text "Provider of Microsoft-maintained base models. Managed in your Azure subscription as a separate resource."
             */
            readonly info: string;
            /**
             * The text "Connect AI Services incl. OpenAI"
             */
            readonly label: string;
            /**
             * The text "Skip connecting AI services"
             */
            readonly skipText: string;
        };
        readonly CreateBladeHubConfigDefaultResourceGroup: {
            /**
             * The text "When a user does not specify a resource group for their project, this resource group will be used by default. The project creator gets granted an Azure RBAC owner role assignment on the project instance"
             */
            readonly info: string;
            /**
             * The text "Default project resource group"
             */
            readonly label: string;
            /**
             * The text "Same as hub resource group"
             */
            readonly sameAsHubLabel: string;
        };
        readonly CrossRegionComputeQuotas: {
            readonly QuotaConfigurationBlade: {
                /**
                 * The text "Enter at least 3 characters to search for workspaces..."
                 */
                readonly filterText: string;
                /**
                 * The text "No workspaces found to display."
                 */
                readonly noItemsText: string;
                /**
                 * The text "Quotas cannot be configured due to following error: {0}"
                 */
                readonly quotaConfigNotAvailable: string;
                /**
                 * The text "Configure your quota across your subscription here"
                 */
                readonly subTitle: string;
                /**
                 * The text "Configure quota"
                 */
                readonly title: string;
                /**
                 * The text "You can only configure quota for workspaces which have been configured before since the list of workspaces of this subscription cannot be reached due to following error: {0}"
                 */
                readonly workspacesNotAvailable: string;
            };
            readonly QuotaUsageBlade: {
                /**
                 * The text "Cross location workspace information (whether the workspace owning a compute is in a different location than the selected location) cannot be displayed due to following error but you can still see all the usages: {0}"
                 */
                readonly crossLocationUsagesNotAvailable: string;
                /**
                 * The text "Learn more"
                 */
                readonly quotaInfoLearnMore: string;
                /**
                 * The text "Azure Machine Learning quota is preconfigured limits, which specifies the maximum number of cores you can use at any given moment."
                 */
                readonly quotaInfoText: string;
                /**
                 * The text "Compute usages cannot be displayed do to following error: {0}"
                 */
                readonly usagesNotAvailable: string;
            };
            readonly UsageGrid: {
                /**
                 * The text "Cores utilization"
                 */
                readonly coreUtilizationColumnText: string;
                /**
                 * The text "{0} of {1} cores utilized"
                 */
                readonly coresUsageTextFormat: string;
                /**
                 * The text "Enable cross location compute toggle"
                 */
                readonly crossLocationEnableText: string;
                /**
                 * The text "{0} cores"
                 */
                readonly crossLocationWarningCoresFormat: string;
                /**
                 * The text "You are using {0} in other locations."
                 */
                readonly crossLocationWarningLine1: string;
                /**
                 * The text "{1} to view them."
                 */
                readonly crossLocationWarningLine2: string;
                /**
                 * The text "Standard {0} Family vCPUs"
                 */
                readonly familyNameFormat: string;
                /**
                 * The text "{0} out of {1} ({2})"
                 */
                readonly totalCoresTextFormat: string;
                /**
                 * The text "Usage"
                 */
                readonly usageColumnText: string;
                /**
                 * The text "Usage percentage"
                 */
                readonly usagePercentageColumnText: string;
                /**
                 * The text "VM Families"
                 */
                readonly vmFamiliesColumnText: string;
                /**
                 * The text "{0} ({1}, {2})"
                 */
                readonly workspaceCrossLocationDisplayText: string;
                /**
                 * The text "{0} ({1})"
                 */
                readonly workspaceDisplayText: string;
                /**
                 * The text "Workspaces"
                 */
                readonly workspacesColumnText: string;
            };
            /**
             * The text "Configure quota"
             */
            readonly configureQuotaButtonText: string;
            /**
             * The text "Dedicated cores usage"
             */
            readonly dedicatedCoresSectionLabel: string;
            /**
             * The text "Loading..."
             */
            readonly loadingText: string;
            /**
             * The text "Low priority cores usage"
             */
            readonly lowPriorityCoresSectionLabel: string;
            /**
             * The text "Please note that the number of low-priority cores per subscription is single value accross VM families."
             */
            readonly lowPriorityUsageInfoText: string;
            /**
             * The text "Quota update failed"
             */
            readonly quotaUpdateFailed: string;
            /**
             * The text "Quota successfully updated"
             */
            readonly quotaUpdateSucceeded: string;
            /**
             * The text "{0} Series"
             */
            readonly requestBladeFamilyNameFormat: string;
            /**
             * The text "Request quota increase"
             */
            readonly requestQuotaButtonText: string;
            /**
             * The text "Subscription view"
             */
            readonly subscriptionViewText: string;
            /**
             * The text "Show workspaces across all locations (cross-location compute)"
             */
            readonly toggleText: string;
            /**
             * The text "Cluster quota:"
             */
            readonly totalClustersLabel: string;
            /**
             * The text "{0} clusters and CIs used | {1} remaining"
             */
            readonly totalClustersTextFormat: string;
            /**
             * The text "{0} cores used | {1} cores remaining"
             */
            readonly totalCoresTextFormat: string;
            /**
             * The text "Dedicated quota:"
             */
            readonly totalDedicatedCoresLabel: string;
            /**
             * The text "Low priority quota:"
             */
            readonly totalLowPriorityCoresLabel: string;
            /**
             * The text "Workspace view"
             */
            readonly workspaceViewText: string;
        };
        readonly DeleteHubBlade: {
            readonly AssociatedResourcesListColumn: {
                /**
                 * The text "Name"
                 */
                readonly name: string;
                /**
                 * The text "Resource Group"
                 */
                readonly resourceGroup: string;
                /**
                 * The text "Type"
                 */
                readonly type: string;
            };
            readonly Buttons: {
                /**
                 * The text "Cancel"
                 */
                readonly cancel: string;
                /**
                 * The text "Delete"
                 */
                readonly delete: string;
            };
            readonly ConfirmDeleteSection: {
                readonly ValidationErrorMessage: {
                    /**
                     * The text "The value must not be empty"
                     */
                    readonly emptyString: string;
                    /**
                     * The text "Name does not match"
                     */
                    readonly nameMismatch: string;
                };
                /**
                 * The text "Confirm delete"
                 */
                readonly label: string;
                /**
                 * The text "Type the resource name"
                 */
                readonly placeholder: string;
            };
            readonly Description: {
                /**
                 * The text "deployed model(s)"
                 */
                readonly deployment: string;
                /**
                 * The text "Hub to be deleted: "
                 */
                readonly hubName: string;
                /**
                 * The text "project(s)"
                 */
                readonly project: string;
                /**
                 * The text "Your hub with {0} and {1} will be permanently deleted."
                 */
                readonly section1: string;
                /**
                 * The text "Connected resources may still store hub data and are not automatically deleted. If you delete these resources you may break other existing Azure deployments."
                 */
                readonly section2: string;
                /**
                 * The text "Choose connected resources you'd like to additionally delete:"
                 */
                readonly section3: string;
            };
            readonly ProjectListColumn: {
                /**
                 * The text "Deployment"
                 */
                readonly deployment: string;
                /**
                 * The text "Project"
                 */
                readonly project: string;
            };
            readonly RBAC: {
                /**
                 * The text "You are missing Azure RBAC delete permission on one or more connected resources. Delete these resources later from Azure portal."
                 */
                readonly message: string;
            };
            readonly ResourceType: {
                /**
                 * The text "AI Services"
                 */
                readonly aiServices: string;
                /**
                 * The text "Application Insights"
                 */
                readonly applicationInsights: string;
                /**
                 * The text "Cognitive Service"
                 */
                readonly cognitiveService: string;
                /**
                 * The text "Computer Vision"
                 */
                readonly computerVision: string;
                /**
                 * The text "Container Registry"
                 */
                readonly containerRegistry: string;
                /**
                 * The text "Content Safety"
                 */
                readonly contentSafety: string;
                /**
                 * The text "Document Intelligence"
                 */
                readonly formRecognizer: string;
                /**
                 * The text "Key Vault"
                 */
                readonly keyVault: string;
                /**
                 * The text "Open AI"
                 */
                readonly openAI: string;
                /**
                 * The text "Search Service"
                 */
                readonly searchService: string;
                /**
                 * The text "Speech Services"
                 */
                readonly speechServices: string;
                /**
                 * The text "Storage Account"
                 */
                readonly storageAccount: string;
                /**
                 * The text "Language"
                 */
                readonly textAnalytics: string;
                /**
                 * The text "Translator"
                 */
                readonly textTranslation: string;
            };
            /**
             * The text "Delete resource"
             */
            readonly title: string;
        };
        readonly DeleteHubNotification: {
            readonly Cancel: {
                /**
                 * The text "Resource deletion canceled"
                 */
                readonly title: string;
            };
            readonly Error: {
                /**
                 * The text "An error occurred while deleting hub '{workspaceName}' and its associated resources."
                 */
                readonly description: string;
                /**
                 * The text "Resource deletion error"
                 */
                readonly title: string;
            };
            readonly InProgress: {
                /**
                 * The text "Hub '{workspaceName}' and its associated resources deletion in progress..."
                 */
                readonly description: string;
                /**
                 * The text "Deleting resource..."
                 */
                readonly title: string;
            };
            readonly Success: {
                /**
                 * The text "Hub '{workspaceName}' and its associated resources were deleted successfully."
                 */
                readonly description: string;
                /**
                 * The text "Successfully deleted"
                 */
                readonly title: string;
            };
        };
        readonly DeleteHubStatusBlade: {
            readonly ResourceStatusListColumn: {
                /**
                 * The text "Resource"
                 */
                readonly resource: string;
                /**
                 * The text "Status"
                 */
                readonly status: string;
            };
            /**
             * The text "Delete resource status"
             */
            readonly title: string;
        };
        readonly Description: {
            /**
             * The text "Centrally configure Azure AI hubs"
             */
            readonly createAzureMachineLearningHubResource: string;
            /**
             * The text "For sharing ML assets across workspaces"
             */
            readonly createAzureMachineLearningRegistryResource: string;
            /**
             * The text "For ML projects and teams"
             */
            readonly createAzureMachineLearningResource: string;
        };
        readonly EncryptionSettings: {
            readonly Command: {
                /**
                 * The text "Discard"
                 */
                readonly discard: string;
                /**
                 * The text "Close"
                 */
                readonly dismissAriaLabel: string;
                /**
                 * The text "Error updating key"
                 */
                readonly resultError: string;
                /**
                 * The text "Updating..."
                 */
                readonly resultLoading: string;
                /**
                 * The text "Success"
                 */
                readonly resultSuccess: string;
                /**
                 * The text "Save"
                 */
                readonly save: string;
                /**
                 * The text "See more"
                 */
                readonly seeMore: string;
            };
            readonly Header1: {
                /**
                 * The text "data encryption"
                 */
                readonly replacement1: string;
                /**
                 * The text "customer-managed key encryption"
                 */
                readonly replacement2: string;
                /**
                 * The text "Your data is encrypted by default using Microsoft-managed keys. For additional control over your data, you may choose to bring your own key for encryption. To learn more, see {} and {}."
                 */
                readonly template: string;
            };
            readonly SelectedKey: {
                /**
                 * The text "Key"
                 */
                readonly keyLabel: string;
                /**
                 * The text "Selected key vault and key"
                 */
                readonly title: string;
                /**
                 * The text "KeyVault"
                 */
                readonly vaultLabel: string;
                /**
                 * The text "Version"
                 */
                readonly versionLabel: string;
            };
            readonly SelectionSection: {
                /**
                 * The text "Encryption selection"
                 */
                readonly title: string;
                /**
                 * The text "Encryption type"
                 */
                readonly typeLabel: string;
                /**
                 * The text "Key vault and key"
                 */
                readonly vaultAndKeyLabel: string;
                /**
                 * The text "Select a key vault and key"
                 */
                readonly vaultAndKeySelect: string;
            };
            /**
             * The text "You have unsaved changes."
             */
            readonly dirtyFormWarning: string;
            /**
             * The text "After a workspace is deployed, you can rotate the encryption key but not change the encryption type from Microsoft-managed key to Customer-managed key."
             */
            readonly header2: string;
            /**
             * The text "Encryption"
             */
            readonly title: string;
        };
        readonly EncryptionTab: {
            /**
             * The text "Encryption"
             */
            readonly title: string;
        };
        readonly Hub: {
            readonly Projects: {
                readonly Toolbar: {
                    /**
                     * The text "
      Add
    "
                     */
                    readonly add: string;
                    /**
                     * The text "
      Refresh
    "
                     */
                    readonly refresh: string;
                };
                /**
                 * The text "
      Associated projects of Azure AI hub with ID "{0}" could not be loaded.
    "
                 */
                readonly loadFailure: string;
                /**
                 * The text "
      Projects
    "
                 */
                readonly title: string;
            };
        };
        readonly HubConfigDefaultResourceGroup: {
            readonly SummaryTab: {
                /**
                 * The text "Default project resource group"
                 */
                readonly label: string;
            };
        };
        readonly IdentityTab: {
            /**
             * The text "Identity"
             */
            readonly title: string;
        };
        readonly Keyvault: {
            readonly Dropdown: {
                readonly Hub: {
                    /**
                     * The text "A key vault is used to store secrets and other sensitive information that is needed by the AI hub. You may create a new Azure Key Vault resource or select an existing one in your subscription."
                     */
                    readonly info: string;
                };
                readonly SettingsBlade: {
                    readonly Name: {
                        /**
                         * The text "The name must meet the following requirements:"
                         */
                        readonly infoDescription: string;
                        /**
                         * The text "Unique across all existing key vaults in Azure"
                         */
                        readonly infoItem1: string;
                        /**
                         * The text "Between 3 and 24 characters long"
                         */
                        readonly infoItem2: string;
                        /**
                         * The text "Only contain alphanumeric characters and hyphens"
                         */
                        readonly infoItem3: string;
                        /**
                         * The text "Cannot start with a number"
                         */
                        readonly infoItem4: string;
                    };
                    /**
                     * The text "Vault name must be between 3-24 alphanumeric characters. The name must begin with a letter, end with a letter or digit, and not contain consecutive hyphens."
                     */
                    readonly nameInvalidMessage: string;
                    /**
                     * The text "Name"
                     */
                    readonly nameLabel: string;
                    /**
                     * The text "Create new key vault"
                     */
                    readonly title: string;
                };
                /**
                 * The text "Create new key vault"
                 */
                readonly createNewAriaLabel: string;
                /**
                 * The text "A key vault is used to store secrets and other sensitive information that is needed by the workspace. You may create a new Azure Key Vault resource or select an existing one in your subscription."
                 */
                readonly info: string;
                /**
                 * The text "Key vault"
                 */
                readonly label: string;
            };
            readonly RadioButton: {
                readonly ManagedKeyVault: {
                    /**
                     * The text "Preview: secrets are stored in Microsoft-managed credential store. Secret data lifecycle follows your hub, projects, connections and compute."
                     */
                    readonly infoIcon: string;
                };
                /**
                 * The text "Azure key vault"
                 */
                readonly byoKeyVault: string;
                /**
                 * The text "Credential store"
                 */
                readonly label: string;
                /**
                 * The text "Microsoft-managed (preview)"
                 */
                readonly managedKeyVault: string;
            };
            readonly SummaryTab: {
                /**
                 * The text "Key vault"
                 */
                readonly label: string;
            };
        };
        readonly Keyword: {
            /**
             * The text "Alert"
             */
            readonly alert: string;
            /**
             * The text "Audit"
             */
            readonly audit: string;
            /**
             * The text "Instance"
             */
            readonly instance: string;
            /**
             * The text "Log"
             */
            readonly log: string;
            /**
             * The text "Rules"
             */
            readonly rules: string;
            /**
             * The text "Scale"
             */
            readonly scale: string;
            /**
             * The text "Scaling"
             */
            readonly scaling: string;
        };
        readonly MLAppDeployments: {
            readonly Grid: {
                readonly StatusFilter: {
                    /**
                     * The text "All provisioning states"
                     */
                    readonly all: string;
                    /**
                     * The text "{0} ({1})"
                     */
                    readonly itemFormat: string;
                    /**
                     * The text "{0} connection states selected"
                     */
                    readonly some: string;
                };
            };
            readonly GridColumns: {
                /**
                 * The text "Deployment name"
                 */
                readonly name: string;
                /**
                 * The text "Provisioning state"
                 */
                readonly status: string;
                /**
                 * The text "Traffic %"
                 */
                readonly traffic: string;
            };
        };
        readonly ManagedIdentities: {
            /**
             * The text "Identity"
             */
            readonly menuText: string;
        };
        readonly Network: {
            readonly NetworkIsolation: {
                /**
                 * The text "An error occurred while saving workspace managed outbound access settings. Please try again or contact support if the issue persists."
                 */
                readonly errorWorkspaceManagedOutboundAccessNotificationDescription: string;
                /**
                 * The text "Failed to Save Workspace Managed Outbound Access Settings"
                 */
                readonly errorWorkspaceManagedOutboundAccessNotificationTitle: string;
                /**
                 * The text "Your changes to workspace managed outbound access settings are being saved."
                 */
                readonly inProgressWorkspaceManagedOutboundAccessNotificationDescription: string;
                /**
                 * The text "Saving Workspace Managed Outbound Access Settings"
                 */
                readonly inProgressWorkspaceManagedOutboundAccessNotificationTitle: string;
                /**
                 * The text "Your changes to workspace managed outbound access settings have been successfully saved."
                 */
                readonly successWorkspaceManagedOutboundAccessNotificationDescription: string;
                /**
                 * The text "Workspace Managed Outbound Access Settings Saved"
                 */
                readonly successWorkspaceManagedOutboundAccessNotificationTitle: string;
            };
        };
        readonly Networking: {
            readonly PrivateEndpointConnections: {
                /**
                 * The text "Private endpoint connections"
                 */
                readonly tabText: string;
            };
            readonly PublicAccess: {
                readonly FirewallValidation: {
                    /**
                     * The text "Two identical address ranges have been specified. Address ranges must be unique."
                     */
                    readonly duplicateIpAddresses: string;
                    /**
                     * The text "The subnet prefix must be equal to {0}."
                     */
                    readonly exactPrefix: string;
                    /**
                     * The text "The prefix must be between {0} and {1}."
                     */
                    readonly formattedPrefix: string;
                    /**
                     * The text "{0} is not a valid CIDR block. Use {1}/{2} instead."
                     */
                    readonly invalidCIDRBlockWithSuggestion: string;
                    /**
                     * The text "Specify an IP address or CIDR."
                     */
                    readonly invalidCidr: string;
                    /**
                     * The text "Invalid IP address."
                     */
                    readonly invalidIPAddress: string;
                    /**
                     * The text "The octet '{0}' in IP address '{1}' contains a leading zero."
                     */
                    readonly leadingZerosIpAddress: string;
                    /**
                     * The text "Malformed address range. Address was {0}."
                     */
                    readonly malformedSubnet: string;
                    /**
                     * The text "The prefix must be smaller than or equal to {0}."
                     */
                    readonly maxPrefix: string;
                    /**
                     * The text "The prefix must be greater than or equal to {0}."
                     */
                    readonly minPrefix: string;
                    /**
                     * The text "A non-null address range is required."
                     */
                    readonly nonNullSubnet: string;
                    /**
                     * The text "Octet {0} with value {1} is invalid. It must be between {2} and {3}."
                     */
                    readonly octet: string;
                    /**
                     * The text "IP rules support public IP addresses only."
                     */
                    readonly publicIpRuleValidation: string;
                };
                readonly Toolbar: {
                    /**
                     * The text "Discard changes"
                     */
                    readonly discardChangesText: string;
                    /**
                     * The text "Refresh"
                     */
                    readonly refreshText: string;
                    /**
                     * The text "Save"
                     */
                    readonly saveText: string;
                    /**
                     * The text "Saving..."
                     */
                    readonly savingText: string;
                };
                /**
                 * The text "You may not wish to add your client IP address if the network you are using the azure portal from is atypical (home vs. work environment for example)."
                 */
                readonly addClientIpAddressInfo: string;
                /**
                 * The text "Add your client IP address ('{0}')"
                 */
                readonly addClientIpAddressLabel: string;
                /**
                 * The text "Address range"
                 */
                readonly addressRange: string;
                /**
                 * The text "IP address or CIDR"
                 */
                readonly addressRangePlaceHolder: string;
                /**
                 * The text "All networks, including the internet, can access this resource."
                 */
                readonly allNetworksInfoText: string;
                /**
                 * The text "Public network access allows access to this resource through the internet using a public IP address. An application or resource that is granted access with the following network rules still requires proper authorization to access this resource. {0}"
                 */
                readonly description: string;
                /**
                 * The text "No public network can access this resource."
                 */
                readonly disabledInfoText: string;
                /**
                 * The text "Allow access from public IP you specified below."
                 */
                readonly enabledFromSelectedIpInfoText: string;
                /**
                 * The text "An error occurred while saving public access settings. Please try again or contact support if the issue persists."
                 */
                readonly failurePublicAccesNetworkNotificationDescription: string;
                /**
                 * The text "Failed to Save Public Access Settings"
                 */
                readonly failurePublicAccesNetworkNotificationTitle: string;
                /**
                 * The text "Add IP ranges to allow access from the internet or your on-premises networks."
                 */
                readonly firewallDescription: string;
                /**
                 * The text "Firewall"
                 */
                readonly firewallHeader: string;
                /**
                 * The text "Learn more"
                 */
                readonly firewallLearnMore: string;
                /**
                 * The text "Your changes to public access settings are being saved."
                 */
                readonly inprogressPublicAccesNetworkNotificationDescription: string;
                /**
                 * The text "Saving Public Access Settings"
                 */
                readonly inprogressPublicAccesNetworkNotificationTitle: string;
                /**
                 * The text "Learn more"
                 */
                readonly learnMoreText: string;
                /**
                 * The text "All networks"
                 */
                readonly radioAllNetworksText: string;
                /**
                 * The text "Disabled"
                 */
                readonly radioDisabledText: string;
                /**
                 * The text "Enabled from all networks"
                 */
                readonly radioEnabledFromAllNetworks: string;
                /**
                 * The text "Enabled from selected IP addresses"
                 */
                readonly radioEnabledFromSelectedIp: string;
                /**
                 * The text "Public network access"
                 */
                readonly radioLabel: string;
                /**
                 * The text "Your changes to public access settings have been successfully saved."
                 */
                readonly successPublicAccesNetworkNotificationDescription: string;
                /**
                 * The text "Public Access Settings Saved"
                 */
                readonly successPublicAccesNetworkNotificationTitle: string;
                /**
                 * The text "Public access"
                 */
                readonly tabText: string;
            };
            readonly WorkspaceManagedOutboundAccess: {
                /**
                 * The text "Workspace managed outbound access"
                 */
                readonly tabText: string;
            };
        };
        readonly OnlineEndpoints: {
            readonly Create: {
                readonly Dependencies: {
                    /**
                     * The text "Conda dependencies file"
                     */
                    readonly condaDependenciesFileLabel: string;
                    /**
                     * The text "Customize your model deployment into inference app."
                     */
                    readonly description: string;
                    /**
                     * The text "Entry script file"
                     */
                    readonly entryScriptFileLabel: string;
                    /**
                     * The text "Select a conda dependency file"
                     */
                    readonly selectCondaDependenciesFile: string;
                    /**
                     * The text "Select an entry script file"
                     */
                    readonly selectEntryScriptFileMessage: string;
                    /**
                     * The text "Dependencies"
                     */
                    readonly tabName: string;
                };
                /**
                 * The text "Azure Machine Learning Inference apps enable you to quickly build, deploy and scale enterprise-grade machine learning models running on any platform. Use any open source machine learning framework like TensorFlow, PyTorch, SciKit-Learn, ONNX and more. Use our “no code deployment” to accelerate your productivity or customize your Inference app with your own docker container and/or model scoring code."
                 */
                readonly introText: string;
                /**
                 * The text "Model"
                 */
                readonly modelLabel: string;
                /**
                 * The text "Select a model"
                 */
                readonly modelPlaceholder: string;
                /**
                 * The text "Model version"
                 */
                readonly modelVersionLabel: string;
                /**
                 * The text "Select a model version"
                 */
                readonly modelVersionPlaceholder: string;
                /**
                 * The text "Name"
                 */
                readonly nameLabel: string;
                /**
                 * The text "Create an ML App"
                 */
                readonly subTitle: string;
                /**
                 * The text "Machine learning online endpoint"
                 */
                readonly title: string;
                /**
                 * The text "Workspace"
                 */
                readonly workspaceLabel: string;
                /**
                 * The text "Select a workspace"
                 */
                readonly workspacePlaceholder: string;
            };
        };
        readonly OutboundAccessRule: {
            readonly SettingsBlade: {
                readonly AppGateway: {
                    readonly SubResource: {
                        /**
                         * The text "No private link config is found in resource. Set it up before creating PE outbound rule."
                         */
                        readonly errorMessage: string;
                    };
                };
                readonly FqdnCostInfo: {
                    /**
                     * The text "Managed Network Isolation."
                     */
                    readonly managedNetworkIsolationLinkText: string;
                    /**
                     * The text "For more information on Azure Firewall, see "
                     */
                    readonly moreInformationText: string;
                    /**
                     * The text "Pricing."
                     */
                    readonly pricingLinkText: string;
                    /**
                     * The text "FQDN outbound rules are implemented using Azure Firewall. If you use outbound FQDN rules, charges for Azure Firewall are included in your billing. To learn more about outbound rules, see "
                     */
                    readonly text: string;
                };
                readonly FqdnValidation: {
                    /**
                     * The text "FQDN is required."
                     */
                    readonly applicationGatewayFqdnRequired: string;
                    /**
                     * The text "Domain label should not be empty."
                     */
                    readonly emptyLabelMessage: string;
                    /**
                     * The text "Specify a valid FQDN with at least three labels for Application Gateway access."
                     */
                    readonly invalidFormatMessage: string;
                };
                /**
                 * The text "Dependency outbound rules will be created for all dependency resources under AzureML registry. View these rules under Dependency outbound rules."
                 */
                readonly amlRegistryPEDependencyRulesWarning: string;
                /**
                 * The text "FQDNs resolve to the private IP of the Application Gateway private endpoint inside the workspace's managed network. FQDNs are editable as needed. Please save after editing the fields."
                 */
                readonly applicationGatewayFqdnInfoBallon: string;
                /**
                 * The text "Type of the outbound destination, FQDN, Private Endpoint, Service Tag."
                 */
                readonly destinationTypeBalloonContent: string;
                /**
                 * The text "Destination type"
                 */
                readonly destinationTypeLabel: string;
                /**
                 * The text "Fully Qualified Domain Name to allow for outbound traffic."
                 */
                readonly fqdnDestinationBalloonContent: string;
                /**
                 * The text "FQDN destination"
                 */
                readonly fqdnDestinationLabel: string;
                /**
                 * The text "FQDNs"
                 */
                readonly fqdnsLabel: string;
                /**
                 * The text "Note"
                 */
                readonly note: string;
                /**
                 * The text "Provide a single port, such as 80; a port range, such as 1024-655535; or a comma-separated list of single ports and/or port ranges, such as 80,1024-655535. This specifies on which ports traffic will be allowed by this rule. Provide an asterisk(*) to allow traffic on any port."
                 */
                readonly portRangeBalloonContent: string;
                /**
                 * The text "Port ranges"
                 */
                readonly portRangeLabel: string;
                /**
                 * The text "Network protocol to allow, TCP, UDP, ICMP or Any"
                 */
                readonly protocolBalloonContent: string;
                /**
                 * The text "Potocol"
                 */
                readonly protocolLabel: string;
                /**
                 * The text "Resource group containing the target resource for the private endpoint."
                 */
                readonly resourceGroupBalloonContent: string;
                /**
                 * The text "Resource group"
                 */
                readonly resourceGroupLabel: string;
                /**
                 * The text "Name of the target resource for the private endpoint."
                 */
                readonly resourceNameBalloonContent: string;
                /**
                 * The text "Resource name"
                 */
                readonly resourceNameLabel: string;
                /**
                 * The text "Type of the Azure resource that supports Private Link."
                 */
                readonly resourceTypeBalloonContent: string;
                /**
                 * The text "Resource type"
                 */
                readonly resourceTypeLabel: string;
                /**
                 * The text "Rule name already exists"
                 */
                readonly ruleNameAlreadyExists: string;
                /**
                 * The text "Name of the outbound rule that is unique in the workspace."
                 */
                readonly ruleNameBalloonContent: string;
                /**
                 * The text "Rule name"
                 */
                readonly ruleNameLabel: string;
                /**
                 * The text "Predefined identifiers that represent a category of IP addresses."
                 */
                readonly serviceTagBalloonContent: string;
                /**
                 * The text "Service tag"
                 */
                readonly serviceTagLabel: string;
                /**
                 * The text "Check to enable an additional private endpoint to be used by jobs running on Spark."
                 */
                readonly sparkEnabledBalloonContent: string;
                /**
                 * The text "Spark enabled"
                 */
                readonly sparkEnabledLabel: string;
                /**
                 * The text "Indicates whether managed network is provisioned for Spark jobs."
                 */
                readonly sparkStatusBalloonContent: string;
                /**
                 * The text "Spark status"
                 */
                readonly sparkStatusLabel: string;
                /**
                 * The text "Status is read-only, when Active, indicates the managed network is provisioned and ready. When Inactive, indicates it has not provisioned."
                 */
                readonly statusBalloonContent: string;
                /**
                 * The text "Status"
                 */
                readonly statusLabel: string;
                /**
                 * The text "Sub resource to connect for the private endpoint."
                 */
                readonly subResourceBalloonContent: string;
                /**
                 * The text "Sub resource"
                 */
                readonly subResourceLabel: string;
                /**
                 * The text "Subscription containing the target resource for the private endpoint."
                 */
                readonly subscriptionBalloonContent: string;
                /**
                 * The text "Subscription"
                 */
                readonly subscriptionLabel: string;
                /**
                 * The text "Workspace outbound rules"
                 */
                readonly title: string;
            };
        };
        readonly Overview: {
            readonly Banner: {
                readonly Description: {
                    /**
                     * The text "The Azure Machine Learning Studio is a web app where you can build, train, test, and deploy ML models. Launch it now to start exploring, or {}"
                     */
                    readonly label: string;
                    /**
                     * The text "learn more about the Azure Machine Learning studio"
                     */
                    readonly learnMoreText: string;
                };
                readonly Registry: {
                    /**
                     * The text "Launch registry in studio"
                     */
                    readonly launchButtonText: string;
                    /**
                     * The text "Work with your registry in Azure Machine Learning Studio"
                     */
                    readonly title: string;
                };
                /**
                 * The text "Launch studio"
                 */
                readonly launchButtonText: string;
                /**
                 * The text "Work with your models in Azure Machine Learning Studio"
                 */
                readonly title: string;
            };
            readonly Essentials: {
                /**
                 * The text "edit"
                 */
                readonly edit: string;
                /**
                 * The text "Managed resource group"
                 */
                readonly managedResourceGroup: string;
                /**
                 * The text "MLFlow tracking URI"
                 */
                readonly mlFlowWebURLText: string;
                /**
                 * The text "Project ID"
                 */
                readonly projectID: string;
                /**
                 * The text "Registry web URL"
                 */
                readonly registryWebURLText: string;
                /**
                 * The text "Studio web URL"
                 */
                readonly studioWebURLText: string;
            };
        };
        readonly PrivateEndpoints: {
            readonly Commands: {
                readonly Approve: {
                    /**
                     * The text "Do you want to approve the {0} selected connections?"
                     */
                    readonly messagePlural: string;
                    /**
                     * The text "Do you want to approve the connection '{0}'?"
                     */
                    readonly messageSingular: string;
                    /**
                     * The text "Approve connection"
                     */
                    readonly title: string;
                };
                readonly ApproveNotifications: {
                    readonly Failure: {
                        /**
                         * The text "Failed to approve {0} out of {1} private endpoint connections"
                         */
                        readonly message: string;
                        /**
                         * The text "Failed to approve private endpoint connections"
                         */
                        readonly title: string;
                    };
                    readonly InProgress: {
                        /**
                         * The text "Approving {0} private endpoint connections"
                         */
                        readonly message: string;
                        /**
                         * The text "Approving private endpoint connections..."
                         */
                        readonly title: string;
                    };
                    readonly Success: {
                        /**
                         * The text "Successfully approved {0} private endpoint connections."
                         */
                        readonly message: string;
                        /**
                         * The text "Successfully approved private endpoint connections"
                         */
                        readonly title: string;
                    };
                };
                readonly DeleteNotifications: {
                    readonly Failure: {
                        /**
                         * The text "Failed to delete {0} out of {1} private endpoint connections"
                         */
                        readonly message: string;
                        /**
                         * The text "Failed to delete private endpoint connections"
                         */
                        readonly title: string;
                    };
                    readonly InProgress: {
                        /**
                         * The text "Deleting {0} private endpoint connections"
                         */
                        readonly message: string;
                        /**
                         * The text "Deleting private endpoint connections..."
                         */
                        readonly title: string;
                    };
                    readonly Success: {
                        /**
                         * The text "Successfully deleted {0} private endpoint connections."
                         */
                        readonly message: string;
                        /**
                         * The text "Successfully deleted private endpoint connections"
                         */
                        readonly title: string;
                    };
                };
                readonly Reject: {
                    /**
                     * The text "Do you want to reject the {0} selected connections?"
                     */
                    readonly messagePlural: string;
                    /**
                     * The text "Do you want to reject the connection '{0}'?"
                     */
                    readonly messageSingular: string;
                    /**
                     * The text "Reject connection"
                     */
                    readonly title: string;
                };
                readonly RejectNotifications: {
                    readonly Failure: {
                        /**
                         * The text "Failed to reject {0} out of {1} private endpoint connections"
                         */
                        readonly message: string;
                        /**
                         * The text "Failed to reject private endpoint connections"
                         */
                        readonly title: string;
                    };
                    readonly InProgress: {
                        /**
                         * The text "Rejecting {0} private endpoint connections"
                         */
                        readonly message: string;
                        /**
                         * The text "Rejecting private endpoint connections..."
                         */
                        readonly title: string;
                    };
                    readonly Success: {
                        /**
                         * The text "Successfully rejected {0} private endpoint connections."
                         */
                        readonly message: string;
                        /**
                         * The text "Successfully rejected private endpoint connections"
                         */
                        readonly title: string;
                    };
                };
                readonly Remove: {
                    /**
                     * The text "Do you want to delete the {0} selected connections?"
                     */
                    readonly messagePlural: string;
                    /**
                     * The text "Do you want to delete the connection '{0}'?"
                     */
                    readonly messageSingular: string;
                    /**
                     * The text "Delete connection"
                     */
                    readonly title: string;
                };
                /**
                 * The text "Private endpoint"
                 */
                readonly addPrivateEndpoint: string;
                /**
                 * The text "Approve"
                 */
                readonly approve: string;
                /**
                 * The text "Description"
                 */
                readonly description: string;
                /**
                 * The text "Reject"
                 */
                readonly reject: string;
                /**
                 * The text "Remove"
                 */
                readonly remove: string;
            };
            readonly Create: {
                /**
                 * The text "{0}: {1}"
                 */
                readonly validationErrorFormat: string;
            };
            readonly Grid: {
                readonly StatusFilter: {
                    /**
                     * The text "All connection states"
                     */
                    readonly all: string;
                    /**
                     * The text "{0} ({1})"
                     */
                    readonly itemFormat: string;
                    /**
                     * The text "{0} connection states selected"
                     */
                    readonly some: string;
                };
            };
            readonly GridColumns: {
                /**
                 * The text "Description"
                 */
                readonly description: string;
                /**
                 * The text "Connection name"
                 */
                readonly name: string;
                /**
                 * The text "Private endpoint"
                 */
                readonly privateEndpoint: string;
                /**
                 * The text "Connection state"
                 */
                readonly status: string;
            };
            readonly Status: {
                /**
                 * The text "Approved"
                 */
                readonly approved: string;
                /**
                 * The text "Disconnected"
                 */
                readonly disconnected: string;
                /**
                 * The text "Pending"
                 */
                readonly pending: string;
                /**
                 * The text "Rejected"
                 */
                readonly rejected: string;
            };
            readonly Toolbar: {
                /**
                 * The text "Refresh"
                 */
                readonly refresh: string;
            };
            /**
             * The text "Filter by name..."
             */
            readonly filterByName: string;
            /**
             * The text "Filter by connection state..."
             */
            readonly filterByStatus: string;
            /**
             * The text "There was an error processing your request. Try again in a few moments."
             */
            readonly genericErrorMessage: string;
            /**
             * The text "Private endpoint connections"
             */
            readonly label: string;
            /**
             * The text "Networking"
             */
            readonly menuText: string;
        };
        readonly Properties: {
            /**
             * The text "Discard changes"
             */
            readonly discard: string;
            /**
             * The text "Refresh"
             */
            readonly refresh: string;
            /**
             * The text "Save"
             */
            readonly save: string;
            /**
             * The text "Saving..."
             */
            readonly saving: string;
        };
        readonly Quota: {
            readonly Link: {
                /**
                 * The text "View quota by subscription and region, and request quota directly from the studio."
                 */
                readonly bladeDescription: string;
                /**
                 * The text "Request and view quota in Azure AI Foundry"
                 */
                readonly bladeTitle: string;
                /**
                 * The text "View quota"
                 */
                readonly button: string;
            };
        };
        readonly RegionsTab: {
            /**
             * The text "Additonal regions"
             */
            readonly additionRegionLabel: string;
            /**
             * The text "Select regions in which you currently have or plan to create AzureML workspaces and use assets from this registry. You can choose to add more regions to the registry later."
             */
            readonly description: string;
            /**
             * The text "Regions"
             */
            readonly title: string;
        };
        readonly RequestQuota: {
            /**
             * The text "Dedicated"
             */
            readonly dedicatedSectionLabel: string;
            /**
             * The text "Low priority"
             */
            readonly lowPrioritySectionLabel: string;
            /**
             * The text "Dedicated"
             */
            readonly vmTypeDedicated: string;
            /**
             * The text "Low priority"
             */
            readonly vmTypeLowPriority: string;
            /**
             * The text "VM Types"
             */
            readonly vmTypesLabel: string;
        };
        readonly SearchService: {
            readonly Dropdown: {
                /**
                 * The text "{0} (SKU: {1}) - Standard SKU is needed at the minimum"
                 */
                readonly standardSupportedFormat: string;
            };
        };
        readonly Security: {
            /**
             * The text "Security"
             */
            readonly menuText: string;
        };
        readonly SoftDeletedWorkspace: {
            readonly Blade: {
                readonly AIStudio: {
                    readonly ToolBar: {
                        /**
                         * The text "AI Foundry resources"
                         */
                        readonly header: string;
                    };
                };
                readonly Footer: {
                    readonly Buttons: {
                        /**
                         * The text "Cancel"
                         */
                        readonly cancel: string;
                        /**
                         * The text "Permanently delete"
                         */
                        readonly purge: string;
                        /**
                         * The text "Recover"
                         */
                        readonly recover: string;
                    };
                };
                readonly ToolBar: {
                    readonly Buttons: {
                        /**
                         * The text "Refresh"
                         */
                        readonly refresh: string;
                    };
                    /**
                     * The text "Machine Learning workspaces"
                     */
                    readonly header: string;
                };
                /**
                 * The text "Error loading soft deleted resources"
                 */
                readonly errorMessage: string;
                /**
                 * The text "Loading..."
                 */
                readonly loadingText: string;
                /**
                 * The text "Recover or permanently delete resources"
                 */
                readonly subTitle: string;
                /**
                 * The text "Recently deleted resources"
                 */
                readonly title: string;
            };
            readonly DeleteBlade: {
                readonly FormControls: {
                    readonly Buttons: {
                        /**
                         * The text "Cancel"
                         */
                        readonly cancel: string;
                        /**
                         * The text "Delete"
                         */
                        readonly delete: string;
                    };
                    readonly Confirm: {
                        /**
                         * The text "Name does not match"
                         */
                        readonly errorMessage: string;
                        /**
                         * The text "Confirm delete"
                         */
                        readonly label: string;
                        /**
                         * The text "Type the resource name"
                         */
                        readonly placeholder: string;
                    };
                    readonly PermanentDelete: {
                        /**
                         * The text "Delete this resource permanently"
                         */
                        readonly checkbox: string;
                        /**
                         * The text "Permanently delete"
                         */
                        readonly header: string;
                    };
                };
                readonly Message: {
                    readonly AIStudio: {
                        /**
                         * The text "Permanently delete this resource "{0}"?"
                         */
                        readonly text: string;
                    };
                    /**
                     * The text "This resource uses a customer-managed key (CMK) for data encryption. When soft-deleted, dependent resources for storing data will not be deleted and will incur cost until this resource is hard-deleted. {0}"
                     */
                    readonly cMKtext: string;
                    /**
                     * The text "Learn more"
                     */
                    readonly link: string;
                    /**
                     * The text "When you delete this resource, it is temporarily placed in a ‘soft-delete’ state that allows you to recover it. Deletion of your data is postponed until your resource is permanently deleted by you, or until the expiry of the soft-delete data retention period of fourteen (14) days. You can override the soft-delete behavior and permanently delete your resource immediately. {0}"
                     */
                    readonly text: string;
                };
                /**
                 * The text "Delete resource"
                 */
                readonly title: string;
            };
            readonly Grid: {
                readonly Columns: {
                    /**
                     * The text "Deleted date"
                     */
                    readonly deletedDate: string;
                    /**
                     * The text "Name"
                     */
                    readonly name: string;
                    /**
                     * The text "Scheduled permanent deletion date"
                     */
                    readonly purgeDate: string;
                    /**
                     * The text "Resource group"
                     */
                    readonly resourceGroup: string;
                };
                /**
                 * The text "Deleted resources grid"
                 */
                readonly ariaLabel: string;
                /**
                 * The text "No resources found to display"
                 */
                readonly noWorkspacesFound: string;
            };
            readonly OverviewBlade: {
                /**
                 * The text "The soft delete feature has been enabled on this resource. After you soft delete,  this resource data remains available. It will get purged after the retention period. You may purge it sooner, or recover the resource"
                 */
                readonly deleteMessage: string;
            };
            readonly WorkspacesBlade: {
                readonly Commands: {
                    readonly Generic: {
                        readonly Error: {
                            /**
                             * The text "There was an error processing your request. Try again in a few moments"
                             */
                            readonly message: string;
                        };
                    };
                    readonly PurgeNotifications: {
                        readonly Failure: {
                            /**
                             * The text "Failed to permanently delete {0} out of {1} resources"
                             */
                            readonly message: string;
                            /**
                             * The text "Failed to delete resources"
                             */
                            readonly title: string;
                        };
                        readonly InProgress: {
                            /**
                             * The text "Permanently deleting {0} resources"
                             */
                            readonly message: string;
                            /**
                             * The text "Deleting resources ..."
                             */
                            readonly title: string;
                        };
                        readonly Success: {
                            /**
                             * The text "Successfully deleted {0} resource(s) permanently"
                             */
                            readonly message: string;
                            /**
                             * The text "Successfully deleted resources"
                             */
                            readonly title: string;
                        };
                    };
                    readonly RecoverNotifications: {
                        readonly Failure: {
                            /**
                             * The text "Failed to recover {0} out of {1} resources"
                             */
                            readonly message: string;
                            /**
                             * The text "Failed to recover resources"
                             */
                            readonly title: string;
                        };
                        readonly InProgress: {
                            /**
                             * The text "Recovering {0} resource(s)"
                             */
                            readonly message: string;
                            /**
                             * The text "Recovering resources ..."
                             */
                            readonly title: string;
                        };
                        readonly Success: {
                            /**
                             * The text "Successfully recovered {0} resource(s)"
                             */
                            readonly message: string;
                            /**
                             * The text "Successfully recovered resources"
                             */
                            readonly title: string;
                        };
                    };
                    readonly ValidateResources: {
                        readonly AppInsights: {
                            readonly Failure: {
                                /**
                                 * The text "Recovery error: the associated Application Insights resource "{0}" could not be found. It may have been deleted. Recover or recreate the application insights resource under its former name "{0}". You may restore your application insights data by recovering the associated log analytics workspace first.""
                                 */
                                readonly message: string;
                            };
                        };
                        readonly ContainerRegistry: {
                            readonly Failure: {
                                /**
                                 * The text "Recovery error: the associated Container Registry resource "{0}" could not be found. A soft-deleted cannot be recovered without a Container Registry as a dependency."
                                 */
                                readonly message: string;
                            };
                        };
                        readonly Failure: {
                            /**
                             * The text "Failed to validate resource {0}"
                             */
                            readonly title: string;
                        };
                        readonly InProgress: {
                            /**
                             * The text "Validating {0} resources for {1}"
                             */
                            readonly message: string;
                            /**
                             * The text "Validating resources ..."
                             */
                            readonly title: string;
                        };
                        readonly KeyVault: {
                            readonly Failure: {
                                /**
                                 * The text "Recovery error: the associated Azure Key Vault resource "{0}" could not be found. A soft-deleted resource cannot be recovered without its previously attached Key Vault. The required Azure Key Vault may still be recoverable, see `Manage deleted vaults`."
                                 */
                                readonly message: string;
                            };
                        };
                        readonly StorageAccount: {
                            readonly Failure: {
                                /**
                                 * The text "Recovery error: the associated Storage Account resource "{0}" could not be found. If the storage account was previously soft-deleted, recover it first before recovering this resource."
                                 */
                                readonly message: string;
                            };
                        };
                        readonly Success: {
                            /**
                             * The text "Successfully validated resources for {0}"
                             */
                            readonly message: string;
                            /**
                             * The text "Successfully validated"
                             */
                            readonly title: string;
                        };
                    };
                };
            };
        };
        readonly StorageAccount: {
            readonly Dropdown: {
                readonly Hub: {
                    /**
                     * The text "A storage account is used as the default datastore for the AI hub. You may create a new Azure Storage resource or select an existing one in your subscription."
                     */
                    readonly info: string;
                };
                readonly SettingsBlade: {
                    readonly Name: {
                        /**
                         * The text "The name must meet the following requirements:"
                         */
                        readonly infoDescription: string;
                        /**
                         * The text "Unique across all existing storage account names in Azure"
                         */
                        readonly infoItem1: string;
                        /**
                         * The text "Between 3 and 24 characters long"
                         */
                        readonly infoItem2: string;
                        /**
                         * The text "Only contain lowercase letters and numbers"
                         */
                        readonly infoItem3: string;
                    };
                    readonly Performance: {
                        /**
                         * The text "Standard storage accounts are backed by magnetic drives and provide the lowest cost per GB. Premium storage accounts are backed by solid state drives and offer consistent, low-latency performance."
                         */
                        readonly info: string;
                    };
                    readonly Replication: {
                        /**
                         * The text "The data in your storage account is always replicated to ensure durability and high availability. Choose a replication strategy that best matches you requirements."
                         */
                        readonly info: string;
                        /**
                         * The text "Learn more"
                         */
                        readonly infoLearnMore: string;
                    };
                    /**
                     * The text "Enable hierarchical namespace (preview)"
                     */
                    readonly hnsCheckLabel: string;
                    /**
                     * The text "The Data Lake Storage Gen2 hierarchical namespace accelerates big data analytics workloads, enables faster and more reliable file operations, and enables file-level access control lists (ACLs). {0}"
                     */
                    readonly hnsDescriptionText: string;
                    /**
                     * The text "Data Lake Storage Gen2 (preview)"
                     */
                    readonly hnsDescriptionTitle: string;
                    /**
                     * The text "{0} - Hierarchical namespace is not supported"
                     */
                    readonly hnsNotSupportedFormat: string;
                    /**
                     * The text "Learn more"
                     */
                    readonly learnMoreText: string;
                    /**
                     * The text "Storage name must be between 3 and 24 characters and may only contain lowercase letters and numbers."
                     */
                    readonly nameInvalidMessage: string;
                    /**
                     * The text "Name"
                     */
                    readonly nameLabel: string;
                    /**
                     * The text "Performance"
                     */
                    readonly performanceLabel: string;
                    /**
                     * The text "Premium"
                     */
                    readonly performancePremium: string;
                    /**
                     * The text "Standard"
                     */
                    readonly performanceStandard: string;
                    /**
                     * The text "{0} - Premium storage account is not supported"
                     */
                    readonly premiumNotSupportedFormat: string;
                    /**
                     * The text "Geo-redundant storage (GRS)"
                     */
                    readonly replicationGRS: string;
                    /**
                     * The text "Geo-zone-redundant storage (GZRS)"
                     */
                    readonly replicationGZRS: string;
                    /**
                     * The text "Locally-redundant storage (LRS)"
                     */
                    readonly replicationLRS: string;
                    /**
                     * The text "Replication"
                     */
                    readonly replicationLabel: string;
                    /**
                     * The text "Read-access geo-redundant storage (RA-GRS)"
                     */
                    readonly replicationRAGRS: string;
                    /**
                     * The text "Read-access geo-zone-redundant storage (RA-GZRS)"
                     */
                    readonly replicationRAGZRS: string;
                    /**
                     * The text "Zone-redundant storage (ZRS)"
                     */
                    readonly replicationZRS: string;
                    /**
                     * The text "Create new storage account"
                     */
                    readonly title: string;
                };
                /**
                 * The text "Create new storage account"
                 */
                readonly createNewAriaLabel: string;
                /**
                 * The text "A storage account is used as the default datastore for the workspace. You may create a new Azure Storage resource or select an existing one in your subscription."
                 */
                readonly info: string;
                /**
                 * The text "Storage account"
                 */
                readonly label: string;
            };
            readonly SummaryTab: {
                /**
                 * The text "Storage account"
                 */
                readonly label: string;
            };
        };
        readonly StorageTab: {
            /**
             * The text "As users work in Azure AI Foundry, uploaded data, stored credentials and generated artifacts like logs are stored."
             */
            readonly description: string;
            /**
             * The text "Configure how your data is stored"
             */
            readonly header: string;
            /**
             * The text "Logs and docker images"
             */
            readonly logsAndDockerHeader: string;
            /**
             * The text "Storage"
             */
            readonly title: string;
        };
        readonly SummaryTab: {
            readonly Advanced: {
                readonly ManagedIdentity: {
                    /**
                     * The text "System assigned"
                     */
                    readonly systemAssigned: string;
                    /**
                     * The text "Identity type"
                     */
                    readonly typeText: string;
                    /**
                     * The text "User assigned"
                     */
                    readonly userAssigned: string;
                    /**
                     * The text "User identity name"
                     */
                    readonly userIdentityName: string;
                    /**
                     * The text "User identity resource group"
                     */
                    readonly userIdentityResourceGroup: string;
                };
            };
            readonly Resource: {
                /**
                 * The text "{0} ({1})"
                 */
                readonly existingFormat: string;
                /**
                 * The text "Microsoft-managed"
                 */
                readonly microsoftManaged: string;
                /**
                 * The text "(new) {0}"
                 */
                readonly newFormat: string;
                /**
                 * The text "None"
                 */
                readonly none: string;
            };
            /**
             * The text "Error creating resource group when creating default log analytics workspace: {0}"
             */
            readonly createResourceGroupErrorText: string;
            /**
             * The text "Error getting log workspaces for creating default log analytics workspace: {0}"
             */
            readonly gettingLogWorkspacesErrorText: string;
        };
        readonly Toolbar: {
            /**
             * The text "Feedback"
             */
            readonly feedback: string;
        };
        /**
         * The text "For your convenience, these resources are added automatically to the workspace, if regionally available: <a href={0} target="_blank">Azure storage</a>, <a href={1} target="_blank">Azure Application Insights</a> and <a href={2} target="_blank">Azure Key Vault</a>."
         */
        readonly additionalResourceInfo: string;
        /**
         * The text "Download a template for automation"
         */
        readonly automationLink: string;
        /**
         * The text "Basics"
         */
        readonly basics: string;
        /**
         * The text "Every workspace must be assigned to an Azure subscription, which is where billing happens. You use resource groups like folders to organize and manage resources, including the workspace you're about to create."
         */
        readonly basicsBladeDetailsIntro: string;
        /**
         * The text "Learn more about Azure resource groups"
         */
        readonly basicsBladeDetailsIntroLearnMore: string;
        /**
         * The text "Select the subscription to manage deployed resources and costs. Use resource groups like folders to organize and manage all your resources. An AI hub is a collaboration environment for a team to share project work, model endpoints, compute, (data) connections, security settings, govern usage."
         */
        readonly basicsBladeHubDetailsIntro: string;
        /**
         * The text "Configure your basic workspace settings like its storage connection, authentication, container, and more."
         */
        readonly basicsBladeInstanceIntro: string;
        /**
         * The text "Learn more"
         */
        readonly basicsBladeInstanceIntroLearnMore: string;
        /**
         * The text "Select a subscription and resource group to organize this and other resources, inherit access, and attribute cost."
         */
        readonly basicsBladeProjectDetailsIntro: string;
        /**
         * The text "Configure your basic registry settings like its name and description."
         */
        readonly basicsBladeRegistryInstanceIntro: string;
        /**
         * The text "Every registry must be assigned to an Azure subscription, which is where billing happens. You use resource groups like folders to organize and manage resources, including the registry you're about to create."
         */
        readonly basicsRegistryBladeDetailsIntro: string;
        /**
         * The text "Basics"
         */
        readonly basicsTabTitle: string;
        /**
         * The text "Create"
         */
        readonly buttonCreate: string;
        /**
         * The text "Next : {0}"
         */
        readonly buttonNext: string;
        /**
         * The text "Next >"
         */
        readonly buttonNextPlaceholder: string;
        /**
         * The text "< Previous"
         */
        readonly buttonPrevious: string;
        /**
         * The text "Review + create"
         */
        readonly buttonReviewCreate: string;
        /**
         * The text "Created on"
         */
        readonly columnCreationTime: string;
        /**
         * The text "Workspace ID"
         */
        readonly columnMachineLearningWorkspaceId: string;
        /**
         * The text "Cancel"
         */
        readonly commandCancel: string;
        /**
         * The text "Create project"
         */
        readonly commandCreateProject: string;
        /**
         * The text "Delete"
         */
        readonly commandDelete: string;
        /**
         * The text "Download config.json"
         */
        readonly commandDownloadConfig: string;
        /**
         * The text "Use this file to load the workspace configuration in your Azure ML SDK notebook or Python script"
         */
        readonly commandDownloadConfigTooltip: string;
        /**
         * The text "Are you sure you want to delete Workspace {0}?"
         */
        readonly confirmationMachineLearningDelete: string;
        /**
         * The text "Are you sure you want to delete Registry {0}?"
         */
        readonly confirmationMachineLearningRegistryDelete: string;
        /**
         * The text "Create"
         */
        readonly createBladeCreateButtonName: string;
        /**
         * The text "Description"
         */
        readonly createBladeDescriptionLabel: string;
        /**
         * The text "Display name of the AI hub that will be displayed in AI Foundry"
         */
        readonly createBladeFriendlyNameInfo: string;
        /**
         * The text "Friendly name"
         */
        readonly createBladeFriendlyNameLabel: string;
        /**
         * The text "Hub"
         */
        readonly createBladeHubNameLabel: string;
        /**
         * The text "Create an Azure AI hub resource"
         */
        readonly createBladeHubSubtitle: string;
        /**
         * The text "Azure AI hub"
         */
        readonly createBladeHubTitle: string;
        /**
         * The text "Location"
         */
        readonly createBladeLocationLabel: string;
        /**
         * The text "Main"
         */
        readonly createBladeMainTabTitle: string;
        /**
         * The text "How to use MLflow with Azure ML"
         */
        readonly createBladeNextStepsMLFlowAzureMLDescription: string;
        /**
         * The text "Organize and track work, collaborate with others and upload data. Access your work in Azure AI Foundry or Azure Machine Learning Studio."
         */
        readonly createBladeProjectSubtitle: string;
        /**
         * The text "Azure AI project"
         */
        readonly createBladeProjectTitle: string;
        /**
         * The text "description"
         */
        readonly createBladeRegistryDescriptionKey: string;
        /**
         * The text "Description"
         */
        readonly createBladeRegistryDescriptionLabel: string;
        /**
         * The text "Name"
         */
        readonly createBladeRegistryNameLabel: string;
        /**
         * The text "Create a machine learning registry"
         */
        readonly createBladeRegistrySubtitle: string;
        /**
         * The text "Resource Group"
         */
        readonly createBladeResourceGroupLabel: string;
        /**
         * The text "Review + Create"
         */
        readonly createBladeReviewCreateButtonName: string;
        /**
         * The text "Review"
         */
        readonly createBladeReviewTabTitle: string;
        /**
         * The text "Subscription Id"
         */
        readonly createBladeSubscriptionIdLabel: string;
        /**
         * The text "Subscription"
         */
        readonly createBladeSubscriptionLabel: string;
        /**
         * The text "Create a machine learning workspace"
         */
        readonly createBladeSubtitle: string;
        /**
         * The text "Tags"
         */
        readonly createBladeTagTabTitle: string;
        /**
         * The text "Template Error"
         */
        readonly createBladeTemplateErrorText: string;
        /**
         * The text "Azure Machine Learning"
         */
        readonly createBladeTitle: string;
        /**
         * The text "Workspace creation in progress"
         */
        readonly createBladeWorkspaceCreationProgressNotification: string;
        /**
         * The text "Workspace deployment Error"
         */
        readonly createBladeWorkspaceDeploymentErrorText: string;
        /**
         * The text "Workspace details"
         */
        readonly createBladeWorkspaceDetails: string;
        /**
         * The text "Name"
         */
        readonly createBladeWorkspaceNameLabel: string;
        /**
         * The text "Location"
         */
        readonly createLocationLabelDefault: string;
        /**
         * The text "Primary Region"
         */
        readonly createLocationLabelPrimaryRegion: string;
        /**
         * The text "Region"
         */
        readonly createLocationLabelRegion: string;
        /**
         * The text "Resource group"
         */
        readonly createResourceGroup: string;
        /**
         * The text ""
         */
        readonly createResourceGroupCreateNewPlaceholder: string;
        /**
         * The text "Resource group"
         */
        readonly createResourceGroupTitle: string;
        /**
         * The text "Subscription"
         */
        readonly createSubscriptionLabel: string;
        /**
         * The text "Tags are name/value pairs that enable you to categorize resources and view consolidated billing by applying the same tag to multiple resources and resource groups."
         */
        readonly createTagsTabIntro: string;
        /**
         * The text "Learn more about tags"
         */
        readonly createTagsTabLearnMoreText: string;
        /**
         * The text "Tags"
         */
        readonly createTagsTabTitle: string;
        /**
         * The text "Note that if you create tags and then change resource settings on other tabs, your tags will be automatically updated."
         */
        readonly createTagsTabUpdateNotice: string;
        /**
         * The text "Validation failed. Click here to view details."
         */
        readonly createTemplateValidationError: string;
        /**
         * The text "Running final validation..."
         */
        readonly createTemplateValidationInProgress: string;
        /**
         * The text "Validation passed"
         */
        readonly createTemplateValidationSuccess: string;
        /**
         * The text "Deployment Capacity"
         */
        readonly deploymentCapacityMetric: string;
        /**
         * The text "Cpu Utilization"
         */
        readonly deploymentCpuUtilizationMetric: string;
        /**
         * The text "Disk Utilization"
         */
        readonly deploymentDiskUtilizationMetric: string;
        /**
         * The text "Environment"
         */
        readonly deploymentEnvironmentId: string;
        /**
         * The text "Deployment Type"
         */
        readonly deploymentKind: string;
        /**
         * The text "Memory Utilization"
         */
        readonly deploymentMemoryUtilizationMetric: string;
        /**
         * The text "Model Id"
         */
        readonly deploymentModelIdLabel: string;
        /**
         * The text "Deployment Name"
         */
        readonly deploymentName: string;
        /**
         * The text "Provisioning State"
         */
        readonly deploymentProvisioningStateLabel: string;
        /**
         * The text "Resource details"
         */
        readonly detailsLabel: string;
        /**
         * The text "Download a template for automation"
         */
        readonly downloadTemplateLinkText: string;
        /**
         * The text "Auth Mode"
         */
        readonly endpointAuthModeLabel: string;
        /**
         * The text "Endpoint Id"
         */
        readonly endpointId: string;
        /**
         * The text "Provisioning State"
         */
        readonly endpointProvisioningStateLabel: string;
        /**
         * The text "Request Latency"
         */
        readonly endpointRequestLatencyMetric: string;
        /**
         * The text "Request Per Minute"
         */
        readonly endpointRequestsPerMinuteMetric: string;
        /**
         * The text "Scoring Uri"
         */
        readonly endpointScoringUriLabel: string;
        /**
         * The text "Swagger Uri"
         */
        readonly endpointSwaggerLabel: string;
        /**
         * The text "An unexpected parsing error occurred."
         */
        readonly errorJsonParsingException: string;
        /**
         * The text "Instance details"
         */
        readonly instanceLabel: string;
        /**
         * The text "Automated machine learning"
         */
        readonly labelAutoMLMenuItem: string;
        /**
         * The text "Refresh"
         */
        readonly labelCommandButtonRefresh: string;
        /**
         * The text "Compute"
         */
        readonly labelComputeMenuItem: string;
        /**
         * The text "Data Labeling"
         */
        readonly labelDataLabeling: string;
        /**
         * The text "Data (Preview)"
         */
        readonly labelDataMenuItem: string;
        /**
         * The text "Deployments"
         */
        readonly labelDeployments: string;
        /**
         * The text "Deployments"
         */
        readonly labelDeploymentsMenuItem: string;
        /**
         * The text "Images"
         */
        readonly labelImagesMenuItem: string;
        /**
         * The text "Application Insights"
         */
        readonly labelInsights: string;
        /**
         * The text "Key Vault"
         */
        readonly labelKeyVault: string;
        /**
         * The text "Visual interface"
         */
        readonly labelMLStudioLauncher: string;
        /**
         * The text "MLflow tracking URI"
         */
        readonly labelMlFlowUri: string;
        /**
         * The text "Models"
         */
        readonly labelModelsMenuItem: string;
        /**
         * The text "Pipelines"
         */
        readonly labelPipelinesMenuItem: string;
        /**
         * The text "Azure AI hub"
         */
        readonly labelProjectHubResource: string;
        /**
         * The text "Experiments"
         */
        readonly labelProjectsMenuItem: string;
        /**
         * The text "Properties"
         */
        readonly labelProperties: string;
        /**
         * The text "Usage + quotas"
         */
        readonly labelQuotaUsage: string;
        /**
         * The text "Container Registry"
         */
        readonly labelRegistry: string;
        /**
         * The text "Request Quota"
         */
        readonly labelRequestQuota: string;
        /**
         * The text "Resource"
         */
        readonly labelResource: string;
        /**
         * The text "Storage"
         */
        readonly labelStorage: string;
        /**
         * The text "Activities"
         */
        readonly labelTasksMenuItem: string;
        /**
         * The text "Notebook VMs"
         */
        readonly labelWorkstationsMenuItem: string;
        /**
         * The text "Learn more"
         */
        readonly learnMore: string;
        /**
         * The text "Location"
         */
        readonly location: string;
        /**
         * The text "Managed Compute"
         */
        readonly machineLearningCompute: string;
        /**
         * The text "Microsoft-managed"
         */
        readonly managedKeyVault: string;
        /**
         * The text "Drag-n-Drop to build machine learning models"
         */
        readonly mlStudioCapability1: string;
        /**
         * The text "No limit to data size or compute capacity for model training"
         */
        readonly mlStudioCapability2: string;
        /**
         * The text "Intrinsic and powerful Python support"
         */
        readonly mlStudioCapability3: string;
        /**
         * The text "One click to deploy your web service"
         */
        readonly mlStudioCapability4: string;
        /**
         * The text "Rich and fast-growing modules support"
         */
        readonly mlStudioCapability5: string;
        /**
         * The text "Launch visual interface"
         */
        readonly mlStudioLaunchLabel: string;
        /**
         * The text "What's possible with visual interface"
         */
        readonly mlStudioSubtitle: string;
        /**
         * The text "Visual interface (preview)"
         */
        readonly mlStudioTitle: string;
        /**
         * The text "Validation failed for the following tabs: {0}. Required information is missing or not valid."
         */
        readonly multipleInvalidTabErrorMessage: string;
        /**
         * The text "(new) {0}"
         */
        readonly newResource: string;
        /**
         * The text "(New) {0}"
         */
        readonly newResourceCapitalized: string;
        /**
         * The text "(New) {0}"
         */
        readonly newResourceFormatCaps: string;
        /**
         * The text "No content"
         */
        readonly noContent: string;
        /**
         * The text "None"
         */
        readonly none: string;
        /**
         * The text "Machine learning online endpoint"
         */
        readonly onlineEndpointName: string;
        /**
         * The text "Workspace"
         */
        readonly onlineEndpointWorkspaceName: string;
        /**
         * The text "Overview"
         */
        readonly overview: string;
        /**
         * The text "Summary Home"
         */
        readonly overviewKeywords: string;
        /**
         * The text "The resource could not be deleted."
         */
        readonly progressMachineLearningDeleteError: string;
        /**
         * The text "The Registry could not be deleted."
         */
        readonly progressMachineLearningRegistryDeleteError: string;
        /**
         * The text "Deleting the Registry"
         */
        readonly progressMachineLearningRegistryDeleteExecuting: string;
        /**
         * The text "Deleting resource"
         */
        readonly progressMachineLearningServicesDeleteExecuting: string;
        /**
         * The text "An error occurred while deleting the Registry name '{registryName}'.{lineBreak}Error details:{lineBreak}{errorMessage}."
         */
        readonly progressNotificationMachineLearningRegistryDeleteError: string;
        /**
         * The text "Registry deletion error"
         */
        readonly progressNotificationMachineLearningRegistryDeleteErrorTitle: string;
        /**
         * The text "Registry '{registryName}' deletion in progress..."
         */
        readonly progressNotificationMachineLearningRegistryDeleteExecuting: string;
        /**
         * The text "Deleting Registry..."
         */
        readonly progressNotificationMachineLearningRegistryDeleteExecutingTitle: string;
        /**
         * The text "Registry '{registryName}' was deleted successfully."
         */
        readonly progressNotificationMachineLearningRegistryDeleteSuccess: string;
        /**
         * The text "Registry deleted"
         */
        readonly progressNotificationMachineLearningRegistryDeleteSuccessTitle: string;
        /**
         * The text "Resource deletion canceled"
         */
        readonly progressNotificationMachineLearningServicesDeleteCancelTitle: string;
        /**
         * The text "An error occurred while deleting the Workspace name '{workspaceName}'.{lineBreak}Error details:{lineBreak}{errorMessage}."
         */
        readonly progressNotificationMachineLearningServicesDeleteError: string;
        /**
         * The text "Resource deletion error"
         */
        readonly progressNotificationMachineLearningServicesDeleteErrorTitle: string;
        /**
         * The text "Resource '{workspaceName}' deletion in progress..."
         */
        readonly progressNotificationMachineLearningServicesDeleteExecuting: string;
        /**
         * The text "Deleting resource..."
         */
        readonly progressNotificationMachineLearningServicesDeleteExecutingTitle: string;
        /**
         * The text "Resource '{workspaceName}' was deleted successfully."
         */
        readonly progressNotificationMachineLearningServicesDeleteSuccess: string;
        /**
         * The text "Succesfully deleted"
         */
        readonly progressNotificationMachineLearningServicesDeleteSuccessTitle: string;
        /**
         * The text "Application Insights"
         */
        readonly propertiesBladeApplicationInsightsLabel: string;
        /**
         * The text "Container Registry ID"
         */
        readonly propertiesBladeContainerRegistryIdLabel: string;
        /**
         * The text "Created on"
         */
        readonly propertiesBladeCreationDateLabel: string;
        /**
         * The text "Key Vault ID"
         */
        readonly propertiesBladeKeyVaultIdLabel: string;
        /**
         * The text "Location"
         */
        readonly propertiesBladeLocationLabel: string;
        /**
         * The text "Resource Group"
         */
        readonly propertiesBladeResourceGroupLabel: string;
        /**
         * The text "Resource ID"
         */
        readonly propertiesBladeResourceIdLabel: string;
        /**
         * The text "Storage account ID"
         */
        readonly propertiesBladeStorageAccountIdLabel: string;
        /**
         * The text "Subscription ID"
         */
        readonly propertiesBladeSubscriptionIdLabel: string;
        /**
         * The text "Subscription Name"
         */
        readonly propertiesBladeSubscriptionNameLabel: string;
        /**
         * The text "AI hub ID"
         */
        readonly propertiesBladeWorkspaceHubIdLabel: string;
        /**
         * The text "Machine Learning workspace ID"
         */
        readonly propertiesBladeWorkspaceIdLabel: string;
        /**
         * The text "Quick link under Overview blade."
         */
        readonly quickLinkUnderOverviewBladeAriaLabel: string;
        /**
         * The text "Usage + quotas"
         */
        readonly quotaBladeTitle: string;
        /**
         * The text "Note:"
         */
        readonly quotaNote: string;
        /**
         * The text "Your subscription {0} is not registered with the '{1}' resource provider. Please create a 'Machine Learning service' workspace to auto-register and retry submitting the support request."
         */
        readonly quotaProviderNotRegisteredErrorMsg: string;
        /**
         * The text "Registering your subscription {0} with the '{1}' resource provider."
         */
        readonly quotaProviderRegisteringErrorMsg: string;
        /**
         * The text "Configure quotas"
         */
        readonly quotaQuotaSettingTabHeader: string;
        /**
         * The text "Current vCPU limit"
         */
        readonly quotaRequestCurrentLimit: string;
        /**
         * The text "Click to learn more about Compute (cores/vCPUs) quota increase requests."
         */
        readonly quotaRequestDocumentationInfoBox: string;
        /**
         * The text "Please enter the limit for any resource(s) you are requesting:"
         */
        readonly quotaRequestHeader: string;
        /**
         * The text "{0} - current value: {1} / requested value: {2}"
         */
        readonly quotaRequestLogOutputMessageDetail: string;
        /**
         * The text "New vCPU limit"
         */
        readonly quotaRequestNewLimit: string;
        /**
         * The text "No quota resources found for given location"
         */
        readonly quotaRequestNotFound: string;
        /**
         * The text "Resource name"
         */
        readonly quotaRequestResourceName: string;
        /**
         * The text "Save and continue"
         */
        readonly quotaRequestSubmit: string;
        /**
         * The text "Quota details"
         */
        readonly quotaRequestTitle: string;
        /**
         * The text "VM series"
         */
        readonly quotaRequestVMFamily: string;
        /**
         * The text "For a cluster we show your currently allocated cores and maximum cores it can scale to."
         */
        readonly quotaSubscriptionViewClusterHelptext: string;
        /**
         * The text "Expand each VM family to view your quota allocation and resource usage by workspace and further to view your clusters and instances."
         */
        readonly quotaSubscriptionViewResourceNameHelptext: string;
        /**
         * The text "Subscription view"
         */
        readonly quotaSubscriptionViewTabHeader: string;
        /**
         * The text "For an instance it continues to use quota even in Stopped state so you can restart it at any time."
         */
        readonly quotaSubscriptionViewUsageHelptext: string;
        /**
         * The text "Dedicated cores usage"
         */
        readonly quotaTableHeaderDedicated: string;
        /**
         * The text "Low priority cores usage"
         */
        readonly quotaTableHeaderLowPriority: string;
        /**
         * The text "Resource name"
         */
        readonly quotaTableHeaderQuota: string;
        /**
         * The text "No data to display"
         */
        readonly quotaTableNoData: string;
        /**
         * The text "The server encountered an error processing current request. Please refresh the table again."
         */
        readonly quotaTableServerError: string;
        /**
         * The text "Total subscription quota"
         */
        readonly quotaTableTotalSubscriptionQuota: string;
        /**
         * The text "Workspace level quota cannot exceed the subscription level quota limit."
         */
        readonly quotaWorkspaceQuotaExceedSubscriptionLimit: string;
        /**
         * The text "You are not authorized to set quota at the workspace level. Please reach out to your subscription admin to help allocate resources between workspaces."
         */
        readonly quotaWorkspaceQuotaInsufficientPermissions: string;
        /**
         * The text "Please specify a VM family that is supported in the $region region and you have subscription level quota for."
         */
        readonly quotaWorkspaceQuotaInvalidVMFamilyName: string;
        /**
         * The text "Workspace level quota cannot be less than the cores required to support the minimum nodes of this workspace’s clusters and instances."
         */
        readonly quotaWorkspaceQuotaLessThanMinimumClusterCores: string;
        /**
         * The text "Allocate subscription level quota between workspaces by setting it here. To change the all-up subscription level quota, use the request quota button at the bottom. Note that you need to be a subscription owner to modify these values."
         */
        readonly quotaWorkspaceQuotaNewLimitHelpText: string;
        /**
         * The text "Unallocated cores: {0}, Maximum: {1}"
         */
        readonly quotaWorkspaceQuotaPlaceHolder: string;
        /**
         * The text "Expand each VM size to view and allocate subscription level quota between workspaces."
         */
        readonly quotaWorkspaceQuotaResourceNameHelpText: string;
        /**
         * The text "Unknown error"
         */
        readonly quotaWorkspaceQuotaUnknownError: string;
        /**
         * The text "For a cluster we show your currently allocated cores and maximum cores it can scale to."
         */
        readonly quotaWorkspaceViewClusterHelptext: string;
        /**
         * The text "Expand each workspace to view your quota allocation and resource usage by VM family and further to view your clusters and instances."
         */
        readonly quotaWorkspaceViewResourceNameHelptext: string;
        /**
         * The text "Workspace view"
         */
        readonly quotaWorkspaceViewTabHeader: string;
        /**
         * The text "For an instance it continues to use quota even in Stopped state so you can restart it at any time."
         */
        readonly quotaWorkspaceViewUsageHelptext: string;
        /**
         * The text "Recently deleted"
         */
        readonly recentlyDeletedWorkspaces: string;
        /**
         * The text "Location"
         */
        readonly resourceLocationColumn: string;
        /**
         * The text "The resource name is required"
         */
        readonly resourceNameRequired: string;
        /**
         * The text "Scaling"
         */
        readonly scaling: string;
        /**
         * The text "Select a location"
         */
        readonly selectLocation: string;
        /**
         * The text "Select a subscription"
         */
        readonly selectSubscription: string;
        /**
         * The text "Validation failed for the following tab: {0}. Required information is missing or not valid."
         */
        readonly singleInvalidTabErrorMessage: string;
        /**
         * The text "Configure compute for deploying and managing models"
         */
        readonly subtitleEnvironmentInfoBlade: string;
        /**
         * The text "Review + create"
         */
        readonly summaryTabTitle: string;
        /**
         * The text "Validation failed. Required information is missing or not valid."
         */
        readonly tabValidationErrors: string;
        /**
         * The text "Tags"
         */
        readonly tags: string;
        /**
         * The text "Create New"
         */
        readonly textAzureDatabricksOptionCreateNew: string;
        /**
         * The text "Use Existing"
         */
        readonly textAzureDatabricksOptionUseExisting: string;
        /**
         * The text "Explore your Machine Learning workspace to run and track experiments, compare model performance, and deploy models."
         */
        readonly textLaunchWebWorkspace: string;
        /**
         * The text "Explore your Azure Machine Learning workspace"
         */
        readonly textLaunchWebWorkspaceHeader: string;
        /**
         * The text "Automatically create a model from your existing data."
         */
        readonly textMachineLearningServicesAutoMLLinkBody: string;
        /**
         * The text "Create a new Automated Machine Learning Model (Preview)"
         */
        readonly textMachineLearningServicesAutoMLLinkHeader: string;
        /**
         * The text "Learn how to use Azure Machine Learning."
         */
        readonly textMachineLearningServicesDocumentationLinkBody: string;
        /**
         * The text "View Documentation"
         */
        readonly textMachineLearningServicesDocumentationLinkHeader: string;
        /**
         * The text "Join the discussion of Azure Machine Learning. "
         */
        readonly textMachineLearningServicesForumLinkBody: string;
        /**
         * The text "View Forum"
         */
        readonly textMachineLearningServicesForumLinkHeader: string;
        /**
         * The text "Create and Manage"
         */
        readonly textMachineLearningServicesManageFeatoreStoreLinkHeader: string;
        /**
         * The text "Learn how to create and manage Machine Learning registries."
         */
        readonly textMachineLearningServicesManageFeatureStoreLinkBody: string;
        /**
         * The text "Learn how to create and manage Machine Learning registries."
         */
        readonly textMachineLearningServicesManageRegistryLinkBody: string;
        /**
         * The text "Create and Manage"
         */
        readonly textMachineLearningServicesManageRegistryLinkHeader: string;
        /**
         * The text "Quickly get started with the Python SDK and run sample experiments with Azure Machine Learning Notebook VMs."
         */
        readonly textMachineLearningServicesNotebookVMLinkBody: string;
        /**
         * The text "Get Started with Sample Notebooks (Preview)"
         */
        readonly textMachineLearningServicesNotebookVMLinkHeader: string;
        /**
         * The text "Learn how to share Machine Learning assets using registries."
         */
        readonly textMachineLearningServicesShareRegistryLinkBody: string;
        /**
         * The text "Share models, components and environments"
         */
        readonly textMachineLearningServicesShareRegistryLinkHeader: string;
        /**
         * The text "Drag and drop existing components to create new models."
         */
        readonly textMachineLearningServicesVisualInterfaceLinkBody: string;
        /**
         * The text "Build a model using the Visual Interface (Preview)"
         */
        readonly textMachineLearningServicesVisualInterfaceLinkHeader: string;
        /**
         * The text "N/A"
         */
        readonly textNotAvailable: string;
        /**
         * The text "Get inspired by a large collection of machine learning examples."
         */
        readonly textViennaGitHubLinkBody: string;
        /**
         * The text "View more samples at GitHub"
         */
        readonly textViennaGitHubLinkHeader: string;
        /**
         * The text "Assets"
         */
        readonly titleAssetsGroup: string;
        /**
         * The text "Authoring (Preview)"
         */
        readonly titleAuthoringGroup: string;
        /**
         * The text "Deployments"
         */
        readonly titleDeployments: string;
        /**
         * The text "Machine Learning Compute"
         */
        readonly titleEnvironmentInfoBlade: string;
        /**
         * The text "Error"
         */
        readonly titleError: string;
        /**
         * The text "Give feedback"
         */
        readonly titleGiveFeedback: string;
        /**
         * The text "Insights (preview)"
         */
        readonly titleInsights: string;
        /**
         * The text "Delete Workspace"
         */
        readonly titleMachineLearningServicesDeleteConfirmationMessageBox: string;
        /**
         * The text "Delete Registry"
         */
        readonly titleMachineLearningServicesRegistryDeleteConfirmationMessageBox: string;
        /**
         * The text "Getting Started"
         */
        readonly titleMonitoringLens: string;
        /**
         * The text "Settings"
         */
        readonly titleSettings: string;
        /**
         * The text "Support + troubleshooting"
         */
        readonly titleSupport: string;
        /**
         * The text "Machine Learning"
         */
        readonly titleWebWorkspaceBlade: string;
        /**
         * The text "This AI Services name already exists"
         */
        readonly validationAIServicesNameAlreadyInUse: string;
        /**
         * The text "This application insights name already exists"
         */
        readonly validationAppInsightsNameAlreadyInUse: string;
        /**
         * The text "This container registry name already exists"
         */
        readonly validationContainerRegistryNameAlreadyInUse: string;
        /**
         * The text "You don't have the required permissions ({0}) to create an account under the selected resource group"
         */
        readonly validationCreateWorkspacePermission: string;
        /**
         * The text "Dependent resources with this name already exist"
         */
        readonly validationDependentResourcesAlreadyInUse: string;
        /**
         * The text "There was an error while attempting to validate the resource"
         */
        readonly validationError: string;
        /**
         * The text "This AI hub name already exists"
         */
        readonly validationHubNameAlreadyInUse: string;
        /**
         * The text "This key vault name already exists"
         */
        readonly validationKeyVaultNameAlreadyInUse: string;
        /**
         * The text "Please ensure the primary region is one of the selected values"
         */
        readonly validationPrimaryRegionNotSelected: string;
        /**
         * The text "This AI project name already exists"
         */
        readonly validationProjectNameAlreadyInUse: string;
        /**
         * The text "Please select at least one region"
         */
        readonly validationRegionNotSelected: string;
        /**
         * The text "This registry description has a maximum length of 256 characters."
         */
        readonly validationRegistryDescriptionTooLarge: string;
        /**
         * The text "This registry name already exists"
         */
        readonly validationRegistryNameAlreadyInUse: string;
        /**
         * The text "Registry name must be between 3 and 33 characters long. Its first character has to be alphanumeric, and the rest may contain hyphens and underscores. No whitespace is allowed."
         */
        readonly validationRegistryNameInvalid: string;
        /**
         * The text "This storage account name already exists"
         */
        readonly validationStorageAccountNameAlreadyInUse: string;
        /**
         * The text "This workspace name already exists"
         */
        readonly validationWorkspaceNameAlreadyInUse: string;
        /**
         * The text "This workspace name already exists, or is being reserved by a workspace which was previously soft deleted. Please use a different name"
         */
        readonly validationWorkspaceNameAlreadyInUseAndSoftDeleted: string;
        /**
         * The text "Resource name must be between 3 and 33 characters long. Its first character has to be alphanumeric, and the rest may contain hyphens and underscores. No whitespace is allowed."
         */
        readonly validationWorkspaceNameInvalid: string;
        /**
         * The text "This workspace name is reserved"
         */
        readonly validationWorkspaceNameReserved: string;
        /**
         * The text "
      Cancel
    "
         */
        readonly workspaceCancelUpgradeButtonText: string;
        /**
         * The text "
      Confirm Upgrade
    "
         */
        readonly workspaceConfirmUpgradeButtonText: string;
        /**
         * The text "
      View full pricing details
    "
         */
        readonly workspaceCreateSKUPricingDetails: string;
        /**
         * The text "
      The Enterprise SKU (preview) boosts productivity for all ML skill levels and offers robust MLOps capabilities to manage the complete ML lifecycle. THe Basic SKU workspace allows for open source development and a code-first experience.
    "
         */
        readonly workspaceCreateSKUTooltip: string;
        /**
         * The text "The selected subscription doesn’t have permissions to register the resource provider. For more information, <a href = {0} target = "_blank">click here</a>."
         */
        readonly workspaceErrorMessage: string;
        /**
         * The text "
      Workspace with ID "{0}" could not be loaded.
    "
         */
        readonly workspaceLoadFailure: string;
        /**
         * The text "
      Workspace edition
    "
         */
        readonly workspaceSKUPropertyLabel: string;
        /**
         * The text "
      Introducing a new immersive experience (preview) for managing the end-to-end machine learning lifecycle.
    "
         */
        readonly workspaceTwoBannerBasicSKUBody: string;
        /**
         * The text "
      Launch Preview Now
    "
         */
        readonly workspaceTwoBannerBasicSKUTitle: string;
        /**
         * The text "
      An immersive experience for managing the end-to-end machine learning lifecycle.
    "
         */
        readonly workspaceTwoBannerBody: string;
        /**
         * The text "
      Introducing a new immersive experience (preview) for managing the end-to-end machine learning lifecycle.
    "
         */
        readonly workspaceTwoBannerBodyPreview: string;
        /**
         * The text "
      Launch now
    "
         */
        readonly workspaceTwoBannerButton: string;
        /**
         * The text "
      Learn more
    "
         */
        readonly workspaceTwoBannerLink: string;
        /**
         * The text "
      Launch now
    "
         */
        readonly workspaceTwoNoticeButton: string;
        /**
         * The text "
      Contents of this page will be moving to a new immersive experience for
      managing the end-to-end machine learning lifecycle. Compute targets will
      be manageable from both locations. Features provided in preview are
      offered at no additional charge but may not remain so after general
      availability.
    "
         */
        readonly workspaceTwoNoticeMessage: string;
        /**
         * The text "
      about our pricing
    "
         */
        readonly workspaceUpgradeAboutOurPricing: string;
        /**
         * The text "
      You currently do not have permissions to upgrade your workspace. Please contact your IT administrator.
    "
         */
        readonly workspaceUpgradeAuthorizationFailed: string;
        /**
         * The text "Upgrade this workspace to Enterprise edition (preview) to use visual machine learning, advanced automated machine learning, and to manage quota."
         */
        readonly workspaceUpgradeBannerText: string;
        /**
         * The text "
      You need an Enterprise edition (preview) workspace to view and set quotas on Enterprise workspaces.
    "
         */
        readonly workspaceUpgradeBulletPoint: string;
        /**
         * The text "
      Upgrade
    "
         */
        readonly workspaceUpgradeButtonText: string;
        /**
         * The text "
      Upgrade workspace to access Enterprise (preview) capabilities. Visit our {0} for more information.
    "
         */
        readonly workspaceUpgradeConfirmationBoxContent: string;
        /**
         * The text "
      Confirm workspace upgrade
    "
         */
        readonly workspaceUpgradeConfirmationBoxTitle: string;
        /**
         * The text "
      Learn more
    "
         */
        readonly workspaceUpgradeLearnMore: string;
        /**
         * The text "
      pricing page
    "
         */
        readonly workspaceUpgradePricingPage: string;
        /**
         * The text "
      Upgrade this workspace to Enterprise edition (preview) to use UI-based tools for all skill levels and more.
    "
         */
        readonly workspaceUpgradeQuickLinkBannerText: string;
        /**
         * The text "
      Learn about Enterprise Edition (preview)
    "
         */
        readonly workspaceUpgradeQuickLinkLearnAboutEnterpriseEdition: string;
        /**
         * The text "
      Use the Enterprise edition (preview) to access UI-based tools for all skill levels, built-in MLOps and more
    "
         */
        readonly workspaceUpgradeQuickLinkPostUpgradeBannerText: string;
        /**
         * The text "
      This feature is disabled for you. Please upgrade to an Enterprise edition (preview) workspace to configure quotas.
    "
         */
        readonly workspaceUpgradeSetQuotaOperationNotAllowed: string;
        /**
         * The text "
      Your workspace {0} upgraded successfully.
    "
         */
        readonly workspaceUpgradeUpgradeCompleteNotificationContent: string;
        /**
         * The text "
      Workspace Upgrade Complete
    "
         */
        readonly workspaceUpgradeUpgradeCompleteNotificationTitle: string;
        /**
         * The text "
      An unexpected error occurred, please try upgrading your workspace again, or submitting a support ticket.
    "
         */
        readonly workspaceUpgradeUpgradeFailed: string;
        /**
         * The text "
      Your workspace {0} did not upgrade successfully.
    "
         */
        readonly workspaceUpgradeUpgradeFailedNotificationContent: string;
        /**
         * The text "
      Workspace Upgrade Failed
    "
         */
        readonly workspaceUpgradeUpgradeFailedNotificationTitle: string;
        /**
         * The text "
      Upgrade Azure Machine Learning workspace {0} succeeded.
    "
         */
        readonly workspaceUpgradeUpgradeSucceeded: string;
        /**
         * The text "
      Upgrading Azure Machine Learning workspace {0}
    "
         */
        readonly workspaceUpgradeUpgrading: string;
        /**
         * The text "
      Your workspace {0} is upgrading from Basic to Enterprise
    "
         */
        readonly workspaceUpgradeUpgradingNotificationContent: string;
        /**
         * The text "
      Workspace is currently upgrading
    "
         */
        readonly workspaceUpgradeUpgradingNotificationTitle: string;
    };
}