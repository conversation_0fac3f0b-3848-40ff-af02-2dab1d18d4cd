import DeploymentData = require("./DataModels/OnlineDeploymentDataModel");
import Utilities from "../Shared/Utilities";
import { Logging, ActionModifier } from "../Shared/Logging";
import Constants = require("../Shared/Constants");
import ExtensionDefinition = require("../_generated/ExtensionDefinition");
import * as Di from "Fx/DependencyInjection";

@Di.Class()
export class OnlineDeploymentData {
    public armEndpoint: string = MsPortalFx.getEnvironmentValue("armEndpoint");
    public endpointApiVersion: string = "api-version=" + MsPortalFx.getEnvironmentValue("onlineEndpointsApiVersion");
    public workspaceApiVersion: string = "api-version=" + MsPortalFx.getEnvironmentValue("machineLearningServicesApiVersion");
    public locationsApiVersion: string = "api-version=" + MsPortalFx.getEnvironmentValue("locationsApiVersion");
    private static DataModelsOnlineDeploymentType = "Microsoft_Azure_MLTeamAccounts.DataModels.OnlineDeployment";
    private static source = "OnlineDeploymentData";

    public deploymentsQuery = new MsPortalFx.Data.QueryCache<DeploymentData.OnlineDeployment, any>({
        entityTypeName: OnlineDeploymentData.DataModelsOnlineDeploymentType,
        sourceUri: MsPortalFx.Data.uriFormatter(this.armEndpoint + `/subscriptions/{subscriptonId}/providers/${Constants.machineLearningServicesResourcesProvider}/${Constants.machineLearningWorkspaces}/{workspaceId}/${Constants.OnlineEndpoints.onlineEndpointsResourceName}/{endpointId}/${Constants.OnlineEndpoints.deploymentsResourceName}/{deploymentId}?` + this.endpointApiVersion, false),
        supplyData: (httpMethod: string, uri: string, headers?: StringMap<any>, data?: any, params?: any) => {
            return Q(MsPortalFx.Base.Net2.ajax({
                uri: uri,
                type: httpMethod || "GET",
                dataType: "json",
                headers: headers,
                contentType: "application/json",
                setAuthorizationHeader: true,
                invokeApi: "api/invoke",
                data: data
            }).catch((failureResponse: MsPortalFx.Base.Net2.ErrorResponse<any>) => {
                Logging.LogAjaxFailure(failureResponse.jqXHR, "deploymentsQuery");
            }));
        },
        processServerResponse: (response) => {
            return {
                data: response && response.value
            };
        }
    });

    public onlineDeploymentsQuery = new MsPortalFx.Data.QueryCache<DeploymentData.OnlineDeployment, any>({
        entityTypeName: OnlineDeploymentData.DataModelsOnlineDeploymentType,
        sourceUri: (params: any) => { return params; },
        supplyData: (httpMethod: string, uri: string, headers: StringMap<any>, data: any, params: any, entryLifetime: MsPortalFx.Base.LifetimeManager) => {
            var resourceIds: string[] = params;
            if (!resourceIds || resourceIds.length === 0) {
                return Q([]);
            }

            var defer = Q.defer();
            Q.all(resourceIds.map(
                (resourceId) => {
                    var deploymentsQueryView = this.deploymentsQuery.createView(entryLifetime);
                    return Q(deploymentsQueryView.fetch(resourceId)).then(
                        () => {
                            return ko.toJS(deploymentsQueryView.items());
                        });
                })).then(
                    (value) => {
                        var allTeamAccounts = [].concat.apply([], value);
                        defer.resolve(allTeamAccounts);
                    },
                    (reason) => {
                        defer.reject(reason);
                    });
            return defer.promise;
        },
        poll: true
    });

    public onlineDeploymentEntity = new MsPortalFx.Data.EntityCache<DeploymentData.OnlineDeployment, string>({
        entityTypeName: OnlineDeploymentData.DataModelsOnlineDeploymentType,
        sourceUri: MsPortalFx.Data.uriFormatter(this.armEndpoint + "{id}?" + this.endpointApiVersion, false),
        supplyData: (httpMethod: string, uri: string, headers?: StringMap<any>, data?: any, params?: any) => {
            var resourceId = params;

            return Utilities.armGetRequest(uri).then((response) => response,
                (failureResponse: MsPortalFx.Base.Net2.ErrorResponse<any>) => {
                    if (failureResponse.jqXHR.status === MsPortalFx.Base.Net2.HttpStatusCode.NotFound) {
                        MsPortalFx.UI.AssetManager.notifyAssetDeleted(ExtensionDefinition.AssetTypeNames.machineLearningServices, resourceId);
                    } else {
                        Logging.LogAjaxFailure(failureResponse.jqXHR, "onlineDeploymentEntity");
                    }
                    return failureResponse;
                });
        }
    });
}