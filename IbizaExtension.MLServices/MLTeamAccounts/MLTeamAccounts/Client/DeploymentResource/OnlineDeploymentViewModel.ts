import { BladeReferences } from "Fx/Composition";
import * as deploymentDataModels from "./DataModels/OnlineDeploymentDataModel";
import { ResourceMenuBladeIds, MenuItemIds } from "../Shared/Constants";
import { Icons } from "../Shared/Icons";
import { DataContext } from "./DeploymentResourceArea";
import * as Di from "Fx/DependencyInjection";
import * as ClientResources from "Resx/ClientResources";
import { Logging } from "Shared/Logging";
import FxAssets = MsPortalFx.Assets;
import { Experimentation } from "Fx/Experimentation";
import FxBase = MsPortalFx.Base;
import AssetsApi = MsPortalFx.Assets;
import ResourceTypesService = MsPortalFx.ViewModels.Services.ResourceTypes;

module BrowseColumns {
    "use strict";

    export var deploymentName = "deploymentName";
    export var deploymentKind = "deploymentKind";
    export var deploymentState = "deploymentState";
}

/**
 * ResourceAssetType that implements Resource Menu and Browse Contract
 * Learn more about Browse: https://aka.ms/portalfx/browse
 * Learn more about Resource Menu: https://aka.ms/portalfx/resourcemenu
 */
@Di.Class("viewModel")
export class OnlineDeploymentViewModel
    implements FxAssets.ResourceMenuWithCallerSuppliedResourceContract, FxAssets.BrowseConfigContract, MsPortalFx.Assets.SupplementalDataContract {
    /**
     * The container contains APIs you can call to interact with the shell.
     */
    private _container: MsPortalFx.ViewModels.ContainerContract;

    /**
     * The context property contains APIs you can call to interact with the shell.
     * Learn more about: https://aka.ms/portalfx/datacontext
     */
    private _runningSupplementalDataFetch: Promise<unknown>;
    private _dataContext: DataContext;
    private _supplementalDataView: MsPortalFx.Data.QueryView<deploymentDataModels.OnlineDeployment, any>;
    private _experimentation: Experimentation = null;

    public supplementalDataStream = ko.observableArray<AssetsApi.SupplementalData>([]);

    /**
     * Constructor for Asset Type
     * @param container
     * @param initialState
     * @param DataContext
     */
    constructor(container: MsPortalFx.ViewModels.ContainerContract, dataContext: DataContext, experimentation: Experimentation) {
        this._container = container;
        this._dataContext = dataContext;
        this._experimentation = experimentation;
    }

    /**
     * Specifies the Browse configuration such as columns
     */
    public getBrowseConfig(): Promise<FxAssets.BrowseConfig> {
        return Q({
            columns: [
                {
                    id: BrowseColumns.deploymentName,
                    name: ko.observable<string>(ClientResources.deploymentName),
                    itemKey: BrowseColumns.deploymentName
                },
                {
                    id: BrowseColumns.deploymentKind,
                    name: ko.observable<string>(ClientResources.deploymentKind),
                    itemKey: BrowseColumns.deploymentKind
                },
                {
                    id: BrowseColumns.deploymentState,
                    name: ko.observable<string>(ClientResources.deploymentProvisioningStateLabel),
                    itemKey: BrowseColumns.deploymentState
                }
            ],
            defaultColumns: ["resourceGroup", "location"]
        });
    }

    /**
     * The menu config for the Resource menu blade
     * @param resourceInfo The resource informaiton
     */
    public getMenuConfig(resourceInfo: FxAssets.ResourceInformation): Promise<FxAssets.ResourceMenuConfig> {
        const { resourceId } = resourceInfo;

        const overviewItem: FxAssets.MenuItem = {
            id: ResourceMenuBladeIds.overview, // menu item IDs must be unique, must not be localized, should not contain spaces and should be lowercase
            displayText: ClientResources.overview,
            enabled: ko.observable(true),
            keywords: ClientResources.overviewKeywords,
            icon: Icons.cloudService,
            supplyBladeReference: () => {
                return BladeReferences.forBlade("OnlineDeploymentOverviewBlade").createReference({
                    parameters: {
                        id: resourceId
                    }
                });
            }
        };

        return Q.all([this._dataContext.deploymentData.onlineDeploymentEntity.fetch(resourceId, null)]).then(
            values => {
                const scalingItem: FxAssets.MenuItem = {
                    id: "scaling",
                    displayText: ClientResources.scaling,
                    enabled: ko.observable(true),
                    keywords: [
                        ClientResources.Keyword.alert,
                        ClientResources.Keyword.audit,
                        ClientResources.Keyword.instance,
                        ClientResources.Keyword.log,
                        ClientResources.Keyword.rules,
                        ClientResources.Keyword.scale,
                        ClientResources.Keyword.scaling
                    ],
                    icon: MsPortalFx.Base.Images.Polychromatic.Scale(),
                    supplyBladeReference: () => {
                        return BladeReferences.forExtension("Microsoft_Azure_Monitoring").forBlade("AutoScaleSettingsBlade").createReference({
                            parameters: {
                                resourceId
                            }
                        });
                    }
                };

                const d = values[0].data();
                const settingsGroup: FxAssets.MenuGroup = {
                    id: FxAssets.ManagementGroupId,
                    displayText: ClientResources.titleSettings,
                    items: d.kind && d.kind() === "Managed" ? [scalingItem] : []
                };

                // build the resource menu config.
                const menuConfig: FxAssets.ResourceMenuConfig = {
                    overview: overviewItem,
                    options: {
                        // resourceMenuOptions information: https://github.com/Azure/portaldocs/blob/master/portal-sdk/generated/portalfx-resourcemenu-api.md
                        enableRbac: true,
                        enableTags: true,
                        enableMetrics: true,
                        enableDiagnostics: false,
                        enableAlerts: true,
                        enableLogs: true,
                        enableSupportTroubleshootV2: true,
                        enableSupportResourceHealth: false,
                        enableSupportHelpRequest: true,
                        enableSupportEventLogs: true,
                        enableEventGridPublisher: true
                    },
                    groups: [settingsGroup].filter(g => g.items.length > 0)
                };

                return Q(menuConfig);
            },
            _reason => {
                // If any failure getting flights, show original menu
                // build the resource menu config.
                const menuConfig: FxAssets.ResourceMenuConfig = {
                    overview: overviewItem,
                    options: {
                        // resourceMenuOptions information: https://github.com/Azure/portaldocs/blob/master/portal-sdk/generated/portalfx-resourcemenu-api.md
                        enableRbac: true,
                        enableTags: true,
                        enableMetrics: true,
                        enableDiagnostics: false,
                        enableAlerts: true,
                        enableLogs: true,
                        enableSupportTroubleshootV2: true,
                        enableSupportResourceHealth: false,
                        enableSupportHelpRequest: true,
                        enableSupportEventLogs: true,
                        enableEventGridPublisher: true
                    },
                    groups: [].filter(g => g.items.length > 0)
                };

                return Q(menuConfig);
            }
        );
    }

    public getSupplementalData(resourceIds: string[], columns: string[]): Promise<unknown> {
        this._supplementalDataView =
            this._supplementalDataView || this._dataContext.deploymentData.onlineDeploymentsQuery.createView(this._container);

        // connect the view to the supplemental data stream
        AssetsApi.SupplementalDataStreamHelper.ConnectView(
            this._container,
            this._supplementalDataView,
            this.supplementalDataStream,
            (deployment: deploymentDataModels.OnlineDeployment) => {
                return resourceIds.some(resourceId => {
                    return ResourceTypesService.compareResourceId(resourceId, deployment.id());
                });
            },
            (deployment: deploymentDataModels.OnlineDeployment) => {
                // save the resource id so Browse knows which row to update
                var supplementalData = <AssetsApi.SupplementalData>{ resourceId: deployment.id() };

                if (columns.indexOf(BrowseColumns.deploymentName) !== -1) {
                    supplementalData[BrowseColumns.deploymentName] = deployment.name();
                }

                if (columns.indexOf(BrowseColumns.deploymentKind) !== -1) {
                    supplementalData[BrowseColumns.deploymentKind] = deployment.kind();
                }

                if (columns.indexOf(BrowseColumns.deploymentState) !== -1) {
                    supplementalData[BrowseColumns.deploymentState] = deployment.properties().provisioningState();
                }

                return supplementalData;
            }
        );

        const latencyTelemetryEvent = Logging.StartLatencyTelemetryEvent("OnlineDeploymentViewModel", "getSupplementalData", {
            resourceIds
        });
        const supplementalDataPromise = this._runFetchForSupplementalData(resourceIds);
        supplementalDataPromise.then(() => {
            Logging.EndLatencyTelemetryEvent(latencyTelemetryEvent);
        });

        return supplementalDataPromise;
    }

    private _runFetchForSupplementalData(resourceIds: string[]): Promise<unknown> {
        // make sure multiple fetches don't step on each other.
        if (!this._runningSupplementalDataFetch) {
            this._runningSupplementalDataFetch = this._supplementalDataView.fetch(resourceIds).then(() => {
                this._runningSupplementalDataFetch = null;
            });
        }
        return this._runningSupplementalDataFetch;
    }
}
