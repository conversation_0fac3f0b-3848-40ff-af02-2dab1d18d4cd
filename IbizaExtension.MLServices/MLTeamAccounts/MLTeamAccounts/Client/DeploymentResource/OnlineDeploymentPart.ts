﻿import * as ClientResources from "Resx/ClientResources";
import * as TemplatePart from "Fx/Composition/TemplatePart";
import { BladeReferences } from "Fx/Composition";
import { DataContext } from "./DeploymentResourceArea";
import Icons = require("../Shared/Icons");

/**
 * Resource Part that implements pinned part for Overview blade
 * Learn more about decorator based parts at: https://aka.ms/portalfx/nopdl
 */
@TemplatePart.Decorator({
    htmlTemplate:
        "<div class='msportalfx-padding'>" +
        "  <div style='height: 20px; width: 20px;' data-bind='image: customIcon'></div>" +
        "</div>",
    styleSheets: ["../Shared/Styles/CustomStyles.css"],
    resizeMode: TemplatePart.ResizeMode.User,
    initialSize: TemplatePart.Size.Custom,
    initialHeight: 1,
    initialWidth: 2
})
@TemplatePart.InjectableModel.Decorator(DataContext)
export class OnlineDeploymentPart {

    public title: string;
    public subtitle: string;
    public customIcon: MsPortalFx.Base.Image;

    /**
     * The context property contains APIs you can call to interact with the shell.
     * It will be populated for you by the framework before your onInitialize() function is called.
     *   https://aka.ms/portalfx/nopdl/context
     */
    public context: TemplatePart.Context<OnlineDeploymentBlade.OnlineDeploymentBladeParameters, DataContext>;

    public onInitialize() {
        this.title = MsPortalFx.ViewModels.Services.ResourceTypes.parseResourceDescriptor(
            this.context.parameters.id).resource;
        this.subtitle = ClientResources.AssetTypeNames.MLAppDeployment.singular;
        this.customIcon = Icons.Icons.webWorkspaceIcon;
        return Q();
    }

    public onClick() {
        const { container, parameters } = this.context;
        return container.openBlade(BladeReferences.forBlade("OnlineDeploymentOverviewBlade").createReference({parameters}));
    }
}