import { ClickableLink } from "Fx/Composition";
import * as TemplateBlade from "Fx/Composition/TemplateBlade";
import * as CustomHtml from "Fx/Controls/CustomHtml";
import * as Essentials from "Fx/Controls/Essentials";
import { ResourceLayoutContract } from "Fx/Controls/Essentials";
import * as MonitorChartV2 from "Fx/Controls/MonitorChartV2";
import * as Section from "Fx/Controls/Section";
import * as ClientResources from "Resx/ClientResources";
import Card from "../../../Shared/Cards";
import { Logging } from "../../../Shared/Logging";
import { DataContext } from "../../DeploymentResourceArea";

import Constants = require("../../../Shared/Constants");
import Icons = require("../../../Shared/Icons");

import ExtensionDefinition = require("../../../_generated/ExtensionDefinition");

import deploymentDataModels = require("../../DataModels/OnlineDeploymentDataModel");
import Toolbars = MsPortalFx.ViewModels.Toolbars;
import Images = MsPortalFx.Base.Images;

/**
 * Overview blade provides the overview of resource on resource menu.
 * Learn more about decorator based blades at: https://aka.ms/portalfx/nopdl
 */
@TemplateBlade.Decorator({
    htmlTemplate:
        `
    <div data-bind='pcControl: essentialsViewModel'></div>
    <div class='msportalfx-padding'>
        <div class='ext-ml-chart'>
            <div data-bind='pcControl: cpuUtilizationChart'></div>
        </div>
        <div class='ext-ml-chart'>
            <div data-bind='pcControl: deploymentCapacityChart'></div>
        </div>
        <div class='ext-ml-chart'>
            <div data-bind='pcControl: diskUtilizationChart'></div>
        </div>
        <div class='ext-ml-chart'>
            <div data-bind='pcControl: memoryUtilizationChart'></div>
        </div>
    </div>
    <div class='msportalfx-padding'>
        <div class='msportalfx-form ext-ml-section-header' data-bind='pcControl: infoSection'></div>
        <div class='ext-ml-card'>
            <div data-bind='pcControl: mlsDocumentationCard'></div>
        </div>
        <div class='ext-ml-card'>
            <div data-bind='pcControl: mlsForumCard'></div>
        </div>
    </div>
    `,
    styleSheets: ["../../../Shared/Styles/CustomStyles.css"],
    isPinnable: false,
    forAsset: { assetType: ExtensionDefinition.AssetTypeNames.machineLearningServices, assetIdParameter: "id" },
    forExport: true
})
@TemplateBlade.InjectableModel.Decorator(DataContext)
export class OnlineDeploymentOverviewBlade {
    public title = "";
    public subtitle = "";
    public context: TemplateBlade.Context<OnlineDeploymentBlade.OnlineDeploymentBladeParameters, DataContext>;

    public essentialsViewModel: KnockoutObservable<ResourceLayoutContract>;
    public infoSection: Section.Contract;

    public mlsDocumentationCard: CustomHtml.Contract;
    public mlsForumCard: CustomHtml.Contract;

    // Chart UI elements
    public cpuUtilizationChart: MonitorChartV2.Contract;
    public deploymentCapacityChart: MonitorChartV2.Contract;
    public diskUtilizationChart: MonitorChartV2.Contract;
    public memoryUtilizationChart: MonitorChartV2.Contract;

    private _deploymentEntityView: MsPortalFx.Data.EntityView<deploymentDataModels.OnlineDeployment, string>;
    private _onlineDeployment: deploymentDataModels.OnlineDeployment = null;

    private _downloadCommandButton: MsPortalFx.ViewModels.Toolbars.FileDownloadButton;

    private _modelId: Essentials.Item = null;
    private _name: Essentials.Item = null;
    private _kind: Essentials.Item = null;
    private _provisioningState: Essentials.Item = null;
    private _environmentId: Essentials.Item = null;

    public onInitialize() {
        const { container, parameters } = this.context;

        this._setInitialState();

        this.infoSection = Section.create(container, {
            name: ClientResources.titleMonitoringLens,
            children: []
        });

        return this._loadOnlineDeployment(parameters.id);
    }

    private _setInitialState(): void {
        const { model, container } = this.context;
        this._deploymentEntityView = model.deploymentData.onlineDeploymentEntity.createView(container);
    }

    private _loadOnlineDeployment(resourceId: string): Q.Promise<void> {
        const { container, model } = this.context;
        const deferred = Q.defer();

        const deploymentPromise = this._deploymentEntityView.fetch(resourceId);
        const permissionsPromise = MsPortalFx.Base.Security.hasPermission(resourceId, [ExtensionDefinition.AssetTypes.MachineLearningServices.Permissions.readServices]);

        Q.all([permissionsPromise, deploymentPromise]).then(async (values) => {
            const hasAccess = values[0];

            this._onlineDeployment = this._deploymentEntityView.item();

            this.title = this._onlineDeployment.name();
            this._initializeCommandBar();
            this._initializeEssentials();
            await this._initializeLinks();

            if (!hasAccess) {
                container.unauthorized();
            } else {
                deferred.resolve();
            }
        }, (reason) => {
            deferred.reject(reason);
        });
        return deferred.promise;
    }

    // Initializes properties for the essentials part
    private _initializeEssentials(): void {
        const { container, parameters } = this.context;
        const resourceId = parameters.id;
        const workspaceId = parameters.id.split("/".concat(Constants.OnlineEndpoints.onlineEndpointsResourceName),1)[0];
        const endpointId = parameters.id.split("/".concat(Constants.OnlineEndpoints.deploymentsResourceName),1)[0];

        if (!MsPortalFx.isNullOrUndefined(this._onlineDeployment)
            && !MsPortalFx.isNullOrUndefined(this._onlineDeployment.properties()))
        {
            this._name = {
                label: ClientResources.deploymentName,
                value: ko.observable<string>(
                    this._onlineDeployment.name() !== ""
                        ? this._onlineDeployment.name()
                        : Constants.pendingStringValue
                )
            };

            this._kind = {
                label: ClientResources.deploymentKind,
                value: ko.observable<string>(
                    this._onlineDeployment.kind() !== ""
                        ? this._onlineDeployment.kind()
                        : Constants.pendingStringValue
                )
            };

            this._provisioningState = {
                label: ClientResources.deploymentProvisioningStateLabel,
                value: ko.observable<string>(
                    this._onlineDeployment.properties().provisioningState() !== ""
                        ? this._onlineDeployment.properties().provisioningState()
                        : Constants.pendingStringValue
                )
            };

            this._environmentId = {
                label: ClientResources.deploymentEnvironmentId,
                value: ko.observable<string>(
                    this._onlineDeployment.properties().environmentId() !== ""
                        ? this._onlineDeployment.properties().environmentId()
                        : Constants.pendingStringValue
                )
            };
        }

        const workspaceProperty = {
            label: ClientResources.onlineEndpointWorkspaceName,
            value: ko.computed(container, () => {
                return !MsPortalFx.isNullOrUndefined(workspaceId) && workspaceId !== "" ?
                    MsPortalFx.ViewModels.Services.ResourceTypes.parseResourceDescriptor(workspaceId).resource :
                    Constants.pendingStringValue;
            }),
            onClick: () => {
                const bladeReference = ko.computed(container, () => {
                    return !MsPortalFx.isNullOrUndefined(workspaceId) && workspaceId !== "" ?
                        MsPortalFx.Assets.mapResourceIdToBladeReference(workspaceId) :
                        null;
                });
                container.openBladeAsync(Q(bladeReference()));
            }
        };

        const endpointProperty = {
            label: ClientResources.onlineEndpointName,
            value: ko.computed(container, () => {
                return !MsPortalFx.isNullOrUndefined(endpointId) && endpointId !== "" ?
                    MsPortalFx.ViewModels.Services.ResourceTypes.parseResourceDescriptor(endpointId).resource :
                    Constants.pendingStringValue;
            }),
            onClick: () => {
                const bladeReference = ko.computed(container, () => {
                    return !MsPortalFx.isNullOrUndefined(endpointId) && endpointId !== "" ?
                        MsPortalFx.Assets.mapResourceIdToBladeReference(endpointId) :
                        null;
                });
                container.openBladeAsync(Q(bladeReference()));
            }
        };

        const leftItems: (Essentials.BuiltInType | Essentials.Item)[] = [
                Essentials.BuiltInType.ResourceGroup,
                Essentials.BuiltInType.Location,
                Essentials.BuiltInType.SubscriptionName,
                Essentials.BuiltInType.SubscriptionId,
                workspaceProperty
            ];

        const essentialsOptions: Essentials.CustomResourceLayoutOptions = {
            resourceId: resourceId,
            left: leftItems,
            right: [
                endpointProperty,
                this._name,
                this._kind,
                this._provisioningState,
                this._environmentId
            ],
            hiddenChangeLink: [
                Essentials.BuiltInType.ResourceGroup,
                Essentials.BuiltInType.SubscriptionName
            ]
        };

        const essentialsVM = Essentials.createCustomResourceLayout(container, essentialsOptions);
        this.essentialsViewModel = ko.observable(essentialsVM);
    }

    private async _initializeLinks(): Promise<void> {
        const { container, menu, parameters } = this.context;

        const machineLearningServicesDocumentationUrl = MsPortalFx.getEnvironmentValue("machineLearningServicesDocumentationUrl");
        const mlsDocumentationClickableLink = new ClickableLink(machineLearningServicesDocumentationUrl, '_blank', undefined, (keypress) => {
            Logging.LogInfoBoxUrlItemClick("machineLearningServicesDocumentation", machineLearningServicesDocumentationUrl);
        });

        const machineLearningServicesForumUrl = MsPortalFx.getEnvironmentValue("machineLearningServicesForumUrl");
        const mlsForumClickableLink = new ClickableLink(machineLearningServicesForumUrl, '_blank', undefined, (keypress) => {
            Logging.LogInfoBoxUrlItemClick("machineLearningForum", machineLearningServicesForumUrl);
        });

        const cpuUtilizationMetricOptions: MonitorChartV2.Metric.Options = {
            resourceMetadata: { id: parameters.id },
            name: "CpuUtilizationPercentage",
            aggregationType: MonitorChartV2.Metric.AggregationType.Avg,
        };

        const deploymentCapacityMetricOptions: MonitorChartV2.Metric.Options = {
            resourceMetadata: { id: parameters.id },
            name: "DeploymentCapacity",
            aggregationType: MonitorChartV2.Metric.AggregationType.Avg,
        };

        const diskUtilizationMetricOptions: MonitorChartV2.Metric.Options = {
            resourceMetadata: { id: parameters.id },
            name: "DiskUtilization",
            aggregationType: MonitorChartV2.Metric.AggregationType.Avg,
        };

        const memoryUtilizationMetricOptions: MonitorChartV2.Metric.Options = {
            resourceMetadata: { id: parameters.id },
            name: "CpuMemoryUtilizationPercentage",
            aggregationType: MonitorChartV2.Metric.AggregationType.Avg,
        };

        this.mlsDocumentationCard = Card.createCard(container, {
            clickableLink: mlsDocumentationClickableLink,
            header: ClientResources.textMachineLearningServicesDocumentationLinkHeader,
            headerAriaLabel: `${ClientResources.quickLinkUnderOverviewBladeAriaLabel} ${ClientResources.textMachineLearningServicesDocumentationLinkHeader}.`,
            body: ClientResources.textMachineLearningServicesDocumentationLinkBody,
            bodyAriaDescription: ClientResources.textMachineLearningServicesDocumentationLinkBody,
            icon: Icons.Icons.documentationIcon
        });
        this.mlsForumCard = Card.createCard(container, {
            clickableLink: mlsForumClickableLink,
            header: ClientResources.textMachineLearningServicesForumLinkHeader,
            headerAriaLabel: `${ClientResources.quickLinkUnderOverviewBladeAriaLabel} ${ClientResources.textMachineLearningServicesForumLinkHeader}.`,
            body: ClientResources.textMachineLearningServicesForumLinkBody,
            bodyAriaDescription: ClientResources.textMachineLearningServicesForumLinkBody,
            icon: Icons.Icons.forumIcon
        });

        defaultChartInputs.metrics = [cpuUtilizationMetricOptions];
        defaultChartInputs.title = ClientResources.deploymentCpuUtilizationMetric;
        this.cpuUtilizationChart = MonitorChartV2.create(container, defaultChartInputs);

        defaultChartInputs.metrics = [deploymentCapacityMetricOptions];
        defaultChartInputs.title = ClientResources.deploymentCapacityMetric;
        this.deploymentCapacityChart = MonitorChartV2.create(container, defaultChartInputs);

        defaultChartInputs.metrics = [diskUtilizationMetricOptions];
        defaultChartInputs.title = ClientResources.deploymentDiskUtilizationMetric;
        this.diskUtilizationChart = MonitorChartV2.create(container, defaultChartInputs);

        defaultChartInputs.metrics = [memoryUtilizationMetricOptions];
        defaultChartInputs.title = ClientResources.deploymentMemoryUtilizationMetric;
        this.memoryUtilizationChart = MonitorChartV2.create(container, defaultChartInputs);
    }

    private _createDownloadConfigCommand(): Toolbars.FileDownloadButton {
        const { parameters } = this.context;

        const resourceDescriptor = MsPortalFx.ViewModels.Services.ResourceTypes.parseResourceDescriptor(parameters.id);
        const workspaceConfig = {
            subscription_id: resourceDescriptor.subscription,
            resource_group: resourceDescriptor.resourceGroup,
            workspace_name: resourceDescriptor.resources[0],
            online_endpoint: resourceDescriptor.resources[1],
            deployment_name: resourceDescriptor.resource
        };

        return new Toolbars.FileDownloadButton({
            label: ClientResources.commandDownloadConfig,
            tooltip: ClientResources.commandDownloadConfigTooltip,
            icon: Images.Download(),
            targetUri: ko.observable<string>("data:application/json," + encodeURIComponent(JSON.stringify(workspaceConfig, null, 4))),
            targetFileName: ko.observable<string>("config.json")
        });
    }

    private _initializeCommandBar(): void {
        const { container } = this.context;
        const commandBar = new Toolbars.Toolbar(container);

        this._downloadCommandButton = this._createDownloadConfigCommand();

        commandBar.setItems([
            this._downloadCommandButton,
        ]);
        container.commandBar = commandBar;
    }
}

const defaultChartInputs: MonitorChartV2.Options = {
    metrics: [],
    openBladeOnClick: {       // Specify no click behavior
        openBlade: false,
    },
    timespan: {
        relative: {
            duration: 1 * 24 * 60 * 60 * 1000
        },
    },
};