﻿/// <amd-bundling generated="typemetadata" />
/// <amd-dependency path="./OnlineDeploymentProperties" />
import { OnlineDeploymentProperties, OnlineDeploymentPropertiesMetadata } from "./OnlineDeploymentProperties";

export interface OnlineDeployment {
    id: KnockoutObservable<string>;
    name: KnockoutObservable<string>;
    type: KnockoutObservable<string>;
    location: KnockoutObservable<string>;
    kind: KnockoutObservable<string>;
    properties: KnockoutObservable<OnlineDeploymentProperties>;
}

export const OnlineDeploymentMetadata: MsPortalFx.Data.Metadata.Metadata = {
    "name": "Microsoft_Azure_MLTeamAccounts.DataModels.OnlineDeployment",
    "properties": {
        "properties": {
            "itemType": OnlineDeploymentPropertiesMetadata.name
        },
        "id": {},
        "name": {},
        "type": {},
        "location": {},
        "kind": {}
    },
    "idProperties": [
        "id"
    ],
    "entityType": true,
    "hasGloballyUniqueId": true
};

MsPortalFx.Data.Metadata.setTypeMetadata(OnlineDeploymentMetadata.name, OnlineDeploymentMetadata);
