﻿/// <amd-bundling generated="typemetadata" />

export interface DeploymentEnvironmentOverrides {
    environmentVariables: KnockoutObservable<StringMap<string>>;
}

export const DeploymentEnvironmentOverridesMetadata: MsPortalFx.Data.Metadata.Metadata = {
    "name": "Microsoft_Azure_MLTeamAccounts.DataModels.DeploymentEnvironmentOverridesMetadata",
    "properties": {
        "environmentVariables": {}
    },
    "idProperties": [],
    "entityType": true,
    "hasGloballyUniqueId": true
};

MsPortalFx.Data.Metadata.setTypeMetadata(DeploymentEnvironmentOverridesMetadata.name, DeploymentEnvironmentOverridesMetadata);

export interface DeploymentCodeConfiguration {
    codeId: KnockoutObservable<string>;
    scoringScript: KnockoutObservableArray<string>;
}

export const DeploymentCodeConfigurationMetadata: MsPortalFx.Data.Metadata.Metadata = {
    "name": "Microsoft_Azure_MLTeamAccounts.DataModels.DeploymentCodeConfigurationMetadata",
    "properties": {
        "codeId": {},
        "scoringScript": {}
    },
    "idProperties": [],
    "entityType": true,
    "hasGloballyUniqueId": true
};

MsPortalFx.Data.Metadata.setTypeMetadata(DeploymentCodeConfigurationMetadata.name, DeploymentCodeConfigurationMetadata);

export interface DeploymentScaleSettings {
    minInstances: KnockoutObservable<string>;
    maximum: KnockoutObservable<string>;
    instanceCount: KnockoutObservable<string>;
    scaleType: KnockoutObservable<string>;
}

export const DeploymentScaleSettingsMetadata: MsPortalFx.Data.Metadata.Metadata = {
    "name": "Microsoft_Azure_MLTeamAccounts.DataModels.DeploymentScaleSettingsMetadata",
    "properties": {
        "minInstances": {},
        "maxInstances": {},
        "instanceCount": {},
        "scaleType": {}
    },
    "idProperties": [],
    "entityType": true,
    "hasGloballyUniqueId": true
};

MsPortalFx.Data.Metadata.setTypeMetadata(DeploymentScaleSettingsMetadata.name, DeploymentScaleSettingsMetadata);

export interface OnlineDeploymentProperties {
    description: KnockoutObservable<string>;
    type: KnockoutObservable<string>;
    environmentId: KnockoutObservable<string>;
    codeConfiguration: KnockoutObservable<DeploymentCodeConfiguration>;
    provisioningState: KnockoutObservable<string>;
    scaleSettings: KnockoutObservable<DeploymentScaleSettings>;
    environmentOverrides: KnockoutObservable<DeploymentEnvironmentOverrides>;
}

export const OnlineDeploymentPropertiesMetadata: MsPortalFx.Data.Metadata.Metadata = {
    "name": "Microsoft_Azure_MLTeamAccounts.DataModels.OnlineEnpointPropertiesMetadata",
    "properties": {
        "description": {},
        "type": {},
        "environmentId": {},
        "codeConfiguration": {
            "itemType": DeploymentCodeConfigurationMetadata.name
        },
        "provisioningState": {},
        "scaleSettings": {
            "itemType": DeploymentScaleSettingsMetadata.name
        },
        "environmentOverrides": {
            "itemType": DeploymentEnvironmentOverridesMetadata.name
        }
    },
    "idProperties": [],
    "entityType": true,
    "hasGloballyUniqueId": true
};

MsPortalFx.Data.Metadata.setTypeMetadata(OnlineDeploymentPropertiesMetadata.name, OnlineDeploymentPropertiesMetadata);
