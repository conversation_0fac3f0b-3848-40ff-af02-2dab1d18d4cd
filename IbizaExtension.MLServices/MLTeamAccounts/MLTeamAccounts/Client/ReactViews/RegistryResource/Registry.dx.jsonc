{"$schema": "../node_modules/@microsoft/azureportal-build/lib/definitions/dx.schema.json", "area": "DeploymentResource", "stringSource": "../Resx/ClientResources.resjson", "assetType": {"name": "Registry", "displayNames": {"singular": "AssetTypeNames_MLRegistry.singular", "plural": "AssetTypeNames_MLRegistry.plural", "lowerSingular": "AssetTypeNames_MLRegistry.lowerSingular", "lowerPlural": "AssetTypeNames_MLRegistry.lowerPlural"}, "viewModel": {"name": "RegistryViewModel", "module": "../../RegistryResource/RegistryViewModel"}, "icon": {"file": "../Content/SVG/ml_services_icon.svg"}, "part": "RegistryPart", "blade": "RegistryOverviewBlade", "permissions": [{"name": "readServices", "action": "microsoft.machinelearningservices/registries/read"}, {"name": "moveServices", "action": "Microsoft.MachineLearningServices/resourceGroups/moveResources"}], "preview": false, "keywords": "AssetType.keywords", "resourceMenu": {"staticOverview": true, "resourceProvidedBy": "ProvidedByResourceMenu"}, "browse": {"type": "ResourceType", "query": {"file": "./BrowseQuery.kml"}, "defaultColumns": ["FxColumns.ResourceGroup", "FxColumns.Location", "FxColumns.Subscription"], "customConfig": {"useSupplementalData": true}}, "description": "AssetType.description", "resourceType": {"name": "Microsoft.MachineLearningServices/registries", "apiVersion": "2022-10-01-preview"}, "options": ["HideAssetType"]}}