import type { Workspace } from "../../../Client/MLServicesDataModels/Workspace.types";
import { TraceSource, GetWorkspaceAction } from "../Logging/Logging.types";
import { execArmBatch, getArmSettings } from "./queryUtils"

export async function getWorkspace(workspaceId: string, options: {
    getManagedNetworkDetails?: boolean,
    telemetry: {
        source: TraceSource,
        action: GetWorkspaceAction
    }
}) {
    const { APIVersion, endpoint } = getArmSettings();
    const source = `${options.telemetry.source}/getWorkspace`;
    const getManagedNetworkDetails = (options?.getManagedNetworkDetails || false);
    const getManagedNetworkDetailsQuery = `getManagedNetworkDetails=${getManagedNetworkDetails}&`;

    return execArmBatch<Workspace>(source, options.telemetry.action, {
        uri: `${endpoint}/${workspaceId}?${getManagedNetworkDetails ? getManagedNetworkDetailsQuery : "" }api-version=${APIVersion}`
    });
}
