import type { Workspace } from "../../../Client/MLServicesDataModels/Workspace.types";
import { TraceSource, TraceAction } from "../Logging/Logging.types";
import { execArmBatch, getArmSettings } from "./queryUtils";

export async function getProjects(
    subscriptions: string[],
    options: {
        hubResourceId?: string;
        telemetry: {
            source: TraceSource;
        };
    }
) {
    const { APIVersion, endpoint } = getArmSettings("armResourceGraphApiVersion");
    const source = `${options.telemetry.source}/getProjects`;

    const hubResourceIdFilter = options.hubResourceId
        ? `
    | where isnotnull(properties.hubResourceId)
    | extend hubResourceId = tolower(properties.hubResourceId)
    | where hubResourceId == "${options.hubResourceId.toLowerCase()}"`
        : "";

    const query = `resources
    | where type == 'microsoft.machinelearningservices/workspaces'
    | where (kind == @\"Project\") or (kind == @\"Lean\")
    ${hubResourceIdFilter}
    | order by tolower(name) asc`;

    return execArmBatch<{ data: Workspace[]; count: number; totalRecords: number; resultTruncated: string }>(source, TraceAction.LoadProjects, {
        uri: `${endpoint}/providers/Microsoft.ResourceGraph/resources?api-version=${APIVersion}`,
        content: { query: query, subscriptions },
        type: "POST"
    });
}
