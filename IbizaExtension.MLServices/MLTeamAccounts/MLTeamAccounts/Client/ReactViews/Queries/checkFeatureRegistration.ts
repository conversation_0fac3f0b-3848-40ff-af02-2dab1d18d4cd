import type { FeatureResult } from "@azure/arm-features";
import { machineLearningServicesResourcesProvider } from "../../Shared/Constants";
import { TraceAction, TraceSource } from "../Logging/Logging.types";
import { execArmBatch, getArmSettings } from "./queryUtils";

export const checkFeatureRegistration = async (subscriptionId: string, featureName: string, telemetry: {
    source: TraceSource
}) => {
    const { APIVersion, endpoint } = getArmSettings("featuresProviderApiVersion");
    const source = `${telemetry.source}/checkFeatureRegistration`;
    const action = `${TraceAction.CheckFeatureRegistration}/${featureName}`;

    return execArmBatch<FeatureResult>(source, action, {
        uri: `${endpoint}/subscriptions/${subscriptionId}/providers/Microsoft.Features/providers/${machineLearningServicesResourcesProvider}/features/${featureName}?api-version=${APIVersion}`
    });
};
