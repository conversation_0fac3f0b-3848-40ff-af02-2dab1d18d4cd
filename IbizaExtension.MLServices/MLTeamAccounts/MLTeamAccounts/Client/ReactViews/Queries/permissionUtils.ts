import { Permission } from "./getPermissions";

/**
 * Converts a wildcard-style pattern string to Regex, and tests `str` against it.
 *
 * @param wildcard Wildcard style pattern string
 * @param str Search string
 */
function wildTest(wildcard: string, str: string): boolean {
  // escape special characters
  const w = wildcard.replace(/[.+^${}()|[\]\\]/g, "\\$&");
  // case insensitive, "*" => zero or more, Regex match
  const re = new RegExp(
    `^${w
      // Convert * to .*
      .replace(/\*/g, ".*")
      // Convert /.*/ to (/|/.*/). This is because /*/ does not require an extra segment for matches
      .replace(/\/\.\*\//g, "(\\/|\\/.*\\/)")
      // Convert ? to .
      .replace(/\?/g, ".")}$`,
    "i",
  );
  return re.test(str);
}

function allowActionForPermission(action: string, allowedActions?: string[], notAllowedActions?: string[]): boolean {
  const inActions = allowedActions?.some(a => wildTest(a, action));
  // if not even in 'actions' no need to check 'notActions'
  if (!inActions) {
    return false;
  }
  const inNotActions = notAllowedActions?.some(na => wildTest(na, action));
  // if it is in not actions, means that this Permission does not allow the desired action
  return !inNotActions;
}

export function allowActionForPermissions(
  action: string,
  permissionGetResult: Permission[],
): boolean {
  return permissionGetResult.some(p => allowActionForPermission(action, p.actions, p.notActions));
}