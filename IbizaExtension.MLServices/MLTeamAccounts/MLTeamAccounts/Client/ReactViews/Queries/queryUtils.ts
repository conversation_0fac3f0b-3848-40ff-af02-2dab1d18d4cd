import * as Az from "@microsoft/azureportal-reactview/Az";
import { Ajax } from "@microsoft/azureportal-reactview/FxReactCommon.Modules";
import { batch } from "@microsoft/azureportal-reactview/Ajax";
import { getEnvironmentValue } from "@microsoft/azureportal-reactview/Environment";
import { start, startTrace, endTrace } from "../Logging/Logging";
import { armPollingDelayDefault } from "../../Shared/Constants";
import { extractHttpClientErrorMessage, getHttpStatusCode, isUnauthorized, isNotFound, isUserError } from "../../Shared/ViewAgnostic/Utilities";

export interface HttpClientResponseError {
    message: string | undefined;
    httpStatusCode: number;
    isUserError: boolean;
    isNotFound: boolean;
    isUnauthorized: boolean;
}

function stringifyError(error: unknown): string | undefined {
    let message = extractHttpClientErrorMessage(error);
    if (message) {
        return message;
    }
    if (error instanceof Error) {
        return error.message;
    }
    if (error !== null && typeof error !== "undefined") {
        return JSON.stringify(error);
    }
}

/**
 * tryStringifyError catches and throws away any parsing exceptions, vs stringifyError which will throw if error occurs during parsing.
 */
export function tryStringifyError(
    error: unknown,
    logOptions?: {
        area?: string;
    }
): string | undefined {
    try {
        return stringifyError(error);
    } catch (ex) {
        Az.log([
            {
                timestamp: Date.now(),
                level: Az.LogEntryLevel.Warning,
                message: "Failed to stringify error",
                area: logOptions?.area || "tryStringifyError",
                args: [ex, error]
            }
        ]);
    }
}

export function handleResponseFailures(
    error: unknown,
    options?: {
        handlers?: {
            /**
             * Callback to handle failure responses. i.e. completed network requests with status code >= 400.
             * Return true to indicate that the response has been handled, otherwise false.
             */
            handleFailureResponse?: (failureResponse: HttpClientResponseError) => boolean | undefined;
            /**
             * Callback to handle any failure responses which weren't handled by the 'failureResponse' callback, or
             * for thrown Errors. i.e. incomplete network requests (typically CORS/Network related).
             */
            handleError?: (error: Partial<HttpClientResponseError>) => void;
        };
        errorLogArea?: string;
    }
) {
    const { errorLogArea, handlers } = options || {};
    let failureMessage = {
        message: tryStringifyError(error, { area: errorLogArea ?? "handleResponseFailures" })
    };
    const { handleError, handleFailureResponse } = handlers || {};
    const httpStatusCode = getHttpStatusCode(error);
    if (httpStatusCode !== undefined) {
        const failureResponse = {
            ...failureMessage,
            httpStatusCode,
            isUserError: isUserError(httpStatusCode),
            isNotFound: isNotFound(httpStatusCode),
            isUnauthorized: isUnauthorized(httpStatusCode)
        };
        const handled = handleFailureResponse?.(failureResponse);
        if (!handled) {
            handleError?.(failureResponse);
        }
    } else {
        handleError?.(failureMessage);
    }
}

export function getArmSettings(apiVersionConfigKey = "machineLearningServicesApiVersionPreview"): {
    endpoint: string;
    APIVersion: string;
} {
    const armEndpointEnv = getEnvironmentValue("armEndpoint");
    const endpoint = armEndpointEnv.endsWith("/") ? armEndpointEnv.slice(0, armEndpointEnv.length - 1) : armEndpointEnv;
    const APIVersion = String(getEnvironmentValue(apiVersionConfigKey) ?? "");
    return {
        endpoint,
        APIVersion
    };
}

type PaginationFields = Record<string, string | undefined> & {
    nextLink?: string | undefined;
};

type PaginatedResponse<T> = {
    value: T[];
} & PaginationFields;

export async function execArmBatchAccumulated<T>(
    source: string,
    action: string,
    batchOptions: Ajax.BatchSettings,
    nextLinkField: string = "nextLink"
): Promise<Ajax.BatchResponseItem<PaginatedResponse<T>>> {
    start(source, action);
    try {
        const deferred: {
            resolve(value: Ajax.BatchResponseItem<PaginatedResponse<T>>): void;
            reject(error: any): void;
        } = {
            resolve: () => {},
            reject: () => {}
        };
        const deferredPromise = new Promise<Ajax.BatchResponseItem<PaginatedResponse<T>>>((resolve, reject) => {
            deferred.resolve = resolve;
            deferred.reject = reject;
        });
        let accumulatedValues: Array<T> = [];
        const fetchResources = (batchOptions: Ajax.BatchSettings) => {
            batch<PaginatedResponse<T>>({
                ...batchOptions,
                setTelemetryHeader: source
            }).then(
                response => {
                    accumulatedValues = accumulatedValues.concat(response.content.value);
                    const nextUri = response.content[nextLinkField];
                    if (nextUri) {
                        fetchResources({
                            ...batchOptions,
                            uri: nextUri,
                            setTelemetryHeader: source
                        });
                    } else {
                        deferred.resolve({
                            ...response,
                            content: {
                                value: accumulatedValues
                            } as PaginatedResponse<T>
                        });
                    }
                },
                failureResponse => {
                    deferred.reject(failureResponse);
                }
            );
        };
        fetchResources({
            ...batchOptions,
            setTelemetryHeader: source
        });
        const result = await deferredPromise;
        endTrace(source, action, {
            httpStatusCode: result.httpStatusCode
        });
        return result;
    } catch (ex: unknown) {
        endTrace(source, action, {
            httpStatusCode: getHttpStatusCode(ex),
            exception: ex
        });
        throw ex;
    }
}

export async function execArmBatch<T>(source: string, action: string, options: Ajax.BatchSettings): Promise<Ajax.BatchResponseItem<T>> {
    start(source, action);
    try {
        const result = await batch<T>({
            ...options,
            setTelemetryHeader: source
        });
        endTrace(source, action, {
            httpStatusCode: result.httpStatusCode
        });
        return result;
    } catch (ex: unknown) {
        endTrace(source, action, {
            httpStatusCode: getHttpStatusCode(ex),
            exception: ex
        });
        throw ex;
    }
}

export interface IPollerSettings {
    location: string;
    retryAfter: number;
    notificationId?: string;
    source: string;
    action: string;
}

export const parseRetryAfter = (retryAfterHeader?: string) => {
    if (!retryAfterHeader) {
        return armPollingDelayDefault;
    }
    let retryAfter: number;
    try {
        retryAfter = Number.parseInt(retryAfterHeader, 10);
        if (!Number.isSafeInteger(retryAfter)) {
            // ensure retry-after header is valid number
            retryAfter = armPollingDelayDefault;
        }
    } catch (e) {
        retryAfter = armPollingDelayDefault;
    }
    return retryAfter;
};

async function delay(ms: number): Promise<void> {
    return new Promise<void>(resolve => {
        setTimeout(() => {
            resolve();
        }, ms);
    });
}

export async function pollAndWait(settings: IPollerSettings, onUpdate?: (res: Ajax.BatchResponseItem<unknown>) => boolean): Promise<void> {
    const { notificationId, source, action } = settings;
    startTrace(source, `${action}/Poll`, {
        notificationId
    });
    return pollAndWaitInternal(settings, onUpdate);
}

async function pollAndWaitInternal(settings: IPollerSettings, onUpdate?: (res: Ajax.BatchResponseItem<unknown>) => boolean): Promise<void> {
    const { location, retryAfter, notificationId, source, action } = settings;

    return delay(retryAfter * 1000).then(async () => {
        try {
            const result = await batch({
                uri: location,
                type: "GET"
            });
            const stopPolling = onUpdate?.(result);
            if (stopPolling) {
                // interrupted
                endTrace(source, `${action}/Poll`, {
                    notificationId,
                    httpStatusCode: result.httpStatusCode,
                    interrupted: true
                });
                return;
            }
            if (result.httpStatusCode === 200 || result.httpStatusCode === 204) {
                // ended successfully
                endTrace(source, `${action}/Poll`, {
                    notificationId,
                    httpStatusCode: result.httpStatusCode
                });
                return;
            }
            if (result.httpStatusCode === 202) {
                // keep polling
                Az.trace([
                    {
                        action: `${action}/Poll`,
                        source,
                        timestamp: Date.now(),
                        actionModifier: "mark",
                        data: {
                            notificationId,
                            httpStatusCode: result.httpStatusCode
                        }
                    }
                ]);
                const nextLocation = result.headers.Location;
                const nextRetry = parseRetryAfter(result.headers["Retry-After"]);
                if (!nextLocation) {
                    throw new Error("Missing location header");
                }
                return pollAndWaitInternal({ location: nextLocation, retryAfter: nextRetry, notificationId, source, action }, onUpdate);
            }
            const error = new Error(`Unexpected polling status code ${result.httpStatusCode}`);
            (error as any).httpStatusCode = result.httpStatusCode;
            throw error;
        } catch (ex) {
            // ended with exception
            endTrace(source, `${action}/Poll`, {
                exception: ex
            });
            throw ex;
        }
    });
}
