import type { WorkspaceEndpointKeys } from "../../../Client/MLServicesDataModels/Workspace.types";
import { TraceSource, TraceAction } from "../Logging/Logging.types";
import { execArmBatch, getArmSettings } from "./queryUtils"

export async function getWorkspaceEndpointKeys(endpointId: string, options: {
    telemetry: {
        source: TraceSource
    }
}) {
    const { APIVersion, endpoint } = getArmSettings();
    return execArmBatch<WorkspaceEndpointKeys>(options.telemetry.source, TraceAction.ListWorkspaceEndpointKeys, {
        type: "POST",
        uri: `${endpoint}/${endpointId}/listKeys?api-version=${APIVersion}`
    });
}
