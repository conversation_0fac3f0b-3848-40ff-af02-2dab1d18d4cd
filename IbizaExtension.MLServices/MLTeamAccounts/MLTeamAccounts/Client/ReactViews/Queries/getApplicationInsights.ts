import { TraceSource, TraceAction } from "../Logging/Logging.types";
import { execArmBatchAccumulated, getArmSettings } from "./queryUtils";

interface IApplicationInsightsResource {
    id: string;
    name: string;
    location: string;
}

export interface IGetApplicationInsightsProps {
    subscriptionId: string,
    options: {
        telemetry: {
            source: TraceSource;
        };
    }
}

export async function getApplicationInsights(props: IGetApplicationInsightsProps) {
    const { options, subscriptionId } = props;
    const { APIVersion, endpoint } = getArmSettings("appInsightsApiVersion");
    const source = `${options.telemetry.source}/getApplicationInsights`;

    return execArmBatchAccumulated<IApplicationInsightsResource>(
        source,
        TraceAction.LoadApplicationInsights,
        {
            uri: `${endpoint}/subscriptions/${subscriptionId}/providers/Microsoft.Insights/components?api-version=${APIVersion}`
        }
    );
}
