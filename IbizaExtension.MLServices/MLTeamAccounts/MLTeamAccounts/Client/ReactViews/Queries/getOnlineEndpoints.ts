import { OnlineEndpointTrackedResource } from "../../MLServicesDataModels/OnlineEndpoint.types"
import { TraceSource, TraceAction } from "../Logging/Logging.types";
import { execArmBatch, getArmSettings } from "./queryUtils"

export interface OnlineEndpointTrackedResourceArmPaginatedResult {
    /**
     *
     * @type {Array<OnlineEndpointTrackedResource>}
     * @memberof OnlineEndpointTrackedResourceArmPaginatedResult
     */
    value?: OnlineEndpointTrackedResource[];
    /**
     *
     * @type {string}
     * @memberof OnlineEndpointTrackedResourceArmPaginatedResult
     */
    nextLink?: string;
}

export async function getOnlineEndpoints(workspaceId: string, options: {
    telemetry: {
        source: TraceSource
    }
}) {
    const { APIVersion, endpoint } = getArmSettings();
    return execArmBatch<OnlineEndpointTrackedResourceArmPaginatedResult>(options.telemetry.source, TraceAction.ListOnlineEndpoints, {
        uri: `${endpoint}/${workspaceId}/onlineEndpoints?api-version=${APIVersion}`,
    });
}
