import type { Connection } from "../../../Client/MLServicesDataModels/Connection.types";
import { TraceSource, TraceAction } from "../Logging/Logging.types";
import { execArmBatch, getArmSettings } from "./queryUtils";

export async function getConnections(workspaceId: string, options: {
    telemetry: {
        source: TraceSource
    }
}) {
    const { APIVersion, endpoint } = getArmSettings();
    return execArmBatch<{ value: Connection[]}>(options.telemetry.source, TraceAction.LoadHubConnections, {
        uri: `${endpoint}/${workspaceId}/connections?api-version=${APIVersion}&includeAll=true`,
    });
}