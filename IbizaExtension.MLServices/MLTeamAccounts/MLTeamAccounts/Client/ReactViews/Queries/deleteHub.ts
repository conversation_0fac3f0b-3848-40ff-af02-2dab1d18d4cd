import type { Workspace } from "../../../Client/MLServicesDataModels/Workspace.types";
import * as Az from "@microsoft/azureportal-reactview/Az";
import { Ajax } from "@microsoft/azureportal-reactview/FxReactCommon.Modules";
import { LogArea, TraceAction, TraceSource } from "../Logging/Logging.types";
import { execArmBatch, getArmSettings, IPollerSettings, parseRetryAfter } from "./queryUtils";
import { executeHubDeleteNotificationOperation } from "../Notifications/Notifications";

export async function beginDeleteHub(workspaceId: string, deleteResourcesList: string[], options: {
    telemetry: {
        source: TraceSource
    }
}): Promise<IPollerSettings> {
    const { endpoint } = getArmSettings();
    const { telemetry } = options;
    const source = `${telemetry.source}/deleteHub`;
    const action = TraceAction.DeleteWorkspace;
    const entities = deleteResourcesList.map(resource => { return { id: resource } });

    const batchPromise = execArmBatch<Workspace>(source, action, {
        uri: `${endpoint}/bulkDelete?api-version=2022-12-01`,
        type: "POST",
        content: {
            entities,
            type: "Default"
        },
    });

    const result = await batchPromise;
    if (result.httpStatusCode !== Ajax.HttpStatusCode.Accepted) {
        throw new Error(`Unexpected status code ${result.httpStatusCode}`);
    }
    const location = result.headers["Location"];
    const retryAfter = parseRetryAfter(result.headers["Retry-After"]);
    let notificationId: string | undefined = undefined;
    try {
        const notification = executeHubDeleteNotificationOperation(workspaceId, batchPromise);
        notificationId = notification.id;
    } catch (ex) {
        Az.log([{
            area: LogArea.beginDeleteHub,
            level: Az.LogEntryLevel.Error,
            message: "Error initiating delete notification polling",
            args: [ex],
            timestamp: Date.now()
        }]);
    }
    return { location, retryAfter, notificationId, action, source };
}
