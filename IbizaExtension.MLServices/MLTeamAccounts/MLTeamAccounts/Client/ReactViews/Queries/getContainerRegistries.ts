import { TraceSource, TraceAction } from "../Logging/Logging.types";
import { execArmBatchAccumulated, getArmSettings } from "./queryUtils";

interface IContainerRegistryResource {
    id: string;
    name: string;
    location: string;
}

export interface IGetContainerRegistriesProps {
    subscriptionId: string;
    options: {
        telemetry: {
            source: TraceSource;
        };
    };
}

export async function getContainerRegistries(props: {
    subscriptionId: string;
    options: {
        telemetry: {
            source: TraceSource;
        };
    };
}) {
    const { options, subscriptionId } = props;
    const { APIVersion, endpoint } = getArmSettings("containerRegistryApiVersion");
    const source = `${options.telemetry.source}/getContainerRegistries`;

    return execArmBatchAccumulated<IContainerRegistryResource>(source, TraceAction.LoadContainerRegistry, {
        uri: `${endpoint}/subscriptions/${subscriptionId}/providers/Microsoft.ContainerRegistry/registries?api-version=${APIVersion}`
    });
}
