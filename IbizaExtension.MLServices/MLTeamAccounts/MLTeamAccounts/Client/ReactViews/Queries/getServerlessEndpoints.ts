import { ServerlessEndpoint } from "../../MLServicesDataModels/ServerlessEndpoint.types"
import { TraceSource, TraceAction } from "../Logging/Logging.types";
import { execArmBatch, getArmSettings } from "./queryUtils"

export interface ServerlessEndpointArmPaginatedResult {
    value: ServerlessEndpoint[];
}

export async function getServerlessEndpoints(workspaceId: string, options: {
    telemetry: {
        source: TraceSource
    }
}) {
    const { APIVersion, endpoint } = getArmSettings();
    return execArmBatch<ServerlessEndpointArmPaginatedResult>(options.telemetry.source, TraceAction.ListServerlessEndpoints, {
        uri: `${endpoint}/${workspaceId}/serverlessEndpoints?api-version=${APIVersion}`,
    });
}
