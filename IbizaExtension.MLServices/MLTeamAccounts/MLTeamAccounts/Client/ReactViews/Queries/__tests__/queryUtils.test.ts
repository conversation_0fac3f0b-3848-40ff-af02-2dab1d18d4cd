import { tryStringifyError, pollAndWait } from "../queryUtils";
import * as Az from "@microsoft/azureportal-reactview/Az";
import { batch } from "@microsoft/azureportal-reactview/Ajax";
import * as Logging from "../../Logging/Logging";

jest.mock("../../Logging/Logging", () => {
    return {
        start: jest.fn(),
        startTrace: jest.fn(),
        endTrace: jest.fn()
    }
});

describe("queryUtils", () => {
    let endTraceSpy: jest.SpyInstance;
    let startTraceSpy: jest.SpyInstance;
    let batchMock: jest.Mock;

    beforeEach(() => {
        jest.useFakeTimers();
        endTraceSpy = jest.spyOn(Logging, "endTrace");
        endTraceSpy.mockReset();
        startTraceSpy = jest.spyOn(Logging, "startTrace");
        startTraceSpy.mockReset();
        batchMock = batch as jest.Mock;
        batchMock.mockReset();
    })

    describe("tryStringifyError", () => {
      it("should return the error message of an Error object input", () => {
        const error = new Error("Test error");
        const result = tryStringifyError(error);
        expect(result).toEqual("Test error");
      });

      it("should return the error message of an http client error responseText", () => {
        const error = {
            responseText: '{"error":{"message":"Test error"}}',
        };
        const result = tryStringifyError(error);
        expect(result).toEqual("Test error");
      });

      it("should return the error message of an http client error responseJSON", () => {
        const error = {
            responseText: "invalid", // responseText is ignored when responseJSON is present
            responseJSON: {"error":{"message":"Test error"}},
        };
        const result = tryStringifyError(error);
        expect(result).toEqual("Test error");
      });

      it("should return the error message of an http client error content", () => {
        const error = {
            content: {"error": {"message": "Test error"}}
        };
        const result = tryStringifyError(error);
        expect(result).toEqual("Test error");
      });

      it("should log a warning and return undefined if stringification fails", () => {
        const error = {
            responseText: 'invalid',
        };
        const logOptions = { area: "testArea" };
        const logSpy = jest.spyOn(Az, "log");
        const result = tryStringifyError(error, logOptions);
        expect(result).toBeUndefined();
        expect(logSpy).toHaveBeenCalledWith(
            expect.arrayContaining([
                expect.objectContaining({
                    timestamp: expect.any(Number),
                    level: 1,
                    message: "Failed to stringify error",
                    area: "testArea",
                    args: [expect.any(Error), error],
                })
            ])
        );
        logSpy.mockRestore();
      });
    });

    describe("pollAndWait", () => {
      it("stop polling if request returns successfully (status code 200 or 204)", async () => {
        batchMock.mockResolvedValue({
            httpStatusCode: 200
        });

        expect(startTraceSpy).not.toHaveBeenCalled();

        const pollPromise = pollAndWait(
          {
            location: "test-location",
            retryAfter: 5,
            notificationId: "test-notificationId",
            source: "test-source",
            action: "test-action",
          }
        );

        expect(startTraceSpy).toHaveBeenCalledWith("test-source", "test-action/Poll", expect.objectContaining({ notificationId: "test-notificationId" }));

        jest.runAllTimers();

        expect(endTraceSpy).not.toHaveBeenCalled();

        await pollPromise;

        expect(endTraceSpy).toHaveBeenCalledWith("test-source", "test-action/Poll", expect.objectContaining({
            notificationId: "test-notificationId",
            httpStatusCode: 200
        }));
      });

      it("should keep polling if the HTTP status code is 202", async () => {
        const traceSpy = jest.spyOn(Az, "trace");

        batchMock.mockResolvedValueOnce({
            httpStatusCode: 202,
            headers: {
                Location: "next-location",
                "Retry-After": "10",
            },
        });

        batchMock.mockResolvedValue({
            httpStatusCode: 200
        });

        expect(startTraceSpy).not.toHaveBeenCalled();

        pollAndWait(
          {
            location: "test-location",
            retryAfter: 5,
            notificationId: "test-notificationId",
            source: "test-source",
            action: "test-action",
          }
        );

        expect(startTraceSpy).toHaveBeenCalledWith("test-source", "test-action/Poll", expect.objectContaining({ notificationId: "test-notificationId" }));

        jest.runAllTimers();

        await new Promise(resolve => setImmediate(resolve));

        expect(endTraceSpy).not.toHaveBeenCalled();

        expect(traceSpy).toHaveBeenCalledWith(expect.arrayContaining([
            expect.objectContaining({
                action: "test-action/Poll",
                source: "test-source",
                timestamp: expect.any(Number),
                actionModifier: "mark",
                data: {
                    notificationId: "test-notificationId",
                    httpStatusCode: 202
                }
            })
        ]));
      });

      it("should throw an error if the HTTP status code is unexpected", async () => {
        jest.useRealTimers();

        batchMock.mockResolvedValue({
            httpStatusCode: 500,
            headers: {},
          });

        try {
            await pollAndWait(
                {
                  location: "test-location",
                  retryAfter: 0.1,
                  notificationId: "test-notificationId",
                  source: "test-source",
                  action: "test-action",
                }
            );
        } catch (ex) {
            expect(ex).toEqual(new Error("Unexpected polling status code 500"));
        }
      });
    });
});

