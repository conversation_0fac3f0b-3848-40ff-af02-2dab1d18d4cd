import { TraceSource, TraceAction } from "../Logging/Logging.types";
import { execArmBatch } from "./queryUtils";

type DeleteState =
  | "Accepted"
  | "Deleting"
  | "Failed"
  | "Canceled"
  | "Succeeded";

interface ResourceDeleteErrorResponse {
    error?: {
        code?: string;
        message?: string;
    }
}

interface ResourceDeleteStatus {
    id?: string;
    status?: DeleteState;
    httpStatusCode?: string;
    errorResponse?: ResourceDeleteErrorResponse;
}

export interface DeleteHubStatusResult {
    id?: string;
    name?: string;
    status?: DeleteState;
    responses?: ResourceDeleteStatus[];
}

export async function getDeleteHubStatus(location: string, options: {
    telemetry: {
        source: TraceSource
    }
}) {
    return execArmBatch<DeleteHubStatusResult>(options.telemetry.source, TraceAction.LoadDeleteHubStatus, {
        uri: location,
    });
}