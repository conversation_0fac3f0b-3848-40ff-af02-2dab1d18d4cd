import type { WorkspaceEndpoint } from "../../../Client/MLServicesDataModels/Workspace.types";
import { TraceSource, TraceAction } from "../Logging/Logging.types";
import { execArmBatch, getArmSettings } from "./queryUtils"

export async function getWorkspaceEndpoints(workspaceId: string, options: {
    telemetry: {
        source: TraceSource
    }
}) {
    const { APIVersion, endpoint } = getArmSettings();
    return execArmBatch<{ value: WorkspaceEndpoint[] }>(options.telemetry.source, TraceAction.ListWorkspaceEndpoints, {
        uri: `${endpoint}/${workspaceId}/endpoints?api-version=${APIVersion}`
    });
}
