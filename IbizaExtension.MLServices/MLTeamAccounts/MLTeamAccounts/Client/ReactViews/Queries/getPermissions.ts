import { TraceSource, TraceAction } from "../Logging/Logging.types";
import { execArmBatch, getArmSettings } from "./queryUtils";

export interface Permission {
    actions?: string[];
    dataActions?: string[];
    notActions?: string[];
    notDataActions?: string[];
}

export async function getPermissions(resourceId: string, options: {
    telemetry: {
        source: TraceSource
    }
}) {
    const { endpoint } = getArmSettings();
    const APIVersion = "2022-04-01";
    return execArmBatch<{ value: Permission[] }>(options.telemetry.source, TraceAction.LoadPermissions, {
        uri: `${endpoint}/${resourceId}/providers/Microsoft.Authorization/permissions?api-version=${APIVersion}`,
    });
}
