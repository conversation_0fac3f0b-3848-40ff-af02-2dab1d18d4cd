import type { Workspace } from "../../../Client/MLServicesDataModels/Workspace.types";
import * as Az from "@microsoft/azureportal-reactview/Az";
import { Ajax } from "@microsoft/azureportal-reactview/FxReactCommon.Modules";
import { LogArea, TraceAction, TraceSource } from "../Logging/Logging.types";
import { execArmBatch, getArmSettings, IPollerSettings, parseRetryAfter } from "./queryUtils";
import { executeWorkspaceDeleteNotificationOperation } from "../Notifications/Notifications";

export async function beginDeleteMachineLearningWorkspace(workspaceId: string, options: {
    purge?: boolean
    telemetry: {
        source: TraceSource
    }
}): Promise<IPollerSettings> {
    const { APIVersion, endpoint } = getArmSettings();
    const { purge, telemetry } = options;
    const source = `${telemetry.source}/deleteMachineLearningWorkspace`;
    const action = TraceAction.DeleteWorkspace;
    const batchPromise = execArmBatch<Workspace>(source, action, {
        uri: `${endpoint}${workspaceId}?api-version=${APIVersion}${purge ? `&forceToPurge=${purge}` : ""}`,
        type: "DELETE"
    });
    const result = await batchPromise;
    if (result.httpStatusCode !== Ajax.HttpStatusCode.Accepted) {
        throw new Error(`Unexpected status code ${result.httpStatusCode}`);
    }
    const location = result.headers["Location"];
    const retryAfter = parseRetryAfter(result.headers["Retry-After"]);
    let notificationId: string | undefined = undefined;
    try {
        const notification = executeWorkspaceDeleteNotificationOperation(workspaceId, location);
        notificationId = notification.id;
    } catch (ex) {
        Az.log([{
            area: LogArea.beginDeleteMachineLearningWorkspaceError,
            level: Az.LogEntryLevel.Error,
            message: "Error initiating delete notification polling",
            args: [ex],
            timestamp: Date.now()
        }]);
    }
    return { location, retryAfter, notificationId, action, source };
}
