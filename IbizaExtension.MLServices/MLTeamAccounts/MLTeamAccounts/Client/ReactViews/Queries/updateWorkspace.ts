import type { Workspace, WorkspaceProperties } from "../../../Client/MLServicesDataModels/Workspace.types";
import { Ajax } from "@microsoft/azureportal-reactview/FxReactCommon.Modules";
import { TraceAction, TraceSource, UpdateWorkspaceAction } from "../Logging/Logging.types";
import { execArmBatch, getArmSettings, parseRetryAfter, pollAndWait } from "./queryUtils"

/**
 * Calls workspace update API and waits for the operation to complete.
 */
export async function updateWorkspaceAndWait(workspaceId: string, payload: Partial<Omit<Workspace, 'properties'> & { properties?: Partial<WorkspaceProperties> }>, options: {
    telemetry: {
        source: TraceSource,
        action: UpdateWorkspaceAction
    }
}) {
    const { APIVersion, endpoint } = getArmSettings();
    const source = `${options.telemetry.source}/updateWorkspace`;
    const action = options.telemetry.action;
    const result = await execArmBatch<Workspace>(source, action, {
        uri: `${endpoint}/${workspaceId}?api-version=${APIVersion}`,
        type: "PATCH",
        content: {
            ...payload
        }
    });
    if (result.httpStatusCode !== Ajax.HttpStatusCode.Accepted) {
        const error = new Error(`Unexpected status code ${result.httpStatusCode}`);
        (error as any).httpStatusCode = result.httpStatusCode;
        throw error;
    }
    const location = result.headers["Location"];
    const retryAfter = parseRetryAfter(result.headers["Retry-After"]);
    return pollAndWait({ location, retryAfter, action, source });
}

export async function updateWorkspaceCMK(workspaceId: string, data: Required<Pick<KeyVaultPickerV2.KeyPickerV2Outputs, "keyId" | "keyVersionId" | "vaultResourceId">>, options: {
    telemetry: {
        source: TraceSource
    }
}) {
    return updateWorkspaceAndWait(workspaceId, {
        properties: {
            encryption: {
                status: "Enabled",
                keyVaultProperties: {
                    keyIdentifier: data.keyVersionId,
                    keyVaultArmId: data.vaultResourceId
                }
            }
        }
    }, {
        telemetry: {
            source: options.telemetry.source,
            action: TraceAction.UpdateCustomerManagedKey
        }
    });
}

export async function updateWorkspaceAppInsights(workspaceId: string, data: { applicationInsightsId: string }, options: {
    telemetry: {
        source: TraceSource
    }
}) {
    return updateWorkspaceAndWait(workspaceId, {
        properties: {
            applicationInsights: data.applicationInsightsId
        }
    }, {
        telemetry: {
            source: options.telemetry.source,
            action: TraceAction.UpdateApplicationInsights
        }
    });
}

export async function updateWorkspaceContainerRegistry(workspaceId: string, data: { containerRegistryId: string }, options: {
    telemetry: {
        source: TraceSource
    }
}) {
    return updateWorkspaceAndWait(workspaceId, {
        properties: {
            containerRegistry: data.containerRegistryId
        }
    }, {
        telemetry: {
            source: options.telemetry.source,
            action: TraceAction.UpdateContainerRegistry
        }
    });
}
