import { Message<PERSON>ar, MessageBarType, Shimmer } from "@fluentui/react";
import { Combobox, makeStyles, shorthands, Option } from "@fluentui/react-components";
import {
    Virtualizer,
    useStaticVirtualizerMeasure,
} from "@fluentui/react-components/unstable";
import * as Az from "@microsoft/azureportal-reactview/Az";
import * as React from "react";
import { useApplicationInsightsInSubscriptionContext } from "../Hooks/useApplicationInsights";
import { TraceSource } from "../Logging/Logging.types";
import { handleResponseFailures } from "../Queries/queryUtils";
import { AssociatedResource_AppInsights_ReactView_Dropdown as ClientResources_AssociatedResource_AppInsights_ReactView_Dropdown } from "../Resx/ClientResources.resjson";

const optionHeight = 32;
const useStyles = makeStyles({
    root: {
        // Stack the label above the field with a gap
        display: "grid",
        gridTemplateRows: "repeat(1fr)",
        justifyItems: "start",
        ...shorthands.gap("2px")
    },
    listbox: {
        maxHeight: "250px",
    },
    // these styles wrap the value text within the dropdown button and cause it to truncate
    option: {
        height: `${optionHeight}px`,
    },
});

export interface IApplicationInsightsDropdownProps {
    id?: string;
    inlinePopup?: boolean;
    required?: boolean;
    value: string | undefined;
    onOptionSelect: (aiId: string | undefined) => void;
}

export const ApplicationInsightsDropdown: React.FC<IApplicationInsightsDropdownProps> = ({
    id,
    inlinePopup,
    required,
    value,
    onOptionSelect
}) => {
    const { data, error, isError, isLoading } = useApplicationInsightsInSubscriptionContext();

    const styles = useStyles();

    const options = React.useMemo(() => {
        return data?.content.value.map(ai => {
            return {
                key: ai.id.toLowerCase(),
                rg: ai.resourceGroupName,
                text: ai.name
            }
        }) || [];
    }, [data?.content.value]);

    const [searchValue, setSearchValue] = React.useState<string>("");

    React.useEffect(() => {
        if (options.length > 0 && value && !searchValue) {
            const textFromValue = options.find(o => o.key === value?.toLowerCase())?.text;
            // if we have a value but no display text, then look up the dispay text.
            // if there is no display text, then the value is invalid and we should clear it.
            if (!textFromValue) {
                onOptionSelect(undefined);
            } else {
                setSearchValue(textFromValue);
            }
        }
    }, [options, value, searchValue])

    const placeholder = ClientResources_AssociatedResource_AppInsights_ReactView_Dropdown.placeholder;

    const sortedOptions = !searchValue ? options : options.map(o => {
        return {
            ...o,
            matched: o.text.toLowerCase().includes(searchValue.toLowerCase())
        }
    }).sort((a, b) => {
        if (a.key === value?.toLowerCase()) {
            return -1;
        }
        if (a.matched && !b.matched) {
            return -1;
        }
        if (!a.matched && b.matched) {
            return 1;
        }
        return a.text.localeCompare(b.text);
    });

    // TODO: figure out how to get option grouping working with virtualized list
    // const groupedAndsortedOptions = sortedOptions.reduce((groups, opt) => {
    //     const resourceGroup = opt.rg;
    //     if (!groups[resourceGroup]) {
    //         groups[resourceGroup] = [];
    //     }
    //     groups[resourceGroup].push(opt);
    //     return groups;
    // }, {} as Record<string, Array<{ key: string, text: string }>>);

    const { virtualizerLength, bufferItems, bufferSize, scrollRef, containerSizeRef } = useStaticVirtualizerMeasure({
        defaultItemSize: optionHeight,
        direction: "vertical",
    });

    if (isError) {
        handleResponseFailures(error, {
            errorLogArea: TraceSource.ApplicationInsightsDropdown,
            handlers: {
                handleFailureResponse: failureResponse => {
                    if (failureResponse.isUserError) {
                        return true; // mark user errors as handled
                    }
                },
                handleError(error) {
                    // log unhandled errors for investigation
                    Az.log([
                        {
                            timestamp: Date.now(),
                            level: Az.LogEntryLevel.Error,
                            message: error.message || "",
                            area: TraceSource.ApplicationInsightsDropdown,
                            code: error.httpStatusCode,
                            args: [error]
                        }
                    ])
                },
            }
        })
        return <MessageBar messageBarType={MessageBarType.error}>
            {ClientResources_AssociatedResource_AppInsights_ReactView_Dropdown.errorLoadingMessage}
        </MessageBar>
    }

    const emptyMessage = ClientResources_AssociatedResource_AppInsights_ReactView_Dropdown.emptyMessage;
    return (
        <Shimmer isDataLoaded={!isLoading}>
            <Combobox
                id={id}
                appearance="underline"
                clearable={true}
                listbox={{ ref: scrollRef, className: styles.listbox }}
                selectedOptions={value ? [value] : []}
                onOptionSelect={(_e, selected) => {
                    onOptionSelect(selected.optionValue)
                    setSearchValue(selected.optionText || "")
                }}
                onChange={(ev) => {
                    setSearchValue(ev.target.value);
                }}
                required={required}
                value={searchValue}
                placeholder={placeholder}
                inlinePopup={inlinePopup}
            >
                {sortedOptions.length === 0 &&
                    <Option key="no-results" disabled={true} text={emptyMessage} value="no-results">
                        {emptyMessage}
                    </Option>
                }
                {sortedOptions.length > 0 && (
                    <Virtualizer key={searchValue} numItems={sortedOptions.length} virtualizerLength={virtualizerLength} bufferItems={bufferItems} bufferSize={bufferSize} itemSize={optionHeight} containerSizeRef={containerSizeRef} >
                        {(index) => {
                            const optionAtIndex = sortedOptions[index];
                            if (!optionAtIndex) {
                                return null;
                            }
                            return (
                                <Option
                                    className={styles.option}
                                    key={optionAtIndex.key}
                                    value={optionAtIndex.key}
                                    text={optionAtIndex.text}
                                    aria-posinset={index}
                                    aria-setsize={sortedOptions.length}
                                >
                                    {optionAtIndex.text}
                                </Option>
                            );
                        }}
                    </Virtualizer>
                )}
            </Combobox>
        </Shimmer>
    )
}