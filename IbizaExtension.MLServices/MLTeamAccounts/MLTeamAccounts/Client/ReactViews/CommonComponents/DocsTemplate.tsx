import * as React from "react";
import { Text } from "@fluentui/react/lib/Text";

export const DocsTemplate: React.FC<{ templateString: string, replacements: JSX.Element[], replacementString: string }> = ({ templateString, replacements, replacementString }): JSX.Element => {
    const pieces = templateString.split(replacementString);
    if (pieces.length < 2) {
        return <Text>{templateString}</Text>;
    }
    const lastPiece = pieces.pop();
    return (
        <>
            {pieces.map((onePiece, idx) => (
                <React.Fragment key={idx}>
                    <Text>{onePiece}</Text>
                    {replacements[idx]}
                </React.Fragment>
            ))}
            <Text>{lastPiece}</Text>
        </>
    );
};
