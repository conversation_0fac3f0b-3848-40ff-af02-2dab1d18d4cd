import * as React from "react";
import { Cell, Grid } from "./Grid";

export interface IKeyValuePairListItem {
    key: string;
    label: string | ((props: { key: string, index: number }) => React.ReactNode);
    value: string | ((props: { key: string, index: number }) => React.ReactNode);
}
export interface IKeyValuePairListProps {
    items: IKeyValuePairListItem[];
    labelMaxWidth?: number;
}

export const KeyValuePairList: React.FC<IKeyValuePairListProps> = ({ items, labelMaxWidth = 250 }) => {
    return (
        <Grid gridTemplateColumns={labelMaxWidth ? `minmax(0, ${labelMaxWidth}px)` : undefined}>
            {items.map(({ key, label, value }, idx) => (
                <React.Fragment key={key}>
                    <Cell row={idx} column={1}>
                        {typeof label === "string" ? label : label({ key, index: idx })}
                    </Cell>
                    <Cell row={idx} column={2}>
                        {typeof value === "string" ? value : value({ key, index: idx })}
                    </Cell>
                </React.Fragment>
            ))}
        </Grid>
    );
}
