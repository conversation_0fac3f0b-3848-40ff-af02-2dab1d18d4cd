import { <PERSON><PERSON>, <PERSON>mmerElementType, MessageBar, MessageBarType } from "@fluentui/react";
import { De<PERSON><PERSON><PERSON><PERSON><PERSON>, PrimaryButton } from "@fluentui/react/lib/Button";
import { CopyableLabel } from "@microsoft/azureportal-reactview/CopyableLabel";
import * as React from "react";
import { useWorkspaceEndpoints } from "../Hooks/useWorkspaceEndpoints";
import { useWorkspaceContext } from "./WorkspaceContext";
import { isAIStudioKind } from "../../Shared/ViewAgnostic/Utilities";
import { TraceSource } from "../Logging/Logging.types";
import { useWorkspaceEndpointKeys } from "../Hooks/useWorkspaceEndpointKeys";
import { AssetType_AIStudio_KeysAndEndpoints as ClientResources_AssetType_AIStudio_KeysAndEndpoints } from "../Resx/ClientResources.resjson";

export interface IKeysAndEndpointsProps {
  telemetrySource: TraceSource;
  header?: "h2" | "h3";
  regenerateKeys?(this: void): Promise<void>;
}

export const KeysAndEndpoints: React.FC<IKeysAndEndpointsProps> = ({ telemetrySource, header, regenerateKeys }) => {
  const workspaceContext = useWorkspaceContext();
  const enableEndpoints = workspaceContext.workspace?.kind && isAIStudioKind(workspaceContext.workspace.kind);
  const endpoints = useWorkspaceEndpoints({
    workspace: enableEndpoints ? workspaceContext.workspace : undefined,
    telemetry: {
      source: telemetrySource
    }
  });
  const [showingKeys, setShowingKeys] = React.useState(false);
  const showKeysClicked = React.useCallback(() => {
    setShowingKeys((val) => !val);
  }, []);

  const OpenAIEndpoint = React.useMemo(() => {
    return endpoints?.data?.content?.value.find(endpoint => endpoint.name === "Azure.OpenAI")
  }, [endpoints?.data?.content?.value]);

  const keys = useWorkspaceEndpointKeys({
    endpointId: OpenAIEndpoint?.id,
    telemetry: {
      source: telemetrySource,
    }
  });

  const shimmerElements = React.useMemo(() => {
    const elements = [];
    for (let idx = 0; idx < 4; idx++) {
      elements.push({
        type: ShimmerElementType.line,
        height: 20,
        width: "100%"
      });
    }
    return elements;
  }, []);

  if (!enableEndpoints) {
    return <></>;
  }

  const keysAndEndpoints = (endpoints.isLoading || keys.isLoading) ? (
    <Shimmer shimmerElements={shimmerElements} />
  ) : (
    <>
      <div style={{ display: "flex", flexDirection: "row", gap: "10px", margin: "10px 0" }}>
        <PrimaryButton onClick={showKeysClicked}>{showingKeys ? ClientResources_AssetType_AIStudio_KeysAndEndpoints.hideKeys : ClientResources_AssetType_AIStudio_KeysAndEndpoints.showKeys}</PrimaryButton>
        {regenerateKeys && <DefaultButton onClick={regenerateKeys}>{ClientResources_AssetType_AIStudio_KeysAndEndpoints.regenerateKeys}</DefaultButton>}
      </div>
      <CopyableLabel label={ClientResources_AssetType_AIStudio_KeysAndEndpoints.key1} mask={!showingKeys} value={keys.data?.content?.keys.Key1 || ""} stackVertical={true} />
      <CopyableLabel label={ClientResources_AssetType_AIStudio_KeysAndEndpoints.key2} mask={!showingKeys} value={keys.data?.content?.keys.Key2 || ""} stackVertical={true} />
      {endpoints?.data?.content?.value.map(endpoint => <CopyableLabel key={endpoint.id} label={`${ClientResources_AssetType_AIStudio_KeysAndEndpoints.endpoint} - ${endpoint.name}`} value={endpoint.properties.endpointUri} stackVertical={true} />)}
    </>
  );

  const Header = header || "h3";

  return (
    <div style={{
        maxWidth: 600
      }}
    >
      <Header>{ClientResources_AssetType_AIStudio_KeysAndEndpoints.title}</Header>
      <div style={{
          marginLeft: 20
        }}
      >
        <MessageBar
          messageBarType={MessageBarType.info}
          isMultiline={true}
        >
          {ClientResources_AssetType_AIStudio_KeysAndEndpoints.message}
        </MessageBar>
        {keysAndEndpoints}
      </div>
    </div>
  );
}
