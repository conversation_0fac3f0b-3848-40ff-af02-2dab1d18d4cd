import { PrimaryButton } from "@fluentui/react/lib/Button";
import { Text } from "@fluentui/react/lib/Text";
import { mergeStyleSets, getTheme } from "@fluentui/react/lib/Styling";

interface IModelProxyLandingProps {
    descriptionBody: String,
    descriptionIntro?: String,
    buttonText: String,
    url: any
}



const styleSet = {
    root: {
        margin: "20px",
        padding: "20px",
        display: "flex",
        flexDirection: "column",
        alignItems: "justified",
        gap: "10px",
        maxWidth: "450px"
    }
}

export const ModelProxyLanding = (props: IModelProxyLandingProps) => {
    const { descriptionBody, descriptionIntro, buttonText, url } = props;
    const classes = mergeStyleSets(styleSet);
    const theme = getTheme();
    return (
        <div className={classes.root} style={{
            border: `1px solid ${theme.palette.black}`
        }}>
            {descriptionIntro ?
            <Text>
                {descriptionIntro}
                <br></br>
            </Text>: <></>
        }
            <Text>
                {descriptionBody}
            </Text>
            <div>
                <PrimaryButton
                    href={url}
                    target="_blank"
                    styles={{
                        root: {
                            marginTop: "14px",
                            marginBottom: "10px"
                        }
                    }}>
                    {buttonText}
                </PrimaryButton>
            </div>
        </div>
    );
};

