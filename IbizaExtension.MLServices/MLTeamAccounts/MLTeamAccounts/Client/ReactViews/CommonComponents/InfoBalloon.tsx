import * as Az from "@microsoft/azureportal-reactview/Az";
import { FrameworkIcon } from "@microsoft/azureportal-reactview/FrameworkIcon";
import { Stack, StackItem } from "@fluentui/react/lib/Stack";
import { TooltipHost } from "@fluentui/react/lib/Tooltip";
import * as React from "react";

interface IInfoBalloonProps {
    content: string;
    tooltipId: string;
    gap?: number | string;
}

export const InfoBalloon: React.FC<IInfoBalloonProps> = ({ tooltipId, children, gap, content }) => {
    const { calloutProps, tokens, imageProp, iconStyles, tooltipHostStyles } = React.useMemo(() => ({
        calloutProps: { gapSpace: 0 },
        tokens: { childrenGap: gap ?? 5 },
        imageProp: { type: Az.IconType.Info },
        iconStyles: { height: 14, width: 12 },
        tooltipHostStyles: { root: { display: 'inline-block' } }
    }), [gap, content]);

    return (<Stack horizontal tokens={tokens}>
        <StackItem>
            {children}
        </StackItem>
        <StackItem>
            <TooltipHost content={content} id={tooltipId} calloutProps={calloutProps} styles={tooltipHostStyles} >
                <FrameworkIcon image={imageProp} style={iconStyles} />
            </TooltipHost>
        </StackItem>
    </Stack>)
}
