import * as React from "react";
import { useQuery } from 'react-query'
import { ClientResources } from "../Resx/ClientResources.resjson";
import { format } from "@fluentui/react";
import { Shimmer } from "@fluentui/react/lib/Shimmer";
import * as Az from "@microsoft/azureportal-reactview/Az";
import { Ajax } from "@microsoft/azureportal-reactview/FxReactCommon.Modules";
import { FluentProvider, webDarkTheme, webLightTheme, makeStyles } from '@fluentui/react-components';
import { usePortalTheme } from "@microsoft/azureportal-reactview/Theming";
import type { Workspace } from "../../../Client/MLServicesDataModels/Workspace.types";
import { getHttpStatusCode } from "../../Shared/ViewAgnostic/Utilities";
import { handleResponseFailures } from "../Queries/queryUtils";
import { LogArea, TraceAction, TraceSource } from "../Logging/Logging.types";
import { WorkspaceWithID, asWorkspaceWithID } from "../workspaceUtils";
import { ViewQueryContext } from "./ViewQueryContext";
import { WorkspaceContext } from "./WorkspaceContext";

export interface IViewProps {
    workspaceId: string;
    telemetryErrorArea: LogArea;
    telemetryTraceSource: TraceSource;
    dataFetcher?: (this: void, resourceId: string) => Promise<Ajax.BatchResponseItem<Workspace>>;
    title?: string;
    subtitle?: string;
    fullHeight?: boolean;
}

const useStyles = makeStyles({ fullHeight: { display: "flex", height: "100%", flexDirection: "column" } });

const WorkspaceDataFetcher: React.FC<IViewProps> = (props) => {
    const { title, subtitle, workspaceId, dataFetcher, telemetryErrorArea, telemetryTraceSource, children } = props;
    const [workspace, setWorkspace] = React.useState<WorkspaceWithID>();
    const loadFailureMessage = format(ClientResources.workspaceLoadFailure, workspaceId);

    const workspaceQueryResult = useQuery(['workspace', workspaceId], () => dataFetcher!(workspaceId), {
        onError: (error) => {
            handleResponseFailures(error, {
                handlers: {
                    handleFailureResponse: failureResponse => {
                        if (failureResponse.isNotFound) {
                            Az.notFound({
                                message: loadFailureMessage,
                                metadata: failureResponse
                            });
                            return true;
                        } else if (failureResponse.isUnauthorized) {
                            Az.setBladeUnauthorized(loadFailureMessage);
                            return true;
                        }
                        return false;
                    },
                    handleError: (error) => {
                        Az.log([
                            {
                                timestamp: Date.now(),
                                level: Az.LogEntryLevel.Error,
                                message: error.message || "",
                                area: telemetryErrorArea,
                                code: error.httpStatusCode,
                                args: [error]
                            }
                        ]);
                        Az.handledError({
                            message: loadFailureMessage,
                            code: error.httpStatusCode,
                            metadata: error
                        });
                    }
                },
                errorLogArea: telemetryErrorArea
            });
        }
    });

    React.useEffect(() => {
        if (!workspaceQueryResult.isLoading) {
            Az.trace([
                {
                    timestamp: Date.now(),
                    action: TraceAction.LoadView,
                    source: telemetryTraceSource,
                    data: {
                        success: !workspaceQueryResult.isLoadingError,
                        code: getHttpStatusCode(workspaceQueryResult?.error),
                        kind: workspaceQueryResult.data?.content.kind,
                        extension: "Microsoft_Azure_MLTeamAccounts"
                    }
                }
            ]);
        }
    }, [workspaceQueryResult.isLoadingError, workspaceQueryResult.isLoading])

    React.useEffect(() => {
        if (workspaceQueryResult.data?.content?.id) {
            const workspaceWithID = asWorkspaceWithID(workspaceQueryResult.data.content, workspaceId);
            const titleToSet = title ?? workspaceWithID.name;
            if (titleToSet) {
                if (subtitle) {
                    Az.setTitle({ title: titleToSet, subtitle: subtitle });
                } else {
                    Az.setTitle(titleToSet);
                }
            }
            setWorkspace(workspaceWithID);
        }
    }, [workspaceQueryResult.data, workspaceId])

    const workspaceContextValue = React.useMemo(() => ({
        workspace
    }), [workspace]);

    if (!workspace) {
        return (
            <Shimmer />
        );
    }

    return (
        <WorkspaceContext.Provider value={workspaceContextValue}>
            {children}
        </WorkspaceContext.Provider>
    );
};

export const View: React.FC<IViewProps> = (props) => {
    const { title, subtitle, dataFetcher, children } = props;
    const portalTheme = usePortalTheme();
    const styles = useStyles();
    const fluentTheme = portalTheme.mode === Az.ThemeMode.Dark ? webDarkTheme : webLightTheme;

    React.useEffect(() => {
        if (title) {
            if (subtitle) {
                Az.setTitle({ title, subtitle });
            } else {
                Az.setTitle(title);
            }
        }
    }, [title, subtitle]);

    return (
        <FluentProvider theme={fluentTheme} className={props.fullHeight ? styles.fullHeight : undefined}>
            <ViewQueryContext>
                {dataFetcher ? <WorkspaceDataFetcher {...props} /> : children}
            </ViewQueryContext>
        </FluentProvider>
    );
}
