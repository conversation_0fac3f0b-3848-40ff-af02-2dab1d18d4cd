{"$schema": "../node_modules/@microsoft/azureportal-build/lib/definitions/dx.schema.json", "area": "Resource", "stringSource": "../Resx/ClientResources.resjson", "assetType": {"name": "AIStudio", "displayNames": "AssetType_AIStudio_Names", "icon": {"file": "../Content/SVG/ai_studio_icon_color.svg"}, "keywords": "AssetType_AIStudio.keywords", "browse": {"type": "ResourceType", "query": {"file": "./AIStudioBrowseQuery.kml"}, "defaultColumns": ["FxColumns.ResourceGroup", "FxColumns.AssetType", "FxColumns.Location", "FxColumns.Subscription"], "mergedResourceTypes": [{"name": "microsoft.machinelearningservices/workspaces", "kind": "<PERSON><PERSON>"}, {"name": "microsoft.machinelearningservices/workspaces", "kind": "Project"}, {"name": "microsoft.machinelearningservices/workspaces", "kind": "<PERSON>n"}], "commands": [{"visibility": [], "kind": "MenuC<PERSON>mand", "id": "AddAIStudioCreateDropdown", "displayName": "AssetType_AIStudio_Browse_Commands.createAzureAI", "ariaLabel": "AssetType_AIStudio_Browse_Commands.createAzureAILabel", "icon": "Add", "commands": [{"kind": "OpenBladeCommand", "id": "CreateAzureAIProject", "displayName": "AssetType_AIStudio_Browse_Commands.createAzureAIProject", "ariaLabel": "AssetType_AIStudio_Browse_Commands.createAzureAIProjectLabel", "content": "AssetType_AIStudio_Browse_Description.createAzureAIProject", "icon": {"file": "../Content/SVG/ai_studio_icon_color.svg"}, "blade": {"name": "CreateAIStudioProjectBlade", "extension": "Microsoft_Azure_MLTeamAccounts", "doesProvisioning": true, "parameters": {"kind": "Project"}}}, {"kind": "OpenBladeCommand", "id": "CreateAzureAIHub", "displayName": "AssetType_AIStudio_Browse_Commands.createAzureAIHub", "ariaLabel": "AssetType_AIStudio_Browse_Commands.createAzureAIHubLabel", "content": "AssetType_AIStudio_Browse_Description.createAzureAIHub", "icon": {"file": "../Content/SVG/ai_studio_icon_color.svg"}, "blade": {"name": "CreateAIStudioResourceBlade", "extension": "Microsoft_Azure_MLTeamAccounts", "doesProvisioning": true, "parameters": {"kind": "<PERSON><PERSON>"}}}]}], "selectionCommands": []}, "links": [{"title": {"property": "AssetType_AIStudio_Project_Overview_Banner.launchButtonText", "module": "../Resx/ClientResources.resjson"}, "uri": "https://ai.azure.com"}], "description": "AssetType_AIStudio.description", "resourceType": {"name": "Microsoft.MachineLearningServices/aistudio", "apiVersion": "2023-04-01"}, "variants": {"ussec": {"links": [{"title": {"property": "AssetType_AIStudio_Project_Overview_Banner.launchButtonText", "module": "../Resx/ClientResources.resjson"}, "uri": "https://ai.azure.microsoft.scloud"}]}, "usnat": {"links": [{"title": {"property": "AssetType_AIStudio_Project_Overview_Banner.launchButtonText", "module": "../Resx/ClientResources.resjson"}, "uri": "https://ai.azure.eaglex.ic.gov"}]}, "fairfax": {"links": [{"title": {"property": "AssetType_AIStudio_Project_Overview_Banner.launchButtonText", "module": "../Resx/ClientResources.resjson"}, "uri": "https://ai.azure.us"}]}}}}