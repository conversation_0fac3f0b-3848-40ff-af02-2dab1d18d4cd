{"$schema": "../node_modules/@microsoft/azureportal-build/lib/definitions/dx.schema.json", "area": "Resource", "stringSource": "../Resx/ClientResources.resjson", "assetType": {"name": "MachineLearningServicesCreate", "create": {"blade": {"name": "CreateMachineLearningServicesBladeV2", "extension": "Microsoft_Azure_MLTeamAccounts"}}, "displayNames": "AssetTypeNames_MachineLearningServices", "icon": {"file": "../Content/SVG/ml_services_icon.svg"}, "keywords": "AssetType.keywords", "description": "AssetType.description", "resourceType": {"name": "Microsoft.MachineLearningServices/workspacescreate", "apiVersion": "2023-04-01"}}}