{"$schema": "../node_modules/@microsoft/azureportal-build/lib/definitions/dx.schema.json", "area": "Resource", "stringSource": "../Resx/ClientResources.resjson", "assetType": {"name": "Core42", "create": {"blade": {"name": "Core42Asset.ReactView", "extension": "Microsoft_Azure_MLTeamAccounts"}}, "displayNames": "AssetType_Core42_Names", "preview": true, "blade": "Core42Asset.ReactView", "part": "MachineLearningServicesPart", "icon": {"file": "../Content/SVG/ai_studio_icon_color.svg"}, "keywords": "AssetType_AIStudio.keywords", "browse": {"type": "AssetTypeBlade"}, "description": "AssetType_AIStudio.description"}}