{"$schema": "../node_modules/@microsoft/azureportal-build/lib/definitions/dx.schema.json", "area": "Resource", "stringSource": "../Resx/ClientResources.resjson", "assetType": {"name": "Model<PERSON>rovider", "create": {"blade": {"name": "ModelProvider.ReactView", "extension": "Microsoft_Azure_MLTeamAccounts"}}, "displayNames": "AssetType_ModelProvider_Names", "preview": true, "blade": "ModelProvider.ReactView", "part": "MachineLearningServicesPart", "icon": {"file": "../Content/SVG/ai_studio_icon_color.svg"}, "keywords": "AssetType_AIStudio.keywords", "browse": {"type": "AssetTypeBlade"}, "description": "AssetType_AIStudio.description"}}