{"$schema": "../node_modules/@microsoft/azureportal-build/lib/definitions/dx.schema.json", "area": "Resource", "stringSource": "../Resx/ClientResources.resjson", "assetType": {"name": "<PERSON><PERSON><PERSON>", "create": {"blade": {"name": "NixtlaAsset.ReactView", "extension": "Microsoft_Azure_MLTeamAccounts"}}, "displayNames": "AssetType_Nixtla_Names", "preview": true, "blade": "NixtlaAsset.ReactView", "part": "MachineLearningServicesPart", "icon": {"file": "../Content/SVG/ai_studio_icon_color.svg"}, "keywords": "AssetType_AIStudio.keywords", "browse": {"type": "AssetTypeBlade"}, "description": "AssetType_AIStudio.description"}}