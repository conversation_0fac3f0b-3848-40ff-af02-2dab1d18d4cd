{"$schema": "../node_modules/@microsoft/azureportal-build/lib/definitions/dx.schema.json", "area": "Resource", "stringSource": "../Resx/ClientResources.resjson", "assetType": {"name": "MachineLearningServices", "displayNames": "AssetTypeNames_MachineLearningServices", "viewModel": {"name": "MachineLearningServicesViewModel", "module": "../../Resource/MachineLearningServicesViewModel"}, "icon": {"file": "../Content/SVG/ml_services_icon.svg"}, "part": "MachineLearningServicesPart", "blade": "Overview.ReactView", "permissions": [{"name": "readServices", "action": "Microsoft.MachineLearningServices/workspaces/read"}, {"name": "writeServices", "action": "Microsoft.MachineLearningServices/workspaces/write"}, {"name": "deleteServices", "action": "Microsoft.MachineLearningServices/workspaces/delete"}, {"name": "moveServices", "action": "Microsoft.MachineLearningServices/resourceGroups/moveResources"}], "keywords": "AssetType.keywords", "resourceMenu": {"staticOverview": true, "resourceProvidedBy": "ProvidedByResourceMenu"}, "browse": {"type": "ResourceType", "query": {"file": "./BrowseQuery.kml"}, "defaultColumns": ["FxColumns.ResourceGroup", "FxColumns.AssetType", "FxColumns.Location", "FxColumns.Subscription"], "customConfig": {"useSupplementalData": true}, "mergedResourceTypes": [{"name": "microsoft.machinelearningservices/registries"}], "commands": [{"visibility": [], "kind": "MenuC<PERSON>mand", "id": "AddAzureMachineLearningCreateDropdown", "displayName": "Create_CreateButton.label", "ariaLabel": "Create_Wizard.title", "icon": "Add", "commands": [{"kind": "OpenBladeCommand", "id": "AddAzureMachineLearningCreate", "displayName": "Browse.addMachineLearningLabel", "ariaLabel": "Browse.addMachineLearningLabel", "content": "Description.createAzureMachineLearningResource", "icon": {"file": "../Content/SVG/ml_services_icon.svg"}, "blade": {"name": "CreateMachineLearningServicesBladeV2", "extension": "Microsoft_Azure_MLTeamAccounts", "doesProvisioning": true, "parameters": {"kind": "<PERSON><PERSON><PERSON>"}}}, {"kind": "OpenBladeCommand", "id": "AddAzureMachineLearningRegistry", "displayName": "Browse.addMachineLearningRegistryLabel", "ariaLabel": "Browse.addMachineLearningRegistryLabel", "content": "Description.createAzureMachineLearningRegistryResource", "icon": {"file": "../Content/SVG/ml_services_icon.svg"}, "blade": {"name": "CreateRegistryBlade", "extension": "Microsoft_Azure_MLTeamAccounts", "doesProvisioning": true, "parameters": {}}}]}, {"visibility": [], "kind": "OpenBladeCommand", "id": "RecentlyDeletedWorkspaces", "displayName": "ClientResources.recentlyDeletedWorkspaces", "ariaLabel": "ClientResources.recentlyDeletedWorkspaces", "icon": {"file": "../Content/SVG/recycle_bin.svg"}, "blade": {"name": "RecentlyDeletedWorkspacesBlade", "parameters": {"assetView": "AML"}, "inContextPane": true}}], "selectionCommands": []}, "description": "AssetType.description", "resourceType": {"name": "Microsoft.MachineLearningServices/workspaces", "apiVersion": "2023-04-01", "kinds": [{"name": "<PERSON><PERSON><PERSON>", "default": true, "displayNames": "AssetTypeNames_MachineLearningServices_Default"}, {"name": "FeatureStore", "preview": true, "displayNames": "AssetTypeNames_MachineLearningServices_FeatureStore"}, {"name": "<PERSON><PERSON>", "icon": {"file": "../Content/SVG/ai_studio_icon_color.svg"}, "displayNames": "AssetTypeNames_MachineLearningServices_Hub"}, {"name": "Project", "icon": {"file": "../Content/SVG/ai_studio_icon_color.svg"}, "displayNames": "AssetTypeNames_MachineLearningServices_Project"}, {"name": "<PERSON>n", "icon": {"file": "../Content/SVG/ai_studio_icon_color.svg"}, "displayNames": "AssetTypeNames_MachineLearningServices_Project"}]}}}