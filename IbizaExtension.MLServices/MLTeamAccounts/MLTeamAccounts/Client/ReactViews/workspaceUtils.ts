import { Workspace } from "../../Client/MLServicesDataModels/Workspace.types";
import { getEnvironmentValue } from "@microsoft/azureportal-reactview/Environment";
import { WorkspaceMappedKind } from "../Shared/Enums";
import { asMappedKind } from "../Shared/ViewAgnostic/Utilities";

export type WorkspaceWithID = Workspace &
    Required<Pick<Workspace, "id">> & {
        kind: WorkspaceMappedKind;
    };

export function getSimplifiedStudioHomeUrl(): string {
    return `${getEnvironmentValue("webWorkspace2BaseUrl")}/projects`;
}

export function hasID(workspace: Workspace): workspace is WorkspaceWithID {
    return !!workspace.id;
}

export function asWorkspaceWithID(workspace: Workspace, id: string): WorkspaceWithID {
    return !hasID(workspace)
        ? { ...workspace, id, kind: asMappedKind(workspace.kind || "") }
        : { ...workspace, kind: asMappedKind(workspace.kind || "") };
}
