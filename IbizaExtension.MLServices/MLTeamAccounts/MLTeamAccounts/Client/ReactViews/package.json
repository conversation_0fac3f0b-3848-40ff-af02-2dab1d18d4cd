{"name": "views", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "jest --config ./test-config/jest.config.js", "build": "cross-env NODE_OPTIONS=--max-old-space-size=4096 extension-build", "build:dev": "cross-env NODE_OPTIONS=--max-old-space-size=4096 extension-build --development", "build-hybrid": "cross-env NODE_OPTIONS=--max-old-space-size=4096 extension-build-hybrid", "build-hybrid:dev": "cross-env NODE_OPTIONS=--max-old-space-size=4096 extension-build-hybrid --development", "serve": "extension-serve", "start": "cross-env NODE_OPTIONS=--max-old-space-size=4096 extension-start", "start-hybrid": "cross-env NODE_OPTIONS=--max-old-space-size=4096 extension-start-hybrid"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@fluentui/react-components": "^9.46.4", "@fluentui/react-hooks": "^8.6.29", "@microsoft/azureportal-reactview": "16.108.0", "react-query": "^3.39.3"}, "devDependencies": {"@azure/arm-features": "3.1.0", "@microsoft/azureportal-build": "16.108.0", "@types/jest": "29.5.14", "cross-env": "^6.0.3", "jest": "29.7.0", "jest-junit": "16.0.0", "ts-jest": "29.3.4"}, "overrides": {"react": "17.0.2", "react-dom": "17.0.2", "@types/react": "17.0.80", "@types/react-dom": "17.0.18"}}