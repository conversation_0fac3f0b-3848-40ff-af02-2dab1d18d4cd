// Portal logging docs: https://eng.ms/docs/products/azure-portal-framework-ibizafx/telemetry/logging-telemetry
// AML logging wiki: https://msdata.visualstudio.com/Vienna/_wiki/wikis/Workspace%20Portal%20Wiki/9825/Ibiza-telemetry-and-alerting?anchor=exttelemetry-action-names
// If changing telemetry names please update the AML logging wiki above.

// LogArea is used in client error logging.
// i.e. Az.log
// These logs go to "ExtEvents" table.
export enum LogArea {
    deleteMachineLearningWorkspaceError = "Overview.ReactView/CommandBar/Delete",
    beginDeleteHub = "Overview.ReactView/Queries/deleteHub",
    beginDeleteMachineLearningWorkspaceError = "Overview.ReactView/Queries/deleteMachineLearningWorkspace",
    Overview = "Overview.ReactView",
    EncryptionSettings = "EncryptionSettings.ReactView",
    AssociatedWorkspaces = "AssociatedWorkspaces.ReactView",
    KeysAndEndpoints = "KeysAndEndpoints.ReactView",
    ChangeAppInsightsPane = "ChangeAppInsightsPane.ReactView",
    ChangeContainerRegistryPane = "ChangeContainerRegistryPane.ReactView",
    DeleteHubPane = "DeleteHubPane.ReactView",
    DeleteHubStatusPane = "DeleteHubStatusPane.ReactView",
}

// LogEvent and Source enums are used in trace logging.
// i.e. Az.trace
// These logs go to "ExtTelemetry" table.
export enum TraceAction {
    LoadWorkspace = "LoadWorkspace",
    LoadAssociatedWorkspaces = "LoadAssociatedWorkspaces",
    LoadProjects = "LoadProjects",
    LoadApplicationInsights = "LoadApplicationInsights",
    LoadContainerRegistry = "LoadContainerRegistry",
    CheckWorkspacesAvailability = "CheckWorkspacesAvailability",
    CheckFeatureRegistration = "CheckFeatureRegistration",
    DeleteWorkspace = "DeleteWorkspace",
    DeleteHubInitiated = "DeleteHubInitiated",
    DeleteHubResult = "DeleteHubResult",
    LoadView = "LoadView",
    LaunchPortalEssentialsLinkClicked = "LaunchPortalEssentialsLinkClicked",
    LaunchPortalClicked = "LaunchPortalClicked",
    UpdateCustomerManagedKey = "UpdateCustomerManagedKey",
    UpdateApplicationInsights = "UpdateApplicationInsights",
    UpdateContainerRegistry = "UpdateContainerRegistry",
    ListWorkspaceEndpoints = "ListWorkspaceEndpoints",
    ListWorkspaceEndpointKeys = "ListWorkspaceEndpointKeys",
    ListOnlineEndpoints = "ListOnlineEndpoints",
    ListServerlessEndpoints = "ListServerlessEndpoints",
    LoadHubConnections = "LoadHubConnections",
    LoadDeleteHubStatus = "LoadDeleteHubStatus",
    LoadPermissions = "LoadPermissions",
}

export type GetWorkspaceAction = TraceAction.LoadWorkspace | TraceAction.CheckWorkspacesAvailability | TraceAction.LoadAssociatedWorkspaces;
export type UpdateWorkspaceAction = TraceAction.UpdateCustomerManagedKey | TraceAction.UpdateApplicationInsights | TraceAction.UpdateContainerRegistry;

export enum TraceSource {
    Overview = "Overview.ReactView",
    EncryptionSettings = "EncryptionSettings.ReactView",
    AssociatedWorkspaces = "AssociatedWorkspaces.ReactView",
    KeysAndEndpoints = "KeysAndEndpoints.ReactView",
    ChangeAppInsightsPane = "ChangeAppInsightsPane.ReactView",
    ChangeContainerRegistryPane = "ChangeContainerRegistryPane.ReactView",
    ApplicationInsightsDropdown = "ApplicationInsightsDropdown",
    ContainerRegistryDropdown = "ContainerRegistryDropdown",
    DeleteHubPane = "DeleteHubPane.ReactView",
    DeleteHubStatusPane = "DeleteHubStatusPane.ReactView",
}
