import * as Az from "@microsoft/azureportal-reactview/Az";

export type ActionModifier = "start" | "complete" | "cancel" | "reset" | "mark";

interface ITelemetryEvent extends Az.TelemetryEvent {
    source: string;
    action: string;
    actionModifier: ActionModifier;
}

function trace(events: readonly ITelemetryEvent[]) {
    Az.trace(events);
}

const eventStarts: Map<string, number> = new Map();
const key = (source: string, event: string) => `${source}-${event}`;

function setTime(source: string, event: string): { timestampMs: number } {
    const timestamp = new Date();
    const timestampMs = timestamp.getTime();
    eventStarts.set(key(source, event), timestampMs);
    return { timestampMs };
}

function getDuration(source: string, event: string): { timestampMs: number; duration: number } {
    const timestamp = new Date();
    const timestampMs = timestamp.getTime();
    const startTime = eventStarts.get(key(source, event));
    const duration = typeof startTime === "number" && Number.isFinite(startTime) && timestampMs > startTime ? timestampMs - startTime : 0;
    return { timestampMs, duration };
}

export function startTrace(source: string, event: string, data?: Record<string, any> & { exception?: unknown; httpStatusCode?: number }) {
    const { timestampMs } = setTime(source, event);
    trace([{ source, actionModifier: "start", action: event, timestamp: timestampMs, data }]);
}

export function start(source: string, event: string) {
    setTime(source, event);
}

export function endTrace(
    source: string,
    event: string,
    data?: Record<string, any> & {
        exception?: unknown;
        httpStatusCode?: number;
    }
) {
    const { timestampMs, duration } = getDuration(source, event);
    trace([{ source, actionModifier: "complete", action: event, timestamp: timestampMs, duration, data }]);
}

export function cancelTrace(source: string, event: string, data?: Record<string, any> & { exception?: unknown; httpStatusCode?: number }) {
    const { timestampMs, duration } = getDuration(source, event);
    trace([{ source, actionModifier: "cancel", action: event, timestamp: timestampMs, duration, data }]);
}
