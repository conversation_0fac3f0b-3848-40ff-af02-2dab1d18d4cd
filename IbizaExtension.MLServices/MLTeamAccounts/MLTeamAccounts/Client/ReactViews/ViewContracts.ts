import type { ViewContract } from "@microsoft/azureportal-reactview/Navigation";

declare global {
    interface ViewContracts {
        "ChangeAppInsightsPane.ReactView": ViewContract<{
            component: typeof import("./Pages/AppInsights/ChangeAppInsightsPane.ReactView").default;
        }>;
        "AssociatedWorkspaces.ReactView": ViewContract<{
            component: typeof import("./Pages/AssociatedWorkspaces/AssociatedWorkspaces.ReactView").default;
        }>;
        "CohereAsset.ReactView": ViewContract<{
            component: typeof import("./Pages/CohereAsset/CohereAsset.ReactView").default;
        }>;
        "ChangeContainerRegistryPane.ReactView": ViewContract<{
            component: typeof import("./Pages/ContainerRegistry/ChangeContainerRegistryPane.ReactView").default;
        }>;
        "Core42Asset.ReactView": ViewContract<{
            component: typeof import("./Pages/Core42Asset/Core42Asset.ReactView").default;
        }>;
        "DeleteHubPane.ReactView": ViewContract<{
            component: typeof import("./Pages/DeleteHub/DeleteHubPane.ReactView").default;
        }>;
        "DeleteHubStatusPane.ReactView": ViewContract<{
            component: typeof import("./Pages/DeleteHub/DeleteHubStatusPane.ReactView").default;
        }>;
        "EncryptionSettings.ReactView": ViewContract<{
            component: typeof import("./Pages/EncryptionSettings/EncryptionSettings.ReactView").default;
        }>;
        "KeysAndEndpoints.ReactView": ViewContract<{
            component: typeof import("./Pages/KeysAndEndpoints/KeysAndEndpoints.ReactView").default;
        }>;
        "Llama2Asset.ReactView": ViewContract<{
            component: typeof import("./Pages/Llama2Asset/Llama2Asset.ReactView").default;
        }>;
        "MistralAsset.ReactView": ViewContract<{
            component: typeof import("./Pages/MistralAsset/MistralAsset.ReactView").default;
        }>;
        "ModelProvider.ReactView": ViewContract<{
            component: typeof import("./Pages/ModelProvider/ModelProvider.ReactView").default;
        }>;
        "NixtlaAsset.ReactView": ViewContract<{
            component: typeof import("./Pages/NixtlaAsset/NixtlaAsset.ReactView").default;
        }>;
        "Overview.ReactView": ViewContract<{
            component: typeof import("./Pages/Overview/Overview.ReactView").default;
        }>;
    }
}
