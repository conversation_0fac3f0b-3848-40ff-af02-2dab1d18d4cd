
import { Ajax, Notifications } from "@microsoft/azureportal-reactview/FxReactCommon.Modules";
import * as Notification from "@microsoft/azureportal-reactview/Notification";
import {
    ClientResources,
    DeleteHubNotification_Cancel as ClientResources_DeleteHubNotification_Cancel,
    DeleteHubNotification_Error as ClientResources_DeleteHubNotification_Error,
    DeleteHubNotification_InProgress as ClientResources_DeleteHubNotification_InProgress,
    DeleteHubNotification_Success as ClientResources_DeleteHubNotification_Success,
} from "../Resx/ClientResources.resjson";
import { ArmId } from "@microsoft/azureportal-reactview/ResourceManagement";
import { armPollingDelayDefault } from "../../Shared/Constants"

/**
 * Informs the azure portal to begin polling for status updates on the workspace delete operation.
 * Updates will be published to the portal notification system.
 */
export function executeWorkspaceDeleteNotificationOperation(workspaceId: string, location: string): Notifications.Notification {
    const armId = ArmId.parse(workspaceId);
    const workspaceName = armId.resourceName;

    const inProgressTitle = ClientResources.progressNotificationMachineLearningServicesDeleteExecutingTitle;
    const inProgressDescription = ClientResources.progressNotificationMachineLearningServicesDeleteExecuting.replace("{workspaceName}", workspaceName);

    const successTitle = ClientResources.progressNotificationMachineLearningServicesDeleteSuccessTitle;
    const successDescription = ClientResources.progressNotificationMachineLearningServicesDeleteSuccess.replace("{workspaceName}", workspaceName);
    const successNotification = {
        status: Notifications.Status.Success as Notifications.Status.Success,
        title: successTitle,
        description: successDescription
    };

    const errorNotification = {
        title: ClientResources.progressNotificationMachineLearningServicesDeleteErrorTitle,
        description: ClientResources.progressNotificationMachineLearningServicesDeleteError.substring(0, ClientResources.progressNotificationMachineLearningServicesDeleteError.indexOf("{lineBreak}")).replace("{workspaceName}", workspaceName)
    };

    return Notification.publishPollingNotification({
        title: inProgressTitle,
        description: inProgressDescription,
        linkTo: {
            extensionName: "Microsoft_Azure_MLTeamAccounts",
            assetType: "MachineLearningServices",
            assetId: workspaceId
        },
        pollingDetails: {
            uri: location,
            keepPollingCodes: [202],
            customDelayInMs: armPollingDelayDefault * 1000,
            defaultFailureNotification: errorNotification,
            responseNotificationByStatusCode: {
                "200": {
                    notification: successNotification
                },
                "204": {
                    notification: successNotification
                }
            }
        }
    });
}

/**
 * Informs the azure portal to begin polling for status updates on the hub delete operation.
 * Updates will be published to the portal notification system.
 */
export function executeHubDeleteNotificationOperation(workspaceId: string, batchPromise: Promise<Ajax.BatchResponseItem<any>>): Notifications.Notification {
    const armId = ArmId.parse(workspaceId);
    const workspaceName = armId.resourceName;

    const inProgressTitle = ClientResources_DeleteHubNotification_InProgress.title;
    const inProgressDescription = ClientResources_DeleteHubNotification_InProgress.description.replace("{workspaceName}", workspaceName);

    const successTitle = ClientResources_DeleteHubNotification_Success.title;
    const successDescription = ClientResources_DeleteHubNotification_Success.description.replace("{workspaceName}", workspaceName);

    const errorTitle = ClientResources_DeleteHubNotification_Error.title;
    const errorDescription = ClientResources_DeleteHubNotification_Error.description.replace("{workspaceName}", workspaceName)

    const cancelTitle = ClientResources_DeleteHubNotification_Cancel.title;

    return Notification.executeAzureAsyncOperation(batchPromise, {
        title: inProgressTitle,
        description: inProgressDescription,
        linkTo: {
            extensionName: "Microsoft_Azure_MLTeamAccounts",
            assetType: "MachineLearningServices",
            assetId: workspaceId
        },
        pollingDetails: {
            keepPollingStatuses: ["Accepted"],
            defaultFailureNotification: {
                title: errorTitle,
                description: errorDescription
            },
            responseNotificationByStatus: {
                "Canceled": {
                    notification: {
                        status: Notifications.Status.Information,
                        title: cancelTitle
                    }
                },
                "Failed": {
                    notification: {
                        status: Notifications.Status.Error,
                        title: errorTitle,
                        description: errorDescription
                    }
                },
                "Succeeded": {
                    notification: {
                        status: Notifications.Status.Success,
                        title: successTitle,
                        description: successDescription
                    }
                }
            }
        }
    });
}
