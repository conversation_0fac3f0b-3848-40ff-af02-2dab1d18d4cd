{"AccountPart.deprecated": "Deprecated", "AccountPart.deprecatedLongMessage": "The resource is no longer available. Please use Machine Learning Services Workspace Extension.", "AccountPart.deprecatedShortMessage": "Use Machine Learning Services Extension.", "AdvancedTab__Key_KeyVaultChangeControl.version": "Version: {version}", "AdvancedTab_Key_KeyVaultChangeControl.action": "Click to select key", "AdvancedTab_Key_KeyVaultChangeControl.key": "Key: {key}", "AdvancedTab_Key_KeyVaultChangeControl.keyVault": "Key vault: {keyvault}", "AdvancedTab_Key_KeyVaultChangeControl.label": "Key vault", "AdvancedTab_Key_KeyVaultChangeControl.purgeProtectionRequired": "When bringing your own encryption key, your Azure Key Vault must have purge protection enabled to protect against accidental loss of data access.", "AdvancedTab_Key_KeyVaultChangeControl.required": "Key is required", "AdvancedTab_Key_KeyVaultChangeControl.title": "Key vault and key", "AdvancedTab_Key_KeyVaultChangeControl.version": "Version: {version}", "AdvancedTab_Section_DataEncryption_ServiceSide.infoText": "Azure machine learning service stores metrics and metadata in an Azure Cosmos DB instance where all data is encrypted ​at rest. By default, the data is encrypted with Microsoft-managed keys. You may choose to bring your own (customer-managed) keys. {0}", "AdvancedTab_Section_DataEncryption_ServiceSide.label": "Use service-side encryption ({0})", "AdvancedTab_Section_DataEncryption_Type.label": "Encrypt data using a customer managed key", "AdvancedTab_Section_DataEncryption.checkboxByoInfo": "Optionally use pre-created resources for storing encrypted workspace data. Using your own resources for encryption, allows for enhanced configuration of these resources in compliance with your organization’s IT and security requirements, but implies additional management actions by you.", "AdvancedTab_Section_DataEncryption.checkboxByoLabel": "Bring existing resources for storing encrypted data (preview)", "AdvancedTab_Section_DataEncryption.checkboxLabel": "Enable encryption using a Customer Managed Key", "AdvancedTab_Section_DataEncryption.cosmosInfo": "Select a pre-created Azure Cosmos DB resource. If you select 'None', Azure ML will create a Cosmos DB resource in a new resource group in your subscription that will be managed by Microsoft. Using your own resources for encryption implies additional management responsibility by you.", "AdvancedTab_Section_DataEncryption.cosmosLabel": "Cosmos DB", "AdvancedTab_Section_DataEncryption.customerManaged": "Customer-managed keys", "AdvancedTab_Section_DataEncryption.header": "Your data is encrypted by default using Microsoft-managed keys. For additional control over your data, you may choose to bring your own key for encryption. {0}", "AdvancedTab_Section_DataEncryption.infoText": "Azure machine learning service stores metrics and metadata in an Azure Cosmos DB instance where all data is encrypted ​at rest. By default, the data is encrypted with Microsoft-managed keys. You may choose to bring your own (customer-managed) keys. {0}", "AdvancedTab_Section_DataEncryption.learnMoreText": "Learn more", "AdvancedTab_Section_DataEncryption.linkText": "Learn more about customer-managed key encryption.", "AdvancedTab_Section_DataEncryption.microsoftManaged": "Microsoft-managed keys", "AdvancedTab_Section_DataEncryption.searchInfo": "Select a pre-created Azure Search resource. If you select 'None', Azure ML will create a Search resource in a new resource group in your subscription that will be managed by Microsoft. Using your own resources for encryption implies additional management responsibility by you.", "AdvancedTab_Section_DataEncryption.searchLabel": "Search", "AdvancedTab_Section_DataEncryption.storageInfo": "Select a pre-created Azure Storage resource. If you select 'None', Azure ML will create a Storage resource in a new resource group in your subscription that will be managed by Microsoft. Using your own resources for encryption implies additional management responsibility by you.", "AdvancedTab_Section_DataEncryption.storageLabel": "Storage", "AdvancedTab_Section_DataEncryption.title": "Data encryption", "AdvancedTab_Section_DataEncryption.warningMessage": "After workspace creation, you cannot change encryption key type between Microsoft-managed keys  and Customer-managed keys.", "AdvancedTab_Section_DataImpact_HBI.label": "High business impact workspace", "AdvancedTab_Section_DataImpact.header": "If your workspace contains sensitive data, you can specify a high business impact workspace. This will control the amount of data Microsoft collects for diagnostic purposes and enables additional encryption in Microsoft managed environments.​", "AdvancedTab_Section_DataImpact.title": "Data impact", "AdvancedTab_Section_ManagedIdentity.description": "A managed identity enables Azure resources to authenticate to cloud services without storing credentials in code. Once enabled, all necessary permissions can be granted via Azure role-based access control. A workspace can be given either a system assigned identity or a user assigned identity.", "AdvancedTab_Section_ManagedIdentity.disabledWarning": "The managed user assigned identity option is only supported if an existing storage account, key vault, and container registry are used.", "AdvancedTab_Section_ManagedIdentity.header": "Managed identity", "AdvancedTab_Section_ManagedIdentity.permissionWarning": "If you bring your own associated resources, instead of having Azure Machine Learning service create them, you must grant the managed identity roles on those resources. Use the {0} to make the assignments.", "AdvancedTab_Section_ManagedIdentity.permissionWarningLinkText": "role assignment ARM template", "AdvancedTab_Section_ManagedIdentity.radioGroupTitle": "Identity type", "AdvancedTab_Section_ManagedIdentity.systemAssignedText": "System assigned identity", "AdvancedTab_Section_ManagedIdentity.userAssignedPickerLabel": "User assigned identity", "AdvancedTab_Section_ManagedIdentity.userAssignedPickerRequiredMessage": "User identity is required", "AdvancedTab_Section_ManagedIdentity.userAssignedPickerSubscriptionMessage": "Workspace and selected user identity must be in the same subscription.", "AdvancedTab_Section_ManagedIdentity.userAssignedPickerText": "Click to select identity", "AdvancedTab_Section_ManagedIdentity.userAssignedText": "User assigned identity", "AdvancedTab_Section_ManagedIdentity.userIdentityNameLabel": "User identity name", "AdvancedTab_Section_ManagedIdentity.userIdentityResourceGroupLabel": "User identity resource group", "AdvancedTab_Section_StorageAccountAccess.credentialBasedText": "Credential-based access", "AdvancedTab_Section_StorageAccountAccess.description": "Azure machine learning allows you to choose between credential-based or identity-based access when connecting to the default storage account.", "AdvancedTab_Section_StorageAccountAccess.header": "Storage account access", "AdvancedTab_Section_StorageAccountAccess.identityBasedText": "Identity-based access", "AdvancedTab_Section_StorageAccountAccess.identityBasedWarningLearnMore": "learn more about RBAC settings", "AdvancedTab_Section_StorageAccountAccess.identityBasedWarningText": "{0} When using identity-based authentication, \"{1}\" and \"{2}\" roles must be granted to {3} that need access on the storage account. Contact your admin for help or", "AdvancedTab_Section_StorageAccountAccess.important": "Important", "AdvancedTab_Section_StorageAccountAccess.individualUsers": "individual users", "AdvancedTab_Section_StorageAccountAccess.learnMoreText": "Learn more", "AdvancedTab_Section_StorageAccountAccess.previewLinkText": "(preview)", "AdvancedTab_Section_StorageAccountAccess.radioGroupTitle": "Storage account access type", "AdvancedTab_Section_StorageAccountAccess.sharedKeyAccessCheckboxLabel": "Disable shared key access", "AdvancedTab_Section_StorageAccountAccess.sharedKeyAccessDisableDocumentationLinkText": "Learn more", "AdvancedTab_Section_StorageAccountAccess.sharedKeyAccessDisableWarning": "Disable shared key access option {0} disables key access at the storage account, any requests to the storage account that are authorized with shared key, including shared access signatures (SAS), will be denied. This option might require additional role configuration to individual users for certain use cases. {1} about disabling shared key access for your workspace's storage account", "AIServices_SummaryTab.label": "AI Services", "AppInsights_Dropdown_Hub.info": "The AI hub uses Azure Application Insights to store monitoring information about your deployed models. You may create a new Azure Application Insights resource or select an existing one in your subscription.", "AppInsights_Dropdown_SettingsBlade_Name.infoDescription": "The name must meet the following requirements:", "AppInsights_Dropdown_SettingsBlade_Name.infoItem1": "Unique across the resource group", "AppInsights_Dropdown_SettingsBlade_Name.infoItem2": "Between 1 and 255 characters long", "AppInsights_Dropdown_SettingsBlade_Name.infoItem3": "Only contain alphanumeric characters, periods, underscores, hyphens, and parenthesis", "AppInsights_Dropdown_SettingsBlade_Name.infoItem4": "Cannot end with a period", "AppInsights_Dropdown_SettingsBlade.nameInvalidMessage": "The name must contain between 1 to 255 characters inclusive. The name only allows alphanumeric characters, periods, underscores, hyphens and parenthesis and cannot end in a period.", "AppInsights_Dropdown_SettingsBlade.nameLabel": "Name", "AppInsights_Dropdown_SettingsBlade.title": "Create new application insights", "AppInsights_Dropdown.createNewAriaLabel": "Create new application insights", "AppInsights_Dropdown.info": "The workspace uses Azure Application Insights to store monitoring information about your deployed models. You may create a new Azure Application Insights resource or select an existing one in your subscription.", "AppInsights_Dropdown.label": "Application insights", "AppInsights_SummaryTab.label": "Application insights", "AssetType_AIStudio_Browse_Commands.createAzureAI": "Create", "AssetType_AIStudio_Browse_Commands.createAzureAICommandBar": "New Azure AI hub", "AssetType_AIStudio_Browse_Commands.createAzureAIHub": "<PERSON><PERSON>", "AssetType_AIStudio_Browse_Commands.createAzureAIHubLabel": "Create Hub", "AssetType_AIStudio_Browse_Commands.createAzureAILabel": "Create Azure AI Foundry", "AssetType_AIStudio_Browse_Commands.createAzureAIProject": "Project", "AssetType_AIStudio_Browse_Commands.createAzureAIProjectLabel": "Create Project", "AssetType_AIStudio_Browse_Description.createAzureAIHub": "Grouping container for projects. Provides security, connectivity, and compute management.", "AssetType_AIStudio_Browse_Description.createAzureAIProject": "Collaborate, organize, and track work to build AI apps.", "AssetType_AIStudio_Hub_AIServices_Settings.title": "Create new Azure AI Services", "AssetType_AIStudio_Hub_Dropdown.createNewAriaLabel": "Create new <PERSON>b", "AssetType_AIStudio_Hub_Dropdown.info": "Grouping container for projects. Provides security, connectivity, and compute management.", "AssetType_AIStudio_Hub_Dropdown.label": "<PERSON><PERSON>", "AssetType_AIStudio_Hub_Essentials_AIServices.label": "AI Services provider", "AssetType_AIStudio_Hub_Essentials_DefaultProjectResourceGroup.label": "Project resource group (default)", "AssetType_AIStudio_Hub_Overview_Banner_Description.label": "Your Azure AI hub provides enterpise-grade security, and a collaborative environment to build AI solutions. Centrally audit usage and cost, and set up connections to your company resources that all projects can use. {}", "AssetType_AIStudio_Hub_Overview_Banner_Description.learnMoreText": "learn more about the Azure AI Foundry", "AssetType_AIStudio_Hub_Overview_Banner.launchButtonText": "Launch Azure AI Foundry", "AssetType_AIStudio_Hub_Overview_Banner.title": "Govern the environment for your team in AI Foundry", "AssetType_AIStudio_KeysAndEndpoints_Command.regenKey1": "Regenerate Key 1", "AssetType_AIStudio_KeysAndEndpoints_Command.regenKey2": "Regenerate Key 2", "AssetType_AIStudio_KeysAndEndpoints.endpoint": "Endpoint", "AssetType_AIStudio_KeysAndEndpoints.hideKeys": "Hide keys", "AssetType_AIStudio_KeysAndEndpoints.key1": "KEY 1", "AssetType_AIStudio_KeysAndEndpoints.key2": "KEY 2", "AssetType_AIStudio_KeysAndEndpoints.message": "These keys are used to access your Azure AI services API. Do not share your keys. Store them securely- for example, using Azure Key Vault. We also recommend regenerating these keys regularly. Only one key is necessary to make an API call. When regenerating the first key, you can use the second key for continued access to the service.", "AssetType_AIStudio_KeysAndEndpoints.regenerateKeys": "Regenerate keys", "AssetType_AIStudio_KeysAndEndpoints.showKeys": "Show keys", "AssetType_AIStudio_KeysAndEndpoints.title": "Keys and Endpoints", "AssetType_AIStudio_Names.lowerPlural": "Azure AI Foundry", "AssetType_AIStudio_Names.lowerSingular": "Azure AI Foundry", "AssetType_AIStudio_Names.plural": "Azure AI Foundry", "AssetType_AIStudio_Names.singular": "Azure AI Foundry", "AssetType_AIStudio_Project_Overview_Banner_Description.label": "Jumpstart your AI solution development with pre-built templates and work on your project either in code or in the studio.", "AssetType_AIStudio_Project_Overview_Banner.launchButtonText": "Launch studio", "AssetType_AIStudio_Project_Overview_Banner.title": "Start building in Azure AI Foundry", "AssetType_AIStudio.description": "Your platform to build generative AI solutions and custom copilots", "AssetType_AIStudio.keywords": "AI, Hub, AI Studio, Azure AI Studio, AI Foundry, Azure AI Foundry, AI Hub, AI Project, AIStudio, Artificial Intelligence, Preview, Deep Learning, Analytics, Data, NLP, Natural Language Processing, CNN, Neural Network, Train, Computer Vision, Models, Data Science, Classification, Regression, Reinforcement Learning, LLM, Chatbot", "AssetType_Cohere_Names.lowerPlural": "Cohere", "AssetType_Cohere_Names.lowerSingular": "Cohere", "AssetType_Cohere_Names.plural": "Cohere", "AssetType_Cohere_Names.singular": "Cohere", "AssetType_Cohere_Page_Description.body": "Select Continue, and you'll be redirected to the model catalog in Azure AI Foundry where you can finish subscribing and start using this model.", "AssetType_Cohere_Page_Description.intro": "Thank you for your interest.", "AssetType_Cohere_Page.primaryButton": "Continue to Azure AI Foundry model catalog", "AssetType_Cohere_Page.title": "Continue to Azure AI Foundry to use this offer", "AssetType_Core42_Names.lowerPlural": "Core42", "AssetType_Core42_Names.lowerSingular": "Core42", "AssetType_Core42_Names.plural": "Core42", "AssetType_Core42_Names.singular": "Core42", "AssetType_Core42_Page_Description.body": "Select Continue, and you'll be redirected to the model catalog in Azure AI Foundry where you can finish subscribing and start using this model.", "AssetType_Core42_Page_Description.intro": "Thank you for your interest.", "AssetType_Core42_Page.primaryButton": "Continue to Azure AI Foundry model catalog", "AssetType_Core42_Page.title": "Continue to Azure AI Foundry to use this offer", "AssetType_Llama2_Names.lowerPlural": "Llama2", "AssetType_Llama2_Names.lowerSingular": "Llama2", "AssetType_Llama2_Names.plural": "Llama2", "AssetType_Llama2_Names.singular": "Llama2", "AssetType_Llama2_Page_Description.body": "Select Continue, and you'll be redirected to the model catalog in Azure AI Foundry where you can finish subscribing and start using this model.", "AssetType_Llama2_Page_Description.intro": "Thank you for your interest.", "AssetType_Llama2_Page.primaryButton": "Continue to Azure AI Foundry model catalog", "AssetType_Llama2_Page.title": "Continue to Azure AI Foundry to use this offer", "AssetType_Mistral_Names.lowerPlural": "<PERSON><PERSON><PERSON>", "AssetType_Mistral_Names.lowerSingular": "<PERSON><PERSON><PERSON>", "AssetType_Mistral_Names.plural": "<PERSON><PERSON><PERSON>", "AssetType_Mistral_Names.singular": "<PERSON><PERSON><PERSON>", "AssetType_Mistral_Page_Description.body": "Select Continue, and you'll be redirected to the model catalog in Azure AI Foundry where you can finish subscribing and start using this model.", "AssetType_Mistral_Page_Description.intro": "Thank you for your interest.", "AssetType_Mistral_Page.primaryButton": "Continue to Azure AI Foundry model catalog", "AssetType_Mistral_Page.title": "Continue to Azure AI Foundry to use this offer", "AssetType_ModelProvider_Names.lowerPlural": "Model Provider", "AssetType_ModelProvider_Names.lowerSingular": "Model Provider", "AssetType_ModelProvider_Names.plural": "Model Provider", "AssetType_ModelProvider_Names.singular": "Model Provider", "AssetType_ModelProvider_Page_Description.body": "Select Continue, and you'll be redirected to the model catalog in Azure AI Foundry where you can finish subscribing and start using this model.", "AssetType_ModelProvider_Page_Description.intro": "Thank you for your interest.", "AssetType_ModelProvider_Page.primaryButton": "Continue to Azure AI Foundry model catalog", "AssetType_ModelProvider_Page.title": "Continue to Azure AI Foundry to use this offer", "AssetType_Nixtla_Names.lowerPlural": "<PERSON><PERSON><PERSON>", "AssetType_Nixtla_Names.lowerSingular": "<PERSON><PERSON><PERSON>", "AssetType_Nixtla_Names.plural": "<PERSON><PERSON><PERSON>", "AssetType_Nixtla_Names.singular": "<PERSON><PERSON><PERSON>", "AssetType_Nixtla_Page_Description.body": "Select Continue, and you'll be redirected to the model catalog in Azure AI Foundry where you can finish subscribing and start using this model.", "AssetType_Nixtla_Page_Description.intro": "Thank you for your interest.", "AssetType_Nixtla_Page.primaryButton": "Continue to Azure AI Foundry model catalog", "AssetType_Nixtla_Page.title": "Continue to Azure AI Foundry to use this offer", "AssetType.description": "Workspaces are where you manage all the models, assets, and data related to your machine learning projects. Create one now to start using Azure Machine Learning.", "AssetType.keywords": "ML, AML, Machine Learning, AI, Artificial Intelligence, Preview, Deep Learning, Analytics, Data, NLP, Natural Language Processing, CNN, Neural Network, Workbench, Train, Notebooks, AutoML, Designer, Computer Vision, Models, Data Science, Classification, Regression, Reinforcement Learning", "AssetTypeNames_MachineLearningExperimentationAccount.singular": "Retired - Machine Learning Experimentation", "AssetTypeNames_MachineLearningServices_Default.lowerPlural": "workspaces", "AssetTypeNames_MachineLearningServices_Default.lowerSingular": "workspace", "AssetTypeNames_MachineLearningServices_Default.plural": "Azure Machine Learning workspaces", "AssetTypeNames_MachineLearningServices_Default.singular": "Azure Machine Learning workspace", "AssetTypeNames_MachineLearningServices_FeatureStore.lowerPlural": "feature stores", "AssetTypeNames_MachineLearningServices_FeatureStore.lowerSingular": "feature store", "AssetTypeNames_MachineLearningServices_FeatureStore.plural": "Azure Machine Learning feature stores", "AssetTypeNames_MachineLearningServices_FeatureStore.singular": "Azure Machine Learning feature store", "AssetTypeNames_MachineLearningServices_Hub.lowerPlural": "Azure AI hubs", "AssetTypeNames_MachineLearningServices_Hub.lowerSingular": "Azure AI hub", "AssetTypeNames_MachineLearningServices_Hub.plural": "Azure AI hubs", "AssetTypeNames_MachineLearningServices_Hub.singular": "Azure AI hub", "AssetTypeNames_MachineLearningServices_Project.lowerPlural": "Azure AI projects", "AssetTypeNames_MachineLearningServices_Project.lowerSingular": "Azure AI project", "AssetTypeNames_MachineLearningServices_Project.plural": "Azure AI projects", "AssetTypeNames_MachineLearningServices_Project.singular": "Azure AI project", "AssetTypeNames_MachineLearningServices.lowerPlural": "workspaces", "AssetTypeNames_MachineLearningServices.lowerSingular": "workspace", "AssetTypeNames_MachineLearningServices.plural": "Azure Machine Learning", "AssetTypeNames_MachineLearningServices.singular": "Azure Machine Learning workspace", "AssetTypeNames_MLApp.lowerPlural": "machine learning online endpoints", "AssetTypeNames_MLApp.lowerSingular": "machine learning online endpoint", "AssetTypeNames_MLApp.plural": "Machine learning online endpoints", "AssetTypeNames_MLApp.singular": "Machine learning online endpoint", "AssetTypeNames_MLAppDeployment.lowerPlural": "machine learning online deployments", "AssetTypeNames_MLAppDeployment.lowerSingular": "machine learning online deployment", "AssetTypeNames_MLAppDeployment.plural": "Machine learning online deployments", "AssetTypeNames_MLAppDeployment.singular": "Machine learning online deployment", "AssetTypeNames_MLRegistry.lowerPlural": "azure machine learning registries", "AssetTypeNames_MLRegistry.lowerSingular": "azure machine learning registry", "AssetTypeNames_MLRegistry.plural": "Azure Machine Learning registries", "AssetTypeNames_MLRegistry.singular": "Azure Machine Learning registry", "AssociatedResource_AppInsights_ChangeBlade.title": "Select an application insights", "AssociatedResource_AppInsights_Properties.changeText": "Change application insights", "AssociatedResource_AppInsights_ReactView_ChangeAppInsights.appInsightsLabel": "Application insights", "AssociatedResource_AppInsights_ReactView_ChangeAppInsights.appInsightsUpdatingSpinner": "Application insights updating...", "AssociatedResource_AppInsights_ReactView_ChangeAppInsights.error": "An error occurred while updating the application insights for this resource.", "AssociatedResource_AppInsights_ReactView_ChangeAppInsights.errorTitle": "Error updating application insights", "AssociatedResource_AppInsights_ReactView_ChangeAppInsights.footerCancel": "Discard", "AssociatedResource_AppInsights_ReactView_ChangeAppInsights.footerSave": "Save", "AssociatedResource_AppInsights_ReactView_ChangeAppInsights.notFoundError": "The resource being updated could not be found.", "AssociatedResource_AppInsights_ReactView_ChangeAppInsights.unauthorizedError": "You do not have permission to update the application insights for this resource.", "AssociatedResource_AppInsights_ReactView_Dropdown.emptyMessage": "No application insights resources found", "AssociatedResource_AppInsights_ReactView_Dropdown.errorLoadingMessage": "Error loading application insights", "AssociatedResource_AppInsights_ReactView_Dropdown.noMatchMessage": "No results matching {0}", "AssociatedResource_AppInsights_ReactView_Dropdown.placeholder": "Select an application insights", "AssociatedResource_ContainerRegistry_ChangeBlade.title": "Select a container registry", "AssociatedResource_ContainerRegistry_Properties.changeText": "Change container registry", "AssociatedResource_ContainerRegistry_ReactView_ChangeContainerRegistry.containerRegistryLabel": "Container registry", "AssociatedResource_ContainerRegistry_ReactView_ChangeContainerRegistry.containerRegistryUpdatingSpinner": "Container registry updating...", "AssociatedResource_ContainerRegistry_ReactView_ChangeContainerRegistry.error": "An error occurred while updating the container registry for this resource.", "AssociatedResource_ContainerRegistry_ReactView_ChangeContainerRegistry.errorTitle": "Error updating container registry", "AssociatedResource_ContainerRegistry_ReactView_ChangeContainerRegistry.footerCancel": "Discard", "AssociatedResource_ContainerRegistry_ReactView_ChangeContainerRegistry.footerSave": "Save", "AssociatedResource_ContainerRegistry_ReactView_ChangeContainerRegistry.notFoundError": "The resource being updated could not be found.", "AssociatedResource_ContainerRegistry_ReactView_ChangeContainerRegistry.unauthorizedError": "You do not have permission to update the container registry for this resource.", "AssociatedResource_ContainerRegistry_ReactView_Dropdown.emptyMessage": "No container registry resources found", "AssociatedResource_ContainerRegistry_ReactView_Dropdown.errorLoadingMessage": "Error loading container registry", "AssociatedResource_ContainerRegistry_ReactView_Dropdown.noMatchMessage": "No results matching {0}", "AssociatedResource_ContainerRegistry_ReactView_Dropdown.placeholder": "Select a container registry", "AssociatedResource_Dropdown_SettingsBlade.discardButtonText": "Discard", "AssociatedResource_Dropdown_SettingsBlade.saveButtonText": "Save", "AssociatedResource_Dropdown.createNewLinkText": "Create new", "AssociatedResource_Dropdown.filterPlaceholder": "Select existing...", "AssociatedResource_Dropdown.newResourceText": "(new) {0}", "AssociatedResource_Dropdown.none": "None", "BasicsTab_HubName.balloonContent": "Name of the AI hub", "BasicsTab_InfoText.additionalResourceInfoText": "For your convenience, these resources are added automatically to the workspace, if regionally available:", "BasicsTab_InfoText.azureApplicationInsights": "Azure Application Insights", "BasicsTab_InfoText.azureKeyVault": "Azure Key Vault", "BasicsTab_InfoText.azureStorage": "Azure Storage", "BasicsTab_ProjectName.balloonContent": "Name of the AI project", "BasicsTab_Region.balloonContent": "Compute targets can only be created in the same region as the workspace. Ensure the selected region has the virtual machine series needed for your workspace compute targets.", "BasicsTab_Region.learnMoreComputeTargets": "Learn more about compute targets", "BasicsTab_Region.viewAvailableVirtualMachines": "View available virtual machines series by region", "BasicsTab_RegistryName.balloonContent": "The registry name must be unique within your resource group.", "BasicsTab_ResourceGroup.balloonContent": "A resource group is a collection of resources that share the same life cycle, permissions, and policies.", "BasicsTab_Subscription.balloonContent": "All resources in an Azure subscription are billed together.", "BasicsTab_WarningText.workspaceErrorMessageText": "The selected subscription doesn’t have permissions to register the resource provider.", "BasicsTab_WorkspaceEdition.basic": "Basic", "BasicsTab_WorkspaceEdition.enterprise": "Enterprise", "BasicsTab_WorkspaceEdition.viewFullPricingDetails": "View full pricing details", "BasicsTab_WorkspaceName.balloonContent": "The workspace name must be unique within your resource group.", "BasicsTab.aIServiceSectionHeader": "Azure AI services base models", "BasicsTab.hubSectionContent": "Projects are grouped by a hub, which provides security configurations, pre-configured connectivity with other Azure resources, compute, storage, and quota.", "BasicsTab.hubSectionHeader": "Share security, connectivity, compute", "BasicsTab.organization": "Organization", "BasicsTab.registryDetails": "Registry details", "BasicsTab.workspaceDetails": "Workspace details", "BasicsTab.workspaceHubDetails": "Resource details", "Browse.addMachineLearningLabel": "New workspace", "Browse.addMachineLearningRegistryLabel": "New registry", "ClientResources.additionalResourceInfo": "For your convenience, these resources are added automatically to the workspace, if regionally available: <a href={0} target=\"_blank\">Azure storage</a>, <a href={1} target=\"_blank\">Azure Application Insights</a> and <a href={2} target=\"_blank\">Azure Key Vault</a>.", "ClientResources.automationLink": "Download a template for automation", "ClientResources.basics": "Basics", "ClientResources.basicsBladeDetailsIntro": "Every workspace must be assigned to an Azure subscription, which is where billing happens. You use resource groups like folders to organize and manage resources, including the workspace you're about to create.", "ClientResources.basicsBladeDetailsIntroLearnMore": "Learn more about Azure resource groups", "ClientResources.basicsBladeHubDetailsIntro": "Select the subscription to manage deployed resources and costs. Use resource groups like folders to organize and manage all your resources. An AI hub is a collaboration environment for a team to share project work, model endpoints, compute, (data) connections, security settings, govern usage.", "ClientResources.basicsBladeInstanceIntro": "Configure your basic workspace settings like its storage connection, authentication, container, and more.", "ClientResources.basicsBladeInstanceIntroLearnMore": "Learn more", "ClientResources.basicsBladeProjectDetailsIntro": "Select a subscription and resource group to organize this and other resources, inherit access, and attribute cost.", "ClientResources.basicsBladeRegistryInstanceIntro": "Configure your basic registry settings like its name and description.", "ClientResources.basicsRegistryBladeDetailsIntro": "Every registry must be assigned to an Azure subscription, which is where billing happens. You use resource groups like folders to organize and manage resources, including the registry you're about to create.", "ClientResources.basicsTabTitle": "Basics", "ClientResources.buttonCreate": "Create", "ClientResources.buttonNext": "Next : {0}", "ClientResources.buttonNextPlaceholder": "Next >", "ClientResources.buttonPrevious": "< Previous", "ClientResources.buttonReviewCreate": "Review + create", "ClientResources.columnCreationTime": "Created on", "ClientResources.columnMachineLearningWorkspaceId": "Workspace ID", "ClientResources.commandCancel": "Cancel", "ClientResources.commandCreateProject": "Create project", "ClientResources.commandDelete": "Delete", "ClientResources.commandDownloadConfig": "Download config.json", "ClientResources.commandDownloadConfigTooltip": "Use this file to load the workspace configuration in your Azure ML SDK notebook or Python script", "ClientResources.confirmationMachineLearningDelete": "Are you sure you want to delete Workspace {0}?", "ClientResources.confirmationMachineLearningRegistryDelete": "Are you sure you want to delete Registry {0}?", "ClientResources.createBladeCreateButtonName": "Create", "ClientResources.createBladeDescriptionLabel": "Description", "ClientResources.createBladeFriendlyNameInfo": "Display name of the AI hub that will be displayed in AI Foundry", "ClientResources.createBladeFriendlyNameLabel": "Friendly name", "ClientResources.createBladeHubNameLabel": "<PERSON><PERSON>", "ClientResources.createBladeHubSubtitle": "Create an Azure AI hub resource", "ClientResources.createBladeHubTitle": "Azure AI hub", "ClientResources.createBladeLocationLabel": "Location", "ClientResources.createBladeMainTabTitle": "Main", "ClientResources.createBladeNextStepsMLFlowAzureMLDescription": "How to use MLflow with Azure ML", "ClientResources.createBladeProjectSubtitle": "Organize and track work, collaborate with others and upload data. Access your work in Azure AI Foundry or Azure Machine Learning Studio.", "ClientResources.createBladeProjectTitle": "Azure AI project", "ClientResources.createBladeRegistryDescriptionKey": "description", "ClientResources.createBladeRegistryDescriptionLabel": "Description", "ClientResources.createBladeRegistryNameLabel": "Name", "ClientResources.createBladeRegistrySubtitle": "Create a machine learning registry", "ClientResources.createBladeResourceGroupLabel": "Resource Group", "ClientResources.createBladeReviewCreateButtonName": "Review + Create", "ClientResources.createBladeReviewTabTitle": "Review", "ClientResources.createBladeSubscriptionIdLabel": "Subscription Id", "ClientResources.createBladeSubscriptionLabel": "Subscription", "ClientResources.createBladeSubtitle": "Create a machine learning workspace", "ClientResources.createBladeTagTabTitle": "Tags", "ClientResources.createBladeTemplateErrorText": "Template <PERSON><PERSON><PERSON>", "ClientResources.createBladeTitle": "Azure Machine Learning", "ClientResources.createBladeWorkspaceCreationProgressNotification": "Workspace creation in progress", "ClientResources.createBladeWorkspaceDeploymentErrorText": "Workspace deployment Error", "ClientResources.createBladeWorkspaceDetails": "Workspace details", "ClientResources.createBladeWorkspaceNameLabel": "Name", "ClientResources.createLocationLabelDefault": "Location", "ClientResources.createLocationLabelPrimaryRegion": "Primary Region", "ClientResources.createLocationLabelRegion": "Region", "ClientResources.createResourceGroup": "Resource group", "ClientResources.createResourceGroupTitle": "Resource group", "ClientResources.createSubscriptionLabel": "Subscription", "ClientResources.createTagsTabIntro": "Tags are name/value pairs that enable you to categorize resources and view consolidated billing by applying the same tag to multiple resources and resource groups.", "ClientResources.createTagsTabLearnMoreText": "Learn more about tags", "ClientResources.createTagsTabTitle": "Tags", "ClientResources.createTagsTabUpdateNotice": "Note that if you create tags and then change resource settings on other tabs, your tags will be automatically updated.", "ClientResources.createTemplateValidationError": "Validation failed. Click here to view details.", "ClientResources.createTemplateValidationInProgress": "Running final validation...", "ClientResources.createTemplateValidationSuccess": "Validation passed", "ClientResources.deploymentCapacityMetric": "Deployment Capacity", "ClientResources.deploymentCpuUtilizationMetric": "Cpu Utilization", "ClientResources.deploymentDiskUtilizationMetric": "Disk Utilization", "ClientResources.deploymentEnvironmentId": "Environment", "ClientResources.deploymentKind": "Deployment Type", "ClientResources.deploymentMemoryUtilizationMetric": "Memory Utilization", "ClientResources.deploymentModelIdLabel": "Model Id", "ClientResources.deploymentName": "Deployment Name", "ClientResources.deploymentProvisioningStateLabel": "Provisioning State", "ClientResources.detailsLabel": "Resource details", "ClientResources.downloadTemplateLinkText": "Download a template for automation", "ClientResources.endpointAuthModeLabel": "Auth Mode", "ClientResources.endpointId": "Endpoint Id", "ClientResources.endpointProvisioningStateLabel": "Provisioning State", "ClientResources.endpointRequestLatencyMetric": "Request Latency", "ClientResources.endpointRequestsPerMinuteMetric": "Request Per Minute", "ClientResources.endpointScoringUriLabel": "Scoring Uri", "ClientResources.endpointSwaggerLabel": "<PERSON>wa<PERSON>", "ClientResources.errorJsonParsingException": "An unexpected parsing error occurred.", "ClientResources.instanceLabel": "Instance details", "ClientResources.labelAutoMLMenuItem": "Automated machine learning", "ClientResources.labelCommandButtonRefresh": "Refresh", "ClientResources.labelComputeMenuItem": "Compute", "ClientResources.labelDataLabeling": "Data Labeling", "ClientResources.labelDataMenuItem": "Data (Preview)", "ClientResources.labelDeployments": "Deployments", "ClientResources.labelDeploymentsMenuItem": "Deployments", "ClientResources.labelImagesMenuItem": "Images", "ClientResources.labelInsights": "Application Insights", "ClientResources.labelKeyVault": "<PERSON>", "ClientResources.labelMlFlowUri": "MLflow tracking URI", "ClientResources.labelMLStudioLauncher": "Visual interface", "ClientResources.labelModelsMenuItem": "Models", "ClientResources.labelPipelinesMenuItem": "Pipelines", "ClientResources.labelProjectHubResource": "Azure AI hub", "ClientResources.labelProjectsMenuItem": "Experiments", "ClientResources.labelProperties": "Properties", "ClientResources.labelProvisioningState": "Provisioning State", "ClientResources.labelQuotaUsage": "Usage + quotas", "ClientResources.labelRegistry": "Container Registry", "ClientResources.labelRequestQuota": "Request Quota", "ClientResources.labelResource": "Resource", "ClientResources.labelStorage": "Storage", "ClientResources.labelTasksMenuItem": "Activities", "ClientResources.labelWorkstationsMenuItem": "Notebook VMs", "ClientResources.learnMore": "Learn more", "ClientResources.location": "Location", "ClientResources.machineLearningCompute": "Managed Compute", "ClientResources.managedKeyVault": "Microsoft-managed", "ClientResources.mlStudioCapability1": "Drag-n-Drop to build machine learning models", "ClientResources.mlStudioCapability2": "No limit to data size or compute capacity for model training", "ClientResources.mlStudioCapability3": "Intrinsic and powerful Python support", "ClientResources.mlStudioCapability4": "One click to deploy your web service", "ClientResources.mlStudioCapability5": "Rich and fast-growing modules support", "ClientResources.mlStudioLaunchLabel": "Launch visual interface", "ClientResources.mlStudioSubtitle": "What's possible with visual interface", "ClientResources.mlStudioTitle": "Visual interface (preview)", "ClientResources.multipleInvalidTabErrorMessage": "Validation failed for the following tabs: {0}. Required information is missing or not valid.", "ClientResources.newResource": "(new) {0}", "ClientResources.newResourceCapitalized": "(New) {0}", "ClientResources.newResourceFormatCaps": "(New) {0}", "ClientResources.noContent": "No content", "ClientResources.none": "None", "ClientResources.onlineEndpointName": "Machine learning online endpoint", "ClientResources.onlineEndpointWorkspaceName": "Workspace", "ClientResources.overview": "Overview", "ClientResources.overviewKeywords": "Summary Home", "ClientResources.progressMachineLearningDeleteError": "The resource could not be deleted.", "ClientResources.progressMachineLearningRegistryDeleteError": "The Registry could not be deleted.", "ClientResources.progressMachineLearningRegistryDeleteExecuting": "Deleting the Registry", "ClientResources.progressMachineLearningServicesDeleteExecuting": "Deleting resource", "ClientResources.progressNotificationMachineLearningRegistryDeleteError": "An error occurred while deleting the Registry name '{registryName}'.{lineBreak}Error details:{lineBreak}{errorMessage}.", "ClientResources.progressNotificationMachineLearningRegistryDeleteErrorTitle": "Registry deletion error", "ClientResources.progressNotificationMachineLearningRegistryDeleteExecuting": "Registry '{registryName}' deletion in progress...", "ClientResources.progressNotificationMachineLearningRegistryDeleteExecutingTitle": "Deleting Registry...", "ClientResources.progressNotificationMachineLearningRegistryDeleteSuccess": "Registry '{registryName}' was deleted successfully.", "ClientResources.progressNotificationMachineLearningRegistryDeleteSuccessTitle": "Registry deleted", "ClientResources.progressNotificationMachineLearningServicesDeleteCancelTitle": "Resource deletion canceled", "ClientResources.progressNotificationMachineLearningServicesDeleteError": "An error occurred while deleting the Workspace name '{workspaceName}'.{lineBreak}Error details:{lineBreak}{errorMessage}.", "ClientResources.progressNotificationMachineLearningServicesDeleteErrorTitle": "Resource deletion error", "ClientResources.progressNotificationMachineLearningServicesDeleteExecuting": "Resource '{workspaceName}' deletion in progress...", "ClientResources.progressNotificationMachineLearningServicesDeleteExecutingTitle": "Deleting resource...", "ClientResources.progressNotificationMachineLearningServicesDeleteSuccess": "Resource '{workspaceName}' was deleted successfully.", "ClientResources.progressNotificationMachineLearningServicesDeleteSuccessTitle": "Succesfully deleted", "ClientResources.propertiesBladeApplicationInsightsLabel": "Application Insights", "ClientResources.propertiesBladeContainerRegistryIdLabel": "Container Registry ID", "ClientResources.propertiesBladeCreationDateLabel": "Created on", "ClientResources.propertiesBladeKeyVaultIdLabel": "Key Vault ID", "ClientResources.propertiesBladeLocationLabel": "Location", "ClientResources.propertiesBladeResourceGroupLabel": "Resource Group", "ClientResources.propertiesBladeResourceIdLabel": "Resource ID", "ClientResources.propertiesBladeStorageAccountIdLabel": "Storage account ID", "ClientResources.propertiesBladeSubscriptionIdLabel": "Subscription ID", "ClientResources.propertiesBladeSubscriptionNameLabel": "Subscription Name", "ClientResources.propertiesBladeWorkspaceHubIdLabel": "AI hub ID", "ClientResources.propertiesBladeWorkspaceIdLabel": "Machine Learning workspace ID", "ClientResources.quickLinkUnderOverviewBladeAriaLabel": "Quick link under Overview blade.", "ClientResources.quotaBladeTitle": "Usage + quotas", "ClientResources.quotaNote": "Note:", "ClientResources.quotaProviderNotRegisteredErrorMsg": "Your subscription {0} is not registered with the '{1}' resource provider. Please create a 'Machine Learning service' workspace to auto-register and retry submitting the support request.", "ClientResources.quotaProviderRegisteringErrorMsg": "Registering your subscription {0} with the '{1}' resource provider.", "ClientResources.quotaQuotaSettingTabHeader": "Configure quotas", "ClientResources.quotaRequestCurrentLimit": "Current vCPU limit", "ClientResources.quotaRequestDocumentationInfoBox": "Click to learn more about Compute (cores/vCPUs) quota increase requests.", "ClientResources.quotaRequestHeader": "Please enter the limit for any resource(s) you are requesting:", "ClientResources.quotaRequestLogOutputMessageDetail": "{0} - current value: {1} / requested value: {2}", "ClientResources.quotaRequestNewLimit": "New vCPU limit", "ClientResources.quotaRequestNotFound": "No quota resources found for given location", "ClientResources.quotaRequestResourceName": "Resource name", "ClientResources.quotaRequestSubmit": "Save and continue", "ClientResources.quotaRequestTitle": "Quota details", "ClientResources.quotaRequestVMFamily": "VM series", "ClientResources.quotaSubscriptionViewClusterHelptext": "For a cluster we show your currently allocated cores and maximum cores it can scale to.", "ClientResources.quotaSubscriptionViewResourceNameHelptext": "Expand each VM family to view your quota allocation and resource usage by workspace and further to view your clusters and instances.", "ClientResources.quotaSubscriptionViewTabHeader": "Subscription view", "ClientResources.quotaSubscriptionViewUsageHelptext": "For an instance it continues to use quota even in Stopped state so you can restart it at any time.", "ClientResources.quotaTableHeaderDedicated": "Dedicated cores usage", "ClientResources.quotaTableHeaderLowPriority": "Low priority cores usage", "ClientResources.quotaTableHeaderQuota": "Resource name", "ClientResources.quotaTableNoData": "No data to display", "ClientResources.quotaTableServerError": "The server encountered an error processing current request. Please refresh the table again.", "ClientResources.quotaTableTotalSubscriptionQuota": "Total subscription quota", "ClientResources.quotaWorkspaceQuotaExceedSubscriptionLimit": "Workspace level quota cannot exceed the subscription level quota limit.", "ClientResources.quotaWorkspaceQuotaInsufficientPermissions": "You are not authorized to set quota at the workspace level. Please reach out to your subscription admin to help allocate resources between workspaces.", "ClientResources.quotaWorkspaceQuotaInvalidVMFamilyName": "Please specify a VM family that is supported in the $region region and you have subscription level quota for.", "ClientResources.quotaWorkspaceQuotaLessThanMinimumClusterCores": "Workspace level quota cannot be less than the cores required to support the minimum nodes of this workspace’s clusters and instances.", "ClientResources.quotaWorkspaceQuotaNewLimitHelpText": "Allocate subscription level quota between workspaces by setting it here. To change the all-up subscription level quota, use the request quota button at the bottom. Note that you need to be a subscription owner to modify these values.", "ClientResources.quotaWorkspaceQuotaPlaceHolder": "Unallocated cores: {0}, Maximum: {1}", "ClientResources.quotaWorkspaceQuotaResourceNameHelpText": "Expand each VM size to view and allocate subscription level quota between workspaces.", "ClientResources.quotaWorkspaceQuotaUnknownError": "Unknown error", "ClientResources.quotaWorkspaceViewClusterHelptext": "For a cluster we show your currently allocated cores and maximum cores it can scale to.", "ClientResources.quotaWorkspaceViewResourceNameHelptext": "Expand each workspace to view your quota allocation and resource usage by VM family and further to view your clusters and instances.", "ClientResources.quotaWorkspaceViewTabHeader": "Workspace view", "ClientResources.quotaWorkspaceViewUsageHelptext": "For an instance it continues to use quota even in Stopped state so you can restart it at any time.", "ClientResources.recentlyDeletedWorkspaces": "Recently deleted", "ClientResources.resourceLocationColumn": "Location", "ClientResources.resourceNameRequired": "The resource name is required", "ClientResources.scaling": "Sc<PERSON>", "ClientResources.selectLocation": "Select a location", "ClientResources.selectSubscription": "Select a subscription", "ClientResources.singleInvalidTabErrorMessage": "Validation failed for the following tab: {0}. Required information is missing or not valid.", "ClientResources.subtitleEnvironmentInfoBlade": "Configure compute for deploying and managing models", "ClientResources.summaryTabTitle": "Review + create", "ClientResources.tabValidationErrors": "Validation failed. Required information is missing or not valid.", "ClientResources.tags": "Tags", "ClientResources.textAzureDatabricksOptionCreateNew": "Create New", "ClientResources.textAzureDatabricksOptionUseExisting": "Use Existing", "ClientResources.textLaunchWebWorkspace": "Explore your Machine Learning workspace to run and track experiments, compare model performance, and deploy models.", "ClientResources.textLaunchWebWorkspaceHeader": "Explore your Azure Machine Learning workspace", "ClientResources.textMachineLearningServicesAutoMLLinkBody": "Automatically create a model from your existing data.", "ClientResources.textMachineLearningServicesAutoMLLinkHeader": "Create a new Automated Machine Learning Model (Preview)", "ClientResources.textMachineLearningServicesDocumentationLinkBody": "Learn how to use Azure Machine Learning.", "ClientResources.textMachineLearningServicesDocumentationLinkHeader": "View Documentation", "ClientResources.textMachineLearningServicesForumLinkBody": "Join the discussion of Azure Machine Learning.", "ClientResources.textMachineLearningServicesForumLinkHeader": "View Forum", "ClientResources.textMachineLearningServicesManageFeatoreStoreLinkHeader": "Create and Manage", "ClientResources.textMachineLearningServicesManageFeatureStoreLinkBody": "Learn how to create and manage Machine Learning registries.", "ClientResources.textMachineLearningServicesManageRegistryLinkBody": "Learn how to create and manage Machine Learning registries.", "ClientResources.textMachineLearningServicesManageRegistryLinkHeader": "Create and Manage", "ClientResources.textMachineLearningServicesNotebookVMLinkBody": "Quickly get started with the Python SDK and run sample experiments with Azure Machine Learning Notebook VMs.", "ClientResources.textMachineLearningServicesNotebookVMLinkHeader": "Get Started with Sample Notebooks (Preview)", "ClientResources.textMachineLearningServicesShareRegistryLinkBody": "Learn how to share Machine Learning assets using registries.", "ClientResources.textMachineLearningServicesShareRegistryLinkHeader": "Share models, components and environments", "ClientResources.textMachineLearningServicesVisualInterfaceLinkBody": "Drag and drop existing components to create new models.", "ClientResources.textMachineLearningServicesVisualInterfaceLinkHeader": "Build a model using the Visual Interface (Preview)", "ClientResources.textNotAvailable": "N/A", "ClientResources.textViennaGitHubLinkBody": "Get inspired by a large collection of machine learning examples.", "ClientResources.textViennaGitHubLinkHeader": "View more samples at GitHub", "ClientResources.titleAssetsGroup": "Assets", "ClientResources.titleAuthoringGroup": "Authoring (Preview)", "ClientResources.titleDeployments": "Deployments", "ClientResources.titleEnvironmentInfoBlade": "Machine Learning Compute", "ClientResources.titleError": "Error", "ClientResources.titleGiveFeedback": "Give feedback", "ClientResources.titleInsights": "Insights (preview)", "ClientResources.titleMachineLearningServicesDeleteConfirmationMessageBox": "Delete Workspace", "ClientResources.titleMachineLearningServicesRegistryDeleteConfirmationMessageBox": "Delete Registry", "ClientResources.titleMonitoringLens": "Getting Started", "ClientResources.titleSettings": "Settings", "ClientResources.titleSupport": "Support + troubleshooting", "ClientResources.titleWebWorkspaceBlade": "Machine Learning", "ClientResources.validationAIServicesNameAlreadyInUse": "This AI Services name already exists", "ClientResources.validationAppInsightsNameAlreadyInUse": "This application insights name already exists", "ClientResources.validationContainerRegistryNameAlreadyInUse": "This container registry name already exists", "ClientResources.validationCreateWorkspacePermission": "You don't have the required permissions ({0}) to create an account under the selected resource group", "ClientResources.validationDependentResourcesAlreadyInUse": "Dependent resources with this name already exist", "ClientResources.validationError": "There was an error while attempting to validate the resource", "ClientResources.validationHubNameAlreadyInUse": "This AI hub name already exists", "ClientResources.validationKeyVaultNameAlreadyInUse": "This key vault name already exists", "ClientResources.validationPrimaryRegionNotSelected": "Please ensure the primary region is one of the selected values", "ClientResources.validationProjectNameAlreadyInUse": "This AI project name already exists", "ClientResources.validationRegionNotSelected": "Please select at least one region", "ClientResources.validationRegistryDescriptionTooLarge": "This registry description has a maximum length of 256 characters.", "ClientResources.validationRegistryNameAlreadyInUse": "This registry name already exists", "ClientResources.validationRegistryNameInvalid": "Registry name must be between 3 and 33 characters long. Its first character has to be alphanumeric, and the rest may contain hyphens and underscores. No whitespace is allowed.", "ClientResources.validationStorageAccountNameAlreadyInUse": "This storage account name already exists", "ClientResources.validationWorkspaceNameAlreadyInUse": "This workspace name already exists", "ClientResources.validationWorkspaceNameAlreadyInUseAndSoftDeleted": "This workspace name already exists, or is being reserved by a workspace which was previously soft deleted. Please use a different name", "ClientResources.validationWorkspaceNameInvalid": "Resource name must be between 3 and 33 characters long. Its first character has to be alphanumeric, and the rest may contain hyphens and underscores. No whitespace is allowed.", "ClientResources.validationWorkspaceNameReserved": "This workspace name is reserved", "ClientResources.workspaceCancelUpgradeButtonText": "Cancel", "ClientResources.workspaceConfirmUpgradeButtonText": "Confirm Upgrade", "ClientResources.workspaceCreateSKUPricingDetails": "View full pricing details", "ClientResources.workspaceCreateSKUTooltip": "The Enterprise SKU (preview) boosts productivity for all ML skill levels and offers robust MLOps capabilities to manage the complete ML lifecycle. THe Basic SKU workspace allows for open source development and a code-first experience.", "ClientResources.workspaceErrorMessage": "The selected subscription doesn’t have permissions to register the resource provider. For more information, <a href = {0} target = \"_blank\">click here</a>.", "ClientResources.workspaceLoadFailure": "Workspace with ID \"{0}\" could not be loaded.", "ClientResources.workspaceSKUPropertyLabel": "Workspace edition", "ClientResources.workspaceTwoBannerBasicSKUBody": "Introducing a new immersive experience (preview) for managing the end-to-end machine learning lifecycle.", "ClientResources.workspaceTwoBannerBasicSKUTitle": "Launch Preview Now", "ClientResources.workspaceTwoBannerBody": "An immersive experience for managing the end-to-end machine learning lifecycle.", "ClientResources.workspaceTwoBannerBodyPreview": "Introducing a new immersive experience (preview) for managing the end-to-end machine learning lifecycle.", "ClientResources.workspaceTwoBannerButton": "Launch now", "ClientResources.workspaceTwoBannerLink": "Learn more", "ClientResources.workspaceTwoNoticeButton": "Launch now", "ClientResources.workspaceTwoNoticeMessage": "Contents of this page will be moving to a new immersive experience for\n      managing the end-to-end machine learning lifecycle. Compute targets will\n      be manageable from both locations. Features provided in preview are\n      offered at no additional charge but may not remain so after general\n      availability.", "ClientResources.workspaceUpgradeAboutOurPricing": "about our pricing", "ClientResources.workspaceUpgradeAuthorizationFailed": "You currently do not have permissions to upgrade your workspace. Please contact your IT administrator.", "ClientResources.workspaceUpgradeBannerText": "Upgrade this workspace to Enterprise edition (preview) to use visual machine learning, advanced automated machine learning, and to manage quota.", "ClientResources.workspaceUpgradeBulletPoint": "You need an Enterprise edition (preview) workspace to view and set quotas on Enterprise workspaces.", "ClientResources.workspaceUpgradeButtonText": "Upgrade", "ClientResources.workspaceUpgradeConfirmationBoxContent": "Upgrade workspace to access Enterprise (preview) capabilities. Visit our {0} for more information.", "ClientResources.workspaceUpgradeConfirmationBoxTitle": "Confirm workspace upgrade", "ClientResources.workspaceUpgradeLearnMore": "Learn more", "ClientResources.workspaceUpgradePricingPage": "pricing page", "ClientResources.workspaceUpgradeQuickLinkBannerText": "Upgrade this workspace to Enterprise edition (preview) to use UI-based tools for all skill levels and more.", "ClientResources.workspaceUpgradeQuickLinkLearnAboutEnterpriseEdition": "Learn about Enterprise Edition (preview)", "ClientResources.workspaceUpgradeQuickLinkPostUpgradeBannerText": "Use the Enterprise edition (preview) to access UI-based tools for all skill levels, built-in MLOps and more", "ClientResources.workspaceUpgradeSetQuotaOperationNotAllowed": "This feature is disabled for you. Please upgrade to an Enterprise edition (preview) workspace to configure quotas.", "ClientResources.workspaceUpgradeUpgradeCompleteNotificationContent": "Your workspace {0} upgraded successfully.", "ClientResources.workspaceUpgradeUpgradeCompleteNotificationTitle": "Workspace Upgrade Complete", "ClientResources.workspaceUpgradeUpgradeFailed": "An unexpected error occurred, please try upgrading your workspace again, or submitting a support ticket.", "ClientResources.workspaceUpgradeUpgradeFailedNotificationContent": "Your workspace {0} did not upgrade successfully.", "ClientResources.workspaceUpgradeUpgradeFailedNotificationTitle": "Workspace Upgrade Failed", "ClientResources.workspaceUpgradeUpgradeSucceeded": "Upgrade Azure Machine Learning workspace {0} succeeded.", "ClientResources.workspaceUpgradeUpgrading": "Upgrading Azure Machine Learning workspace {0}", "ClientResources.workspaceUpgradeUpgradingNotificationContent": "Your workspace {0} is upgrading from Basic to Enterprise", "ClientResources.workspaceUpgradeUpgradingNotificationTitle": "Workspace is currently upgrading", "Commands.delete": "Delete", "ContainerRegistry_Dropdown_SettingsBlade_Name.infoDescription": "The name must meet the following requirements:", "ContainerRegistry_Dropdown_SettingsBlade_Name.infoItem1": "Unique across all container registries in Azure", "ContainerRegistry_Dropdown_SettingsBlade_Name.infoItem2": "Between 5 and 50 characters long", "ContainerRegistry_Dropdown_SettingsBlade_Name.infoItem3": "Only contain alphanumeric characters", "ContainerRegistry_Dropdown_SettingsBlade_Sku.info": "All SKUs provide the same programmatic capabilities. Choosing a higher SKU will provide more performance and scale.", "ContainerRegistry_Dropdown_SettingsBlade_Sku.infoLearnMore": "Learn more", "ContainerRegistry_Dropdown_SettingsBlade.nameInvalidMessage": "Resource names may contain alpha numeric characters only and must be between 5 and 50 characters.", "ContainerRegistry_Dropdown_SettingsBlade.nameLabel": "Name", "ContainerRegistry_Dropdown_SettingsBlade.skuBasic": "Basic", "ContainerRegistry_Dropdown_SettingsBlade.skuLabel": "SKU", "ContainerRegistry_Dropdown_SettingsBlade.skuPremium": "Premium", "ContainerRegistry_Dropdown_SettingsBlade.skuStandard": "Standard", "ContainerRegistry_Dropdown_SettingsBlade.title": "Create new container registry", "ContainerRegistry_Dropdown.createNewAriaLabel": "Create new container registry", "ContainerRegistry_Dropdown.info": "A container registry is used to register docker images used in training and deployments. To minimize costs, a new Azure Container Registry resource is created only after you build your first image. Alternatively, you may choose to create the resource now or select an existing one in your subscription.", "ContainerRegistry_Dropdown.label": "Container registry", "ContainerRegistry_SummaryTab.label": "Container registry", "Create_CreateButton.label": "Create", "Create_Wizard.title": "Create Azure Machine Learning", "CreateBlade_Advanced_Review_Encryption.title": "Encryption", "CreateBlade_Advanced_Review_Identity.title": "Identity", "CreateBlade_Advanced_Review_Resources.title": "Resources", "CreateBlade_Advanced_Review.byoCosmosText": "Cosmos DB for customer managed keys", "CreateBlade_Advanced_Review.byoSearchText": "Search service for customer managed keys", "CreateBlade_Advanced_Review.byoStorageText": "Storage account for customer managed keys", "CreateBlade_Advanced_Review.byoValueFormat": "{0} ({1})", "CreateBlade_Advanced_Review.credentialBasedStorageAccountAccess": "Credential-based", "CreateBlade_Advanced_Review.customerManagedKeys": "Customer-managed keys", "CreateBlade_Advanced_Review.disabled": "Disabled", "CreateBlade_Advanced_Review.enabled": "Enabled", "CreateBlade_Advanced_Review.enableHBIFlag": "Enable HBI Flag", "CreateBlade_Advanced_Review.encryptionType": "Encryption type", "CreateBlade_Advanced_Review.identityBasedStorageAccountAccess": "Identity-based", "CreateBlade_Advanced_Review.keyURI": "Key URI", "CreateBlade_Advanced_Review.keyVault": "Key vault", "CreateBlade_Advanced_Review.microsoftManagedKeys": "Microsoft-managed keys", "CreateBlade_Advanced_Review.sharedKeyAccess": "Shared key access", "CreateBlade_Advanced_Review.storageAccountAccessType": "Storage account access type", "CreateBlade_Network_ConnectivityMethod.allNetworks": "Enable public access from all networks", "CreateBlade_Network_ConnectivityMethod.private": "Disable public access and use private endpoints", "CreateBlade_Network_ConnectivityMethod.skuPrivateEndpointErrorMessage": "Private with Internet Outbound and Private with Approved Outbound requires premium SKU container registry for having a private endpoint connection. You can create a new premium container registry or select an existing premium container registry in the Basics tab to use with this private workspace.", "CreateBlade_Network_Hub.networkIsolationDescription": "Projects associated to an Azure AI hub share network, and can access resources in your virtual network without additional configuration. Choose the type of network isolation you need, from not isolated at all to an entirely separate virtual network managed by Azure Machine Learning.", "CreateBlade_Network_NetworkIsolation_AllowInternetOutbound.title": "Allow Internet Outbound", "CreateBlade_Network_NetworkIsolation_AllowOnlyApprovedOutbound.title": "Allow Only Approved Outbound", "CreateBlade_Network_NetworkIsolation_Disabled.title": "Disabled", "CreateBlade_Network_NetworkIsolation_PrivateApprovedOutbound.descriptionItemOne": "Workspace is accessed via private endpoint", "CreateBlade_Network_NetworkIsolation_PrivateApprovedOutbound.descriptionItemThree": "Outbound data movement is restricted to approved targets", "CreateBlade_Network_NetworkIsolation_PrivateApprovedOutbound.descriptionItemTwo": "Compute can access allowlisted resources only", "CreateBlade_Network_NetworkIsolation_PrivateApprovedOutbound.learnMore": "Learn more about data exfiltration protection", "CreateBlade_Network_NetworkIsolation_PrivateApprovedOutbound.title": "Private with Approved Outbound", "CreateBlade_Network_NetworkIsolation_PrivateInternetOutbound.descriptionItemOne": "Workspace is accessed via private endpoint", "CreateBlade_Network_NetworkIsolation_PrivateInternetOutbound.descriptionItemThree": "Outbound data movement is unrestricted", "CreateBlade_Network_NetworkIsolation_PrivateInternetOutbound.descriptionItemTwo": "Compute can access private resources", "CreateBlade_Network_NetworkIsolation_PrivateInternetOutbound.learnMore": "Learn more about private networks", "CreateBlade_Network_NetworkIsolation_PrivateInternetOutbound.title": "Private with Internet Outbound", "CreateBlade_Network_NetworkIsolation_Public.descriptionItemOne": "Workspace is accessed via public endpoint", "CreateBlade_Network_NetworkIsolation_Public.descriptionItemThree": "Outbound data movement is unrestricted", "CreateBlade_Network_NetworkIsolation_Public.descriptionItemTwo": "Compute can access public resources", "CreateBlade_Network_NetworkIsolation_Public.learnMore": "Learn more about public networks", "CreateBlade_Network_NetworkIsolation_Public.title": "Public", "CreateBlade_Network_NetworkIsolation.skuErrorMessage": "Allow Internet Outbound and Allow Only Approved Outbound requires premium SKU container registry for having a private endpoint connection.", "CreateBlade_Network_PrivateEndpoints.add": "Add", "CreateBlade_Network_PrivateEndpoints.clickToAdd": "Click on add to create a private endpoint", "CreateBlade_Network_PrivateEndpoints.locationMismatch": "Workspace ({0}) and private endpoint connection ({1}) must be in the same location in order to get compute instance and clusters accessed properly in the virtual network.", "CreateBlade_Network_PrivateEndpoints.name": "Name", "CreateBlade_Network_PrivateEndpoints.nameAndSubResource": "{0} ({1})", "CreateBlade_Network_PrivateEndpoints.nameSubResourceAndDns": "{0} ({1}) ({2})", "CreateBlade_Network_PrivateEndpoints.noContent": "-", "CreateBlade_Network_PrivateEndpoints.registrySubResourceHelp": "This is the specific sub-resource for the new registry that this private endpoint will be able to access.", "CreateBlade_Network_PrivateEndpoints.registrySubResourceLabel": "Registry sub-resource", "CreateBlade_Network_PrivateEndpoints.subResourceHelp": "This is the specific sub-resource for the new workspace that this private endpoint will be able to access.", "CreateBlade_Network_PrivateEndpoints.subResourceLabel": "Workspace sub-resource", "CreateBlade_Network_PrivateEndpoints.title": "Private endpoints", "CreateBlade_Network_PrivateEndpoints.type": "Private Endpoint", "CreateBlade_Network_SubnetDelegate.learnMore": "For more information, see", "CreateBlade_Network_SubnetDelegate.message": "Follows different management boundaries than your hub and projects, injected into your Azure VNET.", "CreateBlade_Network_SubnetDelegate.title": "Delegate Azure virtual network subnet for agents", "CreateBlade_Network_WorkspaceApprovedOutbound_OutboundAccess.azureFirewallSku": "Azure Firewall SKU (Preview)", "CreateBlade_Network_WorkspaceApprovedOutbound_OutboundAccess.azureFirewallSkuDescription": "Select from Basic or Standard SKU for the Azure Firewall deployment. For more information on Azure Firewall, see", "CreateBlade_Network_WorkspaceApprovedOutbound_OutboundAccess.azureFirewallSkuPricingText": "Pricing", "CreateBlade_Network_WorkspaceApprovedOutbound_OutboundAccess.recommendedTargetDescription": "There are several optional outbound targets recommended for your workspace in scenarios such as AutoML and Data Labeling. You can modify or delete them,", "CreateBlade_Network_WorkspaceApprovedOutbound_OutboundAccess.recommendedTargetLearnMore": "Learn more about recommended targets", "CreateBlade_Network_WorkspaceApprovedOutbound_OutboundAccess.requiredTargetDescription": "There are a few outbound targets added by Azure Machine Learning that are required for your workspace to access things like storage,  notebooks, and deployment environments.", "CreateBlade_Network_WorkspaceApprovedOutbound_OutboundAccess.requiredTargetLearnMore": "Learn more about required targets", "CreateBlade_Network_WorkspaceApprovedOutbound_PrivateNetworkSettings.description": "Add private endpoints people can use to access your workspace, and manage the outbound targets to which the workspace can access", "CreateBlade_Network_WorkspaceApprovedOutbound_PrivateNetworkSettings.title": "Private network and data exfiltration settings", "CreateBlade_Network_WorkspaceInternetOutbound_OutboundAccess.useMyOwnVirtualNetwork": "Use my own virtual network", "CreateBlade_Network_WorkspaceInternetOutbound_OutboundAccess.virtualNetworkManagedByAML": "Use a virtual network managed by Azure Machine Learning", "CreateBlade_Network_WorkspaceInternetOutbound_OutboundAccess.virtualNetworkManagedByAMLDescription": "Private endpoints are required for your workspace to access private resources like storage, notebooks, and deployment environments. You can also add your additional private link targets here for your custom scenarios.", "CreateBlade_Network_WorkspaceInternetOutbound_OutboundAccess.virtualNetworkManagedByAMLLearnMore": "Learn more about required private link targets", "CreateBlade_Network_WorkspaceInternetOutbound_PrivateNetworkSettings.description": "Add private endpoints people can use to access your workspace, and choose how to manage outbound access from your workspace to things like Storage Accounts, Key Vaults and Registries.", "CreateBlade_Network_WorkspaceInternetOutbound_PrivateNetworkSettings.title": "Private network settings", "CreateBlade_Network_WorkspaceInternetOutbound.targetDescription": "There are a few private endpoints required for your workspace to access private resources like storage. You can also add your additional private link targets here for your custom scenarios.", "CreateBlade_Network_WorkspaceInternetOutbound.targetLearnMoreLink": "Learn more about required private link target", "CreateBlade_Network_WorkspaceOutboundAccess.activeStatusMessage": "The rule has been applied and effective.", "CreateBlade_Network_WorkspaceOutboundAccess.addUserDefinedOutboundRules": "Add user-defined outbound rules", "CreateBlade_Network_WorkspaceOutboundAccess.connectionName": "Connection Name", "CreateBlade_Network_WorkspaceOutboundAccess.deleteUserDefinedOutboundRules": "Delete user-defined outbound rules", "CreateBlade_Network_WorkspaceOutboundAccess.destination": "Destination", "CreateBlade_Network_WorkspaceOutboundAccess.destinationType": "Destination Type", "CreateBlade_Network_WorkspaceOutboundAccess.inactivePrivateEndpointStatusMessage": "The rule will become active when managed network is provisioned, otherwise please check if approval is pending for the target resource.", "CreateBlade_Network_WorkspaceOutboundAccess.inactiveStatusMessage": "The rule will become active when managed network is provisioned.", "CreateBlade_Network_WorkspaceOutboundAccess.parentRules": "Parent Rules", "CreateBlade_Network_WorkspaceOutboundAccess.provisionManagedVirtualNetwork": "Provision managed virtual network (Preview)", "CreateBlade_Network_WorkspaceOutboundAccess.provisionManagedVirtualNetworkInfo": "Managed virtual network will be provisioned at workspace creation. Charges will be incurred for network resources, such as private endpoint.", "CreateBlade_Network_WorkspaceOutboundAccess.provisionManagedVirtualNetworkSummaryTitle": "Provision managed virtual network", "CreateBlade_Network_WorkspaceOutboundAccess.status": "Status", "CreateBlade_Network_WorkspacePrivateOutbound.addUserDefinedOutboundRuleText": "You can also add your own outbound targets here for your custom scenarios.", "CreateBlade_Network_WorkspacePrivateOutbound.dependencyOutboundRules": "Dependency outbound rules", "CreateBlade_Network_WorkspacePrivateOutbound.recommendedOutboundRules": "Recommended outbound rules", "CreateBlade_Network_WorkspacePrivateOutbound.requiredOutboundRules": "Required outbound rules", "CreateBlade_Network_WorkspacePrivateOutbound.userDefinedOutboundRules": "User-defined outbound rules", "CreateBlade_Network.azureFirewallSku": "Azure Firewall SKU", "CreateBlade_Network.connectivityMethod": "Connectivity method", "CreateBlade_Network.displayPrivateDnsZone": "Private DNS Zone", "CreateBlade_Network.displayRegion": "Region", "CreateBlade_Network.displayResourceGroup": "Resource group", "CreateBlade_Network.displaySubnet": "Subnet", "CreateBlade_Network.displaySubscription": "Subscription", "CreateBlade_Network.displayTargetResourceType": "Target resource type", "CreateBlade_Network.networkIsolationDescription": "Choose the type of network isolation you need for your workspace, from not isolated at all to an entirely separate virtual network managed by Azure Machine Learning.", "CreateBlade_Network.networkIsolationLearnMore": "Learn more about managed network isolation", "CreateBlade_Network.networkIsolationTitle": "Network isolation", "CreateBlade_Network.outboundRulesGridEmptyMessage": "Missing required outbound access rules.", "CreateBlade_Network.outboundRulesNotLoadedMessage": "Configure basic settings in order to create outbound rules.", "CreateBlade_Network.outboundRulesPublicInternetEnabledMessage": "No required outbound rules while the outbound access to the public internet is enabled.", "CreateBlade_Network.overviewDescription": "You can connect to your workspace either publicly or privately using a private endpoint.", "CreateBlade_Network.overviewTitle": "Network connectivity", "CreateBlade_Network.privateEndpointDescription": "Create a private endpoint to allow a private connection to this resource.", "CreateBlade_Network.privateEndpointTitle": "Private endpoint", "CreateBlade_Network.registryOverviewDescription": "You can connect to your registry either publicly or privately using a private endpoint.", "CreateBlade_Network.title": "Networking", "CreateBlade_Network.workspaceInboundAccessTitle": "Workspace Inbound access", "CreateBlade_Network.workspaceOutboundAccessTitle": "Workspace Outbound access", "CreateBlade_Workspace.type": "Workspce type", "CreateBladeHubAIServices.ariaLabel": "Connect AI Services, including OpenAI", "CreateBladeHubAIServices.info": "Provider of Microsoft-maintained base models. Managed in your Azure subscription as a separate resource.", "CreateBladeHubAIServices.label": "Connect AI Services incl. OpenAI", "CreateBladeHubAIServices.skipText": "Skip connecting AI services", "CreateBladeHubConfigDefaultResourceGroup.info": "When a user does not specify a resource group for their project, this resource group will be used by default. The project creator gets granted an Azure RBAC owner role assignment on the project instance", "CreateBladeHubConfigDefaultResourceGroup.label": "Default project resource group", "CrossRegionComputeQuotas_QuotaConfigurationBlade.filterText": "Enter at least 3 characters to search for workspaces...", "CrossRegionComputeQuotas_QuotaConfigurationBlade.noItemsText": "No workspaces found to display.", "CrossRegionComputeQuotas_QuotaConfigurationBlade.quotaConfigNotAvailable": "Quotas cannot be configured due to following error: {0}", "CrossRegionComputeQuotas_QuotaConfigurationBlade.subTitle": "Configure your quota across your subscription here", "CrossRegionComputeQuotas_QuotaConfigurationBlade.title": "Configure quota", "CrossRegionComputeQuotas_QuotaConfigurationBlade.workspacesNotAvailable": "You can only configure quota for workspaces which have been configured before since the list of workspaces of this subscription cannot be reached due to following error: {0}", "CrossRegionComputeQuotas_QuotaUsageBlade.crossLocationUsagesNotAvailable": "Cross location workspace information (whether the workspace owning a compute is in a different location than the selected location) cannot be displayed due to following error but you can still see all the usages: {0}", "CrossRegionComputeQuotas_QuotaUsageBlade.quotaInfoLearnMore": "Learn more", "CrossRegionComputeQuotas_QuotaUsageBlade.quotaInfoText": "Azure Machine Learning quota is preconfigured limits, which specifies the maximum number of cores you can use at any given moment.", "CrossRegionComputeQuotas_QuotaUsageBlade.usagesNotAvailable": "Compute usages cannot be displayed do to following error: {0}", "CrossRegionComputeQuotas_UsageGrid.coresUsageTextFormat": "{0} of {1} cores utilized", "CrossRegionComputeQuotas_UsageGrid.coreUtilizationColumnText": "Cores utilization", "CrossRegionComputeQuotas_UsageGrid.crossLocationEnableText": "Enable cross location compute toggle", "CrossRegionComputeQuotas_UsageGrid.crossLocationWarningCoresFormat": "{0} cores", "CrossRegionComputeQuotas_UsageGrid.crossLocationWarningLine1": "You are using {0} in other locations.", "CrossRegionComputeQuotas_UsageGrid.crossLocationWarningLine2": "{1} to view them.", "CrossRegionComputeQuotas_UsageGrid.familyNameFormat": "Standard {0} Family vCPUs", "CrossRegionComputeQuotas_UsageGrid.totalCoresTextFormat": "{0} out of {1} ({2})", "CrossRegionComputeQuotas_UsageGrid.usageColumnText": "Usage", "CrossRegionComputeQuotas_UsageGrid.usagePercentageColumnText": "Usage percentage", "CrossRegionComputeQuotas_UsageGrid.vmFamiliesColumnText": "VM Families", "CrossRegionComputeQuotas_UsageGrid.workspaceCrossLocationDisplayText": "{0} ({1}, {2})", "CrossRegionComputeQuotas_UsageGrid.workspaceDisplayText": "{0} ({1})", "CrossRegionComputeQuotas_UsageGrid.workspacesColumnText": "Workspaces", "CrossRegionComputeQuotas.configureQuotaButtonText": "Configure quota", "CrossRegionComputeQuotas.dedicatedCoresSectionLabel": "Dedicated cores usage", "CrossRegionComputeQuotas.loadingText": "Loading...", "CrossRegionComputeQuotas.lowPriorityCoresSectionLabel": "Low priority cores usage", "CrossRegionComputeQuotas.lowPriorityUsageInfoText": "Please note that the number of low-priority cores per subscription is single value accross VM families.", "CrossRegionComputeQuotas.quotaUpdateFailed": "Quota update failed", "CrossRegionComputeQuotas.quotaUpdateSucceeded": "<PERSON><PERSON><PERSON> successfully updated", "CrossRegionComputeQuotas.requestBladeFamilyNameFormat": "{0} Series", "CrossRegionComputeQuotas.requestQuotaButtonText": "Request quota increase", "CrossRegionComputeQuotas.subscriptionViewText": "Subscription view", "CrossRegionComputeQuotas.toggleText": "Show workspaces across all locations (cross-location compute)", "CrossRegionComputeQuotas.totalClustersLabel": "Cluster quota:", "CrossRegionComputeQuotas.totalClustersTextFormat": "{0} clusters and CIs used | {1} remaining", "CrossRegionComputeQuotas.totalCoresTextFormat": "{0} cores used | {1} cores remaining", "CrossRegionComputeQuotas.totalDedicatedCoresLabel": "Dedicated quota:", "CrossRegionComputeQuotas.totalLowPriorityCoresLabel": "Low priority quota:", "CrossRegionComputeQuotas.workspaceViewText": "Workspace view", "DeleteHubBlade_AssociatedResourcesListColumn.name": "Name", "DeleteHubBlade_AssociatedResourcesListColumn.resourceGroup": "Resource Group", "DeleteHubBlade_AssociatedResourcesListColumn.type": "Type", "DeleteHubBlade_Buttons.cancel": "Cancel", "DeleteHubBlade_Buttons.delete": "Delete", "DeleteHubBlade_ConfirmDeleteSection_ValidationErrorMessage.emptyString": "The value must not be empty", "DeleteHubBlade_ConfirmDeleteSection_ValidationErrorMessage.nameMismatch": "Name does not match", "DeleteHubBlade_ConfirmDeleteSection.label": "Confirm delete", "DeleteHubBlade_ConfirmDeleteSection.placeholder": "Type the resource name", "DeleteHubBlade_Description.deployment": "deployed model(s)", "DeleteHubBlade_Description.hubName": "Hub to be deleted:", "DeleteHubBlade_Description.project": "project(s)", "DeleteHubBlade_Description.section1": "Your hub with {0} and {1} will be permanently deleted.", "DeleteHubBlade_Description.section2": "Connected resources may still store hub data and are not automatically deleted. If you delete these resources you may break other existing Azure deployments.", "DeleteHubBlade_Description.section3": "Choose connected resources you'd like to additionally delete:", "DeleteHubBlade_ProjectListColumn.deployment": "Deployment", "DeleteHubBlade_ProjectListColumn.project": "Project", "DeleteHubBlade_RBAC.message": "You are missing Azure RBAC delete permission on one or more connected resources. Delete these resources later from Azure portal.", "DeleteHubBlade_ResourceType.aiServices": "AI Services", "DeleteHubBlade_ResourceType.applicationInsights": "Application Insights", "DeleteHubBlade_ResourceType.cognitiveService": "Cognitive Service", "DeleteHubBlade_ResourceType.computerVision": "Computer Vision", "DeleteHubBlade_ResourceType.containerRegistry": "Container Registry", "DeleteHubBlade_ResourceType.contentSafety": "Content Safety", "DeleteHubBlade_ResourceType.endpoint": "AI Endpoint", "DeleteHubBlade_ResourceType.formRecognizer": "Document Intelligence", "DeleteHubBlade_ResourceType.hub": "AI Hub", "DeleteHubBlade_ResourceType.keyVault": "<PERSON>", "DeleteHubBlade_ResourceType.openAI": "Open AI", "DeleteHubBlade_ResourceType.project": "AI Project", "DeleteHubBlade_ResourceType.searchService": "Search Service", "DeleteHubBlade_ResourceType.speechServices": "Speech Services", "DeleteHubBlade_ResourceType.storageAccount": "Storage Account", "DeleteHubBlade_ResourceType.textAnalytics": "Language", "DeleteHubBlade_ResourceType.textTranslation": "Translator", "DeleteHubBlade.title": "Delete resource", "DeleteHubNotification_Cancel.title": "Resource deletion canceled", "DeleteHubNotification_Error.description": "An error occurred while deleting hub '{workspaceName}' and its associated resources.", "DeleteHubNotification_Error.title": "Resource deletion error", "DeleteHubNotification_InProgress.description": "Hub '{workspaceName}' and its associated resources deletion in progress...", "DeleteHubNotification_InProgress.title": "Deleting resource...", "DeleteHubNotification_Success.description": "Hub '{workspaceName}' and its associated resources were deleted successfully.", "DeleteHubNotification_Success.title": "Successfully deleted", "DeleteHubStatusBlade_ResourceStatusListColumn.resource": "Resource", "DeleteHubStatusBlade_ResourceStatusListColumn.status": "Status", "DeleteHubStatusBlade_ResourceStatusListColumn.type": "Type", "DeleteHubStatusBlade.title": "Delete resource status", "Description.createAzureMachineLearningHubResource": "Centrally configure Azure AI hubs", "Description.createAzureMachineLearningRegistryResource": "For sharing ML assets across workspaces", "Description.createAzureMachineLearningResource": "For ML projects and teams", "EncryptionSettings_Command.discard": "Discard", "EncryptionSettings_Command.dismissAriaLabel": "Close", "EncryptionSettings_Command.resultError": "Error updating key", "EncryptionSettings_Command.resultLoading": "Updating...", "EncryptionSettings_Command.resultSuccess": "Success", "EncryptionSettings_Command.save": "Save", "EncryptionSettings_Command.seeMore": "See more", "EncryptionSettings_Header1.replacement1": "data encryption", "EncryptionSettings_Header1.replacement2": "customer-managed key encryption", "EncryptionSettings_Header1.template": "Your data is encrypted by default using Microsoft-managed keys. For additional control over your data, you may choose to bring your own key for encryption. To learn more, see {} and {}.", "EncryptionSettings_SelectedKey.keyLabel": "Key", "EncryptionSettings_SelectedKey.title": "Selected key vault and key", "EncryptionSettings_SelectedKey.vaultLabel": "<PERSON><PERSON><PERSON>", "EncryptionSettings_SelectedKey.versionLabel": "Version", "EncryptionSettings_SelectionSection.title": "Encryption selection", "EncryptionSettings_SelectionSection.typeLabel": "Encryption type", "EncryptionSettings_SelectionSection.vaultAndKeyLabel": "Key vault and key", "EncryptionSettings_SelectionSection.vaultAndKeySelect": "Select a key vault and key", "EncryptionSettings.dirtyFormWarning": "You have unsaved changes.", "EncryptionSettings.header2": "After a workspace is deployed, you can rotate the encryption key but not change the encryption type from Microsoft-managed key to Customer-managed key.", "EncryptionSettings.title": "Encryption", "EncryptionTab.title": "Encryption", "Hub_Projects_Toolbar.add": "Add", "Hub_Projects_Toolbar.refresh": "Refresh", "Hub_Projects.loadFailure": "Associated projects of Azure AI hub with ID \"{0}\" could not be loaded.", "Hub_Projects.title": "Projects", "HubConfigDefaultResourceGroup_SummaryTab.label": "Default project resource group", "IdentityTab.title": "Identity", "Keyvault_Dropdown_Hub.info": "A key vault is used to store secrets and other sensitive information that is needed by the AI hub. You may create a new Azure Key Vault resource or select an existing one in your subscription.", "Keyvault_Dropdown_SettingsBlade_Name.infoDescription": "The name must meet the following requirements:", "Keyvault_Dropdown_SettingsBlade_Name.infoItem1": "Unique across all existing key vaults in Azure", "Keyvault_Dropdown_SettingsBlade_Name.infoItem2": "Between 3 and 24 characters long", "Keyvault_Dropdown_SettingsBlade_Name.infoItem3": "Only contain alphanumeric characters and hyphens", "Keyvault_Dropdown_SettingsBlade_Name.infoItem4": "Cannot start with a number", "Keyvault_Dropdown_SettingsBlade.nameInvalidMessage": "Vault name must be between 3-24 alphanumeric characters. The name must begin with a letter, end with a letter or digit, and not contain consecutive hyphens.", "Keyvault_Dropdown_SettingsBlade.nameLabel": "Name", "Keyvault_Dropdown_SettingsBlade.title": "Create new key vault", "Keyvault_Dropdown.createNewAriaLabel": "Create new key vault", "Keyvault_Dropdown.info": "A key vault is used to store secrets and other sensitive information that is needed by the workspace. You may create a new Azure Key Vault resource or select an existing one in your subscription.", "Keyvault_Dropdown.label": "Key vault", "Keyvault_RadioButton_ManagedKeyVault.infoIcon": "Preview: secrets are stored in Microsoft-managed credential store. Secret data lifecycle follows your hub, projects, connections and compute.", "Keyvault_RadioButton.byoKeyVault": "Azure key vault", "Keyvault_RadioButton.label": "Credential store", "Keyvault_RadioButton.managedKeyVault": "Microsoft-managed (preview)", "Keyvault_SummaryTab.label": "Key vault", "Keyword.alert": "<PERSON><PERSON>", "Keyword.audit": "Audit", "Keyword.instance": "Instance", "Keyword.log": "Log", "Keyword.rules": "Rules", "Keyword.scale": "Scale", "Keyword.scaling": "Sc<PERSON>", "ManagedIdentities.menuText": "Identity", "MLAppDeployments_Grid_StatusFilter.all": "All provisioning states", "MLAppDeployments_Grid_StatusFilter.itemFormat": "{0} ({1})", "MLAppDeployments_Grid_StatusFilter.some": "{0} connection states selected", "MLAppDeployments_GridColumns.name": "Deployment name", "MLAppDeployments_GridColumns.status": "Provisioning state", "MLAppDeployments_GridColumns.traffic": "Traffic %", "Networking_PrivateEndpointConnections.tabText": "Private endpoint connections", "Networking_PublicAccess_FirewallValidation.duplicateIpAddresses": "Two identical address ranges have been specified. Address ranges must be unique.", "Networking_PublicAccess_FirewallValidation.exactPrefix": "The subnet prefix must be equal to {0}.", "Networking_PublicAccess_FirewallValidation.formattedPrefix": "The prefix must be between {0} and {1}.", "Networking_PublicAccess_FirewallValidation.invalidCidr": "Specify an IP address or CIDR.", "Networking_PublicAccess_FirewallValidation.invalidCIDRBlockWithSuggestion": "{0} is not a valid CIDR block. Use {1}/{2} instead.", "Networking_PublicAccess_FirewallValidation.invalidIPAddress": "Invalid IP address.", "Networking_PublicAccess_FirewallValidation.leadingZerosIpAddress": "The octet '{0}' in IP address '{1}' contains a leading zero.", "Networking_PublicAccess_FirewallValidation.malformedSubnet": "Malformed address range. Address was {0}.", "Networking_PublicAccess_FirewallValidation.maxPrefix": "The prefix must be smaller than or equal to {0}.", "Networking_PublicAccess_FirewallValidation.minPrefix": "The prefix must be greater than or equal to {0}.", "Networking_PublicAccess_FirewallValidation.nonNullSubnet": "A non-null address range is required.", "Networking_PublicAccess_FirewallValidation.octet": "Octet {0} with value {1} is invalid. It must be between {2} and {3}.", "Networking_PublicAccess_FirewallValidation.publicIpRuleValidation": "IP rules support public IP addresses only.", "Networking_PublicAccess_Toolbar.discardChangesText": "Discard changes", "Networking_PublicAccess_Toolbar.refreshText": "Refresh", "Networking_PublicAccess_Toolbar.saveText": "Save", "Networking_PublicAccess_Toolbar.savingText": "Saving...", "Networking_PublicAccess.addClientIpAddressInfo": "You may not wish to add your client IP address if the network you are using the azure portal from is atypical (home vs. work environment for example).", "Networking_PublicAccess.addClientIpAddressLabel": "Add your client IP address ('{0}')", "Networking_PublicAccess.addressRange": "Address range", "Networking_PublicAccess.addressRangePlaceHolder": "IP address or CIDR", "Networking_PublicAccess.allNetworksInfoText": "All networks, including the internet, can access this resource.", "Networking_PublicAccess.description": "Public network access allows access to this resource through the internet using a public IP address. An application or resource that is granted access with the following network rules still requires proper authorization to access this resource. {0}", "Networking_PublicAccess.disabledInfoText": "No public network can access this resource.", "Networking_PublicAccess.enabledFromSelectedIpInfoText": "Allow access from public IP you specified below.", "Networking_PublicAccess.firewallDescription": "Add IP ranges to allow access from the internet or your on-premises networks.", "Networking_PublicAccess.firewallHeader": "Firewall", "Networking_PublicAccess.firewallLearnMore": "Learn more", "Networking_PublicAccess.learnMoreText": "Learn more", "Networking_PublicAccess.radioAllNetworksText": "All networks", "Networking_PublicAccess.radioDisabledText": "Disabled", "Networking_PublicAccess.radioEnabledFromAllNetworks": "Enabled from all networks", "Networking_PublicAccess.radioEnabledFromSelectedIp": "Enabled from selected IP addresses", "Networking_PublicAccess.radioLabel": "Public network access", "Networking_PublicAccess.tabText": "Public access", "Networking_WorkspaceManagedOutboundAccess.tabText": "Workspace managed outbound access", "OnlineEndpoints_Create_Dependencies.condaDependenciesFileLabel": "Conda dependencies file", "OnlineEndpoints_Create_Dependencies.description": "Customize your model deployment into inference app.", "OnlineEndpoints_Create_Dependencies.entryScriptFileLabel": "Entry script file", "OnlineEndpoints_Create_Dependencies.selectCondaDependenciesFile": "Select a conda dependency file", "OnlineEndpoints_Create_Dependencies.selectEntryScriptFileMessage": "Select an entry script file", "OnlineEndpoints_Create_Dependencies.tabName": "Dependencies", "OnlineEndpoints_Create.introText": "Azure Machine Learning Inference apps enable you to quickly build, deploy and scale enterprise-grade machine learning models running on any platform. Use any open source machine learning framework like TensorFlow, PyTorch, SciKit-Learn, ONNX and more. Use our “no code deployment” to accelerate your productivity or customize your Inference app with your own docker container and/or model scoring code.", "OnlineEndpoints_Create.modelLabel": "Model", "OnlineEndpoints_Create.modelPlaceholder": "Select a model", "OnlineEndpoints_Create.modelVersionLabel": "Model version", "OnlineEndpoints_Create.modelVersionPlaceholder": "Select a model version", "OnlineEndpoints_Create.nameLabel": "Name", "OnlineEndpoints_Create.subTitle": "Create an ML App", "OnlineEndpoints_Create.title": "Machine learning online endpoint", "OnlineEndpoints_Create.workspaceLabel": "Workspace", "OnlineEndpoints_Create.workspacePlaceholder": "Select a workspace", "OutboundAccessRule_SettingsBlade_AppGateway_SubResource.errorMessage": "No private link config is found in resource. Set it up before creating PE outbound rule.", "OutboundAccessRule_SettingsBlade_FqdnCostInfo.managedNetworkIsolationLinkText": "Managed Network Isolation.", "OutboundAccessRule_SettingsBlade_FqdnCostInfo.moreInformationText": "For more information on Azure Firewall, see", "OutboundAccessRule_SettingsBlade_FqdnCostInfo.pricingLinkText": "Pricing.", "OutboundAccessRule_SettingsBlade_FqdnCostInfo.text": "FQDN outbound rules are implemented using Azure Firewall. If you use outbound FQDN rules, charges for Azure Firewall are included in your billing. To learn more about outbound rules, see", "OutboundAccessRule_SettingsBlade_FqdnValidation.applicationGatewayFqdnRequired": "FQDN is required.", "OutboundAccessRule_SettingsBlade_FqdnValidation.emptyLabelMessage": "Domain label should not be empty.", "OutboundAccessRule_SettingsBlade_FqdnValidation.invalidFormatMessage": "Specify a valid FQDN with at least three labels for Application Gateway access.", "OutboundAccessRule_SettingsBlade.amlRegistryPEDependencyRulesWarning": "Dependency outbound rules will be created for all dependency resources under AzureML registry. View these rules under Dependency outbound rules.", "OutboundAccessRule_SettingsBlade.applicationGatewayFqdnInfoBallon": "FQDNs resolve to the private IP of the Application Gateway private endpoint inside the workspace's managed network. FQDNs are editable as needed. Please save after editing the fields.", "OutboundAccessRule_SettingsBlade.destinationTypeBalloonContent": "Type of the outbound destination, FQDN, Private Endpoint, Service Tag.", "OutboundAccessRule_SettingsBlade.destinationTypeLabel": "Destination type", "OutboundAccessRule_SettingsBlade.fqdnDestinationBalloonContent": "Fully Qualified Domain Name to allow for outbound traffic.", "OutboundAccessRule_SettingsBlade.fqdnDestinationLabel": "FQDN destination", "OutboundAccessRule_SettingsBlade.fqdnsLabel": "FQDNs", "OutboundAccessRule_SettingsBlade.note": "Note", "OutboundAccessRule_SettingsBlade.portRangeBalloonContent": "Provide a single port, such as 80; a port range, such as 1024-655535; or a comma-separated list of single ports and/or port ranges, such as 80,1024-655535. This specifies on which ports traffic will be allowed by this rule. Provide an asterisk(*) to allow traffic on any port.", "OutboundAccessRule_SettingsBlade.portRangeLabel": "Port ranges", "OutboundAccessRule_SettingsBlade.protocolBalloonContent": "Network protocol to allow, TCP, UDP, ICMP or Any", "OutboundAccessRule_SettingsBlade.protocolLabel": "Potocol", "OutboundAccessRule_SettingsBlade.resourceGroupBalloonContent": "Resource group containing the target resource for the private endpoint.", "OutboundAccessRule_SettingsBlade.resourceGroupLabel": "Resource group", "OutboundAccessRule_SettingsBlade.resourceNameBalloonContent": "Name of the target resource for the private endpoint.", "OutboundAccessRule_SettingsBlade.resourceNameLabel": "Resource name", "OutboundAccessRule_SettingsBlade.resourceTypeBalloonContent": "Type of the Azure resource that supports Private Link.", "OutboundAccessRule_SettingsBlade.resourceTypeLabel": "Resource type", "OutboundAccessRule_SettingsBlade.ruleNameAlreadyExists": "Rule name already exists", "OutboundAccessRule_SettingsBlade.ruleNameBalloonContent": "Name of the outbound rule that is unique in the workspace.", "OutboundAccessRule_SettingsBlade.ruleNameLabel": "Rule name", "OutboundAccessRule_SettingsBlade.serviceTagBalloonContent": "Predefined identifiers that represent a category of IP addresses.", "OutboundAccessRule_SettingsBlade.serviceTagLabel": "Service tag", "OutboundAccessRule_SettingsBlade.sparkEnabledBalloonContent": "Check to enable an additional private endpoint to be used by jobs running on Spark.", "OutboundAccessRule_SettingsBlade.sparkEnabledLabel": "Spark enabled", "OutboundAccessRule_SettingsBlade.sparkStatusBalloonContent": "Indicates whether managed network is provisioned for Spark jobs.", "OutboundAccessRule_SettingsBlade.sparkStatusLabel": "Spark status", "OutboundAccessRule_SettingsBlade.statusBalloonContent": "Status is read-only, when Active, indicates the managed network is provisioned and ready. When Inactive, indicates it has not provisioned.", "OutboundAccessRule_SettingsBlade.statusLabel": "Status", "OutboundAccessRule_SettingsBlade.subResourceBalloonContent": "Sub resource to connect for the private endpoint.", "OutboundAccessRule_SettingsBlade.subResourceLabel": "Sub resource", "OutboundAccessRule_SettingsBlade.subscriptionBalloonContent": "Subscription containing the target resource for the private endpoint.", "OutboundAccessRule_SettingsBlade.subscriptionLabel": "Subscription", "OutboundAccessRule_SettingsBlade.title": "Workspace outbound rules", "Overview_Banner_Description.label": "The Azure Machine Learning Studio is a web app where you can build, train, test, and deploy ML models. Launch it now to start exploring, or {}", "Overview_Banner_Description.learnMoreText": "learn more about the Azure Machine Learning studio", "Overview_Banner_Registry.launchButtonText": "Launch registry in studio", "Overview_Banner_Registry.title": "Work with your registry in Azure Machine Learning Studio", "Overview_Banner.launchButtonText": "Launch studio", "Overview_Banner.title": "Work with your models in Azure Machine Learning Studio", "Overview_Essentials.edit": "edit", "Overview_Essentials.managedResourceGroup": "Managed resource group", "Overview_Essentials.mlFlowWebURLText": "MLFlow tracking URI", "Overview_Essentials.projectID": "Project ID", "Overview_Essentials.registryWebURLText": "Registry web URL", "Overview_Essentials.studioWebURLText": "Studio web URL", "PrivateEndpoints_Commands_Approve.messagePlural": "Do you want to approve the {0} selected connections?", "PrivateEndpoints_Commands_Approve.messageSingular": "Do you want to approve the connection '{0}'?", "PrivateEndpoints_Commands_Approve.title": "Approve connection", "PrivateEndpoints_Commands_ApproveNotifications_Failure.message": "Failed to approve {0} out of {1} private endpoint connections", "PrivateEndpoints_Commands_ApproveNotifications_Failure.title": "Failed to approve private endpoint connections", "PrivateEndpoints_Commands_ApproveNotifications_InProgress.message": "Approving {0} private endpoint connections", "PrivateEndpoints_Commands_ApproveNotifications_InProgress.title": "Approving private endpoint connections...", "PrivateEndpoints_Commands_ApproveNotifications_Success.message": "Successfully approved {0} private endpoint connections.", "PrivateEndpoints_Commands_ApproveNotifications_Success.title": "Successfully approved private endpoint connections", "PrivateEndpoints_Commands_DeleteNotifications_Failure.message": "Failed to delete {0} out of {1} private endpoint connections", "PrivateEndpoints_Commands_DeleteNotifications_Failure.title": "Failed to delete private endpoint connections", "PrivateEndpoints_Commands_DeleteNotifications_InProgress.message": "Deleting {0} private endpoint connections", "PrivateEndpoints_Commands_DeleteNotifications_InProgress.title": "Deleting private endpoint connections...", "PrivateEndpoints_Commands_DeleteNotifications_Success.message": "Successfully deleted {0} private endpoint connections.", "PrivateEndpoints_Commands_DeleteNotifications_Success.title": "Successfully deleted private endpoint connections", "PrivateEndpoints_Commands_Reject.messagePlural": "Do you want to reject the {0} selected connections?", "PrivateEndpoints_Commands_Reject.messageSingular": "Do you want to reject the connection '{0}'?", "PrivateEndpoints_Commands_Reject.title": "Reject connection", "PrivateEndpoints_Commands_RejectNotifications_Failure.message": "Failed to reject {0} out of {1} private endpoint connections", "PrivateEndpoints_Commands_RejectNotifications_Failure.title": "Failed to reject private endpoint connections", "PrivateEndpoints_Commands_RejectNotifications_InProgress.message": "Rejecting {0} private endpoint connections", "PrivateEndpoints_Commands_RejectNotifications_InProgress.title": "Rejecting private endpoint connections...", "PrivateEndpoints_Commands_RejectNotifications_Success.message": "Successfully rejected {0} private endpoint connections.", "PrivateEndpoints_Commands_RejectNotifications_Success.title": "Successfully rejected private endpoint connections", "PrivateEndpoints_Commands_Remove.messagePlural": "Do you want to delete the {0} selected connections?", "PrivateEndpoints_Commands_Remove.messageSingular": "Do you want to delete the connection '{0}'?", "PrivateEndpoints_Commands_Remove.title": "Delete connection", "PrivateEndpoints_Commands.addPrivateEndpoint": "Private endpoint", "PrivateEndpoints_Commands.approve": "Approve", "PrivateEndpoints_Commands.description": "Description", "PrivateEndpoints_Commands.reject": "Reject", "PrivateEndpoints_Commands.remove": "Remove", "PrivateEndpoints_Create.validationErrorFormat": "{0}: {1}", "PrivateEndpoints_Grid_StatusFilter.all": "All connection states", "PrivateEndpoints_Grid_StatusFilter.itemFormat": "{0} ({1})", "PrivateEndpoints_Grid_StatusFilter.some": "{0} connection states selected", "PrivateEndpoints_GridColumns.description": "Description", "PrivateEndpoints_GridColumns.name": "Connection name", "PrivateEndpoints_GridColumns.privateEndpoint": "Private endpoint", "PrivateEndpoints_GridColumns.status": "Connection state", "PrivateEndpoints_Status.approved": "Approved", "PrivateEndpoints_Status.disconnected": "Disconnected", "PrivateEndpoints_Status.pending": "Pending", "PrivateEndpoints_Status.rejected": "Rejected", "PrivateEndpoints_Toolbar.refresh": "Refresh", "PrivateEndpoints.filterByName": "Filter by name...", "PrivateEndpoints.filterByStatus": "Filter by connection state...", "PrivateEndpoints.genericErrorMessage": "There was an error processing your request. Try again in a few moments.", "PrivateEndpoints.label": "Private endpoint connections", "PrivateEndpoints.menuText": "Networking", "Properties.discard": "Discard changes", "Properties.refresh": "Refresh", "Properties.save": "Save", "Properties.saving": "Saving...", "Quota_Link.bladeDescription": "View quota by subscription and region, and request quota directly from the studio.", "Quota_Link.bladeTitle": "Request and view quota in Azure AI Foundry", "Quota_Link.button": "View quota", "RegionsTab.additionRegionLabel": "Additonal regions", "RegionsTab.description": "Select regions in which you currently have or plan to create AzureML workspaces and use assets from this registry. You can choose to add more regions to the registry later.", "RegionsTab.title": "Regions", "RequestQuota.dedicatedSectionLabel": "Dedicated", "RequestQuota.lowPrioritySectionLabel": "Low priority", "RequestQuota.vmTypeDedicated": "Dedicated", "RequestQuota.vmTypeLowPriority": "Low priority", "RequestQuota.vmTypesLabel": "VM Types", "SearchService_Dropdown.standardSupportedFormat": "{0} (SKU: {1}) - Standard SKU is needed at the minimum", "Security.menuText": "Security", "SoftDeletedWorkspace_Blade_AIStudio_ToolBar.header": "AI Foundry resources", "SoftDeletedWorkspace_Blade_Footer_Buttons.cancel": "Cancel", "SoftDeletedWorkspace_Blade_Footer_Buttons.purge": "Permanently delete", "SoftDeletedWorkspace_Blade_Footer_Buttons.recover": "Recover", "SoftDeletedWorkspace_Blade_ToolBar_Buttons.refresh": "Refresh", "SoftDeletedWorkspace_Blade_ToolBar.header": "Machine Learning workspaces", "SoftDeletedWorkspace_Blade.errorMessage": "Error loading soft deleted resources", "SoftDeletedWorkspace_Blade.loadingText": "Loading...", "SoftDeletedWorkspace_Blade.subTitle": "Recover or permanently delete resources", "SoftDeletedWorkspace_Blade.title": "Recently deleted resources", "SoftDeletedWorkspace_DeleteBlade_FormControls_Buttons.cancel": "Cancel", "SoftDeletedWorkspace_DeleteBlade_FormControls_Buttons.delete": "Delete", "SoftDeletedWorkspace_DeleteBlade_FormControls_Confirm.errorMessage": "Name does not match", "SoftDeletedWorkspace_DeleteBlade_FormControls_Confirm.label": "Confirm delete", "SoftDeletedWorkspace_DeleteBlade_FormControls_Confirm.placeholder": "Type the resource name", "SoftDeletedWorkspace_DeleteBlade_FormControls_PermanentDelete.checkbox": "Delete this resource permanently", "SoftDeletedWorkspace_DeleteBlade_FormControls_PermanentDelete.header": "Permanently delete", "SoftDeletedWorkspace_DeleteBlade_Message_AIStudio.text": "Permanently delete this resource \"{0}\"?", "SoftDeletedWorkspace_DeleteBlade_Message.cMKtext": "This resource uses a customer-managed key (CMK) for data encryption. When soft-deleted, dependent resources for storing data will not be deleted and will incur cost until this resource is hard-deleted. {0}", "SoftDeletedWorkspace_DeleteBlade_Message.link": "Learn more", "SoftDeletedWorkspace_DeleteBlade_Message.text": "When you delete this resource, it is temporarily placed in a ‘soft-delete’ state that allows you to recover it. Deletion of your data is postponed until your resource is permanently deleted by you, or until the expiry of the soft-delete data retention period of fourteen (14) days. You can override the soft-delete behavior and permanently delete your resource immediately. {0}", "SoftDeletedWorkspace_DeleteBlade.title": "Delete resource", "SoftDeletedWorkspace_Grid_Columns.deletedDate": "Deleted date", "SoftDeletedWorkspace_Grid_Columns.name": "Name", "SoftDeletedWorkspace_Grid_Columns.purgeDate": "Scheduled permanent deletion date", "SoftDeletedWorkspace_Grid_Columns.resourceGroup": "Resource group", "SoftDeletedWorkspace_Grid.ariaLabel": "Deleted resources grid", "SoftDeletedWorkspace_Grid.noWorkspacesFound": "No resources found to display", "SoftDeletedWorkspace_OverviewBlade.deleteMessage": "The soft delete feature has been enabled on this resource. After you soft delete,  this resource data remains available. It will get purged after the retention period. You may purge it sooner, or recover the resource", "SoftDeletedWorkspace_WorkspacesBlade_Commands_Generic_Error.message": "There was an error processing your request. Try again in a few moments", "SoftDeletedWorkspace_WorkspacesBlade_Commands_PurgeNotifications_Failure.message": "Failed to permanently delete {0} out of {1} resources", "SoftDeletedWorkspace_WorkspacesBlade_Commands_PurgeNotifications_Failure.title": "Failed to delete resources", "SoftDeletedWorkspace_WorkspacesBlade_Commands_PurgeNotifications_InProgress.message": "Permanently deleting {0} resources", "SoftDeletedWorkspace_WorkspacesBlade_Commands_PurgeNotifications_InProgress.title": "Deleting resources ...", "SoftDeletedWorkspace_WorkspacesBlade_Commands_PurgeNotifications_Success.message": "Successfully deleted {0} resource(s) permanently", "SoftDeletedWorkspace_WorkspacesBlade_Commands_PurgeNotifications_Success.title": "Successfully deleted resources", "SoftDeletedWorkspace_WorkspacesBlade_Commands_RecoverNotifications_Failure.message": "Failed to recover {0} out of {1} resources", "SoftDeletedWorkspace_WorkspacesBlade_Commands_RecoverNotifications_Failure.title": "Failed to recover resources", "SoftDeletedWorkspace_WorkspacesBlade_Commands_RecoverNotifications_InProgress.message": "Recovering {0} resource(s)", "SoftDeletedWorkspace_WorkspacesBlade_Commands_RecoverNotifications_InProgress.title": "Recovering resources ...", "SoftDeletedWorkspace_WorkspacesBlade_Commands_RecoverNotifications_Success.message": "Successfully recovered {0} resource(s)", "SoftDeletedWorkspace_WorkspacesBlade_Commands_RecoverNotifications_Success.title": "Successfully recovered resources", "SoftDeletedWorkspace_WorkspacesBlade_Commands_ValidateResources_AppInsights_Failure.message": "Recovery error: the associated Application Insights resource \"{0}\" could not be found. It may have been deleted. Recover or recreate the application insights resource under its former name \"{0}\". You may restore your application insights data by recovering the associated log analytics workspace first.\"", "SoftDeletedWorkspace_WorkspacesBlade_Commands_ValidateResources_ContainerRegistry_Failure.message": "Recovery error: the associated Container Registry resource \"{0}\" could not be found. A soft-deleted cannot be recovered without a Container Registry as a dependency.", "SoftDeletedWorkspace_WorkspacesBlade_Commands_ValidateResources_Failure.title": "Failed to validate resource {0}", "SoftDeletedWorkspace_WorkspacesBlade_Commands_ValidateResources_InProgress.message": "Validating {0} resources for {1}", "SoftDeletedWorkspace_WorkspacesBlade_Commands_ValidateResources_InProgress.title": "Validating resources ...", "SoftDeletedWorkspace_WorkspacesBlade_Commands_ValidateResources_KeyVault_Failure.message": "Recovery error: the associated Azure Key Vault resource \"{0}\" could not be found. A soft-deleted resource cannot be recovered without its previously attached Key Vault. The required Azure Key Vault may still be recoverable, see `Manage deleted vaults`.", "SoftDeletedWorkspace_WorkspacesBlade_Commands_ValidateResources_StorageAccount_Failure.message": "Recovery error: the associated Storage Account resource \"{0}\" could not be found. If the storage account was previously soft-deleted, recover it first before recovering this resource.", "SoftDeletedWorkspace_WorkspacesBlade_Commands_ValidateResources_Success.message": "Successfully validated resources for {0}", "SoftDeletedWorkspace_WorkspacesBlade_Commands_ValidateResources_Success.title": "Successfully validated", "StorageAccount_Dropdown_Hub.info": "A storage account is used as the default datastore for the AI hub. You may create a new Azure Storage resource or select an existing one in your subscription.", "StorageAccount_Dropdown_SettingsBlade_Name.infoDescription": "The name must meet the following requirements:", "StorageAccount_Dropdown_SettingsBlade_Name.infoItem1": "Unique across all existing storage account names in Azure", "StorageAccount_Dropdown_SettingsBlade_Name.infoItem2": "Between 3 and 24 characters long", "StorageAccount_Dropdown_SettingsBlade_Name.infoItem3": "Only contain lowercase letters and numbers", "StorageAccount_Dropdown_SettingsBlade_Performance.info": "Standard storage accounts are backed by magnetic drives and provide the lowest cost per GB. Premium storage accounts are backed by solid state drives and offer consistent, low-latency performance.", "StorageAccount_Dropdown_SettingsBlade_Replication.info": "The data in your storage account is always replicated to ensure durability and high availability. Choose a replication strategy that best matches you requirements.", "StorageAccount_Dropdown_SettingsBlade_Replication.infoLearnMore": "Learn more", "StorageAccount_Dropdown_SettingsBlade.hnsCheckLabel": "Enable hierarchical namespace (preview)", "StorageAccount_Dropdown_SettingsBlade.hnsDescriptionText": "The Data Lake Storage Gen2 hierarchical namespace accelerates big data analytics workloads, enables faster and more reliable file operations, and enables file-level access control lists (ACLs). {0}", "StorageAccount_Dropdown_SettingsBlade.hnsDescriptionTitle": "Data Lake Storage Gen2 (preview)", "StorageAccount_Dropdown_SettingsBlade.hnsNotSupportedFormat": "{0} - Hierarchical namespace is not supported", "StorageAccount_Dropdown_SettingsBlade.learnMoreText": "Learn more", "StorageAccount_Dropdown_SettingsBlade.nameInvalidMessage": "Storage name must be between 3 and 24 characters and may only contain lowercase letters and numbers.", "StorageAccount_Dropdown_SettingsBlade.nameLabel": "Name", "StorageAccount_Dropdown_SettingsBlade.performanceLabel": "Performance", "StorageAccount_Dropdown_SettingsBlade.performancePremium": "Premium", "StorageAccount_Dropdown_SettingsBlade.performanceStandard": "Standard", "StorageAccount_Dropdown_SettingsBlade.premiumNotSupportedFormat": "{0} - Premium storage account is not supported", "StorageAccount_Dropdown_SettingsBlade.replicationGRS": "Geo-redundant storage (GRS)", "StorageAccount_Dropdown_SettingsBlade.replicationGZRS": "Geo-zone-redundant storage (GZRS)", "StorageAccount_Dropdown_SettingsBlade.replicationLabel": "Replication", "StorageAccount_Dropdown_SettingsBlade.replicationLRS": "Locally-redundant storage (LRS)", "StorageAccount_Dropdown_SettingsBlade.replicationRAGRS": "Read-access geo-redundant storage (RA-GRS)", "StorageAccount_Dropdown_SettingsBlade.replicationRAGZRS": "Read-access geo-zone-redundant storage (RA-GZRS)", "StorageAccount_Dropdown_SettingsBlade.replicationZRS": "Zone-redundant storage (ZRS)", "StorageAccount_Dropdown_SettingsBlade.title": "Create new storage account", "StorageAccount_Dropdown.createNewAriaLabel": "Create new storage account", "StorageAccount_Dropdown.info": "A storage account is used as the default datastore for the workspace. You may create a new Azure Storage resource or select an existing one in your subscription.", "StorageAccount_Dropdown.label": "Storage account", "StorageAccount_SummaryTab.label": "Storage account", "StorageTab.description": "As users work in Azure AI Foundry, uploaded data, stored credentials and generated artifacts like logs are stored.", "StorageTab.header": "Configure how your data is stored", "StorageTab.logsAndDockerHeader": "Logs and docker images", "StorageTab.title": "Storage", "SummaryTab_Advanced_ManagedIdentity.systemAssigned": "System assigned", "SummaryTab_Advanced_ManagedIdentity.typeText": "Identity type", "SummaryTab_Advanced_ManagedIdentity.userAssigned": "User assigned", "SummaryTab_Advanced_ManagedIdentity.userIdentityName": "User identity name", "SummaryTab_Advanced_ManagedIdentity.userIdentityResourceGroup": "User identity resource group", "SummaryTab_Resource.existingFormat": "{0} ({1})", "SummaryTab_Resource.microsoftManaged": "Microsoft-managed", "SummaryTab_Resource.newFormat": "(new) {0}", "SummaryTab_Resource.none": "None", "SummaryTab.createResourceGroupErrorText": "Error creating resource group when creating default log analytics workspace: {0}", "SummaryTab.gettingLogWorkspacesErrorText": "Error getting log workspaces for creating default log analytics workspace: {0}"}