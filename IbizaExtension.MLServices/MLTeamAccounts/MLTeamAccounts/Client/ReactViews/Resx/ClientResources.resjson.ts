/* eslint-disable  @typescript-eslint/member-delimiter-style */
/* eslint-disable  @typescript-eslint/naming-convention */

export enum AccountPart {
    deprecated = "Deprecated",
    deprecatedLongMessage = "The resource is no longer available. Please use Machine Learning Services Workspace Extension.",
    deprecatedShortMessage = "Use Machine Learning Services Extension.",
}

export enum AdvancedTab__Key_KeyVaultChangeControl {
    version = "Version: {version}",
}

export enum AdvancedTab_Key_KeyVaultChangeControl {
    action = "Click to select key",
    key = "Key: {key}",
    keyVault = "Key vault: {keyvault}",
    label = "Key vault",
    purgeProtectionRequired = "When bringing your own encryption key, your Azure Key Vault must have purge protection enabled to protect against accidental loss of data access.",
    required = "Key is required",
    title = "Key vault and key",
    version = "Version: {version}",
}

export enum AdvancedTab_Section_DataEncryption {
    checkboxByoInfo = "Optionally use pre-created resources for storing encrypted workspace data. Using your own resources for encryption, allows for enhanced configuration of these resources in compliance with your organization’s IT and security requirements, but implies additional management actions by you.",
    checkboxByoLabel = "Bring existing resources for storing encrypted data (preview)",
    checkboxLabel = "Enable encryption using a Customer Managed Key",
    cosmosInfo = "Select a pre-created Azure Cosmos DB resource. If you select 'None', Azure ML will create a Cosmos DB resource in a new resource group in your subscription that will be managed by Microsoft. Using your own resources for encryption implies additional management responsibility by you.",
    cosmosLabel = "Cosmos DB",
    customerManaged = "Customer-managed keys",
    header = "Your data is encrypted by default using Microsoft-managed keys. For additional control over your data, you may choose to bring your own key for encryption. {0}",
    infoText = "Azure machine learning service stores metrics and metadata in an Azure Cosmos DB instance where all data is encrypted ​at rest. By default, the data is encrypted with Microsoft-managed keys. You may choose to bring your own (customer-managed) keys. {0}",
    learnMoreText = "Learn more",
    linkText = "Learn more about customer-managed key encryption.",
    microsoftManaged = "Microsoft-managed keys",
    searchInfo = "Select a pre-created Azure Search resource. If you select 'None', Azure ML will create a Search resource in a new resource group in your subscription that will be managed by Microsoft. Using your own resources for encryption implies additional management responsibility by you.",
    searchLabel = "Search",
    storageInfo = "Select a pre-created Azure Storage resource. If you select 'None', Azure ML will create a Storage resource in a new resource group in your subscription that will be managed by Microsoft. Using your own resources for encryption implies additional management responsibility by you.",
    storageLabel = "Storage",
    title = "Data encryption",
    warningMessage = "After workspace creation, you cannot change encryption key type between Microsoft-managed keys  and Customer-managed keys.",
}

export enum AdvancedTab_Section_DataEncryption_ServiceSide {
    infoText = "Azure machine learning service stores metrics and metadata in an Azure Cosmos DB instance where all data is encrypted ​at rest. By default, the data is encrypted with Microsoft-managed keys. You may choose to bring your own (customer-managed) keys. {0}",
    label = "Use service-side encryption",
}

export enum AdvancedTab_Section_DataEncryption_Type {
    label = "Encrypt data using a customer managed key",
}

export enum AdvancedTab_Section_DataImpact {
    header = "If your workspace contains sensitive data, you can specify a high business impact workspace. This will control the amount of data Microsoft collects for diagnostic purposes and enables additional encryption in Microsoft managed environments.​",
    title = "Data impact",
}

export enum AdvancedTab_Section_DataImpact_HBI {
    label = "High business impact workspace",
}

export enum AdvancedTab_Section_ManagedIdentity {
    description = "A managed identity enables Azure resources to authenticate to cloud services without storing credentials in code. Once enabled, all necessary permissions can be granted via Azure role-based access control. A workspace can be given either a system assigned identity or a user assigned identity.",
    disabledWarning = "The managed user assigned identity option is only supported if an existing storage account, key vault, and container registry are used.",
    header = "Managed identity",
    /** {0}: role assignment ARM template */
    permissionWarning = "If you bring your own associated resources, instead of having Azure Machine Learning service create them, you must grant the managed identity roles on those resources. Use the {0} to make the assignments.",
    permissionWarningLinkText = "role assignment ARM template",
    radioGroupTitle = "Identity type",
    systemAssignedText = "System assigned identity",
    userAssignedPickerLabel = "User assigned identity",
    userAssignedPickerRequiredMessage = "User identity is required",
    userAssignedPickerSubscriptionMessage = "Workspace and selected user identity must be in the same subscription.",
    userAssignedPickerText = "Click to select identity",
    userAssignedText = "User assigned identity",
    userIdentityNameLabel = "User identity name",
    userIdentityResourceGroupLabel = "User identity resource group",
}

export enum AdvancedTab_Section_StorageAccountAccess {
    credentialBasedText = "Credential-based access",
    description = "Azure machine learning allows you to choose between credential-based or identity-based access when connecting to the default storage account.",
    header = "Storage account access",
    identityBasedText = "Identity-based access",
    identityBasedWarningLearnMore = "learn more about RBAC settings",
    /** 0: Important, 1: Storage Blob Data Contributor, 2: Storage File Privileged Contributor, 3: individual users */
    identityBasedWarningText = "{0} When using identity-based authentication, \"{1}\" and \"{2}\" roles must be granted to {3} that need access on the storage account. Contact your admin for help or",
    important = "Important",
    individualUsers = "individual users",
    learnMoreText = "Learn more",
    previewLinkText = "(preview)",
    radioGroupTitle = "Storage account access type",
    sharedKeyAccessCheckboxLabel = "Disable shared key access",
    sharedKeyAccessDisableDocumentationLinkText = "Learn more",
    sharedKeyAccessDisableWarning = "Disable shared key access option {0} disables key access at the storage account, any requests to the storage account that are authorized with shared key, including shared access signatures (SAS), will be denied. This option might require additional role configuration to individual users for certain use cases. {1} about disabling shared key access for your workspace's storage account",
}

export enum AIServices_SummaryTab {
    label = "AI Services",
}

export enum AppInsights_Dropdown {
    createNewAriaLabel = "Create new application insights",
    info = "The workspace uses Azure Application Insights to store monitoring information about your deployed models. You may create a new Azure Application Insights resource or select an existing one in your subscription.",
    label = "Application insights",
}

export enum AppInsights_Dropdown_Hub {
    info = "The AI hub uses Azure Application Insights to store monitoring information about your deployed models. You may create a new Azure Application Insights resource or select an existing one in your subscription.",
}

export enum AppInsights_Dropdown_SettingsBlade {
    nameInvalidMessage = "The name must contain between 1 to 255 characters inclusive. The name only allows alphanumeric characters, periods, underscores, hyphens and parenthesis and cannot end in a period.",
    nameLabel = "Name",
    title = "Create new application insights",
}

export enum AppInsights_Dropdown_SettingsBlade_Name {
    infoDescription = "The name must meet the following requirements:",
    /** Item1 of AppInsights_Dropdown_SettingsBlade_Name_infoDescription */
    infoItem1 = "Unique across the resource group",
    /** Item2 of AppInsights_Dropdown_SettingsBlade_Name_infoDescription */
    infoItem2 = "Between 1 and 255 characters long",
    /** Item3 of AppInsights_Dropdown_SettingsBlade_Name_infoDescription */
    infoItem3 = "Only contain alphanumeric characters, periods, underscores, hyphens, and parenthesis",
    /** Item4 of AppInsights_Dropdown_SettingsBlade_Name_infoDescription */
    infoItem4 = "Cannot end with a period",
}

export enum AppInsights_SummaryTab {
    label = "Application insights",
}

export enum AssetType {
    /** PLEASE DO NOT TRANSLATE "Azure Machine Learning" */
    description = "Workspaces are where you manage all the models, assets, and data related to your machine learning projects. Create one now to start using Azure Machine Learning.",
    keywords = "ML, AML, Machine Learning, AI, Artificial Intelligence, Preview, Deep Learning, Analytics, Data, NLP, Natural Language Processing, CNN, Neural Network, Workbench, Train, Notebooks, AutoML, Designer, Computer Vision, Models, Data Science, Classification, Regression, Reinforcement Learning",
}

export enum AssetType_AIStudio {
    description = "Your platform to build generative AI solutions and custom copilots",
    keywords = "AI, Hub, AI Studio, Azure AI Studio, AI Foundry, Azure AI Foundry, AI Hub, AI Project, AIStudio, Artificial Intelligence, Preview, Deep Learning, Analytics, Data, NLP, Natural Language Processing, CNN, Neural Network, Train, Computer Vision, Models, Data Science, Classification, Regression, Reinforcement Learning, LLM, Chatbot",
}

export enum AssetType_AIStudio_Browse_Commands {
    createAzureAI = "Create",
    createAzureAICommandBar = "New Azure AI hub",
    createAzureAIHub = "Hub",
    createAzureAIHubLabel = "Create Hub",
    createAzureAILabel = "Create Azure AI Foundry",
    createAzureAIProject = "Project",
    createAzureAIProjectLabel = "Create Project",
}

export enum AssetType_AIStudio_Browse_Description {
    createAzureAIHub = "Grouping container for projects. Provides security, connectivity, and compute management.",
    createAzureAIProject = "Collaborate, organize, and track work to build AI apps.",
}

export enum AssetType_AIStudio_Hub_AIServices_Settings {
    title = "Create new Azure AI Services",
}

export enum AssetType_AIStudio_Hub_Dropdown {
    createNewAriaLabel = "Create new Hub",
    info = "Grouping container for projects. Provides security, connectivity, and compute management.",
    label = "Hub",
}

export enum AssetType_AIStudio_Hub_Essentials_AIServices {
    label = "AI Services provider",
}

export enum AssetType_AIStudio_Hub_Essentials_DefaultProjectResourceGroup {
    label = "Project resource group (default)",
}

export enum AssetType_AIStudio_Hub_Overview_Banner {
    launchButtonText = "Launch Azure AI Foundry",
    title = "Govern the environment for your team in AI Foundry",
}

export enum AssetType_AIStudio_Hub_Overview_Banner_Description {
    /** {}: learnMoreText */
    label = "Your Azure AI hub provides enterpise-grade security, and a collaborative environment to build AI solutions. Centrally audit usage and cost, and set up connections to your company resources that all projects can use. {}",
    learnMoreText = "learn more about the Azure AI Foundry",
}

export enum AssetType_AIStudio_KeysAndEndpoints {
    endpoint = "Endpoint",
    hideKeys = "Hide keys",
    key1 = "KEY 1",
    key2 = "KEY 2",
    message = "These keys are used to access your Azure AI services API. Do not share your keys. Store them securely- for example, using Azure Key Vault. We also recommend regenerating these keys regularly. Only one key is necessary to make an API call. When regenerating the first key, you can use the second key for continued access to the service.",
    regenerateKeys = "Regenerate keys",
    showKeys = "Show keys",
    title = "Keys and Endpoints",
}

export enum AssetType_AIStudio_KeysAndEndpoints_Command {
    regenKey1 = "Regenerate Key 1",
    regenKey2 = "Regenerate Key 2",
}

export enum AssetType_AIStudio_Names {
    lowerPlural = "Azure AI Foundry",
    lowerSingular = "Azure AI Foundry",
    plural = "Azure AI Foundry",
    singular = "Azure AI Foundry",
}

export enum AssetType_AIStudio_Project_Overview_Banner {
    launchButtonText = "Launch studio",
    title = "Start building in Azure AI Foundry",
}

export enum AssetType_AIStudio_Project_Overview_Banner_Description {
    label = "Jumpstart your AI solution development with pre-built templates and work on your project either in code or in the studio.",
}

export enum AssetType_Cohere_Names {
    lowerPlural = "Cohere",
    lowerSingular = "Cohere",
    plural = "Cohere",
    singular = "Cohere",
}

export enum AssetType_Cohere_Page {
    primaryButton = "Continue to Azure AI Foundry model catalog",
    title = "Continue to Azure AI Foundry to use this offer",
}

export enum AssetType_Cohere_Page_Description {
    body = "Select Continue, and you'll be redirected to the model catalog in Azure AI Foundry where you can finish subscribing and start using this model.",
    intro = "Thank you for your interest.",
}

export enum AssetType_Core42_Names {
    lowerPlural = "Core42",
    lowerSingular = "Core42",
    plural = "Core42",
    singular = "Core42",
}

export enum AssetType_Core42_Page {
    primaryButton = "Continue to Azure AI Foundry model catalog",
    title = "Continue to Azure AI Foundry to use this offer",
}

export enum AssetType_Core42_Page_Description {
    body = "Select Continue, and you'll be redirected to the model catalog in Azure AI Foundry where you can finish subscribing and start using this model.",
    intro = "Thank you for your interest.",
}

export enum AssetType_Llama2_Names {
    lowerPlural = "Llama2",
    lowerSingular = "Llama2",
    plural = "Llama2",
    singular = "Llama2",
}

export enum AssetType_Llama2_Page {
    primaryButton = "Continue to Azure AI Foundry model catalog",
    title = "Continue to Azure AI Foundry to use this offer",
}

export enum AssetType_Llama2_Page_Description {
    body = "Select Continue, and you'll be redirected to the model catalog in Azure AI Foundry where you can finish subscribing and start using this model.",
    intro = "Thank you for your interest.",
}

export enum AssetType_Mistral_Names {
    lowerPlural = "Mistral",
    lowerSingular = "Mistral",
    plural = "Mistral",
    singular = "Mistral",
}

export enum AssetType_Mistral_Page {
    primaryButton = "Continue to Azure AI Foundry model catalog",
    title = "Continue to Azure AI Foundry to use this offer",
}

export enum AssetType_Mistral_Page_Description {
    body = "Select Continue, and you'll be redirected to the model catalog in Azure AI Foundry where you can finish subscribing and start using this model.",
    intro = "Thank you for your interest.",
}

export enum AssetType_ModelProvider_Names {
    lowerPlural = "Model Provider",
    lowerSingular = "Model Provider",
    plural = "Model Provider",
    singular = "Model Provider",
}

export enum AssetType_ModelProvider_Page {
    primaryButton = "Continue to Azure AI Foundry model catalog",
    title = "Continue to Azure AI Foundry to use this offer",
}

export enum AssetType_ModelProvider_Page_Description {
    body = "Select Continue, and you'll be redirected to the model catalog in Azure AI Foundry where you can finish subscribing and start using this model.",
    intro = "Thank you for your interest.",
}

export enum AssetType_Nixtla_Names {
    lowerPlural = "Nixtla",
    lowerSingular = "Nixtla",
    plural = "Nixtla",
    singular = "Nixtla",
}

export enum AssetType_Nixtla_Page {
    primaryButton = "Continue to Azure AI Foundry model catalog",
    title = "Continue to Azure AI Foundry to use this offer",
}

export enum AssetType_Nixtla_Page_Description {
    body = "Select Continue, and you'll be redirected to the model catalog in Azure AI Foundry where you can finish subscribing and start using this model.",
    intro = "Thank you for your interest.",
}

export enum AssetTypeNames_MachineLearningExperimentationAccount {
    singular = "Retired - Machine Learning Experimentation",
}

export enum AssetTypeNames_MachineLearningServices {
    lowerPlural = "workspaces",
    lowerSingular = "workspace",
    /** PLEASE DO NOT TRANSLATE */
    plural = "Azure Machine Learning",
    /** PLEASE DO NOT TRANSLATE "Azure Machine Learning" */
    singular = "Azure Machine Learning workspace",
}

export enum AssetTypeNames_MachineLearningServices_Default {
    lowerPlural = "workspaces",
    lowerSingular = "workspace",
    /** PLEASE DO NOT TRANSLATE "Azure Machine Learning" */
    plural = "Azure Machine Learning workspaces",
    /** PLEASE DO NOT TRANSLATE "Azure Machine Learning" */
    singular = "Azure Machine Learning workspace",
}

export enum AssetTypeNames_MachineLearningServices_FeatureStore {
    lowerPlural = "feature stores",
    lowerSingular = "feature store",
    /** PLEASE DO NOT TRANSLATE "Azure Machine Learning" */
    plural = "Azure Machine Learning feature stores",
    /** PLEASE DO NOT TRANSLATE "Azure Machine Learning" */
    singular = "Azure Machine Learning feature store",
}

export enum AssetTypeNames_MachineLearningServices_Hub {
    lowerPlural = "Azure AI hubs",
    lowerSingular = "Azure AI hub",
    plural = "Azure AI hubs",
    singular = "Azure AI hub",
}

export enum AssetTypeNames_MachineLearningServices_Project {
    lowerPlural = "Azure AI projects",
    lowerSingular = "Azure AI project",
    plural = "Azure AI projects",
    singular = "Azure AI project",
}

export enum AssetTypeNames_MLApp {
    lowerPlural = "machine learning online endpoints",
    lowerSingular = "machine learning online endpoint",
    plural = "Machine learning online endpoints",
    singular = "Machine learning online endpoint",
}

export enum AssetTypeNames_MLAppDeployment {
    lowerPlural = "machine learning online deployments",
    lowerSingular = "machine learning online deployment",
    plural = "Machine learning online deployments",
    singular = "Machine learning online deployment",
}

export enum AssetTypeNames_MLRegistry {
    lowerPlural = "azure machine learning registries",
    lowerSingular = "azure machine learning registry",
    plural = "Azure Machine Learning registries",
    singular = "Azure Machine Learning registry",
}

export enum AssociatedResource_AppInsights_ChangeBlade {
    title = "Select an application insights",
}

export enum AssociatedResource_AppInsights_Properties {
    changeText = "Change application insights",
}

export enum AssociatedResource_AppInsights_ReactView_ChangeAppInsights {
    appInsightsLabel = "Application insights",
    appInsightsUpdatingSpinner = "Application insights updating...",
    error = "An error occurred while updating the application insights for this resource.",
    errorTitle = "Error updating application insights",
    footerCancel = "Discard",
    footerSave = "Save",
    notFoundError = "The resource being updated could not be found.",
    unauthorizedError = "You do not have permission to update the application insights for this resource.",
}

export enum AssociatedResource_AppInsights_ReactView_Dropdown {
    emptyMessage = "No application insights resources found",
    errorLoadingMessage = "Error loading application insights",
    noMatchMessage = "No results matching {0}",
    placeholder = "Select an application insights",
}

export enum AssociatedResource_ContainerRegistry_ChangeBlade {
    title = "Select a container registry",
}

export enum AssociatedResource_ContainerRegistry_Properties {
    changeText = "Change container registry",
}

export enum AssociatedResource_ContainerRegistry_ReactView_ChangeContainerRegistry {
    containerRegistryLabel = "Container registry",
    containerRegistryUpdatingSpinner = "Container registry updating...",
    error = "An error occurred while updating the container registry for this resource.",
    errorTitle = "Error updating container registry",
    footerCancel = "Discard",
    footerSave = "Save",
    notFoundError = "The resource being updated could not be found.",
    unauthorizedError = "You do not have permission to update the container registry for this resource.",
}

export enum AssociatedResource_ContainerRegistry_ReactView_Dropdown {
    emptyMessage = "No container registry resources found",
    errorLoadingMessage = "Error loading container registry",
    noMatchMessage = "No results matching {0}",
    placeholder = "Select a container registry",
}

export enum AssociatedResource_Dropdown {
    createNewLinkText = "Create new",
    filterPlaceholder = "Select existing...",
    /** 0: name of a resource */
    newResourceText = "(new) {0}",
    none = "None",
}

export enum AssociatedResource_Dropdown_SettingsBlade {
    discardButtonText = "Discard",
    saveButtonText = "Save",
}

export enum BasicsTab {
    aIServiceSectionHeader = "Azure AI services base models",
    hubSectionContent = "Projects are grouped by a hub, which provides security configurations, pre-configured connectivity with other Azure resources, compute, storage, and quota.",
    hubSectionHeader = "Share security, connectivity, compute",
    organization = "Organization",
    registryDetails = "Registry details",
    workspaceDetails = "Workspace details",
    workspaceHubDetails = "Resource details",
}

export enum BasicsTab_HubName {
    balloonContent = "Name of the AI hub",
}

export enum BasicsTab_InfoText {
    additionalResourceInfoText = "For your convenience, these resources are added automatically to the workspace, if regionally available:",
    azureApplicationInsights = "Azure Application Insights",
    azureKeyVault = "Azure Key Vault",
    azureStorage = "Azure Storage",
}

export enum BasicsTab_ProjectName {
    balloonContent = "Name of the AI project",
}

export enum BasicsTab_Region {
    balloonContent = "Compute targets can only be created in the same region as the workspace. Ensure the selected region has the virtual machine series needed for your workspace compute targets.",
    learnMoreComputeTargets = "Learn more about compute targets",
    viewAvailableVirtualMachines = "View available virtual machines series by region",
}

export enum BasicsTab_RegistryName {
    balloonContent = "The registry name must be unique within your resource group.",
}

export enum BasicsTab_ResourceGroup {
    balloonContent = "A resource group is a collection of resources that share the same life cycle, permissions, and policies.",
}

export enum BasicsTab_Subscription {
    balloonContent = "All resources in an Azure subscription are billed together.",
}

export enum BasicsTab_WarningText {
    workspaceErrorMessageText = "The selected subscription doesn’t have permissions to register the resource provider.",
}

export enum BasicsTab_WorkspaceEdition {
    basic = "Basic",
    enterprise = "Enterprise",
    viewFullPricingDetails = "View full pricing details",
}

export enum BasicsTab_WorkspaceName {
    balloonContent = "The workspace name must be unique within your resource group.",
}

export enum Browse {
    addMachineLearningLabel = "New workspace",
    addMachineLearningRegistryLabel = "New registry",
}

export enum ClientResources {
    additionalResourceInfo = "For your convenience, these resources are added automatically to the workspace, if regionally available: <a href={0} target=\"_blank\">Azure storage</a>, <a href={1} target=\"_blank\">Azure Application Insights</a> and <a href={2} target=\"_blank\">Azure Key Vault</a>.",
    automationLink = "Download a template for automation",
    basics = "Basics",
    basicsBladeDetailsIntro = "Every workspace must be assigned to an Azure subscription, which is where billing happens. You use resource groups like folders to organize and manage resources, including the workspace you're about to create.",
    basicsBladeDetailsIntroLearnMore = "Learn more about Azure resource groups",
    basicsBladeHubDetailsIntro = "Select the subscription to manage deployed resources and costs. Use resource groups like folders to organize and manage all your resources. An AI hub is a collaboration environment for a team to share project work, model endpoints, compute, (data) connections, security settings, govern usage.",
    basicsBladeInstanceIntro = "Configure your basic workspace settings like its storage connection, authentication, container, and more.",
    basicsBladeInstanceIntroLearnMore = "Learn more",
    basicsBladeProjectDetailsIntro = "Select a subscription and resource group to organize this and other resources, inherit access, and attribute cost.",
    basicsBladeRegistryInstanceIntro = "Configure your basic registry settings like its name and description.",
    basicsRegistryBladeDetailsIntro = "Every registry must be assigned to an Azure subscription, which is where billing happens. You use resource groups like folders to organize and manage resources, including the registry you're about to create.",
    basicsTabTitle = "Basics",
    buttonCreate = "Create",
    /** 0: step name */
    buttonNext = "Next : {0}",
    buttonNextPlaceholder = "Next >",
    buttonPrevious = "< Previous",
    buttonReviewCreate = "Review + create",
    columnCreationTime = "Created on",
    columnMachineLearningWorkspaceId = "Workspace ID",
    commandCancel = "Cancel",
    commandCreateProject = "Create project",
    commandDelete = "Delete",
    commandDownloadConfig = "Download config.json",
    commandDownloadConfigTooltip = "Use this file to load the workspace configuration in your Azure ML SDK notebook or Python script",
    confirmationMachineLearningDelete = "Are you sure you want to delete Workspace {0}?",
    confirmationMachineLearningRegistryDelete = "Are you sure you want to delete Registry {0}?",
    createBladeCreateButtonName = "Create",
    createBladeDescriptionLabel = "Description",
    createBladeFriendlyNameInfo = "Display name of the AI hub that will be displayed in AI Foundry",
    createBladeFriendlyNameLabel = "Friendly name",
    createBladeHubNameLabel = "Hub",
    createBladeHubSubtitle = "Create an Azure AI hub resource",
    createBladeHubTitle = "Azure AI hub",
    createBladeLocationLabel = "Location",
    createBladeMainTabTitle = "Main",
    createBladeNextStepsMLFlowAzureMLDescription = "How to use MLflow with Azure ML",
    createBladeProjectSubtitle = "Organize and track work, collaborate with others and upload data. Access your work in Azure AI Foundry or Azure Machine Learning Studio.",
    createBladeProjectTitle = "Azure AI project",
    createBladeRegistryDescriptionKey = "description",
    createBladeRegistryDescriptionLabel = "Description",
    createBladeRegistryNameLabel = "Name",
    createBladeRegistrySubtitle = "Create a machine learning registry",
    createBladeResourceGroupLabel = "Resource Group",
    createBladeReviewCreateButtonName = "Review + Create",
    createBladeReviewTabTitle = "Review",
    createBladeSubscriptionIdLabel = "Subscription Id",
    createBladeSubscriptionLabel = "Subscription",
    createBladeSubtitle = "Create a machine learning workspace",
    createBladeTagTabTitle = "Tags",
    createBladeTemplateErrorText = "Template Error",
    /** PLEASE DO NOT TRANSLATE */
    createBladeTitle = "Azure Machine Learning",
    createBladeWorkspaceCreationProgressNotification = "Workspace creation in progress",
    createBladeWorkspaceDeploymentErrorText = "Workspace deployment Error",
    createBladeWorkspaceDetails = "Workspace details",
    createBladeWorkspaceNameLabel = "Name",
    createLocationLabelDefault = "Location",
    createLocationLabelPrimaryRegion = "Primary Region",
    createLocationLabelRegion = "Region",
    createResourceGroup = "Resource group",
    createResourceGroupCreateNewPlaceholder = "",
    createResourceGroupTitle = "Resource group",
    createSubscriptionLabel = "Subscription",
    createTagsTabIntro = "Tags are name/value pairs that enable you to categorize resources and view consolidated billing by applying the same tag to multiple resources and resource groups.",
    createTagsTabLearnMoreText = "Learn more about tags",
    createTagsTabTitle = "Tags",
    createTagsTabUpdateNotice = "Note that if you create tags and then change resource settings on other tabs, your tags will be automatically updated.",
    createTemplateValidationError = "Validation failed. Click here to view details.",
    createTemplateValidationInProgress = "Running final validation...",
    createTemplateValidationSuccess = "Validation passed",
    deploymentCapacityMetric = "Deployment Capacity",
    deploymentCpuUtilizationMetric = "Cpu Utilization",
    deploymentDiskUtilizationMetric = "Disk Utilization",
    deploymentEnvironmentId = "Environment",
    deploymentKind = "Deployment Type",
    deploymentMemoryUtilizationMetric = "Memory Utilization",
    deploymentModelIdLabel = "Model Id",
    deploymentName = "Deployment Name",
    deploymentProvisioningStateLabel = "Provisioning State",
    detailsLabel = "Resource details",
    downloadTemplateLinkText = "Download a template for automation",
    endpointAuthModeLabel = "Auth Mode",
    endpointId = "Endpoint Id",
    endpointProvisioningStateLabel = "Provisioning State",
    endpointRequestLatencyMetric = "Request Latency",
    endpointRequestsPerMinuteMetric = "Request Per Minute",
    endpointScoringUriLabel = "Scoring Uri",
    endpointSwaggerLabel = "Swagger Uri",
    errorJsonParsingException = "An unexpected parsing error occurred.",
    instanceLabel = "Instance details",
    labelAutoMLMenuItem = "Automated machine learning",
    labelCommandButtonRefresh = "Refresh",
    labelComputeMenuItem = "Compute",
    labelDataLabeling = "Data Labeling",
    labelDataMenuItem = "Data (Preview)",
    labelDeployments = "Deployments",
    labelDeploymentsMenuItem = "Deployments",
    labelImagesMenuItem = "Images",
    labelInsights = "Application Insights",
    labelKeyVault = "Key Vault",
    labelMlFlowUri = "MLflow tracking URI",
    labelMLStudioLauncher = "Visual interface",
    labelModelsMenuItem = "Models",
    labelPipelinesMenuItem = "Pipelines",
    labelProjectHubResource = "Azure AI hub",
    labelProjectsMenuItem = "Experiments",
    labelProperties = "Properties",
    labelProvisioningState = "Provisioning State",
    labelQuotaUsage = "Usage + quotas",
    labelRegistry = "Container Registry",
    labelRequestQuota = "Request Quota",
    labelResource = "Resource",
    labelStorage = "Storage",
    labelTasksMenuItem = "Activities",
    labelWorkstationsMenuItem = "Notebook VMs",
    learnMore = "Learn more",
    location = "Location",
    machineLearningCompute = "Managed Compute",
    managedKeyVault = "Microsoft-managed",
    mlStudioCapability1 = "Drag-n-Drop to build machine learning models",
    mlStudioCapability2 = "No limit to data size or compute capacity for model training",
    mlStudioCapability3 = "Intrinsic and powerful Python support",
    mlStudioCapability4 = "One click to deploy your web service",
    mlStudioCapability5 = "Rich and fast-growing modules support",
    mlStudioLaunchLabel = "Launch visual interface",
    mlStudioSubtitle = "What's possible with visual interface",
    mlStudioTitle = "Visual interface (preview)",
    multipleInvalidTabErrorMessage = "Validation failed for the following tabs: {0}. Required information is missing or not valid.",
    newResource = "(new) {0}",
    newResourceCapitalized = "(New) {0}",
    /** 0 = the name of the resource */
    newResourceFormatCaps = "(New) {0}",
    noContent = "No content",
    none = "None",
    onlineEndpointName = "Machine learning online endpoint",
    onlineEndpointWorkspaceName = "Workspace",
    overview = "Overview",
    overviewKeywords = "Summary Home",
    progressMachineLearningDeleteError = "The resource could not be deleted.",
    progressMachineLearningRegistryDeleteError = "The Registry could not be deleted.",
    progressMachineLearningRegistryDeleteExecuting = "Deleting the Registry",
    progressMachineLearningServicesDeleteExecuting = "Deleting resource",
    progressNotificationMachineLearningRegistryDeleteError = "An error occurred while deleting the Registry name '{registryName}'.{lineBreak}Error details:{lineBreak}{errorMessage}.",
    progressNotificationMachineLearningRegistryDeleteErrorTitle = "Registry deletion error",
    progressNotificationMachineLearningRegistryDeleteExecuting = "Registry '{registryName}' deletion in progress...",
    progressNotificationMachineLearningRegistryDeleteExecutingTitle = "Deleting Registry...",
    progressNotificationMachineLearningRegistryDeleteSuccess = "Registry '{registryName}' was deleted successfully.",
    progressNotificationMachineLearningRegistryDeleteSuccessTitle = "Registry deleted",
    progressNotificationMachineLearningServicesDeleteCancelTitle = "Resource deletion canceled",
    progressNotificationMachineLearningServicesDeleteError = "An error occurred while deleting the Workspace name '{workspaceName}'.{lineBreak}Error details:{lineBreak}{errorMessage}.",
    progressNotificationMachineLearningServicesDeleteErrorTitle = "Resource deletion error",
    progressNotificationMachineLearningServicesDeleteExecuting = "Resource '{workspaceName}' deletion in progress...",
    progressNotificationMachineLearningServicesDeleteExecutingTitle = "Deleting resource...",
    progressNotificationMachineLearningServicesDeleteSuccess = "Resource '{workspaceName}' was deleted successfully.",
    progressNotificationMachineLearningServicesDeleteSuccessTitle = "Succesfully deleted",
    propertiesBladeApplicationInsightsLabel = "Application Insights",
    propertiesBladeContainerRegistryIdLabel = "Container Registry ID",
    propertiesBladeCreationDateLabel = "Created on",
    propertiesBladeKeyVaultIdLabel = "Key Vault ID",
    propertiesBladeLocationLabel = "Location",
    propertiesBladeResourceGroupLabel = "Resource Group",
    propertiesBladeResourceIdLabel = "Resource ID",
    propertiesBladeStorageAccountIdLabel = "Storage account ID",
    propertiesBladeSubscriptionIdLabel = "Subscription ID",
    propertiesBladeSubscriptionNameLabel = "Subscription Name",
    propertiesBladeWorkspaceHubIdLabel = "AI hub ID",
    propertiesBladeWorkspaceIdLabel = "Machine Learning workspace ID",
    quickLinkUnderOverviewBladeAriaLabel = "Quick link under Overview blade.",
    quotaBladeTitle = "Usage + quotas",
    quotaNote = "Note:",
    quotaProviderNotRegisteredErrorMsg = "Your subscription {0} is not registered with the '{1}' resource provider. Please create a 'Machine Learning service' workspace to auto-register and retry submitting the support request.",
    quotaProviderRegisteringErrorMsg = "Registering your subscription {0} with the '{1}' resource provider.",
    quotaQuotaSettingTabHeader = "Configure quotas",
    quotaRequestCurrentLimit = "Current vCPU limit",
    quotaRequestDocumentationInfoBox = "Click to learn more about Compute (cores/vCPUs) quota increase requests.",
    quotaRequestHeader = "Please enter the limit for any resource(s) you are requesting:",
    quotaRequestLogOutputMessageDetail = "{0} - current value: {1} / requested value: {2}",
    quotaRequestNewLimit = "New vCPU limit",
    quotaRequestNotFound = "No quota resources found for given location",
    quotaRequestResourceName = "Resource name",
    quotaRequestSubmit = "Save and continue",
    quotaRequestTitle = "Quota details",
    quotaRequestVMFamily = "VM series",
    quotaSubscriptionViewClusterHelptext = "For a cluster we show your currently allocated cores and maximum cores it can scale to.",
    quotaSubscriptionViewResourceNameHelptext = "Expand each VM family to view your quota allocation and resource usage by workspace and further to view your clusters and instances.",
    quotaSubscriptionViewTabHeader = "Subscription view",
    quotaSubscriptionViewUsageHelptext = "For an instance it continues to use quota even in Stopped state so you can restart it at any time.",
    quotaTableHeaderDedicated = "Dedicated cores usage",
    quotaTableHeaderLowPriority = "Low priority cores usage",
    quotaTableHeaderQuota = "Resource name",
    quotaTableNoData = "No data to display",
    quotaTableServerError = "The server encountered an error processing current request. Please refresh the table again.",
    quotaTableTotalSubscriptionQuota = "Total subscription quota",
    quotaWorkspaceQuotaExceedSubscriptionLimit = "Workspace level quota cannot exceed the subscription level quota limit.",
    quotaWorkspaceQuotaInsufficientPermissions = "You are not authorized to set quota at the workspace level. Please reach out to your subscription admin to help allocate resources between workspaces.",
    quotaWorkspaceQuotaInvalidVMFamilyName = "Please specify a VM family that is supported in the $region region and you have subscription level quota for.",
    quotaWorkspaceQuotaLessThanMinimumClusterCores = "Workspace level quota cannot be less than the cores required to support the minimum nodes of this workspace’s clusters and instances.",
    quotaWorkspaceQuotaNewLimitHelpText = "Allocate subscription level quota between workspaces by setting it here. To change the all-up subscription level quota, use the request quota button at the bottom. Note that you need to be a subscription owner to modify these values.",
    quotaWorkspaceQuotaPlaceHolder = "Unallocated cores: {0}, Maximum: {1}",
    quotaWorkspaceQuotaResourceNameHelpText = "Expand each VM size to view and allocate subscription level quota between workspaces.",
    quotaWorkspaceQuotaUnknownError = "Unknown error",
    quotaWorkspaceViewClusterHelptext = "For a cluster we show your currently allocated cores and maximum cores it can scale to.",
    quotaWorkspaceViewResourceNameHelptext = "Expand each workspace to view your quota allocation and resource usage by VM family and further to view your clusters and instances.",
    quotaWorkspaceViewTabHeader = "Workspace view",
    quotaWorkspaceViewUsageHelptext = "For an instance it continues to use quota even in Stopped state so you can restart it at any time.",
    recentlyDeletedWorkspaces = "Recently deleted",
    resourceLocationColumn = "Location",
    /** Required field validation text for create textbox */
    resourceNameRequired = "The resource name is required",
    scaling = "Scaling",
    selectLocation = "Select a location",
    selectSubscription = "Select a subscription",
    singleInvalidTabErrorMessage = "Validation failed for the following tab: {0}. Required information is missing or not valid.",
    subtitleEnvironmentInfoBlade = "Configure compute for deploying and managing models",
    summaryTabTitle = "Review + create",
    tabValidationErrors = "Validation failed. Required information is missing or not valid.",
    tags = "Tags",
    textAzureDatabricksOptionCreateNew = "Create New",
    textAzureDatabricksOptionUseExisting = "Use Existing",
    textLaunchWebWorkspace = "Explore your Machine Learning workspace to run and track experiments, compare model performance, and deploy models.",
    textLaunchWebWorkspaceHeader = "Explore your Azure Machine Learning workspace",
    textMachineLearningServicesAutoMLLinkBody = "Automatically create a model from your existing data.",
    textMachineLearningServicesAutoMLLinkHeader = "Create a new Automated Machine Learning Model (Preview)",
    textMachineLearningServicesDocumentationLinkBody = "Learn how to use Azure Machine Learning.",
    textMachineLearningServicesDocumentationLinkHeader = "View Documentation",
    textMachineLearningServicesForumLinkBody = "Join the discussion of Azure Machine Learning.",
    textMachineLearningServicesForumLinkHeader = "View Forum",
    textMachineLearningServicesManageFeatoreStoreLinkHeader = "Create and Manage",
    textMachineLearningServicesManageFeatureStoreLinkBody = "Learn how to create and manage Machine Learning registries.",
    textMachineLearningServicesManageRegistryLinkBody = "Learn how to create and manage Machine Learning registries.",
    textMachineLearningServicesManageRegistryLinkHeader = "Create and Manage",
    textMachineLearningServicesNotebookVMLinkBody = "Quickly get started with the Python SDK and run sample experiments with Azure Machine Learning Notebook VMs.",
    textMachineLearningServicesNotebookVMLinkHeader = "Get Started with Sample Notebooks (Preview)",
    textMachineLearningServicesShareRegistryLinkBody = "Learn how to share Machine Learning assets using registries.",
    textMachineLearningServicesShareRegistryLinkHeader = "Share models, components and environments",
    textMachineLearningServicesVisualInterfaceLinkBody = "Drag and drop existing components to create new models.",
    textMachineLearningServicesVisualInterfaceLinkHeader = "Build a model using the Visual Interface (Preview)",
    /** short for 'Not Available' */
    textNotAvailable = "N/A",
    textViennaGitHubLinkBody = "Get inspired by a large collection of machine learning examples.",
    textViennaGitHubLinkHeader = "View more samples at GitHub",
    titleAssetsGroup = "Assets",
    titleAuthoringGroup = "Authoring (Preview)",
    titleDeployments = "Deployments",
    titleEnvironmentInfoBlade = "Machine Learning Compute",
    titleError = "Error",
    titleGiveFeedback = "Give feedback",
    titleInsights = "Insights (preview)",
    titleMachineLearningServicesDeleteConfirmationMessageBox = "Delete Workspace",
    titleMachineLearningServicesRegistryDeleteConfirmationMessageBox = "Delete Registry",
    titleMonitoringLens = "Getting Started",
    titleSettings = "Settings",
    titleSupport = "Support + troubleshooting",
    titleWebWorkspaceBlade = "Machine Learning",
    validationAIServicesNameAlreadyInUse = "This AI Services name already exists",
    validationAppInsightsNameAlreadyInUse = "This application insights name already exists",
    validationContainerRegistryNameAlreadyInUse = "This container registry name already exists",
    validationCreateWorkspacePermission = "You don't have the required permissions ({0}) to create an account under the selected resource group",
    validationDependentResourcesAlreadyInUse = "Dependent resources with this name already exist",
    validationError = "There was an error while attempting to validate the resource",
    validationHubNameAlreadyInUse = "This AI hub name already exists",
    validationKeyVaultNameAlreadyInUse = "This key vault name already exists",
    validationPrimaryRegionNotSelected = "Please ensure the primary region is one of the selected values",
    validationProjectNameAlreadyInUse = "This AI project name already exists",
    validationRegionNotSelected = "Please select at least one region",
    validationRegistryDescriptionTooLarge = "This registry description has a maximum length of 256 characters.",
    validationRegistryNameAlreadyInUse = "This registry name already exists",
    validationRegistryNameInvalid = "Registry name must be between 3 and 33 characters long. Its first character has to be alphanumeric, and the rest may contain hyphens and underscores. No whitespace is allowed.",
    validationStorageAccountNameAlreadyInUse = "This storage account name already exists",
    validationWorkspaceNameAlreadyInUse = "This workspace name already exists",
    validationWorkspaceNameAlreadyInUseAndSoftDeleted = "This workspace name already exists, or is being reserved by a workspace which was previously soft deleted. Please use a different name",
    validationWorkspaceNameInvalid = "Resource name must be between 3 and 33 characters long. Its first character has to be alphanumeric, and the rest may contain hyphens and underscores. No whitespace is allowed.",
    validationWorkspaceNameReserved = "This workspace name is reserved",
    workspaceCancelUpgradeButtonText = "Cancel",
    workspaceConfirmUpgradeButtonText = "Confirm Upgrade",
    workspaceCreateSKUPricingDetails = "View full pricing details",
    workspaceCreateSKUTooltip = "The Enterprise SKU (preview) boosts productivity for all ML skill levels and offers robust MLOps capabilities to manage the complete ML lifecycle. THe Basic SKU workspace allows for open source development and a code-first experience.",
    workspaceErrorMessage = "The selected subscription doesn’t have permissions to register the resource provider. For more information, <a href = {0} target = \"_blank\">click here</a>.",
    workspaceLoadFailure = "Workspace with ID \"{0}\" could not be loaded.",
    workspaceSKUPropertyLabel = "Workspace edition",
    workspaceTwoBannerBasicSKUBody = "Introducing a new immersive experience (preview) for managing the end-to-end machine learning lifecycle.",
    workspaceTwoBannerBasicSKUTitle = "Launch Preview Now",
    workspaceTwoBannerBody = "An immersive experience for managing the end-to-end machine learning lifecycle.",
    workspaceTwoBannerBodyPreview = "Introducing a new immersive experience (preview) for managing the end-to-end machine learning lifecycle.",
    workspaceTwoBannerButton = "Launch now",
    workspaceTwoBannerLink = "Learn more",
    workspaceTwoNoticeButton = "Launch now",
    workspaceTwoNoticeMessage = "Contents of this page will be moving to a new immersive experience for\n      managing the end-to-end machine learning lifecycle. Compute targets will\n      be manageable from both locations. Features provided in preview are\n      offered at no additional charge but may not remain so after general\n      availability.",
    workspaceUpgradeAboutOurPricing = "about our pricing",
    workspaceUpgradeAuthorizationFailed = "You currently do not have permissions to upgrade your workspace. Please contact your IT administrator.",
    workspaceUpgradeBannerText = "Upgrade this workspace to Enterprise edition (preview) to use visual machine learning, advanced automated machine learning, and to manage quota.",
    workspaceUpgradeBulletPoint = "You need an Enterprise edition (preview) workspace to view and set quotas on Enterprise workspaces.",
    workspaceUpgradeButtonText = "Upgrade",
    workspaceUpgradeConfirmationBoxContent = "Upgrade workspace to access Enterprise (preview) capabilities. Visit our {0} for more information.",
    workspaceUpgradeConfirmationBoxTitle = "Confirm workspace upgrade",
    workspaceUpgradeLearnMore = "Learn more",
    workspaceUpgradePricingPage = "pricing page",
    workspaceUpgradeQuickLinkBannerText = "Upgrade this workspace to Enterprise edition (preview) to use UI-based tools for all skill levels and more.",
    workspaceUpgradeQuickLinkLearnAboutEnterpriseEdition = "Learn about Enterprise Edition (preview)",
    workspaceUpgradeQuickLinkPostUpgradeBannerText = "Use the Enterprise edition (preview) to access UI-based tools for all skill levels, built-in MLOps and more",
    workspaceUpgradeSetQuotaOperationNotAllowed = "This feature is disabled for you. Please upgrade to an Enterprise edition (preview) workspace to configure quotas.",
    workspaceUpgradeUpgradeCompleteNotificationContent = "Your workspace {0} upgraded successfully.",
    workspaceUpgradeUpgradeCompleteNotificationTitle = "Workspace Upgrade Complete",
    workspaceUpgradeUpgradeFailed = "An unexpected error occurred, please try upgrading your workspace again, or submitting a support ticket.",
    workspaceUpgradeUpgradeFailedNotificationContent = "Your workspace {0} did not upgrade successfully.",
    workspaceUpgradeUpgradeFailedNotificationTitle = "Workspace Upgrade Failed",
    workspaceUpgradeUpgradeSucceeded = "Upgrade Azure Machine Learning workspace {0} succeeded.",
    workspaceUpgradeUpgrading = "Upgrading Azure Machine Learning workspace {0}",
    workspaceUpgradeUpgradingNotificationContent = "Your workspace {0} is upgrading from Basic to Enterprise",
    workspaceUpgradeUpgradingNotificationTitle = "Workspace is currently upgrading",
}

export enum Commands {
    delete = "Delete",
}

export enum ContainerRegistry_Dropdown {
    createNewAriaLabel = "Create new container registry",
    info = "A container registry is used to register docker images used in training and deployments. To minimize costs, a new Azure Container Registry resource is created only after you build your first image. Alternatively, you may choose to create the resource now or select an existing one in your subscription.",
    label = "Container registry",
}

export enum ContainerRegistry_Dropdown_SettingsBlade {
    nameInvalidMessage = "Resource names may contain alpha numeric characters only and must be between 5 and 50 characters.",
    nameLabel = "Name",
    skuBasic = "Basic",
    skuLabel = "SKU",
    skuPremium = "Premium",
    skuStandard = "Standard",
    title = "Create new container registry",
}

export enum ContainerRegistry_Dropdown_SettingsBlade_Name {
    infoDescription = "The name must meet the following requirements:",
    /** Item1 of ContainerRegistry_Dropdown_SettingsBlade_Name_infoDescription */
    infoItem1 = "Unique across all container registries in Azure",
    /** Item2 of ContainerRegistry_Dropdown_SettingsBlade_Name_infoDescription */
    infoItem2 = "Between 5 and 50 characters long",
    /** Item3 of ContainerRegistry_Dropdown_SettingsBlade_Name_infoDescription */
    infoItem3 = "Only contain alphanumeric characters",
}

export enum ContainerRegistry_Dropdown_SettingsBlade_Sku {
    info = "All SKUs provide the same programmatic capabilities. Choosing a higher SKU will provide more performance and scale.",
    infoLearnMore = "Learn more",
}

export enum ContainerRegistry_SummaryTab {
    label = "Container registry",
}

export enum Create_CreateButton {
    label = "Create",
}

export enum Create_Wizard {
    /** PLEASE DO NOT TRANSLATE "Azure Machine Learning" */
    title = "Create Azure Machine Learning",
}

export enum CreateBlade_Advanced_Review {
    byoCosmosText = "Cosmos DB for customer managed keys",
    byoSearchText = "Search service for customer managed keys",
    byoStorageText = "Storage account for customer managed keys",
    /** 0: resource name, 1: resource group name */
    byoValueFormat = "{0} ({1})",
    credentialBasedStorageAccountAccess = "Credential-based",
    customerManagedKeys = "Customer-managed keys",
    disabled = "Disabled",
    enabled = "Enabled",
    enableHBIFlag = "Enable HBI Flag",
    encryptionType = "Encryption type",
    identityBasedStorageAccountAccess = "Identity-based",
    keyURI = "Key URI",
    keyVault = "Key vault",
    microsoftManagedKeys = "Microsoft-managed keys",
    sharedKeyAccess = "Shared key access",
    storageAccountAccessType = "Storage account access type",
}

export enum CreateBlade_Advanced_Review_Encryption {
    title = "Encryption",
}

export enum CreateBlade_Advanced_Review_Identity {
    title = "Identity",
}

export enum CreateBlade_Advanced_Review_Resources {
    title = "Resources",
}

export enum CreateBlade_Network {
    azureFirewallSku = "Azure Firewall SKU",
    connectivityMethod = "Connectivity method",
    displayPrivateDnsZone = "Private DNS Zone",
    displayRegion = "Region",
    displayResourceGroup = "Resource group",
    displaySubnet = "Subnet",
    displaySubscription = "Subscription",
    displayTargetResourceType = "Target resource type",
    networkIsolationDescription = "Choose the type of network isolation you need for your workspace, from not isolated at all to an entirely separate virtual network managed by Azure Machine Learning.",
    networkIsolationLearnMore = "Learn more about managed network isolation",
    networkIsolationTitle = "Network isolation",
    outboundRulesGridEmptyMessage = "Missing required outbound access rules.",
    outboundRulesNotLoadedMessage = "Configure basic settings in order to create outbound rules.",
    outboundRulesPublicInternetEnabledMessage = "No required outbound rules while the outbound access to the public internet is enabled.",
    overviewDescription = "You can connect to your workspace either publicly or privately using a private endpoint.",
    overviewTitle = "Network connectivity",
    privateEndpointDescription = "Create a private endpoint to allow a private connection to this resource.",
    privateEndpointTitle = "Private endpoint",
    registryOverviewDescription = "You can connect to your registry either publicly or privately using a private endpoint.",
    title = "Networking",
    workspaceInboundAccessTitle = "Workspace Inbound access",
    workspaceOutboundAccessTitle = "Workspace Outbound access",
}

export enum CreateBlade_Network_ConnectivityMethod {
    allNetworks = "Enable public access from all networks",
    private = "Disable public access and use private endpoints",
    skuPrivateEndpointErrorMessage = "Private with Internet Outbound and Private with Approved Outbound requires premium SKU container registry for having a private endpoint connection. You can create a new premium container registry or select an existing premium container registry in the Basics tab to use with this private workspace.",
}

export enum CreateBlade_Network_Hub {
    networkIsolationDescription = "Projects associated to an Azure AI hub share network, and can access resources in your virtual network without additional configuration. Choose the type of network isolation you need, from not isolated at all to an entirely separate virtual network managed by Azure Machine Learning.",
}

export enum CreateBlade_Network_NetworkIsolation {
    skuErrorMessage = "Allow Internet Outbound and Allow Only Approved Outbound requires premium SKU container registry for having a private endpoint connection.",
}

export enum CreateBlade_Network_NetworkIsolation_AllowInternetOutbound {
    title = "Allow Internet Outbound",
}

export enum CreateBlade_Network_NetworkIsolation_AllowOnlyApprovedOutbound {
    title = "Allow Only Approved Outbound",
}

export enum CreateBlade_Network_NetworkIsolation_Disabled {
    title = "Disabled",
}

export enum CreateBlade_Network_NetworkIsolation_PrivateApprovedOutbound {
    descriptionItemOne = "Workspace is accessed via private endpoint",
    descriptionItemThree = "Outbound data movement is restricted to approved targets",
    descriptionItemTwo = "Compute can access allowlisted resources only",
    learnMore = "Learn more about data exfiltration protection",
    title = "Private with Approved Outbound",
}

export enum CreateBlade_Network_NetworkIsolation_PrivateInternetOutbound {
    descriptionItemOne = "Workspace is accessed via private endpoint",
    descriptionItemThree = "Outbound data movement is unrestricted",
    descriptionItemTwo = "Compute can access private resources",
    learnMore = "Learn more about private networks",
    title = "Private with Internet Outbound",
}

export enum CreateBlade_Network_NetworkIsolation_Public {
    descriptionItemOne = "Workspace is accessed via public endpoint",
    descriptionItemThree = "Outbound data movement is unrestricted",
    descriptionItemTwo = "Compute can access public resources",
    learnMore = "Learn more about public networks",
    title = "Public",
}

export enum CreateBlade_Network_PrivateEndpoints {
    add = "Add",
    clickToAdd = "Click on add to create a private endpoint",
    /** 0: location, 1: location */
    locationMismatch = "Workspace ({0}) and private endpoint connection ({1}) must be in the same location in order to get compute instance and clusters accessed properly in the virtual network.",
    name = "Name",
    /** 0 = the private endpoint name, 1 = the sub resource name. */
    nameAndSubResource = "{0} ({1})",
    /** 0 = the private endpoint name, 1 = the sub resource name, 2 = the dns zone name. */
    nameSubResourceAndDns = "{0} ({1}) ({2})",
    noContent = "-",
    registrySubResourceHelp = "This is the specific sub-resource for the new registry that this private endpoint will be able to access.",
    registrySubResourceLabel = "Registry sub-resource",
    subResourceHelp = "This is the specific sub-resource for the new workspace that this private endpoint will be able to access.",
    subResourceLabel = "Workspace sub-resource",
    title = "Private endpoints",
    type = "Private Endpoint",
}

export enum CreateBlade_Network_SubnetDelegate {
    learnMore = "For more information, see",
    message = "Follows different management boundaries than your hub and projects, injected into your Azure VNET.",
    title = "Delegate Azure virtual network subnet for agents",
}

export enum CreateBlade_Network_WorkspaceApprovedOutbound_OutboundAccess {
    azureFirewallSku = "Azure Firewall SKU (Preview)",
    azureFirewallSkuDescription = "Select from Basic or Standard SKU for the Azure Firewall deployment. For more information on Azure Firewall, see",
    azureFirewallSkuPricingText = "Pricing",
    recommendedTargetDescription = "There are several optional outbound targets recommended for your workspace in scenarios such as AutoML and Data Labeling. You can modify or delete them,",
    recommendedTargetLearnMore = "Learn more about recommended targets",
    requiredTargetDescription = "There are a few outbound targets added by Azure Machine Learning that are required for your workspace to access things like storage,  notebooks, and deployment environments.",
    requiredTargetLearnMore = "Learn more about required targets",
}

export enum CreateBlade_Network_WorkspaceApprovedOutbound_PrivateNetworkSettings {
    description = "Add private endpoints people can use to access your workspace, and manage the outbound targets to which the workspace can access",
    title = "Private network and data exfiltration settings",
}

export enum CreateBlade_Network_WorkspaceInternetOutbound {
    targetDescription = "There are a few private endpoints required for your workspace to access private resources like storage. You can also add your additional private link targets here for your custom scenarios.",
    targetLearnMoreLink = "Learn more about required private link target",
}

export enum CreateBlade_Network_WorkspaceInternetOutbound_OutboundAccess {
    useMyOwnVirtualNetwork = "Use my own virtual network",
    virtualNetworkManagedByAML = "Use a virtual network managed by Azure Machine Learning",
    virtualNetworkManagedByAMLDescription = "Private endpoints are required for your workspace to access private resources like storage, notebooks, and deployment environments. You can also add your additional private link targets here for your custom scenarios.",
    virtualNetworkManagedByAMLLearnMore = "Learn more about required private link targets",
}

export enum CreateBlade_Network_WorkspaceInternetOutbound_PrivateNetworkSettings {
    description = "Add private endpoints people can use to access your workspace, and choose how to manage outbound access from your workspace to things like Storage Accounts, Key Vaults and Registries.",
    title = "Private network settings",
}

export enum CreateBlade_Network_WorkspaceOutboundAccess {
    activeStatusMessage = "The rule has been applied and effective.",
    addUserDefinedOutboundRules = "Add user-defined outbound rules",
    connectionName = "Connection Name",
    deleteUserDefinedOutboundRules = "Delete user-defined outbound rules",
    destination = "Destination",
    destinationType = "Destination Type",
    inactivePrivateEndpointStatusMessage = "The rule will become active when managed network is provisioned, otherwise please check if approval is pending for the target resource.",
    inactiveStatusMessage = "The rule will become active when managed network is provisioned.",
    parentRules = "Parent Rules",
    provisionManagedVirtualNetwork = "Provision managed virtual network (Preview)",
    provisionManagedVirtualNetworkInfo = "Managed virtual network will be provisioned at workspace creation. Charges will be incurred for network resources, such as private endpoint.",
    provisionManagedVirtualNetworkSummaryTitle = "Provision managed virtual network",
    status = "Status",
}

export enum CreateBlade_Network_WorkspacePrivateOutbound {
    addUserDefinedOutboundRuleText = "You can also add your own outbound targets here for your custom scenarios.",
    dependencyOutboundRules = "Dependency outbound rules",
    recommendedOutboundRules = "Recommended outbound rules",
    requiredOutboundRules = "Required outbound rules",
    userDefinedOutboundRules = "User-defined outbound rules",
}

export enum CreateBlade_Workspace {
    type = "Workspce type",
}

export enum CreateBladeHubAIServices {
    ariaLabel = "Connect AI Services, including OpenAI",
    info = "Provider of Microsoft-maintained base models. Managed in your Azure subscription as a separate resource.",
    label = "Connect AI Services incl. OpenAI",
    skipText = "Skip connecting AI services",
}

export enum CreateBladeHubConfigDefaultResourceGroup {
    info = "When a user does not specify a resource group for their project, this resource group will be used by default. The project creator gets granted an Azure RBAC owner role assignment on the project instance",
    label = "Default project resource group",
}

export enum CrossRegionComputeQuotas {
    configureQuotaButtonText = "Configure quota",
    dedicatedCoresSectionLabel = "Dedicated cores usage",
    loadingText = "Loading...",
    lowPriorityCoresSectionLabel = "Low priority cores usage",
    lowPriorityUsageInfoText = "Please note that the number of low-priority cores per subscription is single value accross VM families.",
    quotaUpdateFailed = "Quota update failed",
    quotaUpdateSucceeded = "Quota successfully updated",
    /** 0: family name */
    requestBladeFamilyNameFormat = "{0} Series",
    requestQuotaButtonText = "Request quota increase",
    subscriptionViewText = "Subscription view",
    toggleText = "Show workspaces across all locations (cross-location compute)",
    totalClustersLabel = "Cluster quota:",
    /** 0: number of used, 1: number of remaining */
    totalClustersTextFormat = "{0} clusters and CIs used | {1} remaining",
    /** 0: number of used, 1: number of remaining */
    totalCoresTextFormat = "{0} cores used | {1} cores remaining",
    totalDedicatedCoresLabel = "Dedicated quota:",
    totalLowPriorityCoresLabel = "Low priority quota:",
    workspaceViewText = "Workspace view",
}

export enum CrossRegionComputeQuotas_QuotaConfigurationBlade {
    filterText = "Enter at least 3 characters to search for workspaces...",
    noItemsText = "No workspaces found to display.",
    /** 0: error details */
    quotaConfigNotAvailable = "Quotas cannot be configured due to following error: {0}",
    subTitle = "Configure your quota across your subscription here",
    title = "Configure quota",
    /** 0: error details */
    workspacesNotAvailable = "You can only configure quota for workspaces which have been configured before since the list of workspaces of this subscription cannot be reached due to following error: {0}",
}

export enum CrossRegionComputeQuotas_QuotaUsageBlade {
    /** 0: error details */
    crossLocationUsagesNotAvailable = "Cross location workspace information (whether the workspace owning a compute is in a different location than the selected location) cannot be displayed due to following error but you can still see all the usages: {0}",
    quotaInfoLearnMore = "Learn more",
    quotaInfoText = "Azure Machine Learning quota is preconfigured limits, which specifies the maximum number of cores you can use at any given moment.",
    /** 0: error details */
    usagesNotAvailable = "Compute usages cannot be displayed do to following error: {0}",
}

export enum CrossRegionComputeQuotas_UsageGrid {
    /** 0: number of used, 1: number of limit */
    coresUsageTextFormat = "{0} of {1} cores utilized",
    coreUtilizationColumnText = "Cores utilization",
    crossLocationEnableText = "Enable cross location compute toggle",
    /** 0: number of cores */
    crossLocationWarningCoresFormat = "{0} cores",
    /** 0: {0} cores (see CrossRegionComputeQuotas_UsageGrid_crossLocationWarningCoresFormat */
    crossLocationWarningLine1 = "You are using {0} in other locations.",
    /** 1: enable text (CrossRegionComputeQuotas_UsageGrid_crossLocationEnableText) */
    crossLocationWarningLine2 = "{1} to view them.",
    /** 0: family name */
    familyNameFormat = "Standard {0} Family vCPUs",
    /** 0: number of used, 1: number of limit, 2: usage in percent */
    totalCoresTextFormat = "{0} out of {1} ({2})",
    usageColumnText = "Usage",
    usagePercentageColumnText = "Usage percentage",
    vmFamiliesColumnText = "VM Families",
    /** 0: workspace name, 1: resource group name, 2: location */
    workspaceCrossLocationDisplayText = "{0} ({1}, {2})",
    /** 0: workspace name, 1: resource group name */
    workspaceDisplayText = "{0} ({1})",
    workspacesColumnText = "Workspaces",
}

export enum DeleteHubBlade {
    title = "Delete resource",
}

export enum DeleteHubBlade_AssociatedResourcesListColumn {
    name = "Name",
    resourceGroup = "Resource Group",
    type = "Type",
}

export enum DeleteHubBlade_Buttons {
    cancel = "Cancel",
    delete = "Delete",
}

export enum DeleteHubBlade_ConfirmDeleteSection {
    label = "Confirm delete",
    placeholder = "Type the resource name",
}

export enum DeleteHubBlade_ConfirmDeleteSection_ValidationErrorMessage {
    emptyString = "The value must not be empty",
    nameMismatch = "Name does not match",
}

export enum DeleteHubBlade_Description {
    deployment = "deployed model(s)",
    hubName = "Hub to be deleted:",
    project = "project(s)",
    section1 = "Your hub with {0} and {1} will be permanently deleted.",
    section2 = "Connected resources may still store hub data and are not automatically deleted. If you delete these resources you may break other existing Azure deployments.",
    section3 = "Choose connected resources you'd like to additionally delete:",
}

export enum DeleteHubBlade_ProjectListColumn {
    deployment = "Deployment",
    project = "Project",
}

export enum DeleteHubBlade_RBAC {
    message = "You are missing Azure RBAC delete permission on one or more connected resources. Delete these resources later from Azure portal.",
}

export enum DeleteHubBlade_ResourceType {
    aiServices = "AI Services",
    applicationInsights = "Application Insights",
    cognitiveService = "Cognitive Service",
    computerVision = "Computer Vision",
    containerRegistry = "Container Registry",
    contentSafety = "Content Safety",
    formRecognizer = "Document Intelligence",
    keyVault = "Key Vault",
    openAI = "Open AI",
    searchService = "Search Service",
    speechServices = "Speech Services",
    storageAccount = "Storage Account",
    textAnalytics = "Language",
    textTranslation = "Translator",
    hub = "AI Hub",
    project = "AI Project",
    endpoint = "AI Endpoint",
}

export enum DeleteHubNotification_Cancel {
    title = "Resource deletion canceled",
}

export enum DeleteHubNotification_Error {
    description = "An error occurred while deleting hub '{workspaceName}' and its associated resources.",
    title = "Resource deletion error",
}

export enum DeleteHubNotification_InProgress {
    description = "Hub '{workspaceName}' and its associated resources deletion in progress...",
    title = "Deleting resource...",
}

export enum DeleteHubNotification_Success {
    description = "Hub '{workspaceName}' and its associated resources were deleted successfully.",
    title = "Successfully deleted",
}

export enum DeleteHubStatusBlade {
    title = "Delete resource status",
}

export enum DeleteHubStatusBlade_ResourceStatusListColumn {
    resource = "Resource",
    status = "Status",
    type = "Type",
}

export enum Description {
    createAzureMachineLearningHubResource = "Centrally configure Azure AI hubs",
    createAzureMachineLearningRegistryResource = "For sharing ML assets across workspaces",
    createAzureMachineLearningResource = "For ML projects and teams",
}

export enum EncryptionSettings {
    dirtyFormWarning = "You have unsaved changes.",
    header2 = "After a workspace is deployed, you can rotate the encryption key but not change the encryption type from Microsoft-managed key to Customer-managed key.",
    title = "Encryption",
}

export enum EncryptionSettings_Command {
    discard = "Discard",
    dismissAriaLabel = "Close",
    resultError = "Error updating key",
    resultLoading = "Updating...",
    resultSuccess = "Success",
    save = "Save",
    seeMore = "See more",
}

export enum EncryptionSettings_Header1 {
    replacement1 = "data encryption",
    replacement2 = "customer-managed key encryption",
    template = "Your data is encrypted by default using Microsoft-managed keys. For additional control over your data, you may choose to bring your own key for encryption. To learn more, see {} and {}.",
}

export enum EncryptionSettings_SelectedKey {
    keyLabel = "Key",
    title = "Selected key vault and key",
    vaultLabel = "KeyVault",
    versionLabel = "Version",
}

export enum EncryptionSettings_SelectionSection {
    title = "Encryption selection",
    typeLabel = "Encryption type",
    vaultAndKeyLabel = "Key vault and key",
    vaultAndKeySelect = "Select a key vault and key",
}

export enum EncryptionTab {
    title = "Encryption",
}

export enum Hub_Projects {
    loadFailure = "Associated projects of Azure AI hub with ID \"{0}\" could not be loaded.",
    title = "Projects",
}

export enum Hub_Projects_Toolbar {
    add = "Add",
    refresh = "Refresh",
}

export enum HubConfigDefaultResourceGroup_SummaryTab {
    label = "Default project resource group",
}

export enum IdentityTab {
    title = "Identity",
}

export enum Keyvault_Dropdown {
    createNewAriaLabel = "Create new key vault",
    info = "A key vault is used to store secrets and other sensitive information that is needed by the workspace. You may create a new Azure Key Vault resource or select an existing one in your subscription.",
    label = "Key vault",
}

export enum Keyvault_Dropdown_Hub {
    info = "A key vault is used to store secrets and other sensitive information that is needed by the AI hub. You may create a new Azure Key Vault resource or select an existing one in your subscription.",
}

export enum Keyvault_Dropdown_SettingsBlade {
    nameInvalidMessage = "Vault name must be between 3-24 alphanumeric characters. The name must begin with a letter, end with a letter or digit, and not contain consecutive hyphens.",
    nameLabel = "Name",
    title = "Create new key vault",
}

export enum Keyvault_Dropdown_SettingsBlade_Name {
    infoDescription = "The name must meet the following requirements:",
    /** Item1 of Keyvault_Dropdown_SettingsBlade_Name_infoDescription */
    infoItem1 = "Unique across all existing key vaults in Azure",
    /** Item2 of Keyvault_Dropdown_SettingsBlade_Name_infoDescription */
    infoItem2 = "Between 3 and 24 characters long",
    /** Item3 of Keyvault_Dropdown_SettingsBlade_Name_infoDescription */
    infoItem3 = "Only contain alphanumeric characters and hyphens",
    /** Item4 of Keyvault_Dropdown_SettingsBlade_Name_infoDescription */
    infoItem4 = "Cannot start with a number",
}

export enum Keyvault_RadioButton {
    byoKeyVault = "Azure key vault",
    label = "Credential store",
    managedKeyVault = "Microsoft-managed (preview)",
}

export enum Keyvault_RadioButton_ManagedKeyVault {
    infoIcon = "Preview: secrets are stored in Microsoft-managed credential store. Secret data lifecycle follows your hub, projects, connections and compute.",
}

export enum Keyvault_SummaryTab {
    label = "Key vault",
}

export enum Keyword {
    alert = "Alert",
    audit = "Audit",
    instance = "Instance",
    log = "Log",
    rules = "Rules",
    scale = "Scale",
    scaling = "Scaling",
}

export enum ManagedIdentities {
    menuText = "Identity",
}

export enum MLAppDeployments_Grid_StatusFilter {
    all = "All provisioning states",
    /** 0: status name, 1: count */
    itemFormat = "{0} ({1})",
    /** 0: a number */
    some = "{0} connection states selected",
}

export enum MLAppDeployments_GridColumns {
    name = "Deployment name",
    status = "Provisioning state",
    traffic = "Traffic %",
}

export enum Networking_PrivateEndpointConnections {
    tabText = "Private endpoint connections",
}

export enum Networking_PublicAccess {
    addClientIpAddressInfo = "You may not wish to add your client IP address if the network you are using the azure portal from is atypical (home vs. work environment for example).",
    /** 0 = the client's ip address */
    addClientIpAddressLabel = "Add your client IP address ('{0}')",
    addressRange = "Address range",
    addressRangePlaceHolder = "IP address or CIDR",
    allNetworksInfoText = "All networks, including the internet, can access this resource.",
    /** 0: learn more link */
    description = "Public network access allows access to this resource through the internet using a public IP address. An application or resource that is granted access with the following network rules still requires proper authorization to access this resource. {0}",
    disabledInfoText = "No public network can access this resource.",
    enabledFromSelectedIpInfoText = "Allow access from public IP you specified below.",
    firewallDescription = "Add IP ranges to allow access from the internet or your on-premises networks.",
    firewallHeader = "Firewall",
    firewallLearnMore = "Learn more",
    learnMoreText = "Learn more",
    radioAllNetworksText = "All networks",
    radioDisabledText = "Disabled",
    radioEnabledFromAllNetworks = "Enabled from all networks",
    radioEnabledFromSelectedIp = "Enabled from selected IP addresses",
    radioLabel = "Public network access",
    tabText = "Public access",
}

export enum Networking_PublicAccess_FirewallValidation {
    duplicateIpAddresses = "Two identical address ranges have been specified. Address ranges must be unique.",
    exactPrefix = "The subnet prefix must be equal to {0}.",
    /** 0 - min prefix, 1 - max prefix */
    formattedPrefix = "The prefix must be between {0} and {1}.",
    invalidCidr = "Specify an IP address or CIDR.",
    /** 0: cidrValue, 1: validIpForCidrPrefix, 2: prefix */
    invalidCIDRBlockWithSuggestion = "{0} is not a valid CIDR block. Use {1}/{2} instead.",
    invalidIPAddress = "Invalid IP address.",
    /** 0 = the octet with a leading zero, 1 = the ip address containing the octet */
    leadingZerosIpAddress = "The octet '{0}' in IP address '{1}' contains a leading zero.",
    malformedSubnet = "Malformed address range. Address was {0}.",
    /** 0: maxPrefix */
    maxPrefix = "The prefix must be smaller than or equal to {0}.",
    /** 0: minPrefix */
    minPrefix = "The prefix must be greater than or equal to {0}.",
    nonNullSubnet = "A non-null address range is required.",
    octet = "Octet {0} with value {1} is invalid. It must be between {2} and {3}.",
    publicIpRuleValidation = "IP rules support public IP addresses only.",
}

export enum Networking_PublicAccess_Toolbar {
    discardChangesText = "Discard changes",
    refreshText = "Refresh",
    saveText = "Save",
    savingText = "Saving...",
}

export enum Networking_WorkspaceManagedOutboundAccess {
    tabText = "Workspace managed outbound access",
}

export enum OnlineEndpoints_Create {
    introText = "Azure Machine Learning Inference apps enable you to quickly build, deploy and scale enterprise-grade machine learning models running on any platform. Use any open source machine learning framework like TensorFlow, PyTorch, SciKit-Learn, ONNX and more. Use our “no code deployment” to accelerate your productivity or customize your Inference app with your own docker container and/or model scoring code.",
    modelLabel = "Model",
    modelPlaceholder = "Select a model",
    modelVersionLabel = "Model version",
    modelVersionPlaceholder = "Select a model version",
    nameLabel = "Name",
    subTitle = "Create an ML App",
    title = "Machine learning online endpoint",
    workspaceLabel = "Workspace",
    workspacePlaceholder = "Select a workspace",
}

export enum OnlineEndpoints_Create_Dependencies {
    condaDependenciesFileLabel = "Conda dependencies file",
    description = "Customize your model deployment into inference app.",
    entryScriptFileLabel = "Entry script file",
    selectCondaDependenciesFile = "Select a conda dependency file",
    selectEntryScriptFileMessage = "Select an entry script file",
    tabName = "Dependencies",
}

export enum OutboundAccessRule_SettingsBlade {
    amlRegistryPEDependencyRulesWarning = "Dependency outbound rules will be created for all dependency resources under AzureML registry. View these rules under Dependency outbound rules.",
    applicationGatewayFqdnInfoBallon = "FQDNs resolve to the private IP of the Application Gateway private endpoint inside the workspace's managed network. FQDNs are editable as needed. Please save after editing the fields.",
    destinationTypeBalloonContent = "Type of the outbound destination, FQDN, Private Endpoint, Service Tag.",
    destinationTypeLabel = "Destination type",
    fqdnDestinationBalloonContent = "Fully Qualified Domain Name to allow for outbound traffic.",
    fqdnDestinationLabel = "FQDN destination",
    fqdnsLabel = "FQDNs",
    note = "Note",
    portRangeBalloonContent = "Provide a single port, such as 80; a port range, such as 1024-655535; or a comma-separated list of single ports and/or port ranges, such as 80,1024-655535. This specifies on which ports traffic will be allowed by this rule. Provide an asterisk(*) to allow traffic on any port.",
    portRangeLabel = "Port ranges",
    protocolBalloonContent = "Network protocol to allow, TCP, UDP, ICMP or Any",
    protocolLabel = "Potocol",
    resourceGroupBalloonContent = "Resource group containing the target resource for the private endpoint.",
    resourceGroupLabel = "Resource group",
    resourceNameBalloonContent = "Name of the target resource for the private endpoint.",
    resourceNameLabel = "Resource name",
    resourceTypeBalloonContent = "Type of the Azure resource that supports Private Link.",
    resourceTypeLabel = "Resource type",
    ruleNameAlreadyExists = "Rule name already exists",
    ruleNameBalloonContent = "Name of the outbound rule that is unique in the workspace.",
    ruleNameLabel = "Rule name",
    serviceTagBalloonContent = "Predefined identifiers that represent a category of IP addresses.",
    serviceTagLabel = "Service tag",
    sparkEnabledBalloonContent = "Check to enable an additional private endpoint to be used by jobs running on Spark.",
    sparkEnabledLabel = "Spark enabled",
    sparkStatusBalloonContent = "Indicates whether managed network is provisioned for Spark jobs.",
    sparkStatusLabel = "Spark status",
    statusBalloonContent = "Status is read-only, when Active, indicates the managed network is provisioned and ready. When Inactive, indicates it has not provisioned.",
    statusLabel = "Status",
    subResourceBalloonContent = "Sub resource to connect for the private endpoint.",
    subResourceLabel = "Sub resource",
    subscriptionBalloonContent = "Subscription containing the target resource for the private endpoint.",
    subscriptionLabel = "Subscription",
    title = "Workspace outbound rules",
}

export enum OutboundAccessRule_SettingsBlade_AppGateway_SubResource {
    errorMessage = "No private link config is found in resource. Set it up before creating PE outbound rule.",
}

export enum OutboundAccessRule_SettingsBlade_FqdnCostInfo {
    managedNetworkIsolationLinkText = "Managed Network Isolation.",
    moreInformationText = "For more information on Azure Firewall, see",
    pricingLinkText = "Pricing.",
    text = "FQDN outbound rules are implemented using Azure Firewall. If you use outbound FQDN rules, charges for Azure Firewall are included in your billing. To learn more about outbound rules, see",
}

export enum OutboundAccessRule_SettingsBlade_FqdnValidation {
    applicationGatewayFqdnRequired = "FQDN is required.",
    emptyLabelMessage = "Domain label should not be empty.",
    invalidFormatMessage = "Specify a valid FQDN with at least three labels for Application Gateway access.",
}

export enum Overview_Banner {
    launchButtonText = "Launch studio",
    title = "Work with your models in Azure Machine Learning Studio",
}

export enum Overview_Banner_Description {
    /** {}: learnMoreText */
    label = "The Azure Machine Learning Studio is a web app where you can build, train, test, and deploy ML models. Launch it now to start exploring, or {}",
    learnMoreText = "learn more about the Azure Machine Learning studio",
}

export enum Overview_Banner_Registry {
    launchButtonText = "Launch registry in studio",
    title = "Work with your registry in Azure Machine Learning Studio",
}

export enum Overview_Essentials {
    edit = "edit",
    managedResourceGroup = "Managed resource group",
    mlFlowWebURLText = "MLFlow tracking URI",
    projectID = "Project ID",
    registryWebURLText = "Registry web URL",
    studioWebURLText = "Studio web URL",
}

export enum PrivateEndpoints {
    filterByName = "Filter by name...",
    filterByStatus = "Filter by connection state...",
    genericErrorMessage = "There was an error processing your request. Try again in a few moments.",
    label = "Private endpoint connections",
    menuText = "Networking",
}

export enum PrivateEndpoints_Commands {
    addPrivateEndpoint = "Private endpoint",
    approve = "Approve",
    description = "Description",
    reject = "Reject",
    remove = "Remove",
}

export enum PrivateEndpoints_Commands_Approve {
    messagePlural = "Do you want to approve the {0} selected connections?",
    messageSingular = "Do you want to approve the connection '{0}'?",
    title = "Approve connection",
}

export enum PrivateEndpoints_Commands_ApproveNotifications_Failure {
    message = "Failed to approve {0} out of {1} private endpoint connections",
    title = "Failed to approve private endpoint connections",
}

export enum PrivateEndpoints_Commands_ApproveNotifications_InProgress {
    message = "Approving {0} private endpoint connections",
    title = "Approving private endpoint connections...",
}

export enum PrivateEndpoints_Commands_ApproveNotifications_Success {
    message = "Successfully approved {0} private endpoint connections.",
    title = "Successfully approved private endpoint connections",
}

export enum PrivateEndpoints_Commands_DeleteNotifications_Failure {
    message = "Failed to delete {0} out of {1} private endpoint connections",
    title = "Failed to delete private endpoint connections",
}

export enum PrivateEndpoints_Commands_DeleteNotifications_InProgress {
    message = "Deleting {0} private endpoint connections",
    title = "Deleting private endpoint connections...",
}

export enum PrivateEndpoints_Commands_DeleteNotifications_Success {
    message = "Successfully deleted {0} private endpoint connections.",
    title = "Successfully deleted private endpoint connections",
}

export enum PrivateEndpoints_Commands_Reject {
    messagePlural = "Do you want to reject the {0} selected connections?",
    messageSingular = "Do you want to reject the connection '{0}'?",
    title = "Reject connection",
}

export enum PrivateEndpoints_Commands_RejectNotifications_Failure {
    message = "Failed to reject {0} out of {1} private endpoint connections",
    title = "Failed to reject private endpoint connections",
}

export enum PrivateEndpoints_Commands_RejectNotifications_InProgress {
    message = "Rejecting {0} private endpoint connections",
    title = "Rejecting private endpoint connections...",
}

export enum PrivateEndpoints_Commands_RejectNotifications_Success {
    message = "Successfully rejected {0} private endpoint connections.",
    title = "Successfully rejected private endpoint connections",
}

export enum PrivateEndpoints_Commands_Remove {
    messagePlural = "Do you want to delete the {0} selected connections?",
    messageSingular = "Do you want to delete the connection '{0}'?",
    title = "Delete connection",
}

export enum PrivateEndpoints_Create {
    /** 0: error code, 1: error message */
    validationErrorFormat = "{0}: {1}",
}

export enum PrivateEndpoints_Grid_StatusFilter {
    all = "All connection states",
    /** 0: status name, 1: count */
    itemFormat = "{0} ({1})",
    /** 0: a number */
    some = "{0} connection states selected",
}

export enum PrivateEndpoints_GridColumns {
    description = "Description",
    name = "Connection name",
    privateEndpoint = "Private endpoint",
    status = "Connection state",
}

export enum PrivateEndpoints_Status {
    approved = "Approved",
    disconnected = "Disconnected",
    pending = "Pending",
    rejected = "Rejected",
}

export enum PrivateEndpoints_Toolbar {
    refresh = "Refresh",
}

export enum Properties {
    discard = "Discard changes",
    refresh = "Refresh",
    save = "Save",
    saving = "Saving...",
}

export enum Quota_Link {
    bladeDescription = "View quota by subscription and region, and request quota directly from the studio.",
    /** PLEASE DO NOT TRANSLATE "Azure AI" */
    bladeTitle = "Request and view quota in Azure AI Foundry",
    button = "View quota",
}

export enum RegionsTab {
    additionRegionLabel = "Additonal regions",
    description = "Select regions in which you currently have or plan to create AzureML workspaces and use assets from this registry. You can choose to add more regions to the registry later.",
    title = "Regions",
}

export enum RequestQuota {
    dedicatedSectionLabel = "Dedicated",
    lowPrioritySectionLabel = "Low priority",
    vmTypeDedicated = "Dedicated",
    vmTypeLowPriority = "Low priority",
    vmTypesLabel = "VM Types",
}

export enum SearchService_Dropdown {
    /** 0: search service name, 1: sku name */
    standardSupportedFormat = "{0} (SKU: {1}) - Standard SKU is needed at the minimum",
}

export enum Security {
    menuText = "Security",
}

export enum SoftDeletedWorkspace_Blade {
    errorMessage = "Error loading soft deleted resources",
    loadingText = "Loading...",
    subTitle = "Recover or permanently delete resources",
    title = "Recently deleted resources",
}

export enum SoftDeletedWorkspace_Blade_AIStudio_ToolBar {
    header = "AI Foundry resources",
}

export enum SoftDeletedWorkspace_Blade_Footer_Buttons {
    cancel = "Cancel",
    purge = "Permanently delete",
    recover = "Recover",
}

export enum SoftDeletedWorkspace_Blade_ToolBar {
    header = "Machine Learning workspaces",
}

export enum SoftDeletedWorkspace_Blade_ToolBar_Buttons {
    refresh = "Refresh",
}

export enum SoftDeletedWorkspace_DeleteBlade {
    title = "Delete resource",
}

export enum SoftDeletedWorkspace_DeleteBlade_FormControls_Buttons {
    cancel = "Cancel",
    delete = "Delete",
}

export enum SoftDeletedWorkspace_DeleteBlade_FormControls_Confirm {
    errorMessage = "Name does not match",
    label = "Confirm delete",
    placeholder = "Type the resource name",
}

export enum SoftDeletedWorkspace_DeleteBlade_FormControls_PermanentDelete {
    checkbox = "Delete this resource permanently",
    header = "Permanently delete",
}

export enum SoftDeletedWorkspace_DeleteBlade_Message {
    cMKtext = "This resource uses a customer-managed key (CMK) for data encryption. When soft-deleted, dependent resources for storing data will not be deleted and will incur cost until this resource is hard-deleted. {0}",
    link = "Learn more",
    text = "When you delete this resource, it is temporarily placed in a ‘soft-delete’ state that allows you to recover it. Deletion of your data is postponed until your resource is permanently deleted by you, or until the expiry of the soft-delete data retention period of fourteen (14) days. You can override the soft-delete behavior and permanently delete your resource immediately. {0}",
}

export enum SoftDeletedWorkspace_DeleteBlade_Message_AIStudio {
    text = "Permanently delete this resource \"{0}\"?",
}

export enum SoftDeletedWorkspace_Grid {
    ariaLabel = "Deleted resources grid",
    noWorkspacesFound = "No resources found to display",
}

export enum SoftDeletedWorkspace_Grid_Columns {
    deletedDate = "Deleted date",
    name = "Name",
    purgeDate = "Scheduled permanent deletion date",
    resourceGroup = "Resource group",
}

export enum SoftDeletedWorkspace_OverviewBlade {
    deleteMessage = "The soft delete feature has been enabled on this resource. After you soft delete,  this resource data remains available. It will get purged after the retention period. You may purge it sooner, or recover the resource",
}

export enum SoftDeletedWorkspace_WorkspacesBlade_Commands_Generic_Error {
    message = "There was an error processing your request. Try again in a few moments",
}

export enum SoftDeletedWorkspace_WorkspacesBlade_Commands_PurgeNotifications_Failure {
    message = "Failed to permanently delete {0} out of {1} resources",
    title = "Failed to delete resources",
}

export enum SoftDeletedWorkspace_WorkspacesBlade_Commands_PurgeNotifications_InProgress {
    message = "Permanently deleting {0} resources",
    title = "Deleting resources ...",
}

export enum SoftDeletedWorkspace_WorkspacesBlade_Commands_PurgeNotifications_Success {
    message = "Successfully deleted {0} resource(s) permanently",
    title = "Successfully deleted resources",
}

export enum SoftDeletedWorkspace_WorkspacesBlade_Commands_RecoverNotifications_Failure {
    message = "Failed to recover {0} out of {1} resources",
    title = "Failed to recover resources",
}

export enum SoftDeletedWorkspace_WorkspacesBlade_Commands_RecoverNotifications_InProgress {
    message = "Recovering {0} resource(s)",
    title = "Recovering resources ...",
}

export enum SoftDeletedWorkspace_WorkspacesBlade_Commands_RecoverNotifications_Success {
    message = "Successfully recovered {0} resource(s)",
    title = "Successfully recovered resources",
}

export enum SoftDeletedWorkspace_WorkspacesBlade_Commands_ValidateResources_AppInsights_Failure {
    message = "Recovery error: the associated Application Insights resource \"{0}\" could not be found. It may have been deleted. Recover or recreate the application insights resource under its former name \"{0}\". You may restore your application insights data by recovering the associated log analytics workspace first.\"",
}

export enum SoftDeletedWorkspace_WorkspacesBlade_Commands_ValidateResources_ContainerRegistry_Failure {
    message = "Recovery error: the associated Container Registry resource \"{0}\" could not be found. A soft-deleted cannot be recovered without a Container Registry as a dependency.",
}

export enum SoftDeletedWorkspace_WorkspacesBlade_Commands_ValidateResources_Failure {
    title = "Failed to validate resource {0}",
}

export enum SoftDeletedWorkspace_WorkspacesBlade_Commands_ValidateResources_InProgress {
    message = "Validating {0} resources for {1}",
    title = "Validating resources ...",
}

export enum SoftDeletedWorkspace_WorkspacesBlade_Commands_ValidateResources_KeyVault_Failure {
    message = "Recovery error: the associated Azure Key Vault resource \"{0}\" could not be found. A soft-deleted resource cannot be recovered without its previously attached Key Vault. The required Azure Key Vault may still be recoverable, see `Manage deleted vaults`.",
}

export enum SoftDeletedWorkspace_WorkspacesBlade_Commands_ValidateResources_StorageAccount_Failure {
    message = "Recovery error: the associated Storage Account resource \"{0}\" could not be found. If the storage account was previously soft-deleted, recover it first before recovering this resource.",
}

export enum SoftDeletedWorkspace_WorkspacesBlade_Commands_ValidateResources_Success {
    message = "Successfully validated resources for {0}",
    title = "Successfully validated",
}

export enum StorageAccount_Dropdown {
    createNewAriaLabel = "Create new storage account",
    info = "A storage account is used as the default datastore for the workspace. You may create a new Azure Storage resource or select an existing one in your subscription.",
    label = "Storage account",
}

export enum StorageAccount_Dropdown_Hub {
    info = "A storage account is used as the default datastore for the AI hub. You may create a new Azure Storage resource or select an existing one in your subscription.",
}

export enum StorageAccount_Dropdown_SettingsBlade {
    hnsCheckLabel = "Enable hierarchical namespace (preview)",
    /** 0: learn more */
    hnsDescriptionText = "The Data Lake Storage Gen2 hierarchical namespace accelerates big data analytics workloads, enables faster and more reliable file operations, and enables file-level access control lists (ACLs). {0}",
    hnsDescriptionTitle = "Data Lake Storage Gen2 (preview)",
    /** {0}: Storage account name */
    hnsNotSupportedFormat = "{0} - Hierarchical namespace is not supported",
    learnMoreText = "Learn more",
    nameInvalidMessage = "Storage name must be between 3 and 24 characters and may only contain lowercase letters and numbers.",
    nameLabel = "Name",
    performanceLabel = "Performance",
    performancePremium = "Premium",
    performanceStandard = "Standard",
    /** {0}: Storage account name */
    premiumNotSupportedFormat = "{0} - Premium storage account is not supported",
    replicationGRS = "Geo-redundant storage (GRS)",
    replicationGZRS = "Geo-zone-redundant storage (GZRS)",
    replicationLabel = "Replication",
    replicationLRS = "Locally-redundant storage (LRS)",
    replicationRAGRS = "Read-access geo-redundant storage (RA-GRS)",
    replicationRAGZRS = "Read-access geo-zone-redundant storage (RA-GZRS)",
    replicationZRS = "Zone-redundant storage (ZRS)",
    title = "Create new storage account",
}

export enum StorageAccount_Dropdown_SettingsBlade_Name {
    infoDescription = "The name must meet the following requirements:",
    /** Item1 of StorageAccount_Dropdown_SettingsBlade_Name_infoDescription */
    infoItem1 = "Unique across all existing storage account names in Azure",
    /** Item2 of StorageAccount_Dropdown_SettingsBlade_Name_infoDescription */
    infoItem2 = "Between 3 and 24 characters long",
    /** Item3 of StorageAccount_Dropdown_SettingsBlade_Name_infoDescription */
    infoItem3 = "Only contain lowercase letters and numbers",
}

export enum StorageAccount_Dropdown_SettingsBlade_Performance {
    info = "Standard storage accounts are backed by magnetic drives and provide the lowest cost per GB. Premium storage accounts are backed by solid state drives and offer consistent, low-latency performance.",
}

export enum StorageAccount_Dropdown_SettingsBlade_Replication {
    info = "The data in your storage account is always replicated to ensure durability and high availability. Choose a replication strategy that best matches you requirements.",
    infoLearnMore = "Learn more",
}

export enum StorageAccount_SummaryTab {
    label = "Storage account",
}

export enum StorageTab {
    description = "As users work in Azure AI Foundry, uploaded data, stored credentials and generated artifacts like logs are stored.",
    header = "Configure how your data is stored",
    logsAndDockerHeader = "Logs and docker images",
    title = "Storage",
}

export enum SummaryTab {
    /** 0: error details */
    createResourceGroupErrorText = "Error creating resource group when creating default log analytics workspace: {0}",
    /** 0: error details */
    gettingLogWorkspacesErrorText = "Error getting log workspaces for creating default log analytics workspace: {0}",
}

export enum SummaryTab_Advanced_ManagedIdentity {
    systemAssigned = "System assigned",
    typeText = "Identity type",
    userAssigned = "User assigned",
    userIdentityName = "User identity name",
    userIdentityResourceGroup = "User identity resource group",
}

export enum SummaryTab_Resource {
    /** 0: resource name, 1: resource group name */
    existingFormat = "{0} ({1})",
    microsoftManaged = "Microsoft-managed",
    /** 0: resource name */
    newFormat = "(new) {0}",
    none = "None",
}

/* eslint-enable  @typescript-eslint/member-delimiter-style */
/* eslint-enable  @typescript-eslint/naming-convention */
