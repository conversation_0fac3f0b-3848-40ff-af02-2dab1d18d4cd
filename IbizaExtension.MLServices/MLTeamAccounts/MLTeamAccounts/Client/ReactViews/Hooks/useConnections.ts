import { getConnections } from "../Queries/getConnections";
import { useQuery } from "react-query";
import { TraceSource } from "../Logging/Logging.types";

export interface IUseConnections {
    workspace?: {
        id: string;
    };
    telemetry: {
        source: TraceSource;
    };
}

export function useConnections(props: IUseConnections) {
    const { workspace, telemetry } = props;

    const queryResult = useQuery(
        ["useConnections", workspace?.id],
        () => {
            return getConnections(workspace?.id || "", {
                telemetry: {
                    source: telemetry.source
                }
            });
        },
        {
            staleTime: 1000 * 60 * 5,
            enabled: !!workspace?.id
        }
    );
    return queryResult;
}
