import { ArmId } from "@microsoft/azureportal-reactview/ResourceManagement";
import * as React from "react";
import { useQuery } from "react-query";
import { useWorkspaceContext } from "../CommonComponents/WorkspaceContext";
import { TraceSource } from "../Logging/Logging.types";
import { getContainerRegistries, IGetContainerRegistriesProps} from "../Queries/getContainerRegistries";

interface IContainerRegistryResource {
    id: string;
    name: string;
    location: string;
    subscriptionId: string;
    resourceGroupName: string;
}

export function useContainerRegistries(props: IGetContainerRegistriesProps) {
    const { subscriptionId } = props;
    const queryResult = useQuery(
        ["containerRegistry", `/subscriptions/${subscriptionId}`],
        () => {
            return getContainerRegistries(props);
        },
        {
            staleTime: 1000 * 60 * 5,
            enabled: Boolean(subscriptionId)
        }
    );
    return queryResult;
}

export function useContainerRegistriesInSubscriptionContext() {
    const workspaceContext = useWorkspaceContext();
    const workspaceId = workspaceContext.workspace?.id;

    const { subscriptionId } = React.useMemo(() => {
        const parsedId = workspaceId ? ArmId.parse(workspaceId) : undefined;
        return {
            subscriptionId: parsedId?.subscription || ""
        };
    }, [workspaceId]);

    const { data, isLoading, isError, error } = useContainerRegistries({
        subscriptionId,
        options: {
                telemetry: {
                source: TraceSource.ContainerRegistryDropdown
            }
        }
    });

    const extendedData = React.useMemo(() => {
        const extendedValues: IContainerRegistryResource[] =  data?.content.value.map((ai) => {
            const parsedId = ArmId.parse(ai.id);
            return {
                ...ai,
                subscriptionId: parsedId?.subscription || "",
                resourceGroupName: parsedId?.resourceGroup || ""
            }
        }) || [];
        return {
            ...data,
            content: {
                ...data?.content,
                value: extendedValues
            }
        };
    }, [data]);

    return {
        data: extendedData,
        error,
        isLoading,
        isError
    }
}
