import { getServerlessEndpoints } from "../Queries/getServerlessEndpoints";
import { useQueries } from "react-query";
import { TraceSource } from "../Logging/Logging.types";

export interface IUseServerlessEndpoints {
    projectList: string[];
    telemetry: {
        source: TraceSource;
    };
}

export interface IUseServerlessEndpointsResult {
    isLoading: boolean;
    serverlessEndpointsList: string[];
}

export function useServerlessEndpoints(props: IUseServerlessEndpoints) {
    const { projectList, telemetry } = props;
    const queries = projectList.map(project => {
        return {
            queryKey: ["serverlessEndpoints", project],
            queryFn: () => {
                return getServerlessEndpoints(project || "", {
                    telemetry: {
                        source: telemetry.source
                    }
                });
            },
            staleTime: 1000 * 60 * 5,
            enabled: !!project
        }
    });
    const queryResults = useQueries(queries);
    return queryResults.map(result => {
        return {
            isLoading: result.isLoading,
            serverlessEndpointsList: result.data?.content.value?.map(endpoint => endpoint.id || "") || []
        };
    });
}
