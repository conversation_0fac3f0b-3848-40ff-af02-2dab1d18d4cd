import { getEnvironmentValue } from "@microsoft/azureportal-reactview/Environment";
import { ArmId } from "@microsoft/azureportal-reactview/ResourceManagement";
import { getSubscriptionInfo } from "@microsoft/azureportal-reactview/Subscriptions";
import { isAIStudioKind } from "../../Shared/ViewAgnostic/Utilities";
import { WorkspaceWithID } from "../workspaceUtils";
import { useAIStudioUrl } from "./useAIStudioUrl";
import { WorkspaceMappedKind } from "../../Shared/Enums";
import * as React from "react";

async function getWorkspaceQueryStringParams(id: ArmId): Promise<string> {
    const tenantId = (await getSubscriptionInfo(id.subscription)).tenantId;
    return `tid=${tenantId}&wsid=/subscriptions/${id.subscription}/resourcegroups/${id.resourceGroup}/providers/${id.provider}/workspaces/${id.resourceName}`;
}

const makeUrl: (args: {
    linkState: ILinkState;
    path?: string;
    extraParams?: string[];
}) => string = ({ linkState, path, extraParams }) => {
    const slashPath = path ? `/${path}` : "";
    const queryString = [linkState.params, linkState.aiStudioFlight, ...(extraParams || [])].filter(Boolean).join("&");
    return isAIStudioKind(linkState.kind) ? `${linkState.aiStudioUrl}${slashPath}?${queryString}` : `${linkState.amlStudioUrl}${slashPath}?${queryString}`;
};

interface ILinkState {
    aiStudioUrl: string;
    amlStudioUrl: string;
    kind: WorkspaceMappedKind;
    params: string;
    workspaceArmId?: ArmId;
    aiStudioFlight?: string;
}

const useWorkspaceLinkState: (workspace?: WorkspaceWithID) => ({
    loading: boolean;
    linkState?: ILinkState;
}) = (workspace) => {
    const amlStudioUrl = getEnvironmentValue("webWorkspace2BaseUrl");
    const { url: aiStudioUrl, flight: aiStudioFlight } = useAIStudioUrl();
    const [isParamLookupComplete, setIsParamLookupComplete] = React.useState(false);
    const [workspaceParams, setWorkspaceParams] = React.useState("");

    const workspaceId = workspace?.id;
    const workspaceArmId = React.useMemo(() => {
        if (!workspaceId) {
            return undefined;
        }
        return ArmId.parse(workspaceId);
    }, [workspaceId]);

    const loadWorkspaceParams = React.useCallback(async () => {
        if (!workspaceArmId) {
            setIsParamLookupComplete(true);
            return;
        }
        try {
            const params = await getWorkspaceQueryStringParams(workspaceArmId);
            setWorkspaceParams(params);
        } finally {
            setIsParamLookupComplete(true);
        }
    }, [workspaceArmId]);

    React.useEffect(() => {
        loadWorkspaceParams();
    }, [loadWorkspaceParams]);

    const kind = workspace?.kind || WorkspaceMappedKind.Default;

    return React.useMemo(() => {
        if (!isParamLookupComplete) {
            return {
                loading: true
            };
        };
        return {
            loading: false,
            linkState: {
                aiStudioUrl,
                amlStudioUrl,
                kind,
                aiStudioFlight,
                workspaceArmId,
                params: workspaceParams
            }
        };
    }, [isParamLookupComplete, aiStudioUrl, amlStudioUrl, kind, aiStudioFlight, workspaceArmId, workspaceParams]);
}

export function getOverviewLinkTargetByKind(kind?: WorkspaceMappedKind): LinkTarget {
    switch(kind) {
        case WorkspaceMappedKind.FeatureStore: {
            return LinkTarget.FeatureStore;
        }
        case WorkspaceMappedKind.Hub: {
            return LinkTarget.TeamOverview;
        }
        case WorkspaceMappedKind.Project: {
            return LinkTarget.BuildOverview;
        }
        case WorkspaceMappedKind.Default:
        default: {
            return LinkTarget.MLStudioWorkspace;
        }
    }
}

export enum LinkTarget {
    FeatureStore = "FeatureStore",
    TeamOverview = "TeamOverview",
    TeamProjects = "TeamProjects",
    BuildOverview = "BuildOverview",
    MLStudioWorkspace = "MLStudioWorkspace"
}

function makeLinkForTarget(linkTarget: LinkTarget, linkState: ILinkState, options?: { createMode?: boolean }): string {
    switch (linkTarget) {
        case LinkTarget.FeatureStore: {
            return makeUrl({ linkState, path: `featureStore/${linkState.workspaceArmId?.resourceName}` });
        }
        case LinkTarget.TeamOverview: {
            return makeUrl({ linkState, path: "managementCenter/hub/overview" });
        }
        case LinkTarget.TeamProjects: {
            return makeUrl({ linkState, path: "manage/projects", extraParams: [options?.createMode ? "createMode=true" : ""] });
        }
        case LinkTarget.BuildOverview: {
            return makeUrl({ linkState, path: "build/overview" });
        }
        case LinkTarget.MLStudioWorkspace: {
            return makeUrl({ linkState });
        }
        default: {
            return "";
        }
    }
}

export const useWorkspaceLink: (linkOptions: { workspace?: WorkspaceWithID, linkTarget: LinkTarget, createMode?: boolean }) => {
    loaded: boolean;
    url?: string;
} = (linkOptions) => {
    const { workspace, linkTarget, createMode } = linkOptions;
    const {
        loading,
        linkState
    } = useWorkspaceLinkState(workspace)

    return React.useMemo(() => {
        if (loading || !linkState) {
            return {
                loaded: !loading
            };
        }
        const url = makeLinkForTarget(linkTarget, linkState, { createMode });
        return {
            loaded: true,
            url
        };
    }, [loading, linkState, linkTarget, createMode]);
};
