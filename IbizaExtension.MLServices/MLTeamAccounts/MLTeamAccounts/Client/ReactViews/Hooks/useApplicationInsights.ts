import { ArmId } from "@microsoft/azureportal-reactview/ResourceManagement";
import * as React from "react";
import { useQuery } from "react-query";
import { useWorkspaceContext } from "../CommonComponents/WorkspaceContext";
import { TraceSource } from "../Logging/Logging.types";
import { IGetApplicationInsightsProps, getApplicationInsights } from "../Queries/getApplicationInsights";

interface IApplicationInsightsResource {
    id: string;
    name: string;
    location: string;
    subscriptionId: string;
    resourceGroupName: string;
}

export function useApplicationInsights(props: IGetApplicationInsightsProps) {
    const { subscriptionId } = props;
    const queryResult = useQuery(
        ["applicationInsights", `/subscriptions/${subscriptionId}`],
        () => {
            return getApplicationInsights(props);
        },
        {
            staleTime: 1000 * 60 * 5,
            enabled: Boolean(subscriptionId)
        }
    );
    return queryResult;
}

export function useApplicationInsightsInSubscriptionContext() {
    const workspaceContext = useWorkspaceContext();
    const workspaceId = workspaceContext.workspace?.id;

    const { subscriptionId } = React.useMemo(() => {
        const parsedId = workspaceId ? ArmId.parse(workspaceId) : undefined;
        return {
            subscriptionId: parsedId?.subscription || ""
        };
    }, [workspaceId]);

    const { data, isLoading, isError, error } = useApplicationInsights({
        subscriptionId,
        options: {
            telemetry: {
                source: TraceSource.ApplicationInsightsDropdown
            }
        }
    });

    const extendedData = React.useMemo(() => {
        const extendedValues: IApplicationInsightsResource[] =  data?.content.value.map((ai) => {
            const parsedId = ArmId.parse(ai.id);
            return {
                ...ai,
                subscriptionId: parsedId?.subscription || "",
                resourceGroupName: parsedId?.resourceGroup || ""
            }
        }) || [];
        return {
            ...data,
            content: {
                ...data?.content,
                value: extendedValues
            }
        };
    }, [data]);

    return {
        data: extendedData,
        error,
        isLoading,
        isError
    };
}
