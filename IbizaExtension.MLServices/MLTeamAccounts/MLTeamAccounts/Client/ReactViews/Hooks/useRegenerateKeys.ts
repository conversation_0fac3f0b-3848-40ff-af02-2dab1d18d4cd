import * as React from "react";
import { AssetType_AIStudio_KeysAndEndpoints_Command as ClientResources_AssetType_AIStudio_KeysAndEndpoints_Command } from "../Resx/ClientResources.resjson";
import { CommandBarItem as ICommandBarItemProps } from "@microsoft/azureportal-reactview/CommandBar";

export const useRegenerateKeys = (): {
    commandBarItems: ICommandBarItemProps[];
    regenerateKey1(): Promise<void>;
    regenerateKey2(): Promise<void>;
} => {
    // TODO: Implement regenerate keys when workspace RP supports it
    const regenerateKey = React.useCallback(async (key: "one" | "two") => {
        console.log("Regenerate key " + key);
        return;
    }, []);
    const regenerateKey1 = React.useCallback(() => regenerateKey("one"), []);
    const regenerateKey2 = React.useCallback(() => regenerateKey("two"), []);

    const regenerateKey1Button = React.useMemo<ICommandBarItemProps>(() => {
        return {
            key: "regenerateKey1",
            title: ClientResources_AssetType_AIStudio_KeysAndEndpoints_Command.regenKey1,
            name: ClientResources_AssetType_AIStudio_KeysAndEndpoints_Command.regenKey1,
            onClick: () => {
                regenerateKey1()
            }
        }
    }, [regenerateKey1]);

    const regenerateKey2Button = React.useMemo<ICommandBarItemProps>(() => {
        return {
            key: "regenerateKey2",
            title: ClientResources_AssetType_AIStudio_KeysAndEndpoints_Command.regenKey2,
            name: ClientResources_AssetType_AIStudio_KeysAndEndpoints_Command.regenKey2,
            onClick: () => {
                regenerateKey2()
            }
        }
    }, [regenerateKey2]);

    const items = React.useMemo(() => [regenerateKey1Button, regenerateKey2Button], [regenerateKey1Button, regenerateKey2Button]);

    return {
        commandBarItems: items,
        regenerateKey1,
        regenerateKey2
    }
}
