import { getKeyNameFromUrl, getKeyVersionFromUrl, getVaultNameFromId, getHttpStatusCode, extractHttpClientErrorMessage, dedupe, getLinkForOffer } from "../../Shared/ViewAgnostic/Utilities";

describe("getHttpStatusCode", () => {
  it("returns undefined for undefined input", () => {
    expect(getHttpStatusCode(undefined)).toBe(undefined);
  });

  it("returns status when object has valid status property as number", () => {
    expect(getHttpStatusCode({
      status: 200
    })).toBe(200);
  });

  it("returns status when object has valid status property as string", () => {
    expect(getHttpStatusCode({ status: "200" })).toBe(200);
  });

  it("returns undefined when object has invalid status property", () => {
    expect(getHttpStatusCode({ status: Infinity })).toBe(undefined);
    expect(getHttpStatusCode({ status: "hello" })).toBe(undefined);
  });
});

describe("extractHttpClientErrorMessage", () => {
  it("returns undefined for undefined input", () => {
    expect(extractHttpClientErrorMessage(undefined)).toBe(undefined);
  });

  it("returns undefined when error is not an object", () => {
    expect(extractHttpClientErrorMessage("error")).toBe(undefined);
  });

  it("returns undefined when error does not have responseText or content property", () => {
    expect(extractHttpClientErrorMessage({})).toBe(undefined);
  });

  it("returns undefined when responseText is not a string", () => {
    expect(extractHttpClientErrorMessage({ responseText: 123 })).toBe(undefined);
  });

  it("throws an error when responseText is a string which cannot be parsed as JSON", () => {
    expect(() => extractHttpClientErrorMessage({ responseText: "invalid" })).toThrow();
  });

  it("returns undefined when responseText JSON does not have error.message property", () => {
    expect(extractHttpClientErrorMessage({ responseText: "{}" })).toBe(undefined);
  });

  it("returns the error message when responseText JSON has error.message property", () => {
    expect(extractHttpClientErrorMessage({ responseText: '{"error":{"message":"test"}}' })).toBe("test");
  });

  it ("only checks for responseJSON when responseText is present", () => {
    expect(extractHttpClientErrorMessage({ responseJSON: { error: { message: "test" } } })).toBe(undefined);
  });

  it("returns the error message when responseJSON has error.message property and responseText is invalid", () => {
    expect(extractHttpClientErrorMessage({ responseText: "invalid", responseJSON: { error: { message: "test" } } })).toBe("test");
  });

  it("returns the error message when responseJSON has error.message property and responseText is valid", () => {
    expect(extractHttpClientErrorMessage({ responseText: '{"error":{"message":"test1"}}', responseJSON: { error: { message: "test2" } } })).toBe("test2");
  });

  it("returns the error message when content object has error.message property", () => {
    expect(extractHttpClientErrorMessage({ content: { error: { message: "test" } } })).toBe("test");
  });
});

describe("getVaultNameFromId", () => {
  it("returns undefined for undefined input", () => {
    expect(getVaultNameFromId(undefined)).toBe(undefined);
  });
  it("returns the last element ", () => {
    expect(getVaultNameFromId("root")).toBe("root");
    expect(getVaultNameFromId("root/seg1")).toBe("seg1");
    expect(getVaultNameFromId("root/seg1/seg2")).toBe("seg2");
  });
});

describe("getKeyNameFromUrl", () => {
  it("returns undefined for undefined input", () => {
    expect(getKeyNameFromUrl(undefined)).toBe(undefined);
  });

  it("returns the second to last element when input has at least three elements", () => {
    expect(getKeyNameFromUrl("root/seg1/seg2")).toBe("seg1");
    expect(getKeyNameFromUrl("root/seg1/seg2/seg3")).toBe("seg2");
    expect(getKeyNameFromUrl("root/seg2/seg2/seg3/seg4")).toBe("seg3");
  });

  it("returns undefined when input has less than three elements", () => {
    expect(getKeyNameFromUrl("root")).toBe(undefined);
    expect(getKeyNameFromUrl("root/seg1")).toBe(undefined);
  });
});

describe("getKeyVersionFromUrl", () => {
  it("returns undefined for undefined input", () => {
    expect(getKeyVersionFromUrl(undefined)).toBe(undefined);
  });

  it("returns undefined when input has less than two elements", () => {
    expect(getKeyVersionFromUrl("root")).toBe(undefined);
  });

  it("returns the last element when input has at least two elements", () => {
    expect(getKeyVersionFromUrl("root/seg1")).toBe("seg1");
    expect(getKeyVersionFromUrl("root/seg1/seg2")).toBe("seg2");
    expect(getKeyVersionFromUrl("root/seg1/seg2/seg3")).toBe("seg3");
  });
});

describe("dedupe", () => {
  it("returns an empty array for an empty input array", () => {
    expect(dedupe([])).toEqual([]);
  });

  it("returns the same array when there are no duplicate values", () => {
    const input = ["a", "b", "c"];
    expect(dedupe(input)).toEqual(input);
  });

  it("removes duplicate values from the input array", () => {
    const input = ["a", "b", "a", "c", "b"];
    expect(dedupe(input)).toEqual(["a", "b", "c"]);
  });

  it("removes empty strings", () => {
    const input = ["a", "", "b", "", "a"];
    expect(dedupe(input)).toEqual(["a", "b"]);
  });

  it("returns offer links", () => {
    expect(getLinkForOffer(["meta-llama-2-70b-offer"])).toEqual("https://aka.ms/aistudio/landing/Llama-2-70b");
    expect(getLinkForOffer(["meta-llama-2-70b-chat"])).toEqual("https://aka.ms/aistudio/landing/Llama-2-70b-chat");
    expect(getLinkForOffer(["meta-llama-2-13b-offer"])).toEqual("https://aka.ms/aistudio/landing/Llama-2-13b");
    expect(getLinkForOffer(["meta-llama-2-13b-chat"])).toEqual("https://aka.ms/aistudio/landing/Llama-2-13b-chat");
    expect(getLinkForOffer(["llama-2-7b-offer"])).toEqual("https://aka.ms/aistudio/landing/Llama-2-7b");
    expect(getLinkForOffer(["llama-2-7b-chat"])).toEqual("https://aka.ms/aistudio/landing/Llama-2-7b-chat");
    expect(getLinkForOffer(["mistral-large-offer"])).toEqual("https://aka.ms/aistudio/landing/mistral-large");
  });
})

