{"compileOnSave": true, "extends": "@microsoft/azureportal-build/tsconfig.webpack.json", "emitDecoratorMetadata": true, "compilerOptions": {"allowJs": true, "module": "esnext", "strict": true, "resolveJsonModule": true, "baseUrl": ".", "paths": {"*": ["*"]}, "types": ["jest", "@microsoft/azureportal-build"], "isolatedModules": false, "moduleResolution": "node"}, "include": ["**/*", "../_extensions/**/*.d.ts", "../_generated/ForReact/**/*"], "exclude": ["test-config/*"]}