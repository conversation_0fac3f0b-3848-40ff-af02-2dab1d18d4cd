{
  "$schema": "../node_modules/@microsoft/azureportal-build/lib/definitions/dx.schema.json",
  "area": "EndpointResource",
  "stringSource": "../Resx/ClientResources.resjson",
  "assetType": {
    "name": "OnlineEndpoint",
    "displayNames": {
      "singular": "AssetTypeNames_MLApp.singular",
      "plural": "AssetTypeNames_MLApp.plural",
      "lowerSingular": "AssetTypeNames_MLApp.lowerSingular",
      "lowerPlural": "AssetTypeNames_MLApp.lowerPlural"
    },
    "viewModel": {
      "name": "OnlineEndpointViewModel",
      "module": "../../EndpointResource/OnlineEndpointViewModel"
    },
    "icon": {
      "file": "../Content/SVG/ml_services_icon.svg"
    },
    "part": "OnlineEndpointPart",
    "blade": "OnlineEndpointOverviewBlade",
    "create": {
      "blade": {
        "name": "CreateOnlineEndpointBlade",
        "extension": "Microsoft_Azure_MLTeamAccounts",
      }
    },
    "permissions": [
      {
        "name": "readServices",
        "action": "Microsoft.MachineLearningServices/workspaces/onlineEndpoints/read"
      },
      {
        "name": "moveServices",
        "action": "Microsoft.MachineLearningServices/resourceGroups/moveResources"
      }
    ],
    "preview": true,
    "keywords": "AssetType.keywords",
    "resourceMenu": {
      "staticOverview": true,
      "resourceProvidedBy": "ProvidedByResourceMenu"
    },
    "browse": {
      "type": "ResourceType",
      "query": {
        "file": "./BrowseQuery.kml"
      },
      "defaultColumns": [
        "FxColumns.ResourceGroup",
        "FxColumns.Location",
        "FxColumns.Subscription"
      ],
      "customConfig": {
        "useSupplementalData": true
      }
    },
    "description": "AssetType.description",
    "options": [
      "HideAssetType"
    ],
    "resourceType": {
      "name": "Microsoft.MachineLearningServices/workspaces/onlineEndpoints",
      "apiVersion": "2021-10-01"
    }
  }
}