import { AssetType_AIStudio_KeysAndEndpoints as ClientResources_AssetType_AIStudio_KeysAndEndpoints } from "../../Resx/ClientResources.resjson";
import { LogArea, TraceSource } from "../../Logging/Logging.types";
import { getResource } from "./KeysAndEndpoints.DataFetcher";
import { IKeysAndEndpointsProps } from "./KeysAndEndpoints.types";
import { View } from "../../CommonComponents/View";
import { KeysAndEndpoints as KeysAndEndpointsComponent } from "../../CommonComponents/KeysAndEndpoints"
// import { useRegenerateKeys } from "../../Hooks/useRegenerateKeys";

const KeysAndEndpoints = (props: IKeysAndEndpointsProps) => {
    // const { commandBarItems, regenerateKey1, regenerateKey2 } = useRegenerateKeys();

    return (
        <View
            title={ClientResources_AssetType_AIStudio_KeysAndEndpoints.title}
            workspaceId={props.parameters.id}
            dataFetcher={getResource}
            telemetryErrorArea={LogArea.KeysAndEndpoints}
            telemetryTraceSource={TraceSource.KeysAndEndpoints}
        >
            {/* <CommandBar
                items={commandBarItems}
                styles={{
                    root: {
                        padding: 0
                    }
                }}
            /> */}
            <KeysAndEndpointsComponent telemetrySource={TraceSource.KeysAndEndpoints} />
        </View>
    );
};

export default KeysAndEndpoints;
