import { getOrAdd } from "@microsoft/azureportal-reactview/DataCache";

import { getWorkspace } from "../../Queries/getWorkspace";
import { TraceSource, TraceAction } from "../../Logging/Logging.types";
import { IKeysAndEndpointsProps } from "./KeysAndEndpoints.types";

export async function getResource(resourceId: string) {
    return getOrAdd(`resource-${resourceId}`, () => getWorkspace(resourceId, {
        telemetry: {
            source: TraceSource.KeysAndEndpoints,
            action: TraceAction.LoadWorkspace
        }
    }));
}

export async function fetchData(viewParameters: IKeysAndEndpointsProps) {
    return getResource(viewParameters.parameters.id);
}
