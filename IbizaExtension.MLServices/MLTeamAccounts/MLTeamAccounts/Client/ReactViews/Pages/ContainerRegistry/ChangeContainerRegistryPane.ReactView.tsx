import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ElementType, Spinner } from "@fluentui/react";
import { shorthands, makeStyles, MessageBar, MessageBarBody, MessageBarTitle } from "@fluentui/react-components";
import { useId } from "@fluentui/react-hooks";
import * as React from "react";
import {
    AssociatedResource_ContainerRegistry_ChangeBlade as ClientResources_AssociatedResource_ContainerRegistry_ChangeBlade,
    AssociatedResource_ContainerRegistry_ReactView_ChangeContainerRegistry as ClientResources_AssociatedResource_ContainerRegistry_ReactView_ChangeContainerRegistry,
} from "../../Resx/ClientResources.resjson";
import * as Az from "@microsoft/azureportal-reactview/Az";
import { View } from "../../CommonComponents/View";
import { useWorkspaceContext } from "../../CommonComponents/WorkspaceContext";
import { Log<PERSON><PERSON>, TraceSource } from "../../Logging/Logging.types";
import { updateWorkspaceContainerRegistry } from "../../Queries/updateWorkspace";
import { handleResponseFailures } from "../../Queries/queryUtils";
import { getResource } from "./ChangeContainerRegistryPane.DataFetcher";
import { IChangeContainerRegistryProps } from "./ChangeContainerRegistryPane.types";

const LazyContainerRegistryDropdown = React.lazy(() => import("../../CommonComponents/ContainerRegistryDropdown").then(md => ({ default: md.ContainerRegistryDropdown })));

const useStyles = makeStyles({
    root: {
        display: "flex",
        flexGrow: 1,
        justifyContent: "space-between",
        flexDirection: "column",
        ...shorthands.gap("2px"),
    },
    form: {
        display: "flex",
        flexDirection: "column",
        ...shorthands.gap("2px")
    },
    footer: {
        display: "flex",
        flexDirection: "row",
        ...shorthands.gap("8px")
    }
});

const ChangeContainerRegistryPane = ({ parameters, closeView }: IChangeContainerRegistryProps) => {
    const onDismiss = React.useCallback((containerRegistryId?: string) => {
        closeView?.(containerRegistryId);
    }, []);

    return (
        <View
            title={ClientResources_AssociatedResource_ContainerRegistry_ChangeBlade.title}
            workspaceId={parameters.id}
            dataFetcher={getResource}
            telemetryErrorArea={LogArea.ChangeContainerRegistryPane}
            telemetryTraceSource={TraceSource.ChangeContainerRegistryPane}
            fullHeight={true}
        >
            <ChangeContainerRegistry workspaceId={parameters.id} onDismiss={onDismiss} />
        </View>
    );
}

const ChangeContainerRegistry: React.FC<{ workspaceId: string, onDismiss(containerRegistryId?: string): void }> = ({ workspaceId, onDismiss }) => {
    const { workspace } = useWorkspaceContext();
    const [containerRegistryId, setContainerRegistryId] = React.useState<string | undefined>(workspace?.properties.containerRegistry);
    const id = useId("containerRegistryDropdown");
    const styles = useStyles();
    const [message, setMessage] = React.useState<string | undefined>();
    const [messageDetails, setMessageDetails] = React.useState<string | undefined>();
    const [isSaving, setIsSaving] = React.useState(false);

    const onSave = React.useCallback(async () => {
        if (!containerRegistryId) {
            return onDismiss();
        }
        setMessage(undefined);
        setMessageDetails(undefined);
        try {
            setIsSaving(true);
            await updateWorkspaceContainerRegistry(workspaceId, { containerRegistryId: containerRegistryId }, { telemetry: { source: TraceSource.ChangeContainerRegistryPane } });
            return onDismiss(containerRegistryId);
        } catch (ex) {
            setIsSaving(false);
            handleResponseFailures(ex, {
                errorLogArea: LogArea.ChangeContainerRegistryPane,
                handlers: {
                    handleFailureResponse: (responseError) => {
                        if (responseError.isUnauthorized) {
                            setMessage(ClientResources_AssociatedResource_ContainerRegistry_ReactView_ChangeContainerRegistry.unauthorizedError);
                            return true;
                        }
                        if (responseError.isNotFound) {
                            setMessage(ClientResources_AssociatedResource_ContainerRegistry_ReactView_ChangeContainerRegistry.notFoundError);
                            return true;
                        }
                        if (responseError.isUserError) {
                            setMessage(ClientResources_AssociatedResource_ContainerRegistry_ReactView_ChangeContainerRegistry.error);
                            setMessageDetails(responseError.message);
                            Az.log([
                                {
                                    timestamp: Date.now(),
                                    level: Az.LogEntryLevel.Warning,
                                    message: responseError.message || "",
                                    area: TraceSource.ChangeContainerRegistryPane,
                                    code: responseError.httpStatusCode,
                                    args: [responseError]
                                }
                            ])
                            return true;
                        }
                    },
                    handleError: (error) => {
                        setMessage(ClientResources_AssociatedResource_ContainerRegistry_ReactView_ChangeContainerRegistry.error);
                        Az.log([
                            {
                                timestamp: Date.now(),
                                level: Az.LogEntryLevel.Error,
                                message: error.message || "",
                                area: TraceSource.ChangeContainerRegistryPane,
                                code: error.httpStatusCode,
                                args: [error]
                            }
                        ])
                    }
                }
            })
        }
    }, [workspaceId, containerRegistryId, onDismiss]);

    return (
        <section className={styles.root}>
            <div className={styles.form}>
                {message && (
                    <MessageBar intent="error">
                        <MessageBarBody>
                            <MessageBarTitle>{ClientResources_AssociatedResource_ContainerRegistry_ReactView_ChangeContainerRegistry.errorTitle}</MessageBarTitle>
                            <br />
                            {message}
                            {messageDetails && (
                                <>
                                    <br />
                                    {messageDetails}
                                </>
                            )}
                        </MessageBarBody>
                    </MessageBar>
                )}
                <Label htmlFor={id}>{ClientResources_AssociatedResource_ContainerRegistry_ReactView_ChangeContainerRegistry.containerRegistryLabel}</Label>
                <React.Suspense fallback={<Shimmer isDataLoaded={false} shimmerElements={[{ type: ShimmerElementType.circle }]} />}>
                    <LazyContainerRegistryDropdown id={id} onOptionSelect={setContainerRegistryId} value={containerRegistryId} inlinePopup={true} />
                </React.Suspense>
            </div>
            <footer className={styles.footer}>
                <PrimaryButton disabled={(!containerRegistryId) || (containerRegistryId && containerRegistryId === workspace?.properties.containerRegistry) || isSaving} onClick={() => {
                    onSave();
                }}>{ClientResources_AssociatedResource_ContainerRegistry_ReactView_ChangeContainerRegistry.footerSave}</PrimaryButton>
                <DefaultButton disabled={isSaving} onClick={() => {
                    onDismiss();
                }}>{ClientResources_AssociatedResource_ContainerRegistry_ReactView_ChangeContainerRegistry.footerCancel}</DefaultButton>
                {isSaving && <Spinner label={ClientResources_AssociatedResource_ContainerRegistry_ReactView_ChangeContainerRegistry.containerRegistryUpdatingSpinner} ariaLive="assertive" labelPosition="left" />}
            </footer>
        </section>
    )
}

export default ChangeContainerRegistryPane;
