import { getOrAdd } from "@microsoft/azureportal-reactview/DataCache";
import { TraceSource, TraceAction } from "../../Logging/Logging.types";
import { getWorkspace } from "../../Queries/getWorkspace";
import { IChangeContainerRegistryProps } from "./ChangeContainerRegistryPane.types";

export async function getResource(resourceId: string) {
    return getOrAdd(`resource-${resourceId}`, () => getWorkspace(resourceId, {
        telemetry: {
            source: TraceSource.ChangeAppInsightsPane,
            action: TraceAction.LoadWorkspace
        }
    }));
}

export async function fetchData(viewParameters: IChangeContainerRegistryProps) {
    return getResource(viewParameters.parameters.id);
}
