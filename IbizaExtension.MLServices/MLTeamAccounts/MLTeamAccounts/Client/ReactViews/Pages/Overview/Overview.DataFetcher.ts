import { getOrAdd } from "@microsoft/azureportal-reactview/DataCache";

import { IOverviewProps } from "./Overview.types"
import { getWorkspace } from "../../Queries/getWorkspace"
import { TraceAction, TraceSource } from "../../Logging/Logging.types";

export async function getResource(resourceId: string) {
    return getOrAdd(`resource-${resourceId}`, () => getWorkspace(resourceId, {
        telemetry: {
            source: TraceSource.Overview,
            action: TraceAction.LoadWorkspace
        }
    }))
}

export async function fetchData(viewParameters: IOverviewProps){
    return getResource(viewParameters.parameters.id);
}
