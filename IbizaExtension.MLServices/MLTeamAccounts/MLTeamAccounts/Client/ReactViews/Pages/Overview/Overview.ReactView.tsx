import * as Az from "@microsoft/azureportal-reactview/Az";
import { Stack, StackItem } from "@fluentui/react";
import { Essentials } from "@microsoft/azureportal-reactview/Essentials";
import * as React from "react";
import { View } from "../../CommonComponents/View";
import { useWorkspaceContext } from "../../CommonComponents/WorkspaceContext";
import { KeysAndEndpoints } from "../../CommonComponents/KeysAndEndpoints";
import { LogArea, TraceSource } from "../../Logging/Logging.types";
import { getResource } from "./Overview.DataFetcher";
import { IOverviewProps } from "./Overview.types";
import { OverviewCommandBar } from "./OverviewCommandBar";
import { useEssentialFields } from "./useEssentialFields";
import { WorkspaceBanner } from "./WorkspaceBanner";

const INCLUDE_KEYS_AND_ENDPOINTS = false;

const OverviewContent: React.FC = () => {
    const { workspace } = useWorkspaceContext();
    const [workspaceState, setWorkspaceState] = React.useState(workspace);
    const [openAppInsightsPanel, setOpenAppInsightsPanel] = React.useState<boolean>(false);
    const [openContainerRegistryPanel, setOpenContainerRegistryPanel] = React.useState<boolean>(false);
    const onAppInsightsEditClick = React.useCallback(() => {
        setOpenAppInsightsPanel(true);
    }, []);
    const onContainerRegistryEditClick = React.useCallback(() => {
        setOpenContainerRegistryPanel(true);
    }, []);

    React.useEffect(() => {
        if (openAppInsightsPanel) {
            Az.openContextPane({
                bladeName: "ChangeAppInsightsPane.ReactView",
                extensionName: "Microsoft_Azure_MLTeamAccounts",
                parameters: {
                    id: workspaceState?.id
                },
                onClosed: async (...args) => {
                    setOpenAppInsightsPanel(false);
                    const updatedAppInsightsId = (args as any[])[1];
                    if (updatedAppInsightsId && workspaceState?.properties) {
                        setWorkspaceState({
                            ...workspaceState,
                            properties: {
                                ...workspaceState.properties,
                                applicationInsights: updatedAppInsightsId
                            }
                        });
                    }
                }
            });
        }
    }, [openAppInsightsPanel, workspaceState?.id]);

    React.useEffect(() => {
        if (openContainerRegistryPanel) {
            Az.openContextPane({
                bladeName: "ChangeContainerRegistryPane.ReactView",
                extensionName: "Microsoft_Azure_MLTeamAccounts",
                parameters: {
                    id: workspaceState?.id
                },
                onClosed: async (...args) => {
                    setOpenContainerRegistryPanel(false);
                    const updatedContainerRegistryId = (args as any[])[1];
                    if (updatedContainerRegistryId && workspaceState?.properties) {
                        setWorkspaceState({
                            ...workspaceState,
                            properties: {
                                ...workspaceState.properties,
                                containerRegistry: updatedContainerRegistryId
                            }
                        });
                    }
                }
            });
        }
    }, [openContainerRegistryPanel, workspaceState?.id]);

    const { fields, customizeResourceFields } = useEssentialFields(workspaceState, onAppInsightsEditClick, onContainerRegistryEditClick);

    return (
        <>
            {workspace && <OverviewCommandBar workspace={workspace} />}
            <Stack>
                <StackItem>
                    <Essentials fields={fields} resourceId={workspaceState?.id} customizeResourceFields={customizeResourceFields} hideTags={true} />
                </StackItem>
                <StackItem>{workspaceState && <WorkspaceBanner workspace={workspaceState} />}</StackItem>
                {INCLUDE_KEYS_AND_ENDPOINTS && (
                    <StackItem>
                        <KeysAndEndpoints telemetrySource={TraceSource.Overview} header="h3" />
                    </StackItem>
                )}
            </Stack>
        </>
    );
};

export const Overview = (props: IOverviewProps) => {
    return (
        <View
            workspaceId={props.parameters.id}
            dataFetcher={getResource}
            telemetryErrorArea={LogArea.Overview}
            telemetryTraceSource={TraceSource.Overview}
        >
            <OverviewContent />
        </View>
    );
};

export default Overview;
