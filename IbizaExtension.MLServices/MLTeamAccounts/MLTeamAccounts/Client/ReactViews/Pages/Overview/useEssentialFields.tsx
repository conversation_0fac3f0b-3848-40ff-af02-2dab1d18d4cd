import * as React from "react";
import { Field, FieldColumn, ResourceField } from "@microsoft/azureportal-reactview/Essentials";
import * as Az from "@microsoft/azureportal-reactview/Az";
import { Link } from "@fluentui/react/lib/Link";
import {
    AssetType_AIStudio_Hub_Essentials_DefaultProjectResourceGroup as ClientResources_AssetType_AIStudio_Hub_Essentials_DefaultProjectResourceGroup,
    ClientResources,
    Overview_Essentials as ClientResources_Overview_Essentials,
} from "../../Resx/ClientResources.resjson";
import { pendingStringValue } from "../../../Shared/Constants";
import { WorkspaceMappedKind } from "../../../Shared/Enums";
import { dedupe, isAIStudioKind } from "../../../Shared/ViewAgnostic/Utilities";
import { ArmId } from "@microsoft/azureportal-reactview/ResourceManagement";
import { ResourceLink } from "@microsoft/azureportal-reactview/ResourceLink";
import { TraceSource, TraceAction } from "../../Logging/Logging.types";
import { WorkspaceWithID } from "../../workspaceUtils";
import { getOverviewLinkTargetByKind, useWorkspaceLink } from "../../Hooks/useWorkspaceLink";

const createField =
    (options: {
        isLoading?: boolean;
        isResourceId?: boolean;
        editFieldCall?: () => void;
        value?: string | string[];
        label: string;
        column: FieldColumn
    }): Field => {
        const { isLoading, column, label, isResourceId = true, editFieldCall, value } = options;

        const markedUpLabel = editFieldCall ? (
            <span style={{ display: "flex", flexDirection: "row", flexWrap: "wrap", gap: 5 }}>
                <span title={label}>{label}</span>
                <Link onClick={editFieldCall} href="#">
                    {`(${ClientResources_Overview_Essentials.edit})`}
                </Link>
            </span>
        ) : label;
        if (isLoading) {
            return {
                label: markedUpLabel,
                column,
                isDataLoaded: false,
                shimmer: true,
                value: pendingStringValue
            }
        }
        if (!value || (Array.isArray(value) && !value.length)) {
            return {
                label: markedUpLabel,
                column,
                value: pendingStringValue
            };
        }
        const values = Array.isArray(value) ? value : [value];
        return {
            label: markedUpLabel,
            column,
            value: (
                <>
                    {values.map((id, idx) => {
                        let displayValue: string | React.ReactNode = id;
                        if (isResourceId) {
                            const armId = ArmId.parse(id);
                            displayValue = (
                                <ResourceLink resourceId={id}>
                                    {armId.kind == ArmId.Kind.ResourceGroup ? armId.resourceGroup : armId.resourceName}
                                </ResourceLink>
                            );
                        }
                        return (
                            <React.Fragment key={id}>
                                {displayValue}
                                {idx < values.length - 1 && <span>,</span>}
                            </React.Fragment>
                        );
                    })}
                </>
            )
        };
    };

const getStudioLinkField = (options: { isDataLoaded: boolean, url: string | undefined; workspace: WorkspaceWithID; column: FieldColumn }): Field => {
    const { isDataLoaded, column, workspace, url } = options;
    return {
        label: ClientResources_Overview_Essentials.studioWebURLText,
        column,
        value: !url ? pendingStringValue : (
            <Link
                href={url}
                target="_blank"
                onClick={() => {
                    Az.trace([
                        {
                            action: TraceAction.LaunchPortalEssentialsLinkClicked,
                            source: TraceSource.Overview,
                            timestamp: Date.now(),
                            data: {
                                workspaceId: workspace?.id,
                                kind: workspace?.kind
                            }
                        }
                    ]);
                }}
            >
                {url}
            </Link>
        ),
        shimmer: true,
        isDataLoaded
    };
};

const getMLFlowField = (options: { mlFlowTrackingUri?: string; column: FieldColumn }): Field => {
    const { column, mlFlowTrackingUri } = options;
    return {
        label: ClientResources.labelMlFlowUri,
        column,
        value: mlFlowTrackingUri || pendingStringValue
    };
};

export const useEssentialFields = (
    workspace?: WorkspaceWithID,
    openAppInsightsPanel?: () => void,
    openContainerRegistryPanel?: () => void): { fields: Field[]; customizeResourceFields: (fields: Field[]) => any[] } => {

    const workspaceLink = useWorkspaceLink({
        workspace,
        linkTarget: getOverviewLinkTargetByKind(workspace?.kind)
    });

    const fields: Field[] = [];

    if (workspace?.kind) {
        const studioLinkField = getStudioLinkField({ isDataLoaded: workspaceLink.loaded, url: workspaceLink.url, workspace, column: FieldColumn.Right });
        switch (workspace?.kind) {
            case WorkspaceMappedKind.FeatureStore: {
                fields.push(studioLinkField);
                break;
            }
            case WorkspaceMappedKind.Hub: {
                fields.push(
                    ...[
                        createField({
                            value: workspace?.properties?.keyVault || ClientResources.managedKeyVault,
                            label: ClientResources.labelKeyVault,
                            column: FieldColumn.Left,
                            isResourceId: !!workspace?.properties?.keyVault
                        }),
                        createField({
                            value: workspace?.properties?.workspaceHubConfig?.defaultWorkspaceResourceGroup,
                            label: ClientResources_AssetType_AIStudio_Hub_Essentials_DefaultProjectResourceGroup.label,
                            column: FieldColumn.Right
                        }),
                        createField({
                            value: dedupe([workspace?.properties?.storageAccount || "", ...(workspace?.properties?.storageAccounts || [])]),
                            label: ClientResources.labelStorage,
                            column: FieldColumn.Right
                        }),
                        createField({
                            value: workspace?.properties?.containerRegistry,
                            label: ClientResources.labelRegistry,
                            column: FieldColumn.Right,
                            editFieldCall: openContainerRegistryPanel
                        }),
                        createField({
                            value: workspace?.properties?.applicationInsights,
                            label: ClientResources.labelInsights,
                            column: FieldColumn.Right,
                            editFieldCall: openAppInsightsPanel
                        }),
                        createField({
                            value: workspace?.properties?.provisioningState,
                            label: ClientResources.labelProvisioningState,
                            column: FieldColumn.Right,
                            isResourceId: false
                        })
                    ]
                );
                break;
            }
            case WorkspaceMappedKind.Project: {
                fields.push(
                    ...[
                        studioLinkField,
                        createField({
                            value: workspace?.properties?.hubResourceId,
                            label: ClientResources.labelProjectHubResource,
                            column: FieldColumn.Right
                        }),
                        createField({
                            value: workspace?.properties?.workspaceId,
                            isResourceId: false,
                            label: ClientResources_Overview_Essentials.projectID,
                            column: FieldColumn.Right
                        })
                    ]
                );
                break;
            }
            default: {
                fields.push(
                    ...[
                        createField({
                            value: dedupe([workspace?.properties?.storageAccount || "", ...(workspace?.properties?.storageAccounts || [])]),
                            label: ClientResources.labelStorage,
                            column: FieldColumn.Left
                        }),
                        studioLinkField,
                        createField({
                            value: workspace?.properties?.containerRegistry,
                            label: ClientResources.labelRegistry,
                            column: FieldColumn.Right
                        }),
                        createField({
                            value: workspace?.properties?.keyVault || ClientResources.managedKeyVault,
                            label: ClientResources.labelKeyVault,
                            column: FieldColumn.Right,
                            isResourceId: !!workspace?.properties?.keyVault
                        }),
                        createField({
                            value: workspace?.properties?.applicationInsights,
                            label: ClientResources.labelInsights,
                            column: FieldColumn.Right
                        }),
                        createField({
                            value: workspace?.properties?.provisioningState,
                            label: ClientResources.labelProvisioningState,
                            column: FieldColumn.Left,
                            isResourceId: false
                        }),
                        ...(workspace?.kind === WorkspaceMappedKind.Default ? [getMLFlowField({ mlFlowTrackingUri: workspace?.properties?.mlFlowTrackingUri, column: FieldColumn.Right })] : [])
                    ]
                );
            }
        }
    }

    /**
     * Customize the built in resource fields (remove "move" link by making immutable).
     */
    const customizeResourceFields = React.useCallback((fields: Field[]) => {
        const resourceFields: any[] = [
            {
                resourceField: ResourceField.ResourceGroup,
                immutable: true
            },
            {
                resourceField: ResourceField.Location,
                immutable: true
            },
            {
                resourceField: ResourceField.Subscription,
                immutable: true
            }
        ];
        if (workspace?.kind && isAIStudioKind(workspace?.kind)) {
            resourceFields.push({
                resourceField: ResourceField.SubscriptionId,
                immutable: true
            });
        }
        return resourceFields.concat(fields);
    }, [workspace?.kind]);

    return { fields, customizeResourceFields };
};
