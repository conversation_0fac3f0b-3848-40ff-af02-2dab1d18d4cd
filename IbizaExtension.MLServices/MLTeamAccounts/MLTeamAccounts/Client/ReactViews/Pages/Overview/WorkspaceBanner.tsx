import * as React from "react";
import * as Az from "@microsoft/azureportal-reactview/Az";
import { PrimaryButton, Text, ImageIcon, ImageFit, Stack, StackItem } from "@fluentui/react";
import {
    AssetType_AIStudio_Hub_Overview_Banner as ClientResources_AssetType_AIStudio_Hub_Overview_Banner,
    AssetType_AIStudio_Hub_Overview_Banner_Description as ClientResources_AssetType_AIStudio_Hub_Overview_Banner_Description,
    AssetType_AIStudio_Project_Overview_Banner as ClientResources_AssetType_AIStudio_Project_Overview_Banner,
    AssetType_AIStudio_Project_Overview_Banner_Description as ClientResources_AssetType_AIStudio_Project_Overview_Banner_Description,
    Overview_Banner as ClientResources_Overview_Banner,
    Overview_Banner_Description as ClientResources_Overview_Banner_Description,
} from "../../Resx/ClientResources.resjson";
import { getEnvironmentValue } from "@microsoft/azureportal-reactview/Environment";
import workbenchSvg from "../../../../Content/SVG/workbench_icon.svg";
import aIStudioSvg from "../../Content/SVG/ai_studio_icon_color.svg";
import { isAIStudioKind, shouldShowBannerForKind } from "../../../Shared/ViewAgnostic/Utilities"
import { TraceAction, TraceSource } from "../../Logging/Logging.types";
import { WorkspaceWithID } from "../../workspaceUtils";
import { LinkWithIcon } from "../../CommonComponents/LinkWithIcon";
import { DocsTemplate } from "../../CommonComponents/DocsTemplate";
import { WorkspaceMappedKind } from "../../../Shared/Enums";
import { getOverviewLinkTargetByKind, useWorkspaceLink } from "../../Hooks/useWorkspaceLink";

export interface IDefaultWorkspaceBannerProps {
    workspace: WorkspaceWithID;
}

const getTitle = (kind: WorkspaceMappedKind): string => {
    if (kind === WorkspaceMappedKind.Hub) {
        return ClientResources_AssetType_AIStudio_Hub_Overview_Banner.title;
    }
    if (kind === WorkspaceMappedKind.Project) {
        return ClientResources_AssetType_AIStudio_Project_Overview_Banner.title;
    }
    return ClientResources_Overview_Banner.title;
};

const getDescription = (kind: WorkspaceMappedKind): JSX.Element => {
    let docsUrl = getEnvironmentValue("machineLearningServicesDocumentationUrl");
    if (isAIStudioKind(kind)) {
        docsUrl = getEnvironmentValue("aiStudioModelCatalogUrl");
    }
    if (kind === WorkspaceMappedKind.Project) {
        return <Text>{ClientResources_AssetType_AIStudio_Project_Overview_Banner_Description.label}</Text>
    }

    const descriptionLabel = kind === "Hub" ? ClientResources_AssetType_AIStudio_Hub_Overview_Banner_Description.label : ClientResources_Overview_Banner_Description.label;
    const learnMoreText = kind === "Hub" ? ClientResources_AssetType_AIStudio_Hub_Overview_Banner_Description.learnMoreText : ClientResources_Overview_Banner_Description.learnMoreText
    return <DocsTemplate templateString={descriptionLabel} replacements={[
        <LinkWithIcon href={docsUrl}>{learnMoreText}</LinkWithIcon>
    ]} replacementString="{}" />;
};

const getButton = ({ kind, studioUrl }: { studioUrl?: string; workspaceId: string; kind: string }): JSX.Element | null => {
    if (!studioUrl) {
        return null;
    }

    let buttonText: string = ClientResources_Overview_Banner.launchButtonText;
    if (kind === WorkspaceMappedKind.Hub) {
        buttonText = ClientResources_AssetType_AIStudio_Hub_Overview_Banner.launchButtonText;
    }
    if (kind === WorkspaceMappedKind.Project) {
        buttonText = ClientResources_AssetType_AIStudio_Project_Overview_Banner.launchButtonText;
    }
    return (
        <PrimaryButton
            href={studioUrl}
            target="_blank"
            styles={{
                root: {
                    marginTop: "14px",
                    marginBottom: "10px"
                }
            }}
            onClick={() => {
                Az.trace([
                    {
                        action: TraceAction.LaunchPortalClicked,
                        source: TraceSource.Overview,
                        timestamp: Date.now(),
                        data: {
                            kind
                        }
                    }
                ]);
            }}
        >
            {buttonText}
        </PrimaryButton>
    );
};

interface ILayout {
    title: React.ReactNode;
    description: React.ReactNode;
    button: React.ReactNode;
}

const MLStudioLayout: React.FC<ILayout> = ({ title, description, button }) => {
    return <Stack horizontalAlign="center" wrap={false} styles={{ root: { padding: "24px 0" } }}>
        <StackItem>
            <ImageIcon
                imageProps={{
                    src: workbenchSvg,
                    imageFit: ImageFit.contain,
                    height: "100px",
                    width: "100px",
                    styles: {
                        root: {
                            padding: "20px 0"
                        },
                        image: {
                            filter: "grayscale(100%)",
                            opacity: 0.5
                        }
                    }
                }}
            />
        </StackItem>
        <StackItem>
            <Text
                as={"h3"}
                block={true}
                styles={{
                    root: {
                        fontSize: "20px",
                        fontWeight: 600,
                        lineHeight: "28px",
                        marginBottom: "8px",
                        marginTop: "16px"
                    }
                }}
            >
                {title}
            </Text>
        </StackItem>
        <StackItem>
            <Text
                block={true}
                styles={{
                    root: { maxWidth: "720px", textAlign: "center" }
                }}
            >
                {description}
            </Text>
        </StackItem>
        <StackItem>{button}</StackItem>
    </Stack>
}

const AIStudioLayout: React.FC<ILayout> = ({ button, description, title }) => {
    return <section>
        <header>
            <h3>{title}</h3>
        </header>
        <Stack horizontal={true}>
            <StackItem>
                <ImageIcon
                    imageProps={{
                        src: aIStudioSvg,
                        imageFit: ImageFit.contain,
                        height: "100px",
                        width: "100px"
                    }}
                />
            </StackItem>
            <StackItem styles={{
                root: { maxWidth: "720px" }
            }}>
                <div>{description}</div>
                {button}
            </StackItem>
        </Stack>
    </section>
}

export const WorkspaceBanner = ({ workspace }: IDefaultWorkspaceBannerProps): JSX.Element => {
    const kind = workspace.kind;
    const workspaceLink = useWorkspaceLink({
        workspace,
        linkTarget: getOverviewLinkTargetByKind(kind)
    });

    if (!shouldShowBannerForKind(kind)) {
        return <></>;
    }

    const title = getTitle(kind);
    const description = getDescription(kind);
    const button = getButton({ kind, studioUrl: workspaceLink.url, workspaceId: workspace.id });

    const Layout = isAIStudioKind(kind) ? AIStudioLayout : MLStudioLayout;

    return <Layout
        title={title}
        button={button}
        description={description}
    />;
};
