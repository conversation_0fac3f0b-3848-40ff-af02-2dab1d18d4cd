import * as Az from "@microsoft/azureportal-reactview/Az";
import { CommandBar, CommandBarItem as ICommandBarItemProps } from "@microsoft/azureportal-reactview/CommandBar";
import { Separator } from "@fluentui/react/lib/Separator"
import {
    MessageBar,
    MessageBarType
} from "@fluentui/react/lib/MessageBar";
import { ArmId } from "@microsoft/azureportal-reactview/ResourceManagement";
import * as React from "react";
import { ClientResources } from "../../Resx/ClientResources.resjson";
import { getHttpStatusCode } from "../../../Shared/ViewAgnostic/Utilities";
import { LogArea, TraceAction, TraceSource } from "../../Logging/Logging.types";
import { IPollerSettings, tryStringifyError } from "../../Queries/queryUtils";
import { useWorkspaceDeleteState, WorkspaceDeleteState } from "../../Hooks/useWorkspaceDeleteState";
import { WorkspaceWithID } from "../../workspaceUtils";
import { WorkspaceMappedKind } from "../../../Shared/Enums";
import { add, get } from "@microsoft/azureportal-reactview/DataCache";
import { beginDeleteHub } from "../../Queries/deleteHub";
import { useDeleteHubStatus } from "../../Hooks/useDeleteHubStatus";
import { UseQueryResult } from "react-query";
import { Ajax } from "@microsoft/azureportal-reactview/FxReactCommon.Modules";
import { DeleteHubStatusResult } from "Queries/getDeleteHubStatus";

function deleteInProgressMessage(): JSX.Element {
    return (
        <MessageBar messageBarType={MessageBarType.info} dismissButtonAriaLabel="Close">
            {ClientResources.progressMachineLearningServicesDeleteExecuting}
        </MessageBar>
    );
}
function deleteSucceededMessage(): JSX.Element {
    return (
        <MessageBar messageBarType={MessageBarType.success} dismissButtonAriaLabel="Close">
            {ClientResources.progressNotificationMachineLearningServicesDeleteSuccessTitle}
        </MessageBar>
    );
}
function deleteFailedMessage(errorMessage?: string): JSX.Element {
    return (
        <MessageBar messageBarType={MessageBarType.error} dismissButtonAriaLabel="Close" truncated={true} overflowButtonAriaLabel="See more">
            <b>{ClientResources.progressMachineLearningDeleteError}</b> {errorMessage}
        </MessageBar>
    );
}

function WorkspaceDeleteStatusBar(props: { deleteState: WorkspaceDeleteState }): JSX.Element | null {
    const { deleteState } = props;
    if (deleteState.initializing) {
        return null;
    }
    if (deleteState.error) {
        const errorMessage = tryStringifyError(deleteState.error);
        Az.log([{
            area: LogArea.deleteMachineLearningWorkspaceError,
            level: Az.LogEntryLevel.Error,
            timestamp: Date.now(),
            code: getHttpStatusCode(deleteState.error),
            message: `"Error deleting resource". ${errorMessage}`,
            args: [deleteState.error]
        }]);
        return deleteFailedMessage(errorMessage);
    }
    if (deleteState.pollingState === "running") {
        return deleteInProgressMessage();
    }
    if (deleteState.pollingState === "complete") {
        return deleteSucceededMessage();
    }
    return null;
}

function HubDeleteStatusBar(props: { hubResourceId: string, deleteHubStatusQuery: UseQueryResult<Ajax.BatchResponseItem<DeleteHubStatusResult>, unknown> }): JSX.Element | null {
    const { hubResourceId, deleteHubStatusQuery } = props;
    if (deleteHubStatusQuery.status === "idle") {
        return null;
    }
    if (deleteHubStatusQuery.isError) {
        const errorMessage = (deleteHubStatusQuery.error as Ajax.BatchResponseItem<DeleteHubStatusResult>)?.content?.responses?.find(response => response.id === hubResourceId)?.errorResponse?.error?.message;
        return deleteFailedMessage(errorMessage);
    }
    if (deleteHubStatusQuery.data?.content?.status === "Accepted") {
        return deleteInProgressMessage();
    }
    if (deleteHubStatusQuery.data?.content?.status === "Succeeded") {
        return deleteSucceededMessage();
    }
    return null;
}

const useDeleteButtonItem = (options: {
    workspace: WorkspaceWithID;
    disabled?: boolean;
    beginDelete?: (purge?: boolean) => void;
    deleteInitiated: () => void;
}): ICommandBarItemProps => {
    const { workspace, disabled, beginDelete, deleteInitiated } = options;
    const { id, name, kind } = workspace;
    const encryptionEnabled = workspace.properties?.encryption?.status === "Enabled";
    return React.useMemo(() => {
        const clickDefaultWorkspaceHandler = async () => {
            Az.openContextPane({
                bladeName: "MachineLearningServicesDeleteBlade",
                extensionName: "Microsoft_Azure_MLTeamAccounts",
                parameters: {
                    id,
                    workspaceName: name,
                    isCMKWorkspace: encryptionEnabled,
                    workspaceKind: workspace.kind
                },
                onClosed: async (_reason, returnData) => {
                    if (returnData?.confirmDelete) {
                        try {
                            await beginDelete?.(!!returnData.permanentlyDelete);
                        } catch { }
                    }
                }
            });
        };
        const clickHubHandler = async () => {
            const operationCacheKey = `${id}-deleteOperationId`;
            const dataCacheSettings = await get<IPollerSettings>(operationCacheKey, true);
            const dataCacheLocation = dataCacheSettings?.location;
            !!dataCacheLocation
                ? Az.openContextPane({
                    bladeName: "DeleteHubStatusPane.ReactView",
                    extensionName: "Microsoft_Azure_MLTeamAccounts",
                    parameters: {
                        id,
                        hubName: name
                    },
                })
                : Az.openContextPane({
                    bladeName: "DeleteHubPane.ReactView",
                    extensionName: "Microsoft_Azure_MLTeamAccounts",
                    parameters: { id },
                    onClosed: async (_reason, returnData) => {
                        if (returnData?.deleteResourcesList) {
                            const settings = await beginDeleteHub(id, returnData.deleteResourcesList, {
                                telemetry: {
                                    source: TraceSource.DeleteHubPane
                                }
                            });
                            add(operationCacheKey, Promise.resolve(settings));
                            deleteInitiated();
                            Az.trace([
                                {
                                    action: TraceAction.DeleteHubInitiated,
                                    source: TraceSource.DeleteHubStatusPane,
                                    timestamp: Date.now(),
                                    data: {
                                        hubResourceId: id,
                                        location: settings.location,
                                        resourcesCount: returnData.deleteResourcesList.length
                                    }
                                }
                            ]);
                            Az.openContextPane({
                                bladeName: "DeleteHubStatusPane.ReactView",
                                extensionName: "Microsoft_Azure_MLTeamAccounts",
                                parameters: {
                                    id,
                                    hubName: name
                                },
                            });
                        }
                    }
                });
        };
        return {
            key: "deleteWorkspace",
            name: ClientResources.commandDelete,
            disabled,
            iconProps: {
                iconName: "Delete"
            },
            onClick: () => {
                kind === WorkspaceMappedKind.Hub ? clickHubHandler() : clickDefaultWorkspaceHandler();
            }
        };
    }, [id, name, kind, encryptionEnabled, disabled, beginDelete]);
};

const useDownloadConfigItem = (linkRef: React.RefObject<HTMLAnchorElement>): ICommandBarItemProps => {
    return React.useMemo(() => {
        const onClick = () => {
            linkRef.current?.click();
        };
        return {
            key: "downloadConfig",
            name: ClientResources.commandDownloadConfig,
            title: ClientResources.commandDownloadConfigTooltip,
            iconProps: {
                iconName: "Download"
            },
            onClick
        };
    }, [linkRef]);
};

export interface IOverviewCommandBarProps {
    workspace: WorkspaceWithID;
}

const useCreateProject = (workspace: WorkspaceWithID): ICommandBarItemProps => {
    const { id } = workspace;
    return React.useMemo(() => {
        const onClick = () => {
            Az.openBlade({
                bladeName: "CreateAIStudioProjectBlade",
                extensionName: "Microsoft_Azure_MLTeamAccounts",
                parameters: { hubResourceId: id }
            });
        }
        return {
            key: "createProject",
            name: ClientResources.commandCreateProject,
            doesProvisioning: true,
            iconProps: {
                iconName: "Add"
            },
            onClick
        }
    }, [id])
};

export const OverviewCommandBar = (props: IOverviewCommandBarProps): JSX.Element => {
    const { workspace } = props;
    const [deleteInitiated, setDeleteInitiated] = React.useState(false);
    const isHub = workspace.kind === WorkspaceMappedKind.Hub;
    const workspaceDeleteState = useWorkspaceDeleteState(props.workspace.id, isHub, {
        telemetry: {
            source: TraceSource.Overview
        }
    });
    const deleteHubStatusQuery = useDeleteHubStatus({ hubResourceId: workspace.id, deleteInitiated, telemetry: { source: TraceSource.Overview } });

    const workspaceConfigDataUrl = React.useMemo(() => {

        const workspaceArmId = ArmId.parse(workspace.id);
        const workspaceConfig = {
            subscription_id: workspaceArmId.subscription,
            resource_group: workspaceArmId.resourceGroup,
            workspace_name: workspaceArmId.resourceName
        };
        return `data:application/json,${encodeURIComponent(JSON.stringify(workspaceConfig, null, 4))}`;
    }, [workspace.id]);

    const linkRef = React.useRef<HTMLAnchorElement>(null);
    const downloadConfigButton = useDownloadConfigItem(linkRef);
    const deleteButton = useDeleteButtonItem({
        workspace,
        disabled: isHub
            ? false
            : workspaceDeleteState.initializing || workspaceDeleteState.pollingState === "running" || workspaceDeleteState.pollingState === "complete",
        beginDelete: workspaceDeleteState.beginDelete,
        deleteInitiated: () => { setDeleteInitiated(true) }
    });
    const createProjectButton = useCreateProject(workspace);

    const items = React.useMemo(() => {
        const commandBarButtons: ICommandBarItemProps[] =[];
        if (workspace.kind === WorkspaceMappedKind.Hub) {
            commandBarButtons.push(createProjectButton);
        }
        commandBarButtons.push(downloadConfigButton);
        commandBarButtons.push(deleteButton);
        return commandBarButtons
    }, [createProjectButton, downloadConfigButton, deleteButton]);
    return (
        <>
            <CommandBar
                items={items}
                styles={{
                    root: {
                        padding: 0
                    }
                }}
            />
            <Separator styles={{ root: { lineHeight: 0, margin: 0, padding: 0 } }} />
            {!isHub && <WorkspaceDeleteStatusBar deleteState={workspaceDeleteState} /> }
            {isHub && <HubDeleteStatusBar hubResourceId={workspace.id} deleteHubStatusQuery={deleteHubStatusQuery} /> }
            <a ref={linkRef} href={workspaceConfigDataUrl} style={{ display: "none", visibility: "hidden" }} target="_blank" download="config.json" />
        </>
    );
};
