import { Provisioning } from "@microsoft/azureportal-reactview/Provisioning";
import {
    AssetType_Nixtla_Page as ClientResources_AssetType_Nixtla_Page,
    AssetType_Nixtla_Page_Description as ClientResources_AssetType_Nixtla_Page_Description,
} from "../../Resx/ClientResources.resjson";
import { setTitle } from "@microsoft/azureportal-reactview/Az";
import { getEnvironmentValue } from "@microsoft/azureportal-reactview/Environment";
import { ModelProxyLanding } from "../../CommonComponents/ModelProxyLanding"
import { createDynamicOfferLink, getLinkForOffer } from "../../../Shared/ViewAgnostic/Utilities";

interface INixtlaAssetProps {
    parameters?: {
        _provisioningContext?: Provisioning;
        offerId?: string;
    }
}

const NixtlaAsset = (props?: INixtlaAssetProps) => {
    const { parameters } = props || {};
    const url = getEnvironmentValue("aiStudioDocumentationUrl");
    const linkUrl = getLinkForOffer([
        parameters?.offerId,
        (parameters?._provisioningContext?.marketplaceItem as any)?.id,
        (parameters?._provisioningContext?.marketplaceItem as any)?.uniqueProductId
    ]) || createDynamicOfferLink(parameters?.offerId) || url;
    setTitle(ClientResources_AssetType_Nixtla_Page.title);
    return (
        <ModelProxyLanding
        descriptionIntro={ClientResources_AssetType_Nixtla_Page_Description.intro}
        descriptionBody={ClientResources_AssetType_Nixtla_Page_Description.body}
        buttonText={ClientResources_AssetType_Nixtla_Page.primaryButton}
        url={linkUrl}
        />
    );
};

export default NixtlaAsset;
