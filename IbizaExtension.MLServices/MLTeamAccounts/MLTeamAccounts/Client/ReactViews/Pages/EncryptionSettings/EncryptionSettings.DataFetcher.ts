import { getOrAdd } from "@microsoft/azureportal-reactview/DataCache";

import { getWorkspace } from "../../Queries/getWorkspace";
import { TraceSource, TraceAction } from "../../Logging/Logging.types";
import { IEncryptionSettingsProps } from "./EncryptionSettings.types";

export async function getResource(resourceId: string) {
    return getOrAdd(`resource-${resourceId}`, () => getWorkspace(resourceId, {
        telemetry: {
            source: TraceSource.EncryptionSettings,
            action: TraceAction.LoadWorkspace
        }
    }));
}

export async function fetchData(viewParameters: IEncryptionSettingsProps) {
    return getResource(viewParameters.parameters.id);
}
