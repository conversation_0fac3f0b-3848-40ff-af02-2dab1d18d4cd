import * as Az from "@microsoft/azureportal-reactview/Az";
import { CopyableLabel } from "@microsoft/azureportal-reactview/CopyableLabel";
import { BladeLink } from "@microsoft/azureportal-reactview/BladeLink";
import { ArmId } from "@microsoft/azureportal-reactview/ResourceManagement";
import { Stack, StackItem } from "@fluentui/react/lib/Stack";
import {
    MessageBar,
    MessageBarType
} from "@fluentui/react/lib/MessageBar";
import {
    CommandBar,
    ICommandBarItemProps
} from "@fluentui/react/lib/CommandBar";
import { Text } from "@fluentui/react/lib/Text";
import { Link } from "@fluentui/react/lib/Link";
import { Label } from "@fluentui/react/lib/Label";
import { useId } from "@fluentui/react-hooks";
import * as React from "react";
import {
    AdvancedTab_Section_DataEncryption as ClientResources_AdvancedTab_Section_DataEncryption,
    ClientResources,
    EncryptionSettings as ClientResources_EncryptionSettings,
    EncryptionSettings_Command as ClientResources_EncryptionSettings_Command,
    EncryptionSettings_Header1 as ClientResources_EncryptionSettings_Header1,
    EncryptionSettings_SelectedKey as ClientResources_EncryptionSettings_SelectedKey,
    EncryptionSettings_SelectionSection as ClientResources_EncryptionSettings_SelectionSection,
} from "../../Resx/ClientResources.resjson";
import { getKeyNameFromUrl, getKeyVersionFromUrl, getVaultNameFromId } from "../../../Shared/ViewAgnostic/Utilities";
import { TraceSource, LogArea } from "../../Logging/Logging.types";
import { getResource } from "./EncryptionSettings.DataFetcher";
import { IEncryptionSettingsProps } from "./EncryptionSettings.types";
import { Section } from "../../CommonComponents/Section";
import { DocsTemplate } from "../../CommonComponents/DocsTemplate";
import { KeyValuePairList, IKeyValuePairListItem } from "../../CommonComponents/KeyValuePairList";
import { updateWorkspaceCMK } from "../../Queries/updateWorkspace";
import { tryStringifyError } from "../../Queries/queryUtils";
import { View } from "../../CommonComponents/View";
import { useWorkspaceContext } from "../../CommonComponents/WorkspaceContext";

const Toolbar: React.FC<{ visible?: boolean; commands: ICommandBarItemProps[] }> = ({ visible, commands }) => {
    if (visible === false) {
        return null;
    }
    return (
        <StackItem>
            <CommandBar items={commands} />
        </StackItem>
    )
}

type QueryResultSuccess = { type: "success" };
type QueryResultError = { type: "error", error: unknown }
type QueryResultLoading = { type: "loading" };
type QueryResult = QueryResultSuccess | QueryResultError | QueryResultLoading;

const EncryptionMessageBar: React.FC<{ queryResult?: QueryResult, onDismiss: () => void }> = ({ queryResult, onDismiss }) => {
    if (!queryResult) {
        return null;
    }
    if (queryResult.type === "error") {
        return (
            <MessageBar messageBarType={MessageBarType.error} dismissButtonAriaLabel={ClientResources_EncryptionSettings_Command.dismissAriaLabel} onDismiss={onDismiss} truncated={true} overflowButtonAriaLabel={ClientResources_EncryptionSettings_Command.seeMore}>
                <b>{ClientResources_EncryptionSettings_Command.resultError}</b> {tryStringifyError(queryResult.error, { area: LogArea.EncryptionSettings })}
            </MessageBar>
        );
    }
    if (queryResult.type === "loading") {
        return (
            <MessageBar messageBarType={MessageBarType.info} dismissButtonAriaLabel={ClientResources_EncryptionSettings_Command.dismissAriaLabel} onDismiss={onDismiss}>
                {ClientResources_EncryptionSettings_Command.resultLoading}
            </MessageBar>
        );
    }
    return (
        <MessageBar messageBarType={MessageBarType.success} dismissButtonAriaLabel={ClientResources_EncryptionSettings_Command.dismissAriaLabel} onDismiss={onDismiss}>
            {ClientResources_EncryptionSettings_Command.resultSuccess}
        </MessageBar>
    );
};

const EncryptionSettingsContent: React.FC = () => {
    const { workspace } = useWorkspaceContext();
    const [queryResult, setQueryResult] = React.useState<QueryResult>();
    // set keyPickerData with selected state from key picker.
    const [keyPickerData, setKeyPickerData] = React.useState<Required<Pick<KeyVaultPickerV2.KeyPickerV2Outputs, "keyId" | "keyVersionId" | "vaultResourceId">>>();
    // set submitted key picker data after successful save, so the copyable label can show the submitted key version
    const [submittedKeyData, setSubmittedKeyData] = React.useState<Required<Pick<KeyVaultPickerV2.KeyPickerV2Outputs, "keyId" | "keyVersionId" | "vaultResourceId">>>();
    const encryptionTypeLabelId = useId("encryptionTypeLabelId");
    const keyVaultAndKeyLabelId = useId("keyVaultAndKeyLabelId");
    const armData = ArmId.parse(workspace?.id || "");
    const parameters: KeyVaultPickerV2.KeyPickerV2Parameters = React.useMemo(() => ({
        enableMHSM: false,
        subscriptionId: armData.subscription,
        location: workspace?.location || "",
        showSubscriptionDropdown: false,
        showCreateNew: false,
        showVersionPicker: true,
        keyAndVersionDropdownOptional: false,
        requiredKeyTypes: [KeyVaultPickerV2.KeyType.EC, KeyVaultPickerV2.KeyType.ECHSM, KeyVaultPickerV2.KeyType.RSA, KeyVaultPickerV2.KeyType.RSAHSM]
    }), [armData.subscription, workspace?.location]);

    const onKeyPickerClosed: Az.BladeReference["onClosed"] = React.useCallback((reason, untypedData) => {
        // safe cast because all data is optional and untypedData is a generic object of string keys
        const data = untypedData as KeyVaultPickerV2.KeyPickerV2Outputs | undefined;
        if (
            reason === Az.BladeClosedReason.ChildClosedSelf &&
            data &&
            data.vaultResourceId &&
            data.keyId &&
            data.keyVersionId
        ) {
            setKeyPickerData(data as Required<Pick<KeyVaultPickerV2.KeyPickerV2Outputs, "keyId" | "keyVersionId" | "vaultResourceId">>);
        }
    }, []);

    const isMicrosoftManagedKey = !workspace?.properties?.encryption;
    const keyIdentifierDisplay = React.useMemo(() => (
        submittedKeyData?.keyVersionId || workspace?.properties?.encryption?.keyVaultProperties?.keyIdentifier || ""
    ), [submittedKeyData?.keyVersionId, workspace?.properties?.encryption?.keyVaultProperties?.keyIdentifier]);

    const listItems = React.useMemo<IKeyValuePairListItem[]>(() => {
        const items = [{
            key: "encryptionType",
            label: () => (<Text id={encryptionTypeLabelId}>{ClientResources_EncryptionSettings_SelectionSection.typeLabel}</Text>),
            value: () => (
                <Text aria-labelledby={encryptionTypeLabelId}>{isMicrosoftManagedKey ? ClientResources_AdvancedTab_Section_DataEncryption.microsoftManaged : ClientResources_AdvancedTab_Section_DataEncryption.customerManaged}</Text>
            )
        }];

        if (keyIdentifierDisplay) {
            items.unshift({
                key: "keyVaultAndKey",
                label: () => <Label styles={{ root: { padding: 0 } }} id={keyVaultAndKeyLabelId} required>{ClientResources_EncryptionSettings_SelectionSection.vaultAndKeyLabel}</Label>,
                value: () => (
                    <>
                        <CopyableLabel aria-labelledby={keyVaultAndKeyLabelId} label={""} value={keyIdentifierDisplay}></CopyableLabel>
                        {(!queryResult || queryResult.type !== "loading") && (
                            <BladeLink openAsContextPane={true} bladeReference={{
                                extensionName: "Microsoft_Azure_KeyVault", bladeName: "KeyPickerV2ViewModel", parameters, onClosed: onKeyPickerClosed
                            }}>{ClientResources_EncryptionSettings_SelectionSection.vaultAndKeySelect}</BladeLink>
                        )}
                    </>
                )
            });
        }

        return items;
    }, [queryResult, encryptionTypeLabelId, keyVaultAndKeyLabelId, isMicrosoftManagedKey, keyIdentifierDisplay, parameters, onKeyPickerClosed]);

    const version = getKeyVersionFromUrl(keyPickerData?.keyVersionId) || ClientResources.noContent;
    const keyValutName = getVaultNameFromId(keyPickerData?.vaultResourceId) || ClientResources.noContent;
    const keyName = getKeyNameFromUrl(keyPickerData?.keyVersionId) || ClientResources.noContent;

    const onSave = React.useCallback(() => {
        if (!workspace?.id || !keyPickerData) {
            return
        }
        setQueryResult({ type: "loading" });
        updateWorkspaceCMK(workspace.id, keyPickerData, {
            telemetry: {
                source: TraceSource.EncryptionSettings,
            }
        }).then(() => {
            setSubmittedKeyData(keyPickerData);
            setKeyPickerData(undefined);
            setQueryResult({ type: "success" });
        }).catch((error) => {
            Az.log([{
                area: LogArea.EncryptionSettings,
                level: Az.LogEntryLevel.Error,
                timestamp: Date.now(),
                message: "Error updating cmk",
                args: [error]
            }]);
            setQueryResult({ type: "error", error });
        });
    }, [workspace?.id, keyPickerData]);

    const commands: ICommandBarItemProps[] = React.useMemo(() => {
        return [{
            key: "Save",
            text: ClientResources_EncryptionSettings_Command.save,
            disabled: !keyPickerData || (queryResult && queryResult.type === "loading"),
            iconProps: {
                iconName: "Save"
            },
            onClick: onSave
        }, {
            key: "Discard",
            text: ClientResources_EncryptionSettings_Command.discard,
            disabled: !keyPickerData || (queryResult && queryResult.type === "loading"),
            iconProps: {
                iconName: "Cancel"
            },
            onClick: () => setKeyPickerData(undefined)
        }];
    }, [queryResult, keyPickerData, onSave]);

    React.useEffect(() => {
        Az.configureBladeAlertOnClose({
            showAlert: !!keyPickerData,
            message: ClientResources_EncryptionSettings.dirtyFormWarning
        });
    }, [keyPickerData]);

    return (
        <>
            <EncryptionMessageBar queryResult={queryResult} onDismiss={() => setQueryResult(undefined)} />
            <Stack grow tokens={{ maxWidth: "720px" }}>
                {/* Don't need to show the toolbar for microsoft managed keys, since they are read only */}
                <Toolbar commands={commands} visible={!isMicrosoftManagedKey} />
                <StackItem>
                    <Text block as="p">
                        <DocsTemplate templateString={ClientResources_EncryptionSettings_Header1.template} replacementString="{}" replacements={[
                            <Link href="https://learn.microsoft.com/azure/machine-learning/concept-data-encryption" target="_blank" >{ClientResources_EncryptionSettings_Header1.replacement1}</Link>,
                            <Link href="https://aka.ms/azuremlcmk" target="_blank" >{ClientResources_EncryptionSettings_Header1.replacement2}</Link>
                        ]} />
                    </Text>
                    <Text block as="p">
                        {ClientResources_EncryptionSettings.header2}
                    </Text>
                </StackItem>
                <StackItem>
                    <Section titleProps={{ content: ClientResources_EncryptionSettings_SelectionSection.title, as: "h3" }}>
                        <KeyValuePairList items={listItems} />
                        {keyPickerData && (
                            <>
                                <div>
                                    <Text styles={{ root: { fontWeight: "bold" } }}>{ClientResources_EncryptionSettings_SelectedKey.title}</Text>
                                </div>
                                <dl>
                                    <dt>{ClientResources_EncryptionSettings_SelectedKey.vaultLabel}</dt>
                                    <dd>{keyValutName}</dd>
                                    <dt>{ClientResources_EncryptionSettings_SelectedKey.keyLabel}</dt>
                                    <dd>{keyName}</dd>
                                    <dt>{ClientResources_EncryptionSettings_SelectedKey.versionLabel}</dt>
                                    <dd>{version}</dd>
                                </dl>
                            </>
                        )}
                    </Section>
                </StackItem>
            </Stack>
        </>
    );
}

const EncryptionSettings = (props: IEncryptionSettingsProps) => {
    return (
        <View
            title={ClientResources_EncryptionSettings.title}
            workspaceId={props.parameters.id}
            dataFetcher={getResource}
            telemetryErrorArea={LogArea.EncryptionSettings}
            telemetryTraceSource={TraceSource.EncryptionSettings}
        >
            <EncryptionSettingsContent />
        </View>
    );
};

export default EncryptionSettings;
