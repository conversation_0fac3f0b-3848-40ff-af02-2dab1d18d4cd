import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ElementType, Spinner } from "@fluentui/react";
import { shorthands, makeStyles, MessageBar, MessageBarBody, MessageBarTitle } from "@fluentui/react-components";
import { useId } from "@fluentui/react-hooks";
import * as React from "react";
import {
    AssociatedResource_AppInsights_ChangeBlade as ClientResources_AssociatedResource_AppInsights_ChangeBlade,
    AssociatedResource_AppInsights_ReactView_ChangeAppInsights as ClientResources_AssociatedResource_AppInsights_ReactView_ChangeAppInsights,
} from "../../Resx/ClientResources.resjson";
import * as Az from "@microsoft/azureportal-reactview/Az";
import { View } from "../../CommonComponents/View";
import { useWorkspaceContext } from "../../CommonComponents/WorkspaceContext";
import { useFlights } from "../../Hooks/useFlights";
import { Log<PERSON><PERSON>, TraceSource } from "../../Logging/Logging.types";
import { updateWorkspaceAppInsights } from "../../Queries/updateWorkspace";
import { handleResponseFailures } from "../../Queries/queryUtils";
import { getResource } from "./ChangeAppInsightsPane.DataFetcher";
import { IChangeAppInsightsPaneProps } from "./ChangeAppInsightsPane.types";

const LazyAppInsightsDropDown = React.lazy(() => import("../../CommonComponents/ApplicationInsightsDropdown").then(md => ({ default: md.ApplicationInsightsDropdown })));

const useStyles = makeStyles({
    root: {
        display: "flex",
        flexGrow: 1,
        justifyContent: "space-between",
        flexDirection: "column",
        ...shorthands.gap("2px"),
    },
    form: {
        display: "flex",
        flexDirection: "column",
        ...shorthands.gap("2px")
    },
    footer: {
        display: "flex",
        flexDirection: "row",
        ...shorthands.gap("8px")
    }
});

const ChangeAppInsightsPane = ({ parameters, closeView }: IChangeAppInsightsPaneProps) => {
    const onDismiss = React.useCallback((appInsightsId?: string) => {
        closeView?.(appInsightsId);
    }, []);

    return (
        <View
            title={ClientResources_AssociatedResource_AppInsights_ChangeBlade.title}
            workspaceId={parameters.id}
            dataFetcher={getResource}
            telemetryErrorArea={LogArea.ChangeAppInsightsPane}
            telemetryTraceSource={TraceSource.ChangeAppInsightsPane}
            fullHeight={true}
        >
            <ChangeAppInsights workspaceId={parameters.id} onDismiss={onDismiss} />
        </View>
    );
}

const ChangeAppInsights: React.FC<{ workspaceId: string, onDismiss(appInsightsId?: string): void }> = ({ workspaceId, onDismiss }) => {
    const { workspace } = useWorkspaceContext();
    const [appInsightsId, setAppInsightsId] = React.useState<string | undefined>(workspace?.properties.applicationInsights);
    const id = useId("appInsightsDropdown");
    const styles = useStyles();
    const [message, setMessage] = React.useState<string | undefined>();
    const [messageDetails, setMessageDetails] = React.useState<string | undefined>();
    const [isSaving, setIsSaving] = React.useState(false);
    const { isEnabled } = useFlights();
    const isAppInsightsOptional = isEnabled({ featureName: "optionalAppInsights" });

    const onSave = React.useCallback(async () => {
        if (!appInsightsId) {
            return onDismiss();
        }
        setMessage(undefined);
        setMessageDetails(undefined);
        try {
            setIsSaving(true);
            await updateWorkspaceAppInsights(workspaceId, { applicationInsightsId: appInsightsId }, { telemetry: { source: TraceSource.ChangeAppInsightsPane } });
            return onDismiss(appInsightsId);
        } catch (ex) {
            setIsSaving(false);
            handleResponseFailures(ex, {
                errorLogArea: LogArea.ChangeAppInsightsPane,
                handlers: {
                    handleFailureResponse: (responseError) => {
                        if (responseError.isUnauthorized) {
                            setMessage(ClientResources_AssociatedResource_AppInsights_ReactView_ChangeAppInsights.unauthorizedError);
                            return true;
                        }
                        if (responseError.isNotFound) {
                            setMessage(ClientResources_AssociatedResource_AppInsights_ReactView_ChangeAppInsights.notFoundError);
                            return true;
                        }
                        if (responseError.isUserError) {
                            setMessage(ClientResources_AssociatedResource_AppInsights_ReactView_ChangeAppInsights.error);
                            setMessageDetails(responseError.message);
                            Az.log([
                                {
                                    timestamp: Date.now(),
                                    level: Az.LogEntryLevel.Warning,
                                    message: responseError.message || "",
                                    area: TraceSource.ChangeAppInsightsPane,
                                    code: responseError.httpStatusCode,
                                    args: [responseError]
                                }
                            ])
                            return true;
                        }
                    },
                    handleError: (error) => {
                        setMessage(ClientResources_AssociatedResource_AppInsights_ReactView_ChangeAppInsights.error);
                        Az.log([
                            {
                                timestamp: Date.now(),
                                level: Az.LogEntryLevel.Error,
                                message: error.message || "",
                                area: TraceSource.ChangeAppInsightsPane,
                                code: error.httpStatusCode,
                                args: [error]
                            }
                        ])
                    }
                }
            })
        }
    }, [workspaceId, appInsightsId, onDismiss]);

    return (
        <section className={styles.root}>
            <div className={styles.form}>
                {message && (
                    <MessageBar intent="error">
                        <MessageBarBody>
                            <MessageBarTitle>{ClientResources_AssociatedResource_AppInsights_ReactView_ChangeAppInsights.errorTitle}</MessageBarTitle>
                            <br />
                            {message}
                            {messageDetails && (
                                <>
                                    <br />
                                    {messageDetails}
                                </>
                            )}
                        </MessageBarBody>
                    </MessageBar>
                )}
                <Label htmlFor={id} required={!isAppInsightsOptional}>{ClientResources_AssociatedResource_AppInsights_ReactView_ChangeAppInsights.appInsightsLabel}</Label>
                <React.Suspense fallback={<Shimmer isDataLoaded={false} shimmerElements={[{ type: ShimmerElementType.circle }]} />}>
                    <LazyAppInsightsDropDown id={id} onOptionSelect={setAppInsightsId} value={appInsightsId} required={!isAppInsightsOptional} inlinePopup={true} />
                </React.Suspense>
            </div>
            <footer className={styles.footer}>
                <PrimaryButton disabled={(!isAppInsightsOptional && !appInsightsId) || (appInsightsId && appInsightsId === workspace?.properties.applicationInsights) || isSaving} onClick={() => {
                    onSave();
                }}>{ClientResources_AssociatedResource_AppInsights_ReactView_ChangeAppInsights.footerSave}</PrimaryButton>
                <DefaultButton disabled={isSaving} onClick={() => {
                    onDismiss();
                }}>{ClientResources_AssociatedResource_AppInsights_ReactView_ChangeAppInsights.footerCancel}</DefaultButton>
                {isSaving && <Spinner label={ClientResources_AssociatedResource_AppInsights_ReactView_ChangeAppInsights.appInsightsUpdatingSpinner} ariaLive="assertive" labelPosition="left" />}
            </footer>
        </section>
    )
}

export default ChangeAppInsightsPane;
