import { getOrAdd } from "@microsoft/azureportal-reactview/DataCache";

import { getWorkspace } from "../../Queries/getWorkspace";
import { TraceSource, TraceAction } from "../../Logging/Logging.types";
import { IAssociatedWorkpacesProps } from "./AssociatedWorkspaces.types";

export async function getResource(resourceId: string) {
    return getOrAdd(`resource-${resourceId}`, () => getWorkspace(resourceId, {
        telemetry: {
            source: TraceSource.AssociatedWorkspaces,
            action: TraceAction.LoadWorkspace
        }
    }));
}

export async function fetchData(viewParameters: IAssociatedWorkpacesProps) {
    return getResource(viewParameters.parameters.id);
}
