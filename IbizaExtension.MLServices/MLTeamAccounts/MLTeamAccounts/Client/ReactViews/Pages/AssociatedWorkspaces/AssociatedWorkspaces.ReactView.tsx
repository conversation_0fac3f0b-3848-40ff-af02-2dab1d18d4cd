import * as Az from "@microsoft/azureportal-reactview/Az";
import { ResourceLink } from "@microsoft/azureportal-reactview/ResourceLink";
import { ArmId } from "@microsoft/azureportal-reactview/ResourceManagement";
import {
    CommandBar,
    ICommandBarItemProps,
} from "@fluentui/react/lib/CommandBar"
import {
    DetailsListLayoutMode,
    IColumn,
    SelectionMode
} from "@fluentui/react/lib/DetailsList";
import {
    ShimmeredDetailsList
} from "@fluentui/react/lib/ShimmeredDetailsList";
import {
    MessageBar,
    MessageBarType
} from "@fluentui/react/lib/MessageBar";
import * as React from "react";
import {
    Hub_Projects as ClientResources_Hub_Projects,
    Hub_Projects_Toolbar as ClientResources_Hub_Projects_Toolbar,
} from "../../Resx/ClientResources.resjson";
import { LogArea, TraceSource } from "../../Logging/Logging.types";
import { WorkspaceWithID, asWorkspaceWithID } from "../../workspaceUtils";
import { getResource } from "./AssociatedWorkspaces.DataFetcher";
import { IAssociatedWorkpacesProps } from "./AssociatedWorkspaces.types";
import { handleResponseFailures } from "../../Queries/queryUtils";
import { getProjects } from "../../Queries/getProjects";
import { format } from "@fluentui/react";
import { useWorkspaceContext } from "../../CommonComponents/WorkspaceContext";
import { View } from "../../CommonComponents/View";
import { LinkTarget, useWorkspaceLink } from "../../Hooks/useWorkspaceLink";

const workspaceColumns: IColumn[] = [
    {
        key: 'column1',
        name: 'Name',
        fieldName: 'name',
        minWidth: 100,
        maxWidth: 200,
        isResizable: true,
        onRender: (item: IWorkspaceDetailsListItem) => (
            <ResourceLink resourceId={item.id}>
                {item.name}
            </ResourceLink>
        ),
    },
    {
        key: 'column2',
        name: 'Description',
        fieldName: 'description',
        minWidth: 100,
        maxWidth: 200,
        isResizable: true
    },
    {
        key: 'column3',
        name: 'Resource Group',
        fieldName: 'resourceGroup',
        minWidth: 100,
        maxWidth: 200,
        isResizable: true,
        onRender: (item: IWorkspaceDetailsListItem) => (
            <ResourceLink resourceId={item.resourceGroupId}>
                {item.resourceGroup}
            </ResourceLink>
        ),
    },
    {
        key: 'column4',
        name: 'Subscription',
        fieldName: 'subscription',
        minWidth: 100,
        maxWidth: 200,
        isResizable: true,
        onRender: (item: IWorkspaceDetailsListItem) => (
            <ResourceLink resourceId={item.subscriptionId}>
                {item.subscription}
            </ResourceLink>
        ),
    },
    {
        key: 'column5',
        name: 'Tags',
        fieldName: 'tags',
        minWidth: 100,
        maxWidth: 200,
        isResizable: true
    }
];

export interface IWorkspaceDetailsListItem {
    key: number;
    name: string;
    description: string;
    resourceGroup: string;
    subscription: string;
    tags: string;
    id: string;
    subscriptionId: string;
    resourceGroupId: string;
}

const AssociatedWorkspacesContent: React.FC = () => {
    const { workspace } = useWorkspaceContext();
    const [loadingAssociatedWorkspaces, setLoadingAssociatedWorkspaces] = React.useState<boolean>(false);
    const [associatedWorkspaces, setAssociatedWorkspaces] = React.useState<WorkspaceWithID[]>([]);
    const [errorMessage, setErrorMessage] = React.useState<string | undefined>(undefined);
    const loadFailureMessage = format(ClientResources_Hub_Projects.loadFailure, workspace?.id);
    const [refreshWorkspaces, setRefreshWorkspaces] = React.useState<boolean>();


    const fetchAssociatedWorkspaces = async (hubResourceId?: string) => {
        const errorLogArea = `${LogArea.AssociatedWorkspaces}/fetchAssociatedWorkspaces`;
        if (!hubResourceId) {
            return;
        }
        setErrorMessage(undefined);
        setLoadingAssociatedWorkspaces(true);
        try {
            const associatedWorkspaceResults = await getProjects([], {
                hubResourceId,
                telemetry: {
                    source: TraceSource.AssociatedWorkspaces,
                }
            });
            const associatedWorkspaces = associatedWorkspaceResults.content.data.reduce<WorkspaceWithID[]>((assocWorkspaces: WorkspaceWithID[], assocWorkspace) => {
                if (assocWorkspace.id) {
                    assocWorkspaces.push(asWorkspaceWithID(assocWorkspace, assocWorkspace.id));
                }
                return assocWorkspaces;
            }, []);
            setAssociatedWorkspaces(associatedWorkspaces);
        }
        catch (error) {
            handleResponseFailures(error, {
                handlers: {
                    handleFailureResponse: () => {
                        setErrorMessage(loadFailureMessage)
                        return true; // mark all http client response errors as handled
                    },
                    handleError: (error) => {
                        // log any exceptions (non http client response)
                        Az.log([
                            {
                                timestamp: Date.now(),
                                level: Az.LogEntryLevel.Error,
                                message: error.message || "",
                                area: errorLogArea,
                                args: [error]
                            }
                        ]);
                    }
                },
                errorLogArea
            });
        }
        setLoadingAssociatedWorkspaces(false);
    };

    React.useEffect(() => {
        fetchAssociatedWorkspaces(workspace?.id);
    }, [workspace?.id]);

    React.useEffect(() => {
        if (refreshWorkspaces === undefined || refreshWorkspaces === true) {
            fetchAssociatedWorkspaces(workspace?.id);
            setRefreshWorkspaces(false);
        }
    }, [refreshWorkspaces]);

    const listItems = React.useMemo(() => associatedWorkspaces.map((associatedWorkspace, idx) => {
        const armInfo = ArmId.parse(associatedWorkspace.id);
        const subId = ArmId.stringify(armInfo, ArmId.Kind.Subscription);
        const rgId = ArmId.stringify(armInfo, ArmId.Kind.ResourceGroup);
        const tagStrings = Object.entries(associatedWorkspace.tags ?? {}).map(([tagkey, tagvalue]) => `${tagkey}: ${tagvalue}`);
        return {
            key: idx,
            name: associatedWorkspace.name ?? "",
            description: associatedWorkspace.properties?.description ?? "",
            tags: tagStrings.join(", ") ?? "",
            resourceGroup: armInfo.resourceGroup ?? "",
            subscription: armInfo.subscription ?? "",
            id: associatedWorkspace.id,
            subscriptionId: subId,
            resourceGroupId: rgId
        }
    }), [associatedWorkspaces]);

    const useCreateProject = (workspace: WorkspaceWithID | undefined): ICommandBarItemProps => {
        const onClick = () => {
            Az.openBlade({
                bladeName: "CreateAIStudioProjectBlade",
                extensionName: "Microsoft_Azure_MLTeamAccounts",
                parameters: { hubResourceId: (workspace && workspace.id) ?? "" }
            });
        }

        return {
            key: "add",
            text: ClientResources_Hub_Projects_Toolbar.add,
            doesProvisioning: true,
            iconProps: { iconName: 'Add' },
            onClick
        }
    };

    const { url: teamProjectsLink } = useWorkspaceLink({
        workspace,
        linkTarget: LinkTarget.TeamProjects,
        createMode: true
    });


    const commandBarItems = React.useMemo<ICommandBarItemProps[]>(() => ([
        useCreateProject(workspace),
        {
            key: 'refresh',
            text: ClientResources_Hub_Projects_Toolbar.refresh,
            iconProps: { iconName: 'Refresh' },
            onClick: () => {
                setRefreshWorkspaces(true);
            }
        }
    ]), [teamProjectsLink, workspace]);

    return (
        <>
            {errorMessage &&
                <MessageBar
                    messageBarType={MessageBarType.error}
                    isMultiline={false}
                    onDismiss={() => setErrorMessage(undefined)}
                    dismissButtonAriaLabel="Close"
                >
                    {errorMessage}
                </MessageBar>}
            {workspace && <CommandBar items={commandBarItems} ariaLabel="Projects actions" />}
            <ShimmeredDetailsList
                items={listItems}
                setKey="items"
                columns={workspaceColumns}
                layoutMode={DetailsListLayoutMode.justified}
                selectionMode={SelectionMode.none}
                enableShimmer={loadingAssociatedWorkspaces}
            />
        </>
    );
}


const AssociatedWorkspaces = (props: IAssociatedWorkpacesProps) => {

    return (
        <View
            title={ClientResources_Hub_Projects.title}
            workspaceId={props.parameters.id}
            dataFetcher={getResource}
            telemetryErrorArea={LogArea.AssociatedWorkspaces}
            telemetryTraceSource={TraceSource.AssociatedWorkspaces}
        >
            <AssociatedWorkspacesContent />
        </View>
    );
};

export default AssociatedWorkspaces;
