import * as Az from "@microsoft/azureportal-reactview/Az";
import { DefaultButton, Selection, TextField, TooltipHost } from "@fluentui/react";
import {
    DetailsListLayoutMode,
    DetailsRow,
    IColumn,
    IDetailsRowProps,
    SelectionMode
} from "@fluentui/react/lib/DetailsList";
import {
    MessageBar,
    MessageBarType
} from "@fluentui/react/lib/MessageBar";
import { ShimmeredDetailsList } from "@fluentui/react/lib/ShimmeredDetailsList";
import { shorthands, makeStyles, tokens } from "@fluentui/react-components";
import { CopyButton } from "@microsoft/azureportal-reactview/CopyButton";
import { getEnvironmentValue } from "@microsoft/azureportal-reactview/Environment";
import * as React from "react";
import {
    DeleteHubBlade as ClientResources_DeleteHubBlade,
    DeleteHubBlade_AssociatedResourcesListColumn as ClientResources_DeleteHubBlade_AssociatedResourcesListColumn,
    DeleteHubBlade_Buttons as ClientResources_DeleteHubBlade_Buttons,
    DeleteHubBlade_ConfirmDeleteSection as ClientResources_DeleteHubBlade_ConfirmDeleteSection,
    DeleteHubBlade_ConfirmDeleteSection_ValidationErrorMessage as ClientResources_DeleteHubBlade_ConfirmDeleteSection_ValidationErrorMessage,
    DeleteHubBlade_Description as ClientResources_DeleteHubBlade_Description,
    DeleteHubBlade_ProjectListColumn as ClientResources_DeleteHubBlade_ProjectListColumn,
    DeleteHubBlade_RBAC as ClientResources_DeleteHubBlade_RBAC,
    DeleteHubBlade_ResourceType as ClientResources_DeleteHubBlade_ResourceType,
} from "../../Resx/ClientResources.resjson";
import { View } from "../../CommonComponents/View";
import { useWorkspaceContext } from "../../CommonComponents/WorkspaceContext";
import { LogArea, TraceSource } from "../../Logging/Logging.types";
import { getResource } from "./DeleteHubPane.DataFetcher";
import { IDeleteHubPaneProps } from "./DeleteHubPane.types";
import { ArmId, ArmResource, ResourceMetadata } from "@microsoft/azureportal-reactview/ResourceManagement";
import { ConnectionCategory } from "../../../MLServicesDataModels/Connection.types";
import { WorkspaceWithID } from "../../workspaceUtils";
import { LinkWithIcon } from "../../CommonComponents/LinkWithIcon";
import { beginDeleteHub } from "../../Queries/deleteHub";
import { IUseOnlineEndpointsResult, useOnlineEndpoints } from "../../Hooks/useOnlineEndpoints";
import { useConnections } from "../../Hooks/useConnections";
import { getSubscriptionInfo } from "@microsoft/azureportal-reactview/Subscriptions";
import { usePermissions } from "../../Hooks/usePermissions";
import { allowActionForPermissions } from "../../Queries/permissionUtils";
import { IUseServerlessEndpointsResult, useServerlessEndpoints } from "../../Hooks/useServerlessEndpoints";
import { AssetTypeWithOptions, getAllAssetTypes } from "@microsoft/azureportal-reactview/AssetTypes";
import { FrameworkIcon } from "@microsoft/azureportal-reactview/FrameworkIcon";

interface IProjectListItem {
    key: string;
    projectId: string;
    deploymentCount: number;
    tenantId?: string;
}

const ProjectListColumns: IColumn[] = [
    {
        key: 'project',
        name: ClientResources_DeleteHubBlade_ProjectListColumn.project,
        fieldName: 'project',
        minWidth: 100,
        maxWidth: 200,
        isResizable: true,
        onRender: (item: IProjectListItem) => {
            const armId = ArmId.parse(item.projectId);
            const hostName = getEnvironmentValue("trustedParentOrigin") || "https://portal.azure.com";
            const projectLink = `${hostName}/#resource${item.projectId}`;
            return (
                <LinkWithIcon href={projectLink}>{armId.resourceName}</LinkWithIcon>
            );
        },
    },
    {
        key: 'deployment',
        name: ClientResources_DeleteHubBlade_ProjectListColumn.deployment,
        fieldName: 'deployment',
        minWidth: 100,
        maxWidth: 200,
        isResizable: true,
        onRender: (item: IProjectListItem) => {
            const aiStudioUrl = getEnvironmentValue("webAIStudioBaseUrl");
            const projectDeploymentLink = item.tenantId && item.tenantId !== ""
                ? `${aiStudioUrl}/build/deployments?tid=${item.tenantId}&wsid=${item.projectId}`
                : `${aiStudioUrl}/build/deployments?wsid=${item.projectId}`;
            return (
                <LinkWithIcon href={projectDeploymentLink}>{`${item.deploymentCount} ${ClientResources_DeleteHubBlade_Description.deployment}`}</LinkWithIcon>
            );
        },
    }
];

interface IAssociatedResourcesListItem {
    key: string;
    resourceId: string;
    type: string;
    assetTypes: ReadonlyArray<AssetTypeWithOptions>;
    kind?: string;
}

const AssociatedResourcesListColumns: IColumn[] = [
    {
        key: 'name',
        name: ClientResources_DeleteHubBlade_AssociatedResourcesListColumn.name,
        fieldName: 'name',
        minWidth: 100,
        maxWidth: 200,
        isResizable: true,
        onRender: (item: IAssociatedResourcesListItem) => {
            const armId = ArmId.parse(item.resourceId);
            const hostName = getEnvironmentValue("trustedParentOrigin") || "https://portal.azure.com";
            const resourceLink = `${hostName}/#resource${item.resourceId}`;
            const armItem: ArmResource = {
                id: item.resourceId,
                name: armId.resourceName,
                type: armId.resourceType,
                location: armId.location || "",
                kind: item.kind,
            };
            const resourceMetadata = ResourceMetadata.parse(armItem, item.assetTypes);
            return (
                <>
                    {resourceMetadata.icon &&
                        <FrameworkIcon
                            image={resourceMetadata.icon}
                            style={{
                                width: "18px",
                                height: "18px",
                                marginRight: "8px",
                                display: "inline-block"
                            }}
                        />
                    }
                    <TooltipHost content={item.resourceId}>
                        <LinkWithIcon href={resourceLink}>{armId.resourceName}</LinkWithIcon>
                    </TooltipHost>
                </>
            );
        },
    },
    {
        key: 'type',
        name: ClientResources_DeleteHubBlade_AssociatedResourcesListColumn.type,
        fieldName: 'type',
        minWidth: 70,
        maxWidth: 100,
        isResizable: true
    },
    {
        key: 'resourceGroup',
        name: ClientResources_DeleteHubBlade_AssociatedResourcesListColumn.resourceGroup,
        fieldName: 'resourceGroup',
        minWidth: 100,
        maxWidth: 200,
        isResizable: true,
        onRender: (item: IAssociatedResourcesListItem) => {
            const armId = ArmId.parse(item.resourceId);
            const hostName = getEnvironmentValue("trustedParentOrigin") || "https://portal.azure.com";
            const resourceGroupLink = `${hostName}/#resource/subscriptions/${armId.subscription}/resourceGroups/${armId.resourceGroup}`;
            return <LinkWithIcon href={resourceGroupLink}>{armId.resourceGroup}</LinkWithIcon>;
        },
    }
];
const mapConnectionCategoryToAzureResourceType: Partial<{ [key in ConnectionCategory]: string }> = {
    [ConnectionCategory.AzureOpenAI]: ClientResources_DeleteHubBlade_ResourceType.openAI,
    [ConnectionCategory.OpenAI]: ClientResources_DeleteHubBlade_ResourceType.openAI,
    [ConnectionCategory.AIServices]: ClientResources_DeleteHubBlade_ResourceType.aiServices,
    [ConnectionCategory.CognitiveSearch]: ClientResources_DeleteHubBlade_ResourceType.searchService,
    [ConnectionCategory.AzureBlob]: ClientResources_DeleteHubBlade_ResourceType.storageAccount,
    [ConnectionCategory.ADLSGen2]: ClientResources_DeleteHubBlade_ResourceType.storageAccount,
    [ConnectionCategory.AzureDataLakeGen2]: ClientResources_DeleteHubBlade_ResourceType.storageAccount,
};
const mapCognitiveServiceKindToAzureResourceType: { [key in string]: string } = {
    ["Content Safety"]: ClientResources_DeleteHubBlade_ResourceType.contentSafety,
    ["SpeechServices"]: ClientResources_DeleteHubBlade_ResourceType.speechServices,
    ["ComputerVision"]: ClientResources_DeleteHubBlade_ResourceType.computerVision,
    ["TextAnalytics"]: ClientResources_DeleteHubBlade_ResourceType.textAnalytics,
    ["TextTranslation"]: ClientResources_DeleteHubBlade_ResourceType.textTranslation,
    ["FormRecognizer"]: ClientResources_DeleteHubBlade_ResourceType.formRecognizer,
    ["CognitiveService"]: ClientResources_DeleteHubBlade_ResourceType.cognitiveService
};

const useStyles = makeStyles({
    root: {
        display: "flex",
        flexGrow: 1,
        justifyContent: "space-between",
        flexDirection: "column",
        ...shorthands.gap("2px"),
        overflowY: "hidden"
    },
    hubNameSection: {
        display: "flex",
        flexDirection: "row",
        ...shorthands.gap("2px"),
        marginBottom: "10px",
    },
    topForm: {
        display: "flex",
        flexDirection: "column",
        ...shorthands.gap("2px"),
        flexGrow: 1,
        overflowY: "auto"
    },
    bottomForm: {
        maxHeight: "150px"
    },
    footer: {
        display: "flex",
        flexDirection: "row",
        ...shorthands.gap("8px"),
        marginTop: "20px"
    },
    deleteButtonEnabled: {
        color: tokens.colorNeutralForegroundInverted,
        ...shorthands.borderColor(tokens.colorStatusDangerBackground3),
        backgroundColor: tokens.colorStatusDangerBackground3,
    },
    description: {
        marginBottom: "10px",
    }
});

const DescriptionSection: React.FC<{
    onlineEndpointsLoading: boolean;
    onlineEndpointsQuery: IUseOnlineEndpointsResult[];
    serverlessEndpointsLoading: boolean;
    serverlessEndpointsQuery: IUseServerlessEndpointsResult[]
}> = ({ onlineEndpointsLoading, onlineEndpointsQuery, serverlessEndpointsLoading, serverlessEndpointsQuery }) => {
    const styles = useStyles();
    const { workspace } = useWorkspaceContext();
    const [tenantId, setTenantId] = React.useState<string>("");
    const associatedWorkspaces = workspace?.properties?.associatedWorkspaces;
    const projectCount = associatedWorkspaces?.length || 0;
    const onlineEndpointCount = onlineEndpointsQuery.reduce((acc, result) => acc + result.onlineEndpointsList.length, 0);
    const serverlessEndpointCount = serverlessEndpointsQuery.reduce((acc, result) => acc + result.serverlessEndpointsList.length, 0);
    const totalDeploymentCount = onlineEndpointCount + serverlessEndpointCount;
    const formattedSection1 = ClientResources_DeleteHubBlade_Description.section1.split(/(\{0\}|\{1\})/).map(part => {
        if (part === '{0}') {
            return `${projectCount} ${ClientResources_DeleteHubBlade_Description.project}`;
        } else if (part === '{1}') {
            return `${totalDeploymentCount} ${ClientResources_DeleteHubBlade_Description.deployment}`;
        }
        return part;
    });
    const projectListItems = associatedWorkspaces?.map((project, idx) => {
        const projectDeploymentCount = onlineEndpointsQuery[idx] && serverlessEndpointsQuery[idx]
            ? onlineEndpointsQuery[idx].onlineEndpointsList.length + serverlessEndpointsQuery[idx].serverlessEndpointsList.length
            : 0;
        return {
            key: project,
            projectId: project,
            deploymentCount: projectDeploymentCount,
            tenantId
        }
    });

    const loadTenantId = React.useCallback(async () => {
        if (!workspace?.id) {
            return;
        }
        const armId = ArmId.parse(workspace.id);
        const tenantId = (await getSubscriptionInfo(armId.subscription)).tenantId;
        setTenantId(tenantId);
    }, [workspace?.id]);

    React.useEffect(() => {
        loadTenantId();
    }, [loadTenantId]);

    return (
        <>
            {!onlineEndpointsLoading && !serverlessEndpointsLoading &&
                <div> {formattedSection1} </div>
            }
            {projectListItems?.length &&
                <div className={styles.description}>
                    <ShimmeredDetailsList
                        items={projectListItems}
                        setKey="items"
                        columns={ProjectListColumns}
                        layoutMode={DetailsListLayoutMode.justified}
                        selectionMode={SelectionMode.none}
                        enableShimmer={onlineEndpointsLoading || serverlessEndpointsLoading}
                    />
                </div>
            }
            <div className={styles.description}>
                {ClientResources_DeleteHubBlade_Description.section2}
            </div>
            <div className={styles.description}>
                {ClientResources_DeleteHubBlade_Description.section3}
            </div>
        </>
    );
}

const generateDefaultListItems = (assetTypes: ReadonlyArray<AssetTypeWithOptions>, workspace?: WorkspaceWithID) => {
    const defaultListItems = [];
    if (workspace?.properties) {
        const { keyVault, storageAccount, applicationInsights, containerRegistry } = workspace?.properties;
        if (keyVault) {
            defaultListItems.push(
                {
                    key: "keyVault",
                    resourceId: keyVault,
                    type: ClientResources_DeleteHubBlade_ResourceType.keyVault,
                    assetTypes
                }
            );
        }
        if (storageAccount) {
            defaultListItems.push(
                {
                    key: "storageAccount",
                    resourceId: storageAccount,
                    type: ClientResources_DeleteHubBlade_ResourceType.storageAccount,
                    assetTypes
                }
            );
        }
        if (applicationInsights) {
            defaultListItems.push(
                {
                    key: "applicationInsights",
                    resourceId: applicationInsights,
                    type: ClientResources_DeleteHubBlade_ResourceType.applicationInsights,
                    assetTypes
                }
            );
        }
        if (containerRegistry) {
            defaultListItems.push(
                {
                    key: "containerRegistry",
                    resourceId: containerRegistry,
                    type: ClientResources_DeleteHubBlade_ResourceType.containerRegistry,
                    assetTypes
                }
            );
        }
    }
    return defaultListItems;
};

const AssociatedResourcesList: React.FC<{ onAssociatedResourcesSelectionChanged(selection: Selection): void }> = ({ onAssociatedResourcesSelectionChanged }) => {
    const { workspace } = useWorkspaceContext();
    const [listItems, setListItems] = React.useState<IAssociatedResourcesListItem[]>([]);
    const [assetTypes, setAssetTypes] = React.useState<ReadonlyArray<AssetTypeWithOptions>>([]);
    const [displayRBACInfoMessage, setDisplayRBACInfoMessage] = React.useState<boolean>(false);
    const selection: Selection = new Selection({onSelectionChanged: () => onAssociatedResourcesSelectionChanged(selection) });
    const connectionsQuery = useConnections({ workspace, telemetry: { source: TraceSource.DeleteHubPane } });

    React.useEffect(() => {
        const fetchAssetTypes = async () => {
            const assetTypes = await getAllAssetTypes();
            setAssetTypes(assetTypes);
            setListItems(generateDefaultListItems(assetTypes, workspace));
        };
        fetchAssetTypes();
      }, []);

    React.useEffect(() => {
        connectionsQuery.data?.content.value.forEach((connection, idx) => {
            const newListItem = listItems;
            let newItem: IAssociatedResourcesListItem | undefined;
            const category = connection?.properties?.category;
            const metadata = connection?.properties?.metadata;

            if (category === ConnectionCategory.AzureOpenAI
                || category === ConnectionCategory.OpenAI
                || category === ConnectionCategory.AIServices
                || category === ConnectionCategory.CognitiveSearch
            ) {
                if (metadata?.ResourceId) {
                    newItem = {
                        key: `connection-${idx}`,
                        resourceId: metadata?.ResourceId,
                        type: mapConnectionCategoryToAzureResourceType[category] || "",
                        kind: category === ConnectionCategory.AzureOpenAI || category === ConnectionCategory.OpenAI
                            ? "OpenAI"
                            : category,
                        assetTypes
                    }
                }
            } else if (category === ConnectionCategory.CognitiveService) {
                if (metadata?.ResourceId) {
                    newItem = {
                        key: `connection-${idx}`,
                        resourceId: metadata?.ResourceId,
                        type: mapCognitiveServiceKindToAzureResourceType[metadata?.Kind || "CognitiveService"],
                        kind: metadata?.Kind == "Content Safety" ? "ContentSafety" : metadata?.Kind,
                        assetTypes
                    }
                }
            } else if (category === ConnectionCategory.AzureBlob
                || category === ConnectionCategory.ADLSGen2
                || category === ConnectionCategory.AzureDataLakeGen2
            ) {
                if (metadata?.SubscriptionId && metadata?.ResourceGroup && metadata?.AccountName) {
                    newItem = {
                        key: `connection-${idx}`,
                        resourceId: `/subscriptions/${metadata?.SubscriptionId}/resourceGroups/${metadata?.ResourceGroup}/providers/Microsoft.Storage/storageAccounts/${metadata?.AccountName}`,
                        type: mapConnectionCategoryToAzureResourceType[category] || "",
                        assetTypes
                    }
                }
            }
            if (newItem && !newListItem.some(item => item.resourceId === newItem?.resourceId)) {
                newListItem.push(newItem);
                setListItems(newListItem);
            }
        });
    }, [connectionsQuery.data?.content.value]);

    const AssociatedResourcesListRow: React.FC<{ rowProps?: IDetailsRowProps }> = ({ rowProps }) => {
        const permissionsQuery = usePermissions({
            resourceId: rowProps?.item.resourceId,
            telemetry: { source: TraceSource.DeleteHubPane }
        });
        const [rowDisabled, setRowDisabled] = React.useState<boolean>(false);

        React.useEffect(() => {
            if (permissionsQuery.data?.content.value) {
                const armId = ArmId.parse(rowProps?.item.resourceId);
                const hasPermission = allowActionForPermissions(`${armId.resourceType}/delete`, permissionsQuery.data?.content.value);
                setRowDisabled(!hasPermission);
                if (!hasPermission) {
                    setDisplayRBACInfoMessage(true);
                }
            }
        }, [permissionsQuery.data?.content.value]);
        return rowProps ? <DetailsRow {...rowProps} disabled={rowDisabled} /> : <></>;
    };

    const onRenderRow = (
        rowProps?: IDetailsRowProps,
    ) => {
        return <AssociatedResourcesListRow rowProps={rowProps} />
    };

    return (
        <>
            {displayRBACInfoMessage &&
                <MessageBar messageBarType={MessageBarType.info}>
                    {ClientResources_DeleteHubBlade_RBAC.message}
                </MessageBar>
            }
            <ShimmeredDetailsList
                items={listItems}
                setKey="items"
                columns={AssociatedResourcesListColumns}
                layoutMode={DetailsListLayoutMode.justified}
                selectionMode={SelectionMode.multiple}
                enableShimmer={connectionsQuery.isLoading}
                selection={selection}
                onRenderRow={onRenderRow}
                selectionPreservedOnEmptyClick={true}
            />
        </>
    );
}

const DeleteHub: React.FC<{ onDismiss(): void }> = ({ onDismiss }) => {
    const styles = useStyles();
    const { workspace } = useWorkspaceContext();
    const [selectedAssociatedResources, setSelectedAssociatedResources] = React.useState<string[]>([]);
    const [deleteButtonDisabled, setDeleteButtonDisabled] = React.useState<boolean>(true);
    const onlineEndpointsQuery = useOnlineEndpoints({ projectList: workspace?.properties.associatedWorkspaces || [], telemetry: { source: TraceSource.DeleteHubPane } });
    const onlineEndpointsLoading = onlineEndpointsQuery.some(result => result.isLoading);
    const serverlessEndpointsQuery = useServerlessEndpoints({ projectList: workspace?.properties.associatedWorkspaces || [], telemetry: { source: TraceSource.DeleteHubPane } });
    const serverlessEndpointsLoading = serverlessEndpointsQuery.some(result => result.isLoading);

    const onAssociatedResourcesSelectionChanged = React.useCallback((selection) => {
        const selected = selection.getSelection() as IAssociatedResourcesListItem[];
        setSelectedAssociatedResources(selected.map(item => item.resourceId));
    }, []);

    const validateHubName = React.useCallback(
        (value: string): string | undefined => {
            if (value === "") {
                setDeleteButtonDisabled(true);
                return ClientResources_DeleteHubBlade_ConfirmDeleteSection_ValidationErrorMessage.emptyString;
            } else if (value !== workspace?.name) {
                setDeleteButtonDisabled(true);
                return ClientResources_DeleteHubBlade_ConfirmDeleteSection_ValidationErrorMessage.nameMismatch;
            } else {
                setDeleteButtonDisabled(false);
                return undefined;
            }
        },
        [workspace?.name],
    );

    const onSave = React.useCallback(async () => {
        if (!workspace?.id) {
            return onDismiss();
        }
        const deleteResourcesList = [];
        deleteResourcesList.push(...selectedAssociatedResources);
        onlineEndpointsQuery.forEach(result => {
            deleteResourcesList.push(...result.onlineEndpointsList);
        });
        serverlessEndpointsQuery.forEach(result => {
            deleteResourcesList.push(...result.serverlessEndpointsList);
        });
        deleteResourcesList.push(...workspace.properties?.associatedWorkspaces || []);
        deleteResourcesList.push(workspace.id);
        Az.closeCurrentBlade({
            deleteResourcesList
        });
    }, [workspace, onlineEndpointsQuery, serverlessEndpointsQuery, selectedAssociatedResources, beginDeleteHub, onDismiss]);

    return (
        <section className={styles.root}>
            <div className={styles.topForm}>
                {workspace?.name &&
                    <div className={styles.hubNameSection}>
                        <span>{`${ClientResources_DeleteHubBlade_Description.hubName}${workspace?.name}`}</span><CopyButton value={workspace?.name} ariaLabel={workspace?.name} />
                    </div>
                }
                <DescriptionSection
                    onlineEndpointsLoading={onlineEndpointsLoading}
                    onlineEndpointsQuery={onlineEndpointsQuery}
                    serverlessEndpointsLoading={serverlessEndpointsLoading}
                    serverlessEndpointsQuery={serverlessEndpointsQuery}
                />
                <AssociatedResourcesList onAssociatedResourcesSelectionChanged={onAssociatedResourcesSelectionChanged}/>
            </div>
            <div className={styles.bottomForm}>
                <TextField
                    label={ClientResources_DeleteHubBlade_ConfirmDeleteSection.label}
                    placeholder={ClientResources_DeleteHubBlade_ConfirmDeleteSection.placeholder}
                    required={true}
                    onGetErrorMessage={validateHubName}
                    validateOnLoad={false}
                />
                <footer className={styles.footer}>
                    <DefaultButton
                        onClick={() => {onSave();}}
                        className={!deleteButtonDisabled ? styles.deleteButtonEnabled : ""}
                        disabled={deleteButtonDisabled}
                    >
                        {ClientResources_DeleteHubBlade_Buttons.delete}
                    </DefaultButton>
                    <DefaultButton
                        onClick={() => {onDismiss();}}
                    >
                        {ClientResources_DeleteHubBlade_Buttons.cancel}
                    </DefaultButton>
                </footer>
            </div>
        </section>
    )
}

const DeleteHubPane = ({ parameters, closeView }: IDeleteHubPaneProps) => {
    const onDismiss = React.useCallback(() => {
        closeView?.();
    }, []);
    const { id } = parameters;
    return (
        <View
            title={ClientResources_DeleteHubBlade.title}
            workspaceId={id}
            dataFetcher={getResource}
            telemetryErrorArea={LogArea.DeleteHubPane}
            telemetryTraceSource={TraceSource.DeleteHubPane}
            fullHeight={true}
        >
            <DeleteHub onDismiss={onDismiss}/>
        </View>
    );
}

export default DeleteHubPane;
