import { getOrAdd } from "@microsoft/azureportal-reactview/DataCache";
import { TraceSource, TraceAction } from "../../Logging/Logging.types";
import { getWorkspace } from "../../Queries/getWorkspace";
import { IDeleteHubPaneProps } from "./DeleteHubPane.types";

export async function getResource(resourceId: string) {
    return getOrAdd(`resource-${resourceId}`, () => getWorkspace(resourceId, {
        telemetry: {
            source: TraceSource.DeleteHubPane,
            action: TraceAction.LoadWorkspace
        }
    }));
}

export async function fetchData(viewParameters: IDeleteHubPaneProps) {
    return getResource(viewParameters.parameters.id);
}
