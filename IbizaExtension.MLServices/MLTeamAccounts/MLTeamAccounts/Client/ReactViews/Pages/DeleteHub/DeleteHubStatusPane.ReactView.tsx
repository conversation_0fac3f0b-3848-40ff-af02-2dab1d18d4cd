import { mergeStyles, TooltipHost } from "@fluentui/react";
import { tokens } from "@fluentui/react-components";
import {
    CheckmarkCircle20Filled,
    Clock20Regular,
    DismissCircle20Filled,
  } from "@fluentui/react-icons";
import {
    DetailsListLayoutMode,
    IColumn,
    SelectionMode
} from "@fluentui/react/lib/DetailsList";
import { ShimmeredDetailsList } from "@fluentui/react/lib/ShimmeredDetailsList";
import * as React from "react";
import {
    DeleteHubStatusBlade as ClientResources_DeleteHubStatusBlade,
    DeleteHubStatusBlade_ResourceStatusListColumn as ClientResources_DeleteHubStatusBlade_ResourceStatusListColumn,
    DeleteHubBlade_ResourceType as ClientResources_DeleteHubBlade_ResourceType,
} from "../../Resx/ClientResources.resjson";
import { View } from "../../CommonComponents/View";
import { LogArea, TraceSource } from "../../Logging/Logging.types";
import { IDeleteHubStatusPaneProps } from "./DeleteHubStatusPane.types";
import { useDeleteHubStatus } from "../../Hooks/useDeleteHubStatus";
import { ArmId, ArmResource, ResourceMetadata } from "@microsoft/azureportal-reactview/ResourceManagement";
import { Ajax } from "@microsoft/azureportal-reactview/FxReactCommon.Modules";
import { DeleteHubStatusResult } from "../../Queries/getDeleteHubStatus";
import { getEnvironmentValue } from "@microsoft/azureportal-reactview/Environment";
import { LinkWithIcon } from "../../CommonComponents/LinkWithIcon";
import { FrameworkIcon } from "@microsoft/azureportal-reactview/FrameworkIcon";
import { AssetTypeWithOptions, getAllAssetTypes } from "@microsoft/azureportal-reactview/AssetTypes";

interface IResourceStatusListItem {
    key: string;
    resourceId: string;
    status: string;
    assetTypes: ReadonlyArray<AssetTypeWithOptions>;
    hubResourceId: string;
}

const mapARMResourceTypeToResourceTypeColumnString: { [key in string]: string } = {
    ["microsoft.keyvault/vaults"]: ClientResources_DeleteHubBlade_ResourceType.keyVault,
    ["microsoft.storage/storageaccounts"]: ClientResources_DeleteHubBlade_ResourceType.storageAccount,
    ["microsoft.insights/components"]: ClientResources_DeleteHubBlade_ResourceType.applicationInsights,
    ["microsoft.containerregistry/registries"] : ClientResources_DeleteHubBlade_ResourceType.containerRegistry,
    ["microsoft.cognitiveservices/accounts"]: ClientResources_DeleteHubBlade_ResourceType.cognitiveService,
    ["microsoft.search/searchservices"]: ClientResources_DeleteHubBlade_ResourceType.searchService
};

const getResourceTypeColumnString = (hubResourceId: string, resourceArmId: string) => {
    const armId = ArmId.parse(resourceArmId);
    if (armId.resourceType.toLowerCase() === "microsoft.machinelearningservices/workspaces") {
        return resourceArmId === hubResourceId ? ClientResources_DeleteHubBlade_ResourceType.hub : ClientResources_DeleteHubBlade_ResourceType.project;
    } else if (armId.resourceType.toLowerCase().includes("endpoint")) {
        return ClientResources_DeleteHubBlade_ResourceType.endpoint;
    } else {
        return mapARMResourceTypeToResourceTypeColumnString[armId.resourceType.toLowerCase()] || armId.resourceType;
    }
};

const ResourceStatusListColumns: IColumn[] = [
    {
        key: 'statusIcon',
        name: "",
        fieldName: 'statusIcon',
        minWidth: 20,
        maxWidth: 20,
        onRender: (item: IResourceStatusListItem) => {
            switch(item.status) {
                case "Accepted":
                    return <Clock20Regular className={mergeStyles({color: tokens.colorNeutralForeground3})} />;
                case "Deleting":
                    return <Clock20Regular className={mergeStyles({color: tokens.colorNeutralForeground3})} />;
                case "Succeeded":
                    return <CheckmarkCircle20Filled className={mergeStyles({color: tokens.colorPaletteGreenForeground3})} />;
                case "Failed":
                    return <DismissCircle20Filled className={mergeStyles({color: tokens.colorPaletteRedForeground1})} />;
                case "Canceled":
                    return <DismissCircle20Filled className={mergeStyles({color: tokens.colorPaletteRedForeground1})} />;
                default:
                    return <></>;
            }
        },
    },
    {
        key: 'resource',
        name: ClientResources_DeleteHubStatusBlade_ResourceStatusListColumn.resource,
        fieldName: 'resource',
        minWidth: 70,
        maxWidth: 200,
        isResizable: true,
        onRender: (item: IResourceStatusListItem) => {
            const armId = ArmId.parse(item.resourceId);
            const hostName = getEnvironmentValue("trustedParentOrigin") || "https://portal.azure.com";
            const resourceLink = `${hostName}/#resource${item.resourceId}`;
            let kind = undefined;
            if (armId.resourceType.toLowerCase() === "microsoft.machinelearningservices/workspaces") {
                kind = item.resourceId === item.hubResourceId ? "Hub" : "Project";
            }
            const armItem: ArmResource = {
                id: item.resourceId,
                name: armId.resourceName,
                type: armId.resourceType,
                location: armId.location || "",
                kind,
            };
            const resourceMetadata = ResourceMetadata.parse(armItem, item.assetTypes);
            return (
                <>
                    {resourceMetadata.icon &&
                        <FrameworkIcon
                            image={resourceMetadata.icon}
                            style={{
                                width: "18px",
                                height: "18px",
                                marginRight: "8px",
                                display: "inline-block"
                            }}
                        />
                    }
                    <TooltipHost content={item.resourceId}>
                        <LinkWithIcon href={resourceLink}>{armId.resourceName}</LinkWithIcon>
                    </TooltipHost>
                </>
            );
        },
    },
    {
        key: 'type',
        name: ClientResources_DeleteHubStatusBlade_ResourceStatusListColumn.type,
        fieldName: 'type',
        minWidth: 70,
        maxWidth: 200,
        isResizable: true,
        onRender: (item: IResourceStatusListItem) => {
            return getResourceTypeColumnString(item.hubResourceId, item.resourceId);
        },
    },
    {
        key: 'status',
        name: ClientResources_DeleteHubStatusBlade_ResourceStatusListColumn.status,
        fieldName: 'status',
        minWidth: 70,
        maxWidth: 150,
        isResizable: true,
    }
];


const DeleteHubStatus: React.FC<{ hubResourceId: string }> = ({ hubResourceId }) => {
    const deleteHubStatusQuery = useDeleteHubStatus({ hubResourceId, deleteInitiated: true, telemetry: { source: TraceSource.DeleteHubStatusPane } });
    const [resourceStatusListItems, setResourceStatusListItems] = React.useState<IResourceStatusListItem[]>([]);
    const [assetTypes, setAssetTypes] = React.useState<ReadonlyArray<AssetTypeWithOptions>>([]);
    React.useEffect(() => {
        const fetchAssetTypes = async () => {
            const assetTypes = await getAllAssetTypes();
            setAssetTypes(assetTypes);
        };
        fetchAssetTypes();
      }, []);
    React.useEffect(() => {
        if (!deleteHubStatusQuery.isLoading) {
            const queryResultContent = deleteHubStatusQuery.isError ? (deleteHubStatusQuery.error as Ajax.BatchResponseItem<DeleteHubStatusResult>)?.content : deleteHubStatusQuery.data?.content;
            const overallStatus = queryResultContent?.status;
            const listItems = queryResultContent?.responses?.map(resource => {
                return {
                    key: resource.id || "",
                    resourceId: resource.id || "",
                    // Current bulkDelete API could return "Failed" for resource deletion on initial attempt.
                    // But this is not the terminal state when overallStatus is still "Accepted" (i.e.: ongoing), it will retry and could succeed the next time.
                    // We avoid showing initial "Failed" as it will confuse users. Once overallStatus reaches a terminal state, we display the actual status for each individual resource.
                    status: overallStatus === "Accepted" && resource.status === "Failed"
                        ? "Deleting"
                        : resource.status || "",
                    assetTypes,
                    hubResourceId
                }
            }) || [];
            setResourceStatusListItems(listItems);
        }
    }, [deleteHubStatusQuery.isLoading, deleteHubStatusQuery.isError, deleteHubStatusQuery.error, deleteHubStatusQuery.data, assetTypes, hubResourceId]);
    return (
        <section>
            <div>
                <ShimmeredDetailsList
                    items={resourceStatusListItems}
                    setKey="items"
                    columns={ResourceStatusListColumns}
                    layoutMode={DetailsListLayoutMode.justified}
                    selectionMode={SelectionMode.none}
                    enableShimmer={deleteHubStatusQuery.isLoading}
                />
            </div>
        </section>
    )
}

const DeleteHubStatusPane = ({ parameters }: IDeleteHubStatusPaneProps) => {
    const { id, hubName } = parameters;
    return (
        <View
            title={ClientResources_DeleteHubStatusBlade.title}
            subtitle={hubName}
            workspaceId={id}
            telemetryErrorArea={LogArea.DeleteHubStatusPane}
            telemetryTraceSource={TraceSource.DeleteHubStatusPane}
            fullHeight={true}
        >
            <DeleteHubStatus hubResourceId={id} />
        </View>
    );
}

export default DeleteHubStatusPane;
