<?xml version="1.0" encoding="utf-8" ?>
<!--    When adding new values, please use UPPER CASE - all values will be compared only to the UPPER CASE strings -->
<PoliCheckExclusions>

  <!-- Each of these exclusions is a folder name - if \[name]\ exists in the file path, it will be skipped -->
  <Exclusion Type="FolderPathFull">.VSCODE | .GIT | AZUREMLCLI | BUILD | LICENSES | PRIVATE | QCONFIG | SCRIPTS | TOOLS | IBIZAEXTENSION.MLSERVICES\MLTEAMACCOUNTS\MLTEAMACCOUNTS\REACTBLADES | IBIZAEXTENSION.MLSERVICES\MLTEAMACCOUNTS\MLTEAMACCOUNTS\DEFINITIONS | IBIZAEXTENSION.MLSERVICES\MLTEAMACCOUNTS\LOCALIZE\LOC | SRC\.VSCODE | SRC\AETHER | SRC\AML | SRC\AZUREML-API | SRC\AZUREML-DATACACHE | SRC\AZUREML-GPT3 | SRC\AZUREML-INFRA | SRC\AZUREML-JOB-RUNTIME | SRC\AZUREML-LOADTEST | SRC\AZUREML-SYNAPSE | SRC\DOCS | SRC\GENEVA_SYNTHETICS | SRC\INTEGRATIONTESTS | SRC\PRODUCT </Exclusion>

  <!-- Justification: No need to check PolicheckExclusion.xml itself -->
  <Exclusion Type="FileName">POLICHECKEXCLUSION.XML</Exclusion>

</PoliCheckExclusions>