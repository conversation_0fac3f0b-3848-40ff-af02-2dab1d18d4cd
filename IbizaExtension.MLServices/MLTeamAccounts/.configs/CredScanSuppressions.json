{"tool": "Credential Scanner", "suppressions": [{"file": ["src\\azureml-api\\src\\RAISvc\\SimulationParametersLibrary\\task_simulator\\parameters\\grounding\\grounding.json", "src\\azureml-api\\src\\AccountRP\\EntryPoints.Gated.Tests\\OneRpTests\\ExtensionsTest\\RequestResponseLoggingExtensionsTests.cs", "AzureMlCli\\envs\\scripts\\import_environments.py", "AzureMlCli\\src\\azureml-core\\sdk_readme.md", "src\\azureml-datacache\\doc\\Azure Data Factory support for Hydration job.md", "AzureMlCli\\notebooks\\how-to-use-azureml\\azure-synapse\\shakespeare.txt", "AzureMlCli\\src\\azureml-contrib-k8s\\doc\\train-on-k8s.ipynb", "AzureMlCli\\src\\azureml-pipeline-steps\\tests\\estimator_mock_objects.py", "src\\azureml-api\\src\\BillingADF\\arm-template-parameters-definition.json", "AzureMlCli\\scripts\\dataprep\\buildVMs\\linux\\buildAgent.ARM.json", "AzureMlCli\\scripts\\dataprep\\buildVMs\\windows\\buildAgent.ARM.json", "AzureMlCli\\src\\azureml-contrib-reinforcementlearning\\tests\\unit\\mock_objects.py", "AzureMlCli\\src\\azureml-monitoring\\tests\\CollectionPerfTest\\basic.json", "AzureMlCli\\src\\azureml-monitoring\\tests\\CollectionPerfTest\\heavy-load.json", "AzureMlCli\\src\\azureml-monitoring\\tests\\CollectionPerfTest\\long-running.json", "AzureMlCli\\src\\azureml-monitoring\\tests\\CollectionPerfTest\\multi-instance.json", "AzureMlCli\\src\\azureml-monitoring\\tests\\CollectionPerfTest\\multi-instance.json", "AzureMlCli\\src\\azureml-monitoring\\tests\\demo\\aml-pipelines-scope.ipynb", "AzureMlCli\\src\\azureml-monitoring\\tests\\demo\\plot_helper.py", "AzureMlCli\\src\\azureml-train-core\\tests\\unittests\\mock_objects.py", "AzureMlCli\\tests\\scenarios\\data\\datastore\\conftest.py", "AzureMlCli\\tests\\scenarios\\data\\datastore\\constants.py", "AzureMlCli\\tests\\scenarios\\data\\datastore\\datastore_e2e_tests.py", "AzureMlCli\\tests\\scenarios\\data\\runner\\region_configs.py", "AzureMlCli\\tests\\scenarios\\experimentation\\cli_tests\\test_cli_tests.py", "AzureMlCli\\tests\\scenarios\\operationalization\\runners\\test_mms_synth_traffic_runners.py", "src\\azureml-api\\deploy\\specs\\prod\\usgovvirginia.yaml", "src\\azureml-api\\src\\Dataset\\EntryPoints.Tests\\IntegrationTestsBootstrap.ps1", "src\\azureml-api\\src\\Dataset\\EntryPoints.Tests\\IntegrationTestsBootstrap_DataCall.ps1", "src\\azureml-api\\src\\EnvironmentManagement\\Services\\CloudImageInspector.cs", "src\\azureml-api\\src\\Execution\\EntryPoints\\appsettings.json", "AzureMlCli\\src\\azure-cli-ml\\ml_cli_private_extension\\azext_ml_private\\_notebook\\utils.py", "AzureMlCli\\src\\azureml-dataprep\\tests\\notebooks\\data\\adls-dpreptestfiles.crt", "AzureMlCli\\src\\azureml-dataprep\\tests\\notebooks\\data\\ADLSgen2-datapreptest.crt", "AzureMlCli\\src\\azureml-parallel-run\\deploy\\specs\\envs\\envs.yaml", "AzureMlCli\\tests\\scenarios\\history\\core\\authentication\\test_auth.py", "src\\aether\\platform\\BBAetherLibrary\\IntegrationTests\\BBAetherLibrary.AutoML.IntegrationTests\\.runsettings", "src\\aether\\platform\\BBAetherLibrary\\IntegrationTests\\shared\\.runsettings", "src\\azureml-api\\src\\Execution\\EntryPoints.Tests\\CommonRuntime\\TrainingFeaturesTests.cs", "src\\azureml-api\\src\\Execution\\EntryPoints.Tests\\ServicesTests\\AiscComponentTests.cs", "src\\azureml-job-runtime\\Hosttools\\src\\hosttools\\streamer\\blobstreamer_test.go", "AzureMlCli\\src\\azure-cli-ml\\ml_cli_extension\\azext_ml\\service\\data\\testrequest.json", "src\\azureml-api\\src\\AccountRP\\EntryPoints.Tests\\OneRpTests\\Tests\\Create_Workspace_GetKeyVaultToken_Succeeds.cs", "src\\azureml-api\\src\\EnvironmentManagement\\Services\\SetupContent\\Common\\docker_registry_context_manager.py", "src\\azureml-api\\src\\HyperDrive\\hyperdrive\\scheduler\\SchedulerTest\\CommonUtilsTest.cs", "src\\azureml-api\\src\\Compute\\Core\\src\\Common.Tests\\UnitTests\\CommonRuntimeTests.cs", "src\\azureml-api\\src\\Designer\\src\\MiddleTier\\MiddleTier.IntegrationTests\\TestData\\Dpv2Components\\Samples\\CLI\\6c_r_iris\\r-iris-example\\azureml_utils.R", "src\\azureml-api\\src\\Designer\\src\\MiddleTier\\MiddleTier.IntegrationTests\\TestData\\Dpv2Components\\Samples\\SDK\\1d_pipeline_with_non_python_components\\train-data-with-R\\azureml_utils.R", "AzureMlCli\\envs\\scripts\\create_ws_config.py", "AzureMlCli\\base_images\\docker\\deprecated_aml-triton-images\\tritonserver-21.02-py38-inference\\tplist.txt", "AzureMlCli\\base_images\\docker\\deprecated_aml-triton-images\\tritonserver-21.02-py38-inference\\tritonserver-21.02-py3-pkgs.txt", "AzureMlCli\\src\\azureml-contrib-reinforcementlearning\\tests\\e2e\\amlwindows_compute_creation_tests.py", "AzureMlCli\\src\\azureml-datadrift\\doc\\tsd\\retrieve-data.ipynb", "AzureMlCli\\src\\azureml-train-core\\tests\\e2e_tests\\base_e2e.py", "AzureMlCli\\tests\\scenarios\\utilities\\helpers\\cleanup_helpers.py", "src\\azureml-api\\deploy\\specs\\test\\test-base.yaml", "src\\azureml-api\\src\\Billing\\Security.Tests\\KeyVaultSecretProviderTests.cs", "src\\azureml-job-runtime\\Hosttools\\tests\\checkin\\subscription_manager.py", "AzureMlCli\\tests\\scenarios\\utilities\\operations\\sdk\\amlcompute.py", "src\\aether\\platform\\msftkubeConfig\\deploy\\specs\\all-base.yaml", "src\\aether\\platform\\msftkubeConfig\\deploy\\specs\\dev-westus2.yaml", "AzureMlCli\\src\\azureml-core\\azureml\\core\\tests\\compute\\test_amlcompute_target.py", "src\\azureml-api\\src\\mlc\\src\\Product\\Compute.Host\\ARM-Templates\\AKS-Parameters.json", "src\\azureml-api\\src\\Compute\\Core\\src\\Client\\Swagger\\Generated\\BatchAI.generated.cs", "src\\azureml-api\\src\\Compute\\Core\\src\\Protocol\\VmComputeProvider\\VmComputeProviderClient.cs", "src\\aether\\platform\\backendV2\\BlueBox\\Clouds\\DataTransferV2Cloud\\Services\\Deploy\\specs\\all-base-datatransferv2cloud.yaml", "src\\aether\\platform\\backendV2\\shared\\SharedConfigurationSettings\\SharedSettings.ini"], "_justification": "Official Builds for MLTeamAccounts extension. These files are irrelevant."}]}