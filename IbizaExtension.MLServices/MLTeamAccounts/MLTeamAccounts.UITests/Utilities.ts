import Configuration = require("./Configuration");

class Utilities {

    public static generateWorkspaceName(): string {
        return "TestWs-" + new Date().valueOf().toString();
    }

    public static getMlServicesBladeName(bladeTitle: string): string {
        return Utilities.getBladeName(Configuration.mlServiceName, bladeTitle);
    }

    private static getBladeName(accountName: string, bladeTitle: string): string {
        return `${accountName} - ${bladeTitle}`;
    }
}

export = Utilities;