import Constants = require("../Constants");
import testFx = require('MsPortalFx-Test');
import Q = require('q');

import By = testFx.Locators.By;
import TextField = testFx.Controls.TextField;
import GroupDropDown = testFx.Controls.GroupDropDownField;
import ResourceGroupDropDown = testFx.Controls.ResourceGroupDropDownField;

class CreateMachineLearningServicesBlade extends testFx.Blades.CreateBlade {
    public workspaceAccountNameField = this.field(TextField, { label: "Workspace Name" });
    public resourceGroupField = this.field(ResourceGroupDropDown, { label: "Resource group" });
    public locationField = this.field(GroupDropDown, { label: "Location" });

    public fillFields(workspaceName: string, resourceGroupName: string, newResourceGroup: boolean, location?: string): Q.Promise<void> {
        return this.waitUntilBladeAndAllTilesLoaded()
            .then(() => {
                this.workspaceAccountNameField.sendKeys(workspaceName)
            })
            .then(() => {
                return newResourceGroup ?
                    this.resourceGroupField.setNewResourceGroup(resourceGroupName) :
                    this.resourceGroupField.setSelectedResourceGroup(resourceGroupName);
            })
            .then(() => {
                return this.locationField.selectOptionByText(location)
            });
    }

}

export = CreateMachineLearningServicesBlade;