import nconf = require('nconf');

class Configuration {

    public static signInEmail: string;
    public static signInPassword: string;
    public static mlServicesTestResourceGroup: string;
    public static defaultLocation: string;
    public static mlServiceName: string;
    public static portalUrl: string;
    public static extensionUrl: string;

    private static testEnvironmentUrls = {
        "local" : {
            "portalUrl": "https://portal.azure.com",
            "extensionUrl": "https://localhost:44300/"
        },
        "dogfood-sideload-mpac": {
            "portalUrl": "https://ms.portal.azure.com",
            "extensionUrl": "https://hosting.onecloud.azure-test.net/mlserviceworkspaces/"
        },
        "dogfood-sideload-prod": {
            "portalUrl": "https://portal.azure.com",
            "extensionUrl": "https://hosting.onecloud.azure-test.net/mlserviceworkspaces/"
        },
        "mpac": {
            "portalUrl": "https://ms.portal.azure.com"
        },
        "prod": {
            "portalUrl": "https://portal.azure.com"
        }
    }


    public static initialize() {
        // Load command line arguments, environment variables and config.json into nconf
        nconf.argv().env().file(__dirname + "/config.json");

        this.signInEmail = nconf.get("USER_EMAIL");
        this.signInPassword = nconf.get("USER_PASSWORD");
        this.mlServicesTestResourceGroup = nconf.get("mlServicesTestResourceGroup");
        this.mlServiceName = nconf.get("mlServiceName");
        this.defaultLocation =  nconf.get("defaultLocation");
        
        const testEnvironment = nconf.get("TEST_ENVIRONMENT");
        this.portalUrl = this.testEnvironmentUrls[testEnvironment].portalUrl;
        this.extensionUrl = this.testEnvironmentUrls[testEnvironment].extensionUrl;

    }
}

export = Configuration;