// Timeout periods (in milliseconds)
export const PortalTimeoutPeriods = {
    // Used for all wait operations inside the extension
    Normal: 30000,

    // Used for the initial loading of the portal and extension
    Large: 90000,

    ExtraLarge: 180000
};

export const createMlServicesBladeName = "Machine Learning";

export const mlServiceResourceProvider = "Microsoft.MachineLearningServices";

export const mlServicesResourceType = "workspaces";

export const mlServicesBrowseBladeName = "Machine Learning";

export const MochaTestTimeout = 240000;

export const TestRetryCount = 3;