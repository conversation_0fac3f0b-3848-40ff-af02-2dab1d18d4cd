declare module 'MsPortalFx-Test/Locators/Locator' {
	import webdriver = require("selenium-webdriver"); abstract class Locator {
	    /**
	     * Schedules a command to search for multiple elements on the page.
	     * @param {webdriver.WebElement | webdriver.WebDriver} context The object used as a starting point for the search.
	     * @returns An array of the webdriver.WebElement objects that were found.
	     */
	    abstract findElements(context: webdriver.WebElement | webdriver.WebDriver): webdriver.promise.Promise<webdriver.WebElement[]>;
	    /**
	     * Gets the string representation of this locator.
	     * @returns The string representation of this locator.
	     */
	    abstract toString(): string;
	}
	export = Locator;

}
declare module 'MsPortalFx-Test/Locators/SimpleLocator' {
	import webdriver = require("selenium-webdriver");
	import Locator = require('MsPortalFx-Test/Locators/Locator'); class SimpleLocator extends Locator {
	    private locator;
	    /**
	     * Creates a new instance of this class.
	     * @param locator The locator strategy to use when searching for elements.
	     */
	    constructor(locator: webdriver.Locator | ((webDriver: webdriver.WebDriver) => webdriver.promise.Promise<any>));
	    /**
	     * Schedules a command to search for multiple elements on the page.
	     * @param {webdriver.WebElement | webdriver.WebDriver} context The object used as a starting point for the search.
	     * @returns An array of the webdriver.WebElement objects that were found.
	     */
	    findElements(context: webdriver.WebElement | webdriver.WebDriver): webdriver.promise.Promise<webdriver.WebElement[]>;
	    /**
	     * Gets the string representation of this locator.
	     * @returns The string representation of this locator.
	     */
	    toString(): string;
	}
	export = SimpleLocator;

}
declare module 'MsPortalFx-Test/Locators/ChainedLocator' {
	import webdriver = require("selenium-webdriver");
	import Locator = require('MsPortalFx-Test/Locators/Locator'); class ChainedLocator extends Locator {
	    private locators;
	    /**
	     * Creates a new instance of this class.
	     * @param {Locator[]} innerLocators The collection of locators used to find elements.
	     */
	    constructor(innerLocators: Locator[]);
	    /**
	     * Schedules a command to search for multiple elements on the page.
	     * @param {webdriver.WebElement | webdriver.WebDriver} context The object used as a starting point for the search.
	     * @returns An array of the webdriver.WebElement objects that were found.
	     */
	    findElements(context: webdriver.WebElement | webdriver.WebDriver): webdriver.promise.Promise<webdriver.WebElement[]>;
	    /**
	     * Gets the string representation of this locator.
	     * @returns The string representation of this locator.
	     */
	    toString(): string;
	    private findElementsInternal;
	}
	export = ChainedLocator;

}
declare module 'MsPortalFx-Test/Locators/ContentLocator' {
	import webdriver = require("selenium-webdriver");
	import Locator = require('MsPortalFx-Test/Locators/Locator'); class ContentLocator extends Locator {
	    private locators;
	    /**
	     * Creates a new instance of this class.
	     * @param {Locator[]} innerLocators The collection of locators used to find elements.
	     */
	    constructor(innerLocators: Locator[]);
	    /**
	     * Schedules a command to search for multiple elements on the page.
	     * @param {webdriver.WebElement | webdriver.WebDriver} context The object used as a starting point for the search.
	     * @returns An array of the webdriver.WebElement objects that were found.
	     */
	    findElements(context: webdriver.WebElement | webdriver.WebDriver): webdriver.promise.Promise<webdriver.WebElement[]>;
	    /**
	     * Gets the string representation of this locator.
	     * @returns The string representation of this locator.
	     */
	    toString(): string;
	    private findElementsInternal;
	}
	export = ContentLocator;

}
declare module 'MsPortalFx-Test/Utils/String' {
	export function format(value: string, ...restArgs: any[]): string;
	export function localeCompareIgnoreCase(value: string, locales?: string[], options?: any): number;
	export function repeat(count: number): string;
	export function startsWith(source: string, searchString: string): boolean;
	export function endsWith(value: string): boolean;

}
declare module 'MsPortalFx-Test/Locators/By' {
	import Locator = require('MsPortalFx-Test/Locators/Locator'); class By {
	    /**
	     * Locates elements that have a specific class name. The returned locator
	     * is equivalent to searching for elements with the CSS selector ".class".
	     * @param {string} value The class name to search for.
	     * @returns The new locator.
	     */
	    static className(value: string): Locator;
	    /**
	     * Locates elements using a CSS selector. For browsers that do not support
	     * CSS selectors, WebDriver implementations may return an
	     * {@linkplain bot.Error.State.INVALID_SELECTOR invalid selector} error. An
	     * implementation may, however, emulate the CSS selector API.
	     * @param {string} value The CSS selector to use.
	     * @returns The new locator.
	     */
	    static css(value: string): Locator;
	    /**
	     * Locates an element by its ID.
	     * @param {string} value The ID to search for.
	     * @returns The new locator.
	     */
	    static id(value: string): Locator;
	    /**
	     * Locates an elements by evaluating a
	     * {@linkplain webdriver.WebDriver#executeScript JavaScript expression}.
	     * The result of this expression must be an element or list of elements.
	     * @param {any} script The script to execute.
	     * @param {any[]} ...var_args The arguments to pass to the script.
	     * @returns A new, JavaScript-based locator function.
	     */
	    static js(script: any, ...var_args: any[]): Locator;
	    /**
	     * Locates link elements whose {@linkplain webdriver.WebElement#getText visible
	     * text} matches the given string.
	     * @param {string} value The link text to search for.
	     * @returns The new locator.
	     */
	    static linkText(value: string): Locator;
	    /**
	     * Locates elements whose {@code nameAttribute} attribute has the given value.
	     * @param {string} value The name attribute to search for.
	     * @returns The new locator.
	     */
	    static nameAttribute(value: string): Locator;
	    /**
	     * Locates link elements whose {@linkplain webdriver.WebElement#getText visible
	     * text} contains the given substring.
	     * @param {string} value The substring to check for in a link's visible text.
	     * @returns The new locator.
	     */
	    static partialLinkText(value: string): Locator;
	    /**
	     * Locates elements with a given tag name. The returned locator is
	     * equivalent to using the
	     * [getElementsByTagName](https://developer.mozilla.org/en-US/docs/Web/API/Element.getElementsByTagName)
	     * DOM function.
	     * @param {string} value The substring to check for in a link's visible text.
	     * @returns The new locator.
	     */
	    static tagName(value: string): Locator;
	    /**
	     * Locates elements matching a XPath selector. Care should be taken when
	     * using an XPath selector with a {@link webdriver.WebElement} as WebDriver
	     * will respect the context in the specified in the selector. For example,
	     * given the selector {@code "//div"}, WebDriver will search from the
	     * document root regardless of whether the locator was used with a
	     * WebElement.
	     * @param {string} value The XPath selector to use.
	     * @returns The new locator.
	     */
	    static xpath(value: string): Locator;
	    /**
	     * Locates elements with the given text.
	     * @param {string} value The text to check for.
	     * @returns The new locator.
	     */
	    static text(value: string): Locator;
	    /**
	     * Locates elements that contain the given text.
	     * @param {string} value The text to check for.
	     * @returns The new locator.
	     */
	    static textContains(value: string): Locator;
	    /**
	     * Locates elements that match the given class name and text.
	     * @param {string} className The class name to search for.
	     * @param {string} text The text to check for.
	     * @returns The new locator.
	     */
	    static classAndText(className: string, text: string): Locator;
	    /**
	     * Locates elements that match all the specified locators. The returned locator will find the element(s) that match the last of the provided locators.
	     * @param {Locator[]} ...values The locators to search for.
	     * @returns The new locator.
	     */
	    static chained(...values: Locator[]): Locator;
	    /**
	     * Locates elements that match all the specified locators. The returned locator will find the element(s) that match the first of the provided locators.
	     * @param {Locator[]} ...values The locators to search for.
	     * @returns The new locator.
	     */
	    static content(...values: Locator[]): Locator;
	}
	export = By;

}
declare module 'MsPortalFx-Test/Accessibility' {
	import * as Q from "q";
	import { WebElement } from "selenium-webdriver";
	export type ResultFilterFunction = (result: Result) => boolean;
	export type LoggingFunction = (message: string) => void;
	export type GetAuthenticationTokenFunction = () => Q.Promise<string>;
	export interface AxeAnalysis {
	    inapplicable: Result[];
	    incomplete: Result[];
	    passes: Result[];
	    timestamp: string;
	    url: string;
	    violations: Result[];
	}
	export interface Result {
	    description: string;
	    help: string;
	    helpUrl: string;
	    id: string;
	    impact: string;
	    tags: string[];
	    nodes: NodeResult[];
	}
	export interface NodeResult {
	    html: string;
	    impact: string;
	    target: string[];
	    any: CheckResult[];
	    all: CheckResult[];
	    none: CheckResult[];
	    failureSummary?: string;
	}
	export interface CheckResult {
	    id: string;
	    impact: string;
	    message: string;
	    data: any;
	    relatedNodes?: {
	        target: string[];
	        html: string;
	    }[];
	}
	export interface Options {
	    stepName?: string;
	    friendlyElementName?: string;
	    overrideElementToInclude?: string;
	    disableRuleIds?: string[];
	    violationFilters?: Map<string, ResultFilterFunction>;
	    verboseLogging?: boolean;
	    overrideLoggingFunction?: LoggingFunction;
	    reportTestResult?: boolean;
	    getAuthenticationToken?: GetAuthenticationTokenFunction;
	}
	export function ensureAccessibilityIsAxeCoreClean(webElement: WebElement, options: Options): Q.Promise<AxeAnalysis>;
	export function setIgnoreNoExplicitLabelViolation(map: Map<string, ResultFilterFunction>): Map<string, ResultFilterFunction>;
	export function setIgnoreExplicitHiddenLabelViolation(map: Map<string, ResultFilterFunction>): Map<string, ResultFilterFunction>;
	export function setIgnoreNoExplicitLabelForRadioGroupsViolation(map: Map<string, ResultFilterFunction>): Map<string, ResultFilterFunction>;
	export function setIgnorePlaceholderViolation(map: Map<string, ResultFilterFunction>): Map<string, ResultFilterFunction>;
	export function setIgnoreDisabledButtonColorContrastViolation(map: Map<string, ResultFilterFunction>): Map<string, ResultFilterFunction>;
	export function getNode(result: Result): NodeResult;
	export function nodeHasId(result: Result, id: string): boolean;
	export function getNodeNoneCheckResults(result: Result): CheckResult[];
	export function getNodeAnyCheckResults(result: Result): CheckResult[];
	export function checkResultsHaveId(checkResults: CheckResult[], idToFind: string): boolean;
	export function checkResultsHaveIds(checkResults: CheckResult[], idsToFind: string[]): boolean;
	export function nodeHtmlHasText(result: Result, textToFind: string): boolean;
	export function nodeFailureSummaryHasText(result: Result, textToFind: string): boolean;
	export function textHasSubstring(textToSearch: string, textToFind: string): boolean;
	export function getDefaultIdsToIgnore(ids: string[]): string[];
	export function getDefaultViolationFilters(map?: Map<string, ResultFilterFunction>): Map<string, ResultFilterFunction>;

}
declare module 'MsPortalFx-Test/TestExtension' {
	/**
	 * Represents side loaded extension.
	 */
	interface TestExtension {
	    /**
	     * Extension name.
	     */
	    name: string;
	    /**
	     * Extension url.
	     */
	    uri: string;
	    /**
	     * Bitmask determining which obsolete bundles are required by the extension.
	     */
	    obsoleteBundlesBitmask?: number;
	}
	export = TestExtension;

}
declare module 'MsPortalFx-Test/Feature' {
	/**
	 * Represents a Portal feature.
	 */
	interface Feature {
	    /**
	     * The feature name.
	     */
	    name: string;
	    /**
	     * The feature value.
	     */
	    value: string;
	}
	export = Feature;

}
declare module 'MsPortalFx-Test/BrowserResolution' {
	/**
	 * Represents a browser's resolution (size).
	 */
	interface BrowserResolution {
	    /**
	     * Width in pixels.
	     */
	    width: number;
	    /**
	     * Height in pixels.
	     */
	    height: number;
	}
	export = BrowserResolution;

}
declare module 'MsPortalFx-Test/Timeout' {
	/**
	 * Represents a user defined timeout.
	 */
	interface Timeout {
	    /**
	     * The shorter timeout that most functions will use.
	     */
	    timeout?: number;
	    /**
	     * The longer timeout that longer running operations will use.
	     */
	    longTimeout?: number;
	}
	export = Timeout;

}
declare module 'MsPortalFx-Test/PortalContext' {
	import TestExtension = require('MsPortalFx-Test/TestExtension');
	import Feature = require('MsPortalFx-Test/Feature');
	import BrowserResolution = require('MsPortalFx-Test/BrowserResolution');
	import Timeout = require('MsPortalFx-Test/Timeout');
	/**
	 * Represents The set of options used to configure a Portal instance.
	 */
	interface PortalContext {
	    /**
	     * The set of capabilities enabled in the webdriver session.
	     * For a list of available capabilities, see https://github.com/SeleniumHQ/selenium/wiki/DesiredCapabilities
	     */
	    capabilities: {
	        /**
	         * The name of the browser being used; should be one of {chrome}
	         */
	        browserName: string;
	        /**
	         * Chrome-specific supported capabilities.
	         */
	        chromeOptions: {
	            /**
	             * List of command-line arguments to use when starting Chrome.
	             */
	            args: string[];
	        };
	        /**
	         * The desired starting browser's resolution in pixels.
	         */
	        browserResolution: BrowserResolution;
	    };
	    /**
	     * The path to the ChromeDriver binary.
	     */
	    chromeDriverPath?: string;
	    /**
	     * The url of the Portal.
	     */
	    portalUrl: string;
	    /**
	     * The url of the page where signin is performed.
	     */
	    signInUrl?: string;
	    /**
	     * Email of the user used to sign in to the Portal.
	     */
	    signInEmail?: string;
	    /**
	     * Password of the user used to sign in to the Portal.
	     */
	    signInPassword?: string;
	    /**
	     * The set of features to enable while navigating within the Portal.
	     */
	    features?: Feature[];
	    /**
	     * The list of patch files to load within the Portal.
	     */
	    patches?: string[];
	    /**
	     * The set of extensions to side load while navigating within the Portal.
	     */
	    testExtensions?: TestExtension[];
	    /**
	     * The set of timeouts used to override the default timeouts.
	     * e.g.
	     * timeouts: {
	     *      timeout: 15000  //Overrides the default short timeout of 10000 (10 seconds).
	     *      longTimeout: 70000 //Overrides the default long timetout of 60000 (60 seconds).
	     * }
	     */
	    timeouts?: Timeout;
	}
	export = PortalContext;

}
declare module 'MsPortalFx-Test/Utils/Timeouts' {
	/**
	 * The default timeout used across msportalfx-test. Checks for user value otherwise defaults to 10 seconds.
	 */
	export let defaultTimeout: number;
	/**
	 * The defaut long timeout used for actions that are expected to take longer than normal (eg loading the portal).
	 * Checks for user value otherwise defaults to 60 seconds.
	 */
	export let defaultLongTimeout: number;

}
declare module 'MsPortalFx-Test/ElementArrayFinder' {
	/** ------------------------------------------- START OF THIRD PARTY NOTICE -----------------------------------------
	 * This file is based on or incorporates material from the projects listed below (Third Party IP).The original copyright notice and the license under which Microsoft received such Third Party IP, are set forth below.Such licenses and notices are provided for informational purposes only.Microsoft licenses the Third Party IP to you under the licensing terms for the Microsoft product.Microsoft reserves all other rights not expressly granted under this agreement, whether by implication, estoppel or otherwise.
	 *
	 * angular - protractor v3.0
	 * Copyright(c) 2010- 2015 Google, Inc.
	 *
	 * Provided for Informational Purposes Only
	 * MIT License
	 *
	 * Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the Software), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and / or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:
	 *
	 * The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.
	 *
	 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
	 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
	 * NONINFRINGEMENT.IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE
	 * LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION
	 * OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION
	 * WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
	 * ----------------------------------------------- END OF THIRD PARTY NOTICE ------------------------------------------
	 */
	import webdriver = require("selenium-webdriver");
	import Locator = require('MsPortalFx-Test/Locators/Locator');
	import PortalElement = require('MsPortalFx-Test/PortalElement');
	import Q = require("q"); class ElementArrayFinder<T extends PortalElement> {
	    /**
	     * A function that returns a list of the underlying Web Elements.
	     */
	    getWebElements: (wait?: boolean) => Q.Promise<webdriver.WebElement[]>;
	    /**
	     * The locator specified when creating this ElementArrayFinder instance.
	     */
	    locator: Locator;
	    /** An array of promises which will be retrieved with then. Resolves to the latest
	     *  action result, or null if no action has been called.
	     */
	    actionResults: any;
	    private elementType;
	    /**
	     * Creates a new instance of this class.
	     * @param getWebElements A function that returns a list of the underlying Web Elements.
	     * @param locator The locator specified when creating this ElementArrayFinder instance.
	     * @param elementType The type of PortalElement that this ElementArrayFinder can produce.
	     * @param actionResults An array of promises which will be retrieved with then. Resolves to the latest
	     *  action result, or null if no action has been called.
	     */
	    constructor(getWebElements?: (wait?: boolean) => Q.Promise<webdriver.WebElement[]>, locator?: Locator, elementType?: {
	        new (elementArrayFinder?: ElementArrayFinder<T>): T;
	    }, actionResults?: any);
	    /**
	     * Returns a new ElementArrayFinder which would contain the children elements found (and could also be empty).
	     * @param locatorOrElementType The sublocator or the type of element from which the sublocator will be extracted.
	     * @returns A new ElementArrayFinder.
	     */
	    all<U extends PortalElement>(locatorOrElementType: Locator | {
	        new (elementArrayFinder?: ElementArrayFinder<U>): U;
	    }): ElementArrayFinder<U>;
	    /**
	     * Applies a filter function to each element within the ElementArrayFinder. This does not actually retrieve
	     *      the underlying list of elements, so it can be used in page objects.
	     * @param filterFn Filter function that will test if an element should be returned. filterFn can either
	     *      return a boolean or a promise that resolves to a boolean.
	     * @returns A ElementArrayFinder that represents an array of elements that satisfy the filter function.
	     */
	    filter(filterFn: (portalElement: T, index: number) => Q.Promise<any>): ElementArrayFinder<T>;
	    /**
	     * Determines whether the specified function returns true for any element within the ElementArrayFinder.
	     * @param fn Function that will be tested. fn can either return a boolean or a promise that resolves to a boolean.
	     * @returns A promise that resolves with a value that determines if the function returned true for any element within the ElementArrayFinder.
	     */
	    some(fn: (portalElement: T, index: number) => Q.Promise<any>): Q.Promise<boolean>;
	    /**
	     * Gets an element within the ElementArrayFinder by index. The index starts at 0.
	     * Negative indices are wrapped (i.e. -i means ith element from last)
	     * This does not actually retrieve the underlying element.
	     * @param {number} index Element index.
	     * @returns The PortalElement that represents the element at the given index.
	     */
	    get(index: number | Q.Promise<any>): T;
	    /**
	     * Get the first matching element for the ElementArrayFinder. This does not actually retrieve the underlying element.
	     * @returns The PortalElement that represents the first matching element.
	     */
	    first(): T;
	    /**
	     * Returns a PortalElement representation of this ElementArrayFinder. It ensures
	     * that the ElementArrayFinder resolves to one and only one underlying element.
	     * @returns A PortalElement representation.
	     */
	    toPortalElement(): T;
	    /**
	     * Counts the number of elements represented by the ElementArrayFinder.
	     * @returns A promise which resolves to the number of elements matching the locator.
	     */
	    count(): Q.Promise<number>;
	    /**
	     * Represents the ElementArrayFinder as an array of PortalElements.
	     * @returns A promise which resolves to a list of PortalElements specified by the locator.
	     */
	    asPortalElements(): Q.Promise<T[]>;
	    /**
	     * Retrieves the elements represented by the ElementArrayFinder. The input
	     * function is passed to the resulting promise, which resolves to an
	     * array of PortalElements.
	     * @param fn Function passed to the resulting promise.
	     * @param errorFn Error function.
	     * @returns A promise which will resolve to an array of ElementFinders represented by the ElementArrayFinder.
	     */
	    then<U>(fn: (portalElements: T[]) => U | Q.IPromise<U>, errorFn?: (error: any) => any): Q.Promise<U>;
	    /**
	     * Calls the input function on each PortalElement represented by the ElementArrayFinder.
	     * @param fn Input function.
	     */
	    each(fn: (portalElement: T, index: number) => any): Q.Promise<void>;
	    /**
	     * Applies a map function to each element within the ElementArrayFinder. The
	     * callback receives the PortalElement as the first argument and the index as
	     * a second arg.
	     * @param mapFn Map function that will be applied to each element.
	     * @returns A promise that resolves to an array of values returned by the map function.
	     */
	    map(mapFn: (portalElement: T, index: number) => any): Q.Promise<any[]>;
	    /**
	     * Invokes the click function on the underlying web elements.
	     * @returns A promise that resolves when the invoked function resolves.
	     */
	    click(): Q.Promise<void[]>;
	    /**
	     * Invokes a right click on the underlying web elements.
	     * @returns A promise that resolves when the right click resolves.
	     */
	    rightClick(): Q.Promise<void[]>;
	    /**
	     * Invokes the getAttribute function on the underlying web elements.
	     * @param {string} attributeName The attribute to retrieve.
	     * @returns A promise that resolves when the invoked function resolves.
	     */
	    getAttribute(attributeName: string): Q.Promise<string[]>;
	    /**
	     * Invokes the sendKeys function on the underlying web elements.
	     * @param {string} var_args The text to send.
	     * @returns A promise that resolves when the invoked function resolves.
	     */
	    sendKeys(...var_args: string[]): Q.Promise<void[]>;
	    /**
	     * Schedules a command to clear the value of this element.
	     * @returns A promise that will be resolved when the element has been cleared.
	     */
	    clear(): Q.Promise<void[]>;
	    /**
	     * Invokes the getText function on the underlying web elements.
	     * @returns A promise that resolves when the invoked function resolves.
	     */
	    getText(): Q.Promise<string[]>;
	    /**
	     * Invokes the isDisplayed function on the underlying web elements.
	     * @returns A promise that resolves when the invoked function resolves.
	     */
	    isDisplayed(): Q.Promise<boolean[]>;
	    private applyAction;
	    private waitForElementLocated;
	}
	export = ElementArrayFinder;

}
declare module 'MsPortalFx-Test/IPortalElementRetryOptions' {
	/**
	 * options used for retrying a portal element.
	 */
	interface IPortalElementRetryOptions {
	    /**
	     * the remaining number of times to retry.
	     */
	    remainingAttempts: number;
	    /**
	     * the amount of time to wait bewteen attempts
	     */
	    delay: number;
	}
	export = IPortalElementRetryOptions;

}
declare module 'MsPortalFx-Test/PortalElement' {
	/** ------------------------------------------- START OF THIRD PARTY NOTICE -----------------------------------------
	 * This file is based on or incorporates material from the projects listed below (Third Party IP).The original copyright notice and the license under which Microsoft received such Third Party IP, are set forth below.Such licenses and notices are provided for informational purposes only.Microsoft licenses the Third Party IP to you under the licensing terms for the Microsoft product.Microsoft reserves all other rights not expressly granted under this agreement, whether by implication, estoppel or otherwise.
	 *
	 * angular - protractor v3.0
	 * Copyright(c) 2010- 2015 Google, Inc.
	 *
	 * Provided for Informational Purposes Only
	 * MIT License
	 *
	 * Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the Software), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and / or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:
	 *
	 * The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.
	 *
	 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
	 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
	 * NONINFRINGEMENT.IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE
	 * LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION
	 * OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION
	 * WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
	 * ----------------------------------------------- END OF THIRD PARTY NOTICE ------------------------------------------
	 */
	import Q = require("q");
	import webdriver = require("selenium-webdriver");
	import { AxeAnalysis, Options as AccessibilityOptions } from 'MsPortalFx-Test/Accessibility';
	import ElementArrayFinder = require('MsPortalFx-Test/ElementArrayFinder');
	import IPortalElementRetryOptions = require('MsPortalFx-Test/IPortalElementRetryOptions');
	import Locator = require('MsPortalFx-Test/Locators/Locator'); class PortalElement {
	    /**
	     * The ElementArrayFinder used to locate elements.
	     */
	    parentElementArrayFinder: ElementArrayFinder<PortalElement>;
	    private elementArrayFinder;
	    private webElement;
	    /**
	     * Creates a PortalElement from the specified webdriver.WebElement.
	     * @param {webdriver.WebElement} webElem The webdriver.WebElement.
	     * @param {Locator} locator Reference locator originaly used to find the webdriver.WebElement.
	     * @param {{ new (elementArrayFinder?} elementType The type of PortalElement from which the locator was extracted.
	     * @returns The PortalElement.
	     */
	    static fromWebElement<T extends PortalElement>(webElem: webdriver.WebElement, locator: Locator, elementType: {
	        new (elementArrayFinder?: ElementArrayFinder<T>): T;
	    }): T;
	    /**
	     * Creates a new instance of this class.
	     * @param elementArrayFinder The ElementArrayFinder used to locate elements.
	     */
	    constructor(elementArrayFinder?: ElementArrayFinder<PortalElement>);
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	    /**
	     * Gets a PortalElement that matches the specified sub locator or element type.
	     * @param subLocatorOrElementType The sub locator or element type.
	     * @returns A PortalElement that matches the specified sub locator or element type.
	     */
	    element<T extends PortalElement>(subLocatorOrElementType: Locator | {
	        new (): T;
	    }): T;
	    /**
	     * Gets an ElementArrayFinder that matches the specified sub locator or element type.
	     * @param subLocatorOrElementType The sub locator or element type.
	     * @returns An ElementArrayFinder that matches the specified sub locator or element type.
	     */
	    all<T extends PortalElement>(subLocatorOrElementType: Locator | {
	        new (): T;
	    }): ElementArrayFinder<T>;
	    /**
	     * Transforms this PortalElement instance into the specified type. A new object is created as opposed to only casting to the specified type.
	     * @param targetType The type into which the PortalElement will be transformed.
	     * @returns A PortalElement of the specified type.
	     */
	    asType<T extends PortalElement>(targetType?: {
	        new (elementArrayFinder: ElementArrayFinder<PortalElement>): T;
	    }): T;
	    /**
	     * Invokes the click function on the underlying web element.
	     * @param retryOptions Optional set of retry options (eg attempts and delays between attempts).  Defaults to 3 retries with a 500ms delay
	     * @returns A promise that resolves when the invoked function resolves.
	     */
	    click(retryOptions?: IPortalElementRetryOptions): Q.Promise<void>;
	    /**
	     * Invokes a right click on the underlying web element.
	     * @returns A promise that resolves when the right click resolves.
	     */
	    rightClick(): Q.Promise<void>;
	    /**
	     * Invokes the getAttribute function on the underlying web element.
	     * @param {string} attributeName The attribute to retrieve.
	     * @returns A promise that resolves when the invoked function resolves.
	     */
	    getAttribute(attributeName: string): Q.Promise<string>;
	    /**
	     * Returns a value that indicates whether this element has the specified attribute and value.
	     * @param {string} attributeName The name of the attribute.
	     * @param {string} attributeValue The value of the attribute.
	     * @returns A promise that resolves with the result of the evaluation.
	     */
	    hasAttributeValue(attributeName: string, attributeValue: string): Q.Promise<boolean>;
	    /**
	     * Returns a value that indicates whether this element does not have the specified attribute and value.
	     * @param {string} attributeName The name of the attribute.
	     * @param {string} attributeValue The value of the attribute.
	     * @returns A promise that resolves with the result of the evaluation.
	     */
	    doesNotHaveAttributeValue(attributeName: string, attributeValue: string): Q.Promise<boolean>;
	    /**
	     * Invokes the sendKeys function on the underlying web element.
	     * @param {string} var_args The text to send.
	     * @returns A promise that resolves when the invoked function resolves.
	     */
	    sendKeys(...var_args: string[]): Q.Promise<void>;
	    /**
	     * Schedules a command to clear the value of this element.
	     * @returns A promise that will be resolved when the element has been cleared.
	     */
	    clear(): Q.Promise<void>;
	    /**
	     * Invokes the getText function on the underlying web element.
	     * @returns A promise that resolves when the invoked function resolves.
	     */
	    getText(): Q.Promise<string>;
	    /**
	     * Invokes the isDisplayed function on the underlying web element.
	     * @returns A promise that resolves when the invoked function resolves.
	     */
	    isDisplayed(): Q.Promise<boolean>;
	    /**
	     * Opposite of isDisplayed.
	     * @returns A promise that resolves when the invoked function resolves.
	     */
	    isNotDisplayed(): Q.Promise<boolean>;
	    /**
	     * Determine whether the element is present on the Portal.
	     * @returns A promise that resolves with a value that indicates whether the element is present on the Portal.
	     */
	    isPresent(): Q.Promise<boolean>;
	    /**
	     * Determine whether the element is not present on the Portal.
	     * @returns A promise that resolves with a value that indicates whether the element is not present on the Portal.
	     */
	    isNotPresent(): Q.Promise<boolean>;
	    /**
	     * Same as PortalElement.isPresent(), except this checks whether the element identified by the subLocator
	     * is present, rather than the current PortalElement.
	     * @param {Locator} subLocator Locator for element to look for.
	     * @returns A promise that resolves with a value that indicates whether the element is present on the Portal.
	     */
	    isElementPresent(subLocator: Locator): Q.Promise<boolean>;
	    /**
	     * Checks whether the element identified by the subLocator
	     * is present and displayed, rather than the current PortalElement.
	     * @param {Locator} subLocator Locator for element to look for.
	     * @returns A promise that resolves with a value that indicates whether the element is present and displayed on the Portal.
	     */
	    isElementDisplayed(subLocator: Locator): Q.Promise<boolean>;
	    /**
	     * Checks whether the element identified by the subLocator
	     * is not present or not displayed, rather than the current PortalElement.
	     * @param {Locator} subLocator Locator for element to look for.
	     * @returns A promise that resolves with a value that indicates whether the element is not present or not displayed on the Portal.
	     */
	    isElementNotDisplayed(subLocator: Locator): Q.Promise<boolean>;
	    /**
	     * Returns a value to specify whether or not the element has a given CSS class.
	     * @param {string} className The CSS class name.
	     * @returns True if the element has the class; false otherwise.
	     */
	    hasClass(className: string): Q.Promise<boolean>;
	    /**
	     * Returns the base webdriver WebElements for this element.
	     * @returns A promise that resolves with the webdriver WebElement
	     */
	    getWebElements(): Q.Promise<webdriver.WebElement[]>;
	    ensureAccessibilityIsAxeCoreClean(options: AccessibilityOptions): Q.Promise<AxeAnalysis>;
	}
	export = PortalElement;

}
declare module 'MsPortalFx-Test/ActionBars/ActionBarButton' {
	import Q = require("q");
	import PortalElement = require('MsPortalFx-Test/PortalElement');
	import Locator = require('MsPortalFx-Test/Locators/Locator'); class ActionBarButton extends PortalElement {
	    private innerButton;
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	    /**
	     * Returns a value that indicates whether the button is enabled.
	     * @returns A promise that resolves with a value that indicates whether the button is enabled.
	     */
	    isEnabled(): Q.Promise<boolean>;
	}
	export = ActionBarButton;

}
declare module 'MsPortalFx-Test/ActionBars/ActionBar' {
	import Locator = require('MsPortalFx-Test/Locators/Locator');
	import PortalElement = require('MsPortalFx-Test/PortalElement');
	import ActionBarButton = require('MsPortalFx-Test/ActionBars/ActionBarButton'); class ActionBar extends PortalElement {
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	    protected button(options: {
	        locator: Locator;
	    }): ActionBarButton;
	}
	export = ActionBar;

}
declare module 'MsPortalFx-Test/ActionBars/CreateActionBarFormElement' {
	import PortalElement = require('MsPortalFx-Test/PortalElement');
	import Locator = require('MsPortalFx-Test/Locators/Locator'); class CreateActionBarFormElement extends PortalElement {
	    protected labelSelector: string;
	    /**
	     * The FormElement label.
	     */
	    label: PortalElement;
	    /**
	     * Gets the locator associated to this element.
	     * @returns {Locator} A Locator instance.
	     */
	    readonly locator: Locator;
	    /**
	     * Builds a FormElement locator using the specified options.
	     * @param options The options used to build the locator.
	     * @returns A Locator instance.
	     */
	    buildLocator(options: {
	        label?: string;
	    }): Locator;
	}
	export = CreateActionBarFormElement;

}
declare module 'MsPortalFx-Test/ActionBars/CreateActionBarCheckBoxField' {
	import Q = require("q");
	import Locator = require('MsPortalFx-Test/Locators/Locator');
	import CreateActionBarFormElement = require('MsPortalFx-Test/ActionBars/CreateActionBarFormElement'); class CreateActionBarCheckBoxField extends CreateActionBarFormElement {
	    private checkBox;
	    /**
	     * Gets the locator associated to this element.
	     * @returns {Locator} A Locator instance.
	     */
	    readonly locator: Locator;
	    /**
	     * Clicks the checkbox.
	     * @returns A promise that resolves when the checkbox has been clicked.
	     */
	    click(): Q.Promise<void>;
	}
	export = CreateActionBarCheckBoxField;

}
declare module 'MsPortalFx-Test/PortalFxResources' {
	export const activityLog = "Activity log";
	export const abort = "Abort";
	export const allow = "Allow";
	export const browse = "Browse";
	export const browseAll = "Browse All";
	export const cancel = "Cancel";
	export const chooseColumns = "Choose columns";
	export const chooseColumnsV2 = "Edit columns";
	export const columns = "Edit columns";
	export const createANewGroup = "Create a new group";
	export const ignore = "Ignore";
	export const localDevelopment = "Local Development";
	export const metrics = "Metrics";
	export const move = "Move";
	export const moveResources = "Move resources";
	export const movingResources = "Moving resources";
	export const movingResourcesComplete = "Moving resources complete";
	export const moveResourceConfirm = "I understand that tools and scripts associated with moved resources will not work until I update them to use new resource IDs";
	export const movingResourcesFailed = "Moving resources failed";
	export const nameLabel = "Name";
	export const no = "No";
	export const ok = "Ok";
	export const overview = "Overview";
	export const pinnedToDashboard = "Pinned to dashboard.";
	export const pinToDashboard = "Pin to dashboard";
	export const properties = "Properties";
	export const quickStart = "Quick start";
	export const quickStartMenu = "Quickstart";
	export const refresh = "Refresh";
	export const resourceGroup = "Resource group";
	export const retry = "Retry";
	export const searchPlaceholder = "Search resources";
	export const selectExistingResourceGroup = "Select existing resource group";
	export const settings = "Settings";
	export const signInPageTitle = "Sign in to Azure Portal";
	export const signInWithUserAndPasswordInstead = "Sign in with a username and password instead";
	export const subscription = "Subscription";
	export const users = "Users";
	export const usersMenu = "Access control (IAM)";
	export const yes = "Yes";
	export const untrustedExtension = "Untrusted Extensions!";
	export const untrustedPatch = "Untrusted Patch!";
	export const hubsExtension: {
	    resourceGroups: {
	        applyButton: string;
	        cancelButton: string;
	        createButton: string;
	        createTitle: string;
	        deleteButton: string;
	        deleteConfirmation: string;
	        locationLabel: string;
	        nameLabel: string;
	        resetButton: string;
	        reviewCreateButton: string;
	    };
	};

}
declare module 'MsPortalFx-Test/ActionBars/CreateActionBar' {
	import Locator = require('MsPortalFx-Test/Locators/Locator');
	import ActionBar = require('MsPortalFx-Test/ActionBars/ActionBar');
	import ActionBarButton = require('MsPortalFx-Test/ActionBars/ActionBarButton');
	import CreateActionBarCheckBoxField = require('MsPortalFx-Test/ActionBars/CreateActionBarCheckBoxField');
	import CreateActionBarFormElement = require('MsPortalFx-Test/ActionBars/CreateActionBarFormElement'); class CreateActionBar extends ActionBar {
	    /**
	     * The create button.
	     */
	    createButton: ActionBarButton;
	    /**
	     * The pin to dashboard button.
	     */
	    pinToDashboardCheckbox: CreateActionBarCheckBoxField;
	    /**
	     * Gets a field contained in this blade that matches the given options.
	     * @param fieldType The type of field.
	     * @param options The options used to locate the field.
	     * @returns An instance of the field.
	     */
	    field<T extends CreateActionBarFormElement>(fieldType: {
	        new (): T;
	    }, options: {
	        label: string;
	    }): T;
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	}
	export = CreateActionBar;

}
declare module 'MsPortalFx-Test/ActionBars/DeleteActionBar' {
	import Locator = require('MsPortalFx-Test/Locators/Locator');
	import ActionBar = require('MsPortalFx-Test/ActionBars/ActionBar');
	import ActionBarButton = require('MsPortalFx-Test/ActionBars/ActionBarButton'); class DeleteActionBar extends ActionBar {
	    /**
	     * The delete button.
	     */
	    deleteButton: ActionBarButton;
	    /**
	     * The cancel button.
	     */
	    cancelButton: ActionBarButton;
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	}
	export = DeleteActionBar;

}
declare module 'MsPortalFx-Test/Constants' {
	/**
	 * enum to represetnt the validation state of a control
	 */
	export enum ControlValidationState {
	    invalid = 1,
	    none = 2,
	    pending = 3,
	    valid = 4
	}
	/**
	 * enum to represetnt the type of splash screen dialog
	 */
	export enum SplashScreenDialogType {
	    extension = 0,
	    patch = 1
	}
	/**
	 * Css Class Names that can be used in locators
	 */
	export const CssClassNames: {
	    ActionBars: {
	        FilterActionBar: {
	            control: string;
	            resetButton: string;
	            updateButton: string;
	        };
	    };
	    Blades: {
	        bladeLoadingStatus: string;
	        bladeProgress: string;
	        bladeProgressBar: string;
	        bladeContentStatePrefix: string;
	        bladeStatusBar: string;
	    };
	    Controls: {
	        buttonDisabled: string;
	        copyableLabel: string;
	        copyableLabelCopyButton: string;
	        editor: string;
	        editableCombo: string;
	        editableComboDropImage: string;
	        essentials: string;
	        essentialsAccordion: string;
	        essentialsItem: string;
	        essentialsLabel: string;
	        essentialsMove: string;
	        essentialsValue: string;
	        essentialsValueContainer: string;
	        essentialsViewAllButton: string;
	        essentialsViewAllContainer: string;
	        filterComboField: string;
	        inputField: string;
	        optionsGroupField: string;
	        edited: string;
	        ValidationState: {
	            invalid: string;
	            none: string;
	            pending: string;
	            valid: string;
	        };
	        pillList: string;
	        pillListContainer: string;
	        pillListItem: string;
	        pillListOverflowItem: string;
	        pillListActionButton: string;
	        monitorChart: string;
	        monitorChartMetric: string;
	        monitorChartValue: string;
	        monitorChartUnit: string;
	        monitorChartData: string;
	    };
	    Forms: {
	        formControl: string;
	        disabledState: string;
	    };
	    Grids: {
	        gridHeader: string;
	    };
	    Parts: {
	        collectionPart: string;
	        collectionPartRollupCount: string;
	        collectionPartRollupLabel: string;
	        collectionPartHasMoreData: string;
	        collectionPartTitle: string;
	        collectionSummary: string;
	        collectionSummaryCount: string;
	        collectionSummaryTitle: string;
	        donutPart: string;
	        donutValue: string;
	        donutUnit: string;
	        donutCaption: string;
	        propertiesPart: string;
	    };
	    SearchMenu: {
	        searchMenu: string;
	        mruItems: string;
	        searchResults: string;
	        resultDetails: string;
	        resultTitle: string;
	        resultType: string;
	        resultSubName: string;
	    };
	};

}
declare module 'MsPortalFx-Test/ActionBars/FilterActionBar' {
	import Locator = require('MsPortalFx-Test/Locators/Locator');
	import ActionBar = require('MsPortalFx-Test/ActionBars/ActionBar');
	import ActionBarButton = require('MsPortalFx-Test/ActionBars/ActionBarButton'); class FilterActionBar extends ActionBar {
	    /**
	     * The update button.
	     */
	    updateButton: ActionBarButton;
	    /**
	     * The reset button.
	     */
	    resetButton: ActionBarButton;
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	}
	export = FilterActionBar;

}
declare module 'MsPortalFx-Test/ActionBars/GenericActionBar' {
	import Locator = require('MsPortalFx-Test/Locators/Locator');
	import ActionBar = require('MsPortalFx-Test/ActionBars/ActionBar');
	import ActionBarButton = require('MsPortalFx-Test/ActionBars/ActionBarButton'); class GenericActionBar extends ActionBar {
	    /**
	     * The action button.
	     */
	    actionButton: ActionBarButton;
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	}
	export = GenericActionBar;

}
declare module 'MsPortalFx-Test/ActionBars/PickerActionBar' {
	import Locator = require('MsPortalFx-Test/Locators/Locator');
	import ActionBar = require('MsPortalFx-Test/ActionBars/ActionBar');
	import ActionBarButton = require('MsPortalFx-Test/ActionBars/ActionBarButton'); class PickerActionBar extends ActionBar {
	    /**
	     * The select button.
	     */
	    selectButton: ActionBarButton;
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	}
	export = PickerActionBar;

}
declare module 'MsPortalFx-Test/ActionBars' {
	export import ActionBar = require('MsPortalFx-Test/ActionBars/ActionBar');
	export import ActionBarButton = require('MsPortalFx-Test/ActionBars/ActionBarButton');
	export import CreateActionBar = require('MsPortalFx-Test/ActionBars/CreateActionBar');
	export import DeleteActionBar = require('MsPortalFx-Test/ActionBars/DeleteActionBar');
	export import FilterActionBar = require('MsPortalFx-Test/ActionBars/FilterActionBar');
	export import GenericActionBar = require('MsPortalFx-Test/ActionBars/GenericActionBar');
	export import PickerActionBar = require('MsPortalFx-Test/ActionBars/PickerActionBar');

}
declare module 'MsPortalFx-Test/Commands/CommandBarItem' {
	import Q = require("q");
	import Locator = require('MsPortalFx-Test/Locators/Locator');
	import PortalElement = require('MsPortalFx-Test/PortalElement'); class CommandBarItem extends PortalElement {
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	    /**
	     * Returns a value that indicates whether the CommandBarItem is in enabled state.
	     * @returns A promise that resolves with a value that indicates whether the CommandBarItem is in enabled state.
	     */
	    isEnabled(): Q.Promise<boolean>;
	}
	export = CommandBarItem;

}
declare module 'MsPortalFx-Test/Controls/CommandBar' {
	import Q = require("q");
	import PortalElement = require('MsPortalFx-Test/PortalElement');
	import Locator = require('MsPortalFx-Test/Locators/Locator');
	import CommandBarItem = require('MsPortalFx-Test/Commands/CommandBarItem'); class CommandBar extends PortalElement {
	    private rootClassName;
	    private expandListClassName;
	    private dropMenuContentClassName;
	    private dropMenuDisplayedClassName;
	    readonly locator: Locator;
	    /**
	     * Gets the collection of command bar items, excluding the overflow button.
	     * If there are overflow commands, the overflow button will be clicked and overflow commands will also be returned.
	     *
	     * @return Promise returning an array of CommandBarItem.
	     */
	    getItems(): Q.Promise<CommandBarItem[]>;
	    /**
	     * Gets the command bar item for the given command name.
	     * If there are overflow commands, the overflow button will be clicked and overflow command matching the command name will be returned.
	     *
	     * @param commandText Name of the command.
	     * @return Promise returning the CommandBarItem matching the given command name.
	     */
	    getItem(commandText: string): Q.Promise<CommandBarItem>;
	}
	export = CommandBar;

}
declare module 'MsPortalFx-Test/Controls/FormElement' {
	import Q = require("q");
	import PortalElement = require('MsPortalFx-Test/PortalElement');
	import Locator = require('MsPortalFx-Test/Locators/Locator');
	import Constants = require('MsPortalFx-Test/Constants');
	import ControlValidationState = Constants.ControlValidationState; class FormElement extends PortalElement {
	    private labelClass;
	    /**
	     * The FormElement label.
	     */
	    label: PortalElement;
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	    /**
	     * Gets the validation state of the element or its children
	     * @returns {ControlValidationState} the validation state if found, else none
	     */
	    getValidationState(): Q.Promise<ControlValidationState>;
	    /**
	     * Waits until the element reaches a given validation state.
	     * @param state the validation state to wait on
	     * @param timeout The amount of time to wait before failing
	     * @returns {Q.Promise<void>} promise that resolves when validationState is present
	     */
	    waitOnValidationState(validationState: ControlValidationState, timeout?: number): Q.Promise<void>;
	    /**
	     * Builds a FormElement locator using the specified options.
	     * @param options The options used to build the locator.
	     * @returns A Locator instance.
	     */
	    buildLocator(options: {
	        label?: string;
	        contains?: boolean;
	    }): Locator;
	    /**
	     * Waits for the form element to be enabled.
	     * @param timeout The amount of time to wait before failing
	     * @returns A promise that resolves when the form element is enabled
	     */
	    waitUntilEnabled(timeout?: number): Q.Promise<void>;
	    private isInValidationState;
	}
	export = FormElement;

}
declare module 'MsPortalFx-Test/Parts/Part' {
	import Q = require("q");
	import Locator = require('MsPortalFx-Test/Locators/Locator');
	import PortalElement = require('MsPortalFx-Test/PortalElement'); class Part extends PortalElement {
	    protected partCss: string;
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	    /**
	     * Builds a Part locator using the specified options.
	     * @param options The options used to build the locator.
	     * @returns A Locator instance.
	     */
	    buildLocator(options: {
	        innerText?: string;
	    }): Locator;
	    /**
	     * Get a value that indicates whether the part is selected.
	     * @returns A promise that resolves with a value that indicates whether the part is selected.
	     */
	    isSelected(): Q.Promise<boolean>;
	    /**
	     * Get a value that indicates whether the part is loaded.
	     * @returns A promise that resolves with a value that indicates whether the part is loaded.
	     */
	    isLoaded(): Q.Promise<boolean>;
	    /**
	     * Get a value that indicates whether the part can be clicked.
	     * @returns A promise that resolves with a value that indicates whether the part can be clicked.
	     */
	    isClickable(): Q.Promise<boolean>;
	    /**
	     * Get a value that indicates whether the part is in an error state.
	     * @returns A promise that resolves with a value that indicates whether the part is in an error state.
	     */
	    hasError(): Q.Promise<boolean>;
	    /**
	     * Clicks the part.
	     * @returns A promise that resolves when the part has been clicked.
	     */
	    click(): Q.Promise<void>;
	    /**
	     * Waits until the part has been loaded. Throws an error if the part is not loaded when it reaches the timeout.
	     * @param {number} timeout Time, in milliseconds, to wait for the part to load.
	     * @returns A promise that resolves to this Part instance when the part has been loaded.
	     */
	    waitUntilLoaded(timeout?: number): Q.Promise<Part>;
	}
	export = Part;

}
declare module 'MsPortalFx-Test/Commands/ContextMenuItem' {
	import Q = require("q");
	import Locator = require('MsPortalFx-Test/Locators/Locator');
	import PortalElement = require('MsPortalFx-Test/PortalElement'); class ContextMenuItem extends PortalElement {
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	    /**
	     * Returns a value that indicates whether the ContextMenuItem is displayed and in enabled state.
	     * @returns A promise that resolves with a value that indicates whether the ContextMenuItem is displayed and in enabled state.
	     */
	    isEnabled(): Q.Promise<boolean>;
	    /**
	     * Builds a ContextMenu locator using the specified options.
	     * @param options The options used to build the locator.
	     * @returns A Locator instance.
	     */
	    buildLocator(options: {
	        text?: string;
	    }): Locator;
	}
	export = ContextMenuItem;

}
declare module 'MsPortalFx-Test/Commands/ContextMenu' {
	import Q = require("q");
	import Locator = require('MsPortalFx-Test/Locators/Locator');
	import PortalElement = require('MsPortalFx-Test/PortalElement');
	import ContextMenuItem = require('MsPortalFx-Test/Commands/ContextMenuItem');
	import testFx = require('MsPortalFx-Test/Index'); class ContextMenu extends PortalElement {
	    /**
	     * The collection of items that belong to this context menu.
	     */
	    items: testFx.ElementArrayFinder<ContextMenuItem>;
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	    /**
	     * Returns a value that indicates whether the ContextMenu is in active state.
	     * @returns A promise that resolves with a value that indicates whether the Context Menu is in active state.
	     */
	    isActive(): Q.Promise<boolean>;
	    /**
	     * Returns the ContextMenuItem that corresponds to the specified text.
	     * @param {{ text?} options The options used to find the ContextMenuItem.
	     * @returns The ContextMenuItem.
	     */
	    item(options: {
	        text?: string;
	    }): ContextMenuItem;
	    /**
	     * Returns a value that specifies whether the specified item exists in this context menu.
	     * @param {string} text The item text.
	     * @returns A promise that resolves with a value that specifies whether the specified item exists in this context menu.
	     */
	    hasItem(text: string): Q.Promise<boolean>;
	    /**
	     * Clicks the specified item.
	     * @param {string} text The text of the item to click.
	     * @returns A promise that resolves when the item has been clicked.
	     */
	    clickItem(text: string): Q.Promise<void>;
	}
	export = ContextMenu;

}
declare module 'MsPortalFx-Test/Parts/Tile' {
	import Q = require("q");
	import testFx = require('MsPortalFx-Test/Index');
	import Locator = require('MsPortalFx-Test/Locators/Locator');
	import PortalElement = require('MsPortalFx-Test/PortalElement'); class Tile extends PortalElement {
	    /**
	     * The part that lives inside this tile.
	     */
	    part: testFx.Parts.Part;
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	    /**
	     * Get a value that indicates whether the tile is loaded.
	     * @returns A promise that resolves with a value that indicates whether the tile is loaded.
	     */
	    isLoaded(): Q.Promise<boolean>;
	    /**
	     * Tries to pin the tile, if the Pin command is available.
	     * @returns A promise that resolves when Pin command has been clicked or when the Pin command has not been found.
	     */
	    tryPin(): Q.Promise<void>;
	    /**
	     * Waits until the tile has been loaded. Throws an error if the tile is not loaded when it reaches the timeout.
	     * @param {number} timeout Time, in milliseconds, to wait for the tile to load.
	     * @returns A promise that resolves to this Tile instance when the tile has been loaded.
	     */
	    waitUntilLoaded(timeout?: number): Q.Promise<Tile>;
	}
	export = Tile;

}
declare module 'MsPortalFx-Test/Blades/BladeDialog' {
	import Q = require("q");
	import Locator = require('MsPortalFx-Test/Locators/Locator');
	import PortalElement = require('MsPortalFx-Test/PortalElement');
	import FormElement = require('MsPortalFx-Test/Controls/FormElement'); class BladeDialog extends PortalElement {
	    private titleTag;
	    /**
	     * The dialog title.
	     */
	    title: PortalElement;
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	    /**
	     * Clicks the specified button in the dialog.
	     * @param {string} buttonText The text of the button to click.
	     * @returns A promise that resolves when the button has been clicked.
	     */
	    clickButton(buttonText: string): Q.Promise<void>;
	    /**
	     * Gets a field contained in this blade dialog that matches the given options.
	     * @param fieldType The type of field.
	     * @param options The options used to locate the field.
	     * @returns An instance of the field.
	     */
	    field<T extends FormElement>(fieldType: {
	        new (): T;
	    }, options: {
	        label: string;
	    }): T;
	    /**
	     * Builds a BladeDialog locator using the specified options.
	     * @param options The options used to build the locator.
	     * @returns A Locator instance.
	     */
	    buildLocator(options: {
	        title?: string;
	    }): Locator;
	}
	export = BladeDialog;

}
declare module 'MsPortalFx-Test/Blades/Blade' {
	import Q = require("q");
	import ActionBar = require('MsPortalFx-Test/ActionBars/ActionBar');
	import CommandBarItem = require('MsPortalFx-Test/Commands/CommandBarItem');
	import FormElement = require('MsPortalFx-Test/Controls/FormElement');
	import testFx = require('MsPortalFx-Test/Index');
	import Locator = require('MsPortalFx-Test/Locators/Locator');
	import Part = require('MsPortalFx-Test/Parts/Part');
	import PortalElement = require('MsPortalFx-Test/PortalElement');
	import BladeDialog = require('MsPortalFx-Test/Blades/BladeDialog'); class Blade extends PortalElement {
	    protected bladeClass: string;
	    protected titleClass: string;
	    private subtitleClass;
	    private closeBladeClass;
	    private statusBarClass;
	    /**
	     * The blade title element.
	     */
	    readonly title: PortalElement;
	    /**
	     * Gets the title element text.
	     * @returns Blade title.
	     */
	    titleAsync(): Q.Promise<string>;
	    /**
	     * The blade subtitle element.
	     */
	    readonly subtitle: PortalElement;
	    /**
	     * Gets the subtitle element text.
	     * @returns Blade subtitle.
	     */
	    subtitleAsync(): Q.Promise<string>;
	    /**
	     * The blade status bar.
	     */
	    statusBar: testFx.PortalElement;
	    /**
	     * The collection of tiles that belong to this blade.
	     */
	    tiles: testFx.ElementArrayFinder<testFx.Parts.Tile>;
	    /**
	     * The collection of command bar items that belong to this blade.
	     */
	    readonly commandBarItems: testFx.ElementArrayFinder<CommandBarItem>;
	    /**
	     * Gets the subtitle element text.
	     * @returns Blade subtitle.
	     */
	    commandBarItemsAsync(): Q.Promise<CommandBarItem[]>;
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	    /**
	     * Builds a Blade locator using the specified options.
	     * @param options The options used to build the locator.
	     * @returns A Locator instance.
	     */
	    buildLocator(options: {
	        title?: string;
	    }): Locator;
	    /**
	     * Returns a value that indicates whether the Blade has a command with the specified text.
	     * @param {string} commandText The text of the command.
	     * @returns A promise that resolves with a value that indicates whether the Blade has the specified command.
	     */
	    hasCommand(commandText: string): Q.Promise<boolean>;
	    /**
	     * Clicks the Blade command that has the specified text.
	     * @param {string} commandText The text of the command.
	     * @param {number} timeout? The time, in milliseconds, to wait for the command to be enabled
	     * @returns A promise that resolves with this same Blade instance.
	     */
	    clickCommand(commandText: string, timeout?: number): Q.Promise<Blade>;
	    /**
	     * Clicks the close button on the blade.
	     * @returns A promise that results once the close button is clicked.
	     */
	    clickClose(): Q.Promise<void>;
	    /**
	     * Returns a value that indicates whether the blade has been revealed (blade may be "loading" additional data in the background)
	     * @returns A promise that resolves with a value that indicates whether the Blade has been revealed
	     */
	    isRevealed(): Q.Promise<boolean>;
	    /**
	     * Returns a value that indicates whether the blade has finished loading.
	     * @returns A promise that resolves with a value that indicates whether the Blade has finished loading.
	     */
	    isLoaded(): Q.Promise<boolean>;
	    /**
	     * Gets a dialog associated to this blade and that has matches the given options.
	     * @param options The options used to locate the dialog.
	     * @returns A BladeDialog instance that matches the specified options.
	     */
	    dialog(options: {
	        title: string;
	    }): BladeDialog;
	    /**
	     * Gets a Part contained in this blade that matches the given options.
	     * @param options The options used to locate the part.
	     * @returns A Part instance that matches the specified options.
	     */
	    part(options: {
	        innerText?: string;
	    }): Part;
	    /**
	     * Gets a field contained in this blade that matches the given options.
	     * @param fieldType The type of field.
	     * @param options The options used to locate the field.
	     * @returns An instance of the field.
	     */
	    field<T extends FormElement>(fieldType: {
	        new (): T;
	    }, options: {
	        label: string;
	        contains?: boolean;
	    }): T;
	    /**
	     * Waits until the blade has been revealed (blade may be "loading" additional data in the background).
	     * @param {number} timeout?  The time, in milliseconds, to wait for the blade to load.
	     * @returns A promise that resolves with this blade instance when it has been revealed.
	     */
	    waitUntilRevealed(timeout?: number): Q.Promise<Blade>;
	    /**
	     * Waits until the blade has finished loading.
	     * @param {number} timeout?  The time, in milliseconds, to wait for the blade to load.
	     * @returns A promise that resolves with this blade instance when it has finished loading.
	     */
	    waitUntilLoaded(timeout?: number): Q.Promise<Blade>;
	    /**
	     * Waits until all blade tiles are loaded. If the tiles have not finished loading after the specified timeout is reached, an error is thrown.
	     * @param {number} timeout The time, in milliseconds, to wait for the tiles to load.
	     * @returns A promise that resolves with this blade instance when all tiles are loaded.
	     */
	    waitForAllTilesLoaded(timeout?: number): Q.Promise<Blade>;
	    /**
	     * Waits until both the blade and all its tiles have finished loading.
	     * @param {number} timeout? The time, in milliseconds, to wait for the blade and tiles to load.
	     * @returns  A promise that resolves with this blade instance when the blade and all tiles are loaded.
	     */
	    waitUntilBladeAndAllTilesLoaded(timeout?: number): Q.Promise<Blade>;
	    protected _hasButtons(buttons: string[], actionBar: ActionBar): Q.Promise<boolean>;
	    protected _getButton(button: string): PortalElement;
	    protected _foundActionBar(actionBar: ActionBar): Q.Promise<boolean>;
	    protected _foundButtons(buttons: string[]): Q.Promise<boolean>;
	}
	export = Blade;

}
declare module 'MsPortalFx-Test/Controls/GridCell' {
	import Q = require("q");
	import PortalElement = require('MsPortalFx-Test/PortalElement');
	import Locator = require('MsPortalFx-Test/Locators/Locator');
	import IPortalElementRetryOptions = require('MsPortalFx-Test/IPortalElementRetryOptions'); class GridCell extends PortalElement {
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	    /**
	     * Invokes the click function of the cell which clicks the cell content.
	     * @param retryOptions Optional set of retry options (eg attempts and delays between attempts).  Defaults to 3 retries with a 500ms delay
	     * @returns A promise that resolves when the invoked function resolves.
	     */
	    click(retryOptions?: IPortalElementRetryOptions): Q.Promise<void>;
	    private cellContent;
	}
	export = GridCell;

}
declare module 'MsPortalFx-Test/Controls/GridRow' {
	import Q = require("q");
	import PortalElement = require('MsPortalFx-Test/PortalElement');
	import ElementArrayFinder = require('MsPortalFx-Test/ElementArrayFinder');
	import Locator = require('MsPortalFx-Test/Locators/Locator');
	import GridCell = require('MsPortalFx-Test/Controls/GridCell');
	import IPortalElementRetryOptions = require('MsPortalFx-Test/IPortalElementRetryOptions'); class GridRow extends PortalElement {
	    /**
	     * The collection of cells.
	     */
	    cells: ElementArrayFinder<GridCell>;
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	    /**
	     * Invokes the click function of the row which clicks the first visible cell.
	     * @param retryOptions Optional set of retry options (eg attempts and delays between attempts).  Defaults to 3 retries with a 500ms delay
	     * @returns A promise that resolves when the invoked function resolves.
	     */
	    click(retryOptions?: IPortalElementRetryOptions): Q.Promise<void>;
	    /**
	     * Invokes the click function of the row which clicks the specified cell.
	     * @param cellIndex The index of the cell to click.
	     * @param retryOptions Optional set of retry options (eg attempts and delays between attempts).  Defaults to 3 retries with a 500ms delay
	     * @returns A promise that resolves when the invoked function resolves.
	     */
	    clickCell(cellIndex: number, retryOptions?: IPortalElementRetryOptions): Q.Promise<void>;
	    /**
	     * Determines if the row has been activated.
	     * @returns A promise that resolves when the activation state is found.
	     */
	    isActivated(): Promise<boolean>;
	}
	export = GridRow;

}
declare module 'MsPortalFx-Test/Controls/Grid' {
	import Q = require("q");
	import testFx = require('MsPortalFx-Test/Index');
	import PortalElement = require('MsPortalFx-Test/PortalElement');
	import Locator = require('MsPortalFx-Test/Locators/Locator');
	import GridRow = require('MsPortalFx-Test/Controls/GridRow'); class Grid extends PortalElement {
	    /**
	     * Represents the collection of rows in the grid.
	     */
	    rows: testFx.ElementArrayFinder<testFx.Controls.GridRow>;
	    /**
	     * The text box used to filter grid results.
	     * Does not apply to Grid2 (DataGrid/EditableGrid).
	     */
	    searchBox: testFx.PortalElement;
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	    /**
	     * Filters the grid rows using the specified text.
	     * Does not apply to Grid2 (DataGrid/EditableGrid).
	     * @param {string} text The filter.
	     * @returns A promise that resolves with this grid instance when filtered rows are available.
	     */
	    filter(text: string): Q.Promise<Grid>;
	    /**
	     * Clicks the row that contains the specified text.
	     * @param options The set of options used to find the row.
	     * @param {string} options.text The text that the row should contain.
	     * @param {number} options.cellIndex The optional index of the cell where the text should be searched.
	     * @param {boolean} options.partialMatch Optionally searches for the text using partial matching.
	     * @returns A promise that resolves when the row has been clicked.
	     */
	    clickRow(options: {
	        text: string;
	        cellIndex?: number;
	        partialMatch?: boolean;
	    }): Q.Promise<void>;
	    /**
	     * If multi-select is enabled, the row that contains the specified text will be selected.
	     * @param options The set of options used to find the row.
	     * @param {string} options.text The text that the row should contain.
	     * @param {boolean} options.partialMatch Optionally searches for the text using partial matching.
	     * @returns A promise that resolves when the row has been selected.
	     */
	    selectRow(options: {
	        text: string;
	        partialMatch?: boolean;
	    }): Q.Promise<void>;
	    /**
	     * If multi-select is enabled, all the rows that contains the specified text will be selected.
	     * @param options The set of options used to find the row.
	     * @param {string} options.text The text that the row should contain.
	     * @param {boolean} options.partialMatch Optionally searches for the text using partial matching.
	     * @returns A promise that resolves when all the rows has been selected.
	     */
	    selectRows(options: {
	        texts: string[];
	        partialMatch?: boolean;
	    }): Q.Promise<any[]>;
	    /**
	     * Performs a right click on the row that contains the specified text.
	     * @param options The set of options used to find the row.
	     * @param {string} options.text The text that the row should contain.
	     * @param {number} options.cellIndex The optional index of the cell where the text should be searched.
	     * @param {boolean} options.partialMatch Optionally searches for the text using partial matching.
	     * @returns A promise that resolves when the row has been right clicked.
	     */
	    rightClickRow(options: {
	        text: string;
	        cellIndex?: number;
	        partialMatch?: boolean;
	    }): Q.Promise<void>;
	    /**
	     * Finds a row that contains the specified text.
	     * @param options The set of options used to find the row.
	     * @param {string} options.text The text that the row should contain.
	     * @param {number} options.cellIndex The optional index of the cell where the text should be searched.
	     * @param {boolean} options.partialMatch Optionally searches for the text using partial matching.
	     * @param {number} [timeout] optional timeout to wait for the row to be present
	     * @returns A promise that resolves with the found row.
	     */
	    findRow(options: {
	        text: string;
	        cellIndex?: number;
	        partialMatch?: boolean;
	    }, timeout?: number): Q.Promise<GridRow>;
	    /**
	     * Returns a value that indicates whether the grid has rows.
	     * @returns A promise that resolves with a value that indicates whether the grid has rows.
	     */
	    hasRows(): Q.Promise<boolean>;
	}
	export = Grid;

}
declare module 'MsPortalFx-Test/Blades/BrowseResourceBladeGridRow' {
	import Q = require("q");
	import GridRow = require('MsPortalFx-Test/Controls/GridRow'); class BrowseResourceBladeGridRow extends GridRow {
	    /**
	     * Invokes the click function on the grid row and waits for it to be activated.
	     * @returns A promise that resolves when the grid row has been activated.
	     */
	    click(): Q.Promise<void>;
	}
	export = BrowseResourceBladeGridRow;

}
declare module 'MsPortalFx-Test/Blades/BrowseResourceBladeGrid' {
	import ElementArrayFinder = require('MsPortalFx-Test/ElementArrayFinder');
	import Grid = require('MsPortalFx-Test/Controls/Grid');
	import BrowseResourceBladeGridRow = require('MsPortalFx-Test/Blades/BrowseResourceBladeGridRow'); class BrowseResourceBladeGrid extends Grid {
	    /**
	     * Gets the grid rows.
	     */
	    rows: ElementArrayFinder<BrowseResourceBladeGridRow>;
	}
	export = BrowseResourceBladeGrid;

}
declare module 'MsPortalFx-Test/Controls/HotSpot' {
	import Q = require("q");
	import PortalElement = require('MsPortalFx-Test/PortalElement');
	import Locator = require('MsPortalFx-Test/Locators/Locator'); class HotSpot extends PortalElement {
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	    /**
	     * Returns a value that determines whether the hotspot is selected.
	     * @returns A promise that resolves to a value that determines whether the hotspot is selected.
	     */
	    isSelected(): Q.Promise<boolean>;
	}
	export = HotSpot;

}
declare module 'MsPortalFx-Test/Controls/AccessHotSpot' {
	import Locator = require('MsPortalFx-Test/Locators/Locator');
	import HotSpot = require('MsPortalFx-Test/Controls/HotSpot'); class AccessHotSpot extends HotSpot {
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	}
	export = AccessHotSpot;

}
declare module 'MsPortalFx-Test/Controls/AsyncFileUpload' {
	import Q = require("q");
	import Locator = require('MsPortalFx-Test/Locators/Locator');
	import FormElement = require('MsPortalFx-Test/Controls/FormElement');
	import PortalElement = require('MsPortalFx-Test/PortalElement'); class AsyncFileUpload extends FormElement {
	    /**
	     * The input element.
	     */
	    input: PortalElement;
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	    /**
	     * Gets the current value of the AsyncFileUpload
	     * @returns A promise that will resolve with the current value of the AsyncFileUpload
	     */
	    value(): Q.Promise<string>;
	    /**
	     * Sends file content to AsyncFileUpload.
	     * @param var_args The text to enter.
	     * @returns A promise that resolves when the file conent has been sent.
	     */
	    sendKeys(...var_args: string[]): Q.Promise<void>;
	    /**
	     * Schedules a command to clear the value of this element.
	     * @returns A promise that will be resolved when the element has been cleared.
	     */
	    clear(): Q.Promise<void>;
	}
	export = AsyncFileUpload;

}
declare module 'MsPortalFx-Test/Controls/CheckBoxField' {
	import Q = require("q");
	import Locator = require('MsPortalFx-Test/Locators/Locator');
	import FormElement = require('MsPortalFx-Test/Controls/FormElement'); class CheckBoxField extends FormElement {
	    private checkBox;
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	    /**
	     * Clicks the checkbox.
	     * @returns A promise that resolves when the checkbox has been clicked.
	     */
	    click(): Q.Promise<void>;
	    /**
	     * determines if the checkbox is currently checked
	     * @returns A promise that resolves with a true/false result if the checkbox is currently checked
	     */
	    isChecked(): Q.Promise<boolean>;
	}
	export = CheckBoxField;

}
declare module 'MsPortalFx-Test/Controls/CopyableLabel' {
	import Q = require("q");
	import Locator = require('MsPortalFx-Test/Locators/Locator');
	import FormElement = require('MsPortalFx-Test/Controls/FormElement');
	import PortalElement = require('MsPortalFx-Test/PortalElement'); class CopyableLabel extends FormElement {
	    /**
	     * The input element.
	     */
	    input: PortalElement;
	    /**
	     * The copy button element.
	     */
	    copyButton: PortalElement;
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	    /**
	     * Gets the current value of the CopyableLabel
	     * @returns A promise that will resolve with the current value of the CopyableLabel
	     */
	    value(): Q.Promise<string>;
	    /**
	     * Enters the specified text in the CopyableLabel.
	     * @param var_args The text to enter.
	     * @returns A promise that resolves when the text has been entered.
	     */
	    sendKeys(...var_args: string[]): Q.Promise<void>;
	    /**
	     * Schedules a command to clear the value of this element.
	     * @returns A promise that will be resolved when the element has been cleared.
	     */
	    clear(): Q.Promise<void>;
	}
	export = CopyableLabel;

}
declare module 'MsPortalFx-Test/Controls/TextField' {
	import Q = require("q");
	import Locator = require('MsPortalFx-Test/Locators/Locator');
	import FormElement = require('MsPortalFx-Test/Controls/FormElement');
	import PortalElement = require('MsPortalFx-Test/PortalElement'); class TextField extends FormElement {
	    /**
	     * The input element.
	     */
	    input: PortalElement;
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	    /**
	     * Gets the current value of the TextField
	     * @returns A promise that will resolve with the current value of the TextField
	     */
	    value(): Q.Promise<string>;
	    /**
	     * Enters the specified text in the TextField.
	     * @param var_args The text to enter.
	     * @returns A promise that resolves when the text has been entered.
	     */
	    sendKeys(...var_args: string[]): Q.Promise<void>;
	    /**
	     * Schedules a command to clear the value of this element.
	     * @returns A promise that will be resolved when the element has been cleared.
	     */
	    clear(): Q.Promise<void>;
	}
	export = TextField;

}
declare module 'MsPortalFx-Test/Controls/CreatorAndSelectorField' {
	import Q = require("q");
	import Locator = require('MsPortalFx-Test/Locators/Locator');
	import FormElement = require('MsPortalFx-Test/Controls/FormElement'); class CreatorAndSelectorField extends FormElement {
	    private createNewTextField;
	    private createNewLink;
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	    /**
	     * Opens the associated picker by clicking on this field.
	     * @returns A promise that resolves when the the field has been clicked.
	     */
	    openPicker(): Q.Promise<void>;
	    /**
	     * Clicks the Create New link on this field.
	     * @returns A promise that resolves when the Create New link has been clicked.
	     */
	    clickCreateNew(): Q.Promise<void>;
	    /**
	     * Enters a new value in the creator TextField.
	     * @param var_args Text to send to the TextField.
	     * @returns A promise that resolves with this CreatorAndSelectorField instance
	     * when the text has been entered in the creator TextField.
	     */
	    enterNewValue(...var_args: string[]): Q.Promise<CreatorAndSelectorField>;
	}
	export = CreatorAndSelectorField;

}
declare module 'MsPortalFx-Test/Controls/OptionsGroupFieldOption' {
	import Q = require("q");
	import PortalElement = require('MsPortalFx-Test/PortalElement');
	import Locator = require('MsPortalFx-Test/Locators/Locator'); class OptionsGroupFieldOption extends PortalElement {
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	    /**
	     * Gets whether the current option is selected or not.
	     * @returns A promise that resolves with whether the option is selected or not.
	     */
	    isSelected(): Q.Promise<boolean>;
	}
	export = OptionsGroupFieldOption;

}
declare module 'MsPortalFx-Test/Controls/FilterComboField' {
	import Q = require("q");
	import FormElement = require('MsPortalFx-Test/Controls/FormElement');
	import Locator = require('MsPortalFx-Test/Locators/Locator'); class FilterComboField extends FormElement {
	    private buttonElement;
	    private inputElement;
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	    /**
	     * Waits for the form element to be enabled.
	     * @param timeout The amount of time to wait before failing
	     * @returns A promise that resolves when the form element is enabled
	     */
	    waitUntilEnabled(timeout?: number): Q.Promise<void>;
	    /**
	     * Sets the specified option by typing in the text.
	     * @param {string} optionText The text of the option to set.
	     * @returns A promise that resolves when the option has been set.
	     */
	    setOption(optionText: string): Q.Promise<void>;
	    /**
	     * Selects the specified option.
	     * @param {string} optionText The text of the option to select.
	     * @returns A promise that resolves when the option has been selected.
	     */
	    selectOption(optionText: string): Q.Promise<void>;
	}
	export = FilterComboField;

}
declare module 'MsPortalFx-Test/Controls/CreateComboBoxField' {
	import Q = require("q");
	import Locator = require('MsPortalFx-Test/Locators/Locator');
	import FormElement = require('MsPortalFx-Test/Controls/FormElement'); class CreateComboBoxField extends FormElement {
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	    /**
	     * Selects the specified option.
	     * @param {string} optionText The text of the option to select.
	     * @returns A promise that resolves when the option has been selected.
	     */
	    selectOption(optionText: string, timeout?: number): Q.Promise<void>;
	    /**
	     * Sets the first text field in the create section
	     * @param {string} value the value of the text to input in the text control
	     * @param {number} [timeout] optional timeout to wait for the CreateComboBoxField to be displayed
	     * @returns {Q.Promise<void>} A promise that resolves when the value has been set
	     */
	    setCreateValue(value: string, timeout?: number): Q.Promise<void>;
	    /**
	     * Gets the first text fields value in the create section
	     * @param {number} [timeout] optional timeout to wait for the CreateComboBoxField to be displayed
	     * @returns {Q.Promise<string>} the value of the text from the create field
	     */
	    getCreateValue(timeout?: number): Q.Promise<string>;
	    /**
	     * Gets the dropdown fields value in the use existing section
	     * @param {number} [timeout] optional timeout to wait for the CreateComboBoxField to be displayed
	     * @returns {Q.Promise<string>} the value of the text from the create field
	     */
	    getDropdownValue(timeout?: number): Q.Promise<string>;
	    private createSection;
	    private filterCombo;
	}
	export = CreateComboBoxField;

}
declare module 'MsPortalFx-Test/Controls/DropDownFieldOption' {
	import PortalElement = require('MsPortalFx-Test/PortalElement');
	import Locator = require('MsPortalFx-Test/Locators/Locator'); class DropDownFieldOption extends PortalElement {
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	}
	export = DropDownFieldOption;

}
declare module 'MsPortalFx-Test/Controls/DropDownField' {
	import Q = require("q");
	import testFx = require('MsPortalFx-Test/Index');
	import FormElement = require('MsPortalFx-Test/Controls/FormElement');
	import Locator = require('MsPortalFx-Test/Locators/Locator'); class DropDownField extends FormElement {
	    /**
	     * The collection of options that belong to this dropdown field.
	     */
	    options: testFx.ElementArrayFinder<testFx.Controls.DropDownFieldOption>;
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	    /**
	     * Selects the specified option.
	     * @param {string} optionText The text of the option to select.
	     * @returns A promise that resolves when the option has been selected.
	     */
	    selectOption(optionText: string): Q.Promise<void>;
	}
	export = DropDownField;

}
declare module 'MsPortalFx-Test/Controls/Grid2Base/GridCell' {
	import Q = require("q");
	import PortalElement = require('MsPortalFx-Test/PortalElement');
	import Locator = require('MsPortalFx-Test/Locators/Locator');
	import IPortalElementRetryOptions = require('MsPortalFx-Test/IPortalElementRetryOptions'); abstract class GridCell extends PortalElement {
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	    /**
	     * Invokes the click function of the cell which clicks the cell content.
	     * @param retryOptions Optional set of retry options (eg attempts and delays between attempts).  Defaults to 3 retries with a 500ms delay
	     * @returns A promise that resolves when the invoked function resolves.
	     */
	    click(retryOptions?: IPortalElementRetryOptions): Q.Promise<void>;
	}
	export = GridCell;

}
declare module 'MsPortalFx-Test/Controls/Grid2Base/GridRow' {
	import PortalElement = require('MsPortalFx-Test/PortalElement');
	import ElementArrayFinder = require('MsPortalFx-Test/ElementArrayFinder');
	import Locator = require('MsPortalFx-Test/Locators/Locator');
	import GridCell = require('MsPortalFx-Test/Controls/Grid2Base/GridCell'); abstract class GridRow<TCell extends GridCell> extends PortalElement {
	    /**
	     * Creates a new cell.
	     */
	    private _createCell;
	    /**
	     * Constructs a base grid row.
	     *
	     * @param createCell Cell constructor.
	     * @param elementArrayFinder Element array finder.
	     */
	    constructor(createCell: new () => TCell, elementArrayFinder?: ElementArrayFinder<GridRow<TCell>>);
	    /**
	     * The collection of cells.
	     */
	    readonly cells: ElementArrayFinder<TCell>;
	    /**
	     * Gets the locator associated to this element.
	     */
	    readonly locator: Locator;
	    /**
	     * Gets the row footer.
	     */
	    readonly footer: PortalElement;
	}
	export = GridRow;

}
declare module 'MsPortalFx-Test/Controls/Grid2Base/GridSortDirection' {
	 enum SortDirection {
	    /**
	     * Data is sorted in ascending order.
	     */
	    Ascending = 1,
	    /**
	     * Data is sorted in descending order.
	     */
	    Descending = -1,
	    /**
	     * Data is not sorted.
	     */
	    Unsorted = 0
	}
	export = SortDirection;

}
declare module 'MsPortalFx-Test/Controls/Grid2Base/GridColumnHeader' {
	/// <reference types="q" />
	import PortalElement = require('MsPortalFx-Test/PortalElement');
	import Locator = require('MsPortalFx-Test/Locators/Locator');
	import SortDirection = require('MsPortalFx-Test/Controls/Grid2Base/GridSortDirection'); abstract class GridColumnHeader extends PortalElement {
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	    canSort(): Q.Promise<boolean>;
	    sortDirection(): Q.Promise<SortDirection>;
	}
	export = GridColumnHeader;

}
declare module 'MsPortalFx-Test/Controls/Grid2Base/GridBase' {
	import Q = require("q");
	import PortalElement = require('MsPortalFx-Test/PortalElement');
	import Locator = require('MsPortalFx-Test/Locators/Locator');
	import ElementArrayFinder = require('MsPortalFx-Test/ElementArrayFinder');
	import GridRow = require('MsPortalFx-Test/Controls/Grid2Base/GridRow');
	import GridCell = require('MsPortalFx-Test/Controls/Grid2Base/GridCell');
	import GridColumnHeader = require('MsPortalFx-Test/Controls/Grid2Base/GridColumnHeader'); abstract class GridBase<TRow extends GridRow<TCell>, TCell extends GridCell, TColumnHeader extends GridColumnHeader> extends PortalElement {
	    private _createRow;
	    private _createCell;
	    private _createColumnHeader;
	    /**
	     * Constructs a base grid.
	     *
	     * @param createRow Constructs a Row.
	     * @param createCell Constructs a Cell.
	     * @param createColumnHeader Constructs a ColumnHeader.
	     * @param elementArrayFinder Element array finder.
	     */
	    constructor(createRow: new () => TRow, createCell: new () => TCell, createColumnHeader: new () => TColumnHeader, elementArrayFinder?: ElementArrayFinder<GridBase<TRow, TCell, TColumnHeader>>);
	    /**
	     * Gets a collection of rows in the grid.
	     */
	    readonly rows: ElementArrayFinder<TRow>;
	    /**
	     * Gets a collection of column headers in the grid.
	     */
	    readonly columnHeaders: ElementArrayFinder<TColumnHeader>;
	    /**
	     * Gets a collection of all the cells in the grid.
	     */
	    readonly cells: ElementArrayFinder<TCell>;
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	    /**
	     * Gets a collection of group rows in the grid.
	     */
	    getGroupRows(): Q.Promise<TRow[]>;
	    /**
	     * Clicks the row that contains the specified text.
	     * @param options The set of options used to find the row.
	     * @param {string} options.text The text that the row should contain.
	     * @param {number} options.cellIndex The optional index of the cell where the text should be searched.
	     * @param {boolean} options.partialMatch Optionally searches for the text using partial matching.
	     * @returns A promise that resolves when the row has been clicked.
	     */
	    clickRow(options: {
	        text: string;
	        cellIndex?: number;
	        partialMatch?: boolean;
	    }): Q.Promise<void>;
	    /**
	     * If multi-select is enabled, the row that contains the specified text will be selected.
	     * @param options The set of options used to find the row.
	     * @param {string} options.text The text that the row should contain.
	     * @param {boolean} options.partialMatch Optionally searches for the text using partial matching.
	     * @returns A promise that resolves when the row has been selected.
	     */
	    selectRow(options: {
	        text: string;
	        partialMatch?: boolean;
	    }): Q.Promise<void>;
	    /**
	     * If multi-select is enabled, all the rows that contains the specified text will be selected.
	     * @param options The set of options used to find the row.
	     * @param {string} options.text The text that the row should contain.
	     * @param {boolean} options.partialMatch Optionally searches for the text using partial matching.
	     * @returns A promise that resolves when all the rows has been selected.
	     */
	    selectRows(options: {
	        texts: string[];
	        partialMatch?: boolean;
	    }): Q.Promise<any[]>;
	    /**
	     * Performs a right click on the row that contains the specified text.
	     * @param options The set of options used to find the row.
	     * @param {string} options.text The text that the row should contain.
	     * @param {number} options.cellIndex The optional index of the cell where the text should be searched.
	     * @param {boolean} options.partialMatch Optionally searches for the text using partial matching.
	     * @returns A promise that resolves when the row has been right clicked.
	     */
	    rightClickRow(options: {
	        text: string;
	        cellIndex?: number;
	        partialMatch?: boolean;
	    }): Q.Promise<void>;
	    /**
	     * Finds a row that contains the specified text.
	     * @param options The set of options used to find the row.
	     * @param {string} options.text The text that the row should contain.
	     * @param {number} options.cellIndex The optional index of the cell where the text should be searched.
	     * @param {boolean} options.partialMatch Optionally searches for the text using partial matching.
	     * @param {number} [timeout] optional timeout to wait for the row to be present
	     * @returns A promise that resolves with the found row.
	     */
	    findRow(options: {
	        text: string;
	        cellIndex?: number;
	        partialMatch?: boolean;
	    }, timeout?: number): Q.Promise<TRow>;
	    /**
	     * Returns a value that indicates whether the grid has rows.
	     * @returns A promise that resolves with a value that indicates whether the grid has rows.
	     */
	    hasRows(): Q.Promise<boolean>;
	}
	export = GridBase;

}
declare module 'MsPortalFx-Test/Controls/EditableGrid' {
	import Q = require("q");
	import Locator = require('MsPortalFx-Test/Locators/Locator');
	import ElementArrayFinder = require('MsPortalFx-Test/ElementArrayFinder');
	import GridBase = require('MsPortalFx-Test/Controls/Grid2Base/GridBase');
	import GridCell = require('MsPortalFx-Test/Controls/Grid2Base/GridCell');
	import GridRow = require('MsPortalFx-Test/Controls/Grid2Base/GridRow');
	import GridColumnHeader = require('MsPortalFx-Test/Controls/Grid2Base/GridColumnHeader'); class EditableGrid extends GridBase<EditableGrid.Row, EditableGrid.Cell, EditableGrid.ColumnHeader> {
	    constructor(elementArrayFinder?: ElementArrayFinder<EditableGrid>);
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	} module EditableGrid {
	    /**
	     * Represents an EditableGrid column header.
	     */
	    class ColumnHeader extends GridColumnHeader {
	    }
	    /**
	     * Represents an EditableGrid cell.
	     */
	    class Cell extends GridCell {
	        /**
	         * Gets the value of the first input in the cell.
	         */
	        getInputValue(): Q.Promise<string>;
	        /**
	         * Sets the value of the first input in the cell.
	         */
	        setInputValue(value: string): Q.Promise<any>;
	    }
	    /**
	     * Represents an EditableGrid row.
	     */
	    class Row extends GridRow<EditableGrid.Cell> {
	        constructor(elementArrayFinder?: ElementArrayFinder<EditableGrid.Row>);
	        /**
	         * Gets the text of the EditableGrid row validation footer.
	         */
	        validationErrors(): Q.Promise<string>;
	    }
	}
	export = EditableGrid;

}
declare module 'MsPortalFx-Test/Controls/DataGrid' {
	import Locator = require('MsPortalFx-Test/Locators/Locator');
	import ElementArrayFinder = require('MsPortalFx-Test/ElementArrayFinder');
	import GridBase = require('MsPortalFx-Test/Controls/Grid2Base/GridBase');
	import GridCell = require('MsPortalFx-Test/Controls/Grid2Base/GridCell');
	import GridRow = require('MsPortalFx-Test/Controls/Grid2Base/GridRow');
	import GridColumnHeader = require('MsPortalFx-Test/Controls/Grid2Base/GridColumnHeader');
	import GridSortDirection = require('MsPortalFx-Test/Controls/Grid2Base/GridSortDirection'); class DataGrid extends GridBase<DataGrid.Row, DataGrid.Cell, DataGrid.ColumnHeader> {
	    constructor(elementArrayFinder?: ElementArrayFinder<DataGrid>);
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	} module DataGrid {
	    /**
	     * Grid sort direction.
	     */
	    export import SortDirection = GridSortDirection;
	    /**
	     * Represents an DataGrid column header.
	     */
	    class ColumnHeader extends GridColumnHeader {
	    }
	    /**
	     * Represents an DataGrid cell.
	     */
	    class Cell extends GridCell {
	    }
	    /**
	     * Represents an DataGrid row.
	     */
	    class Row extends GridRow<DataGrid.Cell> {
	        constructor(elementArrayFinder?: ElementArrayFinder<DataGrid.Row>);
	    }
	}
	export = DataGrid;

}
declare module 'MsPortalFx-Test/Controls/Editor' {
	import Q = require("q");
	import PortalElement = require('MsPortalFx-Test/PortalElement');
	import Locator = require('MsPortalFx-Test/Locators/Locator'); class Editor extends PortalElement {
	    /**
	     * The textarea element.
	     */
	    textarea: PortalElement;
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	    /**
	     * Enters the specified text in the Editor.
	     * @param var_args The text to enter.
	     * @returns A promise that resolves when the text has been entered.
	     */
	    sendKeys(...var_args: string[]): Q.Promise<void>;
	    /**
	     * Gets the current content of the Editor.
	     * @returns A promise that will resolve with the current value of the Editor.
	     */
	    read(): Q.Promise<string>;
	    /**
	     * Sets the content of the Editor.
	     * @returns A promise that will resolve after setting the content.
	     */
	    write(content: string): Q.Promise<void>;
	    /**
	     * empty the content of the Editor.
	     * @return A promise that will resolve after emptying the content.
	     */
	    empty(): Q.Promise<void>;
	    /**
	     * Returns a value that determines whether the editor is disabled.
	     * @returns A promise that resolves to a value that determines whether the editor is disabled.
	     */
	    isDisabled(): Q.Promise<boolean>;
	    /**
	     * Returns a value that determines whether the intellisense window appeared.
	     * @returns A promise that resolves to the boolean that determines whether the intellisense window appeared.
	     */
	    isIntellisenseUp(): Q.Promise<boolean>;
	    /**
	     * Internal method used for testing purpose.
	     * Returns a number of worker IFrames created by the editor.
	     * @returns A promise that resolves to the number of the worker IFrames.
	     */
	    workerIFramesCount(): Q.Promise<number>;
	}
	export = Editor;

}
declare module 'MsPortalFx-Test/Controls/EssentialsItemPropertyType' {
	 const enum ItemPropertyType {
	    /**
	     * Text.
	     */
	    Text = 1,
	    /**
	     * Callback function.
	     */
	    Function = 2,
	    /**
	     * Hyperlink.
	     */
	    Link = 3
	}
	export = ItemPropertyType;

}
declare module 'MsPortalFx-Test/Controls/EssentialsItemProperty' {
	import Q = require("q");
	import PortalElement = require('MsPortalFx-Test/PortalElement');
	import Locator = require('MsPortalFx-Test/Locators/Locator');
	import EssentialsItemPropertyType = require('MsPortalFx-Test/Controls/EssentialsItemPropertyType'); class EssentialsItemProperty extends PortalElement {
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	    /**
	     * Type of the Essentials item property.
	     * @returns A promise that resolves the type of the essentials item property.
	     */
	    getType(): Q.Promise<EssentialsItemPropertyType>;
	    /**
	     * Value of the Essentials item property.
	     * @returns A promise that resolve the value of the property.
	     */
	    getValue(): Q.Promise<string>;
	}
	export = EssentialsItemProperty;

}
declare module 'MsPortalFx-Test/Controls/EssentialsItem' {
	import Q = require("q");
	import PortalElement = require('MsPortalFx-Test/PortalElement');
	import ElementArrayFinder = require('MsPortalFx-Test/ElementArrayFinder');
	import Locator = require('MsPortalFx-Test/Locators/Locator');
	import EssentialsItemProperty = require('MsPortalFx-Test/Controls/EssentialsItemProperty'); class EssentialsItem extends PortalElement {
	    /**
	     * The essentials item properties.
	     */
	    properties: ElementArrayFinder<EssentialsItemProperty>;
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	    /**
	     * Gets the item label text.
	     * @returns A promise that resolves with the label text of the item.
	     */
	    getLabelText(): Q.Promise<string>;
	    /**
	     * Gets whether the item has move resource blade or not.
	     * @returns A promise that resolves with whether the item has move resource blade or not.
	     */
	    hasMoveResource(): Q.Promise<boolean>;
	    /**
	     * Gets the item's side.
	     * @returns A promise that resolves with the side of the item.
	     */
	    getSide(): Q.Promise<string>;
	    /**
	     * Returns an element of matching property value.
	     * @param value The string to match property element value.
	     * @returns An element of matching property value.
	     */
	    getPropertyElementByValue(value: string): Q.Promise<EssentialsItemProperty>;
	}
	export = EssentialsItem;

}
declare module 'MsPortalFx-Test/Controls/Essentials' {
	import Q = require("q");
	import PortalElement = require('MsPortalFx-Test/PortalElement');
	import Locator = require('MsPortalFx-Test/Locators/Locator');
	import EssentialsItem = require('MsPortalFx-Test/Controls/EssentialsItem');
	import ElementArrayFinder = require('MsPortalFx-Test/ElementArrayFinder'); class Essentials extends PortalElement {
	    /**
	     * The essentials items.
	     */
	    items: ElementArrayFinder<EssentialsItem>;
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	    /**
	     * Count the number of all items. (MultiLine Item is counted as one)
	     * @returns A promise that resolves with the number of all items in the essentials.
	     */
	    countItems(): Q.Promise<number>;
	    /**
	     * Returns a value that determines whether the essentials is disabled.
	     * @returns A promise that resolves to a value that determines whether the essentials is disabled.
	     */
	    isDisabled(): Q.Promise<boolean>;
	    /**
	     * Returns a value that determines whether the essentials has ViewAll button or not.
	     * @returns A promise that resolves to a value that determines whether the essentials has ViewAll button or not.
	     */
	    hasViewAll(): Q.Promise<boolean>;
	    /**
	     * Returns a PortalElement of the essentials' viewAll button.
	     * @returns A PortalElement of the essentials' viewAll button.
	     */
	    getViewAllButton(): PortalElement;
	    /**
	     * Returns an EssentialsItem that is found by its label text.
	     * @param label A label of the item.
	     * @returns A promise that resolves to found EssentialsItem.
	     */
	    getItemByLabelText(label: string): Q.Promise<EssentialsItem>;
	    /**
	     * Returns a PortalElement of matching property value.
	     * @param value the string to match property element value.
	     * @returns A PortalElement of matching property value.
	     */
	    getPropertyElementByValue(value: string): Q.Promise<PortalElement>;
	    /**
	     * Returns a value that determines the essentials' expanded state.
	     * @returns A promise that resolves to a value that determines the essentials' expanded state.
	     */
	    getExpandedState(): Q.Promise<boolean>;
	    /**
	     * Returns the Expander element.
	     * @returns A PortalElement of the expander.
	     */
	    getExpander(): PortalElement;
	}
	export = Essentials;

}
declare module 'MsPortalFx-Test/Controls/FormSection' {
	import Q = require("q");
	import PortalElement = require('MsPortalFx-Test/PortalElement');
	import Locator = require('MsPortalFx-Test/Locators/Locator'); class FormSection extends PortalElement {
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	    /**
	     * Returns a value that indicates whether the form section has been changed.
	     * @returns A promise that resolves with a value that indicates whether the form section has been changed.
	     */
	    isChanged(): Q.Promise<boolean>;
	}
	export = FormSection;

}
declare module 'MsPortalFx-Test/Controls/MonitorChart' {
	import Q = require("q");
	import Locator = require('MsPortalFx-Test/Locators/Locator');
	import PortalElement = require('MsPortalFx-Test/PortalElement'); class MonitorChart extends PortalElement {
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	    /**
	     * Gets whether the chart has data
	     */
	    hasData(): Q.Promise<boolean>;
	    /**
	     * Gets the name of the monitor chart's first metric.
	     */
	    getMetric(): Q.Promise<string>;
	    /**
	     * Gets the value of the monitor chart's first metric.
	     */
	    getValue(): Q.Promise<string>;
	    /**
	     * Gets the unit of the monitor chart's first metric.
	     */
	    getUnit(): Q.Promise<string>;
	}
	export = MonitorChart;

}
declare module 'MsPortalFx-Test/Controls/OptionsGroupField' {
	import Q = require("q");
	import Locator = require('MsPortalFx-Test/Locators/Locator');
	import FormElement = require('MsPortalFx-Test/Controls/FormElement');
	import OptionsGroupFieldOption = require('MsPortalFx-Test/Controls/OptionsGroupFieldOption');
	import ElementArrayFinder = require('MsPortalFx-Test/ElementArrayFinder'); class OptionsGroupField extends FormElement {
	    private optionsGroupFieldClass;
	    /**
	     * The collection of options that belong to this option picker field.
	     */
	    options: ElementArrayFinder<OptionsGroupFieldOption>;
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	    /**
	     * Gets the first selected option's text
	     * @returns a promise that resolves with the selected option's text
	     */
	    getTextForSelectedOption(): Q.Promise<string>;
	    /**
	     * Clicks the specified option.
	     * @param {string} optionText The text of the option to select.
	     * @returns A promise that resolves when the option has been selected.
	     */
	    clickOption(optionText: string): Q.Promise<void>;
	}
	export = OptionsGroupField;

}
declare module 'MsPortalFx-Test/Controls/PasswordField' {
	import Locator = require('MsPortalFx-Test/Locators/Locator');
	import TextField = require('MsPortalFx-Test/Controls/TextField'); class PasswordField extends TextField {
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	}
	export = PasswordField;

}
declare module 'MsPortalFx-Test/Controls/PillListActionButton' {
	import Q = require("q");
	import PortalElement = require('MsPortalFx-Test/PortalElement');
	import Locator = require('MsPortalFx-Test/Locators/Locator'); class PillListActionButton extends PortalElement {
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	    /**
	     * Gets the item title text.
	     * @returns A promise that resolves with the title text of the item.
	     */
	    getTitleText(): Q.Promise<string>;
	}
	export = PillListActionButton;

}
declare module 'MsPortalFx-Test/Controls/PillListItem' {
	import Q = require("q");
	import PortalElement = require('MsPortalFx-Test/PortalElement');
	import Locator = require('MsPortalFx-Test/Locators/Locator'); class PillListItem extends PortalElement {
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	    /**
	     * Gets the item value text.
	     * @returns A promise that resolves with the value text of the item.
	     */
	    getValueText(): Q.Promise<string>;
	}
	export = PillListItem;

}
declare module 'MsPortalFx-Test/Controls/PillListOverflowItem' {
	import Q = require("q");
	import PortalElement = require('MsPortalFx-Test/PortalElement');
	import Locator = require('MsPortalFx-Test/Locators/Locator'); class PillListOverflowItem extends PortalElement {
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	    /**
	     * Gets the item value text.
	     * @returns A promise that resolves with the value text of the item.
	     */
	    getValueText(): Q.Promise<string>;
	}
	export = PillListOverflowItem;

}
declare module 'MsPortalFx-Test/Controls/PillList' {
	import Q = require("q");
	import PortalElement = require('MsPortalFx-Test/PortalElement');
	import Locator = require('MsPortalFx-Test/Locators/Locator');
	import PillListActionButton = require('MsPortalFx-Test/Controls/PillListActionButton');
	import PillListItem = require('MsPortalFx-Test/Controls/PillListItem');
	import PillListOverflowItem = require('MsPortalFx-Test/Controls/PillListOverflowItem');
	import ElementArrayFinder = require('MsPortalFx-Test/ElementArrayFinder'); class PillList extends PortalElement {
	    private _container;
	    /**
	     * The pill list items.
	     */
	    items: ElementArrayFinder<PillListItem>;
	    /**
	     * The pill list overflow item.
	     */
	    overflowItem: PillListOverflowItem;
	    /**
	     * The pill list items.
	     */
	    actionButtons: ElementArrayFinder<PillListActionButton>;
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	    /**
	     * Count the number of all items.
	     * @returns A promise that resolves with the number of all items in the pill list.
	     */
	    countItems(): Q.Promise<number>;
	    /**
	     * Count the number of all action buttons.
	     * @returns A promise that resolves with the number of all items in the pill list.
	     */
	    countActionButtons(): Q.Promise<number>;
	    /**
	     * Returns an PillListItem that is found by its text.
	     * @param value A value of the item.
	     * @returns A promise that resolves to found PillListItem.
	     */
	    getItemByText(value: string): Q.Promise<PillListItem>;
	    /**
	     * Returns an PillListActionButton that is found by its text.
	     * @param title A title of the item.
	     * @returns A promise that resolves to found PillListActionButton.
	     */
	    getActionButtonByText(title: string): Q.Promise<PillListActionButton>;
	}
	export = PillList;

}
declare module 'MsPortalFx-Test/Controls/QuickStartHotSpot' {
	import Locator = require('MsPortalFx-Test/Locators/Locator');
	import HotSpot = require('MsPortalFx-Test/Controls/HotSpot'); class QuickStartHotSpot extends HotSpot {
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	}
	export = QuickStartHotSpot;

}
declare module 'MsPortalFx-Test/Controls/SelectorField' {
	import Q = require("q");
	import Locator = require('MsPortalFx-Test/Locators/Locator');
	import FormElement = require('MsPortalFx-Test/Controls/FormElement'); class SelectorField extends FormElement {
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	    /**
	     * Opens the associated picker by clicking on this field.
	     * @returns A promise that resolves when the the field has been clicked.
	     */
	    openPicker(): Q.Promise<void>;
	}
	export = SelectorField;

}
declare module 'MsPortalFx-Test/Controls/GroupDropDownFieldOption' {
	import PortalElement = require('MsPortalFx-Test/PortalElement');
	import Locator = require('MsPortalFx-Test/Locators/Locator'); class GroupDropDownFieldOption extends PortalElement {
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	}
	export = GroupDropDownFieldOption;

}
declare module 'MsPortalFx-Test/Controls/GroupDropDownField' {
	import Q = require("q");
	import FormElement = require('MsPortalFx-Test/Controls/FormElement');
	import Locator = require('MsPortalFx-Test/Locators/Locator'); class GroupDropDownField extends FormElement {
	    /**
	     * The collection of options that belong to this dropdown field.
	     */
	    private options;
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	    getOptions(): Q.Promise<any[]>;
	    /**
	     * Selects the first option that matches the given optionText.
	     * @param {string} optionText The text of the option to select.
	     * @returns A promise that resolves when the option has been selected.
	     */
	    selectOptionByText(optionText: string): Q.Promise<void>;
	    /**
	     * Gets the selected items
	     * @returns An array contains the values (not text) of the selected items.
	     */
	    getSelectedValues(): Q.Promise<any[]>;
	}
	export = GroupDropDownField;

}
declare module 'MsPortalFx-Test/Controls/ResourceGroupDropDownField' {
	import Q = require("q");
	import FormElement = require('MsPortalFx-Test/Controls/FormElement');
	import Locator = require('MsPortalFx-Test/Locators/Locator'); class ResourceGroupDropDown extends FormElement {
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	    /**
	     * Get the selected resource group.
	     * In the old style of the control with Radio buttons, this will switch the mode to UseExisting and return the value of the dropdown.
	     * In the new style of the control, this will return the value of the textbox if allowedMode is createNew, otherwise the value of the dropdown.
	     * @returns A promise that resolves when the resource group has been retrieved.
	     */
	    getSelectedResourceGroup(): Q.Promise<string>;
	    /**
	     * Set the selected resource group in the dropdown.
	     * This will switch the mode of the control if using the old style control with radio buttons.
	     * @param {string} value the value of the text to input in the dropdown control.
	     * @returns A promise that resolves when the resource group has been selected.
	     */
	    setSelectedResourceGroup(value: string): Q.Promise<void>;
	    /**
	     * Get the created resource group from the textbox or dropdown.
	     * This will switch the mode of the control if using the old style control with radio buttons.
	     */
	    getNewResourceGroup(): Q.Promise<string>;
	    /**
	     * Creates a new resource group using the resource group control.
	     * This will switch the mode of the control if using the old style control with radio buttons.
	     * In the old dropdown, this will switch the mode to Create New and enter the value in the textbox.
	     * In the new dropdown, this will either enter the value in the textbox (mode = createNew)
	     * or use the dialog to create a new dropdown option.
	     * @param {string} value The name for the new resource group.
	     * @returns A promise that resolves when the new resource group has been selected.
	     */
	    setNewResourceGroup(resourceGroupName: string): Q.Promise<void>;
	    private _createResourceGroupInDialog;
	    private readonly _dialog;
	    private readonly _createNewButton;
	    private _clickCreateNewButton;
	    private _hasRadioButtons;
	    private _isCreateOnlyMode;
	    private _selectCreateNewMode;
	    private _selectUseExistingMode;
	    private _textBox;
	    private _dropDown;
	}
	export = ResourceGroupDropDown;

}
declare module 'MsPortalFx-Test/Controls' {
	export import AccessHotSpot = require('MsPortalFx-Test/Controls/AccessHotSpot');
	export import AsyncFileUpload = require('MsPortalFx-Test/Controls/AsyncFileUpload');
	export import CheckBoxField = require('MsPortalFx-Test/Controls/CheckBoxField');
	export import CopyableLabel = require('MsPortalFx-Test/Controls/CopyableLabel');
	export import CreatorAndSelectorField = require('MsPortalFx-Test/Controls/CreatorAndSelectorField');
	export import CreateComboBoxField = require('MsPortalFx-Test/Controls/CreateComboBoxField');
	export import DropDownField = require('MsPortalFx-Test/Controls/DropDownField');
	export import DropDownFieldOption = require('MsPortalFx-Test/Controls/DropDownFieldOption');
	export import EditableGrid = require('MsPortalFx-Test/Controls/EditableGrid');
	export import DataGrid = require('MsPortalFx-Test/Controls/DataGrid');
	export import Editor = require('MsPortalFx-Test/Controls/Editor');
	export import Essentials = require('MsPortalFx-Test/Controls/Essentials');
	export import EssentialsItem = require('MsPortalFx-Test/Controls/EssentialsItem');
	export import EssentialsItemProperty = require('MsPortalFx-Test/Controls/EssentialsItemProperty');
	export import EssentialsItemPropertyType = require('MsPortalFx-Test/Controls/EssentialsItemPropertyType');
	export import FilterComboField = require('MsPortalFx-Test/Controls/FilterComboField');
	export import FormElement = require('MsPortalFx-Test/Controls/FormElement');
	export import FormSection = require('MsPortalFx-Test/Controls/FormSection');
	export import Grid = require('MsPortalFx-Test/Controls/Grid');
	export import GridRow = require('MsPortalFx-Test/Controls/GridRow');
	export import GridCell = require('MsPortalFx-Test/Controls/GridCell');
	export import HotSpot = require('MsPortalFx-Test/Controls/HotSpot');
	export import MonitorChart = require('MsPortalFx-Test/Controls/MonitorChart');
	export import OptionsGroupField = require('MsPortalFx-Test/Controls/OptionsGroupField');
	export import OptionsGroupFieldOption = require('MsPortalFx-Test/Controls/OptionsGroupFieldOption');
	export import PasswordField = require('MsPortalFx-Test/Controls/PasswordField');
	export import PillList = require('MsPortalFx-Test/Controls/PillList');
	export import PillListActionButton = require('MsPortalFx-Test/Controls/PillListActionButton');
	export import PillListItem = require('MsPortalFx-Test/Controls/PillListItem');
	export import PillListOverflowItem = require('MsPortalFx-Test/Controls/PillListOverflowItem');
	export import QuickStartHotSpot = require('MsPortalFx-Test/Controls/QuickStartHotSpot');
	export import SelectorField = require('MsPortalFx-Test/Controls/SelectorField');
	export import TextField = require('MsPortalFx-Test/Controls/TextField');
	export import GroupDropDownField = require('MsPortalFx-Test/Controls/GroupDropDownField');
	export import GroupDropDownFieldOption = require('MsPortalFx-Test/Controls/GroupDropDownFieldOption');
	export import ResourceGroupDropDownField = require('MsPortalFx-Test/Controls/ResourceGroupDropDownField');

}
declare module 'MsPortalFx-Test/Blades/BrowseResourceColumnPickerBlade' {
	import Q = require("q");
	import Controls = require('MsPortalFx-Test/Controls');
	import testFx = require('MsPortalFx-Test/Index');
	import Blade = require('MsPortalFx-Test/Blades/Blade'); class BrowseResourceColumnPickerBlade extends Blade {
	    /**
	     * The grid that contains all columns that can be selected
	     */
	    sourceGrid: Controls.EditableGrid;
	    columnGrid: Controls.EditableGrid;
	    /**
	     * Waits until the columns grid has rows then selects each column contained within columnsToSelect then clicks
	     * the update action
	     * @param {Array<string>} columnsToSelect is the list of columns to click on
	     * @param {number} [timeout] is an optional timeout to wait for the grid to have rows
	     * @returns A promise that resolves with this blade instance after the changes.
	     */
	    updateSelectedColumns(columnsToSelect: string[], timeout?: number): Q.Promise<BrowseResourceColumnPickerBlade>;
	    /**
	     * Waits until the columns grid has rows then adds each column contained within columnsToAdd then clicks
	     * the apply button
	     * @param {Array<string>} columnsToAdd is the list of columns to add to the selected columns
	     * @param {number} [timeout] is an optional timeout to wait for the grid to have rows
	     * @returns A promise that resolves with this blade instance after the changes.
	     */
	    addColumns(columnsToAdd: string[], timeout?: number): Q.Promise<BrowseResourceColumnPickerBlade>;
	    /**
	     * Waits until the columns grid has rows then removes each column contained within columnsToRemove then clicks
	     * the apply button
	     * @param {Array<string>} columnsToRemove is the list of columns to add to the selected columns
	     * @param {number} [timeout] is an optional timeout to wait for the grid to have rows
	     * @returns A promise that resolves with this blade instance after the changes.
	     */
	    removeColumns(columnsToRemove: string[], timeout?: number): Q.Promise<BrowseResourceColumnPickerBlade>;
	    /**
	     * Waits until the columns grid has rows then sets each column contained within columnsToSelect then clicks
	     * the apply button
	     * @param {Array<string>} columnsToSelect is the list of columns to be the selected columns
	     * @param {number} [timeout] is an optional timeout to wait for the grid to have rows
	     * @returns A promise that resolves with this blade instance after the changes.
	     */
	    setColumns(columnsToSelect: string[], timeout?: number): Q.Promise<BrowseResourceColumnPickerBlade>;
	    /**
	     * Clicks the apply button on the blade.
	     * @returns A promise that resolves with this blade instance when apply button has been clicked.
	     */
	    apply(): Q.Promise<BrowseResourceColumnPickerBlade>;
	    /**
	     * Clicks the reset button on the blade.
	     * @returns A promise that resolves with this blade instance when reset button has been clicked.
	     */
	    reset(): Q.Promise<BrowseResourceColumnPickerBlade>;
	    /**
	     * Gets the apply button on the blade (button or action bar button).
	     * @returns A promise that resolves with the apply button.
	     */
	    readonly applyButton: Q.Promise<testFx.PortalElement>;
	    /**
	     * Gets the reset button on the blade (button or action bar button).
	     * @returns A promise that resolves with the reset button.
	     */
	    readonly resetButton: Q.Promise<testFx.PortalElement>;
	    /**
	     * Gets the add column button on the blade.
	     * @returns A promise that resolves with the add column button.
	     */
	    readonly addButton: Q.Promise<testFx.PortalElement>;
	    /**
	     * Gets the remove column button on the blade.
	     * @returns A promise that resolves with the remove column button.
	     */
	    readonly removeButton: Q.Promise<testFx.PortalElement>;
	    private _addSelectedColumn;
	    private _removeSelectedColumn;
	    private _getActionButtons;
	    private _hasActionButtons;
	    private _foundActionButtons;
	}
	export = BrowseResourceColumnPickerBlade;

}
declare module 'MsPortalFx-Test/Blades/ResourceFilterTextField' {
	import Q = require("q");
	import Locator = require('MsPortalFx-Test/Locators/Locator');
	import TextField = require('MsPortalFx-Test/Controls/TextField'); class ResourceFilterTextField extends TextField {
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	    /**
	     * Enters the specified text in the TextField.
	     * @param var_args The text to enter.
	     * @returns A promise that resolves when the text has been entered and the ResourceTextFields ratelimit completed
	     */
	    sendKeys(...var_args: string[]): Q.Promise<void>;
	}
	export = ResourceFilterTextField;

}
declare module 'MsPortalFx-Test/Blades/BrowseResourceBlade' {
	import Q = require("q");
	import testFx = require('MsPortalFx-Test/Index');
	import Blade = require('MsPortalFx-Test/Blades/Blade');
	import BrowseResourceColumnPickerBlade = require('MsPortalFx-Test/Blades/BrowseResourceColumnPickerBlade'); class BrowseResourceBlade extends Blade {
	    /**
	     * The text field used to filter browse results.
	     */
	    filterTextField: testFx.Blades.ResourceFilterTextField;
	    /**
	     * The grid that shows the browse results.
	     */
	    grid: testFx.Blades.BrowseResourceBladeGrid;
	    /**
	     * Filters the browse results using the specified filter.
	     * @param {string} filter The filter.
	     * @returns A promise that resolves with this blade instance when filtered browse results are available.
	     */
	    filterItems(filter: string): Q.Promise<BrowseResourceBlade>;
	    /**
	     * Opens the Browse Column picker blade
	     * @param {number} [timeout] optional timeout to wait for the columns blade to load
	     * @returns A promise that resolves with the instance of the BrowseResourceColumnPickerBlade
	     */
	    openColumnsPickerBlade(timeout?: number): Q.Promise<BrowseResourceColumnPickerBlade>;
	}
	export = BrowseResourceBlade;

}
declare module 'MsPortalFx-Test/Blades/CreateBlade' {
	import Blade = require('MsPortalFx-Test/Blades/Blade');
	import CreateActionBar = require('MsPortalFx-Test/ActionBars/CreateActionBar'); class CreateBlade extends Blade {
	    /**
	     * The action bar contained in the blade.
	     */
	    actionBar: CreateActionBar;
	}
	export = CreateBlade;

}
declare module 'MsPortalFx-Test/Blades/ResourceGroupCreateBlade' {
	import Q = require("q");
	import testFx = require('MsPortalFx-Test/Index');
	import Locator = require('MsPortalFx-Test/Locators/Locator');
	import Blade = require('MsPortalFx-Test/Blades/Blade'); class ResourceGroupCreateBlade extends Blade {
	    /**
	     * Gets the name text field.
	     */
	    nameTextField: testFx.Controls.TextField;
	    /**
	     * Gets the name text field.
	     */
	    subscriptionField: testFx.Controls.GroupDropDownField;
	    /**
	     * Gets the name text field.
	     */
	    locationField: testFx.Controls.GroupDropDownField;
	    /**
	     * Gets the name text field.
	     */
	    tagsGrid: testFx.Controls.EditableGrid;
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	    /**
	     * Gets the Review + create or Create button on the blade.
	     * @returns A promise that resolves with the Create or Review + Create button.
	     */
	    readonly createButton: Q.Promise<testFx.PortalElement>;
	    /**
	     * Fills out the fields on the create resource group form.
	     *
	     * @resourceGroupName The name of the resource group.
	     * @subscription The subscription that the resource group should be created in.
	     * @location The location of the resource group.
	     */
	    fillRequiredFields(resourceGroupName: string, subscription: string, location: string): Q.Promise<ResourceGroupCreateBlade>;
	}
	export = ResourceGroupCreateBlade;

}
declare module 'MsPortalFx-Test/Blades/MoveResourcesBladeGridCell' {
	import PortalElement = require('MsPortalFx-Test/PortalElement');
	import GridCell = require('MsPortalFx-Test/Controls/GridCell'); class MoveResourcesBladeGridCell extends GridCell {
	    /**
	     * Gets the item name.
	     */
	    itemName: PortalElement;
	    /**
	     * Gets the item type.
	     */
	    itemType: PortalElement;
	}
	export = MoveResourcesBladeGridCell;

}
declare module 'MsPortalFx-Test/Blades/MoveResourcesBladeGridRow' {
	import GridRow = require('MsPortalFx-Test/Controls/GridRow');
	import GridCell = require('MsPortalFx-Test/Controls/GridCell');
	import MoveResourcesBladeGridCell = require('MsPortalFx-Test/Blades/MoveResourcesBladeGridCell'); class MoveResourcesBladeGridRow extends GridRow {
	    /**
	     * Gets the checkbox cell.
	     */
	    checkBoxCell: GridCell;
	    /**
	     * Gets the resource cell.
	     */
	    resourceCell: MoveResourcesBladeGridCell;
	}
	export = MoveResourcesBladeGridRow;

}
declare module 'MsPortalFx-Test/Blades/MoveResourcesBladeGrid' {
	import ElementArrayFinder = require('MsPortalFx-Test/ElementArrayFinder');
	import Locator = require('MsPortalFx-Test/Locators/Locator');
	import Grid = require('MsPortalFx-Test/Controls/Grid');
	import MoveResourcesBladeGridRow = require('MsPortalFx-Test/Blades/MoveResourcesBladeGridRow'); class MoveResourcesBladeGrid extends Grid {
	    /**
	     * Gets the grid rows.
	     */
	    rows: ElementArrayFinder<MoveResourcesBladeGridRow>;
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	}
	export = MoveResourcesBladeGrid;

}
declare module 'MsPortalFx-Test/Notifications/Notification' {
	import Locator = require('MsPortalFx-Test/Locators/Locator');
	import PortalElement = require('MsPortalFx-Test/PortalElement'); class Notification extends PortalElement {
	    /**
	     * The notification title.
	     */
	    title: PortalElement;
	    /**
	     * The notification title.
	     */
	    progressTitle: PortalElement;
	    /**
	     * The notification description.
	     */
	    description: PortalElement;
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	}
	export = Notification;

}
declare module 'MsPortalFx-Test/Notifications/NotificationsPane' {
	import Q = require("q");
	import testFx = require('MsPortalFx-Test/Index');
	import Locator = require('MsPortalFx-Test/Locators/Locator');
	import PortalElement = require('MsPortalFx-Test/PortalElement');
	import Notification = require('MsPortalFx-Test/Notifications/Notification'); class NotificationsPane extends PortalElement {
	    /**
	     * The list of displayed notifications.
	     */
	    notifications: testFx.ElementArrayFinder<testFx.Notifications.Notification>;
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	    /**
	     * Closes the notifications pane.
	     */
	    close(): Q.Promise<void>;
	    /**
	     * Waits for a new notification to show up given the specified parameters. An error is thrown if the notification
	     * does not show up after the timeout is reached.
	     *
	     * @param {string} title The notification title.
	     * @param {string} description The notification description.
	     * @param {number} timeout The time to wait, in milliseconds, for the notification to show up.
	     * @returns The found Notification.
	     */
	    waitForNewNotification(title?: string, description?: string, timeout?: number): Q.Promise<Notification>;
	    private tryGetText;
	}
	export = NotificationsPane;

}
declare module 'MsPortalFx-Test/Blades/MoveResourcesBlade' {
	import Q = require("q");
	import testFx = require('MsPortalFx-Test/Index');
	import Blade = require('MsPortalFx-Test/Blades/Blade');
	import Locator = require('MsPortalFx-Test/Locators/Locator'); class MoveResourcesBlade extends Blade {
	    /**
	     * The action bar contained in the blade.
	     */
	    actionBar: testFx.ActionBars.GenericActionBar;
	    private subscriptionsDropDown;
	    private relatedResourcesGrid;
	    private confirmationCheckBox;
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	    /**
	     * Starts a move operation using the specified options.
	     * @param options The options used to populate the blade.
	     * @returns A promise that resolves when the move operation has started.
	     */
	    startMove(options: {
	        currentResourceGroup: string;
	        targetResourceGroup: string;
	        createNewGroup?: boolean;
	        subscriptionName?: string;
	        relatedResources?: string[];
	        timeout?: number;
	    }): Q.Promise<void>;
	}
	export = MoveResourcesBlade;

}
declare module 'MsPortalFx-Test/Blades/ContentState' {
	 enum ContentState {
	    /**
	     * No content state.
	     */
	    None = 0,
	    /**
	     * Success content state.
	     */
	    Success = 1,
	    /**
	     * Warning content state.
	     */
	    Warning = 2,
	    /**
	     * Error content state
	     */
	    Error = 3,
	    /**
	     * Info content state
	     */
	    Info = 4
	}
	export = ContentState;

}
declare module 'MsPortalFx-Test/Blades/BladeStatusBar' {
	import Q = require("q");
	import PortalElement = require('MsPortalFx-Test/PortalElement');
	import Locator = require('MsPortalFx-Test/Locators/Locator');
	import ContentState = require('MsPortalFx-Test/Blades/ContentState'); class BladeStatusBar extends PortalElement {
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	    /**
	     * Gets the state of the statusBar.
	     */
	    getContentState(): Q.Promise<ContentState>;
	}
	export = BladeStatusBar;

}
declare module 'MsPortalFx-Test/Controls/MenuBlade/DetailBlade' {
	import Blade = require('MsPortalFx-Test/Blades/Blade'); class DetailBlade extends Blade {
	    protected bladeClass: string;
	}
	export = DetailBlade;

}
declare module 'MsPortalFx-Test/Controls/MenuBlade/MenuBladeItem' {
	import Q = require("q");
	/**
	 * Represents a Menu Blade menu item.
	 */
	interface MenuBladeItem {
	    getText: () => Q.Promise<string>;
	    click: () => void;
	}
	export = MenuBladeItem;

}
declare module 'MsPortalFx-Test/Controls/List/ListViewItem' {
	import PortalElement = require('MsPortalFx-Test/PortalElement');
	import Locator = require('MsPortalFx-Test/Locators/Locator'); class ListViewItem extends PortalElement {
	    private listItemClassName;
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	}
	export = ListViewItem;

}
declare module 'MsPortalFx-Test/Controls/List/ListViewGroup' {
	import PortalElement = require('MsPortalFx-Test/PortalElement');
	import ElementArrayFinder = require('MsPortalFx-Test/ElementArrayFinder');
	import Locator = require('MsPortalFx-Test/Locators/Locator');
	import ListViewItem = require('MsPortalFx-Test/Controls/List/ListViewItem'); class ListViewGroup extends PortalElement {
	    private groupClassName;
	    private groupHeaderClassName;
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	    /**
	     * Gets all of the rows of the group.
	     */
	    readonly listViewItems: ElementArrayFinder<ListViewItem>;
	    /**
	     * Gets the header of the group.
	     */
	    readonly Header: PortalElement;
	    /**
	     * Gets the specified item.
	     * @param identifier Specified identifier
	     */
	    getListViewItem(identifier: string): ListViewItem;
	}
	export = ListViewGroup;

}
declare module 'MsPortalFx-Test/Controls/List/ListView' {
	import Q = require("q");
	import PortalElement = require('MsPortalFx-Test/PortalElement');
	import ElementArrayFinder = require('MsPortalFx-Test/ElementArrayFinder');
	import Locator = require('MsPortalFx-Test/Locators/Locator');
	import ListViewItem = require('MsPortalFx-Test/Controls/List/ListViewItem');
	import ListViewGroup = require('MsPortalFx-Test/Controls/List/ListViewGroup'); class ListView extends PortalElement {
	    private rootClassName;
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	    /**
	     * Gets all of the rows of the group.
	     */
	    readonly groups: ElementArrayFinder<ListViewGroup>;
	    /**
	     * Gets all of the rows of the group.
	     */
	    readonly items: ElementArrayFinder<ListViewItem>;
	    /**
	     * Gets the specified item.
	     * @param identifier Specified identifier
	     */
	    getListViewItem(identifier: string): Q.Promise<any>;
	}
	export = ListView;

}
declare module 'MsPortalFx-Test/Blades/MenuBlade' {
	import Q = require("q");
	import testFx = require('MsPortalFx-Test/Index');
	import PortalElement = require('MsPortalFx-Test/PortalElement');
	import Blade = require('MsPortalFx-Test/Blades/Blade');
	import ElementArrayFinder = require('MsPortalFx-Test/ElementArrayFinder');
	import DetailBlade = require('MsPortalFx-Test/Controls/MenuBlade/DetailBlade');
	import CommandBar = require('MsPortalFx-Test/Controls/CommandBar');
	import MenuBladeItem = require('MsPortalFx-Test/Controls/MenuBlade/MenuBladeItem');
	import CommandBarItem = require('MsPortalFx-Test/Commands/CommandBarItem'); class MenuBlade extends Blade {
	    protected bladeClass: string;
	    /**
	     * Gets the blade status bar.
	     */
	    statusBar: testFx.Blades.BladeStatusBar;
	    /**
	     * Gets the blade command bar.
	     */
	    commandBar: CommandBar;
	    /**
	     * Gets the content detail blade.
	     */
	    detailBlade: DetailBlade;
	    /**
	     * Gets the blade's commandbar buttons.
	     */
	    commandBarItemsAsync(): Q.Promise<CommandBarItem[]>;
	    /**
	     * Gets the specified menu item.
	     */
	    getMenuItem(title: string): Q.Promise<MenuBladeItem>;
	    /**
	     * Opens a menu item in the detail blade.
	     */
	    openMenuItem(title: string): Q.Promise<MenuBlade>;
	    /**
	     * Gets items of specified type from the detail blade.
	     */
	    getDetailBladeItems<T extends PortalElement>(elementType: {
	        new (): T;
	    }): T;
	    /**
	     * Gets items of specified type from the detail blade.
	     */
	    getDetailBladeItemsAsync<T extends PortalElement>(elementType: {
	        new (): T;
	    }): Q.Promise<T>;
	    /**
	     * Gets all the items of specified type from the detail blade.
	     */
	    getAllDetailBladeItems<T extends PortalElement>(elementType: {
	        new (): T;
	    }): testFx.ElementArrayFinder<T>;
	    readonly title: PortalElement;
	    readonly subtitle: PortalElement;
	    readonly commandBarItems: ElementArrayFinder<CommandBarItem>;
	    readonly overviewMenuItem: MenuBladeItem;
	    openOverviewItem(): Q.Promise<MenuBlade>;
	}
	export = MenuBlade;

}
declare module 'MsPortalFx-Test/Blades/PickerBlade' {
	import Q = require("q");
	import Blade = require('MsPortalFx-Test/Blades/Blade');
	import Grid = require('MsPortalFx-Test/Controls/Grid');
	import SelectorField = require('MsPortalFx-Test/Controls/SelectorField'); class PickerBlade extends Blade {
	    /**
	     * Gets the create new field.
	     */
	    createNewField: SelectorField;
	    /**
	     * Gets the blade grid.
	     */
	    grid: Grid;
	    /**
	     * Picks the specified item in the grid.
	     * @param {string} item The item to pick.
	     * @returns A promise that resolves when the specified item has been picked.
	     */
	    pickItem(item: string): Q.Promise<void>;
	}
	export = PickerBlade;

}
declare module 'MsPortalFx-Test/Parts/PartProperty' {
	import Locator = require('MsPortalFx-Test/Locators/Locator');
	import PortalElement = require('MsPortalFx-Test/PortalElement'); class PartProperty extends PortalElement {
	    private nameClass;
	    private valueClass;
	    /**
	     * The name of the property.
	     */
	    name: PortalElement;
	    /**
	     * The value of the property.
	     */
	    value: PortalElement;
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	    /**
	     * Builds a PartProperty locator using the specified options.
	     * @param options The options used to build the locator.
	     * @returns A Locator instance.
	     */
	    buildLocator(options: {
	        name?: string;
	    }): Locator;
	}
	export = PartProperty;

}
declare module 'MsPortalFx-Test/Blades/PropertiesBlade' {
	import Locator = require('MsPortalFx-Test/Locators/Locator');
	import ElementArrayFinder = require('MsPortalFx-Test/ElementArrayFinder');
	import Blade = require('MsPortalFx-Test/Blades/Blade');
	import PartProperty = require('MsPortalFx-Test/Parts/PartProperty'); class PropertiesBlade extends Blade {
	    /**
	     * The collection of part properties contained in this part.
	     */
	    properties: ElementArrayFinder<PartProperty>;
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	    /**
	     * Gets the part property that corresponds to the specified options.
	     * @param options The options used to find the part property.
	     */
	    property(options: {
	        name: string;
	    }): PartProperty;
	}
	export = PropertiesBlade;

}
declare module 'MsPortalFx-Test/Blades/QuickStartBlade' {
	import Q = require("q");
	import Locator = require('MsPortalFx-Test/Locators/Locator');
	import Blade = require('MsPortalFx-Test/Blades/Blade'); class QuickStartBlade extends Blade {
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	    /**
	     * Clicks the specified link.
	     * @param {string} linkText The text of the link to click.
	     * @returns A promise that resolves when the link has been clicked.
	     */
	    clickLink(linkText: string): Q.Promise<void>;
	}
	export = QuickStartBlade;

}
declare module 'MsPortalFx-Test/Blades/SpecPicker' {
	import PortalElement = require('MsPortalFx-Test/PortalElement');
	import Locator = require('MsPortalFx-Test/Locators/Locator'); class SpecPicker extends PortalElement {
	    private headerSpecCodeClass;
	    /**
	     * Gets the SpecPicker code.
	     */
	    code: PortalElement;
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	    /**
	     * Builds a SpecPicker locator using the specified options.
	     * @param options The options used to build the locator.
	     * @returns A Locator instance.
	     */
	    buildLocator(options: {
	        code?: string;
	    }): Locator;
	}
	export = SpecPicker;

}
declare module 'MsPortalFx-Test/Blades/SpecPickerBlade' {
	import Q = require("q");
	import testFx = require('MsPortalFx-Test/Index');
	import Blade = require('MsPortalFx-Test/Blades/Blade'); class SpecPickerBlade extends Blade {
	    /**
	     * The collection of specs in this blade.
	     */
	    specs: testFx.ElementArrayFinder<testFx.Blades.SpecPicker>;
	    /**
	     * The action bar contained in this blade.
	     */
	    actionBar: testFx.ActionBars.PickerActionBar;
	    /**
	     * Picks the spec that corresponds to the specified code.
	     * @param {string} specCode The code associated to the spec to pick.
	     * @returns A promise that resolves when the spec has been picked.
	     */
	    pickSpec(specCode: string): Q.Promise<void>;
	}
	export = SpecPickerBlade;

}
declare module 'MsPortalFx-Test/Blades/UsersBlade' {
	import Locator = require('MsPortalFx-Test/Locators/Locator');
	import Blade = require('MsPortalFx-Test/Blades/Blade'); class UsersBlade extends Blade {
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	}
	export = UsersBlade;

}
declare module 'MsPortalFx-Test/Blades' {
	export import Blade = require('MsPortalFx-Test/Blades/Blade');
	export import BrowseResourceBlade = require('MsPortalFx-Test/Blades/BrowseResourceBlade');
	export import BrowseResourceBladeGrid = require('MsPortalFx-Test/Blades/BrowseResourceBladeGrid');
	export import BrowseResourceBladeGridRow = require('MsPortalFx-Test/Blades/BrowseResourceBladeGridRow');
	export import CreateBlade = require('MsPortalFx-Test/Blades/CreateBlade');
	export import ResourceGroupCreateBlade = require('MsPortalFx-Test/Blades/ResourceGroupCreateBlade');
	export import MoveResourcesBlade = require('MsPortalFx-Test/Blades/MoveResourcesBlade');
	export import MenuBlade = require('MsPortalFx-Test/Blades/MenuBlade');
	export import PickerBlade = require('MsPortalFx-Test/Blades/PickerBlade');
	export import PropertiesBlade = require('MsPortalFx-Test/Blades/PropertiesBlade');
	export import QuickStartBlade = require('MsPortalFx-Test/Blades/QuickStartBlade');
	export import ResourceFilterTextField = require('MsPortalFx-Test/Blades/ResourceFilterTextField');
	export import SpecPicker = require('MsPortalFx-Test/Blades/SpecPicker');
	export import SpecPickerBlade = require('MsPortalFx-Test/Blades/SpecPickerBlade');
	export import UsersBlade = require('MsPortalFx-Test/Blades/UsersBlade');
	export import BladeDialog = require('MsPortalFx-Test/Blades/BladeDialog');
	export import BladeStatusBar = require('MsPortalFx-Test/Blades/BladeStatusBar');
	export import ContentState = require('MsPortalFx-Test/Blades/ContentState');

}
declare module 'MsPortalFx-Test/Locators' {
	export import By = require('MsPortalFx-Test/Locators/By');
	export import ChainedLocator = require('MsPortalFx-Test/Locators/ChainedLocator');
	export import ContentLocator = require('MsPortalFx-Test/Locators/ContentLocator');
	export import Locator = require('MsPortalFx-Test/Locators/Locator');
	export import SimpleLocator = require('MsPortalFx-Test/Locators/SimpleLocator');

}
declare module 'MsPortalFx-Test/Parts/CollectionPart' {
	import Q = require("q");
	import Locator = require('MsPortalFx-Test/Locators/Locator');
	import Part = require('MsPortalFx-Test/Parts/Part');
	import Grid = require('MsPortalFx-Test/Controls/Grid'); class CollectionPart extends Part {
	    /**
	     * @returns {Grid} The grid that shows the collection elements.
	     */
	    readonly grid: Grid;
	    /**
	     * @returns {Promise} A promise that resolves with the rollup count of this CollectionPart
	     */
	    getRollupCount(): Q.Promise<number>;
	    /**
	     * @returns {Promise} A promise that resolves with the rollup label of this CollectionPart
	     */
	    getRollupLabel(): Q.Promise<string>;
	    /**
	     * @returns {Promise} A promise that resolves with the title of this CollectionPart
	     */
	    getTitle(): Q.Promise<string>;
	    /**
	     * Gets the locator associated to this element.
	     * @returns {Locator} A Locator instance.
	     */
	    readonly locator: Locator;
	}
	export = CollectionPart;

}
declare module 'MsPortalFx-Test/Parts/CollectionSummaryPart' {
	import Q = require("q");
	import Locator = require('MsPortalFx-Test/Locators/Locator');
	import Part = require('MsPortalFx-Test/Parts/Part'); class CollectionSummaryPart extends Part {
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	    /**
	     * Gets the count of the part.
	     */
	    getCount(): Q.Promise<number>;
	    /**
	     * Gets the title of the part.
	     */
	    getTitle(): Q.Promise<string>;
	}
	export = CollectionSummaryPart;

}
declare module 'MsPortalFx-Test/Parts/DonutPart' {
	import Q = require("q");
	import Locator = require('MsPortalFx-Test/Locators/Locator');
	import Part = require('MsPortalFx-Test/Parts/Part'); class DonutPart extends Part {
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	    /**
	     * Gets the caption of the donut center text. Note: This changes based on selection.
	     */
	    getCaption(): Q.Promise<string>;
	    /**
	     * Gets the value of the donut center text. Note: This changes based on selection.
	     */
	    getCenterValue(): Q.Promise<string>;
	    /**
	     * Gets the unit of the donut center text. Note: This changes based on selection.
	     */
	    getUnit(): Q.Promise<string>;
	}
	export = DonutPart;

}
declare module 'MsPortalFx-Test/Parts/PropertiesPart' {
	import ElementArrayFinder = require('MsPortalFx-Test/ElementArrayFinder');
	import Locator = require('MsPortalFx-Test/Locators/Locator');
	import Part = require('MsPortalFx-Test/Parts/Part');
	import PartProperty = require('MsPortalFx-Test/Parts/PartProperty'); class PropertiesPart extends Part {
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	    /**
	     * Gets properties contained in this part.
	     * @returns An ElementArrayFinder of PartProperty instances.
	     */
	    readonly properties: ElementArrayFinder<PartProperty>;
	    /**
	     * Gets the part property that corresponds to the specified options.
	     * @param options The options used to find the part property.
	     */
	    property(options: {
	        name: string;
	    }): PartProperty;
	}
	export = PropertiesPart;

}
declare module 'MsPortalFx-Test/Parts/PricingTierPart' {
	import Locator = require('MsPortalFx-Test/Locators/Locator');
	import Part = require('MsPortalFx-Test/Parts/Part'); class PricingTierPart extends Part {
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	}
	export = PricingTierPart;

}
declare module 'MsPortalFx-Test/Parts/ResourceSummaryPart' {
	import Locator = require('MsPortalFx-Test/Locators/Locator');
	import ElementArrayFinder = require('MsPortalFx-Test/ElementArrayFinder');
	import Part = require('MsPortalFx-Test/Parts/Part');
	import PartProperty = require('MsPortalFx-Test/Parts/PartProperty');
	import HotSpot = require('MsPortalFx-Test/Controls/HotSpot');
	import QuickStartHotSpot = require('MsPortalFx-Test/Controls/QuickStartHotSpot');
	import AccessHotSpot = require('MsPortalFx-Test/Controls/AccessHotSpot'); class ResourceSummaryPart extends Part {
	    /**
	     * The resource group property.
	     */
	    resourceGroupProperty: PartProperty;
	    /**
	     * The Quick Start hotspot.
	     */
	    quickStartHotSpot: QuickStartHotSpot;
	    /**
	     * The Access hotspot.
	     */
	    accessHotSpot: AccessHotSpot;
	    /**
	     * The Settings hotspot.
	     */
	    settingsHotSpot: HotSpot;
	    /**
	     * The collection of part properties contained in this part.
	     */
	    properties: ElementArrayFinder<PartProperty>;
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	    /**
	     * Gets the part property that corresponds to the specified options.
	     * @param options The options used to find the part property.
	     */
	    property(options: {
	        name: string;
	    }): PartProperty;
	}
	export = ResourceSummaryPart;

}
declare module 'MsPortalFx-Test/Parts' {
	export import CollectionPart = require('MsPortalFx-Test/Parts/CollectionPart');
	export import CollectionSummaryPart = require('MsPortalFx-Test/Parts/CollectionSummaryPart');
	export import Part = require('MsPortalFx-Test/Parts/Part');
	export import PartProperty = require('MsPortalFx-Test/Parts/PartProperty');
	export import DonutPart = require('MsPortalFx-Test/Parts/DonutPart');
	export import PropertiesPart = require('MsPortalFx-Test/Parts/PropertiesPart');
	export import PricingTierPart = require('MsPortalFx-Test/Parts/PricingTierPart');
	export import ResourceSummaryPart = require('MsPortalFx-Test/Parts/ResourceSummaryPart');
	export import Tile = require('MsPortalFx-Test/Parts/Tile');

}
declare module 'MsPortalFx-Test/Commands/ListCommandPopup' {
	import Q = require("q");
	import Locator = require('MsPortalFx-Test/Locators/Locator');
	import PortalElement = require('MsPortalFx-Test/PortalElement'); class ListCommandPopup extends PortalElement {
	    /**
	     * Represents the collection of items in the popup.
	     */
	    private listItems;
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	    /**
	     * Selects an item by it's text from list popup.
	     * @param {string} text The item text.
	     * @returns  A promise that resolves when the item has been selected.
	     */
	    selectItemByText(text: string): Q.Promise<void>;
	}
	export = ListCommandPopup;

}
declare module 'MsPortalFx-Test/Commands' {
	export import CommandBarItem = require('MsPortalFx-Test/Commands/CommandBarItem');
	export import ContextMenu = require('MsPortalFx-Test/Commands/ContextMenu');
	export import ContextMenuItem = require('MsPortalFx-Test/Commands/ContextMenuItem');
	export import ListCommandPopup = require('MsPortalFx-Test/Commands/ListCommandPopup');

}
declare module 'MsPortalFx-Test/Utils/Arm' {
	import Q = require("q");
	/**
	 * ArmClient is simple abstraction around azure-arm-resource and adal to provide simple crud operations against a resource group
	 */
	export class ArmClient {
	    "use strict": any;
	    resourceManagerClient: any;
	    private maxRetries;
	    private retryDelay;
	    private static AzureDFEnvironment;
	    private static AzureStackEnvironment;
	    /**
	     * creates a new ArmClient that will use the supplied resourceManagerClient
	     * @param resourceManagerClient a resource management client created via azure-arm-resource module
	     */
	    constructor(resourceManagerClient: any);
	    /**
	     * creates an ArmClient configured with the given options
	     * @param {IArmClientOptions} [options]
	     * @returns {Q.Promise<ArmClient>} promise that resolves with a {ArmClient}
	     */
	    static createClient(options?: IArmClientOptions): Q.Promise<ArmClient>;
	    /**
	     * Creates a resource group with the given name and location
	     * @param {string} name is the name of the resource group to create
	     * @param {string} location is the location where the resource group should be created e.g centralus
	     * @param {number} [maxRetries] the number of times the client will attempt to create the resource group after failure. The default is 3.
	     * @result {Q.Promise<any>} returns a promise that resolves when the resource group is created
	     */
	    createResourceGroup(name: string, location: string, maxRetries?: number): Q.Promise<any>;
	    /**
	     * Deletes a resource group with the specified name
	     * @param {string} name is the name of the resource group to delete
	     * @param {number} [maxRetries] the number of times the client will attempt to delete the resource group after failure. The default is 3.
	     * @result {Q.Promise<any>} returns a promise that resolves when the resource group is delete
	     */
	    deleteResourceGroup(name: string, maxRetries?: number): Q.Promise<{}>;
	    /**
	     * gets an array of resource groups for the current subscription
	     * @param {number} [maxRetries] the number of times the client will attempt to list the resource groups after failure. The default is 3.
	     * @result {Q.Promise<any[]>} returns a promise that resolves with the list of resource groups returned
	     */
	    getResourceGroups(maxRetries?: number): Q.Promise<any[]>;
	    /**
	     * creates a resource with the specified options
	     * @param {IResourceOptions} options specifiying the resource you wish to create
	     * @param {number} [maxRetries] the number of times the client will attempt to create the resource after failure. The default is 3.
	     * @result {Q.Promise<any>} returns a promise that resolves when the resource is created
	     */
	    createResource(options: IResourceOptions, maxRetries?: number): Q.Promise<string>;
	    private executeWithRetries;
	    private static getAccessToken;
	    private static loginWithServicePrincipalSecret;
	}
	/**
	 * options required to create an {ArmClient}.
	 */
	export interface IArmClientOptions {
	    /**
	     * the AAD authority url for your tenant id e.g "https://login.windows.net/TENANT_ID_HERE",
	     */
	    aadAuthorityUrl: string;
	    /**
	     * the AAD client id
	     */
	    aadClientId: string;
	    /**
	     * the AAD client secret corresponding to the client id
	     */
	    aadClientSecret: string;
	    /**
	     * arm endpoint you wish to use e.g https://management.azure.com
	     */
	    armEndpoint: string;
	    /**
	     * service management endpoint used by adal client to get an authorization token e.g https://management.core.windows.net/
	     */
	    managementEndpoint: string;
	    /**
	     * the subscription id with which you want the client to act upon
	     */
	    subscriptionId: string;
	}
	/**
	 * options required to create a resource group
	 */
	export interface IResourceOptions {
	    /**
	     * the name of the resource
	     */
	    name: string;
	    /**
	     * the name of the resource group
	     */
	    resourceGroup: string;
	    /**
	     * the resource provider for creating the resource
	     */
	    resourceProvider: string;
	    /**
	     * provide parent resource identity
	     */
	    parentResourcePath?: string;
	    /**
	     * the resource type
	     */
	    resourceType: string;
	    /**
	     * the resource provider API version
	     */
	    resourceProviderApiVersion: string;
	    /**
	     * properties
	     */
	    properties: any;
	    /**
	     * location
	     */
	    location: string;
	}

}
declare module 'MsPortalFx-Test/Utils/ErrorHandling' {
	/**
	 * Returns a string respresentation of the specified error.
	 * @param {any} error The error to read.
	 * @returns A string respresentation of the specified error.
	 */
	export function readError(error: any): any;

}
declare module 'MsPortalFx-Test/Utils/WindowsCredentialManager' {
	 class WindowsCredentialManager {
	    /**
	     * Uses edgejs to retrieve a credential from the windows credential manager
	     * by invoking CredMan.cs which will inturn pinvoke the Win32 credential manager api.
	     */
	    private credManGetFuncSync;
	    /**
	     * Gets the credential matching the given targetName from the Windows Credential Manager.
	     * @param targetName the target name of the credential to retrieve.
	     */
	    getWindowsCredentialSync(targetName: string): any;
	}
	export = WindowsCredentialManager;

}
declare module 'MsPortalFx-Test/Utils/NConfWindowsCredentialManager' {
	import nconf = require("nconf"); class NConfWindowsCredentialManager extends nconf.Provider {
	    private wc;
	    /**
	     * Gets the password for the given key from Windows Credential Manager.
	     * @param key the key in windows credential manager to return the password of
	     */
	    get(key: any): any;
	    /**
	     * The provider name to use when registering this when calling nconf.use(...);
	     */
	    static ProviderName: string;
	}
	export = NConfWindowsCredentialManager;

}
declare module 'MsPortalFx-Test/Utils/UriBuilder' {
	export interface StringMap<T> {
	    [key: string]: T;
	}
	/**
	 * Provides basic query string manipulation functions.
	 */
	export class QueryStringBuilder {
	    private _qs;
	    private _qsMap;
	    private _leadingSeparator;
	    /**
	     * Initializes a new instance of the query string builder.
	     *
	     * @param qs The initial query string.
	     * @param leadingSeparator The leading character of the query string. ? by default.
	     */
	    constructor(qs?: string, leadingSeparator?: string);
	    /**
	     * Sets multiple parameters in the query string. Overwrites any existing values.
	     *
	     * @param keys The object that represents the key value pairs.
	     * @return The QueryStringBuilder instance.
	     */
	    setParameters(keys: StringMap<any>): QueryStringBuilder;
	    /**
	     * Sets a parameter in the query string. Overwrites any existing values.
	     *
	     * @param key The key of the parameter.
	     * @param value The value of the parameter.
	     * @return The QueryStringBuilder instance.
	     */
	    setParameter(key: string, value: any): QueryStringBuilder;
	    /**
	     * Gets the value of a parameter in the query string.
	     *
	     * @param key The key of the parameter.
	     * @return The value of the parameter. Undefined if the value doesn't exist.
	     */
	    getParameter(key: string): string;
	    /**
	     * Gets all values of the query string as a stringmap.
	     *
	     * @return StringMap of all query string key/value pairs.
	     */
	    getParameters(): StringMap<any>;
	    /**
	     * Removes multiple parameters from the query string.
	     *
	     * @param keys The keys of the parameter.
	     * @return The QueryStringBuilder instance.
	     */
	    removeParameters(keys: string[]): QueryStringBuilder;
	    /**
	     * Removes a parameter from the query string.
	     *
	     * @param key The key of the parameter.
	     * @return The QueryStringBuilder instance.
	     */
	    removeParameter(key: string): QueryStringBuilder;
	    /**
	     * Returns a string representation of the URI.
	     *
	     * @param includeLeadingSeparator Whether to emit the leading ?. Defaults to true.
	     * @return A string representation of the URI.
	     */
	    toString(includeLeadingSeparator?: boolean): string;
	}
	/**
	 * Provides basic URI manipulation functions.
	 */
	export class UriBuilder {
	    /**
	     * Gets or sets the path part of the URI. This includes the protocol, domain, port, and virtual path.
	     */
	    path: string;
	    /**
	     * Gets or sets the query string part of the URI. This is represented as key value pairs.
	     */
	    query: QueryStringBuilder;
	    /**
	     * Gets or sets the fragment of the URI. This includes everything after the # separator in the URI.
	     */
	    fragment: string;
	    /**
	     * Initializes a new instance of the URI.
	     */
	    constructor(uri?: string);
	    /**
	     * Returns a string representation of the URI.
	     */
	    toString(): string;
	}

}
declare module 'MsPortalFx-Test/Utils' {
	/**
	 * Provides Azure Resource Manager utilities
	 */
	export import Arm = require('MsPortalFx-Test/Utils/Arm');
	export import ErrorHandling = require('MsPortalFx-Test/Utils/ErrorHandling');
	export import NConfWindowsCredentialManager = require('MsPortalFx-Test/Utils/NConfWindowsCredentialManager');
	export import String = require('MsPortalFx-Test/Utils/String');
	export import Timeouts = require('MsPortalFx-Test/Utils/Timeouts');
	export import UriBuilder = require('MsPortalFx-Test/Utils/UriBuilder');
	export import WindowsCredentialManager = require('MsPortalFx-Test/Utils/WindowsCredentialManager');

}
declare module 'MsPortalFx-Test/StartBoard' {
	import Locator = require('MsPortalFx-Test/Locators/Locator');
	import PortalElement = require('MsPortalFx-Test/PortalElement');
	import ElementArrayFinder = require('MsPortalFx-Test/ElementArrayFinder');
	import Tile = require('MsPortalFx-Test/Parts/Tile'); class StartBoard extends PortalElement {
	    /**
	     * Represents the array of tiles currently pinned to the StartBoard.
	     */
	    tiles: ElementArrayFinder<Tile>;
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	}
	export = StartBoard;

}
declare module 'MsPortalFx-Test/ExpectedConditions' {
	/** ------------------------------------------- START OF THIRD PARTY NOTICE -----------------------------------------
	 * This file is based on or incorporates material from the projects listed below (Third Party IP).The original copyright notice and the license under which Microsoft received such Third Party IP, are set forth below.Such licenses and notices are provided for informational purposes only.Microsoft licenses the Third Party IP to you under the licensing terms for the Microsoft product.Microsoft reserves all other rights not expressly granted under this agreement, whether by implication, estoppel or otherwise.
	 *
	 * angular - protractor v3.0
	 * Copyright(c) 2010- 2015 Google, Inc.
	 *
	 * Provided for Informational Purposes Only
	 * MIT License
	 *
	 * Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the Software), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and / or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:
	 *
	 * The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.
	 *
	 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
	 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
	 * NONINFRINGEMENT.IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE
	 * LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION
	 * OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION
	 * WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
	 * ----------------------------------------------- END OF THIRD PARTY NOTICE ------------------------------------------
	 */
	import Q = require("q");
	import PortalElement = require('MsPortalFx-Test/PortalElement');
	/**
	 * An expectation for checking that an element is present on the DOM
	 * of a page. This does not necessarily mean that the element is visible.
	 * @param {PortalElement} portalElement The element to check.
	 * @returns A promise representing whether the element is present.
	 */
	export function isPresent(portalElement: PortalElement): () => Q.Promise<boolean>;
	/**
	 * An expectation for checking that an element is not present on the DOM
	 * of a page.
	 * @param {PortalElement} portalElement The element to check.
	 * @returns A promise representing whether the element is not present.
	 */
	export function isNotPresent(portalElement: PortalElement): () => Q.Promise<boolean>;
	/**
	 * An expectation for checking that an element is either invisible or not
	 * present on the DOM.
	 * @param {PortalElement} portalElement The element to check.
	 * @returns A promise representing whether the element is invisible.
	 */
	export function isNotDisplayed(portalElement: PortalElement): () => Q.Promise<boolean>;
	/**
	 * An expectation for checking that the specified attribute has the specified value in an element.
	 * @param {PortalElement} portalElement The element to check.
	 * @param {string} attributeName The name of the attribute.
	 * @param {string} attributeValue The value of the attribute.
	 * @returns A promise representing whether the specified attribute has the specified value in the element.
	 */
	export function hasAttributeValue(portalElement: PortalElement, attributeName: string, attributeValue: string): () => Q.Promise<boolean>;
	/**
	 * An expectation for checking that the specified attribute does not have the specified value in an element.
	 * @param {PortalElement} portalElement The element to check.
	 * @param {string} attributeName The name of the attribute.
	 * @param {string} attributeValue The value of the attribute.
	 * @returns A promise representing whether the specified attribute does not have the specified value in the element.
	 */
	export function doesNotHaveAttributeValue(portalElement: PortalElement, attributeName: string, attributeValue: string): () => Q.Promise<boolean>;

}
declare module 'MsPortalFx-Test/Tests/Parts' {
	import Q = require("q");
	/**
	 * Verifies that all the parts in the target blade can be pinned to the dashboard and that the pinned parts load and are not
	 * in an error state.
	 * @param {string} targetBladeDeepLink Deep link to the blade under test.
	 * @param {string} targetBladeTitle The title of the blade under test.
	 * @param {number} timeout The time, in milliseconds, to wait for the blade and parts to load.
	 * @returns A promise that resolves with a value that indicates whether the test passed.
	 */
	export function canPinAllBladeParts(targetBladeDeepLink: string, targetBladeTitle: string, timeout?: number): Q.Promise<boolean>;

}
declare module 'MsPortalFx-Test/Tests/Browse' {
	import Q = require("q");
	/**
	 * Tests context menu in browse contains expected commands.
	 * browse will be opened for the given resource type and the context menu for the first row in the grid
	 * will be opened.  The commands present in the context menu will be compared with the expectedCommands
	 *
	 * @param {string} resourceProvider The resource provider.
	 * @param {string} resourceType The resource type.
	 * @param {string} browseBladeTitle - The title of the browse blade for the resource.
	 * @param {number} columnIndex - The column to right click for the context menu.
	 * @param {Array<string>} expectedCommands - An array of strings representing the expected context menu commands.
	 * @param {number} [timeout] The time to wait, in milliseconds, for the context menu to show up
	 * @returns A promise that resolves when the test is completed
	 * @tutorial tutorial-browse-context-menu
	 */
	export function contextMenuContainsExpectedCommands(resourceProvider: string, resourceType: string, browseBladeTitle: string, columnIndex: number, expectedCommands: string[], timeout?: number): Promise<void>;
	/**
	 * Tests extension resource specific columns can be selected in browse and that after selection the union of defaultColumns and columnsToSelect are shown in the browse grid.
	 * In the case that they are not an AssertionError is thrown.
	 * @param {string} resourceProvider The resource provider.
	 * @param {string} resourceType The resource type.
	 * @param {string} browseBladeTitle - The title of the browse blade for the resource.
	 * @param {Array<ColumnTestOptions>} defaultColumns - An array of default custom columns that are shown in browse
	 * @param {Array<ColumnTestOptions>} columnsToSelect - An array of custom columns to check based on the custom column interface. If empty, this tests default columns only.
	 * @param {number} [timeout] The time to wait
	 * @returns A promise that resolves when the test is completed
	 * @tutorial tutorial-browse-column-tests
	 * @throws {AssertionError} if the columns shown in the browse grid do not match the union of defaultColumns and columnsToSelect
	 */
	export function canSelectResourceColumns(resourceProvider: string, resourceType: string, browseBladeTitle: string, defaultColumns: ColumnTestOptions[], columnsToSelect?: ColumnTestOptions[], timeout?: number): Q.Promise<void>;
	/**
	 * Tests columns in browse.
	 *
	 * @param {string} resourceProvider The resource provider.
	 * @param {string} resourceType The resource type.
	 * @param {string} browseBladeTitle - The title of the browse blade for the resource.
	 * @param {Array<ColumnTestOptions>} defaultColumns - An array of default custom columns that are shown in browse
	 * @param {number} [timeout] The time to wait
	 * @tutorial tutorial-browse-column-tests
	 */
	export function containsExpectedDefaultColumns(resourceProvider: string, resourceType: string, browseBladeTitle: string, defaultColumns: ColumnTestOptions[], timeout?: number): any;
	/**
	 * Options used to specify expectations on columns in browse tests
	 */
	export interface ColumnTestOptions {
	    columnLabel: string;
	}

}
declare module 'MsPortalFx-Test/Tests' {
	export import Parts = require('MsPortalFx-Test/Tests/Parts');
	export import Browse = require('MsPortalFx-Test/Tests/Browse');

}
declare module 'MsPortalFx-Test/Notifications/NotificationsMenu' {
	import Q = require("q");
	import testFx = require('MsPortalFx-Test/Index');
	import Locator = require('MsPortalFx-Test/Locators/Locator');
	import PortalElement = require('MsPortalFx-Test/PortalElement');
	import Notification = require('MsPortalFx-Test/Notifications/Notification'); class NotificationsMenu extends PortalElement {
	    /**
	     * The list of displayed notifications.
	     */
	    notifications: testFx.ElementArrayFinder<testFx.Notifications.Notification>;
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	    /**
	     * Checks if any notifications are open and if so, clicks the notification button to try to close them.
	     */
	    closeNotificationIfOpen(): Q.Promise<void>;
	    /**
	     * Waits for a new notification to show up given the specified parameters. An error is thrown if the notification
	     * does not show up after the timeout is reached.
	     * @param {string} title The notification title.
	     * @param {string} description The notification description.
	     * @param {number} timeout The time to wait, in milliseconds, for the notification to show up.
	     * @returns The found Notification.
	     */
	    waitForNewNotification(title?: string, description?: string, timeout?: number): Q.Promise<Notification>;
	    private tryGetText;
	}
	export = NotificationsMenu;

}
declare module 'MsPortalFx-Test/Notifications' {
	export import Notification = require('MsPortalFx-Test/Notifications/Notification');
	export import NotificationsMenu = require('MsPortalFx-Test/Notifications/NotificationsMenu');
	export import NotificationsPane = require('MsPortalFx-Test/Notifications/NotificationsPane');

}
declare module 'MsPortalFx-Test/LogLevel' {
	 enum LogLevel {
	    All = 0,
	    Debug = 1,
	    Info = 2,
	    Warning = 3,
	    Severe = 4,
	    Off = 5
	}
	export = LogLevel;

}
declare module 'MsPortalFx-Test/SplashScreen' {
	import Q = require("q");
	import Locator = require('MsPortalFx-Test/Locators/Locator');
	import PortalElement = require('MsPortalFx-Test/PortalElement');
	import Constants = require('MsPortalFx-Test/Constants'); class SplashScreen extends PortalElement {
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	    /**
	     * Clicks the OK button in the Untrusted Extensions/Patch dialog.
	     * We need to first identify the dialog by its title
	     * @returns A promise that resolves when the OK button has been clicked.
	     */
	    private _clickDialogAllowButton;
	    private _getDialogTitle;
	    clickUntrustedDialogOkButton(dialogType: Constants.SplashScreenDialogType): Q.Promise<void>;
	}
	export = SplashScreen;

}
declare module 'MsPortalFx-Test/PortalFxQueryStringNames' {
	export let clientSessionIdName: string;

}
declare module 'MsPortalFx-Test/Search/SearchResult' {
	import Locator = require('MsPortalFx-Test/Locators/Locator');
	import PortalElement = require('MsPortalFx-Test/PortalElement'); class SearchResult extends PortalElement {
	    /**
	     * The search result name.
	     */
	    title: PortalElement;
	    /**
	     * The search result type.
	     */
	    resultType: PortalElement;
	    /**
	     * The search result subname.
	     */
	    subName: PortalElement;
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	}
	export = SearchResult;

}
declare module 'MsPortalFx-Test/Search/SearchMenu' {
	import Q = require("q");
	import testFx = require('MsPortalFx-Test/Index');
	import Locator = require('MsPortalFx-Test/Locators/Locator');
	import PortalElement = require('MsPortalFx-Test/PortalElement');
	import SearchResult = require('MsPortalFx-Test/Search/SearchResult'); class SearchMenu extends PortalElement {
	    private searchTextBox;
	    private dropMenu;
	    private mruItemsMenu;
	    private searchResultMenu;
	    /**
	     * The list of displayed MRU items.
	     */
	    mruResults: testFx.ElementArrayFinder<SearchResult>;
	    /**
	     * The list of displayed search results.
	     */
	    searchResults: testFx.ElementArrayFinder<SearchResult>;
	    /**
	     * Gets the locator associated to this element.
	     * @returns A Locator instance.
	     */
	    readonly locator: Locator;
	    /**
	     * Searches for the given string.
	     *
	     * @param {string} text The text to search for.
	     * @returns The list of search results.
	     */
	    search(text?: string): Q.Promise<SearchResult[]>;
	}
	export = SearchMenu;

}
declare module 'MsPortalFx-Test/Portal' {
	import webdriver = require("selenium-webdriver");
	import Locator = require('MsPortalFx-Test/Locators/Locator');
	import Q = require("q");
	import PortalElement = require('MsPortalFx-Test/PortalElement');
	import BrowseResourceBlade = require('MsPortalFx-Test/Blades/BrowseResourceBlade');
	import Blade = require('MsPortalFx-Test/Blades/Blade');
	import PortalContext = require('MsPortalFx-Test/PortalContext');
	import CreateBlade = require('MsPortalFx-Test/Blades/CreateBlade');
	import LogLevel = require('MsPortalFx-Test/LogLevel');
	import ElementArrayFinder = require('MsPortalFx-Test/ElementArrayFinder');
	import SearchResult = require('MsPortalFx-Test/Search/SearchResult');
	import MenuBlade = require('MsPortalFx-Test/Blades/MenuBlade'); class Portal {
	    /**
	     * The associated PortalContext object used to configure the Portal instance.
	     */
	    portalContext: PortalContext;
	    private canModifyExtensionsFeature;
	    private driver;
	    private wdservice;
	    private redirectDivId;
	    private rootClassName;
	    private corpEmailTextBoxCssSelector;
	    private corpPasswordTextBoxCssSelector;
	    private corpSignInUrl;
	    private corpSubmitButtonCssSelector;
	    private liveIdEmailTextBoxCssSelector;
	    private liveIdPasswordTextBoxCssSelector;
	    private liveIdSubmitButtonCssSelector;
	    private orgIdEmailTextBoxCssSelector;
	    private orgIdPasswordTextBoxCssSelector;
	    private orgIdSubmitButtonCssSelector;
	    private keepMeSignedInPageId;
	    private keepMeSignedInDeclineCssSelector;
	    private workAccountTileId;
	    /**
	     * Creates a new instance of this class.
	     */
	    constructor();
	    /**
	     * Navigates to the home url.
	     * @param {number} timeout The time to wait, in milliseconds, for pages to load.
	     */
	    goHome(timeout?: number): Q.Promise<void>;
	    /**
	     * Opens the gallery create blade for the specified gallery package. This function is only supported for
	     * gallery packages deployed to the gallery service. When the package only exists locally use function openGalleryCreateBladeFromLocalPackage.
	     * @param {string} galleryPackageName The name of the gallery package.
	     * @param {string} bladeTitle The title of the blade that is expected to open.
	     * @param {number} timeout The time to wait, in milliseconds, for the blade to show up.
	     * @param {boolean} [waitForBladeLoadedOnly] if true waits for the blade only to load, but not all of its tiles, before resolving
	     * @returns A promise that resolves with the gallery create blade.
	     */
	    openGalleryCreateBlade(galleryPackageName: string, bladeTitle: string, timeout?: number, waitForBladeLoadedOnly?: boolean): Q.Promise<CreateBlade>;
	    /**
	     * Opens the create blade from gallery package that is sideloaded from localhost.
	     * @param {string} galleryItemTitle The title of the gallery item in marketplace
	     * @param {string} bladeTitle The title of the extensions create blade that is expected to open after clicking on the gallery item.
	     * @param {number} [timeout] Optional time to wait, in milliseconds, for the blade to show up.
	     * @param {boolean} [waitForBladeLoadedOnly] If true waits for the blade only to load, but not all of its tiles, before resolving
	     * @returns A promise that resolves with the gallery CreateBlade.
	     */
	    openGalleryCreateBladeFromLocalPackage(galleryItemTitle: string, bladeTitle: string, timeout?: number, waitForBladeLoadedOnly?: boolean): Q.Promise<CreateBlade>;
	    /**
	     * Opens the browse blade for the given resource provider and resource type.
	     * @param {string} resourceProvider The resource provider.
	     * @param {string} resourceType The resource type.
	     * @param {string} bladeTitle The title of the blade that is expected to open.
	     * @param {number} [timeout] The time to wait, in milliseconds, for the blade to show up.
	     * @param {boolean} [waitForBladeLoadedOnly] if true waits for the blade only to load, but not all of its tiles, before resolving
	     * @returns A promise that resolves with the browse blade.
	     */
	    openBrowseBlade(resourceProvider: string, resourceType: string, bladeTitle: string, timeout?: number, waitForBladeLoadedOnly?: boolean): Q.Promise<BrowseResourceBlade>;
	    /**
	     * Opens the resource/summary blade associated to the specified resource id.
	     * @param {string} resourceId The resource id.
	     * @param {string} bladeTitle The title of the blade that is expected to open.
	     * @param {number} timeout The time to wait, in milliseconds, for the blade to show up.
	     * @param {boolean} [waitForBladeLoadedOnly] if true waits for the blade only to load, but not all of its tiles, before resolving
	     * @returns A promise that resolves with the resource blade.
	     */
	    openResourceBlade(resourceId: string, bladeTitle: string, timeout?: number, waitForBladeLoadedOnly?: boolean): Q.Promise<MenuBlade>;
	    /**
	     * Searches for the specified text in the portal top bar and returns search results.
	     * @param text The text to search for.
	     * @returns A promise that resolves when there are search results.
	     */
	    search(text: string): Q.Promise<SearchResult[]>;
	    /**
	     * Starts a move operation using the specified options.
	     * @param options The options used to populate the blade.
	     * @returns A promise that resolves when the move operation has started.
	     */
	    startMoveResource(options: {
	        resourceId: string;
	        targetResourceGroup: string;
	        createNewGroup?: boolean;
	        subscriptionName?: string;
	        relatedResources?: string[];
	        timeout?: number;
	        listOptionText?: string;
	    }): Q.Promise<void>;
	    /**
	     * Resets the current dashboard to its default state.
	     */
	    resetDashboard(): Q.Promise<void>;
	    /**
	     * **************** OBSOLETE ******************
	     * ***** Will be removed after 8/31/2017 ******
	     * Resets the current dashboard to its default state.
	     */
	    restoreDefaultLayout(): Q.Promise<void>;
	    /**
	     * Navigates to the specified uri fragment.
	     * @param {string} fragment The uri fragment to navigate to.
	     * @param {number} timeout The time to wait for pages to load.
	     * @throws Will throw an error if the specified fragment is a falsy value.
	     */
	    navigateToUriFragment(fragment: string, timeout?: number): Q.Promise<any>;
	    /**
	     * Gets a Blade object that represents the blade that matches the specified options.
	     * @param options The options used to find the blade.
	     * @param bladeType The type of blade to return.
	     * @returns A Blade object that represents the blade that matches the specified options.
	     */
	    blade<T extends Blade>(options: {
	        title?: string;
	        bladeType?: {
	            new (): T;
	        };
	    }): T;
	    /**
	     * Gets a PortalElement that matches the specified sub locator or element type.
	     * @param subLocatorOrElementType The sub locator or element type.
	     * @returns A PortalElement that matches the specified sub locator or element type.
	     */
	    element<T extends PortalElement>(subLocatorOrElementType: Locator | {
	        new (): T;
	    }): T;
	    /**
	     * Gets an ElementArrayFinder that matches the specified sub locator or element type.
	     * @param subLocatorOrElementType The sub locator or element type.
	     * @returns An ElementArrayFinder that matches the specified sub locator or element type.
	     */
	    all<T extends PortalElement>(subLocatorOrElementType: Locator | {
	        new (): T;
	    }): ElementArrayFinder<T>;
	    /**
	     * Schedules a command to wait for a condition to hold. The condition may be
	     * specified by a promise or a custom function.
	     *
	     * For a function, the wait will repeatedly evaluate the condition until it returns a truthy value. If any errors occur
	     * while evaluating the condition, they will be allowed to propagate. In the event a condition returns a promise, the
	     * polling loop will wait for it to be resolved and use the resolved value for whether the condition has been satisified.
	     * @param condition The condition to wait on, defined as a promise or a function to evaluate as a condition.
	     * @param {number} timeout How long to wait for the condition to be true.
	     * @param {string} opt_message An optional message to use if the wait times out.
	     * @returns  A promise that will be fulfilled with the first truthy value returned by the condition function, or
	     *     rejected if the condition times out.
	     */
	    wait<T>(condition: Q.Promise<T> | Function, timeout?: number, opt_message?: string): Q.Promise<T>;
	    /**
	     * Schedule a command to take a screenshot. The driver makes a best effort to return a screenshot of the following, in order of preference:
	     * <ol>
	     *   <li>Entire page
	     *   <li>Current window
	     *   <li>Visible portion of the current frame
	     *   <li>The screenshot of the entire display containing the browser
	     * </ol>
	     * @param {string} filePrefix The prefix used for the generated screenshot file.
	     * @returns A promise that will be resolved to the path of the screenshot file.
	     * @tutorial tutorial-screenshot
	     */
	    takeScreenshot(filePrefix?: string): Q.Promise<string>;
	    /**
	     * Accepts an alert if one is present
	     * @returns a promise when the alert has been accepted
	     */
	    acceptAlert(): Q.Promise<void>;
	    /**
	     * Gets the text from an alert if one is present
	     * @returns a promise that will be resolved with the text of the alert.
	     */
	    getAlertText(): Q.Promise<string>;
	    /**
	     * Gets the browser logs for the specified level.
	     * @param {LogLevel} level The level of logs to get.
	     * @returns A promise that resolves to the array of logs.
	     */
	    getBrowserLogs(level: LogLevel): Q.Promise<string[]>;
	    /**
	     * Schedules a command to execute JavaScript in the context of the currently
	     * selected frame or window. The script fragment will be executed as the body
	     * of an anonymous function. If the script is provided as a function object,
	     * that function will be converted to a string for injection into the target
	     * window.
	     * @param {string} script The script to execute.
	     * @param {...*} var_args The arguments to pass to the script.
	     * @returns A promise that will resolve to the scripts return value.
	     */
	    executeScript<T>(script: string, ...var_args: any[]): Q.Promise<T>;
	    /**
	     * Schedules a command to execute asynchronous JavaScript in the context of the
	     * currently selected frame or window. The script fragment will be executed as
	     * the body of an anonymous function. If the script is provided as a function
	     * object, that function will be converted to a string for injection into the
	     * target window.
	     *
	     * Any arguments provided in addition to the script will be included as script
	     * arguments and may be referenced using the {@code arguments} object.
	     * Arguments may be a boolean, number, string, or {@code webdriver.WebElement}.
	     * Arrays and objects may also be used as script arguments as long as each item
	     * adheres to the types previously mentioned.
	     *
	     * Unlike executing synchronous JavaScript with {@link #executeScript},
	     * scripts executed with this function must explicitly signal they are finished
	     * by invoking the provided callback. This callback will always be injected
	     * into the executed function as the last argument, and thus may be referenced
	     * with {@code arguments[arguments.length - 1]}. The following steps will be
	     * taken for resolving this functions return value against the first argument
	     * to the script's callback function:
	     *
	     * - For a HTML element, the value will resolve to a
	     *     {@link webdriver.WebElement}
	     * - Null and undefined return values will resolve to null
	     * - Booleans, numbers, and strings will resolve as is
	     * - Functions will resolve to their string representation
	     * - For arrays and objects, each member item will be converted according to
	     *     the rules above
	     *
	     * __Example #1:__ Performing a sleep that is synchronized with the currently
	     * selected window:
	     *
	     *     var start = new Date().getTime();
	     *     driver.executeAsyncScript(
	     *         'window.setTimeout(arguments[arguments.length - 1], 500);').
	     *         then(function() {
	     *           console.log(
	     *               'Elapsed time: ' + (new Date().getTime() - start) + ' ms');
	     *         });
	     *
	     * __Example #2:__ Synchronizing a test with an AJAX application:
	     *
	     *     var button = driver.findElement(By.id('compose-button'));
	     *     button.click();
	     *     driver.executeAsyncScript(
	     *         'var callback = arguments[arguments.length - 1];' +
	     *         'mailClient.getComposeWindowWidget().onload(callback);');
	     *     driver.switchTo().frame('composeWidget');
	     *     driver.findElement(By.id('to')).sendKeys('<EMAIL>');
	     *
	     * __Example #3:__ Injecting a XMLHttpRequest and waiting for the result. In
	     * this example, the inject script is specified with a function literal. When
	     * using this format, the function is converted to a string for injection, so it
	     * should not reference any symbols not defined in the scope of the page under
	     * test.
	     *
	     *     driver.executeAsyncScript(function() {
	     *       var callback = arguments[arguments.length - 1];
	     *       var xhr = new XMLHttpRequest();
	     *       xhr.open("GET", "/resource/data.json", true);
	     *       xhr.onreadystatechange = function() {
	     *         if (xhr.readyState == 4) {
	     *           callback(xhr.responseText);
	     *         }
	     *       }
	     *       xhr.send('');
	     *     }).then(function(str) {
	     *       console.log(JSON.parse(str)['food']);
	     *     });
	     *
	     * @param {!(string|Function)} script The script to execute.
	     * @param {...*} var_args The arguments to pass to the script.
	     * @return {!webdriver.promise.Promise.<T>} A promise that will resolve to the
	     *    scripts return value.
	     * @template T
	     */
	    executeAsyncScript<T>(script: string, ...var_args: any[]): Q.Promise<T>;
	    /**
	     * Schedules a command to retrieve the URL of the current page.
	     * @returns A promise that will be resolved with the current URL.
	     */
	    getCurrentUrl(): Q.Promise<string>;
	    /**
	     * Schedules a command to close the window (tab) at the specified index.
	     * @param {number} index Index of the window (tab) to close.
	     * @returns A promise that resolves when the window (tab) has been closed.
	     */
	    closeWindow(index: number): Q.Promise<void>;
	    /**
	     * Schedules a command to quit the current session. After calling quit, this
	     * instance will be invalidated and may no longer be used to issue commands
	     * against the browser.
	     * @returns A promise that will be resolved when the command has completed.
	     */
	    quit(): Q.Promise<any>;
	    /**
	     * Gets the internal WebDriver instance used by this Portal object.
	     * @returns The WebDriver instance.
	     */
	    getDriver(): webdriver.WebDriver;
	    /**
	     * string.format implementation
	     * @param format String to be formatted
	     * @param args Values to use
	     * @returns Formatted string
	     */
	    private stringFormat;
	    private hasValidTestExtensionKeys;
	    private useQueryStringTestExtension;
	    private buildHomeUrl;
	    private navigateTo;
	    private getRegisteredExtensions;
	    private registerNonLocalExtensions;
	    private performSignIn;
	    private waitForSplashGone;
	    private isLocalExtension;
	    private acceptPopupDialog;
	    private acceptTrustedExtensionsDialog;
	    private acceptTrustedPatchDialog;
	    /**
	     * Returns a promise that resolves after the blade loading.
	     * @param {Blade} blade The blade object to open.
	     * @param {boolean} waitForBladeLoadedOnly if true waits for the blade only to load, but not all of its tiles, before resolving.
	     * @param {number} timeout The time to wait, in milliseconds, for the blade to show up.
	     * @returns A promise that resolves with the loaded blade.
	     */
	    getBladeLoadPromise(blade: Blade, waitForBladeLoadedOnly?: boolean, timeout?: number): Q.Promise<Blade>;
	}
	export = Portal;

}
declare module 'MsPortalFx-Test/Index' {
	import webdriver = require("selenium-webdriver");
	export import ActionBars = require('MsPortalFx-Test/ActionBars');
	export import Blades = require('MsPortalFx-Test/Blades');
	/**
	 * constants such as css class names and enums that can be used when writing tests
	 */
	export import Constants = require('MsPortalFx-Test/Constants');
	export import Controls = require('MsPortalFx-Test/Controls');
	export import Locators = require('MsPortalFx-Test/Locators');
	export import Parts = require('MsPortalFx-Test/Parts');
	export import Commands = require('MsPortalFx-Test/Commands');
	export import Utils = require('MsPortalFx-Test/Utils');
	export import Tests = require('MsPortalFx-Test/Tests');
	export import Notifications = require('MsPortalFx-Test/Notifications');
	export import Feature = require('MsPortalFx-Test/Feature');
	export import LogLevel = require('MsPortalFx-Test/LogLevel');
	export import Portal = require('MsPortalFx-Test/Portal');
	export import PortalContext = require('MsPortalFx-Test/PortalContext');
	export import PortalElement = require('MsPortalFx-Test/PortalElement');
	export import SplashScreen = require('MsPortalFx-Test/SplashScreen');
	export import TextExtension = require('MsPortalFx-Test/TestExtension');
	export import ElementArrayFinder = require('MsPortalFx-Test/ElementArrayFinder');
	export import until = require('MsPortalFx-Test/ExpectedConditions');
	export import StartBoard = require('MsPortalFx-Test/StartBoard');
	/**
	 * Representations of pressable keys that aren't text.  These are stored in
	 * the Unicode PUA (Private Use Area) code points, 0xE000-0xF8FF.  Refer to
	 * http://www.google.com.au/search?&q=unicode+pua&btnG=Search
	 */
	export let Key: webdriver.IKey;
	/**
	 * The global Portal object.
	 */
	export let portal: Portal;

}
declare module 'MsPortalFx-Test' {
	import main = require('MsPortalFx-Test/Index');
	export = main;
}
