Param(
   $UserEmail,
   $UserPassword,
   $TestEnvironment
)

$PSScriptRoot = Split-Path -Parent -Path $MyInvocation.MyCommand.Definition
echo ("Script location: {0}" -f $PSScriptRoot)

$env:USER_EMAIL = if ($UserEmail) {$UserEmail} else { (Get-AzureKeyVaultSecret -VaultName 'MLServiceWorkspacesTests' -Name 'UserEmail').SecretValueText }
$env:USER_PASSWORD = if ($UserPassword) {$UserPassword} else { (Get-AzureKeyVaultSecret -VaultName 'MLServiceWorkspacesTests' -Name 'UserPassword').SecretValueText }
$env:TEST_ENVIRONMENT = if($TestEnvironment) {$TestEnvironment} else { 'mpac'}

echo ("Testing environment: {0}" -f $env:TEST_ENVIRONMENT)

# Install packages and run tsc and mocha from there
cd $PSScriptRoot
npm install --no-optional
.\node_modules\.bin\tsc .\MachineLearningServiceWorkspacesTests.ts --module commonjs --lib es2015
.\node_modules\.bin\mocha MachineLearningServiceWorkspacesTests.js