import testFx = require('MsPortalFx-Test');
import Constants = require("./Constants");
import MenuBlade = testFx.Blades.MenuBlade;

class Navigation {

    public static goToCreateMachineLearningServicesBlade(): Q.Promise<testFx.Blades.Blade> {
        return testFx.portal.openGalleryCreateBlade(Constants.mlServiceResourceProvider, Constants.createMlServicesBladeName);
    }

    public static goToMachineLearningServicesBlade(machineLearningService: string): Q.Promise<MenuBlade> {
        const machineLearningServicesNameToNavigate = machineLearningService;
        const menuBlade = testFx.portal.blade({ bladeType: MenuBlade });
        return testFx.portal.openBrowseBlade(Constants.mlServiceResourceProvider, Constants.mlServicesResourceType, Constants.mlServicesBrowseBladeName, Constants.PortalTimeoutPeriods.Large).then((blade) => {
            return blade.filterItems(machineLearningServicesNameToNavigate);
        }).then((blade) => {
            return testFx.portal.wait<testFx.Controls.GridRow>(() => {
                return blade.grid.rows.count().then((count) => {
                    return count === 1 ? blade.grid.rows.first() : null;
                });
            }, null, "Expected only one row matching " + machineLearningServicesNameToNavigate);
        }).then((row) => {
            return row.click();
        }).then(() => {
            return menuBlade;
        });
    }
}

export = Navigation;