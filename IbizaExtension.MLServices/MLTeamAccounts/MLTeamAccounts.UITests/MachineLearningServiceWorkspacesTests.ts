/// <reference path="./typings/index.d.ts" />
 
import assert = require('assert');
import testFx = require('MsPortalFx-Test');
import until = testFx.until;

import NotificationsPane = testFx.Notifications.NotificationsPane;
import By = testFx.Locators.By;

import Constants = require("./Constants");
import Navigation = require("./Navigation");
import Configuration = require("./Configuration");
import Utilities = require("./Utilities");
import CreateMachineLearningServicesBlade = require("./Blades/CreateMachineLearningServicesBlade");
 
describe('Machine Learning Service Workspaces Tests', function () {
    this.timeout(Constants.MochaTestTimeout);
    this.retries(Constants.TestRetryCount);
    let mlServiceToDelete = null;
    
    before(() => {
        Configuration.initialize();
   
        testFx.portal.portalContext.portalUrl = Configuration.portalUrl;
        if (Configuration.extensionUrl) {        
            testFx.portal.portalContext.features = [{ name: "feature.canmodifyextensions", value: "true" }];
            testFx.portal.portalContext.testExtensions = [
                {
                    name: "Microsoft_Azure_MLTeamAccounts",
                    uri: Configuration.extensionUrl
                }
            ];
        }

        testFx.Utils.Timeouts.defaultTimeout = Constants.PortalTimeoutPeriods.ExtraLarge;
        testFx.portal.portalContext.signInEmail = Configuration.signInEmail;
        testFx.portal.portalContext.signInPassword = Configuration.signInPassword;
        
        assert.ok(testFx.portal.portalContext.signInEmail, "Sign in email not configured properly");
        assert.ok(testFx.portal.portalContext.signInPassword, "Sign in password not configured properly");
        assert.ok(Configuration.mlServiceName, "Machine Learning Service name has not been configured");
        assert.ok(Configuration.mlServicesTestResourceGroup, "Machine Learning Service resource group has not been configured");
        assert.ok(Configuration.portalUrl, "Portal URL has not been configured");
    })

    it("Can view Machine Learning Services overview blade", () => {
        const machineLearningServicesBlade = testFx.portal.blade({ title: Configuration.mlServiceName });
        return Navigation.goToMachineLearningServicesBlade(Configuration.mlServiceName)
            .then((menuBlade) => menuBlade.openMenuItem("Overview"))
            .then(() => testFx.portal.wait(until.isPresent(machineLearningServicesBlade)))
            .then(() => machineLearningServicesBlade.waitUntilBladeAndAllTilesLoaded());
    });

    it("Can view Machine Learning Services properties blade", () => {
        const propertiesBlade = testFx.portal.blade({ title: Utilities.getMlServicesBladeName("Properties") });
        return Navigation.goToMachineLearningServicesBlade(Configuration.mlServiceName)
            .then((menuBlade) => menuBlade.openMenuItem("Properties"))
            .then(() => propertiesBlade.waitUntilBladeAndAllTilesLoaded());
    });

    it("Can create Machine Learning Service with existing resource group", () => {
        const mlServicesName = Utilities.generateWorkspaceName();
        const resourceGroupName = Configuration.mlServicesTestResourceGroup;
        const createMlServicesBlade = testFx.portal.blade({ title: Constants.createMlServicesBladeName, bladeType: CreateMachineLearningServicesBlade });
        const location = Configuration.defaultLocation;

        console.log(`Filling fields workspaceName: '${mlServicesName}' resourceGroupName: '${resourceGroupName}' and location: '${location}'`);
        
        return Navigation.goToCreateMachineLearningServicesBlade()
            .then((blade) => {
                return createMlServicesBlade.fillFields(
                    mlServicesName,
                    resourceGroupName,
                    false,
                    location);
            })
            .then(() => {
                const createButton = createMlServicesBlade.element(By.css(`.fxc-simplebutton[data-bind="pcControl: createButton"]`));
                createButton.click();
            })
            .then(() => {
                const createButton = createMlServicesBlade.element(By.css(`.fxc-simplebutton[data-bind="pcControl: createButton"]`));
                createButton.click();
            })
            .then(() => {
                mlServiceToDelete = mlServicesName;
                const statusText = testFx.portal.element(By.css("[data-bind='text: statusText']"));
                return testFx.portal.wait(() => {
                    return statusText.getText().then((text) => {
                        return (text === 'Your deployment is complete');
                    })
                });
            });
    });

    it("Can delete newly created workspace", () => {
        const notificationsPane = testFx.portal.element(NotificationsPane);

        if (!mlServiceToDelete) {
            assert.fail(null, null, "There are no services to delete (i.e. previous create test failing)");
        }

        return Navigation.goToMachineLearningServicesBlade(mlServiceToDelete)
            .then((menuBlade) => menuBlade.openMenuItem("Overview"))
            .then((overviewBlade) => overviewBlade.waitUntilBladeAndAllTilesLoaded())
            .then((blade) => blade.clickCommand("Delete"))
            .then((blade) => {
                const deleteMlServiceDialog = blade.dialog({ title: "Delete Workspace" });
                return deleteMlServiceDialog.clickButton("Yes");
            })
            .then(() => notificationsPane.waitForNewNotification('Workspace deleted', null, Constants.PortalTimeoutPeriods.Normal))
            .then(() => notificationsPane.close())
    });

    afterEach(function () {
        /* tslint:disable:no-invalid-this */
        if (this.currentTest.state === "failed") {
            return testFx.portal.takeScreenshot(this.currentTest.title).then(() => {
                return testFx.portal.goHome();
            });
        } else {
            return testFx.portal.goHome();
        }
    });

    after(() => {
        return testFx.portal.quit();
    });

});
