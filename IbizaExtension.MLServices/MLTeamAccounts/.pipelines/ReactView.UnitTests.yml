pool:
  vmImage: ubuntu-latest

trigger:
 branches:
   include:
     - master
 paths:
   include:
     - IbizaExtension.MLServices/MLTeamAccounts/MLTeamAccounts/Client/ReactViews/*
     - IbizaExtension.MLServices/MLTeamAccounts/MLTeamAccounts/Client/Shared/ViewAgnostic/*

variables:
  mlteamaccounts_dir: $(system.defaultworkingdirectory)/IbizaExtension.MLServices/MLTeamAccounts/MLTeamAccounts
  reactviews_dir: $(mlteamaccounts_dir)/Client/ReactViews
  npm_config_userconfig: $(mlteamaccounts_dir)/.npmrc

steps:
- task: UseNode@1
  displayName: 'Use Node v18.17.0'
  inputs:
    version: 18.17.0

- task: npmAuthenticate@0
  displayName: 'Authenticate root npmrc'
  inputs:
    workingFile: $(npm_config_userconfig)

- script: 'npm ci --loglevel=error --no-optional'
  workingDirectory: $(mlteamaccounts_dir)
  failOnStderr: true
  displayName: 'Install npm packages for Extension'

- script: 'npm ci --loglevel=error --no-optional'
  workingDirectory: $(reactviews_dir)
  failOnStderr: true
  displayName: 'Install npm packages for ReactViews'

- script: 'npm run test'
  workingDirectory: $(reactviews_dir)
  displayName: 'reactview unit tests'
