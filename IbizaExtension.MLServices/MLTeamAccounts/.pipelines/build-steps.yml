#
# Common build steps definition for all OneBranch.*.yml file.
#

parameters:
  - name: enableSigning
    type: boolean
  - name: enableLocalizationPR
    type: boolean
    default: true

steps:
- task: UseNode@1
  displayName: Use Node v18.20.4
  inputs:
    version: 18.20.4
- task: npmAuthenticate@0
  displayName: Authenticate root npmrc
  inputs:
    workingFile: '$(project_directory)\.npmrc'
- task: CmdLine@2
  displayName: set npm config env
  inputs:
    script: 'echo ##vso[task.setvariable variable=NPM_CONFIG_USERCONFIG;issecret=false]$(project_directory)\.npmrc'
- task: OneLocBuild@2
  displayName: 'Localization Build: $(solution_directory)\Localize\LocProject.json'
  inputs:
    locProj: '$(solution_directory)\Localize\LocProject.json'
    isCreatePrSelected: ${{ parameters.enableLocalizationPR }}
    isAutoCompletePrSelected: false
    isShouldReusePrSelected: true
    dependencyPackageSource: https://msdata.pkgs.visualstudio.com/Vienna/_packaging/LocTools/nuget/v3/index.json
    packageSourceAuth: patAuth
  env:
    SYSTEM_ACCESSTOKEN: $(System.AccessToken)
- script: npm install -g @microsoft/azureportalcli@16.108.0
  displayName: 'Install Azure Portal CLI'
  workingDirectory: $(project_directory)
- script: ap restore
  displayName: '📦 Install nuget and npm packages'
  workingDirectory: $(project_directory)
- script: ap release /p:RunBundlerInDevMode=false;Configuration=Release;HostingServiceCreateDeploymentArtifacts=true
  displayName: '🛠️ Create release'
  workingDirectory: $(project_directory)
- task: CopyFiles@2
  displayName: 'Copy bin files to: $(Build.StagingDirectory)\out\bin'
  inputs:
    SourceFolder: $(project_directory)\bin
    Contents: '**\**'
    TargetFolder: $(Build.StagingDirectory)\out\bin
- task: CopyFiles@2
  displayName: 'Copy Output files to: $(Build.StagingDirectory)\out\bin'
  inputs:
    SourceFolder: $(project_directory)\Output
    Contents: '**\**'
    TargetFolder: $(Build.StagingDirectory)\out\bin
- ${{ if eq(parameters.enableSigning, 'true') }}:
  - task: onebranch.pipeline.signing@1
    displayName: '✏️ Sign files'
    inputs:
      command: 'sign'
      signing_profile: 'internal_azure_service'
      files_to_sign: '**/*.js;**/*.dll'
      search_root: '$(Build.StagingDirectory)\out'
