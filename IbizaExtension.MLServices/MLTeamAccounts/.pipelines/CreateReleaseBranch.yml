trigger: none

variables:
  - name: RepoUrl
    value: https://anyuser:$(System.AccessToken)@msdata.visualstudio.com/Vienna/_git/vienna
  - name: ReleaseBranch
    value: ibiza/releases/

schedules:
  - cron: "0 14 * * 2"
    displayName: Once Weekly release
    branches:
      include:
        - master
steps:
  - checkout: self
    submodules: false # Avoid bringing in AzureMlCli submodule because it's full of credscan violations
    fetchDepth: 1
    fetchTags: false
  - task: PowerShell@1
    displayName: "Run Script"
    inputs:
      scriptType: inlineScript
      inlineScript: |
        $supress = & IbizaExtension.MLServices/MLTeamAccounts/scripts/deployment/createReleaseBranch.ps1 -repoUrl "$(RepoUrl)" -releaseBranch "$(ReleaseBranch)" 2>&1
        Write-Host $supress
pool:
  vmImage: "windows-latest"
