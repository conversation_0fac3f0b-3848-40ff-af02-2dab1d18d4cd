#################################################################################
#                           Onebranch Official Pipeline                         #
# This pipeline was created by EasyStart from a sample located at:              #
#   https://aka.ms/obpipelines/easystart/samples                                #
# Documentation:  https://aka.ms/obpipelines                                    #
# Yaml Schema:    https://aka.ms/obpipelines/yaml/schema                        #
# Retail Tasks:   https://aka.ms/obpipelines/tasks                              #
# Support:        https://aka.ms/onebranchsup                                   #
#################################################################################

trigger:
  branches:
    include:
      - refs/heads/master
  paths:
    include:
      - IbizaExtension.MLServices
  batch: true

variables:
  # common variables
  - template: common-variables.yml

  - name: BuildTemplate
    value: official

resources:
  repositories:
    - repository: templates
      type: git
      name: OneBranch.Pipelines/GovernedTemplates
      ref: refs/heads/main

extends:
  template: v2/OneBranch.Official.CrossPlat.yml@templates
  parameters:
    featureFlags:
      debugConsole: false
    globalSdl:
      tsa:
        enabled: false
        configFile: $(solution_directory)\.configs\tsaoptions.json
      credscan:
        suppressionsFile: $(solution_directory)\.configs\CredScanSuppressions.json
      policheck:
        break: true
        exclusionsFile: $(solution_directory)\.configs\PolicheckExclusion.xml
      cg:
        failOnAlert: false
        ignoreDirectories: src/azureml-api/src/Common,.vscode,build,AzureMlCli,licenses,private,pull_request_template,qconfig,scripts,tools,src/.vscode,src/aether,src/aml,src/azureml-datacache,src/azureml-job-runtime,src/azureml-loadtest,src/azureml-loadtest,src/azureml-synapse,src/docs,src/geneva_synthetics,src/IntegrationTests,src/Product,tools,src/azureml-api/src/.devops,src/azureml-api/src/AccountRP,src/azureml-api/src/Artifact,src/azureml-api/src/AssetCatalog,src/azureml-api/src/AssetData,src/azureml-api/src/Authorization,src/azureml-api/src/AutoML,src/azureml-api/src/AzurePolicyEvaluation,src/azureml-api/src/BatchEndpoint,src/azureml-api/src/Billing,src/azureml-api/src/BillingADF,src/azureml-api/src/BillingProcessor,src/azureml-api/src/CJPBatch,src/azureml-api/src/CJPCore,src/azureml-api/src/CJPSingularity,src/azureml-api/src/CJPVM,src/azureml-api/src/CloudletProxy,src/azureml-api/src/Collie,src/azureml-api/src/CommonHobo,src/azureml-api/src/CommonJobProvider,src/azureml-api/src/CommonProvisioner,src/azureml-api/src/Component,src/azureml-api/src/ComputeJobServer,src/azureml-api/src/Credential,src/azureml-api/src/DataCache,src/azureml-api/src/DataDrift,src/azureml-api/src/Dataset,src/azureml-api/src/DataStore,src/azureml-api/src/Designer,src/azureml-api/src/Discovery,src/azureml-api/src/DistributedRateLimiter,src/azureml-api/src/EmailNotification,src/azureml-api/src/EndpointRateLimiter,src/azureml-api/src/EnvironmentManagement,src/azureml-api/src/Execution,src/azureml-api/src/FeatureStore,src/azureml-api/src/Feed,src/azureml-api/src/GlobalJobDispatcher,src/azureml-api/src/HyperDrive,src/azureml-api/src/IdentityBridge,src/azureml-api/src/Index,src/azureml-api/src/IntellectualPropertyPublisher,src/azureml-api/src/Jasmine,src/azureml-api/src/JasmineAndAutoMLStatusReceiver,src/azureml-api/src/Labeling,src/azureml-api/src/MachineLearningCompute,src/azureml-api/src/ManagedData,src/azureml-api/src/ManagedStorage,src/azureml-api/src/ManagementFrontEnd,src/azureml-api/src/Metric,src/azureml-api/src/MetricAndMetricWorker,src/azureml-api/src/MetricWorker,src/azureml-api/src/mlc,src/MLflow,src/azureml-api/src/MLOps,src/azureml-api/src/ModelEndpointDiscovery,src/azureml-api/src/ModelMonitor,src/azureml-api/src/ModelRegistry,src/azureml-api/src/NetworkManagement,src/azureml-api/src/NotebookInstance,src/azureml-api/src/NotebookInstanceProxy,src/azureml-api/src/Notification,src/azureml-api/src/NotificationAndEmailNotification,src/azureml-api/src/OboToken,src/azureml-api/src/OnBehalfBilling,src/azureml-api/src/Pipeline,src/azureml-api/src/PrivateLinkCheck,src/azureml-api/src/ProjectContent,src/azureml-api/src/PurviewIngestion,src/azureml-api/src/RegistryManagement,src/azureml-api/src/ReinforcementLearning,src/azureml-api/src/Run,src/azureml-api/src/RunHistory,src/azureml-api/src/Scheduler,src/azureml-api/src/SchedulerProxy,src/azureml-api/src/SparkSession,src/azureml-api/src/Synthetics,src/azureml-api/src/Token,src/azureml-api/src/Trainingsessions,src/azureml-api/src/Trigger,src/azureml-api/src/TunDRA,src/azureml-api/src/UserPreference,src/azureml-api/src/UxDashboard,src/azureml-api/src/VirtualCluster,src/azureml-api/src/VulnerabilityManagement
    git:
      submodules: false # Avoid bringing in AzureMlCli submodule because it's full of credscan violations
      fetchDepth: 1
      fetchTags: false
    stages:
      - template: all-stages.yml
        parameters:
          enableSigning: true
          enableLocalizationPR: true
          isRelease: false
