parameters:
  - name: isRelease
    type: boolean
    default: false
  - name: isHotfix
    type: boolean
    default: false
  - name: enableSigning
    type: boolean
  - name: enableLocalizationPR
    type: boolean
    default: true

stages:
- stage: build
  jobs:
    - job: main
      pool:
        type: windows
        vmImage: 'windows-latest'
      variables:
        ob_outputDirectory: '$(Build.StagingDirectory)\out'
        ob_sdl_codeSignValidation_excludes: -|**\*.js;-|**\UnitTests\**;-|**\E2ETests\** # Excluding client side javascript and all test projects
      steps:
        # common steps
        - template: build-steps.yml
          parameters:
            enableSigning: ${{ parameters.enableSigning }}
            enableLocalizationPR: ${{ parameters.enableLocalizationPR }}

- ${{ if eq(parameters.isRelease, true) }}:
  - stage: Prod_Stage1
    displayName: Deploy Stage1
    dependsOn: build
    variables:
      ob_release_environment: Production
    jobs:
      - job: ReleaseJob
        pool:
          type: release
        steps:
          - download: current
          - task: ExpressV2Internal@1
            inputs:
              UseServerMonitorTask: true
              EndpointProviderType: ApprovalService
              ApprovalServiceEnvironment: Production
              ServiceRootLocation: 'LinkedArtifact'
              RolloutSpecType: 'RSPath'
              ServiceRootPath: $(Pipeline.Workspace)/drop_build_main/bin
              RolloutSpecPath: $(Pipeline.Workspace)/drop_build_main/bin/Production/Slots/RolloutSpec.SetSlot.BuildArtifact.stage1.json

  - stage: Prod_Validate_Stage1
    displayName: Validate Stage1
    dependsOn: Prod_Stage1
    variables:
      ob_release_environment: Production
    jobs:
      - job: ManualValidationJob
        timeoutInMinutes: 4320
        pool:
          type: server
        steps:
          - task: ManualValidation@1
            timeoutInMinutes: 1440
            inputs:
              notifyUsers: '[Project Vienna Services]\Data Experiences'

  - stage: Prod_Stage2
    displayName: Deploy Stage2
    dependsOn: Prod_Validate_Stage1
    variables:
      ob_release_environment: Production
    jobs:
      - job: ReleaseJob
        pool:
          type: release
        steps:
          - download: current
          - task: ExpressV2Internal@1
            inputs:
              UseServerMonitorTask: true
              EndpointProviderType: ApprovalService
              ApprovalServiceEnvironment: Production
              ServiceRootLocation: 'LinkedArtifact'
              RolloutSpecType: 'RSPath'
              ServiceRootPath: $(Pipeline.Workspace)/drop_build_main/bin
              RolloutSpecPath: $(Pipeline.Workspace)/drop_build_main/bin/Production/Slots/RolloutSpec.SetSlot.BuildArtifact.stage2.json

  - ${{ if eq(parameters.isHotfix, false) }}:
    - stage: Prod_Validate_Stage2
      displayName: Validate Stage2
      dependsOn: Prod_Stage2
      variables:
        ob_release_environment: Production
      jobs:
        - job: ManualValidationJob
          timeoutInMinutes: 4320
          pool:
            type: server
          steps:
            - task: ManualValidation@1
              timeoutInMinutes: 1440
              inputs:
                notifyUsers: '[Project Vienna Services]\Data Experiences'
                onTimeout: 'resume'

  - stage: Prod_Stage3
    displayName: Deploy Stage3
    ${{ if eq(parameters.isHotfix, false) }}:
      dependsOn: Prod_Validate_Stage2
    ${{ else }}:
      dependsOn: Prod_Stage2
    variables:
      ob_release_environment: Production
    jobs:
      - job: ReleaseJob
        pool:
          type: release
        steps:
          - download: current
          - task: ExpressV2Internal@1
            inputs:
              UseServerMonitorTask: true
              EndpointProviderType: ApprovalService
              ApprovalServiceEnvironment: Production
              ServiceRootLocation: 'LinkedArtifact'
              RolloutSpecType: 'RSPath'
              ServiceRootPath: $(Pipeline.Workspace)/drop_build_main/bin
              RolloutSpecPath: $(Pipeline.Workspace)/drop_build_main/bin/Production/Slots/RolloutSpec.SetSlot.BuildArtifact.stage3.json

  - ${{ if eq(parameters.isHotfix, false) }}:
    - stage: Prod_Validate_Stage3
      displayName: Validate Stage3
      dependsOn: Prod_Stage3
      variables:
        ob_release_environment: Production
      jobs:
        - job: ManualValidationJob
          timeoutInMinutes: 4320
          pool:
            type: server
          steps:
            - task: ManualValidation@1
              timeoutInMinutes: 1440
              inputs:
                notifyUsers: '[Project Vienna Services]\Data Experiences'
                onTimeout: 'resume'

  - stage: Prod_Stage4
    displayName: Deploy Stage4
    ${{ if eq(parameters.isHotfix, false) }}:
      dependsOn: Prod_Validate_Stage3
    ${{ else }}:
      dependsOn: Prod_Stage3
    variables:
      ob_release_environment: Production
    jobs:
      - job: ReleaseJob
        pool:
          type: release
        steps:
          - download: current
          - task: ExpressV2Internal@1
            inputs:
              UseServerMonitorTask: true
              EndpointProviderType: ApprovalService
              ApprovalServiceEnvironment: Production
              ServiceRootLocation: 'LinkedArtifact'
              RolloutSpecType: 'RSPath'
              ServiceRootPath: $(Pipeline.Workspace)/drop_build_main/bin
              RolloutSpecPath: $(Pipeline.Workspace)/drop_build_main/bin/Production/Slots/RolloutSpec.SetSlot.BuildArtifact.stage4.json

  - ${{ if eq(parameters.isHotfix, false) }}:
    - stage: Prod_Validate_Stage4
      displayName: Validate Stage4
      dependsOn: Prod_Stage4
      variables:
        ob_release_environment: Production
      jobs:
        - job: ManualValidationJob
          timeoutInMinutes: 4320
          pool:
            type: server
          steps:
            - task: ManualValidation@1
              timeoutInMinutes: 1440
              inputs:
                notifyUsers: '[Project Vienna Services]\Data Experiences'
                onTimeout: 'resume'

  - stage: Prod_Stage5
    displayName: Deploy Stage5
    ${{ if eq(parameters.isHotfix, false) }}:
      dependsOn: Prod_Validate_Stage4
    ${{ else }}:
      dependsOn: Prod_Stage4
    variables:
      ob_release_environment: Production
    jobs:
      - job: ReleaseJob
        pool:
          type: release
        steps:
          - download: current
          - task: ExpressV2Internal@1
            inputs:
              UseServerMonitorTask: true
              EndpointProviderType: ApprovalService
              ApprovalServiceEnvironment: Production
              ServiceRootLocation: 'LinkedArtifact'
              RolloutSpecType: 'RSPath'
              ServiceRootPath: $(Pipeline.Workspace)/drop_build_main/bin
              RolloutSpecPath: $(Pipeline.Workspace)/drop_build_main/bin/Production/Slots/RolloutSpec.SetSlot.BuildArtifact.stage5.json

  - stage: MC_PT6H
    displayName: 'MC: PT6H'
    ${{ if eq(parameters.isHotfix, false) }}:
      dependsOn: Prod_Validate_Stage4
    ${{ else }}:
      dependsOn: Prod_Stage4
    variables:
      ob_release_environment: Mooncake
    jobs:
      - job: ReleaseJob
        pool:
          type: release
        steps:
          - download: current
          - task: ExpressV2Internal@1
            inputs:
              UseServerMonitorTask: false
              EnableStrictValidation: false
              ValidateOnly: false
              EndpointProviderType: ApprovalService
              ApprovalServiceEnvironment: Mooncake
              ServiceRootLocation: 'LinkedArtifact'
              RolloutSpecType: 'RSPath'
              ServiceRootPath: $(Pipeline.Workspace)/drop_build_main/bin
              RolloutSpecPath: $(Pipeline.Workspace)/drop_build_main/bin/Mooncake.RolloutSpec.PT6H.json
  - stage: MC_PT6H_RP
    displayName: 'MC: PT6H RP'
    ${{ if eq(parameters.isHotfix, false) }}:
      dependsOn: Prod_Validate_Stage4
    ${{ else }}:
      dependsOn: Prod_Stage4
    variables:
      ob_release_environment: Mooncake
    jobs:
      - job: ReleaseJob
        pool:
          type: release
        steps:
          - download: current
          - task: ExpressV2Internal@1
            inputs:
              UseServerMonitorTask: false
              EnableStrictValidation: false
              ValidateOnly: false
              EndpointProviderType: ApprovalService
              ApprovalServiceEnvironment: Mooncake
              ServiceRootLocation: 'LinkedArtifact'
              RolloutSpecType: 'RSPath'
              ServiceRootPath: $(Pipeline.Workspace)/drop_build_main/bin
              RolloutSpecPath: $(Pipeline.Workspace)/drop_build_main/bin/Mooncake/Slots/RolloutSpec.SetSlot.BuildArtifact.All.PT6H.json

  - stage: FF_PT6H_RP
    displayName: 'FF: PT6H RP'
    ${{ if eq(parameters.isHotfix, false) }}:
      dependsOn: Prod_Validate_Stage4
    ${{ else }}:
      dependsOn: Prod_Stage4
    variables:
      ob_release_environment: Fairfax
    jobs:
      - job: ReleaseJob
        pool:
          type: release
        steps:
          - download: current
          - task: ExpressV2Internal@1
            inputs:
              UseServerMonitorTask: false
              EnableStrictValidation: false
              ValidateOnly: false
              EndpointProviderType: ApprovalService
              ApprovalServiceEnvironment: Fairfax
              ServiceRootLocation: 'LinkedArtifact'
              RolloutSpecType: 'RSPath'
              ServiceRootPath: $(Pipeline.Workspace)/drop_build_main/bin
              RolloutSpecPath: $(Pipeline.Workspace)/drop_build_main/bin/Fairfax/Slots/RolloutSpec.SetSlot.BuildArtifact.All.PT6H.json

  - stage: USSec_P1D_RP
    displayName: Deploy USSec P1D RP
    ${{ if eq(parameters.isHotfix, false) }}:
      dependsOn: Prod_Validate_Stage4
    ${{ else }}:
      dependsOn: Prod_Stage4
    variables:
      ob_release_environment: USSec
      ob_release_stagetype: deployment
      ArtifactToDeploy: drop_build_main
      CvrpManifestRelativePath: \build.manifest
    jobs:
      - job: ReleaseJob
        pool:
          type: release
        steps:
          - download: current
          - task: ExpressV2Internal@1
            inputs:
              UseServerMonitorTask: true
              EnableStrictValidation: false
              ValidateOnly: false
              EndpointProviderType: ApprovalService
              ApprovalServiceEnvironment: USSec
              ServiceRootLocation: 'LinkedArtifact'
              RolloutSpecType: 'RSPath'
              ServiceRootPath: $(Pipeline.Workspace)/drop_build_main/bin
              RolloutSpecPath: $(Pipeline.Workspace)/drop_build_main/bin/USSec/Slots/RolloutSpec.SetSlot.BuildArtifact.All.P1D.json
      - job: MonitorRelease
        dependsOn: ReleaseJob
        pool:
          type: server
        cancelTimeoutInMinutes: 5
        timeoutInMinutes: 7200
        steps:
          - task: SovereignReleaseMonitoring@1
            displayName: "Sovereign Release Monitoring"
            inputs:
              requestedMonitorTimeout: 7200
              defaultTimeoutAction: Failed
              defaultCancelAction: Canceled

  - stage: USNat_P1D_RP
    displayName: Deploy USNat P1D RP
    ${{ if eq(parameters.isHotfix, false) }}:
      dependsOn: Prod_Validate_Stage4
    ${{ else }}:
      dependsOn: Prod_Stage4
    variables:
      ob_release_environment: USNat
      ob_release_stagetype: deployment
      ArtifactToDeploy: drop_build_main
      CvrpManifestRelativePath: \build.manifest
    jobs:
      - job: ReleaseJob
        pool:
          type: release
        steps:
          - download: current
          - task: ExpressV2Internal@1
            inputs:
              UseServerMonitorTask: true
              EnableStrictValidation: false
              ValidateOnly: false
              EndpointProviderType: ApprovalService
              ApprovalServiceEnvironment: USNat
              ServiceRootLocation: 'LinkedArtifact'
              RolloutSpecType: 'RSPath'
              ServiceRootPath: $(Pipeline.Workspace)/drop_build_main/bin
              RolloutSpecPath: $(Pipeline.Workspace)/drop_build_main/bin/USNat/Slots/RolloutSpec.SetSlot.BuildArtifact.All.P1D.json
      - job: MonitorRelease
        dependsOn: ReleaseJob
        pool:
          type: server
        cancelTimeoutInMinutes: 5
        timeoutInMinutes: 7200
        steps:
          - task: SovereignReleaseMonitoring@1
            displayName: "Sovereign Release Monitoring"
            inputs:
              requestedMonitorTimeout: 7200
              defaultTimeoutAction: Failed
              defaultCancelAction: Canceled
