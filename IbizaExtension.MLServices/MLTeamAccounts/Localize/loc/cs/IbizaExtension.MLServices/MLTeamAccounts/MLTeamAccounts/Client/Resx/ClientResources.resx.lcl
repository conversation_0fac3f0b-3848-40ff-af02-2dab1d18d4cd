﻿<?xml version="1.0" encoding="utf-8"?>
<LCX SchemaVersion="6.0" Name="M:\AzureMLVS15-005\_work\161\s\IbizaExtension.MLServices\MLTeamAccounts\MLTeamAccounts\Client\Resx\ClientResources.resx" PsrId="211" FileType="1" SrcCul="en-US" TgtCul="cs-CZ" xmlns="http://schemas.microsoft.com/locstudio/2006/6/lcx">
  <OwnedComments>
    <Cmt Name="Dev" />
    <Cmt Name="LcxAdmin" />
    <Cmt Name="Rccx" />
  </OwnedComments>
  <Settings Name="@SettingsPath@\default.lss" Type="Lss" />
  <Item ItemId=";ClientResources.resx" ItemType="0" PsrId="211" Leaf="false">
    <Disp Icon="Expand" Expand="true" Disp="true" LocTbl="false" />
    <Item ItemId=";Strings" ItemType="0" PsrId="211" Leaf="false">
      <Disp Icon="Str" Disp="true" LocTbl="false" />
      <Item ItemId=";AIServices_SummaryTab_label" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[AI Services]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Služby umělé inteligence]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AccountPart_deprecated" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deprecated]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Zastaralé]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AccountPart_deprecatedLongMessage" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The resource is no longer available. Please use Machine Learning Services Workspace Extension.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Prostředek už není k dispozici. Použijte prosím rozšíření pracovního prostoru Machine Learning Services.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AccountPart_deprecatedShortMessage" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Use Machine Learning Services Extension.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Použijte rozšíření Machine Learning Services.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdditionalResourceInfo" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[For your convenience, these resources are added automatically to the workspace, if regionally available: <a href={0} target="_blank">Azure storage</a>, <a href={1} target="_blank">Azure Application Insights</a> and <a href={2} target="_blank">Azure Key Vault</a>.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Do pracovního prostoru pro váš komfort automaticky následující prostředky (pokud jsou v oblasti k dispozici): <a href={0} target="_blank">Azure Storage</a>, <a href={1} target="_blank">Azure Application Insights</a> and <a href={2} target="_blank">Azure Key Vault</a>.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Key_KeyVaultChangeControl_action" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Click to select key]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Kliknutím vyberte klíč.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Key_KeyVaultChangeControl_key" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Key: {key}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Klíč: {key}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Key_KeyVaultChangeControl_keyVault" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Key vault: {keyvault}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Trezor klíčů: {keyvault}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Key_KeyVaultChangeControl_label" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Key vault]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Trezor klíčů]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Key_KeyVaultChangeControl_purgeProtectionRequired" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[When bringing your own encryption key, your Azure Key Vault must have purge protection enabled to protect against accidental loss of data access.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Při používání vlastního šifrovacího klíče musí mít vaše Azure Key Vault povolenou ochranu před vymazáním, aby se chránila před náhodnou ztrátou přístupu k datům.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Key_KeyVaultChangeControl_required" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Key is required]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Klíč je povinný.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Key_KeyVaultChangeControl_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Key vault and key]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Trezor klíčů a klíč]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Key_KeyVaultChangeControl_version" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Version: {version}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Verze: {version}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_DataEncryption_LinkText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more about customer-managed key encryption.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Přečtěte si další informace o šifrování klíčů spravovaných zákazníkem.]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Use customer-managed keys - Azure Machine Learning | Microsoft Docs]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_DataEncryption_ServiceSide_infoText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure machine learning service stores metrics and metadata in an Azure Cosmos DB instance where all data is encrypted ​at rest. By default, the data is encrypted with Microsoft-managed keys. You may choose to bring your own (customer-managed) keys. {0}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Služba Azure Machine Learning ukládá metriky a metadata do instance Azure Cosmos DB, kde se všechna data šifrují ​při nečinnosti. Standardně se data šifrují pomocí klíčů spravovaných Microsoftem. Můžete ale použít i své vlastní (zákazníkem spravované) klíče. {0}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_DataEncryption_ServiceSide_label" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Use service-side encryption ({0})]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Použít šifrování na straně služby ({0})]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_DataEncryption_Type_label" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Encrypt data using a customer managed key]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Šifrovat data pomocí klíče spravovaného zákazníkem]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Encryption type]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_DataEncryption_WarningMessage" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[After workspace creation, you cannot change encryption key type between Microsoft-managed keys  and Customer-managed keys.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Po vytvoření pracovního prostoru nelze změnit typ šifrovacího klíče z klíče spravovaného společností Microsoft na klíč spravovaný zákazníkem a naopak.]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[When using a customer-managed key, the costs for your subscription will be higher because of the additional resources in your subscription. To estimate the cost, use the Azure pricing calculator. To learn more, see {0}]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_DataEncryption_checkboxByoInfo" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Optionally use pre-created resources for storing encrypted workspace data. Using your own resources for encryption, allows for enhanced configuration of these resources in compliance with your organization’s IT and security requirements, but implies additional management actions by you.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Volitelně můžete k ukládání šifrovaných dat pracovního prostoru použít předem vytvořené prostředky. Použití vašich vlastních zdrojů pro šifrování umožňuje vylepšenou konfiguraci těchto zdrojů v souladu s IT a bezpečnostními požadavky vaší organizace, ale vyžaduje další akce vaší správy.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_DataEncryption_checkboxByoLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Bring existing resources for storing encrypted data (preview)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Použít existující prostředky pro ukládání šifrovaných dat (Preview)]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Bring existing resources for data encryption]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_DataEncryption_checkboxLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Enable encryption using a Customer Managed Key]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Povolit šifrování pomocí klíče spravovaného zákazníkem]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_DataEncryption_cosmosInfo" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select a pre-created Azure Cosmos DB resource. If you select 'None', Azure ML will create a Cosmos DB resource in a new resource group in your subscription that will be managed by Microsoft. Using your own resources for encryption implies additional management responsibility by you.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Vyberte předem vytvořený prostředek Azure Cosmos DB. Když vyberete možnost Žádný, Azure ML vytvoří prostředek služby Cosmos DB v nové skupině prostředků v předplatném, kterou bude spravovat Microsoft. Když k šifrování použijete své vlastní prostředky, bude to pro vás znamenat další odpovědnost na správu.]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Select a pre-created Azure Cosmos DB resource. If you select 'None', Azure ML will create a Cosmos DB resource in a new resource group in your subscription that will be managed by Microsoft.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_DataEncryption_cosmosLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Cosmos DB]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Cosmos DB]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_DataEncryption_customerManaged" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Customer-managed keys]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Klíče spravované zákazníkem]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Customer-managed keys (Only available in enterprise version)]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_DataEncryption_header" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Your data is encrypted by default using Microsoft-managed keys. For additional control over your data, you may choose to bring your own key for encryption. {0}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Vaše data se ve výchozím nastavení šifrují pomocí klíčů spravovaných Microsoftem. Pokud chcete mít nad svými daty větší kontrolu, můžete použít vlastní klíč pro šifrování. {0}]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Azure Machine Learning service stores metrics and metadata in an Azure Cosmos DB instance where all data is encrypted ​at rest. By default, the data is encrypted with Microsoft-managed keys. You may choose to bring your own (customer-managed) keys.​]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_DataEncryption_infoText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure machine learning service stores metrics and metadata in an Azure Cosmos DB instance where all data is encrypted ​at rest. By default, the data is encrypted with Microsoft-managed keys. You may choose to bring your own (customer-managed) keys. {0}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Služba Azure Machine Learning ukládá metriky a metadata do instance Azure Cosmos DB, kde se všechna data šifrují ​při nečinnosti. Standardně se data šifrují pomocí klíčů spravovaných Microsoftem. Můžete ale použít i své vlastní (zákazníkem spravované) klíče. {0}]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Azure machine learning service stores metrics and metadata in an Azure Cosmo DB instance where all data is encrypted ​at rest. By default, the data is encrypted with Microsoft-managed keys. You may choose to bring your own (customer-managed) keys. {0}]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_DataEncryption_learnMoreText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Další informace]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_DataEncryption_microsoftManaged" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Microsoft-managed keys]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Klíče spravované Microsoftem]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_DataEncryption_searchInfo" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select a pre-created Azure Search resource. If you select 'None', Azure ML will create a Search resource in a new resource group in your subscription that will be managed by Microsoft. Using your own resources for encryption implies additional management responsibility by you.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Vyberte předem vytvořený prostředek Azure Search. Když vyberete možnost Žádný, Azure ML vytvoří prostředek služby Search v nové skupině prostředků v předplatném, kterou bude spravovat Microsoft. Když k šifrování použijete své vlastní prostředky, bude to pro vás znamenat další odpovědnost za správu.]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Select a pre-created Azure Search resource. If you select 'None', Azure ML will create a Search resource in a new resource group in your subscription that will be managed by Microsoft.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_DataEncryption_searchLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Search]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Vyhledat]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_DataEncryption_storageInfo" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select a pre-created Azure Storage resource. If you select 'None', Azure ML will create a Storage resource in a new resource group in your subscription that will be managed by Microsoft. Using your own resources for encryption implies additional management responsibility by you.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Vyberte předem vytvořený prostředek Azure Storage. Když vyberete možnost Žádný, Azure ML vytvoří prostředek služby Storage v nové skupině prostředků v předplatném, kterou bude spravovat Microsoft. Když k šifrování použijete své vlastní prostředky, bude to pro vás znamenat další odpovědnost za správu.]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Select a pre-created Azure Storage resource. If you select 'None', Azure ML will create a Storage resource in a new resource group in your subscription that will be managed by Microsoft.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_DataEncryption_storageLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Storage]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Úložiště]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_DataEncryption_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Data encryption]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Šifrování dat]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_DataImpact_HBI_label" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[High business impact workspace]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Pracovní prostor s vysokým dopadem na firmu]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_DataImpact_header" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[If your workspace contains sensitive data, you can specify a high business impact workspace. This will control the amount of data Microsoft collects for diagnostic purposes and enables additional encryption in Microsoft managed environments.​]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Pokud váš pracovní prostor obsahuje citlivá data, můžete určit pracovní prostor s vysokým dopadem na firmu. To umožňuje řídit množství dat, která Microsoft shromažďuje pro diagnostické účely, a povolit další šifrování v prostředích spravovaných Microsoftem.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_DataImpact_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Data impact]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Dopad dat]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_ManagedIdentity_description" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A managed identity enables Azure resources to authenticate to cloud services without storing credentials in code. Once enabled, all necessary permissions can be granted via Azure role-based access control. A workspace can be given either a system assigned identity or a user assigned identity.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Spravovaná identita umožňuje ověřování prostředků Azure v cloudových službách bez uložení přihlašovacích údajů v kódu. Po povolení tohoto nastavení bude možné všechna potřebná oprávnění udělovat prostřednictvím řízení přístupu na základě role v Azure. Pracovní prostor může mít identitu přiřazenou systémem nebo uživatelem.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_ManagedIdentity_disabledWarning" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The managed user assigned identity option is only supported if an existing storage account, key vault, and container registry are used.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Možnost spravované identity přiřazené uživatelem se podporuje jenom v případě, že se používá existující účet úložiště, Key Vault a registr kontejnerů.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_ManagedIdentity_header" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Managed identity]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Spravovaná identita]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_ManagedIdentity_permissionWarning" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[If you bring your own associated resources, instead of having Azure Machine Learning service create them, you must grant the managed identity roles on those resources. Use the {0} to make the assignments.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Pokud přinesete vlastní přidružené prostředky, místo toho, abyste je vytvořili ve službě Azure Machine Learning Service, musíte na těchto prostředcích udělit spravované role identit. Pomocí {0} proveďte přiřazení.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_ManagedIdentity_permissionWarningLinkText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[role assignment ARM template]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[šablona ARM pro přiřazení role]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_ManagedIdentity_radioGroupTitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Identity type]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Typ identity]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_ManagedIdentity_systemAssignedText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[System assigned identity]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Identita přiřazená systémem]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_ManagedIdentity_userAssignedPickerLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[User assigned identity]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Identita přiřazená uživatelem]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_ManagedIdentity_userAssignedPickerRequiredMessage" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[User identity is required]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Identita uživatele se vyžaduje.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_ManagedIdentity_userAssignedPickerSubscriptionMessage" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Workspace and selected user identity must be in the same subscription.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Pracovní prostor a vybraná identita uživatele musejí být ve stejném předplatném.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_ManagedIdentity_userAssignedPickerText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Click to select identity]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Kliknutím vyberte identitu.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_ManagedIdentity_userAssignedText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[User assigned identity]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Identita přiřazená uživatelem]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_ManagedIdentity_userIdentityNameLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[User identity name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Název identity uživatele]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_ManagedIdentity_userIdentityResourceGroupLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[User identity resource group]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Skupina prostředků s identitou uživatele]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_StorageAccountAccess_credentialBasedText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Credential-based access]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Přístup na základě přihlašovacích údajů]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_StorageAccountAccess_description" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure machine learning allows you to choose between credential-based or identity-based access when connecting to the default storage account. When using identity-based authentication, the Storage Blob Data Contributor role must be granted to the workspace managed identity on the storage account.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure Machine Learning umožňuje při připojování k výchozímu účtu úložiště zvolit mezi přístupem na základě přihlašovacích údajů a přístupem na základě identity. Když se bude používat ověřování podle identity, musí se identitě spravované pracovním prostorem v účtu úložiště udělit role Přispěvatel dat v objektech blob služby Storage.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_StorageAccountAccess_header" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Storage account access]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Přístup k účtu úložiště]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_StorageAccountAccess_identityBasedText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Identity-based access]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Přístup na základě identity]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_StorageAccountAccess_learnMoreText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Další informace]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_StorageAccountAccess_previewLinkText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[(preview)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[(Preview)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_StorageAccountAccess_radioGroupTitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Storage account access type]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Typ přístupu účtu úložiště]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_StorageAccountAccess_sharedKeyAccessCheckboxLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Disable shared key access]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Zakázat přístup pomocí sdílených klíčů]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_StorageAccountAccess_sharedKeyAccessDisableDocumentationLinkText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Další informace]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_StorageAccountAccess_sharedKeyAccessDisableWarning" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Disable shared key access option {0} disables key access at the storage account, any requests to the storage account that are authorized with shared key, including shared access signatures (SAS), will be denied. This option might require additional role configuration to individual users for certain use cases. {1} about disabling shared key access for your workspace's storage account]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Možnost Zakázat přístup ke sdílenému klíči {0} zakáže přístup ke klíči v účtu úložiště. Všechny požadavky na účet úložiště, které jsou autorizované pomocí sdíleného klíče, včetně sdílených přístupových podpisů (SAS), se zamítnou. Tato možnost může vyžadovat další konfiguraci role pro jednotlivé uživatele pro určité případy použití. {1} o zakázání přístupu ke sdíleným klíčům pro účet úložiště pracovního prostoru]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Disable shared key access option (preview) disables key access at the storage account, any requests to the storage account that are authorized with shared key, including shared access signatures (SAS), will be denied. This option might require additional role configuration to individual users for certain use cases. {0} about disabling shared key access for your workspace's storage account]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab__Key_KeyVaultChangeControl_version" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Version: {version}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Verze: {version}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AppInsights_Dropdown_Hub_info" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The AI hub uses Azure Application Insights to store monitoring information about your deployed models. You may create a new Azure Application Insights resource or select an existing one in your subscription.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Centrum AI ukládá informace o monitorování nasazených modelů pomocí Azure Application Insights. Můžete vytvořit nový prostředek Azure Application Insights nebo vybrat existující prostředek v předplatném.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AppInsights_Dropdown_SettingsBlade_Name_infoDescription" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The name must meet the following requirements:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Název musí splňovat tyto požadavky:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AppInsights_Dropdown_SettingsBlade_Name_infoItem1" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Unique across the resource group]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Jedinečné mezi skupinami prostředků]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AppInsights_Dropdown_SettingsBlade_Name_infoItem2" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Between 1 and 255 characters long]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Délka mezi 1 a 255 znaky]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AppInsights_Dropdown_SettingsBlade_Name_infoItem3" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Only contain alphanumeric characters, periods, underscores, hyphens, and parenthesis]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Obsahuje jen alfanumerické znaky, tečky, podtržítka, spojovníky a závorky.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AppInsights_Dropdown_SettingsBlade_Name_infoItem4" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Cannot end with a period]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Nemůže končit tečkou.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AppInsights_Dropdown_SettingsBlade_nameInvalidMessage" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The name must contain between 1 to 255 characters inclusive. The name only allows alphanumeric characters, periods, underscores, hyphens and parenthesis and cannot end in a period.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Název může obsahovat 1 až 255 znaků. Může se skládat jen z alfanumerických znaků, teček, podtržítek, pomlček a závorek a nesmí končit tečkou.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AppInsights_Dropdown_SettingsBlade_nameLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Název]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AppInsights_Dropdown_SettingsBlade_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create new application insights]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Vytvořit novou službu Application Insights]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AppInsights_Dropdown_createNewAriaLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create new application insights]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Vytvořit novou službu Application Insights]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AppInsights_Dropdown_info" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The workspace uses Azure Application Insights to store monitoring information about your deployed models. You may create a new Azure Application Insights resource or select an existing one in your subscription.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Pracovní prostor ukládá informace o monitorování nasazených modelů pomocí Azure Application Insights. Můžete vytvořit nový prostředek Azure Application Insights, nebo vybrat existující prostředek v předplatném.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AppInsights_Dropdown_label" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Application insights]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Application Insights]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AppInsights_SummaryTab_label" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Application insights]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Application Insights]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetTypeNames_MLAppDeployment_lowerPlural" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[machine learning online deployments]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[nasazení strojového učení online]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[ml app deployments]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetTypeNames_MLAppDeployment_lowerSingular" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[machine learning online deployment]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[nasazení strojového učení online]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[ml app deployment]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetTypeNames_MLAppDeployment_plural" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Machine learning online deployments]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Nasazení strojového učení online]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[ML App Deployments]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetTypeNames_MLAppDeployment_singular" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Machine learning online deployment]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Nasazení strojového učení online]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[ML App Deployment]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetTypeNames_MLApp_lowerPlural" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[machine learning online endpoints]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[koncové body strojového učení online]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[ml apps]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetTypeNames_MLApp_lowerSingular" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[machine learning online endpoint]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[koncový bod strojového učení online]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[ml app]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetTypeNames_MLApp_plural" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Machine learning online endpoints]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Koncové body strojového učení online]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[ML Apps]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetTypeNames_MLApp_singular" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Machine learning online endpoint]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Koncový bod strojového učení online]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[ML App]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetTypeNames_MLRegistry_lowerPlural" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[azure machine learning registries]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[registry azure machine learning]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetTypeNames_MLRegistry_lowerSingular" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[azure machine learning registry]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[registr azure machine learning]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetTypeNames_MLRegistry_plural" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure Machine Learning registries]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Registry Azure Machine Learning]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetTypeNames_MLRegistry_singular" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure Machine Learning registry]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Registr Azure Machine Learning]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetTypeNames_MachineLearningServices_Default_lowerPlural" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[workspaces]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[pracovní prostory]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetTypeNames_MachineLearningServices_Default_lowerSingular" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[workspace]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[pracovní prostor]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetTypeNames_MachineLearningServices_Default_plural" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure Machine Learning workspaces]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Pracovní prostory Azure Machine Learning]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetTypeNames_MachineLearningServices_Default_singular" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure Machine Learning workspace]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Pracovní prostor Azure Machine Learning]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetTypeNames_MachineLearningServices_FeatureStore_lowerPlural" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[feature stores]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[úložiště funkcí]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetTypeNames_MachineLearningServices_FeatureStore_lowerSingular" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[feature store]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[úložiště funkcí]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetTypeNames_MachineLearningServices_FeatureStore_plural" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure Machine Learning feature stores]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure Machine Learning úložiště funkcí]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetTypeNames_MachineLearningServices_FeatureStore_singular" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure Machine Learning feature store]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure Machine Learning úložiště funkcí]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetTypeNames_MachineLearningServices_Hub_lowerPlural" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure AI hubs]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Centra Azure AI]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Azure AI]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetTypeNames_MachineLearningServices_Hub_lowerSingular" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure AI hub]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Centrum Azure AI]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Azure AI]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetTypeNames_MachineLearningServices_Hub_plural" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure AI hubs]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Centra Azure AI]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Azure AI]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetTypeNames_MachineLearningServices_Hub_singular" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure AI hub]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Centrum Azure AI]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Azure AI]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetTypeNames_MachineLearningServices_Project_lowerPlural" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure AI projects]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Projekty Azure AI]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[workspace projects]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetTypeNames_MachineLearningServices_Project_lowerSingular" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure AI project]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure AI projekt]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[workspace project]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetTypeNames_MachineLearningServices_Project_plural" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure AI projects]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Projekty Azure AI]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Azure Machine Learning workspace projects]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetTypeNames_MachineLearningServices_Project_singular" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure AI project]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure AI projekt]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Azure Machine Learning workspace project]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetTypeNames_MachineLearningServices_lowerPlural" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[workspaces]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[pracovní prostory]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[machine learning workspaces]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetTypeNames_MachineLearningServices_lowerSingular" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[workspace]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[pracovní prostor]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[machine learning workspace]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetTypeNames_MachineLearningServices_plural" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure Machine Learning]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Machine Learning]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Machine learning]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetTypeNames_MachineLearningServices_singular" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure Machine Learning workspace]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Pracovní prostor Azure Machine Learning]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Azure Machine Learning]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_AIStudio_Browse_Commands_CreateAzureAI" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[New Azure AI hub]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Nové centrum Azure AI]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[New Azure AI]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_AIStudio_Description" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Your platform to build generative AI solutions and custom copilots]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Vaše platforma pro vytváření řešení generativní AI a vlastních kopilotů]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Azure AI Studio description]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_AIStudio_Hub_AIServices_Settings_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create new Azure AI Services]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Vytvořit nové Azure AI služby]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_AIStudio_Hub_Essentials_AIServices_label" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[AI Services provider]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Poskytovatel služeb AI]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_AIStudio_Hub_Essentials_DefaultProjectResourceGroup_label" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Project resource group (default)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Skupina prostředků projektu (výchozí)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_AIStudio_Hub_Overview_Banner_Description_label" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Your Azure AI hub provides enterpise-grade security, and a collaborative environment to build AI solutions. Centrally audit usage and cost, and set up connections to your company resources that all projects can use. {}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Vaše centrum Azure AI poskytuje zabezpečení na podnikové úrovni a prostředí pro spolupráci při vytváření řešení AI. Centrálně auditujte využití a náklady a nastavte připojení k prostředkům vaší společnosti, které můžou používat všechny projekty. {}]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Your Azure AI resource provides enterpise-grade security, and a collaborative environment to build AI solutions. Centrally audit usage and cost, and set up connections to your company resources that all projects can use. {}]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_AIStudio_Hub_Overview_Banner_Description_learnMoreText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[learn more about the Azure AI Studio]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[další informace o Azure AI Studio]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_AIStudio_Hub_Overview_Banner_launchButtonText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Launch Azure AI Studio]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Spustit Azure AI Studio]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_AIStudio_Hub_Overview_Banner_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Govern the environment for your team in AI Studio]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Řízení prostředí pro váš tým v AI Studiu]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_AIStudio_KeysAndEndpoints_Command_regenKey1" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Regenerate Key 1]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Znovu vygenerovat klíč 1]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_AIStudio_KeysAndEndpoints_Command_regenKey2" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Regenerate Key 2]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Znovu vygenerovat klíč 2]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_AIStudio_KeysAndEndpoints_endpoint" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Endpoint]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Koncový bod]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_AIStudio_KeysAndEndpoints_hideKeys" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Hide keys]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Skrýt klíče]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_AIStudio_KeysAndEndpoints_key1" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[KEY 1]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[KLÍČ 1]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_AIStudio_KeysAndEndpoints_key2" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[KEY 2]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[KLÍČ 2]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_AIStudio_KeysAndEndpoints_message" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[These keys are used to access your Azure AI services API. Do not share your keys. Store them securely- for example, using Azure Key Vault. We also recommend regenerating these keys regularly. Only one key is necessary to make an API call. When regenerating the first key, you can use the second key for continued access to the service.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Tyto klíče slouží k přístupu k rozhraní API služeb Azure AI. Své klíče s nikým nesdílejte. Ukládejte je bezpečně – například pomocí Azure Key Vault. Doporučujeme je také pravidelně obnovovat. K volání rozhraní API se vyžaduje jen jeden klíč. Během obnovování prvního klíče můžete pro nepřerušený přístup ke službě použít druhý klíč.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_AIStudio_KeysAndEndpoints_regenerateKeys" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Regenerate keys]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Znovu vygenerovat klíče]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_AIStudio_KeysAndEndpoints_showKeys" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Show keys]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Zobrazit klíče]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_AIStudio_KeysAndEndpoints_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Keys and Endpoints]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Klíče a koncové body]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_AIStudio_Keywords" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[AI, Artificial Intelligence, Preview, Deep Learning, Analytics, Data, NLP, Natural Language Processing, CNN, Neural Network, Train, Computer Vision, Models, Data Science, Classification, Regression, Reinforcement Learning, LLM, Chatbot]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[AI, umělá inteligence, Preview, hluboké učení, analýzy, data, NLP, zpracování přirozeného jazyka, CNN, neurální síť, trénování, počítačové zpracování obrazu, modely, datové vědy, klasifikace, regrese, zpětnovazební učení, LLM, chatbot]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_AIStudio_Names_lowerPlural" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure AI Studio]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure AI Studio]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_AIStudio_Names_lowerSingular" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure AI Studio]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure AI Studio]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_AIStudio_Names_plural" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure AI Studio]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure AI Studio]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_AIStudio_Names_singular" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure AI Studio]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure AI Studio]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_AIStudio_Project_Overview_Banner_Description_label" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Jumpstart your AI solution development with pre-built templates and work on your project either in code or in the studio.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Zprovozněte vývoj řešení AI s využitím předem připravených šablon a pracujte na projektu v kódu nebo ve studiu.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_AIStudio_Project_Overview_Banner_launchButtonText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Launch studio]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Spustit studio]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_AIStudio_Project_Overview_Banner_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Start building in Azure AI Studio]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Začněte sestavovat v Azure AI Studio]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_Core42_Names_lowerPlural" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Core42]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Core42]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_Core42_Names_lowerSingular" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Core42]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Core42]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_Core42_Names_plural" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Core42]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Core42]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_Core42_Names_singular" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Core42]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Core42]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_Core42_Page_Description_body" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select Continue, and you'll be redirected to the model catalog in Azure AI Studio where you can finish subscribing and start using this model.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Vyberte Pokračovat a budete přesměrováni do katalogu modelů v nástroji Azure AI Studio, kde můžete dokončit přihlášení k odběru a začít používat tento model.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_Core42_Page_Description_intro" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Thank you for your interest.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Ceníme si vašeho zájmu.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_Core42_Page_primaryButton" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Continue to Azure AI Studio model catalog]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Pokračovat do katalogu modelů Azure AI Studio]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_Core42_Page_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Continue to Azure AI Studio to use this offer]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Pokud chcete tuto nabídku používat, pokračujte do Azure AI Studio.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_Llama2_Names_lowerPlural" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Llama2]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Llama2]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_Llama2_Names_lowerSingular" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Llama2]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Llama2]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_Llama2_Names_plural" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Llama2]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Llama2]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_Llama2_Names_singular" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Llama2]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Llama2]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_Llama2_Page_Description_body" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select Continue, and you'll be redirected to the model catalog in Azure AI Studio where you can finish subscribing and start using this model.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Vyberte Pokračovat a budete přesměrováni do katalogu modelů v nástroji Azure AI Studio, kde můžete dokončit přihlášení k odběru a začít používat tento model.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_Llama2_Page_Description_intro" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Thank you for your interest.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Ceníme si vašeho zájmu.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_Llama2_Page_primaryButton" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Continue to Azure AI Studio model catalog]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Pokračovat do katalogu modelů Azure AI Studio]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_Llama2_Page_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Continue to Azure AI Studio to use this offer]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Pokud chcete tuto nabídku používat, pokračujte do Azure AI Studio.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_Mistral_Names_lowerPlural" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Mistral]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Mistral]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_Mistral_Names_lowerSingular" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Mistral]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Mistral]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_Mistral_Names_plural" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Mistral]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Mistral]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_Mistral_Names_singular" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Mistral]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Mistral]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_Mistral_Page_Description_body" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select Continue, and you'll be redirected to the model catalog in Azure AI Studio where you can finish subscribing and start using this model.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Vyberte Pokračovat a budete přesměrováni do katalogu modelů v nástroji Azure AI Studio, kde můžete dokončit přihlášení k odběru a začít používat tento model.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_Mistral_Page_Description_intro" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Thank you for your interest.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Ceníme si vašeho zájmu.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_Mistral_Page_primaryButton" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Continue to Azure AI Studio model catalog]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Pokračovat do katalogu modelů Azure AI Studio]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_Mistral_Page_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Continue to Azure AI Studio to use this offer]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Pokud chcete tuto nabídku používat, pokračujte do Azure AI Studio.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_ModelProvider_Names_lowerPlural" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model Provider]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Zprostředkovatel modelu]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_ModelProvider_Names_lowerSingular" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model Provider]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Zprostředkovatel modelu]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_ModelProvider_Names_plural" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model Provider]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Zprostředkovatel modelu]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_ModelProvider_Names_singular" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model Provider]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Zprostředkovatel modelu]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_ModelProvider_Page_Description_body" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select Continue, and you'll be redirected to the model catalog in Azure AI Studio where you can finish subscribing and start using this model.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Vyberte Pokračovat a budete přesměrováni do katalogu modelů v nástroji Azure AI Studio, kde můžete dokončit přihlášení k odběru a začít používat tento model.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_ModelProvider_Page_Description_intro" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Thank you for your interest.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Ceníme si vašeho zájmu.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_ModelProvider_Page_primaryButton" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Continue to Azure AI Studio model catalog]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Pokračovat do katalogu modelů Azure AI Studio]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_ModelProvider_Page_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Continue to Azure AI Studio to use this offer]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Pokud chcete tuto nabídku používat, pokračujte do Azure AI Studio.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_Nixtla_Names_lowerPlural" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Nixtla]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Nixtla]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_Nixtla_Names_lowerSingular" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Nixtla]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Nixtla]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_Nixtla_Names_plural" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Nixtla]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Nixtla]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_Nixtla_Names_singular" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Nixtla]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Nixtla]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_Nixtla_Page_Description_body" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select Continue, and you'll be redirected to the model catalog in Azure AI Studio where you can finish subscribing and start using this model.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Vyberte Pokračovat a budete přesměrováni do katalogu modelů v nástroji Azure AI Studio, kde můžete dokončit přihlášení k odběru a začít používat tento model.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_Nixtla_Page_Description_intro" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Thank you for your interest.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Ceníme si vašeho zájmu.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_Nixtla_Page_primaryButton" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Continue to Azure AI Studio model catalog]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Pokračovat do katalogu modelů Azure AI Studio]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_Nixtla_Page_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Continue to Azure AI Studio to use this offer]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Pokud chcete tuto nabídku používat, pokračujte do Azure AI Studio.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_cohere_Names_lowerPlural" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Cohere]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Cohere]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_cohere_Names_lowerSingular" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Cohere]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Cohere]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_cohere_Names_plural" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Cohere]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Cohere]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_cohere_Names_singular" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Cohere]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Cohere]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_cohere_Page_Description_body" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select Continue, and you'll be redirected to the model catalog in Azure AI Studio where you can finish subscribing and start using this model.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Vyberte Pokračovat a budete přesměrováni do katalogu modelů v nástroji Azure AI Studio, kde můžete dokončit přihlášení k odběru a začít používat tento model.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_cohere_Page_Description_intro" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Thank you for your interest.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Ceníme si vašeho zájmu.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_cohere_Page_primaryButton" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Continue to Azure AI Studio model catalog]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Pokračovat do katalogu modelů Azure AI Studio]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_cohere_Page_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Continue to Azure AI Studio to use this offer]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Pokud chcete tuto nabídku používat, pokračujte do Azure AI Studio.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_description" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Workspaces are where you manage all the models, assets, and data related to your machine learning projects. Create one now to start using Azure Machine Learning.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Pracovní prostory jsou místo, kde spravujete všechny modely, prostředky a data související s vašimi projekty strojového učení. Pokud chcete začít používat Azure Machine Learning, vytvořte si pracovní prostor.]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Create a machine learning workspace to manage machine learning solutions through the entire data science lifecycle.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_keywords" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[ML, AML, Machine Learning, AI, Artificial Intelligence, Preview, Deep Learning, Analytics, Data, NLP, Natural Language Processing, CNN, Neural Network, Workbench, Train, Notebooks, AutoML, Designer, Computer Vision, Models, Data Science, Classification, Regression, Reinforcement Learning]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[ML, AML, strojové učení, AI, umělá inteligence, náhled, hluboké učení, analýza, data, NLP, zpracování přirozeného jazyka, CNN, neurální síť, Workbench, trénování, Notebooks, AutoML, Designer, Počítačové zpracování obrazu, modely, datové vědy, klasifikace, regrese, posílení učení]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[ML, AML, Machine Learning, AI, Artificial Intelligence, Preview, Deep Learning, Analytics, Data, NLP, Natural Language Processing, CNN, Neural Network, Workbench, Train]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssociatedResource_AppInsights_ChangeBlade_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select an application insights]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Vyberte Application Insights]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssociatedResource_AppInsights_Properties_changeText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Change application insights]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Změnit Application Insights]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssociatedResource_AppInsights_ReactView_ChangeAppInsights_appInsightsLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Application insights]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Application Insights]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssociatedResource_AppInsights_ReactView_ChangeAppInsights_appInsightsUpdatingSpinner" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Application insights updating...]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Aktualizuje se Application Insights…]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssociatedResource_AppInsights_ReactView_ChangeAppInsights_error" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[An error occurred while updating the application insights for this resource.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Při aktualizaci Application Insights pro tento prostředek došlo k chybě.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssociatedResource_AppInsights_ReactView_ChangeAppInsights_errorTitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Error updating application insights]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Chyba při aktualizaci Application Insights]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssociatedResource_AppInsights_ReactView_ChangeAppInsights_footerCancel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Discard]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Zahodit]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssociatedResource_AppInsights_ReactView_ChangeAppInsights_footerSave" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Save]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Uložit]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssociatedResource_AppInsights_ReactView_ChangeAppInsights_notFoundError" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The resource being updated could not be found.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Prostředek, který se aktualizuje, se nepovedlo najít.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssociatedResource_AppInsights_ReactView_ChangeAppInsights_unauthorizedError" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You do not have permission to update the application insights for this resource.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Nemáte oprávnění aktualizovat Application Insights pro tento prostředek.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssociatedResource_AppInsights_ReactView_Dropdown_emptyMessage" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[No application insights resources found]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Nenašly se žádné prostředky Application Insights]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssociatedResource_AppInsights_ReactView_Dropdown_errorLoadingMessage" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Error loading application insights]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Chyba při načítání Application Insights]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssociatedResource_AppInsights_ReactView_Dropdown_noMatchMessage" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[No results matching {0}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Žádné výsledky, které by odpovídaly: {0}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssociatedResource_AppInsights_ReactView_Dropdown_placeholder" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select an application insights]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Vyberte Application Insights]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Select an application insights instance]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssociatedResource_ContainerRegistry_ChangeBlade_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select a container registry]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Vybrat registr kontejneru]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssociatedResource_ContainerRegistry_Properties_changeText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Change container registry]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Změnit registr kontejneru]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssociatedResource_ContainerRegistry_ReactView_ChangeContainerRegistry_containerRegistryLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Container registry]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Registr kontejneru]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssociatedResource_ContainerRegistry_ReactView_ChangeContainerRegistry_containerRegistryUpdatingSpinner" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Container registry updating...]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Aktualizuje se registr kontejneru...]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssociatedResource_ContainerRegistry_ReactView_ChangeContainerRegistry_error" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[An error occurred while updating the container registry for this resource.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Při aktualizaci registru kontejneru pro tento prostředek došlo k chybě.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssociatedResource_ContainerRegistry_ReactView_ChangeContainerRegistry_errorTitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Error updating container registry]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Chyba při aktualizaci registru kontejneru]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssociatedResource_ContainerRegistry_ReactView_ChangeContainerRegistry_footerCancel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Discard]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Zahodit]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssociatedResource_ContainerRegistry_ReactView_ChangeContainerRegistry_footerSave" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Save]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Uložit]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssociatedResource_ContainerRegistry_ReactView_ChangeContainerRegistry_notFoundError" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The resource being updated could not be found.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Prostředek, který se aktualizuje, se nepovedlo najít.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssociatedResource_ContainerRegistry_ReactView_ChangeContainerRegistry_unauthorizedError" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You do not have permission to update the container registry for this resource.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Nemáte oprávnění k aktualizaci registru kontejneru pro tento prostředek.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssociatedResource_ContainerRegistry_ReactView_Dropdown_emptyMessage" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[No container registry resources found]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Nenašly se žádné prostředky registru kontejneru.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssociatedResource_ContainerRegistry_ReactView_Dropdown_errorLoadingMessage" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Error loading container registry]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Chyba při načítání registru kontejneru]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssociatedResource_ContainerRegistry_ReactView_Dropdown_noMatchMessage" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[No results matching {0}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Žádné výsledky, které by odpovídaly: {0}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssociatedResource_ContainerRegistry_ReactView_Dropdown_placeholder" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select a container registry]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Vybrat registr kontejneru]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Select an container registry instance]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssociatedResource_Dropdown_SettingsBlade_discardButtonText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Discard]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Zahodit]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssociatedResource_Dropdown_SettingsBlade_saveButtonText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Save]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Uložit]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssociatedResource_Dropdown_createNewLinkText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create new]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Vytvořit nový]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssociatedResource_Dropdown_filterPlaceholder" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select existing...]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Vybrat existující...]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssociatedResource_Dropdown_newResourceText" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[(new) {0}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[(nový) {0}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssociatedResource_Dropdown_none" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[None]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Žádný]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BasicsTab_AIServiceSectionHeader" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure AI services base models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Základní modely Azure AI Služeb]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BasicsTab_HubName_BalloonContent" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Name of the AI hub]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Název centra AI]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BasicsTab_InfoText_AdditionalResourceInfoText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[For your convenience, these resources are added automatically to the workspace, if regionally available: ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Tyto prostředky se pro vás do pracovního prostoru přidávají automaticky, pokud jsou k dispozici v dané oblasti: ]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BasicsTab_InfoText_AzureApplicationInsights" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure Application Insights]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure Application Insights]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BasicsTab_InfoText_AzureKeyVault" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure Key Vault]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure Key Vault]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BasicsTab_InfoText_AzureStorage" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure Storage]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure Storage]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BasicsTab_Organization" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Organization]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Organizace]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BasicsTab_Region_BalloonContent" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Compute targets can only be created in the same region as the workspace. Ensure the selected region has the virtual machine series needed for your workspace compute targets.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Cílové výpočetní objekty se dají vytvořit jen ve stejné oblasti, v jaké se nachází pracovní prostor. Ujistěte se, že vybraná oblast má řady virtuálních počítačů potřebné pro cílové výpočetní objekty pracovního prostoru.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BasicsTab_Region_LearnMoreComputeTargets" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more about compute targets]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Další informace o cílových výpočetních objektech]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BasicsTab_Region_ViewAvailableVirtualMachines" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[View available virtual machines series by region]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Zobrazit řady virtuálních počítačů dostupné podle oblasti]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BasicsTab_RegistryDetails" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Registry details]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Podrobnosti o registru]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BasicsTab_RegistryName_BalloonContent" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The registry name must be unique within your resource group.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Název registru musí být ve skupině prostředků jedinečný.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BasicsTab_ResourceGroup_BalloonContent" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A resource group is a collection of resources that share the same life cycle, permissions, and policies.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Skupina prostředků je kolekce prostředků, které sdílejí stejný životní cyklus, oprávnění a zásady.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BasicsTab_Subscription_BalloonContent" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[All resources in an Azure subscription are billed together.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Všechny prostředky v předplatném Azure se fakturují dohromady.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BasicsTab_WarningText_WorkspaceErrorMessageText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The selected subscription doesn’t have permissions to register the resource provider.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Vybrané předplatné nemá oprávnění k registraci tohoto poskytovatele prostředků.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BasicsTab_WorkspaceDetails" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Workspace details]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Podrobnosti pracovního prostoru]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BasicsTab_WorkspaceEdition_Basic" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Basic]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Basic]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BasicsTab_WorkspaceEdition_Enterprise" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Enterprise]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Enterprise]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BasicsTab_WorkspaceEdition_ViewFullPricingDetails" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[View full pricing details]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Zobrazit všechny podrobnosti o cenách]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BasicsTab_WorkspaceHubDetails" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Resource details]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Podrobnosti o prostředku]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Azure AI details]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BasicsTab_WorkspaceName_BalloonContent" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The workspace name must be unique within your resource group.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Název pracovního prostoru musí být ve skupině prostředků jedinečný.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Browse_addMachineLearningHubLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[New Azure AI hub]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Nové centrum Azure AI]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[New Azure AI]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Browse_addMachineLearningLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[New workspace]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Nový pracovní prostor]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Browse_addMachineLearningRegistryLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[New registry]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Nový registr]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ColumnCreationTime" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Created on]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Vytvořené]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ColumnMachineLearningWorkspaceId" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Workspace ID]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[ID pracovního prostoru]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CommandCancel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Cancel]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Zrušit]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CommandDelete" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Delete]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Odstranit]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CommandDownloadConfig" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Download config.json]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Stáhnout config.json]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CommandDownloadConfigTooltip" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Use this file to load the workspace configuration in your Azure ML SDK notebook or Python script]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Tento soubor použijte k načtení konfigurace pracovního prostoru v poznámkovém bloku Azure ML SDK nebo skriptu Python]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Commands_delete" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Delete]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Odstranit]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ConfirmationMachineLearningDelete" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Are you sure you want to delete Workspace {0}?]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Opravdu chcete odstranit pracovní prostor {0}?]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ConfirmationMachineLearningRegistryDelete" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Are you sure you want to delete Registry {0}?]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Určitě chcete odstranit registr {0}?]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ContainerRegistry_Dropdown_SettingsBlade_Name_infoDescription" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The name must meet the following requirements:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Název musí splňovat tyto požadavky:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ContainerRegistry_Dropdown_SettingsBlade_Name_infoItem1" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Unique across all container registries in Azure]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Jedinečné ve všech registrech kontejnerů v Azure]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ContainerRegistry_Dropdown_SettingsBlade_Name_infoItem2" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Between 5 and 50 characters long]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Délka mezi 5 a 50 znaky]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ContainerRegistry_Dropdown_SettingsBlade_Name_infoItem3" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Only contain alphanumeric characters]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Obsahuje jen alfanumerické znaky.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ContainerRegistry_Dropdown_SettingsBlade_Sku_info" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[All SKUs provide the same programmatic capabilities. Choosing a higher SKU will provide more performance and scale.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Všechny skladové položky nabízejí stejné programové funkce. Volba vyšší skladové položky zajistí vyšší výkon a škálu.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ContainerRegistry_Dropdown_SettingsBlade_Sku_infoLearnMore" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Další informace]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ContainerRegistry_Dropdown_SettingsBlade_nameInvalidMessage" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Resource names may contain alpha numeric characters only and must be between 5 and 50 characters.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Názvy prostředků můžou obsahovat jen alfanumerické znaky a musí mít od 5 do 50 znaků.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ContainerRegistry_Dropdown_SettingsBlade_nameLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Název]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ContainerRegistry_Dropdown_SettingsBlade_skuBasic" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Basic]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Basic]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ContainerRegistry_Dropdown_SettingsBlade_skuLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[SKU]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Skladová položka]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ContainerRegistry_Dropdown_SettingsBlade_skuPremium" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Premium]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Premium]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ContainerRegistry_Dropdown_SettingsBlade_skuStandard" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Standard]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Standard]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ContainerRegistry_Dropdown_SettingsBlade_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create new container registry]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Vytvořit nový registr kontejneru]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ContainerRegistry_Dropdown_createNewAriaLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create new container registry]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Vytvořit nový registr kontejneru]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ContainerRegistry_Dropdown_info" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A container registry is used to register docker images used in training and deployments. To minimize costs, a new Azure Container Registry resource is created only after you build your first image. Alternatively, you may choose to create the resource now or select an existing one in your subscription.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Registr kontejneru se používá k registraci imagí Dockeru, které se používají při cvičení a nasazeních. Aby se minimalizovaly náklady, nový prostředek Azure Container Registry se vytváří až po vytvoření první image. Kromě toho se můžete rozhodnout vytvořit nový prostředek hned, nebo vybrat nějaký existující prostředek v předplatném.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ContainerRegistry_Dropdown_label" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Container registry]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Registr kontejneru]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ContainerRegistry_SummaryTab_label" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Container registry]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Registr kontejneru]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBladeCreateButtonName" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Vytvořit]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBladeDescriptionLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Description]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Popis]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBladeFriendlyNameInfo" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Display name of the AI hub that will be displayed in AI studio]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Zobrazovaný název centra AI, který se zobrazí v AI Studiu]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBladeFriendlyNameLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Friendly name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Popisný název]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBladeHubAIServices_ariaLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Connect AI Services, including OpenAI]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Připojení služeb AI, včetně OpenAI]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Create new a Azure OpenAI service]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBladeHubAIServices_info" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Provider of Microsoft-maintained base models. Managed in your Azure subscription as a separate resource.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Poskytovatel základních modelů spravovaných Microsoftem. Spravuje se ve vašem předplatném Azure jako samostatný prostředek.]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Select an existing Azure AI Services instance. This resource type provides endpoints to pre-built AI models including Speech, Content Safety and Azure OpenAI.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBladeHubAIServices_label" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Connect AI Services incl. OpenAI]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Připojení služeb AI, vč. OpenAI]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Azure OpenAI service]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBladeHubAIServices_skipText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Skip connecting AI services]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Přeskočit připojení služeb AI]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBladeHubConfigDefaultResourceGroup_info" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[When a user does not specify a resource group for their project, this resource group will be used by default. The project creator gets granted an Azure RBAC owner role assignment on the project instance]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Pokud uživatel pro svůj projekt nezadá skupinu prostředků, použije se tato skupina prostředků ve výchozím nastavení. Tvůrci projektu se v instanci projektu udělí přiřazení role vlastníka Azure RBAC (řízení přístupu na základě role).]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBladeHubConfigDefaultResourceGroup_label" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Default project resource group]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Výchozí skupina prostředků projektu]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBladeHubSubtitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create an Azure AI hub resource]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Vytvoření prostředku centra Azure AI]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Create an Azure AI hub]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBladeHubTitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure AI hub]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Centrum Azure AI]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Azure AI]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBladeLocationLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Location]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Umístění]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBladeMainTabTitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Main]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Hlavní]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBladeRegistryDescriptionKey" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[description]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[popis]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBladeRegistryDescriptionLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Description]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Popis]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBladeRegistryNameLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Název]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBladeRegistrySubtitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create a machine learning registry]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Vytvořit registr strojového učení]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBladeResourceGroupLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Resource Group]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Skupina prostředků]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBladeReviewCreateButtonName" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Review + Create]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Zkontrolovat a vytvořit]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBladeReviewTabTitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Review]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Zkontrolovat]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBladeSubscriptionIdLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Subscription Id]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[ID předplatného]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBladeSubscriptionLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Subscription]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Předplatné]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBladeSubtitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create a machine learning workspace]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Vytvořit pracovní prostor Machine Learning]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Create]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBladeTagTabTitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Tags]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Značky]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBladeTemplateErrorText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Template Error]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Chyba šablony]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBladeTitle" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure Machine Learning]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Machine Learning]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Machine learning]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBladeWorkspaceCreationProgressNotification" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Workspace creation in progress]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Vytváří se pracovní prostor]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBladeWorkspaceDeploymentErrorText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Workspace deployment Error]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Chyba nasazení pracovního prostoru]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBladeWorkspaceNameLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Název]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Workspace name]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Advanced_Review_Encryption_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Encryption]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Šifrování]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Advanced_Review_Identity_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Identity]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Identita]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Advanced_Review_Resources_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Resources]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Prostředky]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Advanced_Review_byoCosmosText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Cosmos DB for customer managed keys]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Cosmos DB pro klíče spravované zákazníky]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Advanced_Review_byoSearchText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Search service for customer managed keys]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Search pro klíče spravované zákazníkem]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Advanced_Review_byoStorageText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Storage account for customer managed keys]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Účet úložiště pro klíče spravované zákazníkem]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Advanced_Review_byoValueFormat" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{0} ({1})]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{0} ({1})]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Advanced_Review_credentialBasedStorageAccountAccess" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Credential-based]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Na základě přihlašovacích údajů]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Advanced_Review_customerManagedKeys" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Customer-managed keys]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Klíče spravované zákazníkem]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Advanced_Review_disabled" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Disabled]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Zakázáno]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Advanced_Review_enableHBIFlag" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Enable HBI Flag]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Povolit příznak HBI]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Advanced_Review_enabled" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Enabled]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Povoleno]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Advanced_Review_encryptionType" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Encryption type]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Typ šifrování]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Advanced_Review_identityBasedStorageAccountAccess" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Identity-based]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Na základě identity]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Advanced_Review_keyURI" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Key URI]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Identifikátor URI klíče]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Advanced_Review_keyVault" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Key vault]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Trezor klíčů]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Advanced_Review_microsoftManagedKeys" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Microsoft-managed keys]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Klíče spravované Microsoftem]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Advanced_Review_sharedKeyAccess" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Shared key access]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Přístup pomocí sdíleného klíče]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Advanced_Review_storageAccountAccessType" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Storage account access type]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Typ přístupu účtu úložiště]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_ConnectivityMethod_allNetworks" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Enable public access from all networks]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Povolit veřejný přístup ze všech sítí]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Public endpoint (all networks)]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_ConnectivityMethod_private" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Disable public access and use private endpoints]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Zakázat veřejný přístup a použít soukromé koncové body]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Private endpoint]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_ConnectivityMethod_skuPrivateEndpointErrorMessage" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Private with Internet Outbound and Private with Approved Outbound requires premium SKU container registry for having a private endpoint connection. You can create a new premium container registry or select an existing premium container registry in the Basics tab to use with this private workspace.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Privátní s odchozími přenosy a privátní se schváleným odchozími přenosy vyžaduje pro připojení privátního koncového bodu registr kontejneru se skladovou položkou Premium. Můžete vytvořit nový registr kontejneru úrovně Premium nebo vybrat existující registr kontejneru úrovně Premium na kartě Základy, který chcete použít s tímto privátním pracovním prostorem.]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[This workspace is using a private link endpoint and is only compatible with premium SKU container registries. You can create a new premium container registry or select an existing premium container registry in the Basics tab to use with this private workspace.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_Hub_networkIsolationDescription" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Projects associated to an Azure AI hub share network, and can access resources in your virtual network without additional configuration. Choose the type of network isolation you need, from not isolated at all to an entirely separate virtual network managed by Azure Machine Learning.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Projekty přidružené k centru Azure AI sdílejí síť a mají přístup k prostředkům ve vaší virtuální síti bez další konfigurace. Zvolte typ izolace sítě, kterou potřebujete, od vůbec neizolované po zcela samostatnou virtuální síť spravovanou Azure Machine Learning.]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Projects associated to an Azure AI resource share network, and can access resources in your virtual network without additional configuration. Choose the type of network isolation you need, from not isolated at all to an entirely separate virtual network managed by Azure Machine Learning.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_NetworkIsolation_AllowInternetOutbound_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Allow Internet Outbound]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Povolit odchozí připojení k internetu]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_NetworkIsolation_AllowOnlyApprovedOutbound_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Allow Only Approved Outbound]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Povolit pouze schválené odchozí]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_NetworkIsolation_Disabled_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Disabled]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Zakázáno]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_NetworkIsolation_PrivateApprovedOutbound_descriptionItemOne" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Workspace is accessed via private endpoint]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[K pracovnímu prostoru se přistupuje přes privátní koncový bod.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_NetworkIsolation_PrivateApprovedOutbound_descriptionItemThree" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Outbound data movement is restricted to approved targets]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Přesun odchozích dat je omezený na schválené cíle.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_NetworkIsolation_PrivateApprovedOutbound_descriptionItemTwo" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Compute can access allowlisted resources only]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Výpočetní prostředky můžou přistupovat jen k povoleným prostředkům]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_NetworkIsolation_PrivateApprovedOutbound_learnMore" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more about data exfiltration protection]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Další informace o ochraně před exfiltrací dat]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_NetworkIsolation_PrivateApprovedOutbound_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Private with Approved Outbound]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Soukromá se schváleným odchozím]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_NetworkIsolation_PrivateInternetOutbound_descriptionItemOne" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Workspace is accessed via private endpoint]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[K pracovnímu prostoru se přistupuje přes privátní koncový bod.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_NetworkIsolation_PrivateInternetOutbound_descriptionItemThree" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Outbound data movement is unrestricted]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Přesun odchozích dat je neomezený.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_NetworkIsolation_PrivateInternetOutbound_descriptionItemTwo" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Compute can access private resources]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Výpočetní prostředky mají přístup k privátním prostředkům]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_NetworkIsolation_PrivateInternetOutbound_learnMore" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more about private networks]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Další informace o privátních sítích]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_NetworkIsolation_PrivateInternetOutbound_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Private with Internet Outbound]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Privátní s odchozím internetem]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_NetworkIsolation_Public_descriptionItemOne" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Workspace is accessed via public endpoint]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[K pracovnímu prostoru se přistupuje přes veřejný koncový bod.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_NetworkIsolation_Public_descriptionItemThree" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Outbound data movement is unrestricted]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Přesun odchozích dat je neomezený.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_NetworkIsolation_Public_descriptionItemTwo" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Compute can access public resources]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Výpočetní prostředky můžou přistupovat k veřejným prostředkům]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_NetworkIsolation_Public_learnMore" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more about public networks]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Další informace o veřejných sítích]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_NetworkIsolation_Public_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Public]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Veřejné]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_NetworkIsolation_skuErrorMessage" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Allow Internet Outbound and Allow Only Approved Outbound requires premium SKU container registry for having a private endpoint connection.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Aby bylo možné povolit odchozí přenosy a povolit jen schválené odchozí přenosy, vyžaduje registr kontejneru se skladovou položkou Premium, aby bylo možné připojení privátního koncového bodu.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_PrivateEndpoints_add" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Add]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Přidat]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_PrivateEndpoints_clickToAdd" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Click on add to create a private endpoint]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Pokud chcete vytvořit privátní koncový bod, klikněte na přidat]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_PrivateEndpoints_locationMismatch" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Workspace ({0}) and private endpoint connection ({1}) must be in the same location in order to get compute instance and clusters accessed properly in the virtual network.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Pracovní prostor ({0}) a připojení privátního koncového bodu ({1}) musí být ve stejném umístění, aby bylo možné ve virtuální síti správně přistupovat k instanci výpočtu a clusterům.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_PrivateEndpoints_name" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Název]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_PrivateEndpoints_nameAndSubResource" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{0} ({1})]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{0} ({1})]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_PrivateEndpoints_nameSubResourceAndDns" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{0} ({1}) ({2})]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{0} ({1}) ({2})]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_PrivateEndpoints_noContent" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[-]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[-]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_PrivateEndpoints_registrySubResourceHelp" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This is the specific sub-resource for the new registry that this private endpoint will be able to access.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Toto je konkrétní dílčí prostředek pro nový registr, ke kterému bude mít tento privátní koncový bod přístup.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_PrivateEndpoints_registrySubResourceLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Registry sub-resource]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Dílčí prostředek registru]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_PrivateEndpoints_subResourceHelp" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This is the specific sub-resource for the new workspace that this private endpoint will be able to access.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Toto je konkrétní dílčí prostředek pro nový pracovní prostor, ke kterému bude mít tento privátní koncový bod přístup.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_PrivateEndpoints_subResourceLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Workspace sub-resource]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Dílčí prostředek pracovního prostoru]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_PrivateEndpoints_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Private endpoints]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Privátní koncové body]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_PrivateEndpoints_type" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Private Endpoint]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Privátní koncový bod]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_WorkspaceApprovedOutbound_OutboundAccess_recommendedTargetDescription" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[There are several optional outbound targets recommended for your workspace in scenarios such as AutoML and Data Labeling. You can modify or delete them,]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Pro váš pracovní prostor se doporučuje několik volitelných odchozích cílů ve scénářích, jako jsou AutoML a Popisky dat. Můžete je upravit nebo odstranit,]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_WorkspaceApprovedOutbound_OutboundAccess_recommendedTargetLearnMore" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more about recommended targets]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Další informace o doporučených cílech]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_WorkspaceApprovedOutbound_OutboundAccess_requiredTargetDescription" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[There are a few outbound targets added by Azure Machine Learning that are required for your workspace to access things like storage,  notebooks, and deployment environments.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[K dispozici je několik odchozích cílů přidaných Azure Machine Learning, které jsou potřeba pro přístup k pracovnímu prostoru, jako jsou úložiště, poznámkové bloky a prostředí nasazení.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_WorkspaceApprovedOutbound_OutboundAccess_requiredTargetLearnMore" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more about required targets]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Další informace o požadovaných cílech]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_WorkspaceInternetOutbound_OutboundAccess_useMyOwnVirtualNetwork" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Use my own virtual network]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Použít vlastní virtuální síť]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_WorkspaceInternetOutbound_OutboundAccess_virtualNetworkManagedByAML" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Use a virtual network managed by Azure Machine Learning]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Použití virtuální sítě spravované Azure Machine Learning]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_WorkspaceInternetOutbound_OutboundAccess_virtualNetworkManagedByAMLDescription" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Private endpoints are required for your workspace to access private resources like storage, notebooks, and deployment environments. You can also add your additional private link targets here for your custom scenarios.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Pro přístup k privátním prostředkům, jako jsou úložiště, poznámkové bloky a prostředí nasazení, se vyžadují privátní koncové body. Můžete sem také přidat další cíle privátních propojení pro vlastní scénáře.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_WorkspaceInternetOutbound_OutboundAccess_virtualNetworkManagedByAMLLearnMore" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more about required private link targets]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Další informace o požadovaných cílech privátních propojení]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_WorkspaceInternetOutbound_PrivateNetworkSettings_description" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Add private endpoints people can use to access your workspace, and choose how to manage outbound access from your workspace to things like Storage Accounts, Key Vaults and Registries.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Přidejte privátní koncové body, které uživatelé můžou používat pro přístup k vašemu pracovnímu prostoru, a zvolte, jak spravovat odchozí přístup z pracovního prostoru k věcem, jako jsou účty úložiště, trezory klíčů a registry.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_WorkspaceInternetOutbound_PrivateNetworkSettings_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Private network settings]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Nastavení privátní sítě]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_WorkspaceInternetOutbound_targetDescription" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[There are a few private endpoints required for your workspace to access private resources like storage. You can also add your additional private link targets here for your custom scenarios.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Aby měl váš pracovní prostor přístup k privátním prostředkům, jako je například úložiště, je vyžadováno několik privátních koncových bodů. Můžete sem také přidat další cíle privátních propojení pro vlastní scénáře.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_WorkspaceInternetOutbound_targetLearnMoreLink" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more about required private link target]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Další informace o požadovaném cíli privátního propojení]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_WorkspaceOutboundAccess_activeStatusMessage" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The rule has been applied and effective.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Pravidlo bylo použito a bylo účinné.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_WorkspaceOutboundAccess_addUserDefinedOutboundRules" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Add user-defined outbound rules]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Přidání uživatelem definovaných pravidel odchozích přenosů]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_WorkspaceOutboundAccess_connectionName" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Connection Name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Název připojení]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_WorkspaceOutboundAccess_deleteUserDefinedOutboundRules" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Delete user-defined outbound rules]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Odstranit uživatelem definovaná odchozí pravidla]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_WorkspaceOutboundAccess_destination" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Destination]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Cíl]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_WorkspaceOutboundAccess_destinationType" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Destination Type]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Cílový typ]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_WorkspaceOutboundAccess_inactivePrivateEndpointStatusMessage" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The rule will become active when managed network is provisioned, otherwise please check if approval is pending for the target resource.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Pravidlo se aktivuje po zřízení spravované sítě, jinak zkontrolujte, jestli čeká na schválení cílového prostředku.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_WorkspaceOutboundAccess_inactiveStatusMessage" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The rule will become active when managed network is provisioned.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Když se zřídí spravovaná síť, pravidlo se aktivuje.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_WorkspaceOutboundAccess_parentRules" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Parent Rules]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Nadřazená pravidla]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_WorkspaceOutboundAccess_status" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Status]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Stav]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_WorkspacePrivateOutbound_addUserDefinedOutboundRuleText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You can also add your own outbound targets here for your custom scenarios.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Můžete sem také přidat vlastní odchozí cíle pro vlastní scénáře.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_WorkspacePrivateOutbound_dependencyOutboundRules" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Dependency outbound rules]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Pravidla odchozích přenosů závislostí]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_WorkspacePrivateOutbound_recommendedOutboundRules" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Recommended outbound rules]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Doporučená odchozí pravidla]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_WorkspacePrivateOutbound_requiredOutboundRules" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Required outbound rules]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Požadovaná odchozí pravidla]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_WorkspacePrivateOutbound_userDefinedOutboundRules" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[User-defined outbound rules]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Uživatelem definovaná odchozí pravidla]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_connectivityMethod" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Connectivity method]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Metoda připojení]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_displayPrivateDnsZone" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Private DNS Zone]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Zóna Privátního DNS]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_displayRegion" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Region]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Oblast]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_displayResourceGroup" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Resource group]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Skupina prostředků]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_displaySubnet" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Subnet]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Podsíť]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_displaySubscription" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Subscription]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Předplatné]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_displayTargetResourceType" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Target resource type]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Typ cílového prostředku]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_networkIsolationDescription" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Choose the type of network isolation you need for your workspace, from not isolated at all to an entirely separate virtual network managed by Azure Machine Learning.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Zvolte typ izolace sítě, kterou potřebujete pro svůj pracovní prostor, od neizolované vůbec po zcela samostatnou virtuální síť spravovanou Azure Machine Learning.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_networkIsolationLearnMore" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more about managed network isolation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Další informace o izolaci spravované sítě]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_networkIsolationTitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Network isolation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Izolace sítě]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_outboundRulesGridEmptyMessage" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Missing required outbound access rules.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Chybí požadovaná pravidla odchozího přístupu.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_outboundRulesNotLoadedMessage" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Configure basic settings in order to create outbound rules.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Pokud chcete vytvořit odchozí pravidla, nakonfigurujte základní nastavení.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_outboundRulesPublicInternetEnabledMessage" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[No required outbound rules while the outbound access to the public internet is enabled.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Pokud je povolený odchozí přístup k veřejnému internetu, nejsou nutná žádná odchozí pravidla.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_overviewDescription" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You can connect to your workspace either publicly or privately using a private endpoint.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Ke svému pracovnímu prostoru se můžete připojit buď veřejně, nebo privátně pomocí privátního koncového bodu.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_overviewTitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Network connectivity]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Připojení k síti]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_privateEndpointDescription" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create a private endpoint to allow a private connection to this resource.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Vytvořte privátní koncový bod a povolíte privátní připojení k tomuto prostředku.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_privateEndpointTitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Private endpoint]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Privátní koncový bod]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_registryOverviewDescription" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You can connect to your registry either publicly or privately using a private endpoint.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[K registru se můžete připojit buď veřejně, nebo privátně pomocí privátního koncového bodu.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Networking]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Sítě]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_workspaceApprovedOutbound_PrivateNetworkSettings_description" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Add private endpoints people can use to access your workspace, and manage the outbound targets to which the workspace can access]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Přidání privátních koncových bodů, pomocí kterých můžou uživatelé přistupovat k vašemu pracovnímu prostoru a spravovat odchozí cíle, ke kterým má pracovní prostor přístup]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_workspaceApprovedOutbound_PrivateNetworkSettings_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Private network and data exfiltration settings]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Nastavení exfiltrace privátní sítě a dat]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_workspaceInboundAccessTitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Workspace Inbound access]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Příchozí přístup k pracovnímu prostoru]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_workspaceOutboundAccessTitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Workspace Outbound access]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Odchozí přístup pracovního prostoru]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Workspace_Type" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Workspce type]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Typ pracovního prostoru]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Create_CreateButton_label" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Vytvořit]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Create_Wizard_title" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create Azure Machine Learning]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Vytvořit Azure Machine Learning]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_QuotaConfigurationBlade_filterText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Enter at least 3 characters to search for workspaces...]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Zadejte nejméně 3 znaky pro vyhledání pracovních prostorů...]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_QuotaConfigurationBlade_noItemsText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[No workspaces found to display.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Nenašly se žádné pracovní prostory, které by bylo možné zobrazit.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_QuotaConfigurationBlade_quotaConfigNotAvailable" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Quotas cannot be configured due to following error: {0}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Kvóty nelze nakonfigurovat kvůli následující chybě: {0}.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_QuotaConfigurationBlade_subTitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Configure your quota across your subscription here]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Tady nakonfigurujte kvótu v rámci svého předplatného]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_QuotaConfigurationBlade_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Configure quota]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Nakonfigurovat kvótu]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_QuotaConfigurationBlade_workspacesNotAvailable" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You can only configure quota for workspaces which have been configured before since the list of workspaces of this subscription cannot be reached due to following error: {0}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Kvóta se dá nakonfigurovat jen pro pracovní prostory, které už jsou nakonfigurované, protože kvůli následující chybě nelze získat seznam pracovních prostorů tohoto předplatného: {0}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_QuotaUsageBlade_crossLocationUsagesNotAvailable" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Cross location workspace information (whether the workspace owning a compute is in a different location than the selected location) cannot be displayed due to following error but you can still see all the usages: {0}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Informace o pracovním prostoru napříč lokalitami (jestli je pracovní prostor vlastnící výpočetní prostředek v jiném než vybraném umístění) nelze zobrazit kvůli následující chybě, ale i tak si můžete zobrazit všechna využití: {0}.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_QuotaUsageBlade_quotaInfoLearnMore" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Další informace]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_QuotaUsageBlade_quotaInfoText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure Machine Learning quota is preconfigured limits, which specifies the maximum number of cores you can use at any given moment.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Kvóta Azure Machine Learning je předem nakonfigurovaná limity, které určují maximální počet jader, která můžete v daném okamžiku použít.]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Azure Machine Learning quota is preconfigured limits, which specifies the maximum number of cores you can use at any given moment. This summary shows the quota usage and limits across all workspaces in your subscription.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_QuotaUsageBlade_usagesNotAvailable" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Compute usages cannot be displayed do to following error: {0}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Využití výpočetních prostředků nelze zobrazit kvůli následující chybě: {0}.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_UsageGrid_coreUtilizationColumnText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Cores utilization]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Využití jader]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_UsageGrid_coresUsageTextFormat" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{0} of {1} cores utilized]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{0} z {1} využitých jader]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_UsageGrid_crossLocationEnableText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Enable cross location compute toggle]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Povolením přepínače pro výpočetní prostředky napříč lokalitami]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_UsageGrid_crossLocationWarningCoresFormat" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{0} cores]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[počet jader: {0}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_UsageGrid_crossLocationWarningLine1" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You are using {0} in other locations.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[V jiných umístěních používáte: {0}.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_UsageGrid_crossLocationWarningLine2" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{1} to view them.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{1} je zobrazíte.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_UsageGrid_familyNameFormat" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Standard {0} Family vCPUs]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Jednotky vCPU standardní řady {0}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_UsageGrid_totalCoresTextFormat" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{0} out of {1} ({2})]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{0} z {1} ({2})]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_UsageGrid_usageColumnText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Usage]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Využití]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_UsageGrid_usagePercentageColumnText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Usage percentage]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Procento využití]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_UsageGrid_vmFamiliesColumnText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[VM Families]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Řady virtuálních počítačů]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_UsageGrid_workspaceCrossLocationDisplayText" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{0} ({1}, {2})]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{0} ({1}, {2})]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_UsageGrid_workspaceDisplayText" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{0} ({1})]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{0} ({1})]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_UsageGrid_workspacesColumnText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Workspaces]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Pracovní prostory]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_configureQuotaButtonText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Configure quota]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Nakonfigurovat kvótu]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_dedicatedCoresSectionLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Dedicated cores usage]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Využití vyhrazených jader]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_loadingText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Loading...]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Načítání...]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_lowPriorityCoresSectionLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Low priority cores usage]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Využití jader s nízkou prioritou]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_lowPriorityUsageInfoText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Please note that the number of low-priority cores per subscription is single value accross VM families.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Mějte prosím na paměti, že počet jader s nízkou prioritou v daném předplatném představuje jedinou hodnotu napříč řadami virtuálních počítačů.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_quotaUpdateFailed" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Quota update failed]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Aktualizace kvóty se nezdařila.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_quotaUpdateSucceeded" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Quota successfully updated]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Kvóta se úspěšně aktualizovala.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_requestBladeFamilyNameFormat" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{0} Series]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Řada {0}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_requestQuotaButtonText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Request quota increase]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Požádat o navýšení kvóty]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_subscriptionViewText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Subscription view]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Zobrazení předplatného]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_toggleText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Show workspaces across all locations (cross-location compute)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Zobrazit pracovní prostory napříč všemi umístěními (výpočetní prostředky napříč lokalitami)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_totalClustersLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Cluster quota:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Kvóta clusteru:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_totalClustersTextFormat" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{0} clusters and CIs used | {1} remaining]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Počet využitých clusterů a CI: {0} | Zbývá: {1}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_totalCoresTextFormat" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{0} cores used | {1} cores remaining]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Počet využitých jader: {0} | Počet zbývajících jader: {1}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_totalDedicatedCoresLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Dedicated quota:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Vyhrazená kvóta:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_totalLowPriorityCoresLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Low priority quota:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Kvóta pro nízkou prioritu:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_workspaceViewText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Workspace view]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Zobrazení pracovního prostoru]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Description_CreateAzureMachineLearningRegistryResource" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[For sharing ML assets across workspaces]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Pro sdílení prostředků ML mezi pracovními prostory]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Description_CreateAzureMachineLearningResource" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[For ML projects and teams]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Pro projekty a týmy ML]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Description_createAzureMachineLearningHubResource" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Centrally configure Azure AI hubs]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Centrální konfigurace center Azure AI]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Centrally configure Azure AI resources]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";EncryptionSettings_Command_Discard" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Discard]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Zahodit]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";EncryptionSettings_Command_DismissAriaLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Close]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Zavřít]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";EncryptionSettings_Command_ResultError" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Error updating key]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Chyba při aktualizaci klíče]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";EncryptionSettings_Command_ResultLoading" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Updating...]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Aktualizuji...]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";EncryptionSettings_Command_ResultSuccess" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Success]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Povedlo se]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";EncryptionSettings_Command_Save" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Save]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Uložit]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";EncryptionSettings_Command_SeeMore" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[See more]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Zobrazit více]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";EncryptionSettings_DirtyFormWarning" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You have unsaved changes.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Máte neuložené změny.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";EncryptionSettings_Header1_Replacement1" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[data encryption]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[šifrování dat]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";EncryptionSettings_Header1_Replacement2" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[customer-managed key encryption]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Šifrování klíčů spravovaných zákazníkem]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";EncryptionSettings_Header1_Template" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Your data is encrypted by default using Microsoft-managed keys. For additional control over your data, you may choose to bring your own key for encryption. To learn more, see {} and {}.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Vaše data se ve výchozím nastavení šifrují pomocí klíčů spravovaných Microsoftem. Pokud chcete mít nad svými daty větší kontrolu, můžete použít vlastní klíč pro šifrování. Další informace najdete v tématech {} a {}.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";EncryptionSettings_Header2" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[After a workspace is deployed, you can rotate the encryption key but not change the encryption type from Microsoft-managed key to Customer-managed key.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Po nasazení pracovního prostoru můžete šifrovací klíč otočit, ale nemůžete změnit typ šifrování z klíče spravovaného Microsoftem na klíč spravovaný zákazníkem.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";EncryptionSettings_SelectedKey_KeyLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Key]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Klíč]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";EncryptionSettings_SelectedKey_Title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Selected key vault and key]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Vybraný trezor klíčů a klíč]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";EncryptionSettings_SelectedKey_VaultLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[KeyVault]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[KeyVault]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";EncryptionSettings_SelectedKey_VersionLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Version]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Verze]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";EncryptionSettings_SelectionSection_Title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Encryption selection]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Výběr šifrování]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";EncryptionSettings_SelectionSection_TypeLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Encryption type]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Typ šifrování]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";EncryptionSettings_SelectionSection_VaultAndKeyLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Key vault and key]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Trezor klíčů a klíč]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";EncryptionSettings_SelectionSection_VaultAndKeySelect" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select a key vault and key]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Vybrat trezor klíčů a klíč]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";EncryptionSettings_Title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Encryption]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Šifrování]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";EncryptionTab_Title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Encryption]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Šifrování]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ErrorJsonParsingException" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[An unexpected parsing error occurred.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Došlo k neočekávané chybě parsování.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";HubConfigDefaultResourceGroup_SummaryTab_label" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Default project resource group]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Výchozí skupina prostředků projektu]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Hub_Projects_LoadFailure" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]A;      Associated projects of Azure AI hub with ID "{0}" could not be loaded.]A;    ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[]A;      Přidružené projekty centra Azure AI s ID „{0}“ se nepovedlo načíst.]A;    ]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]A;      Associated projects of Azure AI resource with ID "{0}" could not be loaded.]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Hub_Projects_Title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]A;      Projects]A;    ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[]A;      Projekty]A;    ]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Hub_Projects_Toolbar_Add" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]A;      Add]A;    ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[]A;      Přidat]A;    ]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Hub_Projects_Toolbar_Refresh" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]A;      Refresh]A;    ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[]A;      Aktualizovat]A;    ]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";IdentityTab_Title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Identity]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Identita]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Keyvault_Dropdown_Hub_info" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A key vault is used to store secrets and other sensitive information that is needed by the AI hub. You may create a new Azure Key Vault resource or select an existing one in your subscription.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Trezor klíčů se používá k ukládání tajných kódů a dalších citlivých informací, které centrum AI potřebuje. Můžete vytvořit nový prostředek Azure Key Vault nebo vybrat nějaký existující prostředek v předplatném.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Keyvault_Dropdown_SettingsBlade_Name_infoDescription" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The name must meet the following requirements:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Název musí splňovat tyto požadavky:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Keyvault_Dropdown_SettingsBlade_Name_infoItem1" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Unique across all existing key vaults in Azure]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Jedinečné ve všech existujících trezorech klíčů v Azure]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Keyvault_Dropdown_SettingsBlade_Name_infoItem2" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Between 3 and 24 characters long]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Délka mezi 3 a 24 znaky]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Keyvault_Dropdown_SettingsBlade_Name_infoItem3" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Only contain alphanumeric characters and hyphens]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Obsahuje jen alfanumerické znaky a spojovníky]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Keyvault_Dropdown_SettingsBlade_Name_infoItem4" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Cannot start with a number]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Nemůže začínat číslicí.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Keyvault_Dropdown_SettingsBlade_nameInvalidMessage" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Vault name must be between 3-24 alphanumeric characters. The name must begin with a letter, end with a letter or digit, and not contain consecutive hyphens.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Název trezoru musí mít 3 až 24 abecedních znaků. Název musí začínat písmenem, končit písmenem nebo číslicí a nesmí obsahovat několik pomlček po sobě.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Keyvault_Dropdown_SettingsBlade_nameLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Název]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Keyvault_Dropdown_SettingsBlade_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create new key vault]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Vytvořit nový trezor klíčů]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Keyvault_Dropdown_createNewAriaLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create new key vault]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Vytvořit nový trezor klíčů]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Keyvault_Dropdown_info" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A key vault is used to store secrets and other sensitive information that is needed by the workspace. You may create a new Azure Key Vault resource or select an existing one in your subscription.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Trezor klíčů se používá k ukládání tajných kódů a dalších citlivých informací, které pracovní prostor potřebuje. Můžete vytvořit nový prostředek Azure Key Vault, nebo vybrat nějaký existující prostředek v předplatném.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Keyvault_Dropdown_label" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Key vault]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Trezor klíčů]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Keyvault_SummaryTab_label" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Key vault]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Trezor klíčů]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Keyword_alert" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Alert]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Upozornění]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Keyword_audit" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Audit]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Audit]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Keyword_instance" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Instance]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Instance]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Keyword_log" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Log]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Protokol]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Keyword_rules" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Rules]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Pravidla]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Keyword_scale" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Scale]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Škálovat]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Keyword_scaling" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Scaling]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Škálování]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";LabelAutoMLMenuItem" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Automated machine learning]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Automatizované strojové učení]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";LabelComputeMenuItem" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Compute]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Compute]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";LabelDataMenuItem" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Data (Preview)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Data (náhled)]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Data]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";LabelDeployments" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deployments]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Nasazení]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";LabelDeploymentsMenuItem" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deployments]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Nasazení]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";LabelImagesMenuItem" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Images]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Obrázky]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";LabelInsights" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Application Insights]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Application Insights]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";LabelKeyVault" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Key Vault]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Key Vault]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";LabelMlFlowUri" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[MLflow tracking URI]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Identifikátor URI pro sledování toku MLflow]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";LabelModelsMenuItem" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Modely]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";LabelPipelinesMenuItem" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Pipelines]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Kanály]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";LabelProjectHubResource" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure AI hub]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Centrum Azure AI]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Azure AI]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";LabelProjectsMenuItem" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Experiments]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Experimenty]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";LabelProperties" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Properties]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Vlastnosti]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";LabelRegistry" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Container Registry]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Registr kontejneru]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Registry]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";LabelStorage" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Storage]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Úložiště]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";LabelTasksMenuItem" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Activities]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Aktivity]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";MLAppDeployments_GridColumns_name" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deployment name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Název nasazení]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";MLAppDeployments_GridColumns_status" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Provisioning state]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Stav zřizování]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";MLAppDeployments_GridColumns_traffic" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Traffic %]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Procento provozu]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";MLAppDeployments_Grid_StatusFilter_all" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[All provisioning states]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Všechny stavy zřizování]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";MLAppDeployments_Grid_StatusFilter_itemFormat" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{0} ({1})]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{0} ({1})]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";MLAppDeployments_Grid_StatusFilter_some" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{0} connection states selected]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Počet vybraných stavů připojení: {0}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ManagedIdentities_menuText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Identity]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Identita]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Networking_PrivateEndpointConnections_TabText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Private endpoint connections]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Připojení privátních koncových bodů]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Networking_PublicAccess_AllNetworksInfoText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[All networks, including the internet, can access this resource.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[K tomuto prostředku mají přístup všechny sítě včetně internetu.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Networking_PublicAccess_Description" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Public network access allows access to this resource through the internet using a public IP address. An application or resource that is granted access with the following network rules still requires proper authorization to access this resource. {0}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Přístup k veřejné síti umožňuje přístup k tomuto prostředku přes internet pomocí veřejné IP adresy. Aplikace nebo prostředek, pro které se udělil přístup s následujícími pravidly sítě, stále vyžadují pro přístup k tomuto prostředku správnou autorizaci. {0}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Networking_PublicAccess_DisabledInfoText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[No public network can access this resource.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[K tomuto prostředku nemá přístup žádná veřejná síť.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Networking_PublicAccess_EnabledFromSelectedIpInfoText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Allow access from public IP you specified below.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Povolte přístup z veřejné IP adresy, kterou jste zadali níže.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Networking_PublicAccess_FirewallValidation_duplicateIpAddresses" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Two identical address ranges have been specified. Address ranges must be unique.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Byly zadány dva identické rozsahy adres. Rozsahy adres musí být jedinečné.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Networking_PublicAccess_FirewallValidation_exactPrefix" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The subnet prefix must be equal to {0}.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Předpona podsítě se musí rovnat {0}.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Networking_PublicAccess_FirewallValidation_formattedPrefix" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The prefix must be between {0} and {1}.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Předpona musí být v rozsahu {0} až {1}.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Networking_PublicAccess_FirewallValidation_invalidCIDRBlockWithSuggestion" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{0} is not a valid CIDR block. Use {1}/{2} instead.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{0} není platný blok CIDR. Použijte místo něj {1}/{2}.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Networking_PublicAccess_FirewallValidation_invalidCidr" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Specify an IP address or CIDR.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Zadejte IP adresu nebo rozsah CIDR.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Networking_PublicAccess_FirewallValidation_invalidIPAddress" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Invalid IP address.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Neplatná IP adresa.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Networking_PublicAccess_FirewallValidation_leadingZerosIpAddress" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The octet '{0}' in IP address '{1}' contains a leading zero.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Oktet {0} v IP adrese {1} obsahuje na začátku nulu.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Networking_PublicAccess_FirewallValidation_malformedSubnet" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Malformed address range. Address was {0}.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Chybný rozsah adres. Adresa byla {0}.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Networking_PublicAccess_FirewallValidation_maxPrefix" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The prefix must be smaller than or equal to {0}.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Předpona musí být menší než nebo rovna {0}.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Networking_PublicAccess_FirewallValidation_minPrefix" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The prefix must be greater than or equal to {0}.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Předpona musí být větší než nebo rovna {0}.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Networking_PublicAccess_FirewallValidation_nonNullSubnet" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A non-null address range is required.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Rozsah adres musí být jiný než null.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Networking_PublicAccess_FirewallValidation_octet" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Octet {0} with value {1} is invalid. It must be between {2} and {3}.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Oktet {0} s hodnotou {1} je neplatný. Musí být mezi {2} a {3}.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Networking_PublicAccess_FirewallValidation_publicIpRuleValidation" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[IP rules support public IP addresses only.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Pravidla IP podporují jen veřejné IP adresy.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Networking_PublicAccess_LearnMoreText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Další informace]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Networking_PublicAccess_RadioAllNetworksText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[All networks]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Všechny sítě]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Networking_PublicAccess_RadioDisabledText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Disabled]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Zakázáno]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Networking_PublicAccess_RadioEnabledFromAllNetworks" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Enabled from all networks]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Povoleno ze všech sítí]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Networking_PublicAccess_RadioEnabledFromSelectedIp" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Enabled from selected IP addresses]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Povoleno z vybraných IP adres]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Networking_PublicAccess_RadioLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Public network access]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Přístup k veřejné síti]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Networking_PublicAccess_TabText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Public access]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Veřejný přístup]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Networking_PublicAccess_Toolbar_DiscardChangesText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Discard changes]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Zahodit změny]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Networking_PublicAccess_Toolbar_RefreshText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Refresh]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Aktualizovat]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Networking_PublicAccess_Toolbar_SaveText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Save]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Uložit]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Networking_PublicAccess_Toolbar_SavingText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Saving...]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Probíhá ukládání…]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Networking_PublicAccess_addClientIpAddressInfo" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You may not wish to add your client IP address if the network you are using the azure portal from is atypical (home vs. work environment for example).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Pokud není síť, ze které používáte portál Azure Portal, obvyklá (třeba domácí nebo pracovní prostředí), možná budete chtít nepřidávat IP adresu svého klienta.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Networking_PublicAccess_addClientIpAddressLabel" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Add your client IP address ('{0}')]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Přidat IP adresu klienta ({0})]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Networking_PublicAccess_addressRange" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Address range]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Rozsah adres]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Networking_PublicAccess_addressRangePlaceHolder" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[IP address or CIDR]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[IP adresa nebo CIDR]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Networking_PublicAccess_firewallDescription" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Add IP ranges to allow access from the internet or your on-premises networks.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Přidáním rozsahů IP adres umožníte přístup z internetu nebo místních sítí.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Networking_PublicAccess_firewallHeader" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Firewall]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Brána firewall]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Networking_PublicAccess_firewallLearnMore" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Další informace]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Networking_WorkspaceManagedOutboundAccess_tabText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Workspace managed outbound access]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Odchozí přístup spravovaný pracovním prostorem]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OnlineEndpoints_Create_Dependencies_condaDependenciesFileLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Conda dependencies file]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Soubor závislostí Conda]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OnlineEndpoints_Create_Dependencies_description" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Customize your model deployment into inference app.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Přizpůsobte svůj model nasazení na aplikaci pro vyvozování.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OnlineEndpoints_Create_Dependencies_entryScriptFileLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Entry script file]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Soubor zaváděcího skriptu]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OnlineEndpoints_Create_Dependencies_selectCondaDependenciesFile" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select a conda dependency file]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Vyberte soubor závislostí Conda.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OnlineEndpoints_Create_Dependencies_selectEntryScriptFileMessage" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select an entry script file]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Vyberte soubor vstupního skriptu.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OnlineEndpoints_Create_Dependencies_tabName" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Dependencies]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Závislosti]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OnlineEndpoints_Create_introText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure Machine Learning Inference apps enable you to quickly build, deploy and scale enterprise-grade machine learning models running on any platform. Use any open source machine learning framework like TensorFlow, PyTorch, SciKit-Learn, ONNX and more. Use our “no code deployment” to accelerate your productivity or customize your Inference app with your own docker container and/or model scoring code.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Aplikace pro odvozování Azure Machine Learning umožňují rychle vytvářet, nasazovat a škálovat modely strojového učení na úrovni podniků spuštěné na libovolné platformě. Můžete použít jakékoli opensourcové rozhraní strojového učení, třeba TensorFlow, PyTorch, SciKit-Learn, ONNX a další. Díky našemu nasazování bez kódu můžete urychlit nebo přizpůsobit aplikaci pro odvozování vlastním kontejnerem Docker nebo kódem pro hodnocení modelu.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OnlineEndpoints_Create_modelLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Model]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OnlineEndpoints_Create_modelPlaceholder" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select a model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Vyberte model.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OnlineEndpoints_Create_modelVersionLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model version]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Verze modelu]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OnlineEndpoints_Create_modelVersionPlaceholder" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select a model version]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Výběr verze modelu]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OnlineEndpoints_Create_nameLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Název]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[ML App]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OnlineEndpoints_Create_subTitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create an ML App]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Vytvořit aplikaci ML]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OnlineEndpoints_Create_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Machine learning online endpoint]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Koncový bod strojového učení online]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[ML App]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OnlineEndpoints_Create_workspaceLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Workspace]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Pracovní prostor]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OnlineEndpoints_Create_workspacePlaceholder" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select a workspace]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Vybrat pracovní prostor]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutboundAccessRule_SettingsBlade_FqdnCostInfo_managedNetworkIsolationLinkText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Managed Network Isolation.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Spravovaná izolace sítě]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutboundAccessRule_SettingsBlade_FqdnCostInfo_moreInformationText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[For more information on Azure Firewall, see ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Další informace o Azure Firewall najdete tady: ]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutboundAccessRule_SettingsBlade_FqdnCostInfo_pricingLinkText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Pricing.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Ceny]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutboundAccessRule_SettingsBlade_FqdnCostInfo_text" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[FQDN outbound rules are implemented using Azure Firewall. If you use outbound FQDN rules, charges for Azure Firewall are included in your billing. To learn more about outbound rules, see ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Pravidla odchozích přenosů plně kvalifikovaného názvu domény se implementují pomocí Azure Firewall. Pokud budete používat pravidla odchozích přenosů plně kvalifikovaných názvů domén, budou do fakturace zahrnuty poplatky za Azure Firewall. Další informace o pravidlech odchozích přenosů najdete tady: ]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutboundAccessRule_SettingsBlade_Note" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Note]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Poznámka]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutboundAccessRule_SettingsBlade_amlRegistryPEDependencyRulesWarning" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Dependency outbound rules will be created for all dependency resources under AzureML registry. View these rules under Dependency outbound rules.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Pro všechny prostředky závislostí v registru AzureML se vytvoří pravidla odchozích závislostí. Tato pravidla si můžete zobrazit v části Pravidla odchozích přenosů závislostí.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutboundAccessRule_SettingsBlade_appGateway_subResource_errorMessage" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[No private link config is found in resource. Set it up before creating PE outbound rule.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[V prostředku se nenašla žádná konfigurace private link. Nastavte ho před vytvořením pravidla odchozích přenosů PE.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutboundAccessRule_SettingsBlade_destinationTypeBalloonContent" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Type of the outbound destination, FQDN, Private Endpoint, Service Tag.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Typ odchozího cíle, plně kvalifikovaného názvu domény, privátního koncového bodu a značky služby.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutboundAccessRule_SettingsBlade_destinationTypeLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Destination type]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Typ cíle]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutboundAccessRule_SettingsBlade_fqdnDestinationBalloonContent" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Fully Qualified Domain Name to allow for outbound traffic.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Plně kvalifikovaný název domény, který povoluje odchozí provoz.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutboundAccessRule_SettingsBlade_fqdnDestinationLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[FQDN destination]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Cíl plně kvalifikovaného názvu domény]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutboundAccessRule_SettingsBlade_fqdnsLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[FQDNs]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Plně kvalifikované názvy domény]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutboundAccessRule_SettingsBlade_portRangeBalloonContent" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Provide a single port, such as 80; a port range, such as 1024-655535; or a comma-separated list of single ports and/or port ranges, such as 80,1024-655535. This specifies on which ports traffic will be allowed by this rule. Provide an asterisk(*) to allow traffic on any port.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Zadejte jeden port, třeba 80, rozsah portů, třeba 1024–655535, nebo seznam čárkami oddělených samostatných portů a/nebo rozsahů portů, třeba 80,1024-655535. Tím se určí porty, pro které toto pravidlo povolí přenosy. Pokud chcete povolit přenosy přes libovolný port, zadejte hvězdičku (*).]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutboundAccessRule_SettingsBlade_portRangeLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Port ranges]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Rozsahy portů]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutboundAccessRule_SettingsBlade_protocolBalloonContent" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Network protocol to allow, TCP, UDP, ICMP or Any]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Síťový protokol, který se má povolit, TCP, UDP, ICMP nebo Jakýkoli]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutboundAccessRule_SettingsBlade_protocolLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Potocol]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Protokol]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutboundAccessRule_SettingsBlade_resourceGroupBalloonContent" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Resource group containing the target resource for the private endpoint.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Skupina prostředků obsahující cílový prostředek pro privátní koncový bod.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutboundAccessRule_SettingsBlade_resourceGroupLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Resource group]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Skupina prostředků]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutboundAccessRule_SettingsBlade_resourceNameBalloonContent" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Name of the target resource for the private endpoint.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Název cílového prostředku pro privátní koncový bod.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutboundAccessRule_SettingsBlade_resourceNameLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Resource name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Název prostředku]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutboundAccessRule_SettingsBlade_resourceTypeBalloonContent" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Type of the Azure resource that supports Private Link.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Typ prostředku Azure, který podporuje Private Link.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutboundAccessRule_SettingsBlade_resourceTypeLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Resource type]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Typ prostředku]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutboundAccessRule_SettingsBlade_ruleNameAlreadyExists" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Rule name already exists]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Název pravidla už existuje.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutboundAccessRule_SettingsBlade_ruleNameBalloonContent" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Name of the outbound rule that is unique in the workspace.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Název odchozího pravidla, které je v pracovním prostoru jedinečné.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutboundAccessRule_SettingsBlade_ruleNameLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Rule name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Název pravidla]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutboundAccessRule_SettingsBlade_serviceTagBalloonContent" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Predefined identifiers that represent a category of IP addresses.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Předdefinované identifikátory, které představují kategorii IP adres.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutboundAccessRule_SettingsBlade_serviceTagLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Service tag]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Značka služby]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutboundAccessRule_SettingsBlade_sparkEnabledBalloonContent" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Check to enable an additional private endpoint to be used by jobs running on Spark.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Zaškrtněte, pokud chcete povolit použití dalšího privátního koncového bodu úlohami spuštěnými ve Sparku.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutboundAccessRule_SettingsBlade_sparkEnabledLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Spark enabled]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Spark povolen]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutboundAccessRule_SettingsBlade_sparkStatusBalloonContent" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Indicates whether managed network is provisioned for Spark jobs.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Určuje, jestli je pro úlohy Sparku zřízená spravovaná síť.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutboundAccessRule_SettingsBlade_sparkStatusLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Spark status]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Stav Sparku]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutboundAccessRule_SettingsBlade_statusBalloonContent" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Status is read-only, when Active, indicates the managed network is provisioned and ready. When Inactive, indicates it has not provisioned.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Stav je jen pro čtení, pokud je aktivní, znamená to, že spravovaná síť je zřízená a připravená. Pokud je neaktivní, znamená to, že není zřízená.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutboundAccessRule_SettingsBlade_statusLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Status]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Stav]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutboundAccessRule_SettingsBlade_subResourceBalloonContent" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Sub resource to connect for the private endpoint.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Dílčí prostředek, který se má připojit pro privátní koncový bod.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutboundAccessRule_SettingsBlade_subResourceLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Sub resource]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Dílčí prostředek]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutboundAccessRule_SettingsBlade_subscriptionBalloonContent" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Subscription containing the target resource for the private endpoint.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Předplatné obsahující cílový prostředek pro privátní koncový bod.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutboundAccessRule_SettingsBlade_subscriptionLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Subscription]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Předplatné]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutboundAccessRule_SettingsBlade_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Workspace outbound rules]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Pravidla odchozích přenosů pracovního prostoru]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Overview" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Overview]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Přehled]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OverviewKeywords" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Summary Home]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Domovská stránka souhrnu]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Overview_Banner_Description_label" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The Azure Machine Learning Studio is a web app where you can build, train, test, and deploy ML models. Launch it now to start exploring, or {}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure Machine Learning Studio je webová aplikace, ve které můžete vytvářet, cvičit, testovat a nasazovat modely ML. Spusťte ho a začněte ho zkoumat nebo {}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Overview_Banner_Description_learnMoreText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[learn more about the Azure Machine Learning studio]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[další informace o studiu Azure Machine Learning]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[learn more about the Azure Machine Learning Studio]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Overview_Banner_Registry_launchButtonText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Launch registry in studio]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Spustit registr ve studiu]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Overview_Banner_Registry_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Work with your registry in Azure Machine Learning Studio]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Pracujte s registry v Azure Machine Learning Studiu]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Overview_Banner_launchButtonText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Launch studio]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Spustit studio]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Overview_Banner_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Work with your models in Azure Machine Learning Studio]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Pracujte s modely v Azure Machine Learning Studiu]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Manage your machine learning lifecycle]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Overview_Essentials_ProjectID" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Project ID]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[ID projektu]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Overview_Essentials_edit" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[edit]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[upravit]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Overview_Essentials_managedResourceGroup" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Managed resource group]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Spravovaná skupina prostředků]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Overview_Essentials_mlFlowWebURLText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[MLFlow tracking URI]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Identifikátor URI pro sledování toku MLflow]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Overview_Essentials_registryWebURLText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Registry web URL]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Adresa URL webu registru]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Overview_Essentials_studioWebURLText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Studio web URL]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Webová adresa URL studia]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Commands_ApproveNotifications_Failure_message" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Failed to approve {0} out of {1} private endpoint connections]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Nepovedlo se schválit {0} z celkového počtu {1} připojení privátních koncových bodů.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Commands_ApproveNotifications_Failure_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Failed to approve private endpoint connections]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Nepovedlo se schválit připojení privátních koncových bodů]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Commands_ApproveNotifications_InProgress_message" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Approving {0} private endpoint connections]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Schvaluje se tento počet připojení privátních koncových bodů: {0}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Commands_ApproveNotifications_InProgress_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Approving private endpoint connections...]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Schvalují se připojení privátních koncových bodů...]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Commands_ApproveNotifications_Success_message" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Successfully approved {0} private endpoint connections.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Tento počet připojení privátních koncových bodů se úspěšně schválil: {0}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Commands_ApproveNotifications_Success_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Successfully approved private endpoint connections]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Připojení privátních koncových bodů se úspěšně schválila]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Commands_Approve_messagePlural" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Do you want to approve the {0} selected connections?]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Chcete schválit tento počet vybraných připojení: {0}?]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Commands_Approve_messageSingular" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Do you want to approve the connection '{0}'?]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Chcete schválit připojení {0}?]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Commands_Approve_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Approve connection]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Schválit připojení]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Commands_DeleteNotifications_Failure_message" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Failed to delete {0} out of {1} private endpoint connections]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Nepovedlo se odstranit {0} z celkového počtu {1} připojení privátních koncových bodů.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Commands_DeleteNotifications_Failure_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Failed to delete private endpoint connections]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Nepovedlo se odstranit připojení privátních koncových bodů]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Commands_DeleteNotifications_InProgress_message" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deleting {0} private endpoint connections]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Odstraňuje se tento počet připojení privátních koncových bodů: {0}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Commands_DeleteNotifications_InProgress_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deleting private endpoint connections...]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Odstraňují se připojení privátních koncových bodů...]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Commands_DeleteNotifications_Success_message" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Successfully deleted {0} private endpoint connections.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Tento počet připojení privátních koncových bodů se úspěšně odstranil: {0}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Commands_DeleteNotifications_Success_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Successfully deleted private endpoint connections]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Připojení privátních koncových bodů se úspěšně odstranila]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Commands_RejectNotifications_Failure_message" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Failed to reject {0} out of {1} private endpoint connections]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Nepovedlo se zamítnout {0} z celkového počtu {1} připojení privátních koncových bodů.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Commands_RejectNotifications_Failure_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Failed to reject private endpoint connections]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Nepovedlo se zamítnout připojení privátních koncových bodů]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Commands_RejectNotifications_InProgress_message" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Rejecting {0} private endpoint connections]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Zamítá se tento počet připojení privátních koncových bodů: {0}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Commands_RejectNotifications_InProgress_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Rejecting private endpoint connections...]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Zamítají se připojení privátních koncových bodů...]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Commands_RejectNotifications_Success_message" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Successfully rejected {0} private endpoint connections.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Tento počet připojení privátních koncových bodů se úspěšně zamítl: {0}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Commands_RejectNotifications_Success_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Successfully rejected private endpoint connections]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Připojení privátních koncových bodů se úspěšně zamítla]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Commands_Reject_messagePlural" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Do you want to reject the {0} selected connections?]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Chcete zamítnout tento počet vybraných připojení: {0}?]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Commands_Reject_messageSingular" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Do you want to reject the connection '{0}'?]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Chcete zamítnout připojení {0}?]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Commands_Reject_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Reject connection]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Odmítnout připojení]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Commands_Remove_messagePlural" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Do you want to delete the {0} selected connections?]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Chcete odstranit tento počet vybraných připojení: {0}?]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Commands_Remove_messageSingular" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Do you want to delete the connection '{0}'?]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Chcete odstranit připojení {0}?]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Commands_Remove_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Delete connection]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Odstranit připojení]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Commands_addPrivateEndpoint" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Private endpoint]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Privátní koncový bod]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Commands_approve" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Approve]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Schválit]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Commands_description" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Description]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Popis]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Commands_reject" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Reject]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Odmítnout]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Commands_remove" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Remove]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Odebrat]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Create_validationErrorFormat" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{0}: {1}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{0}: {1}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_GridColumns_description" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Description]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Popis]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_GridColumns_name" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Connection name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Název připojení]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_GridColumns_privateEndpoint" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Private endpoint]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Privátní koncový bod]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_GridColumns_status" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Connection state]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Stav připojení]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Grid_StatusFilter_all" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[All connection states]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Všechny stavy připojení]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Grid_StatusFilter_itemFormat" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{0} ({1})]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{0} ({1})]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Grid_StatusFilter_some" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{0} connection states selected]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Počet vybraných stavů připojení: {0}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Status_approved" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Approved]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Schváleno]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Status_disconnected" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Disconnected]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Odpojeno]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Status_pending" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Pending]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Čeká na dokončení]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Status_rejected" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Rejected]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Odmítnuto]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Toolbar_refresh" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Refresh]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Aktualizovat]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_filterByName" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Filter by name...]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Filtrovat podle názvu...]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_filterByStatus" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Filter by connection state...]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Filtrovat podle stavu připojení...]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_genericErrorMessage" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[There was an error processing your request. Try again in a few moments.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Při zpracování vaší žádosti došlo k chybě. Zkuste to za chvíli znovu.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_label" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Private endpoint connections]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Připojení privátních koncových bodů]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_menuText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Networking]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Sítě]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Private endpoint connections]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ProgressMachineLearningDeleteError" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The resource could not be deleted.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Prostředek se nepovedlo odstranit.]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[The Workspace could not be deleted.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ProgressMachineLearningRegistryDeleteError" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The Registry could not be deleted.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Registr se nepovedlo odstranit.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ProgressMachineLearningRegistryDeleteExecuting" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deleting the Registry]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Odstraňuje se registr.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ProgressMachineLearningServicesDeleteExecuting" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deleting resource]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Odstraňování prostředku]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Deleting the Workspace]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ProgressNotificationMachineLearningRegistryDeleteError" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[An error occurred while deleting the Registry name '{registryName}'.{lineBreak}Error details:{lineBreak}{errorMessage}.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Při odstraňování registru s názvem {registryName} došlo k chybě.{lineBreak}Podrobnosti o chybě:{lineBreak}{errorMessage}.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ProgressNotificationMachineLearningRegistryDeleteErrorTitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Registry deletion error]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Chyba při odstraňování registru]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ProgressNotificationMachineLearningRegistryDeleteExecuting" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Registry '{registryName}' deletion in progress...]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Odstraňuje se registr {registryName}…]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ProgressNotificationMachineLearningRegistryDeleteExecutingTitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deleting Registry...]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Odstraňuje se registr…]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ProgressNotificationMachineLearningRegistryDeleteSuccess" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Registry '{registryName}' was deleted successfully.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Registr {registryName} se úspěšně odstranil.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ProgressNotificationMachineLearningRegistryDeleteSuccessTitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Registry deleted]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Registr se odstranil]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ProgressNotificationMachineLearningServicesDeleteCancelTitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Resource deletion canceled]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Odstranění prostředku se zrušilo]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Workspace deletion canceled]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ProgressNotificationMachineLearningServicesDeleteError" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[An error occurred while deleting the Workspace name '{workspaceName}'.{lineBreak}Error details:{lineBreak}{errorMessage}.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Došlo k chybě při odstraňování pracovního prostoru s názvem {workspaceName}.{lineBreak}Podrobnosti o chybě:{lineBreak}{errorMessage}.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ProgressNotificationMachineLearningServicesDeleteErrorTitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Resource deletion error]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Chyba odstranění prostředku]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Workspace deletion error]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ProgressNotificationMachineLearningServicesDeleteExecuting" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Resource '{workspaceName}' deletion in progress...]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Prostředek „{workspaceName}“ se odstraňuje...]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Workspace  '{workspaceName}' deletion in progress...]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ProgressNotificationMachineLearningServicesDeleteExecutingTitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deleting resource...]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Odstraňuje se prostředek…]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Deleting Workspace...]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ProgressNotificationMachineLearningServicesDeleteSuccess" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Resource '{workspaceName}' was deleted successfully.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Prostředek „{workspaceName}“ se úspěšně odstranil.]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Workspace '{workspaceName}' was deleted successfully.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ProgressNotificationMachineLearningServicesDeleteSuccessTitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Succesfully deleted]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Úspěšně odstraněno]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Workspace deleted]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PropertiesBladeApplicationInsightsLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Application Insights]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Application Insights]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PropertiesBladeContainerRegistryIdLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Container Registry ID]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[ID Container Registry]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PropertiesBladeCreationDateLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Created on]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Vytvořené]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PropertiesBladeKeyVaultIdLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Key Vault ID]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[ID služby Key Vault]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PropertiesBladeLocationLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Location]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Umístění]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PropertiesBladeResourceGroupLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Resource Group]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Skupina prostředků]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PropertiesBladeResourceIdLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Resource ID]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[ID prostředku]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PropertiesBladeStorageAccountIdLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Storage account ID]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[ID účtu úložiště]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PropertiesBladeSubscriptionIdLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Subscription ID]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[ID předplatného]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PropertiesBladeSubscriptionNameLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Subscription Name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Název předplatného]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PropertiesBladeWorkspaceHubIdLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[AI hub ID]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[ID centra AI]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Azure AI hub ID]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PropertiesBladeWorkspaceIdLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Machine Learning workspace ID]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[ID pracovního prostoru Machine Learning]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Workspace ID]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Properties_Discard" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Discard changes]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Zahodit změny]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Properties_Refresh" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Refresh]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Aktualizovat]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Properties_Save" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Save]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Uložit]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Properties_Saving" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Saving...]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Ukládání…]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Quota_Link_BladeDescription" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[View quota by subscription and region, and request quota directly from the studio.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Umožňuje zobrazit kvótu podle předplatného a oblasti a požádat o kvótu přímo ze Studia.]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[View subscription- and workspace-level quota, configure workspace quota usages, and request quota directly from the studio.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Quota_Link_BladeTitle" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Request and view quota in Azure AI Studio]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Vyžádání a zobrazení kvóty v Azure AI Studiu]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Request and view quota in Azure Machine Learning Studio]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Quota_Link_Button" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[View quota]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Zobrazit kvótu]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";RecentlyDeletedWorkspaces" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Recently deleted]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Nedávno odstraněno]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";RegionsTab_AdditionRegionLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Additonal regions]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Další oblasti]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";RegionsTab_Description" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select regions in which you currently have or plan to create AzureML workspaces and use assets from this registry. You can choose to add more regions to the registry later.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Vyberte oblasti, ve kterých aktuálně máte nebo plánujete vytvářet pracovní prostory AzureML a používat prostředky z tohoto registru. Další oblasti můžete do registru přidat později.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";RegionsTab_Title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Regions]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Oblasti]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";RequestQuota_dedicatedSectionLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Dedicated]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Vyhrazeno]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";RequestQuota_lowPrioritySectionLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Low priority]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Nízká priorita]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";RequestQuota_vmTypeDedicated" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Dedicated]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Vyhrazeno]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";RequestQuota_vmTypeLowPriority" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Low priority]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Nízká priorita]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";RequestQuota_vmTypesLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[VM Types]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Typy virtuálních počítačů]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ResourceLocationColumn" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Location]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Umístění]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ResourceNameRequired" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The resource name is required]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Název prostředku je povinný.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Scaling" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Scaling]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Škálování]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SearchService_Dropdown_standardSupportedFormat" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{0} (SKU: {1}) - Standard SKU is needed at the minimum]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{0} (SKU: {1}) – Jako minimum se vyžaduje SKU Standard.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Security_menuText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Security]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Zabezpečení]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SelectLocation" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select a location]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Vyberte umístění.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SelectSubscription" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select a subscription]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Vyberte předplatné.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_Blade_AIStudio_ToolBar_Header" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[AI Studio resources]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Prostředky AI Studio]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_Blade_ErrorMessage" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Error loading soft deleted resources]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Chyba při načítání obnovitelně odstraněných prostředků]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Error loading soft deleted workspaces]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_Blade_Footer_Buttons_Cancel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Cancel]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Zrušit]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_Blade_Footer_Buttons_Purge" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Permanently delete]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Trvale odstranit]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Purge]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_Blade_Footer_Buttons_Recover" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Recover]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Obnovit]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_Blade_LoadingText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Loading...]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Načítá se...]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_Blade_SubTitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Recover or permanently delete resources]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Obnovení nebo trvalé odstranění prostředků]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Recover or permanently delete workspaces]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_Blade_Title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Recently deleted resources]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Nedávno odstraněné prostředky]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Recently deleted workspaces (preview)]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_Blade_ToolBar_Buttons_Refresh" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Refresh]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Aktualizovat]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_Blade_ToolBar_Header" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Machine Learning workspaces]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Pracovní prostory Machine Learning]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_DeleteBlade_FormControls_Buttons_cancel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Cancel]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Zrušit]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_DeleteBlade_FormControls_Buttons_delete" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Delete]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Odstranit]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_DeleteBlade_FormControls_Confirm_errorMessage" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Name does not match]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Název neodpovídá.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_DeleteBlade_FormControls_Confirm_label" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Confirm delete]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Potvrdit odstranění]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_DeleteBlade_FormControls_Confirm_placeholder" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Type the resource name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Zadejte název prostředku]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Type the workspace name]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_DeleteBlade_FormControls_PermanentDelete_checkbox" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Delete this resource permanently]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Trvale odstranit tento prostředek]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Delete the workspace permanently]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_DeleteBlade_FormControls_PermanentDelete_header" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Permanently delete]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Trvale odstranit]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_DeleteBlade_Message_AIStudio_text" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Permanently delete this resource "{0}"?]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Trvale odstranit tento prostředek „{0}“?]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_DeleteBlade_Message_CMKtext" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This resource uses a customer-managed key (CMK) for data encryption. When soft-deleted, dependent resources for storing data will not be deleted and will incur cost until this resource is hard-deleted. {0}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Tento prostředek používá k šifrování dat klíč spravovaný zákazníkem (CMK). Při obnovitelném odstranění se závislé prostředky pro ukládání dat neodstraní a budou se za ně účtovat poplatky, dokud se tento prostředek trvale neodstraní. {0}]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[This workspace uses a customer-managed key (CMK) for data encryption. When soft-deleted, dependent resources for storing workspace data will not be deleted and will incur cost until this workspace is hard-deleted. {0}]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_DeleteBlade_Message_link" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Další informace]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_DeleteBlade_Message_text" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[When you delete this resource, it is temporarily placed in a ‘soft-delete’ state that allows you to recover it. Deletion of your data is postponed until your resource is permanently deleted by you, or until the expiry of the soft-delete data retention period of fourteen (14) days. You can override the soft-delete behavior and permanently delete your resource immediately. {0}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Když tento prostředek odstraníte, dočasně se umístí do stavu „obnovitelného odstranění“, který vám umožní ho obnovit. Odstranění vašich dat se odloží, dokud váš prostředek sami trvale neodstraníte, nebo do vypršení platnosti období uchovávání dat obnovitelného odstranění po dobu čtrnácti (14) dnů. Chování obnovitelného odstranění můžete přepsat a váš prostředek tak okamžitě trvale odstranit. {0}]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[When you delete a workspace, it is temporarily placed in a ‘soft-delete’ state that allows you to recover any workspace data that was deleted unintentionally. Deletion of your workspace data is postponed until your workspace is permanently deleted by you, or until the expiry of the soft-delete data retention period of fourteen (14) days. You can override the soft-delete behavior and permanently delete your workspace immediately. {0}]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_DeleteBlade_Title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Delete resource]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Odstranit prostředek]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Delete workspace]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_Grid_AriaLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deleted resources grid]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Mřížka odstraněných prostředků]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Deleted workspaces grid]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_Grid_Columns_DeletedDate" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deleted date]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Datum odstranění]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_Grid_Columns_Name" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Název]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_Grid_Columns_PurgeDate" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Scheduled permanent deletion date]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Naplánované datum trvalého odstranění]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Scheduled purge date]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_Grid_Columns_ResourceGroup" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Resource group]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Skupina prostředků]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_Grid_NoWorkspacesFound" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[No resources found to display]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Nenašly se žádné prostředky, které by se daly zobrazit]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[No workspaces found to display]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_OverviewBlade_DeleteMessage" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The soft delete feature has been enabled on this resource. After you soft delete,  this resource data remains available. It will get purged after the retention period. You may purge it sooner, or recover the resource]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[U tohoto prostředku byla povolena funkce obnovitelného odstranění. Po obnovitelném odstranění zůstanou tato data prostředků dostupná. Po uplynutí doby uchovávání se vyprázdní. Prostředek můžete vyprázdnit dříve nebo ho obnovit]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[The soft delete feature has been enabled on this workspace. After you soft delete this workspace, workspace data remains available. It will get purged after the retention period. You may purge it sooner, or recover the workspace]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_WorkspacesBlade_Commands_Generic_Error_Message" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[There was an error processing your request. Try again in a few moments]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Při zpracování vašeho požadavku se stala chyba. Zkuste to za chvíli znova.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_WorkspacesBlade_Commands_PurgeNotifications_Failure_message" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Failed to permanently delete {0} out of {1} resources]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Nepovedlo se trvale odstranit {0} z(e) {1} prostředků]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Failed to permanently delete {0} out of {1} workpsaces]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_WorkspacesBlade_Commands_PurgeNotifications_Failure_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Failed to delete resources]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Nepovedlo se odstranit prostředky]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Failed to delete workspaces]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_WorkspacesBlade_Commands_PurgeNotifications_InProgress_message" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Permanently deleting {0} resources]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Trvalé odstraňování {0} prostředků]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Permanently deleting {0} workspaces]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_WorkspacesBlade_Commands_PurgeNotifications_InProgress_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deleting resources ...]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Odstraňují se prostředky...]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Deleting workspaces ...]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_WorkspacesBlade_Commands_PurgeNotifications_Success_message" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Successfully deleted {0} resource(s) permanently]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Úspěšně se trvale odstranil tento počet prostředků: {0}.]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Successfully deleted {0} workspace(s) permanently]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_WorkspacesBlade_Commands_PurgeNotifications_Success_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Successfully deleted resources]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Prostředky se úspěšně odstranily]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Successfully deleted workspaces]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_WorkspacesBlade_Commands_RecoverNotifications_Failure_message" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Failed to recover {0} out of {1} resources]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Obnovení {0} z(e) {1} prostředků se nezdařilo.]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Failed to recover {0} out of {1} workpsaces]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_WorkspacesBlade_Commands_RecoverNotifications_Failure_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Failed to recover resources]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Nepovedlo se obnovit prostředky]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Failed to recover workspaces]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_WorkspacesBlade_Commands_RecoverNotifications_InProgress_message" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Recovering {0} resource(s)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Obnovování {0} prostředků]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Recovering {0} workspaces]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_WorkspacesBlade_Commands_RecoverNotifications_InProgress_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Recovering resources ...]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Obnovují se prostředky...]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Recovering workspaces ...]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_WorkspacesBlade_Commands_RecoverNotifications_Success_message" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Successfully recovered {0} resource(s)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Počet úspěšně obnovených prostředků: {0}.]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Successfully recovered {0} workspace(s)]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_WorkspacesBlade_Commands_RecoverNotifications_Success_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Successfully recovered resources]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Prostředky se úspěšně obnovily]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Successfully recovered workspaces]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_WorkspacesBlade_Commands_ValidateResources_AppInsights_Failure_message" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Recovery error: the associated Application Insights resource "{0}" could not be found. It may have been deleted. Recover or recreate the application insights resource under its former name "{0}". You may restore your application insights data by recovering the associated log analytics workspace first."]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Chyba obnovení: Přidružený prostředek Application Insights „{0}“ nebyl nalezen. Je možné, že byl odstraněn. Obnovte nebo znovu vytvořte prostředek Application Insights pod jeho dřívějším názvem „{0}“. Data Application Insights můžete obnovit tak, že nejdříve obnovíte přidružený pracovní prostor log analytics."]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Application insights resource "{0}" is a dependency of the workspace but does not exist. It may have been deleted. Before recovering your workspace, first recreate the application insights resource under its former name "{0}". You may restore your application insights data by recovering the associated log analytics workspace first."]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_WorkspacesBlade_Commands_ValidateResources_ContainerRegistry_Failure_message" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Recovery error: the associated Container Registry resource "{0}" could not be found. A soft-deleted cannot be recovered without a Container Registry as a dependency.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Chyba obnovení: Přidružený prostředek Container Registry „{0}“ nebyl nalezen. Obnovitelně odstraněný objekt nelze obnovit bez Container Registry jako závislosti.]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Recovery error: the workspace-associated Container Registry resource "{0}" no longer exists. A soft-deleted workspace cannot be recovered without a Container Registry as a dependency.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_WorkspacesBlade_Commands_ValidateResources_Failure_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Failed to validate resource {0}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Nepovedlo se ověřit prostředek {0}]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Failed to validate workspace {0}]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_WorkspacesBlade_Commands_ValidateResources_InProgress_message" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Validating {0} resources for {1}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Ověřování {0} prostředků pro {1}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_WorkspacesBlade_Commands_ValidateResources_InProgress_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Validating resources ...]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Ověřování prostředků...]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Validating workspace resources ...]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_WorkspacesBlade_Commands_ValidateResources_KeyVault_Failure_message" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Recovery error: the associated Azure Key Vault resource "{0}" could not be found. A soft-deleted resource cannot be recovered without its previously attached Key Vault. The required Azure Key Vault may still be recoverable, see `Manage deleted vaults`.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Chyba obnovení: Přidružený prostředek Azure Key Vault „{0}“ se nedalo najít. Obnovitelně odstraněný prostředek nelze obnovit bez dříve připojených Key Vault. Požadované Azure Key Vault můžou být stále obnovitelné, přečtěte si téma „Správa odstraněných trezorů“.]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Recovery error: the workspace-associated Azure Key Vault resource "{0}" does not exist. A soft-deleted workspace cannot be recovered without its previously attached Key Vault resource. The required Azure Key Vault may still be recoverable, see `Manage deleted vaults`.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_WorkspacesBlade_Commands_ValidateResources_StorageAccount_Failure_message" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Recovery error: the associated Storage Account resource "{0}" could not be found. If the storage account was previously soft-deleted, recover it first before recovering this resource.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Chyba obnovení: Přidružený prostředek účtu úložiště „{0}“ se nedalo najít. Pokud byl účet úložiště dříve obnovitelně odstraněn, před obnovením tohoto prostředku ho obnovte.]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Recovery error: the workspace-associated Storage Account resource "{0}" no longer exists. If the storage account was previously soft-deleted, recover it first before recovering the workspace.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_WorkspacesBlade_Commands_ValidateResources_Success_message" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Successfully validated resources for {0}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Prostředky pro {0} se úspěšně ověřily.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_WorkspacesBlade_Commands_ValidateResources_Success_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Successfully validated]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Úspěšně ověřeno]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Successfully validated workspace]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";StorageAccount_Dropdown_Hub_info" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A storage account is used as the default datastore for the AI hub. You may create a new Azure Storage resource or select an existing one in your subscription.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Účet úložiště se používá jako výchozí úložiště dat pro centrum AI. Můžete vytvořit nový prostředek Azure Storage nebo vybrat nějaký existující prostředek v předplatném.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";StorageAccount_Dropdown_SettingsBlade_Name_infoDescription" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The name must meet the following requirements:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Název musí splňovat tyto požadavky:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";StorageAccount_Dropdown_SettingsBlade_Name_infoItem1" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Unique across all existing storage account names in Azure]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Jedinečné mezi všemi existujícími názvy účtů úložiště v Azure]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";StorageAccount_Dropdown_SettingsBlade_Name_infoItem2" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Between 3 and 24 characters long]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Délka mezi 3 a 24 znaky]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";StorageAccount_Dropdown_SettingsBlade_Name_infoItem3" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Only contain lowercase letters and numbers]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Obsahuje jen malá písmena a číslice.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";StorageAccount_Dropdown_SettingsBlade_Performance_info" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Standard storage accounts are backed by magnetic drives and provide the lowest cost per GB. Premium storage accounts are backed by solid state drives and offer consistent, low-latency performance.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Účty úložiště Standard se zálohují na magnetické jednotky a cena za GB je u nich nejnižší. Účty Premium Storage se zálohují na jednotky SSD (solid-state drive) a nabízejí konzistentní výkon s nízkou latencí.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";StorageAccount_Dropdown_SettingsBlade_Replication_info" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The data in your storage account is always replicated to ensure durability and high availability. Choose a replication strategy that best matches you requirements.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Data ve vašem účtu úložiště se vždy replikují, aby se zajistila odolnost a vysoká dostupnost. Zvolte strategii replikace, která nejlépe odpovídá vašim požadavkům.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";StorageAccount_Dropdown_SettingsBlade_Replication_infoLearnMore" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Další informace]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";StorageAccount_Dropdown_SettingsBlade_hnsCheckLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Enable hierarchical namespace (preview)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Povolit hierarchický obor názvů (Preview)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";StorageAccount_Dropdown_SettingsBlade_hnsDescriptionText" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The Data Lake Storage Gen2 hierarchical namespace accelerates big data analytics workloads, enables faster and more reliable file operations, and enables file-level access control lists (ACLs). {0}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Hierarchický obor názvů Data Lake Storage Gen2 urychluje úlohy analýzy velkých objemů dat, umožňuje rychlejší a spolehlivější operace se soubory a povoluje seznamy řízení přístupu (ACL) na úrovni souborů. {0}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";StorageAccount_Dropdown_SettingsBlade_hnsDescriptionTitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Data Lake Storage Gen2 (preview)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Data Lake Storage Gen2 (Preview)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";StorageAccount_Dropdown_SettingsBlade_hnsNotSupportedFormat" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{0} - Hierarchical namespace is not supported]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{0} – Hierarchický obor názvů se nepodporuje.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";StorageAccount_Dropdown_SettingsBlade_learnMoreText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Další informace]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";StorageAccount_Dropdown_SettingsBlade_nameInvalidMessage" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Storage name must be between 3 and 24 characters and may only contain lowercase letters and numbers.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Název úložiště musí být dlouhý 3 až 24 znaků a může obsahovat jen malá písmena a číslice.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";StorageAccount_Dropdown_SettingsBlade_nameLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Název]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";StorageAccount_Dropdown_SettingsBlade_performanceLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Performance]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Výkon]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";StorageAccount_Dropdown_SettingsBlade_performancePremium" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Premium]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Premium]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";StorageAccount_Dropdown_SettingsBlade_performanceStandard" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Standard]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Standard]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";StorageAccount_Dropdown_SettingsBlade_premiumNotSupportedFormat" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{0} - Premium storage account is not supported]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{0} – Účet úložiště Premium se nepodporuje.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";StorageAccount_Dropdown_SettingsBlade_replicationGRS" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Geo-redundant storage (GRS)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Geograficky redundantní úložiště (GRS)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";StorageAccount_Dropdown_SettingsBlade_replicationGZRS" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Geo-zone-redundant storage (GZRS)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Geograficky a zónově redundantní úložiště (GZRS)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";StorageAccount_Dropdown_SettingsBlade_replicationLRS" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Locally-redundant storage (LRS)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Místně redundantní úložiště (LRS)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";StorageAccount_Dropdown_SettingsBlade_replicationLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Replication]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Replikace]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";StorageAccount_Dropdown_SettingsBlade_replicationRAGRS" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Read-access geo-redundant storage (RA-GRS)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Geograficky redundantní úložiště s přístupem pro čtení (RA-GRS)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";StorageAccount_Dropdown_SettingsBlade_replicationRAGZRS" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Read-access geo-zone-redundant storage (RA-GZRS)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Geograficky a zónově redundantní úložiště s přístupem pro čtení (RA-GZRS)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";StorageAccount_Dropdown_SettingsBlade_replicationZRS" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Zone-redundant storage (ZRS)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Zónově redundantní úložiště (ZRS)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";StorageAccount_Dropdown_SettingsBlade_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create new storage account]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Vytvořit nový účet úložiště]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";StorageAccount_Dropdown_createNewAriaLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create new storage account]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Vytvořit nový účet úložiště]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";StorageAccount_Dropdown_info" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A storage account is used as the default datastore for the workspace. You may create a new Azure Storage resource or select an existing one in your subscription.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Účet úložiště se používá jako výchozí úložiště dat pro pracovní prostor. Můžete vytvořit nový prostředek Azure Storage, nebo vybrat nějaký existující prostředek v předplatném.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";StorageAccount_Dropdown_label" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Storage account]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Účet úložiště]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";StorageAccount_SummaryTab_label" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Storage account]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Účet úložiště]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";StorageTab_Description" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[As users work in Azure AI Studio, uploaded data, stored credentials and generated artifacts like logs are stored.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Když uživatelé pracují v Azure AI Studiu, ukládají se nahraná data, uložené přihlašovací údaje a vygenerované artefakty, jako jsou protokoly.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";StorageTab_Header" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Configure how your data is stored]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Konfigurace způsobu ukládání dat]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";StorageTab_LogsAndDockerHeader" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Logs and docker images]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Protokoly a image Dockeru]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";StorageTab_Title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Storage]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Úložiště]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SummaryTab_Advanced_ManagedIdentity_systemAssigned" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[System assigned]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Přiřazeno systémem]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SummaryTab_Advanced_ManagedIdentity_typeText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Identity type]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Typ identity]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SummaryTab_Advanced_ManagedIdentity_userAssigned" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[User assigned]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Přiřazeno uživatelem]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SummaryTab_Advanced_ManagedIdentity_userIdentityName" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[User identity name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Název identity uživatele]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SummaryTab_Advanced_ManagedIdentity_userIdentityResourceGroup" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[User identity resource group]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Skupina prostředků s identitou uživatele]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SummaryTab_Resource_existingFormat" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{0} ({1})]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{0} ({1})]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SummaryTab_Resource_newFormat" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[(new) {0}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[(nové) {0}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SummaryTab_Resource_none" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[None]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Žádný]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SummaryTab_createResourceGroupErrorText" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Error creating resource group when creating default log analytics workspace: {0}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Při vytváření výchozího pracovního prostoru služby Log Analytics došlo k chybě: {0}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SummaryTab_gettingLogWorkspacesErrorText" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Error getting log workspaces for creating default log analytics workspace: {0}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Při získávání pracovních prostorů protokolů a vytváření výchozího pracovního prostoru služby Log Analytics došlo k chybě: {0}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";TextLaunchWebWorkspace" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Explore your Machine Learning workspace to run and track experiments, compare model performance, and deploy models.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Pokud chcete spouštět a sledovat experimenty, porovnávat výkon modelu a nasazovat modely, prozkoumejte svůj pracovní prostor služby Machine Learning.]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Explore your Machine Learning service workspace to run and track experiments, compare model performance, and deploy models.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";TextLaunchWebWorkspaceHeader" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Explore your Azure Machine Learning workspace]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Prozkoumat pracovní prostor Azure Machine Learning]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Explore your Azure Machine Learning service workspace]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";TextMachineLearningServicesDocumentationLinkBody" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn how to use Azure Machine Learning.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Naučte se používat Azure Machine Learning.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";TextMachineLearningServicesDocumentationLinkHeader" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[View Documentation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Zobrazit dokumentaci]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";TextMachineLearningServicesForumLinkBody" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Join the discussion of Azure Machine Learning. ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Připojte se k diskuzi o Azure Machine Learning. ]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";TextMachineLearningServicesForumLinkHeader" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[View Forum]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Zobrazit fórum]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";TextMachineLearningServicesManageFeatoreStoreLinkHeader" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create and Manage]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Vytvořit a spravovat]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";TextMachineLearningServicesManageFeatureStoreLinkBody" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn how to create and manage Machine Learning registries.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Zjistěte, jak vytvářet a spravovat registry řešení Machine Learning.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";TextMachineLearningServicesManageRegistryLinkBody" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn how to create and manage Machine Learning registries.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Zjistěte, jak vytvářet a spravovat registry řešení Machine Learning.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";TextMachineLearningServicesManageRegistryLinkHeader" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create and Manage]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Vytvořit a spravovat]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";TextMachineLearningServicesShareRegistryLinkBody" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn how to share Machine Learning assets using registries.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Zjistěte, jak sdílet prostředky pro Machine Learning pomocí registrů.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";TextMachineLearningServicesShareRegistryLinkHeader" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Share models, components and environments]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Sdílení modelů, komponent a prostředí]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";TextViennaGitHubLinkBody" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Get inspired by a large collection of machine learning examples.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Nechte se inspirovat velkou kolekcí příkladů strojového učení.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";TextViennaGitHubLinkHeader" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[View more samples at GitHub]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Další ukázky najdete na GitHubu]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";TitleError" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Error]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Chyba]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";TitleGiveFeedback" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Give feedback]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Poslat názor]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";TitleMachineLearningServicesDeleteConfirmationMessageBox" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Delete Workspace]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Odstranit pracovní prostor]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";TitleMachineLearningServicesRegistryDeleteConfirmationMessageBox" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Delete Registry]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Odstranit registr]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";TitleMonitoringLens" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Getting Started]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Začínáme]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";TitleWebWorkspaceBlade" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Machine Learning]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Machine Learning]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Machine Learning service workspace]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ValidationAIServicesNameAlreadyInUse" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This AI Services name already exists]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Tento název AI služeb už existuje.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ValidationAppInsightsNameAlreadyInUse" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This application insights name already exists]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Tento název Application Insights už existuje.]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[This Application Insights name already exists]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ValidationContainerRegistryNameAlreadyInUse" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This container registry name already exists]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Tento název registru kontejneru už existuje]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ValidationCreateWorkspacePermission" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You don't have the required permissions ({0}) to create an account under the selected resource group]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Nemáte požadovaná oprávnění ({0}) k vytvoření účtu v rámci vybrané skupiny prostředků]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ValidationDependentResourcesAlreadyInUse" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Dependent resources with this name already exist]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Prostředky závislé buňky s tímto názvem už existují]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ValidationError" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[There was an error while attempting to validate the resource]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Při pokusu o ověření prostředku došlo k chybě.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ValidationHubNameAlreadyInUse" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This AI hub name already exists]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Tento název centra AI už existuje.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ValidationKeyVaultNameAlreadyInUse" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This key vault name already exists]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Tento název trezoru klíčů už existuje.]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[This Key Vault name already exists]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ValidationPrimaryRegionNotSelected" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Please ensure the primary region is one of the selected values]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Ujistěte se prosím, že primární oblast je jednou z vybraných hodnot.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ValidationRegionNotSelected" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Please select at least one region]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Vyberte prosím aspoň jednu oblast.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ValidationRegistryDescriptionTooLarge" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This registry description has a maximum length of 256 characters.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Tento popis registru má maximální délku 256 znaků.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ValidationRegistryNameAlreadyInUse" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This registry name already exists]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Tento název registru už existuje.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ValidationRegistryNameInvalid" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Registry name must be between 3 and 33 characters long. Its first character has to be alphanumeric, and the rest may contain hyphens and underscores. No whitespace is allowed.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Název registru musí být dlouhý od 3 do 33 znaků. Jeho první znak musí být alfanumerický a zbytek může obsahovat spojovníky a podtržítka. Nepovolují se žádné prázdné znaky.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ValidationStorageAccountNameAlreadyInUse" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This storage account name already exists]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Tento název účtu úložiště už existuje.]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[This Storage account name already exists]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ValidationWorkspaceNameAlreadyInUse" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This workspace name already exists]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Tento název pracovního prostoru už existuje]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ValidationWorkspaceNameAlreadyInUseAndSoftDeleted" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This workspace name already exists, or is being reserved by a workspace which was previously soft deleted. Please use a different name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Tento název pracovního prostoru už existuje nebo je vyhrazen pracovním prostorem, který byl dříve obnovitelně odstraněn. Použijte prosím jiný název.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ValidationWorkspaceNameInvalid" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Resource name must be between 3 and 33 characters long. Its first character has to be alphanumeric, and the rest may contain hyphens and underscores. No whitespace is allowed.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Název prostředku musí mít 3 až 33 znaků. Jeho první znak musí být alfanumerický a zbytek může obsahovat spojovníky a podtržítka. Nepovolují se žádné prázdné znaky.]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Workspace name must be between 3 and 33 characters long. Its first character has to be alphanumeric, and the rest may contain hyphens and underscores. No whitespace is allowed.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ValidationWorkspaceNameReserved" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This workspace name is reserved]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Tento název pracovního prostoru je rezervovaný]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";WorkspaceErrorMessage" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The selected subscription doesn’t have permissions to register the resource provider. For more information, <a href = {0} target = "_blank">click here</a>.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Vybrané předplatné nemá oprávnění k registraci poskytovatele prostředků. Další informace získáte <a href = {0} target = "_blank">kliknutím sem</a>.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";assetTypeNames_MachineLearningExperimentationAccount_singular" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Retired - Machine Learning Experimentation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Vyřazeno – Experimentování ve službě Machine Learning]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";automationLink" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Download a template for automation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Stáhnout šablonu pro automatizaci]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";basics" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Basics]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Základy]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";basicsBladeDetailsIntro" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Every workspace must be assigned to an Azure subscription, which is where billing happens. You use resource groups like folders to organize and manage resources, including the workspace you're about to create.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Každý pracovní prostor musí být přiřazený k předplatnému Azure, což je místo, kde probíhá fakturace. Skupiny prostředků, jako jsou složky, se používají k uspořádání a správě prostředků, včetně pracovního prostoru, který se chystáte vytvořit.]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Select the subscription to manage deployed resources and costs. Use resource groups like folders to organize and manage all your resources.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";basicsBladeDetailsIntroLearnMore" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more about Azure resource groups]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Další informace o skupinách prostředků Azure]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";basicsBladeHubDetailsIntro" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select the subscription to manage deployed resources and costs. Use resource groups like folders to organize and manage all your resources. An AI hub is a collaboration environment for a team to share project work, model endpoints, compute, (data) connections, security settings, govern usage.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Pokud chcete spravovat nasazené prostředky a náklady, vyberte předplatné. Ve skupinách prostředků, jako jsou složky, můžete uspořádat a spravovat všechny prostředky. Centrum AI je prostředí pro spolupráci, ve které tým může sdílet práci na projektu, koncové body modelu, výpočetní připojení, (datová) připojení či nastavení zabezpečení a řídit využití.]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Select the subscription to manage deployed resources and costs. Use resource groups like folders to organize and manage all your resources.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";basicsBladeInstanceIntro" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Configure your basic workspace settings like its storage connection, authentication, container, and more.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Nakonfigurujte základní nastavení pracovního prostoru, jako jsou připojení k úložišti, ověřování, kontejner a další.]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Specify the name and region for the workspace.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";basicsBladeInstanceIntroLearnMore" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Další informace]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";basicsBladeRegistryInstanceIntro" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Configure your basic registry settings like its name and description.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Nakonfigurujte základní nastavení registru, jako je jeho název a popis.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";basicsRegistryBladeDetailsIntro" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Every registry must be assigned to an Azure subscription, which is where billing happens. You use resource groups like folders to organize and manage resources, including the registry you're about to create.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Každý registr musí být přiřazený k předplatnému Azure, což je místo, kde probíhá fakturace. Skupiny prostředků, jako jsou složky, se používají k uspořádání a správě prostředků, včetně registru, který se chystáte vytvořit.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";basicsTabTitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Basics]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Základy]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";buttonCreate" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Vytvořit]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";buttonNext" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Next : {0}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Další: {0}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";buttonNextPlaceholder" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Next >]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Další >]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";buttonPrevious" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[< Previous]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[< Předchozí]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";buttonReviewCreate" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Review + create]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Zkontrolovat a vytvořit]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";createBladeNextStepsMLFlowAzureMLDescription" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[How to use MLflow with Azure ML]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Jak používat MLflow s Azure ML]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";createBladeWorkspaceDetails" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Workspace details]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Podrobnosti pracovního prostoru]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";createLocationLabelDefault" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Location]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Lokalita]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";createLocationLabelPrimaryRegion" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Primary Region]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Primární oblast]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";createLocationLabelRegion" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Region]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Oblast]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";createResourceGroup" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Resource group]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Skupina prostředků]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";createResourceGroupCreateNewPlaceholder" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]]></Val>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";createResourceGroupTitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Resource group]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Skupina prostředků]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";createSubscriptionLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Subscription]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Předplatné]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";createTagsTabIntro" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Tags are name/value pairs that enable you to categorize resources and view consolidated billing by applying the same tag to multiple resources and resource groups.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Značky jsou dvojice název-hodnota, které umožňují kategorizovat prostředky a zobrazovat souhrnnou fakturaci. Stačí k tomu u několika prostředků a skupin prostředků použít stejnou značku.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";createTagsTabLearnMoreText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more about tags]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Další informace o značkách]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";createTagsTabTitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Tags]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Značky]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";createTagsTabUpdateNotice" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Note that if you create tags and then change resource settings on other tabs, your tags will be automatically updated.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Poznámka: Pokud vytvoříte značky a pak na jiných kartách změníte nastavení prostředku, značky se automaticky aktualizují.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";createTemplateValidationError" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Validation failed. Click here to view details.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Ověření neproběhlo úspěšně. Kliknutím sem zobrazíte podrobnosti.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";createTemplateValidationInProgress" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Running final validation...]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Probíhá konečné ověření...]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";createTemplateValidationSuccess" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Validation passed]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Ověření proběhlo úspěšně.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";deploymentCapacityMetric" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deployment Capacity]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Kapacita nasazení]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";deploymentCpuUtilizationMetric" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Cpu Utilization]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Využití procesoru]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";deploymentDiskUtilizationMetric" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Disk Utilization]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Využití disku]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";deploymentEnvironmentId" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Environment]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Prostředí]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";deploymentKind" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deployment Type]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Typ nasazení]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";deploymentMemoryUtilizationMetric" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Memory Utilization]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Využití paměti]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";deploymentModelIdLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model Id]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[ID modelu]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";deploymentName" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deployment Name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Název nasazení]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";deploymentProvisioningStateLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Provisioning State]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Stav zřizování]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";detailsLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Resource details]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Podrobnosti o prostředku]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Project details]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";downloadTemplateLinkText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Download a template for automation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Stáhnout šablonu pro automatizaci]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";endpointAuthModeLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Auth Mode]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Režim ověřování]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]D;]A;      Auth Mode]D;]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";endpointId" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Endpoint Id]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[ID koncového bodu]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]D;]A;      Endpoint Id]D;]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";endpointProvisioningStateLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Provisioning State]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Stav zřizování]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]D;]A;      Provisioning State]D;]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";endpointRequestLatencyMetric" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Request Latency]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Latence požadavku]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";endpointRequestsPerMinuteMetric" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Request Per Minute]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Požadavky za minutu]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";endpointScoringUriLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Scoring Uri]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Identifikátor URI bodování]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]D;]A;      Scoring Uri]D;]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";endpointSwaggerLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Swagger Uri]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Identifikátor URI Swaggeru]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]D;]A;      Swagger Uri]D;]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";instanceLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Instance details]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Podrobnosti o instancích]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";labelCommandButtonRefresh" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Refresh]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Aktualizovat]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";labelDataLabeling" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Data Labeling]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Popisování dat]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";labelMLStudioLauncher" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Visual interface]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Vizuální rozhraní]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";labelQuotaUsage" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Usage + quotas]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Využití a kvóty]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";labelRequestQuota" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Request Quota]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Kvóta žádostí]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";labelResource" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Resource]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Prostředek]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";labelWorkstationsMenuItem" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Notebook VMs]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Notebookové virtuální počítače]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";learnMore" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Další informace]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";location" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Location]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Umístění]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";machineLearningCompute" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Managed Compute]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Spravované výpočetní prostředky]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Machine Learning Compute]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";mlStudioCapability1" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Drag-n-Drop to build machine learning models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Sestavení modelů strojového učení přetahováním položek]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";mlStudioCapability2" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[No limit to data size or compute capacity for model training]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Žádné omezení velikosti dat nebo vypočetní kapacity pro cvičení modelu]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";mlStudioCapability3" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Intrinsic and powerful Python support]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Skutečná a výkonná podpora Pythonu]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";mlStudioCapability4" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[One click to deploy your web service]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Nasazení vaší webové služby jedním klepnutím]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";mlStudioCapability5" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Rich and fast-growing modules support]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Bohatá a stále se rozšiřující podpora modulů]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";mlStudioLaunchLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Launch visual interface]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Spustit vizuální rozhraní]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";mlStudioSubtitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[What's possible with visual interface]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Co je možné s vizuálním rozhraním]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";mlStudioTitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Visual interface (preview)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Vizuální rozhraní (preview)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";multipleInvalidTabErrorMessage" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Validation failed for the following tabs: {0}. Required information is missing or not valid.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Následující karty se nepovedlo ověřit: {0}. Chybí potřebné informace, nebo nejsou platné.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";newResource" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[(new) {0}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[(nový) {0}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";newResourceCapitalized" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[(New) {0}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[(Nové) {0}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";newResourceFormatCaps" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[(New) {0}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[(Nové) {0}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";noContent" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[No content]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Žádný obsah]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";none" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[None]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Žádná]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";onlineEndpointName" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Machine learning online endpoint]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Koncový bod strojového učení online]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[ML App]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";onlineEndpointWorkspaceName" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Workspace]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Pracovní prostor]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";quickLinkUnderOverviewBladeAriaLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Quick link under Overview blade.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Rychlý odkaz v okně Přehled]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";quotaBladeTitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Usage + quotas]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Využití a kvóty]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";quotaNote" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Note:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Poznámka:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";quotaProviderNotRegisteredErrorMsg" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Your subscription {0} is not registered with the '{1}' resource provider. Please create a 'Machine Learning service' workspace to auto-register and retry submitting the support request.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Vaše předplatné {0} není registrováno u poskytovatele prostředků {1}. Vytvořte pracovní prostor služby Machine Learning, aby se provedla automatická registrace, a zkuste žádost o podporu odeslat znovu.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";quotaProviderRegisteringErrorMsg" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Registering your subscription {0} with the '{1}' resource provider.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Registruje se vaše předplatné {0} u poskytovatele prostředků {1}.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";quotaQuotaSettingTabHeader" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Configure quotas]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Konfigurace kvót]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";quotaRequestCurrentLimit" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Current vCPU limit]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Aktuální limit vCPU]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Current limit]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";quotaRequestDocumentationInfoBox" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Click to learn more about Compute (cores/vCPUs) quota increase requests.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Pro další informace o žádostech o navýšení kvóty výpočetních prostředků (jader a vCPU) klikněte sem.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";quotaRequestHeader" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Please enter the limit for any resource(s) you are requesting:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Zadejte prosím limit pro prostředky, o které žádáte:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";quotaRequestLogOutputMessageDetail" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{0} - current value: {1} / requested value: {2}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{0} – aktuální hodnota: {1} / požadovaná hodnota: {2}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";quotaRequestNewLimit" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[New vCPU limit]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Nový limit vCPU]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[New limit]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";quotaRequestNotFound" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[No quota resources found for given location]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Pro dané umístění se nenašly žádné prostředky kvót.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";quotaRequestResourceName" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Resource name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Název prostředku]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";quotaRequestSubmit" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Save and continue]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Uložit a pokračovat]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";quotaRequestTitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Quota details]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Podrobnosti o kvótě]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";quotaRequestVMFamily" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[VM series]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Řada virtuálních počítačů]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[VM family]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";quotaSubscriptionViewClusterHelptext" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[For a cluster we show your currently allocated cores and maximum cores it can scale to.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[U clusteru zobrazíme vaše aktuálně přidělená jádra a maximální počet jader, na který se může škálovat.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";quotaSubscriptionViewResourceNameHelptext" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Expand each VM family to view your quota allocation and resource usage by workspace and further to view your clusters and instances.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Rozbalte každou řadu virtuálních počítačů, abyste zobrazili přidělení kvóty, využití prostředků podle pracovních prostorů, clustery a instance.]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Expand each VM size to view your quota allocation and resource usage by workspace and further by clusters.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";quotaSubscriptionViewTabHeader" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Subscription view]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Zobrazení předplatného]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Subscription View]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";quotaSubscriptionViewUsageHelptext" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[For an instance it continues to use quota even in Stopped state so you can restart it at any time.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Pro instanci nadále využívá kvótu i ve stavu Zastaveno, proto je možné kdykoli provést restart.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";quotaTableHeaderDedicated" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Dedicated cores usage]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Využití vyhrazených jader]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Dedicated Cores Usage]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";quotaTableHeaderLowPriority" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Low priority cores usage]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Využití jader s nízkou prioritou]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Low Priority Cores Usage]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";quotaTableHeaderQuota" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Resource name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Název prostředku]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Resource Name]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";quotaTableNoData" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[No data to display]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Neexistují žádná data, která by bylo možné zobrazit.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";quotaTableServerError" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The server encountered an error processing current request. Please refresh the table again.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Na serveru došlo k chybě při zpracování aktuálního požadavku. Aktualizujte prosím tabulku znovu.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";quotaTableTotalSubscriptionQuota" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Total subscription quota]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Celková kvóta předplatného]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";quotaWorkspaceQuotaExceedSubscriptionLimit" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Workspace level quota cannot exceed the subscription level quota limit.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Kvóta úrovně pracovního prostoru nemůže překročit limit kvóty úrovně předplatného.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";quotaWorkspaceQuotaInsufficientPermissions" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You are not authorized to set quota at the workspace level. Please reach out to your subscription admin to help allocate resources between workspaces.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Nemáte oprávnění nastavit kvótu na úrovni pracovního prostoru. Pokud chcete pomoci přidělit prostředky mezi pracovní prostory, obraťte se na svého správce předplatného.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";quotaWorkspaceQuotaInvalidVMFamilyName" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Please specify a VM family that is supported in the $region region and you have subscription level quota for.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Zadejte rodinu virtuálních počítačů, která je v oblasti $region podporována a pro kterou máte kvótu úrovně předplatného.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";quotaWorkspaceQuotaLessThanMinimumClusterCores" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Workspace level quota cannot be less than the cores required to support the minimum nodes of this workspace’s clusters and instances.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Kvóta úrovně pracovního prostoru nemůže být nižší než počet jader vyžadovaných pro podporu minima uzlů clusterů a instancí tohoto pracovního prostoru.]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Workspace level quota cannot be less than the cores required to support the minimum nodes of this workspace’s clusters.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";quotaWorkspaceQuotaNewLimitHelpText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Allocate subscription level quota between workspaces by setting it here. To change the all-up subscription level quota, use the request quota button at the bottom. Note that you need to be a subscription owner to modify these values.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Tady můžete nastavit přidělení kvóty úrovně předplatného mezi pracovní prostory. Pokud chcete změnit celkovou kvótu úrovně předplatného, použijte tlačítko žádosti o kvótu dole. Poznámka: Tyto hodnoty může měnit jedině vlastník předplatného.]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[* Allocate subscription level quota between workspaces by setting it here. To change the all-up subscription level quota, use the request quota button at the bottom. Note that you need to be a subscription owner to modify these values.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";quotaWorkspaceQuotaPlaceHolder" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Unallocated cores: {0}, Maximum: {1}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Nepřidělené jádra: {0}, maximum: {1}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";quotaWorkspaceQuotaResourceNameHelpText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Expand each VM size to view and allocate subscription level quota between workspaces.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Rozbalte každou velikost virtuálního počítače, abyste zobrazili a přidělili kvótu úrovně předplatného mezi pracovní prostory.]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[* Expand each VM size to view and allocate subscription level quota between workspaces.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";quotaWorkspaceQuotaUnknownError" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Unknown error]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Neznámá chyba]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";quotaWorkspaceViewClusterHelptext" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[For a cluster we show your currently allocated cores and maximum cores it can scale to.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[U clusteru zobrazíme vaše aktuálně přidělená jádra a maximální počet jader, na který se může škálovat.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";quotaWorkspaceViewResourceNameHelptext" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Expand each workspace to view your quota allocation and resource usage by VM family and further to view your clusters and instances.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Rozbalte každý pracovní prostor, abyste viděli přidělení kvóty, využití prostředků podle řady virtuálních počítačů, clustery a instance.]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Expand each workspace to view your quota allocation and resource usage by VM size and further by clusters.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";quotaWorkspaceViewTabHeader" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Workspace view]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Zobrazení pracovního prostoru]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Workspace View]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";quotaWorkspaceViewUsageHelptext" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[For an instance it continues to use quota even in Stopped state so you can restart it at any time.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Pro instanci nadále využívá kvótu i ve stavu Zastaveno, proto je možné kdykoli provést restart.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";singleInvalidTabErrorMessage" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Validation failed for the following tab: {0}. Required information is missing or not valid.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Následující kartu se nepovedlo ověřit: {0}. Chybí potřebné informace, nebo nejsou platné.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";subtitleEnvironmentInfoBlade" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Configure compute for deploying and managing models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Konfigurovat výpočetní prostředky k nasazení a správě modelů]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";summaryTabTitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Review + create]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Zkontrolovat a vytvořit]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";tabValidationErrors" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Validation failed. Required information is missing or not valid.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Ověřování nebylo úspěšné. Buď chybí požadované informace, nebo nejsou platné.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";tags" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Tags]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Značky]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";textAzureDatabricksOptionCreateNew" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create New]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Vytvořit nové]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";textAzureDatabricksOptionUseExisting" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Use Existing]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Použít existující]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";textMachineLearningServicesAutoMLLinkBody" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Automatically create a model from your existing data.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Automaticky vytvoří model z vašich existujících dat.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";textMachineLearningServicesAutoMLLinkHeader" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create a new Automated Machine Learning Model (Preview)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Vytvoření nového automatizovaného modelu strojového učení (preview)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";textMachineLearningServicesNotebookVMLinkBody" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Quickly get started with the Python SDK and run sample experiments with Azure Machine Learning Notebook VMs.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Rychle začněte se sadou SDK Python. Spusťte si ukázkové experimenty s notebookovými virtuálními počítači Azure Machine Learning.]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Explore data, create models, and deploy services in your Notebook VM.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";textMachineLearningServicesNotebookVMLinkHeader" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Get Started with Sample Notebooks (Preview)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Začínáme se Sample Notebooks (Preview)]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Create Notebooks in an Azure Machine Learning Notebook VM (Preview)]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";textMachineLearningServicesVisualInterfaceLinkBody" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Drag and drop existing components to create new models.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Vytvářejte nové modely přetažením existujících komponent.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";textMachineLearningServicesVisualInterfaceLinkHeader" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Build a model using the Visual Interface (Preview)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Vytvoření modelu pomocí vizuálního rozhraní (preview)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";textNotAvailable" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[N/A]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Není k dispozici]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";titleAssetsGroup" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Assets]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Aktiva]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";titleAuthoringGroup" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Authoring (Preview)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Vytváření (preview)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";titleDeployments" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deployments]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Nasazení]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";titleEnvironmentInfoBlade" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Machine Learning Compute]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Výpočetní prostředky služby Machine Learning]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";titleSettings" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Settings]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Nastavení]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";titleSupport" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Support + troubleshooting]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Podpora a řešení potíží]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";workspaceCancelUpgradeButtonText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]A;      Cancel]A;    ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[]A;      Zrušit]A;    ]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]D;]A;      Cancel]D;]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";workspaceConfirmUpgradeButtonText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]A;      Confirm Upgrade]A;    ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[]A;      Potvrdit upgrade]A;    ]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]D;]A;      Confirm Upgrade]D;]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";workspaceCreateSKUPricingDetails" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]A;      View full pricing details]A;    ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[]A;      Zobrazit všechny podrobnosti o cenách]A;    ]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]D;]A;      View full pricing details]D;]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";workspaceCreateSKUTooltip" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]A;      The Enterprise SKU (preview) boosts productivity for all ML skill levels and offers robust MLOps capabilities to manage the complete ML lifecycle. THe Basic SKU workspace allows for open source development and a code-first experience.]A;    ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[]A;      Skladová položka Enterprise (Preview) zvyšuje produktivitu všech úrovní dovedností ML a nabízí robustní funkce MLOps pro správu celého životního cyklu ML. Pracovní prostor skladové položky Basic umožňuje opensourcový vývoj a nabízí prostředí, kde se upřednostňuje kód.]A;    ]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]D;]A;      The Enterprise SKU (preview) boosts productivity for all ML skill levels and offers robust MLOps capabilities to manage the complete ML lifecycle. THe Basic SKU workspace allows for open source development and a code-first experience.]D;]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";workspaceLoadFailure" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]A;      Workspace with ID "{0}" could not be loaded.]A;    ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[]A;      Pracovní prostor s ID {0} se nepovedlo načíst..]A;    ]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";workspaceSKUPropertyLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]A;      Workspace edition]A;    ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[]A;      Edice pracovního prostoru]A;    ]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]D;]A;      Workspace edition]D;]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";workspaceTwoBannerBasicSKUBody" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]A;      Introducing a new immersive experience (preview) for managing the end-to-end machine learning lifecycle.]A;    ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[]A;      Představujeme nové moderní prostředí (Preview) pro správu kompletního životního cyklu strojového učení.]A;    ]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]D;]A;      Introducing a new immersive experience (preview) for managing the end-to-end machine learning lifecycle.]D;]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";workspaceTwoBannerBasicSKUTitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]A;      Launch Preview Now]A;    ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[]A;      Spustit verzi Preview]A;    ]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]D;]A;      Launch Preview Now]D;]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";workspaceTwoBannerBody" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]A;      An immersive experience for managing the end-to-end machine learning lifecycle.]A;    ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[]A;      Moderní prostředí pro správu kompletního životního cyklu strojového učení.]A;    ]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]D;]A;      An immersive experience for managing the end-to-end machine learning lifecycle.]D;]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";workspaceTwoBannerBodyPreview" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]A;      Introducing a new immersive experience (preview) for managing the end-to-end machine learning lifecycle.]A;    ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[]A;      Představujeme nové moderní prostředí (Preview) pro správu kompletního životního cyklu strojového učení.]A;    ]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]D;]A;      Introducing a new immersive experience (preview) for managing the end-to-end machine learning lifecycle.]D;]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";workspaceTwoBannerButton" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]A;      Launch now]A;    ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[]A;      Spustit nyní]A;    ]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]D;]A;      Launch now]D;]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";workspaceTwoBannerLink" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]A;      Learn more]A;    ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[]A;      Další informace]A;    ]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]D;]A;      Learn more]D;]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";workspaceTwoNoticeButton" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]A;      Launch now]A;    ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[]A;      Spustit nyní]A;    ]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]D;]A;      Launch now]D;]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";workspaceTwoNoticeMessage" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]A;      Contents of this page will be moving to a new immersive experience for]A;      managing the end-to-end machine learning lifecycle. Compute targets will]A;      be manageable from both locations. Features provided in preview are]A;      offered at no additional charge but may not remain so after general]A;      availability.]A;    ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[]A;      Obsah této stránky se přesune na nové moderní prostředí pro]A;      správu kompletního životního cyklu strojového učení. Cíle výpočtů bude]A;      možné spravovat z obou míst. Funkce poskytované ve verzi Preview]A;      se nabízejí bezplatně, v obecné dostupnosti ale zdarma zůstat]A;      nemusí.]A;    ]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]D;]A;      Contents of this page will be moving to a new immersive experience for]D;]A;      managing the end-to-end machine learning lifecycle. Compute targets will]D;]A;      be manageable from both locations. Features provided in preview are]D;]A;      offered at no additional charge but may not remain so after general]D;]A;      availability.]D;]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";workspaceUpgradeAboutOurPricing" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]A;      about our pricing]A;    ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[]A;      o cenách]A;    ]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]D;]A;      about our pricing]D;]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";workspaceUpgradeAuthorizationFailed" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]A;      You currently do not have permissions to upgrade your workspace. Please contact your IT administrator.]A;    ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[]A;      V tuto chvíli nemáte oprávnění upgradovat svůj pracovní prostor. Obraťte se prosím na správce IT.]A;    ]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]D;]A;      You currently do not have permissions to upgrade your workspace. Please contact your IT administrator.]D;]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";workspaceUpgradeBannerText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Upgrade this workspace to Enterprise edition (preview) to use visual machine learning, advanced automated machine learning, and to manage quota.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Pokud chcete používat vizuální strojové učení, rozšířené automatizované strojové učení a správu kvót, upgradujte tento pracovní prostor na edici Enterprise (Preview).]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Upgrade this workspace to Enterprise edition to use visual machine learning, advanced automated machine learning, and to manage quota.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";workspaceUpgradeBulletPoint" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]A;      You need an Enterprise edition (preview) workspace to view and set quotas on Enterprise workspaces.]A;    ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[]A;      Abyste mohli zobrazovat a nastavovat kvóty pro pracovní prostory Enterprise (Preview), potřebujete pracovní prostor edice Enterprise.]A;    ]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]D;]A;      You need an Enterprise edition (preview) workspace to view and set quotas on Enterprise workspaces.]D;]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";workspaceUpgradeButtonText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]A;      Upgrade]A;    ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[]A;      Upgradovat]A;    ]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]D;]A;      Upgrade]D;]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";workspaceUpgradeConfirmationBoxContent" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]A;      Upgrade workspace to access Enterprise (preview) capabilities. Visit our {0} for more information.]A;    ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[]A;      Pokud chcete získat přístup k funkcím edice Enterprise (Preview), upgradujte pracovní prostor. Další informace najdete na našem {0}.]A;    ]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]D;]A;      Upgrade workspace to access Enterprise (preview) capabilities. Visit our {0} for more information.]D;]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";workspaceUpgradeConfirmationBoxTitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]A;      Confirm workspace upgrade]A;    ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[]A;      Potvrdit upgrade pracovního prostoru]A;    ]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]D;]A;      Confirm workspace upgrade]D;]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";workspaceUpgradeLearnMore" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]A;      Learn more]A;    ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[]A;      Další informace]A;    ]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]D;]A;      Learn more]D;]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";workspaceUpgradePricingPage" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]A;      pricing page]A;    ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[]A;      stránka s cenami]A;    ]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]D;]A;      pricing page]D;]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";workspaceUpgradeQuickLinkBannerText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]A;      Upgrade this workspace to Enterprise edition (preview) to use UI-based tools for all skill levels and more.]A;    ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[]A;      Pokud chcete používat nástroje založené na uživatelském rozhraní pro všechny úrovně dovedností a další funkce, upgradujte tento pracovní prostor na edici Enterprise (Preview).]A;    ]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]D;]A;      Upgrade this workspace to Enterprise edition (preview) to use UI-based tools for all skill levels and more.]D;]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";workspaceUpgradeQuickLinkLearnAboutEnterpriseEdition" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]A;      Learn about Enterprise Edition (preview)]A;    ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[]A;      Informace o edici Enterprise (Preview)]A;    ]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]D;]A;      Learn about Enterprise Edition (preview)]D;]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";workspaceUpgradeQuickLinkPostUpgradeBannerText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]A;      Use the Enterprise edition (preview) to access UI-based tools for all skill levels, built-in MLOps and more]A;    ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[]A;      Pokud chcete získat přístup k nástrojům založeným na uživatelském rozhraní pro všechny úrovně dovedností, integrované MLOps a další funkce, použijte edici Enterprise (Preview).]A;    ]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]D;]A;      Use the Enterprise edition (preview) to access UI-based tools for all skill levels, built-in MLOps and more]D;]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";workspaceUpgradeSetQuotaOperationNotAllowed" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]A;      This feature is disabled for you. Please upgrade to an Enterprise edition (preview) workspace to configure quotas.]A;    ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[]A;      Tato funkce je pro vás zakázaná. Pokud chcete nakonfigurovat kvóty, upgradujte prosím na pracovní prostor edice Enterprise (Preview).]A;    ]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]D;]A;      This feature is disabled for you. Please upgrade to an Enterprise edition (preview) workspace to configure quotas.]D;]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";workspaceUpgradeUpgradeCompleteNotificationContent" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]A;      Your workspace {0} upgraded successfully.]A;    ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[]A;      Váš pracovní prostor {0} se úspěšně upgradoval.]A;    ]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]D;]A;      Your workspace {0} upgraded successfully.]D;]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";workspaceUpgradeUpgradeCompleteNotificationTitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]A;      Workspace Upgrade Complete]A;    ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[]A;      Upgrade pracovního prostoru se dokončil]A;    ]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]D;]A;      Workspace Upgrade Complete]D;]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";workspaceUpgradeUpgradeFailed" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]A;      An unexpected error occurred, please try upgrading your workspace again, or submitting a support ticket.]A;    ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[]A;      Došlo k neočekávané chybě. Zkuste prosím upgradovat pracovní prostor znovu, nebo poslat lístek podpory.]A;    ]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]D;]A;      An unexpected error occurred, please try upgrading your workspace again, or submitting a support ticket.]D;]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";workspaceUpgradeUpgradeFailedNotificationContent" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]A;      Your workspace {0} did not upgrade successfully.]A;    ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[]A;      Váš pracovní prostor {0} se neupgradoval úspěšně.]A;    ]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]D;]A;      Your workspace {0} did not upgrade successfully.]D;]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";workspaceUpgradeUpgradeFailedNotificationTitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]A;      Workspace Upgrade Failed]A;    ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[]A;      Nepovedlo se upgradovat pracovní prostor]A;    ]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]D;]A;      Workspace Upgrade Failed]D;]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";workspaceUpgradeUpgradeSucceeded" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]A;      Upgrade Azure Machine Learning workspace {0} succeeded.]A;    ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[]A;      Upgrade pracovního prostoru Azure Machine Learning {0} proběhl úspěšně.]A;    ]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]D;]A;      Upgrade Azure Machine Learning workspace {0} succeeded.]D;]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";workspaceUpgradeUpgrading" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]A;      Upgrading Azure Machine Learning workspace {0}]A;    ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[]A;      Upgraduje se pracovní prostor Azure Machine Learning {0}.]A;    ]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]D;]A;      Upgrading Azure Machine Learning workspace {0}]D;]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";workspaceUpgradeUpgradingNotificationContent" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]A;      Your workspace {0} is upgrading from Basic to Enterprise]A;    ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[]A;      Váš pracovní prostor {0} se upgraduje z edice Basic na Enterprise.]A;    ]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]D;]A;      Your workspace {0} is upgrading from Basic to Enterprise]D;]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";workspaceUpgradeUpgradingNotificationTitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]A;      Workspace is currently upgrading]A;    ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[]A;      Pracovní prostor se aktualizuje]A;    ]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]D;]A;      Workspace is currently upgrading]D;]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
    </Item>
  </Item>
</LCX>