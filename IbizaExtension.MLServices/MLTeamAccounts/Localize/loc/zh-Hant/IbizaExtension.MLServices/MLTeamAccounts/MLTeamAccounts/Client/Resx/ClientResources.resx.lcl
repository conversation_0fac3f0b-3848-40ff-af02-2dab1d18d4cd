﻿<?xml version="1.0" encoding="utf-8"?>
<LCX SchemaVersion="6.0" Name="M:\AzureMLVS15-005\_work\161\s\IbizaExtension.MLServices\MLTeamAccounts\MLTeamAccounts\Client\Resx\ClientResources.resx" PsrId="211" FileType="1" SrcCul="en-US" TgtCul="zh-TW" xmlns="http://schemas.microsoft.com/locstudio/2006/6/lcx">
  <OwnedComments>
    <Cmt Name="Dev" />
    <Cmt Name="LcxAdmin" />
    <Cmt Name="Rccx" />
  </OwnedComments>
  <Settings Name="@SettingsPath@\default.lss" Type="Lss" />
  <Item ItemId=";ClientResources.resx" ItemType="0" PsrId="211" Leaf="false">
    <Disp Icon="Expand" Expand="true" Disp="true" LocTbl="false" />
    <Item ItemId=";Strings" ItemType="0" PsrId="211" Leaf="false">
      <Disp Icon="Str" Disp="true" LocTbl="false" />
      <Item ItemId=";AIServices_SummaryTab_label" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[AI Services]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[AI 服務]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AccountPart_deprecated" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deprecated]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[已淘汰]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AccountPart_deprecatedLongMessage" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The resource is no longer available. Please use Machine Learning Services Workspace Extension.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[資源已無法再使用。請使用機器學習服務工作區延伸模組。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AccountPart_deprecatedShortMessage" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Use Machine Learning Services Extension.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[請使用機器學習服務延伸模組。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdditionalResourceInfo" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[For your convenience, these resources are added automatically to the workspace, if regionally available: <a href={0} target="_blank">Azure storage</a>, <a href={1} target="_blank">Azure Application Insights</a> and <a href={2} target="_blank">Azure Key Vault</a>.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[為您的方便起見，下列資源會自動新增至工作區 (視區域可用性): <a href={0} target="_blank">Azure 儲存體</a>、<a href={1} target="_blank">Azure Application Insights</a> 和 <a href={2} target="_blank">Azure Key Vault</a>。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Key_KeyVaultChangeControl_action" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Click to select key]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[按一下以選取金鑰]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Key_KeyVaultChangeControl_key" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Key: {key}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[金鑰: {key}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Key_KeyVaultChangeControl_keyVault" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Key vault: {keyvault}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[金鑰保存庫: {keyvault}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Key_KeyVaultChangeControl_label" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Key vault]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[金鑰保存庫]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Key_KeyVaultChangeControl_purgeProtectionRequired" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[When bringing your own encryption key, your Azure Key Vault must have purge protection enabled to protect against accidental loss of data access.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[當您自備的加密金鑰時，您的 Azure Key Vault 必須啟用清除保護，以防止意外遺失資料存取。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Key_KeyVaultChangeControl_required" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Key is required]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[需要金鑰]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Key_KeyVaultChangeControl_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Key vault and key]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[金鑰保存庫與金鑰]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Key_KeyVaultChangeControl_version" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Version: {version}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[版本: {version}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_DataEncryption_LinkText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more about customer-managed key encryption.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[深入了解客戶自控金鑰加密。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Use customer-managed keys - Azure Machine Learning | Microsoft Docs]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_DataEncryption_ServiceSide_infoText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure machine learning service stores metrics and metadata in an Azure Cosmos DB instance where all data is encrypted ​at rest. By default, the data is encrypted with Microsoft-managed keys. You may choose to bring your own (customer-managed) keys. {0}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure Machine Learning 服務會將計量和中繼資料儲存在 Azure Cosmos DB 執行個體中，其中資料均經過​待用加密。根據預設，資料會使用 Microsoft 管理的金鑰加密，而您可以選擇攜帶自己的 (客戶管理的) 金鑰。{0}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_DataEncryption_ServiceSide_label" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Use service-side encryption ({0})]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用服務端加密 ({0})]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_DataEncryption_Type_label" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Encrypt data using a customer managed key]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用客戶自控金鑰加密資料]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Encryption type]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_DataEncryption_WarningMessage" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[After workspace creation, you cannot change encryption key type between Microsoft-managed keys  and Customer-managed keys.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[建立工作區之後，您無法在 Microsoft 受控金鑰與客戶自控金鑰之間變更加密金鑰類型。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[When using a customer-managed key, the costs for your subscription will be higher because of the additional resources in your subscription. To estimate the cost, use the Azure pricing calculator. To learn more, see {0}]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_DataEncryption_checkboxByoInfo" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Optionally use pre-created resources for storing encrypted workspace data. Using your own resources for encryption, allows for enhanced configuration of these resources in compliance with your organization’s IT and security requirements, but implies additional management actions by you.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[選擇性地使用預先建立的資源來儲存加密的工作區資料。使用您自己的資源進行加密，可讓您根據組織的 IT 和安全性需求來增強這些資源的設定，但代表您需採取其他管理動作。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_DataEncryption_checkboxByoLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Bring existing resources for storing encrypted data (preview)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[將儲存加密資料的現有資源 (預覽)]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Bring existing resources for data encryption]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_DataEncryption_checkboxLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Enable encryption using a Customer Managed Key]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用客戶自控金鑰啟用加密]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_DataEncryption_cosmosInfo" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select a pre-created Azure Cosmos DB resource. If you select 'None', Azure ML will create a Cosmos DB resource in a new resource group in your subscription that will be managed by Microsoft. Using your own resources for encryption implies additional management responsibility by you.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[選取預先建立的 Azure Cosmos DB 資源。若選取 [無]5D;，Azure ML 會在您的訂用帳戶中由 Microsoft 管理的新資源群組中建立 Cosmos DB 資源。使用您自己的資源進行加密，即表示您必須承擔額外的管理責任。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Select a pre-created Azure Cosmos DB resource. If you select 'None', Azure ML will create a Cosmos DB resource in a new resource group in your subscription that will be managed by Microsoft.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_DataEncryption_cosmosLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Cosmos DB]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Cosmos DB]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_DataEncryption_customerManaged" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Customer-managed keys]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[客戶管理的金鑰]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Customer-managed keys (Only available in enterprise version)]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_DataEncryption_header" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Your data is encrypted by default using Microsoft-managed keys. For additional control over your data, you may choose to bring your own key for encryption. {0}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[預設會使用 Microsoft 受控金鑰加密您的資料。若要進一步控制您的資料，您可以選擇攜帶您自己的金鑰進行加密。{0}]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Azure Machine Learning service stores metrics and metadata in an Azure Cosmos DB instance where all data is encrypted ​at rest. By default, the data is encrypted with Microsoft-managed keys. You may choose to bring your own (customer-managed) keys.​]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_DataEncryption_infoText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure machine learning service stores metrics and metadata in an Azure Cosmos DB instance where all data is encrypted ​at rest. By default, the data is encrypted with Microsoft-managed keys. You may choose to bring your own (customer-managed) keys. {0}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure Machine Learning 服務會將計量和中繼資料儲存在 Azure Cosmos DB 執行個體中，其中資料均經過​待用加密。根據預設，資料會使用 Microsoft 管理的金鑰加密，而您可以選擇攜帶自己的 (客戶管理的) 金鑰。{0}]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Azure machine learning service stores metrics and metadata in an Azure Cosmo DB instance where all data is encrypted ​at rest. By default, the data is encrypted with Microsoft-managed keys. You may choose to bring your own (customer-managed) keys. {0}]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_DataEncryption_learnMoreText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[深入了解]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_DataEncryption_microsoftManaged" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Microsoft-managed keys]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Microsoft 管理的金鑰]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_DataEncryption_searchInfo" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select a pre-created Azure Search resource. If you select 'None', Azure ML will create a Search resource in a new resource group in your subscription that will be managed by Microsoft. Using your own resources for encryption implies additional management responsibility by you.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[選取預先建立的 Azure 搜尋資源。若選取 [無]5D;，Azure ML 會在您的訂用帳戶中由 Microsoft 管理的新資源群組中建立搜尋資源。使用您自己的資源進行加密，即表示您必須承擔額外的管理責任。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Select a pre-created Azure Search resource. If you select 'None', Azure ML will create a Search resource in a new resource group in your subscription that will be managed by Microsoft.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_DataEncryption_searchLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Search]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[搜尋]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_DataEncryption_storageInfo" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select a pre-created Azure Storage resource. If you select 'None', Azure ML will create a Storage resource in a new resource group in your subscription that will be managed by Microsoft. Using your own resources for encryption implies additional management responsibility by you.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[選取預先建立的 Azure 儲存體資源。若選取 [無]5D;，Azure ML 會在您的訂用帳戶中由 Microsoft 管理的新資源群組中建立儲存體資源。使用您自己的資源進行加密，即表示您必須承擔額外的管理責任。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Select a pre-created Azure Storage resource. If you select 'None', Azure ML will create a Storage resource in a new resource group in your subscription that will be managed by Microsoft.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_DataEncryption_storageLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Storage]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[儲存體]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_DataEncryption_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Data encryption]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[資料加密]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_DataImpact_HBI_label" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[High business impact workspace]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[高業務影響工作區]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_DataImpact_header" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[If your workspace contains sensitive data, you can specify a high business impact workspace. This will control the amount of data Microsoft collects for diagnostic purposes and enables additional encryption in Microsoft managed environments.​]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[如果您的工作區包含敏感性資料，您可以指定高業務影響工作區。這會控制 Microsoft 出於診斷目的而收集的資料量，並在 Microsoft 受控環境中啟用額外的加密。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_DataImpact_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Data impact]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[資料影響]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_ManagedIdentity_description" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A managed identity enables Azure resources to authenticate to cloud services without storing credentials in code. Once enabled, all necessary permissions can be granted via Azure role-based access control. A workspace can be given either a system assigned identity or a user assigned identity.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure 資源可在程式碼中沒有儲存認證的情況下，利用受控識別對雲端服務進行驗證。啟用之後，即可透過 Azure 角色型存取控制，授與所有必要的權限。可以為工作區指定系統指派的身分識別或是使用者指派的身分識別。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_ManagedIdentity_disabledWarning" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The managed user assigned identity option is only supported if an existing storage account, key vault, and container registry are used.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[只有在使用現有的儲存體帳戶、金鑰保存庫和容器登錄時，才支援受管理使用者指派的身分識別選項。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_ManagedIdentity_header" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Managed identity]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[受控識別]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_ManagedIdentity_permissionWarning" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[If you bring your own associated resources, instead of having Azure Machine Learning service create them, you must grant the managed identity roles on those resources. Use the {0} to make the assignments.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[如果您有自己的相關資源，而不是讓 Azure Machine Learning 服務建立，您必須在這些資源授與受控識別角色。請使用 {0} 進行指派。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_ManagedIdentity_permissionWarningLinkText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[role assignment ARM template]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[角色指派 ARM 範本]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_ManagedIdentity_radioGroupTitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Identity type]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[身分識別類型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_ManagedIdentity_systemAssignedText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[System assigned identity]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[系統指派的身分識別]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_ManagedIdentity_userAssignedPickerLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[User assigned identity]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用者指派的身分識別]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_ManagedIdentity_userAssignedPickerRequiredMessage" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[User identity is required]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[需要使用者識別]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_ManagedIdentity_userAssignedPickerSubscriptionMessage" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Workspace and selected user identity must be in the same subscription.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[工作區與選取的使用者識別必須位於相同的訂用帳戶內。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_ManagedIdentity_userAssignedPickerText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Click to select identity]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[按一下即可選取身分識別]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_ManagedIdentity_userAssignedText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[User assigned identity]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用者指派的身分識別]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_ManagedIdentity_userIdentityNameLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[User identity name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用者識別名稱]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_ManagedIdentity_userIdentityResourceGroupLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[User identity resource group]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用者識別資源群組]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_StorageAccountAccess_credentialBasedText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Credential-based access]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[認證型存取]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_StorageAccountAccess_description" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure machine learning allows you to choose between credential-based or identity-based access when connecting to the default storage account. When using identity-based authentication, the Storage Blob Data Contributor role must be granted to the workspace managed identity on the storage account.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure Machine Learning 可讓您在連接至預設儲存體帳戶時，選擇認證型或身分識別型存取。使用身分識別型驗證時，必須將儲存體 Blob 資料參與者角色授予儲存體帳戶上的工作區管理身分識別。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_StorageAccountAccess_header" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Storage account access]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[儲存體帳戶存取]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_StorageAccountAccess_identityBasedText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Identity-based access]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[以身分識別為基礎的存取]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_StorageAccountAccess_learnMoreText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[深入了解]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_StorageAccountAccess_previewLinkText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[(preview)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[(預覽)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_StorageAccountAccess_radioGroupTitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Storage account access type]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[儲存體帳戶存取類型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_StorageAccountAccess_sharedKeyAccessCheckboxLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Disable shared key access]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[停用共用金鑰存取]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_StorageAccountAccess_sharedKeyAccessDisableDocumentationLinkText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[深入了解]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab_Section_StorageAccountAccess_sharedKeyAccessDisableWarning" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Disable shared key access option {0} disables key access at the storage account, any requests to the storage account that are authorized with shared key, including shared access signatures (SAS), will be denied. This option might require additional role configuration to individual users for certain use cases. {1} about disabling shared key access for your workspace's storage account]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[停用共用金鑰存取選項 {0} 後，即會停用儲存體帳戶的金鑰存取，因此任何對儲存體帳戶發出且經共用金鑰授權的要求，包含共用存取簽章 (SAS)，皆會遭到拒絕。針對特定使用案例，此選項可能需要為個別使用者進行額外角色設定。{1}如何為您工作區的儲存體帳戶停用共用金鑰存取]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Disable shared key access option (preview) disables key access at the storage account, any requests to the storage account that are authorized with shared key, including shared access signatures (SAS), will be denied. This option might require additional role configuration to individual users for certain use cases. {0} about disabling shared key access for your workspace's storage account]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AdvancedTab__Key_KeyVaultChangeControl_version" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Version: {version}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[版本: {version}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AppInsights_Dropdown_Hub_info" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The AI hub uses Azure Application Insights to store monitoring information about your deployed models. You may create a new Azure Application Insights resource or select an existing one in your subscription.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[AI 中樞會使用 Azure Application Insights 儲存已部署模型的監視資訊。您可以建立新的 Azure Application Insights 資源，或在訂用帳戶中選取現有的資源。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AppInsights_Dropdown_SettingsBlade_Name_infoDescription" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The name must meet the following requirements:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[名稱必須符合下列要求:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AppInsights_Dropdown_SettingsBlade_Name_infoItem1" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Unique across the resource group]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在資源群組中為唯一的]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AppInsights_Dropdown_SettingsBlade_Name_infoItem2" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Between 1 and 255 characters long]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[長度介於 1 到 255 個字元之間]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AppInsights_Dropdown_SettingsBlade_Name_infoItem3" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Only contain alphanumeric characters, periods, underscores, hyphens, and parenthesis]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[只包含英數字元、句點、底線、連字號及括弧]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AppInsights_Dropdown_SettingsBlade_Name_infoItem4" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Cannot end with a period]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[不得以句號結尾]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AppInsights_Dropdown_SettingsBlade_nameInvalidMessage" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The name must contain between 1 to 255 characters inclusive. The name only allows alphanumeric characters, periods, underscores, hyphens and parenthesis and cannot end in a period.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[名稱長度必須介於 1 到 255 個字元 (含) 之間。名稱只可包含英數字元、句號、底線、連字號及括號，而且不能以句號結尾。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AppInsights_Dropdown_SettingsBlade_nameLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[名稱]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AppInsights_Dropdown_SettingsBlade_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create new application insights]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[建立新的 Application Insights]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AppInsights_Dropdown_createNewAriaLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create new application insights]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[建立新的 Application Insights]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AppInsights_Dropdown_info" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The workspace uses Azure Application Insights to store monitoring information about your deployed models. You may create a new Azure Application Insights resource or select an existing one in your subscription.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[工作區使用 Azure Application Insights 儲存已部署模型的監視資訊。您可以建立新的 Azure Application Insights 資源，或在訂用帳戶中選取現有的資源。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AppInsights_Dropdown_label" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Application insights]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Application Insights]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AppInsights_SummaryTab_label" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Application insights]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[應用程式見解]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetTypeNames_MLAppDeployment_lowerPlural" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[machine learning online deployments]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[機器學習線上部署]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[ml app deployments]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetTypeNames_MLAppDeployment_lowerSingular" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[machine learning online deployment]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[機器學習線上部署]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[ml app deployment]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetTypeNames_MLAppDeployment_plural" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Machine learning online deployments]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[機器學習線上部署]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[ML App Deployments]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetTypeNames_MLAppDeployment_singular" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Machine learning online deployment]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[機器學習線上部署]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[ML App Deployment]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetTypeNames_MLApp_lowerPlural" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[machine learning online endpoints]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[機器學習線上端點]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[ml apps]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetTypeNames_MLApp_lowerSingular" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[machine learning online endpoint]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[機器學習線上端點]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[ml app]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetTypeNames_MLApp_plural" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Machine learning online endpoints]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[機器學習線上端點]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[ML Apps]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetTypeNames_MLApp_singular" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Machine learning online endpoint]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[機器學習線上端點]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[ML App]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetTypeNames_MLRegistry_lowerPlural" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[azure machine learning registries]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure Machine Learning 登錄]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetTypeNames_MLRegistry_lowerSingular" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[azure machine learning registry]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure Machine Learning 登錄]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetTypeNames_MLRegistry_plural" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure Machine Learning registries]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure Machine Learning 登錄]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetTypeNames_MLRegistry_singular" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure Machine Learning registry]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure Machine Learning 登錄]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetTypeNames_MachineLearningServices_Default_lowerPlural" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[workspaces]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[工作區]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetTypeNames_MachineLearningServices_Default_lowerSingular" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[workspace]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[工作區]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetTypeNames_MachineLearningServices_Default_plural" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure Machine Learning workspaces]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure Machine Learning 工作區]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetTypeNames_MachineLearningServices_Default_singular" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure Machine Learning workspace]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure Machine Learning 工作區]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetTypeNames_MachineLearningServices_FeatureStore_lowerPlural" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[feature stores]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[功能存放區]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetTypeNames_MachineLearningServices_FeatureStore_lowerSingular" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[feature store]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[功能存放區]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetTypeNames_MachineLearningServices_FeatureStore_plural" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure Machine Learning feature stores]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure Machine Learning 功能存放區]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetTypeNames_MachineLearningServices_FeatureStore_singular" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure Machine Learning feature store]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure Machine Learning 功能存放區]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetTypeNames_MachineLearningServices_Hub_lowerPlural" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure AI hubs]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure AI 中樞]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Azure AI]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetTypeNames_MachineLearningServices_Hub_lowerSingular" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure AI hub]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure AI 中樞]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Azure AI]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetTypeNames_MachineLearningServices_Hub_plural" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure AI hubs]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure AI 中樞]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Azure AI]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetTypeNames_MachineLearningServices_Hub_singular" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure AI hub]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure AI 中樞]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Azure AI]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetTypeNames_MachineLearningServices_Project_lowerPlural" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure AI projects]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure AI 專案]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[workspace projects]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetTypeNames_MachineLearningServices_Project_lowerSingular" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure AI project]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure AI 專案]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[workspace project]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetTypeNames_MachineLearningServices_Project_plural" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure AI projects]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure AI 專案]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Azure Machine Learning workspace projects]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetTypeNames_MachineLearningServices_Project_singular" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure AI project]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure AI 專案]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Azure Machine Learning workspace project]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetTypeNames_MachineLearningServices_lowerPlural" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[workspaces]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[工作區]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[machine learning workspaces]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetTypeNames_MachineLearningServices_lowerSingular" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[workspace]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[工作區]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[machine learning workspace]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetTypeNames_MachineLearningServices_plural" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure Machine Learning]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure Machine Learning]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Machine learning]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetTypeNames_MachineLearningServices_singular" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure Machine Learning workspace]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure Machine Learning 工作區]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Azure Machine Learning]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_AIStudio_Browse_Commands_CreateAzureAI" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[New Azure AI hub]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[新的 Azure AI 中樞]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[New Azure AI]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_AIStudio_Description" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Your platform to build generative AI solutions and custom copilots]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[您建置生成式 AI 解決方案和自訂副手的平台]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Azure AI Studio description]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_AIStudio_Hub_AIServices_Settings_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create new Azure AI Services]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[建立新的 Azure AI 服務]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_AIStudio_Hub_Essentials_AIServices_label" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[AI Services provider]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[AI 服務提供者]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_AIStudio_Hub_Essentials_DefaultProjectResourceGroup_label" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Project resource group (default)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[專案資源群組 (預設)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_AIStudio_Hub_Overview_Banner_Description_label" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Your Azure AI hub provides enterpise-grade security, and a collaborative environment to build AI solutions. Centrally audit usage and cost, and set up connections to your company resources that all projects can use. {}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[您的 Azure AI 中樞可提供企業級安全性與共同作業環境，以建置 AI 解決方案。集中稽核使用方式和成本，並設定所有專案都可使用的公司資源連結。{}]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Your Azure AI resource provides enterpise-grade security, and a collaborative environment to build AI solutions. Centrally audit usage and cost, and set up connections to your company resources that all projects can use. {}]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_AIStudio_Hub_Overview_Banner_Description_learnMoreText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[learn more about the Azure AI Studio]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[深入了解 Azure AI Studio]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_AIStudio_Hub_Overview_Banner_launchButtonText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Launch Azure AI Studio]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[啟動 Azure AI Studio]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_AIStudio_Hub_Overview_Banner_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Govern the environment for your team in AI Studio]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在 AI Studio 中管理小組的環境]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_AIStudio_KeysAndEndpoints_Command_regenKey1" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Regenerate Key 1]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[重新產生金鑰 1]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_AIStudio_KeysAndEndpoints_Command_regenKey2" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Regenerate Key 2]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[重新產生金鑰 2]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_AIStudio_KeysAndEndpoints_endpoint" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Endpoint]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[端點]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_AIStudio_KeysAndEndpoints_hideKeys" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Hide keys]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[隱藏金鑰]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_AIStudio_KeysAndEndpoints_key1" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[KEY 1]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[金鑰 1]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_AIStudio_KeysAndEndpoints_key2" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[KEY 2]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[金鑰 2]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_AIStudio_KeysAndEndpoints_message" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[These keys are used to access your Azure AI services API. Do not share your keys. Store them securely- for example, using Azure Key Vault. We also recommend regenerating these keys regularly. Only one key is necessary to make an API call. When regenerating the first key, you can use the second key for continued access to the service.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[這些金鑰是用來存取您的 Azure AI 服務 API。請勿分享您的金鑰。安全地儲存它們，例如，使用 Azure Key Vault。我們也建議您定期重新產生這些密鑰。進行 API 呼叫只需要一個金鑰。重新產生第一個金鑰時，您可以使用第二個金鑰繼續存取服務。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_AIStudio_KeysAndEndpoints_regenerateKeys" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Regenerate keys]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[重新產生金鑰]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_AIStudio_KeysAndEndpoints_showKeys" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Show keys]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[顯示金鑰]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_AIStudio_KeysAndEndpoints_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Keys and Endpoints]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[金鑰與端點]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_AIStudio_Keywords" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[AI, Artificial Intelligence, Preview, Deep Learning, Analytics, Data, NLP, Natural Language Processing, CNN, Neural Network, Train, Computer Vision, Models, Data Science, Classification, Regression, Reinforcement Learning, LLM, Chatbot]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[AI, 人工智慧, 預覽, 深度學習, 分析, 資料, NLP, 自然語言處理, CNN, 神經網路, 訓練, 電腦視覺, 模型, 資料科學, 分類, 迴歸, 強化學習, LLM, 聊天機器人]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_AIStudio_Names_lowerPlural" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure AI Studio]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure AI Studio]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_AIStudio_Names_lowerSingular" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure AI Studio]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure AI Studio]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_AIStudio_Names_plural" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure AI Studio]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure AI Studio]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_AIStudio_Names_singular" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure AI Studio]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure AI Studio]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_AIStudio_Project_Overview_Banner_Description_label" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Jumpstart your AI solution development with pre-built templates and work on your project either in code or in the studio.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用預建的範本來啟動您的 AI 解決方案開發，並以程式碼或在工作室中開發您的專案。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_AIStudio_Project_Overview_Banner_launchButtonText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Launch studio]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[啟動工作室]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_AIStudio_Project_Overview_Banner_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Start building in Azure AI Studio]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[開始在 Azure AI Studio 中組建]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_Core42_Names_lowerPlural" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Core42]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Core42]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_Core42_Names_lowerSingular" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Core42]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Core42]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_Core42_Names_plural" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Core42]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Core42]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_Core42_Names_singular" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Core42]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Core42]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_Core42_Page_Description_body" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select Continue, and you'll be redirected to the model catalog in Azure AI Studio where you can finish subscribing and start using this model.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[選取 [繼續]5D;，系統會將您重新導向到 Azure AI Studio 中的模型目錄，以完成訂閱並開始使用此模型。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_Core42_Page_Description_intro" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Thank you for your interest.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[感謝您的青睞。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_Core42_Page_primaryButton" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Continue to Azure AI Studio model catalog]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[繼續 Azure AI Studio 模型目錄]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_Core42_Page_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Continue to Azure AI Studio to use this offer]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[繼續使用 Azure AI Studio 以使用此供應項目]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_Llama2_Names_lowerPlural" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Llama2]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Llama2]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_Llama2_Names_lowerSingular" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Llama2]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Llama2]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_Llama2_Names_plural" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Llama2]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Llama2]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_Llama2_Names_singular" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Llama2]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Llama2]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_Llama2_Page_Description_body" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select Continue, and you'll be redirected to the model catalog in Azure AI Studio where you can finish subscribing and start using this model.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[選取 [繼續]5D;，系統會將您重新導向到 Azure AI Studio 中的模型目錄，以完成訂閱並開始使用此模型。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_Llama2_Page_Description_intro" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Thank you for your interest.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[感謝您的青睞。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_Llama2_Page_primaryButton" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Continue to Azure AI Studio model catalog]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[繼續 Azure AI Studio 模型目錄]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_Llama2_Page_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Continue to Azure AI Studio to use this offer]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[繼續使用 Azure AI Studio 以使用此供應項目]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_Mistral_Names_lowerPlural" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Mistral]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Mistral]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_Mistral_Names_lowerSingular" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Mistral]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Mistral]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_Mistral_Names_plural" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Mistral]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Mistral]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_Mistral_Names_singular" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Mistral]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Mistral]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_Mistral_Page_Description_body" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select Continue, and you'll be redirected to the model catalog in Azure AI Studio where you can finish subscribing and start using this model.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[選取 [繼續]5D;，系統會將您重新導向到 Azure AI Studio 中的模型目錄，以完成訂閱並開始使用此模型。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_Mistral_Page_Description_intro" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Thank you for your interest.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[感謝您的青睞。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_Mistral_Page_primaryButton" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Continue to Azure AI Studio model catalog]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[繼續 Azure AI Studio 模型目錄]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_Mistral_Page_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Continue to Azure AI Studio to use this offer]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[繼續使用 Azure AI Studio 以使用此供應項目]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_ModelProvider_Names_lowerPlural" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model Provider]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型提供者]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_ModelProvider_Names_lowerSingular" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model Provider]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型提供者]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_ModelProvider_Names_plural" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model Provider]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型提供者]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_ModelProvider_Names_singular" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model Provider]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型提供者]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_ModelProvider_Page_Description_body" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select Continue, and you'll be redirected to the model catalog in Azure AI Studio where you can finish subscribing and start using this model.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[選取 [繼續]5D;，系統會將您重新導向到 Azure AI Studio 中的模型目錄，以完成訂閱並開始使用此模型。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_ModelProvider_Page_Description_intro" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Thank you for your interest.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[感謝您的青睞。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_ModelProvider_Page_primaryButton" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Continue to Azure AI Studio model catalog]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[繼續 Azure AI Studio 模型目錄]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_ModelProvider_Page_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Continue to Azure AI Studio to use this offer]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[繼續使用 Azure AI Studio 以使用此供應項目]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_Nixtla_Names_lowerPlural" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Nixtla]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Nixtla]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_Nixtla_Names_lowerSingular" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Nixtla]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Nixtla]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_Nixtla_Names_plural" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Nixtla]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Nixtla]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_Nixtla_Names_singular" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Nixtla]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Nixtla]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_Nixtla_Page_Description_body" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select Continue, and you'll be redirected to the model catalog in Azure AI Studio where you can finish subscribing and start using this model.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[選取 [繼續]5D;，系統會將您重新導向到 Azure AI Studio 中的模型目錄，以完成訂閱並開始使用此模型。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_Nixtla_Page_Description_intro" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Thank you for your interest.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[感謝您的青睞。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_Nixtla_Page_primaryButton" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Continue to Azure AI Studio model catalog]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[繼續 Azure AI Studio 模型目錄]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_Nixtla_Page_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Continue to Azure AI Studio to use this offer]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[繼續使用 Azure AI Studio 以使用此供應項目]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_cohere_Names_lowerPlural" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Cohere]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Cohere]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_cohere_Names_lowerSingular" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Cohere]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Cohere]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_cohere_Names_plural" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Cohere]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Cohere]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_cohere_Names_singular" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Cohere]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Cohere]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_cohere_Page_Description_body" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select Continue, and you'll be redirected to the model catalog in Azure AI Studio where you can finish subscribing and start using this model.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[選取 [繼續]5D;，系統會將您重新導向到 Azure AI Studio 中的模型目錄，以完成訂閱並開始使用此模型。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_cohere_Page_Description_intro" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Thank you for your interest.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[感謝您的青睞。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_cohere_Page_primaryButton" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Continue to Azure AI Studio model catalog]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[繼續 Azure AI Studio 模型目錄]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_cohere_Page_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Continue to Azure AI Studio to use this offer]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[繼續使用 Azure AI Studio 以使用此供應項目]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_description" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Workspaces are where you manage all the models, assets, and data related to your machine learning projects. Create one now to start using Azure Machine Learning.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[您可在工作區管理與機器學習專案相關的所有模型、資產與資料。立即建立一個工作區，以開始使用 Azure Machine Learning。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Create a machine learning workspace to manage machine learning solutions through the entire data science lifecycle.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssetType_keywords" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[ML, AML, Machine Learning, AI, Artificial Intelligence, Preview, Deep Learning, Analytics, Data, NLP, Natural Language Processing, CNN, Neural Network, Workbench, Train, Notebooks, AutoML, Designer, Computer Vision, Models, Data Science, Classification, Regression, Reinforcement Learning]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[ML, AML, Machine Learning, AI, 人工智慧, 預覽, 深度學習, 分析, 資料, NLP, 自然語言處理, CNN, 神經網路, Workbench, 定型, Notebooks, AutoML, 設計工具, 電腦視覺, 模型, 資料科學, 分類, 迴歸, 強化學習]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[ML, AML, Machine Learning, AI, Artificial Intelligence, Preview, Deep Learning, Analytics, Data, NLP, Natural Language Processing, CNN, Neural Network, Workbench, Train]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssociatedResource_AppInsights_ChangeBlade_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select an application insights]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[選取一個 Application Insights]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssociatedResource_AppInsights_Properties_changeText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Change application insights]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[變更 Application Insights]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssociatedResource_AppInsights_ReactView_ChangeAppInsights_appInsightsLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Application insights]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Application Insights]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssociatedResource_AppInsights_ReactView_ChangeAppInsights_appInsightsUpdatingSpinner" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Application insights updating...]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Application Insights 更新中...]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssociatedResource_AppInsights_ReactView_ChangeAppInsights_error" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[An error occurred while updating the application insights for this resource.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[更新此資源的 Application Insights 時發生錯誤。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssociatedResource_AppInsights_ReactView_ChangeAppInsights_errorTitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Error updating application insights]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[更新 Application Insights 時發生錯誤]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssociatedResource_AppInsights_ReactView_ChangeAppInsights_footerCancel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Discard]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[捨棄]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssociatedResource_AppInsights_ReactView_ChangeAppInsights_footerSave" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Save]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[儲存]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssociatedResource_AppInsights_ReactView_ChangeAppInsights_notFoundError" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The resource being updated could not be found.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[找不到要更新的資源。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssociatedResource_AppInsights_ReactView_ChangeAppInsights_unauthorizedError" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You do not have permission to update the application insights for this resource.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[您沒有更新此資源的 Application Insights 的權限。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssociatedResource_AppInsights_ReactView_Dropdown_emptyMessage" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[No application insights resources found]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[找不到任何 Application Insights 資源。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssociatedResource_AppInsights_ReactView_Dropdown_errorLoadingMessage" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Error loading application insights]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[載入 Application Insights 時發生錯誤]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssociatedResource_AppInsights_ReactView_Dropdown_noMatchMessage" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[No results matching {0}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[沒有符合 {0} 的結果]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssociatedResource_AppInsights_ReactView_Dropdown_placeholder" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select an application insights]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[選取一個 Application Insights]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Select an application insights instance]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssociatedResource_ContainerRegistry_ChangeBlade_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select a container registry]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[選取容器登錄]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssociatedResource_ContainerRegistry_Properties_changeText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Change container registry]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[變更容器登錄]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssociatedResource_ContainerRegistry_ReactView_ChangeContainerRegistry_containerRegistryLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Container registry]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[容器登錄]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssociatedResource_ContainerRegistry_ReactView_ChangeContainerRegistry_containerRegistryUpdatingSpinner" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Container registry updating...]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正在更新容器登錄...]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssociatedResource_ContainerRegistry_ReactView_ChangeContainerRegistry_error" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[An error occurred while updating the container registry for this resource.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[更新此資源的容器登錄時發生錯誤。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssociatedResource_ContainerRegistry_ReactView_ChangeContainerRegistry_errorTitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Error updating container registry]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[更新容器登錄時發生錯誤]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssociatedResource_ContainerRegistry_ReactView_ChangeContainerRegistry_footerCancel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Discard]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[捨棄]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssociatedResource_ContainerRegistry_ReactView_ChangeContainerRegistry_footerSave" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Save]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[儲存]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssociatedResource_ContainerRegistry_ReactView_ChangeContainerRegistry_notFoundError" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The resource being updated could not be found.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[找不到要更新的資源。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssociatedResource_ContainerRegistry_ReactView_ChangeContainerRegistry_unauthorizedError" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You do not have permission to update the container registry for this resource.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[您沒有為此資源更新容器登錄的權限。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssociatedResource_ContainerRegistry_ReactView_Dropdown_emptyMessage" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[No container registry resources found]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[找不到容器登錄資源]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssociatedResource_ContainerRegistry_ReactView_Dropdown_errorLoadingMessage" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Error loading container registry]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[載入容器登錄時發生錯誤]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssociatedResource_ContainerRegistry_ReactView_Dropdown_noMatchMessage" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[No results matching {0}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[沒有符合 {0} 的結果]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssociatedResource_ContainerRegistry_ReactView_Dropdown_placeholder" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select a container registry]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[選取容器登錄]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Select an container registry instance]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssociatedResource_Dropdown_SettingsBlade_discardButtonText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Discard]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[捨棄]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssociatedResource_Dropdown_SettingsBlade_saveButtonText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Save]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[儲存]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssociatedResource_Dropdown_createNewLinkText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create new]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[新建]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssociatedResource_Dropdown_filterPlaceholder" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select existing...]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[選取現有的...]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssociatedResource_Dropdown_newResourceText" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[(new) {0}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[(新增) {0}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AssociatedResource_Dropdown_none" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[None]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[無]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BasicsTab_AIServiceSectionHeader" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure AI services base models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure AI 服務基礎模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BasicsTab_HubName_BalloonContent" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Name of the AI hub]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[AI 中樞的名稱]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BasicsTab_InfoText_AdditionalResourceInfoText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[For your convenience, these resources are added automatically to the workspace, if regionally available: ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[為方便起見，如果當地可以使用，這些資源就會自動新增到工作區: ]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BasicsTab_InfoText_AzureApplicationInsights" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure Application Insights]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure Application Insights]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BasicsTab_InfoText_AzureKeyVault" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure Key Vault]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure Key Vault]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BasicsTab_InfoText_AzureStorage" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure Storage]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure 儲存體]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BasicsTab_Organization" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Organization]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[組織]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BasicsTab_Region_BalloonContent" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Compute targets can only be created in the same region as the workspace. Ensure the selected region has the virtual machine series needed for your workspace compute targets.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[計算目標只能在與工作區相同的區域中建立。請確認所選區域具有工作區計算目標所需的虛擬機器系列。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BasicsTab_Region_LearnMoreComputeTargets" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more about compute targets]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[深入了解計算目標]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BasicsTab_Region_ViewAvailableVirtualMachines" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[View available virtual machines series by region]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[依區域檢視可用的虛擬機器系列]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BasicsTab_RegistryDetails" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Registry details]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[登錄詳細資料]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BasicsTab_RegistryName_BalloonContent" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The registry name must be unique within your resource group.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[資源群組中的登錄名稱不得重複。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BasicsTab_ResourceGroup_BalloonContent" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A resource group is a collection of resources that share the same life cycle, permissions, and policies.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[資源群組是資源的集合，擁有相同的生命週期、權限及原則。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BasicsTab_Subscription_BalloonContent" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[All resources in an Azure subscription are billed together.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure 訂用帳戶中的所有資源皆會一併計費。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BasicsTab_WarningText_WorkspaceErrorMessageText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The selected subscription doesn’t have permissions to register the resource provider.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[選取的訂用帳戶無權註冊資源提供者。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BasicsTab_WorkspaceDetails" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Workspace details]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[工作區詳細資料]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BasicsTab_WorkspaceEdition_Basic" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Basic]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[基本]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BasicsTab_WorkspaceEdition_Enterprise" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Enterprise]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[企業]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BasicsTab_WorkspaceEdition_ViewFullPricingDetails" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[View full pricing details]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[檢視完整定價詳細資料]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BasicsTab_WorkspaceHubDetails" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Resource details]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[資源詳細資料]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Azure AI details]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BasicsTab_WorkspaceName_BalloonContent" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The workspace name must be unique within your resource group.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[資源群組中的工作區名稱不得重複。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Browse_addMachineLearningHubLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[New Azure AI hub]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[新的 Azure AI 中樞]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[New Azure AI]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Browse_addMachineLearningLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[New workspace]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[新增工作區]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Browse_addMachineLearningRegistryLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[New registry]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[新增登錄]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ColumnCreationTime" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Created on]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[建立日期]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ColumnMachineLearningWorkspaceId" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Workspace ID]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[工作區 ID]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CommandCancel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Cancel]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[取消]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CommandDelete" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Delete]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[刪除]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CommandDownloadConfig" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Download config.json]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[下載 config.json]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CommandDownloadConfigTooltip" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Use this file to load the workspace configuration in your Azure ML SDK notebook or Python script]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用此檔案來載入您 Azure ML SDK 筆記本或 Python 指令碼中的工作區設定]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Commands_delete" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Delete]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[刪除]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ConfirmationMachineLearningDelete" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Are you sure you want to delete Workspace {0}?]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[確定要刪除工作區 {0} 嗎?]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ConfirmationMachineLearningRegistryDelete" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Are you sure you want to delete Registry {0}?]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[是否確定要刪除登錄 {0}?]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ContainerRegistry_Dropdown_SettingsBlade_Name_infoDescription" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The name must meet the following requirements:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[名稱必須符合下列要求:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ContainerRegistry_Dropdown_SettingsBlade_Name_infoItem1" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Unique across all container registries in Azure]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在 Azure 中的所有容器登錄為唯一的]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ContainerRegistry_Dropdown_SettingsBlade_Name_infoItem2" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Between 5 and 50 characters long]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[長度介於 5 到 50 個字元之間]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ContainerRegistry_Dropdown_SettingsBlade_Name_infoItem3" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Only contain alphanumeric characters]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[只包含英數字元]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ContainerRegistry_Dropdown_SettingsBlade_Sku_info" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[All SKUs provide the same programmatic capabilities. Choosing a higher SKU will provide more performance and scale.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[所有 SKU 都提供相同的程式設計功能。選擇較高的 SKU 可提供較高的效能和規模。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ContainerRegistry_Dropdown_SettingsBlade_Sku_infoLearnMore" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[深入了解]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ContainerRegistry_Dropdown_SettingsBlade_nameInvalidMessage" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Resource names may contain alpha numeric characters only and must be between 5 and 50 characters.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[資源名稱只能包含英數字元，而且長度必須介於 5 到 50 個字元之間。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ContainerRegistry_Dropdown_SettingsBlade_nameLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[名稱]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ContainerRegistry_Dropdown_SettingsBlade_skuBasic" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Basic]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[基本]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ContainerRegistry_Dropdown_SettingsBlade_skuLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[SKU]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[SKU]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ContainerRegistry_Dropdown_SettingsBlade_skuPremium" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Premium]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[進階]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ContainerRegistry_Dropdown_SettingsBlade_skuStandard" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Standard]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[標準]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ContainerRegistry_Dropdown_SettingsBlade_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create new container registry]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[建立新的容器登錄]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ContainerRegistry_Dropdown_createNewAriaLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create new container registry]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[建立新的容器登錄]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ContainerRegistry_Dropdown_info" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A container registry is used to register docker images used in training and deployments. To minimize costs, a new Azure Container Registry resource is created only after you build your first image. Alternatively, you may choose to create the resource now or select an existing one in your subscription.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[容器登錄用於登錄訓練和部署所使用的 Docker 映像。為了將成本降至最低，只有在建置第一個映像之後，才會建立新的 Azure Container Registry 資源。您也可以選擇立即建立資源，或在訂用帳戶中選取現有的資源。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ContainerRegistry_Dropdown_label" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Container registry]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[容器登錄]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ContainerRegistry_SummaryTab_label" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Container registry]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[容器登錄]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBladeCreateButtonName" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[建立]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBladeDescriptionLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Description]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[描述]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBladeFriendlyNameInfo" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Display name of the AI hub that will be displayed in AI studio]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[將在 AI Studio 中顯示之 AI 中樞的顯示名稱]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBladeFriendlyNameLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Friendly name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[易記名稱]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBladeHubAIServices_ariaLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Connect AI Services, including OpenAI]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[連線 AI 服務，包括 OpenAI]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Create new a Azure OpenAI service]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBladeHubAIServices_info" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Provider of Microsoft-maintained base models. Managed in your Azure subscription as a separate resource.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Microsoft 維護的基礎模型提供者。在您的 Azure 訂用帳戶中管理為另一個資源。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Select an existing Azure AI Services instance. This resource type provides endpoints to pre-built AI models including Speech, Content Safety and Azure OpenAI.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBladeHubAIServices_label" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Connect AI Services incl. OpenAI]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[連線 AI 服務，包括 OpenAI]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Azure OpenAI service]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBladeHubAIServices_skipText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Skip connecting AI services]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[略過連線 AI 服務]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBladeHubConfigDefaultResourceGroup_info" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[When a user does not specify a resource group for their project, this resource group will be used by default. The project creator gets granted an Azure RBAC owner role assignment on the project instance]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[當使用者未指定其專案的資源群組時，預設即會使用這個資源群組。專案建立者在專案執行個體上獲得 Azure RBAC 擁有者角色指派]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBladeHubConfigDefaultResourceGroup_label" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Default project resource group]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[預設專案資源群組]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBladeHubSubtitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create an Azure AI hub resource]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[建立 Azure AI 中樞資源]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Create an Azure AI hub]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBladeHubTitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure AI hub]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure AI 中樞]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Azure AI]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBladeLocationLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Location]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[位置]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBladeMainTabTitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Main]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[主要]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBladeRegistryDescriptionKey" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[description]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[描述]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBladeRegistryDescriptionLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Description]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[描述]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBladeRegistryNameLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[名稱]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBladeRegistrySubtitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create a machine learning registry]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[建立機器學習登錄]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBladeResourceGroupLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Resource Group]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[資源群組]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBladeReviewCreateButtonName" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Review + Create]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[評論及建立]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBladeReviewTabTitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Review]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[檢閱]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBladeSubscriptionIdLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Subscription Id]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[訂用帳戶識別碼]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBladeSubscriptionLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Subscription]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[訂用帳戶]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBladeSubtitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create a machine learning workspace]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[建立機器學習工作區]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Create]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBladeTagTabTitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Tags]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[標籤]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBladeTemplateErrorText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Template Error]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[範本錯誤]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBladeTitle" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure Machine Learning]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure Machine Learning]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Machine learning]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBladeWorkspaceCreationProgressNotification" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Workspace creation in progress]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正在建立工作區]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBladeWorkspaceDeploymentErrorText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Workspace deployment Error]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[工作區部署錯誤]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBladeWorkspaceNameLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[名稱]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Workspace name]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Advanced_Review_Encryption_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Encryption]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[加密]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Advanced_Review_Identity_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Identity]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[身分識別]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Advanced_Review_Resources_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Resources]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[資源]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Advanced_Review_byoCosmosText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Cosmos DB for customer managed keys]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[客戶管理金鑰的 Cosmos DB]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Advanced_Review_byoSearchText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Search service for customer managed keys]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[客戶管理金鑰的搜尋服務]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Advanced_Review_byoStorageText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Storage account for customer managed keys]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[客戶管理金鑰的儲存體帳戶]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Advanced_Review_byoValueFormat" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{0} ({1})]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{0} ({1})]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Advanced_Review_credentialBasedStorageAccountAccess" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Credential-based]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[以認證為基礎]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Advanced_Review_customerManagedKeys" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Customer-managed keys]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[客戶管理的金鑰]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Advanced_Review_disabled" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Disabled]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[停用]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Advanced_Review_enableHBIFlag" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Enable HBI Flag]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[啟用 HBI 旗標]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Advanced_Review_enabled" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Enabled]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[啟用]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Advanced_Review_encryptionType" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Encryption type]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[加密類型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Advanced_Review_identityBasedStorageAccountAccess" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Identity-based]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[以身分識別為基礎]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Advanced_Review_keyURI" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Key URI]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[金鑰 URI]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Advanced_Review_keyVault" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Key vault]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[金鑰保存庫]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Advanced_Review_microsoftManagedKeys" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Microsoft-managed keys]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Microsoft 管理的金鑰]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Advanced_Review_sharedKeyAccess" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Shared key access]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[共用金鑰存取]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Advanced_Review_storageAccountAccessType" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Storage account access type]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[儲存體帳戶存取類型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_ConnectivityMethod_allNetworks" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Enable public access from all networks]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在所有網路啟用公用存取]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Public endpoint (all networks)]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_ConnectivityMethod_private" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Disable public access and use private endpoints]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[停用公用存取並使用私人端點]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Private endpoint]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_ConnectivityMethod_skuPrivateEndpointErrorMessage" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Private with Internet Outbound and Private with Approved Outbound requires premium SKU container registry for having a private endpoint connection. You can create a new premium container registry or select an existing premium container registry in the Basics tab to use with this private workspace.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[[有網際網路輸出的私人專案]5D; 和 [具有核准輸出的私人專案]5D; 需要進階 SKU 容器登錄才能擁有私人端點連線。您可以建立新的進階容器登錄，或在 [基本]5D; 索引標籤中選取現有的進階容器登錄，以用於此私人工作區。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[This workspace is using a private link endpoint and is only compatible with premium SKU container registries. You can create a new premium container registry or select an existing premium container registry in the Basics tab to use with this private workspace.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_Hub_networkIsolationDescription" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Projects associated to an Azure AI hub share network, and can access resources in your virtual network without additional configuration. Choose the type of network isolation you need, from not isolated at all to an entirely separate virtual network managed by Azure Machine Learning.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[與 Azure AI 中樞共用網路相關聯的專案，而且不需要其他設定即可存取您的虛擬網路中的資源。選擇您需要的網路隔離類型，從完全不隔離到由 Azure Machine Learning 管理的完全分開的虛擬網路。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Projects associated to an Azure AI resource share network, and can access resources in your virtual network without additional configuration. Choose the type of network isolation you need, from not isolated at all to an entirely separate virtual network managed by Azure Machine Learning.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_NetworkIsolation_AllowInternetOutbound_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Allow Internet Outbound]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[允許網際網路輸出]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_NetworkIsolation_AllowOnlyApprovedOutbound_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Allow Only Approved Outbound]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[只允許核准的輸出]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_NetworkIsolation_Disabled_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Disabled]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[已停用]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_NetworkIsolation_PrivateApprovedOutbound_descriptionItemOne" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Workspace is accessed via private endpoint]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[工作區是透過私人端點存取]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_NetworkIsolation_PrivateApprovedOutbound_descriptionItemThree" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Outbound data movement is restricted to approved targets]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[輸出資料移動限制為核准的目標]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_NetworkIsolation_PrivateApprovedOutbound_descriptionItemTwo" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Compute can access allowlisted resources only]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[計算只能存取允許清單的資源]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_NetworkIsolation_PrivateApprovedOutbound_learnMore" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more about data exfiltration protection]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[深入了解資料外流保護]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_NetworkIsolation_PrivateApprovedOutbound_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Private with Approved Outbound]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[具有核准輸出的私人項目]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_NetworkIsolation_PrivateInternetOutbound_descriptionItemOne" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Workspace is accessed via private endpoint]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[工作區是透過私人端點存取]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_NetworkIsolation_PrivateInternetOutbound_descriptionItemThree" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Outbound data movement is unrestricted]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[輸出資料移動不受限制]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_NetworkIsolation_PrivateInternetOutbound_descriptionItemTwo" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Compute can access private resources]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[計算可以存取私人資源]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_NetworkIsolation_PrivateInternetOutbound_learnMore" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more about private networks]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[深入了解私人連結]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_NetworkIsolation_PrivateInternetOutbound_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Private with Internet Outbound]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[有網際網路輸出的私人]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_NetworkIsolation_Public_descriptionItemOne" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Workspace is accessed via public endpoint]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[工作區是透過私人端點存取]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_NetworkIsolation_Public_descriptionItemThree" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Outbound data movement is unrestricted]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[輸出資料移動不受限制]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_NetworkIsolation_Public_descriptionItemTwo" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Compute can access public resources]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[計算可以存取公用資源]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_NetworkIsolation_Public_learnMore" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more about public networks]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[深入了解公用網路]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_NetworkIsolation_Public_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Public]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[公用]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_NetworkIsolation_skuErrorMessage" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Allow Internet Outbound and Allow Only Approved Outbound requires premium SKU container registry for having a private endpoint connection.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[[允許網際網路輸出]5D; 和 [僅允許核准的輸出]5D; 需要進階 SKU 容器登錄才能有私人端點連線。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_PrivateEndpoints_add" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Add]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[新增]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_PrivateEndpoints_clickToAdd" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Click on add to create a private endpoint]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[按一下新增來建立私人端點]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_PrivateEndpoints_locationMismatch" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Workspace ({0}) and private endpoint connection ({1}) must be in the same location in order to get compute instance and clusters accessed properly in the virtual network.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[工作區 ({0}) 和私人端點連線 ({1}) 必須位於相同位置，才能讓計算執行個體和叢集在虛擬網路中正確存取。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_PrivateEndpoints_name" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[名稱]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_PrivateEndpoints_nameAndSubResource" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{0} ({1})]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{0} ({1})]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_PrivateEndpoints_nameSubResourceAndDns" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{0} ({1}) ({2})]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{0} ({1}) ({2})]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_PrivateEndpoints_noContent" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[-]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[-]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_PrivateEndpoints_registrySubResourceHelp" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This is the specific sub-resource for the new registry that this private endpoint will be able to access.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此為新登錄的特定子資源，可供此私人端點存取。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_PrivateEndpoints_registrySubResourceLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Registry sub-resource]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[登錄子資源]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_PrivateEndpoints_subResourceHelp" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This is the specific sub-resource for the new workspace that this private endpoint will be able to access.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[這是新工作區的特定子資源，可供此私人端點存取。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_PrivateEndpoints_subResourceLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Workspace sub-resource]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[工作區子資源]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_PrivateEndpoints_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Private endpoints]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[私人端點]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_PrivateEndpoints_type" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Private Endpoint]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[私人端點]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_WorkspaceApprovedOutbound_OutboundAccess_recommendedTargetDescription" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[There are several optional outbound targets recommended for your workspace in scenarios such as AutoML and Data Labeling. You can modify or delete them,]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在 AutoML 和資料標籤等案例中，您的工作區建議有數個選擇性輸出目標。您可以修改或刪除它們，]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_WorkspaceApprovedOutbound_OutboundAccess_recommendedTargetLearnMore" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more about recommended targets]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[深入了解建議的目標]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_WorkspaceApprovedOutbound_OutboundAccess_requiredTargetDescription" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[There are a few outbound targets added by Azure Machine Learning that are required for your workspace to access things like storage,  notebooks, and deployment environments.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[您的工作區需要 Azure Machine Learning 新增一些輸出目標，才能存取儲存空間、筆記本和部署環境等專案。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_WorkspaceApprovedOutbound_OutboundAccess_requiredTargetLearnMore" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more about required targets]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[深入了解必要的目標]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_WorkspaceInternetOutbound_OutboundAccess_useMyOwnVirtualNetwork" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Use my own virtual network]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用我自己的虛擬網路]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_WorkspaceInternetOutbound_OutboundAccess_virtualNetworkManagedByAML" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Use a virtual network managed by Azure Machine Learning]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用由 Azure Machine Learning 管理的虛擬網路]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_WorkspaceInternetOutbound_OutboundAccess_virtualNetworkManagedByAMLDescription" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Private endpoints are required for your workspace to access private resources like storage, notebooks, and deployment environments. You can also add your additional private link targets here for your custom scenarios.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[您的工作區需要私人端點，才能存取儲存空間、筆記本和部署環境等私人資源。您也可以在此新增自訂案例的其他私人連結目標。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_WorkspaceInternetOutbound_OutboundAccess_virtualNetworkManagedByAMLLearnMore" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more about required private link targets]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[深入了解必要的私人連結目標]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_WorkspaceInternetOutbound_PrivateNetworkSettings_description" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Add private endpoints people can use to access your workspace, and choose how to manage outbound access from your workspace to things like Storage Accounts, Key Vaults and Registries.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[新增私人端點人員可用來存取您的工作區，並選擇如何從您的工作區管理儲存體帳戶、Key Vault 和登錄等的輸出存取。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_WorkspaceInternetOutbound_PrivateNetworkSettings_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Private network settings]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[私人網路設定]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_WorkspaceInternetOutbound_targetDescription" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[There are a few private endpoints required for your workspace to access private resources like storage. You can also add your additional private link targets here for your custom scenarios.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[工作區需要一些私人端點，才能存取儲存空間等私人資源。您也可以在此新增自訂案例的其他私人連結目標。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_WorkspaceInternetOutbound_targetLearnMoreLink" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more about required private link target]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[深入了解必要的私人連結目標]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_WorkspaceOutboundAccess_activeStatusMessage" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The rule has been applied and effective.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[規則已套用且有效。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_WorkspaceOutboundAccess_addUserDefinedOutboundRules" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Add user-defined outbound rules]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[新增使用者定義的輸出規則]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_WorkspaceOutboundAccess_connectionName" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Connection Name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[連線名稱]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_WorkspaceOutboundAccess_deleteUserDefinedOutboundRules" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Delete user-defined outbound rules]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[刪除使用者定義的輸出規則]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_WorkspaceOutboundAccess_destination" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Destination]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[目的地]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_WorkspaceOutboundAccess_destinationType" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Destination Type]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[目的地類型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_WorkspaceOutboundAccess_inactivePrivateEndpointStatusMessage" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The rule will become active when managed network is provisioned, otherwise please check if approval is pending for the target resource.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[當佈建受控網路時，規則會變成使用中，否則請檢查目標資源的核准是否擱置中。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_WorkspaceOutboundAccess_inactiveStatusMessage" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The rule will become active when managed network is provisioned.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[當佈建受控網路時，規則會變成使用中。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_WorkspaceOutboundAccess_parentRules" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Parent Rules]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[父代規則]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_WorkspaceOutboundAccess_status" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Status]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[狀態]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_WorkspacePrivateOutbound_addUserDefinedOutboundRuleText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You can also add your own outbound targets here for your custom scenarios.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[您也可以在此為自訂案例新增自己的輸出目標。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_WorkspacePrivateOutbound_dependencyOutboundRules" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Dependency outbound rules]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[相依性輸出規則]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_WorkspacePrivateOutbound_recommendedOutboundRules" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Recommended outbound rules]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[建議的輸出規則]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_WorkspacePrivateOutbound_requiredOutboundRules" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Required outbound rules]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[必要的輸出規則]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_WorkspacePrivateOutbound_userDefinedOutboundRules" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[User-defined outbound rules]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用者定義的輸出規則]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_connectivityMethod" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Connectivity method]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[連線方法]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_displayPrivateDnsZone" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Private DNS Zone]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[私人 DNS 區域]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_displayRegion" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Region]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[區域]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_displayResourceGroup" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Resource group]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[資源群組]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_displaySubnet" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Subnet]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[子網路]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_displaySubscription" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Subscription]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[訂用帳戶]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_displayTargetResourceType" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Target resource type]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[目標資源類型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_networkIsolationDescription" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Choose the type of network isolation you need for your workspace, from not isolated at all to an entirely separate virtual network managed by Azure Machine Learning.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[選擇您的工作區所需的網路隔離類型，從完全不隔離到由 Azure Machine Learning 管理之完全分開的虛擬網路。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_networkIsolationLearnMore" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more about managed network isolation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[深入了解受管理的網路隔離]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_networkIsolationTitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Network isolation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[網路隔離]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_outboundRulesGridEmptyMessage" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Missing required outbound access rules.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[遺漏必要的輸出存取規則。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_outboundRulesNotLoadedMessage" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Configure basic settings in order to create outbound rules.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[設定基本設定以建立輸出規則。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_outboundRulesPublicInternetEnabledMessage" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[No required outbound rules while the outbound access to the public internet is enabled.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[啟用公用網際網路的輸出存取時，不需要輸出規則。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_overviewDescription" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You can connect to your workspace either publicly or privately using a private endpoint.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[您可以公開連線到工作區，或使用私人端點私下連線。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_overviewTitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Network connectivity]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[網路連線]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_privateEndpointDescription" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create a private endpoint to allow a private connection to this resource.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[請建立私人端點以允許私人連線連到此資源。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_privateEndpointTitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Private endpoint]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[私人端點]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_registryOverviewDescription" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You can connect to your registry either publicly or privately using a private endpoint.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[您可以公開連線到登錄，或使用私人端點私下連線。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Networking]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[網路]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_workspaceApprovedOutbound_PrivateNetworkSettings_description" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Add private endpoints people can use to access your workspace, and manage the outbound targets to which the workspace can access]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[新增私人端點人員可用來存取您的工作區，以及管理工作區可存取的輸出目標]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_workspaceApprovedOutbound_PrivateNetworkSettings_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Private network and data exfiltration settings]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[私人網路和資料外流設定]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_workspaceInboundAccessTitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Workspace Inbound access]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[工作區輸入存取]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Network_workspaceOutboundAccessTitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Workspace Outbound access]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[工作區輸出存取]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateBlade_Workspace_Type" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Workspce type]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[工作區類型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Create_CreateButton_label" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[建立]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Create_Wizard_title" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create Azure Machine Learning]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[建立 Azure Machine Learning]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_QuotaConfigurationBlade_filterText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Enter at least 3 characters to search for workspaces...]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[輸入至少 3 個字元以搜尋工作區...]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_QuotaConfigurationBlade_noItemsText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[No workspaces found to display.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[找不到任何可顯示的工作區。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_QuotaConfigurationBlade_quotaConfigNotAvailable" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Quotas cannot be configured due to following error: {0}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[因為發生下列錯誤，所以無法設定配額: {0}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_QuotaConfigurationBlade_subTitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Configure your quota across your subscription here]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在此設定您的訂用帳戶配額]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_QuotaConfigurationBlade_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Configure quota]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[設定配額]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_QuotaConfigurationBlade_workspacesNotAvailable" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You can only configure quota for workspaces which have been configured before since the list of workspaces of this subscription cannot be reached due to following error: {0}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[由於發生下列錯誤，您無法設定之前已設定之工作區的配額，因為無法到達此訂閱的工作區清單: {0}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_QuotaUsageBlade_crossLocationUsagesNotAvailable" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Cross location workspace information (whether the workspace owning a compute is in a different location than the selected location) cannot be displayed due to following error but you can still see all the usages: {0}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[因為發生下列錯誤，所以無法顯示跨位置工作區資訊 (擁有計算的工作區是否與所選位置處於不同位置)，但您仍可看到所有使用量: {0}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_QuotaUsageBlade_quotaInfoLearnMore" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[深入了解]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_QuotaUsageBlade_quotaInfoText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure Machine Learning quota is preconfigured limits, which specifies the maximum number of cores you can use at any given moment.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure Machine Learning 配額是預先設定的限制，指定了在任何指定時間可使用的核心數目上限。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Azure Machine Learning quota is preconfigured limits, which specifies the maximum number of cores you can use at any given moment. This summary shows the quota usage and limits across all workspaces in your subscription.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_QuotaUsageBlade_usagesNotAvailable" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Compute usages cannot be displayed do to following error: {0}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[因為發生下列錯誤，所以無法顯示計算使用量: {0}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_UsageGrid_coreUtilizationColumnText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Cores utilization]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[核心使用率]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_UsageGrid_coresUsageTextFormat" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{0} of {1} cores utilized]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[已使用 {0} 個核心 (共 {1} 個)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_UsageGrid_crossLocationEnableText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Enable cross location compute toggle]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[啟用跨位置計算切換]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_UsageGrid_crossLocationWarningCoresFormat" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{0} cores]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{0} 個核心]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_UsageGrid_crossLocationWarningLine1" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You are using {0} in other locations.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[您正在使用其他位置中的 {0}。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_UsageGrid_crossLocationWarningLine2" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{1} to view them.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{1}，加以檢視。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_UsageGrid_familyNameFormat" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Standard {0} Family vCPUs]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[標準 {0} 系列 vCPU]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_UsageGrid_totalCoresTextFormat" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{0} out of {1} ({2})]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{0} / {1} ({2})]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_UsageGrid_usageColumnText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Usage]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用量]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_UsageGrid_usagePercentageColumnText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Usage percentage]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用量百分比]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_UsageGrid_vmFamiliesColumnText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[VM Families]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[VM 系列]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_UsageGrid_workspaceCrossLocationDisplayText" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{0} ({1}, {2})]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{0} ({1}，{2})]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_UsageGrid_workspaceDisplayText" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{0} ({1})]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{0} ({1})]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_UsageGrid_workspacesColumnText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Workspaces]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[工作區]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_configureQuotaButtonText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Configure quota]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[設定配額]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_dedicatedCoresSectionLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Dedicated cores usage]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[專用核心使用量]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_loadingText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Loading...]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正在載入...]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_lowPriorityCoresSectionLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Low priority cores usage]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[低優先順序核心使用量]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_lowPriorityUsageInfoText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Please note that the number of low-priority cores per subscription is single value accross VM families.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[請注意，每個訂閱的低優先順序核心數在 VM 系列中都是單一值。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_quotaUpdateFailed" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Quota update failed]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[配額更新失敗]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_quotaUpdateSucceeded" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Quota successfully updated]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[已成功更新配額]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_requestBladeFamilyNameFormat" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{0} Series]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{0} 系列]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_requestQuotaButtonText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Request quota increase]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[要求配額增加]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_subscriptionViewText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Subscription view]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[訂用帳戶檢視]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_toggleText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Show workspaces across all locations (cross-location compute)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[顯示所有位置的工作區 (跨位置計算)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_totalClustersLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Cluster quota:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[叢集配額:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_totalClustersTextFormat" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{0} clusters and CIs used | {1} remaining]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[已使用 {0} 個叢集和 CI | 剩餘 {1} 個]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_totalCoresTextFormat" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{0} cores used | {1} cores remaining]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[已使用 {0} 個核心 | 剩餘 {1} 個核心]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_totalDedicatedCoresLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Dedicated quota:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[專用配額:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_totalLowPriorityCoresLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Low priority quota:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[低優先順序配額:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CrossRegionComputeQuotas_workspaceViewText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Workspace view]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[工作區檢視]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Description_CreateAzureMachineLearningRegistryResource" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[For sharing ML assets across workspaces]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[適用於跨工作區共用 ML 資產]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Description_CreateAzureMachineLearningResource" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[For ML projects and teams]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[適用於 ML 專案和小組]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Description_createAzureMachineLearningHubResource" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Centrally configure Azure AI hubs]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[集中設定 Azure AI 中樞]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Centrally configure Azure AI resources]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";EncryptionSettings_Command_Discard" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Discard]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[捨棄]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";EncryptionSettings_Command_DismissAriaLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Close]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[關閉]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";EncryptionSettings_Command_ResultError" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Error updating key]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[更新金鑰時發生錯誤]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";EncryptionSettings_Command_ResultLoading" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Updating...]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正在更新...]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";EncryptionSettings_Command_ResultSuccess" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Success]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[成功]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";EncryptionSettings_Command_Save" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Save]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[儲存]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";EncryptionSettings_Command_SeeMore" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[See more]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[查看更多]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";EncryptionSettings_DirtyFormWarning" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You have unsaved changes.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[您有未儲存的變更。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";EncryptionSettings_Header1_Replacement1" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[data encryption]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[資料加密]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";EncryptionSettings_Header1_Replacement2" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[customer-managed key encryption]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[客戶自控金鑰加密]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";EncryptionSettings_Header1_Template" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Your data is encrypted by default using Microsoft-managed keys. For additional control over your data, you may choose to bring your own key for encryption. To learn more, see {} and {}.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[預設會使用 Microsoft 受控金鑰加密您的資料。若要進一步控制您的資料，您可以選擇自備金鑰進行加密。若要深入了解，請參閱 {} 和 {}。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";EncryptionSettings_Header2" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[After a workspace is deployed, you can rotate the encryption key but not change the encryption type from Microsoft-managed key to Customer-managed key.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[部署工作區之後，您可以旋轉加密金鑰，但無法將加密類型從 Microsoft 受控金鑰變更為客戶自控金鑰。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";EncryptionSettings_SelectedKey_KeyLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Key]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[金鑰]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";EncryptionSettings_SelectedKey_Title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Selected key vault and key]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[選取的金鑰保存庫與金鑰]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";EncryptionSettings_SelectedKey_VaultLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[KeyVault]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[KeyVault]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";EncryptionSettings_SelectedKey_VersionLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Version]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[版本]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";EncryptionSettings_SelectionSection_Title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Encryption selection]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[加密選取項目]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";EncryptionSettings_SelectionSection_TypeLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Encryption type]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[加密類型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";EncryptionSettings_SelectionSection_VaultAndKeyLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Key vault and key]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[金鑰保存庫與金鑰]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";EncryptionSettings_SelectionSection_VaultAndKeySelect" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select a key vault and key]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[選取金鑰保存庫與金鑰]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";EncryptionSettings_Title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Encryption]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[加密]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";EncryptionTab_Title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Encryption]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[加密]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ErrorJsonParsingException" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[An unexpected parsing error occurred.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[發生未預期的剖析錯誤。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";HubConfigDefaultResourceGroup_SummaryTab_label" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Default project resource group]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[預設專案資源群組]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Hub_Projects_LoadFailure" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]A;      Associated projects of Azure AI hub with ID "{0}" could not be loaded.]A;    ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[]A;      無法載入識別碼為 "{0}" 的 Azure AI 中樞的相關聯專案。]A;    ]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]A;      Associated projects of Azure AI resource with ID "{0}" could not be loaded.]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Hub_Projects_Title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]A;      Projects]A;    ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[]A;      專案]A;    ]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Hub_Projects_Toolbar_Add" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]A;      Add]A;    ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[]A;      新增]A;    ]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Hub_Projects_Toolbar_Refresh" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]A;      Refresh]A;    ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[]A;      重新整理]A;    ]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";IdentityTab_Title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Identity]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[身分識別]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Keyvault_Dropdown_Hub_info" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A key vault is used to store secrets and other sensitive information that is needed by the AI hub. You may create a new Azure Key Vault resource or select an existing one in your subscription.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[金鑰保存庫會用於儲存 AI 中樞所需的祕密及其他敏感性資訊。您可以建立新的 Azure Key Vault 資源，或在訂用帳戶中選取現有的資源。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Keyvault_Dropdown_SettingsBlade_Name_infoDescription" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The name must meet the following requirements:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[名稱必須符合下列要求:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Keyvault_Dropdown_SettingsBlade_Name_infoItem1" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Unique across all existing key vaults in Azure]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在 Azure 中所有現有金鑰保存庫中為唯一的]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Keyvault_Dropdown_SettingsBlade_Name_infoItem2" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Between 3 and 24 characters long]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[長度介於 3 到 24 個字元之間]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Keyvault_Dropdown_SettingsBlade_Name_infoItem3" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Only contain alphanumeric characters and hyphens]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[只能包含英數字元與連字號]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Keyvault_Dropdown_SettingsBlade_Name_infoItem4" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Cannot start with a number]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[不得以數字開頭]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Keyvault_Dropdown_SettingsBlade_nameInvalidMessage" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Vault name must be between 3-24 alphanumeric characters. The name must begin with a letter, end with a letter or digit, and not contain consecutive hyphens.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[保存庫名稱必須介於 3 到 24 個英數字元之間。名稱必須以字母開頭、以字母或數字作為結尾，且不包含連續的連字號。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Keyvault_Dropdown_SettingsBlade_nameLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[名稱]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Keyvault_Dropdown_SettingsBlade_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create new key vault]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[建立新的金鑰保存庫]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Keyvault_Dropdown_createNewAriaLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create new key vault]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[建立新的金鑰保存庫]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Keyvault_Dropdown_info" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A key vault is used to store secrets and other sensitive information that is needed by the workspace. You may create a new Azure Key Vault resource or select an existing one in your subscription.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[金鑰保存庫用於儲存工作區所需的祕密及其他敏感資訊。您可以建立新的 Azure Key Vault 資源，或在訂用帳戶中選取現有的資源。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Keyvault_Dropdown_label" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Key vault]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[金鑰保存庫]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Keyvault_SummaryTab_label" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Key vault]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[金鑰保存庫]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Keyword_alert" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Alert]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[警示]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Keyword_audit" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Audit]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[稽核]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Keyword_instance" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Instance]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[執行個體]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Keyword_log" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Log]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[記錄]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Keyword_rules" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Rules]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[規則]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Keyword_scale" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Scale]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[縮放]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Keyword_scaling" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Scaling]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正在擴充]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";LabelAutoMLMenuItem" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Automated machine learning]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[自動化機器學習]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";LabelComputeMenuItem" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Compute]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[計算]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";LabelDataMenuItem" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Data (Preview)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[資料 (預覽)]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Data]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";LabelDeployments" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deployments]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[部署]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";LabelDeploymentsMenuItem" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deployments]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[部署]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";LabelImagesMenuItem" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Images]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[影像]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";LabelInsights" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Application Insights]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Application Insights]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";LabelKeyVault" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Key Vault]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[金鑰保存庫]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";LabelMlFlowUri" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[MLflow tracking URI]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[MLflow 追蹤 URI]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";LabelModelsMenuItem" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";LabelPipelinesMenuItem" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Pipelines]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[管線]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";LabelProjectHubResource" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure AI hub]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure AI 中樞]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Azure AI]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";LabelProjectsMenuItem" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Experiments]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[實驗]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";LabelProperties" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Properties]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[屬性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";LabelRegistry" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Container Registry]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure Container Registry]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Registry]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";LabelStorage" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Storage]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[儲存空間]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";LabelTasksMenuItem" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Activities]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[活動]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";MLAppDeployments_GridColumns_name" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deployment name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[部署名稱]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";MLAppDeployments_GridColumns_status" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Provisioning state]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[佈建狀態]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";MLAppDeployments_GridColumns_traffic" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Traffic %]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[流量 %]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";MLAppDeployments_Grid_StatusFilter_all" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[All provisioning states]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[所有佈建狀態]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";MLAppDeployments_Grid_StatusFilter_itemFormat" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{0} ({1})]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{0} ({1})]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";MLAppDeployments_Grid_StatusFilter_some" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{0} connection states selected]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[已選取 {0} 個連線狀態]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ManagedIdentities_menuText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Identity]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[身分識別]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Networking_PrivateEndpointConnections_TabText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Private endpoint connections]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[私人端點連線]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Networking_PublicAccess_AllNetworksInfoText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[All networks, including the internet, can access this resource.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[所有網路 (包括網際網路) 皆可存取此資源。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Networking_PublicAccess_Description" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Public network access allows access to this resource through the internet using a public IP address. An application or resource that is granted access with the following network rules still requires proper authorization to access this resource. {0}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[公用網路存取可讓您使用公用 IP 位址透過網際網路存取此資源。使用下列網路規則授與存取權的應用程式或資源仍需要適當的授權，才能存取此資源。{0}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Networking_PublicAccess_DisabledInfoText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[No public network can access this resource.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[沒有可以存取此資源的公用網路。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Networking_PublicAccess_EnabledFromSelectedIpInfoText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Allow access from public IP you specified below.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[允許從您在下方指定的公用 IP 存取。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Networking_PublicAccess_FirewallValidation_duplicateIpAddresses" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Two identical address ranges have been specified. Address ranges must be unique.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[指定了兩個相同的位址範圍。位址範圍必須為唯一。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Networking_PublicAccess_FirewallValidation_exactPrefix" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The subnet prefix must be equal to {0}.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[子網路首碼必須等於 {0}。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Networking_PublicAccess_FirewallValidation_formattedPrefix" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The prefix must be between {0} and {1}.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[首碼必須介於 {0} 到 {1} 之間。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Networking_PublicAccess_FirewallValidation_invalidCIDRBlockWithSuggestion" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{0} is not a valid CIDR block. Use {1}/{2} instead.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{0} 不是有效的 CIDR 區塊。請改用 {1}/{2}。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Networking_PublicAccess_FirewallValidation_invalidCidr" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Specify an IP address or CIDR.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[請指定 IP 位址或 CIDR。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Networking_PublicAccess_FirewallValidation_invalidIPAddress" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Invalid IP address.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[IP 位址無效。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Networking_PublicAccess_FirewallValidation_leadingZerosIpAddress" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The octet '{0}' in IP address '{1}' contains a leading zero.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[IP 位址 '{1}' 中的八位元 '{0}' 包含前置字元零。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Networking_PublicAccess_FirewallValidation_malformedSubnet" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Malformed address range. Address was {0}.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[位址範圍格式錯誤。位址為 {0}。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Networking_PublicAccess_FirewallValidation_maxPrefix" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The prefix must be smaller than or equal to {0}.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[首碼必須小於或等於 {0}。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Networking_PublicAccess_FirewallValidation_minPrefix" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The prefix must be greater than or equal to {0}.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[首碼必須大於或等於 {0}。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Networking_PublicAccess_FirewallValidation_nonNullSubnet" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A non-null address range is required.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[需要非 null 的位址範圍。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Networking_PublicAccess_FirewallValidation_octet" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Octet {0} with value {1} is invalid. It must be between {2} and {3}.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[值為 {1} 的八位元 {0} 無效。此值必須介於 {2} 與 {3} 之間。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Networking_PublicAccess_FirewallValidation_publicIpRuleValidation" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[IP rules support public IP addresses only.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[IP 規則僅支援公用 IP 位址。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Networking_PublicAccess_LearnMoreText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[深入了解]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Networking_PublicAccess_RadioAllNetworksText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[All networks]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[所有網路]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Networking_PublicAccess_RadioDisabledText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Disabled]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[已停用]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Networking_PublicAccess_RadioEnabledFromAllNetworks" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Enabled from all networks]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[已從所有網路啟用]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Networking_PublicAccess_RadioEnabledFromSelectedIp" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Enabled from selected IP addresses]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[從選取的 IP 位址啟用]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Networking_PublicAccess_RadioLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Public network access]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[公用網路存取]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Networking_PublicAccess_TabText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Public access]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[公用存取]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Networking_PublicAccess_Toolbar_DiscardChangesText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Discard changes]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[捨棄變更]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Networking_PublicAccess_Toolbar_RefreshText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Refresh]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[重新整理]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Networking_PublicAccess_Toolbar_SaveText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Save]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[儲存]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Networking_PublicAccess_Toolbar_SavingText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Saving...]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正在儲存...]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Networking_PublicAccess_addClientIpAddressInfo" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You may not wish to add your client IP address if the network you are using the azure portal from is atypical (home vs. work environment for example).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[如果您並非透過慣用網路 (例如家用與工作環境) 使用 Azure 入口網站，您可能不希望新增您的用戶端 IP 位址。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Networking_PublicAccess_addClientIpAddressLabel" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Add your client IP address ('{0}')]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[新增您的用戶端 IP 位址 ('{0}')]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Networking_PublicAccess_addressRange" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Address range]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[位址範圍]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Networking_PublicAccess_addressRangePlaceHolder" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[IP address or CIDR]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[IP 位址或 CIDR]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Networking_PublicAccess_firewallDescription" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Add IP ranges to allow access from the internet or your on-premises networks.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[新增 IP 範圍，以允許來自網際網路或您內部部署網路的存取。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Networking_PublicAccess_firewallHeader" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Firewall]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[防火牆]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Networking_PublicAccess_firewallLearnMore" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[深入了解]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Networking_WorkspaceManagedOutboundAccess_tabText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Workspace managed outbound access]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[工作區受控輸出存取]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OnlineEndpoints_Create_Dependencies_condaDependenciesFileLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Conda dependencies file]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Conda 相依性檔案]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OnlineEndpoints_Create_Dependencies_description" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Customize your model deployment into inference app.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[將您的模型部署自訂為推斷應用程式。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OnlineEndpoints_Create_Dependencies_entryScriptFileLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Entry script file]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[輸入腳本檔案]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OnlineEndpoints_Create_Dependencies_selectCondaDependenciesFile" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select a conda dependency file]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[選取 Conda 相依性檔案]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OnlineEndpoints_Create_Dependencies_selectEntryScriptFileMessage" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select an entry script file]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[選取輸入腳本檔案]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OnlineEndpoints_Create_Dependencies_tabName" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Dependencies]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[相依性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OnlineEndpoints_Create_introText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure Machine Learning Inference apps enable you to quickly build, deploy and scale enterprise-grade machine learning models running on any platform. Use any open source machine learning framework like TensorFlow, PyTorch, SciKit-Learn, ONNX and more. Use our “no code deployment” to accelerate your productivity or customize your Inference app with your own docker container and/or model scoring code.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure Machine Learning 推斷應用程式可讓您快速地在任何平台上建置、部署企業層級的機器學習模型，也可讓您調整這些模型的規模。您可以使用任何開放原始碼的機器學習架構，例如 TensorFlow、PyTorch、SciKit-Learn、ONNX 等等。使用我們的無程式碼部署來提升您的產能，或是使用您自有的 Docker 容器及 (或) 模型計分程式碼，自訂您的推斷應用程式。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OnlineEndpoints_Create_modelLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OnlineEndpoints_Create_modelPlaceholder" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select a model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[選取模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OnlineEndpoints_Create_modelVersionLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model version]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型版本]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OnlineEndpoints_Create_modelVersionPlaceholder" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select a model version]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[選取模型版本]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OnlineEndpoints_Create_nameLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[名稱]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[ML App]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OnlineEndpoints_Create_subTitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create an ML App]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[建立 ML 應用程式]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OnlineEndpoints_Create_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Machine learning online endpoint]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[機器學習線上端點]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[ML App]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OnlineEndpoints_Create_workspaceLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Workspace]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[工作區]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OnlineEndpoints_Create_workspacePlaceholder" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select a workspace]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[選取工作區]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutboundAccessRule_SettingsBlade_FqdnCostInfo_managedNetworkIsolationLinkText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Managed Network Isolation.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[受管理的網路隔離。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutboundAccessRule_SettingsBlade_FqdnCostInfo_moreInformationText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[For more information on Azure Firewall, see ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[如需有關 Azure 防火牆的詳細資訊，請參閱 ]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutboundAccessRule_SettingsBlade_FqdnCostInfo_pricingLinkText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Pricing.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[價格。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutboundAccessRule_SettingsBlade_FqdnCostInfo_text" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[FQDN outbound rules are implemented using Azure Firewall. If you use outbound FQDN rules, charges for Azure Firewall are included in your billing. To learn more about outbound rules, see ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[FQDN 輸出規則是使用 Azure 防火牆實作。如果您使用輸出 FQDN 規則，Azure 防火牆費用會包含在帳單中。如需深入了解輸出規則，請參閱 ]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutboundAccessRule_SettingsBlade_Note" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Note]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[備註]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutboundAccessRule_SettingsBlade_amlRegistryPEDependencyRulesWarning" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Dependency outbound rules will be created for all dependency resources under AzureML registry. View these rules under Dependency outbound rules.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[將針對 AzureML 登錄下的所有相依性資源建立相依性輸出規則。在相依性輸出規則下檢視這些規則。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutboundAccessRule_SettingsBlade_appGateway_subResource_errorMessage" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[No private link config is found in resource. Set it up before creating PE outbound rule.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在資源中找不到私人連結設定。在建立 PE 輸出規則之前進行設定。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutboundAccessRule_SettingsBlade_destinationTypeBalloonContent" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Type of the outbound destination, FQDN, Private Endpoint, Service Tag.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[輸出目的地的類型，FQDN，私人端點，服務標籤。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutboundAccessRule_SettingsBlade_destinationTypeLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Destination type]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[目的地類型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutboundAccessRule_SettingsBlade_fqdnDestinationBalloonContent" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Fully Qualified Domain Name to allow for outbound traffic.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[允許輸出流量的完整網域名稱。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutboundAccessRule_SettingsBlade_fqdnDestinationLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[FQDN destination]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[FQDN 目的地]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutboundAccessRule_SettingsBlade_fqdnsLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[FQDNs]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[FQDN]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutboundAccessRule_SettingsBlade_portRangeBalloonContent" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Provide a single port, such as 80; a port range, such as 1024-655535; or a comma-separated list of single ports and/or port ranges, such as 80,1024-655535. This specifies on which ports traffic will be allowed by this rule. Provide an asterisk(*) to allow traffic on any port.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[請提供單一連接埠 (例如 80)、連接埠範圍 (例如 1024-655535) 或以逗點分隔的單一連接埠及/或連接埠範圍的清單 (例如 80,1024-655535)。這會指定此規則將允許或拒絕哪些連接埠上的流量。提供星號 (*) 則代表允許任何連接埠上的流量。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutboundAccessRule_SettingsBlade_portRangeLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Port ranges]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[連接埠範圍]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutboundAccessRule_SettingsBlade_protocolBalloonContent" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Network protocol to allow, TCP, UDP, ICMP or Any]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[要允許的網路通訊協定，TCP、UDP、ICMP 或任何]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutboundAccessRule_SettingsBlade_protocolLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Potocol]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[通訊協定]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutboundAccessRule_SettingsBlade_resourceGroupBalloonContent" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Resource group containing the target resource for the private endpoint.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[包含私人端點之目標資源的資源群組。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutboundAccessRule_SettingsBlade_resourceGroupLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Resource group]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[資源群組]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutboundAccessRule_SettingsBlade_resourceNameBalloonContent" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Name of the target resource for the private endpoint.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[私人端點的目標資源名稱。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutboundAccessRule_SettingsBlade_resourceNameLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Resource name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[資源名稱]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutboundAccessRule_SettingsBlade_resourceTypeBalloonContent" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Type of the Azure resource that supports Private Link.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[支援 Private Link 的 Azure 資源類型。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutboundAccessRule_SettingsBlade_resourceTypeLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Resource type]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[資源類型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutboundAccessRule_SettingsBlade_ruleNameAlreadyExists" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Rule name already exists]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[規則名稱已存在]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutboundAccessRule_SettingsBlade_ruleNameBalloonContent" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Name of the outbound rule that is unique in the workspace.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[工作區中唯一的輸出規則名稱。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutboundAccessRule_SettingsBlade_ruleNameLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Rule name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[規則名稱]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutboundAccessRule_SettingsBlade_serviceTagBalloonContent" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Predefined identifiers that represent a category of IP addresses.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[代表 IP 位址的類別的已預先定義識別碼。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutboundAccessRule_SettingsBlade_serviceTagLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Service tag]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[服務標籤]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutboundAccessRule_SettingsBlade_sparkEnabledBalloonContent" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Check to enable an additional private endpoint to be used by jobs running on Spark.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[檢查以啟用額外的私人端點，供在 Spark 上執行的作業使用。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutboundAccessRule_SettingsBlade_sparkEnabledLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Spark enabled]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Spark 已啟用]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutboundAccessRule_SettingsBlade_sparkStatusBalloonContent" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Indicates whether managed network is provisioned for Spark jobs.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[指出是否已為 Spark 作業佈建受控網路。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutboundAccessRule_SettingsBlade_sparkStatusLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Spark status]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Spark 狀態]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutboundAccessRule_SettingsBlade_statusBalloonContent" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Status is read-only, when Active, indicates the managed network is provisioned and ready. When Inactive, indicates it has not provisioned.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用中時，狀態為唯讀，表示已佈建受控網路且已就緒。非使用中時，表示尚未佈建。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutboundAccessRule_SettingsBlade_statusLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Status]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[狀態]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutboundAccessRule_SettingsBlade_subResourceBalloonContent" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Sub resource to connect for the private endpoint.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[用於連線私人端點的子資源。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutboundAccessRule_SettingsBlade_subResourceLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Sub resource]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[子資源]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutboundAccessRule_SettingsBlade_subscriptionBalloonContent" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Subscription containing the target resource for the private endpoint.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[包含私人端點之目標資源的訂用帳戶。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutboundAccessRule_SettingsBlade_subscriptionLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Subscription]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[訂用帳戶]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutboundAccessRule_SettingsBlade_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Workspace outbound rules]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[工作區輸出規則]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Overview" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Overview]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[概觀]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OverviewKeywords" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Summary Home]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[摘要首頁]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Overview_Banner_Description_label" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The Azure Machine Learning Studio is a web app where you can build, train, test, and deploy ML models. Launch it now to start exploring, or {}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure Machine Learning 工作室是一種 Web 應用程式，您可以在此建置、訓練、測試及部署 ML 模型。立即啟動以開始探索，或 {}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Overview_Banner_Description_learnMoreText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[learn more about the Azure Machine Learning studio]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[深入了解 Azure Machine Learning 工作室]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[learn more about the Azure Machine Learning Studio]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Overview_Banner_Registry_launchButtonText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Launch registry in studio]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在 Studio 中啟動登錄]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Overview_Banner_Registry_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Work with your registry in Azure Machine Learning Studio]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在 Azure Machine Learning Studio 中使用您的登錄]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Overview_Banner_launchButtonText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Launch studio]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[啟動工作室]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Overview_Banner_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Work with your models in Azure Machine Learning Studio]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在 Azure Machine Learning Studio 中使用您的模型]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Manage your machine learning lifecycle]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Overview_Essentials_ProjectID" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Project ID]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[專案識別碼]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Overview_Essentials_edit" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[edit]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[編輯]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Overview_Essentials_managedResourceGroup" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Managed resource group]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[受控資源群組]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Overview_Essentials_mlFlowWebURLText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[MLFlow tracking URI]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[MLFlow 追蹤 URI]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Overview_Essentials_registryWebURLText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Registry web URL]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[登錄 Web URL]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Overview_Essentials_studioWebURLText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Studio web URL]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[工作室 Web URL]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Commands_ApproveNotifications_Failure_message" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Failed to approve {0} out of {1} private endpoint connections]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[無法核准 {0}/{1} 個私人端點連線]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Commands_ApproveNotifications_Failure_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Failed to approve private endpoint connections]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[無法核准私人端點連線]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Commands_ApproveNotifications_InProgress_message" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Approving {0} private endpoint connections]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正在核准 {0} 個私人端點連線]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Commands_ApproveNotifications_InProgress_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Approving private endpoint connections...]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正在核准私人端點連線...]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Commands_ApproveNotifications_Success_message" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Successfully approved {0} private endpoint connections.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[已成功核准 {0} 個私人端點連線。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Commands_ApproveNotifications_Success_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Successfully approved private endpoint connections]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[已成功核准私人端點連線]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Commands_Approve_messagePlural" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Do you want to approve the {0} selected connections?]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[要核准 {0} 個選取的連線嗎?]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Commands_Approve_messageSingular" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Do you want to approve the connection '{0}'?]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[要核准連線 '{0}' 嗎?]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Commands_Approve_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Approve connection]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[核准連線]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Commands_DeleteNotifications_Failure_message" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Failed to delete {0} out of {1} private endpoint connections]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[無法刪除 {0}/{1} 個私人端點連線]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Commands_DeleteNotifications_Failure_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Failed to delete private endpoint connections]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[無法刪除私人端點連線]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Commands_DeleteNotifications_InProgress_message" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deleting {0} private endpoint connections]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正在刪除 {0} 個私人端點連線]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Commands_DeleteNotifications_InProgress_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deleting private endpoint connections...]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正在刪除私人端點連線...]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Commands_DeleteNotifications_Success_message" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Successfully deleted {0} private endpoint connections.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[已成功刪除 {0} 個私人端點連線。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Commands_DeleteNotifications_Success_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Successfully deleted private endpoint connections]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[已成功刪除私人端點連線]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Commands_RejectNotifications_Failure_message" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Failed to reject {0} out of {1} private endpoint connections]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[無法拒絕 {0}/{1} 個私人端點連線]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Commands_RejectNotifications_Failure_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Failed to reject private endpoint connections]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[無法拒絕私人端點連線]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Commands_RejectNotifications_InProgress_message" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Rejecting {0} private endpoint connections]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正在拒絕 {0} 個私人端點連線]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Commands_RejectNotifications_InProgress_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Rejecting private endpoint connections...]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正在拒絕私人端點連線...]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Commands_RejectNotifications_Success_message" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Successfully rejected {0} private endpoint connections.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[已成功拒絕 {0} 個私人端點連線。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Commands_RejectNotifications_Success_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Successfully rejected private endpoint connections]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[已成功拒絕私人端點連線]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Commands_Reject_messagePlural" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Do you want to reject the {0} selected connections?]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[要拒絕 {0} 個選取的連線嗎?]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Commands_Reject_messageSingular" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Do you want to reject the connection '{0}'?]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[要拒絕連線 '{0}' 嗎?]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Commands_Reject_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Reject connection]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[拒絕連線]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Commands_Remove_messagePlural" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Do you want to delete the {0} selected connections?]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[要刪除 {0} 個選取的連線嗎?]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Commands_Remove_messageSingular" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Do you want to delete the connection '{0}'?]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[要刪除連接 '{0}' 嗎?]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Commands_Remove_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Delete connection]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[刪除連線]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Commands_addPrivateEndpoint" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Private endpoint]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[私人端點]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Commands_approve" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Approve]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[核准]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Commands_description" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Description]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[描述]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Commands_reject" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Reject]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[拒絕]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Commands_remove" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Remove]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[移除]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Create_validationErrorFormat" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{0}: {1}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{0}: {1}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_GridColumns_description" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Description]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[描述]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_GridColumns_name" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Connection name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[連線名稱]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_GridColumns_privateEndpoint" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Private endpoint]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[私人端點]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_GridColumns_status" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Connection state]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[連線狀態]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Grid_StatusFilter_all" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[All connection states]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[所有連線狀態]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Grid_StatusFilter_itemFormat" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{0} ({1})]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{0} ({1})]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Grid_StatusFilter_some" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{0} connection states selected]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[已選取 {0} 個連線狀態]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Status_approved" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Approved]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[已核准]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Status_disconnected" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Disconnected]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[已中斷連線]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Status_pending" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Pending]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[暫止]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Status_rejected" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Rejected]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[已拒絕]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_Toolbar_refresh" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Refresh]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[重新整理]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_filterByName" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Filter by name...]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[依名稱篩選...]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_filterByStatus" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Filter by connection state...]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[依連線狀態篩選...]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_genericErrorMessage" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[There was an error processing your request. Try again in a few moments.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[處理您的要求時發生錯誤。請稍後再試一次。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_label" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Private endpoint connections]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[私人端點連線]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PrivateEndpoints_menuText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Networking]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[網路]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Private endpoint connections]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ProgressMachineLearningDeleteError" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The resource could not be deleted.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[無法刪除資源。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[The Workspace could not be deleted.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ProgressMachineLearningRegistryDeleteError" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The Registry could not be deleted.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[無法刪除登錄。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ProgressMachineLearningRegistryDeleteExecuting" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deleting the Registry]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正在刪除登錄]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ProgressMachineLearningServicesDeleteExecuting" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deleting resource]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正在刪除資源]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Deleting the Workspace]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ProgressNotificationMachineLearningRegistryDeleteError" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[An error occurred while deleting the Registry name '{registryName}'.{lineBreak}Error details:{lineBreak}{errorMessage}.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[刪除登錄名稱 '{workspaceName}' 時發生錯誤。{lineBreak}錯誤詳細資料:{lineBreak}{errorMessage}。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ProgressNotificationMachineLearningRegistryDeleteErrorTitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Registry deletion error]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[登錄刪除錯誤]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ProgressNotificationMachineLearningRegistryDeleteExecuting" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Registry '{registryName}' deletion in progress...]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[登錄 '{registryName}' 刪除進行中...]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ProgressNotificationMachineLearningRegistryDeleteExecutingTitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deleting Registry...]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正在刪除登錄...]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ProgressNotificationMachineLearningRegistryDeleteSuccess" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Registry '{registryName}' was deleted successfully.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[已成功刪除登錄 '{registryName}'。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ProgressNotificationMachineLearningRegistryDeleteSuccessTitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Registry deleted]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[登錄已刪除]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ProgressNotificationMachineLearningServicesDeleteCancelTitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Resource deletion canceled]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[資源刪除已取消]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Workspace deletion canceled]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ProgressNotificationMachineLearningServicesDeleteError" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[An error occurred while deleting the Workspace name '{workspaceName}'.{lineBreak}Error details:{lineBreak}{errorMessage}.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[嘗試刪除工作區名稱 '{workspaceName}' 時發生錯誤。{lineBreak}錯誤詳細資料:{lineBreak}{errorMessage}。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ProgressNotificationMachineLearningServicesDeleteErrorTitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Resource deletion error]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[資源刪除錯誤]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Workspace deletion error]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ProgressNotificationMachineLearningServicesDeleteExecuting" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Resource '{workspaceName}' deletion in progress...]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[刪除資源 '{workspaceName}' 進行中...]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Workspace  '{workspaceName}' deletion in progress...]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ProgressNotificationMachineLearningServicesDeleteExecutingTitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deleting resource...]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正在刪除資源...]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Deleting Workspace...]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ProgressNotificationMachineLearningServicesDeleteSuccess" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Resource '{workspaceName}' was deleted successfully.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[已成功刪除資源 '{workspaceName}'。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Workspace '{workspaceName}' was deleted successfully.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ProgressNotificationMachineLearningServicesDeleteSuccessTitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Succesfully deleted]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[成功刪除]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Workspace deleted]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PropertiesBladeApplicationInsightsLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Application Insights]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Application Insights]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PropertiesBladeContainerRegistryIdLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Container Registry ID]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[容器登錄識別碼]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PropertiesBladeCreationDateLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Created on]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[建立時間]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PropertiesBladeKeyVaultIdLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Key Vault ID]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Key Vault 識別碼]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PropertiesBladeLocationLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Location]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[位置]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PropertiesBladeResourceGroupLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Resource Group]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[資源群組]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PropertiesBladeResourceIdLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Resource ID]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[資源識別碼]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PropertiesBladeStorageAccountIdLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Storage account ID]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[儲存體帳戶識別碼]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PropertiesBladeSubscriptionIdLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Subscription ID]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[訂用帳戶 ID]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PropertiesBladeSubscriptionNameLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Subscription Name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[訂用帳戶名稱]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PropertiesBladeWorkspaceHubIdLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[AI hub ID]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[AI 中樞識別碼]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Azure AI hub ID]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PropertiesBladeWorkspaceIdLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Machine Learning workspace ID]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Machine Learning 工作區識別碼]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Workspace ID]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Properties_Discard" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Discard changes]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[捨棄變更]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Properties_Refresh" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Refresh]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[重新整理]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Properties_Save" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Save]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[儲存]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Properties_Saving" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Saving...]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正在儲存...]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Quota_Link_BladeDescription" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[View quota by subscription and region, and request quota directly from the studio.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[依訂用帳戶和地區檢視配額，並直接從工作室要求配額。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[View subscription- and workspace-level quota, configure workspace quota usages, and request quota directly from the studio.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Quota_Link_BladeTitle" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Request and view quota in Azure AI Studio]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在 Azure AI Studio 中要求並檢視配額]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Request and view quota in Azure Machine Learning Studio]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Quota_Link_Button" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[View quota]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[檢視配額]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";RecentlyDeletedWorkspaces" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Recently deleted]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[最近刪除]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";RegionsTab_AdditionRegionLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Additonal regions]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[其他區域]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";RegionsTab_Description" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select regions in which you currently have or plan to create AzureML workspaces and use assets from this registry. You can choose to add more regions to the registry later.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[選取您目前擁有或計畫要建立 AzureML 工作區的區域，並使用此登錄中的資產。您可以選擇稍後再新增更多區域到登錄。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";RegionsTab_Title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Regions]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[區域]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";RequestQuota_dedicatedSectionLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Dedicated]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[專用]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";RequestQuota_lowPrioritySectionLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Low priority]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[低優先順序]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";RequestQuota_vmTypeDedicated" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Dedicated]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[專用]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";RequestQuota_vmTypeLowPriority" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Low priority]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[低優先順序]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";RequestQuota_vmTypesLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[VM Types]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[VM 類型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ResourceLocationColumn" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Location]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[位置]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ResourceNameRequired" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The resource name is required]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[資源名稱為必要項]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Scaling" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Scaling]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正在調整規模]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SearchService_Dropdown_standardSupportedFormat" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{0} (SKU: {1}) - Standard SKU is needed at the minimum]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{0} (SKU: {1}) - 最小需要標準 SKU]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Security_menuText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Security]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[安全性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SelectLocation" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select a location]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[選取位置]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SelectSubscription" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select a subscription]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[選取訂閱]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_Blade_AIStudio_ToolBar_Header" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[AI Studio resources]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[AI Studio 資源]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_Blade_ErrorMessage" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Error loading soft deleted resources]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[載入虛刪除的資源時發生錯誤]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Error loading soft deleted workspaces]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_Blade_Footer_Buttons_Cancel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Cancel]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[取消]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_Blade_Footer_Buttons_Purge" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Permanently delete]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[永久刪除]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Purge]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_Blade_Footer_Buttons_Recover" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Recover]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[復原]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_Blade_LoadingText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Loading...]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正在載入...]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_Blade_SubTitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Recover or permanently delete resources]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[復原或永久刪除資源]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Recover or permanently delete workspaces]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_Blade_Title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Recently deleted resources]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[最近刪除的資源]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Recently deleted workspaces (preview)]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_Blade_ToolBar_Buttons_Refresh" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Refresh]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[重新整理]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_Blade_ToolBar_Header" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Machine Learning workspaces]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[機器學習工作區]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_DeleteBlade_FormControls_Buttons_cancel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Cancel]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[取消]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_DeleteBlade_FormControls_Buttons_delete" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Delete]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[刪除]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_DeleteBlade_FormControls_Confirm_errorMessage" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Name does not match]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[名稱不相符]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_DeleteBlade_FormControls_Confirm_label" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Confirm delete]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[確認刪除]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_DeleteBlade_FormControls_Confirm_placeholder" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Type the resource name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[輸入資源名稱]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Type the workspace name]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_DeleteBlade_FormControls_PermanentDelete_checkbox" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Delete this resource permanently]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[永久刪除此資源]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Delete the workspace permanently]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_DeleteBlade_FormControls_PermanentDelete_header" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Permanently delete]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[永久刪除]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_DeleteBlade_Message_AIStudio_text" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Permanently delete this resource "{0}"?]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[要永久刪除此資源 "{0}" 嗎?]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_DeleteBlade_Message_CMKtext" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This resource uses a customer-managed key (CMK) for data encryption. When soft-deleted, dependent resources for storing data will not be deleted and will incur cost until this resource is hard-deleted. {0}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此資源使用客戶自控金鑰 (CMK) 進行資料加密。虛刪除時，儲存資料的相依資源將不會刪除，而且在實刪除此資源之前會產生成本。{0}]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[This workspace uses a customer-managed key (CMK) for data encryption. When soft-deleted, dependent resources for storing workspace data will not be deleted and will incur cost until this workspace is hard-deleted. {0}]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_DeleteBlade_Message_link" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[深入了解]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_DeleteBlade_Message_text" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[When you delete this resource, it is temporarily placed in a ‘soft-delete’ state that allows you to recover it. Deletion of your data is postponed until your resource is permanently deleted by you, or until the expiry of the soft-delete data retention period of fourteen (14) days. You can override the soft-delete behavior and permanently delete your resource immediately. {0}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[當您刪除此資源時，它暫時處於可讓您復原的「虛刪除」狀態。資料的刪除會延遲到資源被您永久刪除，或直到十四 (14) 天虛刪除資料保留期到期為止。您可以覆寫虛刪除行為，並立即永久刪除您的資源。{0}]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[When you delete a workspace, it is temporarily placed in a ‘soft-delete’ state that allows you to recover any workspace data that was deleted unintentionally. Deletion of your workspace data is postponed until your workspace is permanently deleted by you, or until the expiry of the soft-delete data retention period of fourteen (14) days. You can override the soft-delete behavior and permanently delete your workspace immediately. {0}]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_DeleteBlade_Title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Delete resource]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[刪除資源]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Delete workspace]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_Grid_AriaLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deleted resources grid]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[相關的資源方格]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Deleted workspaces grid]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_Grid_Columns_DeletedDate" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deleted date]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[刪除日期]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_Grid_Columns_Name" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[名稱]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_Grid_Columns_PurgeDate" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Scheduled permanent deletion date]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[已排程的永久刪除日期]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Scheduled purge date]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_Grid_Columns_ResourceGroup" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Resource group]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[資源群組]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_Grid_NoWorkspacesFound" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[No resources found to display]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[找不到可顯示的資源]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[No workspaces found to display]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_OverviewBlade_DeleteMessage" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The soft delete feature has been enabled on this resource. After you soft delete,  this resource data remains available. It will get purged after the retention period. You may purge it sooner, or recover the resource]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[已在此資源上啟用虛刪除功能。虛刪除之後，資源資料仍可使用。會在保留期限之後清除。您可以提早清除它，或復原資源]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[The soft delete feature has been enabled on this workspace. After you soft delete this workspace, workspace data remains available. It will get purged after the retention period. You may purge it sooner, or recover the workspace]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_WorkspacesBlade_Commands_Generic_Error_Message" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[There was an error processing your request. Try again in a few moments]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[處理要求時發生錯誤。請幾分鐘後再試一次]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_WorkspacesBlade_Commands_PurgeNotifications_Failure_message" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Failed to permanently delete {0} out of {1} resources]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[無法永久刪除 {0} 個資源 (共 {1} 個)]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Failed to permanently delete {0} out of {1} workpsaces]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_WorkspacesBlade_Commands_PurgeNotifications_Failure_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Failed to delete resources]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[無法刪除資源]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Failed to delete workspaces]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_WorkspacesBlade_Commands_PurgeNotifications_InProgress_message" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Permanently deleting {0} resources]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正在永久刪除 {0} 資源]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Permanently deleting {0} workspaces]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_WorkspacesBlade_Commands_PurgeNotifications_InProgress_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deleting resources ...]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正在刪除資源 ...]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Deleting workspaces ...]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_WorkspacesBlade_Commands_PurgeNotifications_Success_message" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Successfully deleted {0} resource(s) permanently]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[已成功永久刪除 {0} 個資源]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Successfully deleted {0} workspace(s) permanently]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_WorkspacesBlade_Commands_PurgeNotifications_Success_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Successfully deleted resources]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[已成功刪除資源]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Successfully deleted workspaces]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_WorkspacesBlade_Commands_RecoverNotifications_Failure_message" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Failed to recover {0} out of {1} resources]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[無法復原 {0} 個資源 (共 {1} 個)]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Failed to recover {0} out of {1} workpsaces]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_WorkspacesBlade_Commands_RecoverNotifications_Failure_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Failed to recover resources]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[無法復原資源]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Failed to recover workspaces]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_WorkspacesBlade_Commands_RecoverNotifications_InProgress_message" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Recovering {0} resource(s)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正在復原 {0} 個資源]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Recovering {0} workspaces]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_WorkspacesBlade_Commands_RecoverNotifications_InProgress_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Recovering resources ...]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正在復原資源...]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Recovering workspaces ...]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_WorkspacesBlade_Commands_RecoverNotifications_Success_message" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Successfully recovered {0} resource(s)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[已成功復原 {0} 個資源]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Successfully recovered {0} workspace(s)]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_WorkspacesBlade_Commands_RecoverNotifications_Success_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Successfully recovered resources]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[已成功復原資源]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Successfully recovered workspaces]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_WorkspacesBlade_Commands_ValidateResources_AppInsights_Failure_message" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Recovery error: the associated Application Insights resource "{0}" could not be found. It may have been deleted. Recover or recreate the application insights resource under its former name "{0}". You may restore your application insights data by recovering the associated log analytics workspace first."]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[復原錯誤: 找不到相關聯的 Application Insights 資源 "{0}"。它可能已遭刪除。復原或重新建立其原名稱為 "{0}" 的 Application Insights 資源。您可以先復原相關聯的 Log Analytics 工作區，以還原您的 Application Insights 資料。」]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Application insights resource "{0}" is a dependency of the workspace but does not exist. It may have been deleted. Before recovering your workspace, first recreate the application insights resource under its former name "{0}". You may restore your application insights data by recovering the associated log analytics workspace first."]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_WorkspacesBlade_Commands_ValidateResources_ContainerRegistry_Failure_message" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Recovery error: the associated Container Registry resource "{0}" could not be found. A soft-deleted cannot be recovered without a Container Registry as a dependency.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[復原錯誤: 找不到相關聯的 ACR 資源 "{0}"。若未以 ACR 作為相依性，則無法復原虛刪除。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Recovery error: the workspace-associated Container Registry resource "{0}" no longer exists. A soft-deleted workspace cannot be recovered without a Container Registry as a dependency.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_WorkspacesBlade_Commands_ValidateResources_Failure_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Failed to validate resource {0}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[無法驗證資源 {0}]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Failed to validate workspace {0}]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_WorkspacesBlade_Commands_ValidateResources_InProgress_message" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Validating {0} resources for {1}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正在驗證 {1} 的 {0} 個資源]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_WorkspacesBlade_Commands_ValidateResources_InProgress_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Validating resources ...]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正在驗證資源 ...]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Validating workspace resources ...]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_WorkspacesBlade_Commands_ValidateResources_KeyVault_Failure_message" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Recovery error: the associated Azure Key Vault resource "{0}" could not be found. A soft-deleted resource cannot be recovered without its previously attached Key Vault. The required Azure Key Vault may still be recoverable, see `Manage deleted vaults`.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[復原錯誤: 找不到相關聯的 Azure Key Vault 資源 "{0}"。若沒有之前連結的 Key Vault，則無法復原虛刪除的資源。必要的 Azure Key Vault 仍有可能復原，方法請參閱 「管理刪除的保存庫」。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Recovery error: the workspace-associated Azure Key Vault resource "{0}" does not exist. A soft-deleted workspace cannot be recovered without its previously attached Key Vault resource. The required Azure Key Vault may still be recoverable, see `Manage deleted vaults`.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_WorkspacesBlade_Commands_ValidateResources_StorageAccount_Failure_message" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Recovery error: the associated Storage Account resource "{0}" could not be found. If the storage account was previously soft-deleted, recover it first before recovering this resource.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[復原錯誤: 找不到相關聯的儲存體帳戶資源 "{0}"。如果儲存體帳戶先前已被虛刪除，請先復原它，再復原此資源。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Recovery error: the workspace-associated Storage Account resource "{0}" no longer exists. If the storage account was previously soft-deleted, recover it first before recovering the workspace.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_WorkspacesBlade_Commands_ValidateResources_Success_message" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Successfully validated resources for {0}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[已成功驗證 {0} 的資源]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SoftDeletedWorkspace_WorkspacesBlade_Commands_ValidateResources_Success_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Successfully validated]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[已成功驗證]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Successfully validated workspace]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";StorageAccount_Dropdown_Hub_info" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A storage account is used as the default datastore for the AI hub. You may create a new Azure Storage resource or select an existing one in your subscription.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[儲存體帳戶作為 AI 中樞的預設資料存放區使用。您可以建立新的 Azure 儲存體資源，或在您的訂用帳戶中選取現有的資源。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";StorageAccount_Dropdown_SettingsBlade_Name_infoDescription" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The name must meet the following requirements:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[名稱必須符合下列要求:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";StorageAccount_Dropdown_SettingsBlade_Name_infoItem1" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Unique across all existing storage account names in Azure]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在 Azure 中所有現有儲存體帳戶名稱中為唯一的]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";StorageAccount_Dropdown_SettingsBlade_Name_infoItem2" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Between 3 and 24 characters long]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[長度介於 3 到 24 個字元之間]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";StorageAccount_Dropdown_SettingsBlade_Name_infoItem3" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Only contain lowercase letters and numbers]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[只能包含小寫字母與數字]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";StorageAccount_Dropdown_SettingsBlade_Performance_info" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Standard storage accounts are backed by magnetic drives and provide the lowest cost per GB. Premium storage accounts are backed by solid state drives and offer consistent, low-latency performance.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[磁碟機支援標準儲存體帳戶，而且每 GB 的費用最低。固態硬碟支援進階儲存體帳戶，能持續提供一致而且低延遲的效能。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";StorageAccount_Dropdown_SettingsBlade_Replication_info" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The data in your storage account is always replicated to ensure durability and high availability. Choose a replication strategy that best matches you requirements.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[儲存體帳戶中的資料一律會加以複寫，以確保其持久性與高可用性。請選擇最符合您需求的複寫策略。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";StorageAccount_Dropdown_SettingsBlade_Replication_infoLearnMore" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[深入了解]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";StorageAccount_Dropdown_SettingsBlade_hnsCheckLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Enable hierarchical namespace (preview)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[啟用階層命名空間 (預覽)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";StorageAccount_Dropdown_SettingsBlade_hnsDescriptionText" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The Data Lake Storage Gen2 hierarchical namespace accelerates big data analytics workloads, enables faster and more reliable file operations, and enables file-level access control lists (ACLs). {0}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Data Lake Storage Gen2 階層式命名空間可加速巨量資料分析工作負載、啟用更快且更可靠的檔案作業，以及啟用檔案層級存取控制清單 (ACL)。 {0}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";StorageAccount_Dropdown_SettingsBlade_hnsDescriptionTitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Data Lake Storage Gen2 (preview)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Data Lake Storage Gen2 (預覽)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";StorageAccount_Dropdown_SettingsBlade_hnsNotSupportedFormat" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{0} - Hierarchical namespace is not supported]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{0} - 不支援階層命名空間]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";StorageAccount_Dropdown_SettingsBlade_learnMoreText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[深入了解]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";StorageAccount_Dropdown_SettingsBlade_nameInvalidMessage" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Storage name must be between 3 and 24 characters and may only contain lowercase letters and numbers.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[儲存體名稱長度應介於 3 到 24 個字元之間，而且只可包含小寫字母與數字。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";StorageAccount_Dropdown_SettingsBlade_nameLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[名稱]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";StorageAccount_Dropdown_SettingsBlade_performanceLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Performance]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[效能]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";StorageAccount_Dropdown_SettingsBlade_performancePremium" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Premium]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[進階]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";StorageAccount_Dropdown_SettingsBlade_performanceStandard" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Standard]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[標準]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";StorageAccount_Dropdown_SettingsBlade_premiumNotSupportedFormat" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{0} - Premium storage account is not supported]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{0} - 不支援進階儲存體帳戶]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";StorageAccount_Dropdown_SettingsBlade_replicationGRS" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Geo-redundant storage (GRS)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[異地備援儲存體 (GRS)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";StorageAccount_Dropdown_SettingsBlade_replicationGZRS" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Geo-zone-redundant storage (GZRS)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[異地區域備援儲存體 (GZRS)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";StorageAccount_Dropdown_SettingsBlade_replicationLRS" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Locally-redundant storage (LRS)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[本地備援儲存體 (LRS)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";StorageAccount_Dropdown_SettingsBlade_replicationLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Replication]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[複寫]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";StorageAccount_Dropdown_SettingsBlade_replicationRAGRS" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Read-access geo-redundant storage (RA-GRS)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[讀取權限異地備援儲存體 (RA-GRS)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";StorageAccount_Dropdown_SettingsBlade_replicationRAGZRS" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Read-access geo-zone-redundant storage (RA-GZRS)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[讀取權限異地區域備援儲存體 (RA-GZRS)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";StorageAccount_Dropdown_SettingsBlade_replicationZRS" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Zone-redundant storage (ZRS)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[區域備援儲存體 (ZRS)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";StorageAccount_Dropdown_SettingsBlade_title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create new storage account]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[建立新的儲存體帳戶]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";StorageAccount_Dropdown_createNewAriaLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create new storage account]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[建立新儲存體帳戶]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";StorageAccount_Dropdown_info" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A storage account is used as the default datastore for the workspace. You may create a new Azure Storage resource or select an existing one in your subscription.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[儲存體帳戶作為工作區的預設資料存放區使用。您可以建立新的 Azure 儲存體資源，或在訂用帳戶中選取現有的資源。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";StorageAccount_Dropdown_label" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Storage account]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[儲存體帳戶]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";StorageAccount_SummaryTab_label" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Storage account]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[儲存體帳戶]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";StorageTab_Description" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[As users work in Azure AI Studio, uploaded data, stored credentials and generated artifacts like logs are stored.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[當使用者在 Azure AI Studio 中工作時，會儲存上傳的數據、已儲存的認證和產生的成品，例如記錄。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";StorageTab_Header" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Configure how your data is stored]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[設定資料的儲存方式]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";StorageTab_LogsAndDockerHeader" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Logs and docker images]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[記錄和 Docker 映像]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";StorageTab_Title" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Storage]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[儲存體]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SummaryTab_Advanced_ManagedIdentity_systemAssigned" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[System assigned]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[系統指派]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SummaryTab_Advanced_ManagedIdentity_typeText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Identity type]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[身分識別類型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SummaryTab_Advanced_ManagedIdentity_userAssigned" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[User assigned]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用者指派]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SummaryTab_Advanced_ManagedIdentity_userIdentityName" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[User identity name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用者識別名稱]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SummaryTab_Advanced_ManagedIdentity_userIdentityResourceGroup" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[User identity resource group]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用者識別資源群組]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SummaryTab_Resource_existingFormat" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{0} ({1})]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{0} ({1})]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SummaryTab_Resource_newFormat" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[(new) {0}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[(新增) {0}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SummaryTab_Resource_none" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[None]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[無]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SummaryTab_createResourceGroupErrorText" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Error creating resource group when creating default log analytics workspace: {0}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[建立預設記錄分析工作區時，建立資源群組時發生錯誤: {0}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SummaryTab_gettingLogWorkspacesErrorText" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Error getting log workspaces for creating default log analytics workspace: {0}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[取得用於建立預設 Log Analytics 工作區的記錄工作區時發生錯誤: {0}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";TextLaunchWebWorkspace" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Explore your Machine Learning workspace to run and track experiments, compare model performance, and deploy models.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索您的 Machine Learning 工作區，以執行及追蹤實驗、比較模型效能，以及部署模型。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Explore your Machine Learning service workspace to run and track experiments, compare model performance, and deploy models.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";TextLaunchWebWorkspaceHeader" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Explore your Azure Machine Learning workspace]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索您的 Azure Machine Learning 工作區]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Explore your Azure Machine Learning service workspace]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";TextMachineLearningServicesDocumentationLinkBody" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn how to use Azure Machine Learning.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[了解如何使用 Azure Machine Learning。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";TextMachineLearningServicesDocumentationLinkHeader" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[View Documentation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[檢視文件]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";TextMachineLearningServicesForumLinkBody" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Join the discussion of Azure Machine Learning. ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[加入 Azure Machine Learning 的討論。 ]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";TextMachineLearningServicesForumLinkHeader" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[View Forum]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[檢視論壇]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";TextMachineLearningServicesManageFeatoreStoreLinkHeader" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create and Manage]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[建立及管理]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";TextMachineLearningServicesManageFeatureStoreLinkBody" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn how to create and manage Machine Learning registries.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[了解如何建立與管理 Machine Learning 登錄。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";TextMachineLearningServicesManageRegistryLinkBody" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn how to create and manage Machine Learning registries.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[了解如何建立與管理 Machine Learning 登錄。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";TextMachineLearningServicesManageRegistryLinkHeader" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create and Manage]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[建立及管理]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";TextMachineLearningServicesShareRegistryLinkBody" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn how to share Machine Learning assets using registries.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[了解如何使用登錄共用 Machine Learning 資產。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";TextMachineLearningServicesShareRegistryLinkHeader" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Share models, components and environments]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[共用模型、元件和環境]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";TextViennaGitHubLinkBody" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Get inspired by a large collection of machine learning examples.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[從機器學習範例的大集錦中獲得啟發。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";TextViennaGitHubLinkHeader" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[View more samples at GitHub]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在 GitHub 檢視更多範例]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";TitleError" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Error]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[錯誤]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";TitleGiveFeedback" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Give feedback]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[提供意見反應]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";TitleMachineLearningServicesDeleteConfirmationMessageBox" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Delete Workspace]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[刪除工作區]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";TitleMachineLearningServicesRegistryDeleteConfirmationMessageBox" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Delete Registry]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[刪除登錄]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";TitleMonitoringLens" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Getting Started]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[快速入門]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";TitleWebWorkspaceBlade" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Machine Learning]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[機器學習]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Machine Learning service workspace]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ValidationAIServicesNameAlreadyInUse" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This AI Services name already exists]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此 AI 服務名稱已存在]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ValidationAppInsightsNameAlreadyInUse" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This application insights name already exists]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此應用程式見解名稱已經存在]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[This Application Insights name already exists]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ValidationContainerRegistryNameAlreadyInUse" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This container registry name already exists]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此容器登錄名稱已存在]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ValidationCreateWorkspacePermission" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You don't have the required permissions ({0}) to create an account under the selected resource group]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[您不具必要的權限 ({0})，無法在選取的資源群組下建立帳戶]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ValidationDependentResourcesAlreadyInUse" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Dependent resources with this name already exist]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[具有此名稱的相依資源已存在]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ValidationError" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[There was an error while attempting to validate the resource]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[嘗試驗證資源時發生錯誤]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ValidationHubNameAlreadyInUse" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This AI hub name already exists]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[AI 中樞名稱已存在]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ValidationKeyVaultNameAlreadyInUse" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This key vault name already exists]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此金鑰保存庫名稱已經存在]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[This Key Vault name already exists]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ValidationPrimaryRegionNotSelected" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Please ensure the primary region is one of the selected values]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[請確認主要區域是其中一個選取的值]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ValidationRegionNotSelected" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Please select at least one region]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[請至少選取一個區域。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ValidationRegistryDescriptionTooLarge" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This registry description has a maximum length of 256 characters.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此登錄描述具有長度為 256 個字元的上限。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ValidationRegistryNameAlreadyInUse" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This registry name already exists]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[這個登錄名稱已經存在]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ValidationRegistryNameInvalid" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Registry name must be between 3 and 33 characters long. Its first character has to be alphanumeric, and the rest may contain hyphens and underscores. No whitespace is allowed.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[登錄名稱的長度必須介於 3 到 33 個字元之間。第一個字元必須是英數字元，而其餘字元可包含連字號及底線。不允許空白字元。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ValidationStorageAccountNameAlreadyInUse" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This storage account name already exists]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此儲存體帳戶名稱已經存在]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[This Storage account name already exists]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ValidationWorkspaceNameAlreadyInUse" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This workspace name already exists]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此工作區名稱已存在]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ValidationWorkspaceNameAlreadyInUseAndSoftDeleted" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This workspace name already exists, or is being reserved by a workspace which was previously soft deleted. Please use a different name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[本工作區名稱正在使用中，或仍由先前已虛刪除的工作區所保留。請使用其他名稱。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ValidationWorkspaceNameInvalid" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Resource name must be between 3 and 33 characters long. Its first character has to be alphanumeric, and the rest may contain hyphens and underscores. No whitespace is allowed.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[資源名稱必須介於 3 到 33 個字元之間。第一個字元必須是英數字元，而其餘的字元可包含連字號及底線。不允許空白字元。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Workspace name must be between 3 and 33 characters long. Its first character has to be alphanumeric, and the rest may contain hyphens and underscores. No whitespace is allowed.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ValidationWorkspaceNameReserved" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This workspace name is reserved]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[這個工作區名稱會保留]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";WorkspaceErrorMessage" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The selected subscription doesn’t have permissions to register the resource provider. For more information, <a href = {0} target = "_blank">click here</a>.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[選取的訂用帳戶無權註冊資源提供者。如需詳細資訊，<a href = {0} target = "_blank">請按一下這裡</a>。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";assetTypeNames_MachineLearningExperimentationAccount_singular" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Retired - Machine Learning Experimentation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[已淘汰 - Machine Learning 測試]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";automationLink" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Download a template for automation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[下載自動化的範本]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";basics" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Basics]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[基本]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";basicsBladeDetailsIntro" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Every workspace must be assigned to an Azure subscription, which is where billing happens. You use resource groups like folders to organize and manage resources, including the workspace you're about to create.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[每個工作區都必須指派給 Azure 訂用帳戶，這是計費的發生位置。您可以使用資料夾之類的資源群組來組織和管理資源，包括您即將建立的工作區。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Select the subscription to manage deployed resources and costs. Use resource groups like folders to organize and manage all your resources.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";basicsBladeDetailsIntroLearnMore" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more about Azure resource groups]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[深入了解 Azure 資源群組]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";basicsBladeHubDetailsIntro" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select the subscription to manage deployed resources and costs. Use resource groups like folders to organize and manage all your resources. An AI hub is a collaboration environment for a team to share project work, model endpoints, compute, (data) connections, security settings, govern usage.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[請選取要用於管理所部署之資源與費用的訂用帳戶。使用資料夾之類的資源群組來組織和管理您所有的資源。AI 中樞是可供小組共用專案工作、模型端點、計算、(資料) 連線、安全性設定、控管使用方式的共同作業環境。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Select the subscription to manage deployed resources and costs. Use resource groups like folders to organize and manage all your resources.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";basicsBladeInstanceIntro" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Configure your basic workspace settings like its storage connection, authentication, container, and more.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[設定基本工作區設定，例如其儲存體連線、驗證、容器等。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Specify the name and region for the workspace.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";basicsBladeInstanceIntroLearnMore" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[深入了解]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";basicsBladeRegistryInstanceIntro" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Configure your basic registry settings like its name and description.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[設定您的基本登錄設定，例如其名稱和描述。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";basicsRegistryBladeDetailsIntro" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Every registry must be assigned to an Azure subscription, which is where billing happens. You use resource groups like folders to organize and manage resources, including the registry you're about to create.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[每個登錄工作區都必須指派給 Azure 訂用帳戶 (計費的發生位置)。您可以使用資料夾之類的資源群組來組織和管理資源，包括您即將建立的工作區。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";basicsTabTitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Basics]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[基本]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";buttonCreate" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[建立]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";buttonNext" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Next : {0}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[下一步: {0}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";buttonNextPlaceholder" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Next >]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[下一步 >]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";buttonPrevious" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[< Previous]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[< 上一步]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";buttonReviewCreate" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Review + create]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[檢閱 + 建立]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";createBladeNextStepsMLFlowAzureMLDescription" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[How to use MLflow with Azure ML]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[如何搭配 Azure ML 使用 MLflow]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";createBladeWorkspaceDetails" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Workspace details]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[工作區詳細資料]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";createLocationLabelDefault" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Location]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[位置]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";createLocationLabelPrimaryRegion" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Primary Region]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[主要區域]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";createLocationLabelRegion" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Region]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[區域]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";createResourceGroup" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Resource group]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[資源群組]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";createResourceGroupCreateNewPlaceholder" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]]></Val>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";createResourceGroupTitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Resource group]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[資源群組]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";createSubscriptionLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Subscription]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[訂用帳戶]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";createTagsTabIntro" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Tags are name/value pairs that enable you to categorize resources and view consolidated billing by applying the same tag to multiple resources and resource groups.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[標籤為成對的名稱和數值，可讓您透過將相同標籤套用至多個資源與資源群組，進而分類資源並檢視合併的帳單。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";createTagsTabLearnMoreText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more about tags]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[深入了解標籤]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";createTagsTabTitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Tags]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[標籤]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";createTagsTabUpdateNotice" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Note that if you create tags and then change resource settings on other tabs, your tags will be automatically updated.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[請注意，若您在建立標籤後變更其他索引標籤上的資源設定，您的標籤將會自動更新。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";createTemplateValidationError" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Validation failed. Click here to view details.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[驗證失敗。按一下這裡以檢視詳細資料。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";createTemplateValidationInProgress" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Running final validation...]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正在執行最後驗證...]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";createTemplateValidationSuccess" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Validation passed]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[已通過驗證]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";deploymentCapacityMetric" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deployment Capacity]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[部署容量]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";deploymentCpuUtilizationMetric" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Cpu Utilization]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[CPU 使用率]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";deploymentDiskUtilizationMetric" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Disk Utilization]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[磁碟使用率]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";deploymentEnvironmentId" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Environment]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[環境]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";deploymentKind" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deployment Type]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[部署類型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";deploymentMemoryUtilizationMetric" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Memory Utilization]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[記憶體使用量]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";deploymentModelIdLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model Id]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型識別碼]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";deploymentName" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deployment Name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[部署名稱]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";deploymentProvisioningStateLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Provisioning State]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[佈建狀態]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";detailsLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Resource details]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[資源詳細資料]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Project details]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";downloadTemplateLinkText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Download a template for automation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[下載自動化的範本]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";endpointAuthModeLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Auth Mode]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[驗證模式]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]D;]A;      Auth Mode]D;]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";endpointId" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Endpoint Id]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[端點識別碼]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]D;]A;      Endpoint Id]D;]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";endpointProvisioningStateLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Provisioning State]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[佈建狀態]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]D;]A;      Provisioning State]D;]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";endpointRequestLatencyMetric" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Request Latency]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[要求延遲]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";endpointRequestsPerMinuteMetric" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Request Per Minute]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[每分鐘的要求數]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";endpointScoringUriLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Scoring Uri]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[評分 URI]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]D;]A;      Scoring Uri]D;]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";endpointSwaggerLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Swagger Uri]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Swagger URI]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]D;]A;      Swagger Uri]D;]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";instanceLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Instance details]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[執行個體詳細資料]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";labelCommandButtonRefresh" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Refresh]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[重新整理]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";labelDataLabeling" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Data Labeling]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[資料標記]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";labelMLStudioLauncher" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Visual interface]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[視覺化介面]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";labelQuotaUsage" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Usage + quotas]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用量 + 配額]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";labelRequestQuota" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Request Quota]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[要求配額]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";labelResource" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Resource]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[資源]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";labelWorkstationsMenuItem" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Notebook VMs]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[筆記本 VM]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";learnMore" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[深入了解]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";location" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Location]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[位置]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";machineLearningCompute" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Managed Compute]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[受控計算]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Machine Learning Compute]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";mlStudioCapability1" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Drag-n-Drop to build machine learning models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[拖放功能可建置機器學習模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";mlStudioCapability2" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[No limit to data size or compute capacity for model training]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型定型沒有資料大小或計算容量的限制]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";mlStudioCapability3" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Intrinsic and powerful Python support]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[功能強大的內建 Python 支援]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";mlStudioCapability4" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[One click to deploy your web service]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[按一下即可部署 Web 服務]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";mlStudioCapability5" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Rich and fast-growing modules support]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[豐富且成長快速的模組支援]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";mlStudioLaunchLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Launch visual interface]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[啟動視覺化介面]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";mlStudioSubtitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[What's possible with visual interface]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[視覺化介面的功能]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";mlStudioTitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Visual interface (preview)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[視覺化介面 (預覽)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";multipleInvalidTabErrorMessage" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Validation failed for the following tabs: {0}. Required information is missing or not valid.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[下列索引標籤的驗證失敗: {0}。缺少必要資訊或其無效。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";newResource" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[(new) {0}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[(新增) {0}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";newResourceCapitalized" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[(New) {0}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[(新增) {0}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";newResourceFormatCaps" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[(New) {0}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[(新增) {0}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";noContent" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[No content]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[沒有內容]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";none" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[None]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[無]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";onlineEndpointName" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Machine learning online endpoint]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[機器學習線上端點]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[ML App]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";onlineEndpointWorkspaceName" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Workspace]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[工作區]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";quickLinkUnderOverviewBladeAriaLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Quick link under Overview blade.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[[概觀]5D; 刀鋒視窗下的快速連結。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";quotaBladeTitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Usage + quotas]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用量 + 配額]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";quotaNote" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Note:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[注意:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";quotaProviderNotRegisteredErrorMsg" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Your subscription {0} is not registered with the '{1}' resource provider. Please create a 'Machine Learning service' workspace to auto-register and retry submitting the support request.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[您的訂用帳戶 {0} 未向 '{1}' 資源提供者註冊。請建立要自動註冊的「Machine Learning 服務」工作區，然後重試提交支援要求。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";quotaProviderRegisteringErrorMsg" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Registering your subscription {0} with the '{1}' resource provider.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正在向 '{1}' 資源提供者註冊您的訂用帳戶 {0}。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";quotaQuotaSettingTabHeader" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Configure quotas]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[設定配額]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";quotaRequestCurrentLimit" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Current vCPU limit]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[目前的 vCPU 限制]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Current limit]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";quotaRequestDocumentationInfoBox" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Click to learn more about Compute (cores/vCPUs) quota increase requests.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[按一下以深入了解計算 (核心/vCPU) 配額增加要求。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";quotaRequestHeader" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Please enter the limit for any resource(s) you are requesting:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[請輸入您要求之任何資源的限制:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";quotaRequestLogOutputMessageDetail" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{0} - current value: {1} / requested value: {2}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{0} - 目前的值: {1} / 要求的值: {2}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";quotaRequestNewLimit" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[New vCPU limit]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[新的 vCPU 限制]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[New limit]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";quotaRequestNotFound" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[No quota resources found for given location]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在指定位置找不到任何配額資源]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";quotaRequestResourceName" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Resource name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[資源名稱]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";quotaRequestSubmit" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Save and continue]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[儲存並繼續]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";quotaRequestTitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Quota details]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[配額詳細資料]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";quotaRequestVMFamily" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[VM series]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[VM 系列]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[VM family]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";quotaSubscriptionViewClusterHelptext" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[For a cluster we show your currently allocated cores and maximum cores it can scale to.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[針對叢集，我們會顯示您目前配置的核心數及其可縮放的最大核心數。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";quotaSubscriptionViewResourceNameHelptext" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Expand each VM family to view your quota allocation and resource usage by workspace and further to view your clusters and instances.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[擴充每個 VM 系列，依工作區檢視配額的配置及資源使用量，並進一步檢視叢集與執行個體。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Expand each VM size to view your quota allocation and resource usage by workspace and further by clusters.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";quotaSubscriptionViewTabHeader" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Subscription view]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[訂用帳戶檢視]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Subscription View]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";quotaSubscriptionViewUsageHelptext" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[For an instance it continues to use quota even in Stopped state so you can restart it at any time.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[若為執行個體，即使是「已停止」狀態也會繼續使用配額，因此您可以隨時重新啟動。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";quotaTableHeaderDedicated" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Dedicated cores usage]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[專用核心使用量]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Dedicated Cores Usage]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";quotaTableHeaderLowPriority" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Low priority cores usage]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[低優先順序核心使用量]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Low Priority Cores Usage]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";quotaTableHeaderQuota" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Resource name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[資源名稱]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Resource Name]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";quotaTableNoData" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[No data to display]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[無任何可顯示的資料]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";quotaTableServerError" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The server encountered an error processing current request. Please refresh the table again.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[伺服器在處理目前的要求時，發生錯誤。請再次重新整理該資料表。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";quotaTableTotalSubscriptionQuota" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Total subscription quota]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[訂用帳戶配額總計]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";quotaWorkspaceQuotaExceedSubscriptionLimit" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Workspace level quota cannot exceed the subscription level quota limit.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[工作區層級配額不能超過訂用帳戶層級配額限制。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";quotaWorkspaceQuotaInsufficientPermissions" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You are not authorized to set quota at the workspace level. Please reach out to your subscription admin to help allocate resources between workspaces.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[未授權您在工作區層級設定配額。請連絡您的訂用帳戶系統管理員，以協助配置工作區間的資源。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";quotaWorkspaceQuotaInvalidVMFamilyName" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Please specify a VM family that is supported in the $region region and you have subscription level quota for.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[請指定 $region 區域支援且您訂用帳戶層級配額所屬的 VM 家族。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";quotaWorkspaceQuotaLessThanMinimumClusterCores" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Workspace level quota cannot be less than the cores required to support the minimum nodes of this workspace’s clusters and instances.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[工作區層級配額不能少於支援此工作區叢集及執行個體最小節點數所需的核心數。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Workspace level quota cannot be less than the cores required to support the minimum nodes of this workspace’s clusters.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";quotaWorkspaceQuotaNewLimitHelpText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Allocate subscription level quota between workspaces by setting it here. To change the all-up subscription level quota, use the request quota button at the bottom. Note that you need to be a subscription owner to modify these values.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在此設定以配置工作區間的訂用帳戶層級配額。若要變更所有訂用帳戶層級配額，請使用底部的 [要求配額]5D; 按鈕。請注意，您必須是訂用帳戶擁有者才能修改這些值。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[* Allocate subscription level quota between workspaces by setting it here. To change the all-up subscription level quota, use the request quota button at the bottom. Note that you need to be a subscription owner to modify these values.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";quotaWorkspaceQuotaPlaceHolder" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Unallocated cores: {0}, Maximum: {1}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[未配置的核心數: {0}，最大值: {1}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";quotaWorkspaceQuotaResourceNameHelpText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Expand each VM size to view and allocate subscription level quota between workspaces.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[擴充每個 VM 大小，以檢視及配置工作區間的訂用帳戶層級配額。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[* Expand each VM size to view and allocate subscription level quota between workspaces.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";quotaWorkspaceQuotaUnknownError" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Unknown error]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[未知的錯誤]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";quotaWorkspaceViewClusterHelptext" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[For a cluster we show your currently allocated cores and maximum cores it can scale to.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[針對叢集，我們會顯示您目前配置的核心數及其可縮放的最大核心數。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";quotaWorkspaceViewResourceNameHelptext" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Expand each workspace to view your quota allocation and resource usage by VM family and further to view your clusters and instances.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[擴充每個工作區，依 VM 系列檢視配額的配置及資源使用量，並進一步檢視叢集與執行個體。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Expand each workspace to view your quota allocation and resource usage by VM size and further by clusters.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";quotaWorkspaceViewTabHeader" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Workspace view]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[工作區檢視]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Workspace View]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";quotaWorkspaceViewUsageHelptext" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[For an instance it continues to use quota even in Stopped state so you can restart it at any time.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[若為執行個體，即使是「已停止」狀態也會繼續使用配額，因此您可以隨時重新啟動。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";singleInvalidTabErrorMessage" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Validation failed for the following tab: {0}. Required information is missing or not valid.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[下列索引標籤的驗證失敗: {0}。缺少必要資訊或其無效。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";subtitleEnvironmentInfoBlade" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Configure compute for deploying and managing models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[設定用於部署及管理模型的計算]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";summaryTabTitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Review + create]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[檢閱 + 建立]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";tabValidationErrors" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Validation failed. Required information is missing or not valid.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[驗證失敗。缺少必要資訊或其無效。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";tags" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Tags]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[標籤]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";textAzureDatabricksOptionCreateNew" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create New]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[新建]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";textAzureDatabricksOptionUseExisting" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Use Existing]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用現有的]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";textMachineLearningServicesAutoMLLinkBody" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Automatically create a model from your existing data.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[自動從現有的資料建立模型。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";textMachineLearningServicesAutoMLLinkHeader" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create a new Automated Machine Learning Model (Preview)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[建立新的自動化機器學習模型 (預覽)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";textMachineLearningServicesNotebookVMLinkBody" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Quickly get started with the Python SDK and run sample experiments with Azure Machine Learning Notebook VMs.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[快速開始使用 Python SDK，並使用 Azure Machine Learning 筆記本 VM 執行範例實驗。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Explore data, create models, and deploy services in your Notebook VM.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";textMachineLearningServicesNotebookVMLinkHeader" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Get Started with Sample Notebooks (Preview)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[開始使用範例 Notebook (預覽)]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Create Notebooks in an Azure Machine Learning Notebook VM (Preview)]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";textMachineLearningServicesVisualInterfaceLinkBody" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Drag and drop existing components to create new models.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[拖放現有元件以建立新的模型。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";textMachineLearningServicesVisualInterfaceLinkHeader" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Build a model using the Visual Interface (Preview)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用視覺化介面建置模型 (預覽)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";textNotAvailable" ItemType="0;.resx" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[N/A]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[無]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";titleAssetsGroup" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Assets]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[資產]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";titleAuthoringGroup" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Authoring (Preview)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[製作 (預覽)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";titleDeployments" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deployments]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[部署]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";titleEnvironmentInfoBlade" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Machine Learning Compute]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Machine Learning Compute]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";titleSettings" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Settings]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[設定]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";titleSupport" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Support + troubleshooting]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[支援與疑難排解]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";workspaceCancelUpgradeButtonText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]A;      Cancel]A;    ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[]A;      取消]A;    ]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]D;]A;      Cancel]D;]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";workspaceConfirmUpgradeButtonText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]A;      Confirm Upgrade]A;    ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[]A;      確認升級]A;    ]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]D;]A;      Confirm Upgrade]D;]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";workspaceCreateSKUPricingDetails" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]A;      View full pricing details]A;    ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[]A;      檢視完整定價詳細資料]A;    ]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]D;]A;      View full pricing details]D;]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";workspaceCreateSKUTooltip" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]A;      The Enterprise SKU (preview) boosts productivity for all ML skill levels and offers robust MLOps capabilities to manage the complete ML lifecycle. THe Basic SKU workspace allows for open source development and a code-first experience.]A;    ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[]A;      Enterprise SKU (預覽) 可為各種 ML 技能等級提高生產力，並提供健全的 MLOps 功能，以管理完整的 ML 生命週期。基本 SKU 工作區可用於開放原始碼開發及程式碼先行體驗。]A;    ]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]D;]A;      The Enterprise SKU (preview) boosts productivity for all ML skill levels and offers robust MLOps capabilities to manage the complete ML lifecycle. THe Basic SKU workspace allows for open source development and a code-first experience.]D;]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";workspaceLoadFailure" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]A;      Workspace with ID "{0}" could not be loaded.]A;    ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[]A;      無法載入識別碼為 "{0}" 的工作區。]A;    ]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";workspaceSKUPropertyLabel" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]A;      Workspace edition]A;    ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[]A;      工作區版本]A;    ]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]D;]A;      Workspace edition]D;]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";workspaceTwoBannerBasicSKUBody" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]A;      Introducing a new immersive experience (preview) for managing the end-to-end machine learning lifecycle.]A;    ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[]A;      已推出可用來管理端對端機器學習生命週期的新沈浸式體驗 (預覽)。]A;    ]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]D;]A;      Introducing a new immersive experience (preview) for managing the end-to-end machine learning lifecycle.]D;]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";workspaceTwoBannerBasicSKUTitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]A;      Launch Preview Now]A;    ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[]A;      立即啟動預覽]A;    ]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]D;]A;      Launch Preview Now]D;]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";workspaceTwoBannerBody" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]A;      An immersive experience for managing the end-to-end machine learning lifecycle.]A;    ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[]A;      管理端到端機器學習生命週期的沈浸式體驗。]A;    ]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]D;]A;      An immersive experience for managing the end-to-end machine learning lifecycle.]D;]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";workspaceTwoBannerBodyPreview" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]A;      Introducing a new immersive experience (preview) for managing the end-to-end machine learning lifecycle.]A;    ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[]A;      已推出可用來管理端對端機器學習生命週期的新沈浸式體驗 (預覽)。]A;    ]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]D;]A;      Introducing a new immersive experience (preview) for managing the end-to-end machine learning lifecycle.]D;]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";workspaceTwoBannerButton" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]A;      Launch now]A;    ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[]A;      立即啟動]A;    ]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]D;]A;      Launch now]D;]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";workspaceTwoBannerLink" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]A;      Learn more]A;    ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[]A;      深入了解]A;    ]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]D;]A;      Learn more]D;]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";workspaceTwoNoticeButton" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]A;      Launch now]A;    ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[]A;      立即啟動]A;    ]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]D;]A;      Launch now]D;]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";workspaceTwoNoticeMessage" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]A;      Contents of this page will be moving to a new immersive experience for]A;      managing the end-to-end machine learning lifecycle. Compute targets will]A;      be manageable from both locations. Features provided in preview are]A;      offered at no additional charge but may not remain so after general]A;      availability.]A;    ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[]A;      此頁面的內容將會移至可用來管理端對端機器學習生命週期的]A;      新沈浸式體驗。計算目標將]A;      可從這兩個位置進行管理。預覽中提供的功能為]A;      免費提供，但正式推出後可能就不再]A;      免費提供。]A;    ]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]D;]A;      Contents of this page will be moving to a new immersive experience for]D;]A;      managing the end-to-end machine learning lifecycle. Compute targets will]D;]A;      be manageable from both locations. Features provided in preview are]D;]A;      offered at no additional charge but may not remain so after general]D;]A;      availability.]D;]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";workspaceUpgradeAboutOurPricing" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]A;      about our pricing]A;    ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[]A;      關於我們的定價]A;    ]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]D;]A;      about our pricing]D;]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";workspaceUpgradeAuthorizationFailed" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]A;      You currently do not have permissions to upgrade your workspace. Please contact your IT administrator.]A;    ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[]A;      您目前無權限升級工作區。請連絡您的 IT 系統管理員。]A;    ]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]D;]A;      You currently do not have permissions to upgrade your workspace. Please contact your IT administrator.]D;]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";workspaceUpgradeBannerText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Upgrade this workspace to Enterprise edition (preview) to use visual machine learning, advanced automated machine learning, and to manage quota.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[請將此工作區升級為企業版 (預覽)，以使用視覺化的機器學習、進階自動化機器學習及管理配額。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Upgrade this workspace to Enterprise edition to use visual machine learning, advanced automated machine learning, and to manage quota.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";workspaceUpgradeBulletPoint" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]A;      You need an Enterprise edition (preview) workspace to view and set quotas on Enterprise workspaces.]A;    ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[]A;      您必須有 Microsoft SQL Server Enterprise (預覽) 工作區，才能在 Enterprise 工作區上檢視及設定配額。]A;    ]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]D;]A;      You need an Enterprise edition (preview) workspace to view and set quotas on Enterprise workspaces.]D;]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";workspaceUpgradeButtonText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]A;      Upgrade]A;    ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[]A;      升級]A;    ]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]D;]A;      Upgrade]D;]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";workspaceUpgradeConfirmationBoxContent" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]A;      Upgrade workspace to access Enterprise (preview) capabilities. Visit our {0} for more information.]A;    ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[]A;      升級工作區即可存取 Enterprise (預覽) 功能。如需詳細資訊，請前往我們的 {0}。]A;    ]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]D;]A;      Upgrade workspace to access Enterprise (preview) capabilities. Visit our {0} for more information.]D;]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";workspaceUpgradeConfirmationBoxTitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]A;      Confirm workspace upgrade]A;    ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[]A;      確認工作區升級]A;    ]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]D;]A;      Confirm workspace upgrade]D;]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";workspaceUpgradeLearnMore" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]A;      Learn more]A;    ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[]A;      深入了解]A;    ]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]D;]A;      Learn more]D;]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";workspaceUpgradePricingPage" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]A;      pricing page]A;    ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[]A;      定價頁面]A;    ]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]D;]A;      pricing page]D;]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";workspaceUpgradeQuickLinkBannerText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]A;      Upgrade this workspace to Enterprise edition (preview) to use UI-based tools for all skill levels and more.]A;    ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[]A;      將此工作區升級為 Microsoft SQL Server Enterprise (預覽) 以在所有技能層級等項目使用 UI 式工具。]A;    ]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]D;]A;      Upgrade this workspace to Enterprise edition (preview) to use UI-based tools for all skill levels and more.]D;]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";workspaceUpgradeQuickLinkLearnAboutEnterpriseEdition" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]A;      Learn about Enterprise Edition (preview)]A;    ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[]A;      了解 Microsoft SQL Server Enterprise (預覽)]A;    ]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]D;]A;      Learn about Enterprise Edition (preview)]D;]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";workspaceUpgradeQuickLinkPostUpgradeBannerText" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]A;      Use the Enterprise edition (preview) to access UI-based tools for all skill levels, built-in MLOps and more]A;    ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[]A;      使用 Microsoft SQL Server Enterprise (預覽) 以在所有技能等級存取 UI 式工具、內建 MLOps 等]A;    ]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]D;]A;      Use the Enterprise edition (preview) to access UI-based tools for all skill levels, built-in MLOps and more]D;]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";workspaceUpgradeSetQuotaOperationNotAllowed" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]A;      This feature is disabled for you. Please upgrade to an Enterprise edition (preview) workspace to configure quotas.]A;    ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[]A;      您無法使用此功能。請升級至 Microsoft SQL Server Enterprise (預覽) 工作區以設定配額。]A;    ]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]D;]A;      This feature is disabled for you. Please upgrade to an Enterprise edition (preview) workspace to configure quotas.]D;]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";workspaceUpgradeUpgradeCompleteNotificationContent" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]A;      Your workspace {0} upgraded successfully.]A;    ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[]A;      已成功升級您的工作區 {0}。]A;    ]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]D;]A;      Your workspace {0} upgraded successfully.]D;]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";workspaceUpgradeUpgradeCompleteNotificationTitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]A;      Workspace Upgrade Complete]A;    ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[]A;      工作區升級完成]A;    ]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]D;]A;      Workspace Upgrade Complete]D;]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";workspaceUpgradeUpgradeFailed" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]A;      An unexpected error occurred, please try upgrading your workspace again, or submitting a support ticket.]A;    ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[]A;      發生未預期的錯誤，請嘗試再次升級您的工作區，或提交支援票證。]A;    ]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]D;]A;      An unexpected error occurred, please try upgrading your workspace again, or submitting a support ticket.]D;]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";workspaceUpgradeUpgradeFailedNotificationContent" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]A;      Your workspace {0} did not upgrade successfully.]A;    ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[]A;      未成功升級您的工作區 {0}。]A;    ]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]D;]A;      Your workspace {0} did not upgrade successfully.]D;]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";workspaceUpgradeUpgradeFailedNotificationTitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]A;      Workspace Upgrade Failed]A;    ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[]A;      工作區升級失敗]A;    ]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]D;]A;      Workspace Upgrade Failed]D;]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";workspaceUpgradeUpgradeSucceeded" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]A;      Upgrade Azure Machine Learning workspace {0} succeeded.]A;    ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[]A;      成功升級 Azure Machine Learning 工作區 {0}。]A;    ]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]D;]A;      Upgrade Azure Machine Learning workspace {0} succeeded.]D;]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";workspaceUpgradeUpgrading" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]A;      Upgrading Azure Machine Learning workspace {0}]A;    ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[]A;      正在升級 Azure Machine Learning 工作區 {0}]A;    ]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]D;]A;      Upgrading Azure Machine Learning workspace {0}]D;]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";workspaceUpgradeUpgradingNotificationContent" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]A;      Your workspace {0} is upgrading from Basic to Enterprise]A;    ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[]A;      您的工作區 {0} 正在從 Basic 升級為 Enterprise]A;    ]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]D;]A;      Your workspace {0} is upgrading from Basic to Enterprise]D;]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";workspaceUpgradeUpgradingNotificationTitle" ItemType="0;.resx" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]A;      Workspace is currently upgrading]A;    ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[]A;      工作區目前正在升級]A;    ]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[]D;]A;      Workspace is currently upgrading]D;]A;    ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
    </Item>
  </Item>
</LCX>