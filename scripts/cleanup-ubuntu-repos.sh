#!/bin/bash

poolName=$1

printf "# Start cleaning up Ubuntu machine git repos (pool = $poolName) #\n"

printf "\n=== Check current path and save current repo number ===\n"
pwd
repoDir=${PWD##*/}
printf "Current repo is $repoDir.\n"
cd ..

printf "\n=== Calling 'df' to see disk space ===\n"
df

printf "\n=== Calling 'ls -al' to see files in folder containing repos ===\n"
ls -al

printf "\n=== Deleting all repos (except one used most recently) ===\n"

regex="^[0-9]*$"
dirlist=$(ls -d */ | cut -f1 -d'/')

for i in $dirlist
do
  if [[ ${i} =~ $regex ]]
  then
    if [ ${i} == $repoDir ]
    then
      printf "Repo $i was used for this run and will not be deleted.\n"
    else
      printf "Deleting repo $i.\n"
      rm -rf $i
    fi
  fi
done

printf "\n=== Calling 'df' to see disk space again ===\n"
df

printf "\n=== Calling 'ls -al' to see files in folder containing repos again ===\n"
ls -al
