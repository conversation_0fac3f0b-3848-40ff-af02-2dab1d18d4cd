#!/usr/bin/env bash
# ********************************************************************************
# * <PERSON><PERSON> file for the Vienna Linux development environment
# ********************************************************************************

# ********************************************************************************
# * Build macros (No configuration specified so Debug is implied)
# ********************************************************************************
alias b="dotnet msbuild /restore /m /v:minimal /clp:summary"
alias bo="b /p:BuildProjectReferences=false"
alias bc="b /t:Rebuild"
alias bco="bc /p:BuildProjectReferences=false"
# Build Publish
alias bp="b /target:Publish"
# Container Build (without pushing container images to the ACR)
# alias cb="bp /p:AmlSkipDockerPush=1"

# ********************************************************************************
# * Build macros (Release configuration)
# ********************************************************************************
alias br="b /p:Configuration=Release"
alias bor="bo /p:Configuration=Release"
alias bcr="bc /p:Configuration=Release"
alias bcor="bco /p:Configuration=Release"
# Build Publish
alias bpr="bp /p:Configuration=Release"
# Container Build (without pushing container images to the ACR)
# alias cbr="cp /p:Configuration=Release"

# ********************************************************************************
# * Build macros (with logging)
# ********************************************************************************
alias bl="dotnet msbuild /restore /m '/flp1:LogFile=msbuild.log;PerformanceSummary;Summary;ShowTimestamp' '/flp2:LogFile=msbuild.wrn;WarningsOnly' '/flp3:LogFile=msbuild.err;ErrorsOnly'"
alias bcl="bl /t:Rebuild"
alias bpl="bl /t:Publish"
# alias cbl="bl /t:Publish /p:AmlSkipDockerPush=1"

alias blr="bl  /p:Configuration=Release"
alias bclr="bcl /p:Configuration=Release"
alias bplr="bpl /p:Configuration=Release"
alias cblr="cbl /p:Configuration=Release"

# ********************************************************************************
# * Restore macros
# ********************************************************************************
alias dr="dotnet restore"
alias drr="dr /p:Configuration=Release"

# ********************************************************************************
# * Test macros
# ********************************************************************************
alias ut='dotnet test --filter "TestCategory!=Component&TestCategory!=Integration"'
alias utr="ur /p:Configuration=Release"

# ********************************************************************************
# * Macros for opening Visual Studio Code on a project
# ********************************************************************************
alias co='dotnet build /restore /m /nologo /t:OpenInVSCode'
alias co0='co /p:Depth=0'
alias co1='co /p:Depth=1'
alias co2='co /p:Depth=2'
alias co3='co /p:Depth=3'

# ********************************************************************************
# * Macros for making a solution or workspace (without opening)
# ********************************************************************************
alias ms='dotnet build /restore /m /nologo /t:MakeSolution'
alias ms0='ms /p:Depth=0'
alias ms1='ms /p:Depth=1'
alias ms2='ms /p:Depth=2'
alias ms3='ms /p:Depth=3'

# ********************************************************************************
# * Navigation aids
# ********************************************************************************
alias up="cd .."
alias up1="cd .."
alias up2="cd ../.."
alias up3="cd ../../.."
alias up4="cd ../../../.."
alias up5="cd ../../../../.."
alias up6="cd ../../../../../.."
alias up7="cd ../../../../../../.."
alias amlsrc="cd $VIENNA_ROOT_DIR/src/azureml-api/src"
alias aether="cd $VIENNA_ROOT_DIR/src/aether"
alias amlinfra="cd $VIENNA_ROOT_DIR/src/azureml-infra"

alias ik="wget -O - https://aka.ms/msftkube-bootstrapper.sh | bash"

# list macros
alias lm="alias"
