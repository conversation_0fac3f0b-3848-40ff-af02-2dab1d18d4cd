# ********************************************************************************
# * <PERSON><PERSON> file for the Vienna Windows development environment
# ********************************************************************************

# ********************************************************************************
# * Build macros (No configuration specified so Debug is implied)
# ********************************************************************************
function global:b { dotnet build /restore /m /v:minimal /clp:summary @args }
function global:bo { b  /p:BuildProjectReferences=false @args }
function global:bc { b  /t:Rebuild @args }
function global:bco { bc /t:Rebuild @args }
# Build Publish
function global:bp { b /t:Publish @args }

# Container Build (without pushing container images to the ACR)
# function global:cb  { b /t:Publish /p:AmlSkipDockerPush=1 @args }

# ********************************************************************************
# * Build macros (Release configuration)
# ********************************************************************************
function global:br { b   /p:Configuration=Release @args }
function global:bor { bo  /p:Configuration=Release @args }
function global:bcr { bc  /p:Configuration=Release @args }
function global:bcor { bco /p:Configuration=Release @args }
function global:bpr { bp  /p:Configuration=Release @args }
function global:cbr { cb  /p:Configuration=Release @args }

# ********************************************************************************
# * Build macros (with logging)
# ********************************************************************************
function global:bl { dotnet msbuild /restore /m /bl "/flp1:LogFile=msbuild.log;PerformanceSummary;Summary;ShowTimestamp" "/flp2:LogFile=msbuild.wrn;WarningsOnly" "/flp3:LogFile=msbuild.err;ErrorsOnly" /clp:summary @args }
function global:bcl { bl /t:Rebuild @args }
function global:bpl { bl /t:Publish @args }
function global:cbl { bl /t:Publish /p:AmlSkipDockerPush=1 @args }

function global:blr { bl  /p:Configuration=Release @args }
function global:bclr { bcl /p:Configuration=Release @args }
function global:bplr { bpl /p:Configuration=Release @args }
function global:cblr { cbl /p:Configuration=Release @args }

# ********************************************************************************
# * Restore macros
# ********************************************************************************
function global:dr { dotnet restore @args }
function global:drr { dr p:Configuration=Release @args }

# ********************************************************************************
# * Test macros
# ********************************************************************************
function global:ut { dotnet test --filter "TestCategory!=Component&TestCategory!=Integration" @args }
function global:utr { ut /p:Configuration=Release @args }

# ********************************************************************************
# * Macros for opening Visual Studio on a project
# ********************************************************************************
function global:open { dotnet build /restore /m /nologo /t:Open @args }
function global:open0 { open /p:Depth=0 @args }
function global:open1 { open /p:Depth=1 @args }
function global:open2 { open /p:Depth=2 @args }
function global:open3 { open /p:Depth=3 @args }

# ********************************************************************************
# * Macros for making a solution or workspace (without opening)
# ********************************************************************************
function global:ms { dotnet build /restore /m /nologo /t:MakeSolution @args }
function global:ms0 { ms /p:Depth=0 @args }
function global:ms1 { ms /p:Depth=1 @args }
function global:ms2 { ms /p:Depth=2 @args }
function global:ms3 { ms /p:Depth=3 @args }

# ********************************************************************************
# * Macros for opening Visual Studio Code on a project
# ********************************************************************************
function global:co { dotnet build /restore /m /nologo /t:OpenInVSCode @args }
function global:co0 { co /p:Depth=0 @args }
function global:co1 { co /p:Depth=1 @args }
function global:co2 { co /p:Depth=2 @args }
function global:co3 { co /p:Depth=3 @args }

# ********************************************************************************
# * Navigation aids
# ********************************************************************************
function global:up { Set-Location .. }
function global:up1 { Set-Location .. }
function global:up2 { Set-Location ../.. }
function global:up3 { Set-Location ../../.. }
function global:up4 { Set-Location ../../../.. }
function global:up5 { Set-Location ../../../../.. }
function global:up6 { Set-Location ../../../../../.. }
function global:up7 { Set-Location ../../../../../../.. }
function global:amlsrc { Push-Location $env:VIENNA_ROOT_DIR/src/azureml-api/src }
function global:aether { Push-Location $env:VIENNA_ROOT_DIR/src/aether }
function global:amlinfra { Push-Location $env:VIENNA_ROOT_DIR/src/azureml-infra }

if ($IsWindows) {
    function global:vs { &"C:\Program Files (x86)\Common Files\Microsoft Shared\MSEnv\VSLauncher.exe" @args }
    function global:n { notepad.exe @args }
    function global:e { explorer @args }
}

function global:ik { Set-ExecutionPolicy Bypass -Scope Process -Force; iex ((New-Object System.Net.WebClient).DownloadString('https://aka.ms/msftkube-bootstrapper.ps1')) }

# list macros
function global:lm { Get-Content $env:VIENNA_ROOT_DIR/scripts/macros.ps1 }
