# DO NOT MODIFY this script without searching for the YAML pipelines that depend on this file
# and verifying those pipelines still work as intended!

# The release fork pipelines will NOT be added as PR gates because they would generate
# unnecessary branches with each PR update.

# Creates a release/<project_name>/<build_number> branch from master branch.
# Intended use:
    # Run on a daily schedule to create daily release branches like:
        # release/common_runtime/20220131.1
        # release/common_runtime/20220201.1
        # release/common_runtime/20220202.1
    # Additional runs can be manually triggered if needed:
        # release/common_runtime/20220202.2
        # release/common_runtime/20220202.3
    # A prod build pipeline to build CR whenever a change in release/common_runtime/*
        # A prod build will be produced for each fork.
    # A release pipeline to publish CR image to prod once a day

param (
    [Parameter()]
    [ValidateNotNullOrEmpty()]
    [string]$repoUrl=$(throw "repoUrl is required"),
    [string]$projectName=$(throw "projectName is required"))

# Inspect the build number to determine release branch name.
# Expect build number to be of the format: yyyyMMdd.r
# Example of release branch name: release/common_runtime/20220131.1
$identifier = $env:BUILD_BUILDNUMBER

# Fork from the source branch where the fork is run on.
$sourceBranch = $env:BUILD_SOURCEBRANCH
Write-Host "BUILD_SOURCEBRANCH = $sourceBranch"

if ([string]::IsNullOrEmpty($sourceBranch) -or $sourceBranch -eq "refs/heads/master")
{
    $sourceBranch = "origin/master"
    Write-Host "Setting source branch to 'origin/master'."
} else {
    $sourceBranch = $sourceBranch -replace "refs/heads/", ""
    $identifier = "$identifier-dev"
    Write-Host "Source branch is a dev branch and not master. Appending with '-dev'."
}

$releaseBranch = "release/$projectName/$identifier"
Write-Host "Release branch = $releaseBranch"

# CAUTION: Don't print $repoUrl as it contains an access token with write access to the repo.
Write-Host "Forking to release branch: Source = $sourceBranch, Release branch = $releaseBranch"

git config --global core.autocrlf false
git config --global push.default simple

Write-Host "Checking out source branch: $sourceBranch"
git checkout $sourceBranch

$tag = "$projectName-$identifier"
Write-Host "Tagging branch with tag: $tag"
git tag $tag
git push $repoUrl $tag

Write-Host "Checking out new release branch: $releaseBranch"
git checkout -b $releaseBranch

git push --force --repo=$repoUrl
