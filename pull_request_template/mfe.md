#### MFE PR includes contract changes

 - [ ] PR does not impact the MFE ARM API (versioned controller/contracts that do not have a dataplane suffix) and therefore does not require the Github PR link.
 - [ ] PR does have ARM API changes. Required link to MFE Azure ARM Github PR is provided below along with other related metadata. [Reference Doc.](https://eng.ms/docs/cloud-ai-platform/ai-platform/ai-platform-ml-platform/project-vienna-services/azure-machine-learning-runbook/operational/managementfrontend/sop/mfecontributor/addingarmspecfeaturechanges). Fill out information below replacing <> items (including <>).

1. Internal API Review PR link: < https://github.com/Azure/azureml_run_specification/pull/... >
2. ARM Github PR link: < https://github.com/Azure/azure-rest-api-specs/pull/... >
3. ARM Github Username: < username >
4. ARM Github PR Branch: < branchname >
5. MFE API-Version: <2023-08-01-preview>
6. New ARM Resource Type: <N/A>
7. If this is a GA change, API for the preview: <N/A>

###Additional Notes
[optional description goes here]