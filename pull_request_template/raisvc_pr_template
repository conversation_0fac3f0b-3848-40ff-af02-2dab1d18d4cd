# Evaluation Service PR Checklist

- [ ] Skip filling details if this is a trivial/urgent PR.

## PR Type and Risk
- Type: [ ] Feature [ ] Bugfix [ ] Refactor [ ] Config [ ] Test [ ] Security/Compliance
- Affects P0 scenario(s): [ ] Yes [ ] No
- Breaking change (API/contract/config behavior): [ ] Yes [ ] No

## Description
### Summary
<!-- Summarize the change -->

### Potential issues/open questions
<!-- Callouts for reviewer discussion (trade-offs, alternatives, known gaps) -->

## Configuration and Flighting
- Does this PR introduce or change configuration? [ ] Yes [ ] No
  - If Yes, scope: [ ] appsettings.* [ ] Regional YAML [ ] ECS flags
- Feature flag(s) involved: [ ] New [ ] Existing [ ] None
  - Flag name(s) and default(s):
  - Exposure scope: [ ] Off by default [ ] % rollout [ ] Region-scoped

## Observability, Metering, and Ops
- Logs: [ ] Added/updated meaningful Info/Warn/Error
- Metrics: [ ] Added/updated counters/gauges/histograms [ ] N/a

## Testing
### Unit Tests
- Coverage for new/changed logic: [ ] Yes [ ] No (justify)
  - Options (pick at least one):
    - [ ] Pure config/docs-only change
    - [ ] Trivial refactor/no logic change
    - [ ] Covered by existing tests
    - [ ] Follow-up task created to add unit tests (link here)

### Integration Tests
- Added/updated integration tests: [ ] Yes [ ] No [ ] N/A (justify if No)

### E2E Tests
- P0 scenario impacted: [ ] Yes [ ] No
  - If Yes:
    - At least one E2E test added or existing validated: [ ] Yes (link to run here) [ ] No [ ] Already exists
    - If No, follow-up created: [link here]

## Documentation
- Updated service docs/TSG/README if applicable: [ ] Yes [ ] N/A [ ] Will do later [paste task below]
  - Link: https://eng.ms/docs/cloud-ai-platform/ai-platform/ai-platform-ml-platform/project-vienna-services/azure-machine-learning-runbook/operational/raievaluationsservice/overview
- External doc update neeeded: [ ] Yes [ ] N/A
  - If Yes:  (link to task for PM here)