﻿namespace Microsoft.MachineLearning.Compute.IntegrationTests.Tests.DataFactory
{
    using System;
    using System.Collections.Generic;
    using System.Net.Http;
    using System.Text;
    using System.Threading;
    using System.Threading.Tasks;
    using Microsoft.Extensions.DependencyInjection;
    using Microsoft.Extensions.Logging;
    using Microsoft.MachineLearning.AzureResourceManager.ARM.DataFactory;
    using Microsoft.MachineLearning.AzureResourceManager.ResourceIds.Provider;
    using Microsoft.MachineLearning.Compute.IntegrationTests.Authorization;
    using Microsoft.MachineLearning.Compute.IntegrationTests.Options;
    using Xunit;
    using Xunit.Abstractions;

    public class DataFactoryRestTests : BaseTest
    {
        private readonly IArmResourceProviderCredentials azureCredentialService;
        private readonly MlcServiceOptions mlcServiceOptions;
        private readonly ILoggerFactory loggerFactory;

        public DataFactoryRestTests(ITestOutputHelper output) : base(output)
        {
            this.azureCredentialService = this.ServiceProvider.GetRequiredService<IArmResourceProviderCredentials>();
            this.mlcServiceOptions = this.ServiceProvider.GetRequiredService<MlcServiceOptions>();
            this.loggerFactory = this.ServiceProvider.GetRequiredService<ILoggerFactory>();
        }

        [Fact]
        [Trait("Category", "Integration")]
        [Trait("Category", "Fast")]
        [Trait("ComputeType", "DataFactory")]
        public async Task DataFactoryClientRestTests()
        {
            var armToken = await this.azureCredentialService.GetAccessTokenAsync(CancellationToken.None).ConfigureAwait(false);

            var dataFactoryClient = new DataFactoryClient("AzureGlobalCloud", new HttpClientFactory(), this.loggerFactory.CreateLogger<DataFactoryClient>());

            var dataFactory = await dataFactoryClient.GetDataFactoryAsync(
                this.ResourceAttachOptions.DataFactoryId.SubscriptionId.Name,
                this.ResourceAttachOptions.DataFactoryId.ResourceGroupId.Name,
                this.ResourceAttachOptions.DataFactoryId.Name,
                armToken,
                CancellationToken.None).ConfigureAwait(false);
        }
    }

    internal class HttpClientFactory : IHttpClientFactory
    {
        public HttpClientFactory()
        {
        }

        public HttpClient CreateClient(string name)
        {
            return new HttpClient();
        }
    }
}
