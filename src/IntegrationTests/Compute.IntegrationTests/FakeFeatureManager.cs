﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.FeatureManagement;

namespace Microsoft.MachineLearning.Compute.IntegrationTests
{
    public class FakeFeatureManager : IFeatureManager
    {
        public IAsyncEnumerable<string> GetFeatureNamesAsync()
        {
            throw new NotImplementedException();
        }

        public Task<bool> IsEnabledAsync(string feature)
        {
            return Task.FromResult(false);
        }

        public Task<bool> IsEnabledAsync<TContext>(string feature, TContext context)
        {
            return Task.FromResult(false);
        }
    }
}
