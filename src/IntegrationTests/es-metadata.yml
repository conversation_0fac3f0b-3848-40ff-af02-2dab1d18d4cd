# 1ES Inventory configuration file:
# These files control automatic routing of bot-created issues; if there is an error, please make a PR with corrections
# See https://eng.ms/docs/cloud-ai-platform/ai-platform/ai-platform-ml-platform/project-vienna-services/azure-machine-learning-runbook/operational/common/tsg/inventoryyamlfiles for more information.
#     For any incorrect information in the AML runbook, please update the page as needed.
# See https://aka.ms/inventory-as-code for examples, reference, and how-to guides.

schemaVersion: 0.0.1

# Classification isProduction - Declares whether the code is production or non-production.
# integration tests - not production
isProduction: false

# Every 1es-inventory.yml file must contain both an 'accountableOwners' and 'routing' section
accountableOwners:
  # Service ID from the Service Tree. 776fcc4a-80c5-455a-8490-449352e5b55b = 'Project Vienna Services'
  service: 031f9ebe-1efe-435a-bbde-68e7f4830a49

routing:
  # Azure DevOps area path - please update at the greatest-depth Inventory.yml file if incorrect for the current path.
  # No area path found for testing, so used Vienna\Compute
  defaultAreaPath:
    org: msdata
    path: Vienna\Compute
