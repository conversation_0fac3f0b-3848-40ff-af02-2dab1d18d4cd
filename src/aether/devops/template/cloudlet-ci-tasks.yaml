parameters:
  - name: app
    type: string
  - name: projectPath
    type: string
  - name: additionalProjectPaths
    type: object
    default: []
  - name: disableDrop
    type: string
    default: false
  - name: buildConfiguration
    type: string
    default: "Release"
  - name: templateType
    type: string
    values:
      - Official
      - NonOfficial
    default: Official

resources:
  repositories:
    - repository: templates
      type: git
      name: OneBranch.Pipelines/GovernedTemplates
      ref: refs/heads/main

extends:
  template: v2/OneBranch.${{ parameters.templateType }}.CrossPlat.yml@templates # https://aka.ms/obpipelines/templates
  parameters:
    git:
      submodules: false
    globalSdl:
      asyncSdl: # https://aka.ms/obpipelines/asyncsdl
        enabled: false
      cg:
        failOnAlert: false
      tsa:
        enabled: false # or false
      credscan:
        suppressionsFile: src\azureml-api\src\Designer\.config\CredScanSuppressions.json
      codeql:
        cadence: 0 # Always refresh CodeQL on every build
    stages:
      - stage: build_image
        displayName: Build Image
        jobs:
          - job: Build
            displayName: Build and Test
            pool:
              name: pipeline-1ES-ubuntu
              type: linux
              isCustom: true
            variables:
              ob_artifactBaseName: drop
            steps:
              - checkout: self
                clean: true
                fetchDepth: 1
                fetchTags: false
                submodules: false

              - task: ComponentGovernanceComponentDetection@0
                displayName: Component Detection
                inputs:
                  scanType: Register
                  verbosity: Verbose
                  alertWarningLevel: High
                  sourceScanPath: src/aether

              - task: UseDotNet@2
                displayName: "Use .NET sdk from global.json"
                inputs:
                  packageType: sdk
                  useGlobalJson: true
                  installationPath: "$(Agent.TempDirectory)/dotnet"

              - task: UseDotNet@2
                displayName: "Use .NET 6.0 sdk"
                inputs:
                  packageType: sdk
                  version: 6.0.x
                  installationPath: "$(Agent.TempDirectory)/dotnet"

              - task: Bash@3
                displayName: Change nuget packages and cache to be on larger disk
                inputs:
                  targetType: inline
                  script: |
                    echo "##vso[task.setvariable variable=NUGET_PACKAGES;]$(Agent.TempDirectory)/.nuget/packages"
                    echo "##vso[task.setvariable variable=MSFTKUBE_NUGET_CACHE_LOCATION;]$(Agent.TempDirectory)/.cache"

              - task: NuGetAuthenticate@1

              - task: DotNetCoreCLI@2
                displayName: dotnet build ${{ parameters.projectPath }}
                inputs:
                  projects: ${{ parameters.projectPath }}/dirs.proj
                  arguments: --configuration ${{parameters.buildConfiguration}}

              - task: AzureCLI@2
                displayName: dotnet test ${{ parameters.projectPath }}
                env:
                  TestHarness: vsts
                inputs:
                  connectedServiceNameARM: fe019f69-b722-4325-ac3d-6b841e99dff1
                  scriptType: bash
                  scriptLocation: inlineScript
                  inlineScript: |
                    set -e -v
                    dotnet test ${{ parameters.projectPath }}/dirs.proj \
                      --configuration ${{parameters.buildConfiguration}} \
                      --no-build \
                      --logger:trx \
                      --collect "Code Coverage" \
                      --settings ${{ parameters.projectPath }}/CodeCoverage.runsettings \
                      --results-directory "$(Agent.TempDirectory)" \

              - ${{ each projectPath in parameters.additionalProjectPaths }}:
                  - task: DotNetCoreCLI@2
                    displayName: dotnet build ${{ projectPath }}
                    inputs:
                      projects: ${{ projectPath }}/dirs.proj
                      arguments: --configuration ${{parameters.buildConfiguration}}

                  - task: AzureCLI@2
                    displayName: dotnet test ${{ projectPath }}
                    env:
                      TestHarness: vsts
                    inputs:
                      connectedServiceNameARM: fe019f69-b722-4325-ac3d-6b841e99dff1
                      scriptType: bash
                      scriptLocation: inlineScript
                      inlineScript: |
                        set -e -v
                        dotnet test ${{ projectPath }}/dirs.proj \
                          --configuration ${{parameters.buildConfiguration}} \
                          --no-build \
                          --logger:trx \
                          --collect "Code Coverage" \
                          --settings ${{ projectPath }}/CodeCoverage.runsettings \
                          --results-directory "$(Agent.TempDirectory)" \

              - task: PublishTestResults@2
                displayName: Publish Test Results
                condition: succeededOrFailed()
                inputs:
                  testRunner: VSTest
                  testResultsFiles: "**/*.trx"
                  searchFolder: $(Agent.TempDirectory)

              - ${{ if and(ne(parameters.disableDrop, 'true'), ne(variables['Build.Reason'], 'PullRequest')) }}:   # Only generate drop for non-PR builds
                - task: PipAuthenticate@1
                  displayName: Use Azure Artifacts feed
                  inputs:
                    artifactFeeds: "Vienna"
                - script: |
                    docker system prune --all --force
                  displayName: 'Docker clean up'
                - task: DotNetCoreCLI@2
                  displayName: Build msftkubeConfig
                  inputs:
                    projects: src/aether/platform/msftkubeConfig/msftkubeconfig.proj
                    arguments: --configuration ${{parameters.buildConfiguration}}
                - task: UsePythonVersion@0
                  displayName: "Use Python 3.9"
                  inputs:
                    versionSpec: 3.9
                - task: AzureCLI@1
                  displayName: Create Drop
                  inputs:
                    azureSubscription: "INFRA Viennadroptest connection"
                    scriptType: bash
                    scriptLocation: inlineScript
                    inlineScript: |
                      # Set environment variable to workaround bug in Azure CLI: https://github.com/Azure/azure-cli/issues/31419
                      export AZURE_CORE_USE_MSAL_HTTP_CACHE=false

                      set -e -v
                      dotnet publish ${{ parameters.projectPath }}/Services -c Release --no-restore -v minimal
                      bash target/distrib/Release/AnyCPU/app/aether/bluebox/msftkubeconfig/scripts/task.sh finish_drop -a ${{ parameters.app }}
                - task: ManifestGeneratorTask@0
                  displayName: "Generate SBOM for package"
                  inputs:
                    BuildDropPath: target/distrib/Release/AnyCPU/app/aether/bluebox/msftkubeconfig/
                - task: PublishPipelineArtifact@1
                  displayName: Publish Pipeline Artifact
                  inputs:
                    targetPath: target/distrib/Release/AnyCPU/app/aether/bluebox/msftkubeconfig/
                    artifactName: drop
                    artifact: drop
                - script: |
                    docker system prune --all --force
                  condition: always()
                  displayName: 'Docker clean up'
