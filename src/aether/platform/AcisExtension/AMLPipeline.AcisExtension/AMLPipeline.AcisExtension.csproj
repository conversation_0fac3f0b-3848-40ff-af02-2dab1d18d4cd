﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{1FBB6791-195C-4BA0-8B61-7751E13F41DB}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Microsoft.MachineLearning.Pipeline.AcisExtension</RootNamespace>
    <AssemblyName>AMLPipeline.AcisExtension</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
    <RuntimeIdentifiers>win-x64</RuntimeIdentifiers>
    <LangVersion>latest</LangVersion>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <PlatformTarget>x64</PlatformTarget>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <PlatformTarget>x64</PlatformTarget>
  </PropertyGroup>
  <PropertyGroup>
    <GASMEPackageInputDir>$(MSBuildProjectDirectory)\$(OutputPath)</GASMEPackageInputDir>
    <GASMEPackageOutputDir>$(MSBuildProjectDirectory)\GAPackage</GASMEPackageOutputDir>
    <GAPackageName>pipeline_AcisExtension</GAPackageName>
    <GASMEAssembly>$(AssemblyName).dll</GASMEAssembly>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Client\ViennaClient.cs" />
    <Compile Include="Common\AcisEndpointSettings.cs" />
    <Compile Include="Common\Consts.cs" />
    <Compile Include="Common\Utils.cs" />
    <Compile Include="Extensions\PipelineAcisExtension.cs" />
    <Compile Include="OperationGroups\PromptFlowOperationGroup.cs" />
    <Compile Include="OperationGroups\DCMOperationGroup.cs" />
    <Compile Include="Operations\DCM\BatchCancelPipelineRunsOperation.cs" />
    <Compile Include="Operations\DCM\DeleteWorkspaceSchedulesOperation.cs" />
    <Compile Include="Operations\DCM\ClearReuseCacheOperation.cs" />
    <Compile Include="Operations\DCM\CopyDMTCacheResultOperation.cs" />
    <Compile Include="Operations\DCM\DisableScheduleOperation.cs" />
    <Compile Include="Operations\DCM\GetNodeHashByReferenceIdOperation.cs" />
    <Compile Include="Operations\DCM\GetNodeInfoOperation.cs" />
    <Compile Include="Operations\DCM\GenevaOperationHelper.cs" />
    <Compile Include="Operations\DCM\GetExperimentEntityOperation.cs" />
    <Compile Include="Operations\DCM\GetGraphEntityOperation.cs" />
    <Compile Include="Operations\DCM\GetModuleEntityOperation.cs" />
    <Compile Include="Operations\DCM\GetNodeHashFromDMTOperation.cs" />
    <Compile Include="Operations\DCM\GetNodesByReuseRunIdOperation.cs" />
    <Compile Include="Operations\DCM\GetReuseCacheOperation.cs" />
    <Compile Include="Operations\DCM\GetScheduleEntityOperation.cs" />
    <Compile Include="Operations\DCM\PingOperation.cs" />
    <Compile Include="Operations\DCM\BatchUpdateExperimentStatusToCanceledOperation.cs" />
    <Compile Include="Operations\DCM\SubmitPipelineRunOperation.cs" />
    <Compile Include="Operations\DCM\UpdateScheduleProvisioningStatusOperation.cs" />
    <Compile Include="Operations\DCM\GetQuotaOperation.cs" />
    <Compile Include="Operations\DCM\UpdateQuotaLimitOperation.cs" />
    <Compile Include="Operations\DCM\CleanupWorkspaceStorageOperation.cs" />
    <Compile Include="Operations\PromptFlow\CancelRunOperation.cs" />
    <Compile Include="Operations\PromptFlow\RunCommandInSessionOperation.cs" />
    <Compile Include="Operations\PromptFlow\RunCommandInRuntimeOperation.cs" />
    <Compile Include="Parameters\PromptFlowParameters.cs" />
    <Compile Include="Parameters\CommonParameters.cs" />
    <Compile Include="Parameters\EnvParameter.cs" />
    <Compile Include="Parameters\QuotaParameter.cs" />
    <Compile Include="Parameters\RegionParameter.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="AcisExtensionSDK" />
    <PackageReference Include="Microsoft.WindowsAzure.Wapd.Acis.Contracts" />
  </ItemGroup>
  <ItemGroup>
    <SMEConfigFiles Include="Configs\*.sme.config" />
  </ItemGroup>
  <!--Package can only be built on Windows build agent-->
  <ItemGroup Condition="$([MSBuild]::IsOSPlatform('Windows'))">
    <GAPackageEnvironment Include="Public;Test;PublicBeta;Fairfax;Mooncake;USSec">
      <SMEConfigFile>Pipeline.%(GAPackageEnvironment.Identity).sme.config</SMEConfigFile>
    </GAPackageEnvironment>
  </ItemGroup>
  <ItemGroup>
    <None Include="Common\LocalPort.json" />
  </ItemGroup>
  <ItemGroup>
    <SMEConfigFiles Include="Configs\Pipeline.PublicBeta.sme.config" />
  </ItemGroup>
  <ItemGroup>
    <SMEConfigFiles Include="Configs\Pipeline.Fairfax.sme.config" />
  </ItemGroup>
  <ItemGroup />
  <Target Name="CopySMEConfigurationFiles" BeforeTargets="Build">
    <Copy SourceFiles="@(SMEConfigFiles)" DestinationFolder="$(GASMEPackageInputDir)" />
  </Target>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>