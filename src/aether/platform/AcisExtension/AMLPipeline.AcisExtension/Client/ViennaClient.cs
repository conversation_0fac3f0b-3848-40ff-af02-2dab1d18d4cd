﻿// <copyright file="ViennaClient.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Microsoft.MachineLearning.Pipeline.AcisExtension.Common;
using Microsoft.WindowsAzure.Wapd.Acis.Contracts;
using System;
using System.Net;
using System.Net.Http;
using System.Security.Cryptography.X509Certificates;
using System.Threading.Tasks;

namespace Microsoft.MachineLearning.Pipeline.AcisExtension.Client
{
    public sealed class ViennaClient : IDisposable
    {
        private HttpClient _httpClient;

        public ViennaClient(IAcisServiceManagementExtension extension, IAcisSMEOperationProgressUpdater updater)
        {
            X509Certificate2 clientCert = extension.Certificates.GetByIdentifier(AcisEndpointSettings.ClientCertIdentifier).Contents;

            updater.WriteLine($"Got cert with thumbprint [{clientCert.Thumbprint}]");

            var handler = CreateHttpClientHandler(clientCert);
            _httpClient = new HttpClient(handler);
            _httpClient.Timeout = TimeSpan.FromMinutes(10);
        }

        public ViennaClient(X509Certificate2 cert)
        {
            var handler = CreateHttpClientHandler(cert);
            _httpClient = new HttpClient(handler);
            _httpClient.Timeout = TimeSpan.FromMinutes(10);
        }

        public async Task<(HttpStatusCode, string)> GetAsync(string uri)
        {
            var response = await _httpClient.GetAsync(uri).ConfigureAwait(false);
            var message = await response.Content.ReadAsStringAsync().ConfigureAwait(false);

            return (response.StatusCode, message);
        }

        public async Task<(HttpStatusCode, string)> PostAsync(string uri, StringContent bodyContent)
        {
            var response = await _httpClient.PostAsync(uri, bodyContent).ConfigureAwait(false);
            var message = await response.Content.ReadAsStringAsync().ConfigureAwait(false);

            return (response.StatusCode, message);
        }

        public async Task<(HttpStatusCode, string)> PatchAsync(string uri)
        {
            var request = new HttpRequestMessage(new HttpMethod("PATCH"), uri);
            var response = await _httpClient.SendAsync(request).ConfigureAwait(false);
            var message = await response.Content.ReadAsStringAsync().ConfigureAwait(false);

            return (response.StatusCode, message);
        }

        public async Task<(HttpStatusCode, string)> DeleteAsync(string uri)
        {
            var response = await _httpClient.DeleteAsync(uri).ConfigureAwait(false);
            var message = await response.Content.ReadAsStringAsync().ConfigureAwait(false);

            return (response.StatusCode, message);
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
        }

        private HttpClientHandler CreateHttpClientHandler(X509Certificate2 cert)
        {
            var handler = new HttpClientHandler()
            {
                AutomaticDecompression = DecompressionMethods.Deflate | DecompressionMethods.GZip,
                ClientCertificateOptions = ClientCertificateOption.Manual
            };

            if (cert != null)
            {
                handler.ClientCertificates.Add(cert);
            }

            return handler;
        }
    }
}
