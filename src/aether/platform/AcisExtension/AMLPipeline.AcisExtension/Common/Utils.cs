﻿using System;
using Microsoft.WindowsAzure.Wapd.Acis.Contracts;

namespace Microsoft.MachineLearning.Pipeline.AcisExtension.Common
{
    static class Utils
    {
        public static string GenerateTargetUrl(string region, IAcisSMEEndpoint endPoint, string serviceName)
        {
            if (region.Equals("localhost", StringComparison.OrdinalIgnoreCase))
            {
                string port = endPoint.Configuration.GetConfigurationValue(serviceName);
                return $"http://127.0.0.1:{port}";
            }

            string serviceDomain = endPoint.Configuration.GetConfigurationValue(AcisEndpointSettings.ServiceDomain);
            if (region.Equals("centraluseuap", StringComparison.OrdinalIgnoreCase))
            {
                region = "master";
            }

            return $"https://{region}.cert.{serviceDomain}";
        }
    }
}
