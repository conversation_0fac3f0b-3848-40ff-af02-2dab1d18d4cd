﻿using Microsoft.WindowsAzure.Wapd.Acis.Contracts;

namespace Microsoft.MachineLearning.Pipeline.AcisExtension.Parameters
{
    class RegionParameter : AcisSMETextParameter
    {
        public override string Name => "Region";

        /// <summary>
        /// Help text that appears on the portal
        /// </summary>
        public override string HelpText => "Choose the required Region. ";

        /// <summary>
        /// Type of the parameter
        /// note that, though the render type is a drop down visually, 
        /// it must use the ConfigList render type rather than ListSelection.
        /// </summary>
        public override int RenderType => (int)ParameterRenderType.ConfigList;

        /// <summary>
        /// To indicate the type of validation needed.  
        /// </summary>
        public override int ValidationTypes => (int)ParameterValidationType.ConfigList;

        public override string ValidationArgs
        {
            get
            {
                ValidationArgsHelper helper = new ValidationArgsHelper();

                //this key is a reference to a config value with the key configValues,
                //which is located under EACH endpoint in the SME config. 
                //It MUST exist in every endpoint (that this parameter appears on)
                //note that there's also an "Extension" option in ValidationArgsHelper - 
                //this uses the config that's at the extension level, rather than the endpoint level.
                helper.SetEndpointConfigListArgs("Region");

                return helper.ToString();
            }
        }
    }
}
