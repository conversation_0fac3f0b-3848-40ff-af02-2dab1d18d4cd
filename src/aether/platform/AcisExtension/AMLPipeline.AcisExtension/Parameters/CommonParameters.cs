﻿using Microsoft.WindowsAzure.Wapd.Acis.Contracts;

namespace Microsoft.MachineLearning.Pipeline.AcisExtension.Parameters
{
    /// <summary>
    /// Base class for text Parameters
    /// </summary>
    internal abstract class BaseTextParameter : AcisSMEDefaultTextParameter
    {
        /// <summary>
        /// Type of the parameter
        /// </summary>
        public override int RenderType => (int)ParameterRenderType.Text;

        /// <summary>
        /// To indicate the type of validation needed. 
        /// Custom validation call IsParameterValid method.
        /// </summary>
        public override int ValidationTypes => (int)ParameterValidationType.Custom;

        /// <summary>
        /// Default value of the parameter
        /// </summary>
        public override string DefaultText => string.Empty;
    }

    /// <summary>
    /// Base class to be inherited by all simple text parameters 
    /// with simple not null validation
    /// </summary>
    internal abstract class BaseTextRequiredParameter : BaseTextParameter
    {
        /// <summary>
        /// Method to implement validation
        /// </summary>
        /// <param name="inputValue">the value that user gives</param>
        /// <param name="validationArgs">Data required to validate</param>
        /// <param name="correctedValue">if the value is not valid the corrected value is given here</param>
        /// <param name="errorMessage">The error message</param>
        /// <returns>true or false based on validation success or failure</returns>
        public override bool IsParameterValid(
            string inputValue, string validationArgs, ref object correctedValue, ref string errorMessage)
        {
            //Checks if User has not entered any value.
            if (string.IsNullOrWhiteSpace(inputValue))
            {
                errorMessage =
                    $"{Name} is mandatory. Please entered the value.";
                return false;
            }
            return true;
        }
    }

    class ResourceGroupNameParameter : BaseTextRequiredParameter
    {
        public override string Name => "Resource group name";

        public override string HelpText => "Specify the resource group name.";
    }

    class WorkspaceNameParameter : BaseTextRequiredParameter
    {
        public override string Name => "AML Workspace";

        public override string HelpText => "Specify the workspace name.";
    }

    class WorkspaceGuidParameter : BaseTextParameter
    {
        public override string Name => "Workspace Id";
        public override string HelpText => "Specify workspace Id.";
    }

    class PipelineRunIdParameter : BaseTextParameter
    {
        public override string Name => "Pipeline Run Id";
        public override string HelpText => "Specify the PipelineRunId of pipeline. It's displayed at the right panel of pipeline level";
    }

    class NodeIdParameter : BaseTextParameter
    {
        public override string Name => "Node Id";
        public override string HelpText => "Specify node id.";
    }

    class ReferenceIdParameter : BaseTextParameter
    {
        public override string Name => "Reference Id";
        public override string HelpText => "Specify the ReferenceId got from reuse cache meta";
    }

    class ComputeNameParameter : BaseTextParameter
    {
        public override string Name => "Compute Name";

        public override string HelpText => "Specify Compute Name.";
    }

    class ModuleIdParameter : BaseTextParameter
    {
        public override string Name => "Module Id";

        public override string HelpText => "Specify module id.";
    }

    class SubscriptionIdParameter : BaseTextParameter
    {
        public override string Name => "Subscription Id";

        public override string HelpText => "Specify subscription id.";
    }

    class ScheduleIdParameter : BaseTextParameter
    {
        public override string Name => "Schedule Id";

        public override string HelpText => "Specify schedule id.";
    }

    class PipelineRunIdGroupParameter : BaseTextParameter
    {
        public override string Name => "Pipeline Run Ids";

        public override string HelpText => "Specify a pipeline run id or multiple pipeline run ids separated by comma.";
    }

    class ReusedRunIdParameter : BaseTextParameter
    {
        public override string Name => "Reused Run Id";

        public override string HelpText => "Specify reused run Id.";
    }

    class SourceRunIdParameter : BaseTextParameter
    {
        public override string Name => "Source Run Id";

        public override string HelpText => "Specify source run Id. We want destination run to reuse to it";
    }

    class DestinationRunIdParameter : BaseTextParameter
    {
        public override string Name => "Destination Run Id";

        public override string HelpText => "Specify destination run Id. The destination will reuse source run after operation";
    }

    class ScheduleProvisioningStatusParameter : BaseTextParameter
    {
        public override string Name => "Schedule Provisioning Status";

        public override string HelpText => "Specify new schedule provisioning status.";
    }

    class QuotaLimitParameter : BaseTextParameter
    {
        public override string Name => "Quota Limit";

        public override string HelpText => "Specify new quota limit for update.";
    }

    class NodeHashSourceParameter : AcisSMETextParameter
    {
        public override string Name => "NodeHashSource";
        public override int RenderType => (int)ParameterRenderType.ConfigList;
        public override int ValidationTypes => (int)ParameterValidationType.ConfigList;

        public override string HelpText => "NodeHash Source Table (e.g. DCM, Cloudlet, APCloud)";

        public override string ValidationArgs
        {
            get
            {
                ValidationArgsHelper helper = new ValidationArgsHelper();
                helper.SetEndpointConfigListArgs("NodeHashSource");

                return helper.ToString();
            }
        }
    }

    class ForceCancelParameter : AcisSMETextParameter
    {
        public override string Name => "ForceCancel";
        public override int RenderType => (int)ParameterRenderType.ConfigList;
        public override int ValidationTypes => (int)ParameterValidationType.ConfigList;
        public override string HelpText => "Force cancel step runs even if they are latched";

        public override string ValidationArgs
        {
            get
            {
                ValidationArgsHelper helper = new ValidationArgsHelper();
                helper.SetEndpointConfigListArgs("ForceCancel");

                return helper.ToString();
            }
        }
    }

    class CancelFinalPhaseParameter : AcisSMETextParameter
    {
        public override string Name => "CancelFinalPhase";
        public override int RenderType => (int)ParameterRenderType.ConfigList;
        public override int ValidationTypes => (int)ParameterValidationType.ConfigList;

        public override string HelpText => "Cancel finalization phase?";

        public override string ValidationArgs
        {
            get
            {
                ValidationArgsHelper helper = new ValidationArgsHelper();
                helper.SetEndpointConfigListArgs("CancelFinalPhase");

                return helper.ToString();
            }
        }
    }
}
