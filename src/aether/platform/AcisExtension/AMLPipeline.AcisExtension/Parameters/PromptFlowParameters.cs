﻿using Microsoft.WindowsAzure.Wapd.Acis.Contracts.SimplificationClasses;
using System.Collections.Generic;

namespace Microsoft.MachineLearning.Pipeline.AcisExtension.Parameters
{
    class SessionIdParameter : BaseTextParameter
    {
        public override string Name => "PromptFlow Session Id";
        public override string HelpText => "Specify the SessionId of Promptflow automatic runtime. It's the flowId for a flow";
    }

    class RuntimeNameParameter : BaseTextParameter
    {
        public override string Name => "CI Runtime Name";
        public override string HelpText => "Specify the name of promptflow CI runtime.";
    }

    class CommmandParameter : BaseTextParameter
    {
        public override string Name => "Command string";
        public override string HelpText => "Specify the command string running in the machine of runtime.";
    }

    class FlowIdParameter : BaseTextRequiredParameter
    {
        public override string Name => "Flow Id";
        public override string HelpText => "Flow Id of batch run or flow run.";
    }

    class RunIdParameter : BaseTextRequiredParameter
    {
        public override string Name => "Run Id";
        public override string HelpText => "Run Id of batch run or flow run.";
    }

    class FlowRunModeParameter : AcisSMEStringListParameter
    {
        public override string Name => "Flow Run Mode";

        public override string HelpText => "Select flow run mode, default as BulkTest.";

        public override IEnumerable<string> GetStringValuesForDropdown()
        {
            return new[] { "BulkTest", "Flow" };
        }
    }
}
