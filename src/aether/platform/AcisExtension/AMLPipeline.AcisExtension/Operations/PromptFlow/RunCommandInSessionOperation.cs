﻿using System.Collections.Generic;
using System.Net.Http;
using Microsoft.MachineLearning.Pipeline.AcisExtension.Common;
using Microsoft.MachineLearning.Pipeline.AcisExtension.OperationGroups;
using Microsoft.MachineLearning.Pipeline.AcisExtension.Operations.GenevaOperationHelper;
using Microsoft.MachineLearning.Pipeline.AcisExtension.Parameters;
using Microsoft.WindowsAzure.Wapd.Acis.Contracts;
using Microsoft.WindowsAzure.Wapd.Acis.Contracts.Models;

namespace Microsoft.MachineLearning.Pipeline.AcisExtension.Operations.RunCommandInSession
{
    internal class RunCommandInSessionOperation : AcisSMEOperation
    {
        private const string _serviceName = "PromptFlowService";

        private const string _urlTemplate = "{0}/flow/api/subscriptions/{1}/resourceGroups/{2}/providers/Microsoft.MachineLearningServices/workspaces/{3}/FlowRuntimesAdmin/session/{4}/runCommand";

        public override IAcisSMEOperationGroup OperationGroup => PromptFlowOperationGroup.Instance;

        public override string OperationName => "Run Command In Session";

        public override string HelpText => @"Run command will need take 5-10 seconds based on command input. Some un-terminated command would run into timeout in 30 seconds";

        public override DataAccessLevel SystemMetadata { get; } = DataAccessLevel.ReadWrite;

        public override DataClassificationLevel DataClassificationLevel => DataClassificationLevel.NoCustomerContent;

        public override IEnumerable<AcisUserClaim> ClaimsRequired => new[]
        {
            AcisSMESecurityGroup.ClientPlatformServiceOperator(Consts.SCOPE_AMLPIPELINE)
        };

        public override IEnumerable<IAcisSMEParameterRef> Parameters =>
           new[]
           {
                ParamRefFromParam.Get<RegionParameter>(),
                ParamRefFromParam.Get<SubscriptionIdParameter>(),
                ParamRefFromParam.Get<ResourceGroupNameParameter>(),
                ParamRefFromParam.Get<WorkspaceNameParameter>(),
                ParamRefFromParam.Get<SessionIdParameter>(),
                ParamRefFromParam.Get<CommmandParameter>(),
           };

        public IAcisSMEOperationResponse RunCommandInSession(
            string region,
            string subscriptionId,
            string resourceGroupName,
            string workspaceName,
            string sessionId,
            string command,
            IAcisServiceManagementExtension extension,
            IAcisSMEOperationProgressUpdater updater,
            IAcisSMEEndpoint endpoint)
        {
            string baseUrl = Utils.GenerateTargetUrl(region, endpoint, _serviceName);
            var url = string.Format(_urlTemplate, baseUrl, subscriptionId, resourceGroupName, workspaceName, sessionId);
            var stringContent = new StringContent(command, System.Text.Encoding.UTF8, mediaType: "application/json");
            return GenevaOperationHelpers.RunGenevaOperation(region, url, OperationName, extension, updater, HttpMethod.Post, stringContent);
        }
    }
}
