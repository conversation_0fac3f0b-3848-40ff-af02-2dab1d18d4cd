﻿using System.Collections.Generic;
using System.Net.Http;
using Microsoft.MachineLearning.Pipeline.AcisExtension.Common;
using Microsoft.MachineLearning.Pipeline.AcisExtension.OperationGroups;
using Microsoft.MachineLearning.Pipeline.AcisExtension.Operations.GenevaOperationHelper;
using Microsoft.MachineLearning.Pipeline.AcisExtension.Parameters;
using Microsoft.WindowsAzure.Wapd.Acis.Contracts;
using Microsoft.WindowsAzure.Wapd.Acis.Contracts.Models;

namespace Microsoft.MachineLearning.Pipeline.AcisExtension.Operations.PromptFlow
{
    internal class CancelRunOperation : AcisSMEOperation
    {
        private const string _serviceName = "PromptFlowService";

        private const string _urlTemplate = "{0}/pfo/api/subscriptions/{1}/resourceGroups/{2}/providers/Microsoft.MachineLearningServices/workspaces/{3}/FlowOrchestratorAdmin/cancel/flow/{4}/run/{5}?runMode={6}";

        public override IAcisSMEOperationGroup OperationGroup => PromptFlowOperationGroup.Instance;

        public override string OperationName => "Cancel Run";

        public override string HelpText => @"Cancel stuck batch run or flow run.";

        public override DataAccessLevel SystemMetadata { get; } = DataAccessLevel.ReadWrite;

        public override DataClassificationLevel DataClassificationLevel => DataClassificationLevel.NoCustomerContent;

        public override IEnumerable<AcisUserClaim> ClaimsRequired => new[]
        {
            AcisSMESecurityGroup.ClientPlatformServiceOperator(Consts.SCOPE_AMLPIPELINE)
        };

        public override IEnumerable<IAcisSMEParameterRef> Parameters =>
           new[]
           {
                ParamRefFromParam.Get<RegionParameter>(),
                ParamRefFromParam.Get<SubscriptionIdParameter>(),
                ParamRefFromParam.Get<ResourceGroupNameParameter>(),
                ParamRefFromParam.Get<WorkspaceNameParameter>(),
                ParamRefFromParam.Get<FlowIdParameter>(),
                ParamRefFromParam.Get<RunIdParameter>(),
                ParamRefFromParam.Get<FlowRunModeParameter>(),
           };

        public IAcisSMEOperationResponse CancelRun(
            string region,
            string subscriptionId,
            string resourceGroupName,
            string workspaceName,
            string flowId,
            string runId,
            string flowRunMode,
            IAcisServiceManagementExtension extension,
            IAcisSMEOperationProgressUpdater updater,
            IAcisSMEEndpoint endpoint)
        {
            string baseUrl = Utils.GenerateTargetUrl(region, endpoint, _serviceName);
            var url = string.Format(_urlTemplate, baseUrl, subscriptionId, resourceGroupName, workspaceName, flowId, runId, flowRunMode);
            return GenevaOperationHelpers.RunGenevaOperation(region, url, OperationName, extension, updater, HttpMethod.Post, null);
        }
    }
}
