﻿using System.Collections.Generic;
using System.Net.Http;
using Microsoft.MachineLearning.Pipeline.AcisExtension.Common;
using Microsoft.MachineLearning.Pipeline.AcisExtension.OperationGroups;
using Microsoft.MachineLearning.Pipeline.AcisExtension.Operations.GenevaOperationHelper;
using Microsoft.MachineLearning.Pipeline.AcisExtension.Parameters;
using Microsoft.WindowsAzure.Wapd.Acis.Contracts;
using Microsoft.WindowsAzure.Wapd.Acis.Contracts.Models;

namespace Microsoft.MachineLearning.Pipeline.AcisExtension.Operations.UpdateScheduleProvisioningStatus
{
    internal class UpdateScheduleProvisioningStatusOperation : AcisSMEOperation
    {
        private const string _serviceName = "DiagnosticService";

        private const string _urlTemplate = "{0}/diagnostic/v1.0/subscription/{1}/resourcegroup/{2}/workspace/{3}/schedule/{4}?ProvisioningStatus={5}";

        public override string HelpText => @"
Update provisioning status of a schedule entity.

The input provisioning status must be either Completed, Provisioning or Failed.
";

        public override IAcisSMEOperationGroup OperationGroup => DCMOperationGroup.Instance;

        public override string OperationName => "Update Schedule Provision Status";

        public override DataAccessLevel SystemMetadata { get; } = DataAccessLevel.ReadWrite;

        // Schedule entity contains user name and Puid.
        public override DataClassificationLevel DataClassificationLevel => DataClassificationLevel.EUPI | DataClassificationLevel.EUII;

        public override IEnumerable<AcisUserClaim> ClaimsRequired => new[]
        {
            AcisSMESecurityGroup.ClientCustomerServiceOperator(Consts.SCOPE_AMLPIPELINE),
            AcisSMESecurityGroup.ClientPlatformServiceOperator(Consts.SCOPE_AMLPIPELINE)
        };

        public override IEnumerable<IAcisSMEParameterRef> Parameters =>
            new[]
            {
                ParamRefFromParam.Get<RegionParameter>(),
                ParamRefFromParam.Get<SubscriptionIdParameter>(),
                ParamRefFromParam.Get<ResourceGroupNameParameter>(),
                ParamRefFromParam.Get<WorkspaceNameParameter>(),
                ParamRefFromParam.Get<ScheduleIdParameter>(),
                ParamRefFromParam.Get<ScheduleProvisioningStatusParameter>()
            };

        public IAcisSMEOperationResponse UpdateScheduleProvisioningStatus(
            string region,
            string subscriptionId,
            string resourceGroupName,
            string workspaceName,
            string scheduleId,
            string scheduleProvisioningStatus,
            IAcisServiceManagementExtension extension,
            IAcisSMEOperationProgressUpdater updater,
            IAcisSMEEndpoint endpoint)
        {
            string baseUrl = Utils.GenerateTargetUrl(region, endpoint, _serviceName);
            var url = string.Format(_urlTemplate, baseUrl, subscriptionId, resourceGroupName, workspaceName, scheduleId, scheduleProvisioningStatus);
            return GenevaOperationHelpers.RunGenevaOperation(region, url, OperationName, extension, updater, HttpMethod.Post);
        }
    }
}
