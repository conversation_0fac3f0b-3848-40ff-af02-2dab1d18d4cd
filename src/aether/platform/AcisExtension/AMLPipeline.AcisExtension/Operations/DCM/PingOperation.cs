﻿// <copyright file="PingOperation.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Collections.Generic;
using System.Net.Http;
using Microsoft.MachineLearning.Pipeline.AcisExtension.Common;
using Microsoft.MachineLearning.Pipeline.AcisExtension.OperationGroups;
using Microsoft.MachineLearning.Pipeline.AcisExtension.Operations.GenevaOperationHelper;
using Microsoft.MachineLearning.Pipeline.AcisExtension.Parameters;
using Microsoft.WindowsAzure.Wapd.Acis.Contracts;
using Microsoft.WindowsAzure.Wapd.Acis.Contracts.Models;

namespace Microsoft.MachineLearning.Pipeline.AcisExtension.Operations.Data
{
    public class PingOperation : AcisSMEOperation
    {
        private const string _serviceName = "DiagnosticService";

        private const string _urlTemplate = "{0}/diagnostic/v1.0/genevaaction/ping";

        public override string HelpText => "Test operation.";

        public override IAcisSMEOperationGroup OperationGroup => DCMOperationGroup.Instance;

        public override string OperationName => "Ping";

        public override DataAccessLevel SystemMetadata { get; } = DataAccessLevel.ReadOnly;

        public override DataClassificationLevel DataClassificationLevel => DataClassificationLevel.NoCustomerContent;

        public override IEnumerable<AcisUserClaim> ClaimsRequired => new[]
        {
            AcisSMESecurityGroup.ClientPlatformServiceOperator(Consts.SCOPE_AMLPIPELINE)
        };

        public override IEnumerable<IAcisSMEParameterRef> Parameters => 
            new[]
            { 
                ParamRefFromParam.Get<RegionParameter>()
            };

        public IAcisSMEOperationResponse Ping(
            string region,
            IAcisServiceManagementExtension extension,
            IAcisSMEOperationProgressUpdater updater,
            IAcisSMEEndpoint endpoint)
        {
            string baseUrl = Utils.GenerateTargetUrl(region, endpoint, _serviceName);
            var url = string.Format(_urlTemplate, baseUrl);
            return GenevaOperationHelpers.RunGenevaOperation(region, url, OperationName, extension, updater, HttpMethod.Get);
        }
    }
}
