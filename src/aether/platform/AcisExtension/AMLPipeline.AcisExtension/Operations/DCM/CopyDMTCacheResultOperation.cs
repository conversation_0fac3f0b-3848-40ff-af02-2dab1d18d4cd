﻿using System.Collections.Generic;
using System.Net.Http;
using Microsoft.MachineLearning.Pipeline.AcisExtension.Common;
using Microsoft.MachineLearning.Pipeline.AcisExtension.OperationGroups;
using Microsoft.MachineLearning.Pipeline.AcisExtension.Operations.GenevaOperationHelper;
using Microsoft.MachineLearning.Pipeline.AcisExtension.Parameters;
using Microsoft.WindowsAzure.Wapd.Acis.Contracts;
using Microsoft.WindowsAzure.Wapd.Acis.Contracts.Models;

namespace Microsoft.MachineLearning.Pipeline.AcisExtension.Operations.CopyDMTCacheResult
{
    internal class CopyDMTCacheResultOperation : AcisSMEOperation
    {
        private const string _serviceName = "DiagnosticService";

        private const string _urlTemplate = "{0}/diagnostic/v1.0/subscription/{1}/resourcegroup/{2}/workspace/{3}/sourcerun/{4}/destinationrun/{5}/copy?source={6}";

        public override string HelpText => @"
Copy the reuse cache content of source run to destination run.

Sometimes we want to fix reuse break quickly. We can use this action to copy the cache from source run to destination run.

And then if the destination run rerun, it will reuse the source run, but not destination run.

The source run must be a completed job.
";

        public override IAcisSMEOperationGroup OperationGroup => DCMOperationGroup.Instance;

        public override string OperationName => "Copy DMT Cache Result";

        public override DataClassificationLevel DataClassificationLevel => DataClassificationLevel.NoCustomerContent;

        public override DataAccessLevel SystemMetadata { get; } = DataAccessLevel.ReadWrite;

        public override IEnumerable<AcisUserClaim> ClaimsRequired => new[]
        {
            AcisSMESecurityGroup.ClientPlatformServiceOperator(Consts.SCOPE_AMLPIPELINE)
        };

        public override IEnumerable<IAcisSMEParameterRef> Parameters =>
            new[]
            {
                ParamRefFromParam.Get<RegionParameter>(),
                ParamRefFromParam.Get<SubscriptionIdParameter>(),
                ParamRefFromParam.Get<ResourceGroupNameParameter>(),
                ParamRefFromParam.Get<WorkspaceNameParameter>(),
                ParamRefFromParam.Get<SourceRunIdParameter>(),
                ParamRefFromParam.Get<DestinationRunIdParameter>(),
                ParamRefFromParam.Get<NodeHashSourceParameter>()
            };

        public IAcisSMEOperationResponse CopyDMTCacheResult(
            string region,
            string subscriptionId,
            string resourceGroupName,
            string workspaceName,
            string sourceRunId,
            string destinationRunId,
            string source,
            IAcisServiceManagementExtension extension,
            IAcisSMEOperationProgressUpdater updater,
            IAcisSMEEndpoint endpoint)
        {
            string baseUrl = Utils.GenerateTargetUrl(region, endpoint, _serviceName);

            // reference id may contain slash, we should do uri escape
            var url = string.Format(_urlTemplate, baseUrl, subscriptionId, resourceGroupName, workspaceName, sourceRunId, destinationRunId, source);
            return GenevaOperationHelpers.RunGenevaOperation(region, url, OperationName, extension, updater, HttpMethod.Post);
        }
    }
}
