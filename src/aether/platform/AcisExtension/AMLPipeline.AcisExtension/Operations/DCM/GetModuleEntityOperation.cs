﻿using System.Collections.Generic;
using System.Net.Http;
using Microsoft.MachineLearning.Pipeline.AcisExtension.Common;
using Microsoft.MachineLearning.Pipeline.AcisExtension.OperationGroups;
using Microsoft.MachineLearning.Pipeline.AcisExtension.Operations.GenevaOperationHelper;
using Microsoft.MachineLearning.Pipeline.AcisExtension.Parameters;
using Microsoft.WindowsAzure.Wapd.Acis.Contracts;
using Microsoft.WindowsAzure.Wapd.Acis.Contracts.Models;

namespace Microsoft.MachineLearning.Pipeline.AcisExtension.Operations.GetModuleEntity
{
    internal class GetModuleEntityOperation : AcisSMEOperation
    {
        private const string _serviceName = "DiagnosticService";

        private const string _urlTemplate = "{0}/diagnostic/v1.0/subscription/{1}/resourcegroup/{2}/workspace/{3}/module/{4}/metastore/module";

        public override string HelpText => "Get module entity.";

        public override IAcisSMEOperationGroup OperationGroup => DCMOperationGroup.Instance;

        public override string OperationName => "Get Module Entity";

        public override DataAccessLevel SystemMetadata { get; } = DataAccessLevel.ReadOnly;

        // Module entity contains user object id and user name.
        public override DataClassificationLevel DataClassificationLevel => DataClassificationLevel.EUPI | DataClassificationLevel.EUII;

        public override IEnumerable<AcisUserClaim> ClaimsRequired => new[]
        {
            AcisSMESecurityGroup.ClientCustomerServiceViewer(Consts.SCOPE_AMLPIPELINE),
            AcisSMESecurityGroup.ClientPlatformServiceOperator(Consts.SCOPE_AMLPIPELINE)
        };
        public override IEnumerable<IAcisSMEParameterRef> Parameters =>
            new[]
            {
                ParamRefFromParam.Get<RegionParameter>(),
                ParamRefFromParam.Get<SubscriptionIdParameter>(),
                ParamRefFromParam.Get<ResourceGroupNameParameter>(),
                ParamRefFromParam.Get<WorkspaceNameParameter>(),
                ParamRefFromParam.Get<ModuleIdParameter>()
            };

        public IAcisSMEOperationResponse GetModuleEntity(
            string region,
            string subscriptionId,
            string resourceGroupName,
            string workspaceName,
            string moduleId,
            IAcisServiceManagementExtension extension,
            IAcisSMEOperationProgressUpdater updater,
            IAcisSMEEndpoint endpoint)
        {
            string baseUrl = Utils.GenerateTargetUrl(region, endpoint, _serviceName);
            var url = string.Format(_urlTemplate, baseUrl, subscriptionId, resourceGroupName, workspaceName, moduleId);
            return GenevaOperationHelpers.RunGenevaOperation(region, url, OperationName, extension, updater, HttpMethod.Get);
        }
    }
}
