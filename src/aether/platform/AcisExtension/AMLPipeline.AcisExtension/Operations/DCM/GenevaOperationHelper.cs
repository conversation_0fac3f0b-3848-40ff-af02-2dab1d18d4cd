﻿using System;
using System.Net;
using System.Net.Http;
using Microsoft.MachineLearning.Pipeline.AcisExtension.Client;
using Microsoft.WindowsAzure.Wapd.Acis.Contracts;

namespace Microsoft.MachineLearning.Pipeline.AcisExtension.Operations.GenevaOperationHelper
{
    public static class GenevaOperationHelpers
    {
        public static IAcisSMEOperationResponse RunGenevaOperation(string region, string url, string action, IAcisServiceManagementExtension extension, IAcisSMEOperationProgressUpdater updater, HttpMethod method, StringContent bodyContent = null)
        {
            try
            {
                updater.WriteLine($"Start to call [{action}] action to region [{region}]");
                updater.WriteLine($"Uri: {url}");
                using (var client = new ViennaClient(extension, updater))
                {
                    HttpStatusCode code = default;
                    string message = default;
                    if(method == HttpMethod.Post)
                    {
                        (code, message) = client.PostAsync(url, bodyContent).Result;
                    }
                    else if(method == HttpMethod.Get)
                    {
                        (code, message) = client.GetAsync(url).Result;
                    }
                    else if (method == HttpMethod.Delete)
                    {
                        (code, message) = client.DeleteAsync(url).Result;
                    }
                    else
                    {
                       throw new ArgumentException($"Argument {nameof(method)} is {method}. Expected {HttpMethod.Post} or {HttpMethod.Get} or {HttpMethod.Delete}.");
                    }

                    updater.WriteLine($"Status code [{code}]. Message:");
                    updater.WriteLine($"{message}");
                    if (code.GetHashCode() >= 400)
                    {
                        return AcisSMEOperationResponseExtensions.SpecificErrorResponse("Failed");
                    }

                    return AcisSMEOperationResponseExtensions.StandardSuccessResponse("Finished");
                }
            }
            catch (Exception ex)
            {
                updater.WriteLine($"Got exception [{ex.Message}], [{ex.StackTrace}]");
                return AcisSMEOperationResponseExtensions.StandardErrorResponse(ex);
            }
        }
    }
}
