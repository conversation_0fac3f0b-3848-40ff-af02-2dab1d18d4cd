﻿using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using Microsoft.MachineLearning.Pipeline.AcisExtension.Common;
using Microsoft.MachineLearning.Pipeline.AcisExtension.OperationGroups;
using Microsoft.MachineLearning.Pipeline.AcisExtension.Operations.BatchCancelPipelineRun;
using Microsoft.MachineLearning.Pipeline.AcisExtension.Operations.GenevaOperationHelper;
using Microsoft.MachineLearning.Pipeline.AcisExtension.Parameters;
using Microsoft.WindowsAzure.Wapd.Acis.Contracts;
using Microsoft.WindowsAzure.Wapd.Acis.Contracts.Models;
using Newtonsoft.Json;

namespace Microsoft.MachineLearning.Pipeline.AcisExtension.Operations.BatchUpdateExperimentEntityStatusToCanceled
{
    internal class BatchUpdateExperimentEntityStatusToCanceledOperation : AcisSMEOperation
    {
        private const string _serviceName = "DiagnosticService";

        private const string _urlTemplate = "{0}/diagnostic/v1.0/subscriptions/{1}/resourceGroups/{2}/workspaces/{3}/cancelExperiment";

        public override string HelpText => @$"
Update one or multiple experiment entity status to canceled. 

[WARNING] First use {nameof(BatchCancelPipelineRunsOperation)} to cancel pipeline run.

If it does not work, then use this operation. It will terminate the processing of specified pipeline runs in orchestration service,
without updating pipeline run and step run status properly.

Separate pipeline run ids with comma.";

        public override IAcisSMEOperationGroup OperationGroup => DCMOperationGroup.Instance;

        public override string OperationName => "Batch Update Experiment Entities Status to Canceled";

        public override DataAccessLevel SystemMetadata { get; } = DataAccessLevel.ReadWrite;

        public override DataClassificationLevel DataClassificationLevel => DataClassificationLevel.NoCustomerContent;

        public override IEnumerable<AcisUserClaim> ClaimsRequired => new[]
        {
            AcisSMESecurityGroup.ClientPlatformServiceOperator(Consts.SCOPE_AMLPIPELINE)
        };

        public override IEnumerable<IAcisSMEParameterRef> Parameters =>
            new[]
            {
                ParamRefFromParam.Get<RegionParameter>(),
                ParamRefFromParam.Get<SubscriptionIdParameter>(),
                ParamRefFromParam.Get<ResourceGroupNameParameter>(),
                ParamRefFromParam.Get<WorkspaceNameParameter>(),
                ParamRefFromParam.Get<PipelineRunIdGroupParameter>(),
            };

        public IAcisSMEOperationResponse BatchUpdateExperimentEntityStatusToCanceled(
            string region,
            string subscriptionId,
            string resourceGroupName,
            string workspaceName,
            string pipelineRunIds, 
            IAcisServiceManagementExtension extension,
            IAcisSMEOperationProgressUpdater updater,
            IAcisSMEEndpoint endpoint)
        {
            string baseUrl = Utils.GenerateTargetUrl(region, endpoint, _serviceName);
            var url = string.Format(_urlTemplate, baseUrl, subscriptionId, resourceGroupName, workspaceName);
            List<string> content = new List<string>(pipelineRunIds.Split(','));
            var body = new StringContent(JsonConvert.SerializeObject(content.Select(id => id.Trim())), Encoding.UTF8, "application/json");
            return GenevaOperationHelpers.RunGenevaOperation(region, url, OperationName, extension, updater, HttpMethod.Post, body);
        }
    }
} 
