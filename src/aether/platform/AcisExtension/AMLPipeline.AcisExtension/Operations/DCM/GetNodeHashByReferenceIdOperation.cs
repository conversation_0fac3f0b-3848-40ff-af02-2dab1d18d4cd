﻿using System;
using System.Collections.Generic;
using System.Net.Http;
using Microsoft.MachineLearning.Pipeline.AcisExtension.Common;
using Microsoft.MachineLearning.Pipeline.AcisExtension.OperationGroups;
using Microsoft.MachineLearning.Pipeline.AcisExtension.Operations.GenevaOperationHelper;
using Microsoft.MachineLearning.Pipeline.AcisExtension.Parameters;
using Microsoft.WindowsAzure.Wapd.Acis.Contracts;
using Microsoft.WindowsAzure.Wapd.Acis.Contracts.Models;

namespace Microsoft.MachineLearning.Pipeline.AcisExtension.Operations.GetNodeHashByReferenceId
{
    internal class GetNodeHashByReferenceIdOperation: AcisSMEOperation
    {
        private const string _serviceName = "DiagnosticService";

        private const string _urlTemplate = "{0}/diagnostic/v1.0/subscription/{1}/resourcegroup/{2}/workspace/{3}/reference/{4}/nodehash?source={5}";

        public override string HelpText => @"
Query the reuse hash source string by reference id of job or cache meta.

When unexpected rerun happen, you can query out the hash of rerun job and compare it with the expecting origin job.

You should first confirm the rerun job:
- is_deterministic is true
- enforce_rerun is null or false
- regenerate_output is false

Otherwise the job' rerun is expected behavior.
";

        public override IAcisSMEOperationGroup OperationGroup => DCMOperationGroup.Instance;

        public override string OperationName => "Get Node Hash by Reference Id";

        public override DataAccessLevel SystemMetadata { get; } = DataAccessLevel.ReadOnly;

        public override DataClassificationLevel DataClassificationLevel => DataClassificationLevel.NoCustomerContent;

        public override IEnumerable<AcisUserClaim> ClaimsRequired => new[]
        {
            AcisSMESecurityGroup.ClientPlatformServiceOperator(Consts.SCOPE_AMLPIPELINE)
        };

        public override IEnumerable<IAcisSMEParameterRef> Parameters =>
            new[]
            {
                ParamRefFromParam.Get<RegionParameter>(),
                ParamRefFromParam.Get<SubscriptionIdParameter>(),
                ParamRefFromParam.Get<ResourceGroupNameParameter>(),
                ParamRefFromParam.Get<WorkspaceNameParameter>(),
                ParamRefFromParam.Get<ReferenceIdParameter>(),
                ParamRefFromParam.Get<NodeHashSourceParameter>()
            };

        public IAcisSMEOperationResponse GetNodeHashByReferenceId(
            string region,
            string subscriptionId,
            string resourceGroupName,
            string workspaceName,
            string referenceId,
            string source,
            IAcisServiceManagementExtension extension,
            IAcisSMEOperationProgressUpdater updater,
            IAcisSMEEndpoint endpoint)
        {
            string baseUrl = Utils.GenerateTargetUrl(region, endpoint, _serviceName);

            // reference id may contain slash, we should do uri escape
            var url = string.Format(_urlTemplate, baseUrl, subscriptionId, resourceGroupName, workspaceName, Uri.EscapeDataString(referenceId), source);
            return GenevaOperationHelpers.RunGenevaOperation(region, url, OperationName, extension, updater, HttpMethod.Get);
        }
    }
}
