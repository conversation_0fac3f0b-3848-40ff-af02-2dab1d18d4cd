﻿using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using Microsoft.MachineLearning.Pipeline.AcisExtension.Common;
using Microsoft.MachineLearning.Pipeline.AcisExtension.OperationGroups;
using Microsoft.MachineLearning.Pipeline.AcisExtension.Operations.GenevaOperationHelper;
using Microsoft.MachineLearning.Pipeline.AcisExtension.Parameters;
using Microsoft.WindowsAzure.Wapd.Acis.Contracts;
using Microsoft.WindowsAzure.Wapd.Acis.Contracts.Models;
using Newtonsoft.Json;

namespace Microsoft.MachineLearning.Pipeline.AcisExtension.Operations.BatchCancelPipelineRun
{
    internal class BatchCancelPipelineRunsOperation : AcisSMEOperation
    {
        private const string _serviceName = "DiagnosticService";

        private const string _urlTemplate = "{0}/diagnostic/v1.0/subscriptions/{1}/resourceGroups/{2}/workspaces/{3}/pipelines/batchCancel?forceCancel={4}&cancelFinalPhase={5}";

        public override string HelpText => "Cancel one or multiple pipeline runs. Separate pipeline run ids with comma.";

        public override IAcisSMEOperationGroup OperationGroup => DCMOperationGroup.Instance;

        public override string OperationName => "Batch cancel pipeline runs";

        public override DataAccessLevel SystemMetadata { get; } = DataAccessLevel.ReadWrite;

        public override DataClassificationLevel DataClassificationLevel => DataClassificationLevel.NoCustomerContent;

        public override IEnumerable<AcisUserClaim> ClaimsRequired => new[]
        {
            AcisSMESecurityGroup.ClientPlatformServiceOperator(Consts.SCOPE_AMLPIPELINE)
        };

        public override IEnumerable<IAcisSMEParameterRef> Parameters =>
            new[]
            {
                ParamRefFromParam.Get<RegionParameter>(),
                ParamRefFromParam.Get<SubscriptionIdParameter>(),
                ParamRefFromParam.Get<ResourceGroupNameParameter>(),
                ParamRefFromParam.Get<WorkspaceNameParameter>(),
                ParamRefFromParam.Get<PipelineRunIdGroupParameter>(),
                ParamRefFromParam.Get<ForceCancelParameter>(),
                ParamRefFromParam.Get<CancelFinalPhaseParameter>(),
            };

        public IAcisSMEOperationResponse BatchCancelPipelineRuns(
            string region,
            string subscriptionId,
            string resourceGroupName,
            string workspaceName,
            string pipelineRunIds,
            string forceCancel,
            string cancelFinalPhase,
            IAcisServiceManagementExtension extension,
            IAcisSMEOperationProgressUpdater updater,
            IAcisSMEEndpoint endpoint)
        {
            string baseUrl = Utils.GenerateTargetUrl(region, endpoint, _serviceName);
            var url = string.Format(_urlTemplate, baseUrl, subscriptionId, resourceGroupName, workspaceName, forceCancel, cancelFinalPhase);
            List<string> content = new List<string>(pipelineRunIds.Split(','));
            var body = new StringContent(JsonConvert.SerializeObject(content.Select(id => id.Trim())), Encoding.UTF8, "application/json");
            return GenevaOperationHelpers.RunGenevaOperation(region, url, OperationName, extension, updater, HttpMethod.Post, body);
        }
    }
}
