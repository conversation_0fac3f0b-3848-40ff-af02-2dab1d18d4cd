﻿<?xml version="1.0" encoding="utf-8"?>
<AcisSMEConfiguration>
  <SMEEndpoints>
    <Endpoint>
      <Name>AzureML Pipeline</Name>
      <EndpointType>AzureML Pipeline</EndpointType>
      <Url></Url>
      <SMEConfig>
        <ConfigElement>
          <Key>Region</Key>
          <Value>eastus2euap|||westcentralus|||eastus|||eastus2|||westeurope|||northeurope|||southeastasia|||australiaeast|||centralus|||westus2|||northcentralus|||southcentralus|||westus|||canadacentral|||eastasia|||uksouth|||japaneast|||centralindia|||brazilsouth|||francecentral|||koreacentral|||japanwest|||jioindiawest|||westus3|||germanywestcentral|||switzerlandnorth|||uaenorth|||southafricanorth|||norwayeast|||canadaeast|||swedencentral|||ukwest|||australiasoutheast|||qatarcentral|||polandcentral|||southindia</Value>
        </ConfigElement>
        <ConfigElement>
          <Key>Env</Key>
          <Value>prod</Value>
        </ConfigElement>
        <ConfigElement>
          <Key>ServiceDomain</Key>
          <Value>api.azureml.ms</Value>
        </ConfigElement>
        <ConfigElement>
          <Key>QuotaType</Key>
          <Value>RunningPipelineRun|||RunningPipelineRunSoft</Value>
        </ConfigElement>
        <ConfigElement>
          <Key>ForceCancel</Key>
          <Value>false|||true</Value>
        </ConfigElement>
        <ConfigElement>
          <Key>CancelFinalPhase</Key>
          <Value>false|||true</Value>
        </ConfigElement>
        <ConfigElement>
          <Key>NodeHashSource</Key>
          <Value>DCM|||Cloudlet|||APCloud</Value>
        </ConfigElement>
      </SMEConfig>
      <TargetRegion>Global</TargetRegion>
    </Endpoint>
  </SMEEndpoints>
  <ExtensionCertificates>
    <ExtensionCertificate>
      <Type>KeyVault</Type>
      <Identifier>GenevaActionsClientCertificate</Identifier>
      <FilePath>https://vienna-global.vault.azure.net/secrets/certificate--global-azureml-internal-ms--pfx</FilePath>
      <InstallTo>
        <StoreLocation>CurrentUser</StoreLocation>
        <StoreName>My</StoreName>
      </InstallTo>
    </ExtensionCertificate>
  </ExtensionCertificates>
  <ContactInfo>
    <ContactEmailAddress><EMAIL></ContactEmailAddress>
    <IcmService>Project Vienna Services</IcmService>
    <IcmTeam>AEther</IcmTeam>
    <ServiceTreeServiceId>2759ddd7-19db-4c59-9824-c2e7a4c6c2a8</ServiceTreeServiceId>
  </ContactInfo>
  <SourceCodeUrl>https://dev.azure.com/msdata/Vienna/_git/vienna?path=/src/aether/platform/AcisExtension/AMLPipeline.AcisExtension</SourceCodeUrl>
</AcisSMEConfiguration>