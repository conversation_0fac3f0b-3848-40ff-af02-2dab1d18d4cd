﻿using System.Linq;
using Microsoft.WindowsAzure.Wapd.Acis.Contracts;

namespace Microsoft.MachineLearning.Pipeline.AcisExtension.Extensions
{
    public class PipelineAcisExtension : AcisServiceManagementExtension
    {
        #region Required for ACIS SME infrastructure

        /// <summary>
        /// Name uniquely identifies the Extension.
        /// </summary>
        public override string ServiceName => "AzureML Pipeline";

        /// <summary>
        /// Extension Version
        /// </summary>
        public override string ExtensionVersion => "1.0";

        public override bool OnLoad()
        {
            Logger.LogVerbose($"Just loaded {this.ServiceName}");
            return true;
        }

        public override bool OnEndpointCreate(IAcisSMEEndpoint endpoint)
        {
            // Report on the core information for the extension
            Logger.LogVerbose(string.Format("Extension {0} creating endpoint {1}", endpoint.ContainingExtension.ServiceName, endpoint.Name));
            Logger.LogVerbose(string.Format(".. claims required are {0}", string.Join("|", endpoint.ClaimsRequired.Select(claim => claim.Name))));
            Logger.LogVerbose(string.Format(".. operations provided are {0}", string.Join("|", endpoint.Operations.Select(op => op.ToString()))));

            // Report on the configuration contained in the endpoint - the Geneva Actions infrastructure doesn't rely on any of this
            //  configuration it's purely for the extension's use
            Logger.LogVerbose(string.Format(".. configuration defines environment as {0}", endpoint.Configuration.GetConfigurationValue("env")));

            return true;
        }

        #endregion
    }
}
