﻿using NUnit.Framework;
using System;
using System.Diagnostics;

namespace BBAetherLibrary.Common
{
    public class TestEnvironment
    {
        private const string AetherEndpointEnvVariable = "AETHER_APIENDPOINT";
        private const string DefaultEnvName = "int";

        // If you want to submit tests to aevaonebox or a locally started service (i.e. ESCloud, MetaStore),
        // specify the local service port here.
        //
        // - Test in aevaonebox: set LocalHostPort to your own ae3pservice port number.
        // - Test local services: set LocalHostPort to the local services port number, usually 8080.
        private static string LocalHostPort = "";

        // If you want to submit tests to an int-transient environment created by a PR,
        // specify the pull request number here.
        private static string PullRequestNumber = "";

        public static string GetEnvName()
        {
            var envName = TestContext.Parameters.Get("EnvName");
            if (string.IsNullOrEmpty(envName))
            {
                return DefaultEnvName;
            }

            return envName.ToLower();
        }

        public static bool IsRunningInInt()
        {
            return GetEnvName() == "int";
        }

        public static bool IsRunningInRunner()
        {
            return Environment.GetEnvironmentVariable("TestHarness") == "Provisioned";
        }

        public static bool IsRunningInLocal()
        {
            var testHarness = Environment.GetEnvironmentVariable("TestHarness");
            foreach (string supportedHarness in new[] { "CloudTest", "VSTS", "Provisioned" })
            {
                if (string.Compare(testHarness, supportedHarness, StringComparison.InvariantCultureIgnoreCase) == 0)
                {
                    return false;
                }
            }

            return true;
        }

        public static string GetFromConfig(string name)
        {
            var value = TestContext.Parameters.Get(name);
            if (string.IsNullOrEmpty(value))
            {
                throw new Exception($"Entry '{name}' not found in runsettings file.");
            }

            return value;
        }

        /// <summary>
        /// Returns the endpoint for the external AzureML service that is not deployed in the transient environment.
        ///
        /// Usually it should return the int environment endpoint: https://int.api.azureml-test.ms
        /// </summary>

        public static string GetAzureMLServiceEndpoint()
        {
            var endpoint = $"https://{GetEnvName()}.api.azureml-test.ms";
            Trace.WriteLine($"AzureML service API endpoint from runsettings: {endpoint}");
            return endpoint;
        }

        /// <summary>
        /// Returns the endpoint for the Aether API.
        ///
        /// When running in local, it returns the int environment https://int.api.azureml-test.ms
        /// When running in CI, it is overridden by the environment variable and the values turns out to be
        /// something like https://pull-1027306.transient.int.api.azureml-test.ms
        /// </summary>
        public static string GetAetherApiEndpoint()
        {
            var aetherEndpointFromEnv = Environment.GetEnvironmentVariable(AetherEndpointEnvVariable);
            if (!string.IsNullOrEmpty(aetherEndpointFromEnv))
            {
                Trace.WriteLine($"Aether API Endpoint from environemnt variable: {aetherEndpointFromEnv}");
                return aetherEndpointFromEnv;
            }

            if (IsRunningInLocal())
            {
                if (!string.IsNullOrEmpty(LocalHostPort))
                {
                    return $"http://localhost:{LocalHostPort}";
                }
                else if (!string.IsNullOrEmpty(PullRequestNumber))
                {
                    return $"https://pull-{PullRequestNumber}.transient.int.api.azureml-test.ms";
                }
                return GetAzureMLServiceEndpoint();
            }

            throw new Exception("AEther API endpoint is not set. Please select a .runsettings file to start.");
        }
    }
}
