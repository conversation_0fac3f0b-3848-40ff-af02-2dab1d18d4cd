﻿// <copyright file="SubGraph.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Runtime.Serialization;
using System.Threading.Tasks;
using Aether.ContractProcessing;
using Microsoft.Aether.AEVA.DataContracts;

namespace Microsoft.Aether.BlueBox.Library
{
    [DataContract]
    internal class SubGraph : SubGraphEntity, ISubGraph
    {
        public string ResourceTypeName
        {
            get { return "SubGraph"; }
        }

        public IExecutionInterface Interface
        {
            get { return _graphInterface; }
        }

        public Task SaveMetadataAsync()
        {
            // TODO: Update subgraph
            throw new NotImplementedException();
            /*
            SubGraphEntity updated = await _aetherEnvironment.ServiceWrapper.UpdateSubGraphFieldsAsync(this,
                ChangeDetector.ListChangedProperties<SubGraphEntity>(_originalEntity, this)).ConfigureAwait(false);
            Cloner.WriteInto<SubGraphEntity>(updated, this);
            _originalEntity = Cloner.DeepCopy(updated);
            */
        }

        public void SaveMetadata()
        {
            SaveMetadataAsync().Wait();
        }

        public async Task<IGraph> GetGraphAsync(bool enforcePortTypes = false)
        {
            Graph result = new Graph(id: null, enforcePortTypes: enforcePortTypes);

            await GraphConverter.CopyToGraphAsync(
                graphToBuild: result,
                graphInterface: _graphInterface,
                graphEntity: await _aetherEnvironment.ServiceWrapper.GetGraphAsync(GraphId).ConfigureAwait(false),
                aetherEnvironment: _aetherEnvironment,
                storage: _aetherEnvironment.Storage,
                dataTypeCollection: await _aetherEnvironment.GetDataTypeCollectionAsync().ConfigureAwait(false)).ConfigureAwait(false);

            return result;
        }

        public IGraph GetGraph()
        {
            return GetGraphAsync().Result;
        }

        // True if this subgraph is published as a module entity
        public bool IsModule { get; set; }

        // TODO: Visual Graph support
        /*
        public async Task<IVisualGraph> GetVisualGraphAsync()
        {
            return await _aetherEnvironment.GetVisualGraphAsync(GraphId).ConfigureAwait(false);
        }

        public IVisualGraph GetVisualGraph()
        {
            return GetVisualGraphAsync().Result;
        }
        */

        // TODO:  Family support
        /*
        public async Task<ISubGraph> GetLatestVersionAsync()
        {
            SubGraphEntity data = await _aetherEnvironment.ServiceWrapper.GetLatestVersionOfSubGraphAsync(FamilyId).ConfigureAwait(false);
            return BuildSubGraph(new ConnectableSubGraph(data, await _aetherEnvironment.ServiceWrapper
                .GetGraphInterfaceAsync(data.GraphId).ConfigureAwait(false)), _aetherEnvironment,
                await _aetherEnvironment.GetDataTypeCollectionAsync().ConfigureAwait(false));
        }

        public ISubGraph GetLatestVersion()
        {
            return GetLatestVersionAsync().Result;
        }

        public async Task<IEnumerable<ISubGraph>> GetRelatedSubGraphsAsync()
        {
            return (await _aetherEnvironment.GetAllSubGraphsAsync(await _aetherEnvironment.ServiceWrapper
                .GetSubGraphFamilyAsync(FamilyId).ConfigureAwait(false)).ConfigureAwait(false))
                .Values.OrderByDescending(subGraph => subGraph.SequenceNumberInFamily);
        }

        public IEnumerable<ISubGraph> GetRelatedSubGraphs()
        {
            return GetRelatedSubGraphsAsync().Result;
        }
        */

        internal SubGraph(IExecutionInterface graphInterface, AetherEnvironment aetherEnvironment, SubGraphEntity originalEntity)
        {
            _graphInterface = graphInterface;
            _aetherEnvironment = aetherEnvironment;
            _originalEntity = originalEntity;
        }

        internal static SubGraph BuildSubGraph(ConnectableSubGraph subGraph,
            AetherEnvironment aetherEnvironment,
            DataTypeCollection dataTypes,
            bool isModule = false)
        {
            SubGraph aetherSubGraph = new SubGraph(ExecutionInterface.BuildFromContracts(subGraph.GraphInterface, dataTypes),
                aetherEnvironment, Cloner.DeepCopy(subGraph.Metadata));
            Cloner.WriteInto<SubGraphEntity>(subGraph.Metadata, aetherSubGraph);
            aetherSubGraph.IsModule = isModule;
            return aetherSubGraph;
        }

        private SubGraphEntity _originalEntity;

        private readonly IExecutionInterface _graphInterface;
        private readonly AetherEnvironment _aetherEnvironment;
    }
}
