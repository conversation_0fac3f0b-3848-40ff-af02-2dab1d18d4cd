﻿// <copyright file="ModuleNode.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.Aether.AEVA.DataContracts;

namespace Microsoft.Aether.BlueBox.Library
{
    internal class ModuleNode : ExecutableNode, IModuleNode
    {
        public ModuleNode(
            string id,
            IAetherModule module,
            IEnumerable<InputSetting> moduleInputSettings,
            IEnumerable<OutputSetting> moduleOutputSettings,
            CloudSettings cloudSettings,
            bool useDefaultCompute = false,
            bool regenerateOutput = false,
            string runConfig = null)
            : base(
                BuildParameterDictionary(module.Interface.Parameters),
                BuildParameterDictionary(module.Interface.MetadataParameters),
                BuildInputSettingDictionary(moduleInputSettings),
                BuildOutputSettingDictionary(moduleOutputSettings))
        {
            Id = id;
            Module = module;
            Name = Module.Name;
            CloudSettings = cloudSettings;
            UseGraphDefaultCompute = useDefaultCompute;
            RegenerateOutput = regenerateOutput;
            Runconfig = runConfig;

            InitializePortDictionaries(module.Interface);
        }

        public override IResource Resource => Module;

        #region IModuleNode implementation

        public IAetherModule Module { get; }

        private readonly Dictionary<string, ControlInput> _controlInputs = new Dictionary<string, ControlInput>();

        public void AddControlInput(ControlInput controlInput)
        {
            if (Inputs.ContainsKey(controlInput.Name))
            {
                throw new ArgumentException($"ModuleNode already has a data input with name {controlInput.Name}.");
            }

            if (_controlInputs.ContainsKey(controlInput.Name))
            {
                throw new ArgumentException($"ModuleNode already has a control input with name {controlInput.Name}.");
            }

            _controlInputs[controlInput.Name] = controlInput;
        }

        public void RemoveControlInput(string controlInputName)
        {
            _controlInputs.Remove(controlInputName);
        }

        public IReadOnlyDictionary<string, ControlInput> GetControlInputs()
        {
            return _controlInputs;
        }

        public string Runconfig { get; set; }

        public override bool CheckParametersValidity(out string details)
        {
            if (base.CheckParametersValidity(out details))
            {
                foreach (var parameter in Parameters.Values.Where(par => par.InputPortName == null))
                {
                    if (parameter.HasExplicitValue &&
                        !parameter.CheckIsValidValue(parameter.Value, out string parameterSpecificDetails))
                    {
                        details = $"Parameter \"{parameter.Name}\" has an invalid value because {parameterSpecificDetails}";
                        return false;
                    }
                }

                details = string.Empty;
                return true;
            }

            return false;
        }

        public override bool CheckMetadataValidity(out string details)
        {
            if (base.CheckMetadataValidity(out details))
            {
                foreach (var metadata in MetadataParameters.Values)
                {
                    if (metadata.HasExplicitValue &&
                        !metadata.CheckIsValidValue(metadata.Value, out string metadataSpecificDetails))
                    {
                        details = $"Metadata Parameter \"{metadata.Name}\" has an invalid value because {metadataSpecificDetails}";
                        return false;
                    }
                }

                details = string.Empty;
                return true;
            }

            return false;
        }

        #endregion

        //-----------------------------------------------------------------------------------------
    }
}
