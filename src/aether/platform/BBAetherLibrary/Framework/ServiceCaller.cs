﻿// <copyright file="ServiceCaller.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using Microsoft.Aether.AEVA.DataContracts;
using TaskStatus = Microsoft.Aether.AEVA.DataContracts.TaskStatus;

namespace Microsoft.Aether.BlueBox.Library
{
    internal class ServiceCaller : IServiceCaller
    {
        internal ServiceCaller(HttpClient workspaceHttpClient, HttpClient repositoryHttpClient = null)
        {
            _workspaceHttpClient = workspaceHttpClient;
            _repositoryHttpClient = repositoryHttpClient;
            Signing = new ResourceRelayServiceCaller(_workspaceHttpClient);
        }

        #region Data types

        public async Task<DataTypeEntity> CreateDataTypeAsync(DataTypeCreationInfo creationInfo)
        {
            string path = _workspaceHttpClient.BaseAddress + "DataTypes";
            return await _workspaceHttpClient.PostEntityAsync<DataTypeCreationInfo, DataTypeEntity>(
                    path,
                    creationInfo).ConfigureAwait(false);
        }

        public async Task<DataTypeEntity> UpdateDataTypeAsync(DataTypeEntity updated)
        {
            return await _workspaceHttpClient.PutEntityAsync(_workspaceHttpClient.BaseAddress +
                "DataTypes/" + updated.Id, updated).ConfigureAwait(false);
        }

        public async Task<IList<DataTypeEntity>> GetAllDataTypesAsync()
        {
            string path = _workspaceHttpClient.BaseAddress + "DataTypes";
            return await _workspaceHttpClient.GetEntityAsync<IList<DataTypeEntity>>(path).ConfigureAwait(false);
        }

        #endregion

        #region Modules

        public async Task<ModuleEntity> CreateModuleAsync(ModuleCreationInfo creationInfo)
        {
            string path = _workspaceHttpClient.BaseAddress + "Modules";
            return await _workspaceHttpClient.PostEntityAsync<ModuleCreationInfo, ModuleEntity>(path, creationInfo).ConfigureAwait(false);
        }

        public async Task<ModuleEntity> UpdateModuleAsync(string id, ModuleEntity updated)
        {
            string path = _workspaceHttpClient.BaseAddress + "Modules/" + id;
            return await _workspaceHttpClient.PutEntityAsync(path, updated).ConfigureAwait(false);
        }

        public async Task<Module> GetModuleAsync(string id)
        {
            return await GetModuleAsync(id, isTry: false).ConfigureAwait(false);
        }

        public async Task<Module> TryGetModuleAsync(string id)
        {
            return await GetModuleAsync(id, isTry: true).ConfigureAwait(false);
        }

        // "V2" version includes a serialized runconfig populated for older modules
        public async Task<Module> GetModuleV2Async(string id)
        {
            return await GetModuleV2Async(id, isTry: false).ConfigureAwait(false);
        }

        // "V2" version includes a serialized runconfig populated for older modules
        public async Task<Module> TryGetModuleV2Async(string id)
        {
            return await GetModuleV2Async(id, isTry: true).ConfigureAwait(false);
        }

        private async Task<Module> GetModuleAsync(string id, bool isTry)
        {
            string path = _workspaceHttpClient.BaseAddress + "Modules/" + id;
            return await _workspaceHttpClient.GetEntityAsync<Module>(path, isTry).ConfigureAwait(false);
        }

        // "V2" version includes a serialized ruconfig populated for older modules
        private async Task<Module> GetModuleV2Async(string id, bool isTry)
        {
            string path = _workspaceHttpClient.BaseAddress + "Modules/V2/" + id;
            return await _workspaceHttpClient.GetEntityAsync<Module>(path, isTry).ConfigureAwait(false);
        }

        public async Task<IList<Module>> BulkGetModulesAsync(string[] ids)
        {
            string path = _workspaceHttpClient.BaseAddress + "BulkGetModules";
            return await _workspaceHttpClient.PostEntityAsync<string[], IList<Module>>(path, ids).ConfigureAwait(false);
        }

        // "V2" version includes a serialized ruconfig populated for older modules
        public async Task<IList<Module>> BulkGetModulesV2Async(string[] ids)
        {
            string path = _workspaceHttpClient.BaseAddress + "BulkGetModules/V2";
            return await _workspaceHttpClient.PostEntityAsync<string[], IList<Module>>(path, ids).ConfigureAwait(false);
        }

        public async Task<Module> GetModuleByHashAsync(string idHash)
        {
            return await GetModuleByHashAsync(idHash, isTry: false).ConfigureAwait(false);
        }

        public async Task<Module> TryGetModuleByHashAsync(string idHash)
        {
            return await GetModuleByHashAsync(idHash, isTry: true).ConfigureAwait(false);
        }

        private async Task<Module> GetModuleByHashAsync(string idHash, bool isTry)
        {
            string path = _workspaceHttpClient.BaseAddress + $"Modules/Hash/{idHash}";
            return await _workspaceHttpClient.GetEntityAsync<Module>(path, isTry).ConfigureAwait(false);
        }

        #endregion

        #region Data sources

        public async Task<DataSourceEntity> CreateDataSourceAsync(DataSourceCreationInfo creationInfo)
        {
            string path = _workspaceHttpClient.BaseAddress + "DataSources";
            return await _workspaceHttpClient.PostEntityAsync<DataSourceCreationInfo, DataSourceEntity>(path, creationInfo).ConfigureAwait(false);
        }

        public async Task<DataSourceEntity> UpdateDataSourceAsync(string dataSourceId, DataSourceEntity updated)
        {
            string path = _workspaceHttpClient.BaseAddress + "DataSources/" + dataSourceId;
            return await _workspaceHttpClient.PutEntityAsync(path, updated).ConfigureAwait(false);
        }

        public async Task<DataSourceEntity> GetDataSourceAsync(string id)
        {
            return await GetDataSourceAsync(id, isTry: false).ConfigureAwait(false);
        }

        public async Task<DataSourceEntity> TryGetDataSourceAsync(string id)
        {
            return await GetDataSourceAsync(id, isTry: true).ConfigureAwait(false);
        }

        public async Task<DataSourceEntity> GetDataSourceByHashAsync(string idHash)
        {
            return await GetDataSourceByHashAsync(idHash, isTry: false).ConfigureAwait(false);
        }

        public async Task<DataSourceEntity> TryGetDataSourceByHashAsync(string idHash)
        {
            return await GetDataSourceByHashAsync(idHash, isTry: true).ConfigureAwait(false);
        }

        private async Task<DataSourceEntity> GetDataSourceAsync(string id, bool isTry)
        {
            string path = _workspaceHttpClient.BaseAddress + "DataSources/" + id;
            return await _workspaceHttpClient.GetEntityAsync<DataSourceEntity>(path, isTry).ConfigureAwait(false);
        }

        public async Task<IList<DataSourceEntity>> BulkGetDataSourcesAsync(string[] ids)
        {
            string path = _workspaceHttpClient.BaseAddress + "BulkGetDatasources";
            return await _workspaceHttpClient.PostEntityAsync<string[], IList<DataSourceEntity>>(path, ids).ConfigureAwait(false);
        }

        private async Task<DataSourceEntity> GetDataSourceByHashAsync(string idHash, bool isTry)
        {
            string path = _workspaceHttpClient.BaseAddress + $"DataSources/Hash/{idHash}";
            return await _workspaceHttpClient.GetEntityAsync<DataSourceEntity>(path, isTry).ConfigureAwait(false);
        }
        #endregion

        #region PipelineRuns

        public async Task<PipelineRunEntity> CreateUnsubmittedPipelineRunAsync(PipelineRunCreationInfoWithGraph pipelineRun)
        {
            return await _workspaceHttpClient.PostEntityAsync<PipelineRunCreationInfoWithGraph, PipelineRunEntity>(
                string.Format("{0}PipelineRuns/Unsubmitted/CreationInfoWithGraph/?experimentName={1}", _workspaceHttpClient.BaseAddress, pipelineRun.CreationInfo.RunHistoryExperimentName),
                pipelineRun).ConfigureAwait(false);
        }

        public async Task SubmitPipelineRunAsync(string pipelineRunId, string parentRunId)
        {
            string requestUri;
            if (string.IsNullOrWhiteSpace(parentRunId))
            {
                requestUri = string.Format("{0}PipelineRuns/Submit/{1}", _workspaceHttpClient.BaseAddress, pipelineRunId);
            }
            else
            {
                requestUri = string.Format("{0}PipelineRuns/Submit/{1}/?parentRunId={2}", _workspaceHttpClient.BaseAddress, pipelineRunId, parentRunId);
            }
            IEnumerable<string> requestId;
            _workspaceHttpClient.DefaultRequestHeaders.TryGetValues(HttpClientExtensions.RequestIdHeader, out requestId);
            var request = HttpClientExtensions.CreateHttpRequestMessage(HttpMethod.Post, requestUri, requestId?.FirstOrDefault());
            request.Content = new StringContent(string.Empty, Encoding.UTF8, HttpClientExtensions.ContentType);

            HttpResponseMessage response = await _workspaceHttpClient.SendAsync(request).ConfigureAwait(false);
            await response.EnsureStatusCodeAsync(ignoreConflictError: true).ConfigureAwait(false);
        }

        public async Task<IList<PipelineRunEntity>> BulkGetPipelineRunAsync(string[] ids)
        {
            string path = string.Format("{0}PipelineRuns/bulk", _workspaceHttpClient.BaseAddress);
            return await _workspaceHttpClient.PostEntityAsync<string[], IList<PipelineRunEntity>>(path, ids).ConfigureAwait(false);
        }

        public async Task<PipelineRunEntity> GetPipelineRunAsync(string pipelineRunId, bool isTry = false)
        {
            string url = string.Format("{0}PipelineRuns/{1}", _workspaceHttpClient.BaseAddress, pipelineRunId);
            return await _workspaceHttpClient.GetEntityAsync<PipelineRunEntity>(url, isTry)
                .ConfigureAwait(false);
        }

        public async Task CancelPipelineRunAsync(string id)
        {
            var response = await _workspaceHttpClient.DeleteAsync(string.Format("{0}PipelineRuns/{1}/Execution",
                                                        _workspaceHttpClient.BaseAddress,
                                                        id)).ConfigureAwait(false);
            await HttpClientExtensions.EnsureStatusCodeAsync(response).ConfigureAwait(false);
        }

        public async Task CancelPipelineRunPostAsync(string id)
        {
            IEnumerable<string> requestId;
            _workspaceHttpClient.DefaultRequestHeaders.TryGetValues(HttpClientExtensions.RequestIdHeader, out requestId);
            var request = HttpClientExtensions.CreateHttpRequestMessage(HttpMethod.Post, string.Format("{0}PipelineRuns/{1}/Cancel", _workspaceHttpClient.BaseAddress, id), requestId?.FirstOrDefault());
            request.Content = new StringContent(string.Empty, Encoding.UTF8, HttpClientExtensions.ContentType);
            var response = await _workspaceHttpClient.SendAsync(request).ConfigureAwait(false);
            await HttpClientExtensions.EnsureStatusCodeAsync(response).ConfigureAwait(false);
        }

        public async Task<PipelineRunEntity> UpdatePipelineRunAsync(string id, PipelineRunEntity entity)
        {
            string path = string.Format("{0}PipelineRuns/{1}", _workspaceHttpClient.BaseAddress, id);
            return await _workspaceHttpClient.PutEntityAsync(path, entity).ConfigureAwait(false);
        }

        public async Task<IDictionary<string, TaskStatus>> GetAllNodesStatusAsync(string pipelineRunId, IList<string> nodeIdPath)
        {
            return await _workspaceHttpClient.PostEntityAsync<IList<string>, IDictionary<string, TaskStatus>>(
                string.Format("{0}PipelineRuns/{1}/Graph/AllNodes/Status", _workspaceHttpClient.BaseAddress, pipelineRunId), nodeIdPath)
                .ConfigureAwait(false);
        }

        public async Task<IDictionary<string, TaskStatusCode>> GetAllNodesStatusCodeAsync(string pipelineRunId, IList<string> nodeIdPath)
        {
            return await _workspaceHttpClient.PostEntityAsync<IList<string>, IDictionary<string, TaskStatusCode>>(
                string.Format("{0}PipelineRuns/{1}/Graph/AllNodes/StatusCode", _workspaceHttpClient.BaseAddress, pipelineRunId), nodeIdPath)
                .ConfigureAwait(false);
        }

        public async Task<IDictionary<string, NodeOutput>> GetNodeOutputs(string pipelineRunId, IList<string> nodeIdPath)
        {
            return await _workspaceHttpClient.PostEntityAsync<IList<string>, IDictionary<string, NodeOutput>>(
                string.Format("{0}PipelineRuns/{1}/Outputs", _workspaceHttpClient.BaseAddress, pipelineRunId), nodeIdPath)
                .ConfigureAwait(false);
        }

        public async Task<TaskStatusCode> GetNodeStatusCodeAsync(string pipelineRunId, IList<string> nodeIdPath)
        {
            return await _workspaceHttpClient.PostEntityAsync<IList<string>, TaskStatusCode>(
                string.Format("{0}PipelineRuns/{1}/Graph/Node/StatusCode", _workspaceHttpClient.BaseAddress, pipelineRunId), nodeIdPath)
                .ConfigureAwait(false);
        }

        public async Task<Stream> GetNodeShareableStdoutAsync(string pipelineRunId, IList<string> nodeIdPath)
        {
            return
                await
                    _workspaceHttpClient.PostStreamAsync(
                        $"{_workspaceHttpClient.BaseAddress}PipelineRuns/{pipelineRunId}/Graph/Shareable/STDOUT",
                        nodeIdPath).ConfigureAwait(false);
        }

        public async Task<Stream> GetNodeShareableStderrAsync(string pipelineRunId, IList<string> nodeIdPath)
        {
            return
                await
                    _workspaceHttpClient.PostStreamAsync(
                        $"{_workspaceHttpClient.BaseAddress}PipelineRuns/{pipelineRunId}/Graph/Shareable/STDERR",
                        nodeIdPath).ConfigureAwait(false);
        }

        public async Task<Stream> GetNodeJobLogAsync(string pipelineRunId, IList<string> nodeIdPath)
        {
            return
                await
                    _workspaceHttpClient.PostStreamAsync(
                        $"{_workspaceHttpClient.BaseAddress}PipelineRuns/{pipelineRunId}/Graph/Shareable/JobLog",
                        nodeIdPath).ConfigureAwait(false);
        }

        public Task<PipelineRunEntity> SubmitPipelineRunFromPipelineAsync(string pipelineId, PipelineSubmissionInfo pipelineSubmissionInfo)
        {
            return _workspaceHttpClient.PostEntityAsync<PipelineSubmissionInfo, PipelineRunEntity>(
                string.Format("{0}PipelineRuns/PipelineSubmit/{1}", _workspaceHttpClient.BaseAddress, pipelineId), pipelineSubmissionInfo);
        }

        public async Task<NodeOutput> GetPipelineRunGraphOutputAsync(string pipelineRunId, string graphOutputName)
        {
            string url = string.Format("{0}PipelineRuns/{1}/PipelineRunOutputs/{2}", _workspaceHttpClient.BaseAddress, pipelineRunId, graphOutputName);
            return await _workspaceHttpClient.GetEntityAsync<NodeOutput>(url).ConfigureAwait(false);
        }

        public async Task<PipelineRunEntity> ResubmitPipelineRunAsync(string pipelineRunId, PipelineRunCreationInfo creationInfo)
        {
            return await _workspaceHttpClient.PostEntityAsync<PipelineRunCreationInfo, PipelineRunEntity>(
                string.Format("{0}PipelineRuns/Resubmit/{1}", _workspaceHttpClient.BaseAddress, pipelineRunId),
                creationInfo).ConfigureAwait(false);
        }

        #endregion

        #region Graph

        public async Task<EntityInterface> GetGraphInterfaceAsync(string graphId)
        {
            return await _workspaceHttpClient.GetEntityAsync<EntityInterface>(string.Format("{0}Graphs/{1}/Interface",
                                                                                    _workspaceHttpClient.BaseAddress,
                                                                                    graphId))
                .ConfigureAwait(false);
        }

        public async Task<GraphEntity> GetGraphAsync(string graphId)
        {
            return await _workspaceHttpClient.GetEntityAsync<GraphEntity>(string.Format("{0}Graphs/{1}", _workspaceHttpClient.BaseAddress, graphId))
                .ConfigureAwait(false);
        }

        // "V2" version includes a serialized runconfig populated for older module nodes
        public async Task<GraphEntity> GetGraphV2Async(string graphId)
        {
            return await _workspaceHttpClient.GetEntityAsync<GraphEntity>(string.Format("{0}Graphs/V2/{1}", _workspaceHttpClient.BaseAddress, graphId))
                .ConfigureAwait(false);
        }

        public async Task<VisualGraphWithEntityInterface> GetVisualGraphWithEntityInterfaceAsync(string graphId)
        {
            return await _workspaceHttpClient.GetEntityAsync<VisualGraphWithEntityInterface>(string.Format("{0}Graphs/{1}/VisualGraphAndInterface", _workspaceHttpClient.BaseAddress, graphId))
                .ConfigureAwait(false);
        }

        // "V2" version includes a serialized runconfig populated for older module nodes
        public async Task<VisualGraphWithEntityInterface> GetVisualGraphWithEntityInterfaceV2Async(string graphId)
        {
            return await _workspaceHttpClient.GetEntityAsync<VisualGraphWithEntityInterface>(string.Format("{0}Graphs/V2/{1}/VisualGraphAndInterface", _workspaceHttpClient.BaseAddress, graphId))
                .ConfigureAwait(false);
        }

        public async Task<GraphLayout> CreateGraphLayoutAsync(string graphId, GraphLayoutCreationInfo graphLayoutCreationInfo)
        {
            var serverPath = string.Format("{0}Graphs/{1}/Layout", _workspaceHttpClient.BaseAddress, graphId);
            return await _workspaceHttpClient.PostEntityAsync<GraphLayoutCreationInfo, GraphLayout>(serverPath, graphLayoutCreationInfo).ConfigureAwait(false);
        }

        public async Task<GraphLayout> UpdateGraphLayoutAsync(string graphId, GraphLayout graphLayout)
        {
            var serverPath = string.Format("{0}Graphs/{1}/Layout", _workspaceHttpClient.BaseAddress, graphId);
            return await _workspaceHttpClient.PutEntityAsync(serverPath, graphLayout).ConfigureAwait(false);
        }

        public async Task<GraphEntity> CreateVisualGraphAndInterfaceAsync(VisualGraphWithEntityInterface visualGraphWithEntityInterface)
        {
            var serverPath = string.Format("{0}Graphs/VisualGraphAndInterface", _workspaceHttpClient.BaseAddress);
            return await _workspaceHttpClient.PostEntityAsync<VisualGraphWithEntityInterface, GraphEntity>(serverPath, visualGraphWithEntityInterface).ConfigureAwait(false);
        }

        public async Task<NestedResourceInfo> GetNestedGraphInfoAsync(string graphId, bool fetchNestedGraphs = true, bool populateRunconfig = false)
        {
            string url = string.Format("{0}Graphs/{1}/NestedInfo/?fetchNestedGraphs={2}&populateRunconfig={3}", _workspaceHttpClient.BaseAddress, graphId, fetchNestedGraphs, populateRunconfig);
            return await _workspaceHttpClient.GetEntityAsync<NestedResourceInfo>(url)
                .ConfigureAwait(false);
        }

        #endregion

        #region Pipelines
        public Task<PipelineEntity> CreatePipelineAsync(PipelineCreationInfo pipelineCreationInfo)
        {
            return _workspaceHttpClient.PostEntityAsync<PipelineCreationInfo, PipelineEntity>(
                string.Format("{0}Pipelines/Create", _workspaceHttpClient.BaseAddress), pipelineCreationInfo);
        }

        public Task<PipelineEntity> GetPipelineAsync(string pipelineId)
        {
            return _workspaceHttpClient.GetEntityAsync<PipelineEntity>(string.Format("{0}Pipelines/{1}", _workspaceHttpClient.BaseAddress, pipelineId));
        }

        public Task<PipelineViewEntity> GetPipelineViewAsync(string pipelineId)
        {
            return _workspaceHttpClient.GetEntityAsync<PipelineViewEntity>(string.Format("{0}Pipelines/{1}/View", _workspaceHttpClient.BaseAddress, pipelineId));
        }

        public Task UpdatePipelineStatusAsync(string pipelineId, EntityStatus entityStatus)
        {
            return _workspaceHttpClient.PutEntityNoResponseAsync(
                string.Format("{0}Pipelines/{1}/status", _workspaceHttpClient.BaseAddress, pipelineId),
                entityStatus.ToString());
        }

        public Task<PipelineEntity> UpdatePipelineAsync(string pipelineId, PipelineEntity updatedPipeline)
        {
            return _workspaceHttpClient.PutEntityAsync<PipelineEntity, PipelineEntity>(
                string.Format("{0}Pipelines/{1}/pipeline", _workspaceHttpClient.BaseAddress, pipelineId),
                updatedPipeline);
        }

        public async Task<EntityWithContinuationToken<IList<PipelineRunEntity>>> GetPipelineRunsByPipelineIdAsync(string pipelineId, string continuationToken, int batchSize)
        {
            return await _workspaceHttpClient.ListEntityWithContinuationTokenAsync<IList<PipelineRunEntity>>(
                serverPath: string.Format("{0}PipelineRuns/pipeline/{1}", _workspaceHttpClient.BaseAddress, pipelineId),
                continuationToken: continuationToken,
                batchSize: batchSize).ConfigureAwait(false);
        }

        public async Task<EntityWithContinuationToken<IList<PipelineEntity>>> GetPipelinesAsync(bool activeOnly, string continuationToken, int batchSize)
        {
            return await _workspaceHttpClient.ListEntityWithContinuationTokenAsync<IList<PipelineEntity>>(
                serverPath: string.Format("{0}Pipelines?activeOnly={1}", _workspaceHttpClient.BaseAddress, activeOnly),
                continuationToken: continuationToken,
                batchSize: batchSize).ConfigureAwait(false);
        }

        public async Task<EntityWithContinuationToken<IList<PipelineViewEntity>>> GetPipelineViewAsync(bool activeOnly, bool skipEndpoints, bool skipTotalRuns, string continuationToken, int batchSize)
        {
            return await _workspaceHttpClient.ListEntityWithContinuationTokenAsync<IList<PipelineViewEntity>>(
                serverPath: string.Format("{0}Pipelines/View?activeOnly={1}&skipEndpoints={2}&skipTotalRuns={3}", _workspaceHttpClient.BaseAddress, activeOnly, skipEndpoints, skipTotalRuns),
                continuationToken: continuationToken,
                batchSize: batchSize).ConfigureAwait(false);
        }

        public async Task<DeepPipelineViewEntity> GetDeepPipelineViewEntityAsync(string pipelineViewId, bool populateRunconfig = false)
        {
            string url = string.Format("{0}Pipelines/{1}/View/Deep/?populateRunConfig={2}", _workspaceHttpClient.BaseAddress, pipelineViewId, populateRunconfig);
            return await _workspaceHttpClient.GetEntityAsync<DeepPipelineViewEntity>(url)
                .ConfigureAwait(false);
        }
        #endregion

        #region Schedules
        public Task<PipelineScheduleEntity> CreateScheduleAsync(ScheduleCreationInfo scheduleCreationInfo)
        {
            return _workspaceHttpClient.PostEntityAsync<ScheduleCreationInfo, PipelineScheduleEntity>(
                string.Format("{0}Schedules/Create", _workspaceHttpClient.BaseAddress), scheduleCreationInfo);
        }

        public Task<PipelineScheduleEntity> GetScheduleAsync(string scheduleId)
        {
            return _workspaceHttpClient.GetEntityAsync<PipelineScheduleEntity>(string.Format("{0}Schedules/{1}", _workspaceHttpClient.BaseAddress, scheduleId));
        }

        public async Task<PipelineScheduleEntity> UpdateScheduleAsync(string scheduleId, PipelineScheduleEntity updated)
        {
            string path = _workspaceHttpClient.BaseAddress + "Schedules/" + scheduleId;
            return await _workspaceHttpClient.PutEntityAsync(path, updated).ConfigureAwait(false);
        }

        public async Task<EntityWithContinuationToken<IList<PipelineScheduleEntity>>> GetSchedulesByPipelineIdAsync(string pipelineId, string continuationToken, int batchSize)
        {
            return await _workspaceHttpClient.ListEntityWithContinuationTokenAsync<IList<PipelineScheduleEntity>>(
                serverPath: string.Format("{0}Schedules/pipeline/{1}", _workspaceHttpClient.BaseAddress, pipelineId),
                continuationToken: continuationToken,
                batchSize: batchSize).ConfigureAwait(false);
        }

        public async Task<EntityWithContinuationToken<IList<PipelineScheduleEntity>>> GetSchedulesByPipelineEndpointIdAsync(string pipelineEndpointId, string continuationToken, int batchSize)
        {
            return await _workspaceHttpClient.ListEntityWithContinuationTokenAsync<IList<PipelineScheduleEntity>>(
               serverPath: string.Format("{0}Schedules/PipelineEndpoint/{1}", _workspaceHttpClient.BaseAddress, pipelineEndpointId),
               continuationToken: continuationToken,
               batchSize: batchSize).ConfigureAwait(false);
        }

        public async Task<EntityWithContinuationToken<IList<PipelineScheduleEntity>>> GetSchedulesAsync(bool activeOnly, string continuationToken, int batchSize)
        {
            return await _workspaceHttpClient.ListEntityWithContinuationTokenAsync<IList<PipelineScheduleEntity>>(
                serverPath: string.Format("{0}Schedules?activeOnly={1}", _workspaceHttpClient.BaseAddress, activeOnly),
                continuationToken: continuationToken,
                batchSize: batchSize).ConfigureAwait(false);
        }

        public async Task<EntityWithContinuationToken<IList<PipelineRunEntity>>> GetPipelineRunsByScheduleIdAsync(string scheduleId, string continuationToken, int batchSize)
        {
            return await _workspaceHttpClient.ListEntityWithContinuationTokenAsync<IList<PipelineRunEntity>>(
                serverPath: string.Format("{0}PipelineRuns/Schedule/{1}", _workspaceHttpClient.BaseAddress, scheduleId),
                continuationToken: continuationToken,
                batchSize: batchSize).ConfigureAwait(false);
        }

        public async Task<PipelineRunEntity> GetLastPipelineRunByScheduleIdAsync(string scheduleId)
        {
            return await _workspaceHttpClient.GetEntityAsync<PipelineRunEntity>(string.Format("{0}PipelineRuns/Schedule/{1}/Last", _workspaceHttpClient.BaseAddress, scheduleId));
        }
        #endregion

        #region PipelineEndpoint
        public Task<PipelineEndpointEntity> CreatePipelineEndpointAsync(PipelineEndpointCreationInfo pipelineEndpointCreationInfo)
        {
            return _workspaceHttpClient.PostEntityAsync<PipelineEndpointCreationInfo, PipelineEndpointEntity>(
                string.Format("{0}PipelineEndpoints/Create", _workspaceHttpClient.BaseAddress), pipelineEndpointCreationInfo);
        }

        public Task<PipelineEndpointEntity> GetPipelineEndpointByIdAsync(string id)
        {
            return _workspaceHttpClient.GetEntityAsync<PipelineEndpointEntity>(string.Format("{0}PipelineEndpoints/Id/{1}", _workspaceHttpClient.BaseAddress, id));
        }

        public Task<PipelineEndpointEntity> GetPipelineEndpointByNameAsync(string name)
        {
            return _workspaceHttpClient.GetEntityAsync<PipelineEndpointEntity>(string.Format("{0}PipelineEndpoints/Name?name={1}", _workspaceHttpClient.BaseAddress, name));
        }

        public async Task<PipelineEndpointEntity> UpdatePipelineEndpointAsync(string id, PipelineEndpointEntity updatedEntity)
        {
            string path = _workspaceHttpClient.BaseAddress + "PipelineEndpoints/" + id;
            return await _workspaceHttpClient.PutEntityAsync(path, updatedEntity).ConfigureAwait(false);
        }

        public async Task<EntityWithContinuationToken<IList<PipelineEndpointEntity>>> GetPipelineEndpointsAsync(bool activeOnly, string continuationToken, int batchSize)
        {
            return await _workspaceHttpClient.ListEntityWithContinuationTokenAsync<IList<PipelineEndpointEntity>>(
                serverPath: string.Format("{0}PipelineEndpoints?activeOnly={1}", _workspaceHttpClient.BaseAddress, activeOnly),
                continuationToken: continuationToken,
                batchSize: batchSize).ConfigureAwait(false);
        }

        public async Task<EntityWithContinuationToken<IList<PipelineEntity>>> GetPipelinesFromPipelineEndpointAsync(string id, bool activeOnly, string continuationToken, int batchSize)
        {
            return await _workspaceHttpClient.ListEntityWithContinuationTokenAsync<IList<PipelineEntity>>(
                serverPath: string.Format("{0}PipelineEndpoints/{1}/pipelines?activeOnly={2}", _workspaceHttpClient.BaseAddress, id, activeOnly),
                continuationToken: continuationToken,
                batchSize: batchSize).ConfigureAwait(false);
        }

        public Task<PipelineRunEntity> SubmitPipelineRunFromPipelineEndpointByIdAsync(string id, PipelineSubmissionInfo pipelineSubmissionInfo, string version)
        {
            return _workspaceHttpClient.PostEntityAsync<PipelineSubmissionInfo, PipelineRunEntity>(
                string.Format("{0}PipelineRuns/PipelineEndpointSubmit/Id/{1}/{2}", _workspaceHttpClient.BaseAddress, id, version), pipelineSubmissionInfo);
        }

        public Task<PipelineRunEntity> SubmitPipelineRunFromPipelineEndpointByNameAsync(string name, PipelineSubmissionInfo pipelineSubmissionInfo, string version)
        {
            return _workspaceHttpClient.PostEntityAsync<PipelineSubmissionInfo, PipelineRunEntity>(
                string.Format("{0}PipelineRuns/PipelineEndpointSubmit/Name?name={1}&pipelineVersion={2} ", _workspaceHttpClient.BaseAddress, name, version), pipelineSubmissionInfo);
        }

        public Task<PipelineRunEntity> SubmitPipelineRunFromPipelineEndpointAsync(string id, PipelineSubmissionInfo pipelineSubmissionInfo)
        {
            return _workspaceHttpClient.PostEntityAsync<PipelineSubmissionInfo, PipelineRunEntity>(
                string.Format("{0}PipelineRuns/PipelineEndpointSubmit/Id/{1}", _workspaceHttpClient.BaseAddress, id), pipelineSubmissionInfo);
        }
        #endregion

        #region AmlModules
        public async Task<AzureMLModule> CreateAmlModuleAsync(AzureMLModuleCreationInfo creationInfo)
        {
            return await CreateAmlModuleAsync(creationInfo, repositoryId: null);
        }

        public async Task<AzureMLModule> CreateAmlModuleAsync(AzureMLModuleCreationInfo creationInfo, string repositoryId = null)
        {
            return await GetHttpClient(repositoryId).PostEntityAsync<AzureMLModuleCreationInfo, AzureMLModule>(
                FormatRoute("CreateAmlModuleAsync", repositoryId), creationInfo);
        }

        public async Task<AzureMLModule> GetAmlModuleByIdAsync(string id)
        {
            return await GetAmlModuleByIdAsync(id, repositoryId: null);
        }

        public async Task<AzureMLModule> GetAmlModuleByIdAsync(string id, string repositoryId = null)
        {
            return await GetHttpClient(repositoryId).GetEntityAsync<AzureMLModule>(FormatRoute("GetAmlModuleByIdAsync", repositoryId, id));
        }

        public async Task<AzureMLModule> GetAmlModuleByNameAsync(string name)
        {
            return await GetAmlModuleByNameAsync(name, repositoryId: null);
        }

        public async Task<AzureMLModule> GetAmlModuleByNameAsync(string name, string repositoryId = null)
        {
            return await GetHttpClient(repositoryId).GetEntityAsync<AzureMLModule>(FormatRoute("GetAmlModuleByNameAsync", repositoryId, HttpUtility.UrlEncode(name)));
        }

        public async Task<AzureMLModule> UpdateAmlModuleAsync(string id, AzureMLModule updatedModule)
        {
            return await UpdateAmlModuleAsync(id, updatedModule, repositoryId: null);
        }

        public async Task<AzureMLModule> UpdateAmlModuleAsync(string id, AzureMLModule updatedModule, string repositoryId = null)
        {
            return await GetHttpClient(repositoryId).PutEntityAsync(FormatRoute("UpdateAmlModuleAsync", repositoryId, id), updatedModule).ConfigureAwait(false);
        }

        public async Task<AzureMLModule> UpdateAmlModuleS2SAsync(string id, AzureMLModule updatedModule)
        {
            return await UpdateAmlModuleS2SAsync(id, updatedModule, repositoryId: null);
        }

        public async Task<AzureMLModule> UpdateAmlModuleS2SAsync(string id, AzureMLModule updatedModule, string repositoryId = null)
        {
            return await GetHttpClient(repositoryId).PutEntityAsync(FormatRoute("UpdateAmlModuleS2SAsync", repositoryId, id), updatedModule).ConfigureAwait(false);
        }

        public async Task<AzureMLModuleVersion> ResolveAmlModuleAsync(string id)
        {
            return await ResolveAmlModuleAsync(id, repositoryId: null);
        }

        public async Task<AzureMLModuleVersion> ResolveAmlModuleAsync(string id, string repositoryId = null)
        {
            return await GetHttpClient(repositoryId).GetEntityAsync<AzureMLModuleVersion>(FormatRoute("ResolveAmlModuleAsync", repositoryId, id));
        }

        public async Task<EntityWithContinuationToken<IList<AzureMLModule>>> GetAmlModulesAsync(bool activeOnly, string continuationToken, int batchSize)
        {
            return await GetAmlModulesAsync(activeOnly, continuationToken, batchSize, repositoryId: null);
        }

        public async Task<EntityWithContinuationToken<IList<AzureMLModule>>> GetAmlModulesAsync(bool activeOnly, string continuationToken, int batchSize, string repositoryId = null)
        {
            return await GetHttpClient(repositoryId).ListEntityWithContinuationTokenAsync<IList<AzureMLModule>>(
                serverPath: FormatRoute("GetAmlModulesAsync", repositoryId, activeOnly),
                continuationToken: continuationToken,
                batchSize: batchSize).ConfigureAwait(false);
        }

        public async Task<EntityWithContinuationToken<IList<AzureMLModuleVersion>>> GetResolvedAmlModulesAsync(bool activeOnly, string continuationToken, int batchSize)
        {
            return await GetResolvedAmlModulesAsync(activeOnly, continuationToken, batchSize, repositoryId: null);
        }

        public async Task<EntityWithContinuationToken<IList<AzureMLModuleVersion>>> GetResolvedAmlModulesAsync(bool activeOnly, string continuationToken, int batchSize, string repositoryId = null)
        {
            return await GetHttpClient(repositoryId).ListEntityWithContinuationTokenAsync<IList<AzureMLModuleVersion>>(
                serverPath: FormatRoute("GetResolvedAmlModulesAsync", repositoryId, activeOnly),
                continuationToken: continuationToken,
                batchSize: batchSize).ConfigureAwait(false);
        }

        public async Task<EntityWithContinuationToken<IList<Tuple<AzureMLModule, IList<AzureMLModuleVersion>>>>> ListAmlModuleWithVersionsAsync(bool activeOnly, string continuationToken, int batchSize, GetAmlModuleVersionsType versionsType, bool removeInterface, string orderBy, string filter)
        {
            return await ListAmlModuleWithVersionsAsync(activeOnly, continuationToken, batchSize, GetAmlModuleVersionsType.Default, false, orderBy, filter, repositoryId: null);
        }

        public async Task<EntityWithContinuationToken<IList<Tuple<AzureMLModule, IList<AzureMLModuleVersion>>>>> ListAmlModuleWithVersionsAsync(bool activeOnly, string continuationToken, int batchSize, GetAmlModuleVersionsType versionsType = GetAmlModuleVersionsType.Default, bool removeInterface = false, string orderBy = null, string filter = null, string repositoryId = null)
        {
            return await GetHttpClient(repositoryId).ListEntityWithContinuationTokenAsync<IList<Tuple<AzureMLModule, IList<AzureMLModuleVersion>>>>(
                serverPath: FormatRoute("ListAmlModuleWithVersionsAsync", repositoryId, activeOnly, versionsType, removeInterface, orderBy, filter),
                continuationToken: continuationToken,
                batchSize: batchSize).ConfigureAwait(false);
        }

        public async Task<IList<AzureMLModuleVersion>> BulkResolveAmlModuleAsync(string[] ids)
        {
            return await BulkResolveAmlModuleAsync(ids, repositoryId: null);
        }

        public async Task<IList<AzureMLModuleVersion>> BulkResolveAmlModuleAsync(string[] ids, string repositoryId = null)
        {
            return await GetHttpClient(repositoryId).PostEntityAsync<string[], IList<AzureMLModuleVersion>>(FormatRoute("BulkResolveAmlModuleAsync", repositoryId), ids);
        }

        public async Task<AzureMLModuleVersion> CreateAmlModuleVersionAsync(AzureMLModuleVersionCreationInfo creationInfo)
        {
            return await CreateAmlModuleVersionAsync(creationInfo, repositoryId: null);
        }

        public async Task<AzureMLModuleVersion> CreateAmlModuleVersionAsync(AzureMLModuleVersionCreationInfo creationInfo, string repositoryId = null)
        {
            return await GetHttpClient(repositoryId).PostEntityAsync<AzureMLModuleVersionCreationInfo, AzureMLModuleVersion>(
               FormatRoute("CreateAmlModuleVersionAsync", repositoryId), creationInfo);
        }

        public async Task<AzureMLModuleVersion> CreateSubGraphModuleVersionAsync(SubGraphVersionCreationInfo creationInfo)
        {
            return await CreateSubGraphModuleVersionAsync(creationInfo, repositoryId: null);
        }

        public async Task<AzureMLModuleVersion> CreateSubGraphModuleVersionAsync(SubGraphVersionCreationInfo creationInfo, string repositoryId = null)
        {
            return await GetHttpClient(repositoryId).PostEntityAsync<SubGraphVersionCreationInfo, AzureMLModuleVersion>(
                FormatRoute("CreateSubGraphModuleVersionAsync", repositoryId), creationInfo).ConfigureAwait(false);
        }

        public async Task<AzureMLModuleVersion> GetAmlModuleVersionByIdAsync(string id)
        {
            return await GetAmlModuleVersionByIdAsync(id, repositoryId: null);
        }

        public async Task<AzureMLModuleVersion> GetAmlModuleVersionByIdAsync(string id, string repositoryId = null)
        {
            return await GetHttpClient(repositoryId).GetEntityAsync<AzureMLModuleVersion>(FormatRoute("GetAmlModuleVersionByIdAsync", repositoryId, id));
        }

        public async Task<AzureMLModuleVersion> GetAzureMLModuleVersionByHashAsync(string idHash)
        {
            return await GetAzureMLModuleVersionByHashAsync(idHash, repositoryId: null);
        }

        public async Task<AzureMLModuleVersion> GetAzureMLModuleVersionByHashAsync(string idHash, string repositoryId)
        {
            return await TryGetAzureMLModuleVersionByHashAsync(idHash, false);
        }

        public async Task<AzureMLModuleVersion> TryGetAzureMLModuleVersionByHashAsync(string idHash, string repositoryId)
        {
            return await TryGetAzureMLModuleVersionByHashAsync(idHash, true, repositoryId);
        }

        public async Task<AzureMLModuleVersion> TryGetAzureMLModuleVersionByHashAsync(string idHash)
        {
            return await TryGetAzureMLModuleVersionByHashAsync(idHash, true, repositoryId: null);
        }

        private async Task<AzureMLModuleVersion> TryGetAzureMLModuleVersionByHashAsync(string idHash, bool isTry, string repositoryId = null)
        {
            Console.WriteLine(repositoryId);
            string path = FormatRoute("TryGetAzureMLModuleVersionByHashAsync", repositoryId, idHash);
            return await GetHttpClient(repositoryId).GetEntityAsync<AzureMLModuleVersion>(path, isTry).ConfigureAwait(false);
        }

        public async Task<AzureMLModuleVersion> UpdateAmlModuleVersionAsync(string id, AzureMLModuleVersion updated)
        {
            return await UpdateAmlModuleVersionAsync(id, updated, repositoryId: null);
        }

        public async Task<AzureMLModuleVersion> UpdateAmlModuleVersionAsync(string id, AzureMLModuleVersion updated, string repositoryId)
        {
            return await GetHttpClient(repositoryId).PutEntityAsync(FormatRoute("UpdateAmlModuleVersionAsync", repositoryId, id), updated).ConfigureAwait(false);
        }

        public async Task<AzureMLModuleVersion> CreateAE365ModuleVersionAsync(AE365ModuleVersionCreationInfo creationInfo)
        {
            return await GetHttpClient(null).PostEntityAsync<AE365ModuleVersionCreationInfo, AzureMLModuleVersion>(
               FormatRoute("CreateAE365ModuleVersionAsync", null), creationInfo);
        }

        public async Task<AzureMLModuleVersion> GetAE365ModuleVersionByIdAsync(string id)
        {
            return await GetHttpClient(null).GetEntityAsync<AzureMLModuleVersion>(FormatRoute("GetAE365ModuleVersionByIdAsync", null, id));
        }

        public async Task<AzureMLModuleVersion> UpdateAE365ModuleVersionAsync(string id, AzureMLModuleVersion updated)
        {
            return await GetHttpClient(null).PutEntityAsync(FormatRoute("UpdateAE365ModuleVersionAsync", null, id), updated).ConfigureAwait(false);
        }
        #endregion

        #region PipelineDrafts
        public async Task<PipelineDraft> CreatePipelineDraftAsync(PipelineDraft pipelineDraft)
        {
            return await _workspaceHttpClient.PostEntityAsync<PipelineDraft, PipelineDraft>(
                string.Format("{0}PipelineDrafts/Create", _workspaceHttpClient.BaseAddress), pipelineDraft);
        }

        public async Task<PipelineDraft> GetPipelineDraftByIdAsync(string id)
        {
            return await _workspaceHttpClient.GetEntityAsync<PipelineDraft>(string.Format("{0}PipelineDrafts/{1}", _workspaceHttpClient.BaseAddress, id));
        }

        public async Task SavePipelineDraftAsync(string id, PipelineDraft updatedEntity)
        {
            string path = _workspaceHttpClient.BaseAddress + "PipelineDrafts/" + id;
            await _workspaceHttpClient.PutEntityAsync(path, updatedEntity).ConfigureAwait(false);
        }

        public async Task<EntityWithContinuationToken<IList<PipelineDraft>>> ListPipelineDraftsAsync(Dictionary<string, string> tagFilters, string continuationToken, int batchSize)
        {
            return await _workspaceHttpClient.ListEntityWithContinuationTokenAsync<IList<PipelineDraft>>(
                serverPath: string.Format("{0}PipelineDrafts", _workspaceHttpClient.BaseAddress),
                filters: tagFilters,
                continuationToken: continuationToken,
                batchSize: batchSize).ConfigureAwait(false);
        }

        public Task DeletePipelineDraftAsync(string id, PipelineDraft pipelineDraft)
        {
            return _workspaceHttpClient.DeleteEntityNoResponseAsync<PipelineDraft>(
                string.Format("{0}PipelineDrafts/{1}", _workspaceHttpClient.BaseAddress, id), pipelineDraft);
        }

        public async Task<PipelineRunEntity> SubmitPipelineRunFromPipelineDraftAsync(PipelineDraft pipelineDraft)
        {
            return await _workspaceHttpClient.PostEntityAsync<PipelineDraft, PipelineRunEntity>(
                string.Format("{0}PipelineRuns/PipelineDraftSubmit", _workspaceHttpClient.BaseAddress), pipelineDraft);
        }

        public async Task<PipelineEntity> CreatePipelineFromPipelineDraftAsync(PipelineDraft pipelineDraft)
        {
            return await _workspaceHttpClient.PostEntityAsync<PipelineDraft, PipelineEntity>(
                string.Format("{0}Pipelines/PipelineDraftCreate", _workspaceHttpClient.BaseAddress), pipelineDraft);
        }

        public async Task<PipelineDraft> CloneFromPipelineDraftAsync(string pipelineDraftId)
        {
            return await _workspaceHttpClient.PostEntityAsync<string, PipelineDraft>(string.Format("{0}PipelineDrafts/Clone", _workspaceHttpClient.BaseAddress), pipelineDraftId);
        }

        public async Task<PipelineDraft> CloneFromPipelineRunAsync(string pipelineRunId)
        {
            return await _workspaceHttpClient.PostEntityAsync<string, PipelineDraft>(string.Format("{0}PipelineDrafts/ClonePipelineRun", _workspaceHttpClient.BaseAddress), pipelineRunId);
        }

        public async Task<PipelineDraft> CloneFromPipelineAsync(string pipelineId)
        {
            return await _workspaceHttpClient.PostEntityAsync<string, PipelineDraft>(string.Format("{0}PipelineDrafts/ClonePublished", _workspaceHttpClient.BaseAddress), pipelineId);
        }

        #endregion PipelineDrafts

        #region GraphDraft
        public Task<GraphDraftEntity> CreateGraphDraftAsync(GraphDraftEntity graphDraft)
        {
            return _workspaceHttpClient.PostEntityAsync<GraphDraftEntity>(
                string.Format("{0}GraphDrafts", _workspaceHttpClient.BaseAddress), graphDraft);
        }

        public Task<GraphDraftEntity> GetGraphDraftAsync(string graphDraftId)
        {
            return _workspaceHttpClient.GetEntityAsync<GraphDraftEntity>(string.Format("{0}GraphDrafts/{1}", _workspaceHttpClient.BaseAddress, graphDraftId));
        }

        // "V2" version includes a serialized runconfig populated for older module nodes
        public Task<GraphDraftEntity> GetGraphDraftV2Async(string graphDraftId)
        {
            return _workspaceHttpClient.GetEntityAsync<GraphDraftEntity>(string.Format("{0}GraphDrafts/V2/{1}", _workspaceHttpClient.BaseAddress, graphDraftId));
        }

        public async Task<GraphDraftEntity> UpdateGraphDraftAsync(string graphDraftId, GraphDraftEntity updated)
        {
            string path = _workspaceHttpClient.BaseAddress + "GraphDrafts/" + graphDraftId;
            return await _workspaceHttpClient.PutEntityAsync(path, updated).ConfigureAwait(false);
        }

        public Task DeleteGraphDraftAsync(string id, GraphDraftEntity graphDraft)
        {
            return _workspaceHttpClient.DeleteEntityNoResponseAsync<GraphDraftEntity>(
                string.Format("{0}GraphDrafts/{1}", _workspaceHttpClient.BaseAddress, id), graphDraft);
        }

        public async Task<NestedResourceInfo> GetNestedGraphDraftInfoAsync(string graphDraftId, bool fetchNestedGraphs = true, bool populateRunconfig = false)
        {
            string url = string.Format("{0}GraphDrafts/{1}/NestedInfo/?fetchNestedGraphs={2}&populateRunconfig={3}", _workspaceHttpClient.BaseAddress, graphDraftId, fetchNestedGraphs, populateRunconfig);
            return await _workspaceHttpClient.GetEntityAsync<NestedResourceInfo>(url)
                .ConfigureAwait(false);
        }

        #endregion

        #region SubGraphs

        public async Task<ModuleEntity> CreateSubGraphModuleAsync(SubGraphCreationInfo creationInfo)
        {
            string path = _workspaceHttpClient.BaseAddress + "Modules/SubGraph";
            return await _workspaceHttpClient.PostEntityAsync<SubGraphCreationInfo, ModuleEntity>(path, creationInfo).ConfigureAwait(false);
        }

        #endregion

        #region temporary workaround for studio registering module before AML repo online

        public async Task<AzureMLModule> CreateAmlModuleWithIdAsync(AzureMLModuleCreationInfo creationInfo)
        {
            return await _workspaceHttpClient.PostEntityAsync<AzureMLModuleCreationInfo, AzureMLModule>(
               string.Format("{0}AzureMLModules/CreateWithId", _workspaceHttpClient.BaseAddress), creationInfo);
        }

        public async Task<AzureMLModuleVersion> CreateAmlModuleVersionWithIdAsync(AzureMLModuleVersionCreationInfo creationInfo)
        {
            return await _workspaceHttpClient.PostEntityAsync<AzureMLModuleVersionCreationInfo, AzureMLModuleVersion>(
              string.Format("{0}AzureMLModuleVersions/CreateWithId", _workspaceHttpClient.BaseAddress), creationInfo);
        }

        #endregion

        private HttpClient GetHttpClient(string repositoryId)
        {
            if (repositoryId != null)
            {
                if (_repositoryHttpClient == null)
                {
                    throw new InvalidOperationException("repositoryHttpClient has not been initialized at construction time.");
                }

                return _repositoryHttpClient;
            }

            return _workspaceHttpClient;
        }

        private string FormatRoute(string methodName, string repositoryId, params object[] args)
        {
            Dictionary<string, string> routes = ServiceCallerRoutes.WorkspaceRoutes;
            string prefix = _workspaceHttpClient.BaseAddress.ToString();

            if (repositoryId != null)
            {
                routes = ServiceCallerRoutes.RepositoryRoutes;
            }

            if (routes.TryGetValue(methodName, out string formatString))
            {
                if (repositoryId != null)
                {
                    prefix = string.Format($"pipelines/{DefaultApiVersion}/repositoryId/{repositoryId}");
                }
                return prefix + string.Format(formatString, args);
            }

            throw new ArgumentException($"There is no route associated with the method {methodName}");
        }

        public async Task<DeepPipelineVisualGraphWithEntityInterface> GetDeepPipelineVisualGraphWithEntityInterfaceAsync(string pipelineRunId, bool populateRunconfig = false)
        {
            string url = string.Format("{0}PipelineRuns/{1}/Deep/?populateRunConfig={2}", _workspaceHttpClient.BaseAddress, pipelineRunId, populateRunconfig);
            return await _workspaceHttpClient.GetEntityAsync<DeepPipelineVisualGraphWithEntityInterface>(url)
                .ConfigureAwait(false);
        }

        public async Task<DeepPipelineGraphDraftEntity> GetDeepPipelineGraphDraftEntityAsync(string pipelineDraftId, bool populateRunconfig = false)
        {
            string url = string.Format("{0}PipelineDrafts/{1}/Deep/?populateRunConfig={2}", _workspaceHttpClient.BaseAddress, pipelineDraftId, populateRunconfig);
            return await _workspaceHttpClient.GetEntityAsync<DeepPipelineGraphDraftEntity>(url)
                .ConfigureAwait(false);
        }

        public const string DefaultApiVersion = "v1.0";

        private readonly HttpClient _workspaceHttpClient;
        private readonly HttpClient _repositoryHttpClient;

        public ResourceRelayServiceCaller Signing { get; }

        private static class ServiceCallerRoutes
        {
            private static readonly Dictionary<string, string> _workspaceRoutes = new Dictionary<string, string>
            {
                { "CreateAmlModuleAsync", "AzureMLModules" },
                { "GetAmlModuleByIdAsync", "AzureMLModules/{0}" },
                { "GetAmlModuleByNameAsync", "AzureMLModules/Name?name={0}" },
                { "UpdateAmlModuleAsync", "AzureMLModules/{0}" },
                { "UpdateAmlModuleS2SAsync", "AzureMLModules/S2S/{0}" },
                { "ResolveAmlModuleAsync", "AzureMLModules/{0}/resolve" },
                { "GetAmlModulesAsync", "AzureMLModules?activeOnly={0}" },
                { "GetResolvedAmlModulesAsync", "AzureMLModules/BulkResolve?activeOnly={0}" },
                { "ListAmlModuleWithVersionsAsync", "AzureMLModules/Versions?activeOnly={0}&versionsType={1}&removeInterface={2}&orderBy={3}&filter={4}" },
                { "BulkResolveAmlModuleAsync", "AzureMLModules/BulkResolve" },
                { "CreateAmlModuleVersionAsync", "AzureMLModuleVersions" },
                { "GetAmlModuleVersionByIdAsync", "AzureMLModuleVersions/{0}" },
                { "TryGetAzureMLModuleVersionByHashAsync",  "AzureMLModuleVersions/Hash/{0}" },
                { "UpdateAmlModuleVersionAsync", "AzureMLModuleVersions/{0}" },
                { "CreateAE365ModuleVersionAsync", "AE365ModuleVersions" },
                { "GetAE365ModuleVersionByIdAsync", "AE365ModuleVersions/{0}" },
                { "UpdateAE365ModuleVersionAsync", "AE365ModuleVersions/{0}" },
                { "CreateSubGraphModuleVersionAsync", "AzureMLModuleVersions/SubGraph" }
            };

            private static readonly Dictionary<string, string> _repositoryRoutes = new Dictionary<string, string>
            {
                { "CreateAmlModuleAsync", "/type/azuremlmodules" },
                { "GetAmlModuleByIdAsync", "/type/azuremlmodules/objectId/{0}" },
                { "GetAmlModuleByNameAsync", "/type/azuremlmodules/objectName/{0}" },
                { "UpdateAmlModuleAsync", "/type/azuremlmodules/objectId/{0}" },
                { "ResolveAmlModuleAsync", "/type/azuremlmodules/objectId/{0}/resolve" },
                { "GetAmlModulesAsync", "/type/azuremlmodules?activeOnly={0}" },
                { "GetResolvedAmlModulesAsync", "/type/azuremlmodules/bulkresolve?activeOnly={0}" },
                { "ListAmlModuleWithVersionsAsync", "/type/azuremlmodules/versions?activeOnly={0}&versionsType={1}&removeInterface={2}&orderBy={3}&filter={4}" },
                { "BulkResolveAmlModuleAsync", "/type/azuremlmodules/bulkresolve" },
                { "CreateAmlModuleVersionAsync", "/type/azuremlmoduleversions" },
                { "GetAmlModuleVersionByIdAsync", "/type/azuremlmoduleversions/objectId/{0}" },
                { "TryGetAzureMLModuleVersionByHashAsync",  "/type/azuremlmoduleversions/hash/{0}" },
                { "UpdateAmlModuleVersionAsync", "/type/azuremlmoduleversions/objectId/{0}" },
                { "CreateSubGraphModuleVersionAsync", "/type/azuremlmoduleversions/subgraph" }
            };

            public static Dictionary<string, string> WorkspaceRoutes { get; } = _workspaceRoutes;

            public static Dictionary<string, string> RepositoryRoutes { get; } = _repositoryRoutes;
        }
    }
}