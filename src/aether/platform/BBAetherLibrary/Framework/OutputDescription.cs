﻿// <copyright file="OutputDescription.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

namespace Microsoft.Aether.BlueBox.Library
{
    public class OutputDescription
    {
        public OutputDescription(string name, IAetherDataType dataType, string passThroughInputName = null, string description = "")
        {
            Name = name;
            DataType = dataType;
            PassThroughInputName = passThroughInputName;
            Description = description;
        }

        public void SetDescription(string description)
        {
            Description = description;
        }

        public string Name { get; private set; }

        public string Description { get; private set; }

        public IAetherDataType DataType { get; private set; }

        public string PassThroughInputName { get; private set; }
    }
}
