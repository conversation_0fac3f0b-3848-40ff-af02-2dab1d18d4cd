// <copyright file="IModuleSigningRequest.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Threading.Tasks;
using Microsoft.Aether.DataContracts.Signing;

namespace Microsoft.Aether.BlueBox.Library
{
    public interface IModuleSigningRequest
    {
        string ModuleId { get; }

        /// <summary>
        /// Fixed text representation of module entity and signing request data
        /// </summary>
        string Manifest { get; }

        /// <summary>
        /// VSO Drop URL (null for simplified deployment)
        /// </summary>
        string DropUrl { get; }

        bool IsAutoApprovedDeployment { get; }

        bool IsPrivateBinaries { get; }

        /// <summary>
        /// SHA256 hash of local module
        /// </summary>
        string PrivateBinariesHash { get; }

        SigningStatus SigningStatus { get; }

        string AuthorSignatureId { get; }

        string AuthorSignature { get; }

        string ApproverSignatureId { get; }

        string ApproverSignature { get; }

        string ApproverComment { get; }

        Task<string> CreateAuthorSignatureAsync(ISigner signer, string signatureId);

        Task<string> CreateApproverSignatureAsync(ISigner signer, string signatureId);
    }
}