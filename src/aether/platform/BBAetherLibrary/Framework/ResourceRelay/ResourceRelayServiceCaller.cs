// <copyright file="ResourceRelayServiceCaller.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.IO;
using System.Net.Http;
using System.Threading.Tasks;
using Microsoft.Aether.DataContracts.Signing;
using Microsoft.RelInfra.Extensions;

namespace Microsoft.Aether.BlueBox.Library
{
    internal class ResourceRelayServiceCaller
    {
        private readonly HttpClient _httpClient;

        public ResourceRelayServiceCaller(HttpClient httpClient)
        {
            _httpClient = httpClient;
        }

        public async Task<DataContracts.Signing.ModuleSigningRequest> TryGetModuleSigningRequestAsync(string moduleId)
        {
            string path = $"{_httpClient.BaseAddress}ResourceRelay/SigningRequest/Module/{moduleId}";

            return await _httpClient.TryGetEntityAsync<DataContracts.Signing.ModuleSigningRequest>(path).ConfigureAwait(false);
        }

        public async Task<DataContracts.Signing.ModuleSigningRequest> CreateModuleSigningRequestAsync(string moduleId, DataContracts.Signing.ModuleSigningRequestData moduleSigningRequestData)
        {
            string path = $"{_httpClient.BaseAddress}ResourceRelay/SigningRequest/Module/{moduleId}";
            return await _httpClient.PostEntityAsync<DataContracts.Signing.ModuleSigningRequestData, DataContracts.Signing.ModuleSigningRequest>(path, moduleSigningRequestData).ConfigureAwait(false);
        }

        public async Task<string[]> GetAuthorSignatureIdsAsync(string moduleId)
        {
            string path = $"{_httpClient.BaseAddress}ResourceRelay/AuthorSignatureIds/Module/{moduleId}";
            return await _httpClient.GetEntityAsync<string[]>(path).ConfigureAwait(false);
        }

        public async Task<string[]> GetAuthorSignatureIdsAsync()
        {
            string path = $"{_httpClient.BaseAddress}ResourceRelay/AuthorSignatureIds/";
            return await _httpClient.GetEntityAsync<string[]>(path).ConfigureAwait(false);
        }

        public async Task<string[]> GetApproverSignatureIdsAsync(string moduleId, string authorSignatureId)
        {
            string path = $"{_httpClient.BaseAddress}ResourceRelay/ApproverSignatureIds/Module/{moduleId}";
            if (!string.IsNullOrEmpty(authorSignatureId))
            {
                path += $"?authorSignatureId={authorSignatureId}";
            }
            return await _httpClient.GetEntityAsync<string[]>(path).ConfigureAwait(false);
        }

        public async Task SignByAuthorAsync(string moduleId, DataContracts.Signing.SigningInfo signingInfo)
        {
            string path = $"{_httpClient.BaseAddress}ResourceRelay/SignByAuthor/Module/{moduleId}";
            await _httpClient.PostEntityNoReturnAsync(path, signingInfo).ConfigureAwait(false);
        }

        public async Task SignByApproverAsync(string moduleId, DataContracts.Signing.SigningInfo signingInfo)
        {
            string path = $"{_httpClient.BaseAddress}ResourceRelay/SignByApprover/Module/{moduleId}";
            await _httpClient.PostEntityNoReturnAsync(path, signingInfo).ConfigureAwait(false);
        }

        public async Task DenyByApproverAsync(string moduleId, string reason)
        {
            string path = $"{_httpClient.BaseAddress}ResourceRelay/DenyByApprover/Module/{moduleId}";
            await _httpClient.PostEntityNoReturnAsync(path, reason).ConfigureAwait(false);
        }

        public async Task CancelByAuthorAsync(string moduleId, string reason)
        {
            string path = $"{_httpClient.BaseAddress}ResourceRelay/CancelByAuthor/Module/{moduleId}";
            await _httpClient.PostEntityNoReturnAsync(path, reason).ConfigureAwait(false);
        }

        public async Task<string[]> GetAllComplianceClustersAsync()
        {
            string path = $"{_httpClient.BaseAddress}ResourceRelay/ComplianceClusters";
            return await _httpClient.GetEntityAsync<string[]>(path).ConfigureAwait(false);
        }

        public async Task<DataContracts.AutoApproval.ModuleAutoApprovalConfig> GetModuleAutoApprovalConfigAsync()
        {
            string path = $"{_httpClient.BaseAddress}ResourceRelay/ModuleAutoApprovalConfig";
            return await _httpClient.GetEntityAsync<DataContracts.AutoApproval.ModuleAutoApprovalConfig>(path).ConfigureAwait(false);
        }

        public async Task<DataContracts.Signing.ModuleSigningConfig> GetModuleSigningConfigAsync()
        {
            string path = $"{_httpClient.BaseAddress}ResourceRelay/ModuleSigningConfig";
            return await _httpClient.GetEntityAsync<DataContracts.Signing.ModuleSigningConfig>(path).ConfigureAwait(false);
        }

        public async Task UpStreamAsync(string moduleId, Stream zippedModule)
        {
            string path = $"{_httpClient.BaseAddress}ResourceRelay/UpStream/Module/{moduleId}";

            await _httpClient.PostStreamAsync(path, zippedModule).ConfigureAwait(false);
        }

        public async Task<DownloadRequestResult> RequestModuleDownloadAsync(string moduleId)
        {
            string path = $"{_httpClient.BaseAddress}ResourceRelay/RequestDownload/Module/{moduleId}";

            return await _httpClient.GetEntityAsync<DownloadRequestResult>(path).ConfigureAwait(false);
        }

        public async Task<Stream> DownloadModuleAsync(string moduleId)
        {
            string path = $"{_httpClient.BaseAddress}ResourceRelay/Download/Module/{moduleId}";

            return await _httpClient.GetStreamAsync(path).ConfigureAwait(false);
        }
    }
}