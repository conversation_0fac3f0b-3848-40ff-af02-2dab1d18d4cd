// <copyright file="SigningAetherEnvironment.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Threading.Tasks;
using Microsoft.Aether.DataContracts.AutoApproval;
using Microsoft.Aether.DataContracts.Signing;

namespace Microsoft.Aether.BlueBox.Library
{
    public class SigningAetherEnvironment
    {
        private readonly ResourceRelayServiceCaller _resourceRelayServiceCaller;
        private readonly IDstsHttpMessageHandlerProvider _dstsHttpMessageHandlerProvider;

        internal SigningAetherEnvironment(ResourceRelayServiceCaller resourceRelayServiceCaller, IDstsHttpMessageHandlerProvider dstsHttpMessageHandlerProvider)
        {
            _resourceRelayServiceCaller = resourceRelayServiceCaller;
            _dstsHttpMessageHandlerProvider = dstsHttpMessageHandlerProvider;
        }

        private async Task<ISigner> CreateSignerAsync()
        {
            ModuleSigningConfig signingConfig = await _resourceRelayServiceCaller.GetModuleSigningConfigAsync().ConfigureAwait(false);
            return new SigningServiceSigner(signingConfig, _dstsHttpMessageHandlerProvider);
        }

        public virtual async Task<IModuleSigningRequest> GetModuleSigningRequestAsync(string moduleId)
        {
            return await GetModuleSigningRequestAsync(moduleId, throwIfMissing: true).ConfigureAwait(false);
        }

        public virtual async Task<IModuleSigningRequest> TryGetModuleSigningRequestAsync(string moduleId)
        {
            return await GetModuleSigningRequestAsync(moduleId, throwIfMissing: false).ConfigureAwait(false);
        }

        private async Task<IModuleSigningRequest> GetModuleSigningRequestAsync(string moduleId, bool throwIfMissing)
        {
            DataContracts.Signing.ModuleSigningRequest signingRequest =
                await _resourceRelayServiceCaller.TryGetModuleSigningRequestAsync(moduleId).ConfigureAwait(false);

            if (signingRequest == null && throwIfMissing)
            {
                throw new AetherArgumentException($"Request {moduleId} not found");
            }

            return signingRequest == null ? null : new ModuleSigningRequest(signingRequest);
        }

        public virtual async Task<IModuleSigningRequest> CreateModuleSigningRequestAsync(string moduleId, CompliantModuleDeploymentInfo deploymentInfo)
        {
            ModuleSigningRequestData requestData = new ModuleSigningRequestData
            {
                DropUrl = deploymentInfo.IsPrivateBinaries ? null : deploymentInfo.BinariesLocation,
                IsPrivateBinaries = deploymentInfo.IsPrivateBinaries,
                PrivateBinariesHash = deploymentInfo.PrivateBinariesHash,
                IsAutoApprovedDeployment = deploymentInfo.IsAutoApproval
            };

            DataContracts.Signing.ModuleSigningRequest signingRequest =
                await _resourceRelayServiceCaller.CreateModuleSigningRequestAsync(moduleId, requestData).ConfigureAwait(false);

            return new ModuleSigningRequest(signingRequest);
        }

        public virtual async Task<string[]> GetAuthorSignatureIdsAsync(string moduleId)
        {
            return await _resourceRelayServiceCaller.GetAuthorSignatureIdsAsync(moduleId).ConfigureAwait(false);
        }

        public virtual async Task<string[]> GetAuthorSignatureIdsAsync()
        {
            return await _resourceRelayServiceCaller.GetAuthorSignatureIdsAsync().ConfigureAwait(false);
        }

        public virtual async Task<string[]> GetApproverSignatureIdsAsync(string moduleId, string authorSignatureId = null)
        {
            return await _resourceRelayServiceCaller.GetApproverSignatureIdsAsync(moduleId, authorSignatureId).ConfigureAwait(false);
        }

        public virtual async Task SignByAuthorAsync(IModuleSigningRequest request, string signatureId)
        {
            ISigner signer = await CreateSignerAsync().ConfigureAwait(false);
            string signature = await request.CreateAuthorSignatureAsync(signer, signatureId)
                                            .ConfigureAwait(false);

            await SignByAuthorAsync(moduleId: request.ModuleId,
                                    signature: signature,
                                    signatureId: signatureId)
                .ConfigureAwait(false);
        }

        public virtual async Task SignByAuthorAsync(string moduleId, string signature, string signatureId)
        {
            SigningInfo signingInfo = new SigningInfo
            {
                SignatureId = signatureId,
                Signature = signature
            };
            await _resourceRelayServiceCaller.SignByAuthorAsync(moduleId, signingInfo).ConfigureAwait(false);
        }

        public virtual async Task SignByApproverAsync(IModuleSigningRequest request, string signatureId, string approverComment)
        {
            ISigner signer = await CreateSignerAsync().ConfigureAwait(false);
            string signature = await request.CreateApproverSignatureAsync(signer, signatureId)
                                            .ConfigureAwait(false);

            await SignByApproverAsync(moduleId: request.ModuleId,
                                      signature: signature,
                                      signatureId: signatureId,
                                      approverComment: approverComment)
                .ConfigureAwait(false);
        }

        public virtual async Task SignByApproverAsync(
            string moduleId,
            string signature,
            string signatureId,
            string approverComment)
        {
            SigningInfo signingInfo = new SigningInfo
            {
                SignatureId = signatureId,
                Signature = signature,
                Comment = approverComment
            };
            await _resourceRelayServiceCaller.SignByApproverAsync(moduleId, signingInfo).ConfigureAwait(false);
        }

        public virtual async Task DenyByApproverAsync(string moduleId, string reason)
        {
            await _resourceRelayServiceCaller.DenyByApproverAsync(moduleId, reason).ConfigureAwait(false);
        }

        public virtual async Task CancelByAuthorAsync(string moduleId, string reason)
        {
            await _resourceRelayServiceCaller.CancelByAuthorAsync(moduleId, reason).ConfigureAwait(false);
        }

        public async Task<string[]> GetAllComplianceClustersAsync()
        {
            return await _resourceRelayServiceCaller.GetAllComplianceClustersAsync().ConfigureAwait(false);
        }

        public async Task<ModuleAutoApprovalConfig> GetModuleAutoApprovalConfigAsync()
        {
            return await _resourceRelayServiceCaller.GetModuleAutoApprovalConfigAsync().ConfigureAwait(false);
        }

        public async Task RequestModuleDownloadAsync(string moduleId)
        {
            TimeSpan delay = TimeSpan.FromSeconds(5);
            DateTime maxTime = DateTime.Now + TimeSpan.FromHours(2);

            while (maxTime > DateTime.Now)
            {
                DownloadRequestResult result = await _resourceRelayServiceCaller.RequestModuleDownloadAsync(moduleId).ConfigureAwait(false);

                if (result.IsReady)
                {
                    return;
                }

                if (result.HasFailed)
                {
                    throw new InvalidOperationException($"Module {moduleId} download failed");
                }

                delay += delay;
                await Task.Delay(delay).ConfigureAwait(false);
            }

            throw new TimeoutException($"Could't download module {moduleId} within two hours.");
        }

        public virtual async Task<string> SignAutoDeploymentAsync(string requestManifest, string familyId, string signatureId)
        {
            ISigner signer = await CreateSignerAsync().ConfigureAwait(false);

            return await signer.SignManifestAsync(familyId, requestManifest, signatureId).ConfigureAwait(false);
        }
    }
}