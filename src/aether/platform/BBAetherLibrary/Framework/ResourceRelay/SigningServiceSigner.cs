// <copyright file="SigningServiceSigner.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using Microsoft.Aether.DataContracts.Signing;

namespace Microsoft.Aether.BlueBox.Library
{
    internal class SigningServiceSigner : ISigner
    {
        private readonly ModuleSigningConfig _signingConfig;
        private readonly IDstsHttpMessageHandlerProvider _dstsHttpMessageHandlerProvider;

        public SigningServiceSigner(ModuleSigningConfig signingConfig, IDstsHttpMessageHandlerProvider dstsHttpMessageHandlerProvider)
        {
            _signingConfig = signingConfig;
            _dstsHttpMessageHandlerProvider = dstsHttpMessageHandlerProvider;
        }

        public async Task<string> SignManifestAsync(string manifestId,
                                                    string manifest,
                                                    string signatureId,
                                                    ICollection<SigningInfo> existingSignatures = null)
        {
            if (string.IsNullOrEmpty(manifestId))
            {
                throw new ArgumentException("ManifestId shouldn't be empty", nameof(manifestId));
            }
            if (string.IsNullOrEmpty(manifest))
            {
                throw new ArgumentException("Manifest shouldn't be empty", nameof(manifest));
            }
            if (string.IsNullOrEmpty(signatureId))
            {
                throw new ArgumentException("SignatureId shouldn't be empty", nameof(signatureId));
            }

            foreach (SigningInfo signingInfo in existingSignatures ?? new List<SigningInfo>())
            {
                SigningHelper.ValidateSigningInfo(signingInfo, nameof(existingSignatures));
                if (string.Equals(signatureId, signingInfo.SignatureId, StringComparison.OrdinalIgnoreCase))
                {
                    throw new AetherSignException($"SignatureId '{signatureId}' is already present in {nameof(existingSignatures)}");
                }
            }

            return await CallSigningServiceAsync(manifest, signatureId);
        }

        private async Task<string> CallSigningServiceAsync(string manifest, string signatureId)
        {
            try
            {
                string request = $"{_signingConfig.SigningServiceAddress}/signManifest?signatureId={HttpUtility.UrlEncode(signatureId)}";
                using (HttpClient client = new HttpClient(_dstsHttpMessageHandlerProvider.GetHandler(_signingConfig)))
                using (HttpResponseMessage response = await client.PostAsync(request, new StringContent(manifest, Encoding.UTF8))
                                                  .ConfigureAwait(false))
                {
                    response.EnsureSuccessStatusCode();
                    return await response.Content.ReadAsStringAsync().ConfigureAwait(false);
                }
            }
            catch (Exception ex)
            {
                throw new AetherSignException($"Error while signing with {signatureId}.", ex);
            }
        }
    }
}