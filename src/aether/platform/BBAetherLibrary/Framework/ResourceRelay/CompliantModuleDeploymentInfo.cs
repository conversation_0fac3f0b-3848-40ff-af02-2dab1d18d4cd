// <copyright file="CompliantModuleDeploymentInfo.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

namespace Microsoft.Aether.BlueBox.Library
{
    public class CompliantModuleDeploymentInfo
    {
        public string BinariesLocation { get; set; }

        public string PrivateBinariesHash { get; set; }

        public bool IsPrivateBinaries { get; set; }

        public bool IsAutoApproval { get; set; }
    }
}