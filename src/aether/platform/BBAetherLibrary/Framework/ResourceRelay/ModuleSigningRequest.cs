// <copyright file="ModuleSigningRequest.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Threading.Tasks;
using Microsoft.Aether.DataContracts.Signing;

namespace Microsoft.Aether.BlueBox.Library
{
    internal class ModuleSigningRequest : DataContracts.Signing.ModuleSigningRequest, IModuleSigningRequest
    {
        public ModuleSigningRequest(DataContracts.Signing.ModuleSigningRequest request)
        {
            ModuleId = request.ModuleId;
            Manifest = request.Manifest;
            SigningStatus = request.SigningStatus;
            AuthorSigningInfo = request.AuthorSigningInfo;
            ApproverSigningInfo = request.ApproverSigningInfo;
            RequestData = request.RequestData;
        }

        string IModuleSigningRequest.DropUrl => RequestData?.DropUrl;

        public bool IsAutoApprovedDeployment => RequestData?.IsAutoApprovedDeployment == true;

        public bool IsPrivateBinaries => RequestData?.IsPrivateBinaries == true;

        public string PrivateBinariesHash => RequestData?.PrivateBinariesHash;

        public string AuthorSignatureId => AuthorSigningInfo?.SignatureId;

        public string AuthorSignature => AuthorSigningInfo?.Signature;

        public string ApproverSignatureId => ApproverSigningInfo?.SignatureId;

        public string ApproverSignature => ApproverSigningInfo?.Signature;

        public string ApproverComment => ApproverSigningInfo?.Comment;

        public async Task<string> CreateAuthorSignatureAsync(ISigner signer, string signatureId)
        {
            return await signer.SignManifestAsync(ModuleId, Manifest, signatureId)
                .ConfigureAwait(false);
        }

        public async Task<string> CreateApproverSignatureAsync(ISigner signer, string signatureId)
        {
            return await signer.SignManifestAsync(ModuleId, Manifest, signatureId, new[] { AuthorSigningInfo })
                .ConfigureAwait(false);
        }
    }
}