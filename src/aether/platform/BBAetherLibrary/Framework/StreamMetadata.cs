﻿// <copyright file="StreamMetadata.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

namespace Microsoft.Aether.BlueBox.Library
{
    public class StreamMetadata
    {
        private readonly string _name;
        private readonly long _length;

        internal StreamMetadata(string name, long length)
        {
            _name = name;
            _length = length;
        }

        public long Length => _length;

        public string Name => _name;
    }
}
