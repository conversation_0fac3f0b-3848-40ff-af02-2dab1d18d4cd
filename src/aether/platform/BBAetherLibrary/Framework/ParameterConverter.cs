﻿// <copyright file="ParameterConverter.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Globalization;
using System.Linq;
using Microsoft.Aether.AEVA.DataContracts;

namespace Microsoft.Aether.BlueBox.Library
{
    internal static class ParameterConverter
    {
        public static IParameter BuildParameter(Parameter parameter)
        {
            switch (parameter.Type)
            {
                case ParameterType.Bool:
                    if (bool.TryParse(parameter.DefaultValue, out var defaultVal))
                    {
                        return new BoolParameter(parameter.Name,
                                                 parameter.IsOptional,
                                                 defaultVal);
                    }
                    else
                    {
                        return new BoolParameter(parameter.Name,
                                                 parameter.IsOptional);
                    }

                case ParameterType.Double:
                    {
                        List<DoubleParameterRule> rules = parameter.MinMaxRules?.Select(rule => new DoubleParameterRule(rule.Min, rule.Max)).ToList();
                        if (rules == null)
                        {
                            rules = new List<DoubleParameterRule>();
                        }
                        if (!rules.Any())
                        {
                            rules.Add(new DoubleParameterRule(min: null, max: null));
                        }

                        // TODO (alexz) figure out what to do about subgraph parameters ("@@foo@@")
                        double defaultValue;
                        if (double.TryParse(parameter.DefaultValue, NumberStyles.Any, CultureInfo.InvariantCulture, out defaultValue))
                        {
                            return new DoubleParameter(parameter.Name,
                                                     parameter.IsOptional,
                                                     defaultValue,
                                                     rules);
                        }
                        else
                        {
                            return new DoubleParameter(parameter.Name,
                                                     parameter.IsOptional,
                                                     rules);
                        }
                    }

                case ParameterType.Int:
                    {
                        List<IntParameterRule> rules =
                            parameter.MinMaxRules?.Select(rule => new IntParameterRule(
                                min: rule.Min != null ? (int?)Math.Max((double)int.MinValue, rule.Min.Value) : (int?)null,
                                max: rule.Max != null ? (int?)Math.Min((double)int.MaxValue, rule.Max.Value) : (int?)null)).ToList();
                        if (rules == null)
                        {
                            rules = new List<IntParameterRule>();
                        }
                        if (!rules.Any())
                        {
                            rules.Add(new IntParameterRule(min: null, max: null));
                        }

                        // TODO (alexz) figure out what to do about subgraph parameters ("@@foo@@")
                        int defaultValue;
                        if (int.TryParse(parameter.DefaultValue, out defaultValue))
                        {
                            return new IntParameter(parameter.Name,
                                                  parameter.IsOptional,
                                                  defaultValue,
                                                  rules);
                        }
                        else
                        {
                            return new IntParameter(parameter.Name,
                                                  parameter.IsOptional,
                                                  rules);
                        }
                    }

                case ParameterType.String:
                default:
                    return string.IsNullOrEmpty(parameter.DefaultValue)
                       ? new StringParameter(parameter.Name,
                                             parameter.IsOptional,
                                             parameter.EnumRules?.Select(rule => new EnumParameterRule(rule.ValidValues)))
                       : new StringParameter(parameter.Name,
                                             parameter.IsOptional,
                                             parameter.DefaultValue,
                                             parameter.EnumRules?.Select(rule => new EnumParameterRule(rule.ValidValues)));
            }
        }

        public static AssignableParameter BuildAssignableParameter(IParameter parameter)
        {
            if (parameter is StringParameter)
            {
                return new AssignableStringParameter(parameter as StringParameter);
            }

            if (parameter is IntParameter)
            {
                return new AssignableIntParameter(parameter as IntParameter);
            }

            if (parameter is DoubleParameter)
            {
                return new AssignableDoubleParameter(parameter as DoubleParameter);
            }

            if (parameter is BoolParameter)
            {
                return new AssignableBoolParameter(parameter as BoolParameter);
            }

            Debug.Assert(false, "Here default is false.");
            throw new ArgumentException("Parameter is of an unknown type", nameof(parameter));
        }
    }
}
