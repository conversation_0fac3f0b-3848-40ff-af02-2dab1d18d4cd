﻿// <copyright file="Port.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;

namespace Microsoft.Aether.BlueBox.Library
{
    internal abstract class Port
    {
        protected Port(INode node, string name)
        {
            Node = node;
            Name = name;
        }

        public bool HasName
        {
            get
            {
                return !string.IsNullOrEmpty(Name);
            }
        }

        public string Name { get; private set; }

        public INode Node { get; private set; }

        internal static IEnumerable<IAetherDataType> GetLegalTypes(IEnumerable<IEnumerable<IAetherDataType>> allConstraints)
        {
            allConstraints = allConstraints.Where(constraint => constraint.Any());
            if (allConstraints.Count() == 1)
            {
                return allConstraints.Single();
            }

            IEnumerable<IAetherDataType> allTypes = allConstraints.SelectMany(types => types).Distinct();

            List<IAetherDataType> legal = allTypes.Where(type => allConstraints.All(constraintSet =>
                    constraintSet.Any(otherType => type.IsSubTypeOf(otherType.Id) || type.Equals(otherType)))).ToList();

            return legal.Where(type => !legal.Any(other => type.IsSubTypeOf(other.Id)));
        }
    }

    internal class InputPort : Port, IInputPort
    {
        public InputPort(IEnumerable<IAetherDataType> dataTypes, IExecutableNode node, string portName)
            : this(dataTypes, node, portName, false, new List<OutputPort>())
        {
        }

        public InputPort(IEnumerable<IAetherDataType> moduleDataTypes, IExecutableNode node, string portName, bool isOptional, IList<OutputPort> passThroughOutputs, IAssignableParameter parameter = null)
            : base(node, portName)
        {
            IsOptional = isOptional;
            _moduleDataTypes = moduleDataTypes;
            _passThroughOutputs = passThroughOutputs;
            Parameter = parameter;
        }

        #region IInputPort methods

        public IEnumerable<IAetherDataType> DataTypes
        {
            get
            {
                // For pasthrough datatypes, check the outgoing edges to determine the datatypes.
                // If an outgoing edge is connected to a parameter port, skip that edge since parameter ports
                // don't have any datatypes.
                // We are also skipping subgraph nodes which don't have any datatypes, which usually means a parameter port.
                List<OutputPort> connectedPassThroughOutputs = PassThroughOutputs
                    .Where(output => output.OutgoingEdges
                        // TODO:  SuGraph and parameter as input
                        //                       .Any(edge => edge.ToPort.Parameter == null && (!(edge.ToNode is ISubGraphNode) || edge.ToPort.DataTypes.Any()))).ToList();
                        .Any(edge => edge.ToPort.DataTypes.Any())).ToList();
                if (!connectedPassThroughOutputs.Any())
                {
                    return _moduleDataTypes;
                }
                else
                {
                    return GetLegalTypes(connectedPassThroughOutputs.Select(output => output.GetDataTypes()));
                }
            }
        }

        public IEnumerable<IEdge> IncomingEdges
        {
            get
            {
                return _incomingEdges;
            }
        }

        public IEnumerable<INode> IncomingNodes
        {
            get
            {
                return _incomingEdges.Select(edge => edge.FromNode);
            }
        }

        public bool IsOptional { get; set; }

        public IAssignableParameter Parameter { get; set; }

        public bool IsConnected
        {
            get { return _incomingEdges.Count > 0 || _incomingGraphInput != null; }
        }

        public IEnumerable<IAetherGraphInput> IncomingGraphInputs
        {
            get
            {
                if (_incomingGraphInput != null)
                {
                    yield return _incomingGraphInput;
                }
            }
        }

        #endregion

        public void AddIncomingEdge(IEdge edge)
        {
            if (edge.ToPort != this)
            {
                throw new ArgumentException("Incoming edge should have to port equal to this.");
            }

            _incomingEdges.Add(edge);
        }

        public void AddIncomingGraphInput(IAetherGraphInput graphInput)
        {
            _incomingGraphInput = graphInput;
        }

        public void DeleteIncomingGraphInput(IAetherGraphInput graphInput)
        {
            if (!IsConnected || graphInput != _incomingGraphInput)
            {
                throw new InvalidOperationException("Port is not connected to this graph input");
            }
            _incomingGraphInput = null;
        }

        public void DeleteIncomingEdges()
        {
            if (!IsConnected)
            {
                throw new InvalidOperationException("Port is not connected");
            }

            _incomingEdges.Clear();
            _incomingGraphInput = null;
        }

        public void DeleteIncomingEdge(IOutputPort from)
        {
            if (!IsConnected || IncomingEdges.All(port => port.FromPort != from))
            {
                throw new InvalidOperationException("Port is not connected to this port");
            }

            IEdge edgeToRemove = _incomingEdges.First(edge => edge.FromPort == from);
            _incomingEdges.Remove(edgeToRemove);
        }

        internal IList<OutputPort> PassThroughOutputs => _passThroughOutputs;

        //-----------------------------------------------------------------------------------------

        private readonly List<IEdge> _incomingEdges = new List<IEdge>();
        private readonly IEnumerable<IAetherDataType> _moduleDataTypes;
        private readonly IList<OutputPort> _passThroughOutputs;
        private IAetherGraphInput _incomingGraphInput;
    }

    internal class OutputPort : Port, IOutputPort
    {
        public OutputPort(IAetherDataType dataType, INode node, string portName, IInputPort passThroughInput = null)
            : base(node, portName)
        {
            _dataType = dataType;
            _passThroughInput = passThroughInput;
        }

        #region IOutputPort members

        public IInputPort PassThroughInput
        {
            get { return _passThroughInput; }
        }

        public IAetherDataType DataType
        {
            get { return GetDataTypes().First(); }
        }

        public IEnumerable<IEdge> OutgoingEdges
        {
            get { return _connections.SelectMany(inputPort => inputPort.IncomingEdges); }
        }

        public IEnumerable<INode> OutgoingNodes
        {
            get { return _connections.Select(inputPort => inputPort.Node); }
        }

        public bool CanConnectTo(IInputPort toPort)
        {
            if (toPort.IsConnected || this.Node == toPort.Node)
            {
                return false;
            }

            // TODO:  Parameter as input
            // if (toPort.Parameter != null || !toPort.DataTypes.Any())
            if (!toPort.DataTypes.Any())
            {
                return true;
            }

            ConnectionType connectionType = GetConnectionType(toPort);
            return connectionType == ConnectionType.CanConnectWithoutCasting;
        }

        public ConnectionType GetConnectionType(IInputPort toPort)
        {
            bool canConnectWithDownCast = false;

            foreach (var dataType in toPort.DataTypes)
            {
                if (dataType.Id == DataType.Id || DataType.IsSubTypeOf(dataType.Id))
                {
                    return ConnectionType.CanConnectWithoutCasting;
                }

                if (dataType.IsSubTypeOf(DataType.Id))
                {
                    canConnectWithDownCast = true;
                }
            }

            if (_passThroughInput != null &&
                (toPort.DataTypes.Any(toType => _passThroughInput.DataTypes
                    .Any(passType => toType.IsSubTypeOf(passType.Id) || passType.Equals(toType))) ||
                    // TODO:  SubGraph support
                    // _passThroughInput.IncomingGraphInputs.Count() == 1 &&
                    //    _passThroughInput.IncomingGraphInputs.Single().CanConnect(toPort) ||
                    (_passThroughInput.IncomingEdges.Count() == 1 &&
                        _passThroughInput.IncomingEdges.Single().FromPort.CanConnectTo(toPort))))
            {
                return ConnectionType.CanConnectWithoutCasting;
            }

            if (canConnectWithDownCast)
            {
                return ConnectionType.NeedsDownCast;
            }

            return ConnectionType.CannotConnect;
        }

        public bool IsConnected
        {
            get { return _connections.Any() || _connectedGraphOutputs.Any(); }
        }

        public IEnumerable<IAetherGraphOutput> OutgoingGraphOutputs
        {
            get { return _connectedGraphOutputs.Values; }
        }

        public void ConnectTo(IAetherGraphOutput graphOutput)
        {
            if (graphOutput.Source != this)
            {
                graphOutput.Connect(this);
            }

            _connectedGraphOutputs[graphOutput.Name] = graphOutput;

            ResetPassThroughCache();
        }

        public void Disconnect(IAetherGraphOutput graphOutput)
        {
            if (graphOutput.IsConnected)
            {
                graphOutput.Disconnect();
            }

            _connectedGraphOutputs.Remove(graphOutput.Name);

            ResetPassThroughCache();
        }

        #endregion

        public IEdge ConnectTo(InputPort toPort)
        {
            return ConnectTo(toPort, enforcePortTypes: true);
        }

        internal IEdge ConnectTo(InputPort toPort, bool enforcePortTypes)
        {
            if (enforcePortTypes && !CanConnectTo(toPort))
            {
                throw new ArgumentException("Can't connect to this port");
            }

            _connections.Add(toPort);
            IEdge connectingEdge = new Edge(this, toPort);
            toPort.AddIncomingEdge(connectingEdge);

            ResetPassThroughCache();
            return connectingEdge;
        }

        public IEdge ConnectTo(InputPort toPort, IEdge edge)
        {
            if (edge == null || edge.FromPort != this || edge.ToPort != toPort)
            {
                throw new ArgumentException("edge needs to connect the two ports");
            }

            _connections.Add(toPort);
            toPort.AddIncomingEdge(edge);

            ResetPassThroughCache();
            return edge;
        }

        public void Disconnect(InputPort toPort)
        {
            if (!toPort.IsConnected || toPort.IncomingEdges.All(port => port.FromPort != this))
            {
                throw new ArgumentException("Ports are not connected to this port");
            }

            _connections.Remove(toPort);

            toPort.DeleteIncomingEdge(this);

            ResetPassThroughCache();
        }

        internal IEnumerable<IAetherDataType> GetDataTypes()
        {
            if (_passThroughInput == null)
            {
                return new IAetherDataType[] { _dataType };
            }
            else
            {
                try
                {
                    if (_passThroughCache != null)
                    {
                        return _passThroughCache;
                    }

                    bool outputsOnly = _resolvesInProgress != 0;
                    _resolvesInProgress++;
                    IEdge edge = _passThroughInput.IncomingEdges.FirstOrDefault();
                    // TODO:  SubGraph support
                    // IAetherGraphInput graphInput = _passThroughInput.IncomingGraphInputs.FirstOrDefault();
                    List<IAetherDataType> inputConstraints = new List<IAetherDataType>();
                    if (!outputsOnly)
                    {
                        if (edge != null)
                        {
                            inputConstraints.Add(edge.FromPort.DataType);
                        }
                        // TODO:  SubGraph support
                        /*
                        else if (graphInput != null)
                        {
                            inputConstraints.AddRange(GetLegalTypes(graphInput.Consumers.Except(new IInputPort[] { _passThroughInput })
                                .Select(consumers => consumers.DataTypes)));
                        }
                        */
                    }

                    IEnumerable<IAetherDataType> legal =
                        GetLegalTypes(OutgoingEdges.Select(outgoing => outgoing.ToPort.DataTypes)
                        .Concat(new IEnumerable<IAetherDataType>[] { inputConstraints }));

                    _passThroughCache = (legal.Any() ? legal : _passThroughInput.DataTypes).ToList();
                    return _passThroughCache;
                }
                finally
                {
                    _resolvesInProgress--;
                }
            }
        }

        private void ResetPassThroughCache()
        {
            _passThroughCache = null;
        }
        //-----------------------------------------------------------------------------------------

        // In a complex graph with passthrough datatypes, we may end up resolving the same output
        // multiple times from different node paths.  Keep a cache so we avoid recomputation.
        private List<IAetherDataType> _passThroughCache;
        private int _resolvesInProgress = 0;

        private readonly List<IInputPort> _connections = new List<IInputPort>();
        private readonly IDictionary<string, IAetherGraphOutput> _connectedGraphOutputs = new Dictionary<string, IAetherGraphOutput>();
        private readonly IInputPort _passThroughInput;
        private readonly IAetherDataType _dataType;
    }
}
