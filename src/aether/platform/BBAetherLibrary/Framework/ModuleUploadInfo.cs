﻿// <copyright file="ModuleUploadInfo.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using Microsoft.Aether.AEVA.DataContracts;

namespace Microsoft.Aether.BlueBox.Library
{
    public class ModuleUploadInfo
    {
        public ModuleUploadInfo(
            string name,
            string displayName,
            string description,
            bool isDeterministic,
            string cloudSystem,
            StructuredInterface structuredInterface = null,
            Guid? snapshotId = null,
            AE365Properties ae365Properties = null,
            ModuleType? moduleType = null,
            string category = null,
            string runconfig = null,
            string stepType = null,
            IDictionary<string, string> kvTags = null,
            IDictionary<string, string> properties = null,
            CloudSettings cloudSettings = null,
            string moduleTypeVersion = null)
        {
            Name = name;
            DisplayName = displayName;
            Description = description;
            IsDeterministic = isDeterministic;
            CloudSystem = cloudSystem;
            StructuredInterface = structuredInterface;
            SnapshotId = snapshotId;
            AE365Properties = ae365Properties;
            ModuleType = moduleType;
            ModuleTypeVersion = moduleTypeVersion;
            KvTags = kvTags;
            Properties = properties;
            Category = category;
            Runconfig = runconfig;
            StepType = stepType;
            CloudSettings = cloudSettings;
        }

        public string Name { get; private set; }

        public string DisplayName { get; private set; }

        public string Description { get; private set; }

        public bool IsDeterministic { get; private set; }

        public string CloudSystem { get; private set; }

        public StructuredInterface StructuredInterface { get; private set; }

        public Guid? SnapshotId { get; private set; }

        public AE365Properties AE365Properties { get; private set; }

        public ModuleType? ModuleType { get; private set; }

        public string ModuleTypeVersion { get; private set; }

        public IDictionary<string, string> KvTags { get; private set; }

        public IDictionary<string, string> Properties { get; private set; }

        public string Category { get; private set; }

        /// <summary>
        /// Runconfig definition serialized as JSON.  This is a default runconfig to apply to instances of the module,
        /// which can be overridden on a per-node basis
        /// </summary>
        public string Runconfig { get; set; }

        /// <summary>
        /// Type of step that generated this module (e.g. PythonScriptStep, ModuleStep, etc.)
        /// </summary>
        public string StepType { get; set; }

        /// <summary>
        /// CloudSettings is the cloud specific settings
        /// </summary>
        public CloudSettings CloudSettings { get; set; }
    }
}
