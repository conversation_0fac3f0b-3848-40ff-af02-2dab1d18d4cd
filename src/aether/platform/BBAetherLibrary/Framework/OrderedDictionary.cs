﻿// <copyright file="OrderedDictionary.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Collections.Generic;
using System.Linq;
using System.Numerics;

namespace Microsoft.Aether.BlueBox.Library
{
    /// <summary>
    /// A dictionary that preserves the insertion order of its elements
    /// </summary>
    /// <remarks>
    /// This class is NOT thread safe
    /// </remarks>
    internal class OrderedDictionary<TKey, TValue> : IDictionary<TKey, TValue>, IReadOnlyDictionary<TKey, TValue>
    {
        public void Add(TKey key, TValue value)
        {
            Add(new KeyValuePair<TKey, TValue>(key, value));
        }

        public bool ContainsKey(TKey key)
        {
            return _keyToIndex.ContainsKey(key);
        }

        public ICollection<TKey> Keys => _keyToIndex.Keys;

        public virtual bool Remove(TKey key)
        {
            BigInteger index;
            if (_keyToIndex.TryGetValue(key, out index))
            {
                _keyToIndex.Remove(key);
                _indexMap.Remove(index);
                return true;
            }
            else
            {
                return false;
            }
        }

        public bool TryGetValue(TKey key, out TValue value)
        {
            BigInteger index;
            if (_keyToIndex.TryGetValue(key, out index))
            {
                value = _indexMap[index].Value;
                return true;
            }
            else
            {
                value = default(TValue);
                return false;
            }
        }

        public ICollection<TValue> Values
        {
            get { return _indexMap.Values.Select(pair => pair.Value).ToList(); }
        }

        public TValue this[TKey key]
        {
            get
            {
                return _indexMap[_keyToIndex[key]].Value;
            }

            set
            {
                BigInteger index;
                if (_keyToIndex.TryGetValue(key, out index))
                {
                    _indexMap[index] = new KeyValuePair<TKey, TValue>(key, value);
                }
                else
                {
                    Add(key, value);
                }
            }
        }

        public void Add(KeyValuePair<TKey, TValue> item)
        {
            _keyToIndex.Add(item.Key, currentIndex);
            _indexMap.Add(currentIndex++, item);
        }

        public virtual void Clear()
        {
            _keyToIndex.Clear();
            _indexMap.Clear();
        }

        public bool Contains(KeyValuePair<TKey, TValue> item)
        {
            BigInteger index;
            if (_keyToIndex.TryGetValue(item.Key, out index))
            {
                return _indexMap[index].Equals(item);
            }
            else
            {
                return false;
            }
        }

        public void CopyTo(KeyValuePair<TKey, TValue>[] array, int arrayIndex)
        {
            _indexMap.Values.CopyTo(array, arrayIndex);
        }

        public int Count
        {
            get { return _keyToIndex.Count; }
        }

        public bool IsReadOnly
        {
            get { return false; }
        }

        public virtual bool Remove(KeyValuePair<TKey, TValue> item)
        {
            BigInteger index = _keyToIndex[item.Key];
            if (_indexMap[index].Equals(item))
            {
                _keyToIndex.Remove(item.Key);
                _indexMap.Remove(index);
                return true;
            }
            else
            {
                return false;
            }
        }

        public IEnumerator<KeyValuePair<TKey, TValue>> GetEnumerator()
        {
            return _indexMap.Values.GetEnumerator();
        }

        IEnumerable<TKey> IReadOnlyDictionary<TKey, TValue>.Keys
        {
            get { return Keys; }
        }

        IEnumerable<TValue> IReadOnlyDictionary<TKey, TValue>.Values
        {
            get { return Values; }
        }

        System.Collections.IEnumerator System.Collections.IEnumerable.GetEnumerator()
        {
            return GetEnumerator();
        }

        protected readonly Dictionary<TKey, BigInteger> _keyToIndex = new Dictionary<TKey, BigInteger>();
        protected readonly SortedDictionary<BigInteger, KeyValuePair<TKey, TValue>> _indexMap =
            new SortedDictionary<BigInteger, KeyValuePair<TKey, TValue>>();

        protected BigInteger currentIndex = 0;
    }
}
