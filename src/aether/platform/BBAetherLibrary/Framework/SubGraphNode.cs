﻿// <copyright file="SubGraphNode.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Collections.Generic;
using System.Linq;
using Microsoft.Aether.AEVA.DataContracts;

namespace Microsoft.Aether.BlueBox.Library
{
    internal class SubGraphNode : ExecutableNode, ISubGraphNode
    {
        internal static SubGraphNode BuildSubGraphNode(ISubGraph subGraph, string id)
        {
            return new SubGraphNode(
                BuildParameterDictionary(subGraph.Interface.Parameters),
                BuildParameterDictionary(subGraph.Interface.MetadataParameters),
                Enumerable.Empty<InputSetting>(),
                Enumerable.Empty<OutputSetting>(),
                subGraph,
                id);
        }

        internal SubGraphNode(IReadOnlyDictionary<string, IAssignableParameter> parameters,
                              IReadOnlyDictionary<string, IAssignableParameter> metaDataParameters,
                              IEnumerable<InputSetting> moduleInputSettings,
                              IEnumerable<OutputSetting> moduleOutputSettings,
                              ISubGraph subGraph,
                              string id)
            : base(
                  parameters,
                  metaDataParameters,
                  BuildInputSettingDictionary(moduleInputSettings),
                  BuildOutputSettingDictionary(moduleOutputSettings))
        {
            Id = id;
            SubGraph = subGraph;
            Name = SubGraph.Name;
            InitializePortDictionaries(subGraph.Interface);
        }

        public ISubGraph SubGraph
        {
            get;
            private set;
        }

        public override IResource Resource
        {
            get { return SubGraph; }
        }

        public IGraphResource GraphResource
        {
            get
            {
                return SubGraph;
            }
        }

        public ComputeSetting DefaultCompute { get; set; }

        public DatastoreSetting DefaultDatastore { get; set; }
    }
}
