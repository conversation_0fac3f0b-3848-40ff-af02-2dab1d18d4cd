﻿// <copyright file="SnapshotUploader.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Aether.BlueBox.SnapshotContracts;
using Microsoft.MachineLearning.Project.Contracts;
using Microsoft.RelInfra.Instrumentation.Logging;

namespace Microsoft.Aether.BlueBox.Library
{
    internal class SnapshotUploader : ISnapshotUploader
    {
        private readonly HttpClient _httpClient;

        public SnapshotUploader(Uri serverUriSnapshot, AuthHandler authHandler, string userAgent, TimeSpan? httpClientTimeout = null, string clientRequestId = null)
        {
            _httpClient = new HttpClient(authHandler)
            {
                BaseAddress = serverUriSnapshot,
            };

            if (httpClientTimeout != null)
            {
                _httpClient.Timeout = httpClientTimeout.Value;
            }

            if (!string.IsNullOrWhiteSpace(userAgent))
            {
                if (!_httpClient.DefaultRequestHeaders.UserAgent.TryParseAdd(userAgent))
                {
                    throw new ArgumentException($"Could not parse the given user agent string: {userAgent}");
                }
            }

            if (!string.IsNullOrWhiteSpace(clientRequestId))
            {
                _httpClient.DefaultRequestHeaders.Add(HttpClientExtensions.RequestIdHeader, clientRequestId);
            }
        }

        public async Task UploadToSnapshotAsync(string snapshotId, string sourcePath, bool isDirectory, IAetherBBSnapshotClient aetherBBSnapshotClient,
            string subscriptionId, string resourceGroupName, string workspaceName, CancellationToken cancellationToken)
        {
            if (isDirectory)
            {
                if (!sourcePath.EndsWith(Path.DirectorySeparatorChar))
                {
                    sourcePath += Path.DirectorySeparatorChar;
                }
                var path = Path.GetFullPath(sourcePath);
                var files = Directory.GetFiles(path, "*", SearchOption.AllDirectories);

                if (!files.Any())
                {
                    throw new ArgumentException($"The directory you are trying to upload is empty {sourcePath}");
                }

                await UploadFileAsync(aetherBBSnapshotClient: aetherBBSnapshotClient, snapshotId: snapshotId, filePath: path,
                    subscriptionId: subscriptionId, resourceGroupName: resourceGroupName, workspaceName: workspaceName, cancellationToken: cancellationToken);
            }
            else
            {
                if (!cancellationToken.IsCancellationRequested)
                {
                    await UploadFileAsync(aetherBBSnapshotClient: aetherBBSnapshotClient, snapshotId: snapshotId, filePath: sourcePath,
                        subscriptionId: subscriptionId, resourceGroupName: resourceGroupName, workspaceName: workspaceName, cancellationToken: cancellationToken);
                }
                else
                {
                    throw new OperationCanceledException();
                }
            }
        }

        public async Task<Uri> GetSnapshotFilesZipSasAsync(IAetherBBSnapshotClient aetherBBSnapshotClient, string snapshotId, string subscriptionId,
            string resourceGroupName, string workspaceName)
        {
            var snapshotZipResponse = await aetherBBSnapshotClient.BuildSnapshotZipAsync(_httpClient, snapshotId, subscriptionId, resourceGroupName, workspaceName, CancellationToken.None).ConfigureAwait(false);
            return snapshotZipResponse.ZipSasUri;
        }

        private async Task UploadFileAsync(IAetherBBSnapshotClient aetherBBSnapshotClient, string snapshotId, string filePath,
            string subscriptionId, string resourceGroupName, string workspaceName, CancellationToken cancellationToken)
        {
            var fileContents = SnapshotHelper.BuildFileContents(filePath);
            var dirTreeNode = SnapshotHelper.BuildMerkleTree(filePath);

            var filePathsAndContent = new Dictionary<string, ByteArrayContent>();
            var fileNames = new List<string>();

            long maxFileSize = 0;
            foreach (string fileName in fileContents.Keys)
            {
                fileNames.Add(fileName);
                filePathsAndContent[fileName] = fileContents[fileName];
                var contentLength = fileContents[fileName].Headers.ContentLength;
                maxFileSize = Math.Max(maxFileSize, contentLength != null ? contentLength.Value : 0);
            }

            var fileNameListDto = new FileNameListDto()
            {
                FileNames = fileNames
            };

            // Get a fileNodeList which contains a blob URI for each file where the contents can be uploaded
            var fileNodeList = await aetherBBSnapshotClient.GetBlobUrisAsync(
                httpClient: _httpClient,
                subscriptionId: subscriptionId,
                resourceGroupName: resourceGroupName,
                workspaceName: workspaceName,
                fileList: fileNameListDto,
                cancellationToken: cancellationToken).ConfigureAwait(false);

            // fill allocated blobs with content information - uses SAS flow as Snapshot is only uploaded in BBAetherLibrary for integration tests (no private link)
            int retryCountForBlobUpload = 3;
            await SnapshotHelper.UploadBlobUrisAsync(fileNodeList, filePathsAndContent, cancellationToken, maxFileSize, retryCountForBlobUpload).ConfigureAwait(false);
            CommonLogger.LogInfo($"Blobs successfully uploaded in SnapshotUploader");

            // Create the DTO needed for the CreateSnapshot call
            var createSnapshotDto = new CreateSnapshotDto
            {
                ParentSnapshotId = Guid.Empty,
                Tags = null,
                Properties = null,
                DirTreeNode = dirTreeNode,
                FileRevisionList = fileNodeList
            };

            await aetherBBSnapshotClient.CreateSnapshotAlreadyUploadAsync(
                httpClient: _httpClient,
                snapshotId: snapshotId,
                subscriptionId: subscriptionId,
                resourceGroupName: resourceGroupName,
                workspaceName: workspaceName,
                createDto: createSnapshotDto,
                cancellationToken: cancellationToken);

            CommonLogger.LogInfo($"Successfully created snapshot in SnapshotUploader");
        }
    }
}
