﻿// <copyright file="OrderedInsertionDictionary.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Numerics;

namespace Microsoft.Aether.BlueBox.Library
{
    /// <summary>
    /// A variation of OrderedDictionary that allows for items to be inserted.
    /// It's separate from OrderedDictionary, because removals are O(n) instead
    /// of O(1), and this trade-off isn't neccessary in most cases.
    /// </summary>
    /// <remarks>
    /// This class is NOT thread safe
    /// </remarks>
    internal class OrderedInsertionDictionary<TKey, TValue> : OrderedDictionary<TKey, TValue>
    {
        public void Insert(long index, TKey key, TValue value)
        {
            Insert(index, new KeyValuePair<TKey, TValue>(key, value));
        }

        public void Insert(long index, KeyValuePair<TKey, TValue> item)
        {
            if (index < 0 || index > currentIndex)
            {
                throw new ArgumentOutOfRangeException(nameof(index));
            }

            _keyToIndex.Add(item.Key, index);
            KeyValuePair<TKey, TValue> prevItem = item;
            for (BigInteger i = index; i < currentIndex; i++)
            {
                KeyValuePair<TKey, TValue> cursor = _indexMap[i];
                _indexMap[i] = prevItem;
                _keyToIndex[prevItem.Key] = i;
                prevItem = cursor;
            }
            _keyToIndex[prevItem.Key] = currentIndex;
            _indexMap.Add(currentIndex++, prevItem);
        }

        public long IndexOf(TKey key)
        {
            BigInteger index;
            if (_keyToIndex.TryGetValue(key, out index))
            {
                return (long)index;
            }
            else
            {
                return -1;
            }
        }

        private bool Remove(TKey key, out BigInteger index)
        {
            if (_keyToIndex.TryGetValue(key, out index))
            {
                _keyToIndex.Remove(key);
                _indexMap.Remove(index);

                for (BigInteger i = index + 1; i < currentIndex; i++)
                {
                    _indexMap.Add(i - 1, _indexMap[i]);
                    _keyToIndex[_indexMap[i].Key] = i - 1;
                    _indexMap.Remove(i);
                }

                currentIndex--;

                return true;
            }
            else
            {
                return false;
            }
        }

        private bool Remove(KeyValuePair<TKey, TValue> item, out BigInteger index)
        {
            if (_keyToIndex.TryGetValue(item.Key, out index) && _indexMap[index].Equals(item))
            {
                return Remove(item.Key, out index);
            }
            else
            {
                return false;
            }
        }

        public bool Remove(TKey key, out long index)
        {
            BigInteger biIndex;
            bool result = Remove(key, out biIndex);
            index = (long)biIndex;
            return result;
        }

        public bool Remove(KeyValuePair<TKey, TValue> item, out long index)
        {
            BigInteger biIndex;
            bool result = Remove(item, out biIndex);
            index = (long)biIndex;
            return result;
        }

        public override bool Remove(TKey key)
        {
            BigInteger index;
            return Remove(key, out index);
        }

        public override bool Remove(KeyValuePair<TKey, TValue> item)
        {
            BigInteger index;
            return Remove(item, out index);
        }

        public override void Clear()
        {
            base.Clear();
            currentIndex = 0;
        }
    }
}
