﻿// <copyright file="ParameterRules.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;

namespace Microsoft.Aether.BlueBox.Library
{
    public class DoubleParameterRule : IParameterRule<double>
    {
        public DoubleParameterRule(double? min, double? max)
        {
            Min = min;
            Max = max;
        }

        public double? Min { get; private set; }

        public double? Max { get; private set; }

        #region IParameterRule Members

        public bool CheckIsConcreteValueValid(double value, out string details)
        {
            bool isValueFitting = (!Min.HasValue || Min <= value) && (!Max.HasValue || value <= Max);
            if (!isValueFitting)
            {
                details = "Out of range.";
                return false;
            }

            details = string.Empty;
            return true;
        }

        public bool CheckIsValueValid(string valueString, out string details)
        {
            double parameterValue;
            bool canBeParsed = double.TryParse(valueString, NumberStyles.Any, CultureInfo.InvariantCulture, out parameterValue);

            if (!canBeParsed)
            {
                details = "not a double";
                return false;
            }

            return CheckIsConcreteValueValid(parameterValue, out details);
        }

        public string GetRuleExplanation()
        {
            return string.Format(CultureInfo.InvariantCulture, "double [{0}..{1}]",
                !Min.HasValue || double.IsNaN(Min.Value) ? string.Empty : Min.Value.ToString(CultureInfo.InvariantCulture),
                !Max.HasValue || double.IsNaN(Max.Value) ? string.Empty : Max.Value.ToString(CultureInfo.InvariantCulture));
        }

        #endregion
    }

    public class IntParameterRule : IParameterRule<int>
    {
        public IntParameterRule(int? min, int? max)
        {
            Min = min;
            Max = max;
        }

        public int? Min { get; private set; }

        public int? Max { get; private set; }

        #region IParameterRule Members

        public bool CheckIsConcreteValueValid(int value, out string details)
        {
            bool isValueFitting = (!Min.HasValue || Min <= value) && (!Max.HasValue || value <= Max);
            if (!isValueFitting)
            {
                details = "Out of range.";
                return false;
            }

            details = string.Empty;
            return true;
        }

        public bool CheckIsValueValid(string valueString, out string details)
        {
            int parameterValue;
            bool canBeParsed = int.TryParse(valueString, out parameterValue);
            if (!canBeParsed)
            {
                details = "not an int";
                return false;
            }

            return CheckIsConcreteValueValid(parameterValue, out details);
        }

        public string GetRuleExplanation()
        {
            return string.Format(CultureInfo.InvariantCulture, "int [{0}..{1}]",
                !Min.HasValue || double.IsNaN(Min.Value) ? string.Empty : Min.Value.ToString(CultureInfo.InvariantCulture),
                !Max.HasValue || double.IsNaN(Max.Value) ? string.Empty : Max.Value.ToString(CultureInfo.InvariantCulture));
        }

        #endregion
    }

    public class EnumParameterRule : IParameterRule<string>
    {
        public EnumParameterRule(IEnumerable<string> values)
            : this(values.ToArray(), new HashSet<string>(values.Select(Normalize), StringComparer.InvariantCultureIgnoreCase))
        {
            LegalValues = values.ToArray();
        }

        public string[] LegalValues { get; private set; }

        #region IParameterRule Members

        public bool CheckIsConcreteValueValid(string value, out string details)
        {
            if (value != null && _normalizedValues.Contains(Normalize(value)))
            {
                details = string.Empty;
                return true;
            }
            else
            {
                details = string.Format("Not one of legal values. [{0}]", GetRuleExplanation());
                return false;
            }
        }

        public bool CheckIsValueValid(string value, out string details)
        {
            return CheckIsConcreteValueValid(value, out details);
        }

        public string GetRuleExplanation()
        {
            return string.Join(", ", LegalValues);
        }

        #endregion

        internal EnumParameterRule(string[] legalValues, HashSet<string> normalizedValues)
        {
            LegalValues = legalValues;
            _normalizedValues = normalizedValues;
        }

        private static string Normalize(string value)
        {
            return value.Trim();
        }

        private readonly HashSet<string> _normalizedValues;
    }
}
