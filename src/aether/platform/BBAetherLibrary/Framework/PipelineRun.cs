﻿// <copyright file="PipelineRun.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Runtime.Serialization;
using System.Threading.Tasks;
using Aether.ContractProcessing;
using Microsoft.Aether.AEVA.DataContracts;

namespace Microsoft.Aether.BlueBox.Library
{
    [DataContract]
    internal class PipelineRun : PipelineRunEntity, IPipelineRun
    {
        public PipelineRunStatusCode CachedStatus
        {
            get { return Status?.StatusCode ?? PipelineRunStatusCode.NotStarted; }
        }

        public DateTime? DateStart
        {
            get
            {
                return Status.CreationTime == CreatedDate ?
              null : new DateTime?(Status.CreationTime);
            }
        }

        public DateTime? DateEnd
        {
            get { return Status.EndTime; }
        }

        public string ResourceTypeName
        {
            get { return "PipelineRun"; }
        }

        public async Task CancelAsync()
        {
            await _environment.ServiceWrapper.CancelPipelineRunAsync(Id).ConfigureAwait(false);
        }

        public IExecutionInterface Interface
        {
            get { return _graphInterface; }
        }

        public async Task<IGraph> GetGraphAsync(bool enforcePortTypes = false)
        {
            return await GetExecutionGraphAsync(enforcePortTypes: enforcePortTypes).ConfigureAwait(false);
        }

        // TODO:  Clone support
        /*
        public async Task<IAetherExperimentDraft> CloneAsync()
        {
            return await _environment.CloneExperimentAsync(this).ConfigureAwait(false);
        }
        */

        public async Task SaveAsync()
        {
            if (!IsSubmitted)
            {
                Cloner.WriteInto(from: await _environment.ServiceWrapper.UpdatePipelineRunAsync(Id, this).ConfigureAwait(false), to: this);
                _originalEntity = Cloner.DeepCopy<PipelineRunEntity>(this);
            }
            else
            {
                throw new AetherInvalidOperationException("A pipeline run cannot be modified after it is submitted for execution");
            }
        }

        public async Task SavePropertiesAsync(IDictionary<string, string> newProperties)
        {
            this.Properties = newProperties;
            Cloner.WriteInto(from: await _environment.ServiceWrapper.UpdatePipelineRunAsync(Id, this).ConfigureAwait(false), to: this);
            _originalEntity = Cloner.DeepCopy<PipelineRunEntity>(this);
        }

        public async Task SaveTagsAsync(IDictionary<string, string> newTags)
        {
            this.KvTags = newTags;
            Cloner.WriteInto(from: await _environment.ServiceWrapper.UpdatePipelineRunAsync(Id, this).ConfigureAwait(false), to: this);
            _originalEntity = Cloner.DeepCopy<PipelineRunEntity>(this);
        }

        public async Task<IExecutionGraph> GetExecutionGraphAsync(bool enforcePortTypes = false)
        {
            return await GetGraphHelperAsync(enforcePortTypes: enforcePortTypes).ConfigureAwait(false);
        }

        public async Task<PipelineRunStatusCode> GetStatusAsync()
        {
            await RefreshMetadataAsync().ConfigureAwait(false);
            return Status?.StatusCode ?? PipelineRunStatusCode.NotStarted;
        }

        public async Task RefreshMetadataAsync()
        {
            PipelineRunEntity data = await _environment.ServiceWrapper.GetPipelineRunAsync(Id).ConfigureAwait(false);
            _originalEntity = Cloner.DeepCopy(data);
            Cloner.WriteInto<PipelineRunEntity>(data, this);
        }

        internal static IPipelineRun BuildPipelineRun(PipelineRunEntity data,
            AetherEnvironment environment, IExecutionInterface graphInterface)
        {
            var pipelineRun = new PipelineRun(environment, Cloner.DeepCopy(data), graphInterface);
            Cloner.WriteInto(data, pipelineRun);
            return pipelineRun;
        }

        internal PipelineRun(AetherEnvironment environment, PipelineRunEntity originalEntity, IExecutionInterface graphInterface)
        {
            _environment = environment;
            _originalEntity = originalEntity;
            _graphInterface = graphInterface;
        }

        private async Task<IExecutionGraph> GetGraphHelperAsync(bool enforcePortTypes = true)
        {
            IExecutionGraph graph = await _environment.ConstructGraphAsync(Id, GraphId, ParameterAssignments, enforcePortTypes: enforcePortTypes).ConfigureAwait(false);

            return graph;
        }

        private readonly AetherEnvironment _environment;
        private readonly IExecutionInterface _graphInterface;
        private PipelineRunEntity _originalEntity;
    }
}
