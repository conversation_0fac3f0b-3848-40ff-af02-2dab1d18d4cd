// <copyright file="OutputControlPort.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Linq;

namespace Microsoft.Aether.BlueBox.Library
{
    internal sealed class OutputControlPort : IOutputControlPort
    {
        public OutputControlPort(IModuleNode moduleNode, string controlOutputName)
        {
            ModuleNode = moduleNode ?? throw new ArgumentException(message: "Module node can't be null", paramName: nameof(moduleNode));
            ControlOutputName = controlOutputName ?? throw new ArgumentException(message: "Control output name can't be null", paramName: nameof(controlOutputName));

            if (!moduleNode.Module.StructuredInterface.ControlOutputs.Any(output =>
                string.Equals(output.Name, controlOutputName, StringComparison.Ordinal)))
            {
                throw new ArgumentException($"Module node {moduleNode.Id} doesn't have a control output with name {controlOutputName}");
            }
        }

        public IModuleNode ModuleNode { get; }

        public string ControlOutputName { get; }

        public override int GetHashCode()
        {
            return (ModuleNode.Id.GetHashCode() * 7) + ControlOutputName.GetHashCode();
        }

        public override bool Equals(object obj)
        {
            switch (obj)
            {
                case null:
                    return false;
                case IOutputControlPort other:
                    return string.Equals(ModuleNode.Id, other.ModuleNode.Id, StringComparison.Ordinal)
                           && string.Equals(ControlOutputName, other.ControlOutputName, StringComparison.Ordinal);
                default:
                    return false;
            }
        }
    }
}