﻿// <copyright file="NodeExecution.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Microsoft.Aether.AEVA.DataContracts;

namespace Microsoft.Aether.BlueBox.Library
{
    internal class NodeExecution : INodeExecution
    {
        public IList<string> NodeIdPath => _nodeIdPath;

        private static async Task<string> FetchStringInChunksAsync(StreamReader reader)
        {
            int bufferSize = 1024 * 1024; // 1MB
            StringBuilder fetchedStrBuilder = new StringBuilder();
            var buffer = new char[bufferSize];
            try
            {
                int numCharsRead;
                while ((numCharsRead = await reader.ReadAsync(buffer, 0, buffer.Length).ConfigureAwait(false)) > 0)
                {
                    fetchedStrBuilder.Append(buffer, 0, numCharsRead);
                }
            }
            catch (Exception e)
            {
                // If there is an exception return whatever has been fetched till now.
                if (fetchedStrBuilder.Length > 0)
                {
                    fetchedStrBuilder.Append($"<Truncated due to exception>: {e}");
                }
                else
                {
                    throw;
                }
            }

            return fetchedStrBuilder.ToString();
        }

        public async Task<string> GetStdOutAsync()
        {
            using (StreamReader reader = new StreamReader(await _service.GetNodeShareableStdoutAsync(_pipelineRunId, _nodeIdPath)
                .ConfigureAwait(false)))
            {
                return await FetchStringInChunksAsync(reader);
            }
        }

        public async Task<string> GetStdErrAsync()
        {
            using (StreamReader reader = new StreamReader(await _service.GetNodeShareableStderrAsync(_pipelineRunId, _nodeIdPath)
                .ConfigureAwait(false)))
            {
                return await FetchStringInChunksAsync(reader);
            }
        }

        public async Task<string> GetJobLogAsync()
        {
            using (StreamReader reader = new StreamReader(await _service.GetNodeJobLogAsync(_pipelineRunId, _nodeIdPath)
                .ConfigureAwait(false)))
            {
                return await FetchStringInChunksAsync(reader);
            }
        }

        public string GetJobLog()
        {
            return GetJobLogAsync().Result;
        }

        public async Task<TaskStatusCode> GetExecutionStatusAsync()
        {
            return await _service.GetNodeStatusCodeAsync(_pipelineRunId, _nodeIdPath).ConfigureAwait(false);
        }

        public IAetherNodeOutput GetOutput(string outputName)
        {
            NodeOutput output;

            if (!_outputs.TryGetValue(outputName, out output))
            {
                throw new ArgumentException(string.Format("{0} is not the name of an output port for this node",
                                                          outputName));
            }
            else
            {
                return AetherNodeOutput.BuildModuleOutput(
                    _aetherEnvironment,
                    _dataTypes[output.DataTypeId],
                    _service,
                    _storage,
                    output);
            }
        }

        internal NodeExecution(AetherEnvironment aetherEnvironment,
                                string pipelineRunId,
                                IList<string> nodeIdPath,
                                IDictionary<string, NodeOutput> outputs,
                                DataTypeCollection dataTypes,
                                IServiceCaller service,
                                IStorage storage)
        {
            _aetherEnvironment = aetherEnvironment;
            _pipelineRunId = pipelineRunId;
            _nodeIdPath = nodeIdPath.ToList();
            _outputs = outputs;
            _dataTypes = dataTypes;
            _service = service;
            _storage = storage;
        }

        private readonly AetherEnvironment _aetherEnvironment;
        private readonly string _pipelineRunId;
        private readonly IList<string> _nodeIdPath;
        private readonly IDictionary<string, NodeOutput> _outputs;
        private readonly DataTypeCollection _dataTypes;
        private readonly IServiceCaller _service;
        private readonly IStorage _storage;
    }
}
