﻿// <copyright file="NodeBase.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Collections.Generic;
using System.Linq;

namespace Microsoft.Aether.BlueBox.Library
{
    internal class NodeBase
    {
        public string Id { get; internal set; }

        public string Name { get; internal set; }

        public IReadOnlyDictionary<string, IInputPort> InputPortDictionary => Inputs;

        public IReadOnlyDictionary<string, IOutputPort> OutputPortDictionary => Outputs;

        public IEnumerable<INode> IncomingNodes
        {
            get { return Inputs.Values.SelectMany(inputPort => inputPort.IncomingNodes); }
        }

        public IEnumerable<INode> OutgoingNodes
        {
            get { return Outputs.Values.SelectMany(outputPort => outputPort.OutgoingNodes); }
        }

        public IEnumerable<IEdge> IncomingEdges
        {
            get { return Inputs.Values.SelectMany(inputPort => inputPort.IncomingEdges); }
        }

        public IEnumerable<IEdge> OutgoingEdges
        {
            get { return Outputs.Values.SelectMany(outputPort => outputPort.OutgoingEdges); }
        }

        public IEnumerable<IInputPort> InputPorts => Inputs.Values;

        public IEnumerable<IOutputPort> OutputPorts => Outputs.Values;

        protected readonly OrderedDictionary<string, IInputPort> Inputs = new OrderedDictionary<string, IInputPort>();

        protected readonly OrderedDictionary<string, IOutputPort> Outputs = new OrderedDictionary<string, IOutputPort>();
    }
}
