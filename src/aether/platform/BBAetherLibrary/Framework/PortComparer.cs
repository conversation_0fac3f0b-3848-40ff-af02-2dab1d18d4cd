﻿// <copyright file="PortComparer.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Collections.Generic;

namespace Microsoft.Aether.BlueBox.Library
{
    internal class PortComparer : IEqualityComparer<IPort>
    {
        public bool Equals(IPort x, IPort y)
        {
            return x.Node.Id == y.Node.Id && x.HasName == y.HasName && (!x.HasName || x.Name == y.Name);
        }

        public int GetHashCode(IPort obj)
        {
            return (obj.Node.Id.GetHashCode() + obj.Name.GetHashCode()).GetHashCode();
        }
    }
}
