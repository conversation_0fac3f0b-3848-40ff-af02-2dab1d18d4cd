﻿using Microsoft.Aether.S2S.Common;
using Microsoft.Aether.S2S.Testing;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.MachineLearning.Common.Caching;
using Microsoft.MachineLearning.Common.Core.Configurations;
using Microsoft.MachineLearning.Common.WebApi.Client;
using Microsoft.MachineLearning.Common.WebApi.Security;
using Microsoft.MachineLearning.Common.WebApi.TeamAccounts;
using NUnit.Framework;
using System;

namespace BBAetherLibrary.DataProvision
{
    public static class TestFixtureInstallation
    {
        public static void AddTestFixture(this IServiceCollection services, IConfiguration configuration)
        {
            //var loggerFactory = new LoggerFactory();
            //services.AddSingleton<ILoggerFactory>(loggerFactory);

            services.AddCommonCaching(configuration);

            services.Configure<ServiceInvokerConfiguration>(configuration.GetSection("ServiceInvoker"));
            services.Configure<RPClientConfiguration>(configuration.GetSection("RPClient"));
            services.Configure<S2SConfiguration>(configuration.GetSection("S2S"));
            services.Configure<ExperimentationHostConfiguration>(configuration.GetSection("ExperimentationHost"));

            services.AddSingleton<AzureConstantsConfiguration>();

            services.AddSingleton<IActiveDirectoryTokenProvider, ServicePrincipalAADTokenProvider>();
            services.AddSingleton<ISubscriptionTenantResolver, SubscriptionTenantResolver>();
            services.AddSingleton<IServiceInvokerRetryPolicy, ServiceInvokerRetryPolicy>();
            services.AddSingleton<IServiceInvokerFactory, Microsoft.MachineLearning.Common.WebApi.Client.ServiceInvokerFactory>();
            services.AddSingleton<IS2SClientFactory, S2SClientFactory>();

            services.AddSingleton<IS2STokenProvider, TestS2STokenProvider>();
            services.AddSingleton<Contributor>();
            services.AddSingleton<ServiceInvokerFactory>();
            services.AddSingleton<SnapshotClient>();
            services.AddSingleton<BlobClient>();

            services.AddSingleton(sp =>
            {
                var azureContansts = sp.GetRequiredService<AzureConstantsConfiguration>();

                var runParameters = TestContext.Parameters;

                return new WorkspaceResources()
                {
                    Tenant = Guid.Parse(TestContext.Parameters.Get("Tenant") ?? azureContansts.MicrosoftTenantId),
                    Region = TestContext.Parameters.Get("Region") ?? throw new ArgumentNullException("Region"),
                    SubscriptionId = Guid.Parse(TestContext.Parameters.Get("SubscriptionId") ?? throw new ArgumentNullException("SubscriptionId")),
                    ResourceGroupName = TestContext.Parameters.Get("ResourceGroup") ?? throw new ArgumentNullException("ResourceGroup"),
                    WorkspaceId = Guid.Parse(TestContext.Parameters.Get("WorkspaceId") ?? throw new ArgumentNullException("WorkspaceId")),
                    WorkspaceName = TestContext.Parameters.Get("WorkspaceName") ?? throw new ArgumentNullException("WorkspaceName"),
                    AmlComputeClusterName = runParameters.Get("AmlComputeName") ?? throw new ArgumentNullException("AmlComputeName"),
                    StorageAccountName = runParameters.Get("StorageAccountName") ?? throw new ArgumentNullException("StorageAccountName"),
                };
            });

            services.AddSingleton<DatasetProvision>();

            services.AddSingleton<ProvisionedTokenProvider>();

            services.AddSingleton<IRPClient, RPClient>();
        }
    }
}
