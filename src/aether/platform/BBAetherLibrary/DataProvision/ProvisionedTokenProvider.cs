﻿using Microsoft.Aether.TokenProvider;
using System.Threading.Tasks;

namespace BBAetherLibrary.DataProvision
{
    public class ProvisionedTokenProvider : ITokenProvider
    {
        private readonly Contributor _contributor;

        public ProvisionedTokenProvider(Contributor contributor)
        {
            _contributor = contributor;
        }

        public async Task<string> GetAccessTokenAsync()
        {
            return await _contributor.GetTokenAsync();
        }
    }
}
