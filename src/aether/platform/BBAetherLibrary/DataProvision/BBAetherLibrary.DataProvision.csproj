<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <RootNamespace>BBAetherLibrary.DataProvision</RootNamespace>
    <AssemblyName>BBAetherLibrary.DataProvision</AssemblyName>
    <IsTestProject>false</IsTestProject>
    <ProduceReferenceAssembly>False</ProduceReferenceAssembly>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="NUnit" />
    <PackageReference Include="NUnit3TestAdapter" />
    <PackageReference Include="Microsoft.Extensions.Configuration" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\backendV2\shared\DataSetClient\DataSetClient.csproj" />
    <ProjectReference Include="..\..\backendV2\BlueBox\Clouds\Clouds.Common\Microsoft.Aether.BlueBox.Clouds.Common.csproj" />
    <ProjectReference Include="..\..\backendV2\shared\Microsoft.Aether.TokenProvider\Microsoft.Aether.TokenProvider.csproj" />
    <ProjectReference Include="..\..\backendV2\shared\Microsoft.Aether.S2S.Testing\Microsoft.Aether.S2S.Testing.csproj" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="$(SrcRoot)\Common\Core\Common.Core.csproj" />
    <ProjectReference Include="$(SrcRoot)\Common\Testing\Common.Testing.csproj" />
    <ProjectReference Include="$(SrcRoot)\Common\WebApi\Common.WebApi.csproj" />
    <ProjectReference Include="$(SrcRoot)\Common\WebApi.Client\Common.WebApi.Client.csproj" />
    <ProjectReference Include="$(SrcRoot)\Common\WebApi.TeamAccounts\Common.WebApi.TeamAccounts.csproj" />
    <ProjectReference Include="$(SrcRoot)\Dataset\Contracts\Dataset.Contracts.csproj" />
    <ProjectReference Include="$(SrcRoot)\ModelRegistry\Contracts\ModelRegistry.Contracts.csproj" />
  </ItemGroup>
  <ItemGroup>
    <None Update="appsettings.Development.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="appsettings\appsettings.override.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>
</Project>
