﻿{
  "Logging": {
    "IncludeScopes": false,
    "LogLevel": {
      "Default": "Information",
      "System": "Warning",
      "Microsoft": "Warning",
      "Orleans": "Warning",
      "Microsoft.Aether": "Information"
    }
  },
  "AmlTelemetry": {
    "Enabled": true,
    "MetricsConfiguration": {
      "Disabled": true
    },
    "Scrubbing": {
      "ScrubbingEnabled": true
    }
  },
  "S2S": {
    "ClientId": "",
    "ClientSecret": "",
    "Authority": "https://login.windows.net/72f988bf-86f1-41af-91ab-2d7cd011db47"
  },
  "RPClient": {
    "GetAccountStorageTimeout": "00:00:05"
  },
  "ServiceInvoker": {
    "DefaultRequestTimeoutMilliseconds": "30000",
    "RetryPolicy": {
      "DefaultRetryCount": "2",
      "DefaultDelayMilliseconds": "1000"
    }
  },
  "ExperimentationHost": {
    "ExperimentationHostUri": "https://master.api.azureml-test.ms",
    "ResourceProviderHostUri": "https://master.api.azureml-test.ms"
  }
}