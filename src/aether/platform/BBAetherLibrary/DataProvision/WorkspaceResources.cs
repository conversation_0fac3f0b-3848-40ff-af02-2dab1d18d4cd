﻿using System;

namespace BBAetherLibrary.DataProvision
{
    public class WorkspaceResources
    {
#pragma warning disable CS8618
        public Guid Tenant { get; set; }
        public Guid SubscriptionId { get; set; }
        public string Region { get; set; }
        public string ResourceGroupName { get; set; }
        public string WorkspaceName { get; set; }
        public Guid WorkspaceId { get; set; }

        public string? AmlComputeClusterName { get; set; }

        public string ContainerRegistryName { get; set; }
        public string StorageAccountName { get; set; }
#pragma warning restore
    }
}
