﻿using Microsoft.Extensions.Logging;
using Microsoft.MachineLearning.Common.WebApi.TeamAccounts;
using Microsoft.MachineLearning.Dataset.Contracts;
using Microsoft.MachineLearning.DataStore.Contracts;
using Microsoft.MachineLearning.ModelRegistry.Contracts;
using Microsoft.MachineLearning.ModelRegistry.Contracts.Common;
using Microsoft.MachineLearning.Project.Contracts;
using System;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace BBAetherLibrary.DataProvision
{
    public class DatasetProvision
    {
        private readonly ILogger<DatasetProvision> _logger;
        private readonly WorkspaceResources _workspaceResource;

        private readonly ISavedDatasetController _savedDataSetController;
        private readonly IDataContainerController _dataContainerController;
        private readonly IDataVersionController _dataVersionController;
        private readonly IDatasetsController _datasetsController;
        private readonly IDataStoreController _dataStoreController;
        private readonly IModelController _modelController;

        private readonly SnapshotClient _snapshotClient;

        private readonly BlobClient _blobClient;
        private readonly IRPClient _rpClient;

        public const string DefaultDatastoreName = "workspaceblobstore";

        public DatasetProvision(
            ILoggerFactory loggerFactory,
            WorkspaceResources workspaceResource,
            ServiceInvokerFactory serviceInvokerFactory,
            SnapshotClient snapshotClient,
            BlobClient blobClient,
            IRPClient rPClient)
        {
            _logger = loggerFactory.CreateLogger<DatasetProvision>();
            _workspaceResource = workspaceResource;

            _savedDataSetController = serviceInvokerFactory.CreateContributorSerivceInvoker<ISavedDatasetController>();
            _datasetsController = serviceInvokerFactory.CreateContributorSerivceInvoker<IDatasetsController>();
            _dataContainerController = serviceInvokerFactory.CreateContributorSerivceInvoker<IDataContainerController>();
            _dataVersionController = serviceInvokerFactory.CreateContributorSerivceInvoker<IDataVersionController>();
            _dataStoreController = serviceInvokerFactory.CreateContributorSerivceInvoker<IDataStoreController>();
            _modelController = serviceInvokerFactory.CreateContributorSerivceInvoker<IModelController>();

            _snapshotClient = snapshotClient;
            _blobClient = blobClient;
            _rpClient = rPClient;
        }

        private async Task<SavedDatasetDto> CreateSavedDataset(string? relativePath, string datastoreName = DefaultDatastoreName)
        {
            var datasetPathDto = new DatasetPathDto()
            {
                DatastoreName = datastoreName,
                RelativePath = relativePath ?? string.Empty,
            };

            var savedDataset = await _savedDataSetController.EnsureSavedFromDataPath(_workspaceResource.SubscriptionId,
                _workspaceResource.ResourceGroupName,
                _workspaceResource.WorkspaceName,
                datasetPathDto);

            return savedDataset;
        }

        public async Task<SavedDatasetDto> PrepareSavedDataset(string relativePath, Stream content, string? blobContainerName = null)
        {
            if (!await _blobClient.BlobExists(relativePath, blobContainerName))
            {
                await _blobClient.UploadBlob(relativePath, content, blobContainerName);
            }

            var dataset = await CreateSavedDataset(relativePath);

            return dataset;
        }

        public async Task<SavedDatasetDto> PrepareSavedDataset(string relativePath, string content)
        {
            return await PrepareSavedDataset(relativePath, new MemoryStream(System.Text.Encoding.UTF8.GetBytes(content)));
        }

        public async Task<Guid> PrepareSnapshot(string experimentName)
        {
            var projectFolderTree = new DirTreeNode(string.Empty, SnapshotDtoConstants.DirectoryType);

            return await _snapshotClient.CreateSnapshotAsync(new FilePayload[] { }, experimentName, projectFolderTree).ConfigureAwait(false);
        }

        public async Task<DatasetDto> RegisterDataset(string name, string datasetType, string relativePath, string? fileType = null, string datastoreName = DefaultDatastoreName)
        {
            if (!DatasetTypes.All.Contains(datasetType))
            {
                throw new ArgumentException("invalid datasetType", nameof(datasetType));
            }

            if (datasetType == DatasetTypes.TabularDataset && fileType == null)
            {
                fileType = FileType.GenericCsv;
            }

            if (!string.IsNullOrEmpty(fileType) && !FileType.All.Contains(fileType))
            {
                throw new ArgumentException("invalid fileType", nameof(fileType));
            }

            var datasetDto = new DatasetRequestDto()
            {
                Name = name,
                DatasetType = datasetType,
                General = new()
                {
                    FileType = fileType ?? FileType.Unknown,
                },
                DataPath = new()
                {
                    DatastoreName = datastoreName,
                    RelativePath = relativePath,
                },
            };

            return await _datasetsController.RegisterFromDataPath(_workspaceResource.SubscriptionId,
                _workspaceResource.ResourceGroupName,
                _workspaceResource.WorkspaceName,
                datasetDto);
        }

        public async Task<string> GetDataAssetUri(string name, string relativePath, DataType dataType, string version = "1")
        {
            if (dataType == DataType.UriFolder && !relativePath.EndsWith("/"))
            {
                relativePath += "/";
            }

            var DataUri = $"azureml://datastores/{DefaultDatastoreName}/paths/{relativePath}";

            DataContainerEntityDto dataContainer;

            if (!await _dataContainerController.Exists(_workspaceResource.SubscriptionId,
                _workspaceResource.ResourceGroupName,
                _workspaceResource.WorkspaceName,
                name))
            {
                var dataContainerDto = new DataContainerDto()
                {
                    Name = name,
                    DataType = dataType.ToString(),
                };

                dataContainer = await _dataContainerController.Create(_workspaceResource.SubscriptionId,
                    _workspaceResource.ResourceGroupName,
                    _workspaceResource.WorkspaceName,
                    dataContainerDto);
            }
            else
            {
                dataContainer = await _dataContainerController.Get(_workspaceResource.SubscriptionId,
                    _workspaceResource.ResourceGroupName,
                    _workspaceResource.WorkspaceName,
                    name);
            }

            if (dataContainer.LatestVersion != null)
            {
                return dataContainer.LatestVersion.DataVersion.AssetId.ToString();
            }

            var dataVersionDto = new DataVersionDto()
            {
                DataContainerName = name,
                VersionId = version,
                DataUri = DataUri,
                DataType = dataType.ToString(),
            };

            DataVersionEntityDto dataVersion;

            if (!await _dataVersionController.Exists(_workspaceResource.SubscriptionId,
                _workspaceResource.ResourceGroupName,
                _workspaceResource.WorkspaceName,
                name,
                version))
            {
                dataVersion = await _dataVersionController.Create(_workspaceResource.SubscriptionId,
                    _workspaceResource.ResourceGroupName,
                    _workspaceResource.WorkspaceName,
                    name,
                    dataVersionDto);
            }
            else
            {
                dataVersion = await _dataVersionController.Get(_workspaceResource.SubscriptionId,
                    _workspaceResource.ResourceGroupName,
                    _workspaceResource.WorkspaceName,
                    name,
                    version);
            }

            return dataVersion.DataVersion.AssetId.ToString();
        }

        public async Task<DataStoreDto> PrepareBlobDatastore(string name, string containerName)
        {
            var connection = await _rpClient.GetStorageDto(_workspaceResource.SubscriptionId, _workspaceResource.WorkspaceId);

            await _blobClient.CreateBlobContainer(name);

            DataStoreDto dataStoreDto = new()
            {
                Name = name,
                DataStoreType = DataStoreType.AzureBlob,
                AzureStorageSection = new()
                {
                    AccountName = _workspaceResource.StorageAccountName,
                    ContainerName = containerName,
                    CredentialType = AzureStorageCredentialTypes.AccountKey,
                    Credential = connection.AccountKey,
                }
            };

            var dataStore = await _dataStoreController.CreateOrUpdate(_workspaceResource.SubscriptionId.ToString(),
                _workspaceResource.ResourceGroupName,
                _workspaceResource.WorkspaceName,
                name,
                dataStoreDto);

            if (((int)dataStore.StatusCode) >= 300)
            {
                _logger.LogError("CreateOrUpdate {dataStore} failed with {statusCode}", name, dataStore.StatusCode);
            }

            return dataStore.Content;
        }

        public async Task<string?> PrepareModelAsset(string name, ModelType type, string path)
        {
            var models = await _modelController.BatchGetOrCreateUnregisteredInputModel(_workspaceResource.SubscriptionId,
                _workspaceResource.ResourceGroupName,
                _workspaceResource.WorkspaceName,
                new BatchCreateUnregisteredInputModelDto()
                {
                    Values = new()
                    {
                        new CreateUnregisteredInputModelDto()
                        {
                            InputName = name,
                            Path = new Uri($"azureml://workspaces/{_workspaceResource.WorkspaceId}/datastores/{DefaultDatastoreName}/paths/{path}"),
                            Type = type.ToString(),
                        }
                    }
                }).ConfigureAwait(false);

            return models.Models?.Values.FirstOrDefault()?.AssetId?.ToString() ?? throw new Exception($"failed to prepare InputModel: {models.Errors?.FirstOrDefault().Value.ErrorResponse?.Error.Message}");
        }
    }
}
