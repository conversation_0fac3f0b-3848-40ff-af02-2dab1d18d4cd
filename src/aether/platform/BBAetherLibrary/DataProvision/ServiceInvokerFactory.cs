﻿using Microsoft.Aether.S2S.Common;
using Microsoft.Extensions.Options;
using Microsoft.MachineLearning.Common.WebApi.Client;
using Microsoft.MachineLearning.Common.WebApi.TeamAccounts;
using Polly;
using System;
using System.Net.Http;
using System.Net.Http.Headers;

namespace BBAetherLibrary.DataProvision
{
    public class ServiceInvokerFactory
    {
        private readonly string UserAgent = "Pipeline Service Test";
        private string ApiEndpoint { get; set; }

        private readonly Contributor Contributor;
        private readonly IS2STokenProvider S2STokenProvider;

        public ServiceInvokerFactory(Contributor contributor,
            IS2STokenProvider s2STokenProvider,
            IOptions<ExperimentationHostConfiguration> experimentationHostConfiguration)
        {
            Contributor = contributor;
            S2STokenProvider = s2STokenProvider;

            ApiEndpoint = experimentationHostConfiguration.Value.ResourceProviderHostUri;
        }

        public T CreateContributorSerivceInvoker<T>()
        {
            var httpClient = new HttpClient();
            httpClient.DefaultRequestHeaders.UserAgent.TryParseAdd(UserAgent);

            var retryPolicy = Policy
                .Handle<Microsoft.MachineLearning.Common.Core.ServiceTimeoutException>()
                .WaitAndRetryAsync(3, i => TimeSpan.FromSeconds(10));

            return ServiceInvoker.Create<T>(
                client: httpClient,
                serviceAddress: new Uri(ApiEndpoint),
                beforeRequestAsync: async (request, method) =>
                {
                    // Add user token validation
                    request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", await Contributor.GetTokenAsync());
                },
                retryPolicy: retryPolicy);
        }

        public T CreateS2SServiceInvoker<T>()
        {
            var httpClient = new HttpClient();
            httpClient.DefaultRequestHeaders.UserAgent.TryParseAdd(UserAgent);

            var retryPolicy = Policy
                .Handle<Microsoft.MachineLearning.Common.Core.ServiceTimeoutException>()
                .WaitAndRetryAsync(3, i => TimeSpan.FromSeconds(10));

            return ServiceInvoker.Create<T>(
                client: httpClient,
                serviceAddress: new Uri(ApiEndpoint),
                beforeRequestAsync: async (request, method) =>
                {
                    // Add user token validation
                    request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", await S2STokenProvider.GetTokenAsync());
                },
                retryPolicy: retryPolicy);
        }
    }
}
