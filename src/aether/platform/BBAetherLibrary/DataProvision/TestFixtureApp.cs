﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System;
using System.IO;

namespace BBAetherLibrary.DataProvision
{
    public class TestFixtureApp
    {
        public IServiceProvider ServiceProvider { get; }

        public TestFixtureApp()
        {
            var environmentName = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");

            var host = new HostBuilder()
                .ConfigureAppConfiguration((builderContext, builder) =>
                {
                    builder.AddJsonFile("appsettings.json")
                        .AddJsonFile(Path.Join("/", "app", "appsettings.json"), optional: true)
                        .AddJsonFile($"appsettings.{environmentName}.json", optional: true, reloadOnChange: true)
                        .AddJsonFile(Path.Combine("appsettings", "appsettings.override.json"), optional: true)
                        // add for using appsettings of k8s runner env
                        .AddJsonFile(Path.Join("/", "app", $"appsettings.{environmentName}.json"), optional: true, reloadOnChange: true)
                        .AddJsonFile(Path.Join("/", "app", "appsettings", "appsettings.override.json"), optional: true)
                        .AddEnvironmentVariables();
                })
                .ConfigureServices((builderContext, services) =>
                {
                    services.AddSingleton<ILoggerFactory, LoggerFactory>();

                    services.AddSingleton((IConfigurationRoot)builderContext.Configuration);

                    services.AddTestFixture(builderContext.Configuration);
                }).Build();

            ServiceProvider = host.Services;
        }

        public T GetRequiredService<T>() where T : notnull
        {
            return ServiceProvider.GetRequiredService<T>();
        }

        public T? GetService<T>()
        {
            return ServiceProvider.GetService<T>();
        }
    }
}
