﻿using Microsoft.Aether.BlueBox.SnapshotContracts;
using Microsoft.Azure.Storage.Auth;
using Microsoft.MachineLearning.Common.WebApi.TeamAccounts;
using Microsoft.MachineLearning.Project.Contracts;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;

namespace BBAetherLibrary.DataProvision
{
    public class SnapshotClient
    {
        private readonly WorkspaceResources _workspaceResources;
        private readonly ISnapshotControllerV2NewRoutes _snapshotController;
        private readonly IRPClient _rpClient;

        private const int RetryCountForBlobUpload = 3;

        public SnapshotClient(WorkspaceResources workspaceResources,
            ServiceInvokerFactory serviceInvokerFactory,
            IRPClient rpClient)
        {
            _workspaceResources = workspaceResources;
            _snapshotController = serviceInvokerFactory.CreateContributorSerivceInvoker<ISnapshotControllerV2NewRoutes>();
            _rpClient = rpClient;
        }

        public async Task<Guid> CreateSnapshotAsync(FilePayload[]? fileData, string experimentName, DirTreeNode projectFolder)
        {
            FileNodeListDto fileNodeList;

            if (fileData != null && fileData.Length > 0)
            {
                var fileDataDictionay = new Dictionary<string, ByteArrayContent>();

                List<string> fileNames = new();
                long maxFileSize = 0;
                if (fileData != null)
                {
                    foreach (FilePayload data in fileData)
                    {
                        fileNames.Add(data.Name);
                        maxFileSize = Math.Max(maxFileSize, data.Content.Headers.ContentLength ?? 0);

                        fileDataDictionay[data.Name] = new ByteArrayContent(await data.Content.ReadAsByteArrayAsync());
                    }
                }

                FileNameListDto fileNameListDto = new()
                {
                    FileNames = fileNames
                };

                fileNodeList = await _snapshotController.GetBlobUris(_workspaceResources.SubscriptionId,
                    _workspaceResources.ResourceGroupName,
                    _workspaceResources.WorkspaceName,
                    fileNameListDto);

                var storageDto = await _rpClient.GetStorageTokenDto(_workspaceResources.SubscriptionId, _workspaceResources.WorkspaceId);

                var msiToken = storageDto.Token;
                var storageCredentials = new StorageCredentials(new TokenCredential(msiToken));

                await SnapshotHelper.UploadBlobUrisAsync(fileNodeList, fileDataDictionay, CancellationToken.None, maxFileSize, RetryCountForBlobUpload, storageCredentials).ConfigureAwait(false);
            }
            else
            {
                fileNodeList = new FileNodeListDto()
                {
                    FileNodes = new(),
                };
            }

            CreateSnapshotDto createSnapshotDto = new()
            {
                ParentSnapshotId = Guid.Empty,
                Tags = null,
                Properties = null,
                DirTreeNode = projectFolder,
                FileRevisionList = fileNodeList
            };

            Guid snapshotId = Guid.NewGuid();

            await _snapshotController.CreateSnapshotAlreadyUpload(_workspaceResources.SubscriptionId,
                _workspaceResources.ResourceGroupName,
                _workspaceResources.WorkspaceName,
                experimentName,
                snapshotId,
                createSnapshotDto);

            return snapshotId;
        }
    }
}
