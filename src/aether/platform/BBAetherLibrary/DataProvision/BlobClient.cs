﻿using Azure.Storage.Blobs;
using Microsoft.MachineLearning.Common.WebApi.TeamAccounts;
using System.Collections.Concurrent;
using System.IO;
using System.Net.Http;
using System.Threading.Tasks;

namespace BBAetherLibrary.DataProvision
{
    public class FilePayload
    {
        public FilePayload(string name, HttpContent content)
        {
            Name = name;
            Content = content;
        }

        public string Name { get; set; }

        public HttpContent Content { get; set; }
    }

    public class BlobClient
    {
        private readonly WorkspaceResources _workspaceResource;

        private readonly IRPClient _rpClient;

        private string? _defaultDataContainerName;// = "myblobdatastore";
        private ConcurrentDictionary<string, BlobContainerClient> _blobContainerClients = new();

        public BlobClient(WorkspaceResources workspaceResource,
            IRPClient rpClient)
        {
            _workspaceResource = workspaceResource;

            _rpClient = rpClient;
        }

        private async Task<BlobContainerClient> GetBlobContainerClient(string? blobContainerName = null)
        {
            BlobContainerClient? blobContainerClient;

            // https://docs.microsoft.com/en-us/dotnet/api/azure.storage.blobs.blobcontainerclient?view=azure-dotnet
            if ((blobContainerName == null && _defaultDataContainerName == null) || !_blobContainerClients.TryGetValue(blobContainerName ?? _defaultDataContainerName!, out blobContainerClient))
            {
                var connection = await _rpClient.GetStorageDto(_workspaceResource.SubscriptionId, _workspaceResource.WorkspaceId);

                var serviceClient = new BlobServiceClient(connection.ConnectionString);

                if (blobContainerName == null)
                {
                    blobContainerName = connection.Container;

                    await foreach (var container in serviceClient.GetBlobContainersAsync(prefix: "azureml-blobstore-"))
                    {
                        blobContainerName = container.Name;

                        _defaultDataContainerName = blobContainerName;

                        break;
                    }
                }

                blobContainerClient = serviceClient.GetBlobContainerClient(blobContainerName);

                _blobContainerClients[blobContainerName] = blobContainerClient;
            }

            return blobContainerClient;
        }

        public async Task UploadBlob(string blobName, Stream content, string? blobContainerName = null)
        {
            if (!await BlobExists(blobName, blobContainerName))
            {
                BlobContainerClient blobContainerClient = await GetBlobContainerClient(blobContainerName);

                await blobContainerClient.UploadBlobAsync(blobName, content);
            }
        }

        public async Task<bool> BlobExists(string blobName, string? blobContainerName = null)
        {
            BlobContainerClient blobContainerClient = await GetBlobContainerClient(blobContainerName);

            return await blobContainerClient.GetBlobClient(blobName).ExistsAsync();
        }

        public async Task UploadBlobs(FilePayload[] fileData, string? blobContainerName = null)
        {
            int numberOfFiles = fileData.Length;
            for (int fileIndex = 0; fileIndex < numberOfFiles; fileIndex++)
            {
                if (!await BlobExists(fileData[fileIndex].Name, blobContainerName))
                {
                    await UploadBlob(fileData[fileIndex].Name, await fileData[fileIndex].Content.ReadAsStreamAsync(), blobContainerName);
                }
            }
        }

        public async Task<string> GetContainerName()
        {
            return (await GetBlobContainerClient()).Name;
        }

        public async Task<BlobContainerClient> CreateBlobContainer(string name)
        {
            var container = await GetBlobContainerClient(name);
            if (!await container.ExistsAsync())
            {
                await container.CreateAsync();
            }

            return container;
        }
    }
}
