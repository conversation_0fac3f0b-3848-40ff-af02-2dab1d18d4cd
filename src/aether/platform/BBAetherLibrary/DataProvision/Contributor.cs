﻿using Azure.Core;
using Azure.Identity;
using Microsoft.Aether.DataContracts;
using Microsoft.Extensions.Configuration;
using Microsoft.MachineLearning.Common.Core.Configurations;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace BBAetherLibrary.DataProvision
{
    public class Contributor
    {
        private readonly string TenantId;
        private readonly string AmlResource;

        private readonly TokenCredential _tokenCredential;

        private AccessToken? _cachedToken;

        private CreatedBy? _createdBy;

        public Contributor(IConfigurationRoot configuration,
            WorkspaceResources workspaceResource,
            AzureConstantsConfiguration azureConstantsConfiguration)
        {
            var proisioned = configuration.GetSection("Provisioned");
            var appid = proisioned.GetValue<string>("ContributorKey");
            var secret = proisioned.GetValue<string>("ContributorSecret");

            TenantId = workspaceResource.Tenant.ToString();
            AmlResource = azureConstantsConfiguration.AzureMachineLearningResourceUri;

            _tokenCredential = (appid == null || secret == null) ? new DefaultAzureCredential() : new ClientSecretCredential(workspaceResource.Tenant.ToString(), appid, secret);
        }

        public CreatedBy GetCreatedBy()
        {
            if (_createdBy == null)
            {
                var jwtToken = new System.IdentityModel.Tokens.Jwt.JwtSecurityTokenHandler().ReadJwtToken(GetToken());
                _createdBy = new CreatedBy(
                    jwtToken.Payload["oid"]?.ToString(),
                    jwtToken.Payload["tid"]?.ToString(),
                    jwtToken.Payload["name"]?.ToString(),
                    jwtToken.Payload.GetValueOrDefault("puid")?.ToString(),
                    jwtToken.Payload.GetValueOrDefault("iss")?.ToString(),
                    jwtToken.Payload.GetValueOrDefault("idp")?.ToString(),
                    jwtToken.Payload.GetValueOrDefault("altsecid")?.ToString()
                );
            }

            return _createdBy;
        }

        public string GetToken(CancellationToken cancellationToken = default(CancellationToken))
        {
            if (_cachedToken == null || _cachedToken?.ExpiresOn < DateTime.Now.AddMinutes(-5))
            {
                _cachedToken = _tokenCredential.GetToken(new TokenRequestContext(new[] { $"{AmlResource}/.default" }, tenantId: TenantId), cancellationToken);
            }

            return _cachedToken?.Token!;
        }

        public async Task<string> GetTokenAsync(CancellationToken cancellationToken = default(CancellationToken))
        {
            if (_cachedToken == null || _cachedToken?.ExpiresOn < DateTime.Now.AddMinutes(-5))
            {
                _cachedToken = await _tokenCredential.GetTokenAsync(new TokenRequestContext(new[] { $"{AmlResource}/.default" }, tenantId: TenantId), cancellationToken);
            }

            return _cachedToken?.Token!;
        }
    }
}
