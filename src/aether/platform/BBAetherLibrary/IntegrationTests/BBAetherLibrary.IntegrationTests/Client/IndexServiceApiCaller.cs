﻿using BBAetherLibrary.Common;
using Microsoft.Aether.BlueBox.Library;
using Microsoft.MachineLearning.Index.Contracts;
using Microsoft.MachineLearning.RunHistory.Contracts;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;

namespace BBAetherLibrary.IntegrationTests.Client
{
    public class IndexServiceApiCaller
    {
        protected HttpClient _httpClient;
        public readonly string DefaultEndpointAddress = $"{TestEnvironment.GetAzureMLServiceEndpoint()}/index/v1.0/";

        public IndexServiceApiCaller(
            string subscription,
            string resourceGroupName,
            string workspaceName,
            Func<Task<string>> accessTokenProvider)
        {
            _httpClient = new ClientFactory(accessTokenProvider).CreateHttpClient(DefaultEndpointAddress, subscription, resourceGroupName, workspaceName);
        }

        private async Task<IndexEntitiesResponse<RunIndexEntity>> WaitToFinishPostRequestForIndexService(string relativePath, IndexEntitiesRequest indexEntitiesRequest)
        {
            IndexEntitiesResponse<RunIndexEntity> result = null;
            var startTime = DateTime.Now;
            var timeLimit = new TimeSpan(0, 3, 0);

            while (DateTime.Now.Subtract(startTime) < timeLimit)
            {
                result = await _httpClient.PostEntityAsync<IndexEntitiesRequest, IndexEntitiesResponse<RunIndexEntity>>(relativePath, indexEntitiesRequest).ConfigureAwait(false);
                if (result.TotalCount != 0)
                {
                    break;
                }

                Thread.Sleep(2000);
            }

            return result;
        }

        private async Task<IndexEntitiesResponse<RunIndexEntity>> GetChildRunsByIndexServiceAsync(string pipelineRunId, string continuationToken = null, int top = 50)
        {
            var pipelineRunIdFilter = IndexEntitiesRequestFilter.Eq("properties/parentRunId", new string[] { pipelineRunId });
            var typeFileter = IndexEntitiesRequestFilter.Eq(IndexEntitiesRequestFilter.Type, new string[] { "runs" });

            var indexEntitiesRequest = new IndexEntitiesRequest()
            {
                PageSize = top,
                Filters = new List<IndexEntitiesRequestFilter>() { pipelineRunIdFilter, typeFileter },
                Order = new List<IndexEntitiesRequestOrder>() { IndexEntitiesRequestOrder.OrderByDesc("annotations/modifiedTime") },
                ContinuationToken = continuationToken,
            };

            var relativePath = $"entities";
            return await WaitToFinishPostRequestForIndexService(relativePath, indexEntitiesRequest).ConfigureAwait(false);
        }

        public async Task<IEnumerable<RunIndexEntity>> ListFullChildRunsByIndexService(string runId)
        {
            var results = new List<RunIndexEntity>();
            var childRuns = await GetChildRunsByIndexServiceAsync(runId).ConfigureAwait(false);

            var continuationToken = childRuns?.ContinuationToken;
            if (childRuns?.Value != null)
            {
                results.AddRange(childRuns.Value);
            }

            while (continuationToken != null)
            {
                childRuns = await GetChildRunsByIndexServiceAsync(runId, continuationToken).ConfigureAwait(false);
                continuationToken = childRuns?.ContinuationToken;
                if (childRuns?.Value != null)
                {
                    results.AddRange(childRuns.Value);
                }
            }
            return results;
        }

        public async Task<IndexEntitiesResponse<RunIndexEntity>> ExtractPipelineRunFromIndexService(string pipelineRunId)
        {
            var pipelineRunIdFilter = IndexEntitiesRequestFilter.Eq("properties/rootRunId", new string[] { pipelineRunId });
            var runTypeFileter = IndexEntitiesRequestFilter.Eq("properties/runType", new string[] { "azureml.PipelineRun" });
            var typeFileter = IndexEntitiesRequestFilter.Eq(IndexEntitiesRequestFilter.Type, new string[] { "runs" });

            var indexEntitiesRequest = new IndexEntitiesRequest()
            {
                PageSize = 1,
                Filters = new List<IndexEntitiesRequestFilter>() { pipelineRunIdFilter, runTypeFileter, typeFileter },
                Order = new List<IndexEntitiesRequestOrder>() { IndexEntitiesRequestOrder.OrderByDesc("annotations/modifiedTime") },
            };

            var relativePath = $"entities";
            return await WaitToFinishPostRequestForIndexService(relativePath, indexEntitiesRequest).ConfigureAwait(false);
        }
    }
}
