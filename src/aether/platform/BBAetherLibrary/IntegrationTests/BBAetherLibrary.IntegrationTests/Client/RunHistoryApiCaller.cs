﻿using BBAetherLibrary.Common;
using Microsoft.Aether.BlueBox.Library;
using Microsoft.MachineLearning.RunHistory.Contracts;
using System;
using System.Net.Http;
using System.Threading.Tasks;

namespace BBAetherLibrary.IntegrationTests.Client
{
    public class RunHistoryApiCaller
    {
        protected HttpClient _httpClient;
        public readonly string DefaultEndpointAddress = $"{TestEnvironment.GetAzureMLServiceEndpoint()}/history/v1.0/";

        public RunHistoryApiCaller(
            string subscription,
            string resourceGroupName,
            string workspaceName,
            Func<Task<string>> accessTokenProvider)
        {
            _httpClient = new ClientFactory(accessTokenProvider).CreateHttpClient(DefaultEndpointAddress, subscription, resourceGroupName, workspaceName);
        }

        public async Task<RunDto> GetRunDto(string experimentName, string runId)
        {
            var relativePath = $"experiments/{experimentName}/runs/{runId}";
            var runDto = await _httpClient.GetEntityAsync<RunDto>(relativePath).ConfigureAwait(false);
            return runDto;
        }

        public async Task<RunDetailsDto> GetRunDetails(string experimentName, string runId)
        {
            var relativePath = $"experiments/{experimentName}/runs/{runId}/details";
            var runDetails = await _httpClient.GetEntityAsync<RunDetailsDto>(relativePath).ConfigureAwait(false);
            return runDetails;
        }
    }
}
