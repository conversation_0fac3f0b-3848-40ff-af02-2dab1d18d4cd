﻿using Microsoft.Aether.BlueBox.Library;
using Microsoft.RelInfra.Common;
using System;
using System.Net.Http;
using System.Threading.Tasks;

namespace BBAetherLibrary.IntegrationTests.Client
{
    class ClientFactory
    {
        public const string AADAuthority = "https://login.microsoftonline.com/common/oauth2/token";
        public const string ClientId = "04b07795-8ddb-461a-bbee-02f9e1bf7b46";
        public const string ResourceId = AdalConstants.ArmResource;

        private AuthHandler handler;

        public ClientFactory(
            Func<Task<string>> accessTokenProvider,
            IRetryPolicy retryPolicy = null,
            string authToken = null,
            CreatedByPayload onBehalfOfAzureUser = null)
        {
            if (retryPolicy == null)
            {
                retryPolicy = RetryPolicyFactory.GetDefaultRetryPolicy();
            }

            handler = accessTokenProvider == null
               ? new AuthHandler(
                   clientId: ClientId,
                   resourceId: ResourceId,
                   authority: AADAuthority,
                   authToken: authToken,
                   onBehalfOfAzureUser: onBehalfOfAzureUser,
                   retryPolicy: retryPolicy)
               : new AuthHandler(accessTokenProvider, retryPolicy);       
        }

        public HttpClient CreateHttpClient(
            string endpointAddress,
            string subscription,
            string resourceGroupName,
            string workspaceName,
            string userAgent = "bbaetherlibrary/default")
        {
            var workspaceRelativePath = $"subscriptions/{subscription}/resourceGroups/{resourceGroupName}/" +
                $"providers/Microsoft.MachineLearningServices/workspaces/{workspaceName}/";

            var httpClient = new HttpClient(handler)
            {
                BaseAddress = new Uri(new Uri(endpointAddress), workspaceRelativePath)
            };

            if (!String.IsNullOrWhiteSpace(userAgent))
            {
                if (!httpClient.DefaultRequestHeaders.UserAgent.TryParseAdd(userAgent))
                {
                    throw new ArgumentException($"Could not parse the given user agent string: {userAgent}");
                }
            }

            return httpClient;
        }

        public HttpClient CreateHttpClient(
            string userAgent = "bbaetherlibrary/default")
        {
            var httpClient = new HttpClient(handler);

            if (!String.IsNullOrWhiteSpace(userAgent))
            {
                if (!httpClient.DefaultRequestHeaders.UserAgent.TryParseAdd(userAgent))
                {
                    throw new ArgumentException($"Could not parse the given user agent string: {userAgent}");
                }
            }

            return httpClient;
        }
    }
}
