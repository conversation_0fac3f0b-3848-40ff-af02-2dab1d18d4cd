{"cells": [{"cell_type": "code", "source": ["from azureml.core import Workspace\r\n", "\r\n", "import os\r\n", "import azureml.core\r\n", "from azureml.core.compute import ComputeTarget, DataFactoryCompute\r\n", "from azureml.exceptions import ComputeTargetException\r\n", "from azureml.core import Workspace, Experiment\r\n", "from azureml.pipeline.core import Pipeline\r\n", "from azureml.core.datastore import Datastore\r\n", "from azureml.data.data_reference import DataReference\r\n", "from azureml.pipeline.steps import DataTransferStep\r\n", "from azureml.core import Workspace, Experiment, Datastore, RunConfiguration\r\n", "from azureml.core.compute import AmlCompute\r\n", "from azureml.core.compute import ComputeTarget\r\n", "from azureml.pipeline.core import Pipeline, PipelineData, PipelineParameter\r\n", "from azureml.pipeline.core.graph import InputPortDef, OutputPortDef\r\n", "from azureml.pipeline.core.module import Module\r\n", "from azureml.pipeline.steps import ModuleStep\r\n", "from azureml.core import Workspace, Dataset\r\n", "\r\n", "## Use this only for Azure AD service-to-service authentication\r\n", "from azure.common.credentials import ServicePrincipalCredentials\r\n", "\r\n", "## Use this only for Azure AD end-user authentication\r\n", "from azure.common.credentials import UserPassCredentials\r\n", "\r\n", "## Use this only for Azure AD multi-factor authentication\r\n", "from msrestazure.azure_active_directory import AADTokenCredentials\r\n", "\r\n", "## Required for Data Lake Storage Gen1 account management\r\n", "from azure.mgmt.datalake.store import DataLakeStoreAccountManagementClient\r\n", "from azure.mgmt.datalake.store.models import DataLakeStoreAccount\r\n", "\r\n", "## Required for Data Lake Storage Gen1 filesystem management\r\n", "from azure.datalake.store import core, lib, multithread\r\n", "\r\n", "# Common Azure imports\r\n", "from azure.mgmt.resource.resources import ResourceManagementClient\r\n", "from azure.mgmt.resource.resources.models import ResourceGroup\r\n", "\r\n", "from azureml.exceptions import UserErrorException\r\n", "\r\n", "## Use these as needed for your application\r\n", "import logging, getpass, pprint, uuid, time\r\n", "\r\n", "from azure.identity import DefaultAzureCredential\r\n", "from azure.keyvault.secrets import SecretClient\r\n", "\r\n", "credential = DefaultAzureCredential()\r\n", "\r\n", "secret_client = SecretClient(vault_url=\"https://aether-tests.vault.azure.net/\", credential=credential)\r\n", "secret = secret_client.get_secret(\"wse2etests-app-secret\")\r\n", "\r\n", "\r\n", "\r\n", "subscription_id = 'b8c23406-f9b5-4ccb-8a65-a8cb5dcd6a5a'\r\n", "resource_group = 'rge2etests'\r\n", "workspace_name = 'wse2etests'\r\n", "\r\n", "## https://ms.portal.azure.com/#blade/Microsoft_AAD_RegisteredApps/ApplicationMenuBlade/Overview/appId/aac7d666-d9f6-468c-84ec-a5e121f6e48d/isMSAApp/\r\n", "tenant_id = \"72f988bf-86f1-41af-91ab-2d7cd011db47\"\r\n", "client_id = \"aac7d666-d9f6-468c-84ec-a5e121f6e48d\"\r\n", "client_secret_placeholder = secret_client.get_secret(\"wse2etests-app-secret\")\r\n", "\r\n", "print(client_secret_placeholder)\r\n", "\r\n", "## Vinoth created below app \r\n", "## https://ms.portal.azure.com/#blade/Microsoft_AAD_RegisteredApps/ApplicationMenuBlade/Overview/appId/ce2aaf83-d332-4d81-8ae2-544af1647f1f/isMSAApp/\r\n", "tenant_id = \"72f988bf-86f1-41af-91ab-2d7cd011db47\"\r\n", "client_id = \"ce2aaf83-d332-4d81-8ae2-544af1647f1f\"\r\n", "client_secret_placeholder = secret_client.get_secret(\"wse2etests-app-secret\")\r\n", "\r\n", "\r\n", "ws = Workspace.from_config()"], "outputs": [{"output_type": "error", "ename": "SyntaxError", "evalue": "invalid syntax (<ipython-input-3-ed8a8f014e38>, line 46)", "traceback": ["\u001b[0;36m  File \u001b[0;32m\"<ipython-input-3-ed8a8f014e38>\"\u001b[0;36m, line \u001b[0;32m46\u001b[0m\n\u001b[0;31m    python -m pip install secrets\u001b[0m\n\u001b[0m                ^\u001b[0m\n\u001b[0;31mSyntaxError\u001b[0m\u001b[0;31m:\u001b[0m invalid syntax\n"]}], "execution_count": 3, "metadata": {"gather": {"logged": 1630097078409}}}, {"cell_type": "code", "source": ["from azureml.core import Dataset\r\n", "\r\n", "\r\n", "web_paths = ['https://azureopendatastorage.blob.core.windows.net/mnist/train-images-idx3-ubyte.gz',\r\n", "             'https://azureopendatastorage.blob.core.windows.net/mnist/train-labels-idx1-ubyte.gz']\r\n", "mnist_ds = Dataset.File.from_files(path=web_paths)"], "outputs": [], "execution_count": 2, "metadata": {"collapsed": true, "jupyter": {"source_hidden": false, "outputs_hidden": false}, "nteract": {"transient": {"deleting": false}}, "gather": {"logged": 1629144387006}}}, {"cell_type": "code", "source": ["## create compute\r\n", "\r\n", "from azureml.core.compute import ComputeTarget, AmlCompute\r\n", "from azureml.core.compute_target import ComputeTargetException\r\n", "\r\n", "# Choose a name for your CPU cluster\r\n", "cpu_cluster_name = \"cpu-cluster\"\r\n", "\r\n", "# Verify that cluster does not exist already\r\n", "try:\r\n", "    cpu_cluster = ComputeTarget(workspace=ws, name=cpu_cluster_name)\r\n", "    print('Found existing cluster, use it.')\r\n", "except ComputeTargetException:\r\n", "    compute_config = AmlCompute.provisioning_configuration(vm_size='STANDARD_D2_V2',\r\n", "                                                            max_nodes=4)\r\n", "    cpu_cluster = ComputeTarget.create(ws, cpu_cluster_name, compute_config)\r\n", "\r\n", "cpu_cluster.wait_for_completion(show_output=True)"], "outputs": [], "execution_count": null, "metadata": {"collapsed": true, "jupyter": {"source_hidden": false, "outputs_hidden": false}, "nteract": {"transient": {"deleting": false}}}}, {"cell_type": "code", "source": ["##  Create blob datastore, with accountKey\r\n", "\r\n", "from azureml.exceptions import UserErrorException\r\n", "\r\n", "blob_datastore_name='myblobdatastore'\r\n", "account_name=os.getenv(\"BLOB_ACCOUNTNAME_62\", \"wse2etesstoragee77dd4b5e\") # Storage account name\r\n", "container_name=os.getenv(\"BLOB_CONTAINER_62\", \"myblobdatastore\") # Name of Azure blob container\r\n", "account_key=os.getenv(\"BLOB_ACCOUNT_KEY_62\", \"[accountkey]\") # Storage account key\r\n", "\r\n", "try:\r\n", "    blob_datastore = Datastore.get(ws, blob_datastore_name)\r\n", "    print(\"Found Blob Datastore with name: %s\" % blob_datastore_name)\r\n", "except:\r\n", "    blob_datastore = Datastore.register_azure_blob_container(\r\n", "        subscription_id=subscription_id, \r\n", "        resource_group=resource_group, \r\n", "        workspace=ws,\r\n", "        datastore_name=blob_datastore_name,\r\n", "        account_name=account_name, # Storage account name\r\n", "        container_name=container_name, # Name of Azure blob container\r\n", "        account_key=account_key) # Storage account key\r\n", "    print(\"Registered blob datastore with name: %s\" % blob_datastore_name)"], "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Found Blob Datastore with name: myblobdatastore\n"]}], "execution_count": 4, "metadata": {"collapsed": true, "jupyter": {"source_hidden": false, "outputs_hidden": false}, "nteract": {"transient": {"deleting": false}}, "gather": {"logged": *************}}}, {"cell_type": "code", "source": ["## create for register tabular dataset, using datastore path\r\n", "dataset_name = \"ESCloudTestTabularData\"\r\n", "datastore_paths = [(blob_datastore, 'ESCloudTests/testdata.csv')]\r\n", "try:\r\n", "    ESCloudTestTabularData_ds = Dataset.get_by_name(ws, dataset_name)\r\n", "    print(\"Found dataset with name: %s\" % dataset_name)\r\n", "except:\r\n", "    ESCloudTestTabularData_ds = Dataset.Tabular.from_delimited_files(path=datastore_paths)\r\n", "    ESCloudTestTabularData_ds = ESCloudTestTabularData_ds.register(workspace=ws,\r\n", "                                 name=dataset_name,\r\n", "                                 description='titanic training data')\r\n", "    print(\"Creted/Register dataset with name: %s\" % dataset_name)"], "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Creted/Register dataset with name: ESCloudTestTabularData_chw\n"]}], "execution_count": 11, "metadata": {"collapsed": true, "jupyter": {"source_hidden": false, "outputs_hidden": false}, "nteract": {"transient": {"deleting": false}}, "gather": {"logged": 1630097460471}}}, {"cell_type": "code", "source": ["## create for register File dataset, using datastore path\r\n", "\r\n", "datastore = Datastore(workspace=ws, name=\"workspaceblobstore\")\r\n", "datastore_paths = [(datastore, 'azureml')]\r\n", "workspaceblobstore_ds = Dataset.File.from_files(path=datastore_paths)\r\n", "\r\n", "\r\n", "try:    \r\n", "    ret_workspaceblobstore_ds = Dataset.get_by_name(workspace=ws, name='testBlobDataset')\r\n", "    print(\"find dataset - testBlobDataset\")\r\n", "except:    \r\n", "    ret_workspaceblobstore_ds = workspaceblobstore_ds.register(workspace=ws,\r\n", "                                 name='testBlobDataset',\r\n", "                                 description='For wse2etests')\r\n", "    print(\"create dataset - testBlobDataset\")\r\n", "print(ret_workspaceblobstore_ds)\r\n"], "outputs": [{"output_type": "stream", "name": "stdout", "text": ["create dataset - testBlobDataset\n", "FileDataset\n", "{\n", "  \"source\": [\n", "    \"('workspaceblobstore', 'azureml')\"\n", "  ],\n", "  \"definition\": [\n", "    \"GetDatastoreFiles\"\n", "  ],\n", "  \"registration\": {\n", "    \"id\": \"06590f9a-c219-449b-a934-fa9787117d98\",\n", "    \"name\": \"testBlobDataset\",\n", "    \"version\": 1,\n", "    \"description\": \"For wse2etests\",\n", "    \"workspace\": \"Workspace.create(name='wse2etests', subscription_id='b8c23406-f9b5-4ccb-8a65-a8cb5dcd6a5a', resource_group='rge2etests')\"\n", "  }\n", "}\n"]}], "execution_count": 7, "metadata": {"collapsed": true, "jupyter": {"source_hidden": false, "outputs_hidden": false}, "nteract": {"transient": {"deleting": false}}, "gather": {"logged": 1629147272147}}}, {"cell_type": "code", "source": ["###   create ADF \r\n", "data_factory_name = 'adftest'\r\n", "\r\n", "def get_or_create_data_factory(workspace, factory_name):\r\n", "    try:\r\n", "        return DataFactoryCompute(workspace, factory_name)\r\n", "    except ComputeTargetException as e:\r\n", "        if 'ComputeTargetNotFound' in e.message:\r\n", "            print('Data factory not found, creating...')\r\n", "            provisioning_config = DataFactoryCompute.provisioning_configuration()\r\n", "            data_factory = ComputeTarget.create(workspace, factory_name, provisioning_config)\r\n", "            data_factory.wait_for_completion()\r\n", "            return data_factory\r\n", "        else:\r\n", "            raise e\r\n", "            \r\n", "data_factory_compute = get_or_create_data_factory(ws, data_factory_name)\r\n", "\r\n", "print(\"Setup Azure Data Factory account complete\")"], "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Data factory not found, creating...\n", "Provisioning operation finished, operation \"Succeeded\"\n", "Setup Azure Data Factory account complete\n"]}], "execution_count": 9, "metadata": {"collapsed": true, "jupyter": {"source_hidden": false, "outputs_hidden": false}, "nteract": {"transient": {"deleting": false}}, "gather": {"logged": *************}}}, {"cell_type": "code", "source": ["###   create ADF \r\n", "data_factory_name = 'dfe2etests'\r\n", "\r\n", "def get_or_create_data_factory(workspace, factory_name):\r\n", "    try:\r\n", "        return DataFactoryCompute(workspace, factory_name)\r\n", "    except ComputeTargetException as e:\r\n", "        if 'ComputeTargetNotFound' in e.message:\r\n", "            print('Data factory not found, creating...')\r\n", "            provisioning_config = DataFactoryCompute.provisioning_configuration()\r\n", "            data_factory = ComputeTarget.create(workspace, factory_name, provisioning_config)\r\n", "            data_factory.wait_for_completion()\r\n", "            return data_factory\r\n", "        else:\r\n", "            raise e\r\n", "            \r\n", "data_factory_compute = get_or_create_data_factory(ws, data_factory_name)\r\n", "\r\n", "print(\"Setup Azure Data Factory account complete\")"], "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Setup Azure Data Factory account complete\n"]}], "execution_count": 11, "metadata": {"collapsed": true, "jupyter": {"source_hidden": false, "outputs_hidden": false}, "nteract": {"transient": {"deleting": false}}, "gather": {"logged": *************}}}, {"cell_type": "code", "source": ["## create AddAndMultiply module: https://github.com/Azure/MachineLearningNotebooks/blob/master/how-to-use-azureml/machine-learning-pipelines/intro-to-pipelines/aml-pipelines-how-to-use-modulestep.ipynb\r\n", "\r\n", "datastore = Datastore(workspace=ws, name=\"workspaceblobstore\")\r\n", "\r\n", "module = Module.create(ws, name=\"AddAndMultiply\", description=\"A module that adds and multiplies\")\r\n", "\r\n", "out_sum = OutputPortDef(name=\"out_sum\", default_datastore_name=datastore.name, default_datastore_mode=\"mount\", \r\n", "                        label=\"Sum of two numbers\")\r\n", "out_prod = OutputPortDef(name=\"out_prod\", default_datastore_name=datastore.name, default_datastore_mode=\"mount\", \r\n", "                         label=\"Product of two numbers\")\r\n", "entry_version = module.publish_python_script(\"calculate.py\", \"initial\", \r\n", "                                             inputs=[], outputs=[out_sum, out_prod], params = {\"initialNum\":12},\r\n", "                                             version=\"1\", source_directory=\"./calc\")\r\n", "\r\n", "\r\n", "in1_mid = InputPortDef(name=\"in1\", default_datastore_mode=\"mount\", \r\n", "default_data_reference_name=datastore.name, label=\"First input number\")\r\n", "in2_mid = InputPortDef(name=\"in2\", default_datastore_mode=\"mount\", \r\n", "default_data_reference_name=datastore.name, label=\"Second input number\")\r\n", "out_sum_mid = OutputPortDef(name=\"out_sum\", default_datastore_name=datastore.name, default_datastore_mode=\"mount\",\r\n", "                            label=\"Sum of two numbers\")\r\n", "out_prod_mid = OutputPortDef(name=\"out_prod\", default_datastore_name=datastore.name, default_datastore_mode=\"mount\",\r\n", "                             label=\"Product of two numbers\")\r\n", "module.publish_python_script(\r\n", "    \"calculate.py\", \"middle\", inputs=[in1_mid, in2_mid], outputs=[out_sum_mid, out_prod_mid], version=\"2\", is_default=True, \r\n", "    source_directory=\"./calc\")\r\n"], "outputs": [{"output_type": "error", "ename": "ErrorResponseException", "evalue": "(UserError) Response status code does not indicate success: 409 (Conflict).\nMicrosoft.RelInfra.Common.Exceptions.ErrorResponseException: Conflict name exists for AzureMLModule named AddAndMultiply", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mErrorResponseException\u001b[0m                    <PERSON><PERSON> (most recent call last)", "\u001b[0;32m<ipython-input-15-9e954c88b22f>\u001b[0m in \u001b[0;36m<module>\u001b[0;34m\u001b[0m\n\u001b[1;32m      3\u001b[0m \u001b[0mdatastore\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mDatastore\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mworkspace\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mws\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mname\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;34m\"workspaceblobstore\"\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      4\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m----> 5\u001b[0;31m \u001b[0mmodule\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mModule\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mcreate\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mws\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mname\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;34m\"AddAndMultiply\"\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mdescription\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;34m\"A module that adds and multiplies\"\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m      6\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      7\u001b[0m out_sum = OutputPortDef(name=\"out_sum\", default_datastore_name=datastore.name, default_datastore_mode=\"mount\", \n", "\u001b[0;32m/anaconda/envs/azureml_py36/lib/python3.6/site-packages/azureml/pipeline/core/module.py\u001b[0m in \u001b[0;36mcreate\u001b[0;34m(workspace, name, description, _workflow_provider)\u001b[0m\n\u001b[1;32m    184\u001b[0m                                       workflow_provider=_workflow_provider)\n\u001b[1;32m    185\u001b[0m         \u001b[0mazure_ml_module_provider\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mgraph_context\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mworkflow_provider\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mazure_ml_module_provider\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 186\u001b[0;31m         \u001b[0mresult\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mazure_ml_module_provider\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mcreate_module\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mname\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mdescription\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    187\u001b[0m         \u001b[0;32mreturn\u001b[0m \u001b[0mresult\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    188\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/anaconda/envs/azureml_py36/lib/python3.6/site-packages/azureml/pipeline/core/_aeva_provider.py\u001b[0m in \u001b[0;36mcreate_module\u001b[0;34m(self, name, description)\u001b[0m\n\u001b[1;32m   2339\u001b[0m             \"\"\"\n\u001b[1;32m   2340\u001b[0m             \u001b[0mcreation_info\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mAzureMLModuleCreationInfo\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mname\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mname\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mdescription\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mdescription\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 2341\u001b[0;31m             \u001b[0mentity\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_service_caller\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mcreate_azure_ml_module_async\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mcreation_info\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m   2342\u001b[0m             return self.from_azure_ml_module_entity(entity, self._workspace, self,\n\u001b[1;32m   2343\u001b[0m                                                     self._module_version_provider)\n", "\u001b[0;32m/anaconda/envs/azureml_py36/lib/python3.6/site-packages/azureml/pipeline/core/_restclients/aeva/service_caller.py\u001b[0m in \u001b[0;36mcreate_azure_ml_module_async\u001b[0;34m(self, azure_ml_module_creation_info)\u001b[0m\n\u001b[1;32m    850\u001b[0m             \u001b[0msubscription_id\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_subscription_id\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mresource_group_name\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_resource_group_name\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    851\u001b[0m             \u001b[0mworkspace_name\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_workspace_name\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mcustom_headers\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_get_custom_headers\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 852\u001b[0;31m             creation_info=azure_ml_module_creation_info)\n\u001b[0m\u001b[1;32m    853\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    854\u001b[0m         \u001b[0;32mreturn\u001b[0m \u001b[0mresult\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/anaconda/envs/azureml_py36/lib/python3.6/site-packages/azureml/pipeline/core/_restclients/aeva/aml_pipelines_api10.py\u001b[0m in \u001b[0;36mcreate_azure_ml_module_async\u001b[0;34m(self, subscription_id, resource_group_name, workspace_name, creation_info, custom_headers, raw, **operation_config)\u001b[0m\n\u001b[1;32m    105\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    106\u001b[0m         \u001b[0;32mif\u001b[0m \u001b[0mresponse\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mstatus_code\u001b[0m \u001b[0;32mnot\u001b[0m \u001b[0;32min\u001b[0m \u001b[0;34m[\u001b[0m\u001b[0;36m200\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 107\u001b[0;31m             \u001b[0;32mraise\u001b[0m \u001b[0mErrorResponseException\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_deserialize\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mresponse\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    108\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    109\u001b[0m         \u001b[0mdeserialized\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;32mNone\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31mErrorResponseException\u001b[0m: (UserError) Response status code does not indicate success: 409 (Conflict).\nMicrosoft.RelInfra.Common.Exceptions.ErrorResponseException: Conflict name exists for AzureMLModule named AddAndMultiply"]}], "execution_count": 15, "metadata": {"collapsed": true, "jupyter": {"source_hidden": false, "outputs_hidden": false}, "nteract": {"transient": {"deleting": false}}}}, {"cell_type": "code", "source": ["## Use this for Azure AD authentication\r\n", "from msrestazure.azure_active_directory import AADTokenCredentials\r\n", "\r\n", "## Required for Azure Data Lake Storage Gen1 account management\r\n", "from azure.mgmt.datalake.store import DataLakeStoreAccountManagementClient\r\n", "from azure.mgmt.datalake.store.models import DataLakeStoreAccount\r\n", "\r\n", "## Required for Azure Data Lake Storage Gen1 filesystem management\r\n", "from azure.datalake.store import core, lib, multithread\r\n", "\r\n", " # Common Azure imports\r\n", "import adal\r\n", "from azure.mgmt.resource.resources import ResourceManagementClient\r\n", "from azure.mgmt.resource.resources.models import ResourceGroup\r\n", "\r\n", " ## Use these as needed for your application\r\n", "import logging, pprint, uuid, time\r\n", "\r\n", "authority_host_url = \"https://login.microsoftonline.com\"\r\n", "tenant = \"72f988bf-86f1-41af-91ab-2d7cd011db47\"\r\n", "authority_url = authority_host_url + '/' + tenant\r\n", "client_id = '33d97d2d-c9bb-43f2-b569-485c7f363c08'\r\n", "redirect = 'urn:ietf:wg:oauth:2.0:oob'\r\n", "RESOURCE = 'https://management.core.windows.net/'\r\n", "\r\n", "context = adal.AuthenticationContext(authority_url)\r\n", "code = context.acquire_user_code(RESOURCE, client_id)\r\n", "print(code['message'])\r\n", "mgmt_token = context.acquire_token_with_device_code(RESOURCE, code, client_id)\r\n", "armCreds = AADTokenCredentials(mgmt_token, client_id, resource = RESOURCE)\r\n", "\r\n", "\r\n", "\r\n", "subscriptionId = 'b8c23406-f9b5-4ccb-8a65-a8cb5dcd6a5a'\r\n", "adlsAccountName = 'wse2etestsadlsgen1'\r\n", "resourceGroup = 'rge2etests'\r\n", "location = 'centraluseuap'\r\n", "\r\n", "## Create Data Lake Storage Gen1 account management client object\r\n", "adlsAcctClient = DataLakeStoreAccountManagementClient(armCreds, subscriptionId)\r\n", "\r\n", "## Create a Data Lake Storage Gen1 account\r\n", "adlsAcctResult = adlsAcctClient.account.create(\r\n", "\tresourceGroup,\r\n", "\tadlsAccountName,\r\n", "\tDataLakeStoreAccount(\r\n", "\t\tlocation=location\r\n", "\t)\r\n", ").wait()"], "outputs": [], "execution_count": null, "metadata": {"collapsed": true, "jupyter": {"source_hidden": false, "outputs_hidden": false}, "nteract": {"transient": {"deleting": false}}}}, {"cell_type": "code", "source": ["##  register datastore \"myadlsdatastore\"\r\n", "\r\n", "from azureml.exceptions import UserErrorException\r\n", "\r\n", "datastore_name='myadlsdatastore'\r\n", "\r\n", "subscription_id=os.getenv(\"ADL_SUBSCRIPTION_62\", \"b8c23406-f9b5-4ccb-8a65-a8cb5dcd6a5a\") # subscription id of ADLS account\r\n", "resource_group=os.getenv(\"ADL_RESOURCE_GROUP_62\", \"aesviennatesteuap\") # resource group of ADLS account\r\n", "store_name=os.getenv(\"ADL_STORENAME_62\", \"testadl7d0648738301c9f3\") # ADLS account name\r\n", "tenant_id=os.getenv(\"ADL_TENANT_62\", tenant_id) # tenant id of service principal\r\n", "client_id=os.getenv(\"ADL_CLIENTID_62\", client_id) # client id of service principal\r\n", "client_secret_placeholder=os.getenv(\"ADL_CLIENT_SECRET_PLACEHOLDER_62\", client_secret_placeholder) # the secret of service principal\r\n", "\r\n", "adls_datastore = Datastore.register_azure_data_lake(\r\n", "    workspace=ws,\r\n", "    datastore_name=datastore_name,\r\n", "    subscription_id=subscription_id, # subscription id of ADLS account\r\n", "    resource_group=resource_group, # resource group of ADLS account\r\n", "    store_name=store_name, # ADLS account name\r\n", "    tenant_id=tenant_id, # tenant id of service principal\r\n", "    client_id=client_id, # client id of service principal\r\n", "    client_secret_placeholder=client_secret_placeholder) # the secret of service principal\r\n", "print(\"Registered datastore with name: %s\" % datastore_name)"], "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Registered datastore with name: myadlsdatastore\n"]}], "execution_count": 21, "metadata": {"collapsed": true, "jupyter": {"source_hidden": false, "outputs_hidden": false}, "nteract": {"transient": {"deleting": false}}, "gather": {"logged": *************}}}, {"cell_type": "code", "source": ["#  Register adls gen2 \"wse2etestsadlsgen2\" as datastore \"testadlsgen2datastore\"\r\n", "\r\n", "subscription_id=os.getenv(\"ADL_SUBSCRIPTION_62\", \"b8c23406-f9b5-4ccb-8a65-a8cb5dcd6a5a\") # subscription id of ADLS account\r\n", "resource_group=os.getenv(\"ADL_RESOURCE_GROUP_62\", \"rge2etests\") # resource group of ADLS account\r\n", "\r\n", "adlsgen2_datastore_name = 'testadlsgen2datastore'\r\n", "account_name = \"wse2etestsadlsgen2\"\r\n", "\r\n", "account_name=os.getenv(\"ADLSGEN2_ACCOUNTNAME_62\", account_name) # ADLS Gen2 account name\r\n", "tenant_id=os.getenv(\"ADLSGEN2_TENANT_62\", tenant_id) # tenant id of service principal\r\n", "client_id=os.getenv(\"ADLSGEN2_CLIENTID_62\", client_id) # client id of service principal\r\n", "client_secret_placeholder=os.getenv(\"ADLSGEN2_CLIENT_SECRET_PLACEHOLDER_62\", client_secret_placeholder) # the secret of service principal\r\n", "\r\n", "try:\r\n", "    adlsgen2_datastore = Datastore.get(ws, adlsgen2_datastore_name)\r\n", "    print(\"Found ADLS Gen2 datastore with name: %s\" % adlsgen2_datastore_name)\r\n", "except:\r\n", "    adlsgen2_datastore = Datastore.register_azure_data_lake_gen2(\r\n", "        subscription_id = subscription_id,\r\n", "        resource_group = resource_group,\r\n", "        workspace=ws,\r\n", "        datastore_name=adlsgen2_datastore_name,\r\n", "        filesystem='test', # Name of ADLS Gen2 filesystem\r\n", "        account_name=account_name, # ADLS Gen2 account name\r\n", "        tenant_id=tenant_id, # tenant id of service principal\r\n", "        client_id=client_id, # client id of service principal\r\n", "        client_secret_placeholder=client_secret_placeholder) # the secret of service principal\r\n", "    print(\"Registered datastore with name: %s\" % adlsgen2_datastore_name)"], "outputs": [], "execution_count": null, "metadata": {"collapsed": true, "jupyter": {"source_hidden": false, "outputs_hidden": false}, "nteract": {"transient": {"deleting": false}}}}, {"cell_type": "code", "source": ["# create datastore \"juliamigratedadls\"\r\n", "\r\n", "datastore_name = \"juliamigratedadls\"\r\n", "store_name = \"testadl7d0648738301c9f3\"\r\n", "\r\n", "try:\r\n", "    adls_datastore = Datastore.get(ws, datastore_name)\r\n", "    print(\"Found ADLS datastore with name: %s\" % datastore_name)\r\n", "except:\r\n", "    \r\n", "    adls_datastore = Datastore.register_azure_data_lake(\r\n", "        workspace=ws,\r\n", "        datastore_name=datastore_name,\r\n", "        subscription_id=subscription_id, # subscription id of ADLS account\r\n", "        resource_group=resource_group, # resource group of ADLS account\r\n", "        store_name=store_name, # ADLS account name\r\n", "        tenant_id=tenant_id, # tenant id of service principal\r\n", "        client_id=client_id, # client id of service principal\r\n", "        client_secret_placeholder=client_secret_placeholder) # the secret of service principal\r\n", "    print(\"Registered datastore with name: %s\" % datastore_name)\r\n", "\r\n"], "outputs": [], "execution_count": null, "metadata": {"collapsed": true, "jupyter": {"source_hidden": false, "outputs_hidden": false}, "nteract": {"transient": {"deleting": false}}}}], "metadata": {"kernelspec": {"name": "python3", "language": "python", "display_name": "Python 3"}, "language_info": {"name": "python", "version": "3.6.9", "mimetype": "text/x-python", "codemirror_mode": {"name": "ipython", "version": 3}, "pygments_lexer": "ipython3", "nbconvert_exporter": "python", "file_extension": ".py"}, "kernel_info": {"name": "python3"}, "nteract": {"version": "nteract-front-end@1.0.0"}, "microsoft": {"host": {"AzureML": {"notebookHasBeenCompleted": true}}}}, "nbformat": 4, "nbformat_minor": 2}