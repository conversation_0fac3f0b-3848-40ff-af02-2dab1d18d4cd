﻿using BBAetherLibrary.IntegrationTests.Client;
using BBAetherLibrary.IntegrationTests.Common;
using Microsoft.Aether.AEVA.DataContracts;
using Microsoft.Aether.BlueBox.Library;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace BBAetherLibrary.IntegrationTests.TestFixtures
{
    [TestFixture]
    [Parallelizable(ParallelScope.All)]
    public class PipelineEndpointTests : BBAetherTestFixtureBase
    {
        private static string _pipelineId = "f661af6c-9ce2-4967-91e0-85a26bbd807c";
        private RunHistoryApiCaller _runHistoryApiCallerEndpoint;

        [OneTimeSetUp]
        public void Init()
        {
            _runHistoryApiCallerEndpoint = new RunHistoryApiCaller(_testConfig.SubscriptionId, _testConfig.ResourceGroup, _testConfig.WorkspaceName, accessTokenProvider: _tokenProvider.GetAccessTokenAsync);
        }

        [Test]
        public void TestPipelineEndpointCreateException()
        {
            PipelineEndpointCreationInfo pipelineEndpointCreationInfo = new PipelineEndpointCreationInfo("testPub", "pipelineEndpoint Description", _pipelineId);
            AetherServiceException ex = Assert.ThrowsAsync<AetherServiceException>(async () => await _testEnvironment.CreatePipelineEndpointAsync(pipelineEndpointCreationInfo));
        }

        //[Test] create once for testing
        public async Task TestPipelineEndpointCreateAsync()
        {
            PipelineEndpointCreationInfo pipelineEndpointCreationInfo = new PipelineEndpointCreationInfo("testPub", "pipelineEndpoint Description", _pipelineId);
            PipelineEndpointEntity pipelineEndpoint = await _testEnvironment.CreatePipelineEndpointAsync(pipelineEndpointCreationInfo);
            var pid = pipelineEndpoint.PipelineVersionList.Find(version => version.Version == "0").PipelineId;
            Assert.AreEqual(_pipelineId, pid, $"PipelineEndpoint with id: {pipelineEndpoint.Id}");
        }

        [Test]
        public async Task TestPipelineEndpointGetByIdAsync()
        {
            var id = "84fc2186-c44f-4f2c-8b5f-a1c3311e5f2d";
            PipelineEndpointEntity pipelineEndpoint = await _testEnvironment.GetPipelineEndpointByIdAsync(id);

            var pid = pipelineEndpoint.PipelineVersionList.Find(version => version.Version == "0").PipelineId;
            Assert.AreEqual(_pipelineId, pid, $"PipelineEndpoint with id: {pipelineEndpoint.Id}");
        }

        [Test]
        public async Task TestPipelineEndpointGetByIdAsyncForSwaggerUrl()
        {
            var id = "84fc2186-c44f-4f2c-8b5f-a1c3311e5f2d";
            PipelineEndpointEntity pipelineEndpoint = await _testEnvironment.GetPipelineEndpointByIdAsync(id);
            Assert.IsNotEmpty(pipelineEndpoint.SwaggerUrl, $"Swagger url is Null");
            Assert.IsTrue(pipelineEndpoint.SwaggerUrl.ToLower().StartsWith("http"), $"Swagger url is not starting with http");
        }

        [Test]
        public async Task TestPipelineEndpointGetByIdAsyncForSwaggerUrlWithHttp()
        {
            var id = "84fc2186-c44f-4f2c-8b5f-a1c3311e5f2d";
            PipelineEndpointEntity pipelineEndpoint = await _testEnvironment.GetPipelineEndpointByIdAsync(id);
            Assert.IsTrue(pipelineEndpoint.SwaggerUrl.ToLower().StartsWith("http"), $"Swagger url is not starting with http");
        }

        [Test]
        public async Task TestPipelineEndpointGetByIdAsyncForSwaggerUrlStartWithHttp()
        {
            var id = "84fc2186-c44f-4f2c-8b5f-a1c3311e5f2d";
            PipelineEndpointEntity pipelineEndpoint = await _testEnvironment.GetPipelineEndpointByIdAsync(id);
            Assert.IsTrue(pipelineEndpoint.SwaggerUrl.ToLower().StartsWith("http"), $"Swagger url is not starting with http");
        }

        [Test]
        public async Task TestPipelineEndpointGetByNameAsync()
        {
            var name = "TestSubmitSavedPipelineRun";
            PipelineEndpointEntity pipelineEndpoint = await _testEnvironment.GetPipelineEndpointByNameAsync(name);

            var pid = pipelineEndpoint.PipelineVersionList.Find(version => version.Version == "0").PipelineId;
            Assert.AreEqual(_pipelineId, pid, $"PipelineEndpoint with id: {pipelineEndpoint.Id}");
        }

        [Test]
        public async Task TestPipelineEndpointGetByNameAsyncSwaggerUrl()
        {
            var name = "TestSubmitSavedPipelineRun";
            PipelineEndpointEntity pipelineEndpoint = await _testEnvironment.GetPipelineEndpointByNameAsync(name);
            Assert.IsNotEmpty(pipelineEndpoint.SwaggerUrl, $"Swagger url is Null");
            Assert.IsTrue(pipelineEndpoint.SwaggerUrl.ToLower().StartsWith("http"), $"Swagger url is not starting with http");
        }

        [Test]
        public async Task TestPipelineEndpointGetByNameAsyncSwaggerUrlStartWithHttp()
        {
            var name = "TestSubmitSavedPipelineRun";
            PipelineEndpointEntity pipelineEndpoint = await _testEnvironment.GetPipelineEndpointByNameAsync(name);
            Assert.IsTrue(pipelineEndpoint.SwaggerUrl.ToLower().StartsWith("http"), $"Swagger url is not starting with http");
        }

        [Test]
        public async Task TestPipelineEndpointListInPipelineUpdate()
        {
            var name = "TestSubmitSavedPipelineRun";
            var pid = "f661af6c-9ce2-4967-91e0-85a26bbd807c";
            PipelineEndpointEntity pipelineEndpoint = await _testEnvironment.GetPipelineEndpointByNameAsync(name);

            PipelineEntity pipeline = await _testEnvironment.GetPipeline(pid);
            Assert.AreEqual(pipeline.PipelineEndpointIds.Contains(pipelineEndpoint.Id), true, $"Check PipelineEndpoint in Pipeline");
        }

        //[Test]
        public async Task TestPipelineEndpoinUpdateSwaggerUrl()
        {
            var name = "TestSubmitSavedPipelineRun";
            PipelineEndpointEntity pipelineEndpoint = await _testEnvironment.GetPipelineEndpointByNameAsync(name);
            PipelineEndpointEntity pipelineEndpointUpdate = await _testEnvironment.UpdatePipelineEndpointAsync(pipelineEndpoint.Id, pipelineEndpoint);
            Assert.IsNotEmpty(pipelineEndpointUpdate.SwaggerUrl, $"Swagger url is Null");
        }

        //[Test]
        public async Task TestPipelineEndpoinUpdateSwaggerUrlWithHttp()
        {
            var name = "TestSubmitSavedPipelineRun";
            PipelineEndpointEntity pipelineEndpoint = await _testEnvironment.GetPipelineEndpointByNameAsync(name);
            PipelineEndpointEntity pipelineEndpointUpdate = await _testEnvironment.UpdatePipelineEndpointAsync(pipelineEndpoint.Id, pipelineEndpoint);
            Assert.IsTrue(pipelineEndpointUpdate.SwaggerUrl.ToLower().StartsWith("http"), $"Swagger url is not starting with http");
        }


        [Test]
        public async Task TestSubmitPipelineFromPipelineEndpointByIdAsync()
        {
            var id = "84fc2186-c44f-4f2c-8b5f-a1c3311e5f2d";
            PipelineEndpointEntity pipelineEndpoint = await _testEnvironment.GetPipelineEndpointByIdAsync(id);
            var customRunId = Guid.NewGuid().ToString();
            var submissionInfo = new PipelineSubmissionInfo
            {
                RunId = customRunId,
                ExperimentName = "test",
                DisplayName = "BBAetherLibraryTestPipelineEndpoint",
                Description = "BBAetherLibrary Test pipelineEndpoint",
                ParameterAssignments = new Dictionary<string, string> { { "1", "chelsea" }, { "2", "united" }, { "3", "city" }, { "4", "pool" } },
                ContinueRunOnStepFailure = true,
            };
            Console.WriteLine($"submitting with custom runid {submissionInfo.RunId}");
            var version = "0";
            var runId = await _testEnvironment.SubmitPipelineRunFromPipelineEndpointByIdAsync(pipelineEndpoint.Id, submissionInfo, version).ConfigureAwait(false);
            Assert.AreEqual(customRunId, runId);
            //var runId = "f50b2c78-2743-485c-9474-983a4c08f36e";
            IPipelineRun pipelineRun = await FetchExperimentOnceCompleteAsync(
                 environment: _testEnvironment,
                 pipelineRunId: runId);

            Assert.AreEqual(PipelineRunStatusCode.Finished, pipelineRun.CachedStatus, $"experiment id: {pipelineRun.Id}");

            var pipelineRunEntity = await _testEnvironment.GetPipelineRunEntityAsync(runId).ConfigureAwait(false);
            var runDto = await _runHistoryApiCallerEndpoint.GetRunDto("test", pipelineRunEntity.PipelineRunId).ConfigureAwait(false);
            Assert.AreEqual("BBAetherLibraryTestPipelineEndpoint", pipelineRunEntity.DisplayName);
            Assert.AreEqual("True", runDto.Properties["azureml.continue_on_step_failure"]);
        }

        [Test]
        public async Task TestVersionNotExistCaseForSubmitPipelineFromPipelineEndpointByIdAsync()
        {
            var id = "84fc2186-c44f-4f2c-8b5f-a1c3311e5f2d";
            var version = "100";
            var submissionInfo = new PipelineSubmissionInfo
            {
                ExperimentName = "TestForFailureCase"
            };
            try
            {
                await _testEnvironment.SubmitPipelineRunFromPipelineEndpointByIdAsync(id, submissionInfo, version).ConfigureAwait(false);
            }
            catch (Exception e)
            {
                StringAssert.Contains("BadRequest", e.Message);
                StringAssert.Contains("Pipeline with version 100, not found in pipelineEndpoint 84fc2186-c44f-4f2c-8b5f-a1c3311e5f2d", e.Message);
            }
        }

        [Test]
        public async Task TestSubmitPipelineFromPipelineEndpointByNameAsync()
        {
            var name = "TestSubmitSavedPipelineRun";
            PipelineEndpointEntity pipelineEndpoint = await _testEnvironment.GetPipelineEndpointByNameAsync(name);
            var customRunId = Guid.NewGuid().ToString();
            var submissionInfo = new PipelineSubmissionInfo
            {
                RunId = customRunId,
                ExperimentName = "experimentName",
                Description = "BBAetherLibrary Test pipelineEndpoint",
                RunSource = "BBAetherLibrary",
                RunType = RunType.HTTP,
                ParameterAssignments = new Dictionary<string, string> { { "1", "chelsea" }, { "2", "united" }, { "3", "city" }, { "4", "pool" } }
            };
            var version = "0";
            var runId = await _testEnvironment.SubmitPipelineRunFromPipelineEndpointByNameAsync(pipelineEndpoint.Name, submissionInfo, version).ConfigureAwait(false);
            Assert.AreEqual(customRunId, runId);
            // This run cannot finish for unknow reason, may investigate it later.
            // IPipelineRun experiment = await _testEnvironment.GetPipelineRunAsync(runId);
            // Assert.AreEqual(PipelineRunStatusCode.Finished, experiment.CachedStatus, $"experiment id: {experiment.Id}");
        }
    }
}
