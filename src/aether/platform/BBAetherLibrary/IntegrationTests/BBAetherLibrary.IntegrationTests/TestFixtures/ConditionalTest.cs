using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using BBAetherLibrary.IntegrationTests.Common;
using Microsoft.Aether.AEVA.DataContracts;
using Microsoft.Aether.BlueBox.Library;
using NUnit.Framework;

namespace BBAetherLibrary.IntegrationTests.TestFixtures
{
    [TestFixture]
    [Parallelizable(ParallelScope.All)]
    // TODO: This test should rely on a real control output value but not on the default values, it's missing due to 
    // external dependencies running behind
    internal sealed class ConditionalTest : BBAetherTestFixtureBase
    {
        private const string DataTypeName = "AnyFile";

        [Test]
        [Ignore("Conditionals not available to users and test takes too long")]
        public async Task TestConditionalBypass()
        {
            IAetherModule copyModule = await UploadESCloudCopyModuleAsync(_testEnvironment);

            IAetherDataSource dataSource = await _testEnvironment.GetDataSourceAsync(
                dataSetDefinition: new DataSetDefinition
                {
                    DataTypeShortName = DataTypeName,
                    Value = new DataSetDefinitionValue
                    {
                        LiteralValue = new DataPath
                        {
                            DataStoreName = _testConfig.BlobDatastoreName,
                            RelativePath = "testdata1"
                        }
                    }
                });

            IGraph graph = _testEnvironment.CreateNewGraph();
            IDataSourceNode inputNode = graph.AddNode(dataSource);
            List<OutputSetting> outputSettings = GetOutputUploadSettings();
            IModuleNode m1 = graph.AddNode(copyModule, moduleOutputSettings: outputSettings);
            IModuleNode m2 = graph.AddNode(copyModule, moduleOutputSettings: outputSettings);
            IModuleNode m3 = graph.AddNode(copyModule, moduleOutputSettings: outputSettings);

            graph.Connect(inputNode, m1.InputPortDictionary["input"]);
            graph.Connect(m1.OutputPortDictionary["output"], m2.InputPortDictionary["input"]);
            graph.Connect(m1.OutputPortDictionary["output"], m3.InputPortDictionary["input"]);

            // control edge for m2
            var m2controlInput = new ControlInput
            {
                Name = "control_input",
                DefaultValue = ControlInputValue.False
            };
            m2.AddControlInput(m2controlInput);
            graph.ConnectControlInput(
                fromNode: m1,
                controlOutputName: "control_output",
                toNode: m2,
                controlInputName: m2controlInput.Name);

            // control edge for m3
            var m3controlInput = new ControlInput
            {
                Name = "control_input",
                DefaultValue = ControlInputValue.True
            };
            m3.AddControlInput(m3controlInput);
            graph.ConnectControlInput(
                fromNode: m1,
                controlOutputName: "control_output",
                toNode: m3,
                controlInputName: m2controlInput.Name);

            IPipelineRun pipelineRun = await RunTestWithLogsAsync(
                graph,
                _testEnvironment);

            Assert.AreEqual(
                expected: PipelineRunStatusCode.Finished,
                actual: pipelineRun.CachedStatus,
                message: $"Experiment with id {pipelineRun.Id} has unexpected status {pipelineRun.CachedStatus}");

            IExecutionGraph executionGraph = await pipelineRun.GetExecutionGraphAsync();
            KeyValuePair<string, TaskStatusCode>[] nodeStatuses =
                await executionGraph.GetAllNodesExecutionStatusAsync();

            Assert.AreEqual(
                expected: TaskStatusCode.Finished,
                actual: nodeStatuses.First(pair => string.Equals(pair.Key, m1.Id)).Value);

            Assert.AreEqual(
                expected: TaskStatusCode.Bypassed,
                actual: nodeStatuses.First(pair => string.Equals(pair.Key, m2.Id)).Value);

            Assert.AreEqual(
                expected: TaskStatusCode.Finished,
                actual: nodeStatuses.First(pair => string.Equals(pair.Key, m3.Id)).Value);
        }

        private async Task<IAetherModule> UploadESCloudCopyModuleAsync(AetherEnvironment environment)
        {
            const string condaDependencies =
@"{
	""name"": ""project_environment"",
	""dependencies"": [
		""python = 3.8"",
		{
			""pip"": [
				""azureml-sdk"",
				""azureml-dataprep""
			],
			""conda"": [
				""pandas"",
				""scikit-learn""
			]
		}
	]
}";

            // create simple copy module
            var structuredInterface = new StructuredInterface
            {
                MetadataParameters = new List<StructuredInterfaceParameter>
                {
                    new StructuredInterfaceParameter
                    {
                        Name = "TargetType" ,
                        DefaultValue = "MLC",
                        ParameterType = ParameterType.String,
                        IsOptional = false
                    },
                },
                Parameters = new List<StructuredInterfaceParameter>
                {
                    new StructuredInterfaceParameter { Name = "Target" , DefaultValue =  _testConfig.AmlComputeName, ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "PrepareEnvironment" , DefaultValue = "true", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "Script" , DefaultValue = "main.py", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "Arguments" , DefaultValue = "--in,$AZUREML_DATAREFERENCE_in,--out,$AZUREML_DATAREFERENCE_out", ParameterType = ParameterType.String, IsOptional = true},
                    new StructuredInterfaceParameter { Name = "Framework" , DefaultValue = "Python", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "UserManagedDependencies" , DefaultValue = "false", ParameterType = ParameterType.String, IsOptional = true},
                    new StructuredInterfaceParameter { Name = "CondaDependencies" , DefaultValue = condaDependencies, ParameterType = ParameterType.String, IsOptional = true},
                    new StructuredInterfaceParameter { Name = "DockerEnabled" , DefaultValue = "true", ParameterType = ParameterType.String, IsOptional = true},
                    new StructuredInterfaceParameter { Name = "BaseDockerImage" , DefaultValue = "mcr.microsoft.com/azureml/base:0.2.1", ParameterType = ParameterType.String, IsOptional = true},
                    new StructuredInterfaceParameter { Name = "NodeCount" , DefaultValue = "1", ParameterType = ParameterType.String, IsOptional = true},
                },
                Inputs = new List<StructuredInterfaceInput>
                {
                    new StructuredInterfaceInput
                    {
                        Name = "input",
                        DataTypeIdsList = new List<string>{ DataTypeName },
                        DataStoreMode = DataStoreMode.Mount,
                        DataReferenceName = "in"
                    },
                },
                Outputs = new List<StructuredInterfaceOutput>
                {
                    new StructuredInterfaceOutput
                    {
                        Name = "output",
                        DataTypeId = DataTypeName,
                        DataStoreMode = DataStoreMode.Mount,
                        DataStoreName = _testConfig.BlobDatastoreName,
                        DataReferenceName = "out",
                        PathOnCompute = "/output",
                        Overwrite = true
                    }
                },
                ControlOutputs = new List<ControlOutput>
                {
                    new ControlOutput
                    {
                        Name = "control_output"
                    }
                }
            };

            var moduleUploadInfo = new ModuleUploadInfo(
               name: "escloud test copy module",
               displayName: "escloud test copy module display",
               description: "escloud test copy module",
               isDeterministic: false,
               cloudSystem: "escloud",
               structuredInterface: structuredInterface);


            string moduleId = await environment.UploadModuleAsync(
                sourcePath: "TestResources/EscloudFile/copy",
                uploadInfo: moduleUploadInfo);

            return await _testEnvironment.GetModuleAsync(moduleId);
        }

        private List<OutputSetting> GetOutputUploadSettings()
        {
            return new List<OutputSetting> {
                new OutputSetting {
                    Name = "output",
                    DataStoreName = _testConfig.BlobDatastoreName,
                    DataStoreMode = DataStoreMode.Mount,
                    Overwrite = true,
                    DataReferenceName = "out",
                    PathOnCompute = "/output"
                }
            };
        }
    }
}