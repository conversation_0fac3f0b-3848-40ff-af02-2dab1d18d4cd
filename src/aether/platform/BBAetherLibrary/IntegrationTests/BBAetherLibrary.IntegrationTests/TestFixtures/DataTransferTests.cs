﻿using BBAetherLibrary.IntegrationTests.Common;
using Microsoft.Aether.AEVA.DataContracts;
using Microsoft.Aether.BlueBox.Library;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace BBAetherLibrary.IntegrationTests.TestFixtures
{
    [TestFixture]
    [Parallelizable(ParallelScope.All)]
    public class DataTransferTests : BBAetherTestFixtureBase
    {
        private const string ModulePath = "TestResources/OneEmptyFile";
        private const string SourceInputName = "Input";
        private const string DestinationInputName = "Output";
        private const string OutputName = "Output";
        private const string AzureBlobReference = "AzureBlobReference";

        private const string DataFactoryResourceIdPattern = "^/subscriptions/(?<subscription>[^/]*)/resourceGroups/(?<resourceGroup>[^/]*)/providers/Microsoft.DataFactory/factories/(?<factoryName>[^/]*)$";
        private static readonly Regex DataFactoryResourceIdRegex = new Regex(DataFactoryResourceIdPattern, RegexOptions.IgnoreCase | RegexOptions.Compiled);

        [Test]
        public async Task TestDataTransferCompatAsync()
        {
            var inputDataSourceId = await DataStoreUtils.CreateDataStoreDataSourceAsync(
                _testEnvironment,
                name: "Blob1",
                description: "Blob1",
                amlDataStoreName: _testConfig.BlobDatastoreName,
                pathOnDataStore: "folder",
                identifierHash: Guid.NewGuid().ToString(),
                contentHash: null);

            var outputDataSourceId = await DataStoreUtils.CreateDataStoreDataSourceAsync(
                _testEnvironment,
                name: "Blob3",
                description: "Blob3",
                amlDataStoreName: _testConfig.BlobDatastoreName,
                pathOnDataStore: "folder_destination",
                identifierHash: Guid.NewGuid().ToString(),
                contentHash: null);

            IAetherDataSource inputDataSource = await _testEnvironment.GetDataSourceAsync(inputDataSourceId);
            IAetherDataSource outputDataSource = await _testEnvironment.GetDataSourceAsync(outputDataSourceId);

            string moduleId = await UploadCompatModuleAsync(_testEnvironment);
            IAetherModule module = await _testEnvironment.GetModuleAsync(moduleId);

            IGraph graph = _testEnvironment.CreateNewGraph();

            IDataSourceNode inputDataSourceNode = graph.AddNode(inputDataSource);
            IDataSourceNode outputDataSourceNode = graph.AddNode(outputDataSource);

            IModuleNode moduleNode = graph.AddNode(module);
            SetModuleParams(moduleNode);

            graph.Connect(inputDataSourceNode, moduleNode.InputPortDictionary["SourceLocation"]);
            graph.Connect(outputDataSourceNode, moduleNode.InputPortDictionary["DestinationLocation"]);

            IPipelineRun experiment = await RunTestWithLogsAsync(graph, _testEnvironment);
            Assert.AreEqual(PipelineRunStatusCode.Finished, experiment.CachedStatus, experiment.Id, $"experiment id: {experiment.Id}");
        }

        [Test]
        public async Task TestDataTransferAsync()
        {
            var inputDataSourceId = await DataStoreUtils.CreateDataStoreDataSourceAsync(
                _testEnvironment,
                name: "Blob1",
                description: "Blob1",
                amlDataStoreName: _testConfig.BlobDatastoreName,
                pathOnDataStore: "folder",
                identifierHash: Guid.NewGuid().ToString(),
                contentHash: null);

            var outputDataSourceId = await DataStoreUtils.CreateDataStoreDataSourceAsync(
                _testEnvironment,
                name: "Blob3",
                description: "Blob3",
                amlDataStoreName: _testConfig.BlobDatastoreName,
                pathOnDataStore: "folder_destination",
                identifierHash: Guid.NewGuid().ToString(),
                contentHash: null);

            IAetherDataSource inputDataSource = await _testEnvironment.GetDataSourceAsync(inputDataSourceId);
            IAetherDataSource outputDataSource = await _testEnvironment.GetDataSourceAsync(outputDataSourceId);

            string moduleId = await UploadModuleAsync(_testEnvironment);
            IAetherModule module = await _testEnvironment.GetModuleAsync(moduleId);

            IGraph graph = _testEnvironment.CreateNewGraph();

            IDataSourceNode inputDataSourceNode = graph.AddNode(inputDataSource);
            IDataSourceNode outputDataSourceNode = graph.AddNode(outputDataSource);

            IModuleNode moduleNode = graph.AddNode(module);

            IReadOnlyDictionary<string, IAssignableParameter> metadataParams = moduleNode.MetadataParameters;
            metadataParams["ComputeName"].Value = _testConfig.DataFactoryComputeName;

            graph.Connect(inputDataSourceNode, moduleNode.InputPortDictionary[SourceInputName]);
            graph.Connect(outputDataSourceNode, moduleNode.InputPortDictionary[DestinationInputName]);

            IPipelineRun experiment = await RunTestWithLogsAsync(graph, _testEnvironment);
            Assert.AreEqual(PipelineRunStatusCode.Finished, experiment.CachedStatus, experiment.Id, $"experiment id: {experiment.Id}");
        }

        [Test]
        [Ignore("Disable due to missing federated credential")]
        public async Task TestDataTransferWithDataset()
        {
            /* Test case to support legacy 3 port with new datatset interface.*/
            var datasetSink = await _datasetApiCaller.GetDatasetByNameAsync(_testConfig.DatasetE2EBlobsnkName).ConfigureAwait(false);
            IAetherDataSource dataSink = await _testEnvironment.GetDataSourceAsync(
                dataSetDefinition: new DataSetDefinition
                {
                    DataTypeShortName = AzureBlobReference,
                    Value = new DataSetDefinitionValue
                    {
                        DataSetReference = new RegisteredDataSetReference
                        {
                            Name = datasetSink.Name,
                            Id = datasetSink.DatasetId.ToString(),
                            Version = datasetSink.Latest.VersionId
                        }
                    }
                });

            var datasetSource = await _datasetApiCaller.GetDatasetByNameAsync("blobdatasetfolder").ConfigureAwait(false);
            IAetherDataSource dataSource = await _testEnvironment.GetDataSourceAsync(
                 dataSetDefinition: new DataSetDefinition
                 {
                     DataTypeShortName = AzureBlobReference,
                     Value = new DataSetDefinitionValue
                     {
                         DataSetReference = new RegisteredDataSetReference
                         {
                             Name = datasetSource.Name,
                             Id = datasetSource.DatasetId.ToString(),
                             Version = datasetSource.Latest.VersionId
                         }
                     }
                 });

            string moduleId = await UploadModuleAsync(_testEnvironment);
            IAetherModule module = await _testEnvironment.GetModuleAsync(moduleId);

            IGraph graph = _testEnvironment.CreateNewGraph();

            IDataSourceNode inputDataSourceNode = graph.AddNode(dataSource);
            IDataSourceNode outputDataSourceNode = graph.AddNode(dataSink);

            IModuleNode moduleNode = graph.AddNode(module);

            IReadOnlyDictionary<string, IAssignableParameter> metadataParams = moduleNode.MetadataParameters;
            metadataParams["ComputeName"].Value = _testConfig.DataFactoryComputeName;

            graph.Connect(inputDataSourceNode, moduleNode.InputPortDictionary[SourceInputName]);
            graph.Connect(outputDataSourceNode, moduleNode.InputPortDictionary[DestinationInputName]);

            IPipelineRun experiment = await RunTestWithLogsAsync(graph, _testEnvironment);
            Assert.AreEqual(PipelineRunStatusCode.Finished, experiment.CachedStatus, experiment.Id, $"experiment id: {experiment.Id}");
        }

        [TestCase("myblobdatastore", "{run-id}/", "adlsgen2destmagicstring", TestName = "1 Input and 1 Output model ADLSgen2 DataTransfer Test with magic strings")]
        public async Task TestDataTransferWithOneInputModel(string DatastoreName, string PathOnDataSource, string RegisteredDataSetName)
        {
            /* Test case to support 2 port model*/
            var datasetBlobSrc = await _datasetApiCaller.GetDatasetByNameAsync("azureblobsnkrunid2").ConfigureAwait(false);
            IAetherDataSource dataBlobSrc = await _testEnvironment.GetDataSourceAsync(
                               dataSetDefinition: new DataSetDefinition
                               {
                                   DataTypeShortName = AzureBlobReference,
                                   Value = new DataSetDefinitionValue
                                   {
                                       DataSetReference = new RegisteredDataSetReference
                                       {
                                           Name = datasetBlobSrc.Name,
                                           Id = datasetBlobSrc.DatasetId.ToString(),
                                           Version = datasetBlobSrc.Latest.VersionId
                                       }
                                   }
                               });

            IGraph graph = _testEnvironment.CreateNewGraph();
            IDataSourceNode inputDataSourceNode = graph.AddNode(dataBlobSrc);
            string moduleId = await UploadModuleOneInputTypeAsync(_testEnvironment, DatastoreName, PathOnDataSource, RegisteredDataSetName);
            IAetherModule module = await _testEnvironment.GetModuleAsync(moduleId);
            IModuleNode moduleNode = graph.AddNode(module);
            IReadOnlyDictionary<string, IAssignableParameter> metadataParams = moduleNode.MetadataParameters;
            metadataParams["ComputeName"].Value = _testConfig.DataFactoryComputeName;

            graph.Connect(inputDataSourceNode, moduleNode.InputPortDictionary[SourceInputName]);

            IPipelineRun experiment = await RunTestWithLogsAsync(graph, _testEnvironment);
            Assert.AreEqual(PipelineRunStatusCode.Finished, experiment.CachedStatus, experiment.Id, $"experiment id: {experiment.Id}");
        }

        //[Test]
        public async Task TestCompleteDataTransferAsync()
        {
            var dataTypes = await _testEnvironment.GetAllDataTypesAsync();
            var blobDataType = dataTypes.First(dataType => dataType.Id.Equals("AzureBlobReference", StringComparison.InvariantCultureIgnoreCase));

            // TODO: currently data stores need to be registered in advance for following to work

            var blobDataSourceId = await DataStoreUtils.CreateDataStoreDataSourceAsync(
                _testEnvironment,
                name: "Blob1",
                description: "Blob1",
                amlDataStoreName: _testConfig.BlobDatastoreName,
                pathOnDataStore: "testdata1",
                identifierHash: Guid.NewGuid().ToString(),
                contentHash: null);

            var secondBlobDataSourceId = await DataStoreUtils.CreateDataStoreDataSourceAsync(
                _testEnvironment,
                name: "Blob2",
                description: "Blob2",
                amlDataStoreName: _testConfig.BlobDatastoreName,
                pathOnDataStore: "testdata2",
                identifierHash: Guid.NewGuid().ToString(),
                contentHash: null);

            var thirdBlobDataSourceId = await DataStoreUtils.CreateDataStoreDataSourceAsync(
                _testEnvironment,
                name: "Blob3",
                description: "Blob3",
                amlDataStoreName: _testConfig.BlobDatastoreName,
                pathOnDataStore: "testdata3",
                identifierHash: Guid.NewGuid().ToString(),
                contentHash: null);

            string adlsDataSourceId = await DataStoreUtils.CreateDataStoreDataSourceAsync(
                _testEnvironment,
                name: "Adls1",
                description: "Adls1",
                amlDataStoreName: _testConfig.AdlsDatastoreName,
                pathOnDataStore: "testdata1",
                identifierHash: Guid.NewGuid().ToString(),
                contentHash: null);

            string secondAdlsDataSourceId = await DataStoreUtils.CreateDataStoreDataSourceAsync(
                _testEnvironment,
                name: "Adls2",
                description: "Adls2",
                amlDataStoreName: _testConfig.AdlsDatastoreName,
                pathOnDataStore: "testdata2",
                identifierHash: Guid.NewGuid().ToString(),
                contentHash: null);

            string moduleId = await UploadCompatModuleAsync(_testEnvironment);
            IAetherModule module = await _testEnvironment.GetModuleAsync(moduleId);

            var graph = _testEnvironment.CreateNewGraph();

            IDataSourceNode blobDataSourceNode = graph.AddNode(await _testEnvironment.GetDataSourceAsync(blobDataSourceId));
            IDataSourceNode secondBlobDataSourceNode = graph.AddNode(await _testEnvironment.GetDataSourceAsync(secondBlobDataSourceId));
            IDataSourceNode thirdBlobDataSourceNode = graph.AddNode(await _testEnvironment.GetDataSourceAsync(thirdBlobDataSourceId));
            IDataSourceNode adlsDataSourceNode = graph.AddNode(await _testEnvironment.GetDataSourceAsync(adlsDataSourceId));
            IDataSourceNode secondAdlsDataSourceNode = graph.AddNode(await _testEnvironment.GetDataSourceAsync(secondAdlsDataSourceId));

            IModuleNode blobToBlobDataTransferNode = graph.AddNode(module);
            IModuleNode blobToAdlsDataTransferNode = graph.AddNode(module);
            IModuleNode adlsToAdlsDataTransferNode = graph.AddNode(module);
            IModuleNode adlsToBlobDataTransferNode = graph.AddNode(module);

            IModuleNode[] dataTransferNodes =
                {
                    blobToBlobDataTransferNode,
                    blobToAdlsDataTransferNode,
                    adlsToAdlsDataTransferNode,
                    adlsToBlobDataTransferNode
                };

            dataTransferNodes.ToList().ForEach(SetModuleParams);

            #region graph viz + helper

            // This is the graph we're building:
            //
            //           ##################                   ##################
            //           #  BLOB          #                   #  SECOND        #
            //           #  DATA          #                   #  BLOB          #
            //           #  SOURCE        #                   #  DATA SRC      #
            //           ##################                   ##################
            //                    #                                    #
            //                    #                                    #
            //           #######################################################
            //           #                                                     #
            //           #        BLOB TO BLOB DATA TRANSFER MODULE            #
            //           #                                                     #
            //           #######################################################
            //                      #
            //                      #                         ##################
            //                      #                         #  ADLS          #
            //                      #                         #  DATA          #
            //                      #                         #  SOURCE        #
            //                      #                         ##################
            //                      #                                  #
            //                      #                                  #
            //           #######################################################
            //           #                                                     #
            //           #       BLOB TO ADLS DATA TRANSFER MODULE             #
            //           #                                                     #
            //           #######################################################
            //                      #
            //                      #                         ##################
            //                      #                         #  SECOND        #
            //                      #                         #  ADLS          #
            //                      #                         #  DATA SRC      #
            //                      #                         ##################
            //                      #                                  #
            //                      #                                  #
            //           #######################################################
            //           #                                                     #
            //           #       ADLS TO ADLS DATA TRANSFER MODULE             #
            //           #                                                     #
            //           #######################################################
            //                      #
            //                      #                         ##################
            //                      #                         #  THIRD         #
            //                      #                         #  BLOB          #
            //                      #                         #  DATA SRC      #
            //                      #                         ##################
            //                      #                                  #
            //                      #                                  #
            //           #######################################################
            //           #                                                     #
            //           #       ADLS TO BLOB DATA TRANSFER MODULE             #
            //           #                                                     #
            //           #######################################################

            void Connect(IModuleNode moduleNode, INode sourceInput, IDataSourceNode destinationInput)
            {
                if (sourceInput is IDataSourceNode)
                {
                    graph.Connect(sourceInput, moduleNode.InputPortDictionary["SourceLocation"]);
                }
                else if (sourceInput is IModuleNode)
                {
                    graph.Connect(sourceInput.OutputPortDictionary["Output"], moduleNode.InputPortDictionary["SourceLocation"]);
                }
                else
                {
                    throw new IOException($"Can't connect input with type {sourceInput.GetType()}");
                }

                graph.Connect(destinationInput, moduleNode.InputPortDictionary["DestinationLocation"]);
            }

            #endregion

            Connect(blobToBlobDataTransferNode, blobDataSourceNode, secondBlobDataSourceNode);
            Connect(blobToAdlsDataTransferNode, blobToBlobDataTransferNode, adlsDataSourceNode);
            Connect(adlsToAdlsDataTransferNode, blobToAdlsDataTransferNode, secondAdlsDataSourceNode);
            Connect(adlsToBlobDataTransferNode, adlsToAdlsDataTransferNode, thirdBlobDataSourceNode);

            IPipelineRun experiment = await RunTestWithLogsAsync(graph, _testEnvironment);
            Assert.AreEqual(PipelineRunStatusCode.Finished, experiment.CachedStatus, $"experiment id: {experiment.Id}");
        }

        private void SetModuleParams(IModuleNode module)
        {
            var resourceId = _testConfig.DataFactoryResourceId;

            var match = DataFactoryResourceIdRegex.Match(resourceId);

            if (!match.Success)
            {
                throw new InvalidOperationException($"Specified data factory is not in correct format {resourceId}");
            }

            IReadOnlyDictionary<string, IAssignableParameter> metadataParams = module.MetadataParameters;
            metadataParams["DataFactorySubscriptionId"].Value = match.Groups["subscription"].Value;
            metadataParams["DataFactoryResourceGroup"].Value = match.Groups["resourceGroup"].Value;
            metadataParams["DataFactoryName"].Value = match.Groups["factoryName"].Value;
        }

        private static async Task<string> UploadCompatModuleAsync(AetherEnvironment environment)
        {
            var dataTypes = await environment.GetAllDataTypesAsync();
            var anyFileDataType = dataTypes.First(dataType => dataType.Id.Equals("AnyFile", StringComparison.InvariantCultureIgnoreCase));
            var anyDirectoryDataType = dataTypes.First(dataType => dataType.Id.Equals("AnyDirectory", StringComparison.InvariantCultureIgnoreCase));
            var inputDataTypes = new List<string> { anyFileDataType.Id, anyDirectoryDataType.Id };

            var structuredInterface = new StructuredInterface
            {
                Inputs = new List<StructuredInterfaceInput>
                {
                    new StructuredInterfaceInput { Name = "SourceLocation", DataTypeIdsList = inputDataTypes },
                    new StructuredInterfaceInput { Name = "DestinationLocation", DataTypeIdsList = inputDataTypes },
                },
                Outputs = new List<StructuredInterfaceOutput>
                {
                    new StructuredInterfaceOutput { Name = "Output", DataTypeId = anyFileDataType.Id},
                },
                Parameters = new List<StructuredInterfaceParameter>
                {
                    new StructuredInterfaceParameter { Name = "Command", DefaultValue="DataCopy", ParameterType = ParameterType.String },
                },
                MetadataParameters = new List<StructuredInterfaceParameter>
                {
                    new StructuredInterfaceParameter { Name = "DataFactorySubscriptionId", ParameterType = ParameterType.String },
                    new StructuredInterfaceParameter { Name = "DataFactoryResourceGroup", ParameterType = ParameterType.String },
                    new StructuredInterfaceParameter { Name = "DataFactoryName", ParameterType = ParameterType.String },
                }
            };

            var uploadInfo = new ModuleUploadInfo(
                name: "DataTransfer",
                displayName: "DataTransfer display",
                description: "Data Transfer module for copying data between blob and data lake accounts",
                isDeterministic: false,
                cloudSystem: "DataTransferCloud",
                structuredInterface: structuredInterface);

            return await environment.UploadModuleAsync(ModulePath, uploadInfo);
        }

        private static async Task<string> UploadModuleAsync(AetherEnvironment environment)
        {
            var dataTypes = await environment.GetAllDataTypesAsync();
            var anyFileDataType = dataTypes.First(dataType => dataType.Id.Equals("AnyFile", StringComparison.InvariantCultureIgnoreCase));
            var anyDirectoryDataType = dataTypes.First(dataType => dataType.Id.Equals("AnyDirectory", StringComparison.InvariantCultureIgnoreCase));
            var inputDataTypes = new List<string> { anyFileDataType.Id, anyDirectoryDataType.Id };

            var structuredInterface = new StructuredInterface
            {
                Inputs = new List<StructuredInterfaceInput>
                {
                    new StructuredInterfaceInput { Name = SourceInputName, DataTypeIdsList = inputDataTypes },
                    new StructuredInterfaceInput { Name = DestinationInputName, DataTypeIdsList = inputDataTypes },
                },
                Outputs = new List<StructuredInterfaceOutput>
                {
                    new StructuredInterfaceOutput { Name = OutputName, DataTypeId = anyDirectoryDataType.Id},
                },
                Parameters = new List<StructuredInterfaceParameter>
                {
                    new StructuredInterfaceParameter { Name = "Command", DefaultValue="DataCopy", ParameterType = ParameterType.String },
                },
                MetadataParameters = new List<StructuredInterfaceParameter>
                {
                    new StructuredInterfaceParameter { Name = "Version", ParameterType = ParameterType.Int, DefaultValue = "2" },
                    new StructuredInterfaceParameter { Name = "SourceInputName", ParameterType = ParameterType.String, DefaultValue = SourceInputName },
                    new StructuredInterfaceParameter { Name = "DestinationInputName", ParameterType = ParameterType.String, DefaultValue = DestinationInputName },
                    new StructuredInterfaceParameter { Name = "OutputName", ParameterType = ParameterType.String, DefaultValue = OutputName },
                    new StructuredInterfaceParameter { Name = "ComputeName", ParameterType = ParameterType.String },
                }
            };

            return await environment.UploadModuleAsync(
                sourcePath: null,
                uploadInfo: new ModuleUploadInfo(
                    name: "DataTransfer",
                    displayName: "DataTransfer display",
                    description: "Data Transfer module for copying data between blob and data lake accounts",
                    isDeterministic: false,
                    cloudSystem: "DataTransferCloud",
                    structuredInterface: structuredInterface,
                    moduleType: ModuleType.None));
        }


        private static async Task<string> UploadModuleOneInputTypeAsync(AetherEnvironment environment, string dataStoreName, string pathOnDatastore, string registeredDatasetName)
        {
            var dataTypes = await environment.GetAllDataTypesAsync();
            var anyFileDataType = dataTypes.First(dataType => dataType.Id.Equals("AnyFile", StringComparison.InvariantCultureIgnoreCase));
            var anyDirectoryDataType = dataTypes.First(dataType => dataType.Id.Equals("AnyDirectory", StringComparison.InvariantCultureIgnoreCase));
            var inputDataTypes = new List<string> { anyFileDataType.Id, anyDirectoryDataType.Id };

            var structuredInterface = new StructuredInterface
            {
                Inputs = new List<StructuredInterfaceInput>
                {
                    new StructuredInterfaceInput {Name = SourceInputName, DataTypeIdsList = inputDataTypes}
                },
                Outputs = new List<StructuredInterfaceOutput>
                {
                    new StructuredInterfaceOutput {Name = OutputName, DataTypeId = anyFileDataType.Id, DataStoreName=dataStoreName,
                                                   DatasetOutput= new DatasetOutput{DatasetRegistration = new DatasetRegistration{Name = registeredDatasetName, CreateNewVersion=true},
                                                                                    DatasetOutputOptions = new DatasetOutputOptions{PathOnDatastore=pathOnDatastore}}},
                },
                Parameters = new List<StructuredInterfaceParameter>
                {
                    new StructuredInterfaceParameter {Name = "Command", DefaultValue="DataCopy", ParameterType = ParameterType.String},
                    new StructuredInterfaceParameter {Name = "source_type", DefaultValue="file", ParameterType = ParameterType.String,IsOptional=true},
                },
                MetadataParameters = new List<StructuredInterfaceParameter>
                {
                    new StructuredInterfaceParameter {Name = "Version", ParameterType = ParameterType.Int, DefaultValue = "2"},
                    new StructuredInterfaceParameter {Name = "SourceInputName", ParameterType = ParameterType.String, DefaultValue = SourceInputName},
                    new StructuredInterfaceParameter {Name = "OutputName", ParameterType = ParameterType.String, DefaultValue = OutputName},
                    new StructuredInterfaceParameter {Name = "ComputeName", ParameterType = ParameterType.String},
                }
            };

            return await environment.UploadModuleAsync(
                sourcePath: null,
                uploadInfo: new ModuleUploadInfo(
                    name: "DataTransfer one-input model",
                    displayName: "One Input Data Transfer display",
                    description: "One Input Data Transfer module for copying data between blob and data lake accounts",
                    isDeterministic: false,
                    cloudSystem: "DataTransferCloud",
                    structuredInterface: structuredInterface,
                    moduleType: ModuleType.None));
        }
    }
}
