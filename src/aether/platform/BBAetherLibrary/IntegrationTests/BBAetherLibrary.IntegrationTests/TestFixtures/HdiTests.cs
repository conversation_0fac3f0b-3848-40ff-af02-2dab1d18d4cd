﻿using BBAetherLibrary.IntegrationTests.Common;
using Microsoft.Aether.AEVA.DataContracts;
using Microsoft.Aether.BlueBox.Library;
using NUnit.Framework;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace BBAetherLibrary.IntegrationTests.TestFixtures
{
    [TestFixture]
    [Parallelizable(ParallelScope.All)]
    public class HdiTests : BBAetherTestFixtureBase
    {
        [Test]
        [Ignore("HDI cloud is not enabled in this release.")]
        public async Task RunSimpleHdiTest()
        {
            string envConfig = "EnvironmentConfigs/helloworld2.xml";
            AetherEnvironment testEnvironment = BBAetherEnvironmentUtils.CreateTestAetherEnvironment(envConfig, GetAetherEndpoint(), _tokenProvider.GetAccessTokenAsync);
            await BBAetherEnvironmentUtils.CreateDataTypesIfNeededAsync(testEnvironment);

            string sourcePath = "TestResources/OneEmptyFile";
            string hdiModuleId = await UploadHdiModule(testEnvironment, sourcePath);

            IGraph graph = await BBAetherEnvironmentUtils.CreateSingleNodeGraph(testEnvironment, hdiModuleId).ConfigureAwait(false);
            IPipelineRun experiment = await RunTestWithLogsAsync(graph, testEnvironment).ConfigureAwait(false);

            Assert.AreEqual(PipelineRunStatusCode.Finished, experiment.CachedStatus, $"experiment id: {experiment.Id}");
        }

        private static async Task<string> UploadHdiModule(AetherEnvironment environment, string sourcePath)
        {
            var uploadInfo = new ModuleUploadInfo(name: "test hdi", displayName: "test hdi display", description: "testhdi", isDeterministic: false, cloudSystem: "HDInsightCloud", structuredInterface: new StructuredInterface
            {
                MetadataParameters = new List<StructuredInterfaceParameter>
                {
                    new StructuredInterfaceParameter { Name = "cluster", DefaultValue = "aether3phdihadooptest", ParameterType = ParameterType.String},
                    new StructuredInterfaceParameter { Name = "jobType", DefaultValue = "MapReduce", ParameterType = ParameterType.String},
                },
                Parameters = new List<StructuredInterfaceParameter>
                {
                    new StructuredInterfaceParameter { Name = "jarFile", DefaultValue = "/example/jars/hadoop-mapreduce-examples.jar", ParameterType = ParameterType.String},
                    new StructuredInterfaceParameter { Name = "jarClass", DefaultValue = "wordcount", ParameterType = ParameterType.String},
                    new StructuredInterfaceParameter { Name = "arguments", DefaultValue = "/example/data/gutenberg/davinci.txt,/example/data/davinciwordcount", ParameterType = ParameterType.String},
                }
            });

            return await environment.UploadModuleAsync(sourcePath, uploadInfo).ConfigureAwait(false);
        }
    }
}
