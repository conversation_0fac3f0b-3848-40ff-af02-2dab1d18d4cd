﻿using BBAetherLibrary.IntegrationTests.Common;
using NUnit.Framework;
using System;
using System.Diagnostics;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;

namespace BBAetherLibrary.IntegrationTests.TestFixtures
{
    [SetUpFixture]
    [Parallelizable(ParallelScope.All)]
    public class NetworkTests
    {
        private static readonly Random _rand = new Random();

        private static HttpClient _httpClient;
        private static CancellationTokenSource _cts;

        private static readonly string _pipelineKeepaliveUri = $"{BBAetherTestFixtureBase.GetAetherEndpoint()}/pipelines/v1.0/keepalive";
        private static int _pipelineTotalCalls = 0;
        private static int _pipelineSuccessCalls = 0;

        private const string _masterKeepaliveUri = "https://master.api.azureml-test.ms/pipelines/v1.0/keepalive";
        private static int _masterTotalCalls = 0;
        private static int _masterSuccessCalls = 0;

        [OneTimeSetUp]
        public void Setup()
        {
            _httpClient = new HttpClient();
            _cts = new CancellationTokenSource();

            Task.Run(() => StartTest(_cts.Token), _cts.Token);
        }

        [OneTimeTearDown]
        public async Task Teardown()
        {
            _cts?.Cancel();
            await Task.Delay(TimeSpan.FromSeconds(10)); // wait for tasks to cleanup
            _cts?.Dispose();

            Trace.WriteLine($"Pipeline /keepalive success rate: {_pipelineSuccessCalls}/{_pipelineTotalCalls}");
            Trace.WriteLine($"Master /keepalive success rate: {_masterSuccessCalls}/{_masterTotalCalls}");
        }

        private static async Task StartTest(CancellationToken cancellationToken)
        {
            while (true)
            {
                ++_pipelineTotalCalls;
                var success = await TestNetwork(_pipelineKeepaliveUri, cancellationToken);
                _pipelineSuccessCalls += success ? 1 : 0;

                await Task.Delay(_rand.Next(2000, 5000), cancellationToken);

                ++_masterTotalCalls;
                success = await TestNetwork(_masterKeepaliveUri, cancellationToken);
                _masterSuccessCalls += success ? 1 : 0;

                await Task.Delay(_rand.Next(2000, 5000), cancellationToken);
            }
        }

        private static async Task<bool> TestNetwork(string requestUri, CancellationToken cancellationToken)
        {
            try
            {
                var response = await _httpClient.GetAsync(requestUri, cancellationToken);
                Trace.WriteLine($"GET {requestUri} -> {response.StatusCode}");
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                Trace.WriteLine($"GET {requestUri} -> {ex.GetType()} ({ex.Message?.Substring(0, 100)})");
            }

            return false;
        }
    }
}
