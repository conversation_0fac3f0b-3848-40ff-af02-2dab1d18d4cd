﻿using BBAetherLibrary.IntegrationTests.Common;
using Microsoft.Aether.AEVA.DataContracts;
using NUnit.Framework;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;
using Newtonsoft.Json;
using System.IO;
using System;

namespace BBAetherLibrary.IntegrationTests.TestFixtures
{
    [TestFixture]
    [Parallelizable(ParallelScope.All)]
    public class PipelineTests : BBAetherTestFixtureBase
    {
        [Test]
        public async Task TestUpdatePipeline()
        {
            var creation = JsonConvert.DeserializeObject<PipelineCreationInfo>(File.ReadAllText($"TestResources/PipelineResources/PipelineCreation.json"));
            var pipelineId = await _testEnvironment.CreatePipelineAsync(creation).ConfigureAwait(false);
            Assert.IsNotNull(pipelineId);

            var pipelineEntity = await _testEnvironment.GetPipeline(pipelineId).ConfigureAwait(false);

            var test_description = "New description.";
            var test_key = "New key";
            var test_value = "New value";

            pipelineEntity.Description = test_description;
            pipelineEntity.KvTags[test_key] = test_value;
            pipelineEntity.Properties[test_key] = test_value;

            await _testEnvironment.UpdatePipelineAsync(pipelineId, pipelineEntity);

            var afterUpdatePipeline = await _testEnvironment.GetPipeline(pipelineId).ConfigureAwait(false);
            Assert.AreEqual(test_description, afterUpdatePipeline.Description, "Description in PipelineEntity was not updated.");
            Assert.IsTrue(afterUpdatePipeline.KvTags.ContainsKey(test_key) && afterUpdatePipeline.KvTags[test_key].Equals(test_value), "Tags in PipelineEntity was not updated.");
            Assert.IsTrue(afterUpdatePipeline.Properties.ContainsKey(test_key) && afterUpdatePipeline.Properties[test_key].Equals(test_value), "Properties in PipelineEntity was not updated.");
        }
    }
}