﻿using BBAetherLibrary.IntegrationTests.Common;
using Microsoft.Aether.AEVA.DataContracts;
using Microsoft.Aether.BlueBox.Library;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;

namespace BBAetherLibrary.IntegrationTests.TestFixtures
{
    [TestFixture(ContainerType.Workspace)]
    //[TestFixture(ContainerType.Repository)]
    [Parallelizable(ParallelScope.All)]
    public class AmlModuleTests : BBAetherTestFixtureBase
    {

        public AmlModuleTests(ContainerType containerType = ContainerType.Workspace) :
            base()
        {
            _containerType = containerType;

            if (_containerType == ContainerType.Repository)
            {
                var runParameters = TestContext.Parameters;
                _defaultRepositoryId = runParameters.Get("RepositoryId");
            }
        }

        [Test]
        public async Task TestAmlModuleDefaultResolve()
        {
            var versionNumber = "7";
            var modModVer = await CreateAmlModuleWithDefaultVersionAsync(versionNumber);
            var resolved = await _testEnvironment.ResolveAmlModuleAsync(modModVer.Item1.Id, _defaultRepositoryId);
            Assert.AreEqual(modModVer.Item1.DefaultVersion, versionNumber, "AmlModule defaultVersion wasn't updated");
            Assert.AreEqual(modModVer.Item2.Data.Id, resolved.Data.Id, "ResolvedVersion is not the default version");
        }


        [Test]
        public async Task TestAmlModuleWithDisabledDefaultResolve()
        {
            var versionNumber = "7";
            var modModVer = await CreateAmlModuleWithDefaultVersionAsync(versionNumber);
            modModVer.Item2.Data.EntityStatus = EntityStatus.Disabled;
            await _testEnvironment.UpdateAmlModuleVersionAsync(modModVer.Item2.Data.Id, modModVer.Item2, _defaultRepositoryId);
            var resolved = await _testEnvironment.ResolveAmlModuleAsync(modModVer.Item1.Id, _defaultRepositoryId);
            Assert.AreNotEqual(modModVer.Item2.Data.Id, resolved.Data.Id, "ResolvedVersion should not be the default version, as it was disabled");
        }

        [Test]
        public async Task TestAmlModuleResolveWithSimpleSemanticVersioning()
        {
            var modModVer = await CreateAmlModuleWithSimpleSemanticVersioning();
            var resolved = await _testEnvironment.ResolveAmlModuleAsync(modModVer.Item1.Id, _defaultRepositoryId);
            Assert.AreEqual(modModVer.Item2.Data.Id, resolved.Data.Id, "ResolvedVersion is not with the highest number");
        }

        [Test]
        public async Task TestAmlModuleResolveWithNoSemanticVersioning()
        {
            var modModVer = await CreateAmlModuleWithNoSemanticVersioning();
            var resolved = await _testEnvironment.ResolveAmlModuleAsync(modModVer.Item1.Id, _defaultRepositoryId);
            Assert.AreEqual(modModVer.Item2.Data.Id, resolved.Data.Id, "ResolvedVersion is not the latest");
        }

        [Test]
        public async Task TestAmlModuleResolveWithComplexSemanticVersioning()
        {
            var modModVer = await CreateAmlModuleWithComplexSemanticVersioning();
            var resolved = await _testEnvironment.ResolveAmlModuleAsync(modModVer.Item1.Id, _defaultRepositoryId);
            Assert.AreEqual(modModVer.Item2.Data.Id, resolved.Data.Id, "ResolvedVersion is not with the highest number");
        }

        [Test]
        public async Task TestListAmlModules()
        {
            string versionNumber = "517";
            var modModVerWithDefault = await CreateAmlModuleWithDefaultVersionAsync(versionNumber);
            var modModVerWithSimpleSemanticVersioning = await CreateAmlModuleWithSimpleSemanticVersioning();
            var modModVerWithNoSemanticVersioning = await CreateAmlModuleWithNoSemanticVersioning();
            var modModVerWithComplexSemanticVersioning = await CreateAmlModuleWithComplexSemanticVersioning();
            var modulesWithToken = await _testEnvironment.GetAmlModulesAsync(true, null, 2, _defaultRepositoryId);
            Assert.AreEqual(2, modulesWithToken.Entity.Count());
            Assert.NotNull(modulesWithToken.ContinuationToken);
            var moreModulesWithToken = await _testEnvironment.GetAmlModulesAsync(true, modulesWithToken.ContinuationToken, 2, _defaultRepositoryId);
            Assert.AreEqual(2, moreModulesWithToken.Entity.Count());
            Assert.NotNull(moreModulesWithToken.ContinuationToken);
            var firstCallIds = new HashSet<string>(modulesWithToken.Entity.Select(m => m.Id));
            var secondCallIds = new HashSet<string>(moreModulesWithToken.Entity.Select(m => m.Id));
            Assert.AreEqual(2, firstCallIds.Count());
            Assert.AreEqual(2, secondCallIds.Count());
            Assert.IsNull(firstCallIds.FirstOrDefault(i => secondCallIds.Contains(i)));
        }

        [Test]
        public async Task TestListResolvedAmlModuleVersions()
        {
            string versionNumber = "650";
            var modModVerWithDefault = await CreateAmlModuleWithDefaultVersionAsync(versionNumber);
            Assert.NotNull(modModVerWithDefault.Item1, "Module with default version is null");
            Assert.NotNull(modModVerWithDefault.Item2, "Default ModuleVersion is null");
            var modModVerWithSimpleSemanticVersioning = await CreateAmlModuleWithSimpleSemanticVersioning();
            Assert.NotNull(modModVerWithSimpleSemanticVersioning.Item1, "Module with simple semantic versioning is null");
            Assert.NotNull(modModVerWithSimpleSemanticVersioning.Item2, "ModuleVersion with highest simple semantic versioning is null");
            var modModVerWithNoSemanticVersioning = await CreateAmlModuleWithNoSemanticVersioning();
            Assert.NotNull(modModVerWithNoSemanticVersioning.Item1, "Module with no semantic versioning is null");
            Assert.NotNull(modModVerWithNoSemanticVersioning.Item2, "ModuleVersion that is latest is null");
            var modModVerWithComplexSemanticVersioning = await CreateAmlModuleWithComplexSemanticVersioning();
            Assert.NotNull(modModVerWithComplexSemanticVersioning.Item1, "Module with complex semantic versioning is null");
            Assert.NotNull(modModVerWithComplexSemanticVersioning.Item2, "ModuleVersion with highest complex semantic versioning is null");
            var modulesWithToken = await _testEnvironment.GetResolvedAmlModulesAsync(true, null, 2, _defaultRepositoryId);
            Assert.AreEqual(2, modulesWithToken.Entity.Count(), "Expected two resolved modules (1)");
            Assert.NotNull(modulesWithToken.ContinuationToken);
            var moreModulesWithToken = await _testEnvironment.GetResolvedAmlModulesAsync(true, modulesWithToken.ContinuationToken, 2, _defaultRepositoryId);
            Assert.AreEqual(2, moreModulesWithToken.Entity.Count(), "Expected two resolved modules (2)");
            var firstCallIds = new HashSet<string>(modulesWithToken.Entity.Select(m => m.Data.Id));
            var secondCallIds = new HashSet<string>(moreModulesWithToken.Entity.Select(m => m.Data.Id));
            Assert.AreEqual(2, firstCallIds.Count(), "First call Id counts is not 2");
            Assert.AreEqual(2, secondCallIds.Count(), "Second call Id count is not 2");
            Assert.IsNull(firstCallIds.FirstOrDefault(i => secondCallIds.Contains(i)));
        }

        [Test]
        public async Task TestListAmlModuleWithVersions()
        {
            if (_defaultRepositoryId != null)
            {
                // Skip repository API test, because currently we don't have a separate repository
                // for the workspace used for testing ListAmlModulesVersions.
                return;
            }

            var amlModules = await ListAmlModulesAsync();

            Assert.AreEqual(5, amlModules.Count(), "Expect total 5 AzureMLModules");

            foreach (var m in amlModules)
            {
                if (m.Item1.Name.Contains("default"))
                {
                    Assert.AreEqual(
                        m.Item1.Versions.First(v => v.Version == listAPITestVersionNumber).ModuleVersionId,
                        m.Item2.First().Data.Id,
                        "Default module version Id mismatch");
                }
                else
                {
                    Assert.AreEqual(0, m.Item2.Count(), "Expect no default module version");
                }
            }
        }

        [TestCase("Name asc", 5, false, TestName = "List all modules with version filter sort by name")]
        [TestCase("Name desc", 5, true, TestName = "List all modules with version filter sort by name desc")]
        public async Task TestListAmlModuleWithVersionsSort(string orderBy, int expectedResult, bool isDesc)
        {
            if (_defaultRepositoryId != null)
            {
                // Skip repository API test, because currently we don't have a separate repository
                // for the workspace used for testing ListAmlModulesVersions.
                return;
            }

            var amlModules = await ListAmlModulesAsync(
                orderBy: orderBy,
                // There are a lot of modules in the workspace.
                // In order to get expected modules in the first several pages, we are setting a filter here.
                filter: "name startswith 'Namespace://TestListAmlModuleVersions'");

            Assert.AreEqual(expectedResult, amlModules.Count(), $"Expect total {expectedResult} AzureMLModules");

            string prev = null;
            foreach (var m in amlModules)
            {
                string name = m.Item1.Name;
                if (prev != null)
                {
                    Assert.IsTrue(string.Compare(name, prev) < 0 == isDesc);
                }
                prev = name;
            }
        }

        [TestCase("name startswith 'Namespace://TestListAmlModuleVersions'", 5, TestName = "List all modules with version filter by name startswith")]
        [TestCase("name !startswith 'Namespace://TestListAmlModuleVersions'", 0, TestName = "List all modules with version filter by name !startswith")]
        [TestCase("name contains 'default'", 2, TestName = "List all modules with version filter by name contains")]
        [TestCase("name !contains 'default'", 3, TestName = "List all modules with version filter by name !contains")]
        [TestCase("name eq 'Namespace://TestListAmlModuleVersions-default-1'", 1, TestName = "List all modules with version filter by name eq")]
        [TestCase("name ne 'Namespace://TestListAmlModuleVersions-default-1'", 4, TestName = "List all modules with version filter by name ne")]
        [TestCase("name gt 'Namespace://TestListAmlModuleVersions-default-1'", 1, TestName = "List all modules with version filter by name gt")]
        [TestCase("name lt 'Namespace://TestListAmlModuleVersions-4'", 2, TestName = "List all modules with version filter by name ng")]
        [TestCase("name in 'Namespace://TestListAmlModuleVersions-default-1','Namespace://TestListAmlModuleVersions-4'", 2, TestName = "List all modules with version filter by name in")]
        [TestCase("name !in 'Namespace://TestListAmlModuleVersions-default-1','Namespace://TestListAmlModuleVersions-4'", 3, TestName = "List all modules with version filter by name !in")]
        public async Task ListAmlModuleWithVersionsFilterTest(string filter, int expectedResult)
        {
            if (_defaultRepositoryId != null)
            {
                // Skip repository API test, because currently we don't have a separate repository
                // for the workspace used for testing ListAmlModulesVersions.
                return;
            }

            var amlModules = await ListAmlModulesAsync(orderBy: null, filter: filter);

            Assert.AreEqual(expectedResult, amlModules.Count(), $"Expect total {expectedResult} AzureMLModules");
        }

        [Test]
        public async Task TestBatchResolving()
        {
            string versionNumber = "515";
            var modModVerWithDefault = await CreateAmlModuleWithDefaultVersionAsync(versionNumber);
            var modModVerWithSimpleSemanticVersioning = await CreateAmlModuleWithSimpleSemanticVersioning();
            var modModVerWithNoSemanticVersioning = await CreateAmlModuleWithNoSemanticVersioning();
            var modModVerWithComplexSemanticVersioning = await CreateAmlModuleWithComplexSemanticVersioning();
            var resolved = await _testEnvironment.BulkResolveAmlModuleAsync(new[] {
                modModVerWithDefault.Item1.Id,
                modModVerWithSimpleSemanticVersioning.Item1.Id,
                modModVerWithNoSemanticVersioning.Item1.Id,
                modModVerWithComplexSemanticVersioning.Item1.Id,
            },
            _defaultRepositoryId);

            var modIdToResolved = resolved.ToDictionary(mv => mv.ModuleId);

            var resolvedDefault = modIdToResolved[modModVerWithDefault.Item1.Id];
            Assert.AreEqual(modModVerWithDefault.Item1.DefaultVersion, versionNumber, "AmlModule defaultVersion wasn't updated");
            Assert.AreEqual(modModVerWithDefault.Item2.Data.Id, resolvedDefault.Data.Id, "ResolvedVersion is not the default version");

            var resolvedSimple = modIdToResolved[modModVerWithSimpleSemanticVersioning.Item1.Id];
            Assert.AreEqual(modModVerWithSimpleSemanticVersioning.Item2.Data.Id, resolvedSimple.Data.Id, "ResolvedVersion is not with the highest number");

            var resolvedNoSemanticVersioning = modIdToResolved[modModVerWithNoSemanticVersioning.Item1.Id];
            Assert.AreEqual(modModVerWithNoSemanticVersioning.Item2.Data.Id, resolvedNoSemanticVersioning.Data.Id, "ResolvedVersion is not the latest");

            var resolvedComplex = modIdToResolved[modModVerWithComplexSemanticVersioning.Item1.Id];
            Assert.AreEqual(modModVerWithComplexSemanticVersioning.Item2.Data.Id, resolvedComplex.Data.Id, "ResolvedVersion is not with the highest number");
        }

        [Test]
        public async Task TestAmlModuleVersionWithoutModule()
        {
            var modVer = await CreateAmlModuleVersion("0", "ver1", "display ver1", "12");
            Assert.IsNull(modVer.ModuleId);
            modVer = await _testEnvironment.GetAmlModuleVersionByIdAsync(modVer.Data.Id, _defaultRepositoryId);
            Assert.IsNull(modVer.ModuleId);
            modVer = await CreateAmlModuleVersion(null, "ver6", "display ver6", "12");
            Assert.IsNotNull(modVer.ModuleId);
        }

        [Test]
        public async Task TestAmlModuleVersionDisplayName()
        {
            var guid = Guid.NewGuid().ToString();
            var displayName = "ver1 display name";
            AzureMLModuleCreationInfo creationInfo = new AzureMLModuleCreationInfo("Namespace://" + guid, "AmlModule Description");
            AzureMLModule amlModule = await CreateOrGetAmlModule(creationInfo, true);
            var mv = await CreateAmlModuleVersion(amlModule.Id, "ver1", displayName, "12");
            var mvId = mv.Data.Id;
            var moduleVersion = await _testEnvironment.GetAmlModuleVersionByIdAsync(mvId, _defaultRepositoryId);
            Assert.AreEqual(displayName, moduleVersion.Data.DisplayName);

            var newDisplayName = "new display name";
            moduleVersion.Data.DisplayName = newDisplayName;
            await _testEnvironment.UpdateAmlModuleVersionAsync(mvId, moduleVersion, _defaultRepositoryId);
            var updatedModuleVersion = await _testEnvironment.GetAmlModuleVersionByIdAsync(mvId, _defaultRepositoryId);
            Assert.AreEqual(newDisplayName, updatedModuleVersion.Data.DisplayName);
        }

        [Test]
        public async Task TestAMLModuleCreateAndUpdate()
        {
            var dt = DateTime.UtcNow;
            var defaultversion = "7";

            var m = await this.CreateAmlModuleWithDefaultVersionAsync(defaultversion);

            var amlmodule = m.Item1;

            var module = m.Item2;

            //Verify the basic properties updated after creation
            Debug.Assert(amlmodule.DefaultVersion.Equals(defaultversion), "DefaultVersion not set correctly after creation");
            Debug.Assert(amlmodule.CreatedDate > dt, "AMLModule LastModifiedDate not updated after creation");
            Debug.Assert(amlmodule.LastModifiedDate > dt, "AMLModule LastModifiedDate not updated after creation");
            Debug.Assert(module.Data.CreatedDate > dt, "ModuleEntity LastModifiedDate not updated after creation");
            Debug.Assert(module.Data.LastModifiedDate > dt, "ModuleEntity LastModifiedDate not updated after creation");

            //Verify the module is linked to amlmodule
            var moduleIdFromAmlModule = amlmodule.Versions.Where(v => v.Version.Equals(amlmodule.DefaultVersion)).FirstOrDefault()?.ModuleVersionId;
            Debug.Assert(module.Data.Id.Equals(moduleIdFromAmlModule));
            Debug.Assert(module.ModuleId.Equals(amlmodule.Id));

            amlmodule.DefaultVersion = amlmodule.Versions.Where(v => v.ModuleVersionId.Equals(m.Item2.Data.Id)).FirstOrDefault().Version;
            var afterUpdate = await _testEnvironment.UpdateAmlModuleAsync(amlmodule.Id, amlmodule, _defaultRepositoryId);

            //Verify the basic properties updated after update
            Debug.Assert(afterUpdate.Id == amlmodule.Id);
            Debug.Assert(afterUpdate.Name.Equals(amlmodule.Name));
            Debug.Assert(afterUpdate.DefaultVersion == amlmodule.DefaultVersion, "DefaultVersion not updated after update");
            Debug.Assert(afterUpdate.LastModifiedDate > amlmodule.LastModifiedDate, "LastModifiedDate not updated after update");

            //Verify the module is linked to amlmodule
            moduleIdFromAmlModule = amlmodule.Versions.Where(v => v.Version.Equals(amlmodule.DefaultVersion)).FirstOrDefault()?.ModuleVersionId;
            Debug.Assert(module.Data.Id.Equals(moduleIdFromAmlModule));
            Debug.Assert(module.ModuleId.Equals(amlmodule.Id));
        }

        [Test]
        public async Task TestSpecialCharInAmlModuleName()
        {
            if (_defaultRepositoryId != null)
            {
                return;
            }

            var guid = Guid.NewGuid().ToString();
            AzureMLModuleCreationInfo creationInfo = new AzureMLModuleCreationInfo("Namespace://~`!@#$%^&*()[]{}\\|;'\",./:<>?+- " + guid, "Name with special chars");
            await CreateOrGetAmlModule(creationInfo);
            var amlModule = await _testEnvironment.GetAmlModuleByNameAsync(creationInfo.Name, _defaultRepositoryId);
            Assert.IsNotNull(amlModule);
            Assert.AreEqual(creationInfo.Name, amlModule.Name, "AmlModule name do not match");
        }

        private async Task<List<Tuple<AzureMLModule, IList<AzureMLModuleVersion>>>> ListAmlModulesAsync(string orderBy = null, string filter = null, bool showTrace = true)
        {
            var amlModules = new List<Tuple<AzureMLModule, IList<AzureMLModuleVersion>>>();
            string contToken = null;
            var pageCount = 0;

            do
            {
                var modulesWithToken = await _testEnvironment.ListAmlModuleWithVersionsAsync(
                    activeOnly: true,
                    continuationToken: contToken,
                    batchSize: 10,
                    versionsType: GetAmlModuleVersionsType.Default,
                    removeInterface: true,
                    orderBy: orderBy,
                    filter: filter);
                contToken = modulesWithToken.ContinuationToken;
                if (showTrace)
                {
                    Trace.WriteLine($"ListAmlModules (filter=\"{filter}\", orderBy=\"{orderBy}\", page={pageCount}):");
                    foreach (var module in modulesWithToken.Entity)
                    {
                        Trace.WriteLine($" - Module id: {module.Item1.Id}, name : {module.Item1.Name}");
                    }
                }
                amlModules.AddRange(modulesWithToken.Entity.Where(m => m.Item1.Name.Contains($"{defaultNamespace}://{listAPITestModuleNamePrefix}")));
                pageCount++;
            }
            // There are much too many modules in the workspace (and growing).
            // We limit the number of pages to 3 here since the modules we want are located in the starting pages.
            while (contToken != null && pageCount < 3);

            return amlModules;
        }

        private async Task<AzureMLModuleVersion> CreateAmlModuleVersion(string moduleId, string name, string displayName, string version)
        {
            var guid = Guid.NewGuid().ToString();
            AzureMLModuleVersionCreationInfo mvCreationInfo = new AzureMLModuleVersionCreationInfo(name + guid,
                displayName,
                "mvDescription",
                false, "mvExecutionType", "mvHash", "mvIdentifierHash",
                moduleId, version, new StructuredInterface { CommandLinePattern = "aaa" });
            AzureMLModuleVersion mv = null;
            try
            {
                mv = await _testEnvironment.CreateAmlModuleVersionAsync(mvCreationInfo, _defaultRepositoryId);
            }
            catch (Exception ex) // do nothing, fallback to making a GET call for the moduleVersion
            {
                Trace.WriteLine($"Unable to create AmlModuleVersion for moduleId {mvCreationInfo.AmlModuleId}, version is {mvCreationInfo.Version}, will try to getAmlModuleVersion: {ex.Message}");
            }
            if (mv == null)
            {
                AzureMLModule module = await _testEnvironment.GetAmlModuleByIdAsync(moduleId, _defaultRepositoryId);
                var descriptor = module.Versions.FirstOrDefault(v => v.Version == version);
                if (descriptor?.ModuleVersionId != null)
                {
                    mv = await _testEnvironment.GetAmlModuleVersionByIdAsync(descriptor.ModuleVersionId, _defaultRepositoryId);
                }

                // The following is not a fix.  It is to collect more information for investigation purpose.
                if (mv == null && version == listAPITestVersionNumber)
                {
                    Trace.WriteLine($"NOT FOUND: {moduleId}, {module.Id}, {descriptor?.Version}, {descriptor?.ModuleVersionId}.");
                    foreach (var v in module.Versions)
                    {
                        Trace.WriteLine($"{v.Version}: {v.ModuleVersionId}");
                    }
                }
            }
            return mv;
        }

        private async Task<Tuple<AzureMLModule, AzureMLModuleVersion>> CreateAmlModuleWithDefaultVersionAsync(
            string defaultVersionNumber, string name = null)
        {
            var guid = name ?? Guid.NewGuid().ToString();
            AzureMLModuleCreationInfo creationInfo = new AzureMLModuleCreationInfo("Namespace://" + guid, "AmlModule Description");
            AzureMLModule amlModule = await CreateOrGetAmlModule(creationInfo, name == null);
            await CreateAmlModuleVersion(amlModule.Id, "ver1", "display ver1", "12");
            var defaultMV = await CreateAmlModuleVersion(amlModule.Id, "ver2", "default version display name", defaultVersionNumber);
            await CreateAmlModuleVersion(amlModule.Id, "ver3", "display ver3", "24");
            var beforeUpdate = await _testEnvironment.GetAmlModuleByIdAsync(amlModule.Id, _defaultRepositoryId);
            beforeUpdate.DefaultVersion = defaultVersionNumber;
            var module = await _testEnvironment.UpdateAmlModuleAsync(beforeUpdate.Id, beforeUpdate, _defaultRepositoryId);
            return Tuple.Create(module, defaultMV);
        }

        private async Task<Tuple<AzureMLModule, AzureMLModuleVersion>> CreateAmlModuleWithSimpleSemanticVersioning(string name = null)
        {
            var guid = name ?? Guid.NewGuid().ToString();
            AzureMLModuleCreationInfo creationInfo = new AzureMLModuleCreationInfo($"{defaultNamespace}://{guid}", "AmlModule Description");
            AzureMLModule amlModule = await CreateOrGetAmlModule(creationInfo, name == null);
            await CreateAmlModuleVersion(amlModule.Id, "ver1", "display ver1", "12");
            await CreateAmlModuleVersion(amlModule.Id, "ver2", "display ver2", "7");
            var highesetVersionMv = await CreateAmlModuleVersion(amlModule.Id, "ver3", "display ver3", "24");
            var beforeUpdate = await _testEnvironment.GetAmlModuleByIdAsync(amlModule.Id, _defaultRepositoryId);
            beforeUpdate.DefaultVersion = null;
            var module = await _testEnvironment.UpdateAmlModuleAsync(beforeUpdate.Id, beforeUpdate, _defaultRepositoryId);
            return Tuple.Create(module, highesetVersionMv);
        }

        private async Task<Tuple<AzureMLModule, AzureMLModuleVersion>> CreateAmlModuleWithNoSemanticVersioning(string name = null)
        {
            var guid = name ?? Guid.NewGuid().ToString();
            AzureMLModuleCreationInfo creationInfo = new AzureMLModuleCreationInfo($"{defaultNamespace}://{guid}", "AmlModule Description");
            AzureMLModule amlModule = await CreateOrGetAmlModule(creationInfo, name == null);
            await CreateAmlModuleVersion(amlModule.Id, "ver1", "display ver1", "212");
            await CreateAmlModuleVersion(amlModule.Id, "ver2", "display ver2", "7a");
            var latestVersionMv = await CreateAmlModuleVersion(amlModule.Id, "ver3", "display ver3", "24");
            var beforeUpdate = await _testEnvironment.GetAmlModuleByIdAsync(amlModule.Id, _defaultRepositoryId);
            beforeUpdate.DefaultVersion = null;
            var module = await _testEnvironment.UpdateAmlModuleAsync(beforeUpdate.Id, beforeUpdate, _defaultRepositoryId);
            return Tuple.Create(module, latestVersionMv);
        }

        private async Task<Tuple<AzureMLModule, AzureMLModuleVersion>> CreateAmlModuleWithComplexSemanticVersioning(string name = null)
        {
            var guid = name ?? Guid.NewGuid().ToString();
            AzureMLModuleCreationInfo creationInfo = new AzureMLModuleCreationInfo($"{defaultNamespace}://{guid}", "AmlModule Description");
            AzureMLModule amlModule = await CreateOrGetAmlModule(creationInfo, name == null);
            await CreateAmlModuleVersion(amlModule.Id, "ver1", "display ver1", "12");
            var highesetVersionMv = await CreateAmlModuleVersion(amlModule.Id, "ver2", "display ver2", "121.1.4");
            await CreateAmlModuleVersion(amlModule.Id, "ver2", "display ver2", "121.1");
            var beforeUpdate = await _testEnvironment.GetAmlModuleByIdAsync(amlModule.Id, _defaultRepositoryId);
            beforeUpdate.DefaultVersion = null;
            var module = await _testEnvironment.UpdateAmlModuleAsync(beforeUpdate.Id, beforeUpdate, _defaultRepositoryId);
            return Tuple.Create(module, highesetVersionMv);
        }

        private async Task<AzureMLModule> CreateOrGetAmlModule(AzureMLModuleCreationInfo creationInfo, bool userProvideName = true)
        {
            AzureMLModule amlModule = null;
            if (userProvideName)
            {
                try
                {
                    amlModule = await _testEnvironment.CreateAmlModuleAsync(creationInfo, _defaultRepositoryId);
                }
                catch (Exception ex) // do nothing, will falling back to a GET call
                {
                    Trace.WriteLine($"Unable to create AmlModule {creationInfo.Name}, will try to get existing AmlModule: {ex.Message}");
                }

                if (amlModule == null)
                {
                    return await _testEnvironment.GetAmlModuleByNameAsync(creationInfo.Name, _defaultRepositoryId);
                }
            }
            else
            {
                try
                {
                    amlModule = await _testEnvironment.GetAmlModuleByNameAsync(creationInfo.Name, _defaultRepositoryId);
                }
                catch // do nothing, will falling back to a GET call
                {
                    Trace.WriteLine($"Unable to Get AmlModule {creationInfo.Name}, will try to create AmlModule");
                }

                if (amlModule == null)
                {
                    return await _testEnvironment.CreateAmlModuleAsync(creationInfo, _defaultRepositoryId);
                }
            }
            return amlModule;
        }

        [OneTimeSetUp]
        public async Task setupListModuleTestEnvironment()
        {
            if (_defaultRepositoryId == null)
            {
                var backupEnvironment = _testEnvironment;
                try
                {
                    _testEnvironment = _testEnvironment;

                    var modModVerWithDefault = await CreateAmlModuleWithDefaultVersionAsync(listAPITestVersionNumber, $"{listAPITestModuleNamePrefix}-default-1");
                    Assert.NotNull(modModVerWithDefault.Item1, "Module with default version is null");
                    Assert.NotNull(modModVerWithDefault.Item2, "Default ModuleVersion is null");
                    var modModVerWithSimpleSemanticVersioning = await CreateAmlModuleWithSimpleSemanticVersioning($"{listAPITestModuleNamePrefix}-2");
                    Assert.NotNull(modModVerWithSimpleSemanticVersioning.Item1, "Module with simple semantic versioning is null");
                    Assert.NotNull(modModVerWithSimpleSemanticVersioning.Item2, "ModuleVersion with highest simple semantic versioning is null");
                    var modModVerWithNoSemanticVersioning = await CreateAmlModuleWithNoSemanticVersioning($"{listAPITestModuleNamePrefix}-3");
                    Assert.NotNull(modModVerWithNoSemanticVersioning.Item1, "Module with no semantic versioning is null");
                    Assert.NotNull(modModVerWithNoSemanticVersioning.Item2, "ModuleVersion that is latest is null");
                    var modModVerWithComplexSemanticVersioning = await CreateAmlModuleWithComplexSemanticVersioning($"{listAPITestModuleNamePrefix}-4");
                    Assert.NotNull(modModVerWithComplexSemanticVersioning.Item1, "Module with complex semantic versioning is null");
                    Assert.NotNull(modModVerWithComplexSemanticVersioning.Item2, "ModuleVersion with highest complex semantic versioning is null");
                    var modModVerWithDefault2 = await CreateAmlModuleWithDefaultVersionAsync(listAPITestVersionNumber, $"{listAPITestModuleNamePrefix}-default-5");
                    Assert.NotNull(modModVerWithDefault2.Item1, "Module with default version is null");
                    Assert.NotNull(modModVerWithDefault2.Item2, "Default ModuleVersion is null");
                }
                catch (Exception e)
                {
                    Trace.WriteLine($"Unable to set up ListAMLModuleWithVersion test environment, exception {e}");
                }
                finally
                {
                    _testEnvironment = backupEnvironment;
                }
            }
        }

        private const string listAPITestVersionNumber = "670";
        private const string listAPITestModuleNamePrefix = "TestListAmlModuleVersions";
        private const string defaultNamespace = "Namespace";

        private string _defaultRepositoryId;
        private ContainerType _containerType = ContainerType.Workspace;
    }
}
