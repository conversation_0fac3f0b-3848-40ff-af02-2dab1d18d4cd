﻿using BBAetherLibrary.IntegrationTests.Common;
using Microsoft.Aether.AEVA.DataContracts;
using Microsoft.Aether.BlueBox.Library;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace BBAetherLibrary.IntegrationTests.TestFixtures
{
    [TestFixture]
    public class PipelineEmailNotificationTest : BBAetherTestFixtureBase
    {
        private const string ModulePath = "TestResources/OneEmptyFile";

        private const string DataFactoryResourceIdPattern = "^/subscriptions/(?<subscription>[^/]*)/resourceGroups/(?<resourceGroup>[^/]*)/providers/Microsoft.DataFactory/factories/(?<factoryName>[^/]*)$";
        private static readonly Regex DataFactoryResourceIdRegex = new Regex(DataFactoryResourceIdPattern, RegexOptions.IgnoreCase | RegexOptions.Compiled);

        [Test]
        public async Task TestPipelineEmailNotificationAsync()
        {
            var inputDataSourceId = await DataStoreUtils.CreateDataStoreDataSourceAsync(
                _testEnvironment,
                name: "Blob1",
                description: "Blob1",
                amlDataStoreName: _testConfig.BlobDatastoreName,
                pathOnDataStore: "testdata1",
                identifierHash: Guid.NewGuid().ToString(),
                contentHash: null);

            var outputDataSourceId = await DataStoreUtils.CreateDataStoreDataSourceAsync(
                _testEnvironment,
                name: "Blob3",
                description: "Blob3",
                amlDataStoreName: _testConfig.BlobDatastoreName,
                pathOnDataStore: "testdata3",
                identifierHash: Guid.NewGuid().ToString(),
                contentHash: null);

            IAetherDataSource inputDataSource = await _testEnvironment.GetDataSourceAsync(inputDataSourceId);
            IAetherDataSource outputDataSource = await _testEnvironment.GetDataSourceAsync(outputDataSourceId);

            string moduleId = await UploadModuleAsync(_testEnvironment);
            IAetherModule module = await _testEnvironment.GetModuleAsync(moduleId);

            IGraph graph = _testEnvironment.CreateNewGraph();

            IDataSourceNode inputDataSourceNode = graph.AddNode(inputDataSource);
            IDataSourceNode outputDataSourceNode = graph.AddNode(outputDataSource);

            IModuleNode moduleNode = graph.AddNode(module);
            SetModuleParams(moduleNode);
            bool enableNotification = true;
            graph.Connect(inputDataSourceNode, moduleNode.InputPortDictionary["SourceLocation"]);
            graph.Connect(outputDataSourceNode, moduleNode.InputPortDictionary["DestinationLocation"]);
            string pipelineId = await _testEnvironment.CreatePipelineAsync(
                pipelineCreationInfo: new PipelineCreationInfo
                {
                    Name = "Test",
                    Description = nameof(TestPipelineEmailNotificationAsync),
                    ContinueRunOnStepFailure = false,
                    EnableNotification = enableNotification,
                    Graph = graph.GetGraphEntity(),
                    GraphInterface = GraphConverter.GetEntityInterfaceFromGraph(graph)
                });

            PipelineEntity pipelineEntity = await _testEnvironment.GetPipeline(pipelineId);

            Assert.AreEqual(enableNotification, pipelineEntity.EnableNotification, $"Pipeline id: {pipelineEntity.Id}");

            string pipelineRunId = await _testEnvironment.SubmitPipelineRunFromPipelineAsync(
                pipelineId: pipelineId,
                pipelineSubmissionInfo: new PipelineSubmissionInfo
                {
                    ExperimentName = "PipelineEmailNotificationTest",
                });

            IPipelineRun pipelineRun = await FetchExperimentOnceCompleteAsync(
                environment: _testEnvironment,
                pipelineRunId: pipelineRunId);
            Assert.AreEqual(PipelineRunStatusCode.Finished, pipelineRun.CachedStatus, $"experiment id: {pipelineRun.Id}");
        }

        private void SetModuleParams(IModuleNode module)
        {
            var resourceId = _testConfig.DataFactoryResourceId;

            var match = DataFactoryResourceIdRegex.Match(resourceId);

            if (!match.Success)
            {
                throw new InvalidOperationException($"Specified data factory is not in correct format {resourceId}");
            }

            IReadOnlyDictionary<string, IAssignableParameter> metadataParams = module.MetadataParameters;
            metadataParams["DataFactorySubscriptionId"].Value = match.Groups["subscription"].Value;
            metadataParams["DataFactoryResourceGroup"].Value = match.Groups["resourceGroup"].Value;
            metadataParams["DataFactoryName"].Value = match.Groups["factoryName"].Value;
        }

        private static async Task<string> UploadModuleAsync(AetherEnvironment environment)
        {
            var dataTypes = await environment.GetAllDataTypesAsync();
            var anyFileDataType = dataTypes.First(dataType => dataType.Id.Equals("AnyFile", StringComparison.InvariantCultureIgnoreCase));
            var anyDirectoryDataType = dataTypes.First(dataType => dataType.Id.Equals("AnyDirectory", StringComparison.InvariantCultureIgnoreCase));
            var inputDataTypes = new List<string> { anyFileDataType.Id, anyDirectoryDataType.Id };

            var structuredInterface = new StructuredInterface
            {
                Inputs = new List<StructuredInterfaceInput>
                {
                    new StructuredInterfaceInput { Name = "SourceLocation", DataTypeIdsList = inputDataTypes },
                    new StructuredInterfaceInput { Name = "DestinationLocation", DataTypeIdsList = inputDataTypes },
                },
                Outputs = new List<StructuredInterfaceOutput>
                {
                    new StructuredInterfaceOutput { Name = "Output", DataTypeId = anyFileDataType.Id},
                },
                Parameters = new List<StructuredInterfaceParameter>
                {
                    new StructuredInterfaceParameter { Name = "Command", DefaultValue="DataCopy", ParameterType = ParameterType.String },
                },
                MetadataParameters = new List<StructuredInterfaceParameter>
                {
                    new StructuredInterfaceParameter { Name = "DataFactorySubscriptionId", ParameterType = ParameterType.String },
                    new StructuredInterfaceParameter { Name = "DataFactoryResourceGroup", ParameterType = ParameterType.String },
                    new StructuredInterfaceParameter { Name = "DataFactoryName", ParameterType = ParameterType.String },
                }
            };

            var uploadInfo = new ModuleUploadInfo(
                name: "TestNotification",
                displayName: "TestNotification display",
                description: "Testing Notification Endpoint",
                isDeterministic: false,
                cloudSystem: "DataTransferCloud",
                structuredInterface: structuredInterface);

            return await environment.UploadModuleAsync(ModulePath, uploadInfo);
        }
    }
}
