﻿using Microsoft.Aether.BlueBox.Library;
using Microsoft.Aether.AEVA.DataContracts;
using NUnit.Framework;
using System.Threading.Tasks;
using BBAetherLibrary.IntegrationTests.Common;

namespace BBAetherLibrary.IntegrationTests.TestFixtures
{
    [TestFixture]
    [Parallelizable(ParallelScope.All)]
    public class ESTests : BBAetherTestFixtureBase
    {
        [Test]
        public async Task RunEsCloudTest()
        {
            string sourcePath = "TestResources/EscloudFile/helloworld";
            string DSVMModuleId = await BBAetherEnvironmentUtils.UploadESCloudModule(_testEnvironment, sourcePath, _testConfig.AmlComputeName);
            IGraph graph = await BBAetherEnvironmentUtils.CreateSingleNodeGraph(_testEnvironment, DSVMModuleId);

            IPipelineRun experiment = await RunTestWithLogsAsync(graph, _testEnvironment, 20);
            Assert.AreEqual(PipelineRunStatusCode.Finished, experiment.CachedStatus, $"experiment id: {experiment.Id}");
        }
    }
}
