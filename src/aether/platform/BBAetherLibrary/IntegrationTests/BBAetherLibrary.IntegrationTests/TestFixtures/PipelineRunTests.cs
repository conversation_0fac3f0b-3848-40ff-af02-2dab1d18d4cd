using BBAetherLibrary.IntegrationTests.Common;
using Microsoft.Aether.AEVA.DataContracts;
using Microsoft.Aether.BlueBox.Library;
using NUnit.Framework;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;
using Newtonsoft.Json;
using System.IO;
using BBAetherLibrary.IntegrationTests.Client;
using System;
using System.Threading;
using System.Net.Http;
using System.Text;
using System.Net;

namespace BBAetherLibrary.IntegrationTests.TestFixtures
{
    [TestFixture]
    [Parallelizable(ParallelScope.All)]
    public class PipelineRunTests : BBAetherTestFixtureBase
    {
        private IndexServiceApiCaller _indexServiceApiCaller;
        private RunHistoryApiCaller _runHistoryApiCaller;

        [OneTimeSetUp]
        public void Init()
        {
            _indexServiceApiCaller = new IndexServiceApiCaller(_testConfig.SubscriptionId, _testConfig.ResourceGroup, _testConfig.WorkspaceName, _tokenProvider.GetAccessTokenAsync);
            _runHistoryApiCaller = new RunHistoryApiCaller(_testConfig.SubscriptionId, _testConfig.ResourceGroup, _testConfig.WorkspaceName, _tokenProvider.GetAccessTokenAsync);
        }

        [Test]
        public async Task TestRecordLineageTypeAndDraftSourceWithCreationInfo()
        {
            var pipelineDraft = JsonConvert.DeserializeObject<PipelineDraft>(File.ReadAllText($"TestResources/PipelineResources/RecordRunLineageInfo.json"));
            pipelineDraft.PipelineSubmissionInfo.ExperimentName = "TestRunLineageType";
            pipelineDraft.PipelineSubmissionInfo.Properties.Add("azureml.runLineageType", "reference");
            pipelineDraft.PipelineSubmissionInfo.Properties.Add("azureml.Designer.inferencePipelineType", "RealTimeInferencePipeline");

            var pipelineRunEntity = await _testEnvironment.SubmitPipelineRunFromPipelineDraftAsync(pipelineDraft).ConfigureAwait(false);
            Thread.Sleep(1000);

            var runDetails = await _runHistoryApiCaller.GetRunDetails(pipelineRunEntity.RunHistoryExperimentName, pipelineRunEntity.PipelineRunId).ConfigureAwait(false);
            Assert.AreEqual("reference", runDetails.Properties["azureml.runLineageType"]);
            Assert.AreEqual("RealTimeInferencePipeline", runDetails.Properties["azureml.Designer.inferencePipelineType"]);
            Assert.AreEqual(pipelineDraft.ParentPipelineRunId, runDetails.Properties["azureml.sourcepipelinerunid"]);
            await _testEnvironment.CancelPipelineRunAsync(pipelineRunEntity.PipelineRunId).ConfigureAwait(false);

            var createInfo = new PipelineRunCreationInfo
            {
                DisplayName = "TestRunLineageTypeForResubmit",
                Description = "TestRunLineageTypeForResubmit",
                RunHistoryExperimentName = "TestRunLineageType",
                Properties = new Dictionary<string, string>
                {
                    { "azureml.runLineageType", "schedule" },
                    { "azureml.genericTriggerName", "tiggerName" },
                    { "azureml.genericTriggerVersionId", "tiggerVersion" },
                    { "azureml.telemetry.attribution", "BBAetherTest" },
                }
            };
            var resubmitRunId = await _testEnvironment.ResubmitPipelineRunAsync(pipelineRunEntity.PipelineRunId, createInfo).ConfigureAwait(false);
            Thread.Sleep(1000);

            runDetails = await _runHistoryApiCaller.GetRunDetails(pipelineRunEntity.RunHistoryExperimentName, resubmitRunId).ConfigureAwait(false);
            // schedule for test overwrite logic
            Assert.AreEqual("schedule", runDetails.Properties["azureml.runLineageType"]);
            // check trigger properties
            Assert.AreEqual("tiggerName", runDetails.Properties["azureml.genericTriggerName"]);
            Assert.AreEqual("tiggerVersion", runDetails.Properties["azureml.genericTriggerVersionId"]);
            // check attribution properties and traits
            Assert.AreEqual("BBAetherTest", runDetails.Properties["azureml.telemetry.attribution"]);
            Assert.True(runDetails.RunTypeV2.Traits.Contains("BBAetherTest"));
            await _testEnvironment.CancelPipelineRunAsync(resubmitRunId).ConfigureAwait(false);
        }

        [Test]
        public async Task TestPipelineRunsGetDeepVisualGraphEntity()
        {
            string sourcePath = "TestResources/EscloudFile/helloworld";
            string DSVMModuleId = await BBAetherEnvironmentUtils.UploadESCloudModule(_testEnvironment, sourcePath, "fake-compute");
            IGraph graph = await BBAetherEnvironmentUtils.CreateSingleNodeGraph(_testEnvironment, DSVMModuleId);
            graph.AddDefaultCompute(new ComputeSetting { Name = _testConfig.AmlComputeName, ComputeType = ComputeType.MLC });
            graph.AddDefaultDatastore(new DatastoreSetting { DataStoreName = _testConfig.BlobDatastoreName });
            var moduleNode = graph.ModuleNodes.FirstOrDefault();

            moduleNode.SetComputeSetting(true);
            moduleNode.SetDatastoreSetting(true);

            GraphDraft createdGraphDraft = await _testEnvironment.CreateGraphDraftAsync(graph);
            createdGraphDraft.ModuleNodes.FirstOrDefault().Parameters["Arguments"].Value = "new";
            await _testEnvironment.UpdateGraphDraftAsync(createdGraphDraft);

            GraphDraft graphDraft = await _testEnvironment.GetGraphDraftAsync(createdGraphDraft.Id);

            var pipelineDraftToCreate = new PipelineDraft()
            {
                Name = "NewPipelineDraft",
                PipelineSubmissionInfo = new PipelineSubmissionInfo()
                {
                    ExperimentName = "helloworld",
                    Description = "created from pipeline draft"
                },
                GraphDraftId = createdGraphDraft.Id,
                KvTags = new Dictionary<string, string>
                {
                    {"tag", "value"}
                }
            };

            var createdDraft = await _testEnvironment.CreatePipelineDraftAsync(pipelineDraftToCreate);
            Assert.AreEqual(pipelineDraftToCreate.Name, createdDraft.Name, $"PipelineDraft with id: {createdDraft.Id}");

            var fetchedDraft = await _testEnvironment.GetPipelineDraftByIdAsync(createdDraft.Id);
            Assert.AreEqual(createdDraft.Id, fetchedDraft.Id);

            var pipelineRun = await _testEnvironment.SubmitPipelineRunFromPipelineDraftAsync(fetchedDraft);
            fetchedDraft = await _testEnvironment.GetPipelineDraftByIdAsync(createdDraft.Id);
            Assert.AreEqual(pipelineRun.Id, fetchedDraft.LastSubmittedPipelineRunId);

            var deepPipelineRun = await _testEnvironment.GetDeepPipelineVisualGraphWithEntityInterfaceAsync(pipelineRun.Id);
            Assert.AreEqual(pipelineRun.Id, deepPipelineRun.PipelineRunEntity.Id);
            Assert.IsNotNull(deepPipelineRun.GraphWithInterface);
            Assert.IsNotNull(deepPipelineRun.GraphWithInterface.Interface);
            Assert.IsNotNull(deepPipelineRun.GraphWithInterface.VisualGraph);
            Assert.IsNotNull(deepPipelineRun.DataSourceEntities);
            Assert.AreEqual(0, deepPipelineRun.DataSourceEntities.Count(), $"Fetched {deepPipelineRun.DataSourceEntities.Count()} data source entities");
            Assert.AreEqual(1, deepPipelineRun.Modules.Count(), $"Fetched {deepPipelineRun.Modules.Count()} modules");

            await _testEnvironment.DeleteGraphDraftAsync(graphDraft);
            await _testEnvironment.DeletePipelineDraftAsync(fetchedDraft);
        }

        // [Test]
        public async Task TestParameterizeDatasetOutputDestination()
        {
            var creation = JsonConvert.DeserializeObject<PipelineCreationInfo>(File.ReadAllText($"TestResources/PipelineResources/PipelineCreation.json"));
            //always query metastore to get the moduleid, in case PipelineCreation.json info is outdated
            var amlModule = await _testEnvironment.GetAmlModuleByNameAsync("AddAndMultiply");
            var moduleId = amlModule.Versions.FirstOrDefault(x => x.Version == "1").ModuleVersionId;
            creation.Graph.ModuleNodes.First().ModuleId = moduleId;

            var pipelineId = await _testEnvironment.CreatePipelineAsync(creation).ConfigureAwait(false);
            Assert.IsNotNull(pipelineId);

            var submission = new PipelineSubmissionInfo()
            {
                ExperimentName = "test_parameterize_dataset_output_destination",
                DisplayName = "TestParameterizeDatasetOutputDestination",
                Description = "integration test for ParameterizeDatasetOutputDestination",
                ParameterAssignments = new Dictionary<string, string> { { "initialNum", "3" } },
                DataPathAssignments = new Dictionary<string, LegacyDataPath>()
                {
                    { "param_sum", new LegacyDataPath() { DataStoreName = "myblobdatastore" } }
                },
            };
            var pipelineRunId = await _testEnvironment.SubmitPipelineRunFromPipelineAsync(
                pipelineId: pipelineId,
                pipelineSubmissionInfo: submission).ConfigureAwait(false);

            IPipelineRun pipelineRun = await FetchExperimentOnceCompleteAsync(
                environment: _testEnvironment,
                pipelineRunId: pipelineRunId).ConfigureAwait(false);

            Assert.AreEqual(PipelineRunStatusCode.Finished, pipelineRun.CachedStatus, $"experiment id: {pipelineRun.Id}");

            var pipelineRunEntity = await _testEnvironment.GetPipelineRunEntityAsync(pipelineRunId).ConfigureAwait(false);
            Assert.AreEqual("TestParameterizeDatasetOutputDestination", pipelineRunEntity.DisplayName);

            var createInfo = new PipelineRunCreationInfo
            {
                DisplayName = "TestDisplayNameForResubmit",
                Description = "TestDisplayNameForResubmit",
                RunHistoryExperimentName = "TestResubmitRun",
            };
            var runId = await _testEnvironment.ResubmitPipelineRunAsync(pipelineRunEntity.Id, createInfo).ConfigureAwait(false);
            pipelineRunEntity = await _testEnvironment.GetPipelineRunEntityAsync(runId).ConfigureAwait(false);
            Assert.AreEqual("TestDisplayNameForResubmit", pipelineRunEntity.DisplayName);
        }

        [Test]
        public async Task TestRecordAssetAsPipelineParameterToRHForPipelineRuns()
        {
            var creation = JsonConvert.DeserializeObject<PipelineCreationInfo>(File.ReadAllText($"TestResources/PipelineResources/PipelineWithAssetInput.json"));
            var pipelineId = await _testEnvironment.CreatePipelineAsync(creation).ConfigureAwait(false);
            Assert.IsNotNull(pipelineId);
            var submission = new PipelineSubmissionInfo()
            {
                ExperimentName = "TestRecordInfoToRH",
                DisplayName = "TestRecordInfoToRH",
                Description = "Test record info to RH for pipeline run",
            };
            var pipelineRunId = await _testEnvironment.SubmitPipelineRunFromPipelineAsync(
                pipelineId: pipelineId,
                pipelineSubmissionInfo: submission).ConfigureAwait(false);
            var runDto = await _runHistoryApiCaller.GetRunDetails(submission.ExperimentName, pipelineRunId).ConfigureAwait(false);
            Assert.IsNotNull(runDto.Inputs);
            Assert.AreEqual(1, runDto.Inputs.Count());
            var inputAssetKV = runDto.Inputs.First();
            Assert.AreEqual("DataSet1", inputAssetKV.Key);
            var inputAsset = inputAssetKV.Value;
            Assert.AreEqual("azureml://locations/centraluseuap/workspaces/3c80c156-6e4e-486d-90b3-3e3753ef4a7f/data/azureml_0b8c3130-dd39-48a2-ab73-c2e4aa72edc1_input_data_input/versions/1", inputAsset.AssetId.ToString());
            Assert.AreEqual("UriFile", inputAsset.Type);
        }

        [Test]
        public async Task TestRecordInfoToRHForPipelineRuns()
        {
            var pipelineId = "344bfc97-5c43-4093-a9e3-4e318d1bc19c";
            try
            {
                // try to get the published pipeline. If failed, create it again.
                await _testEnvironment.GetPipeline(pipelineId).ConfigureAwait(false);
            }
            catch (Exception)
            {
                var creation = JsonConvert.DeserializeObject<PipelineCreationInfo>(File.ReadAllText($"TestResources/PipelineResources/PipelineWithDatasetInput.json"));
                pipelineId = await _testEnvironment.CreatePipelineAsync(creation).ConfigureAwait(false);
                Assert.IsNotNull(pipelineId);
            }

            var submission = new PipelineSubmissionInfo()
            {
                ExperimentName = "TestRecordInfoToRH",
                DisplayName = "TestRecordInfoToRH",
                Description = "Test record info to RH for pipeline run",
            };
            var pipelineRunId = await _testEnvironment.SubmitPipelineRunFromPipelineAsync(
                pipelineId: pipelineId,
                pipelineSubmissionInfo: submission).ConfigureAwait(false);

            await FetchExperimentOnceCompleteAsync(
                environment: _testEnvironment,
                pipelineRunId: pipelineRunId).ConfigureAwait(false);

            var runDto = await _runHistoryApiCaller.GetRunDetails(submission.ExperimentName, pipelineRunId).ConfigureAwait(false);
            Assert.AreEqual("c8564483-a7c5-458c-980f-f44d27d0cf27", runDto.InputDatasets.First().Identifier.RegisteredId);
            Assert.AreEqual("1", runDto.InputDatasets.First().Identifier.RegisteredVersion);
            Assert.AreEqual("IntegrationTestTxt1", runDto.InputDatasets.First().InputDetails.InputName);
            Assert.AreEqual("hello_python_world_default", runDto.Parameters["sample_input_string"].ToString());

            // Add delay here because master index service has delay and thus causing flaky test case.
            await Task.Delay(5000);
            var runEntity = await _indexServiceApiCaller.ExtractPipelineRunFromIndexService(pipelineRunId).ConfigureAwait(false);
            // As pipeline team, we should only validate the result when Index service is working.
            if (runEntity.Value.Any())
            {
                Assert.AreEqual(pipelineRunId, runEntity.Value.First().Properties.RunId);
            }

            var pipelineIdWithoutDatasetVersion = "88c97e91-a4e1-4f21-ad1b-be488da7c337";
            try
            {
                // try to get the published pipeline. If failed, create it again.
                await _testEnvironment.GetPipeline(pipelineIdWithoutDatasetVersion).ConfigureAwait(false);
            }
            catch (Exception)
            {
                var creation = JsonConvert.DeserializeObject<PipelineCreationInfo>(File.ReadAllText($"TestResources/PipelineResources/PipelineWithDatasetInput.json"));
                creation.DataSetDefinitionValueAssignments.First().Value.DataSetReference.Version = null;
                creation.GraphInterface.DataPathParameterList.First().DefaultValue.DataSetReference.Version = null;
                pipelineIdWithoutDatasetVersion = await _testEnvironment.CreatePipelineAsync(creation).ConfigureAwait(false);
                Assert.IsNotNull(pipelineIdWithoutDatasetVersion);
            }

            submission = new PipelineSubmissionInfo()
            {
                ExperimentName = "TestRecordInfoToRH",
                DisplayName = "TestRecordInfoToRHWithoutRegisteredDatasetVersion",
                Description = "Test record info to RH for pipeline run without registered dataset version",
            };

            pipelineRunId = await _testEnvironment.SubmitPipelineRunFromPipelineAsync(
                pipelineId: pipelineIdWithoutDatasetVersion,
                pipelineSubmissionInfo: submission).ConfigureAwait(false);

            await FetchExperimentOnceCompleteAsync(
                environment: _testEnvironment,
                pipelineRunId: pipelineRunId).ConfigureAwait(false);

            runDto = await _runHistoryApiCaller.GetRunDetails(submission.ExperimentName, pipelineRunId).ConfigureAwait(false);
            Assert.AreEqual("c8564483-a7c5-458c-980f-f44d27d0cf27", runDto.InputDatasets.First().Identifier.RegisteredId);
            Assert.Null(runDto.InputDatasets.First().Identifier.RegisteredVersion);
            Assert.AreEqual("IntegrationTestTxt1", runDto.InputDatasets.First().InputDetails.InputName);
            Assert.AreEqual("hello_python_world_default", runDto.Parameters["sample_input_string"].ToString());
        }

        // [Test]
        public async Task TestParameterizeDataStoreModeInPipelineParameter()
        {
            var creation = JsonConvert.DeserializeObject<PipelineCreationInfo>(File.ReadAllText($"TestResources/PipelineResources/Pipeline_OneOutputSettingsLackDataStoreMode.json"));
            var amlModule = await _testEnvironment.GetAmlModuleByNameAsync("AddAndMultiply");
            var moduleId = amlModule.Versions.FirstOrDefault(x => x.Version == "1").ModuleVersionId;
            creation.Graph.ModuleNodes.First().ModuleId = moduleId;

            var pipelineId = await _testEnvironment.CreatePipelineAsync(creation).ConfigureAwait(false);
            var submission = new PipelineSubmissionInfo()
            {
                ExperimentName = "test_parameterize_datastore_mode_in_pipline_parameter",
                DisplayName = "TestParameterizeDatastoreMode",
                Description = "TestParameterizeDatastoreMode",
                DataPathAssignments = new Dictionary<string, LegacyDataPath>()
                {
                    // outputSetting don't set dataStoreMode in Graph, set it when submit
                    { "param_sum", new LegacyDataPath() { DataStoreName = "myblobdatastore", DataStoreMode = DataStoreMode.Mount } },
                    // outputSetting set dataStoreMode as Mount in Graph, set it as Upload when submit
                    { "param_prod", new LegacyDataPath() { DataStoreName = "myblobdatastore",  DataStoreMode = DataStoreMode.Upload} }
                },
            };
            var pipelineRunId = await _testEnvironment.SubmitPipelineRunFromPipelineAsync(
                pipelineId: pipelineId,
                pipelineSubmissionInfo: submission).ConfigureAwait(false);

            await FetchExperimentOnceCompleteAsync(
                environment: _testEnvironment,
                pipelineRunId: pipelineRunId).ConfigureAwait(false);

            var childRunIndexEntity = await _indexServiceApiCaller.ListFullChildRunsByIndexService(pipelineRunId).ConfigureAwait(false);
            var childRunId = childRunIndexEntity.First().Properties.RunId;
            var runDetails = await _runHistoryApiCaller.GetRunDetails(submission.ExperimentName, childRunId).ConfigureAwait(false);
            StringAssert.Contains("\"out_sum\": {\r\n      \"dataStoreName\": \"myblobdatastore\",\r\n      \"mode\": \"Mount\"", runDetails.RunDefinition.ToString());
            StringAssert.Contains("\"out_prod\": {\r\n      \"dataStoreName\": \"myblobdatastore\",\r\n      \"mode\": \"Upload\"", runDetails.RunDefinition.ToString());


            var dataPathAssignments = new Dictionary<string, LegacyDataPath>()
            {
                { "param_sum", new LegacyDataPath() { DataStoreName = "myblobdatastore", DataStoreMode = DataStoreMode.Mount} },
                // outputSetting set dataStoreMode in Graph, don't set it when submit
                { "param_prod", new LegacyDataPath() { DataStoreName = "myblobdatastore" } }
            };

            submission.DataPathAssignments = dataPathAssignments;
            pipelineRunId = await _testEnvironment.SubmitPipelineRunFromPipelineAsync(
                pipelineId: pipelineId,
                pipelineSubmissionInfo: submission).ConfigureAwait(false);

            await FetchExperimentOnceCompleteAsync(
                environment: _testEnvironment,
                pipelineRunId: pipelineRunId).ConfigureAwait(false);
            childRunIndexEntity = await _indexServiceApiCaller.ListFullChildRunsByIndexService(pipelineRunId).ConfigureAwait(false);
            childRunId = childRunIndexEntity.First().Properties.RunId;
            runDetails = await _runHistoryApiCaller.GetRunDetails(submission.ExperimentName, childRunId).ConfigureAwait(false);
            StringAssert.Contains("\"out_sum\": {\r\n      \"dataStoreName\": \"myblobdatastore\",\r\n      \"mode\": \"Mount\"", runDetails.RunDefinition.ToString());
            StringAssert.Contains("\"out_prod\": {\r\n      \"dataStoreName\": \"myblobdatastore\",\r\n      \"mode\": \"Mount\"", runDetails.RunDefinition.ToString());


            // Set dataStoreMode as None in Graph when create pipeline, and reset it as Mount when submit
            creation.Graph.ModuleNodes.First().ModuleOutputSettings.First().DataStoreMode = DataStoreMode.None;
            pipelineId = await _testEnvironment.CreatePipelineAsync(creation).ConfigureAwait(false);

            dataPathAssignments = new Dictionary<string, LegacyDataPath>()
            {
                { "param_sum", new LegacyDataPath() { DataStoreName = "myblobdatastore", DataStoreMode = DataStoreMode.Mount } }
            };

            submission.DataPathAssignments = dataPathAssignments;
            pipelineRunId = await _testEnvironment.SubmitPipelineRunFromPipelineAsync(
                pipelineId: pipelineId,
                pipelineSubmissionInfo: submission).ConfigureAwait(false);

            await FetchExperimentOnceCompleteAsync(
                environment: _testEnvironment,
                pipelineRunId: pipelineRunId).ConfigureAwait(false);
            childRunIndexEntity = await _indexServiceApiCaller.ListFullChildRunsByIndexService(pipelineRunId).ConfigureAwait(false);
            childRunId = childRunIndexEntity.First().Properties.RunId;
            runDetails = await _runHistoryApiCaller.GetRunDetails(submission.ExperimentName, childRunId).ConfigureAwait(false);
            StringAssert.Contains("\"out_sum\": {\r\n      \"dataStoreName\": \"myblobdatastore\",\r\n      \"mode\": \"Mount\"", runDetails.RunDefinition.ToString());

            // Set dataStoreMode as None in Graph when create pipeline, and don't reset it when submit
            creation.Graph.ModuleNodes.First().ModuleOutputSettings.First().DataStoreMode = DataStoreMode.None;
            pipelineId = await _testEnvironment.CreatePipelineAsync(creation).ConfigureAwait(false);
            submission.DataPathAssignments = null;
            pipelineRunId = await _testEnvironment.SubmitPipelineRunFromPipelineAsync(
                pipelineId: pipelineId,
                pipelineSubmissionInfo: submission).ConfigureAwait(false);

            await FetchExperimentOnceCompleteAsync(
                environment: _testEnvironment,
                pipelineRunId: pipelineRunId).ConfigureAwait(false);
            childRunIndexEntity = await _indexServiceApiCaller.ListFullChildRunsByIndexService(pipelineRunId).ConfigureAwait(false);
            childRunId = childRunIndexEntity.First().Properties.RunId;
            runDetails = await _runHistoryApiCaller.GetRunDetails(submission.ExperimentName, childRunId).ConfigureAwait(false);
            Assert.AreEqual("DataStore mode None is not allowed for output out_sum", runDetails.Error.Error.Message);
        }

        [Test]
        public async Task TestCreateUnsubmittedPipelineRunAsync()
        {
            var singleNodeGraph = new GraphEntity
            {
                ModuleNodes = new[]
                {
                    new GraphModuleNode()
                    {
                        ModuleId = "a65b9756-8440-4830-88cc-e77c0452f6c3",
                        Id = "faab7d04",
                        ModuleParameters = new[]
                        {
                            new ParameterAssignment("Parameter 1", "hello"),
                            new ParameterAssignment("Parameter 2", "Red"),
                            new ParameterAssignment("Parameter 3", "2"),
                        },
                    },
                },
                DatasetNodes = new[]
                {
                    new GraphDatasetNode
                    {
                        Id = "faab7d01",
                        DatasetId = null,
                        DataSetDefinition = new DataSetDefinition
                        {
                            DataTypeShortName = "DataFrameDirectory",
                            Value = new DataSetDefinitionValue
                            {
                                LiteralValue = new DataPath
                                {
                                    DataStoreName = "workspaceblobstore",
                                    RelativePath = "azureml/7d3b2744-3609-4f5c-9d53-ec7ce00de404/dataset",
                                },
                            },
                        },
                    },
                },
                Edges = new[]
                {
                    new GraphEdge
                    {
                        SourceOutputPort = new PortInfo
                        {
                            NodeId = "faab7d01",
                            PortName = null,
                        },
                        DestinationInputPort = new PortInfo
                        {
                            NodeId = "faab7d04",
                            PortName = "Input_Port",
                        },
                    },
                },
                DefaultCompute = new ComputeSetting { Name = "cpucluster" },
                DefaultDatastore = new DatastoreSetting { DataStoreName = "workspaceblobstore" },
            };

            var createInfoWithGraph = new PipelineRunCreationInfoWithGraph
            {
                Graph = singleNodeGraph,
                CreationInfo = new PipelineRunCreationInfo
                {
                    DisplayName = "TestDisplayNameForSubmitRunWithGraph",
                    Description = "TestDisplayNameForSubmitRunWithGraph",
                    RunHistoryExperimentName = "TestSubmitPipelineWithDraft",
                },
                GraphInterface = new EntityInterface(new NodePortInterface(Array.Empty<NodeInputPort>(), Array.Empty<NodeOutputPort>(), Array.Empty<ControlOutput>()),
                                                     Array.Empty<Parameter>()),
            };

            var pipelineRunEntity = await _testEnvironment.CreateUnsubmittedPipelineRunAsync(createInfoWithGraph).ConfigureAwait(false);
            Assert.AreEqual("TestDisplayNameForSubmitRunWithGraph", pipelineRunEntity.DisplayName);
        }

        [Test]
        public async Task TestGetPipelineRunAsyncSuccess()
        {
            Assert.IsNotNull(await _testEnvironment.GetPipelineRunAsync("73456c35-e9ae-4566-8f31-a7088b7f2c94").ConfigureAwait(false));
        }

        [Test]
        public async Task TestGetPipelineRunAsyncNotFound()
        {
            var notExistId = "NotExistPipelineRunId";
            var exception = Assert.ThrowsAsync<AetherServiceException>(async () => await _testEnvironment.GetPipelineRunAsync(notExistId).ConfigureAwait(false));
            Assert.IsTrue(exception.Message.Contains("\"Code\": \"NotFound\","));
        }

        [Test]
        public async Task TestGetPipelineRunAsyncNotFoundAsTryGet()
        {
            var notExistId = "NotExistPipelineRunId";

            // Try get will return null result for 404 error.
            Assert.IsNull(await _testEnvironment.GetPipelineRunAsync(notExistId, isTry: true).ConfigureAwait(false));
        }

        [Test]
        public async Task TestSubmitSavedPipelineRunAsyncAndResubmitPipelineRunAsync()
        {
            var tempGraph = await _testEnvironment.GetGraphEntityAsync("0383f783-7772-43b5-af22-fb565e097b19");
            var createInfoWithGraph = new PipelineRunCreationInfoWithGraph
            {
                Graph = tempGraph,
                CreationInfo = new PipelineRunCreationInfo
                {
                    DisplayName = "TestSubmitSavedPipelineRun",
                    Description = "TestSubmitSavedPipelineRun",
                    RunHistoryExperimentName = "TestSubmitSavedPipelineRun",
                    ContinueExperimentOnNodeFailure = true,
                },
                GraphInterface = await _testEnvironment.GetGraphInterfaceAsync("0383f783-7772-43b5-af22-fb565e097b19"),
            };

            var pipelineRunCreationInfoForResubmit = new PipelineRunCreationInfo
            {
                DisplayName = "TestResubmitSavedPipelineRun",
                Description = "TestResubmitSavedPipelineRun",
                RunHistoryExperimentName = "TestResubmitSavedPipelineRun",
                ContinueExperimentOnNodeFailure = true,
            };

            var pipelineRunEntity = await _testEnvironment.CreateUnsubmittedPipelineRunAsync(createInfoWithGraph).ConfigureAwait(false);
            var runIdForSubmit = pipelineRunEntity.PipelineRunId;
            await _testEnvironment.SubmitPipelineRunAsync(runIdForSubmit, null).ConfigureAwait(false);
            var runDto = await _runHistoryApiCaller.GetRunDto("TestSubmitSavedPipelineRun", runIdForSubmit).ConfigureAwait(false);
            Assert.AreEqual("True", runDto.Properties["azureml.continue_on_step_failure"]);
            Assert.IsFalse(runDto.Properties.ContainsKey("azureml.sourcepipelinerunid"));
            Assert.IsFalse(runDto.Properties.ContainsKey("azureml.runLineageType"));

            string runIdForResubmit = await _testEnvironment.ResubmitPipelineRunAsync(pipelineRunEntity.PipelineRunId, pipelineRunCreationInfoForResubmit).ConfigureAwait(false);
            var runDtoForResubmit = await _runHistoryApiCaller.GetRunDto("TestResubmitSavedPipelineRun", runIdForResubmit).ConfigureAwait(false);
            Assert.AreEqual("True", runDtoForResubmit.Properties["azureml.continue_on_step_failure"]);
            Assert.AreEqual("resubmit", runDtoForResubmit.Properties["azureml.runLineageType"]);
            Assert.AreEqual(runIdForSubmit, runDtoForResubmit.Properties["azureml.sourcepipelinerunid"]);
        }

        [Test]
        public async Task TestResubmitPublishedPipelineRun()
        {
            var publishedPipelineId = "f661af6c-9ce2-4967-91e0-85a26bbd807c";
            var pipelineSubmissionInfo = new PipelineSubmissionInfo
            {
                DisplayName = "SubmitRunFromPublishedPipeline",
                Description = "SubmitRunFromPublishedPipeline",
                ExperimentName = "SubmitRunFromPublishedPipeline",
            };
            var runId = await _testEnvironment.SubmitPipelineRunFromPipelineAsync(publishedPipelineId, pipelineSubmissionInfo).ConfigureAwait(false);

            var resubmitSubmissionInfo = new PipelineRunCreationInfo
            {
                DisplayName = "TestResubmitPublishedPipelineRuns",
                Description = "TestResubmitPublishedPipelineRuns",
                RunHistoryExperimentName = "TestResubmitPublishedPipelineRuns",
            };
            var resubmitRunId = await _testEnvironment.ResubmitPipelineRunAsync(runId, resubmitSubmissionInfo).ConfigureAwait(false);
            var runDto = await _runHistoryApiCaller.GetRunDetails(resubmitSubmissionInfo.RunHistoryExperimentName, resubmitRunId).ConfigureAwait(false);
            Assert.AreEqual("resubmit", runDto.Properties["azureml.runLineageType"]);
            Assert.AreEqual(runId, runDto.Properties["azureml.sourcepipelinerunid"]);
        }

        [Test]
        public async Task TestExposeRootErrorCauseForRunHistory()
        {
            var tempGraph = await _testEnvironment.GetGraphEntityAsync("0383f783-7772-43b5-af22-fb565e097b19");
            string oversizeDescription = new string('1', 5005);
            var createInfoWithGraph = new PipelineRunCreationInfoWithGraph
            {
                Graph = tempGraph,
                CreationInfo = new PipelineRunCreationInfo
                {
                    DisplayName = "TestSubmitSavedPipelineRun",
                    Description = oversizeDescription,
                    RunHistoryExperimentName = "TestSubmitSavedPipelineRun",
                    ContinueExperimentOnNodeFailure = true,
                },
                GraphInterface = await _testEnvironment.GetGraphInterfaceAsync("0383f783-7772-43b5-af22-fb565e097b19"),
            };
            var pipelineRunEntity = await _testEnvironment.CreateUnsubmittedPipelineRunAsync(createInfoWithGraph).ConfigureAwait(false);
            try
            {
                await _testEnvironment.SubmitPipelineRunAsync(pipelineRunEntity.PipelineRunId, null).ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                StringAssert.Contains("A field of the entity is over the size limit.", ex.Message);
                StringAssert.Contains("BadRequest", ex.Message);
            }
        }

        [Test]
        public async Task TestSubmitRunFromClonedDraft()
        {
            var runIdForCloneDraft = "349bc650-9e7b-49bd-be6f-56da8264399f";
            var cloneDraft = await _testEnvironment.CloneFromPipelineRunAsync(runIdForCloneDraft).ConfigureAwait(false);
            Assert.AreEqual(cloneDraft.ParentPipelineRunId, runIdForCloneDraft);

            var run = await _testEnvironment.SubmitPipelineRunFromPipelineDraftAsync(cloneDraft).ConfigureAwait(false);
            var runDto = await _runHistoryApiCaller.GetRunDto(run.RunHistoryExperimentName, run.Id).ConfigureAwait(false);
            Assert.AreEqual(runIdForCloneDraft, runDto.Properties["azureml.sourcepipelinerunid"]);
            var targetContinueRunOnStepFailure = cloneDraft.PipelineSubmissionInfo.ContinueRunOnStepFailure ?? false;
            Assert.AreEqual(targetContinueRunOnStepFailure.ToString(), runDto.Properties["azureml.continue_on_step_failure"]);
            await _testEnvironment.DeletePipelineDraftAsync(cloneDraft).ConfigureAwait(false);
            Assert.AreEqual("cloned", runDto.Properties["azureml.runLineageType"]);
        }

        [Test]
        public async Task TestSubmitRunFromDirectlyCreatedDraft()
        {
            var runIdForDraftToGetGraph = "349bc650-9e7b-49bd-be6f-56da8264399f";
            var draftToGetGraph = await _testEnvironment.CloneFromPipelineRunAsync(runIdForDraftToGetGraph).ConfigureAwait(false);
            var GraphId = draftToGetGraph.GraphDraftId;
            var DirectlyCreatedPipelineDraft = new PipelineDraft()
            {
                Name = "DirectlyCreatedPipelineDraft",
                PipelineSubmissionInfo = new PipelineSubmissionInfo()
                {
                    ExperimentName = "DirectlyCreatedSubmissionInfo",
                    Description = "created directly from pipeline draft"
                },
                GraphDraftId = GraphId,
                KvTags = new Dictionary<string, string>
                {
                    {"tag", "value"}
                }
            };

            var runForDirectlyCreatedDraft = await _testEnvironment.SubmitPipelineRunFromPipelineDraftAsync(DirectlyCreatedPipelineDraft).ConfigureAwait(false);
            var runDtoForDirectlyCreatedDraft = await _runHistoryApiCaller.GetRunDto(runForDirectlyCreatedDraft.RunHistoryExperimentName, runForDirectlyCreatedDraft.Id).ConfigureAwait(false);
            Assert.IsFalse(runDtoForDirectlyCreatedDraft.Properties.ContainsKey("azureml.runLineageType"));
            Assert.IsFalse(runDtoForDirectlyCreatedDraft.Properties.ContainsKey("azureml.sourcepipelinerunid"));
        }

        [Test]
        public async Task TestCancelPipelineRunFromCancelUri()
        {
            var creation = JsonConvert.DeserializeObject<PipelineCreationInfo>(File.ReadAllText($"TestResources\\PipelineResources\\PipelineWithDatasetInput.json"));
            var pipelineId = await _testEnvironment.CreatePipelineAsync(creation).ConfigureAwait(false);
            Assert.IsNotNull(pipelineId);

            var submission = new PipelineSubmissionInfo()
            {
                ExperimentName = "TestCancelFromMT",
                DisplayName = "TestCancelFromMT",
                Description = "Test cancel pipeline run with MT cancel url",
            };
            var pipelineRunId = await _testEnvironment.SubmitPipelineRunFromPipelineAsync(
                pipelineId: pipelineId,
                pipelineSubmissionInfo: submission).ConfigureAwait(false);
            var runDto = await _runHistoryApiCaller.GetRunDto(submission.ExperimentName, pipelineRunId).ConfigureAwait(false);
            Assert.IsTrue(runDto.CancelUri.Contains("studioservice"));

            var httpClient = new ClientFactory(_tokenProvider.GetAccessTokenAsync).CreateHttpClient();
            var cancelResponse = await httpClient.PostAsync(runDto.CancelUri, new StringContent(string.Empty, Encoding.UTF8, Microsoft.Aether.BlueBox.Library.HttpClientExtensions.ContentType)).ConfigureAwait(false);

            // To verify cancel API could be correctly executed
            if (cancelResponse.StatusCode == HttpStatusCode.BadRequest)
            {
                var errorRes = cancelResponse.Content.ReadAsStringAsync().Result;
                Assert.True(errorRes.Contains($"The pipeline run {pipelineRunId} is in terminal status, it can't be canceled"));
            }
            else
            {
                // In a very low probability, cancelling a pipeline directly would get 412.
                Assert.IsTrue(cancelResponse.StatusCode == HttpStatusCode.OK || cancelResponse.StatusCode == HttpStatusCode.PreconditionFailed);
            }
        }
    }
}