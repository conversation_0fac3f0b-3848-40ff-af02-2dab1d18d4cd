﻿using BBAetherLibrary.IntegrationTests.Common;
using Microsoft.Aether.AEVA.DataContracts;
using Microsoft.Aether.BlueBox.Library;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BBAetherLibrary.IntegrationTests.TestFixtures
{
    [TestFixture]
    [Parallelizable(ParallelScope.All)]
    public class PipelineDraftTests : BBAetherTestFixtureBase
    {
        [Test]
        public async Task TestPipelineDraftCreateAsync()
        {
            string sourcePath = "TestResources/EscloudFile/helloworld";
            string DSVMModuleId = await BBAetherEnvironmentUtils.UploadESCloudModule(_testEnvironment, sourcePath, "fake-compute");
            IGraph graph = await BBAetherEnvironmentUtils.CreateSingleNodeGraph(_testEnvironment, DSVMModuleId);
            graph.AddDefaultCompute(new ComputeSetting { Name = _testConfig.AmlComputeName, ComputeType = ComputeType.MLC });
            graph.AddDefaultDatastore(new DatastoreSetting { DataStoreName = _testConfig.BlobDatastoreName });
            var moduleNode = graph.ModuleNodes.FirstOrDefault();

            moduleNode.SetComputeSetting(true);
            moduleNode.SetDatastoreSetting(true);

            GraphDraft createdGraphDraft = await _testEnvironment.CreateGraphDraftAsync(graph);
            createdGraphDraft.ModuleNodes.FirstOrDefault().Parameters["Arguments"].Value = "new";
            await _testEnvironment.UpdateGraphDraftAsync(createdGraphDraft);

            GraphDraft graphDraft = await _testEnvironment.GetGraphDraftAsync(createdGraphDraft.Id);

            var pipelineDraftToCreate = new PipelineDraft()
            {
                Name = "NewPipelineDraft",
                PipelineSubmissionInfo = new PipelineSubmissionInfo()
                {
                    ExperimentName = "helloworld",
                    Description = "created from pipeline draft"
                },
                GraphDraftId = createdGraphDraft.Id,
                KvTags = new Dictionary<string, string>
                {
                    {"tag", "value"}
                },
                Properties = new Dictionary<string, string>
                {
                    { "prop1", "valu2"}
                }
            };

            var createdDraft = await _testEnvironment.CreatePipelineDraftAsync(pipelineDraftToCreate);
            Assert.AreEqual(pipelineDraftToCreate.Name, createdDraft.Name, $"PipelineDraft with id: {createdDraft.Id}");

            var fetchedDeepDraft = await _testEnvironment.GetDeepPipelineGraphDraftEntityAsync(createdDraft.Id);
            Assert.AreEqual(createdDraft.Id, fetchedDeepDraft.PipelineDraft.Id);
            Assert.AreEqual(1, fetchedDeepDraft.Modules.Count(), $"Fetched {fetchedDeepDraft.Modules.Count()} modules");

            var fetchedDraft = await _testEnvironment.GetPipelineDraftByIdAsync(createdDraft.Id);
            Assert.AreEqual(createdDraft.Id, fetchedDraft.Id);

            fetchedDraft.Description = "updated description";
            fetchedDraft.Properties = new Dictionary<string, string>
            {
                { "prop2", "valu3"}
            };
            fetchedDraft.KvTags = new Dictionary<string, string>
            {
                { "tag2", "valu3"}
            };
            await _testEnvironment.SavePipelineDraftAsync(fetchedDraft);
            fetchedDraft = await _testEnvironment.GetPipelineDraftByIdAsync(createdDraft.Id);
            Assert.AreEqual("updated description", fetchedDraft.Description);

            fetchedDraft.PipelineSubmissionInfo.DisplayName = "testDisplayName";
            // Specify a custom run id
            var customRunId = Guid.NewGuid().ToString();
            fetchedDraft.PipelineSubmissionInfo.RunId = customRunId;
            var pipelineRun = await _testEnvironment.SubmitPipelineRunFromPipelineDraftAsync(fetchedDraft);
            Assert.AreEqual("testDisplayName", pipelineRun.DisplayName);
            Assert.AreEqual(customRunId, pipelineRun.Id);
            fetchedDraft = await _testEnvironment.GetPipelineDraftByIdAsync(createdDraft.Id);
            Assert.AreEqual(pipelineRun.Id, fetchedDraft.LastSubmittedPipelineRunId);

            var newPipelineDraft = await _testEnvironment.CloneFromPipelineRunAsync(pipelineRun.Id);
            Assert.AreEqual(pipelineRun.Id, newPipelineDraft.ParentPipelineRunId);
            var clonedPipelineRun = await _testEnvironment.SubmitPipelineRunFromPipelineDraftAsync(newPipelineDraft);

            var finishedPipelineRun = await FetchExperimentOnceCompleteAsync(_testEnvironment, pipelineRun.Id);
            Assert.AreEqual(PipelineRunStatusCode.Finished, finishedPipelineRun.CachedStatus, $"experiment id: {pipelineRun.Id}");

            finishedPipelineRun = await FetchExperimentOnceCompleteAsync(_testEnvironment, clonedPipelineRun.Id);
            Assert.AreEqual(PipelineRunStatusCode.Finished, finishedPipelineRun.CachedStatus, $"cloned experiment id: {clonedPipelineRun.Id}");

            var deepPipelineRun = await _testEnvironment.GetDeepPipelineVisualGraphWithEntityInterfaceAsync(pipelineRun.Id);
            Assert.AreEqual(pipelineRun.Id, deepPipelineRun.PipelineRunEntity.Id);
            Assert.AreEqual(1, deepPipelineRun.Modules.Count(), $"Fetched {deepPipelineRun.Modules.Count()} modules");

            await _testEnvironment.DeleteGraphDraftAsync(graphDraft);
            await _testEnvironment.DeletePipelineDraftAsync(fetchedDraft);
        }

        [Test]
        public async Task TestPipelineDraftCreateFromPipelineAsync()
        {
            string sourcePath = "TestResources/EscloudFile/helloworld";
            string DSVMModuleId = await BBAetherEnvironmentUtils.UploadESCloudModule(_testEnvironment, sourcePath, _testConfig.AmlComputeName);
            IGraph graph = await BBAetherEnvironmentUtils.CreateSingleNodeGraph(_testEnvironment, DSVMModuleId);

            PipelineCreationInfo creationInfo = new PipelineCreationInfo()
            {
                Name = "TestPipeline",
                Description = "one node pipeline",
                Version = "1.0"
            };

            var pipelineId = await _testEnvironment.CreatePipelineAsync(creationInfo, graph);

            var newPipelineDraft = await _testEnvironment.CloneFromPipelineAsync(pipelineId);
            Assert.AreEqual(pipelineId, newPipelineDraft.ParentPipelineId);

            newPipelineDraft.PipelineSubmissionInfo.ExperimentName = "helloworld1";
            await _testEnvironment.SavePipelineDraftAsync(newPipelineDraft);
        }

        [Test]
        public async Task TestListPipelineDraftsAsync()
        {
            var pipelineDraftToCreate = new PipelineDraft()
            {
                Name = "NewPipelineDraft",
                PipelineSubmissionInfo = new PipelineSubmissionInfo()
                {
                    ExperimentName = "helloworld",
                    Description = "created from pipeline draft"
                },
                KvTags = new Dictionary<string, string>
                {
                    {"tag", "value"},
                    {"tag2", "value2"},
                    {"tag3", "value3"}
                }
            };

            var createdDraft = await _testEnvironment.CreatePipelineDraftAsync(pipelineDraftToCreate);
            Assert.AreEqual(pipelineDraftToCreate.Name, createdDraft.Name, $"PipelineDraft with id: {createdDraft.Id}");

            var pipelineDrafts = new List<PipelineDraft>((await _testEnvironment.ListPipelineDraftsAsync(null, null, 100)).Entity);
            Assert.Greater(pipelineDrafts.Count, 0);

            Dictionary<string, string> filters = new Dictionary<string, string>
            {
                {"tag", "value"},
                {"tag2", "value2"}
            };

            var pipelineDraftIds = (await _testEnvironment.ListPipelineDraftsAsync(filters, null, 100)).Entity.Select(p => p.Id);
            Assert.True(pipelineDraftIds.Contains(createdDraft.Id));

            filters.Add("newtag", "newvalue");
            pipelineDraftIds = (await _testEnvironment.ListPipelineDraftsAsync(filters, null, 100)).Entity.Select(p => p.Id);
            Assert.False(pipelineDraftIds.Contains(createdDraft.Id));
        }

        [Test]
        public async Task TestPipelineDraftCloneAsync()
        {
            string sourcePath = "TestResources/EscloudFile/helloworld";
            string DSVMModuleId = await BBAetherEnvironmentUtils.UploadESCloudModule(_testEnvironment, sourcePath, _testConfig.AmlComputeName);
            IGraph graph = await BBAetherEnvironmentUtils.CreateSingleNodeGraph(_testEnvironment, DSVMModuleId);

            GraphDraft createdGraphDraft = await _testEnvironment.CreateGraphDraftAsync(graph);
            await _testEnvironment.UpdateGraphDraftAsync(createdGraphDraft);
            GraphDraft graphDraft = await _testEnvironment.GetGraphDraftAsync(createdGraphDraft.Id);

            var pipelineDraftToCreate = new PipelineDraft()
            {
                Name = "NewPipelineDraft",
                PipelineSubmissionInfo = new PipelineSubmissionInfo()
                {
                    ExperimentName = "helloworld",
                    Description = "created from pipeline draft"
                },
                GraphDraftId = createdGraphDraft.Id,
                KvTags = new Dictionary<string, string>
                {
                    {"tag", "value"}
                }
            };

            var createdDraft = await _testEnvironment.CreatePipelineDraftAsync(pipelineDraftToCreate);
            Assert.AreEqual(pipelineDraftToCreate.Name, createdDraft.Name, $"PipelineDraft with id: {createdDraft.Id}");

            var clonedDraft = await _testEnvironment.CloneFromPipelineDraftAsync(createdDraft.Id);
            Assert.AreEqual(createdDraft.Id, clonedDraft.ParentPipelineDraftId);
            Assert.AreNotEqual(createdDraft.GraphDraftId, clonedDraft.GraphDraftId);

            GraphDraft clonedGraphDraft = await _testEnvironment.GetGraphDraftAsync(clonedDraft.GraphDraftId);
            Assert.IsNotNull(clonedGraphDraft.ModuleNodes);
        }

        [Test]
        public async Task TestPipelineDraftCreatePipelineAsync()
        {
            string sourcePath = "TestResources/EscloudFile/helloworld";
            string DSVMModuleId = await BBAetherEnvironmentUtils.UploadESCloudModule(_testEnvironment, sourcePath, _testConfig.AmlComputeName);
            IGraph graph = await BBAetherEnvironmentUtils.CreateSingleNodeGraph(_testEnvironment, DSVMModuleId);

            GraphDraft createdGraphDraft = await _testEnvironment.CreateGraphDraftAsync(graph);
            await _testEnvironment.UpdateGraphDraftAsync(createdGraphDraft);
            GraphDraft graphDraft = await _testEnvironment.GetGraphDraftAsync(createdGraphDraft.Id);

            var pipelineDraftToCreate = new PipelineDraft()
            {
                Name = "NewPipelineDraft",
                PipelineSubmissionInfo = new PipelineSubmissionInfo()
                {
                    ExperimentName = "helloworld",
                    Description = "created from pipeline draft"
                },
                GraphDraftId = createdGraphDraft.Id,
                KvTags = new Dictionary<string, string>
                {
                    {"tag", "value"}
                }
            };

            var createdDraft = await _testEnvironment.CreatePipelineDraftAsync(pipelineDraftToCreate);
            Assert.AreEqual(pipelineDraftToCreate.Name, createdDraft.Name, $"PipelineDraft with id: {createdDraft.Id}");

            var pipeline = await _testEnvironment.CreatePipelineFromPipelineDraftAsync(createdDraft);

            graph = await _testEnvironment.GetGraphAsync(pipeline.GraphId);
            Assert.IsNotNull(graph.ModuleNodes);
        }

        [Test]
        public async Task TestPipelineDraftGetDeepEntityAsync()
        {
            string sourcePath = "TestResources/EscloudFile/helloworld";
            string DSVMModuleId = await BBAetherEnvironmentUtils.UploadESCloudModule(_testEnvironment, sourcePath, _testConfig.AmlComputeName);
            IGraph graph = await BBAetherEnvironmentUtils.CreateSingleNodeGraph(_testEnvironment, DSVMModuleId);

            GraphDraft createdGraphDraft = await _testEnvironment.CreateGraphDraftAsync(graph);
            await _testEnvironment.UpdateGraphDraftAsync(createdGraphDraft);
            GraphDraft graphDraft = await _testEnvironment.GetGraphDraftAsync(createdGraphDraft.Id);

            var pipelineDraftToCreate = new PipelineDraft()
            {
                Name = "NewPipelineDraft",
                PipelineSubmissionInfo = new PipelineSubmissionInfo()
                {
                    ExperimentName = "helloworld",
                    Description = "created from pipeline draft"
                },
                GraphDraftId = createdGraphDraft.Id,
                KvTags = new Dictionary<string, string>
                {
                    {"tag", "value"}
                }
            };

            var createdDraft = await _testEnvironment.CreatePipelineDraftAsync(pipelineDraftToCreate);
            Assert.AreEqual(pipelineDraftToCreate.Name, createdDraft.Name, $"PipelineDraft with id: {createdDraft.Id}");

            var fetchedDeepDraft = await _testEnvironment.GetDeepPipelineGraphDraftEntityAsync(createdDraft.Id);
            Assert.AreEqual(createdDraft.Id, fetchedDeepDraft.PipelineDraft.Id);
            Assert.IsNotNull(fetchedDeepDraft.GraphDraftEntity);
            Assert.IsNotNull(fetchedDeepDraft.DataSourceEntities);
            Assert.AreEqual(0, fetchedDeepDraft.DataSourceEntities.Count(), $"Fetched {fetchedDeepDraft.DataSourceEntities.Count()} data source entities");
            Assert.AreEqual(1, fetchedDeepDraft.Modules.Count(), $"Fetched {fetchedDeepDraft.Modules.Count()} modules");

            await _testEnvironment.DeleteGraphDraftAsync(graphDraft);
        }

        [Test]
        public async Task GetNestedGraphDraftInfoWithUnRegisteredPipelineComponent()
        {
            // repo step:
            // 1. create any pipeline component for a empty draft in ux
            // 2. get all subGraph by calling GetNestedGraphDraftInfoAsync
            var graphWithUnregisteredPipelineComponentDraftId = "2a877c96-17a0-4420-b466-ec0c741839da";
            var nestedResourceInfo = await _testEnvironment.GetNestedGraphDraftInfoAsync(graphWithUnregisteredPipelineComponentDraftId, fetchNestedGraphs: true).ConfigureAwait(false);
            Assert.AreEqual(1, nestedResourceInfo.GraphDrafts.Count());
            Assert.AreEqual(2, nestedResourceInfo.GraphDrafts.First().Value.SubGraphNodes.Count());
        }
    }
}
