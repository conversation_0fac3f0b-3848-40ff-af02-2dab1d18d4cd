﻿using BBAetherLibrary.IntegrationTests.Common;
using Microsoft.Aether.BlueBox.Library;
using Microsoft.IdentityModel.Clients.ActiveDirectory;
using Microsoft.RelInfra.Common;
using NUnit.Framework;
using System;
using System.Threading.Tasks;

namespace BBAetherLibrary.IntegrationTests.TestFixtures
{
    [TestFixture]
    [Parallelizable(ParallelScope.All)]
    public class RBACTests : BBAetherTestFixtureBase
    {
        private const string PIPELINE_USER_SP_APP_NAME = "RBACTestForPipelineUserSPKey";
        private const string PIPELINE_USER_SP_KEY_NAME = "RBACTestForPipelineUserSPPassword";
        private const string ENV_CONFIG = "EnvironmentConfigs/int/pipeline_tests.xml";

        private async Task<AetherEnvironment> SetupPipelineUser()
        {
            string appIdUri = await _testKeyVaultClient.GetSecretValueAsync(PIPELINE_USER_SP_APP_NAME);
            string appKeyUri = await _testKeyVaultClient.GetSecretValueAsync(PIPELINE_USER_SP_KEY_NAME);

            var authenticationContext = new AuthenticationContext("https://login.windows.net/72f988bf-86f1-41af-91ab-2d7cd011db47");
            AuthenticationResult authResult = await authenticationContext.AcquireTokenAsync(AdalConstants.ArmResource, new ClientCredential(appIdUri, appKeyUri));

            return BBAetherEnvironmentUtils.CreateTestAetherEnvironment(ENV_CONFIG, GetAetherEndpoint(), () => Task.FromResult(authResult.AccessToken));
        }

        [Test]
        [Ignore("Legacy test: no need anymore")]
        public async Task TestRBACUnauthorizeExceptionAsync()
        {
            AetherEnvironment pipelineUserAetherEnvironment = await SetupPipelineUser();
            AetherServiceException ex = Assert.ThrowsAsync<AetherServiceException>(
                                            async () => await DataStoreUtils.CreateDataStoreDataSourceAsync(
                                                pipelineUserAetherEnvironment,
                                                name: "PipelineUserBlob",
                                                description: "PipelineUserBlob",
                                                amlDataStoreName: "workspaceblobstore",
                                                pathOnDataStore: "PipelineUserBlobPath",
                                                identifierHash: Guid.NewGuid().ToString(),
                                                contentHash: null));
            StringAssert.Contains("does not have permissions for Microsoft.MachineLearningServices/workspaces/datasets/unregistered/read actions", ex.Message);
            StringAssert.Contains("ForbiddenError", ex.Message);
        }

        [Test]
        [Ignore("No need to test since other test cases already cover this")]
        public async Task TestRBACAuthorizeReadAsync()
        {
            var datasourceId = await DataStoreUtils.CreateDataStoreDataSourceAsync(
                                    _testEnvironment,
                                    name: "PipelineUserBlob",
                                    description: "PipelineUserBlob",
                                    amlDataStoreName: "workspaceblobstore",
                                    pathOnDataStore: "PipelineUserBlobPath",
                                    identifierHash: Guid.NewGuid().ToString(),
                                    contentHash: null);
            AetherEnvironment pipelineUserAetherEnvironment = await SetupPipelineUser();
            IAetherDataSource datasource = await pipelineUserAetherEnvironment.GetDataSourceAsync(datasourceId);
            Assert.AreEqual(datasource.Id, datasourceId);
        }
    }
}
