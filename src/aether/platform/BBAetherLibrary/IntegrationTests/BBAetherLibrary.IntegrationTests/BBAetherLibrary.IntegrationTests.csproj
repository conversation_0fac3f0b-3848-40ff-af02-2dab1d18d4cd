﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <OutputPath>$(BaseTargetDir)\app\aether\BBAetherLibrary\BBAetherLibrary.IntegrationTests</OutputPath>
    <GenerateBindingRedirectsOutputType>true</GenerateBindingRedirectsOutputType>
    <ImportSharedFiles>true</ImportSharedFiles>
  </PropertyGroup>
  <ItemGroup>
    <None Include="..\..\..\backendV2\BatchInferencing\**\*.*">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Content Include="..\shared\EnvironmentConfigs\aesvienna_helloworld.xml" Link="EnvironmentConfigs\aesvienna_helloworld.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="..\shared\EnvironmentConfigs\helloworld2.xml" Link="EnvironmentConfigs\helloworld2.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="..\shared\EnvironmentConfigs\hdipipelinestep_tests.xml" Link="EnvironmentConfigs\hdipipelinestep_tests.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="NUnit" />
    <PackageReference Include="NUnit3TestAdapter" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" />
    <PackageReference Include="coverlet.collector" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="$(AetherSrcRoot)\platform\BBAetherLibrary\Framework\BBAetherLibrary.Framework.csproj" />
    <ProjectReference Include="..\BBAetherLibrary.IntegrationTests.Common\BBAetherLibrary.IntegrationTests.Common.csproj" />
    <ProjectReference Include="..\..\Common\BBAetherLibrary.Common.csproj" />
  </ItemGroup>
</Project>
