﻿How to setup the test environment for ESCloud tests
====================================================

# Prepare Workspace
  Create a new workspace.
  We are using the following one for ESCloud tests:
  https://ml.azure.com/?wsid=/subscriptions/96aede12-2f73-41cb-b983-6d11a904839b/resourceGroups/rge2etests/providers/Microsoft.MachineLearningServices/workspaces/wse2etests&tid=72f988bf-86f1-41af-91ab-2d7cd011db47

# Create Blob Storage Container
  * Create a blob storage container in the workspace storage account.
  * Upload some dummy data to the container, including files to create FileDataset / TabularDataset.
  * Create a DataStore named "myblobdatastore" in the workspace.

# Create Datasets

## FileDataset
  * Go to workspace in https://ml.azure.com
  * Select "Data -> Data assets -> Create" to open create asset panel.
      Name: DummyFileDataset
      Type: File
  * In "Choose a source for your data asset" page, select "From Azure storage".
  * In "Select a datastore" page, select "myblobdatastore"..
  * Select a storage path.
  * Click "Create" to create the datastore.

## TabularDataset
  * Go to workspace in https://ml.azure.com
  * Select "Data -> Data assets -> Create" to open create asset panel.
      Name: DummyFileDataset
      Type: Tabular
  * In "Choose a source for your data asset" page, select "From Azure storage".
  * In "Select a datastore" page, select "myblobdatastore"..
  * Select a storage path containing tabular files.
  * Click "Create" to create the datastore.

## OpenDataset
  * Go to workspace in https://ml.azure.com
  * Select "Data -> Data assets -> Create" to open create asset panel.
      Name: OpenDataset_US_Population_by_County
      Type: Tabular
  * In "Choose a source for your data asset" page, select "From Azure Open Datasets".
  * In "Choose an Azure Open Dataset" page, select "US Population by County".
  * Click "Create" to create the datastore.

## Mnist Dataset
  * Upload the data located in TestResources/mnist_files.
  * Create file dataset using the files located in TestResources/EscloudFile/dataprovision/datasets/mnist/.

# Create Assets

## DummyFileAsset
  * Create a file located on blob storage. Can be any file.

## DummyMLModel
  * Select "Models -> Register -> From datastore" to select the "Register model from datastore" panel.
    * Model type: MLflow
    * Select datastore: workspaceblobstore

# Create Environment
  * Select "Environments -> Create" to open create environment panel.
    * Name: BasicNamedEnvironment
    * Select environment source: Create a new docker context
    * Click "Next" * n times, "Create"

# Create Compute
  Create a compute target named "cpucluster".
  * Select "Compute -> Compute clusters -> New" to open create compute panel.
  * Select spec and set compute name as "cpucluster".

# Create AksCompute
* Create an aks
  * Go to azure portal, select "Create Kubernetes cluster"
  * Create in a new resource group named "pipeline-aks-test-large-rg".
  * Cluster details:
    * Cluster preset configuration: Standard ($$)
    * Kubernetes cluster name: pipeline-aks-test-large
    * Region: East US 2
    * Node size: Select "Standard B4ms"
    * Scale method: Autoscale

* Switch to AML Pipelines R&D subscription.
```sh
   az account set --subscription 96aede12-2f73-41cb-b983-6d11a904839b

   az k8s-extension create --name aml-ext --extension-type Microsoft.AzureML.Kubernetes --config enableTraining=True enableInference=True inferenceRouterServiceType=LoadBalancer allowInsecureConnections=True inferenceLoadBalancerHA=False --cluster-type managedClusters --cluster-name pipeline-aks-test-large --resource-group pipeline-aks-test-large-rg --scope cluster
   az k8s-extension show --name aml-ext --cluster-type managedClusters  --cluster-name pipeline-aks-test-large --resource-group pipeline-aks-test-large-rg
```

* Create instance types
  * Create a file named "my_instance_type_list_large.yaml" with the following content:

```yaml
apiVersion: amlarc.azureml.com/v1alpha1
kind: InstanceTypeList
items:
  - metadata:
      name: defaultinstancetype
    spec:
      resources:
        requests:
          cpu: "1"
          memory: "4Gi" 
        limits:
          cpu: "1"
          nvidia.com/gpu: 0
          memory: "4Gi"
  - metadata:
      name: cpularge
    spec:
      resources:
        requests:
          cpu: "1"
          memory: "5Gi" 
        limits:
          cpu: "1"
          nvidia.com/gpu: 0
          memory: "5Gi"
```

  * Run this command:
```sh
    kubectl apply -f my_instance_type_list_large.yaml 
```
  
* Attach compute
  * On azureml portal, select "Compute -> Kubernetes clusters -> New -> Kubernetes"
	 *  Compute name: test-aks-large
	 *  Kubernetes cluster: pipeline-aks-test-large
	 *  Kubernetes namespace: azureml


# Create Component
## Create Component without Snapshot
The non-snapshot component is only supported on private version CLIs.
 * Use the following code to install a private version CLI:
```sh
    az extension remove -n ml
    az extension add --source https://azuremlsdktestpypi.blob.core.windows.net/wheels/sdk-cli-v2/ml-0.0.88484095-py3-none-any.whl
```
 * Create a component yaml file with the following content:
```yaml
$schema: https://azuremlschemas.azureedge.net/latest/commandComponent.schema.json
type: command

name: component_with_no_snapshot
display_name: Component with no snapshot
version: 2

environment: 
  image: python

command: >-
  echo "hello"
```

 * Register the component with the following command:
```sh
 az ml component create --file component_with_no_snapshot.yaml
```

 * Go to azureml portal, navigate to the component just registered, and copy the id.


## Create component for hyperdrive tests usage
 * Create component using the spec file located in TestResources/EscloudFile/dataprovision/components/microsoft_com_azureml_samples_sweep_minist_train/mnist.yaml
 * Create component using the spec file located in TestResources/EscloudFile/dataprovision/components/microsoft_com_azureml_samples_sweep_minist_train_asset/mnist.yaml

## Create "Output to file" component
 * Create component using the spec file located in TestResources/EscloudFile/dataprovision/components/output-file-component/spec.yaml
