﻿using BBAetherLibrary.Common;
using BBAetherLibrary.DataProvision;
using BBAetherLibrary.IntegrationTests.Client;
using BBAetherLibrary.IntegrationTests.Common;
using Microsoft.Aether.AEVA.DataContracts;
using Microsoft.Aether.BlueBox.Library;
using Microsoft.Aether.TokenProvider;
using Microsoft.Extensions.Logging;
using Microsoft.MachineLearning.Common.Core;
using Microsoft.MachineLearning.Dataset.Contracts;
using Microsoft.MachineLearning.EnvironmentManagement.Contracts;
using Microsoft.RelInfra.Common;
using Microsoft.RelInfra.Common.Serialization;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using static Microsoft.MachineLearning.EnvironmentManagement.Contracts.EnvironmentDefinition;
using ExecutionContracts = Microsoft.MachineLearning.Execution.Contracts;
using PipelineRunExecutionPhaseInfos = Microsoft.Aether.DataContracts.PipelineRunExecutionPhaseInfos;

namespace BBAetherLibrary.ESCloud.IntegrationTests
{
    [TestFixture]
    [Parallelizable(ParallelScope.All)]
    public class ESTests : BBAetherTestFixtureBase
    {

        private const string sourcePath = "TestResources/EscloudFile/helloworld";
        private const string amlComputeType = "AmlCompute";
        private readonly Script fileScript = new Script("file_dataset_validation.py");
        private readonly Script dfdScript = new Script("dfd.py");
        private readonly Script tabularScript = new Script("tabular_dataset_validation.py", true);
        private readonly Script datasetInputOutputScript = new Script("dataset_input_output.py");
        private readonly Script assetInputOutputScript = new Script("asset_input_output.py");

        private const string dataReferenceName = "the_only_input_data";
        private const string moduleInputName = "input";
        private const string moduleOutputName = "output";
        private const string datastoreName = "myblobdatastore";
        private const string exePoolCommandModuleWindowsRunConfig = "{\n  \"Command\": \"CheckFileExistsTest.exe { { {param.DataFolderPath}\\\\DataFileRaw.txt } }\",\n  \"UseAbsolutePath\": false,\n  \"Arguments\": [],\n  \"Framework\": \"Python\",\n  \"Communicator\": \"None\",\n  \"DataReferences\": {},\n  \"Data\": {},\n  \"OutputData\": {},\n  \"CredentialPassthrough\": false,\n  \"Environment\": {\n    \"Python\": {\n      \"UserManagedDependencies\": false,\n      \"CondaDependencies\": {\n        \"name\": \"project_environment\",\n        \"channels\": [\n          \"defaults\"\n        ],\n        \"dependencies\": [\n          \"python=3.6.8\",\n          {\n            \"pip\": [\n              \"azureml-defaults\"\n            ]\n          }\n        ]\n      }\n    },\n    \"Docker\": {\n      \"BaseImage\": \"mcr.microsoft.com/azureml/openmpi4.1.0-ubuntu22.04\",\n      \"Platform\": {\n        \"Os\": \"Windows\",\n        \"Architecture\": \"amd64\"\n      },\n      \"BaseImageRegistry\": {},\n      \"Enabled\": true\n    },\n    \"Spark\": {\n      \"Repositories\": [],\n      \"Packages\": [],\n      \"PrecachePackages\": true\n    }\n  },\n  \"History\": {\n    \"OutputCollection\": false,\n    \"DirectoriesToWatch\": null,\n    \"EnableMLflowTracking\": false\n  },\n  \"Spark\": {\n    \"Configuration\": {}\n  },\n  \"ParallelTask\": {\n    \"MaxRetriesPerWorker\": 0,\n    \"WorkerCountPerNode\": 1,\n    \"Configuration\": {}\n  },\n  \"BatchAi\": {\n    \"NodeCount\": 0\n  },\n  \"AmlCompute\": {\n    \"RetainCluster\": false\n  },\n  \"AISuperComputer\": {},\n  \"Tensorflow\": {\n    \"WorkerCount\": 0,\n    \"ParameterServerCount\": 0\n  },\n  \"Hdi\": {\n    \"YarnDeployMode\": \"None\"\n  },\n  \"ContainerInstance\": {\n    \"CpuCores\": 2.0,\n    \"MemoryGb\": 3.5\n  },\n  \"Docker\": {\n    \"UseDocker\": false,\n    \"SharedVolumes\": true\n  },\n  \"Cmk8sCompute\": {\n    \"Configuration\": {}\n  }\n}";
        private const string artifactOutputName = "artifactOutput";
        private const string serverlessTargetName = "serverless";
        private const string singularityTargetName = "/subscriptions/79a1ba0c-35bb-436b-bff2-3074d5ff1f89/resourceGroups/Runtime/providers/Microsoft.MachineLearningServices/virtualclusters/centeuapvc";

        private const int MinutesToWaitForPipelineToComplete = 25;
        private const int MinutesToWaitForPipelineWithWindowsComputeToComplete = 40;

        private const string EnvironmentNameWithoutVersion = "AzureML-Designer";
        private const string OriginalDockerImage = "mcr.microsoft.com/azureml/openmpi4.1.0-ubuntu22.04";
        private const string TestDockerImage = "mcr.microsoft.com/azureml/openmpi4.1.0-cuda11.8-cudnn8-ubuntu22.04";
        private const string TestCondaDependenciesString = @"
                            {
                                'name': 'project_environment',
                                'dependencies': [
                                'python = 3.9',
                                {
                                        'pip': [
                                        'azureml-defaults'
                                        ]
                                    }
                                    ]
                            }";

        private RunHistoryApiCaller _runHistoryApiCaller;
        private IndexServiceApiCaller _indexServiceApiCaller;

        private readonly TestFixtureApp _testFixtureApp = new();

        private DatasetDto? _tabularDataset;
        private DatasetDto? _fileDataset;
        private DatasetDto? _openDataset;
        private DatasetDto? _mnistDataset;
        private string? _uriFolderAsset = null;
        private string? _uriFileAsset = null;
        private string? _mlTableAsset = null;
        private string? _modelInputAsset = null;

        private readonly string _uriFileAssetInRegistry = "azureml://registries/azureml-dev/data/file1txt/versions/1";
        private readonly string _modelInputAssetInRegistry = "azureml://registries/azureml-dev/models/testprsmodel/versions/1";

        // The definition for "output-file Sample Component" component.
        // Master workspace:
        // https://ml.azure.com/module/output-file-component/name/output-file%20Sample%20Component/0.0.2?wsid=/subscriptions/b8c23406-f9b5-4ccb-8a65-a8cb5dcd6a5a/resourcegroups/rge2etests/workspaces/wse2etests&tid=72f988bf-86f1-41af-91ab-2d7cd011db47
        // Int workspace:
        // https://ml.azure.com/module/output-file-component/name/output-file%20Sample%20Component?wsid=/subscriptions/96aede12-2f73-41cb-b983-6d11a904839b/resourceGroups/rge2etests/providers/Microsoft.MachineLearningServices/workspaces/wse2etests&tid=72f988bf-86f1-41af-91ab-2d7cd011db47
        private readonly string OutputToFileComponentId = IsRunningInInt() ? "61e13f33-0c31-4286-b9cd-ae431a2cc76b" : "2e5063ef-fae4-48d2-8c34-66b30021dbe9";

        // The definition for "Sweep with script" component.
        // Master workspace:
        // https://ml.azure.com/module/microsoft_com_azureml_samples_sweep_minist_train/name/Sweep%20with%20script?wsid=/subscriptions/b8c23406-f9b5-4ccb-8a65-a8cb5dcd6a5a/resourcegroups/rge2etests/workspaces/wse2etests&tid=72f988bf-86f1-41af-91ab-2d7cd011db47
        // Int workspace:
        // https://ml.azure.com/module/microsoft_com_azureml_samples_sweep_minist_train/name/Sweep%20with%20script?wsid=/subscriptions/96aede12-2f73-41cb-b983-6d11a904839b/resourceGroups/rge2etests/providers/Microsoft.MachineLearningServices/workspaces/wse2etests&tid=72f988bf-86f1-41af-91ab-2d7cd011db47
        private readonly string _sweepWithScriptComponentId = IsRunningInInt() ? "31bc4101-4751-489a-b45a-1f0ae1c23819" : "894fc784-cf47-4e49-acba-1567198d85b3";

        // The definition for "Sweep with Asset" component.
        // Master workspace:
        // https://ml.azure.com/module/microsoft_com_azureml_samples_sweep_minist_train_asset/name/Sweep%20with%20Asset?wsid=/subscriptions/b8c23406-f9b5-4ccb-8a65-a8cb5dcd6a5a/resourcegroups/rge2etests/workspaces/wse2etests&tid=72f988bf-86f1-41af-91ab-2d7cd011db47
        // Int workspace:
        // https://ml.azure.com/module/microsoft_com_azureml_samples_sweep_minist_train_asset/name/Sweep%20with%20Asset?wsid=/subscriptions/96aede12-2f73-41cb-b983-6d11a904839b/resourceGroups/rge2etests/providers/Microsoft.MachineLearningServices/workspaces/wse2etests&tid=72f988bf-86f1-41af-91ab-2d7cd011db47
        private readonly string _sweepWithAssetComponentId = IsRunningInInt() ? "75b2a95f-9c41-4544-8be8-29aa594cc0c6" : "41668308-8898-4184-9185-90b43602a8d9";
        public ESTests() : base((ITokenProvider)null, null)
        {
            if (TestEnvironment.IsRunningInRunner())
            {
                _tokenProvider = _testFixtureApp.GetRequiredService<ProvisionedTokenProvider>();
                _testKeyVaultClient = null;
            }
            else if (IsRunningInCloudTestOrVsts())
            {
                _tokenProvider = new ManagedIdentityTokenProvider(AdalConstants.MIArmResource, PIPELINE_CI_IDENTITY_CLIENTID);
                _testKeyVaultClient = new ManagedIdentityKeyVaultClient(TEST_KEY_VAULT_NAME, PIPELINE_CI_IDENTITY_CLIENTID);
            }
            else
            {
                _tokenProvider = new InteractiveTokenProvider(AdalConstants.MicrosoftTenantId, AdalConstants.ArmResource);
                _testKeyVaultClient = new AzureTokenHelperKeyVaultClient(TEST_KEY_VAULT_NAME, new InteractiveTokenProvider(tenantId: AdalConstants.MicrosoftTenantId, audience: AdalConstants.KeyVaultResource));
            }
        }

        [OneTimeSetUp]
        public async Task Init()
        {
            _runHistoryApiCaller = new RunHistoryApiCaller(_testConfig.SubscriptionId, _testConfig.ResourceGroup, _testConfig.WorkspaceName, accessTokenProvider: _tokenProvider.GetAccessTokenAsync);
            _indexServiceApiCaller = new IndexServiceApiCaller(_testConfig.SubscriptionId, _testConfig.ResourceGroup, _testConfig.WorkspaceName, _tokenProvider.GetAccessTokenAsync);

            if (TestEnvironment.IsRunningInRunner())
            {
                await PrepareResources();
            }
            else
            {
                _tabularDataset = await GetDatasetAsync("DummyTabularDataset").ConfigureAwait(false);
                _fileDataset = await GetDatasetAsync("DummyFileDataset").ConfigureAwait(false);
                _openDataset = await GetDatasetAsync("OpenDataset_US_Population_by_County").ConfigureAwait(false);
                _mnistDataset = await GetDatasetAsync("mnist").ConfigureAwait(false);

                _uriFolderAsset = await GetSerializedAssetIdAsync("DummyUriFolder").ConfigureAwait(false);
                _uriFileAsset = await GetSerializedAssetIdAsync("DummyUriFile").ConfigureAwait(false);
                _mlTableAsset = await GetSerializedAssetIdAsync("DummyMLTable").ConfigureAwait(false);
                _modelInputAsset = IsRunningInInt()
                    ? "azureml://locations/centraluseuap/workspaces/4cb595ba-a971-49ea-b17f-fc1f049f599d/models/DummyMLModel/versions/3"
                    : "azureml://locations/centraluseuap/workspaces/3c80c156-6e4e-486d-90b3-3e3753ef4a7f/models/azureml_e2f8e3f9-078c-4caa-9a95-d18c9860c124_input_input/versions/1";
            }
        }

        private async Task PrepareResources()
        {
            var logger = _testFixtureApp.GetRequiredService<ILoggerFactory>().CreateLogger<ESTests>();
            logger.LogInformation("begin preparing resources");

            var datasetProvision = _testFixtureApp.GetRequiredService<DatasetProvision>();

            var blobClient = _testFixtureApp.GetRequiredService<BlobClient>();

            logger.LogInformation("UploadBlobs");
            await blobClient.UploadBlobs(new FilePayload[]
            {
                new("ESCloudTests/testdata.csv", new StringContent("Name\ntestdata")),
                new("ESCloudTests/testdata.txt", new StringContent("testdata")),
                new("ESCloudTests/mltable_file/MLTable", new ByteArrayContent(File.ReadAllBytes("TestResources/EscloudFile/helloworld/MLTable"))),
                new("ESCloudTests/MLModel", new ByteArrayContent(File.ReadAllBytes("TestResources/EscloudFile/helloworld/MLModel"))),
            });

            logger.LogInformation("getDataset TabularDataset");
            _tabularDataset = await datasetProvision.RegisterDataset("ESCloudTestTabularData", DatasetTypes.TabularDataset, "ESCloudTests/testdata.csv", FileType.GenericCsv);

            logger.LogInformation("getDataset FileDataset");
            _fileDataset = await datasetProvision.RegisterDataset("ESCloudTestData", DatasetTypes.FileDataset, "ESCloudTests/testdata.txt");

            logger.LogInformation("getDataAsset UriFolder");
            _uriFolderAsset = await datasetProvision.GetDataAssetUri("azureml_0b8c3130-dd39-48a2-ab73-c2e4aa72edc1_input_data_input", "ESCloudTests/", DataType.UriFolder);

            logger.LogInformation("getDataAsset UriFile");
            _uriFileAsset = await datasetProvision.GetDataAssetUri("azureml_e938484a-d437-4f22-aeb5-2e3173449425_input_data_input", "ESCloudTests/testdata.txt", DataType.UriFile);

            logger.LogInformation("getDataAsset MLTable");
            _mlTableAsset = await datasetProvision.GetDataAssetUri("azureml_e2f8e3f9-078c-4caa-9a95-d18c9860ca3a_input_data_input", "ESCloudTests/mltable_file/MLTable", DataType.MLTable);

            logger.LogInformation("getMLModel CustomModel");
            _modelInputAsset = await datasetProvision.PrepareModelAsset("e2f8e3f9-078c-4caa-9a95-d18c9860c124", Microsoft.MachineLearning.ModelRegistry.Contracts.Common.ModelType.CustomModel, "ESCloudTests/MLModel");

            logger.LogInformation($"PrepareBlobDatastore {datastoreName}");
            await datasetProvision.PrepareBlobDatastore(datastoreName, datastoreName);

            logger.LogInformation($"UploadBlob to {datastoreName}");
            await blobClient.UploadBlob("estestinput/log.txt", new StringContent("").ReadAsStream(), datastoreName);

            logger.LogInformation("preparing resources successfully finished.");
        }

        #region Basic Tests
        [Test]
        [Category("EnableRunner")]
        public async Task RunEsCloudTest()
        {
            string sourcePath = "TestResources/EscloudFile/helloworld";
            IGraph graph = await CreateEsCloudGraph(_testEnvironment, sourcePath);

            IPipelineRun pipelineRun = await RunTestWithLogsAsync(graph, _testEnvironment, MinutesToWaitForPipelineToComplete);
            Assert.AreEqual(PipelineRunStatusCode.Finished, pipelineRun.CachedStatus, $"PipelineRun id: {pipelineRun.Id}");
        }

        [Test]
        [Ignore("Skip due to the non-snapshot component is not supported in the public CLI/SDK")]
        public async Task RunEsCloudNoSnapshotTest()
        {
            var graph = _testEnvironment.CreateNewGraph();

            // The component is registered by the CLI before hand.
            // https://ml.azure.com/module/component_with_no_snapshot/name/Component%20with%20no%20snapshot?wsid=/subscriptions/96aede12-2f73-41cb-b983-6d11a904839b/resourceGroups/rge2etests/providers/Microsoft.MachineLearningServices/workspaces/wse2etests&tid=72f988bf-86f1-41af-91ab-2d7cd011db47#details
            var componentId = IsRunningInInt() ? "d79ced7b-dd40-4cb4-b573-555b58b53aec" : "d79ced7b-dd40-4cb4-b573-555b58b53aec";
            IAetherModule module = await _testEnvironment.GetModuleAsync(componentId);

            var runConfig = new Microsoft.MachineLearning.Execution.Contracts.RunConfiguration
            {
                Target = "cpucluster",
            };

            var cloudSettings = new CloudSettings
            {
                EsCloudConfig = new EsCloudConfiguration
                {
                    RunConfig = JsonConvert.SerializeObject(runConfig),
                }
            };
            graph.AddNode(module, cloudSettings: cloudSettings);

            IPipelineRun pipelineRun = await RunTestWithLogsAsync(graph, _testEnvironment, MinutesToWaitForPipelineToComplete);
            Assert.AreEqual(PipelineRunStatusCode.Finished, pipelineRun.CachedStatus, $"PipelineRun id: {pipelineRun.Id}");
        }

        [Test]
        public async Task RunEsCloudServerlessTest()
        {
            CloudSettings cloudSettings = new CloudSettings
            {
                EsCloudConfig = new EsCloudConfiguration
                {
                    ResourceConfig = new ResourceConfiguration
                    {
                        InstanceType = "Standard_D1_v2"
                    }
                }
            };
            var graph = _testEnvironment.CreateNewGraph();
            var moduleId = await UploadESCloudSimpleModuleUsingServerlessCompute(_testEnvironment, sourcePath);
            IAetherModule module = await _testEnvironment.GetModuleAsync(moduleId);
            graph.AddNode(module, cloudSettings: cloudSettings);

            IPipelineRun pipelineRun = await RunTestWithLogsAsync(graph, _testEnvironment, MinutesToWaitForPipelineToComplete);
            Assert.AreEqual(PipelineRunStatusCode.Finished, pipelineRun.CachedStatus, $"PipelineRun id: {pipelineRun.Id}");
        }

        private async Task<string> UploadESCloudSimpleModuleUsingServerlessCompute(AetherEnvironment environment, string sourcePath)
        {
            var structuredInterface = new StructuredInterface
            {
                Inputs = new List<StructuredInterfaceInput> { },
                Outputs = new List<StructuredInterfaceOutput> { },
                MetadataParameters = new List<StructuredInterfaceParameter>
                {
                    new StructuredInterfaceParameter { Name = "TargetType" , DefaultValue = "MLC", ParameterType = ParameterType.String, IsOptional = false},
                },
                Parameters = new List<StructuredInterfaceParameter>
                {
                    new StructuredInterfaceParameter { Name = "Target" , DefaultValue = serverlessTargetName, ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "MLCComputeType" , DefaultValue = amlComputeType, ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "PrepareEnvironment" , DefaultValue = "true", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "GenerateJson" , DefaultValue = "true", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "Script" , DefaultValue = "main.py", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "Framework" , DefaultValue = "Python", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "maxRunDurationSeconds" , DefaultValue = "1200", ParameterType = ParameterType.String, IsOptional = true},
                    new StructuredInterfaceParameter { Name = "InterpreterPath" , DefaultValue = "python", ParameterType = ParameterType.String, IsOptional = true},
                    new StructuredInterfaceParameter { Name = "UserManagedDependencies" , DefaultValue = "false", ParameterType = ParameterType.String, IsOptional = true},
                    new StructuredInterfaceParameter { Name = "CondaDependencies" , DefaultValue = GetCondaSetup(), ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "DockerEnabled" , DefaultValue = "true", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "BaseDockerImage" , DefaultValue = OriginalDockerImage, ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "SharedVolumes" , DefaultValue = "true", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "NodeCount" , DefaultValue = "1", ParameterType = ParameterType.String, IsOptional = true},
                    new StructuredInterfaceParameter { Name = "ContainerInstanceRegion" , DefaultValue = "None", ParameterType = ParameterType.String, IsOptional = true},
                    new StructuredInterfaceParameter { Name = "ContainerInstanceCpuCores" , DefaultValue = "1", ParameterType = ParameterType.String, IsOptional = true},
                    new StructuredInterfaceParameter { Name = "ContainerInstanceMemoryGb" , DefaultValue = "4", ParameterType = ParameterType.String, IsOptional = true},
                },
            };
            var moduleUploadInfo = new ModuleUploadInfo(
               name: "escloud serverless test module",
               displayName: "escloud serverless test module",
               description: "escloud serverless test module",
               isDeterministic: false,
               cloudSystem: "escloud",
               structuredInterface: structuredInterface,
               moduleTypeVersion: "1.0");

            return await environment.UploadModuleAsync(sourcePath, moduleUploadInfo);
        }

        [Test]
        public async Task RunEsCloudSingularityTest()
        {
            CloudSettings cloudSettings = new CloudSettings
            {
                EsCloudConfig = new EsCloudConfiguration
                {
                    ResourceConfig = new ResourceConfiguration
                    {
                        InstanceType = "Singularity.D4_v3",
                        InstancePriority = "Premium",
                    }
                },
                PriorityConfig = new PriorityConfiguration
                {
                    StringTypePriority = "low"
                }
            };

            // nodePriority > creationPriority > graphPriority
            // Expected priority: low
            var creationPriority = new CloudPrioritySetting
            {
                SingularityPriority = new PriorityConfiguration { StringTypePriority = "high" },
            };

            var graphPriority = new CloudPrioritySetting
            {
                SingularityPriority = new PriorityConfiguration { StringTypePriority = "medium" },
            };

            var graph = _testEnvironment.CreateNewGraph();
            var moduleId = await UploadESCloudSimpleModuleUsingSingularityCompute(_testEnvironment, sourcePath);
            IAetherModule module = await _testEnvironment.GetModuleAsync(moduleId);
            graph.AddNode(module, cloudSettings: cloudSettings);
            graph.AddDefaultCloudPriority(graphPriority);

            IPipelineRun pipelineRun = await RunTestWithLogsAsync(graph, _testEnvironment, MinutesToWaitForPipelineToComplete, defaultCloudPriority: creationPriority);
            Assert.AreEqual(PipelineRunStatusCode.Finished, pipelineRun.CachedStatus, $"PipelineRun id: {pipelineRun.Id}");
        }

        [Test]
        public async Task RunEsCloudSingularityUriFolderAssetTest()
        {
            var outputAssetPath = "azureml://datastores/myblobdatastore/paths/asset_output_folder/${{name}}/";

            IGraph graph = await CreateSingularityEsCloudGraphWithAssetInputOutput(
                _uriFolderAsset,
                AssetType.UriFolder,
                outputAssetPath,
                AssetType.UriFolder,
                _testEnvironment,
                singularityTargetName,
                script: assetInputOutputScript,
                datastoreMode: DataStoreMode.Mount);

            IPipelineRun pipelineRun = await RunTestWithLogsAsync(graph, _testEnvironment, MinutesToWaitForPipelineToComplete, runHistoryExperimentName: "AssetInputOutputWithUriFolder");
            Assert.AreEqual(PipelineRunStatusCode.Finished, pipelineRun.CachedStatus, $"PipelineRun id: {pipelineRun.Id}");
        }

        [Test]
        public async Task RunEsCloudSingularityUriFileAssetTest()
        {
            var outputAssetPath = "azureml://datastores/myblobdatastore/paths/asset_output_test/asset_output_file";

            IGraph graph = await CreateSingularityEsCloudGraphWithAssetInputOutput(
                _uriFileAsset,
                AssetType.UriFile,
                outputAssetPath,
                AssetType.UriFile,
                _testEnvironment,
                singularityTargetName,
                script: assetInputOutputScript,
                datastoreMode: DataStoreMode.Mount);

            IPipelineRun pipelineRun = await RunTestWithLogsAsync(graph, _testEnvironment, MinutesToWaitForPipelineToComplete, runHistoryExperimentName: "AssetInputOutputWithUriFile");
            Assert.AreEqual(PipelineRunStatusCode.Finished, pipelineRun.CachedStatus, $"PipelineRun id: {pipelineRun.Id}");
        }

        private async Task<IGraph> CreateSingularityEsCloudGraphWithAssetInputOutput(
            string inputAssetId,
            AssetType inputType,
            string outputAssetPath,
            AssetType outputType,
            AetherEnvironment environment,
            string computeTargetName,
            Script script,
            DataStoreMode datastoreMode = DataStoreMode.Mount,
            bool setDataStoreModeInAssetOutputSetting = false,
            bool setAssetOutputSettingAsParam = false,
            bool setAssetPathParam = false,
            bool withPipelineOutput = false,
            bool useGraphDefaultDatastore = false)
        {
            var graph = environment.CreateNewGraph();

            var datasetDefinition = new DataSetDefinition()
            {
                DataTypeShortName = ConvertToDataTypeId(inputType),
                Value = new DataSetDefinitionValue()
                {
                    AssetDefinition = new AssetDefinition
                    {
                        AssetId = AssetId.Parse(inputAssetId),
                        SerializedAssetId = inputAssetId,
                        Path = inputAssetId,
                        Type = inputType
                    }
                }
            };

            var inputSettings = new[]
            {
                new InputSetting
                {
                    Name = moduleInputName,
                    DataStoreMode = DataStoreMode.Mount,
                }
            };

            var parameterAssignment = new ParameterAssignment(name: "parameterAssignmentConcatenate", value: "", ParameterValueType.Concatenate)
            {
                AssignmentsToConcatenate = new[]
                    {
                        new ParameterAssignment(name: "parameterAssignmentGraphParameterName", value: "OutputAssetPath", ParameterValueType.GraphParameterName),
                        new ParameterAssignment(name: "parameterAssignmentLiteral", value: outputAssetPath.Substring(8), ParameterValueType.Literal)
                    }
            };

            var assetOutputSettings = new AssetOutputSettings
            {
                Path = setAssetPathParam ? "defaultPath" : outputAssetPath,
                Type = outputType,
                PathParameterAssignment = setAssetPathParam ? parameterAssignment : null
            };

            if (setDataStoreModeInAssetOutputSetting)
            {
                assetOutputSettings.DataStoreMode = DataStoreMode.Mount;
            }

            var outputSettings = new[] { new OutputSetting
            {
                AssetOutputSettingsParameterName = setAssetOutputSettingAsParam ? "assetOutputParam" : null,
                AssetOutputSettings = setAssetOutputSettingAsParam ? null : assetOutputSettings,
                Name = moduleOutputName,
                DataStoreMode = DataStoreMode.Mount,
                Overwrite = true
            } };

            IAetherDataSource datasource = await environment.GetDataSourceAsync(datasetDefinition);
            var cusEnv = GetAssetEnvironmentDict(outputType);
            string moduleId = await UploadESCloudSingularityNodeWithDataSetInput(
                environment,
                sourcePath,
                computeTargetName,
                script,
                datastoreMode,
                cusEnv,
                addAssetOutput: true,
                inputDataTypeId: ConvertToDataTypeId(inputType),
                outputDataTypeId: ConvertToDataTypeId(outputType),
                moduleDisplayName: "Singularity Asset Input/Output Validation",
                moduleName: "escloud asset test module");

            CloudSettings cloudSettings = new CloudSettings
            {
                EsCloudConfig = new EsCloudConfiguration
                {
                    ResourceConfig = new ResourceConfiguration
                    {
                        InstanceType = "Singularity.D4_v3",
                        InstancePriority = "Premium",
                    }
                }
            };

            IAetherModule module = await environment.GetModuleAsync(moduleId);
            IModuleNode moduleNode1 = graph.AddNode(module, moduleInputSettings: inputSettings, moduleOutputSettings: outputSettings, cloudSettings: cloudSettings);
            moduleNode1.SetDatastoreSetting(useGraphDefaultDatastore);

            SetArgumentsByInputAndOutput(moduleNode1);

            IDataSourceNode sourceNode = graph.AddNode(datasource);
            graph.Connect(sourceNode, moduleNode1.InputPortDictionary[moduleInputName]);

            if (useGraphDefaultDatastore)
            {
                var datastoreSetting = new DatastoreSetting()
                {
                    DataStoreName = "myblobdatastore"
                };
                graph.AddDefaultDatastore(datastoreSetting);
            }

            if (withPipelineOutput)
            {
                graph.AddGraphOutput("graphOutput");
                graph.GetGraphOutput("graphOutput").Connect(moduleNode1.OutputPortDictionary[moduleOutputName]);
            }

            return graph;
        }

        private IDictionary<string, string> GetAssetEnvironmentDict(AssetType outputType)
        {
            var modelTypes = new HashSet<AssetType>()
            {
                AssetType.MLFlowModel,
                AssetType.CustomModel,
                AssetType.TritonModel
            };

            return new Dictionary<string, string>
            {
                // These environment variables are used to control test script behavior which is for integration test purpose only
                { "IS_OUTPUT_FILE", outputType == AssetType.UriFile ? "true" : "false" },
                { "IS_OUTPUT_MLTABLE", outputType == AssetType.MLTable ? "true" : "false" },
                { "IS_OUTPUT_MLMODEL", modelTypes.Contains(outputType) ? "true" : "false" }
            };
        }

        private async Task<string> UploadESCloudSingularityNodeWithDataSetInput(
            AetherEnvironment environment,
            string sourcePath,
            string computeTargetName,
            Script script,
            DataStoreMode datastoreMode,
            IDictionary<string, string> env = null,
            bool addDatasetOutput = false,
            bool supportArtifact = false,
            bool addAssetOutput = false,
            string inputDataTypeId = "AzureBlobReference",
            string outputDataTypeId = "AzureBlobReference",
            string moduleDisplayName = "escloud dataset test module display",
            string moduleName = "escloud dataset test module")
        {
            var outputs = !addDatasetOutput ? null : new List<StructuredInterfaceOutput>
            {
                new StructuredInterfaceOutput
                {
                    Name = moduleOutputName,
                    DataTypeId = outputDataTypeId,
                    DataStoreName = datastoreName,
                    DataStoreMode = DataStoreMode.Mount,
                    Overwrite = true,
                    DataReferenceName = "out",
                    DatasetOutput = new DatasetOutput(),
                }
            };

            if (addAssetOutput)
            {
                outputs = new List<StructuredInterfaceOutput>
                {
                    new StructuredInterfaceOutput
                    {
                        Name = moduleOutputName,
                        DataTypeId = outputDataTypeId,
                        DataStoreName = datastoreName,
                        DataStoreMode = datastoreMode,
                        Overwrite = true,
                        DataReferenceName = "out",
                    }
                };
            }

            if (supportArtifact)
            {
                outputs ??= new List<StructuredInterfaceOutput>();
                outputs.Add(new StructuredInterfaceOutput
                {
                    Name = artifactOutputName,
                    DataTypeId = "uri_folder",
                    DataStoreName = datastoreName,
                    DataStoreMode = DataStoreMode.Mount,
                    Overwrite = true,
                    DataReferenceName = artifactOutputName,
                    DatasetOutput = new DatasetOutput(),
                    IsArtifact = true,
                });
            }

            var envVar = new Dictionary<string, string>()
            {
                {"JOB_EXECUTION_MODE", "basic"},
                {"AZUREML_COMPUTE_USE_COMMON_RUNTIME", "True"},
            };
            env?.ToList().ForEach(x => envVar[x.Key] = x.Value);
            var envString = string.Join(";", envVar.Select(x => x.Key + "=" + x.Value).ToArray());

            var structuredInterface = new StructuredInterface
            {
                Inputs = new List<StructuredInterfaceInput>
                {
                    new StructuredInterfaceInput { Name = moduleInputName, DataTypeIdsList = new List<string> { inputDataTypeId }, DataStoreMode = datastoreMode, DataReferenceName = dataReferenceName },
                },
                Outputs = outputs,
                MetadataParameters = new List<StructuredInterfaceParameter>
                {
                    new StructuredInterfaceParameter { Name = "TargetType" , DefaultValue = "Aisc", ParameterType = ParameterType.String, IsOptional = false},
                },
                Parameters = new List<StructuredInterfaceParameter>
                {
                    new StructuredInterfaceParameter { Name = "ShmSize" , DefaultValue = "2g", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "Framework" , DefaultValue = "PyTorch", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "Communicator" , DefaultValue = "nccl", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "MaxRunDurationSeconds" , DefaultValue = "3600", ParameterType = ParameterType.String, IsOptional = true},
                    new StructuredInterfaceParameter { Name = "InterpreterPath" , DefaultValue = "python", ParameterType = ParameterType.String, IsOptional = true},
                    new StructuredInterfaceParameter { Name = "CondaDependencies" , DefaultValue = TestCondaDependenciesString, ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "DockerEnabled" , DefaultValue = "false", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "SharedVolumes" , DefaultValue = "true", ParameterType = ParameterType.String, IsOptional = false},

                    new StructuredInterfaceParameter { Name = "Target" , DefaultValue = computeTargetName, ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "ImageVersion" , DefaultValue = "pytorch", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "PrepareEnvironment" , DefaultValue = "true", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "Script" , DefaultValue = script.Name, ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "Arguments" , DefaultValue = "--in,$AZUREML_DATAREFERENCE_input,--out,$AZUREML_DATAREFERENCE_out", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "NodeCount" , DefaultValue = "1", ParameterType = ParameterType.String, IsOptional = true},
                    new StructuredInterfaceParameter { Name = "EnvironmentVariables" , DefaultValue = envString, ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "UserManagedDependencies" , DefaultValue = "true", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "BaseDockerImage" , DefaultValue = "singularitybase.azurecr.io/base/job/deepspeed/0.4-pytorch-1.7.0-cuda11.0-cudnn8-devel:20221017T152225334", ParameterType = ParameterType.String, IsOptional = false},
                },
            };

            var moduleUploadInfo = new ModuleUploadInfo(
               name: moduleName,
               displayName: moduleDisplayName,
               description: "escloud dataset test module",
               isDeterministic: false,
               cloudSystem: "escloud",
               structuredInterface: structuredInterface,
               moduleTypeVersion: "1.0");

            return await environment.UploadModuleAsync(sourcePath, moduleUploadInfo);
        }

        private async Task<string> UploadESCloudSimpleModuleUsingSingularityCompute(AetherEnvironment environment, string sourcePath)
        {
            var structuredInterface = new StructuredInterface
            {
                Inputs = new List<StructuredInterfaceInput> { },
                Outputs = new List<StructuredInterfaceOutput> { },
                MetadataParameters = new List<StructuredInterfaceParameter>
                {
                    new StructuredInterfaceParameter { Name = "TargetType" , DefaultValue = "Aisc", ParameterType = ParameterType.String, IsOptional = false},
                },
                Parameters = new List<StructuredInterfaceParameter>
                {
                    new StructuredInterfaceParameter { Name = "ShmSize" , DefaultValue = "2g", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "Framework" , DefaultValue = "PyTorch", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "Communicator" , DefaultValue = "nccl", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "MaxRunDurationSeconds" , DefaultValue = "3600", ParameterType = ParameterType.String, IsOptional = true},
                    new StructuredInterfaceParameter { Name = "InterpreterPath" , DefaultValue = "python", ParameterType = ParameterType.String, IsOptional = true},
                    new StructuredInterfaceParameter { Name = "CondaDependencies" , DefaultValue = TestCondaDependenciesString, ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "DockerEnabled" , DefaultValue = "false", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "SharedVolumes" , DefaultValue = "true", ParameterType = ParameterType.String, IsOptional = false},

                    new StructuredInterfaceParameter { Name = "Target" , DefaultValue = singularityTargetName, ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "ImageVersion" , DefaultValue = "pytorch", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "PrepareEnvironment" , DefaultValue = "true", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "Command" , DefaultValue = "echo hello", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "NodeCount" , DefaultValue = "1", ParameterType = ParameterType.String, IsOptional = true},
                    new StructuredInterfaceParameter { Name = "EnvironmentVariables" , DefaultValue = "JOB_EXECUTION_MODE=basic;AZUREML_COMPUTE_USE_COMMON_RUNTIME=True;JOB_EXECUTION_MODE_BASIC_PRIVILEGED=true", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "UserManagedDependencies" , DefaultValue = "true", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "BaseDockerImage" , DefaultValue = "singularitybase.azurecr.io/base/job/deepspeed/0.4-pytorch-1.7.0-cuda11.0-cudnn8-devel:20221017T152225334", ParameterType = ParameterType.String, IsOptional = false},
                },
            };
            var moduleUploadInfo = new ModuleUploadInfo(
               name: "escloud singularity test module",
               displayName: "escloud singularity test module",
               description: "escloud singularity test module",
               isDeterministic: false,
               cloudSystem: "escloud",
               structuredInterface: structuredInterface,
               moduleTypeVersion: "1.0");

            return await environment.UploadModuleAsync(sourcePath, moduleUploadInfo);
        }

        [Test]
        public async Task RunEsCloudWithInitFinalPhasesTest()
        {
            string sourcePath = "TestResources/EscloudFile/helloworld";
            IGraph graph = await CreateEsCloudGraphWithInitFinalPhases(_testEnvironment, sourcePath);
            IPipelineRun pipelineRun = await RunTestWithLogsAsync(graph, _testEnvironment, MinutesToWaitForPipelineToComplete);
            Assert.AreEqual(PipelineRunStatusCode.Finished, pipelineRun.CachedStatus, $"PipelineRun id: {pipelineRun.Id}");

            // Get execution phase info from pipeline run's properties.
            var pipelineRunDetails = await _runHistoryApiCaller.GetRunDetails(pipelineRun.RunHistoryExperimentName, pipelineRun.Id).ConfigureAwait(false);
            Assert.IsTrue(pipelineRunDetails.Properties.TryGetValue("azureml.pipelines.stages", out string serializedStageInfo));
            var phaseInfos = JsonConvert.DeserializeObject<PipelineRunExecutionPhaseInfos>(serializedStageInfo);

            // Make sure execution phase info is correctly set.
            Assert.IsNotNull(phaseInfos.Initialization);
            Assert.IsTrue(phaseInfos.Initialization.EndTime > phaseInfos.Initialization.StartTime);
            Assert.AreEqual(Microsoft.Aether.DataContracts.TaskStatusCode.Finished, phaseInfos.Initialization.Status);

            Assert.IsNotNull(phaseInfos.Execution);
            Assert.IsTrue(phaseInfos.Execution.EndTime > phaseInfos.Execution.StartTime);
            Assert.AreEqual(Microsoft.Aether.DataContracts.TaskStatusCode.Finished, phaseInfos.Execution.Status);

            Assert.IsTrue(phaseInfos.Execution.StartTime == phaseInfos.Initialization.EndTime);
        }

        [Test]
        public async Task RunEsCloudWithGuidPipelineRunIdTest()
        {
            string sourcePath = "TestResources/EscloudFile/helloworld";
            IGraph graph = await CreateEsCloudGraph(_testEnvironment, sourcePath);

            IPipelineRun pipelineRun = await RunTestWithLogsAsync(graph, _testEnvironment, MinutesToWaitForPipelineToComplete, runIdPrefix: "");
            Assert.IsTrue(Guid.TryParse(pipelineRun.Id, out _));
            Assert.AreEqual(PipelineRunStatusCode.Finished, pipelineRun.CachedStatus, $"PipelineRun id: {pipelineRun.Id}");

            await AssertExperimentStoredInMetastore(pipelineRun.Id);
        }

        [Test]
        public async Task RunEsCloudWithNonGuidPipelineRunIdTest()
        {
            string sourcePath = "TestResources/EscloudFile/helloworld";
            IGraph graph = await CreateEsCloudGraph(_testEnvironment, sourcePath);

            IPipelineRun pipelineRun = await RunTestWithLogsAsync(graph, _testEnvironment, MinutesToWaitForPipelineToComplete);
            Assert.IsFalse(Guid.TryParse(pipelineRun.Id, out _));
            Assert.AreEqual(PipelineRunStatusCode.Finished, pipelineRun.CachedStatus, $"PipelineRun id: {pipelineRun.Id}");

            await AssertExperimentStoredInMetastore(pipelineRun.Id);
        }

        /// <summary>
        /// Ideally we want to do assert operation for ExperimentEntity stored in metastore, and check ExperimentEntity has correct Id and PipelineRunId.
        /// But there is not any API in ae3p could get ExperimentEntity.
        /// So we use GetPipelineRunAsync instead, this API's controller will get ExperimentEntity from metastore, and convert ExperimentEntity to PipelineRunEntity.
        /// And this API's client will convert PipelineRunEntity to PipelineRun.
        /// So if we could get PipelineRun by GetPipelineRunAsync and Id equals to PipelineRunId, then we could guarantee there exists one ExperimentEntity with same pipelineRunId in metastore.
        /// </summary>
        private async Task AssertExperimentStoredInMetastore(string pipelineRunId)
        {
            var pipelineRun = await _testEnvironment.GetPipelineRunAsync(pipelineRunId);
            Assert.AreEqual(pipelineRunId, pipelineRun.Id);
        }

        [Test]
        public async Task RunEsCloudDirectoryDataTypeTest()
        {
            string sourcePath = "TestResources/EscloudFile/helloworld";
            IGraph graph = await CreateEsCloudGraphForDataTypeTest(_testEnvironment, sourcePath);

            IPipelineRun pipelineRun = await RunTestWithLogsAsync(graph, _testEnvironment, MinutesToWaitForPipelineToComplete);
            Assert.AreEqual(PipelineRunStatusCode.Finished, pipelineRun.CachedStatus, $"PipelineRun id: {pipelineRun.Id}");
        }

        [Test]
        [Category("SkipRunnerTest")]
        [Ignore("Disable due to can't assign identity in INT env")]
        public async Task RunEsCloudWithAdlsGen2()
        {
            var sourceId = await DataStoreUtils.CreateDataStoreDataSourceAsync(
                _testEnvironment,
                name: "AdlsGen2",
                description: "AdlsGen2",
                amlDataStoreName: _testConfig.AdlsGen2DatastoreName,
                pathOnDataStore: "datainput/test",
                identifierHash: Guid.NewGuid().ToString(),
                contentHash: null).ConfigureAwait(false);

            var dtcGraph = _testEnvironment.CreateNewGraph();
            string moduleId = await UploadESCloudSimpleModuleWithInput(_testEnvironment, sourcePath, false, inputType: "AzureDataLakeGen2Reference");

            IAetherModule module = await _testEnvironment.GetModuleAsync(moduleId);
            IModuleNode moduleNode1 = dtcGraph.AddNode(module);

            IAetherDataSource source = await _testEnvironment.GetDataSourceAsync(sourceId);
            IDataSourceNode sourceNode = dtcGraph.AddNode(source);
            dtcGraph.Connect(sourceNode, moduleNode1.InputPortDictionary["SourceLocation"]);
            IPipelineRun pipelineRun = await RunTestWithLogsAsync(dtcGraph, _testEnvironment, MinutesToWaitForPipelineToComplete);
            Assert.AreEqual(PipelineRunStatusCode.Finished, pipelineRun.CachedStatus, $"PipelineRun id: {pipelineRun.Id}");
        }

        [Test]
        public async Task RunEsCloudTestWithNodeDataStoreSettings()
        {
            string sourcePath = "TestResources/EscloudFile/helloworld";
            IGraph graph = await CreateEsCloudGraphWithNodeDataStoreSettings(_testEnvironment, sourcePath);

            IPipelineRun pipelineRun = await RunTestWithLogsAsync(graph, _testEnvironment, MinutesToWaitForPipelineToComplete);
            Assert.AreEqual(PipelineRunStatusCode.Finished, pipelineRun.CachedStatus, $"experiment id: {pipelineRun.Id}");
        }

        [Test]
        [Category("EnableRunner")]
        public async Task RunEsCloudTabularDatasetTestOnBatchAI()
        {
            IGraph graph = await CreateEsCloudGraphWithDataSetNode(_testEnvironment, sourcePath, _tabularDataset, _testConfig.AmlComputeName, amlComputeType, tabularScript, true, datastoreMode: DataStoreMode.Direct);
            IPipelineRun pipelineRun = await RunTestWithLogsAsync(graph, _testEnvironment, MinutesToWaitForPipelineToComplete);
            Assert.AreEqual(PipelineRunStatusCode.Finished, pipelineRun.CachedStatus, $"PipelineRun id: {pipelineRun.Id}");
        }

        [Test]
        [Category("EnableRunner")]
        public async Task RunEsCloudFileDatasetTestOnBatchAI()
        {
            IGraph graph = await CreateEsCloudGraphWithDataSetNode(_testEnvironment, sourcePath, _fileDataset, _testConfig.AmlComputeName, amlComputeType, fileScript, false);
            IPipelineRun pipelineRun = await RunTestWithLogsAsync(graph, _testEnvironment, MinutesToWaitForPipelineToComplete);
            Assert.AreEqual(PipelineRunStatusCode.Finished, pipelineRun.CachedStatus, $"PipelineRun id: {pipelineRun.Id}");
        }

        [Test]
        [Category("SkipRunnerTest")]
        public async Task RunEsCloudFileDatasetTestOnKubernetes()
        {
            CloudSettings cloudSettings = new CloudSettings
            {
                EsCloudConfig = new EsCloudConfiguration
                {
                    ResourceConfig = new ResourceConfiguration
                    {
                        InstanceType = "cpularge"
                    }
                }
            };
            IGraph graph = await CreateEsCloudGraphWithDataSetNode(_testEnvironment, sourcePath, _fileDataset, "test-aks-large", "kubernetes", fileScript, false, cloudSettings: cloudSettings);
            IPipelineRun pipelineRun = await RunTestWithLogsAsync(graph, _testEnvironment, MinutesToWaitForPipelineToComplete);
            Assert.AreEqual(PipelineRunStatusCode.Finished, pipelineRun.CachedStatus, $"PipelineRun id: {pipelineRun.Id}");
        }

        [Test]
        [Category("SkipRunnerTest")]
        public async Task RunEsCloudTabularDatasetWithToDataFrameDirectoryTestOnBatchAI()
        {
            var additionalTransformation = "{\"blocks\":[{\"id\":\"84ca3d3a-63ba-45fc-92b4-8a0a98fd86dd\",\"type\":\"Microsoft.DPrep.ToDataFrameDirectoryBlock\",\"arguments\":{\"error\":\"ERROR\",\"rowsPerGroup\": 5000},\"localData\":{},\"isEnabled\":true,\"name\":null,\"annotation\":null}],\"inspectors\":[],\"meta\":{}}";
            var inputSetting = new InputSetting
            {
                Name = moduleInputName,
                AdditionalTransformations = additionalTransformation
            };
            IGraph graph = await CreateEsCloudGraphWithDataSetNode(_testEnvironment, sourcePath, _openDataset, _testConfig.AmlComputeName, amlComputeType, dfdScript, inputSetting: inputSetting);
            IPipelineRun pipelineRun = await RunTestWithLogsAsync(graph, _testEnvironment, MinutesToWaitForPipelineToComplete);
            Assert.AreEqual(PipelineRunStatusCode.Finished, pipelineRun.CachedStatus, $"PipelineRun id: {pipelineRun.Id}");
        }

        [Test]
        public async Task RunEsCloudTabularDatasetWithToParquetFilesTestOnBatchAI()
        {
            var additionalTransformation = "{\"blocks\":[{\"id\":\"84ca3d3a-63ba-45fc-92b4-8a0a98fd86dd\",\"type\":\"Microsoft.DPrep.ToParquetStreamsBlock\",\"arguments\":{\"error\":\"ERROR\",\"rowsPerGroup\": 5000},\"localData\":{},\"isEnabled\":true,\"name\":null,\"annotation\":null}],\"inspectors\":[],\"meta\":{}}";
            var inputSetting = new InputSetting
            {
                Name = moduleInputName,
                AdditionalTransformations = additionalTransformation
            };
            IGraph graph = await CreateEsCloudGraphWithDataSetNode(
                _testEnvironment,
                sourcePath,
                _tabularDataset,
                _testConfig.AmlComputeName,
                amlComputeType,
                fileScript,
                false,
                inputSetting: inputSetting);
            IPipelineRun pipelineRun = await RunTestWithLogsAsync(graph, _testEnvironment, MinutesToWaitForPipelineToComplete);
            Assert.AreEqual(PipelineRunStatusCode.Finished, pipelineRun.CachedStatus, $"PipelineRun id: {pipelineRun.Id}");
        }

        [Test]
        public async Task RunEsCloudFileDatasetWithParseDelimitedFilesTestOnBatchAI()
        {
            var additionalTransformation = "{\"blocks\":[{\"id\":\"9fe4ab79-cbab-4a37-a05b-b8bfb9859d39\",\"type\":\"Microsoft.DPrep.DropColumnsBlock\",\"arguments\":{\"columns\":{\"type\":0,\"details\":{\"selectedColumns\":[\"color\"]}}},\"localData\":{},\"isEnabled\":true,\"name\":null,\"annotation\":null}],\"inspectors\":[],\"meta\":{\"steps_added\":\"2\"}}";
            var inputSetting = new InputSetting
            {
                Name = moduleInputName,
                AdditionalTransformations = additionalTransformation
            };
            IGraph graph = await CreateEsCloudGraphWithDataSetNode(
                _testEnvironment,
                sourcePath,
                _tabularDataset,
                _testConfig.AmlComputeName,
                amlComputeType,
                tabularScript,
                true,
                datastoreMode: DataStoreMode.Direct,
                inputSetting: inputSetting);
            IPipelineRun pipelineRun = await RunTestWithLogsAsync(graph, _testEnvironment, MinutesToWaitForPipelineToComplete);
            Assert.AreEqual(PipelineRunStatusCode.Finished, pipelineRun.CachedStatus, $"PipelineRun id: {pipelineRun.Id}");
        }

        [Test]
        public async Task RunEsCloudDatasetInputOutputOnBatchAI()
        {
            IGraph graph = await CreateEsCloudGraphWithDatasetInputOutput(
                _testEnvironment,
                sourcePath,
                _fileDataset,
                _testConfig.AmlComputeName,
                amlComputeType,
                datasetInputOutputScript,
                false);
            IPipelineRun pipelineRun = await RunTestWithLogsAsync(graph, _testEnvironment, MinutesToWaitForPipelineToComplete);
            Assert.AreEqual(PipelineRunStatusCode.Finished, pipelineRun.CachedStatus, $"PipelineRun id: {pipelineRun.Id}");
        }

        [Test]
        [Category("EnableRunner")]
        public async Task RunEsCloudDatasetInputNewOutputOnBatchAI()
        {
            IGraph graph = await CreateEsCloudGraphWithDatasetInputOutput(
                _testEnvironment,
                sourcePath,
                _fileDataset,
                _testConfig.AmlComputeName,
                amlComputeType,
                datasetInputOutputScript,
                false,
                datasetOutputOptions: new DatasetOutputOptions());
            IPipelineRun pipelineRun = await RunTestWithLogsAsync(graph, _testEnvironment, MinutesToWaitForPipelineToComplete);
            Assert.AreEqual(PipelineRunStatusCode.Finished, pipelineRun.CachedStatus, $"PipelineRun id: {pipelineRun.Id}");
        }

        [Test]
        public async Task RunEsCloudOutputSettingOverwrittenByParameterAssignmentOnBatchAI()
        {
            var graph = _testEnvironment.CreateNewGraph();

            var datasetDefinition = new DataSetDefinition()
            {
                DataTypeShortName = "AzureBlobReference",
                Value = new DataSetDefinitionValue()
                {
                    DataSetReference = new RegisteredDataSetReference()
                    {
                        Id = _fileDataset.DatasetId.ToString(),
                    }
                }
            };

            var datasetOutputOptions = new DatasetOutputOptions
            {
                PathOnDatastore = "defaultPath",
                PathOnDatastoreParameterAssignment = new ParameterAssignment(name: "fakeName", value: "fakeValue", ParameterValueType.Concatenate)
                {
                    AssignmentsToConcatenate = new[] {
                            new ParameterAssignment(name: "fakePathOnDatastore", value: "PathOnDatastore", ParameterValueType.GraphParameterName),
                            new ParameterAssignment(name: "fakeName", value: "SecondPartPath"),
                        }
                }
            };
            var outputSettings = new[] {
                new OutputSetting
                {
                    DataReferenceName = "out",
                    DatasetRegistration = new DatasetRegistration(),
                    DataStoreMode = DataStoreMode.Hdfs,
                    DataStoreModeParameterAssignment = new ParameterAssignment(name: "fakeDataStoreMode", value: "DataStoreMode", ParameterValueType.GraphParameterName),
                    DataStoreName = "testDataStoreName",
                    DataStoreNameParameterAssignment = new ParameterAssignment(name: "fakeDataStoreName", value: "DataStoreName", ParameterValueType.GraphParameterName),
                    PathOnCompute = "testPathOnCompute",
                    PathOnComputeParameterAssignment = new ParameterAssignment(name: "fakePathOnCompute", value: "PathOnCompute", ParameterValueType.GraphParameterName),
                    Name = moduleOutputName,
                    Overwrite = true,
                    DatasetOutputOptions = datasetOutputOptions
                }
            };

            IAetherDataSource datasource = await _testEnvironment.GetDataSourceAsync(datasetDefinition);
            string moduleId = await UploadESCloudNodeWithDataSetInput(
                _testEnvironment,
                sourcePath,
                _testConfig.AmlComputeName,
                amlComputeType,
                datasetInputOutputScript,
                false,
                DataStoreMode.Mount,
                addDatasetOutput: true);

            IAetherModule module = await _testEnvironment.GetModuleAsync(moduleId);
            IModuleNode moduleNode1 = graph.AddNode(module, moduleOutputSettings: outputSettings);

            IDataSourceNode sourceNode = graph.AddNode(datasource);
            graph.Connect(sourceNode, moduleNode1.InputPortDictionary[moduleInputName]);

            IModuleNode moduleNode2 = graph.AddNode(module, moduleOutputSettings: outputSettings);
            graph.Connect(moduleNode1.OutputPortDictionary[moduleOutputName], moduleNode2.InputPortDictionary[moduleInputName]);

            graph.AddParameter("PathOnDatastore", false, "FirstPartPath/");
            graph.AddParameter("DataStoreMode", false, "Mount");
            graph.AddParameter("DataStoreName", false, datastoreName);
            graph.AddParameter("PathOnCompute", false, string.Empty);

            IPipelineRun pipelineRun = await RunTestWithLogsAsync(graph, _testEnvironment, MinutesToWaitForPipelineToComplete);
            Assert.AreEqual(PipelineRunStatusCode.Finished, pipelineRun.CachedStatus, $"PipelineRun id: {pipelineRun.Id}");
        }

        [Test]
        [Category("EnableRunner")]
        public async Task RunEsCloudAssetInputOutputWithUriFolderOnBatchAI()
        {
            var inputAssetId = _uriFolderAsset;
            var outputAssetPath = "azureml://datastores/myblobdatastore/paths/asset_output_folder/${{name}}/";

            (IGraph graph, IDictionary<string, AssetOutputSettings> _) = await CreateEsCloudGraphWithAssetInputOutput(
                inputAssetId,
                AssetType.UriFolder,
                outputAssetPath,
                AssetType.UriFolder,
                _testEnvironment,
                _testConfig.AmlComputeName,
                amlComputeType,
                script: assetInputOutputScript,
                dockerEnabled: true,
                datastoreMode: DataStoreMode.Mount);

            IPipelineRun pipelineRun = await RunTestWithLogsAsync(graph, _testEnvironment, MinutesToWaitForPipelineToComplete, runHistoryExperimentName: "AssetInputOutputWithUriFolder");
            Assert.AreEqual(PipelineRunStatusCode.Finished, pipelineRun.CachedStatus, $"PipelineRun id: {pipelineRun.Id}");
        }

        [Test]
        [Category("EnableRunner")]
        public async Task RunEsCloudAssetInputOutputWithUriFileOnBatchAI()
        {
            await RunEsCloudAssetInputOutputWithUriFileOnBatchAI_Inner(_uriFileAsset);
        }

        [Test]
        [Category("EnableRunner")]
        public async Task RunEsCloudAssetInputOutputWithUriFileInRegistryOnBatchAI()
        {
            await RunEsCloudAssetInputOutputWithUriFileOnBatchAI_Inner(_uriFileAssetInRegistry);
        }

        private async Task RunEsCloudAssetInputOutputWithUriFileOnBatchAI_Inner(string inputAssetId)
        {
            var outputAssetPath = "azureml://datastores/myblobdatastore/paths/asset_output_test/asset_output_file";

            (IGraph graph, IDictionary<string, AssetOutputSettings> _) = await CreateEsCloudGraphWithAssetInputOutput(
                inputAssetId,
                AssetType.UriFile,
                outputAssetPath,
                AssetType.UriFile,
                _testEnvironment,
                _testConfig.AmlComputeName,
                amlComputeType,
                script: assetInputOutputScript,
                dockerEnabled: true,
                datastoreMode: DataStoreMode.Mount);

            IPipelineRun pipelineRun = await RunTestWithLogsAsync(graph, _testEnvironment, MinutesToWaitForPipelineToComplete, runHistoryExperimentName: "AssetInputOutputWithUriFile");
            Assert.AreEqual(PipelineRunStatusCode.Finished, pipelineRun.CachedStatus, $"PipelineRun id: {pipelineRun.Id}");
        }

        [Test]
        [Category("EnableRunner")]
        public async Task RunEsCloudAssetInputOutputWithUriFileOnBatchAI_DefaultDatastoreName()
        {
            var inputAssetId = _uriFileAsset;
            var outputAssetPath = "azureml://datastores/${{default_datastore}}/paths/asset_output_test/asset_output_file";

            (IGraph graph, IDictionary<string, AssetOutputSettings> _) = await CreateEsCloudGraphWithAssetInputOutput(
                inputAssetId,
                AssetType.UriFile,
                outputAssetPath,
                AssetType.UriFile,
                _testEnvironment,
                _testConfig.AmlComputeName,
                amlComputeType,
                script: assetInputOutputScript,
                dockerEnabled: true,
                datastoreMode: DataStoreMode.Mount,
                useGraphDefaultDatastore: true);

            IPipelineRun pipelineRun = await RunTestWithLogsAsync(graph, _testEnvironment, MinutesToWaitForPipelineToComplete, runHistoryExperimentName: "AssetInputOutputWithUriFile");
            Assert.AreEqual(PipelineRunStatusCode.Finished, pipelineRun.CachedStatus, $"PipelineRun id: {pipelineRun.Id}");
        }

        [Test]
        [Category("EnableRunner")]
        public async Task RunEsCloudAssetInputOutputWithUriFileOnBatchAI_OutputSettingOverwrittenByParameterAssignment()
        {
            var inputAssetId = _uriFileAsset;
            var outputAssetPath = "azureml://datastores/myblobdatastore/paths/asset_output_test/asset_output_file";

            (IGraph graph, IDictionary<string, AssetOutputSettings> _) = await CreateEsCloudGraphWithAssetInputOutput(
                inputAssetId,
                AssetType.UriFile,
                outputAssetPath,
                AssetType.UriFile,
                _testEnvironment,
                _testConfig.AmlComputeName,
                amlComputeType,
                script: assetInputOutputScript,
                dockerEnabled: true,
                datastoreMode: DataStoreMode.Mount,
                setAssetPathParam: true);

            var parameterAssignment = new Dictionary<string, string>() { { "OutputAssetPath", outputAssetPath.Substring(0, 8) } };

            IPipelineRun pipelineRun = await RunTestWithLogsAsync(graph, _testEnvironment, MinutesToWaitForPipelineToComplete, parameterAssignment: parameterAssignment, runHistoryExperimentName: "AssetInputOutputWithUriFile");
            Assert.AreEqual(PipelineRunStatusCode.Finished, pipelineRun.CachedStatus, $"PipelineRun id: {pipelineRun.Id}");
        }

        [Test]
        [Category("EnableRunner")]
        [TestCase(false, false)]
        [TestCase(true, false)]
        [TestCase(true, true)]
        public async Task RunEsCloudAssetInputOutputWithMlTableOnBatchAI(bool setDataStoreModeInAssetOutputSetting = false, bool setAssetOutputSettingAsParam = false)
        {
            var inputAssetId = _mlTableAsset;
            var outputAssetPath = "azureml://datastores/myblobdatastore/paths/mltable_output_test/";
            (IGraph graph, IDictionary<string, AssetOutputSettings> assetOutputSettingsAssignments) = await CreateEsCloudGraphWithAssetInputOutput(
                inputAssetId,
                AssetType.MLTable,
                outputAssetPath,
                AssetType.MLTable,
                _testEnvironment,
                _testConfig.AmlComputeName,
                amlComputeType,
                script: assetInputOutputScript,
                dockerEnabled: true,
                datastoreMode: DataStoreMode.Mount,
                setDataStoreModeInAssetOutputSetting: setDataStoreModeInAssetOutputSetting,
                setAssetOutputSettingAsParam: setAssetOutputSettingAsParam);

            IPipelineRun pipelineRun = await RunTestWithLogsAsync(graph, _testEnvironment, MinutesToWaitForPipelineToComplete, runHistoryExperimentName: "AssetInputOutputWithMLTable", assetOutputSettingsAssignments: assetOutputSettingsAssignments);
            Assert.AreEqual(PipelineRunStatusCode.Finished, pipelineRun.CachedStatus, $"PipelineRun id: {pipelineRun.Id}");
        }

        [Test]
        [Category("EnableRunner")]
        public async Task RunEsCloudAssetInputOutputWithMlModelOnBatchAI()
        {
            await RunEsCloudAssetInputOutputWithMlModelOnBatchAI_Inner(_modelInputAsset);
        }

        [Test]
        [Category("EnableRunner")]
        public async Task RunEsCloudAssetInputOutputWithMlModelInRegistryOnBatchAI()
        {
            await RunEsCloudAssetInputOutputWithMlModelOnBatchAI_Inner(_modelInputAssetInRegistry);
        }

        private async Task RunEsCloudAssetInputOutputWithMlModelOnBatchAI_Inner(string inputAssetId)
        {
            var outputAssetPath = "azureml://datastores/myblobdatastore/paths/mlmodel_output_test/${{name}}/";
            (IGraph graph, IDictionary<string, AssetOutputSettings> _) = await CreateEsCloudGraphWithAssetInputOutput(
                inputAssetId,
                AssetType.CustomModel,
                outputAssetPath,
                AssetType.CustomModel,
                _testEnvironment,
                _testConfig.AmlComputeName,
                amlComputeType,
                script: assetInputOutputScript,
                dockerEnabled: true,
                datastoreMode: DataStoreMode.Mount);

            IPipelineRun pipelineRun = await RunTestWithLogsAsync(graph, _testEnvironment, MinutesToWaitForPipelineToComplete, runHistoryExperimentName: "AssetInputOutputWithMLModel");
            Assert.AreEqual(PipelineRunStatusCode.Finished, pipelineRun.CachedStatus, $"PipelineRun id: {pipelineRun.Id}");
        }

        [Test]
        public async Task RunEsCloudAssetInputOutputWithMultinodesOnBatchAI()
        {
            var inputAssetId = _uriFolderAsset;
            var module1InputType = AssetType.UriFolder;
            var module1OutputType = AssetType.MLTable;
            var module1OutputAssetPath = "azureml://datastores/myblobdatastore/paths/asset_multi_nodes_output/module1_output/";
            var module2InputType = AssetType.MLTable;
            var module2OutputType = AssetType.CustomModel;
            var module2OutputAssetPath = "azureml://datastores/myblobdatastore/paths/asset_multi_nodes_output/module2_output_${{name}}/";
            var module3InputType = AssetType.CustomModel;
            var module3OutputType = AssetType.UriFile;
            var module3OutputAssetPath = "azureml://datastores/myblobdatastore/paths/asset_multi_nodes_output/module3_output_file";

            var datasetDefinition = new DataSetDefinition()
            {
                DataTypeShortName = ConvertToDataTypeId(module1InputType),
                Value = new DataSetDefinitionValue()
                {
                    AssetDefinition = new AssetDefinition
                    {
                        AssetId = AssetId.Parse(inputAssetId),
                        SerializedAssetId = inputAssetId,
                        Path = inputAssetId,
                        Type = module1InputType
                    }
                }
            };

            var module1InputSettings = GetAssetInputSetting(module1InputType);

            var module1OutputSettings = new[] { new OutputSetting
            {
                AssetOutputSettings = new AssetOutputSettings {
                    Path = module1OutputAssetPath,
                    Type = module1OutputType
                },
                Name = moduleOutputName,
                DataStoreMode = DataStoreMode.Mount,
                Overwrite = true
            } };

            var module1RunConfig = GetAssetTestRunConfiguration(module1OutputType);

            var module2InputSettings = GetAssetInputSetting(module2InputType);

            var module2OutputSettings = new[] { new OutputSetting
            {
                AssetOutputSettings = new AssetOutputSettings {
                    Path = module2OutputAssetPath,
                    Type = module2OutputType
                },
                Name = moduleOutputName,
                DataStoreMode = DataStoreMode.Upload,
                Overwrite = true
            } };

            var module2RunConfig = GetAssetTestRunConfiguration(module2OutputType);

            var module3InputSettings = GetAssetInputSetting(module3InputType);

            var module3OutputSettings = new[] { new OutputSetting
            {
                AssetOutputSettings = new AssetOutputSettings {
                    Path = module3OutputAssetPath,
                    Type = module3OutputType
                },
                Name = moduleOutputName,
                DataStoreMode = DataStoreMode.Mount,
                Overwrite = true
            } };

            var module3RunConfig = GetAssetTestRunConfiguration(module3OutputType);

            var graph = _testEnvironment.CreateNewGraph();
            IAetherDataSource datasource = await _testEnvironment.GetDataSourceAsync(datasetDefinition);

            string module1Id = await UploadESCloudNodeWithDataSetInput(
                _testEnvironment,
                sourcePath,
                _testConfig.AmlComputeName,
                amlComputeType,
                script: assetInputOutputScript,
                dockerEnabled: true,
                datastoreMode: DataStoreMode.Mount,
                addAssetOutput: true,
                inputDataTypeId: ConvertToDataTypeId(module1InputType),
                outputDataTypeId: ConvertToDataTypeId(module1OutputType),
                moduleDisplayName: "UriFolder In MLTable Out",
                moduleName: "escloud asset test module");

            string module2Id = await UploadESCloudNodeWithDataSetInput(
                _testEnvironment,
                sourcePath,
                _testConfig.AmlComputeName,
                amlComputeType,
                script: assetInputOutputScript,
                dockerEnabled: true,
                datastoreMode: DataStoreMode.Mount,
                addAssetOutput: true,
                inputDataTypeId: ConvertToDataTypeId(module2InputType),
                outputDataTypeId: ConvertToDataTypeId(module2OutputType),
                moduleDisplayName: "MLTable In MLModel Out",
                moduleName: "escloud asset test module");

            string module3Id = await UploadESCloudNodeWithDataSetInput(
                _testEnvironment,
                sourcePath,
                _testConfig.AmlComputeName,
                amlComputeType,
                script: assetInputOutputScript,
                dockerEnabled: true,
                datastoreMode: DataStoreMode.Mount,
                addAssetOutput: true,
                inputDataTypeId: ConvertToDataTypeId(module3InputType),
                outputDataTypeId: ConvertToDataTypeId(module3OutputType),
                moduleDisplayName: "MLModel In UriFile Out",
                moduleName: "escloud asset test module");


            IAetherModule module1 = await _testEnvironment.GetModuleAsync(module1Id);
            IAetherModule module2 = await _testEnvironment.GetModuleAsync(module2Id);
            IAetherModule module3 = await _testEnvironment.GetModuleAsync(module3Id);
            IModuleNode moduleNode1 = graph.AddNode(module1, moduleInputSettings: module1InputSettings, moduleOutputSettings: module1OutputSettings, runConfig: SerializationHelpers.SerializeEntity(module1RunConfig));
            IModuleNode moduleNode2 = graph.AddNode(module2, moduleInputSettings: module2InputSettings, moduleOutputSettings: module2OutputSettings, runConfig: SerializationHelpers.SerializeEntity(module2RunConfig));
            IModuleNode moduleNode3 = graph.AddNode(module3, moduleInputSettings: module3InputSettings, moduleOutputSettings: module3OutputSettings, runConfig: SerializationHelpers.SerializeEntity(module3RunConfig));

            SetArgumentsByInputAndOutput(moduleNode1);
            SetArgumentsByInputAndOutput(moduleNode2);
            SetArgumentsByInputAndOutput(moduleNode3);

            IDataSourceNode sourceNode = graph.AddNode(datasource);
            graph.Connect(sourceNode, moduleNode1.InputPortDictionary[moduleInputName]);
            graph.Connect(moduleNode1.OutputPortDictionary[moduleOutputName], moduleNode2.InputPortDictionary[moduleInputName]);
            graph.Connect(moduleNode2.OutputPortDictionary[moduleOutputName], moduleNode3.InputPortDictionary[moduleInputName]);

            IPipelineRun pipelineRun = await RunTestWithLogsAsync(graph, _testEnvironment, MinutesToWaitForPipelineToComplete, runHistoryExperimentName: "AssetInputOutputWithMultipleNodes");
            Assert.AreEqual(PipelineRunStatusCode.Finished, pipelineRun.CachedStatus, $"PipelineRun id: {pipelineRun.Id}");
        }

        [Test]
        [Category("SkipRunnerTest")]
        public async Task RunEsCloudAssetInputOutputWithPipelineOutput()
        {
            var outputAssetPath = "azureml://datastores/myblobdatastore/paths/mlmodel_output_test/${{name}}/";
            (IGraph graph, IDictionary<string, AssetOutputSettings> _) = await CreateEsCloudGraphWithAssetInputOutput(
                _modelInputAsset,
                AssetType.CustomModel,
                outputAssetPath,
                AssetType.CustomModel,
                _testEnvironment,
                _testConfig.AmlComputeName,
                amlComputeType,
                script: assetInputOutputScript,
                dockerEnabled: true,
                datastoreMode: DataStoreMode.Mount,
                withPipelineOutput: true);

            IPipelineRun pipelineRun = await RunTestWithLogsAsync(graph, _testEnvironment, MinutesToWaitForPipelineToComplete, runHistoryExperimentName: "AssetInputOutputWithMLModel");
            Assert.AreEqual(PipelineRunStatusCode.Finished, pipelineRun.CachedStatus, $"PipelineRun id: {pipelineRun.Id}");
        }

        private async Task<(IGraph graph, IDictionary<string, AssetOutputSettings> assetOutputSettingsAssignments)> CreateEsCloudGraphWithAssetInputOutput(
            string inputAssetId,
            AssetType inputType,
            string outputAssetPath,
            AssetType outputType,
            AetherEnvironment environment,
            string computeTargetName,
            string computeTargetType,
            Script script,
            bool dockerEnabled = true,
            DataStoreMode datastoreMode = DataStoreMode.Mount,
            bool setDataStoreModeInAssetOutputSetting = false,
            bool setAssetOutputSettingAsParam = false,
            bool setAssetPathParam = false,
            bool withPipelineOutput = false,
            bool useGraphDefaultDatastore = false)
        {
            var graph = environment.CreateNewGraph();

            var datasetDefinition = new DataSetDefinition()
            {
                DataTypeShortName = ConvertToDataTypeId(inputType),
                Value = new DataSetDefinitionValue()
                {
                    AssetDefinition = new AssetDefinition
                    {
                        AssetId = AssetId.Parse(inputAssetId),
                        SerializedAssetId = inputAssetId,
                        Path = inputAssetId,
                        Type = inputType
                    }
                }
            };

            var inputSettings = new[]
            {
                new InputSetting
                {
                    Name = moduleInputName,
                    DataStoreMode = DataStoreMode.Mount,
                }
            };

            var parameterAssignment = new ParameterAssignment(name: "parameterAssignmentConcatenate", value: "", ParameterValueType.Concatenate)
            {
                AssignmentsToConcatenate = new[]
                    {
                        new ParameterAssignment(name: "parameterAssignmentGraphParameterName", value: "OutputAssetPath", ParameterValueType.GraphParameterName),
                        new ParameterAssignment(name: "parameterAssignmentLiteral", value: outputAssetPath.Substring(8), ParameterValueType.Literal)
                    }
            };

            var assetOutputSettings = new AssetOutputSettings
            {
                Path = setAssetPathParam ? "defaultPath" : outputAssetPath,
                Type = outputType,
                PathParameterAssignment = setAssetPathParam ? parameterAssignment : null
            };

            if (setDataStoreModeInAssetOutputSetting)
            {
                assetOutputSettings.DataStoreMode = DataStoreMode.Mount;
            }

            var assetOutputSettingsAssignments = setAssetOutputSettingAsParam ? new Dictionary<string, AssetOutputSettings>
            {
                { "assetOutputParam", assetOutputSettings },
            } : null;

            var outputSettings = new[] { new OutputSetting
            {
                AssetOutputSettingsParameterName = setAssetOutputSettingAsParam ? "assetOutputParam" : null,
                AssetOutputSettings = setAssetOutputSettingAsParam ? null : assetOutputSettings,
                Name = moduleOutputName,
                DataStoreMode = DataStoreMode.Mount,
                Overwrite = true
            } };

            IAetherDataSource datasource = await environment.GetDataSourceAsync(datasetDefinition);
            string moduleId = await UploadESCloudNodeWithDataSetInput(
                environment,
                sourcePath,
                computeTargetName,
                computeTargetType,
                script,
                dockerEnabled,
                datastoreMode,
                addAssetOutput: true,
                inputDataTypeId: ConvertToDataTypeId(inputType),
                outputDataTypeId: ConvertToDataTypeId(outputType),
                moduleDisplayName: "Asset Input/Output Validation",
                moduleName: "escloud asset test module");

            var runConfig = GetAssetTestRunConfiguration(outputType);

            IAetherModule module = await environment.GetModuleAsync(moduleId);
            IModuleNode moduleNode1 = graph.AddNode(module, moduleInputSettings: inputSettings, moduleOutputSettings: outputSettings, runConfig: SerializationHelpers.SerializeEntity(runConfig));
            moduleNode1.SetDatastoreSetting(useGraphDefaultDatastore);

            SetArgumentsByInputAndOutput(moduleNode1);

            IDataSourceNode sourceNode = graph.AddNode(datasource);
            graph.Connect(sourceNode, moduleNode1.InputPortDictionary[moduleInputName]);

            if (useGraphDefaultDatastore)
            {
                var datastoreSetting = new DatastoreSetting()
                {
                    DataStoreName = "myblobdatastore"
                };
                graph.AddDefaultDatastore(datastoreSetting);
            }

            if (withPipelineOutput)
            {
                graph.AddGraphOutput("graphOutput");
                graph.GetGraphOutput("graphOutput").Connect(moduleNode1.OutputPortDictionary[moduleOutputName]);
            }

            return (graph, assetOutputSettingsAssignments);
        }

        [Test]
        [Category("SkipRunnerTest")]
        public async Task RunEsCloudWithArtifactOutput()
        {
            IGraph graph = await CreateEsCloudGraphWithArtifactOutput();
            IPipelineRun pipelineRun = await RunTestWithLogsAsync(graph, _testEnvironment, MinutesToWaitForPipelineToComplete);
            Assert.AreEqual(PipelineRunStatusCode.Finished, pipelineRun.CachedStatus, $"PipelineRun id: {pipelineRun.Id}");
        }

        [Test]
        [Ignore("Skip due to currently runs on windows compute cannot success for now")]
        public async Task RunEsCloudWindowsCPlusPlusTest()
        {
            // add a guid to avoid reuse
            var commandLine = $"CPlusPlusTest.exe 0 {Guid.NewGuid()}";
            IGraph graph = await CreateCommandGraph(_testEnvironment, "TestResources/EscloudFile/windowscmd", commandLine);

            IPipelineRun pipelineRun = await RunTestWithLogsAsync(graph, _testEnvironment, MinutesToWaitForPipelineWithWindowsComputeToComplete);
            Assert.AreEqual(PipelineRunStatusCode.Finished, pipelineRun.CachedStatus, $"PipelineRun id: {pipelineRun.Id}");
        }
        #endregion

        #region Environment Tests
        [Test]
        public async Task RunEsCloudComponentWithInlineEnvironmentDefinitionInRunConfigOverwriteWithNullEnvironmentInRunConfigTest()
        {
            IGraph graph = await CreateEsCloudGraphUsingInlineEnvironmentModuleInRunConfigWithNullEnvironmentInRunConfigOverwrite(_testEnvironment, sourcePath);
            IPipelineRun pipelineRun = await RunTestWithLogsAsync(graph, _testEnvironment, MinutesToWaitForPipelineToComplete);
            Assert.AreEqual(PipelineRunStatusCode.Finished, pipelineRun.CachedStatus, $"PipelineRun id: {pipelineRun.Id}");

            var runConfig = await GetNodeRunConfig(pipelineRun.Id, pipelineRun.RunHistoryExperimentName).ConfigureAwait(false);
            Assert.AreEqual(OriginalDockerImage, runConfig.Environment.Docker.BaseImage.FullyQualified);
        }

        [Test]
        public async Task RunEsCloudComponentWithInlineEnvironmentDefinitionInRunConfigOverwriteWithNullEnvironmentInEsCloudConfigTest()
        {
            IGraph graph = await CreateGraphUsingInlineEnvironmentDefinitionModuleWithNullEnvironmentInEsCloudConfigOverwrite(_testEnvironment, sourcePath);
            IPipelineRun pipelineRun = await RunTestWithLogsAsync(graph, _testEnvironment, MinutesToWaitForPipelineWithWindowsComputeToComplete);
            Assert.NotNull(pipelineRun.Id);

            var runConfig = await GetNodeRunConfig(pipelineRun.Id, pipelineRun.RunHistoryExperimentName).ConfigureAwait(false);
            Assert.AreEqual(OriginalDockerImage, runConfig.Environment.Docker.BaseImage.FullyQualified);
        }

        [Test]
        public async Task RunEsCloudComponentWithInlineEnvironmentDefinitionInRunConfigOverwriteWithSameInlineEnvironmentInRunConfigTest()
        {
            IGraph graph = await CreateGraphUsingInlineEnvironmentDefinitionWithSameInlineEnvironmentInRunConfigOverwrite(_testEnvironment, sourcePath);
            IPipelineRun pipelineRun = await RunTestWithLogsAsync(graph, _testEnvironment, MinutesToWaitForPipelineWithWindowsComputeToComplete);
            Assert.NotNull(pipelineRun.Id);

            var runConfig = await GetNodeRunConfig(pipelineRun.Id, pipelineRun.RunHistoryExperimentName).ConfigureAwait(false);
            Assert.AreEqual(OriginalDockerImage, runConfig.Environment.Docker.BaseImage.FullyQualified);
        }

        [Test]
        public async Task RunEsCloudComponentWithInlineEnvironmentDefinitionInRunConfigOverwriteWithSameInlineEnvironmentInEsCloudConfigTest()
        {
            IGraph graph = await CreateGraphUsingInlineEnvironmentDefinitionWithSameInlineEnvironmentInEsCloudConfigOverwrite(_testEnvironment, sourcePath);
            IPipelineRun pipelineRun = await RunTestWithLogsAsync(graph, _testEnvironment, MinutesToWaitForPipelineWithWindowsComputeToComplete);
            Assert.NotNull(pipelineRun.Id);

            var runConfig = await GetNodeRunConfig(pipelineRun.Id, pipelineRun.RunHistoryExperimentName).ConfigureAwait(false);
            Assert.AreEqual(OriginalDockerImage, runConfig.Environment.Docker.BaseImage.FullyQualified);
        }

        [Test]
        public async Task RunEsCloudComponentWithInlineEnvironmentDefinitionInRunConfigOverwriteWithInlineEnvironmentInRunConfigTest()
        {
            IGraph graph = await CreateGraphUsingInlineEnvironmentDefinitionModuleWithInlineEnvironmentInRunConfigOverwrite(_testEnvironment, sourcePath);
            IPipelineRun pipelineRun = await RunTestWithLogsAsync(graph, _testEnvironment, MinutesToWaitForPipelineWithWindowsComputeToComplete);
            Assert.NotNull(pipelineRun.Id);

            var runConfig = await GetNodeRunConfig(pipelineRun.Id, pipelineRun.RunHistoryExperimentName).ConfigureAwait(false);
            Assert.AreEqual(TestDockerImage, runConfig.Environment.Docker.BaseImage.FullyQualified);
        }

        [Test]
        [Category("SkipRunnerTest")]
        public async Task RunEsCloudComponentWithInlineEnvironmentDefinitionInRunConfigOverwriteWithNamedEnvironmentInEsCloudConfigTest()
        {
            IGraph graph = await CreateGraphUsingInlineEnvironmentDefinitionModuleWithNamedEnvironmentInEsCloudConfigOverwrite(_testEnvironment, sourcePath);
            IPipelineRun pipelineRun = await RunTestWithLogsAsync(graph, _testEnvironment, MinutesToWaitForPipelineWithWindowsComputeToComplete);
            Assert.NotNull(pipelineRun.Id);

            var runConfig = await GetNodeRunConfig(pipelineRun.Id, pipelineRun.RunHistoryExperimentName).ConfigureAwait(false);
            Assert.AreEqual("BasicNamedEnvironment", runConfig.Environment.Name);
            Assert.AreEqual("1", runConfig.Environment.Version);

        }

        [Test]
        public async Task RunEsCloudComponentWithInlineEnvironmentDefinitionInRunConfigOverwriteWithInlineEnvironmentInEsCloudConfigTest()
        {
            IGraph graph = await CreateGraphUsingInlineEnvironmentDefinitionModuleWithEnvironmentDefinitionInEsCloudConfigOverwrite(_testEnvironment, sourcePath);
            IPipelineRun pipelineRun = await RunTestWithLogsAsync(graph, _testEnvironment, MinutesToWaitForPipelineWithWindowsComputeToComplete);
            Assert.NotNull(pipelineRun.Id);

            var runConfig = await GetNodeRunConfig(pipelineRun.Id, pipelineRun.RunHistoryExperimentName).ConfigureAwait(false);
            Assert.AreEqual(TestDockerImage, runConfig.Environment.Docker.BaseImage.FullyQualified);
        }

        [Test]
        public async Task RunEsCloudComponentWithNamedEnvironmentDefinitionOverwriteWithNullEnvironmentInRunConfigTest()
        {
            IGraph graph = await CreateGraphUsingNamedEnvironmentDefinitionModuleWithNullEnvironmentInRunConfigOverwrite(_testEnvironment, sourcePath);
            IPipelineRun pipelineRun = await RunTestWithLogsAsync(graph, _testEnvironment, MinutesToWaitForPipelineWithWindowsComputeToComplete);
            Assert.NotNull(pipelineRun.Id);

            var runConfig = await GetNodeRunConfig(pipelineRun.Id, pipelineRun.RunHistoryExperimentName).ConfigureAwait(false);
            Assert.AreEqual("AzureML-minimal-ubuntu18.04-py37-cpu-inference", runConfig.Environment.Name);
            Assert.AreEqual("25", runConfig.Environment.Version);
        }

        [Test]
        public async Task RunEsCloudComponentWithNamedEnvironmentDefinitionOverwriteWithoutEnvironmentInRunConfigTest()
        {
            IGraph graph = await CreateGraphUsingNamedEnvironmentDefinitionModuleWithoutEnvironmentInRunConfigOverwrite(_testEnvironment, sourcePath);
            IPipelineRun pipelineRun = await RunTestWithLogsAsync(graph, _testEnvironment, MinutesToWaitForPipelineWithWindowsComputeToComplete);
            Assert.NotNull(pipelineRun.Id);

            var runConfig = await GetNodeRunConfig(pipelineRun.Id, pipelineRun.RunHistoryExperimentName).ConfigureAwait(false);
            Assert.AreEqual("AzureML-minimal-ubuntu18.04-py37-cpu-inference", runConfig.Environment.Name);
            Assert.AreEqual("25", runConfig.Environment.Version);
        }

        [Test]
        public async Task RunEsCloudComponentWithNamedEnvironmentDefinitionOverwriteWithInlineEnvironmentInRunConfigTest()
        {
            IGraph graph = await CreateGraphUsingNamedEnvironmentDefinitionModuleWithInlineEnvironmentOverwrite(_testEnvironment, sourcePath);
            IPipelineRun pipelineRun = await RunTestWithLogsAsync(graph, _testEnvironment, MinutesToWaitForPipelineWithWindowsComputeToComplete);
            Assert.NotNull(pipelineRun.Id);

            var runConfig = await GetNodeRunConfig(pipelineRun.Id, pipelineRun.RunHistoryExperimentName).ConfigureAwait(false);
            Assert.AreEqual(TestDockerImage, runConfig.Environment.Docker.BaseImage.FullyQualified);
            Assert.AreNotEqual("AzureML-minimal-ubuntu18.04-py37-cpu-inference", runConfig.Environment.Name);
            Assert.AreNotEqual("23", runConfig.Environment.Version);
            Assert.Null(runConfig.Environment.Docker.BaseDockerfile);
        }

        [Test]
        [Category("SkipRunnerTest")]
        public async Task RunEsCloudComponentWithNamedEnvironmentDefinitionOverwriteWithNamedEnvironmentInEsCloudConfigTest()
        {
            IGraph graph = await CreateGraphUsingNamedEnvironmentDefinitionModuleWithNamedEnvironmentInEsCloudConfigOverwrite(_testEnvironment, sourcePath);
            IPipelineRun pipelineRun = await RunTestWithLogsAsync(graph, _testEnvironment, MinutesToWaitForPipelineWithWindowsComputeToComplete);
            Assert.NotNull(pipelineRun.Id);

            var runConfig = await GetNodeRunConfig(pipelineRun.Id, pipelineRun.RunHistoryExperimentName).ConfigureAwait(false);
            Assert.AreEqual("BasicNamedEnvironment", runConfig.Environment.Name);
            Assert.AreEqual("1", runConfig.Environment.Version);
        }

        [Test]
        // Below case cannot get correct module cloudsetting since component service doesn't take the change.
        public async Task RunEsCloudComponentWithInlineEnvironmentDefinitionInEsCloudConfigOverwriteWithNullEnvironmentInEsCloudConfigTest()
        {
            IGraph graph = await CreateGraphUsingInlineEnvironmentDefinitionInEsCloudConfigModuleWithNullEnvironmentInEsCloudConfigOverwrite(_testEnvironment, sourcePath);
            IPipelineRun pipelineRun = await RunTestWithLogsAsync(graph, _testEnvironment, MinutesToWaitForPipelineWithWindowsComputeToComplete);
            Assert.NotNull(pipelineRun.Id);

            var runConfig = await GetNodeRunConfig(pipelineRun.Id, pipelineRun.RunHistoryExperimentName).ConfigureAwait(false);
            Assert.AreEqual(OriginalDockerImage, runConfig.Environment.Docker.BaseImage.FullyQualified);
        }

        [Test]
        public async Task RunEsCloudComponentWithInlineEnvironmentDefinitionInEsCloudConfigOverwriteWithNamedEnvironmentInEsCloudConfigTest()
        {
            IGraph graph = await CreateGraphUsingInlineEnvironmentDefinitionInEsCloudConfigModuleWithNamedEnvironmentInEsCloudConfigOverwrite(_testEnvironment, sourcePath);
            IPipelineRun pipelineRun = await RunTestWithLogsAsync(graph, _testEnvironment, MinutesToWaitForPipelineWithWindowsComputeToComplete);
            Assert.NotNull(pipelineRun.Id);

            var runConfig = await GetNodeRunConfig(pipelineRun.Id, pipelineRun.RunHistoryExperimentName).ConfigureAwait(false);
            Assert.AreEqual("AzureML-minimal-ubuntu18.04-py37-cpu-inference", runConfig.Environment.Name);
            Assert.AreEqual("27", runConfig.Environment.Version);

        }

        [Test]
        public async Task RunEsCloudComponentWithInlineEnvironmentDefinitionInEsCloudConfigOverwriteWithEnvironmentDefinitionInEsCloudConfigTest()
        {
            IGraph graph = await CreateGraphUsingInlineEnvironmentDefinitionInEsCloudConfigModuleWithEnvironmentDefinitionInEsCloudConfigOverwrite(_testEnvironment, sourcePath);
            IPipelineRun pipelineRun = await RunTestWithLogsAsync(graph, _testEnvironment, MinutesToWaitForPipelineWithWindowsComputeToComplete);
            Assert.NotNull(pipelineRun.Id);

            var runConfig = await GetNodeRunConfig(pipelineRun.Id, pipelineRun.RunHistoryExperimentName).ConfigureAwait(false);
            Assert.AreEqual(TestDockerImage, runConfig.Environment.Docker.BaseImage.FullyQualified);
        }

        [Test]
        public async Task RunEsCloudComponentWithNamedEnvironmentDefinitionInEsCloudConfigOverwriteWithNamedEnvironmentInEsCloudConfigTest()
        {
            IGraph graph = await CreateGraphUsingNamedEnvironmentDefinitionInEsCloudConfigModuleWithNamedEnvironmentInEsCloudConfigOverwrite(_testEnvironment, sourcePath);
            IPipelineRun pipelineRun = await RunTestWithLogsAsync(graph, _testEnvironment, MinutesToWaitForPipelineWithWindowsComputeToComplete);
            Assert.NotNull(pipelineRun.Id);

            var runConfig = await GetNodeRunConfig(pipelineRun.Id, pipelineRun.RunHistoryExperimentName).ConfigureAwait(false);
            Assert.AreEqual("AzureML-minimal-ubuntu18.04-py37-cpu-inference", runConfig.Environment.Name);
            Assert.AreEqual("27", runConfig.Environment.Version);
        }

        [Test]
        public async Task RunEsCloudComponentWithNamedEnvironmentDefinitionInEsCloudConfigOverwriteWithEnvironmentDefinitionInEsCloudConfigTest()
        {
            IGraph graph = await CreateGraphUsingNamedEnvironmentDefinitionInEsCloudConfigModuleWithEnvironmentDefinitionInEsCloudConfigOverwrite(_testEnvironment, sourcePath);
            IPipelineRun pipelineRun = await RunTestWithLogsAsync(graph, _testEnvironment, MinutesToWaitForPipelineWithWindowsComputeToComplete);
            Assert.NotNull(pipelineRun.Id);

            var runConfig = await GetNodeRunConfig(pipelineRun.Id, pipelineRun.RunHistoryExperimentName).ConfigureAwait(false);
            Assert.AreEqual(TestDockerImage, runConfig.Environment.Docker.BaseImage.FullyQualified);
            Assert.AreNotEqual("AzureML-minimal-ubuntu18.04-py37-cpu-inference", runConfig.Environment.Name);
            Assert.AreNotEqual("23", runConfig.Environment.Version);
        }

        [Test]
        public async Task RunEsCloudComponentWithNamedEnvironmentDefinitionWithNullVersionInRunConfigTest()
        {
            IGraph graph = await CreateGraphUsingNamedEnvironmentDefinitionWithNullVersionInRunConfig(_testEnvironment, sourcePath);
            IPipelineRun pipelineRun = await RunTestWithLogsAsync(graph, _testEnvironment, MinutesToWaitForPipelineWithWindowsComputeToComplete);
            Assert.NotNull(pipelineRun.Id);

            var runConfig = await GetNodeRunConfig(pipelineRun.Id, pipelineRun.RunHistoryExperimentName).ConfigureAwait(false);
            Assert.AreEqual(EnvironmentNameWithoutVersion, runConfig.Environment.Name);
            // Todo: Get real latest version
            var version = int.Parse(runConfig.Environment.Version);
            Assert.True(version > 45);
        }

        [Test]
        public async Task RunEsCloudComponentWithNamedEnvironmentDefinitionWithoutVersionInRunConfigTest()
        {
            IGraph graph = await CreateGraphUsingNamedEnvironmentDefinitionWithoutVersionInRunConfig(_testEnvironment, sourcePath);
            IPipelineRun pipelineRun = await RunTestWithLogsAsync(graph, _testEnvironment, MinutesToWaitForPipelineWithWindowsComputeToComplete);
            Assert.NotNull(pipelineRun.Id);

            var runConfig = await GetNodeRunConfig(pipelineRun.Id, pipelineRun.RunHistoryExperimentName).ConfigureAwait(false);
            Assert.AreEqual(EnvironmentNameWithoutVersion, runConfig.Environment.Name);
            // Todo: Get real latest version
            var version = int.Parse(runConfig.Environment.Version);
            Assert.True(version > 45);
        }

        #endregion

        #region EsCloud Integration Test
        [Test]
        [Ignore("Skip due to currently runs on windows compute cannot success for now")]
        public async Task RunEsCloudExepoolStyleCommandSupportWindowsTest()
        {
            var commandLine = "CheckFileExistsTest.exe {param.DataFolderPath}\\DataFileRaw.txt";
            IGraph graph = await CreateExepoolStyleCommandGraph(_testEnvironment, "TestResources/EscloudFile/checkfileexiststest", commandLine);

            IPipelineRun pipelineRun = await RunTestWithLogsAsync(graph, _testEnvironment, MinutesToWaitForPipelineWithWindowsComputeToComplete);
            Assert.AreEqual(PipelineRunStatusCode.Finished, pipelineRun.CachedStatus, $"PipelineRun id: {pipelineRun.Id}");
        }

        [Test]
        // Expected priority: node1 100, node2 500
        public async Task RunEsCloudWithPriorityTest()
        {
            string sourcePath = "TestResources/EscloudFile/helloworld";
            IGraph graph = await CreateEsCloudGraph(_testEnvironment, sourcePath, true);

            var creationPriority = new CloudPrioritySetting
            {
                AmlComputePriority = new PriorityConfiguration { CloudPriority = 100 },
                ItpPriority = new PriorityConfiguration { CloudPriority = 200 },
            };
            var graphPriority = new CloudPrioritySetting
            {
                AmlComputePriority = new PriorityConfiguration { CloudPriority = 300 },
                ItpPriority = new PriorityConfiguration { CloudPriority = 400 },
            };
            graph.AddDefaultCloudPriority(graphPriority);

            IPipelineRun pipelineRun = await RunTestWithLogsAsync(
                graph, _testEnvironment,
                MinutesToWaitForPipelineToComplete,
                defaultCloudPriority: creationPriority);
            Assert.AreEqual(PipelineRunStatusCode.Finished, pipelineRun.CachedStatus, $"PipelineRun id: {pipelineRun.Id}");
        }

        [Test]
        [Ignore("Data Compute change is not released")]
        public async Task RunEsCloudOutputToFileTest()
        {
            IAetherModule module = await _testEnvironment.GetModuleAsync(OutputToFileComponentId);

            IGraph graph = _testEnvironment.CreateNewGraph();
            IAetherDataSource dataSource1 = await _testEnvironment.GetDataSourceAsync(
                dataSetDefinition: new DataSetDefinition
                {
                    DataTypeShortName = "AnyDirectory",
                    Value = new DataSetDefinitionValue
                    {
                        DataSetReference = new RegisteredDataSetReference
                        {
                            Id = _fileDataset.DatasetId.ToString(),
                        }
                    }
                });

            IDataSourceNode inputNode1 = graph.AddNode(dataSource1);

            var runConfig = new Microsoft.MachineLearning.Execution.Contracts.RunConfiguration
            {
                EnvironmentVariables = new Dictionary<string, string>
                {
                    { "AZUREML_COMPUTE_USE_COMMON_RUNTIME", "false" },
                },
            };

            IModuleNode moduleNode = graph.AddNode(module, cloudSettings: new CloudSettings
            {
                EsCloudConfig = new EsCloudConfiguration
                {
                    EnableOutputToFileBasedOnDataTypeId = true,
                },
            }, useDefaultCompute: true,
            regenerateOutput: true,
            runConfig: SerializationHelpers.SerializeEntity(runConfig));

            var mountOutputSetting = new OutputSetting
            {
                Name = "mounted_output_path",
                DataStoreName = "workspaceblobstore",
                DataStoreMode = DataStoreMode.Mount,
                DataReferenceName = "mounted_output_path",
                DatasetRegistration = new DatasetRegistration { Name = "esCloudMountFileOutputDatasetRegistration", CreateNewVersion = true },
                DatasetOutputOptions = new DatasetOutputOptions { PathOnDatastore = "/outputdataset/mount/not_exist/output.csv", }
            };
            moduleNode.OutputSettings.Add("mounted_output_path", mountOutputSetting);

            var uploadOutputSetting = new OutputSetting
            {
                Name = "upload_output_path",
                DataStoreName = "workspaceblobstore",
                DataStoreMode = DataStoreMode.Upload,
                DataReferenceName = "upload_output_path",
                DatasetRegistration = new DatasetRegistration { Name = "esCloudUploadFileOutputDatasetRegistration", CreateNewVersion = true },
                DatasetOutputOptions = new DatasetOutputOptions { PathOnDatastore = "/outputdataset/upload/not_exist/output.csv", }
            };
            moduleNode.OutputSettings.Add("upload_output_path", uploadOutputSetting);

            graph.Connect(inputNode1, moduleNode.InputPortDictionary["mounted_input_path"]);
            graph.AddDefaultCompute(new ComputeSetting { Name = _testConfig.AmlComputeName, ComputeType = ComputeType.MLC });

            IPipelineRun pipelineRun = await RunTestWithLogsAsync(graph, _testEnvironment, MinutesToWaitForPipelineToComplete);
            Assert.AreEqual(PipelineRunStatusCode.Finished, pipelineRun.CachedStatus, $"PipelineRun id: {pipelineRun.Id}");
        }

        [Test]
        // Expected priority: node1 100, node2 500
        public async Task RunEsCloudTestInSubgraph_CloudPriority()
        {
            // Create subgraph.
            string sourcePath = "TestResources/EscloudFile/helloworld";
            IGraph graph = await CreateEsCloudGraph(_testEnvironment, sourcePath, true);
            string subGraphId = await _testEnvironment.CreateSubGraphAsync(graph, "test subgraph").ConfigureAwait(false);
            Assert.IsNotNull(subGraphId);
            var subGraph = await _testEnvironment.GetSubGraphAsync(subGraphId);

            // Create a new graph containing a subgraph node.
            IGraph parentGraph = _testEnvironment.CreateNewGraph();
            var creationPriority = new CloudPrioritySetting
            {
                AmlComputePriority = new PriorityConfiguration { CloudPriority = 100 },
                ItpPriority = new PriorityConfiguration { CloudPriority = 200 },
                SingularityPriority = new PriorityConfiguration { StringTypePriority = "high" },
            };
            var graphPriority = new CloudPrioritySetting
            {
                AmlComputePriority = new PriorityConfiguration { CloudPriority = 300 },
                ItpPriority = new PriorityConfiguration { CloudPriority = 400 },
                SingularityPriority = new PriorityConfiguration { StringTypePriority = "medium" },
            };

            // Add default cloud priority.
            parentGraph.AddDefaultCloudPriority(graphPriority);
            parentGraph.AddNode(subGraph);

            IPipelineRun pipelineRun = await RunTestWithLogsAsync(
                parentGraph, _testEnvironment,
                MinutesToWaitForPipelineToComplete,
                defaultCloudPriority: creationPriority);
            Assert.AreEqual(PipelineRunStatusCode.Finished, pipelineRun.CachedStatus, $"PipelineRun id: {pipelineRun.Id}");
        }

        [Test]
        public async Task RunEsCloudTestInSubgraph_WithIdentity()
        {
            // Create subgraph.
            string sourcePath = "TestResources/EscloudFile/helloworld";
            IGraph graph = await CreateEsCloudGraph(_testEnvironment, sourcePath, true);
            string subGraphId = await _testEnvironment.CreateSubGraphAsync(graph, "test subgraph").ConfigureAwait(false);
            Assert.IsNotNull(subGraphId);
            var subGraph = await _testEnvironment.GetSubGraphAsync(subGraphId);

            // Create a new graph containing a subgraph node.
            IGraph parentGraph = _testEnvironment.CreateNewGraph();
            parentGraph.AddNode(subGraph);

            IdentitySetting identityConfig = new IdentitySetting
            {
                Type = IdentityType.AMLToken,
            };

            IPipelineRun pipelineRun = await RunTestWithLogsAsync(
                parentGraph, _testEnvironment,
                MinutesToWaitForPipelineToComplete,
                identityConfig: identityConfig);
            Assert.AreEqual(PipelineRunStatusCode.Finished, pipelineRun.CachedStatus, $"PipelineRun id: {pipelineRun.Id}");
        }

        [Test]
        [Ignore("Skip due to currently runs on windows compute cannot success for now")]
        public async Task RunEsCloudWindowsCPlusPlusTestWithNonZeroReturnCode()
        {
            // add a guid to avoid reuse
            var commandLine = $"CPlusPlusTest.exe 1 {Guid.NewGuid()}";
            IGraph graph = await CreateCommandGraph(_testEnvironment, "TestResources/EscloudFile/windowscmd", commandLine);

            IPipelineRun pipelineRun = await RunTestWithLogsAsync(graph, _testEnvironment, MinutesToWaitForPipelineWithWindowsComputeToComplete);
            Assert.AreEqual(PipelineRunStatusCode.Finished, pipelineRun.CachedStatus, $"PipelineRun id: {pipelineRun.Id}");
        }

        #region HyperDrive Run

        [Test]
        [Category("SkipRunnerTest")]
        [Category("SkipForInt")]
        [TestCase(true)]
        [TestCase(false)]
        public async Task RunEsCloudForCommandComponentWithHyperDriveConfiguration(bool useMacroInOutputAssetPath)
        {
            var dtcGraph = _testEnvironment.CreateNewGraph();
            var inputPortName = "data_folder";
            var inputPortType = AssetType.UriFolder;
            var outputPortName = "saved_model";

            var inputAssetId = "azureml://locations/centraluseuap/workspaces/3c80c156-6e4e-486d-90b3-3e3753ef4a7f/data/azureml_e2f8e3f9-078c-4caa-9a95-d18c9860ca2b_input_data_minist/versions/1";
            var datasetDefinition = new DataSetDefinition()
            {
                DataTypeShortName = inputPortType.ToString(),
                Value = new DataSetDefinitionValue()
                {
                    AssetDefinition = new AssetDefinition()
                    {
                        Path = inputAssetId,
                        AssetId = AssetId.Parse(inputAssetId),
                        Type = inputPortType
                    },
                    // DataSetReference should be ignored.
                    DataSetReference = new RegisteredDataSetReference()
                    {
                        Id = _mnistDataset.DatasetId.ToString(),
                    }
                }
            };

            var cloudSettings = new CloudSettings
            {
                EsCloudConfig = new EsCloudConfiguration
                {
                    HyperDriveConfiguration = new HyperDriveConfiguration
                    {
                        HyperDriveRunConfig = "{\"user\":\"<EMAIL>\",\"platform\":\"AML\",\"max_total_jobs\":2,\"max_concurrent_jobs\":2,\"max_duration_minutes\":10080,\"primary_metric_config\":{\"name\":\"validation_acc\",\"goal\":\"maximize\"},\"generator_config\":{\"name\":\"RANDOM\",\"parameter_space\":{\"batch_size\":[\"choice\",[[10,20,30]]],\"first_layer_neurons\":[\"choice\",[[10,20,50]]],\"second_layer_neurons\":[\"choice\",[[10,20,30]]],\"learning_rate\":[\"loguniform\",[-5,-1]]}},\"policy_config\":{\"name\":\"Bandit\",\"properties\":{\"evaluation_interval\":1,\"delay_evaluation\":5,\"slack_factor\":0.1}},\"platform_config\":{\"ServiceAddress\":\"https://master.experiments.azureml-test.net\",\"SubscriptionId\":\"b8c23406-f9b5-4ccb-8a65-a8cb5dcd6a5a\",\"ResourceGroupName\":\"rge2etests\",\"WorkspaceName\":\"wse2etests\",\"ExperimentName\":null,\"Definition\":{\"Configuration\":null,\"Attribution\":null,\"TelemetryValues\":null,\"Overrides\":{\"Script\":null,\"Command\":\"echo \\\"Start training ...\\\" && python mnist.py --data_folder $AZUREML_DATAREFERENCE_data_folder --saved_model DatasetOutputConfig:saved_model --first_layer_neurons $AZUREML_SWEEP_first_layer_neurons --second_layer_neurons $AZUREML_SWEEP_second_layer_neurons --learning_rate $AZUREML_SWEEP_learning_rate --batch_size $AZUREML_SWEEP_batch_size\",\"UseAbsolutePath\":false,\"Arguments\":[],\"SourceDirectoryDataStore\":null,\"Framework\":0,\"Communicator\":0,\"Target\":\"cpucluster\",\"DataReferences\":{},\"Data\":{},\"OutputData\":{},\"Datacaches\":[],\"JobName\":null,\"MaxRunDurationSeconds\":null,\"NodeCount\":1,\"Priority\":null,\"CredentialPassthrough\":false,\"Identity\":null,\"Environment\":{\"Python\":{\"InterpreterPath\":null,\"UserManagedDependencies\":false,\"CondaDependencies\":{\"channels\":[\"defaults\"],\"dependencies\":[\"python=3.7.9\",\"pip=20.0\",{\"pip\":[\"azureml-defaults\",\"azureml-dataprep>=1.6\",\"numpy==1.18.1\",\"tensorflow==2.2.0\",\"protobuf==3.20.1\"]}],\"name\":\"project_environment\"},\"BaseCondaEnvironment\":null},\"EnvironmentVariables\":{},\"Docker\":{\"BaseImage\":\"mcr.microsoft.com/azureml/openmpi4.1.0-ubuntu22.04\",\"Platform\":{\"Os\":\"Linux\",\"Architecture\":\"amd64\"},\"BaseDockerfile\":null,\"BaseImageRegistry\":{\"Address\":null,\"Username\":null,\"Password\":null},\"Enabled\":true},\"Spark\":{\"Repositories\":[],\"Packages\":[],\"PrecachePackages\":false},\"InferencingStackVersion\":null},\"History\":{\"OutputCollection\":true,\"DirectoriesToWatch\":null,\"EnableMLflowTracking\":false},\"Spark\":{\"Configuration\":{}},\"ParallelTask\":{\"MaxRetriesPerWorker\":0,\"WorkerCountPerNode\":1,\"TerminalExitCodes\":null,\"Configuration\":{}},\"BatchAi\":{\"NodeCount\":0},\"AmlCompute\":{\"Name\":null,\"VmSize\":null,\"RetainCluster\":false,\"ClusterMaxNodeCount\":null},\"AISuperComputer\":{\"InstanceType\":null,\"FrameworkImage\":null,\"ImageVersion\":null,\"Location\":null,\"AISuperComputerStorageData\":null,\"Interactive\":false,\"ScalePolicy\":null,\"VirtualClusterArmId\":null,\"TensorboardLogDirectory\":null,\"SSHPublicKey\":null},\"Tensorflow\":{\"WorkerCount\":0,\"ParameterServerCount\":0},\"Mpi\":{\"ProcessCountPerNode\":1},\"PyTorch\":{\"CommunicationBackend\":null,\"ProcessCount\":null},\"Hdi\":{\"YarnDeployMode\":0},\"ContainerInstance\":{\"Region\":null,\"CpuCores\":2.0,\"MemoryGb\":3.5},\"ExposedPorts\":null,\"Docker\":{\"UseDocker\":true,\"SharedVolumes\":true,\"ShmSize\":\"2g\",\"Arguments\":[]},\"Cmk8sCompute\":{\"Configuration\":{}},\"GlobalJobDispatcher\":{\"MyResourceOnly\":false,\"LowPriorityVMTolerant\":true},\"CommandReturnCodeConfig\":{\"ReturnCode\":0,\"SuccessfulReturnCodes\":[]},\"EnvironmentVariables\":{},\"ApplicationEndpoints\":{}},\"SnapshotId\":\"9558239b-6899-490d-abf5-341b540b8833\",\"Snapshots\":[],\"SourceCodeDataReference\":null,\"ParentRunId\":null,\"DataContainerId\":null,\"RunType\":null,\"Properties\":{},\"Tags\":{},\"AggregatedArtifactPath\":null}}}",
                        PrimaryMetricName = "validation_acc",
                        PrimaryMetricGoal = "maximize",
                        Arguments = new List<ArgumentAssignment>(),
                    }
                }
            };

            string outputAssetPath = null;
            if (useMacroInOutputAssetPath)
            {
                outputAssetPath = "azureml://datastores/${{default_datastore}}/paths/hyperdrive_command_test/${{name}}/model/${{output_name}}/";
            }
            else
            {
                outputAssetPath = "azureml://datastores/myblobdatastore/paths/hyperdrive_command_test/${{name}}/model/";
            }

            var outputType = AssetType.CustomModel;
            var inputSettings = new[]
           {
                 new InputSetting
                 {
                     Name = inputPortName,
                     DataStoreMode = DataStoreMode.Mount,
                 }
             };

            var outputSettings = new[]
            {
                new OutputSetting
                {
                    AssetOutputSettings = new AssetOutputSettings {
                        Path = outputAssetPath,
                        Type = outputType
                    },
                    Name = outputPortName,
                    DataStoreMode = DataStoreMode.Upload,
                    Overwrite = true
                }
            };

            IAetherDataSource datasource = await _testEnvironment.GetDataSourceAsync(datasetDefinition);

            IAetherModule module1 = await _testEnvironment.GetModuleAsync(_sweepWithAssetComponentId);
            IModuleNode moduleNode1 = dtcGraph.AddNode(module1, moduleInputSettings: inputSettings, moduleOutputSettings: outputSettings, cloudSettings: cloudSettings, regenerateOutput: true);
            // moduleNode1.Parameters["batch_size"].Value = "['choice', [[50,100,200]]]";
            // moduleNode1.Parameters["first_layer_neurons"].Value = "['choice', [[10, 50, 200, 300]]]";
            // moduleNode1.Parameters["second_layer_neurons"].Value = "['choice', [[10, 50, 200]]]";
            // moduleNode1.Parameters["learning_rate"].Value = "['loguniform', [-5, -1]]";
            moduleNode1.Parameters["batch_size"].Value = "100";
            moduleNode1.Parameters["first_layer_neurons"].Value = "10";
            moduleNode1.Parameters["second_layer_neurons"].Value = "10";
            moduleNode1.Parameters["learning_rate"].Value = "0.002";
            moduleNode1.SetDatastoreSetting(useGraphDefaultDatastore: useMacroInOutputAssetPath);

            IDataSourceNode sourceNode = dtcGraph.AddNode(datasource);
            dtcGraph.Connect(sourceNode, moduleNode1.InputPortDictionary[inputPortName]);

            if (useMacroInOutputAssetPath)
            {
                var datastoreSetting = new DatastoreSetting()
                {
                    DataStoreName = "myblobdatastore"
                };
                dtcGraph.AddDefaultDatastore(datastoreSetting);
            }

            IPipelineRun pipelineRun = await RunTestWithLogsAsync(dtcGraph, _testEnvironment, MinutesToWaitForPipelineToComplete, runHistoryExperimentName: "HyperdriveCommandComponentTest");
            Assert.AreEqual(PipelineRunStatusCode.Finished, pipelineRun.CachedStatus, $"PipelineRun id: {pipelineRun.Id}");
        }

        [Test]
        [Category("SkipRunnerTest")]
        public async Task RunEsCloudForScriptComponentWithHyperDriveConfigurationtOnBatchAI()
        {
            var dtcGraph = _testEnvironment.CreateNewGraph();
            var inputPortName = "data_folder";
            var outputPortName = "saved_model";

            var datasetDefinition = new DataSetDefinition()
            {
                DataTypeShortName = "path",
                Value = new DataSetDefinitionValue()
                {
                    DataSetReference = new RegisteredDataSetReference()
                    {
                        Id = _mnistDataset.DatasetId.ToString(),
                    }
                }
            };

            var cloudSettings = new CloudSettings
            {
                EsCloudConfig = new EsCloudConfiguration
                {
                    HyperDriveConfiguration = new HyperDriveConfiguration
                    {
                        HyperDriveRunConfig = "{\"user\":\"<EMAIL>\",\"platform\":\"AML\",\"max_total_jobs\":2,\"max_concurrent_jobs\":4,\"max_duration_minutes\":10080,\"primary_metric_config\":{\"name\":\"validation_acc\",\"goal\":\"maximize\"},\"generator_config\":{\"name\":\"RANDOM\",\"parameter_space\":{\"batch_size\":[\"choice\",[[10,20,30]]],\"first_layer_neurons\":[\"choice\",[[10,20,50]]],\"second_layer_neurons\":[\"choice\",[[10,20,30]]],\"learning_rate\":[\"loguniform\",[-5,-1]]}},\"policy_config\":{\"name\":\"Bandit\",\"properties\":{\"evaluation_interval\":1,\"delay_evaluation\":5,\"slack_factor\":0.1}},\"platform_config\":{\"ServiceAddress\":\"https://master.experiments.azureml-test.net\",\"SubscriptionId\":\"b8c23406-f9b5-4ccb-8a65-a8cb5dcd6a5a\",\"ResourceGroupName\":\"rge2etests\",\"WorkspaceName\":\"wse2etests\",\"ExperimentName\":null,\"Definition\":{\"Configuration\":null,\"Attribution\":null,\"TelemetryValues\":null,\"Overrides\":{\"Script\":\"mnist.py\",\"Command\":null,\"UseAbsolutePath\":false,\"Arguments\":[],\"SourceDirectoryDataStore\":null,\"Framework\":0,\"Communicator\":0,\"Target\":\"cpucluster\",\"DataReferences\":{},\"Data\":{},\"OutputData\":{},\"Datacaches\":[],\"JobName\":null,\"MaxRunDurationSeconds\":null,\"NodeCount\":1,\"Priority\":null,\"CredentialPassthrough\":false,\"Identity\":null,\"Environment\":{\"Python\":{\"InterpreterPath\":null,\"UserManagedDependencies\":false,\"CondaDependencies\":{\"channels\":[\"defaults\"],\"dependencies\":[\"python=3.7.9\",\"pip=20.0\",{\"pip\":[\"azureml-defaults\",\"azureml-dataprep>=1.6\",\"numpy==1.18.1\",\"tensorflow==2.2.0\",\"protobuf==3.20.1\"]}],\"name\":\"project_environment\"},\"BaseCondaEnvironment\":null},\"EnvironmentVariables\":{},\"Docker\":{\"BaseImage\":\"mcr.microsoft.com/azureml/openmpi4.1.0-ubuntu22.04\",\"Platform\":{\"Os\":\"Linux\",\"Architecture\":\"amd64\"},\"BaseDockerfile\":null,\"BaseImageRegistry\":{\"Address\":null,\"Username\":null,\"Password\":null},\"Enabled\":true},\"Spark\":{\"Repositories\":[],\"Packages\":[],\"PrecachePackages\":false},\"InferencingStackVersion\":null},\"History\":{\"OutputCollection\":true,\"DirectoriesToWatch\":null,\"EnableMLflowTracking\":false},\"Spark\":{\"Configuration\":{}},\"ParallelTask\":{\"MaxRetriesPerWorker\":0,\"WorkerCountPerNode\":1,\"TerminalExitCodes\":null,\"Configuration\":{}},\"BatchAi\":{\"NodeCount\":0},\"AmlCompute\":{\"Name\":null,\"VmSize\":null,\"RetainCluster\":false,\"ClusterMaxNodeCount\":null},\"AISuperComputer\":{\"InstanceType\":null,\"FrameworkImage\":null,\"ImageVersion\":null,\"Location\":null,\"AISuperComputerStorageData\":null,\"Interactive\":false,\"ScalePolicy\":null,\"VirtualClusterArmId\":null,\"TensorboardLogDirectory\":null,\"SSHPublicKey\":null},\"Tensorflow\":{\"WorkerCount\":0,\"ParameterServerCount\":0},\"Mpi\":{\"ProcessCountPerNode\":1},\"PyTorch\":{\"CommunicationBackend\":null,\"ProcessCount\":null},\"Hdi\":{\"YarnDeployMode\":0},\"ContainerInstance\":{\"Region\":null,\"CpuCores\":2.0,\"MemoryGb\":3.5},\"ExposedPorts\":null,\"Docker\":{\"UseDocker\":true,\"SharedVolumes\":true,\"ShmSize\":\"2g\",\"Arguments\":[]},\"Cmk8sCompute\":{\"Configuration\":{}},\"GlobalJobDispatcher\":{\"MyResourceOnly\":false,\"LowPriorityVMTolerant\":true},\"CommandReturnCodeConfig\":{\"ReturnCode\":0,\"SuccessfulReturnCodes\":[]},\"EnvironmentVariables\":{},\"ApplicationEndpoints\":{}},\"SnapshotId\":\"b8181c35-779c-484e-8030-732706d91bd5\",\"Snapshots\":[],\"SourceCodeDataReference\":null,\"ParentRunId\":null,\"DataContainerId\":null,\"RunType\":null,\"Properties\":{},\"Tags\":{},\"AggregatedArtifactPath\":null}}}",
                        PrimaryMetricName = "validation_acc",
                        PrimaryMetricGoal = "maximize",
                        Arguments = new List<ArgumentAssignment>
                        {
                            new ArgumentAssignment { Value = "--data_folder", ValueType = ArgumentValueType.Literal, },
                            new ArgumentAssignment { Value = inputPortName, ValueType = ArgumentValueType.Input, },
                            new ArgumentAssignment { Value = "--saved_model", ValueType = ArgumentValueType.Literal, },
                            new ArgumentAssignment { Value = outputPortName, ValueType = ArgumentValueType.Output, },
                        },
                    }
                }
            };

            var outputSettings = new[] { new OutputSetting
            {
                DataReferenceName = outputPortName,
                DatasetRegistration = new DatasetRegistration(),
                DatasetOutputOptions = new DatasetOutputOptions(),
                DataStoreMode = DataStoreMode.Upload,
                DataStoreName = datastoreName,
                Name = outputPortName,
                Overwrite = true
            } };

            IAetherDataSource datasource = await _testEnvironment.GetDataSourceAsync(datasetDefinition);

            IAetherModule module1 = await _testEnvironment.GetModuleAsync(_sweepWithScriptComponentId);
            IModuleNode moduleNode1 = dtcGraph.AddNode(module1, moduleOutputSettings: outputSettings, cloudSettings: cloudSettings, regenerateOutput: true);
            // moduleNode1.Parameters["batch_size"].Value = "['choice', [[50,100,200]]]";
            // moduleNode1.Parameters["first_layer_neurons"].Value = "['choice', [[10, 50, 200, 300]]]";
            // moduleNode1.Parameters["second_layer_neurons"].Value = "['choice', [[10, 50, 200]]]";
            // moduleNode1.Parameters["learning_rate"].Value = "['loguniform', [-5, -1]]";
            moduleNode1.Parameters["batch_size"].Value = "100";
            moduleNode1.Parameters["first_layer_neurons"].Value = "10";
            moduleNode1.Parameters["second_layer_neurons"].Value = "10";
            moduleNode1.Parameters["learning_rate"].Value = "0.002";

            IDataSourceNode sourceNode = dtcGraph.AddNode(datasource);
            dtcGraph.Connect(sourceNode, moduleNode1.InputPortDictionary[inputPortName]);

            IPipelineRun pipelineRun = await RunTestWithLogsAsync(dtcGraph, _testEnvironment, MinutesToWaitForPipelineToComplete, runHistoryExperimentName: "HyperdriveScriptComponentTest");
            Assert.AreEqual(PipelineRunStatusCode.Finished, pipelineRun.CachedStatus, $"PipelineRun id: {pipelineRun.Id}");
        }

        [Test]
        [Category("SkipRunnerTest")]
        public async Task RunEsCloudForScriptComponentWithHyperDriveConfigurationtOnKubernetes()
        {
            var dtcGraph = _testEnvironment.CreateNewGraph();
            var inputPortName = "data_folder";
            var outputPortName = "saved_model";

            var datasetDefinition = new DataSetDefinition()
            {
                DataTypeShortName = "path",
                Value = new DataSetDefinitionValue()
                {
                    DataSetReference = new RegisteredDataSetReference()
                    {
                        Id = _mnistDataset.DatasetId.ToString(),
                    }
                }
            };

            var cloudSettings = new CloudSettings
            {
                EsCloudConfig = new EsCloudConfiguration
                {
                    HyperDriveConfiguration = new HyperDriveConfiguration
                    {
                        HyperDriveRunConfig = "{\"user\":\"<EMAIL>\",\"platform\":\"AML\",\"max_total_jobs\":2,\"max_concurrent_jobs\":4,\"max_duration_minutes\":10080,\"primary_metric_config\":{\"name\":\"validation_acc\",\"goal\":\"maximize\"},\"generator_config\":{\"name\":\"RANDOM\",\"parameter_space\":{\"batch_size\":[\"choice\",[[10,20,30]]],\"first_layer_neurons\":[\"choice\",[[10,20,50]]],\"second_layer_neurons\":[\"choice\",[[10,20,30]]],\"learning_rate\":[\"loguniform\",[-5,-1]]}},\"policy_config\":{\"name\":\"Bandit\",\"properties\":{\"evaluation_interval\":1,\"delay_evaluation\":5,\"slack_factor\":0.1}},\"platform_config\":{\"ServiceAddress\":\"https://master.experiments.azureml-test.net\",\"SubscriptionId\":\"b8c23406-f9b5-4ccb-8a65-a8cb5dcd6a5a\",\"ResourceGroupName\":\"rge2etests\",\"WorkspaceName\":\"wse2etests\",\"ExperimentName\":null,\"Definition\":{\"Configuration\":null,\"Attribution\":null,\"TelemetryValues\":null,\"Overrides\":{\"Script\":\"mnist.py\",\"Command\":null,\"UseAbsolutePath\":false,\"Arguments\":[],\"SourceDirectoryDataStore\":null,\"Framework\":0,\"Communicator\":0,\"Target\":\"test-aks-large\",\"DataReferences\":{},\"Data\":{},\"OutputData\":{},\"Datacaches\":[],\"JobName\":null,\"MaxRunDurationSeconds\":null,\"NodeCount\":1,\"Priority\":null,\"CredentialPassthrough\":false,\"Identity\":null,\"Environment\":{\"Python\":{\"InterpreterPath\":null,\"UserManagedDependencies\":false,\"CondaDependencies\":{\"channels\":[\"defaults\"],\"dependencies\":[\"python=3.7.9\",\"pip=20.0\",{\"pip\":[\"azureml-defaults\",\"azureml-dataprep>=1.6\",\"numpy==1.18.1\",\"tensorflow==2.2.0\",\"protobuf==3.20.1\"]}],\"name\":\"project_environment\"},\"BaseCondaEnvironment\":null},\"EnvironmentVariables\":{},\"Docker\":{\"BaseImage\":\"mcr.microsoft.com/azureml/openmpi4.1.0-ubuntu22.04\",\"Platform\":{\"Os\":\"Linux\",\"Architecture\":\"amd64\"},\"BaseDockerfile\":null,\"BaseImageRegistry\":{\"Address\":null,\"Username\":null,\"Password\":null},\"Enabled\":true},\"Spark\":{\"Repositories\":[],\"Packages\":[],\"PrecachePackages\":false},\"InferencingStackVersion\":null},\"History\":{\"OutputCollection\":true,\"DirectoriesToWatch\":null,\"EnableMLflowTracking\":false},\"Spark\":{\"Configuration\":{}},\"ParallelTask\":{\"MaxRetriesPerWorker\":0,\"WorkerCountPerNode\":1,\"TerminalExitCodes\":null,\"Configuration\":{}},\"BatchAi\":{\"NodeCount\":0},\"AmlCompute\":{\"Name\":null,\"VmSize\":null,\"RetainCluster\":false,\"ClusterMaxNodeCount\":null},\"AISuperComputer\":{\"InstanceType\":null,\"FrameworkImage\":null,\"ImageVersion\":null,\"Location\":null,\"AISuperComputerStorageData\":null,\"Interactive\":false,\"ScalePolicy\":null,\"VirtualClusterArmId\":null,\"TensorboardLogDirectory\":null,\"SSHPublicKey\":null},\"Tensorflow\":{\"WorkerCount\":0,\"ParameterServerCount\":0},\"Mpi\":{\"ProcessCountPerNode\":1},\"PyTorch\":{\"CommunicationBackend\":null,\"ProcessCount\":null},\"Hdi\":{\"YarnDeployMode\":0},\"ContainerInstance\":{\"Region\":null,\"CpuCores\":2.0,\"MemoryGb\":3.5},\"ExposedPorts\":null,\"Docker\":{\"UseDocker\":true,\"SharedVolumes\":true,\"ShmSize\":\"2g\",\"Arguments\":[]},\"Cmk8sCompute\":{\"Configuration\":{}},\"GlobalJobDispatcher\":{\"MyResourceOnly\":false,\"LowPriorityVMTolerant\":true},\"CommandReturnCodeConfig\":{\"ReturnCode\":0,\"SuccessfulReturnCodes\":[]},\"EnvironmentVariables\":{},\"ApplicationEndpoints\":{}},\"SnapshotId\":\"b8181c35-779c-484e-8030-732706d91bd5\",\"Snapshots\":[],\"SourceCodeDataReference\":null,\"ParentRunId\":null,\"DataContainerId\":null,\"RunType\":null,\"Properties\":{},\"Tags\":{},\"AggregatedArtifactPath\":null}}}",
                        PrimaryMetricName = "validation_acc",
                        PrimaryMetricGoal = "maximize",
                        Arguments = new List<ArgumentAssignment>
                        {
                            new ArgumentAssignment { Value = "--data_folder", ValueType = ArgumentValueType.Literal, },
                            new ArgumentAssignment { Value = inputPortName, ValueType = ArgumentValueType.Input, },
                            new ArgumentAssignment { Value = "--saved_model", ValueType = ArgumentValueType.Literal, },
                            new ArgumentAssignment { Value = outputPortName, ValueType = ArgumentValueType.Output, },
                        },
                    },
                    ResourceConfig = new ResourceConfiguration
                    {
                        InstanceType = "cpularge"
                    }
                }
            };

            var outputSettings = new[] { new OutputSetting
            {
                DataReferenceName = outputPortName,
                DatasetRegistration = new DatasetRegistration(),
                DatasetOutputOptions = new DatasetOutputOptions(),
                DataStoreMode = DataStoreMode.Upload,
                DataStoreName = datastoreName,
                Name = outputPortName,
                Overwrite = true
            } };

            IAetherDataSource datasource = await _testEnvironment.GetDataSourceAsync(datasetDefinition);

            IAetherModule module1 = await _testEnvironment.GetModuleAsync(_sweepWithScriptComponentId);
            IModuleNode moduleNode1 = dtcGraph.AddNode(module1, moduleOutputSettings: outputSettings, cloudSettings: cloudSettings, regenerateOutput: true);
            // moduleNode1.Parameters["batch_size"].Value = "['choice', [[50,100,200]]]";
            // moduleNode1.Parameters["first_layer_neurons"].Value = "['choice', [[10, 50, 200, 300]]]";
            // moduleNode1.Parameters["second_layer_neurons"].Value = "['choice', [[10, 50, 200]]]";
            // moduleNode1.Parameters["learning_rate"].Value = "['loguniform', [-5, -1]]";
            moduleNode1.Parameters["batch_size"].Value = "100";
            moduleNode1.Parameters["first_layer_neurons"].Value = "10";
            moduleNode1.Parameters["second_layer_neurons"].Value = "10";
            moduleNode1.Parameters["learning_rate"].Value = "0.002";

            IDataSourceNode sourceNode = dtcGraph.AddNode(datasource);
            dtcGraph.Connect(sourceNode, moduleNode1.InputPortDictionary[inputPortName]);

            IPipelineRun pipelineRun = await RunTestWithLogsAsync(dtcGraph, _testEnvironment, MinutesToWaitForPipelineToComplete, runHistoryExperimentName: "HyperdriveScriptComponentTest");
            Assert.AreEqual(PipelineRunStatusCode.Finished, pipelineRun.CachedStatus, $"PipelineRun id: {pipelineRun.Id}");
        }

        #endregion

        private async Task<IGraph> CreateEsCloudGraphWithNodeDataStoreSettings(AetherEnvironment environment, string sourcePath)
        {
            var dtcGraph = environment.CreateNewGraph();

            //The datastores in the test are pre-created with the test project identity.
            var sourceId = await DataStoreUtils.CreateDataStoreDataSourceAsync(environment, "computedata", "computedata", datastoreName, "estestinput", identifierHash: Guid.NewGuid().ToString(), contentHash: null);

            string moduleId = await UploadESCloudModuleWithInputOutput(environment, sourcePath, false);

            var outputSettings = GetOutputUploadSettings();

            IAetherModule module = await environment.GetModuleAsync(moduleId);
            IModuleNode moduleNode1 = dtcGraph.AddNode(module, moduleOutputSettings: outputSettings);

            IAetherDataSource source = await environment.GetDataSourceAsync(sourceId);
            IDataSourceNode sourceNode = dtcGraph.AddNode(source);
            dtcGraph.Connect(sourceNode, moduleNode1.InputPortDictionary["SourceLocation"]);

            IModuleNode moduleNode2 = dtcGraph.AddNode(module, moduleOutputSettings: outputSettings);
            dtcGraph.Connect(moduleNode1.OutputPortDictionary[moduleOutputName], moduleNode2.InputPortDictionary["SourceLocation"]);

            return dtcGraph;
        }

        private async Task<IGraph> CreateEsCloudGraph(AetherEnvironment environment, string sourcePath, bool withPriority = false)
        {
            var dtcGraph = environment.CreateNewGraph();

            //The datastores in the test are pre-created with the test project identity.
            var sourceId = await DataStoreUtils.CreateDataStoreDataSourceAsync(environment, "computedata", "computedata", datastoreName, "estestinput", identifierHash: Guid.NewGuid().ToString(), contentHash: null);

            string moduleId = await UploadESCloudModuleWithInputOutput(environment, sourcePath, true);

            IAetherModule module = await environment.GetModuleAsync(moduleId);
            IModuleNode moduleNode1 = dtcGraph.AddNode(module);

            IAetherDataSource source = await environment.GetDataSourceAsync(sourceId);
            IDataSourceNode sourceNode = dtcGraph.AddNode(source);
            dtcGraph.Connect(sourceNode, moduleNode1.InputPortDictionary["SourceLocation"]);

            CloudSettings cloudSettings = null;
            if (withPriority)
            {
                cloudSettings = new CloudSettings
                {
                    PriorityConfig = new PriorityConfiguration { CloudPriority = 500, StringTypePriority = "low" },
                };
            }
            IModuleNode moduleNode2 = dtcGraph.AddNode(module, cloudSettings: cloudSettings);
            dtcGraph.Connect(moduleNode1.OutputPortDictionary[moduleOutputName], moduleNode2.InputPortDictionary["SourceLocation"]);

            return dtcGraph;
        }

        private async Task<IGraph> CreateEsCloudGraphWithInitFinalPhases(AetherEnvironment environment, string sourcePath, bool withPriority = false)
        {
            var dtcGraph = environment.CreateNewGraph();

            //The datastores in the test are pre-created with the test project identity.
            var sourceId = await DataStoreUtils.CreateDataStoreDataSourceAsync(environment, "computedata", "computedata", datastoreName, "estestinput", identifierHash: Guid.NewGuid().ToString(), contentHash: null);

            string moduleId = await UploadESCloudModuleWithInputOutput(environment, sourcePath, true);

            IAetherModule module = await environment.GetModuleAsync(moduleId);

            // Add initialization module.
            var initializationNode = dtcGraph.AddNode(module);
            dtcGraph.ModuleNodes.First(n => n.Id == initializationNode.Id).ExecutionPhase = ExecutionPhase.Initialization;

            IModuleNode moduleNode1 = dtcGraph.AddNode(module);
            IAetherDataSource source = await environment.GetDataSourceAsync(sourceId);
            IDataSourceNode sourceNode = dtcGraph.AddNode(source);

            // Connect data node to moduleNode1 and initialization node.
            dtcGraph.Connect(sourceNode, moduleNode1.InputPortDictionary["SourceLocation"]);
            dtcGraph.Connect(sourceNode, initializationNode.InputPortDictionary["SourceLocation"]);

            CloudSettings cloudSettings = null;
            if (withPriority)
            {
                cloudSettings = new CloudSettings
                {
                    PriorityConfig = new PriorityConfiguration { CloudPriority = 500 },
                };
            }
            IModuleNode moduleNode2 = dtcGraph.AddNode(module, cloudSettings: cloudSettings);
            dtcGraph.Connect(moduleNode1.OutputPortDictionary[moduleOutputName], moduleNode2.InputPortDictionary["SourceLocation"]);

            // Add finalization module.
            var finalizationNode = dtcGraph.AddNode(module);
            dtcGraph.ModuleNodes.First(n => n.Id == finalizationNode.Id).ExecutionPhase = ExecutionPhase.Finalization;
            // Connect data node to finalization node.
            dtcGraph.Connect(sourceNode, finalizationNode.InputPortDictionary["SourceLocation"]);

            return dtcGraph;
        }

        private async Task<IGraph> CreateEsCloudGraphWithDatasetInputOutput(
            AetherEnvironment environment,
            string sourcePath,
            DatasetDto dataset,
            string computeTargetName,
            string computeTargetType,
            Script script,
            bool dockerEnabled = true,
            DataStoreMode datastoreMode = DataStoreMode.Mount,
            DatasetOutputOptions datasetOutputOptions = null)
        {
            var dtcGraph = environment.CreateNewGraph();

            var datasetDefinition = new DataSetDefinition()
            {
                DataTypeShortName = "AzureBlobReference",
                Value = new DataSetDefinitionValue()
                {
                    DataSetReference = new RegisteredDataSetReference()
                    {
                        Id = dataset.DatasetId.ToString(),
                    }
                }
            };
            var outputSettings = new[] { new OutputSetting
            {
                DataReferenceName = "out",
                DatasetRegistration = new DatasetRegistration(),
                DataStoreMode = DataStoreMode.Mount,
                DataStoreName = datastoreName,
                Name = moduleOutputName,
                Overwrite = true
            } };

            outputSettings[0].DatasetOutputOptions = datasetOutputOptions;

            IAetherDataSource datasource = await _testEnvironment.GetDataSourceAsync(datasetDefinition);
            string moduleId = await UploadESCloudNodeWithDataSetInput(
                environment,
                sourcePath,
                computeTargetName,
                computeTargetType,
                script,
                dockerEnabled,
                datastoreMode,
                addDatasetOutput: true);
            string moduleId2 = await UploadESCloudNodeWithDataSetInput(
                environment,
                sourcePath,
                computeTargetName,
                computeTargetType,
                script,
                dockerEnabled,
                datastoreMode,
                addDatasetOutput: true);

            IAetherModule module = await environment.GetModuleAsync(moduleId);
            IModuleNode moduleNode1 = dtcGraph.AddNode(module, moduleOutputSettings: outputSettings);

            IDataSourceNode sourceNode = dtcGraph.AddNode(datasource);
            dtcGraph.Connect(sourceNode, moduleNode1.InputPortDictionary[moduleInputName]);

            IAetherModule module2 = await environment.GetModuleAsync(moduleId2);
            IModuleNode moduleNode2 = dtcGraph.AddNode(module2, moduleOutputSettings: outputSettings);
            dtcGraph.Connect(moduleNode1.OutputPortDictionary[moduleOutputName], moduleNode2.InputPortDictionary[moduleInputName]);

            return dtcGraph;
        }

        private async Task<IGraph> CreateEsCloudGraphWithArtifactOutput()
        {
            var inputAssetId = _uriFolderAsset;
            var module1InputType = AssetType.UriFolder;
            var module1OutputType = AssetType.MLTable;
            var module1OutputAssetPath = "azureml://datastores/myblobdatastore/paths/asset_multi_nodes_output/module1_output/";
            var module2InputType = AssetType.UriFolder;
            var module2OutputType = AssetType.CustomModel;
            var module2OutputAssetPath = "azureml://datastores/myblobdatastore/paths/asset_multi_nodes_output/module2_output_${{name}}/";

            var datasetDefinition = new DataSetDefinition()
            {
                DataTypeShortName = ConvertToDataTypeId(module1InputType),
                Value = new DataSetDefinitionValue()
                {
                    AssetDefinition = new AssetDefinition
                    {
                        AssetId = AssetId.Parse(inputAssetId),
                        SerializedAssetId = inputAssetId,
                        Path = inputAssetId,
                        Type = module1InputType
                    }
                }
            };

            var module1InputSettings = GetAssetInputSetting(module1InputType);

            var module1OutputSettings = new[] {
                new OutputSetting
                {
                    AssetOutputSettings = new AssetOutputSettings {
                        Path = module1OutputAssetPath,
                        Type = module1OutputType
                    },
                    Name = moduleOutputName,
                    DataStoreMode = DataStoreMode.Mount,
                    Overwrite = true
                },
                new OutputSetting
                {
                    DataReferenceName = artifactOutputName,
                    DatasetRegistration = new DatasetRegistration{},
                    DatasetOutputOptions = new DatasetOutputOptions(),
                    DataStoreMode = DataStoreMode.Mount,
                    DataStoreName = datastoreName,
                    Name = artifactOutputName,
                    Overwrite = true
                },
            };

            var module1RunConfig = GetAssetTestRunConfiguration(module1OutputType);

            var module2InputSettings = GetAssetInputSetting(module2InputType);

            var module2OutputSettings = new[] {
                new OutputSetting
                {
                    AssetOutputSettings = new AssetOutputSettings {
                        Path = module2OutputAssetPath,
                        Type = module2OutputType
                    },
                    Name = moduleOutputName,
                    DataStoreMode = DataStoreMode.Upload,
                    Overwrite = true
                },
                new OutputSetting
                {
                    DataReferenceName = artifactOutputName,
                    DatasetRegistration = new DatasetRegistration{ Name = "ArtifactTestDataset1", CreateNewVersion = true },
                    DatasetOutputOptions = new DatasetOutputOptions(),
                    DataStoreMode = DataStoreMode.Mount,
                    DataStoreName = datastoreName,
                    Name = artifactOutputName,
                    Overwrite = true
                },
            };

            var module2RunConfig = GetAssetTestRunConfiguration(module2OutputType);

            var graph = _testEnvironment.CreateNewGraph();
            IAetherDataSource datasource = await _testEnvironment.GetDataSourceAsync(datasetDefinition);

            string module1Id = await UploadESCloudNodeWithDataSetInput(
                _testEnvironment,
                sourcePath,
                _testConfig.AmlComputeName,
                amlComputeType,
                script: assetInputOutputScript,
                dockerEnabled: true,
                datastoreMode: DataStoreMode.Mount,
                supportArtifact: true,
                addAssetOutput: true,
                inputDataTypeId: ConvertToDataTypeId(module1InputType),
                outputDataTypeId: ConvertToDataTypeId(module1OutputType),
                moduleDisplayName: "UriFolder In MLTable Out",
                moduleName: "escloud asset test module");

            string module2Id = await UploadESCloudNodeWithDataSetInput(
                _testEnvironment,
                sourcePath,
                _testConfig.AmlComputeName,
                amlComputeType,
                script: assetInputOutputScript,
                dockerEnabled: true,
                datastoreMode: DataStoreMode.Mount,
                supportArtifact: true,
                addAssetOutput: true,
                inputDataTypeId: ConvertToDataTypeId(module2InputType),
                outputDataTypeId: ConvertToDataTypeId(module2OutputType),
                moduleDisplayName: "MLTable In MLModel Out",
                moduleName: "escloud asset test module");

            IAetherModule module1 = await _testEnvironment.GetModuleAsync(module1Id);
            IAetherModule module2 = await _testEnvironment.GetModuleAsync(module2Id);
            IModuleNode moduleNode1 = graph.AddNode(module1, moduleInputSettings: module1InputSettings, moduleOutputSettings: module1OutputSettings, runConfig: SerializationHelpers.SerializeEntity(module1RunConfig));
            IModuleNode moduleNode2 = graph.AddNode(module2, moduleInputSettings: module2InputSettings, moduleOutputSettings: module2OutputSettings, runConfig: SerializationHelpers.SerializeEntity(module2RunConfig));

            SetArgumentsByInputAndOutput(moduleNode1);
            SetArgumentsByInputAndOutput(moduleNode2);

            IDataSourceNode sourceNode = graph.AddNode(datasource);
            graph.Connect(sourceNode, moduleNode1.InputPortDictionary[moduleInputName]);
            graph.Connect(moduleNode1.OutputPortDictionary[artifactOutputName], moduleNode2.InputPortDictionary[moduleInputName]);

            return graph;
        }

        private async Task<IGraph> CreateEsCloudGraphForDataTypeTest(AetherEnvironment environment, string sourcePath)
        {
            var dtcGraph = environment.CreateNewGraph();

            //The datastores in the test are pre-created with the test project identity.
            var sourceId = await DataStoreUtils.CreateDataStoreDataSourceAsync(environment, "computedata", "computedata", datastoreName, "estestinput", identifierHash: Guid.NewGuid().ToString(), contentHash: null);

            string moduleId1 = await UploadESCloudModuleWithInputOutput(environment, sourcePath, true, inputType: "AzureBlobReference", outputType: "UnknownDirectory");
            IAetherModule module1 = await environment.GetModuleAsync(moduleId1);
            IModuleNode moduleNode1 = dtcGraph.AddNode(module1);

            IAetherDataSource source = await environment.GetDataSourceAsync(sourceId);
            IDataSourceNode sourceNode = dtcGraph.AddNode(source);
            dtcGraph.Connect(sourceNode, moduleNode1.InputPortDictionary["SourceLocation"]);

            string moduleId2 = await UploadESCloudModuleWithInputOutput(environment, sourcePath, true, inputType: "UnknownDirectory", outputType: "AnyDirectory");
            IAetherModule module2 = await environment.GetModuleAsync(moduleId2);
            IModuleNode moduleNode2 = dtcGraph.AddNode(module2);
            dtcGraph.Connect(moduleNode1.OutputPortDictionary[moduleOutputName], moduleNode2.InputPortDictionary["SourceLocation"]);

            return dtcGraph;
        }

        private async Task<string> UploadESCloudSimpleModuleWithInput(AetherEnvironment environment, string sourcePath, bool isDeterministic = false, string inputType = null)
        {
            string computeTargetName = _testConfig.AmlComputeName;

            var structuredInterface = new StructuredInterface
            {
                Inputs = new List<StructuredInterfaceInput>
                {
                    new StructuredInterfaceInput { Name = "SourceLocation", DataTypeIdsList = new List<string>{ inputType ?? "AzureBlobReference" }, DataStoreMode = DataStoreMode.Download, Overwrite = true, DataReferenceName = "in"  },
                },

                MetadataParameters = new List<StructuredInterfaceParameter>
                {
                    new StructuredInterfaceParameter { Name = "TargetType" , DefaultValue = "MLC", ParameterType = ParameterType.String, IsOptional = false},
                },
                Parameters = new List<StructuredInterfaceParameter>
                {
                    new StructuredInterfaceParameter { Name = "Target" , DefaultValue = computeTargetName, ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "MLCComputeType" , DefaultValue = amlComputeType, ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "PrepareEnvironment" , DefaultValue = "true", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "GenerateJson" , DefaultValue = "true", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "Script" , DefaultValue = "main.py", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "Arguments" , DefaultValue = "--in,$AZUREML_DATAREFERENCE_in", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "Framework" , DefaultValue = "Python", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "maxRunDurationSeconds" , DefaultValue = "1200", ParameterType = ParameterType.String, IsOptional = true},
                    new StructuredInterfaceParameter { Name = "InterpreterPath" , DefaultValue = "python", ParameterType = ParameterType.String, IsOptional = true},
                    new StructuredInterfaceParameter { Name = "UserManagedDependencies" , DefaultValue = "false", ParameterType = ParameterType.String, IsOptional = true},
                    new StructuredInterfaceParameter { Name = "CondaDependencies" , DefaultValue = GetCondaSetup(), ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "DockerEnabled" , DefaultValue = "true", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "BaseDockerImage" , DefaultValue = OriginalDockerImage, ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "SharedVolumes" , DefaultValue = "true", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "NodeCount" , DefaultValue = "1", ParameterType = ParameterType.String, IsOptional = true},
                    new StructuredInterfaceParameter { Name = "ContainerInstanceRegion" , DefaultValue = "None", ParameterType = ParameterType.String, IsOptional = true},
                    new StructuredInterfaceParameter { Name = "ContainerInstanceCpuCores" , DefaultValue = "1", ParameterType = ParameterType.String, IsOptional = true},
                    new StructuredInterfaceParameter { Name = "ContainerInstanceMemoryGb" , DefaultValue = "4", ParameterType = ParameterType.String, IsOptional = true},
                },
            };
            var moduleUploadInfo = new ModuleUploadInfo(
               name: "escloud test module no output",
               displayName: "escloud test module no output display",
               description: "escloud test module no output",
               isDeterministic: isDeterministic,
               cloudSystem: "escloud",
               structuredInterface: structuredInterface,
               moduleTypeVersion: "1.0");

            return await environment.UploadModuleAsync(sourcePath, moduleUploadInfo);
        }


        private async Task<string> UploadESCloudModuleWithInputOutput(AetherEnvironment environment, string sourcePath, bool addOutputDatastoreSetting, bool isDeterministic = false, string inputType = null, string outputType = null)
        {
            string computeTargetName = _testConfig.AmlComputeName;

            var structuredInterface = new StructuredInterface
            {
                Inputs = new List<StructuredInterfaceInput>
                {
                    new StructuredInterfaceInput { Name = "SourceLocation", DataTypeIdsList = new List<string>{ inputType ?? "AzureBlobReference" }, DataStoreMode = DataStoreMode.Download, Overwrite = true, DataReferenceName = "in"  },
                },
                Outputs = new List<StructuredInterfaceOutput>
                {
                    addOutputDatastoreSetting ?
                        new StructuredInterfaceOutput { Name = moduleOutputName, DataTypeId = outputType ?? "AzureBlobReference", DataStoreName = datastoreName, DataStoreMode = DataStoreMode.Mount, Overwrite = true, DataReferenceName = "out" } :
                        new StructuredInterfaceOutput { Name = moduleOutputName, DataTypeId = outputType ?? "AzureBlobReference" },
                },
                MetadataParameters = new List<StructuredInterfaceParameter>
                {
                    new StructuredInterfaceParameter { Name = "TargetType" , DefaultValue = "MLC", ParameterType = ParameterType.String, IsOptional = false},
                },
                Parameters = new List<StructuredInterfaceParameter>
                {
                    new StructuredInterfaceParameter { Name = "Target" , DefaultValue = computeTargetName, ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "MLCComputeType" , DefaultValue = amlComputeType, ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "PrepareEnvironment" , DefaultValue = "true", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "GenerateJson" , DefaultValue = "true", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "Script" , DefaultValue = "wordCount.py", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "Arguments" , DefaultValue = "--in,$AZUREML_DATAREFERENCE_in,--out,$AZUREML_DATAREFERENCE_out", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "Framework" , DefaultValue = "Python", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "maxRunDurationSeconds" , DefaultValue = "1200", ParameterType = ParameterType.String, IsOptional = true},
                    new StructuredInterfaceParameter { Name = "InterpreterPath" , DefaultValue = "python", ParameterType = ParameterType.String, IsOptional = true},
                    new StructuredInterfaceParameter { Name = "UserManagedDependencies" , DefaultValue = "false", ParameterType = ParameterType.String, IsOptional = true},
                    new StructuredInterfaceParameter { Name = "CondaDependencies" , DefaultValue = GetCondaSetup(), ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "DockerEnabled" , DefaultValue = "true", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "BaseDockerImage" , DefaultValue = OriginalDockerImage, ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "SharedVolumes" , DefaultValue = "true", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "NodeCount" , DefaultValue = "1", ParameterType = ParameterType.String, IsOptional = true},
                    new StructuredInterfaceParameter { Name = "ContainerInstanceRegion" , DefaultValue = "None", ParameterType = ParameterType.String, IsOptional = true},
                    new StructuredInterfaceParameter { Name = "ContainerInstanceCpuCores" , DefaultValue = "1", ParameterType = ParameterType.String, IsOptional = true},
                    new StructuredInterfaceParameter { Name = "ContainerInstanceMemoryGb" , DefaultValue = "4", ParameterType = ParameterType.String, IsOptional = true},
                },
            };
            var moduleUploadInfo = new ModuleUploadInfo(
               name: "escloud IO test module",
               displayName: "escloud IO test module display",
               description: "escloud IO test module",
               isDeterministic: isDeterministic,
               cloudSystem: "escloud",
               structuredInterface: structuredInterface,
               moduleTypeVersion: "1.0");

            return await environment.UploadModuleAsync(sourcePath, moduleUploadInfo);
        }

        private static List<OutputSetting> GetOutputUploadSettings()
        {
            return new List<OutputSetting> {
                new OutputSetting {
                    Name = moduleOutputName,
                    DataStoreName = datastoreName,
                    DataStoreMode = DataStoreMode.Upload,
                    Overwrite = true,
                    DataReferenceName = "out",
                }
            };
        }

        private async Task<IGraph> CreateEsCloudGraphWithDataSetNode(
            AetherEnvironment environment,
            string sourcePath,
            DatasetDto dataset,
            string computeTargetName,
            string computeTargetType,
            Script script,
            bool dockerEnabled = true,
            DataStoreMode datastoreMode = DataStoreMode.Mount,
            InputSetting inputSetting = null,
            CloudSettings cloudSettings = null)
        {
            var graph = environment.CreateNewGraph();
            var datasetDefinition = new DataSetDefinition()
            {
                DataTypeShortName = "AzureBlobReference",
                Value = new DataSetDefinitionValue()
                {
                    DataSetReference = new RegisteredDataSetReference()
                    {
                        Id = dataset.DatasetId.ToString(),
                    }
                }
            };
            var moduleInputSettings = inputSetting == null ? null : new[] { inputSetting };

            IAetherDataSource datasource = await _testEnvironment.GetDataSourceAsync(datasetDefinition);
            var datasourceNode = graph.AddNode(datasource);

            var moduleId = await UploadESCloudNodeWithDataSetInput(
                environment,
                sourcePath,
                computeTargetName,
                computeTargetType,
                script,
                dockerEnabled,
                datastoreMode);
            IAetherModule module = await environment.GetModuleAsync(moduleId);
            var moduleNode = graph.AddNode(module, moduleInputSettings: moduleInputSettings, cloudSettings: cloudSettings);
            graph.Connect(datasourceNode, moduleNode.InputPortDictionary[moduleInputName]);
            return graph;
        }

        private async Task<string> UploadESCloudNodeWithDataSetInput(
            AetherEnvironment environment,
            string sourcePath,
            string computeTargetName,
            string computeTargetType,
            Script script,
            bool dockerEnabled,
            DataStoreMode datastoreMode,
            bool addDatasetOutput = false,
            bool supportArtifact = false,
            bool addAssetOutput = false,
            string inputDataTypeId = "AzureBlobReference",
            string outputDataTypeId = "AzureBlobReference",
            string moduleDisplayName = "escloud dataset test module display",
            string moduleName = "escloud dataset test module")
        {
            var outputs = !addDatasetOutput ? null : new List<StructuredInterfaceOutput>
            {
                new StructuredInterfaceOutput
                {
                    Name = moduleOutputName,
                    DataTypeId = outputDataTypeId,
                    DataStoreName = datastoreName,
                    DataStoreMode = DataStoreMode.Mount,
                    Overwrite = true,
                    DataReferenceName = "out",
                    DatasetOutput = new DatasetOutput(),
                }
            };

            if (addAssetOutput)
            {
                outputs = new List<StructuredInterfaceOutput>
                {
                    new StructuredInterfaceOutput
                    {
                        Name = moduleOutputName,
                        DataTypeId = outputDataTypeId,
                        DataStoreName = datastoreName,
                        DataStoreMode = datastoreMode,
                        Overwrite = true,
                        DataReferenceName = "out",
                    }
                };
            }
            if (supportArtifact)
            {
                outputs ??= new List<StructuredInterfaceOutput>();
                outputs.Add(new StructuredInterfaceOutput
                {
                    Name = artifactOutputName,
                    DataTypeId = "uri_folder",
                    DataStoreName = datastoreName,
                    DataStoreMode = DataStoreMode.Mount,
                    Overwrite = true,
                    DataReferenceName = artifactOutputName,
                    DatasetOutput = new DatasetOutput(),
                    IsArtifact = true,
                });
            }

            var structuredInterface = new StructuredInterface
            {
                Inputs = new List<StructuredInterfaceInput>
                {
                    new StructuredInterfaceInput { Name = moduleInputName, DataTypeIdsList = new List<string> { inputDataTypeId }, DataStoreMode = datastoreMode, DataReferenceName = dataReferenceName },
                },
                Outputs = outputs,
                MetadataParameters = new List<StructuredInterfaceParameter>
                {
                    new StructuredInterfaceParameter { Name = "TargetType" , DefaultValue = "MLC", ParameterType = ParameterType.String, IsOptional = false},
                },
                Parameters = new List<StructuredInterfaceParameter>
                {
                    new StructuredInterfaceParameter { Name = "Target" , DefaultValue = computeTargetName, ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "MLCComputeType" , DefaultValue = computeTargetType, ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "PrepareEnvironment" , DefaultValue = "true", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "GenerateJson" , DefaultValue = "true", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "Script" , DefaultValue = script.Name, ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "Arguments" , DefaultValue = "--in,$AZUREML_DATAREFERENCE_input,--out,$AZUREML_DATAREFERENCE_out", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "Framework" , DefaultValue = "Python", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "maxRunDurationSeconds" , DefaultValue = "1200", ParameterType = ParameterType.String, IsOptional = true},
                    new StructuredInterfaceParameter { Name = "InterpreterPath" , DefaultValue = "python", ParameterType = ParameterType.String, IsOptional = true},
                    new StructuredInterfaceParameter { Name = "UserManagedDependencies" , DefaultValue = "false", ParameterType = ParameterType.String, IsOptional = true},
                    new StructuredInterfaceParameter { Name = "CondaDependencies" , DefaultValue = GetCondaSetup(script.IncludePandas), ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "DockerEnabled" , DefaultValue = dockerEnabled.ToString().ToLowerInvariant(), ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "BaseDockerImage" , DefaultValue = OriginalDockerImage, ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "SharedVolumes" , DefaultValue = "true", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "NodeCount" , DefaultValue = "1", ParameterType = ParameterType.String, IsOptional = true},
                    new StructuredInterfaceParameter { Name = "ContainerInstanceRegion" , DefaultValue = "None", ParameterType = ParameterType.String, IsOptional = true},
                    new StructuredInterfaceParameter { Name = "ContainerInstanceCpuCores" , DefaultValue = "1", ParameterType = ParameterType.String, IsOptional = true},
                    new StructuredInterfaceParameter { Name = "ContainerInstanceMemoryGb" , DefaultValue = "4", ParameterType = ParameterType.String, IsOptional = true},
                },
            };
            var moduleUploadInfo = new ModuleUploadInfo(
               name: moduleName,
               displayName: moduleDisplayName,
               description: "escloud dataset test module",
               isDeterministic: false,
               cloudSystem: "escloud",
               structuredInterface: structuredInterface,
               moduleTypeVersion: "1.0");

            return await environment.UploadModuleAsync(sourcePath, moduleUploadInfo);
        }
        #endregion


        private async Task<string> UploadESCloudNodeUsingInlineEnvironmentInRunConfig(
            AetherEnvironment environment,
            string sourcePath)
        {
            var structuredInterface = new StructuredInterface
            {

                MetadataParameters = new List<StructuredInterfaceParameter>
                {
                    new StructuredInterfaceParameter { Name = "TargetType" , DefaultValue = "MLC", ParameterType = ParameterType.String, IsOptional = false},
                },
                Parameters = new List<StructuredInterfaceParameter>
                {
                    new StructuredInterfaceParameter { Name = "Target" , DefaultValue = _testConfig.AmlComputeName, ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "MLCComputeType" , DefaultValue = "AmlCompute", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "PrepareEnvironment" , DefaultValue = "true", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "Script" , DefaultValue = "simple_count.py", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "Framework" , DefaultValue = "Python", ParameterType = ParameterType.String, IsOptional = false},
                },
            };
            var runConfig = new ExecutionContracts.RunConfiguration
            {
                Environment = new EnvironmentDefinition
                {
                    Docker = new DockerSection
                    {
                        BaseImage = new DockerImageName(OriginalDockerImage),
                    },
                    Python = new PythonSection
                    {
                        CondaDependencies = JsonConvert.DeserializeObject<JToken>(GetCondaSetup()),
                    }
                }
            };

            var moduleUploadInfo = new ModuleUploadInfo(
               name: "escloud inline environment module",
               displayName: "escloud inline environment module display",
               description: "escloud inline environment module",
               isDeterministic: false,
               cloudSystem: "escloud",
               structuredInterface: structuredInterface,
               moduleTypeVersion: "1.0",
               runconfig: SerializationHelpers.SerializeEntity(runConfig)
               );

            return await environment.UploadModuleAsync(sourcePath, moduleUploadInfo);
        }

        private async Task<string> UploadESCloudNodeUsingInlineEnvironmentInEsCloudConfig(
            AetherEnvironment environment,
            string sourcePath)
        {
            var structuredInterface = new StructuredInterface
            {

                MetadataParameters = new List<StructuredInterfaceParameter>
                {
                    new StructuredInterfaceParameter { Name = "TargetType" , DefaultValue = "MLC", ParameterType = ParameterType.String, IsOptional = false},
                },
                Parameters = new List<StructuredInterfaceParameter>
                {
                    new StructuredInterfaceParameter { Name = "Target" , DefaultValue = _testConfig.AmlComputeName, ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "MLCComputeType" , DefaultValue = "AmlCompute", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "PrepareEnvironment" , DefaultValue = "true", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "Script" , DefaultValue = "simple_count.py", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "Framework" , DefaultValue = "Python", ParameterType = ParameterType.String, IsOptional = false},
                },
            };

            var runConfig = new ExecutionContracts.RunConfiguration
            {
                Environment = new EnvironmentDefinition
                {
                    Docker = new DockerSection
                    {
                        BaseImage = new DockerImageName(OriginalDockerImage),
                    },
                    Python = new PythonSection
                    {
                        CondaDependencies = JsonConvert.DeserializeObject<JToken>(GetCondaSetup()),
                    }
                }
            };

            var cloudSettings = new CloudSettings
            {
                EsCloudConfig = new EsCloudConfiguration
                {
                    Environment = new EnvironmentConfiguration
                    {
                        UseEnvironmentDefinition = true,
                        EnvironmentDefinitionString = JsonConvert.SerializeObject(new EnvironmentDefinition
                        {
                            Docker = new DockerSection
                            {
                                BaseImage = new DockerImageName(OriginalDockerImage),
                            },
                            Python = new PythonSection
                            {
                                CondaDependencies = JsonConvert.DeserializeObject<JToken>(GetCondaSetup()),
                            }
                        })
                    }
                }
            };

            var moduleUploadInfo = new ModuleUploadInfo(
               name: "escloud inline environment module",
               displayName: "escloud inline environment module display",
               description: "escloud inline environment module",
               isDeterministic: false,
               cloudSystem: "escloud",
               structuredInterface: structuredInterface,
               moduleTypeVersion: "1.0",
               cloudSettings: cloudSettings,
               runconfig: SerializationHelpers.SerializeEntity(runConfig)
               );

            return await environment.UploadModuleAsync(sourcePath, moduleUploadInfo);
        }

        private async Task<string> UploadESCloudNodeUsingNamedEnvironmentInRunConfig(
            AetherEnvironment environment,
            string sourcePath)
        {
            var structuredInterface = new StructuredInterface
            {

                MetadataParameters = new List<StructuredInterfaceParameter>
                {
                    new StructuredInterfaceParameter { Name = "TargetType" , DefaultValue = "MLC", ParameterType = ParameterType.String, IsOptional = false},
                },
                Parameters = new List<StructuredInterfaceParameter>
                {
                    new StructuredInterfaceParameter { Name = "Target" , DefaultValue = _testConfig.AmlComputeName, ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "MLCComputeType" , DefaultValue = "AmlCompute", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "PrepareEnvironment" , DefaultValue = "true", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "Script" , DefaultValue = "simple_count.py", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "Framework" , DefaultValue = "Python", ParameterType = ParameterType.String, IsOptional = false},
                },
            };


            var runConfig = "{\"Environment\": {\"Name\": \"AzureML-minimal-ubuntu18.04-py37-cpu-inference\", \"Version\": \"25\", \"ContainsDefinition\":false}}";

            var moduleUploadInfo = new ModuleUploadInfo(
               name: "escloud named environment module",
               displayName: "escloud named environment module display",
               description: "escloud named environment module",
               isDeterministic: false,
               cloudSystem: "escloud",
               structuredInterface: structuredInterface,
               moduleTypeVersion: "1.0",
               runconfig: runConfig
               );

            return await environment.UploadModuleAsync(sourcePath, moduleUploadInfo);
        }

        private async Task<string> UploadESCloudNodeUsingNamedEnvironmentWithoutVersionInRunConfig(
            AetherEnvironment environment,
            string sourcePath)
        {
            var structuredInterface = new StructuredInterface
            {

                MetadataParameters = new List<StructuredInterfaceParameter>
                {
                    new StructuredInterfaceParameter { Name = "TargetType" , DefaultValue = "MLC", ParameterType = ParameterType.String, IsOptional = false},
                },
                Parameters = new List<StructuredInterfaceParameter>
                {
                    new StructuredInterfaceParameter { Name = "Target" , DefaultValue = _testConfig.AmlComputeName, ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "MLCComputeType" , DefaultValue = "AmlCompute", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "PrepareEnvironment" , DefaultValue = "true", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "Script" , DefaultValue = "simple_count.py", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "Framework" , DefaultValue = "Python", ParameterType = ParameterType.String, IsOptional = false},
                },
            };


            var runConfig = "{\"Environment\": {\"Name\": \"" + EnvironmentNameWithoutVersion + "\", \"ContainsDefinition\":false}}";

            var moduleUploadInfo = new ModuleUploadInfo(
               name: "escloud named environment module",
               displayName: "escloud named environment module display",
               description: "escloud named environment module",
               isDeterministic: false,
               cloudSystem: "escloud",
               structuredInterface: structuredInterface,
               moduleTypeVersion: "1.0",
               runconfig: runConfig
               );

            return await environment.UploadModuleAsync(sourcePath, moduleUploadInfo);
        }

        private async Task<string> UploadESCloudNodeUsingNamedEnvironmentWithNullVersionInRunConfig(
            AetherEnvironment environment,
            string sourcePath)
        {
            var structuredInterface = new StructuredInterface
            {

                MetadataParameters = new List<StructuredInterfaceParameter>
                {
                    new StructuredInterfaceParameter { Name = "TargetType" , DefaultValue = "MLC", ParameterType = ParameterType.String, IsOptional = false},
                },
                Parameters = new List<StructuredInterfaceParameter>
                {
                    new StructuredInterfaceParameter { Name = "Target" , DefaultValue = _testConfig.AmlComputeName, ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "MLCComputeType" , DefaultValue = "AmlCompute", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "PrepareEnvironment" , DefaultValue = "true", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "Script" , DefaultValue = "simple_count.py", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "Framework" , DefaultValue = "Python", ParameterType = ParameterType.String, IsOptional = false},
                },
            };


            var runConfig = "{\"Environment\": {\"Name\": \"" + EnvironmentNameWithoutVersion + "\", \"Version\":null, \"ContainsDefinition\":false}}";

            var moduleUploadInfo = new ModuleUploadInfo(
               name: "escloud named environment module",
               displayName: "escloud named environment module display",
               description: "escloud named environment module",
               isDeterministic: false,
               cloudSystem: "escloud",
               structuredInterface: structuredInterface,
               moduleTypeVersion: "1.0",
               runconfig: runConfig
               );

            return await environment.UploadModuleAsync(sourcePath, moduleUploadInfo);
        }

        private async Task<string> UploadESCloudNodeUsingNamedEnvironmentInEsCloudConfig(
            AetherEnvironment environment,
            string sourcePath)
        {
            var structuredInterface = new StructuredInterface
            {

                MetadataParameters = new List<StructuredInterfaceParameter>
                {
                    new StructuredInterfaceParameter { Name = "TargetType" , DefaultValue = "MLC", ParameterType = ParameterType.String, IsOptional = false},
                },
                Parameters = new List<StructuredInterfaceParameter>
                {
                    new StructuredInterfaceParameter { Name = "Target" , DefaultValue = _testConfig.AmlComputeName, ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "MLCComputeType" , DefaultValue = "AmlCompute", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "PrepareEnvironment" , DefaultValue = "true", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "Script" , DefaultValue = "simple_count.py", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "Framework" , DefaultValue = "Python", ParameterType = ParameterType.String, IsOptional = false},
                },
            };

            var cloudSettings = new CloudSettings
            {
                EsCloudConfig = new EsCloudConfiguration
                {
                    Environment = new EnvironmentConfiguration
                    {
                        UseEnvironmentDefinition = false,
                        Name = "BasicNamedEnvironment",
                        Version = "1",
                    }
                }
            };

            var moduleUploadInfo = new ModuleUploadInfo(
               name: "escloud named environment module",
               displayName: "escloud named environment module display",
               description: "escloud named environment module",
               isDeterministic: false,
               cloudSystem: "escloud",
               structuredInterface: structuredInterface,
               moduleTypeVersion: "1.0",
               cloudSettings: cloudSettings
               );

            return await environment.UploadModuleAsync(sourcePath, moduleUploadInfo);
        }

        private async Task<IGraph> CreateEsCloudGraphUsingInlineEnvironmentModuleInRunConfigWithNullEnvironmentInRunConfigOverwrite(AetherEnvironment environment, string sourcePath)
        {
            var graph = environment.CreateNewGraph();
            var moduleId = await UploadESCloudNodeUsingInlineEnvironmentInRunConfig(
                environment,
                sourcePath);
            IAetherModule module = await environment.GetModuleAsync(moduleId);

            var runConfig = new ExecutionContracts.RunConfiguration
            {
                Environment = null,
            };
            graph.AddNode(module, runConfig: SerializationHelpers.SerializeEntity(runConfig));
            return graph;
        }

        private async Task<IGraph> CreateGraphUsingInlineEnvironmentDefinitionModuleWithNullEnvironmentInEsCloudConfigOverwrite(AetherEnvironment environment, string codePath)
        {

            var dtcGraph = environment.CreateNewGraph();
            string moduleId = await UploadESCloudNodeUsingInlineEnvironmentInRunConfig(environment, codePath);
            IAetherModule module = await environment.GetModuleAsync(moduleId);

            var cloudSetting = new CloudSettings
            {
                EsCloudConfig = new EsCloudConfiguration
                {
                    Environment = null,
                }
            };
            dtcGraph.AddNode(module, cloudSettings: cloudSetting);
            return dtcGraph;
        }

        private async Task<IGraph> CreateGraphUsingInlineEnvironmentDefinitionWithSameInlineEnvironmentInRunConfigOverwrite(AetherEnvironment environment, string codePath)
        {

            var dtcGraph = environment.CreateNewGraph();
            string moduleId = await UploadESCloudNodeUsingInlineEnvironmentInRunConfig(environment, codePath);
            IAetherModule module = await environment.GetModuleAsync(moduleId);

            var runConfig = new ExecutionContracts.RunConfiguration
            {
                Environment = new EnvironmentDefinition
                {
                    Docker = new DockerSection
                    {
                        BaseImage = new DockerImageName(OriginalDockerImage),
                    },
                    Python = new PythonSection
                    {
                        CondaDependencies = JsonConvert.DeserializeObject<JToken>(GetCondaSetup()),
                    }
                }
            };
            dtcGraph.AddNode(module, runConfig: SerializationHelpers.SerializeEntity(runConfig));
            return dtcGraph;
        }

        private async Task<IGraph> CreateGraphUsingInlineEnvironmentDefinitionWithSameInlineEnvironmentInEsCloudConfigOverwrite(AetherEnvironment environment, string codePath)
        {

            var dtcGraph = environment.CreateNewGraph();
            string moduleId = await UploadESCloudNodeUsingInlineEnvironmentInRunConfig(environment, codePath);
            IAetherModule module = await environment.GetModuleAsync(moduleId);

            var cloudSettings = new CloudSettings
            {
                EsCloudConfig = new EsCloudConfiguration
                {
                    Environment = new EnvironmentConfiguration
                    {
                        UseEnvironmentDefinition = true,
                        EnvironmentDefinitionString = JsonConvert.SerializeObject(new EnvironmentDefinition
                        {
                            Docker = new DockerSection
                            {
                                BaseImage = new DockerImageName(OriginalDockerImage),
                            },
                            Python = new PythonSection
                            {
                                CondaDependencies = JsonConvert.DeserializeObject<JToken>(GetCondaSetup()),
                            }
                        })
                    }
                }
            };
            dtcGraph.AddNode(module, cloudSettings: cloudSettings);
            return dtcGraph;
        }

        private async Task<IGraph> CreateGraphUsingInlineEnvironmentDefinitionModuleWithInlineEnvironmentInRunConfigOverwrite(AetherEnvironment environment, string codePath)
        {

            var dtcGraph = environment.CreateNewGraph();

            string moduleId = await UploadESCloudNodeUsingInlineEnvironmentInRunConfig(environment, codePath);

            IAetherModule module = await environment.GetModuleAsync(moduleId);
            var runConfig = new ExecutionContracts.RunConfiguration
            {
                Environment = new EnvironmentDefinition
                {
                    Docker = new DockerSection
                    {
                        BaseImage = new DockerImageName(TestDockerImage),
                    },
                    Python = new PythonSection
                    {
                        CondaDependencies = JsonConvert.DeserializeObject<JToken>(TestCondaDependenciesString),
                    }
                }
            };
            dtcGraph.AddNode(module, runConfig: SerializationHelpers.SerializeEntity(runConfig));

            return dtcGraph;
        }

        private async Task<IGraph> CreateGraphUsingInlineEnvironmentDefinitionModuleWithNamedEnvironmentInEsCloudConfigOverwrite(AetherEnvironment environment, string codePath)
        {

            var dtcGraph = environment.CreateNewGraph();
            string moduleId = await UploadESCloudNodeUsingInlineEnvironmentInRunConfig(environment, codePath);
            IAetherModule module = await environment.GetModuleAsync(moduleId);

            var cloudSetting = new CloudSettings
            {
                EsCloudConfig = new EsCloudConfiguration
                {
                    Environment = new EnvironmentConfiguration
                    {
                        Name = "BasicNamedEnvironment",
                        Version = "1",
                        UseEnvironmentDefinition = false,
                    }
                }
            };
            dtcGraph.AddNode(module, cloudSettings: cloudSetting);
            return dtcGraph;
        }

        private async Task<IGraph> CreateGraphUsingInlineEnvironmentDefinitionModuleWithEnvironmentDefinitionInEsCloudConfigOverwrite(AetherEnvironment environment, string codePath)
        {

            var dtcGraph = environment.CreateNewGraph();
            string moduleId = await UploadESCloudNodeUsingInlineEnvironmentInRunConfig(environment, codePath);
            IAetherModule module = await environment.GetModuleAsync(moduleId);

            var cloudSetting = new CloudSettings
            {
                EsCloudConfig = new EsCloudConfiguration
                {
                    Environment = new EnvironmentConfiguration
                    {
                        UseEnvironmentDefinition = true,
                        EnvironmentDefinitionString = JsonConvert.SerializeObject(new EnvironmentDefinition
                        {
                            ContainsDefinition = true,
                            Docker = new DockerSection
                            {
                                BaseImage = new DockerImageName(TestDockerImage),
                            },
                            Python = new PythonSection
                            {
                                CondaDependencies = JsonConvert.DeserializeObject<JToken>(TestCondaDependenciesString),
                            }
                        }),
                    }
                }
            };
            dtcGraph.AddNode(module, cloudSettings: cloudSetting);
            return dtcGraph;
        }

        private async Task<IGraph> CreateGraphUsingNamedEnvironmentDefinitionModuleWithNullEnvironmentInRunConfigOverwrite(AetherEnvironment environment, string codePath)
        {

            var dtcGraph = environment.CreateNewGraph();
            string moduleId = await UploadESCloudNodeUsingNamedEnvironmentInRunConfig(environment, codePath);
            IAetherModule module = await environment.GetModuleAsync(moduleId);

            var runConfig = new ExecutionContracts.RunConfiguration
            {
                Environment = null,
            };

            dtcGraph.AddNode(module, runConfig: SerializationHelpers.SerializeEntity(runConfig));
            return dtcGraph;
        }

        private async Task<IGraph> CreateGraphUsingNamedEnvironmentDefinitionModuleWithoutEnvironmentInRunConfigOverwrite(AetherEnvironment environment, string codePath)
        {

            var dtcGraph = environment.CreateNewGraph();
            string moduleId = await UploadESCloudNodeUsingNamedEnvironmentInRunConfig(environment, codePath);
            IAetherModule module = await environment.GetModuleAsync(moduleId);

            dtcGraph.AddNode(module);
            return dtcGraph;
        }

        private async Task<IGraph> CreateGraphUsingNamedEnvironmentDefinitionModuleWithInlineEnvironmentOverwrite(AetherEnvironment environment, string codePath)
        {

            var dtcGraph = environment.CreateNewGraph();
            string moduleId = await UploadESCloudNodeUsingNamedEnvironmentInRunConfig(environment, codePath);
            IAetherModule module = await environment.GetModuleAsync(moduleId);

            var runConfig = new ExecutionContracts.RunConfiguration
            {
                Environment = new EnvironmentDefinition
                {
                    Docker = new DockerSection
                    {
                        BaseImage = new DockerImageName(TestDockerImage),
                    },
                    Python = new PythonSection
                    {
                        CondaDependencies = JsonConvert.DeserializeObject<JToken>(TestCondaDependenciesString),
                    }
                }
            };
            dtcGraph.AddNode(module, runConfig: SerializationHelpers.SerializeEntity(runConfig));
            return dtcGraph;
        }

        private async Task<IGraph> CreateGraphUsingNamedEnvironmentDefinitionModuleWithNamedEnvironmentInEsCloudConfigOverwrite(AetherEnvironment environment, string codePath)
        {

            var dtcGraph = environment.CreateNewGraph();
            string moduleId = await UploadESCloudNodeUsingNamedEnvironmentInRunConfig(environment, codePath);
            IAetherModule module = await environment.GetModuleAsync(moduleId);

            var cloudSetting = new CloudSettings
            {
                EsCloudConfig = new EsCloudConfiguration
                {
                    Environment = new EnvironmentConfiguration
                    {
                        Name = "BasicNamedEnvironment",
                        Version = "1",
                        UseEnvironmentDefinition = false,
                    }
                }
            };
            dtcGraph.AddNode(module, cloudSettings: cloudSetting);
            return dtcGraph;
        }

        private async Task<IGraph> CreateGraphUsingInlineEnvironmentDefinitionInEsCloudConfigModuleWithNullEnvironmentInEsCloudConfigOverwrite(AetherEnvironment environment, string codePath)
        {

            var dtcGraph = environment.CreateNewGraph();
            string moduleId = await UploadESCloudNodeUsingInlineEnvironmentInEsCloudConfig(environment, codePath);
            IAetherModule module = await environment.GetModuleAsync(moduleId);

            var cloudSetting = new CloudSettings
            {
                EsCloudConfig = new EsCloudConfiguration
                {
                    Environment = null,
                }
            };
            dtcGraph.AddNode(module, cloudSettings: cloudSetting);
            return dtcGraph;
        }

        private async Task<IGraph> CreateGraphUsingInlineEnvironmentDefinitionInEsCloudConfigModuleWithNamedEnvironmentInEsCloudConfigOverwrite(AetherEnvironment environment, string codePath)
        {

            var dtcGraph = environment.CreateNewGraph();
            string moduleId = await UploadESCloudNodeUsingInlineEnvironmentInEsCloudConfig(environment, codePath);
            IAetherModule module = await environment.GetModuleAsync(moduleId);

            var cloudSetting = new CloudSettings
            {
                EsCloudConfig = new EsCloudConfiguration
                {
                    Environment = new EnvironmentConfiguration
                    {
                        Name = "AzureML-minimal-ubuntu18.04-py37-cpu-inference",
                        Version = "27",
                        UseEnvironmentDefinition = false,
                    }
                }
            };
            dtcGraph.AddNode(module, cloudSettings: cloudSetting);
            return dtcGraph;
        }

        private async Task<IGraph> CreateGraphUsingInlineEnvironmentDefinitionInEsCloudConfigModuleWithEnvironmentDefinitionInEsCloudConfigOverwrite(AetherEnvironment environment, string codePath)
        {

            var dtcGraph = environment.CreateNewGraph();
            string moduleId = await UploadESCloudNodeUsingInlineEnvironmentInEsCloudConfig(environment, codePath);
            IAetherModule module = await environment.GetModuleAsync(moduleId);
            var cloudSetting = new CloudSettings
            {
                EsCloudConfig = new EsCloudConfiguration
                {
                    Environment = new EnvironmentConfiguration
                    {
                        UseEnvironmentDefinition = true,
                        EnvironmentDefinitionString = JsonConvert.SerializeObject(new EnvironmentDefinition
                        {
                            ContainsDefinition = true,
                            Docker = new DockerSection
                            {
                                BaseImage = new DockerImageName(TestDockerImage),
                            },
                            Python = new PythonSection
                            {
                                UserManagedDependencies = true,
                            }
                        })
                    }
                }
            };
            dtcGraph.AddNode(module, cloudSettings: cloudSetting);
            return dtcGraph;
        }

        private async Task<IGraph> CreateGraphUsingNamedEnvironmentDefinitionInEsCloudConfigModuleWithNamedEnvironmentInEsCloudConfigOverwrite(AetherEnvironment environment, string codePath)
        {

            var dtcGraph = environment.CreateNewGraph();
            string moduleId = await UploadESCloudNodeUsingNamedEnvironmentInEsCloudConfig(environment, codePath);
            IAetherModule module = await environment.GetModuleAsync(moduleId);

            var cloudSetting = new CloudSettings
            {
                EsCloudConfig = new EsCloudConfiguration
                {
                    Environment = new EnvironmentConfiguration
                    {
                        Name = "AzureML-minimal-ubuntu18.04-py37-cpu-inference",
                        Version = "27",
                        UseEnvironmentDefinition = false,
                    }
                }
            };
            dtcGraph.AddNode(module, cloudSettings: cloudSetting);
            return dtcGraph;
        }

        private async Task<IGraph> CreateGraphUsingNamedEnvironmentDefinitionInEsCloudConfigModuleWithEnvironmentDefinitionInEsCloudConfigOverwrite(AetherEnvironment environment, string codePath)
        {
            var dtcGraph = environment.CreateNewGraph();
            string moduleId = await UploadESCloudNodeUsingNamedEnvironmentInEsCloudConfig(environment, codePath);
            IAetherModule module = await environment.GetModuleAsync(moduleId);

            var cloudSetting = new CloudSettings
            {
                EsCloudConfig = new EsCloudConfiguration
                {
                    Environment = new EnvironmentConfiguration
                    {
                        UseEnvironmentDefinition = true,
                        EnvironmentDefinitionString = JsonConvert.SerializeObject(new EnvironmentDefinition
                        {
                            Docker = new DockerSection
                            {
                                BaseImage = new DockerImageName(TestDockerImage),
                            },
                            Python = new PythonSection
                            {
                                UserManagedDependencies = true,
                            }
                        })
                    }
                }
            };
            dtcGraph.AddNode(module, cloudSettings: cloudSetting);
            return dtcGraph;
        }

        private async Task<IGraph> CreateGraphUsingNamedEnvironmentDefinitionWithNullVersionInRunConfig(AetherEnvironment environment, string codePath)
        {
            var dtcGraph = environment.CreateNewGraph();
            string moduleId = await UploadESCloudNodeUsingNamedEnvironmentWithNullVersionInRunConfig(environment, codePath);
            IAetherModule module = await environment.GetModuleAsync(moduleId);
            dtcGraph.AddNode(module, regenerateOutput: true);
            return dtcGraph;
        }

        private async Task<IGraph> CreateGraphUsingNamedEnvironmentDefinitionWithoutVersionInRunConfig(AetherEnvironment environment, string codePath)
        {
            var dtcGraph = environment.CreateNewGraph();
            string moduleId = await UploadESCloudNodeUsingNamedEnvironmentWithoutVersionInRunConfig(environment, codePath);
            IAetherModule module = await environment.GetModuleAsync(moduleId);
            dtcGraph.AddNode(module, regenerateOutput: true);
            return dtcGraph;
        }

        private async Task<ExecutionContracts.RunConfiguration> GetNodeRunConfig(string pipelineRunId, string experimentName)
        {
            var childRunIndexEntity = await _indexServiceApiCaller.ListFullChildRunsByIndexService(pipelineRunId).ConfigureAwait(false);
            var childRunId = childRunIndexEntity.First().Properties.RunId;
            var runDetail = await _runHistoryApiCaller.GetRunDetails(experimentName, childRunId).ConfigureAwait(false);
            Assert.NotNull(runDetail.RunDefinition);
            return ((JObject)runDetail.RunDefinition).ToObject<ExecutionContracts.RunConfiguration>();
        }

        private async Task<IGraph> CreateCommandGraph(AetherEnvironment environment, string codePath, string scriptName)
        {
            var dtcGraph = environment.CreateNewGraph();

            string moduleId = await UploadCommandModule(environment, codePath, scriptName);

            IAetherModule module = await environment.GetModuleAsync(moduleId);
            dtcGraph.AddNode(module);

            return dtcGraph;
        }

        private async Task<string> UploadCommandModule(AetherEnvironment environment, string codePath, string command)
        {
            var structuredInterface = new StructuredInterface
            {
                MetadataParameters = new List<StructuredInterfaceParameter>
                {
                    new StructuredInterfaceParameter { Name = "TargetType" , DefaultValue = "MLC", ParameterType = ParameterType.String, IsOptional = false},
                },
                Parameters = new List<StructuredInterfaceParameter>
                {
                    new StructuredInterfaceParameter { Name = "Target" , DefaultValue = _testConfig.AmlWindowsComputeName, ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "MLCComputeType" , DefaultValue = "AmlCompute", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "PrepareEnvironment" , DefaultValue = "true", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "GenerateJson" , DefaultValue = "true", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "Command" , DefaultValue = command, ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "Framework" , DefaultValue = "Python", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "maxRunDurationSeconds" , DefaultValue = "1200", ParameterType = ParameterType.String, IsOptional = true},
                    new StructuredInterfaceParameter { Name = "InterpreterPath" , DefaultValue = "python", ParameterType = ParameterType.String, IsOptional = true},
                    new StructuredInterfaceParameter { Name = "UserManagedDependencies" , DefaultValue = "false", ParameterType = ParameterType.String, IsOptional = true},
                    new StructuredInterfaceParameter { Name = "CondaDependencies" , DefaultValue = @"
                            {
                                'name': 'project_environment',
                                'dependencies': [
                                'python = 3.8',
                                {
                                        'pip': [
                                        'azureml-defaults'
                                        ]
                                    }
                                    ]
                            }", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "DockerEnabled" , DefaultValue = "false", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "BaseDockerImage" , DefaultValue = OriginalDockerImage, ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "SharedVolumes" , DefaultValue = "true", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "NodeCount" , DefaultValue = "1", ParameterType = ParameterType.String, IsOptional = true},
                    new StructuredInterfaceParameter { Name = "ContainerInstanceRegion" , DefaultValue = "None", ParameterType = ParameterType.String, IsOptional = true},
                    new StructuredInterfaceParameter { Name = "ContainerInstanceCpuCores" , DefaultValue = "1", ParameterType = ParameterType.String, IsOptional = true},
                    new StructuredInterfaceParameter { Name = "ContainerInstanceMemoryGb" , DefaultValue = "4", ParameterType = ParameterType.String, IsOptional = true}
                }
            };
            var moduleUploadInfo = new ModuleUploadInfo(
               name: "escloud execution test module",
               displayName: "escloud execution test module display",
               description: "escloud execution test module",
               isDeterministic: true,
               cloudSystem: "escloud",
               structuredInterface: structuredInterface,
               moduleTypeVersion: "1.0");

            return await environment.UploadModuleAsync(codePath, moduleUploadInfo);
        }

        private async Task<IGraph> CreateExepoolStyleCommandGraph(AetherEnvironment environment, string codePath, string scriptName)
        {
            var dtcGraph = environment.CreateNewGraph();
            string moduleId = await UploadExepoolStyleCommandModule(environment, codePath, scriptName);

            IAetherModule module = await environment.GetModuleAsync(moduleId, populateRunconfig: true);
            dtcGraph.AddNode(module);

            return dtcGraph;
        }

        private async Task<string> UploadExepoolStyleCommandModule(AetherEnvironment environment, string codePath, string command)
        {
            var structuredInterface = new StructuredInterface
            {
                MetadataParameters = new List<StructuredInterfaceParameter>
                {
                    new StructuredInterfaceParameter { Name = "TargetType" , DefaultValue = "MLC", ParameterType = ParameterType.String, IsOptional = false}
                },
                Parameters = new List<StructuredInterfaceParameter>
                {
                    new StructuredInterfaceParameter { Name = "Target" , DefaultValue = _testConfig.AmlWindowsComputeName, ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "MLCComputeType" , DefaultValue = "AmlCompute", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "PrepareEnvironment" , DefaultValue = "true", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "GenerateJson" , DefaultValue = "true", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "Command" , DefaultValue = command, ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "Framework" , DefaultValue = "Python", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "maxRunDurationSeconds" , DefaultValue = "1200", ParameterType = ParameterType.String, IsOptional = true},
                    new StructuredInterfaceParameter { Name = "InterpreterPath" , DefaultValue = "python", ParameterType = ParameterType.String, IsOptional = true},
                    new StructuredInterfaceParameter { Name = "UserManagedDependencies" , DefaultValue = "false", ParameterType = ParameterType.String, IsOptional = true},
                    new StructuredInterfaceParameter { Name = "CondaDependencies" , DefaultValue = @"
                            {
                                'name': 'project_environment',
                                'dependencies': [
                                'python = 3.8',
                                {
                                        'pip': [
                                        'azureml-defaults'
                                        ]
                                    }
                                    ]
                            }", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "DockerEnabled" , DefaultValue = "false", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "BaseDockerImage" , DefaultValue = OriginalDockerImage, ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "SharedVolumes" , DefaultValue = "true", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "DataFileName" , DefaultValue = "DataFile raw.txt", ParameterType = ParameterType.String, IsOptional = true},
                    new StructuredInterfaceParameter { Name = "DataFolderPath" , DefaultValue = "DataFolder", ParameterType = ParameterType.String, IsOptional = true},
                    new StructuredInterfaceParameter { Name = "ContainerInstanceRegion" , DefaultValue = "None", ParameterType = ParameterType.String, IsOptional = true},
                    new StructuredInterfaceParameter { Name = "ContainerInstanceCpuCores" , DefaultValue = "1", ParameterType = ParameterType.String, IsOptional = true},
                    new StructuredInterfaceParameter { Name = "ContainerInstanceMemoryGb" , DefaultValue = "4", ParameterType = ParameterType.String, IsOptional = true}
                },
                Arguments = new List<ArgumentAssignment>
                {
                    new ArgumentAssignment()
                    {
                        ValueType = ArgumentValueType.StringInterpolationList,
                        StringInterpolationArgumentList = new List<ArgumentAssignment>()
                        {
                            new ArgumentAssignment()
                            {
                                ValueType = ArgumentValueType.Parameter,
                                Value = "DataFolderPath"
                            },
                            new ArgumentAssignment()
                            {
                                ValueType = ArgumentValueType.Literal,
                                Value = "\\DataFileRaw.txt"
                            }
                        }

                    }
                }
            };
            var moduleUploadInfo = new ModuleUploadInfo(
               name: "escloud execution test module",
               displayName: "escloud execution test module display",
               description: "escloud execution test module",
               isDeterministic: true,
               cloudSystem: "escloud",
               structuredInterface: structuredInterface,
               moduleTypeVersion: "1.0",
               runconfig: exePoolCommandModuleWindowsRunConfig);

            return await environment.UploadModuleAsync(codePath, moduleUploadInfo);
        }


        /// <summary>
        /// Change module Arguments for Asset input and output.
        /// </summary>
        /// <param name="module"></param>
        /// <param name="inputName">module input name, same with <settings cref="InputSetting.Name"> and <StructuredInterfaceInput cref="StructuredInterfaceInput.Name"></param>
        /// <param name="outputName">module output name, same with <settings cref="OutputSetting.Name"> and <StructuredInterfaceOutput cref="StructuredInterfaceOutput.Name"></param>
        private static void SetArgumentsByInputAndOutput(IModuleNode module, string inputName = moduleInputName, string outputName = moduleOutputName)
        {
            module.Parameters["Arguments"].Value = $"{(inputName != null ? $"--in,$AZUREML_DATAREFERENCE_{inputName}," : "")}{(outputName != null ? $"--out,${outputName}" : "")}";
        }

        /// <summary>
        /// Brings the latest AzureML SDK version from master
        /// Pip version is set to 21.1.1 because the latest version (21.2.2 at this time) takes a long time to resolve dependencies
        /// </summary>
        private static string GetCondaSetup(bool includePandas = false)
        {
            return $@"{{
                'name': 'project_environment',
                'dependencies': [
                    'python=3.9',
                    'pip=21.1.1',
                    {{
                        'pip': [
                            'azureml-sdk',
                            'azureml-telemetry',
                            {(includePandas ? "'azureml-dataprep[pandas]'," : string.Empty)}
                        ]
                    }}
                ]
            }}";
        }

        private static string ConvertToDataTypeId(AssetType assetType)
        {
            switch (assetType)
            {
                case AssetType.UriFolder: return "uri_folder";
                case AssetType.UriFile: return "uri_file";
                case AssetType.MLTable: return "mltable";
                case AssetType.MLFlowModel: return "mlflow_model";
                case AssetType.CustomModel: return "mlflow_model";
                case AssetType.TritonModel: return "mlflow_model";
                default: return "uri_folder";
            }
        }

        private static IList<InputSetting> GetAssetInputSetting(AssetType inputType)
        {
            var directModeTypes = new HashSet<AssetType>()
            {
                AssetType.MLTable,
                AssetType.MLFlowModel,
                AssetType.CustomModel,
                AssetType.TritonModel
            };
            return new[]
            {
                new InputSetting
                {
                    Name = moduleInputName,
                    DataStoreMode = directModeTypes.Contains(inputType) ? DataStoreMode.Direct : DataStoreMode.Mount,
                }
            };
        }

        private static ExecutionContracts.RunConfiguration GetAssetTestRunConfiguration(AssetType outputType)
        {
            var modelTypes = new HashSet<AssetType>()
            {
                AssetType.MLFlowModel,
                AssetType.CustomModel,
                AssetType.TritonModel
            };

            return new ExecutionContracts.RunConfiguration
            {
                Environment = new EnvironmentDefinition
                {
                    Docker = new DockerSection
                    {
                        BaseImage = new DockerImageName(OriginalDockerImage),
                    },
                    Python = new PythonSection
                    {
                        CondaDependencies = JsonConvert.DeserializeObject<JToken>(TestCondaDependenciesString),
                    }
                },
                EnvironmentVariables = new Dictionary<string, string>
                {
                    // Asset can only work on CommonRuntime
                    { "AZUREML_COMPUTE_USE_COMMON_RUNTIME", "true" },
                    // These environment variables are used to control test script behavior which is for integration test purpose only
                    { "IS_OUTPUT_FILE", outputType == AssetType.UriFile ? "true" : "false" },
                    { "IS_OUTPUT_MLTABLE", outputType == AssetType.MLTable ? "true" : "false" },
                    { "IS_OUTPUT_MLMODEL", modelTypes.Contains(outputType) ? "true" : "false" }
                },
            };
        }

        /// <summary>
        /// Configuration to optimize the time to build the image by making some packages optional
        /// </summary>
        private class Script
        {
            public string Name { get; private set; }

            public bool IncludePandas { get; private set; }

            public Script(string name, bool includePandas = false)
            {
                Name = name;

                IncludePandas = includePandas;
            }
        }
    }
}
