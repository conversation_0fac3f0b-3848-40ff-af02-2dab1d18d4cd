﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <OutputPath>$(BaseTargetDir)\app\aether\BBAetherLibrary\BBAetherLibrary.ESCloud.IntegrationTests</OutputPath>
    <ImportSharedFiles>true</ImportSharedFiles>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="NUnit" />
    <PackageReference Include="NUnit3TestAdapter" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" />
    <PackageReference Include="coverlet.collector" />
    <PackageReference Include="Microsoft.MachineLearning.Execution.Contracts" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\Common\BBAetherLibrary.Common.csproj" />
    <ProjectReference Include="..\..\DataProvision\BBAetherLibrary.DataProvision.csproj" />
    <ProjectReference Include="..\BBAetherLibrary.IntegrationTests.Common\BBAetherLibrary.IntegrationTests.Common.csproj" />
    <ProjectReference Include="..\BBAetherLibrary.IntegrationTests\BBAetherLibrary.IntegrationTests.csproj" />
    <ProjectReference Include="$(SrcRoot)\Dataset\Contracts\Dataset.Contracts.csproj" />
    <ProjectReference Include="$(SrcRoot)\ModelRegistry\Contracts\ModelRegistry.Contracts.csproj" />
  </ItemGroup>
</Project>
