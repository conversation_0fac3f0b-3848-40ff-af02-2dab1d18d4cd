<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <OutputPath>$(BaseTargetDir)\app\aether\BBAetherLibrary\BBAetherLibrary.MetsStoreProxy.IntegrationTests</OutputPath>
    <GenerateBindingRedirectsOutputType>true</GenerateBindingRedirectsOutputType>
    <ImportSharedFiles>true</ImportSharedFiles>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="NUnit" />
    <PackageReference Include="NUnit3TestAdapter" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" />
    <PackageReference Include="coverlet.collector" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\..\backendV2\BlueBox\MetaStore\MetaStoreProxy.Client\MetaStoreProxy.Client.csproj" />
    <ProjectReference Include="..\..\..\backendV2\shared\Microsoft.Aether.ServiceTests.Common\Microsoft.Aether.ServiceTests.Common.csproj" />
    <ProjectReference Include="..\BBAetherLibrary.IntegrationTests.Common\BBAetherLibrary.IntegrationTests.Common.csproj" />
  </ItemGroup>
</Project>
