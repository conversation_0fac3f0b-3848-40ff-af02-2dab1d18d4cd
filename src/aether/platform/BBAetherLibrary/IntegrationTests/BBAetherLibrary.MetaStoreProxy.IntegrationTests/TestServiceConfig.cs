﻿using Microsoft.RelInfra.Common.Configuration;
using Microsoft.RelInfra.Extensions;
using System;
using System.Collections.Generic;

namespace BBAetherLibrary.MetaStoreProxy.IntegrationTests
{
    class TestServiceConfig : IRelInfraConfiguration
    {
        public string AmlServicesEndpoint;
        public Dictionary<string, string> testConfigurationProperties;

        public TestServiceConfig(String AmlServicesEndpoint)
        {
            // This is a service configuration mainly for the test purpose, similar to BBSharedSettings.ini.
            this.AmlServicesEndpoint = AmlServicesEndpoint;
            testConfigurationProperties = new Dictionary<string, string>() {
                { "MetaStoreProxy.BaseUrl", AmlServicesEndpoint },
                { "MetaStoreProxy.AddToIndex", "true" },
                { "MetaStoreProxy.modulesGetIndexEntityByIdPath", "/metastoreproxy/metastore/v1.0/modules/getIndexEntities" },
                { "MetaStoreProxy.modulesRebuildPath", "/metastoreproxy/metastore/v1.0/modules/rebuildIndex" },
                { "MetaStoreProxy.modulesUpdateAmlModuleEntityPath", "/metastoreproxy/v1.0/workspace/{workspaceId}/index/amlmodule" },
                { "MetaStoreProxy.modulesUpdateModuleEntityPath", "/metastoreproxy/v1.0/workspace/{workspaceId}/index/module" },
                { "MetaStoreProxy.pipelinerunsGetIndexEntityByIdPath", "/metastoreproxy/metastore/v1.0/pipelineruns/getIndexEntities" },
                { "MetaStoreProxy.pipelinerunsRebuildPath", "/metastoreproxy/metastore/v1.0/pipelineruns/rebuildIndex" },
                { "MetaStoreProxy.pipelinerunsUpdateExperimentEntityPath", "/metastoreproxy/v1.0/workspace/{workspaceId}/index/pipelinerun" },
                { "MetaStoreProxy.pipelinedraftsGetIndexEntityByIdPath", "/metastoreproxy/metastore/v1.0/pipelinedrafts/getIndexEntities" },
                { "MetaStoreProxy.pipelinedraftsRebuildPath", "/metastoreproxy/metastore/v1.0/pipelinedrafts/rebuildIndex" },
                { "MetaStoreProxy.pipelinedraftsUpdatePipelineDraftPath", "/metastoreproxy/v1.0/workspace/{workspaceId}/index/pipelinedraft" },
                { "MetaStoreProxy.pipelinedraftsDeletePipelineDraftPath", "/metastoreproxy/v1.0/workspace/{workspaceId}/index/pipelinedraft/{entityId}" },
                { "MetaStoreProxy.pipelinesGetIndexEntityByIdPath", "/metastoreproxy/metastore/v1.0/pipelines/getIndexEntities" },
                { "MetaStoreProxy.pipelinesRebuildPath", "/metastoreproxy/metastore/v1.0/pipelines/rebuildIndex" },
                { "MetaStoreProxy.pipelinesUpdatePipelineEndpointEntityPath", "/metastoreproxy/v1.0/workspace/{workspaceId}/index/pipelineendpoint" },
                { "MetaStoreProxy.pipelinesUpdatePipelineEntityPath", "/metastoreproxy/v1.0/workspace/{workspaceId}/index/pipeline" },
                { "MetaStoreProxy.independentpipelinesGetIndexEntityByIdPath", "/metastoreproxy/metastore/v1.0/independentpipelines/getIndexEntities" },
                { "MetaStoreProxy.independentpipelinesRebuildPath", "/metastoreproxy/metastore/v1.0/independentpipelines/rebuildIndex" },
                { "MetaStoreProxy.independentpipelinesUpdatePipelineEntityPath", "/metastoreproxy/v1.0/workspace/{workspaceId}/index/independentpipeline" },
                { "MetaStoreProxy.independentpipelinesDeletePipelineEntityPath", "/metastoreproxy/v1.0/workspace/{workspaceId}/index/independentpipeline/{entityId}" },
                { "MetaStoreProxy.generictriggersGetIndexEntityByIdPath", "/metastoreproxy/metastore/v1.0/generictriggers/getIndexEntities" },
                { "MetaStoreProxy.generictriggersRebuildPath", "/metastoreproxy/metastore/v1.0/generictriggers/rebuildIndex" },
                { "MetaStoreProxy.generictriggersUpdateGenericTriggerEntityPath", "/metastoreproxy/v1.0/workspace/{workspaceId}/index/generictrigger" },
                { "MetaStoreProxy.generictriggersDeleteGenericTriggerEntityPath", "/metastoreproxy/v1.0/workspace/{workspaceId}/index/generictrigger/{entityId}" },
            };
        }

        public IEnumerable<string> ConfigFileLocationList => throw new NotImplementedException();

        public Dictionary<string, string> GetAllParameters()
        {
            return testConfigurationProperties;
        }

        public string GetConfigurationSettingValue(string configurationSettingName) => testConfigurationProperties.GetOrDefault(configurationSettingName, "");

        public void RegisterConfigurationsEventHandler(EventHandler<ConfigurationUpdateEventArgs> updateHandler)
        {
        }
    }
}
