﻿using Microsoft.Aether.S2S.Common;
using System;
using System.Threading.Tasks;

namespace BBAetherLibrary.MetaStoreProxy.IntegrationTests
{
    class MetaStoreProxyServiceTestS2STokenProvider : IS2STokenProvider
    {
        private string AzureToken { get; set; }

        public string AppId => throw new NotImplementedException();

        public string AppKey => throw new NotImplementedException();

        public MetaStoreProxyServiceTestS2STokenProvider(string azureToken)
        {
            AzureToken = azureToken;
        }

        public Task<string> GetTokenAsync() => Task.FromResult(AzureToken);
    }
}
