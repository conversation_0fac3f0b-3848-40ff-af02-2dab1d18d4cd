﻿using Aether.Backend.BlueBox.MetaStore.MetaStoreProxy.Client;
using BBAetherLibrary.IntegrationTests.Common;
using Microsoft.Aether.DataContracts;
using Microsoft.Aether.ServiceTests.Common;
using Microsoft.MachineLearning.Common.Core;
using Microsoft.MachineLearning.Component.IndexServiceContracts;
using Microsoft.MachineLearning.Index.Contracts;
using Microsoft.MachineLearning.Metastore.Index.Contracts;
using Microsoft.MachineLearning.Schema.Contracts;
using Microsoft.RelInfra.Instrumentation;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BBAetherLibrary.MetaStoreProxy.IntegrationTests
{
    [TestFixture]
    public class IndexServiceProvidersTests : BBAetherTestFixtureBase
    {
        private string WorkspaceId;
        private const string AmlModuleId = "76f86dc4-7ff1-4214-92e7-92e0c64d0618";
        private const string ModuleVersionId_1 = "4d719784-1756-425e-bf4d-85eb226aa86b";
        private const string ModuleVersionId_2 = "7de4f4b4-1e59-455f-8835-9e685e46a43f";

        private const string PipelineRunId = "f77218b7-3b91-4258-b95c-f885f45f1520";
        private const string PipelineDraftId = "29e2a51c-e531-4131-82ce-0bbcd13f7386";
        private const string PipelineEndpointId = "84fc2186-c44f-4f2c-8b5f-a1c3311e5f2d";
        private const string PipelineEndpointId2 = "84fc2186-c44f-4f2c-8b5f-a1c3311e5f2d";
        private const string PipelineId = "4f49aba1-1ba0-4cbb-b6f2-2c446b5a266b";
        private const string IndependentPipelineId = "fc434506-7f89-4da9-b127-12044aaca408";

        private IMetaStoreProxyClient _client;

        [Test]
        [Ignore("PipelineRun index disabled")]
        public async Task Test_GetPipelineRunIndexEntities()
        {
            var request = new PipelineRunIndexEntityContracts.UnversionedEntityRequestDto()
            {
                UnversionedEntityIds = new List<EntityId>()
                {
                    new EntityId("centraluseuap", WorkspaceId, "pipelineruns", PipelineRunId)
                }
            };
            var res = await _client.GetPipelineRunsIndexEntityByIdAsync(request).ConfigureAwait(false);
            if (res == null)
            {
                return;
            }

            Assert.AreEqual(1, res.UnversionedEntities.Count);
            Assert.AreEqual("my-experiment", res.UnversionedEntities[0].Annotations.RunHistoryExperimentName);
        }

        [Test]
        public async Task Test_GetGenericTriggerEntityIndexEntities()
        {
            var request = new GenericTriggerIndexEntityContracts.UnversionedEntityRequestDto()
            {
                UnversionedEntityIds = new List<EntityId>()
                {
                    new EntityId("centraluseuap", _testEnvironment.WorkspaceIdentity.WorkspaceId, "generictriggers", "testpipelinejobtriggerad9299cf-d85f-45ba-8e2f-4024f8e83f46_latest")
                }
            };
            var res = await _client.GetGenericTriggersIndexEntityByIdAsync(request).ConfigureAwait(false);
            if (res == null)
            {
                return;
            }

            Assert.AreEqual(1, res.UnversionedEntities.Count);
            Assert.AreEqual("TestPipelineJobTriggerad9299cf-d85f-45ba-8e2f-4024f8e83f46", res.UnversionedEntities[0].Annotations.Name);
        }

        [Test]
        public async Task Test_GetPipelineDraftIndexEntities()
        {
            var request = new PipelineDraftIndexEntityContracts.UnversionedEntityRequestDto()
            {
                UnversionedEntityIds = new List<EntityId>()
                {
                    new EntityId("centraluseuap", WorkspaceId, "pipelinedrafts", PipelineDraftId)
                }
            };
            var res = await _client.GetPipelineDraftsIndexEntityByIdAsync(request).ConfigureAwait(false);
            if (res == null)
            {
                return;
            }

            Assert.AreEqual(1, res.UnversionedEntities.Count);
            Assert.AreEqual("pipeline_with_pipeline_component", res.UnversionedEntities[0].Annotations.Name);
        }

        [Test]
        public async Task Test_GetPipelineEndpointIndexEntities()
        {
            var request = new PipelineIndexEntityContracts.VersionedEntityRequestDto()
            {
                VersionedEntityIds = new List<EntityId>()
                {
                    new EntityId("centraluseuap", WorkspaceId, "pipelines", PipelineEndpointId2, "0")
                },
                LineageRootEntityIds = new List<EntityId>()
                {
                    new EntityId("centraluseuap", WorkspaceId, "pipelines", PipelineEndpointId)
                }
            };
            var res = await _client.GetPipelinesIndexEntityByIdAsync(request).ConfigureAwait(false);
            if (res == null)
            {
                return;
            }

            Assert.AreEqual(1, res.VersionedEntities.Count);
            Assert.AreEqual("TestSubmitSavedPipelineRun 03-24-2023-05-41", res.VersionedEntities[0].Annotations.PipelineName);
            Assert.AreEqual(1, res.LineageRootEntities.Count);
            Assert.AreEqual("TestSubmitSavedPipelineRun", res.LineageRootEntities[0].Annotations.Name);
        }

        [Test]
        public async Task Test_GetIndependentPipelineIndexEntities()
        {
            var request = new IndependentPipelineIndexEntityContracts.UnversionedEntityRequestDto()
            {
                UnversionedEntityIds = new List<EntityId>()
                {
                    new EntityId("centraluseuap", WorkspaceId, "independentpipelines", IndependentPipelineId)
                }
            };
            var res = await _client.GetIndependentPipelinesIndexEntityByIdAsync(request).ConfigureAwait(false);
            if (res == null)
            {
                return;
            }

            Assert.AreEqual(1, res.UnversionedEntities.Count);
            Assert.AreEqual("Record info to RH", res.UnversionedEntities[0].Annotations.Name);
        }

        [Test]
        [Ignore("PipelineRun index disabled")]
        public async Task Test_RebuildPipelineRunIndex()
        {
            var rebuildDto = new PipelineRunIndexEntityContracts.UnversionedRebuildIndexDto()
            {
                EntityCount = 10,
                WorkspaceId = WorkspaceId,
                EntityType = "pipelineruns",
                StartTime = DateTimeOffset.Parse("2020-11-05T17:11:51.8751508Z"),
                EndTime = DateTimeOffset.Parse("2020-11-05T17:11:51.8751509Z"),
            };

            var responseDto = await _client.RebuildPipelineRunsIndexAsync(rebuildDto).ConfigureAwait(false);
            if (responseDto == null)
            {
                return;
            }

            Assert.IsNull(responseDto.Entities.ContinuationToken);
            Assert.AreEqual(1, responseDto.Entities.Count);
            Assert.AreEqual("my-experiment", responseDto.Entities.Value.First().Annotations.RunHistoryExperimentName);
        }

        [Test]
        public async Task Test_RebuildGenericTriggerEntityIndex()
        {
            var rebuildDto = new GenericTriggerIndexEntityContracts.UnversionedRebuildIndexDto()
            {
                EntityCount = 10,
                WorkspaceId = WorkspaceId,
                EntityType = "generictriggers",
                StartTime = DateTimeOffset.Parse("2023-03-28T04:56:30.5664618Z"),
                EndTime = DateTimeOffset.Parse("2023-03-28T04:56:30.5664620Z"),
            };

            var responseDto = await _client.RebuildGenericTriggersIndexAsync(rebuildDto).ConfigureAwait(false);
            if (responseDto == null)
            {
                return;
            }

            Assert.IsNull(responseDto.Entities.ContinuationToken);
            Assert.AreEqual(1, responseDto.Entities.Count);
            Assert.AreEqual("TestPipelineJobTriggerad9299cf-d85f-45ba-8e2f-4024f8e83f46", responseDto.Entities.Value.First().Annotations.Name);
        }

        [Test]
        public async Task Test_RebuildPipelineDraftIndex()
        {
            var rebuildDto = new PipelineDraftIndexEntityContracts.UnversionedRebuildIndexDto()
            {
                EntityCount = 10,
                WorkspaceId = WorkspaceId,
                EntityType = "pipelinedrafts",
                StartTime = DateTimeOffset.Parse("2023-03-23T06:13:24.560538Z"),
                EndTime = DateTimeOffset.Parse("2023-03-23T06:13:24.560540Z"),
            };

            var responseDto = await _client.RebuildPipelineDraftsIndexAsync(rebuildDto).ConfigureAwait(false);
            if (responseDto == null)
            {
                return;
            }

            Assert.IsNull(responseDto.Entities.ContinuationToken);
            Assert.AreEqual(1, responseDto.Entities.Count);
            Assert.AreEqual("Pipeline-Created-on-03-23-2023", responseDto.Entities.Value.First().Annotations.Name);
        }

        [Test]
        public async Task Test_RebuildPipelineEndpointIndex()
        {
            var rebuildDto = new PipelineIndexEntityContracts.VersionedRebuildIndexDto()
            {
                VersionedEntityCount = 10,
                LineageRootEntityCount = 10,
                WorkspaceId = WorkspaceId,
                EntityType = "pipelines",
                StartTime = DateTimeOffset.Parse("2023-03-24T09:41:31.3436958Z"),
                EndTime = DateTimeOffset.Parse("2023-03-28T07:02:37.3580785Z"),
            };

            var responseDto = await _client.RebuildPipelinesIndexAsync(rebuildDto).ConfigureAwait(false);
            if (responseDto == null)
            {
                return;
            }

            Assert.IsNull(responseDto.VersionedEntities.ContinuationToken);
            Assert.AreEqual(1, responseDto.VersionedEntities.Count);
            foreach (var x in responseDto.VersionedEntities.Value)
            {
                Assert.AreEqual("manuallySubmittedJob 03-28-2023-03-01", x.Annotations.PipelineName);
            }
            Assert.IsNull(responseDto.LineageRootEntities.ContinuationToken);
            Assert.AreEqual(3, responseDto.LineageRootEntities.Count);
        }

        [Test]
        public async Task Test_RebuildIndependentPipelineIndex()
        {
            var rebuildDto = new IndependentPipelineIndexEntityContracts.UnversionedRebuildIndexDto()
            {
                EntityCount = 10,
                WorkspaceId = WorkspaceId,
                EntityType = "independentpipelines",
                StartTime = DateTimeOffset.Parse("2023-03-28T06:41:30.3737290Z"),
                EndTime = DateTimeOffset.Parse("2023-03-28T06:41:30.3737293Z"),
            };

            var responseDto = await _client.RebuildIndependentPipelinesIndexAsync(rebuildDto).ConfigureAwait(false);
            if (responseDto == null)
            {
                return;
            }

            Assert.IsNull(responseDto.Entities.ContinuationToken);
            Assert.AreEqual(1, responseDto.Entities.Count);
            Assert.AreEqual("Record info to RH", responseDto.Entities.Value.First().Annotations.Name);
        }

        [Test]
        public async Task Test_GetModuleIndexEntities()
        {
            var request = new ModuleIndexEntityContracts.VersionedEntityRequestDto()
            {
                VersionedEntityIds = new List<EntityId>()
                {
                    new EntityId("centraluseuap", WorkspaceId, EntityType.Modules, AmlModuleId, "1")
                },
                LineageRootEntityIds = new List<EntityId>()
                {
                    new EntityId("centraluseuap", WorkspaceId, EntityType.Modules, AmlModuleId)
                }
            };
            var res = await _client.GetModulesIndexEntityByIdAsync(request).ConfigureAwait(false);
            if (res == null)
            {
                return;
            }

            Assert.AreEqual(1, res.VersionedEntities.Count);
            AssertModuleIndexEntity(res.VersionedEntities[0], "1");

            Assert.AreEqual(1, res.LineageRootEntities.Count);
            AssertAmlModuleIndexEntity(res.LineageRootEntities[0]);
        }

        [Test]
        public async Task Test_RebuildIndex()
        {
            var rebuildDto = new ModuleIndexEntityContracts.VersionedRebuildIndexDto()
            {
                VersionedEntityCount = 500,
                LineageRootEntityCount = 100,
                WorkspaceId = WorkspaceId,
                EntityType = EntityType.Modules,
                EndTime = DateTimeOffset.Parse("2023-03-27T00:00:00Z")
            };

            var responseDto = await _client.RebuildModulesIndexAsync(rebuildDto).ConfigureAwait(false);
            if (responseDto == null)
            {
                return;
            }

            Assert.IsNull(responseDto.VersionedEntities.ContinuationToken);
            Assert.AreEqual(23, responseDto.VersionedEntities.Count);
            Assert.IsNull(responseDto.LineageRootEntities.ContinuationToken);
            Assert.AreEqual(20, responseDto.LineageRootEntities.Count);
        }

        [Test]
        public async Task Test_RebuildIndex_ModuleDetails()
        {
            var rebuildDto = new ModuleIndexEntityContracts.VersionedRebuildIndexDto()
            {
                VersionedEntityCount = 1,
                LineageRootEntityCount = 1,
                WorkspaceId = WorkspaceId,
                EntityType = EntityType.Modules,
                StartTime = DateTimeOffset.Parse("2023-03-24T08:02:25.5588538Z"),
                EndTime = DateTimeOffset.Parse("2023-03-24T08:02:59.6196523Z"),
            };

            var responseDto = await _client.RebuildModulesIndexAsync(rebuildDto).ConfigureAwait(false);
            if (responseDto == null)
            {
                return;
            }

            Assert.IsNotNull(responseDto.VersionedEntities.ContinuationToken);
            Assert.AreEqual(1, responseDto.VersionedEntities.Count);
            var ver1 = responseDto.VersionedEntities.Value.First();
            Assert.NotNull(ver1);
            AssertModuleIndexEntity(ver1, ver1.Version);
            Assert.IsNull(responseDto.LineageRootEntities.ContinuationToken);
            Assert.AreEqual(1, responseDto.LineageRootEntities.Count);
            AssertAmlModuleIndexEntity(responseDto.LineageRootEntities.Value.First());

            rebuildDto.VersionedEntityContinuationToken = responseDto.VersionedEntities.ContinuationToken;
            responseDto = await _client.RebuildModulesIndexAsync(rebuildDto).ConfigureAwait(false);
            Assert.AreEqual(1, responseDto.VersionedEntities.Count);
            var ver2 = responseDto.VersionedEntities.Value.First();
            Assert.AreNotEqual(ver1.Version, ver2.Version);
            Assert.NotNull(ver2);
            AssertModuleIndexEntity(ver2, ver2.Version);
            Assert.IsNull(responseDto.LineageRootEntities.ContinuationToken);
            Assert.AreEqual(1, responseDto.LineageRootEntities.Count);
            AssertAmlModuleIndexEntity(responseDto.LineageRootEntities.Value.First());

            if (responseDto.VersionedEntities.ContinuationToken != null)
            {
                rebuildDto.VersionedEntityContinuationToken = responseDto.VersionedEntities.ContinuationToken;
                responseDto = await _client.RebuildModulesIndexAsync(rebuildDto).ConfigureAwait(false);
                Assert.AreEqual(0, responseDto.VersionedEntities.Count);
                Assert.IsNull(responseDto.LineageRootEntities.ContinuationToken);
            }
        }

        [Test]
        [Ignore("StartTime Rebuild index disabled since the workspace")]
        public async Task Test_RebuildIndex_StartTime()
        {
            var rebuildDto = new ModuleIndexEntityContracts.VersionedRebuildIndexDto()
            {
                VersionedEntityCount = 1,
                LineageRootEntityCount = 0,
                WorkspaceId = WorkspaceId,
                EntityType = EntityType.Modules,
                StartTime = DateTimeOffset.Parse("2023-03-24T08:02:25.5588538Z"),
            };

            var responseDto = await _client.RebuildModulesIndexAsync(rebuildDto).ConfigureAwait(false);
            if (responseDto == null)
            {
                return;
            }

            Assert.IsNotNull(responseDto.VersionedEntities.ContinuationToken);
            Assert.AreEqual(1, responseDto.VersionedEntities.Count);
            Assert.AreEqual("last", responseDto.VersionedEntities.Value.First().Version);
            Assert.IsNotNull(responseDto.LineageRootEntities.ContinuationToken);
            Assert.AreEqual(1, responseDto.LineageRootEntities.Count);
        }

        [Test]
        public async Task Test_RebuildIndex_EndTime()
        {
            var rebuildDto = new ModuleIndexEntityContracts.VersionedRebuildIndexDto()
            {
                VersionedEntityCount = 1,
                LineageRootEntityCount = 1,
                WorkspaceId = WorkspaceId,
                EntityType = EntityType.Modules,
                StartTime = DateTimeOffset.Parse("2023-03-24T08:02:25.5588538Z"),
                EndTime = DateTimeOffset.Parse("2023-03-24T08:02:25.5588541Z"),
            };

            var responseDto = await _client.RebuildModulesIndexAsync(rebuildDto).ConfigureAwait(false);
            if (responseDto == null)
            {
                return;
            }

            Assert.IsNull(responseDto.VersionedEntities.ContinuationToken);
            Assert.AreEqual(1, responseDto.VersionedEntities.Count);
            AssertModuleIndexEntity(responseDto.VersionedEntities.Value.First(), "1");
            Assert.IsNull(responseDto.LineageRootEntities.ContinuationToken);
            Assert.AreEqual(1, responseDto.LineageRootEntities.Count);
            AssertAmlModuleIndexEntity(responseDto.LineageRootEntities.Value.First());
        }

        [Test]
        public async Task Test_RebuildIndex_VersionedEntityContinuationToken()
        {
            var rebuildDto = new ModuleIndexEntityContracts.VersionedRebuildIndexDto()
            {
                VersionedEntityCount = 1,
                LineageRootEntityCount = 0,
                WorkspaceId = WorkspaceId,
                EntityType = EntityType.Modules,
                EndTime = DateTimeOffset.Parse("2023-03-27T00:00:00Z"),
            };

            int count = 0;
            string contToken = null;
            do
            {
                rebuildDto.VersionedEntityContinuationToken = contToken;
                var responseDto = await _client.RebuildModulesIndexAsync(rebuildDto).ConfigureAwait(false);
                if (responseDto == null)
                {
                    return;
                }
                contToken = responseDto.VersionedEntities.ContinuationToken;
                count += responseDto.VersionedEntities.Count ?? 0;
            }
            while (contToken != null);

            Assert.AreEqual(23, count);
        }

        [Test]
        public async Task Test_RebuildIndex_LineageRootEntityContinuationToken()
        {
            var rebuildDto = new ModuleIndexEntityContracts.VersionedRebuildIndexDto()
            {
                VersionedEntityCount = 0,
                LineageRootEntityCount = 1,
                WorkspaceId = WorkspaceId,
                EntityType = EntityType.Modules,
                EndTime = DateTimeOffset.Parse("2023-03-28T00:00:00Z"),
            };

            int count = 0;
            string contToken = null;
            do
            {
                rebuildDto.LineageRootEntityContinuationToken = contToken;
                var responseDto = await _client.RebuildModulesIndexAsync(rebuildDto).ConfigureAwait(false);
                if (responseDto == null)
                {
                    return;
                }
                contToken = responseDto.LineageRootEntities.ContinuationToken;
                count += responseDto.LineageRootEntities.Count ?? 0;
            }
            while (contToken != null);

            Assert.AreEqual(172, count);
        }

        [Test]
        public async Task Test_RebuildIndex_SemanticDefault()
        {
            var rebuildDto = new ModuleIndexEntityContracts.VersionedRebuildIndexDto()
            {
                VersionedEntityCount = 4,
                LineageRootEntityCount = 0,
                WorkspaceId = WorkspaceId,
                EntityType = EntityType.Modules,
                StartTime = DateTimeOffset.Parse("2023-03-24T08:02:25.5588538Z"),
                EndTime = DateTimeOffset.Parse("2023-03-24T08:02:25.5588541Z"),
            };

            var responseDto = await _client.RebuildModulesIndexAsync(rebuildDto).ConfigureAwait(false);
            if (responseDto == null)
            {
                return;
            }

            Assert.IsNull(responseDto.VersionedEntities.ContinuationToken);
            Assert.AreEqual(1, responseDto.VersionedEntities.Count);
            Assert.IsNull(responseDto.LineageRootEntities.ContinuationToken);
            Assert.AreEqual(1, responseDto.LineageRootEntities.Count);
            Assert.AreEqual("2", responseDto.LineageRootEntities.Value.First().Annotations.Labels["default"]);
        }

        [Test]
        public async Task Test_RebuildIndex_LatestDefault()
        {
            var rebuildDto = new ModuleIndexEntityContracts.VersionedRebuildIndexDto()
            {
                VersionedEntityCount = 4,
                LineageRootEntityCount = 0,
                WorkspaceId = WorkspaceId,
                EntityType = EntityType.Modules,
                StartTime = DateTimeOffset.Parse("2023-03-24T08:02:25.5588538Z"),
                EndTime = DateTimeOffset.Parse("2023-03-24T08:02:59.6196523Z"),
            };

            var responseDto = await _client.RebuildModulesIndexAsync(rebuildDto).ConfigureAwait(false);
            if (responseDto == null)
            {
                return;
            }

            Assert.IsNull(responseDto.VersionedEntities.ContinuationToken);
            Assert.AreEqual(2, responseDto.VersionedEntities.Count);
            Assert.IsNull(responseDto.LineageRootEntities.ContinuationToken);
            Assert.AreEqual(1, responseDto.LineageRootEntities.Count);
            Assert.AreEqual("2", responseDto.LineageRootEntities.Value.First().Annotations.Labels["default"]);
        }

        private void AssertModuleIndexEntity(ModuleIndexEntity entity, string version)
        {
            Assert.IsTrue("1" == version || "2" == version);

            Assert.AreEqual(EntityKind.Versioned, entity.Kind);
            Assert.AreEqual(WorkspaceId, entity.EntityId.EntityContainerId);
            Assert.AreEqual(version, entity.EntityId.Version);
            Assert.AreEqual(AmlModuleId, entity.EntityId.ObjectId);

            if (version == "1")
            {
                Assert.AreEqual("AddAndMultiply", entity.Annotations.AmlModuleName);
                Assert.AreEqual("AddAndMultiply", entity.Annotations.ModuleName);
                Assert.AreEqual("initial", entity.Annotations.Description);
                Assert.AreEqual("1", entity.Annotations.ModuleVersion);

                Assert.NotNull(entity.Annotations.Inputs);
                Assert.AreEqual(0, entity.Annotations.Inputs.Count);

                Assert.NotNull(entity.Annotations.Outputs);
                Assert.AreEqual(3, entity.Annotations.Outputs.Count);
                Assert.AreEqual("out_sum", entity.Annotations.Outputs[0].Name);
                Assert.AreEqual("AnyFile", entity.Annotations.Outputs[0].DataTypeId);
                Assert.AreEqual("out_prod", entity.Annotations.Outputs[1].Name);
                Assert.AreEqual("AnyFile", entity.Annotations.Outputs[1].DataTypeId);
                Assert.AreEqual("_run_after_output", entity.Annotations.Outputs[2].Name);
                Assert.AreEqual("AnyFile", entity.Annotations.Outputs[2].DataTypeId);

                Assert.AreEqual(null, entity.Annotations.Category);
                Assert.AreEqual("ModuleStep", entity.Annotations.StepType);
                Assert.AreEqual(EntityStatus.Active, entity.Annotations.EntityStatus);
                Assert.AreEqual(true, entity.Annotations.IsDeterministic);
                Assert.AreEqual(0, entity.Annotations.KvTags.Count);
                Assert.AreEqual(0, entity.Annotations.Properties.Count);

                Assert.AreEqual(ModuleVersionId_1, entity.Properties.Id);
                Assert.AreEqual(AmlModuleId, entity.Properties.AmlModuleId);
            }
            else if (version == "2")
            {
                Assert.AreEqual("AddAndMultiply", entity.Annotations.AmlModuleName);
                Assert.AreEqual("AddAndMultiply", entity.Annotations.ModuleName);
                Assert.AreEqual("middle", entity.Annotations.Description);
                Assert.AreEqual("2", entity.Annotations.ModuleVersion);

                Assert.NotNull(entity.Annotations.Inputs);
                Assert.AreEqual(2, entity.Annotations.Inputs.Count);
                Assert.AreEqual("in1", entity.Annotations.Inputs[0].Name);
                Assert.AreEqual(2, entity.Annotations.Inputs[0].DataTypeIdsList.Count);
                Assert.AreEqual("AnyFile", entity.Annotations.Inputs[0].DataTypeIdsList[0]);
                Assert.AreEqual("AnyDirectory", entity.Annotations.Inputs[0].DataTypeIdsList[1]);
                Assert.AreEqual("in2", entity.Annotations.Inputs[1].Name);
                Assert.AreEqual(2, entity.Annotations.Inputs[1].DataTypeIdsList.Count);
                Assert.AreEqual("AnyFile", entity.Annotations.Inputs[1].DataTypeIdsList[0]);
                Assert.AreEqual("AnyDirectory", entity.Annotations.Inputs[1].DataTypeIdsList[1]);

                Assert.NotNull(entity.Annotations.Outputs);
                Assert.AreEqual(3, entity.Annotations.Outputs.Count);
                Assert.AreEqual("out_sum", entity.Annotations.Outputs[0].Name);
                Assert.AreEqual("AnyFile", entity.Annotations.Outputs[0].DataTypeId);
                Assert.AreEqual("out_prod", entity.Annotations.Outputs[1].Name);
                Assert.AreEqual("AnyFile", entity.Annotations.Outputs[1].DataTypeId);
                Assert.AreEqual("_run_after_output", entity.Annotations.Outputs[2].Name);
                Assert.AreEqual("AnyFile", entity.Annotations.Outputs[2].DataTypeId);

                Assert.AreEqual(null, entity.Annotations.Category);
                Assert.AreEqual("ModuleStep", entity.Annotations.StepType);
                Assert.AreEqual(EntityStatus.Active, entity.Annotations.EntityStatus);
                Assert.AreEqual(true, entity.Annotations.IsDeterministic);
                Assert.AreEqual(0, entity.Annotations.KvTags.Count);
                Assert.AreEqual(0, entity.Annotations.Properties.Count);

                Assert.AreEqual(ModuleVersionId_2, entity.Properties.Id);
                Assert.AreEqual(AmlModuleId, entity.Properties.AmlModuleId);
            }
        }

        private void AssertAmlModuleIndexEntity(AmlModuleIndexEntity entity)
        {
            Assert.AreEqual(EntityKind.LineageRoot, entity.Kind);
            Assert.AreEqual("AddAndMultiply", entity.Annotations.Name);
            Assert.AreEqual("A module that adds and multiplies", entity.Annotations.Description);
            Assert.AreEqual(1, entity.Annotations.Labels.Count);
            Assert.IsTrue(entity.Annotations.Labels.ContainsKey("default"));
            Assert.AreEqual("2", entity.Annotations.Labels["default"]);
        }

        [OneTimeSetUp]
        public void SetupTestEnvironment()
        {
            WorkspaceId = _testEnvironment.WorkspaceIdentity.WorkspaceId;
            var testRelInfraConfig = new TestServiceConfig(GetAetherEndpoint());
            var clientConfig = new MetaStoreProxyClientConfig(testRelInfraConfig);
            var tokenProvider = new TestS2STokenProvider();
            var testTokenProvider = new MetaStoreProxyServiceTestS2STokenProvider(tokenProvider.GetTokenAsync().Result);
            var counters = new CounterManager("MetaStoreProxyClientTest", new LoggingCounterFactory());
            _client = new MetaStoreProxyClientFactory(clientConfig, testTokenProvider, counters).GetClient();
        }
    }
}
