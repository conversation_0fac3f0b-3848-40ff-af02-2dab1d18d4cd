﻿using BBAetherLibrary.IntegrationTests.Common;
using Microsoft.Aether.BackendCommon.ClusterHealthPoller;
using Microsoft.MachineLearning.PolicyService.Contracts;
using Microsoft.MachineLearning.PolicyServiceClient;
using Microsoft.RelInfra.Instrumentation;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;

namespace BBAetherLibrary.PolicyService.IntegrationTests
{
    [TestFixture]
    [NonParallelizable]
    class PolicyServiceClientTests : BBAetherTestFixtureBase
    {
        private PolicyServiceClientConfig _policyServiceClientConfig;
        private PolicyServiceClient _policyServiceClient;
        private WorkspaceIdentityDto _workspaceIdentity;

        private const int MAX_TIMEOUT_IN_SECOND = 300;
        private const int INTERVAL_IN_SECONDS = 15;

        private const string DefaultRunId = "3b1eacda-f368-4968-bbdd-e60116f69585";
        private const string DefaultParentRunId = "2442c637-8f5e-4106-a609-f6340878c34f";
        private EnvironmentDefinitionDto baseEnvironment = new EnvironmentDefinitionDto()
        {
            CondaPackages = new List<string>() { "\"python = 3.8\"", "{\"pip\": [\"azureml-defaults\"]}" },
            CondaChannels = new List<string>(),
            PipPackages = new List<string>() { "azureml-defaults" },
            BaseImage = "mcr.microsoft.com/azureml/base:openmpi4.1.0-ubuntu20.04",
            ContainerRegistry = "",
        };

        [OneTimeSetUp]
        public void SetUpBaseTestEnvironmentForPolicyService()
        {
            var runParameters = TestContext.Parameters;

            _workspaceIdentity = new WorkspaceIdentityDto()
            {
                SubscriptionId = runParameters.Get("SubscriptionId"),
                ResourceGroupName = runParameters.Get("PolicyServiceE2ETestResourceGroup"),
                WorkspaceName = runParameters.Get("PolicyServiceE2ETestWorkspaceName"),
                WorkspaceId = runParameters.Get("PolicyServiceE2ETestWorkspaceId")
            };

            TestServiceConfig policyServiceClientRelInfraConfig = new TestServiceConfig(GetAetherEndpoint());
            _policyServiceClientConfig = new PolicyServiceClientConfig(policyServiceClientRelInfraConfig);
            PolicyServiceTestS2sTokenProvider testServiceTestS2STokenProvider = new PolicyServiceTestS2sTokenProvider(_tokenProvider.GetAccessTokenAsync().Result);
            var counters = new CounterManager("PolicyServiceClientTest", new LoggingCounterFactory());
            var clusterHealthPoller = new ClusterHealthPoller();
            _policyServiceClient = new PolicyServiceClient(_policyServiceClientConfig, testServiceTestS2STokenProvider, counters, clusterHealthPoller);
        }

        /// <summary>
        /// Create ValidationRequestDto using approved Python Packages and verify ValidationStatus will be Succeeded.
        /// </summary>
        [Test]
        public async Task PolicyServiceClientTestWithApprovedPythonPackage()
        {
            var computeName = "policy-python";
            string runId = "2bb3c59c-014b-4373-9c75-e56272418413";
            string parentRunId = "0e7b772b-a939-42d6-ac3f-cc63675bd20d";
            WorkspaceIdentityDto workspaceIdentity = new WorkspaceIdentityDto()
            {
                SubscriptionId = "b8c23406-f9b5-4ccb-8a65-a8cb5dcd6a5a",
                ResourceGroupName = "rg-e2epolicytest",
                WorkspaceName = "ws-e2epolicytest",
                WorkspaceId = "fbf238bc-b169-414a-8936-2ba56bf1c660"
            };

            // The module requires pip packages from azureml-defaults, which is allowed for CI "cpu-cluster"
            var environmentDefinition = new EnvironmentDefinitionDto()
            {
                CondaPackages = new List<string>() { "\"python = 3.8\"", "{\"pip\": [\"azureml-defaults\"]}" },
                CondaChannels = new List<string>(),
                PipPackages = new List<string>() { "azureml-defaults" },
                BaseImage = "acrplcye2etests.azurecr.io/base:openmpi4.1.0-ubuntu20.04",
                ContainerRegistry = "",
            };

            ValidationRequestDto validationRequestDto = ConstructValidationRequestDtoWithParameters(
                computeName: computeName,
                environmentDefinition: environmentDefinition,
                workspaceIdentity: workspaceIdentity,
                runId: runId,
                parentRunId: parentRunId);

            ValidationResponseDto validationResponse = await SendValidationRequestAndWaitForVerificationWithMaxTimeOut(
                policyServiceClient: _policyServiceClient,
                validationRequestDto: validationRequestDto,
                workspaceIdentity: workspaceIdentity,
                testName: "PolicyServiceClientTestWithApprovedPythonPackage");

            Assert.AreEqual(ValidationStatus.Succeeded, validationResponse.Status);

            //  Verify that the Python policy was checked
            var policies = validationResponse.Policies.ToList();
            Assert.AreEqual(policies.Count, 1);
            Assert.AreEqual(policies[0].PolicyName, PolicyEffectName.AllowedPythonPackageChannels);
        }

        /// <summary>
        /// Create a ValidationRequestDto with not allowed Python / Pip packages and verify ValidationStatus will be Failed.
        /// </summary>
        [TestCase]
        public async Task PolicyServiceClientTestWithDeniedPythonPackage()
        {
            var computeName = "policy-python";
            string runId = "2bb3c59c-014b-4373-9c75-e56272418413";
            string parentRunId = "0e7b772b-a939-42d6-ac3f-cc63675bd20d";
            WorkspaceIdentityDto workspaceIdentity = new WorkspaceIdentityDto()
            {
                SubscriptionId = "b8c23406-f9b5-4ccb-8a65-a8cb5dcd6a5a",
                ResourceGroupName = "rg-e2epolicytest",
                WorkspaceName = "ws-e2epolicytest",
                WorkspaceId = "fbf238bc-b169-414a-8936-2ba56bf1c660"
            };

            // The module would requires a Pip-package which is not allowed for this workspace. Verify the job fails.
            var environmentDefinition = new EnvironmentDefinitionDto()
            {
                CondaPackages = new List<string>() { "python = 3.8", "{\"pip\": [\"--index-url https://azuremlsdktestpypi.azureedge.net/sdk-release/master/588E708E0DF342C4A80BD954289657CF\", \"--extra-index-url https://pypi.python.org/simple\", \"azureml-sdk<0.1.1\", \"azureml-dataprep[fuse,pandas]\", \"azureml-telemetry\", \"--pre\"]}" },
                CondaChannels = new List<string>(),
                PipPackages = new List<string>() { "--index-url https://azuremlsdktestpypi.azureedge.net/sdk-release/master/588E708E0DF342C4A80BD954289657CF", "--extra-index-url https://pypi.python.org/simple", "azureml-sdk<0.1.1", "azureml-dataprep[fuse,pandas]", "azureml-telemetry", "--pre" },
                BaseImage = "acrplcye2etests.azurecr.io/base:openmpi4.1.0-ubuntu20.04",
                ContainerRegistry = "",
            };
            ValidationRequestDto validationRequestDto = ConstructValidationRequestDtoWithParameters(
                computeName: computeName,
                environmentDefinition: environmentDefinition,
                workspaceIdentity: workspaceIdentity,
                runId: runId,
                parentRunId: parentRunId);

            ValidationResponseDto validationResponse = await SendValidationRequestAndWaitForVerificationWithMaxTimeOut(
                policyServiceClient: _policyServiceClient,
                validationRequestDto: validationRequestDto,
                workspaceIdentity: workspaceIdentity,
                testName: "PolicyServiceClientTestWithDeniedPythonPackage");

            Assert.AreEqual(ValidationStatus.Failed, validationResponse.Status);

            //  Verify that the Python policy was checked
            var policies = validationResponse.Policies.ToList();
            Assert.AreEqual(policies.Count, 1);
            Assert.AreEqual(policies[0].PolicyName, PolicyEffectName.AllowedPythonPackageChannels);
        }

        /// <summary>
        /// Create a ValidationRequestDto with docker image from approved ACR, and verify ValidationStatus will be Succeeded.
        /// </summary>
        [TestCase]
        public async Task PolicyServiceClientTestWithApprovedACR()
        {
            var computeName = "policy-acr";
            string runId = "2bb3c59c-014b-4373-9c75-e56272418413";
            string parentRunId = "0e7b772b-a939-42d6-ac3f-cc63675bd20d";
            WorkspaceIdentityDto workspaceIdentity = new WorkspaceIdentityDto()
            {
                SubscriptionId = "b8c23406-f9b5-4ccb-8a65-a8cb5dcd6a5a",
                ResourceGroupName = "rg-e2epolicytest",
                WorkspaceName = "ws-e2epolicytest",
                WorkspaceId = "fbf238bc-b169-414a-8936-2ba56bf1c660"
            };

            // The module requires a docker image from approved Container Registry. Verify the job succeeds.
            var environmentDefinition = new EnvironmentDefinitionDto()
            {
                CondaPackages = new List<string>() { "\"python = 3.8\"", "{\"pip\": [\"azureml-defaults\"]}" },
                CondaChannels = new List<string>(),
                PipPackages = new List<string>() { "azureml-defaults" },
                BaseImage = "acrplcye2etests.azurecr.io/base:openmpi4.1.0-ubuntu20.04",
                ContainerRegistry = "acrplcye2etests.azurecr.io",
            };

            ValidationRequestDto validationRequestDto = ConstructValidationRequestDtoWithParameters(
                computeName: computeName,
                environmentDefinition: environmentDefinition,
                workspaceIdentity: workspaceIdentity,
                runId: runId,
                parentRunId: parentRunId);

            ValidationResponseDto validationResponse = await SendValidationRequestAndWaitForVerificationWithMaxTimeOut(
                policyServiceClient: _policyServiceClient,
                validationRequestDto: validationRequestDto,
                workspaceIdentity: workspaceIdentity,
                testName: "PolicyServiceClientTestWithApprovedACR");

            Assert.AreEqual(ValidationStatus.Succeeded, validationResponse.Status);

            //  Verify that the ACR policy was checked
            var policies = validationResponse.Policies.ToList();
            Assert.AreEqual(policies.Count, 1);
            Assert.AreEqual(policies[0].PolicyName, PolicyEffectName.AllowedACRs);
        }

        /// <summary>
        /// Create a ValidationRequestDto with a docker image from an ACR not allowed and verify ValidationStatus will be Failed.
        /// </summary>
        [Test]
        public async Task PolicyServiceClientTestWithDeniedACR()
        {
            var computeName = "policy-acr";
            string runId = "2bb3c59c-014b-4373-9c75-e56272418413";
            string parentRunId = "0e7b772b-a939-42d6-ac3f-cc63675bd20d";
            WorkspaceIdentityDto workspaceIdentity = new WorkspaceIdentityDto()
            {
                SubscriptionId = "b8c23406-f9b5-4ccb-8a65-a8cb5dcd6a5a",
                ResourceGroupName = "rg-e2epolicytest",
                WorkspaceName = "ws-e2epolicytest",
                WorkspaceId = "fbf238bc-b169-414a-8936-2ba56bf1c660"
            };

            // The module requires a docker image from not-allowed Container Registry. Verify the job fails.
            var environmentDefinition = new EnvironmentDefinitionDto()
            {
                CondaPackages = new List<string>() { "\"python = 3.8\"", "{\"pip\": [\"azureml-defaults\"]}" },
                CondaChannels = new List<string>(),
                PipPackages = new List<string>() { "azureml-defaults" },
                BaseImage = "acrplcye2eteststobedenied.azurecr.io/base:openmpi4.1.0-ubuntu20.04",
                ContainerRegistry = "badacrplcye2eteststobedenied.azurecr.io",
            };

            ValidationRequestDto validationRequestDto = ConstructValidationRequestDtoWithParameters(
                computeName: computeName,
                environmentDefinition: environmentDefinition,
                workspaceIdentity: workspaceIdentity,
                runId: runId,
                parentRunId: parentRunId);

            ValidationResponseDto validationResponse = await SendValidationRequestAndWaitForVerificationWithMaxTimeOut(
                policyServiceClient: _policyServiceClient,
                validationRequestDto: validationRequestDto,
                workspaceIdentity: workspaceIdentity,
                testName: "PolicyServiceClientTestWithDeniedACR");

            Assert.AreEqual(ValidationStatus.Failed, validationResponse.Status);

            //  Verify that the ACR policy was checked
            var policies = validationResponse.Policies.ToList();
            Assert.AreEqual(policies.Count, 1);
            Assert.AreEqual(policies[0].PolicyName, PolicyEffectName.AllowedACRs);
        }

        /// <summary>
        /// Used previous StepRun info to verify that a module created by approved author could be allowed by Policy Service.
        /// </summary>
        [Test]
        public async Task PolicyServiceClientTestWithApprovedModuleAuthor()
        {
            var computeName = "policy-author";

            //  The module ID is pulled from the StepRun which uses that module.  This run uses the same module from
            //  the 'approved module signature' run
            string runId = "2bb3c59c-014b-4373-9c75-e56272418413";
            string parentRunId = "0e7b772b-a939-42d6-ac3f-cc63675bd20d";
            WorkspaceIdentityDto workspaceIdentity = new WorkspaceIdentityDto()
            {
                SubscriptionId = "b8c23406-f9b5-4ccb-8a65-a8cb5dcd6a5a",
                ResourceGroupName = "rg-e2epolicytest",
                WorkspaceName = "ws-e2epolicytest",
                WorkspaceId = "fbf238bc-b169-414a-8936-2ba56bf1c660"
            };
            ValidationRequestDto validationRequestDto = ConstructValidationRequestDtoWithParameters(
                computeName: computeName,
                environmentDefinition: baseEnvironment,
                runId: runId,
                parentRunId: parentRunId,
                workspaceIdentity: workspaceIdentity);

            ValidationResponseDto validationResponse = await SendValidationRequestAndWaitForVerificationWithMaxTimeOut(
                policyServiceClient: _policyServiceClient,
                validationRequestDto: validationRequestDto,
                workspaceIdentity: workspaceIdentity,
                testName: "PolicyServiceClientTestWithApprovedModuleAuthor");

            Assert.AreEqual(ValidationStatus.Succeeded, validationResponse.Status);

            //  Verify that the author policy was checked
            var policies = validationResponse.Policies.ToList();
            Assert.AreEqual(policies.Count, 1);
            Assert.AreEqual(policies[0].PolicyName, PolicyEffectName.AllowedModuleAuthors);
        }

        /// <summary>
        /// Used previous StepRun info to verify that a module created by not-allowed author could be rejected by Policy Service.
        /// </summary>
        [Test]
        public async Task PolicyServiceClientTestWithDeniedModuleAuthor()
        {
            var computeName = "policy-badauthor";

            //  The module ID is pulled from the StepRun which uses that module.  This run uses the same module from
            //  the 'approved module signature' run
            string runId = "2bb3c59c-014b-4373-9c75-e56272418413";
            string parentRunId = "0e7b772b-a939-42d6-ac3f-cc63675bd20d";
            WorkspaceIdentityDto workspaceIdentity = new WorkspaceIdentityDto()
            {
                SubscriptionId = "b8c23406-f9b5-4ccb-8a65-a8cb5dcd6a5a",
                ResourceGroupName = "rg-e2epolicytest",
                WorkspaceName = "ws-e2epolicytest",
                WorkspaceId = "fbf238bc-b169-414a-8936-2ba56bf1c660"
            };
            ValidationRequestDto validationRequestDto = ConstructValidationRequestDtoWithParameters(
                computeName: computeName,
                environmentDefinition: baseEnvironment,
                runId: runId,
                parentRunId: parentRunId,
                workspaceIdentity: workspaceIdentity);

            ValidationResponseDto validationResponse = await SendValidationRequestAndWaitForVerificationWithMaxTimeOut(
                policyServiceClient: _policyServiceClient,
                validationRequestDto: validationRequestDto,
                workspaceIdentity: workspaceIdentity,
                testName: "PolicyServiceClientTestWithDeniedModuleAuthor");

            Assert.AreEqual(ValidationStatus.Failed, validationResponse.Status);

            //  Verify that the author policy was checked
            var policies = validationResponse.Policies.ToList();
            Assert.AreEqual(policies.Count, 1);
            Assert.AreEqual(policies[0].PolicyName, PolicyEffectName.AllowedModuleAuthors);
        }

        [Test]
        public async Task PolicyServiceClientTestWithApprovedModuleSigning()
        {
            //  Module snapshot has a catalog file which is correctly signed
            string runId = "2bb3c59c-014b-4373-9c75-e56272418413";
            string parentRunId = "0e7b772b-a939-42d6-ac3f-cc63675bd20d";
            var computeName = "policy-signed";
            WorkspaceIdentityDto workspaceIdentity = new WorkspaceIdentityDto()
            {
                SubscriptionId = "b8c23406-f9b5-4ccb-8a65-a8cb5dcd6a5a",
                ResourceGroupName = "rg-e2epolicytest",
                WorkspaceName = "ws-e2epolicytest",
                WorkspaceId = "fbf238bc-b169-414a-8936-2ba56bf1c660"
            };
            ValidationRequestDto validationRequestDto = ConstructValidationRequestDtoWithParameters(computeName: computeName, environmentDefinition: baseEnvironment, runId: runId, parentRunId: parentRunId,
                workspaceIdentity: workspaceIdentity);
            validationRequestDto.ExecutionContext.ModuleInfo.SnapshotIds = new List<string>() { "f8fc9a1e-8540-4050-a273-31bd9e4cb9ad" };
            ValidationResponseDto validationResponse = await SendValidationRequestAndWaitForVerificationWithMaxTimeOut(
                policyServiceClient: _policyServiceClient,
                validationRequestDto: validationRequestDto,
                workspaceIdentity: workspaceIdentity,
                testName: "PolicyServiceClientTestModuleSigning");

            Assert.AreEqual(ValidationStatus.Succeeded, validationResponse.Status);

            //  Verify that the signing key policy was checked
            var policies = validationResponse.Policies.ToList();
            Assert.AreEqual(policies.Count, 1);
            Assert.AreEqual(policies[0].PolicyName, PolicyEffectName.SigningKeys);
        }

        [Test]
        public async Task PolicyServiceClientTestWithBadModuleSignature()
        {
            //  Module snapshot has a catalog file which does not match the signature
            string runId = "d603d764-b6af-4a71-92b9-9c5fb98b090f";
            string parentRunId = "0e7b772b-a939-42d6-ac3f-cc63675bd20d";
            var computeName = "policy-signed";
            WorkspaceIdentityDto workspaceIdentity = new WorkspaceIdentityDto()
            {
                SubscriptionId = "b8c23406-f9b5-4ccb-8a65-a8cb5dcd6a5a",
                ResourceGroupName = "rg-e2epolicytest",
                WorkspaceName = "ws-e2epolicytest",
                WorkspaceId = "fbf238bc-b169-414a-8936-2ba56bf1c660"
            };
            ValidationRequestDto validationRequestDto = ConstructValidationRequestDtoWithParameters(computeName: computeName, environmentDefinition: baseEnvironment, runId: runId, parentRunId: parentRunId,
                workspaceIdentity: workspaceIdentity);
            validationRequestDto.ExecutionContext.ModuleInfo.SnapshotIds = new List<string>() { "6b69a12d-ee0c-4da0-96cb-a26784b9700f" };
            ValidationResponseDto validationResponse = await SendValidationRequestAndWaitForVerificationWithMaxTimeOut(
                policyServiceClient: _policyServiceClient,
                validationRequestDto: validationRequestDto,
                workspaceIdentity: workspaceIdentity,
                testName: "PolicyServiceClientTestModuleSigning");

            Assert.AreEqual(ValidationStatus.Failed, validationResponse.Status);

            var policies = validationResponse.Policies.ToList();
            Assert.AreEqual(policies.Count, 1);
            Assert.IsTrue(policies[0].ReasonPhrase.Contains("Could not validate GPG signature file"));
        }

        [Test]
        public async Task PolicyServiceClientTestWithApprovedModuleSignature_WithSystemCreatedSnapshots()
        {
            //  ParallelRunStep modules will create an extra snapshot for the system code.
            //  The system created snapshot should not be verified with the signature.
            //  The related data were retrived from the following run:
            //  https://ml.azure.com/experiments/id/1908fee1-7264-4815-93b0-099168e26162/runs/b1c89bc8-7506-4a7e-8abc-fe0441f683bd?wsid=/subscriptions/b8c23406-f9b5-4ccb-8a65-a8cb5dcd6a5a/resourcegroups/rg-e2epolicytest/workspaces/ws-e2epolicytest&tid=72f988bf-86f1-41af-91ab-2d7cd011db47#
            string runId = "51568cfb-df5e-4eaa-9e98-9fc7f7b2d934";
            string parentRunId = "b1c89bc8-7506-4a7e-8abc-fe0441f683bd";
            var computeName = "policy-m365-sign";
            WorkspaceIdentityDto workspaceIdentity = new WorkspaceIdentityDto()
            {
                SubscriptionId = "b8c23406-f9b5-4ccb-8a65-a8cb5dcd6a5a",
                ResourceGroupName = "rg-e2epolicytest",
                WorkspaceName = "ws-e2epolicytest",
                WorkspaceId = "fbf238bc-b169-414a-8936-2ba56bf1c660"
            };
            string snapshotCreatedBySystem = "e6739634-fb36-46a6-adfd-9af6a4ece2fd";
            string snapshotCreatedByUserWithApprovedSignature = "253704a5-38c9-471e-811a-e3db17f06c84";
            ValidationRequestDto validationRequestDto = ConstructValidationRequestDtoWithParameters(
                computeName: computeName,
                environmentDefinition: baseEnvironment,
                workspaceIdentity: workspaceIdentity,
                runId: runId,
                parentRunId: parentRunId);
            validationRequestDto.ExecutionContext.ModuleInfo.SnapshotIds = new List<string>() { snapshotCreatedBySystem, snapshotCreatedByUserWithApprovedSignature };
            ValidationResponseDto validationResponse = await SendValidationRequestAndWaitForVerificationWithMaxTimeOut(
                policyServiceClient: _policyServiceClient,
                validationRequestDto: validationRequestDto,
                workspaceIdentity: workspaceIdentity,
                testName: "PolicyServiceClientTestWithApprovedModuleSignature_WithSystemCreatedSnapshots");

            Assert.AreEqual(ValidationStatus.Succeeded, validationResponse.Status);

            var policies = validationResponse.Policies.Where(p => p.PolicyName == PolicyEffectName.SigningKeys).ToList();
            Assert.AreEqual(policies[0].Status, ValidationStatus.Succeeded);
        }

        [Test]
        public async Task PolicyServiceClientTestWithBadModuleSignature_WithSystemCreatedSnapshots()
        {
            //  ParallelRunStep modules will create an extra snapshot for the system code.
            //  The system created snapshot should not be verified with the signature.
            //  The related data were retrived from the following run:
            //  https://ml.azure.com/experiments/id/1908fee1-7264-4815-93b0-099168e26162/runs/b1c89bc8-7506-4a7e-8abc-fe0441f683bd?wsid=/subscriptions/b8c23406-f9b5-4ccb-8a65-a8cb5dcd6a5a/resourcegroups/rg-e2epolicytest/workspaces/ws-e2epolicytest&tid=72f988bf-86f1-41af-91ab-2d7cd011db47#
            string runId = "ebdc6c8a-a70b-4e0e-9f1e-f2965112a883";
            string parentRunId = "b1c89bc8-7506-4a7e-8abc-fe0441f683bd";
            var computeName = "policy-m365-sign";
            WorkspaceIdentityDto workspaceIdentity = new WorkspaceIdentityDto()
            {
                SubscriptionId = "b8c23406-f9b5-4ccb-8a65-a8cb5dcd6a5a",
                ResourceGroupName = "rg-e2epolicytest",
                WorkspaceName = "ws-e2epolicytest",
                WorkspaceId = "fbf238bc-b169-414a-8936-2ba56bf1c660"
            };
            string snapshotCreatedBySystem = "e6739634-fb36-46a6-adfd-9af6a4ece2fd";
            string snapshotCreatedByUserWithBadSignature = "ab6a550a-17b0-4ec4-a141-40d44a08630b";
            ValidationRequestDto validationRequestDto = ConstructValidationRequestDtoWithParameters(
                computeName: computeName,
                environmentDefinition: baseEnvironment,
                workspaceIdentity: workspaceIdentity,
                runId: runId,
                parentRunId: parentRunId);
            validationRequestDto.ExecutionContext.ModuleInfo.SnapshotIds = new List<string>() { snapshotCreatedByUserWithBadSignature, snapshotCreatedBySystem };
            ValidationResponseDto validationResponse = await SendValidationRequestAndWaitForVerificationWithMaxTimeOut(
                policyServiceClient: _policyServiceClient,
                validationRequestDto: validationRequestDto,
                workspaceIdentity: workspaceIdentity,
                testName: "PolicyServiceClientTestWithBadModuleSignature_WithSystemCreatedSnapshots");

            Assert.AreEqual(ValidationStatus.Failed, validationResponse.Status);

            var policies = validationResponse.Policies.Where(p => p.PolicyName == PolicyEffectName.SigningKeys).ToList();
            Assert.AreEqual(policies[0].Status, ValidationStatus.Failed);
            Assert.IsTrue(policies[0].ReasonPhrase.Contains("Module signature validation failed in catalog verification; Exception: Catalog file catalog.json missing from module snapshot"));
        }

        [Test]
        public async Task PolicyServiceClientTestWithBadModuleCatalog()
        {
            //  Module snapshot has an extra file which is not listed in the catalog
            string runId = "f9346589-00b8-4c45-9b30-8ff549308b9f";
            string parentRunId = "0e7b772b-a939-42d6-ac3f-cc63675bd20d";
            var computeName = "policy-signed";
            WorkspaceIdentityDto workspaceIdentity = new WorkspaceIdentityDto()
            {
                SubscriptionId = "b8c23406-f9b5-4ccb-8a65-a8cb5dcd6a5a",
                ResourceGroupName = "rg-e2epolicytest",
                WorkspaceName = "ws-e2epolicytest",
                WorkspaceId = "fbf238bc-b169-414a-8936-2ba56bf1c660"
            };
            ValidationRequestDto validationRequestDto = ConstructValidationRequestDtoWithParameters(computeName: computeName, environmentDefinition: baseEnvironment, runId: runId, parentRunId: parentRunId,
                workspaceIdentity: workspaceIdentity);
            validationRequestDto.ExecutionContext.ModuleInfo.SnapshotIds = new List<string>() { "0da29891-b062-44e9-8660-5c3ff0218f01" };
            ValidationResponseDto validationResponse = await SendValidationRequestAndWaitForVerificationWithMaxTimeOut(
                policyServiceClient: _policyServiceClient,
                validationRequestDto: validationRequestDto,
                workspaceIdentity: workspaceIdentity,
                testName: "PolicyServiceClientTestModuleSigning");

            Assert.AreEqual(ValidationStatus.Failed, validationResponse.Status);

            var policies = validationResponse.Policies.ToList();
            Assert.AreEqual(policies.Count, 1);
            Assert.IsTrue(policies[0].ReasonPhrase.Contains("No hash listed for file extra.txt"));
        }

        [TestCase(true, ValidationStatus.Succeeded)]
        [TestCase(false, ValidationStatus.Failed)]
        public async Task PolicyServiceClientTestWithIPProtectedParam(bool isValidIPPublisher, ValidationStatus validateStatus)
        {
            //  Module snapshot has a catalog file which does not match the signature
            string runId = "d603d764-b6af-4a71-92b9-9c5fb98b090f";
            string parentRunId = "0e7b772b-a939-42d6-ac3f-cc63675bd20d";
            var computeName = "cpucluster4";
            WorkspaceIdentityDto workspaceIdentity = new WorkspaceIdentityDto()
            {
                SubscriptionId = "b8c23406-f9b5-4ccb-8a65-a8cb5dcd6a5a",
                ResourceGroupName = "rg-e2epolicytest",
                WorkspaceName = "ws-e2epolicytest",
                WorkspaceId = "fbf238bc-b169-414a-8936-2ba56bf1c660"
            };
            ValidationRequestDto validationRequestDto = ConstructValidationRequestDtoWithParameters(
                computeName: computeName,
                environmentDefinition: baseEnvironment,
                runId: runId,
                parentRunId: parentRunId,
                workspaceIdentity: workspaceIdentity,
                IsValidIPPublisher: isValidIPPublisher);
            validationRequestDto.ExecutionContext.ModuleInfo.SnapshotIds = new List<string>() { "6b69a12d-ee0c-4da0-96cb-a26784b9700f" };
            ValidationResponseDto validationResponse = await SendValidationRequestAndWaitForVerificationWithMaxTimeOut(
                policyServiceClient: _policyServiceClient,
                validationRequestDto: validationRequestDto,
                workspaceIdentity: workspaceIdentity,
                testName: "PolicyServiceClientTestWithIPProtected");

            Assert.AreEqual(validateStatus, validationResponse.Status);
        }

        public async Task<ValidationResponseDto> SendValidationRequestAndWaitForVerificationWithMaxTimeOut(
            PolicyServiceClient policyServiceClient,
            ValidationRequestDto validationRequestDto,
            WorkspaceIdentityDto workspaceIdentity,
            int maxTimeOutInSeconds = MAX_TIMEOUT_IN_SECOND,
            string testName = "PolicyServiceClientTest")
        {

            TimeSpan timeoutSpan = TimeSpan.FromSeconds(maxTimeOutInSeconds);

            Task<ValidationResponseDto> validationTask = Task.Run(async () => await SendValidationRequestAndWaitForVerification(policyServiceClient, validationRequestDto, workspaceIdentity));

            if (await Task.WhenAny(validationTask, Task.Delay(timeoutSpan)) == validationTask)
            {
                Trace.WriteLine($"{testName} has finished.");
                ValidationResponseDto validationResponseDto = await validationTask;
                return validationResponseDto;
            }

            throw new TimeoutException($"Test was waiting for {testName} to finish, and gave up after {maxTimeOutInSeconds} seconds");
        }

        public static async Task<ValidationResponseDto> SendValidationRequestAndWaitForVerification(PolicyServiceClient policyServiceClient, ValidationRequestDto validationRequestDto, WorkspaceIdentityDto workspaceIdentity)
        {
            var postValidationRequestReceipt = await policyServiceClient.PostValidationRequestAsync(workspaceIdentity.WorkspaceId, validationRequestDto.RunId, createdBy: null, validationRequestDto);
            ValidationResponseDto validationResponse = null;
            do
            {
                await Task.Delay(TimeSpan.FromSeconds(INTERVAL_IN_SECONDS));
                validationResponse = await policyServiceClient.GetValidationStatusAsync(validationRequestDto.WorkspaceIdentity.WorkspaceId, validationRequestDto.RunId);
            } while (validationResponse.Status == ValidationStatus.InProgress);
            return validationResponse;
        }

        public ValidationRequestDto ConstructValidationRequestDtoWithParameters(
            string computeName,
            EnvironmentDefinitionDto environmentDefinition,
            string runId = DefaultRunId,
            string parentRunId = DefaultParentRunId,
            WorkspaceIdentityDto workspaceIdentity = null,
            bool IsValidIPPublisher = false)
        {
            if (workspaceIdentity == null)
            {
                workspaceIdentity = _workspaceIdentity;
            }
            ValidationRequestDto validationRequestDto = new ValidationRequestDto
            {
                RunId = runId,
                ParentRunId = parentRunId,
                ExperimentName = "IntegrationTestNew",
                WorkspaceIdentity = workspaceIdentity,
                ComputeIdentity = new ComputeIdentityDto()
                {
                    ComputeName = computeName,
                    Type = ComputeType.MachineLearningCompute,
                },
                ExecutionContext = new ExecutionContextDto
                {
                    ModuleInfo = new ModuleInfoDto()
                    {
                        SnapshotIds = new List<string>() { "87f0ed89-d0a1-413c-ad9c-445a3d3a9f4b" },
                        Executable = "python",
                        UserCode = "simple_count.py",
                        Arguments = ""
                    },
                    EnvironmentDefinition = environmentDefinition,
                    DataLocations = new List<DataLocationDto>(),
                    IsValidIntellectualPropertyPublisher = IsValidIPPublisher
                },
                UserIdentity = new UserIdentityDto()
                {
                    AzureTenantId = "72f988bf-86f1-41af-91ab-2d7cd011db47",
                    UserObjectId = "20ee6e52-014c-4404-ba84-8a9631950496",
                    UPN = "UPN"
                }
            };
            return validationRequestDto;
        }
    }
}
