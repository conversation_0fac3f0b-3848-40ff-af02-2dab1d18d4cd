﻿using Microsoft.Aether.S2S.Common;
using System;
using System.Threading.Tasks;

namespace BBAetherLibrary.PolicyService.IntegrationTests
{
    /// <summary>
    /// This is the class to Provide token to access Policy Service Api from AETHER_APIENDPOINT
    /// </summary>
    class PolicyServiceTestS2sTokenProvider : IS2STokenProvider
    {
        public string AppId { get; }
        public string AppKey { get; }
        private string azureToken;
        public PolicyServiceTestS2sTokenProvider(String azureToken)
        {
            this.azureToken = azureToken;
        }

        public Task<string> GetTokenAsync()
        {
            return Task.Run(() =>
            {
                return azureToken;
            });
        }
    }
}
