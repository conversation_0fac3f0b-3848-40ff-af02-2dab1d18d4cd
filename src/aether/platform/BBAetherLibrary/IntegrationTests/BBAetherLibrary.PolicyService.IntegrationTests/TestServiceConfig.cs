﻿using Microsoft.RelInfra.Common.Configuration;
using Microsoft.RelInfra.Extensions;
using System;
using System.Collections.Generic;

namespace BBAetherLibrary.PolicyService.IntegrationTests
{
    class TestServiceConfig : IRelInfraConfiguration
    {
        public string AmlServicesEndpoint;
        public Dictionary<string, string> testConfigurationProperties;

        /// <summary>
        /// This is an IRelInfraConfiguration being consumed by PolicyServiceClient for a dynamically-generated ServiceEndpoint
        /// </summary>
        public TestServiceConfig(String AmlServicesEndpoint)
        {
            // This is a service configuration mainly for the test purpose, similar to BBSharedSettings.ini.
            this.AmlServicesEndpoint = AmlServicesEndpoint;
            testConfigurationProperties = new Dictionary<string, string>() {
                { "AmlServices.AmlServicesEndpoint", AmlServicesEndpoint },
                { "PolicyService.PostValidationRequestPath", "{endpoint}/policy/v1.0/workspaces/{workspaceId}/runs/{runId}/validate"},
                { "PolicyService.GetValidationStatusPath", "{endpoint}/policy/v1.0/workspaces/{workspaceId}/runs/{runId}/status"},
                { "PolicyService.GetPoliciesPath", "{endpoint}/policy/v1.0/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.MachineLearningServices/workspaces/{workspaceName}/runId/{runId}/policies/?computeName={computeName}"},
                { "PolicyService.EnablePolicyService", "false"},
                { "PolicyService.AllowedSubscriptionId", ""}
            };
        }

        public IEnumerable<string> ConfigFileLocationList => throw new NotImplementedException();

        public Dictionary<string, string> GetAllParameters()
        {
            return testConfigurationProperties;
        }

        public string GetConfigurationSettingValue(string configurationSettingName)
        {
            return testConfigurationProperties.GetOrDefault(configurationSettingName, "");
        }

        public void RegisterConfigurationsEventHandler(EventHandler<ConfigurationUpdateEventArgs> updateHandler)
        {
            // pass
        }
    }
}
