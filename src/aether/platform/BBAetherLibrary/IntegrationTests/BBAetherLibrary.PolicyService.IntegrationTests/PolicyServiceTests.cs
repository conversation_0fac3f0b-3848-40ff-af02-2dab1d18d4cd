﻿using Microsoft.Aether.BlueBox.Library;
using Microsoft.Aether.AEVA.DataContracts;
using NUnit.Framework;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using BBAetherLibrary.IntegrationTests.Common;
using System.Linq;
using System.Net.Http;
using Microsoft.MachineLearning.RunHistory.Contracts;
using Microsoft.RelInfra.Extensions;
using Microsoft.MachineLearning.Artifact.Contracts;
using Common.Core.Contracts;
using Microsoft.MachineLearning.Common.AzureStorage;
using System.IO;

namespace BBAetherLibrary.PolicyService.IntegrationTests
{
    [TestFixture]
    class PolicyServiceTests : BBAetherTestFixtureBase
    {
        public const string sourcePath = "TestResources/EscloudFile/helloworld";
        public const string amlComputeType = "AmlCompute";
        public const string moduleOutputName = "output";
        public const string datastoreName = "myblobstorage2";
        public const string computeTargetName = "cpu-cluster";

        private const string EsCloudFilePath = "TestResources/EscloudFile";
        private const string ComputeTargetWithSigningPolicy = "cpucluster3";
        private const string ComputeTargetWithoutSigningPolicy = "cpu-cluster";

        // Policies have been applied to the test workspace with the following allowed values
        // private string[] AllowedContainerRegistry = new string[] { "acrplcye2etests.azurecr.io", "acrplcye2etests" };
        // private const string AllowedPythonPackageRepo = "https://pypi.org/simple";
        // private const string AllowedModuleAuthor = "0274ee43-7962-468c-8240-588e93fe99a4";

        private const string DefaultScript = "simple_count.py";
        private const string DefaultAzureCondaDependency = "{\"name\": \"project_environment\", \"dependencies\": [\"python = 3.8\", {\"pip\": [\"azureml-defaults\"]}]}";
        private const string ModuleIdWithApprovedAuthor = "84db505e-ee19-4e32-9a67-c3dc03b57716"; // created by "e7968063-1a37-4ecd-96a7-54f8d5dee0ce" with cpucluster2 as compute.
        private const string ModuleIdWithDeniedAuthor = "7b86711e-e582-436f-a7cf-27158968d60a"; // created by "c4d20c60-d63f-4ea6-9700-7c34eb3dfd08" with cpucluster2 as compute.
        private static readonly List<StructuredInterfaceParameter> DefaultDockerParameters = new List<StructuredInterfaceParameter>
        {
            new StructuredInterfaceParameter { Name = "BaseDockerImage", DefaultValue = "mcr.microsoft.com/azureml/openmpi4.1.0-ubuntu20.04", ParameterType = ParameterType.String, IsOptional = false }
        };
        private static readonly List<StructuredInterfaceParameter> AllowedDockerParameters = new List<StructuredInterfaceParameter>
        {
            new StructuredInterfaceParameter { Name = "BaseDockerImage", DefaultValue = "mcr.microsoft.com/azureml/openmpi4.1.0-ubuntu20.04", ParameterType = ParameterType.String, IsOptional = false },
        };

        /// <summary>
        /// used to retrieve run error details
        /// </summary>
        private HttpClient _runHistoryClient;

        /// <summary>
        /// used to retrieve policy log file for run, when the run does not fail with policy error
        /// </summary>
        private HttpClient _artifactClient;

        private AetherEnvironment _policyTestEnvironment;
        private TestEnvironmentConfig _policyTestConfig;

        [OneTimeSetUp]
        public void SetUpBaseTestEnvironmentForPolicyService()
        {
            var runParameters = TestContext.Parameters;

            _policyTestConfig = new TestEnvironmentConfig
            {
                SubscriptionId = runParameters.Get("SubscriptionId"),
                ResourceGroup = runParameters.Get("PolicyServiceE2ETestResourceGroup"),
                WorkspaceName = runParameters.Get("PolicyServiceE2ETestWorkspaceName"),
                BlobDatastoreName = runParameters.Get("PolicyServiceE2ETestBlobDatastoreName"),
                AmlComputeName = runParameters.Get("PolicyServiceE2ETestAmlComputeName")
            };
            _policyTestEnvironment = AetherEnvironment.Create(
                subscriptionId: _policyTestConfig.SubscriptionId,
                resourceGroupName: _policyTestConfig.ResourceGroup,
                workspaceName: _policyTestConfig.WorkspaceName,
                endpointAddress: GetAetherEndpoint(),
                apiVersion: AetherEnvironment.DefaultApiVersion,
                accessTokenProvider: _tokenProvider.GetAccessTokenAsync,
                snapshotEndpoint: AetherEnvironment.DefaultSnapshotEndpointAddress,
                dstsHttpMessageHandlerProvider: null,
                onBehalfOfAzureUser: null,
                retryPolicy: AetherEnvironment.DefaultRetryPolicy,
                userAgent: $"bbaetherlibary/integrationtest");

            SetupHttpClients();
        }

        /// <summary>
        /// Create a simple graph with one module executing "simple_count.py".
        /// For any compute
        /// </summary>
        [Test]
        public async Task TestJobWithApprovedPythonRepo()
        {
            IGraph graph = await CreateSingleModuleGraphWithApprovedPythonDependency(
                environment: _policyTestEnvironment,
                sourcePath: sourcePath);
            IPipelineRun pipelineRun = await RunTestWithLogsAsync(graph, _policyTestEnvironment);
            Assert.AreEqual(PipelineRunStatusCode.Finished, pipelineRun.CachedStatus, $"PipelineRun id: {pipelineRun.Id}");
        }

        /// <summary>
        /// Create a simple graph with denied conda dependencies executing "simple_count.py"
        /// For any compute
        /// </summary>
        [Test]
        public async Task TestJobWithDeniedPythonRepo()
        {
            IGraph graph = await CreateSingleModuleGraphWithDeniedPythonDependency(
                environment: _policyTestEnvironment,
                sourcePath: sourcePath);
            IPipelineRun pipelineRun = await RunTestWithLogsAsync(graph, _policyTestEnvironment);

            Assert.AreEqual(PipelineRunStatusCode.Failed, pipelineRun.CachedStatus, $"PipelineRun id: {pipelineRun.Id}");

            await ValidatePolicyError(pipelineRun, "AllowedACRs status is Succeeded; (Definition Id: /subscriptions/{0}/providers/Microsoft.Authorization/policyDefinitions/b8d1923e-60fd-469e-a717-283d67f2234f, Assignment Id: /subscriptions/{0}/resourceGroups/{1}/providers/Microsoft.Authorization/policyAssignments/49b655d6863c424b8e387824) AllowedPythonPackageChannels validation failed - the values supplied are not allowed; (Assignment Id: /subscriptions/{0}/resourceGroups/{1}/providers/Microsoft.Authorization/policyAssignments/ae2ab2bb40eb42009ab2123a, ReasonPhrase: PipPackage Requirements file line: 1 is invalidated. Reason is: https://azuremlsdktestpypi.azureedge.net/sdk-release/master/588E708E0DF342C4A80BD954289657CF is not allowed feed URL..) ");
        }

        /// <summary>
        /// Create a simple graph with one module executing "simple_count.py", but with approved container registry
        /// Comptue is cpu-cluster
        /// </summary>
        [Test]
        public async Task TestJobWithApprovedAzureContainerRegistry()
        {
            IGraph graph = await CreateSingleModuleGraphWithApprovedContainerRegistry(
                environment: _policyTestEnvironment,
                sourcePath: sourcePath);
            IPipelineRun pipelineRun = await RunTestWithLogsAsync(graph, _policyTestEnvironment);
            Assert.AreEqual(PipelineRunStatusCode.Finished, pipelineRun.CachedStatus, $"PipelineRun id: {pipelineRun.Id}");
        }

        /// <summary>
        /// Create a simple graph with one module executing "simple_count.py", but with unallowed container registry
        /// </summary>
        [Test]
        public async Task TestJobWithDeniedAzureContainerRegistry()
        {
            IGraph graph = await CreateSingleModuleGraphWithSpecifiedContainerRegistry(
                environment: _policyTestEnvironment,
                sourcePath: sourcePath);
            IPipelineRun pipelineRun = await RunTestWithLogsAsync(graph, _policyTestEnvironment);

            Assert.AreEqual(PipelineRunStatusCode.Failed, pipelineRun.CachedStatus, $"PipelineRun id: {pipelineRun.Id}");

            // PS error is not recorded as a run error
            // checking the PS log file instead
            var policyLog = await GetPolicyLog(pipelineRun);

            Console.WriteLine("Policy service log file content");
            Console.WriteLine();
            Console.WriteLine(policyLog);
            Console.WriteLine();

            var expectedLogFragment = $@"Validating policy: AllowedACRs (Definition Id: /subscriptions/{_policyTestConfig.SubscriptionId}/providers/Microsoft.Authorization/policyDefinitions/b8d1923e-60fd-469e-a717-283d67f2234f, Assignment Id: /subscriptions/{_policyTestConfig.SubscriptionId}/resourceGroups/{_policyTestConfig.ResourceGroup}/providers/Microsoft.Authorization/policyAssignments/49b655d6863c424b8e387824).
Allowed Container Registry list: acrplcye2etests.azurecr.io, acrplcye2etests, mcr.microsoft.com, mcr.microsoft.com/azureml/.
Container Registry in this run is: acrplcye2eteststobedenied.azurecr.io
Container base image in this run is: 
Allow IP Protected Component: None
Is valid Intellectual Property Publisher: False
Validation result: Failed.
Failure reason: Container Registry acrplcye2eteststobedenied.azurecr.io with base image  is not allowed according to policy; because this job has docker file.";

            Console.WriteLine("Expected log fragment");
            Console.WriteLine();
            Console.WriteLine(expectedLogFragment);
            Console.WriteLine();

            Assert.IsTrue(policyLog.Contains(expectedLogFragment, StringComparison.OrdinalIgnoreCase), $"Unable to find expected log fragment in PipelineRun id: {pipelineRun.Id}");
        }

        /// <summary>
        /// Executing a graph with pre-configured module created by approved author
        /// Compute is "cpucluster2"
        /// </summary>
        [Test]
        public async Task TestJobWithApprovedModuleAuthor()
        {
            IGraph graph = await CreateSingleModuleGraphWithExistingModule(environment: _policyTestEnvironment, moduleId: ModuleIdWithApprovedAuthor);
            IPipelineRun pipelineRun = await RunTestWithLogsAsync(graph, _policyTestEnvironment);
            Assert.AreEqual(PipelineRunStatusCode.Finished, pipelineRun.CachedStatus, $"PipelineRun id: {pipelineRun.Id}");
        }

        /// <summary>
        /// Executing a graph with not allowed module author
        /// Compute is "cpucluster2"
        /// </summary>
        [Test]
        public async Task TestJobWithDeniedModuleAuthor()
        {
            IGraph graph = await CreateSingleModuleGraphWithExistingModule(environment: _policyTestEnvironment, moduleId: ModuleIdWithDeniedAuthor);
            IPipelineRun pipelineRun = await RunTestWithLogsAsync(graph, _policyTestEnvironment);
            Assert.AreEqual(PipelineRunStatusCode.Failed, pipelineRun.CachedStatus, $"PipelineRun id: {pipelineRun.Id}");

            await ValidatePolicyError(pipelineRun, "AllowedModuleAuthors validation failed - the values supplied are not allowed; (Assignment Id: /subscriptions/{0}/resourceGroups/{1}/providers/Microsoft.Authorization/policyAssignments/0d9536c9f2c048b9b8355e1d, ReasonPhrase: Module author validation failed because run is not using a module created by allowed authors..) AllowedPythonPackageChannels status is Succeeded; (Definition Id: /subscriptions/{0}/providers/Microsoft.Authorization/policyDefinitions/8abd2efb-15aa-4c90-9fa5-9049af4573b1, Assignment Id: /subscriptions/{0}/resourceGroups/{1}/providers/Microsoft.Authorization/policyAssignments/ae2ab2bb40eb42009ab2123a) ");
        }

        /// <summary>
        /// Test the SigningKey policy
        /// </summary>
        /// <param name="moduleType"></param>
        /// <param name="signedStatus"></param>
        /// <param name="computeTargetName"></param>
        /// <param name="expectedStatusCode"></param>
        /// <returns></returns>
        [TestCase("SimpleModule", "Unsigned", ComputeTargetWithSigningPolicy, PipelineRunStatusCode.Failed)]
        [TestCase("SimpleModule", "Unsigned", ComputeTargetWithoutSigningPolicy, PipelineRunStatusCode.Finished)]
        [TestCase("SimpleModule", "Signed", ComputeTargetWithSigningPolicy, PipelineRunStatusCode.Finished)]
        [TestCase("ParallelModule", "Signed", ComputeTargetWithSigningPolicy, PipelineRunStatusCode.Finished)]
        public async Task TestSigningKey(string moduleType, string signedStatus, string computeTargetName, PipelineRunStatusCode expectedStatusCode)
        {
            string moduleName = $"{moduleType}{signedStatus}";
            string modulePath = $"{EsCloudFilePath}/signing/{moduleName}";

            string moduleId;
            if (moduleType == "SimpleModule")
            {
                moduleId = await CreateSimpleCountModule(
                    environment: _policyTestEnvironment,
                    sourcePath: modulePath,
                    scriptName: "main.py",
                    dockerParameters: AllowedDockerParameters,
                    computeTarget: computeTargetName);
            }
            else if (moduleType == "ParallelModule")
            {
                moduleId = await CreateParallelRunModule(
                    environment: _policyTestEnvironment,
                    sourcePath: modulePath,
                    scriptName: "main.py",
                    dockerParameters: AllowedDockerParameters,
                    computeTarget: computeTargetName);
            }
            else
            {
                throw new ArgumentException($"Unrecognized moduleType {moduleType}.");
            }

            IGraph graph = await CreateSingleModuleGraphWithExistingModule(environment: _policyTestEnvironment, moduleId: moduleId);

            var extraTags = new Dictionary<string, string>()
            {
                { "Module", moduleName },
                { "Compute", computeTargetName },
                { "Expected", expectedStatusCode.ToString() },
            };
            IPipelineRun pipelineRun = await RunTestWithLogsAsync(graph, _policyTestEnvironment, extraTags: extraTags);

            Assert.AreEqual(expectedStatusCode, pipelineRun.CachedStatus, $"Pipeline run {pipelineRun.Id} expected to be {expectedStatusCode} but got {pipelineRun.CachedStatus}.");

            if (expectedStatusCode == PipelineRunStatusCode.Failed)
            {
                await ValidatePolicyError(pipelineRun, "SigningKeys validation failed - invalid singing keys; (Assignment Id: /subscriptions/{0}/resourceGroups/{1}/providers/Microsoft.Authorization/policyAssignments/034ac276d35249e1a69e7e5c, ReasonPhrase: Module signature validation failed in catalog verification; Exception: Catalog file catalog.json missing from module snapshot.) AllowedPythonPackageChannels status is Succeeded; (Definition Id: /subscriptions/{0}/providers/Microsoft.Authorization/policyDefinitions/8abd2efb-15aa-4c90-9fa5-9049af4573b1, Assignment Id: /subscriptions/{0}/resourceGroups/{1}/providers/Microsoft.Authorization/policyAssignments/ae2ab2bb40eb42009ab2123a) ");
            }
        }

        private async Task ValidatePolicyError(IPipelineRun pipelineRun, string expectErrorMessageFormat)
        {
            Console.WriteLine($"Validate that policy error is set for pipeline with runId {pipelineRun.Id}");

            var run = await GetRunDetails(pipelineRun).ConfigureAwait(false);

            Assert.NotNull(run.Error, "Expected run.Error to be set");
            Assert.NotNull(run.Error.Error, "Expected run.Error.Error to be set");

            var error = run.Error.Error;

            Assert.AreEqual("UserError", error.Code);

            Assert.AreEqual("https://aka.ms/amlpolicydoc", error.DetailsUri.AbsoluteUri);

            Assert.NotNull(error.InnerError, "Expected run.Error.Error.InnerError to be set");
            Assert.AreEqual("Auth", error.InnerError.Code);

            Assert.NotNull(error.InnerError.InnerError, "Expected run.Error.Error.InnerError.InnerError to be set");
            Assert.AreEqual("Authorization", error.InnerError.InnerError.Code);

            Assert.NotNull(error.InnerError.InnerError.InnerError, "Expected run.Error.Error.InnerError.InnerError.InnerError to be set");
            Assert.AreEqual("FailedPolicyServiceValidation", error.InnerError.InnerError.InnerError.Code);

            Assert.Null(error.InnerError.InnerError.InnerError.InnerError, "Expected run.Error.Error.InnerError.InnerError.InnerError.InnerError to be null");

            Assert.AreEqual("Policy validation failed due to {validationResponse}. You might not be able to execute the run according to the policy defined by your organization.", error.MessageFormat);
            Assert.AreEqual(1, error.MessageParameters.Count);
            Assert.IsTrue(error.MessageParameters.ContainsKey("validationResponse"));

            var expectErrorMessage = "Overall status is Failed:\n" + string.Format(expectErrorMessageFormat, _policyTestConfig.SubscriptionId, _policyTestConfig.ResourceGroup);
            Assert.AreEqual(expectErrorMessage, error.MessageParameters["validationResponse"]);
        }

        private async Task<IGraph> CreateSingleModuleGraphWithApprovedPythonDependency(
            AetherEnvironment environment,
            string sourcePath)
        {
            // it will generate a run with default conda-dependency using the default compute instance, i.e cpu-cluster, which has limited access to AllowedPythonPackageRepo
            // it will use default docker image which will be allowed by Policy Service
            // verify Python policy is applied to the compute instance, job submitted using allowed values, and confirm that job succeeded
            var graph = environment.CreateNewGraph();
            string moduleId = await CreateSimpleCountModule(
                environment: environment,
                sourcePath: sourcePath,
                dockerParameters: AllowedDockerParameters,
                computeTarget: computeTargetName);
            IAetherModule module = await environment.GetModuleAsync(moduleId);
            IModuleNode moduleNode1 = graph.AddNode(module);
            return graph;
        }

        private async Task<IGraph> CreateSingleModuleGraphWithDeniedPythonDependency(
            AetherEnvironment environment,
            string sourcePath)
        {
            // it will generate a run with a special conda dependency, which is not allowed for the default compute instance, i.e cpu-cluster
            // it will use default docker image which will be allowed by Policy Service
            // verify Python policy is applied to the compute instance, job submitted using denied values, and confirm that job failed
            var graph = environment.CreateNewGraph();
            string notAllowedCondaDependency = "{\"name\": \"project_environment\", \"dependencies\": [\"python = 3.8\", {\"pip\": [\"--index-url https://azuremlsdktestpypi.azureedge.net/sdk-release/master/588E708E0DF342C4A80BD954289657CF\", \"--extra-index-url https://pypi.python.org/simple\", \"azureml-sdk<0.1.1\", \"azureml-dataprep[fuse,pandas]\", \"azureml-telemetry\", \"--pre\"]}]}";
            string moduleId = await CreateSimpleCountModule(
                environment: environment,
                sourcePath: sourcePath,
                dockerParameters: AllowedDockerParameters,
                condaDependency: notAllowedCondaDependency,
                computeTarget: computeTargetName);
            IAetherModule module = await environment.GetModuleAsync(moduleId);
            IModuleNode moduleNode1 = graph.AddNode(module);
            return graph;
        }

        private async Task<IGraph> CreateSingleModuleGraphWithExistingModule(AetherEnvironment environment, string moduleId)
        {
            var graph = environment.CreateNewGraph();
            IAetherModule module = await environment.GetModuleAsync(moduleId);

            UpdateCondaDependency(module);

            IModuleNode moduleNode1 = graph.AddNode(module);
            return graph;
        }

        private async Task<string> CreateSimpleCountModule(
            AetherEnvironment environment,
            string sourcePath,
            List<StructuredInterfaceParameter> dockerParameters,
            string scriptName = DefaultScript,
            string condaDependency = DefaultAzureCondaDependency,
            string computeTarget = computeTargetName,
            string mlcComputeType = "AmlCompute",
            string isDockerEnabled = "true",
            bool isDeterministic = false)
        {
            var structuredInterface = new StructuredInterface
            {
                MetadataParameters = new List<StructuredInterfaceParameter>
                {
                    new StructuredInterfaceParameter { Name = "TargetType" , DefaultValue = "MLC", ParameterType = ParameterType.String, IsOptional = false},
                },
                Parameters = new List<StructuredInterfaceParameter>
                {
                    new StructuredInterfaceParameter { Name = "Target", DefaultValue = computeTarget, ParameterType = ParameterType.String, IsOptional = false },
                    new StructuredInterfaceParameter { Name = "MLCComputeType", DefaultValue = mlcComputeType, ParameterType = ParameterType.String, IsOptional = false },
                    new StructuredInterfaceParameter { Name = "PrepareEnvironment", DefaultValue = "true", ParameterType = ParameterType.String, IsOptional = false },
                    new StructuredInterfaceParameter { Name = "GenerateJson", DefaultValue = "true", ParameterType = ParameterType.String, IsOptional = false },
                    new StructuredInterfaceParameter { Name = "Script", DefaultValue = scriptName, ParameterType = ParameterType.String, IsOptional = false },
                    new StructuredInterfaceParameter { Name = "Framework", DefaultValue = "Python", ParameterType = ParameterType.String, IsOptional = false },
                    new StructuredInterfaceParameter { Name = "maxRunDurationSeconds", DefaultValue = "1200", ParameterType = ParameterType.String, IsOptional = true },
                    new StructuredInterfaceParameter { Name = "InterpreterPath", DefaultValue = "python", ParameterType = ParameterType.String, IsOptional = true },
                    new StructuredInterfaceParameter { Name = "UserManagedDependencies", DefaultValue = "false", ParameterType = ParameterType.String, IsOptional = true },
                    new StructuredInterfaceParameter { Name = "CondaDependencies" , DefaultValue = condaDependency, ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "DockerEnabled", DefaultValue = isDockerEnabled, ParameterType = ParameterType.String, IsOptional = false },
                    new StructuredInterfaceParameter { Name = "SharedVolumes", DefaultValue = "true", ParameterType = ParameterType.String, IsOptional = false },
                    new StructuredInterfaceParameter { Name = "NodeCount", DefaultValue = "1", ParameterType = ParameterType.String, IsOptional = true },
                    new StructuredInterfaceParameter { Name = "ContainerInstanceRegion", DefaultValue = "None", ParameterType = ParameterType.String, IsOptional = true },
                    new StructuredInterfaceParameter { Name = "ContainerInstanceCpuCores", DefaultValue = "1", ParameterType = ParameterType.String, IsOptional = true },
                    new StructuredInterfaceParameter { Name = "ContainerInstanceMemoryGb", DefaultValue = "4", ParameterType = ParameterType.String, IsOptional = true },
                },
            };
            structuredInterface.Parameters.AddRange(dockerParameters);
            var moduleUploadInfo = new ModuleUploadInfo(
               name: "PolicyService single-module graph testing module",
               displayName: "PolicyService single-module graph testing module display",
               description: "PolicyService single-module graph testing module",
               isDeterministic: isDeterministic,
               cloudSystem: "escloud",
               structuredInterface: structuredInterface);

            return await environment.UploadModuleAsync(sourcePath, moduleUploadInfo);
        }

        private async Task<IGraph> CreateSingleModuleGraphWithSpecifiedContainerRegistry(
            AetherEnvironment environment,
            string sourcePath)
        {
            // it will generate a run with default conda-dependency using the pre-configured compute instance, i.e cpu-cluster, which has limited access to AllowedContainerRegistry
            // it will use docker image from registry by adding dockerParameters as follows
            // verify ACP policy is applied to the compute instance, job submitted using not-allowed values, and confirm that job failed
            var graph = environment.CreateNewGraph();
            List<StructuredInterfaceParameter> dockerFromRegistryParameters = new List<StructuredInterfaceParameter>
            {
                    new StructuredInterfaceParameter { Name = "BaseImageRegistryAddress", DefaultValue = "acrplcye2eteststobedenied.azurecr.io", ParameterType = ParameterType.String, IsOptional = false },
                    new StructuredInterfaceParameter { Name = "BaseDockerfile", DefaultValue = "https://github.com/docker/distribution-library-image/blob/0b6ea3ba50b65563600a717f07db4cfa6f18f957/amd64/Dockerfile", ParameterType = ParameterType.String, IsOptional = false },
            };
            string computeTarget = "cpu-cluster";
            string moduleId = await CreateSimpleCountModule(
                environment: environment,
                sourcePath: sourcePath,
                dockerParameters: dockerFromRegistryParameters,
                computeTarget: computeTarget);
            IAetherModule module = await environment.GetModuleAsync(moduleId);
            IModuleNode moduleNode1 = graph.AddNode(module);
            return graph;
        }

        private async Task<IGraph> CreateSingleModuleGraphWithApprovedContainerRegistry(
            AetherEnvironment environment,
            string sourcePath)
        {
            // it will generate a run with default conda-dependency using the pre-configured compute instance, i.e cpucluster2, with allowed container registry
            // it will use docker image from registry by adding dockerParameters as follows
            // verify ACP policy is applied to the compute instance, job submitted using approved values, and confirm that job succeed
            var graph = environment.CreateNewGraph();
            string computeTarget = "cpu-cluster";
            string moduleId = await CreateSimpleCountModule(
                environment: environment,
                sourcePath: sourcePath,
                dockerParameters: AllowedDockerParameters,
                computeTarget: computeTarget);
            IAetherModule module = await environment.GetModuleAsync(moduleId);
            IModuleNode moduleNode1 = graph.AddNode(module);
            return graph;
        }

        private async Task<string> CreateParallelRunModule(
            AetherEnvironment environment,
            string sourcePath,
            List<StructuredInterfaceParameter> dockerParameters,
            string scriptName,
            string condaDependency = DefaultAzureCondaDependency,
            string computeTarget = computeTargetName,
            string mlcComputeType = "AmlCompute",
            string isDockerEnabled = "true",
            bool isDeterministic = false)
        {
            string prs_params = "{\"Name\": \"step200413-1755\", \"ComputeName\": \"e2ecpucluster\", \"AppInsightsEnabled\": false,"
                + " \"EventHubEnabled\": false, \"StorageEnabled\": false, \"EntryScript\": \"parallel_run_script.py\","
                + " \"NodeCount\": 3, \"InputFormat\": \"file\", \"MiniBatchSize\": 2, \"ErrorThreshold\": 50, \"OutputAction\": \"summary_only\","
                + " \"ModelIds\": \"[]\", \"Tags\": \"{\\\"build_id\\\": \\\"local\\\", \\\"build_number\\\": \\\"local\\\"}\","
                + " \"Properties\": \"null\", \"EnvironmentName\": \"parallel_run_step\", \"EnvironmentVersion\": null, \"ProcessCountPerNode\": 2}";

            var structuredInterface = new StructuredInterface
            {
                MetadataParameters = new List<StructuredInterfaceParameter>
                {
                    new StructuredInterfaceParameter { Name = "TargetType" , DefaultValue = "MLC", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "BatchInferencingMetaData" , DefaultValue = prs_params, ParameterType = ParameterType.String, IsOptional = false},
                },
                Parameters = new List<StructuredInterfaceParameter>
                {
                    new StructuredInterfaceParameter { Name = "Target", DefaultValue = computeTarget, ParameterType = ParameterType.String, IsOptional = false },
                    new StructuredInterfaceParameter { Name = "MLCComputeType", DefaultValue = mlcComputeType, ParameterType = ParameterType.String, IsOptional = false },
                    new StructuredInterfaceParameter { Name = "PrepareEnvironment", DefaultValue = "true", ParameterType = ParameterType.String, IsOptional = false },
                    new StructuredInterfaceParameter { Name = "GenerateJson", DefaultValue = "true", ParameterType = ParameterType.String, IsOptional = false },
                    new StructuredInterfaceParameter { Name = "Script", DefaultValue = "driver/amlbi_main.py", ParameterType = ParameterType.String, IsOptional = false },
                    new StructuredInterfaceParameter { Name = "Framework", DefaultValue = "Python", ParameterType = ParameterType.String, IsOptional = false },
                    new StructuredInterfaceParameter { Name = "maxRunDurationSeconds", DefaultValue = "1200", ParameterType = ParameterType.String, IsOptional = true },
                    new StructuredInterfaceParameter { Name = "InterpreterPath", DefaultValue = "python", ParameterType = ParameterType.String, IsOptional = true },
                    new StructuredInterfaceParameter { Name = "UserManagedDependencies", DefaultValue = "false", ParameterType = ParameterType.String, IsOptional = true },
                    new StructuredInterfaceParameter { Name = "CondaDependencies" , DefaultValue = condaDependency, ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "DockerEnabled", DefaultValue = isDockerEnabled, ParameterType = ParameterType.String, IsOptional = false },
                    new StructuredInterfaceParameter { Name = "SharedVolumes", DefaultValue = "true", ParameterType = ParameterType.String, IsOptional = false },
                    new StructuredInterfaceParameter { Name = "ContainerInstanceRegion", DefaultValue = "None", ParameterType = ParameterType.String, IsOptional = true },
                    new StructuredInterfaceParameter { Name = "ContainerInstanceCpuCores", DefaultValue = "1", ParameterType = ParameterType.String, IsOptional = true },
                    new StructuredInterfaceParameter { Name = "ContainerInstanceMemoryGb", DefaultValue = "4", ParameterType = ParameterType.String, IsOptional = true },
                },
                Arguments = new List<ArgumentAssignment>
                {
                    new ArgumentAssignment() { ValueType = ArgumentValueType.Literal, Value = "--scoring_module_name" },
                    new ArgumentAssignment() { ValueType = ArgumentValueType.Literal, Value = scriptName },

                    new ArgumentAssignment() { ValueType = ArgumentValueType.Literal, Value = "--error_threshold" },
                    new ArgumentAssignment() { ValueType = ArgumentValueType.Literal, Value = "-1" },

                    new ArgumentAssignment() { ValueType = ArgumentValueType.Literal, Value = "--output_action" },
                    new ArgumentAssignment() { ValueType = ArgumentValueType.Literal, Value = "summary_only" },

                    new ArgumentAssignment() { ValueType = ArgumentValueType.Literal, Value = "--create_snapshot_at_runtime" },
                    new ArgumentAssignment() { ValueType = ArgumentValueType.Literal, Value = "True" },

                    new ArgumentAssignment() { ValueType = ArgumentValueType.Literal, Value = "--rank_mini_batch_count" },
                    new ArgumentAssignment() { ValueType = ArgumentValueType.Literal, Value = "10" },

                    new ArgumentAssignment() { ValueType = ArgumentValueType.Literal, Value = "--allowed_failed_count" },
                    new ArgumentAssignment() { ValueType = ArgumentValueType.Literal, Value = "10" }
                },
            };
            structuredInterface.Parameters.AddRange(dockerParameters);

            // To upload a parallel module, a snapshot id is required to be passed in.
            // To get the snapshot id, upload a plain module first and then get its snapshot id.
            string moduleId = await CreateSimpleCountModule(
                environment: environment,
                sourcePath: sourcePath,
                dockerParameters: AllowedDockerParameters,
                computeTarget: computeTarget);
            IAetherModule plainModule = await environment.GetModuleAsync(moduleId);
            Guid plainModuleId = Guid.Parse(plainModule.DataLocation.StorageId);

            var moduleUploadInfo = new ModuleUploadInfo(
               name: "PolicyService ParallelRun module",
               displayName: "PolicyService ParallelRun module",
               description: "PolicyService single-module graph testing for ParallelRun module",
               isDeterministic: isDeterministic,
               cloudSystem: "escloud",
               moduleType: ModuleType.BatchInferencing,
               stepType: "ParallelRunStep",
               snapshotId: plainModuleId,
               structuredInterface: structuredInterface);

            return await environment.UploadModuleAsync(sourcePath, moduleUploadInfo);
        }

        /// <summary>
        /// Update the module setup to avoid having to create a new module
        /// </summary>
        private static void UpdateCondaDependency(IAetherModule module)
        {
            UpdateParameter(module, "CondaDependencies", "python = 3.6.2", "python = 3.8");
            UpdateParameter(module, "BaseDockerImage", "mcr.microsoft.com/azureml/openmpi3.1.2-ubuntu18.04", "mcr.microsoft.com/azureml/openmpi4.1.0-ubuntu20.04");
            UpdateParameter(module, "BaseDockerImage", "mcr.microsoft.com/azureml/base:intelmpi2018.3-ubuntu16.04", "mcr.microsoft.com/azureml/openmpi4.1.0-ubuntu20.04");
        }

        private static void UpdateParameter(IAetherModule module, string parameterName, string replacedValue, string replacement)
        {
            var structuredParameter = module.StructuredInterface.Parameters.FirstOrDefault(p => string.Equals(p.Name, parameterName, StringComparison.OrdinalIgnoreCase));
            if (structuredParameter == null)
            {
                Console.WriteLine($"{parameterName} not found in module {module.Name}");
                return;
            }

            if (string.IsNullOrWhiteSpace(structuredParameter.DefaultValue))
            {
                Console.WriteLine($"{parameterName}.DefaultValue not set for module {module.Name}");
                return;
            }

            if (structuredParameter.DefaultValue.IndexOf(replacedValue, StringComparison.OrdinalIgnoreCase) < 0)
            {
                Console.WriteLine($"{parameterName}.DefaultValue for module {module.Name} does not contain {replacedValue}");
                return;
            }

            Console.WriteLine($"Replacing {parameterName}.DefaultValue for module {module.Name}");
            structuredParameter.DefaultValue = structuredParameter.DefaultValue.Replace(replacedValue, replacement);

            if (module.Interface == null)
            {
                Console.WriteLine($"Interface for module {module.Name} is null");
                return;
            }

            if (module.Interface.Parameters == null)
            {
                Console.WriteLine($"Interface.Parameters for module {module.Name} is null");
                return;
            }

            var interfaceParameters = module.Interface.Parameters as List<IParameter>;

            if (interfaceParameters == null)
            {
                Console.WriteLine($"Unable to cast module.Interface.Parameters as List<IParameter>");
                return;
            }

            var index = interfaceParameters.FindIndex(p => string.Equals(p.Name, parameterName, StringComparison.OrdinalIgnoreCase));
            if (index < 0l)
            {
                Console.WriteLine($"{parameterName} not found in module {module.Name} Interface.Parameters");
                return;
            }

            var interfaceParameter = interfaceParameters[index];

            if (string.IsNullOrWhiteSpace(interfaceParameter.DefaultValue))
            {
                Console.WriteLine($"{parameterName}.DefaultValue not set for module {module.Name} Interface.Parameters");
                return;
            }

            if (interfaceParameter.DefaultValue.IndexOf(replacedValue, StringComparison.OrdinalIgnoreCase) < 0)
            {
                Console.WriteLine($"{parameterName}.DefaultValue for module {module.Name} Interface.Parameters does not contain {replacedValue}");
                return;
            }

            var rules = new List<Microsoft.Aether.BlueBox.Library.EnumParameterRule>();
            foreach (var rule in interfaceParameter.Rules)
            {
                if (rule is Microsoft.Aether.BlueBox.Library.EnumParameterRule enumRule)
                {
                    rules.Add(new Microsoft.Aether.BlueBox.Library.EnumParameterRule(enumRule.LegalValues));
                }
            }

            var dependenciesUpdated = interfaceParameter.DefaultValue.Replace(replacedValue, replacement);
            var newDependencies = new StringParameter(interfaceParameter.Name, interfaceParameter.IsOptional, dependenciesUpdated, rules)
            {
                Description = interfaceParameter.Description
            };

            Console.WriteLine($"Replacing {parameterName} for module {module.Name} Interface.Parameters");
            interfaceParameters[index] = newDependencies;
        }

        /// <summary>
        /// Setup RunHistory and Artifact clients using master environment. It will be updated to INT when teams are switching
        /// </summary>
        private void SetupHttpClients()
        {
            var token = _tokenProvider.GetAccessTokenAsync().Result;

            var runHistoryEndpointAddress = $"{TestContext.Parameters.Get("PolicyServiceE2ETestRunHistoryHost")}/history/v1.0/subscriptions/{_policyTestConfig.SubscriptionId}/resourceGroups/{_policyTestConfig.ResourceGroup}/providers/Microsoft.MachineLearningServices/workspaces/{_policyTestConfig.WorkspaceName}/runs/";
            _runHistoryClient = CreateHttpClient(runHistoryEndpointAddress, token);

            var artifactEndpointAddress = $"{TestContext.Parameters.Get("PolicyServiceE2ETestArtifactHost")}/artifact/v2.0/subscriptions/{_policyTestConfig.SubscriptionId}/resourceGroups/{_policyTestConfig.ResourceGroup}/providers/Microsoft.MachineLearningServices/workspaces/{_policyTestConfig.WorkspaceName}/artifacts/prefix/contentinfo/{ArtifactOrigins.ExperimentRun}/";
            _artifactClient = CreateHttpClient(artifactEndpointAddress, token);
        }

        private HttpClient CreateHttpClient(string endpointAddress, string token)
        {
            var handler = new AuthHandler(token, AetherEnvironment.DefaultRetryPolicy);

            var client = new HttpClient(handler)
            {
                BaseAddress = new Uri(endpointAddress)
            };

            client.DefaultRequestHeaders.UserAgent.TryParseAdd("bbaetherlibary/integrationtest");

            return client;
        }

        private async Task<RunDto> GetRunDetails(IPipelineRun pipelineRun)
        {
            Console.WriteLine($"GetExecutionGraphAsync for pipeline with runId {pipelineRun.Id}");
            var executionGraph = await pipelineRun.GetExecutionGraphAsync();

            Console.WriteLine($"GetAllNodesExecutionInfoAsync for pipeline with runId {pipelineRun.Id}");
            var executionNodes = await executionGraph.GetAllNodesExecutionInfoAsync();

            var runId = executionNodes.First().Value.RunId;

            Console.WriteLine($"Retrieve details for run {runId} which is part of pipeline with runId {pipelineRun.Id}");
            return await _runHistoryClient.GetEntityAsync<RunDto>(runId).ConfigureAwait(false);
        }

        private async Task<string> GetPolicyLog(IPipelineRun pipelineRun)
        {
            Console.WriteLine($"Validate that policy error is set for pipeline with runId {pipelineRun.Id}");

            var run = await GetRunDetails(pipelineRun).ConfigureAwait(false);

            var logPaths = await _artifactClient.GetEntityAsync<PaginatedResult<ArtifactContentInformationDto>>($"{run.DataContainerId}/logs/azureml").ConfigureAwait(false);

            const string policyLogFile = "logs/azureml/90_policy_results.txt";

            Uri contentUri = null;
            foreach (var logPath in logPaths.Value)
            {
                if (string.Equals(logPath.Path, policyLogFile, StringComparison.OrdinalIgnoreCase))
                {
                    contentUri = logPath.ContentUri;
                    break;
                }
            }

            if (contentUri == null)
            {
                Assert.Fail($"Unable to find policy log file {policyLogFile} for pipeline run {pipelineRun.Id}");
            }

            var cloudBlob = CloudBlobHelper.BuildCloudBlob(contentUri);
            using (var stream = new MemoryStream())
            {
                await cloudBlob.DownloadToStreamAsync(stream).ConfigureAwait(false);
                stream.Seek(0, SeekOrigin.Begin);
                using (var stringReader = new StreamReader(stream, leaveOpen: true))
                {
                    return stringReader.ReadToEnd();
                }
            }
        }
    }
}
