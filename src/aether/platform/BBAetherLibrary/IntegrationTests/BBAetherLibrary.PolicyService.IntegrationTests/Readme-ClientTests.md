**Overview**

The policy client tests use several policies which are applied to the resource group 'rg-e2epolicytest'
in the Aether3P Dev subscription.  Each policy is applied to a different compute target so each test case can
run against one policy.

Python package tests use the compute 'policy-python' with the following package feeds allowed: 
["azureml-defaults", "https://pypi.org/simple"]

ACR tests use the compute 'policy-acr' with the following registries allowed:
["acrplcye2etests.azurecr.io", "acrplcye2etests"]

The 'approved module author' test uses the compute 'policy-author' with the following identity allowed:
["7b7ce063-1c80-4997-8e71-27c32cdd7791"]

The 'denied module author' test uses the compute 'policy-badauthor' with a non-existent identity as
the only allowed author:  ["bad-7b7ce063-1c80-4997-8e71-27c32cdd7791"]

Module Signing tests uses the compute 'policy-signed' with a policy which enforces code signatures. The
public key and signed manifest was generated with the gpg tool.

**Detailed setup steps:**

Initial setup:

Created resource group:
rg-e2epolicytest

Created machine learning workspace:
ws-e2epolicytest

**Generating module snapshots**
The module signature test and module author test require several module snapshots to be uploaded.
Run the notebook 'policy-module-creation.ipynb' from the zip file create-snapshots.zip.
The run will fail but it should create the module snapshots for signature validation.

Open the pipeline run in the portal UX.  There should be 3 runs (goodSignature, badSignature, extraFile)
corresponding to 3 test cases for signature validation.  

Do the following 3 times to get the snapshot IDs for the 3 tests:
Navigate to one of the step runs
Click "Raw JSON" under "See all properties"
Search for ContentSnapshotId

**How to sign modules**
The public/private key pair was generated with the gpg tool.  The public key file is exported to pgp text
format and then modified to fit on one line.  (Replace "\r\n" newlines with literal strings @"\r\n").
That public key is used as part of the policy definition.

For generating the signed modules, first run the CatalogGenerator tool to generate the catalog file (catalog.json).
The tool is checked in at \vienna\src\aether\platform\backendV2\BlueBox\PolicyService\CatalogGenerator.
Then run the gpg signing tool to sign the catalog.json file and produce a signature file catalog.json.sig.

**Set up Module Signing Policy**

Create compute cluster:
policy-signed

* Go to https://portal.azure.com, search for "Policy".

* In Policy page, select Assignments -> Assign Policy

Enter details:

Scope: Aether3P Dev/rg-e2epolicytest

Description:
Code signing test for e2e policy tests

Policy definition:
[Preview]: Configure code signing for training code for specified Azure Machine Learning computes
(Built-in)

Assignment name:
E2E Policy Tests: [Preview]: Configure code signing for training code for specified Azure Machine Learning computes

Parameters:
(Uncheck "Only show parameters that require input")
Compute names: ["policy-signed"]
Compute type: any
Isolated network: Any
PGP public key: (Copy/paste the text from the file publicKey.txt)
Effect: enforceSetting



> NOTE: The parallel module (Used in PolicyServiceClientTestWithApprovedModuleSignature_WithSystemCreatedSnapshots test item) were signed with another signing key. So, the policy was assigned to a separate compute (policy-m365-sign) with the publicKey_m365.txt public key.



**Create Approved Python package policy**

Create compute:
policy-python

Policy -> Assignments -> Assign Policy

Scope: Aether3P Dev/rg-e2epolicytest

Policy definition:
[Preview]: Configure allowed Python packages for specified Azure Machine Learning computes
[Built-in]

Assignment name:
E2E Policy Tests: [Preview]: Configure allowed Python packages for specified Azure Machine Learning computes

Description:
Python package validation for e2e policy tests


Parameters:
(Uncheck "Only show parameters that require input")
Compute names: ["policy-python"]
Compute type: Any
Isolated network: Any
Allowed Python package indexes:  ["azureml-defaults", "https://pypi.org/simple"]
Effect: enforceSetting


**Create Approved ACR policy**

Create compute:
policy-acr

Policy -> Assignments -> Assign Policy

Scope: Aether3P Dev/rg-e2epolicytest

Policy definition:
[Preview]: Configure allowed registries for specified Azure Machine Learning computes
[Built-in]

Assignment name:
E2E Policy Tests: [Preview]: Configure allowed registries for specified Azure Machine Learning computes

Description:
ACR validation for e2e policy tests


Parameters:
(Uncheck "Only show parameters that require input")
Compute names: ["policy-acr"]
Compute type: Any
Isolated network: Any
Azure Container Registries: ["acrplcye2etests.azurecr.io", "acrplcye2etests"]
Effect: enforceSetting


**Create Module Author policy**

To get a user ID:

In BBAetherLibrary tester app, edit CreateAetherEnvironment to use the resource group and workspace
of your module.  Then run code similar to:

            var module = await environment.GetModuleEntityAsync("6d7e0e21-5b62-4ddd-b95b-e34d7b6e8f4a");
            Console.WriteLine($"Got user ID: {module.Data.CreatedBy.UserObjectId}");

Create compute:
policy-author

Policy -> Assignments -> Assign Policy

Scope: Aether3P Dev/rg-e2epolicytest

Policy definition:
[Preview]: Configure allowed module authors for specified Azure Machine Learning computes
[Built-in]

Assignment name:
E2E Policy Tests: [Preview]: Configure allowed module authors for specified Azure Machine Learning computes

Description:
Module author validation for e2e policy tests


Parameters:
(Uncheck "Only show parameters that require input")
Compute names: ["policy-author"]
Compute type: Any
Isolated network: Any
Allowed module authors: ["7b7ce063-1c80-4997-8e71-27c32cdd7791"]
Effect: enforceSetting

Create 2nd policy with different author:

Assignment name:
E2E Policy Tests: (bad author) [Preview]: Configure allowed module authors for specified Azure Machine Learning computes

Compute names: ["policy-badauthor"]
Allowed module authors: ["bad-7b7ce063-1c80-4997-8e71-27c32cdd7791"]

