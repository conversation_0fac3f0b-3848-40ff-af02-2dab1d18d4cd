﻿# What is Policy Service Integration Test?
Policy Service Integration test will be used to verify that policies could be defined, assigned and correctly applied to target resource-group/ work-space.  

## What policies have been verified?
Currently four policies have been verified:
- Configure allowed Python packages for specified Azure Machine Learning computes (effective for the whole resource group)
- Configure allowed module authors for specified Azure Machine Learning computes (effective only for cpucluster2)
- Configure allowed registries for specified Azure Machine Learning computes (effective only for cpu-cluster)
- Configure allowed signing key for specified Azure Machine Learning computes (effective only for cpucluster3)

## Steps taken to verify the policies
1. Created a dedicated ResourceGroup (i.e. _rg_plcy_e2etests_) and a WorkSpace (i.e. _ws_plcy_e2etests_). (https://ml.azure.com/?tid=72f988bf-86f1-41af-91ab-2d7cd011db47&wsid=/subscriptions/b8c23406-f9b5-4ccb-8a65-a8cb5dcd6a5a/resourcegroups/rg_plcy_e2etests/workspaces/ws_plcy_e2etests)
Created a Storage Account (i.e. _saplcye2etests_) and a Storage Container (i.e. _containerplcye2etests_) under the ResourceGroup. 
Created a DataStore under the Azure Machine Learning Workspace (i.e. _myblobstorage2_) with the associated storage account.
Create three Compute Instances, _cpu-cluster_, _cpucluster2_ and _cpucluster3_ within the workspace.
  
2. Assign "Configure allowed Python packages for specified Azure Machine Learning computes" policy to the resource group and workspace. 
In the Assignment, only allow Python packages from "https://pypi.org/simple" repo. Two tests are created. The one named _TestJobWithApprovedPythonRepo_ used azureml-defaults, which points to "https://pypi.org/simple". It should succeed.
The other one named _TestJobWithDeniedPythonRepo_ used Python package from other repo, which should be rejected.
  
3. Assign "Configure allowed module authors for specified Azure Machine Learning computes" to the resource and workspace. Particularly, this is assigned to _cpucluster2_. This is the Compute Instance that will be isolated to verify module author.
This is handled with care since in the cloud-test environmemt or local tests on different machines, we want it to perform the same regardless of author identity. 
Therefore, two pre-configured modules are used and a separate compute instance is required to isolate the this test from other tests and modules created during the integration test. One module with id "74f2c1f0-3c82-4d46-8e63-a6665a3f3488" was
created by indentity "20ee6e52-014c-4404-ba84-8a9631950496". The other module with id "27c5aac6-1aaa-40e6-80db-c1083fef46f4" was created by other user. The policy will only allow modules created by user with identity of "20ee6e52-014c-4404-ba84-8a9631950496". 
Therefore the module with id "74f2c1f0-3c82-4d46-8e63-a6665a3f3488" (_TestJobWithApprovedModuleAuthor_) should pass and the module with id "27c5aac6-1aaa-40e6-80db-c1083fef46f4" (_TestJobWithDeniedModuleAuthor_) should fail.
  
4. Assign "Configure allowed registries for specified Azure Machine Learning computes" to the resource group and workspace. Particularly, this is assigned to _cpu-cluster_. It will only allow ACR named "acrplcye2etests".
Two Azure Container Registries were created, i.e. "acrplcye2etests" and "acrplcye2eteststobedenied". A base Ubuntu image was uploaded to the registry. The policy should only allow docker image from "acrplcye2etests" registry to execute, 
and reject the other one.

5. Assign "Configure allowed signing key for specified Azure Machine Learning computes" to the resource group and workspace. Particularly, this is assigned to _cpucluster3_. It will only allow M365 compliant signed modules run on the compute.
The code runs on the compute must be signed with M365 key signing, otherwise check will fail. Refer to [../shared/TestResources/EscloudFile/helloworld/README.txt](../shared/TestResources/EscloudFile/helloworld/README.txt) to see how to sign the code.

# What is Policy Service Client Integration Test?
Policy Service Client Integration Test is a set of tests replicating the actual requests sending to Policy Service. The ValidationRequestDto will be the same as ES sending to Policy Service with actual module and environment information. 
Currently we are testing three policies, "Configure allowed Python packages for specified Azure Machine Learning computes", "Configure allowed module authors for specified Azure Machine Learning computes", and "Configure allowed registries for specified Azure Machine Learning computes".
The goal of the test is to verify the Policy Service Client could achieve the desired level of functionalities.
