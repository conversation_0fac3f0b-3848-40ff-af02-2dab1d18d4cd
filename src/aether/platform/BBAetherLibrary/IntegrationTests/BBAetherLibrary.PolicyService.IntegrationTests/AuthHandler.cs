﻿// <copyright file="AuthHandler.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Net.Http;
using System.Net.Http.Headers;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Aether.BlueBox.Library;

namespace BBAetherLibrary.PolicyService.IntegrationTests
{
    internal class AuthHandler : HttpClientHandler
    {
        private readonly string _token;
        private readonly IRetryPolicy _retryPolicy;

        public AuthHandler(string token, IRetryPolicy retryPolicy)
        {
            _token = token;
            _retryPolicy = retryPolicy;
        }

        protected override async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
        {
            request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", _token);
            return await _retryPolicy.SendAsync(token => base.SendAsync(request, token), request).ConfigureAwait(false);
        }
    }
}
