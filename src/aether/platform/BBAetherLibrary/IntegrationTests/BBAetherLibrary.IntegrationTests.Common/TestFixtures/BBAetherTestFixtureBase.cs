﻿using BBAetherLibrary.Common;
using BBAetherLibrary.IntegrationTests.Common.ApiCallers;
using Microsoft.Aether.AEVA.DataContracts;
using Microsoft.Aether.BackendCommon.Extensions;
using Microsoft.Aether.BlueBox.Library;
using Microsoft.Aether.DataContracts.Entity;
using Microsoft.Aether.S2S.Common;
using Microsoft.Aether.TokenProvider;
using Microsoft.MachineLearning.Common.Core.Contracts;
using Microsoft.MachineLearning.Dataset.Contracts;
using Microsoft.RelInfra.Common;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

[assembly: Parallelizable(ParallelScope.Fixtures)]

namespace BBAetherLibrary.IntegrationTests.Common
{
    /// <summary>
    ///   This needs to run once before any test runs in this namespace
    /// </summary>
    [SetUpFixture]
    public class BBAEtherTestSetupFixture : BBAetherTestFixtureBase
    {
        [OneTimeSetUp]
        public async Task TestSetupAsync()
        {
            await InitAsync();
            await BBAetherEnvironmentUtils.CreateDataTypesIfNeededAsync(_testEnvironment);
        }
    }


    [TestFixture]
    [Parallelizable(ParallelScope.All)]
    public class BBAetherTestFixtureBase
    {
        private ExperimentLogger Logger;
        protected ITokenProvider _tokenProvider;
        protected readonly IS2STokenProvider _s2STokenProvider;
        protected IKeyVaultClient _testKeyVaultClient;
        private readonly string _baseLogFolder;
        protected string _workspaceId;
        protected string _workspaceName;
        protected string _subscriptionId;
        protected string _resourceGroup;
        protected string _envConfigFile;

        private const string LOCAL_LOG_LOCATION = "ExperimentLogs";

        public const string AzureCliClientId = "04b07795-8ddb-461a-bbee-02f9e1bf7b46";
        public static readonly Uri AzurePowerShellRedirectUri = new Uri("urn:ietf:wg:oauth:2.0:oob");

        public const string TorusTenantId = "cdc5aeea-15c5-4db6-b079-fcadd2505dc2";

        // Test Key Vault
        // https://ms.portal.azure.com/#@microsoft.onmicrosoft.com/resource/subscriptions/e75c95f3-27b4-410f-a40e-2b9153a807dd/resourceGroups/Aether/providers/Microsoft.KeyVault/vaults/aether-tests
        protected const string TEST_KEY_VAULT_NAME = "aether-tests";

        // App to access Test Key Vault
        // https://ms.portal.azure.com/#blade/Microsoft_AAD_RegisteredApps/ApplicationMenuBlade/Overview/appId/ab8612bc-e898-4f85-9a42-fbfd107f435d/isMSAApp/
        protected const string TEST_KEY_VAULT_AAD_APP = "ab8612bc-e898-4f85-9a42-fbfd107f435d";

        // aetherCloudtest Azure app
        // https://ms.portal.azure.com/#blade/Microsoft_AAD_IAM/ApplicationBlade/appId/2dd3713c-f4d1-4a45-b702-b55f9f524322/objectId/45491791-989e-4ccd-a765-9d3a9654c29a
        protected const string AETHER_CLOUDTEST_CLIENTID = "2dd3713c-f4d1-4a45-b702-b55f9f524322";

        // pipeline-ci-identity
        // https://ms.portal.azure.com/#@microsoft.onmicrosoft.com/resource/subscriptions/96aede12-2f73-41cb-b983-6d11a904839b/resourceGroups/pipeline-agents/providers/Microsoft.ManagedIdentity/userAssignedIdentities/pipeline-ci-identity/overview
        protected const string PIPELINE_CI_IDENTITY_CLIENTID = "0ce3137b-edd6-4ae4-bfb2-1f655bb14272";

        protected const string ENV_CONFIG_FILE_FOR_MASTER = "EnvironmentConfigs/pipeline_tests.xml";
        protected const string ENV_CONFIG_FILE_FOR_INT = "EnvironmentConfigs/int/pipeline_tests.xml";

        // restrict each test runtime to max 25 mins with default as 15 mins
        private const int DefaultWaitTimeForPipelineRun = 15;
        private const int MaxWaitTimeForPipelineRun = 50;

        protected AetherEnvironment _testEnvironment;
        protected AetherEnvironment _testListAmlEnvironment;
        protected AetherEnvironment _testSingularityEnvironment;
        protected TestEnvironmentConfig _testConfig;

        protected DatasetApiCaller _datasetApiCaller;
        protected DataAssetApiCaller _dataAssetApiCaller;

        protected WorkspaceIdentity WorkspaceIdentity { get; private set; }
        protected WorkspaceContext2 WorkspaceContext { get; private set; }

        protected static readonly RunStateTracking _runStateTracking = new RunStateTracking();

        public BBAetherTestFixtureBase() : this(authTenantId: AdalConstants.MicrosoftTenantId, authAudience: AdalConstants.ArmResource)
        {
        }

        public BBAetherTestFixtureBase(string authTenantId, string authAudience) : this(
            tokenProvider: IsRunningInCloudTestOrVsts()
                ? (ITokenProvider)new ManagedIdentityTokenProvider(AdalConstants.MIArmResource, PIPELINE_CI_IDENTITY_CLIENTID)
                : (ITokenProvider)new InteractiveTokenProvider(authTenantId, authAudience),
            keyVaultClient: IsRunningInCloudTestOrVsts()
                ? (IKeyVaultClient)new ManagedIdentityKeyVaultClient(TEST_KEY_VAULT_NAME, PIPELINE_CI_IDENTITY_CLIENTID)
                : (IKeyVaultClient)new AzureTokenHelperKeyVaultClient(TEST_KEY_VAULT_NAME, new InteractiveTokenProvider(tenantId: authTenantId, audience: AdalConstants.KeyVaultResource)))
        {
        }

        public BBAetherTestFixtureBase(ITokenProvider tokenProvider, IKeyVaultClient keyVaultClient)
        {
            _tokenProvider = tokenProvider;
            _testKeyVaultClient = keyVaultClient;

            if (IsRunningInCloudTestOrVsts())
            {
                _baseLogFolder = Environment.GetEnvironmentVariable("LoggingDirectory");
            }
            else
            {
                // TestContext.CurrentContext.TestDirectory is returning a really long path, ex:
                // D:\aethergit0\Aether\target\distrib\Debug\AnyCPU\app\aether\BBAetherLibrary\BBAetherLibrary.AzureBatch.IntegrationTests\net462
                // this will ultimately cause the tests to fail writing the logs, because path will exceed 260 chars as inner folders are created
                // feel free to set BaseLogFolder = @"d:\temp\logs" for local tests debugging purposes.
                _baseLogFolder = Path.Combine(TestContext.CurrentContext.TestDirectory, LOCAL_LOG_LOCATION);
            }

            _envConfigFile = IsRunningInInt() ? ENV_CONFIG_FILE_FOR_INT : ENV_CONFIG_FILE_FOR_MASTER;
            Trace.WriteLine($"Environment config file: {_envConfigFile}");
        }

        protected async Task InitAsync()
        {
            Logger = new ExperimentLogger(_baseLogFolder);

            Trace.WriteLine($"Setting current directory to {TestContext.CurrentContext.TestDirectory}");
            Directory.SetCurrentDirectory(TestContext.CurrentContext.TestDirectory);

            TestParameters runParameters = TestContext.Parameters;

            _testConfig = new TestEnvironmentConfig
            {
                SubscriptionId = runParameters.Get("SubscriptionId"),
                ResourceGroup = runParameters.Get("ResourceGroup"),
                WorkspaceId = runParameters.Get("WorkspaceId"),
                WorkspaceName = runParameters.Get("WorkspaceName"),
                ListAmlResourceGroup = runParameters.Get("ListAmlResourceGroup"),
                ListAmlWorkspaceName = runParameters.Get("ListAmlWorkspaceName"),
                BlobDatastoreName = runParameters.Get("BlobDatastoreName"),
                AdlsDatastoreName = runParameters.Get("AdlsDatastoreName"),
                AdlsGen2DatastoreName = runParameters.Get("AdlsGen2DatastoreName"),
                MigratedAdlsDatastoreName = runParameters.Get("MigratedAdlsDatastoreName"),
                AmlComputeName = runParameters.Get("AmlComputeName"),
                AmlSparkComputeName = runParameters.Get("AmlSparkComputeName"),
                AmlWindowsComputeName = runParameters.Get("AmlWindowsComputeName"),
                DataFactoryResourceId = runParameters.Get("DataFactoryResourceId"),
                DataFactoryComputeName = runParameters.Get("DataFactoryComputeName"),
                MigratedAnalyticsAccountName = runParameters.Get("MigratedAnalyticsAccountName"),
                BlueShiftAnalyticsAccountName = runParameters.Get("BlueShiftAnalyticsAccountName"),
                BatchResourceId = runParameters.Get("BatchResourceId"),
                RepositoryId = runParameters.Get("RepositoryId"),
                DataTransferWorkspaceName = runParameters.Get("DataTransferWorkspaceName"),
                DataTransferDataFactoryName = runParameters.Get("DataTransferDataFactoryName"),
                DataTransferDefaultBlobDatastoreName = runParameters.Get("DataTransferDefaultBlobDatastoreName"),
                DataTransferBlobDatastoreName = runParameters.Get("DataTransferBlobDatastoreName"),
                DataTransferBlobDatastoreName_SAS = runParameters.Get("DataTransferBlobDatastoreName_SAS"),
                DataTransferAdlsGen1DatastoreName = runParameters.Get("DataTransferAdlsGen1DatastoreName"),
                DataTransferAdlsGen2DatastoreName = runParameters.Get("DataTransferAdlsGen2DatastoreName"),
                DataTransferSqlDBDatastoreName = runParameters.Get("DataTransferSqlDBDatastoreName"),
                DataTransferSqlDBDatastoreName_DBCredential = runParameters.Get("DataTransferSqlDBDatastoreName_DBCredential"),
                DataTransferPostgresDBDatastoreName = runParameters.Get("DataTransferPostgresDBDatastoreName"),
                DataTransferMySqlDBDatastoreName = runParameters.Get("DataTransferMySqlDBDatastoreName"),
                DataTransferBlobDatastoreName_NoCredential = runParameters.Get("DataTransferBlobDatastoreName_NoCredential"),
                DataTransferAdlsGen1DatastoreName_NoCredential = runParameters.Get("DataTransferAdlsGen1DatastoreName_NoCredential"),
                DatasetE2EADLGen1srcName = runParameters.Get("DatasetE2EADLGen1srcName"),
                DatasetE2EBlobsnkName = runParameters.Get("DatasetE2EBlobsnkName"),
                DatasetE2EADLGen1snkName = runParameters.Get("DatasetE2EADLGen1snkName"),
                DataTransferAdlsGen2DatastoreName_NoCredential = runParameters.Get("DataTransferAdlsGen2DatastoreName_NoCredential"),
                SingularityWorkspaceName = runParameters.Get("SingularityWorkspaceName"),
            };

            if (_testConfig.SubscriptionId == null)
            {
                throw new InvalidOperationException("Failed to get runSettings! For local runs, please select 'shared/.runsettings' file in 'Test > Test Settings > Select Test Settings File' menu");
            }

            WorkspaceIdentity = new WorkspaceIdentity()
            {
                SubscriptionId = _testConfig.SubscriptionId,
                ResourceGroupName = _testConfig.ResourceGroup,
                WorkspaceName = _testConfig.WorkspaceName,
                WorkspaceId = _testConfig.WorkspaceId,
            };

            WorkspaceContext = WorkspaceIdentity.ConvertToWorkspaceContext2();

            AetherEnvironment CreateEnv(string resourceGroupName, string workspaceName) =>
                AetherEnvironment.Create(
                    subscriptionId: _testConfig.SubscriptionId,
                    resourceGroupName: resourceGroupName,
                    workspaceName: workspaceName,
                    endpointAddress: GetAetherEndpoint(),
                    snapshotEndpoint: TestEnvironment.GetAzureMLServiceEndpoint(),
                    apiVersion: AetherEnvironment.DefaultApiVersion,
                    authToken: null,
                    accessTokenProvider: _tokenProvider.GetAccessTokenAsync,
                    dstsHttpMessageHandlerProvider: null,
                    onBehalfOfAzureUser: null,
                    retryPolicy: AetherEnvironment.DefaultRetryPolicy,
                    userAgent: "bbaetherlibary/integrationtest");

            _testEnvironment = CreateEnv(_testConfig.ResourceGroup, _testConfig.WorkspaceName);
            _testListAmlEnvironment = CreateEnv(_testConfig.ListAmlResourceGroup, _testConfig.ListAmlWorkspaceName);
            _testSingularityEnvironment = CreateEnv(_testConfig.ResourceGroup, _testConfig.SingularityWorkspaceName);
            _resourceGroup = _testEnvironment.WorkspaceIdentity.ResourceGroupName;
            _workspaceName = _testEnvironment.WorkspaceIdentity.WorkspaceName;
            _subscriptionId = _testEnvironment.WorkspaceIdentity.SubscriptionId;
            _workspaceId = _testEnvironment.WorkspaceIdentity.WorkspaceId;

            InitApiCallers();

            await Task.CompletedTask;
        }

        [OneTimeSetUp]
        public async Task SetUpBase()
        {
            await InitAsync();
        }

        public static string GetAetherEndpoint()
        {
            return TestEnvironment.GetAetherApiEndpoint();
        }

        public static bool IsRunningInInt()
        {
            return TestEnvironment.IsRunningInInt();
        }

        public static bool IsRunningProvisioned()
        {
            string testHarness = Environment.GetEnvironmentVariable("TestHarness");
            return string.Compare(testHarness, "Provisioned", StringComparison.InvariantCultureIgnoreCase) == 0;
        }

        public static bool IsRunningInCloudTestOrVsts()
        {
            if (IsRunningProvisioned())
            {
                return true;
            }

            string testHarness = Environment.GetEnvironmentVariable("TestHarness");
            foreach (string supportedHarness in new[] { "CloudTest", "VSTS" })
            {
                if (string.Compare(testHarness, supportedHarness, StringComparison.InvariantCultureIgnoreCase) == 0)
                {
                    return true;
                }
            }

            return false;
        }

        protected async Task<DatasetDto> GetDatasetAsync(string name)
        {
            return await _datasetApiCaller.GetDatasetByNameAsync(name).ConfigureAwait(false);
        }

        protected async Task<string> GetDatasetIdAsync(string name)
        {
            var dataset = await GetDatasetAsync(name).ConfigureAwait(false);
            return dataset.DatasetId.ToString();
        }

        protected async Task<string> GetDatasetSavedIdAsync(string name)
        {
            var dataset = await GetDatasetAsync(name).ConfigureAwait(false);
            return dataset.Latest.SavedDatasetId;
        }

        protected async Task<string> GetSerializedAssetIdAsync(string name)
        {
            return await _dataAssetApiCaller.GetSerializedAssetIdAsync(name).ConfigureAwait(false);
        }

        protected async Task<IPipelineRun> RunTestWithLogsAsync(
            IGraph graph,
            AetherEnvironment environment,
            int timeoutMinutes = DefaultWaitTimeForPipelineRun,
            IDictionary<string, DataSetDefinitionValue> dataSetDefinitionValueAssignments = default,
            NodeCompositionMode nodeCompositionMode = NodeCompositionMode.None,
            [CallerMemberName] string memberName = "",
            [CallerFilePath] string sourceFilePath = "",
            [CallerLineNumber] int sourceLineNumber = 0,
            Dictionary<string, string> extraTags = null,
            Dictionary<string, string> parameterAssignment = null,
            string runHistoryExperimentName = "IntegrationTestNew",
            IDictionary<string, AssetOutputSettings> assetOutputSettingsAssignments = null,
            CloudPrioritySetting defaultCloudPriority = null,
            string runIdPrefix = "runid",
            bool? enforceRerun = true,
            IdentitySetting identityConfig = null)
        {
            string caller = $"[{memberName}] {sourceFilePath}:{sourceLineNumber}";

            try
            {
                string pipelineRunId = await SetPipelineRunCreationInfoAndSubmitPipeline(graph, environment, caller, dataSetDefinitionValueAssignments, nodeCompositionMode, memberName, extraTags, parameterAssignment, runHistoryExperimentName, assetOutputSettingsAssignments, defaultCloudPriority, runIdPrefix, enforceRerun, identityConfig);

                return await FetchExperimentOnceCompleteAsync(
                    environment,
                    pipelineRunId,
                    timeoutMinutes,
                    memberName,
                    sourceFilePath,
                    sourceLineNumber);
            }
            catch (Exception ex)
            {
                Trace.Write($"RunTestWithLogsAsync FAILED for caller {caller}: {ex}");
                throw;
            }
        }

        protected async Task<string> RunTestWithLogsAndNotWaitForCompletionAsync(
            IGraph graph,
            AetherEnvironment environment,
            IDictionary<string, DataSetDefinitionValue> dataSetDefinitionValueAssignments = default,
            NodeCompositionMode nodeCompositionMode = NodeCompositionMode.None,
            [CallerMemberName] string memberName = "",
            [CallerFilePath] string sourceFilePath = "",
            [CallerLineNumber] int sourceLineNumber = 0,
            Dictionary<string, string> extraTags = null,
            Dictionary<string, string> parameterAssignment = null,
            string runHistoryExperimentName = "IntegrationTestNew",
            IDictionary<string, AssetOutputSettings> assetOutputSettingsAssignments = null,
            CloudPrioritySetting defaultCloudPriority = null,
            string runIdPrefix = "runid",
            bool? enforceRerun = true)
        {
            string caller = $"[{memberName}] {sourceFilePath}:{sourceLineNumber}";

            try
            {
                return await SetPipelineRunCreationInfoAndSubmitPipeline(graph, environment, caller, dataSetDefinitionValueAssignments, nodeCompositionMode, memberName, extraTags, parameterAssignment, runHistoryExperimentName, assetOutputSettingsAssignments, defaultCloudPriority, runIdPrefix, enforceRerun);
            }
            catch (Exception ex)
            {
                Trace.Write($"RunTestWithLogsAsync FAILED for caller {caller}: {ex}");
                throw;
            }
        }

        private void InitApiCallers()
        {
            _datasetApiCaller = new DatasetApiCaller(_tokenProvider, WorkspaceContext);
            _dataAssetApiCaller = new DataAssetApiCaller(_tokenProvider, WorkspaceContext);
        }

        private async Task<string> SetPipelineRunCreationInfoAndSubmitPipeline(
            IGraph graph,
            AetherEnvironment environment,
            string caller,
            IDictionary<string, DataSetDefinitionValue> dataSetDefinitionValueAssignments = default,
            NodeCompositionMode nodeCompositionMode = NodeCompositionMode.None,
            [CallerMemberName] string memberName = "",
            Dictionary<string, string> extraTags = null,
            Dictionary<string, string> parameterAssignment = null,
            string runHistoryExperimentName = "IntegrationTestNew",
            IDictionary<string, AssetOutputSettings> assetOutputSettingsAssignments = null,
            CloudPrioritySetting defaultCloudPriority = null,
            string runIdPrefix = "runid",
            bool? enforceRerun = true,
            IdentitySetting identityConfig = null)
        {
            PipelineRunCreationInfo info = new PipelineRunCreationInfo
            {
                RunHistoryExperimentName = runHistoryExperimentName,
                RunId = runIdPrefix + Guid.NewGuid().ToString(),
                ParameterAssignments = parameterAssignment,
                DataSetDefinitionValueAssignments = dataSetDefinitionValueAssignments,
                AssetOutputSettingsAssignments = assetOutputSettingsAssignments,
#pragma warning disable CS0612 // Type or member is obsolete
                NodeCompositionMode = nodeCompositionMode,
#pragma warning restore CS0612 // Type or member is obsolete
                DefaultCloudPriority = defaultCloudPriority,
                EnforceRerun = enforceRerun,
                IdentityConfig = identityConfig,
            };

            if (memberName != null)
            {
                IDictionary<string, string> tags = new Dictionary<string, string>();
                // Set test case name, requested user, build id, etc to the tag of the run,
                // to make it easy to see which test case invoked the run.
                tags.AddIfNotNull("TestCase", TestContext.CurrentContext.Test.Name);
                tags.AddIfNotNull("RequestedFor", Environment.GetEnvironmentVariable("BUILD_REQUESTEDFOR"));
                tags.AddIfNotNull("BuildNumber", Environment.GetEnvironmentVariable("BUILD_BUILDNUMBER"));
                tags.AddIfNotNull("PR", GetPrNumber());

                if (extraTags != null)
                {
                    tags = tags.Concat(extraTags).ToDictionary(pair => pair.Key, pair => pair.Value);
                }
                info.KvTags = tags;
            }

            string pipelineRunId = await environment.SubmitPipelineRunAsync(info, graph);
            Trace.WriteLine($"Submitted experiment {pipelineRunId} for caller {caller}");

            return pipelineRunId;
        }

        protected async Task<IPipelineRun> FetchExperimentOnceCompleteAsync(
            AetherEnvironment environment,
            string pipelineRunId,
            int timeoutMinutes = DefaultWaitTimeForPipelineRun,
            [CallerMemberName] string memberName = "",
            [CallerFilePath] string sourceFilePath = "",
            [CallerLineNumber] int sourceLineNumber = 0)
        {
            TimeSpan timeout = TimeSpan.FromMinutes(Math.Min(timeoutMinutes, MaxWaitTimeForPipelineRun));

            _runStateTracking.StartFetchExperimentLoop(memberName, pipelineRunId, timeout);

            string caller = $"[{memberName}] {sourceFilePath}:{sourceLineNumber}";

            try
            {
                Task timeoutTask = Task.Delay(timeout);

                Task<IPipelineRun> pipelineRunCompletionTask = WaitRunToCompleteAsync(environment, pipelineRunId, memberName);

                if (await Task.WhenAny(pipelineRunCompletionTask, timeoutTask) == pipelineRunCompletionTask)
                {
                    Trace.WriteLine($"Experiment has finished for caller {caller}.");

                    _runStateTracking.OnRunComplete(memberName, pipelineRunId);

                    IPipelineRun pipelineRun = await pipelineRunCompletionTask;

                    try
                    {
                        await Logger.LogExperimentInfoAsync(
                            experiment: pipelineRun,
                            testClass: TestContext.CurrentContext.Test.ClassName);
                    }
                    catch (Exception ex)
                    {
                        Trace.WriteLine($"ExperimentLogger exception: {ex}");
                    }

                    return pipelineRun;
                }

                _runStateTracking.RunTimedOut(memberName, pipelineRunId);
                throw new TimeoutException($"Test was waiting for pipeline run {pipelineRunId} to finish, and gave up after {timeoutMinutes} minutes");
            }
            catch (Exception ex)
            {
                _runStateTracking.RunFailedWithException(memberName, pipelineRunId, ex);
                Trace.Write($"FetchExperimentOnceCompleteAsync FAILED for caller {caller}: {ex}");
                throw;
            }
        }

        private async Task<IPipelineRun> WaitRunToCompleteAsync(
            AetherEnvironment environment,
            string pipelineRunId,
            string memberName = "")
        {
            return await Task.Run(async () =>
            {
                IPipelineRun pipelineRun = null;
                PipelineRunStatusCode status = PipelineRunStatusCode.NotStarted;

                while (status == PipelineRunStatusCode.NotStarted || status == PipelineRunStatusCode.Running)
                {
                    pipelineRun = await environment.GetPipelineRunAsync(pipelineRunId);
                    status = await pipelineRun.GetStatusAsync();

                    _runStateTracking.UpdateRunStatus(memberName, pipelineRunId, status);
                    await Task.Delay(TimeSpan.FromSeconds(5));
                }

                Trace.WriteLine($"Pipeline run {pipelineRunId} has reached final state {status}.");

                return pipelineRun;
            });
        }

        protected void AssertJsonEqual<T>(T expected, T actual)
        {
            var expectedStr = JsonSerializer.Serialize(expected);
            var actualStr = JsonSerializer.Serialize(actual);
            Assert.AreEqual(expectedStr, actualStr);
        }

        protected T LoadJson<T>(string relativePath)
        {
            var options = new JsonSerializerOptions();
            options.Converters.Add(new JsonStringEnumConverter());  // Make sure Enum could be recognized.
            var filePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "TestResources", relativePath);
            return JsonSerializer.Deserialize<T>(File.ReadAllText(filePath), options);
        }

        /// <summary>
        /// Get PR number from environment variable.
        ///
        /// When build in DevOps CI, the environment variable BUILD_SOURCEBRANCH
        /// will look like "refs/pull/738023/merge", the third part is the PR number.
        /// </summary>
        /// <returns>The PR number.</returns>
        protected string GetPrNumber()
        {
            string branch = Environment.GetEnvironmentVariable("BUILD_SOURCEBRANCH");
            string[] splits = branch?.Split('/');
            return splits?.Length > 2 ? splits[2] : null;
        }
    }
}