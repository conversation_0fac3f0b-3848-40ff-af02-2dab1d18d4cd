﻿using Microsoft.Aether.BlueBox.Library;
using System;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace BBAetherLibrary.IntegrationTests.Common
{
    public class ExperimentLogger
    {
        private const string visualGraphRelativePath = "VisualGraph.log";
        private const string executionGraphPath = "ExecutionGraph.log";
        private const string nodeExecutionFolder = "NodeExecutionLogs";
        private string BaseLogPath;

        public ExperimentLogger(string baseLogPath)
        {
            BaseLogPath = baseLogPath;
        }

        public async Task LogExperimentInfoAsync(IPipelineRun experiment, string testClass)
        {
            string testPath = Path.Combine(BaseLogPath, testClass, experiment.Id.Substring(0, 8));
            Directory.CreateDirectory(testPath);

            await LogGraphInfoAsync(experiment, testPath);
            await LogExecutionGraphInfoAsync(experiment, testPath);
        }

        private async Task LogGraphInfoAsync(IPipelineRun experiment, string logPath)
        {
            string visualGraphFile = @"\\?\" + Path.Combine(logPath, visualGraphRelativePath);
            IGraph graphFromExperiment = await experiment.GetGraphAsync();

            WriteLog(visualGraphFile, $"Got graph with {graphFromExperiment.Nodes.Count()} nodes");
        }

        private async Task LogExecutionGraphInfoAsync(IPipelineRun experiment, string logPath)
        {
            string executionGraphFile = @"\\?\" + Path.Combine(logPath, executionGraphPath);
            IExecutionGraph executionGraph = await experiment.GetExecutionGraphAsync();
            WriteLog(executionGraphFile, $"Got execution graph with {executionGraph.Nodes.Count()} nodes");

            var executionInfo = await executionGraph.GetAllNodesExecutionInfoAsync();
            WriteLog(executionGraphFile, $"Got execution info with {executionInfo.Count} nodes");
            foreach (var item in executionInfo)
            {
                WriteLog(executionGraphFile, $"Got node {item.Key} with code {item.Value.StatusCode}");
                WriteLog(executionGraphFile, $"\tDetails: {item.Value.StatusDetail}");

                Trace.WriteLine($"node Id: {item.Key}, Node Status {item.Value.StatusCode}");
            }

            string nodeExecutionPath = Path.Combine(logPath, nodeExecutionFolder);
            foreach (IModuleNode module in executionGraph.ModuleNodes)
            {
                INodeExecution nodeExecution = await executionGraph.GetNodeExecutionAsync(module.Id);
                string nodeLogPath = @"\\?\" + Path.Combine(nodeExecutionPath, module.Name);
                Directory.CreateDirectory(nodeLogPath);
                await PrintNodeExecutionInfoAsync(nodeExecution, nodeLogPath);
            }
        }

        private async Task PrintNodeExecutionInfoAsync(INodeExecution nodeExecution, string logPath)
        {
            try
            {
                WriteLog(Path.Combine(logPath, "joblog.log"), await nodeExecution.GetJobLogAsync());
                WriteLog(Path.Combine(logPath, "stdout.log"), await nodeExecution.GetStdOutAsync());
                WriteLog(Path.Combine(logPath, "stderr.log"), await nodeExecution.GetStdErrAsync());
            }
            catch (Exception e)
            {
                //The log might not be available if the node hasn't started or cancelled.
                Trace.WriteLine($"ExperimentLogger exception while getting job logs: {e}");
            }
        }

        private void WriteLog(string logPath, string content)
        {
            File.AppendAllText(logPath, content + "\n");
        }
    }
}