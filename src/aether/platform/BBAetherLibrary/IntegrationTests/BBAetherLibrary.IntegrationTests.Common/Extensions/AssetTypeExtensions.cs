﻿using Microsoft.Aether.AEVA.DataContracts;

namespace BBAetherLibrary.IntegrationTests.Common.Extensions
{
    public static class AssetTypeExtensions
    {
        public static string ToDataTypeId(this AssetType assetType)
        {
            return assetType switch
            {
                AssetType.UriFolder => "uri_folder",
                AssetType.UriFile => "uri_file",
                AssetType.MLTable => "mltable",
                AssetType.MLFlowModel => "mlflow_model",
                AssetType.CustomModel => "mlflow_model",
                AssetType.TritonModel => "mlflow_model",
                AssetType.OpenAIModel => "mlflow_model",
                _ => "uri_folder",
            };
        }
    }
}
