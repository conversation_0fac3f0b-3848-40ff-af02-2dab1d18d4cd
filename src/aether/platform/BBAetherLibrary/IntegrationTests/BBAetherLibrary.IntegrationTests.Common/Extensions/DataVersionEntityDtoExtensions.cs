﻿using Microsoft.Aether.AEVA.DataContracts;
using Microsoft.MachineLearning.Dataset.Contracts;
using System;

namespace BBAetherLibrary.IntegrationTests.Common.Extensions
{
    public static class DataVersionEntityDtoExtensions
    {
        public static AssetType GetAssetType(this DataVersionEntityDto dataVersionEntityDto)
        {
            var strDataType = dataVersionEntityDto?.DataVersion.DataType;
            if (Enum.TryParse(strDataType, out AssetType result))
            {
                return result;
            }
            else
            {
                throw new Exception("Unrecognized AssetType 'strDataType'.");
            }
        }

        public static AssetDefinition ToAssetDefinition(this DataVersionEntityDto dataVersionEntityDto)
        {
            return new AssetDefinition
            {
                Type = dataVersionEntityDto.GetAssetType(),
                AssetId = dataVersionEntityDto.DataVersion.AssetId,
            };
        }

        public static DataSetDefinition ToDataSetDefinition(this DataVersionEntityDto dataVersionEntityDto)
        {
            return new DataSetDefinition()
            {
                DataTypeShortName = dataVersionEntityDto.GetAssetType().ToDataTypeId(),
                Value = new DataSetDefinitionValue()
                {
                    AssetDefinition = dataVersionEntityDto.ToAssetDefinition(),
                }
            };
        }
    }
}
