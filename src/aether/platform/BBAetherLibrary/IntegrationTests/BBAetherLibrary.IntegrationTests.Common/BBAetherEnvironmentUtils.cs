﻿using BBAetherLibrary.Common;
using Microsoft.Aether.AEVA.DataContracts;
using Microsoft.Aether.BlueBox.Library;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;

namespace BBAetherLibrary.IntegrationTests.Common
{
    public class BBAetherEnvironmentUtils
    {
        public static AetherEnvironment CreateTestAetherEnvironment(string configPath, string aetherEndpoint, Func<Task<string>> accessTokenProvider)
        {
            TestEnvironmentConfig environmentConfig = CreateAetherEnvironmentConfig(configPath);

            return AetherEnvironment.Create(
                subscriptionId: environmentConfig.SubscriptionId,
                resourceGroupName: environmentConfig.ResourceGroup,
                workspaceName: environmentConfig.WorkspaceName,
                retryPolicy: RetryPolicyFactory.GetDefaultRetryPolicy(),
                endpointAddress: aetherEndpoint,
                snapshotEndpoint: TestEnvironment.GetAzureMLServiceEndpoint(),
                authToken: null,
                accessTokenProvider: accessTokenProvider);
        }

        private static TestEnvironmentConfig CreateAetherEnvironmentConfig(string configPath)
        {
            var configurationBuilder = new ConfigurationBuilder()
                .AddXmlFile(configPath);
            var configuration = configurationBuilder.Build();

            return new TestEnvironmentConfig
            {
                SubscriptionId = configuration["AzureSubscription"],
                ResourceGroup = configuration["ResourceGroup"],
                WorkspaceName = configuration["WorkspaceName"]
            };
        }

        public static async Task<IGraph> CreateSingleNodeGraph(AetherEnvironment environment, string moduleId)
        {
            var graph = environment.CreateNewGraph();
            var module = await environment.GetModuleAsync(moduleId).ConfigureAwait(false);
            var moduleNode = graph.AddNode(module);
            return graph;
        }

        public static async Task CreateDataTypesIfNeededAsync(AetherEnvironment environment)
        {
            var types = new HashSet<string>((await environment.GetAllDataTypesAsync()).Select(dataType => dataType.Id));

            if (!types.Contains("AzureBlobReference"))
            {
                await environment.CreateDataTypeAsync(new DataTypeCreationInfo(id: "AzureBlobReference",
                                                                               name: "AzureBlobReference",
                                                                               description: "AzureBlobReference",
                                                                               isDirectory: false));
                Trace.WriteLine("Created AzureBlobReference data type");
            }

            if (!types.Contains("AzureDataLakeReference"))
            {
                await environment.CreateDataTypeAsync(new DataTypeCreationInfo(id: "AzureDataLakeReference",
                                                                               name: "AzureDataLakeReference",
                                                                               description: "AzureDataLakeReference",
                                                                               isDirectory: false));
                Trace.WriteLine("Created AzureDataLakeReference data type");
            }

            if (!types.Contains("AnyFile"))
            {
                await environment.CreateDataTypeAsync(new DataTypeCreationInfo(id: "AnyFile",
                                                                               name: "AnyFile",
                                                                               description: "AnyFile",
                                                                               isDirectory: false));
                Trace.WriteLine("Created AnyFile data type");
            }

            if (!types.Contains("AnyDirectory"))
            {
                await environment.CreateDataTypeAsync(new DataTypeCreationInfo(id: "AnyDirectory",
                                                                               name: "AnyDirectory",
                                                                               description: "AnyDirectory",
                                                                               isDirectory: true));
                Trace.WriteLine("Created AnyDirectory data type");
            }

            if (!types.Contains("AzureDataLakeGen2Reference"))
            {
                await environment.CreateDataTypeAsync(new DataTypeCreationInfo(id: "AzureDataLakeGen2Reference",
                                                                               name: "AzureDataLakeGen2Reference",
                                                                               description: "AzureDataLakeGen2Reference",
                                                                               isDirectory: false));
                Trace.WriteLine("Created AzureDataLakeGen2Reference data type");
            }

            if (!types.Contains("AzureMySqlDatabaseReference"))
            {
                await environment.CreateDataTypeAsync(new DataTypeCreationInfo(id: "AzureMySqlDatabaseReference",
                                                                               name: "AzureMySqlDatabaseReference",
                                                                               description: "AzureMySqlDatabaseReference",
                                                                               isDirectory: false));
                Trace.WriteLine("Created AzureMySqlDatabaseReference data type");
            }

            if (!types.Contains("AzurePostgresDatabaseReference"))
            {
                await environment.CreateDataTypeAsync(new DataTypeCreationInfo(id: "AzurePostgresDatabaseReference",
                                                                               name: "AzurePostgresDatabaseReference",
                                                                               description: "AzurePostgresDatabaseReference",
                                                                               isDirectory: false));
                Trace.WriteLine("Created AzurePostgresDatabaseReference data type");
            }

            if (!types.Contains("AzureSqlDatabaseReference"))
            {
                await environment.CreateDataTypeAsync(new DataTypeCreationInfo(id: "AzureSqlDatabaseReference",
                                                                               name: "AzureSqlDatabaseReference",
                                                                               description: "AzureSqlDatabaseReference",
                                                                               isDirectory: false));
                Trace.WriteLine("Created AzureSqlDatabaseReference data type");
            }
        }

        public static async Task<string> UploadADLADataSourceAsync(AetherEnvironment environment)
        {
            return await DataStoreUtils.CreateDataStoreDataSourceAsync(
                environment,
                name: "adlstoretestdata",
                description: "adlstoretestdata",
                amlDataStoreName: BBAetherLibrary.Common.TestEnvironment.GetFromConfig("AmlDataStoreNameForScheduler"),
                pathOnDataStore: "testdata/testdata.txt",
                identifierHash: Guid.NewGuid().ToString(),
                contentHash: null);
        }

        public static async Task<string> UploadADLAModuleAsync(AetherEnvironment environment, string modulePath, bool isDeterministic = false)
        {
            var dataTypes = await environment.GetAllDataTypesAsync();
            var adlsDataType = dataTypes.First(dataType => dataType.Id.Equals("AzureDataLakeReference", StringComparison.InvariantCultureIgnoreCase));

            return await environment.UploadModuleAsync(
                sourcePath: modulePath,
                uploadInfo: new ModuleUploadInfo(
                name: "Test adla",
                displayName: "Test adla display",
                description: "Test adla",
                isDeterministic: isDeterministic,
                cloudSystem: "AdlCloud",
                structuredInterface: new StructuredInterface
                {
                    Inputs = new List<StructuredInterfaceInput>
                    {
                        new StructuredInterfaceInput { Name = "script_input", DataTypeIdsList = new List<string> { adlsDataType.Id } },
                    },
                    Outputs = new List<StructuredInterfaceOutput>
                    {
                        new StructuredInterfaceOutput { Name = "script_output", DataTypeId = adlsDataType.Id },
                    },
                    MetadataParameters = new List<StructuredInterfaceParameter>
                    {
                        new StructuredInterfaceParameter { Name = "ScriptName", ParameterType = ParameterType.String },
                        new StructuredInterfaceParameter { Name = "SubscriptionId", ParameterType = ParameterType.String },
                        new StructuredInterfaceParameter { Name = "ResourceGroupName", ParameterType = ParameterType.String },
                        new StructuredInterfaceParameter { Name = "AnalyticsAccountName", ParameterType = ParameterType.String },
                    }
                }));
        }

        public static void SetADLAModuleParams(IModuleNode module, string scriptName, string configPath)
        {
            IReadOnlyDictionary<string, IAssignableParameter> metadataParams = module.MetadataParameters;
            metadataParams["ScriptName"].Value = scriptName;

            var configuration = new ConfigurationBuilder()
                .AddXmlFile(configPath)
                .Build();

            metadataParams["SubscriptionId"].Value = configuration["AzureSubscription"];
            metadataParams["ResourceGroupName"].Value = configuration["AdlaResourceGroup"];
            metadataParams["AnalyticsAccountName"].Value = configuration["AdlaAccountName"];
        }

        public static async Task<string> UploadESCloudModule(AetherEnvironment environment, string sourceFolder, string computeName)
        {
            // Create Sample Escloud dsvm module
            var structuredInterface = new StructuredInterface
            {
                MetadataParameters = new List<StructuredInterfaceParameter>
                {
                    new StructuredInterfaceParameter { Name = "TargetType" , DefaultValue = "MLC", ParameterType = ParameterType.String, IsOptional = false},                },
                Parameters = new List<StructuredInterfaceParameter>
                {
                    new StructuredInterfaceParameter { Name = "Target" , DefaultValue =  computeName, ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "MLCComputeType" , DefaultValue = "AmlCompute", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "PrepareEnvironment" , DefaultValue = "true", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "Script" , DefaultValue = "main.py", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "Arguments" , DefaultValue = "5", ParameterType = ParameterType.String, IsOptional = true},
                    new StructuredInterfaceParameter { Name = "Framework" , DefaultValue = "Python", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "UserManagedDependencies" , DefaultValue = "false", ParameterType = ParameterType.String, IsOptional = true},
                    new StructuredInterfaceParameter { Name = "CondaDependencies" , DefaultValue = "{\"name\": \"project_environment\", \"dependencies\": [\"python = 3.9\", {\"pip\": [\"azureml-defaults\"]}]}", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "DockerEnabled" , DefaultValue = "true", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "BaseDockerImage" , DefaultValue = "mcr.microsoft.com/azureml/openmpi4.1.0-ubuntu22.04", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "SharedVolumes" , DefaultValue = "true", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "SparkRepositories" , DefaultValue = "https://mmlspark.azureedge.net/maven", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "SparkMavenPackages" , DefaultValue = "[{\"group\":\"com.microsoft.ml.spark\",\"artifact\":\"mmlspark_2.11\",\"version\": \"0.12\"}]", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "HistoryEnabled" , DefaultValue = "true", ParameterType = ParameterType.String, IsOptional = true},
                    new StructuredInterfaceParameter { Name = "SparkConfiguration" , DefaultValue = "spark.app.name=AzureMLExperiment;spark.yarn.maxAppAttempts=1", ParameterType = ParameterType.String, IsOptional = true},
                    new StructuredInterfaceParameter { Name = "ContainerInstanceRegion" , DefaultValue = "None", ParameterType = ParameterType.String, IsOptional = true},
                    new StructuredInterfaceParameter { Name = "ContainerInstanceCpuCores" , DefaultValue = "1", ParameterType = ParameterType.String, IsOptional = true},
                    new StructuredInterfaceParameter { Name = "ContainerInstanceMemoryGb" , DefaultValue = "4", ParameterType = ParameterType.String, IsOptional = true},
                },
            };
            var moduleUploadInfo = new ModuleUploadInfo(
               name: "escloud test module",
               displayName: "escloud test module display",
               description: "escloud test module",
               isDeterministic: false,
               cloudSystem: "escloud",
               structuredInterface: structuredInterface);


            return await environment.UploadModuleAsync(sourceFolder, moduleUploadInfo);
        }
    }
}
