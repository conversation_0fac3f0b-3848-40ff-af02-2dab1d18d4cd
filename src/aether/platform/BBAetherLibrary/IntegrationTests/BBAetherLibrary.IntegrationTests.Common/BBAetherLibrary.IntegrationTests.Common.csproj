﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
  </PropertyGroup>
  <ItemGroup>
    <Content Include="..\shared\cache\**">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Azure.Identity" />
    <PackageReference Include="Microsoft.Extensions.Configuration" />
    <PackageReference Include="Microsoft.Extensions.Caching.Memory" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Xml" />
    <PackageReference Include="Microsoft.IdentityModel.Clients.ActiveDirectory" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" />
    <PackageReference Include="NUnit" />
    <PackageReference Include="NUnit3TestAdapter" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="$(AetherSrcRoot)\platform\backendV2\shared\Microsoft.Aether.S2S.Common\Microsoft.Aether.S2S.Common.csproj" />
    <ProjectReference Include="$(AetherSrcRoot)\platform\backendV2\shared\Microsoft.Aether.TokenProvider\Microsoft.Aether.TokenProvider.csproj" />
    <ProjectReference Include="$(AetherSrcRoot)\platform\backendV2\shared\Microsoft.Aether.BackendCommon\Microsoft.Aether.BackendCommon.csproj" />
    <ProjectReference Include="$(AetherSrcRoot)\platform\BBAetherLibrary\Framework\BBAetherLibrary.Framework.csproj" />
    <ProjectReference Include="$(AetherSrcRoot)\platform\BBAetherLibrary\Common\BBAetherLibrary.Common.csproj" />
    <ProjectReference Include="$(SrcRoot)\Common\Testing\Common.Testing.csproj" />
    <ProjectReference Include="$(SrcRoot)\Dataset\Contracts\Dataset.Contracts.csproj" />
  </ItemGroup>
</Project>
