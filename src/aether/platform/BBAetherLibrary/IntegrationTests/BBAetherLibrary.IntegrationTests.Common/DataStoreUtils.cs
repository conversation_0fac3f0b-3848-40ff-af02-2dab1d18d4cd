﻿using Microsoft.Aether.AEVA.DataContracts;
using Microsoft.Aether.BlueBox.Library;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace BBAetherLibrary.IntegrationTests.Common
{
    public class DataStoreUtils
    {
        public static async Task<string> CreateDataStoreDataSourceAsync(
            AetherEnvironment environment,
            string name,
            string description,
            string amlDataStoreName,
            string pathOnDataStore,
            string identifierHash,
            string contentHash,
            string sqlQuery = null,
            string tableName = null,
            string storedProcName = null,
            List<StoredProcedureParameter> storedProcParams = null)
        {
            var creationInfo = new DataSourceCreationInfo
            {
                Name = name,
                Description = description,
                DataStoreName = amlDataStoreName,
                PathOnDataStore = pathOnDataStore,
                IdentifierHash = identifierHash,
                ContentHash = contentHash,
                SqlQuery = sqlQuery,
                SqlTableName = tableName,
                SqlStoredProcedureName = storedProcName,
                SqlStoredProcedureParams = storedProcParams
            };
            
            return await environment.CreateDataSourceAsync(creationInfo);
        }


        public static async Task<IAetherDataSource> GetDataSetAsync(
            AetherEnvironment environment,
            string dataType,
            string datasetName,
            string datasetId,
            string version)
        {
            return await environment.GetDataSourceAsync(
                        dataSetDefinition: new DataSetDefinition
                        {
                            DataTypeShortName = dataType,
                            Value = new DataSetDefinitionValue
                            {
                                DataSetReference = new RegisteredDataSetReference
                                {
                                    Name = datasetName,
                                    Id = datasetId,
                                    Version = version
                                }
                            }
                        });
        }
    }
}
