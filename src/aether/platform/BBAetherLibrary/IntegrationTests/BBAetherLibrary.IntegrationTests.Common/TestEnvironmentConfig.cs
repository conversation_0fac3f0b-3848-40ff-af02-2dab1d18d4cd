﻿namespace BBAetherLibrary.IntegrationTests.Common
{
    public struct TestEnvironmentConfig
    {
        // Workspace info
        public string SubscriptionId;
        public string ResourceGroup;
        public string WorkspaceName;
        public string WorkspaceId;
        public string ListAmlResourceGroup;
        public string ListAmlWorkspaceName;

        // Datastores registered to workspace
        public string BlobDatastoreName;
        public string AdlsDatastoreName;
        public string AdlsGen2DatastoreName;
        public string MigratedAdlsDatastoreName;

        // Names of computes attached to workspace
        public string AmlComputeName;
        public string AmlSparkComputeName;
        public string AmlWindowsComputeName;

        // Resource ids for computes attached to workspace
        public string DataFactoryResourceId;
        public string DataFactoryComputeName;
        public string MigratedDataLakeAnalyticsResourceId;
        public string MigratedAnalyticsAccountName;
        public string BlueShiftAnalyticsAccountName;
        public string BatchResourceId;

        // Repository info
        public string RepositoryId;

        // DataTransfer resources
        public string DataTransferWorkspaceName;
        public string DataTransferDataFactoryName;
        public string DataTransferDefaultBlobDatastoreName;
        public string DataTransferBlobDatastoreName;
        public string DataTransferBlobDatastoreName_SAS;
        public string DataTransferAdlsGen1DatastoreName;
        public string DataTransferAdlsGen2DatastoreName;
        public string DataTransferSqlDBDatastoreName;
        public string DataTransferSqlDBDatastoreName_DBCredential;
        public string DataTransferPostgresDBDatastoreName;
        public string DataTransferMySqlDBDatastoreName;

        public string DataTransferBlobDatastoreName_NoCredential;
        public string DataTransferAdlsGen1DatastoreName_NoCredential;
        public string DataTransferAdlsGen2DatastoreName_NoCredential;
        public string DatasetE2EADLGen1srcName;
        public string DatasetE2EBlobsnkName;
        public string DatasetE2EADLGen1snkName;

        public string SingularityWorkspaceName;
    }
}
