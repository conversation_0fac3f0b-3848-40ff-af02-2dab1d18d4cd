# Get data info by name instead of hardcoding

## Background
In some tests, we manually define dataset or asset objects to create data sources like the following code.

``` C#
// Dataset
var dataset_input = await DataStoreUtils.GetDataSetAsync(
    _testDataTransferEnvironment,
    AzureBlobReference,
    datasetName: "dts_blob_tabular",
    datasetId: "7d670b5e-b564-4983-90f0-984a7e36850e",
    version: "1").ConfigureAwait(false);

// Data asset
var asset_input = new DataSetDefinition()
{
    DataTypeShortName = UriFile,
    Value = new DataSetDefinitionValue()
    {
        AssetDefinition = new AssetDefinition()
        {
            AssetId = AssetId.Parse("azureml://locations/centraluseuap/workspaces/{workspace_id}}/data/{name}/versions/{version}"),
            Type = AssetType.UriFile
        }
    }
}; 
```
The premise of this definition is that we must know the dataset/asset id. However, these ids cannot be seen in the UI and can only be obtained through the SDK demo or the developer tool of the web page, which is very inconvenient.

Therefore, in order to get the existing data info more easily, we call the data service to get it by name instead of hardcoding.

## Improvement

We can use `DatasetApiCaller` and `DataAssetApiCaller` to get data infos(dataset or asset) according to the name (optionally version).

`DatasetApiCaller`: Pass dataset name to get DatasetDto.

`DataAssetApiCaller`: Pass data asset name to get DataVersionEntityDto or other asset related things like assetId, serializedAssetId or directly construct DatasetDefinition based on asset info.

However, when the tests run in parallel, the data service will be called multiple times in a short period of time, causing it to return 429. Therefore, we add local file cache to reduce requests, that is, write the data info obtained from the data service into the json file, and get it directly from the local file next time.

## Usage
- Initialize ApiCaller: Initialize a `DatasetApiCaller` and `DataAssetApiCaller` in your test file and get data info by passing name.
- Run tests locally: When the tests run in CI, it will read the cache from the compilation directory without calling data service. So please be sure to run it locally before submitting a PR. When you test locally, a json file will be generated in the `DataFileCache` directory as a cache.
### File Cache Directory Structure
```
-- IntegrationTests\shared\cache
  -- cache
    -- The hash of "{Subscription Id}/{ResourceGroup Name}/{WorkspaceName}"
      -- Datasets
        -- {name}.json
      -- DataAssets
        -- {name}_v{version}.json
```