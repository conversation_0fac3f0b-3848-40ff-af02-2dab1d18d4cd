﻿using BBAetherLibrary.Common;
using BBAetherLibrary.IntegrationTests.Common.Extensions;
using Microsoft.Aether.AEVA.DataContracts;
using Microsoft.Aether.TokenProvider;
using Microsoft.MachineLearning.Common.Core;
using Microsoft.MachineLearning.Common.Core.Contracts;
using Microsoft.MachineLearning.Dataset.Contracts;
using System;
using System.Threading.Tasks;

namespace BBAetherLibrary.IntegrationTests.Common.ApiCallers
{
    public class DataAssetApiCaller : ApiCallerBase
    {
        private readonly WorkspaceContext2 _workspace;
        private readonly IDataVersionController _controller;
        private readonly DataFileCacheHandler<DataVersionEntityDto> _dataFileCache;

        public DataAssetApiCaller(
            ITokenProvider tokenProvider,
            WorkspaceContext2 workspace)
            : base(tokenProvider)
        {
            _workspace = workspace;
            _controller = GetServiceInvoker<IDataVersionController>();
            _dataFileCache = new DataFileCacheHandler<DataVersionEntityDto>(workspace, "DataAssets");
        }

        public async Task<DataVersionEntityDto> GetDataVersionEntityAsync(string name, string version = "1")
        {
            var dataAsset = await _dataFileCache.Get(name, version).ConfigureAwait(false);
            if (dataAsset == null)
            {
                if (!TestEnvironment.IsRunningInLocal())
                {
                    throw new Exception("Please run the tests locally first to generate a local file cache in directory " +
                        "[BBAetherLibrary/IntegrationTests/shared/cache/], then run CI again.\n" +
                        $"You can reference README.md under the directory. Data asset name: {name}, version: {version}");
                }

                dataAsset = await _controller.Get(
                    _workspace.SubscriptionId,
                    _workspace.ResourceGroup,
                    _workspace.WorkspaceName,
                    name,
                    version).ConfigureAwait(false);
                await _dataFileCache.Put(dataAsset, name, version).ConfigureAwait(false);
            }
            return dataAsset;
        }

        public async Task<AssetId> GetAssetIdAsync(string name, string version = "1")
        {
            var dataVersion = await GetDataVersionEntityAsync(name, version).ConfigureAwait(false);
            return dataVersion.DataVersion.AssetId;
        }

        public async Task<string> GetSerializedAssetIdAsync(string name, string version = "1")
        {
            var assetId = await GetAssetIdAsync(name, version).ConfigureAwait(false);
            return assetId.ToString();
        }

        public async Task<DataSetDefinition> GetDatasetDefinitionFromNameAsync(string name, string version = "1")
        {
            var dataVersion = await GetDataVersionEntityAsync(name, version).ConfigureAwait(false);
            return dataVersion.ToDataSetDefinition();
        }
    }
}
