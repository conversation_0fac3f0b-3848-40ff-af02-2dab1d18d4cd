﻿using BBAetherLibrary.Common;
using Microsoft.Aether.TokenProvider;
using Microsoft.MachineLearning.Common.WebApi.Client;
using System;
using System.Net.Http;
using System.Net.Http.Headers;

namespace BBAetherLibrary.IntegrationTests.Common.ApiCallers
{
    public class ApiCallerBase
    {
        public readonly string DefaultEndpointAddress = TestEnvironment.GetAzureMLServiceEndpoint();
        public const string UserAgent = "PipelineTester/ApiCaller";

        private readonly ITokenProvider _tokenProvider;

        public ApiCallerBase(ITokenProvider tokenProvider)
        {
            _tokenProvider = tokenProvider;
        }

        public T GetServiceInvoker<T>()
        {
            var httpClient = new HttpClient();
            httpClient.DefaultRequestHeaders.UserAgent.TryParseAdd(UserAgent);

            return ServiceInvoker.Create<T>(
                client: httpClient,
                serviceAddress: new Uri(DefaultEndpointAddress),
                beforeRequestAsync: async (request, method) =>
                {
                    request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", await _tokenProvider.GetAccessTokenAsync());
                });
        }
    }
}
