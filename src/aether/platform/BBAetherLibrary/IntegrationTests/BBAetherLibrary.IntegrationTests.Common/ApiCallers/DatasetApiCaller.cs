﻿using BBAetherLibrary.Common;
using Microsoft.Aether.TokenProvider;
using Microsoft.MachineLearning.Common.Core.Contracts;
using Microsoft.MachineLearning.Dataset.Contracts;
using System;
using System.Threading.Tasks;

namespace BBAetherLibrary.IntegrationTests.Common.ApiCallers
{
    public class DatasetApiCaller : ApiCallerBase
    {
        private readonly WorkspaceContext2 _workspace;
        private readonly IDatasetsController _controller;
        private readonly DataFileCacheHandler<DatasetDto> _dataFileCache;

        public DatasetApiCaller(
            ITokenProvider tokenProvider,
            WorkspaceContext2 workspace)
            : base(tokenProvider)
        {
            _workspace = workspace;
            _controller = GetServiceInvoker<IDatasetsController>();
            _dataFileCache = new DataFileCacheHandler<DatasetDto>(workspace, "Datasets");
        }

        public async Task<DatasetDto> GetDatasetByNameAsync(string name)
        {
            var dataset = await _dataFileCache.Get(name).ConfigureAwait(false);
            if (dataset == null)
            {
                if (!TestEnvironment.IsRunningInLocal())
                {
                    throw new Exception("Please run the tests locally first to generate a local file cache in directory " +
                        "[BBAetherLibrary/IntegrationTests/shared/cache/], then run CI again.\n" +
                        $"You can reference README.md under the directory. Dataset name: {name}");
                }

                dataset = await _controller.GetDatasetByName(
                    _workspace.SubscriptionId,
                    _workspace.ResourceGroup,
                    _workspace.WorkspaceName,
                    name).ConfigureAwait(false);
                await _dataFileCache.Put(dataset, name).ConfigureAwait(false);
            }
            return dataset;
        }
    }
}
