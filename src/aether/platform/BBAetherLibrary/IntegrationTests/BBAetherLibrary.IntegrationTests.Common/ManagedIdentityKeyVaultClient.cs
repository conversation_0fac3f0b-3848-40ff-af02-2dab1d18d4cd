﻿using Azure.Identity;
using Azure.Security.KeyVault.Secrets;
using Microsoft.RelInfra.Common;
using System;
using System.Threading.Tasks;

namespace Microsoft.Aether.TokenProvider
{
    public class ManagedIdentityKeyVaultClient : IKeyVaultClient
    {
        private readonly SecretClient _secretClient;

        public ManagedIdentityKeyVaultClient(string vaultName, string clientId)
        {
            _secretClient = new SecretClient(new Uri($"https://{vaultName}.vault.azure.net"), new ManagedIdentityCredential(clientId));
        }

        public async Task<string> GetSecretValueAsync(string secretName)
        {
            KeyVaultSecret secret = await _secretClient.GetSecretAsync(secretName);

            return secret.Value;
        }

        public async Task<string> SetSecretValueAsync(string secretName, string secretValue)
        {
            return null;
        }

        public void Dispose()
        {
        }
    }
}
