﻿using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;

using Microsoft.Aether.AEVA.DataContracts;

namespace BBAetherLibrary.IntegrationTests.Common
{
    public class RunStateTracking
    {
        private readonly ConcurrentDictionary<string, RunState> _runStates = new ConcurrentDictionary<string, RunState>();

        private RunState GetRunState(string testName, string runId)
        {
            return _runStates.GetOrAdd($"{testName}/{runId}", new RunState { TestName = testName, RunId = runId });
        }

        public void StartFetchExperimentLoop(string testName, string runId, TimeSpan timeout)
        {
            var runState = GetRunState(testName, runId);
            runState.Timeout = timeout;
            runState.StartTime = DateTime.UtcNow;
        }

        public void RunTimedOut(string testName, string runId)
        {
            var runState = GetRunState(testName, runId);
            runState.RunTimedOut = true;
            runState.FinishTime = DateTime.UtcNow;
        }

        public void RunFailedWithException(string testName, string runId, Exception ex)
        {
            var runState = GetRunState(testName, runId);
            runState.Exception = ex;
            runState.FinishTime = DateTime.UtcNow;
        }

        public void UpdateRunStatus(string testName, string runId, PipelineRunStatusCode status)
        {
            var runState = GetRunState(testName, runId);
            runState.Status = status;
        }

        public void OnRunComplete(string testName, string runId)
        {
            var runState = GetRunState(testName, runId);
            runState.FinishTime = DateTime.UtcNow;
        }

        public void Print()
        {
            int[] columnWidths = new int[] { 50, 10, 36, 10, 10, 10, 10, 50, 100 };
            string[] columnNames = new string[] { "Test", "Submitted?", "Run ID", "Status", "Run time", "Timed out?", "Wait time", "Exception type", "Exception message" };

            var output = new List<string>();

            output.Add(string.Empty);
            output.Add(string.Join(" | ", columnNames.Select((c, i) => Format(c, columnWidths[i]))));

            foreach (var r in _runStates.Values)
            {
                output.Add(string.Join(" | ", new string[] { r.TestName, r.IsRunSubmitted.ToString(), r.RunId, r.Status.ToString(), r.RunTime, r.RunTimedOut.ToString(), r.Timeout.ToString(), r.Exception?.GetType().ToString(), r.Exception?.Message }.Select((v, i) => Format(v, columnWidths[i]))));
            }

            output.Add(string.Empty);

            Trace.WriteLine(string.Join("\n", output));
        }

        private string Format(string s, int displayWidth)
        {
            if (s == null)
            {
                s = string.Empty;
            }

            if (s.Length > displayWidth)
            {
                s = s.Substring(0, displayWidth);
            }

            s = s.PadRight(displayWidth, ' ');
            return s;
        }

        private class RunState
        {
            public string TestName { get; set; }

            public string RunId { get; set; }

            public bool IsRunSubmitted => !string.IsNullOrEmpty(RunId);

            public DateTime? StartTime { get; set; }

            public DateTime? FinishTime { get; set; }

            public string RunTime => FinishTime.HasValue && StartTime.HasValue ? (FinishTime.Value - StartTime.Value).ToString() : string.Empty;

            public PipelineRunStatusCode Status { get; set; } = PipelineRunStatusCode.NotStarted;

            public bool RunTimedOut { get; set; }

            public TimeSpan Timeout { get; set; } = default;

            public Exception Exception { get; set; }
        }
    }
}