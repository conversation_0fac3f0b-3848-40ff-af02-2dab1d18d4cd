<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <OutputPath>$(BaseTargetDir)\app\aether\BBAetherLibrary\BBAetherLibrary.HelloCloud.IntegrationTests</OutputPath>
    <IsPackable>false</IsPackable>
    <ImportSharedFiles>true</ImportSharedFiles>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.NET.Test.Sdk" />
    <PackageReference Include="NUnit" />
    <PackageReference Include="NUnit3TestAdapter" />
    <PackageReference Include="coverlet.collector" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\BBAetherLibrary.IntegrationTests.Common\BBAetherLibrary.IntegrationTests.Common.csproj" />
  </ItemGroup>

</Project>