﻿using BBAetherLibrary.IntegrationTests.Common;
using Microsoft.Aether.AEVA.DataContracts;
using Microsoft.Aether.BlueBox.Library;
using Microsoft.MachineLearning.Common.Core;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BBAetherLibrary.HelloCloud.IntegrationTests.TestFixtures
{
    [TestFixture]
    [Parallelizable(ParallelScope.All)]
    public class HelloCloudTests : BBAetherTestFixtureBase
    {

        private const string SourceInputName = "Input";
        private const string OutputName = "Output";
        List<string> inputDataTypes = new List<string> { "AzureBlobReference" };
        string outputDataTypeId = "AzureBlobReference";
        private const int MinutesToWaitForPipelineToComplete = 7;

        private const string ComputeTargetName = "cpucluster";
        private const string ComputeTargetType = "AmlCompute";
        private const string OriginalDockerImage = "mcr.microsoft.com/azureml/openmpi4.1.0-ubuntu20.04";

        [Test]
        public async Task RunHelloCloudSampleTest()
        {
            //https://ml.azure.com/dataset/HelloCloudInputDataset/latest/details?wsid=/subscriptions/b8c23406-f9b5-4ccb-8a65-a8cb5dcd6a5a/resourcegroups/rge2etests/workspaces/wse2etests&tid=72f988bf-86f1-41af-91ab-2d7cd011db47
            string fileDatasetId = "33704e5b-f7e3-4d8f-bca4-f96b11c5edbc";
            string version = "1";
            IGraph graph = await CreateHelloCloudGraphWithDataSetNode(_testEnvironment, fileDatasetId, version);
            IPipelineRun pipelineRun = await RunTestWithLogsAsync(
                graph,
                _testEnvironment,
                runHistoryExperimentName: "BBAetherLibrary_HelloCloud",
                timeoutMinutes: MinutesToWaitForPipelineToComplete);
            Assert.AreEqual(PipelineRunStatusCode.Finished, pipelineRun.CachedStatus, $"PipelineRun id: {pipelineRun.Id}");
        }

        /// <summary>
        /// Support "partition" macro in CloudletFramework output relative path.
        /// outputPathPattern: "azureml/{partition}/{output-name}/{run-id}"
        /// result: "azureml/c/output/c96260a0-544f-45aa-b43b-3134a176041b"
        /// </summary>
        /// <returns></returns>
        [Test]
        public async Task RunHelloCloudOutputMacroSampleTest()
        {
            //https://ml.azure.com/dataset/HelloCloudInputDataset/latest/details?wsid=/subscriptions/b8c23406-f9b5-4ccb-8a65-a8cb5dcd6a5a/resourcegroups/rge2etests/workspaces/wse2etests&tid=72f988bf-86f1-41af-91ab-2d7cd011db47
            string fileDatasetId = "4210c7c1-b441-4748-9f77-47040c385cff";
            string version = "1";
            IGraph graph = await CreateHelloCloudGraphWithDataSetNode(_testEnvironment, fileDatasetId, version,
                outputPath: "azureml/{partition}/{output-name}/{run-id}");
            IPipelineRun pipelineRun = await RunTestWithLogsAsync(
                graph,
                _testEnvironment,
                runHistoryExperimentName: "BBAetherLibrary_HelloCloud",
                timeoutMinutes: MinutesToWaitForPipelineToComplete);
            Assert.AreEqual(PipelineRunStatusCode.Finished, pipelineRun.CachedStatus, $"PipelineRun id: {pipelineRun.Id}");
        }

        [Test]
        public async Task RunHelloCloudSampleTest_WithESCloudNode()
        {
            //https://ml.azure.com/dataset/HelloCloudInputDataset/latest/details?wsid=/subscriptions/b8c23406-f9b5-4ccb-8a65-a8cb5dcd6a5a/resourcegroups/rge2etests/workspaces/wse2etests&tid=72f988bf-86f1-41af-91ab-2d7cd011db47
            string fileDatasetId = "c96260a0-544f-45aa-b43b-3134a176041b";
            string version = "1";
            IGraph graph = await CreateHelloCloudGraphWithESCloudNode(_testEnvironment, fileDatasetId, version);
            IPipelineRun pipelineRun = await RunTestWithLogsAsync(
                graph,
                _testEnvironment,
                runHistoryExperimentName: "BBAetherLibrary_HelloCloud",
                timeoutMinutes: MinutesToWaitForPipelineToComplete);
            Assert.AreEqual(PipelineRunStatusCode.Finished, pipelineRun.CachedStatus, $"PipelineRun id: {pipelineRun.Id}");
        }

        [Test]
        public async Task RunHelloCloudAssetTest_SubmitAndCancel()
        {
            IGraph graph = await CreateHelloCloudGraphWithAssetNode(_testEnvironment);
            string pipelineRunId = await RunTestWithLogsAndNotWaitForCompletionAsync(
                graph,
                _testEnvironment,
                runHistoryExperimentName: "BBAetherLibrary_HelloCloud");
            // Wait for the HelloCloud node to execute for a while
            await Task.Delay(TimeSpan.FromSeconds(5));

            // Cancel the pipeline
            await _testEnvironment.CancelPipelineRunPostAsync(pipelineRunId).ConfigureAwait(false);
            // Wait for the pipeline to be canceled
            await Task.Delay(TimeSpan.FromSeconds(5));

            var pipelineRun = await _testEnvironment.GetPipelineRunAsync(pipelineRunId).ConfigureAwait(false);
            Assert.AreEqual(PipelineRunStatusCode.Canceled, pipelineRun.CachedStatus, $"PipelineRun id: {pipelineRunId}");
        }

        private async Task<IGraph> CreateHelloCloudGraphWithDataSetNode(
        AetherEnvironment environment,
        string datasetId,
        string version,
        InputSetting? inputSetting = null,
        string outputPath = "azureml/{output-name}/{run-id}")
        {
            var graph = environment.CreateNewGraph();
            var datasetDefinition = new DataSetDefinition()
            {
                DataTypeShortName = "AzureBlobReference",
                Value = new DataSetDefinitionValue()
                {
                    DataSetReference = new RegisteredDataSetReference()
                    {
                        Id = datasetId,
                        Version = version,
                    }
                }
            };

            IAetherDataSource datasource = await _testEnvironment.GetDataSourceAsync(datasetDefinition);
            var datasourceNode = graph.AddNode(datasource);

            var step1Node = await AddHelloCloudModuleNode(
                environment,
                graph,
                moduleName: "Step 1",
                moduleDisplayName: "Prepare Data, Deterministic",
                isDeterministic: true,
                outputPath: outputPath);

            var subGraphNode = await AddSubgraphNode(
                environment,
                graph);

            var step2Node = await AddHelloCloudModuleNode(
                environment,
                graph,
                moduleName: "Step 2",
                moduleDisplayName: "Process Data, Non-deterministic",
                isDeterministic: false,
                outputPath: outputPath);

            var step3Node = await AddHelloCloudModuleNode(
                environment,
                graph,
                moduleName: "Step 3",
                moduleDisplayName: "Finalize, Deterministic",
                isDeterministic: true,
                outputPath: outputPath);

            graph.Connect(datasourceNode, step1Node.InputPortDictionary[SourceInputName]);
            graph.Connect(step1Node.OutputPortDictionary[OutputName], subGraphNode.InputPortDictionary[SourceInputName]);
            graph.Connect(subGraphNode.OutputPortDictionary[OutputName], step2Node.InputPortDictionary[SourceInputName]);
            graph.Connect(step2Node.OutputPortDictionary[OutputName], step3Node.InputPortDictionary[SourceInputName]);

            return graph;
        }

        private async Task<IGraph> CreateHelloCloudGraphWithESCloudNode(
            AetherEnvironment environment,
            string datasetId,
            string version)
        {
            var graph = environment.CreateNewGraph();

            var datasetDefinition = new DataSetDefinition()
            {
                DataTypeShortName = "AzureBlobReference",
                Value = new DataSetDefinitionValue()
                {
                    DataSetReference = new RegisteredDataSetReference()
                    {
                        Id = datasetId,
                        Version = version,
                    }
                }
            };

            IAetherDataSource datasource = await _testEnvironment.GetDataSourceAsync(datasetDefinition);
            var datasourceNode = graph.AddNode(datasource);

            var helloCloudNode = await AddHelloCloudModuleNode(
                environment,
                graph,
                moduleName: "HelloCloudStep1",
                moduleDisplayName: "Prepare Data, Deterministic",
                isDeterministic: true,
                useExisitingFilePath: true);

            var esCloudNode = await AddESCloudModuleNode(
                environment,
                graph,
                moduleName: "ESCloudStep2",
                moduleDisplayName: "Read input and write sample output, Deterministic",
                isDeterministic: true);

            var helloCloudNode2 = await AddHelloCloudModuleNode(
                environment,
                graph,
                moduleName: "HelloCloudStep3",
                moduleDisplayName: "Process Data, Non-deterministic",
                isDeterministic: false);

            var helloCloudNode3 = await AddHelloCloudModuleNode(
                environment,
                graph,
                moduleName: "HelloCloudStep4",
                moduleDisplayName: "Finalize, Deterministic",
                isDeterministic: true);

            graph.Connect(datasourceNode, helloCloudNode.InputPortDictionary[SourceInputName]);
            graph.Connect(helloCloudNode.OutputPortDictionary[OutputName], esCloudNode.InputPortDictionary[SourceInputName]);
            graph.Connect(esCloudNode.OutputPortDictionary[OutputName], helloCloudNode2.InputPortDictionary[SourceInputName]);
            graph.Connect(helloCloudNode2.OutputPortDictionary[OutputName], helloCloudNode3.InputPortDictionary[SourceInputName]);

            return graph;
        }

        private async Task<IGraph> CreateHelloCloudGraphWithAssetNode(AetherEnvironment environment)
        {
            var graph = environment.CreateNewGraph();
            var assetDefinition = new DataSetDefinition()
            {
                DataTypeShortName = "AzureBlobReference",
                Value = new DataSetDefinitionValue()
                {
                    AssetDefinition = new AssetDefinition()
                    {
                        AssetId = AssetId.Parse("azureml://locations/centraluseuap/workspaces/3c80c156-6e4e-486d-90b3-3e3753ef4a7f/data/hellocloud_file_input/versions/1"),
                        Type = AssetType.UriFile,
                    }
                }
            };

            IAetherDataSource datasource = await _testEnvironment.GetDataSourceAsync(assetDefinition);
            var datasourceNode = graph.AddNode(datasource);

            var outputSetting = new OutputSetting()
            {
                Name = OutputName,
                AssetOutputSettings = new AssetOutputSettings()
                {
                    Path = "azureml://datastores/workspaceblobstore/paths/${{name}}/${{output_name}}/",
                    Type = AssetType.UriFolder
                }
            };


            var extraParameters = new List<StructuredInterfaceParameter>
            {
                new StructuredInterfaceParameter { Name = "MinExecutionTime", DefaultValue="30", ParameterType = ParameterType.String },
                new StructuredInterfaceParameter { Name = "ExpectedResult", DefaultValue="Succeeded", ParameterType = ParameterType.String },
            };

            var step1Node = await AddHelloCloudModuleNode(
                environment,
                graph,
                moduleDisplayName: "HelloCloud Node",
                outputSetting: outputSetting,
                extraParameters: extraParameters);

            graph.Connect(datasourceNode, step1Node.InputPortDictionary[SourceInputName]);

            return graph;
        }

        private async Task<IGraph> CreateSubGraph(
            AetherEnvironment environment)
        {
            var graph = environment.CreateNewGraph();

            var step1Node = await AddHelloCloudModuleNode(
                environment,
                graph,
                moduleName: "SubGraph Step 1",
                moduleDisplayName: "Prepare Data, Deterministic",
                isDeterministic: true);

            var step2Node = await AddHelloCloudModuleNode(
                environment,
                graph,
                moduleName: "SubGraph Step 2",
                moduleDisplayName: "Process Data, Non-deterministic",
                isDeterministic: false);

            var graphInput = graph.AddGraphInput(SourceInputName);
            var graphOutput = graph.AddGraphOutput(OutputName);

            graphInput.Connect(step1Node.InputPortDictionary[SourceInputName]);
            graph.Connect(step1Node.OutputPortDictionary[OutputName], step2Node.InputPortDictionary[SourceInputName]);
            graphOutput.Connect(step2Node.OutputPortDictionary[OutputName]);

            return graph;
        }

        private async Task<ISubGraphNode> AddSubgraphNode(
            AetherEnvironment environment,
            IGraph graph)
        {
            var graphBody = await CreateSubGraph(environment);
            var subGraphId = await environment.CreateSubGraphAsync(graphBody, "MySubGraph");
            var subGraph = await environment.GetSubGraphAsync(subGraphId);
            return graph.AddNode(subGraph);
        }

        private async Task<IModuleNode> AddHelloCloudModuleNode(
            AetherEnvironment environment,
            IGraph graph,
            string moduleName = "HelloCloud dataset test module",
            string? moduleDisplayName = null,
            bool isDeterministic = false,
            InputSetting? inputSetting = null,
            OutputSetting? outputSetting = null,
            bool useExisitingFilePath = false,
            List<StructuredInterfaceParameter>? extraParameters = null,
            string outputPath = "azureml/{output-name}/{run-id}")
        {
            var moduleInputSettings = inputSetting == null ? null : new[] { inputSetting };

            var pathOnDatastore = useExisitingFilePath ? "azureml/HelloCloudTest/SampleOutput.txt" : outputPath;
            outputSetting ??= new OutputSetting()
            {
                Name = OutputName,
                DataStoreName = "workspaceblobstore",
                DataStoreMode = DataStoreMode.Mount,
                DataReferenceName = OutputName,
                DatasetRegistration = new DatasetRegistration
                {
                    Name = OutputName,
                    CreateNewVersion = true,
                },
                DatasetOutputOptions = new DatasetOutputOptions
                {
                    PathOnDatastore = pathOnDatastore,
                },
            };
            var moduleOutputSettings = new[] { outputSetting };

            var moduleId = await UploadHelloCloudNodeWithDataSetInput(environment, moduleName, moduleDisplayName, isDeterministic, extraParameters);
            IAetherModule module = await environment.GetModuleAsync(moduleId);
            var node = graph.AddNode(
                module,
                moduleInputSettings: moduleInputSettings,
                moduleOutputSettings: moduleOutputSettings);

            IReadOnlyDictionary<string, IAssignableParameter> metadataParams = node.MetadataParameters;
            metadataParams["ComputeName"].Value = "DummyCompute";

            return node;
        }

        private async Task<string> UploadHelloCloudNodeWithDataSetInput(
            AetherEnvironment environment,
            string moduleName = "HelloCloud dataset test module",
            string? moduleDisplayName = null,
            bool isDeterministic = false,
            List<StructuredInterfaceParameter>? extraParameters = null)
        {
            var structuredInterface = new StructuredInterface
            {
                Inputs = new List<StructuredInterfaceInput>
                {
                    new StructuredInterfaceInput { Name = SourceInputName, DataTypeIdsList = inputDataTypes },
                },
                Outputs = new List<StructuredInterfaceOutput>
                {
                    new StructuredInterfaceOutput { Name = OutputName, DataTypeId = outputDataTypeId},
                },
                Parameters = new List<StructuredInterfaceParameter>
                {
                    new StructuredInterfaceParameter { Name = "Command", DefaultValue="SayHello", ParameterType = ParameterType.String },
                },
                MetadataParameters = new List<StructuredInterfaceParameter>
                {
                    new StructuredInterfaceParameter { Name = "Version", ParameterType = ParameterType.Int, DefaultValue = "2" },
                    new StructuredInterfaceParameter { Name = "ComputeName", ParameterType = ParameterType.String },
                },
                Arguments = new List<ArgumentAssignment>(),
            };

            if (extraParameters != null && extraParameters.Any())
            {
                structuredInterface.Parameters.AddRange(extraParameters);
            }

            var moduleUploadInfo = new ModuleUploadInfo(
               name: moduleName,
               displayName: moduleDisplayName,
               description: "hellocloud dataset test module",
               isDeterministic: isDeterministic,
               cloudSystem: "HelloCloud",
               structuredInterface: structuredInterface,
               moduleTypeVersion: "1.0");

            return await environment.UploadModuleAsync(null, moduleUploadInfo, forceCreateNew: true);
        }

        private async Task<IModuleNode> AddESCloudModuleNode(
            AetherEnvironment environment,
            IGraph graph,
            string moduleName = "escloud dataset test module",
            string moduleDisplayName = null,
            bool isDeterministic = false)
        {
            string sourcePath = "TestResources/EscloudFile/helloworld";
            Script datasetInputOutputScript = new Script("dataset_input_output.py");

            var outputSettings = new[] { new OutputSetting
            {
                DataReferenceName = OutputName,
                DatasetRegistration = new DatasetRegistration(),
                DataStoreMode = DataStoreMode.Mount,
                DataStoreName = "workspaceblobstore",
                Name = OutputName,
                Overwrite = true,
                DatasetOutputOptions = new DatasetOutputOptions
                {
                    PathOnDatastore = "azureml/HelloCloudTest/ESCloudOutput/",
                },
            } };

            string moduleId = await UploadESCloudNodeWithDataSetInput(
                environment: environment,
                sourcePath: sourcePath,
                computeTargetName: ComputeTargetName,
                computeTargetType: ComputeTargetType,
                script: datasetInputOutputScript,
                dockerEnabled: false,
                datastoreMode: DataStoreMode.Mount,
                moduleDisplayName: moduleDisplayName,
                moduleName: moduleName,
                isDeterministic: isDeterministic);

            IAetherModule module = await environment.GetModuleAsync(moduleId);
            IModuleNode moduleNode = graph.AddNode(module, moduleOutputSettings: outputSettings);
            return moduleNode;
        }

        private async Task<string> UploadESCloudNodeWithDataSetInput(
             AetherEnvironment environment,
             string sourcePath,
             string computeTargetName,
             string computeTargetType,
             Script script,
             bool dockerEnabled,
             DataStoreMode datastoreMode,
             string inputDataTypeId = "AzureBlobReference",
             string outputDataTypeId = "AzureBlobReference",
             string moduleDisplayName = "escloud dataset test module display",
             string moduleName = "escloud dataset test module",
             bool isDeterministic = false)
        {
            var structuredInterface = new StructuredInterface
            {
                Inputs = new List<StructuredInterfaceInput>
                {
                    new StructuredInterfaceInput { Name = SourceInputName, DataTypeIdsList = new List<string> { inputDataTypeId }, DataStoreMode = datastoreMode, DataReferenceName = SourceInputName },
                },
                Outputs = new List<StructuredInterfaceOutput>
                {
                    new StructuredInterfaceOutput { Name = OutputName, DataTypeId = outputDataTypeId }
                },
                MetadataParameters = new List<StructuredInterfaceParameter>
                {
                    new StructuredInterfaceParameter { Name = "TargetType" , DefaultValue = "MLC", ParameterType = ParameterType.String, IsOptional = false},
                },
                Parameters = new List<StructuredInterfaceParameter>
                {
                    new StructuredInterfaceParameter { Name = "Target" , DefaultValue = computeTargetName, ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "MLCComputeType" , DefaultValue = computeTargetType, ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "PrepareEnvironment" , DefaultValue = "true", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "GenerateJson" , DefaultValue = "true", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "Script" , DefaultValue = script.Name, ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "Arguments" , DefaultValue = "--in,$AZUREML_DATAREFERENCE_input,--out,$AZUREML_DATAREFERENCE_out", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "Framework" , DefaultValue = "Python", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "maxRunDurationSeconds" , DefaultValue = "1200", ParameterType = ParameterType.String, IsOptional = true},
                    new StructuredInterfaceParameter { Name = "InterpreterPath" , DefaultValue = "python", ParameterType = ParameterType.String, IsOptional = true},
                    new StructuredInterfaceParameter { Name = "UserManagedDependencies" , DefaultValue = "false", ParameterType = ParameterType.String, IsOptional = true},
                    new StructuredInterfaceParameter { Name = "CondaDependencies" , DefaultValue = GetCondaSetup(script.IncludePandas), ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "DockerEnabled" , DefaultValue = dockerEnabled.ToString().ToLowerInvariant(), ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "BaseDockerImage" , DefaultValue = OriginalDockerImage, ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "SharedVolumes" , DefaultValue = "true", ParameterType = ParameterType.String, IsOptional = false},
                    new StructuredInterfaceParameter { Name = "NodeCount" , DefaultValue = "1", ParameterType = ParameterType.String, IsOptional = true},
                    new StructuredInterfaceParameter { Name = "ContainerInstanceRegion" , DefaultValue = "None", ParameterType = ParameterType.String, IsOptional = true},
                    new StructuredInterfaceParameter { Name = "ContainerInstanceCpuCores" , DefaultValue = "1", ParameterType = ParameterType.String, IsOptional = true},
                    new StructuredInterfaceParameter { Name = "ContainerInstanceMemoryGb" , DefaultValue = "4", ParameterType = ParameterType.String, IsOptional = true},
                },
            };
            var moduleUploadInfo = new ModuleUploadInfo(
               name: moduleName,
               displayName: moduleDisplayName,
               description: "escloud dataset test module",
               isDeterministic: isDeterministic,
               cloudSystem: "escloud",
               structuredInterface: structuredInterface,
               moduleTypeVersion: "1.0");

            return await environment.UploadModuleAsync(sourcePath, moduleUploadInfo);
        }

        /// <summary>
        /// Brings the latest AzureML SDK version from master
        /// Pip version is set to 21.1.1 because the latest version (21.2.2 at this time) takes a long time to resolve dependencies
        /// </summary>
        private static string GetCondaSetup(bool includePandas = false)
        {
            return $@"{{
                'name': 'project_environment',
                'dependencies': [
                    'python = 3.8',
                    'pip = 21.1.1',
                    {{
                        'pip': [
                            '--index-url https://azuremlsdktestpypi.azureedge.net/sdk-release/master/588E708E0DF342C4A80BD954289657CF',
                            '--extra-index-url https://pypi.python.org/simple',
                            'azureml-sdk<0.1.1',
                            'azureml-telemetry',
                            {(includePandas ? "'azureml-dataprep[pandas]'," : string.Empty)}
                            '--pre'
                        ]
                    }}
                ]
            }}";
        }

        private class Script
        {
            public string Name { get; private set; }

            public bool IncludePandas { get; private set; }

            public Script(string name, bool includePandas = false)
            {
                Name = name;

                IncludePandas = includePandas;
            }
        }
    }
}
