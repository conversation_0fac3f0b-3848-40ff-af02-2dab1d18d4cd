import argparse
import os
import sys

print(sys.argv)

parser = argparse.ArgumentParser()
parser.add_argument('--in', dest="input")
parser.add_argument('--out', dest="output")
args = parser.parse_args()

inputPath = args.input
outputPath = args.output + "/output"

os.makedirs(outputPath, exist_ok=True)

print('--input content')
if not os.path.exists(inputPath):
    raise 'Path does not exist'

if os.path.isfile(inputPath):
    print('%s is a file' % inputPath)
else:
    print('%s is a directory' % inputPath)
    print(os.listdir(inputPath))

cmd = "cp -r {} {}".format(inputPath, outputPath)
print('Run: ', cmd)
os.system(cmd)

print('--output content')
print(os.listdir(outputPath))
