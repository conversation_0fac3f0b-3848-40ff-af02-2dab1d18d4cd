import os

print('environment variable tests:')
print(os.environ)

assert os.environ.get('output') != None, 'output path with key "output" does not exist in environment variable, please check module output name'
assert os.environ.get('AZUREML_DATAREFERENCE_input') != None, 'input path with key "AZUREML_DATAREFERENCE_input" does not exist in environment variable, please check module input name'
assert os.environ.get('AZURE_ML_INPUT_input') != None, 'input path with key "AZURE_ML_INPUT_input" does not exist in environment variable, please check module input name'
assert os.environ.get('AZUREML_DATAREFERENCE_input') == os.environ.get('AZURE_ML_INPUT_input'), 'The values of "AZUREML_DATAREFERENCE_input" and "AZURE_ML_INPUT_input" are not the same.'

target_path = os.environ.get('input')
print('target_path: ', target_path)

actual_file_list = []
expected_file_list = ["testFile_1.txt", "testFile_2.txt", "testFile_3.txt"]

print('list input files:')
for r, s, files in os.walk(target_path):
    for f in files:
        actual_file_list.append(f)
        print(f)
 
assert actual_file_list == expected_file_list, 'The result of data transfer is not as expected.'

output_path = os.environ.get('AZUREML_DATAREFERENCE_output') or os.environ.get('output')
is_output_file = os.environ.get('IS_OUTPUT_FILE')

if is_output_file == "true":
    output_file_path = output_path
else:
    if not os.path.exists(output_path):
        print('directory doesnt exist, creating one')
        os.makedirs(output_path)
    output_file_path = os.path.join(output_path, 'test.txt')

with open(output_file_path, 'w') as f:
    f.write('hello datatransferv2 world!')
