#include <iostream>
#include <fstream>
using namespace std;

int main(int argc, char* argv[])
{
    if (argc > 1)
    {
        char* fileName = argv[1];
        std::ifstream file(fileName);

        if (!file.is_open())
        {
            std::cout << "File not found. File name : " << fileName <<"\n";
            return -1;
        }
        
        std::cout << "File found. File name : " << fileName << "\n";
        file.close();
    }
    else
    {
        std::cout << "Usage : CoolTest.exe <fileName>\n";
    }

    return 0;
}
