
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License.
import sys
import os

print("*********************************************************")
print("Hello Azure ML!")

mounted_input_path = sys.argv[1]
mounted_output_path = sys.argv[2]
upload_output_path = sys.argv[3]

print("Argument 1: %s" % mounted_input_path)
print("Argument 2: %s" % mounted_output_path)
print("Argument 3: %s" % upload_output_path)

with open(mounted_input_path, 'r') as f:
    content = f.read()
    # with open(os.path.join(mounted_output_path, 'output.csv'), 'w') as fw:
    with open(mounted_output_path, 'w') as fw:
        fw.write(content)
    
    with open(upload_output_path, 'w') as fw:
        fw.write(content)
