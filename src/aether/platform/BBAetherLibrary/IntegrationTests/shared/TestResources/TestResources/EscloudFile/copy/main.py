import argparse
import os
import sys

print(sys.argv)

parser = argparse.ArgumentParser()
parser.add_argument('--in', dest="input")
parser.add_argument('--out', dest="output")
args = parser.parse_args()

inputPath = args.input
outputPath = args.output + "/output"

os.makedirs(outputPath, exist_ok=True)

print('--input content')
print(os.listdir(inputPath))

cmd = "cp -r {} {}/output".format(inputPath, outputPath)
print('Run: ', cmd)
os.system(cmd)

print('--output content')
print(os.listdir(outputPath))
