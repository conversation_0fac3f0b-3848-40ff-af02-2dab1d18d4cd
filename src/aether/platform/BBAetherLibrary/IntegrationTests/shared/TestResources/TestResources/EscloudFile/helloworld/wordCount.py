# ---------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# ---------------------------------------------------------

import sys
import os
import fnmatch
import argparse

print(os.environ)
parser = argparse.ArgumentParser()


parser.add_argument('--input', type=str, help="input file path")
parser.add_argument('--output', type=str, help="output file path")
args = parser.parse_args()


print("Hello, worldCount!")
print("Driver arguments = " + repr(sys.argv))
print("Python version = " + sys.version)


wordcount={}
for file in os.listdir(args.input):
	if (fnmatch.fnmatch(file, '*.txt')):
		with open(args.input + "/" +  file,"r+") as f:
			for word in f.read().split():
				if word not in wordcount:
					wordcount[word] = 1
				else:
					wordcount[word] += 1
		f.close()
	for key in wordcount.keys():
		print ("%s %s " %(key , wordcount[key]))

filename = args.output + "/output.txt"
if not os.path.exists(os.path.dirname(filename)):
	os.makedirs(os.path.dirname(filename))

fo = open(filename, 'w+')
for key in wordcount.keys():
	fo.write ("%s %s \n" %(key , wordcount[key]))
fo.close()