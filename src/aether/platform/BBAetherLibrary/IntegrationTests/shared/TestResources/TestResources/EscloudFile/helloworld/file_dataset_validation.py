from azureml.core.run import Run
import argparse
import os

print('enter file_dataset_test.py')

print('environment variable tests:')
print('input:', os.environ.get("input"))
print('AZUREML_DATASET_ENVIRONMENT_VARS:', os.environ.get("AZUREML_DATASET_ENVIRONMENT_VARS"))
print('AZUREML_CONTEXT_MANAGER_DATASET:', os.environ.get("AZUREML_CONTEXT_MANAGER_DATASET"))

print('list download files:')
target_path = os.path.abspath(os.environ.get("input"))
print('target_path: ', target_path)
for r, s, files in os.walk(target_path):
	for f in files:
		print(os.path.join(r, f))

print('run context test start.')
run = Run.get_context()
file_dataset_path = run.input_datasets['input']

assert file_dataset_path == target_path

print("file_dataset_path: ", file_dataset_path)
print('run context test end.')
print('exit file_dataset_test.py')