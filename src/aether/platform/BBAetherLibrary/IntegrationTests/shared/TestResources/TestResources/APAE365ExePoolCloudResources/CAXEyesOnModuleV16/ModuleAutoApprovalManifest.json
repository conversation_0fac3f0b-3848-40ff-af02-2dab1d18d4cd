{"AutoApprovalEnabled": true, "ModuleFolderName": "cax.eyesonmodule", "AutoApprovalConditions": {"ExecutionEnvironments": ["ExeWorkerMachine"], "EyesOnOnlyMachines": false, "InterfaceString": "cax.eyesonmodule.exe input={in:AzureEncryptedBlobReference:DataToLookAt} inputGoldHitRta=[{in:AzureEncryptedBlobReference:GoldHitRTAData}] localoutputfolderEnc=[(localoutputfolderEnc - Optional)] localoutputfolderDec=[(localoutputfolderDec - Optional)] timeoutSeconds=(TimeoutSeconds:int,3600,2000000000:default,50000) hitappid=(hitappid:int,,:default,12345) projectname=(projectname:default,annotationproject1) judges=(judges:default,tarom;bradwall) outputfolderEnc={out:AzureEncryptedBlobReference:outputfolderEnc} outputfolderDec={out:AzureEncryptedBlobReference:outputfolderDec} annotationsMayIncludeCustomerContent=(annotationsMayIncludeCustomerContent:default,yes) taskGroupId=(TaskGroupId:int,,:default,12345) goldHitRTADataType=[(GoldHitRTADataType:enum,Gold,RTA,Qualification,Preview)] outputfolderForOriginalHitData={out:AzureEncryptedBlobReference:OriginalHitData} taskFileTimestamp=(taskFileTimestamp)", "ApprovedClusters": ["*"], "ApprovedRegions": ["*"], "ApprovedStorageAccounts": ["*"], "CloudSystem": "AE365ExePoolCloud"}}