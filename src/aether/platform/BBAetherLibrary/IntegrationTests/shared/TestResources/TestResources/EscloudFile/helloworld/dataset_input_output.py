import os
from azureml.core import Run, Datastore, Dataset

print('environment variable tests:')
print(os.environ)

print('list input files:')
target_path = os.environ.get('input')
print('target_path: ', target_path)
for r, s, files in os.walk(target_path):
    for f in files:
        print(os.path.join(r, f))


output_dir = os.environ.get('AZUREML_DATAREFERENCE_output') or os.environ.get('output')
if not os.path.exists(output_dir):
    print('directory doesnt exist, creating one')
    os.makedirs(output_dir)
output = os.path.join(output_dir, 'test.txt')
with open(output, 'w') as f:
    f.write('hello world!')

workspace = Run.get_context().experiment.workspace
datastore = Datastore(workspace, 'workspaceblobstore')
Dataset.File.from_files((datastore, 'does_not_exist'), validate=False).register(workspace, 'does_not_exist', create_new_version=True)

