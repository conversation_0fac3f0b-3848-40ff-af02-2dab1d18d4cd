$schema: https://componentsdk.azureedge.net/jsonschema/SweepComponent.json
name: microsoft_com_azureml_samples_sweep_minist_component
version: 0.0.1
display_name: Sweep Component for Hyperparameter tuning
type: SweepComponent
description: A sweep component for training hyper parameters
is_deterministic: true
tags: {category: Component <PERSON><PERSON><PERSON>, contact: <EMAIL>}

outputs:
  metrics:
    type: metrics
    description: metrics data

trial: file:mnist.yaml

algorithm: random
search_space:
  batch_size:
    type: choice
    values: [25, 50, 100]
  first_layer_neurons:
    type: choice
    values: [10, 50, 200, 300, 500]
  second_layer_neurons:
    type: choice
    values: [10, 50, 200, 500]
  learning_rate:
    type: loguniform
    min_value: -6
    max_value: -1

objective:
  primary_metric:
    default: validation_acc
    enum: [validation_acc]
  goal: maximize

early_termination:
    policy_type: bandit
    slack_factor: 0.1
    evaluation_interval: 2
    delay_evaluation: 5
limits:
  max_total_trials: 4
  max_concurrent_trials: 4
