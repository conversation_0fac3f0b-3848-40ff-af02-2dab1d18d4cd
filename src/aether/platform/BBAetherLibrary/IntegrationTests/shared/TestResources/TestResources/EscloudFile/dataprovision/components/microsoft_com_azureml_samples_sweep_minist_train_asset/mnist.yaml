$schema: https://componentsdk.azureedge.net/jsonschema/CommandComponent.json
name: microsoft_com_azureml_samples_sweep_minist_train_asset
version: 0.0.1
display_name: Sweep with Asset
type: CommandComponent
description: A dummy train component
is_deterministic: true
tags: {category: Component <PERSON><PERSON><PERSON>, contact: <EMAIL>}
inputs:
  data_folder:
    type: uri_folder
    optional: false
  batch_size:
    type: integer
    description: Batch size for each epoch
    optional: false
  first_layer_neurons:
    type: integer
    description: Number of neurons of neural networks's first layer
    optional: false
  second_layer_neurons:
    type: integer
    description: Number of neurons of neural networks's second layer
    optional: false
  learning_rate:
    type: float
    description: Learning rate
    min: 0.001
    max: 0.1
    optional: false
  resume_from:
    type: path
    description: location of the model or checkpoint files from where to resume the training
    optional: true
outputs:
  saved_model:
    type: mlflow_model
    description: path of saved_model of trial run
environment:
  docker:
    image: mcr.microsoft.com/azureml/openmpi4.1.0-ubuntu20.04
  conda:
    conda_dependencies_file: conda.yaml
command: >-
  echo "Start training ..." &&
  python mnist.py --data_folder {inputs.data_folder} --batch_size {inputs.batch_size}
  --first_layer_neurons {inputs.first_layer_neurons} --second_layer_neurons {inputs.second_layer_neurons}
  --learning_rate {inputs.learning_rate} [--resume-from {inputs.resume_from}] --saved_model {outputs.saved_model}
