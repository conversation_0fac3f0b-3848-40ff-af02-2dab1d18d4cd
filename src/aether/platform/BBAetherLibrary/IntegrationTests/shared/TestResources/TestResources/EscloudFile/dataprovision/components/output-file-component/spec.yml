$schema: https://componentsdk.azureedge.net/jsonschema/CommandComponent.json

# name is the readable identifier, immutable
name: output-file-component
# display name is the name on UI, mutable, can be updated with version upgrade
display_name: output-file Sample Component
description: A sample to demostrate modules with customized source directory
version: 0.0.2

type: CommandComponent@1-legacy

# need to check 
is_deterministic: True

inputs: 
    mounted_input_path:
        type: AnyDirectory
        description: mounted_input_path

outputs: 
    mounted_output_path:
        type: AnyFile
        description: mounted_output_path
    upload_output_path:
        type: AnyFile
        description: upload_output_path        

command: python dummy_train.py {inputs.mounted_input_path} {outputs.mounted_output_path} {outputs.upload_output_path}