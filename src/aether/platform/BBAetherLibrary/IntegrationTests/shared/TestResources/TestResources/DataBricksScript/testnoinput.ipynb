{"cells": [{"cell_type": "code", "source": ["dbutils.widgets.get(\"myparam\")\ny = getArgument(\"myparam\")\nprint (\"Param -\\'myparam':\")\nprint (y)"], "metadata": {"application/vnd.databricks.v1+cell": {"showTitle": false, "cellMetadata": {}, "nuid": "8075caea-f2a3-4486-90cd-7b56617a731a", "inputWidgets": {}, "title": ""}}, "outputs": [], "execution_count": 0}], "metadata": {"application/vnd.databricks.v1+notebook": {"notebookName": "testnoinput", "dashboards": [], "notebookMetadata": {"pythonIndentUnit": 2}, "language": "python", "widgets": {}, "notebookOrigID": 2531670655646223}}, "nbformat": 4, "nbformat_minor": 0}