{"cells": [{"cell_type": "code", "source": ["dbutils.widgets.get(\"myparam\")\nf = getArgument(\"myparam\")\nprint (\"Param -\\'myparam':\")\nprint (f)\n\ndbutils.widgets.get(\"input\")\ny = getArgument(\"input\")\nprint (\"Param -\\'input':\")\nprint (y)\n\ndbutils.widgets.get(\"output\")\nx = getArgument(\"output\")\nprint (\"Param -\\'output':\")\nprint (x)\n\nn=y+\"/test\"\ndf = spark.read.csv(n)\n\ndisplay (df)\n\n\ndata = [('alon', 'test')]\ndf2 = spark.createDataFrame(data)\n\nz=x+\"/test\"\ndf2.write.csv(z)"], "metadata": {"application/vnd.databricks.v1+cell": {"showTitle": false, "cellMetadata": {}, "nuid": "f4f32e26-09ad-49f1-a083-e92e13dd3c2e", "inputWidgets": {}, "title": ""}}, "outputs": [], "execution_count": 0}, {"cell_type": "code", "source": ["  from time import sleep\n  import datetime\n\ndatetime.datetime.now().time()\n\nsleep(30)\n\ndatetime.datetime.now().time()"], "metadata": {"application/vnd.databricks.v1+cell": {"showTitle": false, "cellMetadata": {}, "nuid": "fe63440c-f067-4d4c-8cc3-3ded674ebe84", "inputWidgets": {}, "title": ""}}, "outputs": [], "execution_count": 0}, {"cell_type": "code", "source": ["import sys \nprint(\"Python version\")\nprint (sys.version)"], "metadata": {"application/vnd.databricks.v1+cell": {"showTitle": false, "cellMetadata": {}, "nuid": "05b0fe50-a58a-471d-8eac-0dde9f3859f5", "inputWidgets": {}, "title": ""}}, "outputs": [], "execution_count": 0}], "metadata": {"application/vnd.databricks.v1+notebook": {"notebookName": "testinputoutput", "dashboards": [], "notebookMetadata": {"pythonIndentUnit": 2}, "language": "python", "widgets": {}, "notebookOrigID": 3570565962519128}}, "nbformat": 4, "nbformat_minor": 0}