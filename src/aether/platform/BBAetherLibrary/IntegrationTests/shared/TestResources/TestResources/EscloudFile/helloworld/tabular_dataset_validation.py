from azureml.core.run import Run
import argparse
import os

print('enter tabular_dataset_test.py')
print('environment variable tests:')

print('input:', os.environ.get("input"))
print('AZUREML_DATASET_ENVIRONMENT_VARS:', os.environ.get("AZUREML_DATASET_ENVIRONMENT_VARS"))
print('AZUREML_CONTEXT_MANAGER_DATASET:', os.environ.get("AZUREML_CONTEXT_MANAGER_DATASET"))
print('run context test start.')
run = Run.get_context()
tabular_dataset = run.input_datasets['input']
print("tabular_dataset.id: ", tabular_dataset.id)
print("tabular first line:")
print(tabular_dataset.take(1).to_pandas_dataframe())
print('run context test end.')
print('exit tabular_dataset_test.py')