using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using NUnit.Framework;
using Microsoft.Aether.BlueBox.Library;
using Microsoft.Aether.AEVA.DataContracts;
using BBAetherLibrary.IntegrationTests.Common;

namespace BBAetherLibrary.HyperDrive.IntegrationTests.TestFixtures
{
    [TestFixture]
    [Parallelizable(ParallelScope.All)]
    public class HyperDriveTests : BBAetherTestFixtureBase
    {
        private const string EnvironmentConfig = "EnvironmentConfigs/pipeline_tests.xml";

        private const string RunConfigParameterName = "HyperDriveRunConfig";
        private const string RunConfig = @"{""user"":""<EMAIL>"",""name"":""hd_int_test"",""description"":null,""max_concurrent_jobs"":1,""max_total_jobs"":1,""max_duration_minutes"":10080,""platform"":""AML"",""policy_config"":{""name"":""Default""},""generator_config"":{""name"":""RANDOM"",""parameter_space"":{""--seed1"":[""choice"",[[24,49,74,99]]],""--seed2"":[""choice"",[[49,99]]]}},""primary_metric_config"":{""name"":""validation_acc"",""goal"":""maximize""},""platform_config"":{""ServiceAddress"":""https://master.experiments.azureml-test.net"",""ServiceArmScope"":""subscriptions/b8c23406-f9b5-4ccb-8a65-a8cb5dcd6a5a/resourceGroups/aesviennatesteuap/providers/Microsoft.MachineLearningServices/workspaces/aevahdrivetest/experiments/test"",""SubscriptionId"":""b8c23406-f9b5-4ccb-8a65-a8cb5dcd6a5a"",""ResourceGroupName"":""aesviennatesteuap"",""WorkspaceName"":""aevahdrivetest"",""ExperimentName"":""test"",""Definition"":{""Configuration"":null,""SnapshotId"":""ea613b95-6731-470b-b470-d571a0592ff6"",""TargetDetails"":null,""ParentRunId"":null,""RunType"":null,""Attribution"":null,""TelemetryValues"":null,""Overrides"":{""Script"":""tf_mnist.py"",""Arguments"":[""--data-folder"",""$AZUREML_DATAREFERENCE_mnist_data"",""--seed3"",""$AML_PARAMETER_seed_value_param""],""SourceDirectoryDataStore"":null,""Framework"":0,""Communicator"":0,""Target"":""gpucluster"",""DataReferences"":{""mnist_data"":{""DataStoreName"":""blobstore"",""Mode"":0,""PathOnDataStore"":""mnist"",""PathOnCompute"":null,""Overwrite"":false}},""JobName"":null,""AutoPrepareEnvironment"":true,""MaxRunDurationSeconds"":null,""NodeCount"":1,""Environment"":{""Python"":{""InterpreterPath"":""python"",""UserManagedDependencies"":false,""CondaDependencies"":{""name"":""project_environment"",""dependencies"":[""python=3.6.2"",{""pip"":[""azureml-defaults"",""tensorflow-gpu==1.10.0""]}]},""CondaDependenciesFile"":null},""EnvironmentVariables"":{""EXAMPLE_ENV_VAR"":""EXAMPLE_VALUE"",""NCCL_SOCKET_IFNAME"":""^docker0"",""AML_PARAMETER_seed_value_param"":""59""},""Docker"":{""BaseImage"":""mcr.microsoft.com/azureml/base-gpu:0.1.4"",""Enabled"":true,""SharedVolumes"":true,""Preparation"":null,""Arguments"":[],""BaseImageRegistry"":{""Address"":null,""Username"":null,""Password"":null}},""Spark"":{""Repositories"":[""https://mmlspark.azureedge.net/maven""],""Packages"":[{""Group"":""com.microsoft.ml.spark"",""Artifact"":""mmlspark_2.11"",""Version"":""0.12""}],""PrecachePackages"":true}},""History"":{""OutputCollection"":true},""SparkConfiguration"":null,""AmlCompute"":{""Name"":null,""VmSize"":null,""VmPriority"":null,""Location"":null,""RetainCluster"":false},""Tensorflow"":{""WorkerCount"":1,""ParameterServerCount"":1},""Mpi"":{""ProcessCountPerNode"":1},""Hdi"":{""YarnDeployMode"":2},""ContainerInstance"":{""Region"":null,""CpuCores"":1.0,""MemoryGb"":4.0},""ExposedPorts"":null}}}}";

        [Test]
        public async Task RunHyperDriveTest()
        {
            string aetherEndpoint = GetAetherEndpoint();

            AetherEnvironment environment = BBAetherEnvironmentUtils.CreateTestAetherEnvironment(EnvironmentConfig, aetherEndpoint, _tokenProvider.GetAccessTokenAsync);
            await BBAetherEnvironmentUtils.CreateDataTypesIfNeededAsync(environment);

            var dataSourceId = await DataStoreUtils.CreateDataStoreDataSourceAsync(
                environment,
                name: "mnist_data",
                description: "mnist_data",
                amlDataStoreName: "blobstore",
                pathOnDataStore: "mnist",
                identifierHash: Guid.NewGuid().ToString(),
                contentHash: null);

            IAetherDataSource dataSource = await environment.GetDataSourceAsync(dataSourceId);

            string moduleId = await UploadHyperDriveModuleAsync(environment);
            IAetherModule module = await environment.GetModuleAsync(moduleId);

            IGraph graph = environment.CreateNewGraph();

            IDataSourceNode dataSourceNode = graph.AddNode(dataSource);

            IModuleNode moduleNode = graph.AddNode(module);
            SetModuleParams(moduleNode);

            graph.Connect(dataSourceNode, moduleNode.InputPortDictionary["mnist_data"]);

            IPipelineRun experiment = await RunTestWithLogsAsync(graph, environment, timeoutMinutes: 20);
            Assert.AreEqual(PipelineRunStatusCode.Finished, experiment.CachedStatus, $"experiment id: {experiment.Id}");
        }

        private static async Task<string> UploadHyperDriveModuleAsync(AetherEnvironment environment, bool isDeterministic = false)
        {
            return await environment.UploadModuleAsync(
                sourcePath: null,
                uploadInfo: new ModuleUploadInfo(
                    name: "Test hyperdrive",
                    displayName: "Test hyperdrive display",
                    description: "hyperdrive",
                    isDeterministic: isDeterministic,
                    cloudSystem: "HyperDriveCloud",
                    moduleTypeVersion: null,
                    structuredInterface: new StructuredInterface
                    {
                        Inputs = new List<StructuredInterfaceInput>
                        {
                            new StructuredInterfaceInput { Name = "mnist_data", DataReferenceName = "mnist_data", DataStoreMode = DataStoreMode.Mount, DataTypeIdsList = new List<string> {"AnyFile", "AnyDirectory"}}
                        },
                        Parameters = new List<StructuredInterfaceParameter>
                        {
                            new StructuredInterfaceParameter { Name = "Arguments", ParameterType = ParameterType.String },
                        },
                        MetadataParameters = new List<StructuredInterfaceParameter>
                        {
                            new StructuredInterfaceParameter { Name = RunConfigParameterName, ParameterType = ParameterType.String },
                        }
                    }));
        }

        private static void SetModuleParams(IModuleNode module)
        {
            IReadOnlyDictionary<string, IAssignableParameter> parameters = module.Parameters;
            parameters["Arguments"].Value = "--data-folder,$AZUREML_DATAREFERENCE_mnist_data,--seed3,$AML_PARAMETER_seed_value_param";

            IReadOnlyDictionary<string, IAssignableParameter> metadataParameters = module.MetadataParameters;
            metadataParameters[RunConfigParameterName].Value = RunConfig;
        }
    }
}