<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <OutputPath>$(BaseTargetDir)\app\aether\BBAetherLibrary\BBAetherLibrary.HyperDrive.IntegrationTests</OutputPath>
  </PropertyGroup>
  <ItemGroup>
    <None Include="..\shared\.runsettings">
      <Link>.runsettings</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="..\shared\setup_certificates.ps1">
      <Link>setup_certificates.ps1</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="EnvironmentConfigs\pipeline_tests.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="NUnit" />
    <PackageReference Include="NUnit3TestAdapter" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" />
    <PackageReference Include="coverlet.collector" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="$(AetherSrcRoot)\platform\BBAetherLibrary\Framework\BBAetherLibrary.Framework.csproj" />
    <ProjectReference Include="..\BBAetherLibrary.IntegrationTests.Common\BBAetherLibrary.IntegrationTests.Common.csproj" />
  </ItemGroup>
</Project>
