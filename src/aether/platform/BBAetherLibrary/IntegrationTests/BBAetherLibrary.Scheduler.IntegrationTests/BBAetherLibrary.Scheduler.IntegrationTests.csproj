<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <OutputPath>$(BaseTargetDir)\app\aether\BBAetherLibrary\BBAetherLibrary.Scheduler.IntegrationTests</OutputPath>
    <GenerateBindingRedirectsOutputType>true</GenerateBindingRedirectsOutputType>
    <ImportSharedFiles>true</ImportSharedFiles>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="NUnit" />
    <PackageReference Include="NUnit3TestAdapter" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" />
    <PackageReference Include="coverlet.collector" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\BBAetherLibrary.IntegrationTests.Common\BBAetherLibrary.IntegrationTests.Common.csproj" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="EnvironmentConfigs\" />
  </ItemGroup>
</Project>
