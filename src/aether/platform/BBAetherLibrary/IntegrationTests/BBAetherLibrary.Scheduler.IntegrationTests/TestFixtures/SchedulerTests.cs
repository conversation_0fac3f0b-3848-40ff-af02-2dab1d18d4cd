﻿using System;
using System.Linq;
using System.Threading.Tasks;
using NUnit.Framework;
using Microsoft.Aether.BlueBox.Library;
using Microsoft.Aether.AEVA.DataContracts;
using BBAetherLibrary.IntegrationTests.Common;
using System.Collections.Generic;

namespace BBAetherLibrary.Scheduler.IntegrationTests
{
    [TestFixture]
    public class SchedulerTests : BBAetherTestFixtureBase
    {
        [Test]
        public async Task SchedulerTest()
        {
            const string scheduleName = "integrationTestSchedule";

            AetherEnvironment testEnvironment = BBAetherEnvironmentUtils.CreateTestAetherEnvironment(_envConfigFile, GetAetherEndpoint(), _tokenProvider.GetAccessTokenAsync);            
            await BBAetherEnvironmentUtils.CreateDataTypesIfNeededAsync(testEnvironment);

            //clean up old integration test schedules
            EntityWithContinuationToken<IList<PipelineScheduleEntity>> schedules = await testEnvironment.GetSchedulesAsync(activeOnly: true, continuationToken: null, batchSize: 100);
            
            while(schedules.Entity.Any())
            {
                foreach (var s in schedules.Entity.Where(x => x.Name.Equals(scheduleName) && x.LastModifiedDate < DateTime.UtcNow.AddHours(-2)))
                {
                    s.EntityStatus = EntityStatus.Deprecated;

                    try
                    {
                        await testEnvironment.UpdateScheduleAsync(s.Id, s);
                    }
                    catch
                    {
                    }
                }
                if(!string.IsNullOrEmpty(schedules.ContinuationToken))
                {
                    schedules = await testEnvironment.GetSchedulesAsync(activeOnly: true, continuationToken: schedules.ContinuationToken, batchSize: 100);
                }
                else
                {
                    break;
                }
            }

            string dataSourceId = await BBAetherEnvironmentUtils.UploadADLADataSourceAsync(testEnvironment);
            IAetherDataSource dataSource = await testEnvironment.GetDataSourceAsync(dataSourceId);

            string moduleId = await BBAetherEnvironmentUtils.UploadADLAModuleAsync(testEnvironment, "TestResources/AdlaScript");
            IAetherModule module = await testEnvironment.GetModuleAsync(moduleId);

            IGraph graph = testEnvironment.CreateNewGraph();

            IDataSourceNode inputNode = graph.AddNode(dataSource);

            IModuleNode moduleNode = graph.AddNode(module);
            BBAetherEnvironmentUtils.SetADLAModuleParams(module: moduleNode, scriptName: "test_adla_script.usql", configPath: _envConfigFile);

            graph.Connect(inputNode, moduleNode.InputPortDictionary["script_input"]);

            // Create Pipeline
            string pipelineId = await testEnvironment.CreatePipelineAsync(new PipelineCreationInfo
            {
                Name = "Scheduler Test Graph",
                Graph = graph.GetGraphEntity(),
            });

            //Create Pipeline Schedule
            PipelineScheduleEntity schedule = await testEnvironment.CreateScheduleAsync(
                new ScheduleCreationInfo(name: scheduleName,
                pipelineId: pipelineId,
                pipelineSubmissionInfo: new PipelineSubmissionInfo
                {
                    ExperimentName = "experimentName",
                    RunType = RunType.Schedule
                },
                recurrence: new Recurrence
                {
                    Frequency = Frequency.Minute,
                    Interval = 1
                }));

            schedule = await testEnvironment.GetScheduleAsync(schedule.Id);
            int msElapsed = 0;

            while(schedule.ProvisioningStatus == ProvisioningStatus.Provisioning && msElapsed < 100000)
            {
                await Task.Delay(2000);
                msElapsed += 2000;
                schedule = await testEnvironment.GetScheduleAsync(schedule.Id);
            }

            Assert.AreEqual(schedule.ProvisioningStatus, ProvisioningStatus.Completed, $"schedule id failed to provision create in 100s: {schedule.Id}");

            // ensure schedule actually submitted after at most a minute
            PipelineRunEntity pipelineRun = null;
            msElapsed = 0;

            while (msElapsed < 60000 && pipelineRun == null)
            {
                await Task.Delay(2000);
                msElapsed += 2000;
                try
                {
                    pipelineRun = await testEnvironment.GetLastPipelineRunByScheduleIdAsync(schedule.Id);
                }
                catch (Exception)
                {
                    // keep trying up to a minute
                }
            }

            schedule = await testEnvironment.GetScheduleAsync(schedule.Id);
            //disable schedule
            schedule.EntityStatus = EntityStatus.Deprecated;

            schedule = await testEnvironment.UpdateScheduleAsync(schedule.Id, schedule);

            msElapsed = 0;

            while (schedule.ProvisioningStatus == ProvisioningStatus.Provisioning && msElapsed < 30000)
            {
                await Task.Delay(2000);
                msElapsed += 2000;
                schedule = await testEnvironment.GetScheduleAsync(schedule.Id);
            }

            Assert.AreNotEqual(pipelineRun, null, $"schedule id failed to submit a run in 130s: {schedule.Id}");
            Assert.AreEqual(schedule.ProvisioningStatus, ProvisioningStatus.Completed, $"schedule id failed to provision update in 130s: {schedule.Id}");
        }

        [Test]
        public async Task SchedulerPipelineEndpointTest()
        {
            const string scheduleName = "integrationTestSchedulePipelineEndpoint";

            AetherEnvironment testEnvironment = BBAetherEnvironmentUtils.CreateTestAetherEnvironment(_envConfigFile, GetAetherEndpoint(), _tokenProvider.GetAccessTokenAsync);
            await BBAetherEnvironmentUtils.CreateDataTypesIfNeededAsync(testEnvironment);

            //clean up old integration test schedules
            EntityWithContinuationToken<IList<PipelineScheduleEntity>> schedules = await testEnvironment.GetSchedulesAsync(activeOnly: true, continuationToken: null, batchSize: 100);

            while (schedules.Entity.Any())
            {
                foreach (var s in schedules.Entity.Where(x => x.Name.Equals(scheduleName) && x.LastModifiedDate < DateTime.UtcNow.AddHours(-2)))
                {
                    s.EntityStatus = EntityStatus.Deprecated;

                    try
                    {
                        await testEnvironment.UpdateScheduleAsync(s.Id, s);
                    }
                    catch
                    {
                    }
                }
                if (!string.IsNullOrEmpty(schedules.ContinuationToken))
                {
                    schedules = await testEnvironment.GetSchedulesAsync(activeOnly: true, continuationToken: schedules.ContinuationToken, batchSize: 100);
                }
                else
                {
                    break;
                }
            }

            string dataSourceId = await BBAetherEnvironmentUtils.UploadADLADataSourceAsync(testEnvironment);
            IAetherDataSource dataSource = await testEnvironment.GetDataSourceAsync(dataSourceId);

            string moduleId = await BBAetherEnvironmentUtils.UploadADLAModuleAsync(testEnvironment, "TestResources/AdlaScript");
            IAetherModule module = await testEnvironment.GetModuleAsync(moduleId);

            IGraph graph = testEnvironment.CreateNewGraph();

            IDataSourceNode inputNode = graph.AddNode(dataSource);

            IModuleNode moduleNode = graph.AddNode(module);
            BBAetherEnvironmentUtils.SetADLAModuleParams(module: moduleNode, scriptName: "test_adla_script.usql", configPath: _envConfigFile);

            graph.Connect(inputNode, moduleNode.InputPortDictionary["script_input"]);

            // Create Pipeline
            string pipelineId = await testEnvironment.CreatePipelineAsync(new PipelineCreationInfo
            {
                Name = "Scheduler Pipeline endpoint Test Graph",
                Graph = graph.GetGraphEntity(),
            });

            // create Pipeline Enpoint 
            Guid Endpoint_uuid = Guid.NewGuid();
            PipelineEndpointCreationInfo pipelineEndpointCreationInfo = new PipelineEndpointCreationInfo("testScheduleEndpoint"+ Endpoint_uuid.ToString(), "Test for schedule uysing pipelineEndpoint", pipelineId);
            PipelineEndpointEntity pipelineEndpoint = await testEnvironment.CreatePipelineEndpointAsync(pipelineEndpointCreationInfo);
            var pid = pipelineEndpoint.PipelineVersionList.Find(version => version.Version == "0").PipelineId;
            Assert.AreEqual(pipelineId, pid, $"PipelineEndpoint with id: {pipelineEndpoint.Id}");

            //Create Pipeline Schedule
            PipelineScheduleEntity schedule = await testEnvironment.CreateScheduleAsync(
                new ScheduleCreationInfo(name: scheduleName,
                pipelineId: "",
                pipelineSubmissionInfo: new PipelineSubmissionInfo
                {
                    ExperimentName = "experimentNameforPipelineEndpoint",
                    RunType = RunType.Schedule
                },
                recurrence: new Recurrence
                {
                    Frequency = Frequency.Minute,
                    Interval = 1
                },
                pipelineendpointId: pipelineEndpoint.Id
                ));

            schedule = await testEnvironment.GetScheduleAsync(schedule.Id);
            int msElapsed = 0;

            while (schedule.ProvisioningStatus == ProvisioningStatus.Provisioning && msElapsed < 100000)
            {
                await Task.Delay(2000);
                msElapsed += 2000;
                schedule = await testEnvironment.GetScheduleAsync(schedule.Id);
            }

            Assert.AreEqual(schedule.ProvisioningStatus, ProvisioningStatus.Completed, $"schedule id failed to provision create in 100s: {schedule.Id}");

            // ensure schedule actually submitted after at most a minute
            PipelineRunEntity pipelineRun = null;
            msElapsed = 0;

            while (msElapsed < 60000 && pipelineRun == null)
            {
                await Task.Delay(2000);
                msElapsed += 2000;
                try
                {
                    pipelineRun = await testEnvironment.GetLastPipelineRunByScheduleIdAsync(schedule.Id);
                }
                catch (Exception)
                {
                    // keep trying up to a minute
                }
            }

            // Check if we can get schedule based on the Pipeline endpointID
            EntityWithContinuationToken<IList<PipelineScheduleEntity>> pipelineSchedules = await testEnvironment.GetSchedulesByPipelineEndpointIdAsync(pipelineEndpoint.Id, null, 2);
            List<PipelineScheduleEntity> listPipelineScheduleEnity = pipelineSchedules.Entity.ToList<PipelineScheduleEntity>();
            Assert.IsTrue(listPipelineScheduleEnity.Count == 1, $"No Schedule exists for pipleine Endpoint ID: {pipelineEndpoint.Id}");

            schedule = await testEnvironment.GetScheduleAsync(schedule.Id);
            //disable schedule
            schedule.EntityStatus = EntityStatus.Deprecated;

            schedule = await testEnvironment.UpdateScheduleAsync(schedule.Id, schedule);

            msElapsed = 0;

            while (schedule.ProvisioningStatus == ProvisioningStatus.Provisioning && msElapsed < 30000)
            {
                await Task.Delay(2000);
                msElapsed += 2000;
                schedule = await testEnvironment.GetScheduleAsync(schedule.Id);
            }

            Assert.AreNotEqual(pipelineRun, null, $"schedule id failed to submit a run in 130s: {schedule.Id}");
            Assert.AreEqual(schedule.ProvisioningStatus, ProvisioningStatus.Completed, $"schedule id failed to provision update in 130s: {schedule.Id}");

        }


        }
}
