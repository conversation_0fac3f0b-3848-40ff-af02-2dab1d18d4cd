﻿using NUnit.Framework;
using System.Diagnostics;

// Run up to 20 tests in parallel
[assembly: LevelOfParallelism(20)]

namespace BBAetherLibrary.Scheduler.IntegrationTests
{
    [SetUpFixture]
    public class NUnitSetup
    {
        [OneTimeSetUp]
        public void RunBeforeAnyTests()
        {
            // This makes all Trace.WriteLine() go into nunit progress tracking, and show up in VSTS online console log for diagnostic during automated runs
            Trace.Listeners.Add(new TextWriterTraceListener(NUnit.Framework.TestContext.Progress));
        }
    }
}
