﻿using BBAetherLibrary.IntegrationTests.Common;
using Microsoft.Aether.AEVA.DataContracts;
using Microsoft.Aether.BlueBox.Library;
using Microsoft.MachineLearning.Common.Core;
using NUnit.Framework;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace BBAetherLibrary.FineTuning.IntegrationTests.TestFixtures
{
    [TestFixture]
    [Parallelizable(ParallelScope.All)]
    public class FineTuningTests : BBAetherTestFixtureBase
    {
        private const string sourcePath = "TestResources/SparkCloudResources/helloworld";
        private const int MinutesToWaitForPipelineToComplete = 25;


        private const string moduleFileInputName = "test_file";
        private const string moduleTabularInputName = "test_tabular";
        private const string moduleUrlFileInputName = "url_file";
        private const string moduleOutputName = "output";

        private const string datastoreName = "workspaceblobstore";

        private const string fileDatasetId = "5efc886d-9a6a-4205-bf86-9c055d7751ee";
        private const string tabularDatasetId = "76ae2421-b7b4-4f8b-8109-86662580f6cd";
        private readonly Script datasetTestScript = new Script("file_dataset_validation.py");
        private readonly Script assetTestScript = new Script("asset_validation.py");
        private readonly Script environmentVariableTestScript = new Script("environment_variables_validation.py");
        private const string dataReferenceName = "the_only_input_data";

        [Test]
        public async Task RunFineTuningCloudFileDatasetTest()
        {
            IGraph graph = await CreateFineTuningCloudGraphWithDataSetNode(_testEnvironment, sourcePath, datasetTestScript, null, null, false);
            IPipelineRun pipelineRun = await RunTestWithLogsAsync(graph, _testEnvironment, MinutesToWaitForPipelineToComplete, runHistoryExperimentName: "IntegrationTestFineTuning");
            Assert.AreEqual(PipelineRunStatusCode.Finished, pipelineRun.CachedStatus, $"PipelineRun id: {pipelineRun.Id}");
        }

        [Test]
        public async Task RunFineTuningCloudFileDatasetTestWithMacroUseDefaultCase()
        {
            // The defaultDatastore will replace marco in OutputSetting.DatastoreName
            IGraph graph = await CreateFineTuningCloudGraphWithDataSetNode(_testEnvironment, sourcePath, datasetTestScript, datastoreName, "${{default_datastore}}", true);
            IPipelineRun pipelineRun = await RunTestWithLogsAsync(graph, _testEnvironment, MinutesToWaitForPipelineToComplete, runHistoryExperimentName: "IntegrationTestFineTuningDatastoreMacro");
            Assert.AreEqual(PipelineRunStatusCode.Finished, pipelineRun.CachedStatus, $"PipelineRun id: {pipelineRun.Id}");
        }

        [Test]
        public async Task RunFineTuningCloudFileDatasetTestWithMacro()
        {
            // The defaultDatastore will replace marco in OutputSetting.DatastoreName, the value of useGraphDefaultDatastore will not affect it.
            IGraph graph = await CreateFineTuningCloudGraphWithDataSetNode(_testEnvironment, sourcePath, datasetTestScript, datastoreName, "${{default_datastore}}", false);
            IPipelineRun pipelineRun = await RunTestWithLogsAsync(graph, _testEnvironment, MinutesToWaitForPipelineToComplete, runHistoryExperimentName: "IntegrationTestFineTuningDatastoreMacro");
            Assert.AreEqual(PipelineRunStatusCode.Finished, pipelineRun.CachedStatus, $"PipelineRun id: {pipelineRun.Id}");
        }

        [Test]
        public async Task RunFineTuningCloudWithAssetEnv()
        {
            IGraph graph = await CreateFineTuningCloudGraphWithAssetEnv(_testEnvironment, assetTestScript, DataStoreMode.Direct);
            IPipelineRun pipelineRun = await RunTestWithLogsAsync(graph, _testEnvironment, MinutesToWaitForPipelineToComplete, runHistoryExperimentName: "IntegrationTestFineTuning");
            Assert.AreEqual(PipelineRunStatusCode.Finished, pipelineRun.CachedStatus, $"PipelineRun id: {pipelineRun.Id}");

        }

        [Test]
        public async Task RunFineTuningCloudWithRegistryAssetEnv()
        {
            IGraph graph = await CreateFineTuningCloudGraphWithRegistryAssetEnv(_testEnvironment, assetTestScript, DataStoreMode.Direct);
            IPipelineRun pipelineRun = await RunTestWithLogsAsync(graph, _testEnvironment, MinutesToWaitForPipelineToComplete, runHistoryExperimentName: "IntegrationTestFineTuning");
            Assert.AreEqual(PipelineRunStatusCode.Finished, pipelineRun.CachedStatus, $"PipelineRun id: {pipelineRun.Id}");

        }

        [Test]
        public async Task RunFineTuningCloudWithInlineEnv()
        {
            IGraph graph = await CreateFineTuningCloudGraphWithInlineEnv(_testEnvironment, assetTestScript, DataStoreMode.Direct);
            IPipelineRun pipelineRun = await RunTestWithLogsAsync(graph, _testEnvironment, MinutesToWaitForPipelineToComplete, runHistoryExperimentName: "IntegrationTestFineTuning");
            Assert.AreEqual(PipelineRunStatusCode.Finished, pipelineRun.CachedStatus, $"PipelineRun id: {pipelineRun.Id}");

        }

        [Test]
        public async Task RunFineTuningCloudAssetTest()
        {
            IGraph graph = await CreateFineTuningCloudGraphWithAssetNode(_testEnvironment, assetTestScript, DataStoreMode.Direct);
            IPipelineRun pipelineRun = await RunTestWithLogsAsync(graph, _testEnvironment, MinutesToWaitForPipelineToComplete, runHistoryExperimentName: "IntegrationTestFineTuning");
            Assert.AreEqual(PipelineRunStatusCode.Finished, pipelineRun.CachedStatus, $"PipelineRun id: {pipelineRun.Id}");
        }

        [Test]
        public async Task RunFineTuningCloudAssetTestWithWithDefaultComputeCannotSet()
        {
            // When default compute given, if node.useGraphDefaultCompute is false, the default compute target will not be set
            IGraph graph = await CreateFineTuningCloudGraphWithAssetNode(_testEnvironment, assetTestScript, DataStoreMode.Direct, useGraphDefaultCompute: false, defaultComputeName: "spark32test");
            IPipelineRun pipelineRun = await RunTestWithLogsAsync(graph, _testEnvironment, MinutesToWaitForPipelineToComplete, runHistoryExperimentName: "IntegrationTestFineTuningWithDefaultComputeCannotSet");
            Assert.AreEqual(PipelineRunStatusCode.Finished, pipelineRun.CachedStatus, $"PipelineRun id: {pipelineRun.Id}");
        }

        [Test]
        public async Task RunFineTuningCloudAssetTestWithDefaultCompute()
        {
            IGraph graph = await CreateFineTuningCloudGraphWithAssetNode(_testEnvironment, assetTestScript, DataStoreMode.Direct, useGraphDefaultCompute: true, defaultComputeName: "myTestSynapse");
            IPipelineRun pipelineRun = await RunTestWithLogsAsync(graph, _testEnvironment, MinutesToWaitForPipelineToComplete, runHistoryExperimentName: "IntegrationTestFineTuningWithDefaultCompute");
            Assert.AreEqual(PipelineRunStatusCode.Failed, pipelineRun.CachedStatus, $"PipelineRun id: {pipelineRun.Id}");
        }

        [Test]
        public async Task RunFineTuningCloudWithEnvironmentVariables()
        {
            IGraph graph = await CreateFineTuningCloudGraphWithAssetNode(_testEnvironment, environmentVariableTestScript, DataStoreMode.Direct);
            IPipelineRun pipelineRun = await RunTestWithLogsAsync(graph, _testEnvironment, MinutesToWaitForPipelineToComplete, runHistoryExperimentName: "IntegrationTestFineTuning");
            Assert.AreEqual(PipelineRunStatusCode.Finished, pipelineRun.CachedStatus, $"PipelineRun id: {pipelineRun.Id}");
        }

        private async Task<IGraph> CreateFineTuningCloudGraphWithAssetNode(
            AetherEnvironment environment,
            Script script,
            DataStoreMode datastoreMode = DataStoreMode.Direct,
            bool useGraphDefaultCompute = false,
            string defaultComputeName = null)
        {
            var dtcGraph = environment.CreateNewGraph();

            var inputAssetId = "azureml://locations/centraluseuap/workspaces/bfff2042-8eb9-4c10-b6be-5ba4d9a473db/data/fine-tuning-test-2/versions/1";
            var uriFileAssetDefinition = new DataSetDefinition()
            {
                DataTypeShortName = "uri_file",
                Value = new DataSetDefinitionValue()
                {
                    AssetDefinition = new AssetDefinition
                    {
                        AssetId = AssetId.Parse(inputAssetId),
                        SerializedAssetId = inputAssetId,
                        Type = AssetType.UriFile
                    }
                }
            };

            var outputAssetPath = "azureml://datastores/${{default_datastore}}/paths/spark_output/${{name}}/${{output_name}}/";
            var assetOutputSettings = new AssetOutputSettings
            {
                Path = outputAssetPath,
                Type = AssetType.UriFolder
            };

            var outputSettings = new[] { new OutputSetting
            {
                DataReferenceName = moduleOutputName,
                AssetOutputSettings = assetOutputSettings,
                DataStoreMode = DataStoreMode.Direct,
                DataStoreName = datastoreName,
                Name = moduleOutputName,
                Overwrite = true
            } };

            IAetherDataSource datasource = await _testEnvironment.GetDataSourceAsync(uriFileAssetDefinition);
            var moduleId = await UploadFineTuningCloudNodeWithAssetInput(
              environment,
              sourcePath,
              script,
              environmentAssetId: null,
              inlineEnvironmentString: null,
              inputDataTypeId: "uri_file",
              outputDataTypeId: "uri_folder"
              );

            IAetherModule module = await environment.GetModuleAsync(moduleId);

            IModuleNode moduleNode1 = dtcGraph.AddNode(module, moduleOutputSettings: outputSettings);
            moduleNode1.CloudSettings = new CloudSettings
            {
                FineTuningCloudSetting = new FineTuningCloudSetting
                {
                    Compute = "spark32",
                    DriverCores = 1,
                    DriverMemory = "1g",
                    ExecutorCores = 1,
                    ExecutorMemory = "1g",
                    NumberExecutors = 1,
                    EnvironmentVariables = new Dictionary<string, string>()
                    {
                        { "test_key_1", "test_value_1" },
                        { "test_key_2", "test_value_2" }
                    }
                },
            };

            moduleNode1.SetComputeSetting(useGraphDefaultCompute: useGraphDefaultCompute);
            IDataSourceNode sourceNode = dtcGraph.AddNode(datasource);
            dtcGraph.Connect(sourceNode, moduleNode1.InputPortDictionary[moduleUrlFileInputName]);
            if (!string.IsNullOrEmpty(defaultComputeName))
            {
                dtcGraph.AddDefaultCompute(
                    new ComputeSetting()
                    {
                        Name = defaultComputeName
                    }
                );
            }

            moduleNode1.SetDatastoreSetting(useGraphDefaultDatastore: true);
            var datastoreSetting = new DatastoreSetting()
            {
                DataStoreName = "workspaceblobstore"
            };
            dtcGraph.AddDefaultDatastore(datastoreSetting);

            return dtcGraph;
        }

        private async Task<IGraph> CreateFineTuningCloudGraphWithDataSetNode(
            AetherEnvironment environment,
            string sourcePath,
            Script script,
            string? defaultDatastoreName,
            string? outputSettingDatastoreName,
            bool useGraphDefaultDatastore = false)
        {
            var dtcGraph = environment.CreateNewGraph();

            var tabularDatasetDefinition = new DataSetDefinition()
            {
                DataTypeShortName = "AzureBlobReference",
                Value = new DataSetDefinitionValue()
                {
                    DataSetReference = new RegisteredDataSetReference()
                    {
                        Id = tabularDatasetId,
                        Version = "1",
                    }
                }
            };

            var fileDatasetDefinition = new DataSetDefinition()
            {
                DataTypeShortName = "AzureBlobReference",
                Value = new DataSetDefinitionValue()
                {
                    DataSetReference = new RegisteredDataSetReference()
                    {
                        Id = fileDatasetId,
                        Version = "2",
                    }
                }
            };


            var outputSettings = new[] { new OutputSetting
            {
                DataReferenceName = moduleOutputName,
                DatasetRegistration = new DatasetRegistration(),
                DataStoreMode = DataStoreMode.Hdfs,
                DataStoreName = string.IsNullOrEmpty(outputSettingDatastoreName) ? datastoreName: outputSettingDatastoreName,
                Name = moduleOutputName,
                Overwrite = true
            } };
            outputSettings[0].DatasetOutputOptions = new DatasetOutputOptions { PathOnDatastore = "defaultPath" };

            IAetherDataSource tabularDataSource = await _testEnvironment.GetDataSourceAsync(tabularDatasetDefinition);
            IAetherDataSource fileDataSource = await _testEnvironment.GetDataSourceAsync(fileDatasetDefinition);

            var moduleId = await UploadFineTuningCloudNodeWithDataSetInput(
              environment,
              sourcePath,
              script
              );

            IAetherModule module = await environment.GetModuleAsync(moduleId);

            IModuleNode moduleNode1 = dtcGraph.AddNode(module, moduleOutputSettings: outputSettings);
            moduleNode1.CloudSettings = new CloudSettings
            {
                FineTuningCloudSetting = new FineTuningCloudSetting
                {
                    Compute = "spark32",
                    DriverCores = 1,
                    DriverMemory = "1g",
                    ExecutorCores = 1,
                    ExecutorMemory = "1g",
                    NumberExecutors = 1,
                }
            };
            IDataSourceNode tabularNode = dtcGraph.AddNode(tabularDataSource);
            IDataSourceNode fileNode = dtcGraph.AddNode(fileDataSource);
            dtcGraph.Connect(tabularNode, moduleNode1.InputPortDictionary[moduleTabularInputName]);
            dtcGraph.Connect(fileNode, moduleNode1.InputPortDictionary[moduleFileInputName]);

            moduleNode1.SetDatastoreSetting(useGraphDefaultDatastore: useGraphDefaultDatastore);
            if (!string.IsNullOrEmpty(defaultDatastoreName))
            {
                var datastoreSetting = new DatastoreSetting()
                {
                    DataStoreName = defaultDatastoreName
                };
                dtcGraph.AddDefaultDatastore(datastoreSetting);
            }

            return dtcGraph;
        }

        private async Task<IGraph> CreateFineTuningCloudGraphWithAssetEnv(
           AetherEnvironment environment,
           Script script,
           DataStoreMode datastoreMode = DataStoreMode.Direct,
           InputSetting inputSetting = null)
        {
            var dtcGraph = environment.CreateNewGraph();
            var inputAssetId = "azureml://locations/centraluseuap/workspaces/bfff2042-8eb9-4c10-b6be-5ba4d9a473db/data/fine-tuning-test-2/versions/1";
            var uriFileAssetDefinition = new DataSetDefinition()
            {
                DataTypeShortName = "uri_file",
                Value = new DataSetDefinitionValue()
                {
                    AssetDefinition = new AssetDefinition
                    {
                        AssetId = AssetId.Parse(inputAssetId),
                        SerializedAssetId = inputAssetId,
                        Type = AssetType.UriFile
                    }
                }
            };

            var outputAssetPath = "azureml://datastores/workspaceblobstore/paths/spark_output/${{name}}/";
            var assetOutputSettings = new AssetOutputSettings
            {
                Path = outputAssetPath,
                Type = AssetType.UriFolder
            };

            var outputSettings = new[] { new OutputSetting
            {
                DataReferenceName = moduleOutputName,
                AssetOutputSettings = assetOutputSettings,
                DataStoreMode = DataStoreMode.Direct,
                DataStoreName = datastoreName,
                Name = moduleOutputName,
                Overwrite = true
            } };

            IAetherDataSource datasource = await _testEnvironment.GetDataSourceAsync(uriFileAssetDefinition);
            var moduleId = await UploadFineTuningCloudNodeWithAssetInput(
              environment,
              sourcePath,
              script,
              environmentAssetId: GetEnvironmentSetup(),
              inlineEnvironmentString: null,
              inputDataTypeId: "uri_file",
              outputDataTypeId: "uri_folder"
              );

            IAetherModule module = await environment.GetModuleAsync(moduleId);
            IModuleNode moduleNode1 = dtcGraph.AddNode(module, moduleOutputSettings: outputSettings);

            moduleNode1.CloudSettings = new CloudSettings
            {
                FineTuningCloudSetting = new FineTuningCloudSetting
                {
                    Compute = "spark32",
                    DriverCores = 1,
                    DriverMemory = "1g",
                    ExecutorCores = 1,
                    ExecutorMemory = "1g",
                    NumberExecutors = 1,
                }
            };
            IDataSourceNode sourceNode = dtcGraph.AddNode(datasource);
            dtcGraph.Connect(sourceNode, moduleNode1.InputPortDictionary[moduleUrlFileInputName]);

            return dtcGraph;
        }

        private async Task<IGraph> CreateFineTuningCloudGraphWithInlineEnv(
          AetherEnvironment environment,
          Script script,
          DataStoreMode datastoreMode = DataStoreMode.Direct,
          InputSetting inputSetting = null)
        {
            var dtcGraph = environment.CreateNewGraph();

            var inputAssetId = "azureml://locations/centraluseuap/workspaces/bfff2042-8eb9-4c10-b6be-5ba4d9a473db/data/fine-tuning-test-2/versions/1";
            var uriFileAssetDefinition = new DataSetDefinition()
            {
                DataTypeShortName = "uri_file",
                Value = new DataSetDefinitionValue()
                {
                    AssetDefinition = new AssetDefinition
                    {
                        AssetId = AssetId.Parse(inputAssetId),
                        SerializedAssetId = inputAssetId,
                        Type = AssetType.UriFile
                    }
                }
            };

            var outputAssetPath = "azureml://datastores/workspaceblobstore/paths/spark_output/${{name}}/";
            var assetOutputSettings = new AssetOutputSettings
            {
                Path = outputAssetPath,
                Type = AssetType.UriFolder
            };

            var outputSettings = new[] { new OutputSetting
            {
                DataReferenceName = moduleOutputName,
                AssetOutputSettings = assetOutputSettings,
                DataStoreMode = DataStoreMode.Direct,
                DataStoreName = datastoreName,
                Name = moduleOutputName,
                Overwrite = true
            } };

            IAetherDataSource datasource = await _testEnvironment.GetDataSourceAsync(uriFileAssetDefinition);
            var moduleId = await UploadFineTuningCloudNodeWithAssetInput(
              environment,
              sourcePath,
              script,
              environmentAssetId: null,
              inlineEnvironmentString: GetEnvironmentInlineString(),
              inputDataTypeId: "uri_file",
              outputDataTypeId: "uri_folder"
              );

            IAetherModule module = await environment.GetModuleAsync(moduleId);

            IModuleNode moduleNode1 = dtcGraph.AddNode(module, moduleOutputSettings: outputSettings);

            moduleNode1.CloudSettings = new CloudSettings
            {
                FineTuningCloudSetting = new FineTuningCloudSetting
                {
                    Resources = new ResourcesSetting
                    {
                        InstanceSize = "standard_e4s_v3",
                        SparkVersion = "3.3"
                    },
                    DriverCores = 1,
                    DriverMemory = "1g",
                    ExecutorCores = 1,
                    ExecutorMemory = "1g",
                    NumberExecutors = 1,
                }
            };
            IDataSourceNode sourceNode = dtcGraph.AddNode(datasource);
            dtcGraph.Connect(sourceNode, moduleNode1.InputPortDictionary[moduleUrlFileInputName]);

            return dtcGraph;
        }

        private async Task<IGraph> CreateFineTuningCloudGraphWithRegistryAssetEnv(
           AetherEnvironment environment,
           Script script,
           DataStoreMode datastoreMode = DataStoreMode.Direct,
           InputSetting inputSetting = null)
        {
            var dtcGraph = environment.CreateNewGraph();

            var inputAssetId = "azureml://locations/centraluseuap/workspaces/bfff2042-8eb9-4c10-b6be-5ba4d9a473db/data/fine-tuning-test-2/versions/1";
            var uriFileAssetDefinition = new DataSetDefinition()
            {
                DataTypeShortName = "uri_file",
                Value = new DataSetDefinitionValue()
                {
                    AssetDefinition = new AssetDefinition
                    {
                        AssetId = AssetId.Parse(inputAssetId),
                        SerializedAssetId = inputAssetId,
                        Type = AssetType.UriFile
                    }
                }
            };

            var outputAssetPath = "azureml://datastores/workspaceblobstore/paths/spark_output/${{name}}/";
            var assetOutputSettings = new AssetOutputSettings
            {
                Path = outputAssetPath,
                Type = AssetType.UriFolder
            };

            var outputSettings = new[] { new OutputSetting
            {
                DataReferenceName = moduleOutputName,
                AssetOutputSettings = assetOutputSettings,
                DataStoreMode = DataStoreMode.Direct,
                DataStoreName = datastoreName,
                Name = moduleOutputName,
                Overwrite = true
            } };

            IAetherDataSource datasource = await _testEnvironment.GetDataSourceAsync(uriFileAssetDefinition);
            var moduleId = await UploadFineTuningCloudNodeWithAssetInput(
              environment,
              sourcePath,
              script,
              environmentAssetId: GetRegistryEnvironmentSetup(),
              inlineEnvironmentString: null,
              inputDataTypeId: "uri_file",
              outputDataTypeId: "uri_folder"
              );

            IAetherModule module = await environment.GetModuleAsync(moduleId);
            IModuleNode moduleNode1 = dtcGraph.AddNode(module, moduleOutputSettings: outputSettings);

            moduleNode1.CloudSettings = new CloudSettings
            {
                FineTuningCloudSetting = new FineTuningCloudSetting
                {
                    Compute = "spark32",
                    DriverCores = 1,
                    DriverMemory = "1g",
                    ExecutorCores = 1,
                    ExecutorMemory = "1g",
                    NumberExecutors = 1,
                }
            };
            IDataSourceNode sourceNode = dtcGraph.AddNode(datasource);
            dtcGraph.Connect(sourceNode, moduleNode1.InputPortDictionary[moduleUrlFileInputName]);

            return dtcGraph;
        }

        private async Task<string> UploadFineTuningCloudNodeWithDataSetInput(
            AetherEnvironment environment,
            string sourcePath,
            Script script,
            bool addDatasetOutput = false,
            string inputDataTypeId = "AzureBlobReference",
            string outputDataTypeId = "AzureBlobReference",
            string moduleDisplayName = "FineTuning dataset test module display",
            string moduleName = "Fine Tuning dataset test module")
        {
            var outputs = new List<StructuredInterfaceOutput>
            {
                new StructuredInterfaceOutput
                {
                    Name = moduleOutputName,
                    DataTypeId = outputDataTypeId,
                    DataStoreName = datastoreName,
                    DataStoreMode = DataStoreMode.Hdfs,
                    Overwrite = true,
                    DataReferenceName = moduleOutputName,
                }
            };

            var Arguments = new List<ArgumentAssignment>
            {
                new ArgumentAssignment()
                {
                    ValueType = ArgumentValueType.Literal,
                    Value = "--test_tabular"
                },
                new ArgumentAssignment()
                {
                    ValueType = ArgumentValueType.Input,
                    Value = "test_tabular"
                },
                new ArgumentAssignment()
                {
                    ValueType = ArgumentValueType.Literal,
                    Value = "--test_file"
                },
                new ArgumentAssignment()
                {
                    ValueType = ArgumentValueType.Input,
                    Value = "test_file"
                },
                new ArgumentAssignment()
                {
                    ValueType = ArgumentValueType.Literal,
                    Value = "--output"
                },
                new ArgumentAssignment()
                {
                    ValueType = ArgumentValueType.Output,
                    Value = "output"
                }
            };
            return await environment.UploadModuleAsync(
                sourcePath: sourcePath,
                uploadInfo: new ModuleUploadInfo(
               name: moduleName,
               displayName: moduleDisplayName,
               description: "finetuning dataset test module",
               isDeterministic: false,
               cloudSystem: "FineTuningCloud",
               moduleTypeVersion: "1.0",
               cloudSettings: new CloudSettings
               {
                   FineTuningCloudSetting = new FineTuningCloudSetting
                   {
                       Entry = new EntrySetting
                       {
                           File = script.Name,
                       },
                   }
               },
               structuredInterface: new StructuredInterface
               {
                   Inputs = new List<StructuredInterfaceInput>
                {
                    new StructuredInterfaceInput { Name = moduleTabularInputName, DataTypeIdsList = new List<string> { inputDataTypeId }, DataStoreMode = DataStoreMode.Direct, DataReferenceName = dataReferenceName },
                    new StructuredInterfaceInput { Name = moduleFileInputName, DataTypeIdsList = new List<string> { inputDataTypeId }, DataStoreMode = DataStoreMode.Hdfs, DataReferenceName = dataReferenceName },

                   },
                   Outputs = outputs,
                   Arguments = Arguments,
               }),
                forceCreateNew: true);
        }

        private async Task<string> UploadFineTuningCloudNodeWithAssetInput(
           AetherEnvironment environment,
           string sourcePath,
           Script script,
           string environmentAssetId,
           string inlineEnvironmentString,
           bool addDatasetOutput = false,
           string inputDataTypeId = "AzureBlobReference",
           string outputDataTypeId = "AzureBlobReference",
           string moduleDisplayName = "FineTuning dataset test module display",
           string moduleName = "Fine Tuning asset test module")
        {
            var outputs = new List<StructuredInterfaceOutput>
            {
                new StructuredInterfaceOutput
                {
                    Name = moduleOutputName,
                    DataTypeId = outputDataTypeId,
                    DataStoreMode = DataStoreMode.Direct,
                    Overwrite = true,
                }
            };

            var Arguments = new List<ArgumentAssignment>
            {
                new ArgumentAssignment()
                {
                    ValueType = ArgumentValueType.Literal,
                    Value = "--url_file"
                },
                new ArgumentAssignment()
                {
                    ValueType = ArgumentValueType.Input,
                    Value = "url_file"
                },
                new ArgumentAssignment()
                {
                    ValueType = ArgumentValueType.Literal,
                    Value = "--output"
                },
                new ArgumentAssignment()
                {
                    ValueType = ArgumentValueType.Output,
                    Value = "output"
                }
            };
            return await environment.UploadModuleAsync(
                sourcePath: sourcePath,
                uploadInfo: new ModuleUploadInfo(
               name: moduleName,
               displayName: moduleDisplayName,
               description: "finetuning dataset test module",
               isDeterministic: false,
               cloudSystem: "FineTuningCloud",
               moduleTypeVersion: "1.0",
               cloudSettings: new CloudSettings
               {
                   FineTuningCloudSetting = new FineTuningCloudSetting
                   {
                       Entry = new EntrySetting
                       {
                           File = script.Name,
                       },
                       EnvironmentAssetId = environmentAssetId,
                       InlineEnvironmentDefinitionString = inlineEnvironmentString,
                   }
               },
               structuredInterface: new StructuredInterface
               {
                   Inputs = new List<StructuredInterfaceInput>
                {
                    new StructuredInterfaceInput { Name = moduleUrlFileInputName, DataTypeIdsList = new List<string> { inputDataTypeId }, DataStoreMode = DataStoreMode.Direct, DataReferenceName = dataReferenceName },
                },
                   Outputs = outputs,
                   Arguments = Arguments,
               }),
                forceCreateNew: true);
        }

        /// <summary>
        /// Configuration to optimize the time to build the image by making some packages optional
        /// </summary>
        private class Script
        {
            public string Name { get; private set; }

            public bool IncludePandas { get; private set; }

            public Script(string name, bool includePandas = false)
            {
                Name = name;

                IncludePandas = includePandas;
            }
        }



        /// <summary>
        /// Brings the latest AzureML SDK version from master
        /// Pip version is set to 21.1.1 because the latest version (21.2.2 at this time) takes a long time to resolve dependencies
        /// </summary>
        private static string GetCondaSetup()
        {
            return $@"{{
                'name': 'project_environment',
                'dependencies': [
                    'python = 3.8',
                    'pip = 21.1.1',
                    {{
                        'pip': [
                           'azureml-defaults',
                           'azureml-core==1.20.0'
                        ]
                    }}
                ],
                'channels': [
                        'anaconda',
                        'conda-forge'
                ]
            }}";

        }

        private static string GetEnvironmentSetup()
        {

            return "azureml://locations/centraluseuap/workspaces/bfff2042-8eb9-4c10-b6be-5ba4d9a473db/environments/FineTuningEnv/versions/1";
        }

        private static string GetRegistryEnvironmentSetup()
        {
            return "azureml://registries/testFeed/environments/SKLearnEnv5/versions/4";
        }

        private static string GetEnvironmentInlineString()
        {
            return @"{
                'python': {
                    'interpreterPath': 'python',
                    'userManagedDependencies': false,
                    'condaDependencies': {
                        'name': 'project_environment',
                        'dependencies': [
                            'python=3.8.12',
                            {
                                'pip': [
                                    'azureml-defaults',
                                    'pillow'
                                ]
                            },
                            'numpy==1.17.0'
                        ],
                        'channels': [
                            'anaconda',
                            'conda-forge'
                        ]
                    },
                    'baseCondaEnvironment': null
                }
            }";
        }
    }
}
