using System;
using System.Linq;
using System.Collections.Generic;
using System.Threading.Tasks;
using NUnit.Framework;
using Microsoft.Aether.BlueBox.Library;
using Microsoft.Aether.AEVA.DataContracts;
using BBAetherLibrary.IntegrationTests.Common;

namespace BBAetherLibrary.Kusto.IntegrationTests
{
    [TestFixture]
    [Parallelizable(ParallelScope.All)]
    public class KustoTests : BBAetherTestFixtureBase
    {
        private const string ModulePath = @"TestResources/KustoTestResources";

        [Test]
        public async Task RunKustoTest()
        {
            IGraph graph = await SetupGraphAsync();
            IPipelineRun experiment = await RunTestWithLogsAsync(graph, _testEnvironment);

            Assert.AreEqual(PipelineRunStatusCode.Finished, experiment.CachedStatus, $"experiment id: {experiment.Id}");
        }

        private async Task<IGraph> SetupGraphAsync()
        {
            var moduleId = await UploadKustoModule(_testEnvironment);
            var kustoGraph = _testEnvironment.CreateNewGraph();
            IAetherModule module = await _testEnvironment.GetModuleAsync(moduleId);
            IModuleNode moduleNode1 = kustoGraph.AddNode(module);

            return kustoGraph;
        }

        private async Task<string> UploadKustoModule(AetherEnvironment environment)
        {
            var structuredInterface = new StructuredInterface
            {
                Outputs = new List<StructuredInterfaceOutput>
                {
                    new StructuredInterfaceOutput { Name = "Output", DataTypeId = "AzureBlobReference", DataStoreName = "myblobdatastore", DataReferenceName = "out" }
                },
                MetadataParameters = new List<StructuredInterfaceParameter>
                {
                    new StructuredInterfaceParameter { Name = "compute_name", DefaultValue = "kustocompute", ParameterType = ParameterType.String },
                    new StructuredInterfaceParameter { Name = "database_name", DefaultValue = "TestDatabase", ParameterType = ParameterType.String }
                },
                Parameters = new List<StructuredInterfaceParameter>
                {
                    new StructuredInterfaceParameter { Name = "parameter_dict", DefaultValue = @"{'specificState': 'MISSISSIPPI'}", ParameterType = ParameterType.String }
                }
            };

            var uploadInfo = new ModuleUploadInfo(
                name: "test kusto",
                displayName: "test kusto display",
                description: "kusto",
                isDeterministic: false,
                cloudSystem: "KustoCloud",
                structuredInterface: structuredInterface);

            return await environment.UploadModuleAsync(sourcePath: ModulePath, uploadInfo: uploadInfo);
        }
    }
}