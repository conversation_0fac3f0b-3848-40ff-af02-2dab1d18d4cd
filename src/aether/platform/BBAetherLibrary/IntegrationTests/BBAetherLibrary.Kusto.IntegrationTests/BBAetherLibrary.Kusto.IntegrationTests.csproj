<Project Sdk="Microsoft.NET.Sdk">
	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<OutputPath>$(BaseTargetDir)\app\aether\BBAetherLibrary\BBAetherLibrary.Kusto.IntegrationTests</OutputPath>
		<GenerateBindingRedirectsOutputType>true</GenerateBindingRedirectsOutputType>
    <ImportSharedFiles>true</ImportSharedFiles>
	</PropertyGroup>
	<ItemGroup>
		<Content Include="..\shared\TestResources\TestResources\KustoTestResources\query.txt">
			<Link>TestResources\KustoTestResources\query.txt</Link>
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
	</ItemGroup>
	<ItemGroup>
            <PackageReference Include="NUnit" />
            <PackageReference Include="NUnit3TestAdapter" />
            <PackageReference Include="Microsoft.NET.Test.Sdk" />
            <PackageReference Include="coverlet.collector" />
	</ItemGroup>
	<ItemGroup>
		<ProjectReference Include="..\BBAetherLibrary.IntegrationTests.Common\BBAetherLibrary.IntegrationTests.Common.csproj" />
		<ProjectReference Include="..\..\..\backendV2\BlueBox\AE3pService\DataContracts\Microsoft.Aether.AEVA.DataContracts.csproj" />
    <ProjectReference Include="$(AetherSrcRoot)\platform\BBAetherLibrary\Framework\BBAetherLibrary.Framework.csproj" />
	</ItemGroup>
</Project>
