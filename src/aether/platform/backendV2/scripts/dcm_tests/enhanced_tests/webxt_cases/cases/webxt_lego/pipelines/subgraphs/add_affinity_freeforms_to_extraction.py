# ------------------------------------------------------------------------------
# This is generated from https://ml.azure.com/runs/62ee89f3-775f-4273-a380-9b1d6f1ffeb0?wsid=/subscriptions/316a14b3-f94a-4267-a76a-497e589ef0a8/resourcegroups/training/workspaces/wxtcstrain
# To run this code, please install SDK by this command:
# pip install "azure-ml-component[notebooks]>=0.9.5" --upgrade
# More detailed guide to set up your environment: https://componentsdk.azurewebsites.net/getting_started.html
# ------------------------------------------------------------------------------
from azure.ml.component import Pipeline, dsl


from ...components import bigextractions_append_ranker_scores_partitioned_support_minscore_maxscore_cutoff_func
from ...components import create_ranker_zip_archive_from_freeforms_func
from ...components import scope_replace_strings_no_comma_func


# define pipeline
@dsl.pipeline(
    name='add_affinity_freeforms_to_extraction',
    description='Add Affinity FreeForms to Extraction',
)
def add_affinity_freeforms_to_extraction_func(
    input_freeforms=None,
    header=None,
    extraction=None,
) -> Pipeline:
    zip_ranker_freeforms_step = create_ranker_zip_archive_from_freeforms_func(
        input_freeforms=input_freeforms,
    )
    zip_ranker_freeforms_step.outputs.zip_rankers.configure(
        mode='mount',
    )

    replace_strings_before_append_step = scope_replace_strings_no_comma_func(
        input=header,
        sources='^m_,(\t)m_',
        replacements='m:,$1m:',
    )

    append_ranker_scores_step = bigextractions_append_ranker_scores_partitioned_support_minscore_maxscore_cutoff_func(
        input_header=replace_strings_before_append_step.outputs.output,
        input_partitions=extraction,
        seedrankers=zip_ranker_freeforms_step.outputs.zip_rankers,
        columnname='AffinityFreeForm',
        scalingfactor=1,
        translationfactor=0,
        minscore=0,
        maxscore=1000000000,
    )

    return {
        'output_header': append_ranker_scores_step.outputs.header,
        'output_extraction': append_ranker_scores_step.outputs.partitions,
    }
