# ------------------------------------------------------------------------------
# This is generated from https://ml.azure.com/runs/62ee89f3-775f-4273-a380-9b1d6f1ffeb0?wsid=/subscriptions/316a14b3-f94a-4267-a76a-497e589ef0a8/resourcegroups/training/workspaces/wxtcstrain
# To run this code, please install SDK by this command:
# pip install "azure-ml-component[notebooks]>=0.9.5" --upgrade
# More detailed guide to set up your environment: https://componentsdk.azurewebsites.net/getting_started.html
# ------------------------------------------------------------------------------
from azure.ml.component import Pipeline, dsl


from ...components import create_ranker_zip_archive_from_freeforms_func


from .append_freeform import append_freeform_func


# define pipeline
@dsl.pipeline(
    name='add_term_based_features',
    description='Add Term Based features',
)
def add_term_based_features_func(
    input=None,
    extraction=None,
    term_based_freeforms=None,
) -> Pipeline:
    zip_ranker_freeforms_step = create_ranker_zip_archive_from_freeforms_func(
        input_freeforms=term_based_freeforms,
    )
    zip_ranker_freeforms_step.outputs.zip_rankers.configure(
        mode='mount',
    )

    append_freeform_step = append_freeform_func(
        header_path=input,
        partitions_content=extraction,
        zip_rankers=zip_ranker_freeforms_step.outputs.zip_rankers,
        affinity_free_form_name='TermBasedFreeForm',
    )

    return {
        'header': append_freeform_step.outputs.header,
        'partitions': append_freeform_step.outputs.partitions,
    }
