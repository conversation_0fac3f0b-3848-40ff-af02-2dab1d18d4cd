$schema: https://componentsdk.azureedge.net/jsonschema/CommandComponent.json
command: echo "DUMMY COMPONENT"
description: Replace file content by replacement map (regex pattern/exact match).
display_name: Replace File Given Replacement Map (Input File)
environment:
  name: env_for_dcm_testing
inputs:
  by_row_or_column:
    default: row
    enum:
    - row
    - col
    optional: false
    type: Enum
  exact_replacements:
    description: Json format for replacement map, exact match
    optional: true
    type: String
  input:
    optional: false
    type: path
  output_new_line_at_end:
    default: true
    description: Whether to output new line at the end of the file
    optional: false
    type: String
  regex_pattern_replacements:
    description: Json format for replacement map, key is regex match pattern
    optional: true
    type: String
is_deterministic: true
name: replace_regex_string_file
outputs:
  output:
    type: path
type: CommandComponent
version: 0.0.1
