# ------------------------------------------------------------------------------
# This is generated from https://ml.azure.com/experiments/id/757f3391-a0a0-49d3-8780-99a744f72a99/runs/ff039288-a2ca-48e4-805e-25e6a4f5c65d?wsid=/subscriptions/b8c23406-f9b5-4ccb-8a65-a8cb5dcd6a5a/resourcegroups/dcm-westus2/workspaces/dcm-fastorc-test
# To run this code, please install SDK by this command:
# pip install "azure-ml-component[notebooks]>=0.9.5" --upgrade
# More detailed guide to set up your environment: https://componentsdk.azurewebsites.net/getting_started.html
# ------------------------------------------------------------------------------
from azure.ml.component import Pipeline, dsl
from azure.ml.component.dsl.types import Integer, String


from .convert_extraction_to_lightgbm_chunks_2 import convert_extraction_to_lightgbm_chunks_2_func


# define pipeline
@dsl.pipeline(
    name='prepare_eval_extraction_0',
    description='Prepare Extraction for Evaluation',
)
def prepare_eval_extraction_0_func(
    label_to_rate_mapping: String(optional=False)=None,
    num_test_chunks=None,
    run_id: String(optional=False)=None,
    extraction_header=None,
    test_partitions_content=None,
    input_ini_file=None,
) -> Pipeline:
    convert_eval_data_step = convert_extraction_to_lightgbm_chunks_2_func(
        extraction_header=extraction_header,
        partitions_content=test_partitions_content,
        input_ini_file=input_ini_file,
        comma_sep_extra_select_cols='"m:DocId,m:L_DocId,m:R_DocId,m:Rating"',
        extra_cols='m_DocId,m_L_DocId,m_R_DocId,m_Rating',
        label_to_rate_mapping=label_to_rate_mapping,
        num_chunks=num_test_chunks,
        run_id=run_id,
    )

    return {
        'test_data_dir': convert_eval_data_step.outputs.cosmos_chunks_directory,
        'test_data_meta_dir': convert_eval_data_step.outputs.meta_output_chunks_folder,
    }
