# ------------------------------------------------------------------------------
# This is generated from https://ml.azure.com/experiments/id/757f3391-a0a0-49d3-8780-99a744f72a99/runs/ff039288-a2ca-48e4-805e-25e6a4f5c65d?wsid=/subscriptions/b8c23406-f9b5-4ccb-8a65-a8cb5dcd6a5a/resourcegroups/dcm-westus2/workspaces/dcm-fastorc-test
# To run this code, please install SDK by this command:
# pip install "azure-ml-component[notebooks]>=0.9.5" --upgrade
# More detailed guide to set up your environment: https://componentsdk.azurewebsites.net/getting_started.html
# ------------------------------------------------------------------------------
from azure.ml.component import Pipeline, dsl
from azure.ml.component.dsl.types import String


from ...components import calculate_diffatclicklabel_func
from ...components import streamprocessor_ss_ss_func
from ...components import streamprocessor_tsv_tsv_func


from .ranker_inference import ranker_inference_func


# define pipeline
@dsl.pipeline(
    name='produce_sbs_scores',
    description='Produce SBS Scores',
)
def produce_sbs_scores_func(
    ctl_score_min_cap=None,
    ctl_score_max_cap=None,
    trt_score_min_cap=None,
    trt_score_max_cap=None,
    run_id: String(optional=False)=None,
    header=None,
    input_partitions=None,
    fp_label=None,
    control_ranker=None,
    treatment_ranker=None,
    control_smoothed_training_ini=None,
    treatment_smoothed_training_ini=None,
) -> Pipeline:
    control_inference_step = ranker_inference_func(
        header=header,
        input_partitions=input_partitions,
        ranker=control_ranker,
        smoothed_training_ini=control_smoothed_training_ini,
        label_to_rate_mapping='"0:0|1:1"',
        num_test_chunks='4',
        score_min_cap=ctl_score_min_cap,
        score_max_cap=ctl_score_max_cap,
        run_id=run_id,
    )

    treatment_inference_step = ranker_inference_func(
        header=header,
        input_partitions=input_partitions,
        ranker=treatment_ranker,
        smoothed_training_ini=treatment_smoothed_training_ini,
        label_to_rate_mapping='"0:0|1:1"',
        num_test_chunks='4',
        score_min_cap=trt_score_min_cap,
        score_max_cap=trt_score_max_cap,
        run_id=run_id,
    )

    combine_control_and_treatment_step = streamprocessor_tsv_tsv_func(
        input1=control_inference_step.outputs.score,
        input2=treatment_inference_step.outputs.score,
        scopecode='Out1 = SELECT In1.QueryId, In1.DocId, In1.Score AS ControlScore, In2.Score AS TreatmentScore FROM In1 INNER JOIN In2 ON In1.QueryId == In2.QueryId AND In1.DocId== In2.DocId; Out2 = SELECT * FROM Out1;',
        schema1='QueryId,DocId,Score',
        schema2='QueryId,DocId,Score',
    )
    combine_control_and_treatment_step.outputs.output1.configure(
        mode='mount',
    )
    combine_control_and_treatment_step.outputs.output2.configure(
        mode='mount',
    )

    calculate_diffatclicklabel_step = calculate_diffatclicklabel_func(
        input=combine_control_and_treatment_step.outputs.output1,
    )
    calculate_diffatclicklabel_step.outputs.output.configure(
        mode='mount',
    )

    join_fp_label_step = streamprocessor_ss_ss_func(
        input1=fp_label,
        input2=calculate_diffatclicklabel_step.outputs.output,
        input2isfolder='true',
        scopecode='In1 = SELECT DISTINCT * FROM In1; In2 = SELECT DISTINCT * FROM In2; Out1 = SELECT In1.*, In2.ControlScore AS ControlScoreA, In2.TreatmentScore AS TreatmentScoreA, In2.DAC AS WinUrlDAC FROM In1 INNER JOIN In2 ON In1.QueryId == In2.QueryId AND In1.FirstDocPosition == In2.DocId; Out1 = SELECT Out1.*, In2.ControlScore AS ControlScoreB, In2.TreatmentScore AS TreatmentScoreB, In2.DAC AS LossUrlDAC, In2.RankSame AS CTRankSame FROM Out1 INNER JOIN In2 ON Out1.QueryId == In2.QueryId AND Out1.SecondDocPosition == In2.DocId; Out2 = SELECT * FROM Out1 WHERE Type != -1 AND CTRankSame  != 1 AND WinUrlDAC != 1 AND LossUrlDAC != 1;',
        schema2='QueryId:long,DocId:int,ControlScore:float,TreatmentScore:float,DAC:int,RankSame:int',
    )
    join_fp_label_step.outputs.output1.configure(
        mode='mount',
    )
    join_fp_label_step.outputs.output2.configure(
        mode='mount',
    )

    return {
        'output': join_fp_label_step.outputs.output2,
    }
