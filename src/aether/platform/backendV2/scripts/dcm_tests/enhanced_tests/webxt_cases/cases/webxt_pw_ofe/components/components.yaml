components:
  add_freeforms_to_ur_freeform_func:
    yaml: ../components/add_freeforms_to_ur_freeform/0.4/component_spec.yaml
  add_line_number_func:
    yaml: ../components/add_line_number/0.1/component_spec.yaml
  add_remove_sort_dedup_func:
    yaml: ../components/add_remove_sort_dedup/0.1/component_spec.yaml
  all_feature_coverages_func:
    yaml: ../components/all_feature_coverages/0.2/component_spec.yaml
  append_mebraw_processed_feature_func:
    yaml: ../components/append_mebraw_processed_feature/0.0.0.6/component_spec.yaml
  as_online_ranker_gen_fastrank_to_code_func:
    yaml: ../components/as_online_ranker_gen_fastrank_to_code/1.0/component_spec.yaml
  assert_equal_byte_by_byte_func:
    yaml: ../components/assert_equal_byte_by_byte/0.1/component_spec.yaml
  bigextractions_append_ranker_scores_partitioned_support_minscore_maxscore_cutoff_func:
    yaml: ../components/bigextractions_append_ranker_scores_partitioned_support_minscore_maxscore_cutoff/1.1/component_spec.yaml
  bigextractions_rename_merge_and_slim_metastream_and_features_partitioned_and_output_test_data_func:
    yaml: ../components/bigextractions_rename_merge_and_slim_metastream_and_features_partitioned_and_output_test_data/1.6.6/component_spec.yaml
  calculate_diffatclicklabel_func:
    yaml: ../components/calculate_diffatclicklabel/0.0.0/component_spec.yaml
  calculate_rmse_func:
    yaml: ../components/calculate_rmse/0.1/component_spec.yaml
  cat_sort_and_unique_func:
    yaml: ../components/cat_sort_and_unique/0.0.1/component_spec.yaml
  compute_onedcg_output_html_with_markets_func:
    yaml: ../components/compute_onedcg_output_html_with_markets/0.2/component_spec.yaml
  concat_files_in_directory_func:
    yaml: ../components/concat_files_in_directory/0.1/component_spec.yaml
  concatenategenerictsvwithnewline_file_func:
    yaml: ../components/concatenategenerictsvwithnewline_file/0.1/component_spec.yaml
  concatenategenerictsvwithnewline_func:
    yaml: ../components/concatenategenerictsvwithnewline/0.1/component_spec.yaml
  conditional_passthrough_func:
    yaml: ../components/conditional_passthrough/0.1/component_spec.yaml
  convert_lightgbm_model_to_bing_ini_func:
    yaml: ../components/convert_lightgbm_model_to_bing_ini/0.1/component_spec.yaml
  convert_sstream_to_tsv_with_header_func:
    yaml: ../components/convert_sstream_to_tsv_with_header/0.1/component_spec.yaml
  convert_to_libsvm_format_func:
    yaml: ../components/convert_to_libsvm_format/0.6/component_spec.yaml
  countlinesoncosmos_func:
    yaml: ../components/countlinesoncosmos/1.1/component_spec.yaml
  create_dataset_func:
    yaml: ../components/create_dataset/0.0.1/component_spec.yaml
  create_generic_tsv_from_string_list_func:
    yaml: ../components/create_generic_tsv_from_string_list/0.0.1/component_spec.yaml
  create_pairwise_delta_freeforms_func:
    yaml: ../components/create_pairwise_delta_freeforms/0.3/component_spec.yaml
  create_pairwise_feature_names_func:
    yaml: ../components/create_pairwise_feature_names/0.0.0/component_spec.yaml
  create_ranker_zip_archive_from_freeforms_func:
    yaml: ../components/create_ranker_zip_archive_from_freeforms/0.3/component_spec.yaml
  createfrinputini_func:
    yaml: ../components/createfrinputini/0.1/component_spec.yaml
  custom_diff_func:
    yaml: ../components/custom_diff/0.0.1/component_spec.yaml
  esample_deterministic_func:
    yaml: ../components/esample_deterministic/0.2/component_spec.yaml
  extract_all_features_ffv1_ffv2_func:
    yaml: ../components/extract_all_features_ffv1_ffv2/1.2/component_spec.yaml
  extract_feature_importance_func:
    yaml: ../components/extract_feature_importance/1.1/component_spec.yaml
  extraction_feature_distribution_func:
    yaml: ../components/extraction_feature_distribution/0.0.1/component_spec.yaml
  extractrankerfeatures_func:
    yaml: ../components/extractrankerfeatures/1.0/component_spec.yaml
  fastrank_create_feature_input_func:
    yaml: ../components/fastrank_create_feature_input/0.1/component_spec.yaml
  generate_fp_pairwise_extraction_func:
    yaml: ../components/generate_fp_pairwise_extraction/0.0.0.0/component_spec.yaml
  inline_metrics_calculation_func:
    yaml: ../components/inline_metrics_calculation/0.0.1/component_spec.yaml
  join_feature_score_func:
    yaml: ../components/join_feature_score/0.47/component_spec.yaml
  join_ranklm_score_on_position_0_25_func:
    yaml: ../components/join_ranklm_score_on_position_0_25/0.0.0.1/component_spec.yaml
  lightgbm_inference_func:
    yaml: ../components/lightgbm_inference/0.0.1/component_spec.yaml
  lightgbm_training_func:
    yaml: ../components/lightgbm_training/0.0.1/component_spec.yaml
  log_blob_data_func:
    yaml: ../components/log_blob_data/0.2/component_spec.yaml
  merge_ini_func:
    yaml: ../components/merge_ini/0.1/component_spec.yaml
  merge_partition_chunks_and_header_to_sstream_func:
    yaml: ../components/merge_partition_chunks_and_header_to_sstream/0.4/component_spec.yaml
  merge_two_files_by_column_func:
    yaml: ../components/merge_two_files_by_column/0.1/component_spec.yaml
  output_html_for_inline_metrics_func:
    yaml: ../components/output_html_for_inline_metrics/0.0.0.1/component_spec.yaml
  prepare_onedcg_data_func:
    yaml: ../components/prepare_onedcg_data/0.1/component_spec.yaml
  pw_feature_processing_func:
    yaml: ../components/pw_feature_processing/3.0/component_spec.yaml
  pw_feature_renaming_func:
    yaml: ../components/pw_feature_renaming/0.1/component_spec.yaml
  remove_feature_prefix_func:
    yaml: ../components/remove_feature_prefix/1.0/component_spec.yaml
  replace_regex_string_file_func:
    yaml: ../components/replace_regex_string_file/0.0.1/component_spec.yaml
  replace_regex_string_folder_func:
    yaml: ../components/replace_regex_string_folder/0.0.1/component_spec.yaml
  scope_replace_strings_no_comma_func:
    yaml: ../components/scope_replace_strings_no_comma/0.0.1/component_spec.yaml
  smooth_training_ini_func:
    yaml: ../components/smooth_training_ini/0.0.0/component_spec.yaml
  ss_to_generic_tsv_base64_func:
    yaml: ../components/ss_to_generic_tsv_base64/0.0.0.6/component_spec.yaml
  streamprocessor_ss_ss_func:
    yaml: ../components/streamprocessor_ss_ss/0.5/component_spec.yaml
  streamprocessor_tsv_tsv_func:
    yaml: ../components/streamprocessor_tsv_tsv/0.5/component_spec.yaml
  unified_ofe_post_process_func:
    yaml: ../components/unified_ofe_post_process/0.33/component_spec.yaml
  unified_ranker_chashcodetol3codeforpwml_with_delta_freeform_as_input_5_func:
    yaml: ../components/unified_ranker_chashcodetol3codeforpwml_with_delta_freeform_as_input_5/0.5/component_spec.yaml
  unified_ranker_generate_pairwise_extraction_for_evaluation_func:
    yaml: ../components/unified_ranker_generate_pairwise_extraction_for_evaluation/2.7.8/component_spec.yaml
  unified_ranker_generate_pairwise_extraction_func:
    yaml: ../components/unified_ranker_generate_pairwise_extraction/2.1.8/component_spec.yaml
  unified_ranker_replace_delta_freeforms_with_place_holders_func:
    yaml: ../components/unified_ranker_replace_delta_freeforms_with_place_holders/0.4/component_spec.yaml
