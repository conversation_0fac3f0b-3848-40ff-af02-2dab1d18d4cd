$schema: https://componentsdk.azureedge.net/jsonschema/CommandComponent.json
command: echo "DUMMY COMPONENT"
description: 'Created from Aether client version: ********, module id:97a36725-746b-4338-98bf-f7e2d122f0f6,
  module name:[BigExtractions] Rename Merge and Slim Metastream and Features (Partitioned
  and Output Test Data) and module description Rename and merge columns. The syntax
  of MergeSpec is X=A|B|C, Y=D|E, meaning columns A, B and C should be merged to produce
  column X (removing A, B, C) and D and E should merge to produce Y. If the renamed
  column already exists in the schema it will be merged. The syntax of FeatureRenaming
  is TargetFeature=SourceFeature per line.'
display_name: '[BigExtractions] Rename Merge and Slim Metastream and Features (Partitioned
  and Output Test Data)'
environment:
  name: env_for_dcm_testing
inputs:
  featurerenaming:
    is_resource: true
    optional: false
    type: AnyDirectory
  input_header:
    is_resource: true
    optional: false
    type: AnyDirectory
  input_partitions:
    optional: false
    type: AnyDirectory
  inputini:
    is_resource: true
    optional: false
    type: AnyDirectory
  mergespec:
    is_resource: true
    optional: false
    type: AnyDirectory
  numinputpartitions:
    default: 1000
    optional: false
    type: Integer
  numoutputpartitions:
    default: 1000
    optional: false
    type: Integer
is_deterministic: true
name: bigextractions_rename_merge_and_slim_metastream_and_features_partitioned_and_output_test_data
outputs:
  header:
    type: AnyDirectory
  numrows:
    type: AnyDirectory
  partitions:
    type: AnyDirectory
  testextraction:
    type: AnyDirectory
type: CommandComponent
version: 1.6.6
