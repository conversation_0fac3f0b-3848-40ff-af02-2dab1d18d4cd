# ------------------------------------------------------------------------------
# This is generated from https://ml.azure.com/runs/62ee89f3-775f-4273-a380-9b1d6f1ffeb0?wsid=/subscriptions/316a14b3-f94a-4267-a76a-497e589ef0a8/resourcegroups/training/workspaces/wxtcstrain
# To run this code, please install SDK by this command:
# pip install "azure-ml-component[notebooks]>=0.9.5" --upgrade
# More detailed guide to set up your environment: https://componentsdk.azurewebsites.net/getting_started.html
# ------------------------------------------------------------------------------
from azure.ml.component import Pipeline, dsl
from azure.ml.component.dsl.types import Integer, String


from ...components import fastrank_inference_on_cosmos_func
from ...components import streamprocessor_ss_ss_func


from .inference_post_process_c524bed8_b49c_4442_873e_d5a8aad254ee import inference_post_process_c524bed8_b49c_4442_873e_d5a8aad254ee_func


# define pipeline
@dsl.pipeline(
    name='click_dcg_evaluation',
    description='Click DCG Evaluation',
)
def click_dcg_evaluation_func(
    columns: String(optional=False)=None,
    control_score_min_cap: Integer(optional=False)=None,
    control_score_max_cap: Integer(optional=False)=None,
    treatment_score_min_cap: Integer(optional=False)=None,
    treatment_score_max_cap: Integer(optional=False)=None,
    click_dcg_extraction=None,
    metadata=None,
    header=None,
    extraction=None,
    control_ranker=None,
    treatment_ranker=None,
) -> Pipeline:
    distinct_extraction = streamprocessor_ss_ss_func(
        input1=click_dcg_extraction,
        scopecode='Out1 = SELECT DISTINCT m_QueryId, m_DocId, m_UtilityLabel, m_Language, m_Region FROM In1; Out2 = SELECT * FROM Out1;',
    )

    control_inference = fastrank_inference_on_cosmos_func(
        header=header,
        extraction=extraction,
        ranker=control_ranker,
        auto_set_column_types='true',
        keep_all_extraction_columns='false',
        score_column_name='Score',
        columns_to_extract=columns,
        order_by=columns,
    )

    treatment_inference = fastrank_inference_on_cosmos_func(
        header=header,
        extraction=extraction,
        ranker=treatment_ranker,
        auto_set_column_types='true',
        keep_all_extraction_columns='false',
        score_column_name='Score',
        columns_to_extract=columns,
        order_by=columns,
    )

    control_post_process = inference_post_process_c524bed8_b49c_4442_873e_d5a8aad254ee_func(
        metadata=metadata,
        inference_extraction=control_inference.outputs.extraction_inference,
        click_dcg_extraction=distinct_extraction.outputs.output1,
        exponent=True,
        min_cap=control_score_min_cap,
        max_cap=control_score_max_cap,
    )

    treatment_post_process = inference_post_process_c524bed8_b49c_4442_873e_d5a8aad254ee_func(
        metadata=metadata,
        inference_extraction=treatment_inference.outputs.extraction_inference,
        click_dcg_extraction=distinct_extraction.outputs.output1,
        exponent=True,
        min_cap=treatment_score_min_cap,
        max_cap=treatment_score_max_cap,
    )

    return {
        'control_result': control_post_process.outputs.result,
        'treatment_result': treatment_post_process.outputs.result,
    }
