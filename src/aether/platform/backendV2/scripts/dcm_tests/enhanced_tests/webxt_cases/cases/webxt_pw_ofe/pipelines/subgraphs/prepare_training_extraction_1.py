# ------------------------------------------------------------------------------
# This is generated from https://ml.azure.com/experiments/id/757f3391-a0a0-49d3-8780-99a744f72a99/runs/ff039288-a2ca-48e4-805e-25e6a4f5c65d?wsid=/subscriptions/b8c23406-f9b5-4ccb-8a65-a8cb5dcd6a5a/resourcegroups/dcm-westus2/workspaces/dcm-fastorc-test
# To run this code, please install SDK by this command:
# pip install "azure-ml-component[notebooks]>=0.9.5" --upgrade
# More detailed guide to set up your environment: https://componentsdk.azurewebsites.net/getting_started.html
# ------------------------------------------------------------------------------
from azure.ml.component import Pipeline, dsl
from azure.ml.component.dsl.types import Integer, String


from ...components import smooth_training_ini_func


from .convert_extraction_to_lightgbm_chunks_1 import convert_extraction_to_lightgbm_chunks_1_func


# define pipeline
@dsl.pipeline(
    name='prepare_training_extraction_1',
    description='Prepare Extraction for Training',
)
def prepare_training_extraction_1_func(
    run_id: String(optional=False)=None,
    tsv2bin_custom_label_to_rate_mapping: String(optional=False)=None,
    training_nodes: Integer(optional=False)=None,
    num_test_chunks: Integer(optional=False)=None,
    extraction_header=None,
    train_partitions_content=None,
    test_partitions_content=None,
    input_ini_file=None,
) -> Pipeline:
    smooth_training_ini_step = smooth_training_ini_func(
        extraction_header=extraction_header,
        traininputini=input_ini_file,
    )
    smooth_training_ini_step.outputs.smoothedtraininputini.configure(
        mode='mount',
    )
    smooth_training_ini_step.outputs.modifications_file.configure(
        mode='mount',
    )

    convert_train_data_step = convert_extraction_to_lightgbm_chunks_1_func(
        extraction_header=extraction_header,
        partitions_content=train_partitions_content,
        input_ini_file=smooth_training_ini_step.outputs.smoothedtraininputini,
        comma_sep_extra_select_cols='"m:DocId,m:L_DocId,m:R_DocId,m:Rating"',
        extra_cols='m_DocId,m_L_DocId,m_R_DocId,m_Rating',
        label_to_rate_mapping=tsv2bin_custom_label_to_rate_mapping,
        num_chunks=training_nodes,
        run_id=run_id,
    )

    convert_test_data_step = convert_extraction_to_lightgbm_chunks_1_func(
        extraction_header=extraction_header,
        partitions_content=test_partitions_content,
        input_ini_file=smooth_training_ini_step.outputs.smoothedtraininputini,
        comma_sep_extra_select_cols='"m:DocId,m:L_DocId,m:R_DocId,m:Rating"',
        extra_cols='m_DocId,m_L_DocId,m_R_DocId,m_Rating',
        label_to_rate_mapping=tsv2bin_custom_label_to_rate_mapping,
        num_chunks=num_test_chunks,
        run_id=run_id,
    )

    return {
        'smoothed_training_ini': smooth_training_ini_step.outputs.smoothedtraininputini,
        'train_data_dir': convert_train_data_step.outputs.cosmos_chunks_directory,
        'train_data_meta_dir': convert_train_data_step.outputs.meta_output_chunks_folder,
        'test_data_dir': convert_test_data_step.outputs.cosmos_chunks_directory,
        'test_data_meta_dir': convert_test_data_step.outputs.meta_output_chunks_folder,
    }
