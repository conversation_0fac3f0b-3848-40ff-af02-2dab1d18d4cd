from datetime import datetime

from utils import submit_a_test_case


def webxt_lego(tags, experiment_name, is_first_time=False, regenerate_outputs=False):
    from .pipelines.lego import pipeline
    tags["azureml.Designer"] = "true"
    timestamp = datetime.now().isoformat(' ', 'seconds')
    display_name = 'lego ' + timestamp

    return submit_a_test_case(pipeline, tags, experiment_name, display_name, is_first_time, regenerate_outputs)
