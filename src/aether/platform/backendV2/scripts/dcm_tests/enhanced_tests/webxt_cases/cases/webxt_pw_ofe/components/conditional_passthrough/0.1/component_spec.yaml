$schema: https://componentsdk.azureedge.net/jsonschema/CommandComponent.json
command: echo "DUMMY COMPONENT"
description: Pass through input if control is true, otherwise fail
display_name: Conditional Passthrough
environment:
  name: env_for_dcm_testing
inputs:
  control:
    optional: false
    type:
    - AnyDirectory
    - AnyDirectory
  input:
    optional: false
    type:
    - AnyDirectory
    - AnyDirectory
is_deterministic: true
name: conditional_passthrough
outputs:
  output:
    type: AnyDirectory
type: CommandComponent
version: 0.1
