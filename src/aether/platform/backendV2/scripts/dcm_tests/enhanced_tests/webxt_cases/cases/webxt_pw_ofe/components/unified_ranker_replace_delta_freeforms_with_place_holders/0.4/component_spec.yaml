$schema: https://componentsdk.azureedge.net/jsonschema/CommandComponent.json
command: echo "DUMMY COMPONENT"
description: 'Created from Aether client version: ********, module id:dea98733-a178-4fe1-ab50-f6d2cb11de5a,
  module name:[Unified Ranker] Replace Delta FreeForms with Place Holders and module
  description Replace Delta FreeForms with Place Holders.  Assumes any FreeForm that''s
  present must be a Delta feature.  Uses NeuralTreeEvaluator.dll to parse the expressions.'
display_name: '[Unified Ranker] Replace Delta FreeForms with Place Holders'
environment:
  name: env_for_dcm_testing
inputs:
  input:
    optional: false
    type: AnyDirectory
is_deterministic: true
name: unified_ranker_replace_delta_freeforms_with_place_holders
outputs:
  output:
    type: AnyDirectory
type: CommandComponent
version: 0.4
