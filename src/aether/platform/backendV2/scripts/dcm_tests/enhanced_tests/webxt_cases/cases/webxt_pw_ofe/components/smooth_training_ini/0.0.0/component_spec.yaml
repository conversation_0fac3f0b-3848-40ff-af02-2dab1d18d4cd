$schema: https://componentsdk.azureedge.net/jsonschema/CommandComponent.json
command: echo "DUMMY COMPONENT"
description: 'Created from Aether client version: ********, module id:0a69226b-cb7d-49dd-8399-2529739c6ab4,
  module name:Smooth Training Ini and module description Consume a training ini file,
  and smooth divide-by-zero expressions that would evaluate to NaN or +/- Infinity'
display_name: Smooth Training INI
environment:
  name: env_for_dcm_testing
inputs:
  extraction_header:
    optional: false
    type: AnyDirectory
  traininputini:
    optional: false
    type: AnyDirectory
is_deterministic: true
name: smooth_training_ini
outputs:
  modifications_file:
    type: AnyDirectory
  smoothedtraininputini:
    type: AnyDirectory
type: CommandComponent
version: 0.0.0
