# ------------------------------------------------------------------------------
# This is generated from https://ml.azure.com/experiments/id/757f3391-a0a0-49d3-8780-99a744f72a99/runs/ff039288-a2ca-48e4-805e-25e6a4f5c65d?wsid=/subscriptions/b8c23406-f9b5-4ccb-8a65-a8cb5dcd6a5a/resourcegroups/dcm-westus2/workspaces/dcm-fastorc-test
# To run this code, please install SDK by this command:
# pip install "azure-ml-component[notebooks]>=0.9.5" --upgrade
# More detailed guide to set up your environment: https://componentsdk.azurewebsites.net/getting_started.html
# ------------------------------------------------------------------------------
from azureml.core import Workspace
from azureml.core import Dataset
from azure.ml.component import Pipeline, dsl
from azure.ml.component.dsl.types import Integer

from ...components import bigextractions_rename_merge_and_slim_metastream_and_features_partitioned_and_output_test_data_func


# get dataset

titanic_csv = Dataset.File.from_files("https://dprepdata.blob.core.windows.net/demo/Titanic.csv")


# define pipeline
@dsl.pipeline(
    name='ofe_slim_2',
    description='OFE Slim',
)
def ofe_slim_2_func(
    num_input_partitions: Integer(optional=False)=None,
    num_output_partitions: Integer(optional=False)=None,
    input_features=None,
    header_path=None,
    partitions_content=None,
) -> Pipeline:
    rename_merge_slim_features_step = bigextractions_rename_merge_and_slim_metastream_and_features_partitioned_and_output_test_data_func(
        mergespec=titanic_csv,
        featurerenaming=titanic_csv,
        inputini=input_features,
        input_header=header_path,
        input_partitions=partitions_content,
        numinputpartitions=num_input_partitions,
        numoutputpartitions=num_output_partitions,
    )
    rename_merge_slim_features_step.outputs.header.configure(
        mode='mount',
    )
    rename_merge_slim_features_step.outputs.partitions.configure(
        mode='mount',
    )
    rename_merge_slim_features_step.outputs.testextraction.configure(
        mode='mount',
    )
    rename_merge_slim_features_step.outputs.numrows.configure(
        mode='mount',
    )

    return {
        'header': rename_merge_slim_features_step.outputs.header,
        'partitions': rename_merge_slim_features_step.outputs.partitions,
    }
