# ------------------------------------------------------------------------------
# This is generated from https://ml.azure.com/experiments/id/757f3391-a0a0-49d3-8780-99a744f72a99/runs/ff039288-a2ca-48e4-805e-25e6a4f5c65d?wsid=/subscriptions/b8c23406-f9b5-4ccb-8a65-a8cb5dcd6a5a/resourcegroups/dcm-westus2/workspaces/dcm-fastorc-test
# To run this code, please install SDK by this command:
# pip install "azure-ml-component[notebooks]>=0.9.5" --upgrade
# More detailed guide to set up your environment: https://componentsdk.azurewebsites.net/getting_started.html
# ------------------------------------------------------------------------------
from azureml.core import Workspace
from azureml.core import Dataset
from azure.ml.component import Pipeline, dsl
from azure.ml.component.dsl.types import String


from ...components import create_ranker_zip_archive_from_freeforms_func
from ...components import createfrinputini_func
from ...components import custom_diff_func
from ...components import extraction_feature_distribution_func


from .add_term_based_features import add_term_based_features_func
from .append_freeform_0 import append_freeform_0_func
from .feature_predicate import feature_predicate_func
from .join_with_latest_ranklm_feature_snapshot import join_with_latest_ranklm_feature_snapshot_func
from .join_with_meb_click_scores import join_with_meb_click_scores_func
from .ofe_feature_rename_and_post_process import ofe_feature_rename_and_post_process_func
from .ofe_slim_1 import ofe_slim_1_func


# get dataset
titanic_csv = Dataset.File.from_files("https://dprepdata.blob.core.windows.net/demo/Titanic.csv")


# define pipeline
@dsl.pipeline(
    name='training_data_processing',
    description='Training Data Processing',
)
def training_data_processing_func(
    ranklm_feature_cov_thres=None,
    start_date=None,
    end_date=None,
    feature_list=None,
    fusion_affinity_freeforms=None,
    term_based_freeforms=None,
) -> Pipeline:
    ofe_feature_rename_and_postprocess_step = ofe_feature_rename_and_post_process_func(
        pathpara=titanic_csv,
        partitions_content=titanic_csv,
    )

    feature_predicate_step = feature_predicate_func(
        original_file_for_string_replace=ofe_feature_rename_and_postprocess_step.outputs.output,
        partitions_content=ofe_feature_rename_and_postprocess_step.outputs.partitions,
    )

    diff_after_feature_predicate_step = custom_diff_func(
        input_a=feature_list,
        input_b=feature_predicate_step.outputs.header,
    )
    diff_after_feature_predicate_step.outputs.input_a_diff.configure(
        mode='mount',
    )
    diff_after_feature_predicate_step.outputs.intersection.configure(
        mode='mount',
    )
    diff_after_feature_predicate_step.outputs.input_b_diff.configure(
        mode='mount',
    )

    createfrinputini_step = createfrinputini_func(
        featurelist=feature_list,
    )
    createfrinputini_step.outputs.inputini.configure(
        mode='mount',
    )

    ofe_slim_step = ofe_slim_1_func(
        input_features=createfrinputini_step.outputs.inputini,
        header_path=feature_predicate_step.outputs.header,
        partitions_content=feature_predicate_step.outputs.partitions,
        num_input_partitions=1000,
        num_output_partitions=1000,
    )

    feature_distribution_step_0 = extraction_feature_distribution_func(
        headerpath=ofe_slim_step.outputs.header,
        extraction=ofe_slim_step.outputs.partitions,
        columnstoremoveregex='m:',
        columnstoremoveinvert='false',
        invalidvalueoverrideregex='nan',
    )
    feature_distribution_step_0.outputs.output.configure(
        mode='mount',
    )
    feature_distribution_step_0.outputs.outputss.configure(
        mode='mount',
    )

    zip_ranker_freeforms_step = create_ranker_zip_archive_from_freeforms_func(
        input_freeforms=fusion_affinity_freeforms,
    )
    zip_ranker_freeforms_step.outputs.zip_rankers.configure(
        mode='mount',
    )

    append_freeform_step = append_freeform_0_func(
        header_path=ofe_slim_step.outputs.header,
        partitions_content=ofe_slim_step.outputs.partitions,
        zip_rankers=zip_ranker_freeforms_step.outputs.zip_rankers,
        affinity_free_form_name='AffinityFreeForm',
    )

    join_ranklm_feature_step = join_with_latest_ranklm_feature_snapshot_func(
        header_path=append_freeform_step.outputs.header,
        extraction_content=append_freeform_step.outputs.partitions,
        input_features=createfrinputini_step.outputs.inputini,
        ranklm_feature_cov_thres=ranklm_feature_cov_thres,
    )

    add_term_based_features_step = add_term_based_features_func(
        term_based_freeforms=term_based_freeforms,
        input=join_ranklm_feature_step.outputs.header,
        extraction=join_ranklm_feature_step.outputs.extraction,
    )

    feature_distribution_step_1 = extraction_feature_distribution_func(
        headerpath=add_term_based_features_step.outputs.header,
        extraction=add_term_based_features_step.outputs.partitions,
        columnstoremoveregex='m:',
        columnstoremoveinvert='false',
        invalidvalueoverrideregex='nan',
    )
    feature_distribution_step_1.outputs.output.configure(
        mode='mount',
    )
    feature_distribution_step_1.outputs.outputss.configure(
        mode='mount',
    )

    diff_after_add_term_based_features_step = custom_diff_func(
        input_a=feature_list,
        input_b=add_term_based_features_step.outputs.header,
    )
    diff_after_add_term_based_features_step.outputs.input_a_diff.configure(
        mode='mount',
    )
    diff_after_add_term_based_features_step.outputs.intersection.configure(
        mode='mount',
    )
    diff_after_add_term_based_features_step.outputs.input_b_diff.configure(
        mode='mount',
    )

    join_with_meb_click_scores_step = join_with_meb_click_scores_func(
        header_path=add_term_based_features_step.outputs.header,
        extraction_content=add_term_based_features_step.outputs.partitions,
    )

    feature_distribution_step_2 = extraction_feature_distribution_func(
        headerpath=join_with_meb_click_scores_step.outputs.header,
        extraction=join_with_meb_click_scores_step.outputs.extraction,
        columnstoremoveregex='m:',
        columnstoremoveinvert='false',
        invalidvalueoverrideregex='nan',
    )
    feature_distribution_step_2.outputs.output.configure(
        mode='mount',
    )
    feature_distribution_step_2.outputs.outputss.configure(
        mode='mount',
    )

    diff_after_join_with_meb_click_scores_step = custom_diff_func(
        input_a=feature_list,
        input_b=join_with_meb_click_scores_step.outputs.header,
    )
    diff_after_join_with_meb_click_scores_step.outputs.input_a_diff.configure(
        mode='mount',
    )
    diff_after_join_with_meb_click_scores_step.outputs.intersection.configure(
        mode='mount',
    )
    diff_after_join_with_meb_click_scores_step.outputs.input_b_diff.configure(
        mode='mount',
    )

    return {
        'header': join_with_meb_click_scores_step.outputs.header,
        'extraction': join_with_meb_click_scores_step.outputs.extraction,
    }
