# ------------------------------------------------------------------------------
# This is generated from https://ml.azure.com/experiments/id/757f3391-a0a0-49d3-8780-99a744f72a99/runs/ff039288-a2ca-48e4-805e-25e6a4f5c65d?wsid=/subscriptions/b8c23406-f9b5-4ccb-8a65-a8cb5dcd6a5a/resourcegroups/dcm-westus2/workspaces/dcm-fastorc-test
# To run this code, please install SDK by this command:
# pip install "azure-ml-component[notebooks]>=0.9.5" --upgrade
# More detailed guide to set up your environment: https://componentsdk.azurewebsites.net/getting_started.html
# ------------------------------------------------------------------------------
from azure.ml.component import Pipeline, dsl
from azure.ml.component.dsl.types import Float, Integer


from ...components import esample_deterministic_func


# define pipeline
@dsl.pipeline(
    name='sample_ofe_0',
    description='Sample OFE',
)
def sample_ofe_0_func(
    sample_rate: Float(optional=False)=None,
    seed: Integer(optional=False)=None,
    num_partitions: Integer(optional=False)=None,
    input_extraction_header=None,
    input_extraction=None,
) -> Pipeline:
    sample_extraction_step = esample_deterministic_func(
        input_extraction_header=input_extraction_header,
        input_extraction=input_extraction,
        extraction_columns='META_ImpressionGuid',
        key_combine_expression='META_ImpressionGuid',
        buckets=1000,
        sample_rate=sample_rate,
        seed=seed,
        num_partitions=num_partitions,
    )
    sample_extraction_step.outputs.output_sample_extraction.configure(
        mode='mount',
    )
    sample_extraction_step.outputs.output_others_extraction.configure(
        mode='mount',
    )
    sample_extraction_step.outputs.output_sample_extraction_count.configure(
        mode='mount',
    )
    sample_extraction_step.outputs.output_others_extraction_count.configure(
        mode='mount',
    )

    return {
        'output_sample_extraction': sample_extraction_step.outputs.output_sample_extraction,
        'output_others_extraction': sample_extraction_step.outputs.output_others_extraction,
    }
