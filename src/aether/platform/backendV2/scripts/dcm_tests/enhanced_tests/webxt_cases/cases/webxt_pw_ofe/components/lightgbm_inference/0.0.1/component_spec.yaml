$schema: https://componentsdk.azureedge.net/jsonschema/CommandComponent.json
command: echo "DUMMY COMPONENT"
description: A module to run LightGBM inference job
display_name: LightGBM Inference
environment:
  name: env_for_dcm_testing
inputs:
  adls_datastore:
    default: adls_relevance09
    type: String
  lgbm_model:
    description: LightGBM model directory
    optional: false
    type: AnyDirectory
  test_data_dir:
    description: Directory with test data files
    optional: false
    type: AnyDirectory
  test_data_file_name:
    default: File_{0}.txt
    description: Test data file name pattern (similar to train)
    type: String
is_deterministic: true
name: lightgbm_inference
outputs:
  output_classification:
    description: Output directory for classification result
    type: AnyDirectory
  output_folder:
    description: Output directory
    type: AnyDirectory
  output_portal_url:
    description: Output directory for an output file containing URL to the AML portal
      for current run
    type: AnyDirectory
type: CommandComponent
version: 0.0.1
