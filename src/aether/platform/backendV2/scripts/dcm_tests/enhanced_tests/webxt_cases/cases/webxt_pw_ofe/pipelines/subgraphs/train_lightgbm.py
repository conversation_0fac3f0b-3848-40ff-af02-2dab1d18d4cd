# ------------------------------------------------------------------------------
# This is generated from https://ml.azure.com/experiments/id/757f3391-a0a0-49d3-8780-99a744f72a99/runs/ff039288-a2ca-48e4-805e-25e6a4f5c65d?wsid=/subscriptions/b8c23406-f9b5-4ccb-8a65-a8cb5dcd6a5a/resourcegroups/dcm-westus2/workspaces/dcm-fastorc-test
# To run this code, please install SDK by this command:
# pip install "azure-ml-component[notebooks]>=0.9.5" --upgrade
# More detailed guide to set up your environment: https://componentsdk.azurewebsites.net/getting_started.html
# ------------------------------------------------------------------------------
from azure.ml.component import Pipeline, dsl
from azure.ml.component.dsl.types import Integer, String


from ...components import add_remove_sort_dedup_func
from ...components import createfrinputini_func
from ...components import custom_diff_func
from ...components import extraction_feature_distribution_func


from .ofe_slim_0 import ofe_slim_0_func
from .sample_ofe_0 import sample_ofe_0_func
from .train_pairwise_model_select_top_feature_lightgbm import train_pairwise_model_select_top_feature_lightgbm_func
from .train_pw_model_lightgbm_0 import train_pw_model_lightgbm_0_func


# define pipeline
@dsl.pipeline(
    name='train_lightgbm',
    description='LightGBM Training Subgraph',
)
def train_lightgbm_func(
    regression_mid_value: Integer(optional=False)=None,
    label_mapping: String(optional=False)=None,
    label_gain: String(optional=False)=None,
    run_id: String(optional=False)=None,
    num_test_chunks=None,
    header=None,
    partitions=None,
    feature_list=None,
) -> Pipeline:
    adjust_doc_dnn_bert_scores_step = add_remove_sort_dedup_func(
        input=feature_list,
        add='["DocDNNBertScore_0","DocDNNBertScore_1","DocDNNBertScore_2","DocDNNBertScore_3","DocDNNBertScore_4","DocDNNBertScore_5","DocDNNBertScore_6","DocDNNBertScore_7","DocDNNBertScore_8","DocDNNBertScore_9","DocDNNBertScore_10","DocDNNBertScore_11","DocDNNBertScore_12","DocDNNBertScore_13","DocDNNBertScore_14","DocDNNBertScore_15","DocDNNBertScore_16","DocDNNBertScore_17","DocDNNBertScore_18","DocDNNBertScore_19","DocDNNBertScore_20","DocDNNBertScore_21","DocDNNBertScore_22","DocDNNBertScore_23","DocDNNBertScore_24","DocDNNBertScore_25"]',
        remove='["L2TaggedEvaluatorRankValue_Concept","L2TaggedEvaluatorRankValue_TopOFE","L2TaggedEvaluatorRankValue_Dwell","L2TaggedEvaluatorRankValue_TopHRS","L2TaggedEvaluatorRankValue_SBS","RecallDRScore","L2TaggedEvaluatorRankValue_Season","L2TaggedEvaluatorRankValue_BC","PrecisionDRScore"]',
        match_mode='fullmatch',
    )
    adjust_doc_dnn_bert_scores_step.outputs.output.configure(
        mode='mount',
    )

    sample_ofe_step_0 = sample_ofe_0_func(
        input_extraction=partitions,
        input_extraction_header=header,
        sample_rate=0.2,
        seed=5678,
        num_partitions=1000,
    )

    train_and_select_top_feature_step = train_pairwise_model_select_top_feature_lightgbm_func(
        header=header,
        training_data=sample_ofe_step_0.outputs.output_sample_extraction,
        features=adjust_doc_dnn_bert_scores_step.outputs.output,
        top_n=249,
        label_mapping=label_mapping,
        label_gain=label_gain,
        run_id=run_id,
        num_test_chunks=num_test_chunks,
    )

    create_ini_step = createfrinputini_func(
        featurelist=train_and_select_top_feature_step.outputs.top_features,
    )
    create_ini_step.outputs.inputini.configure(
        mode='mount',
    )

    ofe_slim_step = ofe_slim_0_func(
        header_path=header,
        partitions_content=partitions,
        input_features=create_ini_step.outputs.inputini,
        num_input_partitions=1000,
        num_output_partitions=100,
    )

    diff_step = custom_diff_func(
        input_a=adjust_doc_dnn_bert_scores_step.outputs.output,
        input_b=ofe_slim_step.outputs.header,
    )
    diff_step.outputs.input_a_diff.configure(
        mode='mount',
    )
    diff_step.outputs.intersection.configure(
        mode='mount',
    )
    diff_step.outputs.input_b_diff.configure(
        mode='mount',
    )

    extraction_feature_distribution_step = extraction_feature_distribution_func(
        headerpath=ofe_slim_step.outputs.header,
        extraction=ofe_slim_step.outputs.partitions,
        columnstoremoveregex='m:',
        columnstoremoveinvert='false',
        invalidvalueoverrideregex='nan',
        numpartitions=100,
    )
    extraction_feature_distribution_step.outputs.output.configure(
        mode='mount',
    )
    extraction_feature_distribution_step.outputs.outputss.configure(
        mode='mount',
    )

    sample_ofe_step_1 = sample_ofe_0_func(
        input_extraction=ofe_slim_step.outputs.partitions,
        input_extraction_header=ofe_slim_step.outputs.header,
        sample_rate=0.8,
        seed=12345,
        num_partitions=100,
    )

    train_pw_model_lightgbm_step = train_pw_model_lightgbm_0_func(
        header=ofe_slim_step.outputs.header,
        train_data=sample_ofe_step_1.outputs.output_sample_extraction,
        test_data=sample_ofe_step_1.outputs.output_others_extraction,
        features=train_and_select_top_feature_step.outputs.top_features,
        training_nodes_step_1=2,
        training_nodes_step_2=1,
        regression_mid_value=regression_mid_value,
        label_mapping=label_mapping,
        label_gain=label_gain,
        run_id=run_id,
        num_test_chunks=num_test_chunks,
    )

    return {
        'feature_distribution': extraction_feature_distribution_step.outputs.outputss,
        'smoothed_training_ini': train_pw_model_lightgbm_step.outputs.smoothed_training_ini,
        'output_metric_of_last_iteration': train_pw_model_lightgbm_step.outputs.output_metric_of_last_iteration,
        'output_model': train_pw_model_lightgbm_step.outputs.output_model,
        'output_portal_url': train_pw_model_lightgbm_step.outputs.output_portal_url,
        'fastrank_model': train_pw_model_lightgbm_step.outputs.fastrank_model,
        'test_rmse': train_pw_model_lightgbm_step.outputs.test_rmse,
        'raw_feature_importance': train_pw_model_lightgbm_step.outputs.raw_feature_importance,
    }
