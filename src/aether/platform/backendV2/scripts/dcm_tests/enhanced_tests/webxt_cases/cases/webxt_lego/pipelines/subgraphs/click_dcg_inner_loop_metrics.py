# ------------------------------------------------------------------------------
# This is generated from https://ml.azure.com/runs/62ee89f3-775f-4273-a380-9b1d6f1ffeb0?wsid=/subscriptions/316a14b3-f94a-4267-a76a-497e589ef0a8/resourcegroups/training/workspaces/wxtcstrain
# To run this code, please install SDK by this command:
# pip install "azure-ml-component[notebooks]>=0.9.5" --upgrade
# More detailed guide to set up your environment: https://componentsdk.azurewebsites.net/getting_started.html
# ------------------------------------------------------------------------------
from azure.ml.component import Pipeline, dsl
from azure.ml.component.dsl.types import Integer


from ...components import extraction_feature_distribution_func
from ...components import unified_ranker_generate_pairwise_extraction_for_evaluation_func


from .click_dcg_evaluation import click_dcg_evaluation_func
from .fail_if_missing_features import fail_if_missing_features_func
from .merge_dedup_ranker_features import merge_dedup_ranker_features_func
from .prepare_click_extraction import prepare_click_extraction_func


# define pipeline
@dsl.pipeline(
    name='click_dcg_inner_loop_metrics',
    description='Click DCG Inner Loop Metrics',
)
def click_dcg_inner_loop_metrics_func(
    control_score_min_cap: Integer(optional=False)=None,
    control_score_max_cap: Integer(optional=False)=None,
    treatment_score_min_cap: Integer(optional=False)=None,
    treatment_score_max_cap: Integer(optional=False)=None,
    control_ranker=None,
    treatment_ranker=None,
    affinity_freeforms=None,
    term_freeforms=None,
) -> Pipeline:
    click_extraction = prepare_click_extraction_func(
        affinity_freeforms=affinity_freeforms,
        term_freeforms=term_freeforms,
    )

    feature_distribution = extraction_feature_distribution_func(
        headerpath=click_extraction.outputs.header,
        extraction=click_extraction.outputs.extraction,
        columnstoremoveregex='m:',
        columnstoremoveinvert='false',
        invalidvalueoverrideregex='nan',
    )

    features = merge_dedup_ranker_features_func(
        control=control_ranker,
        treatment=treatment_ranker,
    )

    pw_extraction = unified_ranker_generate_pairwise_extraction_for_evaluation_func(
        header=click_extraction.outputs.header,
        inputextraction=click_extraction.outputs.extraction,
        features=features.outputs.merged_features,
        metadataoutput2urls='m:Position',
        deltafeature='false',
        deltaoffset='10000000',
        allowshortlines='true',
    )

    missing_control_features = fail_if_missing_features_func(
        header=click_extraction.outputs.header,
        features=features.outputs.control_features,
        ignore_filter=None,
        ignore_grep_params=None,
    )

    missing_treatment_features = fail_if_missing_features_func(
        header=click_extraction.outputs.header,
        features=features.outputs.treatment_features,
        ignore_filter=None,
        ignore_grep_params=None,
    )

    results = click_dcg_evaluation_func(
        control_ranker=control_ranker,
        treatment_ranker=treatment_ranker,
        click_dcg_extraction=click_extraction.outputs.extraction_sstream,
        metadata=pw_extraction.outputs.outputmetadata,
        header=pw_extraction.outputs.outputheader,
        extraction=pw_extraction.outputs.outputextraction,
        columns='m_Market,m_Query,m_QueryId,m_DocId',
        control_score_min_cap=control_score_min_cap,
        control_score_max_cap=control_score_max_cap,
        treatment_score_min_cap=treatment_score_min_cap,
        treatment_score_max_cap=treatment_score_max_cap,
    )

    return {
        'control_scores': results.outputs.control_result,
        'treatment_scores': results.outputs.treatment_result,
    }
