$schema: https://componentsdk.azureedge.net/jsonschema/CommandComponent.json
command: echo "DUMMY COMPONENT"
description: 'Rewrite from Aether client version: ********, module id:18fd973f-8109-476c-a25f-371696489b2f,
  module name:All Feature Coverages and module description Calculates coverage of
  all features in the input extraction file. (change from ipy to python)'
display_name: Compute Feature Coverages
environment:
  name: env_for_dcm_testing
inputs:
  control_ranker_features:
    optional: false
    type: AnyDirectory
  features_header:
    optional: false
    type: AnyDirectory
  input_samples:
    optional: false
    type: AnyDirectory
  treatment_ranker_features:
    optional: false
    type: AnyDirectory
is_deterministic: true
name: all_feature_coverages
outputs:
  samples_control_ranker_feature_coverage:
    type: AnyDirectory
  samples_feature_coverage:
    type: AnyDirectory
  samples_treatment_ranker_feature_coverage:
    type: AnyDirectory
type: CommandComponent
version: 0.2
