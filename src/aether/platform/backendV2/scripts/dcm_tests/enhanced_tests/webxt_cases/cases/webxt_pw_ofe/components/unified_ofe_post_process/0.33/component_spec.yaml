$schema: https://componentsdk.azureedge.net/jsonschema/CommandComponent.json
command: echo "DUMMY COMPONENT"
description: 'Created from Aether client version: ********, module id:5a5e1b2e-2470-4eda-a94b-************,
  module name:Unified OFE Post Process and module description Remove Duplicate DocId+Url
  (from other query paths) for the same ImpressionGuid and Unify QueryId for the same
  ImpressionGuid, convert double feature to int, keep url with longest features. Source
  code: https://msasg.visualstudio.com/Bing_and_IPG/_git/RelevanceFundamentals?path=/private/OFE/ScopeScripts/ExtractionToolsLibrary/UnifiedOFEImpressionReducer.cs&version=GBqinguo/Join.HRS.PW.Update&_a=history,
  ExtractionToolsLibrary.dll commit: b7a7ab55519084a8f17dc6e139a396bdb7f2581f'
display_name: Unified OFE Post Process
environment:
  name: env_for_dcm_testing
inputs:
  extractionheader:
    optional: false
    type: AnyDirectory
  numoutputchunks:
    default: 1000
    optional: false
    type: Integer
  partitionscontent:
    optional: false
    type: AnyDirectory
is_deterministic: true
name: unified_ofe_post_process
outputs:
  outputnumrows:
    type: AnyDirectory
  partitions:
    type: AnyDirectory
type: CommandComponent
version: 0.33
