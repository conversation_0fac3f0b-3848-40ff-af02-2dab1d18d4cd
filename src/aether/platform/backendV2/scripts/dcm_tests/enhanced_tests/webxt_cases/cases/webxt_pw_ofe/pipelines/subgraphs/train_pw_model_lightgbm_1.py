# ------------------------------------------------------------------------------
# This is generated from https://ml.azure.com/experiments/id/757f3391-a0a0-49d3-8780-99a744f72a99/runs/ff039288-a2ca-48e4-805e-25e6a4f5c65d?wsid=/subscriptions/b8c23406-f9b5-4ccb-8a65-a8cb5dcd6a5a/resourcegroups/dcm-westus2/workspaces/dcm-fastorc-test
# To run this code, please install SDK by this command:
# pip install "azure-ml-component[notebooks]>=0.9.5" --upgrade
# More detailed guide to set up your environment: https://componentsdk.azurewebsites.net/getting_started.html
# ------------------------------------------------------------------------------
from azure.ml.component import Pipeline, dsl
from azure.ml.component.dsl.types import Integer, String


from ...components import calculate_rmse_func
from ...components import extract_feature_importance_func


from .generate_fastrank_ini_with_pw_features_with_delta_freeforms_1 import generate_fastrank_ini_with_pw_features_with_delta_freeforms_1_func
from .lightgbm_inference_on_aml_1 import lightgbm_inference_on_aml_1_func
from .lightgbm_training_and_convert_to_fastrank_1 import lightgbm_training_and_convert_to_fastrank_1_func
from .lightgbm_training_and_convert_to_fastrank_2 import lightgbm_training_and_convert_to_fastrank_2_func
from .prepare_training_extraction_1 import prepare_training_extraction_1_func


# define pipeline
@dsl.pipeline(
    name='train_pw_model_lightgbm_1',
    description='Train PW Model (LightGBM)',
)
def train_pw_model_lightgbm_1_func(
    parallel_tree_learner: String(optional=False)=None,
    label_gain: String(optional=False)=None,
    label_mapping=None,
    run_id: String(optional=False)=None,
    num_test_chunks=None,
    exclude_patterns=None,
    training_nodes_step_1: Integer(optional=False)=None,
    training_nodes_step_2: Integer(optional=False)=None,
    pw_header=None,
    train_extraction=None,
    test_extraction=None,
    features=None,
) -> Pipeline:
    generate_features_step = generate_fastrank_ini_with_pw_features_with_delta_freeforms_1_func(
        raw_features=features,
    )

    prepare_extraction_step = prepare_training_extraction_1_func(
        extraction_header=pw_header,
        train_partitions_content=train_extraction,
        test_partitions_content=test_extraction,
        input_ini_file=generate_features_step.outputs.pw_features,
        run_id=run_id,
        training_nodes=training_nodes_step_1,
        num_test_chunks=num_test_chunks,
        tsv2bin_custom_label_to_rate_mapping='"{}"'.format(label_mapping),
    )

    lgbm_train_and_convert_to_fastrank_step_1 = lightgbm_training_and_convert_to_fastrank_1_func(
        train_data_dir=prepare_extraction_step.outputs.train_data_dir,
        test_data_dir=prepare_extraction_step.outputs.test_data_dir,
        smoothed_training_ini=prepare_extraction_step.outputs.smoothed_training_ini,
        label_gain=label_gain,
        run_id=run_id,
        parallel_tree_learner=parallel_tree_learner,
        training_nodes=training_nodes_step_1,
        num_test_chunks=num_test_chunks,
    )

    extract_feature_importance_step = extract_feature_importance_func(
        input=lgbm_train_and_convert_to_fastrank_step_1.outputs.fastrank_model,
        top_n=100,
        exclude_patterns=exclude_patterns,
    )
    extract_feature_importance_step.outputs.feature_importance_all.configure(
        mode='mount',
    )
    extract_feature_importance_step.outputs.raw_feature_importance.configure(
        mode='mount',
    )
    extract_feature_importance_step.outputs.features.configure(
        mode='mount',
    )

    generate_features_step_2 = generate_fastrank_ini_with_pw_features_with_delta_freeforms_1_func(
        raw_features=extract_feature_importance_step.outputs.features,
    )

    prepare_extraction_step_2 = prepare_training_extraction_1_func(
        extraction_header=pw_header,
        train_partitions_content=train_extraction,
        test_partitions_content=test_extraction,
        input_ini_file=generate_features_step_2.outputs.pw_features,
        run_id=run_id,
        training_nodes=training_nodes_step_2,
        num_test_chunks=num_test_chunks,
        tsv2bin_custom_label_to_rate_mapping='"{}"'.format(label_mapping),
    )

    lgbm_train_and_convert_to_fastrank_step_2 = lightgbm_training_and_convert_to_fastrank_2_func(
        train_data_dir=prepare_extraction_step_2.outputs.train_data_dir,
        test_data_dir=prepare_extraction_step_2.outputs.test_data_dir,
        smoothed_training_ini=prepare_extraction_step_2.outputs.smoothed_training_ini,
        label_gain=label_gain,
        run_id=run_id,
        parallel_tree_learner=parallel_tree_learner,
        training_nodes=training_nodes_step_2,
        num_test_chunks=num_test_chunks,
    )

    lightgbm_inference_step = lightgbm_inference_on_aml_1_func(
        lgbm_model=lgbm_train_and_convert_to_fastrank_step_2.outputs.output_model,
        test_data_dir=prepare_extraction_step_2.outputs.test_data_dir,
        test_data_file_name='File_{0}.txt',
    )

    calculate_rmse_step = calculate_rmse_func(
        predicts_folder=lightgbm_inference_step.outputs.predicts,
        ratings_folder=prepare_extraction_step_2.outputs.test_data_meta_dir,
    )
    calculate_rmse_step.outputs.output.configure(
        mode='mount',
    )

    extract_feature_importance_step_2 = extract_feature_importance_func(
        input=lgbm_train_and_convert_to_fastrank_step_2.outputs.fastrank_model,
    )
    extract_feature_importance_step_2.outputs.feature_importance_all.configure(
        mode='mount',
    )
    extract_feature_importance_step_2.outputs.raw_feature_importance.configure(
        mode='mount',
    )
    extract_feature_importance_step_2.outputs.features.configure(
        mode='mount',
    )

    return {
        'smoothed_training_ini': prepare_extraction_step_2.outputs.smoothed_training_ini,
        'output_metric_of_last_iteration': lgbm_train_and_convert_to_fastrank_step_2.outputs.output_metric_of_last_iteration,
        'output_model': lgbm_train_and_convert_to_fastrank_step_2.outputs.output_model,
        'output_portal_url': lgbm_train_and_convert_to_fastrank_step_2.outputs.output_portal_url,
        'fastrank_model': lgbm_train_and_convert_to_fastrank_step_2.outputs.fastrank_model,
        'test_rmse': calculate_rmse_step.outputs.output,
        'raw_feature_importance': extract_feature_importance_step_2.outputs.raw_feature_importance,
    }
