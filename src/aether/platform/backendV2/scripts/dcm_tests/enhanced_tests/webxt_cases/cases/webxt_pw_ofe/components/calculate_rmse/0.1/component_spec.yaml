$schema: https://componentsdk.azureedge.net/jsonschema/CommandComponent.json
command: echo "DUMMY COMPONENT"
description: Given predicts and ratings, Calculate RMSE
display_name: Calculate RMSE from given predicts and ratings
environment:
  name: env_for_dcm_testing
inputs:
  predict_file_pattern:
    default: File_*.txt
    description: Filename pattern for predicts
    optional: false
    type: String
  predicts_folder:
    description: Input folder containing predicts
    optional: false
    type: AnyDirectory
  rating_file_pattern:
    default: DocInfo_*.txt
    description: Filename pattern for ratings
    optional: false
    type: String
  ratings_folder:
    description: Input folder containing raitings
    optional: false
    type: AnyDirectory
is_deterministic: true
name: calculate_rmse
outputs:
  output:
    type: AnyDirectory
type: CommandComponent
version: 0.1
