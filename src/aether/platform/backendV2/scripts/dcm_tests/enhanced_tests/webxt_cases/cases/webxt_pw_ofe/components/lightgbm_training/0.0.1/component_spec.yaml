$schema: https://componentsdk.azureedge.net/jsonschema/CommandComponent.json
command: echo "DUMMY COMPONENT"
description: A module to run LightGBM training job
display_name: LightGBM Training
environment:
  name: env_for_dcm_testing
inputs:
  additional_lgbm_params:
    default: NotSet
    description: Additional parameters to pass to LightGBM. Use NotSet if none
    type: String
  adls_datastore:
    default: adls_relevance09
    type: String
  aml_run_node_count:
    default: 1
    description: Number of nodes used by this AML run
    type: Integer
  aml_run_process_count:
    default: 1
    description: Number of processes per node used by this AML run. Anything other
      than 1 is untested
    type: Integer
  continued_training_model:
    default: NotSet
    description: Input model file name from a previous training run
    type: String
  continued_training_model_dir:
    description: Directory with continued training model
    optional: true
    type: AnyDirectory
  debug:
    default: false
    type: Boolean
  enable_mlflow:
    default: true
    type: Boolean
  feature_fraction:
    default: 0.15
    description: Fraction of features to use for each tree
    type: Float
  label_gain:
    description: Relevant gain for labels. Used only in lambdarank
    optional: false
    type: String
  learning_rate:
    default: 0.3
    description: Learning rate
    type: Float
  max_bin:
    default: 16
    description: Max number of bins that feature values will be bucketed in
    type: Integer
  metric:
    description: Metrics to report
    optional: false
    type: String
  min_data_in_leaf:
    default: 1
    description: Minimum data points per leaf node
    type: Integer
  mlflow_parent_run:
    default: true
    type: Boolean
  num_leaves:
    default: 30
    description: Number of leaves
    type: Integer
  num_test_chunks:
    description: Number of test file chunks
    optional: false
    type: Integer
  num_trees:
    description: Number of trees to train
    optional: false
    type: Integer
  objective:
    default: lambdarank
    description: Learning objective, support customize objective or standard lib objective
    enum:
    - regression
    - regression_l1
    - huber
    - fair
    - poisson
    - quantile
    - mape
    - gamma
    - tweedie
    - binary
    - multiclass
    - multiclassova
    - cross_entropy
    - cross_entropy_lambda
    - lambdarank
    - rank_xendcg
    type: Enum
  parallel_tree_learner:
    default: data_parallel
    description: Type of LightGBM parallel learning algorithm to use. See https://lightgbm.readthedocs.io/en/latest/Parallel-Learning-Guide.html#choose-appropriate-parallel-algorithm
      for help
    enum:
    - serial
    - data_parallel
    - voting_parallel
    type: Enum
  query_column:
    default: NotSet
    description: Index of the query column after the label column
    type: String
  rerun_key:
    default: '2019-11-01'
    description: Dummy parameter only used to force a rerun in Aether and AML
    type: String
  test_data_dir:
    description: Directory with test data files
    optional: false
    type: AnyDirectory
  test_data_file_name:
    default: File_{0}.txt
    description: Test data file name pattern (similar to train)
    type: String
  train_data_dir:
    description: Directory with training data files
    optional: false
    type: AnyDirectory
  train_data_file_name:
    default: File_{0}.txt
    description: Training data file name pattern that is used to determine the file
      name used by the i-th node
    type: String
  weight_file_column:
    default: NotSet
    description: Index of the weight column after the label column
    type: String
is_deterministic: true
name: lightgbm_training
outputs:
  output_logs_zip:
    description: Output directory for an output zip containing some of the logs from
      training
    type: AnyDirectory
  output_metric_of_last_iteration:
    description: Output directory for metric of last iteration
    type: AnyDirectory
  output_metric_plot:
    description: Output directory for metric plot
    type: AnyDirectory
  output_metrics:
    description: Output directory for metrics TSV
    type: AnyDirectory
  output_model:
    description: Output directory for model
    type: AnyDirectory
  output_portal_url:
    description: Output directory for an output file containing URL to the AML portal
      for current run
    type: AnyDirectory
type: CommandComponent
version: 0.0.1
