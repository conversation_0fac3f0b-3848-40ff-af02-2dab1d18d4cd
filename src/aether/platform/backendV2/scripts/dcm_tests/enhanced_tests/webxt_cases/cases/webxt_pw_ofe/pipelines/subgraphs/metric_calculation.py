# ------------------------------------------------------------------------------
# This is generated from https://ml.azure.com/experiments/id/757f3391-a0a0-49d3-8780-99a744f72a99/runs/ff039288-a2ca-48e4-805e-25e6a4f5c65d?wsid=/subscriptions/b8c23406-f9b5-4ccb-8a65-a8cb5dcd6a5a/resourcegroups/dcm-westus2/workspaces/dcm-fastorc-test
# To run this code, please install SDK by this command:
# pip install "azure-ml-component[notebooks]>=0.9.5" --upgrade
# More detailed guide to set up your environment: https://componentsdk.azurewebsites.net/getting_started.html
# ------------------------------------------------------------------------------
from azure.ml.component import Pipeline, dsl
from azure.ml.component.dsl.types import String


from ...components import concatenategenerictsvwithnewline_file_func
from ...components import inline_metrics_calculation_func
from ...components import output_html_for_inline_metrics_func


from .produce_sbs_scores import produce_sbs_scores_func


# define pipeline
@dsl.pipeline(
    name='metric_calculation',
    description='Metric Calculation',
)
def metric_calculation_func(
    ctl_score_min_cap=None,
    ctl_score_max_cap=None,
    trt_score_min_cap=None,
    trt_score_max_cap=None,
    run_id: String(optional=False)=None,
    header=None,
    input_partitions=None,
    fp_label=None,
    control_ranker=None,
    treatment_ranker=None,
    control_smoothed_training_ini=None,
    treatment_smoothed_training_ini=None,
) -> Pipeline:
    produce_sbs_scores_step = produce_sbs_scores_func(
        header=header,
        input_partitions=input_partitions,
        fp_label=fp_label,
        control_ranker=control_ranker,
        treatment_ranker=treatment_ranker,
        control_smoothed_training_ini=control_smoothed_training_ini,
        treatment_smoothed_training_ini=treatment_smoothed_training_ini,
        ctl_score_min_cap=ctl_score_min_cap,
        ctl_score_max_cap=ctl_score_max_cap,
        trt_score_min_cap=trt_score_min_cap,
        trt_score_max_cap=trt_score_max_cap,
        run_id=run_id,
    )

    all_inline_metrics_calculation_step = inline_metrics_calculation_func(
        input=produce_sbs_scores_step.outputs.output,
        tag='ALL',
        market_filter='WHERE Market IN ("de-DE", "en-AU", "en-CA", "en-GB", "en-IN", "en-US", "es-ES", "es-MX", "fr-CA", "fr-FR", "it-IT", "ja-JP", "pt-BR", "zh-CN", "zh-HK", "zh-TW")',
    )
    all_inline_metrics_calculation_step.outputs.all_segment_output.configure(
        mode='mount',
    )
    all_inline_metrics_calculation_step.outputs.query_level_output.configure(
        mode='mount',
    )
    all_inline_metrics_calculation_step.outputs.market_output.configure(
        mode='mount',
    )
    all_inline_metrics_calculation_step.outputs.combined_all_ouput.configure(
        mode='mount',
    )
    all_inline_metrics_calculation_step.outputs.outputheader.configure(
        mode='mount',
    )

    sat_click_inline_metrics_calculation_step = inline_metrics_calculation_func(
        input=produce_sbs_scores_step.outputs.output,
        tag='SAT',
        input_source_filter='WHERE Type == 2',
    )
    sat_click_inline_metrics_calculation_step.outputs.all_segment_output.configure(
        mode='mount',
    )
    sat_click_inline_metrics_calculation_step.outputs.query_level_output.configure(
        mode='mount',
    )
    sat_click_inline_metrics_calculation_step.outputs.market_output.configure(
        mode='mount',
    )
    sat_click_inline_metrics_calculation_step.outputs.combined_all_ouput.configure(
        mode='mount',
    )
    sat_click_inline_metrics_calculation_step.outputs.outputheader.configure(
        mode='mount',
    )

    qb_inline_metrics_calculation_step = inline_metrics_calculation_func(
        input=produce_sbs_scores_step.outputs.output,
        tag='QB',
        input_source_filter='WHERE Type == 1',
    )
    qb_inline_metrics_calculation_step.outputs.all_segment_output.configure(
        mode='mount',
    )
    qb_inline_metrics_calculation_step.outputs.query_level_output.configure(
        mode='mount',
    )
    qb_inline_metrics_calculation_step.outputs.market_output.configure(
        mode='mount',
    )
    qb_inline_metrics_calculation_step.outputs.combined_all_ouput.configure(
        mode='mount',
    )
    qb_inline_metrics_calculation_step.outputs.outputheader.configure(
        mode='mount',
    )

    sqb_inline_metric_calculation_step = inline_metrics_calculation_func(
        input=produce_sbs_scores_step.outputs.output,
        tag='SQB',
        input_source_filter='WHERE Type == 0',
    )
    sqb_inline_metric_calculation_step.outputs.all_segment_output.configure(
        mode='mount',
    )
    sqb_inline_metric_calculation_step.outputs.query_level_output.configure(
        mode='mount',
    )
    sqb_inline_metric_calculation_step.outputs.market_output.configure(
        mode='mount',
    )
    sqb_inline_metric_calculation_step.outputs.combined_all_ouput.configure(
        mode='mount',
    )
    sqb_inline_metric_calculation_step.outputs.outputheader.configure(
        mode='mount',
    )

    merge_all_step = concatenategenerictsvwithnewline_file_func(
        input1=all_inline_metrics_calculation_step.outputs.outputheader,
        input2=all_inline_metrics_calculation_step.outputs.all_segment_output,
        input3=sat_click_inline_metrics_calculation_step.outputs.all_segment_output,
        input4=qb_inline_metrics_calculation_step.outputs.all_segment_output,
        input5=sqb_inline_metric_calculation_step.outputs.all_segment_output,
        input6=all_inline_metrics_calculation_step.outputs.query_level_output,
        input7=all_inline_metrics_calculation_step.outputs.market_output,
    )
    merge_all_step.outputs.output.configure(
        mode='mount',
    )

    metric_summary_to_html_step = output_html_for_inline_metrics_func(
        input=merge_all_step.outputs.output,
    )
    metric_summary_to_html_step.outputs.output.configure(
        mode='mount',
    )

    return {
        'metric_summary': merge_all_step.outputs.output,
        'metric_summary_html': metric_summary_to_html_step.outputs.output,
    }
