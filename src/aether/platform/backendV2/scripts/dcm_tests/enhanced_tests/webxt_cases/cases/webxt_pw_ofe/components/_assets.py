# THIS IS AN AUTO GENERATED FILE.
# PLEASE DO NOT MODIFY MANUALLY.
# Components included in this generated file:
#  - add_freeforms_to_ur_freeform::0.4
#  - add_line_number::0.1
#  - add_remove_sort_dedup::0.1
#  - all_feature_coverages::0.2
#  - append_mebraw_processed_feature::0.0.0.6
#  - as_online_ranker_gen_fastrank_to_code::1.0
#  - assert_equal_byte_by_byte::0.1
#  - bigextractions_append_ranker_scores_partitioned_support_minscore_maxscore_cutoff::1.1
#  - bigextractions_rename_merge_and_slim_metastream_and_features_partitioned_and_output_test_data::1.6.6
#  - calculate_diffatclicklabel::0.0.0
#  - calculate_rmse::0.1
#  - cat_sort_and_unique::0.0.1
#  - compute_onedcg_output_html_with_markets::0.2
#  - concat_files_in_directory::0.1
#  - concatenategenerictsvwithnewline::0.1
#  - concatenategenerictsvwithnewline_file::0.1
#  - conditional_passthrough::0.1
#  - convert_lightgbm_model_to_bing_ini::0.1
#  - convert_sstream_to_tsv_with_header::0.1
#  - convert_to_libsvm_format::0.6
#  - countlinesoncosmos::1.1
#  - create_dataset::0.0.1
#  - create_generic_tsv_from_string_list::0.0.1
#  - create_pairwise_delta_freeforms::0.3
#  - create_pairwise_feature_names::0.0.0
#  - create_ranker_zip_archive_from_freeforms::0.3
#  - createfrinputini::0.1
#  - custom_diff::0.0.1
#  - esample_deterministic::0.2
#  - extract_all_features_ffv1_ffv2::1.2
#  - extract_feature_importance::1.1
#  - extraction_feature_distribution::0.0.1
#  - extractrankerfeatures::1.0
#  - fastrank_create_feature_input::0.1
#  - generate_fp_pairwise_extraction::0.0.0.0
#  - inline_metrics_calculation::0.0.1
#  - join_feature_score::0.47
#  - join_ranklm_score_on_position_0_25::*******
#  - lightgbm_inference::0.0.1
#  - lightgbm_training::0.0.1
#  - log_blob_data::0.2
#  - merge_ini::0.1
#  - merge_partition_chunks_and_header_to_sstream::0.4
#  - merge_two_files_by_column::0.1
#  - output_html_for_inline_metrics::*******
#  - prepare_onedcg_data::0.1
#  - pw_feature_processing::3.0
#  - pw_feature_renaming::0.1
#  - remove_feature_prefix::1.0
#  - replace_regex_string_file::0.0.1
#  - replace_regex_string_folder::0.0.1
#  - scope_replace_strings_no_comma::0.0.1
#  - smooth_training_ini::0.0.0
#  - ss_to_generic_tsv_base64::0.0.0.6
#  - streamprocessor_ss_ss::0.5
#  - streamprocessor_tsv_tsv::0.5
#  - unified_ofe_post_process::0.33
#  - unified_ranker_chashcodetol3codeforpwml_with_delta_freeform_as_input_5::0.5
#  - unified_ranker_generate_pairwise_extraction::2.1.8
#  - unified_ranker_generate_pairwise_extraction_for_evaluation::2.7.8
#  - unified_ranker_replace_delta_freeforms_with_place_holders::0.4
from pathlib import Path
from enum import Enum
from typing import Any as Data, Union

from azure.ml.component import Component
from azure.ml.component.component import Input, Output
from azure.ml.component.dsl._component_loader import ComponentLoader


component_loader = ComponentLoader(Path(__file__).parent / "components.yaml")


class _CommandComponentRunsettingDockerConfiguration:
    """resource section controls the number of nodes, cpus, gpus, docker configurations the job will consume."""
    arguments: Union[str, list]
    """Arguments to pass to the Docker run command."""
    shared_volumes: bool
    """Indicates whether to use shared volumes. Set to False if necessary to work around shared volume bugs on Windows. The default is True."""
    shm_size: str
    """The size of the docker container's shared memory block. This should be in the format of `<number><unit>` where number as to be greater than 0 and the unit can be one of `b` (bytes), `k` (kilobytes), `m` (megabytes), or `g` (gigabytes)."""
    use_docker: bool
    """Specifies whether the environment to run the experiment should be Docker-based. Amlcompute linux clusters require that jobs running inside Docker containers. The backend will override the value to be true for Amlcompute linux clusters."""


class _CommandComponentRunsettingEnvironment:
    """Environment section set runtime environment."""
    conda: str
    """Defines conda dependencies"""
    docker: str
    """Defines settings to customize the Docker image built to the environment's specifications."""
    environment_name: str
    """Curated environment name"""
    environment_version: str
    """Curated environment version"""
    os: str
    """Defines the operating system the component running on. Could be Windows or Linux. Defaults to Linux if not specified. (enum: ['Windows', 'Linux'])"""


class _CommandComponentRunsettingResourceLayout:
    """resource section controls the number of nodes, cpus, gpus, docker configurations the job will consume."""
    instance_count: int
    """Number of instances in the compute target used for training. (min: 1)"""
    instance_type: str
    """Instance type used for training."""
    node_count: int
    """Number of nodes in the compute target used for training. (min: 1)"""


class _CommandComponentRunsettingEarlyTermination:
    """This section contains specific early termination settings for sweep component."""
    delay_evaluation: int
    """delays the first policy evaluation for a specified number of intervals."""
    evaluation_interval: int
    """the frequency for applying the policy."""
    policy_type: str
    """The early termination policy type. Current default means no termination policy. (enum: ['default', 'bandit', 'median_stopping', 'truncation_selection'])"""
    slack_amount: float
    """the slack amount allowed with respect to the best performing training run."""
    slack_factor: float
    """the slack ratio allowed with respect to the best performing training run."""
    truncation_percentage: int
    """the percentage of lowest performing runs to terminate at each evaluation interval. An integer value between 1 and 99. (min: 1, max: 99)"""


class _CommandComponentRunsettingLimits:
    """This section contains specific limits settings for sweep component."""
    max_concurrent_trials: int
    """Maximum number of runs that can run concurrently. If not specified, all runs launch in parallel. If specified, must be an integer between 1 and 100. (min: 1, max: 100)"""
    max_total_trials: int
    """Maximum number of training runs. Must be an integer between 1 and 1000. (min: 1, max: 1000)"""
    sweep_timeout_seconds: int
    """The maximum time in seconds the entire sweep job is allowed to run. Once this limit is reached, the system will cancel the sweep job, including all its trials. (min: 0)"""
    timeout_minutes: int
    """Maximum duration, in minutes, of the hyperparameter tuning experiment. Runs after this duration are canceled. (min: 0)"""


class _CommandComponentRunsettingObjective:
    """This section contains specific objective settings for sweep component."""
    goal: str
    """Whether the primary metric will be maximized or minimized when evaluating the runs. (enum: ['minimize', 'maximize'])"""
    primary_metric: str
    """The name of the primary metric needs to exactly match the name of the metric logged by the training script."""


class _CommandComponentRunsettingSweep:
    """This section contains specific settings for sweep component."""
    algorithm: str
    """Specify the parameter sampling method to use over the hyperparameter search space. Possible values are [random, grid, bayesian]. (enum: ['grid', 'random', 'bayesian'])"""
    enabled: bool
    """Enable runtime sweep configuration."""
    early_termination: _CommandComponentRunsettingEarlyTermination
    """This section contains specific early termination settings for sweep component."""
    limits: _CommandComponentRunsettingLimits
    """This section contains specific limits settings for sweep component."""
    objective: _CommandComponentRunsettingObjective
    """This section contains specific objective settings for sweep component."""


class _CommandComponentRunsettingTargetSelector:
    """Specify desired target properties, instead of specifying a cluster name. When target is set, target_selector will be ignored."""
    allow_spot_vm: bool
    """Flag to enable target selector service to send job to low priority VM. Currently it only works for AmlK8s."""
    cluster_block_list: Union[str, list]
    """User specified block list of Cluster."""
    compute_type: str
    """Compute type that target selector could route job to. (enum: ['AmlCompute', 'AmlK8s'])"""
    instance_types: Union[str, list]
    """List of instance_type that job could use. If no instance_type specified, all sizes are allowed."""
    my_resource_only: bool
    """Flag to control whether the job should be sent to the cluster owned by user. If False, target selector may send the job to shared cluster. Currently it only works for AmlK8s."""
    regions: Union[str, list]
    """List of region that would like to submit job to. If no region specified, all regions are allowed."""
    vc_block_list: Union[str, list]
    """User specified block list of VC."""


class _CommandComponentRunsetting:
    """Run setting configuration for CommandComponent"""
    aisupercomputer: str
    """The configure details of the compute target \"aisupercomputer\" to be created during experiment."""
    environment_variables: Union[str, dict]
    """Environment variables can be used to specify environment variables to be passed. It is a dictionary of environment name to environment value mapping. User can use this to adjust some component runtime behavior which is not exposed as component parameter, e.g. enable some debug switch."""
    identity_client_id: str
    """ClientId of the managed identity."""
    identity_msi_resource_id: str
    """ResourceId of the managed identity."""
    identity_object_id: str
    """ObjectId(PrincipalId) of the managed identity."""
    identity_type: str
    """Type of the identity. (enum: ['UserIdentity', 'Managed', 'AMLToken'])"""
    priority: int
    """The priority of a job which is a integer. For AmlK8s Compute, User can set it to 100~200. Any value larger than 200 or less than 100 will be treated as 200. For Aml Compute, User can set it to 1~1000. Any value larger than 1000 or less than 1 will be treated as 1000."""
    target: str
    """The compute target to use"""
    timeout_seconds: int
    """Maximum allowed time for the run. The system will attempt to automatically cancel the run if it took longer than this value. Null value means infinite duration."""
    trial_timeout_seconds: int
    """The maximum time in seconds the entire sweep job is allowed to run. Once this limit is reached, the system will cancel the sweep job, including all its trials."""
    docker_configuration: _CommandComponentRunsettingDockerConfiguration
    """resource section controls the number of nodes, cpus, gpus, docker configurations the job will consume."""
    environment: _CommandComponentRunsettingEnvironment
    """Environment section set runtime environment."""
    resource_layout: _CommandComponentRunsettingResourceLayout
    """resource section controls the number of nodes, cpus, gpus, docker configurations the job will consume."""
    sweep: _CommandComponentRunsettingSweep
    """This section contains specific settings for sweep component."""
    target_selector: _CommandComponentRunsettingTargetSelector
    """Specify desired target properties, instead of specifying a cluster name. When target is set, target_selector will be ignored."""


class _AddFreeformsToUrFreeformFuncInput:
    input: Input = None
    """AnyDirectory"""
    freeforms: Input = None
    """AnyDirectory"""
    renaming: Input = None
    """AnyDirectory(optional)"""
    freeform_prefix: str = 'AffinityFreeForm'
    """string"""


class _AddFreeformsToUrFreeformFuncOutput:
    output: Output = None
    """AnyDirectory"""


class _AddFreeformsToUrFreeformFuncComponent(Component):
    inputs: _AddFreeformsToUrFreeformFuncInput
    outputs: _AddFreeformsToUrFreeformFuncOutput
    runsettings: _CommandComponentRunsetting


def add_freeforms_to_ur_freeform_func(
    input: Data = None,
    freeforms: Data = None,
    renaming: Data = None,
    freeform_prefix: str = 'AffinityFreeForm',
) -> _AddFreeformsToUrFreeformFuncComponent:
    """Created from Aether client version: ********, module id:b96c3e2c-0c24-4b55-90cc-fe1f03307180, module name:Add FreeForms to UR Freeform and module description Add FreeForms to UR Freeform
    
    Args:
        input (Data): AnyDirectory
        freeforms (Data): AnyDirectory
        renaming (Data): AnyDirectory(optional)
        freeform_prefix (str): string
    
    Returns:
        _AddFreeformsToUrFreeformFuncComponent: A component object. This component contains the following output ports:
    
            output (Output): AnyDirectory\n
    """
    _add_freeforms_to_ur_freeform_func = component_loader.load_component(name='add_freeforms_to_ur_freeform_func')
    return _add_freeforms_to_ur_freeform_func(
            input=input,
            freeforms=freeforms,
            renaming=renaming,
            freeform_prefix=freeform_prefix,)


class _AddLineNumberFuncInput:
    input: Input = None
    """AnyDirectory"""


class _AddLineNumberFuncOutput:
    output: Output = None
    """AnyDirectory"""


class _AddLineNumberFuncComponent(Component):
    inputs: _AddLineNumberFuncInput
    outputs: _AddLineNumberFuncOutput
    runsettings: _CommandComponentRunsetting


def add_line_number_func(
    input: Data = None,
) -> _AddLineNumberFuncComponent:
    """Add line number before a line: Line{num}=...
    
    Args:
        input (Data): AnyDirectory
    
    Returns:
        _AddLineNumberFuncComponent: A component object. This component contains the following output ports:
    
            output (Output): AnyDirectory\n
    """
    _add_line_number_func = component_loader.load_component(name='add_line_number_func')
    return _add_line_number_func(
            input=input,)


class _AddRemoveSortDedupFuncMatchModeEnum(Enum):
    search = 'search'
    match = 'match'
    fullmatch = 'fullmatch'


class _AddRemoveSortDedupFuncInput:
    input: Input = None
    """AnyDirectory"""
    input1: Input = None
    """A text file to add to input file(optional)"""
    input2: Input = None
    """Second text file to add to input file(optional)"""
    add: str = None
    """Json format string of line list to add to input file (optional)"""
    remove: str = None
    """Json format string of line/regex list to remove from input file (optional)"""
    match_mode: _AddRemoveSortDedupFuncMatchModeEnum = _AddRemoveSortDedupFuncMatchModeEnum.search
    """Pattern match mode for remove list (enum: ['search', 'match', 'fullmatch'])"""
    sort: bool = True
    """Whether to sort lines"""
    custom_order: str = None
    """Json format string of line list keep the exact order (optional)"""
    dedup: bool = True
    """Whether to dedup lines"""
    ignore_case: bool = False
    """Whether to ignore case for sort and dedup"""


class _AddRemoveSortDedupFuncOutput:
    output: Output = None
    """AnyDirectory"""


class _AddRemoveSortDedupFuncComponent(Component):
    inputs: _AddRemoveSortDedupFuncInput
    outputs: _AddRemoveSortDedupFuncOutput
    runsettings: _CommandComponentRunsetting


def add_remove_sort_dedup_func(
    input: Data = None,
    input1: Data = None,
    input2: Data = None,
    add: str = None,
    remove: str = None,
    match_mode: _AddRemoveSortDedupFuncMatchModeEnum = _AddRemoveSortDedupFuncMatchModeEnum.search,
    sort: bool = True,
    custom_order: str = None,
    dedup: bool = True,
    ignore_case: bool = False,
) -> _AddRemoveSortDedupFuncComponent:
    """This component does the following in order, skipping any step that does not apply (empty lines are skipped). 0. Load input file content 1. Load input1 file content 2. Add lines to input file 3. Remove lines from input file 4. Sort lines 5. Dedup lines
    
    Args:
        input (Data): AnyDirectory
        input1 (Data): A text file to add to input file(optional)
        input2 (Data): Second text file to add to input file(optional)
        add (str): Json format string of line list to add to input file (optional)
        remove (str): Json format string of line/regex list to remove from input file (optional)
        match_mode (_AddRemoveSortDedupFuncMatchModeEnum): Pattern match mode for remove list (enum: ['search', 'match', 'fullmatch'])
        sort (bool): Whether to sort lines
        custom_order (str): Json format string of line list keep the exact order (optional)
        dedup (bool): Whether to dedup lines
        ignore_case (bool): Whether to ignore case for sort and dedup
    
    Returns:
        _AddRemoveSortDedupFuncComponent: A component object. This component contains the following output ports:
    
            output (Output): AnyDirectory\n
    """
    _add_remove_sort_dedup_func = component_loader.load_component(name='add_remove_sort_dedup_func')
    return _add_remove_sort_dedup_func(
            input=input,
            input1=input1,
            input2=input2,
            add=add,
            remove=remove,
            match_mode=match_mode,
            sort=sort,
            custom_order=custom_order,
            dedup=dedup,
            ignore_case=ignore_case,)


class _AllFeatureCoveragesFuncInput:
    input_samples: Input = None
    """AnyDirectory"""
    features_header: Input = None
    """AnyDirectory"""
    control_ranker_features: Input = None
    """AnyDirectory"""
    treatment_ranker_features: Input = None
    """AnyDirectory"""


class _AllFeatureCoveragesFuncOutput:
    samples_feature_coverage: Output = None
    """AnyDirectory"""
    samples_control_ranker_feature_coverage: Output = None
    """AnyDirectory"""
    samples_treatment_ranker_feature_coverage: Output = None
    """AnyDirectory"""


class _AllFeatureCoveragesFuncComponent(Component):
    inputs: _AllFeatureCoveragesFuncInput
    outputs: _AllFeatureCoveragesFuncOutput
    runsettings: _CommandComponentRunsetting


def all_feature_coverages_func(
    input_samples: Data = None,
    features_header: Data = None,
    control_ranker_features: Data = None,
    treatment_ranker_features: Data = None,
) -> _AllFeatureCoveragesFuncComponent:
    """Rewrite from Aether client version: ********, module id:18fd973f-8109-476c-a25f-371696489b2f, module name:All Feature Coverages and module description Calculates coverage of all features in the input extraction file. (change from ipy to python)
    
    Args:
        input_samples (Data): AnyDirectory
        features_header (Data): AnyDirectory
        control_ranker_features (Data): AnyDirectory
        treatment_ranker_features (Data): AnyDirectory
    
    Returns:
        _AllFeatureCoveragesFuncComponent: A component object. This component contains the following output ports:
    
            samples_feature_coverage (Output): AnyDirectory\n
            samples_control_ranker_feature_coverage (Output): AnyDirectory\n
            samples_treatment_ranker_feature_coverage (Output): AnyDirectory\n
    """
    _all_feature_coverages_func = component_loader.load_component(name='all_feature_coverages_func')
    return _all_feature_coverages_func(
            input_samples=input_samples,
            features_header=features_header,
            control_ranker_features=control_ranker_features,
            treatment_ranker_features=treatment_ranker_features,)


class _AppendMebrawProcessedFeatureFuncInput:
    headerpath: Input = None
    """AnyDirectory"""
    extraction: Input = None
    """AnyDirectory"""
    numoutputchunks: int = 1000
    """integer"""


class _AppendMebrawProcessedFeatureFuncOutput:
    header: Output = None
    """AnyDirectory"""
    partitions: Output = None
    """AnyDirectory"""


class _AppendMebrawProcessedFeatureFuncComponent(Component):
    inputs: _AppendMebrawProcessedFeatureFuncInput
    outputs: _AppendMebrawProcessedFeatureFuncOutput
    runsettings: _CommandComponentRunsetting


def append_mebraw_processed_feature_func(
    headerpath: Data = None,
    extraction: Data = None,
    numoutputchunks: int = 1000,
) -> _AppendMebrawProcessedFeatureFuncComponent:
    """Created from Aether client version: ********, module id:15c520a8-4e19-49b7-a5bd-3951d6dee8f9, module name:append MebRaw processed feature and module description append MebRaw processed feature
    
    Args:
        headerpath (Data): AnyDirectory
        extraction (Data): AnyDirectory
        numoutputchunks (int): integer
    
    Returns:
        _AppendMebrawProcessedFeatureFuncComponent: A component object. This component contains the following output ports:
    
            header (Output): AnyDirectory\n
            partitions (Output): AnyDirectory\n
    """
    _append_mebraw_processed_feature_func = component_loader.load_component(name='append_mebraw_processed_feature_func')
    return _append_mebraw_processed_feature_func(
            headerpath=headerpath,
            extraction=extraction,
            numoutputchunks=numoutputchunks,)


class _AsOnlineRankerGenFastrankToCodeFuncInput:
    inputini: Input = None
    """AnyDirectory"""


class _AsOnlineRankerGenFastrankToCodeFuncOutput:
    code_file: Output = None
    """AnyDirectory"""
    features_list: Output = None
    """AnyDirectory"""


class _AsOnlineRankerGenFastrankToCodeFuncComponent(Component):
    inputs: _AsOnlineRankerGenFastrankToCodeFuncInput
    outputs: _AsOnlineRankerGenFastrankToCodeFuncOutput
    runsettings: _CommandComponentRunsetting


def as_online_ranker_gen_fastrank_to_code_func(
    inputini: Data = None,
) -> _AsOnlineRankerGenFastrankToCodeFuncComponent:
    """Created from Aether client version: ********, module id:a4e8f944-091d-442c-afcc-8ac302ee53ec, module name:[AS Online Ranker Gen] FastRank to Code and module description Takes an ensemble of decission trees and generates code. IT DOESN'T WORK WITH AGGREGATES AND FREEFORMS!
    
    Args:
        inputini (Data): AnyDirectory
    
    Returns:
        _AsOnlineRankerGenFastrankToCodeFuncComponent: A component object. This component contains the following output ports:
    
            code_file (Output): AnyDirectory\n
            features_list (Output): AnyDirectory\n
    """
    _as_online_ranker_gen_fastrank_to_code_func = component_loader.load_component(name='as_online_ranker_gen_fastrank_to_code_func')
    return _as_online_ranker_gen_fastrank_to_code_func(
            inputini=inputini,)


class _AssertEqualByteByByteFuncInput:
    input1: Input = None
    """['AnyDirectory', 'AnyDirectory']"""
    input2: Input = None
    """['AnyDirectory', 'AnyDirectory']"""


class _AssertEqualByteByByteFuncOutput:
    output: Output = None
    """AnyDirectory"""


class _AssertEqualByteByByteFuncComponent(Component):
    inputs: _AssertEqualByteByByteFuncInput
    outputs: _AssertEqualByteByByteFuncOutput
    runsettings: _CommandComponentRunsetting


def assert_equal_byte_by_byte_func(
    input1: Data = None,
    input2: Data = None,
) -> _AssertEqualByteByByteFuncComponent:
    """assert_equal_byte_by_byte
    
    Args:
        input1 (Data): ['AnyDirectory', 'AnyDirectory']
        input2 (Data): ['AnyDirectory', 'AnyDirectory']
    
    Returns:
        _AssertEqualByteByByteFuncComponent: A component object. This component contains the following output ports:
    
            output (Output): AnyDirectory\n
    """
    _assert_equal_byte_by_byte_func = component_loader.load_component(name='assert_equal_byte_by_byte_func')
    return _assert_equal_byte_by_byte_func(
            input1=input1,
            input2=input2,)


class _BigextractionsAppendRankerScoresPartitionedSupportMinscoreMaxscoreCutoffFuncInput:
    input_header: Input = None
    """AnyDirectory"""
    input_partitions: Input = None
    """AnyDirectory"""
    seedrankers: Input = None
    """AnyDirectory"""
    partitions_is_folder: str = 'true'
    """string"""
    columnname: str = 'm:RankScore'
    """string"""
    scalingfactor: int = 10000
    """integer"""
    translationfactor: int = 100
    """integer"""
    minscore: int = -100
    """integer"""
    maxscore: int = 100
    """integer"""
    numoutputchunks: int = 1000
    """integer"""


class _BigextractionsAppendRankerScoresPartitionedSupportMinscoreMaxscoreCutoffFuncOutput:
    header: Output = None
    """AnyDirectory"""
    partitions: Output = None
    """AnyDirectory"""


class _BigextractionsAppendRankerScoresPartitionedSupportMinscoreMaxscoreCutoffFuncComponent(Component):
    inputs: _BigextractionsAppendRankerScoresPartitionedSupportMinscoreMaxscoreCutoffFuncInput
    outputs: _BigextractionsAppendRankerScoresPartitionedSupportMinscoreMaxscoreCutoffFuncOutput
    runsettings: _CommandComponentRunsetting


def bigextractions_append_ranker_scores_partitioned_support_minscore_maxscore_cutoff_func(
    input_header: Data = None,
    input_partitions: Data = None,
    seedrankers: Data = None,
    partitions_is_folder: str = 'true',
    columnname: str = 'm:RankScore',
    scalingfactor: int = 10000,
    translationfactor: int = 100,
    minscore: int = -100,
    maxscore: int = 100,
    numoutputchunks: int = 1000,
) -> _BigextractionsAppendRankerScoresPartitionedSupportMinscoreMaxscoreCutoffFuncComponent:
    """Created from Aether client version: ********, module id:b7151a17-334c-450f-be48-cfa229e361d4, module name:[BigExtractions] Append Ranker Scores (Partitioned) [support MinScore/MaxScore cutoff] and module description Append ranker scores for OFE extraction. Support scaling and translation. The new column name for ranker score should start with \"m:\".  Adds unzip.exe as an explicit resource (seems that some Cosmos machines no longer have it accessible by default)
    
    Args:
        input_header (Data): AnyDirectory
        input_partitions (Data): AnyDirectory
        seedrankers (Data): AnyDirectory
        partitions_is_folder (str): string
        columnname (str): string
        scalingfactor (int): integer
        translationfactor (int): integer
        minscore (int): integer
        maxscore (int): integer
        numoutputchunks (int): integer
    
    Returns:
        _BigextractionsAppendRankerScoresPartitionedSupportMinscoreMaxscoreCutoffFuncComponent: A component object. This component contains the following output ports:
    
            header (Output): AnyDirectory\n
            partitions (Output): AnyDirectory\n
    """
    _bigextractions_append_ranker_scores_partitioned_support_minscore_maxscore_cutoff_func = component_loader.load_component(name='bigextractions_append_ranker_scores_partitioned_support_minscore_maxscore_cutoff_func')
    return _bigextractions_append_ranker_scores_partitioned_support_minscore_maxscore_cutoff_func(
            input_header=input_header,
            input_partitions=input_partitions,
            seedrankers=seedrankers,
            partitions_is_folder=partitions_is_folder,
            columnname=columnname,
            scalingfactor=scalingfactor,
            translationfactor=translationfactor,
            minscore=minscore,
            maxscore=maxscore,
            numoutputchunks=numoutputchunks,)


class _BigextractionsRenameMergeAndSlimMetastreamAndFeaturesPartitionedAndOutputTestDataFuncInput:
    mergespec: Input = None
    """AnyDirectory"""
    featurerenaming: Input = None
    """AnyDirectory"""
    inputini: Input = None
    """AnyDirectory"""
    input_header: Input = None
    """AnyDirectory"""
    input_partitions: Input = None
    """AnyDirectory"""
    numinputpartitions: int = 1000
    """integer"""
    numoutputpartitions: int = 1000
    """integer"""


class _BigextractionsRenameMergeAndSlimMetastreamAndFeaturesPartitionedAndOutputTestDataFuncOutput:
    header: Output = None
    """AnyDirectory"""
    partitions: Output = None
    """AnyDirectory"""
    testextraction: Output = None
    """AnyDirectory"""
    numrows: Output = None
    """AnyDirectory"""


class _BigextractionsRenameMergeAndSlimMetastreamAndFeaturesPartitionedAndOutputTestDataFuncComponent(Component):
    inputs: _BigextractionsRenameMergeAndSlimMetastreamAndFeaturesPartitionedAndOutputTestDataFuncInput
    outputs: _BigextractionsRenameMergeAndSlimMetastreamAndFeaturesPartitionedAndOutputTestDataFuncOutput
    runsettings: _CommandComponentRunsetting


def bigextractions_rename_merge_and_slim_metastream_and_features_partitioned_and_output_test_data_func(
    mergespec: Data = None,
    featurerenaming: Data = None,
    inputini: Data = None,
    input_header: Data = None,
    input_partitions: Data = None,
    numinputpartitions: int = 1000,
    numoutputpartitions: int = 1000,
) -> _BigextractionsRenameMergeAndSlimMetastreamAndFeaturesPartitionedAndOutputTestDataFuncComponent:
    """Created from Aether client version: ********, module id:97a36725-746b-4338-98bf-f7e2d122f0f6, module name:[BigExtractions] Rename Merge and Slim Metastream and Features (Partitioned and Output Test Data) and module description Rename and merge columns. The syntax of MergeSpec is X=A|B|C, Y=D|E, meaning columns A, B and C should be merged to produce column X (removing A, B, C) and D and E should merge to produce Y. If the renamed column already exists in the schema it will be merged. The syntax of FeatureRenaming is TargetFeature=SourceFeature per line.
    
    Args:
        mergespec (Data): AnyDirectory
        featurerenaming (Data): AnyDirectory
        inputini (Data): AnyDirectory
        input_header (Data): AnyDirectory
        input_partitions (Data): AnyDirectory
        numinputpartitions (int): integer
        numoutputpartitions (int): integer
    
    Returns:
        _BigextractionsRenameMergeAndSlimMetastreamAndFeaturesPartitionedAndOutputTestDataFuncComponent: A component object. This component contains the following output ports:
    
            header (Output): AnyDirectory\n
            partitions (Output): AnyDirectory\n
            testextraction (Output): AnyDirectory\n
            numrows (Output): AnyDirectory\n
    """
    _bigextractions_rename_merge_and_slim_metastream_and_features_partitioned_and_output_test_data_func = component_loader.load_component(name='bigextractions_rename_merge_and_slim_metastream_and_features_partitioned_and_output_test_data_func')
    return _bigextractions_rename_merge_and_slim_metastream_and_features_partitioned_and_output_test_data_func(
            mergespec=mergespec,
            featurerenaming=featurerenaming,
            inputini=inputini,
            input_header=input_header,
            input_partitions=input_partitions,
            numinputpartitions=numinputpartitions,
            numoutputpartitions=numoutputpartitions,)


class _CalculateDiffatclicklabelFuncInput:
    input: Input = None
    """path"""


class _CalculateDiffatclicklabelFuncOutput:
    output: Output = None
    """path"""


class _CalculateDiffatclicklabelFuncComponent(Component):
    inputs: _CalculateDiffatclicklabelFuncInput
    outputs: _CalculateDiffatclicklabelFuncOutput
    runsettings: _CommandComponentRunsetting


def calculate_diffatclicklabel_func(
    input: Data = None,
) -> _CalculateDiffatclicklabelFuncComponent:
    """Created from Aether client version: ********, module id:184a01a3-cbfe-4f4e-8bba-754ad19fe315, module name:Calculate DiffAtClickLabel and module description Calculate DiffAtClickLabel Calculate DiffAtClickLabel
    
    Args:
        input (Data): path
    
    Returns:
        _CalculateDiffatclicklabelFuncComponent: A component object. This component contains the following output ports:
    
            output (Output): path\n
    """
    _calculate_diffatclicklabel_func = component_loader.load_component(name='calculate_diffatclicklabel_func')
    return _calculate_diffatclicklabel_func(
            input=input,)


class _CalculateRmseFuncInput:
    predicts_folder: Input = None
    """Input folder containing predicts"""
    ratings_folder: Input = None
    """Input folder containing raitings"""
    predict_file_pattern: str = 'File_*.txt'
    """Filename pattern for predicts"""
    rating_file_pattern: str = 'DocInfo_*.txt'
    """Filename pattern for ratings"""


class _CalculateRmseFuncOutput:
    output: Output = None
    """AnyDirectory"""


class _CalculateRmseFuncComponent(Component):
    inputs: _CalculateRmseFuncInput
    outputs: _CalculateRmseFuncOutput
    runsettings: _CommandComponentRunsetting


def calculate_rmse_func(
    predicts_folder: Data = None,
    ratings_folder: Data = None,
    predict_file_pattern: str = 'File_*.txt',
    rating_file_pattern: str = 'DocInfo_*.txt',
) -> _CalculateRmseFuncComponent:
    """Given predicts and ratings, Calculate RMSE
    
    Args:
        predicts_folder (Data): Input folder containing predicts
        ratings_folder (Data): Input folder containing raitings
        predict_file_pattern (str): Filename pattern for predicts
        rating_file_pattern (str): Filename pattern for ratings
    
    Returns:
        _CalculateRmseFuncComponent: A component object. This component contains the following output ports:
    
            output (Output): AnyDirectory\n
    """
    _calculate_rmse_func = component_loader.load_component(name='calculate_rmse_func')
    return _calculate_rmse_func(
            predicts_folder=predicts_folder,
            ratings_folder=ratings_folder,
            predict_file_pattern=predict_file_pattern,
            rating_file_pattern=rating_file_pattern,)


class _CatSortAndUniqueFuncInput:
    input1: Input = None
    """path"""
    input2: Input = None
    """path(optional)"""
    input3: Input = None
    """path(optional)"""
    input4: Input = None
    """path(optional)"""
    input5: Input = None
    """path(optional)"""


class _CatSortAndUniqueFuncOutput:
    output: Output = None
    """path"""


class _CatSortAndUniqueFuncComponent(Component):
    inputs: _CatSortAndUniqueFuncInput
    outputs: _CatSortAndUniqueFuncOutput
    runsettings: _CommandComponentRunsetting


def cat_sort_and_unique_func(
    input1: Data = None,
    input2: Data = None,
    input3: Data = None,
    input4: Data = None,
    input5: Data = None,
) -> _CatSortAndUniqueFuncComponent:
    """Concatenate text files, sort them and make them unique
    
    Args:
        input1 (Data): path
        input2 (Data): path(optional)
        input3 (Data): path(optional)
        input4 (Data): path(optional)
        input5 (Data): path(optional)
    
    Returns:
        _CatSortAndUniqueFuncComponent: A component object. This component contains the following output ports:
    
            output (Output): path\n
    """
    _cat_sort_and_unique_func = component_loader.load_component(name='cat_sort_and_unique_func')
    return _cat_sort_and_unique_func(
            input1=input1,
            input2=input2,
            input3=input3,
            input4=input4,
            input5=input5,)


class _ComputeOnedcgOutputHtmlWithMarketsFuncInput:
    input: Input = None
    """path"""
    header: str = None
    """string"""
    table_name: str = '"Metrics Summary"'
    """string"""


class _ComputeOnedcgOutputHtmlWithMarketsFuncOutput:
    metric_summary_html: Output = None
    """path"""
    sorted_by_losses: Output = None
    """path"""
    sorted_by_wins: Output = None
    """path"""


class _ComputeOnedcgOutputHtmlWithMarketsFuncComponent(Component):
    inputs: _ComputeOnedcgOutputHtmlWithMarketsFuncInput
    outputs: _ComputeOnedcgOutputHtmlWithMarketsFuncOutput
    runsettings: _CommandComponentRunsetting


def compute_onedcg_output_html_with_markets_func(
    input: Data = None,
    header: str = None,
    table_name: str = '"Metrics Summary"',
) -> _ComputeOnedcgOutputHtmlWithMarketsFuncComponent:
    """Created from Aether client version: ********, module id:5d668624-7016-4129-8860-ed4cdebb5d5d, module name:compute onedcg output html with markets and module description compute onedcg output html add in markets
    
    Args:
        input (Data): path
        header (str): string
        table_name (str): string
    
    Returns:
        _ComputeOnedcgOutputHtmlWithMarketsFuncComponent: A component object. This component contains the following output ports:
    
            metric_summary_html (Output): path\n
            sorted_by_losses (Output): path\n
            sorted_by_wins (Output): path\n
    """
    _compute_onedcg_output_html_with_markets_func = component_loader.load_component(name='compute_onedcg_output_html_with_markets_func')
    return _compute_onedcg_output_html_with_markets_func(
            input=input,
            header=header,
            table_name=table_name,)


class _ConcatFilesInDirectoryFuncInput:
    input: Input = None
    """AnyDirectory"""
    remove_header: bool = False
    """boolean"""


class _ConcatFilesInDirectoryFuncOutput:
    output: Output = None
    """AnyDirectory"""


class _ConcatFilesInDirectoryFuncComponent(Component):
    inputs: _ConcatFilesInDirectoryFuncInput
    outputs: _ConcatFilesInDirectoryFuncOutput
    runsettings: _CommandComponentRunsetting


def concat_files_in_directory_func(
    input: Data = None,
    remove_header: bool = False,
) -> _ConcatFilesInDirectoryFuncComponent:
    """Concat all files in a directory
    
    Args:
        input (Data): AnyDirectory
        remove_header (bool): boolean
    
    Returns:
        _ConcatFilesInDirectoryFuncComponent: A component object. This component contains the following output ports:
    
            output (Output): AnyDirectory\n
    """
    _concat_files_in_directory_func = component_loader.load_component(name='concat_files_in_directory_func')
    return _concat_files_in_directory_func(
            input=input,
            remove_header=remove_header,)


class _ConcatenategenerictsvwithnewlineFuncRemoveHeaderEnum(Enum):
    true = 'true'
    false = 'false'


class _ConcatenategenerictsvwithnewlineFuncInput:
    input1: Input = None
    """path"""
    input2: Input = None
    """path(optional)"""
    input3: Input = None
    """path(optional)"""
    input4: Input = None
    """path(optional)"""
    input5: Input = None
    """path(optional)"""
    input6: Input = None
    """path(optional)"""
    input7: Input = None
    """path(optional)"""
    input8: Input = None
    """path(optional)"""
    input9: Input = None
    """path(optional)"""
    input10: Input = None
    """path(optional)"""
    remove_header: _ConcatenategenerictsvwithnewlineFuncRemoveHeaderEnum = _ConcatenategenerictsvwithnewlineFuncRemoveHeaderEnum.false
    """enum (enum: ['true', 'false'])"""
    output_extension: str = None
    """string (optional)"""


class _ConcatenategenerictsvwithnewlineFuncOutput:
    output: Output = None
    """path"""


class _ConcatenategenerictsvwithnewlineFuncComponent(Component):
    inputs: _ConcatenategenerictsvwithnewlineFuncInput
    outputs: _ConcatenategenerictsvwithnewlineFuncOutput
    runsettings: _CommandComponentRunsetting


def concatenategenerictsvwithnewline_func(
    input1: Data = None,
    input2: Data = None,
    input3: Data = None,
    input4: Data = None,
    input5: Data = None,
    input6: Data = None,
    input7: Data = None,
    input8: Data = None,
    input9: Data = None,
    input10: Data = None,
    remove_header: _ConcatenategenerictsvwithnewlineFuncRemoveHeaderEnum = _ConcatenategenerictsvwithnewlineFuncRemoveHeaderEnum.false,
    output_extension: str = None,
) -> _ConcatenategenerictsvwithnewlineFuncComponent:
    """concatenategenerictsvwithnewline
    
    Args:
        input1 (Data): path
        input2 (Data): path(optional)
        input3 (Data): path(optional)
        input4 (Data): path(optional)
        input5 (Data): path(optional)
        input6 (Data): path(optional)
        input7 (Data): path(optional)
        input8 (Data): path(optional)
        input9 (Data): path(optional)
        input10 (Data): path(optional)
        remove_header (_ConcatenategenerictsvwithnewlineFuncRemoveHeaderEnum): enum (enum: ['true', 'false'])
        output_extension (str): string (optional)
    
    Returns:
        _ConcatenategenerictsvwithnewlineFuncComponent: A component object. This component contains the following output ports:
    
            output (Output): path\n
    """
    _concatenategenerictsvwithnewline_func = component_loader.load_component(name='concatenategenerictsvwithnewline_func')
    return _concatenategenerictsvwithnewline_func(
            input1=input1,
            input2=input2,
            input3=input3,
            input4=input4,
            input5=input5,
            input6=input6,
            input7=input7,
            input8=input8,
            input9=input9,
            input10=input10,
            remove_header=remove_header,
            output_extension=output_extension,)


class _ConcatenategenerictsvwithnewlineFileFuncRemoveHeaderEnum(Enum):
    true = 'true'
    false = 'false'


class _ConcatenategenerictsvwithnewlineFileFuncInput:
    input1: Input = None
    """path"""
    input2: Input = None
    """path(optional)"""
    input3: Input = None
    """path(optional)"""
    input4: Input = None
    """path(optional)"""
    input5: Input = None
    """path(optional)"""
    input6: Input = None
    """path(optional)"""
    input7: Input = None
    """path(optional)"""
    input8: Input = None
    """path(optional)"""
    input9: Input = None
    """path(optional)"""
    input10: Input = None
    """path(optional)"""
    remove_header: _ConcatenategenerictsvwithnewlineFileFuncRemoveHeaderEnum = _ConcatenategenerictsvwithnewlineFileFuncRemoveHeaderEnum.false
    """enum (enum: ['true', 'false'])"""
    output_extension: str = None
    """string (optional)"""


class _ConcatenategenerictsvwithnewlineFileFuncOutput:
    output: Output = None
    """path"""


class _ConcatenategenerictsvwithnewlineFileFuncComponent(Component):
    inputs: _ConcatenategenerictsvwithnewlineFileFuncInput
    outputs: _ConcatenategenerictsvwithnewlineFileFuncOutput
    runsettings: _CommandComponentRunsetting


def concatenategenerictsvwithnewline_file_func(
    input1: Data = None,
    input2: Data = None,
    input3: Data = None,
    input4: Data = None,
    input5: Data = None,
    input6: Data = None,
    input7: Data = None,
    input8: Data = None,
    input9: Data = None,
    input10: Data = None,
    remove_header: _ConcatenategenerictsvwithnewlineFileFuncRemoveHeaderEnum = _ConcatenategenerictsvwithnewlineFileFuncRemoveHeaderEnum.false,
    output_extension: str = None,
) -> _ConcatenategenerictsvwithnewlineFileFuncComponent:
    """concatenategenerictsvwithnewline_file
    
    Args:
        input1 (Data): path
        input2 (Data): path(optional)
        input3 (Data): path(optional)
        input4 (Data): path(optional)
        input5 (Data): path(optional)
        input6 (Data): path(optional)
        input7 (Data): path(optional)
        input8 (Data): path(optional)
        input9 (Data): path(optional)
        input10 (Data): path(optional)
        remove_header (_ConcatenategenerictsvwithnewlineFileFuncRemoveHeaderEnum): enum (enum: ['true', 'false'])
        output_extension (str): string (optional)
    
    Returns:
        _ConcatenategenerictsvwithnewlineFileFuncComponent: A component object. This component contains the following output ports:
    
            output (Output): path\n
    """
    _concatenategenerictsvwithnewline_file_func = component_loader.load_component(name='concatenategenerictsvwithnewline_file_func')
    return _concatenategenerictsvwithnewline_file_func(
            input1=input1,
            input2=input2,
            input3=input3,
            input4=input4,
            input5=input5,
            input6=input6,
            input7=input7,
            input8=input8,
            input9=input9,
            input10=input10,
            remove_header=remove_header,
            output_extension=output_extension,)


class _ConditionalPassthroughFuncInput:
    input: Input = None
    """['AnyDirectory', 'AnyDirectory']"""
    control: Input = None
    """['AnyDirectory', 'AnyDirectory']"""


class _ConditionalPassthroughFuncOutput:
    output: Output = None
    """AnyDirectory"""


class _ConditionalPassthroughFuncComponent(Component):
    inputs: _ConditionalPassthroughFuncInput
    outputs: _ConditionalPassthroughFuncOutput
    runsettings: _CommandComponentRunsetting


def conditional_passthrough_func(
    input: Data = None,
    control: Data = None,
) -> _ConditionalPassthroughFuncComponent:
    """Pass through input if control is true, otherwise fail
    
    Args:
        input (Data): ['AnyDirectory', 'AnyDirectory']
        control (Data): ['AnyDirectory', 'AnyDirectory']
    
    Returns:
        _ConditionalPassthroughFuncComponent: A component object. This component contains the following output ports:
    
            output (Output): AnyDirectory\n
    """
    _conditional_passthrough_func = component_loader.load_component(name='conditional_passthrough_func')
    return _conditional_passthrough_func(
            input=input,
            control=control,)


class _ConvertLightgbmModelToBingIniFuncInput:
    lightgbm_model: Input = None
    """AnyDirectory"""
    features_ini: Input = None
    """AnyDirectory"""
    offset: int = None
    """integer (optional)"""


class _ConvertLightgbmModelToBingIniFuncOutput:
    bing_ini: Output = None
    """AnyDirectory"""


class _ConvertLightgbmModelToBingIniFuncComponent(Component):
    inputs: _ConvertLightgbmModelToBingIniFuncInput
    outputs: _ConvertLightgbmModelToBingIniFuncOutput
    runsettings: _CommandComponentRunsetting


def convert_lightgbm_model_to_bing_ini_func(
    lightgbm_model: Data = None,
    features_ini: Data = None,
    offset: int = None,
) -> _ConvertLightgbmModelToBingIniFuncComponent:
    """convert_lightgbm_model_to_bing_ini
    
    Args:
        lightgbm_model (Data): AnyDirectory
        features_ini (Data): AnyDirectory
        offset (int): integer (optional)
    
    Returns:
        _ConvertLightgbmModelToBingIniFuncComponent: A component object. This component contains the following output ports:
    
            bing_ini (Output): AnyDirectory\n
    """
    _convert_lightgbm_model_to_bing_ini_func = component_loader.load_component(name='convert_lightgbm_model_to_bing_ini_func')
    return _convert_lightgbm_model_to_bing_ini_func(
            lightgbm_model=lightgbm_model,
            features_ini=features_ini,
            offset=offset,)


class _ConvertSstreamToTsvWithHeaderFuncInput:
    inputstream: Input = None
    """AnyDirectory"""


class _ConvertSstreamToTsvWithHeaderFuncOutput:
    outputheader: Output = None
    """AnyDirectory"""
    outputfile: Output = None
    """AnyDirectory"""


class _ConvertSstreamToTsvWithHeaderFuncComponent(Component):
    inputs: _ConvertSstreamToTsvWithHeaderFuncInput
    outputs: _ConvertSstreamToTsvWithHeaderFuncOutput
    runsettings: _CommandComponentRunsetting


def convert_sstream_to_tsv_with_header_func(
    inputstream: Data = None,
) -> _ConvertSstreamToTsvWithHeaderFuncComponent:
    """Created from Aether client version: ********, module id:093f6c87-f958-4207-bdd7-99acec3b8866, module name:Convert Sstream to TSV with Header and module description Convert Sstream to TSV with Header
    
    Args:
        inputstream (Data): AnyDirectory
    
    Returns:
        _ConvertSstreamToTsvWithHeaderFuncComponent: A component object. This component contains the following output ports:
    
            outputheader (Output): AnyDirectory\n
            outputfile (Output): AnyDirectory\n
    """
    _convert_sstream_to_tsv_with_header_func = component_loader.load_component(name='convert_sstream_to_tsv_with_header_func')
    return _convert_sstream_to_tsv_with_header_func(
            inputstream=inputstream,)


class _ConvertToLibsvmFormatFuncInput:
    extractionheader: Input = None
    """AnyDirectory"""
    partitionscontent: Input = None
    """AnyDirectory"""
    inputinifile: Input = None
    """AnyDirectory"""
    outputmetainfoheader: str = 'true'
    """string"""
    commasepextraselectcols: str = '"m:DocId"'
    """string"""
    extracols: str = '"m_DocId"'
    """string"""
    numpartitions: int = 1
    """integer"""
    labeltoratemapping: str = '"Label0:0|Label1:1|Label2:2|Label3:3|Label4:4|Label5:5|Label6:6|Label7:7|Label8:8|Label9:9|Label10:10|Label11:11|Label12:12|Label13:13|Label14:14|Label15:15|Label16:16|Label17:17|Label18:18|Label19:19|Label20:20"'
    """string"""
    runid: str = None
    """string"""


class _ConvertToLibsvmFormatFuncOutput:
    outputchunksfolder: Output = None
    """AnyDirectory"""
    metaoutputchunksfolder: Output = None
    """AnyDirectory"""


class _ConvertToLibsvmFormatFuncComponent(Component):
    inputs: _ConvertToLibsvmFormatFuncInput
    outputs: _ConvertToLibsvmFormatFuncOutput
    runsettings: _CommandComponentRunsetting


def convert_to_libsvm_format_func(
    extractionheader: Data = None,
    partitionscontent: Data = None,
    inputinifile: Data = None,
    outputmetainfoheader: str = 'true',
    commasepextraselectcols: str = '"m:DocId"',
    extracols: str = '"m_DocId"',
    numpartitions: int = 1,
    labeltoratemapping: str = '"Label0:0|Label1:1|Label2:2|Label3:3|Label4:4|Label5:5|Label6:6|Label7:7|Label8:8|Label9:9|Label10:10|Label11:11|Label12:12|Label13:13|Label14:14|Label15:15|Label16:16|Label17:17|Label18:18|Label19:19|Label20:20"',
    runid: str = None,
) -> _ConvertToLibsvmFormatFuncComponent:
    """Created from Aether client version: ********, module id:0347bf31-636f-44bf-8c36-30e5fe82f80d, module name:Convert To LibSVM Format (output custom cols) and module description Convert To LibSVM Format, custom cols, output custom cols to another folder add bool param outputMetaInfoHeader
    
    Args:
        extractionheader (Data): AnyDirectory
        partitionscontent (Data): AnyDirectory
        inputinifile (Data): AnyDirectory
        outputmetainfoheader (str): string
        commasepextraselectcols (str): string
        extracols (str): string
        numpartitions (int): integer
        labeltoratemapping (str): string
        runid (str): string
    
    Returns:
        _ConvertToLibsvmFormatFuncComponent: A component object. This component contains the following output ports:
    
            outputchunksfolder (Output): AnyDirectory\n
            metaoutputchunksfolder (Output): AnyDirectory\n
    """
    _convert_to_libsvm_format_func = component_loader.load_component(name='convert_to_libsvm_format_func')
    return _convert_to_libsvm_format_func(
            extractionheader=extractionheader,
            partitionscontent=partitionscontent,
            inputinifile=inputinifile,
            outputmetainfoheader=outputmetainfoheader,
            commasepextraselectcols=commasepextraselectcols,
            extracols=extracols,
            numpartitions=numpartitions,
            labeltoratemapping=labeltoratemapping,
            runid=runid,)


class _CountlinesoncosmosFuncInput:
    inputpath: Input = None
    """AnyDirectory"""
    num_partitions: int = 1
    """integer"""


class _CountlinesoncosmosFuncOutput:
    outputpath: Output = None
    """AnyDirectory"""


class _CountlinesoncosmosFuncComponent(Component):
    inputs: _CountlinesoncosmosFuncInput
    outputs: _CountlinesoncosmosFuncOutput
    runsettings: _CommandComponentRunsetting


def countlinesoncosmos_func(
    inputpath: Data = None,
    num_partitions: int = 1,
) -> _CountlinesoncosmosFuncComponent:
    """Created from Aether client version: ********, module id:886ea9b0-19e1-40c0-b69e-999cc13dbaa5, module name:CountLinesOnCosmos and module description Count the number of lines in a text file on cosmos.
    
    Args:
        inputpath (Data): AnyDirectory
        num_partitions (int): integer
    
    Returns:
        _CountlinesoncosmosFuncComponent: A component object. This component contains the following output ports:
    
            outputpath (Output): AnyDirectory\n
    """
    _countlinesoncosmos_func = component_loader.load_component(name='countlinesoncosmos_func')
    return _countlinesoncosmos_func(
            inputpath=inputpath,
            num_partitions=num_partitions,)


class _CreateDatasetFuncInput:
    datastore_name: str = 'adls_relevance09'
    """ADLS datastore name for Cosmos"""
    relative_path: str = None
    """Relative path on datastore"""
    validate: bool = True
    """Whether to validate dataset"""


class _CreateDatasetFuncOutput:
    output: Output = None
    """Output dataset"""


class _CreateDatasetFuncComponent(Component):
    inputs: _CreateDatasetFuncInput
    outputs: _CreateDatasetFuncOutput
    runsettings: _CommandComponentRunsetting


def create_dataset_func(
    datastore_name: str = 'adls_relevance09',
    relative_path: str = None,
    validate: bool = True,
) -> _CreateDatasetFuncComponent:
    """Create an AML dataset from datastore and relative path
    
    Args:
        datastore_name (str): ADLS datastore name for Cosmos
        relative_path (str): Relative path on datastore
        validate (bool): Whether to validate dataset
    
    Returns:
        _CreateDatasetFuncComponent: A component object. This component contains the following output ports:
    
            output (Output): Output dataset\n
    """
    _create_dataset_func = component_loader.load_component(name='create_dataset_func')
    return _create_dataset_func(
            datastore_name=datastore_name,
            relative_path=relative_path,
            validate=validate,)


class _CreateGenericTsvFromStringListFuncInput:
    isverbatim: str = 'false'
    """string"""
    lines: str = None
    """Json format of lines list"""


class _CreateGenericTsvFromStringListFuncOutput:
    output: Output = None
    """path"""


class _CreateGenericTsvFromStringListFuncComponent(Component):
    inputs: _CreateGenericTsvFromStringListFuncInput
    outputs: _CreateGenericTsvFromStringListFuncOutput
    runsettings: _CommandComponentRunsetting


def create_generic_tsv_from_string_list_func(
    isverbatim: str = 'false',
    lines: str = None,
) -> _CreateGenericTsvFromStringListFuncComponent:
    """Generate Generic TSV From String List.
    
    Args:
        isverbatim (str): string
        lines (str): Json format of lines list
    
    Returns:
        _CreateGenericTsvFromStringListFuncComponent: A component object. This component contains the following output ports:
    
            output (Output): path\n
    """
    _create_generic_tsv_from_string_list_func = component_loader.load_component(name='create_generic_tsv_from_string_list_func')
    return _create_generic_tsv_from_string_list_func(
            isverbatim=isverbatim,
            lines=lines,)


class _CreatePairwiseDeltaFreeformsFuncInput:
    input: Input = None
    """AnyDirectory"""
    left_template: str = 'L_{feature}'
    """string (optional)"""
    right_template: str = 'R_{feature}'
    """string (optional)"""
    delta_template: str = '(- {left} {right})'
    """string (optional)"""


class _CreatePairwiseDeltaFreeformsFuncOutput:
    output: Output = None
    """AnyDirectory"""


class _CreatePairwiseDeltaFreeformsFuncComponent(Component):
    inputs: _CreatePairwiseDeltaFreeformsFuncInput
    outputs: _CreatePairwiseDeltaFreeformsFuncOutput
    runsettings: _CommandComponentRunsetting


def create_pairwise_delta_freeforms_func(
    input: Data = None,
    left_template: str = 'L_{feature}',
    right_template: str = 'R_{feature}',
    delta_template: str = '(- {left} {right})',
) -> _CreatePairwiseDeltaFreeformsFuncComponent:
    """Created from Aether client version: ********, module id:75bef050-b67f-4051-a3e5-4771210f9e82, module name:Create Pairwise Delta FreeForms and module description Create Pairwise Delta FreeForms
    
    Args:
        input (Data): AnyDirectory
        left_template (str): string (optional)
        right_template (str): string (optional)
        delta_template (str): string (optional)
    
    Returns:
        _CreatePairwiseDeltaFreeformsFuncComponent: A component object. This component contains the following output ports:
    
            output (Output): AnyDirectory\n
    """
    _create_pairwise_delta_freeforms_func = component_loader.load_component(name='create_pairwise_delta_freeforms_func')
    return _create_pairwise_delta_freeforms_func(
            input=input,
            left_template=left_template,
            right_template=right_template,
            delta_template=delta_template,)


class _CreatePairwiseFeatureNamesFuncInput:
    input: Input = None
    """AnyDirectory"""
    left_name: str = 'L_{feature}'
    """string (optional)"""
    right_name: str = 'R_{feature}'
    """string (optional)"""


class _CreatePairwiseFeatureNamesFuncOutput:
    output: Output = None
    """AnyDirectory"""


class _CreatePairwiseFeatureNamesFuncComponent(Component):
    inputs: _CreatePairwiseFeatureNamesFuncInput
    outputs: _CreatePairwiseFeatureNamesFuncOutput
    runsettings: _CommandComponentRunsetting


def create_pairwise_feature_names_func(
    input: Data = None,
    left_name: str = 'L_{feature}',
    right_name: str = 'R_{feature}',
) -> _CreatePairwiseFeatureNamesFuncComponent:
    """Created from Aether client version: ********, module id:c357f0a6-cbea-4b21-975b-551ae9845d89, module name:Create Pairwise Feature Names and module description Create Pairwise Feature Names
    
    Args:
        input (Data): AnyDirectory
        left_name (str): string (optional)
        right_name (str): string (optional)
    
    Returns:
        _CreatePairwiseFeatureNamesFuncComponent: A component object. This component contains the following output ports:
    
            output (Output): AnyDirectory\n
    """
    _create_pairwise_feature_names_func = component_loader.load_component(name='create_pairwise_feature_names_func')
    return _create_pairwise_feature_names_func(
            input=input,
            left_name=left_name,
            right_name=right_name,)


class _CreateRankerZipArchiveFromFreeformsFuncInput:
    input_freeforms: Input = None
    """AnyDirectory"""


class _CreateRankerZipArchiveFromFreeformsFuncOutput:
    zip_rankers: Output = None
    """AnyDirectory"""


class _CreateRankerZipArchiveFromFreeformsFuncComponent(Component):
    inputs: _CreateRankerZipArchiveFromFreeformsFuncInput
    outputs: _CreateRankerZipArchiveFromFreeformsFuncOutput
    runsettings: _CommandComponentRunsetting


def create_ranker_zip_archive_from_freeforms_func(
    input_freeforms: Data = None,
) -> _CreateRankerZipArchiveFromFreeformsFuncComponent:
    """Created from Aether client version: ********, module id:a48e8fd5-4bb6-4abe-918c-71b7c5291d63, module name:Create Ranker ZIP Archive from FreeForms and module description Creates ZIP archive with a ranker file for each input freeform.  The ranker will simply evaluate the freeform.
    
    Args:
        input_freeforms (Data): AnyDirectory
    
    Returns:
        _CreateRankerZipArchiveFromFreeformsFuncComponent: A component object. This component contains the following output ports:
    
            zip_rankers (Output): AnyDirectory\n
    """
    _create_ranker_zip_archive_from_freeforms_func = component_loader.load_component(name='create_ranker_zip_archive_from_freeforms_func')
    return _create_ranker_zip_archive_from_freeforms_func(
            input_freeforms=input_freeforms,)


class _CreatefrinputiniFuncInput:
    featurelist: Input = None
    """AnyDirectory"""


class _CreatefrinputiniFuncOutput:
    inputini: Output = None
    """AnyDirectory"""


class _CreatefrinputiniFuncComponent(Component):
    inputs: _CreatefrinputiniFuncInput
    outputs: _CreatefrinputiniFuncOutput
    runsettings: _CommandComponentRunsetting


def createfrinputini_func(
    featurelist: Data = None,
) -> _CreatefrinputiniFuncComponent:
    """Rewrite from Aether client version: ********, module id:66ea05bd-bc46-4e0b-8f60-b2f89dbfbe98, module name:CreateFRInputIni and module description Create FastRank InputIni
    
    Args:
        featurelist (Data): AnyDirectory
    
    Returns:
        _CreatefrinputiniFuncComponent: A component object. This component contains the following output ports:
    
            inputini (Output): AnyDirectory\n
    """
    _createfrinputini_func = component_loader.load_component(name='createfrinputini_func')
    return _createfrinputini_func(
            featurelist=featurelist,)


class _CustomDiffFuncInput:
    input_a: Input = None
    """A directory path containing one text file"""
    input_b: Input = None
    """A text file containing tab separated lines"""


class _CustomDiffFuncOutput:
    input_a_diff: Output = None
    """AnyDirectory"""
    intersection: Output = None
    """AnyDirectory"""
    input_b_diff: Output = None
    """AnyDirectory"""


class _CustomDiffFuncComponent(Component):
    inputs: _CustomDiffFuncInput
    outputs: _CustomDiffFuncOutput
    runsettings: _CommandComponentRunsetting


def custom_diff_func(
    input_a: Data = None,
    input_b: Data = None,
) -> _CustomDiffFuncComponent:
    """Read input_a as is. Read lines from input_b, split on '\t', write each part of it as its own new line, and repeat until end of file. Compare difference between input_a and input_b. Note: all empty lines are removed
    
    Args:
        input_a (Data): A directory path containing one text file
        input_b (Data): A text file containing tab separated lines
    
    Returns:
        _CustomDiffFuncComponent: A component object. This component contains the following output ports:
    
            input_a_diff (Output): AnyDirectory\n
            intersection (Output): AnyDirectory\n
            input_b_diff (Output): AnyDirectory\n
    """
    _custom_diff_func = component_loader.load_component(name='custom_diff_func')
    return _custom_diff_func(
            input_a=input_a,
            input_b=input_b,)


class _EsampleDeterministicFuncInput:
    input_extraction_header: Input = None
    """AnyDirectory"""
    input_extraction: Input = None
    """AnyDirectory"""
    extraction_columns: str = None
    """string"""
    key_combine_expression: str = None
    """string"""
    sample_rate: float = None
    """float"""
    seed: int = 1234
    """integer"""
    buckets: int = 10000
    """integer"""
    num_partitions: int = 1000
    """integer"""


class _EsampleDeterministicFuncOutput:
    output_sample_extraction: Output = None
    """AnyDirectory"""
    output_others_extraction: Output = None
    """AnyDirectory"""
    output_sample_extraction_count: Output = None
    """AnyDirectory"""
    output_others_extraction_count: Output = None
    """AnyDirectory"""


class _EsampleDeterministicFuncComponent(Component):
    inputs: _EsampleDeterministicFuncInput
    outputs: _EsampleDeterministicFuncOutput
    runsettings: _CommandComponentRunsetting


def esample_deterministic_func(
    input_extraction_header: Data = None,
    input_extraction: Data = None,
    extraction_columns: str = None,
    key_combine_expression: str = None,
    sample_rate: float = None,
    seed: int = 1234,
    buckets: int = 10000,
    num_partitions: int = 1000,
) -> _EsampleDeterministicFuncComponent:
    """Created from Aether client version: ******** module id: dcb04993-f14c-49f2-aa29-d5a4df5c9cc4 module name: [Scope] eSample Deterministic module description:
    Same as 09b59e5d-2a84-47b1-aa3f-6d87efddae91 using a MD5 hash of the key combined with the seed.
   In other words, the \"random\" number is fully deterministic, given the key and seed values.
    
    Args:
        input_extraction_header (Data): AnyDirectory
        input_extraction (Data): AnyDirectory
        extraction_columns (str): string
        key_combine_expression (str): string
        sample_rate (float): float
        seed (int): integer
        buckets (int): integer
        num_partitions (int): integer
    
    Returns:
        _EsampleDeterministicFuncComponent: A component object. This component contains the following output ports:
    
            output_sample_extraction (Output): AnyDirectory\n
            output_others_extraction (Output): AnyDirectory\n
            output_sample_extraction_count (Output): AnyDirectory\n
            output_others_extraction_count (Output): AnyDirectory\n
    """
    _esample_deterministic_func = component_loader.load_component(name='esample_deterministic_func')
    return _esample_deterministic_func(
            input_extraction_header=input_extraction_header,
            input_extraction=input_extraction,
            extraction_columns=extraction_columns,
            key_combine_expression=key_combine_expression,
            sample_rate=sample_rate,
            seed=seed,
            buckets=buckets,
            num_partitions=num_partitions,)


class _ExtractAllFeaturesFfv1Ffv2FuncInput:
    input: Input = None
    """AnyDirectory"""


class _ExtractAllFeaturesFfv1Ffv2FuncOutput:
    output: Output = None
    """AnyDirectory"""


class _ExtractAllFeaturesFfv1Ffv2FuncComponent(Component):
    inputs: _ExtractAllFeaturesFfv1Ffv2FuncInput
    outputs: _ExtractAllFeaturesFfv1Ffv2FuncOutput
    runsettings: _CommandComponentRunsetting


def extract_all_features_ffv1_ffv2_func(
    input: Data = None,
) -> _ExtractAllFeaturesFfv1Ffv2FuncComponent:
    """Rewrite of Aether client version: ********, module id:b5f8bbdb-3811-4e6a-83f7-3ba05a8fcaff, module name:ExtractAllFeatures (support ffv1 & ffv2) and module description Extract the raw flat features from an input ini: support both ffv1 & ffv2. the previous does not support ffv2; v1.0: filter out ffv2 non-feature strings. v1.1 filter out suspicious feature strings v1.2 fix multiline issue
    
    Args:
        input (Data): AnyDirectory
    
    Returns:
        _ExtractAllFeaturesFfv1Ffv2FuncComponent: A component object. This component contains the following output ports:
    
            output (Output): AnyDirectory\n
    """
    _extract_all_features_ffv1_ffv2_func = component_loader.load_component(name='extract_all_features_ffv1_ffv2_func')
    return _extract_all_features_ffv1_ffv2_func(
            input=input,)


class _ExtractFeatureImportanceFuncInput:
    input: Input = None
    """AnyDirectory"""
    exclude_patterns: str = None
    """Json format list of regex patterns to exclude (optional)"""
    top_n: int = None
    """Keep only top N (optional)"""


class _ExtractFeatureImportanceFuncOutput:
    feature_importance_all: Output = None
    """AnyDirectory"""
    raw_feature_importance: Output = None
    """AnyDirectory"""
    features: Output = None
    """AnyDirectory"""


class _ExtractFeatureImportanceFuncComponent(Component):
    inputs: _ExtractFeatureImportanceFuncInput
    outputs: _ExtractFeatureImportanceFuncOutput
    runsettings: _CommandComponentRunsetting


def extract_feature_importance_func(
    input: Data = None,
    exclude_patterns: str = None,
    top_n: int = None,
) -> _ExtractFeatureImportanceFuncComponent:
    """extract_feature_importance
    
    Args:
        input (Data): AnyDirectory
        exclude_patterns (str): Json format list of regex patterns to exclude (optional)
        top_n (int): Keep only top N (optional)
    
    Returns:
        _ExtractFeatureImportanceFuncComponent: A component object. This component contains the following output ports:
    
            feature_importance_all (Output): AnyDirectory\n
            raw_feature_importance (Output): AnyDirectory\n
            features (Output): AnyDirectory\n
    """
    _extract_feature_importance_func = component_loader.load_component(name='extract_feature_importance_func')
    return _extract_feature_importance_func(
            input=input,
            exclude_patterns=exclude_patterns,
            top_n=top_n,)


class _ExtractionFeatureDistributionFuncInput:
    headerpath: Input = None
    """AnyDirectory"""
    extraction: Input = None
    """AnyDirectory"""
    columnstoremoveregex: str = None
    """string"""
    columnstoremoveinvert: str = None
    """string"""
    invalidvalueoverrideregex: str = None
    """string"""
    numpartitions: int = 1000
    """integer"""


class _ExtractionFeatureDistributionFuncOutput:
    output: Output = None
    """AnyDirectory"""
    outputss: Output = None
    """AnyDirectory"""


class _ExtractionFeatureDistributionFuncComponent(Component):
    inputs: _ExtractionFeatureDistributionFuncInput
    outputs: _ExtractionFeatureDistributionFuncOutput
    runsettings: _CommandComponentRunsetting


def extraction_feature_distribution_func(
    headerpath: Data = None,
    extraction: Data = None,
    columnstoremoveregex: str = None,
    columnstoremoveinvert: str = None,
    invalidvalueoverrideregex: str = None,
    numpartitions: int = 1000,
) -> _ExtractionFeatureDistributionFuncComponent:
    """Created from Aether client version: ********, module id:41101b00-d34f-4949-9bc9-815b9955195d, module name:Extraction Feature Distribution and module description Creates distribution of features in an extraction.  OFE data sometimes contains invalid values such as -nan(ind) which need to removed.  This can be done by specifying the InvalidValueOverrideRegex.  When double.TryParse fails, this regex is tried and if it matches, 0 is returned instead of failing.
    
    Args:
        headerpath (Data): AnyDirectory
        extraction (Data): AnyDirectory
        columnstoremoveregex (str): string
        columnstoremoveinvert (str): string
        invalidvalueoverrideregex (str): string
        numpartitions (int): integer
    
    Returns:
        _ExtractionFeatureDistributionFuncComponent: A component object. This component contains the following output ports:
    
            output (Output): AnyDirectory\n
            outputss (Output): AnyDirectory\n
    """
    _extraction_feature_distribution_func = component_loader.load_component(name='extraction_feature_distribution_func')
    return _extraction_feature_distribution_func(
            headerpath=headerpath,
            extraction=extraction,
            columnstoremoveregex=columnstoremoveregex,
            columnstoremoveinvert=columnstoremoveinvert,
            invalidvalueoverrideregex=invalidvalueoverrideregex,
            numpartitions=numpartitions,)


class _ExtractrankerfeaturesFuncInput:
    ranker: Input = None
    """path"""


class _ExtractrankerfeaturesFuncOutput:
    features: Output = None
    """path"""


class _ExtractrankerfeaturesFuncComponent(Component):
    inputs: _ExtractrankerfeaturesFuncInput
    outputs: _ExtractrankerfeaturesFuncOutput
    runsettings: _CommandComponentRunsetting


def extractrankerfeatures_func(
    ranker: Data = None,
) -> _ExtractrankerfeaturesFuncComponent:
    """Rewrite of Aether client version: ********, module id:08b1692d-a052-40f5-85f8-4be37c59d3c3, module name:ExtractRankerFeatures and module description Extract all features from a ranker (or any ini) for both standalone features and features found in freeform.
    
    Args:
        ranker (Data): path
    
    Returns:
        _ExtractrankerfeaturesFuncComponent: A component object. This component contains the following output ports:
    
            features (Output): path\n
    """
    _extractrankerfeatures_func = component_loader.load_component(name='extractrankerfeatures_func')
    return _extractrankerfeatures_func(
            ranker=ranker,)


class _FastrankCreateFeatureInputFuncInput:
    input: Input = None
    """AnyDirectory"""


class _FastrankCreateFeatureInputFuncOutput:
    output: Output = None
    """AnyDirectory"""


class _FastrankCreateFeatureInputFuncComponent(Component):
    inputs: _FastrankCreateFeatureInputFuncInput
    outputs: _FastrankCreateFeatureInputFuncOutput
    runsettings: _CommandComponentRunsetting


def fastrank_create_feature_input_func(
    input: Data = None,
) -> _FastrankCreateFeatureInputFuncComponent:
    """fastrank_create_feature_input
    
    Args:
        input (Data): AnyDirectory
    
    Returns:
        _FastrankCreateFeatureInputFuncComponent: A component object. This component contains the following output ports:
    
            output (Output): AnyDirectory\n
    """
    _fastrank_create_feature_input_func = component_loader.load_component(name='fastrank_create_feature_input_func')
    return _fastrank_create_feature_input_func(
            input=input,)


class _GenerateFpPairwiseExtractionFuncInput:
    inputextraction: Input = None
    """AnyDirectory"""
    header: Input = None
    """AnyDirectory"""
    features: Input = None
    """AnyDirectory"""
    inputisfolder: str = 'false'
    """string"""
    numinputchunks: int = 1
    """integer"""
    outputheaderinextraction: str = None
    """string"""
    originalfeature: str = None
    """string"""
    deltafeature: str = None
    """string"""
    ratiofeature: str = None
    """string"""
    allowshortlines: str = None
    """string"""
    deltaoffset: str = None
    """string"""
    truncatetouintrange: str = None
    """string"""
    metadataoutput2urls: str = None
    """string"""
    scopeparams: str = None
    """string (optional)"""


class _GenerateFpPairwiseExtractionFuncOutput:
    outputheader: Output = None
    """AnyDirectory"""
    outputextraction: Output = None
    """AnyDirectory"""
    outputmetadata: Output = None
    """AnyDirectory"""


class _GenerateFpPairwiseExtractionFuncComponent(Component):
    inputs: _GenerateFpPairwiseExtractionFuncInput
    outputs: _GenerateFpPairwiseExtractionFuncOutput
    runsettings: _CommandComponentRunsetting


def generate_fp_pairwise_extraction_func(
    inputextraction: Data = None,
    header: Data = None,
    features: Data = None,
    inputisfolder: str = 'false',
    numinputchunks: int = 1,
    outputheaderinextraction: str = None,
    originalfeature: str = None,
    deltafeature: str = None,
    ratiofeature: str = None,
    allowshortlines: str = None,
    deltaoffset: str = None,
    truncatetouintrange: str = None,
    metadataoutput2urls: str = None,
    scopeparams: str = None,
) -> _GenerateFpPairwiseExtractionFuncComponent:
    """Created from Aether client version: ********, module id:57540934-5534-4a77-9f75-33c4edc44d01, module name:[RM Must FP][Dedup][Unified Ranker] [Output Metadata] [Output Fair Pairs] Generate Pairwise Extraction and module description Generates pairwise extraction from a regular extraction.   Sorts extraction lines by Query Id and Doc1 Id and Doc2 Id for full reproducibility. rm must be FP
    
    Args:
        inputextraction (Data): AnyDirectory
        header (Data): AnyDirectory
        features (Data): AnyDirectory
        inputisfolder (str): string
        numinputchunks (int): integer
        outputheaderinextraction (str): string
        originalfeature (str): string
        deltafeature (str): string
        ratiofeature (str): string
        allowshortlines (str): string
        deltaoffset (str): string
        truncatetouintrange (str): string
        metadataoutput2urls (str): string
        scopeparams (str): string (optional)
    
    Returns:
        _GenerateFpPairwiseExtractionFuncComponent: A component object. This component contains the following output ports:
    
            outputheader (Output): AnyDirectory\n
            outputextraction (Output): AnyDirectory\n
            outputmetadata (Output): AnyDirectory\n
    """
    _generate_fp_pairwise_extraction_func = component_loader.load_component(name='generate_fp_pairwise_extraction_func')
    return _generate_fp_pairwise_extraction_func(
            inputextraction=inputextraction,
            header=header,
            features=features,
            inputisfolder=inputisfolder,
            numinputchunks=numinputchunks,
            outputheaderinextraction=outputheaderinextraction,
            originalfeature=originalfeature,
            deltafeature=deltafeature,
            ratiofeature=ratiofeature,
            allowshortlines=allowshortlines,
            deltaoffset=deltaoffset,
            truncatetouintrange=truncatetouintrange,
            metadataoutput2urls=metadataoutput2urls,
            scopeparams=scopeparams,)


class _InlineMetricsCalculationFuncInput:
    input: Input = None
    """AnyDirectory"""
    tag: str = None
    """string"""
    input_source_filter: str = 'WHERE 1 == 1'
    """string"""
    market_filter: str = 'WHERE 1 == 1'
    """string"""
    schema: str = None
    """string (optional)"""


class _InlineMetricsCalculationFuncOutput:
    all_segment_output: Output = None
    """AnyDirectory"""
    query_level_output: Output = None
    """AnyDirectory"""
    market_output: Output = None
    """AnyDirectory"""
    combined_all_ouput: Output = None
    """AnyDirectory"""
    outputheader: Output = None
    """AnyDirectory"""


class _InlineMetricsCalculationFuncComponent(Component):
    inputs: _InlineMetricsCalculationFuncInput
    outputs: _InlineMetricsCalculationFuncOutput
    runsettings: _CommandComponentRunsetting


def inline_metrics_calculation_func(
    input: Data = None,
    tag: str = None,
    input_source_filter: str = 'WHERE 1 == 1',
    market_filter: str = 'WHERE 1 == 1',
    schema: str = None,
) -> _InlineMetricsCalculationFuncComponent:
    """replace GUID:dd2c0fb6-ddfc-45aa-8845-5fe699080de7
    
    Args:
        input (Data): AnyDirectory
        tag (str): string
        input_source_filter (str): string
        market_filter (str): string
        schema (str): string (optional)
    
    Returns:
        _InlineMetricsCalculationFuncComponent: A component object. This component contains the following output ports:
    
            all_segment_output (Output): AnyDirectory\n
            query_level_output (Output): AnyDirectory\n
            market_output (Output): AnyDirectory\n
            combined_all_ouput (Output): AnyDirectory\n
            outputheader (Output): AnyDirectory\n
    """
    _inline_metrics_calculation_func = component_loader.load_component(name='inline_metrics_calculation_func')
    return _inline_metrics_calculation_func(
            input=input,
            tag=tag,
            input_source_filter=input_source_filter,
            market_filter=market_filter,
            schema=schema,)


class _JoinFeatureScoreFuncInput:
    input_header: Input = None
    """AnyDirectory"""
    input_extraction: Input = None
    """AnyDirectory"""
    extra_extraction: Input = None
    """AnyDirectory"""
    feature_name: str = None
    """string"""
    num_output_partitions: str = '1000'
    """string"""


class _JoinFeatureScoreFuncOutput:
    header: Output = None
    """AnyDirectory"""
    extraction: Output = None
    """AnyDirectory"""


class _JoinFeatureScoreFuncComponent(Component):
    inputs: _JoinFeatureScoreFuncInput
    outputs: _JoinFeatureScoreFuncOutput
    runsettings: _CommandComponentRunsetting


def join_feature_score_func(
    input_header: Data = None,
    input_extraction: Data = None,
    extra_extraction: Data = None,
    feature_name: str = None,
    num_output_partitions: str = '1000',
) -> _JoinFeatureScoreFuncComponent:
    """Created from Aether client version: ******** module id: 4287fb2e-286e-4c4b-9be1-192cb0fb22f5 module name: Join Feature Score module description: Join Feature Score
    
    Args:
        input_header (Data): AnyDirectory
        input_extraction (Data): AnyDirectory
        extra_extraction (Data): AnyDirectory
        feature_name (str): string
        num_output_partitions (str): string
    
    Returns:
        _JoinFeatureScoreFuncComponent: A component object. This component contains the following output ports:
    
            header (Output): AnyDirectory\n
            extraction (Output): AnyDirectory\n
    """
    _join_feature_score_func = component_loader.load_component(name='join_feature_score_func')
    return _join_feature_score_func(
            input_header=input_header,
            input_extraction=input_extraction,
            extra_extraction=extra_extraction,
            feature_name=feature_name,
            num_output_partitions=num_output_partitions,)


class _JoinRanklmScoreOnPosition025FuncInput:
    input_header: Input = None
    """AnyDirectory"""
    input_extraction: Input = None
    """AnyDirectory"""
    extra_extraction: Input = None
    """AnyDirectory"""
    num_input_partitions: int = 1000
    """integer"""
    num_output_partitions: int = 1000
    """integer"""


class _JoinRanklmScoreOnPosition025FuncOutput:
    header: Output = None
    """AnyDirectory"""
    extraction: Output = None
    """AnyDirectory"""


class _JoinRanklmScoreOnPosition025FuncComponent(Component):
    inputs: _JoinRanklmScoreOnPosition025FuncInput
    outputs: _JoinRanklmScoreOnPosition025FuncOutput
    runsettings: _CommandComponentRunsetting


def join_ranklm_score_on_position_0_25_func(
    input_header: Data = None,
    input_extraction: Data = None,
    extra_extraction: Data = None,
    num_input_partitions: int = 1000,
    num_output_partitions: int = 1000,
) -> _JoinRanklmScoreOnPosition025FuncComponent:
    """Created from Aether client version: ********, module id: 5e685b0d-02ed-4e01-aeab-c869995f5801 module name: Join RankLM Score on Position, 0-25
    
    Args:
        input_header (Data): AnyDirectory
        input_extraction (Data): AnyDirectory
        extra_extraction (Data): AnyDirectory
        num_input_partitions (int): integer
        num_output_partitions (int): integer
    
    Returns:
        _JoinRanklmScoreOnPosition025FuncComponent: A component object. This component contains the following output ports:
    
            header (Output): AnyDirectory\n
            extraction (Output): AnyDirectory\n
    """
    _join_ranklm_score_on_position_0_25_func = component_loader.load_component(name='join_ranklm_score_on_position_0_25_func')
    return _join_ranklm_score_on_position_0_25_func(
            input_header=input_header,
            input_extraction=input_extraction,
            extra_extraction=extra_extraction,
            num_input_partitions=num_input_partitions,
            num_output_partitions=num_output_partitions,)


class _LightgbmInferenceFuncInput:
    lgbm_model: Input = None
    """LightGBM model directory"""
    test_data_dir: Input = None
    """Directory with test data files"""
    test_data_file_name: str = 'File_{0}.txt'
    """Test data file name pattern (similar to train)"""
    adls_datastore: str = 'adls_relevance09'
    """string"""


class _LightgbmInferenceFuncOutput:
    output_folder: Output = None
    """Output directory"""
    output_classification: Output = None
    """Output directory for classification result"""
    output_portal_url: Output = None
    """Output directory for an output file containing URL to the AML portal for current run"""


class _LightgbmInferenceFuncComponent(Component):
    inputs: _LightgbmInferenceFuncInput
    outputs: _LightgbmInferenceFuncOutput
    runsettings: _CommandComponentRunsetting


def lightgbm_inference_func(
    lgbm_model: Data = None,
    test_data_dir: Data = None,
    test_data_file_name: str = 'File_{0}.txt',
    adls_datastore: str = 'adls_relevance09',
) -> _LightgbmInferenceFuncComponent:
    """A module to run LightGBM inference job
    
    Args:
        lgbm_model (Data): LightGBM model directory
        test_data_dir (Data): Directory with test data files
        test_data_file_name (str): Test data file name pattern (similar to train)
        adls_datastore (str): string
    
    Returns:
        _LightgbmInferenceFuncComponent: A component object. This component contains the following output ports:
    
            output_folder (Output): Output directory\n
            output_classification (Output): Output directory for classification result\n
            output_portal_url (Output): Output directory for an output file containing URL to the AML portal for current run\n
    """
    _lightgbm_inference_func = component_loader.load_component(name='lightgbm_inference_func')
    return _lightgbm_inference_func(
            lgbm_model=lgbm_model,
            test_data_dir=test_data_dir,
            test_data_file_name=test_data_file_name,
            adls_datastore=adls_datastore,)


class _LightgbmTrainingFuncObjectiveEnum(Enum):
    regression = 'regression'
    regression_l1 = 'regression_l1'
    huber = 'huber'
    fair = 'fair'
    poisson = 'poisson'
    quantile = 'quantile'
    mape = 'mape'
    gamma = 'gamma'
    tweedie = 'tweedie'
    binary = 'binary'
    multiclass = 'multiclass'
    multiclassova = 'multiclassova'
    cross_entropy = 'cross_entropy'
    cross_entropy_lambda = 'cross_entropy_lambda'
    lambdarank = 'lambdarank'
    rank_xendcg = 'rank_xendcg'


class _LightgbmTrainingFuncParallelTreeLearnerEnum(Enum):
    serial = 'serial'
    data_parallel = 'data_parallel'
    voting_parallel = 'voting_parallel'


class _LightgbmTrainingFuncInput:
    continued_training_model_dir: Input = None
    """Directory with continued training model(optional)"""
    test_data_dir: Input = None
    """Directory with test data files"""
    train_data_dir: Input = None
    """Directory with training data files"""
    additional_lgbm_params: str = 'NotSet'
    """Additional parameters to pass to LightGBM. Use NotSet if none"""
    adls_datastore: str = 'adls_relevance09'
    """string"""
    aml_run_node_count: int = 1
    """Number of nodes used by this AML run"""
    aml_run_process_count: int = 1
    """Number of processes per node used by this AML run. Anything other than 1 is untested"""
    continued_training_model: str = 'NotSet'
    """Input model file name from a previous training run"""
    debug: bool = False
    """boolean"""
    enable_mlflow: bool = True
    """boolean"""
    feature_fraction: float = 0.15
    """Fraction of features to use for each tree"""
    label_gain: str = None
    """Relevant gain for labels. Used only in lambdarank"""
    learning_rate: float = 0.3
    """Learning rate"""
    max_bin: int = 16
    """Max number of bins that feature values will be bucketed in"""
    metric: str = None
    """Metrics to report"""
    min_data_in_leaf: int = 1
    """Minimum data points per leaf node"""
    mlflow_parent_run: bool = True
    """boolean"""
    num_leaves: int = 30
    """Number of leaves"""
    num_test_chunks: int = None
    """Number of test file chunks"""
    num_trees: int = None
    """Number of trees to train"""
    objective: _LightgbmTrainingFuncObjectiveEnum = _LightgbmTrainingFuncObjectiveEnum.lambdarank
    """Learning objective, support customize objective or standard lib objective (enum: ['regression', 'regression_l1', 'huber', 'fair', 'poisson', 'quantile', 'mape', 'gamma', 'tweedie', 'binary', 'multiclass', 'multiclassova', 'cross_entropy', 'cross_entropy_lambda', 'lambdarank', 'rank_xendcg'])"""
    parallel_tree_learner: _LightgbmTrainingFuncParallelTreeLearnerEnum = _LightgbmTrainingFuncParallelTreeLearnerEnum.data_parallel
    """Type of LightGBM parallel learning algorithm to use. See https://lightgbm.readthedocs.io/en/latest/Parallel-Learning-Guide.html#choose-appropriate-parallel-algorithm for help (enum: ['serial', 'data_parallel', 'voting_parallel'])"""
    query_column: str = 'NotSet'
    """Index of the query column after the label column"""
    rerun_key: str = '2019-11-01'
    """Dummy parameter only used to force a rerun in Aether and AML"""
    test_data_file_name: str = 'File_{0}.txt'
    """Test data file name pattern (similar to train)"""
    train_data_file_name: str = 'File_{0}.txt'
    """Training data file name pattern that is used to determine the file name used by the i-th node"""
    weight_file_column: str = 'NotSet'
    """Index of the weight column after the label column"""


class _LightgbmTrainingFuncOutput:
    output_logs_zip: Output = None
    """Output directory for an output zip containing some of the logs from training"""
    output_metric_of_last_iteration: Output = None
    """Output directory for metric of last iteration"""
    output_metric_plot: Output = None
    """Output directory for metric plot"""
    output_metrics: Output = None
    """Output directory for metrics TSV"""
    output_model: Output = None
    """Output directory for model"""
    output_portal_url: Output = None
    """Output directory for an output file containing URL to the AML portal for current run"""


class _LightgbmTrainingFuncComponent(Component):
    inputs: _LightgbmTrainingFuncInput
    outputs: _LightgbmTrainingFuncOutput
    runsettings: _CommandComponentRunsetting


def lightgbm_training_func(
    continued_training_model_dir: Data = None,
    test_data_dir: Data = None,
    train_data_dir: Data = None,
    additional_lgbm_params: str = 'NotSet',
    adls_datastore: str = 'adls_relevance09',
    aml_run_node_count: int = 1,
    aml_run_process_count: int = 1,
    continued_training_model: str = 'NotSet',
    debug: bool = False,
    enable_mlflow: bool = True,
    feature_fraction: float = 0.15,
    label_gain: str = None,
    learning_rate: float = 0.3,
    max_bin: int = 16,
    metric: str = None,
    min_data_in_leaf: int = 1,
    mlflow_parent_run: bool = True,
    num_leaves: int = 30,
    num_test_chunks: int = None,
    num_trees: int = None,
    objective: _LightgbmTrainingFuncObjectiveEnum = _LightgbmTrainingFuncObjectiveEnum.lambdarank,
    parallel_tree_learner: _LightgbmTrainingFuncParallelTreeLearnerEnum = _LightgbmTrainingFuncParallelTreeLearnerEnum.data_parallel,
    query_column: str = 'NotSet',
    rerun_key: str = '2019-11-01',
    test_data_file_name: str = 'File_{0}.txt',
    train_data_file_name: str = 'File_{0}.txt',
    weight_file_column: str = 'NotSet',
) -> _LightgbmTrainingFuncComponent:
    """A module to run LightGBM training job
    
    Args:
        continued_training_model_dir (Data): Directory with continued training model(optional)
        test_data_dir (Data): Directory with test data files
        train_data_dir (Data): Directory with training data files
        additional_lgbm_params (str): Additional parameters to pass to LightGBM. Use NotSet if none
        adls_datastore (str): string
        aml_run_node_count (int): Number of nodes used by this AML run
        aml_run_process_count (int): Number of processes per node used by this AML run. Anything other than 1 is untested
        continued_training_model (str): Input model file name from a previous training run
        debug (bool): boolean
        enable_mlflow (bool): boolean
        feature_fraction (float): Fraction of features to use for each tree
        label_gain (str): Relevant gain for labels. Used only in lambdarank
        learning_rate (float): Learning rate
        max_bin (int): Max number of bins that feature values will be bucketed in
        metric (str): Metrics to report
        min_data_in_leaf (int): Minimum data points per leaf node
        mlflow_parent_run (bool): boolean
        num_leaves (int): Number of leaves
        num_test_chunks (int): Number of test file chunks
        num_trees (int): Number of trees to train
        objective (_LightgbmTrainingFuncObjectiveEnum): Learning objective, support customize objective or standard lib objective (enum: ['regression', 'regression_l1', 'huber', 'fair', 'poisson', 'quantile', 'mape', 'gamma', 'tweedie', 'binary', 'multiclass', 'multiclassova', 'cross_entropy', 'cross_entropy_lambda', 'lambdarank', 'rank_xendcg'])
        parallel_tree_learner (_LightgbmTrainingFuncParallelTreeLearnerEnum): Type of LightGBM parallel learning algorithm to use. See https://lightgbm.readthedocs.io/en/latest/Parallel-Learning-Guide.html#choose-appropriate-parallel-algorithm for help (enum: ['serial', 'data_parallel', 'voting_parallel'])
        query_column (str): Index of the query column after the label column
        rerun_key (str): Dummy parameter only used to force a rerun in Aether and AML
        test_data_file_name (str): Test data file name pattern (similar to train)
        train_data_file_name (str): Training data file name pattern that is used to determine the file name used by the i-th node
        weight_file_column (str): Index of the weight column after the label column
    
    Returns:
        _LightgbmTrainingFuncComponent: A component object. This component contains the following output ports:
    
            output_logs_zip (Output): Output directory for an output zip containing some of the logs from training\n
            output_metric_of_last_iteration (Output): Output directory for metric of last iteration\n
            output_metric_plot (Output): Output directory for metric plot\n
            output_metrics (Output): Output directory for metrics TSV\n
            output_model (Output): Output directory for model\n
            output_portal_url (Output): Output directory for an output file containing URL to the AML portal for current run\n
    """
    _lightgbm_training_func = component_loader.load_component(name='lightgbm_training_func')
    return _lightgbm_training_func(
            continued_training_model_dir=continued_training_model_dir,
            test_data_dir=test_data_dir,
            train_data_dir=train_data_dir,
            additional_lgbm_params=additional_lgbm_params,
            adls_datastore=adls_datastore,
            aml_run_node_count=aml_run_node_count,
            aml_run_process_count=aml_run_process_count,
            continued_training_model=continued_training_model,
            debug=debug,
            enable_mlflow=enable_mlflow,
            feature_fraction=feature_fraction,
            label_gain=label_gain,
            learning_rate=learning_rate,
            max_bin=max_bin,
            metric=metric,
            min_data_in_leaf=min_data_in_leaf,
            mlflow_parent_run=mlflow_parent_run,
            num_leaves=num_leaves,
            num_test_chunks=num_test_chunks,
            num_trees=num_trees,
            objective=objective,
            parallel_tree_learner=parallel_tree_learner,
            query_column=query_column,
            rerun_key=rerun_key,
            test_data_file_name=test_data_file_name,
            train_data_file_name=train_data_file_name,
            weight_file_column=weight_file_column,)


class _LogBlobDataFuncInput:
    input1: Input = None
    """path"""
    input2: Input = None
    """path(optional)"""
    input3: Input = None
    """path(optional)"""
    input4: Input = None
    """path(optional)"""
    input5: Input = None
    """path(optional)"""
    target_filename_1: str = None
    """string"""
    target_filename_2: str = None
    """string (optional)"""
    target_filename_3: str = None
    """string (optional)"""
    target_filename_4: str = None
    """string (optional)"""
    target_filename_5: str = None
    """string (optional)"""


class _LogBlobDataFuncOutput:
    pass


class _LogBlobDataFuncComponent(Component):
    inputs: _LogBlobDataFuncInput
    outputs: _LogBlobDataFuncOutput
    runsettings: _CommandComponentRunsetting


def log_blob_data_func(
    input1: Data = None,
    input2: Data = None,
    input3: Data = None,
    input4: Data = None,
    input5: Data = None,
    target_filename_1: str = None,
    target_filename_2: str = None,
    target_filename_3: str = None,
    target_filename_4: str = None,
    target_filename_5: str = None,
) -> _LogBlobDataFuncComponent:
    """log_blob_data
    
    Args:
        input1 (Data): path
        input2 (Data): path(optional)
        input3 (Data): path(optional)
        input4 (Data): path(optional)
        input5 (Data): path(optional)
        target_filename_1 (str): string
        target_filename_2 (str): string (optional)
        target_filename_3 (str): string (optional)
        target_filename_4 (str): string (optional)
        target_filename_5 (str): string (optional)
    
    Returns:
        _LogBlobDataFuncComponent: A component object. This component contains the following output ports:
    
    """
    _log_blob_data_func = component_loader.load_component(name='log_blob_data_func')
    return _log_blob_data_func(
            input1=input1,
            input2=input2,
            input3=input3,
            input4=input4,
            input5=input5,
            target_filename_1=target_filename_1,
            target_filename_2=target_filename_2,
            target_filename_3=target_filename_3,
            target_filename_4=target_filename_4,
            target_filename_5=target_filename_5,)


class _MergeIniFuncInput:
    input1: Input = None
    """AnyDirectory"""
    input2: Input = None
    """AnyDirectory"""


class _MergeIniFuncOutput:
    output: Output = None
    """AnyDirectory"""


class _MergeIniFuncComponent(Component):
    inputs: _MergeIniFuncInput
    outputs: _MergeIniFuncOutput
    runsettings: _CommandComponentRunsetting


def merge_ini_func(
    input1: Data = None,
    input2: Data = None,
) -> _MergeIniFuncComponent:
    """merge_ini
    
    Args:
        input1 (Data): AnyDirectory
        input2 (Data): AnyDirectory
    
    Returns:
        _MergeIniFuncComponent: A component object. This component contains the following output ports:
    
            output (Output): AnyDirectory\n
    """
    _merge_ini_func = component_loader.load_component(name='merge_ini_func')
    return _merge_ini_func(
            input1=input1,
            input2=input2,)


class _MergePartitionChunksAndHeaderToSstreamFuncInput:
    header: Input = None
    """AnyDirectory"""
    input: Input = None
    """AnyDirectory"""
    inputisfolder: str = 'true'
    """string"""
    numinputchunks: int = 1000
    """integer"""


class _MergePartitionChunksAndHeaderToSstreamFuncOutput:
    output: Output = None
    """AnyDirectory"""


class _MergePartitionChunksAndHeaderToSstreamFuncComponent(Component):
    inputs: _MergePartitionChunksAndHeaderToSstreamFuncInput
    outputs: _MergePartitionChunksAndHeaderToSstreamFuncOutput
    runsettings: _CommandComponentRunsetting


def merge_partition_chunks_and_header_to_sstream_func(
    header: Data = None,
    input: Data = None,
    inputisfolder: str = 'true',
    numinputchunks: int = 1000,
) -> _MergePartitionChunksAndHeaderToSstreamFuncComponent:
    """Created from Aether client version: ********, module id:dc2aedd1-0947-4e92-8803-9a46f45d1709, module name:Merge TSV Files to SStream and module description Merge TSV Files to SStream
    
    Args:
        header (Data): AnyDirectory
        input (Data): AnyDirectory
        inputisfolder (str): string
        numinputchunks (int): integer
    
    Returns:
        _MergePartitionChunksAndHeaderToSstreamFuncComponent: A component object. This component contains the following output ports:
    
            output (Output): AnyDirectory\n
    """
    _merge_partition_chunks_and_header_to_sstream_func = component_loader.load_component(name='merge_partition_chunks_and_header_to_sstream_func')
    return _merge_partition_chunks_and_header_to_sstream_func(
            header=header,
            input=input,
            inputisfolder=inputisfolder,
            numinputchunks=numinputchunks,)


class _MergeTwoFilesByColumnFuncInput:
    input1: Input = None
    """['AnyDirectory', 'AnyDirectory']"""
    input2: Input = None
    """['AnyDirectory', 'AnyDirectory']"""
    delimiter: str = None
    """string (optional)"""


class _MergeTwoFilesByColumnFuncOutput:
    output: Output = None
    """AnyDirectory"""


class _MergeTwoFilesByColumnFuncComponent(Component):
    inputs: _MergeTwoFilesByColumnFuncInput
    outputs: _MergeTwoFilesByColumnFuncOutput
    runsettings: _CommandComponentRunsetting


def merge_two_files_by_column_func(
    input1: Data = None,
    input2: Data = None,
    delimiter: str = None,
) -> _MergeTwoFilesByColumnFuncComponent:
    """Merge two files by column. Fail if number of lines differ.
    
    Args:
        input1 (Data): ['AnyDirectory', 'AnyDirectory']
        input2 (Data): ['AnyDirectory', 'AnyDirectory']
        delimiter (str): string (optional)
    
    Returns:
        _MergeTwoFilesByColumnFuncComponent: A component object. This component contains the following output ports:
    
            output (Output): AnyDirectory\n
    """
    _merge_two_files_by_column_func = component_loader.load_component(name='merge_two_files_by_column_func')
    return _merge_two_files_by_column_func(
            input1=input1,
            input2=input2,
            delimiter=delimiter,)


class _OutputHtmlForInlineMetricsFuncInput:
    input: Input = None
    """AnyDirectory"""
    table_name: str = '"Metrics Summary"'
    """string"""


class _OutputHtmlForInlineMetricsFuncOutput:
    output: Output = None
    """AnyDirectory"""


class _OutputHtmlForInlineMetricsFuncComponent(Component):
    inputs: _OutputHtmlForInlineMetricsFuncInput
    outputs: _OutputHtmlForInlineMetricsFuncOutput
    runsettings: _CommandComponentRunsetting


def output_html_for_inline_metrics_func(
    input: Data = None,
    table_name: str = '"Metrics Summary"',
) -> _OutputHtmlForInlineMetricsFuncComponent:
    """Created from Aether client version: ********, module id:52f7fcb3-128e-4d0c-b87f-222d803866ce, module name:output html for inline metrics and module description output html for inline metrics
    
    Args:
        input (Data): AnyDirectory
        table_name (str): string
    
    Returns:
        _OutputHtmlForInlineMetricsFuncComponent: A component object. This component contains the following output ports:
    
            output (Output): AnyDirectory\n
    """
    _output_html_for_inline_metrics_func = component_loader.load_component(name='output_html_for_inline_metrics_func')
    return _output_html_for_inline_metrics_func(
            input=input,
            table_name=table_name,)


class _PrepareOnedcgDataFuncInput:
    onedcg_urp: Input = None
    """AnyDirectory"""
    onedcg_title_snippet: Input = None
    """AnyDirectory"""
    renamecolumnparams: str = '""'
    """string"""
    removecolumnparams: str = '""'
    """string"""


class _PrepareOnedcgDataFuncOutput:
    output_extraction: Output = None
    """AnyDirectory"""
    output_distinct_queries: Output = None
    """AnyDirectory"""


class _PrepareOnedcgDataFuncComponent(Component):
    inputs: _PrepareOnedcgDataFuncInput
    outputs: _PrepareOnedcgDataFuncOutput
    runsettings: _CommandComponentRunsetting


def prepare_onedcg_data_func(
    onedcg_urp: Data = None,
    onedcg_title_snippet: Data = None,
    renamecolumnparams: str = '""',
    removecolumnparams: str = '""',
) -> _PrepareOnedcgDataFuncComponent:
    """Created from Aether client version: ********, module id:0b501e1f-be25-4d0f-af19-74db7afea2ae, module name:Add QueryId for SStream and module description Add QueryId for SStream
    
    Args:
        onedcg_urp (Data): AnyDirectory
        onedcg_title_snippet (Data): AnyDirectory
        renamecolumnparams (str): string
        removecolumnparams (str): string
    
    Returns:
        _PrepareOnedcgDataFuncComponent: A component object. This component contains the following output ports:
    
            output_extraction (Output): AnyDirectory\n
            output_distinct_queries (Output): AnyDirectory\n
    """
    _prepare_onedcg_data_func = component_loader.load_component(name='prepare_onedcg_data_func')
    return _prepare_onedcg_data_func(
            onedcg_urp=onedcg_urp,
            onedcg_title_snippet=onedcg_title_snippet,
            renamecolumnparams=renamecolumnparams,
            removecolumnparams=removecolumnparams,)


class _PwFeatureProcessingFuncInput:
    input: Input = None
    """AnyDirectory"""
    featurelistfile: Input = None
    """AnyDirectory"""
    featurecolumnname: str = '"Features"'
    """string"""
    excludecolumns: str = 'Features'
    """string"""
    clusteredby: str = 'ImpressionGuid'
    """string"""


class _PwFeatureProcessingFuncOutput:
    output: Output = None
    """AnyDirectory"""


class _PwFeatureProcessingFuncComponent(Component):
    inputs: _PwFeatureProcessingFuncInput
    outputs: _PwFeatureProcessingFuncOutput
    runsettings: _CommandComponentRunsetting


def pw_feature_processing_func(
    input: Data = None,
    featurelistfile: Data = None,
    featurecolumnname: str = '"Features"',
    excludecolumns: str = 'Features',
    clusteredby: str = 'ImpressionGuid',
) -> _PwFeatureProcessingFuncComponent:
    """Created from Aether client, module id:16ce45c4-bded-4350-a639-7aa619622171, module name:Feature Processor v3
    
    Args:
        input (Data): AnyDirectory
        featurelistfile (Data): AnyDirectory
        featurecolumnname (str): string
        excludecolumns (str): string
        clusteredby (str): string
    
    Returns:
        _PwFeatureProcessingFuncComponent: A component object. This component contains the following output ports:
    
            output (Output): AnyDirectory\n
    """
    _pw_feature_processing_func = component_loader.load_component(name='pw_feature_processing_func')
    return _pw_feature_processing_func(
            input=input,
            featurelistfile=featurelistfile,
            featurecolumnname=featurecolumnname,
            excludecolumns=excludecolumns,
            clusteredby=clusteredby,)


class _PwFeatureRenamingFuncInput:
    input: Input = None
    """AnyDirectory"""
    transform: bool = False
    """boolean"""


class _PwFeatureRenamingFuncOutput:
    output: Output = None
    """AnyDirectory"""


class _PwFeatureRenamingFuncComponent(Component):
    inputs: _PwFeatureRenamingFuncInput
    outputs: _PwFeatureRenamingFuncOutput
    runsettings: _CommandComponentRunsetting


def pw_feature_renaming_func(
    input: Data = None,
    transform: bool = False,
) -> _PwFeatureRenamingFuncComponent:
    """Created from Aether client version: ********, module id:fa29bd6d-4ea8-4f90-b66d-1456634089b1, module name:PW feature renaming and module description PW feature renaming
    
    Args:
        input (Data): AnyDirectory
        transform (bool): boolean
    
    Returns:
        _PwFeatureRenamingFuncComponent: A component object. This component contains the following output ports:
    
            output (Output): AnyDirectory\n
    """
    _pw_feature_renaming_func = component_loader.load_component(name='pw_feature_renaming_func')
    return _pw_feature_renaming_func(
            input=input,
            transform=transform,)


class _RemoveFeaturePrefixFuncInput:
    input: Input = None
    """AnyDirectory"""


class _RemoveFeaturePrefixFuncOutput:
    output: Output = None
    """AnyDirectory"""


class _RemoveFeaturePrefixFuncComponent(Component):
    inputs: _RemoveFeaturePrefixFuncInput
    outputs: _RemoveFeaturePrefixFuncOutput
    runsettings: _CommandComponentRunsetting


def remove_feature_prefix_func(
    input: Data = None,
) -> _RemoveFeaturePrefixFuncComponent:
    """Rewrite of Aether client version: ********, module id:25cc88b3-89c6-481a-b34a-cb1df2c37a9c, module name:RemoveFeaturePrefix and module description Remove Feature Prefix \"R_\" OR \"L_\"
    
    Args:
        input (Data): AnyDirectory
    
    Returns:
        _RemoveFeaturePrefixFuncComponent: A component object. This component contains the following output ports:
    
            output (Output): AnyDirectory\n
    """
    _remove_feature_prefix_func = component_loader.load_component(name='remove_feature_prefix_func')
    return _remove_feature_prefix_func(
            input=input,)


class _ReplaceRegexStringFileFuncByRowOrColumnEnum(Enum):
    row = 'row'
    col = 'col'


class _ReplaceRegexStringFileFuncInput:
    input: Input = None
    """path"""
    regex_pattern_replacements: str = None
    """Json format for replacement map, key is regex match pattern (optional)"""
    exact_replacements: str = None
    """Json format for replacement map, exact match (optional)"""
    by_row_or_column: _ReplaceRegexStringFileFuncByRowOrColumnEnum = _ReplaceRegexStringFileFuncByRowOrColumnEnum.row
    """enum (enum: ['row', 'col'])"""
    output_new_line_at_end: str = 'true'
    """Whether to output new line at the end of the file"""


class _ReplaceRegexStringFileFuncOutput:
    output: Output = None
    """path"""


class _ReplaceRegexStringFileFuncComponent(Component):
    inputs: _ReplaceRegexStringFileFuncInput
    outputs: _ReplaceRegexStringFileFuncOutput
    runsettings: _CommandComponentRunsetting


def replace_regex_string_file_func(
    input: Data = None,
    regex_pattern_replacements: str = None,
    exact_replacements: str = None,
    by_row_or_column: _ReplaceRegexStringFileFuncByRowOrColumnEnum = _ReplaceRegexStringFileFuncByRowOrColumnEnum.row,
    output_new_line_at_end: str = 'true',
) -> _ReplaceRegexStringFileFuncComponent:
    """Replace file content by replacement map (regex pattern/exact match).
    
    Args:
        input (Data): path
        regex_pattern_replacements (str): Json format for replacement map, key is regex match pattern (optional)
        exact_replacements (str): Json format for replacement map, exact match (optional)
        by_row_or_column (_ReplaceRegexStringFileFuncByRowOrColumnEnum): enum (enum: ['row', 'col'])
        output_new_line_at_end (str): Whether to output new line at the end of the file
    
    Returns:
        _ReplaceRegexStringFileFuncComponent: A component object. This component contains the following output ports:
    
            output (Output): path\n
    """
    _replace_regex_string_file_func = component_loader.load_component(name='replace_regex_string_file_func')
    return _replace_regex_string_file_func(
            input=input,
            regex_pattern_replacements=regex_pattern_replacements,
            exact_replacements=exact_replacements,
            by_row_or_column=by_row_or_column,
            output_new_line_at_end=output_new_line_at_end,)


class _ReplaceRegexStringFolderFuncByRowOrColumnEnum(Enum):
    row = 'row'
    col = 'col'


class _ReplaceRegexStringFolderFuncInput:
    input: Input = None
    """path"""
    regex_pattern_replacements: str = None
    """Json format for replacement map, key is regex match pattern (optional)"""
    exact_replacements: str = None
    """Json format for replacement map, exact match (optional)"""
    by_row_or_column: _ReplaceRegexStringFolderFuncByRowOrColumnEnum = _ReplaceRegexStringFolderFuncByRowOrColumnEnum.row
    """enum (enum: ['row', 'col'])"""
    output_new_line_at_end: str = 'true'
    """Whether to output new line at the end of the file"""


class _ReplaceRegexStringFolderFuncOutput:
    output: Output = None
    """path"""


class _ReplaceRegexStringFolderFuncComponent(Component):
    inputs: _ReplaceRegexStringFolderFuncInput
    outputs: _ReplaceRegexStringFolderFuncOutput
    runsettings: _CommandComponentRunsetting


def replace_regex_string_folder_func(
    input: Data = None,
    regex_pattern_replacements: str = None,
    exact_replacements: str = None,
    by_row_or_column: _ReplaceRegexStringFolderFuncByRowOrColumnEnum = _ReplaceRegexStringFolderFuncByRowOrColumnEnum.row,
    output_new_line_at_end: str = 'true',
) -> _ReplaceRegexStringFolderFuncComponent:
    """Replace file content by replacement map (regex pattern/exact match).
    
    Args:
        input (Data): path
        regex_pattern_replacements (str): Json format for replacement map, key is regex match pattern (optional)
        exact_replacements (str): Json format for replacement map, exact match (optional)
        by_row_or_column (_ReplaceRegexStringFolderFuncByRowOrColumnEnum): enum (enum: ['row', 'col'])
        output_new_line_at_end (str): Whether to output new line at the end of the file
    
    Returns:
        _ReplaceRegexStringFolderFuncComponent: A component object. This component contains the following output ports:
    
            output (Output): path\n
    """
    _replace_regex_string_folder_func = component_loader.load_component(name='replace_regex_string_folder_func')
    return _replace_regex_string_folder_func(
            input=input,
            regex_pattern_replacements=regex_pattern_replacements,
            exact_replacements=exact_replacements,
            by_row_or_column=by_row_or_column,
            output_new_line_at_end=output_new_line_at_end,)


class _ScopeReplaceStringsNoCommaFuncInput:
    input: Input = None
    """AnyDirectory"""
    sources: str = None
    """string"""
    replacements: str = None
    """string"""


class _ScopeReplaceStringsNoCommaFuncOutput:
    output: Output = None
    """AnyDirectory"""


class _ScopeReplaceStringsNoCommaFuncComponent(Component):
    inputs: _ScopeReplaceStringsNoCommaFuncInput
    outputs: _ScopeReplaceStringsNoCommaFuncOutput
    runsettings: _CommandComponentRunsetting


def scope_replace_strings_no_comma_func(
    input: Data = None,
    sources: str = None,
    replacements: str = None,
) -> _ScopeReplaceStringsNoCommaFuncComponent:
    """scope_replace_strings_no_comma
    
    Args:
        input (Data): AnyDirectory
        sources (str): string
        replacements (str): string
    
    Returns:
        _ScopeReplaceStringsNoCommaFuncComponent: A component object. This component contains the following output ports:
    
            output (Output): AnyDirectory\n
    """
    _scope_replace_strings_no_comma_func = component_loader.load_component(name='scope_replace_strings_no_comma_func')
    return _scope_replace_strings_no_comma_func(
            input=input,
            sources=sources,
            replacements=replacements,)


class _SmoothTrainingIniFuncInput:
    extraction_header: Input = None
    """AnyDirectory"""
    traininputini: Input = None
    """AnyDirectory"""


class _SmoothTrainingIniFuncOutput:
    smoothedtraininputini: Output = None
    """AnyDirectory"""
    modifications_file: Output = None
    """AnyDirectory"""


class _SmoothTrainingIniFuncComponent(Component):
    inputs: _SmoothTrainingIniFuncInput
    outputs: _SmoothTrainingIniFuncOutput
    runsettings: _CommandComponentRunsetting


def smooth_training_ini_func(
    extraction_header: Data = None,
    traininputini: Data = None,
) -> _SmoothTrainingIniFuncComponent:
    """Created from Aether client version: ********, module id:0a69226b-cb7d-49dd-8399-2529739c6ab4, module name:Smooth Training Ini and module description Consume a training ini file, and smooth divide-by-zero expressions that would evaluate to NaN or +/- Infinity
    
    Args:
        extraction_header (Data): AnyDirectory
        traininputini (Data): AnyDirectory
    
    Returns:
        _SmoothTrainingIniFuncComponent: A component object. This component contains the following output ports:
    
            smoothedtraininputini (Output): AnyDirectory\n
            modifications_file (Output): AnyDirectory\n
    """
    _smooth_training_ini_func = component_loader.load_component(name='smooth_training_ini_func')
    return _smooth_training_ini_func(
            extraction_header=extraction_header,
            traininputini=traininputini,)


class _SsToGenericTsvBase64FuncInput:
    sspath: Input = None
    """AnyDirectory"""


class _SsToGenericTsvBase64FuncOutput:
    tsvpath: Output = None
    """AnyDirectory"""
    schemafile_tsvpath: Output = None
    """AnyDirectory"""


class _SsToGenericTsvBase64FuncComponent(Component):
    inputs: _SsToGenericTsvBase64FuncInput
    outputs: _SsToGenericTsvBase64FuncOutput
    runsettings: _CommandComponentRunsetting


def ss_to_generic_tsv_base64_func(
    sspath: Data = None,
) -> _SsToGenericTsvBase64FuncComponent:
    """Created from Aether client version: ********, module id:26e6accf-2703-420e-a624-0f9394f82426, module name:SS To Generic TSV (Base64) and module description Convert SS to a generic tsv and a schema file. The binary will be base64 converted. the /t /r /n in string will be converted to #TAB# #R# #N#
    
    Args:
        sspath (Data): AnyDirectory
    
    Returns:
        _SsToGenericTsvBase64FuncComponent: A component object. This component contains the following output ports:
    
            tsvpath (Output): AnyDirectory\n
            schemafile_tsvpath (Output): AnyDirectory\n
    """
    _ss_to_generic_tsv_base64_func = component_loader.load_component(name='ss_to_generic_tsv_base64_func')
    return _ss_to_generic_tsv_base64_func(
            sspath=sspath,)


class _StreamprocessorSsSsFuncInput1IsfolderEnum(Enum):
    true = 'true'
    false = 'false'


class _StreamprocessorSsSsFuncInput2IsfolderEnum(Enum):
    true = 'true'
    false = 'false'


class _StreamprocessorSsSsFuncOutput1IssstreamEnum(Enum):
    true = 'true'
    false = 'false'


class _StreamprocessorSsSsFuncOutput2IssstreamEnum(Enum):
    true = 'true'
    false = 'false'


class _StreamprocessorSsSsFuncInput:
    input1: Input = None
    """AnyDirectory"""
    input2: Input = None
    """AnyDirectory(optional)"""
    input1isfolder: _StreamprocessorSsSsFuncInput1IsfolderEnum = _StreamprocessorSsSsFuncInput1IsfolderEnum.false
    """Whether input1 is folder (in case it is connected from CommandComponent, the output is folder) (enum: ['true', 'false'])"""
    input2isfolder: _StreamprocessorSsSsFuncInput2IsfolderEnum = _StreamprocessorSsSsFuncInput2IsfolderEnum.false
    """Whether input1 is folder (in case it is connected from CommandComponent, the output is folder) (enum: ['true', 'false'])"""
    comment: str = None
    """string (optional)"""
    scopecode: str = None
    """string"""
    schema1: str = None
    """string (optional)"""
    schema2: str = None
    """string (optional)"""
    output1issstream: _StreamprocessorSsSsFuncOutput1IssstreamEnum = _StreamprocessorSsSsFuncOutput1IssstreamEnum.true
    """enum (enum: ['true', 'false'])"""
    output1param: str = None
    """string (optional)"""
    output2issstream: _StreamprocessorSsSsFuncOutput2IssstreamEnum = _StreamprocessorSsSsFuncOutput2IssstreamEnum.true
    """enum (enum: ['true', 'false'])"""
    output2param: str = None
    """string (optional)"""
    extractparam: str = '"-silent"'
    """string"""
    csharpcode: str = '//'
    """string"""
    directive1: str = '//'
    """string"""
    directive2: str = '//'
    """string"""


class _StreamprocessorSsSsFuncOutput:
    output1: Output = None
    """AnyDirectory"""
    output2: Output = None
    """AnyDirectory"""


class _StreamprocessorSsSsFuncComponent(Component):
    inputs: _StreamprocessorSsSsFuncInput
    outputs: _StreamprocessorSsSsFuncOutput
    runsettings: _CommandComponentRunsetting


def streamprocessor_ss_ss_func(
    input1: Data = None,
    input2: Data = None,
    input1isfolder: _StreamprocessorSsSsFuncInput1IsfolderEnum = _StreamprocessorSsSsFuncInput1IsfolderEnum.false,
    input2isfolder: _StreamprocessorSsSsFuncInput2IsfolderEnum = _StreamprocessorSsSsFuncInput2IsfolderEnum.false,
    comment: str = None,
    scopecode: str = None,
    schema1: str = None,
    schema2: str = None,
    output1issstream: _StreamprocessorSsSsFuncOutput1IssstreamEnum = _StreamprocessorSsSsFuncOutput1IssstreamEnum.true,
    output1param: str = None,
    output2issstream: _StreamprocessorSsSsFuncOutput2IssstreamEnum = _StreamprocessorSsSsFuncOutput2IssstreamEnum.true,
    output2param: str = None,
    extractparam: str = '"-silent"',
    csharpcode: str = '//',
    directive1: str = '//',
    directive2: str = '//',
) -> _StreamprocessorSsSsFuncComponent:
    """Created from Aether client version: ********, module id:c8cbd966-3e92-4eb9-8adb-4685d70d9eb9, module name:StreamProcessor and module description stream processor, support sstream, modified from yongzhi's. v0.2, support aggregate path
    
    Args:
        input1 (Data): AnyDirectory
        input2 (Data): AnyDirectory(optional)
        input1isfolder (_StreamprocessorSsSsFuncInput1IsfolderEnum): Whether input1 is folder (in case it is connected from CommandComponent, the output is folder) (enum: ['true', 'false'])
        input2isfolder (_StreamprocessorSsSsFuncInput2IsfolderEnum): Whether input1 is folder (in case it is connected from CommandComponent, the output is folder) (enum: ['true', 'false'])
        comment (str): string (optional)
        scopecode (str): string
        schema1 (str): string (optional)
        schema2 (str): string (optional)
        output1issstream (_StreamprocessorSsSsFuncOutput1IssstreamEnum): enum (enum: ['true', 'false'])
        output1param (str): string (optional)
        output2issstream (_StreamprocessorSsSsFuncOutput2IssstreamEnum): enum (enum: ['true', 'false'])
        output2param (str): string (optional)
        extractparam (str): string
        csharpcode (str): string
        directive1 (str): string
        directive2 (str): string
    
    Returns:
        _StreamprocessorSsSsFuncComponent: A component object. This component contains the following output ports:
    
            output1 (Output): AnyDirectory\n
            output2 (Output): AnyDirectory\n
    """
    _streamprocessor_ss_ss_func = component_loader.load_component(name='streamprocessor_ss_ss_func')
    return _streamprocessor_ss_ss_func(
            input1=input1,
            input2=input2,
            input1isfolder=input1isfolder,
            input2isfolder=input2isfolder,
            comment=comment,
            scopecode=scopecode,
            schema1=schema1,
            schema2=schema2,
            output1issstream=output1issstream,
            output1param=output1param,
            output2issstream=output2issstream,
            output2param=output2param,
            extractparam=extractparam,
            csharpcode=csharpcode,
            directive1=directive1,
            directive2=directive2,)


class _StreamprocessorTsvTsvFuncInput1IsfolderEnum(Enum):
    true = 'true'
    false = 'false'


class _StreamprocessorTsvTsvFuncInput2IsfolderEnum(Enum):
    true = 'true'
    false = 'false'


class _StreamprocessorTsvTsvFuncOutput1IssstreamEnum(Enum):
    true = 'true'
    false = 'false'


class _StreamprocessorTsvTsvFuncOutput2IssstreamEnum(Enum):
    true = 'true'
    false = 'false'


class _StreamprocessorTsvTsvFuncInput:
    input1: Input = None
    """AnyDirectory"""
    input2: Input = None
    """AnyDirectory(optional)"""
    input1isfolder: _StreamprocessorTsvTsvFuncInput1IsfolderEnum = _StreamprocessorTsvTsvFuncInput1IsfolderEnum.false
    """Whether input1 is folder (in case it is connected from CommandComponent, the output is folder) (enum: ['true', 'false'])"""
    input2isfolder: _StreamprocessorTsvTsvFuncInput2IsfolderEnum = _StreamprocessorTsvTsvFuncInput2IsfolderEnum.false
    """Whether input1 is folder (in case it is connected from CommandComponent, the output is folder) (enum: ['true', 'false'])"""
    comment: str = None
    """string (optional)"""
    scopecode: str = None
    """string"""
    schema1: str = None
    """string (optional)"""
    schema2: str = None
    """string (optional)"""
    output1issstream: _StreamprocessorTsvTsvFuncOutput1IssstreamEnum = _StreamprocessorTsvTsvFuncOutput1IssstreamEnum.false
    """enum (enum: ['true', 'false'])"""
    output1param: str = None
    """string (optional)"""
    output2issstream: _StreamprocessorTsvTsvFuncOutput2IssstreamEnum = _StreamprocessorTsvTsvFuncOutput2IssstreamEnum.false
    """enum (enum: ['true', 'false'])"""
    output2param: str = None
    """string (optional)"""
    extractparam: str = '"-silent"'
    """string"""
    csharpcode: str = '//'
    """string"""
    directive1: str = '//'
    """string"""
    directive2: str = '//'
    """string"""


class _StreamprocessorTsvTsvFuncOutput:
    output1: Output = None
    """AnyDirectory"""
    output2: Output = None
    """AnyDirectory"""


class _StreamprocessorTsvTsvFuncComponent(Component):
    inputs: _StreamprocessorTsvTsvFuncInput
    outputs: _StreamprocessorTsvTsvFuncOutput
    runsettings: _CommandComponentRunsetting


def streamprocessor_tsv_tsv_func(
    input1: Data = None,
    input2: Data = None,
    input1isfolder: _StreamprocessorTsvTsvFuncInput1IsfolderEnum = _StreamprocessorTsvTsvFuncInput1IsfolderEnum.false,
    input2isfolder: _StreamprocessorTsvTsvFuncInput2IsfolderEnum = _StreamprocessorTsvTsvFuncInput2IsfolderEnum.false,
    comment: str = None,
    scopecode: str = None,
    schema1: str = None,
    schema2: str = None,
    output1issstream: _StreamprocessorTsvTsvFuncOutput1IssstreamEnum = _StreamprocessorTsvTsvFuncOutput1IssstreamEnum.false,
    output1param: str = None,
    output2issstream: _StreamprocessorTsvTsvFuncOutput2IssstreamEnum = _StreamprocessorTsvTsvFuncOutput2IssstreamEnum.false,
    output2param: str = None,
    extractparam: str = '"-silent"',
    csharpcode: str = '//',
    directive1: str = '//',
    directive2: str = '//',
) -> _StreamprocessorTsvTsvFuncComponent:
    """Created from Aether client version: ********, module id:c8cbd966-3e92-4eb9-8adb-4685d70d9eb9, module name:StreamProcessor and module description stream processor, support sstream, modified from yongzhi's. v0.2, support aggregate path
    
    Args:
        input1 (Data): AnyDirectory
        input2 (Data): AnyDirectory(optional)
        input1isfolder (_StreamprocessorTsvTsvFuncInput1IsfolderEnum): Whether input1 is folder (in case it is connected from CommandComponent, the output is folder) (enum: ['true', 'false'])
        input2isfolder (_StreamprocessorTsvTsvFuncInput2IsfolderEnum): Whether input1 is folder (in case it is connected from CommandComponent, the output is folder) (enum: ['true', 'false'])
        comment (str): string (optional)
        scopecode (str): string
        schema1 (str): string (optional)
        schema2 (str): string (optional)
        output1issstream (_StreamprocessorTsvTsvFuncOutput1IssstreamEnum): enum (enum: ['true', 'false'])
        output1param (str): string (optional)
        output2issstream (_StreamprocessorTsvTsvFuncOutput2IssstreamEnum): enum (enum: ['true', 'false'])
        output2param (str): string (optional)
        extractparam (str): string
        csharpcode (str): string
        directive1 (str): string
        directive2 (str): string
    
    Returns:
        _StreamprocessorTsvTsvFuncComponent: A component object. This component contains the following output ports:
    
            output1 (Output): AnyDirectory\n
            output2 (Output): AnyDirectory\n
    """
    _streamprocessor_tsv_tsv_func = component_loader.load_component(name='streamprocessor_tsv_tsv_func')
    return _streamprocessor_tsv_tsv_func(
            input1=input1,
            input2=input2,
            input1isfolder=input1isfolder,
            input2isfolder=input2isfolder,
            comment=comment,
            scopecode=scopecode,
            schema1=schema1,
            schema2=schema2,
            output1issstream=output1issstream,
            output1param=output1param,
            output2issstream=output2issstream,
            output2param=output2param,
            extractparam=extractparam,
            csharpcode=csharpcode,
            directive1=directive1,
            directive2=directive2,)


class _UnifiedOfePostProcessFuncInput:
    extractionheader: Input = None
    """AnyDirectory"""
    partitionscontent: Input = None
    """AnyDirectory"""
    numoutputchunks: int = 1000
    """integer"""


class _UnifiedOfePostProcessFuncOutput:
    partitions: Output = None
    """AnyDirectory"""
    outputnumrows: Output = None
    """AnyDirectory"""


class _UnifiedOfePostProcessFuncComponent(Component):
    inputs: _UnifiedOfePostProcessFuncInput
    outputs: _UnifiedOfePostProcessFuncOutput
    runsettings: _CommandComponentRunsetting


def unified_ofe_post_process_func(
    extractionheader: Data = None,
    partitionscontent: Data = None,
    numoutputchunks: int = 1000,
) -> _UnifiedOfePostProcessFuncComponent:
    """Created from Aether client version: ********, module id:5a5e1b2e-2470-4eda-a94b-************, module name:Unified OFE Post Process and module description Remove Duplicate DocId+Url (from other query paths) for the same ImpressionGuid and Unify QueryId for the same ImpressionGuid, convert double feature to int, keep url with longest features. Source code: https://msasg.visualstudio.com/Bing_and_IPG/_git/RelevanceFundamentals?path=/private/OFE/ScopeScripts/ExtractionToolsLibrary/UnifiedOFEImpressionReducer.cs&version=GBqinguo/Join.HRS.PW.Update&_a=history, ExtractionToolsLibrary.dll commit: b7a7ab55519084a8f17dc6e139a396bdb7f2581f
    
    Args:
        extractionheader (Data): AnyDirectory
        partitionscontent (Data): AnyDirectory
        numoutputchunks (int): integer
    
    Returns:
        _UnifiedOfePostProcessFuncComponent: A component object. This component contains the following output ports:
    
            partitions (Output): AnyDirectory\n
            outputnumrows (Output): AnyDirectory\n
    """
    _unified_ofe_post_process_func = component_loader.load_component(name='unified_ofe_post_process_func')
    return _unified_ofe_post_process_func(
            extractionheader=extractionheader,
            partitionscontent=partitionscontent,
            numoutputchunks=numoutputchunks,)


class _UnifiedRankerChashcodetol3CodeforpwmlWithDeltaFreeformAsInput5FuncInput:
    input: Input = None
    """AnyDirectory"""
    tabscnt: int = None
    """integer"""
    deltatemplate: str = None
    """string (optional)"""
    deltaheader: str = None
    """string (optional)"""


class _UnifiedRankerChashcodetol3CodeforpwmlWithDeltaFreeformAsInput5FuncOutput:
    output: Output = None
    """AnyDirectory"""


class _UnifiedRankerChashcodetol3CodeforpwmlWithDeltaFreeformAsInput5FuncComponent(Component):
    inputs: _UnifiedRankerChashcodetol3CodeforpwmlWithDeltaFreeformAsInput5FuncInput
    outputs: _UnifiedRankerChashcodetol3CodeforpwmlWithDeltaFreeformAsInput5FuncOutput
    runsettings: _CommandComponentRunsetting


def unified_ranker_chashcodetol3codeforpwml_with_delta_freeform_as_input_5_func(
    input: Data = None,
    tabscnt: int = None,
    deltatemplate: str = None,
    deltaheader: str = None,
) -> _UnifiedRankerChashcodetol3CodeforpwmlWithDeltaFreeformAsInput5FuncComponent:
    """Created from Aether client version: ********, module id:777d1fc1-a7d8-4381-806e-89797df53ad2, module name:[Unified Ranker] CHashCodeToL3CodeForPWML with Delta FreeForm as input and module description Like 75eff9ae-7929-4bb4-8efd-9ba4212fd602 but with the option to specify the Delta Freeform and Delta Header as input
    
    Args:
        input (Data): AnyDirectory
        tabscnt (int): integer
        deltatemplate (str): string (optional)
        deltaheader (str): string (optional)
    
    Returns:
        _UnifiedRankerChashcodetol3CodeforpwmlWithDeltaFreeformAsInput5FuncComponent: A component object. This component contains the following output ports:
    
            output (Output): AnyDirectory\n
    """
    _unified_ranker_chashcodetol3codeforpwml_with_delta_freeform_as_input_5_func = component_loader.load_component(name='unified_ranker_chashcodetol3codeforpwml_with_delta_freeform_as_input_5_func')
    return _unified_ranker_chashcodetol3codeforpwml_with_delta_freeform_as_input_5_func(
            input=input,
            tabscnt=tabscnt,
            deltatemplate=deltatemplate,
            deltaheader=deltaheader,)


class _UnifiedRankerGeneratePairwiseExtractionFuncInput:
    header: Input = None
    """AnyDirectory"""
    input_extraction: Input = None
    """AnyDirectory"""
    features: Input = None
    """AnyDirectory"""
    output_header_in_extraction: str = 'true'
    """string"""
    original_feature: str = 'true'
    """string"""
    delta_feature: str = 'true'
    """string"""
    ratio_feature: str = 'false'
    """string"""
    label_mapping: str = 'Label0:0|Label1:1|Label2:2|Label3:3|Label4:4|Label5:5|Label6:6|Label7:7|Label8:8|Label9:9|Label10:10|Label11:11|Label12:12|Label13:13|Label14:14|Label15:15|Label16:16|Label17:17|Label18:18|Label19:19|Label20:20|Label21:21|Label22:22|Label23:23|Label24:24|Label25:25|Label26:26|Label27:27|Label28:28|Label29:29|Label30:30|Label31:31|Label32:32|Label33:33|Label34:34|Label35:35|Label36:36|Label37:37|Label38:38|Label39:39|Label40:40|Label41:41|Label42:42|Label43:43|Label44:44|Label45:45|Label46:46|Label47:47|Label48:48|Label49:49|Label50:50|Label51:51|Label52:52|Label53:53|Label54:54|Label55:55|Label56:56|Label57:57|Label58:58|Label59:59|Label60:60|Label61:61|Label62:62|Label63:63|Label64:64|Label65:65|Label66:66|Label67:67|Label68:68|Label69:69|Label70:70|Label71:71|Label72:72|Label73:73|Label74:74|Label75:75|Label76:76|Label77:77|Label78:78|Label79:79|Label80:80|Label81:81|Label82:82|Label83:83|Label84:84|Label85:85|Label86:86|Label87:87|Label88:88|Label89:89|Label90:90|Label91:91|Label92:92|Label93:93|Label94:94|Label95:95|Label96:96|Label97:97|Label98:98|Label99:99|Label100:100|Label101:101|Label102:102|Label103:103|Label104:104|Label105:105|Label106:106|Label107:107|Label108:108|Label109:109|Label110:110|Label111:111|Label112:112|Label113:113|Label114:114|Label115:115|Label116:116|Label117:117|Label118:118|Label119:119|Label120:120|Label121:121|Label122:122|Label123:123|Label124:124|Label125:125|Label126:126|Label127:127|Label128:128|Label129:129|Label130:130|Label131:131|Label132:132|Label133:133|Label134:134|Label135:135|Label136:136|Label137:137|Label138:138|Label139:139|Label140:140|Label141:141|Label142:142|Label143:143|Label144:144|Label145:145|Label146:146|Label147:147|Label148:148|Label149:149|Label150:150|Label151:151|Label152:152|Label153:153|Label154:154|Label155:155|Label156:156|Label157:157|Label158:158|Label159:159|Label160:160|Label161:161|Label162:162|Label163:163|Label164:164|Label165:165|Label166:166|Label167:167|Label168:168|Label169:169|Label170:170|Label171:171|Label172:172|Label173:173|Label174:174|Label175:175|Label176:176|Label177:177|Label178:178|Label179:179|Label180:180|Label181:181|Label182:182|Label183:183|Label184:184|Label185:185|Label186:186|Label187:187|Label188:188|Label189:189|Label190:190|Label191:191|Label192:192|Label193:193|Label194:194|Label195:195|Label196:196|Label197:197|Label198:198|Label199:199|Label200:200|Label201:201|Label202:202|Label203:203|Label204:204|Label205:205|Label206:206|Label207:207|Label208:208|Label209:209|Label210:210|Label211:211|Label212:212|Label213:213|Label214:214|Label215:215|Label216:216|Label217:217|Label218:218|Label219:219|Label220:220|Label221:221|Label222:222|Label223:223|Label224:224|Label225:225|Label226:226|Label227:227|Label228:228|Label229:229|Label230:230|Label231:231|Label232:232|Label233:233|Label234:234|Label235:235|Label236:236|Label237:237|Label238:238|Label239:239|Label240:240|Label241:241|Label242:242|Label243:243|Label244:244|Label245:245|Label246:246|Label247:247|Label248:248|Label249:249|Label250:250'
    """string"""
    is_for_regression: str = 'true'
    """string"""
    regression_mid_value: int = 127
    """integer"""
    output_equal_rating_pairs: str = 'false'
    """string"""
    output_all_rating0: str = 'true'
    """string"""
    max_num_of_rating0_output: int = 10
    """integer"""
    delta_offset: int = 2147483647
    """integer"""
    non_click_label: int = 40
    """integer"""
    truncate_to_uint_range: str = 'true'
    """string"""
    allow_short_lines: str = 'false'
    """string"""
    hrs_equal_label_rate_fair: float = 0.8
    """float"""
    hrs_equal_label_rate_bad: float = 0.6
    """float"""
    training_model: str = 'OFE'
    """string"""
    random_seed: str = '1234'
    """string"""


class _UnifiedRankerGeneratePairwiseExtractionFuncOutput:
    output_header: Output = None
    """AnyDirectory"""
    output_extraction: Output = None
    """AnyDirectory"""


class _UnifiedRankerGeneratePairwiseExtractionFuncComponent(Component):
    inputs: _UnifiedRankerGeneratePairwiseExtractionFuncInput
    outputs: _UnifiedRankerGeneratePairwiseExtractionFuncOutput
    runsettings: _CommandComponentRunsetting


def unified_ranker_generate_pairwise_extraction_func(
    header: Data = None,
    input_extraction: Data = None,
    features: Data = None,
    output_header_in_extraction: str = 'true',
    original_feature: str = 'true',
    delta_feature: str = 'true',
    ratio_feature: str = 'false',
    label_mapping: str = 'Label0:0|Label1:1|Label2:2|Label3:3|Label4:4|Label5:5|Label6:6|Label7:7|Label8:8|Label9:9|Label10:10|Label11:11|Label12:12|Label13:13|Label14:14|Label15:15|Label16:16|Label17:17|Label18:18|Label19:19|Label20:20|Label21:21|Label22:22|Label23:23|Label24:24|Label25:25|Label26:26|Label27:27|Label28:28|Label29:29|Label30:30|Label31:31|Label32:32|Label33:33|Label34:34|Label35:35|Label36:36|Label37:37|Label38:38|Label39:39|Label40:40|Label41:41|Label42:42|Label43:43|Label44:44|Label45:45|Label46:46|Label47:47|Label48:48|Label49:49|Label50:50|Label51:51|Label52:52|Label53:53|Label54:54|Label55:55|Label56:56|Label57:57|Label58:58|Label59:59|Label60:60|Label61:61|Label62:62|Label63:63|Label64:64|Label65:65|Label66:66|Label67:67|Label68:68|Label69:69|Label70:70|Label71:71|Label72:72|Label73:73|Label74:74|Label75:75|Label76:76|Label77:77|Label78:78|Label79:79|Label80:80|Label81:81|Label82:82|Label83:83|Label84:84|Label85:85|Label86:86|Label87:87|Label88:88|Label89:89|Label90:90|Label91:91|Label92:92|Label93:93|Label94:94|Label95:95|Label96:96|Label97:97|Label98:98|Label99:99|Label100:100|Label101:101|Label102:102|Label103:103|Label104:104|Label105:105|Label106:106|Label107:107|Label108:108|Label109:109|Label110:110|Label111:111|Label112:112|Label113:113|Label114:114|Label115:115|Label116:116|Label117:117|Label118:118|Label119:119|Label120:120|Label121:121|Label122:122|Label123:123|Label124:124|Label125:125|Label126:126|Label127:127|Label128:128|Label129:129|Label130:130|Label131:131|Label132:132|Label133:133|Label134:134|Label135:135|Label136:136|Label137:137|Label138:138|Label139:139|Label140:140|Label141:141|Label142:142|Label143:143|Label144:144|Label145:145|Label146:146|Label147:147|Label148:148|Label149:149|Label150:150|Label151:151|Label152:152|Label153:153|Label154:154|Label155:155|Label156:156|Label157:157|Label158:158|Label159:159|Label160:160|Label161:161|Label162:162|Label163:163|Label164:164|Label165:165|Label166:166|Label167:167|Label168:168|Label169:169|Label170:170|Label171:171|Label172:172|Label173:173|Label174:174|Label175:175|Label176:176|Label177:177|Label178:178|Label179:179|Label180:180|Label181:181|Label182:182|Label183:183|Label184:184|Label185:185|Label186:186|Label187:187|Label188:188|Label189:189|Label190:190|Label191:191|Label192:192|Label193:193|Label194:194|Label195:195|Label196:196|Label197:197|Label198:198|Label199:199|Label200:200|Label201:201|Label202:202|Label203:203|Label204:204|Label205:205|Label206:206|Label207:207|Label208:208|Label209:209|Label210:210|Label211:211|Label212:212|Label213:213|Label214:214|Label215:215|Label216:216|Label217:217|Label218:218|Label219:219|Label220:220|Label221:221|Label222:222|Label223:223|Label224:224|Label225:225|Label226:226|Label227:227|Label228:228|Label229:229|Label230:230|Label231:231|Label232:232|Label233:233|Label234:234|Label235:235|Label236:236|Label237:237|Label238:238|Label239:239|Label240:240|Label241:241|Label242:242|Label243:243|Label244:244|Label245:245|Label246:246|Label247:247|Label248:248|Label249:249|Label250:250',
    is_for_regression: str = 'true',
    regression_mid_value: int = 127,
    output_equal_rating_pairs: str = 'false',
    output_all_rating0: str = 'true',
    max_num_of_rating0_output: int = 10,
    delta_offset: int = 2147483647,
    non_click_label: int = 40,
    truncate_to_uint_range: str = 'true',
    allow_short_lines: str = 'false',
    hrs_equal_label_rate_fair: float = 0.8,
    hrs_equal_label_rate_bad: float = 0.6,
    training_model: str = 'OFE',
    random_seed: str = '1234',
) -> _UnifiedRankerGeneratePairwiseExtractionFuncComponent:
    """Created from Aether client version: ********, module id:800b20aa-d838-42e3-9581-d28b91a27ea5, module name:[Unified Ranker] Generate Pairwise Extraction and module description Generates pairwise extraction from a regular extraction.   Sorts extraction lines by Query Id and Doc1 Id and Doc2 Id for full reproducibility.
    
    Args:
        header (Data): AnyDirectory
        input_extraction (Data): AnyDirectory
        features (Data): AnyDirectory
        output_header_in_extraction (str): string
        original_feature (str): string
        delta_feature (str): string
        ratio_feature (str): string
        label_mapping (str): string
        is_for_regression (str): string
        regression_mid_value (int): integer
        output_equal_rating_pairs (str): string
        output_all_rating0 (str): string
        max_num_of_rating0_output (int): integer
        delta_offset (int): integer
        non_click_label (int): integer
        truncate_to_uint_range (str): string
        allow_short_lines (str): string
        hrs_equal_label_rate_fair (float): float
        hrs_equal_label_rate_bad (float): float
        training_model (str): string
        random_seed (str): string
    
    Returns:
        _UnifiedRankerGeneratePairwiseExtractionFuncComponent: A component object. This component contains the following output ports:
    
            output_header (Output): AnyDirectory\n
            output_extraction (Output): AnyDirectory\n
    """
    _unified_ranker_generate_pairwise_extraction_func = component_loader.load_component(name='unified_ranker_generate_pairwise_extraction_func')
    return _unified_ranker_generate_pairwise_extraction_func(
            header=header,
            input_extraction=input_extraction,
            features=features,
            output_header_in_extraction=output_header_in_extraction,
            original_feature=original_feature,
            delta_feature=delta_feature,
            ratio_feature=ratio_feature,
            label_mapping=label_mapping,
            is_for_regression=is_for_regression,
            regression_mid_value=regression_mid_value,
            output_equal_rating_pairs=output_equal_rating_pairs,
            output_all_rating0=output_all_rating0,
            max_num_of_rating0_output=max_num_of_rating0_output,
            delta_offset=delta_offset,
            non_click_label=non_click_label,
            truncate_to_uint_range=truncate_to_uint_range,
            allow_short_lines=allow_short_lines,
            hrs_equal_label_rate_fair=hrs_equal_label_rate_fair,
            hrs_equal_label_rate_bad=hrs_equal_label_rate_bad,
            training_model=training_model,
            random_seed=random_seed,)


class _UnifiedRankerGeneratePairwiseExtractionForEvaluationFuncInput:
    header: Input = None
    """AnyDirectory"""
    inputextraction: Input = None
    """AnyDirectory"""
    features: Input = None
    """AnyDirectory"""
    inputisfolder: str = 'false'
    """string"""
    numinputchunks: int = 1
    """integer"""
    metadataoutput2urls: str = 'm:LPSat;m:Effortless;m:Fresh;m:Location;m:Position'
    """string"""
    outputheaderinextraction: str = 'true'
    """string"""
    originalfeature: str = 'true'
    """string"""
    deltafeature: str = 'true'
    """string"""
    ratiofeature: str = 'false'
    """string"""
    deltaoffset: str = '2147483647'
    """string"""
    truncatetouintrange: str = 'true'
    """string"""
    allowshortlines: str = 'false'
    """string"""


class _UnifiedRankerGeneratePairwiseExtractionForEvaluationFuncOutput:
    outputmetadata: Output = None
    """AnyDirectory"""
    outputheader: Output = None
    """AnyDirectory"""
    outputextraction: Output = None
    """AnyDirectory"""


class _UnifiedRankerGeneratePairwiseExtractionForEvaluationFuncComponent(Component):
    inputs: _UnifiedRankerGeneratePairwiseExtractionForEvaluationFuncInput
    outputs: _UnifiedRankerGeneratePairwiseExtractionForEvaluationFuncOutput
    runsettings: _CommandComponentRunsetting


def unified_ranker_generate_pairwise_extraction_for_evaluation_func(
    header: Data = None,
    inputextraction: Data = None,
    features: Data = None,
    inputisfolder: str = 'false',
    numinputchunks: int = 1,
    metadataoutput2urls: str = 'm:LPSat;m:Effortless;m:Fresh;m:Location;m:Position',
    outputheaderinextraction: str = 'true',
    originalfeature: str = 'true',
    deltafeature: str = 'true',
    ratiofeature: str = 'false',
    deltaoffset: str = '2147483647',
    truncatetouintrange: str = 'true',
    allowshortlines: str = 'false',
) -> _UnifiedRankerGeneratePairwiseExtractionForEvaluationFuncComponent:
    """Created from Aether client version: ********, module id:3aefa2f9-6819-4016-86f8-bb982063af58, module name:[Unified Ranker] [Output Metadata] [Output all pairs]Generate Pairwise Extraction and module description Generates pairwise extraction from a regular extraction.   Sorts extraction lines by Query Id and Doc1 Id and Doc2 Id for full reproducibility.
    
    Args:
        header (Data): AnyDirectory
        inputextraction (Data): AnyDirectory
        features (Data): AnyDirectory
        inputisfolder (str): string
        numinputchunks (int): integer
        metadataoutput2urls (str): string
        outputheaderinextraction (str): string
        originalfeature (str): string
        deltafeature (str): string
        ratiofeature (str): string
        deltaoffset (str): string
        truncatetouintrange (str): string
        allowshortlines (str): string
    
    Returns:
        _UnifiedRankerGeneratePairwiseExtractionForEvaluationFuncComponent: A component object. This component contains the following output ports:
    
            outputmetadata (Output): AnyDirectory\n
            outputheader (Output): AnyDirectory\n
            outputextraction (Output): AnyDirectory\n
    """
    _unified_ranker_generate_pairwise_extraction_for_evaluation_func = component_loader.load_component(name='unified_ranker_generate_pairwise_extraction_for_evaluation_func')
    return _unified_ranker_generate_pairwise_extraction_for_evaluation_func(
            header=header,
            inputextraction=inputextraction,
            features=features,
            inputisfolder=inputisfolder,
            numinputchunks=numinputchunks,
            metadataoutput2urls=metadataoutput2urls,
            outputheaderinextraction=outputheaderinextraction,
            originalfeature=originalfeature,
            deltafeature=deltafeature,
            ratiofeature=ratiofeature,
            deltaoffset=deltaoffset,
            truncatetouintrange=truncatetouintrange,
            allowshortlines=allowshortlines,)


class _UnifiedRankerReplaceDeltaFreeformsWithPlaceHoldersFuncInput:
    input: Input = None
    """AnyDirectory"""


class _UnifiedRankerReplaceDeltaFreeformsWithPlaceHoldersFuncOutput:
    output: Output = None
    """AnyDirectory"""


class _UnifiedRankerReplaceDeltaFreeformsWithPlaceHoldersFuncComponent(Component):
    inputs: _UnifiedRankerReplaceDeltaFreeformsWithPlaceHoldersFuncInput
    outputs: _UnifiedRankerReplaceDeltaFreeformsWithPlaceHoldersFuncOutput
    runsettings: _CommandComponentRunsetting


def unified_ranker_replace_delta_freeforms_with_place_holders_func(
    input: Data = None,
) -> _UnifiedRankerReplaceDeltaFreeformsWithPlaceHoldersFuncComponent:
    """Created from Aether client version: ********, module id:dea98733-a178-4fe1-ab50-f6d2cb11de5a, module name:[Unified Ranker] Replace Delta FreeForms with Place Holders and module description Replace Delta FreeForms with Place Holders.  Assumes any FreeForm that's present must be a Delta feature.  Uses NeuralTreeEvaluator.dll to parse the expressions.
    
    Args:
        input (Data): AnyDirectory
    
    Returns:
        _UnifiedRankerReplaceDeltaFreeformsWithPlaceHoldersFuncComponent: A component object. This component contains the following output ports:
    
            output (Output): AnyDirectory\n
    """
    _unified_ranker_replace_delta_freeforms_with_place_holders_func = component_loader.load_component(name='unified_ranker_replace_delta_freeforms_with_place_holders_func')
    return _unified_ranker_replace_delta_freeforms_with_place_holders_func(
            input=input,)


# No datasets class is generated.
