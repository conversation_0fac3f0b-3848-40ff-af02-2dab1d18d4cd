$schema: https://componentsdk.azureedge.net/jsonschema/CommandComponent.json
command: echo "DUMMY COMPONENT"
description: 'Created from Aether client version: ********, module id:777d1fc1-a7d8-4381-806e-89797df53ad2,
  module name:[Unified Ranker] CHashCodeToL3CodeForPWML with Delta FreeForm as input
  and module description Like 75eff9ae-7929-4bb4-8efd-9ba4212fd602 but with the option
  to specify the Delta Freeform and Delta Header as input'
display_name: '[Unified Ranker] CHashCodeToL3CodeForPWML with Delta FreeForm as Input'
environment:
  name: env_for_dcm_testing
inputs:
  deltaheader:
    optional: true
    type: String
  deltatemplate:
    optional: true
    type: String
  input:
    optional: false
    type: AnyDirectory
  tabscnt:
    optional: false
    type: Integer
is_deterministic: true
name: unified_ranker_chashcodetol3codeforpwml_with_delta_freeform_as_input_5
outputs:
  output:
    type: AnyDirectory
type: CommandComponent
version: 0.5
