# ------------------------------------------------------------------------------
# This is generated from https://ml.azure.com/experiments/id/757f3391-a0a0-49d3-8780-99a744f72a99/runs/ff039288-a2ca-48e4-805e-25e6a4f5c65d?wsid=/subscriptions/b8c23406-f9b5-4ccb-8a65-a8cb5dcd6a5a/resourcegroups/dcm-westus2/workspaces/dcm-fastorc-test
# To run this code, please install SDK by this command:
# pip install "azure-ml-component[notebooks]>=0.9.5" --upgrade
# More detailed guide to set up your environment: https://componentsdk.azurewebsites.net/getting_started.html
# ------------------------------------------------------------------------------
from azure.ml.component import Pipeline, dsl
from azure.ml.component.dsl.types import Integer, String


from ...components import calculate_rmse_func
from ...components import extract_feature_importance_func


from .generate_fastrank_ini_with_pw_features_with_delta_freeforms_0 import generate_fastrank_ini_with_pw_features_with_delta_freeforms_0_func
from .generate_pairwise_extraction_0 import generate_pairwise_extraction_0_func
from .lightgbm_inference_on_aml_0 import lightgbm_inference_on_aml_0_func
from .lightgbm_training_and_convert_to_fastrank_0 import lightgbm_training_and_convert_to_fastrank_0_func
from .prepare_training_extraction_0 import prepare_training_extraction_0_func
from .sample_ofe_1 import sample_ofe_1_func


# define pipeline
@dsl.pipeline(
    name='train_pairwise_model_select_top_feature_lightgbm',
    description='Train Pairwise Model (Select Top Feature) (LightGBM)',
)
def train_pairwise_model_select_top_feature_lightgbm_func(
    label_mapping: String(optional=False)=None,
    label_gain: String(optional=False)=None,
    top_n: Integer(optional=True)=None,
    run_id: String(optional=False)=None,
    num_test_chunks=None,
    training_nodes=None,
    header=None,
    training_data=None,
    features=None,
) -> Pipeline:
    generate_pw_train_extraction_step = generate_pairwise_extraction_0_func(
        input_header=header,
        input_extraction=training_data,
        features=features,
        original_feature='true',
        delta_feature='false',
        ratio_feature='false',
        is_for_regression='true',
        regression_mid_value=127,
        output_all_rating0='false',
        delta_offset=10000000,
        label_mapping=label_mapping,
    )

    sample_ofe_step = sample_ofe_1_func(
        input_extraction=training_data,
        input_extraction_header=header,
        sample_rate=0.75,
        seed=12388,
        num_partitions=-1,
    )

    generate_pw_test_extraction_step = generate_pairwise_extraction_0_func(
        input_header=header,
        features=features,
        input_extraction=sample_ofe_step.outputs.output_others_extraction,
        original_feature='true',
        delta_feature='false',
        ratio_feature='false',
        is_for_regression='true',
        regression_mid_value=127,
        output_all_rating0='false',
        delta_offset=10000000,
        label_mapping=label_mapping,
    )

    generate_features_step = generate_fastrank_ini_with_pw_features_with_delta_freeforms_0_func(
        raw_features=features,
    )

    prepare_extraction_step = prepare_training_extraction_0_func(
        extraction_header=generate_pw_train_extraction_step.outputs.header,
        train_partitions_content=generate_pw_train_extraction_step.outputs.extraction,
        test_partitions_content=generate_pw_test_extraction_step.outputs.extraction,
        input_ini_file=generate_features_step.outputs.pw_features,
        training_nodes=1,
        run_id=run_id,
        num_test_chunks=num_test_chunks,
        tsv2bin_custom_label_to_rate_mapping='"{}"'.format(label_mapping),
    )

    lgbm_train_and_convert_to_fastrank_step = lightgbm_training_and_convert_to_fastrank_0_func(
        train_data_dir=prepare_extraction_step.outputs.train_data_dir,
        test_data_dir=prepare_extraction_step.outputs.test_data_dir,
        smoothed_training_ini=prepare_extraction_step.outputs.smoothed_training_ini,
        parallel_tree_learner='data_parallel',
        training_nodes=1,
        label_gain=label_gain,
        run_id=run_id,
        num_test_chunks=num_test_chunks,
    )

    lightgbm_inference_step = lightgbm_inference_on_aml_0_func(
        lgbm_model=lgbm_train_and_convert_to_fastrank_step.outputs.output_model,
        test_data_dir=prepare_extraction_step.outputs.test_data_dir,
        test_data_file_name='File_{0}.txt',
    )

    calculate_rmse_step = calculate_rmse_func(
        predicts_folder=lightgbm_inference_step.outputs.predicts,
        ratings_folder=prepare_extraction_step.outputs.test_data_meta_dir,
    )
    calculate_rmse_step.outputs.output.configure(
        mode='mount',
    )

    extract_feature_importance_step = extract_feature_importance_func(
        input=lgbm_train_and_convert_to_fastrank_step.outputs.fastrank_model,
        top_n=top_n,
    )
    extract_feature_importance_step.outputs.feature_importance_all.configure(
        mode='mount',
    )
    extract_feature_importance_step.outputs.raw_feature_importance.configure(
        mode='mount',
    )
    extract_feature_importance_step.outputs.features.configure(
        mode='mount',
    )

    return {
        'output_model': lgbm_train_and_convert_to_fastrank_step.outputs.output_model,
        'fastrank_model': lgbm_train_and_convert_to_fastrank_step.outputs.fastrank_model,
        'regressor_rmse': calculate_rmse_step.outputs.output,
        'raw_feature_importance': extract_feature_importance_step.outputs.raw_feature_importance,
        'top_features': extract_feature_importance_step.outputs.features,
    }
