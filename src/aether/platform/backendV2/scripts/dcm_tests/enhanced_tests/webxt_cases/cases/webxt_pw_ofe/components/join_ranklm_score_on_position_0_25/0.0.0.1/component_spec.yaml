$schema: https://componentsdk.azureedge.net/jsonschema/CommandComponent.json
command: echo "DUMMY COMPONENT"
description: 'Created from Aether client version: ********, module id: 5e685b0d-02ed-4e01-aeab-c869995f5801
  module name: <PERSON><PERSON> on Position, 0-25'
display_name: <PERSON><PERSON> on Position, 0-25
environment:
  name: env_for_dcm_testing
inputs:
  extra_extraction:
    optional: false
    type: AnyDirectory
  input_extraction:
    optional: false
    type: AnyDirectory
  input_header:
    optional: false
    type: AnyDirectory
  num_input_partitions:
    default: 1000
    optional: false
    type: Integer
  num_output_partitions:
    default: 1000
    optional: false
    type: Integer
is_deterministic: true
name: join_ranklm_score_on_position_0_25
outputs:
  extraction:
    type: AnyDirectory
  header:
    type: AnyDirectory
type: CommandComponent
version: *******
