# ------------------------------------------------------------------------------
# This is generated from https://ml.azure.com/experiments/id/757f3391-a0a0-49d3-8780-99a744f72a99/runs/ff039288-a2ca-48e4-805e-25e6a4f5c65d?wsid=/subscriptions/b8c23406-f9b5-4ccb-8a65-a8cb5dcd6a5a/resourcegroups/dcm-westus2/workspaces/dcm-fastorc-test
# To run this code, please install SDK by this command:
# pip install "azure-ml-component[notebooks]>=0.9.5" --upgrade
# More detailed guide to set up your environment: https://componentsdk.azurewebsites.net/getting_started.html
# ------------------------------------------------------------------------------
from azure.ml.component import Pipeline, dsl
from azure.ml.component.dsl.types import Integer, String


from ...components import convert_lightgbm_model_to_bing_ini_func


from .lightgbm_training_on_aml_2 import lightgbm_training_on_aml_2_func


# define pipeline
@dsl.pipeline(
    name='lightgbm_training_and_convert_to_fastrank_2',
    description='LightGBM Training and Convert to FastRank',
)
def lightgbm_training_and_convert_to_fastrank_2_func(
    label_gain: String(optional=False)=None,
    run_id: String(optional=False)=None,
    parallel_tree_learner: String(optional=False)=None,
    training_nodes: Integer(optional=False)=None,
    num_test_chunks: Integer(optional=False)=None,
    train_data_dir=None,
    test_data_dir=None,
    smoothed_training_ini=None,
) -> Pipeline:
    lgbm_training_step = lightgbm_training_on_aml_2_func(
        train_data_dir=train_data_dir,
        test_data_dir=test_data_dir,
        train_data_file_name='File_{0}.txt',
        test_data_file_name='File_{0}.txt',
        query_column='0',
        weight_file_column='-1',
        enable_mlflow=True,
        mlflow_parent_run=True,
        continued_training_model='NotSet',
        num_test_chunks=num_test_chunks,
        label_gain=label_gain,
        parallel_tree_learner=parallel_tree_learner,
        rerun_key=run_id,
        node_count=training_nodes,
    )

    convert_lgbm_to_bing_ini_step = convert_lightgbm_model_to_bing_ini_func(
        lightgbm_model=lgbm_training_step.outputs.output_model,
        features_ini=smoothed_training_ini,
    )
    convert_lgbm_to_bing_ini_step.outputs.bing_ini.configure(
        mode='mount',
    )

    return {
        'output_logs_zip': lgbm_training_step.outputs.output_logs_zip,
        'output_metric_of_last_iteration': lgbm_training_step.outputs.output_metric_of_last_iteration,
        'output_metric_plot': lgbm_training_step.outputs.output_metric_plot,
        'output_metrics': lgbm_training_step.outputs.output_metrics,
        'output_model': lgbm_training_step.outputs.output_model,
        'output_portal_url': lgbm_training_step.outputs.output_portal_url,
        'fastrank_model': convert_lgbm_to_bing_ini_step.outputs.bing_ini,
    }
