$schema: https://componentsdk.azureedge.net/jsonschema/CommandComponent.json
command: echo "DUMMY COMPONENT"
description: 'Created from Aether client version: ********, module id:0b501e1f-be25-4d0f-af19-74db7afea2ae,
  module name:Add QueryId for SStream and module description Add QueryId for SStream'
display_name: Prepare OneDCG Data
environment:
  name: env_for_dcm_testing
inputs:
  onedcg_title_snippet:
    type: AnyDirectory
  onedcg_urp:
    type: AnyDirectory
  removecolumnparams:
    default: '""'
    optional: false
    type: String
  renamecolumnparams:
    default: '""'
    optional: false
    type: String
is_deterministic: true
name: prepare_onedcg_data
outputs:
  output_distinct_queries:
    type: AnyDirectory
  output_extraction:
    type: AnyDirectory
type: CommandComponent
version: 0.1
