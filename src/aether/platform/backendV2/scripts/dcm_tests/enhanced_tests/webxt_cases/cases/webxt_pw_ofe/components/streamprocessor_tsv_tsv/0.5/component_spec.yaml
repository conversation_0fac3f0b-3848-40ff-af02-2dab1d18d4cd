$schema: https://componentsdk.azureedge.net/jsonschema/CommandComponent.json
command: echo "DUMMY COMPONENT"
description: 'Created from Aether client version: ********, module id:c8cbd966-3e92-4eb9-8adb-4685d70d9eb9,
  module name:StreamProcessor and module description stream processor, support sstream,
  modified from yongzhi''s. v0.2, support aggregate path'
display_name: StreamProcessor (tsv & tsv)
environment:
  name: env_for_dcm_testing
inputs:
  comment:
    optional: true
    type: String
  csharpcode:
    default: //
    optional: false
    type: String
  directive1:
    default: //
    optional: false
    type: String
  directive2:
    default: //
    optional: false
    type: String
  extractparam:
    default: '"-silent"'
    optional: false
    type: String
  input1:
    optional: false
    type: AnyDirectory
  input1isfolder:
    default: false
    description: Whether input1 is folder (in case it is connected from CommandComponent,
      the output is folder)
    enum:
    - true
    - false
    optional: false
    type: Enum
  input2:
    optional: true
    type: AnyDirectory
  input2isfolder:
    default: false
    description: Whether input1 is folder (in case it is connected from CommandComponent,
      the output is folder)
    enum:
    - true
    - false
    optional: false
    type: Enum
  output1issstream:
    default: false
    enum:
    - true
    - false
    optional: false
    type: Enum
  output1param:
    optional: true
    type: String
  output2issstream:
    default: false
    enum:
    - true
    - false
    optional: false
    type: Enum
  output2param:
    optional: true
    type: String
  schema1:
    optional: true
    type: String
  schema2:
    optional: true
    type: String
  scopecode:
    optional: false
    type: String
is_deterministic: true
name: streamprocessor_tsv_tsv
outputs:
  output1:
    type: AnyDirectory
  output2:
    type: AnyDirectory
type: CommandComponent
version: 0.5
