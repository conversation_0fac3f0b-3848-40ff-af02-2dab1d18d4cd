$schema: https://componentsdk.azureedge.net/jsonschema/CommandComponent.json
command: echo "DUMMY COMPONENT"
description: 'Created from Aether client version: ********, module id:b7151a17-334c-450f-be48-cfa229e361d4,
  module name:[BigExtractions] Append Ranker Scores (Partitioned) [support MinScore/MaxScore
  cutoff] and module description Append ranker scores for OFE extraction. Support
  scaling and translation. The new column name for ranker score should start with
  "m:".  Adds unzip.exe as an explicit resource (seems that some Cosmos machines no
  longer have it accessible by default)'
display_name: '[BigExtractions] Append Ranker Scores (Partitioned) [support MinScore/MaxScore
  cutoff]'
environment:
  name: env_for_dcm_testing
inputs:
  columnname:
    default: m:RankScore
    optional: false
    type: String
  input_header:
    is_resource: true
    optional: false
    type: AnyDirectory
  input_partitions:
    optional: false
    type: AnyDirectory
  maxscore:
    default: 100
    optional: false
    type: Integer
  minscore:
    default: -100
    optional: false
    type: Integer
  numoutputchunks:
    default: 1000
    optional: false
    type: Integer
  partitions_is_folder:
    default: true
    optional: false
    type: String
  scalingfactor:
    default: 10000
    optional: false
    type: Integer
  seedrankers:
    is_resource: true
    optional: false
    type: AnyDirectory
  translationfactor:
    default: 100
    optional: false
    type: Integer
is_deterministic: true
name: bigextractions_append_ranker_scores_partitioned_support_minscore_maxscore_cutoff
outputs:
  header:
    type: AnyDirectory
  partitions:
    type: AnyDirectory
type: CommandComponent
version: 1.1
