$schema: https://componentsdk.azureedge.net/jsonschema/CommandComponent.json
command: echo "DUMMY COMPONENT"
description: 'Created from Aether client version: ********, module id:800b20aa-d838-42e3-9581-d28b91a27ea5,
  module name:[Unified Ranker] Generate Pairwise Extraction and module description
  Generates pairwise extraction from a regular extraction.   Sorts extraction lines
  by Query Id and Doc1 Id and Doc2 Id for full reproducibility.'
display_name: '[Unified Ranker] Generate Pairwise Extraction'
environment:
  name: env_for_dcm_testing
inputs:
  allow_short_lines:
    default: false
    optional: false
    type: String
  delta_feature:
    default: true
    optional: false
    type: String
  delta_offset:
    default: 2147483647
    optional: false
    type: Integer
  features:
    is_resource: true
    optional: false
    type: AnyDirectory
  header:
    is_resource: true
    optional: false
    type: AnyDirectory
  hrs_equal_label_rate_bad:
    default: 0.6
    optional: false
    type: Float
  hrs_equal_label_rate_fair:
    default: 0.8
    optional: false
    type: Float
  input_extraction:
    optional: false
    type: AnyDirectory
  is_for_regression:
    default: true
    optional: false
    type: String
  label_mapping:
    default: Label0:0|Label1:1|Label2:2|Label3:3|Label4:4|Label5:5|Label6:6|Label7:7|Label8:8|Label9:9|Label10:10|Label11:11|Label12:12|Label13:13|Label14:14|Label15:15|Label16:16|Label17:17|Label18:18|Label19:19|Label20:20|Label21:21|Label22:22|Label23:23|Label24:24|Label25:25|Label26:26|Label27:27|Label28:28|Label29:29|Label30:30|Label31:31|Label32:32|Label33:33|Label34:34|Label35:35|Label36:36|Label37:37|Label38:38|Label39:39|Label40:40|Label41:41|Label42:42|Label43:43|Label44:44|Label45:45|Label46:46|Label47:47|Label48:48|Label49:49|Label50:50|Label51:51|Label52:52|Label53:53|Label54:54|Label55:55|Label56:56|Label57:57|Label58:58|Label59:59|Label60:60|Label61:61|Label62:62|Label63:63|Label64:64|Label65:65|Label66:66|Label67:67|Label68:68|Label69:69|Label70:70|Label71:71|Label72:72|Label73:73|Label74:74|Label75:75|Label76:76|Label77:77|Label78:78|Label79:79|Label80:80|Label81:81|Label82:82|Label83:83|Label84:84|Label85:85|Label86:86|Label87:87|Label88:88|Label89:89|Label90:90|Label91:91|Label92:92|Label93:93|Label94:94|Label95:95|Label96:96|Label97:97|Label98:98|Label99:99|Label100:100|Label101:101|Label102:102|Label103:103|Label104:104|Label105:105|Label106:106|Label107:107|Label108:108|Label109:109|Label110:110|Label111:111|Label112:112|Label113:113|Label114:114|Label115:115|Label116:116|Label117:117|Label118:118|Label119:119|Label120:120|Label121:121|Label122:122|Label123:123|Label124:124|Label125:125|Label126:126|Label127:127|Label128:128|Label129:129|Label130:130|Label131:131|Label132:132|Label133:133|Label134:134|Label135:135|Label136:136|Label137:137|Label138:138|Label139:139|Label140:140|Label141:141|Label142:142|Label143:143|Label144:144|Label145:145|Label146:146|Label147:147|Label148:148|Label149:149|Label150:150|Label151:151|Label152:152|Label153:153|Label154:154|Label155:155|Label156:156|Label157:157|Label158:158|Label159:159|Label160:160|Label161:161|Label162:162|Label163:163|Label164:164|Label165:165|Label166:166|Label167:167|Label168:168|Label169:169|Label170:170|Label171:171|Label172:172|Label173:173|Label174:174|Label175:175|Label176:176|Label177:177|Label178:178|Label179:179|Label180:180|Label181:181|Label182:182|Label183:183|Label184:184|Label185:185|Label186:186|Label187:187|Label188:188|Label189:189|Label190:190|Label191:191|Label192:192|Label193:193|Label194:194|Label195:195|Label196:196|Label197:197|Label198:198|Label199:199|Label200:200|Label201:201|Label202:202|Label203:203|Label204:204|Label205:205|Label206:206|Label207:207|Label208:208|Label209:209|Label210:210|Label211:211|Label212:212|Label213:213|Label214:214|Label215:215|Label216:216|Label217:217|Label218:218|Label219:219|Label220:220|Label221:221|Label222:222|Label223:223|Label224:224|Label225:225|Label226:226|Label227:227|Label228:228|Label229:229|Label230:230|Label231:231|Label232:232|Label233:233|Label234:234|Label235:235|Label236:236|Label237:237|Label238:238|Label239:239|Label240:240|Label241:241|Label242:242|Label243:243|Label244:244|Label245:245|Label246:246|Label247:247|Label248:248|Label249:249|Label250:250
    optional: false
    type: String
  max_num_of_rating0_output:
    default: 10
    optional: false
    type: Integer
  non_click_label:
    default: 40
    optional: false
    type: Integer
  original_feature:
    default: true
    optional: false
    type: String
  output_all_rating0:
    default: true
    optional: false
    type: String
  output_equal_rating_pairs:
    default: false
    optional: false
    type: String
  output_header_in_extraction:
    default: true
    optional: false
    type: String
  random_seed:
    default: 1234
    optional: false
    type: String
  ratio_feature:
    default: false
    optional: false
    type: String
  regression_mid_value:
    default: 127
    optional: false
    type: Integer
  training_model:
    default: OFE
    optional: false
    type: String
  truncate_to_uint_range:
    default: true
    optional: false
    type: String
is_deterministic: true
name: unified_ranker_generate_pairwise_extraction
outputs:
  output_extraction:
    type: AnyDirectory
  output_header:
    type: AnyDirectory
type: CommandComponent
version: 2.1.8
