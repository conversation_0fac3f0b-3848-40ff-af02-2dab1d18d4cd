$schema: https://componentsdk.azureedge.net/jsonschema/CommandComponent.json
command: echo "DUMMY COMPONENT"
description: This component does the following in order, skipping any step that does
  not apply (empty lines are skipped). 0. Load input file content 1. Load input1 file
  content 2. Add lines to input file 3. Remove lines from input file 4. Sort lines
  5. Dedup lines
display_name: Add, Remove, Sort, Dedup
environment:
  name: env_for_dcm_testing
inputs:
  add:
    description: Json format string of line list to add to input file
    optional: true
    type: String
  custom_order:
    description: Json format string of line list keep the exact order
    optional: true
    type: String
  dedup:
    default: true
    description: Whether to dedup lines
    optional: false
    type: Boolean
  ignore_case:
    default: false
    description: Whether to ignore case for sort and dedup
    optional: false
    type: Boolean
  input:
    optional: false
    type: AnyDirectory
  input1:
    description: A text file to add to input file
    optional: true
    type: AnyDirectory
  input2:
    description: Second text file to add to input file
    optional: true
    type: AnyDirectory
  match_mode:
    default: search
    description: Pattern match mode for remove list
    enum:
    - search
    - match
    - fullmatch
    optional: false
    type: Enum
  remove:
    description: Json format string of line/regex list to remove from input file
    optional: true
    type: String
  sort:
    default: true
    description: Whether to sort lines
    optional: false
    type: Boolean
is_deterministic: true
name: add_remove_sort_dedup
outputs:
  output:
    type: AnyDirectory
type: CommandComponent
version: 0.1
