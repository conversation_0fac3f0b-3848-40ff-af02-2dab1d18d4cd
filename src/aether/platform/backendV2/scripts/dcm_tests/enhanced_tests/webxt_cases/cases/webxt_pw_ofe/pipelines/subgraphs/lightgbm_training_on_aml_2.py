# ------------------------------------------------------------------------------
# This is generated from https://ml.azure.com/experiments/id/757f3391-a0a0-49d3-8780-99a744f72a99/runs/ff039288-a2ca-48e4-805e-25e6a4f5c65d?wsid=/subscriptions/b8c23406-f9b5-4ccb-8a65-a8cb5dcd6a5a/resourcegroups/dcm-westus2/workspaces/dcm-fastorc-test
# To run this code, please install SDK by this command:
# pip install "azure-ml-component[notebooks]>=0.9.5" --upgrade
# More detailed guide to set up your environment: https://componentsdk.azurewebsites.net/getting_started.html
# ------------------------------------------------------------------------------
from azure.ml.component import Pipeline, dsl
from azure.ml.component.dsl.types import Boolean, Integer, String


from ...components import lightgbm_training_func


# define pipeline
@dsl.pipeline(
    name='lightgbm_training_on_aml_2',
    description='Running LightGBM Training on AML',
)
def lightgbm_training_on_aml_2_func(
    num_test_chunks: Integer(optional=False)=None,
    train_data_file_name: String(optional=False)=None,
    test_data_file_name: String(optional=False)=None,
    label_gain: String(optional=False)=None,
    parallel_tree_learner: String(optional=False)=None,
    query_column: String(optional=False)=None,
    weight_file_column: String(optional=False)=None,
    enable_mlflow: Boolean(optional=False)=None,
    mlflow_parent_run: Boolean(optional=False)=None,
    rerun_key: String(optional=False)=None,
    continued_training_model: String(optional=False)=None,
    node_count: Integer(optional=False)=None,
    train_data_dir=None,
    test_data_dir=None,
    continued_training_model_dir=None,
) -> Pipeline:
    lightgbm_training_step = lightgbm_training_func(
        continued_training_model_dir=continued_training_model_dir,
        test_data_dir=test_data_dir,
        train_data_dir=train_data_dir,
        additional_lgbm_params='enable_bundle=false',
        feature_fraction=1.0,
        learning_rate=0.26,
        max_bin=1023,
        metric='rmse,ndcg',
        min_data_in_leaf=3750,
        num_leaves=250,
        num_trees=60,
        objective='regression',
        aml_run_node_count=node_count,
        continued_training_model=continued_training_model,
        enable_mlflow=enable_mlflow,
        label_gain=label_gain,
        mlflow_parent_run=mlflow_parent_run,
        num_test_chunks=num_test_chunks,
        parallel_tree_learner=parallel_tree_learner,
        query_column=query_column,
        rerun_key=rerun_key,
        test_data_file_name=test_data_file_name,
        train_data_file_name=train_data_file_name,
        weight_file_column=weight_file_column,
    )
    lightgbm_training_step.outputs.output_logs_zip.configure(
        mode='mount',
    )
    lightgbm_training_step.outputs.output_metric_of_last_iteration.configure(
        mode='mount',
    )
    lightgbm_training_step.outputs.output_metric_plot.configure(
        mode='mount',
    )
    lightgbm_training_step.outputs.output_metrics.configure(
        mode='mount',
    )
    lightgbm_training_step.outputs.output_model.configure(
        mode='mount',
    )
    lightgbm_training_step.outputs.output_portal_url.configure(
        mode='mount',
    )

    return {
        'output_logs_zip': lightgbm_training_step.outputs.output_logs_zip,
        'output_metric_of_last_iteration': lightgbm_training_step.outputs.output_metric_of_last_iteration,
        'output_metric_plot': lightgbm_training_step.outputs.output_metric_plot,
        'output_metrics': lightgbm_training_step.outputs.output_metrics,
        'output_model': lightgbm_training_step.outputs.output_model,
        'output_portal_url': lightgbm_training_step.outputs.output_portal_url,
    }
