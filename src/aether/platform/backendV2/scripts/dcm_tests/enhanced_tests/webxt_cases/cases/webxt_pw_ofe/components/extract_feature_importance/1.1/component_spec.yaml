$schema: https://componentsdk.azureedge.net/jsonschema/CommandComponent.json
command: echo "DUMMY COMPONENT"
display_name: Extract Feature Importance
environment:
  name: env_for_dcm_testing
inputs:
  exclude_patterns:
    description: Json format list of regex patterns to exclude
    optional: true
    type: String
  input:
    optional: false
    type: AnyDirectory
  top_n:
    description: Keep only top N
    optional: true
    type: Integer
is_deterministic: true
name: extract_feature_importance
outputs:
  feature_importance_all:
    type: AnyDirectory
  features:
    type: AnyDirectory
  raw_feature_importance:
    type: AnyDirectory
type: CommandComponent
version: 1.1
