$schema: https://componentsdk.azureedge.net/jsonschema/CommandComponent.json
command: echo "DUMMY COMPONENT"
description: 'Created from Aether client version: ********, module id:75bef050-b67f-4051-a3e5-4771210f9e82,
  module name:Create Pairwise Delta FreeForms and module description Create Pairwise
  Delta FreeForms'
display_name: Create Pairwise Delta FreeForms
environment:
  name: env_for_dcm_testing
inputs:
  delta_template:
    default: (- {left} {right})
    optional: true
    type: String
  input:
    optional: false
    type: AnyDirectory
  left_template:
    default: L_{feature}
    optional: true
    type: String
  right_template:
    default: R_{feature}
    optional: true
    type: String
is_deterministic: true
name: create_pairwise_delta_freeforms
outputs:
  output:
    type: AnyDirectory
type: CommandComponent
version: 0.3
