$schema: https://componentsdk.azureedge.net/jsonschema/CommandComponent.json
command: echo "DUMMY COMPONENT"
description: 'Created from Aether client version: ********, module id:26e6accf-2703-420e-a624-0f9394f82426,
  module name:SS To Generic TSV (Base64) and module description Convert SS to a generic
  tsv and a schema file. The binary will be base64 converted. the /t /r /n in string
  will be converted to #TAB# #R# #N#'
display_name: SS to Generic TSV (Base64)
environment:
  name: env_for_dcm_testing
inputs:
  sspath:
    optional: false
    type: AnyDirectory
is_deterministic: true
name: ss_to_generic_tsv_base64
outputs:
  schemafile_tsvpath:
    type: AnyDirectory
  tsvpath:
    type: AnyDirectory
type: CommandComponent
version: *******
