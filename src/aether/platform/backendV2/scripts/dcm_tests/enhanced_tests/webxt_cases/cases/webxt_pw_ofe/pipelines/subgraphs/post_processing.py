# ------------------------------------------------------------------------------
# This is generated from https://ml.azure.com/experiments/id/757f3391-a0a0-49d3-8780-99a744f72a99/runs/ff039288-a2ca-48e4-805e-25e6a4f5c65d?wsid=/subscriptions/b8c23406-f9b5-4ccb-8a65-a8cb5dcd6a5a/resourcegroups/dcm-westus2/workspaces/dcm-fastorc-test
# To run this code, please install SDK by this command:
# pip install "azure-ml-component[notebooks]>=0.9.5" --upgrade
# More detailed guide to set up your environment: https://componentsdk.azurewebsites.net/getting_started.html
# ------------------------------------------------------------------------------
from azureml.core import Workspace
from azureml.core import Dataset
from azure.ml.component import Pipeline, dsl
from azure.ml.component.dsl.types import String


from ...components import add_freeforms_to_ur_freeform_func
from ...components import add_line_number_func
from ...components import add_remove_sort_dedup_func
from ...components import as_online_ranker_gen_fastrank_to_code_func
from ...components import pw_feature_renaming_func
from ...components import unified_ranker_chashcodetol3codeforpwml_with_delta_freeform_as_input_5_func
from ...components import unified_ranker_replace_delta_freeforms_with_place_holders_func


from .ranker_rename_1 import ranker_rename_1_func
from .ranker_rename_2 import ranker_rename_2_func


# get dataset
titanic_csv = Dataset.File.from_files("https://dprepdata.blob.core.windows.net/demo/Titanic.csv")


# define pipeline
@dsl.pipeline(
    name='post_processing',
    description='Post Processing',
)
def post_processing_func(
    delta_freeform_template=None,
    fastrank_model=None,
    affinity_freeforms=None,
) -> Pipeline:
    ranker_rename_before_step = ranker_rename_1_func(
        input_ranker=fastrank_model,
    )

    replace_with_placeholder_step = unified_ranker_replace_delta_freeforms_with_place_holders_func(
        input=ranker_rename_before_step.outputs.output_ranker,
    )
    replace_with_placeholder_step.outputs.output.configure(
        mode='mount',
    )

    pw_feature_renaming_step = pw_feature_renaming_func(
        input=replace_with_placeholder_step.outputs.output,
    )
    pw_feature_renaming_step.outputs.output.configure(
        mode='mount',
    )

    fastrank_to_code_step = as_online_ranker_gen_fastrank_to_code_func(
        inputini=pw_feature_renaming_step.outputs.output,
    )
    fastrank_to_code_step.outputs.code_file.configure(
        mode='mount',
    )
    fastrank_to_code_step.outputs.features_list.configure(
        mode='mount',
    )

    c_hash_code_to_l3_step = unified_ranker_chashcodetol3codeforpwml_with_delta_freeform_as_input_5_func(
        input=fastrank_to_code_step.outputs.code_file,
        tabscnt=1,
        deltatemplate=delta_freeform_template,
    )
    c_hash_code_to_l3_step.outputs.output.configure(
        mode='mount',
    )

    concat_step_0 = add_remove_sort_dedup_func(
        input=titanic_csv,
        input1=c_hash_code_to_l3_step.outputs.output,
        input2=titanic_csv,
        sort=False,
        dedup=False,
    )
    concat_step_0.outputs.output.configure(
        mode='mount',
    )

    add_line_number_step = add_line_number_func(
        input=concat_step_0.outputs.output,
    )
    add_line_number_step.outputs.output.configure(
        mode='mount',
    )

    concat_step_1 = add_remove_sort_dedup_func(
        input=titanic_csv,
        input1=add_line_number_step.outputs.output,
        input2=titanic_csv,
        sort=False,
        dedup=False,
    )
    concat_step_1.outputs.output.configure(
        mode='mount',
    )

    add_term_based_freeform_step = add_freeforms_to_ur_freeform_func(
        input=concat_step_1.outputs.output,
        freeforms=titanic_csv,
        freeform_prefix='TermBasedFreeForm',
    )
    add_term_based_freeform_step.outputs.output.configure(
        mode='mount',
    )

    add_affinity_freeform_step = add_freeforms_to_ur_freeform_func(
        input=add_term_based_freeform_step.outputs.output,
        freeforms=affinity_freeforms,
    )
    add_affinity_freeform_step.outputs.output.configure(
        mode='mount',
    )

    ranker_rename_after_step = ranker_rename_2_func(
        input_ranker=add_affinity_freeform_step.outputs.output,
    )

    return {
        'model_for_checkin': ranker_rename_after_step.outputs.output_ranker,
    }
