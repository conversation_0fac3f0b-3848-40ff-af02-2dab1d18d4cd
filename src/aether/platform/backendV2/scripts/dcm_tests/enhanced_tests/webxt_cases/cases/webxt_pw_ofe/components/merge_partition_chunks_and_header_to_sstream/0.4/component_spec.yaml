$schema: https://componentsdk.azureedge.net/jsonschema/CommandComponent.json
command: echo "DUMMY COMPONENT"
description: 'Created from Aether client version: ********, module id:dc2aedd1-0947-4e92-8803-9a46f45d1709,
  module name:Merge TSV Files to SStream and module description Merge TSV Files to
  SStream'
display_name: Merge Partition Chunks and Header To AnyDirectory
environment:
  name: env_for_dcm_testing
inputs:
  header:
    is_resource: true
    optional: false
    type: AnyDirectory
  input:
    optional: false
    type: AnyDirectory
  inputisfolder:
    default: true
    optional: false
    type: String
  numinputchunks:
    default: 1000
    optional: false
    type: Integer
is_deterministic: true
name: merge_partition_chunks_and_header_to_sstream
outputs:
  output:
    type: AnyDirectory
type: CommandComponent
version: 0.4
