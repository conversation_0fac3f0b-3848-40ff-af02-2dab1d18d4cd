$schema: https://componentsdk.azureedge.net/jsonschema/CommandComponent.json
command: echo "DUMMY COMPONENT"
description: 'Created from Aether client version: ******** module id: 4287fb2e-286e-4c4b-9be1-192cb0fb22f5
  module name: Join Feature Score module description: Join Feature Score'
display_name: Join Feature Score
environment:
  name: env_for_dcm_testing
inputs:
  extra_extraction:
    optional: false
    type: AnyDirectory
  feature_name:
    optional: false
    type: String
  input_extraction:
    optional: false
    type: AnyDirectory
  input_header:
    is_resource: true
    optional: false
    type: AnyDirectory
  num_output_partitions:
    default: 1000
    optional: false
    type: String
is_deterministic: true
name: join_feature_score
outputs:
  extraction:
    type: AnyDirectory
  header:
    type: AnyDirectory
type: CommandComponent
version: 0.47
