# ------------------------------------------------------------------------------
# This is generated from https://ml.azure.com/experiments/id/757f3391-a0a0-49d3-8780-99a744f72a99/runs/ff039288-a2ca-48e4-805e-25e6a4f5c65d?wsid=/subscriptions/b8c23406-f9b5-4ccb-8a65-a8cb5dcd6a5a/resourcegroups/dcm-westus2/workspaces/dcm-fastorc-test
# To run this code, please install SDK by this command:
# pip install "azure-ml-component[notebooks]>=0.9.5" --upgrade
# More detailed guide to set up your environment: https://componentsdk.azurewebsites.net/getting_started.html
# ------------------------------------------------------------------------------
from azure.ml.component import Pipeline, dsl
from azure.ml.component.dsl.types import String


from ...components import lightgbm_inference_func


# define pipeline
@dsl.pipeline(
    name='lightgbm_inference_on_aml_1',
    description='Running LightGBM Inference on AML',
)
def lightgbm_inference_on_aml_1_func(
    test_data_file_name: String(optional=False)=None,
    lgbm_model=None,
    test_data_dir=None,
) -> Pipeline:
    lightgbm_inference_step = lightgbm_inference_func(
        lgbm_model=lgbm_model,
        test_data_dir=test_data_dir,
        test_data_file_name=test_data_file_name,
    )
    lightgbm_inference_step.outputs.output_folder.configure(
        mode='mount',
    )
    lightgbm_inference_step.outputs.output_classification.configure(
        mode='mount',
    )
    lightgbm_inference_step.outputs.output_portal_url.configure(
        mode='mount',
    )

    return {
        'predicts': lightgbm_inference_step.outputs.output_folder,
        'classification': lightgbm_inference_step.outputs.output_classification,
        'portal_url': lightgbm_inference_step.outputs.output_portal_url,
    }
