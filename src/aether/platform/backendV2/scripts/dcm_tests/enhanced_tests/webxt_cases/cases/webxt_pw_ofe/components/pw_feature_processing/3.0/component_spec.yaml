$schema: https://componentsdk.azureedge.net/jsonschema/CommandComponent.json
command: echo "DUMMY COMPONENT"
description: Created from Aether client, module id:16ce45c4-bded-4350-a639-7aa619622171,
  module name:Feature Processor v3
display_name: Pairwise Feature Processing
environment:
  name: env_for_dcm_testing
inputs:
  clusteredby:
    default: ImpressionGuid
    optional: false
    type: String
  excludecolumns:
    default: Features
    optional: false
    type: String
  featurecolumnname:
    default: '"Features"'
    optional: false
    type: String
  featurelistfile:
    is_resource: true
    optional: false
    type: AnyDirectory
  input:
    optional: false
    type: AnyDirectory
is_deterministic: true
name: pw_feature_processing
outputs:
  output:
    type: AnyDirectory
type: CommandComponent
version: 3.0
