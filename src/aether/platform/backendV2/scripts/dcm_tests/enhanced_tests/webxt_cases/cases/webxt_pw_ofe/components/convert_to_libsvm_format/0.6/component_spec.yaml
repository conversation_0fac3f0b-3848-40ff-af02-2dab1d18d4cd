$schema: https://componentsdk.azureedge.net/jsonschema/CommandComponent.json
command: echo "DUMMY COMPONENT"
description: 'Created from Aether client version: ********, module id:0347bf31-636f-44bf-8c36-30e5fe82f80d,
  module name:Convert To LibSVM Format (output custom cols) and module description
  Convert To LibSVM Format, custom cols, output custom cols to another folder add
  bool param outputMetaInfoHeader'
display_name: Convert To LibSVM Format (Output Custom Cols)
environment:
  name: env_for_dcm_testing
inputs:
  commasepextraselectcols:
    default: '"m:DocId"'
    optional: false
    type: String
  extracols:
    default: '"m_DocId"'
    optional: false
    type: String
  extractionheader:
    is_resource: true
    optional: false
    type: AnyDirectory
  inputinifile:
    is_resource: true
    optional: false
    type: AnyDirectory
  labeltoratemapping:
    default: '"Label0:0|Label1:1|Label2:2|Label3:3|Label4:4|Label5:5|Label6:6|Label7:7|Label8:8|Label9:9|Label10:10|Label11:11|Label12:12|Label13:13|Label14:14|Label15:15|Label16:16|Label17:17|Label18:18|Label19:19|Label20:20"'
    optional: false
    type: String
  numpartitions:
    default: 1
    optional: false
    type: Integer
  outputmetainfoheader:
    default: true
    optional: false
    type: String
  partitionscontent:
    optional: false
    type: AnyDirectory
  runid:
    optional: false
    type: String
is_deterministic: true
name: convert_to_libsvm_format
outputs:
  metaoutputchunksfolder:
    type: AnyDirectory
  outputchunksfolder:
    type: AnyDirectory
type: CommandComponent
version: 0.6
