$schema: https://componentsdk.azureedge.net/jsonschema/CommandComponent.json
command: echo "DUMMY COMPONENT"
description: 'Created from Aether client version: ********, module id:3aefa2f9-6819-4016-86f8-bb982063af58,
  module name:[Unified Ranker] [Output Metadata] [Output all pairs]Generate Pairwise
  Extraction and module description Generates pairwise extraction from a regular extraction.   Sorts
  extraction lines by Query Id and Doc1 Id and Doc2 Id for full reproducibility.'
display_name: '[Unified Ranker] Generate Pairwise Extraction For Evaluation'
environment:
  name: env_for_dcm_testing
inputs:
  allowshortlines:
    default: false
    optional: false
    type: String
  deltafeature:
    default: true
    optional: false
    type: String
  deltaoffset:
    default: 2147483647
    optional: false
    type: String
  features:
    is_resource: true
    optional: false
    type: AnyDirectory
  header:
    is_resource: true
    optional: false
    type: AnyDirectory
  inputextraction:
    optional: false
    type: AnyDirectory
  inputisfolder:
    default: false
    optional: false
    type: String
  metadataoutput2urls:
    default: m:LPSat;m:Effortless;m:Fresh;m:Location;m:Position
    optional: false
    type: String
  numinputchunks:
    default: 1
    optional: false
    type: Integer
  originalfeature:
    default: true
    optional: false
    type: String
  outputheaderinextraction:
    default: true
    optional: false
    type: String
  ratiofeature:
    default: false
    optional: false
    type: String
  truncatetouintrange:
    default: true
    optional: false
    type: String
is_deterministic: true
name: unified_ranker_generate_pairwise_extraction_for_evaluation
outputs:
  outputextraction:
    type: AnyDirectory
  outputheader:
    type: AnyDirectory
  outputmetadata:
    type: AnyDirectory
type: CommandComponent
version: 2.7.8
