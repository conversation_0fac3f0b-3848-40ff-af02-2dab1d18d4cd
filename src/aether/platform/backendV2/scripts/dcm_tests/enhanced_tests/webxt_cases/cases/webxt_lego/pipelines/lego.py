# ------------------------------------------------------------------------------
# This is generated from https://ml.azure.com/runs/62ee89f3-775f-4273-a380-9b1d6f1ffeb0?wsid=/subscriptions/316a14b3-f94a-4267-a76a-497e589ef0a8/resourcegroups/training/workspaces/wxtcstrain
# To run this code, please install SDK by this command:
# pip install "azure-ml-component[notebooks]>=0.9.5" --upgrade
# More detailed guide to set up your environment: https://componentsdk.azurewebsites.net/getting_started.html
# ------------------------------------------------------------------------------
from azureml.core import Workspace
from azureml.core import Dataset
from azure.ml.component import <PERSON><PERSON><PERSON>, dsl, Component


from ..components import assert_equal_byte_by_byte_func
from ..components import smile_properties_func


from .subgraphs.pairwise_inner_loops import pairwise_inner_loops_func
from .subgraphs.pw_hrs import pw_hrs_func
from .subgraphs.pw_ofe import pw_ofe_func
from .subgraphs.rankers_doccombiner_check_in import rankers_doccombiner_check_in_func


import os
file_path = os.path.dirname(os.path.dirname(os.path.abspath(os.path.expanduser(__file__))))
file_path = os.path.abspath(os.path.join(file_path, "components/convert_param_to_boolean/0.0.1/convert_param_to_boolean.spec.yaml"))
convert_param_to_boolean_func = Component.from_yaml(yaml_file = file_path)

# get dataset
doccombiner_znw6rc0 = Dataset.File.from_files("https://dprepdata.blob.core.windows.net/demo/Titanic.csv")
urfusionznw6rc0_hrs = Dataset.File.from_files("https://dprepdata.blob.core.windows.net/demo/Titanic.csv")
urfusionznw6rc0_ofe = Dataset.File.from_files("https://dprepdata.blob.core.windows.net/demo/Titanic.csv")


# define pipeline
@dsl.pipeline(
    name='lego',
)
def lego(
    hrs_ranklm_feature_cov_thres=None,
    hrs_run_id=None,
    hrs_heavy_test_enabled=None,
    hrs_model_checkin_run_id=None,
    hrs_model_checkin_enabled=None,
    ofe_ranklm_feature_cov_thres=None,
    ofe_run_id=None,
    ofe_heavy_test_enabled=None,
    ofe_model_checkin_enabled=None,
    ofe_model_checkin_run_id=None,
    ofe_model_checkin_path=None,
    doc_combiner_checkin_path=None,
    doc_combiner_checkin_run_id=None,
    doc_combiner_baseline_model=None,
    hrs_heavy_test_baseline_model=None,
    ofe_heavy_test_baseline_model=None,
) -> Pipeline:
    publish_smile_properties_step = smile_properties_func(
        project='pw_ofe_hrs',
    )
    publish_smile_properties_step.outputs.output.configure(
        mode='mount',
    )

    hrs = pw_hrs_func(
        ranklm_feature_cov_thres=hrs_ranklm_feature_cov_thres,
        run_id=hrs_run_id,
    )

    compare_hrs = assert_equal_byte_by_byte_func(
        input1=hrs_heavy_test_baseline_model,
        input2=hrs.outputs.model_for_check_in,
    )
    compare_hrs.outputs.output.configure(
        mode='mount',
    )

    hrs_heavy_test_enabled_check = convert_param_to_boolean_func(
        input=hrs_heavy_test_enabled,
    )
    # hrs_heavy_test_enabled_check.outputs.output.configure(
    #     mode='mount',
    # )

    ofe = pw_ofe_func(
        ranklm_feature_cov_thres=ofe_ranklm_feature_cov_thres,
        run_id=ofe_run_id,
    )

    compare_ofe = assert_equal_byte_by_byte_func(
        input1=ofe_heavy_test_baseline_model,
        input2=ofe.outputs.model_for_check_in,
    )
    compare_ofe.outputs.output.configure(
        mode='mount',
    )

    ofe_heavy_test_enabled_check = convert_param_to_boolean_func(
        input=ofe_heavy_test_enabled,
    )
    # ofe_heavy_test_enabled_check.outputs.output.configure(
    #     mode='mount',
    # )

    rankers_doccombiner_check_in = rankers_doccombiner_check_in_func(
        doc_combiner_baseline_model=doc_combiner_baseline_model,
        hrs_ranker=hrs.outputs.model_for_check_in,
        ofe_ranker=ofe.outputs.model_for_check_in,
        hrs_check_in_path='Fusion\\<user_alias>\\AML\\<y><m><d>\\PW.OneDCG.BERT.<aml_sid>.ini',
        doc_combiner_enabled=True,
        hrs_enabled=hrs_model_checkin_enabled,
        hrs_run_id=hrs_model_checkin_run_id,
        ofe_check_in_path=ofe_model_checkin_path,
        ofe_enabled=ofe_model_checkin_enabled,
        ofe_run_id=ofe_model_checkin_run_id,
        doc_combiner_checkin_path=doc_combiner_checkin_path,
        doc_combiner_run_id=doc_combiner_checkin_run_id,
    )

    pairwise_inner_loops = pairwise_inner_loops_func(
        hrs_ranker=hrs.outputs.model_for_inner_loop,
        ofe_ranker=ofe.outputs.model_for_inner_loop,
        control_ofe_weight=0.575,
        treatment_ofe_weight=0.575,
        hrs_control_score_max_cap=140,
        hrs_control_score_min_cap=60,
        hrs_treatment_score_max_cap=140,
        hrs_treatment_score_min_cap=60,
        ofe_control_score_max_cap=184,
        ofe_control_score_min_cap=70,
        ofe_treatment_score_max_cap=184,
        ofe_treatment_score_min_cap=70,
    )

    dsl.condition(
        condition=hrs_heavy_test_enabled_check.outputs.output,
        true_block=compare_hrs,
    )
    dsl.condition(
        condition=ofe_heavy_test_enabled_check.outputs.output,
        true_block=compare_ofe,
    )

# create a pipeline
pipeline = lego(
    hrs_ranklm_feature_cov_thres=0.9,
    hrs_run_id='2021-11-23',
    hrs_heavy_test_enabled=True,
    hrs_model_checkin_run_id='1',
    hrs_model_checkin_enabled=True,
    ofe_ranklm_feature_cov_thres=0.9,
    ofe_run_id='2021-11-23',
    ofe_heavy_test_enabled=True,
    ofe_model_checkin_enabled=True,
    ofe_model_checkin_run_id='1',
    ofe_model_checkin_path='Fusion\\<user_alias>\\AML\\<y><m><d>\\PW.OFE.BERT.<aml_sid>.ini',
    doc_combiner_checkin_path='Fusion\\<user_alias>\\AML\\<y><m><d>\\DocCombiner.<aml_sid>.js',
    doc_combiner_checkin_run_id='1',
    doc_combiner_baseline_model=doccombiner_znw6rc0,
    hrs_heavy_test_baseline_model=urfusionznw6rc0_hrs,
    ofe_heavy_test_baseline_model=urfusionznw6rc0_ofe,
)

for node in pipeline._expand_pipeline_nodes():
    node.runsettings._store._user_provided_values = {}
    if node.k8srunsettings:
        node.k8srunsettings._store._user_provided_values = {}
    for name, output in node.outputs.items():
        output.configure(mode="mount")
