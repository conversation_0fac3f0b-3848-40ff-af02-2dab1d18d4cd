# -*- coding:utf-8 -*-
from datetime import datetime
import time
import json

from azureml.core import Experiment
from azureml.data.datapath import DataPath, DataPathComputeBinding
from azureml.pipeline.core import PipelineParameter, Pipeline
from azureml.pipeline.steps import PythonScriptStep
from azureml.core.runconfig import RunConfiguration
from azureml.core.conda_dependencies import CondaDependencies

from constants import CLUSTER_NAME, EXPERIMENT_NAME, BUILD_AND_PUBLISH_PIPELINE_DESCRIPTION, SOURCE_DIRECTORY, \
    PUBLISHED_PIPELINE_NAME_PREFIX, DATASTORE_PATH, RESOLVE_STEP_SCRIPT_NAME, PIPELINE_PARAMS, BUILD_AND_PUBLISH_PIPELINE_CATEGORY
from dcm_tests.common import utils


@utils.timethis
def build_publish_pipeline(workspace):
    # Get compute target
    utils.provision_cluster(workspace, CLUSTER_NAME)
    Workspace_compute_targets = workspace.compute_targets
    compute_target = Workspace_compute_targets[CLUSTER_NAME]
    experiment = Experiment(workspace, EXPERIMENT_NAME)

    # Define a pipeline
    timestamp = datetime.now().isoformat()
    pipeline_name = PUBLISHED_PIPELINE_NAME_PREFIX + timestamp
    print(f"pipeline_name is: {pipeline_name}")

    def_blob_store = workspace.get_default_datastore()
    print(f"Default datastore's name: {def_blob_store.name}")
    # Add data_path parameter for datastore schedule type in future
    data_path = DataPath(datastore=def_blob_store, path_on_datastore=DATASTORE_PATH)
    datapath_pipeline_param = PipelineParameter(name="input_datapath", default_value=data_path)
    datapath_input = (datapath_pipeline_param, DataPathComputeBinding(mode='mount'))
    pipeline_category = PipelineParameter(name="pipeline_category", default_value=BUILD_AND_PUBLISH_PIPELINE_CATEGORY)
    arguments = ["--datapath", datapath_input, "--pipeline_category", pipeline_category]
    for param, value in PIPELINE_PARAMS.items():
        arguments.append("--" + param)
        arguments.append(PipelineParameter(name=param, default_value=value))

    # Specify the python version to avoid e2e test failed due to lower python version reached end-of-life.
    remote_run_config = RunConfiguration()
    remote_run_config.environment.docker.base_image = "mcr.microsoft.com/azureml/openmpi4.1.0-ubuntu22.04"
    remote_run_config.environment.python.conda_dependencies = CondaDependencies(_underlying_structure=json.loads(
        """{"name": "project_environment", "channels": ["conda-forge"], "dependencies": ["python=3.9.12"]}"""))
    resolve_step = PythonScriptStep(
        name='resolve_step',
        script_name=RESOLVE_STEP_SCRIPT_NAME,
        arguments=arguments,
        inputs=[datapath_input],
        compute_target=compute_target,
        source_directory=SOURCE_DIRECTORY,
        runconfig=remote_run_config)
    print("Resolve_step created")

    pipeline = Pipeline(workspace=workspace, steps=[resolve_step])
    print("Pipeline with the resolve_step created")

    pipeline_run = experiment.submit(pipeline)
    print("Pipeline is submitted for execution")

    # Publish pipeline
    published_pipeline = pipeline_run.publish_pipeline(
        name=pipeline_name,
        description=BUILD_AND_PUBLISH_PIPELINE_DESCRIPTION,
        version="1.0")

    print(f"Pipeline published: {published_pipeline.id}")
    time.sleep(20)
    return published_pipeline
