# -*- coding: utf-8 -*-

import json

from base_test_case_v2 import BaseTestCaseV2
from constants import AZURE_GIT_PREFIX
from constants import EnumTestGroup
from utils import ContentRepo
from utils import generate_condition_operator

class StreamInSubgraphFailedWithOptionalDescendentsNotContinueTestCase(BaseTestCaseV2):
    """
    Stream component is in some subgraph.
    The stream component node failed.
    The inputs of following nodes are optional and continue_run_on_failed_optional_input is False.
    """
    def __init__(self):
        super(__class__, self).__init__()
        self.case_name = "stream_in_subgraph_failed_with_optional_descendents_not_continue"
        self.flag_collect_children_run_details_by_ml_flow = True
        self.continue_on_step_failure = True
        self.set_test_group(EnumTestGroup.StreamOrc)
        self.root_pipeline_description = "Case file: {}/{}".format(AZURE_GIT_PREFIX,
                "stream_orc_cases/stream_in_subgraph_failed_with_optional_descendents_not_continue.py")

    def generate_pipelines(self):
        default_subpipeline_decorator = self.generate_default_pipeline_decorator()
        pipeline_decorator_root = self.generate_root_pipeline_decorator()

        dummy_with_0_input_func = ContentRepo.dummy_with_0_input_func_v2
        dummy_with_1_ordinary_optional_input_func = ContentRepo.dummy_with_1_ordinary_optional_input_func_v2
        dummy_with_2_primitive_optional_int_inputs_func = ContentRepo.dummy_with_2_primitive_optional_int_inputs_func_v2
        dummy_with_3_primitive_optional_int_inputs_func = ContentRepo.dummy_with_3_primitive_optional_int_inputs_func_v2
        generic_stream_func = ContentRepo.generic_stream_func_v2
        ls_command_func = ContentRepo.ls_command_func_v2

        process_config = \
                {
                    "prefix_operation": {},
                    "execute_steps":
                    [
                        {
                            "write_u": True,
                            "write_v": 23
                        },
                        {
                            "pre_sleep": 60,
                            "write_w": 24,
                            "post_sleep": 120
                        },
                        {
                            "assert_false": True
                        }
                    ],
                    "post_operation": {},
                    "early_available_property_prefix": "azureml.pipeline.control.",
                    "primitive_outputs_property": "azureml.pipeline.control",
                    "update_primitive_output_in_final": True
                }

        @default_subpipeline_decorator
        def generate_sub_pipeline():
            simple_former_node = ls_command_func(input_dir = ContentRepo.meaningless_files_dataset_v2)

            the_stream_component = generic_stream_func(
                    required_input_path_a = simple_former_node.outputs.output_dir,
                    process_config_json = json.dumps(process_config))

            return the_stream_component.outputs

        @pipeline_decorator_root
        def generate_pipeline_root():

            stream_subgraph = generate_sub_pipeline()

            dummy_with_1_ordinary_optional_input = dummy_with_1_ordinary_optional_input_func(
                    the_input = stream_subgraph.outputs.ordinary_output)
            dummy_with_2_primitive_optional_int_inputs = dummy_with_2_primitive_optional_int_inputs_func(
                    input_a = stream_subgraph.outputs.ea_output_v,
                    input_b = stream_subgraph.outputs.ea_output_w)
            dummy_with_3_primitive_optional_int_inputs = dummy_with_3_primitive_optional_int_inputs_func(
                    input_a = stream_subgraph.outputs.ea_output_x,
                    input_b = stream_subgraph.outputs.primitive_output_y,
                    input_c = stream_subgraph.outputs.primitive_output_z)

            for_true = dummy_with_0_input_func();
            for_false = dummy_with_0_input_func();
            generate_condition_operator(stream_subgraph.outputs.ea_output_u, for_true, for_false)

        self.root_pipeline = generate_pipeline_root()
        self.root_pipeline.settings.continue_run_on_failed_optional_input = False

    def do_assert(self):
        self.assert_node_status_is("Failed",
                self.case_name,
                "stream_subgraph",
                "the_stream_component")
        self.assert_node_status_is("Completed",
                "simple_former_node",
                "dummy_with_2_primitive_optional_int_inputs",
                "for_false")
        self.assert_unsubmitted_nodes(
                "for_true",
                "dummy_with_1_ordinary_optional_input",
                "dummy_with_3_primitive_optional_int_inputs")
        self.assert_first_node_submitted_before_second_node_completed(
                "for_false", "the_stream_component")
        self.assert_first_node_submitted_before_second_node_completed(
                "dummy_with_2_primitive_optional_int_inputs", "the_stream_component")
