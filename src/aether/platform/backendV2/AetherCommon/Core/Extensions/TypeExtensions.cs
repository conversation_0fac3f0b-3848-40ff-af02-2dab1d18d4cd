﻿// <copyright file="TypeExtensions.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

namespace Microsoft.MachineLearning.AetherCommon.Core.Extensions
{
    /// <summary>
    /// This class provides extension methods to System.Type class.
    /// </summary>
    public static class TypeExtensions
    {
        /// <summary>
        /// Checks whether the type represents a collection (Array, List, etc.)
        /// </summary>
        /// <param name="t">The type to check.</param>
        /// <returns>Returns true if it is a collection type, false otherwise.</returns>
        public static bool IsCollectionType(this Type t)
        {
            if (t == typeof(string))
            {
                return false;
            }

            if (t.IsArray)
            {
                return true;
            }

            // Dictionary is also an IEnumerable,
            // do not consider it as collection type for our case.
            return t.ImplementsGenericTypeDefinition(typeof(IEnumerable<>))
                && !t.IsDictionaryType();
        }

        /// <summary>
        /// Checks whether the type is a dictionary type.
        /// </summary>
        /// <param name="t">The type to check.</param>
        /// <returns>Returns true if it is dictionary type, false otherwise.</returns>
        public static bool IsDictionaryType(this Type t)
        {
            return t.ImplementsGenericTypeDefinition(typeof(IDictionary<,>))
                || t.ImplementsGenericTypeDefinition(typeof(IReadOnlyDictionary<,>));
        }

        /// <summary>
        /// Returns the type of element if it is a collection type.
        ///
        /// If the given type is array or list, returns the type of elements.
        /// If the given type is dictionary, returns the type of dictionary values.
        /// Otherwise, return null.
        /// </summary>
        /// <param name="t">The type to check.</param>
        /// <returns>The type of element inside the container type.</returns>
        public static Type? GetContainerElementType(this Type t)
        {
            if (t.IsArray)
            {
                return t.GetElementType();
            }
            else if (t.IsDictionaryType())
            {
                var genericArguments = t.GetGenericArguments();
                return genericArguments.Length >= 2 ? genericArguments[1] : null;
            }
            else if (t.IsCollectionType())
            {
                var genericArguments = t.GetGenericArguments();
                return genericArguments.Length >= 1 ? genericArguments[0] : null;
            }
            else
            {
                return null;
            }
        }

        /// <summary>
        /// Checks whether the given type implements a given generic type definition.
        /// </summary>
        /// <param name="t">The type to check.</param>
        /// <param name="genericTypeDefinition">The expected generic type definition.</param>
        /// <returns>Returns true if the type implements the given definition, false otherwise.</returns>
        public static bool ImplementsGenericTypeDefinition(this Type t, Type genericTypeDefinition)
        {
            if (t.CompatibleWithGenericTypeDefinition(genericTypeDefinition))
            {
                return true;
            }

            foreach (Type i in t.GetInterfaces())
            {
                if (i.CompatibleWithGenericTypeDefinition(genericTypeDefinition))
                {
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// Checks whether the given type is derived from a given generic type definition.
        ///
        /// See the following link for details.
        /// https://docs.microsoft.com/en-us/dotnet/api/system.type.getgenerictypedefinition?view=net-6.0
        /// </summary>
        /// <param name="t">The type to check.</param>
        /// <param name="genericTypeDefinition">The expected generic type definition.</param>
        /// <returns>Returns true if the type can be constructed from the given definition, false otherwise.</returns>
        public static bool CompatibleWithGenericTypeDefinition(this Type t, Type genericTypeDefinition)
        {
            return t.IsGenericType && t.GetGenericTypeDefinition() == genericTypeDefinition;
        }
    }
}
