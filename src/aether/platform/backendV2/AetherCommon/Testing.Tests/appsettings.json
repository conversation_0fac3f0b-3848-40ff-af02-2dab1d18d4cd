﻿{
  "Logging": {
    "IncludeScopes": false,
    "LogLevel": {
      "Default": "Warning",
      "System": "Information",
      "Microsoft": "Information"
    }
  },
  "Kestrel": {
    "ServerListenPort": 89
  },
  "AmlTelemetry": {
    "Enabled": true,
    "Scrubbing": {
      "CustomDimensionScrubbers": {
        // from in BBSharedSettings.ini
        "calculated_node_hash": "Block",
        "exception": "Block"
      },
      "TelemetryTypes": {
        "ExceptionTelemetry": {
          "Message": "Block"
        },
        "ExceptionDetailsInfo": {
          "Message": "Block"
        }
      }
    }
  },
  "LoggerEndpoint": {
    "SocketFolder": "/var/run/applogs/",
    "Enabled": true
  },
  "ErrorResponse": {
    "StackTraceTenants": {
      "microsoft": "72f988bf-86f1-41af-91ab-2d7cd011db47"
    }
  },
  "ApplicationInsights": {
    "InstrumentationKey": "d6d867dc-7ff0-4f0f-b26a-6f75a7c971dd"
  },
  "S2S": {
    "ClientId": "",
    "ClientSecret": "",
    "Authority": "https://login.microsoftonline.com/72f988bf-86f1-41af-91ab-2d7cd011db47",
    "AlternateAllowedClientIds": {}
  },
  "TokenValidation": {
    "Authority": "https://login.microsoftonline.com/common",
    "KeyDiscoveryUris": {
      "AAD": "https://login.microsoftonline.com/common/discovery/v2.0/keys",
      "Token": "https://master.experiments.azureml-test.net/token/v1.0/keys"
    }
  },
  "ArmAadSettings": {
    "ArmCertificateMetadataEndpoint": "https://management.azure.com:24582/metadata/authentication?api-version=2015-01-01",
    "AadAuthority": "https://login.microsoftonline.com/{0}",
    "AadAppClientId": "607ece82-f922-494f-88b8-30effaf12214",
    "GraphAuthority": "https://login.microsoftonline.com/{0}",
    "FetchStorageDetailsTimeout": "0:00:03",
    "StorageClientPollingInterval": 100,
    "StorageMatchConfig": true,
    "ArmResourceUri": "https://management.core.windows.net/",
    "GraphEndpointResource": "https://graph.windows.net/",
    "ArmManagementEndpoint": "https://management.azure.com"
  },
  "ExperimentationHost": {
    "ExperimentationHostUri": "https://master.experiments.azureml-test.net",
    "ResourceProviderHostUri": "https://master.api.azureml-test.ms"
  }
}
