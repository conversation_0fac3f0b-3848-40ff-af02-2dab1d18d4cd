﻿using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.MachineLearning.Common.Startup;
using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Reflection;
using System.Text;

namespace Microsoft.Aether.Common.Testing.Tests
{
    public class TestStartup : IAppStartup
    {
        public IConfigurationRoot Configuration { get; set; }

        public TestStartup(IConfiguration configuration)
        {
            Configuration = (IConfigurationRoot)configuration;
        }

        public void DoGeneralAppStartup(IServiceProvider serviceProvider)
        {
            
        }

        public void DoWebStartup(IApplicationBuilder app)
        {

        }

        [ExcludeFromCodeCoverage]
        public void DoWorkerStartup(IServiceProvider serviceProvider)
        {
            throw new NotImplementedException();
        }

        [ExcludeFromCodeCoverage]
        public Assembly[] GetOrleansAssemblies()
        {
            throw new NotImplementedException();
        }

        public void RegisterGeneralAppComponents(IServiceCollection services)
        {
            
        }

        public void RegisterWebComponents(IServiceCollection services)
        {
            
        }

        [ExcludeFromCodeCoverage]
        public void RegisterWorkerComponents(IServiceCollection services)
        {
            throw new NotImplementedException();
        }
    }
}
