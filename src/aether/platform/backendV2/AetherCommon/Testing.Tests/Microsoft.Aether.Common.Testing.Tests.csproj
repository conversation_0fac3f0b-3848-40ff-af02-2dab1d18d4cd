﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <OutputType>Exe</OutputType>
  </PropertyGroup>

  <ItemGroup>

    <PackageReference Include="Microsoft.NET.Test.Sdk" />
		<PackageReference Include="xunit" />
		<PackageReference Include="xunit.runner.visualstudio" />
    <PackageReference Include="Microsoft.AspNetCore.TestHost" />
    <PackageReference Include="FakeItEasy" />
    <PackageReference Include="FluentAssertions" />
    <PackageReference Include="Azure.Security.KeyVault.Secrets" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Testing\Microsoft.Aether.Common.Testing.csproj" />
  </ItemGroup>

</Project>
