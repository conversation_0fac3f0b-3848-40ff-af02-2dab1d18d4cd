﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Xunit;

namespace Microsoft.Aether.Common.Testing.Tests
{
    [Trait("Category", "QTestSkip")]
    public class ServiceInvokerTest : IClassFixture<TestFixture>
    {
        protected TestFixture Fixture { get; private set; }

        public ServiceInvokerTest(TestFixture fixture)
        {
            Fixture = fixture;
        }

        [Fact]
        public async void Test_ServiceInvoker()
        {
            var controller = Fixture.CreateControllerInvoker<ITestController>();

            var result = await controller.TestAsync();

            Assert.Equal("test", result);
        }
    }
}
