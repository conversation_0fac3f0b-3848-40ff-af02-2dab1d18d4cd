﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.MachineLearning.Common.Testing;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Microsoft.Aether.Common.Testing
{
    public static class FileLoggingExtensions
    {
        public static IServiceCollection AddFileLogging(this IServiceCollection services, FileLoggerProvider provider)
        {
            return services
                .AddSingleton<ILoggerProvider>(provider);
        }

        public static ILoggingBuilder AddFileLogging(this ILoggingBuilder builder, FileLoggerProvider provider)
        {
            return builder.AddProvider(provider);
        }
    }
}
