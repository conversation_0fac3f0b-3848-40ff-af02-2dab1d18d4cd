﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Library</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>annotations</Nullable>
    <IsTestProject>false</IsTestProject>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <NoWarn>CA1801;</NoWarn>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <NoWarn>CA1801;</NoWarn>
  </PropertyGroup>

  <ItemGroup>
		<PackageReference Include="xunit" />
    <PackageReference Include="xunit.runner.visualstudio" />
    <ProjectReference Include="$(SrcRoot)\Common\Core\Common.Core.csproj" />
    <ProjectReference Include="$(SrcRoot)\Common\Startup\Common.Startup.csproj" />
    <ProjectReference Include="$(SrcRoot)\Common\Testing\Common.Testing.csproj" />
    <ProjectReference Include="$(SrcRoot)\Common\WebApi.Client\Common.WebApi.Client.csproj" />
    <ProjectReference Include="$(SrcRoot)\Common\Caching\Common.Caching.csproj" />
    <PackageReference Include="FluentAssertions" />
    <PackageReference Include="Azure.Security.KeyVault.Secrets" />
  </ItemGroup>

  <ItemGroup>
    <None Update="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>
</Project>
