﻿using Microsoft.Azure.Amqp;
using Microsoft.Extensions.Logging;
using Microsoft.MachineLearning.Common.Testing;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Xunit.Abstractions;

namespace Microsoft.Aether.Common.Testing
{
    public class FileLoggerProvider : ILoggerProvider
    {
        public const string LogPrefix = "[CloudService]";
        private StreamWriter _logger;
        private string _loggerFilePath;

        public FileLoggerProvider()
        {
            _loggerFilePath = Path.Combine(Path.GetTempPath(), "serviceTest." + Guid.NewGuid() + ".log");
            _logger = new StreamWriter(_loggerFilePath);
        }

        public ILogger CreateLogger(string categoryName)
        {
            return new FileLogger(_logger, categoryName);
        }

        public void Dispose()
        {
            _logger.Dispose();
        }

        public string GetLoggerFilePath()
        {
            return _loggerFilePath;
        }

        public class FileLogger : ILogger
        {
            private readonly string _categoryName;
            private StreamWriter _logger;

            public FileLogger(StreamWriter logger, string categoryName)
            {
                _logger = logger;
                _categoryName = categoryName;
            }

            public IDisposable BeginScope<TState>(TState state)
            {
                return null;
            }

            public bool IsEnabled(LogLevel logLevel)
            {
                return true;
            }

            [DebuggerHidden]
            [DebuggerNonUserCode]
            public void Log<TState>(LogLevel logLevel, EventId eventId, TState state, Exception? exception, Func<TState, Exception?, string> formatter)
            {
                try
                {
                    var logPrefixWithCategory = LogPrefix + ":" + (_categoryName ?? string.Empty);
                    _logger?.WriteLine($"{logPrefixWithCategory} [{DateTime.UtcNow:O}] {formatter(state, exception)}");
                }
                catch (Exception)
                {
                }
            }
        }
    }
}
