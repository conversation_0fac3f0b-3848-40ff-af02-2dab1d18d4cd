﻿// <copyright file="HostCreator.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.MachineLearning.Common.Startup;
using Microsoft.MachineLearning.Common.Testing;
using Microsoft.MachineLearning.Common.Testing.Logging;
using System;
using System.Net;

namespace Microsoft.Aether.Common.Testing
{
    public class HostCreator<TStartup, TEntryPoint> : IHostCreator
        where TStartup : class, IAppStartup
        where TEntryPoint : EntryPoint<FixtureStartup<TStartup>>, new()
    {
        public FileLoggerProvider FileLoggerProvider { get; private set; }
        protected readonly Fixture<TStartup> _fixture;
        private readonly bool _useCmkCosmosDbWorkspaces;

        public HostCreator(Fixture<TStartup> fixture, bool useCmkCosmosDbWorkspaces = false)
        {
            _fixture = fixture;
            _useCmkCosmosDbWorkspaces = useCmkCosmosDbWorkspaces;
            FileLoggerProvider = new FileLoggerProvider();
        }

        /// <summary>
        /// customize configuration building
        /// for most scenarios, just override GetTestabilityConfigurationOverrides of Fixture
        /// </summary>
        /// <param name="builderContext"></param>
        /// <param name="builder"></param>
        public virtual void ConfigureAppConfiguration(WebHostBuilderContext builderContext, IConfigurationBuilder builder)
        {
            builder.AddInMemoryCollection(_fixture.GetTestabilityConfigurationOverrides());
        }

        public virtual void ConfigureController(IServiceCollection services)
        {
            services
            .AddMvc()
            .AddApplicationPart(typeof(TStartup).Assembly)
            .AddControllersAsServices();
        }

        public virtual void ConfigureTestHarness(IServiceCollection services)
        {
            services.AddSingleton<ITestHarness>(new TestHarness(s =>
            {
            }));
        }
        /// <summary>
        /// register more services by overriding this method
        /// </summary>
        /// <param name="services"></param>
        public virtual void RegisterServices(IServiceCollection services)
        {
            ConfigureController(services);
            ConfigureTestHarness(services);
        }

        public virtual IWebHostBuilder CreateHostBuilder(string url)
        {
            var entryPoint = new TEntryPoint();

            return entryPoint
                .GetWebHostBuilder()
                .UseKestrel(options =>
                {
                    options.Listen(IPAddress.Any, new Uri(url).Port);
                })
                .UseUrls(url)
                .ConfigureAppConfiguration((builderContext, builder) =>
                {
                    ConfigureAppConfiguration(builderContext, builder);
                })
                .ConfigureServices(services =>
                {
                    RegisterServices(services);
                })
                .ConfigureLogging(builder =>
                {
                    builder
                    .ClearProviders()
                    .AddFileLogging(FileLoggerProvider);
                });
        }

        public IWebHost CreateHost(string url)
        {
            return CreateHostBuilder(url).Build();
        }
    }
}
