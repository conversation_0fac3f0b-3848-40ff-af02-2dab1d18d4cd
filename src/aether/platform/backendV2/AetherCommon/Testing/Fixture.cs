﻿// <copyright file="Fixture.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Microsoft.Azure.Services.AppAuthentication;
using Microsoft.IdentityModel.Clients.ActiveDirectory;
using Microsoft.MachineLearning.Common.Startup;
using Microsoft.MachineLearning.Common.Testing;
using Microsoft.MachineLearning.Common.Testing.Fixtures;
using System;
using System.Collections.Generic;
using System.Net.Http.Headers;
using System.Threading.Tasks;
using Xunit.Abstractions;

namespace Microsoft.Aether.Common.Testing
{
    public class Fixture<TStartup> : TestFixtureBase
        where TStartup : class, IAppStartup
    {
        public Fixture(IMessageSink diagnosticMessageSink = null)
            : this(diagnosticMessageSink, false)
        {
        }

        protected Fixture(
            IMessageSink diagnosticMessageSink,
            bool useCmkCosmosDbWorkspaces = false)
            : base(diagnosticMessageSink)
        {
            LoggerCallbackHandler.UseDefaultLogging = false;

            HostCreator = CreateHostCreator();
        }

        public override Guid SubscriptionId { get; set; }

        public override string ResourceGroupName { get; set; }

        public override string WorkspaceName { get; set; }

        public override Guid WorkspaceId { get; set; }

        private TestServicePrincipal KeyVaultSP;

        private string? authToken;

        public virtual IHostCreator CreateHostCreator()
        {
            return new HostCreator<TStartup, EntryPoint<FixtureStartup<TStartup>>>(this);
        }

        public override IDictionary<string, string> GetTestabilityConfigurationOverrides()
        {
            IDictionary<string, string> config;

            try
            {
                config = base.GetTestabilityConfigurationOverrides();
            }
            catch (AzureServiceTokenProviderException ex)
            {
                Console.Write($"failed setup original TestabilityConfigurationOverrides: {ex.Message}");

                config = new Dictionary<string, string>();
            }

            config.Update(new Dictionary<string, string>
            {
                { "Logging:DisableConsole", "true" },
                { "Redis:EnableCaching", "false" },
            });

            return config;
        }

        private string? GetKeyVaultValue(string name)
        {
            try
            {
                return FetchTestSecretsFromKeyVault.BuildKVSecrets?.GetValueOrDefault(name);
            }
            catch (AzureServiceTokenProviderException ex)
            {
                Console.Write($"failed to get value from keyvault: {ex.Message}");

                return null;
            }
        }

        public override async Task InitializeAsync()
        {
            FetchTestSecretsFromKeyVault = new FetchTestSecretsFromKeyVault();

            // Contributor Role
            // az ad sp create-for-rbac--scopes / <access> --role Contributor --years 50 --name <name>
            KeyVaultSP = new TestServicePrincipal
            {
                AppId = GetKeyVaultValue("sp-client-id"),
                Password = GetKeyVaultValue("sp-client-secret"),
                Tenant = "72f988bf-86f1-41af-91ab-2d7cd011db47"
            };

            S2SRole = new TestServicePrincipal
            {
                AppId = GetKeyVaultValue("kubernetes--sp-client-id"),
                DisplayName = "S2S",
                Name = "S2S",
                Password = GetKeyVaultValue("kubernetes--sp-client-secret"),
                Tenant = "72f988bf-86f1-41af-91ab-2d7cd011db47"
            };

            var testWorkspaceNameFromBuild = Environment.GetEnvironmentVariable("API_TESTS_WORKSPACE_NAME");

            if (!string.IsNullOrWhiteSpace(testWorkspaceNameFromBuild))
            {
                var workspaceFixture = new WorkspaceFixture();

                var workspace = await workspaceFixture.GetOrCreateShortLivedTestWorkspace(testWorkspaceNameFromBuild).ConfigureAwait(false);
                SubscriptionId = Guid.Parse(workspace.SubscriptionId);
                ResourceGroupName = workspace.ResourceGroup;
                WorkspaceName = workspace.Name;
                WorkspaceId = Guid.Parse(workspace.WorkspaceId);
            }
            else
            {
                SubscriptionId = base.SubscriptionId;
                ResourceGroupName = base.ResourceGroupName;
                WorkspaceName = base.WorkspaceName;
                WorkspaceId = base.WorkspaceId;
            }

            StartLocalAPI(false);
        }

        private void SetEnvironmentVariable(string name, string value)
        {
            if (Environment.GetEnvironmentVariable(name) == null)
            {
                Environment.SetEnvironmentVariable(name, value);
            }
        }

        protected virtual async Task<string> GetAuthTokenAsync()
        {
            if (authToken != null)
            {
                return authToken;
            }

            if (ContributorRole != null)
            {
                return authToken = await GetAuthTokenAsync(ContributorRole).ConfigureAwait(false);
            }

            foreach (string connectionString in new string[] { "RunAs=Developer; DeveloperTool=VisualStudio", "RunAs=Developer; DeveloperTool=AzureCli" })
            {
                try
                {
                    return authToken = (await new AzureServiceTokenProvider(connectionString).GetAccessTokenAsync("https://management.core.windows.net/", KeyVaultSP.Tenant).ConfigureAwait(false));
                }
                catch (AzureServiceTokenProviderException ex)
                {
                    Console.WriteLine("cannot get token with connectString", connectionString, ":", ex.Message);
                }
            }

            return null;
        }

        public TController CreateControllerInvoker<TController>()
        {
            return TestServiceInvokerFactory.SetupServiceInvokerFactory().Create<TController>(HttpClient.BaseAddress, beforeRequest: (r, m) => r.Headers.Authorization = new AuthenticationHeaderValue("Bearer", authToken));
        }

        public string GetFileLoggerPath()
        {
            return ((HostCreator<TStartup, EntryPoint<FixtureStartup<TStartup>>>)HostCreator).FileLoggerProvider.GetLoggerFilePath();
        }
    }
}
