﻿// <copyright file="Startup.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.MachineLearning.Common.Caching;
using Microsoft.MachineLearning.Common.Startup;
using Microsoft.MachineLearning.Common.Testing;
using System;
using System.Collections.Generic;
using System.Reflection;
using System.Text;

namespace Microsoft.Aether.Common.Testing
{
    public class FixtureStartup<TStartup> : IAppStartup
        where TStartup : class, IAppStartup
    {
        private TStartup _appStartup;

        public IConfigurationRoot Configuration { get; set; }

        public FixtureStartup(IConfiguration configuration)
        {
            Configuration = (IConfigurationRoot)configuration;
        }

        public void RegisterGeneralAppComponents(IServiceCollection services)
        {
            services.AddSingleton<TStartup>();
            _appStartup = services.BuildServiceProvider().GetRequiredService<TStartup>();

            _appStartup.RegisterGeneralAppComponents(services);

            services.AddCommonCaching(Configuration);
        }

        public void RegisterWebComponents(IServiceCollection services)
        {
            _appStartup.RegisterWebComponents(services);

            TestHarness.Install(services);
        }

        public void RegisterWorkerComponents(IServiceCollection services)
        {
            _appStartup.RegisterGeneralAppComponents(services);
        }

        public void DoGeneralAppStartup(IServiceProvider serviceProvider)
        {
            _appStartup.DoGeneralAppStartup(serviceProvider);
        }

        public void DoWebStartup(IApplicationBuilder app)
        {
            _appStartup.DoWebStartup(app);
        }

        public void DoWorkerStartup(IServiceProvider serviceProvider)
        {
            _appStartup.DoWorkerStartup(serviceProvider);
        }

        public Assembly[] GetOrleansAssemblies()
        {
            return _appStartup.GetOrleansAssemblies();
        }
    }
}
