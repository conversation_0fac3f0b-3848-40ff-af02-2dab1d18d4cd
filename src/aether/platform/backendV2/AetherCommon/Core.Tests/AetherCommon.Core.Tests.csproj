﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <IsPackable>false</IsPackable>
    <RootNamespace>Microsoft.MachineLearning.AetherCommon.Core.Tests</RootNamespace>
    <AssemblyName>Microsoft.MachineLearning.AetherCommon.Core.Tests</AssemblyName>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="FakeItEasy" />
    <PackageReference Include="FluentAssertions" />
    <PackageReference Include="xunit" />
    <PackageReference Include="xunit.runner.visualstudio" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="$(AetherSrcRoot)\platform\backendV2\AetherCommon\Core\AetherCommon.Core.csproj" />
  </ItemGroup>

</Project>
