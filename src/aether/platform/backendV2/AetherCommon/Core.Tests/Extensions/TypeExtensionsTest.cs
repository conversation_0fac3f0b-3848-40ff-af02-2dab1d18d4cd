﻿// <copyright file="TypeExtensionsTest.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using FluentAssertions;
using Microsoft.MachineLearning.AetherCommon.Core.Extensions;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Xunit;

namespace Microsoft.MachineLearning.AetherCommon.Core.Tests.Extensions
{
    public class TypeExtensionsTest
    {
        [Theory]
        [InlineData(typeof(string), false)]
        [InlineData(typeof(List<string>), true)]
        [InlineData(typeof(string[]), true)]
        [InlineData(typeof(Dictionary<string, string>), false)]
        [InlineData(typeof(IDictionary<string, string>), false)]
        [InlineData(typeof(ReadOnlyDictionary<string, string>), false)]
        [InlineData(typeof(IReadOnlyDictionary<string, string>), false)]
        public void TestIsCollectionType(Type type, bool expected)
        {
            type.IsCollectionType().Should().Be(expected);
        }

        [Theory]
        [InlineData(typeof(string), false)]
        [InlineData(typeof(List<string>), false)]
        [InlineData(typeof(Dictionary<string, string>), true)]
        [InlineData(typeof(IDictionary<string, string>), true)]
        [InlineData(typeof(ReadOnlyDictionary<string, string>), true)]
        [InlineData(typeof(IReadOnlyDictionary<string, string>), true)]
        public void TestIsDictionaryType(Type type, bool expected)
        {
            type.IsDictionaryType().Should().Be(expected);
        }

        [Theory]
        [InlineData(typeof(string), null)]
        [InlineData(typeof(List<string>), typeof(string))]
        [InlineData(typeof(NonGenericList), null)]
        [InlineData(typeof(Dictionary<string, Guid>), typeof(Guid))]
        [InlineData(typeof(NonGenericDictionary), null)]
        [InlineData(typeof(string[]), typeof(string))]
        [InlineData(typeof(Guid[]), typeof(Guid))]
        public void TestGetContainerElementType(Type type, Type expected)
        {
            type.GetContainerElementType().Should().Be(expected);
        }

        [Theory]
        [InlineData(typeof(IDictionary<string, string>), typeof(IDictionary<,>), true)]
        [InlineData(typeof(Dictionary<string, string>), typeof(IDictionary<,>), true)]
        [InlineData(typeof(Dictionary<string, string>), typeof(Dictionary<,>), true)]
        [InlineData(typeof(IDictionary<string, string>), typeof(Dictionary<,>), false)]
        [InlineData(typeof(string), typeof(IDictionary<,>), false)]
        public void TestImplementsGenericTypeDefinition(Type type, Type genericTypeDefinition, bool expected)
        {
            type.ImplementsGenericTypeDefinition(genericTypeDefinition).Should().Be(expected);
        }

        [Theory]
        [InlineData(typeof(string), typeof(string), false)]
        [InlineData(typeof(List<string>), typeof(List<>), true)]
        [InlineData(typeof(List<string>), typeof(IList<>), false)]
        [InlineData(typeof(List<string>), typeof(List<string>), false)]
        [InlineData(typeof(Dictionary<string, string>), typeof(Dictionary<,>), true)]
        [InlineData(typeof(Dictionary<string, string>), typeof(IDictionary<,>), false)]
        [InlineData(typeof(Dictionary<string, string>), typeof(ReadOnlyDictionary<,>), false)]
        public void TestCompatibleWithGenericTypeDefinition(Type type, Type genericTypeDefinition, bool expected)
        {
            type.CompatibleWithGenericTypeDefinition(genericTypeDefinition).Should().Be(expected);
        }
    }

    public class NonGenericDictionary : Dictionary<string, object>
    {
    }

    public class NonGenericList : List<object>
    {
    }
}
