﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Common.GenevaAction;
using Microsoft.AspNetCore.Authorization;
using Common.WebApi.Security;
using Microsoft.MachineLearning.Common.Core.Security;
using Microsoft.AspNetCore.Builder;
using Microsoft.MachineLearning.Common.WebApi.Security;
using Microsoft.MachineLearning.Common.Arm;

namespace Microsoft.Aether.BlueBox.Common.GenevaActions
{
    public static class GenevaActionsExtensions
    {
        public static IServiceCollection AddGenevaActions(this IServiceCollection services, IConfiguration Configuration)
        {
            services.Configure<TestAadAppConfiguration>(Configuration.GetSection("TestAadApp"));

            // Geneva Action
            services.Configure<GenevaActionConfiguration>(Configuration.GetSection("GenevaAction"));
            if (Configuration.GetSection("GenevaAction").GetValue<bool?>("EnableGenevaActionAuth") ?? true)
            {
                services
                    .AddSingleton<IAuthorization<PERSON><PERSON><PERSON>, GenevaActionClientCertificatAuthorizationHandler>()
                    .AddAuthorization(ConfigureGenevaAuthorization);
            }

            return services;
        }

        private static void ConfigureGenevaAuthorization(AuthorizationOptions options)
        {
            // Exclude any "Action" word in policy in case conflict with role-based auth handler
            options.AddPolicy(
                "Acis",
                policy =>
                {
                    policy.AddRequirements(
                        new ResourceAuthorizationRequirements()
                        {
                            Role = AuthorizationRole.GenevaAction
                        });
                });
        }

        public static void UseGenevaActions(this IApplicationBuilder app)
        {
            app.UseAuthentication();
            app.UseAuthorization();
        }
    }
}