﻿# Geneva Actions Service Library

## How to implement

* Call `AddGenevaActions` in `RegisterWebComponents` method of `Startup`
```
services.AddGenevaActions(Configuration);
```

* Call `InstallGenevaActions` in DoWebStartup method of `Startup`
```
app.InstallGenevaActions();
```

* Implement Controller and add `Authorize(Policy = "Acis")` attribute to controller class.
```
[Authorize(Policy = "Acis")]
```
