# pipeline link: https://dev.azure.com/msdata/Vienna/_build?definitionId=36571

variables:
  PipelineArtifactPath: $(System.DefaultWorkingDirectory)\PipelineArtifacts
  AETHER_APIENDPOINT: https://int.api.azureml-test.ms
  AetherBuildAlias: _Pipelines-CI-Build
  dropdesitnation: $(System.DefaultWorkingDirectory)
  EnvName: int
  IGNORE_SSL_ERRORS: False
  LoggingDirectory: $(System.DefaultWorkingDirectory)
  ParallelRunNameFilterForComponent: Name=TestSubmitGraphFromSimpleSample|Name=TestCase01VNext|Name=TestCase02ParallelCopyFiles|Name=TestCase03CommandComponentForDockerImageWithSpecialFiles
  PIPELINERUN_WITH_EXPIRED_DATASET_ID: ed745c0f-b619-4eb7-a30b-d5b5977a2b8b
  PIPELINERUN_WITH_VALID_DATASET_ID: 5214f82f-dafc-4555-8bd4-7f92161dd79e
  relativepath: /Release/Amd64/app/aether/bluebox
  RESOURCE_GROUP: rge2etests
  Running: 0
  SEPARATOR_LINE: ################################################################################
  SUBSCRIPTION_ID: b8c23406-f9b5-4ccb-8a65-a8cb5dcd6a5a
  TargetFramework: net8.0
  TestHarness: VSTS
  vstspat: $(System.AccessToken)
  WORKSPACE_NAME: wse2etests_automl

resources:
  repositories:
  - repository: OneBranchTemplateRepo
    type: git
    name: OneBranch.Pipelines/GovernedTemplates
    ref: refs/heads/main
  pipelines:
  - pipeline: pipeline_ci_build
    project: Vienna
    source: 'Pipeline CI Build PullRequest'
    trigger: true

extends:
  template: v2/OneBranch.NonOfficial.CrossPlat.yml@OneBranchTemplateRepo
  parameters:
    git:
      submodules: false
    globalSdl:
      asyncSdl:
        enabled: false
      cg:
        failOnAlert: false
      tsa:
        enabled: false
      credscan:
        suppressionsFile: src\azureml-api\src\Designer\.config\CredScanSuppressions.json

    stages:
    - stage: deploy_to_int_transient
      displayName: Deploy
      jobs:
      - job: deploy
        displayName: Deploy To Transient
        timeoutInMinutes: 120
        pool:
          name: promptflow-1es-pt-win
          type: windows
          isCustom: true
        variables:
          - name: TestHarness
            value: VSTS
          - name: skipComponentGovernanceDetection
            value: true

        steps:
        - checkout: none

        - task: vsrm-ev2.vss-services-ev2.adm-release-task.ExpressV2Internal@1
          displayName: 'FAIL if not valid cloud'
          inputs:
            EnableStrictValidation: true
            EndpointProviderType: ApprovalService
            ServiceRootPath: '$(System.DefaultWorkingDirectory)/integration-does-not-exist'
            RolloutSpecPath: '$(System.DefaultWorkingDirectory)/integration-does-not-exist'
            OutputRolloutId: 'ROLLOUT_ID'
            OutputServiceGroupName: 'ROLLOUT_SERVICEGROUP_NAME'
            OutputRolloutStatus: 'ROLLOUT_STATUS'
          condition: and(succeeded(), not(in('integration', 'integration', 'public', 'fairfax', 'mooncake', 'usnat', 'ussec')))
          timeoutInMinutes: 7200

        - task: DownloadPipelineArtifact@2
          displayName: 'Download CI Build Artifact: drop'
          inputs:
            source: specific
            project: Vienna
            pipeline: $(resources.pipeline.pipeline_ci_build.pipelineName)
            buildId: $(resources.pipeline.pipeline_ci_build.runName)
            allowPartiallySucceededBuilds: true
            artifactName: drop
            itemPattern:
            targetPath: '$(PipelineArtifactPath)\drop'

        - task: vsrm-ev2.vss-services-ev2.adm-release-task.ExpressV2Internal@1
          displayName: 'Ev2-msftkube: Deploy To Integration'
          inputs:
            EnableStrictValidation: true
            ConnectedServiceName: 'Ev2 Infra INT environment deployments'
            ServiceRootPath: '$(PipelineArtifactPath)/drop/'
            RolloutSpecPath: '$(PipelineArtifactPath)/drop/msftkube_shell/int-transient/RolloutSpec.json'
            InlineDynamicBindingOverrides: '{     "$schema": "https://ev2schema.azure.net/schemas/2020-01-01/scopeBindings.json",     "scopeBindings": [         {             "scopeTagName": "msftkube",             "bindings": [                 {                     "find": "__MSFTKUBECMD__",                     "replaceWith": "release -v -g aether extra-cloudlet -e int-transient"                 },                 {                     "find": "__RELEASE_ID__",                     "replaceWith": "$(Release.ReleaseId)"                 },                 {                     "find": "__RELEASE_DEFINITION_ID__",                     "replaceWith": "$(Release.DefinitionId)"                 },                 {                     "find": "__RELEASE_DEFINITION_NAME__",                     "replaceWith": "$(Release.DefinitionName)"                 },                 {                     "find": "__MSFTKUBE_SHARED_TASK_GROUP__",                     "replaceWith": "true"                 }             ]         }     ] }'
            OutputRolloutId: 'ROLLOUT_ID'
            OutputServiceGroupName: 'ROLLOUT_SERVICEGROUP_NAME'
            OutputRolloutStatus: 'ROLLOUT_STATUS'
          condition: and(succeeded(), eq('integration', 'integration'), not(startsWith('release -v -g aether extra-cloudlet', '-')))
          timeoutInMinutes: 7200

        - task: vsrm-ev2.vss-services-ev2.adm-release-task.ExpressV2Internal@1
          displayName: 'Ev2: Deploy To Integration'
          inputs:
            EnableStrictValidation: true
            ConnectedServiceName: 'Ev2 Infra INT environment deployments'
            ServiceRootPath: '$(PipelineArtifactPath)/drop'
            RolloutSpecPath: '$(PipelineArtifactPath)/drop/ev2/int-transient/RolloutSpec.json'
            OutputRolloutId: 'ROLLOUT_ID'
            OutputServiceGroupName: 'ROLLOUT_SERVICEGROUP_NAME'
            OutputRolloutStatus: 'ROLLOUT_STATUS'
          condition: and(succeeded(), eq('integration', 'integration'), startsWith('release -v -g aether extra-cloudlet', '-'))
          timeoutInMinutes: 7200

    - stage: integration_test
      displayName: Integration Test
      dependsOn: deploy_to_int_transient
      jobs:

      - job: TestVars
        pool:
          name: pipeline-1ES-MMS
          type: windows
          isCustom: true
        steps:
        - checkout: none

        - script: |
            echo $(Release.ReleaseId)
            echo $(Release.DefinitionId)
            echo $(Release.DefinitionName)
          displayName: Show Variables

      # - ${{ each var in variables }}:
      #   - script: |
      #       echo ${{ var.key }}
      #       echo ${{ var.value }}
      #     displayName: ${{ var.key }}

      - job: integration_test
        displayName: Integration Tests
        timeoutInMinutes: 120
        pool:
          name: pipeline-1ES-MMS
          type: windows
          isCustom: true
        variables:
          - name: TestHarness
            value: VSTS
          - name: skipComponentGovernanceDetection
            value: true

        steps:
        - checkout: none

        - task: DownloadPipelineArtifact@2
          displayName: 'Download CI Build Artifact: aetherdrop'
          inputs:
            source: specific
            project: Vienna
            pipeline: $(resources.pipeline.pipeline_ci_build.pipelineName)
            buildId: $(resources.pipeline.pipeline_ci_build.runName)
            allowPartiallySucceededBuilds: true
            artifactName: aetherdrop
            itemPattern:
            targetPath: '$(PipelineArtifactPath)\aetherdrop'

        - task: AzureKeyVault@1
          displayName: 'Azure Key Vault: aethercloudtest'
          inputs:
            azureSubscription: 'DCM-E2E-Test'
            KeyVaultName: aethercloudtest
            SecretsFilter: AetherCloudtestCert

        - task: PowerShell@2
          displayName: 'Setup Certificate'
          inputs:
            targetType: filePath
            filePath: '$(PipelineArtifactPath)/aetherdrop/Release/AnyCPU/app/aether/BBAetherLibrary/BBAetherLibrary.IntegrationTests/$(TargetFramework)/setup_certificates.ps1'
            arguments: '$(AetherCloudtestCert)'

        - task: PowerShell@2
          displayName: 'Set Aether Endpoint'
          inputs:
            targetType: inline
            script: |
             $BUILD_SOURCEBRANCH=$Env:BUILD_SOURCEBRANCH
             $AETHER_APIENDPOINT=".transient.int.api.azureml-test.ms"
             $AETHER_ENDPOINT=".transient.int.api.azureml-test.ms"

             # Write-Output $BUILD_SOURCEBRANCH

             # if( $BUILD_SOURCEBRANCH -like "refs/pull/*/merge")
             # {
             #     # pull request case.
             #     Write-Output "Pull request release"
             #     $BUILD_SOURCEBRANCH -match "refs/pull/(?<pullid>[0-9]+)/merge"
             #     $Matches.pullid
             #     $AETHER_APIENDPOINT="https://pull-"+$Matches.pullid+".transient.int.api.azureml-test.ms"
             #     $AETHER_ENDPOINT="https://pull-"+$Matches.pullid+".transient.int.api.azureml-test.ms"
             # }
             # elseif ( $BUILD_SOURCEBRANCH -like "refs/heads/*")
             # {
             #     # manual request case.
             #     Write-Output "Manual release"
             #     $BUILD_SOURCEBRANCH -match "refs/heads/(?<branch>.+)"
             #     $branch = $Matches.branch  -replace "/","-"
             #     $branch = $branch  -replace "_","-"
             #     $AETHER_APIENDPOINT="https://"+$branch+".transient.int.api.azureml-test.ms"
             #     $AETHER_ENDPOINT="https://"+$branch+".transient.int.api.azureml-test.ms"
             # }
             # else
             # {
             #     exit(1)
             # }

             $AETHER_APIENDPOINT="https://pull-1387956.transient.int.api.azureml-test.ms"
             $AETHER_ENDPOINT="https://pull-1387956.transient.int.api.azureml-test.ms"

             Write-Output $AETHER_APIENDPOINT
             Write-Output $AETHER_ENDPOINT

             Write-Host "##vso[task.setvariable variable=AETHER_APIENDPOINT]$AETHER_APIENDPOINT"
             Write-Host "##vso[task.setvariable variable=AETHER_ENDPOINT]$AETHER_ENDPOINT"

        - task: VSTest@2
          displayName: 'VsTest - Integration Test'
          inputs:
            testAssemblyVer2: |
             **\BBAetherLibrary\BBAetherLibrary.IntegrationTests\**\BBAetherLibrary.IntegrationTests.dll
             !**\ref\**
            searchFolder: '$(PipelineArtifactPath)\aetherdrop'
            testFiltercriteria: 'FullyQualifiedName!=BBAetherLibrary.IntegrationTests.TestFixtures.PipelineRunTests.TestParameterizeDataStoreModeInPipelineParameter&FullyQualifiedName!=BBAetherLibrary.IntegrationTests.TestFixtures.AutoMLV2ModuleTests.TestAutoMLV2WithRegisteredTabularDatasets'
            runSettingsFile: '$(PipelineArtifactPath)/aetherdrop/Release/AnyCPU/app/aether/BBAetherLibrary/BBAetherLibrary.IntegrationTests/$(TargetFramework)/$(EnvName).runsettings'
            otherConsoleOptions: '/Parallel /logger:"console"'
            diagnosticsEnabled: True
            rerunFailedTests: false

        - task: VSTest@2
          displayName: 'VsTest - Asset, Dataset Test'
          inputs:
            testAssemblyVer2: |
             **\BBAetherLibrary\BBAetherLibrary.ESCloud.IntegrationTests\**\BBAetherLibrary.ESCloud.IntegrationTests.dll
             !**\ref\**
            searchFolder: '$(PipelineArtifactPath)\aetherdrop'
            testFiltercriteria: 'FullyQualifiedName=BBAetherLibrary.ESCloud.IntegrationTests.ESTests.RunEsCloudAssetInputOutputWithMlModelOnBatchAI|FullyQualifiedName=BBAetherLibrary.ESCloud.IntegrationTests.ESTests.RunEsCloudAssetInputOutputWithMultinodesOnBatchAI|FullyQualifiedName=BBAetherLibrary.ESCloud.IntegrationTests.ESTests.RunEsCloudAssetInputOutputWithUriFileOnBatchAI|FullyQualifiedName=BBAetherLibrary.ESCloud.IntegrationTests.ESTests.RunEsCloudAssetInputOutputWithUriFolderOnBatchAI|FullyQualifiedName=BBAetherLibrary.ESCloud.IntegrationTests.ESTests.RunEsCloudTabularDatasetTestOnBatchAI|FullyQualifiedName=BBAetherLibrary.ESCloud.IntegrationTests.ESTests.RunEsCloudTabularDatasetWithToDataFrameDirectoryTestOnBatchAI|FullyQualifiedName=BBAetherLibrary.ESCloud.IntegrationTests.ESTests.RunEsCloudTabularDatasetWithToParquetFilesTestOnBatchAI|FullyQualifiedName=BBAetherLibrary.ESCloud.IntegrationTests.ESTests.RunEsCloudFileDatasetTestOnBatchAI|FullyQualifiedName=BBAetherLibrary.ESCloud.IntegrationTests.ESTests.RunEsCloudFileDatasetWithParseDelimitedFilesTestOnBatchAI|FullyQualifiedName=BBAetherLibrary.ESCloud.IntegrationTests.ESTests.RunEsCloudDatasetInputNewOutputOnBatchAI|FullyQualifiedName=BBAetherLibrary.ESCloud.IntegrationTests.ESTests.RunEsCloudDatasetInputOutputOnBatchAI|FullyQualifiedName=BBAetherLibrary.ESCloud.IntegrationTests.ESTests.RunEsCloudDatasetOutputOverwrittenByParameterAssignmentOnBatchAI'
            runSettingsFile: '$(PipelineArtifactPath)/aetherdrop/Release/AnyCPU/app/aether/BBAetherLibrary/BBAetherLibrary.ESCloud.IntegrationTests/$(TargetFramework)/$(EnvName).runsettings'
            otherConsoleOptions: '/Parallel /logger:"console"'
            diagnosticsEnabled: True
            rerunFailedTests: false

    - stage: escloud_integration_test
      displayName: ESCloud Integration Test
      dependsOn: deploy_to_int_transient
      jobs:

      - job: escloud_integration_test
        displayName: ESCloud Integration Tests
        timeoutInMinutes: 120
        pool:
          name: pipeline-1ES-MMS
          type: windows
          isCustom: true
        variables:
          - name: TestHarness
            value: VSTS
          - name: skipComponentGovernanceDetection
            value: true

        steps:
        - checkout: none

        - task: DownloadPipelineArtifact@2
          displayName: 'Download CI Build Artifact: aetherdrop'
          inputs:
            source: specific
            project: Vienna
            pipeline: $(resources.pipeline.pipeline_ci_build.pipelineName)
            buildId: $(resources.pipeline.pipeline_ci_build.runName)
            allowPartiallySucceededBuilds: true
            artifactName: aetherdrop
            itemPattern:
            targetPath: '$(PipelineArtifactPath)\aetherdrop'

        - task: AzureKeyVault@1
          displayName: 'Azure Key Vault: aethercloudtest'
          inputs:
            azureSubscription: 'DCM-E2E-Test'
            KeyVaultName: aethercloudtest
            SecretsFilter: AetherCloudtestCert

        - task: PowerShell@2
          displayName: 'Setup Certificate'
          inputs:
            targetType: filePath
            filePath: '$(PipelineArtifactPath)/aetherdrop/Release/AnyCPU/app/aether/BBAetherLibrary/BBAetherLibrary.ESCloud.IntegrationTests/$(TargetFramework)/setup_certificates.ps1'
            arguments: '$(AetherCloudtestCert)'

        - task: PowerShell@2
          displayName: 'Set Aether Endpoint'
          inputs:
            targetType: inline
            script: |
             $BUILD_SOURCEBRANCH=$Env:BUILD_SOURCEBRANCH
             $AETHER_APIENDPOINT=".transient.int.api.azureml-test.ms"
             $AETHER_ENDPOINT=".transient.int.api.azureml-test.ms"

             # Write-Output $BUILD_SOURCEBRANCH

             # if( $BUILD_SOURCEBRANCH -like "refs/pull/*/merge")
             # {
             #     # pull request case.
             #     Write-Output "Pull request release"
             #     $BUILD_SOURCEBRANCH -match "refs/pull/(?<pullid>[0-9]+)/merge"
             #     $Matches.pullid
             #     $AETHER_APIENDPOINT="https://pull-"+$Matches.pullid+".transient.int.api.azureml-test.ms"
             #     $AETHER_ENDPOINT="https://pull-"+$Matches.pullid+".transient.int.api.azureml-test.ms"
             # }
             # elseif ( $BUILD_SOURCEBRANCH -like "refs/heads/*")
             # {
             #     # manual request case.
             #     Write-Output "Manual release"
             #     $BUILD_SOURCEBRANCH -match "refs/heads/(?<branch>.+)"
             #     $branch = $Matches.branch  -replace "/","-"
             #     $branch = $branch  -replace "_","-"
             #     $AETHER_APIENDPOINT="https://"+$branch+".transient.int.api.azureml-test.ms"
             #     $AETHER_ENDPOINT="https://"+$branch+".transient.int.api.azureml-test.ms"
             # }
             # else
             # {
             #     exit(1)
             # }

             $AETHER_APIENDPOINT="https://pull-1387956.transient.int.api.azureml-test.ms"
             $AETHER_ENDPOINT="https://pull-1387956.transient.int.api.azureml-test.ms"

             Write-Output $AETHER_APIENDPOINT
             Write-Output $AETHER_ENDPOINT

             Write-Host "##vso[task.setvariable variable=AETHER_APIENDPOINT]$AETHER_APIENDPOINT"
             Write-Host "##vso[task.setvariable variable=AETHER_ENDPOINT]$AETHER_ENDPOINT"

        - task: VSTest@2
          displayName: 'VsTest - testAssemblies'
          inputs:
            testAssemblyVer2: |
             **\aether\BBAetherLibrary\BBAetherLibrary.ESCloud.IntegrationTests\**\BBAetherLibrary.ESCloud.IntegrationTests.dll
             !**\ref\**
            searchFolder: '$(PipelineArtifactPath)\aetherdrop'
            testFiltercriteria: 'FullyQualifiedName!=BBAetherLibrary.ESCloud.IntegrationTests.ESTests.RunEsCloudForScriptComponentWithHyperDriveConfigurationtOnKubernetes&FullyQualifiedName!=BBAetherLibrary.ESCloud.IntegrationTests.ESTests.RunEsCloudForScriptComponentWithHyperDriveConfigurationtOnBatchAI&FullyQualifiedName!=BBAetherLibrary.ESCloud.IntegrationTests.ESTests.RunEsCloudForCommandComponentWithHyperDriveConfiguration&FullyQualifiedName!=BBAetherLibrary.ESCloud.IntegrationTests.ESTests.RunEsCloudForScriptComponentWithHyperDriveConfiguration&FullyQualifiedName!=BBAetherLibrary.ESCloud.IntegrationTests.ESTests.RunEsCloudAssetInputOutputWithMlModelOnBatchAI&FullyQualifiedName!=BBAetherLibrary.ESCloud.IntegrationTests.ESTests.RunEsCloudAssetInputOutputWithMultinodesOnBatchAI&FullyQualifiedName!=BBAetherLibrary.ESCloud.IntegrationTests.ESTests.RunEsCloudAssetInputOutputWithUriFileOnBatchAI&FullyQualifiedName!=BBAetherLibrary.ESCloud.IntegrationTests.ESTests.RunEsCloudAssetInputOutputWithUriFolderOnBatchAI&FullyQualifiedName!=BBAetherLibrary.ESCloud.IntegrationTests.ESTests.RunEsCloudTabularDatasetTestOnBatchAI&FullyQualifiedName!=BBAetherLibrary.ESCloud.IntegrationTests.ESTests.RunEsCloudTabularDatasetWithToDataFrameDirectoryTestOnBatchAI&FullyQualifiedName!=BBAetherLibrary.ESCloud.IntegrationTests.ESTests.RunEsCloudTabularDatasetWithToParquetFilesTestOnBatchAI&FullyQualifiedName!=BBAetherLibrary.ESCloud.IntegrationTests.ESTests.RunEsCloudFileDatasetTestOnBatchAI&FullyQualifiedName!=BBAetherLibrary.ESCloud.IntegrationTests.ESTests.RunEsCloudFileDatasetWithParseDelimitedFilesTestOnBatchAI&FullyQualifiedName!=BBAetherLibrary.ESCloud.IntegrationTests.ESTests.RunEsCloudDatasetInputNewOutputOnBatchAI&FullyQualifiedName!=BBAetherLibrary.ESCloud.IntegrationTests.ESTests.RunEsCloudDatasetInputOutputOnBatchAI&FullyQualifiedName!=BBAetherLibrary.ESCloud.IntegrationTests.ESTests.RunEsCloudDatasetOutputOverwrittenByParameterAssignmentOnBatchAI&TestCategory!=SkipForInt'
            runSettingsFile: '$(PipelineArtifactPath)/aetherdrop/Release/AnyCPU/app/aether/BBAetherLibrary/BBAetherLibrary.ESCloud.IntegrationTests/$(TargetFramework)/$(EnvName).runsettings'
            distributionBatchType: basedOnExecutionTime
            diagnosticsEnabled: True
            rerunFailedTests: false

    - stage: quota_service_integration_test
      displayName: Quota Service Integration Test
      dependsOn: deploy_to_int_transient
      jobs:

      - job: quota_service_integration_test
        displayName: Quota Service Integration Test
        timeoutInMinutes: 120
        pool:
          name: pipeline-1ES-MMS
          type: windows
          isCustom: true
        variables:
          - name: TestHarness
            value: VSTS
          - name: skipComponentGovernanceDetection
            value: true

        steps:
        - checkout: none

        - task: DownloadPipelineArtifact@2
          displayName: 'Download CI Build Artifact: aetherdrop'
          inputs:
            source: specific
            project: Vienna
            pipeline: $(resources.pipeline.pipeline_ci_build.pipelineName)
            buildId: $(resources.pipeline.pipeline_ci_build.runName)
            allowPartiallySucceededBuilds: true
            artifactName: aetherdrop
            itemPattern:
            targetPath: '$(PipelineArtifactPath)\aetherdrop'

        - task: AzureKeyVault@1
          displayName: 'Azure Key Vault: aethercloudtest'
          inputs:
            azureSubscription: 'DCM-E2E-Test'
            KeyVaultName: aethercloudtest
            SecretsFilter: AetherCloudtestCert

        - task: PowerShell@2
          displayName: 'Setup Certificate'
          inputs:
            targetType: filePath
            filePath: '$(PipelineArtifactPath)/aetherdrop/Release/AnyCPU/app/aether/BBAetherLibrary/BBAetherLibrary.QuotaService.ServiceTests/$(TargetFramework)/setup_certificates.ps1'
            arguments: '$(AetherCloudtestCert)'

        - task: PowerShell@2
          displayName: 'Set Aether Endpoint'
          inputs:
            targetType: inline
            script: |
             $BUILD_SOURCEBRANCH=$Env:BUILD_SOURCEBRANCH
             $AETHER_APIENDPOINT=".transient.int.api.azureml-test.ms"
             $AETHER_ENDPOINT=".transient.int.api.azureml-test.ms"

             # Write-Output $BUILD_SOURCEBRANCH

             # if( $BUILD_SOURCEBRANCH -like "refs/pull/*/merge")
             # {
             #     # pull request case.
             #     Write-Output "Pull request release"
             #     $BUILD_SOURCEBRANCH -match "refs/pull/(?<pullid>[0-9]+)/merge"
             #     $Matches.pullid
             #     $AETHER_APIENDPOINT="https://pull-"+$Matches.pullid+".transient.int.api.azureml-test.ms"
             #     $AETHER_ENDPOINT="https://pull-"+$Matches.pullid+".transient.int.api.azureml-test.ms"
             # }
             # elseif ( $BUILD_SOURCEBRANCH -like "refs/heads/*")
             # {
             #     # manual request case.
             #     Write-Output "Manual release"
             #     $BUILD_SOURCEBRANCH -match "refs/heads/(?<branch>.+)"
             #     $branch = $Matches.branch  -replace "/","-"
             #     $branch = $branch  -replace "_","-"
             #     $AETHER_APIENDPOINT="https://"+$branch+".transient.int.api.azureml-test.ms"
             #     $AETHER_ENDPOINT="https://"+$branch+".transient.int.api.azureml-test.ms"
             # }
             # else
             # {
             #     exit(1)
             # }

             $AETHER_APIENDPOINT="https://pull-1387956.transient.int.api.azureml-test.ms"
             $AETHER_ENDPOINT="https://pull-1387956.transient.int.api.azureml-test.ms"

             Write-Output $AETHER_APIENDPOINT
             Write-Output $AETHER_ENDPOINT

             Write-Host "##vso[task.setvariable variable=AETHER_APIENDPOINT]$AETHER_APIENDPOINT"
             Write-Host "##vso[task.setvariable variable=AETHER_ENDPOINT]$AETHER_ENDPOINT"

        - task: AzureCLI@1
          displayName: 'Run Tests'
          inputs:
            azureSubscription: 'Azure Infra INT environment deployments'
            scriptLocation: inlineScript
            inlineScript: 'dotnet test $(PipelineArtifactPath)/aetherdrop/Release/AnyCPU/app/aether/BBAetherLibrary/BBAetherLibrary.QuotaService.ServiceTests/$(TargetFramework)/BBAetherLibrary.QuotaService.ServiceTests.dll  --logger:trx;LogFileName=results.trx --settings $(PipelineArtifactPath)/aetherdrop/Release/AnyCPU/app/aether/BBAetherLibrary/BBAetherLibrary.QuotaService.ServiceTests/$(TargetFramework)/$(EnvName).runsettings'
          continueOnError: true
          retryCountOnTaskFailure: 1

        - task: PublishTestResults@2
          displayName: 'Publish Test Results **/*.trx'
          inputs:
            testResultsFormat: VSTest
            testResultsFiles: '**/*.trx'
            failTaskOnFailedTests: true
            testRunTitle: QuotaServiceTests

    - stage: compliant_tests
      displayName: Compliant Tests
      dependsOn: deploy_to_int_transient
      jobs:

      - job: compliant_tests
        displayName: Compliant Tests
        timeoutInMinutes: 120
        pool:
          name: pipeline-1ES-ubuntu
          type: linux
          isCustom: true
        variables:
          - name: TestHarness
            value: VSTS
          - name: skipComponentGovernanceDetection
            value: true

        steps:
        - checkout: none

        - task: DownloadPipelineArtifact@2
          displayName: 'Download CI Build Artifact: aetherdrop'
          inputs:
            source: specific
            project: Vienna
            pipeline: $(resources.pipeline.pipeline_ci_build.pipelineName)
            buildId: $(resources.pipeline.pipeline_ci_build.runName)
            allowPartiallySucceededBuilds: true
            artifactName: scripts
            itemPattern:
            targetPath: '$(System.DefaultWorkingDirectory)/PipelineArtifacts/scripts'


        - task: UsePythonVersion@0
          displayName: 'Use Python 3.8.12'
          inputs:
            versionSpec: 3.8.12

        - task: PowerShell@2
          displayName: 'Set Aether Endpoint'
          inputs:
            targetType: inline
            script: |
             $BUILD_SOURCEBRANCH=$Env:BUILD_SOURCEBRANCH
             $AETHER_APIENDPOINT=".transient.int.api.azureml-test.ms"
             $AETHER_ENDPOINT=".transient.int.api.azureml-test.ms"

             # Write-Output $BUILD_SOURCEBRANCH

             # if( $BUILD_SOURCEBRANCH -like "refs/pull/*/merge")
             # {
             #     # pull request case.
             #     Write-Output "Pull request release"
             #     $BUILD_SOURCEBRANCH -match "refs/pull/(?<pullid>[0-9]+)/merge"
             #     $Matches.pullid
             #     $AETHER_APIENDPOINT="https://pull-"+$Matches.pullid+".transient.int.api.azureml-test.ms"
             #     $AETHER_ENDPOINT="https://pull-"+$Matches.pullid+".transient.int.api.azureml-test.ms"
             # }
             # elseif ( $BUILD_SOURCEBRANCH -like "refs/heads/*")
             # {
             #     # manual request case.
             #     Write-Output "Manual release"
             #     $BUILD_SOURCEBRANCH -match "refs/heads/(?<branch>.+)"
             #     $branch = $Matches.branch  -replace "/","-"
             #     $branch = $branch  -replace "_","-"
             #     $AETHER_APIENDPOINT="https://"+$branch+".transient.int.api.azureml-test.ms"
             #     $AETHER_ENDPOINT="https://"+$branch+".transient.int.api.azureml-test.ms"
             # }
             # else
             # {
             #     exit(1)
             # }

             $AETHER_APIENDPOINT="https://pull-1387956.transient.int.api.azureml-test.ms"
             $AETHER_ENDPOINT="https://pull-1387956.transient.int.api.azureml-test.ms"

             Write-Output $AETHER_APIENDPOINT
             Write-Output $AETHER_ENDPOINT

             Write-Host "##vso[task.setvariable variable=AETHER_APIENDPOINT]$AETHER_APIENDPOINT"
             Write-Host "##vso[task.setvariable variable=AETHER_ENDPOINT]$AETHER_ENDPOINT"

        - task: AzureCLI@2
          displayName: 'Run Tests'
          inputs:
            azureSubscription: 'DCM-E2E-Test'
            scriptType: bash
            scriptLocation: inlineScript
            inlineScript: |
             pip install azure-core==1.12.0
             pip install azure-common==1.1.27
             pip install azureml-pipeline==1.33.0
             pip install azureml-core==1.33.0
             pip install azureml-dataset-runtime[fuse] --upgrade
             pip install azure-ml-component==0.9.13.post1
             pip install azure-cli-core
             pip install azure-ai-ml
             pip install azure-identity

             echo $(AETHER_APIENDPOINT)
             python test_compliant.py --host $(AETHER_APIENDPOINT) --region int_compliant
            workingDirectory: '$(System.DefaultWorkingDirectory)/PipelineArtifacts/scripts/dcm_tests/transient_gate'

    - stage: apcloud_integration_test
      displayName: APCloud Integration Test
      dependsOn: deploy_to_int_transient
      jobs:

      - job: apcloud_integration_test
        displayName: APCloud Integration Test
        timeoutInMinutes: 120
        pool:
          name: pipeline-1ES-MMS
          type: windows
          isCustom: true
        variables:
          - name: TestHarness
            value: VSTS
          - name: skipComponentGovernanceDetection
            value: true

        steps:
        - checkout: none

        - task: DownloadPipelineArtifact@2
          displayName: 'Download CI Build Artifact: aetherdrop'
          inputs:
            source: specific
            project: Vienna
            pipeline: $(resources.pipeline.pipeline_ci_build.pipelineName)
            buildId: $(resources.pipeline.pipeline_ci_build.runName)
            allowPartiallySucceededBuilds: true
            artifactName: aetherdrop
            itemPattern:
            targetPath: '$(PipelineArtifactPath)\aetherdrop'

        - task: AzureKeyVault@1
          displayName: 'Azure Key Vault: aethercloudtest'
          inputs:
            azureSubscription: 'DCM-E2E-Test'
            KeyVaultName: aethercloudtest
            SecretsFilter: AetherCloudtestCert

        - task: PowerShell@2
          displayName: 'Setup Certificate'
          inputs:
            targetType: filePath
            filePath: '$(PipelineArtifactPath)/aetherdrop/Release/AnyCPU/app/aether/BBAetherLibrary/BBAetherLibrary.Apcloud.IntegrationTests/$(TargetFramework)/setup_certificates.ps1'
            arguments: '$(AetherCloudtestCert)'

        - task: PowerShell@2
          displayName: 'Set Aether Endpoint'
          inputs:
            targetType: inline
            script: |
             $BUILD_SOURCEBRANCH=$Env:BUILD_SOURCEBRANCH
             $AETHER_APIENDPOINT=".transient.int.api.azureml-test.ms"
             $AETHER_ENDPOINT=".transient.int.api.azureml-test.ms"

             # Write-Output $BUILD_SOURCEBRANCH

             # if( $BUILD_SOURCEBRANCH -like "refs/pull/*/merge")
             # {
             #     # pull request case.
             #     Write-Output "Pull request release"
             #     $BUILD_SOURCEBRANCH -match "refs/pull/(?<pullid>[0-9]+)/merge"
             #     $Matches.pullid
             #     $AETHER_APIENDPOINT="https://pull-"+$Matches.pullid+".transient.int.api.azureml-test.ms"
             #     $AETHER_ENDPOINT="https://pull-"+$Matches.pullid+".transient.int.api.azureml-test.ms"
             # }
             # elseif ( $BUILD_SOURCEBRANCH -like "refs/heads/*")
             # {
             #     # manual request case.
             #     Write-Output "Manual release"
             #     $BUILD_SOURCEBRANCH -match "refs/heads/(?<branch>.+)"
             #     $branch = $Matches.branch  -replace "/","-"
             #     $branch = $branch  -replace "_","-"
             #     $AETHER_APIENDPOINT="https://"+$branch+".transient.int.api.azureml-test.ms"
             #     $AETHER_ENDPOINT="https://"+$branch+".transient.int.api.azureml-test.ms"
             # }
             # else
             # {
             #     exit(1)
             # }

             $AETHER_APIENDPOINT="https://pull-1387956.transient.int.api.azureml-test.ms"
             $AETHER_ENDPOINT="https://pull-1387956.transient.int.api.azureml-test.ms"

             Write-Output $AETHER_APIENDPOINT
             Write-Output $AETHER_ENDPOINT

             Write-Host "##vso[task.setvariable variable=AETHER_APIENDPOINT]$AETHER_APIENDPOINT"
             Write-Host "##vso[task.setvariable variable=AETHER_ENDPOINT]$AETHER_ENDPOINT"

        - task: VSTest@2
          displayName: 'VsTest - testAssemblies'
          inputs:
            testAssemblyVer2: |
             **\aether\BBAetherLibrary\BBAetherLibrary.Apcloud.IntegrationTests\**\BBAetherLibrary.Apcloud.IntegrationTests.dll
             !**\ref\**
            searchFolder: '$(PipelineArtifactPath)\aetherdrop'
            testFiltercriteria: 'TestCategory=RunsInCI'
            runSettingsFile: '$(PipelineArtifactPath)/aetherdrop/Release/AnyCPU/app/aether/BBAetherLibrary/BBAetherLibrary.IntegrationTests/$(TargetFramework)/$(EnvName).runsettings'
            otherConsoleOptions: '/Parallel /logger:"console"'
            publishRunAttachments: false
            diagnosticsEnabled: True
            rerunFailedTests: false

    - stage: metastore_service_test
      displayName: Metastore Service Test
      dependsOn: deploy_to_int_transient
      jobs:

      - job: metastore_service_test
        displayName: Metastore Service Test
        timeoutInMinutes: 120
        pool:
          name: pipeline-1ES-MMS
          type: windows
          isCustom: true
        variables:
          - name: TestHarness
            value: VSTS
          - name: skipComponentGovernanceDetection
            value: true

        steps:
        - checkout: none

        - task: DownloadPipelineArtifact@2
          displayName: 'Download CI Build Artifact: aetherdrop'
          inputs:
            source: specific
            project: Vienna
            pipeline: $(resources.pipeline.pipeline_ci_build.pipelineName)
            buildId: $(resources.pipeline.pipeline_ci_build.runName)
            allowPartiallySucceededBuilds: true
            artifactName: aetherdrop
            itemPattern:
            targetPath: '$(PipelineArtifactPath)\aetherdrop'

        - task: AzureKeyVault@1
          displayName: 'Azure Key Vault: aethercloudtest'
          inputs:
            azureSubscription: 'DCM-E2E-Test'
            KeyVaultName: aethercloudtest
            SecretsFilter: AetherCloudtestCert

        - task: PowerShell@2
          displayName: 'Setup Certificate'
          inputs:
            targetType: filePath
            filePath: '$(PipelineArtifactPath)/aetherdrop/Release/AnyCPU/app/aether/BBAetherLibrary/BBAetherLibrary.Metastore.IntegrationTests/$(TargetFramework)/setup_certificates.ps1'
            arguments: '$(AetherCloudtestCert)'

        - task: PowerShell@2
          displayName: 'Set Aether Endpoint'
          inputs:
            targetType: inline
            script: |
             $BUILD_SOURCEBRANCH=$Env:BUILD_SOURCEBRANCH
             $AETHER_APIENDPOINT=".transient.int.api.azureml-test.ms"
             $AETHER_ENDPOINT=".transient.int.api.azureml-test.ms"

             # Write-Output $BUILD_SOURCEBRANCH

             # if( $BUILD_SOURCEBRANCH -like "refs/pull/*/merge")
             # {
             #     # pull request case.
             #     Write-Output "Pull request release"
             #     $BUILD_SOURCEBRANCH -match "refs/pull/(?<pullid>[0-9]+)/merge"
             #     $Matches.pullid
             #     $AETHER_APIENDPOINT="https://pull-"+$Matches.pullid+".transient.int.api.azureml-test.ms"
             #     $AETHER_ENDPOINT="https://pull-"+$Matches.pullid+".transient.int.api.azureml-test.ms"
             # }
             # elseif ( $BUILD_SOURCEBRANCH -like "refs/heads/*")
             # {
             #     # manual request case.
             #     Write-Output "Manual release"
             #     $BUILD_SOURCEBRANCH -match "refs/heads/(?<branch>.+)"
             #     $branch = $Matches.branch  -replace "/","-"
             #     $branch = $branch  -replace "_","-"
             #     $AETHER_APIENDPOINT="https://"+$branch+".transient.int.api.azureml-test.ms"
             #     $AETHER_ENDPOINT="https://"+$branch+".transient.int.api.azureml-test.ms"
             # }
             # else
             # {
             #     exit(1)
             # }

             $AETHER_APIENDPOINT="https://pull-1387956.transient.int.api.azureml-test.ms"
             $AETHER_ENDPOINT="https://pull-1387956.transient.int.api.azureml-test.ms"

             Write-Output $AETHER_APIENDPOINT
             Write-Output $AETHER_ENDPOINT

             Write-Host "##vso[task.setvariable variable=AETHER_APIENDPOINT]$AETHER_APIENDPOINT"
             Write-Host "##vso[task.setvariable variable=AETHER_ENDPOINT]$AETHER_ENDPOINT"

        - task: AzureCLI@1
          displayName: 'Run Tests'
          inputs:
            azureSubscription: 'Azure Infra INT environment deployments'
            scriptLocation: inlineScript
            inlineScript: 'dotnet test $(PipelineArtifactPath)/aetherdrop/Release/AnyCPU/app/aether/BBAetherLibrary/BBAetherLibrary.Metastore.ServiceTests/$(TargetFramework)/BBAetherLibrary.Metastore.ServiceTests.dll  --logger:trx;LogFileName=results.trx --settings $(PipelineArtifactPath)/aetherdrop/Release/AnyCPU/app/aether/BBAetherLibrary/BBAetherLibrary.Metastore.ServiceTests/$(TargetFramework)/$(EnvName).runsettings'
          continueOnError: true

        - task: PublishTestResults@2
          displayName: 'Publish Test Results **/*.trx'
          inputs:
            testResultsFormat: VSTest
            testResultsFiles: '**/*.trx'
            failTaskOnFailedTests: true
            testRunTitle: MetastoreServiceTests

    - stage: datatransfer_integration_test
      displayName: DataTransfer Integration Tests
      dependsOn: deploy_to_int_transient
      jobs:

      - job: datatransfer_integration_test
        displayName: DataTransfer Integration Tests
        timeoutInMinutes: 120
        pool:
          name: pipeline-1ES-MMS
          type: windows
          isCustom: true
        variables:
          - name: TestHarness
            value: VSTS
          - name: skipComponentGovernanceDetection
            value: true

        steps:
        - checkout: none

        - task: DownloadPipelineArtifact@2
          displayName: 'Download CI Build Artifact: aetherdrop'
          inputs:
            source: specific
            project: Vienna
            pipeline: $(resources.pipeline.pipeline_ci_build.pipelineName)
            buildId: $(resources.pipeline.pipeline_ci_build.runName)
            allowPartiallySucceededBuilds: true
            artifactName: aetherdrop
            itemPattern:
            targetPath: '$(PipelineArtifactPath)\aetherdrop'

        - task: AzureKeyVault@1
          displayName: 'Azure Key Vault: aethercloudtest'
          inputs:
            azureSubscription: 'DCM-E2E-Test'
            KeyVaultName: aethercloudtest
            SecretsFilter: AetherCloudtestCert

        - task: PowerShell@2
          displayName: 'Setup Certificate'
          inputs:
            targetType: filePath
            filePath: '$(PipelineArtifactPath)/aetherdrop/Release/AnyCPU/app/aether/BBAetherLibrary/BBAetherLibrary.DataTransfer.IntegrationTests/$(TargetFramework)/setup_certificates.ps1'
            arguments: '$(AetherCloudtestCert)'

        - task: PowerShell@2
          displayName: 'Set Aether Endpoint'
          inputs:
            targetType: inline
            script: |
             $BUILD_SOURCEBRANCH=$Env:BUILD_SOURCEBRANCH
             $AETHER_APIENDPOINT=".transient.int.api.azureml-test.ms"
             $AETHER_ENDPOINT=".transient.int.api.azureml-test.ms"

             # Write-Output $BUILD_SOURCEBRANCH

             # if( $BUILD_SOURCEBRANCH -like "refs/pull/*/merge")
             # {
             #     # pull request case.
             #     Write-Output "Pull request release"
             #     $BUILD_SOURCEBRANCH -match "refs/pull/(?<pullid>[0-9]+)/merge"
             #     $Matches.pullid
             #     $AETHER_APIENDPOINT="https://pull-"+$Matches.pullid+".transient.int.api.azureml-test.ms"
             #     $AETHER_ENDPOINT="https://pull-"+$Matches.pullid+".transient.int.api.azureml-test.ms"
             # }
             # elseif ( $BUILD_SOURCEBRANCH -like "refs/heads/*")
             # {
             #     # manual request case.
             #     Write-Output "Manual release"
             #     $BUILD_SOURCEBRANCH -match "refs/heads/(?<branch>.+)"
             #     $branch = $Matches.branch  -replace "/","-"
             #     $branch = $branch  -replace "_","-"
             #     $AETHER_APIENDPOINT="https://"+$branch+".transient.int.api.azureml-test.ms"
             #     $AETHER_ENDPOINT="https://"+$branch+".transient.int.api.azureml-test.ms"
             # }
             # else
             # {
             #     exit(1)
             # }

             $AETHER_APIENDPOINT="https://pull-1387956.transient.int.api.azureml-test.ms"
             $AETHER_ENDPOINT="https://pull-1387956.transient.int.api.azureml-test.ms"

             Write-Output $AETHER_APIENDPOINT
             Write-Output $AETHER_ENDPOINT

             Write-Host "##vso[task.setvariable variable=AETHER_APIENDPOINT]$AETHER_APIENDPOINT"
             Write-Host "##vso[task.setvariable variable=AETHER_ENDPOINT]$AETHER_ENDPOINT"

        - task: VSTest@2
          displayName: 'VsTest - testAssemblies'
          inputs:
            testAssemblyVer2: |
             **\aether\BBAetherLibrary\BBAetherLibrary.DataTransfer.IntegrationTests\**\BBAetherLibrary.DataTransfer.IntegrationTests.dll
             !**\ref\**
            searchFolder: '$(PipelineArtifactPath)\aetherdrop'
            runSettingsFile: '$(PipelineArtifactPath)/aetherdrop/Release/AnyCPU/app/aether/BBAetherLibrary/BBAetherLibrary.DataTransfer.IntegrationTests/$(TargetFramework)/$(EnvName).runsettings'
            otherConsoleOptions: '/Parallel /logger:"console"'
            diagnosticsEnabled: True
            rerunFailedTests: false

    - stage: hyperdrive_integration_test
      displayName: HyperDrive Integration Tests
      dependsOn: deploy_to_int_transient
      jobs:

      - job: hyperdrive_integration_test
        displayName: HyperDrive Integration Tests
        timeoutInMinutes: 120
        pool:
          name: pipeline-1ES-MMS
          type: windows
          isCustom: true
        variables:
          - name: TestHarness
            value: VSTS
          - name: skipComponentGovernanceDetection
            value: true

        steps:
        - checkout: none

        - task: DownloadPipelineArtifact@2
          displayName: 'Download CI Build Artifact: aetherdrop'
          inputs:
            source: specific
            project: Vienna
            pipeline: $(resources.pipeline.pipeline_ci_build.pipelineName)
            buildId: $(resources.pipeline.pipeline_ci_build.runName)
            allowPartiallySucceededBuilds: true
            artifactName: aetherdrop
            itemPattern:
            targetPath: '$(PipelineArtifactPath)\aetherdrop'

        - task: AzureKeyVault@1
          displayName: 'Azure Key Vault: aethercloudtest'
          inputs:
            azureSubscription: 'DCM-E2E-Test'
            KeyVaultName: aethercloudtest
            SecretsFilter: AetherCloudtestCert

        - task: PowerShell@2
          displayName: 'Setup Certificate'
          inputs:
            targetType: filePath
            filePath: '$(PipelineArtifactPath)/aetherdrop/Release/AnyCPU/app/aether/BBAetherLibrary/BBAetherLibrary.HyperDrive.IntegrationTests/$(TargetFramework)/setup_certificates.ps1'
            arguments: '$(AetherCloudtestCert)'

        - task: PowerShell@2
          displayName: 'Set Aether Endpoint'
          inputs:
            targetType: inline
            script: |
             $BUILD_SOURCEBRANCH=$Env:BUILD_SOURCEBRANCH
             $AETHER_APIENDPOINT=".transient.int.api.azureml-test.ms"
             $AETHER_ENDPOINT=".transient.int.api.azureml-test.ms"

             # Write-Output $BUILD_SOURCEBRANCH

             # if( $BUILD_SOURCEBRANCH -like "refs/pull/*/merge")
             # {
             #     # pull request case.
             #     Write-Output "Pull request release"
             #     $BUILD_SOURCEBRANCH -match "refs/pull/(?<pullid>[0-9]+)/merge"
             #     $Matches.pullid
             #     $AETHER_APIENDPOINT="https://pull-"+$Matches.pullid+".transient.int.api.azureml-test.ms"
             #     $AETHER_ENDPOINT="https://pull-"+$Matches.pullid+".transient.int.api.azureml-test.ms"
             # }
             # elseif ( $BUILD_SOURCEBRANCH -like "refs/heads/*")
             # {
             #     # manual request case.
             #     Write-Output "Manual release"
             #     $BUILD_SOURCEBRANCH -match "refs/heads/(?<branch>.+)"
             #     $branch = $Matches.branch  -replace "/","-"
             #     $branch = $branch  -replace "_","-"
             #     $AETHER_APIENDPOINT="https://"+$branch+".transient.int.api.azureml-test.ms"
             #     $AETHER_ENDPOINT="https://"+$branch+".transient.int.api.azureml-test.ms"
             # }
             # else
             # {
             #     exit(1)
             # }

             $AETHER_APIENDPOINT="https://pull-1387956.transient.int.api.azureml-test.ms"
             $AETHER_ENDPOINT="https://pull-1387956.transient.int.api.azureml-test.ms"

             Write-Output $AETHER_APIENDPOINT
             Write-Output $AETHER_ENDPOINT

             Write-Host "##vso[task.setvariable variable=AETHER_APIENDPOINT]$AETHER_APIENDPOINT"
             Write-Host "##vso[task.setvariable variable=AETHER_ENDPOINT]$AETHER_ENDPOINT"

        # It has been disabled in original pipeline.
        # - task: VSTest@2
        #   displayName: 'VsTest - testAssemblies'
        #   inputs:
        #     testAssemblyVer2: |
        #      **\BBAetherLibrary.HyperDrive.IntegrationTests.dll
        #      !**\ref\**
        #     searchFolder: '$(PipelineArtifactPath)\aetherdrop'
        #     runSettingsFile: '$(PipelineArtifactPath)/aetherdrop/Release/AnyCPU/app/aether/BBAetherLibrary/BBAetherLibrary.HyperDrive.IntegrationTests/$(TargetFramework)/$(EnvName).runsettings'
        #     otherConsoleOptions: '/Parallel /logger:"console"'
        #     diagnosticsEnabled: True
        #   enabled: false

        - task: VSTest@2
          displayName: 'VsTest - case from BBAetherLibrary.ESCloud.IntegrationTests assembly'
          inputs:
            testAssemblyVer2: |
             **\aether\BBAetherLibrary\BBAetherLibrary.ESCloud.IntegrationTests\**\BBAetherLibrary.ESCloud.IntegrationTests.dll
             !**\ref\**
            searchFolder: '$(PipelineArtifactPath)\aetherdrop'
            testFiltercriteria: 'FullyQualifiedName=BBAetherLibrary.ESCloud.IntegrationTests.ESTests.RunEsCloudForCommandComponentWithHyperDriveConfiguration|FullyQualifiedName=BBAetherLibrary.ESCloud.IntegrationTests.ESTests.RunEsCloudForScriptComponentWithHyperDriveConfigurationtOnKubernetes|FullyQualifiedName=BBAetherLibrary.ESCloud.IntegrationTests.ESTests.RunEsCloudForScriptComponentWithHyperDriveConfigurationtOnBatchAI'
            runSettingsFile: '$(PipelineArtifactPath)/aetherdrop/Release/AnyCPU/app/aether/BBAetherLibrary/BBAetherLibrary.ESCloud.IntegrationTests/$(TargetFramework)/$(EnvName).runsettings'
            otherConsoleOptions: '/Parallel /logger:"console"'
            diagnosticsEnabled: true

    - stage: escloud_service_test
      displayName: ESCloud Service Test
      dependsOn: deploy_to_int_transient
      jobs:

      - job: escloud_service_test
        displayName: ESCloud Service Test
        timeoutInMinutes: 120
        pool:
          name: pipeline-1ES-MMS
          type: windows
          isCustom: true
        variables:
          - name: TestHarness
            value: VSTS
          - name: skipComponentGovernanceDetection
            value: true

        steps:
        - checkout: none

        - task: DownloadPipelineArtifact@2
          displayName: 'Download CI Build Artifact: aetherdrop'
          inputs:
            source: specific
            project: Vienna
            pipeline: $(resources.pipeline.pipeline_ci_build.pipelineName)
            buildId: $(resources.pipeline.pipeline_ci_build.runName)
            allowPartiallySucceededBuilds: true
            artifactName: aetherdrop
            itemPattern:
            targetPath: '$(PipelineArtifactPath)\aetherdrop'

        - task: AzureKeyVault@1
          displayName: 'Azure Key Vault: aethercloudtest'
          inputs:
            azureSubscription: 'DCM-E2E-Test'
            KeyVaultName: aethercloudtest
            SecretsFilter: AetherCloudtestCert

        - task: PowerShell@2
          displayName: 'Setup Certificate'
          inputs:
            targetType: filePath
            filePath: '$(PipelineArtifactPath)/aetherdrop/Release/AnyCPU/app/aether/BBAetherLibrary/BBAetherLibrary.ESCloud.ServiceTests/$(TargetFramework)/setup_certificates.ps1'
            arguments: '$(AetherCloudtestCert)'

        - task: PowerShell@2
          displayName: 'Set Aether Endpoint'
          inputs:
            targetType: inline
            script: |
             $BUILD_SOURCEBRANCH=$Env:BUILD_SOURCEBRANCH
             $AETHER_APIENDPOINT=".transient.int.api.azureml-test.ms"
             $AETHER_ENDPOINT=".transient.int.api.azureml-test.ms"

             # Write-Output $BUILD_SOURCEBRANCH

             # if( $BUILD_SOURCEBRANCH -like "refs/pull/*/merge")
             # {
             #     # pull request case.
             #     Write-Output "Pull request release"
             #     $BUILD_SOURCEBRANCH -match "refs/pull/(?<pullid>[0-9]+)/merge"
             #     $Matches.pullid
             #     $AETHER_APIENDPOINT="https://pull-"+$Matches.pullid+".transient.int.api.azureml-test.ms"
             #     $AETHER_ENDPOINT="https://pull-"+$Matches.pullid+".transient.int.api.azureml-test.ms"
             # }
             # elseif ( $BUILD_SOURCEBRANCH -like "refs/heads/*")
             # {
             #     # manual request case.
             #     Write-Output "Manual release"
             #     $BUILD_SOURCEBRANCH -match "refs/heads/(?<branch>.+)"
             #     $branch = $Matches.branch  -replace "/","-"
             #     $branch = $branch  -replace "_","-"
             #     $AETHER_APIENDPOINT="https://"+$branch+".transient.int.api.azureml-test.ms"
             #     $AETHER_ENDPOINT="https://"+$branch+".transient.int.api.azureml-test.ms"
             # }
             # else
             # {
             #     exit(1)
             # }

             $AETHER_APIENDPOINT="https://pull-1387956.transient.int.api.azureml-test.ms"
             $AETHER_ENDPOINT="https://pull-1387956.transient.int.api.azureml-test.ms"

             Write-Output $AETHER_APIENDPOINT
             Write-Output $AETHER_ENDPOINT

             Write-Host "##vso[task.setvariable variable=AETHER_APIENDPOINT]$AETHER_APIENDPOINT"
             Write-Host "##vso[task.setvariable variable=AETHER_ENDPOINT]$AETHER_ENDPOINT"

        - task: AzureCLI@1
          displayName: 'Run Tests'
          inputs:
            azureSubscription: 'Azure Infra INT environment deployments'
            scriptLocation: inlineScript
            inlineScript: 'dotnet test $(PipelineArtifactPath)/aetherdrop/Release/AnyCPU/app/aether/BBAetherLibrary/BBAetherLibrary.ESCloud.ServiceTests/$(TargetFramework)/BBAetherLibrary.ESCloud.ServiceTests.dll  --logger:trx;LogFileName=results.trx --settings $(PipelineArtifactPath)/aetherdrop/Release/AnyCPU/app/aether/BBAetherLibrary/BBAetherLibrary.ESCloud.ServiceTests/$(TargetFramework)/$(EnvName).runsettings'
          continueOnError: true

# {
#   "$schema": "https://ev2schema.azure.net/schemas/2020-01-01/scopeBindings.json",
#   "scopeBindings":
#   [
#     {
#       "scopeTagName": "msftkube",
#       "bindings":
#       [
#         {
#           "find": "__MSFTKUBECMD__",
#           "replaceWith": "release -v -g aether extra-cloudlet -e int-transient"
#         },
#         {
#           "find": "__RELEASE_ID__",
#           "replaceWith": "$(Release.ReleaseId)"
#         },
#         {
#           "find": "__RELEASE_DEFINITION_ID__",
#           "replaceWith": "$(Release.DefinitionId)"
#         },
#         {
#           "find": "__RELEASE_DEFINITION_NAME__",
#           "replaceWith": "$(Release.DefinitionName)"
#         },
#         {
#           "find": "__MSFTKUBE_SHARED_TASK_GROUP__",
#           "replaceWith": "true"
#         }
#       ]
#     }
#   ]
# }
