{"version": "latest", "suppressionSets": {"default": {"name": "default", "createdDate": "2024-06-18 08:06:33Z", "lastUpdatedDate": "2024-06-18 08:06:33Z"}}, "results": {"bf49aa5ceee6cd8213ea273bd270adbcbd8b67e71724c755c32a9214e025f7c6": {"signature": "bf49aa5ceee6cd8213ea273bd270adbcbd8b67e71724c755c32a9214e025f7c6", "alternativeSignatures": [], "target": "src/azureml-api/src/Dataset/EntryPoints.Tests/IntegrationTestsBootstrap.ps1", "memberOf": ["default"], "tool": "credscan", "ruleId": "CSCAN-GENERAL0060", "type": null, "expirationDate": null, "createdDate": "2024-06-21 08:11:53Z"}, "d3a3db2d9456722c2a8e97a264f672962434582d080eb7b2f87697b89aa6e5c2": {"signature": "d3a3db2d9456722c2a8e97a264f672962434582d080eb7b2f87697b89aa6e5c2", "alternativeSignatures": [], "target": "src/azureml-api/src/AccountRP/EntryPoints.Gated.Tests/OneRpTests/ExtensionsTest/RequestResponseLoggingExtensionsTests.cs", "memberOf": ["default"], "tool": "credscan", "ruleId": "CSCAN-GENERAL0060", "type": null, "expirationDate": null, "createdDate": "2024-06-21 08:11:53Z"}, "eb1393291ee5df4b2aa8221449fcf65629093c84714a8d1754fa1b7369c7a546": {"signature": "eb1393291ee5df4b2aa8221449fcf65629093c84714a8d1754fa1b7369c7a546", "alternativeSignatures": [], "target": "src/azureml-api/src/AccountRP/EntryPoints.Gated.Tests/OneRpTests/ExtensionsTest/RequestResponseLoggingExtensionsTests.cs", "memberOf": ["default"], "tool": "credscan", "ruleId": "CSCAN-GENERAL0060", "type": null, "expirationDate": null, "createdDate": "2024-06-21 08:11:53Z"}, "67c965c915b0f3b3d5449246d50f23f648f8edf8dd97211fd29afe46702b76d4": {"signature": "67c965c915b0f3b3d5449246d50f23f648f8edf8dd97211fd29afe46702b76d4", "alternativeSignatures": [], "target": "src/azureml-api/src/AccountRP/EntryPoints.Gated.Tests/OneRpTests/ExtensionsTest/RequestResponseLoggingExtensionsTests.cs", "memberOf": ["default"], "tool": "credscan", "ruleId": "CSCAN-GENERAL0060", "type": null, "expirationDate": null, "createdDate": "2024-06-21 08:11:53Z"}, "f8fb9237d7aeafce4ecf10902cc043c3d8f37e0c3983a4406036b2c98d6d8dca": {"signature": "f8fb9237d7aeafce4ecf10902cc043c3d8f37e0c3983a4406036b2c98d6d8dca", "alternativeSignatures": [], "target": "src/azureml-api/src/AccountRP/EntryPoints.Gated.Tests/OneRpTests/ExtensionsTest/RequestResponseLoggingExtensionsTests.cs", "memberOf": ["default"], "tool": "credscan", "ruleId": "CSCAN-GENERAL0060", "type": null, "expirationDate": null, "createdDate": "2024-06-21 08:11:53Z"}, "6e8afea5c39e555e3f40defe86b7663cf925da85eb2df7d7d8697e5e7d2f18a8": {"signature": "6e8afea5c39e555e3f40defe86b7663cf925da85eb2df7d7d8697e5e7d2f18a8", "alternativeSignatures": [], "target": "src/azureml-api/src/AccountRP/EntryPoints.Gated.Tests/OneRpTests/ExtensionsTest/RequestResponseLoggingExtensionsTests.cs", "memberOf": ["default"], "tool": "credscan", "ruleId": "CSCAN-GENERAL0060", "type": null, "expirationDate": null, "createdDate": "2024-06-21 08:11:53Z"}, "aab8d3cbfb40a62ca81728682d373944e6734fbedc4aa89eedc7077c3a2a0d7d": {"signature": "aab8d3cbfb40a62ca81728682d373944e6734fbedc4aa89eedc7077c3a2a0d7d", "alternativeSignatures": [], "target": "src/azureml-job-runtime/Hosttools/src/hosttools/streamer/blobstreamer_test.go", "memberOf": ["default"], "tool": "credscan", "ruleId": "CSCAN-AZURE0070", "type": null, "expirationDate": null, "createdDate": "2024-06-21 08:11:53Z"}, "f18d02c6a52a7d654b068ad4b0c7da4fc70704fe99a5c9d2439a9afbdb968666": {"signature": "f18d02c6a52a7d654b068ad4b0c7da4fc70704fe99a5c9d2439a9afbdb968666", "alternativeSignatures": [], "target": "src/azureml-api/src/RAISvc/SimulationParametersLibrary/task_simulator/parameters.json", "memberOf": ["default"], "tool": "credscan", "ruleId": "CSCAN-GENERAL0060", "type": null, "expirationDate": null, "createdDate": "2024-06-21 08:11:53Z"}, "9c16c66dfb3b714e0f7a07d855a51ec561c154bedb1303b7c2fce090fbbf7af7": {"signature": "9c16c66dfb3b714e0f7a07d855a51ec561c154bedb1303b7c2fce090fbbf7af7", "alternativeSignatures": [], "target": "src/aether/platform/msftkubeConfig/deploy/specs/all-base.yaml", "memberOf": ["default"], "tool": "credscan", "ruleId": "CSCAN-GENERAL0060", "type": null, "expirationDate": null, "createdDate": "2024-06-21 08:11:53Z"}, "789be0c5082888e4ad432a2b9986b0d84bc7d8892448abbf20cdb87959f7e7c6": {"signature": "789be0c5082888e4ad432a2b9986b0d84bc7d8892448abbf20cdb87959f7e7c6", "alternativeSignatures": [], "target": "src/aether/platform/msftkubeConfig/deploy/specs/all-base.yaml", "memberOf": ["default"], "tool": "credscan", "ruleId": "CSCAN-GENERAL0060", "type": null, "expirationDate": null, "createdDate": "2024-06-21 08:11:53Z"}, "c258d56527efa0375a4a4669a1a3278a0a84c3a77bd5e81f1b5dda722d1e3cac": {"signature": "c258d56527efa0375a4a4669a1a3278a0a84c3a77bd5e81f1b5dda722d1e3cac", "alternativeSignatures": [], "target": "src/aether/platform/backendV2/shared/SharedConfigurationSettings/SharedSettings.ini", "memberOf": ["default"], "tool": "credscan", "ruleId": "CSCAN-AZURE0151", "type": null, "expirationDate": null, "createdDate": "2024-06-21 08:11:53Z"}, "b7c8f1269c0868ad7f76472ee4ef7f794463b4872bd4a40a0bcdff4f634172ed": {"signature": "b7c8f1269c0868ad7f76472ee4ef7f794463b4872bd4a40a0bcdff4f634172ed", "alternativeSignatures": [], "target": "src/azureml-api/src/Dataset/EntryPoints.Tests/IntegrationTestsBootstrap_DataCall.ps1", "memberOf": ["default"], "tool": "credscan", "ruleId": "CSCAN-GENERAL0060", "type": null, "expirationDate": null, "createdDate": "2024-06-21 08:11:53Z"}, "aba901c2fcac587db8630023e2904c350ee1b8a94eccfa2225e23f3b63c380a7": {"signature": "aba901c2fcac587db8630023e2904c350ee1b8a94eccfa2225e23f3b63c380a7", "alternativeSignatures": [], "target": "src/azureml-api/src/HyperDrive/hyperdrive/documentation/static/lib/marked_v0.3.12/marked.min.js", "memberOf": ["default"], "tool": "credscan", "ruleId": "@microsoft/sdl/no-html-method", "type": null, "expirationDate": null, "createdDate": "2025-07-31 14:11:53Z"}, "fd6f1da49014dce86cdfca9a28b44cf6cba6d2f680df3e7dd79c663d94c8577b": {"signature": "fd6f1da49014dce86cdfca9a28b44cf6cba6d2f680df3e7dd79c663d94c8577b", "alternativeSignatures": [], "target": "src/azureml-api/src/RAISvc/SimulationParametersLibrary/task_simulator/parameters/grounding/grounding.json", "memberOf": ["default"], "tool": "credscan", "ruleId": "CSCAN-GENERAL0060", "type": null, "expirationDate": null, "createdDate": "2025-08-01 14:29:53Z"}}}