resources:
  repositories:
  - repository: self
    type: git
    ref: master
  - repository: OneBranchTemplateRepo
    type: git
    name: OneBranch.Pipelines/GovernedTemplates
    ref: refs/heads/main

parameters:
- name: transientOnly
  displayName: Transient Environment only?
  type: boolean
  default: false
- name: deploymentGroup
  displayName: msftkube deployment group
  type: string
  default: "aether"
  values:
  - "aether"
  - "aether-ae3pservice"
  - "aether-dcm"
  - "aether-cloudlet"
  - "aether-metastore"

variables:
  skipComponentGovernanceDetection: true
  buildConfiguration: 'Release'
  OwningTeam: 'AEther'
  DisableDockerDetector: true
  ComponentDetection.Timeout: 1200
  ${{ if eq(variables['Build.Reason'], 'PullRequest') }}: # VS code coverage for PR build
    testDataCollector: 'Code coverage'
    runSettings: 'platform/backendV2/vscoverage.runsettings'
    additionalParamForDotnetTest: ''
    additionalParamForGenerateCoverage: '$(Agent.TempDirectory)\*\coverage.cobertura.xml'
    additionalParamForPublishCoverage: '$(Agent.TempDirectory)/CodeCoverage/Cobertura.xml'
  ${{ else }}: # Coverlet coverage for general build
    testDataCollector: 'XPlat Code Coverage'
    runSettings: 'platform/backendV2/coverletcoverage.runsettings'
    additionalParamForDotnetTest: '/p:CollectCoverage=true /p:CoverletOutputFormat=cobertura /p:CoverletOutput=TestResults\Coverage'
    additionalParamForGenerateCoverage: '$(Agent.TempDirectory)\**\coverage.cobertura.xml'
    additionalParamForPublishCoverage: '$(Agent.TempDirectory)/**/coverage.cobertura.xml'

trigger:
  batch: true
  branches:
    include:
    - master
  paths:
    include:
    - 'src/aether/*'
    exclude:
    - 'src/aether/platform/AcisExtension/*'
    - 'src/aether/scripts/*'

extends:
  template: v2/OneBranch.NonOfficial.CrossPlat.yml@OneBranchTemplateRepo
  parameters:
    git:
      submodules: false
    globalSdl:
      asyncSdl: # https://aka.ms/obpipelines/asyncsdl
        enabled: false
      cg:
        failOnAlert: false
      tsa:
        enabled: false
      credscan:
        suppressionsFile: src\azureml-api\src\Designer\.config\CredScanSuppressions.json

    stages:
    - stage: Build_and_Test
      displayName: Build and Test
      jobs:
      - job: Job_1
        displayName: Clone, restore, build, test, push
        timeoutInMinutes: 120
        pool:
          name: pipeline-1ES-MMS
          type: windows
          isCustom: true
        variables:
          TestHarness: VSTS
          skipComponentGovernanceDetection: true
          ob_artifactBaseName: aetherdrop
          ob_sdl_suppression_suppressionFile: $(Build.SourcesDirectory)\src\aether\platform\backendV2\AdoPipelines\.gdn\pipelineci.gdnsuppress
          ob_sdl_suppression_suppressionSet: default
        steps:
        # Set git config to allow long paths because some wiki paths+filenames are too long
        - powershell: git config --system core.longpaths true
          displayName: Set git core.longpaths
        - checkout: self
          clean: true
          fetchDepth: 1
          fetchTags: false
          submodules: false
        - task: PipAuthenticate@1
          displayName: Use Azure Artifacts feed
          inputs:
            artifactFeeds: 'Vienna/Vienna_PublicPackages'
        - task: PythonScript@0
          displayName: 'Tag Build'
          condition: or(eq(variables['Build.Reason'], 'PullRequest'), eq(variables['Build.Reason'], 'IndividualCI'))
          inputs:
            scriptSource: 'filePath'
            scriptPath: 'src/aether/platform/backendV2/scripts/buildtag/addbuildtags.py'
            arguments: --user '' --token $(System.AccessToken) --mode $(Build.Reason)
        - task: UseDotNet@2
          displayName: 'Use .NET sdk from global.json'
          inputs:
            packageType: sdk
            useGlobalJson: true
        - task: UseDotNet@2
          displayName: 'Use .NET 6.0 sdk'
          inputs:
            packageType: sdk
            version: 6.0.x
        - task: NuGetAuthenticate@1
        - task: DotNetCoreCLI@2
          retryCountOnTaskFailure: 3
          displayName: dotnet build
          inputs:
            projects: src/aether/dirs.proj
            arguments: --configuration $(buildConfiguration)
            workingDirectory: src/aether
        - task: DotNetCoreCLI@2
          displayName: dotnet test
          inputs:
            command: test
            projects: src/aether/dirs.proj
            arguments: --configuration $(buildConfiguration) --no-restore --no-build --filter "FullyQualifiedName!~BBAetherLibrary&FullyQualifiedName!~AutoMLCloud&Category!=QTestSkip&AzLoginRequired!=true" --settings $(runSettings) --collect "$(testDataCollector)" $(additionalParamForDotnetTest)
            nobuild: True
            workingDirectory: src/aether
          env:
            ASPNETCORE_ENVIRONMENT: master
        - task: CmdLine@2
          displayName: Install ReportGenerator as a local tool and get CodeCoverage
          condition: ne(variables['Build.Reason'], 'PullRequest')
          inputs:
            script: |
              dotnet new tool-manifest
              dotnet tool install dotnet-reportgenerator-globaltool
              dotnet reportgenerator -reports:"$(additionalParamForGenerateCoverage)" -targetdir:"$(Agent.TempDirectory)\CodeCoverage" -reporttypes:"Cobertura"
        - task: PublishCodeCoverageResults@1
          displayName: Publish code coverage
          condition: ne(variables['Build.Reason'], 'PullRequest')
          inputs:
            codeCoverageTool: 'Cobertura'
            summaryFileLocation: '$(additionalParamForPublishCoverage)'
            reportDirectory: '$(Agent.TempDirectory)/CodeCoverage'
        - task: ManifestGeneratorTask@0
          displayName: "Generate SBOM for package"
          inputs:
            BuildDropPath: target/distrib
        - task: PublishPipelineArtifact@1
          displayName: 'Publish Pipeline Artifact'
          inputs:
            targetPath: target/distrib
            artifact: aetherdrop
        - task: NuGetAuthenticate@1
          displayName: 'NuGet Authenticate'
          condition: eq(variables['Build.SourceBranchName'], 'master')
        - task: NuGetCommand@2
          displayName: 'NuGet push'
          condition: eq(variables['Build.SourceBranchName'], 'master')
          inputs:
            command: push
            packagesToPush: packages/**/*.nupkg
            feedsToUse: 'config'
            publishVstsFeed: 'Vienna'
            nugetConfigPath: src/aether/NuGet.config
            allowPackageConflicts: true

      - job: Job_2
        displayName: Publish script artifact
        timeoutInMinutes: 120
        pool:
          name: pipeline-1ES-MMS
          type: windows
          isCustom: true
        variables:
          TestHarness: VSTS
          skipComponentGovernanceDetection: true
          ob_artifactBaseName: scripts
        steps:
        # Set git config to allow long paths because some wiki paths+filenames are too long
        - powershell: git config --system core.longpaths true
          displayName: Set git core.longpaths
        - checkout: self
          clean: true
          fetchDepth: 1
          fetchTags: false
          submodules: false
        - task: ManifestGeneratorTask@0
          displayName: "Generate SBOM for package"
          inputs:
            BuildDropPath: src/aether/platform/backendV2/scripts
        - task: PublishPipelineArtifact@1
          displayName: 'Publish Artifact: Scripts'
          inputs:
            targetPath: src/aether/platform/backendV2/scripts
            artifact: scripts

    - stage: Create_Drop
      displayName: Create Drop
      dependsOn: Build_and_Test
      jobs:
      - job: Job_3
        displayName: Create and Push drop
        pool:
          name: pipeline-1ES-ubuntu
          type: linux
          isCustom: true
        variables:
          ob_artifactBaseName: drop
        timeoutInMinutes: 120
        steps:
        - checkout: none
        - task: PipAuthenticate@1
          displayName: Use Azure Artifacts feed
          inputs:
            artifactFeeds: 'Vienna'
        - script: |
            docker system prune --all --force
          displayName: 'Docker clean up'
        - task: DownloadPipelineArtifact@2
          displayName: 'Download Pipeline Artifact'
          inputs:
            artifactName: aetherdrop
            targetPath: $(System.ArtifactsDirectory)
        - task: UsePythonVersion@0
          displayName: 'Use Python 3.9'
          inputs:
            versionSpec: 3.9
        - task: UseDotNet@2
          displayName: 'Use .NET sdk from global.json'
          inputs:
            packageType: sdk
            version: 6.0.x
            installationPath: "$(Agent.TempDirectory)/dotnet"
        - task: Bash@3
          displayName: Change nuget packages and cache to be on larger disk
          inputs:
           targetType: inline
           script: |
             echo "##vso[task.setvariable variable=NUGET_PACKAGES;]$(Agent.TempDirectory)/.nuget/packages"
             echo "##vso[task.setvariable variable=MSFTKUBE_NUGET_CACHE_LOCATION;]$(Agent.TempDirectory)/.cache"
        - task: NuGetAuthenticate@1
        - task: AzureCLI@2
          displayName: 'Create Drop (Aether Services)'
          inputs:
            azureSubscription: 'INFRA Viennadroptest connection'
            scriptType: bash
            scriptLocation: inlineScript
            inlineScript: |
              # Set environment variable to workaround bug in Azure CLI: https://github.com/Azure/azure-cli/issues/31419
              export AZURE_CORE_USE_MSAL_HTTP_CACHE=false

              echo $(Build.Reason)
              echo ${{ parameters.transientOnly }}

              # policyservice is removed from "aether" group, but still need to publish the drop currently,
              # so only specify group parameter if not default "aether" case.
              # TODO: use "parameters.deploymentGroup" directly after policyservice don't use this pipeline CI build artifacts.
              group_param=""
              if [ ${{ parameters.deploymentGroup }} != "aether" ]
              then
                  group_param="-g ${{ parameters.deploymentGroup }}"
              fi

              if [ $(Build.Reason) == 'PullRequest' ]
              then
                echo create drop for int-transient and int-master
                bash $(System.ArtifactsDirectory)/Release/AnyCPU/app/aether/bluebox/msftkubeconfig/scripts/task.sh -v finish_drop -e eastus2euap int int-transient $group_param
              elif [ ${{ parameters.transientOnly }} == True ]
              then
                echo transientOnly is True create drop for int-transient and int-release
                bash $(System.ArtifactsDirectory)/Release/AnyCPU/app/aether/bluebox/msftkubeconfig/scripts/task.sh -v finish_drop -e eastus2euap int int-transient $group_param
              else
                echo create drop for all
                bash $(System.ArtifactsDirectory)/Release/AnyCPU/app/aether/bluebox/msftkubeconfig/scripts/task.sh -v finish_drop $group_param
              fi
        - task: PowerShell@2
          displayName: "Prepare list of images for Component Detection Task"
          inputs:
            filePath: '$(System.ArtifactsDirectory)/Release/AnyCPU/app/aether/bluebox/msftkubeconfig/deployment/create-image-sbom.ps1'
            failOnStderr: true
            ignoreLASTEXITCODE: true
            pwsh: true
            workingDirectory: '$(System.ArtifactsDirectory)'
        - task: ComponentGovernanceComponentDetection@0
          displayName: Component Detection
        - task: CopyFiles@2
          displayName: Copy Files to drop
          inputs:
            SourceFolder: '$(System.ArtifactsDirectory)/_manifest'
            TargetFolder: '$(System.ArtifactsDirectory)/Release/AnyCPU/app/aether/bluebox/msftkubeconfig/_manifest'
        - task: PublishPipelineArtifact@1
          displayName: 'Publish Artifact: drop'
          inputs:
            targetPath: '$(System.ArtifactsDirectory)/Release/AnyCPU/app/aether/bluebox/msftkubeconfig'
            artifact: drop
        - script: |
            docker system prune --all --force
          condition: always()
          displayName: 'Docker clean up'
