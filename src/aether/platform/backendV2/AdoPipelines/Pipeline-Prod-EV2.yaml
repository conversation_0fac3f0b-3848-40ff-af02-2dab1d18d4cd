trigger: none

variables:
- name: command
  value: release_all
- name: commandArguments
  value: -v -g aether
- name: pipelineName
  value: pipeline_ci_build

parameters:
  - name: 'rolloutSpec'
    displayName: 'RolloutSpec'
    type: string
    default: imageindrop.RolloutSpec.json
  - name: deployNewRegion
    displayName: 'Deploy New Region'
    type: boolean
    default: false
  - name: enableManagedSDP
    displayName: 'Managed SDP Deployment'
    type: boolean
    default: true

resources:
  pipelines:
  - pipeline: pipeline_ci_build
    project: Vienna
    source: 'Pipeline CI Build Official'

extends:
  template: ../../../../azureml-infra/.pipelines/templates/yaml-release/aml-app-deploy.yml
  parameters:
    command: ${{ variables.command }}
    commandArguments: ${{ variables.commandArguments }}
    pipelineName: ${{ variables.pipelineName }}
    deployNewRegion: ${{ parameters.deployNewRegion }}
    rolloutSpec: ${{ parameters.rolloutSpec }}
    enableManagedSDP: ${{ parameters.enableManagedSDP }}
