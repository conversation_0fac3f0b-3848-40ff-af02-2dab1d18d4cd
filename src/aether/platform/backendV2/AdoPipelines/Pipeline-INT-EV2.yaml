resources:
  pipelines:
  - pipeline: pipeline_ci_build
    project: Vienna
    source: 'Pipeline CI Build Official'
    trigger:
      branches:
        include:
        - master

  repositories:
    - repository: repoOneBranch
      type: git
      name: OneBranch.Pipelines/GovernedTemplates
      ref: refs/heads/main

    - repository: repoVienna
      type: git
      name: vienna
      ref: refs/heads/master

variables:
  AETHER_APIENDPOINT: https://int.api.azureml-test.ms
  AETHER_ENDPOINT: https://westus3.aether-dev.ms
  pipelineName: pipeline_ci_build
  artifactName: 'drop'
  command: release_all
  commandArguments: -v -a ae3pservice nodeprocessor metastore metastorefe metastoreworker metastoreproxy storagecleanup diagnosticservice experimentsubmitter bbquotaservice cloudsubmitter datamanagementtracking cloudworker scheduler subgraphscloud
  rolloutSpec: 'imageindrop.RolloutSpec.json'
  PipelineArtifactPath: $(System.DefaultWorkingDirectory)\PipelineArtifacts
  TargetFramework: net8.0

extends:
  template: v2/OneBranch.NonOfficial.CrossPlat.yml@repoOneBranch
  parameters:
    git:
      submodules: false

    globalSDL:
      suppression:
        suppressionSet: default
        suppressionFile: $(Build.SourcesDirectory)\src\aether\platform\backendV2\AdoPipelines\.gdn\pipeline_int_ev2.gdnsuppress

    stages:
      - stage: Test_Import
        displayName: 'Test:Import Drop Images'
        dependsOn: []
        variables:
          ob_release_environment: Test
        jobs:
          - template: src/azureml-infra/.pipelines/templates/yaml-release/aml-import-drop-images-to-int.yml@repoVienna
            parameters:
              env: int
              pipelineName: ${{ variables.pipelineName }}
              artifactName: ${{ variables.artifactName }}

      - stage: Test_Deploy
        lockBehavior: sequential
        displayName: 'Test:Int'
        dependsOn: [Test_Import]
        variables:
          ob_release_environment: Test
        jobs:
          - template: src/azureml-infra/.pipelines/templates/yaml-release/aml-deploy-to-int.yml@repoVienna
            parameters:
              env: int
              pipelineName: ${{ variables.pipelineName }}
              artifactName: ${{ variables.artifactName }}
              msftkubeCmd: '${{ variables.command }} ${{ variables.commandArguments }}'
              rolloutSpec: ${{ variables.rolloutSpec }}

      - stage: integration_tests
        displayName: 'Integration Tests'
        dependsOn: Test_Deploy
        jobs:
        - job: integration_tests
          displayName: 'Integration Tests'
          timeoutInMinutes: 120
          pool:
            name: pipeline-1ES-MMS
            type: windows
            isCustom: true
          variables:
            TestHarness: VSTS
            skipComponentGovernanceDetection: true
          steps:
          - checkout: none

          - task: DownloadPipelineArtifact@2
            displayName: 'Download CI Build Artifact: aetherdrop'
            inputs:
              source: specific
              project: Vienna
              pipeline: $(resources.pipeline.pipeline_ci_build.pipelineName)
              buildId: $(resources.pipeline.pipeline_ci_build.runName)
              allowPartiallySucceededBuilds: true
              artifactName: aetherdrop
              itemPattern:
              targetPath: '$(PipelineArtifactPath)\aetherdrop'

          - task: AzureKeyVault@2
            displayName: 'Azure Key Vault: aethercloudtest'
            inputs:
              azureSubscription: 'DCM-E2E-Test'
              KeyVaultName: aethercloudtest
              SecretsFilter: AetherCloudtestCert

          - task: AzureKeyVault@2
            displayName: 'Azure Key Vault: databricks test key'
            inputs:
              azureSubscription: 'DCM-E2E-Test'
              KeyVaultName: aethercloudtest
              SecretsFilter: DataBricksE2EPAT

          - task: PowerShell@2
            displayName: 'Set Certtificates'
            inputs:
              targetType: filePath
              filePath: '$(PipelineArtifactPath)/aetherdrop/Release/AnyCPU/app/aether/BBAetherLibrary/BBAetherLibrary.IntegrationTests/$(TargetFramework)/setup_certificates.ps1'
              arguments: '$(AetherCloudtestCert)'

          - task: VSTest@2
            displayName: 'VsTest - testAssemblies'
            inputs:
              testAssemblyVer2: |
               **\aether\BBAetherLibrary\BBAetherLibrary.IntegrationTests\**\BBAetherLibrary.IntegrationTests.dll
               !**\ref\**
              searchFolder: '$(PipelineArtifactPath)'
              testFiltercriteria: 'FullyQualifiedName!=BBAetherLibrary.IntegrationTests.TestFixtures.PipelineRunTests.TestParameterizeDataStoreModeInPipelineParameter'
              runSettingsFile: '$(PipelineArtifactPath)/aetherdrop/Release/AnyCPU/app/aether/BBAetherLibrary/BBAetherLibrary.IntegrationTests/$(TargetFramework)/int.runsettings'
              overrideTestrunParameters: '-SolutionDirectory $(PipelineArtifactPath)/aetherdrop/Release/AnyCPU/aether/BBAetherLibrary/BBAetherLibrary.IntegrationTests'
              otherConsoleOptions: '/Parallel /logger:"console"'
              diagnosticsEnabled: true

      - stage: scheduler_test
        displayName: 'Scheduler Tests'
        dependsOn: Test_Deploy
        jobs:
        - job: scheduler_test
          displayName: 'Scheduler Tests'
          timeoutInMinutes: 120
          pool:
            name: promptflow-1es-pt-ubuntu
            type: linux
            isCustom: true
          variables:
            TestHarness: VSTS
            skipComponentGovernanceDetection: true
          steps:
          - checkout: none

          - task: DownloadPipelineArtifact@2
            displayName: 'Download CI Build Artifact: scripts'
            inputs:
              source: specific
              project: Vienna
              pipeline: $(resources.pipeline.pipeline_ci_build.pipelineName)
              buildId: $(resoutrces.pipeline.pipeline_ci_build.runName)
              allowPartiallySucceededBuilds: true
              artifactName: scripts
              itemPattern:
              targetPath: '$(System.DefaultWorkingDirectory)/PipelineArtifacts/scripts'

          - task: UsePythonVersion@0
            displayName: 'Use Python 3.9'
            inputs:
              versionSpec: 3.9

          - task: AzureCLI@2
            displayName: 'Run Tests'
            inputs:
              azureSubscription: 'DCM-E2E-Test'
              workingDirectory: '$(System.DefaultWorkingDirectory)/PipelineArtifacts/scripts/dcm_tests/scheduler_test'
              scriptType: bash
              scriptLocation: inlineScript
              inlineScript: |
               echo "THE TEST HAS BEEN DISABLED!"
               # echo "######## PIP INSTALL ########"
               # pip install azureml-defaults azureml-pipeline azure-cli-core azure-ai-ml azure-identity
               #
               # echo ""
               # echo "######## AZ SET #############"
               # echo 'az account set -s "96aede12-2f73-41cb-b983-6d11a904839b"'
               # az account set -s "96aede12-2f73-41cb-b983-6d11a904839b"

               # echo 'az account show'
               # az account show

               # echo 'az account list'
               # az account list

               # echo ""
               # echo "######## DELETE IF EXIST ####"
               # echo 'python detect_step_before_create.py --region int --host int'
               # python detect_step_before_create.py --region int --host int

               # echo ""
               # echo "######## AZ CREATE ##########"
               # echo 'az extension add -n ml --version 2.6.1 -y'
               # az extension add -n ml --version 2.6.1 -y

               # echo 'az configure --defaults group=dcm-int workspace=reuse-runner-ws'
               # az configure --defaults group=dcm-int workspace=reuse-runner-ws

               # echo 'az ml compute create -f create-instance.yml'
               # az ml compute create -f create-instance.yml

               # echo ""
               # echo "######## SCHEDULE ###########"
               # echo 'python compute_instance_schedule_validation.py --region int --host int'
               # python compute_instance_schedule_validation.py --region int --host int

      - stage: reuse_test
        displayName: 'Reuse Tests'
        dependsOn: Test_Deploy
        jobs:
        - job: reuse_test
          displayName: 'Reuse Tests'
          timeoutInMinutes: 120
          pool:
            name: promptflow-1es-pt-ubuntu
            type: linux
            isCustom: true
          variables:
            TestHarness: VSTS
            skipComponentGovernanceDetection: true
          steps:
          - checkout: none

          - task: DownloadPipelineArtifact@2
            displayName: 'Download CI Build Artifact: scripts'
            inputs:
              source: specific
              project: Vienna
              pipeline: $(resources.pipeline.pipeline_ci_build.pipelineName)
              buildId: $(resources.pipeline.pipeline_ci_build.runName)
              allowPartiallySucceededBuilds: true
              artifactName: scripts
              itemPattern:
              targetPath: '$(System.DefaultWorkingDirectory)/PipelineArtifacts/scripts'

          - task: UsePythonVersion@0
            displayName: 'Use Python 3.8'
            inputs:
              versionSpec: 3.8

          - task: AzureCLI@1
            displayName: 'Run Tests'
            env:
              REUSE_DATABRICKS_ID: e8d89fdc-00f2-4a40-8992-acac134ed12e
              REUSE_LAYER_1: 7df4f40c-7a16-4b50-a7a4-fdc328aba66a
              REUSE_LAYER_2: 447c7fd6-61a7-4e98-bd66-5cd38331f1e7
              REUSE_LAYER_3: 11b53af1-f916-4467-ab44-799afbf183a5
              REUSE_LAYER_4: baeb7dbe-48fe-4578-97d3-84c2b2aa1d69
              REUSE_LAYER_5: 736d93f8-99a3-4716-a934-cf2e40ba5559
              REUSE_LS_COMPONENT: d3fea1d5-feec-448c-bf8f-5dc2a9219890
              REUSE_SCOPE_ID: ba183476-48b6-445c-94e1-a829c9cea74a
              REUSE_SUBGRAPHRUN_ID: 709c052e-2380-47bf-a08a-236634637166
              REUSE_PIPELINERUN_ID1: 67687bee-4bc4-41d7-93dd-a973a5127a31
              REUSE_PIPELINERUN_ID2: 16e70b7e-e4a0-4a20-a8eb-7d32d0a9890d
              REUSE_PIPELINERUN_ID3: 3232e7af-ff0b-4f81-966a-bc910e137fe1
              REUSE_PIPELINERUN_ID4: c4e26481-42ab-4402-8d71-b6df5dbf511c
              REUSE_PIPELINERUN_ID5_1: 8072a27a-e218-45e8-bcd5-a524faf08226
              REUSE_PIPELINERUN_ID5_2: 2f1c28cd-e2bf-45db-a8c8-64a7eb297b0c
              REUSE_SCOPE_ID1: 4bded875-24cd-47f4-984a-37a4f8144c21
              REUSE_SCOPE_ID2: 67a05329-b787-4a4b-8cfb-c4188c01b1ed
              REUSE_SCOPE_ID3: f3fdc0ef-811e-4b05-a497-7f3f875cc654
              REUSE_SCOPE_ID4: 91f1ec88-e8e2-49c1-8c6a-136eda99a441
              REUSE_SCOPE_ID5_1: c2213ba1-d8c7-4a30-a083-71a5e75a5ef3
              REUSE_SCOPE_ID5_2: e8f65eaa-ce0f-49fe-bdac-2f4946e124a9
              ENFORCE_RERUN_ORIGIN_RUN: 595bcdb8-24cc-44a9-8dc4-90c33a079bfb
            inputs:
              azureSubscription: 'DCM-E2E-Test'
              workingDirectory: '$(System.DefaultWorkingDirectory)/PipelineArtifacts/scripts/dcm_tests/runner'
              scriptLocation: inlineScript
              inlineScript: |
               echo "THE TEST HAS BEEN DISABLED!"
               #  pip install -r <(sed '/^\(pywin32\|mldesigner\)/d' ../orchestrator/configs/pip_requirements.txt)
               #  pip install strictyaml==1.6.2 azureml-pipeline azure-cli-core mldesigner==0.1.0b12 azure-ml-component==0.9.18.post2
               #  pip list
               #  python test.py --region int --host int

      - stage: orchestration_test
        displayName: 'Orchestration Tests'
        dependsOn: Test_Deploy
        jobs:
        - job: orchestration_test
          displayName: 'Orchestration Tests'
          timeoutInMinutes: 120
          pool:
            name: promptflow-1es-pt-ubuntu
            type: linux
            isCustom: true
          variables:
            TestHarness: VSTS
            skipComponentGovernanceDetection: true
          steps:
          - checkout: none

          - task: DownloadPipelineArtifact@2
            displayName: 'Download CI Build Artifact: scripts'
            inputs:
              source: specific
              project: Vienna
              pipeline: $(resources.pipeline.pipeline_ci_build.pipelineName)
              buildId: $(resources.pipeline.pipeline_ci_build.runName)
              allowPartiallySucceededBuilds: true
              artifactName: scripts
              itemPattern:
              targetPath: '$(System.DefaultWorkingDirectory)/PipelineArtifacts/scripts'

          - task: AzureCLI@2
            displayName: 'Run tests'
            inputs:
              azureSubscription: 'DCM-E2E-Test'
              workingDirectory: '$(System.DefaultWorkingDirectory)/PipelineArtifacts/scripts/dcm_tests/orchestrator'
              scriptLocation: inlineScript
              scriptType: bash
              inlineScript: |
                echo "THE TEST HAS BEEN DISABLED!"
                # pip install -r <(sed '/^\(pywin32\|azure-ml-component\|mldesigner\)/d' ../orchestrator/configs/pip_requirements.txt)
                # pip install azureml-pipeline azure-cli-core mldesigner==0.1.0b12 azure-ml-component==0.9.18.post2
                # pip list
                # python3 main.py \
                #     --auth-by-env 1 \
                #     --retry-for-case 3 \
                #     --retry-for-detail 5 \
                #     --exclude-prod-only-test True \
                #     --config ./configs/int_dcm-fastorc-test.json \
                #     --compute-name "cpu-cluster" \
                #     --skip-case DynamicSubgraphE2ETestCase \
                #     --skip-case PipelineCancelledWithCompilerJobRunningStateTestCase \
                #     --skip-case DefaultErrorDetailCheckTestCase \
                #     --skip-case SpecificErrorDetailCheckTestCase \
                #     --skip-case OperatorInDeepSubgraphTestCase \
                #     --skip-case DescSubgraphNodeControlledByOperatorTestCase \
                #     --skip-case CompilerJobFailedWithContinueOnStepFailureTrueValueTestCase \
                #     --skip-case CompilerJobFailedWithContinueOnStepFailureFalseValueTestCase \
                #     --skip-case DynamicSubgraphCombinedWithDoWhileTestCase \
                #     --skip-case DynamicSubgraphCombinedWithConditionTestCase \
                #     --skip-case DynamicSubgraphWithStreamTestCase \
                #     --skip-case StreamToSubgraphAfterCompletedEmptyOutputsTestCase \
                #     --skip-case StreamToSubgraphAfterCompletedMissingOutputsTestCase \
                #     --skip-case ComponentSuccessWithNullParameterTestCase\
                #     --skip-case DowhileComponentAscendentHasFailedControlOutputTestCase\
                #     --skip-case DowhilePipelineAscendentHasFailedControlOutputTestCase

      - stage: enhanced_test
        displayName: 'Enhanced Tests'
        dependsOn: Test_Deploy
        jobs:
        - job: enhanced_test
          displayName: 'Enhanced Tests'
          timeoutInMinutes: 120
          pool:
            name: promptflow-1es-pt-ubuntu
            type: linux
            isCustom: true
          variables:
            TestHarness: VSTS
            skipComponentGovernanceDetection: true
          steps:
          - checkout: none

          - task: DownloadPipelineArtifact@2
            displayName: 'Download CI Build Artifact: scripts'
            inputs:
              source: specific
              project: Vienna
              pipeline: $(resources.pipeline.pipeline_ci_build.pipelineName)
              buildId: $(resources.pipeline.pipeline_ci_build.runName)
              allowPartiallySucceededBuilds: true
              artifactName: scripts
              itemPattern:
              targetPath: '$(System.DefaultWorkingDirectory)/PipelineArtifacts/scripts'

          - task: AzureCLI@2
            displayName: 'Run Tests'
            timeoutInMinutes: 120
            inputs:
              azureSubscription: 'DCM-E2E-Test'
              workingDirectory: '$(System.DefaultWorkingDirectory)/PipelineArtifacts/scripts/dcm_tests/enhanced_tests/webxt_cases'
              useGlobalConfig: true
              scriptType: bash
              scriptLocation: inlineScript
              inlineScript: |
                echo "THE TEST HAS BEEN DISABLED!"
                # pip install pip==23.0.1 setuptools==67.5.1 wheel==0.38.1
                # pip install -r pip_requirements.txt
                # pip list
                #
                # export COSMOS_DB_URL=$(COSMOS_DB_URL)
                # export COSMOS_DB_KEY=$(COSMOS_DB_KEY)
                #
                # python main.py --webxt-ci-timeout 3600 --webxt-pw-ofe-timeout 3600 --webxt-ci-v2-timeout 3600 --region centraluseuap -snum 5 -sub "96aede12-2f73-41cb-b983-6d11a904839b" -rg "dcm-int"  -ws "dcm-fastorc-test" --webxt-cases webxt_lego
