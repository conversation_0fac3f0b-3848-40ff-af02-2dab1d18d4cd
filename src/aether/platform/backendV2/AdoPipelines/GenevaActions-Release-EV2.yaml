trigger: none

variables:
  - name: app
    value: pipeline
  - name: ev2ConfigPath
    value: pipeline-geneva-action
  - name: pipelineName
    value: "GenevaActions CI Build Official"


resources:
  pipelines:
  - pipeline: "GenevaActions CI Build Official"
    project: Vienna
    source: "GenevaActions CI Build Official"

extends:
  template: ../../../../azureml-infra/.pipelines/templates/yaml-release/aml-geneva-action-deploy.yml
  parameters:
    pipelineName: ${{ variables.pipelineName }}
    app: ${{ variables.app }}
    ev2ConfigPath: ${{ variables.ev2ConfigPath }}