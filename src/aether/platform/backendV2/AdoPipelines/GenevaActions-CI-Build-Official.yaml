# Geneva Actions Extensions build pipeline
trigger:
  batch: true
  branches:
    include:
    - master
  paths:
    include:
    - 'src/aether/platform/AcisExtension/*'

resources:
  repositories:
  - repository: self
    type: git
    ref: master
  - repository: OneBranchTemplateRepo
    type: git
    name: OneBranch.Pipelines/GovernedTemplates
    ref: refs/heads/main

variables:
  buildConfiguration: 'Release'
  BuildPlatform: 'Any CPU'
  ${{ if eq(variables['Build.Reason'], 'PullRequest') }}: # VS code coverage for PR build
    testDataCollector: 'Code coverage'
    runSettings: 'platform/backendV2/vscoverage.runsettings'
  ${{ else }}: # Coverlet coverage for general build
    testDataCollector: 'XPlat Code Coverage'
    runSettings: 'platform/backendV2/coverletcoverage.runsettings'
  OwningTeam: 'AEther'
  Codeql.TSAEnabled:  true
  DisableDockerDetector: true
  WindowsContainerImage: 'onebranch.azurecr.io/windows/ltsc2019/vse2022:latest' # Docker image which is used to build the project https://aka.ms/obpipelines/containers

extends:
  template: v2/OneBranch.Official.CrossPlat.yml@OneBranchTemplateRepo
  parameters:
    git:
      submodules: false
      longpaths: true
    globalSdl:
      sbom:
        # https://eng.ms/docs/cloud-ai-platform/azure-edge-platform-aep/aep-engineering-systems/productivity-and-experiences/ce-legacy-infrastructure/onebranch/build/howtoguides/generatingsbom
        enabled: true
      asyncSdl: # https://aka.ms/obpipelines/asyncsdl
        enabled: false
      cg:
        failOnAlert: false
      tsa:
        enabled: false
      credscan:
        suppressionsFile: src\azureml-api\src\Designer\.config\CredScanSuppressions.json
      codeql:
        cadence: 0 # Always refresh CodeQL on every build

    stages:
    - stage: Build_and_Test
      displayName: Build and Test
      jobs:
      - job: Job_1
        displayName: Clone, restore, build pipelineGAPackage, EsrpCodeSigning, test, push
        timeoutInMinutes: 120
        pool:
          # name: pipeline-1ES-MMS
          type: windows
          # isCustom: true
        variables:
          - name: TestHarness
            value: VSTS
          - name: skipComponentGovernanceDetection
            value: true
          - name: ob_artifactBaseName
            value: pipelineGAPackage
          - name: ob_outputDirectory
            value: src/aether/platform/AcisExtension/AMLPipeline.AcisExtension/GAPackage
        steps:
        # Set git config to allow long paths because some wiki paths+filenames are too long
        # - powershell: git config --system core.longpaths true
        #   displayName: Set git core.longpaths
        # - checkout: self
        #   fetchDepth: 1
        #   persistCredentials: True
        #   clean: true
        #   fetchTags: false
        - task: PythonScript@0
          displayName: 'Tag Build'
          condition: or(eq(variables['Build.Reason'], 'PullRequest'), eq(variables['Build.Reason'], 'IndividualCI'))
          inputs:
            scriptSource: 'filePath'
            scriptPath: 'src/aether/platform/backendV2/scripts/buildtag/addbuildtags.py'
            arguments: --user '' --token $(System.AccessToken) --mode $(Build.Reason)
        - task: UseDotNet@2
          displayName: 'Use .NET sdk from global.json'
          inputs:
            packageType: sdk
            useGlobalJson: true
        - task: UseDotNet@2
          displayName: 'Use .NET sdk 6.0'
          inputs:
            packageType: sdk
            version: 6.0.x
        - task: NuGetAuthenticate@1
        - task: DotNetCoreCLI@2
          displayName: 'dotnet restore'
          inputs:
            command: 'restore'
            projects: 'src/aether/platform/AcisExtension/AcisExtension.sln'
            selectOrConfig: config
            nugetConfigPath: src/aether/NuGet.Config
            verbosityRestore: Minimal
        # - task: DotNetCoreCLI@2
        #   displayName: 'Build binaries'
        #   inputs:
        #     workingDirectory: 'src/aether'
        #     command: 'build'
        #     projects: 'src/aether/platform/AcisExtension/AcisExtension.sln'
        #     arguments: '--configuration Release /p:GAExtensionVersion=$(Build.BuildNumber) /p:SkipPackaging=true'
        - task: MSBuild@1
          displayName: msbuild AcisExtension
          inputs:
            solution: 'src/aether/platform/AcisExtension/AcisExtension.sln'
            configuration: $(buildConfiguration)
        - task: onebranch.pipeline.signing@1
          displayName: 'Sign Geneva Actions binaries before packaging'
          inputs:
            command: 'sign'
            signing_environment: 'azure-ado'
            signing_profile: 'internal_azure_service'
            files_to_sign: '**/*.exe;**/*.dll;**/*.ps1;**/*.psm1'
            search_root: 'src/aether/platform/AcisExtension/AMLPipeline.AcisExtension'
        - task: DotNetCoreCLI@2
          displayName: 'Build GA packages using signed binaries'
          inputs:
            workingDirectory: 'src/aether/platform/AcisExtension'
            command: 'build'
            projects: 'src/aether/platform/AcisExtension/AMLPipeline.AcisExtension/AMLPipeline.AcisExtension.csproj'
            arguments: '--configuration Release -t:PackagingGAExtension'
        - task: onebranch.pipeline.signing@1
          displayName: 'Sign Geneva Action Packages'
          inputs:
            command: 'sign'
            signing_profile: '143360024'
            files_to_sign: '*.Package'
            search_root: 'src/aether/platform/AcisExtension/AMLPipeline.AcisExtension/GAPackage/'
        # - task: EsrpCodeSigning@2
        #   displayName: ESRP CodeSigning for Geneva Action packages
        #   inputs:
        #     ConnectedServiceName: 904eac4b-edf9-44b0-a579-5bfb44b7c025
        #     FolderPath: src/aether/platform/AcisExtension/AMLPipeline.AcisExtension/GAPackage
        #     Pattern: '*.Package'
        #     signConfigType: inlineSignParams
        #     inlineOperation: |
        #       [
        #           {
        #               "KeyCode" : "CP-236167",
        #               "OperationCode" : "GenevaActionsPackageSign",
        #               "Parameters" : {
        #                   "FileDigest" : "/fd SHA256"
        #               },
        #               "ToolName" : "sign",
        #               "ToolVersion" : "1.0"
        #           },
        #           {
        #               "KeyCode" : "CP-236167",
        #               "OperationCode" : "GenevaActionsPackageVerify",
        #               "Parameters" : {},
        #               "ToolName" : "sign",
        #               "ToolVersion" : "1.0"
        #           }
        #       ]
        # - task: PublishPipelineArtifact@1
        #   displayName: 'Publish Artifact: GAPackage'
        #   inputs:
        #     targetPath: src/aether/platform/AcisExtension/AMLPipeline.AcisExtension/GAPackage
        #     artifact: pipelineGAPackage

      - job: Job_2
        displayName: Clone, restore, build, test, push
        timeoutInMinutes: 120
        pool:
          # name: pipeline-1ES-MMS
          type: windows
          # isCustom: true
        variables:
          - name: TestHarness
            value: VSTS
          - name: skipComponentGovernanceDetection
            value: true
          - name: ob_artifactBaseName
            value: aetherDrop
          - name: ob_outputDirectory
            value: target/distrib/$(buildConfiguration)/AnyCPU/app/aether/bluebox
          - name: ob_sdl_suppression_suppressionFile
            value: $(Build.SourcesDirectory)\src\aether\platform\backendV2\AdoPipelines\.gdn\genevaaction.gdnsuppress
          - name: ob_sdl_suppression_suppressionSet
            value: default
        steps:
        # Set git config to allow long paths because some wiki paths+filenames are too long
        # - powershell: git config --system core.longpaths true
        #   displayName: Set git core.longpaths
        # - checkout: self
        #   fetchDepth: 1
        #   persistCredentials: True
        #   clean: true
        #   fetchTags: false
        - task: PythonScript@0
          displayName: 'Tag Build'
          condition: or(eq(variables['Build.Reason'], 'PullRequest'), eq(variables['Build.Reason'], 'IndividualCI'))
          inputs:
            scriptSource: 'filePath'
            scriptPath: 'src/aether/platform/backendV2/scripts/buildtag/addbuildtags.py'
            arguments: --user '' --token $(System.AccessToken) --mode $(Build.Reason)
        - task: UseDotNet@2
          displayName: 'Use .NET sdk from global.json'
          inputs:
            packageType: sdk
            useGlobalJson: true
        - task: UseDotNet@2
          displayName: 'Use .NET sdk 6.0'
          inputs:
            packageType: sdk
            version: 6.0.x
        - task: NuGetAuthenticate@1
        - task: DotNetCoreCLI@2
          displayName: 'dotnet restore'
          inputs:
            command: 'restore'
            projects: 'src/aether/platform/AcisExtension/AcisExtension.sln'
            selectOrConfig: config
            nugetConfigPath: src/aether/NuGet.Config
            verbosityRestore: Minimal
        - task: DotNetCoreCLI@2
          displayName: dotnet build msftkubeconfig
          inputs:
            projects: 'src/aether/platform/msftkubeConfig/msftkubeconfig.proj'
            arguments: --configuration $(buildConfiguration)
            workingDirectory: src/aether
        - task: DotNetCoreCLI@2
          displayName: dotnet build DiagnosticService
          inputs:
            projects: 'src/aether/platform/backendV2/BlueBox/DiagnosticService/DiagnosticService.csproj'
            arguments: --configuration $(buildConfiguration)
            workingDirectory: src/aether
        - task: onebranch.pipeline.signing@1 # https://aka.ms/obpipelines/signing
          displayName: 'Sign binaries/files'
          inputs:
            command: 'sign'
            signing_environment: 'azure-ado'
            signing_profile: 'internal_azure_service'
            files_to_sign: '**/*.exe;**/*.dll;**/*.ps1;**/*.psm1;**/*.js'
            search_root: 'target/distrib/$(buildConfiguration)/AnyCPU/app/aether/bluebox'

        # - task: PublishPipelineArtifact@1
        #   displayName: 'Publish Artifact: Deploy'
        #   inputs:
        #     targetPath: target/distrib/$(buildConfiguration)/AnyCPU/app/aether/bluebox
        #     artifact: aetherDrop

    - stage: Create_Drop
      displayName: Create Drop
      dependsOn: Build_and_Test
      jobs:
      - job: Job_3
        displayName: Create and Push drop
        timeoutInMinutes: 120
        pool:
          name: pipeline-1ES-ubuntu
          type: linux
          isCustom: true
        variables:
          - name: ob_artifactBaseName
            value: drop
        steps:
        - checkout: none
        - task: DownloadPipelineArtifact@2
          displayName: 'Download Pipeline Artifact'
          inputs:
            artifactName: pipelineGAPackage
            patterns: '**/*.Package'
            targetPath: $(Pipeline.Workspace)
        - task: DownloadPipelineArtifact@2
          displayName: 'Download msftkube Artifact'
          inputs:
            artifactName: aetherDrop
            targetPath: $(System.ArtifactsDirectory)
        - task: UsePythonVersion@0
          displayName: 'Use Python 3.9'
          inputs:
            versionSpec: 3.9
        - task: UseDotNet@2
          displayName: 'Use .NET sdk from global.json'
          inputs:
            packageType: sdk
            version: 6.0.x
        - task: PipAuthenticate@1
          displayName: Use Azure Artifacts feed
          inputs:
            artifactFeeds: 'Vienna'
        - task: NuGetAuthenticate@1
        # have to copy GA packages before creating drop so cvrp is created properly for airgap regions
        - task: PythonScript@0
          displayName: 'Copy Geneva Action Packages'
          inputs:
            scriptSource: 'inline'
            script: |
              import shutil
              import os
              import glob

              if __name__ == '__main__':
                  for source_path in glob.glob('$(Pipeline.Workspace)/**/*.Package', recursive=True):
                      ga_extension_path_template = '$(System.ArtifactsDirectory)/msftkubeconfig/ev2/%s/' + 'pipeline-geneva-action/'
                      env_region_mapping = {
                          "Test": "int",
                          "PublicBeta": "eastus2euap",
                          "Public": "eastus2",
                          "Fairfax": "usgovvirginia",
                          "Mooncake": "chinaeast2",
                          "USSec": "ussecwest"
                      }
                      print('$(Pipeline.Workspace)')
                      for env, region in env_region_mapping.items():
                          if source_path.find(env) >= 0:
                              destination_path = ga_extension_path_template % region
                              with os.scandir('$(Pipeline.Workspace)') as dirs:
                                  for entry in dirs:
                                      print(entry.name)
                              print(f'Geneva Action package file: {source_path}')
                              print(f'Destination path: {destination_path}')
                              if not os.path.exists(destination_path):
                                  os.makedirs(destination_path, exist_ok=True)
                              print(shutil.disk_usage(destination_path))
                              shutil.copy(source_path, destination_path)
                              print(f'{env}: Update rollout files succeeded.\n')
        - task: AzureCLI@2
          displayName: 'Create Drop'
          inputs:
            azureSubscription: 'INFRA Viennadroptest connection'
            scriptType: bash
            scriptLocation: inlineScript
            inlineScript: |
              # Set environment variable to workaround bug in Azure CLI: https://github.com/Azure/azure-cli/issues/31419
              export AZURE_CORE_USE_MSAL_HTTP_CACHE=false

              az acr login -n viennadroptest
              bash $(System.ArtifactsDirectory)/msftkubeconfig/scripts/task.sh -v finish_drop -a diagnosticservice -e int eastus2euap eastus2 chinaeast2 usgovvirginia ussecwest usnatwest
        - task: PublishPipelineArtifact@1
          displayName: 'Publish Artifact: drop'
          condition: ne(variables['Build.Reason'], 'PullRequest')
          inputs:
            targetPath: '$(System.ArtifactsDirectory)/msftkubeconfig/'
            artifact: drop

