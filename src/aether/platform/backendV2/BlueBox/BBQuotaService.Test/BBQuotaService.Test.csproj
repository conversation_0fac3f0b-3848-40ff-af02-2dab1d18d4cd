﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <AssemblyName>BBQuotaService.Test</AssemblyName>
    <IsPackable>false</IsPackable>
  </PropertyGroup>
  <Import Project="..\..\..\QTest.props" />
  <ItemGroup>
    <PackageReference Include="NUnit" />
    <PackageReference Include="NUnit3TestAdapter" />
    <PackageReference Include="Moq" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" />
    <PackageReference Include="FluentAssertions" />
    <PackageReference Include="coverlet.collector" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\AetherQuota\AetherQuota.Test\AetherQuota.Test.csproj" />
    <ProjectReference Include="..\BBQuotaService\BBQuotaService.csproj" />
  </ItemGroup>
</Project>
