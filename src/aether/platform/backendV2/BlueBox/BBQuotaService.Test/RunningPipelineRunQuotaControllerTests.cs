﻿// <copyright file="RunningPipelineRunQuotaControllerTests.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Microsoft.Aether.BackendCommon;
using Microsoft.Aether.BBQuotaService;
using Microsoft.Aether.DataContracts;
using Microsoft.Aether.DataContracts.Entity;
using Microsoft.Aether.QuotaService;
using Microsoft.Aether.QuotaService.Shared;
using Microsoft.Aether.QuotaService.Test;
using Microsoft.RelInfra.Common.Configuration;
using Microsoft.RelInfra.Instrumentation;
using Microsoft.RelInfra.Storage;
using Microsoft.RelInfra.Storage.Exceptions;
using Moq;
using NUnit.Framework;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace BBQuotaService.Test
{
    public class RunningPipelineRunQuotaControllerTests
    {
        private readonly MockRepository _repository;
        private readonly CounterManager _fakeCounterManager;
        private readonly QuotaServiceConfig _fakeQuotaServiceConfig;
        private readonly Mock<IWorkspaceResourcesCache> _fakeIWorkspaceResourcesCache;
        private readonly Mock<IQueryableDbStorage> _fakeLimitContainer;
        private readonly Mock<IQueryableDbStorage> _fakeUsageContainer;

        private readonly string _subscriptionId;
        private readonly string _resourceGroupName;
        private readonly string _workspaceName;
        private readonly string _workspaceId;
        private readonly string _experimentId;

        private RunningPipelineRunQuotaController _fakeRunningPipelineRunQuotaController;
        private RunningPipelineRunQuotaManager _fakeRunningPipelineRunQuotaManager;

        public RunningPipelineRunQuotaControllerTests()
        {
            _subscriptionId = "testsub";
            _resourceGroupName = "testrg";
            _workspaceName = "testwsname";
            _workspaceId = "testwsid";
            _experimentId = "testexpid";
            _fakeQuotaServiceConfig = new QuotaServiceConfig(QuotaServiceConfigMock.GetConfiguration(), secretProvider: null);
            _repository = new MockRepository(MockBehavior.Loose) { DefaultValue = DefaultValue.Mock, CallBase = true };
            _fakeCounterManager = _repository.Create<CounterManager>(string.Empty, _repository.Create<ICounterFactory>().Object).Object;
            _fakeIWorkspaceResourcesCache = _repository.Create<IWorkspaceResourcesCache>();
            _fakeLimitContainer = _repository.Create<IQueryableDbStorage>();
            _fakeUsageContainer = _repository.Create<IQueryableDbStorage>();
            // Setup
            QuotaServiceTestHelper.InitCounter(_repository, _fakeCounterManager);
            _fakeIWorkspaceResourcesCache.Setup(
                  workspaceResourcesCache => workspaceResourcesCache.GetWorkspaceIdAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CreatedBy>()))
                .Returns(Task.FromResult(_workspaceId));
        }

        [SetUp]
        public void Init()
        {
            QuotaServiceTestHelper.SetupMockStorage(_fakeLimitContainer, new Dictionary<string, Storable<RunningPipelineRunQuotaLimitEntity>>());
            QuotaServiceTestHelper.SetupMockStorage(_fakeUsageContainer, new Dictionary<string, Storable<RunningPipelineRunQuotaUsageEntity>>());
            _fakeRunningPipelineRunQuotaManager = new RunningPipelineRunQuotaManager(
                _fakeQuotaServiceConfig,
                _fakeLimitContainer.Object,
                _fakeUsageContainer.Object);
            _fakeRunningPipelineRunQuotaController = new RunningPipelineRunQuotaController(
                _fakeCounterManager,
                _fakeRunningPipelineRunQuotaManager,
                _fakeIWorkspaceResourcesCache.Object);
        }

        [Test]
        public async Task TestAcquireQuota_Normal_Return()
        {
            var quotaResponse = await _fakeRunningPipelineRunQuotaController.AcquireQuotaAsync(
                _subscriptionId,
                _resourceGroupName,
                _workspaceName,
                _experimentId);
            Assert.IsTrue(quotaResponse.IsQuotaAvailable);
        }

        [Test]
        public async Task TestReleaseQuota_Normal_Return()
        {
            // Acquire quota
            var quotaResponse = await _fakeRunningPipelineRunQuotaController.AcquireQuotaAsync(
                _subscriptionId,
                _resourceGroupName,
                _workspaceName,
                _experimentId);
            Assert.IsTrue(quotaResponse.IsQuotaAvailable);

            // Release quota
            var released = await _fakeRunningPipelineRunQuotaController.ReleaseQuotaAsync(
                _subscriptionId,
                _resourceGroupName,
                _workspaceName,
                _experimentId);
            Assert.IsTrue(released);

            // Release quota again
            released = await _fakeRunningPipelineRunQuotaController.ReleaseQuotaAsync(
                _subscriptionId,
                _resourceGroupName,
                _workspaceName,
                _experimentId);
            Assert.IsFalse(released);
        }

        [Test]
        public async Task TestGetQuota_Normal_Return()
        {
            var quotaResponse = await _fakeRunningPipelineRunQuotaController.GetQuotaAsync(
                _subscriptionId,
                _resourceGroupName,
                _workspaceName);
            Assert.IsTrue(quotaResponse.IsQuotaAvailable);
            Assert.AreEqual(0, quotaResponse.CurrentUsageCount);
            Assert.AreEqual(_fakeQuotaServiceConfig.RunningPipelineRunDefaultQuotaLimit, quotaResponse.MaxLimit);
        }

        [Test]
        public async Task TestUpdateQuotaLimit_Normal_Return()
        {
            var quotaLimit = await _fakeRunningPipelineRunQuotaController.UpdateQuotaLimitAsync(
                _subscriptionId,
                _resourceGroupName,
                _workspaceName,
                new QuotaLimitRequest { MaxLimit = 1 });
            Assert.AreEqual(1, quotaLimit.MaxLimit);
        }

        [Test]
        public async Task TestDeleteQuotaLimit_Normal_Return()
        {
            // Set quota limit
            var quotaLimit = await _fakeRunningPipelineRunQuotaController.UpdateQuotaLimitAsync(
                _subscriptionId,
                _resourceGroupName,
                _workspaceName,
                new QuotaLimitRequest { MaxLimit = 1 });
            Assert.AreEqual(1, quotaLimit.MaxLimit);

            // Delete quota limit
            var deleted = await _fakeRunningPipelineRunQuotaController.DeleteQuotaLimitAsync(
                _subscriptionId,
                _resourceGroupName,
                _workspaceName,
                _workspaceId);
            Assert.IsTrue(deleted);

            // Delete quota limit again
            deleted = await _fakeRunningPipelineRunQuotaController.DeleteQuotaLimitAsync(
                _subscriptionId,
                _resourceGroupName,
                _workspaceName,
                _workspaceId);
            Assert.IsFalse(deleted);

            // If workspaceId is null
            deleted = await _fakeRunningPipelineRunQuotaController.DeleteQuotaLimitAsync(
                _subscriptionId,
                _resourceGroupName,
                _workspaceName);
            Assert.IsFalse(deleted);
        }

        [Test]
        public async Task TestDeleteUsages_Normal_Return()
        {
            // Acquire quota
            var quotaResponse = await _fakeRunningPipelineRunQuotaController.AcquireQuotaAsync(
                _subscriptionId,
                _resourceGroupName,
                _workspaceName,
                _experimentId);
            Assert.IsTrue(quotaResponse.IsQuotaAvailable);

            // Delete usages
            var deleted = await _fakeRunningPipelineRunQuotaController.DeleteUsagesAsync(
                _subscriptionId,
                _resourceGroupName,
                _workspaceName,
                _workspaceId);
            Assert.IsTrue(deleted);

            // Acquire quota
            quotaResponse = await _fakeRunningPipelineRunQuotaController.AcquireQuotaAsync(
                _subscriptionId,
                _resourceGroupName,
                _workspaceName,
                _experimentId);
            Assert.IsTrue(quotaResponse.IsQuotaAvailable);

            // Delete usages but workspaceId is null
            deleted = await _fakeRunningPipelineRunQuotaController.DeleteUsagesAsync(
                _subscriptionId,
                _resourceGroupName,
                _workspaceName).ConfigureAwait(false);
            Assert.IsFalse(deleted);

            // Change response to false which indicates the usage entity is not found
            _fakeUsageContainer.Setup(c => c.TryDeleteAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .Returns(Task.FromResult(false));

            deleted = await _fakeRunningPipelineRunQuotaController.DeleteUsagesAsync(
                _subscriptionId,
                _resourceGroupName,
                _workspaceName,
                _workspaceId).ConfigureAwait(false);
            Assert.IsFalse(deleted);
        }

        [Test]
        public void TestAcquireQuota_ThrowException()
        {
            QuotaServiceTestHelper.SetupMockStorageThrowException<RunningPipelineRunQuotaLimitEntity>(_fakeLimitContainer);
            Assert.ThrowsAsync<RelInfraStorageException>(
                async () => await _fakeRunningPipelineRunQuotaController.AcquireQuotaAsync(
                    _subscriptionId,
                    _resourceGroupName,
                    _workspaceName,
                    _experimentId));
        }

        [Test]
        public void TestReleaseQuota_ThrowException()
        {
            QuotaServiceTestHelper.SetupMockStorageThrowException<RunningPipelineRunQuotaUsageEntity>(_fakeUsageContainer);
            Assert.ThrowsAsync<RelInfraStorageException>(
                async () => await _fakeRunningPipelineRunQuotaController.ReleaseQuotaAsync(
                    _subscriptionId,
                    _resourceGroupName,
                    _workspaceName,
                    _experimentId));
        }

        [Test]
        public void TestGetQuota_ThrowException()
        {
            QuotaServiceTestHelper.SetupMockStorageThrowException<RunningPipelineRunQuotaLimitEntity>(_fakeLimitContainer);
            Assert.ThrowsAsync<RelInfraStorageException>(
                async () => await _fakeRunningPipelineRunQuotaController.GetQuotaAsync(
                    _subscriptionId,
                    _resourceGroupName,
                    _workspaceName));
        }

        [Test]
        public void TestUpdateQuotaLimit_ThrowException()
        {
            QuotaServiceTestHelper.SetupMockStorageThrowException<RunningPipelineRunQuotaLimitEntity>(_fakeLimitContainer);
            Assert.ThrowsAsync<RelInfraStorageException>(
                async () => await _fakeRunningPipelineRunQuotaController.UpdateQuotaLimitAsync(
                    _subscriptionId,
                    _resourceGroupName,
                    _workspaceName,
                    new QuotaLimitRequest { MaxLimit = 1 }));
        }

        [Test]
        public void TestDeleteQuotaLimit_ThrowException()
        {
            QuotaServiceTestHelper.SetupMockStorageThrowException<RunningPipelineRunQuotaLimitEntity>(_fakeLimitContainer);
            Assert.ThrowsAsync<RelInfraStorageException>(
                async () => await _fakeRunningPipelineRunQuotaController.DeleteQuotaLimitAsync(
                    _subscriptionId,
                    _resourceGroupName,
                    _workspaceName,
                    _workspaceId));
        }

        [Test]
        public void TestDeleteUsages_ThrowException()
        {
            QuotaServiceTestHelper.SetupMockStorageThrowException<RunningPipelineRunQuotaUsageEntity>(_fakeUsageContainer);
            Assert.ThrowsAsync<RelInfraStorageException>(
                async () => await _fakeRunningPipelineRunQuotaController.DeleteUsagesAsync(
                    _subscriptionId,
                    _resourceGroupName,
                    _workspaceName,
                    _workspaceId));
        }
    }
}
