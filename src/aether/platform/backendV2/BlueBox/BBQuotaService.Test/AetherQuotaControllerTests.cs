﻿// <copyright file="AetherQuotaControllerTests.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using FluentAssertions;
using Microsoft.Aether.BBQuotaService;
using Microsoft.Aether.QuotaService;
using Microsoft.Aether.QuotaService.Shared;
using Microsoft.AspNetCore.Mvc;
using Microsoft.RelInfra.Common;
using Microsoft.RelInfra.Common.Configuration;
using Microsoft.RelInfra.Common.Secrets;
using Microsoft.RelInfra.Instrumentation;
using Microsoft.RelInfra.Storage;
using Moq;
using NUnit.Framework;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Net;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace BBQuotaService.Test
{
    public class AetherQuotaControllerTests
    {
        private readonly MockRepository _repository;

        private AetherQuotaController _dummyAetherQuotaController;
        private readonly CounterManager _dummyCounterManager;
        private ConcurrentDictionary<string, ICounter> _dummyCounterCache;
        private Dictionary<AetherQuotaType, ResourceManager> _dummyResourceManagers;
        private AetherQuotaManager _fakeAetherQuotaManager;
        private QuotaServiceConfig _dummyQuotaServiceConfig;
        private IRelInfraConfiguration _fakeIRelInfraConfiguration;
        private SecretProvider _fakeSecretProvider;
        private IQuotaRoiClient _fakeIQuotaRoiClient;
        private ISecretStoreHelper _fakeISecretStoreHelper;
        private IKeyVaultClient _fakeIKeyVaultClient;
        private QuotaReservationResponse _dummyQuotaReservationResponse;
        private IDictionary<string, string> _dummyConfig;
        private ResourceUsageEntity _dummyResourceUsageEntity;

        private readonly string _experimentId;
        private readonly string _userName;
        private readonly string _teamId;
        private ResourceManager _dummyResourceManager;

        public AetherQuotaControllerTests()
        {
            _repository = new MockRepository(MockBehavior.Loose) { DefaultValue = DefaultValue.Mock, CallBase = true };
            _dummyCounterManager = new CounterManager("testSvc", _repository.Create<ICounterFactory>().Object);

            _experimentId = Guid.NewGuid().ToString();
            _userName = "testUserName";
            _teamId = "testTeamId";
        }

        [SetUp]
        public void Init()
        {
            ICounter rateCounter = Mock.Of<ICounter>(m =>
             m.CounterType == CounterType.Rate);
            ICounter latencyCounter = Mock.Of<ICounter>(m =>
             m.CounterType == CounterType.Number_Percentiles);

            _dummyCounterCache = new ConcurrentDictionary<string, ICounter>();
            _dummyCounterCache["TryReserveQuotaRate"] = rateCounter;
            _dummyCounterCache["TryReserveQuotaLatency"] = latencyCounter;
            _dummyCounterCache["TryReserveQuotaSuccessRate"] = rateCounter;
            _dummyCounterCache["TryReserveQuotaFailureRate"] = rateCounter;

            _dummyCounterCache["TryReserveRunningJobsQuotaRate"] = rateCounter;
            _dummyCounterCache["TryReserveRunningJobsQuotaLatency"] = latencyCounter;
            _dummyCounterCache["TryReserveRunningJobsQuotaSuccessRate"] = rateCounter;
            _dummyCounterCache["TryReserveRunningJobsQuotaFailureRate"] = rateCounter;

            _dummyCounterCache["TryIncrementRunningJobsQuotaRate"] = rateCounter;
            _dummyCounterCache["TryIncrementRunningJobsQuotaLatency"] = latencyCounter;
            _dummyCounterCache["TryIncrementRunningJobsQuotaSuccessRate"] = rateCounter;
            _dummyCounterCache["TryIncrementRunningJobsQuotaFailureRate"] = rateCounter;

            _dummyCounterCache["TryReleaseRunningJobsQuotaRate"] = rateCounter;
            _dummyCounterCache["TryReleaseRunningJobsQuotaLatency"] = latencyCounter;
            _dummyCounterCache["TryReleaseRunningJobsQuotaSuccessRate"] = rateCounter;
            _dummyCounterCache["TryReleaseRunningJobsQuotaFailureRate"] = rateCounter;

            typeof(CounterManager)
                .GetField("_counterCache", BindingFlags.Instance | BindingFlags.NonPublic)
                .SetValue(_dummyCounterManager, _dummyCounterCache);

            _dummyConfig = new Dictionary<string, string>();
            _dummyConfig["QuotaSettings.SubmittedNodesLimit"] = "2";
            _dummyConfig["QuotaSettings.NodesExemption"] = "none";
            _dummyConfig["QuotaSettings.SubmissionExpirationInterval"] = "0:0:6.8954321";
            _dummyConfig["QuotaSettings.RunningNodesLimit"] = "3";
            _dummyConfig["QuotaSettings.RunningJobExpirationInterval"] = "0:0:6.8";
            _dummyConfig["QuotaSettings.EmailsLimit"] = "100";
            _dummyConfig["QuotaSettings.EmailsExemption"] = "nothing";
            _dummyConfig["QuotaSettings.PromotionsLimit"] = "6";
            _dummyConfig["QuotaSettings.PromotionsExemption"] = "hello";
            _dummyConfig["QuotaSettings.UploadsLimit"] = "2";
            _dummyConfig["QuotaSettings.UploadsExemption"] = "world";

            _fakeIRelInfraConfiguration = new DynamicConfigurationFlexMock(_dummyConfig);

            _fakeIKeyVaultClient = _repository.Create<IKeyVaultClient>().Object;
            _fakeISecretStoreHelper = _repository.Create<ISecretStoreHelper>().Object;
            _fakeSecretProvider = _repository.Create<SecretProvider>(_fakeISecretStoreHelper, _fakeIKeyVaultClient).Object;
            _dummyQuotaServiceConfig = new QuotaServiceConfig(_fakeIRelInfraConfiguration, _fakeSecretProvider);
            _fakeIQuotaRoiClient = _repository.Create<IQuotaRoiClient>().Object;

            _fakeAetherQuotaManager = _repository.Create<AetherQuotaManager>(_fakeIQuotaRoiClient).Object;

            _dummyResourceManagers = new Dictionary<AetherQuotaType, ResourceManager>();
            _dummyResourceManager = new ResourceManager(
                _repository.Create<IAzureBlobStorage>().Object,
                string.Empty,
                string.Empty,
                default(TimeSpan),
                2,
                _repository.Create<IQuotaRoiClient>().Object);
            _dummyResourceManagers[AetherQuotaType.Node] = _dummyResourceManager;
            _dummyResourceManagers[AetherQuotaType.RunningNode] = _dummyResourceManager;
            _dummyResourceManagers[AetherQuotaType.Email] = _dummyResourceManager;
            _dummyResourceManagers[AetherQuotaType.Promotion] = _dummyResourceManager;
            _dummyResourceManagers[AetherQuotaType.Upload] = _dummyResourceManager;

            _dummyAetherQuotaController = new AetherQuotaController(
                _dummyCounterManager,
                _fakeAetherQuotaManager,
                _dummyResourceManagers,
                _dummyQuotaServiceConfig);

            _dummyQuotaReservationResponse = new QuotaReservationResponse();
            Mock.Get(_fakeAetherQuotaManager)
                .Setup(
                   service => service.ReserveTeamUserQuotaAsync(
                       It.IsAny<string>(),
                       It.IsAny<string>(),
                       It.IsAny<int>(),
                       It.IsAny<ResourceManager>(),
                       It.IsAny<int>(),
                       It.IsAny<HashSet<string>>(),
                       It.IsAny<string>(),
                       It.IsAny<TimeSpan>()))
                .Returns(Task.FromResult(_dummyQuotaReservationResponse));

            _dummyResourceUsageEntity = new ResourceUsageEntity();
            Mock.Get(_fakeAetherQuotaManager)
                .Setup(
                   service => service.GetUserResourceUsageAsync(
                       It.IsAny<string>(),
                       It.IsAny<string>(),
                       It.IsAny<ResourceManager>()))
                .Returns(Task.FromResult(_dummyResourceUsageEntity));

            Mock.Get(_fakeAetherQuotaManager)
                .Setup(
                   service => service.ReserveUserQuotaAsync(
                       It.IsAny<string>(),
                       It.IsAny<string>(),
                       It.IsAny<int>(),
                       It.IsAny<ResourceManager>(),
                       It.IsAny<int>(),
                       It.IsAny<HashSet<string>>(),
                       It.IsAny<TimeSpan>()))
                .Returns(Task.FromResult(_dummyQuotaReservationResponse));

            Mock.Get(_fakeAetherQuotaManager)
                .Setup(
                   service => service.IncrementUserQuotaAsync(
                       It.IsAny<string>(),
                       It.IsAny<string>(),
                       It.IsAny<int>(),
                       It.IsAny<ResourceManager>(),
                       It.IsAny<int>(),
                       It.IsAny<HashSet<string>>(),
                       It.IsAny<TimeSpan>()))
                .Returns(Task.FromResult(_dummyQuotaReservationResponse));

            Mock.Get(_fakeAetherQuotaManager)
                .Setup(
                   service => service.ReleaseUserQuotaAsync(
                       It.IsAny<string>(),
                       It.IsAny<string>(),
                       It.IsAny<ResourceManager>(),
                       It.IsAny<HashSet<string>>(),
                       It.IsAny<TimeSpan>()))
                .Returns(Task.FromResult(_dummyQuotaReservationResponse));
        }

        [Test]
        public async Task TestReserveJobsQuotaAsync_Normal_Return()
        {
            var actualRet = await _dummyAetherQuotaController.ReserveJobsQuotaAsync(
                _experimentId,
                _userName,
                _teamId,
                2).ConfigureAwait(false);
            Assert.IsInstanceOf<ActionResult<QuotaReservationResponse>>(actualRet);
            Assert.AreEqual(_dummyQuotaReservationResponse, actualRet.Value);
        }

        [Test]
        public void TestReserveJobsQuotaAsync_ReserveTeamUserQuotaAsyncFailed_Return()
        {
            var exp = new Exception("test exp");
            Mock.Get(_fakeAetherQuotaManager)
                .Setup(
                   service => service.ReserveTeamUserQuotaAsync(
                       It.IsAny<string>(),
                       It.IsAny<string>(),
                       It.IsAny<int>(),
                       It.IsAny<ResourceManager>(),
                       It.IsAny<int>(),
                       It.IsAny<HashSet<string>>(),
                       It.IsAny<string>(),
                       It.IsAny<TimeSpan>()))
                .Throws(exp);

            Assert.DoesNotThrowAsync(async () => await _dummyAetherQuotaController.ReserveJobsQuotaAsync(
                _experimentId,
                _userName,
                _teamId,
                2).ConfigureAwait(false));
        }

        [Test]
        public async Task TestGetExperimentRunningNodeUsage_Normal_Return()
        {
            var actualRet = await _dummyAetherQuotaController.GetExperimentRunningNodeUsage(
                _experimentId,
                _userName).ConfigureAwait(false);

            Mock.Get(_fakeAetherQuotaManager)
                .Verify(
                   service => service.GetUserResourceUsageAsync(
                       _userName,
                       _experimentId,
                       It.IsAny<ResourceManager>()), Times.Once);
        }

        [Test]
        public async Task TestReserveRunningJobsQuotaAsync_Normal_Return()
        {
            var actualRet = await _dummyAetherQuotaController.ReserveRunningJobsQuotaAsync(
                _experimentId,
                _teamId,
                _userName,
                3).ConfigureAwait(false);

            Assert.IsInstanceOf<ActionResult<QuotaReservationResponse>>(actualRet);
            Assert.AreEqual(_dummyQuotaReservationResponse, actualRet.Value);
        }

        [Test]
        public void TestReserveRunningJobsQuotaAsync_ReserveUserQuotaAsyncFailed_Return()
        {
            var exp = new Exception("test exp");
            Mock.Get(_fakeAetherQuotaManager)
                .Setup(
                   service => service.ReserveUserQuotaAsync(
                       It.IsAny<string>(),
                       It.IsAny<string>(),
                       It.IsAny<int>(),
                       It.IsAny<ResourceManager>(),
                       It.IsAny<int>(),
                       It.IsAny<HashSet<string>>(),
                       It.IsAny<TimeSpan>()))
                .Throws(exp);

            Assert.DoesNotThrowAsync(async () => await _dummyAetherQuotaController.ReserveRunningJobsQuotaAsync(
                _experimentId,
                _teamId,
                _userName,
                3).ConfigureAwait(false));
        }

        [Test]
        public async Task TestIncrementRunningJobsQuotaAsync_Normal_Return()
        {
            var actualRet = await _dummyAetherQuotaController.IncrementRunningJobsQuotaAsync(
                _experimentId,
                _teamId,
                _userName,
                3).ConfigureAwait(false);

            Assert.IsInstanceOf<ActionResult<QuotaReservationResponse>>(actualRet);
            Assert.AreEqual(_dummyQuotaReservationResponse, actualRet.Value);
        }

        [Test]
        public void TestIncrementUserQuotaAsync_ReserveUserQuotaAsyncFailed_Return()
        {
            var exp = new Exception("test exp");
            Mock.Get(_fakeAetherQuotaManager)
                .Setup(
                   service => service.IncrementUserQuotaAsync(
                       It.IsAny<string>(),
                       It.IsAny<string>(),
                       It.IsAny<int>(),
                       It.IsAny<ResourceManager>(),
                       It.IsAny<int>(),
                       It.IsAny<HashSet<string>>(),
                       It.IsAny<TimeSpan>()))
                .Throws(exp);

            Assert.DoesNotThrowAsync(async () => await _dummyAetherQuotaController.ReserveRunningJobsQuotaAsync(
                _experimentId,
                _teamId,
                _userName,
                3).ConfigureAwait(false));
        }

        [Test]
        public async Task TestReleaseRunningJobsQuotaAsync_Normal_Return()
        {
            var actualRet = await _dummyAetherQuotaController.ReleaseRunningJobsQuotaAsync(
                _experimentId,
                _teamId,
                _userName).ConfigureAwait(false);

            Assert.IsInstanceOf<IActionResult>(actualRet);
            actualRet.Should().BeEquivalentTo(new NoContentResult());
        }

        [Test]
        public void TestReleaseRunningJobsQuotaAsync_ReleaseUserQuotaAsyncFailed_Return()
        {
            var exp = new Exception("test exp");
            Mock.Get(_fakeAetherQuotaManager)
                .Setup(
                   service => service.ReleaseUserQuotaAsync(
                       It.IsAny<string>(),
                       It.IsAny<string>(),
                       It.IsAny<ResourceManager>(),
                       It.IsAny<HashSet<string>>(),
                       It.IsAny<TimeSpan>()))
                .Throws(exp);

            Assert.DoesNotThrowAsync(async () => await _dummyAetherQuotaController.ReleaseRunningJobsQuotaAsync(
                _experimentId,
                _teamId,
                _userName).ConfigureAwait(false));
        }

        [Test]
        public async Task TestReserveEmailsQuotaAsync_Normal_Return()
        {
            var actualRet = await _dummyAetherQuotaController.ReserveEmailsQuotaAsync(
                _experimentId,
                _teamId,
                _userName).ConfigureAwait(false);

            Assert.IsInstanceOf<ActionResult<QuotaReservationResponse>>(actualRet);
            Assert.AreEqual(_dummyQuotaReservationResponse, actualRet.Value);
        }

        [Test]
        public void TestReserveEmailsQuotaAsync_ReserveTeamUserQuotaAsyncFailed_Return()
        {
            var exp = new Exception("test exp");
            Mock.Get(_fakeAetherQuotaManager)
                .Setup(
                   service => service.ReserveTeamUserQuotaAsync(
                       It.IsAny<string>(),
                       It.IsAny<string>(),
                       It.IsAny<int>(),
                       It.IsAny<ResourceManager>(),
                       It.IsAny<int>(),
                       It.IsAny<HashSet<string>>(),
                       It.IsAny<string>(),
                       It.IsAny<TimeSpan>()))
                .Throws(exp);

            Assert.DoesNotThrowAsync(async () => await _dummyAetherQuotaController.ReserveEmailsQuotaAsync(
                _experimentId,
                _teamId,
                _userName).ConfigureAwait(false));
        }

        [Test]
        public async Task TestReservePromotionQuotaAsync_Normal_Return()
        {
            var actualRet = await _dummyAetherQuotaController.ReservePromotionQuotaAsync(
                _experimentId,
                _teamId,
                _userName).ConfigureAwait(false);

            Assert.IsInstanceOf<ActionResult<QuotaReservationResponse>>(actualRet);
            Assert.AreEqual(_dummyQuotaReservationResponse, actualRet.Value);
        }

        [Test]
        public void TestReservePromotionQuotaAsync_ReserveTeamUserQuotaAsyncFailed_Return()
        {
            var exp = new Exception("test exp");
            Mock.Get(_fakeAetherQuotaManager)
                .Setup(
                   service => service.ReserveTeamUserQuotaAsync(
                       It.IsAny<string>(),
                       It.IsAny<string>(),
                       It.IsAny<int>(),
                       It.IsAny<ResourceManager>(),
                       It.IsAny<int>(),
                       It.IsAny<HashSet<string>>(),
                       It.IsAny<string>(),
                       It.IsAny<TimeSpan>()))
                .Throws(exp);

            Assert.DoesNotThrowAsync(async () => await _dummyAetherQuotaController.ReservePromotionQuotaAsync(
                _experimentId,
                _teamId,
                _userName).ConfigureAwait(false));
        }

        [Test]
        public async Task TestReserveUploadQuotaAsync_Normal_Return()
        {
            var actualRet = await _dummyAetherQuotaController.ReservePromotionQuotaAsync(
                _experimentId,
                _teamId,
                _userName).ConfigureAwait(false);

            Assert.IsInstanceOf<ActionResult<QuotaReservationResponse>>(actualRet);
            Assert.AreEqual(_dummyQuotaReservationResponse, actualRet.Value);
        }

        [Test]
        public void TestReserveUploadQuotaAsync_ReserveTeamUserQuotaAsyncFailed_Return()
        {
            var exp = new Exception("test exp");
            Mock.Get(_fakeAetherQuotaManager)
                .Setup(
                   service => service.ReserveTeamUserQuotaAsync(
                       It.IsAny<string>(),
                       It.IsAny<string>(),
                       It.IsAny<int>(),
                       It.IsAny<ResourceManager>(),
                       It.IsAny<int>(),
                       It.IsAny<HashSet<string>>(),
                       It.IsAny<string>(),
                       It.IsAny<TimeSpan>()))
                .Throws(exp);

            Assert.DoesNotThrowAsync(async () => await _dummyAetherQuotaController.ReservePromotionQuotaAsync(
                _experimentId,
                _teamId,
                _userName).ConfigureAwait(false));
        }
    }
}
