﻿// <copyright file="RunningPipelineRunSoftQuotaControllerTests.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Microsoft.Aether.BackendCommon;
using Microsoft.Aether.BBQuotaService;
using Microsoft.Aether.DataContracts;
using Microsoft.Aether.QuotaService;
using Microsoft.Aether.QuotaService.Shared;
using Microsoft.Aether.QuotaService.Test;
using Microsoft.MachineLearning.Common.ServiceBus;
using Microsoft.RelInfra.Instrumentation;
using Microsoft.RelInfra.Storage;
using Microsoft.RelInfra.Storage.Exceptions;
using Moq;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace BBQuotaService.Test
{
    public class RunningPipelineRunSoftQuotaControllerTests
    {
        private readonly MockRepository _repository;
        private readonly CounterManager _fakeCounterManager;
        private readonly QuotaServiceConfig _fakeQuotaServiceConfig;
        private readonly Mock<IWorkspaceResourcesCache> _fakeWorkspaceResourcesCache;
        private readonly Mock<IQueryableDbStorage> _fakeLimitContainer;
        private readonly Mock<IQueryableDbStorage> _fakeUsageContainer;
        private readonly Mock<IQueryableDbStorage> _fakeCachedContainer;

        private readonly string _subscriptionId;
        private readonly string _resourceGroupName;
        private readonly string _workspaceName;
        private readonly string _workspaceId;
        private readonly string _experimentId;

        private RunningPipelineRunSoftQuotaManager _fakeRunningPipelineRunSoftQuotaManager;
        private RunningPipelineRunSoftQuotaController _fakeRunningPipelineRunSoftQuotaController;
        private ICachedPipelineRunManager _fakeCachedPipelineRunManager;
        private IServiceBusTopicPublisher _fakeCheckCachedTopicPublisher;
        private IExperimentQueuePublisher _fakeExperimentQueuePublisher;

        public RunningPipelineRunSoftQuotaControllerTests()
        {
            _subscriptionId = "testsub";
            _resourceGroupName = "testrg";
            _workspaceName = "testwsname";
            _workspaceId = "testwsid";
            _experimentId = "testexpid";
            _fakeQuotaServiceConfig = new QuotaServiceConfig(QuotaServiceConfigMock.GetConfiguration(), secretProvider: null);
            _repository = new MockRepository(MockBehavior.Loose) { DefaultValue = DefaultValue.Mock, CallBase = true };
            _fakeCounterManager = _repository.Create<CounterManager>(string.Empty, _repository.Create<ICounterFactory>().Object).Object;
            _fakeLimitContainer = _repository.Create<IQueryableDbStorage>();
            _fakeUsageContainer = _repository.Create<IQueryableDbStorage>();
            _fakeCachedContainer = _repository.Create<IQueryableDbStorage>();
            _fakeWorkspaceResourcesCache = _repository.Create<IWorkspaceResourcesCache>();
            // Setup
            QuotaServiceTestHelper.InitCounter(_repository, _fakeCounterManager);
            _fakeWorkspaceResourcesCache.Setup(c => c.GetWorkspaceIdAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CreatedBy>()))
                .Returns(Task.FromResult(_workspaceId));
        }

        [SetUp]
        public void Init()
        {
            QuotaServiceTestHelper.SetupMockStorage(_fakeLimitContainer, new Dictionary<string, Storable<RunningPipelineRunQuotaLimitEntity>>());
            QuotaServiceTestHelper.SetupMockStorage(_fakeUsageContainer, new Dictionary<string, Storable<RunningPipelineRunQuotaUsageEntity>>());
            QuotaServiceTestHelper.SetupMockStorage(_fakeCachedContainer, new Dictionary<string, Storable<CachedPipelineRunEntity>>());

            _fakeExperimentQueuePublisher = _repository.Create<IExperimentQueuePublisher>().Object;
            _fakeCachedPipelineRunManager = new CachedPipelineRunManager(_fakeQuotaServiceConfig, _fakeCounterManager, _fakeCachedContainer.Object, _fakeExperimentQueuePublisher);
            _fakeCheckCachedTopicPublisher = _repository.Create<IServiceBusTopicPublisher>().Object;
            _fakeRunningPipelineRunSoftQuotaManager = new RunningPipelineRunSoftQuotaManager(
                config: _fakeQuotaServiceConfig,
                limitContainer: _fakeLimitContainer.Object,
                usageContainer: _fakeUsageContainer.Object,
                cachedPipelineRunManager: _fakeCachedPipelineRunManager,
                checkCachedTopicPublisher: _fakeCheckCachedTopicPublisher);
            _fakeRunningPipelineRunSoftQuotaController = new RunningPipelineRunSoftQuotaController(
                counterManager: _fakeCounterManager,
                runningPipelineRunSoftQuotaManager: _fakeRunningPipelineRunSoftQuotaManager,
                workspaceResourcesCache: _fakeWorkspaceResourcesCache.Object);
        }

        [Test]
        public async Task TestAcquireSoftQuotaAvailableAsync()
        {
            var quotaResponse = await _fakeRunningPipelineRunSoftQuotaController.AcquireSoftQuotaAsync(
                _subscriptionId,
                _resourceGroupName,
                _workspaceName,
                Guid.NewGuid().ToString());
            Assert.IsTrue(quotaResponse.IsQuotaAvailable);
        }

        [Test]
        public async Task TestAcquireSoftQuotaUnavailableAsync()
        {
            var quotaResponse = await _fakeRunningPipelineRunSoftQuotaController.GetSoftQuotaAsync(
                _subscriptionId,
                _resourceGroupName,
                _workspaceName);
            Assert.IsTrue(quotaResponse.IsQuotaAvailable);

            // Occupy all quotas.
            int availableQuota = quotaResponse.MaxLimit - quotaResponse.CurrentUsageCount;
            for (int i = 0; i < availableQuota; i++)
            {
                quotaResponse = await _fakeRunningPipelineRunSoftQuotaController.AcquireSoftQuotaAsync(
                    _subscriptionId,
                    _resourceGroupName,
                    _workspaceName,
                    Guid.NewGuid().ToString());
                Assert.IsTrue(quotaResponse.IsQuotaAvailable);
            }

            quotaResponse = await _fakeRunningPipelineRunSoftQuotaController.AcquireSoftQuotaAsync(
                _subscriptionId,
                _resourceGroupName,
                _workspaceName,
                Guid.NewGuid().ToString());
            // Quota is unavailable when reached maximum limit.
            Assert.IsFalse(quotaResponse.IsQuotaAvailable);
        }

        [Test]
        public async Task TestReleaseSoftQuotaSucceededAsync()
        {
            string experimentId = Guid.NewGuid().ToString();
            var quotaResponse = await _fakeRunningPipelineRunSoftQuotaController.AcquireSoftQuotaAsync(
                _subscriptionId,
                _resourceGroupName,
                _workspaceName,
                experimentId);
            Assert.IsTrue(quotaResponse.IsQuotaAvailable);

            var released = await _fakeRunningPipelineRunSoftQuotaController.ReleaseSoftQuotaAsync(
                _subscriptionId,
                _resourceGroupName,
                _workspaceName,
                experimentId);
            Assert.IsTrue(released);
        }

        [Test]
        public async Task TestReleaseSoftQuotaFailedAsync()
        {
            string experimentId = Guid.NewGuid().ToString();
            // Release a not exist pipeline run return false.
            var released = await _fakeRunningPipelineRunSoftQuotaController.ReleaseSoftQuotaAsync(
                _subscriptionId,
                _resourceGroupName,
                _workspaceName,
                experimentId);
            Assert.IsFalse(released);
        }

        [Test]
        public async Task TestGetSoftQuotaAsync()
        {
            var quotaResponse = await _fakeRunningPipelineRunSoftQuotaController.GetSoftQuotaAsync(
                _subscriptionId,
                _resourceGroupName,
                _workspaceName);
            Assert.IsTrue(quotaResponse.IsQuotaAvailable);
            Assert.AreEqual(0, quotaResponse.CurrentUsageCount);
            Assert.AreEqual(_fakeQuotaServiceConfig.RunningPipelineRunDefaultSoftQuotaLimit, quotaResponse.MaxLimit);
        }

        [Test]
        public async Task TestSetSoftQuotaLimitAsync()
        {
            var newSoftLimit = new QuotaLimitRequest { MaxLimit = 10 };
            var updatedSoftQuotaLimit = await _fakeRunningPipelineRunSoftQuotaController.UpdateSoftQuotaLimitAsync(
                _subscriptionId,
                _resourceGroupName,
                _workspaceName,
                newSoftLimit);
            Assert.AreEqual(newSoftLimit.MaxLimit, updatedSoftQuotaLimit.MaxSoftLimit);
        }

        [Test]
        public void TestAcquireSoftQuotaAsync_ThrowException()
        {
            QuotaServiceTestHelper.SetupMockStorageThrowException<RunningPipelineRunQuotaUsageEntity>(_fakeUsageContainer);
            Assert.ThrowsAsync<RelInfraStorageException>(
                async () => await _fakeRunningPipelineRunSoftQuotaController.AcquireSoftQuotaAsync(
                    _subscriptionId,
                    _resourceGroupName,
                    _workspaceName,
                    _experimentId));
        }

        [Test]
        public void TestReleaseSoftQuotaAsync_ThrowException()
        {
            QuotaServiceTestHelper.SetupMockStorageThrowException<RunningPipelineRunQuotaUsageEntity>(_fakeUsageContainer);
            Assert.ThrowsAsync<RelInfraStorageException>(
                async () => await _fakeRunningPipelineRunSoftQuotaController.ReleaseSoftQuotaAsync(
                    _subscriptionId,
                    _resourceGroupName,
                    _workspaceName,
                    _experimentId));
        }

        [Test]
        public void TestGetSoftQuotaAsync_ThrowException()
        {
            QuotaServiceTestHelper.SetupMockStorageThrowException<RunningPipelineRunQuotaUsageEntity>(_fakeUsageContainer);
            Assert.ThrowsAsync<RelInfraStorageException>(
                async () => await _fakeRunningPipelineRunSoftQuotaController.GetSoftQuotaAsync(
                    _subscriptionId,
                    _resourceGroupName,
                    _workspaceName));
        }

        [Test]
        public void TestSetSoftQuotaLimitAsync_ThrowException()
        {
            QuotaServiceTestHelper.SetupMockStorageThrowException<RunningPipelineRunQuotaLimitEntity>(_fakeLimitContainer);
            Assert.ThrowsAsync<RelInfraStorageException>(
                async () => await _fakeRunningPipelineRunSoftQuotaController.UpdateSoftQuotaLimitAsync(
                    _subscriptionId,
                    _resourceGroupName,
                    _workspaceName,
                    new QuotaLimitRequest { MaxLimit = 10 }));
        }
    }
}
