﻿// <copyright file="CachedPipelineRunControllerTests.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Microsoft.Aether.BackendCommon;
using Microsoft.Aether.BBQuotaService;
using Microsoft.Aether.DataContracts;
using Microsoft.Aether.DataContracts.Entity;
using Microsoft.Aether.QuotaService;
using Microsoft.Aether.QuotaService.Shared;
using Microsoft.Aether.QuotaService.Test;
using Microsoft.RelInfra.Common.Configuration;
using Microsoft.RelInfra.Instrumentation;
using Microsoft.RelInfra.Storage;
using Microsoft.RelInfra.Storage.Exceptions;
using Moq;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace BBQuotaService.Test
{
    public class CachedPipelineRunControllerTests
    {
        private readonly MockRepository _repository;
        private readonly CounterManager _fakeCounterManager;
        private readonly QuotaServiceConfig _fakeQuotaServiceConfig;
        private readonly Mock<IWorkspaceResourcesCache> _fakeIWorkspaceResourcesCache;
        private readonly Mock<IQueryableDbStorage> _fakeCachedContainer;
        private readonly Mock<IExperimentQueuePublisher> _fakeExperimentQueuePublisher;

        private readonly string _subscriptionId;
        private readonly string _resourceGroupName;
        private readonly string _workspaceName;
        private readonly string _workspaceId;
        private readonly string _experimentId;

        private CachedPipelineRunController _fakeCachedPipelineRunController;
        private CachedPipelineRunManager _fakeCachedPipelineRunManager;

        public CachedPipelineRunControllerTests()
        {
            _subscriptionId = "testsub";
            _resourceGroupName = "testrg";
            _workspaceName = "testwsname";
            _workspaceId = "testwsid";
            _experimentId = "testexpid";
            _repository = new MockRepository(MockBehavior.Loose) { DefaultValue = DefaultValue.Mock, CallBase = true };
            _fakeCounterManager = _repository.Create<CounterManager>(string.Empty, _repository.Create<ICounterFactory>().Object).Object;
            _fakeIWorkspaceResourcesCache = _repository.Create<IWorkspaceResourcesCache>();
            _fakeCachedContainer = _repository.Create<IQueryableDbStorage>();
            _fakeQuotaServiceConfig = new QuotaServiceConfig(QuotaServiceConfigMock.GetConfiguration(), secretProvider: null);
            // Setup
            QuotaServiceTestHelper.InitCounter(_repository, _fakeCounterManager);
            _fakeIWorkspaceResourcesCache.Setup(c => c.GetWorkspaceIdAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CreatedBy>()))
                .Returns(Task.FromResult(_workspaceId));
            _fakeExperimentQueuePublisher = _repository.Create<IExperimentQueuePublisher>();
            _fakeExperimentQueuePublisher.Setup(e => e.EnqueueExperimentAsync(It.IsAny<WorkspaceIdentity>(), It.IsAny<string>(), It.IsAny<string>()))
                .Returns(Task.FromResult(true));
        }

        [SetUp]
        public void Init()
        {
            QuotaServiceTestHelper.SetupMockStorage(_fakeCachedContainer, new Dictionary<string, Storable<CachedPipelineRunEntity>>());
            _fakeCachedContainer.Setup(c => c.TryDeleteAsync(It.IsAny<string>(), It.Is<string>(q => q.Equals("pipelineRunFailedToRemove", StringComparison.InvariantCultureIgnoreCase)), It.IsAny<CancellationToken>()))
                .Returns(Task.FromResult(false));

            _fakeCachedPipelineRunManager = new CachedPipelineRunManager(
                config: _fakeQuotaServiceConfig,
                counterManager: _fakeCounterManager,
                cachedContainer: _fakeCachedContainer.Object,
                experimentQueuePublisher: _fakeExperimentQueuePublisher.Object);

            _fakeCachedPipelineRunController = new CachedPipelineRunController(
                counterManager: _fakeCounterManager,
                cachedPipelineRunManager: _fakeCachedPipelineRunManager,
                workspaceResourcesCache: _fakeIWorkspaceResourcesCache.Object);
        }

        [Test]
        public async Task TestDeleteAllCachedPipelineRuns_Normal_Return()
        {
            // If workspaceId is null it should return false.
            var deleted = await _fakeCachedPipelineRunController.DeleteAllCachedPipelineRunsAsync(
                _subscriptionId,
                _resourceGroupName,
                _workspaceName).ConfigureAwait(false);
            Assert.AreEqual(false, deleted);

            // Add cached pipeline run then delete it.
            var experimentId = Guid.NewGuid().ToString();
            var queueMessage = "queue message";
            var added = await _fakeCachedPipelineRunController.AddCachedPipelineRunAsync(
                _subscriptionId,
                _resourceGroupName,
                _workspaceName,
                experimentId,
                queueMessage);
            Assert.IsTrue(added);

            deleted = await _fakeCachedPipelineRunController.DeleteAllCachedPipelineRunsAsync(
                _subscriptionId,
                _resourceGroupName,
                _workspaceName,
                _workspaceId).ConfigureAwait(false);
            Assert.IsTrue(deleted);

            // Delete when workspace does not have any cached pipeline run.
            deleted = await _fakeCachedPipelineRunController.DeleteAllCachedPipelineRunsAsync(
                _subscriptionId,
                _resourceGroupName,
                _workspaceName,
                _workspaceId).ConfigureAwait(false);
            Assert.IsTrue(deleted);

            // Delete all cached pipeline run but one pipeline run is failed to remove.
            experimentId = "pipelineRunFailedToRemove";
            added = await _fakeCachedPipelineRunController.AddCachedPipelineRunAsync(
                _subscriptionId,
                _resourceGroupName,
                _workspaceName,
                experimentId,
                queueMessage);
            Assert.IsTrue(added);

            deleted = await _fakeCachedPipelineRunController.DeleteAllCachedPipelineRunsAsync(
                _subscriptionId,
                _resourceGroupName,
                _workspaceName,
                _workspaceId).ConfigureAwait(false);
            Assert.IsFalse(deleted);
        }

        [Test]
        public async Task TestAddCachedPipelineRun_Normal_Return()
        {
            var experimentId = Guid.NewGuid().ToString();
            var added = await _fakeCachedPipelineRunController.AddCachedPipelineRunAsync(
                _subscriptionId,
                _resourceGroupName,
                _workspaceName,
                experimentId,
                null).ConfigureAwait(false);
            Assert.IsTrue(added);

            // Add cached pipeline run multiple times
            added = await _fakeCachedPipelineRunController.AddCachedPipelineRunAsync(
                _subscriptionId,
                _resourceGroupName,
                _workspaceName,
                experimentId,
                null).ConfigureAwait(false);
            Assert.IsFalse(added);
        }

        [Test]
        public async Task TestCancelCachedPipelineRun_Normal_Return()
        {
            var experimentId = Guid.NewGuid().ToString();
            var added = await _fakeCachedPipelineRunController.AddCachedPipelineRunAsync(
                _subscriptionId,
                _resourceGroupName,
                _workspaceName,
                experimentId,
                null).ConfigureAwait(false);
            Assert.IsTrue(added);

            var cancelled = await _fakeCachedPipelineRunController.CancelCachedPipelineRunAsync(
                _subscriptionId,
                _resourceGroupName,
                _workspaceName,
                experimentId).ConfigureAwait(false);
            Assert.IsTrue(cancelled);

            // Cancel cached pipeline run that does not exist.
            var deleted = await _fakeCachedPipelineRunController.DeleteAllCachedPipelineRunsAsync(
                _subscriptionId,
                _resourceGroupName,
                _workspaceName,
                _workspaceId).ConfigureAwait(false);
            Assert.IsTrue(deleted);

            cancelled = await _fakeCachedPipelineRunController.CancelCachedPipelineRunAsync(
                _subscriptionId,
                _resourceGroupName,
                _workspaceName,
                experimentId).ConfigureAwait(false);
            Assert.IsFalse(cancelled);
        }

        [Test]
        public async Task TestDeleteCachedPipelineRun_Normal_Return()
        {
            var experimentId = Guid.NewGuid().ToString();
            var added = await _fakeCachedPipelineRunController.AddCachedPipelineRunAsync(
                _subscriptionId,
                _resourceGroupName,
                _workspaceName,
                experimentId,
                null).ConfigureAwait(false);
            Assert.IsTrue(added);

            var deleted = await _fakeCachedPipelineRunController.DeleteCachedPipelineRunAsync(
                _subscriptionId,
                _resourceGroupName,
                _workspaceName,
                experimentId).ConfigureAwait(false);
            Assert.IsTrue(deleted);

            // Delete cached pipeline run that does not exist.
            deleted = await _fakeCachedPipelineRunController.DeleteCachedPipelineRunAsync(
                _subscriptionId,
                _resourceGroupName,
                _workspaceName,
                experimentId).ConfigureAwait(false);
            Assert.IsFalse(deleted);
        }

        [Test]
        public async Task TestGetAllCachedPipelineRuns_Normal_Return()
        {
            for (int i = 0; i < 10; i++)
            {
                var experimentId = Guid.NewGuid().ToString();
                var added = await _fakeCachedPipelineRunController.AddCachedPipelineRunAsync(
                    _subscriptionId,
                    _resourceGroupName,
                    _workspaceName,
                    experimentId,
                    null).ConfigureAwait(false);
                Assert.IsTrue(added);
            }

            var allCached = await _fakeCachedPipelineRunController.GetAllCachedPipelineRunsAsync(
                _subscriptionId,
                _resourceGroupName,
                _workspaceName).ConfigureAwait(false);
            Assert.AreEqual(10, allCached.Count());

            var deleted = await _fakeCachedPipelineRunController.DeleteAllCachedPipelineRunsAsync(
                _subscriptionId,
                _resourceGroupName,
                _workspaceName,
                _workspaceId).ConfigureAwait(false);
            Assert.IsTrue(deleted);

            allCached = await _fakeCachedPipelineRunController.GetAllCachedPipelineRunsAsync(
                _subscriptionId,
                _resourceGroupName,
                _workspaceName).ConfigureAwait(false);
            Assert.AreEqual(0, allCached.Count());
        }

        [Test]
        public void TestDeleteAllCachedPipelineRuns_ThrowException()
        {
            QuotaServiceTestHelper.SetupMockStorageThrowException<CachedPipelineRunEntity>(_fakeCachedContainer);
            Assert.ThrowsAsync<RelInfraStorageException>(
                async () => await _fakeCachedPipelineRunController.DeleteAllCachedPipelineRunsAsync(
                    _subscriptionId,
                    _resourceGroupName,
                    _workspaceName,
                    _workspaceId));
        }

        [Test]
        public void TestAddCachedPipelineRun_ThrowException()
        {
            QuotaServiceTestHelper.SetupMockStorageThrowException<CachedPipelineRunEntity>(_fakeCachedContainer);
            Assert.ThrowsAsync<RelInfraStorageException>(
                async () => await _fakeCachedPipelineRunController.AddCachedPipelineRunAsync(
                    _subscriptionId,
                    _resourceGroupName,
                    _workspaceName,
                    _experimentId,
                    "queue message"));
        }

        [Test]
        public void TestCancelCachedPipelineRun_ThrowException()
        {
            QuotaServiceTestHelper.SetupMockStorageThrowException<CachedPipelineRunEntity>(_fakeCachedContainer);
            Assert.ThrowsAsync<RelInfraStorageException>(
                async () => await _fakeCachedPipelineRunController.CancelCachedPipelineRunAsync(
                    _subscriptionId,
                    _resourceGroupName,
                    _workspaceName,
                    _experimentId));
        }

        [Test]
        public void TestDeleteCachedPipelineRun_ThrowException()
        {
            QuotaServiceTestHelper.SetupMockStorageThrowException<CachedPipelineRunEntity>(_fakeCachedContainer);
            Assert.ThrowsAsync<RelInfraStorageException>(
                async () => await _fakeCachedPipelineRunController.DeleteCachedPipelineRunAsync(
                    _subscriptionId,
                    _resourceGroupName,
                    _workspaceName,
                    _experimentId));
        }

        [Test]
        public void TestGetAllCachedPipelineRuns_ThrowException()
        {
            QuotaServiceTestHelper.SetupMockStorageThrowException<CachedPipelineRunEntity>(_fakeCachedContainer);
            Assert.ThrowsAsync<RelInfraStorageException>(
                async () => await _fakeCachedPipelineRunController.GetAllCachedPipelineRunsAsync(
                    _subscriptionId,
                    _resourceGroupName,
                    _workspaceName));
        }
    }
}
