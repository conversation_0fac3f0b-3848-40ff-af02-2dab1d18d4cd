using System.Threading.Tasks;
using Microsoft.Aether.DataContracts.Entity;
using Microsoft.Aether.AEVA.DataContracts;

using CreatedBy = Microsoft.Aether.DataContracts.CreatedBy;

namespace Microsoft.Aether.BlueBox.Ae3PServiceClient
{
    public interface IAe3PServiceClient
    {
        Task<PipelineRunEntity> CreateUnsubmittedPipelineRunWithGraph(PipelineRunCreationInfoWithGraph creationInfo, WorkspaceIdentity workspaceIdentity, CreatedBy createdBy);

        Task SubmitSavedPipelineRunAsync(string pipelineRunId, WorkspaceIdentity workspaceIdentity, CreatedBy createdBy, string parentPipelineRunId, string userAlias = null);

        Task CancelPipelineRunAsync(string pipelineRunId, WorkspaceIdentity workspaceIdentity, CreatedBy createdBy);
    }
}
