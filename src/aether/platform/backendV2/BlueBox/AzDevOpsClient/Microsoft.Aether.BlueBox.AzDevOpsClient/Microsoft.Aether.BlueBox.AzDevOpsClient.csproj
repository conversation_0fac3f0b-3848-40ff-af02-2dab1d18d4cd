<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ResolveAssemblyWarnOrErrorOnTargetArchitectureMismatch>None</ResolveAssemblyWarnOrErrorOnTargetArchitectureMismatch>
    <QInstrumentForCoverage>false</QInstrumentForCoverage>
  </PropertyGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\..\..\RelInfra\RelInfra.Common.Library\src\Extensions\RelInfra.Extensions.csproj" />
    <ProjectReference Include="..\..\..\shared\Microsoft.Aether.DataContract\Microsoft.Aether.DataContracts.csproj" />
    <ProjectReference Include="..\..\CredentialServiceClient\Microsoft.Aether.BlueBox.CredentialServiceClient\Microsoft.Aether.BlueBox.CredentialServiceClient.csproj" />
  </ItemGroup>
</Project>
