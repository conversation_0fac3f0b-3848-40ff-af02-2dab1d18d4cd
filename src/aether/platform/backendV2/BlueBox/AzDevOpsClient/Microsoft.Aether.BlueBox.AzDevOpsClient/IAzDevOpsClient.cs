﻿using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.Aether.DataContracts;
using Microsoft.Aether.DataContracts.Entity;

namespace Microsoft.Aether.BlueBox.AzDevOpsClient
{
    public interface IAzDevOpsClient
    {
        Task UpdateAzDevOpsTaskOnRunCompletionAsync(WorkspaceIdentity workspace, AzDevOpsCallBackInfo callBackInfo, CallBackStatus status, CreatedBy createdBy = null);
    }
}
