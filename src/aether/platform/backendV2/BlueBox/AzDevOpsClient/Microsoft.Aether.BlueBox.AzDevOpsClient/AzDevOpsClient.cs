﻿using System;
using System.Globalization;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Aether.DataContracts;
using Microsoft.Aether.DataContracts.Entity;
using Microsoft.Aether.BlueBox.CredentialServiceClient;
using Microsoft.RelInfra.Common.RetryExecution;
using Microsoft.RelInfra.Common.RetryExecution.Policy;
using Microsoft.RelInfra.Extensions;
using Microsoft.RelInfra.Instrumentation;
using ExponentialBackoffRetryPolicy = Microsoft.RelInfra.Common.RetryExecution.Policy.ExponentialBackoffRetryPolicy;
using Newtonsoft.Json;
using Microsoft.RelInfra.Common.Exceptions;
using Microsoft.RelInfra.Instrumentation.Logging;

namespace Microsoft.Aether.BlueBox.AzDevOpsClient
{
    public class AzDevOpsClient : IAzDevOpsClient
    {
        private const string TaskCompleted = "TaskCompleted";
        private readonly AzDevOpsClientConfig _azDevOpsClientConfig;
        private readonly ICredentialServiceClient _credentialServiceClient;
        private readonly CounterManager _counters;
        private readonly IRetryPolicy _retryPolicy;

        public AzDevOpsClient(AzDevOpsClientConfig azDevOpsClientConfig, ICredentialServiceClient credentialServiceClient, CounterManager counters)
        {
            _azDevOpsClientConfig = azDevOpsClientConfig;
            _credentialServiceClient = credentialServiceClient;
            _retryPolicy = new ExponentialBackoffRetryPolicy(_azDevOpsClientConfig.MaxRetriesForCallBack);
            _counters = counters;
        }

        public async Task UpdateAzDevOpsTaskOnRunCompletionAsync(WorkspaceIdentity workspace, AzDevOpsCallBackInfo callBackInfo, CallBackStatus status, CreatedBy createdBy = null)
        {
            string authToken = callBackInfo.AuthToken;

            if (string.IsNullOrEmpty(authToken))
            {
                CredentialDto credential = await _credentialServiceClient.GetCredentialAsync(workspace, callBackInfo.AuthTokenId, createdBy);
                authToken = credential.Value;
            }

            AzDevOpsCallBackResponse response = new AzDevOpsCallBackResponse
            {
                Name = TaskCompleted,
                TaskId = callBackInfo.TaskInstanceId,
                JobId = callBackInfo.JobId,
                Result = status
            };

            HttpClient _httpClient = new HttpClient
            {
                BaseAddress = new Uri(callBackInfo.PlanUri)
            };

            using (CancellationTokenSource cancellationTokenSource = new CancellationTokenSource(_azDevOpsClientConfig.TimeoutForCallBackInMs))
            {
                using (_counters.GetLatencyCounter("RunCompletionAzDevOpsCallBackLatency").StartCounter())
                {
                    using (StringContent content = new StringContent(JsonConvert.SerializeObject(response), Encoding.UTF8, "application/json"))
                    {
                        string serverPath = this.GetAzDevOpsCallBackPath(callBackInfo);
                        try
                        {
                            await RetryExecutionHelper.ExecuteFuncWithRetryAsync(
                                function: async () => await _httpClient.SendContentWithAuthHeaderAsync(httpMethod: HttpMethod.Post, serverPath: serverPath, content: content, authToken: authToken, customHeaders: createdBy?.GetCreatedByHeaders()),
                                shouldRetryException: this.ShouldRetryHttpClientExtensionsException,
                                retryPolicy: _retryPolicy,
                                token: cancellationTokenSource.Token);
                        }
                        catch (Exception e)
                        {
                            throw new ServiceInvocationException(
                                operationName: "UpdateAzDevOpsTaskOnRunCompletion",
                                calledService: "AzDevOpsClient",
                                httpMethod: HttpMethod.Post,
                                innerException: e);
                        }
                    }
                }
            }
        }

        private string GetAzDevOpsCallBackPath(AzDevOpsCallBackInfo callBackInfo)
        {
            return string.Format(CultureInfo.InvariantCulture, _azDevOpsClientConfig.CallBackPath, callBackInfo.ProjectId, callBackInfo.HubName, callBackInfo.PlanId, _azDevOpsClientConfig.AzDevOpsCallBackApiVersion);
        }
		
		private bool ShouldRetryHttpClientExtensionsException(Exception exception)
        {
            HttpRequestDetailException requestDetailException = exception as HttpRequestDetailException;

            if (requestDetailException != null)
            {
                if (requestDetailException.StatusCode == HttpStatusCode.BadGateway ||
                    requestDetailException.StatusCode == HttpStatusCode.ServiceUnavailable ||
                    requestDetailException.StatusCode == HttpStatusCode.GatewayTimeout ||
                    requestDetailException.StatusCode == HttpStatusCode.RequestTimeout)
                {
                    _counters.GetRateCounter("RunCompletionAzDevOpsCallBackRetryRate").Increment();
                    return true;
                }

                if (requestDetailException.InnerException != null && requestDetailException.InnerException.InnerException != null)
                {
                    WebException webException = requestDetailException.InnerException.InnerException as WebException;
                    if (webException != null)
                    {
                        return webException.Status == WebExceptionStatus.ConnectFailure || webException.Status == WebExceptionStatus.NameResolutionFailure;
                    }
                }
            }

            return false;
        }
    }
}
