﻿using System.Threading.Tasks;
using Microsoft.Aether.DataContracts;
using Microsoft.Aether.DataContracts.Entity;

namespace Microsoft.Aether.BlueBox.AzDevOpsClient
{
    public class DummyAzDevOpsClient : IAzDevOpsClient
    {
        public Task UpdateAzDevOpsTaskOnRunCompletionAsync(WorkspaceIdentity workspace, AzDevOpsCallBackInfo callBackInfo, CallBackStatus status, CreatedBy createdBy = null)
        {
            return Task.CompletedTask;
        }
    }
}
