﻿using Microsoft.RelInfra.Common.Configuration;

namespace Microsoft.Aether.BlueBox.AzDevOpsClient
{
    public class AzDevOpsClientConfig : NestedVerifiableConfig
    {
        public AzDevOpsClientConfig(IRelInfraConfiguration config)
            : base(config, configName: nameof(AzDevOpsClientConfig))
        {
        }

        public string CallBackPath => Config.GetString("AzDevOpsClient.CallBackPath");

        public string AzDevOpsCallBackApiVersion => Config.GetString("AzDevOpsClient.CallBackApiVersion");

        public int MaxRetriesForCallBack => Config.GetInteger("AzDevOpsClient.MaxRetriesForCallBack");

        public int TimeoutForCallBackInMs => Config.GetInteger("AzDevOpsClient.TimeoutForCallBackInMs");
    }
}
