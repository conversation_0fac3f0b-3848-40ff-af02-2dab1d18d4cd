﻿using DataBricksCloudK8sService.DataBricksClient;
using Microsoft.Aether.BackendCommon.StdOutLogger;
using Microsoft.Aether.BlueBox.DataStoreClient;
using Microsoft.Aether.BlueBox.RunHistoryClient;
using Microsoft.Aether.BlueBox.Storage;
using Microsoft.Aether.BlueBox.TokenServiceClient;
using Microsoft.Aether.BlueBox.WorkspaceResourcesClient;
using Microsoft.Aether.CloudCommon;
using Microsoft.Aether.CloudCommon.Worker;
using Microsoft.Aether.Clouds.CloudCommon;
using Microsoft.Aether.Clouds.CloudCommon.Cloudlets;
using Microsoft.Aether.Clouds.CloudCommon.JobQueueProcessing;
using Microsoft.Aether.S2S.Common;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.RelInfra.Instrumentation;

namespace Microsoft.Aether.DataBricksCloud.K8sService
{
    public class JobProcessorFactory : IJobProcessorFactory<DataBricksJobMetadata>
    {
        private readonly IJobMetadataStorage<DataBricksJobMetadata> _jobStorage;
        private readonly CounterManager _counterManager;
        private readonly IWorkDirectoryManager _workDirectoryManager;
        private readonly IDataBricksClientFactory _dataBricksClientFactory;
        private readonly IDataStoreClient _dataStoreClient;
        private readonly IRunStateUpdater _runStateUpdater;
        private readonly IWorkspaceResourcesClient _workspaceResourcesClient;
        private readonly IMemoryCache _workspaceUrlAndPATCache;
        private readonly IS2STokenProvider _s2STokenProvider;
        private readonly IModuleResourceManagerFactory _moduleResourceManagerFactory;
        private readonly ITokenServiceClient _tokenServiceClient;
        private readonly IDBFSClientFactory _dbfsClientFactory;
        private readonly DataBricksCloudConfig _databricksCloudConfig;
        private readonly IJobStdOutLoggerFactory _jobStdOutLoggerFactory;
        private readonly IJobStatusCache _jobStatusCache;
        private readonly ArtifactServiceRegistry _artifactServiceRegistry;
        private readonly IRunHistoryClient _runHistoryClient;
        private readonly IErrorClassifier _errorClassifier;

        public JobProcessorFactory(IJobMetadataStorage<DataBricksJobMetadata> jobStorage,
            CounterManager counterManager,
            IDataBricksClientFactory dataBricksClientFactory,
            IDataStoreClient dataStoreClient,
            IRunStateUpdater runStateUpdater,
            IWorkspaceResourcesClient workspaceResourcesClient,
            IWorkDirectoryManager workDirectoryManager,
            IMemoryCache workspaceUrlAndPATCache,
            IS2STokenProvider s2STokenProvider,
            IModuleResourceManagerFactory moduleResourceManagerFactory,
            ITokenServiceClient tokenServiceClient,
            IDBFSClientFactory dbfsClientFactory,
            DataBricksCloudConfig databricksCloudConfig,
            IJobStdOutLoggerFactory jobStdOutLoggerFactory,
            IJobStatusCache jobStatusCache,
            ArtifactServiceRegistry artifactServiceRegistry,
            IRunHistoryClient runHistoryClient,
            IErrorClassifier errorClassifier)
        {
            _jobStorage = jobStorage;
            _counterManager = counterManager;
            _dataBricksClientFactory = dataBricksClientFactory;
            _dataStoreClient = dataStoreClient;
            _runStateUpdater = runStateUpdater;
            _workspaceResourcesClient = workspaceResourcesClient;
            _workDirectoryManager = workDirectoryManager;
            _workspaceUrlAndPATCache = workspaceUrlAndPATCache;
            _s2STokenProvider = s2STokenProvider;
            _moduleResourceManagerFactory = moduleResourceManagerFactory;
            _tokenServiceClient = tokenServiceClient;
            _dbfsClientFactory = dbfsClientFactory;
            _databricksCloudConfig = databricksCloudConfig;
            _jobStdOutLoggerFactory = jobStdOutLoggerFactory;
            _jobStatusCache = jobStatusCache;
            _artifactServiceRegistry = artifactServiceRegistry;
            _runHistoryClient = runHistoryClient;
            _errorClassifier = errorClassifier;
        }

        public IJobProcessor<DataBricksJobMetadata> CreateJobProcessor(string jobId)
        {
            string executionDirectory = _workDirectoryManager.CreateDirectoryForJob(jobId);

            return new JobProcessor(jobId: jobId,
                jobStorage: _jobStorage,
                counterManager: _counterManager,
                dataBricksClientFactory: _dataBricksClientFactory,
                dataStoreClient: _dataStoreClient,
                runStateUpdater: _runStateUpdater,
                workspaceResourcesClient: _workspaceResourcesClient,
                executionDirectory: executionDirectory,
                workspaceUrlAndPATCache: _workspaceUrlAndPATCache,
                s2STokenProvider: _s2STokenProvider,
                moduleResourceManagerFactory: _moduleResourceManagerFactory,
                tokenServiceClient: _tokenServiceClient,
                dbfsClientFactory: _dbfsClientFactory,
                workingDirectoryManager: _workDirectoryManager,
                databricksCloudConfig: _databricksCloudConfig,
                jobStdOutLoggerFactory: _jobStdOutLoggerFactory,
                jobStatusCache: _jobStatusCache,
                artifactServiceRegistry: _artifactServiceRegistry,
                runHistoryClient: _runHistoryClient,
                errorClassifier: _errorClassifier);
        }
    }
}
