﻿using System;
using System.Collections.Generic;
using System.Linq;
using DataBricksCloudK8sService.DataBricksClient;
using Microsoft.Aether.Clouds.CloudCommon;

namespace Microsoft.Aether.DataBricksCloud.K8sService
{
    public static class DataBricksParameterParser
    {
        // current expected format:
        //coordinates=library1coordinates|repo=library1repo|exclusions=library1exclusion1,library1exclusing2;
        //coordinates=library2coordinates|repo=library2repo|exclusions=library2exclusion1,library2exclusing2
        public static List<MavenLibraryDto> TryGetMavenLibraries(Dictionary<string, string> parameters, string name)
        {
            string value = ParameterParser.TryGetParameterValue(parameters, name);
            if (string.IsNullOrEmpty(value))
            {
                return new List<MavenLibraryDto>();
            }

            List<MavenLibraryDto> libraries = new List<MavenLibraryDto>();
            if (!string.IsNullOrWhiteSpace(value))
            {
                var LibraryList = value.Split(';');
                foreach (string lib in LibraryList)
                {
                    var fieldList = lib.Split('|');
                    MavenLibraryDto library = new MavenLibraryDto
                    {
                        Coordinates = TryGetFieldValue("coordinates=", fieldList),
                        Repo = TryGetFieldValue("repo=", fieldList),
                        Exclusions= TryGetFieldValue("exclusions=", fieldList)?.Split(',').ToList()
                    };
                    libraries.Add(library);
                }
            }

            return libraries;
        }

        // current expected format:
        //package=library1package|repo=library1repo;
        //package=library2package|repo=library2repo
        public static List<PythonPyPiOrRCranLibraryDto> TryGetPythonPyPiOrRCranLibraries(Dictionary<string, string> parameters, string name)
        {
            string value = ParameterParser.TryGetParameterValue(parameters, name);
            if (string.IsNullOrEmpty(value))
            {
                return new List<PythonPyPiOrRCranLibraryDto>();
            }

            List<PythonPyPiOrRCranLibraryDto> libraries = new List<PythonPyPiOrRCranLibraryDto>();
            if (!string.IsNullOrWhiteSpace(value))
            {
                var LibraryList = value.Split(';');
                foreach (string lib in LibraryList)
                {
                    var fieldList = lib.Split('|');
                    PythonPyPiOrRCranLibraryDto library = new PythonPyPiOrRCranLibraryDto
                    {
                        Package = TryGetFieldValue("package=", fieldList),
                        Repo = TryGetFieldValue("repo=", fieldList),
                    };
                    libraries.Add(library);
                }
            }

            return libraries;
        }

        private static string TryGetFieldValue(string field, string[] fieldList)
        {
            var valuePair = fieldList.FirstOrDefault(x => x.Contains(field))?.Split(field);

            if(valuePair?.Length != 2)
            {
                return null;
            }

            return valuePair[1];
        }
    }
}
