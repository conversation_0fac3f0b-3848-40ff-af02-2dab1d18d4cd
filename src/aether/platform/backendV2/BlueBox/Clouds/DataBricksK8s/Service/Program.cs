﻿using Aether.Backend.CacheClient;
using DataBricksCloudK8sService.DataBricksClient;
using Microsoft.Aether.BackendCommon;
using Microsoft.Aether.BackendCommon.ClusterHealthPoller;
using Microsoft.Aether.BackendCommon.Configuration;
using Microsoft.Aether.BackendCommon.Time;
using Microsoft.Aether.BlueBox.AetherK8SBaseService;
using Microsoft.Aether.BlueBox.AetherK8SCommon;
using Microsoft.Aether.BlueBox.AetherK8SCommon.Auth;
using Microsoft.Aether.BlueBox.ArtifactClient;
using Microsoft.Aether.BlueBox.Clouds.Common;
using Microsoft.Aether.BlueBox.DataStoreClient;
using Microsoft.Aether.BlueBox.RunHistoryClient;
using Microsoft.Aether.BlueBox.SnapshotClient;
using Microsoft.Aether.BlueBox.Storage;
using Microsoft.Aether.BlueBox.TokenServiceClient;
using Microsoft.Aether.BlueBox.Web.Common;
using Microsoft.Aether.BlueBox.WorkspaceResourcesClient;
using Microsoft.Aether.CloudCommon;
using Microsoft.Aether.CloudCommon.Worker;
using Microsoft.Aether.CloudCommon.Worker.Cache;
using Microsoft.Aether.Clouds.CloudCommon;
using Microsoft.Aether.Clouds.CloudCommon.Cloudlets;
using Microsoft.Aether.Clouds.CloudCommon.JobQueueProcessing;
using Microsoft.Aether.Clouds.CloudCommon.JobQueueProcessing.QueueRetrieving;
using Microsoft.Aether.Clouds.CloudCommon.WebNetCore;
using Microsoft.Aether.StorageDescription;
using Microsoft.Aether.TaggedQueueManager;
using Microsoft.Aether.TaggedQueueManager.CloudTags;
using Microsoft.ApplicationInsights;
using Microsoft.AspNetCore;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.MachineLearning.Common.WebApi.AppInsights;
using Microsoft.RelInfra.Common;
using Microsoft.RelInfra.Common.Configuration;
using Microsoft.RelInfra.Common.Exceptions;
using Microsoft.RelInfra.Common.RetryExecution.Policy;
using Microsoft.RelInfra.Instrumentation;
using Microsoft.RelInfra.Instrumentation.Logging;
using Microsoft.RelInfra.Storage;
using System;
using System.Collections.Generic;
using System.IO;
using System.Net;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;

namespace Microsoft.Aether.DataBricksCloud.K8sService
{
    [System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
    class DataBricksCloudK8sService
    {
        private const string ServiceName = ServiceNames.DataBricksCloudService;
        private const string configFileName = "DataBricksCloudService.ini";
        private const string sharedConfigFilename = "SharedSettings.ini";
        private const string BBSharedConfigFilename = "BBSharedSettings.ini";
        private const string DefaultIndexPolicyFile = "cloudletindexpolicy.json";
        private const string DataDirectoryVariable = "DataDirectory";

        private static void Main(string[] args)
        {
            IWebHostBuilder hostBuilder = WebHost.CreateDefaultBuilder(args)
                .UseStartup<DataBricksCloudK8sService>()
                .UseKestrel(options =>
                {
                    var cfg = options.ApplicationServices.GetService<IRelInfraConfiguration>();
                    //Used to be MaxReceivedMessageSize. Seem to both by in bytes.
                    options.Limits.MaxRequestBodySize = cfg.GetInteger("Server.MaxReceivedMessageSize");
                    //just pull out the port. Do we care
                    var url = new Uri(cfg.GetString("Server.BaseAddress"));
                    options.Listen(IPAddress.Any, url.Port, listenOptions =>
                    {
                        CommonLogger.LogEntityInfo(ServiceName, $"Port: {url.Port}");
                        DataBricksCloudConfig dataBricksCloudConfig = options.ApplicationServices.GetService<DataBricksCloudConfig>();
                        if (Env.DeploymentType() != "onebox")
                        {
                            listenOptions.UseHttps(fileName: dataBricksCloudConfig.GetSslCert(), password: string.Empty);
                        }
                    });
                    //https://github.com/aspnet/KestrelHttpServer/blob/rel/2.0.0/src/Microsoft.AspNetCore.Server.Kestrel.Core/KestrelServerLimits.cs
                    //don't seem to have an equivalent of SendTimeout = TimeSpan.FromSeconds(300),
                    //could use MaxResponseBufferSize or KeepAliveTimeout but I don't think that's what we want.
                    //https://github.com/aspnet/KestrelHttpServer/issues/611
                })
                .ConfigureLogging(builder => builder.ClearProviders().AddCommonLogger());

            hostBuilder.Build()
                .Run();
        }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            StartupAsync(services).Wait();
        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app)
        {
            //http://www.talkingdotnet.com/how-to-enable-gzip-compression-in-asp-net-core/
            app.UseResponseCompression();
            app.UseMvc();
        }

        static async Task StartupAsync(IServiceCollection services)
        {
            try
            {
                string dataDirectory = Environment.GetEnvironmentVariable(DataDirectoryVariable);
                string machineName = Environment.MachineName;

                var k8sService = new K8sBaseService(ServiceName, configFilename: configFileName,
                    iniFileNames: new[] { sharedConfigFilename, BBSharedConfigFilename });
                k8sService.InitializeAsync().Wait();

                var cloudletConfig = new CloudletConfig(k8sService.ApConfig, k8sService.SecretProvider);
                cloudletConfig.VerifyConfig();

                CounterManager counterManager = k8sService.CounterManager;

                var storageDescriptions = new StorageDescriptions();
                var legacyStorageInitializer = new LegacyStorageInitializer();
                await legacyStorageInitializer.InitializeLegacyStoragesForCloudletAsync(cloudletConfig, storageDescriptions.DataBricksCloudlet);

                services.ConfigureAppInsightsTargetFilter(new HashSet<string>() {
                    $"{Env.StorageAccountNameCloudlet()}{Env.StorageQueueEndpoint()}",
                    $"{Env.StorageAccountNameCloudletQueue()}{Env.StorageQueueEndpoint()}",
                });

                services.ConfigureBasicServices(serviceName: ServiceName, baseService: k8sService, configuration: null);

                var workloadIdentityAvailable = k8sService.WorkloadIdentityProvider?.IsAvailable ?? false;
                CommonLogger.LogInfo($"Workload identity available: {workloadIdentityAvailable}");
                IStorageObjectFactory storageObjectFactory;
                if (workloadIdentityAvailable)
                {
                    storageObjectFactory = new StorageObjectFactoryV2(counterManager, k8sService.WorkloadIdentityProvider);
                }
                else
                {
                    storageObjectFactory = new StorageObjectFactory(counterManager);
                }
                storageObjectFactory.LoadProvisionedStoragesFromFile(StorageObjectFactory.ProvisionedStoragesFileName);

                var dataBricksCloudConfig = new DataBricksCloudConfig(k8sService.ApConfig, k8sService.SecretProvider);
                dataBricksCloudConfig.VerifyConfig();

                var artifactPrefix = Env.AzurePrefix();
                CommonLogger.LogInfo($"Azure table prefix is {artifactPrefix}");

                CosmosDbConnectionInfo cosmosDbConnectionInfo = storageObjectFactory.GetCosmosDbConnectionInfo(connectionName: storageDescriptions.CloudletsShared.SharedCosmosDb);

                // job storage
                var indexPolicy = CosmosDbIndexingPolicyHelper.LoadIndexingPolicyFromFileV2(DefaultIndexPolicyFile);

                var memoryCache = new MemoryCache(new MemoryCacheOptions());
                BbMetaStorageConnectionProvider defaultJobStorageConnectionProvider = new BbMetaStorageConnectionProvider(cosmosDbConnectionInfo);
                var workspaceJobStorageProvider = new BlueBoxUserCosmosDbStorageProviderV2(databaseName: dataBricksCloudConfig.JobStorageDatabaseName,
                    containerName: artifactPrefix + dataBricksCloudConfig.JobStorageTableName,
                    partitionKeyPath: "/id",
                    workloadIdentityProvider: k8sService.WorkloadIdentityProvider,
                    cache: memoryCache, cacheExpiration: dataBricksCloudConfig.WorkspaceJobStorageCacheDuration,
                    defaultConnectionProvider: defaultJobStorageConnectionProvider, counterManager: counterManager,
                    defaultTimeToLive: TimeSpan.FromDays(60),
                    indexPolicy: indexPolicy,
                    cosmosDbOptions: cloudletConfig.GetCosmosDbOptions());
                await workspaceJobStorageProvider.InitDefaultDbStorageAsync();
                var jobStorage = new JobMetadataDbStorage<DataBricksJobMetadata>(workspaceJobStorageProvider);

                ITaggedQueueManager<AssignedQueueIdTags, string> queueManager;
                if (workloadIdentityAvailable)
                {
                    var serviceUri = Env.GetQueueStorageEndpointUri(Env.StorageAccountNameCloudletQueue());
                    queueManager = new AzureTaggedQueueManagerV2<AssignedQueueIdTags, string>(
                        serviceUri: serviceUri,
                        credential: k8sService.WorkloadIdentityProvider.Credential,
                        queuePrefix: artifactPrefix + dataBricksCloudConfig.AzureQueueManagerPrefix,
                        timerFactory: new TimerFactory(),
                        updateTimeSpan: dataBricksCloudConfig.AzureQueueManagerUpdatePeriod);
                }
                else
                {
                    var queueConnectionstring = storageObjectFactory.GetAzureConnectionString(connectionName: storageDescriptions.DataBricksCloudlet.QueueAzureStorage);
                    queueManager = new AzureTaggedQueueManager<AssignedQueueIdTags, string>(
                        connectionString: queueConnectionstring,
                        queuePrefix: artifactPrefix + dataBricksCloudConfig.AzureQueueManagerPrefix,
                        timerFactory: new TimerFactory(),
                        updateTimeSpan: dataBricksCloudConfig.AzureQueueManagerUpdatePeriod);
                }
                await queueManager.InitializeAsync();

                var lockStorage = new AzureBlobLeasableContainerStorageV2(storageObjectFactory.GetAzureBlobContainer(storageDescriptions.DataBricksCloudlet.CloudletLocks));

                var amlSnapshotConfig = new AmlSnapshotConfig(k8sService.ApConfig);
                var clusterHealthPoller = new ClusterHealthPoller();
                var snapshotClient = new SnapshotClient(amlSnapshotConfig, k8sService.ServicePrincipalAadTokenProvider, k8sService.CounterManager, clusterHealthPoller);

                var workspaceResourcesConfig = new WorkspaceResourcesConfig(k8sService.ApConfig);
                var workspaceResourcesClient = new WorkspaceResourcesSvcClient(workspaceResourcesConfig, k8sService.ServicePrincipalAadTokenProvider, k8sService.CounterManager, clusterHealthPoller);
                var workspaceResourcesCache = new WorkspaceResourcesCache(distributedCache: null, memoryCache: memoryCache, workspaceResourcesClient: workspaceResourcesClient, counterManager);

                IS2SArtifactClient artifactClient = new S2SArtifactSvcClient(
                    config: new ArtifactConfig(k8sService.ApConfig, ServiceName),
                    servicePrincipalTokenProvider: k8sService.ServicePrincipalAadTokenProvider,
                    retryPolicy: new ExponentialBackoffRetryPolicy(2),
                    retryOnTooManyRequestsException: true,
                    counters: k8sService.CounterManager,
                    clusterHealthPoller: clusterHealthPoller);

                IModuleResourceManagerFactory moduleResourceManagerFactory = await
                    BlueBoxModuleResourceManagerFactory.CreateFactoryAsync(
                        dataDirectory: dataDirectory,
                        counters: counterManager,
                        serviceConfig: k8sService.ApConfig,
                        maxInputSize: k8sService.ApConfig.GetInteger("DataBricksJob.MaxInputSize"),
                        maxModuleSize: k8sService.ApConfig.GetInteger("DataBricksJob.MaxModuleSize"),
                        workspaceResourcesCache: workspaceResourcesCache,
                        snapshotClient: snapshotClient,
                        artifactClient: artifactClient,
                        cacheReporter: new CacheInMemoryReporter(machineName)).ConfigureAwait(false);

                var amltokenServiceConfig = new AmlTokenServiceConfig(k8sService.ApConfig);

                ITokenServiceClient tokenServiceClient = new TokenServiceClient(amltokenServiceConfig, k8sService.ServicePrincipalAadTokenProvider, clusterHealthPoller);

                var amlDataStoreConfig = new AmlDataStoreConfig(k8sService.ApConfig);
                var dataStoreClient = new DataStoreSvcClient(amlDataStoreConfig, k8sService.ServicePrincipalAadTokenProvider, k8sService.CounterManager, clusterHealthPoller);

                var amlRunMetricConfig = new AmlRunMetricServiceConfig(k8sService.ApConfig);
                var amlRunMetricClient = new AmlRunMetricClient(amlRunMetricConfig, k8sService.ServicePrincipalAadTokenProvider, k8sService.CounterManager);
                var amlRunHistoryConfig = new AmlRunHistoryConfig(k8sService.ApConfig);
                var runHistoryClient = new RunHistorySvcClient(amlRunHistoryConfig, k8sService.ServicePrincipalAadTokenProvider, amlRunMetricClient, k8sService.CounterManager, clusterHealthPoller);

                string workDir = Path.Combine(dataDirectory, dataBricksCloudConfig.WorkDirectory);
                if (!Directory.Exists(workDir))
                {
                    Directory.CreateDirectory(workDir);
                }
                var directoryManager = new WorkDirectoryManager(workDir);

                MemoryCache workspaceUrlAndPATCache = new MemoryCache(new MemoryCacheOptions { SizeLimit = 10000 }); // Allow 10,000 entries

                RedisCacheConfiguration redisConfig = new RedisCacheConfiguration(k8sService.ApConfig, k8sService.SecretProvider);
                var redisCache = await AetherRedisCache.CreateAsync(redisConfig, counterManager);
                var jobStatusCache = new JobStatusCache(redisCache, redisConfig.ValidityPeriod);
                string path = Path.Combine(Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location), @"error_config.json");
                var errorClassifier = new ErrorClassifier(path, AMLServiceTeams.DataBricksCloud);
                var jobProcessorFactory = new JobProcessorFactory(jobStorage: jobStorage,
                    counterManager: counterManager,
                    dataBricksClientFactory: new DataBricksClientFactory(),
                    dataStoreClient: dataStoreClient,
                    runStateUpdater: new RunStateUpdater(runHistoryClient, counterManager),
                    workspaceResourcesClient: workspaceResourcesClient,
                    workDirectoryManager: directoryManager,
                    workspaceUrlAndPATCache: workspaceUrlAndPATCache,
                    s2STokenProvider: k8sService.ServicePrincipalAadTokenProvider,
                    moduleResourceManagerFactory: moduleResourceManagerFactory,
                    tokenServiceClient: tokenServiceClient,
                    dbfsClientFactory: new DBFSClientFactory(),
                    databricksCloudConfig: dataBricksCloudConfig,
                    jobStdOutLoggerFactory: new MsiJobStdOutLoggerFactory(workspaceResourcesCache, counterManager),
                    jobStatusCache: jobStatusCache,
                    artifactServiceRegistry: new ArtifactServiceRegistry(moduleResourceManagerFactory),
                    runHistoryClient: runHistoryClient,
                    errorClassifier: errorClassifier);

                string cloudNameTablePrefix = "DataBricksCloud";
                CloudJobQueueTracker<DataBricksJobMetadata> jobQueueTracker;
                if (workloadIdentityAvailable)
                {
                    var storageAccountUri = Env.GetTableStorageEndpointUri(Env.StorageAccountNameCloudlet());
                    jobQueueTracker = new CloudJobQueueTracker<DataBricksJobMetadata>(
                        storageAccountUri: storageAccountUri,
                        credential: k8sService.WorkloadIdentityProvider.Credential,
                        machineName: machineName,
                        dataDirectory: dataDirectory,
                        cloudName: cloudNameTablePrefix,
                        jobQueueName: queueManager.QueuePrefix,
                        counters: counterManager);
                }
                else
                {
                    var storageConnectionstring = storageObjectFactory.GetAzureConnectionString(connectionName: storageDescriptions.DataBricksCloudlet.SharedAzureStorage);
                    jobQueueTracker = new CloudJobQueueTracker<DataBricksJobMetadata>(
                        azureConnectionString: storageConnectionstring,
                        machineName: machineName,
                        dataDirectory: dataDirectory,
                        cloudName: cloudNameTablePrefix,
                        jobQueueName: queueManager.QueuePrefix,
                        counters: counterManager);
                }

                var queueMessageRetriever = new TaggedQueueManagerMessageRetriever<AssignedQueueIdTags, string>(
                    taggedQueueManager: queueManager,
                    tagDescriptor: new AssignedQueueIdDescriptor(),
                    counterManager: counterManager);

                var taggedJobQueueManager = new TaggedJobQueueManager<AssignedQueueIdTags, DataBricksJobMetadata>(
                    config: cloudletConfig.JobQueueManagerConfig,
                    counters: counterManager,
                    queueMessageRetriever: queueMessageRetriever,
                    lockStorage: lockStorage,
                    jobStorage: jobStorage,
                    jobProcessorFactory: jobProcessorFactory,
                    jobQueueTracker: jobQueueTracker);

                var commonCounterManager = await k8sService.InitializeCommonCounterAsync();
                var jobQueue = new JobQueue(taggedJobQueueManager, k8sService.ApConfig, commonCounterManager);
                jobQueue.ProcessQueueAsync(CancellationToken.None).FireAndForget();

                var taggedJobsManager = new TaggedJobsManager<AssignedQueueIdTags, DataBricksJobMetadata>(
                    jobMetadataStorage: jobStorage,
                    taggedQueueManager: queueManager,
                    outputSource: new NAJobsManagerOutputSource(),
                    cloudletJobMetadataFactory: new DataBricksJobMetadataFactory(),
                    jobStatusCache: jobStatusCache);

                services.AddSingleton<IRelInfraConfiguration>(k8sService.ApConfig);
                services.AddSingleton(k8sService.WorkloadIdentityProvider);
                services.AddSingleton(dataBricksCloudConfig);
                services.AddSingleton<CounterManager>(counterManager);
                services.AddSingleton<ITaggedJobsManager<AssignedQueueIdTags, DataBricksJobMetadata>>(taggedJobsManager);
                services.AddSingleton(jobQueue);

                services.AddMvc(options =>
                {
                    options.EnableEndpointRouting = false;
                    if (Env.DeploymentType() != "onebox")
                    {
                        options.AddCommonAppInsightsComponents();
                        options.Filters.Add(new S2sAuthorizeAttribute(k8sService.S2sAuthorizer, keepAlivePath: "/keepalive"));
                    }
                    options.Filters.Add(new CloudletExceptionFilter());
                });

                var telemetryClient = services.BuildServiceProvider().GetRequiredService<TelemetryClient>();
                // Update telemetry client here since telemetry client is updated after the queue manager created
                taggedJobQueueManager.UpdateTelemetryClient(telemetryClient);
            }
            catch (ServiceInvocationException e)
            {
                Console.WriteLine($"{e}");
                CommonLogger.LogEntityError(ServiceName, "Unhandled Exception: {safe_exception_message}, stack trace: {stack_trace}", e.Message, e.StackTrace);
                throw;
            }
            catch (Exception e)
            {
                Console.WriteLine($"{e}");
                CommonLogger.LogEntityError(ServiceName, $"Unhandled Exception: {e}");
                throw;
            }
        }
    }
}
