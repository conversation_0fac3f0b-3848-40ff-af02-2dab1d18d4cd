﻿[Server]
; Address to listen for incoming requests
k8SEnvType:onebox$BaseAddress=http://localhost:80
BaseAddress=https://localhost:443

; 50MB
MaxReceivedMessageSize=52428800

[CosmosStorage]
UseMfCertificate=false

[RedisCache]
k8SEnvType:onebox$ConnectionStringFileName=keyvault://aether3prediskey
k8sEnvType:dev$ConnectionStringFileName=keyvault://aether3prediskey
k8sEnvType:ppe$ConnectionStringFileName=keyvault://aether3prediskey
k8sEnvType:prod$ConnectionStringFileName=keyvault://aether3prediskey

RetryAttempts=1
RetryIntervalTimeSpan=0:0:1.0
ValidityPeriodTimeSpan=0:2:0.0

[JobQueueManager]
MaxConcurrentJobs=50
LeaseTimeForJobQueueMessage=00:00:30
ProcessQueueDelay=00:00:5

JobLockContainerName=databrickslockslocks

[AzureStorage]
AzureConnectionFile=AEtherCloudletsSecrets\HdiCloud\azurestorage-connectstr.encr
isK8s:true$AzureConnectionFile=keyvault://databricksstorageconnectionstr
QueueStorageAzureConnectionFile=keyvault://databricksqueuestorageconnectionstr

[JobStorage]
; For local/transient environment, we share table with other clouds to reduce cost
AzurePrefix:transient$TableName=transientjobs
AzurePrefix:onebox$TableName=oneboxjobs
TableName=databricksjobs
AzurePrefix:master$DatabaseName=cloudlets
AzurePrefix:transient$DatabaseName=transientcloudlets
AzurePrefix:onebox$DatabaseName=oneboxcloudlets
CmkDatabaseName=db
CmkTableName=pipelinejobs

WorkspaceCacheDuration=00:10:00

; We set this value as null so we don't adjust throughput in the service.
CosmosDbThroughput=
IsAutoscale=true

; For cmk workspace, we still keep non-autoscale settings
CmkCosmosDbThroughput=
CmkIsAutoscale=false

[JobQueue]
LockStorageNumRetries=3
LockStorageRetryInterval=00:00:05

[AzureQueueManager]
QueuePrefix=databricksjobs

UpdatePeriod=00:10:00

[DataBricksJob]
; TODO - Temporarily set to 1M 
MaxInputSize=1048576

; TODO - Temporarily set to 1M 
MaxModuleSize=1048576

[JobGc]
; Sleeps between iterations of garbage collector
;  15 minutes
SleepBetweenGcTimespan=00:15:00

; Archive any job which was completed that long ago
;  2 days
ArchiveCompletedJobsOlderThanTimespan=2:0:0:0.0

;
;  Cache
;
[DataCache]
; minimumfreespace when cache enabled is ~1.5TiB
MinimumFreeGBs=1400
MinimumFreeGBsAfterClean=1800
MaximumCacheCount=20000
MaximumCacheCountAfterClean=10000
CacheLocalPath=cache\
CacheNetworkPath=AEtherDataDir\cache\
Enabled=true
CacheAuxStoragePath=cacheinternal\
; How often GC runs
GarbageCollectCycleSec=300

; Give up after trying N remote machines allegedly having data cached
MaxRemoteMachinesToAttemptCopy=5

; Global cache is not necessary persistent, hence it's better to regular repopulate it.
; After GC ran, the bulk update of global cache will be done according to this timespan since previous bulk attempt
BulkRefreshGlobalCacheMapEverySec=14400

; Throttling for the global cache bulk update
NumberOfReportsPerSec=10

[WorkDirectory]
Name=AetherDataBricksCloud
; Sleep for 1 hour between cleanups
CleanupSleepTimeSpan=0.1:0:0
; Cleanup temp directories 1 day after they've been created (don't use 24:0:0 - it would mean 24 days')
CleanupAgeTimeSpan=1.0:0:0

[ClusterHealthPoller]
Enabled=false
k8sEnvType:dev$Enabled=true
k8SEnvType:prod$Enabled=true
ClusterHealthCheckDelay=00:02:00