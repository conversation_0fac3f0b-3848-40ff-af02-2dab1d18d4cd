﻿using Common.Core.Contracts;
using Common.WebApi.Exceptions;
using DataBricksCloudK8sService.DataBricksClient;
using Microsoft.Aether.BackendCommon.Exceptions;
using Microsoft.Aether.BackendCommon.Logging;
using Microsoft.Aether.BackendCommon.StdOutLogger;
using Microsoft.Aether.BlueBox.Clouds.Common;
using Microsoft.Aether.BlueBox.DataStoreClient;
using Microsoft.Aether.BlueBox.DataStoreClient.Contracts;
using Microsoft.Aether.BlueBox.Storage;
using Microsoft.Aether.BlueBox.TokenServiceClient;
using Microsoft.Aether.BlueBox.WorkspaceResourcesClient;
using Microsoft.Aether.BlueBox.WorkspaceResourcesContracts;
using Microsoft.Aether.CloudCommon;
using Microsoft.Aether.CloudCommon.Worker;
using Microsoft.Aether.Clouds.CloudCommon;
using Microsoft.Aether.Clouds.CloudCommon.Cloudlets;
using Microsoft.Aether.Clouds.CloudCommon.JobQueueProcessing;
using Microsoft.Aether.DataContracts;
using Microsoft.Aether.DataContracts.CloudSystem;
using Microsoft.Aether.DataContracts.DataReferences;
using Microsoft.Aether.DataContracts.Entity;
using Microsoft.Aether.S2S.Common;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.RelInfra.Common.Exceptions;
using Microsoft.RelInfra.Extensions;
using Microsoft.RelInfra.Instrumentation;
using Microsoft.RelInfra.Instrumentation.Exceptions;
using Microsoft.RelInfra.Instrumentation.Logging;
using Microsoft.MachineLearning.Common.WebApi.Exceptions;
using Microsoft.MachineLearning.RunHistory.Contracts;
using Microsoft.Aether.BlueBox.RunHistoryClient;
using Microsoft.Aether.BackendCommon.Extensions;
using Microsoft.Aether.BackendCommon.Configuration;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Threading;
using System.Threading.Tasks;
using System.Text;

namespace Microsoft.Aether.DataBricksCloud.K8sService
{
    public class JobProcessor : JobProcessorBase<DataBricksJobMetadata>
    {
        private readonly IJobMetadataStorage<DataBricksJobMetadata> _jobStorage;
        private readonly IDataStoreClient _dataStoreClient;
        private readonly IDataBricksClientFactory _dataBricksClientFactory;
        private readonly IRunStateUpdater _runStateUpdater;
        private readonly IWorkspaceResourcesClient _workspaceResourcesClient;
        private readonly IModuleResourceManagerFactory _moduleResourceManagerFactory;
        private readonly ITokenServiceClient _tokenServiceClient;
        private readonly IDBFSClientFactory _dbfsClientFactory;
        private readonly IWorkDirectoryManager _workingDirectoryManager;
        private readonly string _executionDirectory;
        private readonly DataBricksCloudConfig _databricksCloudConfig;

        private readonly string _jobId;
        private readonly CounterManager _counterManager;

        private readonly string _secretScopeName = "amlscope";
        private bool _createdScope = false;

        private OriginClusterSparkSetting _originClusterSparkSetting;
        private ClusterDto _dynamicClusterDto;

        private JobLoggerInfo _jobLoggerInfo;
        private IJobDualLogger _dualLogger;
        private readonly IMemoryCache _workspaceUrlAndPATCache;
        private readonly IJobStdOutLoggerFactory _jobStdOutLoggerFactory;
        private readonly IJobStatusCache _jobStatusCache;
        private readonly ArtifactServiceRegistry _artifactServiceRegistry;
        private readonly IRunHistoryClient _runHistoryClient;
        private readonly IErrorClassifier _errorClassifier;

        class WorkspaceUrlAndPAT
        {
            internal string WorkspaceUrl { get; set; }
            internal string PAT { get; set; }
        }

        public JobProcessor(string jobId,
            IJobMetadataStorage<DataBricksJobMetadata> jobStorage,
            CounterManager counterManager,
            IDataBricksClientFactory dataBricksClientFactory,
            IDataStoreClient dataStoreClient,
            IRunStateUpdater runStateUpdater,
            IWorkspaceResourcesClient workspaceResourcesClient,
            string executionDirectory,
            IMemoryCache workspaceUrlAndPATCache,
            IS2STokenProvider s2STokenProvider,
            IModuleResourceManagerFactory moduleResourceManagerFactory,
            ITokenServiceClient tokenServiceClient,
            IDBFSClientFactory dbfsClientFactory,
            IWorkDirectoryManager workingDirectoryManager,
            DataBricksCloudConfig databricksCloudConfig,
            IJobStdOutLoggerFactory jobStdOutLoggerFactory,
            IJobStatusCache jobStatusCache,
            ArtifactServiceRegistry artifactServiceRegistry,
            IRunHistoryClient runHistoryClient,
            IErrorClassifier errorClassifier) : base(s2STokenProvider)
        {
            _jobId = jobId;
            _jobStorage = jobStorage;
            _counterManager = counterManager;
            _dataBricksClientFactory = dataBricksClientFactory;
            _dataStoreClient = dataStoreClient;
            _runStateUpdater = runStateUpdater;
            _workspaceResourcesClient = workspaceResourcesClient;
            _executionDirectory = executionDirectory;
            _workspaceUrlAndPATCache = workspaceUrlAndPATCache;
            _moduleResourceManagerFactory = moduleResourceManagerFactory;
            _tokenServiceClient = tokenServiceClient;
            _dbfsClientFactory = dbfsClientFactory;
            _workingDirectoryManager = workingDirectoryManager;
            _databricksCloudConfig = databricksCloudConfig;
            _jobStdOutLoggerFactory = jobStdOutLoggerFactory;
            _jobStatusCache = jobStatusCache;
            _artifactServiceRegistry = artifactServiceRegistry;
            _runHistoryClient = runHistoryClient;
            _errorClassifier = errorClassifier;
        }

        public override async Task<DataBricksJobMetadata> ProcessJobAsync(DataBricksJobMetadata job)
        {
            CommonLogger.LogEntityInfo(job.Id, "Starting processing job in status {0}", job.Status);

            _jobLoggerInfo = await _jobStdOutLoggerFactory.CreateJobLoggerAsync(jobId: job.Id,
                    exeJobLogSasUri: job.WritableLogUris?.ExecutionLogUri,
                    stdOutSasUri: job.WritableLogUris?.StdoutLogUri,
                    stdErrSasUri: job.WritableLogUris?.StderrLogUri,
                    workspaceIdentity: job.WorkspaceIdentity,
                    createdBy: job.CreatedBy);
            
            _dualLogger = new JobDualLogger(_jobLoggerInfo.Logger, _jobLoggerInfo.JobLogger);

            JobStatus initialStatus = job.Status;

            try
            {
                switch (job.Status)
                {
                    case JobStatus.Submitted:
                        await StartNewJobAsync(job);
                        break;

                    case JobStatus.Running:
                        await CheckJobStatusAsync(job);
                        break;

                    case JobStatus.Cancelling:
                        await CancelJobAsync(job);
                        await MarkJobAsCancelledAndCleanSecretsAsync(job);
                        break;

                    case JobStatus.Failed:
                    case JobStatus.Cancelled:
                        break;

                    default:
                        throw new InvalidOperationException($"Unexpected job status {job.Status}");
                }
            }
            catch (HttpRequestDetailException ex) when (RelInfra.Extensions.HttpClientExtensions.IsTooManyRequestsException(ex))
            {
                throw;
            }
            catch (Exception ex) when (!ex.IsRetriable())
            {
                ErrorResponse errorResponse = ErrorResponseCreator.CreateErrorResponse(_errorClassifier, ex.ToString(), RootErrorCode.ServiceError, job.ModuleInfo.CloudSystem);
                CommonLogger.LogEntityError(job.Id, $"Found ErrorCategoryInfo: {errorResponse.GetErrorCategoryInfo()}");
                await _runStateUpdater.UpdateRunErrorAsync(
                    job: job,
                    errorResponse: errorResponse,
                    logger: _dualLogger);
                job.ErrorResponse = errorResponse;
                _dualLogger.LogEntityError(job.Id, "Exception while processing job, error: {exception}", ex);
                await MarkJobAsFailedAndCleanSecretsAsync(job);
            }
            finally
            {
                try
                {
                    _workingDirectoryManager.DeleteDirectory(_executionDirectory);
                }
                catch (Exception ex)
                {
                    _dualLogger.LogEntityError(job.Id,
                                               "Exception while deleting execution directory {execution_directory}, error: {exception}",
                                               _executionDirectory, ex);
                }
            }

            await FlushLogsAsync(job.Id);
            job = await CommitJobAsync(job, initialStatus);

            return job;
        }

        private async Task<DataBricksJobMetadata> StartNewJobAsync(DataBricksJobMetadata job)
        {
            try
            {
                await InitalizeNewJobAsync(job);

                SubmitRunDto submitRunDto = StructuredInterfaceParser.ParseSubmitRunDtoFromStructuredInterface(job);
                string scriptName = StructuredInterfaceParser.ParseScriptNameFromStructuredInterface(job);

                if (!string.IsNullOrEmpty(submitRunDto.ExistingClusterId))
                {
                    await GetClusterIfNeededAsync(job, submitRunDto);
                }

                await SetupInputsAndOutputsAsync(job, submitRunDto);

                // if module has a specified script name (i.e doesn't already exist on ADB), download snapshot and upload to dbfs
                if (!string.IsNullOrEmpty(scriptName))
                {
                    _dualLogger.LogEntityInfo(job.Id, "Start uploading resources in snapshot to databricks cluster.");
                    await SetupDBFSAsync(job, scriptName);
                    _dualLogger.LogEntityInfo(job.Id, "Finish uploading resources to databricks cluster.");
                    SetupPythonScriptLocationParamAndEnvVar(job, submitRunDto);
                }

                TokenResult amlToken = await GetAmlToken(job);
                string experimentId = await GetRunhistoryExperimentIdAsync(job);

                string serviceEndPoint = await GetServiceEndpointAsync(job);

                SetupAMLIdentityParams(job, amlToken.Token, amlToken.ExpiryTimeUtc, submitRunDto, experimentId, serviceEndPoint);

                SetupEnvVariablesForRunContext(job, amlToken.Token, amlToken.ExpiryTimeUtc, submitRunDto, experimentId, serviceEndPoint);

                // Edit & Restart cluster if needed
                if (Parser.NeedEditExistingCluster(submitRunDto, _originClusterSparkSetting, _dynamicClusterDto))
                {
                    var clusterState = await GetClusterStateAsync(job, submitRunDto);
                    if (String.Compare(clusterState, DataBricksConstants.Terminated, StringComparison.OrdinalIgnoreCase) != 0
                         && String.Compare(clusterState, DataBricksConstants.Running, StringComparison.OrdinalIgnoreCase) != 0)
                    {
                        _dualLogger.LogEntityError(job.Id, $"Cannot edit Cluster {submitRunDto.ExistingClusterId} since it`s in state [{clusterState}]! Submission will be delayed.");
                        return job;
                    }

                    _dualLogger.LogEntityInfo(job.Id, $"Editing cluster {submitRunDto.ExistingClusterId} new attributes.");

                    try
                    {
                        await EditClusterAsync(job, submitRunDto);
                    }
                    catch (HttpRequestDetailException ex) when (ex.StatusCode == HttpStatusCode.BadRequest && ex.Message.Contains("INVALID_STATE"))
                    {
                        // Edit cluster API hit BadRequest response with INVALID_STATE error code.
                        // This reponse indicates cluster is not in state which could be edit.
                        // Postpone submission till cluster is ready.
                        // https://learn.microsoft.com/en-us/azure/databricks/dev-tools/api/latest/clusters#--edit
                        _dualLogger.LogEntityError(job.Id, $"Failed to edit Cluster {submitRunDto.ExistingClusterId}. Submission will be delayed.");
                        return job;
                    }

                    if(String.Compare(clusterState, DataBricksConstants.Terminated, StringComparison.OrdinalIgnoreCase) == 0)
                    {
                        _dualLogger.LogEntityInfo(job.Id, $"Cluster {submitRunDto.ExistingClusterId} is in TERMINATED state, manually starting to take new attributes into effect...");
                        await StartClusterAsync(job, submitRunDto);
                    }
                }

                await SubmitJobAsync(job, submitRunDto);

                _dualLogger.LogEntityInfo(job.Id, $"Submitted a job to {GetClusterType(submitRunDto)}, spark version {GetSparkVersion(submitRunDto)}");

                JobReportingLogger.LogComputeTarget(
                    logger: _dualLogger,
                    jobId: job.Id,
                    runId: job.RunId,
                    parentRunId: job.PipelineRunId ?? job.ExperimentId,
                    computeTarget: "DataBricks");

                CommonLogger.LogEntityInfo(job.Id, "Collect Databrick job submission info. "
                    + $"SubscriptionId: [{job.WorkspaceIdentity?.SubscriptionId}], "
                    + $"WorkspaceId: [{job.WorkspaceIdentity?.WorkspaceId}], "
                    + $"JobId: [{job.Id}], "
                    + $"RunId: [{job.RunId}], "
                    + $"TaskType: [{GetTaskType(submitRunDto)}], "
                    + $"ClusterType: [{GetClusterType(submitRunDto)}], "
                    + $"SparkVersion: [{GetSparkVersion(submitRunDto)}],"
                    + $"ClusterInitScript: [{IsClusterInitScriptUsed(submitRunDto)}].");

                await CheckSupportedRuntimeVersions(job, submitRunDto);
            }
            catch (Exception retriable) when (ShouldRetrySubmission(retriable))
            {
                CommonLogger.LogEntityWarning(job.Id, "Transient error has occured. Submission will be delayed. error: {exception}", retriable);
                return job;
            }
            catch (Exception unretriable)
            {
                if (unretriable is ServiceInvocationException)
                {
                    _dualLogger.LogEntityError(job.Id, "Failed to submit job because of error: {safe_exception_message}, stack_trace: {stack_trace}", unretriable.Message, unretriable.StackTrace);
                }
                else
                {
                    _dualLogger.LogEntityError(job.Id, "Failed to submit job because of error: {exception}", unretriable);
                }

                throw;
            }

            return job;
        }

        private async Task SubmitJobAsync(DataBricksJobMetadata job, SubmitRunDto submitRunDto)
        {
            WorkspaceUrlAndPAT workspaceUrlAndPAT = await GetWorkspaceUrlAndPATAsync(job);
            var client = _dataBricksClientFactory.GetClient(workspaceUrlAndPAT.WorkspaceUrl, workspaceUrlAndPAT.PAT);
            string runId = string.Empty;

            if (_dualLogger.JobExecutionLogger != null)
            {
                try
                {
                    string serializedRunDto = JsonConvert.SerializeObject(submitRunDto,
                        settings: new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });

                    _dualLogger.JobExecutionLogger.LogEntityInfo(job.Id, $"SubmitJobAsync - Submitting DataBricksCloud Job. "
                        + $"submitRunDto: {Convert.ToBase64String(Encoding.UTF8.GetBytes(serializedRunDto))}");
                }
                catch (Exception ex)
                {
                    CommonLogger.LogEntityWarning(job.Id, "Failed to log downstream API details due to error {exception}", ex);
                }
            }

            await TryDatabricksOperationAndRehydrateCacheIfNecessaryAsync(job,
                async (IDataBricksClient adbClient) => { 
                    runId = await adbClient.SubmitRunAsync(submitRunDto); 
                },
                client);

            job.DataBricksRunId = runId;

            MarkJobAsRunning(job);
        }

        private async Task InitalizeNewJobAsync(DataBricksJobMetadata job)
        {
            job.StdOutUploadLocation = _jobLoggerInfo.StdOutStreamPath;
            job.StdErrUploadLocation = _jobLoggerInfo.StdErrStreamPath;

            StructuredInterfaceParser.ParseStructuredInterface(job);

            WorkspaceUrlAndPAT WorkspaceUrlAndPAT = await GetWorkspaceUrlAndPATAsync(job);
            _dualLogger.LogEntityInfo(job.Id, $"Parsed interface. Will be submitting job to Azure Databricks {WorkspaceUrlAndPAT.WorkspaceUrl}");

            await UpdateRunHistoryComputeTargetAsync(job);
        }

        private async Task UpdateRunHistoryComputeTargetAsync(DataBricksJobMetadata job)
        {
            try
            {
                CommonLogger.LogEntityInfo(job.Id, "Updating compute target in run history");
                var createRunDto = new CreateRunDto
                {
                    RunId = job.RunId,
                    Target = job.ComputeName
                };
                await _runHistoryClient.UpdateRunAsync(
                    job.WorkspaceIdentity,
                    job.RunHistoryExperimentName,
                    job.RunId,
                    createRunDto,
                    job.CreatedBy);
            }
            catch (Exception exception)
            {
                CommonLogger.LogEntityWarning(job.Id, "Couldn't update compute target in run history. Error will be ignored. Error: {safe_exception_message}, stack_trace: {stack_trace}", exception.Message, exception.StackTrace);
            }
        }

        private async Task SetupInputsAndOutputsAsync(DataBricksJobMetadata job, SubmitRunDto submitRunDto)
        {
            bool hasInputs = job.ExecutionStructuredInterface.Inputs?.Any(inputpair => !inputpair.Key.SkipProcessing) ?? false;
            bool hasOutputs = job.ExecutionStructuredInterface.Outputs?.Any(outputpair => !outputpair.Key.SkipProcessing) ?? false;

            if ((hasInputs || hasOutputs) && submitRunDto.NewCluster != null && submitRunDto.NewCluster.SparkConf == null)
            {
                submitRunDto.NewCluster.SparkConf = new Dictionary<string, string>();
            }

            if ((hasInputs || hasOutputs) && submitRunDto.NotebookTask != null && submitRunDto.NotebookTask.BaseParameters == null)
            {
                submitRunDto.NotebookTask.BaseParameters = new Dictionary<string, string>();
            }

            int numOfSecrets = 1;

            // Set up sas or key for blob inputs
            if(hasInputs)
            {
                foreach (var input in job.ExecutionStructuredInterface.Inputs)
                {
                    StructuredInterfaceInput inputInfo = input.Key;
                    ModuleInputExecutionInfo inputExecutionInfo = input.Value;

                    if (inputExecutionInfo == null)
                    {
                        if (inputInfo.IsOptional)
                        {
                            //Skip null optional inputs
                            continue;
                        }
                        else
                        {
                            throw new ApplicationException($"input {inputInfo.Name} is not provided");
                        }
                    }

                    AzureBlobReference blobReference = inputExecutionInfo.DataLocation?.DataReference?.AzureBlobReference;
                    AzureDataLakeReference dataLakeReference = inputExecutionInfo.DataLocation?.DataReference?.AzureDataLakeReference;
                    DBFSReference dbfsReference = inputExecutionInfo.DataLocation?.DataReference?.DBFSReference;
                    AzureDataLakeGen2Reference dataLakeGen2Reference = inputExecutionInfo.DataLocation?.DataReference?.AzureDataLakeGen2Reference;

                    string dataStoreName = blobReference?.AmlDataStoreName ?? dataLakeReference?.AmlDataStoreName ?? dbfsReference?.AmlDataStoreName ?? dataLakeGen2Reference?.AmlDataStoreName;

                    if (string.IsNullOrEmpty(dataStoreName))
                    {
                        if (inputInfo.SkipProcessing)
                        {
                            // The Python SDK currently adds these for sequencing, ignore
                            continue;
                        }
                        _dualLogger.LogEntityError(job.Id, 
                                                   "The Azure Databricks cloud accepts only Datastore Azure blob, Azure Data Lake, Azure Data Lake Gen2 or DBFS inputs." +
                                                   "\nInvalid input: {user_file_path} id: {input_execution_info_data_id}",
                                                   inputExecutionInfo.Name, inputExecutionInfo.DataId);
                        throw new ArgumentException($"Databricks job input/output validation - Invalid input: {inputExecutionInfo.Name} id: {inputExecutionInfo.DataId}");
                    }

                    DataStoreDto dto = await _dataStoreClient.GetAsync(dataStoreName, job.WorkspaceIdentity, job.CreatedBy, CancellationToken.None);

                    if (blobReference != null)
                    {
                        await SetBlobSparkParametersAndSecretAsync(job, submitRunDto, blobReference, inputExecutionInfo.Name, dto, numOfSecrets);
                        AddParams(job, submitRunDto, inputExecutionInfo.Name, blobReference.Uri);
                    }
                    else if (dataLakeReference != null)
                    {

                        if (dto.AzureDataLakeSection.IsCertAuth)
                        {
                            _dualLogger.LogEntityError(job.Id,
                                                       "For Azure Data Lake DataStore inputs, only service principal auth is acceptable." +
                                                        "\nInvalid input: {user_file_path} id: {input_executionInfo_data_id}",
                                                       inputExecutionInfo.Name, inputExecutionInfo.DataId);
                            throw new ArgumentException($"Databricks job input/output validation - Invalid input: {inputExecutionInfo.Name} id: {inputExecutionInfo.DataId}");
                        }
                        await SetDataLakeSparkParametersAndSecretAsync(job, submitRunDto, dataLakeReference, inputExecutionInfo.Name, dto, numOfSecrets);
                        AddParams(job, submitRunDto, inputExecutionInfo.Name, dataLakeReference.GetUri());
                    }
                    else if(dataLakeGen2Reference != null)
                    {
                        await SetDataLakeGen2SparkParametersAndSecretAsync(job, submitRunDto, dataLakeGen2Reference, inputExecutionInfo.Name, dto, numOfSecrets);
                        AddParams(job, submitRunDto, inputExecutionInfo.Name, dataLakeGen2Reference.GetUri());
                    }
                    else if (dbfsReference != null)
                    {
                        AddIOToNotebookParams(submitRunDto, inputExecutionInfo.Name, dbfsReference.GetFullPath());
                        AddParams(job, submitRunDto, inputExecutionInfo.Name, dbfsReference.GetFullPath());
                    }
                    else
                    {
                        _dualLogger.LogEntityError(job.Id, $"Unkown input datatype {inputExecutionInfo.DataLocation?.DataStoreName}");
                        throw new ArgumentException($"Databricks job input/output validation - Invalid input: {inputExecutionInfo.Name} id: {inputExecutionInfo.DataId}");
                    }

                    numOfSecrets++;
                }
            }

            if (hasOutputs)
            {
                foreach (var output in job.ExecutionStructuredInterface.Outputs)
                {
                    StructuredInterfaceOutput outputInfo = output.Key;
                    ModuleOutputExecutionInfo outputExecutionInfo = output.Value;

                    if (string.IsNullOrEmpty(outputInfo.DataStoreName))
                    {
                        if(outputInfo.SkipProcessing)
                        {
                            // The Python SDK currently adds these for sequencing, ignore
                            continue;
                        }
                        else
                        {
                            _dualLogger.LogEntityError(job.Id,
                                                       "The Azure Databricks cloud creates only Datastore Azure blob or Azure Data Lake outputs." +
                                                       "\nInvalid output: {user_directory}",
                                                       output.Key.Name);
                            throw new ArgumentException($"Databricks job input/output validation - Invalid output: { output.Key.Name }");
                        }
                    }

                    DataStoreDto dto = await _dataStoreClient.GetAsync(outputInfo.DataStoreName, job.WorkspaceIdentity, job.CreatedBy, CancellationToken.None);

                    if (dto.DataStoreType != DataStoreType.AzureBlob &&
                        dto.DataStoreType != DataStoreType.AzureDataLake &&
                        dto.DataStoreType != DataStoreType.DBFS &&
                        dto.DataStoreType != DataStoreType.AzureDataLakeGen2)
                    {
                        _dualLogger.LogEntityError(job.Id,
                                                   "The Azure Databricks cloud accepts only Datastore Azure blob, Azure Data Lake, Azure Data Lake Gen2 or DBFS outputs." +
                                                   "\nInvalid output: {user_directory}",
                                                   output.Key.Name);
                        throw new ArgumentException($"Databricks job input/output validation - Invalid output: {output.Key.Name}");
                    }

                    outputExecutionInfo.DataLocation = OutputDataLocationUtils.GenerateOutputDataLocation(job, outputInfo.Name, dto);

                    if (dto.DataStoreType == DataStoreType.AzureBlob)
                    {
                        await SetBlobSparkParametersAndSecretAsync(job, submitRunDto, outputExecutionInfo.DataLocation.DataReference.AzureBlobReference, output.Key.Name, dto, numOfSecrets);
                        AddParams(job, submitRunDto, output.Key.Name, outputExecutionInfo.DataLocation.DataReference.AzureBlobReference.Uri);
                    }
                    else if (dto.DataStoreType == DataStoreType.AzureDataLake)
                    {
                        if (dto.AzureDataLakeSection.IsCertAuth)
                        {
                            _dualLogger.LogEntityError(job.Id,
                                                       "For Azure Data Lake DataStore outputs, only service principal auth is acceptable." +
                                                       "\nInvalid output: {user_directory}",
                                                       output.Key.Name);
                            throw new ArgumentException($"Databricks job input/output validation - Invalid output: {output.Key.Name}");
                        }
                        await SetDataLakeSparkParametersAndSecretAsync(job, submitRunDto, outputExecutionInfo.DataLocation.DataReference.AzureDataLakeReference, output.Key.Name, dto, numOfSecrets);
                        AddParams(job, submitRunDto, output.Key.Name, outputExecutionInfo.DataLocation.DataReference.AzureDataLakeReference.GetUri());
                    }
                    else if (dto.DataStoreType == DataStoreType.DBFS)
                    {
                        AddIOToNotebookParams(submitRunDto, output.Key.Name, outputExecutionInfo.DataLocation.DataReference.DBFSReference.GetFullPath());
                        AddParams(job, submitRunDto, output.Key.Name, outputExecutionInfo.DataLocation.DataReference.DBFSReference.GetFullPath());
                    }
                    else if(dto.DataStoreType == DataStoreType.AzureDataLakeGen2)
                    {
                        await SetDataLakeGen2SparkParametersAndSecretAsync(job, submitRunDto, outputExecutionInfo.DataLocation.DataReference.AzureDataLakeGen2Reference, output.Key.Name, dto, numOfSecrets);
                        AddParams(job, submitRunDto, output.Key.Name, outputExecutionInfo.DataLocation.DataReference.AzureDataLakeGen2Reference.GetUri());
                    }
                    else
                    {
                        _dualLogger.LogEntityError(job.Id, $"Unknow output type {dto.DataStoreType}");
                        throw new ArgumentException($"Databricks job input/output validation - Invalid output: {output.Key.Name}");
                    }

                    numOfSecrets++;
                }
            }
        }

        private async Task SetBlobSparkParametersAndSecretAsync(DataBricksJobMetadata job, SubmitRunDto submitRunDto, AzureBlobReference blobReference, string paramName, DataStoreDto dto, int secretNum)
        {
            await SetBlobSparkConfOnly(job, submitRunDto, blobReference, dto);

            // Set up parameters and secrets for mounting blob
            // using a number so we won't constrain input/output names due to db secret name length restrictions
            string secretName = job.RunId + "_" + secretNum;

            await AddDatabricksSecretAsync(job,
                secretName,
                dto.AzureStorageSection.IsSas ? dto.AzureStorageSection.SasToken : dto.AzureStorageSection.AccountKey);

            if (submitRunDto.NotebookTask != null)
            {
                // Add as parameter
                AddParameterOnDict(submitRunDto.NotebookTask.BaseParameters, paramName, blobReference.Uri);

                AddParameterOnDict(submitRunDto.NotebookTask.BaseParameters, paramName + "_blob_secretname", secretName);
                AddParameterOnDict(submitRunDto.NotebookTask.BaseParameters, paramName + "_blob_config",
                    dto.AzureStorageSection.IsSas ? $"fs.azure.sas.{blobReference.Container}.{blobReference.Account}.blob.core{Env.ResourceId()}"
                    : $"fs.azure.account.key.{blobReference.Account}.blob.core{Env.ResourceId()}");
            }
        }

        private async Task SetBlobSparkConfOnly(DataBricksJobMetadata job, SubmitRunDto submitRunDto, AzureBlobReference blobReference, DataStoreDto dto)
        {
            if (!string.IsNullOrEmpty(submitRunDto.ExistingClusterId))
            {
                await GetClusterIfNeededAsync(job, submitRunDto);
            }

            var sparkConf = string.IsNullOrEmpty(submitRunDto.ExistingClusterId) ? submitRunDto.NewCluster.SparkConf : _dynamicClusterDto.SparkConf;
            if(!sparkConf.ContainsKey($"fs.azure.account.key.{blobReference.Account}.blob.core{Env.ResourceId()}"))
            {
                if (!string.IsNullOrEmpty(submitRunDto.ExistingClusterId) && !job.PermitClusterRestart)
                {
                    var msg = $"The cluster is not allowed to restart but needs a restart to configure it's spark configuration"
                             +$"in order to access Azure Blob account {blobReference.Account}. Try setting the parameter {DataBricksConstants.RestartParam}";
                    throw new BadRequestException(msg).Format();
                }

                // Setup azure blob access
                if (dto.AzureStorageSection.IsSas)
                {
                    if (!sparkConf.ContainsKey($"fs.azure.sas.{blobReference.Container}.{blobReference.Account}.blob.core{Env.ResourceId()}"))
                    {
                        sparkConf.Add(
                            $"fs.azure.sas.{blobReference.Container}.{blobReference.Account}.blob.core{Env.ResourceId()}",
                            dto.AzureStorageSection.SasToken);

                        // For RDD API
                        sparkConf.Add(
                            $"spark.hadoop.fs.azure.sas.{blobReference.Container}.{blobReference.Account}.blob.core{Env.ResourceId()}",
                            dto.AzureStorageSection.SasToken);
                    }
                }
                else
                {
                    // remove all sas entries for this account since we have the account key
                    var toRemove = sparkConf.Where(pair =>
                        pair.Key.EndsWith($"{blobReference.Account}.blob.core{Env.ResourceId()}")).Select(pair => pair.Key)
                         .ToList();

                    foreach (var key in toRemove)
                    {
                        sparkConf.Remove(key);
                    }

                    sparkConf.Add(
                        $"fs.azure.account.key.{blobReference.Account}.blob.core{Env.ResourceId()}",
                        dto.AzureStorageSection.AccountKey);

                    // For RDD API
                    sparkConf.Add(
                        $"spark.hadoop.fs.azure.account.key.{blobReference.Account}.blob.core{Env.ResourceId()}",
                        dto.AzureStorageSection.AccountKey);
                }
            }
        }

        private string GetTaskType(SubmitRunDto submitRunDto)
        {
            string taskType = "Unknown";
            if (submitRunDto.NotebookTask != null) taskType = "Notebook";
            if (submitRunDto.SparkPythonTask != null) taskType = "PythonScript";
            if (submitRunDto.SparkJarTask != null) taskType = "SparkJar";

            return taskType;
        }

        private string GetClusterType(SubmitRunDto submitRunDto)
        {
            return submitRunDto.NewCluster != null ? "new cluster" : "existing cluster";
        }

        private string GetSparkVersion(SubmitRunDto submitRunDto)
        {
            return submitRunDto?.NewCluster?.SparkVersion
              ?? _dynamicClusterDto?.SparkVersion
              ?? "Unknown";
        }

        private bool IsClusterInitScriptUsed(SubmitRunDto submitRunDto)
        {
            return submitRunDto?.NewCluster?.DbfsInitScripts?.Count > 0;
        }

        private void AddParams(DataBricksJobMetadata job, SubmitRunDto submitRunDto, string paramName, string paramValue)
        {
            paramValue = paramValue ?? string.Empty;
            _dualLogger.LogEntityError(job.Id, "Adding {user_parameter_name} as parameter with value {user_parameter_value}", paramName, paramValue);

            if (submitRunDto.SparkPythonTask != null)
            {
                submitRunDto.SparkPythonTask.Parameters.Add("--" + paramName);
                submitRunDto.SparkPythonTask.Parameters.Add(paramValue);
            }
            else if (submitRunDto.SparkJarTask != null)
            {
                submitRunDto.SparkJarTask.Parameters.Add("--" + paramName);
                submitRunDto.SparkJarTask.Parameters.Add(paramValue);
            }
            else if (submitRunDto.NotebookTask != null)
            {
                AddParameterOnDict(submitRunDto.NotebookTask.BaseParameters, "--" + paramName, paramValue);
            }
        }

        private void AddIOToNotebookParams(SubmitRunDto submitRunDto, string paramName, string paramUri)
        {
            if (submitRunDto.NotebookTask != null)
            {
                // Add as parameter
                AddParameterOnDict(submitRunDto.NotebookTask.BaseParameters, paramName, paramUri);
            }
        }

        private async Task SetDataLakeSparkParametersAndSecretAsync(DataBricksJobMetadata job, SubmitRunDto submitRunDto, AzureDataLakeReference dataLakeReference, string paramName, DataStoreDto dto, int secretNum)
        {
            // check if sp is already set up
            await SetSparkConfCommonAsync(job, submitRunDto, dto, (Dictionary<string, string> sparkConf) =>
            {
                sparkConf.Add(
                   "dfs.adls.oauth2.access.token.provider.type",
                   "ClientCredential");
                sparkConf.Add(
                    "dfs.adls.oauth2.client.id",
                    $"{dto.AzureDataLakeSection.ClientId}");
                sparkConf.Add(
                    "dfs.adls.oauth2.credential",
                    $"{dto.AzureDataLakeSection.ClientSecret}");
                sparkConf.Add(
                    "dfs.adls.oauth2.refresh.url",
                    $"{_databricksCloudConfig.Authority}/{dto.AzureDataLakeSection.TenantId}/oauth2/token");

                // For RDD API
                sparkConf.Add(
                    "spark.hadoop.dfs.adls.oauth2.access.token.provider.type",
                    "ClientCredential");
                sparkConf.Add(
                    "spark.hadoop.dfs.adls.oauth2.client.id",
                    $"{dto.AzureDataLakeSection.ClientId}");
                sparkConf.Add(
                    "spark.hadoop.dfs.adls.oauth2.credential",
                    $"{dto.AzureDataLakeSection.ClientSecret}");
                sparkConf.Add(
                    "spark.hadoop.dfs.adls.oauth2.refresh.url",
                    $"{_databricksCloudConfig.Authority}/{dto.AzureDataLakeSection.TenantId}/oauth2/token");
            });

            // Set up parameters and secrets for mounting adls gen1
            // using a number so we won't constrain input/output names due to db secret name length restrictions
            string secretName = job.RunId + "_" + secretNum;

            await AddDatabricksSecretAsync(job,
                secretName,
                dto.AzureDataLakeSection.ClientSecret);

            if (submitRunDto.NotebookTask != null)
            {
                // Add as parameter
                AddParameterOnDict(submitRunDto.NotebookTask.BaseParameters, paramName, dataLakeReference.GetUri());

                AddParameterOnDict(submitRunDto.NotebookTask.BaseParameters, paramName + "_adls_secretname", secretName);
                AddParameterOnDict(submitRunDto.NotebookTask.BaseParameters, paramName + "_adls_clientid",
                    dto.AzureDataLakeSection.ClientId.ToString());

                AddParameterOnDict(submitRunDto.NotebookTask.BaseParameters, paramName + "_adls_refresh_url",
                    $"{_databricksCloudConfig.Authority}/{dto.AzureDataLakeSection.TenantId}/oauth2/token");
            }
        }

        private async Task SetDataLakeGen2SparkParametersAndSecretAsync(DataBricksJobMetadata job, SubmitRunDto submitRunDto, AzureDataLakeGen2Reference dataLakeGen2Reference, string paramName, DataStoreDto dto, int secretNum)
        {
            await SetSparkConfCommonAsync(job, submitRunDto, dto, (Dictionary<string, string> sparkConf) =>
            {
                sparkConf.Add("fs.azure.account.auth.type", "OAuth");
                sparkConf.Add("fs.azure.account.oauth.provider.type", "org.apache.hadoop.fs.azurebfs.oauth2.ClientCredsTokenProvider");
                sparkConf.Add("fs.azure.account.oauth2.client.id", $"{dto.AzureStorageSection.ClientCredentials.ClientId}");
                sparkConf.Add("fs.azure.account.oauth2.client.secret", $"{dto.AzureStorageSection.ClientCredentials.ClientSecret}");
                sparkConf.Add("fs.azure.account.oauth2.client.endpoint", $"{_databricksCloudConfig.Authority}/{dto.AzureStorageSection.ClientCredentials.TenantId}/oauth2/token");

                // For RDD API
                var storageAccount = dataLakeGen2Reference.Account;
                if (!sparkConf.ContainsKey($"spark.hadoop.fs.azure.account.oauth2.client.id.{storageAccount}.dfs.core{Env.ResourceId()}"))
                {
                    sparkConf.Add($"spark.hadoop.fs.azure.account.auth.type.{storageAccount}.dfs.core{Env.ResourceId()}", "OAuth");
                    sparkConf.Add(
                        $"spark.hadoop.fs.azure.account.oauth.provider.type.{storageAccount}.dfs.core{Env.ResourceId()}",
                        $"org.apache.hadoop.fs.azurebfs.oauth2.ClientCredsTokenProvider");
                    sparkConf.Add(
                        $"spark.hadoop.fs.azure.account.oauth2.client.id.{storageAccount}.dfs.core{Env.ResourceId()}",
                        $"{dto.AzureStorageSection.ClientCredentials.ClientId}");
                    sparkConf.Add(
                        $"spark.hadoop.fs.azure.account.oauth2.client.secret.{storageAccount}.dfs.core{Env.ResourceId()}",
                        $"{dto.AzureStorageSection.ClientCredentials.ClientSecret}");
                    sparkConf.Add(
                        $"spark.hadoop.fs.azure.account.oauth2.client.endpoint.{storageAccount}.dfs.core{Env.ResourceId()}",
                        $"{_databricksCloudConfig.Authority}/{dto.AzureStorageSection.ClientCredentials.TenantId}/oauth2/token");
                }
            });

            // Set up parameters and secrets for mounting adls gen2
            // using a number so we won't constrain input/output names due to db secret name length restrictions
            string secretName = job.RunId + "_" + secretNum;

            await AddDatabricksSecretAsync(job,
                secretName,
                dto.AzureStorageSection.ClientCredentials.ClientSecret);

            if (submitRunDto.NotebookTask != null)
            {
                // Add as parameter
                AddParameterOnDict(submitRunDto.NotebookTask.BaseParameters, paramName, dataLakeGen2Reference.GetUri());

                AddParameterOnDict(submitRunDto.NotebookTask.BaseParameters, paramName + "_adlgen2_secretname", secretName);
                AddParameterOnDict(submitRunDto.NotebookTask.BaseParameters, paramName + "_adlgen2_clientid",
                    dto.AzureStorageSection.ClientCredentials.ClientId.ToString());

                AddParameterOnDict(submitRunDto.NotebookTask.BaseParameters, paramName + "_adlgen2_refresh_url",
                    $"{_databricksCloudConfig.Authority}/{dto.AzureStorageSection.ClientCredentials.TenantId}/oauth2/token");
            }
        }

        private void AddParameterOnDict(Dictionary<string, string> paraDict, string key, string value)
        {
            if (!paraDict.TryAdd(key, value))
            {
                throw new ValidationDetailsException($"Parameter [{key}] already exists in parameter dictionary.");
            }
        }

        private async Task SetSparkConfCommonAsync(DataBricksJobMetadata job, SubmitRunDto submitRunDto, DataStoreDto dto, Action<Dictionary<string, string>> addSparkConfFunc)
        {
            if (!string.IsNullOrEmpty(submitRunDto.ExistingClusterId))
            {
                await GetClusterIfNeededAsync(job, submitRunDto);
            }

            var sparkConf = string.IsNullOrEmpty(submitRunDto.ExistingClusterId) ? submitRunDto.NewCluster.SparkConf : _dynamicClusterDto.SparkConf;
            var clientIdKey = GetSparkConfClientIdKey(dto.DataStoreType);
            if (!sparkConf.ContainsKey(clientIdKey))
            {
                if (!string.IsNullOrEmpty(submitRunDto.ExistingClusterId) && !job.PermitClusterRestart)
                {
                    var msg = $"The cluster is not allowed to restart but needs a restart to configure it's spark configuration"
                             + $"in order to access {dto.DataStoreType}. Try setting the parameter {DataBricksConstants.RestartParam}";
                    throw new BadRequestException(msg).Format();
                }

                addSparkConfFunc(sparkConf);
            }
            else
            {
                // Can only set up one sp for adls/gen2 auth
                var clientId = sparkConf[clientIdKey];
                var existingClientId = dto.DataStoreType == DataStoreType.AzureDataLake ? dto.AzureDataLakeSection?.ClientId : dto.AzureStorageSection?.ClientCredentials?.ClientId;

                if (existingClientId != null && !clientId.Equals($"{existingClientId}", StringComparison.InvariantCultureIgnoreCase))
                {
                    throw new ArgumentException($"{dto.DataStoreType} DataStore inputs and outputs must use the same auth service principal");
                }
            }

            string GetSparkConfClientIdKey(DataStoreType dataStoreType)
            {
                switch(dataStoreType)
                {
                    case DataStoreType.AzureDataLake:
                        return "dfs.adls.oauth2.client.id";
                    case DataStoreType.AzureDataLakeGen2:
                        return "fs.azure.account.oauth2.client.id";
                    default:
                        throw new SystemException($"{nameof(SetSparkConfCommonAsync)} only support Azure Data Lake or Azure Data Lake Gen2 type!");
                }
            }
        }

        private async Task AddDatabricksSecretAsync(DataBricksJobMetadata job, string secretName, string secretValue)
        {
            if (string.IsNullOrEmpty(secretValue))
            {
                var msg = $"Encounter an empty databricks secret";
                throw new BadRequestException(msg).Format();
            }

            WorkspaceUrlAndPAT WorkspaceUrlAndPAT = await GetWorkspaceUrlAndPATAsync(job);
            var client = _dataBricksClientFactory.GetClient(WorkspaceUrlAndPAT.WorkspaceUrl, WorkspaceUrlAndPAT.PAT);

            if(!_createdScope)
            {
                _createdScope = await CreateSecretScopeAsync(job);
            }

            await TryDatabricksOperationAndRehydrateCacheIfNecessaryAsync(job,
                async (IDataBricksClient adbClient) => {
                    await adbClient.CreateSecretAsync(new SecretDto
                    {
                        Scope = _secretScopeName,
                        Key = secretName,
                        StringValue = secretValue
                    });
                },
                client);
        }

        private async Task<bool> CreateSecretScopeAsync(DataBricksJobMetadata job)
        {
            WorkspaceUrlAndPAT WorkspaceUrlAndPAT = await GetWorkspaceUrlAndPATAsync(job);
            var client = _dataBricksClientFactory.GetClient(WorkspaceUrlAndPAT.WorkspaceUrl, WorkspaceUrlAndPAT.PAT);

            try
            {
                await TryDatabricksOperationAndRehydrateCacheIfNecessaryAsync(job,
                    async (IDataBricksClient adbClient) => {
                        await adbClient.CreateSecretScopeAsync(new ScopeDto
                        {
                            Scope = _secretScopeName,
                            // This means available to all users in the databricks workspace
                            // Secret access control is only available for premium workspaces
                            InitialManagePrincipal = "users"
                        });
                    },
                    client);

                return true;
            }
            catch (Exception ex)
            {
                if(ex.Message.Contains("RESOURCE_ALREADY_EXISTS"))
                {
                    return true;
                }
                throw;
            }
        }

        private async Task<TokenResult> GetAmlToken(DataBricksJobMetadata job)
        {
            try
            {
                return await _tokenServiceClient.CreateRunTokenAndReturnExpiryAsync(runId: job.RunId,
                    amlWorkspaceIdentity: job.WorkspaceIdentity,
                    createdBy: job.CreatedBy);
            }
            catch (ServiceInvocationException ex)
            {
                CommonLogger.LogEntityError(job.Id, "Fail to create run token and return expiry because of error: {safe_exception_message}, stack trace: {stack_trace}", ex.Message, ex.StackTrace);
                throw;
            }
        }

        private async Task<string> GetRunhistoryExperimentIdAsync(DataBricksJobMetadata job)
        {
            try
            {
                var runDto = await _runHistoryClient.GetRunAsync(
                    job.WorkspaceIdentity,
                    job.RunHistoryExperimentName,
                    job.RunId,
                    job.CreatedBy);

                return runDto.ExperimentId;
            }
            catch(ServiceInvocationException ex)
            {
                CommonLogger.LogEntityError(job.Id, "Fail to get run dto from RH because of error: {safe_exception_message}, stack trace: {stack_trace}", ex.Message, ex.StackTrace);
            }

            // Return empty to skip setting it to be environment variables, the side effect is just a warning error of RunContext
            return string.Empty;
        }

        private void SetupAMLIdentityParams(DataBricksJobMetadata job, string amlToken, DateTimeOffset amlTokenExpiry, SubmitRunDto submitRunDto, string experimentId, string serviceEndPoint)
        {
            AddParams(job: job, submitRunDto: submitRunDto, paramName: "AZUREML_RUN_TOKEN", paramValue: amlToken);
            AddParams(job: job, submitRunDto: submitRunDto, paramName: "AZUREML_RUN_TOKEN_EXPIRY", paramValue: amlTokenExpiry.ToUnixTimeSeconds().ToString());
            AddParams(job: job, submitRunDto: submitRunDto, paramName: "AZUREML_RUN_ID", paramValue: job.RunId);
            AddParams(job: job, submitRunDto: submitRunDto, paramName: "AZUREML_ARM_SUBSCRIPTION", paramValue: job.WorkspaceIdentity?.SubscriptionId);
            AddParams(job: job, submitRunDto: submitRunDto, paramName: "AZUREML_ARM_RESOURCEGROUP", paramValue: job.WorkspaceIdentity?.ResourceGroupName);
            AddParams(job: job, submitRunDto: submitRunDto, paramName: "AZUREML_ARM_WORKSPACE_NAME", paramValue: job.WorkspaceIdentity?.WorkspaceName);
            AddParams(job: job, submitRunDto: submitRunDto, paramName: "AZUREML_ARM_PROJECT_NAME", paramValue: job.RunHistoryExperimentName);
            AddParams(job: job, submitRunDto: submitRunDto, paramName: "AZUREML_SERVICE_ENDPOINT", paramValue: serviceEndPoint);
            AddParams(job: job, submitRunDto: submitRunDto, paramName: "AZUREML_WORKSPACE_ID", paramValue: job.WorkspaceIdentity?.WorkspaceId);
            if (!string.IsNullOrEmpty(experimentId))
            {
                AddParams(job: job, submitRunDto: submitRunDto, paramName: "AZUREML_EXPERIMENT_ID", paramValue: experimentId);
            }
        }

        private async Task<string> GetServiceEndpointAsync(DataBricksJobMetadata job)
        {
            string serviceEndpoint = _databricksCloudConfig.AmlServicesEndpoint;
            try
            {
                WorkspaceDto workspace = await _workspaceResourcesClient.GetWorkspaceAsync(workspace: job.WorkspaceIdentity, createdBy: job.CreatedBy, cancellationToken: CancellationToken.None);

                if (long.TryParse(workspace?.Properties?.PrivateLinkCount, out long workspacePrivateLinkCount) &&
                    workspacePrivateLinkCount > 0)
                {
                    CommonLogger.LogEntityInfo(job.Id, "Private link is enabled for this workspace");
                    Uri serviceUri = new Uri(serviceEndpoint);
                    // Service endpoint for workspace in pvt link: https://<ws_id>.workspace.<host_domain>
                    serviceEndpoint = new UriBuilder(serviceUri.Scheme, job.WorkspaceIdentity?.WorkspaceId + ".workspace." + serviceUri.Host).ToString().TrimEnd('/');
                }
            }
            catch (ServiceInvocationException ex)
            {
                CommonLogger.LogEntityError(job.Id, "Failed to get service endpoint and returning default because of error: {safe_exception_message}, stack trace: {stack_trace}", ex.Message, ex.StackTrace);
            }

            CommonLogger.LogEntityInfo(job.Id, "Service endpoint: {service_endpoint}", serviceEndpoint);
            return serviceEndpoint;
        }

        private void SetupEnvVariablesForRunContext(DataBricksJobMetadata job, string amlToken, DateTimeOffset amlTokenExpiry, SubmitRunDto submitRunDto, string experimentId, string serviceEndPoint)
        {
            if (submitRunDto.NewCluster != null)
            {
                if (submitRunDto.NewCluster.SparkEnvVars == null)
                {
                    submitRunDto.NewCluster.SparkEnvVars = new Dictionary<string, string>();
                }

                submitRunDto.NewCluster.SparkEnvVars.Add("AZUREML_ARM_SUBSCRIPTION", job.WorkspaceIdentity?.SubscriptionId);
                submitRunDto.NewCluster.SparkEnvVars.Add("AZUREML_RUN_ID", job.RunId);
                submitRunDto.NewCluster.SparkEnvVars.Add("AZUREML_ARM_RESOURCEGROUP", job.WorkspaceIdentity?.ResourceGroupName);
                submitRunDto.NewCluster.SparkEnvVars.Add("AZUREML_ARM_WORKSPACE_NAME", job.WorkspaceIdentity?.WorkspaceName);
                submitRunDto.NewCluster.SparkEnvVars.Add("AZUREML_ARM_PROJECT_NAME", job.RunHistoryExperimentName);
                submitRunDto.NewCluster.SparkEnvVars.Add("AZUREML_RUN_TOKEN", amlToken);
                submitRunDto.NewCluster.SparkEnvVars.Add("AZUREML_RUN_TOKEN_EXPIRY", amlTokenExpiry.ToUnixTimeSeconds().ToString());
                submitRunDto.NewCluster.SparkEnvVars.Add("AZUREML_SERVICE_ENDPOINT", serviceEndPoint);
                submitRunDto.NewCluster.SparkEnvVars.Add("AZUREML_WORKSPACE_ID", job.WorkspaceIdentity?.WorkspaceId);
                if (!string.IsNullOrEmpty(experimentId))
                {
                    submitRunDto.NewCluster.SparkEnvVars.Add("AZUREML_EXPERIMENT_ID", experimentId);
                }
            }
        }

        private void SetupPythonScriptLocationParamAndEnvVar(DataBricksJobMetadata job, SubmitRunDto submitRunDto)
        {
            string directoryName = $"{job.RunId}";
            submitRunDto.SparkPythonTask.Parameters.Add("--AZUREML_SCRIPT_DIRECTORY_NAME");
            submitRunDto.SparkPythonTask.Parameters.Add(directoryName);

            if (submitRunDto.NewCluster != null)
            {
                if (submitRunDto.NewCluster.SparkEnvVars == null)
                {
                    submitRunDto.NewCluster.SparkEnvVars = new Dictionary<string, string>();
                }

                submitRunDto.NewCluster.SparkEnvVars.Add("AZUREML_SCRIPT_DIRECTORY_NAME", directoryName);
            }
        }

        private async Task SetupDBFSAsync(DataBricksJobMetadata job, string scriptName)
        {
            // TODO lisun remove datacontainerId fallback to RunId, this is only used for existing running jobs during deployment
            var moduleResourceManger = _moduleResourceManagerFactory.Create(jobId: _jobId, dataContainerId: job.DataContainerId ?? job.RunId,
                executionDirectory: _executionDirectory,
                workspaceIdentity: job.WorkspaceIdentity,
                createdBy: job.CreatedBy);

            string moduleDirPath = await moduleResourceManger.GetModuleAsync(setupDirectory: _executionDirectory,
                moduleExecutionInfo: job.ModuleInfo,
                token: CancellationToken.None);

            // validate specified script actually exists in the snapshot dir
            if (!File.Exists(Path.Combine(moduleDirPath, scriptName)))
            {
                throw new ArgumentException($"specified script {scriptName} does not exist in snapshot {job.ModuleInfo.DataLocation.StorageId}");
            }

            WorkspaceUrlAndPAT WorkspaceUrlAndPAT = await GetWorkspaceUrlAndPATAsync(job);
            bool continueOnDFSFailure = false;
            var dbfsClient = _dbfsClientFactory.GetClient(job.Id, WorkspaceUrlAndPAT.WorkspaceUrl, WorkspaceUrlAndPAT.PAT, _dualLogger, continueOnFailure: continueOnDFSFailure);

            await TryDBFSOperationAndRehydrateCacheIfNecessaryAsync(job,
                async (IDBFSClient client) => {
                    await client.CopyDirectoryAsync(sourcePath: moduleDirPath, targetDbfsPath: $"dbfs:/{job.RunId}", overwrite: true);
                },
                dbfsClient,
                continueOnDFSFailure);
        }

        private async Task CleanJobDatabricksArtifacts(DataBricksJobMetadata job)
        {
            await CleanJobDBFSAsync(job);
            await CleanJobSecretsAsync(job);
        }

        private async Task CleanJobDBFSAsync(DataBricksJobMetadata job)
        {
            // having a script name implies we had to upload the script, so we clean it here
            if(!string.IsNullOrEmpty(StructuredInterfaceParser.ParseScriptNameFromStructuredInterface(job)))
            {
                try
                {
                    WorkspaceUrlAndPAT WorkspaceUrlAndPAT = await GetWorkspaceUrlAndPATAsync(job);
                    bool continueOnDFSFailure = true;
                    var dbfsClient = _dbfsClientFactory.GetClient(job.Id, WorkspaceUrlAndPAT.WorkspaceUrl, WorkspaceUrlAndPAT.PAT, _dualLogger, continueOnFailure: continueOnDFSFailure);

                    await TryDBFSOperationAndRehydrateCacheIfNecessaryAsync(job,
                        async (IDBFSClient client) => {
                            await client.DeleteDirectoryAsync($"dbfs:/{job.RunId}", recursive: true);
                        },
                        dbfsClient,
                        continueOnDFSFailure);
                }
                catch (Exception ex)
                {
                    _dualLogger.LogEntityError(job.Id, "failed to clean up DBFS directory: {run_id}, due to error: {exception}", job.RunId, ex);
                }
            }
        }

        private async Task CleanJobSecretsAsync(DataBricksJobMetadata job)
        {
            // Cleaning up all of the generated Databricks secrets for inputs and outputs mounting
            // They were named based on the runId and a number suffix from 1 to number of inputs and outputs
            int inputsAndOutputs = (job.ExecutionStructuredInterface.Inputs?.Where(inputpair => !inputpair.Key.SkipProcessing).Count() ?? 0) +
                (job.ExecutionStructuredInterface.Outputs?.Where(outputpair => !outputpair.Key.SkipProcessing).Count() ?? 0);

            if(inputsAndOutputs > 0)
            {
                try
                {
                    WorkspaceUrlAndPAT WorkspaceUrlAndPAT = await GetWorkspaceUrlAndPATAsync(job);
                    var client = _dataBricksClientFactory.GetClient(WorkspaceUrlAndPAT.WorkspaceUrl, WorkspaceUrlAndPAT.PAT);

                    for (int i = 1; i <= (inputsAndOutputs); i++)
                    {
                        string secretName = $"{job.RunId}_{i}";
                        try
                        {
                            await TryDatabricksOperationAndRehydrateCacheIfNecessaryAsync(job,
                                async (IDataBricksClient adbClient) => {
                                    await adbClient.DeleteSecretAsync(new SecretDto
                                    {
                                        Scope = _secretScopeName,
                                        Key = secretName
                                    });
                                },
                                client);
                        }
                        catch (Exception ex)
                        {
                            if (!ex.Message.Contains("RESOURCE_DOES_NOT_EXIST"))
                            {
                                _dualLogger.LogEntityError(job.Id, "Failed to delete Databricks secret:" +
                                    "{user_secret} in {secret_scope_name} as part of the Databricks job cleanup",
                                    secretName, _secretScopeName);
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    string secretNamesList = string.Join(",", Enumerable.Range(1, inputsAndOutputs).Select(x => job.RunId + "_" + x.ToString()));
                    _dualLogger.LogEntityError(job.Id, "failed to clean up Databricks secrets: {user_secret}, due to error: {exception}", secretNamesList, ex);
                }
            }
        }

        private void UpdateOutputs(DataBricksJobMetadata job)
        {
            if (job.OutputInfos.Count == 0)
            {
                return;
            }

            Dictionary<string, ModuleOutputExecutionInfo> outputsByDataId = job.ExecutionStructuredInterface.Outputs.ToDictionary(pair => pair.Value.DataId, pair => pair.Value);

            foreach (var outputInfo in job.OutputInfos)
            {
                outputInfo.DataLocation.DataReference = outputsByDataId[outputInfo.DataId].DataLocation.DataReference;
                outputInfo.DataLocation.DataStoreName = outputsByDataId[outputInfo.DataId].DataLocation.DataStoreName;
            }
        }

        private async Task<DataBricksJobMetadata> CheckJobStatusAsync(DataBricksJobMetadata job)
        {
            try
            {
                WorkspaceUrlAndPAT WorkspaceUrlAndPAT = await GetWorkspaceUrlAndPATAsync(job);
                var client = _dataBricksClientFactory.GetClient(WorkspaceUrlAndPAT.WorkspaceUrl, WorkspaceUrlAndPAT.PAT);

                RunStateDto runStateDto = null;

                await TryDatabricksOperationAndRehydrateCacheIfNecessaryAsync(job,
                    async (IDataBricksClient adbClient) => {
                        runStateDto = await adbClient.GetRunStatusAsync(job.DataBricksRunId);
                    },
                    client);

                if (!job.ReportedRunPage)
                {
                    string runLinkMessage = $"Submission to Azure Databricks was successful, Azure Databricks run Id is {job.DataBricksRunId}," +
                        $"Please view the run page for details: {runStateDto.RunPageUrl}";
                    // write to stdout a link to the run page in databricks
                    _dualLogger.StdOutOnlyLogger.LogEntityInfo(job.Id, runLinkMessage);
                    _dualLogger.LogEntityInfo(job.Id,
                                              "Submission to Azure Databricks was successful, Azure Databricks run Id is {job.DataBricksRunId}," +
                                              "Please view the run page for details: {user_data_location_uri}",
                                              job.DataBricksRunId, runStateDto.RunPageUrl);
                    job.ReportedRunPage = true;
                    await UpdateRunPropertiesAsync(job, "azureml.joblink", runStateDto.RunPageUrl);
                }

                if (runStateDto.State.LifeCycleState.Equals("terminated", StringComparison.InvariantCultureIgnoreCase))
                {
                    if (job.StartTime.HasValue)
                    {
                        JobReportingLogger.LogExecutionTime(
                            logger: _dualLogger,
                            jobId: job.Id,
                            runId: job.RunId,
                            executionTimeInMin: (DateTime.UtcNow - job.StartTime.Value).TotalMinutes);
                    }

                    if (runStateDto.State.ResultState.Equals("success", StringComparison.InvariantCultureIgnoreCase))
                    {
                        _dualLogger.LogEntityInfo(job.Id, "Job finished successfully in Azure Databricks.");

                        try
                        {
                            UpdateOutputs(job);

                            await _artifactServiceRegistry.RegisterOutputsAsync(
                                job: job,
                                executionDirectory: _executionDirectory,
                                token: new CancellationToken()
                            ).ConfigureAwait(false);
                            await MarkJobAsSucceededAndCleanSecretsAsync(job);
                        }
                        catch (Exception e) when (!ExceptionUtils.IsRetriableSystemException(e))
                        {
                            _dualLogger.LogEntityError(job.Id, "Failed to upload outputs and mark job as failed, error: {exception}", e);
                            await MarkJobAsFailedAndCleanSecretsAsync(job);
                            await _runStateUpdater.UpdateRunErrorAsync(
                                job: job,
                                errorCode: RootErrorCode.ServiceError,
                                errorMessage: $"Failed to upload outputs due to {e.Message}",
                                logger: _dualLogger);
                        }
                    }
                    else
                    {
                        _dualLogger.LogEntityError(job.Id, $"Job Failed in Azure Databricks. Run State: {runStateDto.State.ResultState}");
                        await MarkJobAsFailedAndCleanSecretsAsync(job);
                        await _runStateUpdater.UpdateRunErrorAsync(
                            job: job,
                            errorCode: RootErrorCode.UserError,
                            errorMessage: $"Job failed in Azure Databricks. State:{runStateDto.State.ResultState}, Message:{runStateDto.State.StateMessage}. " +
                                          $"Please view the run page for details: {runStateDto.RunPageUrl}",
                            logger: _dualLogger);
                    }
                }
                else if (runStateDto.State.LifeCycleState.Equals("internal_error", StringComparison.InvariantCultureIgnoreCase)
                    || runStateDto.State.LifeCycleState.Equals("skipped", StringComparison.InvariantCultureIgnoreCase))
                {
                    _dualLogger.LogEntityError(job.Id, $"Job Failed in Azure Databricks. Life cycle state: {runStateDto.State.LifeCycleState}");
                    _dualLogger.LogEntityError(job.Id, $"Life cycle message: {runStateDto.State.StateMessage}");
                    await MarkJobAsFailedAndCleanSecretsAsync(job);
                    await _runStateUpdater.UpdateRunErrorAsync(
                        job: job,
                        errorCode: RootErrorCode.UserError,
                        errorMessage: $"Job failed in Azure Databricks. State:{runStateDto.State.LifeCycleState}, Message:{runStateDto.State.StateMessage}. " +
                                      $"Please view the run page for details: {runStateDto.RunPageUrl}",
                        logger: _dualLogger);
                }
                else
                {
                    _dualLogger.LogEntityInfo(job.Id, $"Job is running. Life cycle state: {runStateDto.State.LifeCycleState}, " +
                        $"\n State message: {runStateDto.State.StateMessage}");
                    MarkJobAsRunning(job);
                }
            }
            catch (HttpRequestDetailException ex) when (RelInfra.Extensions.HttpClientExtensions.IsTooManyRequestsException(ex))
            {
                throw;
            }
            catch (Exception exception)
            {
                CommonLogger.LogEntityWarning(job.Id, "Can't check job status because of error: {exception}", exception);
            }

            return job;
        }

        private async Task<bool> UpdateRunPropertiesAsync(DataBricksJobMetadata job, string key, string value)
        {
            try
            {
                Dictionary<string, string> properties = new Dictionary<string, string>();
                properties.Add(key, value);

                var createRunDto = new CreateRunDto()
                {
                    Properties = properties
                };

                _dualLogger.LogEntityInfo(job.Id, $"Add property {key} - {value}");
                await _runHistoryClient.UpdateRunAsync(job.WorkspaceIdentity, job.RunHistoryExperimentName, job.RunId, createRunDto, job.CreatedBy);
            }
            catch (Exception exception)
            {
                _dualLogger.LogEntityWarning(job.Id, "Can't update run properties. Error: {exception}", exception);
                return false;
            }

            return true;
        }

        private async Task CancelJobAsync(DataBricksJobMetadata job)
        {
            _dualLogger.LogEntityInfo(job.Id, $"Cancelling Azure Databricks job {job.DataBricksRunId}.");

            try
            {
                if(!string.IsNullOrWhiteSpace(job.DataBricksRunId))
                {
                    WorkspaceUrlAndPAT WorkspaceUrlAndPAT = await GetWorkspaceUrlAndPATAsync(job);
                    var client = _dataBricksClientFactory.GetClient(WorkspaceUrlAndPAT.WorkspaceUrl, WorkspaceUrlAndPAT.PAT);

                    await TryDatabricksOperationAndRehydrateCacheIfNecessaryAsync(job,
                        async (IDataBricksClient adbClient) => {
                            await adbClient.CancelRunAsync(job.DataBricksRunId);
                        },
                        client);
                }
            }
            catch (Exception exception)
            {
                _dualLogger.LogEntityWarning(job.Id, "Can't cancel Azure Databricks job because of error: {exception}", exception);
            }

            _dualLogger.LogEntityInfo(job.Id, $"Azure Databricks job {job.DataBricksRunId} was cancelled");
        }

        private async Task<DataBricksJobMetadata> CommitJobAsync(DataBricksJobMetadata job, JobStatus initialStatus)
        {
            if (job.Status != initialStatus)
            {
                await _runStateUpdater.UpdateRunStateAsync(job, _dualLogger);
            }

            try
            {
                job = await _jobStorage.UpdateJobAsync(job);
                await _jobStatusCache.TryStoreJobStatusAsync(job);
            }
            catch (Exception ex)
            {
                _dualLogger.LogEntityError(job.Id, "Failed to commit job, error: {exception}", ex);
                throw;
            }

            if (job.Status != initialStatus)
            {
                var noWait = PingDcmAsync(job);
            }

            return job;
        }

        private void MarkJobAsRunning(DataBricksJobMetadata job)
        {
            job.Status = JobStatus.Running;
            if (!job.StartTime.HasValue)
            {
                job.StartTime = DateTime.UtcNow;
            }
        }

        private async Task MarkJobAsFailedAndCleanSecretsAsync(DataBricksJobMetadata job)
        {
            job.Status = JobStatus.Failed;
            CommonLogger.LogEntityInfo(job.Id, "Marking job Failed");
            SetJobEndTime(job);
            await CleanJobDatabricksArtifacts(job);
        }

        private async Task MarkJobAsCancelledAndCleanSecretsAsync(DataBricksJobMetadata job)
        {
            job.Status = JobStatus.Cancelled;
            SetJobEndTime(job);
            await CleanJobDatabricksArtifacts(job);
        }

        private async Task MarkJobAsSucceededAndCleanSecretsAsync(DataBricksJobMetadata job)
        {
            job.Status = JobStatus.Completed;
            CommonLogger.LogEntityInfo(job.Id, "Marking job Succeeded");
            SetJobEndTime(job);
            await CleanJobDatabricksArtifacts(job);
        }

        private void SetJobEndTime(DataBricksJobMetadata job)
        {
            job.EndTime = DateTime.UtcNow;
        }

        private async Task FlushLogsAsync(string jobId)
        {
            try
            {
                await _dualLogger.FlushAsync();
            }
            catch (Exception ex)
            {
                CommonLogger.LogEntityError(jobId, "Failed to flush logs, ignoring the error. Logs might be incomplete, error: {exception}", ex);
            }
        }

        private async Task<WorkspaceUrlAndPAT> RehydrateWorkspaceUrlAndPATCache(DataBricksJobMetadata job)
        {
            string WorkspaceUrlAndPATCacheKey = CreateWorkspaceUrlAndPATCacheKey(job.WorkspaceIdentity, job.ComputeName);
            _workspaceUrlAndPATCache.Remove(WorkspaceUrlAndPATCacheKey);

            return await GetWorkspaceUrlAndPATAsync(job);
        }

        private async Task<WorkspaceUrlAndPAT> GetWorkspaceUrlAndPATAsync(DataBricksJobMetadata job)
        {
            WorkspaceUrlAndPAT workspaceUrlAndPAT;
            string WorkspaceUrlAndPATCacheKey = CreateWorkspaceUrlAndPATCacheKey(job.WorkspaceIdentity, job.ComputeName);

            if (!_workspaceUrlAndPATCache.TryGetValue<WorkspaceUrlAndPAT>(WorkspaceUrlAndPATCacheKey, out workspaceUrlAndPAT))
            {
                ComputeResourceAndSecrets computeAndSecret;
                try
                {
                    computeAndSecret = await _workspaceResourcesClient.GetComputeResourceAndSecretsAsync(job.WorkspaceIdentity, job.ComputeName, job.CreatedBy, CancellationToken.None);
                }
                catch (ServiceInvocationException ex)
                {
                    CommonLogger.LogEntityError(job.Id, "Can't get compute resource and secrets because of error: {safe_exception_message}, stack trace: {stack_trace}", ex.Message, ex.StackTrace);
                    throw;
                }
                if (computeAndSecret?.Resource?.Properties?.ComputeType != "Databricks")
                {
                    throw new InvalidOperationException($"ComputeType needs to be Databricks but is found to be {computeAndSecret?.Resource?.Properties?.ComputeType} for compute {job.ComputeName}");
                }


                workspaceUrlAndPAT = new WorkspaceUrlAndPAT();
                var adbProperties = computeAndSecret?.Resource?.Properties;

                // fetching older ADB Workspace if not backfilled and remove roll-out depedency
                if(String.IsNullOrEmpty(adbProperties?.Properties?.WorkspaceUrl))
                {
                    workspaceUrlAndPAT.WorkspaceUrl = $"{ adbProperties?.ComputeLocation}.azuredatabricks.net";
                }
                else
                {
                    workspaceUrlAndPAT.WorkspaceUrl = adbProperties?.Properties?.WorkspaceUrl;
                }
                
                workspaceUrlAndPAT.PAT = computeAndSecret.Secrets.DatabricksAccessToken;

                _workspaceUrlAndPATCache.Set<WorkspaceUrlAndPAT>(WorkspaceUrlAndPATCacheKey,
                    workspaceUrlAndPAT,
                    new MemoryCacheEntryOptions()
                        .SetSize(1) // Each entry takes 1 slot
                        .SetSlidingExpiration(TimeSpan.FromHours(1)) // Entry is ready to be evicted after 1 hour since last access
                        );
            }

            return workspaceUrlAndPAT;
        }

        private string CreateWorkspaceUrlAndPATCacheKey(WorkspaceIdentity workspace, string computeName)
        {
            return $"{workspace.SubscriptionId}_{workspace.ResourceGroupName}_{workspace.WorkspaceName}_{computeName}";
        }

        private async Task TryDatabricksOperationAndRehydrateCacheIfNecessaryAsync(DataBricksJobMetadata job, Func<IDataBricksClient, Task> operation, IDataBricksClient client)
        {
            try
            {
                await operation(client);
            }
            catch (Exception ex)
            {
                // in case the pat has expired, clear local cache and try one more time
                if (ex is ADBAuthException)
                {
                    CommonLogger.LogEntityInfo(job.Id, $"Detected an authentication failure when calling Databricks, it is likely that the Databricks Compute PAT is expired. Retrieving the latest PAT and retrying.");
                    WorkspaceUrlAndPAT WorkspaceUrlAndPAT = await RehydrateWorkspaceUrlAndPATCache(job);
                    client = _dataBricksClientFactory.GetClient(WorkspaceUrlAndPAT.WorkspaceUrl, WorkspaceUrlAndPAT.PAT);
                    await operation(client);
                }
                else if (ex is JsonReaderException)
                {
                    // The JsonReaderException is handled separately because the DataBricks API may return 302 and redirect to login.html
                    // due to misconfiguration of ADB private link, which leads to json parsing failure.
                    CommonLogger.LogEntityInfo(job.Id, $"Failed to parse json response from DataBricks API, possibly because the private link of ADB is misconfigured.");
                    throw new ADBApiParseJsonException(ex);
                }
                else
                {
                    throw;
                }
            }
        }

        private async Task TryDBFSOperationAndRehydrateCacheIfNecessaryAsync(DataBricksJobMetadata job, Func<IDBFSClient, Task> operation, IDBFSClient client, bool continueOnDFSFailure)
        {
            try
            {
                await operation(client);
            }
            catch (Exception ex)
            {
                // in case the pat has expired, clear local cache and try one more time
                if (ex is ADBAuthException)
                {
                    CommonLogger.LogEntityInfo(job.Id, $"Detected an authentication failure when calling DBFS, it is likely that the Databricks Compute PAT is expired. Retrieving the latest PAT and retrying.");
                    WorkspaceUrlAndPAT WorkspaceUrlAndPAT = await RehydrateWorkspaceUrlAndPATCache(job);
                    client = _dbfsClientFactory.GetClient(job.Id, WorkspaceUrlAndPAT.WorkspaceUrl, WorkspaceUrlAndPAT.PAT, _dualLogger, continueOnFailure: continueOnDFSFailure);
                    await operation(client);
                }
                else if (ex is JsonReaderException)
                {
                    // The JsonReaderException is handled separately because the DBFS API may return 302 and redirect to login.html
                    // due to misconfiguration of ADB private link, which leads to json parsing failure.
                    CommonLogger.LogEntityInfo(job.Id, $"Failed to parse json response from DBFS API, possibly because the private link of ADB is misconfigured.");
                    throw new ADBApiParseJsonException(ex);
                }
                else
                {
                    throw;
                }
            }
        }

        private async Task GetClusterIfNeededAsync(DataBricksJobMetadata job, SubmitRunDto submitRunDto)
        {
            if (_dynamicClusterDto == null)
            {
                await DoClusterOperationAsync(
                    job,
                    submitRunDto,
                    async (IDataBricksClient adbClient) =>
                    {
                        _dynamicClusterDto = await adbClient.GetClusterAsync(submitRunDto.ExistingClusterId);
                    },
                    nameof(GetClusterIfNeededAsync));

                _originClusterSparkSetting = new OriginClusterSparkSetting
                {
                    Conf = _dynamicClusterDto.SparkConf?.ToDictionary(kv => kv.Key, kv => kv.Value),
                };

                _dynamicClusterDto.SparkConf = _dynamicClusterDto.SparkConf ?? new Dictionary<string, string>();
            }
        }

        private async Task EditClusterAsync(DataBricksJobMetadata job, SubmitRunDto submitRunDto)
        {
            await DoClusterOperationAsync(
                job,
                submitRunDto,
                async (IDataBricksClient adbClient) =>
                {
                    await adbClient.EditClusterAsync(_dynamicClusterDto.ConvertToEditClusterDto());
                },
                nameof(EditClusterAsync));
        }

        private async Task<string> GetClusterStateAsync(DataBricksJobMetadata job, SubmitRunDto submitRunDto)
        {
            ClusterDto clusterDto = null;
            await DoClusterOperationAsync(
                 job,
                 submitRunDto,
                 async (IDataBricksClient adbClient) =>
                 {
                     clusterDto = await adbClient.GetClusterAsync(submitRunDto.ExistingClusterId);
                 },
                 nameof(GetClusterStateAsync));

            return clusterDto.State;
        }

        private async Task StartClusterAsync(DataBricksJobMetadata job, SubmitRunDto submitRunDto)
        {
            await DoClusterOperationAsync(
                      job,
                      submitRunDto,
                      async (IDataBricksClient adbClient) =>
                      {
                          await adbClient.StartClusterAsync(submitRunDto.ExistingClusterId);
                      },
                      nameof(StartClusterAsync));
        }

        private async Task DoClusterOperationAsync(DataBricksJobMetadata job, SubmitRunDto submitRunDto, Func<IDataBricksClient, Task> clusterOperation, string OpName)
        {
            try
            {
                _dualLogger.LogEntityInfo(job.Id, $"Executing {OpName} on cluster {submitRunDto.ExistingClusterId}.");

                WorkspaceUrlAndPAT workspaceUrlAndPAT = await GetWorkspaceUrlAndPATAsync(job);
                var client = _dataBricksClientFactory.GetClient(workspaceUrlAndPAT.WorkspaceUrl, workspaceUrlAndPAT.PAT);

                await TryDatabricksOperationAndRehydrateCacheIfNecessaryAsync(job,
                    clusterOperation,
                    client);
            }
            catch (SubmissionRetriableException)
            {
                throw;
            }
            catch (Exception ex) when (!HttpClientExtensions.IsTooManyRequestsException(ex))
            {
                _dualLogger.LogEntityError(job.Id, $"Exception happened when calling {OpName} on existing cluster {submitRunDto.ExistingClusterId} info, error: {ex.Message}, stack_trace: {ex.StackTrace}!");
                
                throw;
            }
        }

        private async Task CheckSupportedRuntimeVersions(DataBricksJobMetadata job, SubmitRunDto submitRunDto)
        {
            try
            {
                string sparkVersion = GetSparkVersion(submitRunDto);
                WorkspaceUrlAndPAT workspaceUrlAndPAT = await GetWorkspaceUrlAndPATAsync(job);
                var client = _dataBricksClientFactory.GetClient(workspaceUrlAndPAT.WorkspaceUrl, workspaceUrlAndPAT.PAT);

                ClusterRuntimeDto runtimeDto = null;
                await TryDatabricksOperationAndRehydrateCacheIfNecessaryAsync(job,
                    async (IDataBricksClient adbClient) => {
                        runtimeDto = await adbClient.GetClusterRuntimeVersionAsync();
                    },
                    client);

                bool isSupportedVersion = runtimeDto.VersionList.Select(sparkVersionDto => sparkVersionDto.Verison).ToList().Contains(sparkVersion);
                if (!isSupportedVersion)
                {
                    string warningMessage = string.Format($"The spark version({sparkVersion}) is no longer supported by Azure DataBricks. Please check the reference: https://learn.microsoft.com/en-us/azure/databricks/release-notes/runtime/databricks-runtime-ver#runtime-support");
                    _dualLogger.LogEntityWarning(job.Id, warningMessage);
                    await _runStateUpdater.UpdateRunWarningAsync(
                        job: job,
                        source: "DataBricksCloud",
                        message: warningMessage,
                        logger: _dualLogger);
                }
            }
            catch (HttpRequestDetailException ex) when (HttpClientExtensions.IsTooManyRequestsException(ex))
            {
                throw;
            }
            catch (Exception exception)
            {
                CommonLogger.LogEntityWarning(job.Id, "Can't check job spark version because of error: {exception}", exception);
            }
        }

        private bool ShouldRetrySubmission(Exception exception)
        {
            if (exception is ServiceInvocationException e)
            {
                exception = e.InnerException;
            }

            if (exception is RunHistoryException runHistoryException && runHistoryException.IsRetriable())
            {
                return true;
            }

            if (HttpClientExtensions.ShouldRetryAmlHttpClientException(exception))
            {
                return true;
            }

            if (exception is SubmissionRetriableException)
            {
                return true;
            }

            return false;
        }
    }

    public class OriginClusterSparkSetting
    {
        public Dictionary<string, string> Conf { get; set; }
    }
}
