﻿using Microsoft.Aether.Clouds.CloudCommon.Cloudlets;
using Microsoft.Aether.TaggedQueueManager.CloudTags;
using Microsoft.RelInfra.Instrumentation;
using Microsoft.Aether.BlueBox.Clouds.Common;
using Microsoft.Aether.Clouds.CloudCommon.JobQueueProcessing;

namespace Microsoft.Aether.DataBricksCloud.K8sService
{
    public class DataBricksJobsController : BaseJobController<DataBricksJobMetadata>
    {
        public DataBricksJobsController(
            ITaggedJobsManager<AssignedQueueIdTags, DataBricksJobMetadata> taggedJobsManager, 
            CounterManager counters,
            JobQueue jobQueue)
            : base(taggedJobsManager, counters, jobQueue)
        {
        }
    }
}
