<Project Sdk="Microsoft.NET.Sdk" DefaultTargets="Publish;Build">
  <PropertyGroup>
    <PublishDir>$(BaseTargetDir)\app\aether\bluebox\DataBricksCloudK8sService</PublishDir>
    <OutputType>Exe</OutputType>
    <RootNamespace>Microsoft.Aether.DataBricksCloud.K8sService</RootNamespace>
    <AssemblyName>Microsoft.Aether.DataBricksCloud.K8sService</AssemblyName>
    <TargetFramework>net8.0</TargetFramework>
    <ResolveAssemblyWarnOrErrorOnTargetArchitectureMismatch>None</ResolveAssemblyWarnOrErrorOnTargetArchitectureMismatch>
    <HighEntropyVA>true</HighEntropyVA>
  </PropertyGroup>
  <ItemGroup>
    <None Include="..\..\..\..\shared\Scripts\start.sh">
      <Link>start.sh</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="..\..\..\..\shared\SharedConfigurationSettings\BBSharedSettings.ini" Link="BBSharedSettings.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="..\..\..\..\shared\SharedConfigurationSettings\SharedSettings.ini" Link="SharedSettings.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="DataBricksCloudService.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="..\..\..\CA\*.cer">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="Dockerfile">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="..\..\Clouds.Common\cloudletindexpolicy.json">
      <Link>cloudletindexpolicy.json</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <ContentWithTargetPath  Include="error_config.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      <TargetPath>error_config.json</TargetPath>
    </ContentWithTargetPath >
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Versioning" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Versioning.ApiExplorer" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" />
    <PackageReference Include="Microsoft.Extensions.Caching.Memory" />
    <PackageReference Include="System.Net.Security" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\..\..\..\RelInfra\RelInfra.Common.Library\src\Common\RelInfra.Common.csproj" />
    <ProjectReference Include="..\..\..\..\Clouds\Common\CloudCommon.WebNetCore\CloudCommon.WebNetCore.csproj" />
    <ProjectReference Include="..\..\..\..\Clouds\Common\CloudCommon\CloudCommon.csproj" />
    <ProjectReference Include="..\..\..\..\Clouds\Common\Worker\Microsoft.Aether.CloudCommon.Worker.csproj" />
    <ProjectReference Include="..\..\..\..\shared\Microsoft.Aether.BackendCommon\Microsoft.Aether.BackendCommon.csproj" />
    <ProjectReference Include="..\..\..\..\shared\Microsoft.Aether.S2S.Common\Microsoft.Aether.S2S.Common.csproj" />
    <ProjectReference Include="..\..\..\..\shared\Microsoft.Aether.TaggedQueueManager.CloudTags\Microsoft.Aether.TaggedQueueManager.CloudTags.csproj" />
    <ProjectReference Include="..\..\..\..\shared\Microsoft.Aether.TaggedQueueManager\Microsoft.Aether.TaggedQueueManager.csproj" />
    <ProjectReference Include="..\..\..\AetherK8SBaseService\Microsoft.Aether.BlueBox.AetherK8SBaseService.csproj" />
    <ProjectReference Include="..\..\..\AetherK8SCommon\Microsoft.Aether.BlueBox.AetherK8SCommon.csproj" />
    <ProjectReference Include="..\..\..\ArtifactClient\Microsoft.Aether.BlueBox.ArtifactClient\Microsoft.Aether.BlueBox.ArtifactClient.csproj" />
    <ProjectReference Include="..\..\..\DataStoreClient\Microsoft.Aether.BlueBox.DataStoreClient\Microsoft.Aether.BlueBox.DataStoreClient.csproj" />
    <ProjectReference Include="..\..\..\Microsoft.Aether.BlueBox.Storage\Microsoft.Aether.BlueBox.Common.Storage.csproj" />
    <ProjectReference Include="..\..\..\Microsoft.Aether.BlueBox.Web.Common\Microsoft.Aether.BlueBox.Web.Common.csproj" />
    <ProjectReference Include="..\..\..\SnapshotClient\Microsoft.Aether.BlueBox.SnapshotClient\Microsoft.Aether.BlueBox.SnapshotClient.csproj" />
    <ProjectReference Include="..\..\..\TokenServiceClient\Client\Microsoft.Aether.BlueBox.TokenServiceClient.csproj" />
    <ProjectReference Include="..\..\..\WorkspaceResourcesClient\Microsoft.Aether.BlueBox.WorkspaceResourcesClient\Microsoft.Aether.BlueBox.WorkspaceResourcesClient.csproj" />
    <ProjectReference Include="..\..\..\WorkspaceResourcesClient\Microsoft.Aether.BlueBox.WorkspaceResourcesContracts\Microsoft.Aether.BlueBox.WorkspaceResourcesContracts.csproj" />
    <ProjectReference Include="..\..\Clouds.Common\Microsoft.Aether.BlueBox.Clouds.Common.csproj" />
    <ProjectReference Include="..\Client\DataBricksCloudK8sService.DataBricksClient.csproj" />
    <ProjectReference Include="..\..\..\RunHistoryClient\src\client\Microsoft.Aether.BlueBox.RunHistoryClient.csproj" />
    <ProjectReference Include="$(SrcRoot)\Common\WebApi\Common.WebApi.csproj" />
  </ItemGroup>
</Project>
