﻿using System;
using Microsoft.RelInfra.Common.Configuration;
using Microsoft.RelInfra.Common.Secrets;

namespace Microsoft.Aether.DataBricksCloud.K8sService
{
    public class DataBricksCloudConfig : VerifiableConfig
    {
        private readonly SecretProvider _secretProvider;

        public DataBricksCloudConfig(IRelInfraConfiguration config, SecretProvider secretProvider)
            : base(config, verifyConfigOnChanges: true, configName: nameof(DataBricksCloudConfig))
        {
            _secretProvider = secretProvider;
        }
        
        public string GetSslCert()
        {
            return Config.GetString("AmlServices.SslCertSecretPath");
        }

        public string JobStorageDatabaseName => Config.GetString("JobStorage.DatabaseName");
        public string JobStorageTableName => Config.GetString("JobStorage.TableName");
        public string AzureQueueManagerPrefix => Config.GetString("AzureQueueManager.QueuePrefix");
        public TimeSpan AzureQueueManagerUpdatePeriod => Config.GetTimeSpan("AzureQueueManager.UpdatePeriod");
        public TimeSpan WorkspaceJobStorageCacheDuration => Config.GetTimeSpan("JobStorage.WorkspaceCacheDuration");
        public string WorkDirectory => Config.GetString("WorkDirectory.Name");

        public string AmlServicesEndpoint => Config.GetString("AmlServices.AmlServicesEndpoint");
        public string Authority => Config.GetString("AmlServices.Authority");
    }
}
