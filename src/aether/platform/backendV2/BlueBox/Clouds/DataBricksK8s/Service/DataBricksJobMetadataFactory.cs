﻿using System.Collections.Generic;
using System.Linq;
using Microsoft.Aether.CloudCommon;
using Microsoft.Aether.Clouds.CloudCommon.Cloudlets;
using Microsoft.Aether.DataContract.Backend.Clouds;
using Microsoft.Aether.DataContracts.CloudSystem;

namespace Microsoft.Aether.DataBricksCloud.K8sService
{
    class DataBricksJobMetadataFactory : ICloudletJobMetadataFactory<DataBricksJobMetadata>
    {
        public DataBricksJobMetadata Create(JobSubmissionInfo info)
        {
            var inputs = info.Inputs ?? new List<ModuleInputExecutionInfo>();
            var outputs = info.Outputs ?? new List<ModuleOutputExecutionInfo>();

            return new DataBricksJobMetadata
            {
                Id = info.JobId,
                ExperimentId = info.ExperimentId,
                PipelineRunId = info.PipelineRunId ?? info.ExperimentId,
                RunId = info.RunId,
                DataContainerId = info.DataContainerId,
                TeamId = info.TeamId,
                UserName = info.Owner,
                JobNotificationCallbackUrl = info.NotificationCallbackUrl,
                ModuleInfo = new CloudModuleExecutionInfo(info.ModuleInfo),
                InputInfos = inputs.Select(i => new CloudModuleInputExecutionInfo(i)).ToList(),
                OutputInfos = outputs.Select(o => new CloudModuleOutputExecutionInfo(o)).ToList(),
                StdOutUploadLocation = info.StdOutUploadLocation,
                StdErrUploadLocation = info.StdErrUploadLocation,
                WritableLogUris = info.WritableLogUris,
                Status = JobStatus.Submitted,
                ExecutionStructuredInterface = info.ExecutionStructuredInterface,
                WorkspaceIdentity = info.WorkspaceIdentity,
                RunHistoryExperimentName = info.RunHistoryExperimentName,
                ReportedRunPage = false,
                AzureTenantId = info.AzureTenantId,
                AzureUserId = info.AzureUserId,
                AuthToken = info.AuthToken,
                CreatedBy = info.CreatedBy,
                GraphDefaultCompute = info.GraphDefaultCompute,
                ParentSubGraphModuleIds = info.ParentSubGraphModuleIds,
            };
        }
    }
}
