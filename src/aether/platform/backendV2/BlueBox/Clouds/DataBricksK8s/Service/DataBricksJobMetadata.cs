﻿using System.Runtime.Serialization;
using Microsoft.Aether.Clouds.CloudCommon.Cloudlets;
using Microsoft.Aether.DataContracts;
using Microsoft.Aether.DataContracts.CloudSystem;
using Newtonsoft.Json;

namespace Microsoft.Aether.DataBricksCloud.K8sService
{
    public class DataBricksJobMetadata : AMLJobMetadata
    {
        [DataMember]
        public string ExperimentId { get; set; }

        [DataMember]
        public string UserName { get; set; }


        [DataMember]
        public string DataBricksRunId { get; set; }

        [DataMember]
        public string ComputeName { get; set; }

        [DataMember]
        public bool ReportedRunPage { get; set; }

        [DataMember]
        public string AzureTenantId { get; set; }

        [DataMember]
        public string AzureUserId { get; set; }

        [DataMember]
        public bool PermitClusterRestart { get; set; }

        public override string ToString()
        {
            return JsonConvert.SerializeObject(this, Formatting.Indented);
        }
    }
}
