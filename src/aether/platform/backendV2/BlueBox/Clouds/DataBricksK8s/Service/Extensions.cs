﻿using DataBricksCloudK8sService.DataBricksClient;
using Microsoft.Aether.AEVA.DataContracts;

namespace Microsoft.Aether.DataBricksCloud.K8sService
{
    public static class Extensions
    {
        public static EditClusterDto ConvertToEditClusterDto(this ClusterDto clusterDto)
        {
            return new EditClusterDto
            {
                NumWorker = clusterDto.NumWorker,
                AutoScale = clusterDto.AutoScale,
                ClusterId = clusterDto.ClusterId,
                ClusterName = clusterDto.ClusterName,
                SparkVersion = clusterDto.SparkVersion,
                SparkConf = clusterDto.SparkConf,
                NodeTypeId = clusterDto.NodeTypeId,
                DriverNodeTypeId = clusterDto.DriverNodeTypeId,
                InitScripts = clusterDto.InitScripts,
                DockerImage = clusterDto.DockerImage,
                SparkEnvVars = clusterDto.SparkEnvVars,
                AutoterminationMinutes = clusterDto.AutoterminationMinutes,
                InstancePoolId = clusterDto.InstancePoolId
            };
        }
    }
}
