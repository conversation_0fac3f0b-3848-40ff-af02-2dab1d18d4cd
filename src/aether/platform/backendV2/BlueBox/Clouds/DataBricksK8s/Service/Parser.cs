﻿using System;
using System.Collections.Generic;
using System.Linq;
using DataBricksCloudK8sService.DataBricksClient;
using Microsoft.Aether.Clouds.CloudCommon;
using Microsoft.Aether.DataContracts;
using Microsoft.Aether.DataContracts.CloudSystem;
using Microsoft.RelInfra.Instrumentation.Logging;

namespace Microsoft.Aether.DataBricksCloud.K8sService
{
    public static class Parser
    {
        private const string amlParameterPrefix = "_aml_parameter_";

        public static void Parse(DataBricksJobMetadata job, Dictionary<string, string> metadata)
        {
            job.ComputeName = job.GraphDefaultCompute?.Name ?? ParameterParser.GetParameterValue(job.Id, metadata, "compute_name");
            job.PermitClusterRestart = GetPermitClusterRestartParam(metadata);
        }
        
        public static string GetScriptName(Dictionary<string, string> parameters)
        {
            return ParameterParser.TryGetParameterValue(parameters, "python_script_name");
        }

        public static SubmitRunDto GetSubmitRunDto(DataBricksJobMetadata job, Dictionary<string, string> metadata, Dictionary<string, string> parameters)
        {
            ValidateParameters(job, metadata, parameters);

            NewClusterDto newClusterDto = null;
            string existingClusterId = job.GraphDefaultCompute?.DatabricksComputeInfo.ExistingClusterId ?? ParameterParser.TryGetParameterValue(metadata, "existing_cluster_id");

            if (string.IsNullOrEmpty(existingClusterId))
            {
                newClusterDto = new NewClusterDto
                {
                    SparkVersion = ParameterParser.GetParameterValue(job.Id, metadata, "spark_version"),
                    NodeTypeId = ParameterParser.TryGetParameterValue(metadata, "node_type_id"),
                    InstancePoolId = ParameterParser.TryGetParameterValue(metadata, "instance_pool_id"),
                    SparkEnvVars = ParameterParser.TryGetDictionaryValuesHaveEquals(metadata, "spark_env_vars"),
                    SparkConf = ParameterParser.TryGetDictionaryValuesHaveEquals(metadata, "spark_conf"),
                };

                int? numWorkers = ParameterParser.GetNullableInt(job.Id, metadata, "num_workers");

                int? maxWorkers = ParameterParser.GetNullableInt(job.Id, metadata, "max_workers");
                int? minWorkers = ParameterParser.GetNullableInt(job.Id, metadata, "min_workers");


                if (numWorkers.HasValue)
                {
                    newClusterDto.NumWorkers = numWorkers.Value;
                }
                else
                {
                    newClusterDto.AutoScale = new AutoScaleDto
                    {
                        MaxWorkers = maxWorkers.Value,
                        MinWorkers = minWorkers.Value
                    };
                }

                List<string> initScripts = ParameterParser.TryGetCommaSeparatedStrings(metadata, "init_scripts");
                if (initScripts != null)
                {
                    newClusterDto.DbfsInitScripts = new List<InitScriptInfoDto>();
                    foreach (string initScript in initScripts)
                    {
                        newClusterDto.DbfsInitScripts.Add(new InitScriptInfoDto
                        {
                            DbfsPath = new DbfsStorageInfoDto { Destination = initScript }
                        });
                    }
                }

                string clusterLogPath = ParameterParser.TryGetParameterValue(metadata, "cluster_log_dbfs_path");
                if (!string.IsNullOrEmpty(clusterLogPath))
                {
                    newClusterDto.ClusterLogConf = new ClusterLogConfDto
                    {
                        Dbfs = new DbfsStorageInfoDto
                        {
                            Destination = clusterLogPath
                        }
                    };
                }
            }

            NoteBookTaskDto noteBookTaskDto = null;
            SparkPythonTaskDto sparkPythonTaskDto = null;
            SparkJarTaskDto sparkJarTaskDto = null;
            string notebookPath = ParameterParser.TryGetParameterValue(parameters, "notebook_path");
            string pythonScriptPath = ParameterParser.TryGetParameterValue(parameters, "python_script_path");
            string mainClassName = ParameterParser.TryGetParameterValue(parameters, "main_class_name");
            string pythonScriptName = ParameterParser.TryGetParameterValue(parameters, "python_script_name");
            if (!string.IsNullOrEmpty(notebookPath))
            {
                noteBookTaskDto = new NoteBookTaskDto
                {
                    NotebookPath = notebookPath,
                    BaseParameters = DecodeBaseParams(parameters, "base_parameters")
                };
            }
            else if (!string.IsNullOrEmpty(pythonScriptPath))
            {
                sparkPythonTaskDto = new SparkPythonTaskDto
                {
                    PythonFile = pythonScriptPath,
                    Parameters = DecodeStringParams(parameters, "python_script_params")
                };
            }
            else if (!string.IsNullOrEmpty(mainClassName))
            {
                sparkJarTaskDto = new SparkJarTaskDto
                {
                    MainClassName = mainClassName,
                    Parameters = DecodeStringParams(parameters, "jar_params")
                };
            }
            else
            {
                sparkPythonTaskDto = new SparkPythonTaskDto
                {
                    PythonFile = $"dbfs:/{job.RunId}/{pythonScriptName}",
                    Parameters = DecodeStringParams(parameters, "python_script_params")
                };
            }

            // Setup Libraries
            List<object> libraries = new List<object>();
            // maven
            List<MavenLibraryDto> mavenLibraries = DataBricksParameterParser.TryGetMavenLibraries(
                metadata, "maven_libraries");
            libraries.AddRange(mavenLibraries.Select(x => new MavenLibraryWrapperDto { MavenLibrary = x }));

            // pypi
            List<PythonPyPiOrRCranLibraryDto> pypiLibraries = DataBricksParameterParser.TryGetPythonPyPiOrRCranLibraries(
                metadata, "pypi_libraries");
            libraries.AddRange(pypiLibraries.Select(x => new PythonPyPiLibraryWrapperDto { PythonPyPiLibrary = x }));

            // RCran
            List<PythonPyPiOrRCranLibraryDto> rcranLibraries = DataBricksParameterParser.TryGetPythonPyPiOrRCranLibraries(
                metadata, "rcran_libraries");
            libraries.AddRange(rcranLibraries.Select(x => new RCranLibraryWrapperDto { RCranLibrary = x }));

            // jar
            List<string> jarLibraries = ParameterParser.TryGetCommaSeparatedStrings(metadata, "jar_libraries");
            if(jarLibraries != null)
            {
                libraries.AddRange(jarLibraries.Select(x => new JarLibraryWrapperDto { JarLibrary = x }));
            }

            // egg
            List<string> eggLibraries = ParameterParser.TryGetCommaSeparatedStrings(metadata, "egg_libraries");
            if (eggLibraries != null)
            {
                libraries.AddRange(eggLibraries.Select(x => new EggLibraryWrapperDto { EggLibrary = x }));
            }

            return new SubmitRunDto
            {
                RunName = ParameterParser.GetParameterValue(job.Id, metadata, "run_name", true) ?? job.RunId,
                ExistingClusterId = existingClusterId,
                NewCluster = newClusterDto,
                NotebookTask = noteBookTaskDto,
                SparkPythonTask = sparkPythonTaskDto,
                SparkJarTask = sparkJarTaskDto,
                Libraries = libraries,
                TimeoutSeconds = ParameterParser.GetNullableInt(job.Id, metadata, "timeout_seconds")
            };
        }

        public static bool NeedEditExistingCluster(SubmitRunDto submitRunDto, OriginClusterSparkSetting originClusterSparkSetting, ClusterDto dynamicClusterDto)
        {
            if (!string.IsNullOrEmpty(submitRunDto.ExistingClusterId) && dynamicClusterDto != null)
            {
                if (dynamicClusterDto.SparkConf == null || dynamicClusterDto.SparkConf.Count == 0)
                {
                    return false;
                }

                if (originClusterSparkSetting?.Conf == null)
                {
                    return true;
                }

                foreach (var kv in dynamicClusterDto.SparkConf)
                {
                    if (!originClusterSparkSetting.Conf.ContainsKey(kv.Key) || originClusterSparkSetting.Conf[kv.Key] != kv.Value)
                    {
                        return true;
                    }
                }
            }

            return false;
        }

        private static bool GetPermitClusterRestartParam(Dictionary<string, string> metadata)
        {
            var restartCluster = false;
            var restartClusterStr = ParameterParser.TryGetParameterValue(metadata, DataBricksConstants.RestartParam);
            if(!string.IsNullOrEmpty(restartClusterStr) && bool.TryParse(restartClusterStr, out var value))
            {
                restartCluster = value;
            }

            return restartCluster;
        }

        private static List<string> DecodeStringParams(Dictionary<string, string> parameters, string entityName)
        {
            string encodedStringParams = ParameterParser.TryGetParameterValue(parameters, entityName);
            if (string.IsNullOrEmpty(encodedStringParams))
            {
                return new List<string>();
            }

            // Separator is || and replace every instance of |- with |
            string[] stringParams = encodedStringParams.Split("||");
            List<string> paramList = new List<string>();

            for (int i = 0; i < stringParams.Length; i++)
            {
                string amlParameterName = String.Concat(amlParameterPrefix, stringParams[i]);
                string value = "";

                /*
                   If _aml_parameter_<param> is present in parameters,
                   then it is a PipelineParameter and need to be added as two items
                   one is name followed by its new value which is in parameters.

                   For eg: If stringParams has ["_aml_parameter_myparam", "testparam"] and
                   Parameters has ["_aml_parameter_myparam", "pipeline_param1"] then
                   new item will be ["--MYPARAM", "pipeline_param1"]
                */
                if (parameters.ContainsKey(amlParameterName))
                {

                    paramList.Add(String.Concat("--", stringParams[i].Replace("|-", "|").ToUpper()));
                    value = parameters[amlParameterName];
                    i++;
                }
                else
                {
                    value = stringParams[i];
                }
                paramList.Add(value.Replace("|-", "|"));
            }

            return paramList;
        }

        private static Dictionary<string, string> DecodeBaseParams(Dictionary<string, string> parameters, string entityName)
        {
            Dictionary<string, string> entityParameters = ParameterParser.TryGetDictionaryValuesHaveEquals(parameters, entityName);
            if(entityParameters == null)
            {
                return new Dictionary<string, string>();
            }

            Dictionary<string, string> pipelineParamsToUpdate = new Dictionary<string, string>();

            foreach (KeyValuePair<string, string> param in entityParameters)
            {
                string amlParameterName = String.Concat(amlParameterPrefix, param.Key);

                /*
                   If _aml_parameter_<param> is present in parameters,
                   then it is a PipelineParameter and need to be added with
                   its new value which is in parameters.

                   For eg: If entityParameters has {"_aml_parameter_myparam": "testparam"} and
                   Parameters has {"_aml_parameter_myparam": "pipeline_param1"} then
                   new item will be {"myparam", "pipeline_param1"}
                */
                if (parameters.ContainsKey(amlParameterName) &&
                    !parameters[amlParameterName].Equals(param.Value))
                {
                    pipelineParamsToUpdate.Add(param.Key, parameters[amlParameterName].ToString());
                }
            }

            foreach (KeyValuePair<string, string> pipelineParam in pipelineParamsToUpdate)
            {
                entityParameters[pipelineParam.Key] = pipelineParam.Value;
            }

            return entityParameters;
        }

        private static void ValidateParameters(DataBricksJobMetadata job, Dictionary<string, string> metadata, Dictionary<string, string> parameters)
        {
            // validate cluster setup
            string existingClusterId = ParameterParser.TryGetParameterValue(metadata, "existing_cluster_id");
            if(!string.IsNullOrWhiteSpace(existingClusterId))
            {
                ValidateExistingClusterSetup(job, metadata);
            }
            else
            {
                // validate new cluster worker setup
                ValidateClusterWorkersSetup(job, metadata);
            }

            // validate workload
            ValidateWorkloadSetup(job, parameters);
        }

        private static void ValidateClusterWorkersSetup(DataBricksJobMetadata job, Dictionary<string, string> metadata)
        {
            int? numWorkers = ParameterParser.GetNullableInt(job.Id, metadata, "num_workers");

            int? maxWorkers = ParameterParser.GetNullableInt(job.Id, metadata, "max_workers");
            int? minWorkers = ParameterParser.GetNullableInt(job.Id, metadata, "min_workers");

            bool autoScaleIsValid = maxWorkers.HasValue && minWorkers.HasValue;
            bool numWorkersIsValid = numWorkers.HasValue;

            if (!autoScaleIsValid && !numWorkersIsValid)
            {
                string errorMessage = "either the num_workers or the autoscale paramater must be specified.";
                CommonLogger.LogEntityError(job.Id, errorMessage);
                throw new ArgumentException(errorMessage);
            }
            else if (autoScaleIsValid && numWorkersIsValid)
            {
                string errorMessage = "only one of the num_workers or the autoscale paramaters must be specified.";
                CommonLogger.LogEntityError(job.Id, errorMessage);
                throw new ArgumentException(errorMessage);
            }
        }

        private static void ValidateExistingClusterSetup(DataBricksJobMetadata job, Dictionary<string, string> metadata)
        {
            bool nodeTypeSpecified = !string.IsNullOrEmpty(ParameterParser.TryGetParameterValue(metadata, "node_type_id")) ||
                !string.IsNullOrEmpty(ParameterParser.TryGetParameterValue(metadata, "instance_pool_id"));

            if ( !string.IsNullOrEmpty(ParameterParser.TryGetParameterValue(metadata, "spark_version"))
                || nodeTypeSpecified
                || !string.IsNullOrEmpty(ParameterParser.TryGetParameterValue(metadata, "spark_env_vars"))
                || !string.IsNullOrEmpty(ParameterParser.TryGetParameterValue(metadata, "spark_conf"))
                || !string.IsNullOrEmpty(ParameterParser.TryGetParameterValue(metadata, "num_workers"))
                || !string.IsNullOrEmpty(ParameterParser.TryGetParameterValue(metadata, "max_workers"))
                || !string.IsNullOrEmpty(ParameterParser.TryGetParameterValue(metadata, "min_workers")))
            {
                string errorMessage = "you may not specify cluster creation parameters: " +
                    "spark_version,node_type_id,instance_pool_id,spark_env_vars,spark_conf,num_workers,max_workers,min_workers " +
                    "when running a databricks job on an existing cluster.";
                CommonLogger.LogEntityError(job.Id, errorMessage);
                throw new ArgumentException(errorMessage);
            }
        }

        private static void ValidateWorkloadSetup(DataBricksJobMetadata job, Dictionary<string, string> parameters)
        {
            string notebookPath = ParameterParser.TryGetParameterValue(parameters, "notebook_path");
            string pythonPath = ParameterParser.TryGetParameterValue(parameters, "python_script_path");
            string pythonParams = ParameterParser.TryGetParameterValue(parameters, "python_script_params");
            Dictionary<string, string> notebookParams = ParameterParser.TryGetDictionaryValuesHaveEquals(parameters, "base_parameters");
            string jarMainClassName = ParameterParser.TryGetParameterValue(parameters, "main_class_name");
            string jarParams = ParameterParser.TryGetParameterValue(parameters, "jar_params");

            // python script + snapshot scenario (rather than existing artifact on ADB)
            string pythonScriptName = ParameterParser.TryGetParameterValue(parameters, "python_script_name");
            bool invalid = false;
            if (!string.IsNullOrEmpty(notebookPath))
            {
                if (!string.IsNullOrEmpty(pythonPath) ||
                    !string.IsNullOrEmpty(pythonParams) ||
                    !string.IsNullOrEmpty(pythonScriptName) ||
                    !string.IsNullOrEmpty(jarMainClassName) ||
                    !string.IsNullOrEmpty(jarParams))
                {
                    invalid = true;
                }
            }
            else if (!string.IsNullOrEmpty(pythonPath))
            {
                if (!string.IsNullOrEmpty(notebookPath) ||
                    (notebookParams != null) ||
                    !string.IsNullOrEmpty(pythonScriptName) ||
                    !string.IsNullOrEmpty(jarMainClassName) ||
                    !string.IsNullOrEmpty(jarParams))
                {
                    invalid = true;
                }
            }
            else if (!string.IsNullOrEmpty(pythonScriptName))
            {
                if (!string.IsNullOrEmpty(notebookPath) ||
                    (notebookParams != null) ||
                    !string.IsNullOrEmpty(pythonPath) ||
                    !pythonScriptName.EndsWith(".py") ||
                    !string.IsNullOrEmpty(jarMainClassName) ||
                    !string.IsNullOrEmpty(jarParams))
                {
                    invalid = true;
                }
            }
            else if (!string.IsNullOrEmpty(jarMainClassName))
            {
                if (!string.IsNullOrEmpty(notebookPath) ||
                    (notebookParams != null) ||
                    !string.IsNullOrEmpty(pythonPath) ||
                    !string.IsNullOrEmpty(pythonParams) ||
                    !string.IsNullOrEmpty(pythonScriptName))
                {
                    invalid = true;
                }
            }
            else
            {
                invalid = true;
            }

            if (invalid)
            {
                string errorMessage = "You must specify one of: " +
                    "notebook_path, python_script_path, script_name or main_class_name. " +
                    "Make sure to only add a snapshot to script_name tasks. \n" +
                    "Make sure to only specify the relevant parameter argument, for notebook_path; base_parameters " +
                    "for script_name or python_script_path; python_script_params and for main_class_name (JAR); jar_params. \n" +
                    "Note only python scripts are acceptable for the script_name parameter.";
                CommonLogger.LogEntityError(job.Id, errorMessage);
                throw new ArgumentException(errorMessage);
            }
        }
    }
}
