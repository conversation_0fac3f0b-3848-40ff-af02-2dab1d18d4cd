﻿using DataBricksCloudK8sService.DataBricksClient;
using Microsoft.Aether.DataBricksCloud.K8sService;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System.Collections.Generic;

namespace DataBricksCloudK8sService.Tests
{
    [TestClass]
    public class DataBricksParameterParserTest
    {
        [TestMethod]
        public void TryGetPythonPyPiOrRCranLibrariesTest()
        {
            string expectedInterfaceFormat =
                "package=library1package|repo=library1repo;package=library2package|repo=library2repo";
            string parameterName = "pypi_libraries";
            Dictionary<string, string> parameters = new Dictionary<string, string>();
            parameters.Add(parameterName, expectedInterfaceFormat);

            List<PythonPyPiOrRCranLibraryDto> libraries = DataBricksParameterParser.TryGetPythonPyPiOrRCranLibraries(parameters, parameterName);

            Assert.AreEqual(libraries.Count, 2);

            Assert.AreEqual(libraries[0].Package, "library1package");
            Assert.AreEqual(libraries[0].Repo, "library1repo");

            Assert.AreEqual(libraries[1].Package, "library2package");
            Assert.AreEqual(libraries[1].Repo, "library2repo");
        }

        [TestMethod]
        public void TryGetMavenLibrariesTest()
        {
            string expectedInterfaceFormat =
                "coordinates=library1coordinates|repo=library1repo|exclusions=library1exclusion1,library1exclusing2;" +
                "coordinates=library2coordinates|repo=library2repo|exclusions=library2exclusion1,library2exclusing2";
            string parameterName = "maven_libraries";
            Dictionary<string, string> parameters = new Dictionary<string, string>();
            parameters.Add(parameterName, expectedInterfaceFormat);

            List<MavenLibraryDto> libraries = DataBricksParameterParser.TryGetMavenLibraries(parameters, parameterName);

            Assert.AreEqual(libraries.Count, 2);

            Assert.AreEqual(libraries[0].Coordinates, "library1coordinates");
            Assert.AreEqual(libraries[0].Repo, "library1repo");
            Assert.AreEqual(libraries[0].Exclusions.Count, 2);
            Assert.AreEqual(libraries[0].Exclusions[0], "library1exclusion1");
            Assert.AreEqual(libraries[0].Exclusions[1], "library1exclusing2");

            Assert.AreEqual(libraries[1].Coordinates, "library2coordinates");
            Assert.AreEqual(libraries[1].Repo, "library2repo");
            Assert.AreEqual(libraries[1].Exclusions.Count, 2);
            Assert.AreEqual(libraries[1].Exclusions[0], "library2exclusion1");
            Assert.AreEqual(libraries[1].Exclusions[1], "library2exclusing2");
        }
    }
}
