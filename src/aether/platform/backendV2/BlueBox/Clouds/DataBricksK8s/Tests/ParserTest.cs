﻿using DataBricksCloudK8sService.DataBricksClient;
using Microsoft.Aether.DataBricksCloud.K8sService;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System.Collections.Generic;

namespace DataBricksCloudK8sService.Tests
{
    [TestClass]
    public class ParserTest
    {
        [TestMethod]
        public void ParseTest()
        {
            DataBricksJobMetadata job = new DataBricksJobMetadata
            {
                GraphDefaultCompute = new Microsoft.Aether.DataContracts.ComputeSetting { Name = "testcompute" }
            };
            Dictionary<string, string> metaParameters = new Dictionary<string, string>();
            Parser.Parse(job, metaParameters);
            Assert.AreEqual(false, job.PermitClusterRestart);

            metaParameters.Add(DataBricksConstants.RestartParam, null);
            Parser.Parse(job, metaParameters);
            Assert.AreEqual(false, job.PermitClusterRestart);

            metaParameters[DataBricksConstants.RestartParam] = "invalid";
            Parser.Parse(job, metaParameters);
            Assert.AreEqual(false, job.PermitClusterRestart);

            metaParameters[DataBricksConstants.RestartParam] = "true";
            Parser.Parse(job, metaParameters);
            Assert.AreEqual(true, job.PermitClusterRestart);

            metaParameters[DataBricksConstants.RestartParam] = "false";
            Parser.Parse(job, metaParameters);
            Assert.AreEqual(false, job.PermitClusterRestart);
        }

        [TestMethod]
        public void NeedEditExistingClusterTest()
        {
            SubmitRunDto submitRunDto = new SubmitRunDto{ };
            ClusterDto dynamicRunDto = null;
            bool needEdit = Parser.NeedEditExistingCluster(submitRunDto, null, dynamicRunDto);
            Assert.AreEqual(false, needEdit);

            submitRunDto = new SubmitRunDto { ExistingClusterId = "fake_cluster_Id" };
            needEdit = Parser.NeedEditExistingCluster(submitRunDto, null, dynamicRunDto);
            Assert.AreEqual(false, needEdit);

            OriginClusterSparkSetting originSetting = new OriginClusterSparkSetting
            {
                Conf = null
            };
            dynamicRunDto = new ClusterDto
            {
                SparkConf = null
            };
            needEdit = Parser.NeedEditExistingCluster(submitRunDto, originSetting, dynamicRunDto);
            Assert.AreEqual(false, needEdit);

            originSetting.Conf = new Dictionary<string, string> {
                    { "abc", "123"}
                };
            needEdit = Parser.NeedEditExistingCluster(submitRunDto, originSetting, dynamicRunDto);
            Assert.AreEqual(false, needEdit);

            dynamicRunDto.SparkConf = new Dictionary<string, string> { { "abc", "1234" } };
            needEdit = Parser.NeedEditExistingCluster(submitRunDto, originSetting, dynamicRunDto);
            Assert.AreEqual(true, needEdit);

            dynamicRunDto.SparkConf = new Dictionary<string, string> { { "abc", "123" } };
            needEdit = Parser.NeedEditExistingCluster(submitRunDto, originSetting, dynamicRunDto);
            Assert.AreEqual(false, needEdit);

            dynamicRunDto.SparkConf.Add("efg", "456");
            needEdit = Parser.NeedEditExistingCluster(submitRunDto, originSetting, dynamicRunDto);
            Assert.AreEqual(true, needEdit);
        }
    }
}
