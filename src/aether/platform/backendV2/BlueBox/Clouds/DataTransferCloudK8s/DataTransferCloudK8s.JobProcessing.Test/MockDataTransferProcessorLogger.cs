﻿using Microsoft.RelInfra.Instrumentation.Logging;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Threading.Tasks;

namespace DataTransferCloudK8s.JobProcessing.Test
{
    class MockDataTransferProcessorLogger : ICommonLogger
    {
        public void Dispose()
        {
            throw new NotImplementedException();
        }

        public void Flush()
        {
            throw new NotImplementedException();
        }

        public Task FlushAsync()
        {
            throw new NotImplementedException();
        }

        public void LogDebug(string message, params object[] args)
        {
            throw new NotImplementedException();
        }

        public void LogDebug(string message)
        {
            throw new NotImplementedException();
        }

        public void LogEntityDebug(string entityId, string message, params object[] args)
        {
            throw new NotImplementedException();
        }

        public void LogEntityDebug(string entityId, string message)
        {
            throw new NotImplementedException();
        }

        public void LogEntityError(string entityId, string message, params object[] args)
        {
            throw new NotImplementedException();
        }

        public void LogEntityError(string entityId, string message)
        {
            throw new NotImplementedException();
        }

        public void LogEntityInfo(string entityId, string message)
        {
            Debug.WriteLine(message);
        }

        public void LogEntityInfo(string entityId, string message, params object[] args)
        {
            Debug.WriteLine(message);
        }

        public void LogEntityWarning(string entityId, string message, params object[] args)
        {
            Debug.WriteLine(message);
        }

        public void LogEntityWarning(string entityId, string message)
        {
            throw new NotImplementedException();
        }

        public void LogError(string message, params object[] args)
        {
            throw new NotImplementedException();
        }

        public void LogError(string message)
        {
            throw new NotImplementedException();
        }

        public void LogInfo(string message, params object[] args)
        {
            throw new NotImplementedException();
        }

        public void LogInfo(string message)
        {
            throw new NotImplementedException();
        }

        public void LogMetric(string metric, long value, Dictionary<string, string> metricData = null)
        {
            throw new NotImplementedException();
        }

        public void LogWarning(string message, params object[] args)
        {
            throw new NotImplementedException();
        }

        public void LogWarning(string message)
        {
            throw new NotImplementedException();
        }
    }
}
