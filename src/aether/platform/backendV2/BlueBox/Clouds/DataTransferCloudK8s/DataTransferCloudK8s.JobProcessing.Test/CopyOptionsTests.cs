﻿using Microsoft.Aether.DataContracts.DataReferences;
using Microsoft.Aether.DataTransferCloud.JobProcessing;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Text;

namespace DataTransferCloudK8s.JobProcessing.Test
{
    public class CopyOptionsTests
    {
        [TestCase("file", "file", FileBasedPathType.File, FileBasedPathType.File)]
        [TestCase("file", "directory", FileBasedPathType.File, FileBasedPathType.Folder)]
        [TestCase("directory", "file", FileBasedPathType.Folder, FileBasedPathType.File)]
        [TestCase("directory", "directory", FileBasedPathType.Folder, FileBasedPathType.Folder)]
        public void GetCopyOperationOptions_Test(string source, string destination, FileBasedPathType sourceType, FileBasedPathType destType)
        {
            var copyOption = new CopyOptions();
            copyOption.SourceReferenceType = source;
            copyOption.DestinationReferenceType = destination;
            var copyOperationOption = CopyOptionsExtensions.GetCopyOperationOptions(copyOption);
            Assert.AreEqual(copyOperationOption.SourcePathType, sourceType);
            Assert.AreEqual(copyOperationOption.DestinationPathType, destType);
        }

        [TestCase("file", FileBasedPathType.File)]
        [TestCase("directory", FileBasedPathType.Folder)]
        [TestCase("file1", FileBasedPathType.Unknown)]
        public void TestGetPathType_ReferenceTypeDiff_Return(string input, FileBasedPathType expectedType)
        {
            Assert.AreEqual(CopyOptionsExtensions.GetPathType(input), expectedType);
        }
    }
}
