﻿using Microsoft.Aether.CloudCommon;
using Microsoft.Aether.DataTransferCloud.JobProcessing;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using static Microsoft.Aether.DataTransferCloud.JobProcessing.StateMachine;

namespace DataTransferCloudK8s.JobProcessing.Test
{
    public class StateMachineTests
    {
        private static string _myStatus;

        [SetUp]
        public void Init()
        {
            _myStatus = null;
        }

        [TestCase(JobStatus.Submitted, Events.CopyOperationSucceeded)]
        [TestCase(JobStatus.Failed, Events.CopyOperationCancelled)]
        [TestCase(JobStatus.Cancelling, Events.CopyOperationInProgress)]
        public void TestRaiseEventAsync_UnexpectedEvents_InvalidOperationException(JobStatus currentState, Events @event) 
        {
           Assert.ThrowsAsync<InvalidOperationException>(async() => await new StateMachine(currentState).RaiseEventAsync(@event).ConfigureAwait(false));         
        }

        [TestCase(JobStatus.Running, Events.CopyOperationFailed, JobStatus.Failed)]
        [TestCase(JobStatus.Submitted, Events.CopyOperationStarted, JobStatus.Running)]
        [TestCase(JobStatus.Cancelling, Events.CopyOperationCancelled, JobStatus.Cancelled)]
        public async Task TestRaiseEventAsync_SubscribeEvent_Return(JobStatus currentState, Events @event, JobStatus newState)
        {
            var machine = new StateMachine(currentState);
            machine.StateTransition += TestHandler;
            await machine.RaiseEventAsync(@event).ConfigureAwait(false);
            Assert.AreEqual($"Detected status change from {currentState} to {newState}", _myStatus);
        }

        [TestCase(JobStatus.Running, Events.CopyOperationFailed)]
        public async Task TestRaiseEventAsync_NoSubscriber_NoStatusChange(JobStatus currentState, Events @event)
        {
            var machine = new StateMachine(currentState);
            await machine.RaiseEventAsync(@event).ConfigureAwait(false);
            Assert.IsNull(_myStatus);
        }


        private static Task TestHandler(object sender, StateTransitionEventArgs args)
        {
             _myStatus = $"Detected status change from {args.OldState} to {args.CurrentState}";
            return Task.CompletedTask;
        }
    }
}
