﻿using Microsoft.Aether.DataTransfer.Cloud.Common.DataContracts;
using Microsoft.Aether.DataTransferCloud.JobProcessing.Actions;
using Microsoft.MachineLearning.PolicyService.Contracts;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Text;

namespace DataTransferCloudK8s.JobProcessing.Test.Actions
{
    [TestFixture]
    public class ChangePolicyValidationStatusTests
    {

        [Test]
        [Category("ChangeStatus")]
        public void TestChangeStatus_CommandEntityNotValid_InvalidOperationException()
        {
            DataTransferJobMetadata dummyDataTransferJobMetadata = new DataTransferJobMetadata()
            {
                CommandEntity = new DataTransferCommandEntity()
                {
                    DataCopyCommand = null
                }
            };

            Assert.IsNull(dummyDataTransferJobMetadata.CommandEntity.DataCopyCommand);
            var exp = Assert.Throws<InvalidOperationException>(() => ChangePolicyValidationStatus.ChangeStatus(dummyDataTransferJobMetadata, ValidationStatus.Succeeded));
            Assert.AreEqual("Trying to change PolicyValidationStatus for an unprocessed DataTransferJob", exp.Message);
        }

        [TestCase(ValidationStatus.Succeeded, PolicyValidationStatus.Completed)]
        [TestCase(ValidationStatus.InProgress, PolicyValidationStatus.InProgress)]
        [TestCase(ValidationStatus.SystemError, PolicyValidationStatus.Failed)]
        [TestCase(ValidationStatus.Failed, PolicyValidationStatus.Failed)]
        [Category("ChangeStatus")]
        public void TestChangeStatus_CommandEntityNotValid_InvalidOperationException(ValidationStatus validationStatus, PolicyValidationStatus policyValidationStatus)
        {
            DataTransferJobMetadata dummyDataTransferJobMetadata = new DataTransferJobMetadata()
            {
                CommandEntity = new DataTransferCommandEntity()
                {
                    DataCopyCommand = new DataTransferDataCopyCommand()
                    {
                        CopyOperationEntity = null
                    }
                }
            };

            Assert.IsNotNull(dummyDataTransferJobMetadata.CommandEntity.DataCopyCommand);
            ChangePolicyValidationStatus.ChangeStatus(dummyDataTransferJobMetadata, validationStatus);
            Assert.AreEqual(dummyDataTransferJobMetadata.CommandEntity.DataCopyCommand.PolicyValidationStatus, policyValidationStatus);
        }
    }
}
