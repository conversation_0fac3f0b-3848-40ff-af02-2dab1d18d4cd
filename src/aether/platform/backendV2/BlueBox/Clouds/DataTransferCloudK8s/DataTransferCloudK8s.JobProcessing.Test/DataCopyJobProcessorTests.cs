﻿using Common.Core.Contracts;
using Microsoft.Aether.BlueBox.DataStoreClient;
using Microsoft.Aether.BlueBox.RunHistoryClient;
using Microsoft.Aether.BlueBox.S2S.Common;
using Microsoft.Aether.BlueBox.WorkspaceResourcesClient;
using Microsoft.Aether.CloudCommon;
using Microsoft.Aether.CloudCommon.Worker;
using Microsoft.Aether.Clouds.CloudCommon;
using Microsoft.Aether.Clouds.CloudCommon.Cloudlets;
using Microsoft.Aether.Clouds.CloudCommon.JobQueueProcessing;
using Microsoft.Aether.DataContract.Backend.Clouds;
using Microsoft.Aether.DataTransfer.Cloud.Common;
using Microsoft.Aether.DataTransfer.Cloud.Common.DataContracts;
using Microsoft.Aether.DataTransferCloud.CopyService;
using Microsoft.Aether.DataTransferCloud.JobProcessing;
using Microsoft.Aether.DataTransferCloud.JobProcessing.Actions;
using Microsoft.Aether.S2S.Common;
using Microsoft.MachineLearning.Dataset.Client;
using Microsoft.MachineLearning.PolicyServiceClient;
using Microsoft.RelInfra.Common.Configuration;
using Microsoft.RelInfra.Common.Secrets;
using Microsoft.RelInfra.Extensions;
using Microsoft.RelInfra.Instrumentation;
using Microsoft.RelInfra.Instrumentation.Logging;
using Moq;
using NUnit.Framework;
using System;
using System.IO;
using System.Collections.Generic;
using System.Net;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Rest.Azure;
using Microsoft.MachineLearning.Pipeline.Common;

namespace DataTransferCloudK8s.JobProcessing.Test
{
    public class DataCopyJobProcessorTests
    {
        private readonly MockRepository _repository;

        private DataCopyJobProcessor _dummyDataCopyJobProcessor;

        private DataTransferCloudConfig _fakeDataTransferCloudConfig;
        private IRelInfraConfiguration _fakeIRelInfraConfiguration;
        private SecretProvider _dummySecretProvider;
        private ICommonLogger _fakeICommonLogger;
        private IJobActionFactory _fakeIJobActionFactory;
        private IStateTransitionActionFactory _fakeIStateTransitionActionFactory;
        private IRunStateUpdater _fakeIRunStateUpdater;
        private List<IStateTransitionAction> _dummyIStateTransitionActions;
        private IJobAction _fakeIJobAction;

        private DataCopyJobProcessorFactory _dummyDataCopyJobProcessorFactory;
        private IJobMetadataStorage<DataTransferJobMetadata> _dataTransferJobMetadataStorage;
        private IModuleResourceManagerFactory _fakeIModuleResourceManagerFactory;
        private IAmlWorkspaceMsiTokenProvider _fakeIAmlWorkspaceMsiTokenProvider;
        private IDataStoreClient _fakeIDataStoreClient;
        private CounterManager _dummyCounterManager;
        private IS2STokenProvider _fakeIS2STokenProvider;
        private IWorkspaceResourcesClient _fakeIWorkspaceResourcesClient;
        private IPolicyServiceClient _fakeIPolicyServiceClient;
        private IModuleResourceManager _fakeIModuleResourceManager;
        private IAzureMachineLearningDatasetManagementClientFactory _fakeDatasetClientFactory;
        private IRunHistoryClient _fakeRunHistoryClient;
        private bool _callUpdateRunError = false;
        private IErrorClassifier _fakeErrorClassifier;
        private IAmlSvcClientFactory _fakeAmlClientFactory;

        public DataCopyJobProcessorTests()
        {
            _repository = new MockRepository(MockBehavior.Loose) { DefaultValue = DefaultValue.Mock, CallBase = true };
        }

        [SetUp]
        public void Init()
        {
            _fakeIRelInfraConfiguration = _repository.Create<IRelInfraConfiguration>().Object;
            _dummySecretProvider = new SecretProvider();
            _fakeICommonLogger = _repository.Create<ICommonLogger>().Object;
            _fakeDataTransferCloudConfig = _repository.Create<DataTransferCloudConfig>(
                                _fakeIRelInfraConfiguration,
                                _dummySecretProvider).Object;
            _fakeIJobActionFactory = _repository.Create<IJobActionFactory>().Object;
            _fakeIStateTransitionActionFactory = _repository.Create<IStateTransitionActionFactory>().Object;
            _fakeIRunStateUpdater = _repository.Create<IRunStateUpdater>().Object;
            _dataTransferJobMetadataStorage = _repository.Create<IJobMetadataStorage<DataTransferJobMetadata>>().Object;
            _fakeIModuleResourceManagerFactory = _repository.Create<IModuleResourceManagerFactory>().Object;
            _fakeIAmlWorkspaceMsiTokenProvider = _repository.Create<IAmlWorkspaceMsiTokenProvider>().Object;
            _fakeIDataStoreClient = _repository.Create<IDataStoreClient>().Object;
            _dummyCounterManager = new CounterManager("dummyservice", _repository.Create<ICounterFactory>().Object);
            _fakeIS2STokenProvider = _repository.Create<IS2STokenProvider>().Object;
            _fakeIWorkspaceResourcesClient = _repository.Create<IWorkspaceResourcesClient>().Object;
            _fakeIPolicyServiceClient = _repository.Create<IPolicyServiceClient>().Object;
            _fakeIModuleResourceManager = _repository.Create<IModuleResourceManager>().Object;
            _fakeDatasetClientFactory = _repository.Create<IAzureMachineLearningDatasetManagementClientFactory>().Object;
            _fakeRunHistoryClient = _repository.Create<IRunHistoryClient>().Object;
            _fakeErrorClassifier = _repository.Create<IErrorClassifier>().Object;
            _fakeAmlClientFactory = _repository.Create<IAmlSvcClientFactory>().Object;

            _dummyIStateTransitionActions = new List<IStateTransitionAction>();
            Mock.Get(_fakeIStateTransitionActionFactory)
                .Setup(
                   service => service.GetStateTransitionActionsAsync(
                       It.IsAny<JobStatus>(),
                       It.IsAny<JobStatus>(),
                       It.IsAny<DataTransferJobMetadata>()
                       ))
                .Returns(Task.FromResult(_dummyIStateTransitionActions));

            _fakeIJobAction = _repository.Create<IJobAction>().Object;
            Mock.Get(_fakeIJobActionFactory)
                .Setup(
                   service => service.GetJobActionAsync(
                       It.IsAny<DataTransferJobMetadata>(),
                       It.IsAny<StateMachine>()
                       ))
                .Returns(Task.FromResult(_fakeIJobAction));

            Mock.Get(_fakeIJobAction)
                .Setup(
                   service => service.ExecuteAsync())
                .Returns(Task.CompletedTask);

            Mock.Get(_fakeIRunStateUpdater)
                .Setup(
                   service => service.UpdateRunErrorAsync(
                        It.IsAny<AMLJobMetadata>(),
                        It.IsAny<ErrorResponse>(),
                        It.IsAny<ICommonLogger>()
                       ))
                .Callback(() => { _callUpdateRunError = true; });

            Mock.Get(_fakeErrorClassifier).Setup(service => service.GetErrorCategoryInfo(It.IsAny<string>(), It.IsAny<string>())).Returns(It.IsAny<ErrorCategoryInfo>());

            _dummyDataCopyJobProcessor = new DataCopyJobProcessor(
                _fakeDataTransferCloudConfig,
                _fakeICommonLogger,
                _fakeIJobActionFactory,
                _fakeIStateTransitionActionFactory,
                _fakeIRunStateUpdater,
                _fakeErrorClassifier
                );

            _dummyDataCopyJobProcessorFactory = new DataCopyJobProcessorFactory(
                _dataTransferJobMetadataStorage,
                _fakeDataTransferCloudConfig,
                _fakeIRunStateUpdater,
                _fakeIModuleResourceManagerFactory,
                ".",
                _fakeIAmlWorkspaceMsiTokenProvider,
                _fakeIDataStoreClient,
                _dummyCounterManager,
                _fakeIS2STokenProvider,
                _fakeIWorkspaceResourcesClient,
                _fakeIPolicyServiceClient,
                _fakeDatasetClientFactory,
                _fakeRunHistoryClient,
                _fakeErrorClassifier,
                _fakeAmlClientFactory
                );
        }

        [Test]
        public async Task TestProcessJobAsync_Normal_Return()
        {
            DataTransferJobMetadata job = new DataTransferJobMetadata();
            var actualRet = await _dummyDataCopyJobProcessor.ProcessJobAsync(job).ConfigureAwait(false);
            Assert.AreEqual(job, actualRet);
        }

        [Test]
        public void TestProcessJobAsync_TooManyException_ThrowException()
        {
            var tooManyExp = new HttpRequestDetailException("test toomany exception", (HttpStatusCode)429);
            Mock.Get(_fakeIJobAction)
                .Setup(
                   service => service.ExecuteAsync())
                .Throws(tooManyExp);

            DataTransferJobMetadata job = new DataTransferJobMetadata()
            {
                Status = JobStatus.Running
            };
            var actualExp = Assert.ThrowsAsync<HttpRequestDetailException>(async () => await _dummyDataCopyJobProcessor.ProcessJobAsync(job).ConfigureAwait(false));
            Assert.AreEqual(tooManyExp.Message, actualExp.Message);
        }

        [Test]
        public void TestProcessJobAsync_RetriableException_NoException()
        {
            var retriableExp = new RetriableCopyOperationException("test retriable exception", null);
            Mock.Get(_fakeIJobAction)
                .Setup(
                   service => service.ExecuteAsync())
                .Throws(retriableExp);

            DataTransferJobMetadata job = new DataTransferJobMetadata()
            {
                Status = JobStatus.Completed
            };
            Assert.DoesNotThrowAsync(async () => await _dummyDataCopyJobProcessor.ProcessJobAsync(job).ConfigureAwait(false));
        }

        [Test]
        public void TestProcessJobAsync_RegularException_NoException()
        {
            var exp = new Exception("test exception");
            Mock.Get(_fakeIJobAction)
                .Setup(
                   service => service.ExecuteAsync())
                .Throws(exp);

            DataTransferJobMetadata job = new DataTransferJobMetadata()
            {
                ModuleInfo = new CloudModuleExecutionInfo(),
            };
            Assert.DoesNotThrowAsync(async () => await _dummyDataCopyJobProcessor.ProcessJobAsync(job).ConfigureAwait(false));
            Mock.Get(_fakeIRunStateUpdater)
                .Verify(
                   service => service.UpdateRunErrorAsync(
                        It.IsAny<AMLJobMetadata>(),
                        It.IsAny<ErrorResponse>(),
                        It.IsAny<ICommonLogger>()
                       ), Times.Once);
        }

        [Test]
        public async Task TestProcessJobAsync_WithRunError_NoException()
        {
            _callUpdateRunError = false;
            var _fakedDataTransferJobMetadata = new DataTransferJobMetadata
            {
                Status = JobStatus.Running,
                CommandEntity = new DataTransferCommandEntity()
                {
                    DataCopyCommand = new DataTransferDataCopyCommand()
                    {
                        CopyOperationEntity = Newtonsoft.Json.Linq.JObject.FromObject(new AsyncCopyOperationEntity()
                        {
                            DataFactoryPipelineName = "testPipelineName",
                            DataFactoryPipelineRunId = Guid.NewGuid().ToString(),
                            DataFactorySourceDatasetNames = new List<string>() { "testSourceDatasetName" },
                            DataFactorySinkDatasetName = "testSinkDatasetName",
                        }),
                        PolicyValidationStatus = PolicyValidationStatus.None
                    }
                },
                ModuleInfo = new CloudModuleExecutionInfo(),
            };
            var exp = new Exception("test exception");
            Mock.Get(_fakeIJobActionFactory)
                .Setup(
                   service => service.GetJobActionAsync(
                       It.IsAny<DataTransferJobMetadata>(),
                       It.IsAny<StateMachine>()
                       ))
                .Throws(exp);
            var realDataCopyJobProcessor = new DataCopyJobProcessor(
                _fakeDataTransferCloudConfig,
                _fakeICommonLogger,
                _fakeIJobActionFactory,
                _fakeIStateTransitionActionFactory,
                _fakeIRunStateUpdater,
                _fakeErrorClassifier
                );

            await realDataCopyJobProcessor.ProcessJobAsync(_fakedDataTransferJobMetadata);
            Assert.AreEqual(true, _callUpdateRunError);
        }

        [TestCase(CommandName.Copy)]
        [TestCase(CommandName.Wait)]
        public void TestCreateJobProcessor_NotDataCopyCommand_Exception(CommandName cmd)
        {
            Assert.Throws<InvalidOperationException>(() => _dummyDataCopyJobProcessorFactory.CreateJobProcessor(cmd, _fakeIModuleResourceManager, _fakeICommonLogger));
        }

        [TestCase(CommandName.DataCopy)]
        public void TestCreateJobProcessor_DataCopyCommand_Return(CommandName cmd)
        {
            var actualRet = _dummyDataCopyJobProcessorFactory.CreateJobProcessor(cmd, _fakeIModuleResourceManager, _fakeICommonLogger);
            Assert.IsInstanceOf<DataCopyJobProcessor>(actualRet);
        }

        [Test]
        public void TestProcessJobAsync_CloudException_ThrowException()
        {
            DataTransferJobMetadata job = new DataTransferJobMetadata()
            {
                ModuleInfo = new CloudModuleExecutionInfo()
                {
                    CloudSystem = AMLServiceTeams.DataTransferCloud.GetDescription()
                }
            };

            var dataFactoryResourcesLimitationExp = new CloudException("The document creation failed because datafactory has a limit of '5000' for maximum number of resources in the factory");
            Mock.Get(_fakeIJobAction)
                .Setup(
                   service => service.ExecuteAsync())
                .Throws(dataFactoryResourcesLimitationExp);

            Mock.Get(_fakeErrorClassifier)
                .Setup(
                    service => service.GetErrorCategoryInfo(
                        It.Is<string>(s => s.Contains("The document creation failed because datafactory has a limit of '5000' for maximum number of resources in the factory")), "ServiceError"))
                .Returns(new ErrorCategoryInfo("UserError", "Resource creation failed as ADF maximum number limitation", AMLServiceTeams.DataTransferCloud,
                         new string[] { "The document creation failed because datafactory has a limit", "for maximum number of resources in the factory" }));

            Assert.DoesNotThrowAsync(async () => await _dummyDataCopyJobProcessor.ProcessJobAsync(job).ConfigureAwait(false));
            Mock.Get(_fakeIRunStateUpdater)
                .Verify(
                   service => service.UpdateRunErrorAsync(
                        It.IsAny<AMLJobMetadata>(),
                        It.IsAny<ErrorResponse>(),
                        It.IsAny<ICommonLogger>()
                       ), Times.Once);

            Assert.AreEqual(RootErrorCode.UserError, job.ErrorResponse.Error.Code);
            Assert.AreEqual("Resource creation failed as ADF maximum number limitation", job.ErrorResponse.Error.ReferenceCode);
        }

        [Test]
        public void TestOverrideErrorMessageByCategory()
        {
            string value = "helloWorld";
            string errorMessage = value;
            ErrorCategoryInfo info = new ErrorCategoryInfo(
                "UserError",
                "path not found",
                AMLServiceTeams.DataTransferCloud,
                new string[] { "The document creation failed because datafactory has a limit", "for maximum number of resources in the factory" });

            _dummyDataCopyJobProcessor.OverrideErrorMessageByCategory(info, ref errorMessage);
            Assert.AreEqual(value, errorMessage);

            info = new ErrorCategoryInfo(
                "UserError",
                "Resource creation failed as ADF maximum number limitation",
                AMLServiceTeams.DataTransferCloud,
                new string[] { "The document creation failed because datafactory has a limit", "for maximum number of resources in the factory" });

            _dummyDataCopyJobProcessor.OverrideErrorMessageByCategory(info, ref errorMessage);
            Assert.AreEqual("Quota limitation issue: The resource creation failed because datafactory has maximum number limitation of resources in the factory (reference link: https://docs.microsoft.com/en-us/azure/azure-resource-manager/management/azure-subscription-service-limits#data-factory-limits).", errorMessage);

        }

        private JobActionFactory GetDummyJobActionFactory()
        {
            var _fakeConfiguration = _repository.Create<DataTransferCloudConfig>(
                _fakeIRelInfraConfiguration,
                _dummySecretProvider).Object;
            Mock.Get(_fakeConfiguration)
                    .Setup(
                       service => service.EnablePolicyValidation(
                           It.IsAny<string>()))
                    .Returns(true);

            var _fakeIAsyncCopyServiceFactory = _repository.Create<IAsyncCopyServiceFactory>().Object;
            var _fakeDataStoreClient = _repository.Create<IDataStoreClient>().Object;
            var _fakeIAsyncCopyService = _repository.Create<IAsyncCopyService>().Object;
            Mock.Get(_fakeIAsyncCopyService)
                .Setup(
                service => service.CheckCopyOperationStatusAsync(
                    It.IsAny<AsyncCopyOperationEntity>()
                    ))
                .Returns(Task.FromResult(
                    new AsyncCopyOperationStatusEntity
                    {
                        Status = AsyncCopyOperationStatus.Failed,
                        Error = "mocked error details"
                    }));
            Mock.Get(_fakeIAsyncCopyServiceFactory)
                .Setup(
                service => service.CreateCopyServiceAsync(
                It.IsAny<DataTransferJobMetadata>()))
                .Returns(Task.FromResult(_fakeIAsyncCopyService));

            return new JobActionFactory(
                _fakeConfiguration,
                _fakeIModuleResourceManagerFactory,
                ".",
                _fakeICommonLogger,
                _fakeIAsyncCopyServiceFactory,
                _fakeIPolicyServiceClient,
                _fakeDatasetClientFactory,
                _fakeRunHistoryClient,
                _fakeDataStoreClient,
                _fakeErrorClassifier,
                _fakeAmlClientFactory);
        }
    }
}
