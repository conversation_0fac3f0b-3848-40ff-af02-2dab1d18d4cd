﻿using Microsoft.Aether.BlueBox.DataStoreClient;
using Microsoft.Aether.BlueBox.RunHistoryClient;
using Microsoft.Aether.CloudCommon;
using Microsoft.Aether.Clouds.CloudCommon;
using Microsoft.Aether.CloudCommon.Worker;
using Microsoft.Aether.DataContract.Backend.Clouds;
using Microsoft.Aether.DataContracts;
using Microsoft.Aether.DataContracts.Entity;
using Microsoft.Aether.DataTransfer.Cloud.Common;
using Microsoft.Aether.DataTransfer.Cloud.Common.DataContracts;
using Microsoft.Aether.DataTransfer.Cloud.Common.Exceptions;
using Microsoft.Aether.DataTransferCloud.CopyService;
using Microsoft.Aether.DataTransferCloud.JobProcessing;
using Microsoft.Aether.DataTransferCloud.JobProcessing.Actions;
using Microsoft.MachineLearning.Dataset.Client;
using Microsoft.MachineLearning.PolicyServiceClient;
using Microsoft.RelInfra.Common.Configuration;
using Microsoft.RelInfra.Common.Secrets;
using Microsoft.RelInfra.Instrumentation.Logging;
using Moq;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using static Microsoft.Aether.DataTransferCloud.JobProcessing.Actions.JobActionFactory;
using Microsoft.MachineLearning.Pipeline.Common;

namespace DataTransferCloudK8s.JobProcessing.Test.Actions
{
    public class JobActionFactoryTests
    {
        private readonly MockRepository _repository;

        private JobActionFactory _dummyJobActionFactory;
        private DataTransferCloudConfig _fakeConfiguration;
        private IRelInfraConfiguration _fakeIRelInfraConfiguration;
        private SecretProvider _dummySecretProvider;
        private IModuleResourceManagerFactory _fakeIModuleResourceManagerFactory;
        private ICommonLogger _fakeICommonLogger;
        private IAsyncCopyServiceFactory _fakeIAsyncCopyServiceFactory;
        private IPolicyServiceClient _fakeIPolicyServiceClient;
        private IAsyncCopyService _fakeIAsyncCopyService;
        private IModuleResourceManager _fakeIModuleResourceManager;
        private DataTransferJobMetadata _dummyDataTransferJobMetadata;
        private StateMachine _fakeStateMachine;
        private IAzureMachineLearningDatasetManagementClientFactory _fakeDatasetClientFactory;
        private IRunHistoryClient _fakeRunHistoryClient;
        private IDataStoreClient _fakeDataStoreClient;
        private IErrorClassifier _fakeErrorClassifier;
        private IAmlSvcClientFactory _fakeAmlClientFactory;

        public JobActionFactoryTests()
        {
            _repository = new MockRepository(MockBehavior.Loose) { DefaultValue = DefaultValue.Mock, CallBase = true };
        }

        [SetUp]
        public void Init()
        {
            _fakeIRelInfraConfiguration = _repository.Create<IRelInfraConfiguration>().Object;
            _dummySecretProvider = new SecretProvider();
            _fakeIModuleResourceManagerFactory = _repository.Create<IModuleResourceManagerFactory>().Object;
            _fakeIAsyncCopyServiceFactory = _repository.Create<IAsyncCopyServiceFactory>().Object;

            _fakeConfiguration = _repository.Create<DataTransferCloudConfig>(
                _fakeIRelInfraConfiguration,
                _dummySecretProvider).Object;
            _fakeICommonLogger = _repository.Create<ICommonLogger>().Object;
            _fakeIPolicyServiceClient = _repository.Create<IPolicyServiceClient>().Object;
            _fakeIAsyncCopyService = _repository.Create<IAsyncCopyService>().Object;
            _fakeStateMachine = _repository.Create<StateMachine>(JobStatus.Submitted).Object;
            _fakeDatasetClientFactory = _repository.Create<IAzureMachineLearningDatasetManagementClientFactory>().Object;
            _fakeRunHistoryClient = _repository.Create<IRunHistoryClient>().Object;
            _fakeDataStoreClient = _repository.Create<IDataStoreClient>().Object;
            _fakeErrorClassifier = _repository.Create<IErrorClassifier>().Object;
            _fakeAmlClientFactory = _repository.Create<IAmlSvcClientFactory>().Object;

            Mock.Get(_fakeErrorClassifier).Setup(service => service.GetErrorCategoryInfo(It.IsAny<string>(), It.IsAny<string>())).Returns(It.IsAny<ErrorCategoryInfo>());

            _dummyJobActionFactory = new JobActionFactory(
                _fakeConfiguration,
                _fakeIModuleResourceManagerFactory,
                ".",
                _fakeICommonLogger,
                _fakeIAsyncCopyServiceFactory,
                _fakeIPolicyServiceClient,
                _fakeDatasetClientFactory,
                _fakeRunHistoryClient,
                _fakeDataStoreClient,
                _fakeErrorClassifier,
                _fakeAmlClientFactory);

            Mock.Get(_fakeIAsyncCopyServiceFactory)
                .Setup(
                   service => service.CreateCopyServiceAsync(
                       It.IsAny<DataTransferJobMetadata>()))
                .Returns(Task.FromResult(_fakeIAsyncCopyService));

            _fakeIModuleResourceManager = _repository.Create<IModuleResourceManager>().Object;
            Mock.Get(_fakeIModuleResourceManagerFactory)
                .Setup(
                   service => service.Create(
                       It.IsAny<string>(),
                       It.IsAny<string>(),
                       It.IsAny<string>(),
                       It.IsAny<WorkspaceIdentity>(),
                       It.IsAny<CreatedBy>()))
                .Returns(_fakeIModuleResourceManager);

            _dummyDataTransferJobMetadata = new DataTransferJobMetadata()
            {
                Id = Guid.NewGuid().ToString(),
                RunId = Guid.NewGuid().ToString(),
                WorkspaceIdentity = new WorkspaceIdentity()
                {
                    WorkspaceId = Guid.NewGuid().ToString()
                },
                CreatedBy = new CreatedBy(),
                CommandEntity = new DataTransferCommandEntity()
                {
                    DataCopyCommand = new DataTransferDataCopyCommand()
                    {
                        PolicyValidationStatus = PolicyValidationStatus.Completed,
                        SourceDataId = "input",
                        DestinationDataId = "output"
                    }
                },
                InputInfos = new List<CloudModuleInputExecutionInfo> {
                 new CloudModuleInputExecutionInfo() {
                  DataId="input",
                  DataLocation = new DataLocation() { AmlDataset=null }
                 },
                 new CloudModuleInputExecutionInfo() {
                   DataId= "output",
                   DataLocation = new DataLocation() { AmlDataset=null }
                 }
                }
            };
        }
        [TestCase(JobStatus.Submitted, PolicyValidationStatus.None, typeof(SubmitApprovalAction))]
        [TestCase(JobStatus.Submitted, PolicyValidationStatus.InProgress, typeof(CheckApprovalStatusAction))]
        [TestCase(JobStatus.Submitted, PolicyValidationStatus.Completed, typeof(SubmitJobAction))]
        [TestCase(JobStatus.Running, PolicyValidationStatus.None, typeof(CheckJobStatusAction))]
        [TestCase(JobStatus.Cancelling, PolicyValidationStatus.None, typeof(CancelJobAction))]
        [TestCase(JobStatus.Completed, PolicyValidationStatus.None, typeof(NullAction))]
        [TestCase(JobStatus.Failed, PolicyValidationStatus.None, typeof(NullAction))]
        [TestCase(JobStatus.Cancelled, PolicyValidationStatus.None, typeof(NullAction))]
        [Category("GetJobActionAsync")]
        public async Task TestGetJobActionAsync_JobStatus_ReturnType(JobStatus status, PolicyValidationStatus validationStatus, Type t)
        {
            bool enablePolicyValidation = true;
            Mock.Get(_fakeConfiguration)
                .Setup(
                   service => service.EnablePolicyValidation(
                       It.IsAny<string>()))
                .Returns(enablePolicyValidation);

            _dummyDataTransferJobMetadata.Status = status;
            _dummyDataTransferJobMetadata.CommandEntity.DataCopyCommand.PolicyValidationStatus = validationStatus;
            var actualRet = await _dummyJobActionFactory.GetJobActionAsync(_dummyDataTransferJobMetadata, _fakeStateMachine).ConfigureAwait(false);
            Assert.That(actualRet, Is.TypeOf(t));
        }

        [TestCase(JobStatus.Submitted, PolicyValidationStatus.Failed, "Invalid PolicyValidationStatus:")]
        [TestCase(JobStatus.Assigned, PolicyValidationStatus.Failed, "Unexpected job status")]
        [Category("GetJobActionAsync")]
        public void TestGetJobActionAsync_ThrowException_InvalidJobStateException(JobStatus status, PolicyValidationStatus validationStatus, string info)
        {
            bool enablePolicyValidation = true;
            Mock.Get(_fakeConfiguration)
                .Setup(
                   service => service.EnablePolicyValidation(
                       It.IsAny<string>()))
                .Returns(enablePolicyValidation);

            _dummyDataTransferJobMetadata.Status = status;
            _dummyDataTransferJobMetadata.CommandEntity.DataCopyCommand.PolicyValidationStatus = validationStatus;
            var actualExp = Assert.ThrowsAsync<InvalidJobStateException>(async () => await _dummyJobActionFactory.GetJobActionAsync(_dummyDataTransferJobMetadata, _fakeStateMachine).ConfigureAwait(false));
            StringAssert.Contains(info, actualExp.Message);
        }
    }
}
