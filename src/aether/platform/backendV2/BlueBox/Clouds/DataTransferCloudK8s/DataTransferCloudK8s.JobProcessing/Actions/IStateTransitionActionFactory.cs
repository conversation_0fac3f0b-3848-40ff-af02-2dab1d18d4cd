﻿using Microsoft.Aether.CloudCommon;
using Microsoft.Aether.DataTransfer.Cloud.Common.DataContracts;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Microsoft.Aether.DataTransferCloud.JobProcessing.Actions
{
    public interface IStateTransitionActionFactory
    {
        Task<List<IStateTransitionAction>> GetStateTransitionActionsAsync(
            JobStatus previousState, 
            JobStatus currentState,
            DataTransferJobMetadata job);
    }
}
