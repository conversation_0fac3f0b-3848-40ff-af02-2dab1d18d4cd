﻿using Microsoft.Aether.Clouds.CloudCommon;
using Microsoft.Aether.DataContracts.DataReferences;
using Microsoft.Aether.DataContracts.DataManagement;
using Microsoft.Aether.DataTransfer.Cloud.Common.DataContracts;
using Microsoft.Aether.DataTransferCloud.CopyService;
using Microsoft.RelInfra.Common.Exceptions;
using Microsoft.RelInfra.Instrumentation.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Aether.BackendCommon.Logging;
using Microsoft.Aether.DataContracts;
using Microsoft.Aether.Clouds.CloudCommon.DatasetAdapter;
using Microsoft.Aether.BlueBox.Clouds.Common;
using Microsoft.Aether.BlueBox.RunHistoryClient;
using Microsoft.MachineLearning.Common.Core;
using Microsoft.MachineLearning.Pipeline.Common;
using Microsoft.Aether.DataContracts.CloudSystem;
using Microsoft.MachineLearning.RunHistory.Contracts;
using Microsoft.Aether.DataContract.Backend.Clouds;
using Microsoft.Aether.DataTransferCloud.JobProcessing.Utilities;

namespace Microsoft.Aether.DataTransferCloud.JobProcessing.Actions
{
    internal class SubmitJobAction : IJobAction
    {
        private readonly DataTransferJobMetadata _job;
        private readonly ModuleHelper _moduleHelper;
        private readonly ICommonLogger _dualLogger;
        private readonly StateMachine _stateMachine;
        private readonly IAsyncCopyService _asyncCopyService;
        private readonly DataSetFileHelper _dataSetFileHelper;
        private readonly IDataSetAdapter _datasetAdapter;
        private readonly IRunHistoryClient _runHistoryClient;
        private readonly IAmlSvcClientFactory _amlSvcClientFactory;

        internal SubmitJobAction(
            ModuleHelper moduleHelper,
            ICommonLogger dualLogger,
            StateMachine stateMachine,
            DataTransferJobMetadata job,
            IAsyncCopyService asyncCopyService,
            DataSetFileHelper datasetFileHelper,
            IDataSetAdapter datasetAdapter,
            IRunHistoryClient runHistoryClient,
            IAmlSvcClientFactory amlSvcClientFactory)
        {
            _moduleHelper = moduleHelper ?? throw new ArgumentNullException(nameof(moduleHelper));
            _dualLogger = dualLogger ?? throw new ArgumentNullException(nameof(dualLogger));
            _stateMachine = stateMachine ?? throw new ArgumentNullException(nameof(stateMachine));
            _job = job ?? throw new ArgumentNullException(nameof(job));
            _asyncCopyService = asyncCopyService ?? throw new ArgumentNullException(nameof(asyncCopyService));
            _dataSetFileHelper = datasetFileHelper ?? throw new ArgumentNullException(nameof(datasetFileHelper));
            _datasetAdapter = datasetAdapter;
            _runHistoryClient = runHistoryClient ?? throw new ArgumentNullException(nameof(runHistoryClient));
            _amlSvcClientFactory = amlSvcClientFactory ?? throw new ArgumentNullException(nameof(amlSvcClientFactory));
        }

        public async Task ExecuteAsync()
        {
            await _moduleHelper.UpdateRunHistoryComputeTargetAsync(_job);
            DataTransferDataCopyCommand command = _job.CommandEntity.DataCopyCommand;

            try
            {
                PipelineResourceBuilderMetadata metadata;

                if (_job.Version < (int)DataTransferModuleVersions.Version3)
                {
                    DataReference sourceDataReference = _moduleHelper.GetDataReferenceFromInputId(_job, command.SourceDataId);
                    DataReference destinationDataReference = _moduleHelper.GetDataReferenceFromInputId(_job, command.DestinationDataId);

                    string databaseMetricsSourceLogInfo = CreateDatabaseMetricsLogInfo(sourceDataReference, "Source");
                    string databaseMetricsSinkLogInfo = CreateDatabaseMetricsLogInfo(destinationDataReference, "Sink");

                    CommonLogger.LogEntityInfo(_job.Id, "Data Transfer job metrics: "
                                 + $"SubscriptionId={_job.WorkspaceIdentity.SubscriptionId}, "
                                 + $"WorkspaceId={_job.WorkspaceIdentity.WorkspaceId}, "
                                 + $"JobId={_job.Id}, "
                                 + $"RunId={_job.RunId}, "
                                 + $"SourceType={sourceDataReference.Type}, "
                                 + $"SinkType={destinationDataReference.Type}"
                                 + $"{databaseMetricsSourceLogInfo}"
                                 + $"{databaseMetricsSinkLogInfo}");

                    CopyOperationOptions options = GetCopyOperationOptions(command.CopyOptions);

                    metadata = new PipelineResourceBuilderMetadata
                    {
                        Sources = new List<PipelineResourceInfo>()
                        {
                            new PipelineResourceInfo
                            {
                                DataReference = sourceDataReference,
                                PathType = options?.SourcePathType ?? FileBasedPathType.Unknown,
                            },
                        },
                        Sink = new PipelineResourceInfo
                        {
                            DataReference = destinationDataReference,
                            PathType = options?.DestinationPathType ?? FileBasedPathType.Unknown,
                        },
                    };

                }
                else
                {
                    IList<bool> skipProcessingsOfInPorts = _moduleHelper.GetSkipProcessingFromInput(_job);
                    IList<bool> skipProcessingsOfOutPorts = _moduleHelper.GetSkipProcessingFromOutput(_job);
                    _moduleHelper.ValidateSkipProcessingStatus(skipProcessingsOfInPorts, skipProcessingsOfOutPorts);

                    if (_job.CloudSettings?.DataTransferCloudConfig?.AllowOverwrite == false)
                    {
                        // Currently we only support one type of data structure as input and output
                        // And the input and output types are consistent
                        // We use the output structure as the basis for judging file-conlict
                        CloudModuleOutputExecutionInfo outputInfo = _job.OutputInfos.First();
                        var executionInterfaceOutputSettingsList = _job.ExecutionStructuredInterface.Outputs.
                            Where(pair => !pair.Key.SkipProcessing && pair.Key.Name.Equals(outputInfo?.Name, StringComparison.OrdinalIgnoreCase));
                        var executionInterfaceOutputSettings = executionInterfaceOutputSettingsList.First();

                        if (executionInterfaceOutputSettings.Key.DatasetOutput != null)
                        {
                            var datasetList = new List<AmlDataset>();

                            // Get input dataset
                            _dualLogger.LogEntityInfo(_job.Id, "Extracting processing input datasets from ExecutionStructuredInterface");
                            var sourceDatasets = _moduleHelper.GetProcessingDatasetFromInput(_job);

                            // Get output dataset
                            _dualLogger.LogEntityInfo(_job.Id, "Extracting processing output datasets from ExecutionStructuredInterface");
                            var destinationDatasets = await ExtractProcessingOutputDatasetAsync();

                            // Check file conflicts
                            _dualLogger.LogEntityInfo(_job.Id, "Checking inputs and output file conflicts");
                            var conflictFiles = await _dataSetFileHelper.GetDataSetConflictFilesAsync(sourceDatasets.Concat(destinationDatasets).ToList(), _job);

                            if (conflictFiles.Any())
                            {
                                var msg = "Job submission failed: File conflicts are detected in datasets";
                                _dualLogger.LogEntityError(_job.Id, msg);
                                if (_dualLogger is JobDualLogger logger)
                                {
                                    logger.StdOutOnlyLogger.LogEntityError(_job.Id, $"Files below have been detected in multiple storages.\n {string.Join("\n", conflictFiles)}");
                                }

                                throw new Exception(msg);
                            }
                        }
                        else if (executionInterfaceOutputSettings.Key.AssetOutputSettings?.Path != null)
                        {
                            // Check asset file conflicts
                            _dualLogger.LogEntityError(_job.Id, $"Start the asset file conflict check");

                            //
                            // Resolve AssetType
                            IDictionary<string, AssetType?> inputAssetTypes = _moduleHelper.ResolveInputAssetTypes(_job);
                            IDictionary<string, AssetType?> outputAssetTypes = _moduleHelper.ResolveOutputAssetTypes(_job);

                            // Resolve AssetId
                            IDictionary<string, AssetId?> inputAssetIds = _moduleHelper.ResolveInputAssetIds(_job);
                            IDictionary<string, AssetId?> outputAssetIds = await _moduleHelper.ResolveOutputAssetIds(_job);

                            var assetIdList = new List<IDictionary<string, AssetId>>() { inputAssetIds, outputAssetIds };
                            var assetIdDict = assetIdList.SelectMany(dict => dict).ToDictionary(pair => pair.Key, pair => pair.Value);

                            var assetTypeList = new List<IDictionary<string, AssetType?>>() { inputAssetTypes, outputAssetTypes };
                            var assetTypeDict = assetTypeList.SelectMany(dict => dict).ToDictionary(pair => pair.Key, pair => pair.Value);
                            await DataCopyConflictDetector.CheckConflictAsync(
                                assetIdDict: assetIdDict,
                                assetTypeDict: assetTypeDict,
                                job: _job,
                                amlSvcClientFactory: _amlSvcClientFactory,
                                createdBy: _job.CreatedBy,
                                dualLogger: _dualLogger,
                                earlyReturn: true);
                        }
                    }
                    else
                    {
                        _dualLogger.LogEntityInfo(_job.Id, "Confliction check is skipped due to DataTransferCloudConfig.AllowOverwrite is true");
                    }

                    IList<DataReference> sourceDataReferences = _moduleHelper.GetAllDataReferencesFromInput(_job);
                    IList<DataReference> destinationDataReference = _moduleHelper.GetAllDataReferencesFromOutput(_job);

                    var input_types = string.Join(",", sourceDataReferences.Where((info, index) => !skipProcessingsOfInPorts[index]).Select(source => source.Type).ToList());
                    var output_types = string.Join(",", destinationDataReference.Select(sink => sink.Type).ToList());
                    CommonLogger.LogEntityInfo(_job.Id, "Data Transfer job metrics: "
                        + $"SubscriptionId={_job.WorkspaceIdentity.SubscriptionId}, "
                        + $"WorkspaceId={_job.WorkspaceIdentity.WorkspaceId}, "
                        + $"JobId={_job.Id}, "
                        + $"RunId={_job.RunId}, "
                        + $"SourceType=[{input_types}], "
                        + $"SinkType =[{output_types}]");

                    metadata = new PipelineResourceBuilderMetadata
                    {
                        Sources = sourceDataReferences
                                    .Select((sourceDataReference, index) => new PipelineResourceInfo
                                    {
                                        DataReference = sourceDataReference,
                                        PathType = _job.CommandEntity.DataCopyCommand.InPortDataTypes[index]
                                    }).Where((info, index) => !skipProcessingsOfInPorts[index]).ToList(),
                        Sink = new PipelineResourceInfo
                        {
                            DataReference = destinationDataReference.FirstOrDefault(),
                            PathType = _job.CommandEntity.DataCopyCommand.OutPortDataType
                        },
                    };
                }

                var src_path_type = metadata.Sources.Select(src => src.PathType).ToArray();
                _dualLogger.LogEntityInfo(_job.Id, "Build PipelineResourceBuilderMetadata (exclude skipProcessing port) source = [{src}], destination = [{dest}]", string.Join(",", src_path_type), metadata.Sink.PathType);

                AsyncCopyOperationEntity entity = await _asyncCopyService.StartCopyOperationAsync(metadata);
                command.CopyOperationEntity = entity.ToStorageContract();
                // Filter skip input
                List<String> validInputNames = _moduleHelper.GetValidInputNames(_job);
                //update RunHistory InputAsset
                await InputDataLocationUtils.UpdateAssetInputsToRunDtoAsync(_job, _runHistoryClient, validInputNames);
                //update RunHistory Inputdatasets 
                await _datasetAdapter.UpdateRunHistoryInputDatasetsAsync(_job);
            }
            catch (Exception ex) when (ex.IsRetriableException())
            {
                _dualLogger.LogEntityWarning(_job.Id, "Data transfer job could not be submitted due to a temporary error; will retry. Exception: {exception}; Type: {exception_type}", ex, ex.GetInnerTypes());
                await _stateMachine.RaiseEventAsync(StateMachine.Events.CopyOperationTransientFailure);
                return;
            }

            JobReportingLogger.LogComputeTarget(
                logger: _dualLogger,
                    jobId: _job.Id,
                    runId: _job.RunId,
                    parentRunId: _job.PipelineRunId ?? _job.ExperimentId,
                    computeTarget: "ADF");

            await _stateMachine.RaiseEventAsync(StateMachine.Events.CopyOperationStarted);
        }

        private string CreateDatabaseMetricsLogInfo(DataReference dataReference, string type)
        {
            string tableName = "";
            string sqlQuery = "";
            string storedProcedureName = "";
            string storedProcedureParametersCount = "";
            IDictionary<string, string> jobDatabaseMetrics = new Dictionary<string, string>();
            try
            {
                switch (dataReference.Type)
                {
                    case DataReferenceType.AzureSqlDatabase:
                        jobDatabaseMetrics = GetJobDatabaseMetrics(dataReference.AzureSqlDatabaseReference);
                        break;
                    case DataReferenceType.AzurePostgresDatabase:
                        jobDatabaseMetrics = GetJobDatabaseMetrics(dataReference.AzurePostgresDatabaseReference);
                        break;
                    case DataReferenceType.AzureMySqlDatabase:
                        jobDatabaseMetrics = GetJobDatabaseMetrics(dataReference.AzureMySqlDatabaseReference);
                        break;
                }

                tableName = string.IsNullOrEmpty(jobDatabaseMetrics["tableName"]) ? "" : $", {type}TableName={jobDatabaseMetrics["tableName"]}";
                sqlQuery = string.IsNullOrEmpty(jobDatabaseMetrics["sqlQuery"]) ? "" : $", {type}SqlQuery={jobDatabaseMetrics["sqlQuery"]}";
                storedProcedureName = string.IsNullOrEmpty(jobDatabaseMetrics["storedProcedureName"]) ? "" : $", {type}StoredProcedureName={jobDatabaseMetrics["storedProcedureName"]}";
                storedProcedureParametersCount = string.IsNullOrEmpty(storedProcedureName) ? ""
                    : $", {type}StoredProcedureParametersCount={jobDatabaseMetrics["storedProcedureParametersCount"]}";
            }
            catch (Exception ex)
            {
                CommonLogger.LogEntityError(_job.Id, $"Failed to update databaseMetrics variables, exception:{ex}");
            }

            return $"{storedProcedureName + storedProcedureParametersCount + tableName + sqlQuery}";
        }

        private IDictionary<string, string> GetJobDatabaseMetrics(AzureDatabaseReference databaseReference)
        {
            var jobDatabaseMetrics = new Dictionary<string, string>
            {
                { "tableName", string.IsNullOrEmpty(databaseReference?.TableName) ? "" : databaseReference?.TableName?.Length.ToString() },
                { "sqlQuery", string.IsNullOrEmpty(databaseReference?.SqlQuery) ? "" : databaseReference?.SqlQuery?.Length.ToString() },
                { "storedProcedureName", string.IsNullOrEmpty(databaseReference?.StoredProcedureName) ? "" : databaseReference?.StoredProcedureName?.Length.ToString() },
                {
                    "storedProcedureParametersCount",
                    databaseReference?.StoredProcedureParameters?.Count == null ? "0" :
                        (databaseReference?.StoredProcedureParameters?.Count) + ""
                }
            };

            return jobDatabaseMetrics;
        }

        private async Task<IList<AmlDataset>> ExtractProcessingOutputDatasetAsync()
        {
            var datasets = new List<AmlDataset>();
            foreach (var output in _job.ExecutionStructuredInterface.Outputs.Where(pair => !pair.Key.SkipProcessing))
            {
                var dataset = new AmlDataset()
                {
                    SavedDataSetReference = new SavedDataSetReference()
                    {
                        Id = await _dataSetFileHelper.GetSavedDatasetIdFromOutputAsync(_job, output),
                    }
                };

                datasets.Add(dataset);
            }

            return datasets;
        }

        private CopyOperationOptions GetCopyOperationOptions(string serializedOptions)
        {
            if (string.IsNullOrEmpty(serializedOptions))
            {
                return null;
            }

            return JsonConvert.DeserializeObject<CopyOptions>(serializedOptions)
                .GetCopyOperationOptions();
        }
    }
}
