<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <ProductVersion>8.0.30703</ProductVersion>
    <TargetFramework>net8.0</TargetFramework>
    <ResolveAssemblyWarnOrErrorOnTargetArchitectureMismatch>None</ResolveAssemblyWarnOrErrorOnTargetArchitectureMismatch>
    <RootNamespace>Microsoft.Aether.DataTransferCloud.JobProcessing</RootNamespace>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="System.Net.Security" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\..\..\..\RelInfra\RelInfra.Instrumentation.Library\src\Microsoft.RelInfra.Instrumentation.csproj" />
    <ProjectReference Include="..\..\..\..\Clouds\Common\Worker\Microsoft.Aether.CloudCommon.Worker.csproj" />
    <ProjectReference Include="..\..\..\..\Clouds\DataTransfer\CloudCommon\DataTransferCloudCommon.csproj" />
    <ProjectReference Include="..\..\..\DataStoreClient\Microsoft.Aether.BlueBox.DataStoreClient\Microsoft.Aether.BlueBox.DataStoreClient.csproj" />
    <ProjectReference Include="..\..\..\PolicyService\Microsoft.MachineLearning.PolicyServiceClient\Microsoft.MachineLearning.PolicyServiceClient.csproj" />
    <ProjectReference Include="..\..\Clouds.Common\Microsoft.Aether.BlueBox.Clouds.Common.csproj" />
    <ProjectReference Include="..\..\Microsoft.Aether.BlueBox.Common.DataAdapter\Microsoft.Aether.BlueBox.Clouds.Common.DataAdapter.csproj" />
    <ProjectReference Include="..\DataTransferCloudK8s.CopyService\DataTransferCloudK8s.CopyService.csproj" />
  </ItemGroup>
</Project>
