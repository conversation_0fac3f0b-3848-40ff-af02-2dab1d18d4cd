﻿using Microsoft.Aether.BlueBox.Clouds.Common;
using Microsoft.Aether.BlueBox.RunHistoryClient;
using Microsoft.Aether.BlueBox.Storage;
using Microsoft.Aether.CloudCommon.Worker;
using Microsoft.Aether.DataContract.Backend.Clouds;
using Microsoft.Aether.DataContracts;
using Microsoft.Aether.DataContracts.CloudSystem;
using Microsoft.Aether.DataContracts.DataManagement;
using Microsoft.Aether.DataContracts.DataReferences;
using Microsoft.Aether.DataTransfer.Cloud.Common.DataContracts;
using Microsoft.Aether.DataTransfer.Cloud.Common.Exceptions;
using Microsoft.MachineLearning.Common.Core;
using Microsoft.MachineLearning.Pipeline.Common;
using Microsoft.MachineLearning.RunHistory.Contracts;
using Microsoft.RelInfra.Instrumentation.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Microsoft.Aether.DataTransferCloud.JobProcessing
{
    class ModuleHelper
    {
        private readonly IModuleResourceManager _moduleResourceManager;
        private readonly string _executionDirectory;
        private readonly IRunHistoryClient _runHistoryClient;
        private static readonly string AnyFile = "AnyFile";
        private readonly IAmlSvcClientFactory _amlClientFactory;

        public ModuleHelper(
            IModuleResourceManager moduleResourceManager,
            string executionDirectory,
            IRunHistoryClient runHistoryClient,
            IAmlSvcClientFactory amlClientFactory)
        {
            _moduleResourceManager = moduleResourceManager ?? throw new ArgumentNullException(nameof(moduleResourceManager));
            _executionDirectory = executionDirectory ?? throw new ArgumentNullException(nameof(executionDirectory));
            _runHistoryClient = runHistoryClient ?? throw new ArgumentNullException(nameof(runHistoryClient));
            _amlClientFactory = amlClientFactory;
        }

        internal async Task UpdateRunHistoryComputeTargetAsync(DataTransferJobMetadata job)
        {
            try
            {
                CommonLogger.LogEntityInfo(job.Id, "Updating compute target in run history");
                var createRunDto = new CreateRunDto
                {
                    RunId = job.RunId,
                    Target = job.CommandEntity.DataCopyCommand.ComputeName
                };
                await _runHistoryClient.UpdateRunAsync(
                    job.WorkspaceIdentity,
                    job.RunHistoryExperimentName,
                    job.RunId,
                    createRunDto,
                    job.CreatedBy);
            }
            catch (Exception exception)
            {
                CommonLogger.LogEntityWarning(job.Id, "Couldn't update compute target in run history. Error will be ignored. Error: {safe_exception_message}, stack_trace: {stack_trace}", exception.Message, exception.StackTrace);
            }
        }

        // get all datareference from input, including skip input.
        internal IList<DataReference> GetAllDataReferencesFromInput(DataTransferJobMetadata job)
        {
            return job.InputInfos.Select(input => input.DataLocation.DataReference).ToList();
        }

        // get all datareference from output, including skip output.
        internal IList<DataReference> GetAllDataReferencesFromOutput(DataTransferJobMetadata job)
        {
            return job.OutputInfos.Select(output => output.DataLocation.DataReference).ToList();
        }

        // get datareference from input, exclude skip input.
        internal IList<DataReference> GetDataReferencesExcludeSkipInput(DataTransferJobMetadata job)
        {
            return job.ExecutionStructuredInterface.Inputs.Where(input => !input.Key.SkipProcessing && (!input.Key.IsOptional || input.Value != null)).Select(input => input.Value?.DataLocation?.DataReference).ToList();
        }

        // get datareference from output, exclude skip output.
        internal IList<DataReference> GetDataReferencesExcludeSkipFromOutput(DataTransferJobMetadata job)
        {
            return job.ExecutionStructuredInterface.Outputs.Where(output => !output.Key.SkipProcessing).Select(output => output.Value?.DataLocation?.DataReference).ToList();
        }

        internal string GetAssetIdExcludeSkipForOneInput(DataTransferJobMetadata job)
        {
            return job.ExecutionStructuredInterface.Inputs.Where(input => !input.Key.SkipProcessing && (!input.Key.IsOptional || input.Value != null))
                       .ToList()?.FirstOrDefault().Value?.DataLocation?.AssetDefinition?.SerializedAssetId;
        }

        internal List<string> GetAssetIdExcludeSkipForMultiInput(DataTransferJobMetadata job)
        {
            return job.ExecutionStructuredInterface.Inputs.Where(input => !input.Key.SkipProcessing && (!input.Key.IsOptional || input.Value != null))
                       .Select(input => input.Value?.DataLocation?.AssetDefinition?.SerializedAssetId).ToList();
        }

        // get datareference from output, exclude skip output.
        internal string GetDataURIExcludeSkipOutput(DataTransferJobMetadata job)
        {
            return job.ExecutionStructuredInterface.Outputs.Where(output => !output.Key.SkipProcessing).ToList()?.FirstOrDefault().Key?.AssetOutputSettings?.Path;
        }

        internal DataReference GetDataReferenceFromInputId(DataTransferJobMetadata job, string inputDataId)
        {
            return LookupModuleInput(job, inputDataId).DataLocation.DataReference;
        }

        internal AmlDataset GetDatasetFromInputId(DataTransferJobMetadata job, string inputDataId)
        {
            return LookupModuleInput(job, inputDataId).DataLocation.AmlDataset;
        }

        internal IList<AmlDataset> GetProcessingDatasetFromInput(DataTransferJobMetadata job)
        {
            return job.ExecutionStructuredInterface.Inputs.Where(pair => !pair.Key.SkipProcessing).Select(pair => pair.Value?.DataLocation?.AmlDataset).ToList();
        }

        internal IList<AmlDataset> GetDatasetFromOutput(DataTransferJobMetadata job)
        {
            return job.OutputInfos.Select(output => output.DataLocation.AmlDataset).ToList();
        }

        internal IList<bool> GetSkipProcessingFromInput(DataTransferJobMetadata job)
        {
            return job.ExecutionStructuredInterface.Inputs.Select(pair => pair.Key.SkipProcessing).ToList();
        }

        internal IList<bool> GetSkipProcessingFromOutput(DataTransferJobMetadata job)
        {
            return job.ExecutionStructuredInterface.Outputs.Select(pair => pair.Key.SkipProcessing).ToList();
        }

        internal List<string> GetValidInputNames(DataTransferJobMetadata job)
        {
            List<string> validInputNames = new List<String>();
            foreach (var input in job.InputInfos)
            {
                var executionInterfaceInputs = job.ExecutionStructuredInterface.Inputs.Where(pair => pair.Key.Name.Equals(input.Name, StringComparison.OrdinalIgnoreCase)).ToList();
                if (executionInterfaceInputs.Count != 1)
                {
                    CommonLogger.LogEntityWarning(job.Id, "Couldn't read input settings for input {job_input_name}: expected 1, got: {input_settings_count}", input.Name, executionInterfaceInputs.Count);
                    continue;
                }
                var executionInterfaceInput = executionInterfaceInputs.Single();
                if (executionInterfaceInput.Key.SkipProcessing)
                {
                    continue;
                }
                validInputNames.Add(input.Name);
            }
            return validInputNames;
        }

        /// <summary>
        /// Fetch assetId for each inputs in dictionary format
        /// The key: Input Name
        /// The value: AssetId? (might be null)
        /// </summary>
        /// <param name="job">Cloudlet Job</param>
        /// <returns>AssertId by inputName</returns>
        internal IDictionary<string, AssetId?> ResolveInputAssetIds(DataTransferJobMetadata job)
        {
            return job?.ExecutionStructuredInterface?.Inputs?.Where(pair => !pair.Key.SkipProcessing)
                .Select(input =>
                {
                    var assetId = input.Value?.DataLocation?.AssetDefinition?.GetAssetId();
                    return new { input.Key.Name, AssetId = assetId };
                }).ToDictionary(pair => pair.Name, pair => pair.AssetId) ?? new Dictionary<string, AssetId?>();
        }

        /// <summary>
        /// Fetch assetId for each outputs in dictionary format
        /// The key: Output Name
        /// The value: AssetId? (might be null)
        /// </summary>
        /// <param name="job">Cloudlet Job</param>
        /// <returns>AssertId by outputName</returns>
        public async Task<IDictionary<string, AssetId?>> ResolveOutputAssetIds(DataTransferJobMetadata job)
        {
            var unnamedAssetOutputs = job.ExecutionStructuredInterface.Outputs?.Where(pair => pair.Key?.AssetOutputSettings != null).Select(kv => kv.Key).ToList();
            // Create unnamed output asset for all the output, in order to get legitimate assetId
            Dictionary<string, TypedAssetReference> outputAssets = await OutputDataLocationUtils.ResolveUnNamedOutputAssetIds(unnamedAssetOutputs, job, _amlClientFactory);
            return outputAssets?.Select(output => new { Name = output.Key, output.Value?.AssetId }).ToDictionary(pair => pair.Name, pair => pair.AssetId) ?? new Dictionary<string, AssetId?>();
        }

        /// <summary>
        /// Fetch asset type for each inputs in dictionary format
        /// The key: Input Name
        /// The value: AssetType? (might be null)
        /// </summary>
        /// <param name="job">Cloudlet Job</param>
        /// <returns>Assert type by inputName</returns>
        public IDictionary<string, AssetType?> ResolveInputAssetTypes(DataTransferJobMetadata job)
        {
            return job?.ExecutionStructuredInterface?.Inputs?.Where(pair => !pair.Key.SkipProcessing)
                .Select(input => new { input.Key?.Name, input.Value?.DataLocation?.AssetDefinition?.Type }).ToDictionary(pair => pair.Name, pair => pair.Type) ?? new Dictionary<string, AssetType?>();
        }

        /// <summary>
        /// Fetch asset type for each outputs in dictionary format
        /// The key: Output Name
        /// The value: AssetType? (might be null)
        /// </summary>
        /// <param name="job">Cloudlet Job</param>
        /// <returns>Assert type by outputName</returns>
        public IDictionary<string, AssetType?> ResolveOutputAssetTypes(DataTransferJobMetadata job)
        {
            return job?.ExecutionStructuredInterface?.Outputs?.Where(pair => !pair.Key.SkipProcessing)
                .Select(output => new { output.Key?.Name, output.Key?.AssetOutputSettings?.Type }).ToDictionary(pair => pair.Name, pair => pair.Type) ?? new Dictionary<string, AssetType?>();
        }

        internal void ValidateSkipProcessingStatus(IList<bool> skipProcessingsOfInPorts, IList<bool> skipProcessingsOfOutPorts)
        {
            int validInPortCount = skipProcessingsOfInPorts.Where(x => !x).Count();
            int validOutPortCount = skipProcessingsOfOutPorts.Where(x => !x).Count();
            if (validInPortCount == 0)
            {
                throw new ArgumentException("All the InPorts are skipped-processing");
            }
            if (skipProcessingsOfOutPorts.Count() != 1)
            {
                throw new ArgumentException("Only One output port is allowed under current data transfer model");
            }
            if (skipProcessingsOfOutPorts[0])
            {
                throw new ArgumentException("Can not skip process the only one output port in Data transfer");
            }
        }

        internal async Task UploadOutputAsync(DataTransferJobMetadata job)
        {
            DataTransferDataCopyCommand command = job.CommandEntity.DataCopyCommand;

            DataReference outputDataReference = GetDataReferenceFromInputId(job, command.DestinationDataId);

            CloudModuleOutputExecutionInfo outputInfo = LookupModuleOutput(job, command.OutputDataId);
            outputInfo.DataLocation.DataReference = outputDataReference;

            await ArtifactServiceRegistry.RegisterOutputAsync(
                job,
                _executionDirectory,
                outputInfo,
                _moduleResourceManager,
                token: new CancellationToken());
        }

        internal async Task UploadOutputIntoRegistryAsync(DataTransferJobMetadata job)
        {
            CloudModuleOutputExecutionInfo outputInfo = job.OutputInfos.FirstOrDefault();

            await ArtifactServiceRegistry.RegisterOutputAsync(
                job,
                _executionDirectory,
                outputInfo,
                _moduleResourceManager,
                token: new CancellationToken());
        }

        internal async Task UploadOutputDatasetAsync(DataTransferJobMetadata job)
        {
            DataTransferDataCopyCommand command = job.CommandEntity.DataCopyCommand;
            CloudModuleInputExecutionInfo destinationInfo = LookupModuleInput(job, command.DestinationDataId);
            AmlDataset outputDataSet = destinationInfo.DataLocation.AmlDataset;

            CloudModuleOutputExecutionInfo outputInfo = LookupModuleOutput(job, command.OutputDataId);
            outputInfo.DataLocation.AmlDataset = outputDataSet;
            outputInfo.DataLocation.DataReference = destinationInfo.DataLocation.DataReference;
            Dictionary<string, string> tags = new Dictionary<string, string>();
            var executionInterfaceOutputSettings = job.ExecutionStructuredInterface.Outputs.
                                                   Where(pair => !pair.Key.SkipProcessing && pair.Key.Name.Equals(outputInfo.Name, StringComparison.OrdinalIgnoreCase)).
                                                   ToList();
            KeyValuePair<StructuredInterfaceOutput, ModuleOutputExecutionInfo> outputSettingKeyValuePair = executionInterfaceOutputSettings.Single();
            outputSettingKeyValuePair.Value.DataLocation = destinationInfo.DataLocation;
            outputSettingKeyValuePair.Key.DataReferenceName = destinationInfo.DataLocation.AmlDataset.RegisteredDataSetReference?.Name;
            outputSettingKeyValuePair.Value.DataLocation.DataReference = destinationInfo.DataLocation.DataReference;
            outputSettingKeyValuePair.Value.DataLocation.DataStoreName = destinationInfo.DataLocation.DataStoreName;
            tags.Add("preversion", outputDataSet.RegisteredDataSetReference?.Version);
            outputSettingKeyValuePair.Key.DatasetOutput = new DatasetOutput
            {
                DatasetRegistration = new DatasetRegistration { Name = outputDataSet.RegisteredDataSetReference?.Name, Tags = tags, CreateNewVersion = false },
                DatasetOutputOptions = new DatasetOutputOptions { PathOnDatastore = destinationInfo.DataLocation.DataReference.GetPathOnDataStore() }
            };

            await ArtifactServiceRegistry.RegisterOutputAsync(
                job,
                _executionDirectory,
                outputInfo,
                _moduleResourceManager,
                token: new CancellationToken());
        }

        public static CloudModuleInputExecutionInfo LookupModuleInput(DataTransferJobMetadata job, string inputDataId)
        {
            CloudModuleInputExecutionInfo moduleInput = job.InputInfos.FirstOrDefault(input => string.Compare(input.DataId, inputDataId, StringComparison.InvariantCultureIgnoreCase) == 0);

            if (moduleInput == null)
            {
                throw new CommandParsingException($"Input {inputDataId} expected based on module interface, but was missing from request");
            }

            return moduleInput;
        }

        private static CloudModuleOutputExecutionInfo LookupModuleOutput(DataTransferJobMetadata job, string outputDataId)
        {
            CloudModuleOutputExecutionInfo outputInfo = job.OutputInfos.FirstOrDefault(output => string.Compare(output.DataId, outputDataId, StringComparison.InvariantCultureIgnoreCase) == 0);

            if (outputInfo == null)
            {
                throw new CommandParsingException($"Output {outputDataId} expected based on module interface, but was missing from request");
            }

            return outputInfo;
        }

        public static IList<FileBasedPathType> GetFileBasedPathTypeFromInput(DataTransferJobMetadata job)
        {
            IList<List<string>> sourceDataTypeIdsListCollection = job.ExecutionStructuredInterface.Inputs.Select(pair => pair.Key.DataTypeIdsList).ToList();
            IList<FileBasedPathType> inPortTypeList = sourceDataTypeIdsListCollection.Select(col => ConvertToFileBasedPathType(col)).ToList();
            return inPortTypeList;
        }

        public static IList<FileBasedPathType> GetFileBasedPathTypeFromOutput(DataTransferJobMetadata job)
        {
            IList<string> destinationDataTypeIdsListCollection = job.ExecutionStructuredInterface.Outputs.Select(pair => pair.Key.DataTypeId).ToList();
            IList<FileBasedPathType> outPortTypeList = destinationDataTypeIdsListCollection.Select(id => ConvertToFileBasedPathType(id)).ToList();
            return outPortTypeList;
        }

        public static FileBasedPathType ConvertToFileBasedPathType(IList<string> dataTypeIdsList)
        {
            // this method is try to use port dataTypeIdsList filed to infer the input port type (file vs folder)
            if (dataTypeIdsList == null || dataTypeIdsList.Count == 0)
            {
                throw new ArgumentException("Can not convert to PathType by dataTypeIdsList since it is null or emtpy");
            }

            var fileList = dataTypeIdsList.Where(id => string.Equals(id, AnyFile, StringComparison.OrdinalIgnoreCase)).ToList();
            if (fileList.Count > 0 && dataTypeIdsList.Count > 1)
            {
                // if mix of File and non-File , take directory as default
                return FileBasedPathType.Folder;
            }
            else if (fileList.Count > 0 && dataTypeIdsList.Count == 1)
            {
                // if only File type specfied
                return FileBasedPathType.File;
            }

            return FileBasedPathType.Folder;
        }

        public static FileBasedPathType ConvertAssetTypeToFileBasedPathType(AssetType? assetType, string parameterInputOrOutput, int Index)
        {
            if (assetType == null || string.IsNullOrWhiteSpace(assetType.ToString()))
            {
                throw new ArgumentException($"Can not convert to PathType by assetType = {assetType}");
            }

            if (assetType != AssetType.UriFolder && assetType != AssetType.UriFile)
            {
                throw new ArgumentException($"{parameterInputOrOutput}[{Index}] AssetType {assetType} should either be UriFile or UriFolder");
            }
            return assetType == AssetType.UriFolder ? FileBasedPathType.Folder : FileBasedPathType.File;
        }

        public static FileBasedPathType ConvertToFileBasedPathType(string dataTypeId)
        {
            if (string.IsNullOrEmpty(dataTypeId))
            {
                throw new ArgumentException($"Can not convert to PathType by dataTypeId = {dataTypeId}");
            }

            if (string.Equals(dataTypeId, AnyFile, StringComparison.OrdinalIgnoreCase))
            {
                return FileBasedPathType.File;
            }

            return FileBasedPathType.Folder;
        }

    }
}
