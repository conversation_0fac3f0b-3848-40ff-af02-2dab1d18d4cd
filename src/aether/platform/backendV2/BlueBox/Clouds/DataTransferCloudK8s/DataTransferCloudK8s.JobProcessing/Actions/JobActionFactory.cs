﻿using Microsoft.Aether.BlueBox.Clouds.Common.AmlDatasetAdapter;
using Microsoft.Aether.BlueBox.DataStoreClient;
using Microsoft.Aether.BlueBox.RunHistoryClient;
using Microsoft.Aether.CloudCommon;
using Microsoft.Aether.CloudCommon.Worker;
using Microsoft.Aether.Clouds.CloudCommon;
using Microsoft.Aether.Clouds.CloudCommon.DatasetAdapter;
using Microsoft.Aether.DataContract.Backend.Clouds;
using Microsoft.Aether.DataContracts.DataManagement;
using Microsoft.Aether.DataTransfer.Cloud.Common;
using Microsoft.Aether.DataTransfer.Cloud.Common.DataContracts;
using Microsoft.Aether.DataTransfer.Cloud.Common.Exceptions;
using Microsoft.Aether.DataTransferCloud.CopyService;
using Microsoft.MachineLearning.Dataset.Client;
using Microsoft.MachineLearning.Pipeline.Common;
using Microsoft.MachineLearning.PolicyServiceClient;
using Microsoft.MachineLearning.RunHistory.Contracts;
using Microsoft.RelInfra.Instrumentation.Logging;
using System;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;

[assembly: InternalsVisibleTo("Microsoft.Aether.DataTransferCloud.JobProcessing.Test")]
namespace Microsoft.Aether.DataTransferCloud.JobProcessing.Actions
{
    class JobActionFactory : IJobActionFactory
    {
        private readonly DataTransferCloudConfig _configuration;
        private readonly IModuleResourceManagerFactory _moduleResourceManagerFactory;
        private readonly string _executionDirectory;
        private readonly ICommonLogger _dualLogger;
        private readonly IAsyncCopyServiceFactory _asyncCopyServiceFactory;
        private readonly IPolicyServiceClient _policyServiceClient;
        private readonly IAzureMachineLearningDatasetManagementClientFactory _datasetClientFactory;
        private readonly IRunHistoryClient _runHistoryClient;
        private readonly IDataStoreClient _dataStoreClient;
        private readonly IErrorClassifier _errorClassifier;
        private readonly IAmlSvcClientFactory _amlClientFactory;

        internal JobActionFactory(
            DataTransferCloudConfig configuration,
            IModuleResourceManagerFactory moduleResourceManagerFactory,
            string executionDirectory,
            ICommonLogger dualLogger,
            IAsyncCopyServiceFactory asyncCopyServiceFactory,
            IPolicyServiceClient policyServiceClient,
            IAzureMachineLearningDatasetManagementClientFactory datasetClientFactory,
            IRunHistoryClient runHistoryClient,
            IDataStoreClient dataStoreClient,
            IErrorClassifier errorClassifier,
            IAmlSvcClientFactory amlClientFactory)
        {
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
            _moduleResourceManagerFactory = moduleResourceManagerFactory ?? throw new ArgumentNullException(nameof(moduleResourceManagerFactory));
            _executionDirectory = executionDirectory ?? throw new ArgumentNullException(nameof(executionDirectory));
            _dualLogger = dualLogger ?? throw new ArgumentNullException(nameof(dualLogger));
            _asyncCopyServiceFactory = asyncCopyServiceFactory ?? throw new ArgumentNullException(nameof(asyncCopyServiceFactory));
            _policyServiceClient = policyServiceClient ?? throw new ArgumentNullException(nameof(policyServiceClient));
            _datasetClientFactory = datasetClientFactory ?? throw new ArgumentNullException(nameof(datasetClientFactory));
            _runHistoryClient = runHistoryClient ?? throw new ArgumentNullException(nameof(runHistoryClient));
            _dataStoreClient = dataStoreClient ?? throw new ArgumentNullException(nameof(dataStoreClient));
            _errorClassifier = errorClassifier ?? throw new ArgumentNullException(nameof(errorClassifier));
            _amlClientFactory = amlClientFactory ?? throw new ArgumentNullException(nameof(amlClientFactory));
        }

        public async Task<IJobAction> GetJobActionAsync(DataTransferJobMetadata job, StateMachine stateMachine)
        {
            IAsyncCopyService copyService = await _asyncCopyServiceFactory.CreateCopyServiceAsync(job);

            IModuleResourceManager moduleResourceManager = _moduleResourceManagerFactory.Create(
                jobId: job.Id,
                executionDirectory: _executionDirectory,
                dataContainerId: job.DataContainerId ?? job.RunId, // TODO lisun remove datacontainerId fallback to RunId, this is only used for existing running jobs during deployment
                workspaceIdentity: job.WorkspaceIdentity,
                createdBy: job.CreatedBy);

            ModuleHelper moduleHelper = new ModuleHelper(moduleResourceManager, _executionDirectory, _runHistoryClient, _amlClientFactory);

            IDataSetAdapter adapter = new DataSetAdapter(_datasetClientFactory, _dataStoreClient, _runHistoryClient, _dualLogger);

            DataSetFileHelper fileHelper = new DataSetFileHelper(_datasetClientFactory, _dualLogger);

            var parentSubGraphModuleIds = job.ParentSubGraphModuleIds == null ? "" : string.Join("；", job.ParentSubGraphModuleIds);
            CommonLogger.LogEntityInfo(job.Id, $"Data Transfer Cloud created Policy ValidationRequestDto with " +
                $"ParentRunId={job.PipelineRunId ?? job.ExperimentId}, " +
                $"RunId={job.RunId}, " +
                $"ExperimentName={job.RunHistoryExperimentName ?? ""}, " +
                $"ParentSubGraphModuleIds={parentSubGraphModuleIds}");

            switch (job.Status)
            {
                case JobStatus.Submitted:
                    if (_configuration.EnablePolicyValidation(job.WorkspaceIdentity.SubscriptionId))
                    {
                        switch (job.CommandEntity.DataCopyCommand.PolicyValidationStatus)
                        {
                            case PolicyValidationStatus.None:
                                return new SubmitApprovalAction(moduleHelper, _dualLogger, stateMachine, job, _policyServiceClient, _errorClassifier);
                            case PolicyValidationStatus.InProgress:
                                return new CheckApprovalStatusAction(_dualLogger, stateMachine, job, _policyServiceClient, _errorClassifier);
                            case PolicyValidationStatus.Completed:
                                return new SubmitJobAction(moduleHelper, _dualLogger, stateMachine, job, copyService, fileHelper, adapter, _runHistoryClient, _amlClientFactory);
                            default:
                                throw new InvalidJobStateException($"Invalid PolicyValidationStatus: {job.CommandEntity.DataCopyCommand.PolicyValidationStatus}");
                        }
                    }
                    else
                    {
                        return new SubmitJobAction(moduleHelper, _dualLogger, stateMachine, job, copyService, fileHelper, adapter, _runHistoryClient, _amlClientFactory);
                    }
                case JobStatus.Running:
                    return new CheckJobStatusAction(moduleHelper, _dualLogger, stateMachine, job, copyService, adapter, _errorClassifier, _amlClientFactory, _runHistoryClient);
                case JobStatus.Cancelling:
                    return new CancelJobAction(moduleHelper, _dualLogger, stateMachine, job, copyService);
                case JobStatus.Completed:
                case JobStatus.Failed:
                case JobStatus.Cancelled:
                    return new NullAction();
                default:
                    throw new InvalidJobStateException($"Unexpected job status {job.Status}");
            }
        }

        internal class NullAction : IJobAction
        {
            public Task ExecuteAsync()
            {
                return Task.CompletedTask;
            }
        }
    }
}
