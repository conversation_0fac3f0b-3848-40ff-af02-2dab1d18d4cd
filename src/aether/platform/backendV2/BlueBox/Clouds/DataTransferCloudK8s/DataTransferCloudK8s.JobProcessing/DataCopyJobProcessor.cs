﻿using Common.Core.Contracts;
using Microsoft.Aether.CloudCommon;
using Microsoft.Aether.Clouds.CloudCommon;
using Microsoft.Aether.Clouds.CloudCommon.JobQueueProcessing;
using Microsoft.Aether.DataTransfer.Cloud.Common;
using Microsoft.Aether.DataTransfer.Cloud.Common.DataContracts;
using Microsoft.Aether.DataTransferCloud.JobProcessing.Actions;
using Microsoft.MachineLearning.Common.Core.Exceptions;
using Microsoft.RelInfra.Common.Exceptions;
using Microsoft.RelInfra.Common.Serialization;
using Microsoft.RelInfra.Extensions;
using Microsoft.RelInfra.Instrumentation.Logging;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace Microsoft.Aether.DataTransferCloud.JobProcessing
{
    class DataCopyJobProcessor : IJobProcessor<DataTransferJobMetadata>
    {
        private readonly DataTransferCloudConfig _configuration;
        private readonly ICommonLogger _dualLogger;
        private readonly IJobActionFactory _jobActionFactory;
        private readonly IStateTransitionActionFactory _stateTransitionActionFactory;
        private readonly IRunStateUpdater _runStateUpdater;
        private readonly IErrorClassifier _errorClassifier;

        internal DataCopyJobProcessor(
            DataTransferCloudConfig configuration,
            ICommonLogger dualLogger,
            IJobActionFactory jobActionFactory,
            IStateTransitionActionFactory stateTransitionActionFactory,
            IRunStateUpdater runStateUpdater,
            IErrorClassifier errorClassifier)
        {
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
            _dualLogger = dualLogger ?? throw new ArgumentNullException(nameof(dualLogger));
            _jobActionFactory = jobActionFactory ?? throw new ArgumentNullException(nameof(jobActionFactory));
            _stateTransitionActionFactory = stateTransitionActionFactory ?? throw new ArgumentNullException(nameof(stateTransitionActionFactory));
            _runStateUpdater = runStateUpdater;
            _errorClassifier = errorClassifier ?? throw new ArgumentNullException(nameof(errorClassifier));
        }

        public async Task<DataTransferJobMetadata> ProcessJobAsync(DataTransferJobMetadata job)
        {
            var stateMachine = new StateMachine(job.Status);
            stateMachine.StateTransition += BuildTransitionHandler(job);
            stateMachine.StateRunError += BuildRunErrorHanlder(job);

            try
            {
                IJobAction action = await _jobActionFactory.GetJobActionAsync(job, stateMachine);
                await action.ExecuteAsync();
            }
            catch (Exception ex) when (HttpClientExtensions.IsTooManyRequestsException(ex))
            {
                _dualLogger.LogEntityError(job.Id, "Data transfer job is getting throttled and will be retried after some time: {exception}; Exception type: {exception_type}", ex, ex.GetInnerTypes());
                throw;
            }
            catch (Exception ex) when (ex.IsRetriableException())
            {
                _dualLogger.LogEntityError(job.Id, "Data transfer job is hitting transient error and will be retried: {exception}; Exception type: {exception_type}", ex, ex.GetInnerTypes());
            }
            catch (Exception ex)
            {
                _dualLogger.LogEntityError(job.Id, "Data transfer job failed with unexpected error: {exception}; Exception type: {exception_type}", ex, ex.GetInnerTypes());
                var rawErrorMessage = ex.Message;
                var errorCode = RootErrorCode.ServiceError;
                if (ex is BaseException)
                {
                    // grab message and code by BaseException structure
                    rawErrorMessage = ((BaseException)ex).Error?.ToRootError()?.Message;
                    errorCode = ((BaseException)ex).Error?.ToRootError()?.Code;
                }

                var exceptionString = DataTransferCloudExceptionHelper.ToExceptionString(ex);
                CommonLogger.LogEntityInfo(job.Id, $"Error classify Exception: {exceptionString}");

                ErrorCategoryInfo category = _errorClassifier.GetErrorCategoryInfo(exceptionString, errorCode);
                CommonLogger.LogEntityInfo(job.Id, $"[DataCopyJobProcessor] Found ErrorCategoryInfo: {SerializationHelpers.SerializeEntity<ErrorCategoryInfo>(category)}");

                // Why override?
                // 1.To make some system error message more understandable to user
                // 2.Complement necessary instructions/guidances to users
                // But this is not good practice.  we shall avoid this!
                // Future dev plan: Consider integrate DefaultErrorMessage into error_config.json
                OverrideErrorMessageByCategory(category, ref rawErrorMessage);

                await stateMachine.RaiseRunErrorEventAsync(
                        errorDetails: $"Unexpected error: {rawErrorMessage}",
                        errorCategory: category);
                await stateMachine.RaiseEventAsync(StateMachine.Events.CopyOperationFailed);
            }

            await FlushLogsAsync(job.Id);
            return job;
        }

        private Func<object, StateTransitionEventArgs, Task> BuildTransitionHandler(DataTransferJobMetadata job)
        {
            return async (object sender, StateTransitionEventArgs eventArgs) =>
            {
                job.Status = eventArgs.CurrentState;
                UpdateStartFinishTimes(job);

                CommonLogger.LogEntityInfo(job.Id, $"Job status updated to {job.Status} as a result of event {eventArgs.Trigger} (old state: {eventArgs.OldState})");

                var transitionActions = await _stateTransitionActionFactory.GetStateTransitionActionsAsync(
                    previousState: eventArgs.OldState,
                    currentState: eventArgs.CurrentState,
                    job: job);

                await Task.WhenAll(transitionActions.Select(transitionAction => transitionAction.ExecuteAsync()));
            };
        }

        public Func<object, StateRunErrorEventEventArgs, Task> BuildRunErrorHanlder(DataTransferJobMetadata job)
        {
            return async (object sender, StateRunErrorEventEventArgs eventArgs) =>
            {
                var errorResponse = ErrorResponseCreator.CreateErrorResponse(eventArgs.ErrorCategory, $"User job failed with error: {eventArgs.ErrorDetails}", job.ModuleInfo.CloudSystem);
                await _runStateUpdater.UpdateRunErrorAsync(
                  job: job,
                  errorResponse: errorResponse,
                  logger: _dualLogger);
                job.ErrorResponse = errorResponse;
            };
        }

        private void UpdateStartFinishTimes(DataTransferJobMetadata job)
        {
            JobStatus state = job.Status;
            if (state == JobStatus.Running)
            {
                if (!job.StartTime.HasValue)
                {
                    job.StartTime = DateTime.UtcNow;
                }
            }
            else if (state == JobStatus.Completed || state == JobStatus.Cancelled || state == JobStatus.Failed)
            {
                if (!job.EndTime.HasValue)
                {
                    job.EndTime = DateTime.UtcNow;
                }
            }
        }

        private async Task FlushLogsAsync(string jobId)
        {
            try
            {
                await _dualLogger.FlushAsync();
            }
            catch (Exception ex)
            {
                CommonLogger.LogEntityError(jobId, "Failed to flush logs to cosmos, ignoring the error. Logs might be incomplete. exception {exception}", ex);
                // Swallow exception
            }
        }

        internal void OverrideErrorMessageByCategory(ErrorCategoryInfo category, ref string rawErrorMessage)
        {
            if (category?.FailureName == "Resource creation failed as ADF maximum number limitation")
            {
                rawErrorMessage = "Quota limitation issue: The resource creation failed because datafactory has maximum number limitation of resources in the factory (reference link: https://docs.microsoft.com/en-us/azure/azure-resource-manager/management/azure-subscription-service-limits#data-factory-limits).";
            }
        }
    }
}
