﻿using Microsoft.Aether.DataTransfer.Cloud.Common.DataContracts;
using Microsoft.Aether.DataTransferCloud.CopyService;
using Microsoft.RelInfra.Common.Exceptions;
using Microsoft.RelInfra.Instrumentation.Logging;
using System;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;

[assembly: InternalsVisibleTo("Microsoft.Aether.DataTransferCloud.JobProcessing.Test")]
namespace Microsoft.Aether.DataTransferCloud.JobProcessing.Actions
{
    class CancelJobAction : IJobAction
    {
        private readonly DataTransferJobMetadata _job;
        private readonly ModuleHelper _moduleHelper;
        private readonly ICommonLogger _dualLogger;
        private readonly StateMachine _stateMachine;
        private readonly IAsyncCopyService _asyncCopyService;

        internal CancelJobAction(
            ModuleHelper moduleHelper,
            ICommonLogger dualLogger,
            StateMachine stateMachine,
            DataTransferJobMetadata job,
            IAsyncCopyService asyncCopyService)
        {
            _moduleHelper = moduleHelper ?? throw new ArgumentNullException(nameof(moduleHelper));
            _dualLogger = dualLogger ?? throw new ArgumentNullException(nameof(dualLogger));
            _stateMachine = stateMachine ?? throw new ArgumentNullException(nameof(stateMachine));
            _job = job ?? throw new ArgumentNullException(nameof(job));
            _asyncCopyService = asyncCopyService ?? throw new ArgumentNullException(nameof(asyncCopyService));
        }

        public async Task ExecuteAsync()
        {
            var storageEntity = _job.CommandEntity.DataCopyCommand.CopyOperationEntity;

            if (storageEntity == null)
            {
                _dualLogger.LogEntityWarning(_job.Id, "Data transfer hasn't started yet; job canceled");
            }
            else
            {
                AsyncCopyOperationEntity copyOperationEntity = AsyncCopyOperationEntity.FromStorageContract(storageEntity);

                try
                {
                    await _asyncCopyService.CancelCopyOperationAsync(copyOperationEntity);
                }
                catch (Exception ex) when (ex.IsRetriableException())
                {
                    _dualLogger.LogEntityWarning(_job.Id, "Failed to cancel job due to a temporary issue, will retry. Exception: {exception}; Type: {exception_type}", ex, ex.GetInnerTypes());
                    await _stateMachine.RaiseEventAsync(StateMachine.Events.CopyOperationTransientFailure);
                    return;
                }

                _dualLogger.LogEntityWarning(_job.Id, "Data transfer pipeline was successfully canceled");
            }

            await _stateMachine.RaiseEventAsync(StateMachine.Events.CopyOperationCancelled);
        }
    }
}
