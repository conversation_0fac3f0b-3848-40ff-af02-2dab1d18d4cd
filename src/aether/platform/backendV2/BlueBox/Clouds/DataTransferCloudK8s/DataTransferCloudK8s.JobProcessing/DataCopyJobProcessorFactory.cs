﻿using System;
using Microsoft.Aether.BlueBox.DataStoreClient;
using Microsoft.Aether.BlueBox.S2S.Common;
using Microsoft.Aether.CloudCommon.Worker;
using Microsoft.Aether.Clouds.CloudCommon;
using Microsoft.Aether.Clouds.CloudCommon.JobQueueProcessing;
using Microsoft.Aether.DataTransfer.Cloud.Common;
using Microsoft.Aether.DataTransfer.Cloud.Common.DataContracts;
using Microsoft.Aether.DataTransferCloud.JobProcessing.Actions;
using Microsoft.Aether.DataTransferCloud.CopyService;
using Microsoft.RelInfra.Instrumentation.Logging;
using Microsoft.RelInfra.Instrumentation;
using Microsoft.Aether.S2S.Common;
using Microsoft.Aether.BlueBox.WorkspaceResourcesClient;
using Microsoft.MachineLearning.PolicyServiceClient;
using Microsoft.Aether.BlueBox.RunHistoryClient;
using Microsoft.MachineLearning.Dataset.Client;
using Microsoft.MachineLearning.Pipeline.Common;

namespace Microsoft.Aether.DataTransferCloud.JobProcessing
{
    public class DataCopyJobProcessorFactory : IDataTransferJobProcessorFactory
    {
        private IJobMetadataStorage<DataTransferJobMetadata> _jobStorage;
        private DataTransferCloudConfig _configuration;
        private readonly IRunStateUpdater _runStateUpdater;
        private readonly IModuleResourceManagerFactory _moduleResourceManagerFactory;
        private readonly string _executionDirectory;
        private readonly IAmlWorkspaceMsiTokenProvider _amlWorkspaceMsiTokenProvider;
        private readonly IDataStoreClient _dataStoreClient;
        private readonly CounterManager _counterManager;
        private readonly IS2STokenProvider _s2STokenProvider;
        private readonly IWorkspaceResourcesClient _workspaceResourcesClient;
        private readonly IPolicyServiceClient _policyServiceClient;
        private readonly IAzureMachineLearningDatasetManagementClientFactory _datasetClientFactory;
        private readonly IRunHistoryClient _runHistoryClient;
        private readonly IErrorClassifier _errorClassifier;
        private readonly IAmlSvcClientFactory _amlClientFactory;

        public DataCopyJobProcessorFactory(
            IJobMetadataStorage<DataTransferJobMetadata> jobStorage,
            DataTransferCloudConfig configuration,
            IRunStateUpdater runStateUpdater,
            IModuleResourceManagerFactory moduleResourceManagerFactory,
            string executionDirectory,
            IAmlWorkspaceMsiTokenProvider amlWorkspaceMsiTokenProvider,
            IDataStoreClient dataStoreClient,
            CounterManager counterManager,
            IS2STokenProvider s2STokenProvider,
            IWorkspaceResourcesClient workspaceResourcesClient,
            IPolicyServiceClient policyServiceClient,
            IAzureMachineLearningDatasetManagementClientFactory datasetClientFactory,
            IRunHistoryClient runHistoryClient,
            IErrorClassifier errorClassifier,
            IAmlSvcClientFactory amlClientFactory)
        {
            _jobStorage = jobStorage ?? throw new ArgumentNullException(nameof(jobStorage));
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
            _runStateUpdater = runStateUpdater ?? throw new ArgumentNullException(nameof(runStateUpdater));
            _moduleResourceManagerFactory = moduleResourceManagerFactory ?? throw new ArgumentNullException(nameof(moduleResourceManagerFactory));
            _executionDirectory = executionDirectory ?? throw new ArgumentNullException(nameof(executionDirectory));
            _amlWorkspaceMsiTokenProvider = amlWorkspaceMsiTokenProvider ?? throw new ArgumentNullException(nameof(amlWorkspaceMsiTokenProvider));
            _dataStoreClient = dataStoreClient ?? throw new ArgumentNullException(nameof(dataStoreClient));
            _counterManager = counterManager ?? throw new ArgumentNullException(nameof(counterManager));
            _s2STokenProvider = s2STokenProvider ?? throw new ArgumentNullException(nameof(s2STokenProvider));
            _workspaceResourcesClient = workspaceResourcesClient ?? throw new ArgumentNullException(nameof(workspaceResourcesClient));
            _policyServiceClient = policyServiceClient ?? throw new ArgumentNullException(nameof(policyServiceClient));
            _datasetClientFactory = datasetClientFactory ?? throw new ArgumentNullException(nameof(datasetClientFactory));
            _runHistoryClient = runHistoryClient ?? throw new ArgumentNullException(nameof(runHistoryClient));
            _errorClassifier = errorClassifier ?? throw new ArgumentNullException(nameof(errorClassifier));
            _amlClientFactory = amlClientFactory ?? throw new ArgumentNullException(nameof(amlClientFactory));
        }

        public IJobProcessor<DataTransferJobMetadata> CreateJobProcessor(CommandName jobCommand,
            IModuleResourceManager resourceManager, ICommonLogger dualLogger)
        {
            if (jobCommand != CommandName.DataCopy)
            {
                throw new InvalidOperationException($"Unsupported command: {jobCommand}");
            }

            IAsyncCopyServiceFactory asyncCopyServiceFactory = new DataFactoryCopyServiceFactory(
                _amlWorkspaceMsiTokenProvider, _dataStoreClient, _counterManager, dualLogger, _workspaceResourcesClient);

            IJobActionFactory jobActionFactory = new JobActionFactory(_configuration, _moduleResourceManagerFactory,
                _executionDirectory, dualLogger, asyncCopyServiceFactory, _policyServiceClient, _datasetClientFactory, _runHistoryClient, _dataStoreClient, _errorClassifier, _amlClientFactory);

            IStateTransitionActionFactory stateTransitionActionFactory = new StateTransitionActionFactory(
                _jobStorage, dualLogger, _runStateUpdater, _s2STokenProvider);

            return new DataCopyJobProcessor(_configuration, dualLogger, jobActionFactory, stateTransitionActionFactory, _runStateUpdater, _errorClassifier);
        }
    }
}