﻿using System;
using Microsoft.Aether.DataContracts.DataReferences;
using Microsoft.Aether.DataTransfer.Cloud.Common;
using Microsoft.Aether.DataTransferCloud.CopyService;
using Newtonsoft.Json;

namespace Microsoft.Aether.DataTransferCloud.JobProcessing
{
    public class CopyOptions
    {
        [JsonProperty("source_reference_type")]
        public string SourceReferenceType { get; set; }

        [JsonProperty("destination_reference_type")]
        public string DestinationReferenceType { get; set; }
    }

    public static class CopyOptionsExtensions
    {
        public static CopyOperationOptions GetCopyOperationOptions(this CopyOptions copyOptions)
        {
            return new CopyOperationOptions
            {
                SourcePathType = GetPathType(copyOptions.SourceReferenceType),
                DestinationPathType = GetPathType(copyOptions.DestinationReferenceType),
            };
        }

        public static FileBasedPathType GetPathType(string referenceType)
        {
            if (referenceType?.Equals(InterfaceStringConstants.FileReferenceType, StringComparison.OrdinalIgnoreCase) == true)
            {
                return FileBasedPathType.File;
            }

            if (referenceType?.Equals(InterfaceStringConstants.FolderReferenceType, StringComparison.OrdinalIgnoreCase) == true)
            {
                return FileBasedPathType.Folder;
            }

            return FileBasedPathType.Unknown;            
        }
    }
}
