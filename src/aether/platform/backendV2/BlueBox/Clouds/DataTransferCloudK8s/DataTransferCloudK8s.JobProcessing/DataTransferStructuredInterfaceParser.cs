﻿using Microsoft.Aether.BackendCommon.Logging;
using Microsoft.Aether.BlueBox.Clouds.Common;
using Microsoft.Aether.BlueBox.Clouds.Common.AmlDatasetAdapter;
using Microsoft.Aether.BlueBox.DataStoreClient;
using Microsoft.Aether.BlueBox.RunHistoryClient;
using Microsoft.Aether.Clouds.CloudCommon;
using Microsoft.Aether.Clouds.CloudCommon.DatasetAdapter;
using Microsoft.Aether.DataContract.Backend.Clouds;
using Microsoft.Aether.DataContracts;
using Microsoft.Aether.DataContracts.CloudSystem;
using Microsoft.Aether.DataContracts.DataReferences;
using Microsoft.Aether.DataTransfer.Cloud.Common;
using Microsoft.Aether.DataTransfer.Cloud.Common.DataContracts;
using Microsoft.Aether.DataTransfer.Cloud.Common.Exceptions;
using Microsoft.Aether.DataTransfer.Server.Library;
using Microsoft.MachineLearning.Dataset.Client;
using Microsoft.MachineLearning.Pipeline.Common;
using Microsoft.RelInfra.Common.Serialization;
using Microsoft.RelInfra.Instrumentation.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Microsoft.Aether.DataTransferCloud.JobProcessing
{
    class DataTransferStructuredInterfaceParser : IDataTransferJobCommandEntityParser
    {
        private readonly IAzureMachineLearningDatasetManagementClientFactory _datasetClientFactory;
        private readonly IRunHistoryClient _runHistoryClient;
        private readonly IDataStoreClient _dataStoreClient;
        private readonly IAmlSvcClientFactory _amlClientFactory;
        private const string _dataCopyCommandName = "datacopy";
        private const string _commandParamName = "command";
        private const string _destinationLocationKeyName = "DestinationLocation";

        public DataTransferStructuredInterfaceParser(
               IAzureMachineLearningDatasetManagementClientFactory datasetClientFactory,
               IRunHistoryClient runHistoryClient,
               IDataStoreClient dataStoreClient,
               IAmlSvcClientFactory amlClientFactory = null)
        {
            _datasetClientFactory = datasetClientFactory ?? throw new ArgumentNullException(nameof(datasetClientFactory));
            _runHistoryClient = runHistoryClient ?? throw new ArgumentNullException(nameof(runHistoryClient));
            _dataStoreClient = dataStoreClient ?? throw new ArgumentNullException(nameof(dataStoreClient));
            _amlClientFactory = amlClientFactory;
        }

        public async Task<DataTransferCommandEntity> ParseCommandTextAsync(DataTransferJobMetadata jobMetadata, JobDualLogger dualLogger, IAmlSvcClientFactory amlClientFactory)
        {
            IDataSetAdapter adapter = new DataSetAdapter(_datasetClientFactory, _dataStoreClient, _runHistoryClient, dualLogger);
            //generate dataid and input datareference if missing
            await GenerateDataIdAndDataReferenceAsync(jobMetadata, adapter, dualLogger, amlClientFactory);

            jobMetadata.Version = ParseVersionFromJobMetaData(jobMetadata);
            dualLogger.LogEntityInfo(jobMetadata.Id, $"Data Transfer structure Interface model version = {jobMetadata.Version}");


            // to keep v1.0 and v1.5 logic intact
            if (jobMetadata.Version < (int)DataTransferModuleVersions.Version3)
            {
                //Only for 1 Input and 1 Ouput Model
                await GenerateInputFromOutput(jobMetadata, dualLogger, adapter);
            }
            else
            {
                //only for Version = Version3 for now
                await GenerateOutputDataReference(jobMetadata, dualLogger, adapter);
            }
            return ParseStructuredInterface(jobMetadata);
        }

        private static DataTransferCommandEntity ParseStructuredInterface(DataTransferJobMetadata job)
        {
            ValidateInterface(job);

            Dictionary<string, string> metadataValues = job
                .ExecutionStructuredInterface
                .MetadataParameters?
                .ToDictionary(pair => pair.Key.Name.ToLower(), pair => pair.Value);

            Dictionary<string, string> parameterValues = job
                .ExecutionStructuredInterface
                .Parameters?
                .ToDictionary(pair => pair.Key.Name.ToLower(), pair => pair.Value);

            Dictionary<string, string> inputValues = job
                .ExecutionStructuredInterface
                .Inputs?
                .Where(pair => !pair.Key.SkipProcessing)
                .ToDictionary(pair => pair.Key.Name.ToLower(), pair => pair.Value?.DataId);

            Dictionary<string, string> outputValues = job
                .ExecutionStructuredInterface
                .Outputs?
                .Where(pair => !pair.Key.SkipProcessing)
                .ToDictionary(pair => pair.Key.Name.ToLower(), pair => pair.Value?.DataId);

            var commandEntity = new DataTransferCommandEntity
            {
                Command = GetEnumValue<CommandName>(job.Id, parameterValues, InterfaceStringConstants.CommandToken),
                IsDataManagementEnabled = GetOptionalBoolValue(parameterValues, InterfaceStringConstants.DataManagementEnabled, defaultValue: true).Value,
                ComplianceCluster = TryGetParameterValue(parameterValues, InterfaceStringConstants.ComplianceClusterToken)
            };

            switch (commandEntity.Command)
            {
            case CommandName.Copy:
                commandEntity.CopyCommand = new DataTransferCopyCommand
                {
                    OriginType = GetEnumValue<CloudStorageType>(job.Id, parameterValues, InterfaceStringConstants.OriginTypeToken),
                    OriginDataId = GetParameterValue(job.Id, inputValues, InterfaceStringConstants.OriginToken),
                    DestinationType = GetEnumValue<CloudStorageType>(job.Id, parameterValues, InterfaceStringConstants.DestinationTypeToken),
                    DestinationReplicationFactor = GetOptionalIntValue(parameterValues, InterfaceStringConstants.DestinationReplicationFactorToken, defaultValue: null),
                    DestinationFileName = TryGetParameterValue(parameterValues, InterfaceStringConstants.DestinationFileNameToken),
                    DestinationInputDataId = TryGetParameterValue(inputValues, InterfaceStringConstants.DestinationInputToken),
                    DestinationPathRoot = TryGetParameterValue(parameterValues, InterfaceStringConstants.DestinationRootToken),
                    DestinationPath = TryGetParameterValue(parameterValues, InterfaceStringConstants.DestinationPathToken),
                    DestinationOutputDataId = GetParameterValue(job.Id, outputValues, InterfaceStringConstants.DestinationOutputToken),
                    DestinationRunAs = TryGetParameterValue(parameterValues, InterfaceStringConstants.DestinationRunAsToken),
                    OriginRunAs = TryGetParameterValue(parameterValues, InterfaceStringConstants.OriginRunAsToken),
                    DestinationExpirationDays = GetOptionalIntValue(parameterValues, InterfaceStringConstants.DestinationExpirationDaysToken, defaultValue: -1).Value,
                    // data transfer service expects negative number if no expiration
                    ShouldOverrideIfExists = GetOptionalBoolValue(parameterValues, InterfaceStringConstants.DataManagementEnabled, defaultValue: false).Value,
                    CosmosShouldRespectLineBoundaries = GetOptionalBoolValue(parameterValues, InterfaceStringConstants.CosmosShouldRespectLineBoundariesToken, defaultValue: null),
                    ConcatenationRegexp = TryGetParameterValue(parameterValues, InterfaceStringConstants.ConcatenationRegexpToken),
                    DestinationDataLakeTenant = TryGetParameterValue(parameterValues, InterfaceStringConstants.DestinationDataLakeTenantToken),
                    UseMarsTestEnvironment = GetOptionalBoolValue(parameterValues, InterfaceStringConstants.UseMarsTestEnvironmentToken, defaultValue: false).Value,
                };

                if (commandEntity.CopyCommand.DestinationReplicationFactor.HasValue &&
                    commandEntity.CopyCommand.DestinationType != CloudStorageType.PhillyHdfs &&
                    commandEntity.CopyCommand.DestinationType != CloudStorageType.MultitenancyHdfs)
                {
                    throw new CommandParsingException($"Invalid module interface. Replication factor should be used only for HDFS destination, but actual destination type is {commandEntity.CopyCommand.DestinationType}");
                }

                break;
            case CommandName.Wait:
                commandEntity.WaitCommand = new DataTransferWaitCommand
                {
                    StorageType = GetEnumValue<CloudStorageType>(job.Id, parameterValues, "StorageType"),
                    TimeLimit = TimeSpan.FromMinutes(int.Parse(GetParameterValue(job.Id, parameterValues, "TimeLimitMin"))),
                    RunAs = TryGetParameterValue(parameterValues, "RunAs"),
                    InputNameToGuid = inputValues?.Where(kvp => kvp.Key.StartsWith("pathin_")).ToDictionary(kvp => kvp.Key.Substring("pathin_".Length), kvp => kvp.Value) ?? new Dictionary<string, string>(),
                    OutputGuidToName = outputValues?.Where(kvp => kvp.Key.StartsWith("pathout_")).ToDictionary(kvp => kvp.Value, kvp => kvp.Key.Substring("pathout_".Length)) ?? new Dictionary<string, string>(),
                };

                foreach (string outputName in commandEntity.WaitCommand.OutputGuidToName.Values)
                {
                    if (!commandEntity.WaitCommand.InputNameToGuid.ContainsKey(outputName))
                    {
                        throw new CommandParsingException($"Output with name {outputName} doesn't have corresponding input");
                    }
                }
                break;

            case CommandName.DataCopy:
                if (inputValues?.Count == 0 || outputValues?.Count != 1)
                {
                    throw new CommandParsingException($"Interface should contain at least 1 inputs (has {inputValues?.Count}) and exactly 1 output (has {outputValues?.Count}).");
                }

                commandEntity.DataCopyCommand = BuildDataCopyCommand(job, parameterValues, metadataValues, inputValues, outputValues);
                break;

            default:
                throw new CommandParsingException($"Unsupported command type: {commandEntity.Command}");
            }

            return commandEntity;
        }

        private static DataTransferDataCopyCommand BuildDataCopyCommand(DataTransferJobMetadata job,
            Dictionary<string, string> parameterValues, Dictionary<string, string> metadataValues,
            Dictionary<string, string> inputValues, Dictionary<string, string> outputValues)
        {
            if (job.Version == (int)DataTransferModuleVersions.Version3)
            {
                // parse the port type, when version is 3
                IList<FileBasedPathType> inPortTypeList = ModuleHelper.GetFileBasedPathTypeFromInput(job);
                IList<FileBasedPathType> outPortTypeList = ModuleHelper.GetFileBasedPathTypeFromOutput(job);
                // check if the job is based on Multiple-In/One-out model for data transfer     
                ValidatePortDataTypes(inPortTypeList, outPortTypeList);
                // Vaildate data consistent between AssetType and PortType
                ValidateDataConsistency(job, inPortTypeList, outPortTypeList);

                return new DataTransferDataCopyCommand
                {
                    ComputeName = job.GraphDefaultCompute?.Name ?? GetParameterValue(job.Id, metadataValues, InterfaceStringConstants.ComputeName),
                    InPortDataTypes = inPortTypeList,
                    OutPortDataType = outPortTypeList.FirstOrDefault()
                };
            }
            else if (job.Version == (int)DataTransferModuleVersions.Version2)
            {
                string sourceInputName = GetParameterValue(job.Id, metadataValues, InterfaceStringConstants.SourceInputName);
                string destinationInputName = GetParameterValue(job.Id, metadataValues, InterfaceStringConstants.DestinationInputName);
                string outputName = GetParameterValue(job.Id, metadataValues, InterfaceStringConstants.OutputName);

                return new DataTransferDataCopyCommand
                {
                    ComputeName = job.GraphDefaultCompute?.Name ?? GetParameterValue(job.Id, metadataValues, InterfaceStringConstants.ComputeName),
                    AzureDataFactoryConfig = null,
                    SourceDataId = GetParameterValue(job.Id, inputValues, sourceInputName),
                    DestinationDataId = GetParameterValue(job.Id, inputValues, destinationInputName),
                    OutputDataId = GetParameterValue(job.Id, outputValues, outputName),
                    CopyOptions = TryGetParameterValue(parameterValues, InterfaceStringConstants.CopyOptions),
                };
            }


            // maintain compatibility with older versions of SDK
            return new DataTransferDataCopyCommand
            {
                AzureDataFactoryConfig = new AzureDataFactoryConfig
                {
                    SubscriptionId = GetParameterValue(job.Id, metadataValues, "DataFactorySubscriptionId"),
                    ResourceGroupName = GetParameterValue(job.Id, metadataValues, "DataFactoryResourceGroup"),
                    FactoryName = GetParameterValue(job.Id, metadataValues, "DataFactoryName"),
                },
                SourceDataId = GetParameterValue(job.Id, inputValues, "SourceLocation"),
                DestinationDataId = GetParameterValue(job.Id, inputValues, "DestinationLocation"),
                OutputDataId = GetParameterValue(job.Id, outputValues, "Output"),
                CopyOptions = TryGetParameterValue(metadataValues, InterfaceStringConstants.CopyOptions),
                ComputeName = job.GraphDefaultCompute?.Name ?? GetParameterValue(job.Id, metadataValues, "DataFactoryName")
            };
        }

        private static void ValidateInterface(DataTransferJobMetadata job)
        {
            if (!CheckUniqueEntries(job.ExecutionStructuredInterface.MetadataParameters?.Select(param => param.Key.Name)))
            {
                throw new ArgumentException("Duplicate metadata parameter specified.");
            }
            if (!CheckUniqueEntries(job.ExecutionStructuredInterface.Parameters?.Select(param => param.Key.Name)))
            {
                throw new ArgumentException("Duplicate parameter specified.");
            }
        }

        private static bool CheckUniqueEntries(IEnumerable<string> entries)
        {
            if (entries == null)
            {
                return true;
            }
            var entriesList = entries as IList<string> ?? entries.ToList();
            return entriesList.Distinct().Count() == entriesList.Count;
        }

        private async Task GenerateDataIdAndDataReferenceAsync(DataTransferJobMetadata job, IDataSetAdapter adapter, JobDualLogger dualLogger, IAmlSvcClientFactory amlClientFactory)
        {
            // generate dataId from Dataset/AssetDefinition update input.
            foreach (CloudModuleInputExecutionInfo item in job.InputInfos)
            {
                var executionInterfaceInputs = job.ExecutionStructuredInterface.Inputs.Where(pair => pair.Key.Name.Equals(item.Name, StringComparison.OrdinalIgnoreCase)).ToList();
                if (executionInterfaceInputs.Count != 1)
                {
                    CommonLogger.LogEntityWarning(job.Id, "Couldn't read input settings for input {job_input_name}: expected 1, got: {input_settings_count}", item.Name, executionInterfaceInputs.Count);
                    continue;
                }

                var executionInterfaceInput = executionInterfaceInputs.Single();

                if (item != null && item.DataId == null)
                {
                    if (item?.DataLocation?.AmlDataset != null)
                    {
                        if (string.IsNullOrWhiteSpace(item?.DataLocation?.AmlDataset?.SavedDataSetReference?.Id))
                        {
                            throw new ArgumentException($"Input dataset is invalid, SavedDatasetId is not available");
                        }
                        item.DataId = item.DataLocation.AmlDataset.SavedDataSetReference.Id;
                        if (!executionInterfaceInput.Key.SkipProcessing)
                        {
                            executionInterfaceInput.Value.DataId = item.DataLocation.AmlDataset.SavedDataSetReference.Id;
                        }
                        dualLogger.LogEntityInfo(job.Id, $"Preparing inputInfos with saveddatasetId : {item.DataId} for input name inputName: {item.Name}");
                    }
                    else if (item?.DataLocation?.AssetDefinition != null)
                    {
                        if (string.IsNullOrWhiteSpace(item?.DataLocation?.AssetDefinition?.SerializedAssetId))
                        {
                            throw new InvalidOperationException($"Input AssetDefinition is invalid, SerializedAssetId is not available.");
                        }
                        string dataId = Guid.NewGuid().ToString();
                        item.DataId = dataId;
                        if (!executionInterfaceInput.Key.SkipProcessing)
                        {
                            executionInterfaceInput.Value.DataId = dataId;
                        }
                        dualLogger.LogEntityInfo(job.Id, $"Preparing inputInfos with asset random generation Id : {item.DataId} for input name inputName: {item.Name}");
                    }
                }
            }

            Dictionary<string, CloudModuleInputExecutionInfo> jobInputInfosByDataId = job.InputInfos.ToDictionary(info => info.DataId);
            foreach (KeyValuePair<StructuredInterfaceInput, ModuleInputExecutionInfo> valuePair in job.ExecutionStructuredInterface.Inputs?.Where(pair => !pair.Key.SkipProcessing).ToList())
            {
                if (valuePair.Value?.DataLocation == null)
                {
                    continue;
                }
                // Generates and setup datareference for backward compat
                await GenerateDataReferenceForInputAsync(valuePair, job, jobInputInfosByDataId, adapter, amlClientFactory);
            }
        }

        private async Task GenerateDataReferenceForInputAsync(
            KeyValuePair<StructuredInterfaceInput, ModuleInputExecutionInfo> valuePair,
            DataTransferJobMetadata job,
            Dictionary<string, CloudModuleInputExecutionInfo> jobInputInfosByDataId,
            IDataSetAdapter adapter,
            IAmlSvcClientFactory amlClientFactory)
        {
            if (valuePair.Value?.DataLocation?.DataReference == null)
            {
                if (valuePair.Value?.DataLocation?.AmlDataset != null)
                {
                    valuePair.Value.DataLocation.DataReference = await adapter.ConvertToDataRefAsync(valuePair.Value.DataLocation.AmlDataset, job);
                }
                else if (valuePair.Value?.DataLocation?.AssetDefinition != null)
                {
                    DataLocation dataLocation = await InputDataLocationUtils.GenerateInputDataLocationAsync(job, valuePair.Value.DataLocation.AssetDefinition, amlClientFactory, _dataStoreClient);
                    valuePair.Value.DataLocation.DataReference = dataLocation.DataReference;
                    if (jobInputInfosByDataId != null && jobInputInfosByDataId.ContainsKey(valuePair.Value.DataId))
                    {
                        jobInputInfosByDataId[valuePair.Value.DataId].DataLocation.DataReference = dataLocation.DataReference;
                        jobInputInfosByDataId[valuePair.Value.DataId].DataLocation.DataStoreName = dataLocation.DataStoreName;
                    }
                }
            }
            return;
        }

        private async Task GenerateOutputDataReference(DataTransferJobMetadata job, JobDualLogger dualLogger, IDataSetAdapter adapter)
        {
            if (job.ExecutionStructuredInterface.Outputs?
                            .Where(pair => !pair.Key.SkipProcessing).Count() == 1 &&
                            job.ExecutionStructuredInterface.Inputs?
                            .Where(pair => !pair.Key.SkipProcessing).Count() >= 1)
            {
                var parameterValues = job.ExecutionStructuredInterface.Parameters?.
                                      Where(pair => pair.Key.Name.Equals(_commandParamName, StringComparison.OrdinalIgnoreCase));
                // check if this is a DataCopy command
                if (parameterValues == null || parameterValues.Count() == 0 ||
                    parameterValues.First().Value == null ||
                    !parameterValues.First().Value.Equals(_dataCopyCommandName, StringComparison.OrdinalIgnoreCase))
                {
                    return;
                }

                dualLogger.LogEntityInfo(job.Id, "Starting populate dataReference for output");
                CloudModuleOutputExecutionInfo outputInfo = job.OutputInfos.First();
                var executionInterfaceOutputSettingsList = job.ExecutionStructuredInterface.Outputs.
                                                           Where(pair => !pair.Key.SkipProcessing &&
                                                                 pair.Key.Name.Equals(outputInfo.Name, StringComparison.OrdinalIgnoreCase));
                if (executionInterfaceOutputSettingsList == null ||
                    executionInterfaceOutputSettingsList.Count() == 0 ||
                    executionInterfaceOutputSettingsList.First().Value == null ||
                    executionInterfaceOutputSettingsList.First().Key == null)
                {
                    throw new ArgumentException($"Output setting {outputInfo.Name} cannot be null");
                }

                var executionInterfaceOutputSettings = executionInterfaceOutputSettingsList.First();

                DataReference outputDataReference = new DataReference();
                string datastoreName = "";
                if (executionInterfaceOutputSettings.Key?.DatasetOutput != null)
                {
                    string pathOnDatastore = executionInterfaceOutputSettings.Key.DatasetOutput.DatasetOutputOptions?.PathOnDatastore;
                    if (string.IsNullOrEmpty(pathOnDatastore))
                    {
                        pathOnDatastore = $"/dataset/{job.RunId}/{executionInterfaceOutputSettings.Key.Name}/";
                    }
                    else
                    {
                        pathOnDatastore = OutputDataLocationUtils.CheckAndExpandMagicString(pathOnDatastore, job.RunId, executionInterfaceOutputSettings.Key.Name);
                    }

                    dualLogger.LogEntityInfo(job.Id, $"Generating path for output dataset path:{pathOnDatastore}");

                    if (executionInterfaceOutputSettings.Key.DatasetOutput.DatasetRegistration == null)
                    {
                        executionInterfaceOutputSettings.Key.DatasetOutput.DatasetRegistration = new DatasetRegistration();
                    }

                    executionInterfaceOutputSettings.Key.DatasetOutput.DatasetRegistration.CreateNewVersion = true;
                    datastoreName = executionInterfaceOutputSettings.Key.DataStoreName;
                    // Generate dataReference
                    outputDataReference = adapter.ConvertToDataRefForDataSource(datastoreName, pathOnDatastore, job, null);
                }
                else if (executionInterfaceOutputSettings.Key?.AssetOutputSettings?.Path != null)
                {
                    executionInterfaceOutputSettings.Key.AssetOutputSettings.Path = AssetPathHelper.RenderAssetTemplate(
                        executionInterfaceOutputSettings.Key.AssetOutputSettings.Path,
                        defaultDatastore: executionInterfaceOutputSettings.Key.DataStoreName,
                        runId: job.RunId,
                        outputName: outputInfo.Name);
                    DataLocation dataLocation = await OutputDataLocationUtils.GenerateOutputDataLocationAsync(
                        job, executionInterfaceOutputSettings.Key, _dataStoreClient, amlSvcClientFactory: _amlClientFactory);
                    datastoreName = dataLocation.DataStoreName;
                    outputDataReference = dataLocation.DataReference;
                }
                else
                {
                    throw new ArgumentException($"DataTransfer Output setting cannot be Null");
                }

                //update the DataLocation at ExectuionStructureInterface.Outputs value(ModuleOutputExecutionInfo)
                dualLogger.LogEntityInfo(job.Id, "DataRefernce is successfully generated for v3(multiple-Inputs/One-output) model");
                if (executionInterfaceOutputSettings.Value.DataLocation == null)
                {
                    executionInterfaceOutputSettings.Value.DataLocation = new DataLocation() { };
                }
                executionInterfaceOutputSettings.Value.DataLocation.DataReference = outputDataReference;
                executionInterfaceOutputSettings.Value.DataLocation.DataStoreName = datastoreName;

                // sync DataLocation object between InputInfos and ExectuionStructureInterface.Outputs
                outputInfo.DataLocation = executionInterfaceOutputSettings.Value.DataLocation;

                string dataId = outputInfo.DataId;
                dualLogger.LogEntityInfo(job.Id, $"Successfully generated output dataReference with DataId");
            }
        }

        private async Task GenerateInputFromOutput(DataTransferJobMetadata job, JobDualLogger dualLogger, IDataSetAdapter adapter)
        {
            /* This is for the new 1-input and 1-output model requested by the SDK team. Traditionally, we pass the destination port as input.
               In this case, i.e 1-input and 1 output model. we set the destination port as output. 
               We are converting output(datastore and path) into input so that, we can re-use backend code without breaking backward compatablity.
            */
            if (job.ExecutionStructuredInterface.Outputs?
                .Where(pair => !pair.Key.SkipProcessing).Count() == 1 &&
                job.ExecutionStructuredInterface.Inputs?
                .Where(pair => !pair.Key.SkipProcessing).Count() == 1)
            {
                var parameterValues = job.ExecutionStructuredInterface.Parameters?.
                                      Where(pair => pair.Key.Name.Equals(_commandParamName, StringComparison.OrdinalIgnoreCase));
                // check if this is a DataCopy command
                if (parameterValues == null || parameterValues.Count() == 0 ||
                    parameterValues.First().Value == null ||
                    !parameterValues.First().Value.Equals(_dataCopyCommandName, StringComparison.OrdinalIgnoreCase))
                {
                    return;
                }
                List<string> inputPortDataTypeIdsList = job.ExecutionStructuredInterface.Inputs.Where(pair => !pair.Key.SkipProcessing).Select(pair => pair.Key.DataTypeIdsList).FirstOrDefault();
                string outputPortDataTypeId = job.ExecutionStructuredInterface.Outputs.Where(pair => !pair.Key.SkipProcessing).Select(pair => pair.Key.DataTypeId).FirstOrDefault();
                var portNameKeyValuePair = job.ExecutionStructuredInterface.MetadataParameters?.
                                           Where(pair => pair.Key.Name.Equals(_destinationLocationKeyName, StringComparison.OrdinalIgnoreCase));

                if (portNameKeyValuePair == null || portNameKeyValuePair.Count() == 0)
                {
                    // generate pseudo output param
                    StructuredInterfaceParameter destinationPortKey = new StructuredInterfaceParameter();
                    destinationPortKey.Name = InterfaceStringConstants.DestinationInputName;
                    job.ExecutionStructuredInterface.MetadataParameters.
                        Add(new KeyValuePair<StructuredInterfaceParameter, string>(destinationPortKey, _destinationLocationKeyName));
                }
                // get executionInterfaceInputSettings to obtain the asset type
                var executionStructuredInterfaceInputs = job.ExecutionStructuredInterface.Inputs.Where(pair => !pair.Key.SkipProcessing);
                var executionInterfaceInputSettingsList = executionStructuredInterfaceInputs.Where(pair => JobInputsInfoContainsInputKeyName(job, pair.Key.Name));

                if (executionInterfaceInputSettingsList == null ||
                    executionInterfaceInputSettingsList.Count() == 0 ||
                    executionInterfaceInputSettingsList.First().Value == null ||
                    executionInterfaceInputSettingsList.First().Key == null)
                {
                    throw new ArgumentException($"job input {executionStructuredInterfaceInputs.First().Key.Name} cannot be null");
                }
                var executionInterfaceInputSettings = executionInterfaceInputSettingsList.First();

                dualLogger.LogEntityInfo(job.Id, "Starting Output to Input conversion");
                CloudModuleOutputExecutionInfo outputInfo = job.OutputInfos.First();
                var executionInterfaceOutputSettingsList = job.ExecutionStructuredInterface.Outputs.
                                                           Where(pair => !pair.Key.SkipProcessing &&
                                                                 pair.Key.Name.Equals(outputInfo.Name, StringComparison.OrdinalIgnoreCase));
                if (executionInterfaceOutputSettingsList == null ||
                    executionInterfaceOutputSettingsList.Count() == 0 ||
                    executionInterfaceOutputSettingsList.First().Value == null ||
                    executionInterfaceOutputSettingsList.First().Key == null)
                {
                    throw new ArgumentException($"Output setting {outputInfo.Name} cannot be null");
                }

                var executionInterfaceOutputSettings = executionInterfaceOutputSettingsList.First();

                DataReference outputDataReference = new DataReference();
                string datastoreName = "";
                if (executionInterfaceOutputSettings.Key.DatasetOutput != null)
                {
                    string pathOnDatastore = executionInterfaceOutputSettings.Key.DatasetOutput.DatasetOutputOptions?.PathOnDatastore;
                    if (string.IsNullOrEmpty(pathOnDatastore))
                    {
                        pathOnDatastore = $"/dataset/{job.RunId}/{executionInterfaceOutputSettings.Key.Name}/";
                    }
                    else
                    {
                        pathOnDatastore = OutputDataLocationUtils.CheckAndExpandMagicString(pathOnDatastore, job.RunId, executionInterfaceOutputSettings.Key.Name);
                    }

                    dualLogger.LogEntityInfo(job.Id, $"Generating path for output dataset path:{pathOnDatastore}");

                    if (executionInterfaceOutputSettings.Key.DatasetOutput.DatasetRegistration == null)
                    {
                        executionInterfaceOutputSettings.Key.DatasetOutput.DatasetRegistration = new DatasetRegistration();
                    }

                    executionInterfaceOutputSettings.Key.DatasetOutput.DatasetRegistration.CreateNewVersion = true;
                    datastoreName = executionInterfaceOutputSettings.Key.DataStoreName;
                    // Generate dataReference
                    outputDataReference = adapter.ConvertToDataRefForDataSource(datastoreName, pathOnDatastore, job, null);
                }
                else if (executionInterfaceOutputSettings.Key.AssetOutputSettings?.Path != null)
                {
                    executionInterfaceOutputSettings.Key.AssetOutputSettings.Path = AssetPathHelper.RenderAssetTemplate(
                        executionInterfaceOutputSettings.Key.AssetOutputSettings.Path,
                        defaultDatastore: executionInterfaceOutputSettings.Key.DataStoreName,
                        runId: job.RunId,
                        outputName: outputInfo.Name);
                    // Generate dataReference
                    DataLocation dataLocation = await OutputDataLocationUtils.GenerateOutputDataLocationAsync(
                        job, executionInterfaceOutputSettings.Key, _dataStoreClient, amlSvcClientFactory: _amlClientFactory);
                    datastoreName = dataLocation.DataStoreName;
                    outputDataReference = dataLocation.DataReference;
                }
                else
                {
                    throw new ArgumentException($"DataTransfer Output setting cannot be Null");
                }

                dualLogger.LogEntityInfo(job.Id, "DataRefernce is successfully generated for 1-input and 1-output model");
                if (executionInterfaceOutputSettings.Value.DataLocation == null)
                {
                    executionInterfaceOutputSettings.Value.DataLocation = new DataLocation();
                }
                executionInterfaceOutputSettings.Value.DataLocation.DataReference = outputDataReference;
                string dataId = outputInfo.DataId;

                // pseudo generated settings for input
                ModuleInputExecutionInfo inputInfo = new ModuleInputExecutionInfo();
                inputInfo.Name = _destinationLocationKeyName;
                inputInfo.DataId = dataId;
                inputInfo.DataTypeId = outputDataReference.Type.ToString();
                inputInfo.DataLocation = new DataLocation { DataStoreName = datastoreName, DataReference = outputDataReference };
                CloudModuleInputExecutionInfo destination = new CloudModuleInputExecutionInfo(inputInfo);
                job.InputInfos.Add(destination);

                // Add ExecutionStructuredInterface interface
                StructuredInterfaceInput pseudoInputInterface = new StructuredInterfaceInput();
                pseudoInputInterface.Name = _destinationLocationKeyName;
                pseudoInputInterface.DatasetTypes = new HashSet<DatasetType> { DatasetType.File, DatasetType.Tabular };
                job.ExecutionStructuredInterface.Inputs.Add(new KeyValuePair<StructuredInterfaceInput, ModuleInputExecutionInfo>(pseudoInputInterface, inputInfo));

                // Try to find in Parameter "Copyoption Exists"
                var copyOptionResult = job.ExecutionStructuredInterface.Parameters.
                                       Where(pair => pair.Key.Name.Equals(InterfaceStringConstants.CopyOptions, StringComparison.OrdinalIgnoreCase));
                StructuredInterfaceParameter copyOptionKey = new StructuredInterfaceParameter();
                copyOptionKey.Name = InterfaceStringConstants.CopyOptions;
                dynamic copyOption = new JObject();

                if (copyOptionResult != null && copyOptionResult.Count() > 0)
                {
                    // extract the value 
                    var copyOptions = copyOptionResult.First();
                    string copyOptionValue = copyOptions.Value;
                    // Try to convert to object and see if the value is valid
                    CopyOptions copyOptionObject = TryDeserlizeToCopyOption(job.Id, copyOptionValue);

                    // We shouldn't have the check on Component SDK experience
                    // ValidateCopyOption(copyOptionObject);
                }
                else
                {
                    /*
                     * For 1-in, 1-out, we check types by order:
                     *   1) assetType value
                     *   If the assetType value is not null:
                     *      If the source_type/destination_type is not null, we compare the assetType value with the source_type/destination_type value, if they are not the same, we print a warning message and use the assetType value as copyOption's type.
                     *      If the source_type/destination_type is null, we compare the assetType value with the componentType(port data) value, if they are not the same, we print a warning message and use the assetType value as copyOption's type.
                     *   If the assetType value is null:
                     *      If the source_type/destination_type value is not null, we use the source_type/destination_type value as copyOption's type.
                     *      If the source_type/destination_type value is null, we use the componentType(port data) value as copyOption's type.
                     *   2) source_type/destination_type value;
                     *   3) port data type
                     *   4) default to be 'directory'
                     */

                    AssetDefinition assetDefinition = executionInterfaceInputSettings.Value?.DataLocation?.AssetDefinition;
                    AssetOutputSettings assetOutputSettings = executionInterfaceOutputSettings.Key?.AssetOutputSettings;
                    string inputAssetType = assetDefinition == null ? "" : ParseAssetTypeToStringConst(assetDefinition.Type, "Input");
                    string outputAssetType = assetOutputSettings == null ? "" : ParseAssetTypeToStringConst(assetOutputSettings.Type, "Output");

                    string inputPortType = ConvertInPortTypeToStringConst(job.Id, inputPortDataTypeIdsList);
                    string outputPortType = ConvertOutPortTypeToStringConst(job.Id, outputPortDataTypeId);
                    copyOption.source_reference_type = ParseDataReferenceType(job, paramName: InterfaceStringConstants.SourceType, componentType: inputPortType, inputAssetType, "Input");
                    copyOption.destination_reference_type = ParseDataReferenceType(job, paramName: InterfaceStringConstants.DestinationType, componentType: outputPortType, outputAssetType, "OutPut");

                    job.ExecutionStructuredInterface.Parameters.Add(new KeyValuePair<StructuredInterfaceParameter, string>(copyOptionKey, copyOption.ToString()));
                }
                dualLogger.LogEntityInfo(job.Id, $"Conversion from output to input completed successfully with DataId:{dataId}");
            }
        }

        private static string ParseAssetTypeToStringConst(AssetType? assetType, string parameter)
        {
            if (assetType == null || string.IsNullOrWhiteSpace(assetType.ToString()))
            {
                throw new ArgumentException($"Can not convert to PathType by assetType = {assetType}");
            }
            if (assetType != AssetType.UriFolder && assetType != AssetType.UriFile)
            {
                throw new ArgumentException($"{parameter} AssetType {assetType} should either be UriFile or UriFolder");
            }
            return assetType == AssetType.UriFolder ? InterfaceStringConstants.FolderReferenceType : InterfaceStringConstants.FileReferenceType;
        }

        private static string ParseDataReferenceType(DataTransferJobMetadata job, string paramName, string componentType, string assetType, string dataSource)
        {
            // If assetType is null, we use componentType as default datareference type
            // If assetType is not null, we use assetType as default datareference type
            var datareferenceType = componentType;
            if (!string.IsNullOrEmpty(assetType))
            {
                datareferenceType = assetType;
            }
            // check if user specified type exists.
            var typeResult = job.ExecutionStructuredInterface.Parameters.
                               Where(pair => pair.Key.Name.Equals(paramName, StringComparison.OrdinalIgnoreCase));

            if (typeResult != null && typeResult.Count() > 0)
            {
                var userSpecifiedType = typeResult.First();
                string userSpecifiedTypeValue = userSpecifiedType.Value;

                if (!string.IsNullOrEmpty(userSpecifiedTypeValue))
                {
                    // If userSpecifiedTypeValue has vaule, compare it with assetType, if they are not the same, print a warning log and set assetType as datareferenceType
                    if (!(userSpecifiedTypeValue.Equals(InterfaceStringConstants.FolderReferenceType, StringComparison.OrdinalIgnoreCase) ||
                        userSpecifiedTypeValue.Equals(InterfaceStringConstants.FileReferenceType, StringComparison.OrdinalIgnoreCase)))
                    {
                        throw new ArgumentException($"Parameter {paramName} should either be file or directory");
                    }

                    datareferenceType = userSpecifiedTypeValue.Equals(InterfaceStringConstants.FolderReferenceType, StringComparison.OrdinalIgnoreCase) ?
                                                       InterfaceStringConstants.FolderReferenceType : InterfaceStringConstants.FileReferenceType;

                    if (!string.IsNullOrEmpty(assetType) && !assetType.Equals(datareferenceType, StringComparison.OrdinalIgnoreCase))
                    {
                        // TODO: Observe the inconsistency between assetType and userSpecifiedTypeValue
                        CommonLogger.LogEntityWarning(job.Id, $" {dataSource} User specified type is {datareferenceType}, but {dataSource} assetType is {assetType}, use {assetType} instead.");
                        datareferenceType = assetType;
                    }
                }
                else
                {
                    // if userSpecifiedTypeValue has no value, compare assetType with componentType, if they are not the same, print a warning log and set assetType as datareferenceType
                    if (!string.IsNullOrEmpty(assetType) && !assetType.Equals(componentType, StringComparison.OrdinalIgnoreCase))
                    {
                        // TODO: Observe the inconsistency between assetType and componentType
                        CommonLogger.LogEntityWarning(job.Id, $"{dataSource} Component type is {componentType}, but {dataSource} assetType is {assetType}, use {assetType} instead.");
                        datareferenceType = assetType;
                    }
                }
            }

            return datareferenceType;
        }

        private static string ConvertInPortTypeToStringConst(string jobId, IList<string> inputDataTypes)
        {
            try
            {
                string inType = ConvertFileBasedPathTypeToStringConst(ModuleHelper.ConvertToFileBasedPathType(inputDataTypes));
                CommonLogger.LogEntityInfo(jobId, $"Identified InPort datatype string inputs: {inType}");
                return inType;
            }
            catch (Exception e)
            {
                /*
                 * Why swallow the exception?  
                 * We are not 100% sure current version2 user experiments are always with valid DataTypeIdList or not.
                 * If it is null or empty, exception might throw. We do not want to break existing experiments.
                 */
                CommonLogger.LogEntityError(jobId, "Found error while parse inPort data type into string. We will default to Folder type: {safe_exception_message}, stack trace: {stack_trace}", e.Message, e.StackTrace);
                // If any exception, default to Folder type.
                return InterfaceStringConstants.FolderReferenceType;
            }
        }

        private static string ConvertOutPortTypeToStringConst(string jobId, string outputDataType)
        {
            try
            {
                string outType = ConvertFileBasedPathTypeToStringConst(ModuleHelper.ConvertToFileBasedPathType(outputDataType));
                CommonLogger.LogEntityInfo(jobId, $"Identified OutPort datatype string inputs: outpus: {outType}");
                return outType;
            }
            catch (Exception e)
            {
                /*
                 * Why swallow the exception?  
                 * We are not 100% sure current version2 user experiments are always with valid DataTypeIdList or not.
                 * If it is null or empty, exception might throw. We do not want to break existing experiments.
                 */
                CommonLogger.LogEntityError(jobId, "Found error while parse outPort data type into string. We will default to Folder type : {safe_exception_message}, stack trace: {stack_trace}", e.Message, e.StackTrace);
                // If any exception, default to Folder type.
                return InterfaceStringConstants.FolderReferenceType;
            }
        }

        private static string ConvertFileBasedPathTypeToStringConst(FileBasedPathType type)
        {
            if (type == FileBasedPathType.File)
            {
                return InterfaceStringConstants.FileReferenceType;
            }
            else if (type == FileBasedPathType.Folder)
            {
                return InterfaceStringConstants.FolderReferenceType;
            }
            return InterfaceStringConstants.FolderReferenceType;
        }

        private static CopyOptions TryDeserlizeToCopyOption(string jobId, string serializedOptions)
        {
            try
            {
                return JsonConvert.DeserializeObject<CopyOptions>(serializedOptions);
            }
            catch (Exception ex)
            {
                string errorMessage = $"Invalid parameter CopyOption: {serializedOptions}";
                CommonLogger.LogEntityError(jobId, errorMessage);
                throw new ArgumentException(errorMessage, ex);
            }
        }

        private static void ValidateCopyOption(CopyOptions options)
        {
            if (!options.DestinationReferenceType.Equals(InterfaceStringConstants.FolderReferenceType, StringComparison.OrdinalIgnoreCase))
            {
                throw new ArgumentException($"Output Type of CopyOptions has to be {InterfaceStringConstants.FolderReferenceType} value :{options.DestinationReferenceType} is invalid");
            }

            if (!(options.SourceReferenceType.Equals(InterfaceStringConstants.FolderReferenceType, StringComparison.OrdinalIgnoreCase) ||
                options.SourceReferenceType.Equals(InterfaceStringConstants.FileReferenceType, StringComparison.OrdinalIgnoreCase)))
            {
                throw new ArgumentException($"Input Type of CopyOptions has to be {InterfaceStringConstants.FolderReferenceType} or {InterfaceStringConstants.FileReferenceType} value :{options.SourceReferenceType} is invalid");
            }
        }

        private static T GetEnumValue<T>(string jobId, Dictionary<string, string> parameters, string name)
        {
            string value = GetParameterValue(jobId, parameters, name);
            try
            {
                return (T)Enum.Parse(typeof(T), value);
            }
            catch (Exception e)
            {
                string errorMessage = $"Invalid parameter {name}: {value}.";
                CommonLogger.LogEntityError(jobId, "Invalid parameter {user_parameter_name}: {user_parameter_value} in GetEnumValue", name, value);
                throw new ArgumentException(errorMessage, e);
            }
        }

        private static List<string> GetCommaSeparatedStrings(string jobId, Dictionary<string, string> parameters, string name)
        {
            string value = GetParameterValue(jobId, parameters, name);
            return value?.Split(',').ToList() ?? new List<string>();
        }

        private static Dictionary<string, string> GetDefines(string jobId, Dictionary<string, string> parameters, string name)
        {
            string value = GetParameterValue(jobId, parameters, name);

            var defines = new Dictionary<string, string>();
            if (!string.IsNullOrWhiteSpace(value))
            {
                var defineList = value.Split(';');
                foreach (var define in defineList)
                {
                    var pair = define.Split('=');
                    if (pair.Length < 2)
                    {
                        throw new ArgumentException($"Invalid Define parameter {value}");
                    }
                    defines[pair[0]] = pair[1];
                }
            }

            return defines;
        }

        private static string GetParameterValue(string jobId, Dictionary<string, string> parameters, string name, bool optional = false)
        {
            string value = TryGetParameterValue(parameters, name);
            if (optional)
            {
                return value;
            }

            if (!string.IsNullOrWhiteSpace(value))
            {
                return value;
            }

            string errorMessage = $"Parameter {name} not found";
            CommonLogger.LogEntityError(jobId, "Parameter {user_parameter_name} not found", name);
            throw new ArgumentException(errorMessage);
        }

        private static int? GetOptionalIntValue(Dictionary<string, string> parameters, string name, int? defaultValue)
        {
            string value = TryGetParameterValue(parameters, name);
            if (!string.IsNullOrWhiteSpace(value))
            {
                return int.Parse(value);
            }
            return defaultValue;
        }

        private static bool? GetOptionalBoolValue(Dictionary<string, string> parameters, string name, bool? defaultValue)
        {
            string value = TryGetParameterValue(parameters, name);
            if (!string.IsNullOrWhiteSpace(value))
            {
                return bool.Parse(value);
            }
            return defaultValue;
        }

        private static string TryGetParameterValue(Dictionary<string, string> parameters, string name)
        {
            string normalizedName = name.ToLower();

            if (parameters != null)
            {
                if (parameters.ContainsKey(normalizedName))
                {
                    return parameters[normalizedName];
                }
            }
            return null;
        }

        public static int ParseVersionFromJobMetaData(DataTransferJobMetadata job)
        {
            Dictionary<string, string> metadataValues = job
                .ExecutionStructuredInterface
                .MetadataParameters?
                .ToDictionary(pair => pair.Key.Name.ToLower(), pair => pair.Value);
            int? ver = GetOptionalIntValue(metadataValues, "Version", defaultValue: (int)DataTransferModuleVersions.Version1);
            int version = ver.Value;

            string destinationInputName = GetParameterValue(job.Id, metadataValues, InterfaceStringConstants.DestinationInputName, optional: true);
            var allowOverwrite = job.CloudSettings?.DataTransferCloudConfig?.AllowOverwrite;
            if (version == (int)DataTransferModuleVersions.Version2
                && ((allowOverwrite != null && allowOverwrite == false)
                    || job.ExecutionStructuredInterface.Inputs
                          .Where(pair => !(pair.Key.SkipProcessing
                           || string.Equals(pair.Key.Name, destinationInputName, StringComparison.OrdinalIgnoreCase))).Count() > 1))
            {
                // Mark the version to be 3 when current version is 2 and any of below criteria meet:
                // 1. AllowOverwrite is false ( main to handle single input case)
                // 2. There are more than 1 input ports
                version = (int)DataTransferModuleVersions.Version3;
            }
            return version;
        }

        public static void ValidatePortDataTypes(IList<FileBasedPathType> inPortTypeList, IList<FileBasedPathType> outPortTypeList)
        {
            // this method only applied to v3 for now
            if (inPortTypeList == null || inPortTypeList.Count == 0 || outPortTypeList == null || outPortTypeList.Count == 0)
            {
                throw new ArgumentException("Input or output Port datatype list are not valid");
            }

            if (inPortTypeList.Any(type => type == FileBasedPathType.Unknown) || outPortTypeList.Any(type => type == FileBasedPathType.Unknown))
            {
                throw new ArgumentException("Port DataType [AnyFile,AnyDirectory...] must be accurately defined in experiment submission");
            }
        }

        public static bool JobInputsInfoContainsInputKeyName(DataTransferJobMetadata job, string inputName)
        {
            return job.InputInfos.Any(info => info.Name.Equals(inputName, StringComparison.OrdinalIgnoreCase)) ? true : false;
        }

        public static void ValidateDataConsistency(DataTransferJobMetadata job, IList<FileBasedPathType> inPortTypeList, IList<FileBasedPathType> outPortTypeList)
        {
            int i = 0;
            foreach (KeyValuePair<StructuredInterfaceInput, ModuleInputExecutionInfo> valuePair in job.ExecutionStructuredInterface.Inputs)
            {
                if (valuePair.Value?.DataLocation?.AssetDefinition != null)
                {
                    FileBasedPathType inputAssetType = ModuleHelper.ConvertAssetTypeToFileBasedPathType(valuePair.Value.DataLocation?.AssetDefinition?.Type, "Input", i);
                    if (inputAssetType != inPortTypeList[i])
                    {
                        CommonLogger.LogEntityWarning(job.Id, $"InPortType{i} is {inPortTypeList[i]}, but input assetType{i} is {inputAssetType}, use {inputAssetType} instead.");
                        inPortTypeList[i] = inputAssetType;
                    }
                }
                i++;
            }

            i = 0;
            foreach (KeyValuePair<StructuredInterfaceOutput, ModuleOutputExecutionInfo> valuePair in job.ExecutionStructuredInterface.Outputs)
            {
                if (valuePair.Key?.AssetOutputSettings != null)
                {
                    FileBasedPathType outputAssetType = ModuleHelper.ConvertAssetTypeToFileBasedPathType(valuePair.Key.AssetOutputSettings?.Type, "Output", i);
                    if (outputAssetType != outPortTypeList[i])
                    {
                        CommonLogger.LogEntityWarning(job.Id, $"OutPortType{i} is {outPortTypeList[i]}, but output assetType{i} is {outputAssetType}, use {outputAssetType} instead.");
                        outPortTypeList[i] = outputAssetType;
                    }
                }
                i++;
            }
        }

    }
}
