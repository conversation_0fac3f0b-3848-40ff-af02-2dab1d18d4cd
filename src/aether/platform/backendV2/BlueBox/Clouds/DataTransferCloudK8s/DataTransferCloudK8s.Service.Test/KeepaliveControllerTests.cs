﻿using Microsoft.Aether.DataTransferCloud;
using Microsoft.AspNetCore.Mvc;
using Moq;
using NUnit.Framework;
namespace DataTransferCloudK8s.Service.Test
{
    [TestFixture]
    class KeepaliveControllerTests
    {
        private readonly MockRepository _repository;
        private KeepaliveController _fakeKeepaliveController;
        private OkObjectResult _dummyOkObjectResult;

        public KeepaliveControllerTests()
        {
            _repository = new MockRepository(MockBehavior.Loose) { DefaultValue = DefaultValue.Mock, CallBase = true };
           
        }

        [SetUp]
        public void Init()
        {
            _fakeKeepaliveController = _repository.Create<KeepaliveController>().Object;
            _dummyOkObjectResult = new OkObjectResult("hello world");
            Mock.Get(_fakeKeepaliveController)
                .Setup(
                   service => service.Ok(
                       It.IsAny<object>()))
                .Returns(_dummyOkObjectResult);
        }
        [Test]
        public void TestKeepAlive()
        {
            var actualRet = _fakeKeepaliveController.GetKeepalive();
            Assert.AreEqual(_dummyOkObjectResult, actualRet);
        }
    }
}
