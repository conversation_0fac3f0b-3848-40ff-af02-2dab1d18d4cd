<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <AssemblyName>Microsoft.Aether.DataTransferCloud.Service.Test</AssemblyName>
    <IsPackable>false</IsPackable>
  </PropertyGroup>
  <Import Project="..\..\..\..\..\QTest.props" />
  <ItemGroup>
    <PackageReference Include="NUnit" />
    <PackageReference Include="NUnit3TestAdapter" />
    <PackageReference Include="Moq" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" />
    <PackageReference Include="coverlet.collector" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\DataTransferCloudK8s.Service\DataTransferCloudK8s.Service.csproj" />
  </ItemGroup>
</Project>
