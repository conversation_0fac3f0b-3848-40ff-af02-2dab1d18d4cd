﻿using Microsoft.Aether.BlueBox.DataStoreClient;
using Microsoft.Aether.BlueBox.DataStoreClient.Contracts;
using Microsoft.Aether.DataTransferCloud.DataFactory.Authentications;
using System;
using System.Security.Authentication;

namespace Microsoft.Aether.DataTransferCloud.CopyService
{
    public class DataTransferAuthenticationFactory
    {
        /// <summary>
        /// Fetch Data Transfer Authentication object for Blob
        /// </summary>
        /// <param name="blobDto"></param>
        /// <returns></returns>
        public static DataTransferAuthentication GetBlobAuthentication(AzureStorageDto blobDto)
        {
            if (blobDto == null)
            {
                throw new ArgumentException(nameof(blobDto));
            }
            if (blobDto.HasNoCredential())
            {
                // If crdential-less, use ADF msi for authentication
                return new DataTransferAuthentication.DataTransferAuthenticationBuilder(AuthenticationTypes.MSI)
                        .build();
            }

            ClientCredentialsDto credentials = blobDto.ClientCredentials;

            switch (blobDto.CredentialType)
            {
                case CredentialTypes.None:
                    {
                        throw new AuthenticationException("Blob has Credential, but credentialType is None");
                    }
                case CredentialTypes.Sas:
                    {
                        return new DataTransferAuthentication.DataTransferAuthenticationBuilder(AuthenticationTypes.Sas)
                                .SetSasToken(blobDto.SasToken)
                                .build();
                    }
                case CredentialTypes.AccountKey:
                    {
                        return new DataTransferAuthentication.DataTransferAuthenticationBuilder(AuthenticationTypes.AccountKey)
                                .SetAccountKey(blobDto.AccountKey)
                                .build();
                    }
                case CredentialTypes.ClientCredentials:
                    {
                        return new DataTransferAuthentication.DataTransferAuthenticationBuilder(AuthenticationTypes.ServicePrincipal)
                                .SetServicePrincipalId(credentials?.ClientId.ToString())
                                .SetServicePrincipalKey(credentials.ClientSecret)
                                .SetServicePrincipalTenant(credentials?.TenantId.ToString())
                                .build();
                    }
                default:
                    throw new AuthenticationException($"Could not recognize CredentialTypes: {blobDto.CredentialType}");
            }
        }
        /// <summary>
        /// Fetch Data Transfer Authentication object for ADLS GEN1
        /// </summary>
        /// <param name="dataLakeDto"></param>
        /// <returns></returns>
        public static DataTransferAuthentication GetADLSGen1Authentication(AzureDataLakeDto dataLakeDto)
        {
            if (dataLakeDto == null)
            {
                throw new ArgumentException(nameof(dataLakeDto));
            }

            if (dataLakeDto.HasNoCredential())
            {
                // If crdential-less, use ADF msi for authentication
                return new DataTransferAuthentication.DataTransferAuthenticationBuilder(AuthenticationTypes.MSI)
                        .build();
            }

            if (dataLakeDto.CredentialType.HasValue)
            {

                switch (dataLakeDto.CredentialType.Value)
                {
                    case AzureDataLakeCredentialTypes.None:
                        {
                            throw new AuthenticationException("Adls Gen1 has Credential, but credentialType is None");
                        }
                    case AzureDataLakeCredentialTypes.ServicePrincipal:
                        {
                            return new DataTransferAuthentication.DataTransferAuthenticationBuilder(AuthenticationTypes.ServicePrincipal)
                                    .SetServicePrincipalId(dataLakeDto.ClientId.ToString())
                                    .SetServicePrincipalKey(dataLakeDto.ClientSecret)
                                    .SetServicePrincipalTenant(dataLakeDto.TenantId.ToString())
                                    .build();
                        }
                    default:
                        throw new AuthenticationException($"Could not recognize AzureDataLakeCredentialTypes: {dataLakeDto.CredentialType.Value}");
                }
            }
            else {
                // If no value for CredentialType, probably datastore team data population issue, we will try to come up with possible authentication
                if (!(dataLakeDto.ClientId == Guid.Empty ||
                    string.IsNullOrEmpty(dataLakeDto.ClientSecret) ||
                    dataLakeDto.TenantId == Guid.Empty))
                {
                    // Try with ServicePrincipal if argument all valid
                    return new DataTransferAuthentication.DataTransferAuthenticationBuilder(AuthenticationTypes.ServicePrincipal)
                            .SetServicePrincipalId(dataLakeDto.ClientId.ToString())
                            .SetServicePrincipalKey(dataLakeDto.ClientSecret)
                            .SetServicePrincipalTenant(dataLakeDto.TenantId.ToString())
                            .build();
                }
                else 
                {
                    // other cases all default to be credential-less
                    return new DataTransferAuthentication.DataTransferAuthenticationBuilder(AuthenticationTypes.MSI)
                            .build();
                }
            }

        }

        /// <summary>
        /// Fetch Data Transfer Authentication object for ADLS GEN2
        /// </summary>
        /// <param name="blobDto"></param>
        /// <returns></returns>
        public static DataTransferAuthentication GetADLSGen2Authentication(AzureStorageDto storageDto)
        {
            if (storageDto == null)
            {
                throw new ArgumentException(nameof(storageDto));
            }

            if (storageDto.HasNoCredential())
            {
                // If crdential-less, use ADF msi for authentication
                return new DataTransferAuthentication.DataTransferAuthenticationBuilder(AuthenticationTypes.MSI)
                       .build();
            }

            ClientCredentialsDto credentials = storageDto.ClientCredentials;

            switch (storageDto.CredentialType)
            {
                case CredentialTypes.None:
                    {
                        throw new AuthenticationException("ADLS Gen2 has Credential, but credentialType is None");
                    }
                case CredentialTypes.Sas:
                    {
                        throw new AuthenticationException("ADLS Gen2 does not support Sas Authentication");
                    }
                case CredentialTypes.AccountKey:
                    {
                        return new DataTransferAuthentication.DataTransferAuthenticationBuilder(AuthenticationTypes.AccountKey)
                                .SetAccountKey(storageDto.AccountKey)
                                .build();
                    }
                case CredentialTypes.ClientCredentials:
                    {
                        var servicePrincipalCredential = string.IsNullOrEmpty(credentials?.ClientSecret) ? credentials?.Certificate : credentials?.ClientSecret;
                        return new DataTransferAuthentication.DataTransferAuthenticationBuilder(AuthenticationTypes.ServicePrincipal)
                                .SetServicePrincipalId(credentials?.ClientId.ToString())
                                .SetServicePrincipalKey(servicePrincipalCredential)
                                .SetServicePrincipalTenant(credentials?.TenantId.ToString())
                                .build();
                    }
                default:
                    throw new AuthenticationException($"Could not recognize CredentialTypes: {storageDto.CredentialType}");
            }
        }

        /// <summary>
        /// Fetch Data Transfer Authentication object for SQL DB
        /// </summary>
        /// <param name="sqlDto"></param>
        /// <returns></returns>
        public static DataTransferAuthentication GetSqlDBAuthentication(AzureSqlDatabaseDto sqlDto)
        {
            if (sqlDto == null)
            {
                throw new ArgumentException(nameof(sqlDto));
            }

            if (sqlDto.HasNoCredential())
            {
                // If crdential-less, use ADF msi for authentication
                return new DataTransferAuthentication.DataTransferAuthenticationBuilder(AuthenticationTypes.MSI)
                        .build();
            }

            if (sqlDto.CredentialType.HasValue)
            {
                switch (sqlDto.CredentialType.Value)
                {
                    case SqlCredentialTypes.None:
                        {
                            throw new AuthenticationException("SqlDB has Credential, but credentialType is None");
                        }
                    case SqlCredentialTypes.SqlAuthentication:
                        {
                            return new DataTransferAuthentication.DataTransferAuthenticationBuilder(AuthenticationTypes.SqlAuthentication)
                                .SetUserId(sqlDto.UserId)
                                .SetUserPassword(sqlDto.UserPassword)
                                .build();
                        }
                    case SqlCredentialTypes.ServicePrincipal:
                        {
                            return new DataTransferAuthentication.DataTransferAuthenticationBuilder(AuthenticationTypes.ServicePrincipal)
                                    .SetServicePrincipalId(sqlDto.ClientId.ToString())
                                    .SetServicePrincipalKey(sqlDto.ClientSecret)
                                    .SetServicePrincipalTenant(sqlDto.TenantId.ToString())
                                    .build();
                        }
                    default:
                        throw new AuthenticationException($"Could not recognize SqlCredentialTypes: {sqlDto.CredentialType.Value}");
                }
            }
            else {
                // If no value for CredentialType, probably datastore team data population issue, we will try to come up with possible authentication
                if (!(sqlDto.ClientId == Guid.Empty || string.IsNullOrEmpty(sqlDto.ClientSecret) || sqlDto.TenantId == Guid.Empty))
                {
                    // Try ServicePrincipal if argument valid
                    return new DataTransferAuthentication.DataTransferAuthenticationBuilder(AuthenticationTypes.ServicePrincipal)
                            .SetServicePrincipalId(sqlDto.ClientId.ToString())
                            .SetServicePrincipalKey(sqlDto.ClientSecret)
                            .SetServicePrincipalTenant(sqlDto.TenantId.ToString())
                            .build();
                }

                throw new AuthenticationException("SqlDB has Credential, but could not come up with authentication obj");
            }
        }

        /// <summary>
        /// Fetch Data Transfer Authentication object for Postgres DB
        /// </summary>
        /// <param name="postgresDto"></param>
        /// <returns></returns>
        public static DataTransferAuthentication GetPostgresDBAuthentication(AzurePostgresDatabaseDto postgresDto)
        {
            if (postgresDto == null)
            {
                throw new ArgumentException(nameof(postgresDto));
            }

            switch (postgresDto.CredentialType)
            {
                case SqlCredentialTypes.None:
                    {
                        throw new AuthenticationException("Postgresdb does not support MSI Authentication");
                    }
                case SqlCredentialTypes.SqlAuthentication:
                    {
                        return new DataTransferAuthentication.DataTransferAuthenticationBuilder(AuthenticationTypes.SqlAuthentication)
                            .SetUserId(postgresDto.UserId)
                            .SetUserPassword(postgresDto.UserPassword)
                            .build();
                    }
                case SqlCredentialTypes.ServicePrincipal:
                    {
                        throw new AuthenticationException("Postgresdb does not support ServicePrincipal Authentication");
                    }
                default:
                    throw new AuthenticationException($"Could not recognize SqlCredentialTypes: {postgresDto.CredentialType}");
            }
        }

        /// <summary>
        /// Fetch Data Transfer Authentication object for MySql DB
        /// </summary>
        /// <param name="mySqlDto"></param>
        /// <returns></returns>
        public static DataTransferAuthentication GetMySqlDBAuthentication(AzureMySqlDatabaseDto mySqlDto)
        {
            if (mySqlDto == null)
            {
                throw new ArgumentException(nameof(mySqlDto));
            }

            switch (mySqlDto.CredentialType)
            {
                case SqlCredentialTypes.None:
                    {
                        throw new AuthenticationException("MyqlDB does not support MSI Authentication");
                    }
                case SqlCredentialTypes.SqlAuthentication:
                    {
                        return new DataTransferAuthentication.DataTransferAuthenticationBuilder(AuthenticationTypes.SqlAuthentication)
                                .SetUserId(mySqlDto.UserId)
                                .SetUserPassword(mySqlDto.UserPassword)
                                .build();
                    }
                case SqlCredentialTypes.ServicePrincipal:
                    {
                        throw new AuthenticationException("MyqlDB does not support ServicePrincipal Authentication");
                    }
                default:
                    throw new AuthenticationException($"Could not recognize SqlCredentialTypes: {mySqlDto.CredentialType}");
            }
        }
    }
}
