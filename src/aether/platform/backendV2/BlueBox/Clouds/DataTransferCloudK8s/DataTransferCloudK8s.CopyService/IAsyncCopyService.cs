﻿using Microsoft.Aether.DataContracts;
using Microsoft.Aether.DataContracts.DataReferences;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Microsoft.Aether.DataTransferCloud.CopyService
{
    public interface IAsyncCopyService
    {
        Task<AsyncCopyOperationEntity> StartCopyOperationAsync(PipelineResourceBuilderMetadata metadata);

        Task<AsyncCopyOperationStatusEntity> CheckCopyOperationStatusAsync(AsyncCopyOperationEntity entity);

        Task CancelCopyOperationAsync(AsyncCopyOperationEntity entity);
    }
}
