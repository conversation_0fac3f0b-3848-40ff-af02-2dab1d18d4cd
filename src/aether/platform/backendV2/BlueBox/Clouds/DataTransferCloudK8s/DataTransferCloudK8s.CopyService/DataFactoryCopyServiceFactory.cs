﻿using System;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Aether.BlueBox.DataStoreClient;
using Microsoft.Aether.BlueBox.S2S.Common;
using Microsoft.Aether.BlueBox.WorkspaceResourcesClient;
using Microsoft.Aether.BlueBox.WorkspaceResourcesContracts;
using Microsoft.Aether.DataContracts.Entity;
using Microsoft.Aether.DataContracts.Error;
using Microsoft.Aether.DataTransfer.Cloud.Common.DataContracts;
using Microsoft.Aether.DataTransferCloud.DataFactory;
using Microsoft.MachineLearning.Common.Core.Contracts.Errors;
using Microsoft.MachineLearning.Common.Core.Exceptions;
using Microsoft.RelInfra.Instrumentation;
using Microsoft.RelInfra.Instrumentation.Logging;

namespace Microsoft.Aether.DataTransferCloud.CopyService
{
    public class DataFactoryCopyServiceFactory : IAsyncCopyServiceFactory
    {
        private readonly IAmlWorkspaceMsiTokenProvider _amlWorkspaceMsiTokenProvider;
        private readonly IDataStoreClient _dataStoreClient;
        private readonly CounterManager _counterManager;
        private readonly ICommonLogger _dualLogger;
        private readonly IWorkspaceResourcesClient _workspaceResourcesClient;

        private const string DataFactoryResourceIdPattern = "^/subscriptions/(?<subscription>[^/]*)/resourceGroups/(?<resourceGroup>[^/]*)/providers/Microsoft.DataFactory/factories/(?<factoryName>[^/]*)$";
        private static readonly Regex DataFactoryResourceIdRegex = new Regex(DataFactoryResourceIdPattern, RegexOptions.IgnoreCase | RegexOptions.Compiled);

        public DataFactoryCopyServiceFactory(
            IAmlWorkspaceMsiTokenProvider amlWorkspaceMsiTokenProvider,
            IDataStoreClient dataStoreClient,
            CounterManager counterManager,
            ICommonLogger dualLogger,
            IWorkspaceResourcesClient workspaceResourcesClient)
        {
            _amlWorkspaceMsiTokenProvider = amlWorkspaceMsiTokenProvider ?? throw new ArgumentNullException(nameof(amlWorkspaceMsiTokenProvider));
            _dataStoreClient = dataStoreClient ?? throw new ArgumentNullException(nameof(dataStoreClient));
            _counterManager = counterManager ?? throw new ArgumentNullException(nameof(counterManager));
            _dualLogger = dualLogger ?? throw new ArgumentNullException(nameof(dualLogger));
            _workspaceResourcesClient = workspaceResourcesClient ?? throw new ArgumentNullException(nameof(workspaceResourcesClient));
        }

        public async Task<IAsyncCopyService> CreateCopyServiceAsync(DataTransferJobMetadata job)
        {
            WorkspaceIdentity workspaceIdentity = job.WorkspaceIdentity;
            string msiToken = await _amlWorkspaceMsiTokenProvider.GetWorkspaceMsiTokenAsync(workspaceIdentity, job.CreatedBy);
            AzureDataFactoryConfig config = await GetDataFactoryConfigAsync(job);

            var pipelineResourceBuilder = new PipelineResourceBuilder(_dataStoreClient, workspaceIdentity, job.CreatedBy);

            var dataFactoryClient = new DataFactoryClient(
                loggingId: job.Id,
                dataFactorySubscriptionId: config.SubscriptionId,
                dataFactoryResourceGroup: config.ResourceGroupName,
                dataFactoryName: config.FactoryName,
                dataFactoryAuthToken: msiToken,
                _dualLogger);

            if (job.Status == CloudCommon.JobStatus.Submitted)
            {
                try
                {
                    await dataFactoryClient.ValidateDataFactoryReadyAsync();
                }
                catch (DataFactoryRetriableException ex)
                {
                    throw new RetriableCopyOperationException("Transient error while validating data factory provisioning state", ex);
                }
            }

            return new DataFactoryCopyService(job.Id, dataFactoryClient, pipelineResourceBuilder, _counterManager, _dualLogger);
        }

        private async Task<AzureDataFactoryConfig> GetDataFactoryConfigAsync(DataTransferJobMetadata job)
        {
            if (job.CommandEntity.DataCopyCommand.AzureDataFactoryConfig == null)
            {
                job.CommandEntity.DataCopyCommand.AzureDataFactoryConfig = await GetDataFactoryConfigFromComputeAsync(job);
            }

            return job.CommandEntity.DataCopyCommand.AzureDataFactoryConfig;
        }

        private async Task<AzureDataFactoryConfig> GetDataFactoryConfigFromComputeAsync(DataTransferJobMetadata job)
        {
            string computeName = job.CommandEntity.DataCopyCommand.ComputeName;

            if (string.IsNullOrEmpty(computeName))
            {
                CommonLogger.LogEntityError(job.RunId, "Compute target is Null or Empty");
                throw new InvalidOperationException("One of the Data factory config or compute name is required for data transfer service to work");
            }

            ComputeResourceAndSecrets computeAndSecrets = await _workspaceResourcesClient.GetComputeResourceAndSecretsAsync(
                workspace: job.WorkspaceIdentity,
                computeName,
                job.CreatedBy,
                cancellationToken: CancellationToken.None);

            ComputeResource compute = computeAndSecrets?.Resource;

            if (compute == null ||
                compute.Properties?.ComputeType == null ||
                compute.Properties?.ResourceId == null ||
                compute.Properties?.ProvisioningState == null ||
                !compute.Properties.ProvisioningState.Equals("Succeeded", StringComparison.OrdinalIgnoreCase))
            {
                CommonLogger.LogEntityError(job.RunId, "Compute target is not provisioned");
                throw new InvalidOperationException($"Compute target {computeName} is not correctly provisioned");
            }

            if (!compute.Properties.ComputeType.Equals("DataFactory", StringComparison.OrdinalIgnoreCase))
            {
                BaseError error = new InvalidDataTransferCompute().ToBaseError(target: null, messageParams: new[] { computeName, compute.Properties.ComputeType });                
                CommonLogger.LogEntityError(job.RunId, error.Message);
                throw new BaseException(error);
            }

            string resourceId = compute.Properties.ResourceId;
            var match = DataFactoryResourceIdRegex.Match(resourceId);

            if (!match.Success)
            {
                CommonLogger.LogEntityError(job.RunId, "Compute target has an invalid resource id");
                throw new InvalidOperationException($"Compute {computeName} has an invalid resource id: {resourceId}");
            }

            return new AzureDataFactoryConfig
            {
                SubscriptionId = match.Groups["subscription"].Value,
                ResourceGroupName = match.Groups["resourceGroup"].Value,
                FactoryName = match.Groups["factoryName"].Value,
            };
        }
    }
}
