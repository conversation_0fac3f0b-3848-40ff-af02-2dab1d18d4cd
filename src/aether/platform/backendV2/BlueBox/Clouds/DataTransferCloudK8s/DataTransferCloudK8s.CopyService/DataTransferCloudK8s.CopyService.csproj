<Project Sdk="Microsoft.NET.Sdk" DefaultTargets="Publish;Build">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <RootNamespace>Microsoft.Aether.DataTransferCloud.CopyService</RootNamespace>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="System.Net.Security" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\..\..\..\RelInfra\RelInfra.Instrumentation.Library\src\Microsoft.RelInfra.Instrumentation.csproj" />
    <ProjectReference Include="..\..\..\..\Clouds\DataTransfer\CloudCommon\DataTransferCloudCommon.csproj" />
    <ProjectReference Include="..\..\..\..\shared\Microsoft.Aether.BackendCommon.Extensions\Microsoft.Aether.BackendCommon.Extensions.csproj" />
    <ProjectReference Include="..\..\..\DataStoreClient\Microsoft.Aether.BlueBox.DataStoreClient\Microsoft.Aether.BlueBox.DataStoreClient.csproj" />
    <ProjectReference Include="..\..\..\S2S.Common\Microsoft.Aether.BlueBox.S2S.Common\Microsoft.Aether.BlueBox.S2S.Common.csproj" />
    <ProjectReference Include="..\..\..\WorkspaceResourcesClient\Microsoft.Aether.BlueBox.WorkspaceResourcesClient\Microsoft.Aether.BlueBox.WorkspaceResourcesClient.csproj" />
    <ProjectReference Include="..\DataTransferCloudK8s.DataFactory\DataTransferCloudK8s.DataFactory.csproj" />
  </ItemGroup>
</Project>
