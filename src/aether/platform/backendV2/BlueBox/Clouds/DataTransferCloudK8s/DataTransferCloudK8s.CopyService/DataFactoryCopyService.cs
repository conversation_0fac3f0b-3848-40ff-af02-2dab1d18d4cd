﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Aether.BackendCommon.Logging;
using Microsoft.Aether.DataContracts;
using Microsoft.Aether.DataContracts.DataReferences;
using Microsoft.Aether.DataTransferCloud.DataFactory;
using Microsoft.Aether.DataTransferCloud.DataFactory.Resources;
using Microsoft.RelInfra.Instrumentation;
using Microsoft.RelInfra.Instrumentation.Logging;
using Newtonsoft.Json;

[assembly: InternalsVisibleTo("DataTransferCloudK8s.DataFactoryTester")]
[assembly: InternalsVisibleTo("Microsoft.Aether.DataTransferCloud.CopyService.Test")]
namespace Microsoft.Aether.DataTransferCloud.CopyService
{
    class DataFactoryCopyService : IAsyncCopyService
    {
        private readonly string _jobId;
        private readonly DataFactoryClient _dataFactoryClient;
        private readonly IPipelineResourceBuilder _pipelineResourceBuilder;
        private readonly CounterManager _counterManager;
        private readonly ICommonLogger _dualLogger;

        public DataFactoryCopyService(
            string jobId,
            DataFactoryClient dataFactoryClient,
            IPipelineResourceBuilder pipelineResourceBuilder,
            CounterManager counterManager,
            ICommonLogger dualLogger)
        {
            _jobId = jobId ?? throw new ArgumentNullException(nameof(jobId));
            _dataFactoryClient = dataFactoryClient ?? throw new ArgumentNullException(nameof(dataFactoryClient));
            _pipelineResourceBuilder = pipelineResourceBuilder ?? throw new ArgumentNullException(nameof(pipelineResourceBuilder));
            _counterManager = counterManager ?? throw new ArgumentNullException(nameof(counterManager));
            _dualLogger = dualLogger ?? throw new ArgumentNullException(nameof(dualLogger));
        }

        public async Task<AsyncCopyOperationEntity> StartCopyOperationAsync(PipelineResourceBuilderMetadata metadata)
        {

            (IList<IPipelineResource> source, IPipelineResource sink) = await _pipelineResourceBuilder.CreatePipelineResourcesAsync(metadata);

            LogCopySourceAndSink(source, sink);

            _counterManager.GetRateCounter("StartPipelineRate").Increment();

            try
            {
                using (new DisposableTimer(_counterManager.GetLatencyCounter("StartPipelineLatency")))
                {
                     if (_dualLogger is IJobDualLogger dualLogger && dualLogger.JobExecutionLogger != null)
                    {
                        try
                        {
                            var serializerSetting = new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore };
                            string sourceJson = JsonConvert.SerializeObject(source, serializerSetting);
                            string sinkJson = JsonConvert.SerializeObject(sink, serializerSetting);

                            dualLogger.JobExecutionLogger.LogEntityInfo(_jobId, $"StartCopyOperationAsync - Starting DataTransferCloud job. "
                                + $"source: {Convert.ToBase64String(Encoding.UTF8.GetBytes(sourceJson))}, "
                                + $"sink: {Convert.ToBase64String(Encoding.UTF8.GetBytes(sinkJson))}");
                        }
                        catch (Exception ex)
                        {
                            CommonLogger.LogEntityWarning(_jobId, "Failed to log downstream API details due to error {exception}", ex);
                        }
                    }

                    DataFactoryOperationEntity entity = await _dataFactoryClient.StartCopyOperationAsync(source, sink);

                    return new AsyncCopyOperationEntity
                    {
                        DataFactoryPipelineName = entity.PipelineName,
                        DataFactoryPipelineRunId = entity.PipelineRunId,
                        DataFactorySourceDatasetNames = entity.SourceDatasetNames,
                        DataFactorySinkDatasetName = entity.SinkDatasetName,
                    };
                }
            }
            catch (DataFactoryRetriableException ex)
            {
                throw new RetriableCopyOperationException("Transient error when starting copy operation", ex);
            }
        }

        public async Task<AsyncCopyOperationStatusEntity> CheckCopyOperationStatusAsync(AsyncCopyOperationEntity entity)
        {
            var dataFactoryEntity = GetDataFactoryOperationEntity(entity);

            try
            {
                var statusEntity = await _dataFactoryClient.CheckCopyOperationStatusAsync(dataFactoryEntity);

                return new AsyncCopyOperationStatusEntity
                {
                    Status = (AsyncCopyOperationStatus)statusEntity.Status,
                    Output = statusEntity.Output,
                    Error = statusEntity.Error,
                };
            }
            catch (DataFactoryRetriableException ex)
            {
                throw new RetriableCopyOperationException("Transient error when checking copy operation status", ex);
            }
        }

        public async Task CancelCopyOperationAsync(AsyncCopyOperationEntity entity)
        {
            var dataFactoryEntity = GetDataFactoryOperationEntity(entity);

            _counterManager.GetRateCounter("CancelPipelineRate").Increment();

            try
            {
                using (new DisposableTimer(_counterManager.GetLatencyCounter("CancelPipelineLatency")))
                {
                    await _dataFactoryClient.CancelCopyOperationAsync(dataFactoryEntity);
                }
            }
            catch (DataFactoryRetriableException ex)
            {
                throw new RetriableCopyOperationException("Transient network error when cancelling copy operation", ex);
            }
        }

        private static DataFactoryOperationEntity GetDataFactoryOperationEntity(AsyncCopyOperationEntity entity)
        {
            var dataFactoryEntity = new DataFactoryOperationEntity
            {
                PipelineName = entity.DataFactoryPipelineName,
                PipelineRunId = entity.DataFactoryPipelineRunId,
                SourceDatasetNames = entity.DataFactorySourceDatasetNames,
                SinkDatasetName = entity.DataFactorySinkDatasetName
            };

            ValidateCopyOperationEntity(dataFactoryEntity);

            return dataFactoryEntity;
        }

        private void LogCopySourceAndSink(IList<IPipelineResource> sources, IPipelineResource sink)
        {
            int i = 1;
            // Explicitly call EnumerableExtension.ForEach to avoid ambiguous ForEach extension method.
            // The background is dataset library introduces another extension method: System.Linq.EnumerableEx.ForEach
            EnumerableExtension.ForEach(sources, source => _dualLogger.LogEntityInfo(_jobId, "Copy source {index}: {user_data_location_uri}, {safe_pipelineresource_info}", i++, source, source.GetResourceMetrics()));
            _dualLogger.LogEntityInfo(_jobId, "Copy sink: {user_data_location_uri}, {safe_pipelineresource_info}", sink, sink.GetResourceMetrics());

            if (_dualLogger is IJobDualLogger dualLogger)
            {
                i = 1;
                // Explicitly call EnumerableExtension.ForEach to avoid ambiguous ForEach extension method.
                // The background is dataset library introduces another extension method: System.Linq.EnumerableEx.ForEach
                EnumerableExtension.ForEach(sources, source => dualLogger.StdOutOnlyLogger.LogEntityInfo(_jobId, $"Copy source {i++}: {source}"));
                dualLogger.StdOutOnlyLogger.LogEntityInfo(_jobId, $"Copy sink: {sink}");
            }
        }

        private static void ValidateCopyOperationEntity(DataFactoryOperationEntity entity)
        {
            if (entity == null || String.IsNullOrEmpty(entity.PipelineRunId) || String.IsNullOrEmpty(entity.PipelineName))
            {
                throw new InvalidOperationException("Copy operation entity is null or invalid");
            }
        }
    }
}
