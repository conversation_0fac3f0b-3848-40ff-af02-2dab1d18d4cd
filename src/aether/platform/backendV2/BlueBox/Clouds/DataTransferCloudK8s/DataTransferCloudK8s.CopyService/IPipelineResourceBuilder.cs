﻿using Microsoft.Aether.DataContracts.DataReferences;
using Microsoft.Aether.DataTransferCloud.DataFactory.Resources;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Microsoft.Aether.DataTransferCloud.CopyService
{
    public interface IPipelineResourceBuilder
    {
        Task<(IList<IPipelineResource> source, IPipelineResource sink)> CreatePipelineResourcesAsync(PipelineResourceBuilderMetadata info);
    }
}
