﻿using Microsoft.Aether.DataTransfer.Cloud.Common.DataContracts;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;

namespace Microsoft.Aether.DataTransferCloud.CopyService
{
    public class AsyncCopyOperationEntity
    {
        public string DataFactoryPipelineName { get; set; }

        public string DataFactoryPipelineRunId { get; set; }

        public IList<string> DataFactorySourceDatasetNames { get; set; }

        public string DataFactorySinkDatasetName { get; set; }

        public JObject ToStorageContract()
        {
            return JObject.FromObject(this);
        }

        public static AsyncCopyOperationEntity FromStorageContract(JObject jObject)
        {
            AsyncCopyOperationEntity entity = jObject.ToObject<AsyncCopyOperationEntity>();

            if (string.IsNullOrEmpty(entity.DataFactoryPipelineName))
            {
                throw new InvalidOperationException("Copy operation entity is invalid");
            }

            return entity;
        }

        public static AsyncCopyOperationEntity FromJobMetadata(DataTransferJobMetadata jobMetadata)
        {
            var storageEntity = jobMetadata.CommandEntity.DataCopyCommand?.CopyOperationEntity;

            if (storageEntity == null)
            {
                throw new InvalidOperationException("CopyOperationEntity is not populated in DataCopyCommand");
            }

            return FromStorageContract(storageEntity);
        }
    }
}
