﻿using System;
using System.Collections.Generic;
using Microsoft.Aether.BlueBox.DataStoreClient.Contracts;
using Microsoft.Aether.DataContracts.DataReferences;

namespace Microsoft.Aether.DataTransferCloud.CopyService
{
    public class PipelineResourceBuilderMetadata
    {
        public IList<PipelineResourceInfo> Sources { get; set; }

        public PipelineResourceInfo Sink { get; set; }
    }

    public class PipelineResourceInfo
    {
        public DataReference DataReference { get; set; }

        public DataStoreDto DataStoreDto { get; set; }

        public string FolderPath { get; set; }

        public string FileName { get; set; }

        public bool SupportsBinaryCopy => IsFileBasedReference;

        public bool CopyAsText { get; set; }

        public char ColumnDelimiter { get; set; }

        public char RowDelimiter { get; set; }

        public bool FirstRowAsHeader { get; set; }

        public FileBasedPathType PathType { get; set; }

        public DataReferenceType Type => DataReference.Type;

        public bool IsFileBasedReference => Type == DataReferenceType.AzureBlob ||
            Type == DataReferenceType.AzureDataLake || Type == DataReferenceType.AzureDataLakeGen2;

        public AzureFileBasedDataReference FileBasedReference
        {
            get
            {
                if (Type == DataReferenceType.AzureBlob)
                {
                    return DataReference.AzureBlobReference;
                }

                if (Type == DataReferenceType.AzureDataLake)
                {
                    return DataReference.AzureDataLakeReference;
                }

                if (Type == DataReferenceType.AzureDataLakeGen2)
                {
                    return DataReference.AzureDataLakeGen2Reference;
                }

                throw new InvalidOperationException($"Unsupported data reference type: {Type}");
            }
        }

        public AzureDataReference InnerDataReference
        {
            get
            {
                if (Type == DataReferenceType.AzureSqlDatabase)
                {
                    return DataReference.AzureSqlDatabaseReference;
                }

                if (Type == DataReferenceType.AzurePostgresDatabase)
                {
                    return DataReference.AzurePostgresDatabaseReference;
                }

                if (Type == DataReferenceType.AzureMySqlDatabase)
                {
                    return DataReference.AzureMySqlDatabaseReference;
                }

                return FileBasedReference;
            }
        }
    }
}
