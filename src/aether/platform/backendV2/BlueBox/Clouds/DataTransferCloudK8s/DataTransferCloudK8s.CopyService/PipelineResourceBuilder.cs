﻿using System;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Aether.BackendCommon.Extensions;
using Microsoft.Aether.BlueBox.DataStoreClient;
using Microsoft.Aether.BlueBox.DataStoreClient.Contracts;
using Microsoft.Aether.DataContracts;
using Microsoft.Aether.DataContracts.DataReferences;
using Microsoft.Aether.DataContracts.Entity;
using Microsoft.Aether.DataTransferCloud.DataFactory.Resources;
using Microsoft.RelInfra.Common;

using DataFactoryStoredProcedureParam = Microsoft.Azure.Management.DataFactory.Models.StoredProcedureParameter;
using Microsoft.Aether.DataTransferCloud.DataFactory.Authentications;
using System.Collections.Generic;

namespace Microsoft.Aether.DataTransferCloud.CopyService
{
    public class PipelineResourceBuilder : IPipelineResourceBuilder
    {
        private readonly IDataStoreClient _dataStoreClient;
        private readonly WorkspaceIdentity _workspaceIdentity;
        private readonly CreatedBy _createdBy;

        public PipelineResourceBuilder(
            IDataStoreClient dataStoreClient,
            WorkspaceIdentity workspaceIdentity,
            CreatedBy createdBy)
        {
            _dataStoreClient = dataStoreClient ?? throw new ArgumentNullException(nameof(dataStoreClient));
            _workspaceIdentity = workspaceIdentity ?? throw new ArgumentNullException(nameof(workspaceIdentity));
            _createdBy = createdBy;
        }

        public async Task<(IList<IPipelineResource> source, IPipelineResource sink)> CreatePipelineResourcesAsync(PipelineResourceBuilderMetadata metadata)
        {
            Validate(metadata);

            await UpdateDataStoreDto(metadata);
            await NormalizePathTypeAsync(metadata);

            UpdateCopyAsText(metadata);
            UpdateFolderPathAndFileName(metadata);

            return (CreatePipelineResources(metadata.Sources), CreatePipelineResource(metadata.Sink));
        }

        private static void Validate(PipelineResourceBuilderMetadata metadata)
        {
            Validate(metadata.Sources);
            Validate(metadata.Sink);
        }

        private static void Validate(IList<PipelineResourceInfo> resources)
        {
            // Explicitly call EnumerableExtension.ForEach to avoid ambiguous ForEach extension method.
            // The background is dataset library introduces another extension method: System.Linq.EnumerableEx.ForEach
            EnumerableExtension.ForEach(resources, resource => Validate(resource));
        }

        private static void Validate(PipelineResourceInfo resource)
        {
            if (resource.DataReference == null)
            {
                throw new ArgumentNullException(nameof(resource.DataReference));
            }
        }

        private async Task UpdateDataStoreDto(PipelineResourceBuilderMetadata metadata)
        {
            IList<Task> updateDataStoreDtoTasks = metadata.Sources.Select(source => UpdateDataStoreDto(source)).ToList();
            updateDataStoreDtoTasks.Add(UpdateDataStoreDto(metadata.Sink));
            await Task.WhenAll(updateDataStoreDtoTasks);
        }

        private async Task UpdateDataStoreDto(PipelineResourceInfo resource)
        {
            if (resource.DataStoreDto == null)
            {
                string dataStoreName = resource.InnerDataReference.AmlDataStoreName;

                if (string.IsNullOrEmpty(dataStoreName))
                {
                    throw new ArgumentException("Data store name cannot be null or empty");
                }

                resource.DataStoreDto = await _dataStoreClient.GetAsync(dataStoreName, _workspaceIdentity, _createdBy, CancellationToken.None);
            }
        }

        public async Task NormalizePathTypeAsync(PipelineResourceBuilderMetadata metadata)
        {
            IList<Task> normalizePathTypeTasks = metadata.Sources.Select(source => NormalizePathTypeAsync(metadata, source)).ToList();
            PipelineResourceInfo sink = metadata.Sink;
            normalizePathTypeTasks.Add(NormalizePathTypeAsync(metadata, sink));

            await Task.WhenAll(normalizePathTypeTasks);

            ValidateSourcePathTypeAfterNormalization(metadata.Sources);
            ValidatePathTypeAfterNormalization(metadata.Sources, metadata.Sink);
        }

        public void ValidateSourcePathTypeAfterNormalization(IList<PipelineResourceInfo> sources)
        {
            IList<int> unknownIndexs = new List<int>();
            for (int i = 0; i < sources.Count(); i++)
            {
                if (sources[i].IsFileBasedReference && sources[i].PathType == FileBasedPathType.Unknown)
                {
                    unknownIndexs.Add(i);
                }
            }
            if (unknownIndexs.Count > 0)
            {
                throw new InvalidOperationException($"Failed to determine File-based source pathType for inputs indexed with : [{string.Join(", ", unknownIndexs)}]");
            }
        }

        public void ValidatePathTypeAfterNormalization(IList<PipelineResourceInfo> sources, PipelineResourceInfo sink)
        {
            // if sink pathType is unknown,
            //    if single source, keep it consistent with source PathType 
            //    if multiple source, make "Folder" as defult sink type
            if (sink.IsFileBasedReference && sink.PathType == FileBasedPathType.Unknown)
            {
                sink.PathType = sources.Count == 1 && sources[0].IsFileBasedReference ? sources[0].PathType : FileBasedPathType.Folder;
            }

            // throw exception if copy activity is Folder --> File for 1-input/1-output for file-based system
            if (sources.Count == 1 && sources[0].IsFileBasedReference && sink.IsFileBasedReference)
            {
                if (sources[0].PathType == FileBasedPathType.Folder && sink.PathType == FileBasedPathType.File)
                {
                    throw new ArgumentException("Cannot copy from a directory source to a file destination.");
                }
            }

            // throw exception if multiple inputs--> File for filesystem based sink 
            // if this is non-file system like db, we ingore this check
            if (sources.Count > 1 && sink.IsFileBasedReference && sink.PathType == FileBasedPathType.File)
            {
                throw new ArgumentException("Cannot copy from multiple file sources to a file destination.");
            }
        }

        public virtual async Task NormalizePathTypeAsync(PipelineResourceBuilderMetadata metadata, PipelineResourceInfo resource)
        {
            if (resource.IsFileBasedReference)
            {
                // User specified type takes precedance
                var pathType = resource.PathType;

                // otherwise, check if already we know the type
                if (pathType == FileBasedPathType.Unknown)
                {
                    pathType = resource.FileBasedReference.PathType;
                }

                // otherwise, check from storage account
                if (pathType == FileBasedPathType.Unknown)
                {
                    try
                    {
                        pathType = await GetPathTypeFromStorageAsync(resource);
                    }
                    catch (FileNotFoundException) when (resource == metadata.Sink)
                    {
                        // destination path may not exist
                        pathType = FileBasedPathType.Unknown;
                    }
                    catch (FileNotFoundException ex)
                    {
                        throw new InvalidOperationException("Specified path couldn't be found", ex);
                    }
                }

                resource.PathType = pathType;
            }
        }

        private static async Task<FileBasedPathType> GetPathTypeFromStorageAsync(PipelineResourceInfo resource)
        {
            if (resource.Type == DataReferenceType.AzureBlob)
            {
                return await GetPathTypeFromStorageAsync(resource.DataReference.AzureBlobReference, resource.DataStoreDto);
            }

            if (resource.Type == DataReferenceType.AzureDataLake)
            {
                return await GetPathTypeFromStorageAsync(resource.DataReference.AzureDataLakeReference, resource.DataStoreDto);
            }

            if (resource.Type == DataReferenceType.AzureDataLakeGen2)
            {
                return await GetPathTypeFromStorageAsync(resource.DataReference.AzureDataLakeGen2Reference, resource.DataStoreDto);
            }

            throw new InvalidOperationException($"Unsupported data reference type: {resource.Type}");
        }

        private static async Task<FileBasedPathType> GetPathTypeFromStorageAsync(AzureBlobReference blobReference, DataStoreDto dataStoreDto)
        {
            AzureStorageDto blobDto = dataStoreDto.AzureStorageSection;

            string blobConnectionString = AzureBlobReferencePathExtensions.GetBlobStorageConnectionString(
                accountName: blobDto.AccountName,
                credential: blobDto.Credential,
                isSas: blobDto.CredentialType == BlueBox.DataStoreClient.Contracts.CredentialTypes.Sas);

            return await blobReference.GetPathTypeFromStorageAsync(blobConnectionString);
        }

        private static async Task<FileBasedPathType> GetPathTypeFromStorageAsync(AzureDataLakeReference dataLakeReference, DataStoreDto dataStoreDto)
        {
            AzureDataLakeDto dataLakeDto = dataStoreDto.AzureDataLakeSection;

            return await dataLakeReference.GetPathTypeFromStorageAsync(
                clientId: dataLakeDto.ClientId.ToString(),
                clientSecret: dataLakeDto.ClientSecret,
                clientTenantId: dataLakeDto.TenantId.ToString());
        }

        private static async Task<FileBasedPathType> GetPathTypeFromStorageAsync(AzureDataLakeGen2Reference dataLakeGen2Reference, DataStoreDto dataStoreDto)
        {
            AzureStorageDto storageDto = dataStoreDto.AzureStorageSection;

            if (storageDto.CredentialType != BlueBox.DataStoreClient.Contracts.CredentialTypes.ClientCredentials)
            {
                throw new InvalidOperationException("Only AAD auth is supported for Data Lake Gen2 accounts.");
            }

            ClientCredentialsDto clientCredentials = storageDto.ClientCredentials;

            string clientId = clientCredentials.ClientId.ToString();
            string clientSecret = clientCredentials.ClientSecret;
            string clientTenantId = clientCredentials.TenantId.ToString();

            return await dataLakeGen2Reference.GetPathTypeFromStorageAsync(clientId, clientSecret, clientTenantId);
        }

        private static void UpdateCopyAsText(PipelineResourceBuilderMetadata metadata)
        {
            IList<PipelineResourceInfo> sources = metadata.Sources;
            PipelineResourceInfo sink = metadata.Sink;

            bool binaryCopySupported = true;

            // Explicitly call EnumerableExtension.ForEach to avoid ambiguous ForEach extension method.
            // The background is dataset library introduces another extension method: System.Linq.EnumerableEx.ForEach
            EnumerableExtension.ForEach(sources, source => binaryCopySupported = binaryCopySupported && source.SupportsBinaryCopy);

            binaryCopySupported = binaryCopySupported && sink.SupportsBinaryCopy;

            if (!binaryCopySupported)
            {
                // Explicitly call EnumerableExtension.ForEach to avoid ambiguous ForEach extension method.
                // The background is dataset library introduces another extension method: System.Linq.EnumerableEx.ForEach
                EnumerableExtension.ForEach(sources, source => source.CopyAsText = true);
                sink.CopyAsText = true;
            }
        }

        private static void UpdateFolderPathAndFileName(PipelineResourceBuilderMetadata metadata)
        {
            // Explicitly call EnumerableExtension.ForEach to avoid ambiguous ForEach extension method.
            // The background is dataset library introduces another extension method: System.Linq.EnumerableEx.ForEach
            EnumerableExtension.ForEach(metadata.Sources, source => UpdateFolderPathAndFileName(source));
            UpdateFolderPathAndFileName(metadata.Sink);
        }

        private static void UpdateFolderPathAndFileName(PipelineResourceInfo resource)
        {
            if (resource.IsFileBasedReference)
            {
                string folderPath = resource.FileBasedReference.RelativePath;
                string fileName = null;

                if (resource.PathType == FileBasedPathType.File)
                {
                    (folderPath, fileName) = SplitFilePathIntoParentAndFileName(folderPath);
                }

                // need to prepend container name for blobs
                if (resource.Type == DataReferenceType.AzureBlob)
                {
                    folderPath = ForwardSlashPath.Combine(resource.DataReference.AzureBlobReference.Container, folderPath ?? "");
                }

                // need to prepend filesystem name for ADLSv2
                if (resource.Type == DataReferenceType.AzureDataLakeGen2)
                {
                    folderPath = ForwardSlashPath.Combine(resource.DataReference.AzureDataLakeGen2Reference.FileSystemName, folderPath ?? "");
                }

                resource.FolderPath = folderPath;
                resource.FileName = fileName;
            }
        }

        private static (string parentPath, string fileName) SplitFilePathIntoParentAndFileName(string path)
        {
            if (path == null)
            {
                throw new ArgumentNullException(nameof(path));
            }

            int index = path.LastIndexOfAny(new char[] { '/', '\\' });

            if (index > 0)
            {
                return (path.Substring(0, index), path.Substring(index + 1));
            }

            return (null, path);
        }

        private static IList<IPipelineResource> CreatePipelineResources(IList<PipelineResourceInfo> infos)
        {
            return infos.Select(info => CreatePipelineResource(info)).ToList();
        }

        private static IPipelineResource CreatePipelineResource(PipelineResourceInfo info)
        {
            if (info.Type == DataReferenceType.AzureBlob)
            {
                AzureStorageDto blobDto = info.DataStoreDto.AzureStorageSection;
                DataTransferAuthentication auth = DataTransferAuthenticationFactory.GetBlobAuthentication(blobDto);

                return new AzureBlobPipelineResource(
                    accountName: blobDto.AccountName,
                    authentication: auth,
                    folderPath: info.FolderPath,
                    fileName: info.FileName,
                    copyAsText: info.CopyAsText);
            }

            if (info.Type == DataReferenceType.AzureDataLake)
            {
                AzureDataLakeDto dataLakeDto = info.DataStoreDto.AzureDataLakeSection;
                AzureDataLakeReference dataLakeReference = info.DataReference.AzureDataLakeReference;
                DataTransferAuthentication auth = DataTransferAuthenticationFactory.GetADLSGen1Authentication(dataLakeDto);

                return new AzureDataLakeStorePipelineResource(
                    subscriptionId: dataLakeReference.Subscription ?? dataLakeDto.SubscriptionId.ToString(),
                    resourceGroupName: dataLakeReference.ResourceGroup ?? dataLakeDto.ResourceGroup,
                    accountName: dataLakeReference.Account ?? dataLakeDto.StoreName,
                    authentication: auth,
                    folderPath: info.FolderPath,
                    fileName: info.FileName,
                    copyAsText: info.CopyAsText);
            }

            if (info.Type == DataReferenceType.AzureSqlDatabase)
            {
                AzureSqlDatabaseDto sqlDto = info.DataStoreDto.AzureSqlDatabaseSection;
                AzureDatabaseReference sqlReference = info.DataReference.AzureSqlDatabaseReference;
                DataTransferAuthentication auth = DataTransferAuthenticationFactory.GetSqlDBAuthentication(sqlDto);

                return new AzureSqldbPipelineResource(
                    serverName: sqlDto.ServerName,
                    serverUri: sqlReference.ServerUri,
                    databaseName: sqlReference.DatabaseName,
                    authentication: auth,
                    tableName: sqlReference.TableName,
                    sqlQuery: sqlReference.SqlQuery,
                    storedProcName: sqlReference.StoredProcedureName,
                    storedProcParams: sqlReference.StoredProcedureParameters?.ToDictionary(
                        keySelector: p => p.Name,
                        elementSelector: p => new DataFactoryStoredProcedureParam(p.Value, p.Type.ToString())));
            }

            if (info.Type == DataReferenceType.AzurePostgresDatabase)
            {
                AzurePostgresDatabaseDto postgresDto = info.DataStoreDto.AzurePostgreSqlSection;
                AzureDatabaseReference postgresReference = info.DataReference.AzurePostgresDatabaseReference;
                DataTransferAuthentication auth = DataTransferAuthenticationFactory.GetPostgresDBAuthentication(postgresDto);

                return new AzurePostgresDatabasePipelineResource(
                    serverUri: postgresReference.ServerUri,
                    databaseName: postgresReference.DatabaseName,
                    authentication: auth,
                    tableName: postgresReference.TableName,
                    query: postgresReference.SqlQuery,
                    portNumber: postgresDto.PortNumber);
            }

            if (info.Type == DataReferenceType.AzureDataLakeGen2)
            {
                AzureStorageDto storageDto = info.DataStoreDto.AzureStorageSection;
                DataTransferAuthentication auth = DataTransferAuthenticationFactory.GetADLSGen2Authentication(storageDto);

                return new AzureDataLakeGen2PipelineResource(
                    accountName: storageDto.AccountName,
                    authentication: auth,
                    folderPath: info.FolderPath,
                    fileName: info.FileName,
                    copyAsText: info.CopyAsText);
            }

            if (info.Type == DataReferenceType.AzureMySqlDatabase)
            {
                AzureMySqlDatabaseDto mySqlDto = info.DataStoreDto.AzureMySqlSection;
                AzureDatabaseReference mySqlReference = info.DataReference.AzureMySqlDatabaseReference;
                DataTransferAuthentication auth = DataTransferAuthenticationFactory.GetMySqlDBAuthentication(mySqlDto);

                return new AzureMySqlDatabasePipelineResource(
                    serverUri: mySqlReference.ServerUri,
                    databaseName: mySqlReference.DatabaseName,
                    authentication: auth,
                    tableName: mySqlReference.TableName,
                    query: mySqlReference.SqlQuery,
                    portNumber: mySqlDto.PortNumber);
            }

            throw new InvalidOperationException($"Unsupported data reference type: {info.Type}");
        }
    }
}
