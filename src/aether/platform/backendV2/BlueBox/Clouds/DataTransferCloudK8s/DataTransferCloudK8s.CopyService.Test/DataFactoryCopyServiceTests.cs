﻿using Microsoft.Aether.DataContracts.DataReferences;
using Microsoft.Aether.DataTransferCloud.CopyService;
using Microsoft.Aether.DataTransferCloud.DataFactory;
using Microsoft.Aether.DataTransferCloud.DataFactory.Resources;
using Microsoft.RelInfra.Instrumentation;
using Microsoft.RelInfra.Instrumentation.Logging;
using Moq;
using NUnit.Framework;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Text;
using System.Threading.Tasks;

[assembly: InternalsVisibleTo("Microsoft.Aether.DataTransferCloud.CopyService.Test")]
namespace DataTransferCloudK8s.CopyService.Test
{
    public class DataFactoryCopyServiceTests
    {
        private readonly MockRepository _repository;

        private DataFactoryCopyService _dummyDataFactoryCopyService;
        private DataFactoryClient _fakeDataFactoryClient;
        private IPipelineResourceBuilder _fakeIPipelineResourceBuilder;
        private CounterManager _dummyCounterManager;
        private ICommonLogger _fakeICommonLogger;

        private string loggingId;
        private string dataFactorySubscriptionId;
        private string dataFactoryResourceGroup;
        private string dataFactoryName;
        private string dataFactoryAuthToken;
        private ConcurrentDictionary<string, ICounter> _dummyCounterCache;
        private DataFactoryOperationEntity _dummyDataFactoryOperationEntity;

        private DataReference sourceReference;
        private DataReference destReference;

        private IPipelineResource _fakeSource;
        private IPipelineResource _fakeSink;

        private AsyncCopyOperationEntity _dummyAsyncCopyOperationEntity;
        private DataFactoryOperationStatusEntity _dummyDataFactoryOperationStatusEntity;

        public DataFactoryCopyServiceTests()
        {
            loggingId = Guid.NewGuid().ToString();
            dataFactorySubscriptionId = Guid.NewGuid().ToString();
            dataFactoryResourceGroup = "testRG";
            dataFactoryName = "testFactoryName";
            dataFactoryAuthToken = "testFactoryAuthToken";

            _repository = new MockRepository(MockBehavior.Loose) { DefaultValue = DefaultValue.Mock, CallBase = true };
        }

        [SetUp]
        public void Init()
        {
            ICounter rateCounter = Mock.Of<ICounter>(m =>
                        m.CounterType == CounterType.Rate);
            ICounter latencyCounter = Mock.Of<ICounter>(m =>
                         m.CounterType == CounterType.Number_Percentiles);
            _dummyCounterManager = new CounterManager("serviceName", _repository.Create<ICounterFactory>().Object);
            _dummyCounterCache = new ConcurrentDictionary<string, ICounter>();
            _dummyCounterCache["StartPipelineRate"] = rateCounter;
            _dummyCounterCache["StartPipelineLatency"] = latencyCounter;
            _dummyCounterCache["CancelPipelineRate"] = rateCounter;
            _dummyCounterCache["CancelPipelineLatency"] = latencyCounter;
            typeof(CounterManager)
                .GetField("_counterCache", BindingFlags.Instance | BindingFlags.NonPublic)
                .SetValue(_dummyCounterManager, _dummyCounterCache);

            _fakeICommonLogger = _repository.Create<ICommonLogger>().Object;

            _fakeDataFactoryClient = _repository.Create<DataFactoryClient>(
                loggingId,
                dataFactorySubscriptionId,
                dataFactoryResourceGroup,
                dataFactoryName,
                dataFactoryAuthToken,
                _fakeICommonLogger
            ).Object;

            _fakeIPipelineResourceBuilder = _repository.Create<IPipelineResourceBuilder>().Object;

            _dummyDataFactoryCopyService = new DataFactoryCopyService(
                Guid.NewGuid().ToString(),
                _fakeDataFactoryClient,
                _fakeIPipelineResourceBuilder,
                _dummyCounterManager,
                _fakeICommonLogger
            );

            sourceReference = new DataReference();
            destReference = new DataReference();

            _fakeSource = _repository.Create<IPipelineResource>().Object;
            _fakeSink = _repository.Create<IPipelineResource>().Object;
            IList<IPipelineResource> fakeSources = new List<IPipelineResource>() { _fakeSource };
            Mock.Get(_fakeIPipelineResourceBuilder)
                .Setup(
                   service => service.CreatePipelineResourcesAsync(It.IsAny<PipelineResourceBuilderMetadata>()))
                .Returns(Task.FromResult((fakeSources, _fakeSink)));

            _dummyDataFactoryOperationEntity = new DataFactoryOperationEntity()
            {
                PipelineName = "testPipelineName",
                PipelineRunId = Guid.NewGuid().ToString()
            };
            Mock.Get(_fakeDataFactoryClient)
                .Setup(
                   service => service.StartCopyOperationAsync(It.IsAny<IList<IPipelineResource>>(), It.IsAny<IPipelineResource>()))
                .Returns(Task.FromResult(_dummyDataFactoryOperationEntity));

            _dummyAsyncCopyOperationEntity = new AsyncCopyOperationEntity()
            {
                DataFactoryPipelineName = "testDataFactoryPipelineName",
                DataFactoryPipelineRunId = "testDataFactoryPipelineRunId",
                DataFactorySourceDatasetNames = new List<string>() { "testDataFactorySourceDatasetName" },
                DataFactorySinkDatasetName = "testDataFactorySinkDatasetName",
            };

            _dummyDataFactoryOperationStatusEntity = new DataFactoryOperationStatusEntity()
            { 
                Status = DataFactoryOperationStatus.InProgress,
                Output = "testOutput",
                Error = "testError"
            };
            Mock.Get(_fakeDataFactoryClient)
                .Setup(
                   service => service.CheckCopyOperationStatusAsync(It.IsAny<DataFactoryOperationEntity>()))
                .Returns(Task.FromResult(_dummyDataFactoryOperationStatusEntity));

            Mock.Get(_fakeDataFactoryClient)
                .Setup(
                   service => service.CancelCopyOperationAsync(It.IsAny<DataFactoryOperationEntity>()))
                .Returns(Task.CompletedTask);
        }

        [Test]
        public async Task TestStartCopyOperationAsync_Normal_Return()
        {
            var sourceDataReferences = new List<DataReference>() { sourceReference };
            PipelineResourceBuilderMetadata metadata = new PipelineResourceBuilderMetadata
            {          
                Sources = sourceDataReferences
                                    .Select(sourceDataReference => new PipelineResourceInfo
                                    {
                                        DataReference = sourceDataReference,
                                        PathType = FileBasedPathType.Unknown,
                                    }).ToList(),
                Sink = new PipelineResourceInfo
                {
                    DataReference = destReference,
                    PathType = FileBasedPathType.Unknown,
                },
            };
            var actualRet = await _dummyDataFactoryCopyService.StartCopyOperationAsync(metadata).ConfigureAwait(false);
            Assert.IsInstanceOf<AsyncCopyOperationEntity>(actualRet);
            Assert.AreEqual(_dummyDataFactoryOperationEntity.PipelineName, actualRet.DataFactoryPipelineName);
            Assert.AreEqual(_dummyDataFactoryOperationEntity.PipelineRunId, actualRet.DataFactoryPipelineRunId);
            Assert.AreEqual(_dummyDataFactoryOperationEntity.SourceDatasetNames, actualRet.DataFactorySourceDatasetNames);
            Assert.AreEqual(_dummyDataFactoryOperationEntity.SinkDatasetName, actualRet.DataFactorySinkDatasetName);
        }

        [Test]
        public void TestStartCopyOperationAsync_StartCopyOperationAsync_RetriableCopyOperationException()
        {
            DataFactoryRetriableException exp = new DataFactoryRetriableException(new Exception("test exception"));
            Mock.Get(_fakeDataFactoryClient)
                .Setup(
                   service => service.StartCopyOperationAsync(It.IsAny<IList<IPipelineResource>>(), It.IsAny<IPipelineResource>()))
                .Throws(exp);

            var sourceDataReferences = new List<DataReference>() { sourceReference };
            PipelineResourceBuilderMetadata metadata = new PipelineResourceBuilderMetadata
            {
                Sources = sourceDataReferences
                                    .Select(sourceDataReference => new PipelineResourceInfo
                                    {
                                        DataReference = sourceDataReference,
                                        PathType = FileBasedPathType.Unknown,
                                    }).ToList(),
                Sink = new PipelineResourceInfo
                {
                    DataReference = destReference,
                    PathType = FileBasedPathType.Unknown,
                },
            };
            var actualExp = Assert.ThrowsAsync<RetriableCopyOperationException>(async() => await _dummyDataFactoryCopyService.StartCopyOperationAsync(metadata).ConfigureAwait(false));
            Assert.AreEqual("Transient error when starting copy operation", actualExp.Message);
        }

        [Test]
        public async Task TestCheckCopyOperationStatusAsync_Normal_Return()
        {
            var actualRet = await _dummyDataFactoryCopyService.CheckCopyOperationStatusAsync(_dummyAsyncCopyOperationEntity).ConfigureAwait(false);
            Assert.IsInstanceOf<AsyncCopyOperationStatusEntity>(actualRet);
            Assert.AreEqual((AsyncCopyOperationStatus)_dummyDataFactoryOperationStatusEntity.Status, actualRet.Status);
            Assert.AreEqual(_dummyDataFactoryOperationStatusEntity.Output, actualRet.Output);
            Assert.AreEqual(_dummyDataFactoryOperationStatusEntity.Error, actualRet.Error);
        }

        [Test]
        public void TestCheckCopyOperationStatusAsync_CheckCopyOperationStatusAsyncFailed_RetriableCopyOperationException()
        {
            DataFactoryRetriableException exp = new DataFactoryRetriableException(new Exception("test exception"));
            Mock.Get(_fakeDataFactoryClient)
                .Setup(
                   service => service.CheckCopyOperationStatusAsync(It.IsAny<DataFactoryOperationEntity>()))
                .Throws(exp);
            var actualExp = Assert.ThrowsAsync<RetriableCopyOperationException>(async () => await _dummyDataFactoryCopyService.CheckCopyOperationStatusAsync(_dummyAsyncCopyOperationEntity).ConfigureAwait(false));
            Assert.AreEqual("Transient error when checking copy operation status", actualExp.Message);
        }

        [Test]
        public void TestCancelCopyOperationAsync_Normal_Return()
        {
            Assert.DoesNotThrowAsync( async() => await _dummyDataFactoryCopyService.CancelCopyOperationAsync(_dummyAsyncCopyOperationEntity).ConfigureAwait(false));
        }

        [Test]
        public void TestCancelCopyOperationAsync_CancelCopyOperationAsyncFailed_RetriableCopyOperationException()
        {
            DataFactoryRetriableException exp = new DataFactoryRetriableException(new Exception("test exception"));
            Mock.Get(_fakeDataFactoryClient)
                .Setup(
                   service => service.CancelCopyOperationAsync(It.IsAny<DataFactoryOperationEntity>()))
                .Throws(exp);
            var actualExp = Assert.ThrowsAsync<RetriableCopyOperationException>(async () => await _dummyDataFactoryCopyService.CancelCopyOperationAsync(_dummyAsyncCopyOperationEntity).ConfigureAwait(false));
            Assert.AreEqual("Transient network error when cancelling copy operation", actualExp.Message);
        }

        [Test]
        public void TestValidateCopyOperationEntity_InvalidEntity_InvalidOperationException()
        {
            MethodInfo method_ValidateCopyOperationEntity = typeof(DataFactoryCopyService).GetMethod("ValidateCopyOperationEntity", BindingFlags.Static | BindingFlags.NonPublic);
            var actualExp = Assert.Throws<TargetInvocationException>(() => method_ValidateCopyOperationEntity.Invoke(null, new object[] { new DataFactoryOperationEntity()}));
            Assert.IsInstanceOf<InvalidOperationException>(actualExp.InnerException);
            Assert.AreEqual("Copy operation entity is null or invalid", actualExp.InnerException.Message);
        }
    }
}
