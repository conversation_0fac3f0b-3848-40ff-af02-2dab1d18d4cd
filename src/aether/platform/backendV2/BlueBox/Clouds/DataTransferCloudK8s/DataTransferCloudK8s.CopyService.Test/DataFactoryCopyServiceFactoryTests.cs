﻿using Microsoft.Aether.BlueBox.DataStoreClient;
using Microsoft.Aether.BlueBox.S2S.Common;
using Microsoft.Aether.BlueBox.WorkspaceResourcesClient;
using Microsoft.Aether.BlueBox.WorkspaceResourcesContracts;
using Microsoft.Aether.CloudCommon;
using Microsoft.Aether.DataContracts;
using Microsoft.Aether.DataContracts.Entity;
using Microsoft.Aether.DataTransfer.Cloud.Common.DataContracts;
using Microsoft.Aether.DataTransferCloud.CopyService;
using Microsoft.MachineLearning.Common.Core.Exceptions;
using Microsoft.RelInfra.Instrumentation;
using Microsoft.RelInfra.Instrumentation.Logging;
using Moq;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Reflection;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace DataTransferCloudK8s.CopyService.Test
{
    public class DataFactoryCopyServiceFactoryTests
    {
        private readonly MockRepository _repository;

        private DataFactoryCopyServiceFactory _dummyDataFactoryCopyServiceFactory;

        private IAmlWorkspaceMsiTokenProvider _fakeIAmlWorkspaceMsiTokenProvider;
        private IDataStoreClient _fakeIDataStoreClient;
        private CounterManager _dummyCounterManager;
        private ICommonLogger _fakeICommonLogger;
        private IWorkspaceResourcesClient _fakeIWorkspaceResourcesClient;

        private DataTransferJobMetadata _dummyDataTransferJobMetadata;
        private ComputeResourceAndSecrets _dummyComputeResourceAndSecrets;

        private readonly string _dummySubscriptionId;
        private readonly string _dummyResourceGroup;
        private readonly string _dummyFactoryName;

        public DataFactoryCopyServiceFactoryTests()
        {
            _repository = new MockRepository(MockBehavior.Loose) { DefaultValue = DefaultValue.Mock, CallBase = true };
            _dummySubscriptionId = Guid.NewGuid().ToString();
            _dummyResourceGroup = "RG-PD-0001639";
            _dummyFactoryName = "testFactory";
        }

        [SetUp]
        public void Init()
        {
            _dummyCounterManager = new CounterManager("serviceName", _repository.Create<ICounterFactory>().Object);

            _fakeIAmlWorkspaceMsiTokenProvider = _repository.Create<IAmlWorkspaceMsiTokenProvider>().Object;
            _fakeIDataStoreClient = _repository.Create<IDataStoreClient>().Object;
            _fakeIWorkspaceResourcesClient = _repository.Create<IWorkspaceResourcesClient>().Object;
            _fakeICommonLogger = _repository.Create<ICommonLogger>().Object;
            _dummyDataFactoryCopyServiceFactory = new DataFactoryCopyServiceFactory(
                _fakeIAmlWorkspaceMsiTokenProvider,
                _fakeIDataStoreClient,
                _dummyCounterManager,
                _fakeICommonLogger,
                _fakeIWorkspaceResourcesClient
            );

            _dummyDataTransferJobMetadata = new DataTransferJobMetadata()
            {
                CommandEntity = new DataTransferCommandEntity()
                {
                    DataCopyCommand = new DataTransferDataCopyCommand()
                    {
                        AzureDataFactoryConfig = new AzureDataFactoryConfig()
                        {
                            SubscriptionId = Guid.NewGuid().ToString(),
                            ResourceGroupName = "testRG",
                            FactoryName = "testFactory"
                        },
                        ComputeName = "testCompuateName"
                    }
                },
                Status = JobStatus.Submitted,
                Id = Guid.NewGuid().ToString(),
                WorkspaceIdentity = new WorkspaceIdentity()
            };

            Mock.Get(_fakeIAmlWorkspaceMsiTokenProvider)
                .Setup(
                   service => service.GetWorkspaceMsiTokenAsync(
                       It.IsAny<WorkspaceIdentity>(),
                       It.IsAny<CreatedBy>()
                       ))
                .Returns(Task.FromResult("testToken"));

            _dummyComputeResourceAndSecrets = new ComputeResourceAndSecrets()
            {
                Resource = new ComputeResource()
                {
                    Properties = new ComputeResourceProperties()
                    {
                        ComputeType = "DataFactory",
                        ResourceId = $"/subscriptions/{_dummySubscriptionId}/resourceGroups/{_dummyResourceGroup}/providers/Microsoft.DataFactory/factories/{_dummyFactoryName}",
                        ProvisioningState = "Succeeded",
                    }
                }
            };
            Mock.Get(_fakeIWorkspaceResourcesClient)
                .Setup(
                   service => service.GetComputeResourceAndSecretsAsync(
                       It.IsAny<WorkspaceIdentity>(),
                       It.IsAny<string>(),
                       It.IsAny<CreatedBy>(),
                       It.IsAny<CancellationToken>()
                       ))
                .Returns(Task.FromResult(_dummyComputeResourceAndSecrets));

        }

        [TestCase(JobStatus.Assigned)]
        [TestCase(JobStatus.Cancelled)]
        [TestCase(JobStatus.Cancelling)]
        [TestCase(JobStatus.Failed)]
        [TestCase(JobStatus.FailedSetup)]
        [TestCase(JobStatus.Running)]
        public async Task TestCreateCopyServiceAsync_NonSubmitJobStatus_Return(JobStatus status)
        {
            _dummyDataTransferJobMetadata.Status = status;
            var actualRet = await _dummyDataFactoryCopyServiceFactory.CreateCopyServiceAsync(_dummyDataTransferJobMetadata).ConfigureAwait(false);
            Assert.IsInstanceOf<DataFactoryCopyService>(actualRet);
        }

        [Test]
        public async Task TestGetDataFactoryConfigAsync_AzureDataFactoryConfigNull_Return()
        {
            _dummyDataTransferJobMetadata.CommandEntity.DataCopyCommand.AzureDataFactoryConfig = null;
            MethodInfo method_GetDataFactoryConfigAsync = typeof(DataFactoryCopyServiceFactory).GetMethod("GetDataFactoryConfigAsync", BindingFlags.Instance | BindingFlags.NonPublic);
            var task = (Task<AzureDataFactoryConfig>)method_GetDataFactoryConfigAsync.Invoke(_dummyDataFactoryCopyServiceFactory, new object[] { _dummyDataTransferJobMetadata });
            var actualRet = await task.ConfigureAwait(false);
            Assert.AreEqual(_dummySubscriptionId, actualRet.SubscriptionId);
            Assert.AreEqual(_dummyResourceGroup, actualRet.ResourceGroupName);
            Assert.AreEqual(_dummyFactoryName, actualRet.FactoryName);
        }

        [Test]
        public void TestGetDataFactoryConfigFromComputeAsync_ComputeNameNull_InvalidOperationException()
        {
            _dummyDataTransferJobMetadata.CommandEntity.DataCopyCommand.ComputeName = null;
            MethodInfo method_GetDataFactoryConfigFromComputeAsync = typeof(DataFactoryCopyServiceFactory).GetMethod("GetDataFactoryConfigFromComputeAsync", BindingFlags.Instance | BindingFlags.NonPublic);
            var task = (Task<AzureDataFactoryConfig>)method_GetDataFactoryConfigFromComputeAsync.Invoke(_dummyDataFactoryCopyServiceFactory, new object[] { _dummyDataTransferJobMetadata });
            var actualExp = Assert.ThrowsAsync<InvalidOperationException>(async() => await task.ConfigureAwait(false));
            Assert.AreEqual("One of the Data factory config or compute name is required for data transfer service to work", actualExp.Message);
        }

        [Test]
        public void TestGetDataFactoryConfigFromComputeAsync_ComputeNull_InvalidOperationException()
        {
            _dummyComputeResourceAndSecrets.Resource = null;
            MethodInfo method_GetDataFactoryConfigFromComputeAsync = typeof(DataFactoryCopyServiceFactory).GetMethod("GetDataFactoryConfigFromComputeAsync", BindingFlags.Instance | BindingFlags.NonPublic);
            var task = (Task<AzureDataFactoryConfig>)method_GetDataFactoryConfigFromComputeAsync.Invoke(_dummyDataFactoryCopyServiceFactory, new object[] { _dummyDataTransferJobMetadata });
            var actualExp = Assert.ThrowsAsync<InvalidOperationException>(async () => await task.ConfigureAwait(false));
            Assert.AreEqual($"Compute target {_dummyDataTransferJobMetadata.CommandEntity.DataCopyCommand.ComputeName} is not correctly provisioned", actualExp.Message);
        }

        [Test]
        public void TestGetDataFactoryConfigFromComputeAsync_ComputeTypeNotValid_InvalidOperationException()
        {
            _dummyComputeResourceAndSecrets.Resource.Properties.ComputeType = "DataFactory1";
            Assert.AreNotEqual(_dummyComputeResourceAndSecrets.Resource.Properties.ComputeType, "DataFactory");
            MethodInfo method_GetDataFactoryConfigFromComputeAsync = typeof(DataFactoryCopyServiceFactory).GetMethod("GetDataFactoryConfigFromComputeAsync", BindingFlags.Instance | BindingFlags.NonPublic);
            var task = (Task<AzureDataFactoryConfig>)method_GetDataFactoryConfigFromComputeAsync.Invoke(_dummyDataFactoryCopyServiceFactory, new object[] { _dummyDataTransferJobMetadata });
            var actualExp = Assert.ThrowsAsync<BaseException>(async () => await task.ConfigureAwait(false));
            StringAssert.Contains("For DataTransferStep, compute_target needs to be of type `DataFactory' (Azure Data Factory)", actualExp.Message);
        }

        [Test]
        public void TestGetDataFactoryConfigFromComputeAsync_DataFactoryResourceIdRegexNotMatch_InvalidOperationException()
        {
            _dummyComputeResourceAndSecrets.Resource.Properties.ResourceId = "hello";
            MethodInfo method_GetDataFactoryConfigFromComputeAsync = typeof(DataFactoryCopyServiceFactory).GetMethod("GetDataFactoryConfigFromComputeAsync", BindingFlags.Instance | BindingFlags.NonPublic);
            var task = (Task<AzureDataFactoryConfig>)method_GetDataFactoryConfigFromComputeAsync.Invoke(_dummyDataFactoryCopyServiceFactory, new object[] { _dummyDataTransferJobMetadata });
            var actualExp = Assert.ThrowsAsync<InvalidOperationException>(async () => await task.ConfigureAwait(false));
            Assert.AreEqual($"Compute {_dummyDataTransferJobMetadata.CommandEntity.DataCopyCommand.ComputeName} has an invalid resource id: {_dummyComputeResourceAndSecrets.Resource.Properties.ResourceId}", actualExp.Message);
        }



    }
}
