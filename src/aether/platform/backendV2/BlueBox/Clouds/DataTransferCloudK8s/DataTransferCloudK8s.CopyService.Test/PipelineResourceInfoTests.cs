﻿using Microsoft.Aether.DataContracts.DataReferences;
using Microsoft.Aether.DataTransferCloud.CopyService;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Reflection;
using System.Text;

namespace DataTransferCloudK8s.CopyService.Test
{
    public class PipelineResourceInfoTests
    {

        [TestCase(DataReferenceType.AzureBlob, true)]
        [TestCase(DataReferenceType.AzureDataLake, true)]
        [TestCase(DataReferenceType.AzureDataLakeGen2, true)]
        [TestCase(DataReferenceType.Cosmos, false)]
        [TestCase(DataReferenceType.AzurePostgresDatabase, false)]
        public void TestIsFileBasedReference_Normal_Return(DataReferenceType type, bool IsFileBasedReference)
        {
            PipelineResourceInfo info = new PipelineResourceInfo()
            {
                DataReference = new DataReference()
                {
                    Type = type,
                },
            
            };
            Assert.AreEqual(IsFileBasedReference, info.IsFileBasedReference);
        }

        [TestCase(DataReferenceType.AzureBlob, typeof(AzureBlobReference))]
        [TestCase(DataReferenceType.AzureDataLake, typeof(AzureDataLakeReference))]
        [TestCase(DataReferenceType.AzureDataLakeGen2, typeof(AzureDataLakeGen2Reference))]
        public void TestFileBasedReference_Normal_Return(DataReferenceType type, Type instanceType)
        {
            PipelineResourceInfo info = new PipelineResourceInfo()
            {
                DataReference = new DataReference()
                {
                    Type = type,
                    AzureBlobReference = new AzureBlobReference(),
                    AzureDataLakeReference = new AzureDataLakeReference(),
                    AzureDataLakeGen2Reference = new AzureDataLakeGen2Reference()
                },

            };
            Assert.True(instanceType.IsInstanceOfType(info.FileBasedReference));
        }

        [TestCase(DataReferenceType.Cosmos)]
        [TestCase(DataReferenceType.AzureMySqlDatabase)]
        [TestCase(DataReferenceType.AzureSqlDatabase)]
        public void TestFileBasedReference_NonFileBasedReference_InvalidOperationException(DataReferenceType type)
        {
            PipelineResourceInfo info = new PipelineResourceInfo()
            {
                DataReference = new DataReference()
                {
                    Type = type,
                    AzureBlobReference = new AzureBlobReference(),
                    AzureDataLakeReference = new AzureDataLakeReference(),
                    AzureDataLakeGen2Reference = new AzureDataLakeGen2Reference()
                },

            };
            AzureFileBasedDataReference kk;
            Assert.Throws<InvalidOperationException>(() => kk = info.FileBasedReference);
        }

        [TestCase(DataReferenceType.AzureDataLake, typeof(AzureFileBasedDataReference))]
        [TestCase(DataReferenceType.AzureBlob, typeof(AzureFileBasedDataReference))]
        [TestCase(DataReferenceType.AzureMySqlDatabase, typeof(AzureDatabaseReference))]
        [TestCase(DataReferenceType.AzureSqlDatabase, typeof(AzureDatabaseReference))]
        [TestCase(DataReferenceType.AzurePostgresDatabase, typeof(AzureDatabaseReference))]
        public void TestInnerDataReference_Normal_Return(DataReferenceType type, Type instanceType)
        {
            PipelineResourceInfo info = new PipelineResourceInfo()
            {
                DataReference = new DataReference()
                {
                    Type = type,
                    AzureBlobReference = new AzureBlobReference(),
                    AzureDataLakeReference = new AzureDataLakeReference(),
                    AzureDataLakeGen2Reference = new AzureDataLakeGen2Reference(),
                    AzureSqlDatabaseReference = new AzureDatabaseReference(),
                    AzurePostgresDatabaseReference = new AzureDatabaseReference(),
                    AzureMySqlDatabaseReference = new AzureDatabaseReference()
                },

            };
            Assert.True(instanceType.IsInstanceOfType(info.InnerDataReference));
        }
    }
}
