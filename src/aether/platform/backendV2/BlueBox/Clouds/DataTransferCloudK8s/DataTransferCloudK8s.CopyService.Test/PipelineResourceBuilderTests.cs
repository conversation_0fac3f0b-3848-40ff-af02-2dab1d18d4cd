﻿using Microsoft.Aether.BlueBox.DataStoreClient;
using Microsoft.Aether.BlueBox.DataStoreClient.Contracts;
using Microsoft.Aether.DataContracts;
using Microsoft.Aether.DataContracts.DataReferences;
using Microsoft.Aether.DataContracts.Entity;
using Microsoft.Aether.DataTransferCloud.CopyService;
using Microsoft.Aether.DataTransferCloud.DataFactory.Resources;
using Moq;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace DataTransferCloudK8s.CopyService.Test
{
    public class PipelineResourceBuilderTests
    {
        private readonly MockRepository _repository;

        private PipelineResourceBuilder _fakePipelineResourceBuilder;
        private IDataStoreClient _fakeIDataStoreClient;
        private WorkspaceIdentity _dummyWorkspaceIdentity;
        private CreatedBy _dummyCreatedBy;
        private PipelineResourceBuilderMetadata _dummyPipelineResourceBuilderMetadata;
        private PipelineResourceInfo _dummyPipelineResourceInfo_AzureBlob;
        private PipelineResourceInfo _dummyPipelineResourceInfo_AzureDataLake;
        private PipelineResourceInfo _dummyPipelineResourceInfo_AzureSqlDatabase;
        private PipelineResourceInfo _dummyPipelineResourceInfo_AzurePostgresDatabase;
        private PipelineResourceInfo _dummyPipelineResourceInfo_AzureDataLakeGen2;
        private PipelineResourceInfo _dummyPipelineResourceInfo_AzureMySqlDatabase;
        private PipelineResourceInfo _dummyPipelineResourceInfo_Others;
        private DataStoreDto _dummyDataStoreDto;

        public PipelineResourceBuilderTests()
        {
            _repository = new MockRepository(MockBehavior.Loose) { DefaultValue = DefaultValue.Mock, CallBase = true };
        }

        [SetUp]
        public void Init()
        {
            _fakeIDataStoreClient = _repository.Create<IDataStoreClient>().Object;
            _dummyWorkspaceIdentity = new WorkspaceIdentity();
            _dummyCreatedBy = new CreatedBy();
            _fakePipelineResourceBuilder = _repository.Create<PipelineResourceBuilder>(
                _fakeIDataStoreClient,
                _dummyWorkspaceIdentity,
                _dummyCreatedBy).Object;

            _dummyPipelineResourceBuilderMetadata = new PipelineResourceBuilderMetadata();

            _dummyPipelineResourceInfo_AzureBlob = new PipelineResourceInfo()
            {
                DataReference = new DataReference()
                {
                    Type = DataReferenceType.AzureBlob,
                },
                DataStoreDto = new DataStoreDto()
                {
                    AzureStorageSection = new AzureStorageDto()
                    {
                        AccountName = "testAccountName"
                    }
                },
                FolderPath = ".",
                FileName = "testFile",
                CopyAsText = false
            };

            _dummyPipelineResourceInfo_AzureDataLake = new PipelineResourceInfo()
            {
                DataReference = new DataReference()
                {
                    Type = DataReferenceType.AzureDataLake,
                    AzureDataLakeReference = new AzureDataLakeReference()
                },
                DataStoreDto = new DataStoreDto()
                {
                    AzureDataLakeSection = new AzureDataLakeDto()
                    {
                        SubscriptionId = Guid.NewGuid(),
                        ResourceGroup = "testResourceGroup",
                        StoreName = "testStoreName",
                    }
                },
                FolderPath = ".",
                FileName = "testFile",
                CopyAsText = false
            };

            _dummyPipelineResourceInfo_AzureSqlDatabase = new PipelineResourceInfo()
            {
                DataReference = new DataReference()
                {
                    Type = DataReferenceType.AzureSqlDatabase,
                    AzureSqlDatabaseReference = new AzureDatabaseReference()
                    {
                        TableName = "testTableName",
                        ServerUri = "testDatabaseName.database.windows.net",
                        DatabaseName = "testDatabaseName"
                    }
                },
                DataStoreDto = new DataStoreDto()
                {
                    AzureSqlDatabaseSection = new AzureSqlDatabaseDto()
                    {
                        ServerName = "testServerName",
                        Endpoint = "database.windows.net",
                        DatabaseName = "testDatabaseName"
                    }
                },
                FolderPath = ".",
                FileName = "testFile",
                CopyAsText = false
            };

            _dummyPipelineResourceInfo_AzurePostgresDatabase = new PipelineResourceInfo()
            {
                DataReference = new DataReference()
                {
                    Type = DataReferenceType.AzurePostgresDatabase,
                    AzurePostgresDatabaseReference = new AzureDatabaseReference()
                    {
                        ServerUri = "testServerUri",
                        DatabaseName = "testDatabaseName",
                        TableName = "testTableName",
                    }
                },
                DataStoreDto = new DataStoreDto()
                {
                    AzurePostgreSqlSection = new AzurePostgresDatabaseDto()
                    {
                        PortNumber = "3066",
                        UserId = "testUserId",
                        UserPassword = "[PLACEHOLDER]"
                    },
                },
                FolderPath = ".",
                FileName = "testFile",
                CopyAsText = false
            };

            _dummyPipelineResourceInfo_AzureDataLakeGen2 = new PipelineResourceInfo()
            {
                DataReference = new DataReference()
                {
                    Type = DataReferenceType.AzureDataLakeGen2,
                },
                DataStoreDto = new DataStoreDto()
                {
                    AzureStorageSection = new AzureStorageDto()
                    {
                        AccountName = "testAccountName"
                    },
                },
                FolderPath = ".",
                FileName = "testFile",
                CopyAsText = false
            };

            _dummyPipelineResourceInfo_AzureMySqlDatabase = new PipelineResourceInfo()
            {
                DataReference = new DataReference()
                {
                    Type = DataReferenceType.AzureMySqlDatabase,
                    AzureMySqlDatabaseReference = new AzureDatabaseReference()
                    {
                        ServerUri = "testServerUri",
                        DatabaseName = "testDatabaseName",
                        TableName = "testTableName"
                    }
                },
                DataStoreDto = new DataStoreDto()
                {
                    AzureMySqlSection = new AzureMySqlDatabaseDto()
                    {
                        PortNumber = "2000",
                        UserId = "testUserId",
                        UserPassword = "[PLACEHOLDER]"
                    },
                },
                FolderPath = ".",
                FileName = "testFile",
                CopyAsText = false
            };

            _dummyPipelineResourceInfo_Others = new PipelineResourceInfo()
            {
                DataReference = new DataReference()
            };

            _dummyDataStoreDto = new DataStoreDto();
            Mock.Get(_fakeIDataStoreClient)
                .Setup(
                   service => service.GetAsync(
                       It.IsAny<string>(),
                       It.IsAny<WorkspaceIdentity>(),
                       It.IsAny<CreatedBy>(),
                       It.IsAny<CancellationToken>()
                       ))
                .Returns(Task.FromResult(_dummyDataStoreDto));
        }

        [Test]
        public void TestCreatePipelineResource_AzureBlob_Return()
        {
            MethodInfo method_CreatePipelineResource = typeof(PipelineResourceBuilder).GetMethod("CreatePipelineResource", BindingFlags.Static | BindingFlags.NonPublic);
            var actualRet = (IPipelineResource)method_CreatePipelineResource.Invoke(_fakePipelineResourceBuilder, new object[] { _dummyPipelineResourceInfo_AzureBlob });
            Assert.IsInstanceOf<AzureBlobPipelineResource>(actualRet);
        }

        [Test]
        public void TestCreatePipelineResource_AzureDataLake_Return()
        {
            MethodInfo method_CreatePipelineResource = typeof(PipelineResourceBuilder).GetMethod("CreatePipelineResource", BindingFlags.Static | BindingFlags.NonPublic);
            var actualRet = (IPipelineResource)method_CreatePipelineResource.Invoke(_fakePipelineResourceBuilder, new object[] { _dummyPipelineResourceInfo_AzureDataLake });
            Assert.IsInstanceOf<AzureDataLakeStorePipelineResource>(actualRet);
        }

        [Test]
        public void TestCreatePipelineResource_AzureSqlDatabase_Return()
        {
            MethodInfo method_CreatePipelineResource = typeof(PipelineResourceBuilder).GetMethod("CreatePipelineResource", BindingFlags.Static | BindingFlags.NonPublic);
            var actualRet = (IPipelineResource)method_CreatePipelineResource.Invoke(_fakePipelineResourceBuilder, new object[] { _dummyPipelineResourceInfo_AzureSqlDatabase });
            Assert.IsInstanceOf<AzureSqldbPipelineResource>(actualRet);
        }

        [Test]
        public void TestCreatePipelineResource_AzurePostgresDatabase_Return()
        {
            MethodInfo method_CreatePipelineResource = typeof(PipelineResourceBuilder).GetMethod("CreatePipelineResource", BindingFlags.Static | BindingFlags.NonPublic);
            var actualRet = (IPipelineResource)method_CreatePipelineResource.Invoke(_fakePipelineResourceBuilder, new object[] { _dummyPipelineResourceInfo_AzurePostgresDatabase });
            Assert.IsInstanceOf<AzurePostgresDatabasePipelineResource>(actualRet);
        }

        [Test]
        public void TestCreatePipelineResource_AzureDataLakeGen2_Return()
        {
            MethodInfo method_CreatePipelineResource = typeof(PipelineResourceBuilder).GetMethod("CreatePipelineResource", BindingFlags.Static | BindingFlags.NonPublic);
            var actualRet = (IPipelineResource)method_CreatePipelineResource.Invoke(_fakePipelineResourceBuilder, new object[] { _dummyPipelineResourceInfo_AzureDataLakeGen2 });
            Assert.IsInstanceOf<AzureDataLakeGen2PipelineResource>(actualRet);
        }

        [Test]
        public void TestCreatePipelineResource_AzureMySqlDatabase_Return()
        {
            MethodInfo method_CreatePipelineResource = typeof(PipelineResourceBuilder).GetMethod("CreatePipelineResource", BindingFlags.Static | BindingFlags.NonPublic);
            var actualRet = (IPipelineResource)method_CreatePipelineResource.Invoke(_fakePipelineResourceBuilder, new object[] { _dummyPipelineResourceInfo_AzureMySqlDatabase });
            Assert.IsInstanceOf<AzureMySqlDatabasePipelineResource>(actualRet);
        }

        [TestCase(DataReferenceType.None)]
        [TestCase(DataReferenceType.Cosmos)]
        [TestCase(DataReferenceType.DBFS)]
        [TestCase(DataReferenceType.PhillyHdfs)]
        public void TestCreatePipelineResource_OtherDataReferenceType_InvalidOperationException(DataReferenceType type)
        {
            _dummyPipelineResourceInfo_Others.DataReference.Type = type;
            MethodInfo method_CreatePipelineResource = typeof(PipelineResourceBuilder).GetMethod("CreatePipelineResource", BindingFlags.Static | BindingFlags.NonPublic);
            var actualExp = Assert.Throws<TargetInvocationException>(() => method_CreatePipelineResource.Invoke(_fakePipelineResourceBuilder, new object[] { _dummyPipelineResourceInfo_Others }));
            Assert.IsInstanceOf<InvalidOperationException>(actualExp.InnerException);
            Assert.AreEqual($"Unsupported data reference type: {type}", actualExp.InnerException.Message);
        }

        [Test]
        public void TestSplitFilePathIntoParentAndFileName_PathNotValid_Return()
        {
            MethodInfo method_SplitFilePathIntoParentAndFileName = typeof(PipelineResourceBuilder).GetMethod("SplitFilePathIntoParentAndFileName", BindingFlags.Static | BindingFlags.NonPublic);
            var actualExp = Assert.Throws<TargetInvocationException>(() => method_SplitFilePathIntoParentAndFileName.Invoke(_fakePipelineResourceBuilder, new object[] { null }));
            Assert.IsInstanceOf<ArgumentNullException>(actualExp.InnerException);
        }

        [Test]
        public void TestSplitFilePathIntoParentAndFileName_Normal_Return()
        {
            string path = "/folder1/folder2";
            string filename = "file.txt";
            MethodInfo method_SplitFilePathIntoParentAndFileName = typeof(PipelineResourceBuilder).GetMethod("SplitFilePathIntoParentAndFileName", BindingFlags.Static | BindingFlags.NonPublic);
            var actualRet = ((string parentPath, string fileName))method_SplitFilePathIntoParentAndFileName.Invoke(_fakePipelineResourceBuilder, new object[] { path + "/" + filename });
            Assert.AreEqual(path, actualRet.parentPath);
            Assert.AreEqual(filename, actualRet.fileName);

            actualRet = ((string parentPath, string fileName))method_SplitFilePathIntoParentAndFileName.Invoke(_fakePipelineResourceBuilder, new object[] { filename });
            Assert.IsNull(actualRet.parentPath);
            Assert.AreEqual(filename, actualRet.fileName);
        }

        [Test]
        public void TestUpdateFolderPathAndFileName_AzureBlob_Return()
        {
            string path = "/folder1/folder2";
            string filename = "file.txt";
            string container = "/testContainer";
            PipelineResourceInfo info = new PipelineResourceInfo()
            {
                DataReference = new DataReference()
                {
                    Type = DataReferenceType.AzureBlob,
                    AzureBlobReference = new AzureBlobReference()
                    {
                        RelativePath = path + "/" + filename,
                        Container = container
                    }
                },
                PathType = FileBasedPathType.File,
            };
            Assert.IsNull(info.FolderPath);
            Assert.IsNull(info.FileName);
            MethodInfo method_UpdateFolderPathAndFileName = typeof(PipelineResourceBuilder).GetMethod("UpdateFolderPathAndFileName", BindingFlags.Static | BindingFlags.NonPublic, Type.DefaultBinder, new Type[] { typeof(PipelineResourceInfo) }, null);
            Assert.DoesNotThrow(() => method_UpdateFolderPathAndFileName.Invoke(_fakePipelineResourceBuilder, new object[] { info }));
            Assert.AreEqual(container + path, info.FolderPath);
            Assert.AreEqual(filename, info.FileName);
        }

        [Test]
        public void TestUpdateFolderPathAndFileName_AzureDataLakeGen2_Return()
        {
            string path = "/folder1/folder2";
            string filename = "file.txt";
            string container = "/testContainer";
            PipelineResourceInfo info = new PipelineResourceInfo()
            {
                DataReference = new DataReference()
                {
                    Type = DataReferenceType.AzureDataLakeGen2,
                    AzureDataLakeGen2Reference = new AzureDataLakeGen2Reference()
                    {
                        RelativePath = path + "/" + filename,
                        FileSystemName = container
                    }
                },
                PathType = FileBasedPathType.File,
            };
            Assert.IsNull(info.FolderPath);
            Assert.IsNull(info.FileName);
            MethodInfo method_UpdateFolderPathAndFileName = typeof(PipelineResourceBuilder).GetMethod("UpdateFolderPathAndFileName", BindingFlags.Static | BindingFlags.NonPublic, Type.DefaultBinder, new Type[] { typeof(PipelineResourceInfo) }, null);
            Assert.DoesNotThrow(() => method_UpdateFolderPathAndFileName.Invoke(_fakePipelineResourceBuilder, new object[] { info }));
            Assert.AreEqual(container + path, info.FolderPath);
            Assert.AreEqual(filename, info.FileName);
        }

        [TestCase(DataReferenceType.AzureDataLakeGen2, DataReferenceType.AzureBlob, false)]
        [TestCase(DataReferenceType.Cosmos, DataReferenceType.AzureBlob, true)]
        public void TestUpdateCopyAsText_binaryCopySupported_Return(DataReferenceType sourceType, DataReferenceType sinkType, bool copyAsText)
        {
            PipelineResourceBuilderMetadata metadta = new PipelineResourceBuilderMetadata()
            {
                Sources = new List<PipelineResourceInfo>() { 
                    new PipelineResourceInfo()
                    {
                        DataReference = new DataReference()
                        {
                            Type = sourceType,
                        }
                    }
                },
                Sink = new PipelineResourceInfo()
                {
                    DataReference = new DataReference()
                    {
                        Type = sinkType,
                    },
                }
            };
            MethodInfo method_UpdateCopyAsText = typeof(PipelineResourceBuilder).GetMethod("UpdateCopyAsText", BindingFlags.Static | BindingFlags.NonPublic);
            Assert.DoesNotThrow(() => method_UpdateCopyAsText.Invoke(_fakePipelineResourceBuilder, new object[] { metadta }));
            EnumerableExtension.ForEach(metadta.Sources, source => Assert.AreEqual(copyAsText, source.CopyAsText));
            Assert.AreEqual(copyAsText, metadta.Sink.CopyAsText);
        }

        [TestCase(CredentialTypes.AccountKey)]
        [TestCase(CredentialTypes.None)]
        [TestCase(CredentialTypes.Sas)]
        public void TestGetPathTypeFromStorageAsync_CredentialTypeNotValid_InvalidOperationException(CredentialTypes type)
        {
            AzureDataLakeGen2Reference dataLakeGen2Reference = new AzureDataLakeGen2Reference();
            DataStoreDto dto = new DataStoreDto()
            {
                AzureStorageSection = new AzureStorageDto()
                {
                    CredentialType = type,
                    ClientCredentials = new ClientCredentialsDto()
                    {
                        ClientId = Guid.NewGuid(),
                        ClientSecret = "PLACEHOLDER",
                        TenantId = Guid.NewGuid(),
                    }
                }
            };

            MethodInfo method_GetPathTypeFromStorageAsync = typeof(PipelineResourceBuilder).GetMethod("GetPathTypeFromStorageAsync", BindingFlags.Static | BindingFlags.NonPublic, Type.DefaultBinder, new Type[] { typeof(AzureDataLakeGen2Reference), typeof(DataStoreDto) }, null);
            var task = (Task<FileBasedPathType>)method_GetPathTypeFromStorageAsync.Invoke(_fakePipelineResourceBuilder, new object[] { dataLakeGen2Reference, dto });
            var actualExp = Assert.ThrowsAsync<InvalidOperationException>(async () => await task.ConfigureAwait(false));
            Assert.AreEqual("Only AAD auth is supported for Data Lake Gen2 accounts.", actualExp.Message);
        }


        [TestCase(DataReferenceType.Cosmos)]
        [TestCase(DataReferenceType.AzureFiles)]
        [TestCase(DataReferenceType.AzureMySqlDatabase)]
        [TestCase(DataReferenceType.AzurePostgresDatabase)]
        [TestCase(DataReferenceType.AzureSqlDatabase)]
        [TestCase(DataReferenceType.DBFS)]
        [TestCase(DataReferenceType.PhillyHdfs)]
        [TestCase(DataReferenceType.None)]
        public void TestGetPathTypeFromStorageAsync_(DataReferenceType type)
        {
            PipelineResourceInfo info = new PipelineResourceInfo()
            {
                DataReference = new DataReference()
                {
                    Type = type,
                }
            };

            MethodInfo method_GetPathTypeFromStorageAsync = typeof(PipelineResourceBuilder).GetMethod("GetPathTypeFromStorageAsync", BindingFlags.Static | BindingFlags.NonPublic, Type.DefaultBinder, new Type[] { typeof(PipelineResourceInfo) }, null);
            var task = (Task<FileBasedPathType>)method_GetPathTypeFromStorageAsync.Invoke(_fakePipelineResourceBuilder, new object[] { info });
            var actualExp = Assert.ThrowsAsync<InvalidOperationException>(async () => await task.ConfigureAwait(false));
            Assert.AreEqual($"Unsupported data reference type: {info.Type}", actualExp.Message);
        }

        [Test]
        public async Task TestNormalizePathTypeAsync_PathTypeUnknow_Return()
        {
            PipelineResourceInfo info = new PipelineResourceInfo()
            {
                DataReference = new DataReference()
                {
                    Type = DataReferenceType.AzureBlob,
                    AzureBlobReference = new AzureBlobReference()
                    {
                        PathType = FileBasedPathType.Folder
                    }
                },
                PathType = FileBasedPathType.Unknown,
            };

            PipelineResourceBuilderMetadata metadata = new PipelineResourceBuilderMetadata();
            Assert.AreEqual(FileBasedPathType.Unknown, info.PathType);
            await _fakePipelineResourceBuilder.NormalizePathTypeAsync(metadata, info).ConfigureAwait(false);
            Assert.AreEqual(info.FileBasedReference.PathType, info.PathType);
        }

        [Test]
        public void TestNormalizePathTypeAsync_SourcePathTypeUnknow_InvalidOperationException()
        {
            Mock.Get(_fakePipelineResourceBuilder)
                .Setup(
                   service => service.NormalizePathTypeAsync(
                       It.IsAny<PipelineResourceBuilderMetadata>(),
                       It.IsAny<PipelineResourceInfo>()))
                .Returns(Task.CompletedTask);

            PipelineResourceBuilderMetadata metadata = new PipelineResourceBuilderMetadata()
            {
                Sources = new List<PipelineResourceInfo>() 
                {
                    new PipelineResourceInfo()
                    {
                        DataReference = new DataReference()
                        {
                            Type = DataReferenceType.AzureBlob,
                            AzureBlobReference = new AzureBlobReference()
                            {
                                PathType = FileBasedPathType.Unknown
                            }
                        },
                        PathType = FileBasedPathType.Unknown
                    },
                    new PipelineResourceInfo()
                    {
                        DataReference = new DataReference()
                        {
                            Type = DataReferenceType.AzureBlob,
                            AzureBlobReference = new AzureBlobReference()
                            {
                                PathType = FileBasedPathType.File
                            }
                        },
                        PathType = FileBasedPathType.File
                    },
                    new PipelineResourceInfo()
                    {
                        DataReference = new DataReference()
                        {
                            Type = DataReferenceType.AzureBlob,
                            AzureBlobReference = new AzureBlobReference()
                            {
                                PathType = FileBasedPathType.Unknown
                            }
                        },
                        PathType = FileBasedPathType.Unknown
                    }
                },
                Sink = new PipelineResourceInfo()
                {
                    DataReference = new DataReference()
                    {
                        Type = DataReferenceType.AzureBlob,
                        AzureBlobReference = new AzureBlobReference()
                        {
                            PathType = FileBasedPathType.Unknown
                        }
                    },
                }
            };

            var actualExp = Assert.ThrowsAsync<InvalidOperationException>(async () => await _fakePipelineResourceBuilder.NormalizePathTypeAsync(metadata).ConfigureAwait(false));
            Assert.AreEqual("Failed to determine File-based source pathType for inputs indexed with : [0, 2]", actualExp.Message);

            metadata = new PipelineResourceBuilderMetadata()
            {
                Sources = new List<PipelineResourceInfo>()
                {
                    new PipelineResourceInfo()
                    {
                        DataReference = new DataReference()
                        {
                            Type = DataReferenceType.AzureBlob,
                            AzureBlobReference = new AzureBlobReference()
                            {
                                PathType = FileBasedPathType.File
                            }
                        },
                        PathType = FileBasedPathType.File
                    }
                },
                Sink = new PipelineResourceInfo()
                {
                    DataReference = new DataReference()
                    {
                        Type = DataReferenceType.AzureBlob,
                        AzureBlobReference = new AzureBlobReference()
                        {
                            PathType = FileBasedPathType.Unknown
                        }
                    },
                }
            };

            Assert.DoesNotThrowAsync(async () => await _fakePipelineResourceBuilder.NormalizePathTypeAsync(metadata).ConfigureAwait(false));
        }

        [TestCase(DataReferenceType.AzureDataLakeGen2, FileBasedPathType.Folder, FileBasedPathType.Folder)]
        [TestCase(DataReferenceType.AzureDataLakeGen2, FileBasedPathType.File, FileBasedPathType.File)]
        [TestCase(DataReferenceType.Cosmos, FileBasedPathType.File, FileBasedPathType.Folder)]
        public async Task TestNormalizePathTypeAsync_SinkPathTypeUnknown_ArgumentException(DataReferenceType sourceType, FileBasedPathType sourcePathType, FileBasedPathType expectedSinkType)
        {
            Mock.Get(_fakePipelineResourceBuilder)
                .Setup(
                   service => service.NormalizePathTypeAsync(
                       It.IsAny<PipelineResourceBuilderMetadata>(),
                       It.IsAny<PipelineResourceInfo>()))
                .Returns(Task.CompletedTask);

            PipelineResourceBuilderMetadata metadata = new PipelineResourceBuilderMetadata()
            {
                Sources = new List<PipelineResourceInfo>() 
                {
                    new PipelineResourceInfo()
                    {
                        DataReference = new DataReference()
                        {
                            Type = sourceType,
                            AzureBlobReference = new AzureBlobReference()
                            {
                                PathType = sourcePathType
                            }
                        },
                        PathType = sourcePathType
                    }
                },
                Sink = new PipelineResourceInfo()
                {
                    DataReference = new DataReference()
                    {
                        Type = DataReferenceType.AzureBlob,
                        AzureBlobReference = new AzureBlobReference()
                        {
                            PathType = FileBasedPathType.Unknown
                        }
                    },
                    PathType = FileBasedPathType.Unknown
                }
            };


            await _fakePipelineResourceBuilder.NormalizePathTypeAsync(metadata).ConfigureAwait(false);
            Assert.AreEqual(expectedSinkType, metadata.Sink.PathType);
        }

        [Test]
        public void TestNormalizePathTypeAsync_SourceFolderToSinkFile_ArgumentException()
        {
            Mock.Get(_fakePipelineResourceBuilder)
                .Setup(
                   service => service.NormalizePathTypeAsync(
                       It.IsAny<PipelineResourceBuilderMetadata>(),
                       It.IsAny<PipelineResourceInfo>()))
                .Returns(Task.CompletedTask);

            PipelineResourceBuilderMetadata metadata = new PipelineResourceBuilderMetadata()
            {
                Sources = new List<PipelineResourceInfo>() 
                {
                    new PipelineResourceInfo()
                    {
                        DataReference = new DataReference()
                        {
                            Type = DataReferenceType.AzureDataLakeGen2,
                            AzureBlobReference = new AzureBlobReference()
                            {
                                PathType = FileBasedPathType.Folder
                            }
                        },
                        PathType = FileBasedPathType.Folder
                    }
                },
                Sink = new PipelineResourceInfo()
                {
                    DataReference = new DataReference()
                    {
                        Type = DataReferenceType.AzureBlob,
                        AzureBlobReference = new AzureBlobReference()
                        {
                            PathType = FileBasedPathType.File
                        }
                    },
                    PathType = FileBasedPathType.File
                }
            };

            var actualExp = Assert.ThrowsAsync<ArgumentException>(async () => await _fakePipelineResourceBuilder.NormalizePathTypeAsync(metadata).ConfigureAwait(false));
            Assert.AreEqual("Cannot copy from a directory source to a file destination.", actualExp.Message);
        }

        [Test]
        public void TestNormalizePathTypeAsync_MultipleInputs_SinkTypeIsFile_Exception()
        {
            Mock.Get(_fakePipelineResourceBuilder)
                .Setup(
                   service => service.NormalizePathTypeAsync(
                       It.IsAny<PipelineResourceBuilderMetadata>(),
                       It.IsAny<PipelineResourceInfo>()))
                .Returns(Task.CompletedTask);

            PipelineResourceBuilderMetadata metadata = new PipelineResourceBuilderMetadata()
            {
                Sources = new List<PipelineResourceInfo>()
                {
                    new PipelineResourceInfo()
                    {
                        DataReference = new DataReference()
                        {
                            Type = DataReferenceType.AzureBlob,
                            AzureBlobReference = new AzureBlobReference()
                            {
                                PathType = FileBasedPathType.File
                            }
                        },
                        PathType = FileBasedPathType.File
                    },
                    new PipelineResourceInfo()
                    {
                        DataReference = new DataReference()
                        {
                            Type = DataReferenceType.AzureDataLakeGen2,
                            AzureBlobReference = new AzureBlobReference()
                            {
                                PathType = FileBasedPathType.File
                            }
                        },
                        PathType = FileBasedPathType.File
                    }
                },
                Sink = new PipelineResourceInfo()
                {
                    DataReference = new DataReference()
                    {
                        Type = DataReferenceType.AzureBlob,
                        AzureBlobReference = new AzureBlobReference()
                        {
                            PathType = FileBasedPathType.File
                        }
                    },
                    PathType = FileBasedPathType.File
                }
            };

            var actualExp = Assert.ThrowsAsync<ArgumentException>(async () => await _fakePipelineResourceBuilder.NormalizePathTypeAsync(metadata).ConfigureAwait(false));
            Assert.AreEqual("Cannot copy from multiple file sources to a file destination.", actualExp.Message);
        }

        [Test]
        public void TestUpdateDataStoreDto_DataStoreNameNull_ArgumentException()
        {
            PipelineResourceInfo info = new PipelineResourceInfo()
            {
                DataReference = new DataReference()
                {
                    Type = DataReferenceType.AzureMySqlDatabase,
                    AzureMySqlDatabaseReference = new AzureDatabaseReference()
                    {
                        //AmlDataStoreName = "testAmlDataStoreName"
                    }
                },
                PathType = FileBasedPathType.Unknown
            };

            Assert.IsNull(info.InnerDataReference.AmlDataStoreName);
            MethodInfo method_UpdateDataStoreDto = typeof(PipelineResourceBuilder).GetMethod("UpdateDataStoreDto", BindingFlags.Instance | BindingFlags.NonPublic, Type.DefaultBinder, new Type[] { typeof(PipelineResourceInfo) }, null);
            var task = (Task)method_UpdateDataStoreDto.Invoke(_fakePipelineResourceBuilder, new object[] { info });
            var actualExp = Assert.ThrowsAsync<ArgumentException>(async () => await task.ConfigureAwait(false));
            Assert.AreEqual("Data store name cannot be null or empty", actualExp.Message);
        }

        [Test]
        public async Task TestUpdateDataStoreDto_Normal_Return()
        {
            PipelineResourceInfo info = new PipelineResourceInfo()
            {
                DataReference = new DataReference()
                {
                    Type = DataReferenceType.AzureMySqlDatabase,

                    AzureMySqlDatabaseReference = new AzureDatabaseReference()
                    {
                        AmlDataStoreName = "testAmlDataStoreName"
                    }
                },
                PathType = FileBasedPathType.Unknown
            };

            Assert.IsNotNull(info.InnerDataReference.AmlDataStoreName);
            MethodInfo method_UpdateDataStoreDto = typeof(PipelineResourceBuilder).GetMethod("UpdateDataStoreDto", BindingFlags.Instance | BindingFlags.NonPublic, Type.DefaultBinder, new Type[] { typeof(PipelineResourceInfo) }, null);
            var task = (Task)method_UpdateDataStoreDto.Invoke(_fakePipelineResourceBuilder, new object[] { info });
            await task.ConfigureAwait(false);
            Assert.AreEqual(_dummyDataStoreDto, info.DataStoreDto);
        }

        [Test]
        public void TestValidate_DataReferenceNull_ArgumentNullException()
        {
            PipelineResourceInfo info = new PipelineResourceInfo();

            MethodInfo method_Validate = typeof(PipelineResourceBuilder).GetMethod("Validate", BindingFlags.Static | BindingFlags.NonPublic, Type.DefaultBinder, new Type[] { typeof(PipelineResourceInfo) }, null);
            var actualExp = Assert.Throws<TargetInvocationException>(() => method_Validate.Invoke(null, new object[] { info }));
            Assert.IsInstanceOf<ArgumentNullException>(actualExp.InnerException);
        }
    }
}
