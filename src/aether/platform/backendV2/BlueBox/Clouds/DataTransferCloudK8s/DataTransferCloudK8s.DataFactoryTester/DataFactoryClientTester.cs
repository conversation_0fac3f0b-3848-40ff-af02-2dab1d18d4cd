﻿using System;
using System.Collections.Generic;
using System.Net;
using System.Threading.Tasks;
using Microsoft.Aether.BackendCommon;
using Microsoft.Aether.BackendCommon.KeyVault;
using Microsoft.Aether.DataTransferCloud.DataFactory;
using Microsoft.Aether.DataTransferCloud.DataFactory.Authentications;
using Microsoft.Aether.DataTransferCloud.DataFactory.Resources;
using Microsoft.Azure.Management.DataFactory.Models;
using Microsoft.IdentityModel.Clients.ActiveDirectory;
using Microsoft.RelInfra.Common;

namespace DataTransferCloudK8s.DataFactoryTester
{
    class DataFactoryClientTester
    {
        private const string KeyVaultResource = "https://vault.azure.net";
        private const string TenantId = "72f988bf-86f1-41af-91ab-2d7cd011db47";
        private const string SubscriptionId = "b8c23406-f9b5-4ccb-8a65-a8cb5dcd6a5a";
        private const string ResourceGroupName = "aesviennatesteuap";

        private const string DataFactorySubscriptionId = SubscriptionId;
        private const string DataFactoryResourceGroupName = ResourceGroupName;
        private const string DataFactoryName = "adftestb02244263f67982d";

        private const string SqlServerUri = "testsqldb3p.database.windows.net";
        private const string SqlServerName = "testsqldb3p";
        private const string SqlDatabaseName = "testsqlsb";
        private const string SqlClientId = "522f51ab-594e-455d-b044-2a9ffa3840a0";
        private const string SqlClientTenantId = TenantId;

        private const string BlobAccountName = "adftestblobweu";

        private const string PostgresDatabaseServerUri = "aespostgrestestserver.postgres.database.azure.com";
        private const string PostgresDatabaseName = "mytestdb";
        private const string PostgresDatabaseUserId = "myadmin@aespostgrestestserver";

        private const string MySqlServerUri = "aesmysqltestserver.mysql.database.azure.com";
        private const string MySqlDatabaseName = "mytestdb";
        private const string MySqlUserId = "dbadmin@aesmysqltestserver";

        private const string AdlsResourceGroupName = "adftestadlsrg";
        private const string AdlsAccountName = "adftestadls";
        private const string AdlsClientId = "5729aff6-ac52-4b67-b6e5-385a14f15a5f";

        private static string DataFactoryAccessToken;
        private static string BlobAccountKey;
        private static string AdlsClientSecret;
        private static string SqlClientSecret;
        private static string PostgresDatabaseUserPassword;
        private static string MySqlPassword;

        private static async Task SetupSecrets()
        {
            string appId = Environment.GetEnvironmentVariable("SECRET_S2S_APPID");
            string appKey = Environment.GetEnvironmentVariable("SECRET_S2S_APPKEY");

            IKeyVaultClient keyVault = KeyVaultClient.CreateWithKeyVaultName(
                vaultName: "A3PVault",
                messageHandler: AzureAuthentication.CreateKeyVaultMessageHandler(TenantId, SubscriptionId, KeyVaultResource,
                credentials: new NetworkCredential(appId, appKey)));
            BlobAccountKey = await keyVault.GetSecretValueAsync("adftestblobweu-accountkey");
            SqlClientSecret = await keyVault.GetSecretValueAsync("sqltestsqldb3p-clientsecret");
            PostgresDatabaseUserPassword = await keyVault.GetSecretValueAsync("aespostgrestestserver-userpw");
            MySqlPassword = await keyVault.GetSecretValueAsync("aesmysqltestserver-userpw");
            AdlsClientSecret = await keyVault.GetSecretValueAsync("adftestadls-clientsecret");

            var userAssertion = new ClientCredential(appId, appKey);
#pragma warning disable CS0618 // Type or member is obsolete
            var authContext = new AuthenticationContext($"{AdalConstants.AdalAuthEndpoint}/{TenantId}");
#pragma warning restore CS0618 // Type or member is obsolete
            var authenticationResult = await authContext.AcquireTokenAsync(AdalConstants.AzureManagementUrl, userAssertion);
            DataFactoryAccessToken = authenticationResult.AccessToken;
        }

        public static async Task TestDataFactoryClientAsync()
        {
            await SetupSecrets();

            var dataFactory = new DataFactoryClient(
                loggingId: "test",
                dataFactorySubscriptionId: DataFactorySubscriptionId,
                dataFactoryResourceGroup: DataFactoryResourceGroupName,
                dataFactoryName: DataFactoryName,
                dataFactoryAuthToken: DataFactoryAccessToken);

            await dataFactory.ValidateDataFactoryReadyAsync();

            List<CopyMetadata> runs = CreateTestRuns();

            foreach (var run in runs)
            {
                Console.WriteLine($"Starting copy for run: {run.Description}");

                var copyOperation = await dataFactory.StartCopyOperationAsync(new List<IPipelineResource>() { run.Source }, run.Sink);

                while (true)
                {
                    DataFactoryOperationStatusEntity statusEntity = await dataFactory.CheckCopyOperationStatusAsync(copyOperation);
                    DataFactoryOperationStatus status = statusEntity.Status;
                    Console.WriteLine($"Current run status: {status}");

                    if (status == DataFactoryOperationStatus.InProgress)
                    {
                        await Task.Delay(TimeSpan.FromSeconds(10));
                    }
                    else
                    {
                        Console.WriteLine($"StdOut: {statusEntity.Output}");
                        Console.WriteLine($"StdErr: {statusEntity.Error}");
                        break;
                    }
                }

                Console.WriteLine("Press any key to continue...");
                Console.ReadKey();
                Console.WriteLine();
            }
        }

        private static List<CopyMetadata> CreateTestRuns()
        {
            var copySqlTableToBlob = new CopyMetadata
            {
                Description = "Sqldb table to blob",
                Source = GetSqlResource(tableName: "TestData"),
                Sink = GetBlobResource(folderPath: "testcont/testdata", fileName: "tablename.csv"),
            };

            var copySqlQueryToBlob = new CopyMetadata
            {
                Description = "Sqldb query to blob",
                Source = GetSqlResource(sqlQuery: "select top 1 * from TestData"),
                Sink = GetBlobResource(folderPath: "testcont/testdata", fileName: "query.csv"),
            };

            var copySqlSpToBlob = new CopyMetadata
            {
                Description = "Sqldb stored proc to blob",
                Source = GetSqlResource(storedProcName: "TestStoredProcedure"),
                Sink = GetBlobResource(folderPath: "testcont/testdata", fileName: "StoredProc.csv"),
            };

            var copySqlSpWithParamsToBlob = new CopyMetadata
            {
                Description = "Sqldb stored proc with params to blob",
                Source = new AzureSqldbPipelineResource(
                        serverName: SqlServerName,
                        serverUri: SqlServerUri,
                        databaseName: SqlDatabaseName,
                        authentication:
                        new DataTransferAuthentication.DataTransferAuthenticationBuilder(AuthenticationTypes.ServicePrincipal)
                                .SetServicePrincipalId(SqlClientId)
                                .SetServicePrincipalKey(SqlClientSecret)
                                .SetServicePrincipalTenant(SqlClientTenantId)
                                .build(),
                        null,
                        null,
                        storedProcName: "TestStoredProcedureWithParameters",
                        storedProcParams: new Dictionary<string, StoredProcedureParameter>
                        {
                            {
                                "firstName",
                                new StoredProcedureParameter
                                {
                                    Type = "String",
                                    Value = "Akshay",
                                }
                            },
                        }),
                Sink = GetBlobResource(folderPath: "testcont/testdata", fileName: "StoredProcWithParams.csv"),
            };

            var copyBlobToSqlTable = new CopyMetadata
            {
                Description = "Copy blob to Sql table",
                Source = GetBlobResource(folderPath: "test/sql", fileName: "names.txt"),
                Sink = GetSqlResource(tableName: "TestData2"),
            };

            var copyPostgresQueryToBlob = new CopyMetadata
            {
                Description = "Copy Postgres database query to blob",
                Source = GetPostgresDatabaseResource(query: "SELECT * FROM testtable"),
                Sink = GetBlobResource(folderPath: "testcont/testdata", fileName: "postgreQuery.csv"),
            };

            var copyPostgresTableToBlob = new CopyMetadata
            {
                Description = "Copy Postgres database table to blob",
                Source = GetPostgresDatabaseResource(tableName: "testtable"),
                Sink = GetBlobResource(folderPath: "testcont/testdata", fileName: "postgreTable.csv"),
            };

            var copyMySqlQueryToBlob = new CopyMetadata
            {
                Description = "Copy MySql database query to blob",
                Source = GetMySqlResource(query: "SELECT * FROM testtable"),
                Sink = GetBlobResource(folderPath: "testcont/testdata", fileName: "mySqlQuery.csv"),
            };

            var copyMySqlTableToBlob = new CopyMetadata
            {
                Description = "Copy MySql database table to blob",
                Source = GetMySqlResource("inventory"),
                Sink = GetBlobResource(folderPath: "testcont/testdata", fileName: "mySqlTable.csv"),
            };

            var copyBlobToAdls = new CopyMetadata
            {
                Description = "Copy Blob to ADLS",
                Source = GetBlobResource(folderPath: "test/sql", fileName: "names.txt"),
                Sink = GetAdlsResource(folderPath: "testdata1", fileName: "testdata.txt"),
            };

            var copyAdlsToBlob = new CopyMetadata
            {
                Description = "Copy ADLS to Blob",
                Source = GetAdlsResource(folderPath: "testdata1", fileName: "testdata.txt"),
                Sink = GetBlobResource(folderPath: "test/sql", fileName: "testdata.txt"),
            };

            var copyBlobToAdlsRootFile = new CopyMetadata
            {
                Description = "Copy a file from Blob to specified location in ADLS root folder",
                Source = GetBlobResource(folderPath: "test/sql", fileName: "names.txt"),
                Sink = GetAdlsResource(folderPath: null, fileName: "testdata.txt"),
            };

            var copyBlobToAdlsRootFolder = new CopyMetadata
            {
                Description = "Copy a file from Blob to ADLS root folder",
                Source = GetBlobResource(folderPath: "test/sql", fileName: "names.txt"),
                Sink = GetAdlsResource(folderPath: null, fileName: null),
            };

            var copyAdlsRootFileToBlob = new CopyMetadata
            {
                Description = "Copy a file from ADLS root folder to Blob",
                Source = GetAdlsResource(folderPath: null, fileName: "testdata.txt"),
                Sink = GetBlobResource(folderPath: "test", fileName: "names.txt"),
            };

            return new List<CopyMetadata>
            {
                copySqlTableToBlob,
                copySqlQueryToBlob,
                copySqlSpToBlob,
                copySqlSpWithParamsToBlob,
                copyBlobToSqlTable,
                copyPostgresQueryToBlob,
                copyPostgresTableToBlob,
                copyMySqlTableToBlob,
                copyBlobToAdls,
                copyAdlsToBlob,
                copyBlobToAdlsRootFile,
                copyBlobToAdlsRootFolder,
                copyAdlsRootFileToBlob,
            };
        }

        class CopyMetadata
        {
            public string Description { get; set; }
            public IPipelineResource Source { get; set; }
            public IPipelineResource Sink { get; set; }
        }

        private static AzureBlobPipelineResource GetBlobResource(string folderPath, string fileName = null)
        {
            DataTransferAuthentication auth = new DataTransferAuthentication.DataTransferAuthenticationBuilder(AuthenticationTypes.Sas)
                                                .SetAccountKey(BlobAccountKey)
                                                .build();
            return new AzureBlobPipelineResource(
                accountName: BlobAccountName,
                authentication: auth,
                folderPath: folderPath,
                fileName: fileName,
                copyAsText: true);
        }

        private static string ParseContainerName(string folderPath)
        { 
            // assume the the upper layer of folder in folderPath is the containerName
            if(string.IsNullOrEmpty(folderPath))
            {
                return folderPath;
            }

            int pos = folderPath.IndexOf("/");
            pos = pos < 0 ? folderPath.Length : pos;
            return folderPath.Substring(0, pos);
        }

        private static AzureDataLakeStorePipelineResource GetAdlsResource(string folderPath, string fileName = null)
        {
            DataTransferAuthentication auth = new DataTransferAuthentication.DataTransferAuthenticationBuilder(AuthenticationTypes.ServicePrincipal)
                                                .SetServicePrincipalId(AdlsClientId)
                                                .SetServicePrincipalKey(AdlsClientSecret)
                                                .SetServicePrincipalTenant(TenantId)
                                                .build();
            return new AzureDataLakeStorePipelineResource(
                subscriptionId: SubscriptionId,
                resourceGroupName: AdlsResourceGroupName,
                accountName: AdlsAccountName,
                authentication: auth,
                folderPath: folderPath,
                fileName: fileName,
                copyAsText: true);
        }

        private static AzureSqldbPipelineResource GetSqlResource(string tableName = null, string sqlQuery = null, string storedProcName = null)
        {
            DataTransferAuthentication auth = new DataTransferAuthentication.DataTransferAuthenticationBuilder(AuthenticationTypes.ServicePrincipal)
                                                .SetServicePrincipalId(SqlClientId)
                                                .SetServicePrincipalKey(SqlClientSecret)
                                                .SetServicePrincipalTenant(SqlClientTenantId)
                                                .build();
            return new AzureSqldbPipelineResource(
                serverName: SqlServerName,
                serverUri: SqlServerUri,
                databaseName: SqlDatabaseName,
                authentication: auth,
                tableName: tableName,
                sqlQuery: sqlQuery,
                storedProcName: storedProcName);
        }

        private static AzurePostgresDatabasePipelineResource GetPostgresDatabaseResource(string tableName = null, string query = null)
        {
            DataTransferAuthentication auth = new DataTransferAuthentication.DataTransferAuthenticationBuilder(AuthenticationTypes.SqlAuthentication)
                                                .SetUserId(PostgresDatabaseUserId)
                                                .SetUserPassword(PostgresDatabaseUserPassword)
                                                .build();
            return new AzurePostgresDatabasePipelineResource(
                serverUri: PostgresDatabaseServerUri,
                databaseName: PostgresDatabaseName,
                authentication: auth,
                tableName: tableName,
                query: query);
        }

        private static AzureMySqlDatabasePipelineResource GetMySqlResource(string tableName = null, string query = null)
        {
            DataTransferAuthentication auth = new DataTransferAuthentication.DataTransferAuthenticationBuilder(AuthenticationTypes.SqlAuthentication)
                                                .SetUserId(MySqlUserId)
                                                .SetUserPassword(MySqlPassword)
                                                .build();
            return new AzureMySqlDatabasePipelineResource(
                serverUri: MySqlServerUri,
                databaseName: MySqlDatabaseName,
                authentication: auth,
                tableName: tableName,
                query: query);
        }
    }
}
