<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
  </PropertyGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\..\..\..\RelInfra\RelInfra.Common.Library\src\Common\RelInfra.Common.csproj" />
    <ProjectReference Include="..\..\..\..\shared\Microsoft.Aether.BackendCommon\Microsoft.Aether.BackendCommon.csproj" />
    <ProjectReference Include="..\..\..\..\shared\Microsoft.Aether.S2S.Common\Microsoft.Aether.S2S.Common.csproj" />
    <ProjectReference Include="..\..\..\S2S.Common\Microsoft.Aether.BlueBox.S2S.Common\Microsoft.Aether.BlueBox.S2S.Common.csproj" />
    <ProjectReference Include="..\DataTransferCloudK8s.CopyService\DataTransferCloudK8s.CopyService.csproj" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="System.Net.Security" />
  </ItemGroup>
  <ItemGroup>
    <None Include="..\..\..\..\shared\SharedConfigurationSettings\SharedSettings.ini">
      <Link>SharedSettings.ini</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="..\..\..\..\shared\SharedConfigurationSettings\BBSharedSettings.ini" Link="BBSharedSettings.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>
</Project>
