﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.Aether.BackendCommon.Configuration;
using Microsoft.Aether.BlueBox.DataStoreClient;
using Microsoft.Aether.BlueBox.S2S.Common;
using Microsoft.Aether.DataContracts.Entity;
using Microsoft.Aether.DataContracts.DataReferences;
using Microsoft.Aether.DataTransfer.Cloud.Common.DataContracts;
using Microsoft.Aether.DataTransferCloud.CopyService;
using Microsoft.Aether.DataTransferCloud.DataFactory;
using Microsoft.Aether.S2S.Common;
using Microsoft.RelInfra.Instrumentation;
using Microsoft.RelInfra.Instrumentation.Test.Mocks;
using Microsoft.RelInfra.Instrumentation.Logging;
using System.Diagnostics;
using System.Linq;
using Microsoft.Aether.BackendCommon.ClusterHealthPoller;

namespace DataTransferCloudK8s.DataFactoryTester
{
    class CopyServiceTester
    {
        private const string TenantId = "72f988bf-86f1-41af-91ab-2d7cd011db47";
        private const string SubscriptionId = "b8c23406-f9b5-4ccb-8a65-a8cb5dcd6a5a";

        private static readonly WorkspaceIdentity _workspaceIdentity = new WorkspaceIdentity
        {
            SubscriptionId = SubscriptionId,
            ResourceGroupName = "rgtestakshay",
            WorkspaceName = "wsmaster",
        };

        private static readonly AzureDataFactoryConfig _dataFactoryConfig = new AzureDataFactoryConfig
        {
            SubscriptionId = SubscriptionId,
            ResourceGroupName = "aesviennatesteuap",
            FactoryName = "adftestb02244263f67982d",
        };

        private static int passedRuns = 0;
        private static int failedRuns = 0;

        public static async Task TestCopyServiceAsync()
        {
            var configDimensions = new IniFileDimensions(isK8s: true, k8SEnvType: K8SEnvType.Dev);

            var serviceConfig = new NETCoreConfiguration(
                configFilename: "SharedSettings.ini",
                dimensions: configDimensions,
                replacements: NETCoreConfiguration.CreateDefaultReplacements(
                    k8SRegionValue: null,
                    keyVaultUrlValue: null),
                iniFileNames: new[] { "BBSharedSettings.ini" });

            var amlS2SServicePrincipalConfig = new AmlS2SServicePrincipalConfig(serviceConfig);
            string appId = Environment.GetEnvironmentVariable(amlS2SServicePrincipalConfig.SecretS2SAppIdEnvironmentVariable);
            string appKey = Environment.GetEnvironmentVariable(amlS2SServicePrincipalConfig.SecretS2SAppKeyEnvironmentVariable);
            IS2STokenProvider servicePrincipalAadTokenProvider =
                new S2STokenProvider(config: amlS2SServicePrincipalConfig, appId: appId, appKey: appKey, counters: new CounterManager("Tester", new LoggingCounterFactory()));

            CounterManager counters = new CounterManager("test", new LoggingCounterFactory());
            var amlDataStoreConfig = new AmlDataStoreConfig(serviceConfig);
            var dataStoreClient = new DataStoreClient(amlDataStoreConfig, servicePrincipalAadTokenProvider, counters);

            var counterManager = new CounterManager("CopyServiceTester", new MockCounterFactory());

            var clusterHealthPoller = new ClusterHealthPoller();
            var amlWorkspaceMsiServiceConfig = new AmlWorkspaceMsiServiceConfig(serviceConfig);
            var msiTokenProvider = new AmlWorkspaceMsiTokenProvider(amlWorkspaceMsiServiceConfig, servicePrincipalAadTokenProvider, clusterHealthPoller);

            string dataFactoryAccessToken = await msiTokenProvider.GetWorkspaceMsiTokenAsync(_workspaceIdentity, null);

            var pipelineResourceBuilder = new PipelineResourceBuilder(dataStoreClient, _workspaceIdentity, null);

            var dataFactoryClient = new DataFactoryClient(
                loggingId: "test",
                dataFactorySubscriptionId: _dataFactoryConfig.SubscriptionId,
                dataFactoryResourceGroup: _dataFactoryConfig.ResourceGroupName,
                dataFactoryName: _dataFactoryConfig.FactoryName,
                dataFactoryAuthToken: dataFactoryAccessToken);

            await dataFactoryClient.ValidateDataFactoryReadyAsync();

            var copyService = new DataFactoryCopyService("1", dataFactoryClient, pipelineResourceBuilder, counterManager, new NoopLogger());

            List<CopyMetadata> runs = CreateTestRuns();

            foreach (var run in runs)
            {
                var stopWatch = Stopwatch.StartNew();

                await Copy(copyService, run);

                Console.WriteLine($"Run completed in {stopWatch.ElapsedMilliseconds}ms");
            }

            Console.WriteLine($"Passed:{passedRuns}, Failed:{failedRuns}");
            Console.ReadLine();
        }

        private static async Task Copy(IAsyncCopyService copyService, CopyMetadata run)
        {
            try
            {
                Console.WriteLine($"Starting run: {run.Description}");

                IList<DataReference> sourceDataReferences = new List<DataReference>() { run.InputDataReference };
                PipelineResourceBuilderMetadata metadata = new PipelineResourceBuilderMetadata
                {
                    Sources = sourceDataReferences
                                    .Select(sourceDataReference => new PipelineResourceInfo
                                    {
                                        DataReference = sourceDataReference,
                                        PathType = run.Options?.SourcePathType ?? FileBasedPathType.Unknown,
                                    }).ToList(),
                    Sink = new PipelineResourceInfo
                    {
                        DataReference = run.OutputDataReference,
                        PathType = run.Options?.DestinationPathType ?? FileBasedPathType.Unknown,
                    },
                };
                AsyncCopyOperationEntity copyOperation = await copyService.StartCopyOperationAsync(metadata);

                Console.WriteLine($"DataFactoryPipelineName: {copyOperation.DataFactoryPipelineName}");
                Console.WriteLine($"DataFactoryPipelineRunId: {copyOperation.DataFactoryPipelineRunId}");
                Console.WriteLine($"DataFactoryPipelineRunId: {string.Join(", ", copyOperation.DataFactorySourceDatasetNames)}");
                Console.WriteLine($"DataFactoryPipelineRunId: {copyOperation.DataFactorySinkDatasetName}");

                while (true)
                {
                    AsyncCopyOperationStatusEntity statusEntity = await copyService.CheckCopyOperationStatusAsync(copyOperation);
                    AsyncCopyOperationStatus status = statusEntity.Status;
                    Console.WriteLine($"Current run status: {status}");

                    if (status == AsyncCopyOperationStatus.InProgress)
                    {
                        await Task.Delay(TimeSpan.FromSeconds(10));
                    }
                    else
                    {
                        Console.WriteLine("StdOut:");
                        Console.WriteLine(statusEntity.Output);

                        Console.WriteLine("StdErr:");
                        Console.WriteLine(statusEntity.Error);

                        if (status == AsyncCopyOperationStatus.Failed)
                        {
                            Debugger.Break();
                            failedRuns++;
                        }
                        else
                        {
                            passedRuns++;
                        }

                        break;
                    }
                }
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                Debugger.Break();
                failedRuns++;
            }
        }

        private static List<CopyMetadata> CreateTestRuns()
        {
            var testRuns = new List<CopyMetadata>();

            testRuns.AddRange(new List<CopyMetadata>()
            {
                new CopyMetadata
                {
                    Description = "NewBlobFileToAmbiguousBlobPath",
                    InputDataReference = CreateBlobReference("testdata1/testdata.txt"),
                    OutputDataReference = CreateBlobReference("testdata3/testdata.txt"), // this exists as both a file and folder
                    Options = new CopyOperationOptions
                    {
                        DestinationPathType = FileBasedPathType.File,
                    },
                },
                new CopyMetadata
                {
                    Description = "postgresQueryToBlob",
                    InputDataReference = CreatePostgresDatabaseReference(query: "SELECT * FROM testtable"),
                    OutputDataReference = CreateBlobReference("testdata4/postgreQuery"),
                },
                new CopyMetadata
                {
                    Description = "postgresTableToBlob",
                    InputDataReference = CreatePostgresDatabaseReference(tableName: "testtable"),
                    OutputDataReference = CreateBlobReference("testdata4/postgreTable"),
                },
                new CopyMetadata
                {
                    Description = "postgresTableToPostgresTable",
                    InputDataReference = CreatePostgresDatabaseReference(tableName: "testtable"),
                    OutputDataReference = CreatePostgresDatabaseReference(
                        tableName: "testtable2",
                        query: "DROP TABLE IF EXISTS testtable2;CREATE TABLE testtable2  AS TABLE testtable"),
                },
                new CopyMetadata
                {
                    Description = "mySqlQueryToBlob",
                    InputDataReference = CreateMySqlDatabaseReference(query: "SELECT * FROM testtable"),
                    OutputDataReference = CreateBlobReference("testdata4/mySqlQuery"),
                },
                new CopyMetadata
                {
                    Description = "mySqlTableToBlob",
                    InputDataReference = CreateMySqlDatabaseReference(tableName: "testtable"),
                    OutputDataReference = CreateBlobReference("testdata4/mySqlTable"),
                },
                new CopyMetadata
                {
                    Description = "mySqlTableToMySqlTable",
                    InputDataReference = CreateMySqlDatabaseReference(tableName: "testtable"),
                    OutputDataReference = CreateMySqlDatabaseReference(tableName: "testtable2"),
                },
                new CopyMetadata
                {
                    Description = "blobToMySqlTable",
                    InputDataReference = CreateBlobReference("testdata4/mySqlCsv"),
                    OutputDataReference = CreateMySqlDatabaseReference(tableName: "testtable"),
                }
            });

            var sourceFolderPath = "folder1";
            var sinkFolderPath = "folder2";
            var sourceFilePath = sourceFolderPath + "/file";
            var sinkFilePath = sinkFolderPath + "/file";
            var unknownFilePath = "unknown/files/" + Guid.NewGuid();
            var unknownFolderPath = "unknown/folders/" + Guid.NewGuid();

            var sourceSinkMapping = new SourceSinkMapping
            {
                { CreateBlobReference, CreateDataLakeReference },
                { CreateDataLakeReference, CreateAdlsgen2Reference },
                { CreateAdlsgen2Reference, CreateBlobReference },

                // exhaustive:
                //{ CreateBlobReference, CreateBlobReference },
                //{ CreateBlobReference, CreateDataLakeReference },
                //{ CreateBlobReference, CreateAdlsgen2Reference },

                //{ CreateDataLakeReference, CreateBlobReference },
                //{ CreateDataLakeReference, CreateDataLakeReference },
                //{ CreateDataLakeReference, CreateAdlsgen2Reference },

                //{ CreateAdlsgen2Reference, CreateBlobReference },
                //{ CreateAdlsgen2Reference, CreateDataLakeReference },
                //{ CreateAdlsgen2Reference, CreateAdlsgen2Reference },
            };

            foreach (var entry in sourceSinkMapping)
            {
                var sourceDataRefCreator = entry.Key;
                var sinkDataRefCreator = entry.Value;

                var sourceFolder = sourceDataRefCreator(sourceFolderPath);
                var sourceFile = sourceDataRefCreator(sourceFilePath);

                var sinkFolder = sinkDataRefCreator(sinkFolderPath);
                var sinkFile = sinkDataRefCreator(sinkFilePath);
                var sinkUnknownFile = sinkDataRefCreator(unknownFilePath);
                var sinkUnknownFolder = sinkDataRefCreator(unknownFolderPath);

                buildRun(sourceFolder, sinkFolder);
                buildRun(sourceFile, sinkFile);
                buildRun(sourceFile, sinkFolder);
                buildRun(sourceFile, sinkUnknownFile);
                buildRun(sourceFolder, sinkUnknownFolder);
            }

            void buildRun(DataReference source, DataReference sink)
            {
                var sourceSummary = GetDataRefSummary(source);
                var sinkSummary = GetDataRefSummary(sink);

                testRuns.Add(new CopyMetadata
                {
                    Description = $"Copy from {sourceSummary} to {sinkSummary}",
                    InputDataReference = source,
                    OutputDataReference = sink
                });
            }

            return testRuns;
        }

        class CopyMetadata
        {
            public string Description { get; set; }
            public DataReference InputDataReference { get; set; }
            public DataReference OutputDataReference { get; set; }
            public CopyOperationOptions Options { get; set; }
        }

        private static DataReference CreateBlobReference(string relativePath)
        {
            return new DataReference
            {
                Type = DataReferenceType.AzureBlob,
                AzureBlobReference = new AzureBlobReference
                {
                    AmlDataStoreName = "myblobdatastore",
                    Account = "adftestblobweu",
                    Container = "testcont",
                    RelativePath = relativePath,
                },
            };
        }

        private static DataReference CreateAdlsgen2Reference(string relativePath)
        {
            return new DataReference
            {
                Type = DataReferenceType.AzureDataLakeGen2,
                AzureDataLakeGen2Reference = new AzureDataLakeGen2Reference
                {
                    AmlDataStoreName = "myadlsgen2store",
                    Account = "teststor02euap",
                    FileSystemName = "test",
                    RelativePath = relativePath,
                },
            };
        }

        private static DataReference CreateDataLakeReference(string relativePath)
        {
            return new DataReference
            {
                Type = DataReferenceType.AzureDataLake,
                AzureDataLakeReference = new AzureDataLakeReference
                {
                    AmlDataStoreName = "myadlsdatastore",
                    Tenant = TenantId,
                    Subscription = SubscriptionId,
                    ResourceGroup = "adftestadlsrg",
                    Account = "adftestadls",
                    RelativePath = relativePath,
                }
            };
        }

        private static string GetDataRefSummary(DataReference dr)
        {
            switch (dr.Type)
            {
                case DataReferenceType.AzureBlob:
                    return $"Blob:" +
                        $"{dr.AzureBlobReference.Account}/" +
                        $"{dr.AzureBlobReference.Container}/" +
                        $"{dr.AzureBlobReference.RelativePath}";

                case DataReferenceType.AzureDataLake:
                    return $"ADLS:" +
                        $"{dr.AzureDataLakeReference.Account}/" +
                        $"{dr.AzureDataLakeReference.RelativePath}";

                case DataReferenceType.AzureDataLakeGen2:
                    return $"DataLakeGen2:" +
                        $"{dr.AzureDataLakeGen2Reference.Account}/" +
                        $"{dr.AzureDataLakeGen2Reference.FileSystemName}/" +
                        $"{dr.AzureDataLakeGen2Reference.RelativePath}";

                default:
                    throw new Exception("Unsupported data reference type");
            }
        }

        private static DataReference CreatePostgresDatabaseReference(string tableName = null, string query = null)
        {
            return new DataReference
            {
                Type = DataReferenceType.AzurePostgresDatabase,
                AzurePostgresDatabaseReference = new AzureDatabaseReference
                {
                    AmlDataStoreName = "mypostgresqldatastore",
                    ServerUri = "aespostgrestestserver.postgres.database.azure.com",
                    DatabaseName = "mytestdb",
                    TableName = tableName,
                    SqlQuery = query,
                }
            };
        }

        private static DataReference CreateMySqlDatabaseReference(string tableName = null, string query = null)
        {
            return new DataReference
            {
                Type = DataReferenceType.AzureMySqlDatabase,
                AzureMySqlDatabaseReference = new AzureDatabaseReference
                {
                    AmlDataStoreName = "mysqldatastore",
                    ServerUri = "mysqltestserver.mysql.database.azure.com",
                    DatabaseName = "mytestdb",
                    TableName = tableName,
                    SqlQuery = query,
                }
            };
        }

        public class SourceSinkMapping : List<KeyValuePair<Func<string, DataReference>, Func<string, DataReference>>>
        {
            public void Add(Func<string, DataReference> source, Func<string, DataReference> sink)
            {
                Add(new KeyValuePair<Func<string, DataReference>, Func<string, DataReference>>(source, sink));
            }
        }
    }
}
