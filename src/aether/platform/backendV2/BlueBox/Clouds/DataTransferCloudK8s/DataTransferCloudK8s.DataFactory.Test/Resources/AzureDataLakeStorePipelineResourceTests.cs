﻿using Microsoft.Aether.DataTransferCloud.DataFactory.Authentications;
using Microsoft.Aether.DataTransferCloud.DataFactory.Resources;
using Microsoft.Azure.Management.DataFactory.Models;
using Microsoft.RelInfra.Instrumentation.Exceptions;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Reflection;
using System.Text;

namespace DataTransferCloudK8s.DataFactory.Test.Resources
{
    public class AzureDataLakeStorePipelineResourceTests
    {
        private DataTransferAuthentication _auth_sp;
        private DataTransferAuthentication _auth_msi;

        private readonly string _AccountName = "accountName";
        private readonly string _ServicePrincipalId;
        private readonly string _ServicePrincipalKey;
        private readonly string _Tenant;

        private readonly string _SubscriptionId;
        private readonly string _ResourceGroupName;
        private readonly string _FileName;
        private readonly string _FolderPath;

        public AzureDataLakeStorePipelineResourceTests()
        {
            _ServicePrincipalId = Guid.NewGuid().ToString();
            _ServicePrincipalKey = "mySecret";
            _Tenant = Guid.NewGuid().ToString();
            _SubscriptionId = Guid.NewGuid().ToString();
            _ResourceGroupName = "testRG";

            _FileName = "testFileName";
            _FolderPath = "./hello/";

            _auth_sp = new DataTransferAuthentication
                .DataTransferAuthenticationBuilder(AuthenticationTypes.ServicePrincipal)
                .SetServicePrincipalId(_ServicePrincipalId)
                .SetServicePrincipalKey(_ServicePrincipalKey)
                .SetServicePrincipalTenant(_Tenant)
                        .build();

            _auth_msi = new DataTransferAuthentication
                .DataTransferAuthenticationBuilder(AuthenticationTypes.MSI)
                        .build();
        }

        [Test]
        public void TestGetLinkedService_ServicePrincipal_Return()
        {
            AzureDataLakeStorePipelineResource resource = new AzureDataLakeStorePipelineResource(
              subscriptionId:  _SubscriptionId,
              resourceGroupName:  _ResourceGroupName,
              accountName: _AccountName,
              authentication: _auth_sp,
              folderPath: "."
           );
            var actualRet = resource.GetLinkedService();
            Assert.IsInstanceOf<AzureDataLakeStoreLinkedService>(actualRet);
            var uriFormat = (string)typeof(AzureDataLakeStorePipelineResource).GetField("UriFormat", BindingFlags.NonPublic | BindingFlags.Instance | BindingFlags.Static).GetValue(resource);
            uriFormat = uriFormat
                            .Replace("{AccountName}", _AccountName);
            Assert.AreEqual(uriFormat, ((AzureDataLakeStoreLinkedService)actualRet).DataLakeStoreUri);
            Assert.AreEqual(_SubscriptionId, ((AzureDataLakeStoreLinkedService)actualRet).SubscriptionId);
            Assert.AreEqual(_ResourceGroupName, ((AzureDataLakeStoreLinkedService)actualRet).ResourceGroupName);
            Assert.AreEqual(_ServicePrincipalId, ((AzureDataLakeStoreLinkedService)actualRet).ServicePrincipalId);
            Assert.AreEqual(new SecureString(_ServicePrincipalKey).ToString(), ((AzureDataLakeStoreLinkedService)actualRet).ServicePrincipalKey.ToString());
            Assert.AreEqual(_Tenant, ((AzureDataLakeStoreLinkedService)actualRet).Tenant);
        }

        [Test]
        public void TestGetLinkedService_MSI_Return()
        {
            AzureDataLakeStorePipelineResource resource = new AzureDataLakeStorePipelineResource(
              subscriptionId: _SubscriptionId,
              resourceGroupName: _ResourceGroupName,
              accountName: _AccountName,
              authentication: _auth_msi,
              folderPath: "."
           );
            var actualRet = resource.GetLinkedService();
            Assert.IsInstanceOf<AzureDataLakeStoreLinkedService>(actualRet);
            var uriFormat = (string)typeof(AzureDataLakeStorePipelineResource).GetField("UriFormat", BindingFlags.NonPublic | BindingFlags.Instance | BindingFlags.Static).GetValue(resource);
            uriFormat = uriFormat
                            .Replace("{AccountName}", _AccountName);
            Assert.AreEqual(uriFormat, ((AzureDataLakeStoreLinkedService)actualRet).DataLakeStoreUri);
            Assert.AreEqual(_SubscriptionId, ((AzureDataLakeStoreLinkedService)actualRet).SubscriptionId);
            Assert.AreEqual(_ResourceGroupName, ((AzureDataLakeStoreLinkedService)actualRet).ResourceGroupName);
        }

        [Test]
        public void TestGetCopyFormat_CopyAsTextFalse_Return()
        {
            AzureDataLakeStorePipelineResource resource = new AzureDataLakeStorePipelineResource(
              subscriptionId: _SubscriptionId,
              resourceGroupName: _ResourceGroupName,
              accountName: _AccountName,
              authentication: _auth_msi,
              folderPath: ".",
              fileName: null,
              copyAsText: false
           );
            var actualRet = resource.GetDataset("dummyLinkedServiceName");
            Assert.IsInstanceOf<AzureDataLakeStoreDataset>(actualRet);
            Assert.AreEqual(null, ((AzureDataLakeStoreDataset)actualRet).Format);
        }

        [Test]
        public void TestGetCopyFormat_CopyAsTextTrue_Return()
        {
            AzureDataLakeStorePipelineResource resource = new AzureDataLakeStorePipelineResource(
              subscriptionId: _SubscriptionId,
              resourceGroupName: _ResourceGroupName,
              accountName: _AccountName,
              authentication: _auth_msi,
              folderPath: ".",
              fileName: null,
              copyAsText: true
           );
            var actualRet = resource.GetDataset("dummyLinkedServiceName");
            Assert.IsInstanceOf<AzureDataLakeStoreDataset>(actualRet);
            Assert.IsInstanceOf<TextFormat>(((AzureDataLakeStoreDataset)actualRet).Format);
        }

        [Test]
        public void TestCreateCopySource_Normal_Return()
        {
            AzureDataLakeStorePipelineResource resource = new AzureDataLakeStorePipelineResource(
              subscriptionId: _SubscriptionId,
              resourceGroupName: _ResourceGroupName,
              accountName: _AccountName,
              authentication: _auth_msi,
              folderPath: ".",
              fileName: null,
              copyAsText: true
           );
            var actualRet = resource.CreateCopySource();
            Assert.IsInstanceOf<AzureDataLakeStoreSource>(actualRet);
        }

        [Test]
        public void TestCreateCopySink_SubIdNotValid_ArgumentException()
        {
            AzureDataLakeStorePipelineResource resource = new AzureDataLakeStorePipelineResource(
              subscriptionId: "",
              resourceGroupName: _ResourceGroupName,
              accountName: _AccountName,
              authentication: _auth_msi,
              folderPath: ".",
              fileName: null,
              copyAsText: true
           );
            var actualExp = Assert.Throws<ArgumentException>(() => resource.CreateCopySink());
            Assert.AreEqual("ADLS datastore must specify subscription id and resource group name when used as data transfer destination.", actualExp.Message);
        }

        [Test]
        public void TestCreateCopySink_ResourceGroupNotValid_ArgumentException()
        {
            AzureDataLakeStorePipelineResource resource = new AzureDataLakeStorePipelineResource(
              subscriptionId: _SubscriptionId,
              resourceGroupName: "",
              accountName: _AccountName,
              authentication: _auth_msi,
              folderPath: ".",
              fileName: null,
              copyAsText: true
           );
            var actualExp = Assert.Throws<ArgumentException>(() => resource.CreateCopySink());
            Assert.AreEqual("ADLS datastore must specify subscription id and resource group name when used as data transfer destination.", actualExp.Message);
            Assert.AreEqual("ADLS datastore must specify subscription id and resource group name when used as data transfer destination.", actualExp.GetMessageFormat());
        }

        [Test]
        public void TestCreateCopySink_Normal_Return()
        {
            AzureDataLakeStorePipelineResource resource = new AzureDataLakeStorePipelineResource(
              subscriptionId: _SubscriptionId,
              resourceGroupName: _ResourceGroupName,
              accountName: _AccountName,
              authentication: _auth_msi,
              folderPath: ".",
              fileName: null,
              copyAsText: true
           );
            var actualRet = resource.CreateCopySink();
            Assert.IsInstanceOf<AzureDataLakeStoreSink>(actualRet);
        }

        [Test]
        public void TestToString_Normal_Return()
        {
            AzureDataLakeStorePipelineResource resource = new AzureDataLakeStorePipelineResource(
              subscriptionId: _SubscriptionId,
              resourceGroupName: _ResourceGroupName,
              accountName: _AccountName,
              authentication: _auth_msi,
              folderPath: _FolderPath,
              fileName: _FileName,
              copyAsText: true
           );
            var actualRet = resource.ToString();
            StringAssert.Contains(_AccountName, actualRet);
            StringAssert.Contains(_FolderPath, actualRet);
            StringAssert.Contains(_FileName, actualRet);
        }
    }
}
