﻿using Microsoft.Aether.BackendCommon.Configuration;
using Microsoft.Aether.DataTransferCloud.DataFactory.Authentications;
using Microsoft.Aether.DataTransferCloud.DataFactory.Resources;
using Microsoft.Azure.Management.DataFactory.Models;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Reflection;
using System.Text;

namespace DataTransferCloudK8s.DataFactory.Test.Resources
{
    public class AzureDataLakeGen2PipelineResourceTests
    {
        private DataTransferAuthentication _auth_sp;
        private DataTransferAuthentication _auth_accountkey;
        private DataTransferAuthentication _auth_msi;

        private readonly string _AccountName = "accountName";
        private readonly string _AccountKey = "myAccountKey";
        private readonly string _ServicePrincipalId;
        private readonly string _ServicePrincipalKey;
        private readonly string _Tenant;
        private readonly string _FileName;
        private readonly string _FolderPath;

        public AzureDataLakeGen2PipelineResourceTests()
        {
            _ServicePrincipalId = Guid.NewGuid().ToString();
            _ServicePrincipalKey = "mySecret";
            _Tenant = Guid.NewGuid().ToString();

            _FileName = "testFileName";
            _FolderPath = "./hello/";


            _auth_accountkey = new DataTransferAuthentication
                .DataTransferAuthenticationBuilder(AuthenticationTypes.AccountKey)
                .SetAccountKey(_AccountKey)
                        .build();

            _auth_sp = new DataTransferAuthentication
                .DataTransferAuthenticationBuilder(AuthenticationTypes.ServicePrincipal)
                .SetServicePrincipalId(_ServicePrincipalId)
                .SetServicePrincipalKey(_ServicePrincipalKey)
                .SetServicePrincipalTenant(_Tenant)
                        .build();

            _auth_msi = new DataTransferAuthentication
                .DataTransferAuthenticationBuilder(AuthenticationTypes.MSI)
                        .build();
        }

        [Test]
        public void TestGetLinkedService_AccountKey_Return()
        {
            AzureDataLakeGen2PipelineResource resource = new AzureDataLakeGen2PipelineResource(
               accountName: _AccountName,
               authentication: _auth_accountkey,
               folderPath: "."
            );
            var actualRet = resource.GetLinkedService();
            Assert.IsInstanceOf<AzureBlobFSLinkedService>(actualRet);
            var uriFormat = $"https://{_AccountName}.dfs{Env.StorageEndpoint()}";
            Assert.AreEqual(uriFormat, ((AzureBlobFSLinkedService)actualRet).Url.ToString());
            Assert.AreEqual(new SecureString(_AccountKey).ToString(), ((AzureBlobFSLinkedService)actualRet).AccountKey.ToString());
        }

        [Test]
        public void TestGetLinkedService_ServicePrincipal_Return()
        {
            AzureDataLakeGen2PipelineResource resource = new AzureDataLakeGen2PipelineResource(
              accountName: _AccountName,
              authentication: _auth_sp,
              folderPath: "."
           );
            var actualRet = resource.GetLinkedService();
            Assert.IsInstanceOf<AzureBlobFSLinkedService>(actualRet);
            var uriFormat = $"https://{_AccountName}.dfs{Env.StorageEndpoint()}";
            Assert.AreEqual(uriFormat, ((AzureBlobFSLinkedService)actualRet).Url);
            Assert.AreEqual(_ServicePrincipalId, ((AzureBlobFSLinkedService)actualRet).ServicePrincipalId);
            Assert.AreEqual(new SecureString(_ServicePrincipalKey).ToString(), ((AzureBlobFSLinkedService)actualRet).ServicePrincipalKey.ToString());
            Assert.AreEqual(_Tenant, ((AzureBlobFSLinkedService)actualRet).Tenant);
        }

        [Test]
        public void TestGetLinkedService_MSI_Return()
        {
            AzureDataLakeGen2PipelineResource resource = new AzureDataLakeGen2PipelineResource(
               accountName: _AccountName,
               authentication: _auth_msi,
               folderPath: "."
            );
            var actualRet = resource.GetLinkedService();
            Assert.IsInstanceOf<AzureBlobFSLinkedService>(actualRet);
            var uriFormat = $"https://{_AccountName}.dfs{Env.StorageEndpoint()}";
            Assert.AreEqual(uriFormat, ((AzureBlobFSLinkedService)actualRet).Url);
        }

        [Test]
        public void TestGetCopyFormat_CopyAsTextFalse_Return()
        {
            AzureDataLakeGen2PipelineResource resource = new AzureDataLakeGen2PipelineResource(
               accountName: _AccountName,
               authentication: _auth_msi,
               folderPath: ".",
               fileName: null,
               copyAsText: false
            );
            var actualRet = resource.GetDataset("dummyLinkedServiceName");
            Assert.IsInstanceOf<AzureBlobFSDataset>(actualRet);
            Assert.AreEqual(null, ((AzureBlobFSDataset)actualRet).Format);
        }

        [Test]
        public void TestGetCopyFormat_CopyAsTextTrue_Return()
        {
            AzureDataLakeGen2PipelineResource resource = new AzureDataLakeGen2PipelineResource(
               accountName: _AccountName,
               authentication: _auth_msi,
               folderPath: ".",
               fileName: null,
               copyAsText: true
            );
            var actualRet = resource.GetDataset("dummyLinkedServiceName");
            Assert.IsInstanceOf<AzureBlobFSDataset>(actualRet);
            Assert.IsInstanceOf<TextFormat>(((AzureBlobFSDataset)actualRet).Format);
        }

        [Test]
        public void TestCreateCopySource_Normal_Return()
        {
            AzureDataLakeGen2PipelineResource resource = new AzureDataLakeGen2PipelineResource(
               accountName: _AccountName,
               authentication: _auth_msi,
               folderPath: ".",
               fileName: null,
               copyAsText: true
            );
            var actualRet = resource.CreateCopySource();
            Assert.IsInstanceOf<AzureBlobFSSource>(actualRet);
        }

        [Test]
        public void TestCreateCopySink_Normal_Return()
        {
            AzureDataLakeGen2PipelineResource resource = new AzureDataLakeGen2PipelineResource(
               accountName: _AccountName,
               authentication: _auth_msi,
               folderPath: ".",
               fileName: null,
               copyAsText: true
            );
            var actualRet = resource.CreateCopySink();
            Assert.IsInstanceOf<AzureBlobFSSink>(actualRet);
        }

        [Test]
        public void TestToString_Normal_Return()
        {
            AzureDataLakeGen2PipelineResource resource = new AzureDataLakeGen2PipelineResource(
               accountName: _AccountName,
               authentication: _auth_msi,
               folderPath: _FolderPath,
               fileName: _FileName,
               copyAsText: true
            );
            var actualRet = resource.ToString();
            StringAssert.Contains(_AccountName, actualRet);
            StringAssert.Contains(_FolderPath, actualRet);
            StringAssert.Contains(_FileName, actualRet);
        }
    }
}
