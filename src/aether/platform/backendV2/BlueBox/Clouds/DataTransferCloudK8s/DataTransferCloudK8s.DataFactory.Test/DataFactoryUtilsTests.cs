﻿using Microsoft.Aether.DataTransferCloud.DataFactory;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Text;

namespace DataTransferCloudK8s.DataFactory.Test
{
    public class DataFactoryUtilsTests
    {
        [Test]
        public void Test_ExtractErrorCodeFromMessage()
        {
            var message = "{\"errorCode\":\"1000\",\"message\":\"ErrorCode=SystemErrorFailedToGetAccessTokenByMI,'Type=Microsoft.DataTransfer.Common.Shared.HybridDeliveryException,Message=Internal Server Error. Please retry later. If issue consist repro, please contact us.,Source=Microsoft.DataTransfer.ClientLibrary,''Type=Microsoft.DataTransfer.MsiStoreService.Client.Models.ErrorResponseException,Message=Acquire MI token from AAD failed. ErrorCode: invalid_client, Message: A configuration issue is preventing authentication - check the error message from the server for details. You can modify the configuration in the application registration portal. See https:\\/\\/aka.ms\\/msal-net-invalid-client for details.  Original exception: AADSTS700027: Client assertion contains an invalid signature. [Reason - The key was not found., Thumbprint of key used by client: 'C3879A35D6A7705122CDD4739D2ACE676F2DD181', Please visit the Azure Portal, Graph Explorer or directly use MS Graph to see configured keys for app Id '3b0ea827-fbb6-4165-abbd-6f9c080b7e53'. Review the documentation at https:\\/\\/docs.microsoft.com\\/en-us\\/graph\\/deployments to determine the corresponding service endpoint and https:\\/\\/docs.microsoft.com\\/en-us\\/graph\\/api\\/application-get?view=graph-rest-1.0&tabs=http to build a query request URL, such as 'https:\\/\\/graph.microsoft.com\\/beta\\/applications\\/3b0ea827-fbb6-4165-abbd-6f9c080b7e53']\r\nTrace ID: 3cc4f687-75c0-46d6-9a03-18ef33e52600\r\nCorrelation ID: d3fe353c-c98c-4809-a352-ebee43623288\r\nTimestamp: 2021-06-14 17:37:26Z,Source=Microsoft.DataTransfer.MsiStoreServiceClient,'\",\"failureType\":\"SystemError\",\"target\":\"CopyActivity\",\"details\":[]}";
            Assert.IsTrue(DataFactoryUtils.IsTransientDataFactoryError(message));

            message = "{\"errorCode\":\"1000\",\"message\":\"ErrorCode=AdlsGen2OperationFailed,'Type=Microsoft.DataTransfer.Common.Shared.HybridDeliveryException,Message=Internal Server Error. Please retry later. If issue consist repro, please contact us.,Source=Microsoft.DataTransfer.ClientLibrary,''Type=Microsoft.DataTransfer.MsiStoreService.Client.Models.ErrorResponseException,Message=Acquire MI token from AAD failed. ErrorCode: invalid_client, Message: A configuration issue is preventing authentication - check the error message from the server for details. You can modify the configuration in the application registration portal. See https:\\/\\/aka.ms\\/msal-net-invalid-client for details.  Original exception: AADSTS700027: Client assertion contains an invalid signature. [Reason - The key was not found., Thumbprint of key used by client: 'C3879A35D6A7705122CDD4739D2ACE676F2DD181', Please visit the Azure Portal, Graph Explorer or directly use MS Graph to see configured keys for app Id '3b0ea827-fbb6-4165-abbd-6f9c080b7e53'. Review the documentation at https:\\/\\/docs.microsoft.com\\/en-us\\/graph\\/deployments to determine the corresponding service endpoint and https:\\/\\/docs.microsoft.com\\/en-us\\/graph\\/api\\/application-get?view=graph-rest-1.0&tabs=http to build a query request URL, such as 'https:\\/\\/graph.microsoft.com\\/beta\\/applications\\/3b0ea827-fbb6-4165-abbd-6f9c080b7e53']\r\nTrace ID: 3cc4f687-75c0-46d6-9a03-18ef33e52600\r\nCorrelation ID: d3fe353c-c98c-4809-a352-ebee43623288\r\nTimestamp: 2021-06-14 17:37:26Z,Source=Microsoft.DataTransfer.MsiStoreServiceClient,'\",\"failureType\":\"SystemError\",\"target\":\"CopyActivity\",\"details\":[]}";
            Assert.IsTrue(DataFactoryUtils.IsTransientDataFactoryError(message));

            message = "{\"errorCode\":\"1000\",\"message\":\"ErrorCode=AdlsGen2OperationFailed,'Type=Microsoft.DataTransfer.Common.Shared.HybridDeliveryException,Message=Internal Server Error. Please retry later. If issue consist repro, please contact us.,Source=Microsoft.DataTransfer.ClientLibrary,''Type=Microsoft.DataTransfer.MsiStoreService.Client.Models.ErrorResponseException,Message=Acquire MI token from AAD failed. ErrorCode: AdlsGen2OperationFailed, Message: A configuration issue is preventing authentication - check the error message from the server for details. You can modify the configuration in the application registration portal. See https:\\/\\/aka.ms\\/msal-net-invalid-client for details.  Original exception: AADSTS700027: Client assertion contains an invalid signature. [Reason - The key was not found., Thumbprint of key used by client: 'C3879A35D6A7705122CDD4739D2ACE676F2DD181', Please visit the Azure Portal, Graph Explorer or directly use MS Graph to see configured keys for app Id '3b0ea827-fbb6-4165-abbd-6f9c080b7e53'. Review the documentation at https:\\/\\/docs.microsoft.com\\/en-us\\/graph\\/deployments to determine the corresponding service endpoint and https:\\/\\/docs.microsoft.com\\/en-us\\/graph\\/api\\/application-get?view=graph-rest-1.0&tabs=http to build a query request URL, such as 'https:\\/\\/graph.microsoft.com\\/beta\\/applications\\/3b0ea827-fbb6-4165-abbd-6f9c080b7e53']\r\nTrace ID: 3cc4f687-75c0-46d6-9a03-18ef33e52600\r\nCorrelation ID: d3fe353c-c98c-4809-a352-ebee43623288\r\nTimestamp: 2021-06-14 17:37:26Z,Source=Microsoft.DataTransfer.MsiStoreServiceClient,'\",\"failureType\":\"SystemError\",\"target\":\"CopyActivity\",\"details\":[]}";
            Assert.IsFalse(DataFactoryUtils.IsTransientDataFactoryError(message));

            message = "{\"errorCode\":\"1000\",\"message\":\"ErrorCode=invalid_client,'Type=Microsoft.DataTransfer.Common.Shared.HybridDeliveryException,Message=Internal Server Error. Please retry later. If issue consist repro, please contact us.,Source=Microsoft.DataTransfer.ClientLibrary,''Type=Microsoft.DataTransfer.MsiStoreService.Client.Models.ErrorResponseException,Message=Acquire MI token from AAD failed. ErrorCode: AdlsGen2OperationFailed, Message: A configuration issue is preventing authentication - check the error message from the server for details. You can modify the configuration in the application registration portal. See https:\\/\\/aka.ms\\/msal-net-invalid-client for details.  Original exception: AADSTS700027: Client assertion contains an invalid signature. [Reason - The key was not found., Thumbprint of key used by client: 'C3879A35D6A7705122CDD4739D2ACE676F2DD181', Please visit the Azure Portal, Graph Explorer or directly use MS Graph to see configured keys for app Id '3b0ea827-fbb6-4165-abbd-6f9c080b7e53'. Review the documentation at https:\\/\\/docs.microsoft.com\\/en-us\\/graph\\/deployments to determine the corresponding service endpoint and https:\\/\\/docs.microsoft.com\\/en-us\\/graph\\/api\\/application-get?view=graph-rest-1.0&tabs=http to build a query request URL, such as 'https:\\/\\/graph.microsoft.com\\/beta\\/applications\\/3b0ea827-fbb6-4165-abbd-6f9c080b7e53']\r\nTrace ID: 3cc4f687-75c0-46d6-9a03-18ef33e52600\r\nCorrelation ID: d3fe353c-c98c-4809-a352-ebee43623288\r\nTimestamp: 2021-06-14 17:37:26Z,Source=Microsoft.DataTransfer.MsiStoreServiceClient,'\",\"failureType\":\"SystemError\",\"target\":\"CopyActivity\",\"details\":[]}";
            Assert.IsFalse(DataFactoryUtils.IsTransientDataFactoryError(message));

            message = "ErrorCode=NoAccess";
            Assert.IsFalse(DataFactoryUtils.IsTransientDataFactoryError(message));
        }
    }
}
