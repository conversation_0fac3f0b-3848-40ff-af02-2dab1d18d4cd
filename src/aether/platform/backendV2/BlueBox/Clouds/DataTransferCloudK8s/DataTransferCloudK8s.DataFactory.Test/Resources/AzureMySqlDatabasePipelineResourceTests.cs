﻿using Microsoft.Aether.DataTransferCloud.DataFactory.Authentications;
using Microsoft.Aether.DataTransferCloud.DataFactory.Resources;
using Microsoft.Azure.Management.DataFactory.Models;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Reflection;
using System.Text;

namespace DataTransferCloudK8s.DataFactory.Test.Resources
{
    public class AzureMySqlDatabasePipelineResourceTests
    {
        private DataTransferAuthentication _auth_sqlauthentication;

        private readonly string _ServerUri;
        private readonly string _DatabaseName;
        private readonly string _PortNumber;
        private readonly string _TableName;
        private readonly string _Query;

        public AzureMySqlDatabasePipelineResourceTests()
        {
            _ServerUri = "testServiceUrl";
            _DatabaseName = "testdb";
            _PortNumber = "3306";
            _TableName = "testTableName";
            _Query = "select * from hello;";

            _auth_sqlauthentication = new DataTransferAuthentication
                .DataTransferAuthenticationBuilder(AuthenticationTypes.SqlAuthentication)
                .SetUserId("myuserId")
                .SetUserPassword("mypwd")
                        .build();
        }

        [Test]
        public void TestGetLinkedService_SqlAuthentication_Return()
        {
            AzureMySqlDatabasePipelineResource resource = new AzureMySqlDatabasePipelineResource(
              serverUri: _ServerUri,
              databaseName: _DatabaseName,
              authentication: _auth_sqlauthentication,
              tableName: _TableName
           );
            var actualRet = resource.GetLinkedService();
            Assert.IsInstanceOf<AzureMySqlLinkedService>(actualRet);
            var connectionStringFormat = (string)typeof(AzureMySqlDatabasePipelineResource).GetField("ConnectionStringFormat", BindingFlags.NonPublic | BindingFlags.Instance | BindingFlags.Static).GetValue(resource);
            connectionStringFormat = string.Format(connectionStringFormat, _ServerUri, _PortNumber, _DatabaseName, _auth_sqlauthentication.UserId, _auth_sqlauthentication.UserPassword);
            Assert.AreEqual(new SecureString(connectionStringFormat).ToString(), ((AzureMySqlLinkedService)actualRet).ConnectionString.ToString());
        }

        [Test]
        public void TestGetDataset_Normal_Return()
        {
            AzureMySqlDatabasePipelineResource resource = new AzureMySqlDatabasePipelineResource(
              serverUri: _ServerUri,
              databaseName: _DatabaseName,
              authentication: _auth_sqlauthentication,
              tableName: _TableName
           );
            var actualRet = resource.GetDataset("dummyLinkedServiceName");
            Assert.IsInstanceOf<AzureMySqlTableDataset>(actualRet);
            Assert.AreEqual(_TableName, ((AzureMySqlTableDataset)actualRet).TableName);
        }

        [Test]
        public void TestCreateCopySource_Normal_Return()
        {
            AzureMySqlDatabasePipelineResource resource = new AzureMySqlDatabasePipelineResource(
              serverUri: _ServerUri,
              databaseName: _DatabaseName,
              authentication: _auth_sqlauthentication,
              tableName: null,
              query: _Query
           );
            var actualRet = resource.CreateCopySource();
            Assert.IsInstanceOf<AzureMySqlSource>(actualRet);
            Assert.AreEqual(_Query, ((AzureMySqlSource)actualRet).Query);
        }

        [Test]
        public void TestCreateCopySink_Normal_Return()
        {
            AzureMySqlDatabasePipelineResource resource = new AzureMySqlDatabasePipelineResource(
              serverUri: _ServerUri,
              databaseName: _DatabaseName,
              authentication: _auth_sqlauthentication,
              tableName: null,
              query: _Query
           );
            var actualRet = resource.CreateCopySink();
            Assert.IsInstanceOf<AzureMySqlSink>(actualRet);
        }

        [Test]
        public void TestToString_ByQuery_Return()
        {
            AzureMySqlDatabasePipelineResource resource = new AzureMySqlDatabasePipelineResource(
              serverUri: _ServerUri,
              databaseName: _DatabaseName,
              authentication: _auth_sqlauthentication,
              tableName: null,
              query: _Query
           );
            var actualRet = resource.ToString();
            StringAssert.DoesNotContain($"table: {_TableName}, ", actualRet);
            StringAssert.Contains($"query: {_Query}, ", actualRet);
        }

        [Test]
        public void TestToString_ByTable_Return()
        {
            AzureMySqlDatabasePipelineResource resource = new AzureMySqlDatabasePipelineResource(
              serverUri: _ServerUri,
              databaseName: _DatabaseName,
              authentication: _auth_sqlauthentication,
              tableName: _TableName
           );
            var actualRet = resource.ToString();
            StringAssert.Contains($"table: {_TableName}, ", actualRet);
            StringAssert.DoesNotContain($"query: {_Query}, ", actualRet);
        }
    }
}
