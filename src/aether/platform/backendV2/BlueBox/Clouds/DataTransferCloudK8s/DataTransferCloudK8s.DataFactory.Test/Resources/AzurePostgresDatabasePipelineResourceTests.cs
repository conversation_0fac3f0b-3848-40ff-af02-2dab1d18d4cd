﻿using Microsoft.Aether.DataTransferCloud.DataFactory.Authentications;
using Microsoft.Aether.DataTransferCloud.DataFactory.Resources;
using Microsoft.Azure.Management.DataFactory.Models;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Reflection;
using System.Text;

namespace DataTransferCloudK8s.DataFactory.Test.Resources
{
    public class AzurePostgresDatabasePipelineResourceTests
    {

        private DataTransferAuthentication _auth_sqlauthentication;

        private readonly string _ServerUri;
        private readonly string _DatabaseName;
        private readonly string _PortNumber;
        private readonly string _TableName;
        private readonly string _Query;

        public AzurePostgresDatabasePipelineResourceTests()
        {
            _ServerUri = "testServiceUrl";
            _DatabaseName = "testdb";
            _PortNumber = "5432";
            _TableName = "testTableName";
            _Query = "select * from hello;";

            _auth_sqlauthentication = new DataTransferAuthentication
                .DataTransferAuthenticationBuilder(AuthenticationTypes.SqlAuthentication)
                .SetUserId("myuserId")
                .SetUserPassword("mypwd")
                        .build();
        }

        [Test]
        public void TestGetLinkedService_SqlAuthentication_Return()
        {
            AzurePostgresDatabasePipelineResource resource = new AzurePostgresDatabasePipelineResource(
              serverUri: _ServerUri,
              databaseName: _DatabaseName,
              authentication: _auth_sqlauthentication,
              tableName: _TableName
           );
            var actualRet = resource.GetLinkedService();
            Assert.IsInstanceOf<AzurePostgreSqlLinkedService>(actualRet);
            var connectionStringFormat = (string)typeof(AzurePostgresDatabasePipelineResource).GetField("ConnectionStringFormat", BindingFlags.NonPublic | BindingFlags.Instance | BindingFlags.Static).GetValue(resource);
            connectionStringFormat = connectionStringFormat
                            .Replace("{serverUri}", _ServerUri)
                            .Replace("{databaseName}", _DatabaseName)
                            .Replace("{portNumber}", _PortNumber)
                            .Replace("{userId}", _auth_sqlauthentication.UserId)
                            .Replace("{userPassword}", _auth_sqlauthentication.UserPassword);
            Assert.AreEqual(new SecureString(connectionStringFormat).ToString(), ((AzurePostgreSqlLinkedService)actualRet).ConnectionString.ToString());
        }

        [Test]
        public void TestGetDataset_Normal_Return()
        {
            AzurePostgresDatabasePipelineResource resource = new AzurePostgresDatabasePipelineResource(
              serverUri: _ServerUri,
              databaseName: _DatabaseName,
              authentication: _auth_sqlauthentication,
              tableName: _TableName
           );
            var actualRet = resource.GetDataset("dummyLinkedServiceName");
            Assert.IsInstanceOf<AzurePostgreSqlTableDataset>(actualRet);
            Assert.AreEqual(_TableName, ((AzurePostgreSqlTableDataset)actualRet).TableName);
        }

        [Test]
        public void TestCreateCopySource_Normal_Return()
        {
            AzurePostgresDatabasePipelineResource resource = new AzurePostgresDatabasePipelineResource(
              serverUri: _ServerUri,
              databaseName: _DatabaseName,
              authentication: _auth_sqlauthentication,
              tableName: null,
              query: _Query
           );
            var actualRet = resource.CreateCopySource();
            Assert.IsInstanceOf<AzurePostgreSqlSource>(actualRet);
            Assert.AreEqual(_Query, ((AzurePostgreSqlSource)actualRet).Query);
        }

        [Test]
        public void TestCreateCopySink_Normal_Return()
        {
            AzurePostgresDatabasePipelineResource resource = new AzurePostgresDatabasePipelineResource(
              serverUri: _ServerUri,
              databaseName: _DatabaseName,
              authentication: _auth_sqlauthentication,
              tableName: null,
              query: _Query
           );
            var actualRet = resource.CreateCopySink();
            Assert.IsInstanceOf<AzurePostgreSqlSink>(actualRet);
            Assert.AreEqual(_Query, ((AzurePostgreSqlSink)actualRet).PreCopyScript);
        }

        [Test]
        public void TestToString_ByQuery_Return()
        {
            AzurePostgresDatabasePipelineResource resource = new AzurePostgresDatabasePipelineResource(
              serverUri: _ServerUri,
              databaseName: _DatabaseName,
              authentication: _auth_sqlauthentication,
              tableName: null,
              query: _Query
           );
            var actualRet = resource.ToString();
            StringAssert.Contains(_DatabaseName, actualRet);
            StringAssert.Contains(_ServerUri, actualRet);
            StringAssert.Contains(_PortNumber, actualRet);
            StringAssert.Contains(_Query, actualRet);
        }
    }
}
