﻿using Microsoft.Aether.BackendCommon.Configuration;
using Microsoft.Aether.DataTransferCloud.DataFactory.Authentications;
using Microsoft.Aether.DataTransferCloud.DataFactory.Resources;
using Microsoft.Azure.Management.DataFactory.Models;
using NUnit.Framework;
using System;

namespace DataTransferCloudK8s.DataFactory.Test.Resources
{
    public class AzureBlobPipelineResourceTests
    {
        private DataTransferAuthentication _auth_sas;
        private DataTransferAuthentication _auth_sp;
        private DataTransferAuthentication _auth_accountkey;
        private DataTransferAuthentication _auth_msi;

        private readonly string _SasToken;
        private readonly string _AccountName = "accountName";
        private readonly string _AccountKey = "myAccountKey";
        private readonly string _ServicePrincipalId;
        private readonly string _ServicePrincipalKey;
        private readonly string _Tenant;

        private readonly string _FileName;
        private readonly string _FolderPath;

        public AzureBlobPipelineResourceTests()
        {
            _ServicePrincipalId = Guid.NewGuid().ToString();
            _ServicePrincipalKey = "mySecret";
            _Tenant = Guid.NewGuid().ToString();

            _SasToken = "mySasToken";
            _FileName = "testFileName";
            _FolderPath = "./hello/";

            _auth_sas = new DataTransferAuthentication
                .DataTransferAuthenticationBuilder(AuthenticationTypes.Sas)
                .SetSasToken(_SasToken)
                        .build();
            _auth_accountkey = new DataTransferAuthentication
                .DataTransferAuthenticationBuilder(AuthenticationTypes.AccountKey)
                .SetAccountKey(_AccountKey)
                        .build();

            _auth_sp = new DataTransferAuthentication
                .DataTransferAuthenticationBuilder(AuthenticationTypes.ServicePrincipal)
                .SetServicePrincipalId(_ServicePrincipalId)
                .SetServicePrincipalKey(_ServicePrincipalKey)
                .SetServicePrincipalTenant(_Tenant)
                        .build();

            _auth_msi = new DataTransferAuthentication
                .DataTransferAuthenticationBuilder(AuthenticationTypes.MSI)
                        .build();
        }

        [Test]
        public void TestGetLinkedService_Sas_Return()
        {
            AzureBlobPipelineResource resource = new AzureBlobPipelineResource(
               accountName : _AccountName,
               authentication: _auth_sas,
               folderPath : "."
            );
            var actualRet = resource.GetLinkedService();
            Assert.IsInstanceOf<AzureStorageLinkedService>(actualRet);
            var blobAccountSasEndpointFormat = $"https://{_AccountName}{Env.StorageBlobEndpoint()}/{_SasToken}";
            Assert.AreEqual(new SecureString(blobAccountSasEndpointFormat).ToString(), ((AzureStorageLinkedService)actualRet).SasUri.ToString());

        }

        [Test]
        public void TestGetLinkedService_AccountKey_Return()
        {
            AzureBlobPipelineResource resource = new AzureBlobPipelineResource(
               accountName: _AccountName,
               authentication: _auth_accountkey,
               folderPath: "."
            );
            var actualRet = resource.GetLinkedService();
            Assert.IsInstanceOf<AzureStorageLinkedService>(actualRet);
            var expectedConnectionStr = new SecureString(
                                $"DefaultEndpointsProtocol=https;" +
                                $"AccountName={_AccountName};" +
                                $"AccountKey={_AccountKey}");
            Assert.AreEqual(expectedConnectionStr.ToString(), ((AzureStorageLinkedService)actualRet).ConnectionString.ToString());

        }

        [Test]
        public void TestGetLinkedService_ServicePrincipal_Return()
        {
            AzureBlobPipelineResource resource = new AzureBlobPipelineResource(
               accountName: _AccountName,
               authentication: _auth_sp,
               folderPath: "."
            );
            var actualRet = resource.GetLinkedService();
            Assert.IsInstanceOf<AzureBlobStorageLinkedService>(actualRet);
            var blobAccountEndpointFormat = $"https://{_AccountName}{Env.StorageBlobEndpoint()}/";
            Assert.AreEqual(blobAccountEndpointFormat, ((AzureBlobStorageLinkedService)actualRet).ServiceEndpoint);
            Assert.AreEqual(_ServicePrincipalId, ((AzureBlobStorageLinkedService)actualRet).ServicePrincipalId);
            Assert.AreEqual(new SecureString(_ServicePrincipalKey).ToString(), ((AzureBlobStorageLinkedService)actualRet).ServicePrincipalKey.ToString());
            Assert.AreEqual(_Tenant, ((AzureBlobStorageLinkedService)actualRet).Tenant);
        }

        [Test]
        public void TestGetLinkedService_MSI_Return()
        {
            AzureBlobPipelineResource resource = new AzureBlobPipelineResource(
               accountName: _AccountName,
               authentication: _auth_msi,
               folderPath: "."
            );
            var actualRet = resource.GetLinkedService();
            Assert.IsInstanceOf<AzureBlobStorageLinkedService>(actualRet);
            var blobAccountEndpointFormat = $"https://{_AccountName}{Env.StorageBlobEndpoint()}/";
            Assert.AreEqual(blobAccountEndpointFormat, ((AzureBlobStorageLinkedService)actualRet).ServiceEndpoint);
        }

        [Test]
        public void TestGetCopyFormat_CopyAsTextFalse_Return()
        {
            AzureBlobPipelineResource resource = new AzureBlobPipelineResource(
               accountName: _AccountName,
               authentication: _auth_msi,
               folderPath: ".",
               fileName: null,
               copyAsText: false
            );
            var actualRet = resource.GetDataset("dummyLinkedServiceName");
            Assert.IsInstanceOf<AzureBlobDataset>(actualRet);
            Assert.AreEqual(null, ((AzureBlobDataset)actualRet).Format);
        }

        [Test]
        public void TestGetCopyFormat_CopyAsTextTrue_Return()
        {
            AzureBlobPipelineResource resource = new AzureBlobPipelineResource(
               accountName: _AccountName,
               authentication: _auth_msi,
               folderPath: ".",
               fileName: null,
               copyAsText: true
            );
            var actualRet = resource.GetDataset("dummyLinkedServiceName");
            Assert.IsInstanceOf<AzureBlobDataset>(actualRet);
            Assert.IsInstanceOf<TextFormat>(((AzureBlobDataset)actualRet).Format);
        }

        [Test]
        public void TestCreateCopySource_Normal_Return()
        {
            AzureBlobPipelineResource resource = new AzureBlobPipelineResource(
               accountName: _AccountName,
               authentication: _auth_msi,
               folderPath: ".",
               fileName: null,
               copyAsText: true
            );
            var actualRet = resource.CreateCopySource();
            Assert.IsInstanceOf<BlobSource>(actualRet);
        }

        [Test]
        public void TestCreateCopySink_Normal_Return()
        {
            AzureBlobPipelineResource resource = new AzureBlobPipelineResource(
               accountName: _AccountName,
               authentication: _auth_msi,
               folderPath: ".",
               fileName: null,
               copyAsText: true
            );
            var actualRet = resource.CreateCopySink();
            Assert.IsInstanceOf<BlobSink>(actualRet);
        }

        [Test]
        public void TestToString_Normal_Return()
        {
            AzureBlobPipelineResource resource = new AzureBlobPipelineResource(
               accountName: _AccountName,
               authentication: _auth_msi,
               folderPath: _FolderPath,
               fileName: _FileName,
               copyAsText: true
            );
            var actualRet = resource.ToString();
            StringAssert.Contains(_AccountName, actualRet);
            StringAssert.Contains(_FolderPath, actualRet);
            StringAssert.Contains(_FileName, actualRet);
        }
    }
}
