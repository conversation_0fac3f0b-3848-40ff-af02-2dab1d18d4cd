﻿using Microsoft.Aether.DataTransferCloud.DataFactory;
using Microsoft.Azure.Management.DataFactory.Models;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Text;

namespace DataTransferCloudK8s.DataFactory.Test
{
    public class PipelineRunExtensionsTests
    {
        [TestCase("InProgress", DataFactoryOperationStatus.InProgress)]
        [TestCase("Queued", DataFactoryOperationStatus.Queued)]
        [TestCase("Cancelled", DataFactoryOperationStatus.Cancelled)]
        [TestCase("Canceling", DataFactoryOperationStatus.Canceling)]
        [TestCase("Succeeded", DataFactoryOperationStatus.Succeeded)]
        [TestCase("Failed", DataFactoryOperationStatus.Failed)]
        public void TestGetStatus_Normal_Retrun(string status, DataFactoryOperationStatus expectedRet)
        {
            PipelineRun run = new PipelineRun(status: status);
            var actualRet = run.GetStatus();
            Assert.AreEqual(expectedRet, actualRet);
        }

        [TestCase("Others")]
        public void TestGetStatus_Other_Retrun(string status)
        {
            PipelineRun run = new PipelineRun(status: status);
            NotSupportedException ex = Assert.Throws<NotSupportedException>(() => run.GetStatus());
            Assert.AreEqual("Unrecognized ADF run status : Others", ex.Message);
        }
    }
}
