﻿using Microsoft.Aether.DataTransferCloud.DataFactory;
using Microsoft.Aether.DataTransferCloud.DataFactory.Resources;
using Microsoft.Azure.Management.DataFactory;
using Microsoft.Azure.Management.DataFactory.Models;
using Microsoft.Rest;
using Microsoft.Rest.Azure;
using Moq;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Reflection;
using System.Security.Authentication;
using System.Text;
using System.Threading.Tasks;

namespace DataTransferCloudK8s.DataFactory.Test
{


    public class DataFactoryClientTests
    {
        private DataFactoryClient _dummyDataFactoryClient;

        private string loggingId;
        private string dataFactorySubscriptionId;
        private string dataFactoryResourceGroup;
        private string dataFactoryName;
        private string dataFactoryAuthToken;

        private IPipelineResource source;
        private IPipelineResource sink;

        private readonly MockRepository _repository;

        private IDataFactoryManagementClient _fakeIDataFactoryManagementClient;
        private IPipelineResource _fakeIPipelineResource;

        private LinkedService _dummyLinkedService;
        private Dataset _dummyDataset;
        private CopySource _dummyCopySource;
        private CopySink _dummyCopySink;
        private DataFactoryOperationEntity _dummyDataFactoryOperationEntity;

        public DataFactoryClientTests()
        {
            loggingId = Guid.NewGuid().ToString();
            dataFactorySubscriptionId = Guid.NewGuid().ToString();
            dataFactoryResourceGroup = "testRG";
            dataFactoryName = "testFactoryName";
            dataFactoryAuthToken = "testFactoryAuthToken";

            _dummyDataFactoryClient = new DataFactoryClient(
                loggingId,
                dataFactorySubscriptionId,
                dataFactoryResourceGroup,
                dataFactoryName,
                dataFactoryAuthToken
            );

            _repository = new MockRepository(MockBehavior.Loose) { DefaultValue = DefaultValue.Mock, CallBase = true };

            _dummyCopySource = new CopySource();
            _dummyCopySink = new CopySink();

            // Needed when creating DataFactoryManagementClient
            Environment.SetEnvironmentVariable("K8SRESOURCEMANAGERURL", "https://management.azure.com");
        }

        [SetUp]
        public void Init()
        {
            source = _repository.Create<IPipelineResource>().Object;
            sink = _repository.Create<IPipelineResource>().Object;

            _fakeIDataFactoryManagementClient = _repository.Create<IDataFactoryManagementClient>().Object;
            _fakeIPipelineResource = _repository.Create<IPipelineResource>().Object;

            _dummyLinkedService = new LinkedService();
            Mock.Get(_fakeIPipelineResource)
                .Setup(
                   service => service.GetLinkedService())
                .Returns(_dummyLinkedService);

            _dummyDataset = new Dataset();
            Mock.Get(_fakeIPipelineResource)
                .Setup(
                   service => service.GetDataset(
                       It.IsAny<string>()))
                .Returns(_dummyDataset);

            _dummyDataFactoryOperationEntity = new DataFactoryOperationEntity()
            {
                PipelineRunId = Guid.NewGuid().ToString(),
                PipelineName = "pipelineName"
            };
        }

        [Test]
        public void TestCreateDataFactoryManagementClient_Normal_Return()
        {
            MethodInfo method_CreateDataFactoryManagementClient = typeof(DataFactoryClient).GetMethod("CreateDataFactoryManagementClient", BindingFlags.Instance | BindingFlags.NonPublic);
            var actualRet = (IDataFactoryManagementClient)method_CreateDataFactoryManagementClient.Invoke(_dummyDataFactoryClient, new object[] { });
            Assert.IsInstanceOf<DataFactoryManagementClient>(actualRet);
            Assert.AreEqual(dataFactorySubscriptionId, actualRet.SubscriptionId);
        }

        [Test]
        public void TestCreateDatasetAsync_Normal_Return()
        {
            MethodInfo method_CreateDatasetAsync = typeof(DataFactoryClient).GetMethod("CreateDatasetAsync", BindingFlags.Instance | BindingFlags.NonPublic);
            var task = (Task)method_CreateDatasetAsync.Invoke(_dummyDataFactoryClient, new object[] { _fakeIDataFactoryManagementClient, _fakeIPipelineResource, "datasetName" });
            Assert.DoesNotThrowAsync(async() => await task.ConfigureAwait(false));
        }

        [Test]
        public void TestCreatePipelineAsync_Normal_Return()
        {
            MethodInfo method_CreatePipelineAsync = typeof(DataFactoryClient).GetMethod("CreatePipelineAsync", BindingFlags.Instance | BindingFlags.NonPublic);
            var task = (Task)method_CreatePipelineAsync.Invoke(_dummyDataFactoryClient, new object[] { 
                _fakeIDataFactoryManagementClient,
                "pipelineName",
                new List<string>(){ "sourceDatasetName" },
                "sinkDatasetName",
                new List<CopySource>(){ _dummyCopySource },
                _dummyCopySink});
            Assert.DoesNotThrowAsync(async () => await task.ConfigureAwait(false));
        }

        [Test]
        public async Task TestExecuteDataFactoryOperationAsync_Normal_Return()
        {
            int res = 30;
            var method_ExecuteDataFactoryOperationAsync = _dummyDataFactoryClient.GetType()
                         .GetMethods(BindingFlags.Instance | BindingFlags.NonPublic)
                         .Where(m => m.Name == "ExecuteDataFactoryOperationAsync" && m.IsGenericMethod == true)
                         .First();
            var method_ExecuteDataFactoryOperationAsyn_int =  method_ExecuteDataFactoryOperationAsync.MakeGenericMethod(typeof(int));

            Func<Task<int>> func = () => Task.FromResult(res);
            var task = (Task<int>)method_ExecuteDataFactoryOperationAsyn_int.Invoke(_dummyDataFactoryClient, new object[] { func });
            var actualRet = await task.ConfigureAwait(false);
            Assert.AreEqual(res, actualRet);
        }

        [TestCase(HttpStatusCode.NotFound, "Data Factory .* does not exist.")]
        public void TestExecuteDataFactoryOperationAsync_CloudException_ArgumentException(HttpStatusCode code, string message)
        {
            var method_ExecuteDataFactoryOperationAsync = _dummyDataFactoryClient.GetType()
                         .GetMethods(BindingFlags.Instance | BindingFlags.NonPublic)
                         .Where(m => m.Name == "ExecuteDataFactoryOperationAsync" && m.IsGenericMethod == true)
                         .First();
            var method_ExecuteDataFactoryOperationAsyn_int = method_ExecuteDataFactoryOperationAsync.MakeGenericMethod(typeof(int));

            Func<Task<int>> func = () => throw new CloudException()
            {
               Response  = new HttpResponseMessageWrapper(new HttpResponseMessage(code), "")
            };
            var task = (Task<int>)method_ExecuteDataFactoryOperationAsyn_int.Invoke(_dummyDataFactoryClient, new object[] { func });
            var actualExp = Assert.ThrowsAsync<ArgumentException>(async() => await task.ConfigureAwait(false));
            Assert.That(actualExp.Message, Does.Match(message));
        }

        [TestCase(HttpStatusCode.Forbidden, "Access to Data Factory .* is forbidden.")]
        public void TestExecuteDataFactoryOperationAsync_CloudException_AuthenticationException(HttpStatusCode code, string message)
        {
            var method_ExecuteDataFactoryOperationAsync = _dummyDataFactoryClient.GetType()
                         .GetMethods(BindingFlags.Instance | BindingFlags.NonPublic)
                         .Where(m => m.Name == "ExecuteDataFactoryOperationAsync" && m.IsGenericMethod == true)
                         .First();
            var method_ExecuteDataFactoryOperationAsyn_int = method_ExecuteDataFactoryOperationAsync.MakeGenericMethod(typeof(int));

            Func<Task<int>> func = () => throw new CloudException()
            {
                Response = new HttpResponseMessageWrapper(new HttpResponseMessage(code), "")
            };
            var task = (Task<int>)method_ExecuteDataFactoryOperationAsyn_int.Invoke(_dummyDataFactoryClient, new object[] { func });
            var actualExp = Assert.ThrowsAsync<AuthenticationException>(async () => await task.ConfigureAwait(false));
            Assert.That(actualExp.Message, Does.Match(message));
        }

        [Test]
        public void TestExecuteDataFactoryOperationAsync_ForbiddenException_DataFactoryRetriableException()
        {
            var method_ExecuteDataFactoryOperationAsync = _dummyDataFactoryClient.GetType()
                         .GetMethods(BindingFlags.Instance | BindingFlags.NonPublic)
                         .Where(m => m.Name == "ExecuteDataFactoryOperationAsync" && m.IsGenericMethod == true)
                         .First();
            var method_ExecuteDataFactoryOperationAsyn_int = method_ExecuteDataFactoryOperationAsync.MakeGenericMethod(typeof(int));

            var cloudExp = new CloudException("dummy cloud exp")
            {
                Response = new HttpResponseMessageWrapper(new HttpResponseMessage(HttpStatusCode.BadGateway), "")
            };
            Func<Task<int>> func = () => throw cloudExp;
            var task = (Task<int>)method_ExecuteDataFactoryOperationAsyn_int.Invoke(_dummyDataFactoryClient, new object[] { func });
            var actualExp = Assert.ThrowsAsync<DataFactoryRetriableException>(async () => await task.ConfigureAwait(false));
            Assert.AreEqual("Transient error encountered in Data Factory operation", actualExp.Message);
        }

        [Test]
        public void TestExecuteDataFactoryOperationAsync_CloudException_CloudException()
        {
            var method_ExecuteDataFactoryOperationAsync = _dummyDataFactoryClient.GetType()
                         .GetMethods(BindingFlags.Instance | BindingFlags.NonPublic)
                         .Where(m => m.Name == "ExecuteDataFactoryOperationAsync" && m.IsGenericMethod == true)
                         .First();
            var method_ExecuteDataFactoryOperationAsyn_int = method_ExecuteDataFactoryOperationAsync.MakeGenericMethod(typeof(int));

            var cloudExp = new CloudException("dummy cloud exp")
            {
                Response = new HttpResponseMessageWrapper(new HttpResponseMessage(HttpStatusCode.Created), "")
            };
            Func<Task<int>> func = () => throw cloudExp;
            var task = (Task<int>)method_ExecuteDataFactoryOperationAsyn_int.Invoke(_dummyDataFactoryClient, new object[] { func });
            var actualExp = Assert.ThrowsAsync<CloudException>(async () => await task.ConfigureAwait(false));
            Assert.AreEqual(cloudExp.Message, actualExp.Message);
        }
    }
}
