using Microsoft.Aether.BlueBox.Clouds.Common;
using Microsoft.Aether.Clouds.CloudCommon.Cloudlets;
using Microsoft.Aether.Clouds.CloudCommon.JobQueueProcessing;
using Microsoft.Aether.DataTransfer.Cloud.Common.DataContracts;
using Microsoft.Aether.TaggedQueueManager.CloudTags;
using Microsoft.RelInfra.Instrumentation;

namespace Microsoft.Aether.DataTransferCloud
{
    public class DataTransferCloudJobsController : BaseJobController<DataTransferJobMetadata>
    {
        public DataTransferCloudJobsController(
            ITaggedJobsManager<AssignedQueueIdTags, DataTransferJobMetadata> taggedJobsManager, 
            CounterManager counters,
            JobQueue jobQueue)
            : base (taggedJobsManager, counters, jobQueue)
        {
        }
    }
}