using System;
using System.Collections.Generic;
using System.Net;
using System.Threading.Tasks;
using Microsoft.Aether.CloudCommon.Worker.JobQueueProcessing;
using Microsoft.Aether.Clouds.CloudCommon.Cloudlets;
using Microsoft.Aether.DataTransfer.Cloud.Common.DataContracts;
using Microsoft.Aether.TaggedQueueManager.CloudTags;
using Microsoft.AspNetCore.Mvc;
using Microsoft.RelInfra.Instrumentation;
using Microsoft.RelInfra.Instrumentation.Logging;

namespace Microsoft.Aether.DataTransferCloud
{
    public class DataTransferCloudDebugController : Controller
    {
        private readonly ITaggedJobsManager<AssignedQueueIdTags, DataTransferJobMetadata> _taggedJobsManager;
        private readonly CounterManager _counters;

        public DataTransferCloudDebugController(
            ITaggedJobsManager<AssignedQueueIdTags, DataTransferJobMetadata> taggedJobsManager, 
            CounterManager counters)
        {
            _taggedJobsManager = taggedJobsManager;
            _counters = counters;
        }

        [HttpGet]
        [Route("dtc/v1/debug/jobs")]
        public async Task<IActionResult> GetDebugJobsAsync()
        {
            _counters.GetRateCounter("GetDebugJobsRate").Increment();
            const string routePath = "dtc/v1/debug/jobs";
            try
            {
                using (new DisposableTimer(_counters.GetLatencyCounter("GetDebugJobsLatency")))
                {
                    IEnumerable<DataTransferJobMetadata> jobs = await _taggedJobsManager.GetAllJobsMetadataAsync();
                    _counters.GetRateCounter("GetDebugJobsSuccessRate").Increment();
                    CommonLogger.LogInfo("GET [{route_path}] Succeeded", routePath);
                    return Ok(jobs);
                }
            }
            catch (Exception e)
            {
                CommonLogger.LogWarning("GET [{route_path}] Failed. Error: {exception}", routePath, e);
                _counters.GetRateCounter("GetDebugJobsFailureRate").Increment();
                return new ContentResult
                {
                    Content = e.ToString(),
                    ContentType = "text/plain",
                    StatusCode = (int) HttpStatusCode.InternalServerError
                };
            }
        }

        [HttpGet]
        [Route("dtc/v1/debug/jobs")]
        public async Task<IActionResult> GetDebugJobAsync(string workspaceId, string id)
        {
            _counters.GetRateCounter("GetDebugJobRate").Increment();
            const string routePath = "dtc/v1/debug/jobs?id";
            try
            {
                using (new DisposableTimer(_counters.GetLatencyCounter("GetDebugJobLatency")))
                {
                    DataTransferJobMetadata job = await _taggedJobsManager.GetJobMetadataAsync(id, new DataContracts.Entity.WorkspaceIdentity { WorkspaceId = workspaceId });
                    _counters.GetRateCounter("GetDebugJobSuccessRate").Increment();
                    CommonLogger.LogEntityInfo(id, "GET [{route_path}] Succeeded", routePath);
                    return Ok(job);
                }
            }
            catch (StoredJobNotFoundException e)
            {
                CommonLogger.LogEntityWarning(id, "GET [{route_path}] Not found. Error: {exception}", routePath, e);
                _counters.GetRateCounter("GetDebugJobNotFoundFailureRate").Increment();
                return NotFound(e);
            }
            catch (Exception e)
            {
                CommonLogger.LogEntityError(id, "GET [{route_path}]  Failed. Error: {exception}", routePath, e);
                _counters.GetRateCounter("GetDebugJobFailureRate").Increment();
                return new ContentResult
                {
                    Content = e.ToString(),
                    ContentType = "text/plain",
                    StatusCode = (int) HttpStatusCode.InternalServerError
                };
            }
        }
    }
}