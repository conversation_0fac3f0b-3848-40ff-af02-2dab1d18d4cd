# Local debugging for DataTransferCloud

1. Open powershell in `vienna/src/aether`
2. Run `.\setup-devenv.ps1`
3. Run `az login` and `az acr login --name viennadroptest`
4. Run `localsetup datatransfercloud` to initialize the secret and the launchsetting for debugging
5. We could then run datatransfercloud service from Visual Studio or from Command Line:
   * From Visual Studio: Set `DataTransferCloudK8s.Service` project as startup project and press F5 for local debugging.
   * From command Line: Run `localrun datatransfercloud` to start the service in your powershell command line.