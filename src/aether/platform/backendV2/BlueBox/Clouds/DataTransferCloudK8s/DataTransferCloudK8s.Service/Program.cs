﻿using Aether.Backend.CacheClient;
using Microsoft.Aether.BackendCommon;
using Microsoft.Aether.BackendCommon.ClusterHealthPoller;
using Microsoft.Aether.BackendCommon.Configuration;
using Microsoft.Aether.BackendCommon.Time;
using Microsoft.Aether.BlueBox.AetherK8SBaseService;
using Microsoft.Aether.BlueBox.AetherK8SCommon;
using Microsoft.Aether.BlueBox.AetherK8SCommon.Auth;
using Microsoft.Aether.BlueBox.ArtifactClient;
using Microsoft.Aether.BlueBox.Clouds.Common;
using Microsoft.Aether.BlueBox.DataStoreClient;
using Microsoft.Aether.BlueBox.RunHistoryClient;
using Microsoft.Aether.BlueBox.S2S.Common;
using Microsoft.Aether.BlueBox.SnapshotClient;
using Microsoft.Aether.BlueBox.Storage;
using Microsoft.Aether.BlueBox.Web.Common;
using Microsoft.Aether.BlueBox.WorkspaceResourcesClient;
using Microsoft.Aether.CloudCommon.Worker;
using Microsoft.Aether.CloudCommon.Worker.Cache;
using Microsoft.Aether.Clouds.CloudCommon;
using Microsoft.Aether.Clouds.CloudCommon.Cloudlets;
using Microsoft.Aether.Clouds.CloudCommon.JobQueueProcessing;
using Microsoft.Aether.Clouds.CloudCommon.JobQueueProcessing.QueueRetrieving;
using Microsoft.Aether.Clouds.CloudCommon.WebNetCore;
using Microsoft.Aether.DataTransfer.Cloud.Common;
using Microsoft.Aether.DataTransfer.Cloud.Common.DataContracts;
using Microsoft.Aether.DataTransferCloud.JobProcessing;
using Microsoft.Aether.S2S.Common;
using Microsoft.Aether.StorageDescription;
using Microsoft.Aether.TaggedQueueManager;
using Microsoft.Aether.TaggedQueueManager.CloudTags;
using Microsoft.ApplicationInsights;
using Microsoft.AspNetCore;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.MachineLearning.Common.WebApi.AppInsights;
using Microsoft.MachineLearning.Dataset.Client;
using Microsoft.MachineLearning.Pipeline.Common;
using Microsoft.MachineLearning.PolicyServiceClient;
using Microsoft.RelInfra.Common;
using Microsoft.RelInfra.Common.Configuration;
using Microsoft.RelInfra.Common.Exceptions;
using Microsoft.RelInfra.Common.RetryExecution.Policy;
using Microsoft.RelInfra.Instrumentation;
using Microsoft.RelInfra.Instrumentation.Logging;
using Microsoft.RelInfra.Storage;
using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.IO;
using System.Net;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;

namespace Microsoft.Aether.DataTransferCloud
{
    [ExcludeFromCodeCoverage]
    class DataTransferCloudService
    {
        private const string ServiceName = ServiceNames.DataTransferCloud;
        private const string configFileName = "DataTransferCloud.ini";
        private const string sharedConfigFilename = "SharedSettings.ini";
        private const string BBSharedConfigFileName = "BBSharedSettings.ini";
        private const string DefaultIndexPolicyFile = "cloudletindexpolicy.json";
        private const string DataDirectoryVariable = "DataDirectory";

        static void Main()
        {
            SecretsHelper.PrepareSecretsForDevEnvironment();
            IWebHostBuilder hostBuilder = WebHost.CreateDefaultBuilder()
                .UseStartup<DataTransferCloudService>()
                .UseKestrel(options =>
                {
                    var cfg = options.ApplicationServices.GetService<IRelInfraConfiguration>();
                    //Used to be MaxReceivedMessageSize. Seem to both by in bytes.
                    options.Limits.MaxRequestBodySize = cfg.GetInteger("Server.MaxReceivedMessageSize");
                    //just pull out the port. Do we care
                    var url = new Uri(cfg.GetString("Server.BaseAddress"));
                    options.Listen(IPAddress.Any, url.Port, listenOptions =>
                    {
                        CommonLogger.LogEntityInfo(ServiceName, $"Port: {url.Port}");
                        K8DataTransferCloudConfig k8DataTransferCloudConfig = options.ApplicationServices.GetService<K8DataTransferCloudConfig>();
                        if (url.Scheme == Uri.UriSchemeHttps)
                        {
                            listenOptions.UseHttps(fileName: k8DataTransferCloudConfig.GetSslCert(), password: string.Empty);
                        }
                    });
                    //https://github.com/aspnet/KestrelHttpServer/blob/rel/2.0.0/src/Microsoft.AspNetCore.Server.Kestrel.Core/KestrelServerLimits.cs
                    //don't seem to have an equivalent of SendTimeout = TimeSpan.FromSeconds(300),
                    //could use MaxResponseBufferSize or KeepAliveTimeout but I don't think that's what we want.
                    //https://github.com/aspnet/KestrelHttpServer/issues/611
                })
                .ConfigureLogging(builder => builder.ClearProviders().AddCommonLogger());

            hostBuilder.Build()
                .Run();
        }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            StartupAsync(services).Wait();
        }
        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app)
        {
            //http://www.talkingdotnet.com/how-to-enable-gzip-compression-in-asp-net-core/
            app.UseResponseCompression();
            app.UseMvc();
        }

        static async Task StartupAsync(IServiceCollection services)
        {
            try
            {
                string dataDirectory = Environment.GetEnvironmentVariable(DataDirectoryVariable);
                string machineName = Environment.MachineName;

                var k8sService = new K8sBaseService(ServiceName, configFilename: configFileName,
                    iniFileNames: new[] { sharedConfigFilename, BBSharedConfigFileName });
                k8sService.InitializeAsync().Wait();

                var cloudletConfig = new CloudletConfig(k8sService.ApConfig, k8sService.SecretProvider);
                cloudletConfig.VerifyConfig();

                CounterManager counters = k8sService.CounterManager;

                var storageDescriptions = new StorageDescriptions();
                var legacyStorageInitializer = new LegacyStorageInitializer();
                await legacyStorageInitializer.InitializeLegacyStoragesForCloudletAsync(cloudletConfig, storageDescriptions.DataTransferCloud);

                services.ConfigureAppInsightsTargetFilter(new HashSet<string>() {
                    $"{Env.StorageAccountNameCloudlet()}{Env.StorageQueueEndpoint()}",
                    $"{Env.StorageAccountNameCloudletQueue()}{Env.StorageQueueEndpoint()}",
                });

                services.ConfigureBasicServices(serviceName: ServiceName, baseService: k8sService, configuration: null);

                AppDomain.CurrentDomain.UnhandledException += (sender, callerArgs) =>
                {
                    Exception e = (Exception)callerArgs.ExceptionObject;
                    CommonLogger.LogEntityError(ServiceName, "Unhandled error: {safe_exception_message} and stack trace: {stack_trace}", e.Message, e.StackTrace);
                    Thread.Sleep(5 * 1000);
                };

                var dtConfig = new K8DataTransferCloudConfig(k8sService.ApConfig, k8sService.SecretProvider);
                dtConfig.VerifyConfig();

                MemoryCache memoryCache = new MemoryCache(new MemoryCacheOptions());

                var clusterHealthPoller = new ClusterHealthPoller();
                IS2SArtifactClient artifactClient = new S2SArtifactSvcClient(
                    config: new ArtifactConfig(k8sService.ApConfig, ServiceName),
                    servicePrincipalTokenProvider: k8sService.ServicePrincipalAadTokenProvider,
                    retryPolicy: new ExponentialBackoffRetryPolicy(2),
                    retryOnTooManyRequestsException: true,
                    counters: k8sService.CounterManager,
                    clusterHealthPoller: clusterHealthPoller);

                var amlSnapshotConfig = new AmlSnapshotConfig(k8sService.ApConfig);
                var snapshotClient = new SnapshotClient(amlSnapshotConfig, k8sService.ServicePrincipalAadTokenProvider, k8sService.CounterManager, clusterHealthPoller);

                var workspaceResourcesConfig = new WorkspaceResourcesConfig(k8sService.ApConfig);
                var workspaceResourcesClient = new WorkspaceResourcesSvcClient(workspaceResourcesConfig, k8sService.ServicePrincipalAadTokenProvider, k8sService.CounterManager, clusterHealthPoller);
                var workspaceResourcesCache = new WorkspaceResourcesCache(distributedCache: null, memoryCache: memoryCache, workspaceResourcesClient: workspaceResourcesClient, k8sService.CounterManager);

                IModuleResourceManagerFactory moduleResourceManagerFactory =
                    await BlueBoxModuleResourceManagerFactory.CreateFactoryAsync(
                        dataDirectory: dataDirectory,
                        counters: counters,
                        serviceConfig: dtConfig.GetRelInfraConfiguration(),
                        maxInputSize: dtConfig.MaxInputSize,
                        maxModuleSize: dtConfig.MaxModuleSize,
                        workspaceResourcesCache: workspaceResourcesCache,
                        artifactClient: artifactClient,
                        snapshotClient: snapshotClient,
                        cacheReporter: new DummyCacheRepositoryReporter()).ConfigureAwait(false);

                var artifactPrefix = Env.AzurePrefix();
                CommonLogger.LogInfo($"Azure table prefix is {artifactPrefix}");

                var workloadIdentityAvailable = k8sService.WorkloadIdentityProvider?.IsAvailable ?? false;
                CommonLogger.LogInfo($"Workload identity available: {workloadIdentityAvailable}");
                IStorageObjectFactory storageObjectFactory;
                if (workloadIdentityAvailable)
                {
                    storageObjectFactory = new StorageObjectFactoryV2(k8sService.CounterManager, k8sService.WorkloadIdentityProvider);
                }
                else
                {
                    storageObjectFactory = new StorageObjectFactory(k8sService.CounterManager);
                }

                storageObjectFactory.LoadProvisionedStoragesFromFile(StorageObjectFactory.ProvisionedStoragesFileName);

                CosmosDbConnectionInfo cosmosDbConnectionInfo = storageObjectFactory.GetCosmosDbConnectionInfo(connectionName: storageDescriptions.CloudletsShared.SharedCosmosDb);

                var indexPolicy = CosmosDbIndexingPolicyHelper.LoadIndexingPolicyFromFileV2(DefaultIndexPolicyFile);

                BbMetaStorageConnectionProvider defaultJobStorageConnectionProvider = new BbMetaStorageConnectionProvider(cosmosDbConnectionInfo);
                var workspaceJobStorageProvider = new BlueBoxUserCosmosDbStorageProviderV2(databaseName: dtConfig.JobStorageDatabaseName,
                    containerName: artifactPrefix + dtConfig.JobStorageTableName,
                    partitionKeyPath: "/id",
                    workloadIdentityProvider: k8sService.WorkloadIdentityProvider,
                    cache: memoryCache, cacheExpiration: dtConfig.WorkspaceJobStorageCacheDuration,
                    defaultConnectionProvider: defaultJobStorageConnectionProvider, counterManager: counters,
                    defaultTimeToLive: TimeSpan.FromDays(60),
                    indexPolicy: indexPolicy,
                    cosmosDbOptions: cloudletConfig.GetCosmosDbOptions());
                await workspaceJobStorageProvider.InitDefaultDbStorageAsync();
                var jobStorage = new JobMetadataDbStorage<DataTransferJobMetadata>(workspaceJobStorageProvider);

                var amlDataStoreConfig = new AmlDataStoreConfig(k8sService.ApConfig);
                var dataStoreClient = new DataStoreSvcClient(amlDataStoreConfig, k8sService.ServicePrincipalAadTokenProvider, k8sService.CounterManager, clusterHealthPoller);

                var amlWorkspaceMsiServiceConfig = new AmlWorkspaceMsiServiceConfig(k8sService.ApConfig);
                var msiTokenProvider = new AmlWorkspaceMsiTokenProvider(amlWorkspaceMsiServiceConfig, k8sService.ServicePrincipalAadTokenProvider, clusterHealthPoller);

                var amlRunMetricConfig = new AmlRunMetricServiceConfig(k8sService.ApConfig);
                var amlRunMetricClient = new AmlRunMetricClient(amlRunMetricConfig, k8sService.ServicePrincipalAadTokenProvider, k8sService.CounterManager);
                var amlRunHistoryConfig = new AmlRunHistoryConfig(k8sService.ApConfig);
                var runHistoryClient = new RunHistorySvcClient(amlRunHistoryConfig, k8sService.ServicePrincipalAadTokenProvider, amlRunMetricClient, k8sService.CounterManager, clusterHealthPoller);
                var runStateUpdater = new RunStateUpdater(runHistoryClient, counters);

                IAzureMachineLearningDatasetManagementClientFactory datasetClientFactory = new AzureMachineLearningDatasetManagementSvcClientFactory(
                    handler: new MessageHandlerWithAuth(k8sService.ServicePrincipalAadTokenProvider),
                    config: new DatasetConfig(k8sService.ApConfig),
                    poller: clusterHealthPoller);

                var policyServiceConfig = new PolicyServiceClientConfig(k8sService.ApConfig);
                var policyServiceClient = new PolicyServiceClient(
                    config: policyServiceConfig,
                    tokenProvider: k8sService.ServicePrincipalAadTokenProvider,
                    counters: k8sService.CounterManager,
                    clusterHealthPoller);

                RedisCacheConfiguration redisConfig = new RedisCacheConfiguration(k8sService.ApConfig, k8sService.SecretProvider);
                var redisCache = await AetherRedisCache.CreateAsync(redisConfig, counters);
                var jobStatusCache = new JobStatusCache(redisCache, redisConfig.ValidityPeriod);
                string path = Path.Combine(Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location), @"error_config.json");
                var errorClassifier = new ErrorClassifier(path, AMLServiceTeams.DataTransferCloud);
                var amlClientFactory = new AmlSvcClientFactory(k8sService, clusterHealthPoller);
                var jobProcessorFactory = new JobProcessorFactory(
                    config: dtConfig,
                    moduleResourceManagerFactory: moduleResourceManagerFactory,
                    dataDir: dataDirectory,
                    jobStorage: jobStorage,
                    counterManager: counters,
                    runStateUpdater: runStateUpdater,
                    amlWorkspaceMsiTokenProvider: msiTokenProvider,
                    dataStoreClient: dataStoreClient,
                    s2STokenProvider: k8sService.ServicePrincipalAadTokenProvider,
                    jobStdOutLoggerFactory: new MsiJobStdOutLoggerFactory(workspaceResourcesCache, counters),
                    workspaceResourcesClient: workspaceResourcesClient,
                    jobStatusCache: jobStatusCache,
                    policyServiceClient: policyServiceClient,
                    datasetClientFactory: datasetClientFactory,
                    runHistoryClient: runHistoryClient,
                    errorClassifier: errorClassifier,
                    amlClientFactory: amlClientFactory);

                var lockStorage = new AzureBlobLeasableContainerStorageV2(storageObjectFactory.GetAzureBlobContainer(storageDescriptions.DataTransferCloud.CloudletLocks));

                ITaggedQueueManager<AssignedQueueIdTags, string> queueManager;
                if (workloadIdentityAvailable)
                {
                    var serviceUri = Env.GetQueueStorageEndpointUri(Env.StorageAccountNameCloudletQueue());
                    queueManager = new AzureTaggedQueueManagerV2<AssignedQueueIdTags, string>(
                        serviceUri: serviceUri,
                        credential: k8sService.WorkloadIdentityProvider.Credential,
                        queuePrefix: artifactPrefix + dtConfig.AzureQueueManagerPrefix,
                        timerFactory: new TimerFactory(),
                        updateTimeSpan: dtConfig.AzureQueueManagerUpdatePeriod);
                }
                else
                {
                    var queueConnectionstring = storageObjectFactory.GetAzureConnectionString(connectionName: storageDescriptions.DataTransferCloud.QueueAzureStorage);
                    queueManager = new AzureTaggedQueueManager<AssignedQueueIdTags, string>(
                        connectionString: queueConnectionstring,
                        queuePrefix: artifactPrefix + dtConfig.AzureQueueManagerPrefix,
                        timerFactory: new TimerFactory(),
                        updateTimeSpan: dtConfig.AzureQueueManagerUpdatePeriod);
                }

                await queueManager.InitializeAsync();

                string cloudNameTablePrefix = "Dtc";
                CloudJobQueueTracker<DataTransferJobMetadata> jobQueueTracker;
                if (workloadIdentityAvailable)
                {
                    var storageAccountUri = Env.GetTableStorageEndpointUri(Env.StorageAccountNameCloudlet());
                    jobQueueTracker = new CloudJobQueueTracker<DataTransferJobMetadata>(
                        storageAccountUri: storageAccountUri,
                        credential: k8sService.WorkloadIdentityProvider.Credential,
                        machineName: machineName,
                        dataDirectory: dataDirectory,
                        cloudName: cloudNameTablePrefix,
                        jobQueueName: queueManager.QueuePrefix,
                        counters: k8sService.CounterManager);
                }
                else
                {
                    var storageConnectionstring = storageObjectFactory.GetAzureConnectionString(connectionName: storageDescriptions.DataTransferCloud.SharedAzureStorage);
                    jobQueueTracker = new CloudJobQueueTracker<DataTransferJobMetadata>(
                        azureConnectionString: storageConnectionstring,
                        machineName: machineName,
                        dataDirectory: dataDirectory,
                        cloudName: cloudNameTablePrefix,
                        jobQueueName: queueManager.QueuePrefix,
                        counters: k8sService.CounterManager);
                }

                var queueMessageRetriever = new TaggedQueueManagerMessageRetriever<AssignedQueueIdTags, string>(
                    taggedQueueManager: queueManager,
                    tagDescriptor: new AssignedQueueIdDescriptor(),
                    counterManager: counters);

                var taggedJobQueueManager = new TaggedJobQueueManager<AssignedQueueIdTags, DataTransferJobMetadata>(
                    config: cloudletConfig.JobQueueManagerConfig,
                    counters: counters,
                    queueMessageRetriever: queueMessageRetriever,
                    lockStorage: lockStorage,
                    jobStorage: jobStorage,
                    jobProcessorFactory: jobProcessorFactory,
                    jobQueueTracker: jobQueueTracker);

                var commonCounterManager = await k8sService.InitializeCommonCounterAsync();
                var jobQueue = new JobQueue(taggedJobQueueManager, k8sService.ApConfig, commonCounterManager);
                jobQueue.ProcessQueueAsync(CancellationToken.None).FireAndForget();

                var taggedJobsManager = new TaggedJobsManager<AssignedQueueIdTags, DataTransferJobMetadata>(
                    jobMetadataStorage: jobStorage,
                    taggedQueueManager: queueManager,
                    outputSource: new NAJobsManagerOutputSource(),
                    cloudletJobMetadataFactory: new DataTransferJobMetadataFactory(),
                    jobStatusCache: jobStatusCache);

                services.AddSingleton(k8sService.WorkloadIdentityProvider);
                services.AddSingleton<IAmlSvcClientFactory>(amlClientFactory);
                services.AddSingleton<IRelInfraConfiguration>(k8sService.ApConfig);
                services.AddSingleton(dtConfig);
                services.AddSingleton<CounterManager>(counters);
                services.AddSingleton<ITaggedJobsManager<AssignedQueueIdTags, DataTransferJobMetadata>>(taggedJobsManager);
                services.AddSingleton(jobQueue);

                services.AddMvc(options =>
                {
                    options.EnableEndpointRouting = false;
                    if (Env.DeploymentType() != "onebox")
                    {
                        options.AddCommonAppInsightsComponents();
                        options.Filters.Add(new S2sAuthorizeAttribute(k8sService.S2sAuthorizer, keepAlivePath: "/keepalive"));
                    }
                    options.Filters.Add(new CloudletExceptionFilter());
                });

                var telemetryClient = services.BuildServiceProvider().GetRequiredService<TelemetryClient>();
                // Update telemetry client here since telemetry client is updated after the queue manager created
                taggedJobQueueManager.UpdateTelemetryClient(telemetryClient);
            }
            catch (ServiceInvocationException e)
            {
                CommonLogger.LogEntityError(ServiceName, "Unhandled Exception: {safe_exception_message}, stack trace: {stack_trace}", e.Message, e.StackTrace);
                throw;
            }
        }
    }
}
