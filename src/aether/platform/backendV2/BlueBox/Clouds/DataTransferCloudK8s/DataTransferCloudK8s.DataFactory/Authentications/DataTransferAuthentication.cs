﻿using System;
using System.Collections.Generic;
using System.Security.Authentication;
using System.Text;

namespace Microsoft.Aether.DataTransferCloud.DataFactory.Authentications
{
    public class DataTransferAuthentication
    {
        public AuthenticationTypes AuthenticationType { get; }
  
        public string AccountKey { get; }

        public string ServicePrincipalId { get; }
        public string ServicePrincipalKey { get; }
        public string ServicePrincipalTenant { get; }

        public string SasToken { get; }

        public string UserId { get; }
        public string UserPassword { get; }


        private DataTransferAuthentication(DataTransferAuthenticationBuilder builder)
        {
            this.AuthenticationType = builder.AuthenticationType;
            this.AccountKey = builder.AccountKey;
            this.ServicePrincipalId = builder.ServicePrincipalId;
            this.ServicePrincipalKey = builder.ServicePrincipalKey;
            this.ServicePrincipalTenant = builder.ServicePrincipalTenant;
            this.SasToken = builder.SasToken; 
            this.UserId = builder.UserId;
            this.UserPassword = builder.UserPassword;
        }

        public override string ToString()
        {
            return $"AuthenticationType={AuthenticationType.ToString("G")}";
        }


        public class DataTransferAuthenticationBuilder
        {
            public AuthenticationTypes AuthenticationType { get; private set; }

            public string AccountKey { get; private set; }

            public string ServicePrincipalId { get; private set; }
            public string ServicePrincipalKey { get; private set; }
            public string ServicePrincipalTenant { get; private set; }

            public string SasToken { get; private set; }

            public string UserId { get; private set; }
            public string UserPassword { get; private set; }

            public DataTransferAuthenticationBuilder(AuthenticationTypes type)
            {
                AuthenticationType = type;
            }

            public DataTransferAuthentication build()
            {
                DataTransferAuthentication auth = new DataTransferAuthentication(this);
                ValidateDataTransferAuthentication(auth);
                return auth;
            }

            private void ValidateDataTransferAuthentication(DataTransferAuthentication auth)
            {
                switch (auth.AuthenticationType)
                {
                    case AuthenticationTypes.Sas:
                        {
                            if (string.IsNullOrEmpty(SasToken))
                            {
                                throw new AuthenticationException("Invalid authentication input: SasToken");
                            }
                            return;
                        }
                    case AuthenticationTypes.AccountKey:
                        {
                            if (string.IsNullOrEmpty(AccountKey))
                            {
                                throw new AuthenticationException("Invalid authentication input: AccountKey");
                            }
                            return;
                        }
                    case AuthenticationTypes.ServicePrincipal:
                        {
                            if (!Guid.TryParse(ServicePrincipalId, out Guid id) || string.IsNullOrEmpty(ServicePrincipalKey) || !Guid.TryParse(ServicePrincipalTenant, out Guid tenant))
                            {
                                throw new AuthenticationException($"Invalid authentication input: ServicePrincipalId/ServicePrincipalKey/ServicePrincipalTenant");
                            }
                            return;
                        }
                    case AuthenticationTypes.SqlAuthentication:
                        {
                            if (string.IsNullOrEmpty(UserId) || string.IsNullOrEmpty(UserPassword))
                            {
                                throw new AuthenticationException($"Invalid authentication input: UserId/UserPassword");
                            }
                            return;
                        }
                    case AuthenticationTypes.MSI:
                        {
                            return;
                        }
                    default:
                        throw new AuthenticationException($"Does not supported AuthenticationTypes={auth.AuthenticationType}");
                }
            }

            public DataTransferAuthenticationBuilder SetAccountKey(string accountKey)
            {
                this.AccountKey = accountKey;
                return this;
            }

            public DataTransferAuthenticationBuilder SetServicePrincipalId(string servicePrincipalId)
            {
                this.ServicePrincipalId = servicePrincipalId;
                return this;
            }

            public DataTransferAuthenticationBuilder SetServicePrincipalKey(string servicePrincipalKey)
            {
                this.ServicePrincipalKey = servicePrincipalKey;
                return this;
            }

            public DataTransferAuthenticationBuilder SetServicePrincipalTenant(string servicePrincipalTenant)
            {
                this.ServicePrincipalTenant = servicePrincipalTenant;
                return this;
            }

            public DataTransferAuthenticationBuilder SetSasToken(string sasToken)
            {
                this.SasToken = sasToken;
                return this;
            }

            public DataTransferAuthenticationBuilder SetUserId(string userId)
            {
                this.UserId = userId;
                return this;
            }

            public DataTransferAuthenticationBuilder SetUserPassword(string userPassword)
            {
                this.UserPassword = userPassword;
                return this;
            }
        }
    }
}
