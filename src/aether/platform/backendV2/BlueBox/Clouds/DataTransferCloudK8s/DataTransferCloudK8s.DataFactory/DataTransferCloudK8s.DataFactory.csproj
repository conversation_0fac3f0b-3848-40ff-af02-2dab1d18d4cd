<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <RootNamespace>Microsoft.Aether.DataTransferCloud.DataFactory</RootNamespace>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.Azure.Management.DataFactory" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\..\..\..\RelInfra\RelInfra.Common.Library\src\Common\RelInfra.Common.csproj" />
    <ProjectReference Include="..\..\..\..\..\RelInfra\RelInfra.Common.Library\src\Extensions\RelInfra.Extensions.csproj" />
    <ProjectReference Include="..\..\..\..\..\RelInfra\RelInfra.Instrumentation.Library\src\Microsoft.RelInfra.Instrumentation.csproj" />
    <ProjectReference Include="..\..\..\..\shared\Microsoft.Aether.BackendCommon\Microsoft.Aether.BackendCommon.csproj" />
  </ItemGroup>
</Project>
