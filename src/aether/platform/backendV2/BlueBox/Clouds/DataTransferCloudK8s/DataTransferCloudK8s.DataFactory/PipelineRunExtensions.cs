﻿using Microsoft.Azure.Management.DataFactory.Models;
using System;
using System.Runtime.CompilerServices;

[assembly: InternalsVisibleTo("Microsoft.Aether.DataTransferCloud.DataFactory.Test")]
namespace Microsoft.Aether.DataTransferCloud.DataFactory
{
    static class PipelineRunExtensions
    {
        internal static DataFactoryOperationStatus GetStatus(this PipelineRun pipelineRun)
        {
            // Map ADF run status to DataTransfer cloud internal status 
            // Doc : https://learn.microsoft.com/en-us/azure/data-factory/monitor-programmatically#pipeline-run-information
            string status = pipelineRun.Status;

            if (Enum.TryParse(status, ignoreCase: true, out DataFactoryOperationStatus operationStatus))
            {
                return operationStatus;
            }
            else
            {
                throw new NotSupportedException($"Unrecognized ADF run status : {status}");
            }
        }
    }
}
