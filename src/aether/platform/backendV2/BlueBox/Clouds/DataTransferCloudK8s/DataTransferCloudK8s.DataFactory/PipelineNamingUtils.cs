﻿using System;
using System.Text;

namespace Microsoft.Aether.DataTransferCloud.DataFactory
{
    static class PipelineNamingUtils
    {
        internal static string CreateUniquePipelineName()
        {
            // Using 32 random letters, as Guid does not seem to work.
            return CreateUniqueString(length: 32);
        }

        internal static string GetInputDatasetName(string pipelineName)
        {
            return pipelineName + "inputdataset";
        }

        internal static string GetOutputDatasetName(string pipelineName)
        {
            return pipelineName + "outputdataset";
        }

        internal static string GetLinkedServiceName(string datasetName)
        {
            return datasetName + "linkedservice";
        }

        internal static string CreateUniqueString(uint length)
        {
            StringBuilder value = new StringBuilder();
            Random rand = new Random((int)DateTime.Now.Ticks);

            for (int i = 0; i < length; ++i)
            {
                value.Append((char)('a' + rand.Next(0, 26)));
            }

            return value.ToString();
        }
    }
}
