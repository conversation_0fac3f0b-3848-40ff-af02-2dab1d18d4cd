﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Azure.Management.DataFactory" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\..\..\shared\Microsoft.Aether.TokenProvider\Microsoft.Aether.TokenProvider.csproj" />
    <ProjectReference Include="..\DataTransferCloudK8s.CopyService\DataTransferCloudK8s.CopyService.csproj" />
    <ProjectReference Include="..\DataTransferCloudK8s.DataFactory\DataTransferCloudK8s.DataFactory.csproj" />
  </ItemGroup>
</Project>