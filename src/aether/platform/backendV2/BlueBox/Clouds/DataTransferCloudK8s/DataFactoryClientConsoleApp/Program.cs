﻿using Microsoft.Aether.BackendCommon.Configuration;
using Microsoft.Aether.BlueBox.DataStoreClient.Contracts;
using Microsoft.Aether.DataTransferCloud.CopyService;
using Microsoft.Aether.DataTransferCloud.DataFactory;
using Microsoft.Aether.DataTransferCloud.DataFactory.Authentications;
using Microsoft.Aether.DataTransferCloud.DataFactory.Resources;
using Microsoft.Aether.TokenProvider;
using Microsoft.Azure.Management.DataFactory;
using Microsoft.Azure.Management.DataFactory.Models;
using Microsoft.RelInfra.Common;
using Microsoft.RelInfra.Common.Serialization;
using Microsoft.Rest;
using Microsoft.Rest.Azure;
using ITokenProvider = Microsoft.Aether.TokenProvider.ITokenProvider;

namespace DataFactoryClientConsoleApp
{
    public class Program
    {
        private Guid subscriptionId = new Guid(g: "b8c23406-f9b5-4ccb-8a65-a8cb5dcd6a5a");
        private string resourceGroupName = "rge2etests";
        private string dataFactoryName = "adftestb3a13496e35d";
        private string integrationRuntimeName = "integrationRuntime-chw";

        private DataFactoryClient _dataFactoryClient;
        private ITokenProvider _azureInteractiveTokenProvider;
        private ITokenProvider _keyVaultTokenProvider;
        private IKeyVaultClient _keyVaultClient;
        private readonly string TEST_KEY_VAULT_NAME = "aether-tests";
        private readonly string DataTransfer_PrivateBlob_AccountKey_SecretName = "DataTransferTest-Privateblob2AccountKey";


        public Program()
        {
            _azureInteractiveTokenProvider = new InteractiveTokenProvider(tenantId: AdalConstants.MicrosoftTenantId, audience: AdalConstants.AzureManagementUrl);
            _keyVaultTokenProvider = new InteractiveTokenProvider(tenantId: AdalConstants.MicrosoftTenantId, audience: AdalConstants.KeyVaultResource);

            _dataFactoryClient = new DataFactoryClient(
                loggingId: Guid.NewGuid().ToString(),
                dataFactorySubscriptionId: subscriptionId.ToString(),
                dataFactoryResourceGroup: resourceGroupName,
                dataFactoryName: dataFactoryName,
                dataFactoryAuthToken: _azureInteractiveTokenProvider.GetAccessTokenAsync().Result
           );

            _keyVaultClient = new AzureTokenHelperKeyVaultClient(TEST_KEY_VAULT_NAME, _keyVaultTokenProvider);
        }

        static async Task Main()
        {
            Program program = new Program();
            await program.TestADF();
            //await program.TestGetIntegrationRuntimeByName();
            //await program.TestGetFirstIntegrationRuntimeWithVirtualNetwork();
            //await program.Testkeyvalut3();
        }

        public async Task TestADF()
        {
            var token = await _azureInteractiveTokenProvider.GetAccessTokenAsync();
            using (var client = CreateDataFactoryManagementClient(token))
            {
                //Factory factory = await client.Factories.GetAsync(resourceGroupName, "adftestb3a13496e35d");
                IList<IPipelineResource> sources = new List<IPipelineResource>() { await CreateSourcePipelineResource() };
                IPipelineResource sink = await CreateSinkPipelineResource();
                DataFactoryOperationEntity entity = await _dataFactoryClient.StartCopyOperationAsync(sources, sink);

                Console.WriteLine("PipelineName = " + entity.PipelineName);
                Console.WriteLine("PipelineId = " + entity.PipelineRunId);
            }

        }

        public async Task TestGetIntegrationRuntimeByName()
        {
            var token = await _azureInteractiveTokenProvider.GetAccessTokenAsync();
            using (var client = CreateDataFactoryManagementClient(token))
            {
                IntegrationRuntimeResource resource = await client.IntegrationRuntimes.GetAsync(resourceGroupName, dataFactoryName, integrationRuntimeName);
                /*  IntegrationRuntimeResource structure
                 * {
                  "properties": {
                    "typeProperties.computeProperties": {
                      "location": "East US",
                      "dataFlowProperties": {
                        "computeType": "General",
                        "coreCount": 8,
                        "timeToLive": 10,
                        "cleanup": false
                      }
                    },
                    "managedVirtualNetwork": {
                      "referenceName": "default",
                      "type": "ManagedVirtualNetworkReference"
                    },
                    "typeProperties": {
                      "computeProperties": {
                        "location": "East US",
                        "dataFlowProperties": {
                          "computeType": "General",
                          "coreCount": 8,
                          "timeToLive": 10,
                          "cleanup": false
                        }
                      }
                    }
                  },
                  "id": "/subscriptions/b8c23406-f9b5-4ccb-8a65-a8cb5dcd6a5a/resourceGroups/rge2etests/providers/Microsoft.DataFactory/factories/adftestb3a13496e35d/integrationruntimes/integrationRuntime-chw",
                  "name": "integrationRuntime-chw",
                  "type": "Microsoft.DataFactory/factories/integrationruntimes",
                }
                 */
                Console.WriteLine($"Name: {resource.Name}");
            }
        }

        public async Task TestGetFirstIntegrationRuntimeWithVirtualNetwork()
        {
            var token = await _azureInteractiveTokenProvider.GetAccessTokenAsync();
            using (var client = CreateDataFactoryManagementClient(token))
            {
                IPage<IntegrationRuntimeResource> resources = await client.IntegrationRuntimes.ListByFactoryAsync(resourceGroupName, dataFactoryName);
                foreach (IntegrationRuntimeResource resource in resources)
                {
                    var managedIntegrationRunTime = resource.Properties as ManagedIntegrationRuntime;
                    if (managedIntegrationRunTime != null)
                    {
                        Console.WriteLine($"Found it: {SerializationHelpers.SerializeEntity<IntegrationRuntimeResource>(resource)}");
                    }
                }
            }
        }


        private async Task<AzureBlobPipelineResource> CreateSourcePipelineResource()
        {
            var accountKey =  await _keyVaultClient.GetSecretValueAsync(DataTransfer_PrivateBlob_AccountKey_SecretName);
            Console.WriteLine("accountkey="+accountKey);
            AzureStorageDto blobDto = new AzureStorageDto()
            {
                AccountName = "wse2etestsdtblobprivate2",
                SubscriptionId = subscriptionId,
                CredentialType = CredentialTypes.AccountKey,
                IsSas = false,
                Credential = accountKey,
                AccountKey = accountKey

            };
            DataTransferAuthentication auth = DataTransferAuthenticationFactory.GetBlobAuthentication(blobDto);
            return new AzureBlobPipelineResource(
                accountName: blobDto.AccountName,
                authentication: auth,
                folderPath: "test-data-transfer/test_private_endpoint/",
                fileName: "emp.txt",
                copyAsText: true);
        }

        private async Task<AzureBlobPipelineResource> CreateSinkPipelineResource()
        {
            var accountKey = await _keyVaultClient.GetSecretValueAsync(DataTransfer_PrivateBlob_AccountKey_SecretName);
            AzureStorageDto blobDto = new AzureStorageDto()
            {
                AccountName = "wse2etestsdtblobprivate",
                SubscriptionId = subscriptionId,
                CredentialType = CredentialTypes.AccountKey,
                IsSas = false,
                Credential = accountKey,
                AccountKey = accountKey

            };
            DataTransferAuthentication auth = DataTransferAuthenticationFactory.GetBlobAuthentication(blobDto);
            return new AzureBlobPipelineResource(
                accountName: blobDto.AccountName,
                authentication: auth,
                folderPath: "test-data-transfer/",
                fileName: "",
                copyAsText: true);
        }

        private IDataFactoryManagementClient CreateDataFactoryManagementClient(string token)
        {
            return new DataFactoryManagementClient(new TokenCredentials(token))
            {
                SubscriptionId = subscriptionId.ToString(),
                BaseUri = new Uri(Env.ResourceManagerUrl())
            };

        }

    }
}
