﻿{
  "ApCloudService": {
    "ApCloudProxy": {
      "ApCloudProxyUri": "https://apcloudproxy.dev.aetherclouds.ms/",
      "AetherSnapshotCosmosRoot": "https://cosmos09.osdinfra.net/cosmos/searchRelevance.aether.test/local/public2/modules/"
    },
    "ExePoolModuleAllowList": {
      "ModuleIds": [
        "9390d3b4-ee74-4dc4-b06d-3c3c6b4d3117",
        "3b624e83-b371-4ff8-bf60-aebce7a457ad",
        "8c69b79c-9193-4995-ac8a-c63c36cbe2f4",
        "c231ec4e-b467-4825-bc9c-974a75652d17",
        "259eee39-1ce6-41f9-851a-d48eeb34b0eb",
        "e4010928-4e8a-495c-aefb-c188b3851ca3",
        "99e6e7e3-e413-482c-859c-9b7d87b387d5",
        "83b5abda-c052-4fdd-8dd5-1f2ed39314ed",
        "648f1224-7cda-43d0-8212-20a9c32f8500",
        "ae336a2d-9486-4755-ab12-258b9a613ea7",
        "13e8b566-1dae-41e5-b62a-c24e1bd8843c",
        "5e37aba9-7285-4ed2-b11d-cbe784f2b59b",
        "41234a99-687a-481d-9ae7-0273c67c3baf",
        "5f0c179e-6d55-4439-880d-97a4582a6373",
        "11ca60b8-dc83-42ae-9d2b-1049122f2b3b",
        "13ebd9e5-d9af-48d4-b4e2-a566be548f19",
        "4cd370cb-02c6-4f62-827e-3f01cbaa89d5",
        "218a2ed6-4be5-4ecd-9bba-af292c3d488a",
        "6e8be1c9-b94b-41c9-8ab0-6c783956d2a8",
        "d4d690f6-bf1a-4cec-8972-69425fdb4f19",
        "04e541ef-accd-4af3-8fc1-315bf94b3d2e"
      ]
    },
    "ExePoolModuleFailForPositiveReturnCodeAllowList": {
      "ModuleIds": [
        "9390d3b4-ee74-4dc4-b06d-3c3c6b4d3117",
        "c231ec4e-b467-4825-bc9c-974a75652d17",
        "259eee39-1ce6-41f9-851a-d48eeb34b0eb",
        "e4010928-4e8a-495c-aefb-c188b3851ca3",
        "99e6e7e3-e413-482c-859c-9b7d87b387d5",
        "83b5abda-c052-4fdd-8dd5-1f2ed39314ed",
        "5f0c179e-6d55-4439-880d-97a4582a6373",
        "11ca60b8-dc83-42ae-9d2b-1049122f2b3b",
        "d4d690f6-bf1a-4cec-8972-69425fdb4f19"
      ]
    },
    "AetherBridgeModuleAllowList": {
      "ModuleIds": [
        "9390d3b4-ee74-4dc4-b06d-3c3c6b4d3117",
        "3b624e83-b371-4ff8-bf60-aebce7a457ad",
        "8c69b79c-9193-4995-ac8a-c63c36cbe2f4",
        "c231ec4e-b467-4825-bc9c-974a75652d17",
        "259eee39-1ce6-41f9-851a-d48eeb34b0eb",
        "e4010928-4e8a-495c-aefb-c188b3851ca3",
        "99e6e7e3-e413-482c-859c-9b7d87b387d5",
        "83b5abda-c052-4fdd-8dd5-1f2ed39314ed",
        "648f1224-7cda-43d0-8212-20a9c32f8500",
        "ae336a2d-9486-4755-ab12-258b9a613ea7",
        "13e8b566-1dae-41e5-b62a-c24e1bd8843c",
        "5e37aba9-7285-4ed2-b11d-cbe784f2b59b",
        "41234a99-687a-481d-9ae7-0273c67c3baf",
        "5f0c179e-6d55-4439-880d-97a4582a6373",
        "11ca60b8-dc83-42ae-9d2b-1049122f2b3b",
        "13ebd9e5-d9af-48d4-b4e2-a566be548f19",
        "4cd370cb-02c6-4f62-827e-3f01cbaa89d5",
        "218a2ed6-4be5-4ecd-9bba-af292c3d488a",
        "6e8be1c9-b94b-41c9-8ab0-6c783956d2a8",
        "31542b44-ed76-405f-867d-36bc0b7207a2",
        "04e541ef-accd-4af3-8fc1-315bf94b3d2e"
      ]
    },
    "ApCloudSystems": {
      "Scope": {
        "APServiceUrl": "https://Scopecloud.dev.aetherclouds.ms/scope/"
      },
      "CosmosSpark": {
        "APServiceUrl": "https://cosmossparkcloud.dev.aetherclouds.ms/cosmosspark/"
      }
    },
    "Reuse": {
      "ReuseDryRunCloudSystem": [ "scope" ],
      "ReuseEnableCloudSystem": [ "scope" ]
    }
  }
}