﻿using Aether.ContractProcessing;
using Microsoft.Aether.APCloud.Service;
using Microsoft.Aether.BackendCommon.Extensions;
using Microsoft.Aether.Clouds.CloudCommon.Reuse;
using Microsoft.Aether.DataContracts;
using Microsoft.Aether.DataContracts.CloudSystem;
using Microsoft.Aether.DataContracts.DataReferences;
using Microsoft.Aether.DataContracts.Entity;
using Microsoft.MachineLearning.Common.Core.Asset;
using Newtonsoft.Json;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;

namespace Microsoft.Aether.APCloud.K8sService.Utility
{
    public class JobSubmissionInfoForReuse
    {
        public ModuleExecutionInfo ModuleInfo { get; set; }

        public string CommandText { get; set; }

        public List<ModuleInputExecutionInfo> Inputs { get; set; }

        public List<ModuleOutputExecutionInfo> Outputs { get; set; }

        public List<StructuredInterfaceOutput> OutputSettings { get; set; }
    }

    public class ScopeReuseHashUtils
    {
        private static readonly JsonSerializer serializer = new JsonSerializer()
        {
            // DefaultValueHandling.Populate: Members with a default value but no JSON will be set to their default value when deserializing.
            // Add this to aviod the data inconsistency before and after deep copy.
            DefaultValueHandling = DefaultValueHandling.Populate,

            // NullValueHandling.Ignore: Ignore null values when serializing and deserializing objects.
            // Need it for RunConfiguration.Environment.Python.CondaDependencies,
            // which is a JToken and it has some problems when serializing without this setting.
            NullValueHandling = NullValueHandling.Ignore,
        };

        public static string GetJobSubmissionInfoForReuseMay2023(APCloudJobMetadata job)
        {
            var jobSubmissionInfo = Cloner.DeepCopy(job.JobSubmissionInfo, serializer);
            if (jobSubmissionInfo == null)
            {
                return null;
            }

            var jobSubmissionInfoToBeHashed = new JobSubmissionInfoForReuse
            {
                ModuleInfo = GetModuleInfoToBeHashed(jobSubmissionInfo),
                CommandText = GetCommandTextHashString(jobSubmissionInfo.CommandText),
                Inputs = GetInputsToBeHashed(jobSubmissionInfo),
                Outputs = GetOutputsToBeHashed(jobSubmissionInfo, job.OriginalOutputPathSettings, job.WorkspaceIdentity),
                OutputSettings = GetOutputSettingsToBeHashed(jobSubmissionInfo)
            };

            // Add a space suffix to discard the old version reuse cache.
            return ReuseHashHelper.GetSerializedReuseContractData(jobSubmissionInfoToBeHashed) + " ";
        }

        private static List<string> InputAndOutputPrefixs = new List<string> { "PATHIN_", "PATHOUT_", "RESOURCE_" };

        // The first three are from ScopeCloudConfiguration, "-p" is synchronized from dcm.
        private static List<string> KeysToIgnore = new List<string> { "-tokens", "-vcp", "-autotoken", "-p" };

        private static string GetCommandTextHashString(string commandText)
        {
            var filteredCommandText = commandText;

            // Remove input and output dataid form command text, because it could be randomly generated.
            foreach (var prefix in InputAndOutputPrefixs)
            {
                filteredCommandText = Regex.Replace(filteredCommandText, $@"({prefix}\S+=)\.\.\\\S+", "$1");
            }

            // Remove tokens, because they do not affect execution results.
            foreach (var key in KeysToIgnore)
            {
                filteredCommandText = Regex.Replace(filteredCommandText, $@"\s*{key}\s+[0-9]+\s*", " ");
            }

            filteredCommandText = Regex.Replace(filteredCommandText, @"\s+", " ");

            return filteredCommandText?.Trim();
        }

        private static ModuleExecutionInfo GetModuleInfoToBeHashed(JobSubmissionInfo jobSubmissionInfo)
        {
            if (jobSubmissionInfo.ModuleInfo == null)
            {
                return null;
            }

            var moduleInfoToBeHashed = jobSubmissionInfo.ModuleInfo;
            if (moduleInfoToBeHashed.DataLocation?.Uri != null)
            {
                // Uri was got from snapshot service, it is only related to snapshotId.
                moduleInfoToBeHashed.DataLocation.Uri = null;
            }

            return moduleInfoToBeHashed;
        }

        private static List<ModuleOutputExecutionInfo> GetOutputsToBeHashed(
            JobSubmissionInfo jobSubmissionInfo,
            IDictionary<string, OutputPathSetting> originalOutputPathSettings,
            WorkspaceIdentity workspaceIdentity)
        {
            if (jobSubmissionInfo.Outputs == null)
            {
                return null;
            }

            var outputsToBeHashed = jobSubmissionInfo.Outputs;
            foreach (var output in outputsToBeHashed)
            {
                // The output's cosmos path is generated by dataid.
                output.CosmosPath = null;

                if (!string.IsNullOrEmpty(output.DataLocation?.DataReference?.GetPathOnDataStore()))
                {
                    // The output path may contain templates, such as {runid} {output_name}.
                    // They have been replaced before. We need to use the original path to calculate reuse hash.
                    if (originalOutputPathSettings != null && originalOutputPathSettings.TryGetValue(output.Name, out var outputPath))
                    {
                        string originalPath = outputPath.DatasetPath;
                        if (outputPath.AssetPath != null)
                        {
                            var workspaceContext = workspaceIdentity.ConvertToWorkspaceContext2();
                            if (DpV2DataPath.TryParse(workspaceContext, outputPath.AssetPath, out var dpV2DataPath))
                            {
                                originalPath ??= dpV2DataPath.RelativePath;
                            }
                        }

                        // Trim ambiguous slash to avoid reuse breaking.
                        output.DataLocation.DataReference.UpdatePathOnDataStore(originalPath?.Trim('/'));
                    }
                }
            }

            return outputsToBeHashed;
        }

        private static List<StructuredInterfaceOutput> GetOutputSettingsToBeHashed(JobSubmissionInfo jobSubmissionInfo)
        {
            if (jobSubmissionInfo.ExecutionStructuredInterface?.Outputs == null)
            {
                return null;
            }

            var outputSettings = jobSubmissionInfo.ExecutionStructuredInterface.Outputs.Select(pair => pair.Key).ToList();
            var outputSettingsToBeHashed = new List<StructuredInterfaceOutput>();

            // Since the 1p service supports only dataReference data, the creation of dataset and asset is done in cloudlet.
            // so we need to subjoin the dataset and asset settings to reuse hash.
            foreach (var output in outputSettings)
            {
                var newOutput = new StructuredInterfaceOutput { Name = output.Name };
                if (output.DatasetOutput != null)
                {
                    newOutput.DatasetOutput = new DatasetOutput();
                    if (output.DatasetOutput.DatasetRegistration != null)
                    {
                        newOutput.DatasetOutput.DatasetRegistration = output.DatasetOutput.DatasetRegistration;
                    }
                }

                if (output.AssetOutputSettings != null)
                {
                    // Asset path has been calculated into reuse hash in Outputs.
                    // The path here may has been changed due to marco replacement.
                    // Remove it from output settings to avoid reuse breaking.
                    output.AssetOutputSettings.Path = null;
                    newOutput.AssetOutputSettings = output.AssetOutputSettings;
                }

                outputSettingsToBeHashed.Add(newOutput);
            }

            return outputSettingsToBeHashed;
        }

        private static List<ModuleInputExecutionInfo> GetInputsToBeHashed(JobSubmissionInfo jobSubmissionInfo)
        {
            if (jobSubmissionInfo.Inputs == null)
            {
                return null;
            }

            var inputsToBeHashed = jobSubmissionInfo.Inputs;
            foreach (var input in inputsToBeHashed)
            {
                // Trim ambiguous slash to avoid reuse breaking.
                var relativePath = input.DataLocation?.DataReference?.GetPathOnDataStore();
                if (!string.IsNullOrEmpty(relativePath))
                {
                    input.DataLocation.DataReference.UpdatePathOnDataStore(relativePath.Trim('/'));
                }
            }

            return inputsToBeHashed;
        }
    }
}