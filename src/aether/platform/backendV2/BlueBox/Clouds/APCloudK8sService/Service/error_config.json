[{"ErrorCode": "UserError", "FailureName": "AuthorizationPermissionMismatch", "ResponsibleTeam": "NA", "Keywords": ["This request is not authorized to perform this operation using this permission."]}, {"ErrorCode": "UserError", "FailureName": "MissingUserToken", "ResponsibleTeam": "NA", "Keywords": ["No external user token saved in the job for https://management.core.windows.net/"]}, {"ErrorCode": "UserError", "FailureName": "FileNotFound", "ResponsibleTeam": "NA", "Keywords": ["failed with HttpStatus:NotFound RemoteException: FileNotFoundException File/Folder does not exist"]}, {"ErrorCode": "UserError", "FailureName": "ACLVerificationForbidden", "ResponsibleTeam": "NA", "Keywords": ["Forbidden. ACL verification failed. Either the resource does not exist or the user is not authorized to perform the requested operation."]}, {"ErrorCode": "UserError", "FailureName": "NotSupportedStorageType", "ResponsibleTeam": "NA", "Keywords": ["Input", "should be of type AzureDataLakeReference"]}, {"ErrorCode": "UserError", "FailureName": "NotSupportedStorageType", "ResponsibleTeam": "NA", "Keywords": ["Scope cloud only support Azure Data Lake and MTHDFS Storage"]}, {"ErrorCode": "UserError", "FailureName": "InvalidADLSAccount", "ResponsibleTeam": "NA", "Keywords": ["doesn't have a valid ADLS account name."]}, {"ErrorCode": "UserError", "FailureName": "NotSupportedTenantId", "ResponsibleTeam": "NA", "Keywords": ["Tenand ID expected to be"]}, {"ErrorCode": "ServiceError", "FailureName": "Failed to download resources", "ResponsibleTeam": "External_Cosmos", "Keywords": ["Error in download resource from"]}, {"ErrorCode": "ServiceError", "FailureName": "Failed to download resources", "ResponsibleTeam": "External_Cosmos", "Keywords": ["Error in getting metadata for", "Operation: GETFILESTATUS failed with HttpStatus:ServiceUnavailable"]}, {"ErrorCode": "ServiceError", "FailureName": "Failed to download resources", "ResponsibleTeam": "External_Cosmos", "Keywords": ["Error in getting metadata for", "Operation: GETFILESTATUS failed with HttpStatus:InternalServerError"]}, {"ErrorCode": "ServiceError", "FailureName": "ReferencedDataUris cannot be null or empty", "ResponsibleTeam": "APCloud", "Keywords": ["The ReferencedDataUris of MLTable", "cannot be null or empty"]}, {"ErrorCode": "UserError", "FailureName": "Job submitted by service principal is not supported", "ResponsibleTeam": "NA", "Keywords": ["Service principals are not supported for submitting this kind of job. Please submit the job on behalf of a user."]}, {"ErrorCode": "UserError", "FailureName": "DirectoryNotFound", "ResponsibleTeam": "NA", "Keywords": ["DirectoryNotFoundException", "Blob or Directory not found"]}, {"ErrorCode": "UserError", "FailureName": "NotAllowedModuleId", "ResponsibleTeam": "NA", "Keywords": ["The module Id", "is not in allow list"]}, {"ErrorCode": "UserError", "FailureName": "NotSupportedStorageTypeForScopeScriptInput", "ResponsibleTeam": "NA", "Keywords": ["ScopeScriptInput is only supported for AzureDataLakeReference(gen1)"]}, {"ErrorCode": "UserError", "FailureName": "NotSupportedManagedStorage", "ResponsibleTeam": "NA", "Keywords": ["APDataTransfer is only supported for not managed data store"]}, {"ErrorCode": "UserError", "FailureName": "Output datastore cannot be null or empty", "ResponsibleTeam": "NA", "Keywords": ["The Output datastore is null or empty"]}]