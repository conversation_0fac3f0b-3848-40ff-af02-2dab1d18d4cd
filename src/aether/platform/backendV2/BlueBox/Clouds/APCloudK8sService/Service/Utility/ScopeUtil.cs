﻿using Microsoft.Aether.APCloud.Service;
using Microsoft.Aether.Clouds.CloudCommon;
using Microsoft.Aether.DataContracts;
using Microsoft.Aether.DataContracts.CloudSystem;
using Microsoft.RelInfra.Instrumentation.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;

namespace Microsoft.Aether.APCloud.K8sService.Utility
{
    public class ScopeUtil
    {
        /// <summary>
        /// In Scope Cloud, sometimes user want to specify path suffixes to its input/output path.
        /// The path suffix is set via CloudSettings.ScopeCloudConfig.InputPathSuffixes/OutputPathSuffixes.
        ///
        /// This function is to retrieve the path suffix of the given port from CloudSettings.
        /// </summary>
        /// <param name="job">The APCloudJobMetadata object.</param>
        /// <param name="portInfo">The info of the port to be inspected.</param>
        /// <returns>The path suffix of the given port.</returns>
        public static string GetPathSuffixForPort(APCloudJobMetadata job, ModulePortExecutionInfo portInfo)
        {
            var scopeCloudConfig = job.JobSubmissionInfo.CloudSettings?.ScopeCloudConfig;
            if (scopeCloudConfig == null)
            {
                return null;
            }

            var suffixDict = portInfo is ModuleInputExecutionInfo ? scopeCloudConfig.InputPathSuffixes : scopeCloudConfig.OutputPathSuffixes;
            if (suffixDict == null)
            {
                return null;
            }

            var suffixesByName = suffixDict.Where(
                item => item.Key.Equals(portInfo.Name, StringComparison.OrdinalIgnoreCase)
                )?.ToList();
            if (suffixesByName == null || suffixesByName.Count == 0)
            {
                return null;
            }

            if (suffixesByName.Count > 1)
            {
                CommonLogger.LogEntityWarning(job.JobSubmissionInfo.JobId, "Multiple path suffixes found for port {port_name}, using the first one.", portInfo.Name);
            }

            var argumentList = StructuredInterfaceParserHelper.BuildAmlArgumentList(
                job.ExecutionStructuredInterface,
                legacyArguments: null,
                commandTextMode: StructuredInterfaceParserHelper.CommandTextMode.ApCommandTextMode,
                useParamForInputFile: true,
                overriddenArguments: new List<ArgumentAssignment>() { suffixesByName.First().Value });

            return string.Join(string.Empty, argumentList);
        }

    }
}
