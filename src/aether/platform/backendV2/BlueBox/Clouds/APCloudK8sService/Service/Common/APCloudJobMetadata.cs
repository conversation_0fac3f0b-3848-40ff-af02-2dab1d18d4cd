﻿using Microsoft.Aether.APCloud.K8sService.Utility;
using Microsoft.Aether.Clouds.CloudCommon.Cloudlets;
using Microsoft.Aether.Clouds.CloudCommon.Reuse;
using Microsoft.Aether.Clouds.CloudCommon.ReuseHash;
using Microsoft.Aether.DataContracts;
using Microsoft.Aether.DataContracts.CloudSystem;
using Microsoft.MachineLearning.RunHistory.Contracts;
using Newtonsoft.Json;
using System.Collections.Generic;
using System.Runtime.Serialization;

namespace Microsoft.Aether.APCloud.Service
{
    public class APCloudJobMetadata : AMLJobMetadata, IReuseHashable
    {
        [DataMember]
        public JobSubmissionInfo JobSubmissionInfo { get; set; }

        /// <summary>
        /// Indicate whether the job was submitted from AetherBridge component.
        /// If true, both AnyFile and AnyDirectory are supported for input and output.
        /// For AnyFile input, we will change the cosmos path to directory and add the file name to the command line,
        /// for AnyFile output, we will change the cosmos path to directory and set the DataId to be the file name.
        /// If false, only AnyDirectory is supported.
        /// Supported cloud system by AetherBridge component: ExePool, ScrapingCloud.
        /// </summary>
        [DataMember]
        public bool IsFromAetherBridgeComponent { get; set; }

        // It is used to store the original output path settings before render the template.
        // It is only used to calculate the reuse hash.
        [DataMember]
        public IDictionary<string, OutputPathSetting> OriginalOutputPathSettings { get; set; }

        [DataMember]
        public CreateRunDto InputLineages { get; set; }

        [DataMember]
        public string VcCommand { get; set; }

        public override string ToString()
        {
            return JsonConvert.SerializeObject(this, Formatting.Indented);
        }

        public override void InitializeByJobSubmissionInfo(JobSubmissionInfo jobSubmissionInfo)
        {
            base.InitializeByJobSubmissionInfo(jobSubmissionInfo);
            JobSubmissionInfo = jobSubmissionInfo;
            ReuseStatus = ReuseStatusCode.Undecided;
        }

        public string GetLatestDataToBeHashed()
        {
            return ScopeReuseHashUtils.GetJobSubmissionInfoForReuseMay2023(this);
        }

        /// <summary>
        /// Get the list of old version data to be used to calculate hash.
        /// </summary>
        /// <returns>The list of old version data to be used to calculate hash.</
        /// returns>
        public List<string> GetOldDataToBeHashedList()
        {
            return new List<string>();
        }
    }

    public class OutputPathSetting
    {
        [DataMember]
        public string AssetPath { get; set; }

        [DataMember]
        public string DatasetPath { get; set; }
    }
}
