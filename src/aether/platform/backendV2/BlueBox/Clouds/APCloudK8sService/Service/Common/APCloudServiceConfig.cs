﻿using CsvHelper;
using Microsoft.Aether.CloudletCommon.Configurations;
using Microsoft.RelInfra.Common.Exceptions;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;

namespace Microsoft.Aether.APCloud.K8sService.Common
{
    public class APCloudServiceConfig: CommonCloudUserConfig
    {
        public ApCloudProxyConfig ApCloudProxy { get; set; }
        public ExePoolModuleAllowListConfig ExePoolModuleAllowList { get; set; }

        public ExePoolModuleFailForPositiveReturnCodeAllowListConfig ExePoolModuleFailForPositiveReturnCodeAllowList { get; set; }

        public ApAe365ExePoolModuleAllowListConfig ApAe365ExePoolModuleAllowList { get; set; }

        public AetherBridgeModuleAllowListConfig AetherBridgeModuleAllowList { get; set; }

        public APCloudSystemsConfig APCloudSystems { get; set; }

        public ReuseConfig Reuse { get; set; }

        public CosmosVcMapping[] CosmosVcMappings => _cosmosVcMappings;

        public readonly IReadOnlyList<string> SupportedAetherBridgeCloudSystem = new List<string>
        {
            "exepool",
            "scrapingcloud"
        };

        private CosmosVcMapping[] _cosmosVcMappings;

        public APCloudServiceConfig()
        {
            ParseCosmosVcConfig();
        }

        public class ReuseConfig
        {
            public List<string> ReuseDryRunCloudSystem { get; set; }
            public List<string> ReuseEnableCloudSystem { get; set; }

            public ReuseConfig()
            {
                ReuseDryRunCloudSystem = new List<string>();
                ReuseEnableCloudSystem = new List<string>();
            }
        }

        public class ApCloudProxyConfig
        {
            public string ApCloudProxyUri { get; set; }
            public string AetherSnapshotCosmosRoot { get; set; }
        }

        public class ExePoolModuleAllowListConfig
        {
            public HashSet<string> ModuleIds { get; set; }
        }

        public class ExePoolModuleFailForPositiveReturnCodeAllowListConfig
        {
            public HashSet<string> ModuleIds { get; set; }
        }

        public class ApAe365ExePoolModuleAllowListConfig
        {
            public HashSet<string> ModuleIds { get; set; }
        }

        public class AetherBridgeModuleAllowListConfig
        {
            public HashSet<string> ModuleIds { get; set; }
        }

        public class APCloudSystemsConfig : Dictionary<string, APCloudSystem>, IAPCloudSystemsConfig
        {
            public virtual APCloudSystem GetCloudSystem(string cloudSystemName)
            {
                APCloudSystem cloudSystem;
                if (!TryGetValue(cloudSystemName, out cloudSystem))
                {
                    throw new RelInfraConfigurationException($"AP cloudsystem {cloudSystemName} is not configured");
                }
                return cloudSystem;
            }
        }

        private void ParseCosmosVcConfig()
        {
            //The latest version of ADLToVCMapping.csv can be found https://aad.cosmos08.osdinfra.net/cosmos/cosmos.clientTools/VC_to_ADL_Account/ADLToVCMapping.csv?property=info.
            using (var reader = new StreamReader("ADLToVCMapping.csv"))
            {
                using (var csv = new CsvReader(reader, CultureInfo.InvariantCulture))
                {
                    _cosmosVcMappings = csv.GetRecords<CosmosVcMapping>().ToArray();
                }
            }
        }
    }
}
