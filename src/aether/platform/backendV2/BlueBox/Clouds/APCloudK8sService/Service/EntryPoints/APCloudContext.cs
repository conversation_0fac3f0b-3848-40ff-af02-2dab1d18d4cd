﻿using Aether.Backend.BlueBox.DMTracking.Client;
using DMTracking.Client;
using Microsoft.Aether.APCloud.K8sService.Common;
using Microsoft.Aether.APCloud.Service;
using Microsoft.Aether.BackendCommon.ClusterHealthPoller;
using Microsoft.Aether.BackendCommon.DataManagement;
using Microsoft.Aether.BackendCommon.NodeHashStringComputation;
using Microsoft.Aether.BlueBox.AetherK8SBaseService;
using Microsoft.Aether.BlueBox.OboTokenServiceClient;
using Microsoft.Aether.CloudletCommon;
using Microsoft.Aether.Clouds.CloudCommon;
using Microsoft.Aether.Clouds.CloudCommon.Cloudlets;
using Microsoft.Aether.Clouds.CloudCommon.Decorators;
using Microsoft.Aether.Clouds.CloudCommon.JobQueueProcessing;
using Microsoft.Aether.Clouds.CloudCommon.Reuse;
using Microsoft.Aether.Clouds.CloudCommon.ReuseHash;
using Microsoft.Aether.DataContract.Backend.DataManagement;
using Microsoft.Aether.DMTracking.Common.DataAvailabilityProcessor;
using Microsoft.Aether.S2S.Common;
using Microsoft.Aether.TaggedQueueManager.CloudTags;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using Microsoft.MachineLearning.Pipeline.Common;
using Microsoft.RelInfra.Common.Configuration;
using System;
using System.IO;
using System.Reflection;

namespace Microsoft.Aether.APCloud.K8sService.EntryPoints
{
    public class APCloudContext : ICloudletContext<APCloudJobMetadata>
    {
        public string ServiceName => "APCloudK8sService";

        public string CloudName => "APCloud";


        public APCloudContext()
        {
        }

        public void RegisterServices(IServiceCollection services, IConfigurationRoot Configuration)
        {
            InjectAPCloudServiceConfig(services, Configuration);

            InjectAPServiceClientFactory(services);

            InjectJobProcessorFactoryAndQueue(services);

            InjectErrorClassifier(services);

            InjectReuseHelper(services, Configuration);
        }

        private void InjectAPCloudServiceConfig(IServiceCollection services, IConfigurationRoot Configuration)
        {
            services.Configure<APCloudServiceConfig>(Configuration.GetSection("ApCloudService"));
            var serviceProvider = services.BuildServiceProvider();
            services.AddSingleton(serviceProvider.GetRequiredService<IOptions<APCloudServiceConfig>>().Value);
        }

        private void InjectAPServiceClientFactory(IServiceCollection services)
        {
            IServiceProvider serviceProvider = services.BuildServiceProvider();
            var oboTokenServiceClient = serviceProvider.GetRequiredService<IOboTokenServiceClient>();
            var tokenProvider = serviceProvider.GetRequiredService<IS2STokenProvider>();

            var apServiceClientProvider = new APServiceClientFactory(
                servicePrincipalTokenProvider: tokenProvider,
                oboTokenServiceClient: oboTokenServiceClient);

            services.AddSingleton<IAPServiceClientFactory>(apServiceClientProvider);

            var k8sService = serviceProvider.GetRequiredService<K8sBaseService>();
            var clusterHealthPoller = serviceProvider.GetRequiredService<IClusterHealthPoller>();
            var amlClientFactory = new AmlSvcClientFactory(k8sService, clusterHealthPoller);
            services.AddSingleton<IAmlSvcClientFactory>(amlClientFactory);
        }

        private void InjectJobProcessorFactoryAndQueue(IServiceCollection services)
        {
            services.AddSingleton<IJobProcessorFactory<APCloudJobMetadata>, JobProcessorFactory>();
            services.AddSingleton<IJobQueueManager, TaggedJobQueueManager<AssignedQueueIdTags, APCloudJobMetadata>>();
            services.AddSingleton<JobQueue>();
        }

        private void InjectErrorClassifier(IServiceCollection services)
        {
            string path = Path.Combine(Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location), @"error_config.json");
            var errorClassifier = new ErrorClassifier(path, AMLServiceTeams.APCloud);
            services.AddSingleton<IErrorClassifier>(errorClassifier);
        }

        private void InjectReuseHelper(IServiceCollection services, IConfigurationRoot configuration)
        {
            services.Configure<DMTConfig>(configuration.GetSection("DMTConfig"));
            var serviceProvider = services.BuildServiceProvider();
            services.AddSingleton(serviceProvider.GetRequiredService<IOptions<DMTConfig>>().Value);

            var k8sService = serviceProvider.GetRequiredService<K8sBaseService>();

            var dmtClient = new DmtClient(
                k8sService.ApConfig.GetString("DataManagementTracking.Address"), 
                k8sService.ServicePrincipalAadTokenProvider, 
                SourceType.APCloud);
            services.AddSingleton<IDMTrackingClient>(dmtClient);

            var cloudWorkerClient = new CloudWorkerClient(
                k8sService.ApConfig.GetString("CloudWorkerClient.BaseUrl"),
                TimeSpan.FromSeconds(k8sService.ApConfig.GetInteger("CloudWorkerClient.HttpTimeoutInSec")), 
                s2STokenProvider: k8sService.ServicePrincipalAadTokenProvider);
            services.AddSingleton<ICloudWorkerClient>(cloudWorkerClient);

            services.AddSingleton<IModuleInfoRetriever, CloudletJobModuleInfoRetriever>();
            services.AddSingleton<IHashCalculator, ReuseHashCalculator>();

            services.AddSingleton<ICompliantSubscriptionValidator, CompliantSubscriptionValidator>();
            services.AddSingleton<IManagedDataAvailabilityCheckValidator, ManagedDataAvailabilityCheckValidator>();
            services.AddSingleton<CountAndLogExceptionDecorator>();

            services.AddSingleton<ReuseHelper<APCloudJobMetadata>>();
        }
    }
}
