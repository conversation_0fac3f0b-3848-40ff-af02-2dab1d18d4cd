﻿using System;
using Microsoft.RelInfra.Common.RetryExecution.Policy;
using Microsoft.Aether.S2S.Common;
using Microsoft.Aether.BlueBox.OboTokenServiceClient;
using Microsoft.Aether.BackendCommon.Logging;
using Microsoft.Aether.APCloud.K8sService.Common;

namespace Microsoft.Aether.APCloud.Service
{
    public class APServiceClientFactory: IAPServiceClientFactory
    {
        private readonly IS2STokenProvider _servicePrincipalTokenProvider;
        private readonly IOboTokenServiceClient _oboTokenServiceClient;
        private readonly IRetryPolicy _retryPolicy;

        public APServiceClientFactory(
            IS2STokenProvider servicePrincipalTokenProvider,
            IOboTokenServiceClient oboTokenServiceClient)
        {
            _servicePrincipalTokenProvider = servicePrincipalTokenProvider ?? throw new ArgumentNullException(nameof(servicePrincipalTokenProvider));
            _oboTokenServiceClient = oboTokenServiceClient ?? throw new ArgumentNullException(nameof(oboTokenServiceClient));
            _retryPolicy = new ExponentialBackoffRetryPolicy(0);
        }

        public APServiceClient GetClient(APCloudSystem aPCloudSystem, IJobDualLogger dualLogger)
        {
            return new APServiceClient(
                servicePrincipalAadTokenProvider: _servicePrincipalTokenProvider,
                oboTokenServiceClient: _oboTokenServiceClient,
                retryPolicy: _retryPolicy,
                apCloudSystem: aPCloudSystem,
                dualLogger: dualLogger);
        }
    }
}