﻿using System;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.RelInfra.Common;
using Microsoft.RelInfra.Common.Exceptions;
using Microsoft.RelInfra.Common.RetryExecution.Policy;
using Microsoft.RelInfra.Common.Serialization;
using Microsoft.RelInfra.Extensions;
using Microsoft.Aether.BackendCommon.Logging;
using Microsoft.Aether.S2S.Common;
using Microsoft.Aether.BlueBox.OboTokenServiceClient;
using Microsoft.Aether.BlueBox.OboTokenServiceClient.Contracts;
using Microsoft.Aether.DataContracts.CloudSystem;
using Microsoft.RelInfra.Instrumentation.Logging;
using System.Collections.Generic;
using Microsoft.Aether.APCloud.K8sService.Common;
using Newtonsoft.Json;
using System.Text;

namespace Microsoft.Aether.APCloud.Service
{
    public class APServiceClient
    {
        private readonly IS2STokenProvider _servicePrincipalAadTokenProvider;
        private readonly IOboTokenServiceClient _oboTokenServiceClient;
        private readonly IRetryPolicy _retryPolicy;
        private readonly APCloudSystem _apCloudSystem;
        private readonly IJobDualLogger _dualLogger;
        private readonly HttpClient _client;

        public APServiceClient(
            IS2STokenProvider servicePrincipalAadTokenProvider,
            IOboTokenServiceClient oboTokenServiceClient,
            IRetryPolicy retryPolicy, 
            APCloudSystem apCloudSystem,
            IJobDualLogger dualLogger)
        {
            _servicePrincipalAadTokenProvider = servicePrincipalAadTokenProvider ?? throw new ArgumentNullException(nameof(servicePrincipalAadTokenProvider));
            _oboTokenServiceClient = oboTokenServiceClient ?? throw new ArgumentNullException(nameof(oboTokenServiceClient));
            _retryPolicy = retryPolicy;
            _apCloudSystem = apCloudSystem ?? throw new ArgumentNullException(nameof(apCloudSystem));
            _dualLogger = dualLogger ?? throw new ArgumentNullException(nameof(dualLogger));
            _client = new HttpClient();
        }

        public async Task<string> SubmitAsync(APCloudJobMetadata job, CancellationToken cancellationToken)
        {
            try
            {
                if (!IsTenantIdAllowed(job.JobSubmissionInfo.AzureTenantId))
                {
                    throw new InvalidOperationException($"Tenand ID expected to be {_apCloudSystem.MicrosoftTenantId} or {_apCloudSystem.TorusTenantId} or {_apCloudSystem.PMETenantId}, but was {job.JobSubmissionInfo.AzureTenantId}.");
                }

                APCloudJobMetadata newJob = SerializationHelpers.DeserializeEntity<APCloudJobMetadata>(SerializationHelpers.SerializeEntity(job));
                newJob.JobSubmissionInfo.TeamId = job.WorkspaceIdentity.WorkspaceId;

                var customHeaders = await PrepareCustomTokenHeadersForJobAsync(newJob);
                _dualLogger.LogEntityInfo(job.Id, $"APServiceClient Submit APCloud job to: {_apCloudSystem.APServiceUrl} for workspace: {job.WorkspaceIdentity.WorkspaceId} with subscription: {job.WorkspaceIdentity.SubscriptionId} and respurce group: {job.WorkspaceIdentity.ResourceGroupName}.");
                string serverPath = ForwardSlashPath.Combine(_apCloudSystem.APServiceUrl, "jobs");

                if (_dualLogger.JobExecutionLogger != null)
                {
                    try
                    {
                        string serializedJobSubmissionInfo = JsonConvert.SerializeObject(newJob.JobSubmissionInfo,
                            settings: new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });

                        _dualLogger.JobExecutionLogger.LogEntityInfo(job.Id, $"SubmitJobAsync - Submitting APCloud Job. "
                            + $"serverPath: {serverPath}, "
                            + $"entity: {Convert.ToBase64String(Encoding.UTF8.GetBytes(serializedJobSubmissionInfo))}");
                    }
                    catch (Exception ex)
                    {
                        CommonLogger.LogEntityWarning(job.Id, "Failed to log downstream API details due to error {exception}", ex);
                    }
                }

                return await _client.PostEntityWithAuthHeaderAsync<JobSubmissionInfo, string>(
                    serverPath: serverPath,
                    entity: newJob.JobSubmissionInfo,
                    authToken: await _servicePrincipalAadTokenProvider.GetTokenAsync(),
                    retryPolicy: _retryPolicy,
                    cancellationToken: cancellationToken,
                    customHeaders: customHeaders);
            }
            catch (ServiceInvocationException ex)
            {
                _dualLogger.LogEntityError(job.Id, "Failed to submit the AP cloud job because of error: {safe_exception_message}, stack trace: {stack_trace}", ex.Message, ex.StackTrace);
                throw;
            }
        }

        public async Task<CloudJobStatus> GetJobStatusAsync(APCloudJobMetadata job,  CancellationToken cancellationToken)
        {
            var customHeaders = await PrepareCustomTokenHeadersForJobAsync(job);
            return await _client.GetEntityWithAuthHeaderAsync<CloudJobStatus>(
                serverPath: ForwardSlashPath.Combine(_apCloudSystem.APServiceUrl, $"jobs/{job.Id}/status"), 
                authToken: await  _servicePrincipalAadTokenProvider.GetTokenAsync(),
                retryPolicy: _retryPolicy, 
                cancellationToken: cancellationToken,
                customHeaders: customHeaders);
        }
        
        public async Task<CloudJobResultInfo> GetJobResultInfoAsync(APCloudJobMetadata job, CancellationToken cancellationToken)
        {
            var customHeaders = await PrepareCustomTokenHeadersForJobResultAsync(job);
            return await _client.GetEntityWithAuthHeaderAsync<CloudJobResultInfo>(
                serverPath: ForwardSlashPath.Combine(_apCloudSystem.APServiceUrl, $"jobs/{job.Id}/result"), 
                authToken: await  _servicePrincipalAadTokenProvider.GetTokenAsync(),
                retryPolicy: _retryPolicy, 
                cancellationToken: cancellationToken,
                customHeaders: customHeaders);
        }
        
        public async Task DeleteJobAsync(APCloudJobMetadata job)
        {
             await _client.DeleteEntityWithAuthHeaderAsync(
                serverPath: ForwardSlashPath.Combine(_apCloudSystem.APServiceUrl, $"jobs/{job.Id}"), 
                authToken: await  _servicePrincipalAadTokenProvider.GetTokenAsync());
        }

        private bool IsTenantIdAllowed(string tenantId)
        {
            return _apCloudSystem.MicrosoftTenantId.Equals(tenantId, StringComparison.OrdinalIgnoreCase)
                || _apCloudSystem.TorusTenantId.Equals(tenantId, StringComparison.OrdinalIgnoreCase)
                || _apCloudSystem.PMETenantId.Equals(tenantId, StringComparison.OrdinalIgnoreCase);
        }

        private async Task<Dictionary<string, string>> PrepareCustomTokenHeadersForJobAsync(APCloudJobMetadata job)
        {
            Dictionary<string, string> customHeaders = new Dictionary<string, string>();

            job.JobSubmissionInfo.AuthToken = await AddCustomTokenAsync(customHeaders: customHeaders, job: job, audience: AdalConstants.Aether1pResource, tokenHeader: OboTokenServiceClient.Aether1PUserTokenHeader, useDualLog: true);
            await AddCustomTokenAsync(customHeaders: customHeaders, job: job, audience: AdalConstants.ArmResource, tokenHeader: OboTokenServiceClient.ArmUserTokenHeader);
            await AddCustomTokenAsync(customHeaders: customHeaders, job: job, audience: AdalConstants.StorageResource, tokenHeader: OboTokenServiceClient.StoreUserTokenHeader);
            await AddCustomTokenAsync(customHeaders: customHeaders, job: job, audience: AdalConstants.CosmosResource, tokenHeader: OboTokenServiceClient.CosmosUserTokenHeader);

            return customHeaders;
        }

        private async Task<Dictionary<string, string>> PrepareCustomTokenHeadersForJobResultAsync(APCloudJobMetadata job)
        {
            Dictionary<string, string> customHeaders = new Dictionary<string, string>();
            await AddCustomTokenAsync(customHeaders: customHeaders, job: job, audience: AdalConstants.ArmResource, tokenHeader: OboTokenServiceClient.ArmUserTokenHeader);
            await AddCustomTokenAsync(customHeaders: customHeaders, job: job, audience: AdalConstants.CosmosResource, tokenHeader: OboTokenServiceClient.CosmosUserTokenHeader);
            return customHeaders;
        }

        private async Task<string> AddCustomTokenAsync(IDictionary<string, string> customHeaders, APCloudJobMetadata job, string audience, string tokenHeader, bool useDualLog = false)
        {
            string userOboToken = await TryGetOboTokenAsync(job, audience, useDualLog);
            if( userOboToken != null)
            {
                customHeaders.Add(tokenHeader, userOboToken);
            }
            return userOboToken;
        }

        private async Task<string> TryGetOboTokenAsync(APCloudJobMetadata job, string audience, bool useDualLog)
        {
            var userTokenDto = new GetUserTokenDto()
            {
                Tid = job.CreatedBy.UserTenantId,
                Oid = job.CreatedBy.UserObjectId,
                Resource = audience
            };

            try
            {
                string accessToken = await _oboTokenServiceClient.GetOboTokenAsync(job.WorkspaceIdentity.SubscriptionId, userTokenDto);
                LogInfo(jobId: job.Id, message: $"Got OboToken successfully. (audience: {audience})", useDualLog: useDualLog);
                return accessToken;
            }
            catch(ServiceInvocationException e)
            {
                var ex = e.InnerException;
                if (ex.Message.Contains("no token was found in the cache"))
                {
                    LogInfo(jobId: job.Id, message: $"No user token found in OboToken Service cache for given user oid. (audience: {audience})", useDualLog: useDualLog);
                    return null;
                }
                else
                {
                    LogInfo(jobId: job.Id, message: $"Not able to get user token for given user oid (audience: {audience}), statusCode: {e.StatusCode} from AML OboToken Service. Ignore for now.", useDualLog: useDualLog);
                    // Todo throw exception after e2e is enabled and all test scenrios are covered.
                    //_dualLogger.LogEntityError(job.Id, "Exception when calling OboToken Service to get user token for user: {safe_exception_message}, stack trace: {stack_trace}", e.InnerException.Message, e.StackTrace);
                    //throw;
                    return null;
                }
            }
        }

        private void LogInfo(string jobId, string message, bool useDualLog)
        {
            if (useDualLog)
            {
                _dualLogger.LogEntityInfo(jobId, message);
            }
            else
            {
                CommonLogger.LogEntityInfo(jobId, message);
            }
        }
    }
}