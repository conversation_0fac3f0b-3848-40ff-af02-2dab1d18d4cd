﻿using Microsoft.Aether.BlueBox.Clouds.Common;
using Microsoft.Aether.Clouds.CloudCommon.Cloudlets;
using Microsoft.Aether.Clouds.CloudCommon.JobQueueProcessing;
using Microsoft.Aether.TaggedQueueManager.CloudTags;
using Microsoft.RelInfra.Instrumentation;

namespace Microsoft.Aether.APCloud.Service
{
    public class APCloudJobsController : BaseJobController<APCloudJobMetadata>
    {
        public APCloudJobsController(
            ITaggedJobsManager<AssignedQueueIdTags, APCloudJobMetadata> taggedJobsManager,
            CounterManager counters,
            JobQueue jobQueue)
            : base(taggedJobsManager, counters, jobQueue)
        {
        }
    }
}
