﻿{
  "Logging": {
    "IncludeScopes": false,
    "LogLevel": {
      "Default": "Information",
      "System": "Information",
      "Microsoft": "Information"
    }
  },
  "AmlTelemetry": {
    "Enabled": true,
    "GenevaAccount": "Vienna",
    "MetricsConfiguration": {
      "Disabled": false,
      "Namespace": "Aether.K8S.APCloudK8sService"
    },
    "IsDeveloperWorkstation": false,
    "Scrubbing": {
      "ScrubbingEnabled": true
    }
  },
  "Kestrel": {
    "ServerListenPort": "443",
    "ServerCertificate": "cert/cert.pfx",
    "Limits": {
      "MaxRequestBodySize": "********"
    }
  },
  "ApCloudService": {
    "EnablePolicyService": false,
    "ApCloudProxy": {
      "ApCloudProxyUri": "https://apcloudproxy.prod.aetherclouds.ms/",
      "AetherSnapshotCosmosRoot": "https://cosmos09.osdinfra.net/cosmos/searchRelevance.aether.store/local/public2/modules/"
    },
    "_ExePoolModuleAllowListAnnotations": [
      "; There are some AEther modules that need to access LEGO portal, which is only accessible in AP virtual network for now.",
      "; These modules cannot be migrated to AzureML Compute until LEGO portal is available on internet.",
      "; Before that happens, we adopt a short-term solution of allowlisting these AEther modules and forward the request to AP ExePool.",
      "; The modules are:",
      "; - L3/L4 prevalidation: 9390d3b4-ee74-4dc4-b06d-3c3c6b4d3117",
      "; - Get IndexServe Depolyment Info: 3b624e83-b371-4ff8-bf60-aebce7a457ad",
      "; - Data Deployment Monitor: 8c69b79c-9193-4995-ac8a-c63c36cbe2f4",
      "; - Run Raas and XCache Test: c231ec4e-b467-4825-bc9c-974a75652d17",
      "; - Report Workflow Finish: 259eee39-1ce6-41f9-851a-d48eeb34b0eb",
      "; - Get IndexServe Deployment Info v1.0.16 (Fail for Positive Return Code): e4010928-4e8a-495c-aefb-c188b3851ca3",
      "; - Data Deployment Monitor v1.0.19 (Fail for Positive Return Code): 99e6e7e3-e413-482c-859c-9b7d87b387d5",
      "; - Get IndexServe Deployment Info v1.0.19: 83b5abda-c052-4fdd-8dd5-1f2ed39314ed",
      "; - [LEGO] L1/L2 Model Check-In Prevalidation: 4cd370cb-02c6-4f62-827e-3f01cbaa89d5",
      "; - [LEGO] Barrier: 218a2ed6-4be5-4ecd-9bba-af292c3d488a",
      "; - [LEGO] L1/L2 Model Check-In Prevalidation (Fail for Positive Return Code): 7ef16df1-b63c-4d72-8df9-6ebf198fc69a",
      "; - Pass Through (for test only): 5e37aba9-7285-4ed2-b11d-cbe784f2b59b",
      "; - [DeepL2] Endpoint Scraping, DeepL2MVP: d4d690f6-bf1a-4cec-8972-69425fdb4f19",
      "; - QRMRetrieveFromEQ: 04e541ef-accd-4af3-8fc1-315bf94b3d2e",
      "",
      "; Some modules are for test only in AEther Dev env",
      "; - Pass Through: 648f1224-7cda-43d0-8212-20a9c32f8500",
      "; - Sleep for 1h to test Cancel: ae336a2d-9486-4755-ab12-258b9a613ea7",
      "; - Duplication of L3/L4 prevalidation: 13e8b566-1dae-41e5-b62a-c24e1bd8843c",
      "; - Wait Five Min migu: 41234a99-687a-481d-9ae7-0273c67c3baf, used to trigger a relatively long run on APCloud and observe the return value of GetStatus API",
      "; - Positive Exit Code Test migu: 5f0c179e-6d55-4439-880d-97a4582a6373, to trigger a run in ExePool that fails with positive return code for a module enabled FailForPositiveReturnCode.",
      "; - Zero Exit Code Test migu: 11ca60b8-dc83-42ae-9d2b-1049122f2b3b, to trigger a run in ExePool that return 0 for a module enabled FailForPositiveReturnCode.",
      "; - Success for Positive Exit Code Test migu: 13ebd9e5-d9af-48d4-b4e2-a566be548f19, to trigger a run in ExePool that fails with positive return code for a module dit NOT enable FailForPositiveReturnCode.",
      "; - Pass Through Directory by migu: 6e8be1c9-b94b-41c9-8ab0-6c783956d2a8, to trigger a run with AnyDirectory output"
    ],
    "ExePoolModuleAllowList": {
      "ModuleIds": [
        "9390d3b4-ee74-4dc4-b06d-3c3c6b4d3117",
        "3b624e83-b371-4ff8-bf60-aebce7a457ad",
        "8c69b79c-9193-4995-ac8a-c63c36cbe2f4",
        "c231ec4e-b467-4825-bc9c-974a75652d17",
        "259eee39-1ce6-41f9-851a-d48eeb34b0eb",
        "e4010928-4e8a-495c-aefb-c188b3851ca3",
        "99e6e7e3-e413-482c-859c-9b7d87b387d5",
        "83b5abda-c052-4fdd-8dd5-1f2ed39314ed",
        "4cd370cb-02c6-4f62-827e-3f01cbaa89d5",
        "218a2ed6-4be5-4ecd-9bba-af292c3d488a",
        "7ef16df1-b63c-4d72-8df9-6ebf198fc69a",
        "5e37aba9-7285-4ed2-b11d-cbe784f2b59b",
        "d4d690f6-bf1a-4cec-8972-69425fdb4f19",
        "04e541ef-accd-4af3-8fc1-315bf94b3d2e"
      ]
    },
    "_ExePoolModuleFailForPositiveReturnCodeAllowListAnnotations": [
      "; ExePool supports FailForPositiveReturnCode, ref: https://aetherwiki.azurewebsites.net/articles/FeatureAreas/Non-Zero_Return_Codes.html",
      "; A subset of ExePoolModuleAllowList have enabled it.",
      "; The modules are:",
      "; - L3/L4 prevalidation: 9390d3b4-ee74-4dc4-b06d-3c3c6b4d3117",
      "; - Run Raas and XCache Test: c231ec4e-b467-4825-bc9c-974a75652d17",
      "; - Report Workflow Finish: 259eee39-1ce6-41f9-851a-d48eeb34b0eb",
      "; - Get IndexServe Deployment Info v1.0.16 (Fail for Positive Return Code): e4010928-4e8a-495c-aefb-c188b3851ca3",
      "; - Data Deployment Monitor v1.0.19 (Fail for Positive Return Code): 99e6e7e3-e413-482c-859c-9b7d87b387d5",
      "; - Get IndexServe Deployment Info v1.0.19: 83b5abda-c052-4fdd-8dd5-1f2ed39314ed",
      "; - [LEGO] L1/L2 Model Check-In Prevalidation (Fail for Positive Return Code): 7ef16df1-b63c-4d72-8df9-6ebf198fc69a",
      "; - [DeepL2] Endpoint Scraping, DeepL2MVP: d4d690f6-bf1a-4cec-8972-69425fdb4f19",
      "; - [LEGO] Data Deployment Monitor: 73577422-4720-4fd7-bb91-4f2d1f122034",
      "; Some modules are for test only in AEther Dev env",
      "; Positive Exit Code Test migu: 5f0c179e-6d55-4439-880d-97a4582a6373",
      "; Zero Exit Code Test migu: 11ca60b8-dc83-42ae-9d2b-1049122f2b3b"
    ],
    "ExePoolModuleFailForPositiveReturnCodeAllowList": {
        "ModuleIds": [
            "9390d3b4-ee74-4dc4-b06d-3c3c6b4d3117",
            "c231ec4e-b467-4825-bc9c-974a75652d17",
            "259eee39-1ce6-41f9-851a-d48eeb34b0eb",
            "e4010928-4e8a-495c-aefb-c188b3851ca3",
            "99e6e7e3-e413-482c-859c-9b7d87b387d5",
            "83b5abda-c052-4fdd-8dd5-1f2ed39314ed",
            "7ef16df1-b63c-4d72-8df9-6ebf198fc69a",
            "d4d690f6-bf1a-4cec-8972-69425fdb4f19",
            "73577422-4720-4fd7-bb91-4f2d1f122034",
            "cfd167a2-eb28-42af-a234-b696c44ff4fd",
            "9a813cca-71f3-4092-8332-539340962531",
            "a91d4990-6b85-4bff-b318-089b1e82ebe6",
            "2a96c5c8-edfb-457d-b7d7-a0de181bed61",
            "c59f551a-9831-46fa-8ae0-43c5faa2a14d",
            "00fc2cac-1771-48d7-ac73-b75670fff584",
            "4be2462a-489c-47e1-84d2-5ed184009995",
            "9bcb112b-ffac-4b46-ada7-bc5813845486",
            "9e94409a-3cb1-4339-9c97-fec9a64df1ed",
            "69c78bf7-ebd4-4774-a521-5b9d435ca7ff"
        ]
    },
    "_ApAe365ExePoolModuleAllowListAnnotations": [
      "; These are AEther Eyes-On modules that have to run in AP Ae365ExePool for compliance constrains.",
      "; AzureML Compute have to implement compliance services for them to be migrated.",
      "; Before that happens, we adopt a short-term solution of allowlisting these AEther modules, and the AzureML client can",
      "; reference these moduleIds. And the request will be forwarded to AP Ae365ExePool.",
      "; The modules are:",
      "; - CAX EyesOn Module [ND] v1.6: 654ec0ba-bed3-48eb-a594-efd0e9275e0d"
    ],
    "ApAe365ExePoolModuleAllowList": {
      "ModuleIds": [
        "654ec0ba-bed3-48eb-a594-efd0e9275e0d"
      ]
    },
    "AetherBridgeModuleAllowListAnnotations": [
      "; AetherBridge is a short-term solution that can bridge job requests to AEther.",
      "",
      "; There are some AEther modules that need to access LEGO portal, which is only accessible in AP virtual network for now.",
      "; These modules cannot be migrated to AzureML Compute until LEGO portal is available on internet.",
      "; Before that happens, we allowlist these AEther modules and forward the request to AP ExePool through AetherBridge.",
      "; The modules are:",
      "; - L3/L4 prevalidation: 9390d3b4-ee74-4dc4-b06d-3c3c6b4d3117",
      "; - Get IndexServe Depolyment Info: 3b624e83-b371-4ff8-bf60-aebce7a457ad",
      "; - Data Deployment Monitor: 8c69b79c-9193-4995-ac8a-c63c36cbe2f4",
      "; - Run Raas and XCache Test: c231ec4e-b467-4825-bc9c-974a75652d17",
      "; - Report Workflow Finish: 259eee39-1ce6-41f9-851a-d48eeb34b0eb",
      "; - Get IndexServe Deployment Info v1.0.16 (Fail for Positive Return Code): e4010928-4e8a-495c-aefb-c188b3851ca3",
      "; - Data Deployment Monitor v1.0.19 (Fail for Positive Return Code): 99e6e7e3-e413-482c-859c-9b7d87b387d5",
      "; - Get IndexServe Deployment Info v1.0.19: 83b5abda-c052-4fdd-8dd5-1f2ed39314ed",
      "; - [LEGO] L1/L2 Model Check-In Prevalidation: 4cd370cb-02c6-4f62-827e-3f01cbaa89d5",
      "; - [LEGO] Barrier: 218a2ed6-4be5-4ecd-9bba-af292c3d488a",
      "; - [LEGO] L1/L2 Model Check-In Prevalidation (Fail for Positive Return Code): 7ef16df1-b63c-4d72-8df9-6ebf198fc69a",
      "; - Pass Through (for test only): 5e37aba9-7285-4ed2-b11d-cbe784f2b59b",
      "; - [DeepL2] Endpoint Scraping, DeepL2MVP: d4d690f6-bf1a-4cec-8972-69425fdb4f19",
      "; - QRMRetrieveFromEQ: 04e541ef-accd-4af3-8fc1-315bf94b3d2e",
      "; - Create Crush File List: fa74a01b-d1b3-4798-a6c1-3731832d6fbc",
      "; - Split CosmosPath by Size: ff1a73c0-13dd-44a6-af17-b9bc91f50e3d",
      "; - CosmosPath To ZipFile: 1e570417-86ae-48f3-9c21-5c941e56b5cd",
      "",
      "; Some modules are for test only in AEther Dev env",
      "; - Pass Through: 648f1224-7cda-43d0-8212-20a9c32f8500",
      "; - Sleep for 1h to test Cancel: ae336a2d-9486-4755-ab12-258b9a613ea7",
      "; - Duplication of L3/L4 prevalidation: 13e8b566-1dae-41e5-b62a-c24e1bd8843c",
      "; - Wait Five Min migu: 41234a99-687a-481d-9ae7-0273c67c3baf, used to trigger a relatively long run on APCloud and observe the return value of GetStatus API",
      "; - Positive Exit Code Test migu: 5f0c179e-6d55-4439-880d-97a4582a6373, to trigger a run in ExePool that fails with positive return code for a module enabled FailForPositiveReturnCode.",
      "; - Zero Exit Code Test migu: 11ca60b8-dc83-42ae-9d2b-1049122f2b3b, to trigger a run in ExePool that return 0 for a module enabled FailForPositiveReturnCode.",
      "; - Success for Positive Exit Code Test migu: 13ebd9e5-d9af-48d4-b4e2-a566be548f19, to trigger a run in ExePool that fails with positive return code for a module dit NOT enable FailForPositiveReturnCode.",
      "; - Pass Through Directory by migu: 6e8be1c9-b94b-41c9-8ab0-6c783956d2a8, to trigger a run with AnyDirectory output",
      "",
      "; WebXT team wants to access Aether ScrapingCloud through AML, so, we allowlist these AEther modules and forward the request to AP ScrapingCloud through AetherBridge.",
      "; The modules are:",
      "; - ScrapingModule (Non Deterministic): 59a97d83-3c68-425d-b43e-cb683b5e2d43",
      "; - ScrapingModule (Deterministic): bd4ea700-39c0-437b-ae3d-43a5aab5615e",
      "; - ScrapingModule (D): 422486ba-eb18-4d0d-aa63-49041dfd704a",
      "",
      "; Some modules are for test only in AEther Dev env",
      "; - ScrapingModule: 31542b44-ed76-405f-867d-36bc0b7207a2"
    ],
    "AetherBridgeModuleAllowList": {
      "ModuleIds": [
        "9390d3b4-ee74-4dc4-b06d-3c3c6b4d3117",
        "3b624e83-b371-4ff8-bf60-aebce7a457ad",
        "8c69b79c-9193-4995-ac8a-c63c36cbe2f4",
        "c231ec4e-b467-4825-bc9c-974a75652d17",
        "259eee39-1ce6-41f9-851a-d48eeb34b0eb",
        "e4010928-4e8a-495c-aefb-c188b3851ca3",
        "99e6e7e3-e413-482c-859c-9b7d87b387d5",
        "83b5abda-c052-4fdd-8dd5-1f2ed39314ed",
        "4cd370cb-02c6-4f62-827e-3f01cbaa89d5",
        "218a2ed6-4be5-4ecd-9bba-af292c3d488a",
        "7ef16df1-b63c-4d72-8df9-6ebf198fc69a",
        "5e37aba9-7285-4ed2-b11d-cbe784f2b59b",
        "59a97d83-3c68-425d-b43e-cb683b5e2d43",
        "bd4ea700-39c0-437b-ae3d-43a5aab5615e",
        "422486ba-eb18-4d0d-aa63-49041dfd704a",
        "d4d690f6-bf1a-4cec-8972-69425fdb4f19",
        "04e541ef-accd-4af3-8fc1-315bf94b3d2e",
        "fa74a01b-d1b3-4798-a6c1-3731832d6fbc",
        "ff1a73c0-13dd-44a6-af17-b9bc91f50e3d",
        "1e570417-86ae-48f3-9c21-5c941e56b5cd"
      ]
    },
    "ApCloudSystems": {
      "Scope": {
        "APServiceUrl": "https://Scopecloud.prod.aetherclouds.ms/scope/",
        "TorusTenantId": "cdc5aeea-15c5-4db6-b079-fcadd2505dc2",
        "PMETenantId": "975f013f-7f24-47e8-a7d3-abc4752bf346"
      },
      "ScopeTest": {
        "APServiceUrl": "https://Scopecloud.dev.aetherclouds.ms/scope/",
        "TorusTenantId": "cdc5aeea-15c5-4db6-b079-fcadd2505dc2",
        "PMETenantId": "975f013f-7f24-47e8-a7d3-abc4752bf346"
      },
      "CosmosSpark": {
        "APServiceUrl": "https://cosmossparkcloud.prod.aetherclouds.ms/cosmosspark/",
        "TorusTenantId": "cdc5aeea-15c5-4db6-b079-fcadd2505dc2",
        "PMETenantId": "975f013f-7f24-47e8-a7d3-abc4752bf346"
      }
    },
    "Reuse": {
      "ReuseDryRunCloudSystem": [ "scope" ],
      "ReuseEnableCloudSystem": []
    }
  },
  "SystemConfigurationOverride": {
    "JobQueueManager.ProcessQueueDelay": "00:00:10",
    "AzureQueueManager.QueuePrefix": "apjobs",
    "WorkDirectory.Name": "AetherAPCloud"
  },
  "DMTConfig": {
    "IsDataManagementTrackingEnabled": "true",
    "IsTrackingNoThrowOnError": "false",
    "DMTExtensionsDegreeOfParallelism": "10",
    "OutputProcessorDegreeOfParallelism": "5",
    "CaseLimitForLogger": "5",
    "DataAvailabilityCheckDataOlderThanTimeSpan": "29.00:00:00",
    "DataAvailabilityRequiredFreshnessTimeSpan": "1.00:00:00",
    "DataAvailabilityQuickCheckDataOlderThanTimeSpan": "00:30:00",
    "DataAvailabilityQuickCheckDataSubscription": "1aefdc5e-3a7c-4d71-a9f9-f5d3b03be19a",
    "DataAvailabilityTestRequiredFreshnessTimeSpan": "00:05:00",
    "DataAvailabilityTestCheckDataWorkspace": "reuse-runner-ws",
    "DefaultRetentionDays": "14"
  }
}