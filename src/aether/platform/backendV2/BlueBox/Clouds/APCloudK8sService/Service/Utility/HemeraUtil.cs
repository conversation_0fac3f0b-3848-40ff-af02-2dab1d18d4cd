﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Microsoft.Aether.APCloud.K8sService.Utility
{
    class HemeraUtil
    {
        // Copied from Hemera repo
        // https://msasg.visualstudio.com/Multi%20Tenancy/_git/Hemera?path=/src/HemeraManager/HemeraManager/JobSchedulerUtil.cs&version=GBdevelop&line=257&lineEnd=257&lineStartColumn=30&lineEndColumn=43&lineStyle=plain&_a=contents
        public static string DecorateJobId(string alias, string jobid)
        {
            string newJobId = jobid;
            string[] items = newJobId.Split(new string[] { "@@@" }, StringSplitOptions.None);
            items[1] = items[1].Substring(0, Math.Min(25, items[1].Length));
            newJobId = string.Join("___", items);
            string shortAlias = alias.Substring(0, Math.Min(8, alias.Length));
            newJobId = string.Format("{0}_{1}", shortAlias, newJobId);

            return "JD_" + newJobId.Replace("\\", "-");
        }

        public static string GetWebPortalLink(string owner, string jobid)
        {
            var alias = GetAlias(owner);
            return string.Format("https://magnetar/job-detail.html?jobName={0}", DecorateJobId(alias, jobid));
        }

        private static string GetAlias(string owner)
        {
            return owner.Split('\\')[1].Replace("$", "");
        }
    }
}
