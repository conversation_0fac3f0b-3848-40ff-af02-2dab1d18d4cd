﻿using Microsoft.Aether.APCloud.Service;
using Microsoft.Aether.CloudletCommon;
using Microsoft.MachineLearning.Common.Startup;

namespace Microsoft.Aether.APCloud.K8sService.EntryPoints
{
    [System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
    public class Program
    {
        public static void Main(string[] args)
        {
            new EntryPoint<CloudletStartup<APCloudContext, APCloudJobMetadata>>().DoEntryPoint(args);
        }
    }
}
