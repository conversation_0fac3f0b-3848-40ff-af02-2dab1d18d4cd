<Project Sdk="Microsoft.NET.Sdk" DefaultTargets="Publish;Build">
  <PropertyGroup>
    <PublishDir>$(BaseTargetDir)\app\aether\bluebox\BBCloudSubmitter</PublishDir>
    <OutputType>Exe</OutputType>
    <AssemblyName>BBCloudSubmitter</AssemblyName>
    <TargetFramework>net8.0</TargetFramework>
    <ResolveAssemblyWarnOrErrorOnTargetArchitectureMismatch>None</ResolveAssemblyWarnOrErrorOnTargetArchitectureMismatch>
    <HighEntropyVA>true</HighEntropyVA>
    <RootNamespace>Microsoft.Aether.CloudManager.BBCloudSubmitter</RootNamespace>
  </PropertyGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\..\..\RelInfra\RelInfra.Common.Library\src\Common\RelInfra.Common.csproj" />
    <ProjectReference Include="..\..\..\..\RelInfra\RelInfra.Instrumentation.Library\src\Microsoft.RelInfra.Instrumentation.csproj" />
    <ProjectReference Include="..\..\..\..\RelInfra\RelInfra.Storage.Library\src\Storage\RelInfra.Storage.csproj" />
    <ProjectReference Include="..\..\..\shared\Microsoft.Aether.BackendCommon\Microsoft.Aether.BackendCommon.csproj" />
    <ProjectReference Include="..\..\..\shared\Microsoft.Aether.DataContract.Backend\Microsoft.Aether.DataContract.Backend.csproj" />
    <ProjectReference Include="..\..\..\shared\Microsoft.Aether.DataContract\Microsoft.Aether.DataContracts.csproj" />
    <ProjectReference Include="..\..\..\CloudManager\CloudSubmitterCommon\Microsoft.Aether.CloudManager.CloudSubmitterCommon.csproj" />
    <ProjectReference Include="..\..\..\CloudManager\Common\Microsoft.Aether.CloudManager.Common.csproj" />
    <ProjectReference Include="..\..\AetherK8SBaseService\Microsoft.Aether.BlueBox.AetherK8SBaseService.csproj" />
    <ProjectReference Include="..\..\AetherK8SCommon\Microsoft.Aether.BlueBox.AetherK8SCommon.csproj" />
    <ProjectReference Include="..\..\CloudletProxyClient\Microsoft.Aether.BlueBox.CloudletProxyClient\Microsoft.Aether.BlueBox.CloudletProxyClient.csproj" />
    <ProjectReference Include="..\..\MetaStore\MetaStoreV2.Client\MetaStoreV2.Client.csproj" />
    <ProjectReference Include="..\..\RunHistoryClient\src\client\Microsoft.Aether.BlueBox.RunHistoryClient.csproj" />
    <ProjectReference Include="$(SrcRoot)\Common\WebApi\Common.WebApi.csproj" />
    <ProjectReference Include="$(SrcRoot)\Common\Core\Common.Core.csproj" />
  </ItemGroup>
  <ItemGroup>
    <None Include="..\..\..\shared\Scripts\start.sh">
      <Link>start.sh</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="..\..\CA\*.cer">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="Dockerfile">
      <Link>Dockerfile</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="App.config" />
    <None Include="..\..\..\CloudManager\CloudSubmitter\CloudSubmitter.ini">
      <Link>CloudSubmitter.ini</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="..\..\..\shared\SharedConfigurationSettings\SharedSettings.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="..\..\..\CloudManager\Common\storage.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="..\..\..\shared\SharedConfigurationSettings\BBSharedSettings.ini">
      <Link>BBSharedSettings.ini</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="System.Net.Security" />
  </ItemGroup>
</Project>
