﻿// <copyright file="BBCloudSubmitterService.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Aether.Backend.BlueBox.MetaStore.Client;
using Microsoft.Aether.BackendCommon;
using Microsoft.Aether.BackendCommon.ClusterHealthPoller;
using Microsoft.Aether.BackendCommon.Configuration;
using Microsoft.Aether.BlueBox.AetherK8SBaseService;
using Microsoft.Aether.BlueBox.CloudletProxyClient;
using Microsoft.Aether.CloudManager.CloudSubmitter;
using Microsoft.Aether.CloudManager.Common;
using Microsoft.Aether.DataContract.Backend.CloudManager;
using Microsoft.Aether.StorageDescription;
using Microsoft.AIPlatform.WorkloadIdentity;
using Microsoft.AspNetCore;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.MachineLearning.Common.WebApi.AppInsights;
using Microsoft.RelInfra.Instrumentation;
using Microsoft.RelInfra.Instrumentation.Logging;
using Microsoft.RelInfra.Storage;

namespace Microsoft.Aether.CloudManager.BBCloudSubmitter
{
    internal class CloudSubmitterService
    {
        private const string ServiceName = ServiceNames.CloudSubmitter;
        private const string ServiceConfigFilename = "CloudSubmitter.ini";
        private const string StorageConfigFilename = "storage.ini";
        private const string SharedConfigFilename = "SharedSettings.ini";
        private const string BBSharedConfigFilename = "BBSharedSettings.ini";

        private static async Task Main()
        {
            try
            {
                var k8sService = new K8sBaseService(serviceName: ServiceName,
                    configFilename: ServiceConfigFilename,
                    iniFileNames: new[] { SharedConfigFilename, StorageConfigFilename, BBSharedConfigFilename });

                k8sService.InitializeAsync().Wait();
                TelemetrySetupHelper.SetupATLTelemetryBeforeServiceStartup(ServiceName, k8sService);

                var submitterConfig = new CloudSubmitterConfiguration(k8sService.ApConfig, k8sService.SecretProvider);
                submitterConfig.VerifyConfig();

                CounterManager counterManager = k8sService.CounterManager;
                IWorkloadIdentityProvider workloadIdentityProvider = k8sService.WorkloadIdentityProvider;

                var sd = new StorageDescriptions();
                await InitializeLegacyStoragesAsync(
                    artifactPrefix: Env.AzurePrefix(),
                    submitterConfig: submitterConfig,
                    provisionedStoragesJson: StorageObjectFactory.ProvisionedStoragesFileName,
                    sd: sd);

                IStorageObjectFactory storageObjectFactory;
                if (workloadIdentityProvider?.IsAvailable == true)
                {
                    storageObjectFactory = new StorageObjectFactoryV2(counterManager, workloadIdentityProvider);
                    storageObjectFactory.LoadProvisionedStoragesFromFile(StorageObjectFactoryV2.ProvisionedStoragesFileName);
                }
                else
                {
                    storageObjectFactory = new StorageObjectFactory(counterManager);
                    storageObjectFactory.LoadProvisionedStoragesFromFile(StorageObjectFactory.ProvisionedStoragesFileName);
                }

                var initTasks = new List<Task>();
                IQueue<CloudSubmitterTask> cloudSubmitterQueue = storageObjectFactory.GetAzureQueue<CloudSubmitterTask>(sd.DcmShared.JobSubmission);
                initTasks.Add(cloudSubmitterQueue.InitializeQueueAsync());
                ITableStorage nodesTable = storageObjectFactory.GetAzureTable(sd.DcmShared.FastOrcNodes);
                initTasks.Add(nodesTable.CreateIfNotExistsAsync());
                ITableStorage submittedNodeTable = storageObjectFactory.GetAzureTable(sd.DcmShared.SubmittedNodes);
                initTasks.Add(submittedNodeTable.CreateIfNotExistsAsync());
                ITableStorage failedExperimentTable = storageObjectFactory.GetAzureTable(sd.DcmShared.FailedExperiments);
                initTasks.Add(failedExperimentTable.CreateIfNotExistsAsync());
                IAzureBlobContainerStorage queueStorage = storageObjectFactory.GetAzureBlobContainer(sd.CloudSubmitter.QueueBlobAccount);
                initTasks.Add(queueStorage.InitializeAsync());
                ITableStorage cancelRequestedExperimentsTable = storageObjectFactory.GetAzureTable(sd.DcmShared.CancelRequestedExperiments);
                initTasks.Add(cancelRequestedExperimentsTable.CreateIfNotExistsAsync());

                await Task.WhenAll(initTasks);

                CommonLogger.LogEntityInfo(submitterConfig.ServiceName, "Initializing JobRepository.");
                IExecutionJobRepository jobRepository = new FastOrcExecutionJobRepository(nodesTable, submittedNodeTable, counterManager);

                CommonLogger.LogEntityInfo(submitterConfig.ServiceName, "Setting up cloud systems.");
                var factory = new StoreFactory(baseUrl: submitterConfig.MetaStoreUrl, s2STokenProvider: k8sService.ServicePrincipalAadTokenProvider);
                var context = new DataContracts.StoreOperationContext { UserName = "cloudsubmitter", ClientName = "cloudmanager" };
                var dataTypeStore = factory.CreateDataTypeStore(context);
                var dataTypeRepository = new DatatypeRepository(dataTypeStore);
                var cloudSystemStore = factory.CreateCloudSystemStore(context);
                var cloudletProxyClient = new CloudletProxyClient(new CloudletProxyConfig(k8sService.ApConfig), k8sService.ServicePrincipalAadTokenProvider);
                var cloudSystemFactory = new CloudSystemFactory(
                    datatypeRepository: dataTypeRepository,
                    counter: counterManager,
                    shouldPopulateDataTypesForInputs: true, // azure flow requires data types for inputs
                    s2STokenProvider: k8sService.ServicePrincipalAadTokenProvider,
                    cloudletProxyClient: cloudletProxyClient);
                var cloudSystemRepository = new CloudSystemRepository(cloudSystemStore, cloudSystemFactory);
                await cloudSystemRepository.InitAsync();

                CommonLogger.LogEntityInfo(submitterConfig.ServiceName, "Initializing cloud submitter.");

                var taskPipeline = new ConcurrentQueue<Storable<CloudSubmitterTask>>();
                var queueAutoLoaderCancellationTokenSource = new CancellationTokenSource();
                QueueAutoLoader queueAutoLoader = new QueueAutoLoader(cloudSubmitterQueue: cloudSubmitterQueue,
                                                                      serviceConfig: submitterConfig,
                                                                      taskPipeline: taskPipeline,
                                                                      cts: queueAutoLoaderCancellationTokenSource,
                                                                      counterManager: counterManager);
                var autoLoaderCompletionTask = queueAutoLoader.RunAsync();

                var jobRequestHandler = new JobRequestHandler(submitterConfig, cloudSubmitterQueue, queueStorage, counterManager);

                var commonCounterManager = await k8sService.InitializeCommonCounterAsync();
                var submitter = new CloudSubmitter.CloudSubmitter(
                    submitterConfig,
                    cloudSystemRepository,
                    cloudSubmitterQueue,
                    queueStorage,
                    counterManager,
                    commonCounterManager,
                    jobRepository,
                    taskPipeline,
                    jobRequestHandler);

                GcCollectorTimer gcCollectorTimer = null;
                if (submitterConfig.ForceGarbageCollectionMinutesInterval > 0)
                {
                    gcCollectorTimer = new GcCollectorTimer(submitterConfig.ServiceName, submitterConfig.ForceGarbageCollectionMinutesInterval);
                }

                Task submitterServiceTask = submitter.RunAsync();
                CommonLogger.LogEntityInfo(submitterConfig.ServiceName, "Started processing");
                // Todo actually expose some status of submitter so we know it's actually doing stuff

                IWebHostBuilder hostBuilder = WebHost.CreateDefaultBuilder()
                    .UseKestrel(_ => _.Listen(System.Net.IPAddress.Any, 5000))
                    .Configure(app =>
                    {
                        app.UseMvc();
                    })
                    .ConfigureServices(services =>
                    {
                        services.ConfigureBasicServices(serviceName: ServiceName, baseService: k8sService, configuration: null);
                        services.AddMvc(options =>
                        {
                            options.EnableEndpointRouting = false;
                            if (Env.DeploymentType() != "onebox")
                            {
                                options.AddCommonAppInsightsComponents();
                            }
                        });
                    })
                    .ConfigureLogging(builder => builder.ClearProviders().AddCommonLogger());

                Task webHostTask = hostBuilder.Build().RunAsync();
                CommonLogger.LogEntityInfo(ServiceName, $"Workload identity available: {workloadIdentityProvider?.IsAvailable}.");

                await Task.WhenAny(submitterServiceTask, autoLoaderCompletionTask, webHostTask);

                if (gcCollectorTimer != null)
                {
                    GC.KeepAlive(gcCollectorTimer);
                }
            }
            catch (Exception e)
            {
                CommonLogger.LogEntityError(ServiceName, "Unhandled exception: [Error: {safe_exception_message}, Stack Trace: {stack_trace}]", e.Message, e.StackTrace);
                await Task.Delay(TimeSpan.FromSeconds(5));
            }
        }

        private static async Task InitializeLegacyStoragesAsync(
            string artifactPrefix,
            CloudSubmitterConfiguration submitterConfig,
            string provisionedStoragesJson,
            StorageDescriptions sd)
        {
            CommonLogger.LogInfo($"Azure table prefix is {artifactPrefix}");

            var legacyProvisioning = new LegacyProvisioning();
            legacyProvisioning.LegacyRegisterQueue(queueDef: sd.DcmShared.JobSubmission,
                                                   queueName: artifactPrefix + submitterConfig.CloudStorage.JobSubmissionQueue);

            legacyProvisioning.LegacyRegisterTableStorage(tableDef: sd.DcmShared.FastOrcNodes,
                                                          tableName: artifactPrefix + submitterConfig.CloudStorage.FastOrcNodesTableName);

            legacyProvisioning.LegacyRegisterTableStorage(tableDef: sd.DcmShared.SubmittedNodes,
                                                          tableName: artifactPrefix + submitterConfig.CloudStorage.SubmittedTableName);

            legacyProvisioning.LegacyRegisterTableStorage(tableDef: sd.DcmShared.CancelRequestedExperiments,
                                                          tableName: artifactPrefix + submitterConfig.CloudStorage.CancelRequestedExperimentsTableName);

            legacyProvisioning.LegacyRegisterTableStorage(tableDef: sd.DcmShared.FailedExperiments,
                                                          tableName: artifactPrefix + submitterConfig.CloudStorage.FailedTableName);

            legacyProvisioning.LegacyRegisterBlobContainer(blobDef: sd.CloudSubmitter.QueueBlobAccount,
                                                           containerName: artifactPrefix + submitterConfig.CloudStorage.QueueBlobContainerName);

            legacyProvisioning.LegacyRegisterConnectionString(name: StorageDescriptionsCloudSubmitter.StateStorageAccountName, connectionString: await submitterConfig.CloudStorage.GetAzureConnectionStringAsync());
            legacyProvisioning.LegacyRegisterConnectionString(name: StorageDescriptionsDcmShared.StateStorageAccountName, connectionString: await submitterConfig.CloudStorage.GetAzureConnectionStringAsync());

            legacyProvisioning.LegacyRegisterAzureStorageAccountName(name: StorageDescriptionsCloudSubmitter.StateStorageAccountName, accountName: Env.StorageAccountNameCloudManager());
            legacyProvisioning.LegacyRegisterAzureStorageAccountName(name: StorageDescriptionsDcmShared.StateStorageAccountName, accountName: Env.StorageAccountNameCloudManager());

            legacyProvisioning.SerializeIntoFile(provisionedStoragesJson);
        }
    }
}
