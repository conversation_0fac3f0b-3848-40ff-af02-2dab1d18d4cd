﻿// <copyright file="KeepaliveController.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Microsoft.AspNetCore.Mvc;

namespace Microsoft.Aether.CloudManager.BBCloudSubmitter
{
    public class KeepaliveController : Controller
    {
        // GET /keepalive
        [HttpGet]
        [Route("keepalive")]
        public IActionResult GetKeepalive()
        {
            return Ok("Keepalive response");
        }
    }
}
