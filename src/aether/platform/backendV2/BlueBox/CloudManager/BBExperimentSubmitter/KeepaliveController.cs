﻿// <copyright file="KeepaliveController.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Aether.BackendCommon.ClusterHealthPoller;
using Microsoft.Aether.BackendCommon.JobQueueProcessing;
using Microsoft.Aether.CloudManager.ExperimentSubmitterCommon;
using Microsoft.AspNetCore.Mvc;
using Microsoft.RelInfra.Instrumentation.Logging;

namespace Microsoft.Aether.CloudManager.BBExperimentSubmitter
{
    public class KeepaliveController : Controller
    {
        private const int MaxUnprocessedQueueTimeInSeconds = 20 * 60;

        private readonly JobQueueTracker _jobQueueTracker;
        private readonly ExperimentSubmitterConfig _submitterConfig;
        private readonly ClusterHealthPollerWrapper _healthWrapper;

        public KeepaliveController(JobQueueTracker jobQueueTracker, ExperimentSubmitterConfig submitterConfig, ClusterHealthPollerWrapper healthWrapper)
        {
            _jobQueueTracker = jobQueueTracker;
            _submitterConfig = submitterConfig;
            _healthWrapper = healthWrapper;
        }

        [HttpGet]
        [Route("dcm/keepalive")]
        public string KeepAlive()
        {
            return "Keepalive response";
        }

        [HttpGet]
        [Route("dcm/liveness")]
        public async Task<IActionResult> LivenessProbe()
        {
            // check whether queues are stuck without processing new jobs
            DateTime lastProcessedTime = _jobQueueTracker.GetLastQueueAvailableTime();
            if (lastProcessedTime.AddSeconds(MaxUnprocessedQueueTimeInSeconds) < DateTime.UtcNow)
            {
                if (_submitterConfig.EnabledClusterHealthPoller)
                {
                    var health = await _healthWrapper.CheckClusterHealthy();
                    if (!health)
                    {
                        return Ok("liveness response");
                    }
                }

                CommonLogger.LogEntityError("LivenessProbe", $"Liveness probe fails for stuck processing queue. last processed time {lastProcessedTime}");
                var pickedUpMessages = await _jobQueueTracker.GetSnapshotPickedUpJobMessagesAsync();
                string logMessage = string.Join(";", pickedUpMessages.Select(m => $"jobId: {m.JobId}, queue: {m.QueueName}, pickedupTime: {m.PickedUpTimeUtc}"));
                CommonLogger.LogEntityError("LivenessProbe", $"{pickedUpMessages.Count()} messages in queue {logMessage}");
                return StatusCode(500, $"Liveness probe fails for stuck processing queue. last processed time {lastProcessedTime}");
            }

            return Ok("liveness response");
        }

        // For testing purposes
        [HttpGet]
        [Route("dcm/keepalive2")]
        public string KeepAlive2()
        {
            return "Keepalive response";
        }
    }
}
