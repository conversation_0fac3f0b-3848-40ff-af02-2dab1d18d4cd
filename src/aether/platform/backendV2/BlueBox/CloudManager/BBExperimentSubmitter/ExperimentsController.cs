﻿// <copyright file="ExperimentsController.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.Aether.BackendCommon;
using Microsoft.Aether.BackendCommon.Extensions;
using Microsoft.Aether.BlueBox.DistributedTracing;
using Microsoft.Aether.CloudManager.Common;
using Microsoft.Aether.CloudManager.ExperimentSubmitterCommon;
using Microsoft.Aether.DataContracts;
using Microsoft.Aether.DataContracts.Entity;
using Microsoft.AspNetCore.Mvc;
using Constants = Microsoft.Aether.CloudManager.Common.Utils.Constants;

namespace Microsoft.Aether.CloudManager.BBExperimentSubmitter
{
    public class ExperimentsController : Controller
    {
        private readonly IExperimentSubmitter _experimentSubmitter;
        private readonly IWorkspaceResourcesCache _workspaceResourcesCache;
        private readonly CrossServiceSpanMeasurement _crossServiceSpanMeasurement;
        private readonly IExecutionJobRepository _executionJobRepository;

        public ExperimentsController(IExperimentSubmitter experimentSubmitter, IWorkspaceResourcesCache workspaceResourcesCache, CrossServiceSpanMeasurement crossServiceSpanMeasurement = null, IExecutionJobRepository executionJobRepository = null)
        {
            _experimentSubmitter = experimentSubmitter;
            _workspaceResourcesCache = workspaceResourcesCache;
            _crossServiceSpanMeasurement = crossServiceSpanMeasurement;
            _executionJobRepository = executionJobRepository;
        }

        [HttpPost]
        [Route("dcm/v2/experiments")]
        public async Task SubmitExperimentV2([FromBody] ExperimentSubmissionInfoV2 experimentSubmissionInfo)
        {
            if (experimentSubmissionInfo == null)
            {
                throw new ArgumentNullException(nameof(experimentSubmissionInfo), "ExperimentSubmissionInfo can't be null.");
            }

            Request.SetCorrelationId(experimentSubmissionInfo.ExperimentId);

            var customContext = new List<KeyValuePair<string, object>>()
            {
                KeyValuePair.Create<string, object>(Constants.ExperimentIdField, experimentSubmissionInfo.ExperimentId),
                KeyValuePair.Create<string, object>(Constants.PipelineRunIdField, experimentSubmissionInfo.RunId),
            };

            this.SetContextToLogger(experimentSubmissionInfo.WorkspaceIdentity, customContext);
            await _experimentSubmitter.SubmitExperimentAsync(experimentSubmissionInfo);

            if (_crossServiceSpanMeasurement != null)
            {
                _crossServiceSpanMeasurement.RecordSpanStartTime(PipelineSpanKind.PreparingPipeline, experimentSubmissionInfo.ExperimentId, experimentSubmissionInfo.RunId ?? experimentSubmissionInfo.ExperimentId, DateTime.UtcNow);
            }
        }

        [HttpDelete]
        [Route("dcm/v2"
           + "/subscriptions/{subscriptionId}"
           + "/resourceGroups/{resourceGroupName}"
           + "/providers/Microsoft.MachineLearningServices"
           + "/workspaces/{workspaceName}/experiments/{id}")]
        public async Task<IActionResult> CancelExperimentV2Workspace(string id, string subscriptionId, string resourceGroupName, string workspaceName, [FromQuery] bool forceCancel = false, [FromQuery] bool cancelFinalPhase = false)
        {
            var workspaceIdentity = new WorkspaceIdentity()
            {
                SubscriptionId = subscriptionId,
                ResourceGroupName = resourceGroupName,
                WorkspaceName = workspaceName,
                // TODO: Populate CreatedBy properly
                WorkspaceId = await _workspaceResourcesCache?.GetWorkspaceIdAsync(subscriptionId, resourceGroupName, workspaceName, CreatedBy.GetSystemCreatedByObject())
            };

            var customContext = new List<KeyValuePair<string, object>>()
            {
                KeyValuePair.Create<string, object>(Constants.ExperimentIdField, id),
            };

            this.SetContextToLogger(workspaceIdentity, customContext);
            return await CancelExperimentV2Internal(
                id,
                workspaceIdentity,
                forceCancel: forceCancel,
                cancelFinalPhase: cancelFinalPhase);
        }

        [HttpDelete]
        [Route("dcm/v2/experiments/{id}")]
        public async Task<IActionResult> CancelExperimentV2(string id)
        {
            var customContext = new List<KeyValuePair<string, object>>()
            {
                KeyValuePair.Create<string, object>(Constants.ExperimentIdField, id),
            };

            this.SetContextToLogger(workspaceIdentity: null, customContext);
            return await CancelExperimentV2Internal(id, workspaceIdentity: null);
        }

        /// <summary>
        /// For geneva action get node info.
        /// </summary>
        [HttpGet]
        [Route("dcm/v2/experiment/{experimentId}/node/{executionNodeId}")]
        public async Task<NodesTableEntity> GetExecutionNodeTableEntityAsync(string experimentId, string executionNodeId)
        {
            var customContext = new List<KeyValuePair<string, object>>()
            {
                KeyValuePair.Create<string, object>(Constants.ExperimentIdField, experimentId),
            };

            this.SetContextToLogger(workspaceIdentity: null, customContext);
            return await _executionJobRepository.GetExecutionNodeTableEntityAsync(experimentId, executionNodeId);
        }

        private async Task<IActionResult> CancelExperimentV2Internal(string id, WorkspaceIdentity workspaceIdentity, bool forceCancel = false, bool cancelFinalPhase = false)
        {
            if (string.IsNullOrWhiteSpace(id))
            {
                throw new ArgumentNullException(nameof(id), "Experiment id can't be null or empty");
            }

            Request.SetCorrelationId(id);
            return await _experimentSubmitter.CancelExperimentAsync(id, workspaceIdentity, forceCancel, cancelFinalPhase);
        }
    }
}