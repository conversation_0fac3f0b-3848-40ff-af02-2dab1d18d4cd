﻿// <copyright file="NotifyController.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.IO;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Aether.BackendCommon;
using Microsoft.Aether.CloudManager.Common;
using Microsoft.Aether.CloudManager.ExperimentSubmitterCommon;
using Microsoft.Aether.DataContract.Backend.CloudManager;
using Microsoft.Aether.DataContracts;
using Microsoft.Aether.DataContracts.Entity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.MachineLearning.Common.ServiceBus;
using Microsoft.RelInfra.Instrumentation.Logging;

namespace Microsoft.Aether.CloudManager.BBExperimentSubmitter
{
    public class NotifyController : Controller
    {
        private readonly ILatencyMeasurer _latencyMeasurer;
        private readonly IWorkspaceResourcesCache _workspaceResourcesCache;
        private readonly IServiceBusTopicPublisher _notifyTopicPublisher;
        private readonly IExperimentRepository _experimentRepository;

        public NotifyController(
            ILatencyMeasurer latencyMeasurer,
            IWorkspaceResourcesCache workspaceResourcesCache,
            IServiceBusTopicPublisher notifyTopicPublisher,
            IExperimentRepository experimentRepository)
        {
            _latencyMeasurer = latencyMeasurer;
            _workspaceResourcesCache = workspaceResourcesCache;
            _notifyTopicPublisher = notifyTopicPublisher;
            _experimentRepository = experimentRepository;
        }

        [HttpPost]
        [Route("dcm/v2"
           + "/subscriptions/{subscriptionId}"
           + "/resourceGroups/{resourceGroupName}"
           + "/providers/Microsoft.MachineLearningServices"
           + "/workspaces/{workspaceName}/jobcomplete/{jobId}")]
        public async Task JobCompleteAsync(string jobId, string subscriptionId, string resourceGroupName, string workspaceName)
        {
            if (string.IsNullOrEmpty(jobId))
            {
                throw new ArgumentException("jobId can't be null or empty", nameof(jobId));
            }

            CommonLogger.LogEntityInfo(jobId, "ExperimentSubmitter's Job complete notification");

            string experimentId = CloudSystemHelper.GetExperimentIdFromRequestId(jobId);
            if (string.IsNullOrEmpty(experimentId))
            {
                throw new ArgumentException("jobId has incorrect format - " + jobId, nameof(jobId));
            }

            string pipelineRunId;
            if (Request.Body == null)
            {
                throw new ArgumentException("request body needs to contains pipelineRunId " + jobId, nameof(jobId));
            }

            using (StreamReader reader = new StreamReader(Request.Body, Encoding.UTF8))
            {
                pipelineRunId = await reader.ReadToEndAsync();
                CommonLogger.LogEntityInfo(jobId, $"job complete notification pipelineRunId={pipelineRunId}");
            }

            // for old jobs, body is jobid, for new jobs, body is pipelinerunid.
            _ = _latencyMeasurer.StartMeasuringLatencyAsync(jobId);

            try
            {
                await ExperimentProcessInternalAsync(experimentId, new WorkspaceIdentity
                {
                    SubscriptionId = subscriptionId,
                    ResourceGroupName = resourceGroupName,
                    WorkspaceName = workspaceName,
                    // TODO: Populate CreatedBy properly
                    WorkspaceId = await _workspaceResourcesCache?.GetWorkspaceIdAsync(subscriptionId, resourceGroupName, workspaceName, CreatedBy.GetSystemCreatedByObject())
                });
            }
            catch (Exception e)
            {
                CommonLogger.LogEntityError(jobId, "job complete notification error {safe_exception_message}", e);
            }
        }

        [HttpPost]
        [Route("dcm/v2"
            + "/subscriptions/{subscriptionId}"
            + "/resourceGroups/{resourceGroupName}"
            + "/providers/Microsoft.MachineLearningServices"
            + "/workspaces/{workspaceName}/stepRunComplete/{pipelineRunId}/{stepRunId}")]
        public async Task StepRunCompleteAsync(string pipelineRunId, string stepRunId, string subscriptionId, string resourceGroupName, string workspaceName)
        {
            if (string.IsNullOrEmpty(pipelineRunId))
            {
                throw new ArgumentException("pipelineRunId can't be null or empty", nameof(pipelineRunId));
            }

            if (string.IsNullOrEmpty(stepRunId))
            {
                throw new ArgumentException("stepRunId can't be null or empty", nameof(stepRunId));
            }

            CommonLogger.LogEntityInfo(stepRunId, "ExperimentSubmitter's stepRunComplete notification");

            try
            {
                var workspaceIdentity = new WorkspaceIdentity
                {
                    SubscriptionId = subscriptionId,
                    ResourceGroupName = resourceGroupName,
                    WorkspaceName = workspaceName,
                    WorkspaceId = await _workspaceResourcesCache?.GetWorkspaceIdAsync(subscriptionId, resourceGroupName, workspaceName, CreatedBy.GetSystemCreatedByObject())
                };

                // use pipelinerunId here as experimentId can also get the corresponding experiment entity, metastore has handled this case internal
                var experimentEntity = await _experimentRepository.GetExperimentAsync(experimentId: pipelineRunId, workspaceIdentity: workspaceIdentity).ConfigureAwait(false);

                _ = _latencyMeasurer.StartMeasuringLatencyAsync(stepRunId);

                await ExperimentProcessInternalAsync(experimentEntity.Id, new WorkspaceIdentity
                {
                    SubscriptionId = subscriptionId,
                    ResourceGroupName = resourceGroupName,
                    WorkspaceName = workspaceName,
                    WorkspaceId = workspaceIdentity.WorkspaceId,
                });
            }
            catch (Exception e)
            {
                CommonLogger.LogEntityError(stepRunId, "stepRunComplete notification error {safe_exception_message}", e);
            }
        }

        [HttpPost]
        [Route("dcm/v2/notify/jobcomplete")]
        public async Task JobCompleteAsync(string jobId)
        {
            if (string.IsNullOrEmpty(jobId))
            {
                throw new ArgumentException("jobId can't be null or empty", nameof(jobId));
            }

            CommonLogger.LogEntityInfo(jobId, "ExperimentSubmitter's Job complete notification");

            string experimentId = CloudSystemHelper.GetExperimentIdFromRequestId(jobId);
            if (string.IsNullOrEmpty(experimentId))
            {
                throw new ArgumentException("jobId has incorrect format - " + jobId, nameof(jobId));
            }

            Task t = _latencyMeasurer.StartMeasuringLatencyAsync(jobId);
            await ExperimentProcessInternalAsync(experimentId, workspaceIdentity: null);
        }

        [HttpPost]
        [Route("dcm/v2/notify/processexperiment")]
        public async Task ExperimentProcessAsync([FromBody] string experimentId)
        {
            await ExperimentProcessInternalAsync(experimentId, workspaceIdentity: null);
        }

        public virtual async Task ExperimentProcessInternalAsync(string experimentId, WorkspaceIdentity workspaceIdentity)
        {
            CommonLogger.LogEntityInfo(experimentId, "Experiment send notify event to servicebus start");

            Request.SetCorrelationId(experimentId);
            var notifyEvent = new ExperimentQueueEntity()
            {
                ExperimentId = experimentId,
                WorkspaceIdentity = workspaceIdentity,
                ExperimentQueueMessageType = ExperimentQueueMessageType.ProcessExperiment,
            };
            var envelope = new Envelope<ExperimentQueueEntity>(notifyEvent);
            envelope.SessionId = experimentId;

            await _notifyTopicPublisher.SendAsync(envelope);
            CommonLogger.LogEntityInfo(experimentId, "Experiment send notify event to servicebus end");
        }
    }
}
