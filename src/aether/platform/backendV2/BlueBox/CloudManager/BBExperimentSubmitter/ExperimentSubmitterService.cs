﻿// <copyright file="ExperimentSubmitterService.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.IO;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Threading.Tasks;
using Aether.Backend.BlueBox.MetaStore.Client;
using Aether.Backend.CacheClient;
using Microsoft.Aether.BackendCommon;
using Microsoft.Aether.BackendCommon.ClusterHealthPoller;
using Microsoft.Aether.BackendCommon.Configuration;
using Microsoft.Aether.BackendCommon.JobQueueProcessing;
using Microsoft.Aether.BackendCommon.Time;
using Microsoft.Aether.BlueBox.AetherK8SBaseService;
using Microsoft.Aether.BlueBox.AetherK8SCommon;
using Microsoft.Aether.BlueBox.AetherK8SCommon.Auth;
using Microsoft.Aether.BlueBox.ArtifactClient;
using Microsoft.Aether.BlueBox.AzDevOpsClient;
using Microsoft.Aether.BlueBox.CloudletProxyClient;
using Microsoft.Aether.BlueBox.Clouds.Common;
using Microsoft.Aether.BlueBox.CredentialServiceClient;
using Microsoft.Aether.BlueBox.DataStoreClient;
using Microsoft.Aether.BlueBox.DistributedTracing;
using Microsoft.Aether.BlueBox.LogRepository;
using Microsoft.Aether.BlueBox.RunHistoryClient;
using Microsoft.Aether.BlueBox.S2S.Common;
using Microsoft.Aether.BlueBox.Web.Common;
using Microsoft.Aether.BlueBox.WorkspaceResourcesClient;
using Microsoft.Aether.CloudManager.Common;
using Microsoft.Aether.CloudManager.Common.ErrorHandling;
using Microsoft.Aether.CloudManager.Common.TableEntityBatchUpdaters;
using Microsoft.Aether.CloudManager.Common.Web;
using Microsoft.Aether.CloudManager.ExperimentSubmitterCommon;
using Microsoft.Aether.CloudManager.ExperimentSubmitterCommon.CloudRunConfigurationMerger;
using Microsoft.Aether.CloudManager.ExperimentSubmitterCommon.JobGenerator;
using Microsoft.Aether.CloudManager.ExperimentSubmitterCommon.SubmissionFsm;
using Microsoft.Aether.Clouds.CloudCommon.ServiceBus;
using Microsoft.Aether.DataContracts;
using Microsoft.Aether.QuotaService.Shared;
using Microsoft.Aether.S2S.Common;
using Microsoft.Aether.StorageDescription;
using Microsoft.Aether.TaggedQueueManager;
using Microsoft.Aether.TaggedQueueManager.CloudTags;
using Microsoft.AIPlatform.CertificateReloader;
using Microsoft.AIPlatform.Common.Metrics;
using Microsoft.AIPlatform.Telemetry.Contracts.Metrics;
using Microsoft.AIPlatform.WorkloadIdentity;
using Microsoft.AspNetCore;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Azure.ServiceBus;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.MachineLearning.Common.Caching;
using Microsoft.MachineLearning.Common.Core.Metrics;
using Microsoft.MachineLearning.Common.ServiceBus;
using Microsoft.MachineLearning.Common.Startup.Telemetry;
using Microsoft.MachineLearning.Common.WebApi.AppInsights;
using Microsoft.MachineLearning.Dataset.Client;
using Microsoft.MachineLearning.Pipeline.Common;
using Microsoft.RelInfra.Common.Configuration;
using Microsoft.RelInfra.Common.RetryExecution.Policy;
using Microsoft.RelInfra.Instrumentation;
using Microsoft.RelInfra.Instrumentation.Logging;
using Microsoft.RelInfra.Instrumentation.Logging.LoggerConversion;
using Microsoft.RelInfra.Storage;
using RetryStrategy = Microsoft.Aether.Clouds.CloudCommon.ServiceBus.RetryStrategy;
using ServiceBusEnvelopeMessageConverter = Microsoft.Aether.Clouds.CloudCommon.ServiceBus.ServiceBusEnvelopeMessageConverter;
using ServiceBusMessageCompressor = Microsoft.Aether.Clouds.CloudCommon.ServiceBus.ServiceBusMessageCompressor;
using ServiceBusMessageSerializer = Microsoft.Aether.Clouds.CloudCommon.ServiceBus.ServiceBusMessageSerializer;
using ServiceBusTopicPublisher = Microsoft.Aether.Clouds.CloudCommon.ServiceBus.ServiceBusTopicPublisher;

[assembly: InternalsVisibleTo("Microsoft.Aether.CloudManager.ExperimentSubmitter.Test")]

namespace Microsoft.Aether.CloudManager.BBExperimentSubmitter
{
    [System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
    public static class ExperimentSubmitterService
    {
        private const string ServiceName = ServiceNames.ExperimentSubmitter;
        private const string ServiceConfigFilename = "ExperimentSubmitter.ini";
        private const string StorageConfigFilename = "storage.ini";
        private const string SharedConfigFilename = "SharedSettings.ini";
        private const string NotificationConfigFilename = "notification.ini";
        private const string BBSharedConfigFilename = "BBSharedSettings.ini";
        private const string DistributedTracingConfigFileName = "DistributedTracing.ini";

        private static void Main()
        {
            Task mainTask = MainAsync();

            while (!mainTask.IsCompleted)
            {
                Thread.Sleep(TimeSpan.FromSeconds(5));
            }

            mainTask.Wait();
        }

        private static async Task MainAsync()
        {
            try
            {
                var k8sService = new K8sBaseService(serviceName: ServiceName,
                    configFilename: ServiceConfigFilename,
                    iniFileNames: new[] {
                        SharedConfigFilename,
                        StorageConfigFilename,
                        NotificationConfigFilename,
                        BBSharedConfigFilename,
                        DistributedTracingConfigFileName,
                    });
                k8sService.InitializeAsync().Wait();
                TelemetrySetupHelper.SetupATLTelemetryBeforeServiceStartup(ServiceName, k8sService);

                var submitterConfig = new ExperimentSubmitterConfig(k8sService.ApConfig, k8sService.SecretProvider);
                submitterConfig.VerifyConfig();

                var amlS2SServicePrincipalConfig = new AmlS2SServicePrincipalConfig(k8sService.ApConfig);
                var amlS2SAllowedIdsConfig = new AmlS2SAllowedIdsConfig(k8sService.ApConfig);

                var distributedTracingConfig = new DistributedTracingConfig(k8sService.ApConfig);
                distributedTracingConfig.VerifyConfig();

                CounterManager counterManager = k8sService.CounterManager;
                IWorkloadIdentityProvider workloadIdentityProvider = k8sService.WorkloadIdentityProvider;

                CommonLogger.LogEntityInfo(submitterConfig.ServiceName, "Initializing azure storage...");

                var sd = new StorageDescriptions();
                await InitializeLegacyStoragesAsync(submitterConfig, StorageObjectFactoryV2.ProvisionedStoragesFileName, sd);

                IStorageObjectFactory storageObjectFactory;
                ITaggedQueueManager<AssignedQueueIdTags, string> taggedExperimentQueueManager;
                ITaggedQueueManager<AssignedQueueIdTags, ExperimentProcessingEntity> taggedQueueManager;
                if (workloadIdentityProvider?.IsAvailable == true)
                {
                    storageObjectFactory = new StorageObjectFactoryV2(counterManager, workloadIdentityProvider);
                    storageObjectFactory.LoadProvisionedStoragesFromFile(StorageObjectFactoryV2.ProvisionedStoragesFileName);

                    taggedExperimentQueueManager = new AzureTaggedQueueManagerV2<AssignedQueueIdTags, string>(
                        serviceUri: Env.GetQueueStorageEndpointUri(Env.StorageAccountNameNodeProcessorQueue()),
                        credential: workloadIdentityProvider.Credential,
                        queuePrefix: GetLegacyAzurePrefix() + submitterConfig.ExperimentQueueManagerPrefix,
                        timerFactory: new TimerFactory(),
                        updateTimeSpan: submitterConfig.AzureQueueManagerUpdatePeriod);
                    taggedQueueManager = new AzureTaggedQueueManagerV2<AssignedQueueIdTags, ExperimentProcessingEntity>(
                        serviceUri: Env.GetQueueStorageEndpointUri(Env.StorageAccountNameExperimentSubmitterQueue()),
                        credential: workloadIdentityProvider.Credential,
                        queuePrefix: GetLegacyAzurePrefix() + submitterConfig.AzureQueueManagerPrefix,
                        timerFactory: new TimerFactory(),
                        updateTimeSpan: submitterConfig.AzureQueueManagerUpdatePeriod);
                }
                else
                {
                    storageObjectFactory = new StorageObjectFactory(counterManager);
                    storageObjectFactory.LoadProvisionedStoragesFromFile(StorageObjectFactory.ProvisionedStoragesFileName);

                    taggedExperimentQueueManager = new AzureTaggedQueueManager<AssignedQueueIdTags, string>(
                        connectionString: storageObjectFactory.GetAzureConnectionString(StorageDescriptionsNodeProcessor.QueueStorageAccountName),
                        queuePrefix: GetLegacyAzurePrefix() + submitterConfig.ExperimentQueueManagerPrefix,
                        timerFactory: new TimerFactory(),
                        updateTimeSpan: submitterConfig.AzureQueueManagerUpdatePeriod);
                    taggedQueueManager = new AzureTaggedQueueManager<AssignedQueueIdTags, ExperimentProcessingEntity>(
                        connectionString: storageObjectFactory.GetAzureConnectionString(StorageDescriptionsExperimentSubmitter.QueueStorageAccountName),
                        queuePrefix: GetLegacyAzurePrefix() + submitterConfig.AzureQueueManagerPrefix,
                        timerFactory: new TimerFactory(),
                        updateTimeSpan: submitterConfig.AzureQueueManagerUpdatePeriod);
                }

                var initTasks = new List<Task>();
                ITableStorage fastOrcNodesTable = storageObjectFactory.GetAzureTable(sd.DcmShared.FastOrcNodes);
                initTasks.Add(fastOrcNodesTable.CreateIfNotExistsAsync());
                ITableStorage operatorNodesTable = storageObjectFactory.GetAzureTable(sd.DcmShared.OperatorNodes);
                initTasks.Add(operatorNodesTable.CreateIfNotExistsAsync());
                ITableStorage controlReferenceNodesTable = storageObjectFactory.GetAzureTable(sd.DcmShared.ControlReferenceNodes);
                initTasks.Add(controlReferenceNodesTable.CreateIfNotExistsAsync());
                ITableStorage submittedNodeTable = storageObjectFactory.GetAzureTable(sd.DcmShared.SubmittedNodes);
                initTasks.Add(submittedNodeTable.CreateIfNotExistsAsync());
                ITableStorage cancelRequestedExperimentsStorage = storageObjectFactory.GetAzureTable(sd.DcmShared.CancelRequestedExperiments);
                initTasks.Add(cancelRequestedExperimentsStorage.CreateIfNotExistsAsync());
                ITableStorage failingStorage = storageObjectFactory.GetAzureTable(sd.DcmShared.FailedExperiments);
                initTasks.Add(failingStorage.CreateIfNotExistsAsync());
                ITableStorage experimentSubmissionStateStorage = storageObjectFactory.GetAzureTable(sd.ExperimentSubmitter.SubmissionState, defaultMaxExecutionTimeInSeconds: submitterConfig.MetaStoreTimeout.TotalSeconds);
                initTasks.Add(experimentSubmissionStateStorage.CreateIfNotExistsAsync());
                ITableStorage pipelineSpanTable = storageObjectFactory.GetAzureTable(sd.DcmShared.PipelineSpan);
                initTasks.Add(pipelineSpanTable.CreateIfNotExistsAsync());

                initTasks.Add(taggedExperimentQueueManager.InitializeAsync());
                initTasks.Add(taggedQueueManager.InitializeAsync());

                IQueue<string> notifyQueue = storageObjectFactory.GetAzureQueue<string>(sd.DcmShared.Notifications);
                initTasks.Add(notifyQueue.InitializeQueueAsync());

                IAzureBlobContainerStorage experimentLockBlobStorage = storageObjectFactory.GetAzureBlobContainer(sd.ExperimentSubmitter.ExperimentSubmissionLockStorage);
                ILeasableStorage experimentLockStorage = new AzureBlobLeasableContainerStorageV2(experimentLockBlobStorage);

                await Task.WhenAll(initTasks);

                CommonLogger.LogEntityInfo(submitterConfig.ServiceName, "Initializing azure storage...done");

                var clusterHealthPoller = new ClusterHealthPoller();
                var amlRunMetricConfig = new AmlRunMetricServiceConfig(k8sService.ApConfig);
                var amlRunMetricClient = new AmlRunMetricClient(amlRunMetricConfig, k8sService.ServicePrincipalAadTokenProvider, k8sService.CounterManager);
                var amlRunHistoryConfig = new AmlRunHistoryConfig(k8sService.ApConfig);
                var runHistoryClient = new RunHistorySvcClient(
                    amlRunHistoryConfig,
                    k8sService.ServicePrincipalAadTokenProvider,
                    amlRunMetricClient,
                    k8sService.CounterManager,
                    clusterHealthPoller);
                var credentialServiceConfig = new CredentialServiceConfig(k8sService.ApConfig);
                var credentialServiceClient = new CredentialServiceClient(credentialServiceConfig, k8sService.ServicePrincipalAadTokenProvider, k8sService.CounterManager, timeout: submitterConfig.RequestTimeout);
                var azDevOpsClientConfig = new AzDevOpsClientConfig(k8sService.ApConfig);
                var azDevOpsClient = new AzDevOpsClient(azDevOpsClientConfig, credentialServiceClient, k8sService.CounterManager);

                var workspaceResourcesConfig = new WorkspaceResourcesConfig(k8sService.ApConfig);
                var amlWorkspaceResourcesClient = new WorkspaceResourcesClient(workspaceResourcesConfig, k8sService.ServicePrincipalAadTokenProvider, k8sService.CounterManager, timeout: submitterConfig.RequestTimeout);

                var workspaceResourcesCache = new WorkspaceResourcesCache(distributedCache: null, memoryCache: new MemoryCache(new MemoryCacheOptions()), workspaceResourcesClient: amlWorkspaceResourcesClient, counterManager);
                MsiJobStdOutLoggerFactory msiJobStdOutLoggerFactory = new MsiJobStdOutLoggerFactory(workspaceResourcesCache, counterManager);

                var errorResponseBuilder = new ErrorResponseBuilder();
                var exceptionInterpretationPreProcessors = ExceptionInterpretationPreProcessors.DefaultExceptionInterpretationPreProcessors;
                var exceptionInterpretationSelector = new ExceptionInterpretationSelector(exceptionInterpretationPreProcessors);
                var interpreterCandidates = InterpreterCandidates.DefaultInterpreterCandidates;
                var errorResponseHelper = new ErrorResponseHelper(errorResponseBuilder, exceptionInterpretationSelector, interpreterCandidates);

                IExperimentRunStateUpdater experimentStateUpdater = new ExperimentRunStateUpdater(runHistoryClient, counterManager, errorResponseHelper);

                var storeFactory = new StoreFactory(baseUrl: submitterConfig.MetaStoreUrl, s2STokenProvider: k8sService.ServicePrincipalAadTokenProvider, timeout: submitterConfig.MetaStoreTimeout);
                var context = new StoreOperationContext { UserName = submitterConfig.ServiceName, ClientName = "CloudManager" };
                var experimentRepository = new ExperimentRepository(
                    experimentStore: storeFactory.CreateExperimentStore(context),
                    cancelRequestedExperimentsTable: cancelRequestedExperimentsStorage,
                    runStatusUpdater: experimentStateUpdater,
                    counterManager: counterManager,
                    failedExperimentsTable: failingStorage,
                    azDevOpsClient: azDevOpsClient,
                    errorResponseHelper: errorResponseHelper);

                var dataTypeStore = storeFactory.CreateDataTypeStore(context);
                var dataTypeRepository = new DatatypeRepository(dataTypeStore);
                var cloudletProxyClient = new CloudletProxyClient(new CloudletProxyConfig(k8sService.ApConfig), k8sService.ServicePrincipalAadTokenProvider);
                var cloudSystemFactory = new CloudSystemFactory(
                    datatypeRepository: dataTypeRepository,
                    counter: counterManager,
                    shouldPopulateDataTypesForInputs: false, // autopilot flow requires data types for inputs to be null, otherwise cloudlets fail to store entities for jobs with lots of inputs
                    s2STokenProvider: k8sService.ServicePrincipalAadTokenProvider,
                    cloudletProxyClient: cloudletProxyClient);

                var cloudSystemRepository = new CloudSystemRepository(storeFactory.CreateCloudSystemStore(context), cloudSystemFactory);

                // ServiceBus
                var serializer = new ServiceBusMessageSerializer();
                var compressor = new ServiceBusMessageCompressor();
                var converter = new ServiceBusEnvelopeMessageConverter(serializer, compressor);

                ITopicClient notifyEventSenderClient = null;
                if (workloadIdentityProvider?.IsAvailable == true)
                {
                    string endpoint = Env.GetServiceBusEndpoint(Env.ServiceBusAccountNamePipelineCommon());
                    var tokenAdapter = new LegacyServiceBusCredentialAdapter(workloadIdentityProvider.Credential);
                    notifyEventSenderClient = new TopicClient(
                        endpoint: endpoint,
                        entityPath: Env.NotificationTopicName(),
                        tokenProvider: tokenAdapter,
                        retryPolicy: RetryPolicyFactory.GetPolicy(RetryStrategy.Exponential));
                }
                else
                {
                    CommonLogger.LogEntityInfo(nameof(TopicClient), $"Construct client with connection string. TopicName: [{Env.NotificationTopicName()}].");
                    notifyEventSenderClient = new TopicClient(Env.DcmServiceBusConnectionString(), Env.NotificationTopicName(), RetryPolicyFactory.GetPolicy(RetryStrategy.Exponential));
                }

                var notifyEventTopicPublisher = new ServiceBusTopicPublisher(notifyEventSenderClient, converter);
                var operatorBatchUpdatersManager = new BatchUpdatersManager<OperatorNodesTableEntity>(operatorNodesTable, counterManager);

                var operatorJobRepository = new OperatorJobRepository(
                    fastOrcNodesTable,
                    operatorNodesTable,
                    counterManager,
                    operatorBatchUpdatersManager);

                var graphPortsRepository = new GraphPortsRepository(fastOrcNodesTable, operatorNodesTable, counterManager);
                var controlRefNodeBatchUpdatersManager = new BatchUpdatersManager<ControlReferenceNodeEntity>(controlReferenceNodesTable, counterManager);

                var controlReferenceNodesRepository = new ControlReferenceNodesRepository(
                    controlReferenceNodesTable,
                    counterManager,
                    controlRefNodeBatchUpdatersManager);

                var artifactClient = new S2SArtifactSvcClient(
                    config: new ArtifactConfig(k8sService.ApConfig, ServiceName),
                    servicePrincipalTokenProvider: k8sService.ServicePrincipalAadTokenProvider,
                    retryPolicy: new LinearRetryPolicy(TimeSpan.FromSeconds(1), 2),
                    retryOnTooManyRequestsException: true,
                    counters: k8sService.CounterManager,
                    clusterHealthPoller: clusterHealthPoller,
                    timeout: submitterConfig.ArtifactServiceRequestTimeout);

                var artifactProcessor = new ArtifactProcessor(workspaceResourcesCache, artifactClient, counterManager);
                var controlOutputsProcessor = new ControlOutputsProcessor(
                    runHistoryClient,
                    artifactProcessor,
                    counterManager,
                    k8sService.ApConfig);
                var fastOrcJobRepository = new FastOrcExecutionJobRepository(
                    fastOrcNodesTable,
                    operatorNodesTable,
                    submittedNodeTable,
                    controlReferenceNodesTable,
                    counterManager,
                    experimentRepository,
                    runHistoryClient,
                    graphPortsRepository,
                    operatorJobRepository,
                    notifyEventTopicPublisher);

                IDataStoreClient dataStoreClient = new DataStoreSvcClient(
                    amlDataStoreConfig: new AmlDataStoreConfig(k8sService.ApConfig),
                    servicePrincipalAadTokenProvider: k8sService.ServicePrincipalAadTokenProvider,
                    counters: k8sService.CounterManager,
                    timeout: submitterConfig.RequestTimeout,
                    clusterHealthPoller: clusterHealthPoller);

                IAzureMachineLearningDatasetManagementClientFactory datasetClientFactory = new AzureMachineLearningDatasetManagementClientFactory(
                    handler: new MessageHandlerWithAuth(k8sService.ServicePrincipalAadTokenProvider),
                    endpoint: new Uri(k8sService.ApConfig.GetString("AmlDatasetConfig.DatasetInternalEndpoint")),
                    timeout: submitterConfig.RequestTimeout);
                IAmlSvcClientFactory amlSvcClientFactory = new AmlSvcClientFactory(
                    k8sService,
                    clusterHealthPoller,
                    submitterConfig.RequestTimeout);

                var datasetModuleInputHelper = new DatasetModuleInputHelper(dataStoreClient, amlSvcClientFactory, datasetClientFactory);

                var retryFailedJobsProcessor = new RetryFailedJobsProcessor(fastOrcJobRepository);
                var jobGenerator = new JobGenerator(
                    config: submitterConfig,
                    new CloudSettingsMerger(),
                    amlSvcClientFactory,
                    retryFailedJobsProcessor,
                    controlOutputsProcessor,
                    new ControlFlowInfoGenerator(),
                    new ModuleControlInputInfosGenerator(),
                    new InputTypeParameterResolver(),
                    new ModulePortInfosGenerator(datasetModuleInputHelper),
                    experimentRepository);

                IExperimentStore experimentStore = storeFactory.CreateExperimentStore(context);

                var leaseProvider = new SubmissionLeaseProvider(
                    lockStorage: experimentLockStorage,
                    config: submitterConfig);

                var experimentSubmissionStore = new ExperimentSubmissionStore(experimentSubmissionStateStorage, counterManager);
                CommonLogger.LogInfo($"Created amlClientFactory at {k8sService.ApConfig.GetString("AmlServices.AmlServicesEndpoint")}");

                var dataRetriever = new SubmissionDataRetriever(
                    experimentStore: experimentStore,
                    graphStore: storeFactory.CreateGraphStore(context),
                    counterManager: counterManager,
                    config: submitterConfig,
                    datasetStore: storeFactory.CreateDatasetStore(context),
                    dataTypeStore: dataTypeStore,
                    amlSvcClientFactory: amlSvcClientFactory,
                    fastOrcExecutionJobRepository: fastOrcJobRepository);

                var loggerFactory = new LoggerFactory();
                loggerFactory.AddProvider(new CommonLoggerProvider());
                var distributedTracingContext = new DistributedTracingContext(ServiceName, runHistoryClient, distributedTracingConfig, loggerFactory);
                var crossServiceSpanMeasurement = new CrossServiceSpanMeasurement(pipelineSpanTable, distributedTracingContext);

                var quotaServiceClient = new QuotaServiceClient(
                    baseUrl: submitterConfig.QuotaServiceEndpoint,
                    s2STokenProvider: k8sService.ServicePrincipalAadTokenProvider,
                    retryPolicy: new ExponentialBackoffRetryPolicy(submitterConfig.QuotaRetriesCount));

                var logRepository = new LogRepository(workspaceResourcesCache, artifactClient, counterManager);

                var processFsm = new ProcessSubmissionFsm(
                    experimentStore: experimentStore,
                    counterManager: counterManager,
                    experimentsQueueManager: taggedExperimentQueueManager,
                    notifyQueue: notifyQueue,
                    config: submitterConfig,
                    jobRepository: fastOrcJobRepository,
                    graphPortsRepository: graphPortsRepository,
                    operatorJobRepository: operatorJobRepository,
                    quotaClient: quotaServiceClient,
                    jobGenerator: jobGenerator,
                    experimentRepository: experimentRepository,
                    cloudSystemRepository: cloudSystemRepository,
                    submissionStore: experimentSubmissionStore,
                    jobStdOutLoggerFactory: msiJobStdOutLoggerFactory,
                    logRepository: logRepository,
                    notifyEventTopicPublisher: notifyEventTopicPublisher,
                    crossServiceSpanMeasurement: crossServiceSpanMeasurement,
                    controlReferenceNodesRepository: controlReferenceNodesRepository);

                var jobQueueTracker = new JobQueueTracker(counters: counterManager);

                var commonCounterManager = await k8sService.InitializeCommonCounterAsync();

                var queueProcessor = new SubmitRequestQueueProcessor(
                    submissionStore: experimentSubmissionStore,
                    taggedQueueManager: taggedQueueManager,
                    dequeueDescriptor: new AssignedQueueIdDescriptor(),
                    processFsm: processFsm,
                    counterManager: counterManager,
                    commonCounterManager: commonCounterManager,
                    config: submitterConfig,
                    dataRetriever: dataRetriever,
                    leaseProvider: leaseProvider,
                    jobQueueTracker: jobQueueTracker);

                var experimentSubmitter = new ExperimentSubmitterCommon.ExperimentSubmitter(
                    experimentStore: experimentStore,
                    submissionStore: experimentSubmissionStore,
                    dataRetriever: dataRetriever,
                    experimentRepository: experimentRepository,
                    counterManager: counterManager,
                    config: submitterConfig,
                    notifyTopicPublisher: notifyEventTopicPublisher,
                    quotaClient: quotaServiceClient,
                    fastOrcExecutionJobRepository: fastOrcJobRepository,
                    taggedQueueManager: taggedQueueManager);

                Task mainTask = queueProcessor.StartAsync();

                IAetherRedisCache redisCache = await AetherRedisCache.CreateAsync(submitterConfig.RedisCache, counterManager);
                var s2SAuthorizer = new S2sAuthorizer(
                    authority: amlS2SServicePrincipalConfig.Authority,
                    appId: Environment.GetEnvironmentVariable(amlS2SServicePrincipalConfig.SecretS2SAppIdEnvironmentVariable),
                    audience: amlS2SServicePrincipalConfig.Resource,
                    allowedIdsConfig: amlS2SAllowedIdsConfig);
                await s2SAuthorizer.InitAsync();

                var latencyMeasurer = new LatencyMeasurer(redisCache);

                GcCollectorTimer gcCollectorTimer = null;
                if (submitterConfig.ForceGarbageCollectionMinutesInterval > 0)
                {
                    gcCollectorTimer = new GcCollectorTimer(submitterConfig.ServiceName, submitterConfig.ForceGarbageCollectionMinutesInterval);
                }

                var httpsConfiguration = new HttpsServiceConfiguration(k8sService.ApConfig);
                IWebHostBuilder hostBuilder = WebHost.CreateDefaultBuilder()
                    .UseKestrel((builderContext, options) =>
                    {
                        options.Limits.MaxRequestBodySize = submitterConfig.MaxReceivedMessageSize;
                        KestrelHttpsExtensions.ListenHttps(builderContext, options, httpsConfiguration);
                    })
                    .ConfigureLogging(builder => builder.ClearProviders().AddCommonLogger())
                    .Configure(app =>
                    {
                        app.UseResponseCompression();
                        app.UseMvc();
                    })
                    .ConfigureServices(services =>
                    {
                        services.AddSingleton(k8sService.WorkloadIdentityProvider);
                        services.AddSingleton<IJointMetricsPublisher>((sp) => new AmlTelemetryMetricsPublisher(sp.GetRequiredService<ILoggerFactory>(), sp.GetRequiredService<IMetricsReporter>()));
                        services.AddSingleton<IMetricReporter>(sp => new LegacyMetricReporter(sp.GetRequiredService<IJointMetricsPublisher>()));
                        services.ConfigureAppInsightsTargetFilter(new HashSet<string>() {
                            $"{Env.StorageAccountNameCloudManager()}{Env.StorageQueueEndpoint()}",
                            $"{Env.StorageAccountNameExperimentSubmitterQueue()}{Env.StorageQueueEndpoint()}",
                        });
                        services.ConfigureBasicServices(serviceName: ServiceName, baseService: k8sService, configuration: null);
                        var redisConfig = new RedisConfiguration(k8sService.ApConfig, k8sService.SecretProvider);
                        var builder = new ConfigurationBuilder()
                                            .AddJsonFile(Path.Combine("appsettings", "appsettings.override.json"), optional: true, reloadOnChange: true)
                                            .AddInMemoryCollection(redisConfig.AsInMemoryCollection())
                                            .AddEnvironmentVariables();

                        IConfiguration configuration = builder.Build();
                        services.AddSingleton(counterManager);
                        services.AddSingleton(new ClusterHealthPollerWrapper(submitterConfig.ClusterHealthCheckDelay, counterManager));
                        services.AddSingleton<IObjectCacheDecorator, AetherCacheDecorator>();
                        services.AddSingleton<JobQueueTracker>(jobQueueTracker);
                        services.AddMemoryCache();
                        services.AddCommonCaching(configuration);
                        services.AddMvc(options =>
                            {
                                options.EnableEndpointRouting = false;
                                options.Filters.Add(new ServiceExceptionFilterAttribute());
                                if (Env.DeploymentType() != "onebox")
                                {
                                    options.Filters.Add(new S2sAuthorizeAttribute(s2SAuthorizer, keepAlivePaths: new HashSet<string> { "/dcm/keepalive", "/dcm/liveness" }));
                                    options.AddCommonAppInsightsComponents();
                                }
                            }).AddApplicationPart(typeof(ExperimentsController).Assembly); // adding all controllers from Microsoft.Aether.CloudManager.ExperimentSubmitterCommon.Controllers

                        services.AddSingleton(submitterConfig);
                        services.AddSingleton(notifyQueue);
                        services.AddSingleton<IExperimentSubmitter>(experimentSubmitter);
                        services.AddSingleton(clusterHealthPoller);
                        services.AddSingleton<ILatencyMeasurer>(latencyMeasurer);
                        services.AddSingleton<IWorkspaceResourcesCache>(sp =>
                        {
                            IObjectCache cache = sp.GetRequiredService<IObjectCache>();
                            IMemoryCache memoryCache = sp.GetRequiredService<IMemoryCache>();
                            return new WorkspaceResourcesCache(cache, memoryCache, amlWorkspaceResourcesClient, counterManager);
                        });

                        services.AddSingleton(crossServiceSpanMeasurement);
                        services.AddSingleton(amlSvcClientFactory);

                        services.AddSingleton<IServiceBusTopicPublisher>(notifyEventTopicPublisher);
                        services.AddSingleton<IExecutionJobRepository>(fastOrcJobRepository);
                        services.AddSingleton<IExperimentRepository>(experimentRepository);

                        NetCoreControllerActivatorWithLogging.Apply(services, serviceName: ServiceName);

                        if (configuration.GetValue<bool>("CertificateReloading:Enabled", defaultValue: false))
                        {
                            CommonLogger.LogInfo($"Enable certificate reloading");
                            services.ConfigureCertificateReloading(Directory.GetCurrentDirectory());
                        }
                        else
                        {
                            CommonLogger.LogInfo($"Disable certificate reloading");
                        }
                    });

                Task webHostTask = hostBuilder.Build().RunAsync();
                CommonLogger.LogEntityInfo(ServiceName, $"Workload identity available: {workloadIdentityProvider?.IsAvailable}.");

                await Task.WhenAny(mainTask, webHostTask);

                crossServiceSpanMeasurement.Dispose();

                if (gcCollectorTimer != null)
                {
                    GC.KeepAlive(gcCollectorTimer);
                }
            }
            catch (Exception e)
            {
                CommonLogger.LogEntityError(ServiceName, "Unhandled exception: [Error: {safe_exception_message}, Stack Trace: {stack_trace}]", e.Message, e.StackTrace);
                await Task.Delay(TimeSpan.FromSeconds(5));
            }
        }

        private static string GetLegacyAzurePrefix()
        {
            const string AzurePrefix = "AZUREPREFIX";
            return Environment.GetEnvironmentVariable(AzurePrefix) ?? string.Empty;
        }

        private static async Task InitializeLegacyStoragesAsync(
            ExperimentSubmitterConfig submitterConfig,
            string provisionedStoragesJson,
            StorageDescriptions sd)
        {
            string artifactPrefix = GetLegacyAzurePrefix();
            CommonLogger.LogInfo($"Azure table prefix is {artifactPrefix}");

            var legacyProvisioning = new LegacyProvisioning();

            legacyProvisioning.LegacyRegisterTableStorage(tableDef: sd.DcmShared.FastOrcNodes,
                                                          tableName: artifactPrefix + submitterConfig.CloudStorage.FastOrcNodesTableName);
            legacyProvisioning.LegacyRegisterTableStorage(tableDef: sd.DcmShared.OperatorNodes,
                                  tableName: artifactPrefix + submitterConfig.CloudStorage.OperatorNodesTableName);
            legacyProvisioning.LegacyRegisterTableStorage(tableDef: sd.DcmShared.ControlReferenceNodes,
                                  tableName: artifactPrefix + submitterConfig.CloudStorage.ControlRefNodesTableName);
            legacyProvisioning.LegacyRegisterTableStorage(tableDef: sd.DcmShared.SubmittedNodes,
                                                          tableName: artifactPrefix + submitterConfig.CloudStorage.SubmittedTableName);
            legacyProvisioning.LegacyRegisterTableStorage(tableDef: sd.DcmShared.CancelRequestedExperiments,
                                                          tableName: artifactPrefix + submitterConfig.CloudStorage.CancelRequestedExperimentsTableName);
            legacyProvisioning.LegacyRegisterTableStorage(tableDef: sd.DcmShared.FailedExperiments,
                                                          tableName: artifactPrefix + submitterConfig.CloudStorage.FailedTableName);
            legacyProvisioning.LegacyRegisterTableStorage(tableDef: sd.DcmShared.SubmittedExperiments,
                                                          tableName: artifactPrefix + submitterConfig.CloudStorage.SubmittedExperimentsTableName);
            legacyProvisioning.LegacyRegisterTableStorage(tableDef: sd.ExperimentSubmitter.SubmissionState,
                                                          tableName: artifactPrefix + submitterConfig.CloudStorage.SubmissionStateTableName);
            legacyProvisioning.LegacyRegisterTableStorage(tableDef: sd.DcmShared.PipelineSpan,
                                                          tableName: artifactPrefix + submitterConfig.CloudStorage.PipelineSpanTableName);
            legacyProvisioning.LegacyRegisterQueue(queueDef: sd.DcmShared.Notifications,
                                                   queueName: artifactPrefix + submitterConfig.CloudStorage.NotifyQueueName);
            legacyProvisioning.LegacyRegisterQueue(queueDef: sd.ExperimentSubmitter.SubmitterGarbageCollectionQueue,
                                                   queueName: artifactPrefix + submitterConfig.CloudStorage.SubmitterGarbageCollectionQueueName);

            legacyProvisioning.LegacyRegisterBlobContainer(blobDef: sd.ExperimentSubmitter.ExperimentSubmissionLockStorage,
                                                           containerName: submitterConfig.ExperimentSubmissionLockContainerName);

            legacyProvisioning.LegacyRegisterConnectionString(StorageDescriptionsExperimentSubmitter.StateStorageAccountName, await submitterConfig.CloudStorage.GetAzureConnectionStringAsync());
            legacyProvisioning.LegacyRegisterConnectionString(StorageDescriptionsDcmShared.StateStorageAccountName, await submitterConfig.CloudStorage.GetAzureConnectionStringAsync());
            legacyProvisioning.LegacyRegisterAzureStorageAccountName(StorageDescriptionsExperimentSubmitter.StateStorageAccountName, Env.StorageAccountNameCloudManager());
            legacyProvisioning.LegacyRegisterAzureStorageAccountName(StorageDescriptionsDcmShared.StateStorageAccountName, Env.StorageAccountNameCloudManager());

            legacyProvisioning.LegacyRegisterConnectionString(StorageDescriptionsExperimentSubmitter.QueueStorageAccountName, await submitterConfig.GetQueueStorageAzureConnectionStringAsync());
            legacyProvisioning.LegacyRegisterConnectionString(StorageDescriptionsNodeProcessor.QueueStorageAccountName, await submitterConfig.CloudStorage.GetExperimentQueueStorageAzureConnectionStringAsync());
            legacyProvisioning.LegacyRegisterAzureStorageAccountName(StorageDescriptionsExperimentSubmitter.QueueStorageAccountName, Env.StorageAccountNameExperimentSubmitterQueue());
            legacyProvisioning.LegacyRegisterAzureStorageAccountName(StorageDescriptionsNodeProcessor.QueueStorageAccountName, Env.StorageAccountNameNodeProcessorQueue());

            legacyProvisioning.SerializeIntoFile(provisionedStoragesJson);
        }
    }
}
