﻿<Project Sdk="Microsoft.NET.Sdk" DefaultTargets="Publish;Build">
  <PropertyGroup>
    <PublishDir>$(BaseTargetDir)\app\aether\bluebox\BBExperimentSubmitter</PublishDir>
    <OutputType>Exe</OutputType>
    <AssemblyName>BBExperimentSubmitter</AssemblyName>
    <TargetFramework>net8.0</TargetFramework>
    <ResolveAssemblyWarnOrErrorOnTargetArchitectureMismatch>None</ResolveAssemblyWarnOrErrorOnTargetArchitectureMismatch>
    <HighEntropyVA>true</HighEntropyVA>
    <QInstrumentForCoverage>false</QInstrumentForCoverage>
    <RootNamespace>Microsoft.Aether.CloudManager.BBExperimentSubmitter</RootNamespace>
  </PropertyGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\..\..\Microsoft.MachineLearning.Pipeline.Common\Microsoft.MachineLearning.Pipeline.Common.csproj" />
    <ProjectReference Include="..\..\..\..\RelInfra\RelInfra.Common.Library\src\Common\RelInfra.Common.csproj" />
    <ProjectReference Include="..\..\..\..\RelInfra\RelInfra.Instrumentation.Library\src\Microsoft.RelInfra.Instrumentation.csproj" />
    <ProjectReference Include="..\..\..\..\RelInfra\RelInfra.Storage.Library\src\Storage\RelInfra.Storage.csproj" />
    <ProjectReference Include="..\..\..\Aether.Backend.CacheClient\Aether.Backend.CacheClient.csproj" />
    <ProjectReference Include="..\..\..\AetherQuota\AetherQuota.Shared\AetherQuota.Shared.csproj" />
    <ProjectReference Include="..\..\..\AetherQuota\AetherQuotaClient\AetherQuotaClient.csproj" />
    <ProjectReference Include="..\..\..\CloudManager\Microsoft.Aether.BlueBox.CloudManager.Common.Web\Microsoft.Aether.BlueBox.CloudManager.Common.Web.csproj" />
    <ProjectReference Include="..\..\..\shared\Microsoft.Aether.BackendCommon\Microsoft.Aether.BackendCommon.csproj" />
    <ProjectReference Include="..\..\..\shared\Microsoft.Aether.DataContract\Microsoft.Aether.DataContracts.csproj" />
    <ProjectReference Include="..\..\..\CloudManager\Common\Microsoft.Aether.CloudManager.Common.csproj" />
    <ProjectReference Include="..\..\..\CloudManager\ExperimentSubmitterCommon\Microsoft.Aether.CloudManager.ExperimentSubmitterCommon.csproj" />
    <ProjectReference Include="..\..\..\shared\Microsoft.Aether.S2S.Common\Microsoft.Aether.S2S.Common.csproj" />
    <ProjectReference Include="..\..\..\shared\Microsoft.Aether.TaggedQueueManager.CloudTags\Microsoft.Aether.TaggedQueueManager.CloudTags.csproj" />
    <ProjectReference Include="..\..\..\shared\Microsoft.Aether.TaggedQueueManager\Microsoft.Aether.TaggedQueueManager.csproj" />
    <ProjectReference Include="..\..\AetherK8SBaseService\Microsoft.Aether.BlueBox.AetherK8SBaseService.csproj" />
    <ProjectReference Include="..\..\AetherK8SCommon\Microsoft.Aether.BlueBox.AetherK8SCommon.csproj" />
    <ProjectReference Include="..\..\CloudletProxyClient\Microsoft.Aether.BlueBox.CloudletProxyClient\Microsoft.Aether.BlueBox.CloudletProxyClient.csproj" />
    <ProjectReference Include="..\..\Clouds\Clouds.Common\Microsoft.Aether.BlueBox.Clouds.Common.csproj" />
    <ProjectReference Include="..\..\MetaStore\MetaStoreV2.Client\MetaStoreV2.Client.csproj" />
    <ProjectReference Include="..\..\Microsoft.Aether.BlueBox.LogRepository\Microsoft.Aether.BlueBox.LogRepository.csproj" />
    <ProjectReference Include="..\..\Microsoft.Aether.BlueBox.Web.Common\Microsoft.Aether.BlueBox.Web.Common.csproj" />
    <ProjectReference Include="..\..\S2S.Common\Microsoft.Aether.BlueBox.S2S.Common\Microsoft.Aether.BlueBox.S2S.Common.csproj" />
    <ProjectReference Include="..\..\RunHistoryClient\src\client\Microsoft.Aether.BlueBox.RunHistoryClient.csproj" />
    <ProjectReference Include="$(SrcRoot)\Common\Startup\Common.Startup.csproj" />
    <ProjectReference Include="$(SrcRoot)\Common\WebApi\Common.WebApi.csproj" />
  </ItemGroup>
  <ItemGroup>
    <None Include="..\..\..\shared\Scripts\start.sh">
      <Link>start.sh</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="..\..\..\CloudManager\Common\notification.ini">
      <Link>notification.ini</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="..\..\..\CloudManager\ExperimentSubmitter\ExperimentSubmitter.ini">
      <Link>ExperimentSubmitter.ini</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="..\..\..\shared\SharedConfigurationSettings\BBSharedSettings.ini" Link="BBSharedSettings.ini">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="..\..\..\shared\SharedConfigurationSettings\SharedSettings.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="..\..\..\CloudManager\Common\storage.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="..\..\CA\*.cer">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="..\..\DistributedTracing\Microsoft.Aether.BlueBox.DistributedTracing\DistributedTracing.ini">
      <Link>DistributedTracing.ini</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="Dockerfile">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="StackExchange.Redis" />
    <PackageReference Include="System.Net.Security" />
    <PackageReference Include="FluentAssertions" />
    <PackageReference Include="Microsoft.IdentityModel.Authorization.Azure">
      <NoWarn>NU1701</NoWarn>
    </PackageReference>
  </ItemGroup>
</Project>
