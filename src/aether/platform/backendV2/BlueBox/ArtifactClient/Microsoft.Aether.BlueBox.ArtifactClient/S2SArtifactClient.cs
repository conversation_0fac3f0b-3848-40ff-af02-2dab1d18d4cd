﻿using Common.Core.Contracts;
using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Aether.BlueBox.AetherK8SCommon.Http;
using Microsoft.Aether.DataContracts;
using Microsoft.Aether.DataContracts.Entity;
using Microsoft.Aether.S2S.Common;
using Microsoft.RelInfra.Common.RetryExecution;
using Microsoft.RelInfra.Common.RetryExecution.Policy;
using Microsoft.RelInfra.Common.Exceptions;
using Microsoft.RelInfra.Extensions;
using Microsoft.RelInfra.Instrumentation;

namespace Microsoft.Aether.BlueBox.ArtifactClient
{
    public class S2SArtifactClient : IS2SArtifactClient
    {
        private readonly HttpClient _httpClient;

        private readonly IS2STokenProvider _servicePrincipalTokenProvider;

        private readonly IRetryPolicy _retryPolicy;

        private readonly bool _retryOnTooManyRequestsException;

        private readonly CounterManager _counters;

        protected readonly ArtifactConfig _config;

        public static S2SArtifactClient Create(
            ArtifactConfig artifactServiceConfig,
            IS2STokenProvider servicePrincipalTokenProvider,
            CounterManager counters,
            TimeSpan? timeout = null,
            IRetryPolicy retryPolicy = null,
            bool retryOnTooManyRequestsException = true)
        {
            if (artifactServiceConfig == null)
            {
                throw new ArgumentNullException(nameof(artifactServiceConfig));
            }

            if (servicePrincipalTokenProvider == null)
            {
                throw new ArgumentNullException(nameof(servicePrincipalTokenProvider));
            }

            return new S2SArtifactClient(
                config: artifactServiceConfig,
                servicePrincipalTokenProvider: servicePrincipalTokenProvider,
                retryPolicy: retryPolicy ?? new ExponentialBackoffRetryPolicy(2),
                retryOnTooManyRequestsException: retryOnTooManyRequestsException,
                counters: counters,
                timeout: timeout);
        }

        protected S2SArtifactClient(
            ArtifactConfig config,
            IS2STokenProvider servicePrincipalTokenProvider,
            IRetryPolicy retryPolicy,
            bool retryOnTooManyRequestsException,
            CounterManager counters,
            TimeSpan? timeout = null)
        {
            _config = config;
            _servicePrincipalTokenProvider = servicePrincipalTokenProvider;
            _retryPolicy = retryPolicy;
            _retryOnTooManyRequestsException = retryOnTooManyRequestsException;
            _counters = counters;
            
            _httpClient = ViennaHttpClientFactory.CreateHttpClient();
            if (timeout != null)
            {
                _httpClient.Timeout = timeout.Value;
            }
        }

        protected virtual string Host => _config.ArtifactServiceEndpoint.TrimEnd();

        public async Task<BatchArtifactContentInformationDto> BatchCreateEmptyArtifactAsync(string runId, WorkspaceIdentity amlIdentity, BatchArtifactCreateCommand artifactCreateCommand, CreatedBy createdBy, CancellationToken cancellationToken)
        {
            string serverPath = FormatBatchCreateServerPath($"{Host}/{_config.BatchCreateEmptyArtifactPath}", amlIdentity, runId, ArtifactOrigins.ExperimentRun);
            string authToken = await _servicePrincipalTokenProvider.GetTokenAsync();
            using (_counters.GetLatencyCounter("BatchCreateEmptyArtifactLatency").StartCounter())
            {
                try
                {
                    return await RetryExecutionHelper.ExecuteFuncWithRetryAsync(
                        function: async () => await _httpClient.PostEntityWithAuthHeaderAsync<BatchArtifactCreateCommand, BatchArtifactContentInformationDto>(serverPath: serverPath, entity: artifactCreateCommand, authToken: authToken, customHeaders: createdBy?.GetCreatedByHeaders()),
                        shouldRetryException: ShouldRetry,
                        retryPolicy: _retryPolicy,
                        token: cancellationToken);

                }
                catch (Exception ex)
                {
                    throw new ServiceInvocationException(
                        operationName: "BatchCreateEmptyArtifact",
                        calledService: "S2SArtifact",
                        httpMethod: HttpMethod.Post,
                        innerException: ex);
                }
            }
        }

        public async Task<ArtifactContentInformationDto> GetArtifactContentInformationAsync(string artifactId, WorkspaceIdentity amlIdentity, CreatedBy createdBy, CancellationToken cancellationToken)
        {
            string serverPath = FormatServerPath($"{Host}/{_config.GetArtifactContentInformationPath}", amlIdentity, artifactId);
            string authToken = await _servicePrincipalTokenProvider.GetTokenAsync();

            using (_counters.GetLatencyCounter("GetArtifactContentInformationLatency").StartCounter())
            {
                try
                {
                    return await RetryExecutionHelper.ExecuteFuncWithRetryAsync(
                        function: async () => await _httpClient.GetEntityWithAuthHeaderAsync<ArtifactContentInformationDto>(serverPath: serverPath, authToken: authToken, customHeaders: createdBy?.GetCreatedByHeaders()),
                        shouldRetryException: ShouldRetry,
                        retryPolicy: _retryPolicy,
                        token: cancellationToken);
                }
                catch (Exception ex)
                {
                    throw new ServiceInvocationException(
                        operationName: "GetArtifactContentInformation",
                        calledService: "S2SArtifact",
                        httpMethod: HttpMethod.Get,
                        innerException: ex);
                }
            }
        }

        public async Task<ArtifactContentInformationDto> GetWriteableUriByArtifactIdAsync(string artifactId, WorkspaceIdentity amlIdentity, CreatedBy createdBy, CancellationToken cancellationToken)
        {
            string serverPath = FormatServerPath($"{Host}/{_config.GetArtifactWriteableUriPath}", amlIdentity, artifactId);
            string authToken = await _servicePrincipalTokenProvider.GetTokenAsync();
            using (_counters.GetLatencyCounter("GetWriteableUriByArtifactIdLatency").StartCounter())
            {
                try
                {
                    return await RetryExecutionHelper.ExecuteFuncWithRetryAsync(
                        function: async () => await _httpClient.GetEntityWithAuthHeaderAsync<ArtifactContentInformationDto>(serverPath: serverPath, authToken: authToken, customHeaders: createdBy?.GetCreatedByHeaders()),
                        shouldRetryException: ShouldRetry,
                        retryPolicy: _retryPolicy,
                        token: cancellationToken);
                }
                catch (Exception ex)
                {
                    throw new ServiceInvocationException(
                        operationName: "GetWriteableUriByArtifactId",
                        calledService: "S2SArtifact",
                        httpMethod: HttpMethod.Get,
                        innerException: ex);
                }
            }
        }

        public async Task<ArtifactDto> GetArtifactByIdAsync(string artifactId, WorkspaceIdentity amlIdentity, CreatedBy createdBy, CancellationToken cancellationToken)
        {
            string serverPath = FormatServerPath($"{Host}/{_config.GetArtifactMetadataPath}", amlIdentity, artifactId);
            string authToken = await _servicePrincipalTokenProvider.GetTokenAsync();
            using (_counters.GetLatencyCounter("GetArtifactByIdLatency").StartCounter())
            {
                try
                {
                    return await RetryExecutionHelper.ExecuteFuncWithRetryAsync(
                        function: async () => await _httpClient.GetEntityWithAuthHeaderAsync<ArtifactDto>(serverPath: serverPath, authToken: authToken, customHeaders: createdBy?.GetCreatedByHeaders()),
                        shouldRetryException: ShouldRetry,
                        retryPolicy: _retryPolicy,
                        token: cancellationToken);
                }
                catch (Exception ex)
                {
                    throw new ServiceInvocationException(
                        operationName: "GetArtifactById",
                        calledService: "S2SArtifact",
                        httpMethod: HttpMethod.Get,
                        innerException: ex);
                }
            }
        }

        public async Task<ArtifactDto> RegisterArtifact(
            ArtifactDto artifactDto,
            WorkspaceIdentity amlIdentity,
            CreatedBy createdBy,
            CancellationToken cancellationToken)
        {
            string serverPath = FormatRegisterArtifactServerPath($"{Host}/{_config.RegisterArtifactPath}", amlIdentity);
            string authToken = await _servicePrincipalTokenProvider.GetTokenAsync();

            using (_counters.GetLatencyCounter("RegisterArtifactLatency").StartCounter())
            {
                try
                {
                    return await RetryExecutionHelper.ExecuteFuncWithRetryAsync(
                        function: async () => await _httpClient.PostEntityWithAuthHeaderAsync<ArtifactDto, ArtifactDto>(
                            serverPath: serverPath,
                            entity: artifactDto,
                            authToken: authToken,
                            customHeaders: createdBy?.GetCreatedByHeaders()),
                        shouldRetryException: ShouldRetry,
                        retryPolicy: _retryPolicy,
                        token: cancellationToken);
                }
                catch (Exception ex)
                {
                    throw new ServiceInvocationException(
                        operationName: "RegisterArtifact",
                        calledService: "S2SArtifact",
                        httpMethod: HttpMethod.Post,
                        innerException: ex);
                }
            }
        }

        public async Task<List<ArtifactDto>> GetArtifactsInContainer(string runId, WorkspaceIdentity amlIdentity, CreatedBy createdBy, CancellationToken cancellationToken)
        {
            string serverPath = FormatBatchCreateServerPath($"{Host}/{_config.GetArtifactsInContainerPath}", amlIdentity, runId, ArtifactOrigins.ExperimentRun);
            string authToken = await _servicePrincipalTokenProvider.GetTokenAsync();
            using (_counters.GetLatencyCounter("GetArtifactsInContainer").StartCounter())
            {
                try
                {
                    return await RetryExecutionHelper.ExecuteFuncWithRetryAsync(
                        function: async () => (await _httpClient.GetEntityWithAuthHeaderAsync<PaginatedResult<ArtifactDto>>(serverPath: serverPath, authToken: authToken, customHeaders: createdBy?.GetCreatedByHeaders())).Value,
                        shouldRetryException: ShouldRetry,
                        retryPolicy: _retryPolicy,
                        token: cancellationToken);
                }
                catch (Exception ex)
                {
                    throw new ServiceInvocationException(
                        operationName: "GetArtifactsInContainer",
                        calledService: "S2SArtifact",
                        httpMethod: HttpMethod.Get,
                        innerException: ex);
                }
            }
        }

        private static string FormatRegisterArtifactServerPath(string path, WorkspaceIdentity amlIdentity)
        {
            return path
                .Replace("{subscriptionId}", amlIdentity.SubscriptionId)
                .Replace("{resourceGroupName}", amlIdentity.ResourceGroupName)
                .Replace("{workspaceName}", amlIdentity.WorkspaceName);
        }

        private static string FormatBatchCreateServerPath(string path, WorkspaceIdentity amlIdentity, string runId, string origin)
        {
            return path
                .Replace("{subscriptionId}", amlIdentity.SubscriptionId)
                .Replace("{resourceGroupName}", amlIdentity.ResourceGroupName)
                .Replace("{workspaceName}", amlIdentity.WorkspaceName)
                .Replace("{origin}", origin)
                .Replace("{container}", runId);
        }
        
        private static string FormatServerPath(string path, WorkspaceIdentity amlIdentity, string artifactId)
        {
            return path
                .Replace("{subscriptionId}", amlIdentity.SubscriptionId)
                .Replace("{resourceGroupName}", amlIdentity.ResourceGroupName)
                .Replace("{workspaceName}", amlIdentity.WorkspaceName)
                .Replace("{origin}/{container}/{path}", artifactId);
        }

        private bool ShouldRetry(Exception exception)
        {
            var httpException = exception as HttpRequestDetailException;
            if (httpException != null)
            {
                if (httpException.StatusCode == HttpStatusCode.Forbidden ||
                    httpException.StatusCode == HttpStatusCode.NotFound ||
                    httpException.StatusCode == HttpStatusCode.Conflict ||
                    httpException.StatusCode == HttpStatusCode.Unauthorized ||
                    (!_retryOnTooManyRequestsException && httpException.StatusCode == HttpStatusCode.TooManyRequests))
                {
                    return false;
                }
            }

            return true;
        }
    }
}
