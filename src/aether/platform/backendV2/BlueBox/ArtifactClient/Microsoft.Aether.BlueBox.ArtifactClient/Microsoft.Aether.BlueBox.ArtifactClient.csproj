<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <QInstrumentForCoverage>false</QInstrumentForCoverage>
  </PropertyGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\..\..\RelInfra\RelInfra.Common.Library\src\Extensions\RelInfra.Extensions.csproj" />
    <ProjectReference Include="..\..\..\..\RelInfra\RelInfra.Common.Library\src\Common\RelInfra.Common.csproj" />
    <ProjectReference Include="..\..\..\shared\Microsoft.Aether.DataContract\Microsoft.Aether.DataContracts.csproj" />
    <ProjectReference Include="..\..\..\shared\Microsoft.Aether.S2S.Common\Microsoft.Aether.S2S.Common.csproj" />
    <ProjectReference Include="..\..\AetherK8SCommon\Microsoft.Aether.BlueBox.AetherK8SCommon.csproj" />
  </ItemGroup>
</Project>
