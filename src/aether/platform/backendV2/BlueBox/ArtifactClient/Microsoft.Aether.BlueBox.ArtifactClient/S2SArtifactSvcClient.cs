﻿using Microsoft.Aether.BackendCommon.ClusterHealthPoller;
using Microsoft.Aether.S2S.Common;
using Microsoft.RelInfra.Common.RetryExecution.Policy;
using Microsoft.RelInfra.Instrumentation;
using Microsoft.RelInfra.Instrumentation.Logging;
using System;

namespace Microsoft.Aether.BlueBox.ArtifactClient
{
    public class S2SArtifactSvcClient : S2SArtifactClient
    {
        private readonly IClusterHealthPoller _clusterHealthPoller;

        public S2SArtifactSvcClient(
            ArtifactConfig config,
            IS2STokenProvider servicePrincipalTokenProvider,
            IRetryPolicy retryPolicy,
            bool retryOnTooManyRequestsException,
            CounterManager counters,
            IClusterHealthPoller clusterHealthPoller,
            TimeSpan? timeout = null)
            : base(
                  config,
                  servicePrincipalTokenProvider,
                  retryPolicy,
                  retryOnTooManyRequestsException,
                  counters,
                  timeout)
        {
            _clusterHealthPoller = clusterHealthPoller;
        }

        protected override string Host
        {
            get
            {
                if (_clusterHealthPoller.IsClusterHealthy && !_clusterHealthPoller.IsExpired && !string.IsNullOrEmpty(_config.ArtifactServiceSvcEndpoint))
                {
                    return _config.ArtifactServiceSvcEndpoint;
                }
                else
                {
                    CommonLogger.LogWarning($"Current cluster is unhealthy or artifactSvcEnpoint is null or empty. artifactSvcEnpoint IsNullOrEmpty={string.IsNullOrEmpty(_config.ArtifactServiceSvcEndpoint)}. ClusterHealthy={_clusterHealthPoller.IsClusterHealthy}. Expired={_clusterHealthPoller.IsExpired}. LastUpdateTime {_clusterHealthPoller.LastUpdateTimeUtc}.");
                    return _config.ArtifactServiceEndpoint;
                }
            }
        }
    }
}
