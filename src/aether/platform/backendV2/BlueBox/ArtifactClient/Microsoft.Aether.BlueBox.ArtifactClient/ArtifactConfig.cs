﻿using Microsoft.RelInfra.Common.Configuration;

namespace Microsoft.Aether.BlueBox.ArtifactClient
{
    public class ArtifactConfig : NestedVerifiableConfig
    {
        public ArtifactConfig(IRelInfraConfiguration config, string configName)
            : base(config, configName)
        {
        }

        public string ArtifactServiceEndpoint => Config.GetString("AmlServices.AmlServicesEndpoint");
        public string ArtifactServiceSvcEndpoint => Config.GetString("AmlArtifactServiceConfig.ArtifactInternalEndpoint", "");
        public string CreateArtifactPath => IsDataContainerEnabled ? Config.GetString("AmlArtifactServiceConfig.CreateArtifactPathV2") : Config.GetString("AmlArtifactServiceConfig.CreateArtifactPath");
        public string GetArtifactMetadataPath => IsDataContainerEnabled ? Config.GetString("AmlArtifactServiceConfig.GetArtifactMetadataPathV2") : Config.GetString("AmlArtifactServiceConfig.GetArtifactMetadataPath");
        public string BatchCreateEmptyArtifactPath => IsDataContainerEnabled ? Config.GetString("AmlArtifactServiceConfig.BatchCreateEmptyArtifactPathV2") : Config.GetString("AmlArtifactServiceConfig.BatchCreateEmptyArtifactPath");
        public string UploadArtifactPath => IsDataContainerEnabled ? Config.GetString("AmlArtifactServiceConfig.UploadArtifactPathV2") : Config.GetString("AmlArtifactServiceConfig.UploadArtifactPath");
        public string GetArtifactContentInformationPath => IsDataContainerEnabled ? Config.GetString("AmlArtifactServiceConfig.GetArtifactContentInformationPathV2") : Config.GetString("AmlArtifactServiceConfig.GetArtifactContentInformationPath");

        public string GetArtifactWriteableUriPath => Config.GetString("AmlArtifactServiceConfig.GetArtifactWriteableUriPath");
        public bool IsDataContainerEnabled => Config.GetBoolean("AmlRunHistoryConfig.EnableDataContainer");
        public string RegisterArtifactPath => Config.GetString("AmlArtifactServiceConfig.RegisterArtifactPath");
        public string GetArtifactsInContainerPath => Config.GetString("AmlArtifactServiceConfig.GetArtifactsInContainerPath");
    }
}
