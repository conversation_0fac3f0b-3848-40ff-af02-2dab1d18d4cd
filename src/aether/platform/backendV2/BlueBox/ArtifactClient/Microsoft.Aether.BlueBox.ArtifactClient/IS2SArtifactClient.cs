﻿using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Aether.DataContracts;
using Microsoft.Aether.DataContracts.Entity;

namespace Microsoft.Aether.BlueBox.ArtifactClient
{
    public interface IS2SArtifactClient
    {
        Task<BatchArtifactContentInformationDto> BatchCreateEmptyArtifactAsync(string runId, WorkspaceIdentity amlIdentity, BatchArtifactCreateCommand artifactCreateCommand, CreatedBy createdBy, CancellationToken cancellationToken);

        Task<ArtifactContentInformationDto> GetArtifactContentInformationAsync(string artifactId, WorkspaceIdentity amlIdentity, CreatedBy createdBy, CancellationToken cancellationToken);

        Task<ArtifactContentInformationDto> GetWriteableUriByArtifactIdAsync(string artifactId, WorkspaceIdentity amlIdentity, CreatedBy createdBy, CancellationToken cancellationToken);

        Task<ArtifactDto> GetArtifactByIdAsync(string artifactId, WorkspaceIdentity amlIdentity, CreatedBy createdBy, CancellationToken cancellationToken);

        Task<ArtifactDto> RegisterArtifact(
            ArtifactDto artifactDto,
            WorkspaceIdentity amlIdentity,
            CreatedBy creayedBy,
            CancellationToken cancellationToken);

        Task<List<ArtifactDto>> GetArtifactsInContainer(string runId, WorkspaceIdentity amlIdentity, CreatedBy createdBy, CancellationToken cancellationToken);
    }
}
