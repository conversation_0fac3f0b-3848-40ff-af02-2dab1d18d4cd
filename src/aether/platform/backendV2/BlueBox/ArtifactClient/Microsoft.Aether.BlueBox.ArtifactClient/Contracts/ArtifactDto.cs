﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using Microsoft.Aether.DataContracts;

namespace Microsoft.Aether.BlueBox.ArtifactClient
{
    public static class ArtifactOrigins
    {
        public const string Unknown = "Unknown";

        public const string ExperimentRun = "ExperimentRun";

        public static IReadOnlyCollection<string> All { get; } = new HashSet<string>
        {
            ExperimentRun
        };

        public static bool Exists(string origin) => All.Contains(origin);
    }
    
    [DataContract]
    public class ArtifactDto
    {
        [DataMember]
        public string ArtifactId { get; set; }

        [DataMember]
        public string Origin { get; set; }

        [DataMember]
        public string Container { get; set; }

        [DataMember]
        public string Path { get; set; }

        [DataMember]
        public string Etag { get; set; }

        [DataMember]
        public DateTimeOffset CreatedTime { get; set; }

        [DataMember]
        public DataPath DataPath { get; set; }
    }
}
