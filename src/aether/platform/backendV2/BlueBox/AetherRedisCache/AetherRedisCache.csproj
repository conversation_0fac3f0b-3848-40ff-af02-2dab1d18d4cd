<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <RootNamespace>Aether.Backend.CacheClient</RootNamespace>
    <AssemblyName>Aether.Backend.RedisCacheClient</AssemblyName>
    <TargetFramework>net6.0</TargetFramework>
    <ResolveAssemblyWarnOrErrorOnTargetArchitectureMismatch>None</ResolveAssemblyWarnOrErrorOnTargetArchitectureMismatch>
    <QInstrumentForCoverage>false</QInstrumentForCoverage>
  </PropertyGroup>
  <ItemGroup>
    <Compile Include="..\..\Aether.Backend.CacheClient\AetherRedisCache.cs" />
    <Compile Include="..\..\Aether.Backend.CacheClient\IAetherRedisCache.cs" />
    <Compile Include="..\..\Aether.Backend.CacheClient\RedisCacheConfiguration.cs" />
    <Compile Include="..\..\Aether.Backend.CacheClient\TimeoutAetherRedisCache.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\..\RelInfra\RelInfra.Instrumentation.Library\src\Microsoft.RelInfra.Instrumentation.csproj" />
    <ProjectReference Include="..\..\..\RelInfra\RelInfra.Common.Library\src\Common\RelInfra.Common.csproj" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Newtonsoft.Json" />
    <PackageReference Include="System.Net.Security" />
  </ItemGroup>
</Project>
