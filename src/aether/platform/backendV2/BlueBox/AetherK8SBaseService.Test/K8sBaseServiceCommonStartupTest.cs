﻿using Microsoft.Extensions.Logging;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Microsoft.MachineLearning.Common.Startup;
using Microsoft.Extensions.Configuration;
using Microsoft.AspNetCore.Builder;
using System.Reflection;
using Microsoft.Extensions.DependencyInjection;
using Aether.Backend.CacheClient;
using StackExchange.Redis;
using Azure.Security.KeyVault.Secrets;
using Azure.Identity;
using System.IO;
using System.Diagnostics;

namespace Microsoft.Aether.BlueBox.AetherK8SBaseService.Test
{
    internal class K8sBaseServiceCommonStartup : IAppStartup
    {
        private const string ServiceName = "AetherK8SBaseServicve.Test";

        private const string ConfigFileName = "K8sBaseServiceTest.ini";
        private const string SharedConfigFilename = "SharedSettings.ini";
        private const string BbSharedConfigFileName = "BBSharedSettings.ini";
        private const string DistributedTracingConfigFileName = "DistributedTracing.ini";

        public IConfigurationRoot Configuration { get; set; }

        public IServiceCollection Services { get; set; }

        public K8sBaseService k8sService { get; set; }

        public static bool Started { get; set; } = false;

        public static K8sBaseServiceCommonStartup instance { get; set; }

        public K8sBaseServiceCommonStartup(IConfiguration configuration)
        {
            Configuration = (IConfigurationRoot)configuration;

            instance = this;
        }

        public void DoGeneralAppStartup(IServiceProvider serviceProvider)
        {
        }

        public void DoWebStartup(IApplicationBuilder app)
        {
            Started = true;
        }

        public void DoWorkerStartup(IServiceProvider serviceProvider)
        {
            throw new NotImplementedException();
        }

        public Assembly[] GetOrleansAssemblies()
        {
            throw new NotImplementedException();
        }

        public void RegisterGeneralAppComponents(IServiceCollection services)
        {
            Services = services;

            init().Wait();

            services.ConfigureBasicServices(serviceName: ServiceName, baseService: k8sService, configuration: Configuration);
        }

        private async Task init()
        {
            var secretClient = new SecretClient(vaultUri: new Uri("https://vienna-test-westus.vault.azure.net/"), credential: new VisualStudioCredential());

            Environment.SetEnvironmentVariable("ASPNETCORE_ENVIRONMENT", "Development");
            Environment.SetEnvironmentVariable("K8SRESOURCEMANAGERPREFIX", "management.core");
            Environment.SetEnvironmentVariable("K8SRESOURCEMANAGERURL", "https://management.azure.com");
            Environment.SetEnvironmentVariable("STORAGE_BLOB_ENDPOINT", ".blob.core.windows.net");
            Environment.SetEnvironmentVariable("STORAGE_ENDPOINT", ".core.windows.net");
            Environment.SetEnvironmentVariable("K8SRESOURCEID", ".windows.net");
            Environment.SetEnvironmentVariable("K8SAUTODETECTTENANT", "false");
            Environment.SetEnvironmentVariable("K8SMICROSOFTTENANTID", "72f988bf-86f1-41af-91ab-2d7cd011db47");
            Environment.SetEnvironmentVariable("K8SKEYVAULT", "aether-vdev-westus2");
            Environment.SetEnvironmentVariable("K8SREGION", "westus2");
            Environment.SetEnvironmentVariable("K8SAML_ENDPOINT", "api.azureml-test.ms");
            Environment.SetEnvironmentVariable("K8S_STS_ENDPOINT", "https://sts.windows.net");
            Environment.SetEnvironmentVariable("SECRET_APPID", "baafc531-8da9-4acf-b4da-9a236820777a");
            Environment.SetEnvironmentVariable("SECRET_APPKEY", (await secretClient.GetSecretAsync("sp-client-secret").ConfigureAwait(false)).Value.Value);
            Environment.SetEnvironmentVariable("SERVICE_NAME", ServiceName);
            Environment.SetEnvironmentVariable("ISK8S", "1");
            Environment.SetEnvironmentVariable("K8SDEPLOYMENT", "onebox");
            Environment.SetEnvironmentVariable("K8STENANTID", "72f988bf-86f1-41af-91ab-2d7cd011db47");
            Environment.SetEnvironmentVariable("K8SACTIVEDIRECTORY", "login.microsoftonline.com");
            Environment.SetEnvironmentVariable("SUBSCRIPTIONID", "ad203158-bc5d-4e72-b764-2607833a71dc");
            Environment.SetEnvironmentVariable("K8SCLOUD_ENDPOINT", "azure.com");
            Environment.SetEnvironmentVariable("K8SBATCH_RESOURCEID", "https://batch.core.windows.net");
            Environment.SetEnvironmentVariable("K8SALLOWEDCLIENTIDS", "d7e6cd48-d39d-462b-b0aa-ebb4cec5dc5a");
            Environment.SetEnvironmentVariable("SECRET_S2S_APPID", "e5517437-fc71-4a01-b32d-cb8b0056d8e7");
            Environment.SetEnvironmentVariable("SECRET_S2S_APPKEY", (await secretClient.GetSecretAsync("kubernetes--sp-client-secret").ConfigureAwait(false)).Value.Value);
            Environment.SetEnvironmentVariable("SYNDICATIONVAULTRG", "vienna-test-westus2");
            Environment.SetEnvironmentVariable("K8SKEYVAULTDNS", "vault.azure.net");
            Environment.SetEnvironmentVariable("DataDirectory", "/data");

            var k8sService = new K8sBaseService(ServiceName, configFilename: ConfigFileName,
                    iniFileNames: new[] { SharedConfigFilename, BbSharedConfigFileName, DistributedTracingConfigFileName });
            await k8sService.InitializeAsync();
            
            this.k8sService = k8sService;
        }

        public void RegisterWebComponents(IServiceCollection services)
        {
        }

        public void RegisterWorkerComponents(IServiceCollection services)
        {
            throw new NotImplementedException();
        }
    }

    [Category("QTestSkip")]
    public class K8sBaseServiceCommonStartupTest
    {
        private K8sBaseServiceCommonStartup startup;

        private StringWriter stringWriter;

        [SetUp]
        public void Init()
        {
            stringWriter = new StringWriter();
            Console.SetOut(stringWriter);

            var entryPoint = new EntryPoint<K8sBaseServiceCommonStartup>();

            Exception? exception = null;

            var task = new Task(() => {
                try
                {
                    entryPoint.DoEntryPoint(new string[] { });
                }
                catch (Exception ex)
                {
                    exception = ex;
                }
            });

            task.Start();

            for (var i = 0; i < 20 && !K8sBaseServiceCommonStartup.Started; i++)
            {
                Task.Delay(TimeSpan.FromSeconds(1)).Wait();
            }

            if (!K8sBaseServiceCommonStartup.Started)
            {
                if (task.IsFaulted)
                {
                    throw exception ?? task.Exception.InnerException;
                }

                throw exception ?? new TimeoutException();
            }

            startup = K8sBaseServiceCommonStartup.instance;
        }

        [TearDown]
        public void Deinit()
        {
            Console.SetOut(new StreamWriter(Console.OpenStandardOutput()));

            Console.Out.Write(stringWriter.ToString());
        }

        [Test]
        public async Task TestATL_redirectConsoleStdout()
        {
            Assert.IsTrue(stringWriter.ToString().Contains("Telemetry library: ATL"), "ATL should be enabled");

            var loggerFactory = startup.Services.BuildServiceProvider().GetRequiredService<ILoggerFactory>();
            var logger = loggerFactory.CreateLogger(typeof(K8sBaseServiceTest));

            var guid = Guid.NewGuid();
            logger.LogWarning("test logger {guid}", guid);

            await Task.Delay(TimeSpan.FromSeconds(1)).ConfigureAwait(false);

            // verifying data in kusto is better
            Assert.IsTrue(stringWriter.ToString().Contains($"test logger {guid}"), "should find log message in console output");
        }
    }
}
