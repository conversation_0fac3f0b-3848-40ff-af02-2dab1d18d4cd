﻿using Microsoft.Aether.BlueBox.AetherK8SBaseService;
using Microsoft.RelInfra.Common.Configuration;
using Moq;
using NUnit.Framework;
using System.Collections.Generic;
using System;
using System.Globalization;

namespace AetherK8SBaseService.Tests
{
    public class K8sBaseServiceConfigTests
    {
        private DynamicConfigurationFlexMock _dummyConfig;
        private IDictionary<string, string> _config;
        private K8sBaseServiceConfig _k8sConfig;

        [SetUp]
        public void Init() 
        {
            _config = new Dictionary<string, string>();
            _config["ThreadPool.MinCompletionPortThreads"] = "2";
            _config["ThreadPool.MinWorkerThreads"] = "4";
            _config["ThreadPool.MonitorInterval"] = "0:0:6.8954321";
            _config["Telemetry.UseAmlTelemetryLibrary"] = "true";
            _config["Telemetry.GenevaAccount"] = "Vienna";
            _config["Telemetry.MetricsConfigurationDisabled"] = "false";
            _config["Telemetry.MetricsConfigurationNamespace"] = string.Empty;
            _config["CentralLogger.BlacklistedProperties"] = new HashSet<string>() { "hello", "world" }.ToString();
            _config["CentralLogger.ScrubBlacklistedProperties"] = "1";

            _dummyConfig = new DynamicConfigurationFlexMock(_config);

            _k8sConfig = new K8sBaseServiceConfig(_dummyConfig, true);
        }

        [Test]
        public void TestThreadPoolMinCompletionPortThreads()
        {
            int expectRet = Int32.Parse(_config["ThreadPool.MinCompletionPortThreads"]);
            Assert.AreEqual(expectRet, _k8sConfig.ThreadPoolMinCompletionPortThreads);
        }

        [Test]
        public void TestThreadPoolMinWorkerThreads()
        {
            int expectRet = Int32.Parse(_config["ThreadPool.MinWorkerThreads"]);
            Assert.AreEqual(expectRet, _k8sConfig.ThreadPoolMinWorkerThreads);
        }

        [Test]
        public void TestThreadPoolMonitorInterval()
        {
            var expectRet = TimeSpan.Parse(_config["ThreadPool.MonitorInterval"], CultureInfo.InvariantCulture);
            Assert.AreEqual(expectRet, _k8sConfig.ThreadPoolMonitorInterval);
        }

        [Test]
        public void TestLogBlacklistedProperties()
        {
            var expectRet = _config["CentralLogger.BlacklistedProperties"];
            Assert.AreEqual(expectRet, _k8sConfig.LogBlacklistedProperties.ToString());
        }

        [Test]
        public void TestScrubBlacklistedProperties()
        {
            var expectRet = _config["CentralLogger.ScrubBlacklistedProperties"];
            Assert.AreEqual(expectRet, _k8sConfig.ScrubBlacklistedProperties ? "1": "0");
        }
    }
}
