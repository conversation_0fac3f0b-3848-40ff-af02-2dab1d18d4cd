﻿using System;
using System.Threading.Tasks;
using Microsoft.Aether.S2S.Common;
using Microsoft.Identity.Client;
using Microsoft.MachineLearning.Common.WebApi.Client;
using Microsoft.RelInfra.Instrumentation.Logging;

namespace Microsoft.Aether.BlueBox.AetherK8SCommon
{
    /// <summary>
    /// Use MSAL.NET instead of ADAL.NET to acquire token.
    /// Refer to this doc for details:
    /// https://learn.microsoft.com/en-us/azure/active-directory/develop/msal-net-migration-confidential-client?tabs=obo#migrate-a-web-api-that-calls-downstream-web-apis
    /// </summary>
    public class MsalAadAppTokenGenerator : AadAppTokenGenerator
    {
        public MsalAadAppTokenGenerator(
            AmlAadAppConfig config,
            IS2STokenProvider s2sTokenProvider,
            string aadAppClientId,
            string aadSniCertName = null,
            string keyVaultName = null) : base(config, s2sTokenProvider, aadAppClientId, aadSniCertName, keyVaultName)
        {
            // Optional aadSniCertName to allow service to pass in related cert for aadAppClientId
        }

        public override async Task<string> AcquireTokenFromAuthorityAsync(string authority, string resource = null)
        {
            CommonLogger.LogInfo($"Acquiring token for appId {_aadAppClientId} and authority {authority} using MSAL");

            resource = resource ?? _config.ArmResourceUri;
            try
            {
                var app = ConfidentialClientApplicationBuilder.Create(_aadAppClientId)
                        .WithAuthority(authority, false)
                        .WithAzureRegion(Environment.GetEnvironmentVariable("AZURE_REGION") ?? ConfidentialClientApplication.AttemptRegionDiscovery)
                        .WithCertificate(_aadcertsni)
                        .Build();

                string[] scopes = new[] { resource + "/.default" };
                var policy = PollyDefaultRetryPolicies.GetActiveDirectoryAsyncRetryPolicy();
                AuthenticationResult result = await policy.ExecuteAsync(() => app.AcquireTokenForClient(scopes)
                    .WithSendX5C(true)
                    .ExecuteAsync()).ConfigureAwait(false);

                CommonLogger.LogInfo($"Acquired token using MSAL successfully.");
                return result.AccessToken;
            }
            catch (Exception ex)
            {
                CommonLogger.LogError($"Unable to fetch token with MSAL for 1st part aad app using sni cert: {ex.Message}");
                throw;
            }
        }
    }
}
