﻿using Microsoft.RelInfra.Instrumentation.Logging;
using System;
using System.Net.Http;
using System.Reflection;

namespace Microsoft.Aether.BlueBox.AetherK8SCommon.Http
{
    public class ViennaHttpClientFactory
    {
        // defined in msftkubeConfig\deploy\templates\k8s\share\environment_variables.mustache
        private static string KubernetesServiceNameEnvVariable { get; set; } = "SERVICE_NAME";
        private static string KubernetesRegionEnvVariable { get; set; } = "K8SREGION";
        private static string KubernetesBuildCommitVariable { get; set; } = "BUILD_COMMIT";
        private static string KubernetesBuildNumberEnvVariable { get; set; } = "BUILD_NUMBER";

        public static HttpClient CreateHttpClient()
        {
            var httpClient = new HttpClient();
            return SetupUserAgent(httpClient);
        }

        public static HttpClient CreateHttpClient(string baseUrl, TimeSpan? timeout = null)
        {
            var httpClient = new HttpClient
            {
                BaseAddress = new Uri(baseUrl),
            };
            if (timeout != null)
            {
                httpClient.Timeout = timeout.Value;
            }
            return SetupUserAgent(httpClient);
        }

        private static HttpClient SetupUserAgent(HttpClient httpClient)
        {
            var serviceName = Environment.GetEnvironmentVariable(KubernetesServiceNameEnvVariable) ?? "Unknown";
            var region = Environment.GetEnvironmentVariable(KubernetesRegionEnvVariable) ?? "Unknown";
            var buildCommit = Environment.GetEnvironmentVariable(KubernetesBuildCommitVariable) ?? "Unknown";
            var buildNumber = Environment.GetEnvironmentVariable(KubernetesBuildNumberEnvVariable) ?? "Unknown";

            CommonLogger.LogInfo($"Discovered service {serviceName} using environment variable {KubernetesServiceNameEnvVariable}");
            
            var serviceVersion = Assembly.GetEntryAssembly()
                                         .GetCustomAttribute<AssemblyInformationalVersionAttribute>()
                                         ?.InformationalVersion;

            var service = $"Pipelines-{serviceName}/{serviceVersion} ({buildNumber} {buildCommit} {region})";
            if (!httpClient.DefaultRequestHeaders.UserAgent.TryParseAdd(service))
            {
                CommonLogger.LogWarning("Could not parse the given user agent string: {service}", service);
            }
            CommonLogger.LogInfo($"HttpClient Default Headers: {httpClient.DefaultRequestHeaders}");

            return httpClient;
        }
    }
}
