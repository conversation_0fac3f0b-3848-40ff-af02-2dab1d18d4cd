﻿using System;
using System.Globalization;
using System.Net;
using System.Security.Cryptography.X509Certificates;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Aether.BackendCommon;
using Microsoft.Aether.BackendCommon.KeyVault;
using Microsoft.Aether.S2S.Common;
using Microsoft.IdentityModel.Clients.ActiveDirectory;
using Microsoft.MachineLearning.Common.Adal.Utilities;
using Microsoft.RelInfra.Instrumentation.Logging;

namespace Microsoft.Aether.BlueBox.AetherK8SCommon
{
    // To get first party app token using sni certificate
    // Taken from vienna repo:
    // https://msdata.visualstudio.com/Vienna/_git/vienna?path=%2Fsrc%2Fazureml-api%2Fsrc%2FCommon%2FArm%2FAadAppTokenGenerator.cs&_a=contents&version=GBmaster

    public interface IAadAppTokenGenerator
    {
        Task<string> AcquireTokenWithDynamicTenantDetectionAsync(string subscription, string tenantId, string resource = null);

        Task<string> AcquireTokenFromTenantAsync(string tenantId, string resource = null);

        Task<string> AcquireTokenFromAuthorityAsync(string authority, string resource = null);
    }

    public class AadAppTokenGenerator : IAadAppTokenGenerator
    {
        protected readonly X509Certificate2 _aadcertsni;
        protected readonly TenantResolver _tenantResolver;
        protected readonly AmlAadAppConfig _config;
        protected readonly IS2STokenProvider _s2sTokenProvider;
        protected readonly string _aadAppClientId;

        public AadAppTokenGenerator(
            AmlAadAppConfig config,
            IS2STokenProvider s2sTokenProvider,
            string aadAppClientId,
            string aadSniCertName = null,
            string keyVaultName = null)
        {
            _config = config;
            _s2sTokenProvider = s2sTokenProvider;
            _aadAppClientId = aadAppClientId;

            _tenantResolver = new TenantResolver(armEndpoint: _config.ArmEndpoint);
            _aadcertsni = LoadAadCertificate(aadSniCertName, keyVaultName);
        }

        public async Task<string> AcquireTokenWithDynamicTenantDetectionAsync(string subscription, string tenantId, string resource = null)
        {
            var authority = await _tenantResolver.GetAuthorityAsync(subscription, CancellationToken.None).ConfigureAwait(false);
            if (!string.IsNullOrEmpty(authority))
            {
                return await AcquireTokenFromAuthorityAsync(authority, resource).ConfigureAwait(false);
            }
            else
            {
                CommonLogger.LogError($"Unable to get authority details from ARM for subscription {subscription}. Fallback to AcquireTokenFromTenantAsync");
                return await AcquireTokenFromTenantAsync(tenantId, resource).ConfigureAwait(false);
            }
        }

        public async Task<string> AcquireTokenFromTenantAsync(string tenantId, string resource = null)
        {
            string authority = string.Format(CultureInfo.InvariantCulture, _config.AadAuthority, tenantId);
            string token = await this.AcquireTokenFromAuthorityAsync(authority, resource).ConfigureAwait(false);
            return token;
        }

        public virtual async Task<string> AcquireTokenFromAuthorityAsync(string authority, string resource = null)
        {
            resource = resource ?? _config.ArmResourceUri;

            var context = new AuthenticationContext(authority, false);
            CommonLogger.LogDebug($"Acquiring token for appId {_aadAppClientId} and authority {authority}");

            var cred = new ClientAssertionCertificate(_aadAppClientId, _aadcertsni);
            try
            {
                AuthenticationResult result = await context.AcquireTokenAsyncWithRetry(resource, cred, sendX5c: true).ConfigureAwait(false);
                CommonLogger.LogInfo("Fetched token for 1st party aad app using sni cert.");
                return result.AccessToken;
            }
            catch (Exception e)
            {
                CommonLogger.LogError($"Unable to fetch token for 1st part aad app using sni cert: {e.Message}");
                throw;
            }
        }

        private X509Certificate2 LoadAadCertificate(string certName = null, string keyVaultName = null)
        {
            try
            {
                string vaultDNS = _config.KeyVaultDNS;
                string appId = _s2sTokenProvider.AppId;
                string appKey = _s2sTokenProvider.AppKey;

                keyVaultName = keyVaultName ?? _config.KeyVaultName;
                certName = certName ?? _config.AadSniCertName;

                var keyValutUrl = $"https://{keyVaultName}.{vaultDNS}";
                var keyVaultAuthority = _tenantResolver.GetKeyVaultAuthorityAsync(keyValutUrl).GetAwaiter().GetResult();

                KeyVaultClient keyValutClient = null;
                if (_s2sTokenProvider is S2STokenProvider provider && provider.CertificateService != null)
                {
                    CommonLogger.LogInfo($"Creating key vault client for vault {keyVaultName} with appId and certificate");
                    keyValutClient = KeyVaultClient.CreateWithKeyVaultName(
                        vaultName: keyVaultName,
                        vaultDNS: vaultDNS,
                        messageHandler: AzureAuthentication.CreateKeyVaultMessageHandler(
                            authority: keyVaultAuthority,
                            autoDetectTenant: false,
                            keyVaultResource: _config.KeyVaultEndpoint,
                            subscriptionId: null,
                            clientId: appId,
                            certificateService: provider.CertificateService));
                }
                else
                {
                    CommonLogger.LogInfo($"Creating key vault client for vault {keyVaultName} with appId and appKey");
                    keyValutClient = KeyVaultClient.CreateWithKeyVaultName(
                        vaultName: keyVaultName,
                        vaultDNS: vaultDNS,
                        messageHandler: AzureAuthentication.CreateKeyVaultMessageHandler(
                            authority: keyVaultAuthority,
                            autoDetectTenant: false,
                            KeyVaultResource: _config.KeyVaultEndpoint,
                            subscriptionId: null,
                            credentials: new NetworkCredential(appId, appKey)));
                }

                CommonLogger.LogInfo($"Trying to load sni cert from keyvault {keyVaultName} and secret name {certName}");

                var certificateSecret = keyValutClient.GetSecretValueAsync(certName).GetAwaiter().GetResult();
                var privateKeyBytes = Convert.FromBase64String(certificateSecret);

                return new X509Certificate2(privateKeyBytes);
            }
            catch (Exception e)
            {
                CommonLogger.LogError($"Failed to load sni cert from keyvault: {e.Message}");
                return null;
            }
        }
    }
}
