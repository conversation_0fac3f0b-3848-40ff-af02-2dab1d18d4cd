﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Aether.StorageDescription;
using Microsoft.RelInfra.Common.Configuration;
using Microsoft.RelInfra.Common.Exceptions;
using Microsoft.RelInfra.Common.Secrets;

namespace Microsoft.Aether.BlueBox.AetherK8SCommon
{
    public class SharedCosmosDbConfig : NestedVerifiableConfig
    {
        private readonly SecretProvider _secretProvider;

        public SharedCosmosDbConfig(IRelInfraConfiguration config, SecretProvider secretProvider)
            : base(config, configName: nameof(SharedCosmosDbConfig))
        {
            _secretProvider = secretProvider;
        }

        public async Task<ProvisionedCosmosDbConnectionInfo> GetCosmosDbConnectionInfo()
        {
            string connectionString = await GetSharedCosmosDbKeySecretKeyAsync();
            if (!TryParse(connectionString, out ProvisionedCosmosDbConnectionInfo account))
            {
                throw new RelInfraConfigurationException("Cannot parse account from cosmos db connection string");
            }
            return account;
        }

        private async Task<string> GetSharedCosmosDbKeySecretKeyAsync() =>
            await Config.GetSecretStringAsync(_secretProvider, "SharedCosmosDbConnection.KeySecretName");

        //sigh stolen from here https://msdata.visualstudio.com/Vienna/Vienna%20All/_git/vienna?path=%2Fsrc%2Fazureml-api%2Fsrc%2FCommon%2FDocumentDb%2FCosmosDbAccount.cs&version=GBmaster
        private static bool TryParse(string connectionString, out ProvisionedCosmosDbConnectionInfo cred)
        {
            cred = null;
            var parts = connectionString.Trim()
                .Split(new[] {';'}, StringSplitOptions.RemoveEmptyEntries)
                .Select(part => part.Split(new[] {'='}, 2))
                .ToDictionary(part => part[0], part => part[1]);

            //be nice to log something here
            if (!parts.TryGetValue("AccountEndpoint", out string accountEndpoint))
            {
                return false;
            }

            if (!parts.TryGetValue("AccountKey", out string accountKey))
            {
                return false;
            }

            cred = new ProvisionedCosmosDbConnectionInfo
            {
                CosmosDbEndpoint = accountEndpoint,
                CosmosDbKey = accountKey
            };
            return true;
        }
    }
}
