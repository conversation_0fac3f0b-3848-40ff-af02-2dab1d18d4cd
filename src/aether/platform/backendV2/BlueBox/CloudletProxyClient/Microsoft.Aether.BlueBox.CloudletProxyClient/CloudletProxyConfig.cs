﻿using Microsoft.RelInfra.Common.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Microsoft.Aether.BlueBox.CloudletProxyClient
{
    public class CloudletProxyConfig : NestedVerifiableConfig
    {
        public CloudletProxyConfig(IRelInfraConfiguration config)
            : base(config, configName: nameof(CloudletProxyConfig))
        {
            CloudletProxyServiceEndpoint = Config?.GetString("CloudletProxyConfig.CloudletProxyInternalEndpoint");
            JobPath = Config?.GetString("CloudletProxyConfig.JobPath");
        }

        public CloudletProxyConfig() : this(null) { }

        public string CloudletProxyServiceEndpoint { get; set; }

        public string JobPath { get; set; }
    }
}
