﻿using Microsoft.Aether.BlueBox.AetherK8SCommon.Http;
using Microsoft.Aether.DataContracts;
using Microsoft.Aether.DataContracts.CloudSystem;
using Microsoft.Aether.DataContracts.Entity;
using Microsoft.Aether.S2S.Common;
using Microsoft.MachineLearning.CloudletProxy.Contracts.V1;
using Microsoft.RelInfra.Extensions;
using Microsoft.RelInfra.Instrumentation.Logging;

namespace Microsoft.Aether.BlueBox.CloudletProxyClient
{
    public class CloudletProxyClient : ICloudletProxyClient
    {
        private readonly CloudletProxyConfig _config;

        private readonly IS2STokenProvider _tokenProvider;

        private readonly HttpClient _httpCloudletProxyClient;

        public CloudletProxyClient(CloudletProxyConfig config, IS2STokenProvider tokenProvider)
        {
            _config = config;
            _tokenProvider = tokenProvider;
            _httpCloudletProxyClient = ViennaHttpClientFactory.CreateHttpClient();
        }

        public async Task<SubmitJobResponseDto> SubmitAsync(WorkspaceIdentity workspaceIdentity, string cloudSystem, SubmitJobDto submitJobDto, CreatedBy createdBy)
        {
            CommonLogger.LogEntityInfo(submitJobDto?.JobDefinition?.Name, $"Submit new job with name:{submitJobDto?.JobDefinition?.Name} to {cloudSystem}");

            var serverPath = _config.CloudletProxyServiceEndpoint + "/" + FormatServerBasePath(_config.JobPath, workspaceIdentity, cloudSystem) + "/jobs";

            return await _httpCloudletProxyClient.PostEntityWithAuthHeaderAsync<SubmitJobDto, SubmitJobResponseDto>(
                serverPath: serverPath,
                entity: submitJobDto,
                authToken: await _tokenProvider.GetTokenAsync(),
                customHeaders: createdBy.GetCreatedByHeaders());
        }

        public async Task CancelAsync(WorkspaceIdentity workspaceIdentity, string cloudSystem, string jobName, CreatedBy createdBy)
        {
            CommonLogger.LogEntityInfo(jobName, $"Cancel job with Name:{jobName} from {cloudSystem}");

            var serverPath = _config.CloudletProxyServiceEndpoint + "/" + FormatServerBasePath(_config.JobPath, workspaceIdentity, cloudSystem) + $"/jobs/{jobName}/cancel";
            var authToken = await _tokenProvider.GetTokenAsync().ConfigureAwait(false);

            await _httpCloudletProxyClient.PutEntityNoReturnWithAuthHeaderAsync<string>(
                serverPath: serverPath,
                entity: string.Empty,
                authToken: authToken,
                jsonSerializerSettings: null,
                customHeaders: createdBy.GetCreatedByHeaders());
        }

        public async Task<CloudJobResultInfo> GetResultAsync(WorkspaceIdentity workspaceIdentity, string cloudSystem, string jobName, CreatedBy createdBy)
        {
            CommonLogger.LogEntityInfo(jobName, $"Get job result with Name:{jobName} from {cloudSystem}");

            var serverPath = _config.CloudletProxyServiceEndpoint + "/" + FormatServerBasePath(_config.JobPath, workspaceIdentity, cloudSystem) + $"/jobs/{jobName}/result";

            return await _httpCloudletProxyClient.GetEntityWithAuthHeaderAsync<CloudJobResultInfo>(
                serverPath: serverPath,
                authToken: await _tokenProvider.GetTokenAsync(),
                customHeaders: createdBy.GetCreatedByHeaders());
        }

        public async Task<CloudJobStatus> GetStatusAsync(WorkspaceIdentity workspaceIdentity, string cloudSystem, string jobName, CreatedBy createdBy)
        {
            CommonLogger.LogEntityInfo(jobName, $"Get job status with Name:{jobName} from {cloudSystem}");

            var serverPath = _config.CloudletProxyServiceEndpoint + "/" + FormatServerBasePath(_config.JobPath, workspaceIdentity, cloudSystem) + $"/jobs/{jobName}/status";

            return await _httpCloudletProxyClient.GetEntityWithAuthHeaderAsync<CloudJobStatus>(
                serverPath: serverPath,
                authToken: await _tokenProvider.GetTokenAsync(),
                customHeaders: createdBy.GetCreatedByHeaders());
        }

        private static string FormatServerBasePath(string path, WorkspaceIdentity workspaceIdentity, string cloudSystem)
        {
            return path
                .Replace("{subscriptionId}", workspaceIdentity.SubscriptionId)
                .Replace("{resourceGroupName}", workspaceIdentity.ResourceGroupName)
                .Replace("{workspaceName}", workspaceIdentity.WorkspaceName)
                .Replace("{cloudSystem}", cloudSystem);
        }
    }
}
