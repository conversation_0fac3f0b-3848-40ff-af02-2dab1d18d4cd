﻿using Microsoft.Aether.DataContracts;
using Microsoft.Aether.DataContracts.CloudSystem;
using Microsoft.Aether.DataContracts.Entity;
using Microsoft.MachineLearning.CloudletProxy.Contracts.V1;

namespace Microsoft.Aether.BlueBox.CloudletProxyClient
{
    public interface ICloudletProxyClient
    {
        Task<SubmitJobResponseDto> SubmitAsync(WorkspaceIdentity workspaceIdentity, string cloudSystem, SubmitJobDto submitJobDto, CreatedBy createdBy);

        Task<CloudJobStatus> GetStatusAsync(WorkspaceIdentity workspaceIdentity, string cloudSystem, string jobName, CreatedBy createdBy);

        Task<CloudJobResultInfo> GetResultAsync(WorkspaceIdentity workspaceIdentity, string cloudSystem, string jobName, CreatedBy createdBy);

        Task CancelAsync(WorkspaceIdentity workspaceIdentity, string cloudSystem, string jobName, CreatedBy createdBy);
    }
}