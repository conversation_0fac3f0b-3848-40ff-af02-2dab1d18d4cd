﻿using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using Microsoft.RelInfra.Instrumentation.Logging;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace Microsoft.Aether.BlueBox.AetherK8SBaseService
{
    public class ThreadPoolMonitor : IHostedService, IDisposable
    {
        private readonly TimeSpan _loggingInterval;
        private Timer _timer;

        public ThreadPoolMonitor(TimeSpan loggingInterval)
        {
            _loggingInterval = loggingInterval;
        }

        public ThreadPoolMonitor(IOptionsMonitor<K8sBaseServiceConfig> config)
            : this(config.CurrentValue.ThreadPoolMonitorInterval)
        {
            _timer = new Timer(
                    callback: (o) => LogThreadPoolStats(),
                    state: null,
                    dueTime: TimeSpan.Zero,
                    period: _loggingInterval
                    );
        }


        public void Start()
        {
            _timer = new Timer(
                    callback: (o) => LogThreadPoolStats(),
                    state: null,
                    dueTime: TimeSpan.Zero,
                    period: _loggingInterval
                    );
        }

        public Task StartAsync(CancellationToken cancellationToken)
        {
            _timer = new Timer(
                    callback: (o) => LogThreadPoolStats(),
                    state: null,
                    dueTime: TimeSpan.Zero,
                    period: _loggingInterval
                    );
            return Task.CompletedTask;
        }

        public void Stop()
        {
            _timer?.Dispose();
        }

        private void LogThreadPoolStats()
        {
            try
            {
                ThreadPool.GetAvailableThreads(out int freeWorkerThreads, out int freeIoThreads);
                ThreadPool.GetMaxThreads(out int maxWorkerThreads, out int maxIoThreads);
                ThreadPool.GetMinThreads(out int minWorkerThreads, out int minIoThreads);

                CommonLogger.LogEntityInfo(traceId: "ThreadPoolInfo",
                    message: $"BusyIO={maxIoThreads - freeIoThreads},FreeIO={freeIoThreads},MinIO={minIoThreads},MaxIO={maxIoThreads}, " +
                             $"Busy={maxWorkerThreads - freeWorkerThreads},Free={freeWorkerThreads},Min={minWorkerThreads},Max={maxWorkerThreads}");
            }
            catch(Exception ex)
            {
                CommonLogger.LogEntityError(traceId: "ThreadPoolInfo",
                    message: $"Failed to collect thread pool size due to exception {ex}");
            }
        }

        public Task StopAsync(CancellationToken cancellationToken)
        {
            _timer?.Change(Timeout.Infinite, 0);
            return Task.CompletedTask;
        }
        public void Dispose()
        {
            _timer?.Dispose();
        }
    }
}
