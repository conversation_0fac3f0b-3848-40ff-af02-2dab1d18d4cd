﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ResolveAssemblyWarnOrErrorOnTargetArchitectureMismatch>None</ResolveAssemblyWarnOrErrorOnTargetArchitectureMismatch>
    <QInstrumentForCoverage>false</QInstrumentForCoverage>
  </PropertyGroup>
  <PropertyGroup>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <!-- NU1604 is considered a failure on the build server, but it was a Warning locally -->
    <WarningsAsErrors>NU1604</WarningsAsErrors>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.ApplicationInsights.AspNetCore" />
    <PackageReference Include="Microsoft.ApplicationInsights.Profiler.AspNetCore"/>
    <PackageReference Include="Microsoft.Extensions.Logging.Console" />
    <PackageReference Include="Microsoft.Graph" />
    <PackageReference Include="Microsoft.IdentityModel.Clients.ActiveDirectory" />
    <PackageReference Include="Microsoft.IdentityModel.Protocols" />
    <PackageReference Include="Microsoft.IdentityModel.Protocols.OpenIdConnect" />
    <PackageReference Include="Microsoft.AIPlatform.WorkloadIdentity" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\..\RelInfra\Microsoft.RelInfra.Instrumentation.Metrics\Microsoft.RelInfra.Instrumentation.Metrics.csproj" />
    <ProjectReference Include="..\..\..\RelInfra\RelInfra.Instrumentation.Statsd.Library\src\RelInfra.Instrumentation.Statsd.Library.csproj" />
    <ProjectReference Include="..\..\shared\Microsoft.Aether.BackendCommon\Microsoft.Aether.BackendCommon.csproj" />
    <ProjectReference Include="..\..\..\RelInfra\RelInfra.Common.Library\src\Common\RelInfra.Common.csproj" />
    <ProjectReference Include="..\..\shared\Relinfra.Storage.CosmosDb\Relinfra.Storage.CosmosDb.csproj" />
    <ProjectReference Include="..\AetherK8SCommon\Microsoft.Aether.BlueBox.AetherK8SCommon.csproj" />
    <ProjectReference Include="..\..\WrapperApplicationInsightsAspNetCore\Microsoft.Aether.BlueBox.WrapperApplicationInsightsAspNetCore.csproj" />
    <ProjectReference Include="..\S2S.Common\Microsoft.Aether.BlueBox.S2S.Common\Microsoft.Aether.BlueBox.S2S.Common.csproj" />
  </ItemGroup>
</Project>
