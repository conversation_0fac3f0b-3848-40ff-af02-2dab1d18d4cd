﻿// <copyright file="ConnectableSubGraph.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Runtime.CompilerServices;
using AEVADataContracts = Microsoft.Aether.AEVA.DataContracts;
using AetherDataContracts = Microsoft.Aether.DataContracts;

[assembly: InternalsVisibleTo("Microsoft.Aether.AE3pService.Tests")]

namespace Microsoft.Aether.AEVA.DataContractConverter
{
    // This class is used to convert external api contracts (Microsoft.Aether.AEVA.DataContracts) and data store contracts (Microsoft.Aether.DataContracts)
    // back and forth.
    // The external api contracts are a subset of the metastore contracts
    // it uses reflection to cover the 4 basic cases
    // 1) copy the value over for non data contract fields
    // 2) convert data contract enum values based on name
    // 3) convert an IEnumerable of data contract recursively by the elements
    // 4) convert data contract fields recursively
    // For the rare cases that aren't covered by the above 4, you can supply a custom func to convert a specific field
    // You can supply a Dictionary with a Tuple<Type, Type, string> as the key where the first type is the source field type
    // the second type is the dest field type and the string is the field name, The value should be a Func<SourceType, DestType>
    public class ContractConverter
    {
        public static TDest Convert<TSource, TDest>(TSource source,
            Dictionary<Tuple<Type, Type, string>, dynamic>? customConvert = null)
        where TDest : class, new()
        {
            if (source == null)
            {
                return null;
            }

            var sourceProps = typeof(TSource).GetProperties();
            var destProps = typeof(TDest).GetProperties();
            TDest result = new TDest();

            foreach (var sourceProp in sourceProps)
            {
                foreach (var destProp in destProps)
                {
                    if (sourceProp.Name == destProp.Name)
                    {
                        var key = new Tuple<Type, Type, string>(sourceProp.PropertyType, destProp.PropertyType, sourceProp.Name);

                        // Handle nullable enums
                        // Ref: How to identify nullable type - https://docs.microsoft.com/en-us/dotnet/csharp/programming-guide/nullable-types/how-to-identify-a-nullable-type
                        var sourceBaseType = Nullable.GetUnderlyingType(sourceProp.PropertyType);
                        if (sourceBaseType != null && sourceBaseType.IsEnum)
                        {
                            dynamic input = sourceProp.GetValue(source, null);
                            if (input != null)
                            {
                                var destBaseType = Nullable.GetUnderlyingType(destProp.PropertyType);
                                var enumInt = (int)sourceProp.GetValue(source, null);
                                var name = Enum.GetName(sourceBaseType, enumInt);
                                destProp.SetValue(result, Enum.Parse(destBaseType, name));
                            }
                        }
                        // if a custom converter exists for this field, use it
                        else if (customConvert != null && customConvert.ContainsKey(key))
                        {
                            dynamic input = System.Convert.ChangeType(sourceProp.GetValue(source, null), sourceProp.PropertyType);
                            var destValue = customConvert[key](input);
                            destProp.SetValue(result, destValue, null);
                        }
                        else if (sourceProp.PropertyType == destProp.PropertyType)
                        {
                            // Exact same field name and type => assign value to result
                            destProp.SetValue(result, sourceProp.GetValue(source, null), null);
                        }
                        else
                        {
                            // Same field name but another contract type, in the case of enum
                            // get the matching enum value from the result Type by name
                            // (assumes the name exists in both)
                            if (sourceProp.PropertyType.IsEnum && destProp.PropertyType.IsEnum)
                            {
                                var enumInt = (int)sourceProp.GetValue(source, null);
                                var name = Enum.GetName(sourceProp.PropertyType, enumInt);
                                destProp.SetValue(result, Enum.Parse(destProp.PropertyType, name));
                            }
                            else
                            {
                                // The field is not an enum type
                                // In case the field implements or is IEnumerable
                                if (sourceProp.PropertyType.GetInterfaces().Any(t => t.IsGenericType && t.GetGenericTypeDefinition() == typeof(IEnumerable<>))
                                        || (sourceProp.PropertyType.IsGenericType && sourceProp.PropertyType.GetGenericArguments().Length == 1 &&
                                                typeof(IEnumerable<>).MakeGenericType(sourceProp.PropertyType.GetGenericArguments()[0]).IsAssignableFrom(sourceProp.PropertyType)))
                                {
                                    // if the source value is null, assign and continue
                                    if (!(sourceProp.GetValue(source, null) is IEnumerable sourceValue))
                                    {
                                        destProp.SetValue(result, null, null);
                                    }
                                    else
                                    {
                                        // find the generic type of the IEnumerables
                                        Type sourceEnumerableType = sourceProp.PropertyType.GetGenericArguments()[0];
                                        Type destEnumerableType = destProp.PropertyType.GetGenericArguments()[0];
                                        // if they are equal, assign the values to the dest collection
                                        if (sourceEnumerableType == destEnumerableType)
                                        {
                                            // dictionary with same key type, need to convert values
                                            if (sourceProp.PropertyType.GetGenericArguments().Length == 2 && destProp.PropertyType.GetGenericArguments().Length == 2 &&
                                                sourceProp.PropertyType.GetGenericArguments()[1] != destProp.PropertyType.GetGenericArguments()[1])
                                            {
                                                var destDictionary = (System.Collections.IDictionary)Activator.CreateInstance(typeof(Dictionary<,>).MakeGenericType(new Type[] { destEnumerableType, destProp.PropertyType.GetGenericArguments()[1] }));
                                                // If Dictionary value is a collection, we need to resolve it one by one
                                                if (sourceProp.PropertyType.GetGenericArguments()[1].GetInterface(nameof(IEnumerable)) != null)
                                                {
                                                    destEnumerableType = destProp.PropertyType.GetGenericArguments()[1].GetGenericArguments()[0];
                                                    sourceEnumerableType = sourceProp.PropertyType.GetGenericArguments()[1].GetGenericArguments()[0];
                                                    Type destPropGenericType = destProp.PropertyType.GetGenericArguments()[1].GetGenericTypeDefinition();
                                                    foreach (var sourceKey in ((IDictionary)sourceValue).Keys)
                                                    {
                                                        var dictValue = (IList)((IDictionary)sourceValue)[sourceKey];
                                                        var destEnumerable = ConvertEnumerableSource(
                                                            sourceEnumerableType,
                                                            destEnumerableType,
                                                            destPropGenericType,
                                                            dictValue);
                                                        destDictionary.Add(sourceKey, destEnumerable);
                                                    }
                                                }
                                                else
                                                {
                                                    foreach (var sourceKey in ((IDictionary)sourceValue).Keys)
                                                    {
                                                        MethodInfo convertValue = typeof(ContractConverter).GetMethod(nameof(Convert));
                                                        convertValue = convertValue.MakeGenericMethod(sourceProp.PropertyType.GetGenericArguments()[1], destProp.PropertyType.GetGenericArguments()[1]);
                                                        var destValue = convertValue.Invoke(null, new[] { ((IDictionary)sourceValue)[sourceKey], customConvert });
                                                        destDictionary.Add(sourceKey, destValue);
                                                    }
                                                }
                                                destProp.SetValue(result, destDictionary, null);
                                            }
                                            else
                                            {
                                                destProp.SetValue(result, sourceProp.GetValue(source, null), null);
                                            }
                                        }
                                        // otherwise convert recursively on the elements
                                        else
                                        {
                                            var destEnumerable = ConvertEnumerableSource(sourceEnumerableType, destEnumerableType, destProp.PropertyType.GetGenericTypeDefinition(), sourceValue);
                                            destProp.SetValue(result, destEnumerable, null);
                                        }
                                    }
                                }
                                else
                                {
                                    // In case the field is another contract type, convert the child field contract recursively
                                    MethodInfo convert = typeof(ContractConverter).GetMethod(nameof(Convert));
                                    convert = convert.MakeGenericMethod(sourceProp.PropertyType, destProp.PropertyType);
                                    destProp.SetValue(result, convert.Invoke(null, new[] { sourceProp.GetValue(source, null), customConvert }), null);
                                }
                            }
                        }
                    }
                }
            }

            return result;
        }

        private static object ConvertEnumerableSource(Type sourceEnumerableType, Type destEnumerableType, Type destPropGenericTypeDefinition, IEnumerable sourceValue)
        {
            Type destPropGenericType;
            if (destPropGenericTypeDefinition == typeof(ISet<>)
                || destPropGenericTypeDefinition == typeof(HashSet<>))
            {
                destPropGenericType = typeof(HashSet<>);
            }
            else
            {
                destPropGenericType = typeof(List<>);
            }
            destPropGenericType = destPropGenericType.MakeGenericType(destEnumerableType);
            object destEnumerable = Activator.CreateInstance(destPropGenericType);
            MethodInfo addMethod = destPropGenericType.GetMethod("Add");
            foreach (var v in sourceValue)
            {
                if (sourceEnumerableType.IsEnum && destEnumerableType.IsEnum)
                {
                    var enumInt = (int)v;
                    var name = Enum.GetName(sourceEnumerableType, enumInt);
                    var destValue = Enum.Parse(destEnumerableType, name);
                    addMethod.Invoke(destEnumerable, new object[] { destValue });
                }
                else
                {
                    MethodInfo convert = typeof(ContractConverter).GetMethod(nameof(Convert));
                    convert = convert.MakeGenericMethod(sourceEnumerableType, destEnumerableType);
                    var destValue = convert.Invoke(null, new[] { v, null });
                    addMethod.Invoke(destEnumerable, new object[] { destValue });
                }
            }
            return destEnumerable;
        }

        //  Converter from backend DataLocationStorageType to AEVA DataLocationStorageType.
        //  The AEVA version has StorageType of "None" in place of "Cosmos"
        public static Dictionary<Tuple<Type, Type, string>, dynamic> GetDataLocationCustomConverters()
        {
            return new Dictionary<Tuple<Type, Type, string>, dynamic>
            {
                {
                    new Tuple<Type, Type, string>(typeof(AetherDataContracts.DataLocationStorageType),
                        typeof(AEVADataContracts.DataLocationStorageType), "StorageType"),
                    new Func<AetherDataContracts.DataLocationStorageType, AEVADataContracts.DataLocationStorageType>(
                        type =>
                        {
                            switch (type)
                            {
                                case AetherDataContracts.DataLocationStorageType.Artifact:
                                    return AEVADataContracts.DataLocationStorageType.Artifact;

                                case AetherDataContracts.DataLocationStorageType.Snapshot:
                                    return AEVADataContracts.DataLocationStorageType.Snapshot;

                                case AetherDataContracts.DataLocationStorageType.AzureBlob:
                                    return AEVADataContracts.DataLocationStorageType.AzureBlob;

                                case AetherDataContracts.DataLocationStorageType.SavedAmlDataset:
                                    return AEVADataContracts.DataLocationStorageType.SavedAmlDataset;

                                default:
                                    return AEVADataContracts.DataLocationStorageType.None;
                            }
                        })
                }
            };
        }

        //  Reverse converter from AEVA DataLocationStorageType to backend DataLocationStorageType.
        //  The AEVA version has StorageType of "None" in place of "Cosmos"
        public static Dictionary<Tuple<Type, Type, string>, dynamic> GetDataLocationReverseCustomConverters()
        {
            return new Dictionary<Tuple<Type, Type, string>, dynamic>
            {
                {
                    new Tuple<Type, Type, string>(typeof(AEVADataContracts.DataLocationStorageType),
                        typeof(AetherDataContracts.DataLocationStorageType), "StorageType"),
                    new Func<AEVADataContracts.DataLocationStorageType, AetherDataContracts.DataLocationStorageType>(
                        type =>
                        {                            
                            switch (type)
                            {
                                case AEVADataContracts.DataLocationStorageType.None:
                                    return AetherDataContracts.DataLocationStorageType.Cosmos;

                                case AEVADataContracts.DataLocationStorageType.Artifact:
                                    return AetherDataContracts.DataLocationStorageType.Artifact;

                                case AEVADataContracts.DataLocationStorageType.Snapshot:
                                    return AetherDataContracts.DataLocationStorageType.Snapshot;

                                case AEVADataContracts.DataLocationStorageType.AzureBlob:
                                    return AetherDataContracts.DataLocationStorageType.AzureBlob;

                                case AEVADataContracts.DataLocationStorageType.SavedAmlDataset:
                                    return AetherDataContracts.DataLocationStorageType.SavedAmlDataset;

                                default:
                                    return AetherDataContracts.DataLocationStorageType.Cosmos;
                            }
                        })
                }
            };
        }

        // This function is migrated in this PR: https://msdata.visualstudio.com/Vienna/_git/vienna/pullrequest/890859
        // Also clean up duplicated function in PipelineRunsController: https://msdata.visualstudio.com/Vienna/_git/vienna?path=%2Fsrc%2Faether%2Fplatform%2FbackendV2%2FBlueBox%2FAE3pService%2FService%2FControllers%2FPipelineRunsController.cs&_a=contents&version=GBmaster
        public static Dictionary<Tuple<Type, Type, string>, dynamic> GetPipelineStatusCustomConverters()
        {
            return new Dictionary<Tuple<Type, Type, string>, dynamic>
            {
                {
                    new Tuple<Type, Type, string>(typeof(AetherDataContracts.ExperimentStatusCode), typeof(AEVADataContracts.PipelineRunStatusCode), "StatusCode"),
                    new Func<AetherDataContracts.ExperimentStatusCode, AEVADataContracts.PipelineRunStatusCode>(
                        type =>
                        {
                            switch (type)
                            {
                                case AetherDataContracts.ExperimentStatusCode.NotStarted:
                                    return AEVADataContracts.PipelineRunStatusCode.NotStarted;

                                case AetherDataContracts.ExperimentStatusCode.Running:
                                    return AEVADataContracts.PipelineRunStatusCode.Running;

                                case AetherDataContracts.ExperimentStatusCode.Failed:
                                    return AEVADataContracts.PipelineRunStatusCode.Failed;

                                case AetherDataContracts.ExperimentStatusCode.Finished:
                                    return AEVADataContracts.PipelineRunStatusCode.Finished;

                                case AetherDataContracts.ExperimentStatusCode.CancelRequested:
                                    return AEVADataContracts.PipelineRunStatusCode.CancelRequested;

                                case AetherDataContracts.ExperimentStatusCode.Canceled:
                                    return AEVADataContracts.PipelineRunStatusCode.Canceled;

                                case AetherDataContracts.ExperimentStatusCode.Failing:
                                    return AEVADataContracts.PipelineRunStatusCode.Failed;

                                case AetherDataContracts.ExperimentStatusCode.Queued:
                                    return AEVADataContracts.PipelineRunStatusCode.Queued;

                                default:
                                    throw new ArgumentException($"experimentstatus {type} can't be converted into pipelinerunstatus");
                            }
                        })
                }
            };
        }
    }
}
