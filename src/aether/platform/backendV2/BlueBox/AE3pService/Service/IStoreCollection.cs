﻿// <copyright file="IStoreCollection.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Security.Claims;
using Microsoft.Aether.DataContracts.Metastore;
using Microsoft.Aether.DataContracts;
using Microsoft.Aether.DataContracts.Entity;

namespace Microsoft.Aether.AE3pService
{
    ///<Summary>
    ///</Summary>
    public interface IStoreCollection
    {
        ///<Summary>
        ///</Summary>
        IDatasetStore GetDatasetStore(ClaimsPrincipal user, WorkspaceIdentity workspaceIdentity);

        ///<Summary>
        ///</Summary>
        IDataTypeStore GetDataTypeStore(ClaimsPrincipal user, WorkspaceIdentity workspaceIdentity);

        ///<Summary>
        ///</Summary>
        IModuleStore GetModuleStore(ClaimsPrincipal user, WorkspaceIdentity workspaceIdentity);

        ///<Summary>
        ///</Summary>
        IExperimentStore GetPipelineRunStore(ClaimsPrincipal user, WorkspaceIdentity workspaceIdentity);

        ///<Summary>
        ///</Summary>
        IGraphStore GetGraphStore(ClaimsPrincipal user, WorkspaceIdentity workspaceIdentity);

        ///<Summary>
        ///</Summary>
        ILogStore GetShareableLogStore(LogType logType, ClaimsPrincipal user, WorkspaceIdentity workspaceIdentity);

        ///<Summary>
        ///</Summary>
        IPipelineStore GetPipelineStore(ClaimsPrincipal user, WorkspaceIdentity workspaceIdentity);

        ///<Summary>
        ///</Summary>
        IPipelineScheduleStore GetPipelineScheduleStore(ClaimsPrincipal user, WorkspaceIdentity workspaceIdentity);

        ///<Summary>
        ///</Summary>
        IGenericScheduleStore GetGenericScheduleStore(ClaimsPrincipal user, WorkspaceIdentity workspaceIdentity);

        ///<Summary>
        ///</Summary>
        IPipelineEndpointStore GetPipelineEndpointStore(ClaimsPrincipal user, WorkspaceIdentity workspaceIdentity);

        ///<Summary>
        ///</Summary>
        IAmlModuleStore GetAmlModuleStore(ClaimsPrincipal user, WorkspaceIdentity workspaceIdentity);

        ///<Summary>
        ///</Summary>
        IAmlGraphDraftStore GetAmlGraphDraftStore(ClaimsPrincipal user, WorkspaceIdentity workspaceIdentity);

        ///<Summary>
        ///</Summary>
        IPipelineDraftStore GetPipelineDraftStore(ClaimsPrincipal user, WorkspaceIdentity workspaceIdentity);
    }
}
