﻿// <copyright file="IAccessTokenManager.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;

namespace Microsoft.Aether.AE3pService
{
    ///<summary>
    /// AccessTokenManager interface
    ///</summary>
    ///<remarks>User token management.</remarks>
    public interface IAccessTokenManager
    {
        ///<summary>
        /// TrySaveUserTokenToOboAync
        ///</summary>
        ///<remarks>Try to save teh user token to OboTokenService.</remarks>
        /// <param name="request">The HttpRequest</param>
        /// <param name="subscriptionId">Subscription Id</param>
        /// <param name="pipelineRunId">The pipeline run id</param>
        Task TrySaveUserTokenToOboAync(HttpRequest request, string subscriptionId, string pipelineRunId);
    }
}