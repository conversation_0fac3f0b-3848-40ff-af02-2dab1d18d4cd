﻿// <copyright file="PipelineVersionNotExistException.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Net;

namespace Microsoft.Aether.AE3pService
{
    public class PipelineVersionNotExistException : Exception
    {
        public HttpStatusCode StatusCode { get; private set; }

        public PipelineVersionNotExistException(string message) : base(message)
        {
            StatusCode = HttpStatusCode.BadRequest;
        }
    }
}
