﻿// <copyright file="DateTimeMacro.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Linq;
using System.Text.RegularExpressions;

namespace Microsoft.Aether.AE3pService.PipelineRunParameters
{
    internal class DateTimeMacro : BaseMacro
    {
        private const string Macro =
                    //REQUIRED: Set the base time
                    @"^(?<TimeType>NOW|UTCNOW)" +
                    //OPTIONAL: modify base time => add or subtract either specific part of time or many parts in the same time
                    @"(?:\.(?<ModType>ADD|SUB)\((?:(?:(?<ModValue1>\d+)(?<ModValue2>s|m|h|d|mo|y))|(?<ModValue3>\d*:?\d*:?\d*:?\d*:?\d*:?\d+))\))?" +
                    //OPTIONAL: Shift date to last (including current) or next day of the week
                    @"(?:\.(?<ShiftType>LAST|NEXT)_(?<ShiftDay>MONDAY|TUESDAY|WEDNESDAY|THURSDAY|FRIDAY|SATURDAY|SUNDAY))?" +
                    //OPTIONAL: select part of date to display or create your custom string with date time inside
                    @"(?:\.(?:(?<Display1>S|M|H|D|MO|Y|DOW)|(?:FORMAT\((?<Display2>.+)\))))?$";

        //the parsed regex contains following named groups
        // TimeType  - required; value NOW or UTCNOW; base time to use

        // ModType - optional; value ADD or SUB; if present we have to apply modification to the select time
        // ModValue1 - optional [required if ModValue2 present]; integer; defines value by witch we have to change time 
        // ModValue2 - optional [required if ModValue1 present]; s|m|h|d|mo|y; defines unit by witch we have to change time {seconds/minutes/hours/days/months/years}
        // ModValue3 - optional; string with 1 to 6 integers separated by ':'; defines values to changed time - units are taken right to left as seconds->minutes->hours->days->months->years
        // ModValue3 is exclusive to (ModValue1 and ModValue2) - at least 1 ModValueX has to be present if ModType is present

        // Display1 - optional; S|M|H|D|MO|Y|DOW; defines what part of the time we want to take as evaluation second/minute/hour/day/month/year
        // Display2 - optional; string; defines how we want to evaluate the date time - it has to be compatible with C# method DateTime.ToString(string s) where s will be equal to Display2
        // Display1 is exclusive to Display2

        public DateTimeMacro()
            : base(Macro)
        { }

        public override string Evaluate(string value, DateTime utcDateTime, PipelineRunSystemValues systemValues)
        {
            DateTime evaluatedDateTime = utcDateTime;
            Match match = MacroRegex.Match(value);
            if (HasGroupValue(match, "TimeType", "NOW"))
            {
                evaluatedDateTime = evaluatedDateTime.ToLocalTime();
            }
            if (match.Groups["ModType"].Success)
            {
                int negativeMultiplayer = 1;
                if (HasGroupValue(match, "ModType", "SUB"))
                {
                    negativeMultiplayer = -1;
                }
                if (match.Groups["ModValue1"].Success)
                {
                    int modValue = int.Parse(match.Groups["ModValue1"].Value);
                    if (HasGroupValue(match, "ModValue2", "s"))
                    {
                        evaluatedDateTime = evaluatedDateTime.AddSeconds(negativeMultiplayer * modValue);
                    }
                    else if (HasGroupValue(match, "ModValue2", "m"))
                    {
                        evaluatedDateTime = evaluatedDateTime.AddMinutes(negativeMultiplayer * modValue);
                    }
                    else if (HasGroupValue(match, "ModValue2", "h"))
                    {
                        evaluatedDateTime = evaluatedDateTime.AddHours(negativeMultiplayer * modValue);
                    }
                    else if (HasGroupValue(match, "ModValue2", "d"))
                    {
                        evaluatedDateTime = evaluatedDateTime.AddDays(negativeMultiplayer * modValue);
                    }
                    else if (HasGroupValue(match, "ModValue2", "mo"))
                    {
                        evaluatedDateTime = evaluatedDateTime.AddMonths(negativeMultiplayer * modValue);
                    }
                    else if (HasGroupValue(match, "ModValue2", "y"))
                    {
                        evaluatedDateTime = evaluatedDateTime.AddYears(negativeMultiplayer * modValue);
                    }
                }
                else
                {
                    int[] modValues = match.Groups["ModValue3"].Value.Split(new char[] { ':' }, StringSplitOptions.RemoveEmptyEntries).Reverse().Select(int.Parse).ToArray();
                    evaluatedDateTime = evaluatedDateTime.AddSeconds(negativeMultiplayer * modValues[0]);
                    if (modValues.Length > 1)
                    {
                        evaluatedDateTime = evaluatedDateTime.AddMinutes(negativeMultiplayer * modValues[1]);
                    }
                    if (modValues.Length > 2)
                    {
                        evaluatedDateTime = evaluatedDateTime.AddHours(negativeMultiplayer * modValues[2]);
                    }
                    if (modValues.Length > 3)
                    {
                        evaluatedDateTime = evaluatedDateTime.AddDays(negativeMultiplayer * modValues[3]);
                    }
                    if (modValues.Length > 4)
                    {
                        evaluatedDateTime = evaluatedDateTime.AddMonths(negativeMultiplayer * modValues[4]);
                    }
                    if (modValues.Length > 5)
                    {
                        evaluatedDateTime = evaluatedDateTime.AddYears(negativeMultiplayer * modValues[5]);
                    }
                }
            }

            if (match.Groups["ShiftType"].Success && match.Groups["ShiftDay"].Success)
            {
                DayOfWeek dow = evaluatedDateTime.DayOfWeek;
                int daysDiff = 0;
                if (HasGroupValue(match, "ShiftDay", "SUNDAY"))
                {
                    daysDiff = (int)DayOfWeek.Sunday - (int)dow;
                }
                else if (HasGroupValue(match, "ShiftDay", "MONDAY"))
                {
                    daysDiff = (int)DayOfWeek.Monday - (int)dow;
                }
                else if (HasGroupValue(match, "ShiftDay", "TUESDAY"))
                {
                    daysDiff = (int)DayOfWeek.Tuesday - (int)dow;
                }
                else if (HasGroupValue(match, "ShiftDay", "WEDNESDAY"))
                {
                    daysDiff = (int)DayOfWeek.Wednesday - (int)dow;
                }
                else if (HasGroupValue(match, "ShiftDay", "THURSDAY"))
                {
                    daysDiff = (int)DayOfWeek.Thursday - (int)dow;
                }
                else if (HasGroupValue(match, "ShiftDay", "FRIDAY"))
                {
                    daysDiff = (int)DayOfWeek.Friday - (int)dow;
                }
                else if (HasGroupValue(match, "ShiftDay", "SATURDAY"))
                {
                    daysDiff = (int)DayOfWeek.Saturday - (int)dow;
                }

                if (HasGroupValue(match, "ShiftType", "NEXT"))
                {
                    daysDiff = daysDiff == 0 ? 7 : (daysDiff + 7) % 7;
                }
                else
                {
                    daysDiff = (daysDiff - 7) % 7;
                }
                evaluatedDateTime = evaluatedDateTime.AddDays(daysDiff);
            }
            if (match.Groups["Display1"].Success)
            {
                if (HasGroupValue(match, "Display1", "S"))
                {
                    return evaluatedDateTime.Second.ToString();
                }
                if (HasGroupValue(match, "Display1", "M"))
                {
                    return evaluatedDateTime.Minute.ToString();
                }
                if (HasGroupValue(match, "Display1", "H"))
                {
                    return evaluatedDateTime.Hour.ToString();
                }
                if (HasGroupValue(match, "Display1", "D"))
                {
                    return evaluatedDateTime.Day.ToString();
                }
                if (HasGroupValue(match, "Display1", "MO"))
                {
                    return evaluatedDateTime.Month.ToString();
                }
                if (HasGroupValue(match, "Display1", "Y"))
                {
                    return evaluatedDateTime.Year.ToString();
                }
                if (HasGroupValue(match, "Display1", "DOW"))
                {
                    return evaluatedDateTime.DayOfWeek.ToString();
                }
            }
            else if (match.Groups["Display2"].Success)
            {
                return evaluatedDateTime.ToString(match.Groups["Display2"].Value);
            }
            return evaluatedDateTime.ToString();
        }
    }
}
