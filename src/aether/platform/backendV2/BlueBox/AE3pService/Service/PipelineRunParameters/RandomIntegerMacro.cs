﻿// <copyright file="RandomIntegerMacro.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Text.RegularExpressions;

namespace Microsoft.Aether.AE3pService.PipelineRunParameters
{
    internal class RandomIntegerMacro : BaseMacro
    {
        private const string Macro = @"^RAND\((?<N1>\d+|-[1-9]\d*),(?<N2>\d+|-[1-9]\d*)\)$";

        public RandomIntegerMacro()
            : base(Macro)
        { }

        public override string Evaluate(string value, DateTime utcDateTime, PipelineRunSystemValues systemValues)
        {
            Match match = MacroRegex.Match(value);
            int n1 = int.Parse(match.Groups["N1"].Value);
            int n2 = int.Parse(match.Groups["N2"].Value);
            if (n1 == n2)
            {
                return n1.ToString();
            }
            Random rand = new Random((int)DateTime.Now.Ticks);
            if (n1 > n2)
            {
                return rand.Next(n2, n1 + 1).ToString();
            }
            return rand.Next(n1 + 1, n2).ToString();
        }
    }
}
