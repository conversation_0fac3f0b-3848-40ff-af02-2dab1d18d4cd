﻿// <copyright file="ITokenVerifier.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;

namespace Microsoft.Aether.AE3pService.PipelineRunParameters
{
    internal class RandomGuidMacro : BaseMacro
    {
        private const string Macro = "^RGUID$";

        public RandomGuidMacro()
            : base(Macro)
        { }

        public override string Evaluate(string value, DateTime utcDateTime, PipelineRunSystemValues systemValues)
        {
            return Guid.NewGuid().ToString();
        }
    }
}
