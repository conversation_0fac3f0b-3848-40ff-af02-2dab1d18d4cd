﻿// <copyright file="PipelineRunSystemValues.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

namespace Microsoft.Aether.AE3pService
{
    internal class PipelineRunSystemValues
    {
        internal PipelineRunSystemValues(string owner, string teamName, string teamId)
        {
            int pos = owner.IndexOf('\\');
            if (pos == -1 || pos == owner.Length - 1)
            {
                User = owner;
                Domain = string.Empty;
            }
            else
            {
                User = owner.Substring(pos + 1);
                Domain = owner.Substring(0, pos);
            }
            Team = teamName;
            TeamId = teamId;
        }

        internal PipelineRunSystemValues(string user, string domain, string teamName, string teamId)
        {
            User = user;
            Domain = domain;
            Team = teamName;
            TeamId = teamId;
        }

        internal string User { get; }

        internal string Domain { get; }

        internal string Team { get; }

        internal string TeamId { get; }
    }
}
