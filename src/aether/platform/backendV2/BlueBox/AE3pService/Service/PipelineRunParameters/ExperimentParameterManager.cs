﻿// <copyright file="ExperimentParameterManager.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Microsoft.Aether.DataContracts;
using Microsoft.Aether.DataContracts.Metastore;
using Microsoft.RelInfra.Instrumentation.Logging;

namespace Microsoft.Aether.AE3pService.PipelineRunParameters
{
    internal class ExperimentParameterManager
    {
        private const char MacroTag = '#';
        private readonly List<BaseMacro> _knownMacros;
        private readonly PipelineRunSystemValues _systemValues;

        internal ExperimentParameterManager(PipelineRunSystemValues systemValues)
        {
            _knownMacros = new List<BaseMacro>
            {
                new RandomGuidMacro(),
                new DoubleHashMacro(),
                new RandomIntegerMacro(),
                new DateTimeMacro(),
                new UserMacro(),
                new DomainMacro()
            };

            _systemValues = systemValues;
        }

        internal void EvaluateParameters(ExperimentEntity experiment)
        {
            Dictionary<string, string> evaluatedParameters = EvaluateParameterSources(experiment.ParameterSources);
            foreach (KeyValuePair<string, string> evaluatedParameter in evaluatedParameters)
            {
                experiment.ParameterAssignments[evaluatedParameter.Key] = evaluatedParameter.Value;
            }
        }

        private Dictionary<string, string> EvaluateParameterSources(
            IEnumerable<ExperimentParameterSource> parameterSources,
            StringBuilder evaluationMessage = null)
        {
            Dictionary<string, string> evaluatedParameters = new Dictionary<string, string>();
            if (parameterSources == null || !parameterSources.Any())
            {
                return evaluatedParameters;
            }
            DateTime utcDateTime = DateTime.UtcNow;
            StringBuilder message = evaluationMessage ?? new StringBuilder();
            foreach (ExperimentParameterSource parameterSource in parameterSources)
            {
                evaluatedParameters[parameterSource.Name] = EvaluateParameterSource(parameterSource, utcDateTime, message);
                message.AppendFormat($"{parameterSource.Name} [{BuildDisplayString(parameterSource)}] = {evaluatedParameters[parameterSource.Name]}\n\r");
            }

            CommonLogger.LogInfo("Evalauating Parameter Source: {parameter_source_string}", message.ToString());

            return evaluatedParameters;
        }

        private static string BuildDisplayString(ExperimentParameterSource parameterSource)
        {
            switch (parameterSource.Type)
            {
                case ExperimentParameterSourceType.Literal:
                    return parameterSource.Value;
                case ExperimentParameterSourceType.Macro:
                    return string.Format("{0}{0}{1}{0}{0}", MacroTag, parameterSource.Value);
                case ExperimentParameterSourceType.Concatenate:
                    return string.Join(
                        separator: string.Empty,
                        values: parameterSource.SourceList.Select(BuildDisplayString));
                default:
                    throw new ArgumentException("unknown experiment source type", nameof(parameterSource));
            }
        }

        private string EvaluateParameterSource(
            ExperimentParameterSource parameterSource,
            DateTime utcDateTime,
            StringBuilder messageBuilder)
        {
            try
            {
                switch (parameterSource.Type)
                {
                    case ExperimentParameterSourceType.Literal:
                        return parameterSource.Value;
                    case ExperimentParameterSourceType.Macro:
                        return EvaluateMacroParameterSource(parameterSource.Value, utcDateTime);
                    case ExperimentParameterSourceType.Concatenate:
                        return string.Join(string.Empty, parameterSource.SourceList.Select(ps => EvaluateParameterSource(ps, utcDateTime, messageBuilder)));
                    default:
                        throw new ArgumentException("unknown experiment source type", nameof(parameterSource));
                }
            }
            catch (Exception ex)
            {
                messageBuilder.AppendFormat($"{parameterSource.Name}: Error during processing macro {parameterSource.Value}: {ex.Message}\n\r");
            }

            return string.Format("{0}{0}{1}{0}{0}", MacroTag, parameterSource.Value);
        }

        private string EvaluateMacroParameterSource(string macro, DateTime utcDateTime)
        {
            foreach (BaseMacro knownMacro in _knownMacros)
            {
                if (knownMacro.IsMacroRecognized(macro))
                {
                    return knownMacro.Evaluate(macro, utcDateTime, _systemValues);
                }
            }
            throw new ArgumentException("Unknown Macro.");
        }
    }
}
