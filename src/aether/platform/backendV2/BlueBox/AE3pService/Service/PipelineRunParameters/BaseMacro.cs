﻿// <copyright file="BaseMacro.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Text.RegularExpressions;

namespace Microsoft.Aether.AE3pService.PipelineRunParameters
{
    internal abstract class BaseMacro
    {
        protected readonly Regex MacroRegex;

        protected BaseMacro(string macroRegex)
        {
            MacroRegex = new Regex(
                pattern: macroRegex,
                options: RegexOptions.IgnorePatternWhitespace | RegexOptions.IgnoreCase | RegexOptions.Compiled);
        }

        public bool IsMacroRecognized(string value)
        {
            return MacroRegex.IsMatch(value);
        }

        public virtual string Evaluate(string value, DateTime utcDateTime, PipelineRunSystemValues systemValues)
        {
            return string.Empty;
        }

        protected bool HasGroupValue(Match match, string groupName, string value)
        {
            return string.Compare(match.Groups[groupName].Value, value, StringComparison.CurrentCultureIgnoreCase) == 0;
        }
    }
}
