﻿// <copyright file="UserMacro.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;

namespace Microsoft.Aether.AE3pService.PipelineRunParameters
{
    internal class UserMacro : BaseMacro
    {
        private const string Macro = "^USER$";

        public UserMacro()
            : base(Macro)
        { }

        public override string Evaluate(string value, DateTime utcDateTime, PipelineRunSystemValues systemValues)
        {
            return systemValues.User;
        }
    }
}
