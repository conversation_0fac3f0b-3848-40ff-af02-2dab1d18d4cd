﻿// <copyright file="DoubleHashMacro.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;

namespace Microsoft.Aether.AE3pService.PipelineRunParameters
{
    internal class DoubleHashMacro : BaseMacro
    {
        private const string Macro = "^DHASH$";

        public DoubleHashMacro()
            : base(Macro)
        { }

        public override string Evaluate(string value, DateTime utcDateTime, PipelineRunSystemValues systemValues)
        {
            return "##";
        }
    }
}
