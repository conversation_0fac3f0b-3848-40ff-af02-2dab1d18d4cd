﻿// <copyright file="DomainMacro.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;

namespace Microsoft.Aether.AE3pService.PipelineRunParameters
{
    internal class DomainMacro : BaseMacro
    {
        private const string Macro = "^DOMAIN$";

        public DomainMacro()
            : base(Macro)
        { }

        public override string Evaluate(string value, DateTime utcDateTime, PipelineRunSystemValues systemValues)
        {
            return systemValues.Domain;
        }
    }
}
