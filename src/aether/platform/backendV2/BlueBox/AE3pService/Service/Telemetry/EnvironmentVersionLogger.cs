﻿// <copyright file="EnvironmentVersionLogger.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Microsoft.RelInfra.Instrumentation.Logging;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;

namespace Microsoft.Aether.AE3pService.Telemetry
{
    internal class EnvironmentVersionLogger
    {
        private readonly EnvironmentVersionLoggerConfig _config;
        private readonly string _envName;

        public EnvironmentVersionLogger(EnvironmentVersionLoggerConfig config, string envName)
        {
            _config = config;
            _envName = envName;
        }

        public void StartLogging()
        {
            new Task(async () => await RunLoggingAsync()).Start();
            return;
        }

        private async Task RunLoggingAsync()
        {
            // ASPNETCORE_VERSION = 2.1.5
            WriteToLogs("ASPNETCORE_VERSION", Environment.GetEnvironmentVariable("ASPNETCORE_VERSION") ?? "Unknown");
            // HOSTNAME=ae3pservice-bb4d67d69-nx65q
            WriteToLogs("HOSTNAME", Environment.GetEnvironmentVariable("HOSTNAME") ?? "Unknown");

            while (true)
            {
                Dictionary<string, string> filesToPrint = new Dictionary<string, string>()
                {
                    { "DotNetInfo",  _config.DotNetInfoLocation},
                    { "DotNetRuntimeListLocation", _config.DotNetRuntimeListLocation },
                    { "PodInfoAnnotations", _config.PodInfoAnnotations },
                    { "PodInfoLabels", _config.PodInfoLabels }
                };

                // dump files to logs so then they can be monitored
                foreach (var fileToPrint in filesToPrint)
                {
                    string fileCaption = fileToPrint.Key;
                    string filePath = fileToPrint.Value;

                    if (!File.Exists(filePath))
                    {
                        WriteToLogs(fileCaption, $"File {filePath} not found");
                        continue;
                    }
                    foreach (var line in File.ReadLines(filePath))
                    {
                        WriteToLogs(fileCaption, line);
                    }
                }

                await Task.Delay(_config.LoggingIntervalTS);
            }
        }

        private void WriteToLogs(string entity, string message)
        {
            CommonLogger.LogEntityInfo($"{_envName}::{entity}", message);
        }
    }
}
