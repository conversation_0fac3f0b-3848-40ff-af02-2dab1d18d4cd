﻿// <copyright file="EnvironmentVersionLoggerConfig.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using Microsoft.RelInfra.Common.Configuration;

namespace Microsoft.Aether.AE3pService.Telemetry
{
    internal class EnvironmentVersionLoggerConfig : VerifiableConfig
    {
        private readonly IRelInfraConfiguration _config;

        public EnvironmentVersionLoggerConfig(IRelInfraConfiguration config)
            : base(config, verifyConfigOnChanges: true, configName: nameof(EnvironmentVersionLoggerConfig))
        {
            _config = config;
            DotNetInfoLocation = _config?.GetString("Telemetry.DotNetInfoLocation") ?? string.Empty;
            DotNetRuntimeListLocation = _config?.GetString("Telemetry.DotNetRuntimeListLocation") ?? string.Empty;
            PodInfoAnnotations = _config?.GetString("Telemetry.PodInfoAnnotations") ?? string.Empty;
            PodInfoLabels = _config?.GetString("Telemetry.PodInfoLabels") ?? string.Empty;
            LoggingIntervalTS = _config?.GetTimeSpan("Telemetry.LoggingIntervalTS") ?? new TimeSpan(0, 30, 0);
        }

        public EnvironmentVersionLoggerConfig()
            : this(null)
        {
        }

        public string EnvName { get; set; }

        public string DotNetInfoLocation { get; set; }

        public string DotNetRuntimeListLocation { get; set; }

        /// <summary>
        ///    File with pod annotations, for example:
        ///  >cat /etc/podinfo/annotations
        ///  kubernetes.io/config.seen="2018-09-29T07:06:36.891698305Z"
        ///  kubernetes.io/config.source="api"
        ///  kubernetes.io/limit-ranger="LimitRanger plugin set: cpu, memory request for container ae3pservice"root @ae3pservice-674df57c5c-zqgs2:/etc/podinfo# cat annotation
        /// </summary>
        public string PodInfoAnnotations { get; set; }

        /// <summary>
        ///    File with labels assigned to pod during deployment, for example:
        ///     >cat /etc/podinfo/labels
        ///     app="ae3pservice"
        ///     buildnumber="20180929.12"
        ///     commit="2dd2a2b2261c37f19a909effced7a8293177b74d"
        /// </summary>
        public string PodInfoLabels { get; set; }

        /// <summary>
        ///    How often telemetry should be written to stdout. It's done periodically instead of just on startup to see current state more easily on dashboard
        /// </summary>
        public TimeSpan LoggingIntervalTS { get; set; }
    }
}
