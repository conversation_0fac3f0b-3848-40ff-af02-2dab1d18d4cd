﻿// <copyright file="EnvironmentVersionLoggerService.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using Microsoft.RelInfra.Instrumentation.Logging;

namespace Microsoft.Aether.AE3pService.Telemetry
{
    internal class EnvironmentVersionLoggerService : IHostedService, IDisposable
    {
        private readonly EnvironmentVersionLoggerConfig _config;
        private Timer _timer = null!;

        public EnvironmentVersionLoggerService(IOptionsMonitor<EnvironmentVersionLoggerConfig> config)
        {
            _config = config.CurrentValue;
        }

        public Task StartAsync(CancellationToken cancellationToken)
        {
            _timer = new Timer(RunLogging, null, TimeSpan.Zero, _config.LoggingIntervalTS);

            return Task.CompletedTask;
        }

        public void RunLogging(object state)
        {
            // ASPNETCORE_VERSION = 2.1.5
            WriteToLogs("ASPNETCORE_VERSION", Environment.GetEnvironmentVariable("ASPNETCORE_VERSION") ?? "Unknown");
            // HOSTNAME=ae3pservice-bb4d67d69-nx65q
            WriteToLogs("HOSTNAME", Environment.GetEnvironmentVariable("HOSTNAME") ?? "Unknown");
            Dictionary<string, string> filesToPrint = new Dictionary<string, string>()
                {
                    { "DotNetInfo",  _config.DotNetInfoLocation},
                    { "DotNetRuntimeListLocation", _config.DotNetRuntimeListLocation },
                    { "PodInfoAnnotations", _config.PodInfoAnnotations },
                    { "PodInfoLabels", _config.PodInfoLabels }
                };

            // dump files to logs so then they can be monitored
            foreach (var fileToPrint in filesToPrint)
            {
                string fileCaption = fileToPrint.Key;
                string filePath = fileToPrint.Value;

                if (!File.Exists(filePath))
                {
                    WriteToLogs(fileCaption, $"File {filePath} not found");
                    continue;
                }
                foreach (var line in File.ReadLines(filePath))
                {
                    WriteToLogs(fileCaption, line);
                }
            }
        }

        public Task StopAsync(CancellationToken cancellationToken)
        {
            _timer?.Change(Timeout.Infinite, 0);
            return Task.CompletedTask;
        }

        public void Dispose()
        {
            _timer?.Dispose();
        }

        private void WriteToLogs(string entity, string message)
        {
            CommonLogger.LogEntityInfo($"{_config.EnvName}::{entity}", message);
        }
    }
}
