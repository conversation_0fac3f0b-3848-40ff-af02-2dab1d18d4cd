﻿// <copyright file="ITokenVerifier.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Microsoft.Aether.DataContracts;
using System.Security.Claims;
using Microsoft.Aether.DataContracts.Entity;
using Microsoft.Aether.DataContracts.Metastore;

namespace Microsoft.Aether.AE3pService
{
    internal class StoreCollection : IStoreCollection
    {
        private readonly IStoreFactory _storeFactory;
        private readonly IContextProvider _contextProvider;

        public StoreCollection(IStoreFactory storeFactory, IContextProvider contextProvider)
        {
            _storeFactory = storeFactory;
            _contextProvider = contextProvider;
        }

        public IDatasetStore GetDatasetStore(ClaimsPrincipal user, WorkspaceIdentity workspaceIdentity)
        {
            return _storeFactory.CreateDatasetStore(_contextProvider.GetStoreOperationContext(user), workspaceIdentity);
        }

        public IDataTypeStore GetDataTypeStore(ClaimsPrincipal user, WorkspaceIdentity workspaceIdentity)
        {
            return _storeFactory.CreateDataTypeStore(_contextProvider.GetStoreOperationContext(user), workspaceIdentity);
        }

        public IExperimentStore GetPipelineRunStore(ClaimsPrincipal user, WorkspaceIdentity workspaceIdentity)
        {
            return _storeFactory.CreateExperimentStore(_contextProvider.GetStoreOperationContext(user), workspaceIdentity);
        }

        public IModuleStore GetModuleStore(ClaimsPrincipal user, WorkspaceIdentity workspaceIdentity)
        {
            return _storeFactory.CreateModuleStore(_contextProvider.GetStoreOperationContext(user), workspaceIdentity);
        }

        public IGraphStore GetGraphStore(ClaimsPrincipal user, WorkspaceIdentity workspaceIdentity)
        {
            return _storeFactory.CreateGraphStore(_contextProvider.GetStoreOperationContext(user), workspaceIdentity);
        }

        public ILogStore GetShareableLogStore(LogType logType, ClaimsPrincipal user, WorkspaceIdentity workspaceIdentity)
        {
            return _storeFactory.CreateLogStore(_contextProvider.GetStoreOperationContext(user), logType, shareable: true, workspaceIdentity: workspaceIdentity);
        }

        public IPipelineStore GetPipelineStore(ClaimsPrincipal user, WorkspaceIdentity workspaceIdentity)
        {
            return _storeFactory.CreatePipelineStore(_contextProvider.GetStoreOperationContext(user), workspaceIdentity);
        }

        public IPipelineScheduleStore GetPipelineScheduleStore(ClaimsPrincipal user, WorkspaceIdentity workspaceIdentity)
        {
            return _storeFactory.CreatePipelineScheduleStore(_contextProvider.GetStoreOperationContext(user), workspaceIdentity);
        }

        public IGenericScheduleStore GetGenericScheduleStore(ClaimsPrincipal user, WorkspaceIdentity workspaceIdentity)
        {
            return _storeFactory.CreateGenericScheduleStore(_contextProvider.GetStoreOperationContext(user), workspaceIdentity);
        }

        public IAmlModuleStore GetAmlModuleStore(ClaimsPrincipal user, WorkspaceIdentity workspaceIdentity)
        {
            return _storeFactory.CreateAmlModuleStore(_contextProvider.GetStoreOperationContext(user), workspaceIdentity);
        }

        public IPipelineEndpointStore GetPipelineEndpointStore(ClaimsPrincipal user, WorkspaceIdentity workspaceIdentity)
        {
            return _storeFactory.CreatePipelineEndpointStore(_contextProvider.GetStoreOperationContext(user), workspaceIdentity);
        }

        public IAmlGraphDraftStore GetAmlGraphDraftStore(ClaimsPrincipal user, WorkspaceIdentity workspaceIdentity)
        {
            return _storeFactory.CreateAmlGraphDraftStore(_contextProvider.GetStoreOperationContext(user), workspaceIdentity);
        }

        public IPipelineDraftStore GetPipelineDraftStore(ClaimsPrincipal user, WorkspaceIdentity workspaceIdentity)
        {
            return _storeFactory.CreatePipelineDraftStore(_contextProvider.GetStoreOperationContext(user), workspaceIdentity);
        }
    }
}
