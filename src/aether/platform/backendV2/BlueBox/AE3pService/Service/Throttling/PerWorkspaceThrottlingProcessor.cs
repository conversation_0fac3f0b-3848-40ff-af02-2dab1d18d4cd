﻿// <copyright file="PerWorkspaceThrottlingProcessor.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.MachineLearning.Common.Core.Arm.Contracts;
using Microsoft.MachineLearning.Common.WebApi.Throttling.ThrottlingV2;

namespace Microsoft.Aether.AE3pService.Throttling
{
    public class PerWorkspaceThrottlingProcessor : IThrottlingProcessor
    {
        private readonly string _throttlingKey;
        private readonly bool _throttlingEnabled;

        public PerWorkspaceThrottlingProcessor(string throttlingKey, bool throttlingEnabled)
        {
            _throttlingKey = throttlingKey;
            _throttlingEnabled = throttlingEnabled;
        }

        public Task<IReadOnlyCollection<ThrottlingContextForWorkspace>> GetThrottlingContextPerWorkspace(ActionExecutingContext context)
        {
            var subscriptionId = Guid.Parse(context.ActionArguments["subscriptionId"].ToString());
            var resourceGroupName = context.ActionArguments["resourceGroupName"].ToString();
            var workspaceName = context.ActionArguments["workspaceName"].ToString();
            var perWorkspaceContainer = $"{_throttlingKey}:{subscriptionId}:{resourceGroupName}:{workspaceName}";

            return Task.FromResult<IReadOnlyCollection<ThrottlingContextForWorkspace>>(new List<ThrottlingContextForWorkspace>()
            {
                new ThrottlingContextForWorkspace
                {
                    WorkspaceReference = new WorkspaceReference(new AmlWorkspaceArmScope(subscriptionId, resourceGroupName, workspaceName)),
                    ThrottlingContextForApplicableLimitKeys = new List<ThrottlingContextForLimitKey>()
                    {
                        new ThrottlingContextForLimitKey
                        {
                            LimitKey = _throttlingKey,
                            ThrottlingContextForEntityContainers = new List<ThrottlingContextForEntityContainer>()
                            {
                                new ThrottlingContextForEntityContainer { EntityCount = 1, EntityContainer = perWorkspaceContainer }
                            }
                        }
                    }
                }
            });
        }

        public IReadOnlyCollection<string> GetLimitKeys()
        {
            return new List<string>() { _throttlingKey };
        }

        public bool IsThrottlingApplicable(ActionExecutingContext context)
        {
            return _throttlingEnabled && context.ActionArguments.ContainsKey("subscriptionId") && context.ActionArguments.ContainsKey("workspaceName") && context.ActionArguments.ContainsKey("resourceGroupName");
        }
    }
}