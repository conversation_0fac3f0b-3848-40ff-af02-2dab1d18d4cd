﻿// <copyright file="ThrottlingAttributes.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using Microsoft.MachineLearning.Common.WebApi.Throttling.Configurations;
using Microsoft.MachineLearning.Common.WebApi.Throttling.ThrottlingV2;

namespace Microsoft.Aether.AE3pService.Throttling
{
    ///<Summary/>WorkspaceThrottlingAttributeImpl wrapper is required for DI config to get enable flag
    public class WorkspaceThrottlingAttributeImpl : TypeFilterAttribute
    {
        ///<Summary/>WorkspaceThrottlingAttributeImpl is wrapper to inject config for enabling throttling per region.
        public WorkspaceThrottlingAttributeImpl(string throttlingKey, IOptionsMonitor<ThrottlingConfiguration> config)
            : base(typeof(ThrottlingV2Attribute))
        {
            Arguments = new object[] { new PerWorkspaceThrottlingProcessor(throttlingKey, config.CurrentValue.EnableThrottling) };
        }
    }
    ///<Summary/>WorkspaceThrottlingAttribute
    public class WorkspaceThrottlingAttribute : TypeFilterAttribute
    {
        ///<Summary/>
        public WorkspaceThrottlingAttribute(string throttlingKey)
           : base(typeof(WorkspaceThrottlingAttributeImpl))
        {
            Arguments = new object[] { throttlingKey };
        }
    }
}