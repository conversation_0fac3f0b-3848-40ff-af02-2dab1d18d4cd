﻿// <copyright file="ThrottlingLimitKeyConstants.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

namespace Microsoft.Aether.AE3pService.Throttling
{
    ///<Summary/> ThrottlingLimitKeyConstants
    public class ThrottlingLimitKeyConstants
    {
        ///<Summary/> RunSubmitPerWorkspacePerWindow
        public const string SubmitRunPerWorkspacePerWindow = "SubmitRun/Workspace/Sec";
        ///<Summary/> RunSubmitPerWorkspacePerWindow
        public const string CreateRunPerWorkspacePerWindow = "CreateRun/Workspace/Sec";
        ///<Summary/> RunSubmitPerWorkspacePerWindow
        public const string GetRunPerWorkspacePerWindow = "GetRun/Workspace/Sec";
        ///<Summary/> RunSubmitPerWorkspacePerWindow
        public const string CancelRunPerWorkspacePerWindow = "CancelRun/Workspace/Sec";
        ///<Summary/> UpdateRunPerWorkspacePerWindow
        public const string UpdateRunPerWorkspacePerWindow = "UpdateRun/Workspace/Sec";

        ///<Summary/> Pipeline Scenario
        ///<Summary/> CreatePipelinePerWorkspacePerWindow
        public const string CreatePipelinePerWorkspacePerWindow = "CreatePipeline/Workspace/Sec";
        ///<Summary/> GetPipelinePerWorkspacePerWindow
        public const string GetPipelinePerWorkspacePerWindow = "GetPipeline/Workspace/Sec";

        ///<Summary/> PipelineDraft Scenario
        ///<Summary/> CreatePipelineDraftPerWorkspacePerWindow
        public const string CreatePipelineDraftPerWorkspacePerWindow = "CreatePipelineDraft/Workspace/Sec";
        ///<Summary/> GetPipelineDraftPerWorkspacePerWindow
        public const string GetPipelineDraftPerWorkspacePerWindow = "GetPipelineDraft/Workspace/Sec";
        ///<Summary/> SavePipelineDraftPerWorkspacePerWindow
        public const string SavePipelineDraftPerWorkspacePerWindow = "SavePipelineDraft/Workspace/Sec";
        ///<Summary/> DeletePipelineDraftPerWorkspacePerWindow
        public const string DeletePipelineDraftPerWorkspacePerWindow = "DeletePipelineDraft/Workspace/Sec";

        ///<Summary/> PipelineEndpoint Scenario
        ///<Summary/> CreatePipelineEndpointPerWorkspacePerWindow
        public const string CreatePipelineEndpointPerWorkspacePerWindow = "CreatePipelineEndpoint/Workspace/Sec";
        ///<Summary/> GetPipelineEndpointPerWorkspacePerWindow
        public const string GetPipelineEndpointPerWorkspacePerWindow = "GetPipelineEndpoint/Workspace/Sec";
        ///<Summary/> UpdatePipelineEndpointPerWorkspacePerWindow
        public const string UpdatePipelineEndpointPerWorkspacePerWindow = "UpdatePipelineEndpoint/Workspace/Sec";

        ///<Summary/> Module Scenario
        ///<Summary/> CreatePipelineEndpointPerWorkspacePerWindow
        public const string CreateModulePerWorkspacePerWindow = "CreateModule/Workspace/Sec";
        ///<Summary/> GetPipelineEndpointPerWorkspacePerWindow
        public const string GetModulePerWorkspacePerWindow = "GetModule/Workspace/Sec";
        ///<Summary/> UpdatePipelineEndpointPerWorkspacePerWindow
        public const string UpdateModulePerWorkspacePerWindow = "UpdateModule/Workspace/Sec";

        ///<Summary/> ModuleVersion Scenario
        ///<Summary/> CreateModuleVersionPerWorkspacePerWindow
        public const string CreateModuleVersionPerWorkspacePerWindow = "CreateModuleVersion/Workspace/Sec";
        ///<Summary/> GetModuleVersionPerWorkspacePerWindow
        public const string GetModuleVersionPerWorkspacePerWindow = "GetModuleVersion/Workspace/Sec";
        ///<Summary/> UpdateModuleVersionPerWorkspacePerWindow
        public const string UpdateModuleVersionPerWorkspacePerWindow = "UpdateModuleVersion/Workspace/Sec";

        ///<Summary/> Schedule Scenario
        ///<Summary/> CreateSchedulePerWorkspacePerWindow
        public const string CreateSchedulePerWorkspacePerWindow = "CreateSchedule/Workspace/Sec";
        ///<Summary/> GetSchedulePerWorkspacePerWindow
        public const string GetSchedulePerWorkspacePerWindow = "GetSchedule/Workspace/Sec";
        ///<Summary/> UpdateSchedulePerWorkspacePerWindow
        public const string UpdateSchedulePerWorkspacePerWindow = "UpdateSchedule/Workspace/Sec";

        ///<Summary/> Subgraph Scenario
        ///<Summary/> GetSubGraphPerWorkspacePerWindow
        public const string GetSubGraphPerWorkspacePerWindow = "GetSubGraph/Workspace/Sec";
        ///<Summary/> CreateSubGraphPerWorkspacePerWindow
        public const string CreateSubGraphPerWorkspacePerWindow = "CreateSubGraph/Workspace/Sec";
        ///<Summary/> GetPipelineViewPerWorkspacePerWindow
        public const string GetPipelineViewPerWorkspacePerWindow = "GetPipelineView/Workspace/Sec";

        ///<Summary/> Graph Scenario
        ///<Summary/> CreateSchedulePerWorkspacePerWindow
        public const string CreateGraphPerWorkspacePerWindow = "CreateGraph/Workspace/Sec";
        ///<Summary/> GetSchedulePerWorkspacePerWindow
        public const string GetGraphPerWorkspacePerWindow = "GetGraph/Workspace/Sec";
        ///<Summary/> UpdateSchedulePerWorkspacePerWindow
        public const string UpdateGraphPerWorkspacePerWindow = "UpdateGraph/Workspace/Sec";

        ///<Summary/> GraphDraft Scenario
        ///<Summary/> CreateSchedulePerWorkspacePerWindow
        public const string CreateGraphDraftPerWorkspacePerWindow = "CreateGraphDraft/Workspace/Sec";
        ///<Summary/> GetSchedulePerWorkspacePerWindow
        public const string GetGraphDraftPerWorkspacePerWindow = "GetGraphDraft/Workspace/Sec";
        ///<Summary/> UpdateSchedulePerWorkspacePerWindow
        public const string UpdateGraphDraftPerWorkspacePerWindow = "UpdateGraphDraft/Workspace/Sec";
        ///<Summary/> DeleteGraphDraftPerWorkspacePerWindow
        public const string DeleteGraphDraftPerWorkspacePerWindow = "DeleteGraphDraft/Workspace/Sec";

        ///<Summary/> ThrottlingWindowValueSuffix
        public const string ThrottlingWindowValueSuffix = ".ValueInSeconds";
    }
}
