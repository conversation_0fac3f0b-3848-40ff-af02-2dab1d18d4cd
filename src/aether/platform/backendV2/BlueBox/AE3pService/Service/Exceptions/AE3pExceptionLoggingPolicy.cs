﻿// <copyright file="AE3pExceptionLoggingPolicy.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using Microsoft.Azure.Cosmos;
using Microsoft.Azure.Documents;
using Microsoft.RelInfra.Storage.Exceptions;

namespace Microsoft.Aether.AE3pService.Exceptions
{
    /// <inheritdoc/>
    public class AE3pExceptionLoggingPolicy : RelStorageExceptionLoggingPolicy
    {
        /*
         * =============================================================================================
         * NOTE: if you can add exceptions to the base class (without adding a lot of extra dependencies),
         * then please do it there so the change can be more widely used. Similarly, if you can't add the
         * excpetion to this class, then you can check this classes subclass (if present).
         * =============================================================================================
         */

        /// <inheritdoc/>
        protected override ReadOnlyDictionary<Type, Func<Exception, Dictionary<string, string>>> LoggablePropertiesDict { get; }

        /// <summary>
        /// Default constructor
        /// </summary>
        public AE3pExceptionLoggingPolicy()
        {
            Dictionary<Type, Func<Exception, Dictionary<string, string>>> loggableProperties = new Dictionary<Type, Func<Exception, Dictionary<string, string>>>()
            {
                // NOTE: if an exception type isn't in this map, but it's super-type is, then we will use the super-types mapping
                // NOTE: if values of the dict returned by these functions are null then they will be automatically ignored from the final string (see PropertiesToString)
                {
                    typeof(DocumentClientException), ex => {
                        var docEx = ex as DocumentClientException;
                        return new Dictionary<string, string>() { { MessageStr, ex.Message }, { "ActivityId", docEx.ActivityId }, { "StatusCode", docEx.StatusCode.ToString() } };
                    }
                },
                {
                    typeof(CosmosException), ex => {
                        var docEx = ex as CosmosException;
                        return new Dictionary<string, string>() { { MessageStr, ex.Message }, { "ActivityId", docEx.ActivityId }, { "StatusCode", docEx.StatusCode.ToString() } };
                    }
                },
            };
            LoggablePropertiesDict = BuildLoggableProperties(base.LoggablePropertiesDict, loggableProperties);
        }
    }
}
