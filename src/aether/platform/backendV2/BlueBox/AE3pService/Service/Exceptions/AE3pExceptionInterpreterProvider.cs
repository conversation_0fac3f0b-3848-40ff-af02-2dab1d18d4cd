﻿// <copyright file="AE3pExceptionInterpreterProvider.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Microsoft.Extensions.DependencyInjection;
using Microsoft.MachineLearning.Common.WebApi.ErrorHandling;
using Microsoft.RelInfra.Common.Exceptions;
using System;
using System.Collections.Generic;

namespace Microsoft.Aether.AE3pService.Exceptions
{
    public static class AE3pExceptionInterpreterProvider
    {
        public static ExceptionInterpreterProvider GetInterpreterProvider(IServiceProvider sp)
        {
            var list = new List<InterpreterCandidate>();
            list.Add(AE3pAuthorizationUnavailableExceptionInterpreter.InterpreterForAuthorizeError);
            list.Add(AE3pServiceInvocationExceptionInterpreter.InterpreterForUserError);
            list.Add(AE3pServiceInvocationExceptionInterpreter.InterpreterForServiceUnavailable);
            list.AddRange(ErrorHandlingInstallationExtensions.DefaultInterpreterCandidates);
            var interpreter = new FormattableExceptionInterpreter<Exception>(sp.GetRequiredService<IExceptionHandler>());
            var candidate = new InterpreterCandidate(exceptionType: typeof(Exception), interpreter: interpreter);
            list.Add(candidate);

            return new ExceptionInterpreterProvider(list);
        }
    }
}
