﻿// <copyright file="ExceptionHandler.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Microsoft.Aether.BlueBox.RunHistoryClient;
using Microsoft.AspNetCore.Server.Kestrel.Core;
using Microsoft.RelInfra.Extensions;
using Microsoft.RelInfra.Storage.Exceptions;
using Microsoft.Rest;
using System;
using System.Net;
using System.Net.Http;
using System.Net.Sockets;

namespace Microsoft.Aether.AE3pService.Exceptions
{
    /// <inheritdoc/>
    public class ExceptionHandler : StorageExceptionHandler
    {

        /// <inheritdoc/>
        public override HttpStatusCode GetStatusCode(Exception exception)
        {
            HttpStatusCode statusCode = base.GetStatusCode(exception);
            if (statusCode == DefaultStatusCode)
            {
                switch (exception)
                {
                    case RunHistoryException e:
                        return e?.InnerException is HttpRequestDetailException ex && ex.StatusCode.HasValue ? ex.StatusCode.Value : HttpStatusCode.BadRequest;
                    case LimitExceededException e:
                        return e.StatusCode;
                    /*
                     * When the request is cancelled by client side, while the request is still being processed such as reading the request body, then BadHttpRequestException would be throwed. 
                     */
                    case BadHttpRequestException _:
                        return HttpStatusCode.BadRequest;
                    /*
                     *  When innerException is socketerror and error code is TryAgain(Retry and cannot connect), HostNotFound(no retry and cannot connect), TimedOut, HostUnreachable, ConnectionRefused, ConnectionReset will return 503 instead.
                     *  When innerException is System.IO.IOException, will return 503.
                     */
                    case HttpRequestException e:
                        {
                            if (e?.InnerException is SocketException se
                                && (se.SocketErrorCode == SocketError.TryAgain
                                    || se.SocketErrorCode == SocketError.HostNotFound
                                    || se.SocketErrorCode == SocketError.TimedOut
                                    || se.SocketErrorCode == SocketError.HostUnreachable
                                    || se.SocketErrorCode == SocketError.ConnectionRefused
                                    || se.SocketErrorCode == SocketError.ConnectionReset))
                            {
                                return HttpStatusCode.ServiceUnavailable;
                            }
                            else if (e?.InnerException is System.IO.IOException)
                            {
                                return HttpStatusCode.ServiceUnavailable;
                            }
                            else
                            {
                                return statusCode;
                            }
                        }
                    case PipelineVersionNotExistException e:
                        return e.StatusCode;
                    case HttpOperationException e:
                        return e.Response?.StatusCode ?? statusCode;
                }
            }
            return statusCode;
        }
    }
}
