﻿// <copyright file="ExceptionFrameworkMapper.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Aether.Backend.MetaStore.Service;
using Microsoft.Aether.DataContracts.Error;
using Microsoft.MachineLearning.Common.Core.Contracts.Errors;
using Microsoft.MachineLearning.Common.Core.Exceptions;
using Microsoft.RelInfra.Common.Exceptions;
using Microsoft.RelInfra.Extensions;
using Microsoft.RelInfra.Instrumentation.Exceptions;
using System;
using System.Linq;
using System.Net;

namespace Microsoft.Aether.AE3pService.Exceptions
{
    /// <summary>
    /// maps Ae3p controller Exception to latest error framework BaseExceptions
    /// </summary>
    public static class ExceptionFrameworkMapper
    {
        /// <summary>
        /// maps Ae3p controller Exception to latest error framework BaseExceptions
        /// <param name="exception">Incoming Exception</param>
        /// <param name="baseErrorDefinition"></param>
        /// <param name="messageParams"></param>
        /// <returns>BaseException</returns>
        /// </summary>
        public static Exception MapToBaseException(Exception exception, BaseErrorDefinition baseErrorDefinition, params object[] messageParams)
        {
            var outputException = exception;
            if (!(exception is BaseException) && !(exception is EtagMismatchException))
            {
                if (exception is HttpRequestDetailException httpRequestDetail)
                {
                    outputException = MapToPipelineBaseException(httpRequestDetail.StatusCode, httpRequestDetail.Message, httpRequestDetail, null);
                }
                else if (exception is MachineLearning.Common.Core.ServiceInvocationException svc)
                {
                    outputException = MapToPipelineBaseException(svc.Response.StatusCode, svc.Message, svc, null);
                }
                else if (exception is ServiceInvocationException relsvc)
                {
                    // the inner exception won't be included in the message if relsvc is formattable, but because relsvc doesn't include a message it is unlikely this will occur
                    outputException = MapToPipelineBaseException(relsvc.StatusCode ?? HttpStatusCode.InternalServerError, relsvc.Message + "; InnerException: " + exception.InnerException?.Message, relsvc, null);
                }
                else if (exception is MetaStoreException mse)
                {
                    outputException = MapToPipelineBaseException(mse.StatusCode, mse.Message, mse, null);
                }
                else if (exception is System.Net.Sockets.SocketException socerr)
                {
                    outputException = MapToPipelineBaseException(HttpStatusCode.ServiceUnavailable, socerr.Message + "; SocketException in fact.", socerr, null);
                }
                else
                {
                    /*
                     * Still keep the BaseException here since lots of UnitTests depend on BaseException.
                     * Handle the innerException output later in Exception output.
                     */
                    outputException = new BaseException(baseErrorDefinition.ToBaseError(null, (messageParams ?? Array.Empty<object>()).Append(exception.Message).ToArray()), exception);
                }
            }
            return outputException;
        }

        /// <summary>
        /// Maps Ae3p controller HttpStatusCode to latest error framework BaseExceptions with pipeline specified BaseError code.
        /// This will keep both code and message
        /// <returns>BaseException</returns>
        /// </summary>
        public static Exception MapToPipelineBaseException(HttpStatusCode? httpStatusCode, string message, Exception exception, string target)
        {
            Exception outputException;

            switch (httpStatusCode)
            {
                case HttpStatusCode.BadRequest:
                    outputException = ToBaseException<PipelineBadArgument>(message, exception, target);
                    break;
                case HttpStatusCode.Unauthorized:
                    outputException = ToBaseException<PipelineAuthentication>(message, exception, target);
                    break;
                case HttpStatusCode.Forbidden:
                    outputException = ToBaseException<PipelineAuthorization>(message, exception, target);
                    break;
                case HttpStatusCode.NotFound:
                    outputException = ToBaseException<PipelineNotFound>(message, exception, target);
                    break;
                case HttpStatusCode.MethodNotAllowed:
                    outputException = ToBaseException<PipelineMethodNotAllowed>(message, exception, target);
                    break;
                case HttpStatusCode.Conflict:
                    outputException = ToBaseException<PipelineConflict>(message, exception, target);
                    break;
                case HttpStatusCode.LengthRequired:
                    outputException = ToBaseException<PipelineLengthRequired>(message, exception, target);
                    break;
                case HttpStatusCode.PreconditionFailed:
                    outputException = ToBaseException<PipelinePreconditionFailed>(message, exception, target);
                    break;
                case HttpStatusCode.RequestEntityTooLarge:
                    outputException = ToBaseException<PipelineRequestEntityTooLarge>(message, exception, target);
                    break;
                case HttpStatusCode.UnsupportedMediaType:
                    outputException = ToBaseException<PipelineUnsupportedMediaType>(message, exception, target);
                    break;
                case HttpStatusCode.MisdirectedRequest:
                    outputException = ToBaseException<PipelineMisdirectedRequest>(message, exception, target);
                    break;
                case HttpStatusCode.TooManyRequests:
                    outputException = ToBaseException<PipelineTooManyRequests>(message, exception, target);
                    break;
                case (HttpStatusCode)499:
                    outputException = ToBaseException<PipelineClientClosedRequest>(message, exception, target);
                    break;
                case HttpStatusCode.BadGateway:
                    outputException = ToBaseException<PipelineBadGateway>(message, exception, target);
                    break;
                case HttpStatusCode.ServiceUnavailable:
                    outputException = ToBaseException<PipelineServiceUnavailable>(message, exception, target);
                    break;
                case HttpStatusCode.GatewayTimeout:
                    outputException = ToBaseException<PipelineGatewayTimeout>(message, exception, target);
                    break;
                case (HttpStatusCode)530:
                    outputException = ToBaseException<PipelineFailRouteRequest>(message, exception, target);
                    break;
                default:
                    // If no matching error code then default to PipelineInternalError
                    outputException = ToBaseException<PipelineInternalError>(message, exception, target);
                    break;
            }

            return outputException;
        }

        /// <summary>
        /// If the exception is formattable then return it's message format, otherwise return null
        /// </summary>
        /// <param name="exception"></param>
        /// <returns></returns>
        public static string MessageFormat(Exception exception)
        {
            return exception.GetMessageFormat();
        }

        /// <summary>
        /// Converts the exception to a <see cref="BaseException"/>
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="message"></param>
        /// <param name="exception"></param>
        /// <param name="target"></param>
        /// <returns></returns>
        private static BaseException ToBaseException<T>(string message, Exception exception, string target) where T : BaseErrorDefinition
        {
            BaseErrorDefinition baseErrorDef = (BaseErrorDefinition)Activator.CreateInstance(typeof(T), new object[] { exception.GetMessageFormat() });
            return new BaseException(baseErrorDef.ToBaseError(target, MessageParams(exception, message)), exception).MakeFormattable();
        }

        /// <summary>
        /// If the exception is formattable then return it's message args, otherwise return the defaults
        /// </summary>
        /// <param name="exception"></param>
        /// <param name="defaults"></param>
        /// <returns></returns>
        private static object[] MessageParams(Exception exception, params object[] defaults)
        {
            return exception.GetMessageArgs() ?? defaults;
        }
    }
}
