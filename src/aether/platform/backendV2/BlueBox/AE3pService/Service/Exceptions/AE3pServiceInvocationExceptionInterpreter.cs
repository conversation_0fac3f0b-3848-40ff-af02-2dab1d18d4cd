﻿// <copyright file="AE3pServiceInvocationExceptionInterpreter.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Common.Core.Contracts;
using Microsoft.MachineLearning.Common.Core;
using Microsoft.MachineLearning.Common.WebApi.ErrorHandling;
using System.Net;

namespace Microsoft.Aether.AE3pService.Exceptions
{
    public static class AE3pServiceInvocationExceptionInterpreter
    {
        public static readonly InterpreterCandidate InterpreterForServiceUnavailable = ConditionalExceptionInterpreter.MakeDelegatingCandidate<ServiceInvocationException>(
            e => e.Response.StatusCode == HttpStatusCode.ServiceUnavailable,
            HttpStatusCode.ServiceUnavailable,
            RootErrorCode.ServiceError);

        public static readonly InterpreterCandidate InterpreterForUserError = ConditionalExceptionInterpreter.MakeCandidate<ServiceInvocationException>(
            e => e.Response?.StatusCode == HttpStatusCode.Unauthorized || e.Response?.StatusCode == HttpStatusCode.BadRequest,
            new DelegatingExceptionInterpreter<ServiceInvocationException>(
                e => e.Response.StatusCode,
                _ => RootErrorCode.UserError,
                e => e.Message));
    }
}
