﻿// <copyright file="AE3pAuthorizationUbavailableExceptionInterpreter.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Common.Core.Contracts;
using Common.WebApi.Authorization;
using Microsoft.MachineLearning.Common.Core;
using Microsoft.MachineLearning.Common.WebApi.ErrorHandling;
using System.Net;

namespace Microsoft.Aether.AE3pService.Exceptions
{
    internal class AE3pAuthorizationUnavailableExceptionInterpreter
    {
        public static readonly InterpreterCandidate InterpreterForAuthorizeError = ConditionalExceptionInterpreter.MakeCandidate<AuthorizationUnavailableException>(
            e => (e.InnerException as ServiceInvocationException)?.Response?.StatusCode == HttpStatusCode.InternalServerError && (e.InnerException as ServiceInvocationException).ResponseBody.Contains("authorization", System.StringComparison.CurrentCulture),
            new DelegatingExceptionInterpreter<AuthorizationUnavailableException>(
                e => HttpStatusCode.ServiceUnavailable,
                _ => RootErrorCode.ServiceError,
                e => e.Message));
    }
}
