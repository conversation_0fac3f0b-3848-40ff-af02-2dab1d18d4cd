﻿// <copyright file="IExperimentRunCreator.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.Aether.DataContracts;
using Microsoft.Aether.DataContracts.Entity;
using Microsoft.MachineLearning.RunHistory.Contracts;

namespace Microsoft.Aether.AE3pService
{
    ///<Summary>
    ///</Summary>
    public interface IPipelineRunCreator
    {
        ///<Summary>
        ///</Summary>
        Task<RunDto> CreatePipelineRunAsync(
            WorkspaceIdentity amlWorkspaceIdentity,
            string experimentName,
            string displayName,
            string pipelineRunId,
            string? parentRunId,
            string pipelineId,
            string runSource,
            string runType,
            CreatedBy createdBy,
            IDictionary<string, string> parameterAssignments,
            IDictionary<string, DataSetDefinitionValue> dataSetDefinitionValueAssignments,
            IDictionary<string, AssetOutputSettings> assetOutputSettingsAssignments,
            IDictionary<string, string> experimentProperties = null,
            IDictionary<string, string> experimentTags = null,
            string description = null,
            string sourcePipelineRunId = null,
            string? runLineageType = null, // 3 possible value: null, cloned, resubmit.
            bool? continueRunOnStepFailure = null,
            bool? enforceRerun = null,
            int? scopePriority = null,
            int? amlComputePriority = null,
            bool continueRunOnFailedOptionalInput = true,
            ComputeSetting defaultCompute = null,
            DatastoreSetting defaultDatastore = null,
            IdentitySetting identityConfig = null,
            int? pipelineTimeoutSeconds = null,
            IEnumerable<string> inputTypePipelineParameters = null,
            string userAgent = null);
    }
}
