﻿// <copyright file="WorkspaceStatusChangeEventHandler.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.MachineLearning.AccountRP.Contracts.Messages;
using Microsoft.MachineLearning.Common.ServiceBus;
using Microsoft.MachineLearning.Common.WebApi.TeamAccounts;

namespace Microsoft.Aether.AE3pService.EventHandlers
{
    public class WorkspaceStatusChangeEventHandler : IServiceBusMessageHandler<WorkspaceStatusChangedEvent>
    {
        private readonly ILogger _logger;
        private readonly IWorkspaceCacheInvalidator _cacheInvalidator;

        public WorkspaceStatusChangeEventHandler(
            ILogger<WorkspaceStatusChangeEventHandler> logger,
            IWorkspaceCacheInvalidator cacheInvalidator)
        {
            _logger = logger;
            _cacheInvalidator = cacheInvalidator;
        }

        public async Task Handle(Envelope<WorkspaceStatusChangedEvent> envelope, CancellationToken cancellationToken)
        {
            var workspaceId = envelope.Body?.WorkspaceId;
            var worksapceName = envelope.Body?.WorkspaceName;
            var newState = envelope.Body?.NewState;
            _logger.LogInformation($"Handling WorkspaceStatusChangedEvent for workspaceName ={worksapceName} workspaceId={workspaceId} and newState={newState}");
            await _cacheInvalidator.HandleWorkspaceLifecycle(envelope, cancellationToken).ConfigureAwait(false);
        }
    }
}
