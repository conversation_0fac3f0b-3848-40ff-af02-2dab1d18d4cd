﻿// <copyright file="IContextProvider.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Security.Claims;
using Microsoft.Aether.DataContracts;

namespace Microsoft.Aether.AE3pService
{
    ///<Summary>
    ///</Summary>
    public interface IContextProvider
    {
        ///<Summary>
        ///</Summary>
        StoreOperationContext GetStoreOperationContext(ClaimsPrincipal user);

        ///<Summary>
        ///</Summary>
        CreatedBy GetCreatedByObject();
    }
}
