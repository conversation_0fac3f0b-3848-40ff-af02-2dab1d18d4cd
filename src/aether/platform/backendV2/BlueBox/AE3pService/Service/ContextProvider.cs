﻿// <copyright file="ContextProvider.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Security.Claims;
using Common.Core.Contracts;
using Common.WebApi.ActivityExtensions;
using Microsoft.Aether.DataContracts;
using Microsoft.AspNetCore.Http;
using Microsoft.MachineLearning.Common.Core;

namespace Microsoft.Aether.AE3pService
{
    internal class ContextProvider : IContextProvider
    {
        private readonly JwtSecurityTokenHandler _jwtHandler = new JwtSecurityTokenHandler();
        private readonly IOperationContextAccessor _operationContext;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public ContextProvider(IOperationContextAccessor operationContext, IHttpContextAccessor httpContextAccessor)
        {
            _operationContext = operationContext;
            _httpContextAccessor = httpContextAccessor;
        }

        public StoreOperationContext GetStoreOperationContext(ClaimsPrincipal user)
        {
            /*
             * Note: Parameter `user` is defined without any usage since getToken is changed from user claims to HttpContext.
             * But we'd like to keep the function signature because of lots of existing invoking.
             */
            string jwtToken = _httpContextAccessor.HttpContext.GetToken();

            User createdBy = _operationContext.Current.ActivityBaggage.GetCreatedBy();
            string tid = string.IsNullOrEmpty(createdBy?.UserTenantId) ? GetClaimValueFromToken(jwtToken, "tid") : createdBy.UserTenantId;
            string oid = string.IsNullOrEmpty(createdBy?.UserObjectId) ? GetClaimValueFromToken(jwtToken, "oid") : createdBy.UserObjectId;
            string userName = $"tid={tid},oid={oid}";
            string appId = GetClaimValueFromToken(jwtToken, "appid");

            return new StoreOperationContext
            {
                UserName = userName,
                ClientName = "Aether3P.Service",
                AzureTenantId = tid,
                AzureUserId = oid,
                AzureAppId = appId,
                Token = jwtToken,
            };
        }

        public CreatedBy GetCreatedByObject()
        {
            string sourceIp = null;
            bool skipPLCheck = false;
            if (_operationContext.Current.ActivityBaggage != null)
            {
                sourceIp = _operationContext.Current.ActivityBaggage.GetSourceIp();
                skipPLCheck = _operationContext.Current.ActivityBaggage.GetSkipRegistryPrivateLinkCheck();
            }

            User createdBy = _operationContext.Current.ActivityBaggage.GetCreatedBy();
            if (createdBy != null)
            {
                return new CreatedBy(
                    oid: createdBy.UserObjectId,
                    tid: createdBy.UserTenantId,
                    userName: createdBy.UserName,
                    puid: createdBy.UserPuId,
                    iss: createdBy.UserIss,
                    idp: createdBy.UserIdp,
                    altsecId: createdBy.UserAltSecId,
                    sourceIp: sourceIp,
                    skipRegistryPrivateLinkCheck: skipPLCheck);
            }
            else
            {
                return null;
            }
        }

        private string GetClaimValueFromToken(string token, string claim)
        {
            JwtSecurityToken authToken = _jwtHandler.ReadJwtToken(token);

            return authToken.Claims.FirstOrDefault(x => x.Type == claim)?.Value;
        }
    }
}
