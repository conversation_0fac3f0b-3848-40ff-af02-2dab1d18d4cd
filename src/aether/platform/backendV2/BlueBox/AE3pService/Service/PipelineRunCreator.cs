﻿// <copyright file="PipelineRunCreator.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.Aether.BlueBox.RunHistoryClient;
using Microsoft.Aether.DataContracts;
using Microsoft.Aether.DataContracts.Entity;
using Microsoft.MachineLearning.RunHistory.Contracts;

namespace Microsoft.Aether.AE3pService
{
    internal class PipelineRunCreator : IPipelineRunCreator
    {
        private readonly IRunHistoryClient _runHistoryClient;

        public PipelineRunCreator(IRunHistoryClient runHistoryClient)
        {
            _runHistoryClient = runHistoryClient;
        }

        public async Task<RunDto> CreatePipelineRunAsync(
            WorkspaceIdentity amlWorkspaceIdentity,
            string experimentName,
            string displayName,
            string pipelineRunId,
            string parentRunId,
            string pipelineId,
            string runSource,
            string runType,
            CreatedBy createdBy,
            IDictionary<string, string> parameterAssignments,
            IDictionary<string, DataSetDefinitionValue> dataSetDefinitionValueAssignments,
            IDictionary<string, AssetOutputSettings> assetOutputSettingsAssignments,
            IDictionary<string, string> experimentProperties = null,
            IDictionary<string, string> experimentTags = null,
            string description = null,
            string sourcePipelineRunId = null,
            string runLineageType = null,
            bool? continueRunOnStepFailure = null,
            bool? enforceRerun = null,
            int? scopePriority = null,
            int? amlComputePriority = null,
            bool continueRunOnFailedOptionalInput = true,
            ComputeSetting defaultCompute = null,
            DatastoreSetting defaultDatastore = null,
            IdentitySetting identityConfig = null,
            int? pipelineTimeoutSeconds = null,
            IEnumerable<string> inputTypePipelineParameters = null,
            string userAgent = null)
        {
            // If we receive aml token and there is no explicit parent run, extract parent run id out of it
            return await _runHistoryClient.CreateExperimentRunAsync(
                amlWorkspaceIdentity: amlWorkspaceIdentity,
                experimentName: experimentName,
                displayName: displayName,
                parentRunId: parentRunId,
                runId: pipelineRunId,
                pipelineId: pipelineId,
                runSource: runSource,
                runType: runType,
                createdBy: createdBy,
                parameterAssignments: parameterAssignments,
                dataSetDefinitionValueAssignments: dataSetDefinitionValueAssignments,
                experimentProperties: experimentProperties,
                experimentTags: experimentTags,
                description: description,
                sourcePipelineRunId: sourcePipelineRunId,
                runLineageType: runLineageType,
                continueRunOnStepFailure: continueRunOnStepFailure,
                enforceRerun: enforceRerun,
                scopePriority: scopePriority,
                amlComputePriority: amlComputePriority,
                continueRunOnFailedOptionalInput: continueRunOnFailedOptionalInput,
                defaultCompute: defaultCompute,
                defaultDatastore: defaultDatastore,
                identityConfig: identityConfig,
                pipelineTimeoutSeconds: pipelineTimeoutSeconds,
                inputTypePipelineParameters: inputTypePipelineParameters,
                userAgent: userAgent).ConfigureAwait(false);
        }
    }
}
