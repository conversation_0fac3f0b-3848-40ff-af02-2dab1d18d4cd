﻿// <copyright file="ITokenVerifier.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

namespace Microsoft.Aether.AE3pService
{
    ///<summary>
    /// Interface of TokenVerifier
    ///</summary>
    public interface ITokenVerifier
    {
        ///<summary>
        /// IsUserAmlToken
        ///</summary>
        bool IsUserAmlToken(string accessToken, string s2sAppId);
    }
}