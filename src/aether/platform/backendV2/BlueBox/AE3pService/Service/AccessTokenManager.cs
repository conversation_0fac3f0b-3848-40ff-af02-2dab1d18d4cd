﻿// <copyright file="AccessTokenManager.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.RelInfra.Common;
using Microsoft.RelInfra.Instrumentation.Logging;
using Microsoft.Aether.BlueBox.OboTokenServiceClient;
using Microsoft.Aether.S2S.Common;

namespace Microsoft.Aether.AE3pService
{
    ///<summary>
    /// AccessTokenManager
    ///</summary>
    ///<remarks>User token management.</remarks>
    public class AccessTokenManager : IAccessTokenManager
    {
        private readonly IOboTokenServiceClient _oboTokenServiceClient;
        private readonly IS2STokenProvider _s2sTokenProvider;
        private readonly ITokenVerifier _tokenVeirifier;

        ///<summary>
        /// AccessTokenManager
        ///</summary>
        ///<remarks>User token management.</remarks>
        /// <param name="oboTokenServiceClient">The oboTokenServiceClient</param>
        /// <param name="s2sTokenProvider">S2s token provider</param>
        /// <param name="tokenVeirifier">User token verifier</param>
        public AccessTokenManager(IOboTokenServiceClient oboTokenServiceClient, IS2STokenProvider s2sTokenProvider, ITokenVerifier tokenVeirifier = null)
        {
            _oboTokenServiceClient = oboTokenServiceClient;
            _s2sTokenProvider = s2sTokenProvider;
            _tokenVeirifier = tokenVeirifier ?? new TokenVerifier();
        }

        ///<summary>
        /// TrySaveUserTokenToOboAync
        ///</summary>
        ///<remarks>Try to save the user token to OboTokenService.</remarks>
        /// <param name="request">The HttpRequest</param>
        /// <param name="subscriptionId">Subscription Id</param>
        /// <param name="pipelineRunId">The pipeline run id</param>
        public async Task TrySaveUserTokenToOboAync(HttpRequest request, string subscriptionId, string pipelineRunId)
        {
            string accessToken = request.GetAuthToken();
            if (_tokenVeirifier.IsUserAmlToken(accessToken, _s2sTokenProvider.AppId))
            {
                List<string> tokenHeaders = new List<string>()
                {
                    AdalConstants.CosmosResource,
                    AdalConstants.ArmResource,
                    AdalConstants.StorageResource,
                    AdalConstants.Aether1pResource,
                    AdalConstants.SynapseResource
                };

                try
                {
                    await _oboTokenServiceClient.SaveOboTokenAsync(subscriptionId, tokenHeaders, accessToken);
                }
                catch (Microsoft.RelInfra.Common.Exceptions.ServiceInvocationException e)
                {
                    CommonLogger.LogEntityError(pipelineRunId, "Exception when calling OboToken Service to save user token for user: {safe_exception_message}, stack trace: {stack_trace}", e.InnerException.Message, e.StackTrace);
                    throw;
                }
            }
        }
    }
}