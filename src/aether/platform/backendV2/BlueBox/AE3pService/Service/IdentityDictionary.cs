﻿// <copyright file="IdentityDictionary.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Collections.Generic;

namespace Microsoft.Aether.AE3pService
{
    /// <summary>
    /// Assumes an identity mapping between keys and values unless specified otherwise
    /// </summary>
    /// <typeparam name="T">Types of keys an values in the mapping</typeparam>
    public class IdentityDictionary<T>
    {
        /// <param name="nonIdentityMappings">Exemptions to the identity mapping</param>
        internal IdentityDictionary(IReadOnlyDictionary<T, T> nonIdentityMappings)
        {
            _nonIdentityMappings = nonIdentityMappings;
        }

        /// <summary>
        /// Maps key to the corresponding value
        /// </summary>
        /// <param name="key">The key to map</param>
        /// <returns>The corresponding value</returns>
        internal T this[T key]
        {
            get
            {
                if (_nonIdentityMappings.TryGetValue(key, out T value))
                {
                    return value;
                }

                return key;
            }
        }

        private readonly IReadOnlyDictionary<T, T> _nonIdentityMappings;
    }
}
