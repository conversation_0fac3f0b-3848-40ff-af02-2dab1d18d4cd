﻿// <copyright file="StatusRoller.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.Aether.DataContracts;

using TaskStatus = Microsoft.Aether.DataContracts.TaskStatus;

namespace Microsoft.Aether.AE3pService
{
    internal static class StatusRoller
    {
        private static TaskStatusCode RollStatusCode(IEnumerable<TaskStatusCode> codes, bool isExperimentRunning)
        {
            if (!codes.Any())
            {
                return TaskStatusCode.Finished;
            }

            if (codes.All(code => code == TaskStatusCode.Bypassed))
            {
                return TaskStatusCode.Bypassed;
            }

            if (codes.All(code => code == TaskStatusCode.Finished || code == TaskStatusCode.Bypassed))
            {
                return TaskStatusCode.Finished;
            }

            if (codes.All(code => code == TaskStatusCode.NotStarted || code == TaskStatusCode.Bypassed))
            {
                return TaskStatusCode.NotStarted;
            }

            if (codes.Any(code => code == TaskStatusCode.Failed))
            {
                return TaskStatusCode.Failed;
            }

            if (codes.Any(code => code == TaskStatusCode.Canceled))
            {
                return TaskStatusCode.Canceled;
            }

            if (codes.Any(code => code == TaskStatusCode.Running))
            {
                return TaskStatusCode.Running;
            }

            if (codes.Any(code => code == TaskStatusCode.Finished))
            {
                return isExperimentRunning ? TaskStatusCode.PartiallyExecuted : TaskStatusCode.Canceled;
            }

            if (codes.Any(code => code == TaskStatusCode.Queued))
            {
                return TaskStatusCode.Queued;
            }

            if (codes.Any(code => code == TaskStatusCode.PartiallyExecuted))
            {
                return TaskStatusCode.PartiallyExecuted;
            }

            throw new ArgumentException("Invalid nested statuses: " + string.Join(" ", codes));
        }

        public static TaskStatus RollStatus(IEnumerable<TaskStatus> statuses, ExperimentEntity experiment)
        {
            return RollStatus(statuses, experiment.CreatedDate, experiment.Status.StatusCode == ExperimentStatusCode.Running);
        }

        private static TaskStatus RollStatus(
            IEnumerable<TaskStatus> statuses,
            DateTime experimentSubmitTime,
            bool isExperimentRunning)
        {
            List<TaskStatus> taskStatuses = statuses.ToList();
            if (taskStatuses.Any())
            {
                if (taskStatuses.Count > 1)
                {
                    return new TaskStatus
                    {
                        CreationTime = taskStatuses.Min(status => status.CreationTime),
                        EndTime = AggregateTimeStampsWithStatus(taskStatuses.Select(status => new KeyValuePair<TaskStatusCode, DateTime?>(status.StatusCode, status.EndTime)), Extremum.Max),
                        ScheduleTime = AggregateTimeStamps(taskStatuses.Select(status => status.ScheduleTime), Extremum.Min),
                        StartTime = AggregateTimeStampsWithStatus(taskStatuses.Select(status => new KeyValuePair<TaskStatusCode, DateTime?>(status.StatusCode, status.StartTime)), Extremum.Min),
                        StatusCode = RollStatusCode(taskStatuses.Select(status => status.StatusCode), isExperimentRunning),
                        HasWarnings = taskStatuses.Any(status => status.HasWarnings),
                        ReuseInfo = RollReuseInfo(taskStatuses.Select(s => s.ReuseInfo))
                    };
                }

                return taskStatuses.First();
            }

            return new TaskStatus
            {
                CreationTime = experimentSubmitTime,
                EndTime = experimentSubmitTime,
                ScheduleTime = experimentSubmitTime,
                StartTime = experimentSubmitTime,
                StatusCode = TaskStatusCode.Finished,
                HasWarnings = false
            };
        }

        private static TaskReuseInfo RollReuseInfo(IEnumerable<TaskReuseInfo> reuseInfos)
        {
            List<TaskReuseInfo> reuseList = reuseInfos.ToList();
            if (reuseList.Count == 0 || reuseList.Any(ri => ri == null))
            {
                return null;
            }

            if (reuseList.Count == 1)
            {
                return reuseList[0];
            }

            string commonExperimentId = reuseList[0].ExperimentId;
            if (reuseList.Any(ri => ri.ExperimentId != commonExperimentId))
            {
                commonExperimentId = string.Empty;
            }

            string commonPipelineRunId = reuseList[0].PipelineRunId;
            if (reuseList.Any(ri => ri.PipelineRunId != commonPipelineRunId))
            {
                commonPipelineRunId = string.Empty;
            }
            return new TaskReuseInfo
            {
                ExperimentId = commonExperimentId,
                PipelineRunId = commonPipelineRunId,
                NodeId = string.Empty
            };
        }

        private static DateTime? AggregateTimeStamps(IEnumerable<DateTime?> timestamps, Extremum extremum)
        {
            if (timestamps.Any(timestamp => !timestamp.HasValue))
            {
                return null;
            }

            return extremum == Extremum.Max
                ? timestamps.Max()
                : timestamps.Min();
        }

        private static DateTime? AggregateTimeStampsWithStatus(IEnumerable<KeyValuePair<TaskStatusCode, DateTime?>> statusTimestampPairs, Extremum extremum)
        {
            if (statusTimestampPairs.Any(t => !t.Value.HasValue && t.Key != TaskStatusCode.Bypassed))
            {
                return null;
            }

            var timestamps = statusTimestampPairs.Select(k => k.Value).Where(k => k.HasValue);
            return extremum == Extremum.Max ? timestamps.Max() : timestamps.Min();
        }

        private enum Extremum { Min, Max }
    }
}
