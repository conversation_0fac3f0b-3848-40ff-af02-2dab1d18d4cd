﻿// <copyright file="ChangeDetector.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using Newtonsoft.Json.Linq;

internal static class ChangeDetector
{
    /// <summary>
    /// Lists all top level properties that have changed, including changes to referenced data, but not changes in references.
    /// </summary>
    /// <typeparam name="T">The type of the entity to compare. Must be a data contract.</typeparam>
    /// <param name="original">The original version of the entity.</param>
    /// <param name="changed">The entity that may have changes.</param>
    /// <returns>Changed property names.</returns>
    public static IEnumerable<string> ListChangedProperties<T>(T original, T changed)
    {
        return JToken.FromObject(original).Children().Join(JToken.FromObject(changed).Children(),
            token => token.Path, token => token.Path,
            (left, right) => new Tuple<JToken, JToken>(left, right))
            .Where(pair => pair.Item1.ToString() != pair.Item2.ToString()).Select(pair => pair.Item1.Path);
    }
}