﻿// <copyright file="TokenVerifier.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using Microsoft.RelInfra.Common;

namespace Microsoft.Aether.AE3pService
{
    ///<summary>
    /// TokenVerifier
    ///</summary>
    ///<remarks>Check and verify user tokens.</remarks>
    public class TokenVerifier : ITokenVerifier
    {
        ///<summary>
        /// IsUserAmlToken
        ///</summary>
        ///<remarks>Check if the token is user token with alm resource.</remarks>
        /// <param name="accessToken">The access token string</param>
        /// <param name="s2sAppId">S2s app id</param>
        public bool IsUserAmlToken(string accessToken, string s2sAppId)
        {
            var jwToken = new System.IdentityModel.Tokens.Jwt.JwtSecurityToken(accessToken);
            return IsUserAmlToken(jwToken.Claims, s2sAppId);
        }

        ///<summary>
        /// IsUserAmlToken
        ///</summary>
        ///<remarks>Check if the claims in a token is user's with alm resource.</remarks>
        /// <param name="claims">The claims in the token</param>
        /// <param name="s2sAppId">S2s app id</param>
        public bool IsUserAmlToken(IEnumerable<Claim> claims, string s2sAppId)
        {
            var oid = claims.FirstOrDefault(claim => claim.Type == "oid")?.Value ?? string.Empty;
            var tid = claims.FirstOrDefault(claim => claim.Type == "tid")?.Value ?? string.Empty;
            var aud = claims.FirstOrDefault(claim => claim.Type == "aud")?.Value ?? string.Empty;

            if (oid.Equals(s2sAppId) || !aud.TrimEnd('/').Equals(AdalConstants.AmlResource, StringComparison.OrdinalIgnoreCase))
            {
                return false;
            }
            return true;
        }
    }
}