﻿// <copyright file="AE3pServiceConfiguration.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using Microsoft.Aether.BlueBox.AetherK8SCommon;
using Microsoft.RelInfra.Common.Configuration;
using Microsoft.RelInfra.Common.Exceptions;
using Microsoft.RelInfra.Common.Secrets;

namespace Microsoft.Aether.AE3pService
{
    /// <summary>
    /// </summary>
    public class AE3pServiceConfiguration : VerifiableConfig
    {
        private readonly SecretProvider _secretProvider;

        /// <summary>
        /// </summary>
        public RedisConfiguration RedisConfig { get; }

        /// <summary>
        /// </summary>
        public AE3pServiceConfiguration(IRelInfraConfiguration config, SecretProvider secretProvider)
            : base(config, verifyConfigOnChanges: true, configName: nameof(AE3pServiceConfiguration))
        {
            _secretProvider = secretProvider;
            RedisConfig = new RedisConfiguration(Config, secretProvider);
        }

        /// <summary>
        /// </summary>
        public int Port => Config.GetInteger("AE3pService.Port");

        /// <summary>
        /// </summary>
        public string[] AllowedCorsOrigins => Config.GetStringArrayEx(name: "AE3pService.AllowedCorsOrigins");

        /// <summary>
        /// </summary>
        public string ExperimentSubmitterUrl => Config.GetString("CloudManager.BaseUrl");

        /// <summary>
        /// </summary>
        public TimeSpan ExperimentSubmitterTimeout => Config.GetTimeSpan("CloudManager.Timeout");

        /// <summary>
        /// </summary>
        public TimeSpan GenericRequestTimeout => Config.GetTimeSpan("AE3pService.GenericRequestTimeout", TimeSpan.FromSeconds(15));

        /// <summary>
        /// </summary>
        public string MetaStoreUrl => Config.GetString("MetaStore.BaseUrl");

        /// <summary>
        /// </summary>
        public TimeSpan MetaStoreTimeout => Config.GetTimeSpan("SharedMetaStore.MetaStoreTimeout");

        /// <summary>
        /// </summary>
        public int MaxNumberOfNodes => Config.GetInteger("ExperimentSubmission.MaxNumberOfNodes");

        /// <summary>
        /// </summary>
        public string RunDeepLink => Config.GetString("ExperimentSubmission.RunDeepLink");

        /// <summary>
        /// </summary>
        public bool IsTokenValidationEnabled => Config.GetBoolean("TokenValidation.Enabled");

        /// <summary>
        /// </summary>
        public string Authority => Config.GetString("TokenValidation.Authority");

        /// <summary>
        /// </summary>
        public string AADDiscoveryUri => Config.GetString("TokenValidation.AADDiscoveryUri");

        /// <summary>
        /// </summary>
        public string AMLDiscoveryUri => Config.GetString("TokenValidation.AMLDiscoveryUri");

        /// <summary>
        /// </summary>
        public string ManagementUrl => Config.GetString("ARM.ManagementUrl");

        /// <summary>
        /// </summary>
        public string PermissionPath => Config.GetString("ARM.PermissionPath");

        /// <summary>
        /// </summary>
        public string Region => Config.GetString("Scheduler.Region");

        /// <summary>
        /// </summary>
        public int DefaultScheduleLimit => Config.GetInteger("Scheduler.Limit");

        /// <summary>
        /// </summary>
        public virtual int SubscriptionScheduleLimit(string subscriptionId)
        {
            try
            {
                return Config.GetInteger($"Scheduler.{subscriptionId}-Limit");
            }
            catch (RelInfraConfigurationException)
            {
                // no override setting for this subscription
                return -1;
            }
        }

        /// <summary>
        /// </summary>
        public string ResourceRelayUrl => Config.GetString("ResourceRelay.Url");

        /// <summary>
        /// </summary>
        public string AmlServicesEndpoint => Config.GetString("AmlServices.AmlServicesEndpoint");

        /// <summary>
        /// </summary>
        public string AmlAuthorizationEndpoint => Config.GetString("Authorization.AmlAuthServiceInternalEndpoint");

        /// <summary>
        /// </summary>
        public string StsEndpoint => Config.GetString("AmlServices.StsEndpoint");

        /// <summary>
        /// </summary>
        public bool EnableThrottling => Config.GetBoolean("Throttling.EnableThrottling");

        /// <summary>
        /// </summary>
        public bool FallbackToOldAuthorizationIfAuthorizationServiceCallFailed => Config.GetBoolean("AmlServices.FallbackToOldAuthorizationIfAuthorizationServiceCallFailed");

        /// <summary>
        /// </summary>
        public Dictionary<string, string> S2SAlternateAllowedClientIds => Config.GetDictionaryFromSection("S2SAlternateAllowedClientIds");

        /// <summary>
        /// </summary>
        public Dictionary<string, string> DefaultLimits => Config.GetDictionaryFromSection("ThrottlingLimits");

        /// <summary>
        /// </summary>
        public Dictionary<string, string> MachineLearningRepositoryAllowedOids => Config.GetDictionaryFromSection("MachineLearningRepositoryAllowedOids");

        /// <summary>
        /// </summary>
        public string MicrosoftTenantId => Config.GetString("S2SConstants.MicrosoftTenantId");

        /// <summary>
        /// </summary>
        public string InfraTenantId => Config.GetString("S2SConstants.AzureTenantId");

        /// <summary>
        /// </summary>
        public bool IsOtelAuditLogEnabled => Config.GetBoolean("OtelAuditLog.Enabled");

        /// <summary>
        /// </summary>
        public string AmlPipelineUrlTemplate => Config.GetString("AmlPipelineConfig.PipelineUrlTemplate");

        /// <summary>
        /// </summary>
        public string AmlPipelineEndpointUrlTemplate => Config.GetString("AmlPipelineConfig.PipelineEndpointUrlTemplate");

        /// <summary>
        /// </summary>
        public string AmlPrivateLinkPipelineUrlTemplate => Config.GetString("AmlPipelineConfig.PrivateLinkPipelineUrlTemplate");

        /// <summary>
        /// </summary>
        public string AmlPrivateLinkPipelineEndpointUrlTemplate => Config.GetString("AmlPipelineConfig.PrivateLinkPipelineEndpointUrlTemplate");

        /// <summary>
        /// </summary>
        public bool PerformPermissionsTokenCheck => Config.GetBoolean("Authorization.PerformPermissionsTokenCheck");

        /// <summary>
        /// </summary>
        public bool ValidateToken => Config.GetBoolean("Authorization.ValidateToken");

        /// <summary>
        /// </summary>
        public string ServiceInvokerReuseHttpClients => Config.GetString("ServiceInvoker.ReuseHttpClients");

        /// <summary>
        /// </summary>
        public string ServiceInvokerDefaultRequestTimeoutMilliseconds => Config.GetString("ServiceInvoker.DefaultRequestTimeoutMilliseconds");

        /// <summary>
        /// </summary>
        public string ServiceInvokerRDefaultRetryCount => Config.GetString("ServiceInvoker.DefaultRetryCount");

        /// <summary>
        /// </summary>
        public string ServiceInvokerDefaultDelayMilliseconds => Config.GetString("ServiceInvoker.DefaultDelayMilliseconds");
    }
}
