﻿// <copyright file="ClientStartup.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using Microsoft.Aether.BlueBox.CredentialServiceClient;
using Microsoft.Aether.BlueBox.DataStoreClient;
using Microsoft.Aether.BlueBox.DistributedTracing;
using Microsoft.Aether.BlueBox.MmsServiceClient;
using Microsoft.Aether.BlueBox.RepositoryResourcesClient;
using Microsoft.Aether.BlueBox.RunHistoryClient;
using Microsoft.Aether.BlueBox.SchedulerClient;
using Microsoft.Aether.BlueBox.SnapshotClient;
using Microsoft.Aether.BlueBox.WorkspaceResourcesClient;
using Microsoft.Aether.CloudManager.ExperimentSubmitter.Client;
using Microsoft.Aether.DataContracts.Metastore;
using Microsoft.Aether.S2S.Common;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.MachineLearning.Common.Core;
using Microsoft.RelInfra.Instrumentation.Logging;

namespace Microsoft.Aether.AE3pService.Extensions
{
    public class ClientStartup : IStartupFilter
    {
        public Action<IApplicationBuilder> Configure(Action<IApplicationBuilder> next)
        {
            return builder =>
            {
                CommonLogger.LogInfo("Initializing Clients with StartupFilter");
                builder.ApplicationServices.GetRequiredService<IS2STokenProvider>();
                builder.ApplicationServices.GetRequiredService<IStoreFactory>();
                builder.ApplicationServices.GetRequiredService<IDataStoreClient>();
                builder.ApplicationServices.GetRequiredService<ISnapshotClient>();
                builder.ApplicationServices.GetRequiredService<IMmsServiceClient>();
                builder.ApplicationServices.GetRequiredService<IRunMetricClient>();
                builder.ApplicationServices.GetRequiredService<IRunHistoryClient>();
                builder.ApplicationServices.GetRequiredService<IWorkspaceResourcesClient>();
                builder.ApplicationServices.GetRequiredService<IRepositoryResourcesClient>();
                builder.ApplicationServices.GetRequiredService<ICredentialServiceClient>();
                builder.ApplicationServices.GetRequiredService<SpanMeasurement>();
                builder.ApplicationServices.GetRequiredService<IExperimentSubmitterClientV2>();
                builder.ApplicationServices.GetRequiredService<IPipelineRunCreator>();
                builder.ApplicationServices.GetRequiredService<IOperationContextInitializer>();
                builder.ApplicationServices.GetRequiredService<IContextProvider>();
                builder.ApplicationServices.GetRequiredService<IStoreCollection>();
                builder.ApplicationServices.GetRequiredService<SchedulerClient>();
                builder.ApplicationServices.GetRequiredService<IAccessTokenManager>();
                next(builder);
            };
        }
    }
}
