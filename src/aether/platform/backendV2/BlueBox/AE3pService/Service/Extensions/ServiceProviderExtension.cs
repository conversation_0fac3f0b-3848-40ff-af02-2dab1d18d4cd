﻿// <copyright file="ServiceProviderExtension.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Threading;
using Microsoft.Aether.BlueBox.AetherK8SBaseService;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using Microsoft.RelInfra.Instrumentation.Logging;

namespace Microsoft.Aether.AE3pService.Extensions
{
    public static class ServiceProviderExtension
    {
        public static void SetupThreadPool(this IServiceProvider sp)
        {
            var baseServiceConfig = sp.GetService<IOptions<K8sBaseServiceConfig>>().Value;
            // Initialize thread pool
            ThreadPool.GetMinThreads(out int originalMinWorker, out int originalMinIOC);
            CommonLogger.LogEntityInfo("ThreadPool", $"Changing thread pool size. Worker threads {originalMinWorker}->{baseServiceConfig!.ThreadPoolMinWorkerThreads} , IO threads {originalMinIOC}->{baseServiceConfig!.ThreadPoolMinCompletionPortThreads}");
            ThreadPool.SetMinThreads(workerThreads: baseServiceConfig!.ThreadPoolMinWorkerThreads, completionPortThreads: baseServiceConfig!.ThreadPoolMinCompletionPortThreads);
        }
    }
}
