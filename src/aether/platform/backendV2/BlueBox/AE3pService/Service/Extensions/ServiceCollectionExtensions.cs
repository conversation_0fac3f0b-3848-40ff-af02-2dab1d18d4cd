﻿// <copyright file="ServiceCollectionExtensions.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Reflection;
using Aether.Backend.BlueBox.MetaStore.Client;
using Aether.Backend.BlueBox.MetaStoreV2.Client;
using Common.WebApi.Security;
using Microsoft.Aether.AE3pService.Auth;
using Microsoft.Aether.AE3pService.Configuration;
using Microsoft.Aether.AE3pService.EventHandlers;
using Microsoft.Aether.AE3pService.Exceptions;
using Microsoft.Aether.AE3pService.Telemetry;
using Microsoft.Aether.BackendCommon;
using Microsoft.Aether.BackendCommon.AzureMonitor;
using Microsoft.Aether.BackendCommon.ClusterHealthPoller;
using Microsoft.Aether.BlueBox.AetherK8SBaseService;
using Microsoft.Aether.BlueBox.CredentialServiceClient;
using Microsoft.Aether.BlueBox.DataStoreClient;
using Microsoft.Aether.BlueBox.DistributedTracing;
using Microsoft.Aether.BlueBox.MmsServiceClient;
using Microsoft.Aether.BlueBox.OboTokenServiceClient;
using Microsoft.Aether.BlueBox.RepositoryResourcesClient;
using Microsoft.Aether.BlueBox.RunHistoryClient;
using Microsoft.Aether.BlueBox.S2S.Common;
using Microsoft.Aether.BlueBox.SchedulerClient;
using Microsoft.Aether.BlueBox.SnapshotClient;
using Microsoft.Aether.BlueBox.Web.Common;
using Microsoft.Aether.BlueBox.WorkspaceResourcesClient;
using Microsoft.Aether.CloudManager.ExperimentSubmitter.Client;
using Microsoft.Aether.DataContracts.Metastore;
using Microsoft.Aether.S2S.Common;
using Microsoft.AIPlatform.CertificateReloader;
using Microsoft.AIPlatform.Common.Metrics;
using Microsoft.AIPlatform.Common.TelemetryEvents;
using Microsoft.AIPlatform.Telemetry.Contracts.Metrics;
using Microsoft.AIPlatform.WorkloadIdentity;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ApiExplorer;
using Microsoft.AspNetCore.Mvc.Controllers;
using Microsoft.AspNetCore.Mvc.Formatters;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using Microsoft.MachineLearning.AccountRP.Contracts.Messages;
using Microsoft.MachineLearning.Common.Arm;
using Microsoft.MachineLearning.Common.Caching;
using Microsoft.MachineLearning.Common.Core;
using Microsoft.MachineLearning.Common.Core.Arm.Contracts;
using Microsoft.MachineLearning.Common.Core.Configurations;
using Microsoft.MachineLearning.Common.Core.Contracts;
using Microsoft.MachineLearning.Common.Core.Features;
using Microsoft.MachineLearning.Common.Core.Metrics;
using Microsoft.MachineLearning.Common.Core.Security;
using Microsoft.MachineLearning.Common.ServiceBus;
using Microsoft.MachineLearning.Common.Startup;
using Microsoft.MachineLearning.Common.Startup.Telemetry;
using Microsoft.MachineLearning.Common.TelemetryEvents;
using Microsoft.MachineLearning.Common.WebApi.AppInsights;
using Microsoft.MachineLearning.Common.WebApi.Audit.Otel;
using Microsoft.MachineLearning.Common.WebApi.Audit.Otel.Attribute;
using Microsoft.MachineLearning.Common.WebApi.Authorization;
using Microsoft.MachineLearning.Common.WebApi.Authorization.Configuration;
using Microsoft.MachineLearning.Common.WebApi.Client;
using Microsoft.MachineLearning.Common.WebApi.ErrorHandling;
using Microsoft.MachineLearning.Common.WebApi.Extensions;
using Microsoft.MachineLearning.Common.WebApi.HealthChecks;
using Microsoft.MachineLearning.Common.WebApi.Security;
using Microsoft.MachineLearning.Common.WebApi.TeamAccounts;
using Microsoft.MachineLearning.Common.WebApi.TeamAccounts.Authorization;
using Microsoft.MachineLearning.Common.WebApi.Throttling;
using Microsoft.MachineLearning.Common.WebApi.Throttling.Configurations;
using Microsoft.MachineLearning.Dataset.Client;
using Microsoft.MachineLearning.Pipeline.Services.Common.Auth;
using Microsoft.OpenApi.Models;
using Microsoft.RelInfra.Common.Exceptions;
using Microsoft.RelInfra.Instrumentation;
using Microsoft.RelInfra.Instrumentation.Exceptions;
using Microsoft.RelInfra.Instrumentation.Logging;
using Microsoft.RelInfra.Instrumentation.Logging.LoggerConversion;
using Microsoft.RelInfra.Instrumentation.Test.Mocks;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using Orleans;
using Swashbuckle.AspNetCore.SwaggerGen;
using OpenTelemetry.Audit.Geneva;

namespace Microsoft.Aether.AE3pService.Extensions
{
    public static class ServiceCollectionExtensions
    {
        public static void ConfigureClient(this IServiceCollection services, IConfiguration configuration)
        {
            services.Configure<MetaStoreConfig>(configuration.GetSection("MetaStore"));
            services.Configure<AmlDataStoreConfig>(configuration.GetSection("AmlDataStoreClient"));
            services.Configure<AmlSnapshotConfig>(configuration.GetSection("AmlSnapshotClient"));
            services.Configure<MmsServiceConfig>(configuration.GetSection("MmsServiceClient"));
            services.Configure<AmlRunMetricServiceConfig>(configuration.GetSection("AmlRunMetricClient"));
            services.Configure<AmlRunHistoryConfig>(configuration.GetSection("RunHistoryClient"));
            services.Configure<WorkspaceResourcesConfig>(configuration.GetSection("WorkspaceResourcesClient"));
            services.Configure<RepositoryResourcesConfiguration>(configuration.GetSection("RepositoryResourcesClient"));
            services.Configure<CredentialServiceConfig>(configuration.GetSection("CredentialServiceClient"));
            services.Configure<DistributedTracingConfig>(configuration.GetSection("DistributedTracingClient"));
            services.Configure<SchedulerClientConfig>(configuration.GetSection("SchedulerClient"));
            services.Configure<ExperimentSubmitterClientConfig>(configuration.GetSection("ExperimentSubmitter"));
            services.Configure<IConfiguration>(configuration.GetSection("OtelAudit"));
            services.AddSingleton<IS2STokenProvider>(sp =>
            {
                var s2sConfiguration = configuration.GetSection("S2S").Get<S2SConfiguration>();
                var azureConstantConfiguration = configuration.GetSection("AzureConstants").Get<AzureConstantsConfiguration>();
                var amlS2SServicePrincipalConfig = new AmlS2SServicePrincipalConfig
                {
                    Authority = s2sConfiguration.Authority,
                    Resource = s2sConfiguration.Resource,
                    AadAuthority = azureConstantConfiguration.AadAuthority
                };
                var certificateService = sp.GetRequiredService<ICertificateService>();
                return new S2STokenProvider(
                    config: amlS2SServicePrincipalConfig,
                    appId: s2sConfiguration.ClientId,
                    certficateService: certificateService,
                    counters: sp.GetRequiredService<CounterManager>());
            });

            services.AddSingleton<IAzureMachineLearningDatasetManagementClientFactory>(sp =>
            {
                return new AzureMachineLearningDatasetManagementClientFactory(
                    handler: new MessageHandlerWithAuth(sp.GetRequiredService<IS2STokenProvider>()),
                    endpoint: new Uri(configuration.GetValue<string>("AmlDatasetConfig:DatasetInternalEndpoint")),
                    timeout: configuration.GetValue<TimeSpan>("AmlDatasetConfig:Timeout"));
            });

            services.AddSingleton<IStoreFactory, StoreFactory>();
            services.AddSingleton<IDataStoreClient, DataStoreClient>();
            services.AddSingleton<IMmsServiceClient, MmsServiceClient>();
            services.AddSingleton<IRunMetricClient, AmlRunMetricClient>();
            services.AddSingleton<IRunHistoryClient, RunHistoryClient>();
            services.AddSingleton<IWorkspaceResourcesClient, WorkspaceResourcesClient>();
            services.AddSingleton<IRepositoryResourcesClient, RepositoryResourcesClient>();
            services.AddSingleton<ICredentialServiceClient, CredentialServiceClient>();

            services.AddSingleton<ISnapshotClient>(sp =>
            {
                var amlSnapshotConfig = configuration.GetSection("AmlSnapshotClient").Get<AmlSnapshotConfig>();
                var s2sTokenProvider = sp.GetRequiredService<IS2STokenProvider>();
                var counterManager = sp.GetRequiredService<CounterManager>();
                var clusterHealthPoller = new ClusterHealthPoller();
                return new SnapshotClient(amlSnapshotConfig, s2sTokenProvider, counterManager, clusterHealthPoller);
            });

            services.AddSingleton(sp =>
            {
                var loggerFactory = new LoggerFactory(new[] { new CommonLoggerProvider() });
                var configOption = sp.GetRequiredService<IOptionsMonitor<DistributedTracingConfig>>();
                var runHistoryClient = sp.GetRequiredService<IRunHistoryClient>();
                return new SpanMeasurement(new DistributedTracingContext(ServiceNames.Ae3pService, runHistoryClient, configOption.CurrentValue, loggerFactory));
            });
            services.AddSingleton<IExperimentSubmitterClientV2, ExperimentSubmitterClientV2>();
            services.AddSingleton<IPipelineRunCreator, PipelineRunCreator>();
            services.AddSingleton<IOperationContextAccessor>(sp => new OperationContextManager(new Lazy<ILogger<OperationContextManager>>(new LoggerFactory().CreateLogger<OperationContextManager>())));
            services.AddSingleton<IOperationContextInitializer>(sp => sp.GetRequiredService<IOperationContextAccessor>() as OperationContextManager);
            services.AddSingleton<IContextProvider, ContextProvider>();
            services.AddSingleton<IStoreCollection, StoreCollection>();
            services.AddSingleton<SchedulerClient>();

            services.AddSingleton<IAccessTokenManager>(sp =>
            {
                var oboTokenServiceConfig = configuration.GetSection("OboTokenServiceConfig").Get<OboTokenServiceConfig>();
                var s2sTokenProvider = sp.GetRequiredService<IS2STokenProvider>();
                var clusterHealthPoller = new ClusterHealthPoller();
                var oboTokenServiceClient = new OboTokenServiceClient(oboTokenServiceConfig, s2sTokenProvider, clusterHealthPoller);
                return new AccessTokenManager(oboTokenServiceClient, s2sTokenProvider);
            });

            services.AddSingleton<IStartupFilter, ClientStartup>();
        }

        public static void ConfigureATLTelemetry(this IServiceCollection services, IConfiguration configuration)
        {
            services.ConfigureTelemetry(configuration);
            services.Decorate<ITelemetryEventReporter<AzureMLTelemetryEvent>>(
                (inner, provider) => new AzureMLTelemetryEventReporterDecorator(inner, provider.GetRequiredService<IOptionsMonitor<EnvironmentVariableConstantsConfiguration>>()));

            services.AddSingleton<IJointMetricsPublisher>((sp) => new AmlTelemetryMetricsPublisher(sp.GetRequiredService<ILoggerFactory>(), sp.GetRequiredService<IMetricsReporter>()));
            services.AddSingleton<IMetricReporter>(sp => new LegacyMetricReporter(sp.GetRequiredService<IJointMetricsPublisher>()));
        }

        public static void ConfigureGeneralServices(this IServiceCollection services, IConfigurationRoot configuration)
        {
            services.Configure<K8sBaseServiceConfig>(configuration.GetSection("K8sBaseService"));
            services.Configure<AmlPipelineConfig>(configuration.GetSection("AmlPipelineConfig"));
            services.Configure<PipelineRunConfig>(configuration.GetSection("PipelineRunConfig"));

            services.Configure<S2SConfiguration>(configuration.GetSection("S2S"));
            services.Configure<AzureConstantsConfiguration>(configuration.GetSection("AzureConstants"));
            services.Configure<ResourceAuthorizationSettings>(configuration.GetSection("ResourceAuthorizationSettings"));
            services.Configure<TokenValidationConfiguration>(configuration.GetSection("TokenValidation"));
            services.Configure<LimitsConfiguration>(configuration.GetSection("Limits"));
            services.Configure<ARMConfiguration>(configuration.GetSection("ARM"));
            services.Configure<ResourceAuthorizationSettings>(configuration.GetSection("ResourceAuthorization"));
            services.Configure<ServiceInvokerConfiguration>(configuration.GetSection("ServiceInvoker"));
            services.Configure<CheckSubscriptionStateConfiguration>(configuration.GetSection("CheckSubscriptionState"));
            services.Configure<ExperimentationAccountArmConfiguration>(configuration.GetSection("ExperimentationAccountArm"));
            services.Configure<ExperimentationHostConfiguration>(configuration.GetSection("ExperimentationHost"));
            services.Configure<AuthorizationClientConfiguration>(configuration.GetSection("AuthorizationClient"));
            services.Configure<MachineLearningRepositoryConfiguration>(configuration.GetSection("MachineLearningRepository"));
            services.Configure<ThrottlingConfiguration>(configuration.GetSection("Throttling"));

            services.AddSingleton<IExceptionLoggingPolicy, AE3pExceptionLoggingPolicy>();
            services.AddSingleton<IS2SRequestIdentifier, S2SRequestIdentifier>();
            services.AddSingleton<CounterManager>(sp =>
            {
                var statsConfig = configuration.GetSection("SharedStatsdConfig").Get<StatsdConfiguration>();
                if (statsConfig.EnvironmentName.Equals("Onebox", StringComparison.OrdinalIgnoreCase))
                {
                    return new CounterManager("onebox", new MockCounterFactory());
                }
                return new StatsdCounterManagerCreator(statsConfig).CreateAsync(configuration.GetServiceName()).Result;
            });
            services.AddSingleton<IWorkspaceResourcesCache>(sp =>
            {
                IObjectCache cache = sp.GetRequiredService<IObjectCache>();
                IMemoryCache memoryCache = sp.GetRequiredService<IMemoryCache>();
                var amlWorkspaceResourcesClient = sp.GetRequiredService<IWorkspaceResourcesClient>();
                var counterManager = sp.GetRequiredService<CounterManager>();
                return new WorkspaceResourcesCache(cache, memoryCache, amlWorkspaceResourcesClient, counterManager);
            });

            services.AddSingleton<IRequestUserProfileHandler, RequestUserProfileHandler>();
            // Needed for validation
            services.AddSingleton<SigningKeyProviderFactory>();
            services.AddSingleton<ISigningKeyProvider>(sp => sp.GetRequiredService<SigningKeyProviderFactory>().Build());
            services.AddSingleton<ISecurityTokenValidator, JwtSecurityTokenHandler>();
            services.AddSingleton<IAadIssuerValidator, AadIssuerValidator>();

            // SigningKeysHealthCheck is introduced by PR: https://msdata.visualstudio.com/Vienna/_git/vienna/pullrequest/869711
            services.AddSingleton<SigningKeysHealthCheck>();
            services.AddHealthChecks()
                .AddCheck<SigningKeysHealthCheck>(
                    "SigningKeysHealthCheck",
                    tags: new[] { HealthCheckTags.ReadinessTag });

            // Needed for authorization
            services.Configure<AuthorizationClientConfiguration>(configuration.GetSection("AuthorizationClient"));
            services.AddRoleBasedAccessAuthorizationComponents(configuration);

            // S2S
            services.AddSingleton<IAuthorizationHandler, ClaimsClientIdS2SAuthorizationHandler>();

            // Subscription Validation
            services.AddScoped<ISubscriptionStateCache, SubscriptionStateCache>();
            services.AddSingleton<SubscriptionStateValidator>();

            // AML
            services.AddSingleton<IAuthorizationHandler, ViennaTokenAuthorizationHandler>();
            services.AddSingleton<IRPClient, RPClient>();
            services.AddSingleton<IActiveDirectoryTokenProvider, ServicePrincipalAADTokenProvider>();
            services.AddSingleton<IS2SClientFactory, S2SClientFactory>();
            services.AddSingleton<IServiceInvokerFactory, ServiceInvokerFactory>();

            // ARM
            services.AddScoped<IArmAuthorizationHandler, AEVAArmAuthorizationHandler>();
            services.AddScoped<IAuthorizationHandler, ResourceAuthorizationHandler>();

            // Needed for ARMClient
            services.AddSingleton<IServiceInvokerRetryPolicy, ServiceInvokerRetryPolicy>();
            services.AddSingleton<ServiceInvokerFactory>();

            services.AddSingleton<IARMClient, ARMClient>();
            services.AddSingleton<IRPResourceCache, PipelineRPResourceCache>();
            services.AddSingleton<IWorkspaceLimiter, WorkspaceLimiter>();
            services.AddSingleton<IRegistryLimiter, RegistryLimiter>();
            services.AddSingleton<IUserPermissionCache, UserPermissionCache>();
            services.AddSingleton<IScopeRetriever, AzureScopeRetriever>();
            services.AddSingleton<IAzureMachineLearningScopeRetriever, AzureMachineLearningScopeRetriever>();
            services.AddScoped<IArmContext, ArmContext>();

            // azure monitor
            services.AddSingleton<ResourceIDBuilder>();
            services.AddSingleton<IAzureMonitorStatusLogger, AzureMonitorStatusLogger>();

            // thread pool
            services.AddSingleton<ThreadPoolMonitor>();

            services.AddSingleton<IWorkspaceContext, WorkspaceContext>();
            services.AddSingleton<IResourceStorageConnectionDtoCache, ResourceStorageConnectionDtoCache>();
            services.AddSingleton<IWorkspaceStorageMsiTokenCache, WorkspaceStorageMsiTokenCache>();
            services.AddSingleton<IProjectResourceCache, ProjectResourceCache>();
            services.AddSingleton<IMSITokenCache, MSITokenCache>();
            services.AddSingleton<IWorkspaceCacheInvalidator, WorkspaceCacheInvalidator>();
        }

        public static void ConfigureGeneralWebServices(this IServiceCollection services, IConfiguration configuration)
        {
            var corsConfiguration = configuration.GetSection("Cors").Get<CorsConfiguration>();
            services.AddCors(setupAction: corsOptions =>
            {
                corsOptions.AddPolicy(
                    name: BaseController.DefaultCorsPolicyName,
                    configurePolicy: p =>
                    {
                        p.WithOrigins(corsConfiguration.AllowedOrigins.Values.ToArray())
                            .SetIsOriginAllowedToAllowWildcardSubdomains()
                            .AllowAnyMethod()
                            .AllowAnyHeader();
                    });
            });

            services.AddApiThrottling<ThrowThrottlingExceptionThrottlingHandler>(configuration);

            services.AddMvc(options =>
            {
                options.EnableEndpointRouting = false;
                // the Error Handling section below is required for this filter
                options.Filters.Add(typeof(CustomErrorResponseExceptionFilter));
                options.Filters.Add(typeof(ActionDescriptorForwarder));
                options.Filters.Add(typeof(SimpleMeasureAttribute));
                options.AddCommonAppInsightsComponents();
                var stringFormatter = options.OutputFormatters.OfType<StringOutputFormatter>().FirstOrDefault();
                if (stringFormatter != null)
                {
                    options.OutputFormatters.Remove(stringFormatter);
                    options.OutputFormatters.Add(stringFormatter);
                }
            });

            services.AddSingleton<IExceptionHandler, ExceptionHandler>();
            services.AddErrorHandling(configuration, sp => AE3pExceptionInterpreterProvider.GetInterpreterProvider(sp));
            services.AddScoped<IExceptionInterpretationPreProcessor, ExceptionHandler>();
            services.AddSingleton<IExceptionLoggingPolicy, AE3pExceptionLoggingPolicy>();
            services.AddSingleton<IServiceNameGetter>(serviceProvider => new ServiceNameGetter(configuration.GetServiceName()));
            // note: AddErrorHandling adds an IExceptionInterpretationLogger for us, but uses a logger that doesn't respect privacy, so we overwrite it
            services.AddSingleton<IExceptionInterpretationLogger, CommonExceptionInterpretationLogger>();

            services.AddHealthChecks();
            services.AddSingleton<MetaStoreHealthCheck>();
            services.AddSingleton<HttpClient>();
            services.Configure<HealthCheckServiceOptions>(options =>
            {
                options.Registrations.Add(new HealthCheckRegistration(
                    "MetaStoreHealthCheck",
                    sp => sp.GetRequiredService<MetaStoreHealthCheck>(),
                    HealthStatus.Unhealthy,
                    HealthCheckTags.ReadinessTags));
            });
        }

        public static void ConfigureAuthentication(this IServiceCollection services, IConfiguration configuration)
        {
            services
                .AddControllers()
                .AddNewtonsoftJson(options =>
                {
                    options.SerializerSettings.ContractResolver = new DefaultContractResolver();
                    options.SerializerSettings.Formatting = Formatting.Indented;
                });
            services.AddJwtAuthentication(configuration);

            services.AddAuthorization(
                options =>
                {
                    options.AddPolicy(
                        AuthorizationRole.S2S.ToString(),
                        policy =>
                        {
                            policy.AuthenticationSchemes.Add(JwtBearerDefaults.AuthenticationScheme);
                            policy.RequireAuthenticatedUser();
                            policy.AddRequirements(new ResourceAuthorizationRequirements()
                            {
                                Role = AuthorizationRole.S2S
                            });
                        });
                    options.AddPolicy(
                        AuthorizationRole.Contributor.ToString(),
                        policy =>
                        {
                            policy.AuthenticationSchemes.Add(JwtBearerDefaults.AuthenticationScheme);
                            policy.RequireAuthenticatedUser();
                            policy.AddRequirements(new ResourceAuthorizationRequirements()
                            {
                                Role = AuthorizationRole.Contributor
                            });
                        });
                    options.AddPolicy(
                        AuthorizationRole.Reader.ToString(),
                        policy =>
                        {
                            policy.AuthenticationSchemes.Add(JwtBearerDefaults.AuthenticationScheme);
                            policy.RequireAuthenticatedUser();
                            policy.AddRequirements(new ResourceAuthorizationRequirements()
                            {
                                Role = AuthorizationRole.Reader
                            });
                        });
                    options.AddPolicy(
                        "AllowedRepositoryUsers",
                        policy => policy.Requirements.Add(new AllowedRepositoryUsersRequirement()));
                });

            // Test auth with allow list
            services.AddSingleton<IAuthorizationHandler, AllowedRepositoryUsersHandler>();
        }

        public static void ConfigureAPIVersion(this IServiceCollection services)
        {
            // This line ensures the format of the Group Name is v.[major].[minor]-status
            // https://github.com/Microsoft/aspnet-api-versioning/wiki/Version-Format
            // this name is later used in the inclusion predicate to determine which api documentation page
            // an api endpoint belongs to
            services.AddVersionedApiExplorer(o => { o.GroupNameFormat = "'v'VVVV"; o.SubstituteApiVersionInUrl = true; });

            services.AddApiVersioning(o =>
            {
                o.ReportApiVersions = true;
                o.AssumeDefaultVersionWhenUnspecified = true; // this is needed for root / to work, otherwise it complains about missing api version
                o.DefaultApiVersion = new AspNetCore.Mvc.ApiVersion(majorVersion: 1, minorVersion: 0);
                // Remarks:
                //     When this property is set to true, API versioning policies only apply to controllers
                //     that remain after the Microsoft.AspNetCore.Mvc.ApplicationModels.IApiControllerFilter
                //     has been applied. When this property is set to false, API versioning policies
                //     are considers for all controllers. This was default behavior behavior in previous
                //     versions.
                // Since almost all the controllers do not have the [ApiController] attribute, 
                // we set this to false to use the default behaviour in previous versions 
                o.UseApiBehavior = false;
            });
        }

        /// <summary>
        /// TODO: Considering the refactor here.
        /// </summary>
        /// <param name="services">sc</param>
        public static void ConfigureSwaggerGen(this IServiceCollection services)
        {
            services.AddSwaggerGen(swagger =>
            {
                var provider = services.BuildServiceProvider().GetRequiredService<IApiVersionDescriptionProvider>();

                // This portion adds a documentation page for each of the discovered api versions we expose
                foreach (var description in provider.ApiVersionDescriptions)
                {
                    var version = description.ApiVersion.ToString();
                    swagger.SwaggerDoc("public", new OpenApiInfo { Version = version, Title = "AML Pipelines API " + version });
                    swagger.SwaggerDoc("private", new OpenApiInfo { Version = version, Title = "AML Pipelines API Private " + version });
                    swagger.SwaggerDoc("pipelineendpointsubmit", new OpenApiInfo { Version = version, Title = "AML PipelinesEndPointSubmit" + version });
                }

                swagger.CustomSchemaIds(i => i.FullName);

                swagger.DocInclusionPredicate((docName, apiDesc) =>
                {
                    IEnumerable<string> pathparts = apiDesc.RelativePath.Split('/').Select(v => v.Replace("v{version}", "v" + apiDesc.GetApiVersion()));
                    apiDesc.RelativePath = string.Join("/", pathparts);

                    // Exclude legacy "Experiments/" APIs from swagger
                    // todo: remove this condition once /Experiments/ methods are removed from PipelineRuns controller
                    if (apiDesc.RelativePath.Contains("/Experiments/"))
                    {
                        return false;
                    }
                    if (!apiDesc.TryGetMethodInfo(out MethodInfo methodInfo)) return false;

                    if (apiDesc.RelativePath.ToLower().Contains("pipelineendpointsubmit"))
                    {
                        //exclude items marked with private API routing
                        if (apiDesc.RelativePath.Contains("api/v"))
                        {
                            return false;
                        }

                        return true;
                    }

                    if (docName == "public")
                    {
                        //exclude items marked with private API routing
                        if (apiDesc.RelativePath.Contains("api/v"))
                        {
                            return false;
                        }

                        //as well as ones marked as 'only for private (v1.0) document'
                        string groupNameInc = GetGroupNameAttribute(apiDesc);
                        if (groupNameInc == "private")
                        {
                            return false;
                        }

                        return true;
                    }

                    if (docName == "private")
                    {
                        //exclude items marked with public API routing
                        if (apiDesc.RelativePath.Contains("pipelines/v"))
                        {
                            return false;
                        }

                        //as well as ones marked as 'only for public document'
                        string groupNameInc = GetGroupNameAttribute(apiDesc);
                        if (groupNameInc == "public")
                        {
                            return false;
                        }

                        return true;
                    }

                    // return true;

                    // At this point we are writing the logic to determine in which page the api endpoint belongs
                    // We check the controller apiversion attribute and the action MapTo attribute
                    // The MapTo api should take precedence in case of an overlap
                    IEnumerable<AspNetCore.Mvc.ApiVersion> controllerVersions = GetControllerAttributes(apiDesc)
                        .OfType<ApiVersionAttribute>()
                        .SelectMany(attr => attr.Versions);

                    IEnumerable<AspNetCore.Mvc.ApiVersion> actionVersions = GetActionAttributes(apiDesc)
                                                                .OfType<MapToApiVersionAttribute>()
                                                                .SelectMany(attr => attr.Versions);

                    bool controllerAndActionVersionsOverlap = controllerVersions.Intersect(actionVersions).Any();

                    ApiParameterDescription versionParameter = apiDesc.ParameterDescriptions
                                                                      .SingleOrDefault(p => p.Name == "version");

                    // we remove the version parameter from the documentation and we modify the relative path
                    // with the correct api version so that the documentation is clear
                    // it is done when a match is found
                    if (versionParameter != null)
                    {
                        apiDesc.ParameterDescriptions.Remove(versionParameter);
                    }

                    bool match = false;
                    // The comparison is done against the documentation page name which should match the api version
                    if (controllerAndActionVersionsOverlap)
                    {
                        match = actionVersions.Any(v => $"v{v.ToString()}" == docName);
                    }
                    else
                    {
                        // We need to compare the apiDesc GroupName for the case that there is more than one api version in a controller
                        match = $"v{controllerVersions.FirstOrDefault()}" == docName && apiDesc.GroupName == docName;
                    }

                    if (match)
                    {
                        IEnumerable<string> values = apiDesc.RelativePath
                            .Split('/')
                            .Select(v => v.Replace("v{version}", docName));

                        apiDesc.RelativePath = string.Join("/", values);
                    }
                    return match;
                });

                swagger.IncludeXmlComments(Path.Combine(AppContext.BaseDirectory, "AE3pService.xml"));
                swagger.SchemaFilter<SwaggerExcludeFilter>();
            });
            services.AddSwaggerGenNewtonsoftSupport();
        }

        public static void ConfigureCache(this IServiceCollection services, IConfiguration configuration)
        {
            // Add redis cache but inject our Counter decorator around it
            services.AddSingleton<IObjectCacheDecorator, AetherCacheDecorator>();
            services.AddCommonCaching(configuration);
            services.AddMemoryCache();
        }

        public static void ConfigureCommonServiceBus(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddSingleton((IConfigurationRoot)configuration);
            services.AddCommonServiceBus(configuration);
            services.AddCoreFeatureSet((IConfigurationRoot)configuration);
            services.AddWorkloadIdentity();
            services.ConfigureCoreFeatureUsage((sp, features) =>
            {
                var workloadIdentityProvider = sp.GetRequiredService<IWorkloadIdentityProvider>();
                var loggerFactory = sp.GetRequiredService<ILoggerFactory>();
                var logger = loggerFactory.CreateLogger("WorkloadIdentity");
                logger.LogInformation($"Workload identity available: {workloadIdentityProvider.IsAvailable}.");

                features.BindFeatureFlagPredicate(
                    CoreFeature.UseWorkloadIdentityAuthForServiceBus,
                    () => workloadIdentityProvider.IsAvailable);
            });

            services.AddSingleton(sp => sp.CreateTopicMessageReceiver("common", "workspaceLifecycle", "appWorkspaceLifecycle"));
            services.AddSingleton<IServiceBusMessageHandler<WorkspaceStatusChangedEvent>, WorkspaceStatusChangeEventHandler>();
        }

        public static void ConfigureAudit(this IServiceCollection services, IConfiguration configuration)
        {
            if (configuration.IsOtelAuditLogEnabled())
            {
                services.AddAuditComponents(configuration);
            }
        }

        public static void ConfigureHostedService(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddHostedService<ThreadPoolMonitor>();
            services.Configure<EnvironmentVersionLoggerConfig>(configuration.GetSection("Telemetry"));
            services.AddHostedService<EnvironmentVersionLoggerService>();
        }

        private static IEnumerable<object> GetControllerAttributes(ApiDescription apiDescription)
        {
            try
            {
                ControllerActionDescriptor val = apiDescription.ActionDescriptor as ControllerActionDescriptor;
                if (val != null)
                {
                    return CustomAttributeExtensions.GetCustomAttributes(val.ControllerTypeInfo, true).ToList();
                }
            }
            catch (Exception ex)
            {
                CommonLogger.LogInfo($"Unable to get controller attributes for {JsonConvert.SerializeObject(apiDescription)}" + "due to error: {exception}", ex);
            }
            return Enumerable.Empty<object>();
        }

        private static IEnumerable<object> GetActionAttributes(ApiDescription apiDescription)
        {
            try
            {
                ControllerActionDescriptor val = apiDescription.ActionDescriptor as ControllerActionDescriptor;
                if (val != null)
                {
                    return CustomAttributeExtensions.GetCustomAttributes(val.MethodInfo, true).ToList();
                }
            }
            catch (Exception ex)
            {
                CommonLogger.LogInfo($"Unable to get action attributes for {JsonConvert.SerializeObject(apiDescription)} due to exception {ex}");
            }
            return Enumerable.Empty<object>();
        }

        private static string GetGroupNameAttribute(ApiDescription apiDescription)
        {
            try
            {
                ControllerActionDescriptor val = apiDescription.ActionDescriptor as ControllerActionDescriptor;
                ApiExplorerSettingsAttribute myAttr = null;

                // start with the most specific setting
                if (val.MethodInfo != null)
                {
                    myAttr = (ApiExplorerSettingsAttribute)Attribute.GetCustomAttribute(val.MethodInfo, typeof(ApiExplorerSettingsAttribute));
                }

                // fall through to the controller level only if not already resolved
                if ((val.ControllerTypeInfo != null) && (myAttr == null))
                {
                    myAttr = (ApiExplorerSettingsAttribute)Attribute.GetCustomAttribute(val.ControllerTypeInfo, typeof(ApiExplorerSettingsAttribute));
                }

                if (myAttr != null)
                {
                    return myAttr.GroupName;
                }
                else
                {
                    return string.Empty;
                }
            }
            catch (Exception ex)
            {
                CommonLogger.LogInfo($"Unable to get action attributes for {JsonConvert.SerializeObject(apiDescription)} due to exception {ex}");
                return string.Empty;
            }
        }
    }
}
