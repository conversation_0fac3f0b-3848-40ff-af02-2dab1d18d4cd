﻿// <copyright file="ITokenVerifier.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Primitives;
using System;
using System.Linq;

namespace Microsoft.Aether.AE3pService.Extensions
{
    public static class HttpRequestExtensions
    {
        public static LogTelemetryClientType GetRequestClientType(this HttpRequest request)
        {
            var referer = request.GetReferer();
            var userAgent = request.GetUserAgent();

            if (!string.IsNullOrEmpty(referer))
            {
                return LogTelemetryClientType.Browser;
            }

            if (userAgent != null)
            {
                // Standard AzureML SDK/CLI is following this pattern
                if (userAgent.Contains("azureml-sdk-core/", StringComparison.InvariantCultureIgnoreCase))
                {
                    if (userAgent.Contains("azureml-cli/", StringComparison.InvariantCultureIgnoreCase))
                    {
                        return LogTelemetryClientType.Cli;
                    }

                    // SDK1.5 always has UA starts with Designer/1.0
                    if (userAgent.StartsWith("Designer/1.0", StringComparison.InvariantCultureIgnoreCase))
                    {
                        return LogTelemetryClientType.SDK_v1_5;
                    }

                    return LogTelemetryClientType.SDK;
                }

                // Current new Module SDK Implementation is following this pattern
                // GJD is using UserAgent as "FxVersion/4.700.21.6504 OSName/Linux OSVersion/Linux.4.15.0.112.generic.113.Ubuntu.SMP.Thu.Jul.9.23.41.39.UTC.2020 Designer.DesignerServiceClient/1.0.0.0", so add python condition as well for SDK
                if (userAgent.Contains("designerserviceclient/", StringComparison.InvariantCultureIgnoreCase) && userAgent.Contains("python/", StringComparison.InvariantCultureIgnoreCase))
                {
                    // SDK1.5 always has UA starts with Designer/1.0
                    if (userAgent.StartsWith("Designer/1.0", StringComparison.InvariantCultureIgnoreCase))
                    {
                        return LogTelemetryClientType.SDK_v1_5;
                    }

                    return LogTelemetryClientType.SDK;
                }

                if (userAgent.Contains("amlpipelinesapi10/", StringComparison.InvariantCultureIgnoreCase))
                {
                    return LogTelemetryClientType.SDK_v1;
                }

                if (userAgent.Contains("azure-logic-apps/", StringComparison.InvariantCultureIgnoreCase))
                {
                    return LogTelemetryClientType.Schedule;
                }

                if (userAgent.Contains("managementfrontend/", StringComparison.InvariantCultureIgnoreCase))
                {
                    if (userAgent.Contains("azureml-cli-v2/", StringComparison.InvariantCultureIgnoreCase))
                    {
                        return LogTelemetryClientType.Cli_v2;
                    }

                    if (userAgent.Contains("azure-ai-ml/", StringComparison.InvariantCultureIgnoreCase))
                    {
                        return LogTelemetryClientType.SDK_v2;
                    }

                    return LogTelemetryClientType.MFE;
                }

                if (userAgent.Contains("Designer.DesignerServiceClient", StringComparison.InvariantCultureIgnoreCase))
                {
                    return LogTelemetryClientType.GJD;
                }

                if (userAgent.Contains("Mozilla/", StringComparison.InvariantCultureIgnoreCase)
                    || userAgent.Contains("AppleWebKit/", StringComparison.InvariantCultureIgnoreCase)
                    || userAgent.Contains("Chrome/", StringComparison.InvariantCultureIgnoreCase)
                    || userAgent.Contains("Safari/", StringComparison.InvariantCultureIgnoreCase))
                {
                    return LogTelemetryClientType.Browser;
                }

                if (userAgent.Contains("StudioServiceLibrary", StringComparison.InvariantCultureIgnoreCase))
                {
                    return LogTelemetryClientType.Runner;
                }

                if (userAgent.Equals("bbaetherlibrary/default", StringComparison.InvariantCultureIgnoreCase))
                {
                    return LogTelemetryClientType.Test;
                }
            }

            return LogTelemetryClientType.Others;
        }

        public static string GetUserAgent(this HttpRequest request)
        {
            StringValues values;
            var userAgentPresent = request.Headers.TryGetValue("User-Agent", out values);
            if (!userAgentPresent)
            {
                userAgentPresent = request.Headers.TryGetValue("x-ms-user-agent", out values);
            }

            return userAgentPresent ? values.Single() : null;
        }

        public static string GetReferer(this HttpRequest request)
        {
            StringValues values;
            var refererPresent = request.Headers.TryGetValue("Referer", out values);
            return refererPresent ? values.Single() : null;
        }
    }
}
