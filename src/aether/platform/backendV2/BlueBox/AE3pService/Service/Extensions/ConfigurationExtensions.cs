﻿// <copyright file="ConfigurationExtensions.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using Microsoft.Extensions.Configuration;

namespace Microsoft.Aether.AE3pService.Extensions
{
    public static class ConfigurationExtensions
    {
        public static bool IsOtelAuditLogEnabled(this IConfiguration config)
        {
            return config.GetValue<bool>("OtelAuditLog:Enabled");
        }

        public static TimeSpan GetGenericTimeOut(this IConfiguration config)
        {
            return config.GetValue<TimeSpan>("Generic:Timeout");
        }

        public static string GetServiceName(this IConfiguration config)
        {
            return config.GetValue<string>("K8sBaseService:ServiceName");
        }
    }
}
