﻿// <copyright file="ITokenVerifier.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Aether.DataContracts;

namespace Microsoft.Aether.AE3pService
{
    internal class SubGraphAggregator
    {
        public SubGraphAggregator(string workspaceId)
        {
            _workspaceId = workspaceId;
        }

        //  Fetch the graph entities associated with all nested subgraphs in the given graph
        //  Return value is a dictionary of graph ID to GraphEntity for that subgraph
        public async Task<IDictionary<string, GraphEntity>> AggregateSubGraphsAsync(GraphEntity graph,
            IGraphStore graphStore)
        {
            var subGraphs = new ConcurrentDictionary<string, GraphEntity>();
            await AggregateSubGraphsAsync(graph, subGraphs, graphStore).ConfigureAwait(false);
            return subGraphs;
        }

        private async Task AggregateSubGraphsAsync(GraphEntity graph,
            ConcurrentDictionary<string, GraphEntity> subGraphs,
            IGraphStore graphStore)
        {
            await Task.WhenAll((graph.SubGraphNodes ?? Enumerable.Empty<GraphReferenceNode>()).Select(async node =>
            {
                if (subGraphs.TryAdd(node.GraphId, null))
                {
                    GraphEntity nestedGraph = await graphStore.GetEntityAsync(_workspaceId, node.GraphId).ConfigureAwait(false);

                    Task nestedDownloads = AggregateSubGraphsAsync(nestedGraph, subGraphs, graphStore);

                    subGraphs[node.GraphId] = nestedGraph;

                    await nestedDownloads;
                }
            }));
        }

        private readonly string _workspaceId;
    }
}
