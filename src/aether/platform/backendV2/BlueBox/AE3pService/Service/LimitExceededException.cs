﻿// <copyright file="LimitExceededException.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Net;

namespace Microsoft.Aether.AE3pService
{
    /// <summary>
    /// </summary>
    public class LimitExceededException : Exception
    {
        /// <summary>
        /// </summary>
        public HttpStatusCode StatusCode { get; private set; }

        /// <summary>
        /// </summary>
        public LimitExceededException(string message) : base(message)
        {
            StatusCode = HttpStatusCode.TooManyRequests;
        }
    }
}
