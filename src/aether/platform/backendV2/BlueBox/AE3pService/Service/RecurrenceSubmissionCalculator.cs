﻿// <copyright file="ITokenVerifier.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Linq;
using Microsoft.Aether.DataContracts;

namespace Microsoft.Aether.AE3pService
{
    internal class RecurrenceSubmissionCalculator
    {
        private const double MinutesPerMonth = 43800.0;
        private const double HoursPerMonth = 730.0;
        private const double DaysPerMonth = 30.0;
        private const double WeeksPerMonth = 4.3;

        public double CalculateAverageMonthlySubmissions(PipelineScheduleEntity schedule)
        {
            if (schedule.ScheduleType == ScheduleType.Recurrence)
            {
                Recurrence recurrence = schedule.Recurrence;
                switch (recurrence.Frequency)
                {
                    case Frequency.Minute:
                        return MinutesPerMonth / recurrence.Interval;
                    case Frequency.Hour:
                        return HoursPerMonth / recurrence.Interval;
                    case Frequency.Day:
                        return (DaysPerMonth / recurrence.Interval) * CalculateSubmissionsPerDay(recurrence.Schedule);
                    case Frequency.Week:
                        return (WeeksPerMonth / recurrence.Interval) * CalculateSubmissionsPerWeek(recurrence.Schedule);
                    case Frequency.Month:
                        return 1.0 / recurrence.Interval;
                    default:
                        throw new ArgumentException("Invalid Recurrence Frequency");
                }
            }
            else if (schedule.ScheduleType == ScheduleType.DataStore)
            {
                if (schedule.DataStoreTriggerInfo.PollingInterval <= 0)
                {
                    throw new ArgumentException("PollingInterval must be greater than 0.");
                }
                // for datastore trigger schedules, use the PollingInterval to determine how often a schedule may run
                return MinutesPerMonth / schedule.DataStoreTriggerInfo.PollingInterval;
            }
            else
            {
                throw new ArgumentException($"Unsupported ScheduleType: {schedule.ScheduleType}");
            }
        }

        private int CalculateSubmissionsPerDay(RecurrenceSchedule schedule)
        {
            if (schedule != null)
            {
                int hours = 1;
                int minutes = 1;
                if (schedule.Minutes?.Any() ?? false)
                {
                    minutes = schedule.Minutes.Count();
                }
                if (schedule.Hours?.Any() ?? false)
                {
                    hours = schedule.Hours.Count();
                }
                return minutes * hours;
            }
            return 1;
        }

        private int CalculateSubmissionsPerWeek(RecurrenceSchedule schedule)
        {
            if (schedule != null)
            {
                int days = 1;
                if (schedule.WeekDays?.Any() ?? false)
                {
                    days = schedule.Minutes.Count();
                }
                return days * CalculateSubmissionsPerDay(schedule);
            }
            return 1;
        }
    }
}
