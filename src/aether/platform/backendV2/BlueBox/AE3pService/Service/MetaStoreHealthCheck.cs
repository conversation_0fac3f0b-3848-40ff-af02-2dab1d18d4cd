﻿// <copyright file="MetaStoreHealthCheck.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Aether.Backend.BlueBox.MetaStoreV2.Client;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Microsoft.Aether.AE3pService
{
    internal class MetaStoreHealthCheck : IHealthCheck
    {
        private readonly IOptionsMonitor<MetaStoreConfig> _config;
        private readonly HttpClient _client;
        private const string KeepAlivePath = "/keepalive";
        private readonly ILogger<MetaStoreHealthCheck> _logger;

        public MetaStoreHealthCheck(IOptionsMonitor<MetaStoreConfig> config, HttpClient client, ILogger<MetaStoreHealthCheck> logger)
        {
            _config = config;
            _client = client;
            _logger = logger;
        }

        public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
        {
            try
            {
                string serverPath = string.Format("{0}{1}", _config.CurrentValue.MetaStoreUrl, KeepAlivePath);
                await _client.GetAsync(serverPath);
                return HealthCheckResult.Healthy();
            }
            catch (Exception exception)
            {
                _logger.LogWarning(exception, "MetaStoreHealthCheck failed: {exception}", exception);
                return new HealthCheckResult(context.Registration.FailureStatus, exception: exception);
            }
        }
    }
}
