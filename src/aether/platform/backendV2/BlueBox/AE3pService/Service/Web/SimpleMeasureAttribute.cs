﻿// <copyright file="SimpleMeasureAttribute.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Collections.Concurrent;
using System.Diagnostics;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.RelInfra.Instrumentation;

namespace Microsoft.Aether.AE3pService
{
    ///<summary>
    ///</summary>
    public class SimpleMeasureAttribute : IActionFilter
    {
        private readonly ConcurrentDictionary<int, Stopwatch> _callLatencies = new ConcurrentDictionary<int, Stopwatch>();
        private readonly CounterManager _counterManager;

        ///<summary>
        ///</summary>
        public SimpleMeasureAttribute(CounterManager counterManager)
        {
            _counterManager = counterManager;
        }

        ///<summary>
        ///</summary>
        public void OnActionExecuting(ActionExecutingContext actionContext)
        {
            string actionName = actionContext.ActionDescriptor.RouteValues["Action"];
            _counterManager.GetRateCounter($"{actionName}Rate").Increment();
            _callLatencies.TryAdd(actionContext.HttpContext.Request.GetHashCode(), Stopwatch.StartNew());
        }

        ///<summary>
        ///</summary>
        public void OnActionExecuted(ActionExecutedContext actionExecutedContext)
        {
            string actionName = actionExecutedContext.ActionDescriptor.RouteValues["Action"];
            if (actionExecutedContext.Exception != null)
            {
                _counterManager.GetRateCounter($"{actionName}FailureRate").Increment();
            }
            else
            {
                _counterManager.GetRateCounter($"{actionName}SuccessRate").Increment();
            }

            if (_callLatencies.TryRemove(actionExecutedContext.HttpContext.Request.GetHashCode(), out Stopwatch sw))
            {
                _counterManager.GetLatencyCounter($"{actionName}Latency").Set(sw.ElapsedMilliseconds);
            }
        }
    }
}
