﻿<Project Sdk="Microsoft.NET.Sdk" DefaultTargets="Publish;Build">
  <PropertyGroup>
    <PublishDir>$(BaseTargetDir)\app\aether\bluebox\AE3pService</PublishDir>
    <TargetFramework>net8.0</TargetFramework>
    <DocumentationFile>$(OutputPath)\AE3pService.xml</DocumentationFile>
    <ResolveAssemblyWarnOrErrorOnTargetArchitectureMismatch>None</ResolveAssemblyWarnOrErrorOnTargetArchitectureMismatch>
    <NoWarn>NU1608;NU1605</NoWarn>
    <MSBuildWarningsAsMessages>CA5351</MSBuildWarningsAsMessages>
    <HighEntropyVA>true</HighEntropyVA>
    <CodeAnalysisRuleSet>..\..\..\..\ae3p.analysis.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <ItemGroup>
    <None Include="..\..\..\shared\Scripts\start.sh">
      <Link>start.sh</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="..\..\CA\*.cer">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="Dockerfile">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.Azure.Workflows.WebJobs.Extension">
      <ExcludeAssets>contentFiles</ExcludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.IdentityModel.Authorization.Azure" NoWarn="NU1701" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Versioning" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Versioning.ApiExplorer" />
    <PackageReference Include="Microsoft.Extensions.Caching.Abstractions" />
    <PackageReference Include="Microsoft.Extensions.Http" />
    <PackageReference Include="StackExchange.Redis" />
    <PackageReference Include="Swashbuckle.AspNetCore" />
    <PackageReference Include="Swashbuckle.AspNetCore.Annotations" />
    <PackageReference Include="Swashbuckle.AspNetCore.Newtonsoft" />
    <PackageReference Include="System.Net.Security" />
    <PackageReference Include="YamlDotNet" />
    <PackageReference Include="Scrutor" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="$(SrcRoot)\AccountRP\Contracts\AccountRP.Contracts.csproj" />
    <ProjectReference Include="$(SrcRoot)\Common\Caching\Common.Caching.csproj" />
    <ProjectReference Include="$(SrcRoot)\Common\Core\Common.Core.csproj" />
    <ProjectReference Include="$(SrcRoot)\Common\ServiceBus\Common.ServiceBus.csproj" />
    <ProjectReference Include="$(SrcRoot)\Common\Startup\Common.Startup.csproj" />
    <ProjectReference Include="$(SrcRoot)\Common\WebApi\Common.WebApi.csproj" />
    <ProjectReference Include="$(SrcRoot)\Common\WebApi.Audit.Otel\Common.WebApi.Audit.Otel.csproj" />
    <ProjectReference Include="$(SrcRoot)\Common\WebApi.Authorization\Common.WebApi.Authorization.csproj" />
    <ProjectReference Include="$(SrcRoot)\Common\WebApi.TeamAccounts\Common.WebApi.TeamAccounts.csproj" />
    <ProjectReference Include="$(SrcRoot)\Pipeline\Common\Pipeline.Services.Common.csproj" />

    <ProjectReference Include="..\..\..\..\RelInfra\RelInfra.Common.Library\src\Common\RelInfra.Common.csproj" />
    <ProjectReference Include="..\..\..\..\RelInfra\RelInfra.Common.Library\src\Extensions\RelInfra.Extensions.csproj" />
    <ProjectReference Include="..\..\..\..\RelInfra\RelInfra.Instrumentation.Library\src\Microsoft.RelInfra.Instrumentation.csproj" />
    <ProjectReference Include="..\..\..\..\ContractProcessing\ContractProcessing.csproj" />
    <ProjectReference Include="..\..\..\CloudManager\ExperimentSubmitter.Client\Microsoft.Aether.CloudManager.ExperimentSubmitter.Client.csproj" />
    <ProjectReference Include="..\..\..\MetaStore\MetaStore.Service.Common\MetaStore.Service.Common.csproj" />
    <ProjectReference Include="..\..\..\shared\Microsoft.Aether.BackendCommon\Microsoft.Aether.BackendCommon.csproj" />
    <ProjectReference Include="..\..\..\shared\Microsoft.Aether.DataContract\Microsoft.Aether.DataContracts.csproj" />
    <ProjectReference Include="..\..\..\shared\Microsoft.Aether.S2S.Common\Microsoft.Aether.S2S.Common.csproj" />
    <ProjectReference Include="..\..\AetherK8SBaseService\Microsoft.Aether.BlueBox.AetherK8SBaseService.csproj" />
    <ProjectReference Include="..\..\AetherK8SCommon\Microsoft.Aether.BlueBox.AetherK8SCommon.csproj" />
    <ProjectReference Include="..\..\DataStoreClient\Microsoft.Aether.BlueBox.DataStoreClient\Microsoft.Aether.BlueBox.DataStoreClient.csproj" />
    <ProjectReference Include="..\..\DistributedTracing\Microsoft.Aether.BlueBox.DistributedTracing\Microsoft.Aether.BlueBox.DistributedTracing.csproj" />
    <ProjectReference Include="..\..\LegacyRunConfig\Microsoft.Aether.BlueBox.LegacyRunConfigBuilder.csproj" />
    <ProjectReference Include="..\..\MetaStore\MetaStoreProxy.Contract\MetaStoreProxy.Contract.csproj" />
    <ProjectReference Include="..\..\MetaStore\MetaStoreV2.Client\MetaStoreV2.Client.csproj" />
    <ProjectReference Include="..\..\Microsoft.Aether.BlueBox.Web.Common\Microsoft.Aether.BlueBox.Web.Common.csproj" />
    <ProjectReference Include="..\..\OboTokenServiceClient\Client\Microsoft.Aether.BlueBox.OboTokenServiceClient.csproj" />
    <ProjectReference Include="..\..\RepositoryResourcesClient\Client\Microsoft.Aether.BlueBox.RepositoryResourcesClient.csproj" />
    <ProjectReference Include="..\..\RepositoryResourcesClient\Contracts\Microsoft.Aether.BlueBox.RepositoryResourcesContracts.csproj" />
    <ProjectReference Include="..\..\S2S.Common\Microsoft.Aether.BlueBox.S2S.Common\Microsoft.Aether.BlueBox.S2S.Common.csproj" />
    <ProjectReference Include="..\..\SchedulerClient\Microsoft.Aether.BlueBox.SchedulerClient.csproj" />
    <ProjectReference Include="..\..\WorkspaceResourcesClient\Microsoft.Aether.BlueBox.WorkspaceResourcesClient\Microsoft.Aether.BlueBox.WorkspaceResourcesClient.csproj" />
    <ProjectReference Include="..\..\CredentialServiceClient\Microsoft.Aether.BlueBox.CredentialServiceClient\Microsoft.Aether.BlueBox.CredentialServiceClient.csproj" />
    <ProjectReference Include="..\DataContractsValidation\DataContractsValidation.csproj" />
    <ProjectReference Include="..\DataContractConverter\Microsoft.Aether.AEVA.DataContractConverter.csproj" />
    <ProjectReference Include="..\DataContracts\Microsoft.Aether.AEVA.DataContracts.csproj" />
    <ProjectReference Include="..\..\RunHistoryClient\src\client\Microsoft.Aether.BlueBox.RunHistoryClient.csproj" />
    <ProjectReference Include="..\..\SnapshotClient\Microsoft.Aether.BlueBox.SnapshotClient\Microsoft.Aether.BlueBox.SnapshotClient.csproj" />
    <ProjectReference Include="..\..\SnapshotClient\Microsoft.Aether.BlueBox.SnapshotContracts\Microsoft.Aether.BlueBox.SnapshotContracts.csproj" />
    <ProjectReference Include="..\..\MmsServiceClient\Microsoft.Aether.BlueBox.MmsServiceClient\Microsoft.Aether.BlueBox.MmsServiceClient.csproj" />
  </ItemGroup>
</Project>
