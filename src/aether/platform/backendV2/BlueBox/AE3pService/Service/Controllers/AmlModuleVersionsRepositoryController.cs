﻿// <copyright file="AmlModuleVersionsRepositoryController.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Collections.Generic;
using System.Threading.Tasks;
using Common.WebApi.Exceptions;
using Microsoft.Aether.AE3pService.Exceptions;
using Microsoft.Aether.AEVA.DataContracts;
using Microsoft.Aether.BackendCommon;
using Microsoft.Aether.BackendCommon.AzureMonitor;
using Microsoft.Aether.BlueBox.RepositoryResourcesClient;
using Microsoft.Aether.BlueBox.Web.Common;
using Microsoft.Aether.DataContracts.Entity;
using Microsoft.Aether.DataContracts.Error;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using Microsoft.RelInfra.Instrumentation.Logging;
using Microsoft.MachineLearning.Common.WebApi.Audit.Otel.Attribute;
using OpenTelemetry.Audit.Geneva;

namespace Microsoft.Aether.AE3pService
{
    ///<summary>
    /// AmlModuleVersionsWorkspaceController class
    ///</summary>
    [ApiVersion("1.0")]
    [Route(ApiEndpoints.AetherRepositoryApi)]
    [Route(ApiEndpoints.AzureMlPipelinesRepositoryApi)]
    [EnableCors(policyName: DefaultCorsPolicyName)]
    [ApiExplorerSettings(GroupName = "private")]

    public class AmlModuleVersionsRepositoryController : AmlModuleVersionsBaseController
    {
        private readonly IRepositoryResourcesClient _repositoryClient;

        ///<summary>
        /// ModulesController class constructor
        ///</summary>
        public AmlModuleVersionsRepositoryController(IStoreCollection stores, IContextProvider operationContextProvider, IWorkspaceResourcesCache workspaceResourcesCache, IRepositoryResourcesClient repositoryClient, IAzureMonitorStatusLogger azureMonitorStatusLogger)
            : base(stores, operationContextProvider, workspaceResourcesCache, azureMonitorStatusLogger)
        {
            _repositoryClient = repositoryClient;
        }

        ///<summary>
        /// CreateAzureMLModuleVersionAsync
        ///</summary>
        /// <param name="repositoryId">The repository id</param>
        /// <param name="creationInfo">The module creation info</param>
        /// <returns>returns a Module Entity</returns>
        [Authorize(Policy = "AllowedRepositoryUsers")]
        [HttpPost]
        [Route("type/azuremlmoduleversions")]
        [OtelAudit(OperationType.Create, AccessLevel = "AllowedRepositoryUsers")]
        public async Task<AzureMLModuleVersion> CreateAzureMLModuleVersionAsync(
            string repositoryId,
            [FromBody] AzureMLModuleVersionCreationInfo creationInfo)
        {
            var repositoryIdentity = await GetRepository(repositoryId).ConfigureAwait(false);
            return await base.CreateAzureMLModuleVersionAsync(repositoryIdentity.GetWorkspaceIdentity(), creationInfo).ConfigureAwait(false);
        }

        ///<summary>
        /// Create an AzureMLModuleVersion which corresponds to a subgraph.  This will create the graph in the
        /// graph store and then create an AML module version (with ModuleEntity) which points to that graph.
        ///</summary>
        /// <param name="repositoryId">The repository id</param>
        /// <param name="creationInfo">The subgraph creation info.  This includes the graph to use for the subgraph.</param>
        /// <returns>returns a Module Entity</returns>
        [Authorize(Policy = "AllowedRepositoryUsers")]
        [HttpPost]
        [Route("type/azuremlmoduleversions/subgraph")]
        [OtelAudit(OperationType.Create, AccessLevel = "AllowedRepositoryUsers")]
        public async Task<AzureMLModuleVersion> CreateSubGraphModuleVersionAsync(
            string repositoryId,
            [FromBody] SubGraphVersionCreationInfo creationInfo)
        {
            var repositoryIdentity = await GetRepository(repositoryId).ConfigureAwait(false);

            return await base.CreateSubGraphModuleVersionAsync(repositoryIdentity.GetWorkspaceIdentity(), creationInfo).ConfigureAwait(false);
        }

        ///<summary>
        /// UpdateAzureMLModuleVersionAsync
        ///</summary>
        /// <param name="repositoryId">The repository id</param>
        /// <param name="id">The module id</param>
        /// <param name="updated">The Module</param>
        /// <returns>returns a ModuleVersion Entity</returns>
        [Authorize(Policy = "AllowedRepositoryUsers")]
        [HttpPut]
        [Route("type/azuremlmoduleversions/objectId/{id}")]
        [OtelAudit(OperationType.Update, AccessLevel = "AllowedRepositoryUsers")]
        public async Task<AzureMLModuleVersion> UpdateAzureMLModuleVersionAsync(
            string repositoryId,
            string id,
            [FromBody] AzureMLModuleVersion updated)
        {
            var repositoryIdentity = await GetRepository(repositoryId).ConfigureAwait(false);
            return await base.UpdateAzureMLModuleVersionAsync(repositoryIdentity.GetWorkspaceIdentity(), id, updated).ConfigureAwait(false);
        }

        ///<summary>
        /// GetAzureMLModuleVersionAsync
        ///</summary>
        /// <param name="repositoryId">The repository id</param>
        /// <param name="id">The module id</param>
        /// <returns>returns a Module</returns>
        [Authorize(Policy = "AllowedRepositoryUsers")]
        [HttpGet]
        [Route("type/azuremlmoduleversions/objectId/{id}")]
        [OtelAudit(OperationType.Read, AccessLevel = "AllowedRepositoryUsers")]
        public async Task<AzureMLModuleVersion> GetAzureMLModuleVersionAsync(
            string repositoryId,
            string id)
        {
            var repositoryIdentity = await GetRepository(repositoryId).ConfigureAwait(false);
            return await base.GetAzureMLModuleVersionAsync(repositoryIdentity.GetWorkspaceIdentity(), id).ConfigureAwait(false);
        }

        ///<summary>
        /// GetAzureMLModuleVersionByHashAsync
        ///</summary>
        /// <param name="repositoryId">The repository id</param>
        /// <param name="identifierHash">The module identifierHash</param>
        /// <returns>returns a AzureMLModuleVersion</returns>
        [Authorize(Policy = "AllowedRepositoryUsers")]
        [HttpGet]
        [Route("type/azuremlmoduleversions/hash/{identifierHash}")]
        [OtelAudit(OperationType.Read, AccessLevel = "AllowedRepositoryUsers")]
        public async Task<AzureMLModuleVersion> GetAzureMLModuleVersionByHashAsync(
            string repositoryId,
            string identifierHash)
        {
            var repositoryIdentity = await GetRepository(repositoryId).ConfigureAwait(false);
            return await base.GetAzureMLModuleVersionByHashAsync(repositoryIdentity.GetWorkspaceIdentity(), identifierHash).ConfigureAwait(false);
        }

        ///<summary>
        /// BulkGetAzureMLModuleVersionAsync
        ///</summary>
        /// <param name="repositoryId">The repository id</param>
        /// <param name="ids">The module ids</param>
        /// <returns>returns a AzureMLModuleVersion list</returns>
        [Authorize(Policy = "AllowedRepositoryUsers")]
        [HttpPost]
        [Route("type/azuremlmoduleversions/bulk")]
        [OtelAudit(OperationType.Read, AccessLevel = "AllowedRepositoryUsers")]
        public async Task<IEnumerable<AzureMLModuleVersion>> BulkGetAzureMLModuleVersionAsync(
            string repositoryId,
            [FromBody] string[] ids)
        {
            var repositoryIdentity = await GetRepository(repositoryId).ConfigureAwait(false);
            return await base.BulkGetAzureMLModuleVersionAsync(repositoryIdentity.GetWorkspaceIdentity(), ids).ConfigureAwait(false);
        }

        // TODO (ankarlof): Temporary fetcher of the repository pending the ContainerIdentityProvider.
        private async Task<RepositoryIdentity> GetRepository(string repositoryId)
        {
            try
            {
                var repositoryMetadata = await _repositoryClient.GetRepositoryByIdAsync(repositoryId).ConfigureAwait(false);
                return repositoryMetadata.GetRepositoryIdentity();
            }
            catch (ResourceNotFoundException e)
            {
                CommonLogger.LogEntityError(repositoryId, "Unable to find the repository with the following id: {repositoryId}", repositoryId);
                throw ExceptionFrameworkMapper.MapToBaseException(e, new RepositoryNotFound(ExceptionFrameworkMapper.MessageFormat(e)), repositoryId);
            }
        }
    }
}
