﻿// <copyright file="AE365ModuleVersionsController.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.Aether.AE3pService.Exceptions;
using Microsoft.Aether.AEVA.DataContractConverter;
using Microsoft.Aether.AEVA.DataContracts;
using Microsoft.Aether.BackendCommon;
using Microsoft.Aether.BackendCommon.AzureMonitor;
using Microsoft.Aether.BlueBox.Web.Common;
using Microsoft.Aether.DataContracts.Entity;
using Microsoft.Aether.DataContracts.Error;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using Microsoft.MachineLearning.AccountRP.Contracts;
using Microsoft.MachineLearning.Common.Core.Exceptions;
using Microsoft.MachineLearning.Common.WebApi.Audit.Otel.Attribute;
using Microsoft.MachineLearning.Common.WebApi.Authorization.Attribute;
using Microsoft.RelInfra.Instrumentation.Logging;
using AEVAModuleEntity = Microsoft.Aether.AEVA.DataContracts.ModuleEntity;
using OpenTelemetry.Audit.Geneva;

namespace Microsoft.Aether.AE3pService
{
    ///<summary>
    /// AE365ModuleVersionsController class
    ///</summary>
    [ApiVersion("1.0")]
    [Route(ApiEndpoints.AetherApi)]
    [Route(ApiEndpoints.AzureMlPipelinesApi)]
    [EnableCors(policyName: DefaultCorsPolicyName)]
    [ApiExplorerSettings(IgnoreApi = true)]
    public class AE365ModuleVersionsController : BaseController
    {
        ///<summary>
        /// ModulesController class constructor
        ///</summary>
        public AE365ModuleVersionsController(IStoreCollection stores, IContextProvider operationContextProvider, IWorkspaceResourcesCache workspaceResourcesCache, IAzureMonitorStatusLogger azureMonitorStatusLogger)
            : base(stores, operationContextProvider, workspaceResourcesCache)
        {
            // inject stuff
            _amlModuleVersionsBaseController = new AmlModuleVersionsBaseController(stores, operationContextProvider, workspaceResourcesCache, azureMonitorStatusLogger);
            _modulesBaseController = new ModulesBaseController(stores, operationContextProvider, workspaceResourcesCache, null, azureMonitorStatusLogger);
        }

        private AmlModuleVersionsBaseController _amlModuleVersionsBaseController;
        private ModulesBaseController _modulesBaseController;

        ///<summary>
        /// CreateAE365ModuleVersionAsync
        ///</summary>
        /// <param name="subscriptionId">The subscription id</param>
        /// <param name="resourceGroupName">The resource group name</param>
        /// <param name="workspaceName">The workspace name</param>
        /// <param name="creationInfo">The module creation info</param>
        /// <returns>returns a Module Entity</returns>
        [ActionAuthorize(AllOperations.Actions.WorkspacesModulesWrite)]
        [Authorize(Policy = "Contributor")]
        [HttpPost]
        [Route("AE365ModuleVersions")]
        [OtelAudit(OperationType.Create, AccessLevel = "Contributor")]
        public async Task<AzureMLModuleVersion> CreateAE365ModuleVersionAsync(
            string subscriptionId,
            string resourceGroupName,
            string workspaceName,
            [FromBody] AE365ModuleVersionCreationInfo creationInfo)
        {
            try
            {
                var workspaceIdentity = new WorkspaceIdentity()
                {
                    SubscriptionId = subscriptionId,
                    ResourceGroupName = resourceGroupName,
                    WorkspaceName = workspaceName,
                    WorkspaceId = await GetWorkspaceIdentityIdAsync(subscriptionId, resourceGroupName, workspaceName, _operationContextProvider.GetCreatedByObject()),
                };

                if (_studioBuiltinModulePrefixRegex.IsMatch(creationInfo.Name))
                {
                    throw new BaseException(new InvalidArgumentWithDetail().ToBaseError(nameof(creationInfo.Name), nameof(creationInfo.Name), string.Format(AE3pErrorMessages.AE365ModuleUsingPreservedPrefix, _studioBuiltinModulePrefix)));
                }

                return await _amlModuleVersionsBaseController.CreateModuleVersionHelperAsync(
                   _stores.GetAmlModuleStore(User, workspaceIdentity),
                   _stores.GetModuleStore(User, workspaceIdentity),
                   _stores.GetDataTypeStore(User, workspaceIdentity),
                   workspaceIdentity,
                   creationInfo, _operationContextProvider.GetCreatedByObject());
            }
            catch (Exception e)
            {
                var exp = ExceptionFrameworkMapper.MapToBaseException(e, new ErrorInCreateAE365ModuleVersion(ExceptionFrameworkMapper.MessageFormat(e)));
                ControllerHelper.ErrorLogWithSafeMessageJudging(exp, subscriptionId);
                throw exp;
            }
        }

        ///<summary>
        /// UpdateAE365ModuleVersionAsync
        ///</summary>
        /// <param name="subscriptionId">The subscription id</param>
        /// <param name="resourceGroupName">The resource group name</param>
        /// <param name="workspaceName">The workspace name</param>
        /// <param name="id">The module id</param>
        /// <param name="updated">The Module</param>
        /// <returns>returns a ModuleVersion Entity</returns>
        [ActionAuthorize(AllOperations.Actions.WorkspacesModulesWrite)]
        [Authorize(Policy = "Contributor")]
        [HttpPut]
        [Route("AE365ModuleVersions/{id}")]
        [OtelAudit(OperationType.Update, AccessLevel = "Contributor")]
        public async Task<AzureMLModuleVersion> UpdateAE365ModuleVersionAsync(
            string subscriptionId,
            string resourceGroupName,
            string workspaceName,
            string id,
            [FromBody] AzureMLModuleVersion updated)
        {
            try
            {
                if (id != updated.Data.Id)
                {
                    throw new BaseException(new InvalidArgumentWithDetail().ToBaseError(nameof(id), nameof(id), string.Format(AE3pErrorMessages.AE365ModuleUpdateWithDifferentId, id, updated.Data.Id)));
                }
                var m = ContractConverter.Convert<AEVAModuleEntity, DataContracts.ModuleEntity>(updated.Data);

                var store = _stores.GetModuleStore(User,
                    new WorkspaceIdentity()
                    {
                        SubscriptionId = subscriptionId,
                        ResourceGroupName = resourceGroupName,
                        WorkspaceName = workspaceName,
                        WorkspaceId = await GetWorkspaceIdentityIdAsync(subscriptionId, resourceGroupName, workspaceName, _operationContextProvider.GetCreatedByObject()),
                    });

                var moduleEntity = await _modulesBaseController.UpdateModuleAsync(m, store);
                CommonLogger.LogEntityInfo(moduleEntity.Id, $"The moduleVersion was updated successfully. UploadState={moduleEntity.UploadState}");

                var mv = ContractConverter.Convert<DataContracts.ModuleEntity, AzureMLModuleVersion>(moduleEntity);
                mv.ModuleId = moduleEntity.FamilyId;
                return mv;
            }
            catch (Exception e)
            {
                var exp = ExceptionFrameworkMapper.MapToBaseException(e, new ErrorInUpdateAE365ModuleVersion(ExceptionFrameworkMapper.MessageFormat(e)));
                ControllerHelper.ErrorLogWithSafeMessageJudging(exp, id);
                throw exp;
            }
        }

        ///<summary>
        /// GetAE365ModuleAsync
        ///</summary>
        /// <param name="subscriptionId">The subscription id</param>
        /// <param name="resourceGroupName">The resource group name</param>
        /// <param name="workspaceName">The workspace name</param>
        /// <param name="id">The module id</param>
        /// <returns>returns a Module</returns>
        [ActionAuthorize(AllOperations.Actions.WorkspacesModulesRead)]
        [Authorize(Policy = "Reader")]
        [HttpGet]
        [Route("AE365ModuleVersions/{id}")]
        [OtelAudit(OperationType.Read, AccessLevel = "Reader")]
        public async Task<AzureMLModuleVersion> GetAE365ModuleAsync(
            string subscriptionId,
            string resourceGroupName,
            string workspaceName,
            string id)
        {
            try
            {
                var amlWorkspaceIdentity = new WorkspaceIdentity()
                {
                    SubscriptionId = subscriptionId,
                    ResourceGroupName = resourceGroupName,
                    WorkspaceName = workspaceName,
                    WorkspaceId = await GetWorkspaceIdentityIdAsync(subscriptionId, resourceGroupName, workspaceName, _operationContextProvider.GetCreatedByObject()),
                };

                DataContracts.ModuleEntity module = await _stores.GetModuleStore(User, amlWorkspaceIdentity).GetEntityAsync(DefaultWorkspaceId, id);
                HashSet<string> dataTypeNames = await ControllerHelper.GetAllDataTypeNamesSetAsync(_stores.GetDataTypeStore(User, amlWorkspaceIdentity), DefaultWorkspaceId);
                return ModulesBaseController.BuildModule<AzureMLModuleVersion>(module, dataTypeNames);
            }
            catch (Exception e)
            {
                var exp = ExceptionFrameworkMapper.MapToBaseException(e, new ErrorInGetAE365ModuleVersion(ExceptionFrameworkMapper.MessageFormat(e)));
                ControllerHelper.ErrorLogWithSafeMessageJudging(exp, id);
                throw exp;
            }
        }
    }
}
