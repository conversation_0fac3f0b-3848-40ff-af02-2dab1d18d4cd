﻿// <copyright file="AmlModuleVersionsWorkspaceController.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.Aether.AEVA.DataContracts;
using Microsoft.Aether.BackendCommon;
using Microsoft.Aether.BackendCommon.AzureMonitor;
using Microsoft.Aether.BlueBox.Web.Common;
using Microsoft.Aether.DataContracts.Entity;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using Microsoft.MachineLearning.AccountRP.Contracts;
using Microsoft.MachineLearning.Common.Core.Attributes;
using Microsoft.MachineLearning.Common.WebApi.Audit.Otel.Attribute;
using Microsoft.MachineLearning.Common.WebApi.Authorization.Attribute;
using OpenTelemetry.Audit.Geneva;

namespace Microsoft.Aether.AE3pService
{
    ///<summary>
    /// AmlModuleVersionsWorkspaceController class
    ///</summary>
    [ApiVersion("1.0")]
    [Route(ApiEndpoints.AetherApi)]
    [Route(ApiEndpoints.AzureMlPipelinesApi)]
    [EnableCors(policyName: DefaultCorsPolicyName)]
    [ApiExplorerSettings(GroupName = "private")]

    public class AmlModuleVersionsWorkspaceController : AmlModuleVersionsBaseController
    {
        ///<summary>
        /// ModulesController class constructor
        ///</summary>
        public AmlModuleVersionsWorkspaceController(IStoreCollection stores, IContextProvider operationContextProvider, IWorkspaceResourcesCache workspaceResourcesCache, IAzureMonitorStatusLogger azureMonitorStatusLogger)
            : base(stores, operationContextProvider, workspaceResourcesCache, azureMonitorStatusLogger)
        {
            // inject stuff
        }

        ///<summary>
        /// CreateAzureMLModuleVersionAsync
        ///</summary>
        /// <param name="subscriptionId">The subscription id</param>
        /// <param name="resourceGroupName">The resource group name</param>
        /// <param name="workspaceName">The workspace name</param>
        /// <param name="creationInfo">The module creation info</param>
        /// <returns>returns a Module Entity</returns>
        [ActionAuthorize(AllOperations.Actions.WorkspacesModulesWrite)]
        [Authorize(Policy = "Contributor")]
        [HttpPost]
        [Route("AzureMLModuleVersions")]
        [OtelAudit(OperationType.Create, AccessLevel = "Contributor")]
        [ScenarioName(ScenarioName.CreateAzureMLModuleVersion)]
        public async Task<AzureMLModuleVersion> CreateAzureMLModuleVersionAsync(
            string subscriptionId,
            string resourceGroupName,
            string workspaceName,
            [FromBody] AzureMLModuleVersionCreationInfo creationInfo)
        {
            var workspaceIdentity = new WorkspaceIdentity
            {
                SubscriptionId = subscriptionId,
                ResourceGroupName = resourceGroupName,
                WorkspaceName = workspaceName,
                WorkspaceId = await GetWorkspaceIdentityIdAsync(subscriptionId, resourceGroupName, workspaceName, _operationContextProvider.GetCreatedByObject())
            };

            return await base.CreateAzureMLModuleVersionAsync(workspaceIdentity, creationInfo).ConfigureAwait(false);
        }

        ///<summary>
        /// Create an AzureMLModuleVersion which corresponds to a subgraph.  This will create the graph in the
        /// graph store and then create an AML module version (with ModuleEntity) which points to that graph.
        ///</summary>
        /// <param name="subscriptionId">The subscription id</param>
        /// <param name="resourceGroupName">The resource group name</param>
        /// <param name="workspaceName">The workspace name</param>
        /// <param name="creationInfo">The subgraph creation info.  This includes the graph to use for the subgraph.</param>
        /// <returns>returns a Module Entity</returns>
        [ActionAuthorize(AllOperations.Actions.WorkspacesModulesWrite)]
        [Authorize(Policy = "Contributor")]
        [HttpPost]
        [Route("AzureMLModuleVersions/SubGraph")]
        [OtelAudit(AccessLevel = "Contributor")]
        [ScenarioName(ScenarioName.CreateAzureMLModuleVersion)]
        public async Task<AzureMLModuleVersion> CreateSubGraphModuleVersionAsync(
            string subscriptionId,
            string resourceGroupName,
            string workspaceName,
            [FromBody] SubGraphVersionCreationInfo creationInfo)
        {
            var workspaceIdentity = new WorkspaceIdentity
            {
                SubscriptionId = subscriptionId,
                ResourceGroupName = resourceGroupName,
                WorkspaceName = workspaceName,
                WorkspaceId = await GetWorkspaceIdentityIdAsync(subscriptionId, resourceGroupName, workspaceName, _operationContextProvider.GetCreatedByObject())
            };

            return await base.CreateSubGraphModuleVersionAsync(workspaceIdentity, creationInfo).ConfigureAwait(false);
        }

        ///<summary>
        /// UpdateAzureMLModuleVersionAsync
        ///</summary>
        /// <param name="subscriptionId">The subscription id</param>
        /// <param name="resourceGroupName">The resource group name</param>
        /// <param name="workspaceName">The workspace name</param>
        /// <param name="id">The module id</param>
        /// <param name="updated">The Module</param>
        /// <returns>returns a ModuleVersion Entity</returns>
        [ActionAuthorize(AllOperations.Actions.WorkspacesModulesWrite)]
        [Authorize(Policy = "Contributor")]
        [HttpPut]
        [Route("AzureMLModuleVersions/{id}")]
        [OtelAudit(AccessLevel = "Contributor")]
        [ScenarioName(ScenarioName.UpdateAzureMLModuleVersion)]
        public async Task<AzureMLModuleVersion> UpdateAzureMLModuleVersionAsync(
            string subscriptionId,
            string resourceGroupName,
            string workspaceName,
            string id,
            [FromBody] AzureMLModuleVersion updated)
        {
            var workspaceIdentity = new WorkspaceIdentity
            {
                SubscriptionId = subscriptionId,
                ResourceGroupName = resourceGroupName,
                WorkspaceName = workspaceName,
                WorkspaceId = await GetWorkspaceIdentityIdAsync(subscriptionId, resourceGroupName, workspaceName, _operationContextProvider.GetCreatedByObject())
            };

            return await base.UpdateAzureMLModuleVersionAsync(workspaceIdentity, id, updated).ConfigureAwait(false);
        }

        ///<summary>
        /// GetAzureMLModuleVersionAsync
        ///</summary>
        /// <param name="subscriptionId">The subscription id</param>
        /// <param name="resourceGroupName">The resource group name</param>
        /// <param name="workspaceName">The workspace name</param>
        /// <param name="id">The module id</param>
        /// <returns>returns a Module</returns>
        [ActionAuthorize(AllOperations.Actions.WorkspacesModulesRead)]
        [Authorize(Policy = "Reader")]
        [HttpGet]
        [Route("AzureMLModuleVersions/{id}")]
        [OtelAudit(AccessLevel = "Reader")]
        [ScenarioName(ScenarioName.GetAzureMLModuleVersion)]
        public async Task<AzureMLModuleVersion> GetAzureMLModuleVersionAsync(
            string subscriptionId,
            string resourceGroupName,
            string workspaceName,
            string id)
        {
            var workspaceIdentity = new WorkspaceIdentity
            {
                SubscriptionId = subscriptionId,
                ResourceGroupName = resourceGroupName,
                WorkspaceName = workspaceName,
                WorkspaceId = await GetWorkspaceIdentityIdAsync(subscriptionId, resourceGroupName, workspaceName, _operationContextProvider.GetCreatedByObject())
            };

            return await base.GetAzureMLModuleVersionAsync(workspaceIdentity, id).ConfigureAwait(false);
        }

        ///<summary>
        /// GetAzureMLModuleVersionByHashAsync
        ///</summary>
        /// <param name="subscriptionId">The subscription id</param>
        /// <param name="resourceGroupName">The resource group name</param>
        /// <param name="workspaceName">The workspace name</param>
        /// <param name="identifierHash">The module identifierHash</param>
        /// <returns>returns a AzureMLModuleVersion</returns>
        [ActionAuthorize(AllOperations.Actions.WorkspacesModulesRead)]
        [Authorize(Policy = "Reader")]
        [HttpGet]
        [Route("AzureMLModuleVersions/Hash/{identifierHash}")]
        [ApiExplorerSettings(GroupName = "private")]
        [OtelAudit(AccessLevel = "Reader")]
        [ScenarioName(ScenarioName.GetAzureMLModuleVersion)]
        public async Task<AzureMLModuleVersion> GetAzureMLModuleVersionByHashAsync(
            string subscriptionId,
            string resourceGroupName,
            string workspaceName,
            string identifierHash)
        {
            var workspaceIdentity = new WorkspaceIdentity
            {
                SubscriptionId = subscriptionId,
                ResourceGroupName = resourceGroupName,
                WorkspaceName = workspaceName,
                WorkspaceId = await GetWorkspaceIdentityIdAsync(subscriptionId, resourceGroupName, workspaceName, _operationContextProvider.GetCreatedByObject())
            };

            return await base.GetAzureMLModuleVersionByHashAsync(workspaceIdentity, identifierHash).ConfigureAwait(false);
        }

        ///<summary>
        /// BulkGetAzureMLModuleVersionAsync
        ///</summary>
        /// <param name="subscriptionId">The subscription id</param>
        /// <param name="resourceGroupName">The resource group name</param>
        /// <param name="workspaceName">The workspace name</param>
        /// <param name="ids">The module ids</param>
        /// <returns>returns a AzureMLModuleVersion list</returns>
        [ActionAuthorize(AllOperations.Actions.WorkspacesModulesRead)]
        [Authorize(Policy = "Reader")]
        [HttpPost]
        [Route("BulkGetAzureMLModuleVersions")]
        [ApiExplorerSettings(GroupName = "private")]
        [OtelAudit(AccessLevel = "Reader")]
        [ScenarioName(ScenarioName.GetAzureMLModuleVersion)]
        public async Task<IEnumerable<AzureMLModuleVersion>> BulkGetAzureMLModuleVersionAsync(
            string subscriptionId,
            string resourceGroupName,
            string workspaceName,
            [FromBody] string[] ids)
        {
            var workspaceIdentity = new WorkspaceIdentity
            {
                SubscriptionId = subscriptionId,
                ResourceGroupName = resourceGroupName,
                WorkspaceName = workspaceName,
                WorkspaceId = await GetWorkspaceIdentityIdAsync(subscriptionId, resourceGroupName, workspaceName, _operationContextProvider.GetCreatedByObject())
            };

            return await base.BulkGetAzureMLModuleVersionAsync(workspaceIdentity, ids).ConfigureAwait(false);
        }

        #region temporary workaround for studio registering module before AML repo online

        ///<summary>
        /// CreateAzureMLModuleVersionWithIdAsync
        ///</summary>
        /// <param name="subscriptionId">The subscription id</param>
        /// <param name="resourceGroupName">The resource group name</param>
        /// <param name="workspaceName">The workspace name</param>
        /// <param name="creationInfo">The module creation info</param>
        /// <returns>returns a ModuleVersion Entity</returns>
        [Authorize(Policy = "S2S")]
        [HttpPost]
        [Route("AzureMLModuleVersions/CreateWithId")]
        [ApiExplorerSettings(IgnoreApi = true)]
        [OtelAudit(OperationType.Create, AccessLevel = "S2S")]
        public async Task<AzureMLModuleVersion> CreateAzureMLModuleVersionWithIdAsync(
           string subscriptionId,
           string resourceGroupName,
           string workspaceName,
           [FromBody] AzureMLModuleVersionCreationInfo creationInfo)
        {
            var workspaceIdentity = new WorkspaceIdentity()
            {
                SubscriptionId = subscriptionId,
                ResourceGroupName = resourceGroupName,
                WorkspaceName = workspaceName,
                WorkspaceId = await GetWorkspaceIdentityIdAsync(subscriptionId, resourceGroupName, workspaceName, _operationContextProvider.GetCreatedByObject()),
            };

            return await base.CreateAzureMLModuleVersionWithIdAsync(workspaceIdentity, creationInfo).ConfigureAwait(false);
        }
        #endregion
    }
}
