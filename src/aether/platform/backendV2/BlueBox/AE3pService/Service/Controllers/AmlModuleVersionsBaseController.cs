﻿// <copyright file="AmlModuleVersionsBaseController.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Aether.AE3pService.Exceptions;
using Microsoft.Aether.AEVA.DataContractConverter;
using Microsoft.Aether.AEVA.DataContracts;
using Microsoft.Aether.BackendCommon;
using Microsoft.Aether.BackendCommon.AzureMonitor;
using Microsoft.Aether.DataContracts;
using Microsoft.Aether.DataContracts.Entity;
using Microsoft.Aether.DataContracts.Error;
using Microsoft.AIPlatform.Telemetry.Contracts;
using Microsoft.AspNetCore.Mvc;
using Microsoft.MachineLearning.AccountRP.Contracts;
using Microsoft.MachineLearning.Common.Core.Exceptions;
using Microsoft.RelInfra.Instrumentation.Logging;
using AEVACloudSettings = Microsoft.Aether.AEVA.DataContracts.CloudSettings;
using AEVAModuleEntity = Microsoft.Aether.AEVA.DataContracts.ModuleEntity;
using AEVASubGraphConfiguration = Microsoft.Aether.AEVA.DataContracts.SubGraphConfiguration;
using CreatedBy = Microsoft.Aether.DataContracts.CreatedBy;

namespace Microsoft.Aether.AE3pService
{
    ///<summary>
    /// AmlModuleVersionsBaseController class
    ///</summary>
    public class AmlModuleVersionsBaseController : BaseController
    {
        private readonly IAzureMonitorStatusLogger _azureMonitorStatusLogger;
        ///<summary>
        /// ModulesController class constructor
        ///</summary>
        public AmlModuleVersionsBaseController(IStoreCollection stores, IContextProvider operationContextProvider, IWorkspaceResourcesCache workspaceResourcesCache, IAzureMonitorStatusLogger azureMonitorStatusLogger)
            : base(stores, operationContextProvider, workspaceResourcesCache)
        {
            // inject stuff
            _azureMonitorStatusLogger = azureMonitorStatusLogger;
        }

        ///<summary>
        /// CreateAzureMLModuleVersionAsync
        ///</summary>
        /// <param name="workspaceIdentity">The workspace identity</param>
        /// <param name="creationInfo">The module creation info</param>
        /// <returns>returns a Module Entity</returns>
        protected async Task<AzureMLModuleVersion> CreateAzureMLModuleVersionAsync(
            WorkspaceIdentity workspaceIdentity,
            [FromBody] AzureMLModuleVersionCreationInfo creationInfo)
        {
            if (_studioBuiltinModulePrefixRegex.IsMatch(creationInfo.Name))
            {
                throw new BaseException(new InvalidArgumentWithDetail().ToBaseError(nameof(creationInfo.Name),
                        nameof(creationInfo.Name), string.Format(AE3pErrorMessages.VersionNamewithPreservedPrefix, _studioBuiltinModulePrefix)));
            }

            return await CreateModuleVersionHelperAsync(
                _stores.GetAmlModuleStore(User, workspaceIdentity),
                 _stores.GetModuleStore(User, workspaceIdentity),
                _stores.GetDataTypeStore(User, workspaceIdentity),
                workspaceIdentity,
                creationInfo, _operationContextProvider.GetCreatedByObject());
        }

        ///<summary>
        /// Create an AzureMLModuleVersion which corresponds to a subgraph.  This will create the graph in the
        /// graph store and then create an AML module version (with ModuleEntity) which points to that graph.
        ///</summary>
        /// <param name="workspaceIdentity">The workspace identity</param>
        /// <param name="subGraphVersionCreationInfo">The subgraph creation info</param>
        /// <returns>returns a Module Entity</returns>
        protected async Task<AzureMLModuleVersion> CreateSubGraphModuleVersionAsync(
            WorkspaceIdentity workspaceIdentity,
            [FromBody] SubGraphVersionCreationInfo subGraphVersionCreationInfo)
        {
            if (_studioBuiltinModulePrefixRegex.IsMatch(subGraphVersionCreationInfo.Name))
            {
                throw new BaseException(new InvalidArgumentWithDetail().ToBaseError(nameof(subGraphVersionCreationInfo.Name),
                        nameof(subGraphVersionCreationInfo.Name), string.Format(AE3pErrorMessages.VersionNamewithPreservedPrefix, _studioBuiltinModulePrefix)));
            }

            AEVASubGraphConfiguration subGraphConfig = await ControllerHelper.CreateGraphForSubGraphModuleAsync(
                workspaceIdentity.WorkspaceId,
                _stores.GetGraphStore(User, workspaceIdentity),
                _stores.GetAmlGraphDraftStore(User, workspaceIdentity),
                _operationContextProvider,
                subGraphVersionCreationInfo).ConfigureAwait(false);

            AzureMLModuleVersionCreationInfo moduleVersionCreationInfo = new AzureMLModuleVersionCreationInfo(
                name: subGraphVersionCreationInfo.Name,
                displayName: subGraphVersionCreationInfo.DisplayName,
                description: subGraphVersionCreationInfo.Description,
                isDeterministic: subGraphVersionCreationInfo.IsDeterministic,
                moduleExecutionType: "SubGraphCloud",
                hash: string.Empty,
                identifierHash: string.Empty,
                amlModuleId: subGraphVersionCreationInfo.AmlModuleId,
                version: subGraphVersionCreationInfo.Version,
                setAsDefaultVersion: subGraphVersionCreationInfo.SetAsDefaultVersion,
                structuredInterface: ControllerHelper.CreateStructuredInterfaceFromEntityInterface(subGraphVersionCreationInfo.Interface),
                kvTags: subGraphVersionCreationInfo.KvTags,
                properties: subGraphVersionCreationInfo.Properties,
                cloudSettings: new AEVACloudSettings()
                {
                    SubGraphConfig = subGraphConfig
                });

            return await CreateModuleVersionHelperAsync(
                _stores.GetAmlModuleStore(User, workspaceIdentity),
                _stores.GetModuleStore(User, workspaceIdentity),
                _stores.GetDataTypeStore(User, workspaceIdentity),
                workspaceIdentity,
                moduleVersionCreationInfo, _operationContextProvider.GetCreatedByObject());
        }

        ///<summary>
        /// UpdateAzureMLModuleVersionAsync
        ///</summary>
        /// <param name="workspaceIdentity">The workspace identity</param>
        /// <param name="id">The module id</param>
        /// <param name="updated">The Module</param>
        /// <returns>returns a ModuleVersion Entity</returns>
        protected async Task<AzureMLModuleVersion> UpdateAzureMLModuleVersionAsync(
            WorkspaceIdentity workspaceIdentity,
            string id,
            [FromBody] AzureMLModuleVersion updated)
        {
            return await UpdateModuleVersionHelperAsync(workspaceIdentity, id, updated);
        }

        ///<summary>
        /// GetAzureMLModuleVersionAsync
        ///</summary>
        /// <param name="workspaceIdentity">The workspace identity</param>
        /// <param name="id">The module id</param>
        /// <returns>returns a Module</returns>
        protected async Task<AzureMLModuleVersion> GetAzureMLModuleVersionAsync(
            WorkspaceIdentity workspaceIdentity,
            string id)
        {
            var modules = await GetModulesHelperAsync(workspaceIdentity, new string[] { id });

            return modules.FirstOrDefault();
        }

        ///<summary>
        /// GetAzureMLModuleVersionByHashAsync
        ///</summary>
        /// <param name="workspaceIdentity">The workspace identity</param>
        /// <param name="identifierHash">The module identifierHash</param>
        /// <returns>returns a AzureMLModuleVersion</returns>
        protected async Task<AzureMLModuleVersion> GetAzureMLModuleVersionByHashAsync(
            WorkspaceIdentity workspaceIdentity,
            string identifierHash)
        {
            Dictionary<string, string> keyValueId = new Dictionary<string, string>();
            keyValueId[AzureMonitorLogEventKeys.ModuleId] = identifierHash;
            try
            {
                var moduleEntity = await _stores.GetModuleStore(User, workspaceIdentity).TryGetEntityByHashAsync(DefaultWorkspaceId, identifierHash);
                HashSet<string> dataTypeNames = await ControllerHelper.GetAllDataTypeNamesSetAsync(_stores.GetDataTypeStore(User, workspaceIdentity), DefaultWorkspaceId);

                if (moduleEntity != null)
                {
                    AzureMLModuleVersion amlModuleVersion = ModulesBaseController.BuildModule<AzureMLModuleVersion>(moduleEntity, dataTypeNames);
                    amlModuleVersion.ModuleId = (moduleEntity.FamilyId == "0") ? null : moduleEntity.FamilyId;

                    _azureMonitorStatusLogger.LogStatusChange(_operationContextProvider.GetCreatedByObject(), AzureMonitorLogResultType.Succeeded, workspaceIdentity.SubscriptionId, workspaceIdentity.ResourceGroupName, workspaceIdentity.WorkspaceName, keyValueId, AllOperations.Actions.WorkspacesModulesRead);

                    return amlModuleVersion;
                }

                return null;
            }
            catch (Exception e)
            {
                var exp = ExceptionFrameworkMapper.MapToBaseException(e, new ErrorInGetAzureMLModuleVersionByHash(ExceptionFrameworkMapper.MessageFormat(e)));
                ControllerHelper.ErrorLogWithSafeMessageJudging(exp, identifierHash);
                _azureMonitorStatusLogger.LogStatusChange(_operationContextProvider.GetCreatedByObject(), AzureMonitorLogResultType.Failed, workspaceIdentity.SubscriptionId, workspaceIdentity.ResourceGroupName, workspaceIdentity.WorkspaceName, keyValueId, AllOperations.Actions.WorkspacesModulesRead);

                throw exp;
            }
        }

        ///<summary>
        /// BulkGetAzureMLModuleVersionAsync
        ///</summary>
        /// <param name="workspaceIdentity">The workspace identity</param>
        /// <param name="ids">The module ids</param>
        /// <returns>returns a AzureMLModuleVersion list</returns>
        protected async Task<IEnumerable<AzureMLModuleVersion>> BulkGetAzureMLModuleVersionAsync(
            WorkspaceIdentity workspaceIdentity,
            [FromBody] string[] ids)
        {
            return await GetModulesHelperAsync(workspaceIdentity, ids);
        }

        #region temporary workaround for studio registering module before AML repo online

        ///<summary>
        /// CreateAzureMLModuleVersionWithIdAsync
        ///</summary>
        /// <param name="workspaceIdentity">The workspace identity</param>
        /// <param name="creationInfo">The module creation info</param>
        /// <returns>returns a ModuleVersion Entity</returns>
        protected async Task<AzureMLModuleVersion> CreateAzureMLModuleVersionWithIdAsync(
           WorkspaceIdentity workspaceIdentity,
           [FromBody] AzureMLModuleVersionCreationInfo creationInfo)
        {
            if (!_studioBuiltinModulePrefixRegex.IsMatch(creationInfo.Name))
            {
                creationInfo.Name = _studioBuiltinModulePrefix + creationInfo.Name;
            }

            return await CreateModuleVersionHelperAsync(
                _stores.GetAmlModuleStore(User, workspaceIdentity),
                 _stores.GetModuleStore(User, workspaceIdentity),
                _stores.GetDataTypeStore(User, workspaceIdentity),
                workspaceIdentity,
                creationInfo, _operationContextProvider.GetCreatedByObject(),
                AmlModulesBaseController.GetIdFromProperties(creationInfo.Properties));
        }

        #endregion

        ///<summary>
        /// UpdateModuleVersionHelperAsync
        ///</summary>
        protected async Task<AzureMLModuleVersion> UpdateModuleVersionHelperAsync(
            WorkspaceIdentity workspaceIdentity,
            string id,
            [FromBody] AzureMLModuleVersion updated)
        {
            try
            {
                if (id != updated.Data.Id)
                {
                    throw new BaseException(new InvalidArgumentWithDetail().ToBaseError(nameof(id), nameof(id), string.Format(AE3pErrorMessages.AE365ModuleUpdateWithDifferentId, id, updated.Data.Id)));
                }

                var store = _stores.GetModuleStore(
                    User,
                    workspaceIdentity);
                var preChangeModule = await store.GetEntityAsync(DefaultWorkspaceId, id);

                // Change only mutable fields
                preChangeModule.DisplayName = updated.Data.DisplayName;
                preChangeModule.Description = updated.Data.Description;
                switch (updated.Data.EntityStatus)
                {
                    case AEVA.DataContracts.EntityStatus.Active:
                        preChangeModule.EntityStatus = DataContracts.EntityStatus.Active;
                        break;
                    case AEVA.DataContracts.EntityStatus.Deprecated:
                        preChangeModule.EntityStatus = DataContracts.EntityStatus.Deprecated;
                        break;
                    case AEVA.DataContracts.EntityStatus.Disabled:
                        preChangeModule.EntityStatus = DataContracts.EntityStatus.Disabled;
                        break;
                }

                if (updated.Data.Category != null) // clients that were released before adding the category field would provide it as null and accidently nullify this field
                {
                    preChangeModule.Category = updated.Data.Category;
                }
                preChangeModule.LastUpdatedBy = _operationContextProvider.GetCreatedByObject();

                var moduleEntity = await UpdateAsync(store, preChangeModule, typeof(AEVAModuleEntity));
                CommonLogger.LogEntityInfo(moduleEntity.Id, $"The moduleVersion was updated successfully. UploadState={moduleEntity.UploadState}");
                HashSet<string> dataTypeNames = await ControllerHelper.GetAllDataTypeNamesSetAsync(_stores.GetDataTypeStore(User, workspaceIdentity), DefaultWorkspaceId);
                AzureMLModuleVersion amlModuleVersion = ModulesBaseController.BuildModule<AzureMLModuleVersion>(moduleEntity, dataTypeNames);
                amlModuleVersion.ModuleId = (moduleEntity.FamilyId == "0") ? null : moduleEntity.FamilyId;
                return amlModuleVersion;
            }
            catch (Exception e)
            {
                var exp = ExceptionFrameworkMapper.MapToBaseException(e, new ErrorInUpdateModuleVersionHelper(ExceptionFrameworkMapper.MessageFormat(e)));
                ControllerHelper.ErrorLogWithSafeMessageJudging(exp, id);
                throw exp;
            }
        }

        ///<summary>
        /// GetModulesHelperAsync
        ///</summary>
        protected async Task<IEnumerable<AzureMLModuleVersion>> GetModulesHelperAsync(
            WorkspaceIdentity workspaceIdentity,
            string[] ids)
        {
            try
            {
                return await FetchModulesAsync(_stores.GetModuleStore(User, workspaceIdentity), _stores.GetDataTypeStore(User, workspaceIdentity), ids);
            }
            catch (Exception e)
            {
                var exp = ExceptionFrameworkMapper.MapToBaseException(e, new ErrorInGetModulesHelper(ExceptionFrameworkMapper.MessageFormat(e)));
                ids.ForEach(id =>
                {
                    ControllerHelper.ErrorLogWithSafeMessageJudging(exp, id, "Unable to get module");
                });
                throw exp;
            }
        }

        ///<summary>
        /// FetchModulesAsync
        ///</summary>
        public static async Task<IEnumerable<AzureMLModuleVersion>> FetchModulesAsync(IModuleStore moduleStore, IDataTypeStore dataTypesStore, string[] ids)
        {
            var modules = await moduleStore.BulkGetIdsAsync(DefaultWorkspaceId, ids);
            HashSet<string> dataTypeNames = await ControllerHelper.GetAllDataTypeNamesSetAsync(dataTypesStore, DefaultWorkspaceId);

            return modules.Select(module =>
            {
                AzureMLModuleVersion amlModuleVersion = ModulesBaseController.BuildModule<AzureMLModuleVersion>(module, dataTypeNames);
                amlModuleVersion.ModuleId = (module.FamilyId == "0") ? null : module.FamilyId;
                return amlModuleVersion;
            });
        }

        ///<summary>
        /// CreateModuleVersionHelperAsync
        ///</summary>
        public virtual async Task<AzureMLModuleVersion> CreateModuleVersionHelperAsync<T>(
            IAmlModuleStore amlModuleStore,
            IModuleStore moduleStore,
            IDataTypeStore dataTypeStore,
            WorkspaceIdentity workspaceIdentity,
            T creationInfo,
            CreatedBy createdBy,
            string id = null) where T : AE365ModuleVersionCreationInfo
        {
            try
            {
                AmlModuleEntity amlModuleEntity = null;

                if (!string.IsNullOrEmpty(id))
                {
                    //this should be a studio identity, we need validate Id had not been registered and AmlModuleEntity exist
                    DataContracts.ModuleEntity validateModuleEntity = await moduleStore.TryGetEntityAsync(DefaultWorkspaceId, id).ConfigureAwait(false);
                    if (validateModuleEntity != null)
                    {
                        throw new BaseException(new InvalidArgumentWithDetail().ToBaseError(nameof(id), nameof(id), string.Format(AE3pErrorMessages.VersionidRegisterd, id)));
                    }

                    if (string.IsNullOrEmpty(creationInfo.AmlModuleId))
                    {
                        throw new BaseException(new InvalidArgumentWithDetail().ToBaseError(nameof(creationInfo.AmlModuleId), nameof(creationInfo.AmlModuleId), string.Format(AE3pErrorMessages.NullModuleId, id)));
                    }

                    amlModuleEntity = await amlModuleStore.TryGetEntityAsync(DefaultWorkspaceId, creationInfo.AmlModuleId);
                    if (amlModuleEntity == null || !_studioBuiltinModulePrefixRegex.IsMatch(amlModuleEntity.Name))
                    {
                        throw new BaseException(new InvalidArgumentWithDetail().ToBaseError(nameof(creationInfo.AmlModuleId), nameof(creationInfo.AmlModuleId), string.Format(AE3pErrorMessages.InvalidModuleId, id)));
                    }
                }
                else
                {
                    if (creationInfo.AmlModuleId == null)
                    {
                        var amlModule = new AmlModuleEntity(
                            name: creationInfo.Name,
                            description: LoggingEventHelper.RemoveUnallowedCharacters(creationInfo.Description),
                            kvTags: creationInfo.KvTags,
                            properties: creationInfo.Properties,
                            category: creationInfo.Category);
                        amlModuleEntity = await amlModuleStore.CreateAsync(DefaultWorkspaceId, amlModule);
                        creationInfo.AmlModuleId = amlModuleEntity.Id;
                    }
                    else if (!"0".Equals(creationInfo.AmlModuleId))
                    {
                        amlModuleEntity = await amlModuleStore.GetEntityAsync(DefaultWorkspaceId, creationInfo.AmlModuleId);
                    }
                }

                var moduleVersionList = amlModuleEntity?.Versions?.ToList() ?? new List<AmlModuleVersionDescriptor>();
                HashSet<string> usedVersions = moduleVersionList.Select(mv => mv.Version).ToHashSet();
                var version = creationInfo.Version;

                if (usedVersions.Contains(version))
                {
                    CommonLogger.LogEntityError(workspaceIdentity.WorkspaceName, "Cannot create ModuleVersion, version already exists: {aml_model_version}", version);
                    throw new BaseException(new InvalidArgumentWithDetail().ToBaseError(nameof(version), nameof(version), string.Format(AE3pErrorMessages.ModuleVersionExist, version)));
                }

                for (var i = 0; string.IsNullOrEmpty(version); i++)
                {
                    var possibleVersion = (moduleVersionList.Count() + i).ToString();
                    version = usedVersions.Contains(possibleVersion) ? null : possibleVersion;
                }

                DataContracts.ModuleEntity moduleEntity = ModulesBaseController.ConstructModuleEntity(creationInfo: creationInfo, createdBy: createdBy, familyId: amlModuleEntity?.Id ?? creationInfo.AmlModuleId, version: version);

                moduleEntity.Id = id;
                // This is for backward compatibility that metastore service will create a dummy snapshot id before.
                var originalSnapshotId = moduleEntity?.DataLocation?.StorageId;
                if (originalSnapshotId == null)
                {
                    moduleEntity.DataLocation = new DataContracts.DataLocation
                    {
                        StorageId = Guid.NewGuid().ToString(),
                        StorageType = DataContracts.DataLocationStorageType.Snapshot
                    };
                }
                DataContracts.ModuleEntity createdModuleEntity = await moduleStore.CreateAsync(DefaultWorkspaceId, moduleEntity);
                CommonLogger.LogEntityInfo(workspaceIdentity.WorkspaceId, $"The snapshot for module \"{createdModuleEntity?.Id}\" is updated from \"{originalSnapshotId}\" to \"{createdModuleEntity?.DataLocation?.StorageId}\" after creation.");

                HashSet<string> dataTypeNames = await ControllerHelper.GetAllDataTypeNamesSetAsync(dataTypeStore, DefaultWorkspaceId);
                var amlModuleVersion = ModulesBaseController.BuildModule<AzureMLModuleVersion>(createdModuleEntity, dataTypeNames);
                moduleVersionList.Add(new AmlModuleVersionDescriptor { ModuleVersionId = amlModuleVersion.Data.Id, Version = version });

                if ("0".Equals(creationInfo.AmlModuleId))
                {
                    amlModuleVersion.ModuleId = null;
                    return amlModuleVersion;
                }

                amlModuleEntity.Versions = moduleVersionList;

                if (moduleVersionList.Count() == 1 || creationInfo.SetAsDefaultVersion) // if this is the first module, it is the default one
                {
                    amlModuleEntity.DefaultVersion = version;
                }

                try
                {
                    await amlModuleStore.UpdateAsync(DefaultWorkspaceId, amlModuleEntity);
                    amlModuleVersion.ModuleId = amlModuleEntity.Id;
                }
                catch // deprecating the created moduleVersion as we were not able to add it to the AzureMLModule
                {
                    createdModuleEntity.EntityStatus = DataContracts.EntityStatus.Deprecated;
                    var aevaModule = ContractConverter.Convert<DataContracts.ModuleEntity, AEVAModuleEntity>(createdModuleEntity);
                    await UpdateAsync(moduleStore, createdModuleEntity, typeof(AEVAModuleEntity));
                }
                return amlModuleVersion;
            }
            catch (Exception e)
            {
                var exp = ExceptionFrameworkMapper.MapToBaseException(e, new ErrorInCreateModuleVersionHelper(ExceptionFrameworkMapper.MessageFormat(e)));
                ControllerHelper.ErrorLogWithSafeMessageJudging(exp, workspaceIdentity.WorkspaceName);
                throw exp;
            }
        }
    }
}
