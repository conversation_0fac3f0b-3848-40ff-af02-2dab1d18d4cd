﻿// <copyright file="RecurrenceSubmissionCalculatorTests.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Moq;
using NUnit.Framework;
using Microsoft.Aether.DataContracts;
using System;
using System.Collections.Generic;

namespace Microsoft.Aether.AE3pService.Tests
{
    [TestFixture]
    public class RecurrenceSubmissionCalculatorTests
    {
        private readonly MockRepository _repository;
        private RecurrenceSubmissionCalculator _recurrenceSubmissionCalculator = new RecurrenceSubmissionCalculator();
        private PipelineScheduleEntity _fakePipelinesScheduleEntity;
        private const double MinutesPerMonth = 43800.0;
        private const double HoursPerMonth = 730.0;
        private const double DaysPerMonth = 30.0;
        private const double WeeksPerMonth = 4.3;

        public RecurrenceSubmissionCalculatorTests()
        {
            _repository = new MockRepository(MockBehavior.Loose) { DefaultValue = DefaultValue.Mock, CallBase = true };
        }

        [SetUp]
        public void Init()
        {
            _fakePipelinesScheduleEntity = _repository.Create<PipelineScheduleEntity>().Object;
            // test recurrence ScheduleType
            _fakePipelinesScheduleEntity.ScheduleType = ScheduleType.Recurrence;
            _fakePipelinesScheduleEntity.Recurrence = _repository.Create<Recurrence>().Object;
            _fakePipelinesScheduleEntity.Recurrence.Interval = 2;
        }

        [Test]
        public void CalculateAverageMonthlySubmission_NoRecurrenceFrequency_ExpectArgumentException()
        {
            // test no Frequency throws ArgumentException
            try
            {
                _recurrenceSubmissionCalculator.CalculateAverageMonthlySubmissions(_fakePipelinesScheduleEntity);
            }
            catch (Exception ex)
            {
                Assert.True(ex is ArgumentException);
            }
        }

        [TestCase(Frequency.Minute, ExpectedResult = MinutesPerMonth / 2)]
        [TestCase(Frequency.Hour, ExpectedResult = HoursPerMonth / 2)]
        [TestCase(Frequency.Day, ExpectedResult = DaysPerMonth / 2)]
        [TestCase(Frequency.Week, ExpectedResult = WeeksPerMonth / 2)]
        [TestCase(Frequency.Month, ExpectedResult = 1.0 / 2)]
        public double CalculateAverageMonthlySubmissions_RecurrenceFrequency_ExpectNormalReturn(Frequency freq)
        {
            _fakePipelinesScheduleEntity.Recurrence.Frequency = freq;
            return _recurrenceSubmissionCalculator.CalculateAverageMonthlySubmissions(_fakePipelinesScheduleEntity);
        }

        [TestCase(Frequency.Day, ExpectedResult = (DaysPerMonth / 2) * 3)]
        [TestCase(Frequency.Week, ExpectedResult = (WeeksPerMonth / 2) * 9)]
        public double CalculateAverageMonthlySubmissions_SetReferrenceSchedule_ExpectNormalReturn(Frequency freq)
        {
            _fakePipelinesScheduleEntity.Recurrence.Frequency = freq;

            // Schedule is set, so testing other logic of private method CalculateSubmissionsPerDay()
            _fakePipelinesScheduleEntity.Recurrence.Schedule = _repository.Create<RecurrenceSchedule>().Object;
            _fakePipelinesScheduleEntity.Recurrence.Schedule.Minutes = new List<int>() { 1, 2, 3 };
            _fakePipelinesScheduleEntity.Recurrence.Schedule.Hours = new List<int>() { 1 };
            _fakePipelinesScheduleEntity.Recurrence.Schedule.WeekDays = new List<WeekDays>() { WeekDays.Friday };

            return _recurrenceSubmissionCalculator.CalculateAverageMonthlySubmissions(_fakePipelinesScheduleEntity);
        }

        [TestCase(0)]
        [TestCase(5)]
        public void CalculateAverageMonthlySubmissions_DataStoreScheduleTypeNoPollingInterval_ExpectArgumentException(int pollingInterval)
        {
            // test DataStore scheduleType
            _fakePipelinesScheduleEntity.ScheduleType = ScheduleType.DataStore;
            _fakePipelinesScheduleEntity.DataStoreTriggerInfo = _repository.Create<DataStoreTriggerInfo>().Object;
            _fakePipelinesScheduleEntity.DataStoreTriggerInfo.PollingInterval = pollingInterval;
            if (pollingInterval <= 0)
            {
                Assert.That(() => _recurrenceSubmissionCalculator.CalculateAverageMonthlySubmissions(_fakePipelinesScheduleEntity), Throws.TypeOf<ArgumentException>());
            }
            else
            {
                var output = _recurrenceSubmissionCalculator.CalculateAverageMonthlySubmissions(_fakePipelinesScheduleEntity);
                Assert.AreEqual(MinutesPerMonth / pollingInterval, output);
            }
        }
    }
}
