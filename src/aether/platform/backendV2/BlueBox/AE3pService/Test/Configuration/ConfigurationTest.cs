﻿// <copyright file="ConfigurationTest.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using Microsoft.Aether.BackendCommon.Configuration;
using Microsoft.AspNetCore.Cors.Infrastructure;
using Microsoft.Extensions.Configuration;
using Microsoft.MachineLearning.Common.WebApi.Client;
using NUnit.Framework;

namespace Microsoft.Aether.AE3pService.Tests.Configuration
{
    [TestFixture]
    public class ConfigurationTest
    {
        [Test]
        public void IniConfiguration_ServiceConfiguration_ShouldParseCorrect()
        {
            var configurationRoot = new ConfigurationBuilder().AddInMemoryCollection(new Dictionary<string, string>
            {
                {"ServiceInvoker:ReuseHttpClients", "true" },
                {"ServiceInvoker:DefaultRequestTimeoutMilliseconds", "30000"},
                {"ServiceInvoker:RetryPolicy:DefaultRetryCount", "2" },
                {"ServiceInvoker:RetryPolicy:DefaultDelayMilliseconds", "1000" },
            }).Build();

            var serviceInvokerConfiguration = configurationRoot.GetSection("ServiceInvoker").Get<ServiceInvokerConfiguration>();
            Assert.NotNull(serviceInvokerConfiguration);
            Assert.AreEqual(true, serviceInvokerConfiguration.ReuseHttpClients);
            Assert.AreEqual(30000, serviceInvokerConfiguration.DefaultRequestTimeoutMilliseconds);
            Assert.AreEqual(2, serviceInvokerConfiguration.RetryPolicy.DefaultRetryCount);
            Assert.AreEqual(1000, serviceInvokerConfiguration.RetryPolicy.DefaultDelayMilliseconds);
        }

        [Test]
        public void EnvironmentConfiguration_ServiceConfiguration_ShouldParseCorrect()
        {
            Environment.SetEnvironmentVariable("ServiceInvoker__DefaultRequestTimeoutMilliseconds", "10000");
            var configurationRoot = new ConfigurationBuilder().AddEnvironmentVariables().Build();
            var serviceInvokerConfiguration = configurationRoot.GetSection("ServiceInvoker").Get<ServiceInvokerConfiguration>();
            Assert.NotNull(serviceInvokerConfiguration);
            Assert.AreEqual(10000, serviceInvokerConfiguration.DefaultRequestTimeoutMilliseconds);
        }

        [Test]
        public void ExpressionConfiguration_ServiceConfiguration_ShouldParseCorrect()
        {
            Environment.SetEnvironmentVariable("ServiceInvoker__DefaultRequestTimeoutMilliseconds", null);
            Environment.SetEnvironmentVariable("EXPRESSION_CONFIG", "20000");
            Environment.SetEnvironmentVariable("RETRY_COUNT", "2");
            Environment.SetEnvironmentVariable("EXPRESSION_RECUR", "{{RETRY_COUNT}}");
            Environment.SetEnvironmentVariable("USERAGENT", "USERAGENTTEST");
            Environment.SetEnvironmentVariable("EXPRESSION_RECUR", "{{RETRY_COUNT}}");
            Environment.SetEnvironmentVariable("EXPRESSION_RECURTEST", "0");
            var configurationRoot = new ConfigurationBuilder()
                .AddInMemoryCollection(new Dictionary<string, string>
                {
                    {"BaseSetting:UserAgent", "Test_{{USERAGENT}}_{{EXPRESSION_RECURTEST}}" },
                    {"ServiceInvoker:ReuseHttpClients", "true" },
                    {"ServiceInvoker:DefaultRequestTimeoutMilliseconds", "{{EXPRESSION_CONFIG}}"},
                    {"ServiceInvoker:RetryPolicy:DefaultRetryCount", "{{EXPRESSION_RECUR}}" },
                    {"ServiceInvoker:RetryPolicy:DefaultDelayMilliseconds", "1000" },
                    {"ServiceInvoker:UserAgent", "{{BaseSetting:UserAgent}}" },
                }).AddEnvironmentVariables().Build();
            configurationRoot.LoadExpressionConfiguration();
            var serviceInvokerConfiguration = configurationRoot.GetSection("ServiceInvoker").Get<ServiceInvokerConfiguration>();
            Assert.NotNull(serviceInvokerConfiguration);
            Assert.AreEqual(20000, serviceInvokerConfiguration.DefaultRequestTimeoutMilliseconds);
            Assert.AreEqual(2, serviceInvokerConfiguration.RetryPolicy.DefaultRetryCount);
            Assert.AreEqual("Test_USERAGENTTEST_0", serviceInvokerConfiguration.UserAgent);
        }
    }
}
