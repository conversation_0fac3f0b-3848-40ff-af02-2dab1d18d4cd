﻿// <copyright file="ContractConverterTest.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Microsoft.Aether.AEVA.DataContractConverter;
using Microsoft.Aether.DataContracts;
using Microsoft.Aether.DataContracts.DataReferences;
using NUnit.Framework;
using System.Collections.Generic;
using AEVAStructuredInterfaceInput = Microsoft.Aether.AEVA.DataContracts.StructuredInterfaceInput;
using AEVADatasetTypes = Microsoft.Aether.AEVA.DataContracts.DatasetType;
using AEVADataStoreMode = Microsoft.Aether.AEVA.DataContracts.DataStoreMode;
using AEVATrainingOutput = Microsoft.Aether.AEVA.DataContracts.TrainingOutput;
using AEVAModuleEntity = Microsoft.Aether.AEVA.DataContracts.ModuleEntity;
using AEVAModuleType = Microsoft.Aether.AEVA.DataContracts.ModuleType;
using AEVADataLocationStorageType = Microsoft.Aether.AEVA.DataContracts.DataLocationStorageType;
using AEVADataLocation = Microsoft.Aether.AEVA.DataContracts.DataLocation;
using AEVADataReferenceType = Microsoft.Aether.AEVA.DataContracts.DataReferenceType;
using AEVAGraphLayout = Microsoft.Aether.AEVA.DataContracts.GraphLayout;
using AEVAPipelineRunCreationInfo = Microsoft.Aether.AEVA.DataContracts.PipelineRunCreationInfo;
using AEVAGraphInputValue = Microsoft.Aether.AEVA.DataContracts.GraphInputValue;
using AEVAPipelineSubmissionInfo = Microsoft.Aether.AEVA.DataContracts.PipelineSubmissionInfo;
using AEVACloudSettings = Microsoft.Aether.AEVA.DataContracts.CloudSettings;
using AEVAPipelineRunEntity = Microsoft.Aether.AEVA.DataContracts.PipelineRunEntity;
using AEVAPipelineRunStatusCode = Microsoft.Aether.AEVA.DataContracts.PipelineRunStatusCode;
using System.Runtime.Serialization;
using System.Linq;
using Newtonsoft.Json;
using System;

namespace Microsoft.Aether.AE3pService.Tests
{
    [TestFixture]
    public class ContractConverterTest
    {
        [Test]
        public void GraphLayout_Test()
        {
            var nodeLayout = new AEVA.DataContracts.NodeLayout()
            {
                X = 500,
                Y = 50,
                Width = 0,
                Height = 0,
            };
            var aeva = new AEVAGraphLayout
            {
                NodeLayouts = new Dictionary<string, AEVA.DataContracts.NodeLayout>() { { "id", nodeLayout } },
                AnnotationNodes = new List<AEVA.DataContracts.GraphAnnotationNode>
                {
                    new AEVA.DataContracts.GraphAnnotationNode
                    {
                        Id = "id",
                        Content = "content",
                        MentionedNodeNames = new List<string> { "node1", "node2" },
                        StructuredContent = "structuredContent",
                    }
                },
            };
            var result = ContractConverter.Convert<AEVAGraphLayout, GraphLayout>(aeva);
            Assert.True(result is GraphLayout);
            Assert.NotNull(result.AnnotationNodes);
            Assert.AreEqual("id", result.AnnotationNodes.First().Id);
            Assert.AreEqual("content", result.AnnotationNodes.First().Content);
            Assert.AreEqual("structuredContent", result.AnnotationNodes.First().StructuredContent);
            Assert.AreEqual(2, result.AnnotationNodes.First().MentionedNodeNames.Count);
        }

        [Test]
        public void StructuredInterfaceInput_Test()
        {
            var aeva = new AEVAStructuredInterfaceInput()
            {
                DatasetTypes = new HashSet<AEVADatasetTypes>()
                {
                    AEVADatasetTypes.File,
                    AEVADatasetTypes.Tabular
                },
                PathOnCompute = "testPathOnCompute",
                DataStoreMode = AEVADataStoreMode.Download
            };
            var result = ContractConverter.Convert<AEVAStructuredInterfaceInput, StructuredInterfaceInput>(aeva);
            Assert.True(result is StructuredInterfaceInput);
            Assert.AreEqual(result.DatasetTypes.Count, aeva.DatasetTypes.Count);
            Assert.AreEqual(result.PathOnCompute, aeva.PathOnCompute);
            Assert.AreEqual(result.DataStoreMode, DataStoreMode.Download);
        }

        [Test]
        public void TrainingOutput_Test()
        {
            var aeva = new AEVATrainingOutput()
            {
                Iteration = 2,
                Metric = "testMetric"
            };
            var result = ContractConverter.Convert<AEVATrainingOutput, TrainingOutput>(aeva);
            Assert.True(result is TrainingOutput);
            Assert.AreEqual(result.Iteration, aeva.Iteration);
            Assert.AreEqual(result.Metric, aeva.Metric);
            Assert.AreEqual(result.ModelFile, aeva.ModelFile);
        }

        [Test]
        public void ModuleEntity_Test()
        {
            // test nullable field translate
            var aeva = new AEVAModuleEntity()
            {
                ModuleType = AEVAModuleType.BatchInferencing
            };
            var result = ContractConverter.Convert<AEVAModuleEntity, ModuleEntity>(aeva);
            Assert.True(result is ModuleEntity);
            Assert.AreEqual(result.ModuleType, ModuleType.BatchInferencing);
        }

        [Test]
        public void DataLocation_Test()
        {
            var location = new DataLocation()
            {
                StorageType = DataLocationStorageType.Snapshot
            };
            var aeva = ContractConverter.Convert<DataLocation, AEVADataLocation>(location, ContractConverter.GetDataLocationCustomConverters());
            Assert.True(aeva is AEVADataLocation);
            Assert.AreEqual(aeva.StorageType, AEVADataLocationStorageType.Snapshot);

            location = new DataLocation()
            {
                StorageType = DataLocationStorageType.Cosmos,
                DataReference = new DataReference()
                {
                    Type = DataReferenceType.AzureSqlDatabase
                }
            };
            //  Cosmos storage type should be converted to None type
            aeva = ContractConverter.Convert<DataLocation, AEVADataLocation>(location, ContractConverter.GetDataLocationCustomConverters());
            Assert.True(aeva is AEVADataLocation);
            Assert.AreEqual(aeva.StorageType, AEVADataLocationStorageType.None);

            //  DataReferenceType enum values are different in AEVA version, check that it was converted correctly
            Assert.AreEqual(aeva.DataReference.Type, AEVADataReferenceType.AzureSqlDatabase);

            var convertBack = ContractConverter.Convert<AEVADataLocation, DataLocation>(aeva, ContractConverter.GetDataLocationReverseCustomConverters());
            Assert.True(convertBack is DataLocation);
            Assert.AreEqual(convertBack.StorageType, DataLocationStorageType.Cosmos);
            Assert.AreEqual(convertBack.DataReference.Type, DataReferenceType.AzureSqlDatabase);

            //  Test that the custom converter works when the values are in a dictionary
            AEVAPipelineRunCreationInfo creationInfo = new AEVAPipelineRunCreationInfo()
            {
                GraphInputAssignments = new Dictionary<string, AEVAGraphInputValue>()
                {
                    {
                        "input",
                        new AEVAGraphInputValue()
                        {
                            DataId = "dataID",
                            DataLocation = new AEVADataLocation()
                            {
                                StorageType = AEVADataLocationStorageType.None
                            }
                        }
                    }
                }
            };
            ExperimentCreationInfo convertedCreationInfo = ContractConverter.Convert<AEVAPipelineRunCreationInfo, ExperimentCreationInfo>(creationInfo, ContractConverter.GetDataLocationReverseCustomConverters());
            Assert.True(convertedCreationInfo is ExperimentCreationInfo);
            Assert.AreEqual(convertedCreationInfo.GraphInputAssignments["input"].DataId, "dataID");
            Assert.AreEqual(convertedCreationInfo.GraphInputAssignments["input"].DataLocation.StorageType, DataLocationStorageType.Cosmos);
        }

        [Test]
        public void TestContract_Test()
        {
            // test other conner cases
            var aeva = new AEVATestContract()
            {
                Code1 = new List<int>() { 2, 3 },  // test when enumerate type 
                Code2 = new Dictionary<string, AEVADataLocation>()  // test convertible types
                {
                    { "key1", new AEVADataLocation()
                             {
                                StorageId = "storageId1",
                                StorageType = AEVADataLocationStorageType.Snapshot
                             }
                    },
                    { "key2", new AEVADataLocation()
                             {
                                StorageId = "storageId2",
                                StorageType = AEVADataLocationStorageType.AzureBlob
                             }}
                }
            };
            var result = ContractConverter.Convert<AEVATestContract, TestContract>(aeva);
            Assert.True(result is TestContract);
            CollectionAssert.AreEqual(aeva.Code1, result.Code1);
            Assert.AreEqual(aeva.Code2.Count, result.Code2.Count);
            CollectionAssert.AreEqual(aeva.Code2.Keys, result.Code2.Keys);

            foreach (string key in aeva.Code2.Keys)
            {
                Assert.True(result.Code2[key] is DataLocation);
                Assert.AreEqual(aeva.Code2[key].StorageId, result.Code2[key].StorageId);
                Assert.AreEqual((int)aeva.Code2[key].StorageType, (int)result.Code2[key].StorageType);
            }
        }

        [Test]
        public void PipelineSubmissionInfo_Test()
        {
            var pipeInfo = new PipelineSubmissionInfo()
            {
                ParameterAssignments = new Dictionary<string, string> { { "time", "2022-01-11T04:03:14.3774220Z" } }
            };

            var aevaPipeInfo = ContractConverter.Convert<PipelineSubmissionInfo, AEVAPipelineSubmissionInfo>(pipeInfo);

            Assert.IsNotNull(aevaPipeInfo.ParameterAssignments);
            Assert.AreEqual(pipeInfo.ParameterAssignments, aevaPipeInfo.ParameterAssignments);
        }

        [Test]
        public void AutoMLFeaturizationConfig_ColumnTransformer_Test()
        {
            var columnTransfer = new AEVA.DataContracts.ColumnTransformer
            {
                Fields = new List<string>() { "field1" },
                Parameters = new Newtonsoft.Json.Linq.JObject() { { "key", "value" } }
            };
            var aevaClolumnTransfer = ContractConverter.Convert<AEVA.DataContracts.ColumnTransformer, ColumnTransformer>(columnTransfer);
            Assert.NotNull(aevaClolumnTransfer);
            Assert.AreEqual("field1", aevaClolumnTransfer.Fields.First());
            Assert.AreEqual(JsonConvert.SerializeObject(columnTransfer.Parameters), JsonConvert.SerializeObject(aevaClolumnTransfer.Parameters));
        }

        [Test]
        public void CloudSettingAutoMLFeaturizationConfig_Test()
        {
            var cloudSettings = new AEVACloudSettings()
            {
                AutoMLComponentConfig = new AEVA.DataContracts.AutoMLComponentConfiguration()
                {
                    AutoFeaturizeConfig = new AEVA.DataContracts.AutoFeaturizeConfiguration()
                    {
                        FeaturizationConfig = new AEVA.DataContracts.FeaturizationSettings()
                        {
                            BlockedTransformers = new List<string>() { "transformer1", "transformer2" },
                            ColumnPurposes = new Dictionary<string, string>()
                            {
                                { "column1", "purpose1" }
                            },
                            Mode = AEVA.DataContracts.FeaturizationMode.Custom,
                            TransformerParams = new Dictionary<string, IList<AEVA.DataContracts.ColumnTransformer>>()
                            {
                                { "key", new List<AEVA.DataContracts.ColumnTransformer>()
                                    {
                                        new AEVA.DataContracts.ColumnTransformer()
                                        {
                                            Fields = new List<string>() { "field1" },
                                            Parameters = new Newtonsoft.Json.Linq.JObject()
                                            { { "key", "value" } }
                                        }
                                    }
                                }
                            },
                        },
                    },
                }
            };
            var aevaCloudSettings = ContractConverter.Convert<AEVACloudSettings, CloudSettings>(cloudSettings);
            Assert.NotNull(aevaCloudSettings);
            Assert.AreEqual("key", aevaCloudSettings.AutoMLComponentConfig.AutoFeaturizeConfig.FeaturizationConfig.TransformerParams.Keys.First());
            Assert.AreEqual("field1", aevaCloudSettings.AutoMLComponentConfig.AutoFeaturizeConfig.FeaturizationConfig.TransformerParams["key"].First().Fields.First());
            Assert.AreEqual(JsonConvert.SerializeObject(cloudSettings.AutoMLComponentConfig.AutoFeaturizeConfig.FeaturizationConfig.TransformerParams["key"].First().Parameters),
                JsonConvert.SerializeObject(aevaCloudSettings.AutoMLComponentConfig.AutoFeaturizeConfig.FeaturizationConfig.TransformerParams["key"].First().Parameters));
        }

        [Test]
        public void TestGetPipelineStatusCustomConverters()
        {
            var experimentEntity = new ExperimentEntity();
            experimentEntity.Status = new ExperimentStatus();
            foreach (var statusCode in Enum.GetValues(typeof(ExperimentStatusCode)))
            {
                experimentEntity.Status.StatusCode = (ExperimentStatusCode)statusCode;
                var pipelineRunEntity = ContractConverter.Convert<ExperimentEntity, AEVAPipelineRunEntity>(experimentEntity, ContractConverter.GetPipelineStatusCustomConverters());
                switch (statusCode)
                {
                    case ExperimentStatusCode.NotStarted:
                        Assert.AreEqual(AEVAPipelineRunStatusCode.NotStarted, pipelineRunEntity.Status.StatusCode);
                        break;
                    case ExperimentStatusCode.Running:
                        Assert.AreEqual(AEVAPipelineRunStatusCode.Running, pipelineRunEntity.Status.StatusCode);
                        break;
                    case ExperimentStatusCode.Failed:
                        Assert.AreEqual(AEVAPipelineRunStatusCode.Failed, pipelineRunEntity.Status.StatusCode);
                        break;
                    case ExperimentStatusCode.Finished:
                        Assert.AreEqual(AEVAPipelineRunStatusCode.Finished, pipelineRunEntity.Status.StatusCode);
                        break;
                    case ExperimentStatusCode.CancelRequested:
                        Assert.AreEqual(AEVAPipelineRunStatusCode.CancelRequested, pipelineRunEntity.Status.StatusCode);
                        break;
                    case ExperimentStatusCode.Canceled:
                        Assert.AreEqual(AEVAPipelineRunStatusCode.Canceled, pipelineRunEntity.Status.StatusCode);
                        break;
                    case ExperimentStatusCode.Failing:
                        Assert.AreEqual(AEVAPipelineRunStatusCode.Failed, pipelineRunEntity.Status.StatusCode);
                        break;
                    case ExperimentStatusCode.Queued:
                        Assert.AreEqual(AEVAPipelineRunStatusCode.Queued, pipelineRunEntity.Status.StatusCode);
                        break;
                    default:
                        throw new ArgumentException($"experimentstatus {statusCode} doesn' have coresponding pipelinerunstatus");
                }
            }
        }
    }

    /*
     *  Test constract class
     */
    [DataContract]
    public class AEVATestContract
    {
        [DataMember]
        public IEnumerable<int> Code1 { get; set; }

        [DataMember]
        public Dictionary<string, AEVADataLocation> Code2 { get; set; }
    }

    [DataContract]
    public class TestContract
    {
        [DataMember]
        public List<int> Code1 { get; set; }

        [DataMember]
        public IDictionary<string, DataLocation> Code2 { get; set; }
    }
}
