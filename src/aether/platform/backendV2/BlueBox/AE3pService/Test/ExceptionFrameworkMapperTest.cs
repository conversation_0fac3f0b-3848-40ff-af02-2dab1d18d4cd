﻿// <copyright file="ExceptionFrameworkMapperTest.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Aether.Backend.MetaStore.Service;
using Microsoft.Aether.AE3pService.Exceptions;
using Microsoft.Aether.DataContracts.Error;
using Microsoft.MachineLearning.Common.Core.Contracts.Errors;
using Microsoft.MachineLearning.Common.Core.Exceptions;
using Microsoft.RelInfra.Instrumentation.Exceptions;
using Microsoft.RelInfra.Extensions;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;

namespace Microsoft.Aether.AE3pService.Tests
{
    public class ExceptionFrameworkMapperTest
    {
        [Test]
        public void MapToBaseException_FormattableExceptionTest()
        {
            try
            {
                var exp = new HttpRequestDetailException($"{HttpStatusCode.Conflict} - {{error_response}}", HttpStatusCode.Conflict).Format("service error response details");
                throw ExceptionFrameworkMapper.MapToBaseException(exp, new InternalError(), null);
            }
            catch (Exception e)
            {
                Assert.IsTrue(e is BaseException);
                BaseException b = e as BaseException;
                Assert.AreEqual("Conflict - service error response details", b.Message);
                Assert.AreEqual("Conflict - {error_response}", b.Error.Definition.MessageFormat);
                Assert.AreEqual(new Dictionary<string, string>() { { "error_response", "service error response details" } }, b.Error.MessageParameters);
            }
        }

        [Test]
        public void MapToBaseException_BaseExceptionTest()
        {
            try
            {
                var exp = new BaseException(new ArgumentInvalid().ToBaseError(null, "id"));
                throw ExceptionFrameworkMapper.MapToBaseException(exp, new InternalError(), null);
            }
            catch (Exception e)
            {
                Assert.IsTrue(e is BaseException);
                Assert.AreEqual("id is invalid", e.Message);
            }
        }

        [Test]
        public void MapToBaseException_EtagMismatchExceptionTest()
        {
            try
            {
                var exp = new EtagMismatchException("id", "etag", null);
                throw ExceptionFrameworkMapper.MapToBaseException(exp, new InternalError(), null);
            }
            catch (Exception e)
            {
                Assert.IsTrue(e is EtagMismatchException);
                Assert.AreEqual("etag", e.Message);
            }
        }

        [Test]
        public void MapToBaseException_HttpRequestDetailExceptionTest()
        {
            try
            {
                var exp = new HttpRequestDetailException("Conflict", HttpStatusCode.Conflict);
                throw ExceptionFrameworkMapper.MapToBaseException(exp, new InternalError(), null);
            }
            catch (Exception e)
            {
                Assert.IsTrue(e is BaseException);
                Assert.AreEqual("Conflict", e.Message);
            }
        }

        [Test]
        public void MapToBaseException_ServiceInvocationExceptionTest()
        {
            var httpresponse = new HttpResponseMessage(HttpStatusCode.Conflict);
            var exp = new MachineLearning.Common.Core.ServiceInvocationException(null, httpresponse, "Conflict");

            try
            {
                throw ExceptionFrameworkMapper.MapToBaseException(exp, new InternalError(), null);
            }
            catch (Exception e)
            {
                Assert.IsTrue(e is BaseException);
                Assert.AreEqual(exp.Message, e.Message);
            }
        }

        [Test]
        public void MapToBaseException_RefServiceInvocationExceptionTest()
        {
            var innerexp = new HttpRequestDetailException("Conflict", HttpStatusCode.Conflict);
            var exp = new RelInfra.Common.Exceptions.ServiceInvocationException("operation", "service", HttpMethod.Post, innerexp);

            try
            {
                throw ExceptionFrameworkMapper.MapToBaseException(exp, new InternalError(), null);
            }
            catch (Exception e)
            {
                Assert.IsTrue(e is BaseException);
                var bs = (BaseException)e;
                Assert.AreEqual(bs.Error.Definition.Code, new PipelineConflict(null).Code);
                Assert.AreEqual(exp.Message + "; InnerException: Conflict", e.Message);
            }
        }

        [Test]
        public void MapToBaseException_RefServiceInvocationExceptionNonHttpTest()
        {
            var innerexp = new BaseException(new ArgumentInvalid().ToBaseError(null, "id"));
            var exp = new RelInfra.Common.Exceptions.ServiceInvocationException("operation", "service", HttpMethod.Post, innerexp);

            try
            {
                throw ExceptionFrameworkMapper.MapToBaseException(exp, new InternalError(), null);
            }
            catch (Exception e)
            {
                Assert.IsTrue(e is BaseException);
                var bs = (BaseException)e;
                Assert.AreEqual(new PipelineInternalError(null).Code, bs.Error.Definition.Code);
                Assert.AreEqual(exp.Message + "; InnerException: id is invalid", e.Message);
            }
        }

        [Test]
        public void MapToBaseException_RefMetaStoreExceptionTest()
        {
            //var innerexp = new BaseException(new ArgumentInvalid().ToBaseError(null, "id"));
            var errMsg = "Could not find xxx in metastore.";
            var exp = new MetaStoreException(HttpStatusCode.NotFound, errMsg);

            try
            {
                throw ExceptionFrameworkMapper.MapToBaseException(exp, new InternalError(), null);
            }
            catch (Exception e)
            {
                Assert.IsTrue(e is BaseException);
                var bs = (BaseException)e;
                Assert.AreEqual(new PipelineNotFound(null).Code, bs.Error.Definition.Code);
                Assert.AreEqual(errMsg, e.Message);
            }
        }

        [Test]
        public void MapToBaseException_ExceptionTest()
        {
            var exp = new Exception("Error");

            try
            {
                throw ExceptionFrameworkMapper.MapToBaseException(exp, new InternalError(), null);
            }
            catch (Exception e)
            {
                Assert.IsTrue(e is BaseException);
                var bs = (BaseException)e;
                Assert.AreEqual(new InternalError().Code, bs.Error.Definition.Code);
                Assert.AreEqual("Unexpected error occurred", e.Message);
            }
        }

        [Test]
        public void MapToBaseException_SelfDefinedExceptionTest()
        {
            var exp = new Exception("Error");

            try
            {
                throw ExceptionFrameworkMapper.MapToBaseException(exp, new ErrorInGetAllDataTypes(ExceptionFrameworkMapper.MessageFormat(exp)), null);
            }
            catch (Exception e)
            {
                Assert.IsTrue(e is BaseException);
                var bs = (BaseException)e;
                Assert.AreEqual(new ErrorInGetAllDataTypes(string.Empty).Code, bs.Error.Definition.Code);
                Assert.AreEqual("Unexpected System Error occured when getting all DataTypes. The error message is: 'Error'.", e.Message);
            }
        }

        [Test]
        public void MapToPipelineBaseException_RequestEntityTooLargeTest()
        {
            var exp = new Exception("Error");

            try
            {
                throw ExceptionFrameworkMapper.MapToPipelineBaseException(HttpStatusCode.RequestEntityTooLarge, "error", exp, "ta");
            }
            catch (Exception e)
            {
                Assert.IsTrue(e is BaseException);
                var bs = (BaseException)e;
                Assert.AreEqual(new PipelineRequestEntityTooLarge(null).Code, bs.Error.Definition.Code);
                Assert.AreEqual("error", e.Message);
            }
        }

        [Test]
        public void MapToPipelineBaseException_ConflictTest()
        {
            var exp = new Exception("Error");

            try
            {
                throw ExceptionFrameworkMapper.MapToPipelineBaseException(HttpStatusCode.Conflict, "error", exp, "ta");
            }
            catch (Exception e)
            {
                Assert.IsTrue(e is BaseException);
                var bs = (BaseException)e;
                Assert.AreEqual(new PipelineConflict(null).Code, bs.Error.Definition.Code);
                Assert.AreEqual("error", e.Message);
            }
        }

        [Test]
        public void MapToPipelineBaseException_UnauthorizedTest()
        {
            var exp = new Exception("Error");

            try
            {
                throw ExceptionFrameworkMapper.MapToPipelineBaseException(HttpStatusCode.Unauthorized, "error", exp, "ta");
            }
            catch (Exception e)
            {
                Assert.IsTrue(e is BaseException);
                var bs = (BaseException)e;
                Assert.AreEqual(new PipelineAuthentication(null).Code, bs.Error.Definition.Code);
                Assert.AreEqual("error", e.Message);
            }
        }

        [Test]
        public void MapToPipelineBaseException_ForbiddenTest()
        {
            var exp = new Exception("Error");

            try
            {
                throw ExceptionFrameworkMapper.MapToPipelineBaseException(HttpStatusCode.Forbidden, "error", exp, "ta");
            }
            catch (Exception e)
            {
                Assert.IsTrue(e is BaseException);
                var bs = (BaseException)e;
                Assert.AreEqual(new PipelineAuthorization(null).Code, bs.Error.Definition.Code);
                Assert.AreEqual("error", e.Message);
            }
        }

        [Test]
        public void MapToPipelineBaseException_TooManyRequestsTest()
        {
            var exp = new Exception("Error");

            try
            {
                throw ExceptionFrameworkMapper.MapToPipelineBaseException(HttpStatusCode.TooManyRequests, "error", exp, "ta");
            }
            catch (Exception e)
            {
                Assert.IsTrue(e is BaseException);
                var bs = (BaseException)e;
                Assert.AreEqual(new PipelineTooManyRequests(null).Code, bs.Error.Definition.Code);
                Assert.AreEqual("error", e.Message);
            }
        }

        [Test]
        public void MapToPipelineBaseException_NotFoundTest()
        {
            var exp = new Exception("Error");

            try
            {
                throw ExceptionFrameworkMapper.MapToPipelineBaseException(HttpStatusCode.NotFound, "error", exp, "ta");
            }
            catch (Exception e)
            {
                Assert.IsTrue(e is BaseException);
                var bs = (BaseException)e;
                Assert.AreEqual(new PipelineNotFound(null).Code, bs.Error.Definition.Code);
                Assert.AreEqual("error", e.Message);
            }
        }

        [Test]
        public void MapToPipelineBaseException_LengthRequiredTest()
        {
            var exp = new Exception("Error");

            try
            {
                throw ExceptionFrameworkMapper.MapToPipelineBaseException(HttpStatusCode.LengthRequired, "error", exp, "ta");
            }
            catch (Exception e)
            {
                Assert.IsTrue(e is BaseException);
                var bs = (BaseException)e;
                Assert.AreEqual(new PipelineLengthRequired(null).Code, bs.Error.Definition.Code);
                Assert.AreEqual("error", e.Message);
            }
        }

        [Test]
        public void MapToPipelineBaseException_PreconditionFailedTest()
        {
            var exp = new Exception("Error");

            try
            {
                throw ExceptionFrameworkMapper.MapToPipelineBaseException(HttpStatusCode.PreconditionFailed, "error", exp, "ta");
            }
            catch (Exception e)
            {
                Assert.IsTrue(e is BaseException);
                var bs = (BaseException)e;
                Assert.AreEqual(new PipelinePreconditionFailed(null).Code, bs.Error.Definition.Code);
                Assert.AreEqual("error", e.Message);
            }
        }

        [Test]
        public void MapToPipelineBaseException_UnsupportedMediaTypeTest()
        {
            var exp = new Exception("Error");

            try
            {
                throw ExceptionFrameworkMapper.MapToPipelineBaseException(HttpStatusCode.UnsupportedMediaType, "error", exp, "ta");
            }
            catch (Exception e)
            {
                Assert.IsTrue(e is BaseException);
                var bs = (BaseException)e;
                Assert.AreEqual(new PipelineUnsupportedMediaType(null).Code, bs.Error.Definition.Code);
                Assert.AreEqual("error", e.Message);
            }
        }

        [Test]
        public void MapToPipelineBaseException_MisdirectedRequestTest()
        {
            var exp = new Exception("Error");

            try
            {
                throw ExceptionFrameworkMapper.MapToPipelineBaseException(HttpStatusCode.MisdirectedRequest, "error", exp, "ta");
            }
            catch (Exception e)
            {
                Assert.IsTrue(e is BaseException);
                var bs = (BaseException)e;
                Assert.AreEqual(new PipelineMisdirectedRequest(null).Code, bs.Error.Definition.Code);
                Assert.AreEqual("error", e.Message);
            }
        }

        [Test]
        public void MapToPipelineBaseException_BadRequestTest()
        {
            var exp = new Exception("Error");

            try
            {
                throw ExceptionFrameworkMapper.MapToPipelineBaseException(HttpStatusCode.BadRequest, "error", exp, "ta");
            }
            catch (Exception e)
            {
                Assert.IsTrue(e is BaseException);
                var bs = (BaseException)e;
                Assert.AreEqual(new PipelineBadArgument(null).Code, bs.Error.Definition.Code);
                Assert.AreEqual("error", e.Message);
            }
        }

        [Test]
        public void MapToPipelineBaseException_ClientClosedRequestTest()
        {
            var exp = new Exception("Error");

            try
            {
                throw ExceptionFrameworkMapper.MapToPipelineBaseException((HttpStatusCode)499, "error", exp, "ta");
            }
            catch (Exception e)
            {
                Assert.IsTrue(e is BaseException);
                var bs = (BaseException)e;
                Assert.AreEqual(new PipelineClientClosedRequest(null).Code, bs.Error.Definition.Code);
                Assert.AreEqual("error", e.Message);
            }
        }

        [Test]
        public void MapToPipelineBaseException_MethodNotAllowedTest()
        {
            var exp = new Exception("Error");

            try
            {
                throw ExceptionFrameworkMapper.MapToPipelineBaseException(HttpStatusCode.MethodNotAllowed, "error", exp, "ta");
            }
            catch (Exception e)
            {
                Assert.IsTrue(e is BaseException);
                var bs = (BaseException)e;
                Assert.AreEqual(new PipelineMethodNotAllowed(null).Code, bs.Error.Definition.Code);
                Assert.AreEqual("error", e.Message);
            }
        }

        [Test]
        public void MapToPipelineBaseException_BadGatewayTest()
        {
            var exp = new Exception("Error");

            try
            {
                throw ExceptionFrameworkMapper.MapToPipelineBaseException(HttpStatusCode.BadGateway, "error", exp, "ta");
            }
            catch (Exception e)
            {
                Assert.IsTrue(e is BaseException);
                var bs = (BaseException)e;
                Assert.AreEqual(new PipelineBadGateway(null).Code, bs.Error.Definition.Code);
                Assert.AreEqual("error", e.Message);
            }
        }

        [Test]
        public void MapToPipelineBaseException_ServiceUnavailableTest()
        {
            var exp = new Exception("Error");

            try
            {
                throw ExceptionFrameworkMapper.MapToPipelineBaseException(HttpStatusCode.ServiceUnavailable, "error", exp, "ta");
            }
            catch (Exception e)
            {
                Assert.IsTrue(e is BaseException);
                var bs = (BaseException)e;
                Assert.AreEqual(new PipelineServiceUnavailable(null).Code, bs.Error.Definition.Code);
                Assert.AreEqual("error", e.Message);
            }
        }

        [Test]
        public void MapToPipelineBaseException_GatewayTimeoutTest()
        {
            var exp = new Exception("Error");

            try
            {
                throw ExceptionFrameworkMapper.MapToPipelineBaseException(HttpStatusCode.GatewayTimeout, "error", exp, "ta");
            }
            catch (Exception e)
            {
                Assert.IsTrue(e is BaseException);
                var bs = (BaseException)e;
                Assert.AreEqual(new PipelineGatewayTimeout(null).Code, bs.Error.Definition.Code);
                Assert.AreEqual("error", e.Message);
            }
        }

        [Test]
        public void MapToPipelineBaseException_FailRouteRequestTest()
        {
            var exp = new Exception("Error");

            try
            {
                throw ExceptionFrameworkMapper.MapToPipelineBaseException((HttpStatusCode)530, "error", exp, "ta");
            }
            catch (Exception e)
            {
                Assert.IsTrue(e is BaseException);
                var bs = (BaseException)e;
                Assert.AreEqual(new PipelineFailRouteRequest(null).Code, bs.Error.Definition.Code);
                Assert.AreEqual("error", e.Message);
            }
        }

        [Test]
        public void MapToPipelineBaseException_DefaultTest()
        {
            var exp = new Exception("Error");

            try
            {
                throw ExceptionFrameworkMapper.MapToPipelineBaseException(000, "error", exp, "ta");
            }
            catch (Exception e)
            {
                Assert.IsTrue(e is BaseException);
                var bs = (BaseException)e;
                Assert.AreEqual(new PipelineInternalError(null).Code, bs.Error.Definition.Code);
                Assert.AreEqual("error", e.Message);
            }
        }
    }
}
