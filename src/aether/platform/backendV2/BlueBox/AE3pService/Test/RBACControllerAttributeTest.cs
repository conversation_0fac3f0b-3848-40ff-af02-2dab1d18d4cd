﻿// <copyright file="RBACControllerAttributeTest.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using Microsoft.Aether.HdiCloud.Common;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.MachineLearning.Common.WebApi.Authorization.Attribute;
using Microsoft.Rest;
using Microsoft.VisualStudio.TestTools.UnitTesting;

namespace Microsoft.Aether.AE3pService.Tests
{
    [TestClass]
    public class RBACControllerAttributeTests
    {
        private Assembly _ae3pServiceAssembly;
        private HashSet<Type> _typesToExclude;

        public RBACControllerAttributeTests()
        {
            _ae3pServiceAssembly = Assembly.GetAssembly(typeof(AE3pServiceConfiguration));
            _typesToExclude = new HashSet<Type>()
            {
                typeof(AmlModulesBaseController),
                typeof(AmlModulesRepositoryController),
                typeof(AmlModuleVersionsBaseController),
                typeof(AmlModuleVersionsRepositoryController),
                typeof(BaseController),
                typeof(DataTypesBaseController),
                typeof(DataTypesRepositoryController),
                typeof(KeepaliveController),
                typeof(ModulesBaseController),
                typeof(ModulesRepositoryController),
                typeof(RootController)
            };
        }

        [TestMethod]
        public void AuthorizeActionAttributeExistenceTest()
        {
            var controllers = _ae3pServiceAssembly.GetTypes().Where(type => typeof(Controller).IsAssignableFrom(type) && !_typesToExclude.Contains(type));
            foreach (var controller in controllers)
            {
                ValidateControllerActionAuthorizeMethods(controller);
            }
        }

        private void ValidateControllerActionAuthorizeMethods(Type controllerClass)
        {
            List<string> methodsWithMissingActionAttributes = GetMethodsWithMissingActionAuthorizeAttributes(controllerClass);
            if (methodsWithMissingActionAttributes != null && methodsWithMissingActionAttributes.Count() > 0)
            {
                throw new ValidationException($"{string.Join(",", methodsWithMissingActionAttributes)} methods of Controller {controllerClass.Name} are missing ActionAuthorize Atttributes.");
            }
        }

        private List<string> GetMethodsWithMissingActionAuthorizeAttributes(Type controllerClass)
        {
            var controllers = _ae3pServiceAssembly.GetTypes().Where(type => controllerClass.IsAssignableFrom(type));

            List<string> methodNames = new List<string>();

            foreach (var controller in controllers)
            {

                IEnumerable<MethodInfo> methodsWithMissingActionAttributes = controller.GetMethods(BindingFlags.Public | BindingFlags.Instance | BindingFlags.DeclaredOnly)
                                                                                .Where(m => Attribute.GetCustomAttribute(m, typeof(ActionAuthorizeAttribute)) == null &&
                                                                                            (Attribute.GetCustomAttribute(m, typeof(AuthorizeAttribute)) == null ||
                                                                                            ((AuthorizeAttribute)Attribute.GetCustomAttribute(m, typeof(AuthorizeAttribute))).Policy != "S2S"));

                if (methodsWithMissingActionAttributes != null)
                {
                    foreach (MethodInfo methodInfo in methodsWithMissingActionAttributes)
                    {
                        if (Attribute.GetCustomAttribute(methodInfo, typeof(RouteAttribute)) != null)
                        {
                            methodNames.Add(methodInfo.Name);
                        }
                    }
                }
            }

            return methodNames;
        }
    }
}
