<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <AssemblyName>Microsoft.Aether.AE3pService.Tests</AssemblyName>
    <IsPackable>false</IsPackable>
    <CodeAnalysisRuleSet>..\..\..\..\ae3p.analysis.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <Import Project="..\..\..\..\QTest.props" />
  <ItemGroup>
    <PackageReference Include="Microsoft.NET.Test.Sdk" />
    <PackageReference Include="MSTest.TestAdapter" />
    <PackageReference Include="MSTest.TestFramework" />
    <PackageReference Include="System.Net.Security" />
    <PackageReference Include="NUnit" />
    <PackageReference Include="NUnit3TestAdapter" />
    <PackageReference Include="Moq" />
    <PackageReference Include="FluentAssertions" />
    <PackageReference Include="coverlet.collector" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\OboTokenServiceClient\Client\Microsoft.Aether.BlueBox.OboTokenServiceClient.csproj" />
    <ProjectReference Include="..\Service\Microsoft.Aether.AE3pService.csproj" />
  </ItemGroup>
  <ItemGroup>
    <None Update="ClientInitialize\appsettings.test.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>
</Project>