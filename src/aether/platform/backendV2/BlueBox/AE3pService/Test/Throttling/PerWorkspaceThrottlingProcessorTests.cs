﻿// <copyright file="PerWorkspaceThrottlingProcessorTests.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using FluentAssertions;
using Microsoft.Aether.AE3pService.Throttling;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Abstractions;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.AspNetCore.Routing;
using Microsoft.RelInfra.Common.Configuration;
using Microsoft.RelInfra.Common.Secrets;
using Moq;
using NUnit.Framework;

namespace Microsoft.Aether.AE3pService.Tests.Throttling
{
    public class PerWorkspaceThrottlingProcessorTests
    {
        private readonly MockRepository _repository;
        private AE3pServiceConfiguration _fakeAE3pServiceConfiguration;
        private PerWorkspaceThrottlingProcessor _processor;
        private ActionExecutingContext _actionExecutingContext;
        private string _throttling_key = ThrottlingLimitKeyConstants.SubmitRunPerWorkspacePerWindow;
        private Guid _subscriptionId = Guid.NewGuid();
        private string _resourceGroupName = Guid.NewGuid().ToString();
        private string _workspaceName = Guid.NewGuid().ToString();

        public PerWorkspaceThrottlingProcessorTests()
        {
            _repository = new MockRepository(MockBehavior.Loose) { DefaultValue = DefaultValue.Mock, CallBase = true };
        }

        [SetUp]
        public void Init()
        {
            var httpContext = _repository.Create<HttpContext>().Object;
            httpContext.TraceIdentifier = "[PLACEHOLDER]";
            _fakeAE3pServiceConfiguration = _repository.Create<AE3pServiceConfiguration>(
                    GetConfiguration(),
                    _repository.Create<SecretProvider>(null, null).Object).Object;
            ActionContext _actionContext = _repository.Create<ActionContext>().Object;
            _actionContext.HttpContext = httpContext;
            _actionContext.RouteData = _repository.Create<RouteData>().Object;
            _actionContext.ActionDescriptor = _repository.Create<ActionDescriptor>().Object;
            _actionContext.ActionDescriptor.RouteValues = new Dictionary<string, string>();
            _actionContext.ActionDescriptor.RouteValues["Action"] = "actionValue";
            _actionContext.ActionDescriptor.RouteValues["Controller"] = "controllerValue";

            var filters = _repository.Create<List<IFilterMetadata>>().Object;
            var obj = _repository.Create<object>().Object;
            var actionArguments = new Dictionary<string, object>()
            {
                { "subscriptionId", _subscriptionId },
                { "resourceGroupName", _resourceGroupName },
                { "workspaceName", _workspaceName }
            };
            _actionExecutingContext = new ActionExecutingContext(_actionContext, filters, actionArguments, obj);
            _processor = new PerWorkspaceThrottlingProcessor(_throttling_key, true);
        }

        private static IRelInfraConfiguration GetConfiguration()
        {
            IDictionary<string, string> settings = new Dictionary<string, string>()
            {
                {"Throttling.EnableThrottling", "true"}
            };
            return new DynamicConfigurationFlexMock(settings);
        }

        [Test]
        [Category("GetThrottlingContextPerWorkspace")]
        public async Task TestPerWorkspaceThrottlingProcessor_Success()
        {
            var result = await _processor.GetThrottlingContextPerWorkspace(_actionExecutingContext).ConfigureAwait(false);
            result.Count.Equals(1);
            var throttlingContext = result.First();
            throttlingContext.WorkspaceId.Should().BeNull();
            var contextScope = throttlingContext.WorkspaceReference.AmlWorkspaceArmScope;
            contextScope.SubscriptionId.Should().Be(_subscriptionId);
            contextScope.ResourceGroup.Should().Be(_resourceGroupName);
            contextScope.WorkspaceName().Should().Be(_workspaceName);
            throttlingContext.ThrottlingContextForApplicableLimitKeys.Count.Should().Be(1);
            var limitKey = throttlingContext.ThrottlingContextForApplicableLimitKeys.First();
            limitKey.LimitKey.Should().Be(_throttling_key);
            limitKey.ThrottlingContextForEntityContainers.Count.Should().Be(1);
            var container = limitKey.ThrottlingContextForEntityContainers.First();
            container.EntityCount.Should().Be(1);
            container.EntityContainer.Should().Be($"{_throttling_key}:{_subscriptionId}:{_resourceGroupName}:{_workspaceName}");
        }

        [Test]
        [Category("GetLimitKeys")]
        public void TestGetLimitKeys_ReturnSuccess()
        {
            var result = _processor.GetLimitKeys();
            List<string> keyList = new List<string>(result);
            Assert.True(keyList.Contains(_throttling_key));
        }

        [Test]
        [Category("IsThrottlingApplicable")]
        public void IsThrottlingApplicable_ReturnSuccess()
        {
            bool enabled = true;
            var result = _processor.IsThrottlingApplicable(_actionExecutingContext);
            Assert.AreEqual(result, enabled);
        }
    }
}
