﻿{
  "AmlDataStoreClient": {
    "AmlServicesEndpoint": "https://ml.azure.com",
    "CreateStorePath": "datastore/v1.0/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.MachineLearningServices/workspaces/{workspaceName}/datastores",
    "DataStorePath": "datastore/v1.0/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.MachineLearningServices/workspaces/{workspaceName}/datastores/{name}",
    "Timeout": "00:00:15",
    "RetryCount": "3"
  },
  "AmlDatasetConfig": {
    "DatasetInternalEndpoint": "https://ml.azure.com",
    "Timeout": "00:00:15"
  },
  "AmlSnapShotClient": {
    "AmlServicesEndpoint": "https://ml.azure.com",
    "SnapShotPath": "content/v1.0/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.MachineLearningServices/workspaces/{workspaceName}/snapshots/{snapshotId}",
    "SnapshotUrlsPath": "content/v1.0/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.MachineLearningServices/workspaces/{workspaceName}/snapshots/{snapshotId}/sas",
    "SnapshotV2CreatePath": "content/v2.0/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.MachineLearningServices/workspaces/{workspaceName}/snapshots/{snapshotId}",
    "SnapshotV2GetBlobUrisPath": "content/v2.0/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.MachineLearningServices/workspaces/{workspaceName}/snapshots/getblob",
    "SnapshotV2BuildSnapshotZipPath": "content/v2.0/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.MachineLearningServices/workspaces/{workspaceName}/snapshots/{snapshotId}/zip",
    "EnableV2Api": true,
    "RetryCountForBlobUpload": "3",
    "Timeout": "00:00:15",
    "RetryCount": "3"
  },
  "WorkspaceResourcesClient": {
    "WorkspaceRpServiceEndpoint": "https://ml.azure.com",
    "ComputeResourcesPath": "rp/workspaces/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.MachineLearningServices/workspaces/{workspaceName}/computes/{computeName}",
    "FullComputeResourcesPath": "https://management.{{k8sCloudEndpoint}}/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.MachineLearningServices/workspaces/{workspaceName}/computes/{computeName}?api-version=2019-06-01",
    "ComputeSecretsPath": "rp/workspaces/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.MachineLearningServices/workspaces/{workspaceName}/computes/{computeName}/listKeys",
    "WorkspacePath": "rp/workspaces/subscriptions/{subscriptionId}/resourceGroupName/{resourceGroupName}/workspaces/{workspaceName}",
    "WorkspaceRPPath": "rp/workspaces/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.MachineLearningServices/workspaces/{workspaceName}",
    "WorkspaceStorageMsiPath": "rp/workspaces/subscriptions/{subscriptionId}/workspaces/{workspaceId}/storage/token",
    "WorkspaceCmkStorageMsiPath": "rp/workspaces/{workspaceId}/cmkstorage/token",
    "WorkspaceSubscriptionStatePath": "rp/workspaces/subscriptions/{subscriptionId}/state",
    "StorageConnectionSecretsPath": "rp/workspaces/{workspaceId}/connectionsecrets",
    "DefaultBlobStorageConnectionSecrets": "teams/accounts/subscriptions/{subscriptionId}/accounts/{workspaceId}/storage",
    "UseWorkspaceStorage": true,
    "Timeout": "00:00:15",
    "RetryCount": "3"
  },
  "RepositoryResourcesClient": {
    "RepositoryRpServiceEndpoint": "https://ml.azure.com",
    "RepositoryArmRoute": "repositoryrp/v1.0/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.MachineLearningServices/repositories",
    "RepositoryNonArmRoute": "repositoryrp/v1.0",
    "Timeout": "00:00:15",
    "RetryCount": "3"
  },
  "MmsServiceClient": {
    "MmsServiceEndpoint": "https://ml.azure.com",
    "CreateBatchInferencingServicePath": "/modelmanagement/v1.0/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.MachineLearningServices/workspaces/{workspaceName}/services/register?api-version=2018-03-01-preview",
    "GetBatchInferencingServicePath": "/modelmanagement/v1.0/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.MachineLearningServices/workspaces/{workspaceName}/services/{serviceName}?api-version=2018-03-01-preview",
    "Timeout": "00:00:15",
    "RetryCount": "3"
  },
  "CredentialServiceClient": {
    "CredentialsServiceEndpoint": "https://ml.azure.com",
    "CreateCredentialPath": "credential/v1.0/subscriptions/{0}/resourceGroups/{1}/providers/Microsoft.MachineLearningServices/workspaces/{2}/secrets?api-version={3}",
    "GetCredentialPath": "credential/v1.0/subscriptions/{0}/resourceGroups/{1}/providers/Microsoft.MachineLearningServices/workspaces/{2}/secrets/{3}?api-version={4}",
    "CredentialsServiceApiVersion": "2018-03-01-preview",
    "MaxRetriesToUploadSecret": "3",
    "TimeoutToUploadSecretInMs": "2000",
    "Timeout": "00:00:15"
  },
  "RunHistoryClient": {
    "RunHistoryServiceEndpoint": "https://ml.azure.com",
    "IndexServiceEndpoint": "https://ml.azure.com",
    "AetherServiceEndpoint": "https://ml.azure.com",
    "RunPath": "history/v1.0/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.MachineLearningServices/workspaces/{workspaceName}/experiments/{experimentName}/runs/{runId}",
    "UpdateRunStatusPath": "history/v1.0/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.MachineLearningServices/workspaces/{workspaceName}/experiments/{experimentName}/runs/{runId}/events",
    "GetRunDetailsPath": "history/v1.0/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.MachineLearningServices/workspaces/{workspaceName}/experiments/{experimentName}/runs/{runId}/details",
    "PostRunStatusSpan": "history/v1.0/private/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.MachineLearningServices/workspaces/{workspaceName}/runs/{runId}/spans",
    "EnableDataContainer": true,
    "MaxDegreeOfParallelismForMetrics": "5",
    "GetEntitiesFromIndex": "",
    "Timeout": "00:00:15",
    "RetryCount": "3"
  },
  "AmlRunMetricClient": {
    "MetricServiceEndpoint": "https://ml.azure.com",
    "UpdateRunMetricPath": "metric/v2.0/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.MachineLearningServices/workspaces/{workspaceName}/runs/{runId}/batch",
    "RetrieveFullFidelityMetricPath": "metric/v2.0/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.MachineLearningServices/workspaces/{workspaceName}/runs/{runId}/full",
    "ListMetricsPath": "metric/v2.0/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.MachineLearningServices/workspaces/{workspaceName}/runs/{runId}/list",
    "Timeout": "00:00:15",
    "RetryCount": "3"
  },
  "SchedulerClient": {
    "SchedulerBaseUrl": "https://scheduler",
    "Region": "westus",
    "Limit": 100000,
    "ScheduleLimit": {
      "b8c23406-f9b5-4ccb-8a65-a8cb5dcd6a5a": 3000000,
      "4faaaf21-663f-4391-96fd-47197c630979": 300000,
      "2ad17db4-e26d-4c9e-999e-adae9182530c": 200000,
      "e8e7a61c-4699-4da3-8ad0-b46c14565627": 200000,
      "619f1ab9-764e-4308-8360-da885b1c9eaa": 600000,
      "54f8d3a0-969e-4f33-a517-0f2ee2fbdf3e": 500000,
      "7d4f854c-d94a-4b1a-a1e0-60fb42be338c": 500000
    }
  },
  "OtelAudit": {
    "ServiceTreeId": "2759ddd7-19db-4c59-9824-c2e7a4c6c2a8"
  }
}
