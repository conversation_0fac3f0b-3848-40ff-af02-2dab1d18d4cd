﻿// <copyright file="ClientInitializeTest.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using Microsoft.Aether.BackendCommon.ClusterHealthPoller;
using Microsoft.Aether.BlueBox.CredentialServiceClient;
using Microsoft.Aether.BlueBox.DataStoreClient;
using Microsoft.Aether.BlueBox.MmsServiceClient;
using Microsoft.Aether.BlueBox.RepositoryResourcesClient;
using Microsoft.Aether.BlueBox.RunHistoryClient;
using Microsoft.Aether.BlueBox.SchedulerClient;
using Microsoft.Aether.BlueBox.SnapshotClient;
using Microsoft.Aether.BlueBox.WorkspaceResourcesClient;
using Microsoft.Aether.S2S.Common;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using Microsoft.MachineLearning.Dataset.Client;
using Microsoft.RelInfra.Instrumentation;
using Microsoft.RelInfra.Instrumentation.Test.Mocks;
using NUnit.Framework;

namespace Microsoft.Aether.AE3pService.Tests.ClientInitialize
{
    [TestFixture]
    public class ClientInitializeTest
    {
        private ServiceCollection sc;
        private IConfigurationRoot configurationRoot;
        private CounterManager counterManager;
        private S2STokenProvider s2sProvider;
        private IClusterHealthPoller clusterHealthPoller;

        [OneTimeSetUp]
        public void TestSetup()
        {
            sc = new ServiceCollection();
            configurationRoot = new ConfigurationBuilder()
                .AddJsonFile(".\\ClientInitialize\\appsettings.test.json")
                .Build();
            counterManager = new CounterManager("onebox", new MockCounterFactory());
            s2sProvider = new S2STokenProvider(null, string.Empty, string.Empty, counterManager);
            clusterHealthPoller = new ClusterHealthPoller();
        }

        [Test]
        public void TestInitializeWithDI_DataStoreClient()
        {
            sc.Configure<AmlDataStoreConfig>(configurationRoot.GetSection("AmlDataStoreClient"));
            sc.AddSingleton(counterManager);
            sc.AddSingleton<IS2STokenProvider>(s2sProvider);
            sc.AddSingleton<IDataStoreClient, DataStoreClient>();
            var sp = sc.BuildServiceProvider();
            var ds = sp.GetRequiredService<IDataStoreClient>();
            NUnit.Framework.Assert.IsNotNull(ds);
        }

        [Test]
        public void TestInitializeWithSS_SnapShotClient()
        {
            sc.Configure<AmlSnapshotConfig>(configurationRoot.GetSection("AmlSnapShotClient"));
            sc.AddSingleton(counterManager);
            sc.AddSingleton<IS2STokenProvider>(s2sProvider);
            sc.AddSingleton<ISnapshotClient, SnapshotClient>();
            sc.AddSingleton(clusterHealthPoller);
            var sp = sc.BuildServiceProvider();
            var ds = sp.GetRequiredService<ISnapshotClient>();
            NUnit.Framework.Assert.IsNotNull(ds);
        }

        [Test]
        public void TestInitializeWithWR_WorkspaceResourcesClient()
        {
            sc.Configure<WorkspaceResourcesConfig>(configurationRoot.GetSection("WorkspaceResourcesClient"));
            sc.AddSingleton(counterManager);
            sc.AddSingleton<IS2STokenProvider>(s2sProvider);
            sc.AddSingleton<IWorkspaceResourcesClient, WorkspaceResourcesClient>();
            var sp = sc.BuildServiceProvider();
            var ds = sp.GetRequiredService<IWorkspaceResourcesClient>();
            NUnit.Framework.Assert.IsNotNull(ds);
        }

        [Test]
        public void TestInitializeWithRR_RepositoryResourcesClient()
        {
            sc.Configure<RepositoryResourcesConfiguration>(configurationRoot.GetSection("RepositoryResourcesClient"));
            sc.AddSingleton(counterManager);
            sc.AddSingleton<IS2STokenProvider>(s2sProvider);
            sc.AddSingleton<IRepositoryResourcesClient, RepositoryResourcesClient>();
            var sp = sc.BuildServiceProvider();
            var ds = sp.GetRequiredService<IRepositoryResourcesClient>();
            NUnit.Framework.Assert.IsNotNull(ds);
        }

        [Test]
        public void TestInitializeWithMM_MmsServiceClient()
        {
            sc.Configure<MmsServiceConfig>(configurationRoot.GetSection("MmsServiceClient"));
            sc.AddSingleton(counterManager);
            sc.AddSingleton<IS2STokenProvider>(s2sProvider);
            sc.AddSingleton<IMmsServiceClient, MmsServiceClient>();
            var sp = sc.BuildServiceProvider();
            var ds = sp.GetRequiredService<IMmsServiceClient>();
            NUnit.Framework.Assert.IsNotNull(ds);
        }

        [Test]
        public void TestInitializeWithCS_CredentialServiceClient()
        {
            sc.Configure<CredentialServiceConfig>(configurationRoot.GetSection("CredentialServiceClient"));
            sc.AddSingleton(counterManager);
            sc.AddSingleton<IS2STokenProvider>(s2sProvider);
            sc.AddSingleton<ICredentialServiceClient, CredentialServiceClient>();
            var sp = sc.BuildServiceProvider();
            var ds = sp.GetRequiredService<ICredentialServiceClient>();
            NUnit.Framework.Assert.IsNotNull(ds);
        }

        [Test]
        public void TestInitializeWithRH_RunHistoryClient()
        {
            var temp = configurationRoot.GetSection("RunHistoryClient").Get<AmlRunHistoryConfig>();
            sc.Configure<AmlRunHistoryConfig>(configurationRoot.GetSection("RunHistoryClient"));
            sc.AddSingleton(counterManager);
            sc.AddSingleton<IS2STokenProvider>(s2sProvider);
            sc.AddSingleton<IRunHistoryClient, RunHistoryClient>();
            sc.AddSingleton<IAzureMachineLearningDatasetManagementClientFactory>(sp =>
            {
                return new AzureMachineLearningDatasetManagementClientFactory(
                    handler: new MessageHandlerWithAuth(sp.GetRequiredService<IS2STokenProvider>()),
                    endpoint: new Uri(configurationRoot.GetValue<string>("AmlDatasetConfig:DatasetInternalEndpoint")),
                    timeout: configurationRoot.GetValue<TimeSpan>("AmlDatasetConfig:Timeout"));
            });
            sc.Configure<AmlRunMetricServiceConfig>(configurationRoot.GetSection("AmlRunMetricClient"));
            sc.AddSingleton<IRunMetricClient, AmlRunMetricClient>();
            var sp = sc.BuildServiceProvider();
            var ds = sp.GetRequiredService<IRunHistoryClient>();
            NUnit.Framework.Assert.IsNotNull(ds);
        }

        [Test]
        public void TestInitializeWithSS_SchedulerClient()
        {
            sc.Configure<SchedulerClientConfig>(configurationRoot.GetSection("SchedulerClient"));
            sc.AddSingleton(counterManager);
            sc.AddSingleton<IS2STokenProvider>(s2sProvider);
            sc.AddSingleton<SchedulerClient>();
            var sp = sc.BuildServiceProvider();
            var ds = sp.GetRequiredService<SchedulerClient>();
            NUnit.Framework.Assert.IsNotNull(ds);
            var config = sp.GetRequiredService<IOptionsMonitor<SchedulerClientConfig>>();
            Assert.AreEqual(100000, config.CurrentValue.SubscriptionScheduleLimit("fakesubId"), "Unspecified subscriptionId should return default limit");
            Assert.AreEqual(3000000, config.CurrentValue.SubscriptionScheduleLimit("b8c23406-f9b5-4ccb-8a65-a8cb5dcd6a5a"), "Specified subscriptionId should return configured limit");
        }

        [Test]
        public void TestInitializeWithAMLRMC_AmlRunMetricClient()
        {
            sc.Configure<AmlRunMetricServiceConfig>(configurationRoot.GetSection("AmlRunMetricClient"));
            sc.AddSingleton(counterManager);
            sc.AddSingleton<IS2STokenProvider>(s2sProvider);
            sc.AddSingleton<IRunMetricClient, AmlRunMetricClient>();
            var sp = sc.BuildServiceProvider();
            var ds = sp.GetRequiredService<IRunMetricClient>();
            NUnit.Framework.Assert.IsNotNull(ds);
        }
    }
}
