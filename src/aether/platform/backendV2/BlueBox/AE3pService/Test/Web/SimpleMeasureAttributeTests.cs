﻿// <copyright file="SimpleMeasureAttributeTests.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Abstractions;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.AspNetCore.Routing;
using Microsoft.MachineLearning.Common.WebApi.Exceptions;
using Microsoft.RelInfra.Instrumentation;
using Microsoft.RelInfra.Instrumentation.Test.Mocks;
using Moq;
using NUnit.Framework;
using System;
using System.Collections.Generic;

namespace Microsoft.Aether.AE3pService.Tests.Web
{
    [TestFixture]
    public class SimpleMeasureAttributeTests
    {
        private readonly MockRepository _repository;
        private readonly SimpleMeasureAttribute _simpleMeasureAttribute;
        private readonly CounterManager _counterManager;
        private readonly ICounterFactory _counterFactory;
        private ActionContext _actionContext;
        private bool _called_CreateRateCounter;

        public SimpleMeasureAttributeTests()
        {
            _repository = new MockRepository(MockBehavior.Loose) { DefaultValue = DefaultValue.Mock, CallBase = true };
            _counterFactory = _repository.Create<ICounterFactory>().Object;
            _counterManager = new CounterManager("testServiceName", _counterFactory);
            _simpleMeasureAttribute = new SimpleMeasureAttribute(_counterManager);
        }

        [SetUp]
        public void Init()
        {
            var httpContext = _repository.Create<HttpContext>().Object;
            httpContext.TraceIdentifier = "[PLACEHOLDER]";
            _actionContext = _repository.Create<ActionContext>().Object;
            _actionContext.HttpContext = httpContext;
            _actionContext.RouteData = _repository.Create<RouteData>().Object;
            _actionContext.ActionDescriptor = _repository.Create<ActionDescriptor>().Object;
            _actionContext.ActionDescriptor.RouteValues = new Dictionary<string, string>();
            _actionContext.ActionDescriptor.RouteValues["Action"] = "testValue";

            ICounter counter = _repository.Create<MockCounter>("test", "Testcounter", CounterType.Rate).Object;
            _called_CreateRateCounter = false;
            Mock.Get(_counterFactory)
                .Setup(controller => controller.CreateRateCounter(
                              It.IsAny<string>(),
                              It.IsAny<string>()))
                .Returns(counter)
                .Callback(() => _called_CreateRateCounter = true);
        }

        [Test]
        public void OnActionExecuting_ExpectNormalOutput()
        {
            _called_CreateRateCounter = false;
            var filters = _repository.Create<List<IFilterMetadata>>().Object;
            var dictionaryInput = _repository.Create<IDictionary<string, object>>().Object;
            var obj = _repository.Create<object>().Object;
            var actionExecutingContext = new ActionExecutingContext(_actionContext, filters, dictionaryInput, obj);
            actionExecutingContext.HttpContext = _repository.Create<HttpContext>().Object;
            actionExecutingContext.HttpContext.RequestServices = _repository.Create<IServiceProvider>().Object;

            _simpleMeasureAttribute.OnActionExecuting(actionExecutingContext);
            Assert.True(_called_CreateRateCounter);
            Assert.AreEqual(1, _counterManager.GetRateCounter("testValueRate").Get());
        }

        [Test]
        public void OnActionExecuted_NoException_ExpectSuccessRateCounter()
        {
            _called_CreateRateCounter = false;
            var filters = _repository.Create<List<IFilterMetadata>>().Object;
            var obj = _repository.Create<object>().Object;
            var actionExecutedContext = new ActionExecutedContext(_actionContext, filters, obj);
            actionExecutedContext.HttpContext = _repository.Create<HttpContext>().Object;
            actionExecutedContext.HttpContext.RequestServices = _repository.Create<IServiceProvider>().Object;

            _simpleMeasureAttribute.OnActionExecuted(actionExecutedContext);
            Assert.True(_called_CreateRateCounter);
            Assert.AreEqual(1, _counterManager.GetRateCounter("testValueSuccessRate").Get());
        }

        [Test]
        public void OnActionExecuted_HasException_ExpectFailureRateCounter()
        {
            _called_CreateRateCounter = false;
            var filters = _repository.Create<List<IFilterMetadata>>().Object;
            var obj = _repository.Create<object>().Object;
            var actionExecutedContext = new ActionExecutedContext(_actionContext, filters, obj);
            actionExecutedContext.HttpContext = _repository.Create<HttpContext>().Object;
            actionExecutedContext.HttpContext.RequestServices = _repository.Create<IServiceProvider>().Object;
            actionExecutedContext.Exception = new BadRequestException("message");

            _simpleMeasureAttribute.OnActionExecuted(actionExecutedContext);
            Assert.True(_called_CreateRateCounter);
            Assert.AreEqual(1, _counterManager.GetRateCounter("testValueFailureRate").Get());
        }
    }
}
