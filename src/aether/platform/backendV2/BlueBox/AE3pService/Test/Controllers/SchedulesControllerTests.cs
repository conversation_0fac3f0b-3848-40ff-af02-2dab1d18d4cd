﻿// <copyright file="SchedulesControllerTests.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Microsoft.Aether.AEVA.DataContracts;
using Microsoft.Aether.BackendCommon;
using Microsoft.Aether.BlueBox.DataStoreClient;
using Microsoft.Aether.BlueBox.DataStoreClient.Contracts;
using Microsoft.Aether.BlueBox.SchedulerClient;
using Microsoft.Aether.BlueBox.WorkspaceResourcesClient;
using Microsoft.Aether.DataContracts.Entity;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using Microsoft.MachineLearning.Common.WebApi.Exceptions;
using Microsoft.MachineLearning.Common.WebApi.TeamAccounts;
using Microsoft.RelInfra.Common.Configuration;
using Moq;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Reflection;
using System.Security.Claims;
using System.Threading;
using System.Threading.Tasks;

namespace Microsoft.Aether.AE3pService.Tests.Controllers
{
    [TestFixture]
    public class SchedulesControllerTests
    {
        private readonly MockRepository _repository;
        private readonly string subscriptionId = Guid.NewGuid().ToString();
        private readonly string resourceGroupName = "rgName";
        private readonly string workspaceName = "wsName";
        private readonly string scheduleId = "scheduleId";

        private IStoreCollection _stores;
        private IContextProvider _operationContextProvider;
        private IWorkspaceResourcesCache _workspaceResourcesCache;
        private IOptionsMonitor<SchedulerClientConfig> _config;
        private IDataStoreClient _dataStoreClient;
        private IWorkspaceResourcesClient _workspaceResourcesClient;
        private SchedulesController _schedulesController;

        public SchedulesControllerTests()
        {
            _repository = new MockRepository(MockBehavior.Loose) { DefaultValue = DefaultValue.Mock, CallBase = true };
        }

        [SetUp]
        public void Init()
        {
            var httpContext = new DefaultHttpContext();
            httpContext.Request.Headers["ContinuationToken"] = "fake_token_here";
            var workspaceMetadata = new WorkspaceMetadata(Guid.Empty, null, null, Guid.Empty);
            httpContext.Items = new Dictionary<object, object>() { { typeof(WorkspaceMetadata), workspaceMetadata } };

            var controllerContext = new ControllerContext()
            {
                HttpContext = httpContext,
            };

            _stores = _repository.Create<IStoreCollection>().Object;
            _operationContextProvider = _repository.Create<IContextProvider>().Object;
            _workspaceResourcesCache = _repository.Create<IWorkspaceResourcesCache>().Object;
            _dataStoreClient = _repository.Create<IDataStoreClient>().Object;
            _workspaceResourcesCache = _repository.Create<IWorkspaceResourcesCache>().Object;
            _workspaceResourcesClient = _repository.Create<IWorkspaceResourcesClient>().Object;
            var relConfig = _repository.Create<IRelInfraConfiguration>().Object;
            _config = Mock.Of<IOptionsMonitor<SchedulerClientConfig>>(x => x.CurrentValue == new SchedulerClientConfig());

            _schedulesController = new SchedulesController(_stores, _operationContextProvider, _workspaceResourcesCache, null, _config, _dataStoreClient, _workspaceResourcesClient)
            {
                ControllerContext = controllerContext,
            };
        }

        [Test]
        public void CreateScheduleAsync_NullScheduleCreationInfo_ExpectArgumentException()
        {
            PipelineSubmissionInfo pipelineSubmissionInfo = _repository.Create<PipelineSubmissionInfo>().Object;
            ScheduleCreationInfo scheduleCreationInfo = _repository.Create<ScheduleCreationInfo>(
                "name",
                "pipelineId",
                new PipelineSubmissionInfo(),
                new Recurrence(),
                ScheduleType.Recurrence,
                new DataStoreTriggerInfo(), string.Empty).Object;

            Assert.That(() => _schedulesController.CreateScheduleAsync(subscriptionId, resourceGroupName, workspaceName, scheduleCreationInfo), Throws.TypeOf<ArgumentException>());
        }

        [Test]
        public void CreateScheduleAsync_PipepineEndpointIdNotParseable_ExpectArgumentException()
        {
            PipelineSubmissionInfo pipelineSubmissionInfo = _repository.Create<PipelineSubmissionInfo>().Object;
            ScheduleCreationInfo scheduleCreationInfo = _repository.Create<ScheduleCreationInfo>(
                "name",
                "pipelineId",
                new PipelineSubmissionInfo(),
                new Recurrence(),
                ScheduleType.Recurrence,
                new DataStoreTriggerInfo(), string.Empty).Object;

            scheduleCreationInfo.PipelineEndpointId = "fake-PipelineEndpointId";
            Assert.That(() => _schedulesController.CreateScheduleAsync(subscriptionId, resourceGroupName, workspaceName, scheduleCreationInfo), Throws.TypeOf<ArgumentException>());
        }

        [Test]
        public void CreateScheduleAsync_PipelineIdNotParseable_ExpectArgumentException()
        {
            PipelineSubmissionInfo pipelineSubmissionInfo = _repository.Create<PipelineSubmissionInfo>().Object;
            ScheduleCreationInfo scheduleCreationInfo = _repository.Create<ScheduleCreationInfo>(
                "name",
                "pipelineId",
                new PipelineSubmissionInfo(),
                new Recurrence(),
                ScheduleType.Recurrence,
                new DataStoreTriggerInfo(), string.Empty).Object;

            scheduleCreationInfo.PipelineId = "fake-PipelineId";
            Assert.That(() => _schedulesController.CreateScheduleAsync(subscriptionId, resourceGroupName, workspaceName, scheduleCreationInfo), Throws.TypeOf<ArgumentException>());
        }

        [Test]
        public void CreateScheduleAsync_ExperimentNameNotValid_ExpectArgumentException()
        {
            PipelineSubmissionInfo pipelineSubmissionInfo = _repository.Create<PipelineSubmissionInfo>().Object;
            ScheduleCreationInfo scheduleCreationInfo = _repository.Create<ScheduleCreationInfo>(
                "name",
                "pipelineId",
                new PipelineSubmissionInfo(),
                new Recurrence(),
                ScheduleType.Recurrence,
                new DataStoreTriggerInfo(), string.Empty).Object;

            scheduleCreationInfo.PipelineId = Guid.NewGuid().ToString();
            scheduleCreationInfo.PipelineSubmissionInfo = _repository.Create<PipelineSubmissionInfo>().Object;
            scheduleCreationInfo.PipelineSubmissionInfo.ExperimentName = "!hello/world)";
            Assert.That(() => _schedulesController.CreateScheduleAsync(subscriptionId, resourceGroupName, workspaceName, scheduleCreationInfo), Throws.TypeOf<ArgumentException>());
        }

        [TestCase("@utcNow(invalid)")]
        [TestCase("@startOfDay(trigger().a)")]
        public void CreateScheduleAsync_ParameterAssignments_ExpectArgumentException(string paramAssignmentValue)
        {
            PipelineSubmissionInfo pipelineSubmissionInfo = _repository.Create<PipelineSubmissionInfo>().Object;
            ScheduleCreationInfo scheduleCreationInfo = _repository.Create<ScheduleCreationInfo>(
                "name",
                "pipelineId",
                new PipelineSubmissionInfo(),
                new Recurrence(),
                ScheduleType.Recurrence,
                new DataStoreTriggerInfo(), string.Empty).Object;

            scheduleCreationInfo.PipelineId = Guid.NewGuid().ToString();
            scheduleCreationInfo.PipelineSubmissionInfo = _repository.Create<PipelineSubmissionInfo>().Object;
            Dictionary<string, string> paramAssignments = new Dictionary<string, string>();
            paramAssignments["invalidFunc"] = paramAssignmentValue;
            scheduleCreationInfo.PipelineSubmissionInfo.ParameterAssignments = paramAssignments;
            var actualExp = Assert.ThrowsAsync<ArgumentException>(async () => await _schedulesController.CreateScheduleAsync(subscriptionId, resourceGroupName, workspaceName, scheduleCreationInfo).ConfigureAwait(false));
            StringAssert.Contains("Invalid pipeline parameter expression", actualExp.Message);
        }

        [Test]
        public async Task CreateScheduleAsync_RecurrenceScheduleType_ExpectNormalReturn()
        {
            PipelineSubmissionInfo pipelineSubmissionInfo = _repository.Create<PipelineSubmissionInfo>().Object;
            ScheduleCreationInfo scheduleCreationInfo = _repository.Create<ScheduleCreationInfo>(
                "name",
                "pipelineId",
                new PipelineSubmissionInfo(),
                new Recurrence(),
                ScheduleType.Recurrence,
                new DataStoreTriggerInfo(), string.Empty).Object;

            scheduleCreationInfo.Recurrence = new Recurrence();
            scheduleCreationInfo.Recurrence.Schedule = new RecurrenceSchedule();
            scheduleCreationInfo.Recurrence.Interval = 10;
            scheduleCreationInfo.Recurrence.Frequency = Frequency.Week;
            scheduleCreationInfo.ScheduleType = ScheduleType.Recurrence;

            bool called_GetPipelineStore = false;
            Mock.Get(_stores)
                .Setup(controller => controller.GetPipelineStore(
                    It.IsAny<ClaimsPrincipal>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Callback(() => called_GetPipelineStore = true);

            bool called_SubscriptionScheduleLimit = false;
            Mock.Get(_config)
                .Setup(controller => controller.CurrentValue.SubscriptionScheduleLimit(
                    It.IsAny<string>()))
                .Returns(50000)
                .Callback(() => called_SubscriptionScheduleLimit = true);

            scheduleCreationInfo.PipelineId = Guid.NewGuid().ToString();
            scheduleCreationInfo.PipelineSubmissionInfo = _repository.Create<PipelineSubmissionInfo>().Object;
            // Run with valid pipeline parameter built-in functions.
            Dictionary<string, string> paramAssignments = new Dictionary<string, string>();
            paramAssignments.Add("triggerScheduledTime", "@formatDateTime(subtractFromTime(trigger().scheduledTime, 1, 'Day'), 'yyyy-MM-ddHH:mm:ss')");
            paramAssignments.Add("triggerStartTime", "@trigger().startTime");
            paramAssignments.Add("rawString", $"C:\\@invalid\\Test");
            paramAssignments.Add("createdBy", "@createdBy()");
            paramAssignments.Add("runId", "@runId()");
            paramAssignments.Add("yesterday", "@concat(formatDateTime(subtractFromTime(utcNow(),1,'Day'),'yyyy-MM-dd'), 'yesterday')");
            paramAssignments.Add("scopeParam", "@@velocityFeatureIds@@");
            paramAssignments.Add("invalidTopLevelExprTreatedAsRawString", "@invalid()");
            scheduleCreationInfo.PipelineSubmissionInfo.ParameterAssignments = paramAssignments;

            var output = await _schedulesController.CreateScheduleAsync(subscriptionId, resourceGroupName, workspaceName, scheduleCreationInfo).ConfigureAwait(false);
            Assert.True(called_GetPipelineStore);
            Assert.True(called_SubscriptionScheduleLimit);
        }

        [Test]
        public async Task CreateScheduleAsync_DataStoreScheduleType_ExpectNormalReturn()
        {
            PipelineSubmissionInfo pipelineSubmissionInfo = _repository.Create<PipelineSubmissionInfo>().Object;
            ScheduleCreationInfo scheduleCreationInfo = _repository.Create<ScheduleCreationInfo>(
                "name",
                "pipelineId",
                new PipelineSubmissionInfo(),
                new Recurrence(),
                ScheduleType.Recurrence,
                new DataStoreTriggerInfo(), string.Empty).Object;

            scheduleCreationInfo.DataStoreTriggerInfo = _repository.Create<DataStoreTriggerInfo>().Object;
            scheduleCreationInfo.DataStoreTriggerInfo.DataStoreName = "fakeDataStoreName";
            scheduleCreationInfo.DataStoreTriggerInfo.PollingInterval = 2;
            scheduleCreationInfo.ScheduleType = ScheduleType.DataStore;

            bool called_GetAsync = false;
            var dataStore = _repository.Create<DataStoreDto>().Object;
            dataStore.DataStoreType = DataStoreType.AzureBlob;
            dataStore.AzureStorageSection = _repository.Create<AzureStorageDto>().Object;
            dataStore.AzureStorageSection.IsSas = false;

            Mock.Get(_dataStoreClient)
                .Setup(controller => controller.GetAsync(
                    It.IsAny<string>(),
                    It.IsAny<WorkspaceIdentity>(),
                    It.IsAny<DataContracts.CreatedBy>(),
                    It.IsAny<CancellationToken>()))
                .Returns(Task.FromResult(dataStore))
                .Callback(() => called_GetAsync = true);

            bool called_GetPipelineStore = false;
            Mock.Get(_stores)
                .Setup(controller => controller.GetPipelineStore(
                    It.IsAny<ClaimsPrincipal>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Callback(() => called_GetPipelineStore = true);

            bool called_SubscriptionScheduleLimit = false;
            Mock.Get(_config)
                .Setup(controller => controller.CurrentValue.SubscriptionScheduleLimit(
                    It.IsAny<string>()))
                .Returns(50000)
                .Callback(() => called_SubscriptionScheduleLimit = true);

            scheduleCreationInfo.PipelineId = Guid.NewGuid().ToString();
            scheduleCreationInfo.PipelineSubmissionInfo = _repository.Create<PipelineSubmissionInfo>().Object;

            var output = await _schedulesController.CreateScheduleAsync(subscriptionId, resourceGroupName, workspaceName, scheduleCreationInfo).ConfigureAwait(false);
            Assert.True(called_GetPipelineStore);
            Assert.True(called_SubscriptionScheduleLimit);
            Assert.True(called_GetAsync);
        }

        [Test]
        public void ValidateDataStoreTriggerInfoAsync_DataStoreScheduleType_NotValid_ExpectArgumentException()
        {
            PipelineSubmissionInfo pipelineSubmissionInfo = _repository.Create<PipelineSubmissionInfo>().Object;
            ScheduleCreationInfo scheduleCreationInfo = _repository.Create<ScheduleCreationInfo>(
                "name",
                "pipelineId",
                new PipelineSubmissionInfo(),
                new Recurrence(),
                ScheduleType.Recurrence,
                new DataStoreTriggerInfo(), string.Empty).Object;
            scheduleCreationInfo.PipelineId = Guid.NewGuid().ToString();
            scheduleCreationInfo.PipelineSubmissionInfo = _repository.Create<PipelineSubmissionInfo>().Object;
            scheduleCreationInfo.ScheduleType = ScheduleType.DataStore;

            bool called_GetAsync = false;
            var dataStore = _repository.Create<DataStoreDto>().Object;
            Mock.Get(_dataStoreClient)
                .Setup(controller => controller.GetAsync(
                    It.IsAny<string>(),
                    It.IsAny<WorkspaceIdentity>(),
                    It.IsAny<DataContracts.CreatedBy>(),
                    It.IsAny<CancellationToken>()))
                .Returns(Task.FromResult(dataStore))
                .Callback(() => called_GetAsync = true);

            // no DataStoreTriggerInfo
            scheduleCreationInfo.DataStoreTriggerInfo = null;
            Assert.That(() => _schedulesController.CreateScheduleAsync(subscriptionId, resourceGroupName, workspaceName, scheduleCreationInfo), Throws.TypeOf<ArgumentException>());

            // no DataStoreName
            scheduleCreationInfo.DataStoreTriggerInfo = _repository.Create<DataStoreTriggerInfo>().Object;
            Assert.That(() => _schedulesController.CreateScheduleAsync(subscriptionId, resourceGroupName, workspaceName, scheduleCreationInfo), Throws.TypeOf<ArgumentException>());

            // PollingInterval < 0
            scheduleCreationInfo.DataStoreTriggerInfo.DataStoreName = "fakeDataStoreName";
            scheduleCreationInfo.DataStoreTriggerInfo.PollingInterval = -1;
            Assert.That(() => _schedulesController.CreateScheduleAsync(subscriptionId, resourceGroupName, workspaceName, scheduleCreationInfo), Throws.TypeOf<ArgumentException>());

            scheduleCreationInfo.DataStoreTriggerInfo.PollingInterval = 2;

            // DataStoreType is not AzureBlob
            dataStore.DataStoreType = DataStoreType.AzureDataLake;
            Assert.That(() => _schedulesController.CreateScheduleAsync(subscriptionId, resourceGroupName, workspaceName, scheduleCreationInfo), Throws.TypeOf<ArgumentException>());

            // IsSas = true
            dataStore.DataStoreType = DataStoreType.AzureBlob;
            dataStore.AzureStorageSection = _repository.Create<AzureStorageDto>().Object;
            dataStore.AzureStorageSection.IsSas = true;
            Assert.That(() => _schedulesController.CreateScheduleAsync(subscriptionId, resourceGroupName, workspaceName, scheduleCreationInfo), Throws.TypeOf<ArgumentException>());
            Assert.True(called_GetAsync);
        }

        [Test]
        public void ValidateDataStoreTriggerInfoAsync_DataStoreScheduleType_ThrowsException()
        {
            PipelineSubmissionInfo pipelineSubmissionInfo = _repository.Create<PipelineSubmissionInfo>().Object;
            ScheduleCreationInfo scheduleCreationInfo = _repository.Create<ScheduleCreationInfo>(
                "name",
                "pipelineId",
                new PipelineSubmissionInfo(),
                new Recurrence(),
                ScheduleType.Recurrence,
                new DataStoreTriggerInfo(), string.Empty).Object;
            scheduleCreationInfo.PipelineId = Guid.NewGuid().ToString();
            scheduleCreationInfo.PipelineSubmissionInfo = _repository.Create<PipelineSubmissionInfo>().Object;
            scheduleCreationInfo.ScheduleType = ScheduleType.DataStore;
            var dataStore = _repository.Create<DataStoreDto>().Object;

            Mock.Get(_dataStoreClient)
                .Setup(controller => controller.GetAsync(
                    It.IsAny<string>(),
                    It.IsAny<WorkspaceIdentity>(),
                    It.IsAny<DataContracts.CreatedBy>(),
                    It.IsAny<CancellationToken>()))
                .Throws(new BadRequestException("message"));

            scheduleCreationInfo.DataStoreTriggerInfo = _repository.Create<DataStoreTriggerInfo>().Object;
            scheduleCreationInfo.DataStoreTriggerInfo.DataStoreName = "fakeDataStoreName";
            scheduleCreationInfo.DataStoreTriggerInfo.PollingInterval = 2;
            dataStore.DataStoreType = DataStoreType.AzureBlob;
            dataStore.AzureStorageSection = _repository.Create<AzureStorageDto>().Object;
            dataStore.AzureStorageSection.IsSas = false;
            Assert.That(() => _schedulesController.CreateScheduleAsync(subscriptionId, resourceGroupName, workspaceName, scheduleCreationInfo), Throws.TypeOf<ArgumentException>());
        }

        [Test]
        public void ValidateDataStoreTriggerInfoAsync_ThrowsException()
        {
            PipelineSubmissionInfo pipelineSubmissionInfo = _repository.Create<PipelineSubmissionInfo>().Object;
            ScheduleCreationInfo scheduleCreationInfo = _repository.Create<ScheduleCreationInfo>(
                "name",
                "pipelineId",
                new PipelineSubmissionInfo(),
                new Recurrence(),
                ScheduleType.Recurrence,
                new DataStoreTriggerInfo(), string.Empty).Object;

            scheduleCreationInfo.Recurrence = new Recurrence();
            scheduleCreationInfo.Recurrence.Schedule = new RecurrenceSchedule();
            scheduleCreationInfo.Recurrence.Interval = 10;
            scheduleCreationInfo.Recurrence.Frequency = Frequency.Week;
            scheduleCreationInfo.ScheduleType = ScheduleType.Recurrence;

            Mock.Get(_stores)
                .Setup(controller => controller.GetPipelineStore(
                    It.IsAny<ClaimsPrincipal>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Throws(new BadRequestException("message"));

            scheduleCreationInfo.PipelineId = Guid.NewGuid().ToString();
            scheduleCreationInfo.PipelineSubmissionInfo = _repository.Create<PipelineSubmissionInfo>().Object;

            Assert.That(() => _schedulesController.CreateScheduleAsync(subscriptionId, resourceGroupName, workspaceName, scheduleCreationInfo), Throws.TypeOf<BadRequestException>());
        }

        [Test]
        public async Task GetScheduleAsync_ExpectNormalReturn()
        {
            bool called_GetPipelineScheduleStore = false;
            Mock.Get(_stores)
                .Setup(controller => controller.GetPipelineScheduleStore(
                    It.IsAny<ClaimsPrincipal>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Callback(() => called_GetPipelineScheduleStore = true);

            var output = await _schedulesController.GetScheduleAsync(subscriptionId, resourceGroupName, workspaceName, scheduleId).ConfigureAwait(false);
            Assert.True(called_GetPipelineScheduleStore);
            Assert.AreEqual(ScheduleType.Recurrence, output.ScheduleType);
        }

        [Test]
        public void GetScheduleAsync_ExpectException()
        {
            Mock.Get(_stores)
                .Setup(controller => controller.GetPipelineScheduleStore(
                    It.IsAny<ClaimsPrincipal>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Throws(new BadRequestException("test message"));

            Assert.That(() => _schedulesController.GetScheduleAsync(subscriptionId, resourceGroupName, workspaceName, scheduleId).ConfigureAwait(false), Throws.TypeOf<BadRequestException>());
        }

        [Test]
        public async Task GetSchedulesAsync__ExpectNormalOutput()
        {
            var pipelineScheduleStore = _repository.Create<DataContracts.IPipelineScheduleStore>().Object;

            bool called_GetPipelineScheduleStore = false;
            Mock.Get(_stores)
                .Setup(controller => controller.GetPipelineScheduleStore(
                    It.IsAny<ClaimsPrincipal>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Callback(() => called_GetPipelineScheduleStore = true)
                .Returns(pipelineScheduleStore);

            IEnumerable<DataContracts.PipelineScheduleEntity> list = new List<DataContracts.PipelineScheduleEntity>() { _repository.Create<DataContracts.PipelineScheduleEntity>().Object, _repository.Create<DataContracts.PipelineScheduleEntity>().Object };
            Tuple<IEnumerable<DataContracts.PipelineScheduleEntity>, string> outputTuple = new Tuple<IEnumerable<DataContracts.PipelineScheduleEntity>, string>(list, "hello");
            bool called_GetSchedulesByPipelineIdAsync = false;
            Mock.Get(pipelineScheduleStore)
                .Setup(controller => controller.ListEntityAsync(
                    It.IsAny<string>(),
                    It.IsAny<bool>(),
                    It.IsAny<string>(),
                    It.IsAny<int>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(outputTuple))
                .Callback(() => called_GetSchedulesByPipelineIdAsync = true);

            var output = await _schedulesController.GetSchedulesAsync(subscriptionId, resourceGroupName, workspaceName).ConfigureAwait(false);
            Assert.True(called_GetPipelineScheduleStore);
            Assert.True(called_GetSchedulesByPipelineIdAsync);
        }

        [Test]
        public void GetNextScheduleAsync_TryThrowsException_ExpectNotCaught()
        {
            bool called_GetPipelineScheduleStore = false;
            Mock.Get(_stores)
                .Setup(controller => controller.GetPipelineScheduleStore(
                    It.IsAny<ClaimsPrincipal>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Callback(() => called_GetPipelineScheduleStore = true)
                .Throws(new BadRequestException("message"));

            Assert.That(() => _schedulesController.GetNextScheduleAsync(subscriptionId, resourceGroupName, workspaceName), Throws.TypeOf<BadRequestException>());
            Assert.True(called_GetPipelineScheduleStore);
        }

        [Test]
        public async Task GetNextScheduleAsync__ExpectNormalOutput()
        {
            var pipelineScheduleStore = _repository.Create<DataContracts.IPipelineScheduleStore>().Object;

            bool called_GetPipelineScheduleStore = false;
            Mock.Get(_stores)
                .Setup(controller => controller.GetPipelineScheduleStore(
                    It.IsAny<ClaimsPrincipal>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Callback(() => called_GetPipelineScheduleStore = true)
                .Returns(pipelineScheduleStore);

            IEnumerable<DataContracts.PipelineScheduleEntity> list = new List<DataContracts.PipelineScheduleEntity>() { _repository.Create<DataContracts.PipelineScheduleEntity>().Object, _repository.Create<DataContracts.PipelineScheduleEntity>().Object };
            Tuple<IEnumerable<DataContracts.PipelineScheduleEntity>, string> outputTuple = new Tuple<IEnumerable<DataContracts.PipelineScheduleEntity>, string>(list, "hello");
            bool called_GetSchedulesByPipelineIdAsync = false;
            Mock.Get(pipelineScheduleStore)
                .Setup(controller => controller.ListEntityAsync(
                    It.IsAny<string>(),
                    It.IsAny<bool>(),
                    It.IsAny<string>(),
                    It.IsAny<int>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(outputTuple))
                .Callback(() => called_GetSchedulesByPipelineIdAsync = true);

            var output = await _schedulesController.GetNextScheduleAsync(subscriptionId, resourceGroupName, workspaceName).ConfigureAwait(false);
            Assert.True(called_GetPipelineScheduleStore);
            Assert.True(called_GetSchedulesByPipelineIdAsync);
        }

        [Test]
        public void UpdateScheduleAsync_ScheduleIdNotUpdated_ExpectArgumentException()
        {
            PipelineScheduleEntity updated = _repository.Create<PipelineScheduleEntity>().Object;
            updated.Id = "updatedId";

            Assert.That(() => _schedulesController.UpdateScheduleAsync(subscriptionId, resourceGroupName, workspaceName, scheduleId, updated), Throws.TypeOf<ArgumentException>());
        }

        [Test]
        public void UpdateScheduleAsync_ParameterAssignments_ExpectArgumentException()
        {
            PipelineScheduleEntity updated = _repository.Create<PipelineScheduleEntity>().Object;
            updated.PipelineSubmissionInfo = _repository.Create<PipelineSubmissionInfo>().Object;
            Dictionary<string, string> paramAssignments = new Dictionary<string, string>();
            paramAssignments["invalidFunc"] = "@startOfHour(@trigger().StartTime)";
            updated.PipelineSubmissionInfo.ParameterAssignments = paramAssignments;

            Assert.That(() => _schedulesController.UpdateScheduleAsync(subscriptionId, resourceGroupName, workspaceName, scheduleId, updated), Throws.TypeOf<ArgumentException>());
        }

        [Test]
        public void UpdateScheduleAsync_PipelineEntity_NotActive_ExpectNormalReturn()
        {
            PipelineScheduleEntity updated = _repository.Create<PipelineScheduleEntity>().Object;
            updated.Id = scheduleId;
            updated.EntityStatus = EntityStatus.Active;

            updated.PipelineSubmissionInfo = _repository.Create<PipelineSubmissionInfo>().Object;
            updated.PipelineSubmissionInfo.ExperimentName = "testExperimentName";
            updated.ScheduleType = ScheduleType.Recurrence;
            updated.Recurrence = new Recurrence();
            updated.Recurrence.Schedule = new RecurrenceSchedule();
            updated.Recurrence.Interval = 10;
            updated.Recurrence.Frequency = Frequency.Week;

            var pipelineScheduleStore = _repository.Create<DataContracts.IPipelineScheduleStore>().Object;

            bool called_GetPipelineScheduleStore = false;
            Mock.Get(_stores)
                .Setup(controller => controller.GetPipelineScheduleStore(
                    It.IsAny<ClaimsPrincipal>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Returns(pipelineScheduleStore)
                .Callback(() => called_GetPipelineScheduleStore = true);

            var pipelineScheduleEntity = _repository.Create<DataContracts.PipelineScheduleEntity>().Object;
            pipelineScheduleEntity.EntityStatus = DataContracts.EntityStatus.Disabled;
            pipelineScheduleEntity.PipelineEndpointId = "endpointId";
            pipelineScheduleEntity.PipelineId = "pipelineId";

            Mock.Get(pipelineScheduleStore)
                .Setup(controller => controller.GetEntityAsync(
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(pipelineScheduleEntity));

            var pipelineStore = _repository.Create<DataContracts.IPipelineStore>().Object;
            bool called_GetPipelineStore = false;
            Mock.Get(_stores)
                .Setup(controller => controller.GetPipelineStore(
                    It.IsAny<ClaimsPrincipal>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Returns(pipelineStore)
                .Callback(() => called_GetPipelineStore = true);

            var pipelineEntity = _repository.Create<DataContracts.PipelineEntity>().Object;
            pipelineEntity.EntityStatus = DataContracts.EntityStatus.Deprecated; // status not Active
            Mock.Get(pipelineStore)
                .Setup(controller => controller.GetEntityAsync(
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(pipelineEntity));

            Assert.That(() => _schedulesController.UpdateScheduleAsync(subscriptionId, resourceGroupName, workspaceName, scheduleId, updated), Throws.TypeOf<ArgumentException>());
            Assert.True(called_GetPipelineScheduleStore);
            Assert.True(called_GetPipelineStore);
        }

        [Test]
        public void UpdateScheduleAsync_OuterTryBlock_ThrowsException_ExpectException()
        {
            PipelineScheduleEntity updated = _repository.Create<PipelineScheduleEntity>().Object;
            updated.Id = scheduleId;
            updated.EntityStatus = EntityStatus.Active;

            Mock.Get(_operationContextProvider)
                .Setup(controller => controller.GetCreatedByObject())
                .Throws(new BadRequestException("message"));

            Assert.That(() => _schedulesController.UpdateScheduleAsync(subscriptionId, resourceGroupName, workspaceName, scheduleId, updated), Throws.TypeOf<BadRequestException>());
        }

        [Test]
        public void UpdateScheduleAsync_DatastoreScheduleType_ThrowsException_ExpectException()
        {
            PipelineScheduleEntity updated = _repository.Create<PipelineScheduleEntity>().Object;
            updated.Id = scheduleId;
            updated.EntityStatus = EntityStatus.Active;

            updated.PipelineSubmissionInfo = _repository.Create<PipelineSubmissionInfo>().Object;
            updated.PipelineSubmissionInfo.ExperimentName = "testExperimentName";
            updated.ScheduleType = ScheduleType.DataStore;
            updated.DataStoreTriggerInfo = _repository.Create<DataStoreTriggerInfo>().Object;
            updated.DataStoreTriggerInfo.DataStoreName = "fakeDataStoreName";
            updated.DataStoreTriggerInfo.PollingInterval = 2;
            updated.ScheduleType = ScheduleType.DataStore;

            var pipelineScheduleStore = _repository.Create<DataContracts.IPipelineScheduleStore>().Object;
            bool called_GetPipelineScheduleStore = false;
            Mock.Get(_stores)
                .Setup(controller => controller.GetPipelineScheduleStore(
                    It.IsAny<ClaimsPrincipal>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Returns(pipelineScheduleStore)
                .Callback(() => called_GetPipelineScheduleStore = true);

            bool called_GetAsync = false;
            var dataStore = _repository.Create<DataStoreDto>().Object;
            dataStore.DataStoreType = DataStoreType.AzureBlob;
            dataStore.AzureStorageSection = _repository.Create<AzureStorageDto>().Object;
            dataStore.AzureStorageSection.IsSas = false;

            Mock.Get(_dataStoreClient)
                .Setup(controller => controller.GetAsync(
                    It.IsAny<string>(),
                    It.IsAny<WorkspaceIdentity>(),
                    It.IsAny<DataContracts.CreatedBy>(),
                    It.IsAny<CancellationToken>()))
                .Returns(Task.FromResult(dataStore))
                .Callback(() => called_GetAsync = true);

            var pipelineScheduleEntity = _repository.Create<DataContracts.PipelineScheduleEntity>().Object;
            pipelineScheduleEntity.EntityStatus = DataContracts.EntityStatus.Active;
            pipelineScheduleEntity.PipelineEndpointId = "endpointId";

            Mock.Get(pipelineScheduleStore)
                .Setup(controller => controller.GetEntityAsync(
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(pipelineScheduleEntity));

            bool called_SubscriptionScheduleLimit = false;
            Mock.Get(_config)
                .Setup(controller => controller.CurrentValue.SubscriptionScheduleLimit(
                    It.IsAny<string>()))
                .Callback(() => called_SubscriptionScheduleLimit = true)
                .Throws(new ArgumentException("message"));

            Assert.That(() => _schedulesController.UpdateScheduleAsync(subscriptionId, resourceGroupName, workspaceName, scheduleId, updated), Throws.TypeOf<ArgumentException>());
            Assert.True(called_GetPipelineScheduleStore);
            Assert.True(called_GetAsync);
            Assert.True(called_SubscriptionScheduleLimit);
        }

        [Test]
        public async Task UpdateScheduleAsync_ScheduleProvisioningStateTimeout_ExpectNormalReturn()
        {
            FieldInfo fieldInfo = _schedulesController.GetType().GetField("ScheduleProvisioningTimeoutSeconds", BindingFlags.Static | BindingFlags.NonPublic);
            int scheduleProvisioningTimeoutSeconds = (int)fieldInfo.GetValue(_schedulesController);

            DateTime lastModifiedDate = DateTime.UtcNow.AddSeconds(-scheduleProvisioningTimeoutSeconds - 10);
            PipelineScheduleEntity updated = _repository.Create<PipelineScheduleEntity>().Object;
            updated.Id = scheduleId;
            updated.EntityStatus = EntityStatus.Disabled;
            updated.ScheduleType = ScheduleType.Recurrence;
            updated.Recurrence = new Recurrence
            {
                Interval = 10
            };
            updated.ProvisioningStatus = ProvisioningStatus.Provisioning;
            updated.LastModifiedDate = lastModifiedDate;

            var pipelineScheduleStore = _repository.Create<DataContracts.IPipelineScheduleStore>().Object;

            Mock.Get(_stores)
                .Setup(controller => controller.GetPipelineScheduleStore(
                    It.IsAny<ClaimsPrincipal>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Returns(pipelineScheduleStore);

            var pipelineScheduleEntity = _repository.Create<DataContracts.PipelineScheduleEntity>().Object;
            pipelineScheduleEntity.Id = scheduleId;
            pipelineScheduleEntity.EntityStatus = DataContracts.EntityStatus.Active;
            pipelineScheduleEntity.ProvisioningStatus = DataContracts.ProvisioningStatus.Provisioning;
            pipelineScheduleEntity.LastModifiedDate = lastModifiedDate;

            Mock.Get(pipelineScheduleStore)
                .Setup(controller => controller.GetEntityAsync(
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(pipelineScheduleEntity));

            Mock.Get(pipelineScheduleStore)
                .Setup(controller => controller.UpdateAsync(
                    It.IsAny<string>(),
                    It.IsAny<DataContracts.PipelineScheduleEntity>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(pipelineScheduleEntity));

            var output = await _schedulesController.UpdateScheduleAsync(subscriptionId, resourceGroupName, workspaceName, scheduleId, updated).ConfigureAwait(false);

            Mock.Get(_stores)
                .Verify(
                    controller => controller.GetPipelineScheduleStore(
                    It.IsAny<ClaimsPrincipal>(),
                    It.IsAny<WorkspaceIdentity>()),
                Times.Once);

            // There are twice UpdateAsync in normal UT for UpdateScheduleAsync, in this case it happens once more because the provisioning state timeout and the schedule needs to update the state.
            Mock.Get(pipelineScheduleStore)
                .Verify(
                    controller => controller.UpdateAsync(
                        It.IsAny<string>(),
                        It.IsAny<DataContracts.PipelineScheduleEntity>(),
                        It.IsAny<WorkspaceIdentity>()),
                Times.Exactly(3));
        }

        [Test]
        public void UpdateScheduleAsync_CatchEtagMismatchException_ExpectArgumentException()
        {
            PipelineScheduleEntity updated = _repository.Create<PipelineScheduleEntity>().Object;
            updated.Id = scheduleId;
            updated.EntityStatus = EntityStatus.Disabled;
            updated.ScheduleType = ScheduleType.Recurrence;
            updated.Recurrence = new Recurrence
            {
                Interval = 10
            };
            updated.Etag = "Etag1";
            var pipelineScheduleStore = _repository.Create<DataContracts.IPipelineScheduleStore>().Object;
            Mock.Get(_stores)
                .Setup(controller => controller.GetPipelineScheduleStore(
                    It.IsAny<ClaimsPrincipal>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Returns(pipelineScheduleStore);
            var pipelineScheduleEntity = _repository.Create<DataContracts.PipelineScheduleEntity>().Object;
            pipelineScheduleEntity.Id = scheduleId;
            pipelineScheduleEntity.EntityStatus = DataContracts.EntityStatus.Active;
            pipelineScheduleEntity.Etag = "Etag2";
            Mock.Get(pipelineScheduleStore)
                .Setup(controller => controller.GetEntityAsync(
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(pipelineScheduleEntity));
            Assert.That(() => _schedulesController.UpdateScheduleAsync(subscriptionId, resourceGroupName, workspaceName, scheduleId, updated), Throws.TypeOf<ArgumentException>());
            Mock.Get(_stores)
                .Verify(
                    controller => controller.GetPipelineScheduleStore(
                    It.IsAny<ClaimsPrincipal>(),
                    It.IsAny<WorkspaceIdentity>()),
                Times.Once);
        }

        [Test]
        public async Task GetSchedulesByPipelineIdAsync_ExpectNormalOutput()
        {
            var pipelineId = Guid.NewGuid().ToString();
            var pipelineScheduleStore = _repository.Create<DataContracts.IPipelineScheduleStore>().Object;

            bool called_GetPipelineScheduleStore = false;
            Mock.Get(_stores)
                .Setup(controller => controller.GetPipelineScheduleStore(
                    It.IsAny<ClaimsPrincipal>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Callback(() => called_GetPipelineScheduleStore = true)
                .Returns(pipelineScheduleStore);

            IEnumerable<DataContracts.PipelineScheduleEntity> list = new List<DataContracts.PipelineScheduleEntity>() { _repository.Create<DataContracts.PipelineScheduleEntity>().Object, _repository.Create<DataContracts.PipelineScheduleEntity>().Object };
            Tuple<IEnumerable<DataContracts.PipelineScheduleEntity>, string> outputTuple = new Tuple<IEnumerable<DataContracts.PipelineScheduleEntity>, string>(list, "hello");
            bool called_GetSchedulesByPipelineIdAsync = false;
            Mock.Get(pipelineScheduleStore)
                .Setup(controller => controller.GetSchedulesByPipelineIdAsync(
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<int>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(outputTuple))
                .Callback(() => called_GetSchedulesByPipelineIdAsync = true);

            var output = await _schedulesController.GetSchedulesByPipelineIdAsync(subscriptionId, resourceGroupName, workspaceName, pipelineId).ConfigureAwait(false);
            Assert.True(called_GetPipelineScheduleStore);
            Assert.True(called_GetSchedulesByPipelineIdAsync);
        }

        [Test]
        public void LoadSchedulesByPipelineIdAsync_NullPipelineId_ExpectArgumentException()
        {
            Assert.That(() => _schedulesController.LoadSchedulesByPipelineIdAsync(subscriptionId, resourceGroupName, workspaceName, null), Throws.TypeOf<ArgumentException>());
        }

        [Test]
        public void LoadSchedulesByPipelineIdAsync_MisformattedPipelineId_ExpectArgumentException()
        {
            var pipelineId = "notAGuid";
            Assert.That(() => _schedulesController.LoadSchedulesByPipelineIdAsync(subscriptionId, resourceGroupName, workspaceName, pipelineId), Throws.TypeOf<ArgumentException>());
        }

        [Test]
        public void LoadSchedulesByPipelineIdAsync_TryBlockThrowsException_ExpectNotCaught()
        {
            var pipelineId = Guid.NewGuid().ToString();

            bool called_GetPipelineScheduleStore = false;
            Mock.Get(_stores)
                .Setup(controller => controller.GetPipelineScheduleStore(
                    It.IsAny<ClaimsPrincipal>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Callback(() => called_GetPipelineScheduleStore = true)
                .Throws(new BadRequestException("message"));

            Assert.That(() => _schedulesController.LoadSchedulesByPipelineIdAsync(subscriptionId, resourceGroupName, workspaceName, pipelineId), Throws.TypeOf<BadRequestException>());
            Assert.True(called_GetPipelineScheduleStore);
        }

        [Test]
        public async Task LoadSchedulesByPipelineIdAsync_ExpectNormalOutput()
        {
            var pipelineId = Guid.NewGuid().ToString();
            var pipelineScheduleStore = _repository.Create<DataContracts.IPipelineScheduleStore>().Object;

            bool called_GetPipelineScheduleStore = false;
            Mock.Get(_stores)
                .Setup(controller => controller.GetPipelineScheduleStore(
                    It.IsAny<ClaimsPrincipal>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Callback(() => called_GetPipelineScheduleStore = true)
                .Returns(pipelineScheduleStore);

            IEnumerable<DataContracts.PipelineScheduleEntity> list = new List<DataContracts.PipelineScheduleEntity>() { _repository.Create<DataContracts.PipelineScheduleEntity>().Object, _repository.Create<DataContracts.PipelineScheduleEntity>().Object };
            Tuple<IEnumerable<DataContracts.PipelineScheduleEntity>, string> outputTuple = new Tuple<IEnumerable<DataContracts.PipelineScheduleEntity>, string>(list, "hello");
            bool called_GetSchedulesByPipelineIdAsync = false;
            Mock.Get(pipelineScheduleStore)
                .Setup(controller => controller.GetSchedulesByPipelineIdAsync(
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<int>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(outputTuple))
                .Callback(() => called_GetSchedulesByPipelineIdAsync = true);

            var output = await _schedulesController.LoadSchedulesByPipelineIdAsync(subscriptionId, resourceGroupName, workspaceName, pipelineId).ConfigureAwait(false);
            Assert.True(called_GetPipelineScheduleStore);
            Assert.True(called_GetSchedulesByPipelineIdAsync);
        }

        [Test]
        public async Task GetSchedulesByPipelineEndpointIdAsync__ExpectNormalOutput()
        {
            var pipelinEndpointId = Guid.NewGuid().ToString();
            var pipelineScheduleStore = _repository.Create<DataContracts.IPipelineScheduleStore>().Object;

            bool called_GetPipelineScheduleStore = false;
            Mock.Get(_stores)
                .Setup(controller => controller.GetPipelineScheduleStore(
                    It.IsAny<ClaimsPrincipal>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Callback(() => called_GetPipelineScheduleStore = true)
                .Returns(pipelineScheduleStore);

            IEnumerable<DataContracts.PipelineScheduleEntity> list = new List<DataContracts.PipelineScheduleEntity>() { _repository.Create<DataContracts.PipelineScheduleEntity>().Object, _repository.Create<DataContracts.PipelineScheduleEntity>().Object };
            Tuple<IEnumerable<DataContracts.PipelineScheduleEntity>, string> outputTuple = new Tuple<IEnumerable<DataContracts.PipelineScheduleEntity>, string>(list, "hello");
            bool called_GetSchedulesByPipelineEndpointIdAsync = false;
            Mock.Get(pipelineScheduleStore)
                .Setup(controller => controller.GetSchedulesByPipelineEndpointIdAsync(
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<int>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(outputTuple))
                .Callback(() => called_GetSchedulesByPipelineEndpointIdAsync = true);

            var output = await _schedulesController.GetSchedulesByPipelineEndpointIdAsync(subscriptionId, resourceGroupName, workspaceName, pipelinEndpointId).ConfigureAwait(false);
            Assert.True(called_GetPipelineScheduleStore);
            Assert.True(called_GetSchedulesByPipelineEndpointIdAsync);
        }

        [Test]
        public void LoadSchedulesByPipelineEndpointIdAsync_NullPipelineEndpointId_ExpectArgumentException()
        {
            Assert.That(() => _schedulesController.GetSchedulesByPipelineEndpointIdAsync(subscriptionId, resourceGroupName, workspaceName, null), Throws.TypeOf<ArgumentException>());
        }

        [Test]
        public void LoadSchedulesByPipelineEndpointIdAsync_MisformattedPipelineEndpointId_ExpectArgumentException()
        {
            var pipelinEndpointId = "notAGuid";
            Assert.That(() => _schedulesController.LoadSchedulesByPipelineEndpointIdAsync(subscriptionId, resourceGroupName, workspaceName, pipelinEndpointId), Throws.TypeOf<ArgumentException>());
        }

        [Test]
        public void LoadSchedulesByPipelineEndpointIdAsync_TryBlockThrowsException_ExpectNotCaught()
        {
            var pipelinEndpointId = Guid.NewGuid().ToString();

            bool called_GetPipelineScheduleStore = false;
            Mock.Get(_stores)
                .Setup(controller => controller.GetPipelineScheduleStore(
                    It.IsAny<ClaimsPrincipal>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Callback(() => called_GetPipelineScheduleStore = true)
                .Throws(new BadRequestException("message"));

            Assert.That(() => _schedulesController.LoadSchedulesByPipelineEndpointIdAsync(subscriptionId, resourceGroupName, workspaceName, pipelinEndpointId), Throws.TypeOf<BadRequestException>());
            Assert.True(called_GetPipelineScheduleStore);
        }

        [Test]
        public async Task LoadSchedulesByPipelineEndpointIdAsync_ExpectNormalOutput()
        {
            var pipelinEndpointId = Guid.NewGuid().ToString();
            var pipelineScheduleStore = _repository.Create<DataContracts.IPipelineScheduleStore>().Object;

            bool called_GetPipelineScheduleStore = false;
            Mock.Get(_stores)
                .Setup(controller => controller.GetPipelineScheduleStore(
                    It.IsAny<ClaimsPrincipal>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Callback(() => called_GetPipelineScheduleStore = true)
                .Returns(pipelineScheduleStore);

            IEnumerable<DataContracts.PipelineScheduleEntity> list = new List<DataContracts.PipelineScheduleEntity>() { _repository.Create<DataContracts.PipelineScheduleEntity>().Object, _repository.Create<DataContracts.PipelineScheduleEntity>().Object };
            Tuple<IEnumerable<DataContracts.PipelineScheduleEntity>, string> outputTuple = new Tuple<IEnumerable<DataContracts.PipelineScheduleEntity>, string>(list, "hello");
            bool called_GetSchedulesByPipelineEndpointIdAsync = false;
            Mock.Get(pipelineScheduleStore)
                .Setup(controller => controller.GetSchedulesByPipelineEndpointIdAsync(
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<int>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(outputTuple))
                .Callback(() => called_GetSchedulesByPipelineEndpointIdAsync = true);

            var output = await _schedulesController.LoadSchedulesByPipelineEndpointIdAsync(subscriptionId, resourceGroupName, workspaceName, pipelinEndpointId).ConfigureAwait(false);
            Assert.True(called_GetPipelineScheduleStore);
            Assert.True(called_GetSchedulesByPipelineEndpointIdAsync);
        }
    }
}
