﻿// <copyright file="DataTypesRepositoryControllerTest.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Common.WebApi.Exceptions;
using Microsoft.Aether.BackendCommon;
using Microsoft.Aether.BlueBox.RepositoryResourcesClient;
using Microsoft.Aether.BlueBox.RepositoryResourcesContracts;
using Microsoft.Aether.DataContracts;
using Microsoft.Aether.DataContracts.Entity;
using Microsoft.MachineLearning.Common.Core.Exceptions;
using Moq;
using NUnit.Framework;
using System.Security.Claims;
using System.Threading;
using System.Threading.Tasks;

namespace Microsoft.Aether.AE3pService.Tests.Controllers
{
    [TestFixture]
    public class DataTypesRepositoryControllerTest
    {
        private readonly MockRepository _repository;
        private IStoreCollection _stores;
        private IContextProvider _operationContextProvider;
        private IWorkspaceResourcesCache _workspaceResourcesCache;
        private IRepositoryResourcesClient _repositoryClient;
        private DataTypesRepositoryController _dataTypesRepositoryController;
        private bool called_GetDataTypeStore = false;
        private bool called_GetRepositoryById = false;

        public DataTypesRepositoryControllerTest()
        {
            _repository = new MockRepository(MockBehavior.Loose) { DefaultValue = DefaultValue.Mock, CallBase = true };
            _stores = _repository.Create<IStoreCollection>().Object;
            _operationContextProvider = _repository.Create<IContextProvider>().Object;
            _workspaceResourcesCache = _repository.Create<IWorkspaceResourcesCache>().Object;
            _repositoryClient = _repository.Create<IRepositoryResourcesClient>().Object;
            _dataTypesRepositoryController = new DataTypesRepositoryController(
                _stores,
                _operationContextProvider,
                _workspaceResourcesCache,
                _repositoryClient);
        }

        [SetUp]
        public void Init()
        {
            Mock.Get(_repositoryClient)
                .Setup(controller => controller.GetRepositoryByIdAsync(It.IsAny<string>(), It.IsAny<CancellationToken?>()))
                .Returns(Task.FromResult(_repository.Create<RepositoryMetadata>().Object))
                .Callback(() => called_GetRepositoryById = true);

            var dataStore = _repository.Create<IDataTypeStore>().Object;
            Mock.Get(_stores)
                .Setup(controller => controller.GetDataTypeStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(dataStore)
                .Callback(() => called_GetDataTypeStore = true);
        }

        [Test]
        public async Task GetAllDataTypesAsync_ExpectNormalReturn()
        {
            called_GetRepositoryById = false;
            called_GetDataTypeStore = false;
            await _dataTypesRepositoryController.GetAllDataTypesAsync("repositoryId").ConfigureAwait(false);
            Assert.True(called_GetRepositoryById);
            Assert.True(called_GetDataTypeStore);
        }

        [Test]
        public async Task CreateNewDataTypeAsync_ExpectNormalReturn()
        {
            called_GetRepositoryById = false;
            called_GetDataTypeStore = false;
            AEVA.DataContracts.DataTypeCreationInfo creationInfo = _repository.Create<AEVA.DataContracts.DataTypeCreationInfo>("id", "name", "description", false).Object;
            await _dataTypesRepositoryController.CreateNewDataTypeAsync("repositoryId", creationInfo).ConfigureAwait(false);
            Assert.True(called_GetRepositoryById);
            Assert.True(called_GetDataTypeStore);
        }

        [Test]
        public async Task UpdateDataTypeAsync_ExpectNormalReturn()
        {
            called_GetRepositoryById = false;
            called_GetDataTypeStore = false;
            AEVA.DataContracts.DataTypeEntity dataEntity = _repository.Create<AEVA.DataContracts.DataTypeEntity>().Object;
            dataEntity.Id = "id";
            await _dataTypesRepositoryController.UpdateDataTypeAsync("repositoryId", "id", dataEntity).ConfigureAwait(false);
            Assert.True(called_GetRepositoryById);
            Assert.True(called_GetDataTypeStore);
        }

        [Test]
        public void UpdateDataTypeAsync_ResourceNotFound_ThrowsResourceNotFoundException()
        {
            called_GetRepositoryById = false;
            called_GetDataTypeStore = false;
            AEVA.DataContracts.DataTypeEntity dataEntity = _repository.Create<AEVA.DataContracts.DataTypeEntity>().Object;
            dataEntity.Id = "id";
            Mock.Get(_repositoryClient)
                .Setup(controller => controller.GetRepositoryByIdAsync(It.IsAny<string>(), It.IsAny<CancellationToken?>()))
                .Throws(new ResourceNotFoundException());

            Assert.That(() => _dataTypesRepositoryController.UpdateDataTypeAsync("repositoryId", "id", dataEntity),
                Throws.TypeOf<BaseException>());
            Assert.That(() => _dataTypesRepositoryController.UpdateDataTypeAsync("repositoryId", "id", dataEntity),
                Throws.InnerException.TypeOf<ResourceNotFoundException>());
            Assert.False(called_GetRepositoryById);
            Assert.False(called_GetDataTypeStore);
        }
    }
}
