﻿// <copyright file="GraphsControllerTests.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Microsoft.Aether.BackendCommon;
using Microsoft.Aether.DataContracts;
using Microsoft.Aether.DataContracts.Entity;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Routing;
using Microsoft.MachineLearning.Common.Core.Exceptions;
using Microsoft.MachineLearning.Common.WebApi.Exceptions;
using Microsoft.MachineLearning.Common.WebApi.TeamAccounts;
using Moq;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;

namespace Microsoft.Aether.AE3pService.Tests.Controllers
{
    [TestFixture]
    public class GraphsControllerTests
    {
        private readonly MockRepository _repository;
        private GraphsController _graphController;
        private IStoreCollection _fakeStores;
        private IContextProvider _fakeOperationContextProvider;
        private IWorkspaceResourcesCache _fakeWorkspaceResourcesCache;
        private bool _called_GetGraphStore = false;
        private bool _called_CreateGraph = false;
        private IGraphStore _fakeGraphStore;
        private GraphEntity _fakeGraphEntity;
        private bool _called_GetWorkspaceIdentityIdAsync = false;

        public GraphsControllerTests()
        {
            _repository = new MockRepository(MockBehavior.Loose) { DefaultValue = DefaultValue.Mock, CallBase = true };
        }

        [SetUp]
        public void Init()
        {
            _fakeStores = _repository.Create<IStoreCollection>().Object;
            _fakeOperationContextProvider = _repository.Create<IContextProvider>().Object;
            _fakeWorkspaceResourcesCache = _repository.Create<IWorkspaceResourcesCache>().Object;
            _graphController = new GraphsController(_fakeStores, _fakeOperationContextProvider, _fakeWorkspaceResourcesCache);
            _fakeGraphStore = _repository.Create<IGraphStore>().Object;
            _fakeGraphEntity = _repository.Create<GraphEntity>().Object;
            _fakeGraphEntity.Id = "graphId";

            Mock.Get(_fakeStores)
                .Setup(controller => controller.GetGraphStore(
                              It.IsAny<ClaimsPrincipal>(),
                              It.IsAny<WorkspaceIdentity>()))
                .Returns(_fakeGraphStore)
                .Callback(() => _called_GetGraphStore = true);

            Mock.Get(_fakeOperationContextProvider)
                .Setup(controller => controller.GetCreatedByObject())
                .Returns(new CreatedBy())
                .Callback(() => _called_GetWorkspaceIdentityIdAsync = true);

            Mock.Get(_fakeGraphStore)
               .Setup(controller => controller.GetEntityAsync(
                             It.IsAny<string>(),
                             It.IsAny<string>(),
                             It.IsAny<WorkspaceIdentity>()))
               .Returns(Task.FromResult(_fakeGraphEntity));

            Mock.Get(_fakeGraphStore).Setup(g => g.CreateAsync(It.IsAny<string>(), It.IsAny<GraphEntity>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(
                    (string workspaceId, GraphEntity graphEntity, WorkspaceIdentity identity) =>
                    {
                        _called_CreateGraph = true;
                        graphEntity.Id = Guid.NewGuid().ToString();
                        return Task.FromResult(graphEntity);
                    });

            var httpContext = new Mock<HttpContext>();
            var workspaceMetadata = new WorkspaceMetadata(Guid.Empty, null, null, Guid.Empty);
            httpContext.SetupGet(a => a.Items).Returns(new Dictionary<object, object>() { { typeof(WorkspaceMetadata), workspaceMetadata } });
            _graphController.ControllerContext = new ControllerContext(new ActionContext(httpContext.Object, new RouteData(), new Microsoft.AspNetCore.Mvc.Controllers.ControllerActionDescriptor()));
        }

        [Test]
        public void GetGraphAsync_TryBlock_ExpectExceptionThrown()
        {
            Mock.Get(_fakeStores)
                .Setup(controller => controller.GetGraphStore(
                              It.IsAny<ClaimsPrincipal>(),
                              It.IsAny<WorkspaceIdentity>()))
                .Throws(new BadRequestException("message"));

            Assert.That(() => _graphController.GetGraphAsync("subId", "rgName", "wsName", "graphId"), Throws.TypeOf<BaseException>());
        }

        [Test]
        public async Task GetGraphAsync_TryBlock_ExpectNormalReturn()
        {
            _called_GetGraphStore = false;
            var output = await _graphController.GetGraphAsync("subId", "rgName", "wsName", "graphId").ConfigureAwait(false);
            Assert.True(_called_GetGraphStore);
            Assert.AreEqual(typeof(AEVA.DataContracts.GraphEntity), output.GetType());
        }

        [Test]
        public void GetGraphV2Async_TryBlock_ExpectExceptionThrown()
        {
            Mock.Get(_fakeStores)
                .Setup(controller => controller.GetGraphStore(
                              It.IsAny<ClaimsPrincipal>(),
                              It.IsAny<WorkspaceIdentity>()))
                .Throws(new ArgumentException("message"));

            Assert.That(() => _graphController.GetGraphV2Async("subId", "rgName", "wsName", "graphId"), Throws.TypeOf<BaseException>());
        }

        [Test]
        public async Task GetGraphV2Async_TryBlock_ExpectNormalReturn()
        {
            _called_GetGraphStore = false;
            var output = await _graphController.GetGraphV2Async("subId", "rgName", "wsName", "graphId").ConfigureAwait(false);
            Assert.True(_called_GetGraphStore);
            Assert.AreEqual(typeof(AEVA.DataContracts.GraphEntity), output.GetType());
        }

        [Test]
        public async Task GetGraphInterfaceAsync_ExpectNormalReturn()
        {
            _called_GetWorkspaceIdentityIdAsync = false;
            _called_GetGraphStore = false;

            var output = await _graphController.GetGraphInterfaceAsync("subId", "rgName", "wsName", "graphId").ConfigureAwait(false);
            Assert.True(_called_GetWorkspaceIdentityIdAsync);
            Assert.True(_called_GetGraphStore);
            Assert.AreEqual(typeof(AEVA.DataContracts.EntityInterface), output.GetType());
        }

        [Test]
        public void GetVisualGraphAndInterfaceAsync_TryBlock_ExpectExceptionThrown()
        {
            Mock.Get(_fakeOperationContextProvider)
                .Setup(controller => controller.GetCreatedByObject())
                .Throws(new BadRequestException("message"));

            Assert.That(() => _graphController.GetVisualGraphAndInterfaceAsync("subId", "rgName", "wsName", "graphId"), Throws.TypeOf<BaseException>());
        }

        [Test]
        public async Task GetVisualGraphAndInterfaceAsync_TryBlock_ExpectNormalReturn()
        {
            _called_GetGraphStore = false;
            _called_GetWorkspaceIdentityIdAsync = false;
            var output = await _graphController.GetVisualGraphAndInterfaceAsync("subId", "rgName", "wsName", "graphId").ConfigureAwait(false);
            Assert.True(_called_GetGraphStore);
            Assert.True(_called_GetWorkspaceIdentityIdAsync);
            Assert.AreEqual(typeof(AEVA.DataContracts.VisualGraphWithEntityInterface), output.GetType());
            Assert.AreEqual("graphId", output.VisualGraph.Graph.Id);
        }

        [Test]
        public void GetVisualGraphAndInterfaceV2Async_TryBlock_ExpectExceptionThrown()
        {
            Mock.Get(_fakeOperationContextProvider)
                .Setup(controller => controller.GetCreatedByObject())
                .Throws(new BadRequestException("message"));

            Assert.That(() => _graphController.GetVisualGraphAndInterfaceV2Async("subId", "rgName", "wsName", "graphId"), Throws.TypeOf<BaseException>());
        }

        [Test]
        public async Task GetVisualGraphAndInterfaceV2Async_TryBlock_ExpectNormalReturn()
        {
            _called_GetGraphStore = false;
            _called_GetWorkspaceIdentityIdAsync = false;
            var output = await _graphController.GetVisualGraphAndInterfaceV2Async("subId", "rgName", "wsName", "graphId").ConfigureAwait(false);
            Assert.True(_called_GetGraphStore);
            Assert.True(_called_GetWorkspaceIdentityIdAsync);
            Assert.AreEqual(typeof(AEVA.DataContracts.VisualGraphWithEntityInterface), output.GetType());
            Assert.AreEqual("graphId", output.VisualGraph.Graph.Id);
        }

        [Test]
        public void CreateGraphLayoutAsync_TryBlock_ExpectExceptionThrown()
        {
            var graphLayoutInfo = _repository.Create<AEVA.DataContracts.GraphLayoutCreationInfo>().Object;
            Mock.Get(_fakeStores)
                .Setup(controller => controller.GetGraphStore(
                              It.IsAny<ClaimsPrincipal>(),
                              It.IsAny<WorkspaceIdentity>()))
                .Throws(new BadRequestException("message"));

            Assert.That(() => _graphController.CreateGraphLayoutAsync("subId", "rgName", "wsName", "graphId", graphLayoutInfo),
                Throws.TypeOf<BaseException>());
        }

        [Test]
        public void CreateGraphLayoutAsync_GraphNull_ExpectArgumentException()
        {
            var graphLayoutInfo = _repository.Create<AEVA.DataContracts.GraphLayoutCreationInfo>().Object;
            GraphEntity nullGraph = null;

            Mock.Get(_fakeGraphStore)
               .Setup(controller => controller.TryGetEntityAsync(
                             It.IsAny<string>(),
                             It.IsAny<string>(),
                             It.IsAny<WorkspaceIdentity>()))
               .Returns(Task.FromResult(nullGraph));

            Assert.That(() => _graphController.CreateGraphLayoutAsync("subId", "rgName", "wsName", "graphId", graphLayoutInfo),
                Throws.TypeOf<BaseException>());
        }

        [Test]
        public async Task CreateGraphLayoutAsync_TryBlock_ExpectNormalReturn()
        {
            var graphLayoutInfo = _repository.Create<AEVA.DataContracts.GraphLayoutCreationInfo>().Object;
            _called_GetGraphStore = false;
            var output = await _graphController.CreateGraphLayoutAsync("subId", "rgName", "wsName", "graphId", graphLayoutInfo).ConfigureAwait(false);
            Assert.True(_called_GetGraphStore);
            Assert.AreEqual(typeof(AEVA.DataContracts.GraphLayout), output.GetType());
        }

        [Test]
        public async Task CreateVisualGraphAndInterfaceAsync_ExpectNormalReturn()
        {
            AEVA.DataContracts.VisualGraphWithEntityInterface graphToCreate = new AEVA.DataContracts.VisualGraphWithEntityInterface()
            {
                VisualGraph = new AEVA.DataContracts.VisualGraph()
                {
                    Graph = new AEVA.DataContracts.GraphEntity(),
                    Layout = new AEVA.DataContracts.GraphLayout()
                },
                Interface = new AEVA.DataContracts.EntityInterface()
            };

            _called_CreateGraph = false;
            AEVA.DataContracts.GraphEntity outputGraph = await _graphController.CreateVisualGraphAndInterfaceAsync("subId", "rgName", "wsName", graphToCreate).ConfigureAwait(false);
            Assert.True(_called_CreateGraph);
            Assert.IsNotNull(outputGraph);
            Assert.IsNotNull(outputGraph.Id);
        }

        [Test]
        public void UpdateGraphLayoutAsync_OriginalNull_ExpectArgumentException()
        {
            var graphLayout = _repository.Create<AEVA.DataContracts.GraphLayout>().Object;
            GraphLayout nullGraphLayout = null;

            Mock.Get(_fakeGraphStore)
               .Setup(controller => controller.TryGetLayoutAsync(
                             It.IsAny<string>(),
                             It.IsAny<string>(),
                             It.IsAny<WorkspaceIdentity>()))
               .Returns(Task.FromResult(nullGraphLayout));

            Assert.That(() => _graphController.UpdateGraphLayoutAsync("subId", "rgName", "wsName", "graphId", graphLayout),
                Throws.TypeOf<BaseException>());
        }

        [Test]
        public void UpdateGraphLayoutAsync_GraphLayoutIdsNotEqual_ExpectArgumentException()
        {
            var graphLayout = _repository.Create<AEVA.DataContracts.GraphLayout>().Object;
            graphLayout.Id = "layoutId";
            GraphLayout originalGraphLayout = _repository.Create<GraphLayout>().Object;
            originalGraphLayout.Id = "mismatchId";

            Mock.Get(_fakeGraphStore)
               .Setup(controller => controller.TryGetLayoutAsync(
                             It.IsAny<string>(),
                             It.IsAny<string>(),
                             It.IsAny<WorkspaceIdentity>()))
               .Returns(Task.FromResult(originalGraphLayout));

            Assert.That(() => _graphController.UpdateGraphLayoutAsync("subId", "rgName", "wsName", "graphId", graphLayout),
                Throws.TypeOf<BaseException>());
        }

        [Test]
        public void UpdateGraphLayoutAsync_GraphLayoutCreatedDateNotEqual_ExpectArgumentException()
        {
            var graphLayout = _repository.Create<AEVA.DataContracts.GraphLayout>().Object;
            graphLayout.Id = "layoutId";
            graphLayout.CreatedDate = new DateTime(2020, 12, 11);
            GraphLayout originalGraphLayout = _repository.Create<GraphLayout>().Object;
            originalGraphLayout.Id = "layoutId";
            originalGraphLayout.CreatedDate = new DateTime(2010, 12, 11);

            Mock.Get(_fakeGraphStore)
               .Setup(controller => controller.TryGetLayoutAsync(
                             It.IsAny<string>(),
                             It.IsAny<string>(),
                             It.IsAny<WorkspaceIdentity>()))
               .Returns(Task.FromResult(originalGraphLayout));

            Assert.That(() => _graphController.UpdateGraphLayoutAsync("subId", "rgName", "wsName", "graphId", graphLayout),
                Throws.TypeOf<BaseException>());
        }

        [Test]
        public void UpdateGraphLayoutAsync_GraphLayoutLastModifiedDateNotEqual_ExpectArgumentException()
        {
            var graphLayout = _repository.Create<AEVA.DataContracts.GraphLayout>().Object;
            graphLayout.Id = "layoutId";
            graphLayout.CreatedDate = new DateTime(2020, 12, 11);
            graphLayout.LastModifiedDate = new DateTime(2020, 2, 11);
            GraphLayout originalGraphLayout = _repository.Create<GraphLayout>().Object;
            originalGraphLayout.Id = "layoutId";
            originalGraphLayout.CreatedDate = new DateTime(2020, 12, 11);
            originalGraphLayout.LastModifiedDate = new DateTime(2020, 1, 11);

            Mock.Get(_fakeGraphStore)
               .Setup(controller => controller.TryGetLayoutAsync(
                             It.IsAny<string>(),
                             It.IsAny<string>(),
                             It.IsAny<WorkspaceIdentity>()))
               .Returns(Task.FromResult(originalGraphLayout));

            Assert.That(() => _graphController.UpdateGraphLayoutAsync("subId", "rgName", "wsName", "graphId", graphLayout),
                Throws.TypeOf<BaseException>());
        }

        [Test]
        public void UpdateGraphLayoutAsync_TryBlock_ExpectExceptionThrown()
        {
            var graphLayout = _repository.Create<AEVA.DataContracts.GraphLayout>().Object;
            Mock.Get(_fakeStores)
                .Setup(controller => controller.GetGraphStore(
                              It.IsAny<ClaimsPrincipal>(),
                              It.IsAny<WorkspaceIdentity>()))
                .Throws(new BadRequestException("message"));

            Assert.That(() => _graphController.UpdateGraphLayoutAsync("subId", "rgName", "wsName", "graphId", graphLayout),
                Throws.TypeOf<BaseException>());
        }

        [Test]
        public async Task UpdateGraphLayoutAsync_TryBlock_ExpectNormalOutput()
        {
            var graphLayout = _repository.Create<AEVA.DataContracts.GraphLayout>().Object;
            graphLayout.Id = "layoutId";
            graphLayout.CreatedDate = new DateTime(2020, 12, 11);
            graphLayout.LastModifiedDate = new DateTime(2020, 2, 11);
            GraphLayout originalGraphLayout = _repository.Create<GraphLayout>().Object;
            originalGraphLayout.Id = "layoutId";
            originalGraphLayout.CreatedDate = new DateTime(2020, 12, 11);
            originalGraphLayout.LastModifiedDate = new DateTime(2020, 2, 11);

            _called_GetGraphStore = false;
            bool called_TryGetLayoutAsync = false;
            Mock.Get(_fakeGraphStore)
               .Setup(controller => controller.TryGetLayoutAsync(
                             It.IsAny<string>(),
                             It.IsAny<string>(),
                             It.IsAny<WorkspaceIdentity>()))
               .Returns(Task.FromResult(originalGraphLayout))
               .Callback(() => called_TryGetLayoutAsync = true);

            bool called_UpdateLayoutAsync = false;
            Mock.Get(_fakeGraphStore)
               .Setup(controller => controller.UpdateLayoutAsync(
                             It.IsAny<string>(),
                             It.IsAny<string>(),
                             It.IsAny<GraphLayout>(),
                             It.IsAny<WorkspaceIdentity>()))
               .Returns(Task.FromResult(originalGraphLayout))
               .Callback(() => called_UpdateLayoutAsync = true);

            await _graphController.UpdateGraphLayoutAsync("subId", "rgName", "wsName", "graphId", graphLayout).ConfigureAwait(false);
            Assert.True(_called_GetGraphStore);
            Assert.True(called_UpdateLayoutAsync);
            Assert.True(called_TryGetLayoutAsync);
        }

        [Test]
        public async Task GetNestedGraphInfoAsync_ExpectNormalReturn()
        {
            AEVA.DataContracts.NestedResourceInfo returnInfo = await _graphController.GetNestedGraphInfoAsync("subId", "rgName", "wsName", "graphId").ConfigureAwait(false);
            Assert.IsNotNull(returnInfo);
            Assert.AreEqual(returnInfo.Graphs.Count, 1);
            Assert.AreEqual(returnInfo.Graphs["graphId"].VisualGraph.Graph.Id, "graphId");
            Assert.AreEqual(returnInfo.GraphDrafts.Count, 0);
            Assert.AreEqual(returnInfo.Modules.Count(), 0);
            Assert.AreEqual(returnInfo.DataSourceEntities.Count(), 0);
        }
    }
}
