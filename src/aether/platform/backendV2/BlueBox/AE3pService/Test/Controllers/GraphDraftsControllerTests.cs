﻿// <copyright file="GraphDraftsControllerTests.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Microsoft.Aether.AE3pService.Controllers;
using Microsoft.Aether.BackendCommon;
using Microsoft.Aether.DataContracts;
using Microsoft.Aether.DataContracts.Entity;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Routing;
using Microsoft.Azure.Storage;
using Microsoft.MachineLearning.Common.Core.Exceptions;
using Microsoft.MachineLearning.Common.WebApi.Exceptions;
using Microsoft.MachineLearning.Common.WebApi.TeamAccounts;
using Moq;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;

namespace Microsoft.Aether.AE3pService.Tests.Controllers
{
    [TestFixture]
    public class GraphDraftsControllerTests
    {
        private readonly MockRepository _repository;
        private IStoreCollection _fakeStores;
        private IContextProvider _fakeOperationContextProvider;
        private IWorkspaceResourcesCache _fakeWorkspaceResourcesCache;
        private GraphDraftsController _graphDraftsController;
        private string subId = Guid.NewGuid().ToString();
        private string rgName = "testRgName";
        private string wsName = "testWsName";
        private string id = "testId";

        public GraphDraftsControllerTests()
        {
            _repository = new MockRepository(MockBehavior.Loose) { DefaultValue = DefaultValue.Mock, CallBase = true };
        }

        [SetUp]
        public void Init()
        {
            _fakeStores = _repository.Create<IStoreCollection>().Object;
            _fakeOperationContextProvider = _repository.Create<IContextProvider>().Object;
            _fakeWorkspaceResourcesCache = _repository.Create<IWorkspaceResourcesCache>().Object;
            _graphDraftsController = new GraphDraftsController(_fakeStores, _fakeOperationContextProvider, _fakeWorkspaceResourcesCache);

            var httpContext = new Mock<HttpContext>();
            var workspaceMetadata = new WorkspaceMetadata(Guid.Empty, null, null, Guid.Empty);
            httpContext.SetupGet(a => a.Items).Returns(new Dictionary<object, object>() { { typeof(WorkspaceMetadata), workspaceMetadata } });
            _graphDraftsController.ControllerContext = new ControllerContext(new ActionContext(httpContext.Object, new RouteData(), new Microsoft.AspNetCore.Mvc.Controllers.ControllerActionDescriptor()));
        }

        [Test]
        public void GetGraphDraftAsync_CatchBlock_ExpectExceptionThrown()
        {
            IAmlGraphDraftStore graphDraftStore = _repository.Create<IAmlGraphDraftStore>().Object;

            bool called_GetAmlGraphDraftStore = false;
            Mock.Get(_fakeStores)
                .Setup(controller => controller.GetAmlGraphDraftStore(
                    It.IsAny<ClaimsPrincipal>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Returns(graphDraftStore)
                .Callback(() => called_GetAmlGraphDraftStore = true);

            Mock.Get(graphDraftStore)
                .Setup(controller => controller.GetEntityAsync(
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Throws(new BadRequestException("message"));

            Assert.That(() => _graphDraftsController.GetGraphDraftAsync(subId, rgName, wsName, id), Throws.TypeOf<BaseException>());
            Assert.True(called_GetAmlGraphDraftStore);
        }

        [Test]
        public async Task GetGraphDraftAsync_TryBlock_ExpectNormalReturn()
        {
            IAmlGraphDraftStore graphDraftStore = _repository.Create<IAmlGraphDraftStore>().Object;

            bool called_GetAmlGraphDraftStore = false;
            Mock.Get(_fakeStores)
                .Setup(controller => controller.GetAmlGraphDraftStore(
                    It.IsAny<ClaimsPrincipal>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Returns(graphDraftStore)
                .Callback(() => called_GetAmlGraphDraftStore = true);

            AmlGraphDraftEntity amlGraphEntity = _repository.Create<AmlGraphDraftEntity>().Object;
            amlGraphEntity.Id = "testId";
            bool called_GetEntityAsync = false;
            Mock.Get(graphDraftStore)
                .Setup(controller => controller.GetEntityAsync(
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(amlGraphEntity))
                .Callback(() => called_GetEntityAsync = true);

            var output = await _graphDraftsController.GetGraphDraftAsync(subId, rgName, wsName, id).ConfigureAwait(false);
            Assert.True(called_GetEntityAsync);
            Assert.True(called_GetAmlGraphDraftStore);
            Assert.AreEqual("testId", output.Id);
        }

        [Test]
        public void GetGraphDraftV2Async_CatchBlock_ExpectExceptionThrown()
        {
            IAmlGraphDraftStore graphDraftStore = _repository.Create<IAmlGraphDraftStore>().Object;

            bool called_GetAmlGraphDraftStore = false;
            Mock.Get(_fakeStores)
                .Setup(controller => controller.GetAmlGraphDraftStore(
                    It.IsAny<ClaimsPrincipal>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Returns(graphDraftStore)
                .Callback(() => called_GetAmlGraphDraftStore = true);

            Mock.Get(graphDraftStore)
                .Setup(controller => controller.GetEntityAsync(
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Throws(new BadRequestException("message"));

            Assert.That(() => _graphDraftsController.GetGraphDraftV2Async(subId, rgName, wsName, id), Throws.TypeOf<BaseException>());
            Assert.True(called_GetAmlGraphDraftStore);
        }

        [Test]
        public async Task GetGraphDraftV2Async_TryBlock_ExpectNormalReturn()
        {
            IAmlGraphDraftStore graphDraftStore = _repository.Create<IAmlGraphDraftStore>().Object;

            bool called_GetAmlGraphDraftStore = false;
            Mock.Get(_fakeStores)
                .Setup(controller => controller.GetAmlGraphDraftStore(
                    It.IsAny<ClaimsPrincipal>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Returns(graphDraftStore)
                .Callback(() => called_GetAmlGraphDraftStore = true);

            AmlGraphDraftEntity amlGraphEntity = _repository.Create<AmlGraphDraftEntity>().Object;
            amlGraphEntity.Id = "testId";
            bool called_GetEntityAsync = false;
            Mock.Get(graphDraftStore)
                .Setup(controller => controller.GetEntityAsync(
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(amlGraphEntity))
                .Callback(() => called_GetEntityAsync = true);

            var output = await _graphDraftsController.GetGraphDraftV2Async(subId, rgName, wsName, id).ConfigureAwait(false);
            Assert.True(called_GetEntityAsync);
            Assert.True(called_GetAmlGraphDraftStore);
            Assert.AreEqual("testId", output.Id);
        }

        [Test]
        public void CreateGraphDraftAsync_CatchBlock_ExpectExceptionThrown()
        {
            IAmlGraphDraftStore graphDraftStore = _repository.Create<IAmlGraphDraftStore>().Object;
            var graphDraftInput = _repository.Create<AEVA.DataContracts.GraphDraftEntity>().Object;

            bool called_GetAmlGraphDraftStore = false;
            Mock.Get(_fakeStores)
                .Setup(controller => controller.GetAmlGraphDraftStore(
                    It.IsAny<ClaimsPrincipal>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Returns(graphDraftStore)
                .Callback(() => called_GetAmlGraphDraftStore = true);

            Mock.Get(graphDraftStore)
                .Setup(controller => controller.CreateAsync(
                    It.IsAny<string>(),
                    It.IsAny<AmlGraphDraftEntity>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Throws(new StorageException("message"));

            Assert.That(() => _graphDraftsController.CreateGraphDraftAsync(subId, rgName, wsName, graphDraftInput), Throws.TypeOf<BaseException>());
            Assert.True(called_GetAmlGraphDraftStore);
        }

        [Test]
        public async Task CreateGraphDraftAsync_TryBlock_ExpectNormalReturn()
        {
            IAmlGraphDraftStore graphDraftStore = _repository.Create<IAmlGraphDraftStore>().Object;
            var graphDraftInput = _repository.Create<AEVA.DataContracts.GraphDraftEntity>().Object;

            bool called_GetAmlGraphDraftStore = false;
            Mock.Get(_fakeStores)
                .Setup(controller => controller.GetAmlGraphDraftStore(
                    It.IsAny<ClaimsPrincipal>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Returns(graphDraftStore)
                .Callback(() => called_GetAmlGraphDraftStore = true);

            AmlGraphDraftEntity amlGraphEntity = _repository.Create<AmlGraphDraftEntity>().Object;
            amlGraphEntity.Id = "testId";
            bool called_CreateAsync = false;
            Mock.Get(graphDraftStore)
                .Setup(controller => controller.CreateAsync(
                    It.IsAny<string>(),
                    It.IsAny<AmlGraphDraftEntity>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(amlGraphEntity))
                .Callback(() => called_CreateAsync = true);

            var output = await _graphDraftsController.CreateGraphDraftAsync(subId, rgName, wsName, graphDraftInput).ConfigureAwait(false);
            Assert.True(called_CreateAsync);
            Assert.True(called_GetAmlGraphDraftStore);
            Assert.AreEqual("testId", output.Id);
        }

        [Test]
        public void UpdateGraphDraftAsync_MismatchedGraphId_ExpectArgumentException()
        {
            IAmlGraphDraftStore graphDraftStore = _repository.Create<IAmlGraphDraftStore>().Object;
            var graphDraftInput = _repository.Create<AEVA.DataContracts.GraphDraftEntity>().Object;
            graphDraftInput.Id = "mismatchedId";

            bool called_GetAmlGraphDraftStore = false;
            Mock.Get(_fakeStores)
                .Setup(controller => controller.GetAmlGraphDraftStore(
                    It.IsAny<ClaimsPrincipal>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Returns(graphDraftStore)
                .Callback(() => called_GetAmlGraphDraftStore = true);

            Assert.That(() => _graphDraftsController.UpdateGraphDraftAsync(subId, rgName, wsName, id, graphDraftInput), Throws.TypeOf<BaseException>());
            Assert.False(called_GetAmlGraphDraftStore);
        }

        [Test]
        public void UpdateGraphDraftAsync_CatchBlock_ExpectExceptionThrown()
        {
            IAmlGraphDraftStore graphDraftStore = _repository.Create<IAmlGraphDraftStore>().Object;
            var graphDraftInput = _repository.Create<AEVA.DataContracts.GraphDraftEntity>().Object;
            graphDraftInput.Id = "testId";

            bool called_GetAmlGraphDraftStore = false;
            Mock.Get(_fakeStores)
                .Setup(controller => controller.GetAmlGraphDraftStore(
                    It.IsAny<ClaimsPrincipal>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Returns(graphDraftStore)
                .Callback(() => called_GetAmlGraphDraftStore = true);

            Mock.Get(_fakeOperationContextProvider)
                .Setup(controller => controller.GetCreatedByObject())
                .Throws(new BadRequestException("message"));

            Assert.That(() => _graphDraftsController.UpdateGraphDraftAsync(subId, rgName, wsName, id, graphDraftInput), Throws.TypeOf<BaseException>());
            Assert.False(called_GetAmlGraphDraftStore);
        }

        [Test]
        public async Task DeletePipelineDraftAsync_TryBlock_ExpectNormalReturn()
        {
            IAmlGraphDraftStore graphDraftStore = _repository.Create<IAmlGraphDraftStore>().Object;
            var graphDraftInput = _repository.Create<AEVA.DataContracts.GraphDraftEntity>().Object;
            graphDraftInput.Id = "testId";

            bool called_GetAmlGraphDraftStore = false;
            Mock.Get(_fakeStores)
                .Setup(controller => controller.GetAmlGraphDraftStore(
                    It.IsAny<ClaimsPrincipal>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Returns(graphDraftStore)
                .Callback(() => called_GetAmlGraphDraftStore = true);

            bool called_DeleteAsync = false;
            Mock.Get(graphDraftStore)
                .Setup(controller => controller.DeleteAsync(
                    It.IsAny<string>(),
                    It.IsAny<AmlGraphDraftEntity>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Callback(() => called_DeleteAsync = true);

            await _graphDraftsController.DeleteGraphDraftAsync(subId, rgName, wsName, id, graphDraftInput).ConfigureAwait(false);
            Assert.True(called_GetAmlGraphDraftStore);
            Assert.True(called_DeleteAsync);
        }

        [Test]
        public void DeletePipelineDraftAsync_CatchBlock_ExpectExceptionThrown()
        {
            var graphDraftInput = _repository.Create<AEVA.DataContracts.GraphDraftEntity>().Object;
            graphDraftInput.Id = "testId";

            Mock.Get(_fakeStores)
                .Setup(controller => controller.GetAmlGraphDraftStore(
                    It.IsAny<ClaimsPrincipal>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Throws(new ArgumentException("message"));

            Assert.That(() => _graphDraftsController.DeleteGraphDraftAsync(subId, rgName, wsName, id, graphDraftInput), Throws.TypeOf<BaseException>());
        }

        [Test]
        public async Task GetNestedGraphDraftInfoAsync_ExpectNormalReturn()
        {
            IAmlGraphDraftStore graphDraftStore = _repository.Create<IAmlGraphDraftStore>().Object;

            bool called_GetAmlGraphDraftStore = false;
            Mock.Get(_fakeStores)
                .Setup(controller => controller.GetAmlGraphDraftStore(
                    It.IsAny<ClaimsPrincipal>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Returns(graphDraftStore)
                .Callback(() => called_GetAmlGraphDraftStore = true);

            AmlGraphDraftEntity amlGraphEntity = _repository.Create<AmlGraphDraftEntity>().Object;
            amlGraphEntity.Id = "testId";
            bool called_GetEntityAsync = false;
            Mock.Get(graphDraftStore)
                .Setup(controller => controller.GetEntityAsync(
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(amlGraphEntity))
                .Callback(() => called_GetEntityAsync = true);

            AEVA.DataContracts.NestedResourceInfo returnInfo = await _graphDraftsController.GetNestedGraphDraftInfoAsync(subId, rgName, wsName, id).ConfigureAwait(false);
            Assert.True(called_GetEntityAsync);
            Assert.True(called_GetAmlGraphDraftStore);
            Assert.IsNotNull(returnInfo);
            Assert.AreEqual(returnInfo.GraphDrafts.Count, 1);
            Assert.AreEqual(returnInfo.GraphDrafts["testId"].Id, "testId");
            Assert.AreEqual(returnInfo.Graphs.Count, 0);
            Assert.AreEqual(returnInfo.Modules.Count(), 0);
            Assert.AreEqual(returnInfo.DataSourceEntities.Count(), 0);
        }
    }
}
