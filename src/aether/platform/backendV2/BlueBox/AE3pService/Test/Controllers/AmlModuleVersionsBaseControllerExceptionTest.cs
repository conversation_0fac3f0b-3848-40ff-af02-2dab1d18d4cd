﻿// <copyright file="AmlModuleVersionsBaseControllerExceptionTest.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Security.Claims;
using System.Threading;
using System.Threading.Tasks;
using Common.WebApi.Exceptions;
using Microsoft.Aether.AEVA.DataContracts;
using Microsoft.Aether.BackendCommon;
using Microsoft.Aether.BackendCommon.AzureMonitor;
using Microsoft.Aether.BlueBox.RepositoryResourcesClient;
using Microsoft.Aether.BlueBox.RepositoryResourcesContracts;
using Microsoft.Aether.DataContracts;
using Microsoft.Aether.DataContracts.Entity;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Routing;
using Microsoft.MachineLearning.Common.Core.Contracts.Errors;
using Microsoft.MachineLearning.Common.Core.Exceptions;
using Microsoft.MachineLearning.Common.WebApi.TeamAccounts;
using Microsoft.RelInfra.Common.Exceptions;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;

namespace Microsoft.Aether.AE3pService.Tests
{
    [TestClass]
    public class AmlModuleVersionsBaseControllerExceptionTest
    {
        public Mock<IContextProvider> _contextProvider;
        public Mock<IStoreCollection> _storeCollection;
        public Mock<IWorkspaceResourcesCache> _workspaceResourceCache;
        public Mock<IRepositoryResourcesClient> _client;
        public AmlModuleVersionsRepositoryController _controller;
        public AzureMLModuleVersionCreationInfo _amlModuleVersionCreationInfo;
        public ServiceInvocationException _serviceInvocationException;
        public Mock<IAmlModuleStore> _modulestoreMock;
        private Mock<IAzureMonitorStatusLogger> _azureMonitorStatusEmitter;

        [TestInitialize]
        public void Init()
        {
            _contextProvider = new Mock<IContextProvider>();
            Mock<DataContracts.CreatedBy> createdBy = new Mock<DataContracts.CreatedBy>();
            _contextProvider.Setup(r => r.GetCreatedByObject()).Returns(createdBy.Object);
            _storeCollection = new Mock<IStoreCollection>();
            _workspaceResourceCache = new Mock<IWorkspaceResourcesCache>();
            _client = new Mock<IRepositoryResourcesClient>();
            _azureMonitorStatusEmitter = new Mock<IAzureMonitorStatusLogger>();
            RepositoryMetadata metadata = new RepositoryMetadata();
            _client.Setup(r => r.GetRepositoryByIdAsync(It.IsAny<string>(), It.IsAny<CancellationToken?>())).Returns(Task.FromResult(metadata));
            _controller = new AmlModuleVersionsRepositoryController(_storeCollection.Object, _contextProvider.Object, _workspaceResourceCache.Object, _client.Object, _azureMonitorStatusEmitter.Object);
            _amlModuleVersionCreationInfo = new AzureMLModuleVersionCreationInfo
                (
                    "name",
                    "displayname",
                    "description",
                    true,
                    "moduleExecutionType",
                    "hash",
                    "identifierHash",
                    "amlModuleId",
                    "version");
            _serviceInvocationException = new ServiceInvocationException(
                    operationName: "MockException",
                    calledService: "Mock",
                    httpMethod: HttpMethod.Post,
                    innerException: new Exception("Mock throw ServiceInvocationException."));
            _modulestoreMock = new Mock<IAmlModuleStore>();
        }

        [TestMethod]
        public async Task TestCreateAzureMLModuleVersionWithInvalidName()
        {
            try
            {
                _amlModuleVersionCreationInfo.Name = "azureml://name";
                AzureMLModuleVersion modleVersion = await _controller.CreateAzureMLModuleVersionAsync(repositoryId: "repositoryId",
                    _amlModuleVersionCreationInfo);
            }
            catch (Exception e)
            {
                Assert.IsTrue(e is BaseException);
                Assert.AreEqual("Name is invalid. azureml module version name can not have reserved prefix \"azureml://\"!", e.Message);
            }
        }

        [TestMethod]
        public async Task TestCreateAzureMLModuleVersionWithException()
        {
            try
            {
                _storeCollection.Setup(s => s.GetAmlModuleStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>())).Returns(_modulestoreMock.Object);
                AzureMLModuleVersion modleVersion = await _controller.CreateAzureMLModuleVersionAsync(repositoryId: "repositoryId",
                    _amlModuleVersionCreationInfo);
            }
            catch (Exception e)
            {
                Assert.IsTrue(e is BaseException);
                Assert.AreEqual("Unable to create moduleVersion. The error message is: 'Object reference not set to an instance of an object.'.", e.Message);
            }
        }

        [TestMethod]
        public async Task TestCreateAzureMLModuleVersionWithModuleVersionExist()
        {
            try
            {
                _storeCollection.Setup(s => s.GetAmlModuleStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>())).Returns(_modulestoreMock.Object);
                AmlModuleEntity entity = new AmlModuleEntity();
                entity.Versions = new List<AmlModuleVersionDescriptor>() { new AmlModuleVersionDescriptor() { Version = "version" } };
                _modulestoreMock.Setup(m => m.GetEntityAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<WorkspaceIdentity>())).Returns(Task.FromResult(entity));
                AzureMLModuleVersion modleVersion = await _controller.CreateAzureMLModuleVersionAsync(repositoryId: "repositoryId",
                    _amlModuleVersionCreationInfo);
            }
            catch (Exception e)
            {
                Assert.IsTrue(e is BaseException);
                Assert.AreEqual("version is invalid. Cannot create ModuleVersion, version already exists: \"version\".", e.Message);
            }
        }

        [TestMethod]
        public async Task TestCreateAzureMLModuleVersionWithBaseException()
        {
            try
            {
                _storeCollection.Setup(s => s.GetAmlModuleStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>())).Returns(_modulestoreMock.Object);
                _modulestoreMock.Setup(m => m.GetEntityAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<WorkspaceIdentity>())).Throws(new BaseException(new ArgumentInvalid().ToBaseError(null, "id")));

                AzureMLModuleVersion modleVersion = await _controller.CreateAzureMLModuleVersionAsync(repositoryId: "repositoryId",
                    _amlModuleVersionCreationInfo);
            }
            catch (Exception e)
            {
                Assert.IsTrue(e is BaseException);
                Assert.AreEqual("id is invalid", e.Message);
            }
        }

        [TestMethod]
        public async Task TestCreateAzureMLModuleVersionWithServiceInvokationException()
        {
            try
            {
                _storeCollection.Setup(s => s.GetAmlModuleStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>())).Returns(_modulestoreMock.Object);
                _modulestoreMock.Setup(m => m.GetEntityAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<WorkspaceIdentity>())).Throws(_serviceInvocationException);
                AzureMLModuleVersion modleVersion = await _controller.CreateAzureMLModuleVersionAsync(repositoryId: "repositoryId",
                    _amlModuleVersionCreationInfo);
            }
            catch (Exception e)
            {
                Assert.IsTrue(e is BaseException);
                Assert.AreEqual("Failure in MockException while calling service Mock; HttpMethod: POST; Response StatusCode: ; Exception type: System.Exception; InnerException: Mock throw ServiceInvocationException.", e.Message);
            }
        }

        [TestMethod]
        public async Task TestUpdateAzureMLModuleVersionWithWrongid()
        {
            try
            {
                AzureMLModuleVersion version = new AzureMLModuleVersion() { Data = new AEVA.DataContracts.ModuleEntity() { Id = "wrongid" } };
                AzureMLModuleVersion modleVersion = await _controller.UpdateAzureMLModuleVersionAsync(repositoryId: "repositoryId",
                    id: "id",
                    version);
            }
            catch (Exception e)
            {
                Assert.IsTrue(e is BaseException);
                Assert.AreEqual("id is invalid. moduleId should be equal to updated.Data.Id. moduleId is \"id\", and updated.Data.Id is \"wrongid\".", e.Message);
            }
        }

        [TestMethod]
        public async Task TestUpdateAzureMLModuleVersionWithException()
        {
            try
            {
                AzureMLModuleVersion version = new AzureMLModuleVersion() { Data = new AEVA.DataContracts.ModuleEntity() { Id = "id" } };
                Mock<IModuleStore> modulestoreMock = new Mock<IModuleStore>();
                _storeCollection.Setup(s => s.GetModuleStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>())).Returns(modulestoreMock.Object);
                modulestoreMock.Setup(m => m.GetEntityAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<WorkspaceIdentity>())).Throws(new Exception("Error"));

                AzureMLModuleVersion modleVersion = await _controller.UpdateAzureMLModuleVersionAsync(repositoryId: "repositoryId",
                    id: "id",
                    version);
            }
            catch (Exception e)
            {
                Assert.IsTrue(e is BaseException);
                Assert.AreEqual("Unable to update moduleVersion. The error message is: 'Error'.", e.Message);
            }
        }

        [TestMethod]
        public async Task TestUpdateAzureMLModuleVersionWithBaseException()
        {
            try
            {
                AzureMLModuleVersion version = new AzureMLModuleVersion() { Data = new AEVA.DataContracts.ModuleEntity() { Id = "id" } };
                Mock<IModuleStore> modulestoreMock = new Mock<IModuleStore>();
                _storeCollection.Setup(s => s.GetModuleStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>())).Returns(modulestoreMock.Object);
                modulestoreMock.Setup(m => m.GetEntityAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<WorkspaceIdentity>())).Throws(new BaseException(new ArgumentInvalid().ToBaseError(null, "id")));

                AzureMLModuleVersion modleVersion = await _controller.UpdateAzureMLModuleVersionAsync(repositoryId: "repositoryId",
                    id: "id",
                    version);
            }
            catch (Exception e)
            {
                Assert.IsTrue(e is BaseException);
                Assert.AreEqual("id is invalid", e.Message);
            }
        }

        [TestMethod]
        public async Task TestUpdateAzureMLModuleVersionWithServiceInvocationException()
        {
            try
            {
                AzureMLModuleVersion version = new AzureMLModuleVersion() { Data = new AEVA.DataContracts.ModuleEntity() { Id = "id" } };
                Mock<IModuleStore> modulestoreMock = new Mock<IModuleStore>();
                _storeCollection.Setup(s => s.GetModuleStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>())).Returns(modulestoreMock.Object);
                modulestoreMock.Setup(m => m.GetEntityAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<WorkspaceIdentity>())).Throws(_serviceInvocationException);

                AzureMLModuleVersion modleVersion = await _controller.UpdateAzureMLModuleVersionAsync(repositoryId: "repositoryId",
                    id: "id",
                    version);
            }
            catch (Exception e)
            {
                Assert.IsTrue(e is BaseException);
                Assert.AreEqual("Failure in MockException while calling service Mock; HttpMethod: POST; Response StatusCode: ; Exception type: System.Exception; InnerException: Mock throw ServiceInvocationException.", e.Message);
            }
        }

        [TestMethod]
        public async Task TestGetAzureMLModuleVersionWithException()
        {
            try
            {
                Mock<IModuleStore> modulestoreMock = new Mock<IModuleStore>();
                _storeCollection.Setup(s => s.GetModuleStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>())).Throws(new Exception("Error"));
                AzureMLModuleVersion modleVersion = await _controller.GetAzureMLModuleVersionAsync(repositoryId: "repositoryId",
                    id: "id");
            }
            catch (Exception e)
            {
                Assert.IsTrue(e is BaseException);
                Assert.AreEqual("Unable to get module. The error message is: 'Error'.", e.Message);
            }
        }

        [TestMethod]
        public async Task TestGetAzureMLModuleVersionWithBaseException()
        {
            try
            {
                Mock<IModuleStore> modulestoreMock = new Mock<IModuleStore>();
                _storeCollection.Setup(s => s.GetModuleStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>())).Throws(new BaseException(new ArgumentInvalid().ToBaseError(null, "id")));
                AzureMLModuleVersion modleVersion = await _controller.GetAzureMLModuleVersionAsync(repositoryId: "repositoryId",
                    id: "id");
            }
            catch (Exception e)
            {
                Assert.IsTrue(e is BaseException);
                Assert.AreEqual("id is invalid", e.Message);
            }
        }

        [TestMethod]
        public async Task TestGetAzureMLModuleVersionWithServiceInvocationException()
        {
            try
            {
                _storeCollection.Setup(s => s.GetModuleStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>())).Throws(_serviceInvocationException);
                AzureMLModuleVersion modleVersion = await _controller.GetAzureMLModuleVersionAsync(repositoryId: "repositoryId",
                    id: "id");
            }
            catch (Exception e)
            {
                Assert.IsTrue(e is BaseException);
                Assert.AreEqual("Failure in MockException while calling service Mock; HttpMethod: POST; Response StatusCode: ; Exception type: System.Exception; InnerException: Mock throw ServiceInvocationException.", e.Message);
            }
        }

        [TestMethod]
        public async Task TestGetAzureMLModuleVersionByHashWithException()
        {
            try
            {
                _storeCollection.Setup(s => s.GetModuleStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>())).Throws(new Exception("Error"));
                AzureMLModuleVersion modleVersion = await _controller.GetAzureMLModuleVersionByHashAsync(repositoryId: "repositoryId",
                    identifierHash: "identifierHash");
            }
            catch (Exception e)
            {
                Assert.IsTrue(e is BaseException);
                Assert.AreEqual("Unable to get module version object by hash. The error message is: 'Error'.", e.Message);
            }
        }

        [TestMethod]
        public async Task TestGetAzureMLModuleVersionByHashWithBaseException()
        {
            try
            {
                _storeCollection.Setup(s => s.GetModuleStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>())).Throws(new BaseException(new ArgumentInvalid().ToBaseError(null, "id")));
                AzureMLModuleVersion modleVersion = await _controller.GetAzureMLModuleVersionByHashAsync(repositoryId: "repositoryId",
                    identifierHash: "identifierHash");
            }
            catch (Exception e)
            {
                Assert.IsTrue(e is BaseException);
                Assert.AreEqual("id is invalid", e.Message);
            }
        }

        [TestMethod]
        public async Task TestGetAzureMLModuleVersionByHashWithServiceInvocationException()
        {
            try
            {
                _storeCollection.Setup(s => s.GetModuleStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>())).Throws(_serviceInvocationException);
                AzureMLModuleVersion modleVersion = await _controller.GetAzureMLModuleVersionByHashAsync(repositoryId: "repositoryId",
                    identifierHash: "identifierHash");
            }
            catch (Exception e)
            {
                Assert.IsTrue(e is BaseException);
                Assert.AreEqual("Failure in MockException while calling service Mock; HttpMethod: POST; Response StatusCode: ; Exception type: System.Exception; InnerException: Mock throw ServiceInvocationException.", e.Message);
            }
        }

        [TestMethod]
        public async Task TestBulkGetAzureMLModuleVersionWithException()
        {
            try
            {
                _storeCollection.Setup(s => s.GetModuleStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>())).Throws(new Exception("Error"));
                var modleVersion = await _controller.BulkGetAzureMLModuleVersionAsync(repositoryId: "repositoryId",
                    ids: new string[] { "ids" });
            }
            catch (Exception e)
            {
                Assert.IsTrue(e is BaseException);
                Assert.AreEqual("Unable to get module. The error message is: 'Error'.", e.Message);
            }
        }

        [TestMethod]
        public async Task TestBulkGetAzureMLModuleVersionWithBaseException()
        {
            try
            {
                _storeCollection.Setup(s => s.GetModuleStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>())).Throws(new BaseException(new ArgumentInvalid().ToBaseError(null, "id")));
                var modleVersion = await _controller.BulkGetAzureMLModuleVersionAsync(repositoryId: "repositoryId",
                    ids: new string[] { "ids" });
            }
            catch (Exception e)
            {
                Assert.IsTrue(e is BaseException);
                Assert.AreEqual("id is invalid", e.Message);
            }
        }

        [TestMethod]
        public async Task TestBulkGetAzureMLModuleVersionWithServiceInvocationException()
        {
            try
            {
                _storeCollection.Setup(s => s.GetModuleStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>())).Throws(_serviceInvocationException);
                var modleVersion = await _controller.BulkGetAzureMLModuleVersionAsync(repositoryId: "repositoryId",
                    ids: new string[] { "ids" });
            }
            catch (Exception e)
            {
                Assert.IsTrue(e is BaseException);
                Assert.AreEqual("Failure in MockException while calling service Mock; HttpMethod: POST; Response StatusCode: ; Exception type: System.Exception; InnerException: Mock throw ServiceInvocationException.", e.Message);
            }
        }

        [TestMethod]
        public async Task TestBulkGetAzureMLModuleVersionWithRepositoryNotFound()
        {
            try
            {
                _client.Setup(r => r.GetRepositoryByIdAsync(It.IsAny<string>(), It.IsAny<CancellationToken?>())).Throws(new ResourceNotFoundException());
                var modleVersion = await _controller.BulkGetAzureMLModuleVersionAsync(repositoryId: "repositoryId",
                    ids: new string[] { "ids" });
            }
            catch (Exception e)
            {
                Assert.IsTrue(e is BaseException);
                Assert.AreEqual("Unable to find the repository with the following id: repositoryId. The error message is: 'Resource not found'.", e.Message);
            }
        }

        [TestMethod]
        public async Task TestCreateAzureMLModuleVersionWithIdWithNullModuleId()
        {
            try
            {
                AmlModuleVersionsWorkspaceController amlModuleVersionsWorkspaceController = new AmlModuleVersionsWorkspaceController(_storeCollection.Object, _contextProvider.Object, _workspaceResourceCache.Object, _azureMonitorStatusEmitter.Object);
                _amlModuleVersionCreationInfo.Properties = new Dictionary<string, string>() { { "azureml/id", new Guid().ToString() } };
                _amlModuleVersionCreationInfo.AmlModuleId = string.Empty;
                Mock<IModuleStore> modulestoreMock = new Mock<IModuleStore>();
                _storeCollection.Setup(s => s.GetModuleStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>())).Returns(modulestoreMock.Object);
                TestControllerSetup(amlModuleVersionsWorkspaceController);

                var modleVersion = await amlModuleVersionsWorkspaceController.CreateAzureMLModuleVersionWithIdAsync(subscriptionId: "subscriptionId",
                    resourceGroupName: "resourceGroupName", workspaceName: "workspaceName", _amlModuleVersionCreationInfo);
            }
            catch (Exception e)
            {
                Assert.IsTrue(e is BaseException);
                Assert.AreEqual("AmlModuleId is invalid. azure ml module id can not be null when registering studio module version, \"00000000-0000-0000-0000-000000000000\".", e.Message);
            }
        }

        [TestMethod]
        public async Task TestCreateAzureMLModuleVersionWithIdWithVersionidRegisterd()
        {
            try
            {
                AmlModuleVersionsWorkspaceController amlModuleVersionsWorkspaceController = new AmlModuleVersionsWorkspaceController(_storeCollection.Object, _contextProvider.Object, _workspaceResourceCache.Object, _azureMonitorStatusEmitter.Object);
                _amlModuleVersionCreationInfo.Properties = new Dictionary<string, string>() { { "azureml/id", new Guid().ToString() } };
                _amlModuleVersionCreationInfo.AmlModuleId = string.Empty;
                Mock<IModuleStore> modulestoreMock = new Mock<IModuleStore>();
                _storeCollection.Setup(s => s.GetModuleStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>())).Returns(modulestoreMock.Object);
                Mock<DataContracts.ModuleEntity> model = new Mock<DataContracts.ModuleEntity>();
                modulestoreMock.Setup(m => m.TryGetEntityAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<WorkspaceIdentity>())).Returns(Task.FromResult(model.Object));
                TestControllerSetup(amlModuleVersionsWorkspaceController);

                var modleVersion = await amlModuleVersionsWorkspaceController.CreateAzureMLModuleVersionWithIdAsync(subscriptionId: "subscriptionId",
                    resourceGroupName: "resourceGroupName", workspaceName: "workspaceName", _amlModuleVersionCreationInfo);
            }
            catch (Exception e)
            {
                Assert.IsTrue(e is BaseException);
                Assert.AreEqual("id is invalid. azureml module version id had already been registerd, \"00000000-0000-0000-0000-000000000000\".", e.Message);
            }
        }

        [TestMethod]
        public async Task TestCreateAzureMLModuleVersionWithIdWithInvalidModuleId()
        {
            try
            {
                AmlModuleVersionsWorkspaceController amlModuleVersionsWorkspaceController = new AmlModuleVersionsWorkspaceController(_storeCollection.Object, _contextProvider.Object, _workspaceResourceCache.Object, _azureMonitorStatusEmitter.Object);
                _storeCollection.Setup(s => s.GetAmlModuleStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>())).Returns(_modulestoreMock.Object);
                _amlModuleVersionCreationInfo.Properties = new Dictionary<string, string>() { { "azureml/id", new Guid().ToString() } };
                Mock<IModuleStore> modulestoreMock = new Mock<IModuleStore>();
                _storeCollection.Setup(s => s.GetModuleStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>())).Returns(modulestoreMock.Object);
                _modulestoreMock.Setup(m => m.TryGetEntityAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<WorkspaceIdentity>())).Returns(Task.FromResult((AmlModuleEntity)null));
                TestControllerSetup(amlModuleVersionsWorkspaceController);

                var modleVersion = await amlModuleVersionsWorkspaceController.CreateAzureMLModuleVersionWithIdAsync(subscriptionId: "subscriptionId",
                    resourceGroupName: "resourceGroupName", workspaceName: "workspaceName", _amlModuleVersionCreationInfo);
            }
            catch (Exception e)
            {
                Assert.IsTrue(e is BaseException);
                Assert.AreEqual("AmlModuleId is invalid. invalid azure ml module id is assigned when registering studio module versions, \"00000000-0000-0000-0000-000000000000\".", e.Message);
            }
        }

        private void TestControllerSetup(BaseController controller)
        {
            var httpContext = new Mock<HttpContext>();
            var workspaceMetadata = new WorkspaceMetadata(Guid.Empty, null, null, Guid.Empty);
            httpContext.SetupGet(a => a.Items).Returns(new Dictionary<object, object>() { { typeof(WorkspaceMetadata), workspaceMetadata } });
            controller.ControllerContext = new ControllerContext(new ActionContext(httpContext.Object, new RouteData(), new Microsoft.AspNetCore.Mvc.Controllers.ControllerActionDescriptor()));
        }
    }
}
