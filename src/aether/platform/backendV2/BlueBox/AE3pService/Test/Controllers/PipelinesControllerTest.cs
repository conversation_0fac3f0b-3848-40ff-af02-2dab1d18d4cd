﻿// <copyright file="PipelinesControllerTest.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Microsoft.Aether.AE3pService.Configuration;
using Microsoft.Aether.AEVA.DataContractConverter;
using Microsoft.Aether.BackendCommon;
using Microsoft.Aether.BackendCommon.AzureMonitor;
using Microsoft.Aether.DataContracts;
using Microsoft.Aether.DataContracts.Entity;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using Microsoft.MachineLearning.Common.WebApi.TeamAccounts;
using Moq;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Security.Claims;
using System.Threading.Tasks;
using AEVAEntityStatus = Microsoft.Aether.AEVA.DataContracts.EntityStatus;
using AEVAPipelineCreationInfo = Microsoft.Aether.AEVA.DataContracts.PipelineCreationInfo;
using AEVAPipelineEntity = Microsoft.Aether.AEVA.DataContracts.PipelineEntity;

namespace Microsoft.Aether.AE3pService.Tests.Controllers
{
    [TestFixture]
    public class PipelinesControllerTest
    {
        private Mock<IStoreCollection> _stores;
        private Mock<IContextProvider> _contextProvider;
        private Mock<IWorkspaceResourcesCache> _cache;
        private Mock<IAzureMonitorStatusLogger> _azureMonitorStatusEmitter;

        private PipelinesController _controller;
        private const string _subscriptionId = "TestSubscriptionId";
        private const string _resourceGroupName = "TestResourceGroupName";
        private const string _workspaceName = "workspaceName";

        [SetUp]
        public void SetUp()
        {
            // Setup the config
            var settings = new Dictionary<string, string>
            {
                {"AmlPipelineConfig:PipelineUrlTemplate", @"{0}\{1}\{2}\{3}"},
                {"AmlPipelineConfig:PrivateLinkPipelineUrlTemplate", @"{0}\{1}\{2}\{3}\{4}" },
            };
            var configuration = new ConfigurationBuilder().AddInMemoryCollection(settings).Build();

            var amlpipelineConfig = Mock.Of<IOptionsMonitor<AmlPipelineConfig>>(x => x.CurrentValue == configuration.GetSection("AmlPipelineConfig").Get<AmlPipelineConfig>());

            // Setup other mocks
            _stores = new Mock<IStoreCollection>();
            _contextProvider = new Mock<IContextProvider>();
            _azureMonitorStatusEmitter = new Mock<IAzureMonitorStatusLogger>();
            _cache = new Mock<IWorkspaceResourcesCache>();
            _cache.Setup(c => c.GetWorkspaceIdAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CreatedBy>()))
                .Returns(Task.FromResult(Guid.NewGuid().ToString()));

            _controller = new PipelinesController(amlpipelineConfig, _stores.Object, _contextProvider.Object, _cache.Object, _azureMonitorStatusEmitter.Object);
            var httpContext = new Mock<HttpContext>();
            var workspaceMetadata = new WorkspaceMetadata(Guid.Empty, null, null, Guid.Empty);
            httpContext.SetupGet(a => a.Items).Returns(new Dictionary<object, object>() { { typeof(WorkspaceMetadata), workspaceMetadata } });
            _controller.ControllerContext = new ControllerContext(new ActionContext(httpContext.Object, new RouteData(), new Microsoft.AspNetCore.Mvc.Controllers.ControllerActionDescriptor()));
        }

        [Test]
        public async Task TestCreatePipelineAsync_NormalCase_ExpectedCorrectAEVAPipelineEntity()
        {
            // Setup Mock for GraphStore, PipelineStore
            Mock<IGraphStore> graphStore = new Mock<IGraphStore>();
            graphStore.Setup(g => g.CreateAsync(It.IsAny<string>(), It.IsAny<GraphEntity>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(
                    (string s, GraphEntity entity, WorkspaceIdentity identity) => Task.FromResult(entity));
            graphStore.Setup(g => g.CreateGraphInterfaceAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<EntityInterface>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(
                    Task.FromResult(new EntityInterface()));
            Mock<IPipelineStore> pipelineStore = new Mock<IPipelineStore>();
            pipelineStore.Setup(p => p.CreateAsync(It.IsAny<string>(), It.IsAny<PipelineEntity>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(
                    (string s, PipelineEntity entity, WorkspaceIdentity identity) => Task.FromResult(entity));

            _stores.Setup(s => s.GetGraphStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(graphStore.Object);
            _stores.Setup(s => s.GetPipelineStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(pipelineStore.Object);

            // Setup Mock for _operationContextProvider and ResourceCache
            _contextProvider.Setup(c => c.GetStoreOperationContext(It.IsAny<ClaimsPrincipal>()))
                .Returns(new StoreOperationContext { UserName = "TestUserName" });
            _contextProvider.Setup(c => c.GetCreatedByObject())
                .Returns(new CreatedBy());

            var createInfo = new AEVAPipelineCreationInfo
            {
                Name = "TestPipelineName",
                Description = "TestDescription",
                Version = "*******",

                Graph = new AEVA.DataContracts.GraphEntity { Id = "TestGraphId", ModuleNodes = new List<AEVA.DataContracts.GraphModuleNode>() },
                GraphInterface = new AEVA.DataContracts.EntityInterface(),
                GraphLayout = null
            };

            var result = await _controller.CreatePipelineAsync(_subscriptionId, _resourceGroupName, _workspaceName, createInfo).ConfigureAwait(false);
            Assert.AreEqual("TestPipelineName", result.Name);
            Assert.AreEqual("TestDescription", result.Description);
            Assert.AreEqual("*******", result.Version);
            Assert.IsTrue(result.Url.StartsWith(@$"{_subscriptionId}\{_resourceGroupName}\{_workspaceName}"));
        }

        [Test]
        public void TestCreatePipelineAsync_GetGraphStoreThrowsArgumentException_ExpectedArgumentException()
        {
            _stores.Setup(s => s.GetGraphStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Throws(new ArgumentException());

            var createInfo = new AEVAPipelineCreationInfo
            {
                Name = "TestPipelineName",
                Description = "TestDescription",
                Version = "*******",

                Graph = new AEVA.DataContracts.GraphEntity { Id = "TestGraphId", ModuleNodes = new List<AEVA.DataContracts.GraphModuleNode>() },
                GraphInterface = new AEVA.DataContracts.EntityInterface(),
                GraphLayout = null
            };

            Assert.ThrowsAsync<ArgumentException>(async () => await _controller.CreatePipelineAsync(_subscriptionId, _resourceGroupName, _workspaceName, createInfo).ConfigureAwait(false));
        }

        [Test]
        public async Task TestGetPipelineUrl_WorkspaceIdentityIsBehindPrivateLink_ExpectedPipelineUrlWithWorkspaceId()
        {
            var workspaceIdentity = new WorkspaceIdentity
            {
                WorkspaceId = "TestWorkspaceId",
                WorkspaceName = "TestWorkspaceName",
                ResourceGroupName = "TestResourceGroupName",
                SubscriptionId = "TestSubscriptionId",
                BehindPrivateLink = true,
            };

            MethodInfo method_GetPipelineUrl = typeof(PipelinesController).GetMethod("GetPipelineUrl", BindingFlags.Instance | BindingFlags.NonPublic);
            var task = (Task<string>)method_GetPipelineUrl.Invoke(_controller, new object[] { workspaceIdentity, new CreatedBy(), "TestPipelineEntityId" });
            var result = await task.ConfigureAwait(false);
            Assert.AreEqual(@"TestWorkspaceId\TestSubscriptionId\TestResourceGroupName\TestWorkspaceName\TestPipelineEntityId", result);
        }

        [Test]
        public async Task TestGetPipelineAsync_NormalCase_ExpectedCorrectAEVAPipelineEntity()
        {
            Mock<IPipelineStore> pipelineStore = new Mock<IPipelineStore>();
            pipelineStore.Setup(p => p.GetEntityAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(
                    Task.FromResult(new PipelineEntity { Name = "TestPipelineEntity", Version = "*******" }));
            _stores.Setup(s => s.GetPipelineStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(pipelineStore.Object);

            var entity = await _controller.GetPipelineAsync(_subscriptionId, _resourceGroupName, _workspaceName, "TestPipelineId").ConfigureAwait(false);
            Assert.AreEqual("TestPipelineEntity", entity.Name);
            Assert.AreEqual("*******", entity.Version);
        }

        [Test]
        public void TestGetPipelineAsync_GetPipelineStoreThrowsArgumentException_ExpectedArgumentException()
        {
            _stores.Setup(s => s.GetPipelineStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Throws(new ArgumentException());

            Assert.ThrowsAsync<ArgumentException>(async () => await _controller.GetPipelineAsync(_subscriptionId, _resourceGroupName, _workspaceName, "TestPipelineId").ConfigureAwait(false));
        }

        [Test]
        public async Task TestGetPipelinesAsync_NormalCase_ExpectedCorrectListofAEVAPipelineEntity()
        {
            // The HeaderDictionary is needed for adding HTTP headers to the response.
            // This needs a couple of different Mocks, because many properties in the class model are read-only.
            var headerDictionary = new HeaderDictionary();
            headerDictionary.Add("ContinuationToken", "TestContinuationToken");
            headerDictionary.Add("BatchSize", "64");
            var request = new Mock<HttpRequest>();
            request.SetupGet(r => r.Headers).Returns(headerDictionary);
            var response = new Mock<HttpResponse>();
            response.SetupGet(r => r.Headers).Returns(headerDictionary);

            var httpContext = new Mock<HttpContext>();
            httpContext.SetupGet(a => a.Request).Returns(request.Object);
            httpContext.SetupGet(a => a.Response).Returns(response.Object);
            var workspaceMetadata = new WorkspaceMetadata(Guid.Empty, null, null, Guid.Empty);
            httpContext.SetupGet(a => a.Items).Returns(new Dictionary<object, object>() { { typeof(WorkspaceMetadata), workspaceMetadata } });
            _controller.ControllerContext = new ControllerContext(new ActionContext(httpContext.Object, new RouteData(), new AspNetCore.Mvc.Controllers.ControllerActionDescriptor()));

            // Mock stores
            Mock<IPipelineStore> pipelineStore = new Mock<IPipelineStore>();
            IEnumerable<PipelineEntity> listOfEntities = new List<PipelineEntity> { new PipelineEntity { Name = "pipeline1" }, new PipelineEntity { Name = "pipeline2" } };
            pipelineStore.Setup(p => p.ListEntityAsync(It.IsAny<string>(), It.IsAny<bool>(), It.IsAny<string>(), It.IsAny<int>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(new Tuple<IEnumerable<PipelineEntity>, string>(listOfEntities, "TestToken")));
            _stores.Setup(s => s.GetPipelineStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(pipelineStore.Object);

            var result = await _controller.GetPipelinesAsync(_subscriptionId, _resourceGroupName, _workspaceName, true).ConfigureAwait(false);
            Assert.AreEqual(2, result.ToList().Count);
            Assert.AreEqual("pipeline1", result.ToList()[0].Name);
            Assert.AreEqual("pipeline2", result.ToList()[1].Name);
        }

        [Test]
        public void TestGetPipelinesAsync_GetPipelineStoreThrowsArgumentException_ExpectedArgumentException()
        {
            // The HeaderDictionary is needed for adding HTTP headers to the response.
            // This needs a couple of different Mocks, because many properties in the class model are read-only.
            var headerDictionary = new HeaderDictionary();
            headerDictionary.Add("ContinuationToken", "TestContinuationToken");
            headerDictionary.Add("BatchSize", "64");
            var request = new Mock<HttpRequest>();
            request.SetupGet(r => r.Headers).Returns(headerDictionary);
            var response = new Mock<HttpResponse>();
            response.SetupGet(r => r.Headers).Returns(headerDictionary);

            var httpContext = new Mock<HttpContext>();
            httpContext.SetupGet(a => a.Request).Returns(request.Object);
            httpContext.SetupGet(a => a.Response).Returns(response.Object);
            var workspaceMetadata = new WorkspaceMetadata(Guid.Empty, null, null, Guid.Empty);
            httpContext.SetupGet(a => a.Items).Returns(new Dictionary<object, object>() { { typeof(WorkspaceMetadata), workspaceMetadata } });

            _controller.ControllerContext = new ControllerContext(new ActionContext(httpContext.Object, new RouteData(), new AspNetCore.Mvc.Controllers.ControllerActionDescriptor()));

            _stores.Setup(s => s.GetPipelineStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Throws(new ArgumentException());

            Assert.ThrowsAsync<ArgumentException>(async () => await _controller.GetPipelinesAsync(_subscriptionId, _resourceGroupName, _workspaceName, true).ConfigureAwait(false));
        }

        [Test]
        public async Task TestGetPipelineViewAsync_NormalCase_ExpectedCorrectViewEntity()
        {
            Mock<IPipelineStore> pipelineStore = new Mock<IPipelineStore>();
            pipelineStore.Setup(p => p.GetViewEntityAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(
                    Task.FromResult(new PipelineViewEntity { }));
            _stores.Setup(s => s.GetPipelineStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(pipelineStore.Object);

            var entity = await _controller.GetPipelineViewAsync(_subscriptionId, _resourceGroupName, _workspaceName, "TestPipelineId").ConfigureAwait(false);
        }

        [Test]
        public void TestGetPipelineViewAsync_GetPipelineStoreThrowsArgumentException_ExpectedArgumentException()
        {
            Mock<IPipelineStore> pipelineStore = new Mock<IPipelineStore>();
            pipelineStore.Setup(p => p.GetViewEntityAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(
                    Task.FromResult(new PipelineViewEntity { }));
            _stores.Setup(s => s.GetPipelineStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Throws(new ArgumentException());

            Assert.ThrowsAsync<ArgumentException>(async () => await _controller.GetPipelineViewAsync(_subscriptionId, _resourceGroupName, _workspaceName, "TestPipelineId").ConfigureAwait(false));
        }

        [Test]
        public async Task TestGetDeepPipelineViewEntityAsync_NormalCase_ExpectedViewEntity()
        {
            Mock<IPipelineStore> pipelineStore = new Mock<IPipelineStore>();
            pipelineStore.Setup(p => p.GetViewEntityAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(
                    Task.FromResult(new PipelineViewEntity { PipelineEntity = new PipelineEntity { GraphId = "TestGraphId" } }));

            Mock<IGraphStore> graphStore = new Mock<IGraphStore>();
            graphStore.Setup(g => g.GetEntityAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(
                    Task.FromResult(new GraphEntity { }));

            _stores.Setup(s => s.GetPipelineStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(pipelineStore.Object);
            _stores.Setup(s => s.GetGraphStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(graphStore.Object);

            var result = await _controller.GetDeepPipelineViewEntityAsync(_subscriptionId, _resourceGroupName, _workspaceName, "TestPipelineId").ConfigureAwait(false);
            Assert.IsNull(result.GraphWithInterface.Interface);
            Assert.IsEmpty(result.DataSourceEntities);
            Assert.IsEmpty(result.Modules);
        }

        [Test]
        public void TestGetDeepPipelineViewEntityAsync_CacheThrowsArgumentException_ExpectedArgumentException()
        {
            _contextProvider.Setup(c => c.GetCreatedByObject())
                .Throws(new ArgumentException());

            Assert.ThrowsAsync<ArgumentException>(async () => await _controller.GetDeepPipelineViewEntityAsync(_subscriptionId, _resourceGroupName, _workspaceName, "TestPipelineId").ConfigureAwait(false));
        }

        [Test]
        public async Task TestGetPipelinesViewAsync_NormalCase_ExpectedCorrectListOfViewEntities()
        {
            // The HeaderDictionary is needed for adding HTTP headers to the response.
            // This needs a couple of different Mocks, because many properties in the class model are read-only.
            var headerDictionary = new HeaderDictionary();
            headerDictionary.Add("ContinuationToken", "TestContinuationToken");
            headerDictionary.Add("BatchSize", "64");
            var request = new Mock<HttpRequest>();
            request.SetupGet(r => r.Headers).Returns(headerDictionary);
            var response = new Mock<HttpResponse>();
            response.SetupGet(r => r.Headers).Returns(headerDictionary);

            var httpContext = new Mock<HttpContext>();
            httpContext.SetupGet(a => a.Request).Returns(request.Object);
            httpContext.SetupGet(a => a.Response).Returns(response.Object);
            var workspaceMetadata = new WorkspaceMetadata(Guid.Empty, null, null, Guid.Empty);
            httpContext.SetupGet(a => a.Items).Returns(new Dictionary<object, object>() { { typeof(WorkspaceMetadata), workspaceMetadata } });
            _controller.ControllerContext = new ControllerContext(new ActionContext(httpContext.Object, new RouteData(), new AspNetCore.Mvc.Controllers.ControllerActionDescriptor()));

            // Mock stores
            Mock<IPipelineStore> pipelineStore = new Mock<IPipelineStore>();
            IEnumerable<PipelineViewEntity> listOfEntities = new List<PipelineViewEntity> { new PipelineViewEntity { }, new PipelineViewEntity { } };
            pipelineStore.Setup(p => p.ListViewEntityAsync(It.IsAny<string>(), It.IsAny<bool>(), It.IsAny<bool>(), It.IsAny<bool>(), It.IsAny<string>(), It.IsAny<int>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(new Tuple<IEnumerable<PipelineViewEntity>, string>(listOfEntities, "TestToken")));
            _stores.Setup(s => s.GetPipelineStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(pipelineStore.Object);

            var result = await _controller.GetPipelinesViewAsync(_subscriptionId, _resourceGroupName, _workspaceName, true).ConfigureAwait(false);
            Assert.AreEqual(2, result.ToList().Count);
        }

        [Test]
        public void TestGetPipelinesViewAsync__GetPipelineStoreThrowsArgumentException_ExpectedArgumentException()
        {
            // The HeaderDictionary is needed for adding HTTP headers to the response.
            // This needs a couple of different Mocks, because many properties in the class model are read-only.
            var headerDictionary = new HeaderDictionary();
            headerDictionary.Add("ContinuationToken", "TestContinuationToken");
            headerDictionary.Add("BatchSize", "64");
            var request = new Mock<HttpRequest>();
            request.SetupGet(r => r.Headers).Returns(headerDictionary);
            var response = new Mock<HttpResponse>();
            response.SetupGet(r => r.Headers).Returns(headerDictionary);

            var httpContext = new Mock<HttpContext>();
            httpContext.SetupGet(a => a.Request).Returns(request.Object);
            httpContext.SetupGet(a => a.Response).Returns(response.Object);
            var workspaceMetadata = new WorkspaceMetadata(Guid.Empty, null, null, Guid.Empty);
            httpContext.SetupGet(a => a.Items).Returns(new Dictionary<object, object>() { { typeof(WorkspaceMetadata), workspaceMetadata } });
            _controller.ControllerContext = new ControllerContext(new ActionContext(httpContext.Object, new RouteData(), new AspNetCore.Mvc.Controllers.ControllerActionDescriptor()));

            // Mock stores
            _stores.Setup(s => s.GetPipelineStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Throws(new ArgumentException());

            Assert.ThrowsAsync<ArgumentException>(async () => await _controller.GetPipelinesViewAsync(_subscriptionId, _resourceGroupName, _workspaceName, true).ConfigureAwait(false));
        }

        [Test]
        public async Task TestUpdatePipelineStatusAsync_UpdateDeprecatedToActive_ExpectedActiveStatus()
        {
            EntityStatus oldStatus = EntityStatus.Deprecated;
            string newStatus = "Active";

            Mock<IPipelineStore> pipelineStore = new Mock<IPipelineStore>();
            pipelineStore.Setup(p => p.UpdateStatusAsync(It.IsAny<string>(), It.IsAny<EntityStatus>(), It.IsAny<string>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(
                    (string s, EntityStatus status, string s0, WorkspaceIdentity identity) =>
                    {
                        oldStatus = status;
                        return Task.FromResult(oldStatus);
                    });
            _stores.Setup(s => s.GetPipelineStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(pipelineStore.Object);

            await _controller.UpdatePipelineStatusAsync(_subscriptionId, _resourceGroupName, _workspaceName, "TestPipelineId", newStatus).ConfigureAwait(false);
            Assert.AreEqual(EntityStatus.Active, oldStatus);
        }

        [Test]
        public void TestUpdatePipelineStatusAsync_UpdateToActiveFailed_ExpectedActiveStatus()
        {
            EntityStatus oldStatus = EntityStatus.Deprecated;
            string newStatus = "Active";

            Mock<IPipelineStore> pipelineStore = new Mock<IPipelineStore>();
            pipelineStore.Setup(p => p.UpdateStatusAsync(It.IsAny<string>(), It.IsAny<EntityStatus>(), It.IsAny<string>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(oldStatus));
            _stores.Setup(s => s.GetPipelineStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(pipelineStore.Object);

            Assert.ThrowsAsync<Exception>(async () => await _controller.UpdatePipelineStatusAsync(_subscriptionId, _resourceGroupName, _workspaceName, "TestPipelineId", newStatus).ConfigureAwait(false));
            Assert.AreEqual(EntityStatus.Deprecated, oldStatus);
        }

        [Test]
        public async Task TestUpdatePipelineStatusAsync_UpdateActiveToDeprecated_ExpectedDeprecatedStatus()
        {
            EntityStatus oldStatus = EntityStatus.Active;
            string newStatus = "Deprecated";

            Mock<IPipelineStore> pipelineStore = new Mock<IPipelineStore>();
            pipelineStore.Setup(p => p.UpdateStatusAsync(It.IsAny<string>(), It.IsAny<EntityStatus>(), It.IsAny<string>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(
                    (string s, EntityStatus status, string s0, WorkspaceIdentity identity) =>
                    {
                        oldStatus = status;
                        return Task.FromResult(oldStatus);
                    });
            Mock<IPipelineScheduleStore> scheduleStore = new Mock<IPipelineScheduleStore>();
            scheduleStore.Setup(s => s.PipelineHasActiveScheduleAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(false));
            _stores.Setup(s => s.GetPipelineStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(pipelineStore.Object);
            _stores.Setup(s => s.GetPipelineScheduleStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(scheduleStore.Object);

            await _controller.UpdatePipelineStatusAsync(_subscriptionId, _resourceGroupName, _workspaceName, "TestPipelineId", newStatus).ConfigureAwait(false);
            Assert.AreEqual(EntityStatus.Deprecated, oldStatus);
        }

        [Test]
        public async Task TestUpdatePipelineStatusAsync_UpdateActiveToDisabled_ExpectedDisabledStatus()
        {
            EntityStatus oldStatus = EntityStatus.Active;
            string newStatus = "Disabled";

            Mock<IPipelineStore> pipelineStore = new Mock<IPipelineStore>();
            pipelineStore.Setup(p => p.UpdateStatusAsync(It.IsAny<string>(), It.IsAny<EntityStatus>(), It.IsAny<string>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(
                    (string s, EntityStatus status, string s0, WorkspaceIdentity identity) =>
                    {
                        oldStatus = status;
                        return Task.FromResult(oldStatus);
                    });
            Mock<IPipelineScheduleStore> scheduleStore = new Mock<IPipelineScheduleStore>();
            scheduleStore.Setup(s => s.PipelineHasActiveScheduleAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(false));
            _stores.Setup(s => s.GetPipelineStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(pipelineStore.Object);
            _stores.Setup(s => s.GetPipelineScheduleStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(scheduleStore.Object);

            await _controller.UpdatePipelineStatusAsync(_subscriptionId, _resourceGroupName, _workspaceName, "TestPipelineId", newStatus).ConfigureAwait(false);
            Assert.AreEqual(EntityStatus.Disabled, oldStatus);
        }

        [Test]
        public void TestUpdatePipelineStatusAsync_UpdateActiveToDeprecatedFailed_ExpectedArgumentException()
        {
            EntityStatus oldStatus = EntityStatus.Active;
            string newStatus = "Deprecated";

            Mock<IPipelineStore> pipelineStore = new Mock<IPipelineStore>();
            pipelineStore.Setup(p => p.UpdateStatusAsync(It.IsAny<string>(), It.IsAny<EntityStatus>(), It.IsAny<string>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(
                    (string s, EntityStatus status, string s0, WorkspaceIdentity identity) =>
                    {
                        oldStatus = status;
                        return Task.FromResult(oldStatus);
                    });
            Mock<IPipelineScheduleStore> scheduleStore = new Mock<IPipelineScheduleStore>();
            scheduleStore.Setup(s => s.PipelineHasActiveScheduleAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(true));
            _stores.Setup(s => s.GetPipelineStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(pipelineStore.Object);
            _stores.Setup(s => s.GetPipelineScheduleStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(scheduleStore.Object);

            Assert.ThrowsAsync<ArgumentException>(async () => await _controller.UpdatePipelineStatusAsync(_subscriptionId, _resourceGroupName, _workspaceName, "TestPipelineId", newStatus).ConfigureAwait(false));
        }

        [Test]
        public void TestUpdatePipelineStatusAsync_ParseStatusFailed_ExpectedInvalidOperationException()
        {
            string newStatus = "Success";
            Assert.ThrowsAsync<InvalidOperationException>(async () => await _controller.UpdatePipelineStatusAsync(_subscriptionId, _resourceGroupName, _workspaceName, "TestPipelineId", newStatus).ConfigureAwait(false));
        }

        [Test]
        public void TestUpdatePipelineStatusAsync_GetPipelineStoreThrowsArgumentException_ExpectedArgumentException()
        {
            string newStatus = "Active";
            _stores.Setup(s => s.GetPipelineStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Throws(new ArgumentException());
            Assert.ThrowsAsync<ArgumentException>(async () => await _controller.UpdatePipelineStatusAsync(_subscriptionId, _resourceGroupName, _workspaceName, "TestPipelineId", newStatus).ConfigureAwait(false));
        }

        [Test]
        public async Task TestUpdatePipelineAsync_NormalCase_ExpectedValidationPassed()
        {
            var oldPipeline = new PipelineEntity
            {
                Name = "pipeline1",
                Description = "Old description.",
                LastModifiedDate = DateTime.UtcNow,
                KvTags = new Dictionary<string, string> { { "key1", "value1" } },
                Properties = new Dictionary<string, string> { { "property1", "value1" } },
            };

            var aevaUpdatedPipeline = new AEVAPipelineEntity
            {
                Name = "pipeline1",
                Description = "New description.",
                LastModifiedDate = DateTime.UtcNow.AddSeconds(1),
                KvTags = new Dictionary<string, string> { { "key1", "value1" }, { "key2", "value2" } },
                Properties = new Dictionary<string, string> { { "property1", "value1" }, { "property2", "value2" } },
            };

            var updatedPipeline = ContractConverter.Convert<AEVAPipelineEntity, PipelineEntity>(aevaUpdatedPipeline);

            Mock<IPipelineStore> pipelineStore = new Mock<IPipelineStore>();
            pipelineStore.Setup(p => p.GetEntityAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(
                    Task.FromResult(oldPipeline));
            pipelineStore.Setup(p => p.UpdateAsync(It.IsAny<string>(), It.IsAny<PipelineEntity>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(
                    (string workspaceId, PipelineEntity entity, WorkspaceIdentity identity) =>
                    {
                        return Task.FromResult(updatedPipeline);
                    });

            _stores.Setup(s => s.GetPipelineStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(pipelineStore.Object);

            var updatedResult = await _controller.UpdatePipelineAsync(_subscriptionId, _resourceGroupName, _workspaceName, "TestPipelineId", aevaUpdatedPipeline).ConfigureAwait(false);
            Assert.AreEqual(updatedResult.Description, "New description.");
        }

        [Test]
        public async Task TestUpdatePipelineAsync_UpdateImmutable_ExpectedValidationFailed()
        {
            var oldPipeline = new PipelineEntity
            {
                Name = "pipeline1",
                Description = "Old description.",
                LastModifiedDate = DateTime.UtcNow,
                KvTags = new Dictionary<string, string> { { "key1", "value1" } },
                Properties = new Dictionary<string, string> { { "property1", "value1" } },
                EntityStatus = EntityStatus.Active,
            };

            var aevaUpdatedPipeline = new AEVAPipelineEntity
            {
                Name = "pipeline1",
                Description = "New description.",
                LastModifiedDate = DateTime.UtcNow.AddSeconds(1),
                EntityStatus = AEVAEntityStatus.Deprecated,
            };

            var updatedPipeline = ContractConverter.Convert<AEVAPipelineEntity, PipelineEntity>(aevaUpdatedPipeline);

            Mock<IPipelineStore> pipelineStore = new Mock<IPipelineStore>();
            pipelineStore.Setup(p => p.GetEntityAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(oldPipeline));
            pipelineStore.Setup(p => p.UpdateAsync(It.IsAny<string>(), It.IsAny<PipelineEntity>(), It.IsAny<WorkspaceIdentity>()))
                .Returns((string workspaceId, PipelineEntity entity, WorkspaceIdentity identity) => Task.FromResult(updatedPipeline));
            _stores.Setup(s => s.GetPipelineStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(pipelineStore.Object);

            try
            {
                await _controller.UpdatePipelineAsync(_subscriptionId, _resourceGroupName, _workspaceName, "TestPipelineId", aevaUpdatedPipeline).ConfigureAwait(false);
                Assert.Fail("Pipeline update should fail due to immutable field is updated.");
            }
            catch (Exception ex)
            {
                Assert.IsTrue(ex.Message.Contains("The field EntityStatus is immutable."));
            }
        }

        [Test]
        public async Task TestCreatePipelineFromPipelineDraftAsync_NormalCase_ExpectedCorrectAEVAPipelineEntity()
        {
            // Setup Mock for GraphStore, PipelineStore
            Mock<IGraphStore> graphStore = new Mock<IGraphStore>();
            graphStore.Setup(g => g.CreateAsync(It.IsAny<string>(), It.IsAny<GraphEntity>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(
                    (string s, GraphEntity entity, WorkspaceIdentity identity) => Task.FromResult(entity));
            graphStore.Setup(g => g.CreateGraphInterfaceAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<EntityInterface>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(
                    Task.FromResult(new EntityInterface()));
            Mock<IPipelineStore> pipelineStore = new Mock<IPipelineStore>();
            pipelineStore.Setup(p => p.CreateAsync(It.IsAny<string>(), It.IsAny<PipelineEntity>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(
                    (string s, PipelineEntity entity, WorkspaceIdentity identity) => Task.FromResult(entity));

            Mock<IAmlGraphDraftStore> draftStore = new Mock<IAmlGraphDraftStore>();
            draftStore.Setup(d => d.GetEntityAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(new AmlGraphDraftEntity
                {
                    ModuleNodes = new List<GraphModuleNode>(),
                    DatasetNodes = new List<GraphDatasetNode>(),
                    EntityInterface = new EntityInterface(),
                }));

            _stores.Setup(s => s.GetGraphStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(graphStore.Object);
            _stores.Setup(s => s.GetPipelineStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(pipelineStore.Object);
            _stores.Setup(s => s.GetAmlGraphDraftStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(draftStore.Object);

            // Setup Mock for _operationContextProvider and ResourceCache
            _contextProvider.Setup(c => c.GetStoreOperationContext(It.IsAny<ClaimsPrincipal>()))
                .Returns(new StoreOperationContext { UserName = "TestUserName" });
            _contextProvider.Setup(c => c.GetCreatedByObject())
                .Returns(new CreatedBy());

            var pipelineDraft = new AEVA.DataContracts.PipelineDraft
            {
                Name = "TestPipelineName",
                Description = "TestDescription",
                PipelineSubmissionInfo = new AEVA.DataContracts.PipelineSubmissionInfo(),
            };

            var result = await _controller.CreatePipelineFromPipelineDraftAsync(_subscriptionId, _resourceGroupName, _workspaceName, pipelineDraft).ConfigureAwait(false);
            Assert.AreEqual("TestPipelineName", result.Name);
            Assert.AreEqual("TestDescription", result.Description);
            Assert.IsTrue(result.Url.StartsWith(@$"{_subscriptionId}\{_resourceGroupName}\{_workspaceName}"));
        }

        [Test]
        public void TestCreatePipelineFromPipelineDraftAsync_ContextProviderThrowsException_ExpectedArgumentException()
        {
            _contextProvider.Setup(c => c.GetCreatedByObject())
                .Throws(new ArgumentException());

            Assert.ThrowsAsync<ArgumentException>(async () => await _controller.CreatePipelineFromPipelineDraftAsync(_subscriptionId, _resourceGroupName, _workspaceName, new AEVA.DataContracts.PipelineDraft()).ConfigureAwait(false));
        }

        [Test]
        public void TestConvertNodeCompositionMode_ConvertNoneMode_ExpectedNoneMode()
        {
            MethodInfo method_ConvertNodeCompositionMode = typeof(PipelinesController).GetMethod("ConvertNodeCompositionMode", BindingFlags.Static | BindingFlags.NonPublic);
            var mode = method_ConvertNodeCompositionMode.Invoke(_controller, new object[] { AEVA.DataContracts.NodeCompositionMode.None });
            Assert.AreEqual(NodeCompositionMode.None, mode);
        }

        [Test]
        public void TestConvertNodeCompositionMode_ConvertOnlySequentialMode_ExpectedOnlySequentialMode()
        {
            MethodInfo method_ConvertNodeCompositionMode = typeof(PipelinesController).GetMethod("ConvertNodeCompositionMode", BindingFlags.Static | BindingFlags.NonPublic);
            var mode = method_ConvertNodeCompositionMode.Invoke(_controller, new object[] { AEVA.DataContracts.NodeCompositionMode.OnlySequential });
            Assert.AreEqual(NodeCompositionMode.OnlySequential, mode);
        }

        [Test]
        public void TestConvertNodeCompositionMode_ConvertFullMode_ExpectedFullMode()
        {
            MethodInfo method_ConvertNodeCompositionMode = typeof(PipelinesController).GetMethod("ConvertNodeCompositionMode", BindingFlags.Static | BindingFlags.NonPublic);
            var mode = method_ConvertNodeCompositionMode.Invoke(_controller, new object[] { AEVA.DataContracts.NodeCompositionMode.Full });
            Assert.AreEqual(NodeCompositionMode.Full, mode);
        }

        [Test]
        public void TestConvertNodeCompositionMode_ConvertErrorMode_ExpectedArgumentException()
        {
            MethodInfo method_ConvertNodeCompositionMode = typeof(PipelinesController).GetMethod("ConvertNodeCompositionMode", BindingFlags.Static | BindingFlags.NonPublic);
            var exp = Assert.Throws<TargetInvocationException>(() => method_ConvertNodeCompositionMode.Invoke(_controller, new object[] { (AEVA.DataContracts.NodeCompositionMode)3 }));
            Assert.IsInstanceOf<ArgumentException>(exp.InnerException);
        }
    }
}
