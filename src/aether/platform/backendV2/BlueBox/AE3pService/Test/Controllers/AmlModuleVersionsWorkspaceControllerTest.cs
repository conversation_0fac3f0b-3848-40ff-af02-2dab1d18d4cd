﻿// <copyright file="AmlModuleVersionsWorkspaceControllerTest.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using Microsoft.Aether.AEVA.DataContracts;
using Microsoft.Aether.BackendCommon;
using Microsoft.Aether.BackendCommon.AzureMonitor;
using Microsoft.Aether.DataContracts;
using Microsoft.Aether.DataContracts.Entity;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Routing;
using Microsoft.MachineLearning.Common.WebApi.TeamAccounts;
using Moq;
using NUnit.Framework;

namespace Microsoft.Aether.AE3pService.Tests.Controllers
{
    [TestFixture]
    public class AmlModuleVersionsWorkspaceControllerTest
    {
        private Mock<IStoreCollection> _stores;
        private Mock<IContextProvider> _operationContextProvider;
        private Mock<IWorkspaceResourcesCache> _workspaceResourcesCache;

        private Mock<IModuleStore> _moduleStore;
        private Mock<IDataTypeStore> _dataTypeStore;
        private Mock<IAmlModuleStore> _amlModuleStore;
        private Mock<IGraphStore> _graphStore;
        private Mock<IAmlGraphDraftStore> _graphDraftStore;
        private Mock<IAzureMonitorStatusLogger> _azureMonitorStatusEmitter;
        private AmlModuleVersionsWorkspaceController _controller;
        private readonly string _subscriptionId = "TestSubscriptionId";
        private readonly string _resourceGroupName = "TestResourceGroupName";
        private readonly string _workspaceName = "TestWorkspaceName";

        [SetUp]
        public void SetUp()
        {
            _moduleStore = new Mock<IModuleStore>();
            IEnumerable<DataContracts.ModuleEntity> versions =
                new List<DataContracts.ModuleEntity> { new DataContracts.ModuleEntity { Name = "fakeVersion" } };
            _moduleStore.Setup(m => m.BulkGetIdsAsync(It.IsAny<string>(), It.IsAny<string[]>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(versions));
            _moduleStore.Setup(m => m.GetEntityAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(new DataContracts.ModuleEntity { Name = "fakeVersion" }));
            _moduleStore.Setup(m => m.TryGetEntityByHashAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(new DataContracts.ModuleEntity { Name = "fakeVersion" }));
            _moduleStore.Setup(m => m.UpdateAsync(It.IsAny<string>(), It.IsAny<DataContracts.ModuleEntity>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(new DataContracts.ModuleEntity { Name = "fakeVersion" }));
            _azureMonitorStatusEmitter = new Mock<IAzureMonitorStatusLogger>();
            _moduleStore.Setup(m => m.CreateAsync(It.IsAny<string>(), It.IsAny<DataContracts.ModuleEntity>(), It.IsAny<WorkspaceIdentity>(), It.IsAny<DataContracts.CreatedBy>()))
                .Returns(
                    (string workspaceId, DataContracts.ModuleEntity moduleEntity, WorkspaceIdentity identity, DataContracts.CreatedBy createdBy) =>
                    {
                        if (moduleEntity.Name == "subgraph")
                        {
                            moduleEntity.Id = Guid.NewGuid().ToString();
                            return Task.FromResult(moduleEntity);
                        }
                        else
                        {
                            return Task.FromResult(new DataContracts.ModuleEntity { Name = "fakeVersion" });
                        }
                    });

            _dataTypeStore = new Mock<IDataTypeStore>();
            _amlModuleStore = new Mock<IAmlModuleStore>();
            _amlModuleStore.Setup(a => a.GetEntityAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(new AmlModuleEntity { Name = @"azureml://fakeVersion" }));
            _amlModuleStore.Setup(a => a.TryGetEntityAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(new AmlModuleEntity { Name = @"azureml://fakeVersion" }));

            _graphStore = new Mock<IGraphStore>();
            _graphStore.Setup(g => g.CreateAsync(It.IsAny<string>(), It.IsAny<DataContracts.GraphEntity>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(
                    (string workspaceId, DataContracts.GraphEntity graphEntity, WorkspaceIdentity identity) =>
                    {
                        graphEntity.Id = Guid.NewGuid().ToString();
                        return Task.FromResult(graphEntity);
                    });

            _graphDraftStore = new Mock<IAmlGraphDraftStore>();
            _graphDraftStore.Setup(g => g.CreateAsync(It.IsAny<string>(), It.IsAny<AmlGraphDraftEntity>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(
                    (string workspaceId, AmlGraphDraftEntity graphDraftEntity, WorkspaceIdentity identity) =>
                    {
                        graphDraftEntity.Id = Guid.NewGuid().ToString();
                        return Task.FromResult(graphDraftEntity);
                    });

            _stores = new Mock<IStoreCollection>();
            _stores.Setup(s => s.GetModuleStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(_moduleStore.Object);
            _stores.Setup(s => s.GetDataTypeStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(_dataTypeStore.Object);
            _stores.Setup(s => s.GetAmlModuleStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(_amlModuleStore.Object);
            _stores.Setup(s => s.GetGraphStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(_graphStore.Object);
            _stores.Setup(s => s.GetAmlGraphDraftStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(_graphDraftStore.Object);

            _operationContextProvider = new Mock<IContextProvider>();
            _operationContextProvider.Setup(o => o.GetCreatedByObject())
                .Returns(new DataContracts.CreatedBy());

            _workspaceResourcesCache = new Mock<IWorkspaceResourcesCache>();

            _controller = new AmlModuleVersionsWorkspaceController(_stores.Object, _operationContextProvider.Object, _workspaceResourcesCache.Object, _azureMonitorStatusEmitter.Object);
            var httpContext = new Mock<HttpContext>();
            var workspaceMetadata = new WorkspaceMetadata(Guid.Empty, null, null, Guid.Empty);
            httpContext.SetupGet(a => a.Items).Returns(new Dictionary<object, object>() { { typeof(WorkspaceMetadata), workspaceMetadata } });

            _controller.ControllerContext = new ControllerContext(new ActionContext(httpContext.Object, new RouteData(), new Microsoft.AspNetCore.Mvc.Controllers.ControllerActionDescriptor()));
        }

        [Test]
        public async Task TestCreateAzureMLModuleVersionAsync_NormalCase_ExpectedCorrectAzureMLModuleVersion()
        {
            var creationInfo = new AzureMLModuleVersionCreationInfo(@"fakeVersion", "test", "test", false, "test", "test", "test", "fakeAmlId", "test");
            var result = await _controller.CreateAzureMLModuleVersionAsync(_subscriptionId, _resourceGroupName, _workspaceName, creationInfo).ConfigureAwait(false);
            Assert.AreEqual("fakeVersion", result.Data.Name);
        }

        [Test]
        public async Task TestUpdateAzureMLModuleVersionAsync_NormalCase_ExpectedCorrectAzureMLModuleVersion()
        {
            var creationInfo = new AzureMLModuleVersion { Data = new AEVA.DataContracts.ModuleEntity { Name = "fakeVersion", Id = "testId", EntityStatus = AEVA.DataContracts.EntityStatus.Active } };
            var result = await _controller.UpdateAzureMLModuleVersionAsync(_subscriptionId, _resourceGroupName, _workspaceName, "testId", creationInfo).ConfigureAwait(false);
            Assert.AreEqual("fakeVersion", result.Data.Name);
        }

        [Test]
        public async Task TestGetAzureMLModuleVersionAsync_NormalCase_ExpectedCorrectAzureMLModuleVersion()
        {
            var result = await _controller.GetAzureMLModuleVersionAsync(_subscriptionId, _resourceGroupName, _workspaceName, "testId").ConfigureAwait(false);
            Assert.AreEqual("fakeVersion", result.Data.Name);
        }

        [Test]
        public async Task TestGetAzureMLModuleVersionByHashAsync_NormalCase_ExpectedCorrectAzureMLModuleVersion()
        {
            var result = await _controller.GetAzureMLModuleVersionByHashAsync(_subscriptionId, _resourceGroupName, _workspaceName, "testHash").ConfigureAwait(false);
            Assert.AreEqual("fakeVersion", result.Data.Name);
        }

        [Test]
        public async Task TestBulkGetAzureMLModuleVersionAsync_NormalCase_ExpectedCorrectAzureMLModuleVersionList()
        {
            string[] ids = { "testId1", "testId2" };
            var result = await _controller.BulkGetAzureMLModuleVersionAsync(_subscriptionId, _resourceGroupName, _workspaceName, ids).ConfigureAwait(false);
            Assert.AreEqual(1, result.Count());
            Assert.AreEqual("fakeVersion", result.ToList()[0].Data.Name);
        }

        [Test]
        public async Task TestCreateAzureMLModuleVersionWithIdAsync_NormalCase_ExpectedCorrectAzureMLModuleVersion()
        {
            var creationInfo = new AzureMLModuleVersionCreationInfo(@"fakeVersion", "test", "test", false, "test", "test", "test", "fakeAmlId", "test");
            creationInfo.Properties = new Dictionary<string, string> { { @"azureml/id", Guid.NewGuid().ToString() } };
            var result = await _controller.CreateAzureMLModuleVersionWithIdAsync(_subscriptionId, _resourceGroupName, _workspaceName, creationInfo).ConfigureAwait(false);
            Assert.AreEqual("fakeVersion", result.Data.Name);
        }

        [Test]
        public async Task CreateSubGraphModuleVersionAsync_NormalCase()
        {
            SubGraphVersionCreationInfo creationInfo = new SubGraphVersionCreationInfo()
            {
                Name = "subgraph",
                AmlModuleId = "fakeAmlId",
                VisualGraph = new AEVA.DataContracts.VisualGraph()
                {
                    Graph = new AEVA.DataContracts.GraphEntity()
                    {
                        ModuleNodes = new List<AEVA.DataContracts.GraphModuleNode>(),
                        Edges = new List<AEVA.DataContracts.GraphEdge>()
                    }
                },
                Interface = new AEVA.DataContracts.EntityInterface(),
                IsDeterministic = false
            };
            var result = await _controller.CreateSubGraphModuleVersionAsync(_subscriptionId, _resourceGroupName, _workspaceName, creationInfo).ConfigureAwait(false);
            Assert.AreEqual("subgraph", result.Data.Name);
            Assert.IsNotNull(result.Data.CloudSettings.SubGraphConfig.GraphId);
            Assert.AreEqual(result.Data.IsDeterministic, false);

            creationInfo.IsDeterministic = true;
            result = await _controller.CreateSubGraphModuleVersionAsync(_subscriptionId, _resourceGroupName, _workspaceName, creationInfo).ConfigureAwait(false);
            Assert.AreEqual("subgraph", result.Data.Name);
            Assert.IsNotNull(result.Data.CloudSettings.SubGraphConfig.GraphId);
            Assert.AreEqual(result.Data.IsDeterministic, true);

            creationInfo.CreateAsDraft = true;
            result = await _controller.CreateSubGraphModuleVersionAsync(_subscriptionId, _resourceGroupName, _workspaceName, creationInfo).ConfigureAwait(false);
            Assert.AreEqual("subgraph", result.Data.Name);
            Assert.IsNotNull(result.Data.CloudSettings.SubGraphConfig.GraphDraftId);
        }
    }
}
