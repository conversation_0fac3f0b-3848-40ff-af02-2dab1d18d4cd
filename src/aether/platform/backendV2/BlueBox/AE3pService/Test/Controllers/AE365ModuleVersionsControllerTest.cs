﻿// <copyright file="AE365ModuleVersionsControllerTest.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Reflection;
using System.Security.Claims;
using System.Threading.Tasks;
using Microsoft.Aether.AEVA.DataContracts;
using Microsoft.Aether.BackendCommon;
using Microsoft.Aether.BackendCommon.AzureMonitor;
using Microsoft.Aether.DataContracts;
using Microsoft.Aether.DataContracts.Entity;
using Microsoft.Aether.DataContracts.Error;
using Microsoft.MachineLearning.Common.Core.Contracts.Errors;
using Microsoft.MachineLearning.Common.Core.Exceptions;
using Microsoft.RelInfra.Common.Exceptions;
using Moq;
using NUnit.Framework;
using Assert = NUnit.Framework.Assert;
using CreatedBy = Microsoft.Aether.DataContracts.CreatedBy;
using ModuleEntity = Microsoft.Aether.AEVA.DataContracts.ModuleEntity;
using StructuredInterface = Microsoft.Aether.AEVA.DataContracts.StructuredInterface;

namespace Microsoft.Aether.AE3pService.Tests
{
    [TestFixture]
    public class AE365ModuleVersionsControllerTest
    {
        private IStoreCollection _fakeIStoreCollection;
        private IContextProvider _fakeIContextProvider;
        private IWorkspaceResourcesCache _fakeIWorkspaceResourcesCache;
        private AE365ModuleVersionsController _fakeAE365ModuleVersionsController;
        private Mock<ModulesBaseController> _mockModulesBaseController;
        private Mock<AmlModuleVersionsBaseController> _mockAmlModuleVersionsBaseController;
        private IAzureMonitorStatusLogger _azureMonitorStatusLogger;

        private readonly MockRepository _repository;

        private readonly string _id = "dummyId";
        private readonly BaseError _baseError;
        private readonly BaseException _baseExp;
        private readonly ServiceInvocationException _serviceInvocationExp;

        public AE365ModuleVersionsControllerTest()
        {
            _repository = new MockRepository(MockBehavior.Loose) { DefaultValue = DefaultValue.Mock, CallBase = true };
            _baseError = new InvalidArgumentWithDetail().ToBaseError(nameof(_id), nameof(_id), string.Format(AE3pErrorMessages.AE365ModuleUpdateWithDifferentId, _id, "dummyUpdateId"));
            _baseExp = new BaseException(_baseError);
            _serviceInvocationExp = new ServiceInvocationException(
                                    "dummyOperationName",
                                    "dummyCalledService",
                                    new HttpMethod("dummyHttpMethod"),
                                    new Exception("dummyExceptionMessage"));
        }

        [SetUp]
        public void Init()
        {
            _fakeIStoreCollection = _repository.Create<IStoreCollection>().Object;
            _fakeIContextProvider = _repository.Create<IContextProvider>().Object;
            _fakeIWorkspaceResourcesCache = _repository.Create<IWorkspaceResourcesCache>().Object;
            _azureMonitorStatusLogger = _repository.Create<IAzureMonitorStatusLogger>().Object;

            _fakeAE365ModuleVersionsController = _repository.Create<AE365ModuleVersionsController>(_fakeIStoreCollection, _fakeIContextProvider, _fakeIWorkspaceResourcesCache, _azureMonitorStatusLogger).Object;

            _mockAmlModuleVersionsBaseController = _repository.Create<AmlModuleVersionsBaseController>(_fakeIStoreCollection, _fakeIContextProvider, _fakeIWorkspaceResourcesCache, _azureMonitorStatusLogger);
            FieldInfo field = typeof(AE365ModuleVersionsController).GetField("_amlModuleVersionsBaseController", BindingFlags.Instance | BindingFlags.NonPublic);
            field.SetValue(_fakeAE365ModuleVersionsController, _mockAmlModuleVersionsBaseController.Object);

            _mockModulesBaseController = _repository.Create<ModulesBaseController>(_fakeIStoreCollection, _fakeIContextProvider, _fakeIWorkspaceResourcesCache, null, _azureMonitorStatusLogger);
            field = typeof(AE365ModuleVersionsController).GetField("_modulesBaseController", BindingFlags.Instance | BindingFlags.NonPublic);
            field.SetValue(_fakeAE365ModuleVersionsController, _mockModulesBaseController.Object);

            Mock.Get(_fakeAE365ModuleVersionsController)
                .Setup(controller => controller.GetWorkspaceId())
                .Returns(string.Empty);
        }

        [Test]
        public async Task CreateAE365ModuleVersionAsync_Test()
        {
            var guid = Guid.NewGuid().ToString();
            var invalidName = @"azureml://" + guid;
            AE365ModuleVersionCreationInfo creationInfo = new AE365ModuleVersionCreationInfo(name: invalidName,
                displayName: $"{invalidName} display",
                description: "mvDescription" + guid,
                isDeterministic: false,
                moduleExecutionType: "mvExecutionType",
                hash: "mvHash",
                identifierHash: "mvIdentifierHash",
                amlModuleId: null,
                version: "1.0",
                structuredInterface: new StructuredInterface { CommandLinePattern = "aaa" },
                moduleTypeVersion: "1.0");

            // test baseException when creationInfo name is not valid
            var exp = Assert.ThrowsAsync<BaseException>(async () => await _fakeAE365ModuleVersionsController.CreateAE365ModuleVersionAsync(subscriptionId: "subscriptionId",
               resourceGroupName: "resourceGroupName",
               workspaceName: "workspaceName",
               creationInfo: creationInfo).ConfigureAwait(false));
            Assert.AreEqual(@"Name is invalid. AE365 module version name can not have reserved prefix ""azureml://"".", exp.Message);

            // change Name to be correct format and setup activity
            creationInfo.Name = guid;
            var expectedAzureMLModuleVersion = new AzureMLModuleVersion();
            _mockAmlModuleVersionsBaseController
                .SetupSequence(controller => controller.CreateModuleVersionHelperAsync(
                        It.IsAny<IAmlModuleStore>(),
                        It.IsAny<IModuleStore>(),
                        It.IsAny<IDataTypeStore>(),
                        It.IsAny<WorkspaceIdentity>(),
                        It.IsAny<AE365ModuleVersionCreationInfo>(),
                        It.IsAny<CreatedBy>(),
                        It.IsAny<string>()))
                .Throws(_serviceInvocationExp)
                .Throws(new Exception("Error"))
                .Returns(Task.FromResult(expectedAzureMLModuleVersion));

            // test when ServiceInvocationException happen
            exp = Assert.ThrowsAsync<BaseException>(async () => await _fakeAE365ModuleVersionsController.CreateAE365ModuleVersionAsync(subscriptionId: "subscriptionId",
               resourceGroupName: "resourceGroupName",
               workspaceName: "workspaceName",
               creationInfo: creationInfo).ConfigureAwait(false));
            StringAssert.Contains(_serviceInvocationExp.Message, exp.Message);

            // test when normal exception happen
            exp = Assert.ThrowsAsync<BaseException>(async () => await _fakeAE365ModuleVersionsController.CreateAE365ModuleVersionAsync(subscriptionId: "subscriptionId",
               resourceGroupName: "resourceGroupName",
               workspaceName: "workspaceName",
               creationInfo: creationInfo).ConfigureAwait(false));
            StringAssert.Contains("Unexpected System Error occured when creating AE365ModuleVersion. The error message is: 'Error'.", exp.Message);

            // test when regular executed
            var actualReturn = await _fakeAE365ModuleVersionsController.CreateAE365ModuleVersionAsync(subscriptionId: "subscriptionId",
               resourceGroupName: "resourceGroupName",
               workspaceName: "workspaceName",
               creationInfo: creationInfo).ConfigureAwait(false);
            Assert.AreEqual(expectedAzureMLModuleVersion, actualReturn);
            Mock.Get(_fakeIStoreCollection).Verify(
                stores => stores.GetAmlModuleStore(
                    It.IsAny<ClaimsPrincipal>(),
                    It.IsAny<WorkspaceIdentity>()),
                Times.AtLeastOnce());
            Mock.Get(_fakeIStoreCollection).Verify(
                stores => stores.GetModuleStore(
                    It.IsAny<ClaimsPrincipal>(),
                    It.IsAny<WorkspaceIdentity>()),
                Times.AtLeastOnce());
            Mock.Get(_fakeIStoreCollection).Verify(
            stores => stores.GetDataTypeStore(
                It.IsAny<ClaimsPrincipal>(),
                It.IsAny<WorkspaceIdentity>()),
            Times.AtLeastOnce());
            Mock.Get(_fakeIContextProvider).Verify(
            provider => provider.GetCreatedByObject(),
            Times.AtLeastOnce());
        }

        [Test]
        public async Task UpdateAE365ModuleVersionAsync_Test()
        {
            var fakeAzureMLModuleVersion = new AzureMLModuleVersion()
            {
                Data = new ModuleEntity()
                {
                    Id = _id + "00"
                }
            };
            bool called_UpdateModuleAsync = false;

            // test when id not match
            Assert.AreNotEqual(_id, fakeAzureMLModuleVersion.Data.Id);

            var exp = Assert.ThrowsAsync<BaseException>(async () => await _fakeAE365ModuleVersionsController.UpdateAE365ModuleVersionAsync(
               subscriptionId: "subscriptionId",
               resourceGroupName: "resourceGroupName",
               workspaceName: "workspaceName",
               id: "dummyId",
               updated: fakeAzureMLModuleVersion).ConfigureAwait(false));
            Assert.False(called_UpdateModuleAsync);

            //  test regular execution
            fakeAzureMLModuleVersion.Data.Id = _id;
            var fakeIModuleStore = _repository.Create<IModuleStore>().Object;
            var fakeModuleEntity = _repository.Create<Microsoft.Aether.DataContracts.ModuleEntity>().Object;

            Mock.Get(_fakeIStoreCollection)
                .Setup(stores => stores.GetModuleStore(
                             It.IsAny<ClaimsPrincipal>(),
                             It.IsAny<WorkspaceIdentity>()))
                .Returns(fakeIModuleStore);

            _mockModulesBaseController
                .Setup(controller => controller.UpdateModuleAsync(
                    It.IsAny<Microsoft.Aether.DataContracts.ModuleEntity>(),
                    fakeIModuleStore))
                .Callback(() => called_UpdateModuleAsync = true)
                .Returns(Task.FromResult(fakeModuleEntity));

            var actualRet = await _fakeAE365ModuleVersionsController.UpdateAE365ModuleVersionAsync(
               subscriptionId: "subscriptionId",
               resourceGroupName: "resourceGroupName",
               workspaceName: "workspaceName",
               id: "dummyId",
               updated: fakeAzureMLModuleVersion).ConfigureAwait(false);

            Assert.True(actualRet is AzureMLModuleVersion);
            Assert.True(called_UpdateModuleAsync);

            // test when ServiceInvocationException throw
            called_UpdateModuleAsync = false;
            _mockModulesBaseController
                .Setup(controller => controller.UpdateModuleAsync(
                    It.IsAny<Microsoft.Aether.DataContracts.ModuleEntity>(),
                    fakeIModuleStore))
                .Callback(() => called_UpdateModuleAsync = true)
                .Throws(_serviceInvocationExp);

            exp = Assert.ThrowsAsync<BaseException>(async () => await _fakeAE365ModuleVersionsController.UpdateAE365ModuleVersionAsync(
               subscriptionId: "subscriptionId",
               resourceGroupName: "resourceGroupName",
               workspaceName: "workspaceName",
               id: "dummyId",
               updated: fakeAzureMLModuleVersion).ConfigureAwait(false));
            Assert.True(called_UpdateModuleAsync);
            StringAssert.Contains(_serviceInvocationExp.Message, exp.Message);

            // test when other Exception throw
            called_UpdateModuleAsync = false;
            _mockModulesBaseController
                .Setup(controller => controller.UpdateModuleAsync(
                    It.IsAny<Microsoft.Aether.DataContracts.ModuleEntity>(),
                    fakeIModuleStore))
                .Callback(() => called_UpdateModuleAsync = true)
                .Throws(new Exception("Error"));

            exp = Assert.ThrowsAsync<BaseException>(async () => await _fakeAE365ModuleVersionsController.UpdateAE365ModuleVersionAsync(
               subscriptionId: "subscriptionId",
               resourceGroupName: "resourceGroupName",
               workspaceName: "workspaceName",
               id: "dummyId",
               updated: fakeAzureMLModuleVersion).ConfigureAwait(false));
            Assert.True(called_UpdateModuleAsync);
            StringAssert.Contains("Unexpected System Error occured when updating AE365ModuleVersion. The error message is: 'Error'.", exp.Message);
        }

        [Test]
        public async Task GetAE365ModuleAsync_Test()
        {
            var fakeIModuleStore = _repository.Create<IModuleStore>().Object;

            //setup sequence of activities for GetModuleStre
            Mock.Get(_fakeIStoreCollection)
                .SetupSequence(stores => stores.GetModuleStore(
                             It.IsAny<ClaimsPrincipal>(),
                             It.IsAny<WorkspaceIdentity>()))
                .Throws(_baseExp)
                .Throws(_serviceInvocationExp)
                .Throws(new Exception("Error"))
                .Returns(fakeIModuleStore);

            DataContracts.ModuleEntity fakeModuleEntity = _repository.Of<DataContracts.ModuleEntity>()
                .Where(store =>
                store.StructuredInterface == new DataContracts.StructuredInterface())
                .First();

            Mock.Get(fakeIModuleStore)
                .Setup(stores => stores.GetEntityAsync(
                             It.IsAny<string>(),
                             It.IsAny<string>(),
                             It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(fakeModuleEntity));

            var fakeIDataTypeStore = _repository.Create<IDataTypeStore>().Object;
            Mock.Get(_fakeIStoreCollection)
                .Setup(stores => stores.GetDataTypeStore(
                             It.IsAny<ClaimsPrincipal>(),
                             It.IsAny<WorkspaceIdentity>()))
                .Returns(fakeIDataTypeStore);

            IEnumerable<DataContracts.DataTypeEntity> fakeDataTypeEntity = new List<DataContracts.DataTypeEntity>()
            {
                new DataContracts.DataTypeEntity()
                {
                    Id = _id
                }
            };
            Mock.Get(fakeIDataTypeStore)
                .Setup(stores => stores.ListAsync(
                             It.IsAny<string>(),
                             It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(fakeDataTypeEntity));

            // test when base exception throw out
            var exp = Assert.ThrowsAsync<BaseException>(async () => await _fakeAE365ModuleVersionsController.GetAE365ModuleAsync(
                                   subscriptionId: "subscriptionId",
                                   resourceGroupName: "resourceGroupName",
                                   workspaceName: "workspaceName",
                                   id: "dummyId").ConfigureAwait(false));
            Assert.AreEqual(exp.Message, _baseError.Message);

            // test when ServiceInvocationException throw out
            exp = Assert.ThrowsAsync<BaseException>(async () => await _fakeAE365ModuleVersionsController.GetAE365ModuleAsync(
                                   subscriptionId: "subscriptionId",
                                   resourceGroupName: "resourceGroupName",
                                   workspaceName: "workspaceName",
                                   id: "dummyId").ConfigureAwait(false));
            StringAssert.Contains(_serviceInvocationExp.Message, exp.Message);

            // test when ServiceInvocationException throw out
            exp = Assert.ThrowsAsync<BaseException>(async () => await _fakeAE365ModuleVersionsController.GetAE365ModuleAsync(
                                   subscriptionId: "subscriptionId",
                                   resourceGroupName: "resourceGroupName",
                                   workspaceName: "workspaceName",
                                   id: "dummyId").ConfigureAwait(false));
            StringAssert.Contains("Unexpected System Error occured when getting AE365ModuleVersion. The error message is: 'Error'.", exp.Message);

            // test regular execution
            var actualRet = await _fakeAE365ModuleVersionsController.GetAE365ModuleAsync(
                                   subscriptionId: "subscriptionId",
                                   resourceGroupName: "resourceGroupName",
                                   workspaceName: "workspaceName",
                                   id: "dummyId").ConfigureAwait(false);
            Assert.True(actualRet is AzureMLModuleVersion);
        }
    }
}
