﻿// <copyright file="DataTypesWorkspaceControllerTest.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Microsoft.Aether.BackendCommon;
using Microsoft.Aether.DataContracts;
using Microsoft.Aether.DataContracts.Entity;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Routing;
using Microsoft.MachineLearning.Common.WebApi.TeamAccounts;
using Moq;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Security.Claims;
using System.Threading.Tasks;

namespace Microsoft.Aether.AE3pService.Tests.Controllers
{
    [TestFixture]
    public class DataTypesWorkspaceControllerTest
    {
        private readonly MockRepository _repository;
        private IStoreCollection _stores;
        private IContextProvider _operationContextProvider;
        private IWorkspaceResourcesCache _workspaceResourcesCache;
        private DataTypesWorkspaceController _dataTypesWorkspaceController;
        private readonly string subId = Guid.NewGuid().ToString();
        private readonly string wsId = "wsId";
        private readonly string wsName = "wsName";
        private readonly string rgName = "rgName";
        private bool called_GetWorkspaceId = false;
        private bool called_GetDataTypeStore = false;
        private bool called_GetEntity = false;

        public DataTypesWorkspaceControllerTest()
        {
            _repository = new MockRepository(MockBehavior.Loose) { DefaultValue = DefaultValue.Mock, CallBase = true };
            _stores = _repository.Create<IStoreCollection>().Object;
            _operationContextProvider = _repository.Create<IContextProvider>().Object;
            _workspaceResourcesCache = _repository.Create<IWorkspaceResourcesCache>().Object;
            _dataTypesWorkspaceController = new DataTypesWorkspaceController(_stores, _operationContextProvider, _workspaceResourcesCache);

            var httpContext = new Mock<HttpContext>();
            var workspaceMetadata = new WorkspaceMetadata(Guid.Empty, null, null, Guid.Empty);
            httpContext.SetupGet(a => a.Items).Returns(new Dictionary<object, object>() { { typeof(WorkspaceMetadata), workspaceMetadata } });
            _dataTypesWorkspaceController.ControllerContext = new ControllerContext(new ActionContext(httpContext.Object, new RouteData(), new Microsoft.AspNetCore.Mvc.Controllers.ControllerActionDescriptor()));
        }

        [SetUp]
        public void Init()
        {
            Mock.Get(_workspaceResourcesCache)
                .Setup(controller => controller.GetWorkspaceIdAsync(
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<CreatedBy>()))
                .Returns(Task.FromResult(wsId))
                .Callback(() => called_GetWorkspaceId = true);

            var dataStore = _repository.Create<IDataTypeStore>().Object;
            Mock.Get(_stores)
                .Setup(controller => controller.GetDataTypeStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(dataStore)
                .Callback(() => called_GetDataTypeStore = true);

            Mock.Get(dataStore)
                .Setup(controller => controller.GetEntityAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(_repository.Create<DataTypeEntity>().Object))
                .Callback(() => called_GetEntity = true);
        }

        [Test]
        public async Task GetAllDataTypesAsync_ExpectNormalReturn()
        {
            called_GetWorkspaceId = false;
            called_GetDataTypeStore = false;
            await _dataTypesWorkspaceController.GetAllDataTypesAsync(subId, rgName, wsName).ConfigureAwait(false);
            Assert.False(called_GetWorkspaceId);
            Assert.True(called_GetDataTypeStore);
        }

        [Test]
        public async Task CreateNewDataTypeAsync_ExpectNormalReturn()
        {
            called_GetWorkspaceId = false;
            called_GetDataTypeStore = false;
            AEVA.DataContracts.DataTypeCreationInfo creationInfo = _repository.Create<AEVA.DataContracts.DataTypeCreationInfo>("id", "name", "description", false).Object;
            await _dataTypesWorkspaceController.CreateNewDataTypeAsync(subId, rgName, wsName, creationInfo).ConfigureAwait(false);
            Assert.False(called_GetWorkspaceId);
            Assert.True(called_GetDataTypeStore);
        }

        [Test]
        public async Task UpdateDataTypeAsync_ExpectNormalReturn()
        {
            called_GetWorkspaceId = false;
            called_GetDataTypeStore = false;
            AEVA.DataContracts.DataTypeEntity dataEntity = _repository.Create<AEVA.DataContracts.DataTypeEntity>().Object;
            dataEntity.Id = "id";
            await _dataTypesWorkspaceController.UpdateDataTypeAsync(subId, rgName, wsName, "id", dataEntity).ConfigureAwait(false);
            Assert.False(called_GetWorkspaceId);
            Assert.True(called_GetDataTypeStore);
            Assert.True(called_GetEntity);
        }
    }
}
