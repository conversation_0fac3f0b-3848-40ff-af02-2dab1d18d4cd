﻿// <copyright file="AmlModulesRepositoryControllerTest.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Security.Claims;
using System.Threading;
using System.Threading.Tasks;
using Common.WebApi.Exceptions;
using Microsoft.Aether.AEVA.DataContracts;
using Microsoft.Aether.BackendCommon;
using Microsoft.Aether.BackendCommon.AzureMonitor;
using Microsoft.Aether.BlueBox.RepositoryResourcesClient;
using Microsoft.Aether.BlueBox.RepositoryResourcesContracts;
using Microsoft.Aether.DataContracts;
using Microsoft.Aether.DataContracts.Entity;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Routing;
using Microsoft.MachineLearning.Common.Core.Exceptions;
using Moq;
using NUnit.Framework;

namespace Microsoft.Aether.AE3pService.Tests.Controllers
{
    public class AmlModulesRepositoryControllerTest
    {
        private Mock<IStoreCollection> _stores;
        private Mock<IContextProvider> _operationContextProvider;
        private Mock<IWorkspaceResourcesCache> _workspaceResourcesCache;
        private Mock<IRepositoryResourcesClient> _repositoryClient;

        private Mock<IModuleStore> _moduleStore;
        private Mock<IDataTypeStore> _dataTypeStore;
        private Mock<IAmlModuleStore> _amlModuleStore;
        private Mock<IAzureMonitorStatusLogger> _azureMonitorStatusEmitter;
        private AmlModulesRepositoryController _controller;
        private readonly string _repositoryId = "TestRepoId";

        [SetUp]
        public void SetUp()
        {
            _moduleStore = new Mock<IModuleStore>();
            IEnumerable<DataContracts.ModuleEntity> modules = new List<DataContracts.ModuleEntity>() { new DataContracts.ModuleEntity { Id = "TestVersionId", FamilyId = "TestFamilyId" } };
            _moduleStore.Setup(m => m.BulkGetIdsAsync(It.IsAny<string>(), It.IsAny<string[]>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(modules));

            _dataTypeStore = new Mock<IDataTypeStore>();
            IEnumerable<DataContracts.DataTypeEntity> types = new List<DataContracts.DataTypeEntity> { new DataContracts.DataTypeEntity { Id = "TestTypeId" } };
            _dataTypeStore.Setup(t => t.ListAsync(It.IsAny<string>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(types));

            _amlModuleStore = new Mock<IAmlModuleStore>();
            _azureMonitorStatusEmitter = new Mock<IAzureMonitorStatusLogger>();

            _amlModuleStore.Setup(a => a.CreateAsync(It.IsAny<string>(), It.IsAny<AmlModuleEntity>(), It.IsAny<WorkspaceIdentity>()))
                    .Returns(Task.FromResult(new AmlModuleEntity { Name = "fakeModule" }));
            _amlModuleStore.Setup(a => a.GetEntityAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(new AmlModuleEntity
                {
                    Name = @"azureml://fakeVersion",
                    Versions = new List<AmlModuleVersionDescriptor> { { new AmlModuleVersionDescriptor { Version = "TestVersion" } } },
                    Id = "TestId",
                }));
            _amlModuleStore.Setup(a => a.GetAmlModuleByNameAsync(It.IsAny<string>(), It.IsAny<string>()))
                .Returns(Task.FromResult(new AmlModuleEntity { Name = @"azureml://fakeVersion" }));

            IEnumerable<AmlModuleEntity> entityList = new List<AmlModuleEntity> {
                new AmlModuleEntity {
                    Name = "fakeModule",
                    Id = "TestFamilyId",
                    DefaultVersion = "*******",
                    Versions = new List<AmlModuleVersionDescriptor> { new AmlModuleVersionDescriptor { ModuleVersionId = "TestVersionId", Version = "*******" } },
                }
            };
            _amlModuleStore.Setup(a => a.ListEntityAsync(It.IsAny<string>(), It.IsAny<bool>(), It.IsAny<string>(), It.IsAny<int>(), It.IsAny<bool>(), It.IsAny<string>(), It.IsAny<bool>(), It.IsAny<string>(), It.IsAny<bool>()))
                .Returns(Task.FromResult(new Tuple<IEnumerable<AmlModuleEntity>, string>(entityList, "TestToken")));

            _amlModuleStore.Setup(m => m.UpdateAsync(It.IsAny<string>(), It.IsAny<AmlModuleEntity>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(new DataContracts.AmlModuleEntity { Name = "fakeModule" }));

            IEnumerable<AmlModuleEntity> entities = new List<AmlModuleEntity> {
                new AmlModuleEntity {Versions = new List<AmlModuleVersionDescriptor> { new AmlModuleVersionDescriptor { ModuleVersionId = "TestVersionId", Version = "*******" } }, Id = "TestFamilyId" }
            };
            _amlModuleStore.Setup(s => s.BulkGetAsync(It.IsAny<string>(), It.IsAny<string[]>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(entities));

            _stores = new Mock<IStoreCollection>();
            _stores.Setup(s => s.GetModuleStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(_moduleStore.Object);
            _stores.Setup(s => s.GetDataTypeStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(_dataTypeStore.Object);
            _stores.Setup(s => s.GetAmlModuleStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(_amlModuleStore.Object);

            _operationContextProvider = new Mock<IContextProvider>();
            _operationContextProvider.Setup(o => o.GetCreatedByObject())
                .Returns(new DataContracts.CreatedBy());

            _workspaceResourcesCache = new Mock<IWorkspaceResourcesCache>();

            _repositoryClient = new Mock<IRepositoryResourcesClient>();
            _repositoryClient.Setup(r => r.GetRepositoryByIdAsync(It.IsAny<string>(), It.IsAny<CancellationToken?>()))
                .Returns(Task.FromResult(
                        new RepositoryMetadata
                        {
                            SubscriptionId = Guid.NewGuid(),
                            RepositoryName = "TestRepositoryName",
                            ResourceGroupName = "TestResourceGroupName",
                            Id = Guid.NewGuid(),
                        }));

            _controller = new AmlModulesRepositoryController(_stores.Object, _operationContextProvider.Object, _workspaceResourcesCache.Object, _repositoryClient.Object, _azureMonitorStatusEmitter.Object);

            // Setup the HttpContext's Request/Response
            // The HeaderDictionary is needed for adding HTTP headers to the response.
            // This needs a couple of different Mocks, because many properties in the class model are read-only.
            var headerDictionary = new HeaderDictionary();
            headerDictionary.Add("ContinuationToken", "TestContinuationToken");
            headerDictionary.Add("BatchSize", "64");
            var request = new Mock<HttpRequest>();
            request.SetupGet(r => r.Headers).Returns(headerDictionary);
            var response = new Mock<HttpResponse>();
            response.SetupGet(r => r.Headers).Returns(headerDictionary);

            var httpContext = new Mock<HttpContext>();
            httpContext.SetupGet(a => a.Request).Returns(request.Object);
            httpContext.SetupGet(a => a.Response).Returns(response.Object);

            _controller.ControllerContext = new ControllerContext(new ActionContext(httpContext.Object, new RouteData(), new Microsoft.AspNetCore.Mvc.Controllers.ControllerActionDescriptor()));
        }

        [Test]
        public async Task TestCreateAzureMLModuleAsync_NormalCase_ExpectedAzureMLModule()
        {
            var creationInfo = new AzureMLModuleCreationInfo("fakeModule", "fakeDescription", null, null, null);
            var result = await _controller.CreateAzureMLModuleAsync(_repositoryId, creationInfo).ConfigureAwait(false);
            Assert.AreEqual("fakeModule", result.Name);
        }

        [Test]
        public async Task TestUpdateAzureMLModuleAsync_NormalCase_ExpectedAzureMLModule()
        {
            var creationInfo = new AzureMLModule { Name = "fakeModule", Id = "testId" };
            var result = await _controller.UpdateAzureMLModuleAsync(_repositoryId, "testId", creationInfo).ConfigureAwait(false);
            Assert.AreEqual("fakeModule", result.Name);
        }

        [Test]
        public async Task TestGetAzureMLModulesAsync_NormalCase_ExpectedAzureMLModuleList()
        {
            var result = await _controller.GetAzureMLModulesAsync(_repositoryId, false).ConfigureAwait(false);
            Assert.AreEqual(1, result.Count());
            Assert.AreEqual("fakeModule", result.ToList()[0].Name);
        }

        [Test]
        public async Task TestGetAzureMLModuleAsync_NormalCase_ExpectedAzureMLModule()
        {
            var result = await _controller.GetAzureMLModuleAsync(_repositoryId, "testId").ConfigureAwait(false);
            Assert.AreEqual(@"azureml://fakeVersion", result.Name);
        }

        [Test]
        public async Task TestGetAzureMLModuleByNameAsync_NormalCase_ExpectedAzureMLModule()
        {
            var result = await _controller.GetAzureMLModuleByNameAsync(_repositoryId, "testName").ConfigureAwait(false);
            Assert.AreEqual(@"azureml://fakeVersion", result.Name);
        }

        [Test]
        public void TestGetResolveAzureMLModuleVersionAsync_AmlModuleStoreThrows_ExpectedBaseException()
        {
            _stores.Setup(s => s.GetAmlModuleStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Throws(new ArgumentException());

            Assert.ThrowsAsync<BaseException>(async () => await _controller.GetResolveAzureMLModuleVersionAsync(_repositoryId, "testId").ConfigureAwait(false));
        }

        [Test]
        public async Task TestListResolveAzureMLModuleVersionAsync_NormalCase_ExpectedListOfVersions()
        {
            var result = await _controller.ListResolveAzureMLModuleVersionAsync(_repositoryId, true).ConfigureAwait(false);
            Assert.AreEqual("TestVersionId", result.ToList()[0].Data.Id);
        }

        [Test]
        public async Task TestListAzureMLModuleVersionsAsync_NormalCase_ExpectedListOfVersions()
        {
            var result = await _controller.ListAzureMLModuleWithDefaultVersionAsync(_repositoryId, true, GetAmlModuleVersionsType.Default, true, "Version DESC", string.Empty).ConfigureAwait(false);
            Assert.AreEqual("TestFamilyId", result.ToList()[0].Item1.Id);
            Assert.AreEqual(1, result.ToList()[0].Item2.Count());
        }

        [Test]
        public void TestGetBulkResolveAzureMLModuleVersionAsync_AmlModuleStoreThrows_ExpectedArgumentException()
        {
            _stores.Setup(s => s.GetAmlModuleStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Throws(new ArgumentException());
            string[] ids = { "TestId" };
            Assert.ThrowsAsync<ArgumentException>(async () => await _controller.GetBulkResolveAzureMLModuleVersionAsync(_repositoryId, ids).ConfigureAwait(false));
        }

        [Test]
        public void TestGetRepository_FailedToGetRepositoryMetaData_ExpectedBaseException()
        {
            _repositoryClient.Setup(r => r.GetRepositoryByIdAsync(It.IsAny<string>(), It.IsAny<CancellationToken?>()))
                .Throws(new ResourceNotFoundException());

            var method_GetRepository = typeof(AmlModulesRepositoryController).GetMethod("GetRepository", BindingFlags.Instance | BindingFlags.NonPublic);
            var task = (Task<RepositoryIdentity>)method_GetRepository.Invoke(_controller, new object[] { _repositoryId });
            var exp = Assert.ThrowsAsync<BaseException>(async () => await task.ConfigureAwait(false));
            Assert.IsInstanceOf<ResourceNotFoundException>(exp.GetBaseException());
        }
    }
}
