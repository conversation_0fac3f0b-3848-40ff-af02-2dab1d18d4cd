﻿// <copyright file="AmlModuleBaseControllerExceptionTest.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Net.Http;
using System.Security.Claims;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Aether.AEVA.DataContracts;
using Microsoft.Aether.BackendCommon;
using Microsoft.Aether.BackendCommon.AzureMonitor;
using Microsoft.Aether.BlueBox.RepositoryResourcesClient;
using Microsoft.Aether.BlueBox.RepositoryResourcesContracts;
using Microsoft.Aether.DataContracts;
using Microsoft.Aether.DataContracts.Entity;
using Microsoft.MachineLearning.Common.Core.Exceptions;
using Microsoft.RelInfra.Common.Exceptions;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;

namespace Microsoft.Aether.AE3pService.Tests
{
    [TestClass]
    public class AmlModuleBaseControllerExceptionTest
    {
        public Mock<IContextProvider> _contextProvider;
        public Mock<IStoreCollection> _storeCollection;
        public Mock<IWorkspaceResourcesCache> _workspaceResourceCache;
        public Mock<IRepositoryResourcesClient> _client;
        public AmlModulesRepositoryController _controller;
        public AzureMLModuleCreationInfo _amlModuleCreationInfo;
        public ServiceInvocationException _serviceInvocationException;
        public Mock<IAmlModuleStore> _modulestoreMock;
        private Mock<IAzureMonitorStatusLogger> _azureMonitorStatusEmitter;

        [TestInitialize]
        public void Init()
        {
            _contextProvider = new Mock<IContextProvider>();
            Mock<DataContracts.CreatedBy> createdBy = new Mock<DataContracts.CreatedBy>();
            _contextProvider.Setup(r => r.GetCreatedByObject()).Returns(createdBy.Object);
            _storeCollection = new Mock<IStoreCollection>();
            _workspaceResourceCache = new Mock<IWorkspaceResourcesCache>();
            _client = new Mock<IRepositoryResourcesClient>();
            _azureMonitorStatusEmitter = new Mock<IAzureMonitorStatusLogger>();
            RepositoryMetadata metadata = new RepositoryMetadata();
            _client.Setup(r => r.GetRepositoryByIdAsync(It.IsAny<string>(), It.IsAny<CancellationToken?>())).Returns(Task.FromResult(metadata));
            _controller = new AmlModulesRepositoryController(_storeCollection.Object, _contextProvider.Object, _workspaceResourceCache.Object, _client.Object, _azureMonitorStatusEmitter.Object);
            _serviceInvocationException = new ServiceInvocationException(
                    operationName: "MockException",
                    calledService: "Mock",
                    httpMethod: HttpMethod.Post,
                    innerException: new Exception("Mock throw ServiceInvocationException."));
            _modulestoreMock = new Mock<IAmlModuleStore>();
        }

        [TestMethod]
        public async Task TestCreateAzureMLModuleWithInvalidName()
        {
            try
            {
                var amlModuleCreationInfoWithInvlidadPrefixName = new AzureMLModuleCreationInfo
                    (
                        "azureml://name",
                        "description");
                AzureMLModule amlmodule = await _controller.CreateAzureMLModuleAsync(repositoryId: "repositoryId", creationInfo: amlModuleCreationInfoWithInvlidadPrefixName);
            }
            catch (Exception e)
            {
                Assert.IsTrue(e is BaseException);
                Assert.AreEqual("Name is invalid. azureml module name can not have reserved prefix \"azureml://\"!", e.Message);
            }
        }

        [TestMethod]
        public async Task TestCreateAzureMLModuleWithException()
        {
            try
            {
                var amlModuleCreationInfoWithInvalidPrefixName = new AzureMLModuleCreationInfo
                    (
                        "azuremlmodulename",
                        "description");
                var dummyException = new Exception("dummy exception");
                _storeCollection.Setup(s => s.GetAmlModuleStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>())).Throws(dummyException);
                AzureMLModule amlmodule = await _controller.CreateAzureMLModuleAsync(repositoryId: "repositoryId", creationInfo: amlModuleCreationInfoWithInvalidPrefixName);
            }
            catch (Exception e)
            {
                Assert.IsTrue(e is BaseException);
                Assert.AreEqual("Unexpected System Error occured when creating AzureMLModule internal. The error message is: 'dummy exception'.", e.Message);
            }
        }

        [TestMethod]
        public async Task TestUpdateAzureMLModuleWithInvalidId()
        {
            try
            {
                var amlModuleUpdateInfo = new AzureMLModule
                    (
                        "azuremlmodulename",
                        "description");
                amlModuleUpdateInfo.Id = "updateId";
                var moduleId = "dummyId";
                AzureMLModule amlmodule = await _controller.UpdateAzureMLModuleAsync(repositoryId: "repositoryId", id: moduleId, updated: amlModuleUpdateInfo);
            }
            catch (Exception e)
            {
                Assert.IsTrue(e is BaseException);
                Assert.AreEqual("id is invalid. ModuleId should be equal to updated.Id. moduleId is \"dummyId\", and updated.Id is \"updateId\".", e.Message);
            }
        }

        [TestMethod]
        public async Task TestUpdateAzureMLModuleWithInvalidName()
        {
            try
            {
                var amlModuleUpdateInfoWithInvalidPrefixName = new AzureMLModule
                    (
                        "azureml://name",
                        "description");
                var moduleId = "dummyId";
                amlModuleUpdateInfoWithInvalidPrefixName.Id = moduleId;
                AzureMLModule amlmodule = await _controller.UpdateAzureMLModuleAsync(repositoryId: "repositoryId", id: moduleId, updated: amlModuleUpdateInfoWithInvalidPrefixName);
            }
            catch (Exception e)
            {
                Assert.IsTrue(e is BaseException);
                Assert.AreEqual("Name is invalid. azureml module name cannot use builtin prefix \"azureml://\" when updating!", e.Message);
            }
        }

        [TestMethod]
        public async Task TestUpdateAzureMLModuleException()
        {
            try
            {
                var amlModuleUpdateInfo = new AzureMLModule
                    (
                        "azuremlmodulename",
                        "description");
                var moduleId = "dummyId";
                amlModuleUpdateInfo.Id = moduleId;
                var dummyException = new Exception("dummy exception");
                _storeCollection.Setup(s => s.GetAmlModuleStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>())).Throws(dummyException);
                AzureMLModule amlmodule = await _controller.UpdateAzureMLModuleAsync(repositoryId: "repositoryId", id: moduleId, updated: amlModuleUpdateInfo);
            }
            catch (Exception e)
            {
                Assert.IsTrue(e is BaseException);
                Assert.AreEqual("Unexpected System Error occured when updating AzureMLModule. The error message is: 'dummy exception'.", e.Message);
            }
        }

        [TestMethod]
        public async Task TestGetAzureMLModuleException()
        {
            try
            {
                var moduleId = "dummyId";
                var dummyException = new Exception("dummy exception");
                _storeCollection.Setup(s => s.GetAmlModuleStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>())).Throws(dummyException);
                AzureMLModule amlmodule = await _controller.GetAzureMLModuleAsync(repositoryId: "repositoryId", id: moduleId);
            }
            catch (Exception e)
            {
                Assert.IsTrue(e is BaseException);
                Assert.AreEqual("Unexpected System Error occured when getting AzureMLModule. The error message is: 'dummy exception'.", e.Message);
            }
        }

        [TestMethod]
        public async Task TestGetAzureMLModuleByNameException()
        {
            try
            {
                var amlModuleName = "dummyName";
                var dummyException = new Exception("dummy exception");
                _storeCollection.Setup(s => s.GetAmlModuleStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>())).Throws(dummyException);

                AzureMLModule amlmodule = await _controller.GetAzureMLModuleByNameAsync(repositoryId: "repositoryId", name: amlModuleName);
            }
            catch (Exception e)
            {
                Assert.IsTrue(e is BaseException);
                Assert.AreEqual("Unexpected System Error occured when getting AzureMLModule version by name. The error message is: 'dummy exception'.", e.Message);
            }
        }
    }
}
