﻿// <copyright file="PipelineDraftsControllerTest.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Net.Sockets;
using System.Security.Claims;
using System.Threading.Tasks;
using Microsoft.Aether.BackendCommon;
using Microsoft.Aether.BackendCommon.AzureMonitor;
using Microsoft.Aether.BlueBox.WorkspaceResourcesClient;
using Microsoft.Aether.DataContracts;
using Microsoft.Aether.DataContracts.Entity;
using Microsoft.Aether.S2S.Common;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.Options;
using Microsoft.MachineLearning.Common.WebApi.Client;
using Microsoft.MachineLearning.Common.WebApi.Exceptions;
using Microsoft.MachineLearning.Common.WebApi.TeamAccounts;
using Moq;
using NUnit.Framework;

namespace Microsoft.Aether.AE3pService.Tests.Controllers
{
    [TestFixture]
    public class PipelineDraftsControllerTest
    {
        private readonly MockRepository _repository;
        private PipelineDraftsController _pipelinesDraftController;
        private IStoreCollection _stores;
        private IContextProvider _operationContextProvider;
        private IWorkspaceResourcesCache _workspaceResourcesCache;
        private IAzureMonitorStatusLogger _azureMonitorStatusEmitter;
        private IServiceInvokerFactory _serviceInvokeFactory;
        private IS2STokenProvider _s2STokenProvider;
        private IOptionsMonitor<ExperimentationHostConfiguration> _config;
        private IWorkspaceResourcesClient _workspaceResourceClient;
        private string _subscriptionId;
        private string _rgName;
        private string _wsName;

        public PipelineDraftsControllerTest()
        {
            _repository = new MockRepository(MockBehavior.Loose) { DefaultValue = DefaultValue.Mock, CallBase = true };
        }

        [SetUp]
        public void Init()
        {
            _stores = _repository.Create<IStoreCollection>().Object;
            _operationContextProvider = _repository.Create<IContextProvider>().Object;
            _workspaceResourcesCache = _repository.Create<IWorkspaceResourcesCache>().Object;
            _azureMonitorStatusEmitter = _repository.Create<IAzureMonitorStatusLogger>().Object;
            _serviceInvokeFactory = _repository.Create<IServiceInvokerFactory>().Object;
            _s2STokenProvider = _repository.Create<IS2STokenProvider>().Object;
            _config = _repository.Create<IOptionsMonitor<ExperimentationHostConfiguration>>().Object;
            _workspaceResourceClient = _repository.Create<IWorkspaceResourcesClient>().Object;
            _pipelinesDraftController = new PipelineDraftsController(_config, _stores, _serviceInvokeFactory, _s2STokenProvider, _operationContextProvider, _workspaceResourceClient, _workspaceResourcesCache, _azureMonitorStatusEmitter);
            _subscriptionId = Guid.NewGuid().ToString();
            _rgName = "testRG";
            _wsName = "testWSName";

            var httpContext = new Mock<HttpContext>();
            var workspaceMetadata = new WorkspaceMetadata(Guid.Empty, null, null, Guid.Empty);
            httpContext.SetupGet(a => a.Items).Returns(new Dictionary<object, object>() { { typeof(WorkspaceMetadata), workspaceMetadata } });
            _pipelinesDraftController.ControllerContext = new ControllerContext(new ActionContext(httpContext.Object, new RouteData(), new Microsoft.AspNetCore.Mvc.Controllers.ControllerActionDescriptor()));
        }

        [Test]
        public void CreatePipelineDraftAsync_CatchBlock_ExpectExceptionThrown()
        {
            var pipelineDraft = _repository.Create<AEVA.DataContracts.PipelineDraft>().Object;

            Mock.Get(_operationContextProvider)
                .Setup(controller => controller.GetCreatedByObject())
                .Throws(new BadRequestException("message"));

            Assert.That(() => _pipelinesDraftController.CreatePipelineDraftAsync(_subscriptionId, _rgName, _wsName, pipelineDraft), Throws.TypeOf<BadRequestException>());
        }

        [Test]
        public async Task CreatePipelineDraftAsync_EmptyGraphDraftId_ExpectAzMonitorLogs()
        {
            var pipelineDraft = _repository.Create<AEVA.DataContracts.PipelineDraft>().Object;
            IAmlGraphDraftStore graphDraftStore = _repository.Create<IAmlGraphDraftStore>().Object;

            bool called_GetAmlGraphDraftStore = false;
            Mock.Get(_stores)
                .Setup(controller => controller.GetAmlGraphDraftStore(
                    It.IsAny<ClaimsPrincipal>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Returns(graphDraftStore)
                .Callback(() => called_GetAmlGraphDraftStore = true);

            bool called_CreateAsync = false;
            var graphDraftIdentity = _repository.Create<AmlGraphDraftEntity>().Object;
            Mock.Get(graphDraftStore)
                .Setup(controller => controller.CreateAsync(
                    It.IsAny<string>(),
                    It.IsAny<AmlGraphDraftEntity>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(graphDraftIdentity))
                .Callback(() => called_CreateAsync = true);

            await _pipelinesDraftController.CreatePipelineDraftAsync(_subscriptionId, _rgName, _wsName, pipelineDraft).ConfigureAwait(false);
            Assert.True(called_GetAmlGraphDraftStore);
            Assert.True(called_CreateAsync);
        }

        [Test]
        public async Task CreatePipelineDraftAsync_EmptyGraphDraftId_ExpectNormalReturn()
        {
            var pipelineDraft = _repository.Create<AEVA.DataContracts.PipelineDraft>().Object;
            IAmlGraphDraftStore graphDraftStore = _repository.Create<IAmlGraphDraftStore>().Object;

            bool called_GetAmlGraphDraftStore = false;
            Mock.Get(_stores)
                .Setup(controller => controller.GetAmlGraphDraftStore(
                    It.IsAny<ClaimsPrincipal>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Returns(graphDraftStore)
                .Callback(() => called_GetAmlGraphDraftStore = true);

            bool called_CreateAsync = false;
            var graphDraftIdentity = _repository.Create<AmlGraphDraftEntity>().Object;
            Mock.Get(graphDraftStore)
                .Setup(controller => controller.CreateAsync(
                    It.IsAny<string>(),
                    It.IsAny<AmlGraphDraftEntity>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(graphDraftIdentity))
                .Callback(() => called_CreateAsync = true);

            await _pipelinesDraftController.CreatePipelineDraftAsync(_subscriptionId, _rgName, _wsName, pipelineDraft).ConfigureAwait(false);
            Assert.True(called_GetAmlGraphDraftStore);
            Assert.True(called_CreateAsync);
        }

        [Test]
        public async Task CreatePipelineDraftAsync_HasGraphId_ExpectNormalReturn()
        {
            var pipelineDraft = _repository.Create<AEVA.DataContracts.PipelineDraft>().Object;
            pipelineDraft.GraphDraftId = "testGraphDraftId";
            pipelineDraft.Name = "draftName";

            bool called_GetAmlGraphDraftStore = false;
            Mock.Get(_stores)
                .Setup(controller => controller.GetAmlGraphDraftStore(
                    It.IsAny<ClaimsPrincipal>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Callback(() => called_GetAmlGraphDraftStore = true);

            bool called_GetCreatedByObject = false;
            Mock.Get(_operationContextProvider)
                .Setup(controller => controller.GetCreatedByObject())
                .Callback(() => called_GetCreatedByObject = true);

            var output = await _pipelinesDraftController.CreatePipelineDraftAsync(_subscriptionId, _rgName, _wsName, pipelineDraft).ConfigureAwait(false);
            Assert.False(called_GetAmlGraphDraftStore);
            Assert.True(called_GetCreatedByObject);
        }

        [Test]
        public async Task GetPipelineDraftAsync_TryBlock_ExpectNormalReturn()
        {
            var pipelineDraftStore = _repository.Create<IPipelineDraftStore>().Object;

            bool called_GetPipelineDraftStore = false;
            Mock.Get(_stores)
                .Setup(controller => controller.GetPipelineDraftStore(
                    It.IsAny<ClaimsPrincipal>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Returns(pipelineDraftStore)
                .Callback(() => called_GetPipelineDraftStore = true);

            var pipelineDraft = _repository.Create<PipelineDraft>().Object;
            pipelineDraft.GraphDraftId = "testGraphDraftId";
            pipelineDraft.Name = "draftName";
            bool called_GetEntityAsync = false;
            Mock.Get(pipelineDraftStore)
                .Setup(controller => controller.GetEntityAsync(
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(pipelineDraft))
                .Callback(() => called_GetEntityAsync = true);

            await _pipelinesDraftController.GetPipelineDraftAsync(_subscriptionId, _rgName, _wsName, "testGraphDraftId").ConfigureAwait(false);
            Assert.True(called_GetPipelineDraftStore);
            Assert.True(called_GetEntityAsync);
        }

        [Test]
        public void GetPipelineDraftAsync_CatchBlock_ExpectExceptionThrown()
        {
            Mock.Get(_stores)
                .Setup(controller => controller.GetPipelineDraftStore(
                    It.IsAny<ClaimsPrincipal>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Throws(new ArgumentException("message"));

            Assert.That(() => _pipelinesDraftController.GetPipelineDraftAsync(_subscriptionId, _rgName, _wsName, "testGraphDraftId"), Throws.TypeOf<ArgumentException>());
        }

        [Test]
        public async Task GetDeepPipelineGraphDraftEntityAsync_NoExceptionThrown_ExpectNormalOutput()
        {
            var pipelineDraftStore = _repository.Create<IPipelineDraftStore>().Object;

            bool called_GetPipelineDraftStore = false;
            Mock.Get(_stores)
                .Setup(controller => controller.GetPipelineDraftStore(
                    It.IsAny<ClaimsPrincipal>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Returns(pipelineDraftStore)
                .Callback(() => called_GetPipelineDraftStore = true);

            var pipelineDraft = _repository.Create<PipelineDraft>().Object;
            pipelineDraft.GraphDraftId = "testGraphDraftId";
            pipelineDraft.Name = "draftName";
            bool called_GetEntityAsync = false;
            Mock.Get(pipelineDraftStore)
                .Setup(controller => controller.GetEntityAsync(
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(pipelineDraft))
                .Callback(() => called_GetEntityAsync = true);

            bool called_GetAmlGraphDraftStore = false;
            Mock.Get(_stores)
                .Setup(controller => controller.GetAmlGraphDraftStore(
                    It.IsAny<ClaimsPrincipal>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Callback(() => called_GetAmlGraphDraftStore = true);

            var output = await _pipelinesDraftController.GetDeepPipelineGraphDraftEntityAsync(_subscriptionId, _rgName, _wsName, "testGraphDraftId").ConfigureAwait(false);
            Assert.True(called_GetPipelineDraftStore);
            Assert.True(called_GetAmlGraphDraftStore);
            Assert.True(called_GetEntityAsync);
            Assert.AreEqual("testGraphDraftId", output.PipelineDraft.GraphDraftId);
        }

        [Test]
        public void GetDeepPipelineGraphDraftEntityAsync_CatchBlock_ExpectExceptionThrown()
        {
            Mock.Get(_stores)
               .Setup(controller => controller.GetPipelineDraftStore(
                   It.IsAny<ClaimsPrincipal>(),
                   It.IsAny<WorkspaceIdentity>()))
               .Throws(new SocketException());

            Assert.That(() => _pipelinesDraftController.GetDeepPipelineGraphDraftEntityAsync(_subscriptionId, _rgName, _wsName, "testGraphDraftId"), Throws.TypeOf<SocketException>());
        }

        [Test]
        public void ListPipelineDraftsAsync_CatchBlock_ExpectExceptionThrown()
        {
            var dictInput = new Dictionary<string, string>() { };
            Mock.Get(_stores)
               .Setup(controller => controller.GetPipelineDraftStore(
                   It.IsAny<ClaimsPrincipal>(),
                   It.IsAny<WorkspaceIdentity>()))
               .Throws(new SocketException());

            Assert.That(() => _pipelinesDraftController.ListPipelineDraftsAsync(_subscriptionId, _rgName, _wsName, dictInput), Throws.TypeOf<SocketException>());
        }

        [Test]
        public void SavePipelineDraftAsync_MismatchedGraphId_ExpectArgumentException()
        {
            var pipelineDraft = _repository.Create<AEVA.DataContracts.PipelineDraft>().Object;
            pipelineDraft.Id = "mismatchId";
            pipelineDraft.Name = "draftName";
            var pipelineDraftStore = _repository.Create<IPipelineDraftStore>().Object;

            Assert.That(() => _pipelinesDraftController.SavePipelineDraftAsync(_subscriptionId, _rgName, _wsName, "testGraphDraftId", pipelineDraft), Throws.TypeOf<ArgumentException>());
        }

        [Test]
        public void SavePipelineDraftAsync_CatchBlock_ExpectExceptionThrown()
        {
            var pipelineDraft = _repository.Create<AEVA.DataContracts.PipelineDraft>().Object;
            pipelineDraft.Id = "testGraphDraftId";
            pipelineDraft.Name = "draftName";
            Mock.Get(_stores)
                .Setup(controller => controller.GetPipelineDraftStore(
                    It.IsAny<ClaimsPrincipal>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Throws(new BadRequestException("message"));

            Assert.That(() => _pipelinesDraftController.SavePipelineDraftAsync(_subscriptionId, _rgName, _wsName, "testGraphDraftId", pipelineDraft), Throws.TypeOf<BadRequestException>());
        }

        [Test]
        public async Task DeletePipelineDraftAsync_TryBlock_ExpectNormalReturn()
        {
            var pipelineDraftStore = _repository.Create<IPipelineDraftStore>().Object;

            bool called_GetPipelineDraftStore = false;
            Mock.Get(_stores)
                .Setup(controller => controller.GetPipelineDraftStore(
                    It.IsAny<ClaimsPrincipal>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Returns(pipelineDraftStore)
                .Callback(() => called_GetPipelineDraftStore = true);

            var pipelineDraft = _repository.Create<AEVA.DataContracts.PipelineDraft>().Object;
            pipelineDraft.GraphDraftId = "testGraphDraftId";
            pipelineDraft.Name = "draftName";
            bool called_DeleteAsync = false;
            Mock.Get(pipelineDraftStore)
                .Setup(controller => controller.DeleteAsync(
                    It.IsAny<string>(),
                    It.IsAny<PipelineDraft>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Callback(() => called_DeleteAsync = true);

            await _pipelinesDraftController.DeletePipelineDraftAsync(_subscriptionId, _rgName, _wsName, "testGraphDraftId", pipelineDraft).ConfigureAwait(false);
            Assert.True(called_GetPipelineDraftStore);
            Assert.True(called_DeleteAsync);
        }

        [Test]
        public void DeletePipelineDraftAsync_CatchBlock_ExpectExceptionThrown()
        {
            var pipelineDraft = _repository.Create<AEVA.DataContracts.PipelineDraft>().Object;
            pipelineDraft.GraphDraftId = "testGraphDraftId";
            pipelineDraft.Name = "draftName";

            Mock.Get(_stores)
                .Setup(controller => controller.GetPipelineDraftStore(
                    It.IsAny<ClaimsPrincipal>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Throws(new ArgumentException("message"));

            Assert.That(() => _pipelinesDraftController.DeletePipelineDraftAsync(_subscriptionId, _rgName, _wsName, "testGraphDraftId", pipelineDraft), Throws.TypeOf<ArgumentException>());
        }

        [Test]
        public void CloneFromPipelineRunHelper_CatchBlock_ExpectExceptionThrown()
        {
            IGraphStore fakeGraphStore = _repository.Create<IGraphStore>().Object;

            bool called_GetPipelineDraftStore = false;
            Mock.Get(_stores)
                .Setup(controller => controller.GetGraphStore(
                    It.IsAny<ClaimsPrincipal>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Returns(fakeGraphStore)
                .Callback(() => called_GetPipelineDraftStore = true);

            Mock.Get(_stores)
               .Setup(controller => controller.GetAmlGraphDraftStore(
                   It.IsAny<ClaimsPrincipal>(),
                   It.IsAny<WorkspaceIdentity>()))
               .Throws(new SocketException());

            Assert.That(() => _pipelinesDraftController.CloneFromPipelineRunAsync(_subscriptionId, _rgName, _wsName, "testGraphDraftId"), Throws.TypeOf<SocketException>());
            Assert.True(called_GetPipelineDraftStore);
        }

        [Test]
        public async Task ClonePublishedHelper_TryBlock_ExpectNormalReturn()
        {
            IGraphStore fakeGraphStore = _repository.Create<IGraphStore>().Object;

            bool called_GetGraphStore = false;
            Mock.Get(_stores)
                .Setup(controller => controller.GetGraphStore(
                    It.IsAny<ClaimsPrincipal>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Returns(fakeGraphStore)
                .Callback(() => called_GetGraphStore = true);

            IPipelineStore pipelineStore = _repository.Create<IPipelineStore>().Object;
            bool called_GetPipelineStore = false;
            Mock.Get(_stores)
                .Setup(controller => controller.GetPipelineStore(
                    It.IsAny<ClaimsPrincipal>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Returns(pipelineStore)
                .Callback(() => called_GetPipelineStore = true);

            PipelineEntity pipelineEntity = _repository.Create<PipelineEntity>().Object;
            bool called_GetEntityAsync = false;
            Mock.Get(pipelineStore)
                .Setup(controller => controller.GetEntityAsync(
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(pipelineEntity))
                .Callback(() => called_GetEntityAsync = true);

            IAmlGraphDraftStore graphDraftStore = _repository.Create<IAmlGraphDraftStore>().Object;
            bool called_GetAmlGraphDraftStore = false;
            Mock.Get(_stores)
                .Setup(controller => controller.GetAmlGraphDraftStore(
                    It.IsAny<ClaimsPrincipal>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Returns(graphDraftStore)
                .Callback(() => called_GetAmlGraphDraftStore = true);

            bool called_CreateAsync = false;
            var graphDraftIdentity = _repository.Create<AmlGraphDraftEntity>().Object;
            Mock.Get(graphDraftStore)
                .Setup(controller => controller.CreateAsync(
                    It.IsAny<string>(),
                    It.IsAny<AmlGraphDraftEntity>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(graphDraftIdentity))
                .Callback(() => called_CreateAsync = true);

            var pipelineDraftStore = _repository.Create<IPipelineDraftStore>().Object;
            Mock.Get(_stores)
                .Setup(controller => controller.GetPipelineDraftStore(
                    It.IsAny<ClaimsPrincipal>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Returns(pipelineDraftStore);

            var pipelineDraft = _repository.Create<PipelineDraft>().Object;
            pipelineDraft.GraphDraftId = "graphDraftId";
            Mock.Get(pipelineDraftStore)
                .Setup(controller => controller.CreateAsync(
                    It.IsAny<string>(),
                    It.IsAny<PipelineDraft>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(pipelineDraft));

            var output = await _pipelinesDraftController.CloneFromPublishedPipelinePostAsync(_subscriptionId, _rgName, _wsName, "testGraphDraftId").ConfigureAwait(false);
            Assert.True(called_GetGraphStore);
            Assert.True(called_GetPipelineStore);
            Assert.True(called_GetEntityAsync);
            Assert.True(called_GetAmlGraphDraftStore);
            Assert.True(called_CreateAsync);
            Assert.AreEqual("graphDraftId", output.GraphDraftId);
        }

        [Test]
        public void ClonePublishedHelper_CatchBlock_ExpectExceptionThrown()
        {
            var pipelineDraft = _repository.Create<AEVA.DataContracts.PipelineDraft>().Object;
            pipelineDraft.GraphDraftId = "testGraphDraftId";
            pipelineDraft.Name = "draftName";

            Mock.Get(_stores)
                .Setup(controller => controller.GetGraphStore(
                    It.IsAny<ClaimsPrincipal>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Throws(new ArgumentException("message"));

            Assert.That(() => _pipelinesDraftController.CloneFromPublishedPipelineAsync(_subscriptionId, _rgName, _wsName, "testGraphDraftId"), Throws.TypeOf<ArgumentException>());
        }

        [Test]
        public async Task CloneFromPipelineDraftHelper_TryBlock_ExpectNormalReturn()
        {
            IPipelineDraftStore pipelineDraftStore = _repository.Create<IPipelineDraftStore>().Object;
            bool called_GetPipelineDraftStore = false;
            Mock.Get(_stores)
                .Setup(controller => controller.GetPipelineDraftStore(
                    It.IsAny<ClaimsPrincipal>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Returns(pipelineDraftStore)
                .Callback(() => called_GetPipelineDraftStore = true);

            PipelineDraft pipelineEntity = _repository.Create<PipelineDraft>().Object;
            bool called_GetEntityAsync = false;
            Mock.Get(pipelineDraftStore)
                .Setup(controller => controller.GetEntityAsync(
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(pipelineEntity))
                .Callback(() => called_GetEntityAsync = true);

            IAmlGraphDraftStore graphDraftStore = _repository.Create<IAmlGraphDraftStore>().Object;
            bool called_GetAmlGraphDraftStore = false;
            Mock.Get(_stores)
                .Setup(controller => controller.GetAmlGraphDraftStore(
                    It.IsAny<ClaimsPrincipal>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Returns(graphDraftStore)
                .Callback(() => called_GetAmlGraphDraftStore = true);

            bool called_CreateAsync = false;
            var graphDraftIdentity = _repository.Create<AmlGraphDraftEntity>().Object;
            Mock.Get(graphDraftStore)
                .Setup(controller => controller.CreateAsync(
                    It.IsAny<string>(),
                    It.IsAny<AmlGraphDraftEntity>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(graphDraftIdentity))
                .Callback(() => called_CreateAsync = true);

            var pipelineDraft = _repository.Create<PipelineDraft>().Object;
            pipelineDraft.GraphDraftId = "graphDraftId";
            Mock.Get(pipelineDraftStore)
                .Setup(controller => controller.CreateAsync(
                    It.IsAny<string>(),
                    It.IsAny<PipelineDraft>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(pipelineDraft));

            var output = await _pipelinesDraftController.CloneFromPipelineDraftAsync(_subscriptionId, _rgName, _wsName, "testGraphDraftId").ConfigureAwait(false);
            Assert.True(called_GetPipelineDraftStore);
            Assert.True(called_GetEntityAsync);
            Assert.True(called_GetAmlGraphDraftStore);
            Assert.True(called_CreateAsync);
        }

        [Test]
        public void CloneFromPipelineDraftHelper_CatchBlock_ExpectExceptionThrown()
        {
            var pipelineDraft = _repository.Create<AEVA.DataContracts.PipelineDraft>().Object;
            pipelineDraft.GraphDraftId = "testGraphDraftId";
            pipelineDraft.Name = "draftName";

            Mock.Get(_stores)
                .Setup(controller => controller.GetPipelineDraftStore(
                    It.IsAny<ClaimsPrincipal>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Throws(new ArgumentException("message"));

            Assert.That(() => _pipelinesDraftController.CloneFromPipelineDraftPostAsync(_subscriptionId, _rgName, _wsName, "testGraphDraftId"), Throws.TypeOf<ArgumentException>());
        }
    }
}
