﻿// <copyright file="AmlModuleVersionsRepositoryControllerTest.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Security.Claims;
using System.Threading;
using System.Threading.Tasks;
using Common.WebApi.Exceptions;
using Microsoft.Aether.AEVA.DataContracts;
using Microsoft.Aether.BackendCommon;
using Microsoft.Aether.BackendCommon.AzureMonitor;
using Microsoft.Aether.BlueBox.RepositoryResourcesClient;
using Microsoft.Aether.BlueBox.RepositoryResourcesContracts;
using Microsoft.Aether.DataContracts;
using Microsoft.Aether.DataContracts.Entity;
using Microsoft.MachineLearning.Common.Core.Exceptions;
using Moq;
using NUnit.Framework;

namespace Microsoft.Aether.AE3pService.Tests.Controllers
{
    [TestFixture]
    public class AmlModuleVersionsRepositoryControllerTest
    {
        private Mock<IStoreCollection> _stores;
        private Mock<IContextProvider> _operationContextProvider;
        private Mock<IWorkspaceResourcesCache> _workspaceResourcesCache;
        private Mock<IRepositoryResourcesClient> _repositoryClient;
        private Mock<IAzureMonitorStatusLogger> _azureMonitorStatusEmitter;
        private Mock<IModuleStore> _moduleStore;
        private Mock<IDataTypeStore> _dataTypeStore;
        private Mock<IAmlModuleStore> _amlModuleStore;
        private Mock<IGraphStore> _graphStore;
        private Mock<IAmlGraphDraftStore> _graphDraftStore;

        private AmlModuleVersionsRepositoryController _controller;
        private readonly string _repositoryId = "TestRepoId";

        [SetUp]
        public void SetUp()
        {
            _moduleStore = new Mock<IModuleStore>();
            IEnumerable<DataContracts.ModuleEntity> versions =
                new List<DataContracts.ModuleEntity> { new DataContracts.ModuleEntity { Name = "fakeVersion" } };
            _moduleStore.Setup(m => m.BulkGetIdsAsync(It.IsAny<string>(), It.IsAny<string[]>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(versions));
            _moduleStore.Setup(m => m.GetEntityAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(new DataContracts.ModuleEntity { Name = "fakeVersion" }));
            _moduleStore.Setup(m => m.TryGetEntityByHashAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(new DataContracts.ModuleEntity { Name = "fakeVersion" }));
            _moduleStore.Setup(m => m.CreateAsync(It.IsAny<string>(), It.IsAny<DataContracts.ModuleEntity>(), It.IsAny<WorkspaceIdentity>(), It.IsAny<DataContracts.CreatedBy>()))
                .Returns(
                    (string workspaceId, DataContracts.ModuleEntity moduleEntity, WorkspaceIdentity identity, DataContracts.CreatedBy createdBy) =>
                    {
                        if (moduleEntity.Name == "subgraph")
                        {
                            moduleEntity.Id = Guid.NewGuid().ToString();
                            return Task.FromResult(moduleEntity);
                        }
                        else
                        {
                            return Task.FromResult(new DataContracts.ModuleEntity { Name = "fakeVersion" });
                        }
                    });

            _moduleStore.Setup(m => m.UpdateAsync(It.IsAny<string>(), It.IsAny<DataContracts.ModuleEntity>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(new DataContracts.ModuleEntity { Name = "fakeVersion" }));

            _dataTypeStore = new Mock<IDataTypeStore>();
            _amlModuleStore = new Mock<IAmlModuleStore>();
            _amlModuleStore.Setup(a => a.GetEntityAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(new AmlModuleEntity { Name = @"azureml://fakeVersion" }));
            _amlModuleStore.Setup(a => a.TryGetEntityAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(new AmlModuleEntity { Name = @"azureml://fakeVersion" }));

            _graphStore = new Mock<IGraphStore>();
            _graphStore.Setup(g => g.CreateAsync(It.IsAny<string>(), It.IsAny<DataContracts.GraphEntity>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(
                    (string workspaceId, DataContracts.GraphEntity graphEntity, WorkspaceIdentity identity) =>
                    {
                        graphEntity.Id = Guid.NewGuid().ToString();
                        return Task.FromResult(graphEntity);
                    });

            _graphDraftStore = new Mock<IAmlGraphDraftStore>();
            _graphDraftStore.Setup(g => g.CreateAsync(It.IsAny<string>(), It.IsAny<AmlGraphDraftEntity>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(
                    (string workspaceId, AmlGraphDraftEntity graphDraftEntity, WorkspaceIdentity identity) =>
                    {
                        graphDraftEntity.Id = Guid.NewGuid().ToString();
                        return Task.FromResult(graphDraftEntity);
                    });

            _stores = new Mock<IStoreCollection>();
            _stores.Setup(s => s.GetModuleStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(_moduleStore.Object);
            _stores.Setup(s => s.GetDataTypeStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(_dataTypeStore.Object);
            _stores.Setup(s => s.GetAmlModuleStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(_amlModuleStore.Object);
            _stores.Setup(s => s.GetGraphStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(_graphStore.Object);
            _stores.Setup(s => s.GetAmlGraphDraftStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(_graphDraftStore.Object);

            _operationContextProvider = new Mock<IContextProvider>();
            _operationContextProvider.Setup(o => o.GetCreatedByObject())
                .Returns(new DataContracts.CreatedBy());

            _workspaceResourcesCache = new Mock<IWorkspaceResourcesCache>();

            _repositoryClient = new Mock<IRepositoryResourcesClient>();
            _repositoryClient.Setup(r => r.GetRepositoryByIdAsync(It.IsAny<string>(), It.IsAny<CancellationToken?>()))
                .Returns(Task.FromResult(
                        new RepositoryMetadata
                        {
                            SubscriptionId = Guid.NewGuid(),
                            RepositoryName = "TestRepositoryName",
                            ResourceGroupName = "TestResourceGroupName",
                            Id = Guid.NewGuid(),
                        }));
            _azureMonitorStatusEmitter = new Mock<IAzureMonitorStatusLogger>();
            _controller = new AmlModuleVersionsRepositoryController(_stores.Object, _operationContextProvider.Object, _workspaceResourcesCache.Object, _repositoryClient.Object, _azureMonitorStatusEmitter.Object);
        }

        [Test]
        public async Task TestCreateAzureMLModuleVersionAsync_NormalCase_ExpectedCorrectAzureMLModuleVersion()
        {
            var creationInfo = new AzureMLModuleVersionCreationInfo(@"fakeVersion", "test", "test", false, "test", "test", "test", "fakeAmlId", "test");
            var result = await _controller.CreateAzureMLModuleVersionAsync(_repositoryId, creationInfo).ConfigureAwait(false);
            Assert.AreEqual("fakeVersion", result.Data.Name);
        }

        [Test]
        public async Task TestUpdateAzureMLModuleVersionAsync_NormalCase_ExpectedCorrectAzureMLModuleVersion()
        {
            var creationInfo = new AzureMLModuleVersion { Data = new AEVA.DataContracts.ModuleEntity { Name = "fakeVersion", Id = "testId", EntityStatus = AEVA.DataContracts.EntityStatus.Active } };
            var result = await _controller.UpdateAzureMLModuleVersionAsync(_repositoryId, "testId", creationInfo).ConfigureAwait(false);
            Assert.AreEqual("fakeVersion", result.Data.Name);
        }

        [Test]
        public async Task TestGetAzureMLModuleVersionAsync_NormalCase_ExpectedCorrectAzureMLModuleVersion()
        {
            var result = await _controller.GetAzureMLModuleVersionAsync(_repositoryId, "testId").ConfigureAwait(false);
            Assert.AreEqual("fakeVersion", result.Data.Name);
        }

        [Test]
        public async Task TestGetAzureMLModuleVersionByHashAsync_NormalCase_ExpectedCorrectAzureMLModuleVersion()
        {
            var result = await _controller.GetAzureMLModuleVersionByHashAsync(_repositoryId, "testHash").ConfigureAwait(false);
            Assert.AreEqual("fakeVersion", result.Data.Name);
        }

        [Test]
        public async Task TestBulkGetAzureMLModuleVersionAsync_NormalCase_ExpectedCorrectAzureMLModuleVersionList()
        {
            string[] ids = { "testId1", "testId2" };
            var result = await _controller.BulkGetAzureMLModuleVersionAsync(_repositoryId, ids).ConfigureAwait(false);
            Assert.AreEqual(1, result.Count());
            Assert.AreEqual("fakeVersion", result.ToList()[0].Data.Name);
        }

        [Test]
        public void TestGetRepository_FailedToGetRepositoryMetaData_ExpectedBaseException()
        {
            _repositoryClient.Setup(r => r.GetRepositoryByIdAsync(It.IsAny<string>(), It.IsAny<CancellationToken?>()))
                .Throws(new ResourceNotFoundException());

            var method_GetRepository = typeof(AmlModuleVersionsRepositoryController).GetMethod("GetRepository", BindingFlags.Instance | BindingFlags.NonPublic);
            var task = (Task<RepositoryIdentity>)method_GetRepository.Invoke(_controller, new object[] { _repositoryId });
            var exp = Assert.ThrowsAsync<BaseException>(async () => await task.ConfigureAwait(false));
            Assert.IsInstanceOf<ResourceNotFoundException>(exp.GetBaseException());
        }

        [Test]
        public async Task CreateSubGraphModuleVersionAsync_NormalCase()
        {
            SubGraphVersionCreationInfo creationInfo = new SubGraphVersionCreationInfo()
            {
                Name = "subgraph",
                AmlModuleId = "fakeAmlId",
                VisualGraph = new AEVA.DataContracts.VisualGraph()
                {
                    Graph = new AEVA.DataContracts.GraphEntity()
                    {
                        ModuleNodes = new List<AEVA.DataContracts.GraphModuleNode>(),
                        Edges = new List<AEVA.DataContracts.GraphEdge>()
                    }
                },
                Interface = new AEVA.DataContracts.EntityInterface()
            };
            var result = await _controller.CreateSubGraphModuleVersionAsync(_repositoryId, creationInfo).ConfigureAwait(false);
            Assert.AreEqual("subgraph", result.Data.Name);
            Assert.IsNotNull(result.Data.CloudSettings.SubGraphConfig.GraphId);

            creationInfo.CreateAsDraft = true;
            result = await _controller.CreateSubGraphModuleVersionAsync(_repositoryId, creationInfo).ConfigureAwait(false);
            Assert.AreEqual("subgraph", result.Data.Name);
            Assert.IsNotNull(result.Data.CloudSettings.SubGraphConfig.GraphDraftId);
        }
    }
}
