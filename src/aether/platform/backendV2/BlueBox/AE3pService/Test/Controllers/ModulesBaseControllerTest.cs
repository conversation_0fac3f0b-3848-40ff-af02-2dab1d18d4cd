﻿// <copyright file="ModulesBaseControllerTest.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Reflection;
using System.Security.Claims;
using System.Threading;
using System.Threading.Tasks;
using FluentAssertions;
using Microsoft.Aether.AEVA.DataContractConverter;
using Microsoft.Aether.BackendCommon;
using Microsoft.Aether.BackendCommon.AzureMonitor;
using Microsoft.Aether.BlueBox.SnapshotClient;
using Microsoft.Aether.DataContracts;
using Microsoft.Aether.DataContracts.Entity;
using Microsoft.MachineLearning.Common.Core.Exceptions;
using Microsoft.MachineLearning.Project.Contracts;
using Moq;
using NUnit.Framework;
using AEVAArgumentAssignment = Microsoft.Aether.AEVA.DataContracts.ArgumentAssignment;
using AEVAModule = Microsoft.Aether.AEVA.DataContracts.Module;
using AEVAModuleCreationInfo = Microsoft.Aether.AEVA.DataContracts.ModuleCreationInfo;
using AEVAModuleEntity = Microsoft.Aether.AEVA.DataContracts.ModuleEntity;
using AEVAModuleType = Microsoft.Aether.AEVA.DataContracts.ModuleType;
using AEVAStructuredInterface = Microsoft.Aether.AEVA.DataContracts.StructuredInterface;

namespace Microsoft.Aether.AE3pService.Tests.Controllers
{
    public class ModulesBaseControllerTest
    {

        private readonly MockRepository _repository;

        private ModulesBaseController _fakeModulesBaseController;
        private IStoreCollection _fakeIStoreCollection;
        private IContextProvider _fakeIContextProvider;
        private IWorkspaceResourcesCache _fakeIWorkspaceResourcesCache;
        private ISnapshotClient _fakeISnapshotClient;
        private IDataTypeStore _fakeIDataTypeStore;
        private IModuleStore _fakeIModuleStore;
        private IAzureMonitorStatusLogger _azureMonitorStatusLogger;

        private AEVAModuleCreationInfo _creationInfo;
        private readonly string _guid;
        private string _invalidName;
        private string _studioBuiltinModulePrefix;
        private readonly WorkspaceIdentity _dummyWorkspaceIdentity;
        private SnapshotDto _dummySnapshotDto;
        private IEnumerable<DataContracts.DataTypeEntity> _dummyDataTypeEntities;
        private DataContracts.ModuleEntity _dummyModuleEntity;
        private DataContracts.ModuleEntity _dummyModuleEntity1;
        private IEnumerable<DataContracts.ModuleEntity> _dummyModuleEntities;

        public ModulesBaseControllerTest()
        {
            _repository = new MockRepository(MockBehavior.Loose) { DefaultValue = DefaultValue.Mock, CallBase = true };

            _guid = Guid.NewGuid().ToString();
            _dummyWorkspaceIdentity = new WorkspaceIdentity();
            _dummySnapshotDto = new SnapshotDto()
            {
                Root = new DirTreeNode(name: "dummyName", type: "dummyType", null, null)
                {
                    Children = new SortedList<string, DirTreeNode>()
                    {
                        {"driver", null}
                    },
                    Timestamp = DateTimeOffset.UtcNow
                }
            };
        }

        [SetUp]
        public void Init()
        {
            _fakeIStoreCollection = _repository.Create<IStoreCollection>().Object;
            _fakeIContextProvider = _repository.Create<IContextProvider>().Object;
            _fakeIWorkspaceResourcesCache = _repository.Create<IWorkspaceResourcesCache>().Object;
            _fakeISnapshotClient = _repository.Create<ISnapshotClient>().Object;
            var amlSnapshotConfig = new Mock<AmlSnapshotConfig>();
            Mock.Get(_fakeISnapshotClient).SetupGet(service => service.AmlSnapshotConfig).Returns(amlSnapshotConfig.Object);
            amlSnapshotConfig.SetupGet(service => service.EnableV2Api).Returns(true);
            _azureMonitorStatusLogger = _repository.Create<IAzureMonitorStatusLogger>().Object;

            _fakeModulesBaseController = _repository.Create<ModulesBaseController>(
                _fakeIStoreCollection,
                _fakeIContextProvider,
                _fakeIWorkspaceResourcesCache,
                _fakeISnapshotClient, _azureMonitorStatusLogger).Object;

            FieldInfo field_studioBuiltinModulePrefix = typeof(ModulesBaseController).BaseType.GetField("_studioBuiltinModulePrefix", BindingFlags.Static | BindingFlags.NonPublic);
            _studioBuiltinModulePrefix = (string)field_studioBuiltinModulePrefix.GetValue(_fakeModulesBaseController);
            _invalidName = _studioBuiltinModulePrefix + _guid;

            Mock.Get(_fakeISnapshotClient)
                .Setup(client => client.GetSnapshotMetadataAsync(
                        It.IsAny<string>(),
                        It.IsAny<WorkspaceIdentity>(),
                        It.IsAny<CreatedBy>(),
                        It.IsAny<CancellationToken>()))
                .Returns(Task.FromResult(_dummySnapshotDto));

            Mock.Get(_fakeISnapshotClient)
                .Setup(client => client.CreateSnapshotAsync(
                        It.IsAny<string>(),
                        It.IsAny<WorkspaceIdentity>(),
                        It.IsAny<MultipartFormDataContent>(),
                        It.IsAny<CreatedBy>(),
                        It.IsAny<CancellationToken>(),
                        It.IsAny<string>()))
                .Returns(Task.CompletedTask);

            var fileListDto = new FileNodeListDto()
            {
                FileNodes = new List<FileNodeDto>()
            };
            Mock.Get(_fakeISnapshotClient)
                .Setup(client => client.GetBlobUris(
                        It.IsAny<WorkspaceIdentity>(),
                        It.IsAny<FileNameListDto>(),
                        It.IsAny<CreatedBy>(),
                        It.IsAny<CancellationToken>()))
                .Returns(Task.FromResult(fileListDto));

            _fakeIDataTypeStore = _repository.Create<IDataTypeStore>().Object;
            Mock.Get(_fakeIStoreCollection)
                .Setup(
                   stores => stores.GetDataTypeStore(
                       It.IsAny<ClaimsPrincipal>(),
                       It.IsAny<WorkspaceIdentity>()))
                .Returns(_fakeIDataTypeStore);

            _dummyDataTypeEntities = new List<DataTypeEntity>()
            {
                new DataTypeEntity()
                {
                    Id = _guid
                }
            };
            Mock.Get(_fakeIDataTypeStore)
                .Setup(stores => stores.ListAsync(
                             It.IsAny<string>(),
                             It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(_dummyDataTypeEntities));

            _fakeIModuleStore = _repository.Create<IModuleStore>().Object;
            Mock.Get(_fakeIStoreCollection)
                .Setup(
                   stores => stores.GetModuleStore(
                       It.IsAny<ClaimsPrincipal>(),
                       It.IsAny<WorkspaceIdentity>()))
                .Returns(_fakeIModuleStore);

            _dummyModuleEntity = new DataContracts.ModuleEntity()
            {
                Id = _guid,
                Name = "dummyName",
                Description = "dummyDescription",
                UploadState = UploadState.Completed,
                CreatedBy = new CreatedBy()
                {
                    UserObjectId = "dummyUserObjectId"
                },
                Category = "dummyCategory"
            };
            Mock.Get(_fakeIModuleStore)
                .Setup(store => store.CreateAsync(
                       It.IsAny<string>(),
                       It.IsAny<ModuleEntity>(),
                       It.IsAny<WorkspaceIdentity>(),
                       It.IsAny<CreatedBy>()))
                .Returns(Task.FromResult(_dummyModuleEntity));

            Mock.Get(_fakeIModuleStore)
                .Setup(store => store.GetEntityAsync(
                       It.IsAny<string>(),
                       It.IsAny<string>(),
                       It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(_dummyModuleEntity));

            _dummyModuleEntities = new List<DataContracts.ModuleEntity>()
            {
                new DataContracts.ModuleEntity()
                {
                    Id = "Id1",
                    Name = "Name1"
                },
                new DataContracts.ModuleEntity()
                {
                    Id = "Id2",
                    Name = "Name2"
                }
            };
            Mock.Get(_fakeIModuleStore)
                .Setup(
                   stores => stores.BulkGetIdsAsync(
                      It.IsAny<string>(),
                      It.IsAny<string[]>(),
                      It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(_dummyModuleEntities));

            Mock.Get(_fakeIModuleStore)
                .Setup(
                   stores => stores.TryGetEntityByHashAsync(
                      It.IsAny<string>(),
                      It.IsAny<string>(),
                      It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(_dummyModuleEntity));

            _dummyModuleEntity1 = new DataContracts.ModuleEntity()
            {
                Id = _guid,
                Name = "dummyName1",
                Description = "dummyDescription1",
                UploadState = UploadState.Completed
            };
            Mock.Get(_fakeModulesBaseController)
                .Setup(
                   stores => stores.UpdateAsync<ModuleEntity>(
                      It.IsAny<IUpdatableEntityStore<ModuleEntity>>(),
                      It.IsAny<ModuleEntity>(),
                      It.IsAny<Type>(),
                      It.IsAny<bool>()))
                .Returns((IUpdatableEntityStore<ModuleEntity> store, ModuleEntity updatedEntity, Type aevaContract, bool allowEtagOverride)
                                => Task.FromResult(updatedEntity));
        }

        [Test]
        public async Task CreateModuleAsync_Tests()
        {
            MethodInfo method_CreateModuleAsync = typeof(ModulesBaseController).GetMethod("CreateModuleAsync", BindingFlags.Instance | BindingFlags.NonPublic);
            var storageId = Guid.NewGuid().ToString();
            // test ArgumentException throw when creationInfo name is not valid
            _creationInfo = new AEVAModuleCreationInfo(
                    name: _invalidName,     // use invalid name
                    displayName: _guid,
                    description: "mvDescription" + _guid,
                    isDeterministic: false,
                    moduleExecutionType: "mvExecutionType",
                    hash: "mvHash",
                    identifierHash: "mvIdentifierHash",
                    moduleType: AEVAModuleType.BatchInferencing,
                    storageId: storageId,
                    structuredInterface: new AEVAStructuredInterface()
                    {
                        Arguments = new List<AEVAArgumentAssignment>()
                    });

            var task = (Task<AEVAModuleEntity>)method_CreateModuleAsync.Invoke(_fakeModulesBaseController, new object[] { _dummyWorkspaceIdentity, _creationInfo });
            var exp = Assert.ThrowsAsync<BaseException>(async () => await task.ConfigureAwait(false));
            Assert.AreEqual($"Name is invalid. Module name can not have reserved prefix \"{_studioBuiltinModulePrefix}\"!", exp.Message);

            // test regular execution
            _creationInfo = new AEVAModuleCreationInfo(
                    name: _guid,     // use normal name
                    displayName: _guid,
                    description: "mvDescription" + _guid,
                    isDeterministic: false,
                    moduleExecutionType: "mvExecutionType",
                    hash: "mvHash",
                    identifierHash: "mvIdentifierHash",
                    moduleType: AEVAModuleType.BatchInferencing,
                    storageId: storageId,
                    structuredInterface: new AEVAStructuredInterface()
                    {
                        Arguments = new List<AEVAArgumentAssignment>()
                    });

            task = (Task<AEVAModuleEntity>)method_CreateModuleAsync.Invoke(_fakeModulesBaseController, new object[] { _dummyWorkspaceIdentity, _creationInfo });
            var actualRet = await task.ConfigureAwait(false);

            actualRet.Should().BeEquivalentTo(ContractConverter.Convert<ModuleEntity, AEVAModuleEntity>(_dummyModuleEntity));

            // test Exception throws when create module
            var dummyException = new Exception("dummy exception");
            Mock.Get(_fakeIModuleStore)
                .Setup(store => store.CreateAsync(
                       It.IsAny<string>(),
                       It.IsAny<ModuleEntity>(),
                       It.IsAny<WorkspaceIdentity>(),
                       It.IsAny<CreatedBy>()))
                .Throws(dummyException);
            task = (Task<AEVAModuleEntity>)method_CreateModuleAsync.Invoke(_fakeModulesBaseController, new object[] { _dummyWorkspaceIdentity, _creationInfo });
            var exp1 = Assert.ThrowsAsync<BaseException>(async () => await task.ConfigureAwait(false));
            StringAssert.Contains(dummyException.Message, exp1.Message);
        }

        [Test]
        public async Task UpdateModuleAsync_Tests()
        {
            MethodInfo method_UpdateModuleAsync = typeof(ModulesBaseController).GetMethod("UpdateModuleAsync", BindingFlags.Instance | BindingFlags.NonPublic);

            Mock.Get(_fakeModulesBaseController)
                .Setup(controller => controller.UpdateModuleAsync(
                       It.IsAny<ModuleEntity>(),
                       It.IsAny<IModuleStore>()))
                .Returns(Task.FromResult(_dummyModuleEntity));

            string dummyId = Guid.NewGuid().ToString();

            // test ArgumentException throw when creationInfo name is not valid
            var dummyAEVAModuleEntity = new AEVAModuleEntity()
            {
                Name = _invalidName,     // use invalid name
                Id = dummyId
            };

            Assert.AreNotEqual(dummyAEVAModuleEntity.Id, _guid);
            var task = (Task<AEVAModuleEntity>)method_UpdateModuleAsync.Invoke(_fakeModulesBaseController, new object[] { _dummyWorkspaceIdentity, _guid, dummyAEVAModuleEntity });
            var exp = Assert.ThrowsAsync<BaseException>(async () => await task.ConfigureAwait(false));
            Assert.AreEqual($"Name is invalid. Module name cannot use builtin prefix \"{_studioBuiltinModulePrefix}\" when updating!", exp.Message);

            // test ArgumentException throw when param id != creationInfo.id
            dummyAEVAModuleEntity = new AEVAModuleEntity()
            {
                Name = _guid,     // use invalid name
                Id = dummyId
            };

            Assert.AreNotEqual(dummyAEVAModuleEntity.Id, _guid);
            task = (Task<AEVAModuleEntity>)method_UpdateModuleAsync.Invoke(_fakeModulesBaseController, new object[] { _dummyWorkspaceIdentity, _guid, dummyAEVAModuleEntity });
            exp = Assert.ThrowsAsync<BaseException>(async () => await task.ConfigureAwait(false));
            StringAssert.Contains("ModuleId should be equal to updated.Id", exp.Message);

            // test normal execution
            dummyAEVAModuleEntity = new AEVAModuleEntity()
            {
                Name = _guid,     // use invalid name
                Id = _guid
            };
            Assert.AreEqual(dummyAEVAModuleEntity.Id, _guid);
            task = (Task<AEVAModuleEntity>)method_UpdateModuleAsync.Invoke(_fakeModulesBaseController, new object[] { _dummyWorkspaceIdentity, _guid, dummyAEVAModuleEntity });
            var actualRet = await task.ConfigureAwait(false);
            actualRet.Should().BeEquivalentTo(ContractConverter.Convert<ModuleEntity, AEVAModuleEntity>(_dummyModuleEntity));

            // test exception when update module
            var dummyException = new Exception("dummy exception");
            Mock.Get(_fakeModulesBaseController)
                .Setup(controller => controller.UpdateModuleAsync(
                       It.IsAny<ModuleEntity>(),
                       It.IsAny<IModuleStore>()))
                .Throws(dummyException);

            task = (Task<AEVAModuleEntity>)method_UpdateModuleAsync.Invoke(_fakeModulesBaseController, new object[] { _dummyWorkspaceIdentity, _guid, dummyAEVAModuleEntity });
            var exp1 = Assert.ThrowsAsync<BaseException>(async () => await task.ConfigureAwait(false));
            StringAssert.Contains(dummyException.Message, exp1.Message);
        }

        [Test]
        public async Task GetModuleAsync_Tests()
        {
            MethodInfo method_GetModuleAsync = typeof(ModulesBaseController).GetMethod("GetModuleAsync", BindingFlags.Instance | BindingFlags.NonPublic);

            // test normal execution
            var task = (Task<AEVAModule>)method_GetModuleAsync.Invoke(_fakeModulesBaseController, new object[] { _dummyWorkspaceIdentity, _guid });
            var actualRet = await task.ConfigureAwait(false);
            actualRet.Should().BeEquivalentTo(ModulesBaseController.BuildModule<AEVAModule>(_dummyModuleEntity, new HashSet<string>() { _guid }));

            // Test exception when get module entity
            var dummyException = new Exception("dummy exception");
            Mock.Get(_fakeIModuleStore)
                .Setup(store => store.GetEntityAsync(
                       It.IsAny<string>(),
                       It.IsAny<string>(),
                       It.IsAny<WorkspaceIdentity>()))
                .Throws(dummyException);

            task = (Task<AEVAModule>)method_GetModuleAsync.Invoke(_fakeModulesBaseController, new object[] { _dummyWorkspaceIdentity, _guid });
            var exp1 = Assert.ThrowsAsync<BaseException>(async () => await task.ConfigureAwait(false));
            StringAssert.Contains(dummyException.Message, exp1.Message);
        }

        [Test]
        public async Task GetModuleV2Async_Tests()
        {
            // test without RunConfiguration in entity
            MethodInfo method_GetModuleV2Async = typeof(ModulesBaseController).GetMethod("GetModuleV2Async", BindingFlags.Instance | BindingFlags.NonPublic);
            var task = (Task<AEVAModule>)method_GetModuleV2Async.Invoke(_fakeModulesBaseController, new object[] { _dummyWorkspaceIdentity, _guid });
            var actualRet = await task.ConfigureAwait(false);
            actualRet.Should().BeEquivalentTo(ModulesBaseController.BuildModule<AEVAModule>(_dummyModuleEntity, new HashSet<string>() { _guid }));

            // test exception
            var dummyException = new Exception("dummy exception");
            Mock.Get(_fakeIModuleStore)
               .Setup(store => store.GetEntityAsync(
                      It.IsAny<string>(),
                      It.IsAny<string>(),
                      It.IsAny<WorkspaceIdentity>()))
               .Throws(dummyException);

            task = (Task<AEVAModule>)method_GetModuleV2Async.Invoke(_fakeModulesBaseController, new object[] { _dummyWorkspaceIdentity, _guid });
            var exp1 = Assert.ThrowsAsync<BaseException>(async () => await task.ConfigureAwait(false));
            StringAssert.Contains(dummyException.Message, exp1.Message);
        }

        [Test]
        public async Task BulkGetModulesAsync_Tests()
        {
            MethodInfo method_BulkGetModulesAsync = typeof(ModulesBaseController).GetMethod("BulkGetModulesAsync", BindingFlags.Instance | BindingFlags.NonPublic);

            // test regular execution
            var task = (Task<IEnumerable<AEVAModule>>)method_BulkGetModulesAsync.Invoke(_fakeModulesBaseController, new object[] { _dummyWorkspaceIdentity, new string[] { _guid, _guid, _guid } });
            var actualRet = await task.ConfigureAwait(false);
            IEnumerable<AEVAModule> expectedRet = _dummyModuleEntities.Select(entity => ModulesBaseController.BuildModule<AEVAModule>(entity, new HashSet<string>() { entity.Id }));
            actualRet.Should().BeEquivalentTo(expectedRet);

            // test when bulk get throw exception
            var dummyException = new Exception("dummy exception");
            Mock.Get(_fakeIModuleStore)
                .Setup(
                   stores => stores.BulkGetIdsAsync(
                      It.IsAny<string>(),
                      It.IsAny<string[]>(),
                      It.IsAny<WorkspaceIdentity>()))
                .Throws(dummyException);

            task = (Task<IEnumerable<AEVAModule>>)method_BulkGetModulesAsync.Invoke(_fakeModulesBaseController, new object[] { _dummyWorkspaceIdentity, new string[] { _guid, _guid, _guid } });
            var exp1 = Assert.ThrowsAsync<BaseException>(async () => await task.ConfigureAwait(false));
            StringAssert.Contains(dummyException.Message, exp1.Message);
        }

        [Test]
        public async Task BulkGetModulesV2Async_Tests()
        {
            MethodInfo method_BulkGetModulesV2Async = typeof(ModulesBaseController).GetMethod("BulkGetModulesV2Async", BindingFlags.Instance | BindingFlags.NonPublic);

            // test regular execution
            var task = (Task<IEnumerable<AEVAModule>>)method_BulkGetModulesV2Async.Invoke(_fakeModulesBaseController, new object[] { _dummyWorkspaceIdentity, new string[] { _guid, _guid, _guid } });
            var actualRet = await task.ConfigureAwait(false);
            IEnumerable<AEVAModule> expectedRet = _dummyModuleEntities.Select(entity => ModulesBaseController.BuildModule<AEVAModule>(entity, new HashSet<string>() { entity.Id }));
            actualRet.Should().BeEquivalentTo(expectedRet);

            // test when bulk get throw exception
            var dummyException = new Exception("dummy exception");
            Mock.Get(_fakeIModuleStore)
                .Setup(
                   stores => stores.BulkGetIdsAsync(
                      It.IsAny<string>(),
                      It.IsAny<string[]>(),
                      It.IsAny<WorkspaceIdentity>()))
                .Throws(dummyException);

            task = (Task<IEnumerable<AEVAModule>>)method_BulkGetModulesV2Async.Invoke(_fakeModulesBaseController, new object[] { _dummyWorkspaceIdentity, new string[] { _guid, _guid, _guid } });
            var exp1 = Assert.ThrowsAsync<BaseException>(async () => await task.ConfigureAwait(false));
            StringAssert.Contains(dummyException.Message, exp1.Message);
        }

        [Test]
        public async Task GetModuleByHashAsync_Tests()
        {
            MethodInfo method_GetModuleByHashAsync = typeof(ModulesBaseController).GetMethod("GetModuleByHashAsync", BindingFlags.Instance | BindingFlags.NonPublic);

            // test regular execution
            var task = (Task<AEVAModule>)method_GetModuleByHashAsync.Invoke(_fakeModulesBaseController, new object[] { _dummyWorkspaceIdentity, _guid });
            var actualRet = await task.ConfigureAwait(false);
            actualRet.Should().BeEquivalentTo(ModulesBaseController.BuildModule<AEVAModule>(_dummyModuleEntity, new HashSet<string>() { _guid }));

            // test return is null when storemodule is null
            _dummyModuleEntity = null;
            Mock.Get(_fakeIModuleStore)
                .Setup(
                   stores => stores.TryGetEntityByHashAsync(
                      It.IsAny<string>(),
                      It.IsAny<string>(),
                      It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(_dummyModuleEntity));
            task = (Task<AEVAModule>)method_GetModuleByHashAsync.Invoke(_fakeModulesBaseController, new object[] { _dummyWorkspaceIdentity, _guid });
            actualRet = await task.ConfigureAwait(false);
            Assert.IsNull(actualRet);

            // test when exception happened while get module
            var dummyException = new Exception("dummy exception");
            Mock.Get(_fakeIModuleStore)
                .Setup(
                   stores => stores.TryGetEntityByHashAsync(
                      It.IsAny<string>(),
                      It.IsAny<string>(),
                      It.IsAny<WorkspaceIdentity>()))
                .Throws(dummyException);
            task = (Task<AEVAModule>)method_GetModuleByHashAsync.Invoke(_fakeModulesBaseController, new object[] { _dummyWorkspaceIdentity, _guid });
            var exp1 = Assert.ThrowsAsync<BaseException>(async () => await task.ConfigureAwait(false));
            StringAssert.Contains(dummyException.Message, exp1.Message);
        }

        [Test]
        public async Task UpdateModuleAsync_Tests2()
        {
            Assert.IsNull(_dummyModuleEntity1.CreatedBy);
            Assert.IsNull(_dummyModuleEntity1.Category);
            var actualRet = await _fakeModulesBaseController.UpdateModuleAsync(_dummyModuleEntity1, _fakeIModuleStore).ConfigureAwait(false);
            Assert.AreEqual(_dummyModuleEntity1, actualRet);
            Assert.NotNull(_dummyModuleEntity1.CreatedBy);
            Assert.NotNull(_dummyModuleEntity1.Category);
            Assert.AreEqual(_dummyModuleEntity1.CreatedBy, _dummyModuleEntity.CreatedBy);
            Assert.AreEqual(_dummyModuleEntity1.Category, _dummyModuleEntity.Category);
        }
    }
}
