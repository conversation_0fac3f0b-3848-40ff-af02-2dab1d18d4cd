﻿// <copyright file="AmlModuleVersionsBaseControllerTests.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Security.Claims;
using System.Threading.Tasks;
using FluentAssertions;
using Microsoft.Aether.AEVA.DataContractConverter;
using Microsoft.Aether.AEVA.DataContracts;
using Microsoft.Aether.BackendCommon;
using Microsoft.Aether.BackendCommon.AzureMonitor;
using Microsoft.Aether.DataContracts;
using Microsoft.Aether.DataContracts.Entity;
using Moq;
using NUnit.Framework;
using CreatedBy = Microsoft.Aether.DataContracts.CreatedBy;
using ModuleEntity = Microsoft.Aether.DataContracts.ModuleEntity;

namespace Microsoft.Aether.AE3pService.Tests
{
    public class AmlModuleVersionsBaseControllerTests
    {
        private AmlModuleVersionsBaseController _fakeAmlModuleVersionsBaseController;
        private IStoreCollection _fakeIStoreCollection;
        private IContextProvider _fakeIContextProvider;
        private IWorkspaceResourcesCache _fakeIWorkspaceResourcesCache;

        private IAmlModuleStore _fakeIAmlModuleStore;
        private IModuleStore _fakeIModuleStore;
        private IDataTypeStore _fakeIDataTypeStore;
        private CreatedBy _fakeCreatedBy;
        private ModuleEntity _fakeModuleEntity;
        private IEnumerable<ModuleEntity> _fakeModuleEntities;
        private AmlModuleEntity _fakeAmlModuleEntity;
        private IAzureMonitorStatusLogger _azureMonitorStatusEmitter;
        private IEnumerable<DataContracts.DataTypeEntity> _fakeDataTypeEntity;

        private string _studioModuleReservedKey;
        private string _studioBuiltinModulePrefix;
        private string _invalidName;

        private AzureMLModuleVersionCreationInfo _creationInfo;

        private readonly MockRepository _repository;
        private readonly string _guid;
        private readonly WorkspaceIdentity _dummyWorkspaceIdentity;

        public AmlModuleVersionsBaseControllerTests()
        {
            _repository = new MockRepository(MockBehavior.Loose) { DefaultValue = DefaultValue.Mock, CallBase = true };

            _guid = Guid.NewGuid().ToString();
            _dummyWorkspaceIdentity = new WorkspaceIdentity();
        }

        [SetUp]
        public void Init()
        {
            _fakeIStoreCollection = _repository.Create<IStoreCollection>().Object;
            _fakeIContextProvider = _repository.Create<IContextProvider>().Object;
            _fakeIWorkspaceResourcesCache = _repository.Create<IWorkspaceResourcesCache>().Object;
            _azureMonitorStatusEmitter = _repository.Create<IAzureMonitorStatusLogger>().Object;
            _fakeIAmlModuleStore = _repository.Create<IAmlModuleStore>().Object;
            Mock.Get(_fakeIStoreCollection)
                .Setup(
                   stores => stores.GetAmlModuleStore(
                       It.IsAny<ClaimsPrincipal>(),
                       It.IsAny<WorkspaceIdentity>()))
                .Returns(_fakeIAmlModuleStore);

            _fakeIModuleStore = _repository.Create<IModuleStore>().Object;
            Mock.Get(_fakeIStoreCollection)
                .Setup(
                   stores => stores.GetModuleStore(
                       It.IsAny<ClaimsPrincipal>(),
                       It.IsAny<WorkspaceIdentity>()))
                .Returns(_fakeIModuleStore);

            _fakeIDataTypeStore = _repository.Create<IDataTypeStore>().Object;
            Mock.Get(_fakeIStoreCollection)
                .Setup(
                   stores => stores.GetDataTypeStore(
                       It.IsAny<ClaimsPrincipal>(),
                       It.IsAny<WorkspaceIdentity>()))
                .Returns(_fakeIDataTypeStore);

            _fakeCreatedBy = _repository.Create<CreatedBy>().Object;
            Mock.Get(_fakeIContextProvider)
                .Setup(
                   provider => provider.GetCreatedByObject())
                .Returns(_fakeCreatedBy);

            _fakeModuleEntity = _repository.Create<ModuleEntity>().Object;
            Mock.Get(_fakeIModuleStore)
                .Setup(
                   store => store.GetEntityAsync(
                       It.IsAny<string>(),
                       It.IsAny<string>(),
                       It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(_fakeModuleEntity));

            _fakeDataTypeEntity = new List<DataContracts.DataTypeEntity>()
            {
                new DataContracts.DataTypeEntity()
                {
                    Id = _guid
                }
            };

            _fakeModuleEntities = new List<ModuleEntity>()
            {
                new ModuleEntity()
                {
                    FamilyId = "0"
                },

                new ModuleEntity()
                {
                    FamilyId = "33",
                    StructuredInterface = new DataContracts.StructuredInterface()
                }
            };

            Mock.Get(_fakeIDataTypeStore)
                .Setup(stores => stores.ListAsync(
                             It.IsAny<string>(),
                             It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(_fakeDataTypeEntity));

            Mock.Get(_fakeIModuleStore)
               .Setup(
                  stores => stores.BulkGetIdsAsync(
                      It.IsAny<string>(),
                      It.IsAny<string[]>(),
                      It.IsAny<WorkspaceIdentity>()))
               .Returns(Task.FromResult(_fakeModuleEntities));

            _fakeAmlModuleVersionsBaseController = _repository.Create<AmlModuleVersionsBaseController>(_fakeIStoreCollection, _fakeIContextProvider, _fakeIWorkspaceResourcesCache, _azureMonitorStatusEmitter).Object;
            MethodInfo method_UpdateAsync = typeof(AmlModuleVersionsBaseController).GetMethod("UpdateAsync", BindingFlags.Instance | BindingFlags.NonPublic);

            Mock.Get(_fakeAmlModuleVersionsBaseController)
               .Setup(controller => controller.UpdateAsync(
                       It.IsAny<IUpdatableEntityStore<ModuleEntity>>(),
                       It.IsAny<ModuleEntity>(),
                       It.IsAny<Type>(),
                       It.IsAny<bool>()))
               .Returns(Task.FromResult(_fakeModuleEntity));

            FieldInfo field_studioBuiltinModulePrefix = typeof(AmlModuleVersionsBaseController).BaseType.GetField("_studioBuiltinModulePrefix", BindingFlags.Static | BindingFlags.NonPublic);
            _studioBuiltinModulePrefix = (string)field_studioBuiltinModulePrefix.GetValue(_fakeAmlModuleVersionsBaseController);
            FieldInfo field_studioModuleReservedKey = typeof(AmlModuleVersionsBaseController).BaseType.GetField("_studioModuleReservedKey", BindingFlags.Static | BindingFlags.NonPublic);
            _studioModuleReservedKey = (string)field_studioModuleReservedKey.GetValue(_fakeAmlModuleVersionsBaseController);

            _invalidName = _studioBuiltinModulePrefix + _guid;

            _creationInfo = new AzureMLModuleVersionCreationInfo(
                            name: _invalidName,
                            displayName: "display name",
                            description: "mvDescription" + _guid,
                            isDeterministic: false,
                            moduleExecutionType: "mvExecutionType",
                            hash: "mvHash",
                            identifierHash: "mvIdentifierHash",
                            amlModuleId: null,
                            version: "1.0",
                            moduleTypeVersion: "1.0");
            _creationInfo.Properties = new Dictionary<string, string>()
            {
                { _studioModuleReservedKey, _guid}
            };

            _fakeAmlModuleEntity = _repository.Create<AmlModuleEntity>().Object;
        }

        [Test]
        public async Task CreateAzureMLModuleVersionAsync_Tests()
        {
            MethodInfo method_CreateAzureMLModuleVersionAsync = typeof(AmlModuleVersionsBaseController).GetMethod("CreateAzureMLModuleVersionAsync", BindingFlags.Instance | BindingFlags.NonPublic);

            // test regular excution
            _creationInfo.Name = _guid;
            var expectedAzureMLModuleVersion = new AzureMLModuleVersion();
            Mock.Get(_fakeAmlModuleVersionsBaseController)
                .Setup(controller => controller.CreateModuleVersionHelperAsync(
                        It.IsAny<IAmlModuleStore>(),
                        It.IsAny<IModuleStore>(),
                        It.IsAny<IDataTypeStore>(),
                        It.IsAny<WorkspaceIdentity>(),
                        It.IsAny<AE365ModuleVersionCreationInfo>(),
                        It.IsAny<CreatedBy>(),
                        It.IsAny<string>()))
               .Returns(Task.FromResult(expectedAzureMLModuleVersion));

            var task = (Task<AzureMLModuleVersion>)method_CreateAzureMLModuleVersionAsync.Invoke(_fakeAmlModuleVersionsBaseController, new object[] { _dummyWorkspaceIdentity, _creationInfo });
            var actualRet = await task.ConfigureAwait(false);
            Assert.AreEqual(expectedAzureMLModuleVersion, actualRet);
        }

        [Test]
        public async Task GetAzureMLModuleVersionByHashAsync_Tests()
        {
            MethodInfo method_GetAzureMLModuleVersionByHashAsync = typeof(AmlModuleVersionsBaseController).GetMethod("GetAzureMLModuleVersionByHashAsync", BindingFlags.Instance | BindingFlags.NonPublic);

            var fakeModuleEntity = _repository.Create<ModuleEntity>().Object;
            Mock.Get(_fakeIModuleStore)
                .Setup(stores => stores.TryGetEntityByHashAsync(
                        It.IsAny<string>(),
                        It.IsAny<string>(),
                        It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(fakeModuleEntity));

            // test when ModuleEntity.FamilyId is "0"
            fakeModuleEntity.FamilyId = "0";
            var task = (Task<AzureMLModuleVersion>)method_GetAzureMLModuleVersionByHashAsync.Invoke(_fakeAmlModuleVersionsBaseController, new object[] { _dummyWorkspaceIdentity, "dummyIdHash" });
            var actualRet = await task.ConfigureAwait(false);
            Assert.True(actualRet is AzureMLModuleVersion);
            Assert.AreEqual(null, actualRet.ModuleId);

            // test when ModuleEntity.FamilyId is "0"
            fakeModuleEntity.FamilyId = "1";
            task = (Task<AzureMLModuleVersion>)method_GetAzureMLModuleVersionByHashAsync.Invoke(_fakeAmlModuleVersionsBaseController, new object[] { _dummyWorkspaceIdentity, "dummyIdHash" });
            actualRet = await task.ConfigureAwait(false);
            Assert.True(actualRet is AzureMLModuleVersion);
            Assert.AreEqual(fakeModuleEntity.FamilyId, actualRet.ModuleId);

            // test when moduleEntity not exist or null
            fakeModuleEntity = null;
            Mock.Get(_fakeIModuleStore)
                .Setup(stores => stores.TryGetEntityByHashAsync(
                        It.IsAny<string>(),
                        It.IsAny<string>(),
                        It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(fakeModuleEntity));
            task = (Task<AzureMLModuleVersion>)method_GetAzureMLModuleVersionByHashAsync.Invoke(_fakeAmlModuleVersionsBaseController, new object[] { _dummyWorkspaceIdentity, "dummyIdHash" });
            actualRet = await task.ConfigureAwait(false);
            Assert.AreEqual(null, actualRet);
        }

        [Test]
        public async Task CreateAzureMLModuleVersionWithIdAsync_Tests()
        {
            MethodInfo method_CreateAzureMLModuleVersionWithIdAsync = typeof(AmlModuleVersionsBaseController).GetMethod("CreateAzureMLModuleVersionWithIdAsync", BindingFlags.Instance | BindingFlags.NonPublic);
            var expectedAzureMLModuleVersion = new AzureMLModuleVersion();

            Mock.Get(_fakeAmlModuleVersionsBaseController)
            .Setup(controller => controller.CreateModuleVersionHelperAsync(
                    It.IsAny<IAmlModuleStore>(),
                    It.IsAny<IModuleStore>(),
                    It.IsAny<IDataTypeStore>(),
                    It.IsAny<WorkspaceIdentity>(),
                    It.Is<AE365ModuleVersionCreationInfo>(info => info.Name.StartsWith(_studioBuiltinModulePrefix)),
                    It.IsAny<CreatedBy>(),
                    It.IsAny<string>()))
           .Returns(Task.FromResult(expectedAzureMLModuleVersion));

            // test when creationInfo name with prefix
            StringAssert.StartsWith(_studioBuiltinModulePrefix, _creationInfo.Name);
            var task = (Task<AzureMLModuleVersion>)method_CreateAzureMLModuleVersionWithIdAsync.Invoke(_fakeAmlModuleVersionsBaseController, new object[] { _dummyWorkspaceIdentity, _creationInfo });
            var actualRet = await task.ConfigureAwait(false);
            Assert.AreEqual(expectedAzureMLModuleVersion, actualRet);

            // test when creationInfo name with no prefix
            _creationInfo.Name = _guid;
            StringAssert.DoesNotStartWith(_studioBuiltinModulePrefix, _creationInfo.Name);
            task = (Task<AzureMLModuleVersion>)method_CreateAzureMLModuleVersionWithIdAsync.Invoke(_fakeAmlModuleVersionsBaseController, new object[] { _dummyWorkspaceIdentity, _creationInfo });
            actualRet = await task.ConfigureAwait(false);
            Assert.AreEqual(expectedAzureMLModuleVersion, actualRet);
        }

        [Test]
        public async Task FetchModulesAsync_Tests()
        {
            // test module family ID is  "0" and StructuredInterface is null
            string[] ids = new string[] { _guid };
            Assert.IsNull(_fakeModuleEntities.FirstOrDefault().StructuredInterface);
            var actualRet = await AmlModuleVersionsBaseController.FetchModulesAsync(_fakeIModuleStore, _fakeIDataTypeStore, ids).ConfigureAwait(false);
            Assert.AreEqual(_fakeModuleEntities.Count(), actualRet.Count());
            Assert.IsNull(actualRet.ElementAt(0).ModuleId);
            Assert.AreEqual(_fakeModuleEntities.ElementAt(1).FamilyId, actualRet.ElementAt(1).ModuleId);
            var expectedModule = ContractConverter.Convert<ModuleEntity, AEVA.DataContracts.ModuleEntity>(_fakeModuleEntities.FirstOrDefault());
            var actualModule = actualRet.FirstOrDefault().Data;
            actualModule.Should().BeEquivalentTo(expectedModule);
        }

        [Test]
        public async Task CreateModuleVersionHelperAsync_Tests_Execution()
        {
            _fakeAmlModuleEntity.Id = Guid.NewGuid().ToString();
            bool called_GetEntityAsync = false;
            Mock.Get(_fakeIAmlModuleStore)
                .Setup(store => store.GetEntityAsync(
                       It.IsAny<string>(),
                       It.IsAny<string>(),
                       It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(_fakeAmlModuleEntity))
                .Callback(() => called_GetEntityAsync = true);

            // test when creationInfo.AmlModuleId is "0"
            _creationInfo = new AzureMLModuleVersionCreationInfo(
                name: _invalidName,
                displayName: "display name",
                description: "mvDescription" + _guid,
                isDeterministic: false,
                moduleExecutionType: "mvExecutionType",
                hash: "mvHash",
                identifierHash: "mvIdentifierHash",
                amlModuleId: "0",
                version: null);

            bool called_CreateAsync = false;
            bool called_UpdateAsync = false;
            Mock.Get(_fakeIModuleStore)
                .Setup(store => store.CreateAsync(
                       It.IsAny<string>(),
                       It.IsAny<ModuleEntity>(),
                       It.IsAny<WorkspaceIdentity>(),
                       It.IsAny<CreatedBy>()))
                .Returns(Task.FromResult(_fakeModuleEntity))
                .Callback(() => called_CreateAsync = true);

            Mock.Get(_fakeIAmlModuleStore)
                .Setup(store => store.UpdateAsync(
                       It.IsAny<string>(),
                       It.IsAny<AmlModuleEntity>(),
                       It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(_fakeAmlModuleEntity))
                .Callback(() => called_UpdateAsync = true);

            var actualRet = await _fakeAmlModuleVersionsBaseController.CreateModuleVersionHelperAsync(
                amlModuleStore: _fakeIAmlModuleStore,
                moduleStore: _fakeIModuleStore,
                dataTypeStore: _fakeIDataTypeStore,
                workspaceIdentity: _dummyWorkspaceIdentity,
                creationInfo: _creationInfo,
                createdBy: _fakeCreatedBy,
                id: string.Empty).ConfigureAwait(false);
            Assert.True(called_CreateAsync);
            Assert.False(called_UpdateAsync);
            Assert.IsNull(actualRet.ModuleId);
            Assert.False(called_GetEntityAsync);

            // test when AmlModuleId is not "0"
            called_CreateAsync = false;
            called_UpdateAsync = false;
            called_GetEntityAsync = false;
            _creationInfo = new AzureMLModuleVersionCreationInfo(
                name: _invalidName,
                displayName: "display name",
                description: "mvDescription" + _guid,
                isDeterministic: false,
                moduleExecutionType: "mvExecutionType",
                hash: "mvHash",
                identifierHash: "mvIdentifierHash",
                amlModuleId: "2",
                version: null,
                setAsDefaultVersion: true);

            actualRet = await _fakeAmlModuleVersionsBaseController.CreateModuleVersionHelperAsync(
                amlModuleStore: _fakeIAmlModuleStore,
                moduleStore: _fakeIModuleStore,
                dataTypeStore: _fakeIDataTypeStore,
                workspaceIdentity: _dummyWorkspaceIdentity,
                creationInfo: _creationInfo,
                createdBy: _fakeCreatedBy,
                id: string.Empty).ConfigureAwait(false);
            Assert.True(called_CreateAsync);
            Assert.True(called_UpdateAsync);
            Assert.True(called_GetEntityAsync);
            Assert.AreEqual(_fakeAmlModuleEntity.Id, actualRet.ModuleId);
        }
    }
}
