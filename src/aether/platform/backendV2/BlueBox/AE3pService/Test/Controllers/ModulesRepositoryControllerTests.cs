﻿// <copyright file="ModulesRepositoryControllerTests.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Security.Claims;
using System.Threading.Tasks;
using Microsoft.Aether.BackendCommon;
using Microsoft.Aether.BackendCommon.AzureMonitor;
using Microsoft.Aether.BlueBox.RepositoryResourcesClient;
using Microsoft.Aether.BlueBox.SnapshotClient;
using Microsoft.Aether.DataContracts;
using Microsoft.Aether.DataContracts.Entity;
using Moq;
using NUnit.Framework;
using AEVAModuleCreationInfo = Microsoft.Aether.AEVA.DataContracts.ModuleCreationInfo;
using AEVAModuleEntity = Microsoft.Aether.AEVA.DataContracts.ModuleEntity;
using AEVASubGraphCreationInfo = Microsoft.Aether.AEVA.DataContracts.SubGraphCreationInfo;

namespace Microsoft.Aether.AE3pService.Tests.Controllers
{
    [TestFixture]
    public class ModulesRepositoryControllerTests
    {
        private readonly MockRepository _repository;
        private ModulesRepositoryController _modulesRepositoryController;

        private IStoreCollection _fakeIStoreCollection;
        private IContextProvider _fakeIContextProvider;
        private IWorkspaceResourcesCache _fakeIWorkspaceResourcesCache;
        private ISnapshotClient _fakeISnapshotClient;
        private IRepositoryResourcesClient _fakeRepositoryClient;
        private AEVAModuleCreationInfo _dummyAEVAModuleCreationInfo;
        private AEVAModuleEntity _dummyAEVAModuleEntity;
        private ModuleEntity _dummyModuleEntity;
        private IModuleStore _fakeIModuleStore;
        private IGraphStore _fakeGraphStore;
        private IAmlGraphDraftStore _fakeGraphDraftStore;
        private readonly string workspaceId = "workspaceId";
        private IAzureMonitorStatusLogger _azureMonitorStatusEmitter;

        public ModulesRepositoryControllerTests()
        {
            _repository = new MockRepository(MockBehavior.Loose) { DefaultValue = DefaultValue.Mock, CallBase = true };
            _fakeIStoreCollection = _repository.Create<IStoreCollection>().Object;
            _fakeIContextProvider = _repository.Create<IContextProvider>().Object;
            _azureMonitorStatusEmitter = _repository.Create<IAzureMonitorStatusLogger>().Object;
            _fakeIWorkspaceResourcesCache = _repository.Create<IWorkspaceResourcesCache>().Object;
            _fakeISnapshotClient = _repository.Create<ISnapshotClient>().Object;
            _fakeRepositoryClient = _repository.Create<IRepositoryResourcesClient>().Object;
            _modulesRepositoryController = new ModulesRepositoryController(
                _fakeIStoreCollection,
                _fakeIContextProvider,
                _fakeIWorkspaceResourcesCache,
                _fakeISnapshotClient,
                _fakeRepositoryClient, _azureMonitorStatusEmitter);
        }

        [SetUp]
        public void Init()
        {
            _dummyAEVAModuleCreationInfo = new AEVAModuleCreationInfo()
            {
                Name = "testCreationInfo"
            };
            _dummyAEVAModuleEntity = new AEVAModuleEntity()
            {
                Name = "testModuleEntity",
                Id = "idTest"
            };
            _dummyModuleEntity = new ModuleEntity()
            {
                Name = "testEntity"
            };
            _fakeIModuleStore = _repository.Create<IModuleStore>().Object;

            Mock.Get(_fakeIWorkspaceResourcesCache)
                .Setup(stores => stores.GetWorkspaceIdAsync(
                             It.IsAny<string>(),
                             It.IsAny<string>(),
                             It.IsAny<string>(),
                             It.IsAny<CreatedBy>()))
                .Returns(Task.FromResult(workspaceId));

            Mock<IGraphStore> mockGraphStore = new Mock<IGraphStore>();
            mockGraphStore.Setup(g => g.CreateAsync(It.IsAny<string>(), It.IsAny<DataContracts.GraphEntity>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(
                    (string workspaceId, GraphEntity graphEntity, WorkspaceIdentity identity) =>
                    {
                        graphEntity.Id = Guid.NewGuid().ToString();
                        return Task.FromResult(graphEntity);
                    });
            _fakeGraphStore = mockGraphStore.Object;

            Mock<IAmlGraphDraftStore> mockGraphDraftStore = new Mock<IAmlGraphDraftStore>();
            mockGraphDraftStore.Setup(g => g.CreateAsync(It.IsAny<string>(), It.IsAny<AmlGraphDraftEntity>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(
                    (string workspaceId, AmlGraphDraftEntity graphDraftEntity, WorkspaceIdentity identity) =>
                    {
                        graphDraftEntity.Id = Guid.NewGuid().ToString();
                        return Task.FromResult(graphDraftEntity);
                    });
            _fakeGraphDraftStore = mockGraphDraftStore.Object;

            Mock.Get(_fakeIStoreCollection)
                .Setup(stores => stores.GetModuleStore(
                                It.IsAny<ClaimsPrincipal>(),
                                It.IsAny<WorkspaceIdentity>()))
                .Returns(_fakeIModuleStore);

            Mock.Get(_fakeIStoreCollection)
                .Setup(stores => stores.GetGraphStore(
                    It.IsAny<ClaimsPrincipal>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Returns(_fakeGraphStore);

            Mock.Get(_fakeIStoreCollection)
                .Setup(stores => stores.GetAmlGraphDraftStore(
                                It.IsAny<ClaimsPrincipal>(),
                                It.IsAny<WorkspaceIdentity>()))
                .Returns(_fakeGraphDraftStore);

            Mock.Get(_fakeIModuleStore)
                .Setup(stores => stores.GetEntityAsync(
                                It.IsAny<string>(),
                                It.IsAny<string>(),
                                It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(_dummyModuleEntity));

            Mock.Get(_fakeIModuleStore)
                .Setup(stores => stores.GetEntityAsync(
                                It.IsAny<string>(),
                                It.IsAny<string>(),
                                It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(_dummyModuleEntity));

            Mock.Get(_fakeIModuleStore)
                .Setup(stores => stores.CreateAsync(
                                It.IsAny<string>(),
                                It.IsAny<ModuleEntity>(),
                                It.IsAny<WorkspaceIdentity>(),
                                It.IsAny<CreatedBy>()))
                .Returns(
                    (string workspaceId, ModuleEntity moduleEntity, WorkspaceIdentity identity, CreatedBy createdBy) =>
                    {
                        moduleEntity.Id = Guid.NewGuid().ToString();
                        return Task.FromResult(moduleEntity);
                    });
        }

        [Test]
        public void CreateModuleAsync_ExpectNormalReturn()
        {
            Assert.DoesNotThrowAsync(async () => await _modulesRepositoryController.CreateModuleAsync(
                "repositoryId",
                _dummyAEVAModuleCreationInfo).ConfigureAwait(false));
        }

        [Test]
        public void GetModuleAsync_ExpectNormalReturn()
        {
            Assert.DoesNotThrowAsync(async () => await _modulesRepositoryController.GetModuleAsync(
                "repositoryId",
                "idTest").ConfigureAwait(false));
        }

        [Test]
        public void GetModuleV2Async_ExpectNormalReturn()
        {
            Assert.DoesNotThrowAsync(async () => await _modulesRepositoryController.GetModuleV2Async(
                "repositoryId",
                "idV2").ConfigureAwait(false));
        }

        [Test]
        public void BulkGetModulesAsync_ExpectNormalReturn()
        {
            Assert.DoesNotThrowAsync(async () => await _modulesRepositoryController.BulkGetModulesAsync(
                "repositoryId",
                new string[] { "id" }).ConfigureAwait(false));
        }

        [Test]
        public void BulkGetModulesV2Async_ExpectNormalReturn()
        {
            Assert.DoesNotThrowAsync(async () => await _modulesRepositoryController.BulkGetModulesV2Async(
                "repositoryId",
                new string[] { "idV2" }).ConfigureAwait(false));
        }

        [Test]
        public void GetModuleByHashAsync_ExpectNormalReturn()
        {
            Assert.DoesNotThrowAsync(async () => await _modulesRepositoryController.GetModuleByHashAsync(
                "repositoryId",
                "idHash").ConfigureAwait(false));
        }

        [Test]
        public async Task TestCreateSubGraphModuleAsync_Normal_Return()
        {
            AEVASubGraphCreationInfo creationInfo = new AEVASubGraphCreationInfo()
            {
                Name = "subgraph",
                VisualGraph = new AEVA.DataContracts.VisualGraph()
                {
                    Graph = new AEVA.DataContracts.GraphEntity()
                    {
                        ModuleNodes = new List<AEVA.DataContracts.GraphModuleNode>(),
                        Edges = new List<AEVA.DataContracts.GraphEdge>()
                    }
                },
                Interface = new AEVA.DataContracts.EntityInterface()
            };
            var result = await _modulesRepositoryController.CreateSubGraphModuleAsync("repositoryId", creationInfo).ConfigureAwait(false);
            Assert.AreEqual("subgraph", result.Name);
            Assert.IsNotNull(result.CloudSettings.SubGraphConfig.GraphId);

            creationInfo.CreateAsDraft = true;
            result = await _modulesRepositoryController.CreateSubGraphModuleAsync("repositoryId", creationInfo).ConfigureAwait(false);
            Assert.AreEqual("subgraph", result.Name);
            Assert.IsNotNull(result.CloudSettings.SubGraphConfig.GraphDraftId);
        }
    }
}
