﻿// <copyright file="ModulesWorkspaceControllerTests.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Security.Claims;
using System.Threading.Tasks;
using Microsoft.Aether.BackendCommon;
using Microsoft.Aether.BackendCommon.AzureMonitor;
using Microsoft.Aether.BlueBox.SnapshotClient;
using Microsoft.Aether.DataContracts;
using Microsoft.Aether.DataContracts.Entity;
using Moq;
using NUnit.Framework;
using AEVAModuleCreationInfo = Microsoft.Aether.AEVA.DataContracts.ModuleCreationInfo;
using AEVAModuleEntity = Microsoft.Aether.AEVA.DataContracts.ModuleEntity;
using AEVASubGraphCreationInfo = Microsoft.Aether.AEVA.DataContracts.SubGraphCreationInfo;

namespace Microsoft.Aether.AE3pService.Tests.Controllers
{
    public class ModulesWorkspaceControllerTests
    {
        private readonly MockRepository _repository;
        private ModulesWorkspaceController _fakeModulesWorkspaceController;
        private IAzureMonitorStatusLogger _azureMonitorStatusEmitter;

        private IStoreCollection _fakeIStoreCollection;
        private IContextProvider _fakeIContextProvider;
        private IWorkspaceResourcesCache _fakeIWorkspaceResourcesCache;
        private ISnapshotClient _fakeISnapshotClient;
        private AEVAModuleCreationInfo _dummyAEVAModuleCreationInfo;
        private AEVAModuleEntity _dummyAEVAModuleEntity;
        private IModuleStore _fakeIModuleStore;
        private IGraphStore _fakeGraphStore;
        private IAmlGraphDraftStore _fakeGraphDraftStore;
        private ModuleEntity _dummyModuleEntity;

        private string subscriptionId;
        private string resourceGroupName;
        private string workspaceName;
        private string workspaceId;

        public ModulesWorkspaceControllerTests()
        {
            _repository = new MockRepository(MockBehavior.Loose) { DefaultValue = DefaultValue.Mock, CallBase = true };
        }

        [SetUp]
        public void Init()
        {
            subscriptionId = Guid.NewGuid().ToString();
            resourceGroupName = "testRG";
            workspaceName = "workspaceName";
            workspaceId = Guid.NewGuid().ToString();

            _fakeIStoreCollection = _repository.Create<IStoreCollection>().Object;
            _fakeIContextProvider = _repository.Create<IContextProvider>().Object;
            _fakeIWorkspaceResourcesCache = _repository.Create<IWorkspaceResourcesCache>().Object;
            _fakeISnapshotClient = _repository.Create<ISnapshotClient>().Object;
            _fakeIModuleStore = _repository.Create<IModuleStore>().Object;
            _azureMonitorStatusEmitter = _repository.Create<IAzureMonitorStatusLogger>().Object;
            _dummyAEVAModuleCreationInfo = new AEVAModuleCreationInfo()
            {
                Name = "hello"
            };
            _dummyAEVAModuleEntity = new AEVAModuleEntity()
            {
                Name = "hello",
                Id = workspaceId
            };
            _dummyModuleEntity = new ModuleEntity()
            {
                Name = "hello"
            };

            Mock<IGraphStore> mockGraphStore = new Mock<IGraphStore>();
            mockGraphStore.Setup(g => g.CreateAsync(It.IsAny<string>(), It.IsAny<DataContracts.GraphEntity>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(
                    (string workspaceId, GraphEntity graphEntity, WorkspaceIdentity identity) =>
                    {
                        graphEntity.Id = Guid.NewGuid().ToString();
                        return Task.FromResult(graphEntity);
                    });
            _fakeGraphStore = mockGraphStore.Object;

            Mock<IAmlGraphDraftStore> mockGraphDraftStore = new Mock<IAmlGraphDraftStore>();
            mockGraphDraftStore.Setup(g => g.CreateAsync(It.IsAny<string>(), It.IsAny<AmlGraphDraftEntity>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(
                    (string workspaceId, AmlGraphDraftEntity graphDraftEntity, WorkspaceIdentity identity) =>
                    {
                        graphDraftEntity.Id = Guid.NewGuid().ToString();
                        return Task.FromResult(graphDraftEntity);
                    });
            _fakeGraphDraftStore = mockGraphDraftStore.Object;

            Mock.Get(_fakeIWorkspaceResourcesCache)
                .Setup(stores => stores.GetWorkspaceIdAsync(
                             It.IsAny<string>(),
                             It.IsAny<string>(),
                             It.IsAny<string>(),
                             It.IsAny<CreatedBy>()))
                .Returns(Task.FromResult(workspaceId));

            Mock.Get(_fakeIStoreCollection)
                .Setup(stores => stores.GetModuleStore(
                                It.IsAny<ClaimsPrincipal>(),
                                It.IsAny<WorkspaceIdentity>()))
                .Returns(_fakeIModuleStore);

            Mock.Get(_fakeIStoreCollection)
                .Setup(stores => stores.GetGraphStore(
                                It.IsAny<ClaimsPrincipal>(),
                                It.IsAny<WorkspaceIdentity>()))
                .Returns(_fakeGraphStore);

            Mock.Get(_fakeIStoreCollection)
                .Setup(stores => stores.GetAmlGraphDraftStore(
                                It.IsAny<ClaimsPrincipal>(),
                                It.IsAny<WorkspaceIdentity>()))
                .Returns(_fakeGraphDraftStore);

            Mock.Get(_fakeIModuleStore)
                .Setup(stores => stores.GetEntityAsync(
                                It.IsAny<string>(),
                                It.IsAny<string>(),
                                It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(_dummyModuleEntity));

            Mock.Get(_fakeIModuleStore)
                .Setup(stores => stores.CreateAsync(
                                It.IsAny<string>(),
                                It.IsAny<ModuleEntity>(),
                                It.IsAny<WorkspaceIdentity>(),
                                It.IsAny<CreatedBy>()))
                .Returns(
                    (string workspaceId, ModuleEntity moduleEntity, WorkspaceIdentity identity, CreatedBy createdBy) =>
                    {
                        moduleEntity.Id = Guid.NewGuid().ToString();
                        return Task.FromResult(moduleEntity);
                    });

            Mock.Get(_fakeIModuleStore)
                .Setup(stores => stores.GetEntityAsync(
                                It.IsAny<string>(),
                                It.IsAny<string>(),
                                It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(_dummyModuleEntity));

            _fakeModulesWorkspaceController = _repository.Create<ModulesWorkspaceController>(
                    _fakeIStoreCollection,
                    _fakeIContextProvider,
                    _fakeIWorkspaceResourcesCache,
                    _fakeISnapshotClient,
                    _azureMonitorStatusEmitter).Object;

            Mock.Get(_fakeModulesWorkspaceController)
                .Setup(controller => controller.GetWorkspaceId())
                .Returns(workspaceId);

            Mock.Get(_fakeModulesWorkspaceController)
                .Setup(stores => stores.UpdateAsync(
                                It.IsAny<IUpdatableEntityStore<ModuleEntity>>(),
                                It.IsAny<ModuleEntity>(),
                                It.IsAny<Type>(),
                                It.IsAny<bool>()))
                .Returns(Task.FromResult(_dummyModuleEntity));
        }

        [Test]
        public void TestCreateModuleAsync_Normal_Return()
        {
            Assert.DoesNotThrowAsync(async () => await _fakeModulesWorkspaceController.CreateModuleAsync(
                subscriptionId,
                resourceGroupName,
                workspaceName,
                _dummyAEVAModuleCreationInfo).ConfigureAwait(false));
        }

        [Test]
        public void TestUpdateModuleAsync_Normal_Return()
        {
            Assert.DoesNotThrowAsync(async () => await _fakeModulesWorkspaceController.UpdateModuleAsync(
                subscriptionId,
                resourceGroupName,
                workspaceName,
                workspaceId,
                _dummyAEVAModuleEntity).ConfigureAwait(false));
        }

        [Test]
        public void TestGetModuleAsync_Normal_Return()
        {
            Assert.DoesNotThrowAsync(async () => await _fakeModulesWorkspaceController.GetModuleAsync(
                subscriptionId,
                resourceGroupName,
                workspaceName,
                workspaceId).ConfigureAwait(false));
        }

        [Test]
        public void TestGetModuleV2Async_Normal_Return()
        {
            Assert.DoesNotThrowAsync(async () => await _fakeModulesWorkspaceController.GetModuleV2Async(
                subscriptionId,
                resourceGroupName,
                workspaceName,
                workspaceId).ConfigureAwait(false));
        }

        [Test]
        public void TestBulkGetModulesAsync_Normal_Return()
        {
            Assert.DoesNotThrowAsync(async () => await _fakeModulesWorkspaceController.BulkGetModulesAsync(
                subscriptionId,
                resourceGroupName,
                workspaceName,
                new string[] { workspaceId }).ConfigureAwait(false));
        }

        [Test]
        public void TestBulkGetModulesV2Async_Normal_Return()
        {
            Assert.DoesNotThrowAsync(async () => await _fakeModulesWorkspaceController.BulkGetModulesV2Async(
                subscriptionId,
                resourceGroupName,
                workspaceName,
                new string[] { workspaceId }).ConfigureAwait(false));
        }

        [Test]
        public void TestGetModuleByHashAsync_Normal_Return()
        {
            Assert.DoesNotThrowAsync(async () => await _fakeModulesWorkspaceController.GetModuleByHashAsync(
                subscriptionId,
                resourceGroupName,
                workspaceName,
                workspaceId).ConfigureAwait(false));
        }

        [Test]
        public async Task TestCreateSubGraphModuleAsync_Normal_Return()
        {
            AEVASubGraphCreationInfo creationInfo = new AEVASubGraphCreationInfo()
            {
                Name = "subgraph",
                VisualGraph = new AEVA.DataContracts.VisualGraph()
                {
                    Graph = new AEVA.DataContracts.GraphEntity()
                    {
                        ModuleNodes = new List<AEVA.DataContracts.GraphModuleNode>(),
                        Edges = new List<AEVA.DataContracts.GraphEdge>()
                    }
                },
                Interface = new AEVA.DataContracts.EntityInterface(),
                IsDeterministic = false
            };
            var result = await _fakeModulesWorkspaceController.CreateSubGraphModuleAsync(subscriptionId, resourceGroupName, workspaceName, creationInfo).ConfigureAwait(false);
            Assert.AreEqual("subgraph", result.Name);
            Assert.IsNotNull(result.CloudSettings.SubGraphConfig.GraphId);
            Assert.AreEqual(result.IsDeterministic, false);

            creationInfo.IsDeterministic = true;
            result = await _fakeModulesWorkspaceController.CreateSubGraphModuleAsync(subscriptionId, resourceGroupName, workspaceName, creationInfo).ConfigureAwait(false);
            Assert.AreEqual("subgraph", result.Name);
            Assert.IsNotNull(result.CloudSettings.SubGraphConfig.GraphId);
            Assert.AreEqual(result.IsDeterministic, true);

            creationInfo.CreateAsDraft = true;
            result = await _fakeModulesWorkspaceController.CreateSubGraphModuleAsync(subscriptionId, resourceGroupName, workspaceName, creationInfo).ConfigureAwait(false);
            Assert.AreEqual("subgraph", result.Name);
            Assert.IsNotNull(result.CloudSettings.SubGraphConfig.GraphDraftId);
        }
    }
}
