﻿// <copyright file="PipelineRunsControllerTest.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Security.Claims;
using System.Threading.Tasks;
using FluentAssertions;
using Microsoft.Aether.AE3pService.Auth;
using Microsoft.Aether.AE3pService.Configuration;
using Microsoft.Aether.AE3pService.Extensions;
using Microsoft.Aether.AEVA.DataContractConverter;
using Microsoft.Aether.BackendCommon;
using Microsoft.Aether.BackendCommon.AzureMonitor;
using Microsoft.Aether.BlueBox.CredentialServiceClient;
using Microsoft.Aether.BlueBox.DataStoreClient;
using Microsoft.Aether.BlueBox.RunHistoryClient;
using Microsoft.Aether.BlueBox.WorkspaceResourcesContracts;
using Microsoft.Aether.CloudManager.ExperimentSubmitter.Client;
using Microsoft.Aether.DataContracts;
using Microsoft.Aether.DataContracts.Entity;
using Microsoft.Aether.DataContracts.Metastore;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Options;
using Microsoft.MachineLearning.AccountRP.Contracts;
using Microsoft.MachineLearning.Common.WebApi.TeamAccounts;
using Microsoft.MachineLearning.RunHistory.Contracts;
using Microsoft.RelInfra.Common.Configuration;
using Microsoft.RelInfra.Common.Exceptions;
using Microsoft.RelInfra.Instrumentation;
using Moq;
using NUnit.Framework;
using AEVAAzDevOpsCallBackInfo = Microsoft.Aether.AEVA.DataContracts.AzDevOpsCallBackInfo;
using AEVAEntityInterface = Microsoft.Aether.AEVA.DataContracts.EntityInterface;
using AEVAGraphDatasetNode = Microsoft.Aether.AEVA.DataContracts.GraphDatasetNode;
using AEVAGraphEntity = Microsoft.Aether.AEVA.DataContracts.GraphEntity;
using AEVANodeOutput = Microsoft.Aether.AEVA.DataContracts.NodeOutput;
using AEVAPipelineDraft = Microsoft.Aether.AEVA.DataContracts.PipelineDraft;
using AEVAPipelineRunCreationInfo = Microsoft.Aether.AEVA.DataContracts.PipelineRunCreationInfo;
using AEVAPipelineRunCreationInfoWithGraph = Microsoft.Aether.AEVA.DataContracts.PipelineRunCreationInfoWithGraph;
using AEVAPipelineRunEntity = Microsoft.Aether.AEVA.DataContracts.PipelineRunEntity;
using AEVAPipelineSubmissionInfo = Microsoft.Aether.AEVA.DataContracts.PipelineSubmissionInfo;
using AEVASchedulePipelineRunPayload = Microsoft.Aether.AEVA.DataContracts.SchedulePipelineRunPayload;
using AEVATaskStatus = Microsoft.Aether.AEVA.DataContracts.TaskStatus;
using AEVATaskStatusCode = Microsoft.Aether.AEVA.DataContracts.TaskStatusCode;
using PipelineSubmissionInfo = Microsoft.Aether.DataContracts.PipelineSubmissionInfo;

namespace Microsoft.Aether.AE3pService.Tests.Controllers
{
    public class PipelineRunsControllerTest
    {
        private readonly MockRepository _repository;

        private PipelineRunsController _fakePipelineRunsController;
        private WorkspaceIdentity _dummyWorkspaceIdentity;
        private ExperimentEntity _dummyExperimentEntity;
        private IEnumerable<ExperimentEntity> _dummyExperimentEntities;
        private GraphEntity _dummyGraphEntity;
        private string _dummyId;
        private string _dummyRunId;

        private IStoreCollection _fakeIStoreCollection;
        private IGraphStore _fakeIGraphStore;
        private IOptionsMonitor<PipelineRunConfig> _fakeConfig;
        private IWorkspaceResourcesCache _fakeIWorkspaceResourcesCache;
        private WorkspaceDto _dummyWorkspaceDto;
        private WorkspaceMetadata _dummyWorkspaceMetaData;
        private IPipelineRunCreator _fakeIPipelineRunCreator;
        private RunDto _dummyRunDto;
        private IExperimentStore _fakeIExperimentStore;
        private IExperimentSubmitterClientV2 _fakeIExperimentSubmitterClientV2;
        private readonly Exception _dummyException;
        private IContextProvider _fakeIContextProvider;
        private StoreOperationContext _dummyStoreOperationContext;
        private NodeResultEntity _dummyNodeResultEntity;
        private IDatasetStore _fakeIDatasetStore;
        private DatasetEntity _dummyDatasetEntity;
        private IEnumerable<NodeStatus> _dummyNodeStatuses;
        private IPipelineStore _fakeIPipelineStore;
        private PipelineEntity _dumnmyPipelineEntity;
        private IModuleStore _fakeIModuleStore;
        private IEnumerable<ModuleEntity> _dummyModuleEntities;
        private ILogStore _fakeILogStore;
        private Stream _dummyStream;
        private AEVAPipelineRunCreationInfoWithGraph creationInfo;
        private CounterManager _fakeCounterManager;
        private ICredentialServiceClient _fakeICredentialServiceClient;
        private IAccessTokenManager _fakeIAccessTokenManager;
        private IAzureMonitorStatusLogger _azureMonitorStatusEmitter;
        private IAmlGraphDraftStore _fakeGraphDraftStore;
        private IRequestUserProfileHandler _fakeIRequestUserProfileHandler;
        private IHttpContextAccessor _fakeIHttpContextAccessor;

        private string _subscriptionId = "dummySubscriptionId";
        private string _resourceGroupName = "dummyResourceGroupName";
        private string _workspaceName = "dummyWorkspaceName";

        public PipelineRunsControllerTest()
        {
            _repository = new MockRepository(MockBehavior.Loose) { DefaultValue = DefaultValue.Mock, CallBase = true };

            _dummyException = new Exception("dummy test exception");
        }

        [SetUp]
        public void Init()
        {
            _fakeIWorkspaceResourcesCache = _repository.Create<IWorkspaceResourcesCache>().Object;
            _fakeIPipelineRunCreator = _repository.Create<IPipelineRunCreator>().Object;
            _dummyId = Guid.NewGuid().ToString();
            _dummyRunId = Guid.NewGuid().ToString();

            _fakeIStoreCollection = _repository.Create<IStoreCollection>().Object;
            _fakeIExperimentSubmitterClientV2 = _repository.Create<IExperimentSubmitterClientV2>().Object;
            _fakeIContextProvider = _repository.Create<IContextProvider>().Object;

            _fakeIGraphStore = _repository.Create<IGraphStore>().Object;
            _azureMonitorStatusEmitter = _repository.Create<IAzureMonitorStatusLogger>().Object;

            _fakeIDatasetStore = _repository.Create<IDatasetStore>().Object;

            _fakeIPipelineStore = _repository.Create<IPipelineStore>().Object;

            _fakeIModuleStore = _repository.Create<IModuleStore>().Object;

            _fakeILogStore = _repository.Create<ILogStore>().Object;

            _fakeCounterManager = _repository.Create<CounterManager>(
                                string.Empty,
                                _repository.Create<ICounterFactory>().Object).Object;

            _fakeICredentialServiceClient = _repository.Create<ICredentialServiceClient>().Object;

            _fakeIAccessTokenManager = _repository.Create<IAccessTokenManager>().Object;

            _fakeIHttpContextAccessor = _repository.Create<IHttpContextAccessor>().Object;

            _fakeIRequestUserProfileHandler = _repository.Create<IRequestUserProfileHandler>().Object;

            _dummyStream = new MemoryStream();

            _dummyDatasetEntity = new DatasetEntity();

            _fakeGraphDraftStore = _repository.Create<IAmlGraphDraftStore>().Object;

            _dummyNodeStatuses = new List<NodeStatus>()
            {
                new NodeStatus()
                {
                    Id = _dummyId,
                    Status = new DataContracts.TaskStatus()
                }
            };

            _dummyWorkspaceIdentity = new WorkspaceIdentity()
            {
                WorkspaceName = "dummyWSName",
                WorkspaceId = "dummyWSId"
            };
            _dummyExperimentEntity = new ExperimentEntity()
            {
                Id = "dummyExperimentEntityId",
                WorkspaceIdentity = _dummyWorkspaceIdentity,
                Status = new ExperimentStatus()
                {
                    StatusCode = ExperimentStatusCode.Finished
                },
                CredentialPassthrough = true
            };
            _dummyExperimentEntities = new List<ExperimentEntity>
            {
              _dummyExperimentEntity,
              _dummyExperimentEntity
            };

            _dummyGraphEntity = new GraphEntity()
            {
                Edges = new List<GraphEdge>()
                {
                    new GraphEdge()
                    {
                        DestinationInputPort = new PortInfo
                        {
                            GraphPortName = "name1",
                            NodeId = _dummyId,
                            PortName = "11"
                        },
                        SourceOutputPort = new PortInfo
                        {
                            GraphPortName = "name2",
                            NodeId = _dummyId,
                            PortName = "22"
                        }
                    }
                },
                ModuleNodes = new List<GraphModuleNode>() {
                    new GraphModuleNode()
                    {
                        Id = _dummyId,
                        ModuleId = _dummyId
                    }
                },
                SubGraphNodes = new List<GraphReferenceNode>()
                {
                    new GraphReferenceNode()
                    {
                        GraphId = _dummyId,
                        Id = _dummyId
                    }
                },
                DatasetNodes = new List<GraphDatasetNode>()
                {
                    new GraphDatasetNode()
                    {
                        Id = _dummyId,
                        DatasetId = _dummyId
                    }
                }
            };

            _dummyNodeResultEntity = new NodeResultEntity()
            {
                Outputs = new Dictionary<string, NodeOutput>()
            };

            Mock.Get(_fakeIStoreCollection)
                .Setup(
                   stores => stores.GetGraphStore(
                       It.IsAny<ClaimsPrincipal>(),
                       It.IsAny<WorkspaceIdentity>()))
                .Returns(_fakeIGraphStore);

            Mock.Get(_fakeIStoreCollection)
                .Setup(
                   stores => stores.GetPipelineStore(
                       It.IsAny<ClaimsPrincipal>(),
                       It.IsAny<WorkspaceIdentity>()))
                .Returns(_fakeIPipelineStore);

            Mock.Get(_fakeIStoreCollection)
                .Setup(
                   stores => stores.GetModuleStore(
                       It.IsAny<ClaimsPrincipal>(),
                       It.IsAny<WorkspaceIdentity>()))
                .Returns(_fakeIModuleStore);

            Mock.Get(_fakeIStoreCollection)
                .Setup(
                   stores => stores.GetShareableLogStore(
                       It.IsAny<LogType>(),
                       It.IsAny<ClaimsPrincipal>(),
                       It.IsAny<WorkspaceIdentity>()))
                .Returns(_fakeILogStore);

            Mock.Get(_fakeIStoreCollection)
                .Setup(
                   stores => stores.GetAmlGraphDraftStore(
                       It.IsAny<ClaimsPrincipal>(),
                       It.IsAny<WorkspaceIdentity>()))
                .Returns(_fakeGraphDraftStore);

            Mock.Get(_fakeILogStore)
                .Setup(
                   stores => stores.GetLogStreamAsync(
                       It.IsAny<string>(),
                       It.IsAny<string>(),
                       It.IsAny<CreatedBy>()))
                .Returns(Task.FromResult(_dummyStream));

            _dummyModuleEntities = new List<ModuleEntity>()
            {
                new ModuleEntity()
                {
                    FamilyId = Guid.NewGuid().ToString(),
                    Id = "id1",
                    StepType = "stepType"
                }
            };
            Mock.Get(_fakeIModuleStore)
                .Setup(
                   stores => stores.BulkGetIdsAsync(
                       It.IsAny<string>(),
                       It.IsAny<string[]>(),
                       It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(_dummyModuleEntities));

            _dumnmyPipelineEntity = new PipelineEntity()
            {
                Name = "dummyPipelineEntityName",
            };

            Mock.Get(_fakeIPipelineStore)
                .Setup(
                   stores => stores.GetEntityAsync(
                       It.IsAny<string>(),
                       It.IsAny<string>(),
                       It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(_dumnmyPipelineEntity));

            _fakeConfig = Mock.Of<IOptionsMonitor<PipelineRunConfig>>(x => x.CurrentValue == new PipelineRunConfig());

            _fakePipelineRunsController = _repository.Create<PipelineRunsController>(
                            _fakeConfig,
                            _fakeIExperimentSubmitterClientV2,
                            _fakeIPipelineRunCreator,
                            _fakeIStoreCollection,
                            _fakeIContextProvider,
                            _fakeIWorkspaceResourcesCache,
                            _repository.Create<IDataStoreClient>().Object,
                            _fakeICredentialServiceClient,
                            _fakeCounterManager,
                            _repository.Create<IRunHistoryClient>().Object,
                            _fakeIAccessTokenManager,
                            _fakeIHttpContextAccessor,
                            _fakeIRequestUserProfileHandler,
                            null).Object;

            Mock.Get(_fakeIGraphStore)
                .Setup(
                  stores => stores.CreateAsync(
                      It.IsAny<string>(),
                      It.IsAny<GraphEntity>(),
                      It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(_dummyGraphEntity));

            Mock.Get(_fakeIGraphStore)
                .Setup(
                  stores => stores.GetEntityAsync(
                      It.IsAny<string>(),
                      It.IsAny<string>(),
                      It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(_dummyGraphEntity));

            _dummyWorkspaceDto = new WorkspaceDto()
            {
                Id = Guid.NewGuid().ToString(),
                Properties = new WorkspacePropertiesDto()
                {
                    PrivateLinkCount = "5"
                }
            };
            Mock.Get(_fakeIWorkspaceResourcesCache)
                .Setup(
                  stores => stores.GetWorkspaceDtoAsync(
                      It.IsAny<WorkspaceIdentity>(),
                      It.IsAny<CreatedBy>()))
                .Returns(Task.FromResult(_dummyWorkspaceDto));

            Mock.Get(_fakeIWorkspaceResourcesCache)
                .Setup(
                  stores => stores.GetWorkspaceIdAsync(
                      It.IsAny<string>(),
                      It.IsAny<string>(),
                      It.IsAny<string>(),
                      It.IsAny<CreatedBy>()))
                .Returns(Task.FromResult(_dummyId));

            _dummyRunDto = new RunDto()
            {
                RootRunId = Guid.NewGuid().ToString(),
                RunNumber = 10
            };
            Mock.Get(_fakeIPipelineRunCreator)
                .Setup(
                  stores => stores.CreatePipelineRunAsync(
                      It.IsAny<WorkspaceIdentity>(),
                      It.IsAny<string>(),
                      It.IsAny<string>(),
                      It.IsAny<string>(),
                      It.IsAny<string>(),
                      It.IsAny<string>(),
                      It.IsAny<string>(),
                      It.IsAny<string>(),
                      It.IsAny<CreatedBy>(),
                      It.IsAny<IDictionary<string, string>>(),
                      It.IsAny<IDictionary<string, DataSetDefinitionValue>>(),
                      It.IsAny<IDictionary<string, AssetOutputSettings>>(),
                      It.IsAny<IDictionary<string, string>>(),
                      It.IsAny<IDictionary<string, string>>(),
                      It.IsAny<string>(),
                      It.IsAny<string>(),
                      It.IsAny<string>(),
                      It.IsAny<bool>(),
                      It.IsAny<bool>(),
                      It.IsAny<int>(),
                      It.IsAny<int>(),
                      It.IsAny<bool>(),
                      It.IsAny<ComputeSetting>(),
                      It.IsAny<DatastoreSetting>(),
                      It.IsAny<IdentitySetting>(),
                      It.IsAny<int>(),
                      It.IsAny<IEnumerable<string>>(),
                      It.IsAny<string>()))
                .Returns(Task.FromResult(_dummyRunDto));

            _fakeIExperimentStore = _repository.Create<IExperimentStore>().Object;
            Mock.Get(_fakeIStoreCollection)
                .Setup(
                  stores => stores.GetPipelineRunStore(
                      It.IsAny<ClaimsPrincipal>(),
                      It.IsAny<WorkspaceIdentity>()))
                .Returns(_fakeIExperimentStore);

            Mock.Get(_fakeIStoreCollection)
                .Setup(
                  stores => stores.GetDatasetStore(
                      It.IsAny<ClaimsPrincipal>(),
                      It.IsAny<WorkspaceIdentity>()))
                .Returns(_fakeIDatasetStore);

            Mock.Get(_fakeIExperimentStore)
                .Setup(
                  stores => stores.GetEntityAsync(
                      It.IsAny<string>(),
                      It.IsAny<string>(),
                      It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(_dummyExperimentEntity));

            Mock.Get(_fakeIExperimentStore)
                .Setup(
                  stores => stores.UpdateAsync(
                      It.IsAny<string>(),
                      It.IsAny<ExperimentEntity>(),
                      It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(_dummyExperimentEntity));

            Mock.Get(_fakeIExperimentStore)
                .Setup(
                  stores => stores.CreateAsync(
                      It.IsAny<string>(),
                      It.IsAny<ExperimentEntity>(),
                      It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(_dummyExperimentEntity));

            Mock.Get(_fakeIExperimentStore)
                .Setup(
                  stores => stores.BulkGetAsync(
                      It.IsAny<string>(),
                      It.IsAny<string[]>(),
                      It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(_dummyExperimentEntities));

            Mock.Get(_fakeIExperimentStore)
                .Setup(
                    stores => stores.GetNodeResultAsync(
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(_dummyNodeResultEntity));

            Mock.Get(_fakeIExperimentStore)
                .Setup(
                    stores => stores.BulkGetNodesStatusAsync(
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<string[]>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(_dummyNodeStatuses));

            Mock.Get(_fakeIExperimentSubmitterClientV2)
                .Setup(
                  stores => stores.SubmitPipelineRunAsync(
                      It.IsAny<string>(),
                      It.IsAny<string>(),
                      It.IsAny<string>(),
                      It.IsAny<string>(),
                      It.IsAny<WorkspaceIdentity>(),
                      It.IsAny<bool>()))
                .Returns(Task.CompletedTask);

            Mock.Get(_fakeIExperimentSubmitterClientV2)
                .Setup(
                  stores => stores.CancelPipelineRunAsync(
                      It.IsAny<string>(),
                      It.IsAny<string>(),
                      It.IsAny<WorkspaceIdentity>(),
                      false))
                .Returns(Task.CompletedTask);

            _dummyStoreOperationContext = new StoreOperationContext()
            {
                UserName = "dummyUserName",
                Token = "dummyToken"
            };
            Mock.Get(_fakeIContextProvider)
                .Setup(
                  stores => stores.GetStoreOperationContext(
                      It.IsAny<ClaimsPrincipal>()))
                .Returns(_dummyStoreOperationContext);

            Mock.Get(_fakePipelineRunsController)
                .Setup(
                    stores => stores.UpdateAsync(
                    It.IsAny<IUpdatableEntityStore<ExperimentEntity>>(),
                    It.IsAny<ExperimentEntity>(),
                    It.IsAny<Type>(),
                    It.IsAny<bool>()))
                .Returns(Task.FromResult(_dummyExperimentEntity));

            Mock.Get(_fakeIDatasetStore)
                .Setup(
                    stores => stores.GetEntityAsync(
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(_dummyDatasetEntity));

            creationInfo = new AEVAPipelineRunCreationInfoWithGraph()
            {
                CreationInfo = new AEVAPipelineRunCreationInfo()
                {
                    CredentialPassthrough = true
                },
                GraphInterface = new AEVAEntityInterface(),
                Graph = new AEVAGraphEntity()
                {
                    DatasetNodes = new List<AEVAGraphDatasetNode>()
                }
            };

            Mock.Get(_fakePipelineRunsController)
                .Setup(controller => controller.GetWorkspaceId())
                .Returns(_dummyId);

            _dummyWorkspaceMetaData =
                new WorkspaceMetadata(Guid.NewGuid(), string.Empty, string.Empty, Guid.NewGuid())
                {
                    WorkspaceProperties = new Dictionary<string, string>
                    {
                        { "privateLinkCount", "5" }
                    }
                };
            Mock.Get(_fakePipelineRunsController)
                .Setup(controller => controller.GetWorkspaceMetadata())
                .Returns(_dummyWorkspaceMetaData);
        }

        [Test]
        [Category("SubmitPipelineRunInternalAsync")]
        public async Task TestSubmitPipelineRunInternalAsync_Normal_Execution()
        {
            MethodInfo method_SubmitPipelineRunInternalAsync = typeof(PipelineRunsController).GetMethod("SubmitPipelineRunInternalAsync", BindingFlags.Instance | BindingFlags.NonPublic);

            var task = (Task)method_SubmitPipelineRunInternalAsync.Invoke(_fakePipelineRunsController, new object[] {
                _dummyWorkspaceIdentity,
                _dummyExperimentEntity,
                _dummyGraphEntity,
                _dummyId,
                _dummyRunId,
                null,
                false,
                LogTelemetrySourceEnum.Others,
                false,
                null,
                null,
                null,
                null,
                null,
                null,
                null
            });

            await task.ConfigureAwait(false);

            Mock.Get(_fakeIExperimentSubmitterClientV2)
                .Verify(
                  stores => stores.SubmitPipelineRunAsync(
                      It.Is<string>(id => id == _dummyExperimentEntity.Id),
                      It.IsAny<string>(),
                      It.IsAny<string>(),
                      It.Is<string>(token => token == "tokenplaceholder"),
                      It.Is<WorkspaceIdentity>(id => id == _dummyWorkspaceIdentity),
                      It.IsAny<bool>()),
                  Times.Once);
        }

        [Test]
        [Category("ValidateGraphPorts")]
        public void TestValidateGraphPorts_DuplicatedNames_Exception()
        {
            MethodInfo method_ValidateGraphPorts = typeof(PipelineRunsController).GetMethod("ValidateGraphPorts", BindingFlags.Static | BindingFlags.NonPublic);

            var graph = new VisualGraph
            {
                Graph = new GraphEntity()
                {
                    Edges = new List<GraphEdge>()
                    {
                       new GraphEdge()
                       {
                         DestinationInputPort = new PortInfo
                         {
                           GraphPortName = "name1"
                         }
                       },
                       new GraphEdge()
                       {
                         DestinationInputPort = new PortInfo
                         {
                           GraphPortName = "name1"
                         }
                       },
                       new GraphEdge()
                       {
                         DestinationInputPort = new PortInfo
                         {
                           GraphPortName = "name2"
                         }
                       }
                    }
                },
                Layout = new GraphLayout(),
            };

            var actualExp = Assert.Throws<TargetInvocationException>(() => method_ValidateGraphPorts.Invoke(_fakePipelineRunsController, new object[] { graph }));
            Assert.AreEqual("PipelineRun contains duplicate PipelineRun output names, which is not allowed.", actualExp.InnerException.Message);
        }

        [Test]
        [Category("CreateUnsubmittedPipelineRunWithGraphAsync")]
        public async Task TestCreateUnsubmittedPipelineRunWithGraphAsync_Normal_Return()
        {
            AEVAPipelineRunEntity actualRet = await _fakePipelineRunsController.CreateUnsubmittedPipelineRunWithGraphAsync(
                 subscriptionId: "dummySubscriptionId",
                 resourceGroupName: "dummyResourceGroupName",
                 workspaceName: "dummyWorkspaceName",
                 experimentName: "dummyExperimentName",
                 creationInfoWithGraph: creationInfo).ConfigureAwait(false);

            var expectedRet = ContractConverter.Convert<ExperimentEntity, AEVAPipelineRunEntity>(_dummyExperimentEntity, ContractConverter.GetPipelineStatusCustomConverters());
            expectedRet.Should().BeEquivalentTo(actualRet);
        }

        [Test]
        [Category("CreateUnsubmittedPipelineRunWithGraphAsync")]
        public void TestCreateUnsubmittedPipelineRunWithGraphAsync_CreatePipelineFailed_Exception()
        {
            Mock.Get(_fakeIExperimentStore)
                .Setup(
                  stores => stores.CreateAsync(
                      It.IsAny<string>(),
                      It.IsAny<ExperimentEntity>(),
                      It.IsAny<WorkspaceIdentity>()))
                .Throws(_dummyException);

            var actualExp = Assert.ThrowsAsync<Exception>(async () => await _fakePipelineRunsController.CreateUnsubmittedPipelineRunWithGraphAsync(
                 subscriptionId: "dummySubscriptionId",
                 resourceGroupName: "dummyResourceGroupName",
                 workspaceName: "dummyWorkspaceName",
                 experimentName: "dummyExperimentName",
                 creationInfoWithGraph: creationInfo).ConfigureAwait(false));

            Assert.AreEqual(_dummyException.Message, actualExp.Message);
        }

        [Test]
        [Category("GetPipelineRunAsync")]
        public async Task TestGetPipelineRunAsync_Normal_Return()
        {
            AEVAPipelineRunEntity actualRet = await _fakePipelineRunsController.GetPipelineRunAsync(
                 subscriptionId: "dummySubscriptionId",
                 resourceGroupName: "dummyResourceGroupName",
                 workspaceName: "dummyWorkspaceName",
                 pipelineRunId: _dummyId).ConfigureAwait(false);

            var expectedRet = ContractConverter.Convert<ExperimentEntity, AEVAPipelineRunEntity>(_dummyExperimentEntity, ContractConverter.GetPipelineStatusCustomConverters());
            expectedRet.Should().BeEquivalentTo(actualRet);
        }

        [Test]
        [Category("GetPipelineRunAsync")]
        public void TestGetPipelineRunAsync_CreatePipelineFailed_Exception()
        {
            Mock.Get(_fakeIExperimentStore)
                .Setup(
                  stores => stores.GetEntityAsync(
                      It.IsAny<string>(),
                      It.IsAny<string>(),
                      It.IsAny<WorkspaceIdentity>()))
                .Throws(_dummyException);

            var actualExp = Assert.ThrowsAsync<Exception>(async () => await _fakePipelineRunsController.GetPipelineRunAsync(
                 subscriptionId: "dummySubscriptionId",
                 resourceGroupName: "dummyResourceGroupName",
                 workspaceName: "dummyWorkspaceName",
                 pipelineRunId: _dummyId).ConfigureAwait(false));

            Assert.AreEqual(_dummyException.Message, actualExp.Message);
        }

        [Test]
        [Category("BulkGetPipelineRunAsync")]
        public async Task TestBulkGetPipelineRunAsync_Normal_Return()
        {
            var actualRet = await _fakePipelineRunsController.BulkGetPipelineRunAsync(
                 subscriptionId: "dummySubscriptionId",
                 resourceGroupName: "dummyResourceGroupName",
                 workspaceName: "dummyWorkspaceName",
                 pipelineRunIds: new string[] { _dummyId, _dummyId }).ConfigureAwait(false);

            Assert.AreEqual(_dummyExperimentEntities.Count(), actualRet.Count());
            for (int i = 0; i < _dummyExperimentEntities.Count(); i++)
            {
                var expectedEntity = ContractConverter.Convert<ExperimentEntity, AEVAPipelineRunEntity>(_dummyExperimentEntities.ElementAt(i), ContractConverter.GetPipelineStatusCustomConverters());
                expectedEntity.Should().BeEquivalentTo(actualRet.ElementAt(i));
            }
        }

        [Test]
        [Category("BulkGetPipelineRunAsync")]
        public void TestGetPipelineRunAsync_BulkGetAsyncFailed_Exception()
        {
            Mock.Get(_fakeIExperimentStore)
                .Setup(
                  stores => stores.BulkGetAsync(
                      It.IsAny<string>(),
                      It.IsAny<string[]>(),
                      It.IsAny<WorkspaceIdentity>()))
                .Throws(_dummyException);

            var actualExp = Assert.ThrowsAsync<Exception>(async () => await _fakePipelineRunsController.BulkGetPipelineRunAsync(
                 subscriptionId: "dummySubscriptionId",
                 resourceGroupName: "dummyResourceGroupName",
                 workspaceName: "dummyWorkspaceName",
                 pipelineRunIds: new string[] { _dummyId, _dummyId }).ConfigureAwait(false));

            Assert.AreEqual(_dummyException.Message, actualExp.Message);
        }

        [Test]
        [Category("GetDeepPipelineVisualGraphWithEntityInterfaceAsync")]
        public async Task TestGetDeepPipelineVisualGraphWithEntityInterfaceAsync_Normal_Return()
        {
            var actualRet = await _fakePipelineRunsController.GetDeepPipelineVisualGraphWithEntityInterfaceAsync(
                 subscriptionId: "dummySubscriptionId",
                 resourceGroupName: "dummyResourceGroupName",
                 workspaceName: "dummyWorkspaceName",
                 pipelineRunId: _dummyId,
                 populateRunconfig: false).ConfigureAwait(false);

            var expectedRet = ContractConverter.Convert<ExperimentEntity, AEVAPipelineRunEntity>(_dummyExperimentEntity, ContractConverter.GetPipelineStatusCustomConverters());
            expectedRet.Should().BeEquivalentTo(actualRet.PipelineRunEntity);
        }

        [Test]
        [Category("GetDeepPipelineVisualGraphWithEntityInterfaceAsync")]
        public void TestGetDeepPipelineVisualGraphWithEntityInterfaceAsync_GetGraphFailed_Exception()
        {
            Mock.Get(_fakeIStoreCollection)
                .Setup(
                   stores => stores.GetGraphStore(
                       It.IsAny<ClaimsPrincipal>(),
                       It.IsAny<WorkspaceIdentity>()))
                .Throws(_dummyException);

            var actualExp = Assert.ThrowsAsync<Exception>(async () => await _fakePipelineRunsController.GetDeepPipelineVisualGraphWithEntityInterfaceAsync(
                 subscriptionId: "dummySubscriptionId",
                 resourceGroupName: "dummyResourceGroupName",
                 workspaceName: "dummyWorkspaceName",
                 pipelineRunId: _dummyId,
                 populateRunconfig: false).ConfigureAwait(false));

            Assert.AreEqual(_dummyException.Message, actualExp.Message);
        }

        [Test]
        [Category("GetPipelineRunGraphAsync")]
        public async Task TestGetPipelineRunGraphAsync_Normal_Return()
        {
            var actualRet = await _fakePipelineRunsController.GetPipelineRunGraphAsync(
                 subscriptionId: "dummySubscriptionId",
                 resourceGroupName: "dummyResourceGroupName",
                 workspaceName: "dummyWorkspaceName",
                 pipelineRunId: _dummyId).ConfigureAwait(false);

            var expectedRet = ContractConverter.Convert<GraphEntity, AEVAGraphEntity>(_dummyGraphEntity);
            expectedRet.Should().BeEquivalentTo(actualRet);
        }

        [Test]
        [Category("GetPipelineRunGraphAsync")]
        public void TestGetPipelineRunGraphAsync_GetGraphFailed_Exception()
        {
            Mock.Get(_fakeIGraphStore)
                .Setup(
                  stores => stores.GetEntityAsync(
                      It.IsAny<string>(),
                      It.IsAny<string>(),
                      It.IsAny<WorkspaceIdentity>()))
                .Throws(_dummyException);

            var actualExp = Assert.ThrowsAsync<Exception>(async () => await _fakePipelineRunsController.GetPipelineRunGraphAsync(
                 subscriptionId: "dummySubscriptionId",
                 resourceGroupName: "dummyResourceGroupName",
                 workspaceName: "dummyWorkspaceName",
                 pipelineRunId: _dummyId).ConfigureAwait(false));

            Assert.AreEqual(_dummyException.Message, actualExp.Message);
        }

        [Test]
        [Category("CancelPipelineRunAsync")]
        public async Task TestCancelPipelineRunAsync_Normal_Return()
        {
            await _fakePipelineRunsController.CancelPipelineRunAsync(
                 subscriptionId: _subscriptionId,
                 resourceGroupName: _resourceGroupName,
                 workspaceName: _workspaceName,
                 pipelineRunId: _dummyId).ConfigureAwait(false);

            Mock.Get(_fakeIExperimentSubmitterClientV2)
                 .Verify(
                   stores => stores.CancelPipelineRunAsync(
                       It.Is<string>(id => id == _dummyId),
                       It.Is<string>(token => token == _dummyStoreOperationContext.Token),
                       It.Is<WorkspaceIdentity>(
                           ws => ws.SubscriptionId == _subscriptionId &&
                           ws.ResourceGroupName == _resourceGroupName &&
                           ws.WorkspaceName == _workspaceName &&
                           ws.WorkspaceId == _dummyId),
                       false),
                   Times.Once);
        }

        [Test]
        [Category("CancelPipelineRunAsync")]
        public void TestCancelPipelineRunAsync_GetGraphFailed_Exception()
        {
            Mock.Get(_fakeIExperimentSubmitterClientV2)
                .Setup(
                  stores => stores.CancelPipelineRunAsync(
                      It.IsAny<string>(),
                      It.IsAny<string>(),
                      It.IsAny<WorkspaceIdentity>(),
                      false))
                .Throws(_dummyException);

            var actualExp = Assert.ThrowsAsync<Exception>(async () => await _fakePipelineRunsController.CancelPipelineRunAsync(
                 subscriptionId: _subscriptionId,
                 resourceGroupName: _resourceGroupName,
                 workspaceName: _workspaceName,
                 pipelineRunId: _dummyId).ConfigureAwait(false));

            Assert.AreEqual(_dummyException.Message, actualExp.Message);
        }

        [Test]
        [Category("UpdateUnsubmittedPipelineRunAsync")]
        public void TestUpdateUnsubmittedPipelineRunAsync_IdNotMatch_Exception()
        {
            var aevaPipelineRunEntity = new AEVAPipelineRunEntity()
            {
                Id = Guid.NewGuid().ToString()
            };

            Assert.AreNotEqual(_dummyId, aevaPipelineRunEntity.Id);

            var actualExp = Assert.ThrowsAsync<ArgumentException>(async () => await _fakePipelineRunsController.UpdateUnsubmittedPipelineRunAsync(
                 subscriptionId: _subscriptionId,
                 resourceGroupName: _resourceGroupName,
                 workspaceName: _workspaceName,
                 pipelineRunId: _dummyId,
                 updated: aevaPipelineRunEntity).ConfigureAwait(false));

            StringAssert.Contains("pipelineRunId should be equal to updated.PipelineRunId", actualExp.Message);
        }

        [Test]
        [Category("UpdateUnsubmittedPipelineRunAsync")]
        public async Task TestUpdateUnsubmittedPipelineRunAsync_Normal_Return()
        {
            var aevaPipelineRunEntity = new AEVAPipelineRunEntity()
            {
                Id = _dummyId
            };

            Assert.AreEqual(_dummyId, aevaPipelineRunEntity.Id);

            var actualRet = await _fakePipelineRunsController.UpdateUnsubmittedPipelineRunAsync(
                 subscriptionId: _subscriptionId,
                 resourceGroupName: _resourceGroupName,
                 workspaceName: _workspaceName,
                 pipelineRunId: _dummyId,
                 updated: aevaPipelineRunEntity).ConfigureAwait(false);

            var expectedRet = ContractConverter.Convert<ExperimentEntity, AEVAPipelineRunEntity>(_dummyExperimentEntity, ContractConverter.GetPipelineStatusCustomConverters());
            expectedRet.Should().BeEquivalentTo(actualRet);
        }

        [Test]
        [Category("UpdateUnsubmittedPipelineRunAsync")]
        public void TestUpdateUnsubmittedPipelineRunAsync_UpdateFailed_Exception()
        {
            var aevaPipelineRunEntity = new AEVAPipelineRunEntity()
            {
                Id = _dummyId
            };

            Assert.AreEqual(_dummyId, aevaPipelineRunEntity.Id);

            Mock.Get(_fakePipelineRunsController)
                .Setup(
                    stores => stores.UpdateAsync(
                    It.IsAny<IUpdatableEntityStore<ExperimentEntity>>(),
                    It.IsAny<ExperimentEntity>(),
                    It.IsAny<Type>(),
                    It.IsAny<bool>()))
                 .Throws(_dummyException);

            var actualExp = Assert.ThrowsAsync<Exception>(async () => await _fakePipelineRunsController.UpdateUnsubmittedPipelineRunAsync(
                 subscriptionId: _subscriptionId,
                 resourceGroupName: _resourceGroupName,
                 workspaceName: _workspaceName,
                 pipelineRunId: _dummyId,
                 updated: aevaPipelineRunEntity).ConfigureAwait(false));

            Assert.AreEqual(_dummyException.Message, actualExp.Message);
        }

        [Test]
        [Category("GetOutputsAsync")]
        public void TestGetOutputsAsync_FailedToGetNodeResult_Exception()
        {
            Assert.True(_dummyNodeStatuses.Count() == 1);
            _dummyNodeStatuses.ElementAt(0).Status = new DataContracts.TaskStatus()
            {
                StatusCode = TaskStatusCode.Finished
            };

            // test the logic when relations != null && GetNodeStatusCodeAsync returns status == Finished
            var exp1 = new Exception("failed at GetNodeResultAsync");
            Mock.Get(_fakeIExperimentStore)
                .Setup(
                    stores => stores.GetNodeResultAsync(
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Throws(exp1);

            var actualExp = Assert.ThrowsAsync<Exception>(async () => await _fakePipelineRunsController.GetOutputsAsync(
                             subscriptionId: _subscriptionId,
                             resourceGroupName: _resourceGroupName,
                             workspaceName: _workspaceName,
                             pipelineRunId: _dummyId,
                             nodeIdPath: new string[] { _dummyId }).ConfigureAwait(false));
            Assert.AreEqual(exp1.Message, actualExp.Message);
        }

        [Test]
        [Category("GetOutputsAsync")]
        public async Task TestGetOutputsAsync_SubGraphNode()
        {
            Assert.True(_dummyNodeStatuses.Count() == 1);
            _dummyNodeStatuses.ElementAt(0).Status = new DataContracts.TaskStatus()
            {
                StatusCode = TaskStatusCode.Finished
            };

            Mock.Get(_fakeIExperimentStore)
                .Setup(
                    stores => stores.GetNodeResultAsync(
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(
                    new NodeResultEntity()
                    {
                        Id = _dummyId,
                        Outputs = new Dictionary<string, NodeOutput>()
                        {
                            { "output", new NodeOutput() { DataTypeId = "dataType" } }
                        }
                    }));

            //  This should just fetch the node result for the subgraph node and skip the nested output logic
            _dummyExperimentEntity.PreserveSubGraphs = true;
            IDictionary<string, AEVANodeOutput> outputs = await _fakePipelineRunsController.GetOutputsAsync(
                subscriptionId: _subscriptionId,
                resourceGroupName: _resourceGroupName,
                workspaceName: _workspaceName,
                pipelineRunId: _dummyId,
                nodeIdPath: new string[] { _dummyId }).ConfigureAwait(false);

            Assert.IsNotNull(outputs);
            Assert.AreEqual(outputs.Count, 1);
            Assert.AreEqual(outputs["output"].DataTypeId, "dataType");
        }

        [Test]
        [Category("GetOutputsAsync")]
        public async Task TestGetOutputsAsync_NodeNotFinished_ReturnEmpty()
        {

            var actualRet = await _fakePipelineRunsController.GetOutputsAsync(
                             subscriptionId: _subscriptionId,
                             resourceGroupName: _resourceGroupName,
                             workspaceName: _workspaceName,
                             pipelineRunId: _dummyId,
                             nodeIdPath: new string[] { _dummyId }).ConfigureAwait(false);
            Assert.AreEqual(0, actualRet.Count());
        }

        [Test]
        [Category("GetPipelineRunOutputAsync")]
        public void TestGetPipelineRunOutputAsync_EdgetDestGraphPortNameNotMatch_ArgumentException()
        {
            var pipelineRunOutputName = "dummyPipelineOutputName";
            GraphEdge edge = _dummyGraphEntity.Edges.FirstOrDefault(e => string.Equals(e.DestinationInputPort.GraphPortName, pipelineRunOutputName, StringComparison.InvariantCulture));
            Assert.IsNull(edge);
            var actualExp = Assert.ThrowsAsync<ArgumentException>(async () => await _fakePipelineRunsController.GetPipelineRunOutputAsync(
                             subscriptionId: _subscriptionId,
                             resourceGroupName: _resourceGroupName,
                             workspaceName: _workspaceName,
                             pipelineRunId: _dummyId,
                             pipelineRunOutputName: pipelineRunOutputName).ConfigureAwait(false));
            StringAssert.Contains($"PipelineRun output with name {pipelineRunOutputName} does not exist.", actualExp.Message);
        }

        [Test]
        [Category("GetPipelineRunOutputAsync")]
        public void TestGetPipelineRunOutputAsync_OutputNodeNotExist_ArgumentException()
        {
            // avoid the argumentException
            Assert.True(_dummyGraphEntity.Edges.Count() > 0);
            var pipelineRunOutputName = _dummyGraphEntity.Edges.ElementAt(0).DestinationInputPort.GraphPortName;
            GraphEdge edge = _dummyGraphEntity.Edges.FirstOrDefault(e => string.Equals(e.DestinationInputPort.GraphPortName, pipelineRunOutputName, StringComparison.InvariantCulture));
            Assert.IsNotNull(edge);

            AEVATaskStatus dummyAEVATaskStatus = new AEVATaskStatus()
            {
                StatusCode = AEVATaskStatusCode.Finished
            };

            Mock.Get(_fakePipelineRunsController)
                .Setup(
                    stores => stores.GetNodeStatusAsync(
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<string[]>()))
                .Returns(Task.FromResult(dummyAEVATaskStatus));

            _dummyNodeResultEntity = new NodeResultEntity()
            {
                Outputs = new Dictionary<string, NodeOutput>()
            };

            var nodePortName = _dummyGraphEntity.Edges.ElementAt(0).SourceOutputPort.PortName;
            Assert.False(_dummyNodeResultEntity.Outputs.ContainsKey(nodePortName));

            var actualExp = Assert.ThrowsAsync<ArgumentException>(async () => await _fakePipelineRunsController.GetPipelineRunOutputAsync(
                             subscriptionId: _subscriptionId,
                             resourceGroupName: _resourceGroupName,
                             workspaceName: _workspaceName,
                             pipelineRunId: _dummyId,
                             pipelineRunOutputName: pipelineRunOutputName).ConfigureAwait(false));
            StringAssert.Contains($"PipelineRun output with name {pipelineRunOutputName} references node output: {nodePortName} which does not exist.", actualExp.Message);
        }

        [Test]
        [Category("GetPipelineRunOutputAsync")]
        public async Task TestGetPipelineRunOutputAsync_Nomral_Execution()
        {
            // avoid the argumentException
            Assert.True(_dummyGraphEntity.Edges.Count() > 0);
            var pipelineRunOutputName = _dummyGraphEntity.Edges.ElementAt(0).DestinationInputPort.GraphPortName;
            GraphEdge edge = _dummyGraphEntity.Edges.FirstOrDefault(e => string.Equals(e.DestinationInputPort.GraphPortName, pipelineRunOutputName, StringComparison.InvariantCulture));
            Assert.IsNotNull(edge);

            AEVATaskStatus dummyAEVATaskStatus = new AEVATaskStatus()
            {
                StatusCode = AEVATaskStatusCode.Finished
            };

            Mock.Get(_fakePipelineRunsController)
                .Setup(
                    stores => stores.GetNodeStatusAsync(
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<string[]>()))
                .Returns(Task.FromResult(dummyAEVATaskStatus));

            var dummyNodeOutput = new NodeOutput()
            {
                DataTypeId = "dummyDataTypeId",
                DataLocation = new DataLocation()
                {
                    StorageId = "dummyStorageId",
                    Uri = "dummyUri",
                    StorageType = DataLocationStorageType.AzureBlob
                }
            };
            _dummyNodeResultEntity.Outputs[_dummyGraphEntity.Edges.ElementAt(0).SourceOutputPort.PortName] = dummyNodeOutput;

            Assert.True(_dummyNodeResultEntity.Outputs.ContainsKey(_dummyGraphEntity.Edges.ElementAt(0).SourceOutputPort.PortName));

            var actualRet = await _fakePipelineRunsController.GetPipelineRunOutputAsync(
                             subscriptionId: _subscriptionId,
                             resourceGroupName: _resourceGroupName,
                             workspaceName: _workspaceName,
                             pipelineRunId: _dummyId,
                             pipelineRunOutputName: pipelineRunOutputName).ConfigureAwait(false);
            var expectedRet = ContractConverter.Convert<NodeOutput, AEVANodeOutput>(dummyNodeOutput);
            expectedRet.Should().BeEquivalentTo(actualRet);
        }

        [Test]
        [Category("GetPipelineRunOutputAsync")]
        public void TestGetPipelineRunOutputAsync__Execution()
        {
            // avoid the argumentException
            Assert.True(_dummyGraphEntity.Edges.Count() > 0);
            var pipelineRunOutputName = _dummyGraphEntity.Edges.ElementAt(0).DestinationInputPort.GraphPortName;
            GraphEdge edge = _dummyGraphEntity.Edges.FirstOrDefault(e => string.Equals(e.DestinationInputPort.GraphPortName, pipelineRunOutputName, StringComparison.InvariantCulture));
            Assert.IsNotNull(edge);

            AEVATaskStatus dummyAEVATaskStatus = new AEVATaskStatus()
            {
                StatusCode = AEVATaskStatusCode.Finished
            };

            Mock.Get(_fakePipelineRunsController)
                .Setup(
                    stores => stores.GetNodeStatusAsync(
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<string[]>()))
                .Returns(Task.FromResult(dummyAEVATaskStatus));

            var dummyNodeOutput = new NodeOutput()
            {
                DataTypeId = "dummyDataTypeId",
                DataLocation = new DataLocation()
                {
                    StorageId = "dummyStorageId",
                    Uri = "dummyUri",
                    StorageType = DataLocationStorageType.AzureBlob
                }
            };
            _dummyNodeResultEntity.Outputs[_dummyGraphEntity.Edges.ElementAt(0).SourceOutputPort.PortName] = dummyNodeOutput;

            Assert.True(_dummyNodeResultEntity.Outputs.ContainsKey(_dummyGraphEntity.Edges.ElementAt(0).SourceOutputPort.PortName));

            var exp1 = new Exception("failed at GetNodeResultAsync");
            Mock.Get(_fakeIGraphStore)
                .Setup(
                  stores => stores.GetEntityAsync(
                      It.IsAny<string>(),
                      It.IsAny<string>(),
                      It.IsAny<WorkspaceIdentity>()))
                .Throws(exp1);

            var actualExp = Assert.ThrowsAsync<Exception>(async () => await _fakePipelineRunsController.GetPipelineRunOutputAsync(
                             subscriptionId: _subscriptionId,
                             resourceGroupName: _resourceGroupName,
                             workspaceName: _workspaceName,
                             pipelineRunId: _dummyId,
                             pipelineRunOutputName: pipelineRunOutputName).ConfigureAwait(false));
            Assert.AreEqual(exp1.Message, actualExp.Message);
        }

        [Test]
        [Category("GetNodeStatusAsync")]
        public async Task TestGetNodeStatusAsync_NodeIdMatch_Return()
        {
            var nodeId = _dummyId;
            Assert.True(_dummyNodeStatuses.Count() > 0);
            Assert.True(_dummyGraphEntity.SubGraphNodes.Count() > 0);
            Assert.True(_dummyGraphEntity.ModuleNodes.Count() > 0);
            Assert.AreEqual(nodeId, _dummyNodeStatuses.ElementAt(0).Id);
            Assert.AreEqual(nodeId, _dummyGraphEntity.SubGraphNodes.ElementAt(0).Id);
            Assert.AreEqual(nodeId, _dummyGraphEntity.ModuleNodes.ElementAt(0).Id);
            var actualRet = await _fakePipelineRunsController.GetNodeStatusAsync(
                             subscriptionId: _subscriptionId,
                             resourceGroupName: _resourceGroupName,
                             workspaceName: _workspaceName,
                             pipelineRunId: _dummyId,
                             nodeIdPath: new string[] { nodeId }).ConfigureAwait(false);

            var expectedRet = ContractConverter.Convert<DataContracts.TaskStatus, AEVATaskStatus>(_dummyNodeStatuses.ElementAt(0).Status, ControllerHelper.GetTaskStatusCustomConverters());
            expectedRet.Should().BeEquivalentTo(actualRet);
        }

        [Test]
        [Category("GetNodeStatusAsync")]
        public async Task TestGetNodeStatusAsync_SubGraphNodeIdMatch_Return()
        {
            var nodeId = _dummyId;
            Assert.True(_dummyNodeStatuses.Count() > 0);
            Assert.True(_dummyGraphEntity.SubGraphNodes.Count() > 0);
            Assert.True(_dummyGraphEntity.ModuleNodes.Count() > 0);
            Assert.AreEqual(nodeId, _dummyNodeStatuses.ElementAt(0).Id);

            //  This should return the status for the subgraph node
            Assert.AreEqual(nodeId, _dummyGraphEntity.SubGraphNodes.ElementAt(0).Id);
            _dummyGraphEntity.ModuleNodes.ElementAt(0).Id = "DoesNotMatch";
            _dummyExperimentEntity.PreserveSubGraphs = true;

            var actualRet = await _fakePipelineRunsController.GetNodeStatusAsync(
                             subscriptionId: _subscriptionId,
                             resourceGroupName: _resourceGroupName,
                             workspaceName: _workspaceName,
                             pipelineRunId: _dummyId,
                             nodeIdPath: new string[] { nodeId }).ConfigureAwait(false);

            var expectedRet = ContractConverter.Convert<DataContracts.TaskStatus, AEVATaskStatus>(_dummyNodeStatuses.ElementAt(0).Status, ControllerHelper.GetTaskStatusCustomConverters());
            expectedRet.Should().BeEquivalentTo(actualRet);
        }

        [Test]
        [Category("GetNodeStatusAsync")]
        public async Task TestGetNodeStatusAsync_NodeIdNotMatch_Return()
        {
            var nodeId = Guid.NewGuid().ToString();
            Assert.True(_dummyNodeStatuses.Count() > 0);
            Assert.True(_dummyGraphEntity.SubGraphNodes.Count() > 0);
            Assert.True(_dummyGraphEntity.ModuleNodes.Count() > 0);
            Assert.AreNotEqual(nodeId, _dummyNodeStatuses.ElementAt(0).Id);
            Assert.AreNotEqual(nodeId, _dummyGraphEntity.SubGraphNodes.ElementAt(0).Id);
            Assert.AreNotEqual(nodeId, _dummyGraphEntity.ModuleNodes.ElementAt(0).Id);

            _dummyGraphEntity.SubGraphNodes.ElementAt(0).Id = nodeId;

            var actualRet = await _fakePipelineRunsController.GetNodeStatusAsync(
                             subscriptionId: _subscriptionId,
                             resourceGroupName: _resourceGroupName,
                             workspaceName: _workspaceName,
                             pipelineRunId: _dummyId,
                             nodeIdPath: new string[] { nodeId }).ConfigureAwait(false);

            var expectedRet = ContractConverter.Convert<DataContracts.TaskStatus, AEVATaskStatus>(StatusRoller.RollStatus(_dummyNodeStatuses.Select(node => node.Status), _dummyExperimentEntity), ControllerHelper.GetTaskStatusCustomConverters());
            expectedRet.Should().BeEquivalentTo(actualRet);
        }

        [Test]
        [Category("GetNodeStatusAsync")]
        public void TestGetNodeStatusAsync_FailedToGetNodeStatus_Exception()
        {
            var nodeId = Guid.NewGuid().ToString();
            Assert.True(_dummyNodeStatuses.Count() > 0);
            Assert.True(_dummyGraphEntity.SubGraphNodes.Count() > 0);
            Assert.True(_dummyGraphEntity.ModuleNodes.Count() > 0);
            Assert.AreNotEqual(nodeId, _dummyNodeStatuses.ElementAt(0).Id);
            Assert.AreNotEqual(nodeId, _dummyGraphEntity.SubGraphNodes.ElementAt(0).Id);
            Assert.AreNotEqual(nodeId, _dummyGraphEntity.ModuleNodes.ElementAt(0).Id);

            _dummyGraphEntity.SubGraphNodes.ElementAt(0).Id = nodeId;

            var exp = new Exception("failed at GetNodeResultAsync");
            Mock.Get(_fakeIExperimentStore)
                .Setup(
                    stores => stores.BulkGetNodesStatusAsync(
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<string[]>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Throws(exp);

            var actualExp = Assert.ThrowsAsync<Exception>(async () => await _fakePipelineRunsController.GetNodeStatusAsync(
                             subscriptionId: _subscriptionId,
                             resourceGroupName: _resourceGroupName,
                             workspaceName: _workspaceName,
                             pipelineRunId: _dummyId,
                             nodeIdPath: new string[] { nodeId }).ConfigureAwait(false));

            Assert.AreEqual(exp.Message, actualExp.Message);
        }

        [Test]
        [Category("SubmitSavedPipelineRunHelperAsync")]
        public void TestSubmitSavedPipelineRunHelperAsync_SubmitFailed_ServiceInvocationException()
        {
            MethodInfo method_SubmitSavedPipelineRunHelperAsync = typeof(PipelineRunsController).GetMethod("SubmitSavedPipelineRunHelperAsync", BindingFlags.Instance | BindingFlags.NonPublic);

            var parentId = Guid.NewGuid().ToString();

            var exp = new Exception("failed at SubmitPipelineRunInternalAsync");
            var expService = new ServiceInvocationException("opName", "calledService", new System.Net.Http.HttpMethod("Get"), exp);
            Mock.Get(_fakePipelineRunsController)
                .Setup(
                    stores => stores.SubmitPipelineRunInternalAsync(
                    It.IsAny<WorkspaceIdentity>(),
                    It.IsAny<ExperimentEntity>(),
                    It.IsAny<GraphEntity>(),
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<bool>(),
                    It.IsAny<LogTelemetrySourceEnum>(),
                    It.IsAny<bool?>(),
                    It.IsAny<int?>(),
                    It.IsAny<int?>(),
                    It.IsAny<bool>(),
                    It.IsAny<ComputeSetting>(),
                    It.IsAny<DatastoreSetting>(),
                    It.IsAny<IdentitySetting>(),
                    It.IsAny<int?>()))
                .Throws(expService);

            var task = (Task)method_SubmitSavedPipelineRunHelperAsync.Invoke(_fakePipelineRunsController, new object[] {
                      _subscriptionId,
                      _resourceGroupName,
                      _workspaceName,
                      _dummyId,
                      parentId,
                      _dummyRunId,
                      null,
                      false,
                      LogTelemetrySourceEnum.Others,
                      false,
                      null,
                      null,
                      null,
                      null,
                      null,
                      null,
                      null
            });

            var actualExp = Assert.ThrowsAsync<ServiceInvocationException>(async () => await task.ConfigureAwait(false));
            Assert.AreEqual(exp.Message, actualExp.InnerException.Message);
        }

        [Test]
        [Category("SubmitSavedPipelineRunHelperAsync")]
        public void TestSubmitSavedPipelineRunHelperAsync_SubmitFailed_Exception()
        {
            MethodInfo method_SubmitSavedPipelineRunHelperAsync = typeof(PipelineRunsController).GetMethod("SubmitSavedPipelineRunHelperAsync", BindingFlags.Instance | BindingFlags.NonPublic);

            var parentId = Guid.NewGuid().ToString();

            var exp = new Exception("failed at SubmitPipelineRunInternalAsync");
            Mock.Get(_fakePipelineRunsController)
                .Setup(
                    stores => stores.SubmitPipelineRunInternalAsync(
                    It.IsAny<WorkspaceIdentity>(),
                    It.IsAny<ExperimentEntity>(),
                    It.IsAny<GraphEntity>(),
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<bool>(),
                    It.IsAny<LogTelemetrySourceEnum>(),
                    It.IsAny<bool?>(),
                    It.IsAny<int?>(),
                    It.IsAny<int?>(),
                    It.IsAny<bool>(),
                    It.IsAny<ComputeSetting>(),
                    It.IsAny<DatastoreSetting>(),
                    It.IsAny<IdentitySetting>(),
                    It.IsAny<int?>()))
                .Throws(exp);

            var task = (Task)method_SubmitSavedPipelineRunHelperAsync.Invoke(_fakePipelineRunsController, new object[] {
                      _subscriptionId,
                      _resourceGroupName,
                      _workspaceName,
                      _dummyId,
                      parentId,
                      _dummyRunId,
                      null,
                      false,
                      LogTelemetrySourceEnum.Others,
                      false,
                      null,
                      null,
                      null,
                      null,
                      null,
                      null,
                      null
            });

            var actualExp = Assert.ThrowsAsync<Exception>(async () => await task.ConfigureAwait(false));
            Assert.AreEqual(exp.Message, actualExp.Message);
        }

        [Test]
        [Category("SubmitPipelineRunFromPipelineEndpointHelperAsync")]
        public void TestSubmitPipelineRunFromPipelineEndpointHelperAsync_StatusNotActive_ArgumentException()
        {
            MethodInfo method_SubmitPipelineRunFromPipelineEndpointHelperAsync = typeof(PipelineRunsController).GetMethod("SubmitPipelineRunFromPipelineEndpointHelperAsync", BindingFlags.Instance | BindingFlags.NonPublic);

            string version = "1.0.0";
            string pipelineId = Guid.NewGuid().ToString();
            string parentRunId = Guid.NewGuid().ToString();
            var aevaPipelineSubmissionInfo = new AEVAPipelineSubmissionInfo();
            var pipelineEndpointEntity = new PipelineEndpointEntity()
            {
                EntityStatus = EntityStatus.Deprecated,
                PipelineVersionList = new List<PipelineVersion>()
                {
                    new PipelineVersion()
                    {
                       Version = version,
                       PipelineId = pipelineId
                    }
                }
            };
            Assert.AreNotEqual(EntityStatus.Active, pipelineEndpointEntity.EntityStatus);

            var task = (Task<AEVAPipelineRunEntity>)method_SubmitPipelineRunFromPipelineEndpointHelperAsync.Invoke(_fakePipelineRunsController, new object[] {
                      pipelineEndpointEntity,
                      _dummyWorkspaceIdentity,
                      "parentRunId",
                      new AEVAPipelineSubmissionInfo(),
                      "1.0.0",
                      false });

            var actualExp = Assert.ThrowsAsync<ArgumentException>(async () => await task.ConfigureAwait(false));
            StringAssert.Contains($"Entity Status is not active, its {pipelineEndpointEntity.EntityStatus}", actualExp.Message);
        }

        [Test]
        [Category("SubmitPipelineRunFromPipelineEndpointHelperAsync")]
        public async Task TestSubmitPipelineRunFromPipelineEndpointHelperAsync_Normal_Return()
        {
            MethodInfo method_SubmitPipelineRunFromPipelineEndpointHelperAsync = typeof(PipelineRunsController).GetMethod("SubmitPipelineRunFromPipelineEndpointHelperAsync", BindingFlags.Instance | BindingFlags.NonPublic);

            string version = "1.0.0";
            string pipelineId = Guid.NewGuid().ToString();
            string parentRunId = Guid.NewGuid().ToString();
            var aevaPipelineSubmissionInfo = new AEVAPipelineSubmissionInfo();
            var pipelineEndpointEntity = new PipelineEndpointEntity()
            {
                EntityStatus = EntityStatus.Active,
                PipelineVersionList = new List<PipelineVersion>()
                {
                    new PipelineVersion()
                    {
                       Version = version,
                       PipelineId = pipelineId
                    }
                }
            };

            AEVAPipelineRunEntity entity = new AEVAPipelineRunEntity();
            Mock.Get(_fakePipelineRunsController)
                .Setup(
                    stores => stores.SubmitPipelineRunFromPipelineHelperAsync(
                    _dummyWorkspaceIdentity,
                    pipelineId,
                    null,
                    parentRunId,
                    null,
                    null,
                    aevaPipelineSubmissionInfo,
                    LogTelemetrySourceEnum.PipelineEndpoint))
                .Returns(Task.FromResult(entity));

            Assert.AreEqual(EntityStatus.Active, pipelineEndpointEntity.EntityStatus);
            var task = (Task<AEVAPipelineRunEntity>)method_SubmitPipelineRunFromPipelineEndpointHelperAsync.Invoke(_fakePipelineRunsController, new object[] {
                      pipelineEndpointEntity,
                      _dummyWorkspaceIdentity,
                      parentRunId,
                      aevaPipelineSubmissionInfo,
                      version,
                      false });

            var actualRet = await task.ConfigureAwait(false);
            Assert.AreEqual(entity, actualRet);
        }

        [Test]
        [Category("SubmitPipelineRunFromPipelineEndpointHelperAsync")]
        public void TestSubmitPipelineRunFromPipelineEndpointHelperAsync_SubmitFailed_Exception()
        {
            MethodInfo method_SubmitPipelineRunFromPipelineEndpointHelperAsync = typeof(PipelineRunsController).GetMethod("SubmitPipelineRunFromPipelineEndpointHelperAsync", BindingFlags.Instance | BindingFlags.NonPublic);

            string version = "1.0.0";
            string pipelineId = Guid.NewGuid().ToString();
            string parentRunId = Guid.NewGuid().ToString();
            var aevaPipelineSubmissionInfo = new AEVAPipelineSubmissionInfo();
            var pipelineEndpointEntity = new PipelineEndpointEntity()
            {
                EntityStatus = EntityStatus.Active,
                PipelineVersionList = new List<PipelineVersion>()
                {
                    new PipelineVersion()
                    {
                       Version = version,
                       PipelineId = pipelineId
                    }
                }
            };

            AEVAPipelineRunEntity entity = new AEVAPipelineRunEntity();

            var exp = new Exception("failed at SubmitPipelineRunInternalAsync");
            Mock.Get(_fakePipelineRunsController)
                .Setup(
                    stores => stores.SubmitPipelineRunFromPipelineHelperAsync(
                    _dummyWorkspaceIdentity,
                    pipelineId,
                    null,
                    parentRunId,
                    null,
                    null,
                    aevaPipelineSubmissionInfo,
                    LogTelemetrySourceEnum.PipelineEndpoint))
                .Throws(exp);

            Assert.AreEqual(EntityStatus.Active, pipelineEndpointEntity.EntityStatus);
            var task = (Task<AEVAPipelineRunEntity>)method_SubmitPipelineRunFromPipelineEndpointHelperAsync.Invoke(_fakePipelineRunsController, new object[] {
                      pipelineEndpointEntity,
                      _dummyWorkspaceIdentity,
                      parentRunId,
                      aevaPipelineSubmissionInfo,
                      version,
                      false });

            var actualExp = Assert.ThrowsAsync<Exception>(async () => await task.ConfigureAwait(false));
            Assert.AreEqual(exp.Message, actualExp.Message);
        }

        [Test]
        [Category("GetPipelineIdFromPipelineEndpointAsync")]
        public void TestGetPipelineIdFromPipelineEndpointAsync_NoVersionSpecified_Return()
        {
            MethodInfo method_GetPipelineIdFromPipelineEndpointAsync = typeof(PipelineRunsController).GetMethod("GetPipelineIdFromPipelineEndpointAsync", BindingFlags.Instance | BindingFlags.NonPublic);

            string version = Guid.NewGuid().ToString();
            string default_version = Guid.NewGuid().ToString();
            string pipelineId1 = Guid.NewGuid().ToString();
            string pipelineId2 = Guid.NewGuid().ToString();
            var pipelineEndpointEntity = new PipelineEndpointEntity()
            {
                EntityStatus = EntityStatus.Active,
                DefaultVersion = default_version,
                PipelineVersionList = new List<PipelineVersion>()
                {
                    new PipelineVersion()
                    {
                       Version = version,
                       PipelineId = pipelineId1
                    },
                     new PipelineVersion()
                    {
                       Version = default_version,
                       PipelineId = pipelineId2
                    }
                }
            };

            var actualRet = (string)method_GetPipelineIdFromPipelineEndpointAsync.Invoke(_fakePipelineRunsController, new object[] {
                      _dummyWorkspaceIdentity,
                      pipelineEndpointEntity,
                      string.Empty});

            Assert.AreEqual(pipelineId2, actualRet);
        }

        [Test]
        [Category("GetPipelineIdFromPipelineEndpointAsync")]
        public void TestGetPipelineIdFromPipelineEndpointAsync_VersionSpecified_Return()
        {
            MethodInfo method_GetPipelineIdFromPipelineEndpointAsync = typeof(PipelineRunsController).GetMethod("GetPipelineIdFromPipelineEndpointAsync", BindingFlags.Instance | BindingFlags.NonPublic);

            string version = Guid.NewGuid().ToString();
            string default_version = Guid.NewGuid().ToString();
            string pipelineId1 = Guid.NewGuid().ToString();
            string pipelineId2 = Guid.NewGuid().ToString();
            var pipelineEndpointEntity = new PipelineEndpointEntity()
            {
                EntityStatus = EntityStatus.Active,
                DefaultVersion = default_version,
                PipelineVersionList = new List<PipelineVersion>()
                {
                    new PipelineVersion()
                    {
                       Version = version,
                       PipelineId = pipelineId1
                    },
                     new PipelineVersion()
                    {
                       Version = default_version,
                       PipelineId = pipelineId2
                    }
                }
            };

            var actualRet = (string)method_GetPipelineIdFromPipelineEndpointAsync.Invoke(_fakePipelineRunsController, new object[] {
                      _dummyWorkspaceIdentity,
                      pipelineEndpointEntity,
                      version});

            Assert.AreEqual(pipelineId1, actualRet);
        }

        [Test]
        [Category("GetPipelineIdFromPipelineEndpointAsync")]
        public void TestGetPipelineIdFromPipelineEndpointAsync_VersionNotFound_Return()
        {
            MethodInfo method_GetPipelineIdFromPipelineEndpointAsync = typeof(PipelineRunsController).GetMethod("GetPipelineIdFromPipelineEndpointAsync", BindingFlags.Instance | BindingFlags.NonPublic);

            string version = Guid.NewGuid().ToString();
            string default_version = Guid.NewGuid().ToString();
            string pipelineId1 = Guid.NewGuid().ToString();
            string pipelineId2 = Guid.NewGuid().ToString();
            var pipelineEndpointEntity = new PipelineEndpointEntity()
            {
                Id = Guid.NewGuid().ToString(),
                EntityStatus = EntityStatus.Active,
                DefaultVersion = default_version,
                PipelineVersionList = new List<PipelineVersion>()
                {
                     new PipelineVersion()
                    {
                       Version = default_version,
                       PipelineId = pipelineId2
                    }
                }
            };

            var actualExp = Assert.Throws<TargetInvocationException>(() => method_GetPipelineIdFromPipelineEndpointAsync.Invoke(_fakePipelineRunsController, new object[] {
                      _dummyWorkspaceIdentity,
                      pipelineEndpointEntity,
                      version}));

            Assert.AreEqual($"Pipeline with version {version}, not found in pipelineEndpoint {pipelineEndpointEntity.Id}", actualExp.InnerException.Message);
        }

        [Test]
        [Category("SubmitPipelineRunFromPipelineHelperAsync")]
        public void TestSubmitPipelineRunFromPipelineHelperAsync_SubmitFailed_Exception()
        {
            string pipelineId = Guid.NewGuid().ToString();
            string parentPipelineId = Guid.NewGuid().ToString();
            var aevaPipelineSubmissionInfo = new AEVAPipelineSubmissionInfo()
            {
                RunSource = "dummyRunSource",
                ExperimentName = "dummyExperimentName"
            };

            var exp = new Exception("failed at SubmitPipelineRunInternalAsync");
            Mock.Get(_fakePipelineRunsController)
                .Setup(
                    stores => stores.SubmitPipelineRunInternalAsync(
                    It.IsAny<WorkspaceIdentity>(),
                    It.IsAny<ExperimentEntity>(),
                    It.IsAny<GraphEntity>(),
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<bool>(),
                    It.IsAny<LogTelemetrySourceEnum>(),
                    It.IsAny<bool?>(),
                    It.IsAny<int?>(),
                    It.IsAny<int?>(),
                    It.IsAny<bool>(),
                    It.IsAny<ComputeSetting>(),
                    It.IsAny<DatastoreSetting>(),
                    It.IsAny<IdentitySetting>(),
                    It.IsAny<int?>()))
                .Throws(exp);

            var actualExp = Assert.ThrowsAsync<Exception>(async () => await _fakePipelineRunsController.SubmitPipelineRunFromPipelineHelperAsync(
               _dummyWorkspaceIdentity,
               pipelineId,
               null,
               parentPipelineId,
               null,
               null,
               aevaPipelineSubmissionInfo,
               LogTelemetrySourceEnum.Others).ConfigureAwait(false));

            Assert.AreEqual(exp.Message, actualExp.Message);
        }

        [Test]
        [Category("LoadPipelineRunsByPipelineIdAsync")]
        public void TestLoadPipelineRunsByPipelineIdAsync_PipelineIdNotValid_ArgumentException()
        {
            string pipelineId = string.Empty;
            var actualExp = Assert.ThrowsAsync<ArgumentException>(async () => await _fakePipelineRunsController.LoadPipelineRunsByPipelineIdAsync(
              "subscriptionId",
              "rgName",
              "wsName",
              pipelineId).ConfigureAwait(false));
            StringAssert.Contains("pipelineId can't be null or empty", actualExp.Message);
        }

        [Test]
        [Category("LoadPipelineRunsByPipelineIdAsync")]
        public void TestLoadPipelineRunsByPipelineIdAsync_PipelineIdNotGuid_ArgumentException()
        {
            string pipelineId = "Hello";
            var actualExp = Assert.ThrowsAsync<ArgumentException>(async () => await _fakePipelineRunsController.LoadPipelineRunsByPipelineIdAsync(
              "subscriptionId",
              "rgName",
              "wsName",
              pipelineId).ConfigureAwait(false));
            StringAssert.Contains("pipelineId should be a valid guid", actualExp.Message);
        }

        [Test]
        [Category("LoadPipelineRunsByPipelineIdAsync")]
        public void TestLoadPipelineRunsByPipelineIdAsync_GetPipelineFailed_Exception()
        {
            string pipelineId = _dummyId;
            var exp = new Exception("failed at GetPipelineRunStore");
            Mock.Get(_fakeIStoreCollection)
                .Setup(
                  stores => stores.GetPipelineRunStore(
                      It.IsAny<ClaimsPrincipal>(),
                      It.IsAny<WorkspaceIdentity>()))
                .Throws(exp);

            var actualExp = Assert.ThrowsAsync<Exception>(async () => await _fakePipelineRunsController.LoadPipelineRunsByPipelineIdAsync(
              "subscriptionId",
              "rgName",
              "wsName",
              pipelineId).ConfigureAwait(false));

            Assert.AreEqual(exp.Message, actualExp.Message);
        }

        [Test]
        [Category("LoadPipelineRunsByScheduleIdAsync")]
        public void TestLoadPipelineRunsByScheduleIdAsync_ScheduleIdNotValid_ArgumentException()
        {
            string scheduleId = string.Empty;
            var actualExp = Assert.ThrowsAsync<ArgumentException>(async () => await _fakePipelineRunsController.LoadPipelineRunsByScheduleIdAsync(
              "subscriptionId",
              "rgName",
              "wsName",
              scheduleId).ConfigureAwait(false));
            StringAssert.Contains("scheduleId can't be null or empty", actualExp.Message);
        }

        [Test]
        [Category("LoadPipelineRunsByScheduleIdAsync")]
        public void TestLoadPipelineRunsByScheduleIdAsync_ScheduleIdNotGuid_ArgumentException()
        {
            string scheduleId = "hello";
            var actualExp = Assert.ThrowsAsync<ArgumentException>(async () => await _fakePipelineRunsController.LoadPipelineRunsByScheduleIdAsync(
              "subscriptionId",
              "rgName",
              "wsName",
              scheduleId).ConfigureAwait(false));
            StringAssert.Contains("scheduleId should be a valid guid", actualExp.Message);
        }

        [Test]
        [Category("LoadPipelineRunsByScheduleIdAsync")]
        public void TestLoadPipelineRunsByScheduleIdAsync_GetPipelineFailed_Exception()
        {
            string pipelineId = _dummyId;
            var exp = new Exception("failed at GetPipelineRunStore");
            Mock.Get(_fakeIStoreCollection)
                .Setup(
                  stores => stores.GetPipelineRunStore(
                      It.IsAny<ClaimsPrincipal>(),
                      It.IsAny<WorkspaceIdentity>()))
                .Throws(exp);

            var actualExp = Assert.ThrowsAsync<Exception>(async () => await _fakePipelineRunsController.LoadPipelineRunsByScheduleIdAsync(
              "subscriptionId",
              "rgName",
              "wsName",
              pipelineId).ConfigureAwait(false));

            Assert.AreEqual(exp.Message, actualExp.Message);
        }

        [Test]
        [Category("GetLastPipelineRunForSchedule")]
        public void TestGetLastPipelineRunForSchedule_ScheduleIdNotValid_ArgumentException()
        {
            string scheduleId = string.Empty;
            var actualExp = Assert.ThrowsAsync<ArgumentException>(async () => await _fakePipelineRunsController.GetLastPipelineRunForSchedule(
              "subscriptionId",
              "rgName",
              "wsName",
              scheduleId).ConfigureAwait(false));
            StringAssert.Contains("scheduleId can't be null or empty", actualExp.Message);
        }

        [Test]
        [Category("GetLastPipelineRunForSchedule")]
        public void TestGetLastPipelineRunForSchedule_ScheduleIdNotGuid_ArgumentException()
        {
            string scheduleId = "hello";
            var actualExp = Assert.ThrowsAsync<ArgumentException>(async () => await _fakePipelineRunsController.GetLastPipelineRunForSchedule(
              "subscriptionId",
              "rgName",
              "wsName",
              scheduleId).ConfigureAwait(false));
            StringAssert.Contains("scheduleId should be a valid guid", actualExp.Message);
        }

        [Test]
        [Category("GetLastPipelineRunForSchedule")]
        public void TestGetLastPipelineRunForSchedule_GetPipelineFailed_Exception()
        {
            string pipelineId = _dummyId;
            var exp = new Exception("failed at GetPipelineRunStore");
            Mock.Get(_fakeIStoreCollection)
                .Setup(
                  stores => stores.GetPipelineRunStore(
                      It.IsAny<ClaimsPrincipal>(),
                      It.IsAny<WorkspaceIdentity>()))
                .Throws(exp);

            var actualExp = Assert.ThrowsAsync<Exception>(async () => await _fakePipelineRunsController.GetLastPipelineRunForSchedule(
              "subscriptionId",
              "rgName",
              "wsName",
              pipelineId).ConfigureAwait(false));

            Assert.AreEqual(exp.Message, actualExp.Message);
        }

        [Test]
        [Category("SubmitUnsavedPipelineRunByCreationInfoWithGraphAsync")]
        public void TestSubmitUnsavedPipelineRunByCreationInfoWithGraphAsync_CreatePipelineRunFailed_Exception()
        {
            _dummyRunDto = new RunDto()
            {
                RootRunId = Guid.NewGuid().ToString(),
                RunNumber = 10
            };

            Mock.Get(_fakePipelineRunsController)
                .Setup(
                    stores => stores.SubmitPipelineRunInternalAsync(
                    It.IsAny<WorkspaceIdentity>(),
                    It.IsAny<ExperimentEntity>(),
                    It.IsAny<GraphEntity>(),
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<bool>(),
                    It.IsAny<LogTelemetrySourceEnum>(),
                    It.IsAny<bool?>(),
                    It.IsAny<int?>(),
                    It.IsAny<int?>(),
                    It.IsAny<bool>(),
                    It.IsAny<ComputeSetting>(),
                    It.IsAny<DatastoreSetting>(),
                    It.IsAny<IdentitySetting>(),
                    It.IsAny<int>()))
                .Returns(Task.FromResult(_dummyRunDto));

            var exp = new Exception("failed at GetPipelineRunStore");
            Mock.Get(_fakeIGraphStore)
                .Setup(
                  stores => stores.CreateAsync(
                      It.IsAny<string>(),
                      It.IsAny<GraphEntity>(),
                      It.IsAny<WorkspaceIdentity>()))
                .Throws(exp);

            var actualExp = Assert.ThrowsAsync<Exception>(async () => await _fakePipelineRunsController.SubmitUnsavedPipelineRunByCreationInfoWithGraphAsync(
              "subscriptionId",
              "rgName",
              "wsName",
              creationInfo).ConfigureAwait(false));
            Assert.AreEqual(exp.Message, actualExp.Message);
        }

        [Test]
        [Category("ValidateEntityForPipelineRunCreation")]
        public void TestValidateEntityForPipelineRunCreation_DisabledStatus_InvalidOperationException()
        {
            _dumnmyPipelineEntity.EntityStatus = EntityStatus.Disabled;

            MethodInfo method_ValidateEntityForPipelineRunCreation = typeof(PipelineRunsController).GetMethod("ValidateEntityForPipelineRunCreation", BindingFlags.Static | BindingFlags.NonPublic);

            var actualExp = Assert.Throws<TargetInvocationException>(() => method_ValidateEntityForPipelineRunCreation.Invoke(_fakePipelineRunsController, new object[] { _dumnmyPipelineEntity }));

            Assert.True(actualExp.InnerException is InvalidOperationException);
            Assert.AreEqual("Cannot create pipeline run from a disabled pipeline.", actualExp.InnerException.Message);
        }

        [Test]
        [Category("ValidateEntityForPipelineRunCreation")]
        public void TestValidateEntityForPipelineRunCreation_DeprecatedStatus_InvalidOperationException()
        {
            _dumnmyPipelineEntity.EntityStatus = EntityStatus.Deprecated;

            MethodInfo method_ValidateEntityForPipelineRunCreation = typeof(PipelineRunsController).GetMethod("ValidateEntityForPipelineRunCreation", BindingFlags.Static | BindingFlags.NonPublic);

            var actualExp = Assert.Throws<TargetInvocationException>(() => method_ValidateEntityForPipelineRunCreation.Invoke(_fakePipelineRunsController, new object[] { _dumnmyPipelineEntity }));

            Assert.True(actualExp.InnerException is InvalidOperationException);
            Assert.AreEqual("Cannot create pipeline run from a deprecated pipeline.", actualExp.InnerException.Message);
        }

        [Test]
        [Category("GetNodeShareableLogAsync")]
        public async Task TestGetNodeShareableLogAsync_Normal_Execution()
        {
            MethodInfo method_GetNodeShareableLogAsync = typeof(PipelineRunsController).GetMethod("GetNodeShareableLogAsync", BindingFlags.Instance | BindingFlags.NonPublic);

            var task = (Task<Stream>)method_GetNodeShareableLogAsync.Invoke(_fakePipelineRunsController, new object[] {"subscriptionId",
                          "rgName",
                          "wsName",
                          _dummyId,
                          new string[] { _dummyId },
                          LogType.Execution });

            var actualRet = await task.ConfigureAwait(false);

            Assert.AreEqual(_dummyStream, actualRet);
        }

        [Test]
        [Category("CreatePipelineRunWithGraphHelperAsync")]
        public async Task TestCreatePipelineRunWithGraphHelperAsync_Normal_Return()
        {
            MethodInfo method_CreatePipelineRunWithGraphHelperAsync = typeof(PipelineRunsController).GetMethod("CreatePipelineRunWithGraphHelperAsync", BindingFlags.Instance | BindingFlags.NonPublic);

            Mock.Get(_fakePipelineRunsController)
                .Setup(
                    stores => stores.CreateUnsubmittedPipelineRunWithCreationInfoAsync(
                    It.IsAny<ExperimentCreationInfo>(),
                    It.IsAny<string>(),
                    It.IsAny<WorkspaceIdentity>(),
                    It.IsAny<string>(),
                    It.IsAny<int>(),
                    It.IsAny<bool>(),
                    It.IsAny<bool>()))
                .Returns(Task.FromResult(_dummyExperimentEntity));

            _dummyRunDto = new RunDto()
            {
                RootRunId = Guid.NewGuid().ToString(),
                RunNumber = 10
            };

            Mock.Get(_fakePipelineRunsController)
                .Setup(
                    stores => stores.SubmitPipelineRunInternalAsync(
                    It.IsAny<WorkspaceIdentity>(),
                    It.IsAny<ExperimentEntity>(),
                    It.IsAny<GraphEntity>(),
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<bool>(),
                    It.IsAny<LogTelemetrySourceEnum>(),
                    It.IsAny<bool?>(),
                    It.IsAny<int?>(),
                    It.IsAny<int?>(),
                    It.IsAny<bool>(),
                    It.IsAny<ComputeSetting>(),
                    It.IsAny<DatastoreSetting>(),
                    It.IsAny<IdentitySetting>(),
                    It.IsAny<int>()))
                .Returns(Task.FromResult(_dummyRunDto));

            var task = (Task<AEVAPipelineRunEntity>)method_CreatePipelineRunWithGraphHelperAsync.Invoke(_fakePipelineRunsController, new object[] {
                          "expName",
                          _dummyWorkspaceIdentity,
                          creationInfo });

            var actualRet = await task.ConfigureAwait(false);
            var expectedRet = ContractConverter.Convert<ExperimentEntity, AEVAPipelineRunEntity>(_dummyExperimentEntity, ContractConverter.GetPipelineStatusCustomConverters());
            expectedRet.Should().BeEquivalentTo(actualRet);
        }

        [Test]
        [Category("CreateGraphEntityForPipelineRun")]
        public async Task TestCreateGraphEntityForPipelineRun_Normal_Return()
        {
            MethodInfo method_CreateGraphEntityForPipelineRun = typeof(PipelineRunsController).GetMethod("CreateGraphEntityForPipelineRun", BindingFlags.Instance | BindingFlags.NonPublic);

            var task = (Task<GraphEntity>)method_CreateGraphEntityForPipelineRun.Invoke(_fakePipelineRunsController, new object[] {
                          _dummyWorkspaceIdentity,
                          creationInfo });

            var actualRet = await task.ConfigureAwait(false);
            _dummyGraphEntity.Should().BeEquivalentTo(actualRet);
        }

        [Test]
        [Category("CreateExperimentwithGraphInfo")]
        public async Task TestCreateExperimentwithGraphInfo_Normal_Return()
        {
            MethodInfo method_CreateExperimentwithGraphInfo = typeof(PipelineRunsController).GetMethod("CreateExperimentwithGraphInfo", BindingFlags.Instance | BindingFlags.NonPublic);

            Mock.Get(_fakePipelineRunsController)
                .Setup(
                    stores => stores.CreateUnsubmittedPipelineRunWithCreationInfoAsync(
                    It.IsAny<ExperimentCreationInfo>(),
                    It.IsAny<string>(),
                    It.IsAny<WorkspaceIdentity>(),
                    It.IsAny<string>(),
                    It.IsAny<int>(),
                    It.IsAny<bool>(),
                    It.IsAny<bool>()))
                .Returns(Task.FromResult(_dummyExperimentEntity));

            var task = (Task<ExperimentEntity>)method_CreateExperimentwithGraphInfo.Invoke(_fakePipelineRunsController, new object[] {
                          creationInfo,
                          _dummyWorkspaceIdentity,
                          _dummyGraphEntity,
                          "expName" });

            var actualRet = await task.ConfigureAwait(false);
            _dummyExperimentEntity.Should().BeEquivalentTo(actualRet);
        }

        [Test]
        [Category("CreateAndSubmitPipelineRunWithGraphHelperAsync")]
        public async Task TestCreateAndSubmitPipelineRunWithGraphHelperAsync_Normal_Return()
        {
            MethodInfo method_CreatePipelineRunWithGraphHelperAsync = typeof(PipelineRunsController).GetMethod("CreateAndSubmitPipelineRunWithGraphHelperAsync", BindingFlags.Instance | BindingFlags.NonPublic);

            Mock.Get(_fakePipelineRunsController)
                .Setup(
                    stores => stores.CreateUnsubmittedPipelineRunWithCreationInfoAsync(
                    It.IsAny<ExperimentCreationInfo>(),
                    It.IsAny<string>(),
                    It.IsAny<WorkspaceIdentity>(),
                    It.IsAny<string>(),
                    It.IsAny<int>(),
                    It.IsAny<bool>(),
                    It.IsAny<bool>()))
                .Returns(Task.FromResult(_dummyExperimentEntity));

            _dummyRunDto = new RunDto()
            {
                RootRunId = Guid.NewGuid().ToString(),
                RunNumber = 10
            };

            Mock.Get(_fakePipelineRunsController)
                .Setup(
                    stores => stores.SubmitPipelineRunInternalAsync(
                    It.IsAny<WorkspaceIdentity>(),
                    It.IsAny<ExperimentEntity>(),
                    It.IsAny<GraphEntity>(),
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<bool>(),
                    It.IsAny<LogTelemetrySourceEnum>(),
                    It.IsAny<bool?>(),
                    It.IsAny<int?>(),
                    It.IsAny<int?>(),
                    It.IsAny<bool>(),
                    It.IsAny<ComputeSetting>(),
                    It.IsAny<DatastoreSetting>(),
                    It.IsAny<IdentitySetting>(),
                    It.IsAny<int?>()))
                .Returns(Task.FromResult(_dummyRunDto));

            var task = (Task<RunDto>)method_CreatePipelineRunWithGraphHelperAsync.Invoke(_fakePipelineRunsController, new object[] {
                          "expName",
                          _dummyWorkspaceIdentity,
                          creationInfo,
                          false,
                          LogTelemetrySourceEnum.Others});

            var actualRet = await task.ConfigureAwait(false);
            _dummyRunDto.Should().BeEquivalentTo(actualRet);
        }

        [Test]
        [Category("CreateAndSubmitPipelineRunWithGraphHelperAsync")]
        public void TestCreateAndSubmitPipelineRunWithGraphHelperAsync_SubmitFailed_Exception()
        {
            MethodInfo method_CreatePipelineRunWithGraphHelperAsync = typeof(PipelineRunsController).GetMethod("CreateAndSubmitPipelineRunWithGraphHelperAsync", BindingFlags.Instance | BindingFlags.NonPublic);

            Mock.Get(_fakePipelineRunsController)
                .Setup(
                    stores => stores.CreateUnsubmittedPipelineRunWithCreationInfoAsync(
                    It.IsAny<ExperimentCreationInfo>(),
                    It.IsAny<string>(),
                    It.IsAny<WorkspaceIdentity>(),
                    It.IsAny<string>(),
                    It.IsAny<int>(),
                    It.IsAny<bool>(),
                    It.IsAny<bool>()))
                .Returns(Task.FromResult(_dummyExperimentEntity));

            var exp = new Exception("failed to submit");

            Mock.Get(_fakePipelineRunsController)
                .Setup(
                    stores => stores.SubmitPipelineRunInternalAsync(
                    It.IsAny<WorkspaceIdentity>(),
                    It.IsAny<ExperimentEntity>(),
                    It.IsAny<GraphEntity>(),
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<bool>(),
                    It.IsAny<LogTelemetrySourceEnum>(),
                    It.IsAny<bool?>(),
                    It.IsAny<int?>(),
                    It.IsAny<int?>(),
                    It.IsAny<bool>(),
                    It.IsAny<ComputeSetting>(),
                    It.IsAny<DatastoreSetting>(),
                    It.IsAny<IdentitySetting>(),
                    It.IsAny<int?>()))
                .Throws(exp);

            var task = (Task<RunDto>)method_CreatePipelineRunWithGraphHelperAsync.Invoke(_fakePipelineRunsController, new object[] {
                          "expName",
                          _dummyWorkspaceIdentity,
                          creationInfo,
                          false,
                          LogTelemetrySourceEnum.Others });

            var actualExp = Assert.ThrowsAsync<Exception>(async () => await task.ConfigureAwait(false));
            Assert.AreEqual(exp.Message, actualExp.Message);
        }

        [Test]
        [Category("CreateUnsubmittedPipelineRunWithCreationInfoAsync")]
        public async Task TestCreateUnsubmittedPipelineRunWithCreationInfoAsync_Normal_Return()
        {
            ExecutionProperties dummyExecutionProperties = new ExecutionProperties();

            Mock.Get(_fakeIExperimentStore)
                .Setup(
                  stores => stores.CreateExecutionPropertiesAsync(
                      It.IsAny<string>(),
                      It.IsAny<string>(),
                      It.IsAny<ExecutionProperties>(),
                      It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(dummyExecutionProperties));

            ExperimentCreationInfo info = new ExperimentCreationInfo();

            var actualRet = await _fakePipelineRunsController
                .CreateUnsubmittedPipelineRunWithCreationInfoAsync(info, _dummyId, _dummyWorkspaceIdentity, "expName", 2, false, false)
                .ConfigureAwait(false);
            Assert.AreEqual(_dummyExperimentEntity, actualRet);
        }

        [Test]
        [Category("CreateUnsubmittedPipelineRunAsync")]
        public async Task TestCreateUnsubmittedPipelineRunAsync_Normal_Return()
        {
            MethodInfo method_CreateUnsubmittedPipelineRunAsync = typeof(PipelineRunsController).GetMethod("CreateUnsubmittedPipelineRunAsync", BindingFlags.Instance | BindingFlags.NonPublic);

            var task = (Task<ExperimentEntity>)method_CreateUnsubmittedPipelineRunAsync.Invoke(_fakePipelineRunsController, new object[] {
                          _dummyExperimentEntity,
                          _dummyWorkspaceIdentity,
                          "expName",
                          _dummyId,
                          Guid.NewGuid().ToString()});

            var actualRet = await task.ConfigureAwait(false);
            Assert.AreEqual(_dummyExperimentEntity, actualRet);
        }

        [Test]
        [Category("VerifyExperimentName")]
        public void TestVerifyExperimentName_NameLenLessThanMin_Exception()
        {
            MethodInfo method_VerifyExperimentName = typeof(PipelineRunsController).GetMethod("VerifyExperimentName", BindingFlags.Static | BindingFlags.NonPublic);

            var name = string.Empty;
            var exp = Assert.Throws<TargetInvocationException>(() => method_VerifyExperimentName.Invoke(_fakePipelineRunsController, new object[] { name }));

            Assert.True(exp.InnerException is ArgumentException);
            StringAssert.Contains("Experiment name must be between", exp.InnerException.Message);
        }

        [Test]
        [Category("VerifyExperimentName")]
        public void TestVerifyExperimentName_NameLenMoreThanMax_Exception()
        {
            MethodInfo method_VerifyExperimentName = typeof(PipelineRunsController).GetMethod("VerifyExperimentName", BindingFlags.Static | BindingFlags.NonPublic);

            var name = new string('*', 300);
            var exp = Assert.Throws<TargetInvocationException>(() => method_VerifyExperimentName.Invoke(_fakePipelineRunsController, new object[] { name }));

            Assert.True(exp.InnerException is ArgumentException);
            StringAssert.Contains("Experiment name must be between", exp.InnerException.Message);
        }

        [Test]
        [Category("VerifyExperimentName")]
        public void TestVerifyExperimentName_FirstCharNotValid_Exception()
        {
            MethodInfo method_VerifyExperimentName = typeof(PipelineRunsController).GetMethod("VerifyExperimentName", BindingFlags.Static | BindingFlags.NonPublic);

            var name = "^8ac";
            var exp = Assert.Throws<TargetInvocationException>(() => method_VerifyExperimentName.Invoke(_fakePipelineRunsController, new object[] { name }));

            Assert.True(exp.InnerException is ArgumentException);
            StringAssert.Contains($"Its first character has to be alphanumeric. Provided experiment name is {name}", exp.InnerException.Message);
        }

        [Test]
        [Category("ProcessAzDevOpsCallBackInfo")]
        public void TestProcessAzDevOpsCallBackInfo_PipelineSubmissionInfoNotValid_ArgumentException()
        {
            MethodInfo method_ProcessAzDevOpsCallBackInfo = typeof(PipelineRunsController).GetMethod("ProcessAzDevOpsCallBackInfo", BindingFlags.Instance | BindingFlags.NonPublic);

            Mock.Get(_fakeCounterManager)
                .Setup(
                  stores => stores.GetNumberCounter(
                      It.IsAny<string>(),
                      It.IsAny<string>(),
                      It.IsAny<IDictionary<string, string>>()))
                .Returns(new NumberCounter(_repository.Create<ICounter>().Object, "instanceName"));

            AEVAPipelineSubmissionInfo info = new AEVAPipelineSubmissionInfo()
            {
                AzDevOpsCallBackInfo = new AEVAAzDevOpsCallBackInfo()
                {
                    AuthToken = "token"
                }
            };

            var task = (Task)method_ProcessAzDevOpsCallBackInfo.Invoke(_fakePipelineRunsController, new object[] { _dummyWorkspaceIdentity, _dummyId, info });

            var exp = Assert.ThrowsAsync<ArgumentException>(async () => await task.ConfigureAwait(false));

            Assert.True(exp is ArgumentException);
            Assert.AreEqual("PipelineRun is invoked by Azure DevOps but misses required information for the call back.", exp.Message);
        }

        [Test]
        [Category("ProcessAzDevOpsCallBackInfo")]
        public void TestProcessAzDevOpsCallBackInfo_CreateCrendtialFailed_Exception()
        {
            MethodInfo method_ProcessAzDevOpsCallBackInfo = typeof(PipelineRunsController).GetMethod("ProcessAzDevOpsCallBackInfo", BindingFlags.Instance | BindingFlags.NonPublic);

            Mock.Get(_fakeCounterManager)
                .Setup(
                  stores => stores.GetNumberCounter(
                      It.IsAny<string>(),
                      It.IsAny<string>(),
                      It.IsAny<IDictionary<string, string>>()))
                .Returns(new NumberCounter(_repository.Create<ICounter>().Object, "instanceName"));

            var exp = new Exception("failed to create credentials");
            Mock.Get(_fakeICredentialServiceClient)
                 .Setup(
                   stores => stores.CreateCredentialsAsync(
                       It.IsAny<WorkspaceIdentity>(),
                       It.IsAny<string>(),
                       It.IsAny<string>(),
                       It.IsAny<CreatedBy>()))
                 .Throws(exp);

            AEVAPipelineSubmissionInfo info = new AEVAPipelineSubmissionInfo()
            {
                AzDevOpsCallBackInfo = new AEVAAzDevOpsCallBackInfo()
                {
                    AuthToken = "token",
                    PlanUri = "planUri",
                    ProjectId = "projectId",
                    HubName = "hubName",
                    PlanId = "planId",
                    JobId = "jobId",
                    TaskInstanceId = "taskInstanceId"
                }
            };

            var task = (Task)method_ProcessAzDevOpsCallBackInfo.Invoke(_fakePipelineRunsController, new object[] { _dummyWorkspaceIdentity, _dummyId, info });

            var returnExp = Assert.ThrowsAsync<Exception>(async () => await task.ConfigureAwait(false));

            Assert.AreEqual(exp.Message, returnExp.Message);
        }

        [Test]
        [Category("SubmitPipelineRunFromPipelineDraftAsync")]
        public async Task SubmitPipelineRunFromPipelineDraftAsync()
        {
            AEVAPipelineDraft draft = new AEVAPipelineDraft()
            {
                PipelineSubmissionInfo = new AEVAPipelineSubmissionInfo()
                {
                    ExperimentName = "test"
                }
            };

            GraphEntity createdGraphEntity = null;

            Mock.Get(_fakeIGraphStore)
                .Setup(g => g.CreateAsync(It.IsAny<string>(), It.IsAny<GraphEntity>(), It.IsAny<WorkspaceIdentity>()))
                    .Returns(
                        (string workspaceId, GraphEntity graphEntity, WorkspaceIdentity identity) =>
                        {
                            graphEntity.Id = Guid.NewGuid().ToString();
                            createdGraphEntity = graphEntity;
                            return Task.FromResult(graphEntity);
                        });

            AmlGraphDraftEntity graphDraft = new AmlGraphDraftEntity()
            {
                ModuleNodes = new List<GraphModuleNode>()
                {
                    new GraphModuleNode()
                    {
                        Id = "moduleNode1"
                    }
                },
                DatasetNodes = new List<GraphDatasetNode>(),
                SubGraphNodes = new List<GraphReferenceNode>()
                {
                    new GraphReferenceNode()
                    {
                        Id = "subGraphNode1"
                    }
                },
                EntityInterface = new EntityInterface()
            };

            Mock.Get(_fakeGraphDraftStore)
                .Setup(
                  stores => stores.GetEntityAsync(
                      It.IsAny<string>(),
                      It.IsAny<string>(),
                      It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(graphDraft));

            Mock.Get(_fakeIExperimentStore)
                .Setup(
                  stores => stores.CreateAsync(
                      It.IsAny<string>(),
                      It.IsAny<ExperimentEntity>(),
                      It.IsAny<WorkspaceIdentity>()))
                .Returns(
                    (string workspaceId, ExperimentEntity experimentEntity, WorkspaceIdentity identity) =>
                    {
                        experimentEntity.Id = Guid.NewGuid().ToString();
                        return Task.FromResult(experimentEntity);
                    });

            AEVAPipelineRunEntity returnedRun = await _fakePipelineRunsController.SubmitPipelineRunFromPipelineDraftAsync(
                subscriptionId: "dummySubscriptionId",
                resourceGroupName: "dummyResourceGroupName",
                workspaceName: "dummyWorkspaceName",
                parentRunId: "dummyRunId",
                pipelineDraft: draft).ConfigureAwait(false);

            Assert.IsNotNull(returnedRun);
            Assert.IsNotNull(createdGraphEntity);
            Assert.AreEqual(returnedRun.GraphId, createdGraphEntity.Id);

            //  Module node and subgraph node should show up in the created graph
            Assert.AreEqual(createdGraphEntity.ModuleNodes.Count(), 1);
            Assert.AreEqual(createdGraphEntity.ModuleNodes.First().Id, "moduleNode1");
            Assert.AreEqual(createdGraphEntity.SubGraphNodes.Count(), 1);
            Assert.AreEqual(createdGraphEntity.SubGraphNodes.First().Id, "subGraphNode1");

            //  Test SubGraphNodes=null, should be converted to an empty list
            graphDraft.SubGraphNodes = null;

            returnedRun = await _fakePipelineRunsController.SubmitPipelineRunFromPipelineDraftAsync(
                subscriptionId: "dummySubscriptionId",
                resourceGroupName: "dummyResourceGroupName",
                workspaceName: "dummyWorkspaceName",
                parentRunId: "dummyRunId",
                pipelineDraft: draft).ConfigureAwait(false);

            Assert.IsNotNull(returnedRun);
            Assert.IsNotNull(createdGraphEntity);
            Assert.AreEqual(returnedRun.GraphId, createdGraphEntity.Id);
            Assert.IsNotNull(createdGraphEntity.SubGraphNodes);
            Assert.AreEqual(createdGraphEntity.SubGraphNodes.Count(), 0);
        }

        private static IRelInfraConfiguration GetConfiguration()
        {
            IDictionary<string, string> settings = new Dictionary<string, string>()
            {
                {"ExperimentSubmission.MaxNumberOfNodes", "10"}
            };
            return new DynamicConfigurationFlexMock(settings);
        }

        [Test]
        [Category("SubmitPipelineRunFromScheduleAsync")]
        public async Task SubmitPipelineRunFromScheduleAsync()
        {
            List<AEVASchedulePipelineRunPayload> schedulePayloadList = new List<AEVASchedulePipelineRunPayload>();
            schedulePayloadList.Add(new AEVASchedulePipelineRunPayload() { ParameterAssignments = new Dictionary<string, string> { { "time", "2022-01-11T04:03:14.3774220Z" } } });
            // Test the case that the keys between schedule parameterAssignments and schedulePayload are different.
            schedulePayloadList.Add(new AEVASchedulePipelineRunPayload() { ParameterAssignments = new Dictionary<string, string> { { "key", "value" } } });
            // Test the case that schedulePipelineRunPayload's fields are null, in case of meeting null Exception.
            schedulePayloadList.Add(new AEVASchedulePipelineRunPayload());

            var pipelineScheduleStore = _repository.Create<IPipelineScheduleStore>().Object;

            bool called_GetPipelineScheduleStore = false;
            Mock.Get(_fakeIStoreCollection)
                .Setup(controller => controller.GetPipelineScheduleStore(
                    It.IsAny<ClaimsPrincipal>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Callback(() => called_GetPipelineScheduleStore = true)
                .Returns(pipelineScheduleStore);

            List<Dictionary<string, string>> parameterAssignmentsList = new List<Dictionary<string, string>>();
            parameterAssignmentsList.Add(new Dictionary<string, string> { { "time", "@utcNow()" } });
            parameterAssignmentsList.Add(new Dictionary<string, string> { });
            // Test the case that pipelineSubmissionInfo.ParameterAssignments is null.
            parameterAssignmentsList.Add(null);

            PipelineScheduleEntity _fakePipelinesScheduleEntity = new PipelineScheduleEntity();
            var aevaPipelineSubmissionInfo = new AEVAPipelineSubmissionInfo();
            PipelineSubmissionInfo pipelineSubmissionInfo = new PipelineSubmissionInfo();
            _fakePipelinesScheduleEntity = _repository.Create<PipelineScheduleEntity>().Object;
            // test recurrence ScheduleType
            _fakePipelinesScheduleEntity.ScheduleType = ScheduleType.Recurrence;
            _fakePipelinesScheduleEntity.Recurrence = _repository.Create<Recurrence>().Object;
            _fakePipelinesScheduleEntity.Recurrence.Interval = 2;
            _fakePipelinesScheduleEntity.Recurrence.Frequency = Frequency.Day;
            _fakePipelinesScheduleEntity.PipelineId = Guid.NewGuid().ToString();
            _fakePipelinesScheduleEntity.PipelineSubmissionInfo = pipelineSubmissionInfo;

            bool called_GetPipelinesScheduleEntity = false;
            Mock.Get(pipelineScheduleStore)
                .Setup(
                  store => store.GetEntityAsync(
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Callback(() => called_GetPipelinesScheduleEntity = true)
                .Returns(Task.FromResult(_fakePipelinesScheduleEntity));

            AEVAPipelineRunEntity entity = new AEVAPipelineRunEntity();
            string parentRunId = Guid.NewGuid().ToString();

            AEVAPipelineSubmissionInfo callbackAEVAPipeInfo = null;
            Mock.Get(_fakePipelineRunsController)
                .Setup(
                    stores => stores.SubmitPipelineRunFromPipelineHelperAsync(
                    It.IsAny<WorkspaceIdentity>(),
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<AEVAPipelineSubmissionInfo>(),
                    It.IsAny<LogTelemetrySourceEnum>()))
                .Callback((WorkspaceIdentity amlWorkspaceIdentity,
                            string pipelineId,
                            string pipelineEndpointId,
                            string parentRunId,
                            string sourcePipelineRunId,
                            string runLineageType,
                            AEVAPipelineSubmissionInfo pipelineSubmissionInfo,
                            LogTelemetrySourceEnum callerSource) => callbackAEVAPipeInfo = pipelineSubmissionInfo)
                .Returns(Task.FromResult(entity));
            for (int i = 0; i < parameterAssignmentsList.Count; i++)
            {

                _fakePipelinesScheduleEntity.PipelineSubmissionInfo.ParameterAssignments = parameterAssignmentsList[i];

                for (int j = 0; j < schedulePayloadList.Count; j++)
                {
                    callbackAEVAPipeInfo = null;
                    string pipelineRunId = await _fakePipelineRunsController.SubmitPipelineRunFromScheduleAsync(
                    subscriptionId: "dummySubscriptionId",
                    resourceGroupName: "dummyResourceGroupName",
                    workspaceName: "dummyWorkspaceName",
                    scheduleId: "dummyScheduleId",
                    schedulePipelineRunPayload: schedulePayloadList[j]);

                    Assert.True(called_GetPipelineScheduleStore);
                    Assert.True(called_GetPipelinesScheduleEntity);
                    Assert.IsNotNull(callbackAEVAPipeInfo);
                    if (i == 0 && j == 0)
                    {
                        // In this case, schedule ParameterAssignments and schedulePayloadList's keys are same, so schedule ParameterAssignments will be replaced.
                        // Todo, improve code.
                        Assert.AreEqual(callbackAEVAPipeInfo.ParameterAssignments, schedulePayloadList[i].ParameterAssignments);
                    }
                    else
                    {
                        // Other cases keys are difference, so schdule will stay the same
                        Assert.AreEqual(callbackAEVAPipeInfo.ParameterAssignments, pipelineSubmissionInfo.ParameterAssignments);
                    }
                }
            }
        }

        [TestCase(null, null, null)]
        [TestCase("20220608T021535Z", null, "-20220608T021535Z")]
        [TestCase(null, "experiment-name", null)]
        [TestCase("20220608T021535Z", "experiment-name", "experiment-name-20220608T021535Z")]
        [Category("SubmitPipelineRunFromScheduleAsync")]
        public async Task SubmitPipelineRunFromScheduleAsync_TestDisplayName(string scheduleTriggerTime, string experimentName, string expectDisplayName)
        {
            var schedulePayload = new AEVASchedulePipelineRunPayload() { ScheduleTriggerTime = scheduleTriggerTime };

            var pipelineScheduleStore = _repository.Create<IPipelineScheduleStore>().Object;

            bool called_GetPipelineScheduleStore = false;
            Mock.Get(_fakeIStoreCollection)
                .Setup(controller => controller.GetPipelineScheduleStore(
                    It.IsAny<ClaimsPrincipal>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Callback(() => called_GetPipelineScheduleStore = true)
                .Returns(pipelineScheduleStore);

            PipelineScheduleEntity fakePipelinesScheduleEntity = new PipelineScheduleEntity();
            var aevaPipelineSubmissionInfo = new AEVAPipelineSubmissionInfo();
            PipelineSubmissionInfo pipelineSubmissionInfo = new PipelineSubmissionInfo();
            fakePipelinesScheduleEntity = _repository.Create<PipelineScheduleEntity>().Object;
            fakePipelinesScheduleEntity.ScheduleType = ScheduleType.Recurrence;
            fakePipelinesScheduleEntity.PipelineId = Guid.NewGuid().ToString();
            pipelineSubmissionInfo.ExperimentName = experimentName;
            fakePipelinesScheduleEntity.PipelineSubmissionInfo = pipelineSubmissionInfo;

            bool called_GetPipelinesScheduleEntity = false;
            Mock.Get(pipelineScheduleStore)
                .Setup(
                  store => store.GetEntityAsync(
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<WorkspaceIdentity>()))
                .Callback(() => called_GetPipelinesScheduleEntity = true)
                .Returns(Task.FromResult(fakePipelinesScheduleEntity));

            AEVAPipelineRunEntity entity = new AEVAPipelineRunEntity();
            string parentRunId = Guid.NewGuid().ToString();

            AEVAPipelineSubmissionInfo callbackAEVAPipeInfo = null;
            Mock.Get(_fakePipelineRunsController)
                .Setup(
                    stores => stores.SubmitPipelineRunFromPipelineHelperAsync(
                    It.IsAny<WorkspaceIdentity>(),
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<AEVAPipelineSubmissionInfo>(),
                    It.IsAny<LogTelemetrySourceEnum>()))
                .Callback((WorkspaceIdentity amlWorkspaceIdentity,
                            string pipelineId,
                            string pipelineEndpointId,
                            string parentRunId,
                            string sourcePipelineRunId,
                            string runLineageType,
                            AEVAPipelineSubmissionInfo pipelineSubmissionInfo,
                            LogTelemetrySourceEnum callerSource) => callbackAEVAPipeInfo = pipelineSubmissionInfo)
                .Returns(Task.FromResult(entity));

            string pipelineRunId = await _fakePipelineRunsController.SubmitPipelineRunFromScheduleAsync(
            subscriptionId: "dummySubscriptionId",
            resourceGroupName: "dummyResourceGroupName",
            workspaceName: "dummyWorkspaceName",
            scheduleId: "dummyScheduleId",
            schedulePipelineRunPayload: schedulePayload);

            Assert.True(called_GetPipelineScheduleStore);
            Assert.True(called_GetPipelinesScheduleEntity);
            Assert.AreEqual(callbackAEVAPipeInfo.DisplayName, expectDisplayName);
        }
    }
}
