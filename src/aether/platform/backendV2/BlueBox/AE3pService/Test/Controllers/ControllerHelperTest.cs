﻿// <copyright file="ControllerHelperTest.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Microsoft.Aether.AEVA.DataContractConverter;
using Microsoft.Aether.DataContracts;
using Moq;
using NUnit.Framework;
using AEVAGraphEntity = Microsoft.Aether.AEVA.DataContracts.GraphEntity;
using AEVAGraphDraftEntity = Microsoft.Aether.AEVA.DataContracts.GraphDraftEntity;
using AEVAEntityInterface = Microsoft.Aether.AEVA.DataContracts.EntityInterface;
using AEVAGraphLayout = Microsoft.Aether.AEVA.DataContracts.GraphLayout;
using System.Threading.Tasks;
using Microsoft.Aether.DataContracts.Entity;
using Newtonsoft.Json;
using System.Linq;
using System.Collections.Generic;
using Microsoft.Aether.BlueBox.DataStoreClient;
using AEVALegacyDataPath = Microsoft.Aether.AEVA.DataContracts.LegacyDataPath;
using AEVAGraphDatasetNode = Microsoft.Aether.AEVA.DataContracts.GraphDatasetNode;
using AEVAGraphModuleNode = Microsoft.Aether.AEVA.DataContracts.GraphModuleNode;
using AEVAGraphReferenceNode = Microsoft.Aether.AEVA.DataContracts.GraphReferenceNode;
using AEVANodePortInterface = Microsoft.Aether.AEVA.DataContracts.NodePortInterface;
using AEVANodeInputPort = Microsoft.Aether.AEVA.DataContracts.NodeInputPort;
using AEVANodeOutputPort = Microsoft.Aether.AEVA.DataContracts.NodeOutputPort;
using AEVAParameter = Microsoft.Aether.AEVA.DataContracts.Parameter;
using AEVADataPathParameter = Microsoft.Aether.AEVA.DataContracts.DataPathParameter;
using AEVAParameterType = Microsoft.Aether.AEVA.DataContracts.ParameterType;
using AEVAMinMaxParameterRule = Microsoft.Aether.AEVA.DataContracts.MinMaxParameterRule;
using AEVAEnumParameterRule = Microsoft.Aether.AEVA.DataContracts.EnumParameterRule;
using AEVASubGraphCreationInfo = Microsoft.Aether.AEVA.DataContracts.SubGraphCreationInfo;
using AEVAStructuredInterface = Microsoft.Aether.AEVA.DataContracts.StructuredInterface;
using System;
using Microsoft.Aether.DataContracts.EntityInterfaces;
using System.Threading;
using Microsoft.Aether.BlueBox.DataStoreClient.Contracts;
using AEVADataSourceCreationInfo = Microsoft.Aether.AEVA.DataContracts.DataSourceCreationInfo;
using System.Reflection;
using Microsoft.Aether.DataContracts.DataReferences;
using System.Net.Sockets;
using System.Net.Http;
using Microsoft.RelInfra.Common.Exceptions;
using Microsoft.MachineLearning.Common.Core.Exceptions;

namespace Microsoft.Aether.AE3pService.Tests.Controllers
{
    [TestFixture]
    public class ControllerHelperTest
    {
        private string _dummyWorkspaceId;
        private IGraphStore _fakeIGraphStore;
        private AEVAGraphEntity _dummyAevaGraphEntity;
        private GraphEntity _dummyGraphEntity;
        private AEVAEntityInterface _fakeAEVAEntityInterface;
        private EntityInterface _fakeEntityInterface;
        private AEVAGraphLayout _dummyAEVAGraphLayout;
        private GraphLayout _dummyGraphLayout;
        private IDatasetStore _fakeIDatasetStore;
        private IDataStoreClient _fakeIDataStoreClient;
        private IDataTypeStore _fakeIDataTypeStore;
        private IContextProvider _fakeIContextProvider;
        private Dictionary<string, AEVALegacyDataPath> _dummyDataPathAssignments;
        private DataStoreDto _fakeDataStoreDto;

        private AEVALegacyDataPath _dummyAEVALegacyDataPath;
        private LegacyDataPath _dummyLegacyDataPath;

        private IEnumerable<DataTypeEntity> _dummyDataTypeEntities;

        private readonly MockRepository _repository;

        private readonly string _dataPathKey = "name1";
        private readonly string _dummyDataTypeId = "dummyDataTypeId";

        public ControllerHelperTest()
        {
            _repository = new MockRepository(MockBehavior.Loose) { DefaultValue = DefaultValue.Mock, CallBase = true };
        }

        [SetUp]
        public void Init()
        {
            _dummyWorkspaceId = "dummy workspace id";
            _fakeIGraphStore = _repository.Create<IGraphStore>().Object;
            _dummyAevaGraphEntity = new AEVAGraphEntity();
            _dummyGraphEntity = new GraphEntity()
            {
                Id = "dummyGraphEntityId"
            };

            _fakeAEVAEntityInterface = _repository.Create<AEVAEntityInterface>().Object;
            _fakeEntityInterface = _repository.Create<EntityInterface>().Object;

            _dummyAEVAGraphLayout = new AEVAGraphLayout();
            _dummyGraphLayout = new GraphLayout();
            _fakeIDatasetStore = _repository.Create<IDatasetStore>().Object;
            _fakeIDataStoreClient = _repository.Create<IDataStoreClient>().Object;
            _fakeIDataTypeStore = _repository.Create<IDataTypeStore>().Object;
            _fakeIContextProvider = _repository.Create<IContextProvider>().Object;
            _fakeDataStoreDto = _repository.Create<DataStoreDto>().Object;

            _dummyAEVALegacyDataPath = new AEVALegacyDataPath()
            {
                DataStoreName = "dummyAEVADataStoreName",
                RelativePath = "dummyAEVARelativePath"
            };

            _dummyLegacyDataPath = new LegacyDataPath()
            {
                DataStoreName = "dummyDataStoreName",
                RelativePath = "dummyRelativePath"
            };

            _dummyDataTypeEntities = new List<DataTypeEntity>()
            {
                new DataTypeEntity()
                {
                    Id = _dummyDataTypeId
                }
            };

            Mock.Get(_fakeIDataTypeStore)
                .Setup(
                   stores => stores.ListAsync(
                       It.IsAny<string>(),
                       It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(_dummyDataTypeEntities));

            Mock.Get(_fakeIDataStoreClient)
                .Setup(
                   client => client.GetAsync(
                       It.IsAny<string>(),
                       It.IsAny<WorkspaceIdentity>(),
                       It.IsAny<CreatedBy>(),
                       It.IsAny<CancellationToken>()))
                .Returns(Task.FromResult(_fakeDataStoreDto));
        }

        [Test]
        public async Task CreateGraphAsync_Tests()
        {
            // test when aevaGraphLayout param is not null
            bool called_createAsync = false;
            var expectedEntity = ContractConverter.Convert<AEVAGraphEntity, GraphEntity>(_dummyAevaGraphEntity);

            //below is the override requiredment from the code
            expectedEntity.SubGraphNodes = Enumerable.Empty<GraphReferenceNode>();
            expectedEntity.Id = null;

            string expectedEntityJsonString = JsonConvert.SerializeObject(expectedEntity);
            Mock.Get(_fakeIGraphStore)
                .Setup(
                   stores => stores.CreateAsync(
                       _dummyWorkspaceId,
                       It.Is<GraphEntity>(entity => JsonConvert.SerializeObject(entity).Equals(expectedEntityJsonString)),
                       // It.IsAny<GraphEntity>(),
                       It.IsAny<WorkspaceIdentity>()))
                .Callback(() => called_createAsync = true)
                .Returns(Task.FromResult(_dummyGraphEntity));

            bool called_CreateGraphInterfaceAsync = false;
            string expectedEntityInterfaceJsonString = JsonConvert.SerializeObject(ContractConverter.Convert<AEVAEntityInterface, EntityInterface>(_fakeAEVAEntityInterface));
            Mock.Get(_fakeIGraphStore)
                .Setup(
                   stores => stores.CreateGraphInterfaceAsync(
                       _dummyWorkspaceId,
                       _dummyGraphEntity.Id,
                       It.Is<EntityInterface>(entityInterface => JsonConvert.SerializeObject(entityInterface).Equals(expectedEntityInterfaceJsonString)),
                       It.IsAny<WorkspaceIdentity>()))
                .Callback(() => called_CreateGraphInterfaceAsync = true)
                .Returns(Task.FromResult(_fakeEntityInterface));

            bool called_CreateLayoutAsync = false;
            string expectedGraphLayoutJsonString = JsonConvert.SerializeObject(ContractConverter.Convert<AEVAGraphLayout, DataContracts.GraphLayout>(_dummyAEVAGraphLayout));
            Mock.Get(_fakeIGraphStore)
                .Setup(
                   stores => stores.CreateLayoutAsync(
                       _dummyWorkspaceId,
                       _dummyGraphEntity.Id,
                       It.Is<GraphLayout>(layout => JsonConvert.SerializeObject(layout).Equals(expectedGraphLayoutJsonString)),
                       It.IsAny<WorkspaceIdentity>()))
                .Callback(() => called_CreateLayoutAsync = true)
                .Returns(Task.FromResult(_dummyGraphLayout));

            var actualRet = await ControllerHelper.CreateGraphAsync(
                 workspaceId: _dummyWorkspaceId,
                 graphStore: _fakeIGraphStore,
                 aevaGraph: _dummyAevaGraphEntity,
                 aevaEntityInterface: _fakeAEVAEntityInterface,
                 aevaGraphLayout: _dummyAEVAGraphLayout).ConfigureAwait(false);

            Assert.True(called_createAsync);
            Assert.True(called_CreateGraphInterfaceAsync);
            Assert.True(called_CreateLayoutAsync);
            Assert.AreEqual(_dummyGraphEntity, actualRet);

            // test when aevaGraphLayout param is null
            called_createAsync = false;
            called_CreateGraphInterfaceAsync = false;
            called_CreateLayoutAsync = false;

            actualRet = await ControllerHelper.CreateGraphAsync(
                 workspaceId: _dummyWorkspaceId,
                 graphStore: _fakeIGraphStore,
                 aevaGraph: _dummyAevaGraphEntity,
                 aevaEntityInterface: _fakeAEVAEntityInterface,
                 aevaGraphLayout: null).ConfigureAwait(false);

            Assert.True(called_createAsync);
            Assert.True(called_CreateGraphInterfaceAsync);
            Assert.False(called_CreateLayoutAsync);
            Assert.AreEqual(_dummyGraphEntity, actualRet);
        }

        [Test]
        public async Task CreateGraphForSubGraphModuleAsync_Tests()
        {
            GraphEntity createdGraphEntity = null;
            EntityInterface createdInterface = null;
            AmlGraphDraftEntity createdGraphDraftEntity = null;

            Mock<IGraphStore> mockGraphStore = new Mock<IGraphStore>();
            mockGraphStore.Setup(g => g.CreateAsync(It.IsAny<string>(), It.IsAny<GraphEntity>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(
                    (string workspaceId, GraphEntity graphEntity, WorkspaceIdentity identity) =>
                    {
                        graphEntity.Id = Guid.NewGuid().ToString();
                        createdGraphEntity = graphEntity;
                        return Task.FromResult(graphEntity);
                    });

            mockGraphStore.Setup(g => g.CreateGraphInterfaceAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<EntityInterface>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(
                    (string workspaceId, string graphId, EntityInterface entityInterface, WorkspaceIdentity identity) =>
                    {
                        createdInterface = entityInterface;
                        return Task.FromResult(entityInterface);
                    });

            Mock<IAmlGraphDraftStore> mockGraphDraftStore = new Mock<IAmlGraphDraftStore>();
            mockGraphDraftStore.Setup(g => g.CreateAsync(It.IsAny<string>(), It.IsAny<AmlGraphDraftEntity>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(
                    (string workspaceId, AmlGraphDraftEntity graphDraftEntity, WorkspaceIdentity identity) =>
                    {
                        graphDraftEntity.Id = Guid.NewGuid().ToString();
                        createdGraphDraftEntity = graphDraftEntity;
                        return Task.FromResult(graphDraftEntity);
                    });

            AEVASubGraphCreationInfo creationInfo = new AEVASubGraphCreationInfo()
            {
                CreateAsDraft = false,
                Name = "test",
                VisualGraph = new AEVA.DataContracts.VisualGraph()
                {
                    Graph = new AEVAGraphEntity()
                    {
                        ModuleNodes = new List<AEVA.DataContracts.GraphModuleNode>()
                        {
                            new AEVA.DataContracts.GraphModuleNode()
                            {
                                Id = "foo"
                            }
                        }
                    }
                },
                Interface = new AEVAEntityInterface(
                    ports: new AEVANodePortInterface(
                        inputs: new List<AEVA.DataContracts.NodeInputPort>()
                        {
                            new AEVA.DataContracts.NodeInputPort()
                            {
                                Name = "theInput"
                            }
                        },
                        outputs: new List<AEVA.DataContracts.NodeOutputPort>()
                        {
                            new AEVA.DataContracts.NodeOutputPort()
                            {
                                Name = "theOutput",
                                DataTypeId = "outputDataType"
                            }
                        },
                        Array.Empty<AEVA.DataContracts.ControlOutput>()),
                    parameters: Array.Empty<AEVAParameter>())
            };

            //  Test regular subgraph scenario
            AEVA.DataContracts.SubGraphConfiguration returnedConfig = await ControllerHelper.CreateGraphForSubGraphModuleAsync(
                workspaceId: _dummyWorkspaceId,
                graphStore: mockGraphStore.Object,
                graphDraftStore: mockGraphDraftStore.Object,
                contextProvider: _fakeIContextProvider,
                subGraphCreationInfo: creationInfo).ConfigureAwait(false);

            Assert.IsNotNull(createdGraphEntity);
            Assert.AreEqual(createdGraphEntity.Id, returnedConfig.GraphId);
            Assert.AreEqual(createdGraphEntity.ModuleNodes.First().Id, "foo");
            Assert.IsNotNull(createdInterface);
            Assert.AreEqual(createdInterface.Ports.Inputs.First().Name, "theInput");
            Assert.AreEqual(createdInterface.Ports.Outputs.First().Name, "theOutput");
            Assert.AreEqual(createdInterface.Ports.Outputs.First().DataTypeId, "outputDataType");

            //  Test subgraph draft scenario
            creationInfo.CreateAsDraft = true;
            returnedConfig = await ControllerHelper.CreateGraphForSubGraphModuleAsync(
                workspaceId: _dummyWorkspaceId,
                graphStore: mockGraphStore.Object,
                graphDraftStore: mockGraphDraftStore.Object,
                contextProvider: _fakeIContextProvider,
                subGraphCreationInfo: creationInfo).ConfigureAwait(false);

            Assert.IsNotNull(createdGraphDraftEntity);
            Assert.AreEqual(createdGraphDraftEntity.Id, returnedConfig.GraphDraftId);
            Assert.AreEqual(createdGraphDraftEntity.ModuleNodes.First().Id, "foo");
            Assert.AreEqual(createdGraphDraftEntity.EntityInterface.Ports.Inputs.First().Name, "theInput");
            Assert.AreEqual(createdGraphDraftEntity.EntityInterface.Ports.Outputs.First().Name, "theOutput");
            Assert.AreEqual(createdGraphDraftEntity.EntityInterface.Ports.Outputs.First().DataTypeId, "outputDataType");
        }

        [Test]
        public async Task GetDetailedGraphInfo_Test()
        {
            Dictionary<string, ModuleEntity> modules = new Dictionary<string, ModuleEntity>();
            Dictionary<string, DatasetEntity> datasets = new Dictionary<string, DatasetEntity>();

            Mock<IModuleStore> mockModuleStore = new Mock<IModuleStore>();
            mockModuleStore.Setup(g => g.BulkGetIdsAsync(It.IsAny<string>(), It.IsAny<string[]>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(
                    (string workspaceId, string[] moduleIds, WorkspaceIdentity identity) =>
                    {
                        return Task.FromResult(modules.Values.Where(module => moduleIds.Contains(module.Id)));
                    });

            Mock<IDatasetStore> mockDatasetStore = new Mock<IDatasetStore>();
            mockDatasetStore.Setup(g => g.BulkGetAsync(It.IsAny<string>(), It.IsAny<string[]>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(
                    (string workspaceId, string[] datasetIds, WorkspaceIdentity identity) =>
                    {
                        return Task.FromResult(datasets.Values.Where(dataset => datasetIds.Contains(dataset.Id)));
                    });

            Mock<IDataTypeStore> mockDataTypeStore = new Mock<IDataTypeStore>();
            mockDataTypeStore.Setup(g => g.ListAsync(It.IsAny<string>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(Enumerable.Empty<DataTypeEntity>()));

            modules.Add("module1", new ModuleEntity() { Id = "module1", Name = "module1 name" });
            modules.Add("module2", new ModuleEntity() { Id = "module2", Name = "module2 name" });
            modules.Add("subGraph1", new ModuleEntity() { Id = "subGraph1", Name = "subGraph1 name" });

            datasets.Add("data1", new DatasetEntity() { Id = "data1", Name = "data1 name" });
            datasets.Add("data2", new DatasetEntity() { Id = "data2", Name = "data2 name" });

            var parentGraph = new GraphEntity()
            {
                Id = "parentGraph",
                ModuleNodes = new List<GraphModuleNode>()
                {
                    new GraphModuleNode(){ Id = "m1", ModuleId = "module1"},
                    new GraphModuleNode(){ Id = "m2", ModuleId = "module2"},
                },
                DatasetNodes = new List<GraphDatasetNode>()
                {
                    new GraphDatasetNode(){ Id = "d1", DatasetId = "data1"},
                    new GraphDatasetNode(){ Id = "d2", DatasetId = "data2"},
                },
                SubGraphNodes = new List<GraphReferenceNode>()
                {
                    new GraphReferenceNode() { Id = "s1", ModuleId = "subGraph1", GraphId = "graph1"}
                }
            };

            //  2 module nodes + 1 subgraph node
            var (graphEntityWithInterface, nestedResourceInfo) = await ControllerHelper.GetDetailedGraphInfoAsync(
                ContractConverter.Convert<GraphEntity, AEVAGraphEntity>(parentGraph),
                "dummyWorkspace",
                false,
                mockDatasetStore.Object,
                mockModuleStore.Object,
                mockDataTypeStore.Object,
                null,
                null);

            var returnedModuleDictionary = nestedResourceInfo.Modules.ToDictionary(fullModule => fullModule.Data.Id, fullModule => fullModule.Data);
            var returnedDataSourceDictionary = nestedResourceInfo.DataSourceEntities.ToDictionary(dataSource => dataSource.Id, dataSource => dataSource);

            Assert.AreEqual(returnedModuleDictionary.Count, 3);
            Assert.AreEqual(returnedModuleDictionary["module1"].Name, "module1 name");
            Assert.AreEqual(returnedModuleDictionary["module2"].Name, "module2 name");
            Assert.AreEqual(returnedModuleDictionary["subGraph1"].Name, "subGraph1 name");

            Assert.AreEqual(returnedDataSourceDictionary.Count, 2);
            Assert.AreEqual(returnedDataSourceDictionary["data1"].Name, "data1 name");
            Assert.AreEqual(returnedDataSourceDictionary["data2"].Name, "data2 name");

            //   2 module nodes, null subgraph node list
            parentGraph.SubGraphNodes = null;
            (graphEntityWithInterface, nestedResourceInfo) = await ControllerHelper.GetDetailedGraphInfoAsync(
                ContractConverter.Convert<GraphEntity, AEVAGraphEntity>(parentGraph),
                "dummyWorkspace",
                false,
                mockDatasetStore.Object,
                mockModuleStore.Object,
                mockDataTypeStore.Object,
                null,
                null);
            returnedModuleDictionary = nestedResourceInfo.Modules.ToDictionary(fullModule => fullModule.Data.Id, fullModule => fullModule.Data);
            returnedDataSourceDictionary = nestedResourceInfo.DataSourceEntities.ToDictionary(dataSource => dataSource.Id, dataSource => dataSource);

            Assert.AreEqual(returnedModuleDictionary.Count, 2);
            Assert.AreEqual(returnedModuleDictionary["module1"].Name, "module1 name");

            //  Null module node list, one subgraph node
            parentGraph.ModuleNodes = null;
            parentGraph.SubGraphNodes = new List<GraphReferenceNode>()
            {
                new GraphReferenceNode() { Id = "s1", ModuleId = "subGraph1", GraphId = "graph1"}
            };

            (graphEntityWithInterface, nestedResourceInfo) = await ControllerHelper.GetDetailedGraphInfoAsync(
                ContractConverter.Convert<GraphEntity, AEVAGraphEntity>(parentGraph),
                "dummyWorkspace",
                false,
                mockDatasetStore.Object,
                mockModuleStore.Object,
                mockDataTypeStore.Object,
                null,
                null);
            returnedModuleDictionary = nestedResourceInfo.Modules.ToDictionary(fullModule => fullModule.Data.Id, fullModule => fullModule.Data);
            returnedDataSourceDictionary = nestedResourceInfo.DataSourceEntities.ToDictionary(dataSource => dataSource.Id, dataSource => dataSource);

            Assert.AreEqual(returnedModuleDictionary.Count, 1);
            Assert.AreEqual(returnedModuleDictionary["subGraph1"].Name, "subGraph1 name");

            AEVAGraphDraftEntity graphDraft = new AEVAGraphDraftEntity()
            {
                ModuleNodes = new List<AEVAGraphModuleNode>()
                {
                    new AEVAGraphModuleNode(){ Id = "m1", ModuleId = "module1"},
                    new AEVAGraphModuleNode(){ Id = "m2", ModuleId = "module2"},
                },
                SubGraphNodes = new List<AEVAGraphReferenceNode>()
                {
                    new AEVAGraphReferenceNode() { Id = "s1", ModuleId = "subGraph1", GraphId = "graph1"}
                }
            };

            //  Graph draft scenario, 2 module nodes, 1 subgraph node, null dataset node list
            nestedResourceInfo = await ControllerHelper.GetDetailedGraphInfoAsync(
                graphDraft,
                "dummyWorkspace",
                false,
                mockDatasetStore.Object,
                mockModuleStore.Object,
                mockDataTypeStore.Object,
                null,
                null);
            returnedModuleDictionary = nestedResourceInfo.Modules.ToDictionary(fullModule => fullModule.Data.Id, fullModule => fullModule.Data);
            returnedDataSourceDictionary = nestedResourceInfo.DataSourceEntities.ToDictionary(dataSource => dataSource.Id, dataSource => dataSource);

            Assert.AreEqual(returnedModuleDictionary.Count, 3);
            Assert.AreEqual(returnedModuleDictionary["subGraph1"].Name, "subGraph1 name");
            Assert.AreEqual(returnedDataSourceDictionary.Count, 0);
        }

        [Test]
        public async Task GetNestedGraphInfo_Test()
        {
            Dictionary<string, ModuleEntity> modules = new Dictionary<string, ModuleEntity>();
            Dictionary<string, DatasetEntity> datasets = new Dictionary<string, DatasetEntity>();
            Dictionary<string, VisualGraphWithEntityInterface> graphs = new Dictionary<string, VisualGraphWithEntityInterface>();
            Dictionary<string, AmlGraphDraftEntity> graphDrafts = new Dictionary<string, AmlGraphDraftEntity>();

            Mock<IModuleStore> mockModuleStore = new Mock<IModuleStore>();
            mockModuleStore.Setup(g => g.BulkGetIdsAsync(It.IsAny<string>(), It.IsAny<string[]>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(
                    (string workspaceId, string[] moduleIds, WorkspaceIdentity identity) =>
                    {
                        return Task.FromResult(modules.Values.Where(module => moduleIds.Contains(module.Id)));
                    });

            Mock<IDatasetStore> mockDatasetStore = new Mock<IDatasetStore>();
            mockDatasetStore.Setup(g => g.BulkGetAsync(It.IsAny<string>(), It.IsAny<string[]>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(
                    (string workspaceId, string[] datasetIds, WorkspaceIdentity identity) =>
                    {
                        return Task.FromResult(datasets.Values.Where(dataset => datasetIds.Contains(dataset.Id)));
                    });

            Mock<IDataTypeStore> mockDataTypeStore = new Mock<IDataTypeStore>();
            mockDataTypeStore.Setup(g => g.ListAsync(It.IsAny<string>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(Enumerable.Empty<DataTypeEntity>()));

            Mock<IGraphStore> mockGraphStore = new Mock<IGraphStore>();
            mockGraphStore.Setup(g => g.GetEntityAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(
                    (string workspaceId, string graphId, WorkspaceIdentity identity) =>
                    {
                        return Task.FromResult(graphs[graphId].VisualGraph.Graph);
                    });
            mockGraphStore.Setup(g => g.TryGetLayoutAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(
                    (string workspaceId, string graphId, WorkspaceIdentity identity) =>
                    {
                        return Task.FromResult(graphs[graphId].VisualGraph.Layout);
                    });
            mockGraphStore.Setup(g => g.TryGetGraphInterfaceAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(
                    (string workspaceId, string graphId, WorkspaceIdentity identity) =>
                    {
                        return Task.FromResult(graphs[graphId].Interface);
                    });

            Mock<IAmlGraphDraftStore> mockGraphDraftStore = new Mock<IAmlGraphDraftStore>();
            mockGraphDraftStore.Setup(g => g.GetEntityAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(
                    (string workspaceId, string graphId, WorkspaceIdentity identity) =>
                    {
                        return Task.FromResult(graphDrafts[graphId]);
                    });

            modules.Add("module1", new ModuleEntity() { Id = "module1", Name = "module1 name" });
            modules.Add("module2", new ModuleEntity() { Id = "module2", Name = "module2 name" });
            modules.Add("module3", new ModuleEntity() { Id = "module3", Name = "module3 name" });
            modules.Add("subGraph1", new ModuleEntity() { Id = "subGraph1", Name = "subGraph1 name" });
            modules.Add("subGraph2", new ModuleEntity()
            {
                Id = "subGraph2",
                Name = "subGraph2 name",
                CloudSettings = new CloudSettings()
                {
                    SubGraphConfig = new SubGraphConfiguration()
                    {
                        GraphDraftId = "subgraphDraft"
                    }
                }
            });

            datasets.Add("data1", new DatasetEntity() { Id = "data1", Name = "data1 name" });
            datasets.Add("data2", new DatasetEntity() { Id = "data2", Name = "data2 name" });

            //  Parent graph has a subgraph which points to graph1
            graphs.Add("parentGraph", new VisualGraphWithEntityInterface()
            {
                VisualGraph = new VisualGraph()
                {
                    Graph = new GraphEntity()
                    {
                        Id = "parentGraph",
                        ModuleNodes = new List<GraphModuleNode>()
                        {
                            new GraphModuleNode(){ Id = "m1", ModuleId = "module1"},
                            new GraphModuleNode(){ Id = "m2", ModuleId = "module2"},
                        },
                        DatasetNodes = new List<GraphDatasetNode>()
                        {
                            new GraphDatasetNode(){ Id = "d1", DatasetId = "data1"},
                            new GraphDatasetNode(){ Id = "d2", DatasetId = "data2"},
                        },
                        SubGraphNodes = new List<GraphReferenceNode>()
                        {
                            new GraphReferenceNode() { Id = "s1", ModuleId = "subGraph1", GraphId = "graph1"}
                        }
                    },
                    Layout = new GraphLayout()
                },
                Interface = new EntityInterface()
            });

            //  graph1 has a nested subgraph node which points to graph draft subgraphDraft
            graphs.Add("graph1", new VisualGraphWithEntityInterface()
            {
                VisualGraph = new VisualGraph()
                {
                    Graph = new GraphEntity()
                    {
                        Id = "graph1",
                        ModuleNodes = new List<GraphModuleNode>()
                        {
                            new GraphModuleNode(){ Id = "m1", ModuleId = "module1"},
                        },
                        DatasetNodes = new List<GraphDatasetNode>()
                        {
                            new GraphDatasetNode(){ Id = "d1", DatasetId = "data1"}
                        },
                        SubGraphNodes = new List<GraphReferenceNode>()
                        {
                            new GraphReferenceNode() { Id = "s2", ModuleId = "subGraph2"}
                        }
                    },
                    Layout = new GraphLayout()
                },
                Interface = new EntityInterface()
            });

            //  subgraphDraft includes module3
            graphDrafts.Add("subgraphDraft", new AmlGraphDraftEntity()
            {
                Id = "subgraphDraft",
                ModuleNodes = new List<GraphModuleNode>()
                {
                    new GraphModuleNode(){ Id = "m3", ModuleId = "module3"},
                },
            });

            var nestedResourceInfo = await ControllerHelper.GetNestedResourceInfoAsync(
                moduleIds: null,
                datasetIds: null,
                graphIds: new List<string>() { "parentGraph" },
                graphDraftIds: null,
                workSpaceId: "dummyWorkspace",
                populateRunconfig: false,
                fetchNestedGraphs: true,
                mockDatasetStore.Object,
                mockModuleStore.Object,
                mockDataTypeStore.Object,
                mockGraphStore.Object,
                mockGraphDraftStore.Object);

            var returnedModuleDictionary = nestedResourceInfo.Modules.ToDictionary(fullModule => fullModule.Data.Id, fullModule => fullModule.Data);
            var returnedDataSourceDictionary = nestedResourceInfo.DataSourceEntities.ToDictionary(dataSource => dataSource.Id, dataSource => dataSource);

            Assert.AreEqual(returnedModuleDictionary.Count, 5);
            Assert.AreEqual(returnedModuleDictionary["module1"].Name, "module1 name");
            Assert.AreEqual(returnedModuleDictionary["module2"].Name, "module2 name");
            Assert.AreEqual(returnedModuleDictionary["module3"].Name, "module3 name");
            Assert.AreEqual(returnedModuleDictionary["subGraph1"].Name, "subGraph1 name");
            Assert.AreEqual(returnedModuleDictionary["subGraph2"].Name, "subGraph2 name");
            Assert.AreEqual(returnedDataSourceDictionary.Count, 2);
            Assert.AreEqual(returnedDataSourceDictionary["data1"].Name, "data1 name");
            Assert.AreEqual(returnedDataSourceDictionary["data2"].Name, "data2 name");
            Assert.AreEqual(nestedResourceInfo.Graphs.Count, 2);
            Assert.AreEqual(nestedResourceInfo.Graphs["parentGraph"].VisualGraph.Graph.ModuleNodes.Count(), 2);
            Assert.AreEqual(nestedResourceInfo.Graphs["graph1"].VisualGraph.Graph.DatasetNodes.Count(), 1);
            Assert.AreEqual(nestedResourceInfo.GraphDrafts.Count, 1);
            Assert.AreEqual(nestedResourceInfo.GraphDrafts["subgraphDraft"].ModuleNodes.Count(), 1);

            //  Test with fetchNestedGraphs=False
            nestedResourceInfo = await ControllerHelper.GetNestedResourceInfoAsync(
                moduleIds: null,
                datasetIds: null,
                graphIds: new List<string>() { "parentGraph" },
                graphDraftIds: null,
                workSpaceId: "dummyWorkspace",
                populateRunconfig: false,
                fetchNestedGraphs: false,
                mockDatasetStore.Object,
                mockModuleStore.Object,
                mockDataTypeStore.Object,
                mockGraphStore.Object,
                mockGraphDraftStore.Object);

            returnedModuleDictionary = nestedResourceInfo.Modules.ToDictionary(fullModule => fullModule.Data.Id, fullModule => fullModule.Data);
            returnedDataSourceDictionary = nestedResourceInfo.DataSourceEntities.ToDictionary(dataSource => dataSource.Id, dataSource => dataSource);
            Assert.AreEqual(returnedModuleDictionary.Count, 3);
            Assert.AreEqual(nestedResourceInfo.Graphs.Count, 1);
            Assert.AreEqual(nestedResourceInfo.GraphDrafts.Count, 0);
        }

        [Test]
        public void FillInDataPaths_Tests()
        {
            _fakeAEVAEntityInterface = _repository.Of<AEVAEntityInterface>()
                .Where(entity =>
                    entity.DataPathParameters == new List<AEVADataPathParameter>()
                    {
                           new AEVADataPathParameter(
                                _dataPathKey,
                                "document1",
                                _dummyAEVALegacyDataPath,
                                false,
                                _dummyDataTypeId)
                    })
                .First();

            // test exception throw out when evaluatedParameter == null
            _dummyDataPathAssignments = new Dictionary<string, AEVALegacyDataPath>()
            {
                {_dataPathKey, null}
            };

            Assert.IsNull(_dummyDataPathAssignments.GetValueOrDefault(_fakeAEVAEntityInterface.DataPathParameters.ElementAt(0).Name));
            _dummyAevaGraphEntity.DatasetNodes = new List<AEVAGraphDatasetNode>()
            {
              new AEVAGraphDatasetNode()
              {
                DataPathParameterName = _dataPathKey+_dataPathKey,  //make the name not match
                Id = "dummyDatasetId"
              }
            };
            Assert.AreNotEqual(_dummyAevaGraphEntity.DatasetNodes.ElementAt(0).DataPathParameterName, _fakeAEVAEntityInterface.DataPathParameters.ElementAt(0).Name);

            var actualExp = Assert.ThrowsAsync<BaseException>(async () => await ControllerHelper.FillInDataPaths(
                  aevaGraph: _dummyAevaGraphEntity,
                  aevaEntityInterface: _fakeAEVAEntityInterface,
                  dataPathAssignments: _dummyDataPathAssignments,
                  workspaceIdentity: new WorkspaceIdentity(),
                  datasetStore: _fakeIDatasetStore,
                  dataStoreClient: _fakeIDataStoreClient,
                  dataTypeStore: _fakeIDataTypeStore,
                  defaultWorkspaceId: "defaultWorkspaceId",
                  contextProvider: _fakeIContextProvider)
             .ConfigureAwait(false));
            Assert.AreEqual($"Name is invalid. Data path parameter {_fakeAEVAEntityInterface.DataPathParameters.ElementAt(0).Name} is missing both default value and assignment.", actualExp.Message);

            // test argument exception when !dataPathParamNameToDatasetEntity.ContainsKey(datasetNode.DataPathParameterName)

            // below is to make sure  evaluatedParameter != null
            _dummyDataPathAssignments = new Dictionary<string, AEVALegacyDataPath>()
            {
                {_dataPathKey, _dummyAEVALegacyDataPath}
            };

            Assert.True(_dummyDataPathAssignments.ContainsKey(_fakeAEVAEntityInterface.DataPathParameters.ElementAt(0).Name));

            Assert.AreNotEqual(_dummyAevaGraphEntity.DatasetNodes.ElementAt(0).DataPathParameterName, _fakeAEVAEntityInterface.DataPathParameters.ElementAt(0).Name);

            actualExp = Assert.ThrowsAsync<BaseException>(async () => await ControllerHelper.FillInDataPaths(
                 aevaGraph: _dummyAevaGraphEntity,
                 aevaEntityInterface: _fakeAEVAEntityInterface,
                 dataPathAssignments: _dummyDataPathAssignments,
                 workspaceIdentity: new WorkspaceIdentity(),
                 datasetStore: _fakeIDatasetStore,
                 dataStoreClient: _fakeIDataStoreClient,
                 dataTypeStore: _fakeIDataTypeStore,
                 defaultWorkspaceId: "defaultWorkspaceId",
                 contextProvider: _fakeIContextProvider)
            .ConfigureAwait(false));

            Assert.AreEqual($"DataPathParameterName is invalid. Parameter {_dummyAevaGraphEntity.DatasetNodes.ElementAt(0).DataPathParameterName} not found for node {_dummyAevaGraphEntity.DatasetNodes.ElementAt(0).Id}.", actualExp.Message);

            // test regular execution

            _dummyAevaGraphEntity.DatasetNodes = new List<AEVAGraphDatasetNode>()
            {
              new AEVAGraphDatasetNode()
              {
                DataPathParameterName = _dataPathKey,  //make the name not match
                Id = "dummyDatasetId"
              }
            };

            Assert.AreEqual(_dummyAevaGraphEntity.DatasetNodes.ElementAt(0).DataPathParameterName, _fakeAEVAEntityInterface.DataPathParameters.ElementAt(0).Name);

            Assert.DoesNotThrowAsync(async () => await ControllerHelper.FillInDataPaths(
                 aevaGraph: _dummyAevaGraphEntity,
                 aevaEntityInterface: _fakeAEVAEntityInterface,
                 dataPathAssignments: _dummyDataPathAssignments,
                 workspaceIdentity: new WorkspaceIdentity(),
                 datasetStore: _fakeIDatasetStore,
                 dataStoreClient: _fakeIDataStoreClient,
                 dataTypeStore: _fakeIDataTypeStore,
                 defaultWorkspaceId: "defaultWorkspaceId",
                 contextProvider: _fakeIContextProvider)
            .ConfigureAwait(false));
        }

        [Test]
        public void GetDefaultParameters_AEVAEntityInterface_Tests()
        {
            // test return empty dictionary
            Assert.IsNull(_fakeAEVAEntityInterface.Parameters);
            var actualRet = ControllerHelper.GetDefaultParameters(_fakeAEVAEntityInterface);
            Assert.AreEqual(0, actualRet.Count());

            // test with non-empty dictionary
            _fakeAEVAEntityInterface = _repository.Of<AEVAEntityInterface>()
                            .Where(entity =>
                                entity.Parameters == new List<AEVAParameter>()
                                {
                                     new AEVAParameter(
                                          "name1",
                                          "documentation1",
                                          true,
                                          AEVAParameterType.String,
                                          new List<AEVAMinMaxParameterRule>(),
                                          new List<AEVAEnumParameterRule>(),
                                          null,
                                          null,
                                          null),
                                      new AEVAParameter(
                                          "name2",
                                          "documentation2",
                                          true,
                                          AEVAParameterType.String,
                                          new List<AEVAMinMaxParameterRule>(),
                                          new List<AEVAEnumParameterRule>(),
                                          null,
                                          null,
                                          null)
                                })
                            .First();

            actualRet = ControllerHelper.GetDefaultParameters(_fakeAEVAEntityInterface);
            Assert.AreEqual(_fakeAEVAEntityInterface.Parameters.Count(), actualRet.Count());
        }

        [Test]
        public void CreateStructuredInterfaceFromEntityInterface_Text()
        {
            AEVAEntityInterface entityInterface = new AEVAEntityInterface(
                ports: new AEVANodePortInterface(
                    inputs: new List<AEVANodeInputPort>()
                    {
                        new AEVANodeInputPort()
                        {
                            Name = "input",
                            IsOptional = true,
                            DataTypesIds = new List<string>() {"inputType"}
                        }
                    },
                    outputs: new List<AEVANodeOutputPort>()
                    {
                        new AEVANodeOutputPort()
                        {
                            Name = "output",
                            DataTypeId = "outputType"
                        }
                    },
                    Array.Empty<AEVA.DataContracts.ControlOutput>()),
                parameters: new List<AEVAParameter>()
                {
                    new AEVAParameter(
                        name: "param",
                        defaultValue: "default",
                        isOptional: false,
                        type: AEVAParameterType.String,
                        minMaxRules: new List<AEVAMinMaxParameterRule>(),
                        enumRules: new List<AEVAEnumParameterRule>(),
                        label: "paramLabel",
                        groupNames: new List<string>() { "group" },
                        argumentName: "argumentName")
                },
                metadata: new List<AEVAParameter>()
                {
                    new AEVAParameter(
                        name: "param2",
                        defaultValue: null,
                        isOptional: true,
                        type: AEVAParameterType.Int,
                        minMaxRules: new List<AEVAMinMaxParameterRule>(),
                        enumRules: new List<AEVAEnumParameterRule>(),
                        label: null,
                        groupNames: null,
                        argumentName: null)
                });

            AEVAStructuredInterface returnedInterface = ControllerHelper.CreateStructuredInterfaceFromEntityInterface(entityInterface);
            Assert.NotNull(returnedInterface);
            Assert.AreEqual(returnedInterface.Inputs.Count, 1);
            Assert.AreEqual(returnedInterface.Inputs.First().Name, "input");
            Assert.AreEqual(returnedInterface.Inputs.First().IsOptional, true);
            Assert.AreEqual(returnedInterface.Inputs.First().DataTypeIdsList[0], "inputType");
            Assert.AreEqual(returnedInterface.Outputs.First().Name, "output");
            Assert.AreEqual(returnedInterface.Outputs.First().DataTypeId, "outputType");
            Assert.AreEqual(returnedInterface.Parameters.First().Name, "param");
            Assert.AreEqual(returnedInterface.Parameters.First().DefaultValue, "default");
            Assert.AreEqual(returnedInterface.Parameters.First().ParameterType, AEVAParameterType.String);
            Assert.AreEqual(returnedInterface.Parameters.First().Label, "paramLabel");
            Assert.AreEqual(returnedInterface.Parameters.First().GroupNames.First(), "group");
            Assert.AreEqual(returnedInterface.Parameters.First().ArgumentName, "argumentName");
            Assert.AreEqual(returnedInterface.MetadataParameters.First().Name, "param2");
            Assert.AreEqual(returnedInterface.MetadataParameters.First().DefaultValue, null);
            Assert.AreEqual(returnedInterface.MetadataParameters.First().ParameterType, AEVAParameterType.Int);
            Assert.AreEqual(returnedInterface.MetadataParameters.First().Label, null);
            Assert.AreEqual(returnedInterface.MetadataParameters.First().GroupNames, null);
            Assert.AreEqual(returnedInterface.MetadataParameters.First().ArgumentName, null);

            //  Test for null values
            AEVAEntityInterface emptyInterface = new AEVAEntityInterface();
            returnedInterface = ControllerHelper.CreateStructuredInterfaceFromEntityInterface(emptyInterface);
            Assert.NotNull(returnedInterface);
            Assert.AreEqual(returnedInterface.Inputs.Count, 0);
            Assert.AreEqual(returnedInterface.Outputs.Count, 0);
            Assert.AreEqual(returnedInterface.Parameters.Count, 0);
            Assert.AreEqual(returnedInterface.MetadataParameters.Count, 0);
        }

        [Test]
        public void GetDefaultDatapathParameters__EntityInterface_Tests()
        {
            // test return empty dictionary
            Assert.IsNull(_fakeEntityInterface.DataPathParameters);
            var actualRet = ControllerHelper.GetDefaultDatapathParameters(_fakeEntityInterface);
            Assert.AreEqual(0, actualRet.Count());

            // test with non-empty dictionary
            _fakeEntityInterface = _repository.Of<EntityInterface>()
                            .Where(entity =>
                                entity.DataPathParameters == new List<DataPathParameter>()
                                {
                                     new DataPathParameter(
                                         "name1",
                                         "documentation1",
                                         null,
                                         true,
                                         "dataTypeId1"),
                                     new DataPathParameter(
                                         "name2",
                                         "documentation2",
                                         null,
                                         true,
                                         "dataTypeId2")
                                })
                            .First();

            actualRet = ControllerHelper.GetDefaultDatapathParameters(_fakeEntityInterface);
            Assert.AreEqual(_fakeEntityInterface.DataPathParameters.Count(), actualRet.Count());
        }

        [Test]
        public void GetDefaultDatapathParameters__AEVAEntityInterface_Tests()
        {
            // test return empty dictionary
            Assert.IsNull(_fakeAEVAEntityInterface.DataPathParameters);
            var actualRet = ControllerHelper.GetDefaultDatapathParameters(_fakeAEVAEntityInterface);
            Assert.AreEqual(0, actualRet.Count());

            // test with non-empty dictionary
            _fakeAEVAEntityInterface = _repository.Of<AEVAEntityInterface>()
                            .Where(entity =>
                                entity.DataPathParameters == new List<AEVADataPathParameter>()
                                {
                                     new AEVADataPathParameter(
                                         "name1",
                                         "documentation1",
                                         null,
                                         true,
                                         "dataTypeId1"),
                                     new AEVADataPathParameter(
                                         "name2",
                                         "documentation2",
                                         null,
                                         true,
                                         "dataTypeId2")
                                })
                            .First();

            actualRet = ControllerHelper.GetDefaultDatapathParameters(_fakeAEVAEntityInterface);
            Assert.AreEqual(_fakeAEVAEntityInterface.DataPathParameters.Count(), actualRet.Count());
        }

        [Test]
        public void GetDefaultDatapathParametersAsStrings_Tests()
        {
            // test return empty dictionary
            Assert.IsNull(_fakeAEVAEntityInterface.DataPathParameters);
            var actualRet = ControllerHelper.GetDefaultDatapathParametersAsStrings(_fakeAEVAEntityInterface);
            Assert.AreEqual(0, actualRet.Count());

            // test with non-empty dictionary
            _fakeAEVAEntityInterface = _repository.Of<AEVAEntityInterface>()
                            .Where(entity =>
                                entity.DataPathParameters == new List<AEVADataPathParameter>()
                                {
                                     new AEVADataPathParameter(
                                         "name1",
                                         "documentation1",
                                         _dummyAEVALegacyDataPath,
                                         true,
                                         "dataTypeId1"),
                                     new AEVADataPathParameter(
                                         "name2",
                                         "documentation2",
                                         null,
                                         true,
                                         "dataTypeId2")
                                })
                            .First();

            actualRet = ControllerHelper.GetDefaultDatapathParametersAsStrings(_fakeAEVAEntityInterface);
            Assert.AreEqual(_fakeAEVAEntityInterface.DataPathParameters.Count(), actualRet.Count());
            Assert.AreEqual($"{_dummyAEVALegacyDataPath.DataStoreName}/{_dummyAEVALegacyDataPath.RelativePath}", actualRet[_fakeAEVAEntityInterface.DataPathParameters.ElementAt(0).Name]);
            Assert.AreEqual("/", actualRet[_fakeAEVAEntityInterface.DataPathParameters.ElementAt(1).Name]);
        }

        [Test]
        public void GetDefaultDataSetPathParameters_Tests()
        {
            // test return empty dictionary
            Assert.IsNull(_fakeAEVAEntityInterface.DataPathParameters);
            var actualRet = ControllerHelper.GetDefaultDataSetPathParameters(_fakeEntityInterface);
            Assert.AreEqual(0, actualRet.Count());

            // test with non-empty dictionary
            _fakeEntityInterface = _repository.Of<EntityInterface>()
                            .Where(entity =>
                                entity.DataPathParameterList == new List<DataSetPathParameter>()
                                {
                                     new DataSetPathParameter(
                                         "name1",
                                         "documentation1",
                                         new DataSetDefinitionValue(),
                                         true,
                                         "dataTypeId1"),
                                     new DataSetPathParameter(
                                         "name2",
                                         "documentation2",
                                         null,
                                         true,
                                         "dataTypeId2")
                                })
                            .First();

            int count = 0;
            foreach (DataSetPathParameter param in _fakeEntityInterface.DataPathParameterList)
            {
                if (param.DefaultValue != null)
                {
                    count++;
                }
            }

            actualRet = ControllerHelper.GetDefaultDataSetPathParameters(_fakeEntityInterface);
            Assert.AreEqual(count, actualRet.Count());
        }

        [Test]
        public async Task CreateDatasetEntityWithReferenceAsync_Tests()
        {
            MethodInfo method_CreateDatasetEntityWithReferenceAsync = typeof(ControllerHelper).GetMethod("CreateDatasetEntityWithReferenceAsync", BindingFlags.Static | BindingFlags.NonPublic);

            AEVADataSourceCreationInfo _dummyCreationInfo = new AEVADataSourceCreationInfo()
            {
                DataTypeId = _dummyDataTypeId + _dummyDataTypeId,
                PathOnDataStore = "dummyPathOnDataStore"
            };

            var dummyDatasetEntity = new DatasetEntity();

            Mock.Get(_fakeIDatasetStore)
                .Setup(
                   stores => stores.CreateAsync(
                       _dummyWorkspaceId,
                       It.IsAny<DatasetEntity>(),
                       It.IsAny<WorkspaceIdentity>(),
                       It.IsAny<CreatedBy>()))
                .Returns(Task.FromResult(dummyDatasetEntity));

            // test when ArgumentException throws when !dataTypeNames.Contains(providedDataType)
            Assert.AreNotEqual(_dummyCreationInfo.DataTypeId, _dummyDataTypeEntities.ElementAt(0).Id);
            var task = (Task<DatasetEntity>)method_CreateDatasetEntityWithReferenceAsync.Invoke(null, new object[] {

                 new WorkspaceIdentity(),
                 _dummyCreationInfo,
                 _fakeIDatasetStore,
                 _fakeIDataStoreClient,
                 _fakeIDataTypeStore,
                 _dummyWorkspaceId,
                 _fakeIContextProvider
            });

            var actualExp = Assert.ThrowsAsync<BaseException>(async () => await task.ConfigureAwait(false));
            StringAssert.Contains("Please create DataTypeId before uploading datasource.", actualExp.Message);

            // test when  normal executed
            _dummyCreationInfo = new AEVADataSourceCreationInfo()
            {
                DataTypeId = null,
                PathOnDataStore = "dummyPathOnDataStore"
            };

            _fakeDataStoreDto.AzureStorageSection = new AzureStorageDto()
            {
                ContainerName = "dummyContainerName",
                AccountName = "dummyAccountName",
                SasToken = "dummySasToken",
            };

            _fakeDataStoreDto.AzureDataLakeSection = new AzureDataLakeDto()
            {
                SubscriptionId = Guid.NewGuid(),
                ResourceGroup = "dummyResourceGroup",
                StoreName = "dummyStoreName",
                TenantId = Guid.NewGuid()
            };

            _fakeDataStoreDto.AzureSqlDatabaseSection = new AzureSqlDatabaseDto()
            {
                ServerName = "localhost",
                Endpoint = "dummyEndPoint",
                DatabaseName = "dummyDBName"
            };

            _fakeDataStoreDto.AzurePostgreSqlSection = new AzurePostgresDatabaseDto()
            {
                ServerName = "localhost",
                Endpoint = "dummyEndPoint",
                DatabaseName = "dummyDBName"
            };

            _fakeDataStoreDto.AzureMySqlSection = new AzureMySqlDatabaseDto()
            {
                ServerName = "localhost",
                Endpoint = "dummyEndPoint",
                DatabaseName = "dummyDBName"
            };

            _fakeDataStoreDto.CustomSection = new CustomDto()
            {
                Credential = string.Empty,
                DatastoreType = "ManagedNFS",
                Properties = new Dictionary<string, string> { { "container", "dummycontainername" } }
            };

            _fakeDataStoreDto.HdfsSection = new HdfsDto()
            {
                KerberosKdcAddress = "KerberosKDCAddress",
                CredentialValue = "KerberosKeytabSecret",
                CredentialType = HdfsCredentialType.KerberosKeytab,
                KerberosRealm = "KerberosRealmTest",
                KerberosPrincipal = "KerberosPrincipal",
                NameNodeAddress = "NameNodeAddressTest",
                Protocol = "protocol",
            };

            // test when  type is AzureBlobReference
            _fakeDataStoreDto.DataStoreType = DataStoreType.AzureBlob;
            task = (Task<DatasetEntity>)method_CreateDatasetEntityWithReferenceAsync.Invoke(null, new object[] {

                 new WorkspaceIdentity(),
                 _dummyCreationInfo,
                 _fakeIDatasetStore,
                 _fakeIDataStoreClient,
                 _fakeIDataTypeStore,
                 _dummyWorkspaceId,
                 _fakeIContextProvider
            });

            var actualRet = await task.ConfigureAwait(false);

            Mock.Get(_fakeIDatasetStore)
                .Verify(
                   stores => stores.CreateAsync(
                       _dummyWorkspaceId,
                       It.Is<DatasetEntity>(entity => entity.DataTypeId == "AzureBlobReference" && entity.DataLocation.DataReference.Type == DataReferenceType.AzureBlob),
                       It.IsAny<WorkspaceIdentity>(),
                       It.IsAny<CreatedBy>()),
                   Times.Once);
            Assert.AreEqual(dummyDatasetEntity, actualRet);

            // test when  type is AzureFile
            _fakeDataStoreDto.DataStoreType = DataStoreType.AzureFile;
            task = (Task<DatasetEntity>)method_CreateDatasetEntityWithReferenceAsync.Invoke(null, new object[] {

                 new WorkspaceIdentity(),
                 _dummyCreationInfo,
                 _fakeIDatasetStore,
                 _fakeIDataStoreClient,
                 _fakeIDataTypeStore,
                 _dummyWorkspaceId,
                 _fakeIContextProvider
            });

            actualRet = await task.ConfigureAwait(false);

            Mock.Get(_fakeIDatasetStore)
                .Verify(
                   stores => stores.CreateAsync(
                       _dummyWorkspaceId,
                       It.Is<DatasetEntity>(entity => entity.DataTypeId == "AzureFilesReference" && entity.DataLocation.DataReference.Type == DataReferenceType.AzureFiles),
                       It.IsAny<WorkspaceIdentity>(),
                       It.IsAny<CreatedBy>()),
                   Times.Once);
            Assert.AreEqual(dummyDatasetEntity, actualRet);

            // test when  type is AzureDataLake
            _fakeDataStoreDto.DataStoreType = DataStoreType.AzureDataLake;
            task = (Task<DatasetEntity>)method_CreateDatasetEntityWithReferenceAsync.Invoke(null, new object[] {

                 new WorkspaceIdentity(),
                 _dummyCreationInfo,
                 _fakeIDatasetStore,
                 _fakeIDataStoreClient,
                 _fakeIDataTypeStore,
                 _dummyWorkspaceId,
                 _fakeIContextProvider
            });

            actualRet = await task.ConfigureAwait(false);

            Mock.Get(_fakeIDatasetStore)
                .Verify(
                   stores => stores.CreateAsync(
                       _dummyWorkspaceId,
                       It.Is<DatasetEntity>(entity => entity.DataTypeId == "AzureDataLakeReference" && entity.DataLocation.DataReference.Type == DataReferenceType.AzureDataLake),
                       It.IsAny<WorkspaceIdentity>(),
                       It.IsAny<CreatedBy>()),
                   Times.Once);
            Assert.AreEqual(dummyDatasetEntity, actualRet);

            // test when  type is AzureSqlDatabase
            _fakeDataStoreDto.DataStoreType = DataStoreType.AzureSqlDatabase;
            task = (Task<DatasetEntity>)method_CreateDatasetEntityWithReferenceAsync.Invoke(null, new object[] {

                 new WorkspaceIdentity(),
                 _dummyCreationInfo,
                 _fakeIDatasetStore,
                 _fakeIDataStoreClient,
                 _fakeIDataTypeStore,
                 _dummyWorkspaceId,
                 _fakeIContextProvider
            });

            actualRet = await task.ConfigureAwait(false);

            Mock.Get(_fakeIDatasetStore)
                .Verify(
                   stores => stores.CreateAsync(
                       _dummyWorkspaceId,
                       It.Is<DatasetEntity>(entity => entity.DataTypeId == "AzureSqlDatabaseReference" && entity.DataLocation.DataReference.Type == DataReferenceType.AzureSqlDatabase),
                       It.IsAny<WorkspaceIdentity>(),
                       It.IsAny<CreatedBy>()),
                   Times.Once);
            Assert.AreEqual(dummyDatasetEntity, actualRet);

            // test when  type is AzurePostgreSql
            _fakeDataStoreDto.DataStoreType = DataStoreType.AzurePostgreSql;
            task = (Task<DatasetEntity>)method_CreateDatasetEntityWithReferenceAsync.Invoke(null, new object[] {

                 new WorkspaceIdentity(),
                 _dummyCreationInfo,
                 _fakeIDatasetStore,
                 _fakeIDataStoreClient,
                 _fakeIDataTypeStore,
                 _dummyWorkspaceId,
                 _fakeIContextProvider
            });

            actualRet = await task.ConfigureAwait(false);

            Mock.Get(_fakeIDatasetStore)
                .Verify(
                   stores => stores.CreateAsync(
                       _dummyWorkspaceId,
                       It.Is<DatasetEntity>(entity => entity.DataTypeId == "AzurePostgresDatabaseReference" && entity.DataLocation.DataReference.Type == DataReferenceType.AzurePostgresDatabase),
                       It.IsAny<WorkspaceIdentity>(),
                       It.IsAny<CreatedBy>()),
                   Times.Once);
            Assert.AreEqual(dummyDatasetEntity, actualRet);

            // test when  type is AzureMySql
            _fakeDataStoreDto.DataStoreType = DataStoreType.AzureMySql;
            task = (Task<DatasetEntity>)method_CreateDatasetEntityWithReferenceAsync.Invoke(null, new object[] {

                 new WorkspaceIdentity(),
                 _dummyCreationInfo,
                 _fakeIDatasetStore,
                 _fakeIDataStoreClient,
                 _fakeIDataTypeStore,
                 _dummyWorkspaceId,
                 _fakeIContextProvider
            });

            actualRet = await task.ConfigureAwait(false);

            Mock.Get(_fakeIDatasetStore)
                .Verify(
                   stores => stores.CreateAsync(
                       _dummyWorkspaceId,
                       It.Is<DatasetEntity>(entity => entity.DataTypeId == "AzureMySqlDatabaseReference" && entity.DataLocation.DataReference.Type == DataReferenceType.AzureMySqlDatabase),
                       It.IsAny<WorkspaceIdentity>(),
                       It.IsAny<CreatedBy>()),
                   Times.Once);
            Assert.AreEqual(dummyDatasetEntity, actualRet);

            // test when  type is AzureDataLakeGen2
            _fakeDataStoreDto.DataStoreType = DataStoreType.AzureDataLakeGen2;
            task = (Task<DatasetEntity>)method_CreateDatasetEntityWithReferenceAsync.Invoke(null, new object[] {

                 new WorkspaceIdentity(),
                 _dummyCreationInfo,
                 _fakeIDatasetStore,
                 _fakeIDataStoreClient,
                 _fakeIDataTypeStore,
                 _dummyWorkspaceId,
                 _fakeIContextProvider
            });

            actualRet = await task.ConfigureAwait(false);

            Mock.Get(_fakeIDatasetStore)
                .Verify(
                   stores => stores.CreateAsync(
                       _dummyWorkspaceId,
                       It.Is<DatasetEntity>(entity => entity.DataTypeId == "AzureDataLakeGen2Reference" && entity.DataLocation.DataReference.Type == DataReferenceType.AzureDataLakeGen2),
                       It.IsAny<WorkspaceIdentity>(),
                       It.IsAny<CreatedBy>()),
                   Times.Once);
            Assert.AreEqual(dummyDatasetEntity, actualRet);

            // test when  type is DBFS
            _fakeDataStoreDto.DataStoreType = DataStoreType.DBFS;
            task = (Task<DatasetEntity>)method_CreateDatasetEntityWithReferenceAsync.Invoke(null, new object[] {

                 new WorkspaceIdentity(),
                 _dummyCreationInfo,
                 _fakeIDatasetStore,
                 _fakeIDataStoreClient,
                 _fakeIDataTypeStore,
                 _dummyWorkspaceId,
                 _fakeIContextProvider
            });

            actualRet = await task.ConfigureAwait(false);

            Mock.Get(_fakeIDatasetStore)
                .Verify(
                   stores => stores.CreateAsync(
                       _dummyWorkspaceId,
                       It.Is<DatasetEntity>(entity => entity.DataTypeId == "DBFSReference" && entity.DataLocation.DataReference.Type == DataReferenceType.DBFS),
                       It.IsAny<WorkspaceIdentity>(),
                       It.IsAny<CreatedBy>()),
                   Times.Once);
            Assert.AreEqual(dummyDatasetEntity, actualRet);

            // test when  type is Custom
            _fakeDataStoreDto.DataStoreType = DataStoreType.Custom;
            task = (Task<DatasetEntity>)method_CreateDatasetEntityWithReferenceAsync.Invoke(null, new object[] {

                 new WorkspaceIdentity(),
                 _dummyCreationInfo,
                 _fakeIDatasetStore,
                 _fakeIDataStoreClient,
                 _fakeIDataTypeStore,
                 _dummyWorkspaceId,
                 _fakeIContextProvider
            });

            actualRet = await task.ConfigureAwait(false);

            Mock.Get(_fakeIDatasetStore)
                .Verify(
                   stores => stores.CreateAsync(
                       _dummyWorkspaceId,
                       It.Is<DatasetEntity>(entity => entity.DataTypeId == "Custom" && entity.DataLocation.DataReference.Type == DataReferenceType.Custom),
                       It.IsAny<WorkspaceIdentity>(),
                       It.IsAny<CreatedBy>()),
                   Times.Once);
            Assert.AreEqual(dummyDatasetEntity, actualRet);

            // test when type is Hdfs
            _fakeDataStoreDto.DataStoreType = DataStoreType.Hdfs;
            task = (Task<DatasetEntity>)method_CreateDatasetEntityWithReferenceAsync.Invoke(null, new object[] {

                 new WorkspaceIdentity(),
                 _dummyCreationInfo,
                 _fakeIDatasetStore,
                 _fakeIDataStoreClient,
                 _fakeIDataTypeStore,
                 _dummyWorkspaceId,
                 _fakeIContextProvider
            });

            actualRet = await task.ConfigureAwait(false);

            Mock.Get(_fakeIDatasetStore)
                .Verify(
                   stores => stores.CreateAsync(
                       _dummyWorkspaceId,
                       It.Is<DatasetEntity>(entity => entity.DataTypeId == "HdfsReference" && entity.DataLocation.DataReference.Type == DataReferenceType.Hdfs),
                       It.IsAny<WorkspaceIdentity>(),
                       It.IsAny<CreatedBy>()),
                   Times.Once);
            Assert.AreEqual(dummyDatasetEntity, actualRet);

            // test when ServiceInvocationException throws
            var innerException = new SocketException();
            var dummyServiceInvocationException = new ServiceInvocationException(
                  "dummyOperationName",
                  "dummyCallService",
                  new HttpMethod("dummyHttpMethod"),
                  innerException);

            Mock.Get(_fakeIDatasetStore)
                .Setup(
                   stores => stores.CreateAsync(
                       _dummyWorkspaceId,
                       It.IsAny<DatasetEntity>(),
                       It.IsAny<WorkspaceIdentity>(),
                       It.IsAny<CreatedBy>()))
                .Throws(dummyServiceInvocationException);

            task = (Task<DatasetEntity>)method_CreateDatasetEntityWithReferenceAsync.Invoke(null, new object[] {

                 new WorkspaceIdentity(),
                 _dummyCreationInfo,
                 _fakeIDatasetStore,
                 _fakeIDataStoreClient,
                 _fakeIDataTypeStore,
                 _dummyWorkspaceId,
                 _fakeIContextProvider
            });

            var actualExp1 = Assert.ThrowsAsync<BaseException>(async () => await task.ConfigureAwait(false));
            Assert.IsTrue(actualExp1.InnerException.InnerException is SocketException);

            // test when ServiceInvocationException throws
            var dummyException = new Exception("Test exception");

            Mock.Get(_fakeIDatasetStore)
                .Setup(
                   stores => stores.CreateAsync(
                       _dummyWorkspaceId,
                       It.IsAny<DatasetEntity>(),
                       It.IsAny<WorkspaceIdentity>(),
                       It.IsAny<CreatedBy>()))
                .Throws(dummyException);

            task = (Task<DatasetEntity>)method_CreateDatasetEntityWithReferenceAsync.Invoke(null, new object[] {

                 new WorkspaceIdentity(),
                 _dummyCreationInfo,
                 _fakeIDatasetStore,
                 _fakeIDataStoreClient,
                 _fakeIDataTypeStore,
                 _dummyWorkspaceId,
                 _fakeIContextProvider
            });

            var actualExp2 = Assert.ThrowsAsync<BaseException>(async () => await task.ConfigureAwait(false));
            StringAssert.Contains(dummyException.Message, actualExp2.Message);
        }

        [Test]
        public async Task GetAllDataTypeNamesSetAsync_Tests()
        {
            var actualRet = await ControllerHelper.GetAllDataTypeNamesSetAsync(
                  dataTypeStore: _fakeIDataTypeStore,
                  defaultWorkspaceId: _dummyWorkspaceId).ConfigureAwait(false);
            Assert.AreEqual(_dummyDataTypeEntities.Count(), actualRet.Count());
        }

        [Test]
        public void ComputeHash_Tests()
        {
            MethodInfo method_ComputeHash = typeof(ControllerHelper).GetMethod("ComputeHash", BindingFlags.Static | BindingFlags.NonPublic);

            // test when name is null
            var actualExp = Assert.Throws<TargetInvocationException>(() => method_ComputeHash.Invoke(null, new object[] {
                null,   // name is null
                "testDataStoreName",
                "testRelativePath",
                "testDataTypeId"
            }));
            Assert.True(actualExp.InnerException is BaseException);
            StringAssert.Contains("Name should not be null", actualExp.InnerException?.Message);

            // test when dataStoreName is null
            actualExp = Assert.Throws<TargetInvocationException>(() => method_ComputeHash.Invoke(null, new object[] {
                "testName",
                null,          // dataStoreName is null
                "testRelativePath",
                "testDataTypeId"
            }));
            Assert.True(actualExp.InnerException is BaseException);
            StringAssert.Contains("Data store name should not be null", actualExp.InnerException?.Message);

            // test regular execution
            var actualRet = (string)method_ComputeHash.Invoke(null, new object[] {
                "testName",
                "testDataStoreName",
                "testRelativePath",
                "testDataTypeId"
            });

            Assert.True(System.Text.RegularExpressions.Regex.IsMatch(actualRet, @"\A\b[0-9a-fA-F]+\b\Z"));
        }

        [Test]
        public void ValidateDataPathParameters_Tests()
        {
            MethodInfo method_ValidateDataPathParameters = typeof(ControllerHelper).GetMethod("ValidateDataPathParameters", BindingFlags.Static | BindingFlags.NonPublic);

            // test ArgumentException when one of params with null name
            var dataPathParameters = new List<AEVADataPathParameter>()
            {
                    new AEVADataPathParameter(
                                 null, // name is null
                                 "document1",
                                 _dummyAEVALegacyDataPath,
                                 false,
                                 _dummyDataTypeId),
            };

            var actualExp = Assert.Throws<TargetInvocationException>(() => method_ValidateDataPathParameters.Invoke(null, new object[] {
                dataPathParameters
            }));
            Assert.True(actualExp.InnerException is BaseException);
            Assert.AreEqual("paramName is invalid. Data path parameters contains item with no specified name.", actualExp.InnerException?.Message);

            // test ArgumentException when there are dup names
            dataPathParameters = new List<AEVADataPathParameter>()
            {
                    new AEVADataPathParameter(
                                 "name1", // name is null
                                 "document1",
                                 _dummyAEVALegacyDataPath,
                                 false,
                                 _dummyDataTypeId),
                    new AEVADataPathParameter(
                                 "name1", // name is null
                                 "document1",
                                 _dummyAEVALegacyDataPath,
                                 false,
                                 _dummyDataTypeId),
            };

            actualExp = Assert.Throws<TargetInvocationException>(() => method_ValidateDataPathParameters.Invoke(null, new object[] {
                dataPathParameters
            }));
            Assert.True(actualExp.InnerException is BaseException);
            StringAssert.Contains("are duplicated", actualExp.InnerException?.Message);

            // test normal execution
            dataPathParameters = new List<AEVADataPathParameter>()
            {
                    new AEVADataPathParameter(
                                 "name1", // name is null
                                 "document1",
                                 _dummyAEVALegacyDataPath,
                                 false,
                                 _dummyDataTypeId),
                    new AEVADataPathParameter(
                                 "name2", // name is null
                                 "document1",
                                 _dummyAEVALegacyDataPath,
                                 false,
                                 _dummyDataTypeId),
            };

            Assert.DoesNotThrow(() => method_ValidateDataPathParameters.Invoke(null, new object[] {
                dataPathParameters
            }));
        }
    }
}
