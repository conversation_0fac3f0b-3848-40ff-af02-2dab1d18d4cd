﻿// <copyright file="DataTypesBaseControllerTest.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using FluentAssertions;
using Microsoft.Aether.AEVA.DataContractConverter;
using Microsoft.Aether.BackendCommon;
using Microsoft.Aether.DataContracts;
using Microsoft.Aether.DataContracts.Entity;
using Microsoft.MachineLearning.Common.Core.Exceptions;
using Moq;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Security.Claims;
using System.Threading.Tasks;

using AEVADataTypeEntity = Microsoft.Aether.AEVA.DataContracts.DataTypeEntity;
using DataTypeCreationInfo = Microsoft.Aether.AEVA.DataContracts.DataTypeCreationInfo;

namespace Microsoft.Aether.AE3pService.Tests.Controllers
{
    [TestFixture]
    public class DataTypesBaseControllerTest
    {

        private DataTypesBaseController _fakeDataTypesBaseController;

        private readonly MockRepository _repository;

        private IStoreCollection _fakeIStoreCollection;
        private IContextProvider _fakeIContextProvider;
        private IWorkspaceResourcesCache _fakeIWorkspaceResourcesCache;

        private WorkspaceIdentity _fakeWorkspaceIdentity;
        private IDataTypeStore _fakeIDataTypeStore;
        private IEnumerable<DataTypeEntity> _fakeDataTypeEntities;
        private DataTypeEntity _fakeDataTypeEntity;
        private DataTypeEntity _fakeDataTypeEntity_existing;

        private AEVADataTypeEntity _dummyAEVADataTypeEntity;
        private AEVADataTypeEntity _dummyAEVADataTypeEntity_existing;
        private DataTypeCreationInfo _dummyDataTypeCreationInfo;
        private Exception _dummyException;

        public DataTypesBaseControllerTest()
        {
            _repository = new MockRepository(MockBehavior.Loose) { DefaultValue = DefaultValue.Mock, CallBase = true };
        }

        [SetUp]
        public void Init()
        {
            _fakeIStoreCollection = _repository.Create<IStoreCollection>().Object;
            _fakeIContextProvider = _repository.Create<IContextProvider>().Object;
            _fakeIWorkspaceResourcesCache = _repository.Create<IWorkspaceResourcesCache>().Object;
            _fakeWorkspaceIdentity = _repository.Create<WorkspaceIdentity>().Object;
            _fakeDataTypeEntity = new DataTypeEntity()
            {
                Id = "dummyId"
            };
            _fakeDataTypeEntity_existing = new DataTypeEntity()
            {
                Id = "dummyId_1"
            };

            _fakeDataTypesBaseController = _repository.Create<DataTypesBaseController>(_fakeIStoreCollection, _fakeIContextProvider, _fakeIWorkspaceResourcesCache).Object;

            _fakeIDataTypeStore = _repository.Create<IDataTypeStore>().Object;
            Mock.Get(_fakeIStoreCollection)
                .Setup(
                   stores => stores.GetDataTypeStore(
                       It.IsAny<ClaimsPrincipal>(),
                       It.IsAny<WorkspaceIdentity>()))
                .Returns(_fakeIDataTypeStore);

            _fakeDataTypeEntities = new List<DataTypeEntity>() {
                  _fakeDataTypeEntity_existing
            };

            Mock.Get(_fakeIDataTypeStore)
                .Setup(
                   stores => stores.ListAsync(
                       It.IsAny<string>(),
                       It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(_fakeDataTypeEntities));

            Mock.Get(_fakeIDataTypeStore)
                .Setup(
                   stores => stores.BulkGetAsync(
                       It.IsAny<string>(),
                       It.IsAny<string[]>(),
                       It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(_fakeDataTypeEntities));

            Mock.Get(_fakeIDataTypeStore)
                .Setup(
                   stores => stores.GetEntityAsync(
                       It.IsAny<string>(),
                       It.IsAny<string>(),
                       It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(_fakeDataTypeEntity));

            Mock.Get(_fakeIDataTypeStore)
                .Setup(
                   stores => stores.CreateAsync(
                       It.IsAny<string>(),
                       It.IsAny<DataTypeEntity>(),
                       It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(_fakeDataTypeEntity));

            Mock.Get(_fakeIDataTypeStore)
                .Setup(
                   stores => stores.UpdateAsync(
                       It.IsAny<string>(),
                       It.IsAny<DataTypeEntity>(),
                       It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(_fakeDataTypeEntity));

            _dummyAEVADataTypeEntity = ContractConverter.Convert<DataTypeEntity, AEVADataTypeEntity>(_fakeDataTypeEntity);
            _dummyAEVADataTypeEntity_existing = ContractConverter.Convert<DataTypeEntity, AEVADataTypeEntity>(_fakeDataTypeEntity_existing);
            _dummyException = new Exception("dummyException");
            _dummyDataTypeCreationInfo = new DataTypeCreationInfo(
                id: "dummyId",
                name: "dummyName",
                description: "dummyDescription",
                isDirectory: true);
        }

        [Test]
        public async Task GetAllDataTypesAsync_Tests()
        {
            MethodInfo method_GetAllDataTypesAsync = typeof(DataTypesBaseController).GetMethod("GetAllDataTypesAsync", BindingFlags.Instance | BindingFlags.NonPublic);

            // test regular execution
            var task = (Task<IEnumerable<AEVADataTypeEntity>>)method_GetAllDataTypesAsync.Invoke(_fakeDataTypesBaseController, new object[] { _fakeWorkspaceIdentity });
            var actualRet = await task.ConfigureAwait(false);

            Assert.AreEqual(_fakeDataTypeEntities.Count(), actualRet.Count());
            foreach (AEVADataTypeEntity element in actualRet)
            {
                _dummyAEVADataTypeEntity_existing.Should().BeEquivalentTo(element);
            }

            // test Exception throw
            Mock.Get(_fakeIStoreCollection)
                .Setup(
                   stores => stores.GetDataTypeStore(
                       It.IsAny<ClaimsPrincipal>(),
                       It.IsAny<WorkspaceIdentity>()))
                .Throws(_dummyException);

            task = (Task<IEnumerable<AEVADataTypeEntity>>)method_GetAllDataTypesAsync.Invoke(_fakeDataTypesBaseController, new object[] { _fakeWorkspaceIdentity });
            var actualExp = Assert.ThrowsAsync<BaseException>(async () => await task.ConfigureAwait(false));
            Assert.AreEqual(string.Format("Unexpected System Error occured when getting all DataTypes. The error message is: '{0}'.", _dummyException.Message), actualExp.Message);
        }

        [Test]
        public async Task CreateNewDataTypeAsync_Tests()
        {
            MethodInfo method_CreateNewDataTypeAsync = typeof(DataTypesBaseController).GetMethod("CreateNewDataTypeAsync", BindingFlags.Instance | BindingFlags.NonPublic);

            _dummyDataTypeCreationInfo.ParentDataTypeIds = new List<string>()
            {
            };
            Assert.IsNotNull(_dummyDataTypeCreationInfo.ParentDataTypeIds);
            Assert.False(_fakeDataTypeEntity.IsDirectory);
            Assert.True(_dummyDataTypeCreationInfo.IsDirectory);

            // test when exception throws from ValidateParentDataTypes
            var task = (Task<AEVADataTypeEntity>)method_CreateNewDataTypeAsync.Invoke(_fakeDataTypesBaseController, new object[] { _fakeWorkspaceIdentity, _dummyDataTypeCreationInfo });
            var actualExp = Assert.ThrowsAsync<BaseException>(async () => await task.ConfigureAwait(false));
            Assert.AreEqual("ParentDataType is invalid. Directory data types may not extend file data types and vice versa.", actualExp.Message);

            // test ArgumentException from dataType.Id.Contains(illegalCharacter)
            _fakeDataTypeEntity.IsDirectory = true;
            Assert.True(_fakeDataTypeEntity.IsDirectory);
            Assert.True(_dummyDataTypeCreationInfo.IsDirectory);
            var illegal_ch = ">";
            _dummyDataTypeCreationInfo = new DataTypeCreationInfo(
                id: "dummyId" + illegal_ch,
                name: "dummyName",
                description: "dummyDescription",
                isDirectory: true);
            task = (Task<AEVADataTypeEntity>)method_CreateNewDataTypeAsync.Invoke(_fakeDataTypesBaseController, new object[] { _fakeWorkspaceIdentity, _dummyDataTypeCreationInfo });
            actualExp = Assert.ThrowsAsync<BaseException>(async () => await task.ConfigureAwait(false));
            Assert.AreEqual($"Id is invalid. The character '{illegal_ch}' may not be used as part of the datatype id.", actualExp.Message);

            // test regular execution
            _dummyDataTypeCreationInfo = new DataTypeCreationInfo(
               id: "dummyId",
               name: "dummyName",
               description: "dummyDescription",
               isDirectory: true);

            task = (Task<AEVADataTypeEntity>)method_CreateNewDataTypeAsync.Invoke(_fakeDataTypesBaseController, new object[] { _fakeWorkspaceIdentity, _dummyDataTypeCreationInfo });
            var actualRet = await task.ConfigureAwait(false);
            _dummyAEVADataTypeEntity = ContractConverter.Convert<DataTypeEntity, AEVADataTypeEntity>(_fakeDataTypeEntity);
            actualRet.Should().BeEquivalentTo(_dummyAEVADataTypeEntity);

            // test when createAsyc throws exception
            Mock.Get(_fakeIDataTypeStore)
                .Setup(
                   stores => stores.CreateAsync(
                       It.IsAny<string>(),
                       It.IsAny<DataTypeEntity>(),
                       It.IsAny<WorkspaceIdentity>()))
                .Throws(_dummyException);

            task = (Task<AEVADataTypeEntity>)method_CreateNewDataTypeAsync.Invoke(_fakeDataTypesBaseController, new object[] { _fakeWorkspaceIdentity, _dummyDataTypeCreationInfo });
            var actualExp1 = Assert.ThrowsAsync<BaseException>(async () => await task.ConfigureAwait(false));
            Assert.AreEqual(string.Format("Unexpected System Error occured when creating new DataType. The error message is: '{0}'.", _dummyException.Message), actualExp1.Message);
        }

        [Test]
        public void TestCreateNewDataTypeAsync_DuplicatedId_BaseException()
        {
            MethodInfo method_CreateNewDataTypeAsync = typeof(DataTypesBaseController).GetMethod("CreateNewDataTypeAsync", BindingFlags.Instance | BindingFlags.NonPublic);
            _fakeDataTypeEntities = new List<DataTypeEntity>() {
                  new DataTypeEntity()
                {
                    Id = _fakeDataTypeEntity.Id
                }
            };
            Assert.AreEqual(_fakeDataTypeEntities.ElementAt(0).Id, _fakeDataTypeEntity.Id);
            Mock.Get(_fakeIDataTypeStore)
                .Setup(
                   stores => stores.ListAsync(
                       It.IsAny<string>(),
                       It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(_fakeDataTypeEntities));

            var task = (Task<AEVADataTypeEntity>)method_CreateNewDataTypeAsync.Invoke(_fakeDataTypesBaseController, new object[] { _fakeWorkspaceIdentity, _dummyDataTypeCreationInfo });
            var baseExp = Assert.ThrowsAsync<BaseException>(async () => await task.ConfigureAwait(false));
            StringAssert.Contains(string.Format(DataContracts.Error.AE3pErrorMessages.DuplicatedDataTypeIdIgnoreCase, _fakeDataTypeEntity.Id), baseExp.Message);
        }

        [Test]
        public async Task UpdateDataTypeAsync_Tests()
        {
            MethodInfo method_UpdateDataTypeAsync = typeof(DataTypesBaseController).GetMethod("UpdateDataTypeAsync", BindingFlags.Instance | BindingFlags.NonPublic);

            //test when id != updated.Id
            string id = "world";
            AEVADataTypeEntity fakeAEVADataTypeEntity = new AEVADataTypeEntity()
            {
                Id = "hello",
                Name = "dataEntityName",
                Description = "testDescription"
            };
            Assert.AreNotEqual(id, fakeAEVADataTypeEntity.Id);
            var task = (Task<AEVADataTypeEntity>)method_UpdateDataTypeAsync.Invoke(_fakeDataTypesBaseController, new object[] { _fakeWorkspaceIdentity, id, fakeAEVADataTypeEntity });
            var actualExp = Assert.ThrowsAsync<BaseException>(async () => await task.ConfigureAwait(false));
            StringAssert.Contains("id should be equal to updated.Id", actualExp.Message);

            // test when exception from trying to add parent data type id
            _fakeDataTypeEntity.ParentDataTypeIds = new List<string>() { "id2", "id3" };
            fakeAEVADataTypeEntity = new AEVADataTypeEntity()
            {
                Id = id,
                ParentDataTypeIds = new List<string>() { "id1", "id2", "id4" }
            };
            Assert.False(new HashSet<string>(_fakeDataTypeEntity.ParentDataTypeIds).IsSubsetOf(fakeAEVADataTypeEntity.ParentDataTypeIds));
            task = (Task<AEVADataTypeEntity>)method_UpdateDataTypeAsync.Invoke(_fakeDataTypesBaseController, new object[] { _fakeWorkspaceIdentity, id, fakeAEVADataTypeEntity });
            actualExp = Assert.ThrowsAsync<BaseException>(async () => await task.ConfigureAwait(false));
            //StringAssert.Contains(actualExp.Message, "You may only add parent data types (not remove them). Only AEther admins may remove a parent.");

            // test normal execution
            _fakeDataTypeEntity.Id = id;
            _fakeDataTypeEntity.ParentDataTypeIds = new List<string>() { "id2", "id1" };
            Assert.True(new HashSet<string>(_fakeDataTypeEntity.ParentDataTypeIds).IsSubsetOf(fakeAEVADataTypeEntity.ParentDataTypeIds));
            task = (Task<AEVADataTypeEntity>)method_UpdateDataTypeAsync.Invoke(_fakeDataTypesBaseController, new object[] { _fakeWorkspaceIdentity, id, fakeAEVADataTypeEntity });
            var actualRet = await task.ConfigureAwait(false);
            Assert.AreEqual(fakeAEVADataTypeEntity.Name, actualRet.Name);
            Assert.AreEqual(fakeAEVADataTypeEntity.Description, actualRet.Description);
            Assert.AreEqual(fakeAEVADataTypeEntity.Id, actualRet.Id);
            CollectionAssert.AreEqual(fakeAEVADataTypeEntity.ParentDataTypeIds, actualRet.ParentDataTypeIds);

            // test when Exception occur getDataTypeStore
            Mock.Get(_fakeIStoreCollection)
                .Setup(
                   stores => stores.GetDataTypeStore(
                       It.IsAny<ClaimsPrincipal>(),
                       It.IsAny<WorkspaceIdentity>()))
                .Throws(_dummyException);

            task = (Task<AEVADataTypeEntity>)method_UpdateDataTypeAsync.Invoke(_fakeDataTypesBaseController, new object[] { _fakeWorkspaceIdentity, id, fakeAEVADataTypeEntity });
            var actualExp1 = Assert.ThrowsAsync<BaseException>(async () => await task.ConfigureAwait(false));
            Assert.AreEqual(string.Format("Unexpected System Error occured when updating DataType. The error message is: '{0}'.", _dummyException.Message), actualExp1.Message);
        }
    }
}
