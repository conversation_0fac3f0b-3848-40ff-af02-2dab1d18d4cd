﻿// <copyright file="AmlModulesWorkspaceControllerTest.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using Microsoft.Aether.AEVA.DataContracts;
using Microsoft.Aether.BackendCommon;
using Microsoft.Aether.BackendCommon.AzureMonitor;
using Microsoft.Aether.DataContracts;
using Microsoft.Aether.DataContracts.Entity;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Routing;
using Microsoft.MachineLearning.Common.Core.Exceptions;
using Microsoft.MachineLearning.Common.WebApi.TeamAccounts;
using Moq;
using NUnit.Framework;

namespace Microsoft.Aether.AE3pService.Tests.Controllers
{
    [TestFixture]
    public class AmlModulesWorkspaceControllerTest
    {
        private Mock<IStoreCollection> _stores;
        private Mock<IContextProvider> _operationContextProvider;
        private Mock<IWorkspaceResourcesCache> _workspaceResourcesCache;
        private Mock<IModuleStore> _moduleStore;
        private Mock<IDataTypeStore> _dataTypeStore;
        private Mock<IAmlModuleStore> _amlModuleStore;
        private Mock<IAzureMonitorStatusLogger> _azureMonitorStatusEmitter;
        private AmlModulesWorkspaceController _controller;
        private readonly string _subscriptionId = "TestSubscriptionId";
        private readonly string _resourceGroupName = "TestResourceGroupName";
        private readonly string _workspaceName = "TestWorkspaceName";

        [SetUp]
        public void SetUp()
        {
            _moduleStore = new Mock<IModuleStore>();
            IEnumerable<DataContracts.ModuleEntity> modules = new List<DataContracts.ModuleEntity>() { new DataContracts.ModuleEntity { Id = "TestVersionId", FamilyId = "TestFamilyId" } };
            _moduleStore.Setup(m => m.BulkGetIdsAsync(It.IsAny<string>(), It.IsAny<string[]>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(modules));

            _dataTypeStore = new Mock<IDataTypeStore>();
            IEnumerable<DataContracts.DataTypeEntity> types = new List<DataContracts.DataTypeEntity> { new DataContracts.DataTypeEntity { Id = "TestTypeId" } };
            _dataTypeStore.Setup(t => t.ListAsync(It.IsAny<string>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(types));

            _amlModuleStore = new Mock<IAmlModuleStore>();
            _amlModuleStore.Setup(a => a.CreateAsync(It.IsAny<string>(), It.IsAny<AmlModuleEntity>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(new AmlModuleEntity { Name = "fakeModule" }));
            _amlModuleStore.Setup(a => a.GetEntityAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(new AmlModuleEntity
                {
                    Name = @"azureml://fakeVersion",
                    Versions = new List<AmlModuleVersionDescriptor> { { new AmlModuleVersionDescriptor { Version = "TestVersion" } } },
                    Id = "TestId",
                }));
            _amlModuleStore.Setup(a => a.GetAmlModuleByNameAsync(It.IsAny<string>(), It.IsAny<string>()))
                .Returns(Task.FromResult(new AmlModuleEntity { Name = @"azureml://fakeVersion" }));
            _azureMonitorStatusEmitter = new Mock<IAzureMonitorStatusLogger>();
            IEnumerable<AmlModuleEntity> entityList = new List<AmlModuleEntity> {
                new AmlModuleEntity {
                    Name = "fakeModule",
                    Id = "TestFamilyId",
                    DefaultVersion = "*******",
                    Versions = new List<AmlModuleVersionDescriptor> { new AmlModuleVersionDescriptor { ModuleVersionId = "TestVersionId", Version = "*******" } },
                }
            };
            _amlModuleStore.Setup(a => a.ListEntityAsync(It.IsAny<string>(), It.IsAny<bool>(), It.IsAny<string>(), It.IsAny<int>(), It.IsAny<bool>(), It.IsAny<string>(), It.IsAny<bool>(), It.IsAny<string>(), It.IsAny<bool>()))
                .Returns(Task.FromResult(new Tuple<IEnumerable<AmlModuleEntity>, string>(entityList, "TestToken")));

            _amlModuleStore.Setup(m => m.UpdateAsync(It.IsAny<string>(), It.IsAny<AmlModuleEntity>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(new DataContracts.AmlModuleEntity { Name = "fakeModule" }));

            IEnumerable<AmlModuleEntity> entities = new List<AmlModuleEntity> {
                new AmlModuleEntity {Versions = new List<AmlModuleVersionDescriptor> { new AmlModuleVersionDescriptor { ModuleVersionId = "TestVersionId", Version = "*******" } }, Id = "TestFamilyId" }
            };
            _amlModuleStore.Setup(s => s.BulkGetAsync(It.IsAny<string>(), It.IsAny<string[]>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(entities));

            _stores = new Mock<IStoreCollection>();
            _stores.Setup(s => s.GetModuleStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(_moduleStore.Object);
            _stores.Setup(s => s.GetDataTypeStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(_dataTypeStore.Object);
            _stores.Setup(s => s.GetAmlModuleStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(_amlModuleStore.Object);

            _operationContextProvider = new Mock<IContextProvider>();
            _operationContextProvider.Setup(o => o.GetCreatedByObject())
                .Returns(new DataContracts.CreatedBy());

            _workspaceResourcesCache = new Mock<IWorkspaceResourcesCache>();

            _controller = new AmlModulesWorkspaceController(_stores.Object, _operationContextProvider.Object, _workspaceResourcesCache.Object, _azureMonitorStatusEmitter.Object);
            // Setup the HttpContext's Request/Response
            // The HeaderDictionary is needed for adding HTTP headers to the response.
            // This needs a couple of different Mocks, because many properties in the class model are read-only.
            var headerDictionary = new HeaderDictionary();
            headerDictionary.Add("ContinuationToken", "TestContinuationToken");
            headerDictionary.Add("BatchSize", "64");
            var request = new Mock<HttpRequest>();
            request.SetupGet(r => r.Headers).Returns(headerDictionary);
            var response = new Mock<HttpResponse>();
            response.SetupGet(r => r.Headers).Returns(headerDictionary);

            var httpContext = new Mock<HttpContext>();
            httpContext.SetupGet(a => a.Request).Returns(request.Object);
            httpContext.SetupGet(a => a.Response).Returns(response.Object);
            var workspaceMetadata = new WorkspaceMetadata(Guid.Empty, null, null, Guid.Empty);
            httpContext.SetupGet(a => a.Items).Returns(new Dictionary<object, object>() { { typeof(WorkspaceMetadata), workspaceMetadata } });

            _controller.ControllerContext = new ControllerContext(new ActionContext(httpContext.Object, new RouteData(), new Microsoft.AspNetCore.Mvc.Controllers.ControllerActionDescriptor()));
        }

        [Test]
        public async Task TestCreateAzureMLModuleAsync_NormalCase_ExpectedAzureMLModule()
        {
            var creationInfo = new AzureMLModuleCreationInfo("fakeModule", "fakeDescription", null, null, null);
            var result = await _controller.CreateAzureMLModuleAsync(_subscriptionId, _resourceGroupName, _workspaceName, creationInfo).ConfigureAwait(false);
            Assert.AreEqual("fakeModule", result.Name);
        }

        [Test]
        public async Task TestUpdateAzureMLModuleAsync_NormalCase_ExpectedAzureMLModule()
        {
            var creationInfo = new AzureMLModule { Name = "fakeModule", Id = "testId" };
            var result = await _controller.UpdateAzureMLModuleAsync(_subscriptionId, _resourceGroupName, _workspaceName, "testId", creationInfo).ConfigureAwait(false);
            Assert.AreEqual("fakeModule", result.Name);
        }

        [Test]
        public async Task TestGetAzureMLModulesAsync_NormalCase_ExpectedAzureMLModuleList()
        {
            var result = await _controller.GetAzureMLModulesAsync(_subscriptionId, _resourceGroupName, _workspaceName, false).ConfigureAwait(false);
            Assert.AreEqual(1, result.Count());
            Assert.AreEqual("fakeModule", result.ToList()[0].Name);
        }

        [Test]
        public async Task TestGetAzureMLModuleAsync_NormalCase_ExpectedAzureMLModule()
        {
            var result = await _controller.GetAzureMLModuleAsync(_subscriptionId, _resourceGroupName, _workspaceName, "testId").ConfigureAwait(false);
            Assert.AreEqual(@"azureml://fakeVersion", result.Name);
        }

        [Test]
        public async Task TestGetAzureMLModuleByNameAsync_NormalCase_ExpectedAzureMLModule()
        {
            var result = await _controller.GetAzureMLModuleByNameAsync(_subscriptionId, _resourceGroupName, _workspaceName, "testName").ConfigureAwait(false);
            Assert.AreEqual(@"azureml://fakeVersion", result.Name);
        }

        [Test]
        public async Task TestCreateAzureMLModulesWithIdAsync_NormalCase_ExpectedAzureMLModule()
        {
            var creationInfo = new AzureMLModuleCreationInfo("fakeModule", "fakeDescription", null, null, null);
            creationInfo.Properties = new Dictionary<string, string> { { @"azureml/id", Guid.NewGuid().ToString() } };
            var result = await _controller.CreateAzureMLModulesWithIdAsync(_subscriptionId, _resourceGroupName, _workspaceName, creationInfo).ConfigureAwait(false);
            Assert.AreEqual("fakeModule", result.Name);
        }

        [Test]
        public async Task TestUpdateAzureMLModuleS2SAsync_NormalCase_ExpectedAzureMLModule()
        {
            var creationInfo = new AzureMLModule { Name = @"azureml://fakeModule", Id = "testId" };
            var result = await _controller.UpdateAzureMLModuleS2SAsync(_subscriptionId, _resourceGroupName, _workspaceName, "testId", creationInfo).ConfigureAwait(false);
            Assert.AreEqual("fakeModule", result.Name);
        }

        [Test]
        public void TestGetResolveAzureMLModuleVersionAsync_AmlModuleStoreThrows_ExpectedBaseException()
        {
            _stores.Setup(s => s.GetAmlModuleStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Throws(new ArgumentException());

            Assert.ThrowsAsync<BaseException>(async () => await _controller.GetResolveAzureMLModuleVersionAsync(_subscriptionId, _resourceGroupName, _workspaceName, "testId").ConfigureAwait(false));
        }

        [Test]
        public async Task TestListResolveAzureMLModuleVersionAsync_NormalCase_ExpectedListOfVersions()
        {
            var result = await _controller.ListResolveAzureMLModuleVersionAsync(_subscriptionId, _resourceGroupName, _workspaceName, true).ConfigureAwait(false);
            Assert.AreEqual("TestVersionId", result.ToList()[0].Data.Id);
        }

        [Test]
        public async Task TestListAzureMLModuleVersionsAsync_NormalCase_ExpectedListOfVersions()
        {
            var result = await _controller.ListAzureMLModuleVersionsAsync(_subscriptionId, _resourceGroupName, _workspaceName, true, GetAmlModuleVersionsType.Default, true, "Version DESC", string.Empty, true).ConfigureAwait(false);
            Assert.AreEqual("TestFamilyId", result.ToList()[0].Item1.Id);
            Assert.AreEqual(1, result.ToList()[0].Item2.Count());
        }

        [Test]
        public void TestGetBulkResolveAzureMLModuleVersionAsync_AmlModuleStoreThrows_ExpectedArgumentException()
        {
            _stores.Setup(s => s.GetAmlModuleStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Throws(new ArgumentException());
            string[] ids = { "TestId" };
            Assert.ThrowsAsync<ArgumentException>(async () => await _controller.GetBulkResolveAzureMLModuleVersionAsync(_subscriptionId, _resourceGroupName, _workspaceName, ids).ConfigureAwait(false));
        }
    }
}
