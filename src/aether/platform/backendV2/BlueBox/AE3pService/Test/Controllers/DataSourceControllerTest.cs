﻿// <copyright file="DataSourceControllerTest.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Microsoft.Aether.BackendCommon;
using Microsoft.Aether.BlueBox.DataStoreClient;
using Microsoft.Aether.DataContracts.Entity;
using Moq;
using NUnit.Framework;
using DataSourceCreationInfo = Microsoft.Aether.AEVA.DataContracts.DataSourceCreationInfo;
using AEVADataSourceEntity = Microsoft.Aether.AEVA.DataContracts.DataSourceEntity;
using System.Threading.Tasks;
using Microsoft.Aether.DataContracts;
using System;
using System.Security.Claims;
using System.Collections.Generic;
using System.Net.Http;
using Microsoft.MachineLearning.Common.Core;
using System.Linq;
using Microsoft.MachineLearning.Common.Core.Exceptions;

namespace Microsoft.Aether.AE3pService.Tests.Controllers
{
    [TestFixture]
    public class DataSourceControllerTest
    {
        private DataSourcesController _fakeDataSourcesController;

        private readonly MockRepository _repository;

        private IStoreCollection _fakeIStoreCollection;
        private IContextProvider _fakeIContextProvider;
        private IDataStoreClient _fakeIDataStoreClient;
        private IWorkspaceResourcesCache _fakeIWorkspaceResourcesCache;

        private AEVADataSourceEntity _fakeAevaDataSourceEntity;
        private DatasetEntity _fakeDatasetEntity;

        private IDatasetStore _fakeIDatasetStore;
        private bool _called_GetDatasetStore;
        private bool _convert_AEVADataSourceEntity_to_DatasetEntity;
        private bool _convert_DatasetEntity_to_AEVADataSourceEntity;

        private ServiceInvocationException _dummyServiceInvocationException;
        private Exception _dummyException;

        public DataSourceControllerTest()
        {
            _repository = new MockRepository(MockBehavior.Loose) { DefaultValue = DefaultValue.Mock, CallBase = true };
        }

        [SetUp]
        public void Init()
        {
            _fakeIStoreCollection = _repository.Create<IStoreCollection>().Object;
            _fakeIContextProvider = _repository.Create<IContextProvider>().Object;
            _fakeIDataStoreClient = _repository.Create<IDataStoreClient>().Object;
            _fakeIWorkspaceResourcesCache = _repository.Create<IWorkspaceResourcesCache>().Object;

            _fakeDataSourcesController = _repository.Create<DataSourcesController>(
                _fakeIStoreCollection,
                _fakeIContextProvider,
                _fakeIDataStoreClient,
                _fakeIWorkspaceResourcesCache).Object;

            _fakeAevaDataSourceEntity = _repository.Create<AEVADataSourceEntity>().Object;
            _fakeDatasetEntity = _repository.Create<DatasetEntity>().Object;

            _fakeIDatasetStore = _repository.Create<IDatasetStore>().Object;
            _called_GetDatasetStore = false;
            Mock.Get(_fakeIStoreCollection)
                .Setup(controller => controller.GetDatasetStore(
                              It.IsAny<ClaimsPrincipal>(),
                              It.IsAny<WorkspaceIdentity>()))
                .Returns(_fakeIDatasetStore)
                .Callback(() => _called_GetDatasetStore = true);

            _convert_AEVADataSourceEntity_to_DatasetEntity = false;
            Mock.Get(_fakeDataSourcesController)
               .Setup(controller => controller.Convert<AEVADataSourceEntity, DatasetEntity>(
                            It.IsAny<AEVADataSourceEntity>(),
                            It.IsAny<Dictionary<Tuple<Type, Type, string>, dynamic>>()))
               .Callback(() => _convert_AEVADataSourceEntity_to_DatasetEntity = true)
               .Returns(_fakeDatasetEntity);

            _convert_DatasetEntity_to_AEVADataSourceEntity = false;
            Mock.Get(_fakeDataSourcesController)
               .Setup(controller => controller.Convert<DatasetEntity, AEVADataSourceEntity>(
                            It.IsAny<DatasetEntity>(),
                            It.IsAny<Dictionary<Tuple<Type, Type, string>, dynamic>>()))
               .Callback(() => _convert_DatasetEntity_to_AEVADataSourceEntity = true)
               .Returns(_fakeAevaDataSourceEntity);

            Mock.Get(_fakeDataSourcesController)
                .Setup(controller => controller.GetWorkspaceId())
                .Returns(string.Empty);

            _dummyServiceInvocationException = new ServiceInvocationException(
                request: new HttpRequestMessage(),
                response: new HttpResponseMessage(),
                body: "dummyBody");

            _dummyException = new Exception("dummyException");
        }

        [Test]
        public async Task CreateDataSourceAsync_Tests()
        {
            bool called_CreateDataSourceWithReferenceAsync = false;
            bool called_CreateDataSourceWithUploadAsync = false;

            Mock.Get(_fakeDataSourcesController)
                .Setup(controller => controller.CreateDataSourceWithReferenceAsync(
                             It.IsAny<WorkspaceIdentity>(),
                             It.IsAny<DataSourceCreationInfo>(),
                             It.IsAny<IDatasetStore>(),
                             It.IsAny<IDataStoreClient>(),
                             It.IsAny<IDataTypeStore>(),
                             It.IsAny<string>(),
                             It.IsAny<IContextProvider>()))
                .Returns(Task.FromResult(_fakeAevaDataSourceEntity))
                .Callback(() => called_CreateDataSourceWithReferenceAsync = true);

            Mock.Get(_fakeDataSourcesController)
                .Setup(controller => controller.CreateDataSourceWithUploadAsync(
                             It.IsAny<WorkspaceIdentity>(),
                             It.IsAny<DataSourceCreationInfo>()))
                .Returns(Task.FromResult(_fakeAevaDataSourceEntity))
                .Callback(() => called_CreateDataSourceWithUploadAsync = true);

            // test when creationInfo.DataStoreName null or empty
            DataSourceCreationInfo dummyDataSourceCreationInfo = new DataSourceCreationInfo()
            {
                DataStoreName = string.Empty
            };

            Assert.That(dummyDataSourceCreationInfo.DataStoreName, Is.Null.Or.Empty);

            var actualRet = await _fakeDataSourcesController.CreateDataSourceAsync(
                subscriptionId: "dummySubscriptionId",
                resourceGroupName: "dummyResourceGroupName",
                workspaceName: "dummyWorkspaceName",
                creationInfo: dummyDataSourceCreationInfo).ConfigureAwait(false);

            Assert.True(called_CreateDataSourceWithUploadAsync);
            Assert.False(called_CreateDataSourceWithReferenceAsync);
            Assert.AreEqual(_fakeAevaDataSourceEntity, actualRet);

            // test when creationInfo.DataStoreName is valid
            called_CreateDataSourceWithReferenceAsync = false;
            called_CreateDataSourceWithUploadAsync = false;

            dummyDataSourceCreationInfo = new DataSourceCreationInfo()
            {
                DataStoreName = "dummyDataStoreName"
            };
            Assert.That(dummyDataSourceCreationInfo.DataStoreName, Is.Not.Null.And.Not.Empty);
            actualRet = await _fakeDataSourcesController.CreateDataSourceAsync(
                subscriptionId: "dummySubscriptionId",
                resourceGroupName: "dummyResourceGroupName",
                workspaceName: "dummyWorkspaceName",
                creationInfo: dummyDataSourceCreationInfo).ConfigureAwait(false);

            Assert.False(called_CreateDataSourceWithUploadAsync);
            Assert.True(called_CreateDataSourceWithReferenceAsync);
            Assert.AreEqual(_fakeAevaDataSourceEntity, actualRet);
        }

        [Test]
        public async Task UpdateDataSourceAsync_Tests()
        {
            _called_GetDatasetStore = false;
            _convert_AEVADataSourceEntity_to_DatasetEntity = false;
            _convert_DatasetEntity_to_AEVADataSourceEntity = false;

            bool called_UpdateAsync = false;
            Mock.Get(_fakeDataSourcesController)
                .Setup(controller => controller.UpdateAsync(
                             It.IsAny<IUpdatableEntityStore<DatasetEntity>>(),
                             It.IsAny<DatasetEntity>(),
                             It.IsAny<Type>(),
                             It.IsAny<bool>()))
                .Returns(Task.FromResult(_fakeDatasetEntity))
                .Callback(() => called_UpdateAsync = true);

            // test nomal execution
            var actualRet = await _fakeDataSourcesController.UpdateDataSourceAsync(
                subscriptionId: "dummySubscriptionId",
                resourceGroupName: "dummyResourceGroupName",
                workspaceName: "dummyWorkspaceName",
                dataSourceId: "dummyDataSourceId",
                updated: _fakeAevaDataSourceEntity).ConfigureAwait(false);

            Assert.True(_called_GetDatasetStore);
            Assert.True(called_UpdateAsync);
            Assert.True(_convert_AEVADataSourceEntity_to_DatasetEntity);
            Assert.True(_convert_DatasetEntity_to_AEVADataSourceEntity);
            Assert.AreEqual(_fakeAevaDataSourceEntity, actualRet);

            // test serviceInvocationException
            _called_GetDatasetStore = false;
            called_UpdateAsync = false;
            _convert_AEVADataSourceEntity_to_DatasetEntity = false;
            _convert_DatasetEntity_to_AEVADataSourceEntity = false;

            Mock.Get(_fakeDataSourcesController)
                .Setup(controller => controller.UpdateAsync(
                             It.IsAny<IUpdatableEntityStore<DatasetEntity>>(),
                             It.IsAny<DatasetEntity>(),
                             It.IsAny<Type>(),
                             It.IsAny<bool>()))
                .Callback(() => called_UpdateAsync = true)
                .Throws(_dummyServiceInvocationException);

            var actualExp = Assert.ThrowsAsync<BaseException>(async () => await _fakeDataSourcesController.UpdateDataSourceAsync(
                subscriptionId: "dummySubscriptionId",
                resourceGroupName: "dummyResourceGroupName",
                workspaceName: "dummyWorkspaceName",
                dataSourceId: "dummyDataSourceId",
                updated: _fakeAevaDataSourceEntity).ConfigureAwait(false));

            Assert.True(_called_GetDatasetStore);
            Assert.True(called_UpdateAsync);
            Assert.True(_convert_AEVADataSourceEntity_to_DatasetEntity);
            Assert.False(_convert_DatasetEntity_to_AEVADataSourceEntity);

            // test regular exception
            _called_GetDatasetStore = false;
            called_UpdateAsync = false;
            _convert_AEVADataSourceEntity_to_DatasetEntity = false;
            _convert_DatasetEntity_to_AEVADataSourceEntity = false;

            Mock.Get(_fakeDataSourcesController)
                .Setup(controller => controller.UpdateAsync(
                             It.IsAny<IUpdatableEntityStore<DatasetEntity>>(),
                             It.IsAny<DatasetEntity>(),
                             It.IsAny<Type>(),
                             It.IsAny<bool>()))
                .Callback(() => called_UpdateAsync = true)
                .Throws(_dummyException);

            var actualExp2 = Assert.ThrowsAsync<BaseException>(async () => await _fakeDataSourcesController.UpdateDataSourceAsync(
                subscriptionId: "dummySubscriptionId",
                resourceGroupName: "dummyResourceGroupName",
                workspaceName: "dummyWorkspaceName",
                dataSourceId: "dummyDataSourceId",
                updated: _fakeAevaDataSourceEntity).ConfigureAwait(false));

            Assert.True(_called_GetDatasetStore);
            Assert.True(called_UpdateAsync);
            Assert.True(_convert_AEVADataSourceEntity_to_DatasetEntity);
            Assert.False(_convert_DatasetEntity_to_AEVADataSourceEntity);
            StringAssert.Contains(_dummyException.Message, actualExp2.Message);
            Assert.AreEqual(_dummyException, actualExp2.InnerException);
        }

        [Test]
        public async Task GetDataSourceAsync_Tests()
        {
            // test normal execution
            _called_GetDatasetStore = false;
            _convert_DatasetEntity_to_AEVADataSourceEntity = false;
            var actualRet = await _fakeDataSourcesController.GetDataSourceAsync(
                 subscriptionId: "dummySubscriptionId",
                 resourceGroupName: "dummyResourceGroupName",
                 workspaceName: "dummyWorkspaceName",
                 id: "dummyId").ConfigureAwait(false);

            Assert.True(_called_GetDatasetStore);
            Assert.True(_convert_DatasetEntity_to_AEVADataSourceEntity);
            Assert.AreEqual(_fakeAevaDataSourceEntity, actualRet);

            // test when ServiceInvocationException out
            _called_GetDatasetStore = false;
            Mock.Get(_fakeIStoreCollection)
                .Setup(controller => controller.GetDatasetStore(
                              It.IsAny<ClaimsPrincipal>(),
                              It.IsAny<WorkspaceIdentity>()))
                .Callback(() => _called_GetDatasetStore = true)
                .Throws(_dummyServiceInvocationException);

            var actualExp = Assert.ThrowsAsync<BaseException>(async () => await _fakeDataSourcesController.GetDataSourceAsync(
                 subscriptionId: "dummySubscriptionId",
                 resourceGroupName: "dummyResourceGroupName",
                 workspaceName: "dummyWorkspaceName",
                 id: "dummyId").ConfigureAwait(false));
            Assert.True(_called_GetDatasetStore);

            // test when Exception out
            _called_GetDatasetStore = false;
            Mock.Get(_fakeIStoreCollection)
                .Setup(controller => controller.GetDatasetStore(
                              It.IsAny<ClaimsPrincipal>(),
                              It.IsAny<WorkspaceIdentity>()))
                .Callback(() => _called_GetDatasetStore = true)
                .Throws(_dummyException);

            var actualExp2 = Assert.ThrowsAsync<BaseException>(async () => await _fakeDataSourcesController.GetDataSourceAsync(
                 subscriptionId: "dummySubscriptionId",
                 resourceGroupName: "dummyResourceGroupName",
                 workspaceName: "dummyWorkspaceName",
                 id: "dummyId").ConfigureAwait(false));
            Assert.True(_called_GetDatasetStore);
            StringAssert.Contains(_dummyException.Message, actualExp2.Message);
            Assert.AreEqual(_dummyException, actualExp2.InnerException);
        }

        [Test]
        public async Task BulkGetDataSourcesAsync_Tests()
        {
            var ids = new string[] { "dummyId", "dummyId" };
            Assert.True(ids.Length > 0);

            bool called_BulkGetAsync = false;
            IEnumerable<DatasetEntity> array = Enumerable.Repeat(_fakeDatasetEntity, ids.Length).ToArray();
            Mock.Get(_fakeIDatasetStore)
                .Setup(controller => controller.BulkGetAsync(
                              It.IsAny<string>(),
                              It.IsAny<string[]>(),
                              It.IsAny<WorkspaceIdentity>()))
                .Callback(() => called_BulkGetAsync = true)
                .Returns(Task.FromResult(array));

            var actualRet = await _fakeDataSourcesController.BulkGetDataSourcesAsync(
                 subscriptionId: "dummySubscriptionId",
                 resourceGroupName: "dummyResourceGroupName",
                 workspaceName: "dummyWorkspaceName",
                 ids: ids).ConfigureAwait(false);

            Assert.AreEqual(actualRet.Count(), ids.Length);
            Assert.AreEqual(actualRet.ElementAt(0), _fakeAevaDataSourceEntity);
            Assert.True(called_BulkGetAsync);

            // test when ServiceInvocationException
            _called_GetDatasetStore = false;
            called_BulkGetAsync = false;
            Mock.Get(_fakeIStoreCollection)
                .Setup(controller => controller.GetDatasetStore(
                              It.IsAny<ClaimsPrincipal>(),
                              It.IsAny<WorkspaceIdentity>()))
                .Callback(() => _called_GetDatasetStore = true)
                .Throws(_dummyServiceInvocationException);

            var actualExp = Assert.ThrowsAsync<BaseException>(async () => await _fakeDataSourcesController.BulkGetDataSourcesAsync(
                 subscriptionId: "dummySubscriptionId",
                 resourceGroupName: "dummyResourceGroupName",
                 workspaceName: "dummyWorkspaceName",
                 ids: ids).ConfigureAwait(false));
            Assert.True(_called_GetDatasetStore);
            Assert.False(called_BulkGetAsync);

            // test when Exception out
            _called_GetDatasetStore = false;
            Mock.Get(_fakeIStoreCollection)
                .Setup(controller => controller.GetDatasetStore(
                              It.IsAny<ClaimsPrincipal>(),
                              It.IsAny<WorkspaceIdentity>()))
                .Callback(() => _called_GetDatasetStore = true)
                .Throws(_dummyException);

            var actualExp2 = Assert.ThrowsAsync<BaseException>(async () => await _fakeDataSourcesController.BulkGetDataSourcesAsync(
                 subscriptionId: "dummySubscriptionId",
                 resourceGroupName: "dummyResourceGroupName",
                 workspaceName: "dummyWorkspaceName",
                 ids: ids).ConfigureAwait(false));
            Assert.True(_called_GetDatasetStore);
            Assert.False(called_BulkGetAsync);
            StringAssert.Contains(_dummyException.Message, actualExp2.Message);
            Assert.AreEqual(_dummyException, actualExp2.InnerException);
        }

        [Test]
        public async Task GetDataSourceByHashAsync_Tests()
        {
            bool called_GetDataSourceByHashAsync = false;
            Mock.Get(_fakeIDatasetStore)
                .Setup(controller => controller.TryGetEntityByHashAsync(
                              It.IsAny<string>(),
                              It.IsAny<string>(),
                              It.IsAny<WorkspaceIdentity>()))
                .Callback(() => called_GetDataSourceByHashAsync = true)
                .Returns(Task.FromResult(_fakeDatasetEntity));

            var actualRet = await _fakeDataSourcesController.GetDataSourceByHashAsync(
                 subscriptionId: "dummySubscriptionId",
                 resourceGroupName: "dummyResourceGroupName",
                 workspaceName: "dummyWorkspaceName",
                 hash: "dummyHash").ConfigureAwait(false);

            Assert.AreEqual(actualRet, _fakeAevaDataSourceEntity);
            Assert.True(called_GetDataSourceByHashAsync);

            // test null return
            _fakeDatasetEntity = null;
            called_GetDataSourceByHashAsync = false;

            Mock.Get(_fakeIDatasetStore)
                .Setup(controller => controller.TryGetEntityByHashAsync(
                              It.IsAny<string>(),
                              It.IsAny<string>(),
                              It.IsAny<WorkspaceIdentity>()))
                .Callback(() => called_GetDataSourceByHashAsync = true)
                .Returns(Task.FromResult(_fakeDatasetEntity));

            actualRet = await _fakeDataSourcesController.GetDataSourceByHashAsync(
                 subscriptionId: "dummySubscriptionId",
                 resourceGroupName: "dummyResourceGroupName",
                 workspaceName: "dummyWorkspaceName",
                 hash: "dummyHash").ConfigureAwait(false);

            Assert.IsNull(actualRet);
            Assert.True(called_GetDataSourceByHashAsync);

            // test when ServiceInvocationException
            _called_GetDatasetStore = false;
            Mock.Get(_fakeIStoreCollection)
                .Setup(controller => controller.GetDatasetStore(
                              It.IsAny<ClaimsPrincipal>(),
                              It.IsAny<WorkspaceIdentity>()))
                .Callback(() => _called_GetDatasetStore = true)
                .Throws(_dummyServiceInvocationException);

            var actualExp = Assert.ThrowsAsync<BaseException>(async () => await _fakeDataSourcesController.GetDataSourceByHashAsync(
                 subscriptionId: "dummySubscriptionId",
                 resourceGroupName: "dummyResourceGroupName",
                 workspaceName: "dummyWorkspaceName",
                 hash: "dummyHash").ConfigureAwait(false));
            Assert.True(_called_GetDatasetStore);

            // test when Exception out
            _called_GetDatasetStore = false;
            Mock.Get(_fakeIStoreCollection)
                .Setup(controller => controller.GetDatasetStore(
                              It.IsAny<ClaimsPrincipal>(),
                              It.IsAny<WorkspaceIdentity>()))
                .Callback(() => _called_GetDatasetStore = true)
                .Throws(_dummyException);

            var actualExp2 = Assert.ThrowsAsync<BaseException>(async () => await _fakeDataSourcesController.GetDataSourceByHashAsync(
                 subscriptionId: "dummySubscriptionId",
                 resourceGroupName: "dummyResourceGroupName",
                 workspaceName: "dummyWorkspaceName",
                 hash: "dummyHash").ConfigureAwait(false));
            Assert.True(_called_GetDatasetStore);
            StringAssert.Contains(_dummyException.Message, actualExp2.Message);
            Assert.AreEqual(_dummyException, actualExp2.InnerException);
        }

        [Test]
        public async Task CreateDataSourceWithUploadAsync_Tests()
        {
            bool called_CreateAsync = false;
            Mock.Get(_fakeIDatasetStore)
                .Setup(controller => controller.CreateAsync(
                              It.IsAny<string>(),
                              It.IsAny<DatasetEntity>(),
                              It.IsAny<WorkspaceIdentity>(),
                              It.IsAny<CreatedBy>()))
                .Callback(() => called_CreateAsync = true)
                .Returns(Task.FromResult(_fakeDatasetEntity));

            var actualRet = await _fakeDataSourcesController.CreateDataSourceWithUploadAsync(
                 workspaceIdentity: new WorkspaceIdentity(),
                 creationInfo: new DataSourceCreationInfo()).ConfigureAwait(false);

            Assert.AreEqual(actualRet, _fakeAevaDataSourceEntity);
            Assert.True(called_CreateAsync);

            // test when ServiceInvocationException
            _called_GetDatasetStore = false;
            Mock.Get(_fakeIStoreCollection)
                .Setup(controller => controller.GetDatasetStore(
                              It.IsAny<ClaimsPrincipal>(),
                              It.IsAny<WorkspaceIdentity>()))
                .Callback(() => _called_GetDatasetStore = true)
                .Throws(_dummyServiceInvocationException);

            var actualExp = Assert.ThrowsAsync<BaseException>(async () => await _fakeDataSourcesController.CreateDataSourceWithUploadAsync(
                 workspaceIdentity: new WorkspaceIdentity(),
                 creationInfo: new DataSourceCreationInfo()).ConfigureAwait(false));
            Assert.True(_called_GetDatasetStore);

            // test when Exception out
            _called_GetDatasetStore = false;
            Mock.Get(_fakeIStoreCollection)
                .Setup(controller => controller.GetDatasetStore(
                              It.IsAny<ClaimsPrincipal>(),
                              It.IsAny<WorkspaceIdentity>()))
                .Callback(() => _called_GetDatasetStore = true)
                .Throws(_dummyException);

            var actualExp2 = Assert.ThrowsAsync<BaseException>(async () => await _fakeDataSourcesController.CreateDataSourceWithUploadAsync(
                 workspaceIdentity: new WorkspaceIdentity(),
                 creationInfo: new DataSourceCreationInfo()).ConfigureAwait(false));
            Assert.True(_called_GetDatasetStore);
            StringAssert.Contains(_dummyException.Message, actualExp2.Message);
            Assert.AreEqual(_dummyException, actualExp2.InnerException);
        }
    }
}
