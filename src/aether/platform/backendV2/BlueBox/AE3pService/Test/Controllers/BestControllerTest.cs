﻿// <copyright file="BestControllerTest.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Collections.Generic;
using NUnit.Framework;
using Moq;
using Microsoft.Aether.BackendCommon;
using Microsoft.Aether.DataContracts;
using Microsoft.Aether.DataContracts.Entity;
using Microsoft.MachineLearning.Common.Core.Exceptions;
using System.Threading.Tasks;
using System.Reflection;

namespace Microsoft.Aether.AE3pService.Tests.Controllers
{
    [TestFixture]
    public class BestControllerTest
    {
        private Mock<IStoreCollection> _stores;
        private Mock<IContextProvider> _operationContextProvider;
        private Mock<IWorkspaceResourcesCache> _workspaceResourcesCache;

        private BaseController _controller;

        [SetUp]
        public void SetUp()
        {
            _stores = new Mock<IStoreCollection>();
            _operationContextProvider = new Mock<IContextProvider>();
            _workspaceResourcesCache = new Mock<IWorkspaceResourcesCache>();

            _controller = new BaseController(_stores.Object, _operationContextProvider.Object, _workspaceResourcesCache.Object);
        }

        [Test]
        public void TestUpdateAsync_FieldNameContainsTags_ExpectedThrowsArgumentException()
        {
            var updated = new AmlModuleEntity
            {
                Name = "UpdatedName",
                Tags = new List<string> { "TestTagValue" },
            };

            Mock<IAmlModuleStore> store = new Mock<IAmlModuleStore>();
            store.Setup(s => s.GetEntityAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(
                    Task.FromResult(new AmlModuleEntity { Name = "UpdatedName", KvTags = new Dictionary<string, string>() }));

            MethodInfo method_UpdateAsync = typeof(BaseController).GetMethod("UpdateAsync", BindingFlags.Instance | BindingFlags.NonPublic).MakeGenericMethod(typeof(AmlModuleEntity));

            var task = (Task<AmlModuleEntity>)method_UpdateAsync.Invoke(_controller, new object[] { store.Object, updated, typeof(AmlModuleEntity), true });
            Assert.ThrowsAsync<BaseException>(async () => await task.ConfigureAwait(false));
        }
    }
}
