﻿// <copyright file="PipelineEndpointControllerTest.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Reflection;
using System.Security.Claims;
using System.Threading.Tasks;
using FluentAssertions;
using Microsoft.Aether.AE3pService.Configuration;
using Microsoft.Aether.AEVA.DataContractConverter;
using Microsoft.Aether.BackendCommon;
using Microsoft.Aether.BackendCommon.AzureMonitor;
using Microsoft.Aether.BlueBox.WorkspaceResourcesContracts;
using Microsoft.Aether.DataContracts;
using Microsoft.Aether.DataContracts.Entity;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using Microsoft.MachineLearning.Common.Core.Exceptions;
using Microsoft.MachineLearning.Common.WebApi.TeamAccounts;
using Moq;
using NUnit.Framework;
using AEVAEntityStatus = Microsoft.Aether.AEVA.DataContracts.EntityStatus;
using AEVAPipelineEndpointCreationInfo = Microsoft.Aether.AEVA.DataContracts.PipelineEndpointCreationInfo;
using AEVAPipelineEndpointEntity = Microsoft.Aether.AEVA.DataContracts.PipelineEndpointEntity;
using AEVAPipelineVersion = Microsoft.Aether.AEVA.DataContracts.PipelineVersion;

namespace Microsoft.Aether.AE3pService.Tests.Controllers
{
    public class PipelineEndpointControllerTest
    {
        private readonly MockRepository _repository;

        private PipelineEndpointController _fakePipelineEndpointController;
        private IStoreCollection _fakeIStoreCollection;
        private IContextProvider _fakeIContextProvider;
        private IWorkspaceResourcesCache _fakeIWorkspaceResourcesCache;
        private IPipelineStore _fakeIPipelineStore;
        private PipelineEntity _dummyPipelineEntity;
        private PipelineEndpointEntity _dummyPipelineEndpointEntity;
        private IAzureMonitorStatusLogger _azureMonitorStatusEmitter;
        private IPipelineEndpointStore _fakeIPipelineEndpointStore;
        private IEnumerable<PipelineEndpointEntity> _dummyPipelineEndpointEntities;
        private Tuple<IEnumerable<PipelineEndpointEntity>, string> _dummyTuple;
        private AEVAPipelineEndpointEntity _dummyAEVAPipelineEndpointEntity;
        private string _dummyId;
        private readonly WorkspaceIdentity _dummyWorkspaceIdentity;
        private WorkspaceDto _dummyWorkspaceDto;

        private AEVAPipelineEndpointCreationInfo _dummyPipelineEndpointCreationInfo;

        public PipelineEndpointControllerTest()
        {
            _repository = new MockRepository(MockBehavior.Loose) { DefaultValue = DefaultValue.Mock, CallBase = true };
            _dummyWorkspaceIdentity = new WorkspaceIdentity()
            {
                WorkspaceId = "dummywsId",
                SubscriptionId = "dummySubscriptionId",
                ResourceGroupName = "dummyResourceGroupName",
                WorkspaceName = "dummyWorkspaceName"
            };
        }

        [SetUp]
        public void Init()
        {
            _fakeIStoreCollection = _repository.Create<IStoreCollection>().Object;
            _fakeIContextProvider = _repository.Create<IContextProvider>().Object;
            _fakeIWorkspaceResourcesCache = _repository.Create<IWorkspaceResourcesCache>().Object;
            _azureMonitorStatusEmitter = _repository.Create<IAzureMonitorStatusLogger>().Object;

            var configuration = new ConfigurationBuilder().AddInMemoryCollection(GetConfiguration()).Build();

            var amlpipelineConfig = Mock.Of<IOptionsMonitor<AmlPipelineConfig>>(x => x.CurrentValue == configuration.GetSection("AmlPipelineConfig").Get<AmlPipelineConfig>());

            _fakePipelineEndpointController = _repository.Create<PipelineEndpointController>(
                    amlpipelineConfig,
                    _fakeIStoreCollection,
                    _fakeIContextProvider,
                    _fakeIWorkspaceResourcesCache,
                    _azureMonitorStatusEmitter).Object;

            var fakeWorkspaceMetadata = new WorkspaceMetadata(Guid.Empty, null, null, Guid.Empty);
            fakeWorkspaceMetadata.WorkspaceProperties = new Dictionary<string, string>();
            fakeWorkspaceMetadata.WorkspaceProperties.Add("privateLinkCount", "0");

            Mock.Get(_fakePipelineEndpointController)
                .Setup(controller => controller.GetWorkspaceMetadata()).Returns(fakeWorkspaceMetadata);

            _dummyId = Guid.NewGuid().ToString();

            _dummyPipelineEndpointCreationInfo = new AEVAPipelineEndpointCreationInfo()
            {
                PipelineId = _dummyId
            };

            _fakeIPipelineStore = _repository.Create<IPipelineStore>().Object;
            Mock.Get(_fakeIStoreCollection)
                .Setup(
                   stores => stores.GetPipelineStore(
                       It.IsAny<ClaimsPrincipal>(),
                       It.IsAny<WorkspaceIdentity>()))
                .Returns(_fakeIPipelineStore);

            _dummyPipelineEntity = new PipelineEntity()
            {
                EntityStatus = EntityStatus.Disabled,
                Id = Guid.NewGuid().ToString()
            };

            Mock.Get(_fakeIPipelineStore)
                .Setup(
                   stores => stores.GetEntityAsync(
                       It.IsAny<string>(),
                       It.IsAny<string>(),
                       It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(_dummyPipelineEntity));

            Mock.Get(_fakeIPipelineStore)
                .Setup(
                   stores => stores.UpdateAsync(
                       It.IsAny<string>(),
                       It.IsAny<PipelineEntity>(),
                       It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(_dummyPipelineEntity));

            _fakeIPipelineEndpointStore = _repository.Create<IPipelineEndpointStore>().Object;
            Mock.Get(_fakeIStoreCollection)
                .Setup(
                   stores => stores.GetPipelineEndpointStore(
                       It.IsAny<ClaimsPrincipal>(),
                       It.IsAny<WorkspaceIdentity>()))
                .Returns(_fakeIPipelineEndpointStore);

            _dummyPipelineEndpointEntity = new PipelineEndpointEntity()
            {
                Id = _dummyId,
                Name = "dummyPipelineEndpointEntityName",
                Url = "http://www.contoso.com/",
                EntityStatus = EntityStatus.Disabled,
                PipelineVersionList = new List<PipelineVersion>()
                {
                   new PipelineVersion()
                   {
                     Version = "v1",
                     PipelineId = "p1"
                   },
                   new PipelineVersion()
                   {
                     Version = "v2",
                     PipelineId = "p2"
                   }
                }
            };
            Mock.Get(_fakeIPipelineEndpointStore)
                .Setup(
                   stores => stores.CreateAsync(
                       It.IsAny<string>(),
                       It.IsAny<PipelineEndpointEntity>(),
                       It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(_dummyPipelineEndpointEntity));

            Mock.Get(_fakeIPipelineEndpointStore)
                .Setup(
                   stores => stores.GetPipelineEndpointByIdAsync(
                       It.IsAny<string>(),
                       It.IsAny<string>()))
                .Returns(Task.FromResult(_dummyPipelineEndpointEntity));

            Mock.Get(_fakeIPipelineEndpointStore)
                .Setup(
                   stores => stores.GetPipelineEndpointByNameAsync(
                       It.IsAny<string>(),
                       It.IsAny<string>()))
                .Returns(Task.FromResult(_dummyPipelineEndpointEntity));

            Mock.Get(_fakeIPipelineEndpointStore)
                .Setup(
                   stores => stores.GetEntityAsync(
                       It.IsAny<string>(),
                       It.IsAny<string>(),
                       It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(_dummyPipelineEndpointEntity));

            Mock.Get(_fakeIPipelineEndpointStore)
                .Setup(
                   stores => stores.UpdateAsync(
                       It.IsAny<string>(),
                       It.IsAny<PipelineEndpointEntity>(),
                       It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(_dummyPipelineEndpointEntity));

            _dummyPipelineEndpointEntities = new List<PipelineEndpointEntity>()
            {
                       new PipelineEndpointEntity()
                    {
                        Id = "Id1",
                        Name = "Name1",
                        Url = "http://www.contoso.com/1"
                    },
                       new PipelineEndpointEntity()
                    {
                        Id = "Id2",
                        Name = "Name2",
                        Url = "http://www.contoso.com/2"
                    }
            };
            _dummyTuple = Tuple.Create(_dummyPipelineEndpointEntities, "dummyTupleSecondItem");
            Mock.Get(_fakeIPipelineEndpointStore)
                .Setup(
                   stores => stores.ListEntityAsync(
                       It.IsAny<string>(),
                       It.IsAny<bool>(),
                       It.IsAny<string>(),
                       It.IsAny<int>()))
                .Returns(Task.FromResult(_dummyTuple));

            _dummyAEVAPipelineEndpointEntity = new AEVAPipelineEndpointEntity()
            {
                Id = _dummyId,
                EntityStatus = AEVAEntityStatus.Active,
                DefaultVersion = "1",
                Url = "http://www.contoso.com/",
                PipelineVersionList = new List<AEVAPipelineVersion>()
                {
                   new AEVAPipelineVersion()
                   {
                     Version = "2",
                     PipelineId = "p2"
                   },
                  new AEVAPipelineVersion()
                   {
                     Version = "3",
                     PipelineId = "p3"
                   }
                }
            };

            _dummyWorkspaceDto = new WorkspaceDto()
            {
                Id = _dummyWorkspaceIdentity.WorkspaceId,
                Properties = new WorkspacePropertiesDto()
                {
                    PrivateLinkCount = "5"
                }
            };
            Mock.Get(_fakeIWorkspaceResourcesCache)
                .Setup(
                  stores => stores.GetWorkspaceDtoAsync(
                      It.IsAny<WorkspaceIdentity>(),
                      It.IsAny<CreatedBy>()))
                .Returns(Task.FromResult(_dummyWorkspaceDto));

            Mock.Get(_fakePipelineEndpointController)
                .Setup(controller => controller.GetWorkspaceId())
                .Returns(_dummyId);
        }

        [Test]
        [Category("CreatePipelineEndpointAsyncTests")]
        public void TestCreatePipelineEndpointAsync_EntityNotActive_ArgumentException()
        {
            _dummyPipelineEntity.EntityStatus = EntityStatus.Disabled;
            Assert.AreNotEqual(EntityStatus.Active, _dummyPipelineEntity.EntityStatus);

            var actualExp = Assert.ThrowsAsync<BaseException>(async () => await _fakePipelineEndpointController.CreatePipelineEndpointAsync(
                              subscriptionId: "dummySubscriptionId",
                              resourceGroupName: "dummyResourceGroupName",
                              workspaceName: "dummyWorkspaceName",
                              pipelineEndpointCreationInfo: _dummyPipelineEndpointCreationInfo).ConfigureAwait(false));
            StringAssert.Contains($"EntityStatus is invalid. Cannot create pipelineEndpoint with Inactive pipelineId \"{_dummyPipelineEndpointCreationInfo.PipelineId}\".", actualExp.Message);
        }

        [Test]
        [Category("CreatePipelineEndpointAsyncTests")]
        public void TestCreatePipelineEndpointAsync_CreationInfoNameNotValid_ArgumentException()
        {
            _dummyPipelineEntity.EntityStatus = EntityStatus.Active;
            Assert.AreEqual(EntityStatus.Active, _dummyPipelineEntity.EntityStatus);
            Assert.That(_dummyPipelineEndpointCreationInfo.Name, Is.Null.Or.Empty);

            var actualExp = Assert.ThrowsAsync<BaseException>(async () => await _fakePipelineEndpointController.CreatePipelineEndpointAsync(
                              subscriptionId: "dummySubscriptionId",
                              resourceGroupName: "dummyResourceGroupName",
                              workspaceName: "dummyWorkspaceName",
                              pipelineEndpointCreationInfo: _dummyPipelineEndpointCreationInfo).ConfigureAwait(false));
            StringAssert.Contains("Cannot create pipelineEndpoint with empty name", actualExp.Message);
        }

        [Test]
        [Category("CreatePipelineEndpointAsyncTests")]
        public async Task TestCreatePipelineEndpointAsync_Normal_Return()
        {
            _dummyPipelineEntity.EntityStatus = EntityStatus.Active;
            Assert.AreEqual(EntityStatus.Active, _dummyPipelineEntity.EntityStatus);
            _dummyPipelineEndpointCreationInfo.Name = "dummyName";
            Assert.That(_dummyPipelineEndpointCreationInfo.Name, Is.Not.Null.Or.Not.Empty);

            var actualRet = await _fakePipelineEndpointController.CreatePipelineEndpointAsync(
                              subscriptionId: "dummySubscriptionId",
                              resourceGroupName: "dummyResourceGroupName",
                              workspaceName: "dummyWorkspaceName",
                              pipelineEndpointCreationInfo: _dummyPipelineEndpointCreationInfo).ConfigureAwait(false);
            actualRet.Should().BeEquivalentTo(ContractConverter.Convert<PipelineEndpointEntity, AEVAPipelineEndpointEntity>(_dummyPipelineEndpointEntity));
        }

        [Test]
        [Category("CreatePipelineEndpointAsyncTests")]
        public void TestCreatePipelineEndpointAsync_CreatePipelineEndpointFailed_Exception()
        {
            _dummyPipelineEntity.EntityStatus = EntityStatus.Active;
            Assert.AreEqual(EntityStatus.Active, _dummyPipelineEntity.EntityStatus);
            _dummyPipelineEndpointCreationInfo.Name = "dummyName";
            Assert.That(_dummyPipelineEndpointCreationInfo.Name, Is.Not.Null.Or.Not.Empty);

            var dummyException = new Exception("dummy exception");
            Mock.Get(_fakeIPipelineEndpointStore)
                .Setup(
                   stores => stores.CreateAsync(
                       It.IsAny<string>(),
                       It.IsAny<PipelineEndpointEntity>(),
                       It.IsAny<WorkspaceIdentity>()))
                .Throws(dummyException);

            var actualExp = Assert.ThrowsAsync<BaseException>(async () => await _fakePipelineEndpointController.CreatePipelineEndpointAsync(
                              subscriptionId: "dummySubscriptionId",
                              resourceGroupName: "dummyResourceGroupName",
                              workspaceName: "dummyWorkspaceName",
                              pipelineEndpointCreationInfo: _dummyPipelineEndpointCreationInfo).ConfigureAwait(false));
            Assert.AreEqual($"Unable to create PipelineEndpoint with pipelineId {_dummyPipelineEndpointCreationInfo.PipelineId}. The error message is: 'dummy exception'.", actualExp.Message);
        }

        [Test]
        [Category("VerifyVersionTests")]
        public void TestVerifyVersion_Normal_Return()
        {
            MethodInfo method_VerifyVersion = typeof(PipelineEndpointController).GetMethod("VerifyVersion", BindingFlags.Static | BindingFlags.NonPublic);
            string defaultVersion = "21";
            int maxVersion = 30;
            var ret = (string)method_VerifyVersion.Invoke(null, new object[] { defaultVersion, maxVersion });
            Assert.AreEqual(defaultVersion, ret);
        }

        [Test]
        [Category("VerifyVersionTests")]
        public void TestVerifyVersion_VersionTooLarge_ArgumentException()
        {
            MethodInfo method_VerifyVersion = typeof(PipelineEndpointController).GetMethod("VerifyVersion", BindingFlags.Static | BindingFlags.NonPublic);
            string defaultVersion = "31";
            int maxVersion = 30;
            var exp = Assert.Throws<TargetInvocationException>(() => method_VerifyVersion.Invoke(null, new object[] { defaultVersion, maxVersion }));
            Assert.AreEqual($"defaultVersion is invalid. pipelineEndpoint default version \"{defaultVersion}\" is not an integer or is more than maxVersion \"{maxVersion}\" in pipelineVersion List.", exp.InnerException.Message);
        }

        [Test]
        [Category("VerifyVersionTests")]
        public void TestVerifyVersion_VersionNegative_ArgumentException()
        {
            MethodInfo method_VerifyVersion = typeof(PipelineEndpointController).GetMethod("VerifyVersion", BindingFlags.Static | BindingFlags.NonPublic);
            string defaultVersion = "-2";
            int maxVersion = 30;
            var exp = Assert.Throws<TargetInvocationException>(() => method_VerifyVersion.Invoke(null, new object[] { defaultVersion, maxVersion }));
            Assert.AreEqual($"defaultVersion is invalid. pipelineEndpoint default version \"{defaultVersion}\" is not an integer or is more than maxVersion \"{maxVersion}\" in pipelineVersion List.", exp.InnerException.Message);
        }

        [Test]
        [Category("GetPipelineEndpointByIdAsyncTests")]
        public async Task TestGetPipelineEndpointByIdAsync_Normal_Return()
        {
            var actualRet = await _fakePipelineEndpointController.GetPipelineEndpointByIdAsync(
                              subscriptionId: "dummySubscriptionId",
                              resourceGroupName: "dummyResourceGroupName",
                              workspaceName: "dummyWorkspaceName",
                              id: "dummyId").ConfigureAwait(false);
            actualRet.Should().BeEquivalentTo(ContractConverter.Convert<PipelineEndpointEntity, AEVAPipelineEndpointEntity>(_dummyPipelineEndpointEntity));
        }

        [Test]
        [Category("GetPipelineEndpointByIdAsyncTests")]
        public void TestGetPipelineEndpointByIdAsync_GetPipelineEndpointFailed_Exception()
        {
            var dummyException = new Exception("dummy exception");
            Mock.Get(_fakeIPipelineEndpointStore)
                .Setup(
                   stores => stores.GetPipelineEndpointByIdAsync(
                       It.IsAny<string>(),
                       It.IsAny<string>()))
                .Throws(dummyException);

            var actualExp = Assert.ThrowsAsync<BaseException>(async () => await _fakePipelineEndpointController.GetPipelineEndpointByIdAsync(
                              subscriptionId: "dummySubscriptionId",
                              resourceGroupName: "dummyResourceGroupName",
                              workspaceName: "dummyWorkspaceName",
                              id: "dummyId").ConfigureAwait(false));
            Assert.AreEqual("Unable to get pipelineEndpoint. The error message is: 'dummy exception'.", actualExp.Message);
        }

        [Test]
        [Category("GetPipelineEndpointByNameAsyncTests")]
        public async Task TestGetPipelineEndpointByNameAsync_Normal_Return()
        {
            var actualRet = await _fakePipelineEndpointController.GetPipelineEndpointByNameAsync(
                              subscriptionId: "dummySubscriptionId",
                              resourceGroupName: "dummyResourceGroupName",
                              workspaceName: "dummyWorkspaceName",
                              name: "dummyName").ConfigureAwait(false);
            actualRet.Should().BeEquivalentTo(ContractConverter.Convert<PipelineEndpointEntity, AEVAPipelineEndpointEntity>(_dummyPipelineEndpointEntity));
        }

        [Test]
        [Category("GetPipelineEndpointByNameAsyncTests")]
        public void TestGetPipelineEndpointByNameAsync_GetPipelineEndpointFailed_Exception()
        {
            var dummyException = new Exception("dummy exception");
            Mock.Get(_fakeIPipelineEndpointStore)
                .Setup(
                   stores => stores.GetPipelineEndpointByNameAsync(
                       It.IsAny<string>(),
                       It.IsAny<string>()))
                .Throws(dummyException);

            var actualExp = Assert.ThrowsAsync<BaseException>(async () => await _fakePipelineEndpointController.GetPipelineEndpointByNameAsync(
                              subscriptionId: "dummySubscriptionId",
                              resourceGroupName: "dummyResourceGroupName",
                              workspaceName: "dummyWorkspaceName",
                              name: "dummyName").ConfigureAwait(false));
            Assert.AreEqual("Unable to get pipelineEndpoint by name 'dummyName'. The error message is: 'dummy exception'.", actualExp.Message);
        }

        [Test]
        [Category("UpdatePipelineEndpointAsyncTests")]
        public void TestUpdatePipelineEndpointAsync_IdNotMatch_ArgumentException()
        {
            string id = Guid.NewGuid().ToString();
            Assert.AreNotEqual(id, _dummyAEVAPipelineEndpointEntity.Id);

            var actualExp = Assert.ThrowsAsync<BaseException>(async () => await _fakePipelineEndpointController.UpdatePipelineEndpointAsync(
                  subscriptionId: "dummySubscriptionId",
                  resourceGroupName: "dummyResourceGroupName",
                  workspaceName: "dummyWorkspaceName",
                  id: id,
                  updated: _dummyAEVAPipelineEndpointEntity).ConfigureAwait(false));
            Assert.AreEqual($"id is invalid. PipelineEndpoint Id \"{id}\" should be equal to updated.Id \"{_dummyAEVAPipelineEndpointEntity.Id}\".", actualExp.Message);
        }

        [Test]
        [Category("UpdatePipelineEndpointAsyncTests")]
        public void TestUpdatePipelineEndpointAsync_PipelineNotInEndpoint_ArgumentException()
        {
            Assert.AreNotEqual((int)_dummyAEVAPipelineEndpointEntity.EntityStatus, (int)_dummyPipelineEndpointEntity.EntityStatus);
            Assert.AreEqual(AEVAEntityStatus.Active, _dummyAEVAPipelineEndpointEntity.EntityStatus);
            var actualExp = Assert.ThrowsAsync<BaseException>(async () => await _fakePipelineEndpointController.UpdatePipelineEndpointAsync(
                  subscriptionId: "dummySubscriptionId",
                  resourceGroupName: "dummyResourceGroupName",
                  workspaceName: "dummyWorkspaceName",
                  id: _dummyAEVAPipelineEndpointEntity.Id,
                  updated: _dummyAEVAPipelineEndpointEntity).ConfigureAwait(false));
            StringAssert.Contains($"updatedPipelineEndpoint is invalid. Cannot update endpoint, some pipelines are removed from pipelineendpoint id \"{_dummyAEVAPipelineEndpointEntity.Id}\"", actualExp.Message);
        }

        [Test]
        [Category("UpdatePipelineEndpointAsyncTests")]
        public async Task TestUpdatePipelineEndpointAsync_Normal_Return()
        {
            _dummyPipelineEndpointEntity.EntityStatus = EntityStatus.Active;

            Assert.AreEqual((int)_dummyAEVAPipelineEndpointEntity.EntityStatus, (int)_dummyPipelineEndpointEntity.EntityStatus);
            Assert.AreEqual(AEVAEntityStatus.Active, _dummyAEVAPipelineEndpointEntity.EntityStatus);
            var actualRet = await _fakePipelineEndpointController.UpdatePipelineEndpointAsync(
                  subscriptionId: "dummySubscriptionId",
                  resourceGroupName: "dummyResourceGroupName",
                  workspaceName: "dummyWorkspaceName",
                  id: _dummyAEVAPipelineEndpointEntity.Id,
                  updated: _dummyAEVAPipelineEndpointEntity).ConfigureAwait(false);
            actualRet.Should().BeEquivalentTo(ContractConverter.Convert<PipelineEndpointEntity, AEVAPipelineEndpointEntity>(_dummyPipelineEndpointEntity));
        }

        [Test]
        [Category("UpdatePipelineEndpointAsyncTests")]
        public void TestUpdatePipelineEndpointAsync_UpdateFailed_Exception()
        {
            _dummyPipelineEndpointEntity.EntityStatus = EntityStatus.Active;

            var dummyException = new Exception("dummy exception");
            Mock.Get(_fakeIPipelineEndpointStore)
                .Setup(
                   stores => stores.UpdateAsync(
                       It.IsAny<string>(),
                       It.IsAny<PipelineEndpointEntity>(),
                       It.IsAny<WorkspaceIdentity>()))
                .Throws(dummyException);

            Assert.AreEqual((int)_dummyAEVAPipelineEndpointEntity.EntityStatus, (int)_dummyPipelineEndpointEntity.EntityStatus);
            Assert.AreEqual(AEVAEntityStatus.Active, _dummyAEVAPipelineEndpointEntity.EntityStatus);
            var actualExp = Assert.ThrowsAsync<BaseException>(async () => await _fakePipelineEndpointController.UpdatePipelineEndpointAsync(
                  subscriptionId: "dummySubscriptionId",
                  resourceGroupName: "dummyResourceGroupName",
                  workspaceName: "dummyWorkspaceName",
                  id: _dummyAEVAPipelineEndpointEntity.Id,
                  updated: _dummyAEVAPipelineEndpointEntity).ConfigureAwait(false));
            StringAssert.Contains(dummyException.Message, actualExp.Message);
        }

        [Test]
        [Category("UpdatePipelineWithPipelineEndpointIdAsyncTests")]
        public async Task TestUpdatePipelineWithPipelineEndpointIdAsync_PipelineEndpointIdNull_Return()
        {
            MethodInfo method_UpdatePipelineWithPipelineEndpointId = typeof(PipelineEndpointController).GetMethod("UpdatePipelineWithPipelineEndpointIdAsync", BindingFlags.Instance | BindingFlags.NonPublic);
            string pipelineEndpointId = "dummyPipelineEndpointId";
            Assert.IsNull(_dummyPipelineEntity.PipelineEndpointIds);
            var task = (Task)method_UpdatePipelineWithPipelineEndpointId.Invoke(_fakePipelineEndpointController, new object[] { "dummyPipelineId", pipelineEndpointId, _dummyWorkspaceIdentity });
            await task.ConfigureAwait(false);

            Mock.Get(_fakeIPipelineStore)
                .Verify(
                   stores => stores.UpdateAsync(
                       It.Is<string>(s => s == _dummyWorkspaceIdentity.WorkspaceId),
                       It.Is<PipelineEntity>(entity => entity.PipelineEndpointIds.Count == 1 && entity.PipelineEndpointIds[0] == pipelineEndpointId),
                       _dummyWorkspaceIdentity), Times.Once);
        }

        [Test]
        [Category("UpdatePipelineWithPipelineEndpointIdAsyncTests")]
        public async Task TestUpdatePipelineWithPipelineEndpointIdAsync_PipelineEndpointIdNotNull_Return()
        {
            MethodInfo method_UpdatePipelineWithPipelineEndpointId = typeof(PipelineEndpointController).GetMethod("UpdatePipelineWithPipelineEndpointIdAsync", BindingFlags.Instance | BindingFlags.NonPublic);
            string pipelineEndpointId = "dummyPipelineEndpointId";

            _dummyPipelineEntity.PipelineEndpointIds = new List<string>()
            {
                  "id1",
                  "id2"
            };
            int count = _dummyPipelineEntity.PipelineEndpointIds.Count;
            Assert.True(count > 0);
            var task = (Task)method_UpdatePipelineWithPipelineEndpointId.Invoke(_fakePipelineEndpointController, new object[] { "dummyPipelineId", pipelineEndpointId, _dummyWorkspaceIdentity });
            await task.ConfigureAwait(false);

            Mock.Get(_fakeIPipelineStore)
                .Verify(
                   stores => stores.UpdateAsync(
                       It.Is<string>(s => s == _dummyWorkspaceIdentity.WorkspaceId),
                       It.Is<PipelineEntity>(entity => (entity.PipelineEndpointIds.Count == count + 1) && entity.PipelineEndpointIds[count] == pipelineEndpointId),
                       _dummyWorkspaceIdentity), Times.Once);
        }

        [Test]
        [Category("UpdatePipelineWithPipelineEndpointIdAsyncTests")]
        public void TestUpdatePipelineWithPipelineEndpointIdAsync_UpdateFailed_Exception()
        {
            MethodInfo method_UpdatePipelineWithPipelineEndpointId = typeof(PipelineEndpointController).GetMethod("UpdatePipelineWithPipelineEndpointIdAsync", BindingFlags.Instance | BindingFlags.NonPublic);
            string pipelineEndpointId = "dummyPipelineEndpointId";

            var dummyException = new Exception("dummy exception");
            Mock.Get(_fakeIPipelineStore)
                .Setup(
                   stores => stores.UpdateAsync(
                       It.IsAny<string>(),
                       It.IsAny<PipelineEntity>(),
                       It.IsAny<WorkspaceIdentity>()))
                .Throws(dummyException);

            var task = (Task)method_UpdatePipelineWithPipelineEndpointId.Invoke(_fakePipelineEndpointController, new object[] { "dummyPipelineId", pipelineEndpointId, _dummyWorkspaceIdentity });
            var actualExp = Assert.ThrowsAsync<Exception>(async () => await task.ConfigureAwait(false));

            Assert.AreEqual(dummyException.Message, actualExp.Message);
        }

        private static IDictionary<string, string> GetConfiguration()
        {
            IDictionary<string, string> settings = new Dictionary<string, string>()
            {
                {"AmlPipelineConfig:PipelineEndpointUrlTemplate", "https://test.api.azureml-test.ms/pipelines/v1.0/subscriptions/{0}/resourceGroups/{1}/providers/Microsoft.MachineLearningServices/workspaces/{2}/PipelineRuns/PipelineEndpointSubmit/Id/{3}"},
                {"AmlPipelineConfig:PrivateLinkPipelineEndpointUrlTemplate", "https://{0}.workspace.test.api.azureml-test.ms/pipelines/v1.0/subscriptions/{0}/resourceGroups/{1}/providers/Microsoft.MachineLearningServices/workspaces/{2}/PipelineRuns/PipelineEndpointSubmit/Id/{4}"}
            };
            return settings;
        }
    }
}
