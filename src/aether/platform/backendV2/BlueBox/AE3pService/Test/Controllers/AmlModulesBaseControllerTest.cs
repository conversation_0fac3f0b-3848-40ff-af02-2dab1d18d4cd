﻿// <copyright file="AmlModulesBaseControllerTest.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Security.Claims;
using System.Threading.Tasks;
using Microsoft.Aether.AEVA.DataContracts;
using Microsoft.Aether.BackendCommon;
using Microsoft.Aether.BackendCommon.AzureMonitor;
using Microsoft.Aether.DataContracts;
using Microsoft.Aether.DataContracts.Entity;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Routing;
using Microsoft.MachineLearning.Common.Core.Exceptions;
using Moq;
using NUnit.Framework;

namespace Microsoft.Aether.AE3pService.Tests.Controllers
{
    [TestFixture]
    public class AmlModulesBaseControllerTest
    {
        private Mock<IStoreCollection> _stores;
        private Mock<IContextProvider> _contextProvider;
        private Mock<IWorkspaceResourcesCache> _workspaceResourcesCache;
        private Mock<IAzureMonitorStatusLogger> _azureMonitorStatusEmitter;
        private AmlModulesBaseController _controller;
        private WorkspaceIdentity _dummyWorkspaceIdentity = new WorkspaceIdentity();

        [SetUp]
        public void SetUp()
        {
            _stores = new Mock<IStoreCollection>();
            _contextProvider = new Mock<IContextProvider>();
            _azureMonitorStatusEmitter = new Mock<IAzureMonitorStatusLogger>();
            _workspaceResourcesCache = new Mock<IWorkspaceResourcesCache>();

            _controller = new AmlModulesBaseController(_stores.Object, _contextProvider.Object, _workspaceResourcesCache.Object, _azureMonitorStatusEmitter.Object);
        }

        [Test]
        public async Task TestCreateAzureMLModuleAsync_NormalCase_ExpectedAzureMLModule()
        {
            // Set-up Mocks
            Mock<IAmlModuleStore> store = new Mock<IAmlModuleStore>();
            store.Setup(s => s.CreateAsync(It.IsAny<string>(), It.IsAny<AmlModuleEntity>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(
                    (string s, AmlModuleEntity moduleEntity, WorkspaceIdentity identity) => Task.FromResult(moduleEntity));
            _stores.Setup(s => s.GetAmlModuleStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(store.Object);

            // Test the protected-level method by reflection
            MethodInfo method_CreateAzureMLModuleAsync = typeof(AmlModulesBaseController).GetMethod("CreateAzureMLModuleAsync", BindingFlags.Instance | BindingFlags.NonPublic);

            var exampleCreationInfo = new AzureMLModuleCreationInfo(
                "TestModuleName",
                "TestDescription",
                new Dictionary<string, string> { { "tagKey", "tagValue" } },
                new Dictionary<string, string> { { "propKey", "propValue" } },
                "TestCategory");
            var task = (Task<AzureMLModule>)method_CreateAzureMLModuleAsync.Invoke(_controller, new object[] { _dummyWorkspaceIdentity, exampleCreationInfo });
            var result = await task.ConfigureAwait(false);
            Assert.AreEqual(result.Name, exampleCreationInfo.Name);
            Assert.AreEqual(result.Description, exampleCreationInfo.Description);
            Assert.AreEqual(result.KvTags.Count, exampleCreationInfo.KvTags.Count);
            Assert.AreEqual(result.Properties.Count, exampleCreationInfo.Properties.Count);
            Assert.AreEqual(result.Category, exampleCreationInfo.Category);
        }

        [Test]
        public void TestCreateAzureMLModuleAsync_AmlModuleStoreThrows_ExpectedBaseException()
        {
            // Set-up Mocks
            _stores.Setup(s => s.GetAmlModuleStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Throws(new Exception());

            // Test the protected-level method by reflection
            MethodInfo method_CreateAzureMLModuleAsync = typeof(AmlModulesBaseController).GetMethod("CreateAzureMLModuleAsync", BindingFlags.Instance | BindingFlags.NonPublic);

            var exampleCreationInfo = new AzureMLModuleCreationInfo(
                "TestModuleName",
                "TestDescription",
                new Dictionary<string, string> { { "tagKey", "tagValue" } },
                new Dictionary<string, string> { { "propKey", "propValue" } },
                "TestCategory");
            var task = (Task<AzureMLModule>)method_CreateAzureMLModuleAsync.Invoke(_controller, new object[] { _dummyWorkspaceIdentity, exampleCreationInfo });
            Assert.ThrowsAsync<BaseException>(async () => await task.ConfigureAwait(false));
        }

        [Test]
        public void TestCreateAzureMLModuleAsync_CreationInfoNameMatchBuiltinModulePrefix_ExpectedBaseException()
        {
            // Set-up Mocks
            Mock<IAmlModuleStore> store = new Mock<IAmlModuleStore>();
            store.Setup(s => s.CreateAsync(It.IsAny<string>(), It.IsAny<AmlModuleEntity>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(
                    (string s, AmlModuleEntity moduleEntity, WorkspaceIdentity identity) => Task.FromResult(moduleEntity));
            _stores.Setup(s => s.GetAmlModuleStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(store.Object);

            // Test the protected-level method by reflection
            MethodInfo method_CreateAzureMLModuleAsync = typeof(AmlModulesBaseController).GetMethod("CreateAzureMLModuleAsync", BindingFlags.Instance | BindingFlags.NonPublic);

            var exampleCreationInfo = new AzureMLModuleCreationInfo(
                @"azureml://TestModuleName",
                "TestDescription",
                new Dictionary<string, string> { { "tagKey", "tagValue" } },
                new Dictionary<string, string> { { "propKey", "propValue" } },
                "TestCategory");
            var task = (Task<AzureMLModule>)method_CreateAzureMLModuleAsync.Invoke(_controller, new object[] { _dummyWorkspaceIdentity, exampleCreationInfo });
            Assert.ThrowsAsync<BaseException>(async () => await task.ConfigureAwait(false));
        }

        [Test]
        public async Task TestCreateAzureMLModulesWithIdAsync_NormalCase_ExpectedAzureMLModule()
        {
            // Set-up Mocks
            Mock<IAmlModuleStore> store = new Mock<IAmlModuleStore>();
            store.Setup(s => s.CreateAsync(It.IsAny<string>(), It.IsAny<AmlModuleEntity>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(
                    (string s, AmlModuleEntity moduleEntity, WorkspaceIdentity identity) => Task.FromResult(moduleEntity));
            _stores.Setup(s => s.GetAmlModuleStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(store.Object);

            // Test the protected-level method by reflection
            MethodInfo method_CreateAzureMLModulesWithIdAsync = typeof(AmlModulesBaseController).GetMethod("CreateAzureMLModulesWithIdAsync", BindingFlags.Instance | BindingFlags.NonPublic);

            var exampleCreationInfo = new AzureMLModuleCreationInfo(
                "TestModuleName",
                "TestDescription",
                new Dictionary<string, string> { { "tagKey", "tagValue" } },
                new Dictionary<string, string> { { @"azureml/id", Guid.NewGuid().ToString() } },
                "TestCategory");
            var task = (Task<AzureMLModule>)method_CreateAzureMLModulesWithIdAsync.Invoke(_controller, new object[] { _dummyWorkspaceIdentity, exampleCreationInfo });
            var result = await task.ConfigureAwait(false);
            Assert.AreEqual("azureml://" + exampleCreationInfo.Name, result.Name);
            Assert.AreEqual(exampleCreationInfo.Description, result.Description);
            Assert.AreEqual(exampleCreationInfo.KvTags.Count, result.KvTags.Count);
            Assert.AreEqual(exampleCreationInfo.Category, result.Category);
            Assert.AreEqual(exampleCreationInfo.Properties[@"azureml/id"], result.Id);
        }

        [Test]
        public async Task TestUpdateAzureMLModuleAsync_NormalCase_ExpectedAzureMLModule()
        {
            // Set-up Mocks
            Mock<IAmlModuleStore> store = new Mock<IAmlModuleStore>();
            store.Setup(s => s.GetEntityAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(new AmlModuleEntity()));
            store.Setup(s => s.UpdateAsync(It.IsAny<string>(), It.IsAny<AmlModuleEntity>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(
                    (string s, AmlModuleEntity entity, WorkspaceIdentity identity) => Task.FromResult(entity));

            _stores.Setup(s => s.GetAmlModuleStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(store.Object);

            // Test the protected-level method by reflection
            MethodInfo method_UpdateAzureMLModuleAsync = typeof(AmlModulesBaseController).GetMethod("UpdateAzureMLModuleAsync", BindingFlags.Instance | BindingFlags.NonPublic);

            var updated = new AzureMLModule
            {
                Id = "TestId",
                Name = "TestName",
                Description = "TestDescription",
                KvTags = new Dictionary<string, string> { { "tagKey", "tagValue" } },
                Properties = new Dictionary<string, string> { { "propKey", "propValue" } },
                Category = "TestCategory",
                DefaultVersion = "TestVersion",
                Versions = new List<AzureMLModuleVersionDescriptor> { { new AzureMLModuleVersionDescriptor { Version = "TestVersion" } } },
                EntityStatus = AEVA.DataContracts.EntityStatus.Active,
            };

            // Test with active status.
            var task = (Task<AzureMLModule>)method_UpdateAzureMLModuleAsync.Invoke(_controller, new object[] { _dummyWorkspaceIdentity, updated.Id, updated });
            var result = await task.ConfigureAwait(false);
            Assert.IsTrue(1 == 1);
            Assert.AreEqual(AEVA.DataContracts.EntityStatus.Active, result.EntityStatus);

            // Test with Deprecated Status.
            updated.EntityStatus = AEVA.DataContracts.EntityStatus.Deprecated;
            var task2 = (Task<AzureMLModule>)method_UpdateAzureMLModuleAsync.Invoke(_controller, new object[] { _dummyWorkspaceIdentity, updated.Id, updated });
            var result2 = await task2.ConfigureAwait(false);
            Assert.IsTrue(1 == 1);
            Assert.AreEqual(AEVA.DataContracts.EntityStatus.Deprecated, result2.EntityStatus);

            // Test with Disabled Status.
            updated.EntityStatus = AEVA.DataContracts.EntityStatus.Disabled;
            var task3 = (Task<AzureMLModule>)method_UpdateAzureMLModuleAsync.Invoke(_controller, new object[] { _dummyWorkspaceIdentity, updated.Id, updated });
            var result3 = await task3.ConfigureAwait(false);
            Assert.IsTrue(1 == 1);
            Assert.AreEqual(AEVA.DataContracts.EntityStatus.Disabled, result3.EntityStatus);
        }

        [Test]
        public void TestUpdateAzureMLModuleAsync_ModuleIdInconsistency_ExpectedBaseException()
        {
            MethodInfo method_UpdateAzureMLModuleAsync = typeof(AmlModulesBaseController).GetMethod("UpdateAzureMLModuleAsync", BindingFlags.Instance | BindingFlags.NonPublic);

            var updated = new AzureMLModule
            {
                Id = "TestId",
                Name = "TestName",
                Description = "TestDescription",
                KvTags = new Dictionary<string, string> { { "tagKey", "tagValue" } },
                Properties = new Dictionary<string, string> { { "propKey", "propValue" } },
                Category = "TestCategory",
                DefaultVersion = "TestVersion",
                Versions = new List<AzureMLModuleVersionDescriptor> { { new AzureMLModuleVersionDescriptor { Version = "TestVersion" } } },
                EntityStatus = AEVA.DataContracts.EntityStatus.Active,
            };

            // Test with active status.
            var task = (Task<AzureMLModule>)method_UpdateAzureMLModuleAsync.Invoke(_controller, new object[] { _dummyWorkspaceIdentity, "wrongId", updated });
            Assert.ThrowsAsync<BaseException>(async () => await task.ConfigureAwait(false));
        }

        [Test]
        public void TestUpdateAzureMLModuleAsync_ModuleNameMatchBuiltinModulePrefix_ExpectedBaseException()
        {
            MethodInfo method_UpdateAzureMLModuleAsync = typeof(AmlModulesBaseController).GetMethod("UpdateAzureMLModuleAsync", BindingFlags.Instance | BindingFlags.NonPublic);

            var updated = new AzureMLModule
            {
                Id = "TestId",
                Name = @"azureml://TestModuleName",
                Description = "TestDescription",
                KvTags = new Dictionary<string, string> { { "tagKey", "tagValue" } },
                Properties = new Dictionary<string, string> { { "propKey", "propValue" } },
                Category = "TestCategory",
                DefaultVersion = "TestVersion",
                Versions = new List<AzureMLModuleVersionDescriptor> { { new AzureMLModuleVersionDescriptor { Version = "TestVersion" } } },
                EntityStatus = AEVA.DataContracts.EntityStatus.Active,
            };

            // Test with active status.
            var task = (Task<AzureMLModule>)method_UpdateAzureMLModuleAsync.Invoke(_controller, new object[] { _dummyWorkspaceIdentity, updated.Id, updated });
            Assert.ThrowsAsync<BaseException>(async () => await task.ConfigureAwait(false));
        }

        [Test]
        public void TestUpdateAzureMLModuleAsync_StoresThrowArgumentException_ExpectedBaseException()
        {
            _stores.Setup(s => s.GetAmlModuleStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Throws(new ArgumentException());

            MethodInfo method_UpdateAzureMLModuleAsync = typeof(AmlModulesBaseController).GetMethod("UpdateAzureMLModuleAsync", BindingFlags.Instance | BindingFlags.NonPublic);

            var updated = new AzureMLModule
            {
                Id = "TestId",
                Name = "TestModuleName",
                Description = "TestDescription",
                KvTags = new Dictionary<string, string> { { "tagKey", "tagValue" } },
                Properties = new Dictionary<string, string> { { "propKey", "propValue" } },
                Category = "TestCategory",
                DefaultVersion = "TestVersion",
                Versions = new List<AzureMLModuleVersionDescriptor> { { new AzureMLModuleVersionDescriptor { Version = "TestVersion" } } },
                EntityStatus = AEVA.DataContracts.EntityStatus.Active,
            };

            // Test with active status.
            var task = (Task<AzureMLModule>)method_UpdateAzureMLModuleAsync.Invoke(_controller, new object[] { _dummyWorkspaceIdentity, updated.Id, updated });
            Assert.ThrowsAsync<BaseException>(async () => await task.ConfigureAwait(false));
        }

        [Test]
        public void TestUpdateAzureMLModuleAsync_ModuleVersionsNotContainDefaultVersion_ExpectedBaseException()
        {
            // Set-up Mocks
            Mock<IAmlModuleStore> store = new Mock<IAmlModuleStore>();
            store.Setup(s => s.GetEntityAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(new AmlModuleEntity()));
            store.Setup(s => s.UpdateAsync(It.IsAny<string>(), It.IsAny<AmlModuleEntity>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(
                    (string s, AmlModuleEntity entity, WorkspaceIdentity identity) => Task.FromResult(entity));

            _stores.Setup(s => s.GetAmlModuleStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(store.Object);

            MethodInfo method_UpdateAzureMLModuleAsync = typeof(AmlModulesBaseController).GetMethod("UpdateAzureMLModuleAsync", BindingFlags.Instance | BindingFlags.NonPublic);

            var updated = new AzureMLModule
            {
                Id = "TestId",
                Name = "TestModuleName",
                Description = "TestDescription",
                KvTags = new Dictionary<string, string> { { "tagKey", "tagValue" } },
                Properties = new Dictionary<string, string> { { "propKey", "propValue" } },
                Category = "TestCategory",
                DefaultVersion = "TestVersion0",
                Versions = new List<AzureMLModuleVersionDescriptor> { { new AzureMLModuleVersionDescriptor { Version = "TestVersion" } } },
                EntityStatus = AEVA.DataContracts.EntityStatus.Active,
            };

            // Test with active status.
            var task = (Task<AzureMLModule>)method_UpdateAzureMLModuleAsync.Invoke(_controller, new object[] { _dummyWorkspaceIdentity, updated.Id, updated });
            Assert.ThrowsAsync<BaseException>(async () => await task.ConfigureAwait(false));
        }

        [Test]
        public async Task TestGetAzureMLModuleAsync_NormalCase_ExpectedAzureMLModuleAsync()
        {
            // Set-up Mocks
            Mock<IAmlModuleStore> store = new Mock<IAmlModuleStore>();
            store.Setup(s => s.CreateAsync(It.IsAny<string>(), It.IsAny<AmlModuleEntity>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(
                    (string s, AmlModuleEntity moduleEntity, WorkspaceIdentity identity) => Task.FromResult(moduleEntity));
            store.Setup(s => s.GetEntityAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(new AmlModuleEntity
                {
                    Id = "TestId",
                    Name = "TestModuleName",
                    Description = "TestDescription",
                    KvTags = new Dictionary<string, string> { { "tagKey", "tagValue" } },
                    Properties = new Dictionary<string, string> { { "propKey", "propValue" } },
                    Category = "TestCategory",
                    DefaultVersion = "TestVersion",
                    Versions = new List<AmlModuleVersionDescriptor> { { new AmlModuleVersionDescriptor { Version = "TestVersion" } } },
                    EntityStatus = DataContracts.EntityStatus.Active,
                }));

            _stores.Setup(s => s.GetAmlModuleStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(store.Object);

            MethodInfo method_GetAzureMLModuleAsync = typeof(AmlModulesBaseController).GetMethod("GetAzureMLModuleAsync", BindingFlags.Instance | BindingFlags.NonPublic);

            // Test with active status.
            var task = (Task<AzureMLModule>)method_GetAzureMLModuleAsync.Invoke(_controller, new object[] { _dummyWorkspaceIdentity, "RandomId" });
            var result = await task.ConfigureAwait(false);
            Assert.AreEqual("TestModuleName", result.Name);
            Assert.AreEqual("TestDescription", result.Description);
            Assert.AreEqual("TestCategory", result.Category);
            Assert.AreEqual("TestVersion", result.DefaultVersion);
            Assert.AreEqual(AEVA.DataContracts.EntityStatus.Active, result.EntityStatus);
        }

        [Test]
        public void TestGetAzureMLModuleAsync_StoreThrowsArgumentException_ExpectedBaseException()
        {
            _stores.Setup(s => s.GetAmlModuleStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Throws(new ArgumentException());

            MethodInfo method_GetAzureMLModuleAsync = typeof(AmlModulesBaseController).GetMethod("GetAzureMLModuleAsync", BindingFlags.Instance | BindingFlags.NonPublic);

            // Test with active status.
            var task = (Task<AzureMLModule>)method_GetAzureMLModuleAsync.Invoke(_controller, new object[] { _dummyWorkspaceIdentity, "RandomId" });
            Assert.ThrowsAsync<BaseException>(async () => await task.ConfigureAwait(false));
        }

        [Test]
        public void TestGetResolveAzureMLModuleVersionAsync_ModuleStoreThrowsArgumentExpcetion_ExpectedArgumentException()
        {
            Mock<IAmlModuleStore> store = new Mock<IAmlModuleStore>();
            store.Setup(s => s.CreateAsync(It.IsAny<string>(), It.IsAny<AmlModuleEntity>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(
                    (string s, AmlModuleEntity moduleEntity, WorkspaceIdentity identity) => Task.FromResult(moduleEntity));
            store.Setup(s => s.GetEntityAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(new AmlModuleEntity
                {
                    Versions = new List<AmlModuleVersionDescriptor> { { new AmlModuleVersionDescriptor { Version = "TestVersion" } } },
                    Id = "TestId",
                }));

            Mock<IModuleStore> moduleStore = new Mock<IModuleStore>();
            moduleStore.Setup(m => m.BulkGetIdsAsync(It.IsAny<string>(), It.IsAny<string[]>(), It.IsAny<WorkspaceIdentity>()))
                .Throws(new ArgumentException());

            Mock<IDataTypeStore> datatypeStore = new Mock<IDataTypeStore>();

            _stores.Setup(s => s.GetAmlModuleStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(store.Object);
            _stores.Setup(s => s.GetModuleStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(moduleStore.Object);
            _stores.Setup(s => s.GetDataTypeStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(datatypeStore.Object);

            MethodInfo method_GetResolveAzureMLModuleVersionAsync = typeof(AmlModulesBaseController).GetMethod("GetResolveAzureMLModuleVersionAsync", BindingFlags.Instance | BindingFlags.NonPublic);

            var task = (Task<AzureMLModuleVersion>)method_GetResolveAzureMLModuleVersionAsync.Invoke(_controller, new object[] { _dummyWorkspaceIdentity, "RandomId" });
            Assert.ThrowsAsync<ArgumentException>(async () => await task.ConfigureAwait(false));
        }

        [Test]
        public async Task TestListAzureMLModulesAsync_NormalCase_ExpectedListOfModule()
        {
            // The HeaderDictionary is needed for adding HTTP headers to the response.
            // This needs a couple of different Mocks, because many properties in the class model are read-only.
            var headerDictionary = new HeaderDictionary();
            headerDictionary.Add("ContinuationToken", "TestContinuationToken");
            headerDictionary.Add("BatchSize", "64");
            var request = new Mock<HttpRequest>();
            request.SetupGet(r => r.Headers).Returns(headerDictionary);

            var httpContext = new Mock<HttpContext>();
            httpContext.SetupGet(a => a.Request).Returns(request.Object);

            _controller.ControllerContext = new ControllerContext(new ActionContext(httpContext.Object, new RouteData(), new AspNetCore.Mvc.Controllers.ControllerActionDescriptor()));

            // Mock the store
            IEnumerable<AmlModuleEntity> entity = new List<AmlModuleEntity> { new AmlModuleEntity { Id = "TestId" } };
            Mock<IAmlModuleStore> store = new Mock<IAmlModuleStore>();
            store.Setup(s => s.ListEntityAsync(It.IsAny<string>(), It.IsAny<bool>(), It.IsAny<string>(), It.IsAny<int>(), It.IsAny<bool>(), It.IsAny<string>(), It.IsAny<bool>(), It.IsAny<string>(), It.IsAny<bool>()))
                .Returns(
                    Task.FromResult(
                            new Tuple<IEnumerable<AmlModuleEntity>, string>(entity, "TestContinuationToken")));
            _stores.Setup(s => s.GetAmlModuleStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(store.Object);

            MethodInfo method_ListAzureMLModulesAsync = typeof(AmlModulesBaseController).GetMethod("ListAzureMLModulesAsync", BindingFlags.Instance | BindingFlags.NonPublic);

            var task = (Task<EntityWithContinuationToken<IEnumerable<AzureMLModule>>>)method_ListAzureMLModulesAsync.Invoke(_controller, new object[] { _dummyWorkspaceIdentity, true, true, string.Empty, true, string.Empty, true });
            var result = await task.ConfigureAwait(false);
            Assert.AreEqual("TestId", result.Entity.ToList()[0].Id);
            Assert.AreEqual("TestContinuationToken", result.ContinuationToken);
        }

        [Test]
        public async Task TestGetAmlModuleByNameHelperAsync_NormalCase_ExpectedAzureMLModule()
        {
            Mock<IAmlModuleStore> store = new Mock<IAmlModuleStore>();
            store.Setup(s => s.GetAmlModuleByNameAsync(It.IsAny<string>(), It.IsAny<string>()))
                .Returns(
                    Task.FromResult(
                            new AmlModuleEntity { Id = "TestId", Name = "TestName" }));
            _stores.Setup(s => s.GetAmlModuleStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(store.Object);

            MethodInfo method_GetAmlModuleByNameHelperAsync = typeof(AmlModulesBaseController).GetMethod("GetAmlModuleByNameHelperAsync", BindingFlags.Instance | BindingFlags.NonPublic);

            var task = (Task<AzureMLModule>)method_GetAmlModuleByNameHelperAsync.Invoke(_controller, new object[] { _dummyWorkspaceIdentity, "dummyName" });
            var result = await task.ConfigureAwait(false);
            Assert.AreEqual("TestId", result.Id);
            Assert.AreEqual("TestName", result.Name);
        }

        [Test]
        public void TestGetAmlModuleByNameHelperAsync_StoresThrowArgumentException_ExpectedBaseException()
        {
            _stores.Setup(s => s.GetAmlModuleStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Throws(
                    new ArgumentException());

            MethodInfo method_GetAmlModuleByNameHelperAsync = typeof(AmlModulesBaseController).GetMethod("GetAmlModuleByNameHelperAsync", BindingFlags.Instance | BindingFlags.NonPublic);

            var task = (Task<AzureMLModule>)method_GetAmlModuleByNameHelperAsync.Invoke(_controller, new object[] { _dummyWorkspaceIdentity, "dummyName" });
            Assert.ThrowsAsync<BaseException>(async () => await task.ConfigureAwait(false));
        }

        [Test]
        public void TestExtractTopVersion()
        {
            var versions = new List<AzureMLModuleVersionDescriptor>
            {
                new AzureMLModuleVersionDescriptor{ModuleVersionId = "v1", Version = "2.3.1"  },
                new AzureMLModuleVersionDescriptor{ModuleVersionId = "v2", Version = "*******"  },
                new AzureMLModuleVersionDescriptor{ModuleVersionId = "v3", Version = "2.3"  },
                new AzureMLModuleVersionDescriptor{ModuleVersionId = "v4", Version = "2.3.2"  },
                new AzureMLModuleVersionDescriptor{ModuleVersionId = "v5", Version = "*******.6"  },
            };

            MethodInfo method_ExtractTopVersion = typeof(AmlModulesBaseController).GetMethod("ExtractTopVersion", BindingFlags.Static | BindingFlags.NonPublic);

            var result = (string)method_ExtractTopVersion.Invoke(_controller, new object[] { versions });
            Assert.AreEqual("v5", result);
        }

        [Test]
        public void TestGetTopId_ContainsNullVersion_ExpectedThrowsException()
        {
            var versions = new List<Tuple<string, string>>
            {
                null,
                new Tuple<string, string>("v1", "2.3.2"),
                new Tuple<string, string>("v2", "*******"),
                null
            };

            MethodInfo method_GetTopId = typeof(AmlModulesBaseController).GetMethod("GetTopId", BindingFlags.Static | BindingFlags.NonPublic);

            Assert.Throws<TargetInvocationException>(() => method_GetTopId.Invoke(_controller, new object[] { versions }));
        }

        [Test]
        public void TestExtractLatest()
        {
            var versions = new List<AzureMLModuleVersion>
            {
                new AzureMLModuleVersion{ Data = new AEVA.DataContracts.ModuleEntity {Id = "version1", LastModifiedDate = DateTime.UtcNow + TimeSpan.FromSeconds(5) } },
                new AzureMLModuleVersion{ Data = new AEVA.DataContracts.ModuleEntity {Id = "version2", LastModifiedDate = DateTime.UtcNow - TimeSpan.FromSeconds(5) } },
            };
            MethodInfo method_ExtractLatest = typeof(AmlModulesBaseController).GetMethod("ExtractLatest", BindingFlags.Static | BindingFlags.NonPublic);

            var result = (AzureMLModuleVersion)method_ExtractLatest.Invoke(_controller, new object[] { versions });
            Assert.AreEqual("version1", result.Data.Id);
        }

        [Test]
        public void TestIsUsingSemanticVersioning_UseSemanticVersioning_ExpectedTrue()
        {
            var versions = new List<AzureMLModuleVersionDescriptor>
            {
                new AzureMLModuleVersionDescriptor { Version = "*******" },
            };
            MethodInfo method_IsUsingSemanticVersioning = typeof(AmlModulesBaseController).GetMethod("IsUsingSemanticVersioning", BindingFlags.Static | BindingFlags.NonPublic);

            var result = (bool)method_IsUsingSemanticVersioning.Invoke(_controller, new object[] { versions });
            Assert.IsTrue(result);
        }

        [Test]
        public void TestIsUsingSemanticVersioning_NotUseSemanticVersioning_ExpectedFalse()
        {
            var versions = new List<AzureMLModuleVersionDescriptor>
            {
                new AzureMLModuleVersionDescriptor { Version = ".1.2.c..3.4." },
            };
            MethodInfo method_IsUsingSemanticVersioning = typeof(AmlModulesBaseController).GetMethod("IsUsingSemanticVersioning", BindingFlags.Static | BindingFlags.NonPublic);

            var result = (bool)method_IsUsingSemanticVersioning.Invoke(_controller, new object[] { versions });
            Assert.IsFalse(result);
        }

        [Test]
        public void TestIsUsingSemanticVersioning_EmptyOrNullVersions_ExpectedFalse()
        {
            var versions = new List<AzureMLModuleVersionDescriptor>();
            MethodInfo method_IsUsingSemanticVersioning = typeof(AmlModulesBaseController).GetMethod("IsUsingSemanticVersioning", BindingFlags.Static | BindingFlags.NonPublic);

            var result = (bool)method_IsUsingSemanticVersioning.Invoke(_controller, new object[] { versions });
            Assert.IsFalse(result);

            var result2 = (bool)method_IsUsingSemanticVersioning.Invoke(_controller, new object[] { null });
            Assert.IsFalse(result2);
        }

        [Test]
        public void TestGetResolvedVersion_OneViableVersion_ExpectedCorrectVersion()
        {
            var versions = new HashSet<AzureMLModuleVersion> { new AzureMLModuleVersion { Data = new AEVA.DataContracts.ModuleEntity { Id = "TestId" } } };
            var module = new AzureMLModule { Versions = new List<AzureMLModuleVersionDescriptor> { new AzureMLModuleVersionDescriptor { ModuleVersionId = "TestId", Version = "*******" } } };

            MethodInfo method_GetResolvedVersion = typeof(AmlModulesBaseController).GetMethod("GetResolvedVersion", BindingFlags.Instance | BindingFlags.NonPublic);
            var result = (AzureMLModuleVersion)method_GetResolvedVersion.Invoke(_controller, new object[] { module, versions });

            Assert.AreEqual("TestId", result.Data.Id);
        }

        [Test]
        public void TestGetResolvedVersion_HasDefaultVersion_ExpectedDefaultVersion()
        {
            var versions = new HashSet<AzureMLModuleVersion> { new AzureMLModuleVersion { Data = new AEVA.DataContracts.ModuleEntity { Id = "TestId" } },
                                                               new AzureMLModuleVersion { Data = new AEVA.DataContracts.ModuleEntity { Id = "TestId2" } }};
            var module = new AzureMLModule
            {
                Versions = new List<AzureMLModuleVersionDescriptor> {
                new AzureMLModuleVersionDescriptor { ModuleVersionId = "TestId", Version = "*******" },
                new AzureMLModuleVersionDescriptor { ModuleVersionId = "TestId2", Version = "*******"} },
                DefaultVersion = "*******"
            };

            MethodInfo method_GetResolvedVersion = typeof(AmlModulesBaseController).GetMethod("GetResolvedVersion", BindingFlags.Instance | BindingFlags.NonPublic);
            var result = (AzureMLModuleVersion)method_GetResolvedVersion.Invoke(_controller, new object[] { module, versions });

            Assert.AreEqual("TestId2", result.Data.Id);
        }

        [Test]
        public void TestGetResolvedVersion_NormalSemanticVersioning_ExpectedCorrectVersion()
        {
            var versions = new HashSet<AzureMLModuleVersion> { new AzureMLModuleVersion { Data = new AEVA.DataContracts.ModuleEntity { Id = "TestId" } },
                                                               new AzureMLModuleVersion { Data = new AEVA.DataContracts.ModuleEntity { Id = "TestId2" } }};
            var module = new AzureMLModule
            {
                Versions = new List<AzureMLModuleVersionDescriptor> {
                new AzureMLModuleVersionDescriptor { ModuleVersionId = "TestId", Version = "*******" },
                new AzureMLModuleVersionDescriptor { ModuleVersionId = "TestId2", Version = "*******"} },
            };

            MethodInfo method_GetResolvedVersion = typeof(AmlModulesBaseController).GetMethod("GetResolvedVersion", BindingFlags.Instance | BindingFlags.NonPublic);
            var result = (AzureMLModuleVersion)method_GetResolvedVersion.Invoke(_controller, new object[] { module, versions });

            Assert.AreEqual("TestId", result.Data.Id);
        }

        [Test]
        public void TestGetResolvedVersion_NormalTimeStampVersioning_ExpectedCorrectVersion()
        {
            var versions = new HashSet<AzureMLModuleVersion> { new AzureMLModuleVersion { Data = new AEVA.DataContracts.ModuleEntity { Id = "TestId", LastModifiedDate = DateTime.UtcNow - TimeSpan.FromSeconds(1) } },
                                                               new AzureMLModuleVersion { Data = new AEVA.DataContracts.ModuleEntity { Id = "TestId2", LastModifiedDate = DateTime.UtcNow + TimeSpan.FromSeconds(1) } }};
            var module = new AzureMLModule
            {
                Versions = new List<AzureMLModuleVersionDescriptor> {
                new AzureMLModuleVersionDescriptor { ModuleVersionId = "TestId", Version = "2021/01/15/12:00:00" },
                new AzureMLModuleVersionDescriptor { ModuleVersionId = "TestId2", Version = "2021/01/15/12:00:02"},
                new AzureMLModuleVersionDescriptor { ModuleVersionId = "TestId3", Version = "*******" } },
                DefaultVersion = "*******",
            };

            MethodInfo method_GetResolvedVersion = typeof(AmlModulesBaseController).GetMethod("GetResolvedVersion", BindingFlags.Instance | BindingFlags.NonPublic);
            var result = (AzureMLModuleVersion)method_GetResolvedVersion.Invoke(_controller, new object[] { module, versions });

            Assert.AreEqual("TestId2", result.Data.Id);
        }

        [Test]
        public void TestGetIdFromProperties_ContainsAzureMLId_ExpectedCorrectId()
        {
            var exampleProperties = new Dictionary<string, string>
            {
                {@"azureml/id", Guid.NewGuid().ToString() },
            };

            MethodInfo method_GetIdFromProperties = typeof(AmlModulesBaseController).GetMethod("GetIdFromProperties", BindingFlags.Static | BindingFlags.Public);
            var result = (string)method_GetIdFromProperties.Invoke(_controller, new object[] { exampleProperties });

            Assert.AreEqual(exampleProperties[@"azureml/id"], result);
        }

        [Test]
        public void TestGetIdFromProperties_NullProperties_ExpectedBaseException()
        {
            Assert.Throws<BaseException>(() => AmlModulesBaseController.GetIdFromProperties(null));
        }

        [Test]
        public void TestGetIdFromProperties_EmptyProperties_ExpectedBaseException()
        {
            Assert.Throws<BaseException>(() => AmlModulesBaseController.GetIdFromProperties(new Dictionary<string, string>()));
        }

        [Test]
        public void TestGetIdFromProperties_ParseIdFailed_ExpectedBaseException()
        {
            var exampleProperties = new Dictionary<string, string>
            {
                {@"azureml/id", "IdCannotBeParsed" },
            };

            Assert.Throws<BaseException>(() => AmlModulesBaseController.GetIdFromProperties(exampleProperties));
        }

        [Test]
        public void TestUpdateAzureMLModuleS2SAsync_IdNotMatch_ExpectedBaseException()
        {
            MethodInfo method_UpdateAzureMLModuleS2SAsync = typeof(AmlModulesBaseController).GetMethod("UpdateAzureMLModuleS2SAsync", BindingFlags.Instance | BindingFlags.NonPublic);
            var task = (Task<AzureMLModule>)method_UpdateAzureMLModuleS2SAsync.Invoke(_controller, new object[] { _dummyWorkspaceIdentity, "TestId", new AzureMLModule { Id = "TestId2" } });

            Assert.ThrowsAsync<BaseException>(async () => await task.ConfigureAwait(false));
        }

        [Test]
        public void TestUpdateAzureMLModuleS2SAsync_UpdatingModuleWithNonBuiltInPrefixName_ExpectedBaseException()
        {
            // Set-up Mocks
            Mock<IAmlModuleStore> store = new Mock<IAmlModuleStore>();
            store.Setup(s => s.GetEntityAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(new AmlModuleEntity() { Name = @"azureml://TestModuleName" }));
            store.Setup(s => s.UpdateAsync(It.IsAny<string>(), It.IsAny<AmlModuleEntity>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(
                    (string s, AmlModuleEntity entity, WorkspaceIdentity identity) => Task.FromResult(entity));

            _stores.Setup(s => s.GetAmlModuleStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(store.Object);

            // Test by Reflection
            var updated = new AzureMLModule
            {
                Id = "TestId",
                Name = "TestModuleName",
            };

            MethodInfo method_UpdateAzureMLModuleS2SAsync = typeof(AmlModulesBaseController).GetMethod("UpdateAzureMLModuleS2SAsync", BindingFlags.Instance | BindingFlags.NonPublic);
            var task = (Task<AzureMLModule>)method_UpdateAzureMLModuleS2SAsync.Invoke(_controller, new object[] { _dummyWorkspaceIdentity, "TestId", updated });

            Assert.ThrowsAsync<BaseException>(async () => await task.ConfigureAwait(false));
        }

        [Test]
        public async Task TestUpdateAzureMLModuleS2SAsync_NormalCase_ExpectedCorrectAzureMLModule()
        {
            // Set-up Mocks
            Mock<IAmlModuleStore> store = new Mock<IAmlModuleStore>();
            store.Setup(s => s.GetEntityAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(new AmlModuleEntity() { Name = @"TestModuleName", EntityStatus = DataContracts.EntityStatus.Deprecated }));
            store.Setup(s => s.UpdateAsync(It.IsAny<string>(), It.IsAny<AmlModuleEntity>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(
                    (string s, AmlModuleEntity entity, WorkspaceIdentity identity) => Task.FromResult(entity));

            _stores.Setup(s => s.GetAmlModuleStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(store.Object);

            // Test by Reflection
            var updated = new AzureMLModule
            {
                Id = "TestId",
                Name = "azureml://TestModuleName",
                EntityStatus = AEVA.DataContracts.EntityStatus.Active
            };

            MethodInfo method_UpdateAzureMLModuleS2SAsync = typeof(AmlModulesBaseController).GetMethod("UpdateAzureMLModuleS2SAsync", BindingFlags.Instance | BindingFlags.NonPublic);
            var task = (Task<AzureMLModule>)method_UpdateAzureMLModuleS2SAsync.Invoke(_controller, new object[] { _dummyWorkspaceIdentity, "TestId", updated });
            var result = await task.ConfigureAwait(false);

            Assert.AreEqual("azureml://TestModuleName", result.Name);
            Assert.AreEqual(AEVA.DataContracts.EntityStatus.Active, result.EntityStatus);
        }

        [Test]
        public void TestGetDefaultModuleVersionId_NormalCase_ExpectedDefaultModuleVersionId()
        {
            var module = new AzureMLModule
            {
                Versions = new List<AzureMLModuleVersionDescriptor> { new AzureMLModuleVersionDescriptor { ModuleVersionId = "TestId", Version = "*******" } },
                DefaultVersion = "*******"
            };

            MethodInfo method_GetDefaultModuleVersionId = typeof(AmlModulesBaseController).GetMethod("GetDefaultModuleVersionId", BindingFlags.Instance | BindingFlags.NonPublic);
            var result = (string)method_GetDefaultModuleVersionId.Invoke(_controller, new object[] { module });
            Assert.AreEqual("TestId", result);
        }

        [Test]
        public void TestGetDefaultModuleVersionId_NotContainsDefaultVersion_ExpectedNullVersionId()
        {
            var module = new AzureMLModule
            {
                Versions = new List<AzureMLModuleVersionDescriptor> { new AzureMLModuleVersionDescriptor { ModuleVersionId = "TestId", Version = "*******" } },
            };

            MethodInfo method_GetDefaultModuleVersionId = typeof(AmlModulesBaseController).GetMethod("GetDefaultModuleVersionId", BindingFlags.Instance | BindingFlags.NonPublic);
            var result = (string)method_GetDefaultModuleVersionId.Invoke(_controller, new object[] { module });
            Assert.IsNull(result);
        }

        [Test]
        public async Task TestGetAzureMLModulesAsync_NormalCase_ExpectedListOfModule()
        {
            // The HeaderDictionary is needed for adding HTTP headers to the response.
            // This needs a couple of different Mocks, because many properties in the class model are read-only.
            var headerDictionary = new HeaderDictionary();
            headerDictionary.Add("ContinuationToken", "TestContinuationToken");
            headerDictionary.Add("BatchSize", "64");
            var request = new Mock<HttpRequest>();
            request.SetupGet(r => r.Headers).Returns(headerDictionary);
            var response = new Mock<HttpResponse>();
            response.SetupGet(r => r.Headers).Returns(headerDictionary);

            var httpContext = new Mock<HttpContext>();
            httpContext.SetupGet(a => a.Request).Returns(request.Object);
            httpContext.SetupGet(a => a.Response).Returns(response.Object);

            _controller.ControllerContext = new ControllerContext(new ActionContext(httpContext.Object, new RouteData(), new AspNetCore.Mvc.Controllers.ControllerActionDescriptor()));

            // Mock the store
            IEnumerable<AmlModuleEntity> entity = new List<AmlModuleEntity> { new AmlModuleEntity { Id = "TestId" } };
            Mock<IAmlModuleStore> store = new Mock<IAmlModuleStore>();
            store.Setup(s => s.ListEntityAsync(It.IsAny<string>(), It.IsAny<bool>(), It.IsAny<string>(), It.IsAny<int>(), It.IsAny<bool>(), It.IsAny<string>(), It.IsAny<bool>(), It.IsAny<string>(), It.IsAny<bool>()))
                .Returns(
                    Task.FromResult(
                            new Tuple<IEnumerable<AmlModuleEntity>, string>(entity, "TestContinuationToken")));
            _stores.Setup(s => s.GetAmlModuleStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(store.Object);

            MethodInfo method_GetAzureMLModulesAsync = typeof(AmlModulesBaseController).GetMethod("GetAzureMLModulesAsync", BindingFlags.Instance | BindingFlags.NonPublic);

            var task = (Task<IEnumerable<AzureMLModule>>)method_GetAzureMLModulesAsync.Invoke(_controller, new object[] { _dummyWorkspaceIdentity, true });
            var result = await task.ConfigureAwait(false);
            Assert.AreEqual("TestId", result.ToList()[0].Id);
        }

        [Test]
        public async Task TestListAzureMLModuleVersionsAsync_WithOrderBy_ExpectedListOfVersions()
        {
            // The HeaderDictionary is needed for adding HTTP headers to the response.
            // This needs a couple of different Mocks, because many properties in the class model are read-only.
            var headerDictionary = new HeaderDictionary();
            headerDictionary.Add("ContinuationToken", "TestContinuationToken");
            headerDictionary.Add("BatchSize", "64");
            var request = new Mock<HttpRequest>();
            request.SetupGet(r => r.Headers).Returns(headerDictionary);
            var response = new Mock<HttpResponse>();
            response.SetupGet(r => r.Headers).Returns(headerDictionary);

            var httpContext = new Mock<HttpContext>();
            httpContext.SetupGet(a => a.Request).Returns(request.Object);
            httpContext.SetupGet(a => a.Response).Returns(response.Object);

            _controller.ControllerContext = new ControllerContext(new ActionContext(httpContext.Object, new RouteData(), new AspNetCore.Mvc.Controllers.ControllerActionDescriptor()));

            // Mock the store
            IEnumerable<AmlModuleEntity> entity = new List<AmlModuleEntity> {
                new AmlModuleEntity {
                    Id = "TestFamilyId",
                    DefaultVersion = "*******",
                    Versions = new List<AmlModuleVersionDescriptor> { new AmlModuleVersionDescriptor { ModuleVersionId = "TestVersionId", Version = "*******" } },
                }
            };
            Mock<IAmlModuleStore> store = new Mock<IAmlModuleStore>();
            store.Setup(s => s.ListEntityAsync(It.IsAny<string>(), It.IsAny<bool>(), It.IsAny<string>(), It.IsAny<int>(), It.IsAny<bool>(), It.IsAny<string>(), It.IsAny<bool>(), It.IsAny<string>(), It.IsAny<bool>()))
                .Returns(
                    Task.FromResult(
                            new Tuple<IEnumerable<AmlModuleEntity>, string>(entity, "TestContinuationToken")));

            Mock<IModuleStore> moduleStore = new Mock<IModuleStore>();
            IEnumerable<DataContracts.ModuleEntity> modules = new List<DataContracts.ModuleEntity>() { new DataContracts.ModuleEntity { Id = "TestVersionId", FamilyId = "TestFamilyId" } };
            moduleStore.Setup(m => m.BulkGetIdsAsync(It.IsAny<string>(), It.IsAny<string[]>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(modules));

            Mock<IDataTypeStore> typeStore = new Mock<IDataTypeStore>();
            IEnumerable<DataContracts.DataTypeEntity> entities = new List<DataContracts.DataTypeEntity> { new DataContracts.DataTypeEntity { Id = "TestTypeId" } };
            typeStore.Setup(t => t.ListAsync(It.IsAny<string>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(entities));

            _stores.Setup(s => s.GetAmlModuleStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(store.Object);
            _stores.Setup(s => s.GetModuleStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(moduleStore.Object);
            _stores.Setup(s => s.GetDataTypeStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(typeStore.Object);

            MethodInfo method_ListAzureMLModuleVersionsAsync = typeof(AmlModulesBaseController).GetMethod("ListAzureMLModuleVersionsAsync", BindingFlags.Instance | BindingFlags.NonPublic);

            var task = (Task<IEnumerable<Tuple<AzureMLModule, IEnumerable<AzureMLModuleVersion>>>>)method_ListAzureMLModuleVersionsAsync.Invoke(_controller, new object[] { _dummyWorkspaceIdentity, true, GetAmlModuleVersionsType.Default, true, "Version DESC", string.Empty, true });
            var result = await task.ConfigureAwait(false);
            Assert.AreEqual("TestFamilyId", result.ToList()[0].Item1.Id);
            Assert.AreEqual(1, result.ToList()[0].Item2.Count());

            var taskASC = (Task<IEnumerable<Tuple<AzureMLModule, IEnumerable<AzureMLModuleVersion>>>>)method_ListAzureMLModuleVersionsAsync.Invoke(_controller, new object[] { _dummyWorkspaceIdentity, true, GetAmlModuleVersionsType.Default, true, "Version ASC", string.Empty, true });
            var resultASC = await task.ConfigureAwait(false);
            Assert.AreEqual("TestFamilyId", resultASC.ToList()[0].Item1.Id);
            Assert.AreEqual(1, resultASC.ToList()[0].Item2.Count());
        }

        [Test]
        public void TestListAzureMLModuleVersionsAsync_WithOrderByWrongFormat_ExpectedBaseException()
        {
            MethodInfo method_ListAzureMLModuleVersionsAsync = typeof(AmlModulesBaseController).GetMethod("ListAzureMLModuleVersionsAsync", BindingFlags.Instance | BindingFlags.NonPublic);

            var taskMultiParams = (Task<IEnumerable<Tuple<AzureMLModule, IEnumerable<AzureMLModuleVersion>>>>)method_ListAzureMLModuleVersionsAsync.Invoke(_controller, new object[] { _dummyWorkspaceIdentity, true, GetAmlModuleVersionsType.Default, true, "Version DESC param1 param2", string.Empty, true });
            Assert.ThrowsAsync<BaseException>(async () => await taskMultiParams.ConfigureAwait(false));

            var taskWrongFormat = (Task<IEnumerable<Tuple<AzureMLModule, IEnumerable<AzureMLModuleVersion>>>>)method_ListAzureMLModuleVersionsAsync.Invoke(_controller, new object[] { _dummyWorkspaceIdentity, true, GetAmlModuleVersionsType.Default, true, "Version WAVE", string.Empty, true });
            Assert.ThrowsAsync<BaseException>(async () => await taskWrongFormat.ConfigureAwait(false));
        }

        [Test]
        public void TestListAzureMLModuleVersionsAsync_StoresThrowsException_ExpectedBaseException()
        {
            _stores.Setup(s => s.GetAmlModuleStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Throws(new ArgumentException());

            MethodInfo method_ListAzureMLModuleVersionsAsync = typeof(AmlModulesBaseController).GetMethod("ListAzureMLModuleVersionsAsync", BindingFlags.Instance | BindingFlags.NonPublic);

            var taskMultiParams = (Task<IEnumerable<Tuple<AzureMLModule, IEnumerable<AzureMLModuleVersion>>>>)method_ListAzureMLModuleVersionsAsync.Invoke(_controller, new object[] { _dummyWorkspaceIdentity, true, GetAmlModuleVersionsType.Default, true, "Version DESC", string.Empty, true });
            Assert.ThrowsAsync<BaseException>(async () => await taskMultiParams.ConfigureAwait(false));
        }

        [Test]
        public void TestListAzureMLModuleVersionsAsync_WrongVersionType_ExpectedBaseException()
        {
            // The HeaderDictionary is needed for adding HTTP headers to the response.
            // This needs a couple of different Mocks, because many properties in the class model are read-only.
            var headerDictionary = new HeaderDictionary();
            headerDictionary.Add("ContinuationToken", "TestContinuationToken");
            headerDictionary.Add("BatchSize", "64");
            var request = new Mock<HttpRequest>();
            request.SetupGet(r => r.Headers).Returns(headerDictionary);
            var response = new Mock<HttpResponse>();
            response.SetupGet(r => r.Headers).Returns(headerDictionary);

            var httpContext = new Mock<HttpContext>();
            httpContext.SetupGet(a => a.Request).Returns(request.Object);
            httpContext.SetupGet(a => a.Response).Returns(response.Object);

            _controller.ControllerContext = new ControllerContext(new ActionContext(httpContext.Object, new RouteData(), new AspNetCore.Mvc.Controllers.ControllerActionDescriptor()));

            // Mock the store
            IEnumerable<AmlModuleEntity> entity = new List<AmlModuleEntity> {
                new AmlModuleEntity {
                    Id = "TestFamilyId",
                    DefaultVersion = "*******",
                    Versions = new List<AmlModuleVersionDescriptor> { new AmlModuleVersionDescriptor { ModuleVersionId = "TestVersionId", Version = "*******" } },
                }
            };
            Mock<IAmlModuleStore> store = new Mock<IAmlModuleStore>();
            store.Setup(s => s.ListEntityAsync(It.IsAny<string>(), It.IsAny<bool>(), It.IsAny<string>(), It.IsAny<int>(), It.IsAny<bool>(), It.IsAny<string>(), It.IsAny<bool>(), It.IsAny<string>(), It.IsAny<bool>()))
                .Returns(
                    Task.FromResult(
                            new Tuple<IEnumerable<AmlModuleEntity>, string>(entity, "TestContinuationToken")));

            Mock<IModuleStore> moduleStore = new Mock<IModuleStore>();
            IEnumerable<DataContracts.ModuleEntity> modules = new List<DataContracts.ModuleEntity>() { new DataContracts.ModuleEntity { Id = "TestVersionId", FamilyId = "TestFamilyId" } };
            moduleStore.Setup(m => m.BulkGetIdsAsync(It.IsAny<string>(), It.IsAny<string[]>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(modules));

            Mock<IDataTypeStore> typeStore = new Mock<IDataTypeStore>();
            IEnumerable<DataContracts.DataTypeEntity> entities = new List<DataContracts.DataTypeEntity> { new DataContracts.DataTypeEntity { Id = "TestTypeId" } };
            typeStore.Setup(t => t.ListAsync(It.IsAny<string>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(entities));

            _stores.Setup(s => s.GetAmlModuleStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(store.Object);
            _stores.Setup(s => s.GetModuleStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(moduleStore.Object);
            _stores.Setup(s => s.GetDataTypeStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(typeStore.Object);

            MethodInfo method_ListAzureMLModuleVersionsAsync = typeof(AmlModulesBaseController).GetMethod("ListAzureMLModuleVersionsAsync", BindingFlags.Instance | BindingFlags.NonPublic);

            var task = (Task<IEnumerable<Tuple<AzureMLModule, IEnumerable<AzureMLModuleVersion>>>>)method_ListAzureMLModuleVersionsAsync.Invoke(_controller, new object[] { _dummyWorkspaceIdentity, true, GetAmlModuleVersionsType.Resolve, true, "Version DESC", string.Empty, true });
            Assert.ThrowsAsync<BaseException>(async () => await task.ConfigureAwait(false));
        }

        [Test]
        public async Task TestFetchViableVersionsOfModulesAsync_NormalCase_ExpectedCorrectVersionsDictionary()
        {
            var azureMLmodules = new List<AzureMLModule> {
                new AzureMLModule{ Id = "TestFamilyId", Versions = new List<AzureMLModuleVersionDescriptor> { new AzureMLModuleVersionDescriptor { ModuleVersionId = "TestModuleVersionId1" } } },
                new AzureMLModule{ Id = "TestId2", Versions = new List<AzureMLModuleVersionDescriptor> { new AzureMLModuleVersionDescriptor { ModuleVersionId = "TestModuleVersionId2" } } },
                new AzureMLModule{ Id = "TestId3", Versions = new List<AzureMLModuleVersionDescriptor> { new AzureMLModuleVersionDescriptor { ModuleVersionId = "TestModuleVersionId3" } } },
            };

            Mock<IModuleStore> moduleStore = new Mock<IModuleStore>();
            IEnumerable<DataContracts.ModuleEntity> modules = new List<DataContracts.ModuleEntity>() {
                new DataContracts.ModuleEntity { Id = "TestVersionId", FamilyId = "TestFamilyId" },
                new DataContracts.ModuleEntity { Id = "TestVersionId", FamilyId = "TestFamilyId2" }
            };
            moduleStore.Setup(m => m.BulkGetIdsAsync(It.IsAny<string>(), It.IsAny<string[]>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(modules));

            Mock<IDataTypeStore> typeStore = new Mock<IDataTypeStore>();
            IEnumerable<DataContracts.DataTypeEntity> entities = new List<DataContracts.DataTypeEntity> { new DataContracts.DataTypeEntity { Id = "TestTypeId" } };
            typeStore.Setup(t => t.ListAsync(It.IsAny<string>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(entities));

            MethodInfo method_FetchViableVersionsOfModulesAsync = typeof(AmlModulesBaseController).GetMethod("FetchViableVersionsOfModulesAsync", BindingFlags.Instance | BindingFlags.NonPublic);

            var task = (Task<Dictionary<AzureMLModule, HashSet<AzureMLModuleVersion>>>)method_FetchViableVersionsOfModulesAsync.Invoke(_controller, new object[] { azureMLmodules, moduleStore.Object, typeStore.Object });
            var result = await task.ConfigureAwait(false);
            Assert.IsTrue(result.ContainsKey(azureMLmodules[0]));
            Assert.IsFalse(result.ContainsKey(azureMLmodules[1]));
            Assert.IsFalse(result.ContainsKey(azureMLmodules[2]));
            Assert.IsTrue(result[azureMLmodules[0]].ToList()[0].ModuleId == "TestFamilyId");
        }

        [Test]
        public async Task TestResolveModuleVersionAsync_NromalCase_ExpectedCorrectAzureMLModuleVersion()
        {
            // Mock stores to produce an empty Viable moduleToVersionsDictionary
            var azureMLModule = new AzureMLModule { Id = "TestFamilyId", Versions = new List<AzureMLModuleVersionDescriptor> { new AzureMLModuleVersionDescriptor { ModuleVersionId = "TestModuleVersionId1" } } };

            Mock<IModuleStore> moduleStore = new Mock<IModuleStore>();
            IEnumerable<DataContracts.ModuleEntity> modules = new List<DataContracts.ModuleEntity>() {
                new DataContracts.ModuleEntity { Id = "TestVersionId", FamilyId = "TestFamilyId", Version = "*******" },
            };
            moduleStore.Setup(m => m.BulkGetIdsAsync(It.IsAny<string>(), It.IsAny<string[]>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(modules));

            Mock<IDataTypeStore> typeStore = new Mock<IDataTypeStore>();
            IEnumerable<DataContracts.DataTypeEntity> entities = new List<DataContracts.DataTypeEntity> { new DataContracts.DataTypeEntity { Id = "TestTypeId" } };
            typeStore.Setup(t => t.ListAsync(It.IsAny<string>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(entities));

            MethodInfo method_ResolveModuleVersionAsync = typeof(AmlModulesBaseController).GetMethod("ResolveModuleVersionAsync", BindingFlags.Instance | BindingFlags.NonPublic);

            var task = (Task<AzureMLModuleVersion>)method_ResolveModuleVersionAsync.Invoke(_controller, new object[] { azureMLModule, moduleStore.Object, typeStore.Object });
            var result = await task.ConfigureAwait(false);
            Assert.AreEqual("TestVersionId", result.Data.Id);
        }

        [Test]
        public async Task TestResolveModuleVersionAsync_ModuleIsNotViable_ExpectedNullAzureMLModuleVersion()
        {
            // Mock stores to produce an empty Viable moduleToVersionsDictionary
            var azureMLModule = new AzureMLModule { Id = "TestId", Versions = new List<AzureMLModuleVersionDescriptor> { new AzureMLModuleVersionDescriptor { ModuleVersionId = "TestModuleVersionId1" } } };

            Mock<IModuleStore> moduleStore = new Mock<IModuleStore>();
            IEnumerable<DataContracts.ModuleEntity> modules = new List<DataContracts.ModuleEntity>() {
                new DataContracts.ModuleEntity { Id = "TestVersionId", FamilyId = "TestFamilyId" },
            };
            moduleStore.Setup(m => m.BulkGetIdsAsync(It.IsAny<string>(), It.IsAny<string[]>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(modules));

            Mock<IDataTypeStore> typeStore = new Mock<IDataTypeStore>();
            IEnumerable<DataContracts.DataTypeEntity> entities = new List<DataContracts.DataTypeEntity> { new DataContracts.DataTypeEntity { Id = "TestTypeId" } };
            typeStore.Setup(t => t.ListAsync(It.IsAny<string>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(entities));

            MethodInfo method_ResolveModuleVersionAsync = typeof(AmlModulesBaseController).GetMethod("ResolveModuleVersionAsync", BindingFlags.Instance | BindingFlags.NonPublic);

            var task = (Task<AzureMLModuleVersion>)method_ResolveModuleVersionAsync.Invoke(_controller, new object[] { azureMLModule, moduleStore.Object, typeStore.Object });
            var result = await task.ConfigureAwait(false);
            Assert.IsNull(result);
        }

        [Test]
        public async Task TestGetAzureMLModuleByNameAsync_NormalCase_ExpectedCorrectAzureMLModule()
        {
            Mock<IAmlModuleStore> store = new Mock<IAmlModuleStore>();
            store.Setup(s => s.GetAmlModuleByNameAsync(It.IsAny<string>(), It.IsAny<string>()))
                .Returns(
                    Task.FromResult(
                            new AmlModuleEntity { Id = "TestModuleId" }));

            _stores.Setup(s => s.GetAmlModuleStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(store.Object);

            MethodInfo method_GetAzureMLModuleByNameAsync = typeof(AmlModulesBaseController).GetMethod("GetAzureMLModuleByNameAsync", BindingFlags.Instance | BindingFlags.NonPublic);

            var task = (Task<AzureMLModule>)method_GetAzureMLModuleByNameAsync.Invoke(_controller, new object[] { _dummyWorkspaceIdentity, "dummyName" });
            var result = await task.ConfigureAwait(false);
            Assert.AreEqual("TestModuleId", result.Id);
        }

        [Test]
        public void TestGetAzureMLModuleByNameAsync_StoresThrowException_ExpectedBaseException()
        {
            _stores.Setup(s => s.GetAmlModuleStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Throws(new ArgumentException());

            MethodInfo method_GetAzureMLModuleByNameAsync = typeof(AmlModulesBaseController).GetMethod("GetAzureMLModuleByNameAsync", BindingFlags.Instance | BindingFlags.NonPublic);

            var task = (Task<AzureMLModule>)method_GetAzureMLModuleByNameAsync.Invoke(_controller, new object[] { _dummyWorkspaceIdentity, "dummyName" });
            Assert.ThrowsAsync<BaseException>(async () => await task.ConfigureAwait(false));
        }

        [Test]
        public async Task TestListResolveAzureMLModuleVersionAsync_NormalCase_ExpectedListOfVersions()
        {
            // The HeaderDictionary is needed for adding HTTP headers to the response.
            // This needs a couple of different Mocks, because many properties in the class model are read-only.
            var headerDictionary = new HeaderDictionary();
            headerDictionary.Add("ContinuationToken", "TestContinuationToken");
            headerDictionary.Add("BatchSize", "64");
            var request = new Mock<HttpRequest>();
            request.SetupGet(r => r.Headers).Returns(headerDictionary);
            var response = new Mock<HttpResponse>();
            response.SetupGet(r => r.Headers).Returns(headerDictionary);

            var httpContext = new Mock<HttpContext>();
            httpContext.SetupGet(a => a.Request).Returns(request.Object);
            httpContext.SetupGet(a => a.Response).Returns(response.Object);

            _controller.ControllerContext = new ControllerContext(new ActionContext(httpContext.Object, new RouteData(), new AspNetCore.Mvc.Controllers.ControllerActionDescriptor()));

            // Mock the store
            IEnumerable<AmlModuleEntity> entity = new List<AmlModuleEntity> { new AmlModuleEntity { Id = "TestId" } };
            Mock<IAmlModuleStore> store = new Mock<IAmlModuleStore>();
            store.Setup(s => s.ListEntityAsync(It.IsAny<string>(), It.IsAny<bool>(), It.IsAny<string>(), It.IsAny<int>(), It.IsAny<bool>(), It.IsAny<string>(), It.IsAny<bool>(), It.IsAny<string>(), It.IsAny<bool>()))
                .Returns(
                    Task.FromResult(
                            new Tuple<IEnumerable<AmlModuleEntity>, string>(entity, "TestContinuationToken")));
            IEnumerable<AmlModuleEntity> entities = new List<AmlModuleEntity> {
                new AmlModuleEntity {Versions = new List<AmlModuleVersionDescriptor> { new AmlModuleVersionDescriptor { ModuleVersionId = "TestVersionId", Version = "*******" } }, Id = "TestFamilyId" }
            };
            store.Setup(s => s.BulkGetAsync(It.IsAny<string>(), It.IsAny<string[]>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(entities));

            Mock<IModuleStore> moduleStore = new Mock<IModuleStore>();
            IEnumerable<DataContracts.ModuleEntity> modules = new List<DataContracts.ModuleEntity>() { new DataContracts.ModuleEntity { Id = "TestVersionId", FamilyId = "TestFamilyId" } };
            moduleStore.Setup(m => m.BulkGetIdsAsync(It.IsAny<string>(), It.IsAny<string[]>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(modules));

            Mock<IDataTypeStore> typeStore = new Mock<IDataTypeStore>();
            IEnumerable<DataContracts.DataTypeEntity> types = new List<DataContracts.DataTypeEntity> { new DataContracts.DataTypeEntity { Id = "TestTypeId" } };
            typeStore.Setup(t => t.ListAsync(It.IsAny<string>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(Task.FromResult(types));

            _stores.Setup(s => s.GetAmlModuleStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(store.Object);
            _stores.Setup(s => s.GetModuleStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(moduleStore.Object);
            _stores.Setup(s => s.GetDataTypeStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>()))
                .Returns(typeStore.Object);

            MethodInfo method_ListResolveAzureMLModuleVersionAsync = typeof(AmlModulesBaseController).GetMethod("ListResolveAzureMLModuleVersionAsync", BindingFlags.Instance | BindingFlags.NonPublic);

            var task = (Task<IEnumerable<AzureMLModuleVersion>>)method_ListResolveAzureMLModuleVersionAsync.Invoke(_controller, new object[] { _dummyWorkspaceIdentity, true });
            var result = await task.ConfigureAwait(false);
            Assert.AreEqual("TestVersionId", result.ToList()[0].Data.Id);
        }
    }
}
