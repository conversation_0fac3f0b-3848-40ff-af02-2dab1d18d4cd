﻿// <copyright file="PipelineEndpointControllerExceptionTest.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Security.Claims;
using System.Threading.Tasks;
using Microsoft.Aether.AE3pService.Configuration;
using Microsoft.Aether.AEVA.DataContracts;
using Microsoft.Aether.BackendCommon;
using Microsoft.Aether.BackendCommon.AzureMonitor;
using Microsoft.Aether.DataContracts;
using Microsoft.Aether.DataContracts.Entity;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using Microsoft.MachineLearning.Common.Core.Contracts.Errors;
using Microsoft.MachineLearning.Common.Core.Exceptions;
using Microsoft.MachineLearning.Common.WebApi.TeamAccounts;
using Microsoft.RelInfra.Common.Exceptions;
using Microsoft.RelInfra.Extensions;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;

namespace Microsoft.Aether.AE3pService.Tests
{
    [TestClass]
    public class PipelineEndpointControllerExceptionTest
    {
        public Mock<IContextProvider> _contextProvider;
        public Mock<IStoreCollection> _storeCollection;
        public Mock<IWorkspaceResourcesCache> _workspaceResourceCache;
        public PipelineEndpointController _controller;
        public PipelineEndpointCreationInfo _amlPipelineEndpointCreationInfo;
        private Mock<IAzureMonitorStatusLogger> _azureMonitorStatusEmitter;
        public ServiceInvocationException _serviceInvocationException;

        [TestInitialize]
        public void Init()
        {
            _contextProvider = new Mock<IContextProvider>();
            _storeCollection = new Mock<IStoreCollection>();
            _workspaceResourceCache = new Mock<IWorkspaceResourcesCache>();
            _azureMonitorStatusEmitter = new Mock<IAzureMonitorStatusLogger>();
            var httpContext = new DefaultHttpContext();
            httpContext.Request.Headers["ContinuationToken"] = "fake_token_here";
            var workspaceMetadata = new WorkspaceMetadata(Guid.Empty, null, null, Guid.Empty);
            httpContext.Items = new Dictionary<object, object>() { { typeof(WorkspaceMetadata), workspaceMetadata } };
            var controllerContext = new ControllerContext()
            {
                HttpContext = httpContext,
            };

            MockRepository _repository = new MockRepository(MockBehavior.Loose) { DefaultValue = DefaultValue.Mock, CallBase = true };

            var configuration = new ConfigurationBuilder().AddInMemoryCollection(GetConfiguration()).Build();

            var amlpipelineConfig = Mock.Of<IOptionsMonitor<AmlPipelineConfig>>(x => x.CurrentValue == configuration.GetSection("AmlPipelineConfig").Get<AmlPipelineConfig>());

            _controller = new PipelineEndpointController(amlpipelineConfig, _storeCollection.Object, _contextProvider.Object, _workspaceResourceCache.Object, _azureMonitorStatusEmitter.Object)
            {
                ControllerContext = controllerContext,
            };

            _amlPipelineEndpointCreationInfo = new PipelineEndpointCreationInfo
            {
                Name = "name",
                Description = "description",
                PipelineId = "PipelineId"
            };

            _serviceInvocationException = new ServiceInvocationException(
                    operationName: "MockException",
                    calledService: "Mock",
                    httpMethod: HttpMethod.Post,
                    innerException: new Exception("Mock throw ServiceInvocationException."));
        }

        [TestMethod]
        public async Task TestCreatePipelineEndpointWithInvalidPipelineId()
        {
            try
            {
                Mock<IPipelineStore> pipelineStoreMock = new Mock<IPipelineStore>();
                _storeCollection.Setup(s => s.GetPipelineStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>())).Returns(pipelineStoreMock.Object);

                var pipeline = new DataContracts.PipelineEntity();
                pipeline.EntityStatus = DataContracts.EntityStatus.Disabled;

                pipelineStoreMock.Setup(p => p.GetEntityAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<WorkspaceIdentity>())).Returns(Task.FromResult(pipeline));

                var pipelineEndpointEntity = await _controller.CreatePipelineEndpointAsync(subscriptionId: "subscriptionId",
                    resourceGroupName: "resourceGroupName",
                    workspaceName: "workspaceName",
                    _amlPipelineEndpointCreationInfo);
            }
            catch (Exception e)
            {
                Assert.IsTrue(e is BaseException);
                Assert.AreEqual("EntityStatus is invalid. Cannot create pipelineEndpoint with Inactive pipelineId \"PipelineId\".", e.Message);
            }
        }

        [TestMethod]
        public async Task TestCreatePipelineEndpointWithCreatePipelineWithEmptyName()
        {
            try
            {
                PipelineEndpointCreationInfo amlPipelineEndpointCreationInfo = new PipelineEndpointCreationInfo
                {
                    Name = string.Empty,
                    Description = "description",
                    PipelineId = "pipelineid123"
                };

                Mock<IPipelineStore> pipelineStoreMock = new Mock<IPipelineStore>();
                _storeCollection.Setup(s => s.GetPipelineStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>())).Returns(pipelineStoreMock.Object);

                var pipeline = new DataContracts.PipelineEntity();

                pipelineStoreMock.Setup(p => p.GetEntityAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<WorkspaceIdentity>())).Returns(Task.FromResult(pipeline));
                var pipelineEndpointEntity = await _controller.CreatePipelineEndpointAsync(subscriptionId: "subscriptionId",
                    resourceGroupName: "resourceGroupName",
                    workspaceName: "workspaceName",
                    amlPipelineEndpointCreationInfo);
            }
            catch (Exception e)
            {
                Assert.IsTrue(e is BaseException);
                Assert.AreEqual("Name is invalid. Cannot create pipelineEndpoint with empty name.", e.Message);
            }
        }

        [TestMethod]
        public async Task TestCreatePipelineEndpointWithCreatePipelineWithBaseException()
        {
            try
            {
                _storeCollection.Setup(s => s.GetPipelineStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>())).Throws(new BaseException(new ArgumentInvalid().ToBaseError(null, "id")));

                var pipelineEndpointEntity = await _controller.CreatePipelineEndpointAsync(subscriptionId: "subscriptionId",
                    resourceGroupName: "resourceGroupName",
                    workspaceName: "workspaceNameInsufficientMemory",
                    _amlPipelineEndpointCreationInfo);
            }
            catch (Exception e)
            {
                Assert.IsTrue(e is BaseException);
                Assert.AreEqual("id is invalid", e.Message);
            }
        }

        [TestMethod]
        public async Task TestCreatePipelineEndpointWithCreatePipelineWithServiceInvocationException()
        {
            try
            {
                _storeCollection.Setup(s => s.GetPipelineStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>())).Throws(_serviceInvocationException);

                var pipelineEndpointEntity = await _controller.CreatePipelineEndpointAsync(subscriptionId: "subscriptionId",
                    resourceGroupName: "resourceGroupName",
                    workspaceName: "workspaceNameServiceInvocationException",
                    _amlPipelineEndpointCreationInfo);
            }
            catch (Exception e)
            {
                Assert.IsTrue(e is BaseException);
                Assert.AreEqual("Failure in MockException while calling service Mock; HttpMethod: POST; Response StatusCode: ; Exception type: System.Exception; InnerException: Mock throw ServiceInvocationException.", e.Message);
            }
        }

        [TestMethod]
        public async Task TestCreatePipelineEndpointWithCreatePipelineWithHttpRequestDetailException()
        {
            try
            {
                HttpRequestDetailException httpRequestDetailException = new HttpRequestDetailException("error", HttpStatusCode.Conflict);
                _storeCollection.Setup(s => s.GetPipelineStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>())).Throws(httpRequestDetailException);

                var pipelineEndpointEntity = await _controller.CreatePipelineEndpointAsync(subscriptionId: "subscriptionId",
                    resourceGroupName: "resourceGroupName",
                    workspaceName: "workspaceNameServiceInvocationException",
                    _amlPipelineEndpointCreationInfo);
            }
            catch (Exception e)
            {
                Assert.IsTrue(e is BaseException);
                Assert.AreEqual("error", e.Message);
            }
        }

        [TestMethod]
        public async Task TestGetPipelineEndpointByIdAsyncWithException()
        {
            try
            {
                _storeCollection.Setup(s => s.GetPipelineEndpointStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>())).Throws(new Exception("Error"));
                var pipelineEndpointEntity = await _controller.GetPipelineEndpointByIdAsync(subscriptionId: "subscriptionId",
                    resourceGroupName: "resourceGroupName",
                    workspaceName: "workspaceNameInsufficientMemory",
                    id: "id");
            }
            catch (Exception e)
            {
                Assert.IsTrue(e is BaseException);
                Assert.AreEqual("Unable to get pipelineEndpoint. The error message is: 'Error'.", e.Message);
            }
        }

        [TestMethod]
        public async Task TestGetPipelineEndpointByIdAsyncWithBaseException()
        {
            try
            {
                _storeCollection.Setup(s => s.GetPipelineEndpointStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>())).Throws(new BaseException(new ArgumentInvalid().ToBaseError(null, "id")));
                var pipelineEndpointEntity = await _controller.GetPipelineEndpointByIdAsync(subscriptionId: "subscriptionId",
                    resourceGroupName: "resourceGroupName",
                    workspaceName: "workspaceNameInsufficientMemory",
                    id: "id");
            }
            catch (Exception e)
            {
                Assert.IsTrue(e is BaseException);
                Assert.AreEqual("id is invalid", e.Message);
            }
        }

        [TestMethod]
        public async Task TestGetPipelineEndpointByIdAsyncWithServiceInvocationException()
        {
            try
            {
                _storeCollection.Setup(s => s.GetPipelineEndpointStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>())).Throws(_serviceInvocationException);
                var pipelineEndpointEntity = await _controller.GetPipelineEndpointByIdAsync(subscriptionId: "subscriptionId",
                    resourceGroupName: "resourceGroupName",
                    workspaceName: "workspaceNameServiceInvocationException",
                    id: "id");
            }
            catch (Exception e)
            {
                Assert.IsTrue(e is BaseException);
                Assert.AreEqual("Failure in MockException while calling service Mock; HttpMethod: POST; Response StatusCode: ; Exception type: System.Exception; InnerException: Mock throw ServiceInvocationException.", e.Message);
            }
        }

        [TestMethod]
        public async Task TestGetPipelineEndpointByNameAsyncWithException()
        {
            try
            {
                _storeCollection.Setup(s => s.GetPipelineEndpointStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>())).Throws(new Exception("Error"));
                var pipelineEndpointEntity = await _controller.GetPipelineEndpointByNameAsync(subscriptionId: "subscriptionId",
                    resourceGroupName: "resourceGroupName",
                    workspaceName: "workspaceNameInsufficientMemory",
                    name: "Exception");
            }
            catch (Exception e)
            {
                Assert.IsTrue(e is BaseException);
                Assert.AreEqual("Unable to get pipelineEndpoint by name 'Exception'. The error message is: 'Error'.", e.Message);
            }
        }

        [TestMethod]
        public async Task TestGetPipelineEndpointByNameAsyncWithBaseException()
        {
            try
            {
                _storeCollection.Setup(s => s.GetPipelineEndpointStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>())).Throws(new BaseException(new ArgumentInvalid().ToBaseError(null, "id")));
                var pipelineEndpointEntity = await _controller.GetPipelineEndpointByNameAsync(subscriptionId: "subscriptionId",
                    resourceGroupName: "resourceGroupName",
                    workspaceName: "workspaceNameInsufficientMemory",
                    name: "BaseException");
            }
            catch (Exception e)
            {
                Assert.IsTrue(e is BaseException);
                Assert.AreEqual("id is invalid", e.Message);
            }
        }

        [TestMethod]
        public async Task TestGetPipelineEndpointByNameAsyncWithServiceInvocationException()
        {
            try
            {
                _storeCollection.Setup(s => s.GetPipelineEndpointStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>())).Throws(_serviceInvocationException);
                var pipelineEndpointEntity = await _controller.GetPipelineEndpointByNameAsync(subscriptionId: "subscriptionId",
                    resourceGroupName: "resourceGroupName",
                    workspaceName: "workspaceNameServiceInvocationException",
                    name: "BaseException");
            }
            catch (Exception e)
            {
                Assert.IsTrue(e is BaseException);
                Assert.AreEqual("Failure in MockException while calling service Mock; HttpMethod: POST; Response StatusCode: ; Exception type: System.Exception; InnerException: Mock throw ServiceInvocationException.", e.Message);
            }
        }

        [TestMethod]
        public async Task TestGetNextPipelineEndpointAsyncWithException()
        {
            try
            {
                _storeCollection.Setup(s => s.GetPipelineEndpointStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>())).Throws(new Exception("Error"));
                var pipelineEndpointEntity = await _controller.GetNextPipelineEndpointAsync(subscriptionId: "subscriptionId",
                    resourceGroupName: "resourceGroupName",
                    workspaceName: "workspaceNameInsufficientMemory",
                    activeOnly: true);
            }
            catch (Exception e)
            {
                Assert.IsTrue(e is BaseException);
                Assert.AreEqual("Unable to list Pipeline endpoints. The error message is: 'Error'.", e.Message);
            }
        }

        [TestMethod]
        public async Task TestGetNextPipelineEndpointAsyncWithBaseException()
        {
            try
            {
                _storeCollection.Setup(s => s.GetPipelineEndpointStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>())).Throws(new BaseException(new ArgumentInvalid().ToBaseError(null, "id")));
                var pipelineEndpointEntity = await _controller.GetNextPipelineEndpointAsync(subscriptionId: "subscriptionId",
                    resourceGroupName: "resourceGroupName",
                    workspaceName: "workspaceNameInsufficientMemory",
                    activeOnly: true);
            }
            catch (Exception e)
            {
                Assert.IsTrue(e is BaseException);
                Assert.AreEqual("id is invalid", e.Message);
            }
        }

        [TestMethod]
        public async Task TestGetNextPipelineEndpointAsyncWithServiceInvocationException()
        {
            try
            {
                _storeCollection.Setup(s => s.GetPipelineEndpointStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>())).Throws(_serviceInvocationException);
                var pipelineEndpointEntity = await _controller.GetNextPipelineEndpointAsync(subscriptionId: "subscriptionId",
                    resourceGroupName: "resourceGroupName",
                    workspaceName: "workspaceNameServiceInvocationException",
                    activeOnly: true);
            }
            catch (Exception e)
            {
                Assert.IsTrue(e is BaseException);
                Assert.AreEqual("Failure in MockException while calling service Mock; HttpMethod: POST; Response StatusCode: ; Exception type: System.Exception; InnerException: Mock throw ServiceInvocationException.", e.Message);
            }
        }

        [TestMethod]
        public async Task TestGetNextPipelineFromPipelineEndpointAsyncWithException()
        {
            try
            {
                _storeCollection.Setup(s => s.GetPipelineStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>())).Throws(new Exception("Error"));
                var pipelineEndpointEntity = await _controller.GetNextPipelineFromPipelineEndpointAsync(subscriptionId: "subscriptionId",
                    resourceGroupName: "resourceGroupName",
                    workspaceName: "workspaceName",
                    id: "id123",
                    activeOnly: true);
            }
            catch (Exception e)
            {
                Assert.IsTrue(e is BaseException);
                Assert.AreEqual("Unable to get all pipelines from PipelineEndpoint id123. The error message is: 'Error'.", e.Message);
            }
        }

        [TestMethod]
        public async Task TestGetNextPipelineFromPipelineEndpointAsyncWithBaseException()
        {
            try
            {
                _storeCollection.Setup(s => s.GetPipelineStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>())).Throws(new BaseException(new ArgumentInvalid().ToBaseError(null, "id")));
                var pipelineEndpointEntity = await _controller.GetNextPipelineFromPipelineEndpointAsync(subscriptionId: "subscriptionId",
                    resourceGroupName: "resourceGroupName",
                    workspaceName: "workspaceName",
                    id: "id123",
                    activeOnly: true);
            }
            catch (Exception e)
            {
                Assert.IsTrue(e is BaseException);
                Assert.AreEqual("id is invalid", e.Message);
            }
        }

        [TestMethod]
        public async Task TestGetNextPipelineFromPipelineEndpointAsyncWithServiceInvocationException()
        {
            try
            {
                _storeCollection.Setup(s => s.GetPipelineStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>())).Throws(_serviceInvocationException);
                var pipelineEndpointEntity = await _controller.GetNextPipelineFromPipelineEndpointAsync(subscriptionId: "subscriptionId",
                    resourceGroupName: "resourceGroupName",
                    workspaceName: "workspaceName",
                    id: "id123",
                    activeOnly: true);
            }
            catch (Exception e)
            {
                Assert.IsTrue(e is BaseException);
                Assert.AreEqual("Failure in MockException while calling service Mock; HttpMethod: POST; Response StatusCode: ; Exception type: System.Exception; InnerException: Mock throw ServiceInvocationException.", e.Message);
            }
        }

        [TestMethod]
        public async Task TestUpdatePipelineEndpointAsyncWithNotEqualPipelineId()
        {
            try
            {
                var endpointEntity = new AEVA.DataContracts.PipelineEndpointEntity()
                {
                    Id = "updateid"
                }
                ;
                var pipelineEndpointEntity = await _controller.UpdatePipelineEndpointAsync(subscriptionId: "subscriptionId",
                    resourceGroupName: "resourceGroupName",
                    workspaceName: "workspaceNameService",
                    id: "id",
                    updated: endpointEntity);
            }
            catch (Exception e)
            {
                Assert.IsTrue(e is BaseException);
                Assert.AreEqual("id is invalid. PipelineEndpoint Id \"id\" should be equal to updated.Id \"updateid\".", e.Message);
            }
        }

        [TestMethod]
        public async Task TestUpdatePipelineEndpointAsyncWithException()
        {
            try
            {
                var endpointEntity = new AEVA.DataContracts.PipelineEndpointEntity()
                {
                    Id = "id"
                };
                _storeCollection.Setup(s => s.GetPipelineEndpointStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>())).Throws(new Exception("Error"));
                var pipelineEndpointEntity = await _controller.UpdatePipelineEndpointAsync(subscriptionId: "subscriptionId",
                    resourceGroupName: "resourceGroupName",
                    workspaceName: "workspaceNameInsufficientMemory",
                    id: "id",
                    updated: endpointEntity);
            }
            catch (Exception e)
            {
                Assert.IsTrue(e is BaseException);
                Assert.AreEqual("Unable to update PipelineEndpoint id: id. The error message is: 'Error'.", e.Message);
            }
        }

        [TestMethod]
        public async Task TestUpdatePipelineEndpointAsyncWithBaseException()
        {
            try
            {
                var endpointEntity = new AEVA.DataContracts.PipelineEndpointEntity()
                {
                    Id = "id"
                };
                _storeCollection.Setup(s => s.GetPipelineEndpointStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>())).Throws(new BaseException(new ArgumentInvalid().ToBaseError(null, "id")));
                var pipelineEndpointEntity = await _controller.UpdatePipelineEndpointAsync(subscriptionId: "subscriptionId",
                    resourceGroupName: "resourceGroupName",
                    workspaceName: "workspaceNameInsufficientMemory",
                    id: "id",
                    updated: endpointEntity);
            }
            catch (Exception e)
            {
                Assert.IsTrue(e is BaseException);
                Assert.AreEqual("id is invalid", e.Message);
            }
        }

        [TestMethod]
        public async Task TestUpdatePipelineEndpointAsyncWithServiceInvocationException()
        {
            try
            {
                var endpointEntity = new AEVA.DataContracts.PipelineEndpointEntity()
                {
                    Id = "id"
                };
                _storeCollection.Setup(s => s.GetPipelineEndpointStore(It.IsAny<ClaimsPrincipal>(), It.IsAny<WorkspaceIdentity>())).Throws(_serviceInvocationException);
                var pipelineEndpointEntity = await _controller.UpdatePipelineEndpointAsync(subscriptionId: "subscriptionId",
                    resourceGroupName: "resourceGroupName",
                    workspaceName: "workspaceNameServiceInvocationException",
                    id: "id",
                    updated: endpointEntity);
            }
            catch (Exception e)
            {
                Assert.IsTrue(e is BaseException);
                Assert.AreEqual("Failure in MockException while calling service Mock; HttpMethod: POST; Response StatusCode: ; Exception type: System.Exception; InnerException: Mock throw ServiceInvocationException.", e.Message);
            }
        }

        private static IDictionary<string, string> GetConfiguration()
        {
            IDictionary<string, string> settings = new Dictionary<string, string>()
            {
                {"AmlPipelineConfig:PipelineEndpointUrlTemplate", "https://test.api.azureml-test.ms/pipelines/v1.0/subscriptions/{0}/resourceGroups/{1}/providers/Microsoft.MachineLearningServices/workspaces/{2}/PipelineRuns/PipelineEndpointSubmit/Id/{3}"},
                {"AmlPipelineConfig:PrivateLinkPipelineEndpointUrlTemplate", "https://{0}.workspace.test.api.azureml-test.ms/pipelines/v1.0/subscriptions/{0}/resourceGroups/{1}/providers/Microsoft.MachineLearningServices/workspaces/{2}/PipelineRuns/PipelineEndpointSubmit/Id/{4}"}
            };
            return settings;
        }
    }
}
