﻿// <copyright file="AE3pExceptionLoggingPolicyTest.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Microsoft.Aether.AE3pService.Exceptions;
using Microsoft.Aether.BlueBox.RunHistoryClient;
using Microsoft.Azure.Cosmos;
using Microsoft.RelInfra.Extensions;
using Microsoft.RelInfra.Instrumentation.Exceptions;
using Microsoft.RelInfra.Instrumentation.Logging;
using Microsoft.Rest;
using Microsoft.WindowsAzure.Storage;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Net;
using System.Reflection;

namespace Microsoft.Aether.AE3pService.Tests.Exceptions
{
    [TestFixture]
    public class AE3pExceptionLoggingPolicyTest
    {
        public AE3pExceptionLoggingPolicyTest()
        {
            LoggableExtensions.Initialize(new AE3pExceptionLoggingPolicy());
        }

        [Test]
        public void ExceptionLoggingPolicy_CanLogTrue_ExpectNormalReturn()
        {
            string message = "error message";
            Exception ex = StorageException(message, "Bad Request", null);
            Dictionary<string, string> loggableProps = LoggableExtensions.LoggableProperties(ex);
            Assert.AreEqual(2, loggableProps.Count);
            Assert.AreEqual(message, loggableProps["Message"]);

            string paramName = "myParam";
            ex = new ArgumentNullException(paramName, message);
            loggableProps = LoggableExtensions.LoggableProperties(ex);
            Assert.AreEqual(2, loggableProps.Count);
            Assert.AreEqual(paramName, loggableProps["ParamName"]);
            StringAssert.Contains(message, loggableProps["Message"]);

            ex = new ArgumentException(message, paramName);
            loggableProps = LoggableExtensions.LoggableProperties(ex);
            Assert.AreEqual(2, loggableProps.Count);
            Assert.AreEqual(paramName, loggableProps["ParamName"]);
            StringAssert.Contains(message, loggableProps["Message"]);

            ex = new ArgumentException(message);
            loggableProps = LoggableExtensions.LoggableProperties(ex);
            Assert.AreEqual(1, loggableProps.Count);
            StringAssert.Contains(message, loggableProps["Message"]);

            ex = new ArgumentOutOfRangeException(paramName, message);
            loggableProps = LoggableExtensions.LoggableProperties(ex);
            Assert.AreEqual(2, loggableProps.Count);
            Assert.AreEqual(paramName, loggableProps["ParamName"]);
            StringAssert.Contains(message, loggableProps["Message"]);

            ex = new NullReferenceException(message);
            loggableProps = LoggableExtensions.LoggableProperties(ex);
            Assert.AreEqual(1, loggableProps.Count);
            StringAssert.Contains(message, loggableProps["Message"]);

            ex = new System.IO.IOException(message);
            loggableProps = LoggableExtensions.LoggableProperties(ex);
            Assert.AreEqual(1, loggableProps.Count);
            StringAssert.Contains(message, loggableProps["Message"]);

            ex = new System.Net.Http.HttpRequestException(message, new Exception("Inner error"), HttpStatusCode.NotFound);
            loggableProps = LoggableExtensions.LoggableProperties(ex);
            Assert.AreEqual(2, loggableProps.Count);
            Assert.AreEqual(HttpStatusCode.NotFound.ToString(), loggableProps["StatusCode"]);
            StringAssert.Contains(message, loggableProps["Message"]);

            ex = new HttpOperationException(message)
            {
                Response = new HttpResponseMessageWrapper(new System.Net.Http.HttpResponseMessage(System.Net.HttpStatusCode.NotFound), "responseContent")
            };
            loggableProps = LoggableExtensions.LoggableProperties(ex);
            Assert.AreEqual(2, loggableProps.Count);
            Assert.AreEqual(HttpStatusCode.NotFound.ToString(), loggableProps["StatusCode"]);
            StringAssert.Contains(message, loggableProps["Message"]);

            ex = new CosmosException(message, HttpStatusCode.NotFound, 0, Guid.NewGuid().ToString(), 0.0);
            loggableProps = LoggableExtensions.LoggableProperties(ex);
            Assert.That(loggableProps["Message"].Contains(message, StringComparison.InvariantCulture));
        }

        [Test]
        public void ExceptionLoggingPolicy_CanLogFalse_ExpectNormalReturn()
        {
            Exception ex = new RunHistoryException("fakie");
            Dictionary<string, string> loggableProps = LoggableExtensions.LoggableProperties(ex);
            Assert.AreEqual(1, loggableProps.Count);
        }

        [Test]
        public void ExceptionLoggingPolicy_CanLogFormattable_ExpectNormalReturn()
        {
            Exception ex = new HttpRequestDetailException("error calling service 'metastore': {exception_message}", HttpStatusCode.BadRequest).Format("metastore error message");
            Dictionary<string, string> loggableProps = LoggableExtensions.LoggableProperties(ex);
            Assert.AreEqual("error calling service 'metastore': metastore error message", loggableProps["Message"]);
            Assert.AreEqual(HttpStatusCode.BadRequest.ToString(), loggableProps["StatusCode"]);
        }

        [Test]
        public void ExceptionLoggingPolicy_CanLogStorageException_ExpectNormalReturn()
        {
            string message = "error1";
            Exception ex = new StorageException(message);
            Dictionary<string, string> loggableProps = LoggableExtensions.LoggableProperties(ex);
            // if the requestResult is not set then we can't determine if it's loggable so we default to false
            Assert.AreEqual(0, loggableProps.Count);

            message = "error1";
            ex = new StorageException(new RequestResult(), message, null);
            loggableProps = LoggableExtensions.LoggableProperties(ex);
            // if the requestResult.HttpStatusMessage is not set then we can't determine if it's loggable so we default to false
            Assert.AreEqual(0, loggableProps.Count);

            message = "error2";
            string httpStatusMessage = "Authorization Exception";
            ex = StorageException(message, httpStatusMessage, null);
            loggableProps = LoggableExtensions.LoggableProperties(ex);
            // if the HttpStatusMessage hits at the error being for authorization or authentication then don't log the error message, just the status message
            Assert.AreEqual(httpStatusMessage, loggableProps["HttpStatusMessage"]);

            message = "error3";
            httpStatusMessage = "Authentication Exception";
            ex = StorageException(message, httpStatusMessage, null);
            loggableProps = LoggableExtensions.LoggableProperties(ex);
            // if the HttpStatusMessage hits at the error being for authorization or authentication then don't log the error message, just the status message
            Assert.AreEqual(httpStatusMessage, loggableProps["HttpStatusMessage"]);

            message = "error4";
            httpStatusMessage = "Bad Request";
            ex = StorageException(message, httpStatusMessage, null);
            loggableProps = LoggableExtensions.LoggableProperties(ex);
            Assert.AreEqual(httpStatusMessage, loggableProps["HttpStatusMessage"]);
            Assert.AreEqual(message, loggableProps["Message"]);
        }

        public static StorageException StorageException(string message, string httpStatusMessage, Exception inner = null)
        {
            RequestResult requestResult = new RequestResult();
            typeof(RequestResult)
                .GetProperty("HttpStatusMessage")
                .SetValue(requestResult, httpStatusMessage, BindingFlags.NonPublic | BindingFlags.Instance, null, null, null);

            return new StorageException(requestResult, message, inner);
        }
    }
}
