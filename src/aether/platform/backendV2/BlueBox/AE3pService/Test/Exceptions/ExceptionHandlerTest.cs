﻿// <copyright file="ExceptionHandlerTest.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Microsoft.Aether.AE3pService.Exceptions;
using Microsoft.Aether.BlueBox.RunHistoryClient;
using Microsoft.RelInfra.Extensions;
using NUnit.Framework;
using System;
using System.Net;

namespace Microsoft.Aether.AE3pService.Tests.Exceptions
{
    [TestFixture]
    public class ExceptionHandlerTest
    {
        [Test]
        public void ExceptionHandler_GetStatusCode_Test()
        {
            var exceptionHandler = new ExceptionHandler();

            Exception ex = new RunHistoryException("fakie", new HttpRequestDetailException("fakie details", HttpStatusCode.TooManyRequests));
            HttpStatusCode sc = exceptionHandler.GetStatusCode(ex);
            Assert.AreEqual(HttpStatusCode.TooManyRequests, sc);

            ex = new RunHistoryException("fakie");
            sc = exceptionHandler.GetStatusCode(ex);
            Assert.AreEqual(HttpStatusCode.BadRequest, sc);

            ex = new Exception("fakie");
            sc = exceptionHandler.GetStatusCode(ex);
            Assert.AreEqual(ExceptionHandler.DefaultStatusCode, sc);

            ex = new System.Net.Http.HttpRequestException("An error occurred while sending the request", new System.IO.IOException("The response ended prematurely"));
            sc = exceptionHandler.GetStatusCode(ex);
            Assert.AreEqual(HttpStatusCode.ServiceUnavailable, sc);
        }
    }
}
