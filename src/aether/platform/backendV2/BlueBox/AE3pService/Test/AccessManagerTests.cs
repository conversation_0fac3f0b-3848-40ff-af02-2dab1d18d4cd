﻿// <copyright file="AccessManagerTests.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Net.Http;
using Moq;
using NUnit.Framework;
using Microsoft.Aether.BlueBox.OboTokenServiceClient;
using Microsoft.Aether.S2S.Common;
using Microsoft.AspNetCore.Http;
using System.Security.Claims;
using Microsoft.RelInfra.Common;
using Microsoft.MachineLearning.Common.WebApi.Exceptions;

namespace Microsoft.Aether.AE3pService.Tests
{
    [TestFixture]
    public class AccessManagerTests
    {
        private readonly MockRepository _repository;
        private IOboTokenServiceClient _fakeIOboTokenServiceClient;
        private IS2STokenProvider _fakeIS2STokenProvider;
        private ITokenVerifier _fakeTokenVerifier;
        private AccessTokenManager _accessTokenManager;
        private const string UserToken = "tokenstring";

        public AccessManagerTests()
        {
            _repository = new MockRepository(MockBehavior.Loose) { DefaultValue = DefaultValue.Mock, CallBase = true };
        }

        [SetUp]
        public void Init()
        {
            _fakeIOboTokenServiceClient = _repository.Create<IOboTokenServiceClient>().Object;
            _fakeIS2STokenProvider = _repository.Create<IS2STokenProvider>().Object;
            _fakeTokenVerifier = _repository.Create<ITokenVerifier>().Object;
            _accessTokenManager = new AccessTokenManager(_fakeIOboTokenServiceClient, _fakeIS2STokenProvider, _fakeTokenVerifier);

            Mock.Get(_fakeTokenVerifier)
                .Setup(
                    stores => stores.IsUserAmlToken(
                    It.IsAny<string>(),
                    It.IsAny<string>()))
                .Returns(true);
        }

        [Test]
        public async Task TrySaveUserTokenToOboAync_Test()
        {
            Mock.Get(_fakeIOboTokenServiceClient)
            .Setup(
                stores => stores.SaveOboTokenAsync(
                It.IsAny<string>(),
                It.IsAny<List<string>>(),
                It.IsAny<string>()))
            .Returns(Task.CompletedTask);

            await _accessTokenManager.TrySaveUserTokenToOboAync(CreateHttpRequest(), "subId", "runId").ConfigureAwait(false);

            Mock.Get(_fakeIOboTokenServiceClient).Verify(
                stores => stores.SaveOboTokenAsync(
                It.IsAny<string>(),
                It.IsAny<List<string>>(),
                It.IsAny<string>()), Times.Once);
        }

        [Test]
        public void TrySaveUserTokenToOboAync_Test_Exception()
        {
            var se = new Microsoft.RelInfra.Common.Exceptions.ServiceInvocationException(
                       operationName: "SaveOboTokenAsync",
                       calledService: "Ae1pService",
                       httpMethod: HttpMethod.Post,
                       innerException: new Exception());

            Mock.Get(_fakeIOboTokenServiceClient)
            .Setup(
                stores => stores.SaveOboTokenAsync(
                It.IsAny<string>(),
                It.IsAny<List<string>>(),
                It.IsAny<string>()))
            .Throws(se);

            var task = _accessTokenManager.TrySaveUserTokenToOboAync(CreateHttpRequest(), "subId", "runId");
            var actualExp = Assert.ThrowsAsync<Microsoft.RelInfra.Common.Exceptions.ServiceInvocationException>(async () => await task.ConfigureAwait(false));
        }

        [Test]
        public void TokenVerifier_Test_False()
        {
            IEnumerable<Claim> claims = new List<Claim>()
            {
                new Claim("oid", "tetsOid"),
                new Claim("tid", "testTid"),
                new Claim("aud", "testAud")
            };

            var tokenVerifier = new TokenVerifier();

            var result = tokenVerifier.IsUserAmlToken(claims, "s2sAppId");
            Assert.IsFalse(result);
        }

        [Test]
        public void TokenVerifier_Test_True()
        {
            IEnumerable<Claim> claims = new List<Claim>()
            {
                new Claim("oid", "tetsOid"),
                new Claim("tid", "testTid"),
                new Claim("aud", AdalConstants.AmlResource)
            };

            var tokenVerifier = new TokenVerifier();

            var result = tokenVerifier.IsUserAmlToken(claims, "s2sAppId");
            Assert.IsTrue(result);
        }

        private HttpRequest CreateHttpRequest()
        {
            var httpContext = new DefaultHttpContext();
            httpContext.Request.Headers["Authorization"] = "Bearer " + UserToken;
            return httpContext.Request;
        }

        [Test]
        public void GetAuthToken_Test_BadRequestExceptionNoAuthToken()
        {
            var httpContext = new DefaultHttpContext();
            httpContext.Request.Headers["test"] = "userToken";
            var actualExp = Assert.Throws<BadRequestException>(() => httpContext.Request.GetAuthToken());
        }

        [Test]
        public void GetAuthToken_Test_BadRequestExceptionSpace()
        {
            var httpContext = new DefaultHttpContext();
            httpContext.Request.Headers["Authorization"] = " ";
            var actualExp = Assert.Throws<BadRequestException>(() => httpContext.Request.GetAuthToken());
        }

        [Test]
        public void GetAuthToken_Test_GetAuthToken_Test_BadRequestExceptionSpaceNoBearer()
        {
            var httpContext = new DefaultHttpContext();
            httpContext.Request.Headers["Authorization"] = "Bear " + "userToken";
            var actualExp = Assert.Throws<BadRequestException>(() => httpContext.Request.GetAuthToken());
        }

        [Test]
        public void GetAuthToken_Test_BadRequestExceptionSpaceNoToken()
        {
            var httpContext = new DefaultHttpContext();
            httpContext.Request.Headers["Authorization"] = "Bearer ";
            var actualExp = Assert.Throws<BadRequestException>(() => httpContext.Request.GetAuthToken());
        }

        [Test]
        public void GetAuthToken_Test()
        {
            var httpContext = new DefaultHttpContext();
            httpContext.Request.OverrideAuthBearerToken("my token");
            var token = httpContext.Request.GetAuthToken();
            Assert.AreEqual(token, "my token");
        }
    }
}
