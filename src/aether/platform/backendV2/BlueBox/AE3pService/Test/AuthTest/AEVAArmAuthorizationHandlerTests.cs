﻿// <copyright file="AEVAArmAuthorizationHandlerTests.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Common.WebApi.Security;
using Microsoft.Aether.AE3pService.Auth;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.MachineLearning.Common.Core;
using Microsoft.MachineLearning.Common.Core.Arm.Contracts;
using Microsoft.MachineLearning.Common.Core.Security;
using Microsoft.MachineLearning.Common.WebApi.Security;
using Microsoft.MachineLearning.Common.WebApi.TeamAccounts;
using Moq;
using NUnit.Framework;
using System;
using System.Net.Http;
using System.Security.Claims;
using System.Threading;
using System.Threading.Tasks;

namespace Microsoft.Aether.AE3pService.Tests.AuthTest
{
    [TestFixture]
    public class AEVAArmAuthorizationHandlerTests
    {
        private readonly MockRepository _repository;
        private ILoggerFactory _fakeLoggerFactory;
        private IARMClient _fakeClient;
        private IUserPermissionCache _fakeCache;
        private ISubscriptionStateCache _fakeSubscriptionStateCache;
        private IOptionsMonitor<ResourceAuthorizationSettings> _fakeResourceAuthorizationSettings;
        private IOptionsMonitor<CheckSubscriptionStateConfiguration> _fakeCheckSubscriptionStateConfig;
        private AEVAArmAuthorizationHandler _aevaArmAuthorizationHandler;

        private HttpContext _context;
        private ClaimsPrincipal _user;
        private ResourceArmScope _scope;
        private ResourceAuthorizationRequirements _requirement;
        private CancellationToken _cancellationToken;

        public AEVAArmAuthorizationHandlerTests()
        {
            _repository = new MockRepository(MockBehavior.Loose) { DefaultValue = DefaultValue.Mock, CallBase = true };
        }

        [SetUp]
        public void Init()
        {
            _fakeLoggerFactory = _repository.Create<ILoggerFactory>().Object;
            _fakeClient = _repository.Create<IARMClient>().Object;
            _fakeCache = _repository.Create<IUserPermissionCache>().Object;
            _fakeSubscriptionStateCache = _repository.Create<ISubscriptionStateCache>().Object;
            _fakeResourceAuthorizationSettings = _repository.Create<IOptionsMonitor<ResourceAuthorizationSettings>>().Object;
            _fakeCheckSubscriptionStateConfig = _repository.Create<IOptionsMonitor<CheckSubscriptionStateConfiguration>>().Object;
            _aevaArmAuthorizationHandler = _repository.Create<AEVAArmAuthorizationHandler>(_fakeLoggerFactory, _fakeClient, _fakeCache, _fakeSubscriptionStateCache, _fakeResourceAuthorizationSettings, _fakeCheckSubscriptionStateConfig).Object;

            _context = _repository.Create<HttpContext>().Object;
            _user = _repository.Create<ClaimsPrincipal>().Object;
            _scope = _repository.Create<ResourceArmScope>(Guid.NewGuid()).Object;
            _requirement = _repository.Create<ResourceAuthorizationRequirements>().Object;
            _cancellationToken = CancellationToken.None;
        }

        [Test]
        public async Task Authorize_ValidateIssuerReturnsTrue_ExpectOtherMethodsNotCalled()
        {
            _requirement.Role = AuthorizationRole.Contributor;

            bool called_GetCachedResourceRole = false;
            AuthorizationRole? role = AuthorizationRole.ARM;

            Mock.Get(_fakeCache)
                .Setup(controller => controller.GetCachedResourceRole(
                    It.IsAny<ClaimsPrincipal>(),
                    It.IsAny<ResourceArmScope>(),
                    It.IsAny<CancellationToken>()))
                .Returns(Task.FromResult(role))
                .Callback(() => called_GetCachedResourceRole = true);

            bool called_GetResourceRole = false;
            Mock.Get(_fakeClient)
                .Setup(controller => controller.GetResourceRole(
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<CancellationToken>()))
                .Callback(() => called_GetResourceRole = true);

            await _aevaArmAuthorizationHandler.Authorize(_context, _user, _scope, _requirement, _cancellationToken).ConfigureAwait(false);
            Assert.False(called_GetCachedResourceRole);
            Assert.False(called_GetResourceRole);
        }

        [Test]
        public async Task Authorize_IsInCacheReturnsTrue_CExpectCalledValidateIssuerReturnsTrue()
        {
            _requirement.Role = AuthorizationRole.Contributor;

            bool called_ValidateIssuer = false;
            Mock.Get(_aevaArmAuthorizationHandler)
                .Setup(controller => controller.ValidateIssuer(It.IsAny<ClaimsPrincipal>()))
                .Returns(true)
                .Callback(() => called_ValidateIssuer = true);

            bool called_GetCachedResourceRole = false;
            AuthorizationRole? role = AuthorizationRole.ARM;
            Mock.Get(_fakeCache)
                .Setup(controller => controller.GetCachedResourceRole(
                    It.IsAny<ClaimsPrincipal>(),
                    It.IsAny<ResourceArmScope>(),
                    It.IsAny<CancellationToken>()))
                .Returns(Task.FromResult(role))
                .Callback(() => called_GetCachedResourceRole = true);

            bool called_GetResourceRole = false;
            Mock.Get(_fakeClient)
                .Setup(controller => controller.GetResourceRole(
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<CancellationToken>()))
                .Callback(() => called_GetResourceRole = true);

            await _aevaArmAuthorizationHandler.Authorize(_context, _user, _scope, _requirement, _cancellationToken).ConfigureAwait(false);
            Assert.True(called_GetCachedResourceRole);
            Assert.False(called_GetResourceRole);
            Assert.True(called_ValidateIssuer);
        }

        [Test]
        public async Task Authorize_ValidateAgainstArm_CacheRoleHasValue_ExpectRoleValue()
        {
            _requirement.Role = AuthorizationRole.ARM;

            Mock.Get(_aevaArmAuthorizationHandler)
                .Setup(controller => controller.ValidateIssuer(It.IsAny<ClaimsPrincipal>()))
                .Returns(true);

            bool called_GetCachedResourceRole = false;
            AuthorizationRole? role = AuthorizationRole.Contributor;
            Mock.Get(_fakeCache)
                .Setup(controller => controller.GetCachedResourceRole(
                    It.IsAny<ClaimsPrincipal>(),
                    It.IsAny<ResourceArmScope>(),
                    It.IsAny<CancellationToken>()))
                .Returns(Task.FromResult(role))
                .Callback(() => called_GetCachedResourceRole = true);

            bool called_GetResourceRole = false;
            Mock.Get(_fakeClient)
                .Setup(controller => controller.GetResourceRole(
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<CancellationToken>()))
                .Callback(() => called_GetResourceRole = true);

            var output = await _aevaArmAuthorizationHandler.Authorize(_context, _user, _scope, _requirement, _cancellationToken).ConfigureAwait(false);
            Assert.True(called_GetCachedResourceRole);
            // does not go in else block.
            Assert.False(called_GetResourceRole);
        }

        [Test]
        public async Task Authorize_ValidateAgainstArm_RoleLessThanRequirementRole_ExpectAuthorizeReturnsFalse()
        {
            _requirement.Role = AuthorizationRole.ARM;

            Mock.Get(_aevaArmAuthorizationHandler)
                .Setup(controller => controller.ValidateIssuer(It.IsAny<ClaimsPrincipal>()))
                .Returns(true);

            bool called_GetCachedResourceRole = false;
            AuthorizationRole? role = AuthorizationRole.Contributor;
            Mock.Get(_fakeCache)
                .Setup(controller => controller.GetCachedResourceRole(
                    It.IsAny<ClaimsPrincipal>(),
                    It.IsAny<ResourceArmScope>(),
                    It.IsAny<CancellationToken>()))
                .Returns(Task.FromResult(role))
                .Callback(() => called_GetCachedResourceRole = true);

            var output = await _aevaArmAuthorizationHandler.Authorize(_context, _user, _scope, _requirement, _cancellationToken).ConfigureAwait(false);
            Assert.True(called_GetCachedResourceRole);
            Assert.False(output);
        }

        [Test]
        public async Task Authorize_ValidateAgainstArm_RoleMoreThanRequirementROle_AuthorizeReturnsTrue()
        {
            _requirement.Role = AuthorizationRole.Contributor;

            Mock.Get(_aevaArmAuthorizationHandler)
                .Setup(controller => controller.ValidateIssuer(It.IsAny<ClaimsPrincipal>()))
                .Returns(true);

            bool called_GetCachedResourceRole = false;
            AuthorizationRole? role = AuthorizationRole.ARM;
            Mock.Get(_fakeCache)
                .Setup(controller => controller.GetCachedResourceRole(
                    It.IsAny<ClaimsPrincipal>(),
                    It.IsAny<ResourceArmScope>(),
                    It.IsAny<CancellationToken>()))
                .Returns(Task.FromResult(role))
                .Callback(() => called_GetCachedResourceRole = true);

            var output = await _aevaArmAuthorizationHandler.Authorize(_context, _user, _scope, _requirement, _cancellationToken).ConfigureAwait(false);
            Assert.True(called_GetCachedResourceRole);
            Assert.True(output);
        }

        [Test]
        public void Authorize_ValidateAgainstArm_TryBlockException_StatusCodeInternalServerError_ExpectExceptionThrown()
        {
            _requirement.Role = AuthorizationRole.ARM;
            Mock.Get(_aevaArmAuthorizationHandler)
                .Setup(controller => controller.ValidateIssuer(It.IsAny<ClaimsPrincipal>()))
                .Returns(true);

            var fakeServiceInvocationException = new ServiceInvocationException(_repository.Create<HttpRequestMessage>().Object, _repository.Create<HttpResponseMessage>().Object, "mock ServiceInvocationException");
            fakeServiceInvocationException.Response.StatusCode = System.Net.HttpStatusCode.InternalServerError;
            AuthorizationRole? role = AuthorizationRole.Contributor;
            Mock.Get(_fakeCache)
                .SetupSequence(controller => controller.GetCachedResourceRole(
                    It.IsAny<ClaimsPrincipal>(),
                    It.IsAny<ResourceArmScope>(),
                    It.IsAny<CancellationToken>()))
                .Returns(Task.FromResult(role))
                .Throws(fakeServiceInvocationException);

            Assert.That(() => _aevaArmAuthorizationHandler.Authorize(_context, _user, _scope, _requirement, _cancellationToken), Throws.TypeOf<ServiceInvocationException>());
        }

        [Test]
        public async Task Authorize_ValidateAgainstArm_TryBlockException_StatusCodeLessThan_ExpectAuthorizeReturnsFalse()
        {
            _requirement.Role = AuthorizationRole.ARM;
            Mock.Get(_aevaArmAuthorizationHandler)
                .Setup(controller => controller.ValidateIssuer(It.IsAny<ClaimsPrincipal>()))
                .Returns(true);

            var fakeServiceInvocationException = new ServiceInvocationException(_repository.Create<HttpRequestMessage>().Object, _repository.Create<HttpResponseMessage>().Object, "mock ServiceInvocationException");
            fakeServiceInvocationException.Response.StatusCode = System.Net.HttpStatusCode.BadRequest;
            AuthorizationRole? role = AuthorizationRole.Contributor;
            Mock.Get(_fakeCache)
                .SetupSequence(controller => controller.GetCachedResourceRole(
                    It.IsAny<ClaimsPrincipal>(),
                    It.IsAny<ResourceArmScope>(),
                    It.IsAny<CancellationToken>()))
                .Returns(Task.FromResult(role))
                .Throws(fakeServiceInvocationException);

            var output = await _aevaArmAuthorizationHandler.Authorize(_context, _user, _scope, _requirement, _cancellationToken).ConfigureAwait(false);
            Assert.False(output);
        }
    }
}
