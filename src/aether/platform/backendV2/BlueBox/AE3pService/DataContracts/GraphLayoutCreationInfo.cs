﻿// <copyright file="GraphLayoutCreationInfo.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Collections.Generic;
using System.Runtime.Serialization;

namespace Microsoft.Aether.AEVA.DataContracts
{
    [DataContract]
    public class GraphLayoutCreationInfo
    {
        /// <summary>
        /// key is nodeId
        /// </summary>
        [DataMember]
        public IDictionary<string, NodeLayout> NodeLayouts { get; set; }

        [DataMember]
        public string ExtendedData { get; set; }

        [DataMember]
        public IList<GraphAnnotationNode> AnnotationNodes { get; set; }
    }
}
