﻿// <copyright file="UIParameterHint.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Runtime.Serialization;
using Newtonsoft.Json;

namespace Microsoft.Aether.AEVA.DataContracts
{
    [DataContract]
    public class UIParameterHint : IEquatable<UIParameterHint>
    {
        public UIParameterHint()
        {
            UxIgnore = false;
            Anonymous = false;
        }

        [DataMember]
        public UIWidgetTypeEnum UIWidgetType { get; set; } = UIWidgetTypeEnum.Default;

        [DataMember]
        public UIColumnPicker ColumnPicker { get; set; }

        [DataMember]
        public UIScriptLanguageEnum? UIScriptLanguage { get; set; }

        [DataMember]
        public UIJsonEditor JsonEditor { get; set; }

        // Used for PromptFlowConnectionSelection
        [DataMember]
        [JsonProperty(PropertyName = "PromptFlowConnectionSelector", DefaultValueHandling = DefaultValueHandling.Ignore, NullValueHandling = NullValueHandling.Ignore)]
        public UIPromptFlowConnectionSelector PromptFlowConnectionSelector { get; set; }

        // Used for AzureOpenAIDeploymentNameSelection
        [DataMember]
        [JsonProperty(PropertyName = "AzureOpenAIDeploymentNameSelector", DefaultValueHandling = DefaultValueHandling.Ignore, NullValueHandling = NullValueHandling.Ignore)]
        public UIAzureOpenAIDeploymentNameSelector AzureOpenAIDeploymentNameSelector { get; set; }

        [DataMember]
        [JsonProperty(PropertyName = "UxIgnore", DefaultValueHandling = DefaultValueHandling.Ignore)]
        public bool UxIgnore { get; set; }

        // UX don't show name for this parameter and sdk ignore
        [DataMember]
        [JsonProperty(PropertyName = "Anonymous", DefaultValueHandling = DefaultValueHandling.Ignore)]
        public bool Anonymous { get; set; }

        public static bool operator ==(UIParameterHint left, UIParameterHint right)
        {
            return Equals(left, right);
        }

        public static bool operator !=(UIParameterHint left, UIParameterHint right)
        {
            return !Equals(left, right);
        }

        public bool Equals(UIParameterHint other)
        {
            if (ReferenceEquals(null, other))
            {
                return false;
            }

            if (ReferenceEquals(this, other))
            {
                return true;
            }

            return UIWidgetType == other.UIWidgetType && Equals(JsonEditor, other.JsonEditor) && UxIgnore == other.UxIgnore && Anonymous == other.Anonymous;
        }

        public override bool Equals(object obj)
        {
            if (ReferenceEquals(null, obj))
            {
                return false;
            }

            if (ReferenceEquals(this, obj))
            {
                return true;
            }

            if (obj.GetType() != this.GetType())
            {
                return false;
            }

            return Equals((UIParameterHint)obj);
        }

        public override int GetHashCode()
        {
            unchecked
            {
                var hashCode = (int)UIWidgetType;
                hashCode = (hashCode * 397) ^ (JsonEditor != null ? JsonEditor.GetHashCode() : 0);
                hashCode = (hashCode * 397) ^ UxIgnore.GetHashCode();
                hashCode = (hashCode * 397) ^ Anonymous.GetHashCode();
                return hashCode;
            }
        }
    }

    [DataContract]
    public class UIJsonEditor : IEquatable<UIJsonEditor>
    {
        [DataMember]
        public string JsonSchema { get; set; }

        public static bool operator ==(UIJsonEditor left, UIJsonEditor right)
        {
            return Equals(left, right);
        }

        public static bool operator !=(UIJsonEditor left, UIJsonEditor right)
        {
            return !Equals(left, right);
        }

        public bool Equals(UIJsonEditor other)
        {
            if (ReferenceEquals(null, other))
            {
                return false;
            }

            if (ReferenceEquals(this, other))
            {
                return true;
            }

            return string.Equals(JsonSchema, other.JsonSchema, StringComparison.InvariantCulture);
        }

        public override bool Equals(object obj)
        {
            if (ReferenceEquals(null, obj))
            {
                return false;
            }

            if (ReferenceEquals(this, obj))
            {
                return true;
            }

            if (obj.GetType() != this.GetType())
            {
                return false;
            }

            return Equals((UIJsonEditor)obj);
        }

        public override int GetHashCode()
        {
            return JsonSchema != null ? StringComparer.InvariantCulture.GetHashCode(JsonSchema) : 0;
        }
    }
}
