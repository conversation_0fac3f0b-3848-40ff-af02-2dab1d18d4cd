﻿// <copyright file="GraphReferenceNode.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Runtime.Serialization;

namespace Microsoft.Aether.AEVA.DataContracts
{
    [DataContract]
    public class GraphReferenceNode : GraphExecutableNode
    {
        // GraphId specifies the ID of a GraphEntity for this subgraph node.
        // This is required for graphs which are submitted for execution.  Subgraph drafts must be
        // promoted to regular subgraphs before execution.
        [DataMember]
        public string GraphId { get; set; }

        // If specified, a default compute will be applied to the inner graph when submitting
        // the pipeline run for the subgraph job.
        // Note:  If GraphReferenceNode.UseGraphDefaultCompute is set to true while GraphReferenceNode.DefaultCompute
        //        is also specified, the outer graph's default compute will override the subgraph node setting.
        [DataMember]
        public ComputeSetting DefaultCompute { get; set; }

        // If specified, a default datastore will be applied to the inner graph when submitting
        // the pipeline run for the subgraph job.
        // Note:  If GraphReferenceNode.UseGraphDefaultDatastore is set to true while GraphReferenceNode.DefaultDatastore
        //        is also specified, the outer graph's default datastore will override the subgraph node setting.
        [DataMember]
        public DatastoreSetting DefaultDatastore { get; set; }
    }
}
