﻿// <copyright file="IdentitySetting.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Newtonsoft.Json;
using System;
using System.ComponentModel;
using System.Runtime.Serialization;

namespace Microsoft.Aether.AEVA.DataContracts
{
    [DataContract]
    public class IdentitySetting
    {
        [DataMember]
        [DefaultValue(IdentityType.UserIdentity)]
        [NodeConfig(Description = "Type of the identity, can be managed or user_identity(user credential pass through)")]
        public IdentityType Type { get; set; }

        [DataMember]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        [NodeConfig(Description = "ClientId of the managed identity")]
        public Guid? ClientId { get; set; }

        [DataMember]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        [NodeConfig(Description = "ObjectId(PrincipalId) of the managed identity")]
        public Guid? ObjectId { get; set; }

        [DataMember]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        [NodeConfig(Description = "ResourceId of the managed identity")]
        public string MsiResourceId { get; set; }
    }

    [DataContract]
    public enum IdentityType
    {
        [EnumMember(Value = "user_identity")]
        UserIdentity,
        [EnumMember(Value = "managed")]
        Managed,
        [EnumMember(Value = "aml_token")]
        AMLToken
    }
}
