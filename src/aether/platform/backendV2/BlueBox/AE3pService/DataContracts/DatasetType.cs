﻿// <copyright file="DatasetType.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Runtime.Serialization;

namespace Microsoft.Aether.AEVA.DataContracts
{
    /// <summary>
    /// The data type.
    /// When talking about data type in Aether, there are two, the data type and the semantic type.
    /// The data type is represented by Dataset Type, it is equivalent to types in a programming language.
    /// The other type is semantic type, which represents the semantics of the data, for example, age data,
    /// weather data, DataFrameDirectory, etc. The semantic type is represented by DataTypeIdsList in
    /// <seealso cref="StructuredInterfaceInput"/>.
    /// </summary>
    [DataContract]
    public enum DatasetType
    {
        /// <summary>
        /// This matches the "file" dataset type in the Dataset service.
        /// </summary>
        File = 0,

        /// <summary>
        /// This matches the "tabular" dataset type in Dataset service.
        /// </summary>
        Tabular = 1
    }
}
