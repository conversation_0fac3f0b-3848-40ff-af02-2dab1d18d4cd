﻿// <copyright file="Recurrence.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Collections.Generic;
using System.Runtime.Serialization;

namespace Microsoft.Aether.AEVA.DataContracts
{
    [DataContract]
    public class Recurrence
    {
        [DataMember]
        public Frequency Frequency { get; set; }

        [DataMember]
        public int Interval { get; set; }

        // Note: the value here is string because we are following the convention set by
        // LogicApps, the expectation is to provide a string with the format: yyyy-MM-ddTHH:mm:ss
        // in conjunction with pre defined TimeZone strings
        // validation for these will happen in ae3pservice and sdks
        [DataMember]
        public string StartTime { get; set; }

        [DataMember]
        public string TimeZone { get; set; }

        [DataMember]
        public RecurrenceSchedule Schedule { get; set; }
    }

    [DataContract]
    public class RecurrenceSchedule
    {
        [DataMember]
        public List<int> Hours { get; set; }

        [DataMember]
        public List<int> Minutes { get; set; }

        [DataMember]
        public List<WeekDays> WeekDays { get; set; }
    }

    [DataContract]
    public enum Frequency
    {
        [EnumMember]
        Month,

        [EnumMember]
        Week,

        [EnumMember]
        Day,

        [EnumMember]
        Hour,

        [EnumMember]
        Minute
    }

    [DataContract]
    public enum WeekDays
    {
        [EnumMember]
        Monday,

        [EnumMember]
        Tuesday,

        [EnumMember]
        Wednesday,

        [EnumMember]
        Thursday,

        [EnumMember]
        Friday,

        [EnumMember]
        Saturday,

        [EnumMember]
        Sunday
    }
}
