﻿// <copyright file="PipelineRunCreationInfo.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Runtime.Serialization;

namespace Microsoft.Aether.AEVA.DataContracts
{
    [DataContract]
    public class PipelineRunCreationInfo
    {
        public PipelineRunCreationInfo()
        {
            KvTags = new Dictionary<string, string>();
            StepTags = new Dictionary<string, string>();
            Properties = new Dictionary<string, string>();
            StepProperties = new Dictionary<string, string>();
        }

        [DataMember]
        public string Description { get; set; }

        /// <summary>
        ///   Deprecated, do not use. Use ContinueRunOnStepFailure instead.
        /// </summary>
        // TODO yanrez PipelineRun: rename
        [DataMember]
        public bool ContinueExperimentOnNodeFailure { get; set; }

        [DataMember]
        public bool ContinueRunOnStepFailure { get; set; }

        /// <summary>
        /// True to indicate we should continue to run a step if the step has optional input connected to other step's outputs
        /// but the other step failed or skipped
        /// </summary>
        [DataMember]
        [DefaultValue(true)]
        [JsonProperty(DefaultValueHandling = DefaultValueHandling.Populate)]
        public bool ContinueRunOnFailedOptionalInput { get; set; }

        [DataMember]
        public IDictionary<string, string> ParameterAssignments { get; set; }

        [DataMember]
        public IDictionary<string, LegacyDataPath> DataPathAssignments { get; set; }

        [DataMember]
        public IDictionary<string, DataSetDefinitionValue> DataSetDefinitionValueAssignments { get; set; }

        [DataMember]
        public IDictionary<string, AssetOutputSettings> AssetOutputSettingsAssignments { get; set; }

        /// <summary>
        /// For subgraph runs, this contains a mapping of graph input names to assigned input values
        /// </summary>
        [DataMember]
        public IDictionary<string, GraphInputValue> GraphInputAssignments { get; set; }

        [DataMember]
        public string RunSource { get; set; }

        [DataMember]
        public RunType RunType { get; set; }

        [DataMember]
        public string RunHistoryExperimentName { get; set; }

        [DataMember]
        public string DisplayName { get; set; }

        /// <summary>
        /// User provided RunId
        /// </summary>
        [DataMember]
        public string RunId { get; set; }

        [Obsolete]
        [DataMember]
        public NodeCompositionMode NodeCompositionMode { get; set; }

        [DataMember(Name = "tags")]
        public IDictionary<string, string> KvTags { get; set; }

        /// <summary>
        /// User provided tags for steps of pipeline. Each step run in pipeline will get these tags assigned automatically during orchestration.
        /// </summary>
        [DataMember]
        public IDictionary<string, string> StepTags { get; set; }

        /// <summary>
        /// User provided properties for steps of pipeline. Each step run in pipeline will get these properties assigned automatically during orchestration.
        /// </summary>
        [DataMember]
        public IDictionary<string, string> Properties { get; set; }

        [DataMember]
        public IDictionary<string, string> StepProperties { get; set; }

        [DataMember]
        public bool EnableNotification { get; set; }

        [DataMember]
        public bool PreserveSubGraphs { get; set; }

        [DataMember]
        public bool CredentialPassthrough { get; set; }

        /// <summary>
        /// For nested subgraph runs, this is the run ID of the top-level pipeline run
        /// </summary>
        [DataMember]
        public string RootPipelineRunId { get; set; }

        /// <summary>
        /// For nested subgraph runs, this is the parent experiment id of the subgraph pipeline run
        /// </summary>
        [DataMember]
        public string ParentExperimentId { get; set; }

        /// <summary>
        /// True to indicate enforce rerun all child runs under this root run, all child runs will not latching/resue to any run and cannot be latched/reused by other runs
        /// False to indicate set RegenerateOutput of all child runs to false and do common reuse logic
        /// Null to indicate not enable this feature, follow existing logic of RegenerateOutput of node
        /// </summary>
        [DataMember]
        public bool? EnforceRerun { get; set; }

        /// <summary>
        /// Intermediate control parameters for sub graph node
        /// </summary>
        [DataMember]
        public Dictionary<string, string> IntermediateControlParameters { get; set; }

        /// <summary>
        /// Pipeline parameters which is resolved from upstream control outputs
        /// </summary>
        [DataMember]
        public IList<string> InputTypePipelineParameters { get; set; }

        [DataMember]
        public CloudPrioritySetting DefaultCloudPriority { get; set; }

        [DataMember]
        public ComputeSetting DefaultCompute { get; set; }

        [DataMember]
        public DatastoreSetting DefaultDatastore { get; set; }

        [DataMember]
        public IdentitySetting IdentityConfig { get; set; }

        /// <summary>
        /// Used to specify the timeout for pipeline in seconds
        /// The specified timeout should between 1 secondes and 5184000 seconds (60 days)
        /// </summary>
        [DataMember]
        [Range(1, 5184000, ErrorMessage = "Value for {0} must between {1} and {2}")]
        public int? PipelineTimeoutSeconds { get; set; }
    }
}
