﻿// <copyright file="StructuredInterfaceOutput.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Runtime.Serialization;
using Newtonsoft.Json;

namespace Microsoft.Aether.AEVA.DataContracts
{
    [DataContract]
    public class StructuredInterfaceOutput
    {
        // Name of the port, also used to set up an environment variable on the execution node
        [DataMember]
        public string Name { get; set; }

        [DataMember]
        public string Label { get; set; }

        [DataMember]
        public string DataTypeId { get; set; }

        [DataMember]
        public string PassThroughDataTypeInputName { get; set; }

        [DataMember]
        public string Description { get; set; }

        // If SkipProcessing is true, cloudlets won't processes this output.
        // This will help building sequential modules without actual inputs and outputs.
        [DataMember]
        public bool SkipProcessing { get; set; }

        // If IsArtifact is true, this port targets to output to the artifact store.
        [DataMember]
        [JsonProperty(PropertyName = "IsArtifact", DefaultValueHandling = DefaultValueHandling.Ignore)]
        public bool IsArtifact { get; set; }

        // AML DataStore name which stores the Azure storage information, e.g. blob account, file share account and data lake
        // If the DataStoreName is empty for cloudlets which requires DataStore reference, DataStore should inherit from the node
        [DataMember]
        public string DataStoreName { get; set; }

        // The valid DataStoreMode for output is None, Mount and Upload
        [DataMember]
        public DataStoreMode DataStoreMode { get; set; }

        // For Upload mode, specify a local path on the compute to upload . Otherwise it can be null.
        [DataMember]
        public string PathOnCompute { get; set; }

        // For Upload mode, indicate whether to overwrite existing data
        [DataMember]
        public bool Overwrite { get; set; }

        [DataMember]
        public string DataReferenceName { get; set; }

        [DataMember]
        public TrainingOutput TrainingOutput { get; set; }

        [DataMember]
        public DatasetOutput DatasetOutput { get; set; }

        [DataMember]
        [JsonProperty(PropertyName = "AssetOutputSettings", NullValueHandling = NullValueHandling.Ignore)]
        public AssetOutputSettings AssetOutputSettings { get; set; }

        // If EarlyAvailable is true, this port will be ready to consume before job finish.
        [DataMember]
        [JsonProperty(PropertyName = "EarlyAvailable", DefaultValueHandling = DefaultValueHandling.Ignore)]
        public bool EarlyAvailable { get; set; }
    }
}
