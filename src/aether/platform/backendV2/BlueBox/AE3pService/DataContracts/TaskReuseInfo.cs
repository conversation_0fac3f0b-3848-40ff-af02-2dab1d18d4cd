﻿// <copyright file="TaskReuseInfo.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Runtime.Serialization;

namespace Microsoft.Aether.AEVA.DataContracts
{
    [DataContract]
    public class TaskReuseInfo
    {
        /// <summary>
        ///    Deprecated, use PipelineRunId instead
        /// </summary>
        // TODO yanrez PipelineRun: rename
        [DataMember]
        public string ExperimentId { get; set; }

        [DataMember]
        public string PipelineRunId { get; set; }

        [DataMember]
        public string NodeId { get; set; }

        [DataMember]
        public string RequestId { get; set; }

        [DataMember]
        public string RunId { get; set; }

        [DataMember]
        public DateTime NodeStartTime { get; set; }

        [DataMember]
        public DateTime NodeEndTime { get; set; }
    }
}
