// <copyright file="EsCloudConfiguration.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>
using System.Collections.Generic;
using System.Runtime.Serialization;
using Microsoft.MachineLearning.Execution.Contracts;
using Newtonsoft.Json;

namespace Microsoft.Aether.AEVA.DataContracts
{
    /// <summary>
    /// This is the run configuration for ES Cloud
    /// </summary>
    [DataContract]
    public class EsCloudConfiguration
    {
        // null/false means not enable the detection based on DataTypeId
        [DataMember]
        [ModuleConfig]
        public bool? EnableOutputToFileBasedOnDataTypeId { get; set; }

        [DataMember]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public EnvironmentConfiguration Environment { get; set; }

        [DataMember]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public HyperDriveConfiguration HyperDriveConfiguration { get; set; }

        [DataMember]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public K8sConfiguration K8sConfig { get; set; }

        [DataMember]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public ResourceConfiguration ResourceConfig { get; set; }

        [DataMember]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public TorchDistributedConfiguration TorchDistributedConfig { get; set; }

        [DataMember]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public TargetSelectorConfiguration TargetSelectorConfig { get; set; }

        [DataMember]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public DockerSettingConfiguration DockerConfig { get; set; }

        [DataMember]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore, DefaultValueHandling = DefaultValueHandling.Ignore)]
        [NodeConfig(SupportLinkSetting = true)]
        public Dictionary<string, string> EnvironmentVariables { get; set; }

        [DataMember]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore, DefaultValueHandling = DefaultValueHandling.Ignore)]
        [NodeConfig(SupportLinkSetting = true)]
        public int? MaxRunDurationSeconds { get; set; }

        [DataMember]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        [NodeConfig(Description = "Identity used to run the job")]
        public IdentitySetting Identity { get; set; }

        [DataMember]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        [NodeConfig(Description = "Interactive services")]
        public Dictionary<string, ApplicationEndpointConfiguration> ApplicationEndpoints { get; set; }

        // A stringified JSON object of the node runconfig for standalone job.
        [DataMember]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore, DefaultValueHandling = DefaultValueHandling.Ignore)]
        [NodeConfig(Description = "Runconfig for standalone job")]
        public string RunConfig { get; set; }
    }

    [DataContract]
    public class EnvironmentConfiguration
    {
        [DataMember]
        [NodeConfig(SupportLinkSetting = true)]
        public string Name { get; set; }

        [DataMember]
        [NodeConfig(SupportLinkSetting = true)]
        public string Version { get; set; }

        [DataMember]
        public bool UseEnvironmentDefinition { get; set; }

        // Use string to store environment definition to avoid deserialization issue.
        [DataMember]
        public string EnvironmentDefinitionString { get; set; }
    }

    [DataContract]
    public class K8sConfiguration
    {
        [DataMember]
        [NodeConfig(SupportLinkSetting = true)]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore, DefaultValueHandling = DefaultValueHandling.Ignore)]
        public int? MaxRetryCount { get; set; }

        [DataMember]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore, DefaultValueHandling = DefaultValueHandling.Ignore)]
        public ResourceConfig ResourceConfiguration { get; set; }

        [DataMember]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore, DefaultValueHandling = DefaultValueHandling.Ignore)]
        public PriorityConfig PriorityConfiguration { get; set; }

        [DataMember]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore, DefaultValueHandling = DefaultValueHandling.Ignore)]
        public InteractiveConfig InteractiveConfiguration { get; set; }
    }

    [DataContract]
    public class ResourceConfig
    {
        [DataMember]
        [NodeConfig(SupportLinkSetting = true)]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore, DefaultValueHandling = DefaultValueHandling.Ignore)]
        public int? GpuCount { get; set; }

        [DataMember]
        [NodeConfig(SupportLinkSetting = true)]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore, DefaultValueHandling = DefaultValueHandling.Ignore)]
        public int? CpuCount { get; set; }

        [DataMember]
        [NodeConfig(SupportLinkSetting = true)]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore, DefaultValueHandling = DefaultValueHandling.Ignore)]
        public int? MemoryRequestInGB { get; set; }
    }

    [DataContract]
    public class PriorityConfig
    {
        [DataMember]
        [NodeConfig(SupportLinkSetting = true)]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore, DefaultValueHandling = DefaultValueHandling.Ignore)]
        public int? JobPriority { get; set; }

        [DataMember]
        [NodeConfig(SupportLinkSetting = true)]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore, DefaultValueHandling = DefaultValueHandling.Ignore)]
        public bool? IsPreemptible { get; set; }

        [DataMember]
        [NodeConfig(SupportLinkSetting = true)]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore, DefaultValueHandling = DefaultValueHandling.Ignore)]
        public List<int> NodeCountSet { get; set; }

        [DataMember]
        [NodeConfig(SupportLinkSetting = true)]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore, DefaultValueHandling = DefaultValueHandling.Ignore)]
        public int? ScaleInterval { get; set; }
    }

    [DataContract]
    public class InteractiveConfig
    {
        [DataMember]
        [NodeConfig(SupportLinkSetting = true)]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore, DefaultValueHandling = DefaultValueHandling.Ignore)]
        public bool? IsSSHEnabled { get; set; }

        [DataMember]
        [NodeConfig(SupportLinkSetting = true)]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore, DefaultValueHandling = DefaultValueHandling.Ignore)]
        public string SSHPublicKey { get; set; }

        [DataMember]
        [NodeConfig(SupportLinkSetting = true)]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore, DefaultValueHandling = DefaultValueHandling.Ignore)]
        public bool? IsIPythonEnabled { get; set; }

        [DataMember]
        [NodeConfig(SupportLinkSetting = true)]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore, DefaultValueHandling = DefaultValueHandling.Ignore)]
        public bool? IsTensorBoardEnabled { get; set; }

        [DataMember]
        [NodeConfig(SupportLinkSetting = true)]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore, DefaultValueHandling = DefaultValueHandling.Ignore)]
        public int? InteractivePort { get; set; }
    }

    [DataContract]
    public class TorchDistributedConfiguration
    {
        [DataMember]
        [NodeConfig(SupportLinkSetting = true)]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore, DefaultValueHandling = DefaultValueHandling.Ignore)]
        public int? ProcessCountPerNode { get; set; }
    }

    [DataContract]
    public class TargetSelectorConfiguration
    {
        // Default value is true, Name is AllowSpotVm in RunSettingBuilder
        [DataMember]
        [NodeConfig(SupportLinkSetting = true)]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore, DefaultValueHandling = DefaultValueHandling.Ignore)]
        public bool LowPriorityVMTolerant { get; set; } = true;

        // Type is JsonString in RunSettingBuilder
        [DataMember]
        [NodeConfig(SupportLinkSetting = true)]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore, DefaultValueHandling = DefaultValueHandling.Ignore)]
        public List<string> ClusterBlockList { get; set; }

        [DataMember]
        [NodeConfig(SupportLinkSetting = true)]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore, DefaultValueHandling = DefaultValueHandling.Ignore)]
        public string ComputeType { get; set; }

        // Type is JsonString in RunSettingBuilder
        [DataMember]
        [NodeConfig(SupportLinkSetting = true)]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore, DefaultValueHandling = DefaultValueHandling.Ignore)]
        public List<string> InstanceType { get; set; }

        // Type is JsonString in RunSettingBuilder
        [DataMember]
        [NodeConfig(SupportLinkSetting = true)]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore, DefaultValueHandling = DefaultValueHandling.Ignore)]
        public List<string> InstanceTypes { get; set; }

        // Default value is false in RunSettingBuilder
        [DataMember]
        [NodeConfig(SupportLinkSetting = true)]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore, DefaultValueHandling = DefaultValueHandling.Ignore)]
        public bool MyResourceOnly { get; set; } = false;

        [DataMember]
        [NodeConfig(SupportLinkSetting = true)]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore, DefaultValueHandling = DefaultValueHandling.Ignore)]
        public string PlanId { get; set; }

        [DataMember]
        [NodeConfig(SupportLinkSetting = true)]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore, DefaultValueHandling = DefaultValueHandling.Ignore)]
        public string PlanRegionId { get; set; }

        [DataMember]
        [NodeConfig(SupportLinkSetting = true)]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore, DefaultValueHandling = DefaultValueHandling.Ignore)]
        public List<string> Region { get; set; }

        // Type is JsonString in RunSettingBuilder
        [DataMember]
        [NodeConfig(SupportLinkSetting = true)]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore, DefaultValueHandling = DefaultValueHandling.Ignore)]
        public List<string> Regions { get; set; }

        // Type is JsonString in RunSettingBuilder
        [DataMember]
        [NodeConfig(SupportLinkSetting = true)]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore, DefaultValueHandling = DefaultValueHandling.Ignore)]
        public List<string> VcBlockList { get; set; }
    }

    [DataContract]
    public class DockerSettingConfiguration
    {
        /// <summary>
        /// Set true to perform this run inside a Docker container.
        /// </summary>
        [DataMember]
        [NodeConfig(SupportLinkSetting = true)]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore, DefaultValueHandling = DefaultValueHandling.Ignore)]
        public bool? UseDocker { get; set; }

        /// <summary>
        /// Set false to disable AzureML's usage of the Docker shared volumes feature to work around bugs in certain versions of Docker for Windows.
        /// </summary>
        [DataMember]
        [NodeConfig(SupportLinkSetting = true)]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore, DefaultValueHandling = DefaultValueHandling.Ignore)]
        public bool? SharedVolumes { get; set; }

        /// <summary>
        /// The shared memory size setting for NVidia GPUs.
        /// </summary>
        /// <example>2g</example>
        [DataMember]
        [NodeConfig(SupportLinkSetting = true)]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore, DefaultValueHandling = DefaultValueHandling.Ignore)]
        public string ShmSize { get; set; }

        /// <summary>
        /// Extra arguments to the Docker run command.
        /// </summary>
        [DataMember]
        [NodeConfig(SupportLinkSetting = true)]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore, DefaultValueHandling = DefaultValueHandling.Ignore)]
        public List<string> Arguments { get; set; }
    }
}
