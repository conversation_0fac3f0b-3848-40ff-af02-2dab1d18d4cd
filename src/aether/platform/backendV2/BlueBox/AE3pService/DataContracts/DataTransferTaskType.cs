﻿// <copyright file="DataTransferTaskType.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Runtime.Serialization;

namespace Microsoft.Aether.AEVA.DataContracts
{
    [DataContract]
    public enum DataTransferTaskType
    {
        [EnumMember(Value = "import_data")]
        ImportData,

        [EnumMember(Value = "export_data")]
        ExportData,

        [EnumMember(Value = "copy_data")]
        CopyData,
    }
}
