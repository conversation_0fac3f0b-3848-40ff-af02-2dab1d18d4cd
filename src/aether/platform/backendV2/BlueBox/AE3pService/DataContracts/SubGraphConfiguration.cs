﻿// <copyright file="SubGraphConfiguration.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.ComponentModel;
using System.Runtime.Serialization;
using Newtonsoft.Json;

namespace Microsoft.Aether.AEVA.DataContracts
{
    /// <summary>
    /// Configuration settings for a module that represents a subgraph
    /// </summary>
    [DataContract]
    public class SubGraphConfiguration
    {
        /// <summary>
        /// For modules that represent a subgraph, GraphId is the ID of the graph for that subgraph.
        /// GraphId may be used to fetch the GraphEntity, EntityInterface, and GraphLayout entities from the graph store.
        /// Only one of GraphId or GraphDraftId should be specified.
        /// </summary>
        [DataMember]
        public string GraphId { get; set; }

        /// <summary>
        /// For modules that represent a subgraph draft, GraphDraftId is the ID of the graph draft for that subgraph.
        /// GraphDraftId may be used to fetch the GraphDraftEntity from the graph draft store.  (GraphDraftEntity is mutable.)
        /// Only one of GraphId or GraphDraftId should be specified.
        /// </summary>
        [DataMember]
        public string GraphDraftId { get; set; }

        // Run time overwrite for DefaultCloudPriority of SubGraph
        [DataMember]
        [NodeConfig(Description = "Default cloud priority for sub-graph", IsRequired = false)]
        [JsonProperty(PropertyName = "DefaultCloudPriority", NullValueHandling = NullValueHandling.Ignore, DefaultValueHandling = DefaultValueHandling.Ignore)]
        public CloudPrioritySetting DefaultCloudPriority { get; set; }

        // Indicate whether this node is running with static+dynamic mode
        // Need to keep this field nullable to avoid merge cloud setting generate non-empty SubGraphConfiguration
        [DataMember]
        [JsonProperty(PropertyName = "IsDynamic", NullValueHandling = NullValueHandling.Ignore, DefaultValueHandling = DefaultValueHandling.Ignore)]
        [DefaultValue(false)]
        public bool? IsDynamic { get; set; } = false;
    }
}
