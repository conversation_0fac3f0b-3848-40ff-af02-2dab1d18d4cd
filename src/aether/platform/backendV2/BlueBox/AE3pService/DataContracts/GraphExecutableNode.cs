﻿// <copyright file="GraphExecutableNode.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Collections.Generic;
using System.Runtime.Serialization;

namespace Microsoft.Aether.AEVA.DataContracts
{
    [DataContract]
    public class GraphExecutableNode
    {
        [DataMember]
        public string Id { get; set; }

        [DataMember]
        public string ModuleId { get; set; }

        [DataMember]
        public string Comment { get; set; }

        /// <summary>
        /// Optional name identifier to differentiate similar nodes, used by UX/client layers
        /// </summary>
        [DataMember]
        public string Name { get; set; }

        [DataMember]
        public IList<ParameterAssignment> ModuleParameters { get; set; }

        [DataMember]
        public IList<ParameterAssignment> ModuleMetadataParameters { get; set; }

        [DataMember]
        public IList<OutputSetting> ModuleOutputSettings { get; set; }

        [DataMember]
        public IList<InputSetting> ModuleInputSettings { get; set; }

        /// <summary>
        /// If true, the node will use the graph-level default compute and ignore any node-level compute settings
        /// </summary>
        [DataMember]
        public bool UseGraphDefaultCompute { get; set; }

        /// <summary>
        /// If true, the node will use the graph-level default datastore and ignore any node-level datastore settings
        /// </summary>
        [DataMember]
        public bool UseGraphDefaultDatastore { get; set; }

        /// <summary>
        /// Override deterministic module reuse to force rerun the module
        /// </summary>
        [DataMember]
        public bool RegenerateOutput { get; set; }

        /// <summary>
        /// Control inputs for a node. In order to make a node suitable for the execution, each control input should be
        /// evaluated to True value.
        /// </summary>
        [DataMember]
        public List<ControlInput> ControlInputs { get; set; }

        /// <summary>
        /// Gets or Sets CloudSettings. CloudSettings is the cloud specific settings
        /// </summary>
        [DataMember]
        public CloudSettings CloudSettings { get; set; }

        /// <summary>
        /// Execution phase in which a module is executed.
        /// </summary>
        [DataMember]
        public ExecutionPhase ExecutionPhase { get; set; }

        /// <summary>
        /// Run Attribution, will be recorded as runTypeV2.traits for telemetry usage
        /// </summary>
        [DataMember]
        public string RunAttribution { get; set; }
    }
}
