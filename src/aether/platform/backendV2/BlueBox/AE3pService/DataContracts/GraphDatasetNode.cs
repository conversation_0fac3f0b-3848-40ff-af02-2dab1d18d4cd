﻿// <copyright file="GraphDatasetNode.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Runtime.Serialization;

namespace Microsoft.Aether.AEVA.DataContracts
{
    /// <summary>
    /// A class that represents a data set graph node.
    /// There are 3 possible cases:
    ///   1. Data source already exists and it's referenced using DatasetId field.
    ///   2. Data source doesn't exists yet and it should be created from the specified data path parameter (DataPathParameterName field).
    ///   3. A data set definition is used to reference a data set.
    /// Only 1 of possible cases should be specified.
    /// </summary>
    [DataContract]
    public class GraphDatasetNode
    {
        /// <summary>
        /// Node id.
        /// </summary>
        [DataMember]
        public string Id { get; set; }

        /// <summary>
        /// Data source id.
        /// </summary>
        [DataMember]
        public string DatasetId { get; set; }

        /// <summary>
        /// Parameter name for the data path which will produce data set.
        /// </summary>
        [DataMember]
        public string DataPathParameterName { get; set; }

        /// <summary>
        /// A data set definition
        /// </summary>
        [DataMember]
        public DataSetDefinition DataSetDefinition { get; set; }
    }
}
