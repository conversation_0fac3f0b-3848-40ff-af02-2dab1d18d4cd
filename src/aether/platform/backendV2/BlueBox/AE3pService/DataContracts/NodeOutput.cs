﻿// <copyright file="NodeOutput.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Runtime.Serialization;

namespace Microsoft.Aether.AEVA.DataContracts
{
    [DataContract]
    public class NodeOutput
    {
        // TODO: need review the usage scenario
        [DataMember]
        public string DataTypeId { get; set; }

        [DataMember]
        public long LogicalSizeInBytes { get; set; }

        [DataMember]
        public long PhysicalSizeInBytes { get; set; }

        [DataMember]
        public string Hash { get; set; }

        [DataMember]
        public DataLocation DataLocation { get; set; }
    }
}
