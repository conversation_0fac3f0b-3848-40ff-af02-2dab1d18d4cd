﻿// <copyright file="GraphDraftEntity.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Collections.Generic;
using System.Runtime.Serialization;

namespace Microsoft.Aether.AEVA.DataContracts
{
    [DataContract]
    public class GraphDraftEntity : BaseNoStatusEntity
    {
        [DataMember]
        [UserMutable]
        public IList<GraphModuleNode> ModuleNodes { get; set; }

        [DataMember]
        [UserMutable]
        public IList<GraphDatasetNode> DatasetNodes { get; set; }

        [DataMember]
        [UserMutable]
        public IList<GraphReferenceNode> SubGraphNodes { get; set; }

        [DataMember]
        [UserMutable]
        public IList<GraphControlReferenceNode> ControlReferenceNodes { get; set; }

        [DataMember]
        [UserMutable]
        public IList<GraphControlNode> ControlNodes { get; set; }

        [DataMember]
        [UserMutable]
        public IList<GraphEdge> Edges { get; set; }

        [DataMember]
        [UserMutable]
        public EntityInterface EntityInterface { get; set; }

        [DataMember]
        [UserMutable]
        public GraphLayout GraphLayout { get; set; }

        [DataMember]
        public CreatedBy CreatedBy { get; set; }

        [DataMember]
        public CreatedBy LastUpdatedBy { get; set; }

        [DataMember]
        [UserMutable]
        public ComputeSetting DefaultCompute { get; set; }

        [DataMember]
        [UserMutable]
        public DatastoreSetting DefaultDatastore { get; set; }

        [DataMember]
        [UserMutable]
        public CloudPrioritySetting DefaultCloudPriority { get; set; }

        [DataMember]
        [UserMutable]
        public IDictionary<string, string> ExtendedProperties { get; set; }

        /// <summary>
        /// The hierarchical subgraph module id list.
        /// For a root graph without subgraph, it will be null/empty.
        /// For a subgraph referenced by a parent graph, it will contains the subgraph module id itself as well.
        /// E.g. RootGraph#1 -> SubGraph#2 -> SubGraph#3, the <see cref="ParentSubGraphModuleIds" /> of SubGraph#3 will be [2, 3].
        /// </summary>
        [DataMember]
        public IList<string> ParentSubGraphModuleIds { get; set; }
    }
}