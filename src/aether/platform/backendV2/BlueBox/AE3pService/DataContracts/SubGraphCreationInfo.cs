﻿// <copyright file="SubGraphCreationInfo.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Collections.Generic;
using System.Runtime.Serialization;

namespace Microsoft.Aether.AEVA.DataContracts
{
    [DataContract]
    public class SubGraphCreationInfo
    {
        [DataMember]
        public string Name { get; set; }

        [DataMember]
        public string DisplayName { get; set; }

        [DataMember]
        public VisualGraph VisualGraph { get; set; }

        [DataMember]
        public EntityInterface Interface { get; set; }

        [DataMember]
        public string Description { get; set; }

        [DataMember]
        // If true, the subgraph will be created as a subgraph draft, with the graph stored in
        // a GraphDraftEntity instead of GraphEntity
        public bool CreateAsDraft { get; set; }

        [DataMember]
        // If the subgraph is marked as deterministic, the pipeline may reuse an entire subgraph job as a single unit.
        // If it is non-deterministic, then a new subgraph run will be scheduled each time and node reuse would happen
        // at the child/leaf level.
        public bool IsDeterministic { get; set; }

        [DataMember]
        public IDictionary<string, string> KvTags { get; set; }

        [DataMember]
        public IDictionary<string, string> Properties { get; set; }
    }
}
