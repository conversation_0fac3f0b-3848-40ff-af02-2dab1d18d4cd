﻿// <copyright file="DeepPipelineVisualGraphWithEntityInterface.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Collections.Generic;
using System.Runtime.Serialization;

namespace Microsoft.Aether.AEVA.DataContracts
{
    [DataContract]
    public class DeepPipelineVisualGraphWithEntityInterface
    {
        [DataMember]
        public PipelineRunEntity PipelineRunEntity;

        [DataMember]
        public VisualGraphWithEntityInterface GraphWithInterface;

        [DataMember]
        public IList<DataSourceEntity> DataSourceEntities;

        [DataMember]
        public IList<Module> Modules;
    }
}
