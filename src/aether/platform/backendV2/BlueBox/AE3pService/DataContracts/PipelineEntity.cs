﻿// <copyright file="PipelineEntity.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;

namespace Microsoft.Aether.AEVA.DataContracts
{
    public class PipelineEntity : BaseEntity
    {
        public PipelineEntity()
        {
            KvTags = new Dictionary<string, string>();
            StepTags = new Dictionary<string, string>();
            Properties = new Dictionary<string, string>();
            StepProperties = new Dictionary<string, string>();
        }

        [DataMember]
        public string Name { get; set; }

        [DataMember]
        public string Description { get; set; }

        [DataMember]
        public string Version { get; set; }

        [DataMember]
        public string GraphId { get; set; }

        [DataMember]
        public int TotalRunSteps { get; set; }

        [DataMember]
        public IDictionary<string, string> ParameterAssignments { get; set; }

        [DataMember]
        public IDictionary<string, DataSetDefinitionValue> DataSetDefinitionValueAssignments { get; set; }

        [DataMember]
        public IDictionary<string, AssetOutputSettings> AssetOutputSettingsAssignments { get; set; }

        [DataMember]
        public string Url { get; set; }

        [DataMember]
        public bool ContinueRunOnStepFailure { get; set; }

        [DataMember]
        [DefaultValue(true)]
        [JsonProperty(DefaultValueHandling = DefaultValueHandling.Populate)]
        public bool ContinueRunOnFailedOptionalInput { get; set; }

        [SwaggerExclude]
        [Obsolete]
        [DataMember]
        public NodeCompositionMode NodeCompositionMode { get; set; }

        // naming as tags for wirecontracts and KvTags internally due to ContractConverter limitations
        [DataMember(Name = "tags")]
        public IDictionary<string, string> KvTags { get; set; }

        /// <summary>
        /// User provided tags for steps of pipeline. Each step run in pipeline will get these tags assigned automatically during orchestration.
        /// </summary>
        [DataMember]
        public IDictionary<string, string> StepTags { get; set; }

        [DataMember]
        public IDictionary<string, string> Properties { get; set; }

        /// <summary>
        /// User provided properties for steps of pipeline. Each step run in pipeline will get these properties assigned automatically during orchestration.
        /// </summary>
        [DataMember]
        public IDictionary<string, string> StepProperties { get; set; }

        [SwaggerExclude]
        [DataMember]
        public bool EnableNotification { get; set; }

        [DataMember]
        public List<string> PipelineEndpointIds { get; set; }

        [DataMember]
        public CreatedBy CreatedBy { get; set; }

        [DataMember]
        [UserMutable]
        public CreatedBy LastUpdatedBy { get; set; }

        [DataMember]
        [UserMutable]
        public string LastRunId { get; set; }

        /// <summary>
        /// True to indicate enforce rerun all child runs under this root run, all child runs will not latching/resue to any run and cannot be latched/reused by other runs
        /// False to indicate set RegenerateOutput of all child runs to false and do common reuse logic
        /// Null to indicate not enable this feature, follow existing logic of RegenerateOutput of node
        /// </summary>
        [DataMember]
        public bool? EnforceRerun { get; set; }

        [DataMember]
        public CloudPrioritySetting DefaultCloudPriority { get; set; }

        [DataMember]
        public ComputeSetting DefaultCompute { get; set; }

        [DataMember]
        public DatastoreSetting DefaultDatastore { get; set; }

        [DataMember]
        public IdentitySetting IdentityConfig { get; set; }

        [DataMember]
        public int? PipelineTimeoutSeconds { get; set; }
    }
}
