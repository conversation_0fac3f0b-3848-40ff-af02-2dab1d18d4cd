﻿// <copyright file="PortInfo.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Runtime.Serialization;

namespace Microsoft.Aether.AEVA.DataContracts
{
    [DataContract]
    public class PortInfo
    {
        [DataMember]
        public string NodeId { get; set; }

        [DataMember]
        public string PortName { get; set; }

        // If not none, then this port corresponds to a Graph Output with the given name
        [DataMember]
        public string GraphPortName { get; set; }

        [DataMember]
        public bool IsParameter { get; set; }

        [DataMember]
        public string WebServicePort { get; set; }
    }
}
