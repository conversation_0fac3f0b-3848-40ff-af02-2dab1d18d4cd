﻿// <copyright file="PipelineRunEntity.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Newtonsoft.Json;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;

namespace Microsoft.Aether.AEVA.DataContracts
{
    [DataContract]
    public class PipelineRunEntity : BaseEntity
    {
        public PipelineRunEntity()
        {
            KvTags = new Dictionary<string, string>();
            StepTags = new Dictionary<string, string>();
            Properties = new Dictionary<string, string>();
            StepProperties = new Dictionary<string, string>();
        }

        [UserMutable]
        [DataMember]
        public string Description { get; set; }

        [DataMember]
        public PipelineRunStatus Status { get; set; }

        [DataMember]
        public string GraphId { get; set; }

        [DataMember]
        public bool IsSubmitted { get; set; }

        [DataMember]
        public bool HasErrors { get; set; }

        [DataMember]
        public bool HasWarnings { get; set; }

        [DataMember]
        public UploadState UploadState { get; set; }

        [DataMember]
        public IDictionary<string, string> ParameterAssignments { get; set; }

        [DataMember]
        public IDictionary<string, LegacyDataPath> DataPathAssignments { get; set; }

        [DataMember]
        public IDictionary<string, DataSetDefinitionValue> DataSetDefinitionValueAssignments { get; set; }

        [DataMember]
        public IDictionary<string, AssetOutputSettings> AssetOutputSettingsAssignments { get; set; }

        [DataMember]
        public string RunHistoryExperimentName { get; set; }

        [DataMember]
        [UserMutable]
        public string DisplayName { get; set; }

        /// <summary>
        /// User provided RunId
        /// </summary>
        [DataMember]
        public string PipelineRunId { get; set; }

        [DataMember]
        public string PipelineId { get; set; }

        [DataMember]
        public string PipelineEndpointId { get; set; }

        [DataMember]
        public string RunSource { get; set; }

        [DataMember]
        public RunType RunType { get; set; }

        [DataMember]
        public int TotalRunSteps { get; set; }

        [DataMember]
        public string ScheduleId { get; set; }

        /// <summary>
        /// Represents the run url deep link.
        /// </summary>
        [DataMember]
        public string RunUrl { get; set; }

        [DataMember(Name = "tags")]
        [UserMutable]
        public IDictionary<string, string> KvTags { get; set; }

        /// <summary>
        /// User provided tags for steps of pipeline. Each step run in pipeline will get these tags assigned automatically during orchestration.
        /// </summary>
        [DataMember]
        [UserMutable]
        public IDictionary<string, string> StepTags { get; set; }

        [DataMember]
        [UserMutable]
        public IDictionary<string, string> Properties { get; set; }

        /// <summary>
        /// User provided properties for steps of pipeline. Each step run in pipeline will get these properties assigned automatically during orchestration.
        /// </summary>
        [DataMember]
        [UserMutable]
        public IDictionary<string, string> StepProperties { get; set; }

        [DataMember]
        public CreatedBy CreatedBy { get; set; }

        [DataMember]
        public bool PreserveSubGraphs { get; set; }

        /// <summary>
        /// For nested subgraph runs, this is the run ID of the top-level pipeline run.  For non-nested runs,
        /// this is equal to PipelineRunId
        /// </summary>
        [DataMember]
        public string RootPipelineRunId { get; set; }

        /// <summary>
        /// True to indicate enforce rerun all child runs under this root run, all child runs will not latching/resue to any run and cannot be latched/reused by other runs
        /// False to indicate set RegenerateOutput of all child runs to false and do common reuse logic
        /// Null to indicate not enable this feature, follow existing logic of RegenerateOutput of node
        /// </summary>
        [DataMember]
        public bool? EnforceRerun { get; set; }

        /// <summary>
        /// True to indicate we should continue to run a step if the step has optional input connected to other step's outputs
        /// but the other step failed or skipped
        /// </summary>
        [DataMember]
        [DefaultValue(true)]
        [JsonProperty(DefaultValueHandling = DefaultValueHandling.Populate)]
        public bool ContinueRunOnFailedOptionalInput { get; set; }

        /// <summary>
        /// Alias of pipeline runner
        /// </summary>
        [DataMember]
        public string UserAlias { get; set; }

        [DataMember]
        public CloudPrioritySetting DefaultCloudPriority { get; set; }

        [DataMember]
        public ComputeSetting DefaultCompute { get; set; }

        [DataMember]
        public DatastoreSetting DefaultDatastore { get; set; }

        [DataMember]
        public IdentitySetting IdentityConfig { get; set; }

        [DataMember]
        public int? PipelineTimeoutSeconds { get; set; }

        [DataMember]
        public bool ContinueRunOnStepFailure { get; set; }
    }
}
