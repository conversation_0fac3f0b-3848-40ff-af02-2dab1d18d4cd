﻿// <copyright file="SubGraphVersionCreationInfo.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Runtime.Serialization;

namespace Microsoft.Aether.AEVA.DataContracts
{
    // Creation object for a SubGraph which is part of an AmlModuleVersion
    [DataContract]
    public class SubGraphVersionCreationInfo : SubGraphCreationInfo
    {
        [DataMember]
        public string AmlModuleId { get; set; }

        [DataMember]
        public string Version { get; private set; }

        [DataMember]
        public bool SetAsDefaultVersion { get; set; }
    }
}
