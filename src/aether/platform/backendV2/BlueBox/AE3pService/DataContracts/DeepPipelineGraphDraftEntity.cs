﻿// <copyright file="DeepPipelineGraphDraftEntity.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Collections.Generic;
using System.Runtime.Serialization;

namespace Microsoft.Aether.AEVA.DataContracts
{
    [DataContract]
    public class DeepPipelineGraphDraftEntity
    {
        [DataMember]
        public PipelineDraft PipelineDraft;

        [DataMember]
        public GraphDraftEntity GraphDraftEntity;

        [DataMember]
        public IList<DataSourceEntity> DataSourceEntities;

        [DataMember]
        public IList<Module> Modules;
    }
}
