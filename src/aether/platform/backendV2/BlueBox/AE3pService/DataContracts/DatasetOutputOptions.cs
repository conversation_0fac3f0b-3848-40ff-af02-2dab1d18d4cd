﻿// <copyright file="DatasetOutputOptions.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Newtonsoft.Json;
using System.Runtime.Serialization;

namespace Microsoft.Aether.AEVA.DataContracts
{
    [DataContract]
    public class DatasetOutputOptions
    {
        [DataMember]
        public GlobsOptions SourceGlobs { get; set; }

        [DataMember]
        public string PathOnDatastore { get; set; }

        /// <summary>
        /// Represents the assigned value for PathOnDatastore, has higher priority than PathOnDatastore
        /// </summary>
        [DataMember]
        [JsonProperty(PropertyName = "PathOnDatastoreParameterAssignment", NullValueHandling = NullValueHandling.Ignore)]
        public ParameterAssignment PathOnDatastoreParameterAssignment { get; set; }
    }
}
