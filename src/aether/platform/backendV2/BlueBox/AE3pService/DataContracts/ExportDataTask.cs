﻿// <copyright file="ExportDataTask.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Newtonsoft.Json;
using System.Runtime.Serialization;

namespace Microsoft.Aether.AEVA.DataContracts
{
    [DataContract]
    public class ExportDataTask
    {
        [DataMember]
        [JsonProperty(PropertyName = "DataTransferSink", NullValueHandling = NullValueHandling.Ignore)]
        [ModuleConfig(Description = "Output data transfer source")]
        [NodeConfig(Description = "Output data transfer source")]
        public DataTransferSink DataTransferSink { get; set; }
    }
}
