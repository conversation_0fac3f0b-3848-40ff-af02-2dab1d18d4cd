// <copyright file="RegisteredDataSetReference.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Runtime.Serialization;

namespace Microsoft.Aether.AEVA.DataContracts
{
    /// <summary>
    /// A reference to a registered AzureML dataset.
    /// </summary>
    [DataContract]
    public sealed class RegisteredDataSetReference
    {
        /// <summary>
        /// ID of the registered dataset.
        /// </summary>
        [DataMember]
        public string Id { get; set; }

        /// <summary>
        /// The name of the dataset, can be optional.
        /// </summary>
        [DataMember]
        public string Name { get; set; }

        /// <summary>
        /// The version of the dataset, can be optional.
        /// </summary>
        [DataMember]
        public string Version { get; set; }
    }
}