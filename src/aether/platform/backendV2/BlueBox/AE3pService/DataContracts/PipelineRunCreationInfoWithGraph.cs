﻿// <copyright file="PipelineRunCreationInfoWithGraph.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Runtime.Serialization;

namespace Microsoft.Aether.AEVA.DataContracts
{
    [DataContract]
    public class PipelineRunCreationInfoWithGraph
    {
        [DataMember]
        public PipelineRunCreationInfo CreationInfo { get; set; }

        [DataMember]
        public GraphEntity Graph { get; set; }

        [DataMember]
        public EntityInterface GraphInterface { get; set; }

        [DataMember]
        public GraphLayout GraphLayout { get; set; }
    }
}
