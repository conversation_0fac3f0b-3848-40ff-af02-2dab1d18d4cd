﻿// <copyright file="DataTransferSink.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Newtonsoft.Json;
using System.Runtime.Serialization;

namespace Microsoft.Aether.AEVA.DataContracts
{
    [DataContract]
    public class DatabaseSink
    {
        [DataMember]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        [NodeConfig(SupportLinkSetting = true, UIWidgetType = UIWidgetTypeEnum.ConnectionSelection, IsRequired = true)]
        public string Connection { get; set; }

        [DataMember]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        [NodeConfig(SupportLinkSetting = true, IsRequired = true)]
        public string Table { get; set; }
    }
}
