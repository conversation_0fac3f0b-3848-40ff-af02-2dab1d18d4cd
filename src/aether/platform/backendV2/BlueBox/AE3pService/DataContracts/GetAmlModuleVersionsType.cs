﻿// <copyright file="GetAmlModuleVersionsType.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Runtime.Serialization;

namespace Microsoft.Aether.AEVA.DataContracts
{
    /// <summary>
    /// Parameter of API AzureMLModules/Versions
    /// </summary>
    [DataContract]
    public enum GetAmlModuleVersionsType
    {
        /// <summary>
        /// Return default module version with AzureMLModule
        /// </summary>
        [DataMember]
        Default = 0,

        /// <summary>
        /// Return all module versions with AzureMLModule
        /// </summary>
        [DataMember]
        All = 1,

        /// <summary>
        /// Return resolved module version with AzureMLModule
        /// </summary>
        [DataMember]
        Resolve = 2,
    }
}
