﻿// <copyright file="ParameterAssignment.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Collections.Generic;
using System.Runtime.Serialization;

namespace Microsoft.Aether.AEVA.DataContracts
{
    // ParameterAssignment represents the assigned value for a parameter on a module or subgraph node.
    // The parameter may be set to a specific value or linked to a graph parameter in the parent graph.
    // ParameterAssignment.ValueType controls the type of parameter assignment (literal string value,
    // link to a graph parameter, or dataset/datapath value).
    [DataContract]
    public class ParameterAssignment : NameValuePair
    {
        public ParameterAssignment()
        {
        }

        public ParameterAssignment(string name,
                                  string value,
                                  ParameterValueType valueType = ParameterValueType.Literal)
            : base(name, value)
        {
            ValueType = valueType;
        }

        [DataMember]
        public ParameterValueType ValueType { get; private set; }

        [DataMember]
        public IList<ParameterAssignment> AssignmentsToConcatenate { get; set; }

        // Value of a DataPath parameter (ValueType.DataPath)
        [DataMember]
        public LegacyDataPath DataPathAssignment { get; set; }

        // Value of a DataSetDefinition parameter (ValueType.DataSetDefinition)
        [DataMember]
        public DataSetDefinitionValue DataSetDefinitionValueAssignment { get; set; }

        public override string ToString()
        {
            return $"[NameOptionalValuePair: Name={Name}, Value={Value}, ValueType={ValueType}]";
        }
    }
}
