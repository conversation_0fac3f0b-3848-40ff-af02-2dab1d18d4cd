﻿// <copyright file="StructuredInterfaceInput.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Collections.Generic;
using System.Runtime.Serialization;

namespace Microsoft.Aether.AEVA.DataContracts
{
    [DataContract]
    public class StructuredInterfaceInput
    {
        // Name of the port, also used to set up an environment variable on the execution node
        [DataMember]
        public string Name { get; set; }

        [DataMember]
        public string Label { get; set; }

        [DataMember]
        public List<string> DataTypeIdsList { get; set; }

        [DataMember]
        public bool IsOptional { get; set; }

        [DataMember]
        public string Description { get; set; }

        // If SkipProcessing is true, cloudlets won't processes this output.
        // This will help building sequential modules without actual inputs and outputs.
        [DataMember]
        public bool SkipProcessing { get; set; }

        // If IsResource is true, cloudlets will process it like module resource .
        // This is currently used by scope cloud for input resources
        [DataMember]
        public bool IsResource { get; set; }

        // The valid DataStoreMode for input is None, Mount and Download
        [DataMember]
        public DataStoreMode DataStoreMode { get; set; }

        // For Download mode, specify a local path on the compute to download . Otherwise it can be null.
        [DataMember]
        public string PathOnCompute { get; set; }

        // For Download mode, indicate whether to overwrite existing data
        [DataMember]
        public bool Overwrite { get; set; }

        [DataMember]
        public string DataReferenceName { get; set; }

        /// <summary>
        /// This allows the module to define what type of dataset it can take as input, which is the data type.
        /// The <see cref="DataTypeIdsList"/>allows the module to define the supported semantic type.
        /// </summary>
        [DataMember]
        public ISet<DatasetType> DatasetTypes { get; set; }
    }
}
