﻿// <copyright file="HdiRunConfiguration.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Collections.Generic;
using System.Runtime.Serialization;

namespace Microsoft.Aether.AEVA.DataContracts
{
    /// <summary>
    /// This is the run configuration for HdiCloud
    /// </summary>
    [DataContract]
    public class HdiRunConfiguration
    {
        [DataMember]
        [ModuleConfig(IsRequired = true, Description = "File containing the application to execute")]
        public string File { get; set; }

        [DataMember]
        [ModuleConfig(Description = "Application Java/Spark main class")]
        public string ClassName { get; set; }

        [DataMember]
        [ModuleConfig(Description = "Files to be used in this session")]
        public List<string> Files { get; set; }

        [DataMember]
        [ModuleConfig(Description = "Archives to be used in this session")]
        public List<string> Archives { get; set; }

        [DataMember]
        [ModuleConfig(Description = "Jars to be used in this session")]
        public List<string> Jars { get; set; }

        [DataMember]
        [ModuleConfig(Description = "Python files to be used in this session")]
        public List<string> PyFiles { get; set; }

        [DataMember]
        [NodeConfig(IsRequired = true, Description = "Hdi Compute name that is attached to AML", SupportLinkSetting = true, UIWidgetType = UIWidgetTypeEnum.ComputeSelection)]
        public string ComputeName { get; set; }

        [DataMember]
        [NodeConfig(Description = "The name of the YARN queue to which submitted", SupportLinkSetting = true)]
        public string Queue { get; set; }

        [DataMember]
        [NodeConfig(Description = "Amount of memory to use for the driver process." +
            "It's the same format as JVM memory strings. Use lower-case suffixes, " +
            "e.g. k, m, g, t, and p, for kibi-, mebi-, gibi-, tebi-, and pebibytes, respectively.", SupportLinkSetting = true)]
        public string DriverMemory { get; set; }

        [DataMember]
        [NodeConfig(Description = "Number of cores to use for the driver process", SupportLinkSetting = true)]
        public int? DriverCores { get; set; }

        [DataMember]
        [NodeConfig(Description = "Amount of memory to use per executor process. " +
            "It's the same format as JVM memory strings. Use lower-case suffixes, " +
            "e.g. k, m, g, t, and p, for kibi-, mebi-, gibi-, tebi-, and pebibytes, respectively.", SupportLinkSetting = true)]
        public string ExecutorMemory { get; set; }

        [DataMember]
        [NodeConfig(Description = "Number of cores to use for each executor", SupportLinkSetting = true)]
        public int? ExecutorCores { get; set; }

        [DataMember]
        [NodeConfig(Description = "Number of executors to launch for this session", SupportLinkSetting = true)]
        public int? NumberExecutors { get; set; }

        [DataMember]
        [NodeConfig(Description = "Spark configuration properties")]
        public Dictionary<string, string> Conf { get; set; }

        [DataMember]
        [NodeConfig(Description = "The name of this session", SupportLinkSetting = true)]
        public string Name { get; set; }
    }
}
