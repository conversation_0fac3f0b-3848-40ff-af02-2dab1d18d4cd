﻿// <copyright file="InputNotReadyReason.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Runtime.Serialization;

namespace Microsoft.Aether.AEVA.DataContracts
{
    [DataContract]
    public enum InputNotReadyReason
    {
        [EnumMember]
        None = 0,

        [EnumMember]
        UpstreamNodeFailed = 1,

        [EnumMember]
        UpstreamNodeSkipped = 2,

        [EnumMember]
        InConditionFlowSkippedBranch = 3
    }
}
