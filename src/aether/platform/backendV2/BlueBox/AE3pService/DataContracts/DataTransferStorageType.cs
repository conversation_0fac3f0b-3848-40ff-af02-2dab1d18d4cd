﻿// <copyright file="DataTransferStorageType.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Runtime.Serialization;

namespace Microsoft.Aether.AEVA.DataContracts
{
    [DataContract]
    public enum DataTransferStorageType
    {
        [EnumMember(Value = "database")]
        DataBase,

        [EnumMember(Value = "file_system")]
        FileSystem
    }
}
