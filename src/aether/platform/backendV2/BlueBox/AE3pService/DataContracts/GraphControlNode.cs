﻿// <copyright file="GraphControlNode.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Runtime.Serialization;

namespace Microsoft.Aether.AEVA.DataContracts
{
    [DataContract]
    public enum ControlType
    {
        [EnumMember]
        IfElse,
    }

    [DataContract]
    public class GraphControlNode
    {
        [DataMember]
        public string Id { get; set; }

        [DataMember]
        public ControlType ControlType { get; set; }

        [DataMember]
        public ParameterAssignment ControlParameter { get; set; }

        /// <summary>
        /// Run Attribution, will be recorded as runTypeV2.traits for telemetry usage
        /// </summary>
        [DataMember]
        public string RunAttribution { get; set; }
    }
}
