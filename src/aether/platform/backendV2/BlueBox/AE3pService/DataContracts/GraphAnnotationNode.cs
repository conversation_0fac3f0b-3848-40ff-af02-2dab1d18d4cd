﻿// <copyright file="GraphAnnotationNode.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Collections.Generic;
using System.Runtime.Serialization;

namespace Microsoft.Aether.AEVA.DataContracts
{
    [DataContract]
    public class GraphAnnotationNode
    {
        [DataMember]
        public string Id { get; set; }

        [DataMember]
        [UserMutable]
        public string Content { get; set; }

        [DataMember]
        [UserMutable]
        public List<string> MentionedNodeNames { get; set; }

        [DataMember]
        [UserMutable]
        public string StructuredContent { get; set; }
    }
}