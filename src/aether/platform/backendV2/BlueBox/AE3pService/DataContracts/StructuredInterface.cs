﻿// <copyright file="StructuredInterface.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Collections.Generic;
using System.Runtime.Serialization;

namespace Microsoft.Aether.AEVA.DataContracts
{
    [DataContract]
    public class StructuredInterface
    {
        [DataMember]
        public string CommandLinePattern { get; set; }

        [DataMember]
        public List<StructuredInterfaceInput> Inputs { get; set; }

        [DataMember]
        public List<StructuredInterfaceOutput> Outputs { get; set; }

        [DataMember]
        public List<ControlOutput> ControlOutputs { get; set; }

        [DataMember]
        public List<StructuredInterfaceParameter> Parameters { get; set; }

        [DataMember]
        public List<StructuredInterfaceParameter> MetadataParameters { get; set; }

        // The Arguments list is used to construct a command line for module execution
        [DataMember]
        public List<ArgumentAssignment> Arguments { get; set; }
    }
}
