﻿// <copyright file="GraphLayout.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Collections.Generic;
using System.Runtime.Serialization;

namespace Microsoft.Aether.AEVA.DataContracts
{
    [DataContract]
    public class GraphLayout : BaseNoStatusEntity
    {
        /// <summary>
        /// key is nodeId
        /// </summary>
        [DataMember]
        [UserMutable]
        public IDictionary<string, NodeLayout> NodeLayouts { get; set; }

        [DataMember]
        [UserMutable]
        public string ExtendedData { get; set; }

        [DataMember]
        [UserMutable]
        public IList<GraphAnnotationNode> AnnotationNodes { get; set; }
    }
}
