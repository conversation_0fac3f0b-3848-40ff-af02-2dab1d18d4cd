﻿// <copyright file="ResourceConfiguration.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Collections.Generic;

namespace Microsoft.Aether.AEVA.DataContracts
{
    public class ResourceConfiguration
    {
        /// <summary>
        /// Optional number of instances or nodes used by the compute target.
        /// </summary>
        [NodeConfig(Description = "Optional number of instances or nodes used by the compute target", IsRequired = false, SupportLinkSetting = true)]
        public int? InstanceCount { get; set; }

        /// <summary>
        /// Optional type of VM used as supported by the compute target.
        /// </summary>
        [NodeConfig(Description = "Optional type of VM used as supported by the compute target", IsRequired = false, SupportLinkSetting = true)]
        public string InstanceType { get; set; }

        /// <summary>
        /// Additional properties bag.
        /// </summary>
        [NodeConfig(Description = "Properties", IsRequired = false)]
        public Dictionary<string, object> Properties { get; set; }

        /// <summary>
        /// Locations where a job can run.
        /// </summary>
        [NodeConfig(Description = "Locations where a job can run", IsRequired = false, SupportLinkSetting = false)]
        public List<string> Locations { get; set; }

        /// <summary>
        /// Compute priority to run on.
        /// </summary>
        [NodeConfig(Description = "Compute priority to run on", IsRequired = false, SupportLinkSetting = true)]
        public string InstancePriority { get; set; }

        /// <summary>
        /// ResourceId used for quota enforcement at AML layer, used for AOAI jobs
        /// </summary>
        [NodeConfig(Description = "ResourceId used for quota enforcement at AML layer, used for AOAI jobs", IsRequired = false, SupportLinkSetting = true)]
        public string QuotaEnforcementResourceId { get; set; }
    }
}
