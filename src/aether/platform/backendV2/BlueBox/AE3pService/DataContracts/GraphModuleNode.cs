﻿// <copyright file="GraphModuleNode.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>
using System.Runtime.Serialization;

namespace Microsoft.Aether.AEVA.DataContracts
{
    [DataContract]
    public class GraphModuleNode : GraphExecutableNode
    {
        // TODO: Base on <PERSON>'s comments, we should remove this module type for this class,
        // otherwise we would leak a lot of module metadata into node, and then have to
        // deal with failures when info mis-matches for some reason. We keep it now.
        // Add remove it in our next change, at meantime this property will impact pipeline
        // controller to get predictor module, need modify that part too.
        [DataMember]
        public ModuleType? ModuleType { get; set; }

        /// <summary>
        /// Runconfig definition serialized as J<PERSON><PERSON>.  If specified, this overrides the runconfig definition
        /// from the module entity.
        /// </summary>
        [DataMember]
        public string Runconfig { get; set; }

        public GraphModuleNode ShallowCopy()
        {
            return (GraphModuleNode)MemberwiseClone();
        }
    }
}
