﻿// <copyright file="ScopeCloudConfiguration.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Newtonsoft.Json;
using System.Collections.Generic;
using System.Runtime.Serialization;

namespace Microsoft.Aether.AEVA.DataContracts
{
    [DataContract]
    public class ScopeCloudConfiguration
    {
        [DataMember]
        public IDictionary<string, ArgumentAssignment> InputPathSuffixes { get; set; }

        [DataMember]
        public IDictionary<string, ArgumentAssignment> OutputPathSuffixes { get; set; }

        [DataMember]
        public string UserAlias { get; set; }

        [DataMember]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        [NodeConfig(Description = "Specify job's exact number of token allocation", SupportLinkSetting = true)]
        public int? Tokens { get; set; }

        [DataMember]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        [NodeConfig(Description = "Specify job's maximum token allocation", SupportLinkSetting = true)]
        public int? AutoToken { get; set; }

        [DataMember]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        [NodeConfig(Description = "Job's standard VC percent allocation", SupportLinkSetting = true)]
        public float? Vcp { get; set; }
    }
}
