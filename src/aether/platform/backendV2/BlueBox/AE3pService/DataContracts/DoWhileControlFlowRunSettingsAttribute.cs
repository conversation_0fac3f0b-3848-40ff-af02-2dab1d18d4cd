﻿// <copyright file="DoWhileControlFlowRunSettingsAttribute.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;

namespace Microsoft.Aether.AEVA.DataContracts
{
    [AttributeUsage(AttributeTargets.Property, Inherited = false, AllowMultiple = false)]
    public class DoWhileControlFlowRunSettingsAttribute : Attribute
    {
        public string DefaultValue { get; set; }

        public string MaxValue { get; set; }
    }
}
