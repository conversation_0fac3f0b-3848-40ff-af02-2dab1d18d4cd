﻿// <copyright file="ModuleCreationInfo.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Collections.Generic;
using System.Runtime.Serialization;

namespace Microsoft.Aether.AEVA.DataContracts
{
    [DataContract]
    public class ModuleCreationInfo
    {
        public ModuleCreationInfo()
        {
            KvTags = new Dictionary<string, string>();
            Properties = new Dictionary<string, string>();
        }

        public ModuleCreationInfo(
            string name,
            string displayName,
            string description,
            bool isDeterministic,
            string moduleExecutionType,
            string hash,
            string identifierHash,
            StructuredInterface structuredInterface = null,
            AE365Properties ae365 = null,
            IDictionary<string, string> kvTags = null,
            IDictionary<string, string> properties = null,
            ModuleType? moduleType = null,
            string storageId = null,
            string category = null,
            string runConfig = null,
            string stepType = null,
            CloudSettings cloudSettings = null,
            string moduleTypeVersion = null)
            : this()
        {
            Name = name;
            DisplayName = displayName;
            Description = description;
            IsDeterministic = isDeterministic;
            ModuleExecutionType = moduleExecutionType;
            Hash = hash;
            IdentifierHash = identifierHash;
            StructuredInterface = structuredInterface;
            AE365 = ae365;
            KvTags = kvTags;
            Properties = properties;
            ModuleType = moduleType;
            ModuleTypeVersion = moduleTypeVersion;
            StorageId = storageId;
            Category = category;
            Runconfig = runConfig;
            StepType = stepType;
            CloudSettings = cloudSettings;
        }

        [DataMember]
        public string Name { get; set; }

        [DataMember]
        public string DisplayName { get; set; }

        // content hash calculated based on file content of the module,
        // if set, cloudlet resource downloader will verify downloaded content has same hash
        [DataMember]
        public string Hash { get; private set; }

        [DataMember]
        public string Description { get; private set; }

        [DataMember]
        public bool IsDeterministic { get; private set; }

        [DataMember]
        public string ModuleExecutionType { get; private set; }

        [DataMember]
        public ModuleType? ModuleType { get; private set; }

        /// <summary>
        /// This is used for create snapshot of ModuleType based on version.
        /// </summary>
        [DataMember]
        public string ModuleTypeVersion { get; private set; }

        // this is a hash calculated over all possible parameters that could uniquely identify a module's content
        // such as command line, input/output, isdeterministic, file content etc.
        [DataMember]
        public string IdentifierHash { get; private set; }

        [DataMember]
        public StructuredInterface StructuredInterface { get; private set; }

        [DataMember]
        public AE365Properties AE365 { get; private set; }

        // TODO: deprecate and rename to SnapshotId, because it is actually snapshotid. Tracking with https://msdata.visualstudio.com/Vienna/_workitems/edit/431222
        [DataMember]
        public string StorageId { get; private set; }

        [DataMember]
        public bool IsInterfaceOnly { get; private set; }

        [DataMember]
        public IDictionary<string, string> KvTags { get; set; }

        [DataMember]
        public IDictionary<string, string> Properties { get; set; }

        [DataMember]
        public string Category { get; set; }

        /// <summary>
        /// Runconfig definition serialized as JSON.  This is a default runconfig to apply to instances of the module,
        /// which can be overridden on a per-node basis
        /// </summary>
        [DataMember]
        public string Runconfig { get; set; }

        /// <summary>
        /// Gets or Sets CloudSettings. CloudSettings is the cloud specific settings
        /// </summary>
        [DataMember]
        public CloudSettings CloudSettings { get; set; }

        /// <summary>
        /// Type of step that generated this module (e.g. PythonScriptStep, ModuleStep, etc.)
        /// </summary>
        [DataMember]
        public string StepType { get; set; }
    }
}
