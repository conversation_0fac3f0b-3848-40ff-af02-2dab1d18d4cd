﻿// <copyright file="EntityWithContinuationToken.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

namespace Microsoft.Aether.AEVA.DataContracts
{
    public class EntityWithContinuationToken<T>
    {
        public EntityWithContinuationToken(T entity, string continuationToken)
        {
            Entity = entity;
            ContinuationToken = continuationToken;
        }

        public T Entity { get; }

        public string ContinuationToken { get; }
    }
}
