﻿// <copyright file="EntityInterface.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Newtonsoft.Json;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;

namespace Microsoft.Aether.AEVA.DataContracts
{
    [DataContract]
    public class EntityInterface
    {
        public EntityInterface()
        {
        }

        public EntityInterface(
            NodePortInterface ports,
            IList<Parameter> parameters,
            IList<Parameter> metadata = null,
            IList<DataPathParameter> dataPathParameters = null,
            IList<DataSetPathParameter> dataPathParameterList = null,
            IList<AssetOutputSettingsParameter> assetOutputSettingsParameterList = null)
        {
            Ports = ports;
            Parameters = parameters?.ToArray();
            MetadataParameters = metadata;
            DataPathParameters = dataPathParameters;
            DataPathParameterList = dataPathParameterList;
            AssetOutputSettingsParameterList = assetOutputSettingsParameterList;
        }

        [DataMember]
        public IList<Parameter> Parameters { get; set; }

        [DataMember]
        public NodePortInterface Ports { get; set; }

        [DataMember]
        public IList<Parameter> MetadataParameters { get; set; }

        /// <summary>
        /// Old input parameterization. It will be deprecated eventually.
        /// </summary>
        [DataMember]
        public IList<DataPathParameter> DataPathParameters { get; set; }

        /// <summary>
        /// A new input parameterization approach which defines paths in a way compatible with the data sets service.
        /// </summary>
        [DataMember]
        public IList<DataSetPathParameter> DataPathParameterList { get; set; }

        /// <summary>
        /// The parameterization approach which defines AssetOutputSettings.
        /// </summary>
        [DataMember]
        [JsonProperty(PropertyName = "AssetOutputSettingsParameterList", NullValueHandling = NullValueHandling.Ignore)]
        public IList<AssetOutputSettingsParameter> AssetOutputSettingsParameterList { get; set; }
    }
}
