﻿// <copyright file="SavedDataSetReference.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Runtime.Serialization;

namespace Microsoft.Aether.AEVA.DataContracts
{
    /// <summary>
    /// A reference to a saved AzureML Dataset.
    /// </summary>
    [DataContract]
    public class SavedDataSetReference
    {
        /// <summary>
        /// The ID of the saved dataset.
        /// </summary>
        [DataMember]
        public string Id { get; set; }
    }
}
