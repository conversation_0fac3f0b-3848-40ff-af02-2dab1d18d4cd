﻿// <copyright file="ExceptionDetailMessage.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Runtime.Serialization;

namespace Microsoft.Aether.AEVA.DataContracts
{
    [DataContract]
    public class ExceptionDetailMessage
    {
        [DataMember]
        public string Message
        {
            get;
            set;
        }

        [DataMember]
        public string Details
        {
            get; set;
        }

        public static ExceptionDetailMessage FromException(Exception exception)
        {
            return new ExceptionDetailMessage
            {
                Message = exception.Message,
                Details = exception.ToString(),
            };
        }
    }
}
