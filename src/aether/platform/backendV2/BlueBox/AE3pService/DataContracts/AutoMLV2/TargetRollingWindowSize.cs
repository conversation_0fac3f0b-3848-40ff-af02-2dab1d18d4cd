﻿// <copyright file="TargetRollingWindowSize.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Runtime.Serialization;

namespace Microsoft.Aether.AEVA.DataContracts
{
    /// <summary>
    /// Target rolling windows size mode.
    /// </summary>
    public enum TargetRollingWindowSizeMode
    {
        /// <summary>
        /// Determine rolling windows size automatically.
        /// </summary>
        [EnumMember(Value = "auto")]
        Auto = 0,

        /// <summary>
        /// Use the specified rolling window size.
        /// </summary>
        [EnumMember(Value = "custom")]
        Custom = 1
    }

    /// <summary>
    /// Forecasting target rolling window size.
    /// </summary>
    public class TargetRollingWindowSize
    {
        /// <summary>
        /// Target rolling window size mode.
        /// </summary>
        [NodeConfig(Description = "Target rolling window size mode", IsRequired = false)]
        [ModuleConfig(Description = "Target rolling window size mode", IsRequired = false)]
        public TargetRollingWindowSizeMode Mode { get; set; }

        /// <summary>
        /// Target rolling window size value.
        /// </summary>
        [NodeConfig(Description = "Target rolling window size value", IsRequired = false)]
        [ModuleConfig(Description = "Target rolling window size value", IsRequired = false)]
        public int Value { get; set; }
    }
}
