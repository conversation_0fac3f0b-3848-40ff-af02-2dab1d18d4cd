﻿// <copyright file="ComputeConfiguration.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Collections.Generic;

namespace Microsoft.Aether.AEVA.DataContracts
{
    /// <summary>
    /// Compute binding definition.
    /// </summary>
    public class ComputeConfiguration
    {
        /// <summary>
        /// ARM resource ID of the Compute you are targeting. If not provided the resource will be deployed as Managed.
        /// </summary>
        [NodeConfig(Description = "Compute target", IsRequired = false)]
        public string Target { get; set; }

        /// <summary>
        /// Number of instances or nodes.
        /// </summary>
        [NodeConfig(Description = "No.of Instances", IsRequired = false)]
        public int? InstanceCount { get; set; }

        /// <summary>
        /// Set to true for jobs running on local compute.
        /// </summary>
        [NodeConfig(Description = "Is local compute", IsRequired = false)]
        public bool IsLocal { get; set; } = false;

        /// <summary>
        /// Location for virtual cluster run.
        /// </summary>
        [NodeConfig(Description = "Location", IsRequired = false)]
        public string Location { get; set; }

        /// <summary>
        /// Set to true for jobs wanting ES to auto-detect compute usage (target is null except for PipelineJob case).
        /// </summary>
        [NodeConfig(Description = "Auto-detect compute", IsRequired = false)]
        public bool IsClusterless { get; set; } = false;

        /// <summary>
        /// SKU type to run on.
        /// </summary>
        [NodeConfig(Description = "Instance type", IsRequired = false)]
        public string InstanceType { get; set; }

        /// <summary>
        /// Additional properties.
        /// </summary>
        [NodeConfig(Description = "Properties", IsRequired = false)]
        public Dictionary<string, object> Properties { get; set; }

        /// <summary>
        /// Indicates whether this job can be preempted.
        /// </summary>
        [NodeConfig(Description = "Is preemptable", IsRequired = false)]
        public bool IsPreemptable { get; set; }
    }
}
