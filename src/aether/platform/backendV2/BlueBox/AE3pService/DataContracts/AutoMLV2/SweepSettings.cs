// <copyright file="SweepSettings.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Collections.Generic;
using System.Runtime.Serialization;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;

namespace Microsoft.Aether.AEVA.DataContracts
{
    /// <summary>
    /// Hyperparameter settings on AutoML job.
    /// </summary>
    public class SweepSettings
    {
        /// <summary>
        /// Limits for sweeping over the search space.
        /// </summary>
        [NodeConfig(Description = "Limits for sweeping over the search space", IsRequired = false)]
        [ModuleConfig(Description = "Limits for sweeping over the search space", IsRequired = false)]
        public SweepSettingsLimits Limits { get; set; }

        /// <summary>
        /// A list of dictionaries containing each parameter and its distribution. The dictionary key is the name of the parameter
        /// </summary>
        [NodeConfig(Description = "List of dictionaries containing each parameter and its distribution", IsRequired = false)]
        [ModuleConfig(Description = "List of dictionaries containing each parameter and its distribution", IsRequired = false)]
        public IList<Dictionary<string, string>> SearchSpace { get; set; }

        /// <summary>
        /// Type of the hyperparameter sampling algorithms
        /// </summary>
        [NodeConfig(Description = "Type of the hyperparameter sampling algorithms", IsRequired = false, SupportLinkSetting = true)]
        [ModuleConfig(Description = "Type of the hyperparameter sampling algorithms", IsRequired = false)]
        public SamplingAlgorithmType SamplingAlgorithm { get; set; }

        /// <summary>
        /// Type of early termination policy
        /// </summary>
        [NodeConfig(Description = "Type of early termination policy", IsRequired = false)]
        [ModuleConfig(Description = "Type of early termination policy", IsRequired = false)]
        public SweepEarlyTerminationPolicy EarlyTermination { get; set; }
    }

    /// <summary>
    /// Added here as a separate class to align with the SweepJob design.
    /// </summary>
    public class SweepSettingsLimits
    {
        /// <summary>
        /// Number of iterations.
        /// </summary>
        [NodeConfig(Description = "Number of iterations", IsRequired = false, SupportLinkSetting = true)]
        [ModuleConfig(Description = "Number of iterations", IsRequired = false)]
        public int? MaxTotalTrials { get; set; }

        /// <summary>
        /// Maximum Concurrent iterations
        /// </summary>
        [NodeConfig(Description = "Represents the maximum number of iterations that would be executed in parallel. The default value is the same as the number of iterations provided.", IsRequired = false, SupportLinkSetting = true)]
        [ModuleConfig(Description = "Represents the maximum number of iterations that would be executed in parallel. The default value is the same as the number of iterations provided.", IsRequired = false)]
        public int? MaxConcurrentTrials { get; set; }
    }

    /// <summary>
    /// Sampling Algorithm type.
    /// </summary>
    [JsonConverter(typeof(StringEnumConverter))]
    public enum SamplingAlgorithmType
    {
        [EnumMember(Value = "random")]
        Random,
        [EnumMember(Value = "grid")]
        Grid,
        [EnumMember(Value = "bayesian")]
        Bayesian
    }
}
