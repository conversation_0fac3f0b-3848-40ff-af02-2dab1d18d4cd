﻿// <copyright file="FeaturizationSettings.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Collections.Generic;
using Microsoft.AIPlatform.Telemetry.Contracts.Logging.PrivacyAware;
using Newtonsoft.Json.Linq;

namespace Microsoft.Aether.AEVA.DataContracts
{
    /// <summary>
    /// Feturization mode
    /// </summary>
    public enum FeaturizationMode
    {
        /// <summary>
        /// Auto mode
        /// </summary>
        Auto,

        /// <summary>
        /// Custom featurization
        /// </summary>
        Custom,

        /// <summary>
        /// Featurization off.
        /// </summary>
        Off
    }

    /// <summary>
    /// Transformer parameters.
    /// </summary>
    public class ColumnTransformer
    {
        /// <summary>
        /// Fields to apply transformer logic on.
        /// </summary>
        [ModuleConfig(Description = "Fields", IsRequired = false)]
        [NodeConfig(Description = "Fields", IsRequired = false)]
        public IList<string> Fields { get; set; }

        /// <summary>
        /// Different properties to be set in transformer constructor. This is left as JObject intentionally
        /// so as to have extensible behavior without having to update the API or create new API for new
        /// parameters added.
        /// </summary>
        [ModuleConfig(Description = "Parameters", IsRequired = false)]
        [NodeConfig(Description = "Parameters", IsRequired = false)]
        public JObject Parameters { get; set; }
    }

    /// <summary>
    /// Featurization Configuration
    /// </summary>
    public class FeaturizationSettings
    {
        /// <summary>
        /// Featurization mode
        /// </summary>
        [ModuleConfig(Description = "Featurization mode", IsRequired = false)]
        [NodeConfig(Description = "Featurization mode", IsRequired = false, SupportLinkSetting = true)]
        public FeaturizationMode Mode { get; set; }

        /// <summary>
        ///  Blocked transformers
        /// </summary>
        [ModuleConfig(Description = "Blocked transformers", IsRequired = false)]
        [NodeConfig(Description = "Blocked transformers", IsRequired = false, SupportLinkSetting = true)]
        [PrivacyClassification(PrivacyType.SystemMetadata)]
        public IList<string> BlockedTransformers { get; set; }

        /// <summary>
        /// Column purposes
        /// </summary>
        [ModuleConfig(Description = "Column purposes", IsRequired = false)]
        [NodeConfig(Description = "Column purposes", IsRequired = false)]
        [PrivacyClassification(PrivacyType.CustomerContent)]
        public Dictionary<string, string> ColumnPurposes { get; set; }

        /// <summary>
        /// Drop Columns
        /// </summary>
        [PrivacyClassification(PrivacyType.CustomerContent)]
        [NodeConfig(Description = "Drop Columns", IsRequired = false, SupportLinkSetting = true)]
        [ModuleConfig(Description = "Drop Columns", IsRequired = false)]
        public IList<string> DropColumns { get; set; }

        /// <summary>
        /// Transformer Params
        /// </summary>
        [PrivacyClassification(PrivacyType.CustomerContent)]
        [ModuleConfig(Description = "Transformer Params", IsRequired = false)]
        [NodeConfig(Description = "Transformer Params", IsRequired = false)]
        public Dictionary<string, IList<ColumnTransformer>> TransformerParams { get; set; }

        /// <summary>
        /// Dataset language.
        /// </summary>
        [ModuleConfig(Description = "Dataset language", IsRequired = false)]
        [NodeConfig(Description = "Dataset language", IsRequired = false, SupportLinkSetting = true)]
        public string DatasetLanguage { get; set; }

        /// <summary>
        /// Enable Dnn featurization.
        /// </summary>
        [ModuleConfig(Description = "Enable Dnn featurization", IsRequired = false)]
        [NodeConfig(Description = "Enable Dnn featurization", IsRequired = false, SupportLinkSetting = true)]
        public bool? EnableDnnFeaturization { get; set; }
    }
}
