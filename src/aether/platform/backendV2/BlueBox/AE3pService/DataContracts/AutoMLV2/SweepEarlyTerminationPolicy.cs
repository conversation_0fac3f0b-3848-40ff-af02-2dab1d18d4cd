// <copyright file="SweepEarlyTerminationPolicy.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using System.Runtime.Serialization;

namespace Microsoft.Aether.AEVA.DataContracts
{
    /// <summary>
    /// Early termination policies enable canceling poor-performing runs before they complete
    /// </summary>
    public class SweepEarlyTerminationPolicy
    {
        /// <summary>
        /// Name of policy configuration
        /// </summary>
        [NodeConfig(Description = "Name of policy configuration", IsRequired = false, SupportLinkSetting = true)]
        [ModuleConfig(Description = "Name of policy configuration", IsRequired = false)]
        public EarlyTerminationPolicyType PolicyType { get; set; }

        /// <summary>
        /// Interval (number of runs) between policy evaluations.
        /// </summary>
        [NodeConfig(Description = "Interval (number of runs) between policy evaluations.", IsRequired = false, SupportLinkSetting = true)]
        [ModuleConfig(Description = "Interval (number of runs) between policy evaluations.", IsRequired = false)]
        public int? EvaluationInterval { get; set; }

        /// <summary>
        /// Number of intervals by which to delay the first evaluation.
        /// </summary>
        [NodeConfig(Description = "Number of intervals by which to delay the first evaluation.", IsRequired = false, SupportLinkSetting = true)]
        [ModuleConfig(Description = "Number of intervals by which to delay the first evaluation.", IsRequired = false)]
        public int? DelayEvaluation { get; set; }

        /// <summary>
        /// Ratio of the allowed distance from the best performing run.
        /// </summary>
        [NodeConfig(Description = "Ratio of the allowed distance from the best performing run.", IsRequired = false, SupportLinkSetting = true)]
        [ModuleConfig(Description = "Ratio of the allowed distance from the best performing run.", IsRequired = false)]
        public float? SlackFactor { get; set; }

        /// <summary>
        /// Absolute distance allowed from the best performing run.
        /// </summary>
        [NodeConfig(Description = "Absolute distance allowed from the best performing run.", IsRequired = false, SupportLinkSetting = true)]
        [ModuleConfig(Description = "Absolute distance allowed from the best performing run.e", IsRequired = false)]
        public float? SlackAmount { get; set; }

        /// <summary>
        /// The percentage of runs to cancel at each evaluation interval.
        /// </summary>
        [NodeConfig(Description = "The percentage of runs to cancel at each evaluation interval.", IsRequired = false, SupportLinkSetting = true)]
        [ModuleConfig(Description = "The percentage of runs to cancel at each evaluation interval.", IsRequired = false)]
        public int? TruncationPercentage { get; set; }
    }

    /// <summary>
    /// Early termination policy
    /// </summary>
    [JsonConverter(typeof(StringEnumConverter))]
    public enum EarlyTerminationPolicyType
    {
        [EnumMember(Value = "bandit")]
        Bandit,
        [EnumMember(Value = "median_stopping")]
        MedianStopping,
        [EnumMember(Value = "truncation_selection")]
        TruncationSelection
    }
}