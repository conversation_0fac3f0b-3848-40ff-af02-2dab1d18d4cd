﻿// <copyright file="LogVerbosity.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Runtime.Serialization;

namespace Microsoft.Aether.AEVA.DataContracts
{
    /// <summary>
    /// Log verbosity
    /// </summary>
    public enum LogVerbosity
    {
        /// <summary>
        /// Not set.
        /// </summary>
        [EnumMember(Value = "not_set")]
        NotSet,

        /// <summary>
        /// Debug.
        /// </summary>
        [EnumMember(Value = "debug")]
        Debug,

        /// <summary>
        /// Info.
        /// </summary>
        [EnumMember(Value = "info")]
        Info,

        /// <summary>
        /// Warning.
        /// </summary>
        [EnumMember(Value = "warning")]
        Warning,

        /// <summary>
        /// Error.
        /// </summary>
        [EnumMember(Value = "error")]
        Error,

        /// <summary>
        /// Critical.
        /// </summary>
        [EnumMember(Value = "critical")]
        Critical,
    }
}
