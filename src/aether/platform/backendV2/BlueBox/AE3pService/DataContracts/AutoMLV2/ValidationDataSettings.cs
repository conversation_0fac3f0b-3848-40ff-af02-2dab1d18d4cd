﻿// <copyright file="ValidationDataSettings.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Collections.Generic;

namespace Microsoft.Aether.AEVA.DataContracts
{
    /// <summary>
    /// Validation settings for AutoML Experiment
    /// </summary>
    public class ValidationDataSettings
    {
        /// <summary>
        /// Number of cross validation folds to be applied on training dataset
        /// when validation dataset is not provided.
        /// </summary>
        [NodeConfig(Description = "Number of cross validation folds", IsRequired = false)]
        [ModuleConfig(Description = "Number of cross validation folds", IsRequired = false)]
        public NCrossValidations NCrossValidations { get; set; }

        /// <summary>
        /// The fraction of training dataset that needs to be set aside for validation purpose.
        /// Values between (0.0 , 1.0)
        /// Applied when validation dataset is not provided.
        /// </summary>
        [NodeConfig(Description = "The fraction of training dataset", IsRequired = false, SupportLinkSetting = true)]
        [ModuleConfig(Description = "The fraction of training dataset", IsRequired = false)]
        public double? ValidationDataSize { get; set; }

        /// <summary>
        /// Columns to use for CVSPlit data.
        /// </summary>
        [NodeConfig(Description = "Columns to use for CVSPlit data", IsRequired = false, SupportLinkSetting = true)]
        [ModuleConfig(Description = "Columns to use for CVSPlit data", IsRequired = false)]
        public IList<string> CVSplitColumnNames { get; set; }

        /// <summary>
        /// Validation type
        /// </summary>
        [NodeConfig(Description = "Validation type", IsRequired = false)]
        public string ValidationType { get; set; }
    }
}