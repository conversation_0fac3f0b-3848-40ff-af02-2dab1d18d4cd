﻿// <copyright file="Seasonality.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Runtime.Serialization;

namespace Microsoft.Aether.AEVA.DataContracts
{
    /// <summary>
    /// Seasonality mode.
    /// </summary>
    public enum SeasonalityMode
    {
        /// <summary>
        /// Seasonailty to be determined automatically.
        /// </summary>
        [EnumMember(Value = "auto")] 
        Auto = 0,

        /// <summary>
        /// Use the custom seasonality value.
        /// </summary>
        [EnumMember(Value = "custom")]
        Custom = 1
    }

    /// <summary>
    /// Forecasting seasonality.
    /// </summary>
    public class Seasonality
    {
        /// <summary>
        /// Seasonality mode.
        /// </summary>
        [NodeConfig(Description = "Seasonality mode", IsRequired = false)]
        [ModuleConfig(Description = "Seasonality mode", IsRequired = false)]
        public SeasonalityMode Mode { get; set; }

        /// <summary>
        /// Seasonality value.
        /// </summary>
        [NodeConfig(Description = "Seasonality value", IsRequired = false)]
        [ModuleConfig(Description = "Seasonality value", IsRequired = false)]
        public int Value { get; set; }
    }
}
