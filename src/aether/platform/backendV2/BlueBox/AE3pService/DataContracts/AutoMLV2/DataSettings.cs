﻿// <copyright file="DataSettings.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

namespace Microsoft.Aether.AEVA.DataContracts
{
    /// <summary>
    /// This class represents the Dataset Json that is passed into <PERSON> for training.
    /// </summary>
    public sealed class DataSettings
    {
        /// <summary>
        /// Label column name.
        /// </summary>
        [NodeConfig(Description = "Label column name", IsRequired = true, SupportLinkSetting = true)]
        [ModuleConfig(Description = "Label column name", IsRequired = true)]
        public string TargetColumnName { get; set; }

        /// <summary>
        /// Weight column name.
        /// </summary>
        [ModuleConfig(Description = "Weight column name", IsRequired = false)]
        [NodeConfig(Description = "Weight column name", IsRequired = false, SupportLinkSetting = true)]
        public string WeightColumnName { get; set; }

        /// <summary>
        /// Positive Label for binary metrics calculation.
        /// </summary>
        [ModuleConfig(Description = "Positive label", IsRequired = false)]
        [NodeConfig(Description = "Positive label", IsRequired = false)]
        public string PositiveLabel { get; set; }

        /// <summary>
        /// Validation data settings.
        /// </summary>
        [NodeConfig(Description = "Validation data settings", IsRequired = false)]
        [ModuleConfig(Description = "Validation data settings", IsRequired = false)]
        public ValidationDataSettings ValidationData { get; set; }

        /// <summary>
        /// Test data.
        /// </summary>
        [NodeConfig(Description = "Test data settings", IsRequired = false)]
        [ModuleConfig(Description = "Test data settings", IsRequired = false)]
        public TestDataSettings TestData { get; set; }
    }
}
