﻿// <copyright file="StackEnsembleSettings.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Newtonsoft.Json.Linq;
using System.Runtime.Serialization;

namespace Microsoft.Aether.AEVA.DataContracts
{
    /// <summary>
    /// The meta-learner is a model trained on the output of the individual heterogeneous models.
    /// Default meta-learners are LogisticRegression for classification tasks (or LogisticRegressionCV if cross-validation is enabled) and ElasticNet for regression/forecasting tasks (or ElasticNetCV if cross-validation is enabled).
    /// This parameter can be one of the following strings: LogisticRegression, LogisticRegressionCV, LightGBMClassifier, ElasticNet, ElasticNetCV, LightGBMRegressor, or LinearRegression
    /// </summary>
    public enum StackMetaLearnerType
    {
        /// <summary>
        /// None
        /// </summary>
        [EnumMember(Value = "none")]
        None = 0,

        /// <summary>
        /// Default meta-learners are LogisticRegression for classification tasks.
        /// </summary>
        [EnumMember(Value = "logistic_regression")]
        LogisticRegression = 1,

        /// <summary>
        /// Default meta-learners are LogisticRegression for classification task when CV is on.
        /// </summary>
        [EnumMember(Value = "logistic_regression_cv")]
        LogisticRegressionCV = 2,

        /// <summary>
        /// LightGBMClassifier.
        /// </summary>
        [EnumMember(Value = "light_gbm_classifier")]
        LightGBMClassifier = 3,

        /// <summary>
        /// Default meta-learners are LogisticRegression for regression task.
        /// </summary>
        [EnumMember(Value = "elastic_net")]
        ElasticNet = 4,

        /// <summary>
        /// Default meta-learners are LogisticRegression for regression task when CV is on.
        /// </summary>
        [EnumMember(Value = "elastic_net_cv")]
        ElasticNetCV = 5,

        /// <summary>
        /// LightGBMRegressor
        /// </summary>
        [EnumMember(Value = "light_gbm_regressor")]
        LightGBMRegressor = 6,

        /// <summary>
        /// LinearRegression
        /// </summary>
        [EnumMember(Value = "linear_regression")]
        LinearRegression = 7
    }

    /// <summary>
    /// StackEnsembleSettings
    /// </summary>
    public class StackEnsembleSettings
    {
        /// <summary>
        /// The meta-learner is a model trained on the output of the individual heterogeneous models.
        /// </summary>
        [ModuleConfig(Description = "The meta-learner is a model trained on the output of the individual heterogeneous models", IsRequired = false)]
        [NodeConfig(Description = "The meta-learner is a model trained on the output of the individual heterogeneous modelst", IsRequired = false, SupportLinkSetting = true)]
        public StackMetaLearnerType? StackMetaLearnerType { get; set; }

        /// <summary>
        /// Specifies the proportion of the training set (when choosing train and validation type of training) to be reserved for training the meta-learner. Default value is 0.2
        /// </summary>
        [ModuleConfig(Description = "Specifies the proportion of the training set", IsRequired = false)]
        [NodeConfig(Description = "Specifies the proportion of the training set", IsRequired = false, SupportLinkSetting = true)]
        public double? StackMetaLearnerTrainPercentage { get; set; }

        /// <summary>
        /// Optional parameters to pass to the initializer of the meta-learner.
        /// These parameters and parameter types mirror the parameters and parameter types from the corresponding model constructor, and are forwarded to the model constructor.
        /// This is left as Dictionary intentionally so as to have extensible behavior without having to update the API or create
        /// new API for new parameters added.
        /// </summary>
        [ModuleConfig(Description = "Optional parameters to pass to the initializer of the meta-learner", IsRequired = false)]
        [NodeConfig(Description = "Optional parameters to pass to the initializer of the meta-learner", IsRequired = false)]
        public JObject StackMetaLearnerKWargs { get; set; }
    }
}
