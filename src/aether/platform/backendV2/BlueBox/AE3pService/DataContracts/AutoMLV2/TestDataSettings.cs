﻿// <copyright file="TestDataSettings.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

namespace Microsoft.Aether.AEVA.DataContracts
{
    /// <summary>
    /// Test data settings.
    /// </summary>
    public class TestDataSettings
    {
        /// <summary>
        /// The fraction of test dataset that needs to be set aside for validation purpose.
        /// Values between (0.0 , 1.0)
        /// Applied when validation dataset is not provided.
        /// </summary>
        [NodeConfig(Description = "The fraction of test dataset that needs to be set aside for validation purpose", IsRequired = false, SupportLinkSetting = true)]
        [ModuleConfig(Description = "The fraction of test dataset that needs to be set aside for validation purpose", IsRequired = false)]
        public double? TestDataSize { get; set; }
    }
}
