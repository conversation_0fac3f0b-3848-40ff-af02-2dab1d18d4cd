﻿// <copyright file="TrainingSettings.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

namespace Microsoft.Aether.AEVA.DataContracts
{
    /// <summary>
    /// The enumeration with allowed training modes.
    /// </summary>
    public enum TabularTrainingMode
    {
        /// <summary>
        /// Distributed training mode.
        /// </summary>
        [EnumMember(Value = "distributed")]
        Distributed,

        /// <summary>
        /// Disables distributed training.
        /// </summary>
        [EnumMember(Value = "non_distributed")]
        NonDistributed,

        /// <summary>
        /// Currently, it is same as non_distributed. In future, this might change.
        /// </summary>
        [EnumMember(Value = "auto")]
        Auto
    }

    /// <summary>
    /// Training related configuration.
    /// </summary>
    public class TrainingSettings
    {
        /// <summary>
        /// List of Algorithms/Models to be blocked for training.
        /// </summary>
        [ModuleConfig(Description = "List of Algorithms/Models to be blocked for training", IsRequired = false)]
        [NodeConfig(Description = "List of Algorithms/Models to be blocked for training", IsRequired = false, SupportLinkSetting = true)]
        public IList<string> BlockListModels { get; set; }

        /// <summary>
        /// List of Algorithms/Models to be Allowed for training.
        /// </summary>
        [ModuleConfig(Description = "List of Algorithms/Models to be Allowed for training", IsRequired = false)]
        [NodeConfig(Description = "List of Algorithms/Models to be Allowed for training", IsRequired = false, SupportLinkSetting = true)]
        public IList<string> AllowListModels { get; set; }

        /// <summary>
        /// Enable recommendation of DNN models.
        /// </summary>
        [ModuleConfig(Description = "Enable recommendation of DNN models", IsRequired = false)]
        [NodeConfig(Description = "Enable recommendation of DNN models", IsRequired = false, SupportLinkSetting = true)]
        public bool? EnableDnnTraining { get; set; }

        /// <summary>
        /// Flag for enabling onnx compatible models.
        /// </summary>
        [ModuleConfig(Description = "Flag for enabling onnx compatible models", IsRequired = false)]
        [NodeConfig(Description = "Flag for enabling onnx compatible models", IsRequired = false, SupportLinkSetting = true)]
        public bool? EnableOnnxCompatibleModels { get; set; }

        /// <summary>
        /// Stack ensemble settings.
        /// </summary>
        [ModuleConfig(Description = "Stack ensemble settings", IsRequired = false)]
        [NodeConfig(Description = "Stack ensemble settings", IsRequired = false)]
        public StackEnsembleSettings StackEnsembleSettings { get; set; }

        /// <summary>
        /// Enable stack ensemble.
        /// </summary>
        [ModuleConfig(Description = "Enable stack ensemble", IsRequired = false)]
        [NodeConfig(Description = "Enable stack ensemble", IsRequired = false, SupportLinkSetting = true)]
        public bool? EnableStackEnsemble { get; set; }

        /// <summary>
        /// Enable voting ensemble.
        /// </summary>
        [ModuleConfig(Description = "Enable voting ensemble", IsRequired = false)]
        [NodeConfig(Description = "Enable voting ensemble", IsRequired = false, SupportLinkSetting = true)]
        public bool? EnableVoteEnsemble { get; set; }

        /// <summary>
        /// During VotingEnsemble and StackEnsemble model generation, multiple fitted models from the previous child runs are downloaded.
        /// Configure this parameter with a higher value than 300 secs, if more time is needed.
        /// </summary>
        [ModuleConfig(Description = "Enable model download timeout", IsRequired = false)]
        [NodeConfig(Description = "Enable model download timeout", IsRequired = false)]
        public TimeSpan? EnsembleModelDownloadTimeout { get; set; }

        /// <summary>
        /// Flag to turn on explainability on best model.
        /// </summary>
        [ModuleConfig(Description = "Enable model explainability", IsRequired = false)]
        [NodeConfig(Description = "Enable model explainability", IsRequired = false, SupportLinkSetting = true)]
        public bool? EnableModelExplainability { get; set; }

        /// <summary>
        /// The training mode to use.
        /// </summary>
        [ModuleConfig(Description = "The training mode to use.", IsRequired = false)]
        [NodeConfig(Description = "The training mode to use.", IsRequired = false, SupportLinkSetting = true)]
        public TabularTrainingMode? TrainingMode { get; set; }
    }
}