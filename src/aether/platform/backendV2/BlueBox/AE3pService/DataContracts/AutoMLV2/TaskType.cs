﻿// <copyright file="TaskType.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Runtime.Serialization;

namespace Microsoft.Aether.AEVA.DataContracts
{
    /// <summary>
    ///  Experiment Task type.
    /// </summary>
    public enum TaskType
    {
        /// <summary>
        /// Classification
        /// </summary>
        [EnumMember(Value = "classification")]
        Classification,

        /// <summary>
        /// Regression
        /// </summary>
        [EnumMember(Value = "regression")]
        Regression,

        /// <summary>
        /// Forecasting
        /// </summary>
        [EnumMember(Value = "forecasting")]
        Forecasting,

        /// <summary>
        /// Image Classification
        /// </summary>
        [EnumMember(Value = "image_classification")]
        ImageClassification,

        /// <summary>
        /// Image Multilabeling
        /// </summary>
        [EnumMember(Value = "image_classification_multilabel")]
        ImageClassificationMultilabel,

        /// <summary>
        /// Image object detection
        /// </summary>
        [EnumMember(Value = "image_object_detection")]
        ImageObjectDetection,

        /// <summary>
        /// Image instance segmentation
        /// </summary>
        [EnumMember(Value = "image_instance_segmentation")]
        ImageInstanceSegmentation,

        /// <summary>
        /// Text classification.
        /// </summary>
        [EnumMember(Value = "text_classification")]
        TextClassification,

        /// <summary>
        /// Text multi-labeling.
        /// </summary>
        [EnumMember(Value = "text_multilabel")]
        TextMultiLabeling,

        /// <summary>
        /// Text Named Entity Recognition a.k.a. TextNER.
        /// </summary>
        [EnumMember(Value = "text_ner")]
        TextNER,

        /// <summary>
        /// Text multi-labeling.
        /// </summary>
        [EnumMember(Value = "text_classification_multilabel")]
        TextClassificationMultilabel,
    }
}