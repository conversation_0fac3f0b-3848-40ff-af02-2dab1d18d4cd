﻿// <copyright file="TargetLags.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Collections.Generic;
using System.Runtime.Serialization;

namespace Microsoft.Aether.AEVA.DataContracts
{
    /// <summary>
    /// Target lags mode.
    /// </summary>
    public enum TargetLagsMode
    {
        /// <summary>
        /// Target lags to be determined automatically.
        /// </summary>
        [EnumMember(Value = "auto")]
        Auto = 0,

        /// <summary>
        /// Use the custom target lags.
        /// </summary>
        [EnumMember(Value = "custom")]
        Custom = 1
    }

    /// <summary>
    /// Forecasting target lags.
    /// </summary>
    public class TargetLags
    {
        /// <summary>
        /// Target lags mode.
        /// </summary>
        [ModuleConfig(Description = "Enable model download timeout", IsRequired = false)]
        [NodeConfig(Description = "Enable model download timeout", IsRequired = false)]
        public TargetLagsMode Mode { get; set; }

        /// <summary>
        /// Lag values.
        /// </summary>
        [ModuleConfig(Description = "Lag values", IsRequired = false)]
        [NodeConfig(Description = "Lag values", IsRequired = false)]
        public IList<int> Values { get; set; }
    }
}
