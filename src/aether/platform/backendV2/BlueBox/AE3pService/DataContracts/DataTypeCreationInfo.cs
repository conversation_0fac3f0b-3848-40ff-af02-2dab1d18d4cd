﻿// <copyright file="DataTypeCreationInfo.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Collections.Generic;
using System.Runtime.Serialization;

namespace Microsoft.Aether.AEVA.DataContracts
{
    [DataContract]
    public class DataTypeCreationInfo
    {
        public DataTypeCreationInfo(string id,
                            string name,
                            string description,
                            bool isDirectory)
        {
            Name = name;
            Description = description;
            IsDirectory = isDirectory;
            Id = id;
        }

        [DataMember]
        public string Id { get; private set; }

        [DataMember]
        public string Name { get; private set; }

        [DataMember]
        public string Description { get; private set; }

        [SwaggerExclude, DataMember]
        public bool IsDirectory { get; private set; }

        [SwaggerExclude, DataMember]
        public string FileExtension { get; set; }

        [DataMember]
        public IList<string> ParentDataTypeIds { get; set; }
    }
}
