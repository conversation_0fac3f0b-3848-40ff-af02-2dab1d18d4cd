﻿// <copyright file="PipelineDraft.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Collections.Generic;
using System.Runtime.Serialization;

namespace Microsoft.Aether.AEVA.DataContracts
{
    [DataContract]
    public class PipelineDraft : BaseNoStatusEntity
    {
        public PipelineDraft()
        {
            KvTags = new Dictionary<string, string>();
            StepTags = new Dictionary<string, string>();
            Properties = new Dictionary<string, string>();
            StepProperties = new Dictionary<string, string>();
        }

        [DataMember]
        [UserMutable]
        public string Name { get; set; }

        [DataMember]
        [UserMutable]
        public string Description { get; set; }

        // naming as tags for wirecontracts and KvTags internally due to ContractConverter limitations
        [DataMember(Name = "tags")]
        [UserMutable]
        public IDictionary<string, string> KvTags { get; set; }

        /// <summary>
        /// User provided tags for steps of pipeline. Each step run in pipeline will get these tags assigned automatically during orchestration.
        /// </summary>
        [DataMember]
        [UserMutable]
        public IDictionary<string, string> StepTags { get; set; }

        [DataMember]
        [UserMutable]
        public IDictionary<string, string> Properties { get; set; }

        /// <summary>
        /// User provided properties for steps of pipeline. Each step run in pipeline will get these properties assigned automatically during orchestration.
        /// </summary>
        [DataMember]
        [UserMutable]
        public IDictionary<string, string> StepProperties { get; set; }

        [DataMember]
        [UserMutable]
        public PipelineSubmissionInfo PipelineSubmissionInfo { get; set; }

        [DataMember]
        public string GraphDraftId { get; set; }

        [DataMember]
        public string ParentPipelineId { get; set; }

        [DataMember]
        public string ParentPipelineRunId { get; set; }

        [DataMember]
        public List<string> ParentStepRunIds { get; set; }

        [DataMember]
        public string ParentPipelineDraftId { get; set; }

        [DataMember]
        public string LastSubmittedPipelineRunId { get; set; }

        [DataMember]
        public CreatedBy CreatedBy { get; set; }

        [DataMember]
        public CreatedBy LastUpdatedBy { get; set; }
    }
}
