﻿// <copyright file="StructuredInterfaceParameter.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Collections.Generic;
using System.Runtime.Serialization;

namespace Microsoft.Aether.AEVA.DataContracts
{
    [DataContract]
    public class StructuredInterfaceParameter
    {
        [DataMember]
        public string Name { get; set; }

        /// <summary>
        /// Label is a human readable name of a parameter that could be shown in UX
        /// which might be changed in the future for readability reasons.
        /// </summary>
        [DataMember]
        public string Label { get; set; }

        [DataMember]
        public ParameterType ParameterType { get; set; }

        [DataMember]
        public bool IsOptional { get; set; }

        [DataMember]
        public string DefaultValue { get; set; }

        [DataMember]
        public string LowerBound { get; set; }

        [DataMember]
        public string UpperBound { get; set; }

        [DataMember]
        public List<string> EnumValues { get; set; }

        // If specified, EnumValuesToArgumentStrings is a custom mapping between the enum values of this parameter
        // and the argument strings that would be passed into the module during execution.  For example,
        // a parameter value of "Verbose" might map to an argument string of "--show-verbose-logs".
        [DataMember]
        public IDictionary<string, string> EnumValuesToArgumentStrings { get; set; }

        [DataMember]
        public string Description { get; set; }

        [DataMember]
        public bool SetEnvironmentVariable { get; set; }

        [DataMember]
        public string EnvironmentVariableOverride { get; set; }

        // If specified, EnabledByParameterName is the name of another parameter of the current module which
        // can enable or disable this parameter.  For example, a module might have a 'Language' parameter which
        // can be set to 'Python' or 'R'.  For parameters that are only relevant in (e.g.) the 'Python' case,
        // they can use EnabledByParameterName="Language" and EnabledByParameterValues={"Python"}
        [DataMember]
        public string EnabledByParameterName { get; set; }

        // If specified, EnabledByParameterValues is a list of values of another parameter of the current
        // module which can enable or disable this parameter.  For example, a module might have a 'Language'
        // parameter which can be set to 'Python' or 'R'.  For parameters that are only relevant in  (e.g.) the 'Python'
        // case, they can use EnabledByParameterName="Language" and EnabledByParameterValues={"Python"}
        [DataMember]
        public List<string> EnabledByParameterValues { get; set; }

        /// <summary>
        /// Gets or sets the UI hint of this parameter.
        /// UIHint is used to customized the UI rendering of parameters in Pipeline Builder UI.
        /// For example, for "Mode" parameter, UI would render it as a drop down list;
        /// for "ColumnPicker" parameter, UI would pop up a ColumnPicker widget as a input.
        /// </summary>
        [DataMember]
        public UIParameterHint UIHint { get; set; }

        /// <summary>
        /// List of parameter group names associated with this parameter
        /// This is used to construct hierarchical parameter names in the SDK/UX
        /// </summary>
        [DataMember]
        public IList<string> GroupNames { get; set; }

        /// <summary>
        /// ArgumentName is used by the SDK/UX to provide an interface to the customer to specify parameter values,
        /// and this only needs to be unique inside a group
        /// </summary>
        [DataMember]
        public string ArgumentName { get; set; }
    }
}
