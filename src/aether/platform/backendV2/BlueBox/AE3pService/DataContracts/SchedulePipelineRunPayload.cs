// <copyright file="SchedulePipelineRunPayload.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Collections.Generic;
using System.Runtime.Serialization;

namespace Microsoft.Aether.AEVA.DataContracts
{
    [DataContract]
    public class SchedulePipelineRunPayload
    {
        [DataMember]
        public string RelativeDataPath { get; set; }

        // To support pipeline parameter expression
        [DataMember]
        public IDictionary<string, string> ParameterAssignments { get; set; }

        // To support schedule triggered pipelines set display name
        [DataMember]
        public string ScheduleTriggerTime { get; set; }
    }
}
