﻿// <copyright file="DataTransferV2CloudSetting.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Microsoft.MachineLearning.Common.Core.Attributes;
using Newtonsoft.Json;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;

namespace Microsoft.Aether.AEVA.DataContracts
{
    public class DataTransferV2CloudSetting
    {
        [DataMember]
        [ModuleConfig(Description = "Data Transfer task type", IsRequired = true)]
        public DataTransferTaskType? TaskType { get; set; }

        [DataMember, PipelinesHash(RunReuseIgnore = true)]
        [JsonProperty(PropertyName = "ComputeName", NullValueHandling = NullValueHandling.Ignore)]
        [NodeConfig(Description = "Compute instance name", UIWidgetType = UIWidgetTypeEnum.ComputeSelection, SupportLinkSetting = true)]
        public string ComputeName { get; set; }

        [DataMember]
        [JsonProperty(PropertyName = "CopyDataTask", NullValueHandling = NullValueHandling.Ignore)]
        [ModuleConfig(Description = "Copy Data Task")]
        [NodeConfig(Description = "Copy Data Task")]
        public CopyDataTask CopyDataTask { get; set; }

        [DataMember]
        [JsonProperty(PropertyName = "ImportDataTask", NullValueHandling = NullValueHandling.Ignore)]
        [ModuleConfig(Description = "Import Data Task")]
        [NodeConfig(Description = "Import Data Task")]
        public ImportDataTask ImportDataTask { get; set; }

        [DataMember]
        [JsonProperty(PropertyName = "ExportDataTask", NullValueHandling = NullValueHandling.Ignore)]
        [ModuleConfig(Description = "Export Data Task")]
        [NodeConfig(Description = "Export Data Task")]
        public ExportDataTask ExportDataTask { get; set; }

        [DataMember]
        [JsonProperty(PropertyName = "DataTransferSources", NullValueHandling = NullValueHandling.Ignore)]
        [ModuleConfig(Description = "Input data transfer sources")]
        [NodeConfig(Description = "Input data transfer sources")]
        public IDictionary<string, DataTransferSource> DataTransferSources { get; set; }

        [DataMember]
        [JsonProperty(PropertyName = "DataTransferSinks", NullValueHandling = NullValueHandling.Ignore)]
        [ModuleConfig(Description = "Output data transfer sources")]
        [NodeConfig(Description = "Output data transfer sources")]
        public IDictionary<string, DataTransferSink> DataTransferSinks { get; set; }

        [DataMember]
        [JsonProperty(PropertyName = "DataCopyMode", NullValueHandling = NullValueHandling.Ignore)]
        [ModuleConfig(Description = "Data copy mode when conflict")]
        [NodeConfig(Description = "Data copy mode when conflict")]
        public DataCopyMode? DataCopyMode { get; set; }
    }
}
