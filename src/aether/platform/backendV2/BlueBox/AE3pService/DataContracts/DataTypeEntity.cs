﻿// <copyright file="DataTypeEntity.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Collections.Generic;
using System.Runtime.Serialization;

namespace Microsoft.Aether.AEVA.DataContracts
{
    [DataContract]
    public class DataTypeEntity : BaseEntity
    {
        [DataMember]
        [UserMutable]
        public string Name { get; set; }

        [DataMember]
        [UserMutable]
        public string Description { get; set; }

        [SwaggerExclude, DataMember]
        public bool IsDirectory { get; set; }

        [DataMember]
        [UserMutable]
        public IList<string> ParentDataTypeIds { get; set; }
    }
}
