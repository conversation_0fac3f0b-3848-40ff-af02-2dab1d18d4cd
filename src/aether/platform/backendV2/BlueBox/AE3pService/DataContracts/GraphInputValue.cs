﻿// <copyright file="GraphInputValue.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Runtime.Serialization;

namespace Microsoft.Aether.AEVA.DataContracts
{
    /// <summary>
    /// Input value assignment for a subgraph input
    /// </summary>
    [DataContract]
    public sealed class GraphInputValue
    {
        /// <summary>
        /// Data ID for intermediate or data source inputs
        /// </summary>
        [DataMember]
        public string DataId { get; set; }

        /// <summary>
        /// Intermediate or data source ID which is used to compute a hash for data reuse
        /// For dataset inputs, it is a hash based on the data path or dataset ID
        /// </summary>
        [DataMember]
        public string DataIdForHash { get; set; }

        /// <summary>
        /// DataType ID of the connected input source
        /// </summary>
        [DataMember]
        public string DataTypeId { get; set; }

        /// <summary>
        /// DataLocation to use for this input
        /// </summary>
        [DataMember]
        public DataLocation DataLocation { get; set; }

        /// <summary>
        /// The reason why the input is not ready
        /// </summary>
        [DataMember]
        public InputNotReadyReason InputNotReadyReason { get; set; }
    }
}
