﻿// <copyright file="PipelineRunStatusCode.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Runtime.Serialization;

namespace Microsoft.Aether.AEVA.DataContracts
{
    [DataContract]
    public enum PipelineRunStatusCode
    {
        [EnumMember]
        NotStarted = 0,

        [EnumMember]
        Running = 1,

        [EnumMember]
        Failed = 2,

        [EnumMember]
        Finished = 3,

        [EnumMember]
        Canceled = 4,

        [EnumMember]
        Queued = 5,

        [EnumMember]
        CancelRequested = 6,
    }
}
