﻿// <copyright file="UIColumnPicker.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Collections.Generic;
using System.Runtime.Serialization;

namespace Microsoft.Aether.AEVA.DataContracts
{
    [DataContract]
    public class UIColumnPicker
    {
        /// <summary>
        /// Gets or sets the name of the input that this parameter is a column picker for.
        /// </summary>
        [DataMember]
        public string ColumnPickerFor { get; set; }

        /// <summary>
        /// Gets or sets the specified categories of columns that avaible to select, which would be "ALL", "Numeric", "Label", etc.
        /// </summary>
        [DataMember]
        public IList<string> ColumnSelectionCategories { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether only a single column may be selected.
        /// </summary>
        [DataMember]
        public bool SingleColumnSelection { get; set; }
    }
}
