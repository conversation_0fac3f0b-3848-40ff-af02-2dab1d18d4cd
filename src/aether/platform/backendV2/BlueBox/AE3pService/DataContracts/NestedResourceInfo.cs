﻿// <copyright file="NestedResourceInfo.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Collections.Generic;
using System.Runtime.Serialization;

namespace Microsoft.Aether.AEVA.DataContracts
{
    [DataContract]
    public class NestedResourceInfo
    {
        [DataMember]
        public IList<DataSourceEntity> DataSourceEntities;

        [DataMember]
        public IList<Module> Modules;

        [DataMember]
        public IDictionary<string, VisualGraphWithEntityInterface> Graphs;

        [DataMember]
        public IDictionary<string, GraphDraftEntity> GraphDrafts;
    }
}
