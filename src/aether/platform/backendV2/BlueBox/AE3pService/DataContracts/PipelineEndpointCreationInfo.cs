﻿// <copyright file="PipelineEndpointCreationInfo.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Collections.Generic;
using System.Runtime.Serialization;

namespace Microsoft.Aether.AEVA.DataContracts
{
    [DataContract]
    public class PipelineEndpointCreationInfo
    {
        public PipelineEndpointCreationInfo()
        {
            KvTags = new Dictionary<string, string>();
            StepTags = new Dictionary<string, string>();
            Properties = new Dictionary<string, string>();
            StepProperties = new Dictionary<string, string>();
        }

        public PipelineEndpointCreationInfo(
            string name,
            string description,
            string pipelineId,
            IDictionary<string, string> kvTags = null,
            IDictionary<string, string> properties = null,
            IDictionary<string, string> stepTags = null,
            IDictionary<string, string> stepProperties = null)
            : this()
        {
            Name = name;
            Description = description;
            PipelineId = pipelineId;
            KvTags = kvTags;
            StepTags = stepTags;
            Properties = properties;
            StepProperties = stepProperties;
        }

        [DataMember(IsRequired = true)]
        public string Name { get; set; }

        [DataMember]
        public string Description { get; set; }

        [DataMember(IsRequired = true)]
        public string PipelineId { get; set; }

        [DataMember(Name = "tags")]
        public IDictionary<string, string> KvTags { get; set; }

        /// <summary>
        /// User provided tags for steps of pipeline. Each step run in pipeline will get these tags assigned automatically during orchestration.
        /// </summary>
        [DataMember]
        public IDictionary<string, string> StepTags { get; set; }

        [DataMember]
        public IDictionary<string, string> Properties { get; set; }

        /// <summary>
        /// User provided properties for steps of pipeline. Each step run in pipeline will get these properties assigned automatically during orchestration.
        /// </summary>
        [DataMember]
        public IDictionary<string, string> StepProperties { get; set; }
    }
}
