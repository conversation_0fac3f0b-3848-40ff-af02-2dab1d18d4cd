﻿// <copyright file="DatasetOutput.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Runtime.Serialization;

namespace Microsoft.Aether.AEVA.DataContracts
{
    /// <summary>
    /// Represents the type of the output.
    /// </summary>
    /// <remarks>
    /// This class only contains one property for now but will be extended in the future
    /// to include additional properties like how to promote the output to a AzureML
    /// Tabular Dataset.
    /// </remarks>
    [DataContract]
    public class DatasetOutput
    {
        [DataMember]
        public DatasetType DatasetType { get; set; }

        [DataMember]
        public DatasetRegistration DatasetRegistration { get; set; }

        [DataMember]
        public DatasetOutputOptions DatasetOutputOptions { get; set; }
    }
}
