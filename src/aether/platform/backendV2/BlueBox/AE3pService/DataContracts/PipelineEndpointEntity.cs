﻿// <copyright file="PipelineEndpointEntity.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Collections.Generic;
using System.Runtime.Serialization;

namespace Microsoft.Aether.AEVA.DataContracts
{
    public class PipelineEndpointEntity : BaseEntity
    {
        public PipelineEndpointEntity()
        {
            KvTags = new Dictionary<string, string>();
            StepTags = new Dictionary<string, string>();
            Properties = new Dictionary<string, string>();
            StepProperties = new Dictionary<string, string>();
        }

        [DataMember]
        [UserMutable]
        public string Name { get; set; }

        [DataMember]
        [UserMutable]
        public string DefaultVersion { get; set; }

        [DataMember]
        [UserMutable]
        public string Description { get; set; }

        [DataMember]
        [UserMutable]
        public List<PipelineVersion> PipelineVersionList { get; set; }

        [DataMember]
        public string Url { get; set; }

        [DataMember(Name = "tags")]
        public IDictionary<string, string> KvTags { get; set; }

        /// <summary>
        /// User provided tags for steps of pipeline. Each step run in pipeline will get these tags assigned automatically during orchestration.
        /// </summary>
        [DataMember]
        public IDictionary<string, string> StepTags { get; set; }

        [DataMember]
        [UserMutable]
        public IDictionary<string, string> Properties { get; set; }

        /// <summary>
        /// User provided properties for steps of pipeline. Each step run in pipeline will get these properties assigned automatically during orchestration.
        /// </summary>
        [DataMember]
        [UserMutable]
        public IDictionary<string, string> StepProperties { get; set; }

        [DataMember]
        public CreatedBy CreatedBy { get; set; }

        [DataMember]
        public CreatedBy LastUpdatedBy { get; set; }

        [DataMember]
        [UserMutable]
        public string LastRunId { get; set; }

        [DataMember]
        [UserMutable]
        public string SwaggerUrl { get; set; }
    }

    [DataContract]
    public class PipelineVersion
    {
        [DataMember]
        public string Version { get; set; }

        [DataMember]
        public string PipelineId { get; set; }
    }
}
