﻿// <copyright file="DatastoreSetting.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Runtime.Serialization;

namespace Microsoft.Aether.AEVA.DataContracts
{
    [DataContract]
    public class DatastoreSetting
    {
        // AML DataStore name which stores the Azure storage information, e.g. blob account, file share account and data lake
        // If the DataStoreName is empty for cloudlets which requires DataStore reference, DataStore should inherit from the node
        [DataMember]
        public string DataStoreName { get; set; }
    }
}
