﻿// <copyright file="DatabaseSource.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Newtonsoft.Json;
using System.Collections.Generic;
using System.Runtime.Serialization;

namespace Microsoft.Aether.AEVA.DataContracts
{
    [DataContract]
    public class DatabaseSource
    {
        [DataMember]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        [NodeConfig(SupportLinkSetting = true, UIWidgetType = UIWidgetTypeEnum.ConnectionSelection, IsRequired = true)]
        public string Connection { get; set; }

        [DataMember]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        [NodeConfig(SupportLinkSetting = true, UIWidgetType = UIWidgetTypeEnum.Script, IsRequired = true)]
        public string Query { get; set; }

        [DataMember]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        [NodeConfig]
        public string StoredProcedureName { get; set; }

        [DataMember]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        [NodeConfig]
        public IList<StoredProcedureParameter> StoredProcedureParameters { get; set; }
    }
}
