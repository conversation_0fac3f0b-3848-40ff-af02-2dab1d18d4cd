﻿// <copyright file="ScheduleCreationInfo.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Runtime.Serialization;

namespace Microsoft.Aether.AEVA.DataContracts
{
    [DataContract]
    public class ScheduleCreationInfo
    {
        public ScheduleCreationInfo(
            string name,
            string pipelineId,
            PipelineSubmissionInfo pipelineSubmissionInfo,
            Recurrence recurrence,
            ScheduleType scheduleType = ScheduleType.Recurrence,
            DataStoreTriggerInfo dataStoreTriggerInfo = null,
            string pipelineendpointId = null)
        {
            Name = name;
            PipelineId = pipelineId;
            Recurrence = recurrence;
            PipelineSubmissionInfo = pipelineSubmissionInfo;
            ScheduleType = scheduleType;
            DataStoreTriggerInfo = dataStoreTriggerInfo;
            PipelineEndpointId = pipelineendpointId;
        }

        [DataMember]
        public string Name { get; set; }

        [DataMember]
        public string PipelineId { get; set; }

        [DataMember]
        public string PipelineEndpointId { get; set; }

        [DataMember]
        public PipelineSubmissionInfo PipelineSubmissionInfo { get; set; }

        [DataMember]
        public Recurrence Recurrence { get; set; }

        [DataMember]
        public ScheduleType ScheduleType { get; set; }

        [DataMember]
        public DataStoreTriggerInfo DataStoreTriggerInfo { get; set; }
    }
}
