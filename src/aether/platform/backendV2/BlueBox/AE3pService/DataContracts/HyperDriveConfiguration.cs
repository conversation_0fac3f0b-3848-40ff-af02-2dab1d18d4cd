﻿// <copyright file="HyperDriveConfiguration.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Collections.Generic;
using System.Runtime.Serialization;
using Newtonsoft.Json;

namespace Microsoft.Aether.AEVA.DataContracts
{
    /// <summary>
    /// This is the configuration for HyperDrive Run
    /// </summary>
    [DataContract]
    public class HyperDriveConfiguration
    {
        [DataMember]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string HyperDriveRunConfig { get; set; }

        [DataMember]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string PrimaryMetricGoal { get; set; }

        [DataMember]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string PrimaryMetricName { get; set; }

        [DataMember]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public List<ArgumentAssignment> Arguments { get; set; }
    }
}
