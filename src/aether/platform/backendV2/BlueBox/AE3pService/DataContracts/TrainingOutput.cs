﻿// <copyright file="TrainingOutput.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Runtime.Serialization;

namespace Microsoft.Aether.AEVA.DataContracts
{
    [DataContract]
    public class TrainingOutput
    {
        [DataMember]
        public TrainingOutputType TrainingOutputType { get; set; }

        [DataMember]
        public int? Iteration { get; set; }

        [DataMember]
        public string Metric { get; set; }

        [DataMember]
        public string ModelFile { get; set; }
    }
}
