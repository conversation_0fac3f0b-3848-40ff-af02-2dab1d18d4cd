﻿// <copyright file="InputSetting.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Collections.Generic;
using System.Runtime.Serialization;

namespace Microsoft.Aether.AEVA.DataContracts
{
    [DataContract]
    public class InputSetting
    {
        /// <summary>
        /// The port name.
        /// </summary>
        [DataMember]
        public string Name { get; set; }

        /// <summary>
        /// Used to override DataStoreMode of a component's input dataset.
        /// </summary>
        [DataMember]
        public DataStoreMode DataStoreMode { get; set; }

        /// <summary>
        /// Used to override PathOnCompute of a component's input dataset in Download mode.
        /// </summary>
        [DataMember]
        public string PathOnCompute { get; set; }

        /// <summary>
        /// Input options.
        /// </summary>
        [DataMember]
        public IDictionary<string, string> Options { get; set; }

        /// <summary>
        /// A partial dataflow serialized to json string representing any additional transformations
        /// to apply to the input data.
        /// </summary>
        [DataMember]
        public string AdditionalTransformations { get; set; }

        public InputSetting ShallowCopy()
        {
            return (InputSetting)this.MemberwiseClone();
        }
    }
}
