﻿// <copyright file="NodeConfigAttribute.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;

namespace Microsoft.Aether.AEVA.DataContracts
{
    [AttributeUsage(AttributeTargets.Property, Inherited = false, AllowMultiple = false)]
    public sealed class NodeConfigAttribute : RunConfigPropertyAttribute
    {
        /// <summary>
        /// Initialize a new instance of Microsoft.Aether.AEVA.DataContracts.Cloud.NodeConfigAttribute
        /// class
        /// </summary>
        public NodeConfigAttribute()
        {
        }

        /// <summary>
        /// Whether it is allowed to set with a Parameter Assignment
        /// </summary>
        public bool SupportLinkSetting { get; set; }

        /// <summary>
        /// A property of UIHint
        /// </summary>
        public UIWidgetTypeEnum UIWidgetType { get; set; }
    }
}
