﻿// <copyright file="DeepPipelineViewEntity.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Collections.Generic;
using System.Runtime.Serialization;

namespace Microsoft.Aether.AEVA.DataContracts
{
    [DataContract]
    public class DeepPipelineViewEntity
    {
        [DataMember]
        public PipelineViewEntity PipelineViewEntity;

        [DataMember]
        public VisualGraphWithEntityInterface GraphWithInterface;

        [DataMember]
        public IList<DataSourceEntity> DataSourceEntities;

        [DataMember]
        public IList<Module> Modules;
    }
}
