﻿// <copyright file="GraphControlReferenceNode.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Collections.Generic;
using System.Runtime.Serialization;

namespace Microsoft.Aether.AEVA.DataContracts
{
    [DataContract]
    public enum ControlFlowType
    {
        [EnumMember]
        None,

        [EnumMember]
        DoWhile,

        [EnumMember]
        ParallelFor,
    }

    [DataContract]
    public class DoWhileControlFlowRunSettings
    {
        [DataMember]
        [DoWhileControlFlowRunSettings(DefaultValue = "100", MaxValue = "1000")]
        public ParameterAssignment MaxLoopIterationCount { get; set; }
    }

    [DataContract]
    public class DoWhileControlFlowInfo
    {
        /// <summary>
        /// Key: while body output port name, Value: the list of while body input port names
        /// </summary>
        [DataMember]
        public Dictionary<string, List<string>> OutputPortNameToInputPortNamesMapping { get; set; }

        [DataMember]
        public string ConditionOutputPortName { get; set; }

        [DataMember]
        public DoWhileControlFlowRunSettings RunSettings { get; set; }
    }

    [DataContract]
    public class ParallelForControlFlowInfo
    {
        [DataMember]
        public ParameterAssignment ParallelForItemsInput { get; set; }
    }

    [DataContract]
    public class GraphControlReferenceNode
    {
        [DataMember]
        public string Id { get; set; }

        [DataMember]
        public string Name { get; set; }

        [DataMember]
        public string Comment { get; set; }

        [DataMember]
        public ControlFlowType ControlFlowType { get; set; }

        /// <summary>
        /// The node id of the module node or subgraph node which referenced by this GraphControlReferenceNode
        /// </summary>
        [DataMember]
        public string ReferenceNodeId { get; set; }

        [DataMember]
        public DoWhileControlFlowInfo DoWhileControlFlowInfo { get; set; }

        [DataMember]
        public ParallelForControlFlowInfo ParallelForControlFlowInfo { get; set; }

        /// <summary>
        /// Run Attribution, will be recorded as runTypeV2.traits for telemetry usage
        /// </summary>
        [DataMember]
        public string RunAttribution { get; set; }
    }
}
