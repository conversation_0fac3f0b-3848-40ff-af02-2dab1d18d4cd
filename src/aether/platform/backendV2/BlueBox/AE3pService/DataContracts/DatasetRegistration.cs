﻿// <copyright file="DatasetRegistration.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Collections.Generic;
using System.Runtime.Serialization;

namespace Microsoft.Aether.AEVA.DataContracts
{
    [DataContract]
    public class DatasetRegistration
    {
        [DataMember]
        public string Name { get; set; }

        [DataMember]
        public bool CreateNewVersion { get; set; }

        [DataMember]
        public string Description { get; set; }

        [DataMember]
        public Dictionary<string, string> Tags { get; set; }

        [DataMember]
        public string AdditionalTransformations { get; set; }
    }
}
