﻿// <copyright file="GraphEdge.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Runtime.Serialization;

namespace Microsoft.Aether.AEVA.DataContracts
{
    [DataContract]
    public class GraphEdge
    {
        [DataMember]
        public PortInfo SourceOutputPort { get; set; }

        [DataMember]
        public PortInfo DestinationInputPort { get; set; }
    }
}
