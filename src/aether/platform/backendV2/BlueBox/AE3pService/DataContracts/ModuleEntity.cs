﻿// <copyright file="ModuleEntity.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Collections.Generic;
using System.Runtime.Serialization;

namespace Microsoft.Aether.AEVA.DataContracts
{
    [DataContract]
    public class ModuleEntity : ResourceEntity
    {
        [DataMember]
        [UserMutable]
        public string DisplayName { get; set; }

        /// <summary>
        /// Previously known as cloud system
        /// Store will validate the values and it's case insensitive
        /// </summary>
        [DataMember]
        public string ModuleExecutionType { get; set; }

        [DataMember]
        public ModuleType? ModuleType { get; set; }

        /// <summary>
        /// This is used for create snapshot of ModuleType based on version.
        /// </summary>
        [DataMember]
        public string ModuleTypeVersion { get; set; }

        [DataMember]
        [UserMutable]
        public UploadState UploadState { get; set; }

        [DataMember]
        public bool IsDeterministic { get; set; }

        [DataMember]
        public StructuredInterface StructuredInterface { get; set; }

        [DataMember]
        [UserMutable]
        public DataLocation DataLocation { get; set; }

        [DataMember]
        public string IdentifierHash { get; set; }

        [DataMember]
        public string IdentifierHashV2 { get; set; }

        [DataMember(Name = "tags")]
        [UserMutable]
        public IDictionary<string, string> KvTags { get; set; }

        [DataMember]
        [UserMutable]
        public IDictionary<string, string> Properties { get; set; }

        [DataMember]
        public CreatedBy CreatedBy { get; set; }

        [DataMember]
        [UserMutable]
        public CreatedBy LastUpdatedBy { get; set; }

        /// <summary>
        /// Runconfig definition serialized as JSON.  This is a default runconfig to apply to instances of the module,
        /// which can be overridden on a per-node basis
        /// </summary>
        [DataMember]
        public string Runconfig { get; set; }

        /// <summary>
        /// Gets or Sets CloudSettings. CloudSettings is the cloud specific settings
        /// </summary>
        [DataMember]
        public CloudSettings CloudSettings { get; set; }

        [DataMember]
        [UserMutable]
        public string Category { get; set; }

        /// <summary>
        /// Type of step that generated this module (e.g. PythonScriptStep, ModuleStep, etc.)
        /// </summary>
        [DataMember]
        public string StepType { get; set; }

        [DataMember]
        [UserMutable]
        public string Stage { get; set; }

        public ModuleEntity()
        {
            KvTags = new Dictionary<string, string>();
            Properties = new Dictionary<string, string>();
        }

        public ModuleEntity(
            string name,
            string displayName,
            string description,
            bool isDeterministic,
            string moduleExecutionType,
            string hash,
            UploadState uploadState,
            StructuredInterface structuredInterface,
            IDictionary<string, string> kvTags = null,
            IDictionary<string, string> properties = null,
            ModuleType? moduleType = null,
            string runconfig = null,
            string stepType = null)
            : this()
        {
            Name = name;
            DisplayName = displayName;
            Description = description;
            IsDeterministic = isDeterministic;
            ModuleExecutionType = moduleExecutionType;
            Hash = hash;
            UploadState = uploadState;
            StructuredInterface = structuredInterface;
            KvTags = kvTags;
            Properties = properties;
            ModuleType = moduleType;
            Runconfig = runconfig;
            StepType = stepType;
        }
    }
}
