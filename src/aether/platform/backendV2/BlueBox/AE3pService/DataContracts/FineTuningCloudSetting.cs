﻿// <copyright file="AmlSparkCloudSetting.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

namespace Microsoft.Aether.AEVA.DataContracts
{
    /// <summary>
    /// This is the setting for AmlSparkCloud
    /// </summary>
    [DataContract]
    public class FineTuningCloudSetting
    {
        [DataMember]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        [ModuleConfig(IsRequired = true, Description = "Entry point of the application.")]
        public EntrySetting Entry { get; set; }

        [DataMember]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        [ModuleConfig(Description = "Files to be used in this session")]
        public List<string> Files { get; set; }

        [DataMember]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        [ModuleConfig(Description = "Archives to be used in this session")]
        public List<string> Archives { get; set; }

        [DataMember]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        [ModuleConfig(Description = "Jars to be used in this session")]
        public List<string> Jars { get; set; }

        [DataMember]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        [ModuleConfig(Description = "Python files to be used in this session")]
        public List<string> PyFiles { get; set; }

        [DataMember]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        [NodeConfig(Description = "Amount of memory to use for the driver process." +
            "It's the same format as JVM memory strings. Use lower-case suffixes, " +
            "e.g. k, m, g, t, and p, for kibi-, mebi-, gibi-, tebi-, and pebibytes, respectively.", SupportLinkSetting = true)]
        public string DriverMemory { get; set; }

        [DataMember]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        [NodeConfig(Description = "Number of cores to use for the driver process", SupportLinkSetting = true)]
        public int? DriverCores { get; set; }

        [DataMember]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        [NodeConfig(Description = "Amount of memory to use per executor process. " +
            "It's the same format as JVM memory strings. Use lower-case suffixes, " +
            "e.g. k, m, g, t, and p, for kibi-, mebi-, gibi-, tebi-, and pebibytes, respectively.", SupportLinkSetting = true)]
        public string ExecutorMemory { get; set; }

        [DataMember]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        [NodeConfig(Description = "Number of cores to use for each executor", SupportLinkSetting = true)]
        public int? ExecutorCores { get; set; }

        [DataMember]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        [NodeConfig(Description = "Number of executors to launch for this session", SupportLinkSetting = true)]
        public int? NumberExecutors { get; set; }

        [DataMember]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        [ModuleConfig(Description = "Environment Asset Id")]
        public string EnvironmentAssetId { get; set; }

        [DataMember]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore, DefaultValueHandling = DefaultValueHandling.Ignore)]
        [NodeConfig(Description = "Environment variables can be used to specify environment variables to be passed. " +
            "It is a dictionary of environment name to environment value mapping. " +
            "User can use this to adjust some component runtime behavior which is not exposed as component parameter, e.g. enable some debug switch.", SupportLinkSetting = true)]
        public Dictionary<string, string> EnvironmentVariables { get; set; }

        // Use string to store environment definition to avoid deserialization issue.
        [DataMember]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        [ModuleConfig(Description = "Inline Environment Definition")]
        public string InlineEnvironmentDefinitionString { get; set; }

        //depricated and should be allowed to use
        [Obsolete]
        [DataMember]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        [ModuleConfig(Description = "Content of the conda dependencies")]
        public string CondaDependencies { get; set; }

        [DataMember]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        [ModuleConfig(Description = "Spark configuration properties")]
        [NodeConfig(Description = "Spark configuration properties")]
        public Dictionary<string, string> Conf { get; set; }

        [DataMember]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        [NodeConfig(Description = "Compute target of the Spark job, for cluster experience", UIWidgetType = UIWidgetTypeEnum.ComputeSelection, SupportLinkSetting = true)]
        public string Compute { get; set; }

        [DataMember]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        [NodeConfig(Description = "Compute spec of the Spark job, for cluster-less experience")]
        public ResourcesSetting Resources { get; set; }

        [DataMember]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        [NodeConfig(Description = "Identity used to run the job")]
        public IdentitySetting Identity { get; set; }
    }
}
