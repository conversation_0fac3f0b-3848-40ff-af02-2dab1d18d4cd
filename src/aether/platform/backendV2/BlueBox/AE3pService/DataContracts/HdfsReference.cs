﻿// <copyright file="HdfsReference.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Runtime.Serialization;

namespace Microsoft.Aether.AEVA.DataContracts
{
    [DataContract]
    public class HdfsReference
    {
        [DataMember]
        public string AmlDataStoreName { get; set; }

        [DataMember]
        public string RelativePath { get; set; }
    }
}