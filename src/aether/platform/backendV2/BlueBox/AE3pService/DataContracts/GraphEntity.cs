﻿// <copyright file="GraphEntity.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Collections.Generic;
using System.Runtime.Serialization;

namespace Microsoft.Aether.AEVA.DataContracts
{
    [DataContract]
    public class GraphEntity : BaseEntity
    {
        [DataMember]
        public IList<GraphModuleNode> ModuleNodes { get; set; }

        [DataMember]
        public IList<GraphDatasetNode> DatasetNodes { get; set; }

        [DataMember]
        public IList<GraphReferenceNode> SubGraphNodes { get; set; }

        [DataMember]
        public IList<GraphControlReferenceNode> ControlReferenceNodes { get; set; }

        [DataMember]
        public IList<GraphControlNode> ControlNodes { get; set; }

        [DataMember]
        public IList<GraphEdge> Edges { get; set; }

        [DataMember]
        public ComputeSetting DefaultCompute { get; set; }

        [DataMember]
        public DatastoreSetting DefaultDatastore { get; set; }

        [DataMember]
        public CloudPrioritySetting DefaultCloudPriority { get; set; }

        /// <summary>
        /// The hierarchical subgraph module id list.
        /// For a root graph without subgraph, it will be null/empty.
        /// For a subgraph referenced by a parent graph, it will contains the subgraph module id itself as well.
        /// E.g. RootGraph#1 -> SubGraph#2 -> SubGraph#3, the <see cref="ParentSubGraphModuleIds" /> of SubGraph#3 will be [2, 3].
        /// </summary>
        [DataMember]
        public IList<string> ParentSubGraphModuleIds { get; set; }
    }
}
