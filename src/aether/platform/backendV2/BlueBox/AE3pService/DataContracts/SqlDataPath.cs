// <copyright file="SqlDataPath.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Collections.Generic;
using System.Runtime.Serialization;

namespace Microsoft.Aether.AEVA.DataContracts
{
    [DataContract]
    public sealed class SqlDataPath
    {
        [DataMember]
        public string SqlTableName { get; set; }

        [DataMember]
        public string SqlQuery { get; set; }

        [DataMember]
        public string SqlStoredProcedureName { get; set; }

        [DataMember]
        public List<StoredProcedureParameter> SqlStoredProcedureParams { get; set; }
    }
}