﻿<Project Sdk="Microsoft.NET.Sdk" DefaultTargets="Publish;Build">
  <PropertyGroup>
    <PublishDir>$(BaseTargetDir)\app\aether\bluebox\BBQuotaService</PublishDir>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <HighEntropyVA>true</HighEntropyVA>
  </PropertyGroup>
  <ItemGroup>
    <None Include="..\..\shared\Scripts\start.sh">
      <Link>start.sh</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="..\CA\*.cer">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="..\..\AetherQuota\AetherQuotaService\quotaserviceconfig.ini">
      <Link>quotaserviceconfig.ini</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="..\..\CloudManager\Common\storage.ini">
      <Link>storage.ini</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="..\..\shared\SharedConfigurationSettings\SharedSettings.ini">
      <Link>SharedSettings.ini</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="..\..\shared\SharedConfigurationSettings\BBSharedSettings.ini">
      <Link>BBSharedSettings.ini</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>
  <ItemGroup>
    <None Include="Dockerfile">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\AetherQuota\AetherQuota.Common\AetherQuota.Common.csproj" />
    <ProjectReference Include="..\..\Clouds\Common\CloudCommon\CloudCommon.csproj" />
    <ProjectReference Include="..\..\shared\Microsoft.Aether.BackendCommon\Microsoft.Aether.BackendCommon.csproj" />
    <ProjectReference Include="..\..\shared\Microsoft.Aether.TaggedQueueManager.CloudTags\Microsoft.Aether.TaggedQueueManager.CloudTags.csproj" />
    <ProjectReference Include="..\AetherK8SBaseService\Microsoft.Aether.BlueBox.AetherK8SBaseService.csproj" />
    <ProjectReference Include="..\AetherK8SCommon\Microsoft.Aether.BlueBox.AetherK8SCommon.csproj" />
    <ProjectReference Include="..\MetaStore\MetaStoreV2.Client\MetaStoreV2.Client.csproj" />
    <ProjectReference Include="..\Microsoft.Aether.BlueBox.Storage\Microsoft.Aether.BlueBox.Common.Storage.csproj" />
    <ProjectReference Include="..\Microsoft.Aether.BlueBox.Web.Common\Microsoft.Aether.BlueBox.Web.Common.csproj" />
    <ProjectReference Include="$(SrcRoot)\Common\Startup\Common.Startup.csproj" />
    <ProjectReference Include="$(SrcRoot)\Common\WebApi\Common.WebApi.csproj" />
    <ProjectReference Include="$(SrcRoot)\Common\Core\Common.Core.csproj" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.IdentityModel.Clients.ActiveDirectory" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Versioning" />
  </ItemGroup>
</Project>
