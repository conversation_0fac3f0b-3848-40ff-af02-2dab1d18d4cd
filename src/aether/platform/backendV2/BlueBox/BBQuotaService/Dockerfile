FROM mcr.microsoft.com/dotnet/aspnet:8.0-bookworm-slim

# capture dotnet version on front end
RUN mkdir /home/<USER>
RUN dotnet --info > /home/<USER>/dotnet_info_full
RUN dotnet --list-runtimes > /home/<USER>/dotnet_info_runtimes

# Metadata indicating an image maintainer.
MAINTAINER <EMAIL>

COPY . /app
WORKDIR /app

# Sets a command or process that will run each time a container is run from the new image.
# we build on windows so x bit
ENTRYPOINT ["/bin/sh","/app/start.sh", "BBQuotaService.dll"]