﻿// <copyright file="RunningPipelineRunSoftQuotaController.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Microsoft.Aether.BackendCommon;
using Microsoft.Aether.DataContracts;
using Microsoft.Aether.DataContracts.Entity;
using Microsoft.Aether.QuotaService;
using Microsoft.Aether.QuotaService.Shared;
using Microsoft.Aether.QuotaService.Shared.Controllers;
using Microsoft.AspNetCore.Mvc;
using Microsoft.RelInfra.Instrumentation;
using Microsoft.RelInfra.Instrumentation.Logging;
using System;
using System.Threading.Tasks;

namespace Microsoft.Aether.BBQuotaService
{
    [ApiController]
    [ApiVersion("1.0")]
    [Route(Constants.RoutePrefix)]
    public class RunningPipelineRunSoftQuotaController : Controller, IRunningPipelineRunSoftQuotaController
    {
        private readonly CounterManager _counterManager;
        private readonly RunningPipelineRunSoftQuotaManager _runningPipelineRunSoftQuotaManager;
        private readonly IWorkspaceResourcesCache _workspaceResourcesCache;

        public RunningPipelineRunSoftQuotaController(
            CounterManager counterManager,
            RunningPipelineRunSoftQuotaManager runningPipelineRunSoftQuotaManager,
            IWorkspaceResourcesCache workspaceResourcesCache)
        {
            _counterManager = counterManager;
            _runningPipelineRunSoftQuotaManager = runningPipelineRunSoftQuotaManager;
            _workspaceResourcesCache = workspaceResourcesCache;
        }

        [HttpPost]
        [Route("PipelineRuns/{experimentId}")]
        public async Task<QuotaResponse> AcquireSoftQuotaAsync(
            string subscriptionId,
            string resourceGroupName,
            string workspaceName,
            string experimentId,
            [FromQuery] string parentExperimentId = null)
        {
            CommonLogger.LogEntityInfo(
                experimentId,
                "Acquire RunningPipelineRun soft quota for WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}",
                workspaceName,
                resourceGroupName,
                subscriptionId);
            _counterManager.GetNumberCounter(Constants.AcquireRunningPipelineRunSoftQuotaNumberCounter).Set(1);
            try
            {
                var workspaceIdentity = await GetWorkspaceIdentityAsync(subscriptionId, resourceGroupName, workspaceName);

                using (new DisposableTimer(_counterManager.GetLatencyCounter(Constants.AcquireRunningPipelineRunSoftQuotaLatencyCounter)))
                {
                    var softQuotaUsageEntity = GetRunningPipelineRunSoftQuotaUsage(experimentId, workspaceIdentity, parentExperimentId);
                    var result = await _runningPipelineRunSoftQuotaManager.AcquireQuotaAsync(workspaceIdentity, softQuotaUsageEntity).ConfigureAwait(false);

                    _counterManager.GetNumberCounter(Constants.AcquireRunningPipelineRunSoftQuotaSuccessNumberCounter).Set(1);
                    return result;
                }
            }
            catch (Exception ex)
            {
                _counterManager.GetNumberCounter(Constants.AcquireRunningPipelineRunSoftQuotaFailureNumberCounter).Set(1);
                CommonLogger.LogEntityError(
                    experimentId,
                    "Failed to acquire RunningPipelineRun soft quota for WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}. Exception: {exception}",
                    workspaceName,
                    resourceGroupName,
                    subscriptionId,
                    ex);
                throw;
            }
        }

        [HttpDelete]
        [Route("PipelineRuns/{experimentId}")]
        public async Task<bool> ReleaseSoftQuotaAsync(
            string subscriptionId,
            string resourceGroupName,
            string workspaceName,
            string experimentId)
        {
            CommonLogger.LogEntityInfo(
                experimentId,
                "Release RunningPipelineRun soft quota for WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}",
                workspaceName,
                resourceGroupName,
                subscriptionId);
            _counterManager.GetNumberCounter(Constants.ReleaseRunningPipelineRunSoftQuotaNumberCounter).Set(1);
            try
            {
                var workspaceIdentity = await GetWorkspaceIdentityAsync(subscriptionId, resourceGroupName, workspaceName);

                using (new DisposableTimer(_counterManager.GetLatencyCounter(Constants.ReleaseRunningPipelineRunSoftQuotaLatencyCounter)))
                {
                    var softQuotaUsageEntity = GetRunningPipelineRunSoftQuotaUsage(experimentId, workspaceIdentity);
                    bool result = await _runningPipelineRunSoftQuotaManager.ReleaseQuotaAsync(workspaceIdentity, softQuotaUsageEntity).ConfigureAwait(false);

                    _counterManager.GetNumberCounter(Constants.ReleaseRunningPipelineRunSoftQuotaSuccessNumberCounter).Set(1);
                    return result;
                }
            }
            catch (Exception ex)
            {
                _counterManager.GetNumberCounter(Constants.ReleaseRunningPipelineRunSoftQuotaFailureNumberCounter).Set(1);
                CommonLogger.LogEntityError(
                    experimentId,
                    "Failed to release RunningPipelineRun soft quota for WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}. Exception: {exception}",
                    workspaceName,
                    resourceGroupName,
                    subscriptionId,
                    ex);
                throw;
            }
        }

        [HttpGet]
        [Route("quota")]
        public async Task<QuotaResponse> GetSoftQuotaAsync(
            string subscriptionId,
            string resourceGroupName,
            string workspaceName)
        {
            CommonLogger.LogInfo(
                "Get RunningPipelineRun soft quota for WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}",
                workspaceName,
                resourceGroupName,
                subscriptionId);

            _counterManager.GetNumberCounter(Constants.GetRunningPipelineRunSoftQuotaNumberCounter).Set(1);
            try
            {
                var workspaceIdentity = await GetWorkspaceIdentityAsync(subscriptionId, resourceGroupName, workspaceName);

                using (new DisposableTimer(_counterManager.GetLatencyCounter(Constants.GetRunningPipelineRunSoftQuotaLatencyCounter)))
                {
                    var result = await _runningPipelineRunSoftQuotaManager.GetCurrentQuotaAsync(workspaceIdentity).ConfigureAwait(false);
                    _counterManager.GetNumberCounter(Constants.GetRunningPipelineRunSoftQuotaSuccessNumberCounter).Set(1);
                    return result;
                }
            }
            catch (Exception ex)
            {
                _counterManager.GetNumberCounter(Constants.GetRunningPipelineRunSoftQuotaFailureNumberCounter).Set(1);
                CommonLogger.LogError(
                    "Failed to get RunningPipelineRun soft quota for WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}. Exception: {exception}",
                    workspaceName,
                    resourceGroupName,
                    subscriptionId,
                    ex);
                throw;
            }
        }

        [HttpPut]
        [Route("quotalimit")]
        public async Task<RunningPipelineRunQuotaLimitEntity> UpdateSoftQuotaLimitAsync(
            string subscriptionId,
            string resourceGroupName,
            string workspaceName,
            [FromBody] QuotaLimitRequest quotaLimitRequest)
        {
            CommonLogger.LogInfo(
                "Configure RunningPipelineRun max soft quota limit to {maxLimit} for WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}",
                quotaLimitRequest.MaxLimit,
                workspaceName,
                resourceGroupName,
                subscriptionId);
            try
            {
                var workspaceIdentity = await GetWorkspaceIdentityAsync(subscriptionId, resourceGroupName, workspaceName);
                var result = await _runningPipelineRunSoftQuotaManager.SetQuotaLimitAsync(workspaceIdentity, quotaLimitRequest.MaxLimit).ConfigureAwait(false);
                return result;
            }
            catch (Exception ex)
            {
                CommonLogger.LogError(
                    "Failed to configure RunningPipelineRun max soft quota limit to {maxLimit} for WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}. Exception: {exception}",
                    quotaLimitRequest.MaxLimit,
                    workspaceName,
                    resourceGroupName,
                    subscriptionId,
                    ex);
                throw;
            }
        }

        private static RunningPipelineRunQuotaUsageEntity GetRunningPipelineRunSoftQuotaUsage(string experimentId, WorkspaceIdentity workspaceIdentity, string parentExperimentId = null)
        {
            return new RunningPipelineRunQuotaUsageEntity()
            {
                Id = experimentId,
                Workspace = workspaceIdentity,
                CreatedTimeUtc = DateTime.UtcNow,
                UsageCount = 1,
                IsSoftQuota = true, // Occupy soft quota usage
                ParentExperimentId = parentExperimentId,
            };
        }

        private async Task<WorkspaceIdentity> GetWorkspaceIdentityAsync(string subscriptionId, string resourceGroupName, string workspaceName)
        {
            string workspaceId = await _workspaceResourcesCache.GetWorkspaceIdAsync(
                subscriptionId: subscriptionId,
                resourceGroupName: resourceGroupName,
                workspaceName: workspaceName,
                createdBy: CreatedBy.GetSystemCreatedByObject());

            return new WorkspaceIdentity()
            {
                SubscriptionId = subscriptionId,
                ResourceGroupName = resourceGroupName,
                WorkspaceId = workspaceId,
                WorkspaceName = workspaceName,
            };
        }
    }
}
