﻿// <copyright file="CachedPipelineRunController.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Microsoft.Aether.BackendCommon;
using Microsoft.Aether.DataContracts;
using Microsoft.Aether.DataContracts.Entity;
using Microsoft.Aether.QuotaService;
using Microsoft.Aether.QuotaService.Shared;
using Microsoft.Aether.QuotaService.Shared.Controllers;
using Microsoft.AspNetCore.Mvc;
using Microsoft.RelInfra.Instrumentation;
using Microsoft.RelInfra.Instrumentation.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Microsoft.Aether.BBQuotaService
{
    [ApiController]
    [ApiVersion("1.0")]
    [Route(Constants.RoutePrefix)]
    public class CachedPipelineRunController : Controller, ICachedPipelineRunController
    {
        private readonly CounterManager _counterManager;
        private readonly ICachedPipelineRunManager _cachedPipelineRunManager;
        private readonly IWorkspaceResourcesCache _workspaceResourcesCache;

        public CachedPipelineRunController(
            CounterManager counterManager,
            ICachedPipelineRunManager cachedPipelineRunManager,
            IWorkspaceResourcesCache workspaceResourcesCache)
        {
            _counterManager = counterManager;
            _cachedPipelineRunManager = cachedPipelineRunManager;
            _workspaceResourcesCache = workspaceResourcesCache;
        }

        [HttpPost]
        [Route("{experimentId}")]
        public async Task<bool> AddCachedPipelineRunAsync(
            string subscriptionId,
            string resourceGroupName,
            string workspaceName,
            string experimentId,
            [FromBody] string queueMessage)
        {
            CommonLogger.LogInfo(
                "Add cached pipeline run {experimentId} for WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}",
                experimentId,
                workspaceName,
                resourceGroupName,
                subscriptionId);
            _counterManager.GetNumberCounter(Constants.AddCachedPipelineRunNumberCounter).Set(1);

            try
            {
                using (new DisposableTimer(_counterManager.GetLatencyCounter(Constants.AddCachedPipelineRunLatencyCounter)))
                {
                    var workspaceId = await GetWorkspaceIdentityIdAsync(subscriptionId, resourceGroupName, workspaceName, CreatedBy.GetSystemCreatedByObject()).ConfigureAwait(false);
                    var workspaceIdentity = GetWorkspaceIdentity(subscriptionId, resourceGroupName, workspaceName, workspaceId);
                    var cache = GetCachedPipelineRun(experimentId, workspaceIdentity, queueMessage);
                    var result = await _cachedPipelineRunManager.AddCachedPipelineRunAsync(workspaceIdentity, cache).ConfigureAwait(false);
                    _counterManager.GetNumberCounter(Constants.AddCachedPipelineRunSuccessNumberCounter).Set(1);
                    return result;
                }
            }
            catch (Exception ex)
            {
                _counterManager.GetNumberCounter(Constants.AddCachedPipelineRunFailureNumberCounter).Set(1);
                CommonLogger.LogError(
                    "Failed to add cached pipeline run {experimentId} for WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}. Exception: {exception}",
                    experimentId,
                    workspaceName,
                    resourceGroupName,
                    subscriptionId,
                    ex);
                throw;
            }
        }

        [HttpDelete]
        [Route("{experimentId}")]
        public async Task<bool> DeleteCachedPipelineRunAsync(
            string subscriptionId,
            string resourceGroupName,
            string workspaceName,
            string experimentId)
        {
            CommonLogger.LogInfo(
                "Delete cached pipeline run {experimentId} for WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}",
                experimentId,
                workspaceName,
                resourceGroupName,
                subscriptionId);
            _counterManager.GetNumberCounter(Constants.DeleteCachedPipelineRunNumberCounter).Set(1);

            try
            {
                using (new DisposableTimer(_counterManager.GetLatencyCounter(Constants.DeleteCachedPipelineRunLatencyCounter)))
                {
                    var workspaceId = await GetWorkspaceIdentityIdAsync(subscriptionId, resourceGroupName, workspaceName, CreatedBy.GetSystemCreatedByObject()).ConfigureAwait(false);
                    var workspaceIdentity = GetWorkspaceIdentity(subscriptionId, resourceGroupName, workspaceName, workspaceId);
                    var result = await _cachedPipelineRunManager.RemoveCachedPipelineRunAsync(workspaceIdentity, experimentId).ConfigureAwait(false);
                    _counterManager.GetNumberCounter(Constants.DeleteCachedPipelineRunSuccessNumberCounter).Set(1);
                    return result;
                }
            }
            catch (Exception ex)
            {
                _counterManager.GetNumberCounter(Constants.DeleteCachedPipelineRunFailureNumberCounter).Set(1);
                CommonLogger.LogError(
                    "Failed to delete cached pipeline run {experimentId} for WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}. Exception: {exception}",
                    experimentId,
                    workspaceName,
                    resourceGroupName,
                    subscriptionId,
                    ex);
                throw;
            }
        }

        [HttpDelete]
        [Route("cancelledRun/{experimentId}")]
        public async Task<bool> CancelCachedPipelineRunAsync(
            string subscriptionId,
            string resourceGroupName,
            string workspaceName,
            string experimentId)
        {
            CommonLogger.LogInfo(
                "Cancel cached pipeline run {experimentId} for WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}",
                experimentId,
                workspaceName,
                resourceGroupName,
                subscriptionId);
            _counterManager.GetNumberCounter(Constants.CancelCachedPipelineRunNumberCounter).Set(1);

            try
            {
                using (new DisposableTimer(_counterManager.GetLatencyCounter(Constants.CancelCachedPipelineRunLatencyCounter)))
                {
                    var workspaceId = await GetWorkspaceIdentityIdAsync(subscriptionId, resourceGroupName, workspaceName, CreatedBy.GetSystemCreatedByObject()).ConfigureAwait(false);
                    var workspaceIdentity = GetWorkspaceIdentity(subscriptionId, resourceGroupName, workspaceName, workspaceId);
                    var result = await _cachedPipelineRunManager.CancelCachedPipelineRunAsync(workspaceIdentity, experimentId).ConfigureAwait(false);
                    CommonLogger.LogInfo(
                        "Cancelled cached pipeline run {experimentId} for WorkspaceId={workspaceId}, WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}. Cancel result is {result}",
                        experimentId,
                        workspaceId,
                        workspaceName,
                        resourceGroupName,
                        subscriptionId,
                        result);
                    _counterManager.GetNumberCounter(Constants.CancelCachedPipelineRunSuccessNumberCounter).Set(1);

                    return result;
                }
            }
            catch (Exception ex)
            {
                _counterManager.GetNumberCounter(Constants.CancelCachedPipelineRunFailureNumberCounter).Set(1);
                CommonLogger.LogError(
                    "Failed to cancel cached pipeline run {experimentId} for WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}. Exception: {exception}",
                    experimentId,
                    workspaceName,
                    resourceGroupName,
                    subscriptionId,
                    ex);
                throw;
            }
        }

        [HttpDelete]
        [Route("allCached")]
        public async Task<bool> DeleteAllCachedPipelineRunsAsync(
            string subscriptionId,
            string resourceGroupName,
            string workspaceName,
            [FromQuery] string workspaceId = null)
        {
            CommonLogger.LogInfo(
                "Delete all cached pipeline runs for WorkspaceId={workspaceId}, WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}",
                workspaceId,
                workspaceName,
                resourceGroupName,
                subscriptionId);
            _counterManager.GetNumberCounter(Constants.DeleteAllCachedPipelineRunsNumberCounter).Set(1);

            try
            {
                // Should not call WorkspaceResourcesCache to get workspaceId like other API because the workspace may be already deleted.
                // Client should provide the workspaceId from query when call DeleteAllCachedPipelineRunsAsync. If workspaceId is null or empty, ignore it.
                if (string.IsNullOrEmpty(workspaceId))
                {
                    _counterManager.GetNumberCounter(Constants.DeleteAllCachedPipelineRunsFailureNumberCounter).Set(1);
                    CommonLogger.LogError(
                        "WorkspaceId should not be null or empty, failed to delete all cached pipeline runs for WorkspaceId={workspaceId}, WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}",
                        workspaceId,
                        workspaceName,
                        resourceGroupName,
                        subscriptionId);

                    return false;
                }

                using (new DisposableTimer(_counterManager.GetLatencyCounter(Constants.DeleteAllCachedPipelineRunsLatencyCounter)))
                {
                    var workspaceIdentity = GetWorkspaceIdentity(subscriptionId, resourceGroupName, workspaceName, workspaceId);
                    bool result = true;
                    string continuationToken = null;
                    do
                    {
                        var queryResult = await _cachedPipelineRunManager.GetAllCachedPipelineRunsAsync(workspaceIdentity, continuationToken);
                        var cachedPipelineRuns = queryResult.Results.Select(x => x.Entity);
                        continuationToken = queryResult.ContinuationToken;

                        foreach (var cached in cachedPipelineRuns)
                        {
                            var deleted = await _cachedPipelineRunManager.RemoveCachedPipelineRunAsync(workspaceIdentity, cached.Id).ConfigureAwait(false);
                            if (!deleted)
                            {
                                result = false;
                                CommonLogger.LogInfo(
                                    "Failed to delete cached pipeline run {experimentId} for WorkspaceId={workspaceId}, WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}. Resource may already be deleted and not exist",
                                    cached.Id,
                                    workspaceId,
                                    workspaceName,
                                    resourceGroupName,
                                    subscriptionId);
                            }
                        }
                    }
                    while (continuationToken != null);

                    CommonLogger.LogInfo(
                        "Deleted all cached pipeline runs for WorkspaceId={workspaceId}, WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}. Delete result is {result}",
                        workspaceId,
                        workspaceName,
                        resourceGroupName,
                        subscriptionId,
                        result);
                    _counterManager.GetNumberCounter(Constants.DeleteAllCachedPipelineRunsSuccessNumberCounter).Set(1);

                    return result;
                }
            }
            catch (Exception ex)
            {
                _counterManager.GetNumberCounter(Constants.DeleteAllCachedPipelineRunsFailureNumberCounter).Set(1);
                CommonLogger.LogError(
                    "Failed to delete all cached pipeline runs for WorkspaceId={workspaceId}, WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}. Exception: {exception}",
                    workspaceId,
                    workspaceName,
                    resourceGroupName,
                    subscriptionId,
                    ex);
                throw;
            }
        }

        [HttpGet]
        [Route("allCached")]
        public async Task<IEnumerable<CachedPipelineRunEntity>> GetAllCachedPipelineRunsAsync(
            string subscriptionId,
            string resourceGroupName,
            string workspaceName)
        {
            CommonLogger.LogInfo(
                "Get all cached pipeline runs for WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}",
                workspaceName,
                resourceGroupName,
                subscriptionId);
            _counterManager.GetNumberCounter(Constants.GetAllCachedPipelineRunsNumberCounter).Set(1);

            try
            {
                using (new DisposableTimer(_counterManager.GetLatencyCounter(Constants.GetAllCachedPipelineRunsLatencyCounter)))
                {
                    var workspaceId = await GetWorkspaceIdentityIdAsync(subscriptionId, resourceGroupName, workspaceName, CreatedBy.GetSystemCreatedByObject()).ConfigureAwait(false);
                    var workspaceIdentity = GetWorkspaceIdentity(subscriptionId, resourceGroupName, workspaceName, workspaceId);
                    IEnumerable<CachedPipelineRunEntity> result = new List<CachedPipelineRunEntity>();
                    string continuationToken = null;
                    do
                    {
                        var queryResult = await _cachedPipelineRunManager.GetAllCachedPipelineRunsAsync(workspaceIdentity, continuationToken).ConfigureAwait(false);
                        continuationToken = queryResult.ContinuationToken;
                        result = result.Concat(queryResult.Results.Select(r => r.Entity));
                    }
                    while (continuationToken != null);

                    CommonLogger.LogInfo(
                        "Get all cached pipeline runs successfully for WorkspaceId={workspaceId}, WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}. Cancel result is {result}",
                        workspaceId,
                        workspaceName,
                        resourceGroupName,
                        subscriptionId,
                        result);
                    _counterManager.GetNumberCounter(Constants.GetAllCachedPipelineRunsSuccessNumberCounter).Set(1);

                    return result;
                }
            }
            catch (Exception ex)
            {
                _counterManager.GetNumberCounter(Constants.GetAllCachedPipelineRunsFailureNumberCounter).Set(1);
                CommonLogger.LogError(
                    "Failed to get all cached pipeline runs for WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}. Exception: {exception}",
                    workspaceName,
                    resourceGroupName,
                    subscriptionId,
                    ex);
                throw;
            }
        }

        private static CachedPipelineRunEntity GetCachedPipelineRun(string experimentId, WorkspaceIdentity workspaceIdentity, string queueMessage = null)
        {
            return new CachedPipelineRunEntity()
            {
                Id = experimentId,
                Workspace = workspaceIdentity,
                CachedTimeUtc = DateTime.UtcNow,
                QueueMessage = queueMessage,
            };
        }

        private static WorkspaceIdentity GetWorkspaceIdentity(string subscriptionId, string resourceGroupName, string workspaceName, string workspaceId)
        {
            return new WorkspaceIdentity()
            {
                SubscriptionId = subscriptionId,
                ResourceGroupName = resourceGroupName,
                WorkspaceId = workspaceId,
                WorkspaceName = workspaceName,
            };
        }

        private async Task<string> GetWorkspaceIdentityIdAsync(
            string subscriptionId,
            string resourceGroupName,
            string workspaceName,
            CreatedBy createdBy)
        {
            return await _workspaceResourcesCache.GetWorkspaceIdAsync(
                subscriptionId: subscriptionId,
                resourceGroupName: resourceGroupName,
                workspaceName: workspaceName,
                createdBy: createdBy);
        }
    }
}
