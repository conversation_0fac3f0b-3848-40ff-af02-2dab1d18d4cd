﻿// <copyright file="RunningPipelineRunQuotaController.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Microsoft.Aether.BackendCommon;
using Microsoft.Aether.DataContracts;
using Microsoft.Aether.DataContracts.Entity;
using Microsoft.Aether.QuotaService;
using Microsoft.Aether.QuotaService.Shared;
using Microsoft.Aether.QuotaService.Shared.Controllers;
using Microsoft.AspNetCore.Mvc;
using Microsoft.RelInfra.Instrumentation;
using Microsoft.RelInfra.Instrumentation.Logging;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace Microsoft.Aether.BBQuotaService
{
    [ApiController]
    [ApiVersion("1.0")]
    [Route(Constants.RoutePrefix)]
    public class RunningPipelineRunQuotaController : Controller, IRunningPipelineRunQuotaController
    {
        private readonly CounterManager _counterManager;
        private readonly RunningPipelineRunQuotaManager _runningPipelineRunQuotaManager;
        private readonly IWorkspaceResourcesCache _workspaceResourcesCache;

        public RunningPipelineRunQuotaController(
            CounterManager counterManager,
            RunningPipelineRunQuotaManager runningPipelineRunQuotaManager,
            IWorkspaceResourcesCache workspaceResourcesCache)
        {
            _counterManager = counterManager;
            _runningPipelineRunQuotaManager = runningPipelineRunQuotaManager;
            _workspaceResourcesCache = workspaceResourcesCache;
        }

        [HttpPost]
        [Route("PipelineRuns/{experimentId}")]
        public async Task<QuotaResponse> AcquireQuotaAsync(
            string subscriptionId,
            string resourceGroupName,
            string workspaceName,
            string experimentId,
            [FromQuery] string parentExperimentId = null)
        {
            CommonLogger.LogEntityInfo(
                experimentId,
                "Acquire RunningPipelineRun quota for WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}",
                workspaceName,
                resourceGroupName,
                subscriptionId);
            _counterManager.GetNumberCounter(Constants.AcquireRunningPipelineRunQuotaNumberCounter).Set(1);
            try
            {
                var workspaceId = await GetWorkspaceIdentityIdAsync(subscriptionId, resourceGroupName, workspaceName, CreatedBy.GetSystemCreatedByObject()).ConfigureAwait(false);
                var workspaceIdentity = GetWorkspaceIdentity(subscriptionId, resourceGroupName, workspaceName, workspaceId);

                using (new DisposableTimer(_counterManager.GetLatencyCounter(Constants.AcquireRunningPipelineRunQuotaLatencyCounter)))
                {
                    var quotaResourceEntity = GetRunningPipelineRunQuotaUsage(experimentId, workspaceIdentity, parentExperimentId);
                    var result = await _runningPipelineRunQuotaManager.AcquireQuotaAsync(workspaceIdentity, quotaResourceEntity).ConfigureAwait(false);

                    _counterManager.GetNumberCounter(Constants.AcquireRunningPipelineRunQuotaSuccessNumberCounter).Set(1);
                    return result;
                }
            }
            catch (Exception ex)
            {
                _counterManager.GetNumberCounter(Constants.AcquireRunningPipelineRunQuotaFailureNumberCounter).Set(1);
                CommonLogger.LogEntityError(
                    experimentId,
                    "Failed to acquire RunningPipelineRun quota for WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}. Exception: {exception}",
                    workspaceName,
                    resourceGroupName,
                    subscriptionId,
                    ex);
                throw;
            }
        }

        [HttpDelete]
        [Route("PipelineRuns/{experimentId}")]
        public async Task<bool> ReleaseQuotaAsync(
            string subscriptionId,
            string resourceGroupName,
            string workspaceName,
            string experimentId)
        {
            CommonLogger.LogEntityInfo(
                experimentId,
                "Release RunningPipelineRun quota for WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}",
                workspaceName,
                resourceGroupName,
                subscriptionId);
            _counterManager.GetNumberCounter(Constants.ReleaseRunningPipelineRunQuotaNumberCounter).Set(1);
            try
            {
                var workspaceId = await GetWorkspaceIdentityIdAsync(subscriptionId, resourceGroupName, workspaceName, CreatedBy.GetSystemCreatedByObject()).ConfigureAwait(false);
                var workspaceIdentity = GetWorkspaceIdentity(subscriptionId, resourceGroupName, workspaceName, workspaceId);

                using (new DisposableTimer(_counterManager.GetLatencyCounter(Constants.ReleaseRunningPipelineRunQuotaLatencyCounter)))
                {
                    var quotaResourceEntity = GetRunningPipelineRunQuotaUsage(experimentId, workspaceIdentity);
                    bool result = await _runningPipelineRunQuotaManager.ReleaseQuotaAsync(workspaceIdentity, quotaResourceEntity).ConfigureAwait(false);

                    _counterManager.GetNumberCounter(Constants.ReleaseRunningPipelineRunQuotaSuccessNumberCounter).Set(1);
                    return result;
                }
            }
            catch (Exception ex)
            {
                _counterManager.GetNumberCounter(Constants.ReleaseRunningPipelineRunQuotaFailureNumberCounter).Set(1);
                CommonLogger.LogEntityError(
                    experimentId,
                    "Failed to release RunningPipelineRun quota for WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}. Exception: {exception}",
                    workspaceName,
                    resourceGroupName,
                    subscriptionId,
                    ex);
                throw;
            }
        }

        [HttpGet]
        [Route("quota")]
        public async Task<QuotaResponse> GetQuotaAsync(
            string subscriptionId,
            string resourceGroupName,
            string workspaceName)
        {
            CommonLogger.LogInfo(
                "Get RunningPipelineRun quota for WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}",
                workspaceName,
                resourceGroupName,
                subscriptionId);

            _counterManager.GetNumberCounter(Constants.GetRunningPipelineRunQuotaNumberCounter).Set(1);
            try
            {
                var workspaceId = await GetWorkspaceIdentityIdAsync(subscriptionId, resourceGroupName, workspaceName, CreatedBy.GetSystemCreatedByObject()).ConfigureAwait(false);
                using (new DisposableTimer(_counterManager.GetLatencyCounter(Constants.GetRunningPipelineRunQuotaLatencyCounter)))
                {
                    var workspaceIdentity = GetWorkspaceIdentity(subscriptionId, resourceGroupName, workspaceName, workspaceId);
                    var result = await _runningPipelineRunQuotaManager.GetCurrentQuotaAsync(workspaceIdentity).ConfigureAwait(false);
                    _counterManager.GetNumberCounter(Constants.GetRunningPipelineRunQuotaSuccessNumberCounter).Set(1);
                    return result;
                }
            }
            catch (Exception ex)
            {
                _counterManager.GetNumberCounter(Constants.GetRunningPipelineRunQuotaFailureNumberCounter).Set(1);
                CommonLogger.LogError(
                    "Failed to get RunningPipelineRun quota for WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}. Exception: {exception}",
                    workspaceName,
                    resourceGroupName,
                    subscriptionId,
                    ex);
                throw;
            }
        }

        [HttpPut]
        [Route("quotalimit")]
        public async Task<RunningPipelineRunQuotaLimitEntity> UpdateQuotaLimitAsync(
            string subscriptionId,
            string resourceGroupName,
            string workspaceName,
            [FromBody] QuotaLimitRequest quotaLimitRequest)
        {
            CommonLogger.LogInfo(
                "Configuring RunningPipelineRun max quota limit to {maxLimit} for WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}",
                quotaLimitRequest.MaxLimit,
                workspaceName,
                resourceGroupName,
                subscriptionId);
            try
            {
                var workspaceId = await GetWorkspaceIdentityIdAsync(subscriptionId, resourceGroupName, workspaceName, CreatedBy.GetSystemCreatedByObject()).ConfigureAwait(false);
                var workspaceIdentity = GetWorkspaceIdentity(subscriptionId, resourceGroupName, workspaceName, workspaceId);
                var result = await _runningPipelineRunQuotaManager.SetQuotaLimitAsync(workspaceIdentity, quotaLimitRequest.MaxLimit).ConfigureAwait(false);
                return result;
            }
            catch (Exception ex)
            {
                CommonLogger.LogError(
                    "Failed to configure RunningPipelineRun max quota limit to {maxLimit} for WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}. Exception: {exception}",
                    quotaLimitRequest.MaxLimit,
                    workspaceName,
                    resourceGroupName,
                    subscriptionId,
                    ex);
                throw;
            }
        }

        [HttpDelete]
        [Route("quotalimit")]
        public async Task<bool> DeleteQuotaLimitAsync(
            string subscriptionId,
            string resourceGroupName,
            string workspaceName,
            [FromQuery] string workspaceId = null)
        {
            CommonLogger.LogInfo(
                "Delete RunningPipelineRun quota limit for WorkspaceId={workspaceId}, WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}",
                workspaceId,
                workspaceName,
                resourceGroupName,
                subscriptionId);
            _counterManager.GetNumberCounter(Constants.DeleteRunningPipelineRunQuotaLimitNumberCounter).Set(1);

            try
            {
                // Should not call WorkspaceResourcesCache to get workspaceId like other API because the workspace may be already deleted.
                // Client should provide the workspaceId from query when call DeleteQuotaAsync. If workspaceId is null or empty, ignore it.
                if (string.IsNullOrEmpty(workspaceId))
                {
                    _counterManager.GetNumberCounter(Constants.DeleteRunningPipelineRunQuotaLimitFailureNumberCounter).Set(1);
                    CommonLogger.LogError(
                        "WorkspaceId should not be null or empty, failed to delete RunningPipelineRun quota limit for WorkspaceId={workspaceId}, WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}",
                        workspaceId,
                        workspaceName,
                        resourceGroupName,
                        subscriptionId);

                    return false;
                }

                using (new DisposableTimer(_counterManager.GetLatencyCounter(Constants.DeleteRunningPipelineRunQuotaLimitLatencyCounter)))
                {
                    var workspaceIdentity = GetWorkspaceIdentity(subscriptionId, resourceGroupName, workspaceName, workspaceId);
                    // Resource may not exist if workspace has never submitted pipeline runs, in this case RemoveQuotaAsync will return false
                    var result = await _runningPipelineRunQuotaManager.RemoveQuotaLimitAsync(workspaceIdentity).ConfigureAwait(false);
                    CommonLogger.LogInfo(
                        "Deleted RunningPipelineRun quota limit for WorkspaceId={workspaceId}, WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}. Delete result is {result}",
                        workspaceId,
                        workspaceName,
                        resourceGroupName,
                        subscriptionId,
                        result);
                    _counterManager.GetNumberCounter(Constants.DeleteRunningPipelineRunQuotaLimitSuccessNumberCounter).Set(1);

                    return result;
                }
            }
            catch (Exception ex)
            {
                _counterManager.GetNumberCounter(Constants.DeleteRunningPipelineRunQuotaLimitFailureNumberCounter).Set(1);
                CommonLogger.LogError(
                    "Failed to delete RunningPipelineRun quota limit for WorkspaceId={workspaceId}, WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}. Exception: {exception}",
                    workspaceId,
                    workspaceName,
                    resourceGroupName,
                    subscriptionId,
                    ex);
                throw;
            }
        }

        [HttpDelete]
        [Route("quotausages")]
        public async Task<bool> DeleteUsagesAsync(
            string subscriptionId,
            string resourceGroupName,
            string workspaceName,
            [FromQuery] string workspaceId = null)
        {
            CommonLogger.LogInfo(
                "Delete RunningPipelineRun all usages for WorkspaceId={workspaceId}, WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}",
                workspaceId,
                workspaceName,
                resourceGroupName,
                subscriptionId);
            _counterManager.GetNumberCounter(Constants.DeleteRunningPipelineRunQuotaUsagesNumberCounter).Set(1);

            try
            {
                // Should not call WorkspaceResourcesCache to get workspaceId like other API because the workspace may be already deleted.
                // Client should provide the workspaceId from query when call DeleteUsagesAsync. If workspaceId is null or empty, ignore it.
                if (string.IsNullOrEmpty(workspaceId))
                {
                    _counterManager.GetNumberCounter(Constants.DeleteRunningPipelineRunQuotaUsagesFailureNumberCounter).Set(1);
                    CommonLogger.LogError(
                        "WorkspaceId should not be null or empty, failed to delete RunningPipelineRun all usages for WorkspaceId={workspaceId}, WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}",
                        workspaceId,
                        workspaceName,
                        resourceGroupName,
                        subscriptionId);

                    return false;
                }

                using (new DisposableTimer(_counterManager.GetLatencyCounter(Constants.DeleteRunningPipelineRunQuotaUsagesLatencyCounter)))
                {
                    var workspaceIdentity = GetWorkspaceIdentity(subscriptionId, resourceGroupName, workspaceName, workspaceId);
                    var usages = await _runningPipelineRunQuotaManager.GetAllUsagesAsync(workspaceIdentity).ConfigureAwait(false);

                    bool result = true;
                    foreach (var usage in usages)
                    {
                        var deleted = await _runningPipelineRunQuotaManager.RemoveUsageAsync(workspaceIdentity, usage).ConfigureAwait(false);
                        if (!deleted)
                        {
                            result = false;
                            CommonLogger.LogInfo(
                                "Failed to delete RunningPipelineRun usage for ExperimentId={experimentId}, WorkspaceId={workspaceId}, WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}. Resource may already be deleted and not exist",
                                usage.Id,
                                workspaceId,
                                workspaceName,
                                resourceGroupName,
                                subscriptionId);
                        }
                    }

                    CommonLogger.LogInfo(
                        "Deleted RunningPipelineRun all usages for WorkspaceId={workspaceId}, WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}. Delete result is {result}, total usages count is {usageCount}",
                        workspaceId,
                        workspaceName,
                        resourceGroupName,
                        subscriptionId,
                        result,
                        usages.Count());
                    _counterManager.GetNumberCounter(Constants.DeleteRunningPipelineRunQuotaUsagesSuccessNumberCounter).Set(1);

                    return result;
                }
            }
            catch (Exception ex)
            {
                _counterManager.GetNumberCounter(Constants.DeleteRunningPipelineRunQuotaUsagesFailureNumberCounter).Set(1);
                CommonLogger.LogError(
                    "Failed to delete RunningPipelineRun all usages for WorkspaceId={workspaceId}, WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}. Exception: {exception}",
                    workspaceId,
                    workspaceName,
                    resourceGroupName,
                    subscriptionId,
                    ex);
                throw;
            }
        }

        private static RunningPipelineRunQuotaUsageEntity GetRunningPipelineRunQuotaUsage(string experimentId, WorkspaceIdentity workspaceIdentity, string parentExperimentId = null)
        {
            return new RunningPipelineRunQuotaUsageEntity()
            {
                Id = experimentId,
                Workspace = workspaceIdentity,
                CreatedTimeUtc = DateTime.UtcNow,
                UsageCount = 1,
                ParentExperimentId = parentExperimentId,
            };
        }

        private static WorkspaceIdentity GetWorkspaceIdentity(string subscriptionId, string resourceGroupName, string workspaceName, string workspaceId)
        {
            return new WorkspaceIdentity()
            {
                SubscriptionId = subscriptionId,
                ResourceGroupName = resourceGroupName,
                WorkspaceId = workspaceId,
                WorkspaceName = workspaceName,
            };
        }

        private async Task<string> GetWorkspaceIdentityIdAsync(
            string subscriptionId,
            string resourceGroupName,
            string workspaceName,
            CreatedBy createdBy)
        {
            return await _workspaceResourcesCache.GetWorkspaceIdAsync(
                subscriptionId: subscriptionId,
                resourceGroupName: resourceGroupName,
                workspaceName: workspaceName,
                createdBy: createdBy);
        }
    }
}
