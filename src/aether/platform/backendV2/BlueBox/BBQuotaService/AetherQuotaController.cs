﻿// <copyright file="AetherQuotaController.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using Microsoft.Aether.QuotaService.Shared;
using Microsoft.AspNetCore.Mvc;
using Microsoft.RelInfra.Instrumentation;
using Microsoft.RelInfra.Instrumentation.Logging;
using Microsoft.Aether.QuotaService;

namespace Microsoft.Aether.BBQuotaService
{
    [ApiController]
    public class AetherQuotaController : Controller
    {
        private readonly CounterManager _counterManager;
        private readonly AetherQuotaManager _aetherQuotaManager;
        private readonly Dictionary<AetherQuotaType, ResourceManager> _resourceManagers;
        private readonly QuotaServiceConfig _serviceConfig;

        public AetherQuotaController(
            CounterManager counterManager,
            AetherQuotaManager aetherQuotaManager,
            Dictionary<AetherQuotaType, ResourceManager> resourceManagers,
            QuotaServiceConfig serviceConfig)
        {
            _counterManager = counterManager;
            _aetherQuotaManager = aetherQuotaManager;
            _resourceManagers = resourceManagers;
            _serviceConfig = serviceConfig;
        }

        [HttpGet]
        [Route("jobsquota/{teamId}/{userName}/{experimentId}/{jobsCount}")]
        public async Task<ActionResult<QuotaReservationResponse>> ReserveJobsQuotaAsync(
            string experimentId,
            string teamId,
            string userName,
            int jobsCount)
        {
            CommonLogger.LogEntityDebug(experimentId,
                                        "GET jobsquota/{team_id}/{user_name}/{experiment_id}/{jobs_count}",
                                        teamId,
                                        userName,
                                        experimentId,
                                        jobsCount);
            _counterManager.GetRateCounter("TryReserveQuotaRate").Increment();
            try
            {
                using (new DisposableTimer(_counterManager.GetLatencyCounter("TryReserveQuotaLatency")))
                {
                    QuotaReservationResponse quotaReservationEntity = await _aetherQuotaManager.ReserveTeamUserQuotaAsync(
                        userId: userName,
                        resourceId: experimentId,
                        resourceCount: jobsCount,
                        resourceManager: _resourceManagers[AetherQuotaType.Node],
                        userResourceUsageLimit: _serviceConfig.NodesLimit,
                        quotaExemptUsers: _serviceConfig.NodeExemption,
                        teamId: teamId,
                        expirationInterval: _serviceConfig.SubmissionExpirationInterval);

                    _counterManager.GetRateCounter("TryReserveQuotaSuccessRate").Increment();
                    return quotaReservationEntity;
                }
            }
            catch (Exception e)
            {
                _counterManager.GetRateCounter("TryReserveQuotaFailureRate").Increment();
                CommonLogger.LogEntityWarning(experimentId, "Failed to reserve jobs quota. Exception: {exception}", e);
                return new ContentResult
                {
                    Content = $"Failed to reserve jobs quota. Exception: {PrepareExceptionMessage(e)}",
                    ContentType = "text/plain",
                    StatusCode = (int)HttpStatusCode.InternalServerError
                };
            }
        }

        [HttpGet]
        [Route("runningjobsquota/{teamId}/{userName}/{experimentId}")]
        public async Task<ResourceUsageEntity> GetExperimentRunningNodeUsage(string experimentId, string userName)
        {
            return await _aetherQuotaManager.GetUserResourceUsageAsync(userName, experimentId, _resourceManagers[AetherQuotaType.RunningNode]);
        }

        [HttpPut]
        [Route("runningjobsquota/{teamId}/{userName}/{experimentId}")]
        public async Task<ActionResult<QuotaReservationResponse>> ReserveRunningJobsQuotaAsync(
            string experimentId,
            string teamId,
            string userName,
            [FromBody] int jobsCount)
        {
            CommonLogger.LogEntityDebug(experimentId,
                                        "Reserve runningjobsquota/{team_id}/{user_name}/{experiment_id}/{jobs_count}",
                                        teamId,
                                        userName,
                                        experimentId,
                                        jobsCount);
            _counterManager.GetRateCounter("TryReserveRunningJobsQuotaRate").Increment();
            try
            {
                using (new DisposableTimer(_counterManager.GetLatencyCounter("TryReserveRunningJobsQuotaLatency")))
                {
                    QuotaReservationResponse quotaReservationEntity = await _aetherQuotaManager.ReserveUserQuotaAsync(
                        userId: userName,
                        resourceId: experimentId,
                        resourceCount: jobsCount,
                        resourceManager: _resourceManagers[AetherQuotaType.RunningNode],
                        userResourceUsageLimit: _serviceConfig.RunningNodesLimit,
                        quotaExemptUsers: _serviceConfig.NodeExemption,
                        expirationInterval: _serviceConfig.RunningJobExpirationInterval);

                    _counterManager.GetRateCounter("TryReserveRunningJobsQuotaSuccessRate").Increment();
                    return quotaReservationEntity;
                }
            }
            catch (Exception e)
            {
                _counterManager.GetRateCounter("TryReserveRunningJobsQuotaFailureRate").Increment();
                CommonLogger.LogEntityWarning(experimentId, "Failed to reserve running jobs quota. Exception: {exception}", e);
                return new ContentResult
                {
                    Content = $"Failed to reserve running jobs quota. Exception: {PrepareExceptionMessage(e)}",
                    ContentType = "text/plain",
                    StatusCode = (int)HttpStatusCode.InternalServerError
                };
            }
        }

        [HttpPost]
        [Route("runningjobsquota/{teamId}/{userName}/{experimentId}")]
        public async Task<ActionResult<QuotaReservationResponse>> IncrementRunningJobsQuotaAsync(
            string experimentId,
            string teamId,
            string userName,
            [FromBody] int incrementJobsCount)
        {
            CommonLogger.LogEntityDebug(experimentId,
                                        "Increment runningjobsquota/{team_id}/{user_name}/{experiment_id}/{increment_jobs_count}",
                                        teamId,
                                        userName,
                                        experimentId,
                                        incrementJobsCount);
            _counterManager.GetRateCounter("TryIncrementRunningJobsQuotaRate").Increment();
            try
            {
                using (new DisposableTimer(_counterManager.GetLatencyCounter("TryIncrementRunningJobsQuotaLatency")))
                {
                    QuotaReservationResponse quotaReservationEntity = await _aetherQuotaManager.IncrementUserQuotaAsync(
                        userId: userName,
                        resourceId: experimentId,
                        incrementResourceCount: incrementJobsCount,
                        resourceManager: _resourceManagers[AetherQuotaType.RunningNode],
                        userResourceUsageLimit: _serviceConfig.RunningNodesLimit,
                        quotaExemptUsers: _serviceConfig.NodeExemption,
                        expirationInterval: _serviceConfig.RunningJobExpirationInterval);

                    _counterManager.GetRateCounter("TryIncrementRunningJobsQuotaSuccessRate").Increment();
                    return quotaReservationEntity;
                }
            }
            catch (Exception e)
            {
                _counterManager.GetRateCounter("TryIncrementRunningJobsQuotaFailureRate").Increment();
                CommonLogger.LogEntityWarning(experimentId, "Failed to increment running jobs quota. Exception: {exception}", e);
                return new ContentResult
                {
                    Content = $"Failed to increment running jobs quota. Exception: {PrepareExceptionMessage(e)}",
                    ContentType = "text/plain",
                    StatusCode = (int)HttpStatusCode.InternalServerError
                };
            }
        }

        [HttpDelete]
        [Route("runningjobsquota/{teamId}/{userName}/{experimentId}")]
        public async Task<IActionResult> ReleaseRunningJobsQuotaAsync(
        string experimentId,
        string teamId,
        string userName)
        {
            CommonLogger.LogEntityDebug(experimentId,
                                        "Releasing runningjobsquota/{team_id}/{user_name}/{experiment_id}",
                                        teamId,
                                        userName,
                                        experimentId);
            _counterManager.GetRateCounter("TryReleaseRunningJobsQuotaRate").Increment();
            try
            {
                using (new DisposableTimer(_counterManager.GetLatencyCounter("TryReleaseRunningJobsQuotaLatency")))
                {
                    QuotaReservationResponse quotaReservationEntity = await _aetherQuotaManager.ReleaseUserQuotaAsync(
                        userId: userName,
                        resourceId: experimentId,
                        resourceManager: _resourceManagers[AetherQuotaType.RunningNode],
                        quotaExemptUsers: _serviceConfig.NodeExemption,
                        expirationInterval: _serviceConfig.RunningJobExpirationInterval);

                    _counterManager.GetRateCounter("TryReleaseRunningJobsQuotaSuccessRate").Increment();

                    return NoContent();
                }
            }
            catch (Exception e)
            {
                _counterManager.GetRateCounter("TryReleaseRunningJobsQuotaFailureRate").Increment();
                CommonLogger.LogEntityWarning(experimentId, "Failed to release running jobs quota. Exception: {exception}", e);
                return new ContentResult
                {
                    Content = $"Failed to release running jobs quota. Exception: {PrepareExceptionMessage(e)}",
                    ContentType = "text/plain",
                    StatusCode = (int)HttpStatusCode.InternalServerError
                };
            }
        }

        [HttpGet]
        [Route("email/{teamId}/{userName}/{emailId}")]
        public async Task<ActionResult<QuotaReservationResponse>> ReserveEmailsQuotaAsync(
            string emailId,
            string teamId,
            string userName)
        {
            CommonLogger.LogEntityDebug(emailId,
                                        "GET email/{team_id}/{user_name}/{email_id}",
                                        teamId,
                                        userName,
                                        emailId);
            _counterManager.GetRateCounter("TryReserveQuotaRate").Increment();
            try
            {
                using (new DisposableTimer(_counterManager.GetLatencyCounter("TryReserveQuotaLatency")))
                {
                    QuotaReservationResponse quotaReservationEntity = await _aetherQuotaManager.ReserveTeamUserQuotaAsync(
                        userId: userName,
                        resourceId: emailId,
                        resourceCount: 1,
                        resourceManager: _resourceManagers[AetherQuotaType.Email],
                        userResourceUsageLimit: _serviceConfig.EmailsLimit,
                        quotaExemptUsers: _serviceConfig.EmailExemption,
                        teamId: teamId,
                        expirationInterval: _serviceConfig.SubmissionExpirationInterval);

                    _counterManager.GetRateCounter("TryReserveQuotaSuccessRate").Increment();
                    return quotaReservationEntity;
                }
            }
            catch (Exception e)
            {
                _counterManager.GetRateCounter("TryReserveQuotaFailureRate").Increment();
                CommonLogger.LogEntityWarning(emailId, "Failed to reserve quota. Exception: {exception}", e);
                return new ContentResult
                {
                    Content = $"Failed to reserve emails quota. Exception: {PrepareExceptionMessage(e)}",
                    ContentType = "text/plain",
                    StatusCode = (int)HttpStatusCode.InternalServerError
                };
            }
        }

        [HttpGet]
        [Route("promotion/{teamId}/{userName}/{promotionTaskId}")]
        public async Task<ActionResult<QuotaReservationResponse>> ReservePromotionQuotaAsync(
            string promotionTaskId,
            string teamId,
            string userName)
        {
            CommonLogger.LogEntityDebug(promotionTaskId,
                                        "GET promotion/{team_id}/{user_name}/{promotion_task_id}",
                                        teamId,
                                        userName,
                                        promotionTaskId);
            _counterManager.GetRateCounter("TryReserveQuotaRate").Increment();
            try
            {
                using (new DisposableTimer(_counterManager.GetLatencyCounter("TryReserveQuotaLatency")))
                {
                    QuotaReservationResponse quotaReservationEntity = await _aetherQuotaManager.ReserveTeamUserQuotaAsync(
                        userId: userName,
                        resourceId: promotionTaskId,
                        resourceCount: 1,
                        resourceManager: _resourceManagers[AetherQuotaType.Promotion],
                        userResourceUsageLimit: _serviceConfig.PromotionsLimit,
                        quotaExemptUsers: _serviceConfig.PromotionsExemption,
                        teamId: teamId,
                        expirationInterval: _serviceConfig.SubmissionExpirationInterval);

                    _counterManager.GetRateCounter("TryReserveQuotaSuccessRate").Increment();
                    return quotaReservationEntity;
                }
            }
            catch (Exception e)
            {
                _counterManager.GetRateCounter("TryReserveQuotaFailureRate").Increment();
                CommonLogger.LogEntityWarning(promotionTaskId, "Failed to reserve promotion quota. Error: {exception}", e);
                return new ContentResult
                {
                    Content = $"Failed to reserve promotion quota. Exception: {PrepareExceptionMessage(e)}",
                    ContentType = "text/plain",
                    StatusCode = (int)HttpStatusCode.InternalServerError
                };
            }
        }

        [HttpGet]
        [Route("upload/{teamId}/{userName}/{uploadTaskId}")]
        public async Task<ActionResult<QuotaReservationResponse>> ReserveUploadQuotaAsync(
            string uploadTaskId,
            string teamId,
            string userName)
        {
            CommonLogger.LogEntityDebug(uploadTaskId,
                                        "GET upload/{team_id}/{user_name}/{upload_task_id}",
                                        teamId,
                                        userName,
                                        uploadTaskId);
            _counterManager.GetRateCounter("TryReserveQuotaRate").Increment();
            try
            {
                using (new DisposableTimer(_counterManager.GetLatencyCounter("TryReserveQuotaLatency")))
                {
                    QuotaReservationResponse quotaReservationEntity = await _aetherQuotaManager.ReserveTeamUserQuotaAsync(
                        userId: userName,
                        resourceId: uploadTaskId,
                        resourceCount: 1,
                        resourceManager: _resourceManagers[AetherQuotaType.Upload],
                        userResourceUsageLimit: _serviceConfig.UploadsLimit,
                        quotaExemptUsers: _serviceConfig.UploadsExemption,
                        teamId: teamId,
                        expirationInterval: _serviceConfig.SubmissionExpirationInterval);

                    _counterManager.GetRateCounter("TryReserveQuotaSuccessRate").Increment();
                    return quotaReservationEntity;
                }
            }
            catch (Exception e)
            {
                _counterManager.GetRateCounter("TryReserveQuotaFailureRate").Increment();
                CommonLogger.LogEntityWarning(uploadTaskId, "Failed to reserve upload quota. Exception: {exception}", e);
                return new ContentResult
                {
                    Content = $"Failed to reserve upload quota. Exception: {PrepareExceptionMessage(e)}",
                    ContentType = "text/plain",
                    StatusCode = (int)HttpStatusCode.InternalServerError
                };
            }
        }

        private static string PrepareExceptionMessage(Exception e)
        {
            return e.Message.Replace(Environment.NewLine, " ").Replace('\n', ' ').Replace('\r', ' ');
        }

        [HttpGet]
        [Route("usage/experiments")]
        public async Task<IEnumerable<AggregatedResourcesUsageEntity>> GetExperimentsUsage()
        {
            return await _resourceManagers[AetherQuotaType.Node].GetTotalResouceUsageAsync();
        }

        [HttpGet]
        [Route("usage/emails")]
        public async Task<IEnumerable<AggregatedResourcesUsageEntity>> GetEmailsUsage()
        {
            return await _resourceManagers[AetherQuotaType.Email].GetTotalResouceUsageAsync();
        }

        [HttpGet]
        [Route("usage/promotions")]
        public async Task<IEnumerable<AggregatedResourcesUsageEntity>> GetPromotionsUsage()
        {
            return await _resourceManagers[AetherQuotaType.Promotion].GetTotalResouceUsageAsync();
        }

        [HttpGet]
        [Route("usage/uploads")]
        public async Task<IEnumerable<AggregatedResourcesUsageEntity>> GetUploadsUsage()
        {
            return await _resourceManagers[AetherQuotaType.Upload].GetTotalResouceUsageAsync();
        }
    }
}
