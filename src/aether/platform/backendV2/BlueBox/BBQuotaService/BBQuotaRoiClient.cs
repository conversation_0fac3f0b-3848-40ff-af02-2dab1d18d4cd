﻿// <copyright file="BBQuotaRoiClient.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Microsoft.Aether.DataContracts.Entity;
using Microsoft.Aether.QuotaService;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Microsoft.Aether.BBQuotaService
{
    internal class BBQuotaRoiClient : IQuotaRoiClient
    {
        // return active teams that currently have experiments running, to be added
        // once we enable team queues in bluebox
        public Task<Dictionary<string, string>> GetTeamsAsync()
        {
            throw new NotImplementedException();
        }

        public Task<string> GetUserTeamAsync(string userName, string teamId)
        {
            return Task.FromResult(teamId);
        }
    }
}
