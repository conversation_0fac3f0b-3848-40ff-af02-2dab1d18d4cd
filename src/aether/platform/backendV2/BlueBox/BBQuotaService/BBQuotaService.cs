﻿// <copyright file="BBQuotaService.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Aether.Backend.BlueBox.MetaStore.Client;
using Microsoft.Aether.BackendCommon;
using Microsoft.Aether.BackendCommon.Configuration;
using Microsoft.Aether.BackendCommon.Time;
using Microsoft.Aether.BlueBox.AetherK8SBaseService;
using Microsoft.Aether.BlueBox.AetherK8SCommon;
using Microsoft.Aether.BlueBox.AetherK8SCommon.Auth;
using Microsoft.Aether.BlueBox.Storage;
using Microsoft.Aether.BlueBox.Web.Common;
using Microsoft.Aether.BlueBox.WorkspaceResourcesClient;
using Microsoft.Aether.Clouds.CloudCommon.ServiceBus;
using Microsoft.Aether.DataContracts;
using Microsoft.Aether.QuotaService;
using Microsoft.Aether.TaggedQueueManager;
using Microsoft.Aether.TaggedQueueManager.CloudTags;
using Microsoft.AIPlatform.CertificateReloader;
using Microsoft.AIPlatform.Common.Metrics;
using Microsoft.AIPlatform.Telemetry.Contracts.Metrics;
using Microsoft.AIPlatform.WorkloadIdentity;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Cosmos;
using Microsoft.Azure.Cosmos.Scripts;
using Microsoft.Azure.ServiceBus;
using Microsoft.Azure.ServiceBus.Management;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.MachineLearning.Common.Caching;
using Microsoft.MachineLearning.Common.Core;
using Microsoft.MachineLearning.Common.Core.Metrics;
using Microsoft.MachineLearning.Common.Startup;
using Microsoft.MachineLearning.Common.Startup.Telemetry;
using Microsoft.MachineLearning.Common.WebApi.AppInsights;
using Microsoft.MachineLearning.Common.WebApi.ErrorHandling;
using Microsoft.RelInfra.Common.Exceptions;
using Microsoft.RelInfra.Instrumentation;
using Microsoft.RelInfra.Instrumentation.Logging;
using Microsoft.RelInfra.Instrumentation.Metrics;
using Microsoft.RelInfra.Storage;
using Microsoft.RelInfra.Storage.Exceptions;
using Relinfra.Storage.CosmosDb;
using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.IO;
using System.Linq;
using System.Net;
using System.Threading;
using System.Threading.Tasks;

namespace Microsoft.Aether.BBQuotaService
{
    [ExcludeFromCodeCoverage]
    public class BBQuotaService
    {
        private const string ServiceName = ServiceNames.AetherQuotaService;
        private const string ConfigFileName = "quotaserviceconfig.ini";
        private const string StorageConfigFilename = "storage.ini";
        private const string SharedConfigFileName = "SharedSettings.ini";
        private const string BbSharedConfigFileName = "BBSharedSettings.ini";

        public static void Main()
        {
            MainAsync().Wait();
        }

        private static async Task MainAsync()
        {
            IWebHost host = null;
            try
            {
                var k8sService = new K8sBaseService(serviceName: ServiceName,
                                                 configFilename: ConfigFileName,
                                                 iniFileNames: new[]
                                                 {
                                                        StorageConfigFilename,
                                                        SharedConfigFileName,
                                                        BbSharedConfigFileName,
                                                 });
                await k8sService.InitializeAsync();

                var serviceConfig = new QuotaServiceConfig(k8sService.ApConfig, k8sService.SecretProvider);
                serviceConfig.VerifyConfig();

                CounterManager counterManager = k8sService.CounterManager;
                IWorkloadIdentityProvider workloadIdentityProvider = k8sService.WorkloadIdentityProvider;

                var artifactPrefix = Env.AzurePrefix();
                CommonLogger.LogInfo($"Azure table prefix is {artifactPrefix}");

                var workspaceResourcesConfig = new WorkspaceResourcesConfig(k8sService.ApConfig);
                var amlWorkspaceResourcesClient = new WorkspaceResourcesClient(workspaceResourcesConfig, k8sService.ServicePrincipalAadTokenProvider, k8sService.CounterManager);

                var storeFactory = new StoreFactory(baseUrl: serviceConfig.MetaStoreUrl, s2STokenProvider: k8sService.ServicePrincipalAadTokenProvider, timeout: serviceConfig.MetaStoreTimeout);
                var context = new StoreOperationContext { UserName = serviceConfig.ServiceName, ClientName = serviceConfig.ServiceName };
                IExperimentStore experimentStore = storeFactory.CreateExperimentStore(context);

                IAzureBlobStorage quotaBlobStorage = null;
                if (workloadIdentityProvider?.IsAvailable == true)
                {
                    var storageUri = Env.GetBlobStorageEndpointUri(Env.StorageAccountNameCloudManager());
                    quotaBlobStorage = new AzureBlobStorageV2(
                        serviceUri: storageUri,
                        credential: workloadIdentityProvider.Credential,
                        numRetries: serviceConfig.AzureBlobRetriesNumber,
                        intervalBetweenRetries: serviceConfig.AzureBlobRetryInterval,
                        counterManager: counterManager);
                }
                else
                {
                    quotaBlobStorage = new AzureBlobStorage(
                        connectionString: serviceConfig.GetAzureConnectionStringAsync().Result,
                        numRetries: serviceConfig.AzureBlobRetriesNumber,
                        intervalBetweenRetries: serviceConfig.AzureBlobRetryInterval,
                        counterManager: counterManager);
                }

                string blobContainerName = artifactPrefix + serviceConfig.AzureBlobContainerName;
                Task.WaitAll(quotaBlobStorage.InitializeAsync(blobContainerName),
                    quotaBlobStorage.InitializeAsync(artifactPrefix + serviceConfig.UserRunningJobsBlobContainerName));

                string lockBlobContainerName = artifactPrefix + serviceConfig.BlobLeaseContainerName;
                await quotaBlobStorage.InitializeAsync(lockBlobContainerName);

                BBQuotaRoiClient quotaRoiClient = new BBQuotaRoiClient();

                var experimentsResourceManager = new ResourceManager(
                    quotaBlobStorage: quotaBlobStorage,
                    resourceQuotaPrefix: serviceConfig.TeamExperimentsQuotaPrefix,
                    blobContainerName: blobContainerName,
                    blobTimeoutInterval: serviceConfig.AzureBlobTimeoutInterval,
                    parallelismDegree: serviceConfig.MaxParallelismDegree,
                    quotaRoiClient: quotaRoiClient);

                var emailsResourceManager = new ResourceManager(
                    quotaBlobStorage: quotaBlobStorage,
                    resourceQuotaPrefix: serviceConfig.TeamEmailsQuotaPrefix,
                    blobContainerName: blobContainerName,
                    blobTimeoutInterval: serviceConfig.AzureBlobTimeoutInterval,
                    parallelismDegree: serviceConfig.MaxParallelismDegree,
                    quotaRoiClient: quotaRoiClient);

                var promotionsResourceManager = new ResourceManager(
                    quotaBlobStorage: quotaBlobStorage,
                    resourceQuotaPrefix: serviceConfig.TeamPromotionsQuotaPrefix,
                    blobContainerName: blobContainerName,
                    blobTimeoutInterval: serviceConfig.AzureBlobTimeoutInterval,
                    parallelismDegree: serviceConfig.MaxParallelismDegree,
                    quotaRoiClient: quotaRoiClient);

                var uploadsResourceManager = new ResourceManager(
                    quotaBlobStorage: quotaBlobStorage,
                    resourceQuotaPrefix: serviceConfig.TeamUploadsQuotaPrefix,
                    blobContainerName: blobContainerName,
                    blobTimeoutInterval: serviceConfig.AzureBlobTimeoutInterval,
                    parallelismDegree: serviceConfig.MaxParallelismDegree,
                    quotaRoiClient: quotaRoiClient);

                var runningJobsResourceManager = new ResourceManager(
                  quotaBlobStorage: quotaBlobStorage,
                  resourceQuotaPrefix: serviceConfig.UserRunningJobsQuotaPrefix,
                  blobContainerName: artifactPrefix + serviceConfig.UserRunningJobsBlobContainerName,
                  blobTimeoutInterval: serviceConfig.AzureBlobTimeoutInterval,
                  parallelismDegree: serviceConfig.MaxParallelismDegree,
                  quotaRoiClient: quotaRoiClient);

                AetherQuotaManager aetherQuotaManager = new AetherQuotaManager(quotaRoiClient: quotaRoiClient);

                Dictionary<AetherQuotaType, ResourceManager> resourceManagers = new Dictionary<AetherQuotaType, ResourceManager>()
                {
                    { AetherQuotaType.Node, experimentsResourceManager },
                    { AetherQuotaType.Email, emailsResourceManager },
                    { AetherQuotaType.Promotion, promotionsResourceManager },
                    { AetherQuotaType.Upload, uploadsResourceManager },
                    { AetherQuotaType.RunningNode, runningJobsResourceManager }
                };

                ITaggedQueueManager<AssignedQueueIdTags, string> taggedExperimentQueueManager = null;
                if (workloadIdentityProvider?.IsAvailable == true)
                {
                    var serviceUri = Env.GetQueueStorageEndpointUri(Env.StorageAccountNameNodeProcessorQueue());
                    taggedExperimentQueueManager = new AzureTaggedQueueManagerV2<AssignedQueueIdTags, string>(
                        serviceUri: serviceUri,
                        credential: workloadIdentityProvider.Credential,
                        queuePrefix: Env.AzurePrefix() + serviceConfig.ExperimentQueuePrefix,
                        timerFactory: new TimerFactory(),
                        updateTimeSpan: serviceConfig.AzureQueueManagerUpdatePeriod);
                }
                else
                {
                    taggedExperimentQueueManager = new AzureTaggedQueueManager<AssignedQueueIdTags, string>(
                        connectionString: await serviceConfig.GetExperimentQueueStorageAzureConnectionStringAsync(),
                        queuePrefix: Env.AzurePrefix() + serviceConfig.ExperimentQueuePrefix,
                        timerFactory: new TimerFactory(),
                        updateTimeSpan: serviceConfig.AzureQueueManagerUpdatePeriod);
                }

                await taggedExperimentQueueManager.InitializeAsync();
                var experimentQueuePublisher = new ExperimentQueuePublisher(taggedExperimentQueueManager, counterManager);

                var serviceBusMessageConverter = new ServiceBusEnvelopeMessageConverter(
                    serializer: new ServiceBusMessageSerializer(),
                    compressor: new ServiceBusMessageCompressor());

                ITopicClient checkCachedTopicClient = null;
                if (workloadIdentityProvider?.IsAvailable == true)
                {
                    var tokenAdapter = new LegacyServiceBusCredentialAdapter(workloadIdentityProvider.Credential);
                    checkCachedTopicClient = new TopicClient(
                        endpoint: Env.GetServiceBusEndpoint(Env.ServiceBusAccountNamePipelineCommon()),
                        entityPath: Env.QuotaServiceCheckCachedPipelineRunTopicName(),
                        tokenProvider: tokenAdapter,
                        retryPolicy: RetryPolicy.Default);
                }
                else
                {
                    CommonLogger.LogEntityInfo(nameof(TopicClient), $"Construct client with connection string. TopicName: [{Env.QuotaServiceCheckCachedPipelineRunTopicName()}].");
                    checkCachedTopicClient = new TopicClient(
                        connectionString: Env.DcmServiceBusConnectionString(),
                        entityPath: Env.QuotaServiceCheckCachedPipelineRunTopicName(),
                        retryPolicy: RetryPolicy.Default);
                }

                var checkCachedTopicPublisher = new ServiceBusTopicPublisher(checkCachedTopicClient, serviceBusMessageConverter);

                var provisionedCosmosDbConnectionInfo = await serviceConfig.SharedCosmosDbConfig.GetCosmosDbConnectionInfo();
                var cosmosDbConnectionInfo = new BackendCommon.CosmosDbConnectionInfo()
                {
                    CosmosDbEndpoint = provisionedCosmosDbConnectionInfo.CosmosDbEndpoint,
                    CosmosDbKey = provisionedCosmosDbConnectionInfo.CosmosDbKey
                };

                var connectionProvider = new BbMetaStorageConnectionProvider(cosmosDbConnectionInfo);
                var memoryCache = new MemoryCache(new MemoryCacheOptions());

                var runningPipelineRunQuotaLimit = new BlueBoxUserCosmosDbStorageProviderV2(
                            databaseName: serviceConfig.CosmosDbDatabaseName,
                            containerName: artifactPrefix + serviceConfig.RunningPipelineRunQuotaLimitContainerName,
                            partitionKeyPath: null, // using the IQueryableDbStorage interface with default PartitionKey
                            workloadIdentityProvider: k8sService.WorkloadIdentityProvider,
                            cache: memoryCache,
                            cacheExpiration: serviceConfig.ConnectionCacheDuration,
                            defaultConnectionProvider: connectionProvider,
                            counterManager: counterManager,
                            defaultTimeToLive: null,
                            indexPolicy: null,
                            cosmosDbOptions: serviceConfig.GetCosmosDbOptions(),
                            requestTimeout: serviceConfig.RequestTimeout);
                await runningPipelineRunQuotaLimit.InitDefaultQueryableStorageAsync(Enumerable.Empty<StoredProcedureProperties>());
                AzureCosmosDbContainer runningPipelineRunQuotaLimitCollection = await runningPipelineRunQuotaLimit.GetQueryableStorageAsync(containerIdentity: null);

                var runningPipelineRunQuotaUsageDetails = new BlueBoxUserCosmosDbStorageProviderV2(
                            databaseName: serviceConfig.CosmosDbDatabaseName,
                            containerName: artifactPrefix + serviceConfig.RunningPipelineRunQuotaUsageContainerName,
                            partitionKeyPath: null, // using the IQueryableDbStorage interface with default PartitionKey
                            workloadIdentityProvider: k8sService.WorkloadIdentityProvider,
                            cache: memoryCache,
                            cacheExpiration: serviceConfig.ConnectionCacheDuration,
                            defaultConnectionProvider: connectionProvider,
                            counterManager: counterManager,
                            defaultTimeToLive: null,
                            indexPolicy: null,
                            cosmosDbOptions: serviceConfig.GetCosmosDbOptions(),
                            requestTimeout: serviceConfig.RequestTimeout);
                await runningPipelineRunQuotaUsageDetails.InitDefaultQueryableStorageAsync(Enumerable.Empty<StoredProcedureProperties>());
                AzureCosmosDbContainer runningPipelineRunQuotaUsageDetailsCollection = await runningPipelineRunQuotaUsageDetails.GetQueryableStorageAsync(containerIdentity: null);

                var cachedPipelineRunStorage = new BlueBoxUserCosmosDbStorageProviderV2(
                            databaseName: serviceConfig.CosmosDbDatabaseName,
                            containerName: artifactPrefix + serviceConfig.CachedPipelineRunContainerName,
                            partitionKeyPath: null, // using the IQueryableDbStorage interface with default PartitionKey
                            workloadIdentityProvider: k8sService.WorkloadIdentityProvider,
                            cache: memoryCache,
                            cacheExpiration: serviceConfig.ConnectionCacheDuration,
                            defaultConnectionProvider: connectionProvider,
                            counterManager: counterManager,
                            defaultTimeToLive: null,
                            indexPolicy: null,
                            cosmosDbOptions: serviceConfig.GetCosmosDbOptions(),
                            requestTimeout: serviceConfig.RequestTimeout);
                await cachedPipelineRunStorage.InitDefaultQueryableStorageAsync(Enumerable.Empty<StoredProcedureProperties>());
                AzureCosmosDbContainer cachedPipelineRunCollection = await cachedPipelineRunStorage.GetQueryableStorageAsync(containerIdentity: null);

                var cachedPipelineRunManager = new CachedPipelineRunManager(
                    serviceConfig,
                    counterManager,
                    cachedPipelineRunCollection,
                    experimentQueuePublisher);

                var runningPipelineRunQuotaManager = new RunningPipelineRunQuotaManager(
                    serviceConfig,
                    runningPipelineRunQuotaLimitCollection,
                    runningPipelineRunQuotaUsageDetailsCollection);

                var runningPipelineRunSoftQuotaManager = new RunningPipelineRunSoftQuotaManager(
                    serviceConfig,
                    runningPipelineRunQuotaLimitCollection,
                    runningPipelineRunQuotaUsageDetailsCollection,
                    cachedPipelineRunManager,
                    checkCachedTopicPublisher);

                var runningPipelineRunQuotaUsageRevision = new RunningPipelineRunQuotaUsageRevision(
                    serviceConfig,
                    counterManager,
                    runningPipelineRunQuotaManager,
                    runningPipelineRunSoftQuotaManager,
                    experimentStore,
                    quotaBlobStorage,
                    lockBlobContainerName);

                CheckCachedPipelineRunMessageSubscriber messageSubscriber = null;
                if (workloadIdentityProvider?.IsAvailable == true)
                {
                    messageSubscriber = new CheckCachedPipelineRunMessageSubscriber(
                        endpoint: Env.GetServiceBusEndpoint(Env.ServiceBusAccountNamePipelineCommon()),
                        credential: workloadIdentityProvider.Credential,
                        counterManager: counterManager,
                        serviceConfig: serviceConfig,
                        topicName: Env.QuotaServiceCheckCachedPipelineRunTopicName(),
                        subscriptionName: Env.QuotaServiceCheckCachedPipelineRunSubscriptionName(),
                        quotaManager: runningPipelineRunSoftQuotaManager,
                        cachedManager: cachedPipelineRunManager,
                        experimentQueuePublisher: experimentQueuePublisher);
                }
                else
                {
                    messageSubscriber = new CheckCachedPipelineRunMessageSubscriber(
                        connectionString: Env.DcmServiceBusConnectionString(),
                        counterManager: counterManager,
                        serviceConfig: serviceConfig,
                        topicName: Env.QuotaServiceCheckCachedPipelineRunTopicName(),
                        subscriptionName: Env.QuotaServiceCheckCachedPipelineRunSubscriptionName(),
                        quotaManager: runningPipelineRunSoftQuotaManager,
                        cachedManager: cachedPipelineRunManager,
                        experimentQueuePublisher: experimentQueuePublisher);
                }

                messageSubscriber.Start();

                var httpsConfiguration = new HttpsServiceConfiguration(k8sService.ApConfig);

                IWebHostBuilder hostBuilder = new WebHostBuilder()
                        .UseKestrel((builderContext, options) =>
                        {
                            options.Limits.MaxRequestBodySize = serviceConfig.MaxReceivedMessageSize;

                            // Use onebox port from service config.
                            if (Env.DeploymentType() == "onebox")
                            {
                                httpsConfiguration.OneboxPort = serviceConfig.Port;
                            }

                            KestrelHttpsExtensions.ListenHttps(builderContext, options, httpsConfiguration);
                        })
                        .ConfigureServices(services =>
                        {
                            services.AddSingleton(k8sService.WorkloadIdentityProvider);
                            var redisConfig = new RedisConfiguration(k8sService.ApConfig, k8sService.SecretProvider);
                            var builder = new ConfigurationBuilder()
                                .AddJsonFile(Path.Combine("appsettings", "appsettings.override.json"), optional: true, reloadOnChange: true)
                                .AddInMemoryCollection(redisConfig.AsInMemoryCollection())
                                .AddEnvironmentVariables();
                            IConfiguration configuration = builder.Build();

                            services.AddSingleton<IObjectCacheDecorator, AetherCacheDecorator>();
                            services.AddMemoryCache();
                            services.AddCommonCaching(configuration);
                            services.AddSingleton<IJointMetricsPublisher>((sp) => new AmlTelemetryMetricsPublisher(sp.GetRequiredService<ILoggerFactory>(), sp.GetRequiredService<IMetricsReporter>()));
                            services.AddSingleton<IMetricReporter>(sp => new LegacyMetricReporter(sp.GetRequiredService<IJointMetricsPublisher>()));
                            services.AddSingleton<IWorkspaceResourcesCache>(sp =>
                            {
                                IMemoryCache memoryCache = sp.GetRequiredService<IMemoryCache>();
                                IObjectCache cache = sp.GetRequiredService<IObjectCache>();
                                return new WorkspaceResourcesCache(cache, memoryCache, amlWorkspaceResourcesClient, counterManager);
                            });

                            services.AddSingleton<RunningPipelineRunQuotaManager>(runningPipelineRunQuotaManager);
                            services.AddSingleton<RunningPipelineRunSoftQuotaManager>(runningPipelineRunSoftQuotaManager);
                            services.AddSingleton<ICachedPipelineRunManager>(cachedPipelineRunManager);
                            services.AddSingleton(workloadIdentityProvider);

                            ConfigureServices(services,
                                    serviceConfig,
                                    counterManager,
                                    k8sService,
                                    aetherQuotaManager,
                                    resourceManagers);

                            #region Error Handling
                            var exceptionHanlder = new StorageExceptionHandler();
                            services.AddErrorHandling(configuration, sp =>
                            {
                                var list = new List<InterpreterCandidate>();
                                list.AddRange(ErrorHandlingInstallationExtensions.DefaultInterpreterCandidates);

                                var interpreter = new FormattableExceptionInterpreter<Exception>(exceptionHanlder);
                                var candidate = new InterpreterCandidate(exceptionType: typeof(Exception), interpreter: interpreter);
                                list.Add(candidate);

                                return new ExceptionInterpreterProvider(list);
                            });
                            services.AddScoped<IExceptionInterpretationPreProcessor>(sp => exceptionHanlder);
                            services.AddSingleton<IOperationContextAccessor>(serviceProvider => new OperationContextManager(new Lazy<ILogger<OperationContextManager>>(new LoggerFactory().CreateLogger<OperationContextManager>())));
                            services.AddSingleton<IServiceNameGetter>(serviceProvider => new ServiceNameGetter(ServiceName));
                            // AddErrorHandling does this for us, but uses a logger that doesn't respect privacy, so we overwrite it
                            services.AddSingleton<IExceptionInterpretationLogger, CommonExceptionInterpretationLogger>();
                            #endregion

                            if (configuration.GetValue<bool>("CertificateReloading:Enabled", defaultValue: false))
                            {
                                CommonLogger.LogInfo($"Enable certificate reloading");
                                services.ConfigureCertificateReloading(Directory.GetCurrentDirectory());
                            }
                            else
                            {
                                CommonLogger.LogInfo($"Disable certificate reloading");
                            }
                        })
                        .ConfigureLogging(builder => builder.ClearProviders().AddCommonLogger());

                host = hostBuilder.UseStartup<BBQuotaService>().Build();

                CommonLogger.LogEntityInfo(ServiceName, $"Workload identity available: {workloadIdentityProvider?.IsAvailable}.");
                CommonLogger.LogEntityInfo(ServiceName, "done configuration...");

                host.Start();

                runningPipelineRunQuotaUsageRevision.Start();

                AppDomain.CurrentDomain.UnhandledException += (sender, arg) =>
                {
                    Exception e = (Exception)arg.ExceptionObject;
                    CommonLogger.LogEntityError(ServiceName, "Unhandled exception: [Error: {safe_exception_message}, Stack Trace: {stack_trace}]", e.Message, e.StackTrace);
                    Task.Delay(TimeSpan.FromSeconds(5)).Wait();
                };

                var ev = new AutoResetEvent(false);
                Console.CancelKeyPress += (sender, eventArgs) =>
                {
                    eventArgs.Cancel = true;
                    CommonLogger.LogEntityWarning(ServiceName, "Shutting down the service...");
                    ev.Set();
                };
                ev.WaitOne();
            }
            catch (Exception ex)
            {
                CommonLogger.LogError("Exception when running BBQuotaService. Exception: {exception}", ex);
                CommonLogger.LogEntityError(ServiceName, "Exception when running BBQuotaService. Exception: {exception}", ex);
                throw;
            }
            finally
            {
                host?.Dispose();
            }
        }

        private static void ConfigureServices(IServiceCollection services,
            QuotaServiceConfig serviceConfig,
            CounterManager counterManager,
            K8sBaseService k8sService,
            AetherQuotaManager aetherQuotaManager,
            Dictionary<AetherQuotaType, ResourceManager> resourceManagers)
        {
            services.AddSingleton(serviceConfig);
            services.AddSingleton(counterManager);
            services.AddSingleton(aetherQuotaManager);
            services.AddSingleton(resourceManagers);
            services.ConfigureBasicServices(serviceName: ServiceName, baseService: k8sService, configuration: null);

            services.AddMvc(options =>
            {
                options.EnableEndpointRouting = false;
                // Error handling section requires this filter.
                options.Filters.Add(typeof(CustomErrorResponseExceptionFilter));
                if (Env.DeploymentType() != "onebox")
                {
                    options.Filters.Add(new S2sAuthorizeAttribute(k8sService.S2sAuthorizer, keepAlivePath: "/keepalive"));
                    options.AddCommonAppInsightsComponents();
                }
            })
             .AddApplicationPart(typeof(AetherQuotaController).Assembly)
             .AddApplicationPart(typeof(RunningPipelineRunQuotaController).Assembly)
             .AddApplicationPart(typeof(RunningPipelineRunSoftQuotaController).Assembly)
             .AddApplicationPart(typeof(CachedPipelineRunController).Assembly)
             .AddApplicationPart(typeof(KeepaliveController).Assembly);

            services.AddApiVersioning(options =>
            {
                options.ReportApiVersions = true;
                // Set a default version when it's not provided
                options.AssumeDefaultVersionWhenUnspecified = true;
                options.DefaultApiVersion = new ApiVersion(majorVersion: 1, minorVersion: 0);
            });
        }

        public void Configure(IApplicationBuilder app)
        {
            //http://www.talkingdotnet.com/how-to-enable-gzip-compression-in-asp-net-core/
            app.UseResponseCompression();
            app.UseMvc();
        }
    }
}
