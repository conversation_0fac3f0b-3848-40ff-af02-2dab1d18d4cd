﻿// <copyright file="QuotaServiceTestHelper.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Microsoft.Aether.DataContracts.Entity;
using Microsoft.Aether.QuotaService.Shared;
using Microsoft.RelInfra.Instrumentation;
using Microsoft.RelInfra.Storage;
using Microsoft.RelInfra.Storage.Exceptions;
using Moq;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;

namespace Microsoft.Aether.QuotaService.Test
{
    public static class QuotaServiceTestHelper
    {
        public static void SetupMockStorage<T>(Mock<IQueryableDbStorage> mockContainer, Dictionary<string, Storable<T>> storage)
        {
            mockContainer.Setup(c => c.GetAsync<T>(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .Returns((string partitionKey, string id, CancellationToken cancellationToken) =>
                {
                    string key = GetUniqueKey(partitionKey, id);
                    if (!storage.ContainsKey(key))
                    {
                        throw new RelInfraStorageException(StorageError.NotFound);
                    }

                    return Task.FromResult(storage[key]);
                });

            mockContainer.Setup(c => c.TryGetAsync<T>(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .Returns((string partitionKey, string id, CancellationToken cancellationToken) =>
                {
                    string key = GetUniqueKey(partitionKey, id);
                    return Task.FromResult(storage.ContainsKey(key) ? storage[key] : null);
                });

            mockContainer.Setup(c => c.ExistsAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .Returns((string partitionKey, string id, CancellationToken cancellationToken) =>
                {
                    string key = GetUniqueKey(partitionKey, id);
                    return Task.FromResult(storage.ContainsKey(key));
                });

            mockContainer.Setup(c => c.AddAsync<T>(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<Storable<T>>(), It.IsAny<CancellationToken>()))
                .Returns((string partitionKey, string id, Storable<T> entity, CancellationToken cancellationToken) =>
                {
                    string key = GetUniqueKey(partitionKey, id);
                    if (storage.ContainsKey(key))
                    {
                        throw new RelInfraStorageException(StorageError.Conflict);
                    }

                    storage.Add(key, entity);
                    return Task.FromResult(storage[key]);
                });

            mockContainer.Setup(c => c.PutAsync<T>(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<Storable<T>>(), It.IsAny<CancellationToken>()))
                .Returns((string partitionKey, string id, Storable<T> entity, CancellationToken cancellationToken) =>
                {
                    string key = GetUniqueKey(partitionKey, id);
                    storage[key] = entity;
                    return Task.FromResult(storage[key]);
                });

            mockContainer.Setup(c => c.DeleteAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .Callback((string partitionKey, string id, CancellationToken cancellationToken) =>
                {
                    string key = GetUniqueKey(partitionKey, id);
                    if (!storage.ContainsKey(key))
                    {
                        throw new RelInfraStorageException(StorageError.NotFound);
                    }

                    storage.Remove(key);
                });

            mockContainer.Setup(c => c.TryDeleteAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .Returns((string partitionKey, string id, CancellationToken cancellationToken) =>
                {
                    string key = GetUniqueKey(partitionKey, id);
                    if (!storage.ContainsKey(key))
                    {
                        return Task.FromResult(false);
                    }

                    storage.Remove(key);
                    return Task.FromResult(true);
                });

            mockContainer.Setup(c => c.GetPartitionAsync<T>(It.IsAny<string>(), It.IsAny<int>(), It.IsAny<CancellationToken>()))
                .Returns((string partitionKey, int maxCount, CancellationToken cancellationToken) =>
                {
                    var results = storage.Where(kvp => kvp.Key.StartsWith(partitionKey)).Select(kvp => kvp.Value).Take(maxCount);
                    return Task.FromResult(results);
                });

            mockContainer.Setup(c => c.GetAllAsync<T>(It.IsAny<int>(), It.IsAny<CancellationToken>()))
                .Returns((int maxCount, CancellationToken cancellationToken) =>
                {
                    var results = storage.Values.Take(maxCount);
                    return Task.FromResult(results);
                });

            mockContainer.Setup(c => c.GetAllWithContinuationTokenAsync<T>(It.IsAny<string>(), It.IsAny<int>(), It.IsAny<CancellationToken>()))
                .Returns((string continuationToken, int takeCount, CancellationToken cancellationToken) =>
                {
                    var queryResult = new QueryResults<T>(continuationToken: null, results: storage.Values);
                    return Task.FromResult(queryResult);
                });

            mockContainer.Setup(c => c.GetPartitionWithCountinuationTokenAsync<T>(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<int>(), It.IsAny<CancellationToken>()))
                .Returns((string partitionKey, string continuationToken, int takeCount, CancellationToken cancellationToken) =>
                {
                    var results = storage.Where(kvp => kvp.Key.StartsWith(partitionKey)).Select(kvp => kvp.Value);
                    var queryResult = new QueryResults<T>(continuationToken: null, results: results);
                    return Task.FromResult(queryResult);
                });

            // Calculate quota count from usage entities.
            mockContainer.Setup(c => c.ExecuteQueryNonStorableAsync<int>(It.IsAny<string>(), It.Is<string>(q => q.Equals(Constants.TotalQuotaUsageCountQuery, StringComparison.InvariantCultureIgnoreCase)), It.IsAny<string>(), It.IsAny<int?>(), It.IsAny<List<KeyValuePair<string, string>>>(), It.IsAny<CancellationToken>()))
                .Returns((string partitionKey, string query, string continuationToken, int? takeCount, List<KeyValuePair<string, string>> parameters, CancellationToken cancellationToken) =>
                {
                    int totalCount = 0;
                    IEnumerable<Storable<T>> allEntities;
                    if (string.IsNullOrEmpty(partitionKey))
                    {
                        allEntities = storage.Select(kvp => kvp.Value);
                    }
                    else
                    {
                        allEntities = storage.Where(kvp => kvp.Key.StartsWith(partitionKey)).Select(kvp => kvp.Value);
                    }

                    if (typeof(T).IsAssignableTo(typeof(QuotaUsageEntity)))
                    {
                        foreach (var entity in allEntities)
                        {
                            var usageEntity = entity.Entity as QuotaUsageEntity;
                            totalCount += usageEntity.UsageCount;
                        }
                    }

                    var result = new List<int>() { totalCount };
                    return Task.FromResult(new QueryResults<int>(continuationToken: null, results: result));
                });

            // Calculate soft quota count from usage entities.
            mockContainer.Setup(c => c.ExecuteQueryNonStorableAsync<int>(It.IsAny<string>(), It.Is<string>(q => q.Equals(Constants.TotalSoftQuotaUsageCountQuery, StringComparison.InvariantCultureIgnoreCase)), It.IsAny<string>(), It.IsAny<int?>(), It.IsAny<List<KeyValuePair<string, string>>>(), It.IsAny<CancellationToken>()))
                .Returns((string partitionKey, string query, string continuationToken, int? takeCount, List<KeyValuePair<string, string>> parameters, CancellationToken cancellationToken) =>
                {
                    int totalCount = 0;
                    IEnumerable<Storable<T>> allEntities;
                    if (string.IsNullOrEmpty(partitionKey))
                    {
                        allEntities = storage.Select(kvp => kvp.Value);
                    }
                    else
                    {
                        allEntities = storage.Where(kvp => kvp.Key.StartsWith(partitionKey)).Select(kvp => kvp.Value);
                    }

                    if (typeof(T).IsAssignableTo(typeof(QuotaUsageEntity)))
                    {
                        foreach (var entity in allEntities)
                        {
                            var usageEntity = entity.Entity as QuotaUsageEntity;
                            // Only count when it's soft quota usage.
                            if (usageEntity.IsSoftQuota.HasValue && usageEntity.IsSoftQuota == true)
                            {
                                totalCount += usageEntity.UsageCount;
                            }
                        }
                    }

                    var result = new List<int>() { totalCount };
                    return Task.FromResult(new QueryResults<int>(continuationToken: null, results: result));
                });

            // Get all entities count of a container or a partition of a container.
            mockContainer.Setup(c => c.ExecuteQueryNonStorableAsync<int>(It.IsAny<string>(), It.Is<string>(q => q.Equals(Constants.TotalEntityCountQuery, StringComparison.InvariantCultureIgnoreCase)), It.IsAny<string>(), It.IsAny<int?>(), It.IsAny<List<KeyValuePair<string, string>>>(), It.IsAny<CancellationToken>()))
                .Returns((string partitionKey, string query, string continuationToken, int? takeCount, List<KeyValuePair<string, string>> parameters, CancellationToken cancellationToken) =>
                {
                    IEnumerable<Storable<T>> allEntities;
                    if (string.IsNullOrEmpty(partitionKey))
                    {
                        allEntities = storage.Select(kvp => kvp.Value);
                    }
                    else
                    {
                        allEntities = storage.Where(kvp => kvp.Key.StartsWith(partitionKey)).Select(kvp => kvp.Value);
                    }

                    var result = new List<int>() { allEntities.Count() };
                    return Task.FromResult(new QueryResults<int>(continuationToken: null, results: result));
                });

            // Get top k cached pipeline runs of a workspace by CachedTime.
            mockContainer.Setup(c => c.ExecuteQueryNonStorableAsync<T>(It.IsAny<string>(), It.Is<string>(q => Regex.Replace(q, "\\d+", "{k}").Equals(Constants.GetTopKCachedPipelineRunsQuery, StringComparison.InvariantCultureIgnoreCase)), It.IsAny<string>(), It.IsAny<int?>(), It.IsAny<List<KeyValuePair<string, string>>>(), It.IsAny<CancellationToken>()))
                .Returns((string partitionKey, string query, string continuationToken, int? takeCount, List<KeyValuePair<string, string>> parameters, CancellationToken cancellationToken) =>
                {
                    IEnumerable<Storable<T>> allEntities;
                    if (string.IsNullOrEmpty(partitionKey))
                    {
                        allEntities = storage.Select(kvp => kvp.Value);
                    }
                    else
                    {
                        allEntities = storage.Where(kvp => kvp.Key.StartsWith(partitionKey)).Select(kvp => kvp.Value);
                    }

                    var m = Regex.Match(query, @"(\d+)");
                    var k = int.Parse(m.Groups[1].Value);
                    var result = allEntities.Select(x => x.Entity).OrderBy(x => (x as CachedPipelineRunEntity)?.CachedTimeUtc).Take(k);
                    return Task.FromResult(new QueryResults<T>(continuationToken: null, results: result));
                });
        }

        public static void SetupMockStorageThrowException<T>(Mock<IQueryableDbStorage> mockContainer)
        {
            mockContainer.Setup(c => c.GetAsync<T>(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .Throws(new RelInfraStorageException(StorageError.UndefinedError));

            mockContainer.Setup(c => c.TryGetAsync<T>(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .Throws(new RelInfraStorageException(StorageError.UndefinedError));

            mockContainer.Setup(c => c.ExistsAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .Throws(new RelInfraStorageException(StorageError.UndefinedError));

            mockContainer.Setup(c => c.AddAsync<T>(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<Storable<T>>(), It.IsAny<CancellationToken>()))
                .Throws(new RelInfraStorageException(StorageError.UndefinedError));

            mockContainer.Setup(c => c.PutAsync<T>(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<Storable<T>>(), It.IsAny<CancellationToken>()))
                .Throws(new RelInfraStorageException(StorageError.UndefinedError));

            mockContainer.Setup(c => c.DeleteAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .Throws(new RelInfraStorageException(StorageError.UndefinedError));

            mockContainer.Setup(c => c.TryDeleteAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .Throws(new RelInfraStorageException(StorageError.UndefinedError));

            mockContainer.Setup(c => c.GetPartitionAsync<T>(It.IsAny<string>(), It.IsAny<int>(), It.IsAny<CancellationToken>()))
                .Throws(new RelInfraStorageException(StorageError.UndefinedError));

            mockContainer.Setup(c => c.GetAllAsync<T>(It.IsAny<int>(), It.IsAny<CancellationToken>()))
                .Throws(new RelInfraStorageException(StorageError.UndefinedError));

            mockContainer.Setup(c => c.GetAllWithContinuationTokenAsync<T>(It.IsAny<string>(), It.IsAny<int>(), It.IsAny<CancellationToken>()))
                .Throws(new RelInfraStorageException(StorageError.UndefinedError));

            mockContainer.Setup(c => c.GetPartitionWithCountinuationTokenAsync<T>(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<int>(), It.IsAny<CancellationToken>()))
                .Throws(new RelInfraStorageException(StorageError.UndefinedError));

            mockContainer.Setup(c => c.ExecuteQueryNonStorableAsync<int>(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<int?>(), It.IsAny<List<KeyValuePair<string, string>>>(), It.IsAny<CancellationToken>()))
                .Throws(new RelInfraStorageException(StorageError.UndefinedError));
        }

        public static string GetUniqueKey(string partitionKey, string id) => $"{partitionKey}_{id}";

        public static RunningPipelineRunQuotaUsageEntity GenerateRunningPipelineRunQuotaUsageEntity(
            WorkspaceIdentity workspace,
            string experimentId,
            DateTime? createdTimeUtc = null,
            DateTime? lastUpdatedTimeUtc = null,
            bool? isSoftQuota = null,
            string parentExperimentId = null)
        {
            return new RunningPipelineRunQuotaUsageEntity
            {
                Id = experimentId,
                Workspace = workspace,
                CreatedTimeUtc = createdTimeUtc ?? DateTime.UtcNow,
                LastUpdatedTimeUtc = lastUpdatedTimeUtc ?? DateTime.UtcNow,
                UsageCount = 1,
                IsSoftQuota = isSoftQuota,
                ParentExperimentId = parentExperimentId,
            };
        }

        public static CachedPipelineRunEntity GenerateCachedPipelineRunEntity(
            string experimentId,
            WorkspaceIdentity workspaceIdentity,
            DateTime cachedTimeUtc,
            string queueMessage = null)
        {
            return new CachedPipelineRunEntity()
            {
                Id = experimentId,
                Workspace = workspaceIdentity,
                CachedTimeUtc = cachedTimeUtc,
                QueueMessage = queueMessage,
            };
        }

        public static void InitCounter(MockRepository mockRepository, CounterManager counterManager)
        {
            Mock.Get(counterManager)
                .Setup(
                  stores => stores.GetNumberCounter(
                      It.IsAny<string>(),
                      It.IsAny<string>(),
                      It.IsAny<IDictionary<string, string>>()))
                .Returns(new NumberCounter(mockRepository.Create<ICounter>().Object, "numberCounter"));

            Mock.Get(counterManager)
                .Setup(
                  stores => stores.GetLatencyCounter(
                      It.IsAny<string>(),
                      It.IsAny<string>(),
                      It.IsAny<IDictionary<string, string>>()))
                .Returns(new LatencyCounter(mockRepository.Create<ICounter>().Object, "latencyCounter"));
        }
    }
}
