﻿// <copyright file="ResourceManagerTest.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Microsoft.Aether.QuotaService.Shared;
using Microsoft.RelInfra.Storage;
using Moq;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Microsoft.Aether.QuotaService.Test
{
    [TestFixture]
    public class ResourceManagerTest
    {
        private Mock<IQuotaRoiClient> _quotaRoiClient;
        private Mock<IAzureBlobStorage> _quotaBlobStorage;

        private ResourceManager _resourceManager;

        [SetUp]
        public void SetUp()
        {
            _quotaRoiClient = new Mock<IQuotaRoiClient>();
            _quotaBlobStorage = new Mock<IAzureBlobStorage>();
            _quotaBlobStorage.Setup(q => q.TryGetAsync<AggregatedResourcesUsageEntity>(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .Returns((string s1, string entityId, CancellationToken token) => Task.FromResult(
                        new Storable<AggregatedResourcesUsageEntity>()
                        {
                            Entity = new AetherQuotaManagerTest().CreateExampleUsageEntity(entityId, DateTime.UtcNow),
                        }));

            _resourceManager = new ResourceManager(_quotaBlobStorage.Object, "testQuota", "testBlob", TimeSpan.FromDays(1), 1, _quotaRoiClient.Object);
        }

        [Test]
        public void TestGetAggregatedResourceUsageEntityThrowsAsync()
        {
            Assert.ThrowsAsync<ArgumentNullException>(async () => await _resourceManager.GetAggregatedResourceUsageEntityAsync(null).ConfigureAwait(false));
        }

        [Test]
        public async Task TestPutAggregatedResourceUsageEntityAsync()
        {
            var storable = new Storable<AggregatedResourcesUsageEntity>()
            {
                Entity = new AggregatedResourcesUsageEntity()
                {
                    Id = "dummyId",
                }
            };

            // case 1: The resource exists, verify _quotaBlobStorage.UpdateAsync is called.
            _quotaBlobStorage.Setup(q => q.ExistsAsync(It.IsAny<string>(), It.IsAny<string>())).Returns(Task.FromResult(true));
            await _resourceManager.PutAggregatedResourceUsageEntityAsync(storable).ConfigureAwait(false);
            _quotaBlobStorage.Verify(q => q.UpdateAsync(It.IsAny<string>(), It.IsAny<string>(), storable), Times.Once);

            // case 2: The resource not exist, verify _quotaBlobStorage.AddAsync is called.
            _quotaBlobStorage.Setup(q => q.ExistsAsync(It.IsAny<string>(), It.IsAny<string>())).Returns(Task.FromResult(false));
            await _resourceManager.PutAggregatedResourceUsageEntityAsync(storable).ConfigureAwait(false);
            _quotaBlobStorage.Verify(q => q.AddAsync(It.IsAny<string>(), It.IsAny<string>(), storable), Times.Once);
        }

        [Test]
        public void TestPutAggregatedResourceUsageEntityThrowsAsync()
        {
            Assert.ThrowsAsync<ArgumentNullException>(async () => await _resourceManager.PutAggregatedResourceUsageEntityAsync(null).ConfigureAwait(false));
        }

        [Test]
        public async Task TestGetTotalResouceUsageAsync()
        {
            _quotaRoiClient.Setup(q => q.GetTeamsAsync())
                .Returns(Task.FromResult(
                        new Dictionary<string, string> {
                            { "key1", "Name1" },
                            { "key2", "Name2" },
                        }));

            var listOfUsageEntity = (await _resourceManager.GetTotalResouceUsageAsync().ConfigureAwait(false)).ToArray();
            // listOfUsageEntity is unordered. So Name2 is head of Name1
            Assert.AreEqual("Name2", listOfUsageEntity[0].Id);
            Assert.AreEqual("Name1", listOfUsageEntity[1].Id);
        }
    }
}
