﻿// <copyright file="AetherQuotaManagerTest.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Microsoft.Aether.QuotaService.Shared;
using Microsoft.RelInfra.Storage;
using Microsoft.RelInfra.Storage.Exceptions;
using Moq;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace Microsoft.Aether.QuotaService.Test
{
    [TestFixture]
    public class AetherQuotaManagerTest
    {
        private readonly string _userId = "testUserId";
        private readonly string _resourceId = "testResourceId";
        private readonly string _teamId = "testTeamId";
        private readonly int _defaultCount = 5;

        private Mock<IQuotaRoiClient> _quotaRoiClient;
        private Mock<IAzureBlobStorage> _quotaBlobStorage;

        private AetherQuotaManager _quotaManager;
        private ResourceManager _resourceManager;

        [SetUp]
        public void SetUp()
        {
            _quotaRoiClient = new Mock<IQuotaRoiClient>();
            _quotaRoiClient.Setup(q => q.GetUserTeamAsync(It.IsAny<string>(), It.IsAny<string>())).Returns(Task.FromResult(_teamId));

            _quotaManager = new AetherQuotaManager(_quotaRoiClient.Object);

            _quotaBlobStorage = new Mock<IAzureBlobStorage>();
            _quotaBlobStorage.Setup(q => q.TryGetAsync<AggregatedResourcesUsageEntity>(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .Returns((string s1, string entityId, CancellationToken token) => Task.FromResult(
                        new Storable<AggregatedResourcesUsageEntity>()
                        {
                            Entity = CreateExampleUsageEntity(entityId, DateTime.UtcNow),
                        }));
            _resourceManager = new ResourceManager(_quotaBlobStorage.Object, "testQuota", "testBlob", TimeSpan.FromDays(1), 1, _quotaRoiClient.Object);
        }

        [Test]
        public async Task TestReserveTeamUserQuotaAsync()
        {
            // case 1: resourceCount is smaller than limit, Available
            int resourceCount = 50;
            int limit = 60;
            var response1 = await _quotaManager.ReserveTeamUserQuotaAsync(_userId, _resourceId, resourceCount, _resourceManager, limit, new HashSet<string>(), _teamId, TimeSpan.FromSeconds(5)).ConfigureAwait(false);
            Assert.IsTrue(response1.IsQuotaAvailable);
            Assert.AreEqual(limit - resourceCount, response1.AvailableResource);
            Assert.AreEqual(limit, response1.QuotaLimit);
            Assert.AreEqual(_userId, response1.UserId);
            Assert.AreEqual(_resourceId, response1.ResourceId);
            Assert.AreEqual($"testQuota{_teamId}", response1.TeamId);

            // case 2: the quotaExampleUsers contain the user, Available
            resourceCount = 80;
            var response2 = await _quotaManager.ReserveTeamUserQuotaAsync(_userId, _resourceId, resourceCount, _resourceManager, limit, new HashSet<string> { _userId }, _teamId, TimeSpan.FromSeconds(5)).ConfigureAwait(false);
            Assert.IsTrue(response2.IsQuotaAvailable);

            // case 3: resourceCount is bigger than limit and the user is not example users , Unavailable
            resourceCount = 80;
            var response3 = await _quotaManager.ReserveTeamUserQuotaAsync(_userId, _resourceId, resourceCount, _resourceManager, limit, new HashSet<string>(), _teamId, TimeSpan.FromSeconds(5)).ConfigureAwait(false);
            Assert.IsFalse(response3.IsQuotaAvailable);
            Assert.AreEqual(limit - _defaultCount, response3.AvailableResource);
        }

        [Test]
        public async Task TestGetUserResourceUsageAsync()
        {
            var usage = await _quotaManager.GetUserResourceUsageAsync(_userId, _resourceId, _resourceManager).ConfigureAwait(false);
            Assert.AreEqual(_resourceId, usage.Id);
            Assert.AreEqual(_userId, usage.UserId);
            Assert.AreEqual(_defaultCount, usage.ResourceCount);
        }

        [Test]
        public async Task TestIncrementUserQuotaAsync()
        {
            int limit = 60;

            // case 1: based on the default count 5, add 50 count. Should be Available and available count is 5.
            int increaseCount = 50;
            var response1 = await _quotaManager.IncrementUserQuotaAsync(_userId, _resourceId, increaseCount, _resourceManager, limit, new HashSet<string>(), TimeSpan.FromSeconds(10)).ConfigureAwait(false);
            Assert.IsTrue(response1.IsQuotaAvailable);
            Assert.AreEqual(limit - increaseCount - _defaultCount, response1.AvailableResource);

            // case 2: based on the default count 5, add 56 count. Should be Unavabile and available count is original 55.
            int increaseCount2 = 56;
            var response2 = await _quotaManager.IncrementUserQuotaAsync(_userId, _resourceId, increaseCount2, _resourceManager, limit, new HashSet<string>(), TimeSpan.FromSeconds(10)).ConfigureAwait(false);
            Assert.IsFalse(response2.IsQuotaAvailable);
            Assert.AreEqual(limit - _defaultCount, response2.AvailableResource);
        }

        [Test]
        public async Task TestReleaseUserQuotaAsync()
        {
            var response = await _quotaManager.ReleaseUserQuotaAsync(_userId, _resourceId, _resourceManager, new HashSet<string>(), TimeSpan.FromSeconds(10)).ConfigureAwait(false);
            Assert.IsTrue(response.IsQuotaAvailable);
            Assert.IsNull(response.AvailableResource);
        }

        [Test]
        public async Task TestReserveUserQuotaAsync()
        {
            // case 1: resourceCount is smaller than limit, Available. And Assert the teamId is related to userId.
            int resourceCount = 50;
            int limit = 60;
            var response1 = await _quotaManager.ReserveUserQuotaAsync(_userId, _resourceId, resourceCount, _resourceManager, limit, new HashSet<string>(), TimeSpan.FromSeconds(5)).ConfigureAwait(false);
            Assert.IsTrue(response1.IsQuotaAvailable);
            Assert.AreEqual(limit - resourceCount, response1.AvailableResource);
            Assert.AreEqual(limit, response1.QuotaLimit);
            Assert.AreEqual(_userId, response1.UserId);
            Assert.AreEqual(_resourceId, response1.ResourceId);
            Assert.AreEqual($"testQuota{_userId}", response1.TeamId);

            // case 2: the quotaExampleUsers contain the user, Available
            resourceCount = 80;
            var response2 = await _quotaManager.ReserveUserQuotaAsync(_userId, _resourceId, resourceCount, _resourceManager, limit, new HashSet<string> { _userId }, TimeSpan.FromSeconds(5)).ConfigureAwait(false);
            Assert.IsTrue(response2.IsQuotaAvailable);

            // case 3: resourceCount is bigger than limit and the user is not example users , Unavailable
            resourceCount = 80;
            var response3 = await _quotaManager.ReserveUserQuotaAsync(_userId, _resourceId, resourceCount, _resourceManager, limit, new HashSet<string>(), TimeSpan.FromSeconds(5)).ConfigureAwait(false);
            Assert.IsFalse(response3.IsQuotaAvailable);
            Assert.AreEqual(limit - _defaultCount, response3.AvailableResource);
        }

        [Test]
        public async Task TestFreshReserveUserQuotaAsync()
        {
            // reset the quotaBlobStorage with null returnvalue
            var emptyBlobStorage = new Mock<IAzureBlobStorage>();
            _resourceManager = new ResourceManager(emptyBlobStorage.Object, "testQuota", "testBlob", TimeSpan.FromDays(1), 1, _quotaRoiClient.Object);

            // with empty UsageEntity, the Available status and count should be normal
            int resourceCount = 50;
            int limit = 60;
            var response1 = await _quotaManager.ReserveTeamUserQuotaAsync(_userId + "_dummy", _resourceId, resourceCount, _resourceManager, limit, new HashSet<string>(), _teamId, TimeSpan.FromSeconds(5)).ConfigureAwait(false);
            Assert.IsTrue(response1.IsQuotaAvailable);
            Assert.AreEqual(limit - resourceCount, response1.AvailableResource);
            Assert.AreEqual(limit, response1.QuotaLimit);
            Assert.AreEqual(_userId + "_dummy", response1.UserId);
            Assert.AreEqual(_resourceId, response1.ResourceId);
            Assert.AreEqual(_teamId, response1.TeamId);
        }

        [Test]
        public void TestEmptyIdThrowsAsync()
        {
            Assert.ThrowsAsync<ArgumentNullException>(async () => await _quotaManager.ReserveTeamUserQuotaAsync(null, null, 50, null, 60, null, null, TimeSpan.Zero).ConfigureAwait(false));
            Assert.ThrowsAsync<ArgumentNullException>(async () => await _quotaManager.ReserveUserQuotaAsync(null, null, 50, null, 60, null, TimeSpan.Zero).ConfigureAwait(false));
            Assert.ThrowsAsync<ArgumentNullException>(async () => await _quotaManager.ReserveTeamUserQuotaAsync(_userId, null, 50, null, 60, null, null, TimeSpan.Zero).ConfigureAwait(false));
        }

        [Test]
        public async Task TestUpdateQuotaAsyncRetryLogicAsync()
        {
            // Set _quotaBlobStorage.ExistsAsync throws Exception in first 3 calls, then return true.
            _quotaBlobStorage.SetupSequence(s => s.ExistsAsync(It.IsAny<string>(), It.IsAny<string>()))
                .Throws(new RelInfraStorageException() { Error = StorageError.PreconditionFailed })
                .Throws(new RelInfraStorageException() { Error = StorageError.PreconditionFailed })
                .Throws(new RelInfraStorageException() { Error = StorageError.PreconditionFailed })
                .Returns(Task.FromResult(true));

            // Assert the response result is normal, and the API of BlobStorage is called with right times.
            int resourceCount = 50;
            int limit = 60;
            var response = await _quotaManager.ReserveUserQuotaAsync(_userId, _resourceId, resourceCount, _resourceManager, limit, new HashSet<string>(), TimeSpan.FromSeconds(5)).ConfigureAwait(false);
            Assert.IsTrue(response.IsQuotaAvailable);
            Assert.AreEqual(limit - resourceCount, response.AvailableResource);
            _quotaBlobStorage.Verify(s => s.ExistsAsync(It.IsAny<string>(), It.IsAny<string>()), Times.Exactly(4));
            _quotaBlobStorage.Verify(s => s.UpdateAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<Storable<AggregatedResourcesUsageEntity>>()), Times.Once);
        }

        [Test]
        public async Task TestDifferentDeltaTimeForUsageStatAsync()
        {
            // To test if the response is correct under different delta time between submissionTime and DateTime.UtcNow
            _quotaBlobStorage.SetupSequence(q => q.TryGetAsync<AggregatedResourcesUsageEntity>(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .Returns(Task.FromResult(
                        new Storable<AggregatedResourcesUsageEntity>()
                        {
                            Entity = CreateExampleUsageEntity(_userId, DateTime.UtcNow - TimeSpan.FromHours(3)),
                        }))
                .Returns(Task.FromResult(
                        new Storable<AggregatedResourcesUsageEntity>()
                        {
                            Entity = CreateExampleUsageEntity(_userId, DateTime.UtcNow - TimeSpan.FromMinutes(40)),
                        }))
                .Returns(Task.FromResult(
                        new Storable<AggregatedResourcesUsageEntity>()
                        {
                            Entity = CreateExampleUsageEntity(_userId, DateTime.UtcNow - TimeSpan.FromMinutes(10)),
                        }))
                .Returns(Task.FromResult(
                        new Storable<AggregatedResourcesUsageEntity>()
                        {
                            Entity = CreateExampleUsageEntity(_userId, DateTime.UtcNow),
                        }));

            int limit = 60;
            int resourceCount = 80;
            // Called _quotaManager.ReserveTeamUserQuotaAsync 4 times, with 3 hour, 40 min, 10 min and miliseconds. The first 3 cases the Usage for 5min is always 0 so never exceed limit, but the last one is the real number so will exceed limit.
            var response3h = await _quotaManager.ReserveTeamUserQuotaAsync(_userId, _resourceId, resourceCount, _resourceManager, limit, new HashSet<string>(), _teamId, TimeSpan.FromDays(5)).ConfigureAwait(false);
            var response1h = await _quotaManager.ReserveTeamUserQuotaAsync(_userId, _resourceId, resourceCount, _resourceManager, limit, new HashSet<string>(), _teamId, TimeSpan.FromDays(5)).ConfigureAwait(false);
            var response30min = await _quotaManager.ReserveTeamUserQuotaAsync(_userId, _resourceId, resourceCount, _resourceManager, limit, new HashSet<string>(), _teamId, TimeSpan.FromDays(5)).ConfigureAwait(false);
            var response5min = await _quotaManager.ReserveTeamUserQuotaAsync(_userId, _resourceId, resourceCount, _resourceManager, limit, new HashSet<string>(), _teamId, TimeSpan.FromDays(5)).ConfigureAwait(false);
            Assert.IsTrue(response3h.IsQuotaAvailable);
            Assert.IsTrue(response1h.IsQuotaAvailable);
            Assert.IsTrue(response30min.IsQuotaAvailable);
            Assert.IsFalse(response5min.IsQuotaAvailable);
        }

        internal AggregatedResourcesUsageEntity CreateExampleUsageEntity(string entityId, DateTime submissionTime)
        {
            return new AggregatedResourcesUsageEntity()
            {
                Id = entityId,
                ConsumedResources = new List<ResourceUsageEntity>()
                {
                    new ResourceUsageEntity()
                    {
                        Id = _resourceId,
                        UserId = _userId,
                        ResourceCount = _defaultCount,
                        SubmissionTime = submissionTime,
                    }
                }
            };
        }
    }
}