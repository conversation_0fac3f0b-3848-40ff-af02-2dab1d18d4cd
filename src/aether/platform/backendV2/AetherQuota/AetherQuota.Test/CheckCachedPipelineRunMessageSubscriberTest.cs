﻿// <copyright file="CheckCachedPipelineRunMessageSubscriberTest.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Microsoft.Aether.DataContracts.Entity;
using Microsoft.Aether.QuotaService.Shared;
using Microsoft.Aether.TaggedQueueManager;
using Microsoft.Aether.TaggedQueueManager.CloudTags;
using Microsoft.Aether.TaggedQueueManagerTest.TaggedQueueManagerHelperTests;
using Microsoft.MachineLearning.Common.ServiceBus;
using Microsoft.RelInfra.Instrumentation;
using Microsoft.RelInfra.Storage;
using Moq;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Microsoft.Aether.QuotaService.Test
{
    public class CheckCachedPipelineRunMessageSubscriberTest
    {
        private readonly MockRepository _repository;
        private readonly CounterManager _counterManager;
        private readonly QuotaServiceConfig _serviceConfig;
        private readonly WorkspaceIdentity _workspace;

        private Mock<IQueryableDbStorage> _limitContainer;
        private Mock<IQueryableDbStorage> _usageContainer;
        private Mock<IQueryableDbStorage> _cachedContainer;

        private ICachedPipelineRunManager _cachedPipelineRunManager;
        private RunningPipelineRunQuotaManager _hardQuotaManager;
        private RunningPipelineRunSoftQuotaManager _softQuotaManager;
        private CheckCachedPipelineRunMessageSubscriber _checkCachedSubscriber;

        private Mock<IServiceBusTopicPublisher> _topicPublisher;

        private IExperimentQueuePublisher _experimentQueuePublisher;
        private ITaggedQueueManager<AssignedQueueIdTags, string> _experimentQueueManager;

        public CheckCachedPipelineRunMessageSubscriberTest()
        {
            _repository = new MockRepository(MockBehavior.Loose) { DefaultValue = DefaultValue.Mock, CallBase = true };

            _counterManager = _repository.Create<CounterManager>(string.Empty, _repository.Create<ICounterFactory>().Object).Object;
            QuotaServiceTestHelper.InitCounter(_repository, _counterManager);

            _serviceConfig = new QuotaServiceConfig(QuotaServiceConfigMock.GetConfiguration(), secretProvider: null);

            _workspace = new WorkspaceIdentity
            {
                SubscriptionId = Guid.NewGuid().ToString(),
                ResourceGroupName = "testFakeGroup1",
                WorkspaceName = "testFakeWorkspace1",
                WorkspaceId = Guid.NewGuid().ToString(),
            };
        }

        [SetUp]
        public void SetUp()
        {
            _topicPublisher = _repository.Create<IServiceBusTopicPublisher>();

            _experimentQueueManager = new TaggedQueueManagerMock<AssignedQueueIdTags, string>("queuePrefix");
            _experimentQueuePublisher = new ExperimentQueuePublisher(_experimentQueueManager, _counterManager);

            _limitContainer = _repository.Create<IQueryableDbStorage>();
            QuotaServiceTestHelper.SetupMockStorage(_limitContainer, new Dictionary<string, Storable<RunningPipelineRunQuotaLimitEntity>>());
            _usageContainer = _repository.Create<IQueryableDbStorage>();
            QuotaServiceTestHelper.SetupMockStorage(_usageContainer, new Dictionary<string, Storable<RunningPipelineRunQuotaUsageEntity>>());
            _cachedContainer = _repository.Create<IQueryableDbStorage>();
            QuotaServiceTestHelper.SetupMockStorage(_cachedContainer, new Dictionary<string, Storable<CachedPipelineRunEntity>>());

            _cachedPipelineRunManager = new CachedPipelineRunManager(_serviceConfig, _counterManager, _cachedContainer.Object, _experimentQueuePublisher);
            _hardQuotaManager = new RunningPipelineRunQuotaManager(_serviceConfig, _limitContainer.Object, _usageContainer.Object);
            _softQuotaManager = new RunningPipelineRunSoftQuotaManager(
                _serviceConfig,
                _limitContainer.Object,
                _usageContainer.Object,
                _cachedPipelineRunManager,
                _topicPublisher.Object);

            // SubscriptionClient will parse connection string and need correct format.
            string fakeConnectionString = "Endpoint=sb://foo.servicebus.windows.net/;" + "SharedAccessKeyName=someKeyName;" + "SharedAccessKey=someKeyValue";
            _checkCachedSubscriber = new CheckCachedPipelineRunMessageSubscriber(
                fakeConnectionString,
                _serviceConfig,
                _counterManager,
                "testTopicName",
                "testSubscriptionName",
                _softQuotaManager,
                _cachedPipelineRunManager,
                _experimentQueuePublisher);
        }

        [Test]
        public async Task TestCheckCachedPipelineRunAndPutBackAsync()
        {
            int softQuotaLimit = 5;
            int hardQuotaLimit = 10;
            await _softQuotaManager.SetQuotaLimitAsync(_workspace, softQuotaLimit);
            await _hardQuotaManager.SetQuotaLimitAsync(_workspace, hardQuotaLimit);

            string experimentIdPrefix = "testExperimentId";
            for (int i = 0; i < hardQuotaLimit; i++)
            {
                string experimentId = $"{experimentIdPrefix}{i}";
                if (i < softQuotaLimit)
                {
                    // Soft quota is available.
                    var usageEntity = QuotaServiceTestHelper.GenerateRunningPipelineRunQuotaUsageEntity(_workspace, experimentId, isSoftQuota: true);
                    var quotaResponse = await _softQuotaManager.AcquireQuotaAsync(_workspace, usageEntity);
                    Assert.IsTrue(quotaResponse.IsQuotaAvailable);
                }
                else
                {
                    // Exceed soft quota limit, try to acquire hard quota.
                    var usageEntity = QuotaServiceTestHelper.GenerateRunningPipelineRunQuotaUsageEntity(_workspace, experimentId, isSoftQuota: true);
                    var quotaResponse = await _softQuotaManager.AcquireQuotaAsync(_workspace, usageEntity);
                    Assert.IsFalse(quotaResponse.IsQuotaAvailable);

                    usageEntity.IsSoftQuota = false;
                    quotaResponse = await _hardQuotaManager.AcquireQuotaAsync(_workspace, usageEntity);
                    Assert.IsTrue(quotaResponse.IsQuotaAvailable);

                    // Soft quota is unavailable but hard quota is available. Add pipeline run into cache.
                    string experimentQueueMessage = $"serializedMessage{i}";
                    var cachedEntity = QuotaServiceTestHelper.GenerateCachedPipelineRunEntity(usageEntity.Id, _workspace, DateTime.UtcNow, experimentQueueMessage);
                    bool cached = await _cachedPipelineRunManager.AddCachedPipelineRunAsync(_workspace, cachedEntity);
                    Assert.IsTrue(cached);
                }
            }

            // Current quota usage is same as limit and occupied all quota.
            var softQuotaResponse = await _softQuotaManager.GetCurrentQuotaAsync(_workspace);
            Assert.AreEqual(softQuotaLimit, softQuotaResponse.CurrentUsageCount);
            var hardQuotaResponse = await _hardQuotaManager.GetCurrentQuotaAsync(_workspace);
            Assert.AreEqual(hardQuotaLimit, hardQuotaResponse.CurrentUsageCount);

            int expectedCachedCount = hardQuotaLimit - softQuotaLimit;
            int cachedCount = (await _cachedPipelineRunManager.GetAllCachedPipelineRunsAsync(_workspace)).Results.Count();
            Assert.AreEqual(expectedCachedCount, cachedCount);
            // Has cached pipeline run but no soft quota available. No cached pipeline run is put back into experiment queue after checking.
            await _checkCachedSubscriber.CheckCachedPipelineRunAndPutBackAsync(_workspace, CancellationToken.None);
            cachedCount = (await _cachedPipelineRunManager.GetAllCachedPipelineRunsAsync(_workspace)).Results.Count();
            Assert.AreEqual(expectedCachedCount, cachedCount);

            // Release 1 soft quota.
            var usageEntity1 = QuotaServiceTestHelper.GenerateRunningPipelineRunQuotaUsageEntity(_workspace, $"{experimentIdPrefix}0", isSoftQuota: true);
            bool released = await _softQuotaManager.ReleaseQuotaAsync(_workspace, usageEntity1);
            Assert.IsTrue(released);
            softQuotaResponse = await _softQuotaManager.GetCurrentQuotaAsync(_workspace);
            Assert.AreEqual(softQuotaLimit - 1, softQuotaResponse.CurrentUsageCount);

            // Check and will put back a pipeline run into experiment queue.
            await _checkCachedSubscriber.CheckCachedPipelineRunAndPutBackAsync(_workspace, CancellationToken.None);
            cachedCount = (await _cachedPipelineRunManager.GetAllCachedPipelineRunsAsync(_workspace)).Results.Count();
            Assert.AreEqual(expectedCachedCount - 1, cachedCount);

            // The pipeline run released hard quota and change to occupy soft quota.
            softQuotaResponse = await _softQuotaManager.GetCurrentQuotaAsync(_workspace);
            Assert.AreEqual(softQuotaLimit, softQuotaResponse.CurrentUsageCount);
            hardQuotaResponse = await _hardQuotaManager.GetCurrentQuotaAsync(_workspace);
            Assert.AreEqual(hardQuotaLimit - 1, hardQuotaResponse.CurrentUsageCount);

            // Release all soft quota.
            foreach (var softQuotaUsage in await _softQuotaManager.GetAllUsagesAsync(_workspace))
            {
                released = await _softQuotaManager.ReleaseQuotaAsync(_workspace, softQuotaUsage);
                Assert.IsTrue(released);
            }

            // Check and will put remaining cached pipeline run into experiment queue.
            int expectedSoftQuotaUsageCount = (await _cachedPipelineRunManager.GetAllCachedPipelineRunsAsync(_workspace)).Results.Count();
            await _checkCachedSubscriber.CheckCachedPipelineRunAndPutBackAsync(_workspace, CancellationToken.None);
            cachedCount = (await _cachedPipelineRunManager.GetAllCachedPipelineRunsAsync(_workspace)).Results.Count();
            Assert.AreEqual(0, cachedCount);

            // The pipeline run released hard quota and occupy soft quota. 
            softQuotaResponse = await _softQuotaManager.GetCurrentQuotaAsync(_workspace);
            Assert.AreEqual(expectedSoftQuotaUsageCount, softQuotaResponse.CurrentUsageCount);
            // No cached pipeline run occupy hard quota.
            hardQuotaResponse = await _hardQuotaManager.GetCurrentQuotaAsync(_workspace);
            Assert.AreEqual(expectedSoftQuotaUsageCount, hardQuotaResponse.CurrentUsageCount);
        }
    }
}
