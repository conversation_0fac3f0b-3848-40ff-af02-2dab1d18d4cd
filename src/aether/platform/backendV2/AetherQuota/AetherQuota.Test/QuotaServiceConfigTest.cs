﻿// <copyright file="QuotaServiceConfigTest.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using NUnit.Framework;
using System;
using System.Collections.Generic;

namespace Microsoft.Aether.QuotaService.Test
{
    [TestFixture]
    public class QuotaServiceConfigTest
    {
        private readonly QuotaServiceConfig _config = new QuotaServiceConfig(AetherQuotaConfigMock.GetConfiguration(), secretProvider: null);

        [Test]
        public void TestQuotaSettings()
        {
            Assert.AreEqual("experimentsQuotaPrefix", _config.TeamExperimentsQuotaPrefix);
            Assert.AreEqual("JobsQuotaPrefix", _config.UserRunningJobsQuotaPrefix);
            Assert.AreEqual("emailsQuotaPrefix", _config.TeamEmailsQuotaPrefix);
            Assert.AreEqual("promotionsQuotaPrefix", _config.TeamPromotionsQuotaPrefix);
            Assert.AreEqual("UploadQuotaPrefix", _config.TeamUploadsQuotaPrefix);
            Assert.AreEqual(3, _config.MaxParallelismDegree);
            Assert.AreEqual(TimeSpan.FromMinutes(1), _config.SubmissionExpirationInterval);
            Assert.AreEqual(TimeSpan.FromHours(23), _config.CacheExpirationInterval);
            Assert.AreEqual(10, _config.NodesLimit);
            Assert.AreEqual(TimeSpan.FromHours(2), _config.RunningJobExpirationInterval);
            Assert.AreEqual(10, _config.RunningNodesLimit);
            Assert.AreEqual(5, _config.EmailsLimit);
            Assert.AreEqual(5, _config.UploadsLimit);
            Assert.AreEqual(5, _config.PromotionsLimit);
        }

        [Test]
        public void TestAzureSettings()
        {
            Assert.AreEqual(3, _config.AzureBlobRetriesNumber);
            Assert.AreEqual(TimeSpan.FromMinutes(1), _config.AzureBlobRetryInterval);
            Assert.AreEqual(TimeSpan.FromHours(2), _config.AzureBlobTimeoutInterval);
            Assert.AreEqual("blobContainerName", _config.AzureBlobContainerName);
            Assert.AreEqual("userRunningContainerName", _config.UserRunningJobsBlobContainerName);
            Assert.AreEqual(5, _config.MaxBlobsToListForQuotaStat);
        }

        [Test]
        public void TestServiceSettings()
        {
            Assert.AreEqual("http://roi.aether", _config.RoiServiceBaseUrl);
            Assert.AreEqual("teamId", _config.DefaultTeamId);
            Assert.AreEqual("serviceName", _config.ServiceName);
            Assert.AreEqual(100, _config.MaxReceivedMessageSize);
            Assert.AreEqual(23304, _config.Port);
            Assert.AreEqual("secretPath", _config.GetSslCert());
        }

        [Test]
        public void TestExemptionSettings()
        {
            HashSet<string> nodes = _config.NodeExemption;
            Assert.IsTrue(nodes.Contains("node1"));
            Assert.IsTrue(nodes.Contains("node2"));
            Assert.IsTrue(nodes.Contains("node2"));

            HashSet<string> emails = _config.EmailExemption;
            Assert.IsTrue(emails.Contains("email1"));
            Assert.IsTrue(emails.Contains("email2"));
            Assert.IsTrue(emails.Contains("email3"));

            HashSet<string> uploads = _config.UploadsExemption;
            Assert.IsTrue(uploads.Contains("upload1"));
            Assert.IsTrue(uploads.Contains("upload2"));
            Assert.IsTrue(uploads.Contains("upload3"));

            HashSet<string> promotions = _config.PromotionsExemption;
            Assert.IsTrue(promotions.Contains("promotion1"));
            Assert.IsTrue(promotions.Contains("promotion2"));
            Assert.IsTrue(promotions.Contains("promotion3"));
        }
    }
}
