﻿// <copyright file="QuotaServiceConfigMock.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Collections.Generic;
using Microsoft.RelInfra.Common.Configuration;

namespace Microsoft.Aether.QuotaService.Test
{
    public static class QuotaServiceConfigMock
    {
        public static IRelInfraConfiguration GetConfiguration()
        {
            var settings = new Dictionary<string, string>
            {
                { "Service.Name", "serviceName" },
                { "CosmosDb.DatabaseName", "QuotaService" },
                { "CosmosDb.RunningPipelineRunQuotaLimitContainerName", "RunningPipelineRunQuotaLimit" },
                { "CosmosDb.RunningPipelineRunQuotaUsageContainerName", "RunningPipelineRunQuotaUsage" },
                { "CosmosDb.CachedPipelineRunContainerName", "CachedPipelineRun" },
                { "CosmosDb.ConnectionCacheDuration", "50.00:00:00" },
                { "CosmosDb.RequestTimeout", "00:01:00" },
                { "CosmosDb.ContainerThroughput", "10000" },
                { "CosmosDb.IsAutoscale", "23:00:00" },
                { "AzureStorage.BlobLeaseContainerName", "qsleaselock" },
                { "QuotaSettings.RunningPipelineRunDefaultQuotaLimit", "1000" },
                { "QuotaSettings.RunningPipelineRunDefaultSoftQuotaLimit", "100" },
                { "QuotaSettings.RunningPipelineRunReviseTaskTimeout", "00:10:00" },
                { "QuotaSettings.RunningPipelineRunReviseTaskInterval", "01:00:00" },
                { "QuotaSettings.MaxParallelismDegree", "3" },
            };

            return new DynamicConfigurationFlexMock(settings);
        }
    }
}
