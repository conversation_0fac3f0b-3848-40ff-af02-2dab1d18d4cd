<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
  </PropertyGroup>
  <Import Project="..\..\..\QTest.props" />
  <ItemGroup>
    <PackageReference Include="NUnit" />
    <PackageReference Include="NUnit3TestAdapter" />
    <PackageReference Include="Moq" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" />
    <PackageReference Include="coverlet.collector" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\shared\Microsoft.Aether.TaggedQueueManagerTest\Microsoft.Aether.TaggedQueueManagerTest.csproj" />
    <ProjectReference Include="..\AetherQuota.Common\AetherQuota.Common.csproj" />
    <ProjectReference Include="..\..\CloudManager\Common.Test\Microsoft.Aether.CloudManager.Common.Test.csproj" />
  </ItemGroup>
</Project>
