﻿// <copyright file="CachedPipelineRunManagerTests.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Microsoft.Aether.DataContracts.Entity;
using Microsoft.Aether.QuotaService.Shared;
using Microsoft.Aether.TaggedQueueManager;
using Microsoft.Aether.TaggedQueueManager.CloudTags;
using Microsoft.Aether.TaggedQueueManagerTest.TaggedQueueManagerHelperTests;
using Microsoft.RelInfra.Instrumentation;
using Microsoft.RelInfra.Storage;
using Moq;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Microsoft.Aether.QuotaService.Test
{
    [TestFixture]
    public class CachedPipelineRunManagerTests
    {
        private MockRepository _repository;
        private CounterManager _counterManager;
        private CachedPipelineRunManager _cachedPipelineRunManager;
        private QuotaServiceConfig _serviceConfig;
        private Mock<IQueryableDbStorage> _cachedContainer;
        private IExperimentQueuePublisher _experimentQueuePublisher;
        private ITaggedQueueManager<AssignedQueueIdTags, string> _experimentQueueManager;

        private WorkspaceIdentity _workspace;

        [SetUp]
        public void SetUp()
        {
            _repository = new MockRepository(MockBehavior.Loose) { DefaultValue = DefaultValue.Mock, CallBase = true };

            _cachedContainer = _repository.Create<IQueryableDbStorage>();
            QuotaServiceTestHelper.SetupMockStorage(_cachedContainer, new Dictionary<string, Storable<CachedPipelineRunEntity>>());

            _counterManager = _repository.Create<CounterManager>(string.Empty, _repository.Create<ICounterFactory>().Object).Object;
            QuotaServiceTestHelper.InitCounter(_repository, _counterManager);

            _serviceConfig = new QuotaServiceConfig(QuotaServiceConfigMock.GetConfiguration(), secretProvider: null);

            _experimentQueueManager = new TaggedQueueManagerMock<AssignedQueueIdTags, string>("queuePrefix");
            _experimentQueuePublisher = new ExperimentQueuePublisher(_experimentQueueManager, _counterManager);

            _cachedPipelineRunManager = new CachedPipelineRunManager(_serviceConfig, _counterManager, _cachedContainer.Object, _experimentQueuePublisher);

            _workspace = new WorkspaceIdentity
            {
                SubscriptionId = Guid.NewGuid().ToString(),
                ResourceGroupName = "testFakeGroup",
                WorkspaceName = "testFakeWorkspace",
                WorkspaceId = Guid.NewGuid().ToString(),
            };
        }

        [Test]
        public async Task TestAddAndRemoveCachedPipelineRunAsync()
        {
            var pipelineRun1 = QuotaServiceTestHelper.GenerateCachedPipelineRunEntity(Guid.NewGuid().ToString(), _workspace, DateTime.UtcNow);

            // Add cached pipeline run
            await _cachedPipelineRunManager.AddCachedPipelineRunAsync(_workspace, pipelineRun1);
            var allCached = await _cachedPipelineRunManager.GetAllCachedPipelineRunsAsync(_workspace);
            Assert.AreEqual(1, allCached.Results.Count());

            // Add cached pipeline run again to monitor retry AddCachedPipelineRunAsync case
            await _cachedPipelineRunManager.AddCachedPipelineRunAsync(_workspace, pipelineRun1);
            allCached = await _cachedPipelineRunManager.GetAllCachedPipelineRunsAsync(_workspace);
            Assert.AreEqual(1, allCached.Results.Count());

            // Remove cached pipeline run
            var cached = allCached.Results.First().Entity;
            var deleted = await _cachedPipelineRunManager.RemoveCachedPipelineRunAsync(_workspace, cached.Id);
            Assert.IsTrue(deleted);
            allCached = await _cachedPipelineRunManager.GetAllCachedPipelineRunsAsync(_workspace);
            Assert.AreEqual(0, allCached.Results.Count());

            // Remove cached pipeline run again to monitor retry RemoveCachedPipelineRunAsync case
            deleted = await _cachedPipelineRunManager.RemoveCachedPipelineRunAsync(_workspace, cached.Id);
            Assert.IsFalse(deleted);
        }

        [Test]
        public async Task TestCancelCachedPipelineRunAsync()
        {
            var pipelineRun1 = QuotaServiceTestHelper.GenerateCachedPipelineRunEntity(Guid.NewGuid().ToString(), _workspace, DateTime.UtcNow);

            // Add cached pipeline run
            await _cachedPipelineRunManager.AddCachedPipelineRunAsync(_workspace, pipelineRun1);
            var allCached = await _cachedPipelineRunManager.GetAllCachedPipelineRunsAsync(_workspace);
            Assert.AreEqual(1, allCached.Results.Count());

            // Cancel cached pipeline run
            var cancelled = await _cachedPipelineRunManager.CancelCachedPipelineRunAsync(_workspace, pipelineRun1.Id);
            Assert.IsTrue(cancelled);
            allCached = await _cachedPipelineRunManager.GetAllCachedPipelineRunsAsync(_workspace);
            Assert.AreEqual(0, allCached.Results.Count());
            // The pipeline run is put back to queue
            var tagsDescriptor = new AssignedQueueIdDescriptor
            {
                QueueId = QueueAssignmentHelper.GetQueueId(_workspace.SubscriptionId)
            };
            var taggedQueues = _experimentQueueManager.GetQueues(tagsDescriptor);
            Assert.AreEqual(1, taggedQueues.Count());
            var queue = taggedQueues.First().Queue;
            var messageCount = await queue.GetQueueLengthAsync();
            Assert.AreEqual(1, messageCount);

            // Cancel cached pipeline run that does not exist
            cancelled = await _cachedPipelineRunManager.CancelCachedPipelineRunAsync(_workspace, pipelineRun1.Id);
            Assert.IsFalse(cancelled);
            allCached = await _cachedPipelineRunManager.GetAllCachedPipelineRunsAsync(_workspace);
            Assert.AreEqual(0, allCached.Results.Count());
        }

        [Test]
        public async Task TestGetCachedPipelineRunAsync()
        {
            var pipelineRun1 = QuotaServiceTestHelper.GenerateCachedPipelineRunEntity(Guid.NewGuid().ToString(), _workspace, DateTime.UtcNow);

            // Add cached pipeline run
            await _cachedPipelineRunManager.AddCachedPipelineRunAsync(_workspace, pipelineRun1);
            var allCached = await _cachedPipelineRunManager.GetAllCachedPipelineRunsAsync(_workspace);
            Assert.AreEqual(1, allCached.Results.Count());

            // Get cached pipeline run
            var cached = await _cachedPipelineRunManager.GetCachedPipelineRunAsync(_workspace, pipelineRun1.Id);
            Assert.AreEqual(pipelineRun1, cached);

            // Remove cached pipeline run
            var deleted = await _cachedPipelineRunManager.RemoveCachedPipelineRunAsync(_workspace, cached.Id);
            Assert.IsTrue(deleted);

            // Get cached pipeline run that does not exist
            var pipelineRun2 = QuotaServiceTestHelper.GenerateCachedPipelineRunEntity(Guid.NewGuid().ToString(), _workspace, DateTime.UtcNow);
            cached = await _cachedPipelineRunManager.GetCachedPipelineRunAsync(_workspace, pipelineRun2.Id);
            Assert.AreEqual(null, cached);
        }

        [Test]
        public async Task TestHasCachedPipelineRunAsync()
        {
            var pipelineRun1 = QuotaServiceTestHelper.GenerateCachedPipelineRunEntity(Guid.NewGuid().ToString(), _workspace, DateTime.UtcNow);
            var hasCached = await _cachedPipelineRunManager.HasCachedPipelineRunAsync(_workspace);
            Assert.IsFalse(hasCached);

            // Add cached pipeline run
            await _cachedPipelineRunManager.AddCachedPipelineRunAsync(_workspace, pipelineRun1);
            hasCached = await _cachedPipelineRunManager.HasCachedPipelineRunAsync(_workspace);
            Assert.IsTrue(hasCached);

            // Remove cached pipeline run
            var deleted = await _cachedPipelineRunManager.RemoveCachedPipelineRunAsync(_workspace, pipelineRun1.Id);
            Assert.IsTrue(deleted);
        }

        [Test]
        public async Task TestGetTopKCachedPipelineRunsAsync()
        {
            var pipelineRun1 = QuotaServiceTestHelper.GenerateCachedPipelineRunEntity(Guid.NewGuid().ToString(), _workspace, DateTime.UtcNow);
            var pipelineRun2 = QuotaServiceTestHelper.GenerateCachedPipelineRunEntity(Guid.NewGuid().ToString(), _workspace, DateTime.UtcNow.AddHours(-1));
            var pipelineRun3 = QuotaServiceTestHelper.GenerateCachedPipelineRunEntity(Guid.NewGuid().ToString(), _workspace, DateTime.UtcNow.AddDays(-1));
            var pipelineRun4 = QuotaServiceTestHelper.GenerateCachedPipelineRunEntity(Guid.NewGuid().ToString(), _workspace, DateTime.UtcNow.AddDays(1));

            await _cachedPipelineRunManager.AddCachedPipelineRunAsync(_workspace, pipelineRun1);
            await _cachedPipelineRunManager.AddCachedPipelineRunAsync(_workspace, pipelineRun2);
            await _cachedPipelineRunManager.AddCachedPipelineRunAsync(_workspace, pipelineRun3);
            await _cachedPipelineRunManager.AddCachedPipelineRunAsync(_workspace, pipelineRun4);
            var cacheds = await _cachedPipelineRunManager.GetAllCachedPipelineRunsAsync(_workspace);
            Assert.AreEqual(4, cacheds.Results.Count());

            // Get top 3 cached pipeline runs
            var queryResults = await _cachedPipelineRunManager.GetTopKCachedPipelineRunsAsync(3, _workspace);
            var topCached = queryResults.NonStorableResults.ToList();
            Assert.AreEqual(3, topCached.Count());
            Assert.AreEqual(pipelineRun3.Id, topCached[0].Id);
            Assert.AreEqual(pipelineRun2.Id, topCached[1].Id);
            Assert.AreEqual(pipelineRun1.Id, topCached[2].Id);

            foreach (var cached in topCached)
            {
                var deleted = await _cachedPipelineRunManager.RemoveCachedPipelineRunAsync(_workspace, cached.Id);
                Assert.IsTrue(deleted);
            }

            // Get top 2 cached pipeline run (there is only 1 cached pipeline run now)
            topCached = (await _cachedPipelineRunManager.GetTopKCachedPipelineRunsAsync(2, _workspace)).NonStorableResults.ToList();
            Assert.AreEqual(1, topCached.Count());
            Assert.AreEqual(pipelineRun4.Id, topCached[0].Id);

            foreach (var cached in topCached)
            {
                var deleted = await _cachedPipelineRunManager.RemoveCachedPipelineRunAsync(_workspace, cached.Id);
                Assert.IsTrue(deleted);
            }
        }
    }
}
