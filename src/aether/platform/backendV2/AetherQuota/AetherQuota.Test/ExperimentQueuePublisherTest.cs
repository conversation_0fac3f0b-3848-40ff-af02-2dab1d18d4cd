﻿// <copyright file="ExperimentQueuePublisherTest.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Microsoft.Aether.DataContracts.Entity;
using Microsoft.Aether.TaggedQueueManager;
using Microsoft.Aether.TaggedQueueManager.CloudTags;
using Microsoft.Aether.TaggedQueueManagerTest.TaggedQueueManagerHelperTests;
using Microsoft.RelInfra.Instrumentation;
using Moq;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Microsoft.Aether.QuotaService.Test
{
    public class ExperimentQueuePublisherTest
    {
        private MockRepository _repository;
        private CounterManager _counterManager;
        private IExperimentQueuePublisher _experimentQueuePublisher;
        private ITaggedQueueManager<AssignedQueueIdTags, string> _experimentQueueManager;

        private WorkspaceIdentity _workspace1;
        private WorkspaceIdentity _workspace2;

        [SetUp]
        public void Init()
        {
            _repository = new MockRepository(MockBehavior.Loose) { DefaultValue = DefaultValue.Mock, CallBase = true };

            _counterManager = _repository.Create<CounterManager>(string.Empty, _repository.Create<ICounterFactory>().Object).Object;
            QuotaServiceTestHelper.InitCounter(_repository, _counterManager);

            _experimentQueueManager = new TaggedQueueManagerMock<AssignedQueueIdTags, string>("queuePrefix");
            _experimentQueuePublisher = new ExperimentQueuePublisher(_experimentQueueManager, _counterManager);

            _workspace1 = new WorkspaceIdentity
            {
                SubscriptionId = "testFakeSub1",
                ResourceGroupName = "testFakeGroup1",
                WorkspaceName = "testFakeWorkspace1",
                WorkspaceId = Guid.NewGuid().ToString(),
            };

            _workspace2 = new WorkspaceIdentity
            {
                SubscriptionId = "testFakeSub2",
                ResourceGroupName = "testFakeGroup2",
                WorkspaceName = "testFakeWorkspace2",
                WorkspaceId = Guid.NewGuid().ToString(),
            };
        }

        [Test]
        public async Task TestEnqueueMessageAsync()
        {
            string experimentId1 = Guid.NewGuid().ToString();
            string queueMessage1 = "serializedQueueMessage1";
            string experimentId2 = Guid.NewGuid().ToString();
            string queueMessage2 = "serializedQueueMessage2";
            string experimentId3 = Guid.NewGuid().ToString();
            string queueMessage3 = "serializedQueueMessage3";
            await _experimentQueuePublisher.EnqueueExperimentAsync(_workspace1, experimentId1, queueMessage1);
            await _experimentQueuePublisher.EnqueueExperimentAsync(_workspace1, experimentId2, queueMessage2);
            await _experimentQueuePublisher.EnqueueExperimentAsync(_workspace2, experimentId3, queueMessage3);

            // workspace1 queue has 2 messages.
            var tagsDescriptor1 = new AssignedQueueIdDescriptor
            {
                QueueId = QueueAssignmentHelper.GetQueueId(_workspace1.SubscriptionId)
            };
            var taggedQueues = _experimentQueueManager.GetQueues(tagsDescriptor1);
            Assert.AreEqual(1, taggedQueues.Count());
            var queue1 = taggedQueues.First().Queue;
            var messageCount = await queue1.GetQueueLengthAsync();
            Assert.AreEqual(2, messageCount);

            // workspace2 queue has 1 message.
            var tagsDescriptor2 = new AssignedQueueIdDescriptor
            {
                QueueId = QueueAssignmentHelper.GetQueueId(_workspace2.SubscriptionId)
            };
            taggedQueues = _experimentQueueManager.GetQueues(tagsDescriptor2);
            Assert.AreEqual(1, taggedQueues.Count());
            var queue2 = taggedQueues.First().Queue;
            messageCount = await queue2.GetQueueLengthAsync();
            Assert.AreEqual(1, messageCount);
        }
    }
}
