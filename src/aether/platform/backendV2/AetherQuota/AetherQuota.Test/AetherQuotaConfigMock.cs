﻿// <copyright file="AetherQuotaConfigMock.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Microsoft.Aether.CloudManager.Common.Test;
using Microsoft.RelInfra.Common.Configuration;
using System.Collections.Generic;

namespace Microsoft.Aether.QuotaService.Test
{
    public static class AetherQuotaConfigMock
    {
        public static IRelInfraConfiguration GetConfiguration()
        {
            var settings = new Dictionary<string, string>(CloudStorageConfigurationMock.GetSettings())
            {
                { "QuotaSettings.TeamExperimentsQuotaPrefix", "experimentsQuotaPrefix" },
                { "QuotaSettings.UserRunningJobsQuotaPrefix", "JobsQuotaPrefix" },
                { "QuotaSettings.TeamEmailsQuotaPrefix", "emailsQuotaPrefix" },
                { "QuotaSettings.TeamPromotionsQuotaPrefix", "promotionsQuotaPrefix" },
                { "QuotaSettings.TeamUploadsQuotaPrefix", "UploadQuotaPrefix" },
                { "QuotaSettings.MaxParallelismDegree", "3" },
                { "QuotaSettings.SubmissionExpirationInterval", "00:01:00" },
                { "QuotaSettings.CacheExpirationInterval", "23:00:00" },
                { "SharedRoi.ServiceAddress", "http://roi.aether" },
                { "SharedRoi.DefaultTeamId", "teamId" },
                { "Service.Name", "serviceName" },
                { "Service.MaxReceivedMessageSize", "100" },
                { "Service.Port", "23304" },
                { "AmlServices.SslCertSecretPath", "secretPath" },
                { "AmlServices.SavedOboTokenPeriod", "00:00:30" },
                { "AmlServices.OboTokenCacheMaxCount", "100" },
                { "AzureStorage.BlobRetriesNumber", "3" },
                { "AzureStorage.BlobRetryInterval", "00:01:00" },
                { "AzureStorage.BlobTimeoutInterval", "2:00:00" },
                { "AzureStorage.BlobContainerName", "blobContainerName" },
                { "AzureStorage.UserRunningJobsBlobContainerName", "userRunningContainerName" },
                { "AzureStorage.MaxBlobsToListForQuotaStat", "5" },
                { "QuotaSettings.SubmittedNodesLimit", "10" },
                { "QuotaSettings.RunningJobExpirationInterval", "02:00:00" },
                { "QuotaSettings.RunningNodesLimit", "10" },
                { "QuotaSettings.EmailsLimit", "5" },
                { "QuotaSettings.UploadsLimit", "5" },
                { "QuotaSettings.PromotionsLimit", "5" },
                { "QuotaSettings.NodesExemption", "node1,node2,node3" },
                { "QuotaSettings.EmailsExemption", "email1,email2,email3" },
                { "QuotaSettings.UploadsExemption", "upload1,upload2,upload3" },
                { "QuotaSettings.PromotionsExemption", "promotion1,promotion2,promotion3" }
            };
            return new DynamicConfigurationFlexMock(settings);
        }
    }
}
