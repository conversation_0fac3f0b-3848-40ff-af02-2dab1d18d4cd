﻿// <copyright file="RunningPipelineRunQuotaManagerTest.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Moq;
using NUnit.Framework;
using Microsoft.RelInfra.Storage;
using Microsoft.Aether.QuotaService.Shared;
using Microsoft.Aether.DataContracts.Entity;

namespace Microsoft.Aether.QuotaService.Test
{
    [TestFixture]
    public class RunningPipelineRunQuotaManagerTest
    {
        private RunningPipelineRunQuotaManager _quotaManager;
        private QuotaServiceConfig _serviceConfig;
        private Mock<IQueryableDbStorage> _limitContainer;
        private Mock<IQueryableDbStorage> _usageContainer;
        private Dictionary<string, Storable<RunningPipelineRunQuotaLimitEntity>> _limitStorage;
        private Dictionary<string, Storable<RunningPipelineRunQuotaUsageEntity>> _usageStorage;

        private WorkspaceIdentity _workspace1;
        private WorkspaceIdentity _workspace2;

        [SetUp]
        public void SetUp()
        {
            _limitContainer = new Mock<IQueryableDbStorage>();
            _limitStorage = new Dictionary<string, Storable<RunningPipelineRunQuotaLimitEntity>>();
            QuotaServiceTestHelper.SetupMockStorage<RunningPipelineRunQuotaLimitEntity>(_limitContainer, _limitStorage);

            _usageContainer = new Mock<IQueryableDbStorage>();
            _usageStorage = new Dictionary<string, Storable<RunningPipelineRunQuotaUsageEntity>>();
            QuotaServiceTestHelper.SetupMockStorage<RunningPipelineRunQuotaUsageEntity>(_usageContainer, _usageStorage);

            _serviceConfig = new QuotaServiceConfig(QuotaServiceConfigMock.GetConfiguration(), secretProvider: null);
            _quotaManager = new RunningPipelineRunQuotaManager(_serviceConfig, _limitContainer.Object, _usageContainer.Object);

            _workspace1 = new WorkspaceIdentity
            {
                SubscriptionId = Guid.NewGuid().ToString(),
                ResourceGroupName = "testFakeGroup1",
                WorkspaceName = "testFakeWorkspace1",
                WorkspaceId = Guid.NewGuid().ToString(),
            };

            _workspace2 = new WorkspaceIdentity
            {
                SubscriptionId = Guid.NewGuid().ToString(),
                ResourceGroupName = "testFakeGroup2",
                WorkspaceName = "testFakeWorkspace2",
                WorkspaceId = Guid.NewGuid().ToString(),
            };
        }

        [Test]
        public async Task TestAquireAndReleaseQuotaAsync()
        {
            var pipelineRun1 = QuotaServiceTestHelper.GenerateRunningPipelineRunQuotaUsageEntity(_workspace1, Guid.NewGuid().ToString());
            var pipelineRun2 = QuotaServiceTestHelper.GenerateRunningPipelineRunQuotaUsageEntity(_workspace1, Guid.NewGuid().ToString());

            var quotaResponse = await _quotaManager.GetCurrentQuotaAsync(_workspace1);
            Assert.AreEqual(_quotaManager.GetDefaultQuotaLimit(_workspace1).MaxLimit, quotaResponse.MaxLimit);
            Assert.AreEqual(0, quotaResponse.CurrentUsageCount);

            quotaResponse = await _quotaManager.AcquireQuotaAsync(_workspace1, pipelineRun1);
            Assert.IsTrue(quotaResponse.IsQuotaAvailable);
            Assert.AreEqual(1, quotaResponse.CurrentUsageCount);

            quotaResponse = await _quotaManager.AcquireQuotaAsync(_workspace1, pipelineRun2);
            Assert.IsTrue(quotaResponse.IsQuotaAvailable);
            Assert.AreEqual(2, quotaResponse.CurrentUsageCount);

            bool released = await _quotaManager.ReleaseQuotaAsync(_workspace1, pipelineRun2);
            Assert.IsTrue(released);
            quotaResponse = await _quotaManager.GetCurrentQuotaAsync(_workspace1);
            Assert.AreEqual(1, quotaResponse.CurrentUsageCount);

            released = await _quotaManager.ReleaseQuotaAsync(_workspace1, pipelineRun1);
            Assert.IsTrue(released);
            quotaResponse = await _quotaManager.GetCurrentQuotaAsync(_workspace1);
            Assert.AreEqual(0, quotaResponse.CurrentUsageCount);
        }

        [Test]
        public async Task TestAquireQuotaExceedLimitAsync()
        {
            var pipelineRun1 = QuotaServiceTestHelper.GenerateRunningPipelineRunQuotaUsageEntity(_workspace1, Guid.NewGuid().ToString());
            var pipelineRun2 = QuotaServiceTestHelper.GenerateRunningPipelineRunQuotaUsageEntity(_workspace1, Guid.NewGuid().ToString());

            var quotaLimit = await _quotaManager.SetQuotaLimitAsync(_workspace1, maxLimit: 1);
            Assert.AreEqual(1, quotaLimit.MaxLimit);
            var quotaResponse = await _quotaManager.GetCurrentQuotaAsync(_workspace1);
            Assert.AreEqual(0, quotaResponse.CurrentUsageCount);

            quotaResponse = await _quotaManager.AcquireQuotaAsync(_workspace1, pipelineRun1);
            Assert.IsTrue(quotaResponse.IsQuotaAvailable);
            Assert.AreEqual(1, quotaResponse.CurrentUsageCount);

            quotaResponse = await _quotaManager.AcquireQuotaAsync(_workspace1, pipelineRun2);
            Assert.IsFalse(quotaResponse.IsQuotaAvailable);
            quotaResponse = await _quotaManager.GetCurrentQuotaAsync(_workspace1);
            Assert.AreEqual(1, quotaResponse.CurrentUsageCount);
        }

        [Test]
        public async Task TestMultipleAcquireOrReleaseInRetryAsync()
        {
            var pipelineRun1 = QuotaServiceTestHelper.GenerateRunningPipelineRunQuotaUsageEntity(_workspace1, Guid.NewGuid().ToString());
            // Mock multiple acquire retries in failure.
            for (int i = 0; i < 3; i++)
            {
                await _quotaManager.AcquireQuotaAsync(_workspace1, pipelineRun1);
            }

            // Only one quota is reserved for same pipelinerun.
            var currentQuotaEntity = await _quotaManager.GetCurrentQuotaAsync(_workspace1);
            Assert.AreEqual(1, currentQuotaEntity.CurrentUsageCount);

            var pipelineRun2 = QuotaServiceTestHelper.GenerateRunningPipelineRunQuotaUsageEntity(_workspace1, Guid.NewGuid().ToString());
            await _quotaManager.AcquireQuotaAsync(_workspace1, pipelineRun2);
            currentQuotaEntity = await _quotaManager.GetCurrentQuotaAsync(_workspace1);
            Assert.AreEqual(2, currentQuotaEntity.CurrentUsageCount);

            // Mock multiple release retries in failure.
            for (var i = 0; i < 3; i++)
            {
                await _quotaManager.ReleaseQuotaAsync(_workspace1, pipelineRun1);
            }

            // Only one quota is released for same pipelinerun.
            currentQuotaEntity = await _quotaManager.GetCurrentQuotaAsync(_workspace1);
            Assert.AreEqual(1, currentQuotaEntity.CurrentUsageCount);
        }

        [Test]
        public async Task TestReleaseQuotaUsageNoLessThanZeroAsync()
        {
            var pipelineRun1 = QuotaServiceTestHelper.GenerateRunningPipelineRunQuotaUsageEntity(_workspace1, Guid.NewGuid().ToString());

            var quotaResponse = await _quotaManager.AcquireQuotaAsync(_workspace1, pipelineRun1);
            Assert.IsTrue(quotaResponse.IsQuotaAvailable);
            Assert.AreEqual(1, quotaResponse.CurrentUsageCount);

            bool released = await _quotaManager.ReleaseQuotaAsync(_workspace1, pipelineRun1);
            Assert.IsTrue(released);
            var quota = await _quotaManager.GetCurrentQuotaAsync(_workspace1);
            Assert.AreEqual(0, quota.CurrentUsageCount);

            // Release a quota again.
            released = await _quotaManager.ReleaseQuotaAsync(_workspace1, pipelineRun1);
            Assert.IsFalse(released);
            quota = await _quotaManager.GetCurrentQuotaAsync(_workspace1);
            Assert.AreEqual(0, quota.CurrentUsageCount);
        }

        [Test]
        public async Task TestSetQuotaLimitAsync()
        {
            var quotaResponse = await _quotaManager.GetCurrentQuotaAsync(_workspace1);
            Assert.AreEqual(_quotaManager.GetDefaultQuotaLimit(_workspace1).MaxLimit, quotaResponse.MaxLimit);

            int newLimit = 10;
            var quotaLimit = await _quotaManager.SetQuotaLimitAsync(_workspace1, newLimit);
            Assert.AreEqual(newLimit, quotaLimit.MaxLimit);

            newLimit = -1;
            Assert.ThrowsAsync<ArgumentException>(async () => await _quotaManager.SetQuotaLimitAsync(_workspace1, newLimit));
        }

        [Test]
        public async Task TestGetAllQuotaLimitsAsync()
        {
            var defaultQuotaLimit = _quotaManager.GetDefaultQuotaLimit(_workspace1);
            // Configure new quota limit to save it in DB.
            var updatedQuotaLimit1 = await _quotaManager.SetQuotaLimitAsync(_workspace1, defaultQuotaLimit.MaxLimit);
            Assert.AreEqual(defaultQuotaLimit.MaxLimit, updatedQuotaLimit1.MaxLimit);
            var updatedQuotaLimit2 = await _quotaManager.SetQuotaLimitAsync(_workspace2, 2 * defaultQuotaLimit.MaxLimit);
            Assert.AreEqual(2 * defaultQuotaLimit.MaxLimit, updatedQuotaLimit2.MaxLimit);

            var allQuotas = await _quotaManager.GetAllQuotaLimitsAsync();
            Assert.AreEqual(2, allQuotas.Count());
        }

        [Test]
        public async Task TestGetAllUsagesAsync()
        {
            var pipelineRun1 = QuotaServiceTestHelper.GenerateRunningPipelineRunQuotaUsageEntity(_workspace1, Guid.NewGuid().ToString());
            var pipelineRun2 = QuotaServiceTestHelper.GenerateRunningPipelineRunQuotaUsageEntity(_workspace1, Guid.NewGuid().ToString());
            var pipelineRun3 = QuotaServiceTestHelper.GenerateRunningPipelineRunQuotaUsageEntity(_workspace2, Guid.NewGuid().ToString());

            var quotaResponse = await _quotaManager.AcquireQuotaAsync(_workspace1, pipelineRun1);
            Assert.IsTrue(quotaResponse.IsQuotaAvailable);
            quotaResponse = await _quotaManager.AcquireQuotaAsync(_workspace1, pipelineRun2);
            Assert.IsTrue(quotaResponse.IsQuotaAvailable);
            quotaResponse = await _quotaManager.AcquireQuotaAsync(_workspace2, pipelineRun3);
            Assert.IsTrue(quotaResponse.IsQuotaAvailable);

            var allUsages = await _quotaManager.GetAllUsagesAsync();
            Assert.AreEqual(3, allUsages.Count());
            var workspace1Usages = allUsages.Where(r => r.Workspace.WorkspaceId == _workspace1.WorkspaceId);
            Assert.AreEqual(2, workspace1Usages?.Count());
            var workspace2Usages = allUsages.Where(r => r.Workspace.WorkspaceId == _workspace2.WorkspaceId);
            Assert.AreEqual(1, workspace2Usages?.Count());

            var workspaceUsages = await _quotaManager.GetAllUsagesAsync(_workspace1);
            Assert.AreEqual(2, workspaceUsages?.Count());

            bool released = await _quotaManager.ReleaseQuotaAsync(_workspace1, pipelineRun1);
            Assert.IsTrue(released);
            released = await _quotaManager.ReleaseQuotaAsync(_workspace1, pipelineRun2);
            Assert.IsTrue(released);
            released = await _quotaManager.ReleaseQuotaAsync(_workspace2, pipelineRun3);
            Assert.IsTrue(released);

            allUsages = await _quotaManager.GetAllUsagesAsync();
            Assert.AreEqual(0, allUsages.Count());

            workspaceUsages = await _quotaManager.GetAllUsagesAsync(_workspace2);
            Assert.AreEqual(0, workspaceUsages?.Count());
        }

        [Test]
        public async Task TestRemoveQuotaLimitAsync()
        {
            var quotaResponse = await _quotaManager.GetCurrentQuotaAsync(_workspace1);
            Assert.AreEqual(_quotaManager.GetDefaultQuotaLimit(_workspace1).MaxLimit, quotaResponse.MaxLimit);
            Assert.AreEqual(0, quotaResponse.CurrentUsageCount);

            // Configure new quota limit to save it in DB.
            var updatedQuota = await _quotaManager.SetQuotaLimitAsync(_workspace1, 2 * quotaResponse.MaxLimit);

            var allQuotas = await _quotaManager.GetAllQuotaLimitsAsync();
            Assert.AreEqual(1, allQuotas?.Count());

            // Remove quota limit
            var deleted = await _quotaManager.RemoveQuotaLimitAsync(_workspace1);
            Assert.IsTrue(deleted);
            allQuotas = await _quotaManager.GetAllQuotaLimitsAsync();
            Assert.AreEqual(0, allQuotas?.Count());

            // Remove quota limit twice, method should return false
            deleted = await _quotaManager.RemoveQuotaLimitAsync(_workspace1);
            Assert.IsFalse(deleted);
        }

        [Test]
        public async Task TestRemoveUsageAsync()
        {
            var pipelineRun1 = QuotaServiceTestHelper.GenerateRunningPipelineRunQuotaUsageEntity(_workspace1, Guid.NewGuid().ToString());

            var quotaResponse = await _quotaManager.AcquireQuotaAsync(_workspace1, pipelineRun1);
            Assert.AreEqual(1, quotaResponse.CurrentUsageCount);

            var allUsages = await _quotaManager.GetAllUsagesAsync(_workspace1);
            Assert.AreEqual(1, allUsages?.Count());

            // Remove usages
            var usage = allUsages.First();
            var deleted = await _quotaManager.RemoveUsageAsync(_workspace1, usage);
            Assert.IsTrue(deleted);
            allUsages = await _quotaManager.GetAllUsagesAsync(_workspace1);
            Assert.AreEqual(0, allUsages?.Count());

            // Remove usages twice, method should return false
            deleted = await _quotaManager.RemoveUsageAsync(_workspace1, usage);
            Assert.IsFalse(deleted);
        }
    }
}
