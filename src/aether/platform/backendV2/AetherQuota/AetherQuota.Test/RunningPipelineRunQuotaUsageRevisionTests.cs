﻿// <copyright file="RunningPipelineRunQuotaUsageRevisionTests.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Microsoft.Aether.DataContracts;
using Microsoft.Aether.DataContracts.Entity;
using Microsoft.Aether.QuotaService.Shared;
using Microsoft.MachineLearning.Common.ServiceBus;
using Microsoft.RelInfra.Extensions;
using Microsoft.RelInfra.Instrumentation;
using Microsoft.RelInfra.Storage;
using Moq;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading.Tasks;

namespace Microsoft.Aether.QuotaService.Test
{
    public class RunningPipelineRunQuotaUsageRevisionTests
    {
        private readonly MockRepository _repository;

        private RunningPipelineRunQuotaManager _fakeRunningPipelineRunQuotaManager;
        private RunningPipelineRunSoftQuotaManager _fakeRunningPipelineRunSoftQuotaManager;
        private readonly CounterManager _fakeCounterManager;
        private Mock<IQueryableDbStorage> _fakeLimitContainer;
        private Mock<IQueryableDbStorage> _fakeUsageContainer;
        private Dictionary<string, Storable<RunningPipelineRunQuotaLimitEntity>> _fakeLimitStorage;
        private Dictionary<string, Storable<RunningPipelineRunQuotaUsageEntity>> _fakeUsageStorage;
        private QuotaServiceConfig _fakeQuotaServiceConfig;
        private RunningPipelineRunQuotaUsageRevision _fakeRunningPipelineRunQuotaUsageRevision;
        private Mock<IExperimentStore> _fakeExperimentStore;
        private Mock<IAzureBlobStorage> _fakeStorage;
        private string _fakeContainerName;

        private WorkspaceIdentity _workspace;

        public RunningPipelineRunQuotaUsageRevisionTests()
        {
            _repository = new MockRepository(MockBehavior.Loose) { DefaultValue = DefaultValue.Mock, CallBase = true };
            _fakeCounterManager = _repository.Create<CounterManager>(string.Empty, _repository.Create<ICounterFactory>().Object).Object;
        }

        [SetUp]
        public void Init()
        {
            QuotaServiceTestHelper.InitCounter(_repository, _fakeCounterManager);

            _fakeLimitContainer = new Mock<IQueryableDbStorage>();
            _fakeLimitStorage = new Dictionary<string, Storable<RunningPipelineRunQuotaLimitEntity>>();
            QuotaServiceTestHelper.SetupMockStorage<RunningPipelineRunQuotaLimitEntity>(_fakeLimitContainer, _fakeLimitStorage);

            _fakeUsageContainer = new Mock<IQueryableDbStorage>();
            _fakeUsageStorage = new Dictionary<string, Storable<RunningPipelineRunQuotaUsageEntity>>();
            QuotaServiceTestHelper.SetupMockStorage<RunningPipelineRunQuotaUsageEntity>(_fakeUsageContainer, _fakeUsageStorage);

            _fakeQuotaServiceConfig = new QuotaServiceConfig(QuotaServiceConfigMock.GetConfiguration(), secretProvider: null);
            _fakeRunningPipelineRunQuotaManager = new RunningPipelineRunQuotaManager(_fakeQuotaServiceConfig, _fakeLimitContainer.Object, _fakeUsageContainer.Object);
            var cachedManager = _repository.Create<ICachedPipelineRunManager>();
            var topicPubliser = _repository.Create<IServiceBusTopicPublisher>();
            _fakeRunningPipelineRunSoftQuotaManager = new RunningPipelineRunSoftQuotaManager(
                _fakeQuotaServiceConfig,
                _fakeLimitContainer.Object,
                _fakeUsageContainer.Object,
                cachedManager.Object,
                topicPubliser.Object);

            _fakeExperimentStore = new Mock<IExperimentStore>();
            _fakeStorage = new Mock<IAzureBlobStorage>();
            _fakeContainerName = "fakelock";
            _fakeRunningPipelineRunQuotaUsageRevision = new RunningPipelineRunQuotaUsageRevision(
                _fakeQuotaServiceConfig,
                _fakeCounterManager,
                _fakeRunningPipelineRunQuotaManager,
                _fakeRunningPipelineRunSoftQuotaManager,
                _fakeExperimentStore.Object,
                _fakeStorage.Object,
                _fakeContainerName);

            _workspace = new WorkspaceIdentity()
            {
                SubscriptionId = "subscription1",
                ResourceGroupName = "rgname1",
                WorkspaceId = "wsid1",
                WorkspaceName = "wsname1"
            };

            // Mock metastore to return completed or running status when bulk get experiments.
            _fakeExperimentStore.Setup(store => store.BulkGetAsync(It.IsAny<string>(), It.IsAny<string[]>(), It.IsAny<WorkspaceIdentity>()))
                .Returns((string workspaceId, string[] entityIds, WorkspaceIdentity workspaceIdentity) =>
                {
                    var completedEntityIds = entityIds.Where(id => id.StartsWith("Completed", StringComparison.CurrentCulture));
                    var runningEntityIds = entityIds.Where(id => id.StartsWith("Running", StringComparison.CurrentCulture));
                    IEnumerable<ExperimentEntity> experiments = new List<ExperimentEntity>();
                    if (completedEntityIds.Any())
                    {
                        var completedExperiments = entityIds.Select(id => new ExperimentEntity
                        {
                            Id = id,
                            Status = new ExperimentStatus { StatusCode = ExperimentStatusCode.Finished },
                        });

                        experiments = experiments.Concat(completedExperiments);
                    }

                    if (runningEntityIds.Any())
                    {
                        var runningExperiments = entityIds.Select(id => new ExperimentEntity
                        {
                            Id = id,
                            Status = new ExperimentStatus { StatusCode = ExperimentStatusCode.Running },
                        });

                        experiments = experiments.Concat(runningExperiments);
                    }

                    return Task.FromResult(experiments);
                });
        }

        [Test]
        public async Task TestRemoveCompletedPipelineRunsAsync()
        {
            // Set old last updated timestamp to check metastore which returns completed status.
            DateTime checkMetaStoreLastUpdatedTimestamp = DateTime.UtcNow.AddMonths(-1);

            string experimentId1 = "Completed" + Guid.NewGuid().ToString();
            var pipelineRun1 = QuotaServiceTestHelper.GenerateRunningPipelineRunQuotaUsageEntity(_workspace, experimentId1, lastUpdatedTimeUtc: checkMetaStoreLastUpdatedTimestamp);
            var quotaResponse = await _fakeRunningPipelineRunQuotaManager.AcquireQuotaAsync(_workspace, pipelineRun1);
            Assert.AreEqual(1, quotaResponse.CurrentUsageCount);

            string experimentId2 = "Completed" + Guid.NewGuid().ToString();
            var pipelineRun2 = QuotaServiceTestHelper.GenerateRunningPipelineRunQuotaUsageEntity(_workspace, experimentId2, lastUpdatedTimeUtc: checkMetaStoreLastUpdatedTimestamp);
            quotaResponse = await _fakeRunningPipelineRunQuotaManager.AcquireQuotaAsync(_workspace, pipelineRun2);
            Assert.AreEqual(2, quotaResponse.CurrentUsageCount);

            // Trigger revision task. Completed PipelineRuns are removed and quota count fixed.
            await _fakeRunningPipelineRunQuotaUsageRevision.ReviseAllQuotaUsagesInternalAsync();
            quotaResponse = await _fakeRunningPipelineRunQuotaManager.GetCurrentQuotaAsync(_workspace);
            Assert.AreEqual(0, quotaResponse.CurrentUsageCount);
        }

        [Test]
        public async Task TestSkipRemoveCompletedPipelineRunsAsync()
        {
            // Set last updated timestamp to now to skip checking metastore.
            DateTime checkMetaStoreLastUpdatedTimestamp = DateTime.UtcNow;

            string experimentId1 = "Completed" + Guid.NewGuid().ToString();
            var pipelineRun1 = QuotaServiceTestHelper.GenerateRunningPipelineRunQuotaUsageEntity(_workspace, experimentId1, lastUpdatedTimeUtc: checkMetaStoreLastUpdatedTimestamp);
            var quotaResponse = await _fakeRunningPipelineRunQuotaManager.AcquireQuotaAsync(_workspace, pipelineRun1);
            Assert.AreEqual(1, quotaResponse.CurrentUsageCount);

            string experimentId2 = "Completed" + Guid.NewGuid().ToString();
            var pipelineRun2 = QuotaServiceTestHelper.GenerateRunningPipelineRunQuotaUsageEntity(_workspace, experimentId2, lastUpdatedTimeUtc: checkMetaStoreLastUpdatedTimestamp);
            quotaResponse = await _fakeRunningPipelineRunQuotaManager.AcquireQuotaAsync(_workspace, pipelineRun2);
            Assert.AreEqual(2, quotaResponse.CurrentUsageCount);

            // Trigger revision task. PipelineRuns are not removed since not checking metastore even it's completed.
            await _fakeRunningPipelineRunQuotaUsageRevision.ReviseAllQuotaUsagesInternalAsync();
            quotaResponse = await _fakeRunningPipelineRunQuotaManager.GetCurrentQuotaAsync(_workspace);
            Assert.AreEqual(2, quotaResponse.CurrentUsageCount);
        }

        [Test]
        public async Task TestUpdateRunningPipelineRunsAsync()
        {
            // Set old last updated timestamp to check metastore which returns running status.
            DateTime checkMetaStoreLastUpdatedTimestamp = DateTime.UtcNow.AddMonths(-1);

            string experimentId1 = "Running" + Guid.NewGuid().ToString();
            var pipelineRun1 = QuotaServiceTestHelper.GenerateRunningPipelineRunQuotaUsageEntity(_workspace, experimentId1, lastUpdatedTimeUtc: checkMetaStoreLastUpdatedTimestamp);
            var quotaResponse = await _fakeRunningPipelineRunQuotaManager.AcquireQuotaAsync(_workspace, pipelineRun1);
            Assert.AreEqual(1, quotaResponse.CurrentUsageCount);

            string experimentId2 = "Running" + Guid.NewGuid().ToString();
            var pipelineRun2 = QuotaServiceTestHelper.GenerateRunningPipelineRunQuotaUsageEntity(_workspace, experimentId2, lastUpdatedTimeUtc: checkMetaStoreLastUpdatedTimestamp);
            quotaResponse = await _fakeRunningPipelineRunQuotaManager.AcquireQuotaAsync(_workspace, pipelineRun2);
            Assert.AreEqual(2, quotaResponse.CurrentUsageCount);

            // Trigger revision task. The LastUpdatedTimeUtc field of every Running PipelineRun is updated.
            await _fakeRunningPipelineRunQuotaUsageRevision.ReviseAllQuotaUsagesInternalAsync();
            quotaResponse = await _fakeRunningPipelineRunQuotaManager.GetCurrentQuotaAsync(_workspace);
            Assert.AreEqual(2, quotaResponse.CurrentUsageCount);

            var quotaUsages = await _fakeRunningPipelineRunQuotaManager.GetAllUsagesAsync(_workspace);
            foreach (var quotaUsage in quotaUsages)
            {
                Assert.IsTrue(quotaUsage.LastUpdatedTimeUtc > DateTime.UtcNow.AddHours(-1));
            }
        }

        [Test]
        public async Task TestWorkspaceQuotaUsageMetricsAsync()
        {
            var rootPipelineRunHardQuota = QuotaServiceTestHelper.GenerateRunningPipelineRunQuotaUsageEntity(_workspace, experimentId: Guid.NewGuid().ToString());
            var subGraphPipelineRunHardQuota = QuotaServiceTestHelper.GenerateRunningPipelineRunQuotaUsageEntity(_workspace, experimentId: Guid.NewGuid().ToString(), parentExperimentId: Guid.NewGuid().ToString());
            await _fakeRunningPipelineRunQuotaManager.AcquireQuotaAsync(_workspace, rootPipelineRunHardQuota);
            await _fakeRunningPipelineRunQuotaManager.AcquireQuotaAsync(_workspace, subGraphPipelineRunHardQuota);

            var rootPipelineRunSoftQuota = QuotaServiceTestHelper.GenerateRunningPipelineRunQuotaUsageEntity(_workspace, experimentId: Guid.NewGuid().ToString(), isSoftQuota: true);
            var subGraphPipelineRunSoftQuota = QuotaServiceTestHelper.GenerateRunningPipelineRunQuotaUsageEntity(_workspace, experimentId: Guid.NewGuid().ToString(), isSoftQuota: true, parentExperimentId: Guid.NewGuid().ToString());
            await _fakeRunningPipelineRunSoftQuotaManager.AcquireQuotaAsync(_workspace, rootPipelineRunSoftQuota);
            await _fakeRunningPipelineRunSoftQuotaManager.AcquireQuotaAsync(_workspace, subGraphPipelineRunSoftQuota);

            await _fakeRunningPipelineRunQuotaUsageRevision.ReviseAllQuotaUsagesInternalAsync();
            var workspacesUsageStats = _fakeRunningPipelineRunQuotaUsageRevision.GetWorkspacesQuotaUsageStats();
            Assert.AreEqual(1, workspacesUsageStats.Count());
            var usageStats = workspacesUsageStats.First();
            Assert.AreEqual(4, usageStats.QuotaUsageCount);
            Assert.AreEqual(2, usageStats.SoftQuotaUsageCount);
            Assert.AreEqual(2, usageStats.RootPipelineRunQuotaUsageCount);
            Assert.AreEqual(1, usageStats.RootPipelineRunSoftQuotaUsageCount);
            Assert.AreEqual(2, usageStats.SubGraphPipelineRunQuotaUsageCount);
            Assert.AreEqual(1, usageStats.SubGraphPipelineRunSoftQuotaUsageCount);
        }

        [Test]
        public async Task TestCheckMetaStoreStatusWithWorkspaceDeleteAsync()
        {
            DateTime checkMetaStoreLastUpdatedTimestamp = DateTime.UtcNow.AddMonths(-1);
            string experimentId1 = "Running" + Guid.NewGuid().ToString();
            var pipelineRun1 = QuotaServiceTestHelper.GenerateRunningPipelineRunQuotaUsageEntity(_workspace, experimentId1, lastUpdatedTimeUtc: checkMetaStoreLastUpdatedTimestamp);
            var quotaResponse = await _fakeRunningPipelineRunQuotaManager.AcquireQuotaAsync(_workspace, pipelineRun1);
            Assert.AreEqual(1, quotaResponse.CurrentUsageCount);

            // Mock metastore to throw not found exception when workspace is deleted.
            _fakeExperimentStore.Setup(store => store.BulkGetAsync(It.IsAny<string>(), It.IsAny<string[]>(), It.IsAny<WorkspaceIdentity>()))
                .Throws(new HttpRequestDetailException("Could not find workspace", HttpStatusCode.NotFound));

            // Quota usage is removed if workspace is deleted.
            await _fakeRunningPipelineRunQuotaUsageRevision.ReviseAllQuotaUsagesInternalAsync();
            quotaResponse = await _fakeRunningPipelineRunQuotaManager.GetCurrentQuotaAsync(_workspace);
            Assert.AreEqual(0, quotaResponse.CurrentUsageCount);

            // Mock metastore to throw conflict exception when workspace is in deleting state.
            _fakeExperimentStore.Setup(store => store.BulkGetAsync(It.IsAny<string>(), It.IsAny<string[]>(), It.IsAny<WorkspaceIdentity>()))
                .Throws(new HttpRequestDetailException("Request can't be accepted while workspace is in deleting state", HttpStatusCode.Conflict));

            quotaResponse = await _fakeRunningPipelineRunQuotaManager.AcquireQuotaAsync(_workspace, pipelineRun1);
            Assert.AreEqual(1, quotaResponse.CurrentUsageCount);

            // Quota usage is removed if workspace is in deleting state.
            await _fakeRunningPipelineRunQuotaUsageRevision.ReviseAllQuotaUsagesInternalAsync();
            quotaResponse = await _fakeRunningPipelineRunQuotaManager.GetCurrentQuotaAsync(_workspace);
            Assert.AreEqual(0, quotaResponse.CurrentUsageCount);
        }
    }
}
