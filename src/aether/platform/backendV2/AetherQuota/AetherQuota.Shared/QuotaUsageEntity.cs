﻿// <copyright file="QuotaUsageEntity.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Runtime.Serialization;
using Microsoft.Aether.DataContracts.Entity;
using Newtonsoft.Json;

namespace Microsoft.Aether.QuotaService.Shared
{
    [DataContract]
    public class QuotaUsageEntity
    {
        /// <summary>
        /// The resource id which uses a quota.
        /// </summary>
        [DataMember]
        public string Id { get; set; }

        /// <summary>
        /// The workspace which the resource belongs to.
        /// </summary>
        [DataMember]
        public WorkspaceIdentity Workspace { get; set; }

        /// <summary>
        /// The timestamp when the resource acquires a quota.
        /// </summary>
        [DataMember]
        public DateTime CreatedTimeUtc { get; set; }

        /// <summary>
        /// The timestamp when the resource is last updated.
        /// </summary>
        [DataMember]
        public DateTime LastUpdatedTimeUtc { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// The quota usage count of the resource. Normally, it's one.
        /// </summary>
        [DataMember]
        public int UsageCount { get; set; }

        /// <summary>
        /// Whether the resource usage only occupys a soft quota.
        /// It's null if a quota type doesn't support both hard and soft quota limit.
        /// If it's set to true, the usage is counted in the <see cref="QuotaLimitEntity.MaxSoftLimit"/>.
        /// Otherwise, it's is only counted in the <see cref="QuotaLimitEntity.MaxLimit"/>.
        /// </summary>
        [DataMember]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public bool? IsSoftQuota { get; set; }
    }
}
