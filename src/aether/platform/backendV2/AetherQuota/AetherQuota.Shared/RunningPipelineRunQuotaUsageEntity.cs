﻿// <copyright file="RunningPipelineRunQuotaUsageEntity.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Runtime.Serialization;

namespace Microsoft.Aether.QuotaService.Shared
{
    [DataContract]
    public class RunningPipelineRunQuotaUsageEntity : QuotaUsageEntity
    {
        /// <summary>
        /// The parent experiment id of current pipeline run.
        /// It's null if current pipeline run is a root pipeline run.
        /// </summary>
        [DataMember]
        public string ParentExperimentId { get; set; }
    }
}
