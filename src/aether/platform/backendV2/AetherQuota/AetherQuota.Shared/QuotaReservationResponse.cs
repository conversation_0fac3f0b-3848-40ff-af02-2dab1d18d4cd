﻿// <copyright file="QuotaReservationResponse.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Collections.Generic;
using System.Runtime.Serialization;

namespace Microsoft.Aether.QuotaService.Shared
{
    [DataContract]
    public class QuotaReservationResponse
    {
        [DataMember]
        public string TeamId { get; set; }

        [DataMember]
        public string UserId { get; set; }

        [DataMember]
        public string ResourceId { get; set; }

        [DataMember]
        public Dictionary<string, int> TeamResourceUsageStat { get; set; }

        [DataMember]
        public Dictionary<string, int> UserResourceUsageStat { get; set; }

        [DataMember]
        public bool IsQuotaAvailable { get; set; }

        [DataMember]
        public int? QuotaLimit { get; set; }

        [DataMember]
        public int? AvailableResource { get; set; }
    }
}