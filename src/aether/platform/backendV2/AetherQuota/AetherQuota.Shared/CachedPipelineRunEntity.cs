﻿// <copyright file="CachedPipelineRunEntity.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Runtime.Serialization;
using Microsoft.Aether.DataContracts.Entity;
using Newtonsoft.Json;

namespace Microsoft.Aether.QuotaService.Shared
{
    [DataContract]
    public class CachedPipelineRunEntity
    {
        /// <summary>
        /// The pipeline run id which is cached.
        /// </summary>
        [DataMember]
        public string Id { get; set; }

        /// <summary>
        /// The workspace which the pipeline run belongs to.
        /// </summary>
        [DataMember]
        public WorkspaceIdentity Workspace { get; set; }

        /// <summary>
        /// The timestamp when the pipeline run is cached.
        /// </summary>
        [DataMember]
        public DateTime CachedTimeUtc { get; set; }

        /// <summary>
        /// The serialized queue message (ExperimentQueueEntity) of the pipeline run which can be put into NodeProcessor SubmissionQueue.
        /// </summary>
        [DataMember]
        public string QueueMessage { get; set; }
    }
}
