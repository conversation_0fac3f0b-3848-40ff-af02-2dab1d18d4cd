﻿// <copyright file="ResourceUsageEntity.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Runtime.Serialization;

namespace Microsoft.Aether.QuotaService.Shared
{
    [DataContract]
    public class ResourceUsageEntity
    {
        [DataMember]
        public string UserId { get; set; }

        [DataMember]
        public string TeamId { get; set; }

        [DataMember]
        public string Id { get; set; }

        [DataMember]
        public DateTime SubmissionTime { get; set; }

        [DataMember]
        public int ResourceCount { get; set; }
    }
}