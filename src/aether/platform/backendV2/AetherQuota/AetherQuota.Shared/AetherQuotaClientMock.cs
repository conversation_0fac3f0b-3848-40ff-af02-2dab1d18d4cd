// <copyright file="AetherQuotaClientMock.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Collections.Generic;
using System.Threading.Tasks;

namespace Microsoft.Aether.QuotaService.Shared
{
    public class AetherQuotaClientMock : IAetherQuotaClient
    {
        public bool FailQuotaRequest { get; set; }

        public Task<QuotaReservationResponse> ReserveJobsQuotaAsync(string teamId, string userName, string experimentId, int jobsCount)
        {
            return Task.FromResult(new QuotaReservationResponse { IsQuotaAvailable = !FailQuotaRequest });
        }

        public Task<ResourceUsageEntity> GetRunningJobsResourceUsageAsync(string teamId, string userName, string experimentId)
        {
            return Task.FromResult<ResourceUsageEntity>(null);
        }

        public Task<QuotaReservationResponse> IncrementRunningJobsQuotaAsync(string teamId, string userName, string experimentId, int incrementJobsCount)
        {
            return Task.FromResult(new QuotaReservationResponse { IsQuotaAvailable = !FailQuotaRequest });
        }

        public Task<QuotaReservationResponse> ReserveRunningJobsQuotaAsync(string teamId, string userName, string experimentId, int absoluteJobsCount)
        {
            return Task.FromResult(new QuotaReservationResponse { IsQuotaAvailable = !FailQuotaRequest });
        }

        public Task ReleaseRunningJobsQuotaAsync(string teamId, string userName, string experimentId)
        {
            return Task.FromResult(new QuotaReservationResponse { IsQuotaAvailable = !FailQuotaRequest });
        }

        public Task<QuotaReservationResponse> ReserveEmailQuotaAsync(string teamId, string userName, string emailId)
        {
            return Task.FromResult(new QuotaReservationResponse { IsQuotaAvailable = !FailQuotaRequest });
        }

        public Task<QuotaReservationResponse> ReservePromotionQuotaAsync(string teamId, string userName, string promotionTaskId)
        {
            return Task.FromResult(new QuotaReservationResponse { IsQuotaAvailable = !FailQuotaRequest });
        }

        public Task<QuotaReservationResponse> ReserveUploadQuotaAsync(string teamId, string userName, string uploadTaskId)
        {
            return Task.FromResult(new QuotaReservationResponse { IsQuotaAvailable = !FailQuotaRequest });
        }

        public Task<List<AggregatedResourcesUsageEntity>> GetExperimentsUsageAsync()
        {
            throw new System.NotImplementedException();
        }

        public Task<List<AggregatedResourcesUsageEntity>> GetEmailsUsageAsync()
        {
            throw new System.NotImplementedException();
        }

        public Task<List<AggregatedResourcesUsageEntity>> GetPromotionsUsageAsync()
        {
            throw new System.NotImplementedException();
        }

        public Task<List<AggregatedResourcesUsageEntity>> GetUploadsUsageAsync()
        {
            throw new System.NotImplementedException();
        }
    }
}