﻿// <copyright file="IAetherQuotaClient.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Collections.Generic;
using System.Threading.Tasks;

namespace Microsoft.Aether.QuotaService.Shared
{
    public interface IAetherQuotaClient
    {
        Task<QuotaReservationResponse> ReserveJobsQuotaAsync(string teamId, string userName, string experimentId, int jobsCount);

        Task<ResourceUsageEntity> GetRunningJobsResourceUsageAsync(string teamId, string userName, string experimentId);

        Task<QuotaReservationResponse> IncrementRunningJobsQuotaAsync(string teamId, string userName, string experimentId, int incrementJobsCount);

        Task<QuotaReservationResponse> ReserveRunningJobsQuotaAsync(string teamId, string userName, string experimentId, int absoluteJobsCount);

        Task ReleaseRunningJobsQuotaAsync(string teamId, string userName, string experimentId);

        Task<QuotaReservationResponse> ReserveEmailQuotaAsync(string teamId, string userName, string emailId);

        Task<QuotaReservationResponse> ReservePromotionQuotaAsync(string teamId, string userName, string promotionTaskId);

        Task<QuotaReservationResponse> ReserveUploadQuotaAsync(string teamId, string userName, string uploadTaskId);

        Task<List<AggregatedResourcesUsageEntity>> GetExperimentsUsageAsync();

        Task<List<AggregatedResourcesUsageEntity>> GetEmailsUsageAsync();

        Task<List<AggregatedResourcesUsageEntity>> GetPromotionsUsageAsync();

        Task<List<AggregatedResourcesUsageEntity>> GetUploadsUsageAsync();
    }
}