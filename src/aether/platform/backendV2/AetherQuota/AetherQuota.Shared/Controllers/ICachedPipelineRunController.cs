﻿// <copyright file="ICachedPipelineRunController.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Microsoft.MachineLearning.Common.WebApi.Client;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Microsoft.Aether.QuotaService.Shared.Controllers
{
    [InterfaceRoutePrefix("pipelinequotaservice/v1.0/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.MachineLearningServices/workspaces/{workspaceName}/CachedPipelineRun")]
    public interface ICachedPipelineRunController
    {
        [HttpPost("{experimentId}")]
        Task<bool> AddCachedPipelineRunAsync(string subscriptionId, string resourceGroupName, string workspaceName, string experimentId, [FromBody] string queueMessage);

        [HttpDelete("cancelledRun/{experimentId}")]
        Task<bool> CancelCachedPipelineRunAsync(string subscriptionId, string resourceGroupName, string workspaceName, string experimentId);

        [HttpDelete("allCached")]
        Task<bool> DeleteAllCachedPipelineRunsAsync(string subscriptionId, string resourceGroupName, string workspaceName, string workspaceId = null);

        [HttpDelete("{experimentId}")]
        Task<bool> DeleteCachedPipelineRunAsync(string subscriptionId, string resourceGroupName, string workspaceName, string experimentId);

        [HttpGet("allCached")]
        Task<IEnumerable<CachedPipelineRunEntity>> GetAllCachedPipelineRunsAsync(string subscriptionId, string resourceGroupName, string workspaceName);
    }
}
