﻿// <copyright file="IRunningPipelineRunQuotaController.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Microsoft.MachineLearning.Common.WebApi.Client;
using System.Threading.Tasks;

namespace Microsoft.Aether.QuotaService.Shared.Controllers
{
    [InterfaceRoutePrefix("pipelinequotaservice/v1.0/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.MachineLearningServices/workspaces/{workspaceName}/RunningPipelineRunQuota")]
    public interface IRunningPipelineRunQuotaController
    {
        [HttpPost("PipelineRuns/{experimentId}")]
        Task<QuotaResponse> AcquireQuotaAsync(string subscriptionId, string resourceGroupName, string workspaceName, string experimentId, string parentExperimentId = null);

        [HttpDelete("quotalimit")]
        Task<bool> DeleteQuotaLimitAsync(string subscriptionId, string resourceGroupName, string workspaceName, string workspaceId = null);

        [HttpDelete("quotausages")]
        Task<bool> DeleteUsagesAsync(string subscriptionId, string resourceGroupName, string workspaceName, string workspaceId = null);

        [HttpGet("quota")]
        Task<QuotaResponse> GetQuotaAsync(string subscriptionId, string resourceGroupName, string workspaceName);

        [HttpDelete("PipelineRuns/{experimentId}")]
        Task<bool> ReleaseQuotaAsync(string subscriptionId, string resourceGroupName, string workspaceName, string experimentId);

        [HttpPut("quotalimit")]
        Task<RunningPipelineRunQuotaLimitEntity> UpdateQuotaLimitAsync(string subscriptionId, string resourceGroupName, string workspaceName, [FromBody] QuotaLimitRequest quotaLimitRequest);
    }
}
