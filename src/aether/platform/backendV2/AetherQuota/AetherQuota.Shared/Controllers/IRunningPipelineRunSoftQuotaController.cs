﻿// <copyright file="IRunningPipelineRunSoftQuotaController.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Microsoft.MachineLearning.Common.WebApi.Client;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Microsoft.Aether.QuotaService.Shared.Controllers
{
    [InterfaceRoutePrefix("pipelinequotaservice/v1.0/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.MachineLearningServices/workspaces/{workspaceName}/RunningPipelineRunSoftQuota")]
    public interface IRunningPipelineRunSoftQuotaController
    {
        [HttpPost("PipelineRuns/{experimentId}")]
        Task<QuotaResponse> AcquireSoftQuotaAsync(string subscriptionId, string resourceGroupName, string workspaceName, string experimentId, string parentExperimentId = null);

        [HttpGet("quota")]
        Task<QuotaResponse> GetSoftQuotaAsync(string subscriptionId, string resourceGroupName, string workspaceName);

        [HttpDelete("PipelineRuns/{experimentId}")]
        Task<bool> ReleaseSoftQuotaAsync(string subscriptionId, string resourceGroupName, string workspaceName, string experimentId);

        [HttpPut("quotalimit")]
        Task<RunningPipelineRunQuotaLimitEntity> UpdateSoftQuotaLimitAsync(string subscriptionId, string resourceGroupName, string workspaceName, [FromBody] QuotaLimitRequest quotaLimitRequest);
    }
}
