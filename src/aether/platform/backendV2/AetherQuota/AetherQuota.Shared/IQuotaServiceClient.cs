﻿// <copyright file="IQuotaServiceClient.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Microsoft.Aether.DataContracts.Entity;
using Microsoft.RelInfra.Instrumentation;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Microsoft.Aether.QuotaService.Shared
{
    public interface IQuotaServiceClient
    {
        /// <summary>
        /// General method to acquire quota and emit logs/metrics.
        /// </summary>
        public Task<QuotaResponse> AcquireQuotaAsync(QuotaType quotaType, WorkspaceIdentity workspace, string resourceId, CounterManager counterManager, string parentId = null);

        /// <summary>
        /// General method to release quota and emit logs/metrics.
        /// </summary>
        public Task ReleaseQuotaAsync(QuotaType quotaType, WorkspaceIdentity workspace, string resourceId, CounterManager counterManager);

        public Task<QuotaResponse> AcquireRunningPipelineRunQuotaAsync(WorkspaceIdentity workspace, string experimentId, string parentExperimentId = null);

        public Task ReleaseRunningPipelineRunQuotaAsync(WorkspaceIdentity workspace, string experimentId);

        public Task<QuotaResponse> GetRunningPipelineRunQuotaAsync(WorkspaceIdentity workspace);

        public Task<RunningPipelineRunQuotaLimitEntity> UpdateRunningPipelineRunQuotaLimitAsync(WorkspaceIdentity workspace, int maxLimit);

        public Task DeleteRunningPipelineRunQuotaLimitAsync(WorkspaceIdentity workspace);

        public Task DeleteRunningPipelineRunQuotaUsagesAsync(WorkspaceIdentity workspace);

        public Task<QuotaResponse> AcquireRunningPipelineRunSoftQuotaAsync(WorkspaceIdentity workspace, string experimentId, string parentExperimentId = null);

        public Task ReleaseRunningPipelineRunSoftQuotaAsync(WorkspaceIdentity workspace, string experimentId);

        public Task<QuotaResponse> GetRunningPipelineRunSoftQuotaAsync(WorkspaceIdentity workspace);

        public Task<RunningPipelineRunQuotaLimitEntity> UpdateRunningPipelineRunSoftQuotaLimitAsync(WorkspaceIdentity workspace, int maxLimit);

        public Task<bool> AddCachedPipelineRunAsync(WorkspaceIdentity workspace, string experimentId, string queueMessage);

        public Task DeleteCachedPipelineRunAsync(WorkspaceIdentity workspace, string experimentId);

        public Task CancelCachedPipelineRunAsync(WorkspaceIdentity workspace, string experimentId);

        public Task DeleteAllCachedPipelineRunsAsync(WorkspaceIdentity workspace);

        public Task<IEnumerable<CachedPipelineRunEntity>> GetAllCachedPipelineRunsAsync(WorkspaceIdentity workspace);
    }
}
