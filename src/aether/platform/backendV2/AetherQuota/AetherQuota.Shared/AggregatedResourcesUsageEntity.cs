﻿// <copyright file="AggregatedResourcesUsageEntity.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

namespace Microsoft.Aether.QuotaService.Shared
{
    [DataContract]
    public class AggregatedResourcesUsageEntity
    {
        private string _id;
        private string _name;

        [DataMember]
        public string Id
        {
            get
            {
                return _id;
            }

            set
            {
                _id = value;
            }
        }

        [DataMember]
        public string Name
        {
            get
            {
                return _name;
            }

            set
            {
                _id = value;
            }
        }

        // replaced by Id, keeping it for backward compatibility
        [DataMember]
        [Obsolete]
        public string TeamId { get { return _id; } set { _id = value; } }

        // replaced by Name, keeping it for backward compatibility
        [DataMember]
        [Obsolete]
        public string TeamName { get { return _name; } set { _name = value; } }

        [DataMember]
        public List<ResourceUsageEntity> ConsumedResources { get; set; }
    }
}