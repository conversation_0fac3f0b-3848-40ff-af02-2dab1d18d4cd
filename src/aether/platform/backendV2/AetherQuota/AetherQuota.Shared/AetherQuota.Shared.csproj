<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <RootNamespace>Microsoft.Aether.QuotaService.Shared</RootNamespace>
    <AssemblyName>Microsoft.Aether.QuotaService.Shared</AssemblyName>
    <TargetFramework>net6.0</TargetFramework>
  </PropertyGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\..\Microsoft.MachineLearning.Pipeline.Common\Microsoft.MachineLearning.Pipeline.Common.csproj" />
    <ProjectReference Include="..\..\BlueBox\AetherK8SBaseService\Microsoft.Aether.BlueBox.AetherK8SBaseService.csproj" />
    <ProjectReference Include="..\..\shared\Microsoft.Aether.BackendCommon\Microsoft.Aether.BackendCommon.csproj" />
    <ProjectReference Include="..\..\shared\Microsoft.Aether.DataContract\Microsoft.Aether.DataContracts.csproj" />
  </ItemGroup>
</Project>
