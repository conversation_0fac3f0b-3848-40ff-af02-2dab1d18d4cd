﻿// <copyright file="QuotaLimitEntity.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Runtime.Serialization;
using Microsoft.Aether.DataContracts.Entity;
using Newtonsoft.Json;

namespace Microsoft.Aether.QuotaService.Shared
{
    [DataContract]
    public class QuotaLimitEntity
    {
        /// <summary>
        /// The id of configured quota.
        /// Could be workspaceId or subscriptionId according to the quota dimension.
        /// </summary>
        [DataMember]
        public string Id { get; set; }

        /// <summary>
        /// The workspace which the quota belongs to.
        /// </summary>
        [DataMember]
        public WorkspaceIdentity Workspace { get; set; }

        /// <summary>
        /// The max limit of the quota.
        /// </summary>
        [DataMember]
        public int MaxLimit { get; set; }

        /// <summary>
        /// The max soft limit of the quota.
        /// It's null if a quota type doesn't support soft limit yet.
        /// For quota type with both hard and soft limit,
        /// the <see cref="MaxLimit"/> works as the upper hard threshold while the <see cref="MaxSoftLimit"/> works as lower soft threshold.
        /// </summary>
        [DataMember]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public int? MaxSoftLimit { get; set; }
    }
}
