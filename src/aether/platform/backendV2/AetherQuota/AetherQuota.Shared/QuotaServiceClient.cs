﻿// <copyright file="QuotaServiceClient.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Microsoft.Aether.DataContracts;
using Microsoft.Aether.DataContracts.Entity;
using Microsoft.Aether.QuotaService.Shared.Controllers;
using Microsoft.Aether.S2S.Common;
using Microsoft.MachineLearning.Pipeline.Common;
using Microsoft.RelInfra.Common.Exceptions;
using Microsoft.RelInfra.Common.RetryExecution;
using Microsoft.RelInfra.Common.RetryExecution.Policy;
using Microsoft.RelInfra.Common.Serialization;
using Microsoft.RelInfra.Extensions;
using Microsoft.RelInfra.Instrumentation;
using Microsoft.RelInfra.Instrumentation.Logging;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading.Tasks;

namespace Microsoft.Aether.QuotaService.Shared
{
    public class QuotaServiceClient : IQuotaServiceClient
    {
        private readonly ICachedPipelineRunController _cachedPipelineRunController;
        private readonly IRunningPipelineRunQuotaController _runningPipelineRunQuotaController;
        private readonly IRunningPipelineRunSoftQuotaController _runningPipelineRunSoftQuotaController;
        private readonly IRetryPolicy _retryPolicy;

        public QuotaServiceClient(
            string baseUrl,
            IS2STokenProvider s2STokenProvider,
            IRetryPolicy retryPolicy = null)
        {
            _retryPolicy = retryPolicy ?? new ExponentialBackoffRetryPolicy();
            AmlClientFactory clientFactory = new AmlClientFactory(s2STokenProvider, baseUrl);
            _cachedPipelineRunController = clientFactory.GetClient<ICachedPipelineRunController>(new CreatedBy());
            GC.Collect(); // ServiceInvoker consumes around 120MB first time for each controller, we need to force GC.
            _runningPipelineRunQuotaController = clientFactory.GetClient<IRunningPipelineRunQuotaController>(new CreatedBy());
            GC.Collect();
            _runningPipelineRunSoftQuotaController = clientFactory.GetClient<IRunningPipelineRunSoftQuotaController>(new CreatedBy());
            GC.Collect();
        }

        public async Task<QuotaResponse> AcquireQuotaAsync(QuotaType quotaType, WorkspaceIdentity workspace, string resourceId, CounterManager counterManager, string parentId = null)
        {
            string workspaceInfo = SerializationHelpers.SerializeEntity(workspace);
            CommonLogger.LogEntityInfo(resourceId, "Try to acquire {quotaType} quota. Workspace: {workspace}.", quotaType, workspaceInfo);
            counterManager.GetNumberCounter($"Acquire{quotaType}QuotaNumberCounter").Set(1);
            // By default, assume quota is available.
            QuotaResponse quotaResponse = new QuotaResponse { IsQuotaAvailable = true };
            try
            {
                using (new DisposableTimer(counterManager.GetLatencyCounter($"Acquire{quotaType}QuotaLatencyCounter")))
                {
                    switch (quotaType)
                    {
                    case QuotaType.RunningPipelineRun:
                        quotaResponse = await this.AcquireRunningPipelineRunQuotaAsync(workspace, resourceId, parentId).ConfigureAwait(false);
                        break;

                    case QuotaType.RunningPipelineRunSoft:
                        quotaResponse = await this.AcquireRunningPipelineRunSoftQuotaAsync(workspace, resourceId, parentId).ConfigureAwait(false);
                        break;

                    default:
                        CommonLogger.LogEntityWarning(resourceId, "Quota type {quotaType} is not enabled yet. Workspace: {workspace}.", quotaType, workspaceInfo);
                        break;
                    }

                    if (quotaResponse.IsQuotaAvailable)
                    {
                        CommonLogger.LogEntityInfo(resourceId, "Acquire {quotaType} quota successfully. Workspace: {workspace}.", quotaType, workspaceInfo);
                        counterManager.GetNumberCounter($"Acquire{quotaType}QuotaSuccessNumberCounter").Set(1);
                    }
                    else
                    {
                        CommonLogger.LogEntityWarning(resourceId, "Failed to acquire {quotaType} quota because it exceeds maximum limit and not available. Workspace: {workspace}.", quotaType, workspaceInfo);
                        counterManager.GetNumberCounter($"Exceed{quotaType}QuotaNumberCounter").Set(1);
                    }
                }
            }
            catch (Exception ex)
            {
                CommonLogger.LogEntityWarning(resourceId, "Acquire {quotaType} quota failed with exception. Assuming quota is available. Workspace: {workspace}. Exception: {exception}", quotaType, workspaceInfo, ex);
                counterManager.GetNumberCounter($"Acquire{quotaType}QuotaFailureNumberCounter").Set(1);
            }

            return quotaResponse;
        }

        public async Task ReleaseQuotaAsync(QuotaType quotaType, WorkspaceIdentity workspace, string resourceId, CounterManager counterManager)
        {
            string workspaceInfo = SerializationHelpers.SerializeEntity(workspace);
            CommonLogger.LogEntityInfo(resourceId, "Try to release {quotaType} quota. Workspace: {workspace}.", quotaType, workspaceInfo);
            counterManager.GetNumberCounter($"Release{quotaType}QuotaNumberCounter").Set(1);
            try
            {
                using (new DisposableTimer(counterManager.GetLatencyCounter($"Release{quotaType}QuotaLatencyCounter")))
                {
                    // Try to release quota. It's OK to call release API multiple times,
                    // only the first call releases quota (if occupied) while following calls won't change current quota usage.
                    switch (quotaType)
                    {
                    case QuotaType.RunningPipelineRun:
                        await this.ReleaseRunningPipelineRunQuotaAsync(workspace, resourceId).ConfigureAwait(false);
                        break;

                    case QuotaType.RunningPipelineRunSoft:
                        await this.ReleaseRunningPipelineRunSoftQuotaAsync(workspace, resourceId).ConfigureAwait(false);
                        break;

                    default:
                        CommonLogger.LogEntityWarning(resourceId, "Quota type {quotaType} is not enabled yet. Workspace: {workspace}.", quotaType, workspaceInfo);
                        break;
                    }

                    CommonLogger.LogEntityInfo(resourceId, "Release {quotaType} quota successfully. Workspace: {workspace}.", quotaType, workspaceInfo);
                    counterManager.GetNumberCounter($"Release{quotaType}QuotaSuccessNumberCounter").Set(1);
                }
            }
            catch (Exception ex)
            {
                // It's OK to catch and ignore. QuotaService has mechanism to remove and release quota eventually.
                CommonLogger.LogEntityWarning(resourceId, "Release {quotaType} quota failed with exception. Workspace: {workspace}. Exception: {exception}", quotaType, workspaceInfo, ex);
                counterManager.GetNumberCounter($"Release{quotaType}QuotaFailureNumberCounter").Set(1);
            }
        }

        public async Task<QuotaResponse> AcquireRunningPipelineRunQuotaAsync(WorkspaceIdentity workspace, string experimentId, string parentExperimentId = null)
        {
            try
            {
                return await RetryExecutionHelper.ExecuteFuncWithRetryAsync(
                        function: async () => await _runningPipelineRunQuotaController.AcquireQuotaAsync(workspace.SubscriptionId, workspace.ResourceGroupName, workspace.WorkspaceName, experimentId, parentExperimentId),
                        shouldRetryException: HttpClientExtensions.ShouldRetryServiceInvokerHttpClientException,
                        retryPolicy: _retryPolicy,
                        token: default);
            }
            catch (Exception ex)
            {
                throw new ServiceInvocationException(
                    operationName: "AcquireRunningPipelineRunQuotaAsync",
                    calledService: "QuotaService",
                    httpMethod: HttpMethod.Post,
                    innerException: ex);
            }
        }

        public async Task ReleaseRunningPipelineRunQuotaAsync(WorkspaceIdentity workspace, string experimentId)
        {
            try
            {
                await RetryExecutionHelper.ExecuteFuncWithRetryAsync(
                        action: async () => await _runningPipelineRunQuotaController.ReleaseQuotaAsync(workspace.SubscriptionId, workspace.ResourceGroupName, workspace.WorkspaceName, experimentId),
                        shouldRetryException: HttpClientExtensions.ShouldRetryServiceInvokerHttpClientException,
                        retryPolicy: _retryPolicy,
                        token: default);
            }
            catch (Exception ex)
            {
                throw new ServiceInvocationException(
                    operationName: "ReleaseRunningPipelineRunQuotaAsync",
                    calledService: "QuotaService",
                    httpMethod: HttpMethod.Delete,
                    innerException: ex);
            }
        }

        public async Task<QuotaResponse> GetRunningPipelineRunQuotaAsync(WorkspaceIdentity workspace)
        {
            try
            {
                return await RetryExecutionHelper.ExecuteFuncWithRetryAsync(
                        function: async () => await _runningPipelineRunQuotaController.GetQuotaAsync(workspace.SubscriptionId, workspace.ResourceGroupName, workspace.WorkspaceName),
                        shouldRetryException: HttpClientExtensions.ShouldRetryServiceInvokerHttpClientException,
                        retryPolicy: _retryPolicy,
                        token: default);
            }
            catch (Exception ex)
            {
                throw new ServiceInvocationException(
                    operationName: "GetRunningPipelineRunQuotaAsync",
                    calledService: "QuotaService",
                    httpMethod: HttpMethod.Get,
                    innerException: ex);
            }
        }

        public async Task<RunningPipelineRunQuotaLimitEntity> UpdateRunningPipelineRunQuotaLimitAsync(WorkspaceIdentity workspace, int maxLimit)
        {
            try
            {
                var quotaLimitRequest = new QuotaLimitRequest { MaxLimit = maxLimit };
                return await RetryExecutionHelper.ExecuteFuncWithRetryAsync(
                        function: async () => await _runningPipelineRunQuotaController.UpdateQuotaLimitAsync(workspace.SubscriptionId, workspace.ResourceGroupName, workspace.WorkspaceName, quotaLimitRequest),
                        shouldRetryException: HttpClientExtensions.ShouldRetryServiceInvokerHttpClientException,
                        retryPolicy: _retryPolicy,
                        token: default);
            }
            catch (Exception ex)
            {
                throw new ServiceInvocationException(
                    operationName: "ConfigureRunningPipelineRunQuotaLimitAsync",
                    calledService: "QuotaService",
                    httpMethod: HttpMethod.Put,
                    innerException: ex);
            }
        }

        public async Task DeleteRunningPipelineRunQuotaLimitAsync(WorkspaceIdentity workspace)
        {
            try
            {
                await RetryExecutionHelper.ExecuteFuncWithRetryAsync(
                        action: async () => await _runningPipelineRunQuotaController.DeleteQuotaLimitAsync(workspace.SubscriptionId, workspace.ResourceGroupName, workspace.WorkspaceName, workspace.WorkspaceId),
                        shouldRetryException: HttpClientExtensions.ShouldRetryServiceInvokerHttpClientException,
                        retryPolicy: _retryPolicy,
                        token: default);
            }
            catch (Exception ex)
            {
                throw new ServiceInvocationException(
                    operationName: "DeleteRunningPipelineRunQuotaAsync",
                    calledService: "QuotaService",
                    httpMethod: HttpMethod.Delete,
                    innerException: ex);
            }
        }

        public async Task DeleteRunningPipelineRunQuotaUsagesAsync(WorkspaceIdentity workspace)
        {
            try
            {
                await RetryExecutionHelper.ExecuteFuncWithRetryAsync(
                        action: async () => await _runningPipelineRunQuotaController.DeleteUsagesAsync(workspace.SubscriptionId, workspace.ResourceGroupName, workspace.WorkspaceName, workspace.WorkspaceId),
                        shouldRetryException: HttpClientExtensions.ShouldRetryServiceInvokerHttpClientException,
                        retryPolicy: _retryPolicy,
                        token: default);
            }
            catch (Exception ex)
            {
                throw new ServiceInvocationException(
                    operationName: "DeleteRunningPipelineRunQuotaUsagesAsync",
                    calledService: "QuotaService",
                    httpMethod: HttpMethod.Delete,
                    innerException: ex);
            }
        }

        public async Task<QuotaResponse> AcquireRunningPipelineRunSoftQuotaAsync(WorkspaceIdentity workspace, string experimentId, string parentExperimentId = null)
        {
            try
            {
                return await RetryExecutionHelper.ExecuteFuncWithRetryAsync(
                        function: async () => await _runningPipelineRunSoftQuotaController.AcquireSoftQuotaAsync(workspace.SubscriptionId, workspace.ResourceGroupName, workspace.WorkspaceName, experimentId, parentExperimentId),
                        shouldRetryException: HttpClientExtensions.ShouldRetryServiceInvokerHttpClientException,
                        retryPolicy: _retryPolicy,
                        token: default);
            }
            catch (Exception ex)
            {
                throw new ServiceInvocationException(
                    operationName: "AcquireRunningPipelineRunSoftQuotaAsync",
                    calledService: "QuotaService",
                    httpMethod: HttpMethod.Post,
                    innerException: ex);
            }
        }

        public async Task ReleaseRunningPipelineRunSoftQuotaAsync(WorkspaceIdentity workspace, string experimentId)
        {
            try
            {
                await RetryExecutionHelper.ExecuteFuncWithRetryAsync(
                        action: async () => await _runningPipelineRunSoftQuotaController.ReleaseSoftQuotaAsync(workspace.SubscriptionId, workspace.ResourceGroupName, workspace.WorkspaceName, experimentId),
                        shouldRetryException: HttpClientExtensions.ShouldRetryServiceInvokerHttpClientException,
                        retryPolicy: _retryPolicy,
                        token: default);
            }
            catch (Exception ex)
            {
                throw new ServiceInvocationException(
                    operationName: "ReleaseRunningPipelineRunSoftQuotaAsync",
                    calledService: "QuotaService",
                    httpMethod: HttpMethod.Delete,
                    innerException: ex);
            }
        }

        public async Task<QuotaResponse> GetRunningPipelineRunSoftQuotaAsync(WorkspaceIdentity workspace)
        {
            try
            {
                return await RetryExecutionHelper.ExecuteFuncWithRetryAsync(
                        function: async () => await _runningPipelineRunSoftQuotaController.GetSoftQuotaAsync(workspace.SubscriptionId, workspace.ResourceGroupName, workspace.WorkspaceName),
                        shouldRetryException: HttpClientExtensions.ShouldRetryServiceInvokerHttpClientException,
                        retryPolicy: _retryPolicy,
                        token: default);
            }
            catch (Exception ex)
            {
                throw new ServiceInvocationException(
                    operationName: "GetRunningPipelineRunSoftQuotaAsync",
                    calledService: "QuotaService",
                    httpMethod: HttpMethod.Get,
                    innerException: ex);
            }
        }

        public async Task<RunningPipelineRunQuotaLimitEntity> UpdateRunningPipelineRunSoftQuotaLimitAsync(WorkspaceIdentity workspace, int maxLimit)
        {
            try
            {
                var quotaLimitRequest = new QuotaLimitRequest { MaxLimit = maxLimit };
                return await RetryExecutionHelper.ExecuteFuncWithRetryAsync(
                        function: async () => await _runningPipelineRunSoftQuotaController.UpdateSoftQuotaLimitAsync(workspace.SubscriptionId, workspace.ResourceGroupName, workspace.WorkspaceName, quotaLimitRequest),
                        shouldRetryException: HttpClientExtensions.ShouldRetryServiceInvokerHttpClientException,
                        retryPolicy: _retryPolicy,
                        token: default);
            }
            catch (Exception ex)
            {
                throw new ServiceInvocationException(
                    operationName: "ConfigureRunningPipelineRunSoftQuotaLimitAsync",
                    calledService: "QuotaService",
                    httpMethod: HttpMethod.Put,
                    innerException: ex);
            }
        }

        public async Task<bool> AddCachedPipelineRunAsync(WorkspaceIdentity workspace, string experimentId, string queueMessage)
        {
            try
            {
                return await RetryExecutionHelper.ExecuteFuncWithRetryAsync(
                        function: async () => await _cachedPipelineRunController.AddCachedPipelineRunAsync(workspace.SubscriptionId, workspace.ResourceGroupName, workspace.WorkspaceName, experimentId, queueMessage),
                        shouldRetryException: HttpClientExtensions.ShouldRetryServiceInvokerHttpClientException,
                        retryPolicy: _retryPolicy,
                        token: default);
            }
            catch (Exception ex)
            {
                throw new ServiceInvocationException(
                    operationName: "AddCachedPipelineRunAsync",
                    calledService: "QuotaService",
                    httpMethod: HttpMethod.Post,
                    innerException: ex);
            }
        }

        public async Task DeleteAllCachedPipelineRunsAsync(WorkspaceIdentity workspace)
        {
            try
            {
                await RetryExecutionHelper.ExecuteFuncWithRetryAsync(
                        action: async () => await _cachedPipelineRunController.DeleteAllCachedPipelineRunsAsync(workspace.SubscriptionId, workspace.ResourceGroupName, workspace.WorkspaceName, workspace.WorkspaceId),
                        shouldRetryException: HttpClientExtensions.ShouldRetryServiceInvokerHttpClientException,
                        retryPolicy: _retryPolicy,
                        token: default);
            }
            catch (Exception ex)
            {
                throw new ServiceInvocationException(
                    operationName: "DeleteAllCachedPipelineRunsAsync",
                    calledService: "QuotaService",
                    httpMethod: HttpMethod.Delete,
                    innerException: ex);
            }
        }

        public async Task CancelCachedPipelineRunAsync(WorkspaceIdentity workspace, string experimentId)
        {
            try
            {
                await RetryExecutionHelper.ExecuteFuncWithRetryAsync(
                    action: async () => await _cachedPipelineRunController.CancelCachedPipelineRunAsync(workspace.SubscriptionId, workspace.ResourceGroupName, workspace.WorkspaceName, experimentId),
                    shouldRetryException: HttpClientExtensions.ShouldRetryServiceInvokerHttpClientException,
                    retryPolicy: _retryPolicy,
                    token: default);
            }
            catch (Exception ex)
            {
                throw new ServiceInvocationException(
                    operationName: "CancelCachedPipelineRunAsync",
                    calledService: "QuotaService",
                    httpMethod: HttpMethod.Delete,
                    innerException: ex);
            }
        }

        public async Task DeleteCachedPipelineRunAsync(WorkspaceIdentity workspace, string experimentId)
        {
            try
            {
                await RetryExecutionHelper.ExecuteFuncWithRetryAsync(
                    action: async () => await _cachedPipelineRunController.DeleteCachedPipelineRunAsync(workspace.SubscriptionId, workspace.ResourceGroupName, workspace.WorkspaceName, experimentId),
                    shouldRetryException: HttpClientExtensions.ShouldRetryServiceInvokerHttpClientException,
                    retryPolicy: _retryPolicy,
                    token: default);
            }
            catch (Exception ex)
            {
                throw new ServiceInvocationException(
                    operationName: "DeleteCachedPipelineRunAsync",
                    calledService: "QuotaService",
                    httpMethod: HttpMethod.Delete,
                    innerException: ex);
            }
        }

        public async Task<IEnumerable<CachedPipelineRunEntity>> GetAllCachedPipelineRunsAsync(WorkspaceIdentity workspace)
        {
            try
            {
                return await RetryExecutionHelper.ExecuteFuncWithRetryAsync(
                        function: async () => await _cachedPipelineRunController.GetAllCachedPipelineRunsAsync(workspace.SubscriptionId, workspace.ResourceGroupName, workspace.WorkspaceName),
                        shouldRetryException: HttpClientExtensions.ShouldRetryServiceInvokerHttpClientException,
                        retryPolicy: _retryPolicy,
                        token: default);
            }
            catch (Exception ex)
            {
                throw new ServiceInvocationException(
                    operationName: "GetAllCachedPipelineRunsAsync",
                    calledService: "QuotaService",
                    httpMethod: HttpMethod.Get,
                    innerException: ex);
            }
        }
    }
}
