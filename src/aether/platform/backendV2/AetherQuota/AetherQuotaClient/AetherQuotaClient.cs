﻿// <copyright file="AetherQuotaClient.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading.Tasks;
using Microsoft.Aether.BackendCommon;
using Microsoft.Aether.QuotaService.Shared;
using Microsoft.Aether.S2S.Common;
using Microsoft.RelInfra.Extensions;
using Microsoft.RelInfra.Instrumentation.Logging;

namespace Microsoft.Aether.QuotaService.Client
{
    public class AetherQuotaClient : IAetherQuotaClient
    {
        private readonly HttpClientWithAuthProvider _clientProvider;

        public AetherQuotaClient(string baseUrl, IS2STokenProvider s2STokenProvider) : this(baseUrl, new HttpClientHandler { UseDefaultCredentials = true, PreAuthenticate = true }, s2STokenProvider)
        {
        }

        public AetherQuotaClient(string baseUrl, HttpMessageHandler messageHandler, IS2STokenProvider s2STokenProvider)
        {
            _clientProvider = new HttpClientWithAuthProvider(
               handler: messageHandler,
               baseAddress: baseUrl,
               s2STokenProvider: s2STokenProvider);
        }

        public async Task<QuotaReservationResponse> ReserveJobsQuotaAsync(string teamId, string userName, string experimentId, int jobsCount)
        {
            string serverPath = $"jobsquota/{teamId}/{userName}/{experimentId}/{jobsCount}";

            try
            {
                var client = await _clientProvider.GetClientAsync();
                return await client.GetEntityAsync<QuotaReservationResponse>(serverPath);
            }
            catch (Exception ex)
            {
                CommonLogger.LogEntityError(experimentId, "Exception: {exception}", ex);

                throw;
            }
        }

        public async Task<ResourceUsageEntity> GetRunningJobsResourceUsageAsync(string teamId, string userName, string experimentId)
        {
            string serverPath = $"runningjobsquota/{teamId}/{userName}/{experimentId}";
            try
            {
                var client = await _clientProvider.GetClientAsync();
                return await client.GetEntityAsync<ResourceUsageEntity>(serverPath);
            }
            catch (Exception ex)
            {
                CommonLogger.LogEntityError(experimentId, "Exception: {exception}", ex);

                throw;
            }
        }

        public async Task<QuotaReservationResponse> IncrementRunningJobsQuotaAsync(string teamId, string userName, string experimentId, int incrementJobsCount)
        {
            string serverPath = $"runningjobsquota/{teamId}/{userName}/{experimentId}";
            try
            {
                var client = await _clientProvider.GetClientAsync();
                return await client.PostEntityAsync<int, QuotaReservationResponse>(serverPath, incrementJobsCount);
            }
            catch (Exception ex)
            {
                CommonLogger.LogEntityError(experimentId, "Exception: {exception}", ex);

                throw;
            }
        }

        public async Task<QuotaReservationResponse> ReserveRunningJobsQuotaAsync(string teamId, string userName, string experimentId, int absoluteJobsCount)
        {
            string serverPath = $"runningjobsquota/{teamId}/{userName}/{experimentId}";
            try
            {
                var client = await _clientProvider.GetClientAsync();
                return await client.PutEntityAsync<int, QuotaReservationResponse>(serverPath, absoluteJobsCount);
            }
            catch (Exception ex)
            {
                CommonLogger.LogEntityError(experimentId, "Exception: {exception}", ex);

                throw;
            }
        }

        public async Task ReleaseRunningJobsQuotaAsync(string teamId, string userName, string experimentId)
        {
            string serverPath = $"runningjobsquota/{teamId}/{userName}/{experimentId}";
            try
            {
                var client = await _clientProvider.GetClientAsync();
                await client.DeleteAsync(serverPath);
            }
            catch (Exception ex)
            {
                CommonLogger.LogEntityError(experimentId, "Exception: {exception}", ex);

                throw;
            }
        }

        public async Task<QuotaReservationResponse> ReserveEmailQuotaAsync(string teamId, string userName, string emailId)
        {
            string serverPath = $"email/{teamId}/{userName}/{emailId}";
            try
            {
                var client = await _clientProvider.GetClientAsync();
                return await client.GetEntityAsync<QuotaReservationResponse>(serverPath);
            }
            catch (Exception ex)
            {
                CommonLogger.LogEntityError(emailId, "Exception: {exception}", ex);

                throw;
            }
        }

        public async Task<QuotaReservationResponse> ReservePromotionQuotaAsync(string teamId, string userName, string promotionTaskId)
        {
            string serverPath = $"promotion/{teamId}/{userName}/{promotionTaskId}";
            try
            {
                var client = await _clientProvider.GetClientAsync();
                return await client.GetEntityAsync<QuotaReservationResponse>(serverPath);
            }
            catch (Exception ex)
            {
                CommonLogger.LogEntityError(promotionTaskId, "Exception: {exception}", ex);

                throw;
            }
        }

        public async Task<QuotaReservationResponse> ReserveUploadQuotaAsync(string teamId, string userName, string uploadTaskId)
        {
            string serverPath = $"upload/{teamId}/{userName}/{uploadTaskId}";
            try
            {
                var client = await _clientProvider.GetClientAsync();
                return await client.GetEntityAsync<QuotaReservationResponse>(serverPath);
            }
            catch (Exception ex)
            {
                CommonLogger.LogEntityError(uploadTaskId, "Exception: {exception}", ex);

                throw;
            }
        }

        public async Task<List<AggregatedResourcesUsageEntity>> GetExperimentsUsageAsync()
        {
            string serverPath = $"usage/experiments";
            try
            {
                var client = await _clientProvider.GetClientAsync();
                return await client.GetEntityAsync<List<AggregatedResourcesUsageEntity>>(serverPath);
            }
            catch (Exception ex)
            {
                CommonLogger.LogEntityError("GetAllExperiemntsUsageQuota", "Exception: {exception}", ex);

                throw;
            }
        }

        public async Task<List<AggregatedResourcesUsageEntity>> GetEmailsUsageAsync()
        {
            string serverPath = $"usage/emails";
            try
            {
                var client = await _clientProvider.GetClientAsync();
                return await client.GetEntityAsync<List<AggregatedResourcesUsageEntity>>(serverPath);
            }
            catch (Exception ex)
            {
                CommonLogger.LogEntityError("GetAllEmailsUsageQuota", "Exception: {exception}", ex);

                throw;
            }
        }

        public async Task<List<AggregatedResourcesUsageEntity>> GetPromotionsUsageAsync()
        {
            string serverPath = $"usage/promotions";
            try
            {
                var client = await _clientProvider.GetClientAsync();
                return await client.GetEntityAsync<List<AggregatedResourcesUsageEntity>>(serverPath);
            }
            catch (Exception ex)
            {
                CommonLogger.LogEntityError("GetAllPromotionsUsageQuota", "Exception: {exception}", ex);

                throw;
            }
        }

        public async Task<List<AggregatedResourcesUsageEntity>> GetUploadsUsageAsync()
        {
            string serverPath = $"usage/uploads";
            try
            {
                var client = await _clientProvider.GetClientAsync();
                return await client.GetEntityAsync<List<AggregatedResourcesUsageEntity>>(serverPath);
            }
            catch (Exception ex)
            {
                CommonLogger.LogEntityError("GetAllUploadsUsageQuota", "Exception: {exception}", ex);

                throw;
            }
        }
    }
}