﻿// <copyright file="CachedPipelineRunManager.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Microsoft.Aether.BlueBox.Storage;
using Microsoft.Aether.DataContracts.Entity;
using Microsoft.Aether.QuotaService;
using Microsoft.Aether.QuotaService.Shared;
using Microsoft.RelInfra.Common.RetryExecution.Policy;
using Microsoft.RelInfra.Instrumentation;
using Microsoft.RelInfra.Instrumentation.Logging;
using Microsoft.RelInfra.Storage;
using Microsoft.RelInfra.Storage.Exceptions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Microsoft.Aether.QuotaService
{
    public class CachedPipelineRunManager : ICachedPipelineRunManager
    {
        private const int MaxRetries = 10;
        private const int MinIntervalInMs = 100;
        private const int MaxIntervalInMs = 1000;

        protected readonly IQueryableDbStorage _cachedContainer;
        protected IRetryPolicy _retryPolicy;
        protected TimeSpan _requestTimeout;

        private readonly CounterManager _counterManager;
        private readonly IExperimentQueuePublisher _experimentQueuePublisher;

        public CachedPipelineRunManager(
            QuotaServiceConfig config,
            CounterManager counterManager,
            IQueryableDbStorage cachedContainer,
            IExperimentQueuePublisher experimentQueuePublisher,
            TimeSpan? requestTimeout = null)
        {
            _counterManager = counterManager;
            _cachedContainer = cachedContainer;
            _experimentQueuePublisher = experimentQueuePublisher;
            _retryPolicy = new RandomRetryPolicy(
                maxAttempts: MaxRetries,
                minIntervalInMs: MinIntervalInMs,
                maxIntervalInMs: MaxIntervalInMs);
            _requestTimeout = requestTimeout ?? config.RequestTimeout;
        }

        public virtual async Task<CachedPipelineRunEntity> GetCachedPipelineRunAsync(WorkspaceIdentity workspace, string cachedId)
        {
            try
            {
                return await CosmosDbRetryHelper.ExecuteCosmosDbWithRetry(
                func: async () =>
                {
                    Storable<CachedPipelineRunEntity> cachedStorable;
                    using (var cts = new CancellationTokenSource(_requestTimeout))
                    {
                        cachedStorable = await _cachedContainer.GetAsync<CachedPipelineRunEntity>(workspace.WorkspaceId, cachedId, cancellationToken: cts.Token);
                    }

                    CommonLogger.LogEntityInfo(cachedId, "Get cached pipeline run for WorkspaceId={workspaceId}, WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}",
                        workspace.WorkspaceId, workspace.WorkspaceName, workspace.ResourceGroupName, workspace.SubscriptionId);
                    return cachedStorable?.Entity;
                },
                retryPolicy: _retryPolicy).ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                if (ex is RelInfraStorageException && ((RelInfraStorageException)ex).Error == StorageError.NotFound)
                {
                    CommonLogger.LogEntityWarning(cachedId, "Cached pipeline run is not found for WorkspaceId={workspaceId}, WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}. Error: {exception}",
                    workspace.WorkspaceId, workspace.WorkspaceName, workspace.ResourceGroupName, workspace.SubscriptionId, ex);
                    return null;
                }

                CommonLogger.LogEntityError(cachedId, "Failed to get cached pipeline run for WorkspaceId={workspaceId}, WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}. Error: {exception}",
                    workspace.WorkspaceId, workspace.WorkspaceName, workspace.ResourceGroupName, workspace.SubscriptionId, ex);
                throw;
            }
        }

        public virtual async Task<bool> AddCachedPipelineRunAsync(WorkspaceIdentity workspace, CachedPipelineRunEntity cached)
        {
            try
            {
                return await CosmosDbRetryHelper.ExecuteCosmosDbWithRetry(
                    func: async () =>
                    {
                        bool cachedExist;
                        using (var cts = new CancellationTokenSource(_requestTimeout))
                        {
                            cachedExist = await _cachedContainer.ExistsAsync(workspace.WorkspaceId, cached.Id, cancellationToken: cts.Token);
                        }

                        if (cachedExist)
                        {
                            CommonLogger.LogEntityInfo(cached.Id, "Cached pipeline run already exist for WorkspaceId={workspaceId}, WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}",
                                workspace.WorkspaceId, workspace.WorkspaceName, workspace.ResourceGroupName, workspace.SubscriptionId);
                            return false;
                        }

                        using (var cts = new CancellationTokenSource(_requestTimeout))
                        {
                            await _cachedContainer.AddAsync(workspace.WorkspaceId, cached.Id, new Storable<CachedPipelineRunEntity>(cached), cancellationToken: cts.Token);
                            CommonLogger.LogEntityInfo(cached.Id, "Add cached pipeline run successfully for WorkspaceId={workspaceId}, WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}",
                                workspace.WorkspaceId, workspace.WorkspaceName, workspace.ResourceGroupName, workspace.SubscriptionId);
                            return true;
                        }
                    },
                    retryPolicy: _retryPolicy).ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                CommonLogger.LogEntityError(cached.Id, "Failed to add cached pipeline run for WorkspaceId={workspaceId}, WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}. Error: {exception}",
                    workspace.WorkspaceId, workspace.WorkspaceName, workspace.ResourceGroupName, workspace.SubscriptionId, ex);
                throw;
            }
        }

        public virtual async Task<bool> RemoveCachedPipelineRunAsync(WorkspaceIdentity workspace, string cachedId)
        {
            try
            {
                bool deleted = await CosmosDbRetryHelper.ExecuteCosmosDbWithRetry(
                    func: async () =>
                    {
                        using (var cts = new CancellationTokenSource(_requestTimeout))
                        {
                            return await _cachedContainer.TryDeleteAsync(workspace.WorkspaceId, cachedId, cancellationToken: cts.Token);
                        }
                    },
                    retryPolicy: _retryPolicy).ConfigureAwait(false);

                if (deleted)
                {
                    CommonLogger.LogEntityInfo(cachedId, "Remove cached pipeline run successfully for WorkspaceId={workspaceId}, WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}",
                        workspace.WorkspaceId, workspace.WorkspaceName, workspace.ResourceGroupName, workspace.SubscriptionId);
                }
                else
                {
                    CommonLogger.LogEntityInfo(cachedId, "Cached pipeline run already deleted and not exist, failed to remove it for WorkspaceId={workspaceId}, WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}",
                        workspace.WorkspaceId, workspace.WorkspaceName, workspace.ResourceGroupName, workspace.SubscriptionId);
                }

                return deleted;
            }
            catch (Exception ex)
            {
                CommonLogger.LogEntityError(cachedId, "Failed to remove cached pipeline run for WorkspaceId={workspaceId}, WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}. Error: {exception}",
                    workspace.WorkspaceId, workspace.WorkspaceName, workspace.ResourceGroupName, workspace.SubscriptionId, ex);
                throw;
            }
        }

        public virtual async Task<bool> CancelCachedPipelineRunAsync(WorkspaceIdentity workspace, string cachedId)
        {
            try
            {
                return await CosmosDbRetryHelper.ExecuteCosmosDbWithRetry(
                func: async () =>
                {
                    var currentCached = await GetCachedPipelineRunAsync(workspace, cachedId);
                    if (currentCached == null)
                    {
                        CommonLogger.LogEntityInfo(cachedId, "End cancel cached pipeline run due to no cached pipeline run found for WorkspaceId={workspaceId}, WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}",
                            workspace.WorkspaceId, workspace.WorkspaceName, workspace.ResourceGroupName, workspace.SubscriptionId);
                        return false;
                    }
                    else
                    {
                        // Put cached pipeline run into experiment queue to cancel it timely.
                        string experimentId = currentCached.Id;
                        string queueMessage = currentCached.QueueMessage;
                        await _experimentQueuePublisher.EnqueueExperimentAsync(workspace, experimentId, queueMessage);

                        // Remove cached pipeline run after enqueued successfully.
                        await RemoveCachedPipelineRunAsync(workspace, currentCached.Id);
                        return true;
                    }
                },
                retryPolicy: _retryPolicy).ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                CommonLogger.LogEntityError(cachedId, "Failed to cancel cached pipeline run for WorkspaceId={workspaceId}, WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}. Error: {exception}",
                    workspace.WorkspaceId, workspace.WorkspaceName, workspace.ResourceGroupName, workspace.SubscriptionId, ex);
                throw;
            }
        }

        public virtual async Task<QueryResults<CachedPipelineRunEntity>> GetTopKCachedPipelineRunsAsync(int k, WorkspaceIdentity workspace, string continuationToken = null, int? maxCount = 100)
        {
            try
            {
                var queryResults = await CosmosDbRetryHelper.ExecuteCosmosDbWithRetry(
                        func: async () =>
                        {
                            using (var cts = new CancellationTokenSource(_requestTimeout))
                            {
                                return await _cachedContainer.ExecuteQueryNonStorableAsync<CachedPipelineRunEntity>(
                                    partitionKey: workspace.WorkspaceId,
                                    query: Constants.GetTopKCachedPipelineRunsQuery.Replace("{k}", k.ToString()), // Sort by CachedTime in ascending order.
                                    continuationToken: continuationToken,
                                    takeCount: maxCount,
                                    cancellationToken: cts.Token);
                            }
                        },
                        retryPolicy: _retryPolicy).ConfigureAwait(false);

                CommonLogger.LogEntityInfo(workspace.WorkspaceId, "Get {CachedCount} of top {k} cached pipeline run with continuation token successfully for WorkspaceId={workspaceId}, WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}",
                    queryResults.NonStorableResults.Count(), k, workspace.WorkspaceId, workspace.WorkspaceName, workspace.ResourceGroupName, workspace.SubscriptionId);
                return queryResults;
            }
            catch (Exception ex)
            {
                CommonLogger.LogEntityError(workspace.WorkspaceId, "Failed to get top {k} cached pipeline run with continuation token for WorkspaceId={workspaceId}, WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId} with error: {exception}",
                    k, workspace.WorkspaceId, workspace.WorkspaceName, workspace.ResourceGroupName, workspace.SubscriptionId, ex);
                throw;
            }
        }

        public virtual async Task<QueryResults<CachedPipelineRunEntity>> GetAllCachedPipelineRunsAsync(WorkspaceIdentity workspace, string continuationToken = null, int? maxCount = 100)
        {
            try
            {
                var queryResults = await CosmosDbRetryHelper.ExecuteCosmosDbWithRetry(
                        func: async () =>
                        {
                            using (var cts = new CancellationTokenSource(_requestTimeout))
                            {
                                return await _cachedContainer.GetPartitionWithCountinuationTokenAsync<CachedPipelineRunEntity>(
                                partitionKey: workspace.WorkspaceId,
                                continuationToken: continuationToken,
                                takeCount: maxCount,
                                cancellationToken: cts.Token);
                            }
                        },
                        retryPolicy: _retryPolicy).ConfigureAwait(false);

                CommonLogger.LogEntityInfo(workspace.WorkspaceId, "Get {cacheCount} cached pipeline runs successfully for WorkspaceId={workspaceId}, WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}",
                    queryResults.Results.Count(), workspace.WorkspaceId, workspace.WorkspaceName, workspace.ResourceGroupName, workspace.SubscriptionId);
                return queryResults;
            }
            catch (Exception ex)
            {
                CommonLogger.LogEntityError(workspace.WorkspaceId, "Failed to get all cached pipeline runs for WorkspaceId={workspaceId}, WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}. Error: {exception}",
                    workspace.WorkspaceId, workspace.WorkspaceName, workspace.ResourceGroupName, workspace.SubscriptionId, ex);
                throw;
            }
        }

        public virtual async Task<bool> HasCachedPipelineRunAsync(WorkspaceIdentity workspace)
        {
            try
            {
                var hasCached = await CosmosDbRetryHelper.ExecuteCosmosDbWithRetry(
                        func: async () =>
                        {
                            using (var cts = new CancellationTokenSource(_requestTimeout))
                            {
                                var queryResult = await _cachedContainer.ExecuteQueryNonStorableAsync<int>(
                                    partitionKey: workspace.WorkspaceId,
                                    query: Constants.TotalEntityCountQuery,
                                    continuationToken: null,
                                    cancellationToken: cts.Token);
                                // There will be only one result for count query.
                                int currentCachedCount = queryResult.NonStorableResults.First();

                                var metricsDimensions = new Dictionary<string, string>
                                {
                                    { nameof(workspace.WorkspaceName), workspace.WorkspaceName },
                                    { nameof(workspace.WorkspaceId), workspace.WorkspaceId },
                                    { nameof(workspace.SubscriptionId), workspace.SubscriptionId },
                                    { nameof(workspace.ResourceGroupName), workspace.ResourceGroupName },
                                };
                                _counterManager.GetNumberCounter(Constants.CachedPipelineRunNumberCounter, customDimensions: metricsDimensions).Set(currentCachedCount);
                                CommonLogger.LogEntityInfo(workspace.WorkspaceId, "Workspace has {CachedCount} cached pipeline run. WorkspaceId={workspaceId}, WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}",
                                    currentCachedCount, workspace.WorkspaceId, workspace.WorkspaceName, workspace.ResourceGroupName, workspace.SubscriptionId);

                                return currentCachedCount > 0;
                            }
                        },
                        retryPolicy: _retryPolicy).ConfigureAwait(false);

                CommonLogger.LogEntityInfo(workspace.WorkspaceId, "Cached pipeline run exsit {hasCached} for WorkspaceId={workspaceId}, WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}",
                    hasCached, workspace.WorkspaceId, workspace.WorkspaceName, workspace.ResourceGroupName, workspace.SubscriptionId);
                return hasCached;
            }
            catch (Exception ex)
            {
                CommonLogger.LogEntityError(workspace.WorkspaceId, "Failed to check if any cached pipeline run exist for WorkspaceId={workspaceId}, WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}. Error: {exception}",
                    workspace.WorkspaceId, workspace.WorkspaceName, workspace.ResourceGroupName, workspace.SubscriptionId, ex);
                throw;
            }
        }
    }
}
