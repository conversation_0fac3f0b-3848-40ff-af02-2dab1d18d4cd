﻿// <copyright file="WorkspacePipelineRunQuotaUsageStats.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Microsoft.Aether.DataContracts.Entity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Microsoft.Aether.QuotaService
{
    public class WorkspacePipelineRunQuotaUsageStats
    {
        public WorkspaceIdentity Workspace { get; set; }

        public int QuotaUsageCount { get; set; }

        public int SoftQuotaUsageCount { get; set; }

        public int RootPipelineRunQuotaUsageCount { get; set; }

        public int RootPipelineRunSoftQuotaUsageCount { get; set; }

        public int SubGraphPipelineRunQuotaUsageCount { get; set; }

        public int SubGraphPipelineRunSoftQuotaUsageCount { get; set; }
    }
}
