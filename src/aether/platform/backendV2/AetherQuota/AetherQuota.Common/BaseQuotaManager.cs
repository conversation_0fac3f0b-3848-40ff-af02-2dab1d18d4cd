﻿// <copyright file="BaseQuotaManager.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Aether.BackendCommon.Configuration;
using Microsoft.Aether.BlueBox.Storage;
using Microsoft.Aether.DataContracts.Entity;
using Microsoft.Aether.QuotaService.Shared;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.RelInfra.Common.RetryExecution.Policy;
using Microsoft.RelInfra.Instrumentation.Logging;
using Microsoft.RelInfra.Storage;

namespace Microsoft.Aether.QuotaService
{
    public abstract class BaseQuotaManager<TQuota, TUsage> : IQuotaManager<TQuota, TUsage>
        where TQuota : QuotaLimitEntity
        where TUsage : QuotaUsageEntity
    {
        protected readonly QuotaType _quotaType;
        protected readonly QuotaServiceConfig _serviceConfig;
        protected readonly IQueryableDbStorage _limitContainer;
        protected readonly IQueryableDbStorage _usageContainer;

        protected IRetryPolicy _retryPolicy;
        protected TimeSpan _requestTimeout;

        // MemoryCache maximum entry (workspaces) number. A quota limit object takes less than 1KB memory.
        private const int _cacheSizeLimit = 10000;
        // MemoryCache entry expiration time. We should use AbsoluteExpirationRelativeToNow for workspace quota limit
        // so the cache won't be stale and latest configuration is populated.
        private readonly TimeSpan _cacheExpirationTime = TimeSpan.FromMinutes(10);
        private readonly MemoryCache _workspaceQuotaLimitCache;

        public BaseQuotaManager(
            QuotaType quotaType,
            QuotaServiceConfig config,
            IQueryableDbStorage limitContainer,
            IQueryableDbStorage usageContainer,
            IRetryPolicy retryPolicy = null,
            TimeSpan? requestTimeout = null)
        {
            _quotaType = quotaType;
            _serviceConfig = config;
            _limitContainer = limitContainer;
            _usageContainer = usageContainer;
            _retryPolicy = retryPolicy ?? new ExponentialBackoffRetryPolicy();
            _requestTimeout = requestTimeout ?? config.RequestTimeout;
            _workspaceQuotaLimitCache = new MemoryCache(new MemoryCacheOptions { SizeLimit = _cacheSizeLimit });
        }

        /// <summary>
        /// Get the PartitionKey of quota entity.
        /// </summary>
        public abstract string GetQuotaPartitionKey(WorkspaceIdentity workspace);

        /// <summary>
        /// Get the Id of quota entity.
        /// </summary>
        public abstract string GetQuotaId(WorkspaceIdentity workspace);

        /// <summary>
        /// Get the PartitionKey of resource usage entity.
        /// </summary>
        public abstract string GetUsagePartitionKey(WorkspaceIdentity workspace);

        /// <summary>
        /// Get the Id of resource usage entity.
        /// </summary>
        public abstract string GetUsageId(TUsage resource);

        /// <summary>
        /// Get the default quota limit entity of the workspace.
        /// </summary>
        public abstract TQuota GetDefaultQuotaLimit(WorkspaceIdentity workspace);

        public virtual async Task<QuotaResponse> AcquireQuotaAsync(WorkspaceIdentity workspace, TUsage resource)
        {
            CommonLogger.LogEntityInfo(resource.Id, "Try to acquire quota. AcquireCount={acquireCount}, QuotaType={quotaType}, WorkspaceId={workspaceId}, WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}",
                resource.UsageCount, _quotaType, workspace.WorkspaceId, workspace.WorkspaceName, workspace.ResourceGroupName, workspace.SubscriptionId);
            try
            {
                var quotaResponse = await CosmosDbRetryHelper.ExecuteCosmosDbWithRetry(
                    func: async () =>
                    {
                        int quotaMaxLimit = await GetQuotaLimitAsync(workspace);

                        // It's possible that concurrent acquire requests get current quota count at same time and find quota available and reserve quota,
                        // which may cause real usage count exceeds quota limit a little.
                        // We allow this count deviation temporarily. And next acquire requests will get latest quota count correctly and not affected.
                        int currentQuotaCount = await CalculateQuotaCountFromUsagesAsync(workspace);

                        bool usageExists = await ExistsUsageAsync(workspace, GetUsageId(resource));
                        // If the resource usage entity already exists, no need to acquire and return quota available.
                        if (usageExists)
                        {
                            CommonLogger.LogEntityInfo(resource.Id, "Acquire quota successfully since the resource usage already exists and quota reserved. AcquireCount={acquireCount}, QuotaType={quotaType}, WorkspaceId={workspaceId}, WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}",
                                resource.UsageCount, _quotaType, workspace.WorkspaceId, workspace.WorkspaceName, workspace.ResourceGroupName, workspace.SubscriptionId);

                            return new QuotaResponse
                            {
                                IsQuotaAvailable = true,
                                CurrentUsageCount = currentQuotaCount,
                                MaxLimit = quotaMaxLimit,
                            };
                        }

                        // Exceed max quota limit.
                        if (currentQuotaCount + resource.UsageCount > quotaMaxLimit)
                        {
                            CommonLogger.LogEntityWarning(resource.Id, "Failed to acquire quota due to exceeded quota limit. CurrentQuotaCount={quotaCount}, QuotaLimit={quotaLimit}, QuotaType={quotaType}, WorkspaceId={workspaceId}, WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}",
                                currentQuotaCount, quotaMaxLimit, _quotaType, workspace.WorkspaceId, workspace.WorkspaceName, workspace.ResourceGroupName, workspace.SubscriptionId);

                            return new QuotaResponse
                            {
                                IsQuotaAvailable = false,
                                CurrentUsageCount = currentQuotaCount,
                                MaxLimit = quotaMaxLimit,
                            };
                        }

                        // Add resource usage entity to reserve quota.
                        await AddUsageAsync(workspace, resource);

                        currentQuotaCount += resource.UsageCount;
                        CommonLogger.LogEntityInfo(resource.Id, "Acquire quota successfully. AcquireCount={acquireCount}, QuotaType={quotaType}, WorkspaceId={workspaceId}, WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}",
                            resource.UsageCount, _quotaType, workspace.WorkspaceId, workspace.WorkspaceName, workspace.ResourceGroupName, workspace.SubscriptionId);
                        return new QuotaResponse
                        {
                            IsQuotaAvailable = true,
                            CurrentUsageCount = currentQuotaCount,
                            MaxLimit = quotaMaxLimit,
                        };
                    },
                    retryPolicy: _retryPolicy).ConfigureAwait(false);

                return quotaResponse;
            }
            catch (Exception ex)
            {
                CommonLogger.LogEntityError(resource.Id, "Failed to acquire quota. AcquireCount={acquireCount}, QuotaType={quotaType}, WorkspaceId={workspaceId}, WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}. Error: {exception}",
                   resource.UsageCount, _quotaType, workspace.WorkspaceId, workspace.WorkspaceName, workspace.ResourceGroupName, workspace.SubscriptionId, ex);
                throw;
            }
        }

        public virtual async Task<bool> ReleaseQuotaAsync(WorkspaceIdentity workspace, TUsage resource)
        {
            CommonLogger.LogEntityInfo(resource.Id, "Try to release quota. ReleaseCount={releaseCount}, QuotaType={quotaType}, WorkspaceId={workspaceId}, WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}",
                resource.UsageCount, _quotaType, workspace.WorkspaceId, workspace.WorkspaceName, workspace.ResourceGroupName, workspace.SubscriptionId);
            try
            {
                return await CosmosDbRetryHelper.ExecuteCosmosDbWithRetry(
                    func: async () =>
                    {
                        // Remove resource quota usage to release quota.
                        bool deleted = await RemoveUsageAsync(workspace, resource);
                        if (deleted)
                        {
                            CommonLogger.LogEntityInfo(resource.Id, "Release quota successfully. ReleaseCount={releaseCount}, QuotaType={quotaType}, WorkspaceId={workspaceId}, WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}",
                               resource.UsageCount, _quotaType, workspace.WorkspaceId, workspace.WorkspaceName, workspace.ResourceGroupName, workspace.SubscriptionId);
                        }
                        else
                        {
                            CommonLogger.LogEntityInfo(resource.Id, "Failed to release quota because the resource usage doesn't exist. QuotaType={quotaType}, WorkspaceId={workspaceId}, WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}",
                                _quotaType, workspace.WorkspaceId, workspace.WorkspaceName, workspace.ResourceGroupName, workspace.SubscriptionId);
                        }

                        return deleted;
                    },
                    retryPolicy: _retryPolicy).ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                CommonLogger.LogEntityError(resource.Id, "Failed to release quota. QuotaType={quotaType}, WorkspaceId={workspaceId}, WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}. Error: {exception}",
                    _quotaType, workspace.WorkspaceId, workspace.WorkspaceName, workspace.ResourceGroupName, workspace.SubscriptionId, ex);
                throw;
            }
        }

        public virtual async Task<QuotaResponse> GetCurrentQuotaAsync(WorkspaceIdentity workspace)
        {
            try
            {
                int quotaMaxLimit = await GetQuotaLimitAsync(workspace);
                int currentQuotaCount = await CalculateQuotaCountFromUsagesAsync(workspace);
                bool quotaAvailable = currentQuotaCount < quotaMaxLimit;
                CommonLogger.LogEntityInfo(GetQuotaId(workspace), "Get current quota successfully. QuotaType={quotaType}, WorkspaceId={workspaceId}, WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}",
                    _quotaType, workspace.WorkspaceId, workspace.WorkspaceName, workspace.ResourceGroupName, workspace.SubscriptionId);
                return new QuotaResponse
                {
                    IsQuotaAvailable = quotaAvailable,
                    CurrentUsageCount = currentQuotaCount,
                    MaxLimit = quotaMaxLimit,
                };
            }
            catch (Exception ex)
            {
                CommonLogger.LogEntityError(GetQuotaId(workspace), "Failed to get current quota. QuotaType={quotaType}, WorkspaceId={workspaceId}, WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}. Error: {exception}",
                    _quotaType, workspace.WorkspaceId, workspace.WorkspaceName, workspace.ResourceGroupName, workspace.SubscriptionId, ex);
                throw;
            }
        }

        public virtual async Task<int> CalculateQuotaCountFromUsagesAsync(WorkspaceIdentity workspace)
        {
            try
            {
                int currentQuotaCount = await CosmosDbRetryHelper.ExecuteCosmosDbWithRetry(
                       func: async () =>
                       {
                           using (var cts = new CancellationTokenSource(_requestTimeout))
                           {
                               // Calculate the total usage count of the workspace.
                               var queryResult = await _usageContainer.ExecuteQueryNonStorableAsync<int>(
                                   partitionKey: GetUsagePartitionKey(workspace),
                                   query: Constants.TotalQuotaUsageCountQuery,
                                   continuationToken: null,
                                   cancellationToken: cts.Token);
                               // There will be only one result for count query.
                               int quotaUsageCount = queryResult.NonStorableResults.First();
                               return quotaUsageCount;
                           }
                       },
                       retryPolicy: _retryPolicy).ConfigureAwait(false);

                CommonLogger.LogEntityInfo(GetQuotaId(workspace), "Calculate quota count from usage entities successfully. CurrentQuotaCount={quotaCount}, QuotaType={quotaType}, WorkspaceId={workspaceId}, WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}",
                    currentQuotaCount, _quotaType, workspace.WorkspaceId, workspace.WorkspaceName, workspace.ResourceGroupName, workspace.SubscriptionId);
                return currentQuotaCount;
            }
            catch (Exception ex)
            {
                CommonLogger.LogEntityError(GetQuotaId(workspace), "Failed to calculate quota count from usage entities. QuotaType={quotaType}, WorkspaceId={workspaceId}, WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}. Error: {exception}",
                    _quotaType, workspace.WorkspaceId, workspace.WorkspaceName, workspace.ResourceGroupName, workspace.SubscriptionId, ex);
                throw;
            }
        }

        public virtual async Task<TQuota> SetQuotaLimitAsync(WorkspaceIdentity workspace, int maxLimit)
        {
            CommonLogger.LogEntityInfo(GetQuotaId(workspace), "Try to update quota limit. QuotaLimit={quotaLimit}, QuotaType={quotaType}, WorkspaceId={workspaceId}, WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}",
                maxLimit, _quotaType, workspace.WorkspaceId, workspace.WorkspaceName, workspace.ResourceGroupName, workspace.SubscriptionId);

            try
            {
                var newQuotaLimit = await VerifyAndGetNewQuotaLimitAsync(workspace, maxLimit);

                return await CosmosDbRetryHelper.ExecuteCosmosDbWithRetry(
                   func: async () =>
                   {
                       using (var cts = new CancellationTokenSource(_requestTimeout))
                       {
                           var updatedQuotaLimit = await _limitContainer.PutAsync(GetQuotaPartitionKey(workspace), GetQuotaId(workspace), new Storable<TQuota>(newQuotaLimit), cancellation: cts.Token);

                           CommonLogger.LogEntityInfo(GetQuotaId(workspace), "Update quota limit successfully. QuotaLimit={quotaLimit}, QuotaType={quotaType}, WorkspaceId={workspaceId}, WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}",
                               updatedQuotaLimit.Entity.MaxLimit, _quotaType, workspace.WorkspaceId, workspace.WorkspaceName, workspace.ResourceGroupName, workspace.SubscriptionId);
                           return updatedQuotaLimit.Entity;
                       }
                   },
                   retryPolicy: _retryPolicy).ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                CommonLogger.LogEntityError(GetQuotaId(workspace), "Failed to update quota limit. QuotaType={quotaType}, WorkspaceId={workspaceId}, WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}. Error: {exception}",
                    _quotaType, workspace.WorkspaceId, workspace.WorkspaceName, workspace.ResourceGroupName, workspace.SubscriptionId, ex);
                throw;
            }
        }

        public virtual async Task<IEnumerable<TQuota>> GetAllQuotaLimitsAsync(int? maxCount = int.MaxValue)
        {
            try
            {
                var quotaLimits = await CosmosDbRetryHelper.ExecuteCosmosDbWithRetry(
                    func: async () =>
                    {
                        using (var cts = new CancellationTokenSource(_requestTimeout))
                        {
                            return await _limitContainer.GetAllAsync<TQuota>(takeCount: maxCount, cancellationToken: cts.Token);
                        }
                    },
                    retryPolicy: _retryPolicy).ConfigureAwait(false);

                CommonLogger.LogInfo("Get all quota limits successfully. QuotaNumber={quotaNumber}, QuotaType={quotaType}", quotaLimits.Count(), _quotaType);
                return quotaLimits.Select(item => item.Entity);
            }
            catch (Exception ex)
            {
                CommonLogger.LogError("Failed to get all quota limits. QuotaType={quotaType}. Error: {exception}", _quotaType, ex);
                throw;
            }
        }

        public virtual async Task<QueryResults<TQuota>> GetAllQuotaLimitsWithContinuationTokenAsync(string continuationToken = null, int? maxCount = 100)
        {
            try
            {
                var quotaList = await CosmosDbRetryHelper.ExecuteCosmosDbWithRetry(
                    func: async () =>
                    {
                        using (var cts = new CancellationTokenSource(_requestTimeout))
                        {
                            return await _limitContainer.GetAllWithContinuationTokenAsync<TQuota>(continuationToken: continuationToken, takeCount: maxCount, cancellationToken: cts.Token);
                        }
                    },
                    retryPolicy: _retryPolicy).ConfigureAwait(false);

                CommonLogger.LogInfo("Get all quota limits with continuation token successfully. QuotaNumber={quotaNumber}, QuotaType={quotaType}", quotaList.Results.Count(), _quotaType);
                return quotaList;
            }
            catch (Exception ex)
            {
                CommonLogger.LogError("Failed to get all quota limits with continuation token. QuotaType={quotaType}. Error: {exception}", _quotaType, ex);
                throw;
            }
        }

        public virtual async Task AddUsageAsync(WorkspaceIdentity workspace, TUsage resource)
        {
            try
            {
                await CosmosDbRetryHelper.ExecuteCosmosDbWithRetry(
                    func: async () =>
                    {
                        using (var cts = new CancellationTokenSource(_requestTimeout))
                        {
                            return await _usageContainer.AddAsync(GetUsagePartitionKey(workspace), GetUsageId(resource), new Storable<TUsage>(resource), cancellationToken: cts.Token);
                        }
                    },
                    retryPolicy: _retryPolicy).ConfigureAwait(false);

                CommonLogger.LogEntityInfo(GetUsageId(resource), "Add resource usage successfully. QuotaType={quotaType}, WorkspaceId={workspaceId}, WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}",
                    _quotaType, workspace.WorkspaceId, workspace.WorkspaceName, workspace.ResourceGroupName, workspace.SubscriptionId);
            }
            catch (Exception ex)
            {
                CommonLogger.LogEntityError(GetUsageId(resource), "Failed to add resource usage. QuotaType={quotaType}, WorkspaceId={workspaceId}, WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}. Error: {exception}",
                    _quotaType, workspace.WorkspaceId, workspace.WorkspaceName, workspace.ResourceGroupName, workspace.SubscriptionId, ex);
                throw;
            }
        }

        public virtual async Task UpdateUsageAsync(WorkspaceIdentity workspace, TUsage resource)
        {
            try
            {
                await CosmosDbRetryHelper.ExecuteCosmosDbWithRetry(
                    func: async () =>
                    {
                        using (var cts = new CancellationTokenSource(_requestTimeout))
                        {
                            return await _usageContainer.UpdateAsync(GetUsagePartitionKey(workspace), GetUsageId(resource), new Storable<TUsage>(resource), cancellationToken: cts.Token);
                        }
                    },
                    retryPolicy: _retryPolicy).ConfigureAwait(false);

                CommonLogger.LogEntityInfo(GetUsageId(resource), "Update resource usage successfully. QuotaType={quotaType}, WorkspaceId={workspaceId}, WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}",
                    _quotaType, workspace.WorkspaceId, workspace.WorkspaceName, workspace.ResourceGroupName, workspace.SubscriptionId);
            }
            catch (Exception ex)
            {
                CommonLogger.LogEntityError(GetUsageId(resource), "Failed to update resource usage. QuotaType={quotaType}, WorkspaceId={workspaceId}, WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}. Error: {exception}",
                    _quotaType, workspace.WorkspaceId, workspace.WorkspaceName, workspace.ResourceGroupName, workspace.SubscriptionId, ex);
                throw;
            }
        }

        public virtual async Task<bool> RemoveUsageAsync(WorkspaceIdentity workspace, TUsage resource)
        {
            try
            {
                bool deleted = await CosmosDbRetryHelper.ExecuteCosmosDbWithRetry(
                    func: async () =>
                    {
                        using (var cts = new CancellationTokenSource(_requestTimeout))
                        {
                            return await _usageContainer.TryDeleteAsync(GetUsagePartitionKey(workspace), GetUsageId(resource), cancellationToken: cts.Token);
                        }
                    },
                    retryPolicy: _retryPolicy).ConfigureAwait(false);

                if (deleted)
                {
                    CommonLogger.LogEntityInfo(GetUsageId(resource), "Remove resource usage successfully. QuotaType={quotaType}, WorkspaceId={workspaceId}, WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}",
                        _quotaType, workspace.WorkspaceId, workspace.WorkspaceName, workspace.ResourceGroupName, workspace.SubscriptionId);
                }
                else
                {
                    CommonLogger.LogEntityInfo(GetUsageId(resource), "Failed to remove resource usage, the resource may already be deleted and not exist. QuotaType={quotaType}, WorkspaceId={workspaceId}, WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}",
                        _quotaType, workspace.WorkspaceId, workspace.WorkspaceName, workspace.ResourceGroupName, workspace.SubscriptionId);
                }

                return deleted;
            }
            catch (Exception ex)
            {
                CommonLogger.LogEntityError(GetUsageId(resource), "Failed to remove resource usage. QuotaType={quotaType}, WorkspaceId={workspaceId}, WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}. Error: {exception}",
                    _quotaType, workspace.WorkspaceId, workspace.WorkspaceName, workspace.ResourceGroupName, workspace.SubscriptionId, ex);
                throw;
            }
        }

        public virtual async Task<bool> ExistsUsageAsync(WorkspaceIdentity workspace, string resourceId)
        {
            if (string.IsNullOrEmpty(resourceId))
            {
                return false;
            }

            return await CosmosDbRetryHelper.ExecuteCosmosDbWithRetry(
                func: async () =>
                {
                    using (var cts = new CancellationTokenSource(_requestTimeout))
                    {
                        return await _usageContainer.ExistsAsync(GetUsagePartitionKey(workspace), resourceId, cancellationToken: cts.Token);
                    }
                },
                retryPolicy: _retryPolicy).ConfigureAwait(false);
        }

        public virtual async Task<bool> RemoveQuotaLimitAsync(WorkspaceIdentity workspace)
        {
            try
            {
                bool deleted = await CosmosDbRetryHelper.ExecuteCosmosDbWithRetry(
                    func: async () =>
                    {
                        using (var cts = new CancellationTokenSource(_requestTimeout))
                        {
                            return await _limitContainer.TryDeleteAsync(GetQuotaPartitionKey(workspace), GetQuotaId(workspace), cancellationToken: cts.Token);
                        }
                    },
                    retryPolicy: _retryPolicy).ConfigureAwait(false);

                if (deleted)
                {
                    CommonLogger.LogEntityInfo(GetQuotaId(workspace), "Remove quota limit successfully. QuotaType={quotaType}, WorkspaceId={workspaceId}, WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}",
                        _quotaType, workspace.WorkspaceId, workspace.WorkspaceName, workspace.ResourceGroupName, workspace.SubscriptionId);
                }
                else
                {
                    CommonLogger.LogEntityInfo(GetQuotaId(workspace), "Failed to remove quota limit, it may already be deleted and not exist. QuotaType={quotaType}, WorkspaceId={workspaceId}, WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}",
                        _quotaType, workspace.WorkspaceId, workspace.WorkspaceName, workspace.ResourceGroupName, workspace.SubscriptionId);
                }

                return deleted;
            }
            catch (Exception ex)
            {
                CommonLogger.LogEntityError(GetQuotaId(workspace), "Failed to remove quota limit. QuotaType={quotaType}, WorkspaceId={workspaceId}, WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}. Error: {exception}",
                    _quotaType, workspace.WorkspaceId, workspace.WorkspaceName, workspace.ResourceGroupName, workspace.SubscriptionId, ex);
                throw;
            }
        }

        public virtual async Task<IEnumerable<TUsage>> GetAllUsagesAsync(WorkspaceIdentity workspace = null, int? maxCount = int.MaxValue)
        {
            try
            {
                IEnumerable<Storable<TUsage>> quotaUsages;
                if (workspace == null)
                {
                    quotaUsages = await CosmosDbRetryHelper.ExecuteCosmosDbWithRetry(
                        func: async () =>
                        {
                            using (var cts = new CancellationTokenSource(_requestTimeout))
                            {
                                return await _usageContainer.GetAllAsync<TUsage>(takeCount: maxCount, cancellationToken: cts.Token);
                            }
                        },
                        retryPolicy: _retryPolicy).ConfigureAwait(false);

                    CommonLogger.LogInfo("Get all quota usages from all workspaces successfully. UsageCount={usageCount}, QuotaType={quotaType}", quotaUsages.Count(), _quotaType);
                }
                else
                {
                    quotaUsages = await CosmosDbRetryHelper.ExecuteCosmosDbWithRetry(
                        func: async () =>
                        {
                            using (var cts = new CancellationTokenSource(_requestTimeout))
                            {
                                return await _usageContainer.GetPartitionAsync<TUsage>(GetUsagePartitionKey(workspace), takeCount: maxCount, cancellationToken: cts.Token);
                            }
                        },
                        retryPolicy: _retryPolicy).ConfigureAwait(false);

                    CommonLogger.LogInfo("Get all quota usages from the workspace successfully. UsageCount={usageCount}, QuotaType={quotaType}, WorkspaceId={workspaceId}, WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}",
                        quotaUsages.Count(), _quotaType, workspace.WorkspaceId, workspace.WorkspaceName, workspace.ResourceGroupName, workspace.SubscriptionId);
                }

                return quotaUsages.Select(resource => resource.Entity);
            }
            catch (Exception ex)
            {
                CommonLogger.LogError("Failed to get all quota usages details. Error: {exception}", ex);
                throw;
            }
        }

        public virtual async Task<QueryResults<TUsage>> GetAllUsagesWithContinuationTokenAsync(WorkspaceIdentity workspace = null, string continuationToken = null, int? maxCount = 100)
        {
            try
            {
                QueryResults<TUsage> quotaUsages;
                if (workspace == null)
                {
                    quotaUsages = await CosmosDbRetryHelper.ExecuteCosmosDbWithRetry(
                        func: async () =>
                        {
                            using (var cts = new CancellationTokenSource(_requestTimeout))
                            {
                                return await _usageContainer.GetAllWithContinuationTokenAsync<TUsage>(
                                    continuationToken: continuationToken,
                                    takeCount: maxCount,
                                    cancellationToken: cts.Token);
                            }
                        },
                        retryPolicy: _retryPolicy).ConfigureAwait(false);

                    CommonLogger.LogInfo("Get all quota usages from all workspaces with continuation token successfully. UsageCount={usageCount}, QuotaType={quotaType}", quotaUsages.Results.Count(), _quotaType);
                }
                else
                {
                    quotaUsages = await CosmosDbRetryHelper.ExecuteCosmosDbWithRetry(
                        func: async () =>
                        {
                            using (var cts = new CancellationTokenSource(_requestTimeout))
                            {
                                return await _usageContainer.GetPartitionWithCountinuationTokenAsync<TUsage>(
                                    partitionKey: GetUsagePartitionKey(workspace),
                                    continuationToken: continuationToken,
                                    takeCount: maxCount,
                                    cancellationToken: cts.Token);
                            }
                        },
                        retryPolicy: _retryPolicy).ConfigureAwait(false);

                    CommonLogger.LogInfo("Get all quota usages from the workspace with continuation token successfully. UsageCount={usageCount}, QuotaType={quotaType}, WorkspaceId={workspaceId}, WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}",
                        quotaUsages.Results.Count(), _quotaType, workspace.WorkspaceId, workspace.WorkspaceName, workspace.ResourceGroupName, workspace.SubscriptionId);
                }

                return quotaUsages;
            }
            catch (Exception ex)
            {
                CommonLogger.LogError("Failed to get all quota usages with continuation token. Error: {exception}", ex);
                throw;
            }
        }

        /// <summary>
        /// Verify whether can update the workspace quota limit to the new maximum limit.
        /// Return new quota limit if passed validation.
        /// </summary>
        protected virtual Task<TQuota> VerifyAndGetNewQuotaLimitAsync(WorkspaceIdentity workspace, int maxLimit)
        {
            if (maxLimit < 0)
            {
                throw new ArgumentException($"Quota limit {maxLimit} can not be less than 0.", nameof(maxLimit));
            }

            var quotaLimit = GetDefaultQuotaLimit(workspace);
            quotaLimit.MaxLimit = maxLimit;
            return Task.FromResult(quotaLimit);
        }

        protected virtual async Task<int> GetQuotaLimitAsync(WorkspaceIdentity workspace)
        {
            var quotaLimit = await GetOrUseDefaultQuotaLimitInternalAsync(workspace);
            CommonLogger.LogEntityInfo(GetQuotaId(workspace), "Get quota limit successfully. QuotaLimit={quotaLimit}, QuotaType={quotaType}, WorkspaceId={workspaceId}, WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}",
                quotaLimit.MaxLimit, _quotaType, workspace.WorkspaceId, workspace.WorkspaceName, workspace.ResourceGroupName, workspace.SubscriptionId);

            return quotaLimit.MaxLimit;
        }

        protected async Task<TQuota> GetOrUseDefaultQuotaLimitInternalAsync(WorkspaceIdentity workspace)
        {
            return await CosmosDbRetryHelper.ExecuteCosmosDbWithRetry(
                func: async () =>
                {
                    TQuota quotaLimit;
                    if (!_workspaceQuotaLimitCache.TryGetValue<TQuota>(workspace.WorkspaceId, out quotaLimit))
                    {
                        using (var cts = new CancellationTokenSource(_requestTimeout))
                        {
                            var quotaLimitEntity = await _limitContainer.TryGetAsync<TQuota>(GetQuotaPartitionKey(workspace), GetQuotaId(workspace), cancellationToken: cts.Token);
                            if (quotaLimitEntity != null)
                            {
                                quotaLimit = quotaLimitEntity.Entity;
                            }
                            else
                            {
                                // If no quota limit configuration found in DB, just use default limit.
                                quotaLimit = GetDefaultQuotaLimit(workspace);
                                CommonLogger.LogEntityInfo(GetQuotaId(workspace), "Use default quota limit for the workspace. DefaultQuotaLimit={quotaLimit}, QuotaType={quotaType}, WorkspaceId={workspaceId}, WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}",
                                        quotaLimit.MaxLimit, _quotaType, workspace.WorkspaceId, workspace.WorkspaceName, workspace.ResourceGroupName, workspace.SubscriptionId);
                            }
                        }

                        bool isProdEnvironment = IniFileDimensions.ParseDeployment(Env.DeploymentType()) == K8SEnvType.Prod;
                        // Disable cache in dev environment because service tests update quota limit frequently and rely on latest limit.
                        if (isProdEnvironment)
                        {
                            _workspaceQuotaLimitCache.Set<TQuota>(
                                workspace.WorkspaceId,
                                quotaLimit,
                                new MemoryCacheEntryOptions
                                {
                                    Size = 1,   // Each entry takes 1 size.
                                    AbsoluteExpirationRelativeToNow = _cacheExpirationTime,   // Entry will be evicted after the expiry time from now.
                                });
                        }
                    }

                    CommonLogger.LogEntityInfo(GetQuotaId(workspace), "Get quota limit successfully. QuotaLimit={quotaLimit}, QuotaType={quotaType}, WorkspaceId={workspaceId}, WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}",
                        quotaLimit.MaxLimit, _quotaType, workspace.WorkspaceId, workspace.WorkspaceName, workspace.ResourceGroupName, workspace.SubscriptionId);
                    return quotaLimit;
                },
                retryPolicy: _retryPolicy).ConfigureAwait(false);
        }
    }
}
