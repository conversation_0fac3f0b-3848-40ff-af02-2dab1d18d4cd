// <copyright file="ResourceManager.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Aether.QuotaService.Shared;
using Microsoft.RelInfra.Extensions;
using Microsoft.RelInfra.Storage;

namespace Microsoft.Aether.QuotaService
{
    public class ResourceManager
    {
        private readonly IQuotaRoiClient _quotaRoiClient;
        private readonly IAzureBlobStorage _quotaBlobStorage;
        private readonly string _resourceQuotaPrefix;
        private readonly string _blobContainerName;
        private readonly TimeSpan _blobTimeoutInterval;
        private readonly int _parallelismDegree;

        private string GetResourceId(string aggregateResourceId) => $"{_resourceQuotaPrefix}{aggregateResourceId}";

        public ResourceManager(IAzureBlobStorage quotaBlobStorage, string resourceQuotaPrefix, string blobContainerName, TimeSpan blobTimeoutInterval, int parallelismDegree, IQuotaRoiClient quotaRoiClient)
        {
            _quotaBlobStorage = quotaBlobStorage;
            _resourceQuotaPrefix = resourceQuotaPrefix;
            _blobContainerName = blobContainerName;
            _blobTimeoutInterval = blobTimeoutInterval;
            _parallelismDegree = parallelismDegree;
            _quotaRoiClient = quotaRoiClient;
        }

        public async Task<Storable<AggregatedResourcesUsageEntity>> GetAggregatedResourceUsageEntityAsync(string aggregateResourceId)
        {
            if (string.IsNullOrWhiteSpace(aggregateResourceId))
            {
                throw new ArgumentNullException(nameof(aggregateResourceId));
            }

            using (var cancellationTokenSource = new CancellationTokenSource(_blobTimeoutInterval))
            {
                Storable<AggregatedResourcesUsageEntity> teamResourceQuotaStorable = await _quotaBlobStorage.TryGetAsync<AggregatedResourcesUsageEntity>(
                containerName: _blobContainerName,
                resourceId: GetResourceId(aggregateResourceId),
                cancellationToken: cancellationTokenSource.Token);
                return teamResourceQuotaStorable ?? new Storable<AggregatedResourcesUsageEntity>(new AggregatedResourcesUsageEntity()
                {
                    Id = aggregateResourceId
                });
            }
        }

        public async Task PutAggregatedResourceUsageEntityAsync(Storable<AggregatedResourcesUsageEntity> storable)
        {
            if (storable == null)
            {
                throw new ArgumentNullException(nameof(storable));
            }

            string resourceId = GetResourceId(storable.Entity.Id);

            if (await _quotaBlobStorage.ExistsAsync(containerName: _blobContainerName, resourceId: resourceId))
            {
                await _quotaBlobStorage.UpdateAsync(containerName: _blobContainerName, resourceId: resourceId, resource: storable);
            }
            else
            {
                await _quotaBlobStorage.AddAsync(containerName: _blobContainerName, resourceId: resourceId, resource: storable);
            }
        }

        public async Task<IEnumerable<AggregatedResourcesUsageEntity>> GetTotalResouceUsageAsync()
        {
            ConcurrentBag<AggregatedResourcesUsageEntity> quotaEntities = new ConcurrentBag<AggregatedResourcesUsageEntity>();
            Dictionary<string, string> teamInfos = await _quotaRoiClient.GetTeamsAsync();
            using (CancellationTokenSource cts = new CancellationTokenSource(_blobTimeoutInterval))
            {
                await teamInfos.ForEachAsync(_parallelismDegree, async pair =>
                {
                    Storable<AggregatedResourcesUsageEntity> teamQuotaResourceStorable = await _quotaBlobStorage.TryGetAsync<AggregatedResourcesUsageEntity>(
                        containerName: _blobContainerName,
                        resourceId: GetResourceId(pair.Key),
                        cancellationToken: cts.Token);

                    if (teamQuotaResourceStorable?.Entity != null)
                    {
                        teamQuotaResourceStorable.Entity.Name = pair.Value;
                        quotaEntities.Add(teamQuotaResourceStorable.Entity);
                    }
                });
                return quotaEntities;
            }
        }
    }
}