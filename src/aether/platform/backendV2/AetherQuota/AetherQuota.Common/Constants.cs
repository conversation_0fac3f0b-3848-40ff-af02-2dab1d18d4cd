﻿// <copyright file="Constants.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

namespace Microsoft.Aether.QuotaService
{
    public static class Constants
    {
        public const string RoutePrefix = "pipelinequotaservice/v{version:apiVersion}/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.MachineLearningServices/workspaces/{workspaceName}/[controller]";

        // CosmosDB query to calculate the total usage count from all saved QuotaUsageEntity of a workspace.
        public const string TotalQuotaUsageCountQuery = "SELECT VALUE SUM(c.UsageCount) FROM c";

        // CosmosDB query to calculate the soft usage count of a workspace. The `IsSoftQuota` is true if a usage occupies soft quota.
        public const string TotalSoftQuotaUsageCountQuery = "SELECT VALUE SUM(c.UsageCount) FROM c WHERE c.IsSoftQuota=true";

        // CosmosDB query to calculate the total entity count of a workspace.
        public const string TotalEntityCountQuery = "SELECT VALUE COUNT(1) FROM c";

        // CosmosDB query to get top k cached pipeline runs sorting by CachedTime in ascending order from all saved CachedPipelineRunEntity of a workspace.
        public const string GetTopKCachedPipelineRunsQuery = "SELECT TOP {k} * FROM c ORDER BY c.CachedTimeUtc ASC";

        #region Metrics Counter

        public const string AcquireRunningPipelineRunQuotaNumberCounter = "AcquireRunningPipelineRunQuotaNumberCounter";
        public const string AcquireRunningPipelineRunQuotaSuccessNumberCounter = "AcquireRunningPipelineRunQuotaSuccessNumberCounter";
        public const string AcquireRunningPipelineRunQuotaFailureNumberCounter = "AcquireRunningPipelineRunQuotaFailureNumberCounter";
        public const string AcquireRunningPipelineRunQuotaLatencyCounter = "AcquireRunningPipelineRunQuotaLatencyCounter";

        public const string ReleaseRunningPipelineRunQuotaNumberCounter = "ReleaseRunningPipelineRunQuotaNumberCounter";
        public const string ReleaseRunningPipelineRunQuotaSuccessNumberCounter = "ReleaseRunningPipelineRunQuotaSuccessNumberCounter";
        public const string ReleaseRunningPipelineRunQuotaFailureNumberCounter = "ReleaseRunningPipelineRunQuotaFailureNumberCounter";
        public const string ReleaseRunningPipelineRunQuotaLatencyCounter = "ReleaseRunningPipelineRunQuotaLatencyCounter";

        public const string GetRunningPipelineRunQuotaNumberCounter = "GetRunningPipelineRunQuotaNumberCounter";
        public const string GetRunningPipelineRunQuotaSuccessNumberCounter = "GetRunningPipelineRunQuotaSuccessNumberCounter";
        public const string GetRunningPipelineRunQuotaFailureNumberCounter = "GetRunningPipelineRunQuotaFailureNumberCounter";
        public const string GetRunningPipelineRunQuotaLatencyCounter = "GetRunningPipelineRunQuotaLatencyCounter";

        public const string DeleteRunningPipelineRunQuotaLimitNumberCounter = "DeleteRunningPipelineRunQuotaLimitNumberCounter";
        public const string DeleteRunningPipelineRunQuotaLimitSuccessNumberCounter = "DeleteRunningPipelineRunQuotaLimitSuccessNumberCounter";
        public const string DeleteRunningPipelineRunQuotaLimitFailureNumberCounter = "DeleteRunningPipelineRunQuotaLimitFailureNumberCounter";
        public const string DeleteRunningPipelineRunQuotaLimitLatencyCounter = "DeleteRunningPipelineRunQuotaLimitLatencyCounter";

        public const string DeleteRunningPipelineRunQuotaUsagesNumberCounter = "DeleteRunningPipelineRunQuotaUsagesNumberCounter";
        public const string DeleteRunningPipelineRunQuotaUsagesSuccessNumberCounter = "DeleteRunningPipelineRunQuotaUsagesSuccessNumberCounter";
        public const string DeleteRunningPipelineRunQuotaUsagesFailureNumberCounter = "DeleteRunningPipelineRunQuotaUsagesFailureNumberCounter";
        public const string DeleteRunningPipelineRunQuotaUsagesLatencyCounter = "DeleteRunningPipelineRunQuotaUsagesLatencyCounter";

        public const string ReviseRunningPipelineRunQuotaUsageNumberCounter = "ReviseRunningPipelineRunQuotaUsageNumberCounter";
        public const string ReviseRunningPipelineRunQuotaUsageSuccessNumberCounter = "ReviseRunningPipelineRunQuotaUsageSuccessNumberCounter";
        public const string ReviseRunningPipelineRunQuotaUsageFailureNumberCounter = "ReviseRunningPipelineRunQuotaUsageFailureNumberCounter";
        public const string ReviseRunningPipelineRunQuotaUsageLatencyCounter = "ReviseRunningPipelineRunQuotaUsageLatencyCounter";

        // How many workspaces have completed pipelineruns but failed to release quota case which causing incorrect quota count. Detected and fixed by periodical revision task.
        public const string RunningPipelineRunQuotaFixedWorkspaceNumberCounter = "RunningPipelineRunQuotaFixedWorkspaceNumberCounter";
        // How many saved pipelineruns are actually completed but failed to remove/release quota. Detected by periodical revision task.
        public const string RunningPipelineRunQuotaFailedToReleaseNumberCounter = "RunningPipelineRunQuotaFailedToReleaseNumberCounter";
        // How long each pipelinerun occupys a quota, used to measure long running pipelineruns or stuck pipelineruns.
        public const string RunningPipelineRunQuotaOccupyTimeCounter = "RunningPipelineRunQuotaOccupyTimeCounter";

        public const string RunningPipelineRunQuotaUsageNumberCounter = "RunningPipelineRunQuotaUsageNumberCounter";
        public const string RunningPipelineRunSoftQuotaUsageNumberCounter = "RunningPipelineRunSoftQuotaUsageNumberCounter";
        public const string RunningPipelineRunQuotaUsagePercentageCounter = "RunningPipelineRunQuotaUsagePercentageCounter";
        public const string RunningPipelineRunSoftQuotaUsagePercentageCounter = "RunningPipelineRunSoftQuotaUsagePercentageCounter";
        public const string RootPipelineRunQuotaUsageNumberCounter = "RootPipelineRunQuotaUsageNumberCounter";
        public const string RootPipelineRunSoftQuotaUsageNumberCounter = "RootPipelineRunSoftQuotaUsageNumberCounter";
        public const string SubGraphPipelineRunQuotaUsageNumberCounter = "SubGraphPipelineRunQuotaUsageNumberCounter";
        public const string SubGraphPipelineRunSoftQuotaUsageNumberCounter = "SubGraphPipelineRunSoftQuotaUsageNumberCounter";

        public const string AcquireRunningPipelineRunSoftQuotaNumberCounter = "AcquireRunningPipelineRunSoftQuotaNumberCounter";
        public const string AcquireRunningPipelineRunSoftQuotaSuccessNumberCounter = "AcquireRunningPipelineRunSoftQuotaSuccessNumberCounter";
        public const string AcquireRunningPipelineRunSoftQuotaFailureNumberCounter = "AcquireRunningPipelineRunSoftQuotaFailureNumberCounter";
        public const string AcquireRunningPipelineRunSoftQuotaLatencyCounter = "AcquireRunningPipelineRunSoftQuotaLatencyCounter";

        public const string ReleaseRunningPipelineRunSoftQuotaNumberCounter = "ReleaseRunningPipelineRunSoftQuotaNumberCounter";
        public const string ReleaseRunningPipelineRunSoftQuotaSuccessNumberCounter = "ReleaseRunningPipelineRunSoftQuotaSuccessNumberCounter";
        public const string ReleaseRunningPipelineRunSoftQuotaFailureNumberCounter = "ReleaseRunningPipelineRunSoftQuotaFailureNumberCounter";
        public const string ReleaseRunningPipelineRunSoftQuotaLatencyCounter = "ReleaseRunningPipelineRunSoftQuotaLatencyCounter";

        public const string GetRunningPipelineRunSoftQuotaNumberCounter = "GetRunningPipelineRunSoftQuotaNumberCounter";
        public const string GetRunningPipelineRunSoftQuotaSuccessNumberCounter = "GetRunningPipelineRunSoftQuotaSuccessNumberCounter";
        public const string GetRunningPipelineRunSoftQuotaFailureNumberCounter = "GetRunningPipelineRunSoftQuotaFailureNumberCounter";
        public const string GetRunningPipelineRunSoftQuotaLatencyCounter = "GetRunningPipelineRunSoftQuotaLatencyCounter";

        public const string AddCachedPipelineRunNumberCounter = "AddCachedPipelineRunNumberCounter";
        public const string AddCachedPipelineRunSuccessNumberCounter = "AddCachedPipelineRunSuccessNumberCounter";
        public const string AddCachedPipelineRunFailureNumberCounter = "AddCachedPipelineRunFailureNumberCounter";
        public const string AddCachedPipelineRunLatencyCounter = "AddCachedPipelineRunLatencyCounter";

        public const string DeleteCachedPipelineRunNumberCounter = "DeleteCachedPipelineRunNumberCounter";
        public const string DeleteCachedPipelineRunSuccessNumberCounter = "DeleteCachedPipelineRunSuccessNumberCounter";
        public const string DeleteCachedPipelineRunFailureNumberCounter = "DeleteCachedPipelineRunFailureNumberCounter";
        public const string DeleteCachedPipelineRunLatencyCounter = "DeleteCachedPipelineRunLatencyCounter";

        public const string DeleteAllCachedPipelineRunsNumberCounter = "DeleteAllCachedPipelineRunsNumberCounter";
        public const string DeleteAllCachedPipelineRunsSuccessNumberCounter = "DeleteAllCachedPipelineRunsSuccessNumberCounter";
        public const string DeleteAllCachedPipelineRunsFailureNumberCounter = "DeleteAllCachedPipelineRunsFailureNumberCounter";
        public const string DeleteAllCachedPipelineRunsLatencyCounter = "DeleteAllCachedPipelineRunsLatencyCounter";

        public const string CancelCachedPipelineRunNumberCounter = "CancelCachedPipelineRunNumberCounter";
        public const string CancelCachedPipelineRunSuccessNumberCounter = "CancelCachedPipelineRunSuccessNumberCounter";
        public const string CancelCachedPipelineRunFailureNumberCounter = "CancelCachedPipelineRunFailureNumberCounter";
        public const string CancelCachedPipelineRunLatencyCounter = "CancelCachedPipelineRunLatencyCounter";

        public const string GetAllCachedPipelineRunsNumberCounter = "GetAllCachedPipelineRunsNumberCounter";
        public const string GetAllCachedPipelineRunsSuccessNumberCounter = "GetAllCachedPipelineRunsSuccessNumberCounter";
        public const string GetAllCachedPipelineRunsFailureNumberCounter = "GetAllCachedPipelineRunsFailureNumberCounter";
        public const string GetAllCachedPipelineRunsLatencyCounter = "GetAllCachedPipelineRunsLatencyCounter";

        public const string CachedPipelineRunNumberCounter = "CachedPipelineRunNumberCounter";

        public const string CheckCachedPipelineRunMessageReceivedNumberCounter = "CheckCachedPipelineRunMessageReceivedNumberCounter";
        public const string CheckCachedPipelineRunMessageHandleSuccessNumberCounter = "CheckCachedPipelineRunMessageHandleSuccessNumberCounter";
        public const string CheckCachedPipelineRunMessageHandleFailureNumberCounter = "CheckCachedPipelineRunMessageHandleFailureNumberCounter";
        public const string CheckCachedPipelineRunMessageHandleLatencyCounter = "CheckCachedPipelineRunMessageHandleLatencyCounter";
        public const string CheckCachedPipelineRunMessageLifetime = "CheckCachedPipelineRunMessageLifetime";

        public const string PutBackCachedPipelineRunSuccessNumberCounter = "PutBackCachedPipelineRunSuccessNumberCounter";
        public const string PutBackCachedPipelineRunFailureNumberCounter = "PutBackCachedPipelineRunFailureNumberCounter";

        #endregion
    }
}
