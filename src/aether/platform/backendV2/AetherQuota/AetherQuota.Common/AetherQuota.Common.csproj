<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <ProductVersion>8.0.30703</ProductVersion>
    <TargetFramework>net8.0</TargetFramework>
    <ResolveAssemblyWarnOrErrorOnTargetArchitectureMismatch>None</ResolveAssemblyWarnOrErrorOnTargetArchitectureMismatch>
  </PropertyGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\..\RelInfra\RelInfra.Common.Library\src\Common\RelInfra.Common.csproj" />
    <ProjectReference Include="..\..\..\RelInfra\RelInfra.Instrumentation.Library\src\Microsoft.RelInfra.Instrumentation.csproj" />
    <ProjectReference Include="..\..\..\RelInfra\RelInfra.Storage.Library\src\Storage\RelInfra.Storage.csproj" />
    <ProjectReference Include="..\..\BlueBox\AetherK8SCommon\Microsoft.Aether.BlueBox.AetherK8SCommon.csproj" />
    <ProjectReference Include="..\..\BlueBox\Microsoft.Aether.BlueBox.Storage\Microsoft.Aether.BlueBox.Common.Storage.csproj" />
    <ProjectReference Include="..\..\Clouds\Common\CloudCommon\CloudCommon.csproj" />
    <ProjectReference Include="..\..\shared\Microsoft.Aether.TaggedQueueManager.CloudTags\Microsoft.Aether.TaggedQueueManager.CloudTags.csproj" />
    <ProjectReference Include="..\AetherQuota.Shared\AetherQuota.Shared.csproj" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" />
  </ItemGroup>
</Project>
