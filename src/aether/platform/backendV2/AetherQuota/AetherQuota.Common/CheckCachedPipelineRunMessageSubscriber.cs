﻿// <copyright file="CheckCachedPipelineRunMessageSubscriber.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Azure.Core;
using Microsoft.Aether.Clouds.CloudCommon.ServiceBus;
using Microsoft.Aether.DataContracts.Entity;
using Microsoft.Aether.QuotaService.Shared;
using Microsoft.Azure.ServiceBus;
using Microsoft.MachineLearning.Common.ServiceBus;
using Microsoft.RelInfra.Extensions;
using Microsoft.RelInfra.Instrumentation;
using Microsoft.RelInfra.Instrumentation.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Microsoft.Aether.QuotaService
{
    /// <summary>
    /// Receive service bus queue message and check cached pipeline run candidates to put back into experiment queue.
    /// </summary>
    public class CheckCachedPipelineRunMessageSubscriber
    {
        private readonly QuotaServiceConfig _serviceConfig;
        private readonly CounterManager _counterManager;

        private readonly RunningPipelineRunSoftQuotaManager _softQuotaManager;
        private readonly ICachedPipelineRunManager _cachedPipelineRunManager;
        private readonly IExperimentQueuePublisher _experimentQueuePublisher;

        private readonly string _topicName;
        private readonly string _subscriptionName;
        private readonly ISubscriptionClient _subscriptionClient;

        public CheckCachedPipelineRunMessageSubscriber(
            string connectionString,
            QuotaServiceConfig serviceConfig,
            CounterManager counterManager,
            string topicName,
            string subscriptionName,
            RunningPipelineRunSoftQuotaManager quotaManager,
            ICachedPipelineRunManager cachedManager,
            IExperimentQueuePublisher experimentQueuePublisher)
            : this(serviceConfig, counterManager, topicName, subscriptionName, quotaManager, cachedManager, experimentQueuePublisher)
        {
            CommonLogger.LogEntityInfo(nameof(SubscriptionClient), $"Construct client with connection string. TopicName: [{topicName}]. SubscriptionName: [{subscriptionName}].");
            _subscriptionClient = new SubscriptionClient(
                connectionString: connectionString,
                topicPath: topicName,
                subscriptionName: subscriptionName,
                receiveMode: ReceiveMode.PeekLock,
                retryPolicy: RetryPolicy.Default);
        }

        public CheckCachedPipelineRunMessageSubscriber(
            string endpoint,
            TokenCredential credential,
            QuotaServiceConfig serviceConfig,
            CounterManager counterManager,
            string topicName,
            string subscriptionName,
            RunningPipelineRunSoftQuotaManager quotaManager,
            ICachedPipelineRunManager cachedManager,
            IExperimentQueuePublisher experimentQueuePublisher)
            : this(serviceConfig, counterManager, topicName, subscriptionName, quotaManager, cachedManager, experimentQueuePublisher)
        {
            var tokenAdapter = new LegacyServiceBusCredentialAdapter(credential);
            _subscriptionClient = new SubscriptionClient(
                endpoint: endpoint,
                topicPath: topicName,
                subscriptionName: subscriptionName,
                tokenProvider: tokenAdapter,
                receiveMode: ReceiveMode.PeekLock,
                retryPolicy: RetryPolicy.Default);
        }

        private CheckCachedPipelineRunMessageSubscriber(
            QuotaServiceConfig serviceConfig,
            CounterManager counterManager,
            string topicName,
            string subscriptionName,
            RunningPipelineRunSoftQuotaManager quotaManager,
            ICachedPipelineRunManager cachedManager,
            IExperimentQueuePublisher experimentQueuePublisher)
        {
            _serviceConfig = serviceConfig;
            _counterManager = counterManager;
            _topicName = topicName;
            _subscriptionName = subscriptionName;
            _softQuotaManager = quotaManager;
            _cachedPipelineRunManager = cachedManager;
            _experimentQueuePublisher = experimentQueuePublisher;
        }

        /// <summary>
        /// Initialize subscription client and register handler.
        /// </summary>
        public void Start()
        {
            var sessionHandlerOptions = new SessionHandlerOptions(HandleReceiverExceptionAsync)
            {
                AutoComplete = false,
                MessageWaitTimeout = _serviceConfig.ServiceBusMessageWaitTimeout,
                MaxConcurrentSessions = _serviceConfig.ServiceBusMaxConcurrentSessions,
                MaxAutoRenewDuration = _serviceConfig.ServiceBusMaxAutoRenewDuration,
            };

            _subscriptionClient.RegisterSessionHandler(HandleMessageSessionAsync, sessionHandlerOptions);

            CommonLogger.LogInfo($"Initialized client and registered message handler for susbscription: {_subscriptionName} of topic:{_topicName}.");
        }

        internal async Task HandleMessageSessionAsync(IMessageSession session, Message message, CancellationToken cancellationToken)
        {
            CommonLogger.LogEntityInfo(session.SessionId, "Received a message from topic {TopicName}. DeliveryCount={DeliveryCount}.", _topicName, message.SystemProperties.DeliveryCount);
            _counterManager.GetNumberCounter(Constants.CheckCachedPipelineRunMessageReceivedNumberCounter).Set(1);

            string messageType = string.Empty;
            if (message.UserProperties.TryGetValue(Clouds.CloudCommon.ServiceBus.ServiceBusConstants.MessageTypeUserPropertyName, out object typeNameObj))
            {
                messageType = typeNameObj as string;
            }

            Envelope<WorkspaceIdentity> messageEnvelope = null;
            try
            {
                string eventMessage = Encoding.UTF8.GetString(message.Body);
                messageEnvelope = JsonConvert.DeserializeObject<Envelope<WorkspaceIdentity>>(eventMessage);
            }
            catch (Exception ex)
            {
                CommonLogger.LogEntityInfo(session.SessionId, "Failed to deserialize message of type {message_type}. Error: {Exception}", messageType, ex);
                _counterManager.GetNumberCounter(Constants.CheckCachedPipelineRunMessageHandleFailureNumberCounter).Set(1);
                return;
            }

            using (new DisposableTimer(_counterManager.GetLatencyCounter(Constants.CheckCachedPipelineRunMessageHandleLatencyCounter)))
            {
                var workspace = messageEnvelope.Body;
                bool completed = await CheckCachedPipelineRunAndPutBackAsync(workspace, cancellationToken);
                if (completed)
                {
                    CommonLogger.LogEntityInfo(session.SessionId, "Process message successfully. Removing it from queue.");
                    _counterManager.GetNumberCounter(Constants.CheckCachedPipelineRunMessageHandleSuccessNumberCounter).Set(1);
                    await session.CompleteAsync(message.SystemProperties.LockToken);
                    _counterManager.GetNumberCounter(Constants.CheckCachedPipelineRunMessageLifetime).Set(
                        (long)DateTime.UtcNow.Subtract(message.SystemProperties.EnqueuedTimeUtc).TotalMilliseconds);
                }
                else
                {
                    _counterManager.GetNumberCounter(Constants.CheckCachedPipelineRunMessageHandleFailureNumberCounter).Set(1);
                }
            }
        }

        internal async Task<bool> CheckCachedPipelineRunAndPutBackAsync(WorkspaceIdentity workspace, CancellationToken cancellationToken)
        {
            var cacheExists = await _cachedPipelineRunManager.HasCachedPipelineRunAsync(workspace);
            // No cached pipeline runs exist, just return.
            if (!cacheExists)
            {
                CommonLogger.LogEntityInfo(workspace.WorkspaceId, $"No cached pipeline run exists in the workspace. WorkspaceId={workspace.WorkspaceId}, WorkspaceName={workspace.WorkspaceName}, ResourceGroupName={workspace.ResourceGroupName}, SubscriptionId={workspace.SubscriptionId}");
                return true;
            }

            var softQuotaResponse = await _softQuotaManager.GetCurrentQuotaAsync(workspace);
            if (!softQuotaResponse.IsQuotaAvailable)
            {
                CommonLogger.LogEntityInfo(workspace.WorkspaceId, $"Soft quota is not available for the workspace. WorkspaceId={workspace.WorkspaceId}, WorkspaceName={workspace.WorkspaceName}, ResourceGroupName={workspace.ResourceGroupName}, SubscriptionId={workspace.SubscriptionId}");
                return true;
            }

            int availableQuotaCount = softQuotaResponse.MaxLimit - softQuotaResponse.CurrentUsageCount;
            int maxBatchCount = 100;
            string continuationToken = null;
            do
            {
                // Limit to minimum of available quota count or batch count for each iteration.
                var queryResult = await _cachedPipelineRunManager.GetTopKCachedPipelineRunsAsync(availableQuotaCount, workspace, continuationToken, maxBatchCount);
                continuationToken = queryResult.ContinuationToken;

                if (cancellationToken.IsCancellationRequested)
                {
                    CommonLogger.LogEntityWarning(workspace.WorkspaceId, $"Message handling operation is canceled when checking and processing cached pipeline run of the workspace. WorkspaceId={workspace.WorkspaceId}, WorkspaceName={workspace.WorkspaceName}, ResourceGroupName={workspace.ResourceGroupName}, SubscriptionId={workspace.SubscriptionId}");
                    return false;
                }

                // Double check cached pipeline runs exist.
                if (!queryResult.NonStorableResults.Any())
                {
                    CommonLogger.LogEntityInfo(workspace.WorkspaceId, $"No cached pipeline run exists in the workspace. WorkspaceId={workspace.WorkspaceId}, WorkspaceName={workspace.WorkspaceName}, ResourceGroupName={workspace.ResourceGroupName}, SubscriptionId={workspace.SubscriptionId}");
                    return true;
                }

                // Most cases there are only a few cached pipeline runs, so limit parallelism to minimum of config and cached pipeline runs
                // to avoid creating unneccessary Tasks.
                int parallelismDegree = Math.Min(_serviceConfig.MaxParallelismDegree, queryResult.NonStorableResults.Count());

                await queryResult.NonStorableResults.ForEachAsync(parallelismDegree, cancellationToken, async cachedPipelineRun =>
                {
                    // Change to occupy soft quota.
                    await _softQuotaManager.UpdateQuotaUsageToSoftAsync(workspace, cachedPipelineRun.Id);

                    string experimentId = cachedPipelineRun.Id;
                    string queueMessage = cachedPipelineRun.QueueMessage;
                    await _experimentQueuePublisher.EnqueueExperimentAsync(workspace, experimentId, queueMessage);

                    // Only remove cached pipeline run after enqueued it into experiment queue successfully.
                    await _cachedPipelineRunManager.RemoveCachedPipelineRunAsync(workspace, experimentId);
                });
            }
            while (continuationToken != null);

            return true;
        }

        private Task HandleReceiverExceptionAsync(ExceptionReceivedEventArgs args)
        {
            CommonLogger.LogEntityError(_subscriptionName, "Received exception when handling message from topic {TopicName}. Error: {Exception}", _topicName, args.Exception);
            _counterManager.GetNumberCounter(Constants.CheckCachedPipelineRunMessageHandleFailureNumberCounter).Set(1);
            return Task.FromResult(true);
        }
    }
}
