﻿// <copyright file="IQuotaManager.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.Aether.DataContracts.Entity;
using Microsoft.Aether.QuotaService.Shared;
using Microsoft.RelInfra.Storage;

namespace Microsoft.Aether.QuotaService
{
    public interface IQuotaManager<TQuota, TUsage>
    {
        /// <summary>
        /// Acquire a quota with the resource in the workspace.
        /// Apply default quota limit to the workspace if not exists.
        /// IsQuotaAvailable will be true if in quota limit and acquired successfully, false if failed to acquire.
        /// </summary>
        public Task<QuotaResponse> AcquireQuotaAsync(WorkspaceIdentity workspace, TUsage resource);

        /// <summary>
        /// Release a quota with the resource in the workspace.
        /// It's no-op if the workspace quota doesn't exist.
        /// </summary>
        public Task<bool> ReleaseQuotaAsync(WorkspaceIdentity workspace, TUsage resource);

        /// <summary>
        /// Get the current quota usage of the workspace.
        /// Apply default quota limit to the workspace if not exists.
        /// </summary>
        public Task<QuotaResponse> GetCurrentQuotaAsync(WorkspaceIdentity workspace);

        /// <summary>
        /// Calculate the current quota usage count from saved resources in the workspace.
        /// </summary>
        public Task<int> CalculateQuotaCountFromUsagesAsync(WorkspaceIdentity workspace);

        /// <summary>
        /// Update the maximum quota limit of the workspace.
        /// </summary>
        public Task<TQuota> SetQuotaLimitAsync(WorkspaceIdentity workspace, int maxLimit);

        /// <summary>
        /// Get all quota limits of all workspaces.
        /// Should perfer <see cref="GetAllQuotaLimitsWithContinuationTokenAsync"/> to paging with continuation token if results number could be large.
        /// </summary>
        public Task<IEnumerable<TQuota>> GetAllQuotaLimitsAsync(int? maxCount = int.MaxValue);

        /// <summary>
        /// Get all quota limits of all workspaces with continuation token.
        /// </summary>
        public Task<QueryResults<TQuota>> GetAllQuotaLimitsWithContinuationTokenAsync(string continuationToken = null, int? maxCount = 100);

        /// <summary>
        /// Add the resource when acquire a quota.
        /// </summary>
        public Task AddUsageAsync(WorkspaceIdentity workspace, TUsage resource);

        /// <summary>
        /// Update the resource when revise quota.
        /// </summary>
        public Task UpdateUsageAsync(WorkspaceIdentity workspace, TUsage resource);

        /// <summary>
        /// Remove the usage resource when release a quota or delete a workspace.
        /// </summary>
        public Task<bool> RemoveUsageAsync(WorkspaceIdentity workspace, TUsage resource);

        /// <summary>
        /// Check whether the usage resource already exists and reserved quota.
        /// </summary>
        public Task<bool> ExistsUsageAsync(WorkspaceIdentity workspace, string resourceId);

        /// <summary>
        /// Remove the quota limit when delete a workspace.
        /// </summary>
        public Task<bool> RemoveQuotaLimitAsync(WorkspaceIdentity workspace);

        /// <summary>
        /// Get all resources which use quota in the workspace.
        /// If workspace is null, get all resources from all workspaces.
        /// Should perfer <see cref="GetAllUsagesWithContinuationTokenAsync"/> to paging with continuation token if results number could be large.
        /// </summary>
        public Task<IEnumerable<TUsage>> GetAllUsagesAsync(WorkspaceIdentity workspace = null, int? maxCount = int.MaxValue);

        /// <summary>
        /// Get all resources which use quota in the workspace with continuation token.
        /// If workspace is null, get all resources from all workspaces.
        /// </summary>
        public Task<QueryResults<TUsage>> GetAllUsagesWithContinuationTokenAsync(WorkspaceIdentity workspace = null, string continuationToken = null, int? maxCount = 100);
    }
}
