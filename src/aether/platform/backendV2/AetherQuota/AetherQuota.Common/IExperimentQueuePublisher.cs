﻿// <copyright file="IExperimentQueuePublisher.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Microsoft.Aether.DataContracts.Entity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Microsoft.Aether.QuotaService
{
    public interface IExperimentQueuePublisher
    {
        public Task EnqueueExperimentAsync(WorkspaceIdentity workspace, string experimentId, string queueMessage);
    }
}
