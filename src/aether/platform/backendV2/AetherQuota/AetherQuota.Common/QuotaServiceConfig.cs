﻿// <copyright file="QuotaServiceConfig.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.Aether.BlueBox.AetherK8SCommon;
using Microsoft.Aether.BlueBox.Storage;
using Microsoft.RelInfra.Common.Configuration;
using Microsoft.RelInfra.Common.Secrets;

namespace Microsoft.Aether.QuotaService
{
    public class QuotaServiceConfig : VerifiableConfig
    {
        private readonly SecretProvider _secretProvider;

        public QuotaServiceConfig(IRelInfraConfiguration configuration, SecretProvider secretProvider)
            : base(configuration, verifyConfigOnChanges: true, configName: nameof(QuotaServiceConfig))
        {
            _secretProvider = secretProvider;
            SharedCosmosDbConfig = new SharedCosmosDbConfig(configuration, secretProvider: secretProvider);
        }

        public SharedCosmosDbConfig SharedCosmosDbConfig { get; }

        public string CosmosDbDatabaseName => Config.GetString("CosmosDb.DatabaseName");

        public string RunningPipelineRunQuotaLimitContainerName => Config.GetString("CosmosDb.RunningPipelineRunQuotaLimitContainerName");

        public string RunningPipelineRunQuotaUsageContainerName => Config.GetString("CosmosDb.RunningPipelineRunQuotaUsageContainerName");

        public string CachedPipelineRunContainerName => Config.GetString("CosmosDb.CachedPipelineRunContainerName");

        public TimeSpan ConnectionCacheDuration => Config.GetTimeSpan("CosmosDb.ConnectionCacheDuration");

        public TimeSpan RequestTimeout => Config.GetTimeSpan("CosmosDb.RequestTimeout", TimeSpan.FromSeconds(10));

        public int RunningPipelineRunDefaultQuotaLimit => Config.GetInteger("QuotaSettings.RunningPipelineRunDefaultQuotaLimit");

        public int RunningPipelineRunDefaultSoftQuotaLimit => Config.GetInteger("QuotaSettings.RunningPipelineRunDefaultSoftQuotaLimit");

        public TimeSpan RunningPipelineRunReviseTaskTimeout => Config.GetTimeSpan("QuotaSettings.RunningPipelineRunReviseTaskTimeout");

        public TimeSpan RunningPipelineRunReviseTaskInterval => Config.GetTimeSpan("QuotaSettings.RunningPipelineRunReviseTaskInterval");

        public CosmosDbOptions GetDefaultCollectionOptions()
        {
            int throughtput = Config.GetInteger($"CosmosDb.ContainerThroughput", defaultValue: 0);
            bool isAutoscale = Config.GetBoolean($"CosmosDb.IsAutoscale", defaultValue: false);
            return new CosmosDbOptions { isAutoscale = isAutoscale, Throughput = throughtput != 0 ? throughtput : null };
        }

        public DefaultAndCustomerCosmosDbOptions GetCosmosDbOptions()
        {
            return new DefaultAndCustomerCosmosDbOptions()
            {
                DefaultOptions = GetDefaultCollectionOptions(),
            };
        }

        public string MetaStoreUrl => Config.GetString("SharedMetaStore.MetaStoreUrl");

        public TimeSpan MetaStoreTimeout => Config.GetTimeSpan("SharedMetaStore.MetaStoreTimeout");

        public TimeSpan ServiceBusMessageWaitTimeout => Config.GetTimeSpan("ServiceBus.MessageWaitTimeout");

        public int ServiceBusMaxConcurrentSessions => Config.GetInteger("ServiceBus.MaxConcurrentSessions");

        public TimeSpan ServiceBusMaxAutoRenewDuration => Config.GetTimeSpan("ServiceBus.MaxAutoRenewDuration");

        public string ExperimentQueuePrefix => Config.GetString("AzureQueueManager.ExperimentQueuePrefix");

        public TimeSpan AzureQueueManagerUpdatePeriod => Config.GetTimeSpan("AzureQueueManager.UpdatePeriod");

        public Task<string> GetExperimentQueueStorageAzureConnectionStringAsync()
        {
            return Config.GetSecretStringAsync(_secretProvider, "AzureQueueManager.ExperimentQueueStorageAzureConnectionFileName");
        }

        public string TeamExperimentsQuotaPrefix => Config.GetString("QuotaSettings.TeamExperimentsQuotaPrefix");

        public string UserRunningJobsQuotaPrefix => Config.GetString("QuotaSettings.UserRunningJobsQuotaPrefix");

        public string TeamEmailsQuotaPrefix => Config.GetString("QuotaSettings.TeamEmailsQuotaPrefix");

        public string TeamPromotionsQuotaPrefix => Config.GetString("QuotaSettings.TeamPromotionsQuotaPrefix");

        public string TeamUploadsQuotaPrefix => Config.GetString("QuotaSettings.TeamUploadsQuotaPrefix");

        public int MaxParallelismDegree => Config.GetInteger("QuotaSettings.MaxParallelismDegree");

        public TimeSpan SubmissionExpirationInterval => Config.GetTimeSpan("QuotaSettings.SubmissionExpirationInterval");

        public TimeSpan CacheExpirationInterval => Config.GetTimeSpan("QuotaSettings.CacheExpirationInterval");

        public string RoiServiceBaseUrl => Config.GetString("SharedRoi.ServiceAddress");

        public string DefaultTeamId => Config.GetString("SharedRoi.DefaultTeamId");
        #region Service settings

        public string ServiceName => Config.GetString("Service.Name");

        public long MaxReceivedMessageSize => Config.GetInteger("Service.MaxReceivedMessageSize");

        public int Port => Config.GetInteger("Service.Port");

        public string GetSslCert()
        {
            return Config.GetString("AmlServices.SslCertSecretPath");
        }
        #endregion

        #region Azure Settings

        public Task<string> GetAzureConnectionStringAsync()
        {
            return Config.GetSecretStringAsync(_secretProvider, "AzureStorage.ConnectionFileName");
        }

        public int AzureBlobRetriesNumber => Config.GetInteger("AzureStorage.BlobRetriesNumber");

        public TimeSpan AzureBlobRetryInterval => Config.GetTimeSpan("AzureStorage.BlobRetryInterval");

        public TimeSpan AzureBlobTimeoutInterval => Config.GetTimeSpan("AzureStorage.BlobTimeoutInterval");

        public string AzureBlobContainerName => Config.GetString("AzureStorage.BlobContainerName");

        public string UserRunningJobsBlobContainerName => Config.GetString("AzureStorage.UserRunningJobsBlobContainerName");

        public int MaxBlobsToListForQuotaStat => Config.GetInteger("AzureStorage.MaxBlobsToListForQuotaStat");

        public string BlobLeaseContainerName => Config.GetString("AzureStorage.BlobLeaseContainerName");

        public int NodesLimit => Config.GetInteger("QuotaSettings.SubmittedNodesLimit");

        public TimeSpan RunningJobExpirationInterval => Config.GetTimeSpan("QuotaSettings.RunningJobExpirationInterval");

        public int RunningNodesLimit => Config.GetInteger("QuotaSettings.RunningNodesLimit");

        public int EmailsLimit => Config.GetInteger("QuotaSettings.EmailsLimit");

        public int UploadsLimit => Config.GetInteger("QuotaSettings.UploadsLimit");

        public int PromotionsLimit => Config.GetInteger("QuotaSettings.PromotionsLimit");

        public HashSet<string> NodeExemption
        {
            get
            {
                string tmp = Config.GetString("QuotaSettings.NodesExemption");
                return string.IsNullOrWhiteSpace(tmp) ? new HashSet<string>() : new HashSet<string>(tmp.Split(','));
            }
        }

        public HashSet<string> EmailExemption
        {
            get
            {
                string tmp = Config.GetString("QuotaSettings.EmailsExemption");
                return string.IsNullOrWhiteSpace(tmp) ? new HashSet<string>() : new HashSet<string>(tmp.Split(','));
            }
        }

        public HashSet<string> UploadsExemption
        {
            get
            {
                string tmp = Config.GetString("QuotaSettings.UploadsExemption");
                return string.IsNullOrWhiteSpace(tmp) ? new HashSet<string>() : new HashSet<string>(tmp.Split(','));
            }
        }

        public HashSet<string> PromotionsExemption
        {
            get
            {
                string tmp = Config.GetString("QuotaSettings.PromotionsExemption");
                return string.IsNullOrWhiteSpace(tmp) ? new HashSet<string>() : new HashSet<string>(tmp.Split(','));
            }
        }

        #endregion
    }
}
