﻿// <copyright file="RunningPipelineRunQuotaUsageRevision.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Microsoft.Aether.DataContracts;
using Microsoft.Aether.DataContracts.Entity;
using Microsoft.Aether.QuotaService.Shared;
using Microsoft.RelInfra.Extensions;
using Microsoft.RelInfra.Instrumentation;
using Microsoft.RelInfra.Instrumentation.Logging;
using Microsoft.RelInfra.Storage;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Threading.Tasks;

[assembly: InternalsVisibleTo("AetherQuota.Test")]

namespace Microsoft.Aether.QuotaService
{
    public class RunningPipelineRunQuotaUsageRevision : IDisposable
    {
        private readonly QuotaServiceConfig _serviceConfig;
        private readonly CounterManager _counterManager;
        private readonly RunningPipelineRunQuotaManager _quotaManager;
        private readonly RunningPipelineRunSoftQuotaManager _softQuotaManager;

        private readonly IExperimentStore _experimentStore;
        // Each experiment is only checked once in the time interval from metastore.
        private readonly TimeSpan _experimentCheckInterval = TimeSpan.FromDays(1);

        private readonly IAzureBlobStorage _blobStorage;
        private readonly ILeasableStorage _lockStorage;
        private readonly string _lockContainerName;
        private readonly string _lockBlobName = "RunningPipelineRunRevisionLock";
        private readonly string _lastRevisionTimestampBlobName = "RunningPipelineRunLastRevisionTimestamp";

        private readonly Dictionary<string, WorkspacePipelineRunQuotaUsageStats> _workspacesQuotaUsageStats;

        private Timer _quotaUsageReviseTimer;
        private bool disposed;

        public RunningPipelineRunQuotaUsageRevision(
            QuotaServiceConfig config,
            CounterManager counterManager,
            RunningPipelineRunQuotaManager quotaManager,
            RunningPipelineRunSoftQuotaManager softQuotaManager,
            IExperimentStore experimentStore,
            IAzureBlobStorage blobStorage,
            string lockContainerName)
        {
            _serviceConfig = config;
            _counterManager = counterManager;
            _quotaManager = quotaManager;
            _softQuotaManager = softQuotaManager;
            _experimentStore = experimentStore;
            _blobStorage = blobStorage;
            _lockContainerName = lockContainerName;
            _lockStorage = new AzureBlobLeasableContainerStorage(_blobStorage, _lockContainerName);
            _workspacesQuotaUsageStats = new Dictionary<string, WorkspacePipelineRunQuotaUsageStats>();
        }

        public void Start()
        {
            _quotaUsageReviseTimer = new Timer(ReviseAllQuotaUsagesCycle, state: null, dueTime: 0, period: Timeout.Infinite);
        }

        public async void ReviseAllQuotaUsagesCycle(object state)
        {
            CommonLogger.LogEntityInfo(nameof(RunningPipelineRunQuotaUsageRevision), "Quota usage revision task is triggered.");
            _counterManager.GetNumberCounter(Constants.ReviseRunningPipelineRunQuotaUsageNumberCounter).Set(1);
            try
            {
                using (var cts = new CancellationTokenSource())
                {
                    var reviseTask = ReviseAllQuotaUsagesAsync();
                    var delayTask = Task.Delay(_serviceConfig.RunningPipelineRunReviseTaskTimeout, cts.Token);
                    if (await Task.WhenAny(reviseTask, delayTask) == delayTask)
                    {
                        _counterManager.GetNumberCounter(Constants.ReviseRunningPipelineRunQuotaUsageFailureNumberCounter).Set(1);
                        CommonLogger.LogEntityError(nameof(RunningPipelineRunQuotaUsageRevision), $"Quota usage revision task has been stuck for more than {_serviceConfig.RunningPipelineRunReviseTaskTimeout}. Restarting the service...");
                        await Task.Delay(TimeSpan.FromSeconds(5));
                        Environment.Exit(1);
                    }

                    cts.Cancel();
                }
            }
            catch (Exception ex)
            {
                _counterManager.GetNumberCounter(Constants.ReviseRunningPipelineRunQuotaUsageFailureNumberCounter).Set(1);
                CommonLogger.LogEntityError(nameof(RunningPipelineRunQuotaUsageRevision), $"Quota usage revision task failed with error: {ex}");
            }
            finally
            {
                _quotaUsageReviseTimer?.Change(_serviceConfig.RunningPipelineRunReviseTaskInterval, Timeout.InfiniteTimeSpan);
            }
        }

        public async Task ReviseAllQuotaUsagesAsync()
        {
            var lease = new AzureAutoRenewLease(
                storage: _lockStorage,
                resourceId: _lockBlobName,
                leaseTime: TimeSpan.FromSeconds(30));
            try
            {
                if (await lease.TryAcquireLeaseAsync())
                {
                    var lastReviseTimestamp = await _blobStorage.TryGetAsync<DateTime>(_lockContainerName, _lastRevisionTimestampBlobName);
                    // Already run in last interval, skip.
                    if (lastReviseTimestamp != null && lastReviseTimestamp.Entity + _serviceConfig.RunningPipelineRunReviseTaskInterval > DateTime.UtcNow)
                    {
                        CommonLogger.LogEntityInfo(nameof(RunningPipelineRunQuotaUsageRevision), $"Quota revision task already run in {_serviceConfig.RunningPipelineRunReviseTaskInterval} interval, last run timestamp: {lastReviseTimestamp.Entity}. Skipping.");
                        return;
                    }

                    using (new DisposableTimer(_counterManager.GetLatencyCounter(Constants.ReviseRunningPipelineRunQuotaUsageLatencyCounter)))
                    {
                        await ReviseAllQuotaUsagesInternalAsync();
                    }

                    if (lastReviseTimestamp == null)
                    {
                        lastReviseTimestamp = new Storable<DateTime> { Etag = "*" };
                    }

                    // Save lastest revision timestamp.
                    lastReviseTimestamp.Entity = DateTime.UtcNow;
                    await _blobStorage.UpdateAsync(_lockContainerName, _lastRevisionTimestampBlobName, lastReviseTimestamp);
                }
                else
                {
                    CommonLogger.LogEntityInfo(nameof(RunningPipelineRunQuotaUsageRevision), "Failed to acquire lease, there's already a running quota revision task. Skipping.");
                }
            }
            catch (Exception ex)
            {
                _counterManager.GetNumberCounter(Constants.ReviseRunningPipelineRunQuotaUsageFailureNumberCounter).Set(1);
                CommonLogger.LogEntityError(nameof(RunningPipelineRunQuotaUsageRevision), $"Quota usage revision task failed with error: {ex}");
            }
            finally
            {
                await lease.ReleaseLeaseAsync();
            }
        }

        internal IEnumerable<WorkspacePipelineRunQuotaUsageStats> GetWorkspacesQuotaUsageStats() => _workspacesQuotaUsageStats.Values;

        internal async Task ReviseAllQuotaUsagesInternalAsync()
        {
            var exceptions = new List<Exception>();

            _workspacesQuotaUsageStats.Clear(); // Reset stats.
            int maxCount = 100;
            string continuationToken = null;
            var updatedWorkspaces = new HashSet<string>();
            do
            {
                var queryResult = await _quotaManager.GetAllUsagesWithContinuationTokenAsync(
                    workspace: null,    // Use null to query usages from all workspaces
                    continuationToken: continuationToken,
                    maxCount: maxCount);
                continuationToken = queryResult.ContinuationToken;

                var quotaUsageEntities = queryResult.Results.Select(s => s.Entity);
                CommonLogger.LogEntityInfo(nameof(RunningPipelineRunQuotaUsageRevision), $"Get {quotaUsageEntities.Count()} quota usage entities.");

                var quotaUsageEntitiesByWorkspace = quotaUsageEntities.GroupBy(entity => entity.Workspace);

                foreach (var group in quotaUsageEntitiesByWorkspace)
                {
                    var workspace = group.Key;
                    try
                    {
                        // Check experiments (usageEntities) status from MetaStore, remove completed ones if there's any to release quota.
                        bool updatedWorkspaceQuota = await CheckAndUpdateUsageEntitiesAsync(group.Key, group);
                        if (updatedWorkspaceQuota)
                        {
                            updatedWorkspaces.Add(workspace.WorkspaceId);
                        }
                    }
                    catch (Exception ex)
                    {
                        // If revising a workspace meets exception, continue to revise other workspaces.
                        CommonLogger.LogEntityError(nameof(RunningPipelineRunQuotaUsageRevision), $"Quota revision task failed to update quota count for WorkspaceId={workspace.WorkspaceId}, WorkspaceName={workspace.WorkspaceName}, ResourceGroupName={workspace.ResourceGroupName}, SubscriptionId={workspace.SubscriptionId}. Error: {ex}");
                        exceptions.Add(ex);
                    }
                }
            }
            while (continuationToken != null);

            await PublishWorkspaceQuotaUsageStatsMetricsAsync();

            if (updatedWorkspaces.Any())
            {
                CommonLogger.LogEntityInfo(nameof(RunningPipelineRunQuotaUsageRevision), $"Quota revision task has updated quota count of {updatedWorkspaces.Count()} workspaces.");
                _counterManager.GetNumberCounter(Constants.RunningPipelineRunQuotaFixedWorkspaceNumberCounter).Set(updatedWorkspaces.Count());
            }

            if (exceptions.Count > 0)
            {
                CommonLogger.LogEntityError(nameof(RunningPipelineRunQuotaUsageRevision), $"Quota revision task failed to update all workspaces quota count.");
                throw new AggregateException("Quota revision task failed to update all workspaces quota count.", exceptions);
            }

            CommonLogger.LogEntityInfo(nameof(RunningPipelineRunQuotaUsageRevision), "Quota usage revision task completed successfully.");
            _counterManager.GetNumberCounter(Constants.ReviseRunningPipelineRunQuotaUsageSuccessNumberCounter).Set(1);
        }

        private async Task<bool> CheckAndUpdateUsageEntitiesAsync(WorkspaceIdentity workspace, IEnumerable<RunningPipelineRunQuotaUsageEntity> usageEntities)
        {
            bool updatedWorkspaceQuota = false;
            var workspaceStats = _workspacesQuotaUsageStats.GetOrAdd(workspace.WorkspaceId, _ => new WorkspacePipelineRunQuotaUsageStats { Workspace = workspace });

            var metricsDimensions = new Dictionary<string, string>
            {
                { nameof(workspace.WorkspaceName), workspace.WorkspaceName },
                { nameof(workspace.WorkspaceId), workspace.WorkspaceId },
                { nameof(workspace.SubscriptionId), workspace.SubscriptionId },
                { nameof(workspace.ResourceGroupName), workspace.ResourceGroupName },
            };

            foreach (var usageEntity in usageEntities)
            {
                // Track possible long running or stuck pipeline runs.
                TimeSpan quotaOccupiedTime = DateTime.UtcNow - usageEntity.CreatedTimeUtc;
                _counterManager.GetLatencyCounter(Constants.RunningPipelineRunQuotaOccupyTimeCounter, customDimensions: metricsDimensions).Set((long)quotaOccupiedTime.TotalSeconds);

                bool isSoftQuota = usageEntity.IsSoftQuota.HasValue ? usageEntity.IsSoftQuota.Value : false;
                bool isRoot = string.IsNullOrEmpty(usageEntity.ParentExperimentId);
                workspaceStats.QuotaUsageCount++;
                workspaceStats.SoftQuotaUsageCount += isSoftQuota ? 1 : 0;
                workspaceStats.RootPipelineRunQuotaUsageCount += isRoot ? 1 : 0;
                workspaceStats.RootPipelineRunSoftQuotaUsageCount += isRoot && isSoftQuota ? 1 : 0;
                workspaceStats.SubGraphPipelineRunQuotaUsageCount += !isRoot ? 1 : 0;
                workspaceStats.SubGraphPipelineRunSoftQuotaUsageCount += !isRoot && isSoftQuota ? 1 : 0;
            }

            // Only check experiments which is not checked recently.
            var checkingEntities = usageEntities.Where(entity => entity.LastUpdatedTimeUtc + _experimentCheckInterval < DateTime.UtcNow);
            var checkingIds = checkingEntities.Select(entity => entity.Id).ToArray();

            if (checkingIds.Length == 0)
            {
                CommonLogger.LogEntityInfo(workspace.WorkspaceId, "No experiments entities need to check status from metastore.");
                return false;
            }

            CommonLogger.LogEntityInfo(workspace.WorkspaceId, $"Query metastore with {checkingIds.Length} experiment ids.");
            IEnumerable<ExperimentEntity> experiments;
            try
            {
                experiments = await _experimentStore.BulkGetAsync(workspace.WorkspaceId, checkingIds, workspace);
            }
            catch (HttpRequestDetailException ex) when (ex.StatusCode == HttpStatusCode.NotFound || ex.StatusCode == HttpStatusCode.Conflict)
            {
                CommonLogger.LogEntityWarning(workspace.WorkspaceId, $"Query metastore met NotFound or Conflict exception, the workspace may be deleted or in deleting state. Error: {ex}");

                // Try to remove usage entities if workspace not found (404) or in deleting state (409).
                // Normally StorageCleanup service will call QuotaService to remove resources when handling workspace delete event.
                // But just in case StorageCleanup service failed and not deleted.
                foreach (var removeEntity in checkingEntities)
                {
                    await _quotaManager.RemoveUsageAsync(workspace, removeEntity);
                }

                CommonLogger.LogEntityWarning(workspace.WorkspaceId, $"Removed {checkingEntities.Count()} usage entities because the workspace may be deleted or in deleting state.");

                return false;
            }

            var runningEntityIds = new HashSet<string>(
                experiments.Where(e => e.Status.StatusCode != ExperimentStatusCode.Finished
                    && e.Status.StatusCode != ExperimentStatusCode.Failed
                    && e.Status.StatusCode != ExperimentStatusCode.Canceled)
                .Select(e => e.Id));

            var runningEntities = checkingEntities.Where(entity => runningEntityIds.Contains(entity.Id));
            foreach (var runningEntity in runningEntities)
            {
                // Update last timestamp to only check it again in next interval.
                runningEntity.LastUpdatedTimeUtc = DateTime.UtcNow;
                await _quotaManager.UpdateUsageAsync(workspace, runningEntity);
            }

            var completedEntities = checkingEntities.Where(entity => !runningEntityIds.Contains(entity.Id));
            if (completedEntities.Any())
            {
                CommonLogger.LogEntityWarning(workspace.WorkspaceId, $"There are {completedEntities.Count()} experiments already completed but still in saved entity collection. Removing them.");
                _counterManager.GetNumberCounter(Constants.RunningPipelineRunQuotaFailedToReleaseNumberCounter, customDimensions: metricsDimensions).Set(completedEntities.Count());

                foreach (var removeEntity in completedEntities)
                {
                    await _quotaManager.RemoveUsageAsync(workspace, removeEntity);
                }

                updatedWorkspaceQuota = true;
            }

            return updatedWorkspaceQuota;
        }

        private async Task PublishWorkspaceQuotaUsageStatsMetricsAsync()
        {
            foreach (var stats in _workspacesQuotaUsageStats.Values)
            {
                var workspace = stats.Workspace;
                int quotaLimit = (await _quotaManager.GetCurrentQuotaAsync(workspace)).MaxLimit;
                int softQuotaLimit = (await _softQuotaManager.GetCurrentQuotaAsync(workspace)).MaxLimit;
                int quotaUsagePercentage = quotaLimit == 0 ? 0 : 100 * stats.QuotaUsageCount / quotaLimit;
                int softQuotaUsagePercentage = softQuotaLimit == 0 ? 0 : 100 * stats.SoftQuotaUsageCount / softQuotaLimit;

                var metricsDimensions = new Dictionary<string, string>
                {
                    { nameof(workspace.WorkspaceName), workspace.WorkspaceName },
                    { nameof(workspace.WorkspaceId), workspace.WorkspaceId },
                    { nameof(workspace.SubscriptionId), workspace.SubscriptionId },
                    { nameof(workspace.ResourceGroupName), workspace.ResourceGroupName },
                };

                CommonLogger.LogEntityInfo(workspace.WorkspaceId, $"Workspace pipeline run quota usage stats. QuotaLimit={quotaLimit}, SoftQuotaLimit={softQuotaLimit}, " +
                    $"QuotaUsage={stats.QuotaUsageCount}, SoftQuotaUsage={stats.SoftQuotaUsageCount}, QuotaUsagePercentage={quotaUsagePercentage}, SoftQuotaUsagePercentage={softQuotaUsagePercentage}, " +
                    $"RootQuotaUsage={stats.RootPipelineRunQuotaUsageCount}, RootSoftQuotaUsage={stats.RootPipelineRunSoftQuotaUsageCount}, SubGraphQuotaUsage={stats.SubGraphPipelineRunQuotaUsageCount}, SubGraphSoftQuotaUsage={stats.SubGraphPipelineRunSoftQuotaUsageCount}, " +
                    $"WorkspaceName={workspace.WorkspaceName}, ResourceGroupName={workspace.ResourceGroupName}, SubscriptionId={workspace.SubscriptionId}");

                _counterManager.GetNumberCounter(Constants.RunningPipelineRunQuotaUsageNumberCounter, customDimensions: metricsDimensions).Set(stats.QuotaUsageCount);
                _counterManager.GetNumberCounter(Constants.RunningPipelineRunSoftQuotaUsageNumberCounter, customDimensions: metricsDimensions).Set(stats.SoftQuotaUsageCount);
                _counterManager.GetNumberCounter(Constants.RunningPipelineRunQuotaUsagePercentageCounter, customDimensions: metricsDimensions).Set(quotaUsagePercentage);
                _counterManager.GetNumberCounter(Constants.RunningPipelineRunSoftQuotaUsagePercentageCounter, customDimensions: metricsDimensions).Set(softQuotaUsagePercentage);
                _counterManager.GetNumberCounter(Constants.RootPipelineRunQuotaUsageNumberCounter, customDimensions: metricsDimensions).Set(stats.RootPipelineRunQuotaUsageCount);
                _counterManager.GetNumberCounter(Constants.RootPipelineRunSoftQuotaUsageNumberCounter, customDimensions: metricsDimensions).Set(stats.RootPipelineRunSoftQuotaUsageCount);
                _counterManager.GetNumberCounter(Constants.SubGraphPipelineRunQuotaUsageNumberCounter, customDimensions: metricsDimensions).Set(stats.SubGraphPipelineRunQuotaUsageCount);
                _counterManager.GetNumberCounter(Constants.SubGraphPipelineRunSoftQuotaUsageNumberCounter, customDimensions: metricsDimensions).Set(stats.SubGraphPipelineRunSoftQuotaUsageCount);
            }
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!disposed)
            {
                if (disposing)
                {
                    if (_quotaUsageReviseTimer != null)
                    {
                        _quotaUsageReviseTimer.Dispose();
                        _quotaUsageReviseTimer = null;
                    }
                }

                disposed = true;
            }
        }

        public void Dispose()
        {
            Dispose(disposing: true);
            GC.SuppressFinalize(this);
        }
    }
}
