﻿// <copyright file="RunningPipelineRunSoftQuotaManager.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Aether.BlueBox.Storage;
using Microsoft.Aether.Clouds.CloudCommon.ServiceBus;
using Microsoft.Aether.DataContracts.Entity;
using Microsoft.Aether.QuotaService.Shared;
using Microsoft.MachineLearning.Common.ServiceBus;
using Microsoft.RelInfra.Common.RetryExecution.Policy;
using Microsoft.RelInfra.Instrumentation.Logging;
using Microsoft.RelInfra.Storage;

namespace Microsoft.Aether.QuotaService
{
    public class RunningPipelineRunSoftQuotaManager : BaseQuotaManager<RunningPipelineRunQuotaLimitEntity, RunningPipelineRunQuotaUsageEntity>
    {
        private readonly ICachedPipelineRunManager _cachedPipelineRunManager;
        private readonly IServiceBusTopicPublisher _checkCachedTopicPublisher;

        public RunningPipelineRunSoftQuotaManager(
            QuotaServiceConfig config,
            IQueryableDbStorage limitContainer,
            IQueryableDbStorage usageContainer,
            ICachedPipelineRunManager cachedPipelineRunManager,
            IServiceBusTopicPublisher checkCachedTopicPublisher)
            : base(QuotaType.RunningPipelineRunSoft, config, limitContainer, usageContainer)
        {
            _cachedPipelineRunManager = cachedPipelineRunManager;
            _checkCachedTopicPublisher = checkCachedTopicPublisher;
        }

        public override string GetQuotaPartitionKey(WorkspaceIdentity workspace) => workspace.WorkspaceId;

        public override string GetQuotaId(WorkspaceIdentity workspace) => workspace.WorkspaceId;

        public override string GetUsagePartitionKey(WorkspaceIdentity workspace) => workspace.WorkspaceId;

        public override string GetUsageId(RunningPipelineRunQuotaUsageEntity resource) => resource.Id;

        public override RunningPipelineRunQuotaLimitEntity GetDefaultQuotaLimit(WorkspaceIdentity workspace)
        {
            return new RunningPipelineRunQuotaLimitEntity
            {
                Id = workspace.WorkspaceId,
                Workspace = workspace,
                MaxLimit = _serviceConfig.RunningPipelineRunDefaultQuotaLimit,
                MaxSoftLimit = _serviceConfig.RunningPipelineRunDefaultSoftQuotaLimit,
            };
        }

        public override async Task<QuotaResponse> AcquireQuotaAsync(WorkspaceIdentity workspace, RunningPipelineRunQuotaUsageEntity resource)
        {
            if (!string.IsNullOrEmpty(resource.ParentExperimentId))
            {
                // For subgraph pipeline run, if it's parent pipeline run already acquired soft quota and is running,
                // we skip soft quota limit check and just add the usage to reserve a soft quota.
                // So parent pipeline run won't be stuck due to subgraph pipeline run is queued and may never complete in few extreme cases.
                // It may cause current usage count be large than the maximum limit, while a few overlimit is acceptable and allowed.
                bool parentUsageExists = await ExistsUsageAsync(workspace, resource.ParentExperimentId);
                if (parentUsageExists)
                {
                    bool usageExists = await ExistsUsageAsync(workspace, resource.Id);
                    if (!usageExists)
                    {
                        await AddUsageAsync(workspace, resource);
                    }

                    return new QuotaResponse
                    {
                        IsQuotaAvailable = true,
                    };
                }
            }

            bool hasCached = await _cachedPipelineRunManager.HasCachedPipelineRunAsync(workspace);
            // If so, return soft quota unavailable to process cached pipeline runs first.
            if (hasCached)
            {
                CommonLogger.LogEntityInfo(resource.Id, "Failed to acquire soft quota due to there are cached pipeline runs. AcquireCount={acquireCount}, QuotaType={quotaType}, WorkspaceId={workspaceId}, WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}",
                    resource.UsageCount, _quotaType, workspace.WorkspaceId, workspace.WorkspaceName, workspace.ResourceGroupName, workspace.SubscriptionId);
                return new QuotaResponse
                {
                    IsQuotaAvailable = false,
                };
            }

            return await base.AcquireQuotaAsync(workspace, resource);
        }

        public override async Task<bool> ReleaseQuotaAsync(WorkspaceIdentity workspace, RunningPipelineRunQuotaUsageEntity resource)
        {
            var released = await base.ReleaseQuotaAsync(workspace, resource);

            bool hasCached = await _cachedPipelineRunManager.HasCachedPipelineRunAsync(workspace);
            if (hasCached)
            {
                CommonLogger.LogEntityInfo(resource.Id, "Workspace has cached pipeline run, enqueue a message to check and put back to process. WorkspaceId={workspaceId}, WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}",
                    workspace.WorkspaceId, workspace.WorkspaceName, workspace.ResourceGroupName, workspace.SubscriptionId);
                await SendMessageToCheckCachedPipelineRunAsync(workspace, resource.Id).ConfigureAwait(false);
            }

            return released;
        }

        public override async Task<int> CalculateQuotaCountFromUsagesAsync(WorkspaceIdentity workspace)
        {
            try
            {
                int currentQuotaCount = await CosmosDbRetryHelper.ExecuteCosmosDbWithRetry(
                       func: async () =>
                       {
                           using (var cts = new CancellationTokenSource(_requestTimeout))
                           {
                               // Calculate the soft quota usage count of the workspace.
                               var queryResult = await _usageContainer.ExecuteQueryNonStorableAsync<int>(
                                   partitionKey: GetUsagePartitionKey(workspace),
                                   query: Constants.TotalSoftQuotaUsageCountQuery,
                                   continuationToken: null,
                                   cancellationToken: cts.Token);
                               // There will be only one result for count query.
                               int softQuotaUsageCount = queryResult.NonStorableResults.First();
                               return softQuotaUsageCount;
                           }
                       },
                       retryPolicy: _retryPolicy).ConfigureAwait(false);

                CommonLogger.LogEntityInfo(GetQuotaId(workspace), "Calculate quota count from usage entities successfully. CurrentQuotaCount={quotaCount}, QuotaType={quotaType}, WorkspaceId={workspaceId}, WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}",
                    currentQuotaCount, _quotaType, workspace.WorkspaceId, workspace.WorkspaceName, workspace.ResourceGroupName, workspace.SubscriptionId);
                return currentQuotaCount;
            }
            catch (Exception ex)
            {
                CommonLogger.LogEntityError(GetQuotaId(workspace), "Failed to calculate quota count from usage entities. QuotaType={quotaType}, WorkspaceId={workspaceId}, WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}. Error: {exception}",
                    _quotaType, workspace.WorkspaceId, workspace.WorkspaceName, workspace.ResourceGroupName, workspace.SubscriptionId, ex);
                throw;
            }
        }

        protected override async Task<int> GetQuotaLimitAsync(WorkspaceIdentity workspace)
        {
            var quotaLimit = await GetOrUseDefaultQuotaLimitInternalAsync(workspace);
            // Return soft limit. If no soft quota limit configured, use default.
            int maxLimit = quotaLimit.MaxSoftLimit ?? _serviceConfig.RunningPipelineRunDefaultSoftQuotaLimit;
            CommonLogger.LogEntityInfo(GetQuotaId(workspace), "Get quota limit successfully. QuotaLimit={quotaLimit}, QuotaType={quotaType}, WorkspaceId={workspaceId}, WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}",
                maxLimit, _quotaType, workspace.WorkspaceId, workspace.WorkspaceName, workspace.ResourceGroupName, workspace.SubscriptionId);

            return maxLimit;
        }

        protected override async Task<RunningPipelineRunQuotaLimitEntity> VerifyAndGetNewQuotaLimitAsync(WorkspaceIdentity workspace, int maxSoftLimit)
        {
            if (maxSoftLimit < 0)
            {
                throw new ArgumentException($"Quota limit {maxSoftLimit} can not be less than 0.", nameof(maxSoftLimit));
            }

            var quotaLimit = await GetOrUseDefaultQuotaLimitInternalAsync(workspace);
            // Ensure soft quota limit <= hard quota limit
            if (maxSoftLimit > quotaLimit.MaxLimit)
            {
                throw new ArgumentException($"New soft quota limit can not be large than current hard quota limit. MaxHardLimit={quotaLimit.MaxLimit}, MaxSoftLimit={maxSoftLimit}.");
            }

            quotaLimit.MaxSoftLimit = maxSoftLimit;
            return quotaLimit;
        }

        /// <summary>
        /// Update the quota type to occupy soft quota when put back cached pipeline run.
        /// AcquireQuotaAsync will return true if the usage entity already exists and won't change quota type.
        /// </summary>
        internal async Task UpdateQuotaUsageToSoftAsync(WorkspaceIdentity workspace, string resourceId)
        {
            try
            {
                await CosmosDbRetryHelper.ExecuteCosmosDbWithRetry(
                func: async () =>
                {
                    using (var cts = new CancellationTokenSource(_requestTimeout))
                    {
                        var usage = await _usageContainer.TryGetAsync<RunningPipelineRunQuotaUsageEntity>(GetUsagePartitionKey(workspace), resourceId, cancellationToken: cts.Token);
                        if (usage == null)
                        {
                            CommonLogger.LogEntityWarning(resourceId, "Failed to get usage entity, it may not exist. Create new usage entity to occupy soft quota. WorkspaceId={workspaceId}, WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}",
                                workspace.WorkspaceId, workspace.WorkspaceName, workspace.ResourceGroupName, workspace.SubscriptionId);
                            usage = new Storable<RunningPipelineRunQuotaUsageEntity>(new RunningPipelineRunQuotaUsageEntity
                            {
                                Id = resourceId,
                                Workspace = workspace,
                                CreatedTimeUtc = DateTime.UtcNow,
                                UsageCount = 1,
                                IsSoftQuota = true,
                            });
                        }
                        else
                        {
                            // Change to acquire soft quota.
                            usage.Entity.IsSoftQuota = true;
                        }

                        // Add or update the usage entity to occupy soft quota.
                        await _usageContainer.PutAsync(GetUsagePartitionKey(workspace), resourceId, usage, cts.Token);

                        CommonLogger.LogEntityInfo(resourceId, "Update the usage entity to occupy soft quota successfully. WorkspaceId={workspaceId}, WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}",
                                workspace.WorkspaceId, workspace.WorkspaceName, workspace.ResourceGroupName, workspace.SubscriptionId);
                        return true;
                    }
                },
                retryPolicy: _retryPolicy).ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                CommonLogger.LogEntityError(resourceId, "Failed to update usage entity to occupy soft quota. WorkspaceId={workspaceId}, WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}. Error: {Exception}",
                    workspace.WorkspaceId, workspace.WorkspaceName, workspace.ResourceGroupName, workspace.SubscriptionId, ex);
                throw;
            }
        }

        private async Task SendMessageToCheckCachedPipelineRunAsync(WorkspaceIdentity workspace, string traceId)
        {
            try
            {
                CommonLogger.LogEntityInfo(traceId, "Try to enqueue a message to check cached pipeline run. WorkspaceId={workspaceId}, WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}",
                    workspace.WorkspaceId, workspace.WorkspaceName, workspace.ResourceGroupName, workspace.SubscriptionId);

                var messageEnvelope = new Envelope<WorkspaceIdentity>(workspace);
                // Set SessionId to workspace id so that a workspace is only handled by one client at a time.
                messageEnvelope.SessionId = workspace.WorkspaceId;
                await _checkCachedTopicPublisher.SendAsync(messageEnvelope).ConfigureAwait(false);

                CommonLogger.LogEntityInfo(traceId, "Enqueue message to check cached pipeline runs successfully. WorkspaceId={workspaceId}, WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}",
                    workspace.WorkspaceId, workspace.WorkspaceName, workspace.ResourceGroupName, workspace.SubscriptionId);
            }
            catch (Exception ex)
            {
                CommonLogger.LogEntityError(traceId, "Failed to enqueue message to check cached pipeline runs. WorkspaceId={workspaceId}, WorkspaceName={workspaceName}, ResourceGroupName={resourceGroupName}, SubscriptionId={subscriptionId}. Error: {Exception}",
                    workspace.WorkspaceId, workspace.WorkspaceName, workspace.ResourceGroupName, workspace.SubscriptionId, ex);
                throw;
            }
        }
    }
}
