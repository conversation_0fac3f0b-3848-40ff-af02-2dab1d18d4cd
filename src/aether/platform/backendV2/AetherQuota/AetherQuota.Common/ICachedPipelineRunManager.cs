﻿// <copyright file="ICachedPipelineRunManager.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Microsoft.Aether.DataContracts.Entity;
using Microsoft.Aether.QuotaService.Shared;
using Microsoft.RelInfra.Storage;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Microsoft.Aether.QuotaService
{
    public interface ICachedPipelineRunManager
    {
        /// <summary>
        /// Get a cached pipeline run of a workspace.
        /// </summary>
        public Task<CachedPipelineRunEntity> GetCachedPipelineRunAsync(WorkspaceIdentity workspace, string cachedId);

        /// <summary>
        /// Add a cached pipeline run of a workspace.
        /// </summary>
        public Task<bool> AddCachedPipelineRunAsync(WorkspaceIdentity workspace, CachedPipelineRunEntity cached);

        /// <summary>
        /// Remove a cached pipeline run of a workspace.
        /// </summary>
        public Task<bool> RemoveCachedPipelineRunAsync(WorkspaceIdentity workspace, string cachedId);

        /// <summary>
        /// For cancelled pipeline run, check if it is cached. 
        /// If so, put the cached pipeline run message back to submission queue, then remove the cached pipeline run. Otherwise it is no-op.
        /// </summary>
        public Task<bool> CancelCachedPipelineRunAsync(WorkspaceIdentity workspace, string cachedId);

        /// <summary>
        /// Get top k cached pipeline run of a workspace with ContinuationToken. Sorting by CachedTimeUtc in ascending order.
        /// </summary>
        public Task<QueryResults<CachedPipelineRunEntity>> GetTopKCachedPipelineRunsAsync(int k, WorkspaceIdentity workspace, string continuationToken = null, int? maxCount = 100);

        /// <summary>
        /// Get all cached pipeline runs of a workspace with ContinuationToken.
        /// </summary>
        public Task<QueryResults<CachedPipelineRunEntity>> GetAllCachedPipelineRunsAsync(WorkspaceIdentity workspace, string continuationToken = null, int? maxCount = 100);

        /// <summary>
        /// Check if there is any cached pipeline run of a workspace.
        /// </summary>
        public Task<bool> HasCachedPipelineRunAsync(WorkspaceIdentity workspace);
    }
}
