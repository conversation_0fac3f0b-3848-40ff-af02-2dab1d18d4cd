﻿// <copyright file="RunningPipelineRunQuotaManager.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Aether.BlueBox.Storage;
using Microsoft.Aether.DataContracts.Entity;
using Microsoft.Aether.QuotaService.Shared;
using Microsoft.RelInfra.Common.RetryExecution.Policy;
using Microsoft.RelInfra.Instrumentation.Logging;
using Microsoft.RelInfra.Storage;

namespace Microsoft.Aether.QuotaService
{
    public class RunningPipelineRunQuotaManager : BaseQuotaManager<RunningPipelineRunQuotaLimitEntity, RunningPipelineRunQuotaUsageEntity>
    {
        private const int MaxRetries = 10;
        private const int MinIntervalInMs = 100;
        private const int MaxIntervalInMs = 1000;

        public RunningPipelineRunQuotaManager(
            QuotaServiceConfig config,
            IQueryableDbStorage limitContainer,
            IQueryableDbStorage usageContainer)
            : base(QuotaType.RunningPipelineRun, config, limitContainer, usageContainer)
        {
            _retryPolicy = new RandomRetryPolicy(
                maxAttempts: MaxRetries,
                minIntervalInMs: MinIntervalInMs,
                maxIntervalInMs: MaxIntervalInMs);
        }

        public override string GetQuotaPartitionKey(WorkspaceIdentity workspace) => workspace.WorkspaceId;

        public override string GetQuotaId(WorkspaceIdentity workspace) => workspace.WorkspaceId;

        public override string GetUsagePartitionKey(WorkspaceIdentity workspace) => workspace.WorkspaceId;

        public override string GetUsageId(RunningPipelineRunQuotaUsageEntity resource) => resource.Id;

        public override RunningPipelineRunQuotaLimitEntity GetDefaultQuotaLimit(WorkspaceIdentity workspace)
        {
            return new RunningPipelineRunQuotaLimitEntity
            {
                Id = workspace.WorkspaceId,
                Workspace = workspace,
                MaxLimit = _serviceConfig.RunningPipelineRunDefaultQuotaLimit,
            };
        }

        protected override async Task<RunningPipelineRunQuotaLimitEntity> VerifyAndGetNewQuotaLimitAsync(WorkspaceIdentity workspace, int maxLimit)
        {
            if (maxLimit < 0)
            {
                throw new ArgumentException($"Quota limit {maxLimit} can not be less than 0.", nameof(maxLimit));
            }

            var quotaLimit = await GetOrUseDefaultQuotaLimitInternalAsync(workspace);
            // Ensure soft quota limit <= hard quota limit if soft limit exists
            if (quotaLimit.MaxSoftLimit.HasValue && maxLimit < quotaLimit.MaxSoftLimit)
            {
                throw new ArgumentException($"New hard quota limit can not be less than current soft quota limit. MaxHardLimit={maxLimit}, MaxSoftLimit={quotaLimit.MaxSoftLimit}.");
            }

            quotaLimit.MaxLimit = maxLimit;
            return quotaLimit;
        }
    }
}
