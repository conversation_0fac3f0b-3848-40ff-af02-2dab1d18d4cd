﻿// <copyright file="UsageStatGenerator.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.Aether.QuotaService.Shared;

namespace Microsoft.Aether.QuotaService
{
    public class UsageStatGenerator
    {
        private readonly TimeSpan _min5 = TimeSpan.FromMinutes(5);
        private readonly TimeSpan _min30 = TimeSpan.FromMinutes(30);
        private readonly TimeSpan _hour1 = TimeSpan.FromHours(1);

        public static string Bucket5Min = "5 min";
        public static string Bucket30Min = "30 min";
        public static string Bucket1Hour = "1 hour";
        public static string Bucket3Hour = "3 hour";

        /// <summary>
        /// Get resource usage in buckets
        /// </summary>
        /// <param name="consumedResources">List of consumed resources</param>
        /// <param name="aggregateFunc">Aggregate function</param>
        /// <param name="userId">User name</param>
        /// <returns>Resource usage in buckets grouped by time slices</returns>
        internal Dictionary<string, int> GetUsageStatistics(IEnumerable<ResourceUsageEntity> consumedResources, Func<ResourceUsageEntity, int> aggregateFunc, string userId = "")
        {
            if (!string.IsNullOrWhiteSpace(userId))
            {
                consumedResources = consumedResources.Where(entity => string.Compare(entity.UserId, userId, StringComparison.OrdinalIgnoreCase) == 0);
            }

            return CalculateQuotaTimeBuckets(consumedResources, aggregateFunc);
        }

        /// <summary>
        /// TODO: change it to return any arbitrary number of buckets
        /// </summary>
        /// <param name="consumedResources"></param>
        /// <param name="agregationFunc"></param>
        /// <returns></returns>
        private Dictionary<string, int> CalculateQuotaTimeBuckets(IEnumerable<ResourceUsageEntity> consumedResources, Func<ResourceUsageEntity, int> agregationFunc)
        {
            int min5Count = 0;
            int min30Count = 0;
            int hour1Count = 0;
            int hour3Count = 0;

            foreach (ResourceUsageEntity quotaResource in consumedResources)
            {
                TimeSpan delta = DateTime.UtcNow - quotaResource.SubmissionTime;

                if (delta > _hour1)
                {
                    hour3Count += agregationFunc(quotaResource);
                }
                else if (delta > _min30)
                {
                    hour3Count += agregationFunc(quotaResource);
                    hour1Count += agregationFunc(quotaResource);
                }
                else if (delta > _min5)
                {
                    hour3Count += agregationFunc(quotaResource);
                    hour1Count += agregationFunc(quotaResource);
                    min30Count += agregationFunc(quotaResource);
                }
                else
                {
                    hour3Count += agregationFunc(quotaResource);
                    hour1Count += agregationFunc(quotaResource);
                    min30Count += agregationFunc(quotaResource);
                    min5Count += agregationFunc(quotaResource);
                }
            }

            return new Dictionary<string, int>
            {
                { Bucket5Min, min5Count },
                { Bucket30Min, min30Count },
                { Bucket1Hour, hour1Count },
                { Bucket3Hour, hour3Count },
            };
        }
    }
}