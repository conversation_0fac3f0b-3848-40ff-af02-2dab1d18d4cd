﻿// <copyright file="ExperimentQueuePublisher.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Azure;
using Microsoft.Aether.DataContracts.Entity;
using Microsoft.Aether.TaggedQueueManager;
using Microsoft.Aether.TaggedQueueManager.CloudTags;
using Microsoft.RelInfra.Common.RetryPolicy;
using Microsoft.RelInfra.Instrumentation;
using Microsoft.RelInfra.Instrumentation.Logging;
using Microsoft.RelInfra.Storage;
using Microsoft.WindowsAzure.Storage;
using System;
using System.Collections.Generic;
using System.Net;
using System.Threading.Tasks;

namespace Microsoft.Aether.QuotaService
{
    /// <summary>
    /// Enqueue pipeline run into experiment queue which NodeProcessor will dequeue and process.
    /// </summary>
    public class ExperimentQueuePublisher : IExperimentQueuePublisher
    {
        private const int MaxRetries = 5;
        private readonly IRelInfraRetryPolicy _retryPolicy;
        private readonly ITaggedQueueManager<AssignedQueueIdTags, string> _taggedQueueManager;
        private readonly CounterManager _counterManager;

        public ExperimentQueuePublisher(ITaggedQueueManager<AssignedQueueIdTags, string> queueManager, CounterManager counterManager)
        {
            _taggedQueueManager = queueManager;
            _counterManager = counterManager;
            _retryPolicy = new ExponentialBackoffRetryPolicy(MaxRetries);
        }

        public async Task EnqueueExperimentAsync(WorkspaceIdentity workspace, string experimentId, string queueMessage)
        {
            CommonLogger.LogEntityInfo(experimentId, $"Try to put back the experiment into queue. WorkspaceId={workspace.WorkspaceId}, WorkspaceName={workspace.WorkspaceName}, ResourceGroupName={workspace.ResourceGroupName}, SubscriptionId={workspace.SubscriptionId}");
            var metricsDimensions = new Dictionary<string, string>
            {
                { nameof(workspace.WorkspaceName), workspace.WorkspaceName },
                { nameof(workspace.WorkspaceId), workspace.WorkspaceId },
                { nameof(workspace.SubscriptionId), workspace.SubscriptionId },
                { nameof(workspace.ResourceGroupName), workspace.ResourceGroupName },
            };

            var tagsDescriptor = new AssignedQueueIdDescriptor
            {
                QueueId = QueueAssignmentHelper.GetQueueId(workspace.SubscriptionId)
            };

            try
            {
                await RetryExecution.ExecuteFuncWithRetryAsync(
                    action: async () =>
                    {
                        var taggedQueue = await _taggedQueueManager.GetOrCreateQueueAsync(tagsDescriptor);
                        CommonLogger.LogEntityInfo(experimentId, $"Queueing experiment to tagged queue {taggedQueue.Queue.QueueName}. WorkspaceId={workspace.WorkspaceId}, WorkspaceName={workspace.WorkspaceName}, ResourceGroupName={workspace.ResourceGroupName}, SubscriptionId={workspace.SubscriptionId}");

                        try
                        {
                            await taggedQueue.Queue.PutAsync(message: new Storable<string>(queueMessage));
                        }
                        catch (StorageException ex) when (ex.RequestInformation?.HttpStatusCode == (int)HttpStatusCode.NotFound)
                        {
                            CommonLogger.LogEntityError(experimentId, $"Tagged experiment queue {taggedQueue.Queue.QueueName} not found, will retry and reinitialize the queue. WorkspaceId={workspace.WorkspaceId}, WorkspaceName={workspace.WorkspaceName}, ResourceGroupName={workspace.ResourceGroupName}, SubscriptionId={workspace.SubscriptionId}");
                            _taggedQueueManager.RemoveCachedQueue(taggedQueue);
                            throw;
                        }
                        catch (RequestFailedException ex) when (ex.Status == (int)HttpStatusCode.NotFound)
                        {
                            CommonLogger.LogEntityError(experimentId, $"Tagged experiment queue {taggedQueue.Queue.QueueName} not found, will retry and reinitialize the queue. WorkspaceId={workspace.WorkspaceId}, WorkspaceName={workspace.WorkspaceName}, ResourceGroupName={workspace.ResourceGroupName}, SubscriptionId={workspace.SubscriptionId}");
                            _taggedQueueManager.RemoveCachedQueue(taggedQueue);
                            throw;
                        }

                        CommonLogger.LogEntityInfo(experimentId, $"Put back the experiment into queue successfully. WorkspaceId={workspace.WorkspaceId}, WorkspaceName={workspace.WorkspaceName}, ResourceGroupName={workspace.ResourceGroupName}, SubscriptionId={workspace.SubscriptionId}");
                        _counterManager.GetNumberCounter(Constants.PutBackCachedPipelineRunSuccessNumberCounter, customDimensions: metricsDimensions).Set(1);
                        return true;
                    },
                    exceptionHandler: null,
                    retryPolicy: _retryPolicy);
            }
            catch (Exception ex)
            {
                CommonLogger.LogEntityError(experimentId, $"Failed to put back the experiment into queue. WorkspaceId={workspace.WorkspaceId}, WorkspaceName={workspace.WorkspaceName}, ResourceGroupName={workspace.ResourceGroupName}, SubscriptionId={workspace.SubscriptionId}. Error: {ex}");
                _counterManager.GetNumberCounter(Constants.PutBackCachedPipelineRunFailureNumberCounter, customDimensions: metricsDimensions).Set(1);
                throw;
            }
        }
    }
}
