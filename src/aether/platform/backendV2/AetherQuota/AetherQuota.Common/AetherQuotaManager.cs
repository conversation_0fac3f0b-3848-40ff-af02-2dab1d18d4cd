﻿// <copyright file="AetherQuotaManager.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Aether.QuotaService.Shared;
using Microsoft.RelInfra.Instrumentation.Logging;
using Microsoft.RelInfra.Storage;
using Microsoft.RelInfra.Storage.Exceptions;

namespace Microsoft.Aether.QuotaService
{
    public class AetherQuotaManager
    {
        private readonly IQuotaRoiClient _quotaRoiClient;
        private const int MaxConflictRetries = 10;

        public AetherQuotaManager(IQuotaRoiClient quotaRoiClient)
        {
            _quotaRoiClient = quotaRoiClient;
        }

        public virtual async Task<QuotaReservationResponse> ReserveTeamUserQuotaAsync(string userId, string resourceId, int resourceCount, ResourceManager resourceManager, int userResourceUsageLimit, HashSet<string> quotaExemptUsers, string teamId, TimeSpan expirationInterval)
        {
            string userAlias = ExtractUserAlias(userId);
            string userTeam = await _quotaRoiClient.GetUserTeamAsync(userAlias, teamId);

            CommonLogger.LogEntityInfo(resourceId,
               $"Reserving team resources quota - team:{userTeam}, user:{userId}, resourceCount: {resourceCount}");

            return await UpdateQuotaAsync(userId, resourceId, resourceCount, resourceManager, UsageStatGenerator.Bucket5Min, userResourceUsageLimit, quotaExemptUsers, aggregateResourceId: userTeam, expirationInterval: expirationInterval, incrementOnExisting: false);
        }

        public virtual async Task<ResourceUsageEntity> GetUserResourceUsageAsync(string userId, string resourceId, ResourceManager resourceManager)
        {
            var allUsage = await resourceManager.GetAggregatedResourceUsageEntityAsync(userId);
            var resourceUsage = allUsage.Entity.ConsumedResources.FirstOrDefault(
                    usageEntry => string.Compare(usageEntry.Id, resourceId, StringComparison.OrdinalIgnoreCase) == 0);

            return resourceUsage;
        }

        public virtual async Task<QuotaReservationResponse> IncrementUserQuotaAsync(string userId, string resourceId, int incrementResourceCount, ResourceManager resourceManager, int userResourceUsageLimit, HashSet<string> quotaExemptUsers, TimeSpan expirationInterval)
        {
            CommonLogger.LogEntityInfo(resourceId,
               $"Reserving user resources quota - user:{userId}, resourceCount: {incrementResourceCount}");

            var quotaResponse = await UpdateQuotaAsync(userId, resourceId, incrementResourceCount, resourceManager, UsageStatGenerator.Bucket3Hour, userResourceUsageLimit, quotaExemptUsers, aggregateResourceId: userId, expirationInterval: expirationInterval, incrementOnExisting: true);
            quotaResponse.TeamResourceUsageStat = null;
            return quotaResponse;
        }

        public virtual async Task<QuotaReservationResponse> ReleaseUserQuotaAsync(string userId, string resourceId, ResourceManager resourceManager, HashSet<string> quotaExemptUsers, TimeSpan expirationInterval)
        {
            CommonLogger.LogEntityInfo(resourceId,
               $"Releasing user resources quota - user:{userId}");

            var quotaResponse = await UpdateQuotaAsync(userId, resourceId, resourceCount: 0, resourceManager: resourceManager, bucket: UsageStatGenerator.Bucket3Hour, userResourceUsageLimit: null, quotaExemptUsers: quotaExemptUsers, aggregateResourceId: userId, expirationInterval: expirationInterval, incrementOnExisting: false);
            quotaResponse.TeamResourceUsageStat = null;
            return quotaResponse;
        }

        public virtual async Task<QuotaReservationResponse> ReserveUserQuotaAsync(string userId, string resourceId, int resourceCount, ResourceManager resourceManager, int userResourceUsageLimit, HashSet<string> quotaExemptUsers, TimeSpan expirationInterval)
        {
            CommonLogger.LogEntityInfo(resourceId,
               $"update user resources quota - user:{userId}, resourceCount: {resourceCount}");

            var quotaResponse = await UpdateQuotaAsync(userId, resourceId, resourceCount, resourceManager, UsageStatGenerator.Bucket3Hour, userResourceUsageLimit, quotaExemptUsers, aggregateResourceId: userId, expirationInterval: expirationInterval, incrementOnExisting: false);
            quotaResponse.TeamResourceUsageStat = null;
            return quotaResponse;
        }

        private async Task<QuotaReservationResponse> UpdateQuotaAsync(string userId, string resourceId, int resourceCount, ResourceManager resourceManager, string bucket, int? userResourceUsageLimit, HashSet<string> quotaExemptUsers, string aggregateResourceId, TimeSpan expirationInterval, bool incrementOnExisting)
        {
            if (string.IsNullOrWhiteSpace(userId))
            {
                throw new ArgumentNullException(nameof(userId));
            }

            if (string.IsNullOrWhiteSpace(resourceId))
            {
                throw new ArgumentNullException(nameof(resourceId));
            }

            bool shouldRetry;
            bool isQuotaAvailable;
            Dictionary<string, int> teamResourceUsageStat;
            Dictionary<string, int> userResourceUsageStat;
            Dictionary<string, int> teamResourceUsageStatOrig;
            Dictionary<string, int> userResourceUsageStatOrig;
            Storable<AggregatedResourcesUsageEntity> allUsageStorable;
            int? availableResource = null;

            int retries = 0;

            do
            {
                shouldRetry = false;
                // NOTE: How quota reservation works:
                // 1. Get team usage entry
                // 2. Remove already expired submissions
                // 3. Verify if submission info has been added already
                // 4. If not - add new submission now
                // 5. Decide if submission is actually possible (no limits set yet)
                // 6. update storage
                // 7. return reservation

                // 1. Get team usage entry
                allUsageStorable = await resourceManager.GetAggregatedResourceUsageEntityAsync(aggregateResourceId);
                AggregatedResourcesUsageEntity allUsageEntity = allUsageStorable.Entity;
                allUsageEntity.ConsumedResources = allUsageEntity.ConsumedResources ?? new List<ResourceUsageEntity>();

                // 2.Remove already expired submissions
                allUsageEntity.ConsumedResources = allUsageEntity.ConsumedResources.
                    Where(entity => entity.SubmissionTime + expirationInterval > DateTime.UtcNow).ToList();

                UsageStatGenerator statGeneratorOrig = new UsageStatGenerator();
                userResourceUsageStatOrig = statGeneratorOrig.GetUsageStatistics(allUsageStorable.Entity.ConsumedResources, resource => resource.ResourceCount, userId);
                teamResourceUsageStatOrig = statGeneratorOrig.GetUsageStatistics(allUsageStorable.Entity.ConsumedResources, resource => resource.ResourceCount);

                // 3.Verify if submission info has been added already
                ResourceUsageEntity resourceUsageEntity = allUsageEntity.ConsumedResources.FirstOrDefault(
                    usageEntry => string.Compare(usageEntry.Id, resourceId, StringComparison.OrdinalIgnoreCase) == 0);

                // 4. fresh request, not found previously
                if (resourceUsageEntity == null)
                {
                    if (resourceCount > 0)
                    {
                        allUsageEntity.ConsumedResources.Add(new ResourceUsageEntity
                        {
                            Id = resourceId,
                            ResourceCount = resourceCount,
                            SubmissionTime = DateTime.UtcNow,
                            UserId = userId,
                        });
                    }
                }
                else if (incrementOnExisting)
                {
                    resourceUsageEntity.ResourceCount += resourceCount;
                }
                else
                {
                    if (resourceCount > 0)
                    {
                        resourceUsageEntity.ResourceCount = resourceCount;
                    }
                    else
                    {
                        allUsageEntity.ConsumedResources.Remove(resourceUsageEntity);
                    }
                }

                // 5. Decide if quota is available and submission is possible
                UsageStatGenerator statGenerator = new UsageStatGenerator();
                userResourceUsageStat = statGenerator.GetUsageStatistics(allUsageStorable.Entity.ConsumedResources, resource => resource.ResourceCount, userId);
                teamResourceUsageStat = statGenerator.GetUsageStatistics(allUsageStorable.Entity.ConsumedResources, resource => resource.ResourceCount);

                isQuotaAvailable = quotaExemptUsers.Contains(userId) || userResourceUsageLimit == null ||
                    userResourceUsageStat[bucket] <= userResourceUsageLimit;

                if (userResourceUsageLimit != null)
                {
                    if (isQuotaAvailable)
                    {
                        availableResource = userResourceUsageLimit - userResourceUsageStat[bucket];
                    }
                    else
                    {
                        availableResource = userResourceUsageLimit - userResourceUsageStatOrig[bucket];
                    }
                }

                // 6. update storage
                if (isQuotaAvailable)
                {
                    try
                    {
                        await resourceManager.PutAggregatedResourceUsageEntityAsync(allUsageStorable);
                        CommonLogger.LogEntityInfo(resourceId, $"quota for {aggregateResourceId} assigned {resourceCount}, current usage {resourceUsageEntity?.ResourceCount}");
                    }
                    catch (RelInfraStorageException e) when (e.Error == StorageError.PreconditionFailed && retries < MaxConflictRetries)
                    {
                        shouldRetry = true;
                        CommonLogger.LogEntityWarning(resourceId, "conflict in updating quota, retrying for the {retry_count}th time. Error: {exception_message}, Stack Trace: {stack_trace}", retries++, e.Message, e.StackTrace);
                    }
                }
            }
            while (shouldRetry);

            // 7. return reservation info back
            return new QuotaReservationResponse
            {
                IsQuotaAvailable = isQuotaAvailable,
                TeamId = allUsageStorable.Entity.Id,
                UserId = userId,
                TeamResourceUsageStat = isQuotaAvailable ? teamResourceUsageStat : teamResourceUsageStatOrig,
                UserResourceUsageStat = isQuotaAvailable ? userResourceUsageStat : userResourceUsageStatOrig,
                ResourceId = resourceId,
                QuotaLimit = userResourceUsageLimit,
                AvailableResource = availableResource
            };
        }

        private string ExtractUserAlias(string userId)
        {
            if (string.IsNullOrWhiteSpace(userId))
            {
                throw new ArgumentNullException(nameof(userId), "can't be null or empty");
            }

            int idxFirstUnderscore = userId.IndexOf("_", StringComparison.OrdinalIgnoreCase);
            if (idxFirstUnderscore > 0)
            {
                return userId.Substring(idxFirstUnderscore + 1);
            }

            return userId;
        }
    }
}