﻿// <copyright file="IQuotaRoiClient.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Collections.Generic;
using System.Threading.Tasks;

namespace Microsoft.Aether.QuotaService
{
    public interface IQuotaRoiClient
    {
        Task<Dictionary<string, string>> GetTeamsAsync();

        Task<string> GetUserTeamAsync(string userName, string teamId);
    }
}