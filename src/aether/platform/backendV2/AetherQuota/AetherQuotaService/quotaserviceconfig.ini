
[Service]
Name=AetherQuotaService
Url=http://localhost
Port=10205
k8SEnvType:onebox$Port=80
isK8s:true$Port=443
MaxReceivedMessageSize=52428800

SslCertSecret=keyvault://certificate--bbquotaservice--pfx

[CosmosDb]
DatabaseName=QuotaService
ConnectionCacheDuration=50.00:00:00
RequestTimeout=00:00:03
; Use default RU 400 for dev containers.
; And prod containers is configured with autoscale and no need to specify throughput. 
ContainerThroughput=
RunningPipelineRunQuotaLimitContainerName=RunningPipelineRunQuotaLimit
RunningPipelineRunQuotaUsageContainerName=RunningPipelineRunQuotaUsage
CachedPipelineRunContainerName=CachedPipelineRun

[AzureStorage]
ConnectionFileName=AEtherXRayServices\QuotaAzureConnection.encr
k8SEnvType:onebox$ConnectionFileName=keyvault://aether3pcloudmanagerkey
k8sEnvType:dev$ConnectionFileName=keyvault://aether3pcloudmanagerkey
k8sEnvType:ppe$ConnectionFileName=keyvault://aether3pcloudmanagerkey
k8sEnvType:prod$ConnectionFileName=keyvault://aether3pcloudmanagerkey

Environment:Aether-Backend-Prod-VE$BlobContainerName=prodaetherjobsquotas
Environment:Aether-Backend-PPE-VE$BlobContainerName=devaetherjobsquotas
Environment:Aether-Backend-Dev-VE$BlobContainerName=devaetherjobsquotas
devmachine$BlobContainerName=devaetherjobsquotas
isK8s:true$BlobContainerName=jobsquotas
BlobContainerName=

Environment:Aether-Backend-Prod-VE$UserRunningJobsBlobContainerName=prodrunningjobsquotas
Environment:Aether-Backend-PPE-VE$UserRunningJobsBlobContainerName=devrunningjobsquotas
Environment:Aether-Backend-Dev-VE$UserRunningJobsBlobContainerName=devrunningjobsquotas
devmachine$UserRunningJobsBlobContainerName=devrunningjobsquotas
isK8s:true$UserRunningJobsBlobContainerName=runningjobsquotas
UserRunningJobsBlobContainerName=

BlobRetriesNumber=3
MaxBlobsToListForQuotaStat=50
BlobRetryInterval=00:00:05
BlobTimeoutInterval=00:00:30

BlobLeaseContainerName=qsleaselock

[QuotaSettings]
RunningPipelineRunDefaultQuotaLimit=20000
RunningPipelineRunDefaultSoftQuotaLimit=5000
RunningPipelineRunReviseTaskTimeout=00:30:00
RunningPipelineRunReviseTaskInterval=01:00:00

TeamExperimentsQuotaPrefix=jq-
UserRunningJobsQuotaPrefix=uj-
TeamEmailsQuotaPrefix=em-
TeamPromotionsQuotaPrefix=pr-
TeamUploadsQuotaPrefix=up-
SubmissionExpirationInterval=03:00:00
CacheExpirationInterval=00:15:00
MaxParallelismDegree=10
;quota limits & exemptions
; submittednodes limit is how many nodes per user can be submitted through experimentsubmitter
SubmittedNodesLimit=30000
Environment:Aether-Backend-Dev-VE$SubmittedNodesLimit=150
; running nodes limit is how many nodes per user can be running in cloudlets
RunningNodesLimit=500
RunningJobExpirationInterval=2.00:00:00
NodesExemption=none
EmailsLimit=300
Environment:Aether-Backend-Dev-VE$EmailsLimit=10
EmailsExemption=nodeprocessor
UploadsLimit=300
Environment:Aether-Backend-Dev-VE$UploadsLimit=10
UploadsExemption=none
PromotionsLimit=300
Environment:Aether-Backend-Dev-VE$PromotionsLimit=10
PromotionsExemption=none

[ServiceBus]
MessageWaitTimeout=00:01:00
MaxConcurrentSessions=50
MaxAutoRenewDuration=00:05:00

; Inherit ExperimentQueue prefix and connection string from storage.ini
[AzureQueueManager]
UpdatePeriod=00:10:00

[LogRules]
Rule1=*|~Netlib|~ApCommClientServer|~TracedEvents|~CentralLoggerBackup,*,*,localAetherQuotaService
Rule2=*|~Netlib|~ApCommClientServer|~TracedEvents|~CentralLoggerBackup,EWS,*,collectorAetherQuotaService
Rule3=TracedEvents,*,*,localAetherQuotaServiceTracedEvents
Rule4=TracedEvents,EWS,*,collectorAetherQuotaServiceTracedEvents
Rule5=CentralLoggerBackup,*,*,localCentralLoggerBackupgg

[localAetherQuotaService]
FileNameBase=local\AetherQuotaService
MaxFiles=100
MaxFileSize=10000000
BufferSize=10000

[collectorAetherQuotaService]
FileNameBase=collector\AetherQuotaService
MaxFiles=100
MaxFileSize=10000000
BufferSize=10000

[localAetherQuotaServiceTracedEvents]
FileNameBase=local\AetherQuotaServiceTracedEvents
MaxFiles=100
MaxFileSize=10000000
BufferSize=10000

[collectorAetherQuotaServiceTracedEvents]
FileNameBase=collector\AetherQuotaServiceTracedEvents
MaxFiles=100
MaxFileSize=10000000
BufferSize=10000

[localCentralLoggerBackup]
FileNameBase=local\AetherQuotaService_LoggerBackup
MaxFiles=100
MaxFileSize=10000000
BufferSize=10000
