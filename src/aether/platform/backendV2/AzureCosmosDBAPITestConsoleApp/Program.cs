﻿
using Microsoft.Aether.DataContracts;
using Microsoft.Aether.TokenProvider;
using Microsoft.Azure.Documents.Client;
using Microsoft.RelInfra.Common.Serialization;
using Microsoft.RelInfra.Instrumentation;
using Microsoft.RelInfra.Instrumentation.Logging;
using Microsoft.RelInfra.Instrumentation.Test.Mocks;
using Relinfra.Storage.CosmosDb;
using static Aether.Backend.BlueBox.WorkspaceCleanupService.Events.WorkspaceStateChangeHandler;

namespace AzureCosmosDBAPITestConsoleApp
{
    public class Program
    {
        /*
         * This program is playground for cosmosdb access & operation
         * This could be used for dev db documents cleanup. 
         * 1) Please test the search query first to make sure the targeted records are accurately defined by query
         * 2) Make sure store procedure "BulkDeleteEntities" is created on database>Collection
         *    StoreProcedure link: https://msdata.visualstudio.com/Vienna/_git/vienna?path=/src/aether/platform/backendV2/BlueBox/StorageCleanup/StorageCleanupService/StoredProcedures/BulkDeleteEntities.js&_a=contents&version=GBmaster
         *    StoreProcedure usage in cosmos: https://h-savran.blogspot.com/2019/02/creating-and-executing-cosmos-db-stored.html
         * 3) create your own delete method and execute in main
         */

        public Program()
        {
        }

        static async Task Main()
        {
            Program program = new Program();
            //await program.TestSampleQuerySuite();
            await program.TestSampleBulkDeleteSuite();
        }

        private AzureCosmosDbCollection GetDevDBCollection(string databaseName, string collectionName)
        {
            var collection = new AzureCosmosDbCollection(
                endpointUrl: AzureCredHelper.GetDevCosmosDbUrl(),
                authToken: AzureCredHelper.GetDevCosmosDbKey(),
                databaseName: databaseName,
                collectionName: collectionName,
                createDefaultPartitions: false,
                counterManager: new CounterManager("test", new MockCounterFactory()),
                protocol: Protocol.Tcp);

            Console.WriteLine($"Created db connection to DatabaseName={databaseName}, collectionName={collectionName}");
            return collection;
        }

        /// <summary>
        /// Simple method for bulk delete in cosmosdb
        /// </summary>
        /// <param name="dbCollection"></param>
        /// <param name="partitionKey">PartitionKey, Can not be null for delete. But you could pass emptystring to ignore this filter</param>
        /// <param name="query">query sentence (exclude partitionKey)</param>
        /// <returns></returns>
        private async Task BulkDeleteAsync(AzureCosmosDbCollection dbCollection, string partitionKey, string query)
        {
            while (true)
            {
                BulkDeleteResponseDto result = await dbCollection.ExecuteStoredProcedureAsync<BulkDeleteResponseDto>(
                                                         partitionKey: partitionKey,
                                                         storedProcedure: "BulkDeleteEntities",
                                                         procedureParams: new dynamic[] { query });
                Console.WriteLine($"Deleting entities in {dbCollection.CollectionName}, deleted entries number = " + result.Deleted);
                if (result.Continuation != "true")
                {
                    break;
                }
            }
        }

        /// <summary>
        /// Simple method for direct cosmosDB query
        /// </summary>
        /// <typeparam name="T">The contract for DB collection</typeparam>
        /// <param name="dbCollection">DB Collection Name</param>
        /// <param name="partitionKey">DB Partition Key; The partitionKey could be null for query</param>
        /// <param name="query">query sentence (exclude partitionKey)</param>
        /// <returns>List of the Document Entities</returns>
        private async Task<IList<T>> QueryAsync<T>(AzureCosmosDbCollection dbCollection, string partitionKey, string query)
        {
            var res = await dbCollection.ExecuteQueryAsync<T>(partitionKey: partitionKey, query: query, continuationToken: null, enableQueryScan: false, takeCount: null, cancellationToken: CancellationToken.None);
            var entities = res.Results.Select(result => result.Entity).ToList();
            return entities;
        }

        private async Task TestSampleBulkDeleteSuite()
        {
            await TestSampleBulkDelete_withPartitionkey();
            await TestSampleBulkDelete_withoutPartitionkey();
            await TestSampleBulkDelete_PartitionKeyOnly();
        }

        private async Task TestSampleBulkDelete_withPartitionkey()
        {
            var dbCollection_schedule = GetDevDBCollection("MetaStore", "Schedule");
            string partitionKey = "92e7226d-7c9e-4edc-8d44-bbb3c1caf2de";
            /*
             * General Form without filter
             * string query = "SELECT * from c";
             */
            string query = "SELECT * FROM c where c.id = 'cf7c94f6-1314-4c7b-ab7d-6ad9b190b8b8'";

            await BulkDeleteAsync(
                dbCollection: dbCollection_schedule,
                partitionKey: partitionKey,
                query: query);
        }

        private async Task TestSampleBulkDelete_withoutPartitionkey()
        {
            var dbCollection_schedule = GetDevDBCollection("MetaStore", "Schedule");
            //ignore the partionKey field
            string partitionKey = string.Empty;
            string query = "SELECT * FROM c where c.id = '05896ff8-7119-4667-a264-12c583d0e50d'";

            await BulkDeleteAsync(
                dbCollection: dbCollection_schedule,
                partitionKey: partitionKey,
                query: query);
        }

        private async Task TestSampleBulkDelete_PartitionKeyOnly()
        {
            var dbCollection_schedule = GetDevDBCollection("MetaStore", "Schedule");
            //ignore the partionKey field
            string partitionKey = "92e7226d-7c9e-4edc-8d44-bbb3c1caf2de";
            string query = "SELECT * FROM c";

            await BulkDeleteAsync(
                dbCollection: dbCollection_schedule,
                partitionKey: partitionKey,
                query: query);
        }

        private async Task TestSampleQuerySuite()
        {
            await TestSampleQuery_withoutPartitionkey();
            await TestSampleQuery_Partitionkey();
        }

        private async Task TestSampleQuery_withoutPartitionkey()
        {
            var dbCollection_schedule = GetDevDBCollection("MetaStore", "Schedule");
            //ignore the PartitionKey filter
            string partitionKey = null;
            string query = "SELECT * FROM c where c.PipelineSubmissionInfo.ExperimentName = 'Schedule_Experiment2'";

            var entities = await QueryAsync<PipelineScheduleEntity>(
                dbCollection: dbCollection_schedule,
                partitionKey: partitionKey,
                query: query);

            foreach (var entity in entities)
            {
                Console.WriteLine(SerializationHelpers.SerializeEntity<PipelineScheduleEntity>(entity));
            }

            Console.WriteLine($"Total {entities.Count} records returned");
        }

        private async Task TestSampleQuery_Partitionkey()
        {
            var dbCollection_schedule = GetDevDBCollection("MetaStore", "Schedule");
            string partitionKey = "92e7226d-7c9e-4edc-8d44-bbb3c1caf2de";
            string query = "SELECT * from c";

            var entities = await QueryAsync<PipelineScheduleEntity>(
                dbCollection: dbCollection_schedule,
                partitionKey: partitionKey,
                query: query);

            foreach (var entity in entities)
            {
                Console.WriteLine(SerializationHelpers.SerializeEntity<PipelineScheduleEntity>(entity));
            }

            Console.WriteLine($"Total {entities.Count} records returned");
        }

    }
}
