﻿using System;
using System.Threading.Tasks;
using Microsoft.RelInfra.Common.Configuration;
using Microsoft.RelInfra.Common.Secrets;

namespace Aether.Backend.CacheClient
{
    public class RedisCacheConfiguration : NestedVerifiableConfig
    {
        private readonly SecretProvider _secretProvider;

        public RedisCacheConfiguration(IRelInfraConfiguration config, SecretProvider secretProvider)
            : base(config, configName: nameof(RedisCacheConfiguration))
        {
            _secretProvider = secretProvider;
        }

        public virtual Task<string> GetConnectionStringAsync()
        {
            return Config.GetSecretStringAsync(_secretProvider, "RedisCache.ConnectionStringFileName");
        }

        public int RetryCount => Config.GetInteger("RedisCache.RetryAttempts");
        public TimeSpan RetryInterval => Config.GetTimeSpan("RedisCache.RetryIntervalTimeSpan");
        public TimeSpan ValidityPeriod => Config.GetTimeSpan("RedisCache.ValidityPeriodTimeSpan");
    }
}
