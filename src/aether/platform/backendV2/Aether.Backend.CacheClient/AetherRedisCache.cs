﻿using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Newtonsoft.Json;
using System;
using System.Threading;
using StackExchange.Redis;
using Microsoft.RelInfra.Common;
using Microsoft.RelInfra.Common.RetryExecution;
using Microsoft.RelInfra.Common.RetryExecution.Policy;
using Microsoft.RelInfra.Common.RetryPolicy;
using Microsoft.RelInfra.Instrumentation;

namespace Aether.Backend.CacheClient
{
    public class AetherRedisCache : IAetherRedisCache
    {
        private readonly IDatabase _cache;
        private readonly IRetryPolicy _retryPolicy;
        private readonly CounterManager _counterManager;

        public AetherRedisCache(IDatabase cache, CounterManager counterManager, IRetryPolicy retryPolicy)
        {
            _cache = cache;
            _retryPolicy = retryPolicy;
            _counterManager = counterManager;
        }

        public static async Task<IAetherRedisCache> CreateAsync(RedisCacheConfiguration configuration, CounterManager counterManager, IRetryPolicy retryPolicy = null)
        {
            retryPolicy = retryPolicy ?? new RedisCacheRetryPolicy(configuration.RetryInterval, configuration.RetryCount);
            ConnectionMultiplexer connection = await ConnectionMultiplexer.ConnectAsync(await configuration.GetConnectionStringAsync());
            return new AetherRedisCache(connection.GetDatabase(), counterManager, retryPolicy);
        }

        public async Task StoreAsync<T>(string key, T value)
        {
            _counterManager.GetRateCounter("RedisCache.StoreRate").Increment();

            using (_counterManager.GetLatencyCounter("RedisCache.StoreLatency").StartCounter())
            {
                try
                {
                    string val = JsonConvert.SerializeObject(value);
                    await RetryExecutionHelper.ExecuteFuncWithRetryForAnyExceptionAsync(
                        function: async () => await _cache.StringSetAsync(key, val),
                        retryPolicy: _retryPolicy,
                        token: CancellationToken.None);
                }
                catch
                {
                    _counterManager.GetRateCounter("RedisCache.StoreErrorRate").Increment();
                    throw;
                }
            }
        }

        public async Task StoreWithExpirationAsync<T>(string key, T value, TimeSpan validityPeriod)
        {
            _counterManager.GetRateCounter("RedisCache.StoreRate").Increment();

            using (_counterManager.GetLatencyCounter("RedisCache.StoreLatency").StartCounter())
            {
                try
                {
                    string val = JsonConvert.SerializeObject(value);
                    await RetryExecutionHelper.ExecuteFuncWithRetryForAnyExceptionAsync(
                        function: async () => await _cache.StringSetAsync(key, val, validityPeriod),
                        retryPolicy: _retryPolicy,
                        token: CancellationToken.None);
                }
                catch 
                {
                    _counterManager.GetRateCounter("RedisCache.StoreErrorRate").Increment();
                    throw;
                }
            }
        }

        public async Task<T> LoadAsync<T>(string key)
        {
            _counterManager.GetRateCounter("RedisCache.LoadRate").Increment();

            using (_counterManager.GetLatencyCounter("RedisCache.LoadLatency").StartCounter())
            {
                try
                {
                    RedisValue result = await RetryExecutionHelper.ExecuteFuncWithRetryForAnyExceptionAsync(
                        function: async () => await _cache.StringGetAsync(key),
                        retryPolicy: _retryPolicy,
                        token: CancellationToken.None);
                    if (result.IsNull)
                    {
                        return default(T);
                    }

                    return JsonConvert.DeserializeObject<T>(result);
                }
                catch
                {
                    _counterManager.GetRateCounter("RedisCache.LoadErrorRate").Increment();
                    throw;
                }
            }
        }

        public async Task<T[]> BulkLoadAsync<T>(IEnumerable<string> keys)
        {
            _counterManager.GetRateCounter("RedisCache.BulkLoadRate").Increment();

            using (_counterManager.GetLatencyCounter("RedisCache.BulkLoadLatency").StartCounter())
            {
                try
                {
                    RedisKey[] paths = keys.Select(key => (RedisKey)key).ToArray();

                    RedisValue[] results = await RetryExecutionHelper.ExecuteFuncWithRetryForAnyExceptionAsync(
                        function: async () => await _cache.StringGetAsync(paths),
                        retryPolicy: _retryPolicy,
                        token: CancellationToken.None);

                    return results.Select(value => value.IsNull ? default(T) : JsonConvert.DeserializeObject<T>(value))
                        .ToArray();

                }
                catch
                {
                    _counterManager.GetRateCounter("RedisCache.BulkLoadErrorRate").Increment();
                    throw;
                }
            }
        }

        public async Task BulkStoreAsync<T>(IEnumerable<KeyValuePair<string, T>> values)
        {
            _counterManager.GetRateCounter("RedisCache.BulkStoreRate").Increment();

            using (_counterManager.GetLatencyCounter("RedisCache.BulkStoreLatency").StartCounter())
            {
                try
                {
                    KeyValuePair<RedisKey, RedisValue>[] cacheValues =
                        values.Select(pair => new KeyValuePair<RedisKey, RedisValue>(pair.Key, JsonConvert.SerializeObject(pair.Value)))
                            .ToArray();

                    await RetryExecutionHelper.ExecuteFuncWithRetryForAnyExceptionAsync(
                        function: async () => await _cache.StringSetAsync(cacheValues),
                        retryPolicy: _retryPolicy,
                        token: CancellationToken.None);

                }
                catch
                {
                    _counterManager.GetRateCounter("RedisCache.BulkStoreErrorRate").Increment();
                    throw;
                }
                
            }
        }

        public async Task RemoveAsync(string key)
        {
            _counterManager.GetRateCounter("RedisCache.RemoveRate").Increment();

            using (_counterManager.GetLatencyCounter("RedisCache.RemoveLatency").StartCounter())
            {
                try
                {
                    await RetryExecutionHelper.ExecuteFuncWithRetryForAnyExceptionAsync(
                        function: async () => await _cache.KeyDeleteAsync(key),
                        retryPolicy: _retryPolicy,
                        token: CancellationToken.None);

                }
                catch
                {
                    _counterManager.GetRateCounter("RedisCache.RemoveErrorRate").Increment();
                    throw;
                }
                
            }
        }

        public async Task StoreToListAsync<T>(string key, T value)
        {
            _counterManager.GetRateCounter("RedisCache.StoreToListRate").Increment();

            using (_counterManager.GetLatencyCounter("RedisCache.StoreToListLatency").StartCounter())
            {
                try
                {
                    string val = JsonConvert.SerializeObject(value);
                    await RetryExecutionHelper.ExecuteFuncWithRetryForAnyExceptionAsync(
                        function: async () => await _cache.SetAddAsync(key, val),
                        retryPolicy: _retryPolicy,
                        token: CancellationToken.None);
                }
                catch
                {
                    _counterManager.GetRateCounter("RedisCache.StoreToListErrorRate").Increment();
                    throw;
                }                
            }
        }

        public async Task StoreToListWithExpirationAsync<T>(string key, T value, TimeSpan validityPeriod)
        {
            _counterManager.GetRateCounter("RedisCache.StoreToListRate").Increment();

            using (_counterManager.GetLatencyCounter("RedisCache.StoreToListLatency").StartCounter())
            {
                try
                {
                    string val = JsonConvert.SerializeObject(value);
                    await RetryExecutionHelper.ExecuteFuncWithRetryAsync(
                        function: async () => await StoreToListWithExpirationTransactionAsync(key, val, validityPeriod),
                        shouldRetryResult: result => !result,
                        shouldRetryException: exception => true,
                        retryPolicy: _retryPolicy,
                        token: CancellationToken.None);
                }
                catch
                {
                    _counterManager.GetRateCounter("RedisCache.StoreToListErrorRate").Increment();
                    throw;
                }
            }
        }

        public async Task RemoveFromListAsync<T>(string key, T value)
        {
            _counterManager.GetRateCounter("RedisCache.RemoveFromListRate").Increment();

            using (_counterManager.GetLatencyCounter("RedisCache.RemoveFromListLatency").StartCounter())
            {
                try
                {
                    string val = JsonConvert.SerializeObject(value);
                    await RetryExecutionHelper.ExecuteFuncWithRetryForAnyExceptionAsync(
                        function: async () => await _cache.SetRemoveAsync(key, val),
                        retryPolicy: _retryPolicy,
                        token: CancellationToken.None);
                }
                catch
                {
                    _counterManager.GetRateCounter("RedisCache.RemoveFromListErrorRate").Increment();
                    throw;
                }
            }
        }

        public async Task<List<T>> LoadListAsync<T>(string key)
        {
            _counterManager.GetRateCounter("RedisCache.LoadListRate").Increment();

            using (_counterManager.GetLatencyCounter("RedisCache.LoadListLatency").StartCounter())
            {
                try
                {
                    RedisValue[] result = await RetryExecutionHelper.ExecuteFuncWithRetryForAnyExceptionAsync(
                        function: async () => await _cache.SetMembersAsync(key),
                        retryPolicy: _retryPolicy,
                        token: CancellationToken.None);

                    return result.Select(x => JsonConvert.DeserializeObject<T>(x)).ToList();
                }
                catch
                {
                    _counterManager.GetRateCounter("RedisCache.LoadListErrorRate").Increment();
                    throw;
                }
            }
        }

        private async Task<bool> StoreToListWithExpirationTransactionAsync(string key, string value, TimeSpan validityPeriod)
        {
                ITransaction tran = _cache.CreateTransaction();
                // note that these will be executed in the below trans.Execute() call
                // There is no need to await the result
                tran.SetAddAsync(key, value).FireAndForget();
                tran.KeyExpireAsync(key, validityPeriod).FireAndForget();
                return await tran.ExecuteAsync();
        }

        public async Task StoreToHashAsync<T>(string key, string hashKey, T value)
        {
            _counterManager.GetRateCounter("RedisCache.StoreToHashRate").Increment();

            using (_counterManager.GetLatencyCounter("RedisCache.StoreToHashLatency").StartCounter())
            {
                try
                {
                    string val = JsonConvert.SerializeObject(value);
                    await RetryExecutionHelper.ExecuteFuncWithRetryForAnyExceptionAsync(
                        function: async () => await _cache.HashSetAsync(key, hashKey, val),
                        retryPolicy: _retryPolicy,
                        token: CancellationToken.None);
                }
                catch
                {
                    _counterManager.GetRateCounter("RedisCache.StoreToHashErrorRate").Increment();
                    throw;
                }
            }
        }

        public async Task BulkStoreToHashAsync<T>(string key, IEnumerable<KeyValuePair<string, T>> values)
        {
            _counterManager.GetRateCounter("RedisCache.BulkStoreToHashRate").Increment();

            using (_counterManager.GetLatencyCounter("RedisCache.BulkStoreToHashLatency").StartCounter())
            {
                try
                {
                    HashEntry[] hashEntries = values.Select(pair => new HashEntry(pair.Key, JsonConvert.SerializeObject(pair.Value))).ToArray();
                    await RetryExecutionHelper.ExecuteFuncWithRetryAsync(
                        action: async () => await _cache.HashSetAsync(key, hashEntries),
                        shouldRetryException: ex => true,
                        retryPolicy: _retryPolicy,
                        token: CancellationToken.None);
                }
                catch
                {
                    _counterManager.GetRateCounter("RedisCache.BulkStoreToHashErrorRate").Increment();
                    throw;
                }
            }
        }

        public async Task<bool> BulkStoreToHashWithExpirationAsync<T>(string key, IEnumerable<KeyValuePair<string, T>> values, TimeSpan validityPeriod)
        {
            _counterManager.GetRateCounter("RedisCache.BulkStoreToHashRate").Increment();

            using (_counterManager.GetLatencyCounter("RedisCache.BulkStoreToHashLatency").StartCounter())
            {
                try
                {
                    HashEntry[] hashEntries = values.Select(pair => new HashEntry(pair.Key, JsonConvert.SerializeObject(pair.Value))).ToArray();
                    ITransaction tran = _cache.CreateTransaction();
                    // note that these will be executed in the below trans.Execute() call
                    // There is no need to await the result
                    tran.HashSetAsync(key, hashEntries).FireAndForget();
                    tran.KeyExpireAsync(key, validityPeriod).FireAndForget();
                    return await RetryExecutionHelper.ExecuteFuncWithRetryForAnyExceptionAsync(
                        function: async () => { return await tran.ExecuteAsync(); },
                        retryPolicy: _retryPolicy,
                        token: CancellationToken.None);             
                }
                catch
                {
                    _counterManager.GetRateCounter("RedisCache.BulkStoreToHashErrorRate").Increment();
                    throw;
                }
            }
        }

        public async Task<T> LoadFromHashAsync<T>(string key, string hashKey)
        {
            _counterManager.GetRateCounter("RedisCache.LoadFromHashRate").Increment();

            using (_counterManager.GetLatencyCounter("RedisCache.LoadFromHashLatency").StartCounter())
            {
                try
                {
                    RedisValue value = await RetryExecutionHelper.ExecuteFuncWithRetryForAnyExceptionAsync(
                        function: async () => await _cache.HashGetAsync(key, hashKey),
                        retryPolicy: _retryPolicy,
                        token: CancellationToken.None);
                    return value.IsNull ? default(T) : JsonConvert.DeserializeObject<T>(value);
                }
                catch
                {
                    _counterManager.GetRateCounter("RedisCache.LoadFromHashErrorRate").Increment();
                    throw;
                }
            }
        }

        public async Task<IEnumerable<T>> BulkLoadFromHashAsync<T>(string key)
        {
            _counterManager.GetRateCounter("RedisCache.BulkLoadFromHashRate").Increment();

            using (_counterManager.GetLatencyCounter("RedisCache.BulkLoadFromHashLatency").StartCounter())
            {
                try
                {
                    RedisValue[] values = await RetryExecutionHelper.ExecuteFuncWithRetryForAnyExceptionAsync(
                        function: async () => await _cache.HashValuesAsync(key),
                        retryPolicy: _retryPolicy,
                        token: CancellationToken.None);
                    return values.Where(value => value.HasValue).Select(value => JsonConvert.DeserializeObject<T>(value));
                }
                catch
                {
                    _counterManager.GetRateCounter("RedisCache.BulkLoadFromHashErrorRate").Increment();
                    throw;
                }
            }
        }

        public async Task<IEnumerable<T>> BulkLoadFromHashAsync<T>(string key, IEnumerable<string> hashKeys)
        {
            _counterManager.GetRateCounter("RedisCache.BulkLoadFromHashRate").Increment();

            using (_counterManager.GetLatencyCounter("RedisCache.BulkLoadFromHashLatency").StartCounter())
            {
                try
                {
                    RedisValue[] hashKeysValues = hashKeys.Select(k => (RedisValue)k).ToArray();
                    RedisValue[] values = await RetryExecutionHelper.ExecuteFuncWithRetryForAnyExceptionAsync(
                        function: async () => await _cache.HashGetAsync(key, hashKeysValues),
                        retryPolicy: _retryPolicy,
                        token: CancellationToken.None);
                    return values.Where(value => value.HasValue).Select(value => JsonConvert.DeserializeObject<T>(value));
                }
                catch
                {
                    _counterManager.GetRateCounter("RedisCache.BulkLoadFromHashErrorRate").Increment();
                    throw;
                }                
            }
        }
    }
}
