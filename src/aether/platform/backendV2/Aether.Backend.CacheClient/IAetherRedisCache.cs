﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Aether.Backend.CacheClient
{
    public interface IAetherRedisCache
    {
        /// <summary>
        /// Adds or Updates an object in the cache. If the set does not exist, create one.
        /// </summary>
        /// <param name="key">A unique value that is used to store and retrieve the an object from the cache.</param>
        /// <param name="value">The object to put into the cache.</param>
        Task StoreAsync<T>(string key, T value);

        /// <summary>
        /// Adds or Updates an object in the cache. If the set does not exist, create one.
        /// </summary>
        /// <param name="key">A unique value that is used to store and retrieve the an object from the cache.</param>
        /// <param name="value">The object to put into the cache.</param>
        /// <param name="validityPeriod">The validity period of the key</param>
        Task StoreWithExpirationAsync<T>(string key, T value, TimeSpan validityPeriod);

        /// <summary>
        /// Gets an object from the cache using the specified key.
        /// </summary>
        /// <param name="key">A unique value that is used to store and retrieve the object from the cache.</param>
        /// <returns>The object that was cached by using the specified key.</returns>
        Task<T> LoadAsync<T>(string key);

        /// <summary>
        /// Gets array of objects from the cache using specified keys.
        /// </summary>
        /// <param name="keys">Collection of unique values that are used to store and retrieve objects from the cache.</param>
        /// <returns>The array of objects that was cached by using specified keys.</returns>
        Task<T[]> BulkLoadAsync<T>(IEnumerable<string> keys);

        /// <summary>
        /// Adds or Updates a collection of objects in the cache. If the set does not exist, create one.
        /// </summary>
        /// <param name="values">The dictionary of objects to put into the cache.</param>
        Task BulkStoreAsync<T>(IEnumerable<KeyValuePair<string, T>> values);

        /// <summary>
        /// Puts an object into the cache set. If the set does not exist, create one.
        /// </summary>
        /// <param name="key">A unique value that is used to store and retrieve the set of objects from the cache.</param>
        /// <param name="value">The object to put into the cache set.</param>
        Task StoreToListAsync<T>(string key, T value);

        /// <summary>
        /// Removes an object from the cache set. 
        /// </summary>
        /// <param name="key">A unique value that is used to store and retrieve the set of objects from the cache.</param>
        /// <param name="value">The object to remove from the cache set.</param>
        Task RemoveFromListAsync<T>(string key, T value);

        /// <summary>
        /// Removes object or set from the cache.
        /// </summary>
        /// <param name="key">A unique value that is used to store and retrieve the set of objects from the cache.</param>
        Task RemoveAsync(string key);

        /// <summary>
        /// Gets a set from the cache using the specified key.
        /// </summary>
        /// <param name="key">A unique value that is used to store and retrieve the set from the cache.</param>
        /// <returns>A list of the set that was cached by using the specified key.</returns>
        Task<List<T>> LoadListAsync<T>(string key);

        /// <summary>
        /// Puts an object into the cache set. If the set does not exist, create one with an expiration value.
        /// </summary>
        /// <param name="key">A unique value that is used to store and retrieve the set of objects from the cache.</param>
        /// <param name="value">The object to put into the cache set.</param>
        /// <param name="validityPeriod">The validity period of the set key</param>
        Task StoreToListWithExpirationAsync<T>(string key, T value, TimeSpan validityPeriod);

        Task StoreToHashAsync<T>(string key, string hashKey, T value);
        Task BulkStoreToHashAsync<T>(string key, IEnumerable<KeyValuePair<string, T>> values);
        Task<bool> BulkStoreToHashWithExpirationAsync<T>(string key, IEnumerable<KeyValuePair<string, T>> values, TimeSpan validityPeriod);
        Task<T> LoadFromHashAsync<T>(string key, string hashKey);
        Task<IEnumerable<T>> BulkLoadFromHashAsync<T>(string key);
        Task<IEnumerable<T>> BulkLoadFromHashAsync<T>(string key, IEnumerable<string> hashKeys);
    }
}
