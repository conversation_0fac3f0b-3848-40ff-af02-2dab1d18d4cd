<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ResolveAssemblyWarnOrErrorOnTargetArchitectureMismatch>None</ResolveAssemblyWarnOrErrorOnTargetArchitectureMismatch>
    <QInstrumentForCoverage>false</QInstrumentForCoverage>
    <CodeAnalysisRuleSet />
  </PropertyGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\RelInfra\RelInfra.Instrumentation.Library\src\Microsoft.RelInfra.Instrumentation.csproj" />
    <ProjectReference Include="..\..\RelInfra\RelInfra.Common.Library\src\Common\RelInfra.Common.csproj" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Newtonsoft.Json" />
    <PackageReference Include="System.Net.Security" />
  </ItemGroup>
</Project>
