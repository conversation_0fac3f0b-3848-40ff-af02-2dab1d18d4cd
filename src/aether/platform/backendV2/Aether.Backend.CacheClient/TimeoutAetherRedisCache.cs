﻿using Microsoft.RelInfra.Instrumentation;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Aether.Backend.CacheClient
{
    public class TimeoutAetherRedisCache : IAetherRedisCache
    {
        private readonly IAetherRedisCache _cache;
        private readonly TimeSpan _timeout;
        private CounterManager _counterManager;

        public TimeoutAetherRedisCache(IAetherRedisCache aetherRedisCache, TimeSpan timeout, CounterManager counterManager)
        {
            _cache = aetherRedisCache;
            _timeout = timeout;
            _counterManager = counterManager;
        }

        public async Task StoreAsync<T>(string key, T value)
        {
            await ExecuteRedisCacheOperationWithTimeoutAsync(function: async () => 
            {
                await _cache.StoreAsync(key, value); 
                return true; 
            }, "StoreAsync");
        }

        public async Task StoreWithExpirationAsync<T>(string key, T value, TimeSpan validityPeriod)
        {
            await ExecuteRedisCacheOperationWithTimeoutAsync(function: async () => 
            { 
                await _cache.StoreWithExpirationAsync(key, value, validityPeriod); 
                return true; 
            }, "StoreWithExpirationAsync");
        }

        public async Task<T> LoadAsync<T>(string key)
        {
            return await ExecuteRedisCacheOperationWithTimeoutAsync(function: async () => 
            {
                return await _cache.LoadAsync<T>(key);
            }, "LoadAsync");
        }

        public async Task<T[]> BulkLoadAsync<T>(IEnumerable<string> keys)
        {
            return await ExecuteRedisCacheOperationWithTimeoutAsync(function: async () =>
            {
                return await _cache.BulkLoadAsync<T>(keys);
            }, "BulkLoadAsync");
        }

        public async Task BulkStoreAsync<T>(IEnumerable<KeyValuePair<string, T>> values)
        {
            await ExecuteRedisCacheOperationWithTimeoutAsync(function: async () =>
            {
                await _cache.BulkStoreAsync<T>(values);
                return true;
            }, "BulkStoreAsync");
        }

        public async Task StoreToListAsync<T>(string key, T value)
        {
            await ExecuteRedisCacheOperationWithTimeoutAsync(function: async () =>
            {
                await _cache.StoreToListAsync<T>(key, value);
                return true;
            }, "StoreToListAsync");
        }

        public async Task RemoveFromListAsync<T>(string key, T value)
        {
            await ExecuteRedisCacheOperationWithTimeoutAsync(function: async () =>
            {
                await _cache.RemoveFromListAsync<T>(key, value);
                return true;
            }, "RemoveFromListAsync");
        }

        public async Task RemoveAsync(string key)
        {
            await ExecuteRedisCacheOperationWithTimeoutAsync(function: async () =>
            {
                await _cache.RemoveAsync(key);
                return true;
            }, "RemoveAsync");
        }

        public async Task<List<T>> LoadListAsync<T>(string key)
        {
            return await ExecuteRedisCacheOperationWithTimeoutAsync(function: async () =>
            {
                return await _cache.LoadListAsync<T>(key);
            }, "LoadListAsync");
        }

        public async Task StoreToListWithExpirationAsync<T>(string key, T value, TimeSpan validityPeriod)
        {
            await ExecuteRedisCacheOperationWithTimeoutAsync(function: async () =>
            {
                await _cache.StoreToListWithExpirationAsync<T>(key, value, validityPeriod);
                return true;
            }, "StoreToListWithExpirationAsync");
        }

        public async Task StoreToHashAsync<T>(string key, string hashKey, T value)
        {
            await ExecuteRedisCacheOperationWithTimeoutAsync(function: async () =>
            {
                await _cache.StoreToHashAsync<T>(key, hashKey, value);
                return true;
            }, "StoreToHashAsync");
        }

        public async Task BulkStoreToHashAsync<T>(string key, IEnumerable<KeyValuePair<string, T>> values)
        {
            await ExecuteRedisCacheOperationWithTimeoutAsync(function: async () =>
            {
                await _cache.BulkStoreToHashAsync<T>(key, values);
                return true;
            }, "BulkStoreToHashAsync");
        }

        public async Task<bool> BulkStoreToHashWithExpirationAsync<T>(string key, IEnumerable<KeyValuePair<string, T>> values, TimeSpan validityPeriod)
        {
            return await ExecuteRedisCacheOperationWithTimeoutAsync(function: async () =>
            {
                return await _cache.BulkStoreToHashWithExpirationAsync<T>(key, values, validityPeriod);
                
            }, "BulkStoreToHashWithExpirationAsync");
        }

        public async Task<T> LoadFromHashAsync<T>(string key, string hashKey)
        {
            return await ExecuteRedisCacheOperationWithTimeoutAsync(function: async () =>
            {
                return await _cache.LoadFromHashAsync<T>(key, hashKey);
            }, "LoadFromHashAsync");
        }

        public async Task<IEnumerable<T>> BulkLoadFromHashAsync<T>(string key)
        {
            return await ExecuteRedisCacheOperationWithTimeoutAsync(function: async () =>
            {
                return await _cache.BulkLoadFromHashAsync<T>(key);
            }, "BulkLoadFromHashAsync");
        }

        public async Task<IEnumerable<T>> BulkLoadFromHashAsync<T>(string key, IEnumerable<string> hashKeys)
        {
            return await ExecuteRedisCacheOperationWithTimeoutAsync(function: async () =>
            {
                return await _cache.BulkLoadFromHashAsync<T>(key, hashKeys);
            }, "BulkLoadFromHashAsync");
        }

        private async Task<T> ExecuteRedisCacheOperationWithTimeoutAsync<T>(Func<Task<T>> function, string functionName)
        {
           var task = function();
            if (await Task.WhenAny(task, Task.Delay(_timeout)).ConfigureAwait(false) == task)
            {
                // task completed within timeout
                return task.Result;
            }
            else
            {
                _counterManager.GetRateCounter($"RedisCacheTimeout{functionName}").Increment();
                throw new TaskCanceledException($"redis request timed out after {_timeout} in {functionName}");
            }
        }
    }
}
