﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Microsoft.RelInfra.Instrumentation
{
    public class NoOpStatsCounter : ICounter
    {
        public string Name { get; }

        public CounterType CounterType { get; }

        public NoOpStatsCounter(string name, CounterType counterType)
        {
            Name = name;
            CounterType = counterType;
        }

        public bool Decrement(string instanceName = null, IDictionary<string, string> customDimensions = null)
        {
            return true;
        }

        public bool DecrementBy(long value, string instanceName = null, IDictionary<string, string> customDimensions = null)
        {
            return true;
        }

        public void Dispose()
        {
        }

        public long Get(string instanceName = null)
        {
            return 0;
        }

        public bool Increment(string instanceName = null, IDictionary<string, string> customDimensions = null)
        {
            return true;
        }

        public bool IncrementBy(long value, string instanceName = null, IDictionary<string, string> customDimensions = null)
        {
            return true;
        }

        public bool Set(long value, string instanceName = null, IDictionary<string, string> customDimensions = null)
        {
            return true;
        }
    }
}
