﻿using System;
using System.Collections.Generic;
using System.Text;
using Microsoft.RelInfra.Common.Configuration;

namespace Microsoft.RelInfra.Instrumentation
{
    public class StatsdConfiguration : VerifiableConfig
    {
        public StatsdConfiguration(IRelInfraConfiguration config)
            : base(config, verifyConfigOnChanges: true, configName: nameof(StatsdConfiguration))
        {
            IsEnabled = Config?.GetBoolean("SharedStatsdConfig.Enabled") ?? false;
            EnvironmentName = Config?.GetString("SharedStatsdConfig.EnvironmentName");
            MetricNameSpace = Config?.GetString("SharedStatsdConfig.MetricNameSpace");
            StatsdHostOrIp = Config?.GetString("SharedStatsdConfig.StatsdHostOrIp");
            StatsdPort = Config?.GetInteger("SharedStatsdConfig.StatsdPort") ?? 8125;
            Region = Config?.GetString("SharedStatsdConfig.Region");
            Pod = Config?.GetString("SharedStatsdConfig.Pod");
            Cluster = Config?.GetString("SharedStatsdConfig.Cluster");
        }

        public StatsdConfiguration() : this(null) { }

        public bool IsEnabled { get; set; }
        public string EnvironmentName { get; set; }
        public string MetricNameSpace { get; set; }
        public string StatsdHostOrIp { get; set; }
        public int StatsdPort { get; set; }
        public string Region { get; set; }
        public string Pod { get; set; }
        public string Cluster { get; set; }
    }
}