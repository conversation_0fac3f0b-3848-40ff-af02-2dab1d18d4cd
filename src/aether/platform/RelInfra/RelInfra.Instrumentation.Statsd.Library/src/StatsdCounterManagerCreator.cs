﻿using System.Threading.Tasks;

namespace Microsoft.RelInfra.Instrumentation
{
    public class StatsdCounterManagerCreator : ICounterManagerCreator
    {
        private readonly StatsdConfiguration _statsdConfiguration;

        public StatsdCounterManagerCreator(StatsdConfiguration config)
        {
            _statsdConfiguration = config;
        }

        public async Task<CounterManager> CreateAsync(string serviceName)
        {
            var statsdCounterFactory = new StatsdCounterFactory(
                enviroment: _statsdConfiguration.EnvironmentName,
                serviceName: serviceName,
                nameSpace: _statsdConfiguration.MetricNameSpace + "."+ serviceName,
                enabled: _statsdConfiguration.IsEnabled,
                statsdHostOrIp: _statsdConfiguration.StatsdHostOrIp,
                statsdPort: _statsdConfiguration.StatsdPort, 
                region: _statsdConfiguration.Region,
                pod: _statsdConfiguration.Pod,
                cluster: _statsdConfiguration.Cluster);

            return await Task.FromResult(new CounterManager(serviceName, statsdCounterFactory));
        }

        public async Task<CounterManager> CreateCommonCounterAsync(string serviceName)
        {
            var statsdCounterFactory = new StatsdCounterFactory(
                enviroment: _statsdConfiguration.EnvironmentName,
                serviceName: serviceName,
                nameSpace: _statsdConfiguration.MetricNameSpace + ".Common",
                enabled: _statsdConfiguration.IsEnabled,
                statsdHostOrIp: _statsdConfiguration.StatsdHostOrIp,
                statsdPort: _statsdConfiguration.StatsdPort,
                region: _statsdConfiguration.Region,
                pod: _statsdConfiguration.Pod,
                cluster: _statsdConfiguration.Cluster);

            return await Task.FromResult(new CounterManager(serviceName, statsdCounterFactory));
        }
    }
}
