﻿using StatsN;
using System;
using System.Collections.Generic;
using System.Threading;
using Microsoft.RelInfra.Instrumentation.Logging;
using Newtonsoft.Json;

namespace Microsoft.RelInfra.Instrumentation
{
    public class StatsdCounter : ICounter
    {
        private readonly Statsd _statsd;

        private static string EnvironmentDimensionName = "Environment";
        private static string ServiceNameDimensionName = "ServiceName";
        private static string RegionName = "Region";
        private static string InstanceDimensionName = "Instance";
        private static string PodName = "Pod";
        private static string ClusterName = "Cluster";

        public string Name { get; }
        public string NameSpace { get; }
        public string Enviroment { get; }

        public string ServiceName { get; }

        public CounterType CounterType { get; }

        public string Region { get; }

        public string Pod { get; }

        public string Cluster { get; }

        private long _lastValue;

        public StatsdCounter(string name, string nameSpace, string enviroment, string serviceName, Statsd statsd, CounterType counterType, string region, string pod, string cluster)
        {
            Name = name;
            NameSpace = nameSpace;
            Enviroment = enviroment;
            ServiceName = serviceName;
            _statsd = statsd;
            CounterType = counterType;
            _lastValue = 0;
            Region = region;
            Pod = pod;
            Cluster = cluster;
        }

        public bool Decrement(string instanceName = null, IDictionary<string, string> customDimensions = null)
        {
            Interlocked.Decrement(ref _lastValue);
            return UpdateMetric(instanceName, customDimensions);
        }

        public bool DecrementBy(long value, string instanceName = null, IDictionary<string, string> customDimensions = null)
        {
            Interlocked.Add(ref _lastValue, -value);
            return UpdateMetric(instanceName, customDimensions);
        }

        public bool Increment(string instanceName = null, IDictionary<string, string> customDimensions = null)
        {
            Interlocked.Increment(ref _lastValue);
            return UpdateMetric(instanceName, customDimensions);
        }

        public bool IncrementBy(long value, string instanceName = null, IDictionary<string, string> customDimensions = null)
        {
            Interlocked.Add(ref _lastValue, value);
            return UpdateMetric(instanceName, customDimensions);
        }

        public bool Set(long value, string instanceName = null, IDictionary<string, string> customDimensions = null)
        {
            _lastValue = value;
            return UpdateMetric(instanceName, customDimensions);
        }

        public long Get(string instanceName = null)
        {
            return Interlocked.Read(ref _lastValue);
        }
        
        private bool UpdateMetric(string instanceName, IDictionary<string, string> customDimensions)
        {
            try
            {
                _statsd.GaugeAsync(GetFullName(instanceName, customDimensions), Interlocked.Read(ref _lastValue));
            }
            catch(Exception ex)
            {
                CommonLogger.LogEntityError(ServiceName, "Exception happened during the statsd update metric, error: {exception}", ex);
                return false;
            }

            return true;
        }

        private IDictionary<string, string> GetDimensions(string instanceName, IDictionary<string, string> customDimensions)
        {
            var dimensionDict = new Dictionary<string, string>
            {
                { EnvironmentDimensionName, Enviroment},
                { ServiceNameDimensionName, ServiceName},
                { RegionName, Region},
                { PodName, Pod },
                { ClusterName, Cluster},
            };

            if (instanceName != null)
            {
                dimensionDict[InstanceDimensionName] = instanceName;
            }

            var keysSet = new HashSet<string>(dimensionDict.Keys);

            if (customDimensions != null)
            {
                foreach (var kvp in customDimensions)
                {
                    if (keysSet.Contains(kvp.Key))
                    {
                        CommonLogger.LogWarning($"Skip custom dimension {kvp.Key} because it already exists in metrics dimensions.");
                    }
                    else
                    {
                        dimensionDict.Add(kvp.Key, kvp.Value);
                    }
                }
            }

            return dimensionDict;
        }

        private string GetFullName(string instanceName, IDictionary<string, string> customDimensions)
        {
            var dimensionsDict = GetDimensions(instanceName, customDimensions);

            var dimensionStr = JsonConvert.SerializeObject(dimensionsDict);

            return $"{{\"Namespace\":\"{NameSpace}\",\"Metric\":\"{Name}\", \"Dims\": {dimensionStr} }}";
        }
        
        public void Dispose()
        {
        }
    }
}
