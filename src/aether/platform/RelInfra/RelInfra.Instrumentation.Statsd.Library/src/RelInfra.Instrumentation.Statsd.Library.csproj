<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <RootNamespace>Microsoft.RelInfra.Instrumentation</RootNamespace>
    <AssemblyName>Microsoft.RelInfra.Instrumentation.Statsd</AssemblyName>
    <ResolveAssemblyWarnOrErrorOnTargetArchitectureMismatch>None</ResolveAssemblyWarnOrErrorOnTargetArchitectureMismatch>
  </PropertyGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\RelInfra.Instrumentation.Library\src\Microsoft.RelInfra.Instrumentation.csproj" />
    <ProjectReference Include="..\..\RelInfra.Common.Library\src\Common\RelInfra.Common.csproj" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="StatsN" />
  </ItemGroup>
</Project>
