﻿using StatsN;
using System;
using System.Collections.Generic;
using System.Diagnostics;

namespace Microsoft.RelInfra.Instrumentation
{
    public class StatsdCounterFactory : ICounterFactory
    {
        private readonly string _enviroment;
        private readonly string _serviceName;
        private readonly string _nameSpace;
        private readonly bool _disabled;
        private readonly string _statsdHostOrIp;
        private readonly int _statsdPort;
        private Statsd _statsd;
        private readonly string _region;
        private readonly string _pod;
        private readonly string _cluster;

        public StatsdCounterFactory(string enviroment, string serviceName, string nameSpace, bool enabled, string statsdHostOrIp, int statsdPort, string region, string pod, string cluster)
        {
            _enviroment = enviroment;
            _serviceName = serviceName;
            _nameSpace = nameSpace;
            _disabled = !enabled;
            _statsdHostOrIp = statsdHostOrIp;
            _statsdPort = statsdPort;
            _region = region;
            _pod = pod;
            _cluster = cluster;
        }
 

        public ICounter CreateNumberCounter(string sectionName, string counterName)
        {
            if (String.IsNullOrWhiteSpace(counterName))
            {
                Trace.TraceError("Counter name can't be null or empty.");

                throw new ArgumentException("Counter name can't be null or empty.");
            }
            return GetCounter(counterName, CounterType.Number);
        }

        public ICounter CreatePercentilesCounter(string sectionName, string counterName)
        {
            if (String.IsNullOrWhiteSpace(counterName))
            {
                Trace.TraceError("Counter name can't be null or empty.");

                throw new ArgumentException("Counter name can't be null or empty.");
            }

            return GetCounter(counterName, CounterType.Number_Percentiles);
        }

        public ICounter CreateRateCounter(string sectionName, string counterName)
        {
            if (String.IsNullOrWhiteSpace(counterName))
            {
                Trace.TraceError("Counter name can't be null or empty.");

                throw new ArgumentException("Counter name can't be null or empty.");
            }

            return GetCounter(counterName, CounterType.Rate);
        }


        private ICounter GetCounter(string counterName, CounterType counterType)
        {
            if (_disabled)
                return new NoOpStatsCounter(counterName, counterType);

            return new StatsdCounter(name: counterName,
                                     nameSpace: _nameSpace,
                                     enviroment: _enviroment,
                                     serviceName: _serviceName,
                                     statsd: GetStatsd(),
                                     counterType: counterType,
                                     region: _region,
                                     pod: _pod,
                                     cluster: _cluster);
        }

        private Statsd GetStatsd()
        {
            if (_statsd != null)
                return _statsd;

            var opts = new StatsdOptions()
            {
                HostOrIp = _statsdHostOrIp,
                Port = _statsdPort,
                OnExceptionGenerated = (exception) => { Trace.TraceError("StatsD Exception"); Trace.TraceError(exception.Message); },
                OnLogEventGenerated = (log) => { Trace.TraceInformation("StatsD log"); Trace.TraceInformation(log.Message); }
            };

            _statsd = Statsd.New<Udp>(opts);

            return _statsd;
        }
    }
}
