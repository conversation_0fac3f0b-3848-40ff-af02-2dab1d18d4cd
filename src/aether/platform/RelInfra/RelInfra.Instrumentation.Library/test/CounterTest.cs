﻿using System;
using System.Threading;
using Microsoft.RelInfra.Instrumentation.Test.Mocks;
using NUnit.Framework;

namespace Microsoft.RelInfra.Instrumentation.Test
{
    [TestFixture]
    internal class CounterTest
    {
        private const string TestSectionName = "test_counter_section";
        private const string TestCounterName1 = "test_counter_1";
        private const string TestCounterName2 = "test_counter_2";
        private const string TestCounterName3 = "test_counter_3";
        private const string TestCounterName4 = "test_counter_4";
        private const string InstanceName1 = "instance_1";
        private const string InstanceName2 = "instance_2";
        private const string InstanceName3 = "instance_3";

        private enum Dim1
        {
            Test1,
            Test2,
            Test3
        }

        private enum Dim2
        {
            Test1,
            Test2,
            Test3
        }

        private enum Instance
        {
            Inst1,
            Inst2,
            Inst3
        }

        [Test]
        public void TestCounters()
        {
            Counters.AddCounter(new MockCounter(TestSectionName, TestCounterName1, CounterType.None));
            Counters.Set(TestCounterName1, 10);
        }

        [Test]
        public void TestCounterManager()
        {
            var mockFactory = new MockCounterFactory();
            var manager = new CounterManager(TestSectionName, mockFactory);

            LatencyCounter counter1 = manager.GetLatencyCounter(TestCounterName1, InstanceName1);

            // Check that manager used the right values
            MockCounter firstCounter = mockFactory.LastCreatedCounter;
            Assert.AreEqual(TestSectionName, firstCounter.Section);
            Assert.AreEqual(TestCounterName1, firstCounter.Name);

            NumberCounter counter2 = manager.GetNumberCounter(TestCounterName2, InstanceName1);
            Assert.AreNotSame(firstCounter, mockFactory.LastCreatedCounter); // makes sure that the factory was used to create a new counter
            RateCounter counter3 = manager.GetRateCounter(TestCounterName3, InstanceName1);
            Assert.AreNotSame(firstCounter, mockFactory.LastCreatedCounter); // makes sure that the factory was used to create a new counter

            // Test that the same counter object is returned for the same name-instance, and different when instance is different
            Assert.AreSame(counter1, manager.GetLatencyCounter(TestCounterName1, InstanceName1));
            Assert.AreNotSame(counter1, manager.GetLatencyCounter(TestCounterName4, InstanceName1));
            Assert.AreNotSame(firstCounter, mockFactory.LastCreatedCounter); // makes sure that the factory was used to create a new counter
            MockCounter previousCounter = mockFactory.LastCreatedCounter;
            Assert.AreNotSame(counter1, manager.GetLatencyCounter(TestCounterName1, InstanceName2));
            Assert.AreSame(previousCounter, mockFactory.LastCreatedCounter); // makes sure that factory was NOT triggered for same name but different instance 

            // Test that the right exceptions are thrown when type mismatches occur
            Assert.Throws<System.InvalidCastException>(() => manager.GetNumberCounter(TestCounterName1, InstanceName1));
            Assert.Throws<CounterException>(() => manager.GetNumberCounter(TestCounterName1, InstanceName3));
        }

        [Test]
        public void TestBasicCounters()
        {
            var mockFactory = new MockCounterFactory();
            var manager = new CounterManager(TestSectionName, mockFactory);

            // test the latency counter
            LatencyCounter latencyCounter = manager.GetLatencyCounter(TestCounterName1, InstanceName1);
            MockCounter counter = mockFactory.LastCreatedCounter;
            var timespan = new TimeSpan(0, 0, 0, 2, 3);
            Assert.IsTrue(latencyCounter.Set((long)timespan.TotalMilliseconds));
            Assert.AreEqual(counter.CurrentValue, timespan.TotalMilliseconds);

            // Test the disposable timer interface
            using (var t1 = latencyCounter.StartCounter())
            {
                using (var t2 = latencyCounter.StartCounter())
                {
                    Thread.Sleep(10);
                }
            }

            Assert.GreaterOrEqual(counter.CurrentValue, 0);

            // Test rate counter
            RateCounter rateCounter = manager.GetRateCounter(TestCounterName2, InstanceName1);
            counter = mockFactory.LastCreatedCounter;
            Assert.AreEqual(0, counter.CurrentValue);

            Assert.IsTrue(rateCounter.Increment());
            Assert.AreEqual(1, counter.CurrentValue);

            Assert.IsTrue(rateCounter.Increment(5));
            Assert.AreEqual(6, counter.CurrentValue);

            // Test number counter
            NumberCounter numberCounter = manager.GetNumberCounter(TestCounterName3, InstanceName1);
            counter = mockFactory.LastCreatedCounter;
            Assert.AreEqual(0, counter.CurrentValue);

            Assert.IsTrue(numberCounter.Set(321));
            Assert.AreEqual(321, counter.CurrentValue);

            Assert.IsTrue(numberCounter.Set(4));
            Assert.AreEqual(4, counter.CurrentValue);
        }

        [Test]
        public void TestEnumCounterManager()
        {
            var mockFactory = new MockCounterFactory();
            var manager = new EnumCounterManager<Dim1, Dim2, Instance>(TestSectionName, mockFactory);

            LatencyCounter counter = manager.GetLatencyCounter(Dim1.Test3, Dim2.Test1, Instance.Inst2);
            Assert.AreEqual(TestSectionName, mockFactory.LastCreatedCounter.Section);
            Assert.AreEqual("Test3Test1", mockFactory.LastCreatedCounter.Name);
        }
    }
}