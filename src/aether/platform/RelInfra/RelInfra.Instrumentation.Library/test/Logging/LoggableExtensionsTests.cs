﻿using Microsoft.RelInfra.Instrumentation.Exceptions;
using Microsoft.WindowsAzure.Storage;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using static Microsoft.RelInfra.Instrumentation.Logging.LoggableTestUtils;

namespace Microsoft.RelInfra.Instrumentation.Logging
{
    public class LoggableExtensionsTests
    {

        [Test]
        public void Test_LoggableExtensions_LoggableProperties()
        {
            PrivacyAwareILoggableWorkspace ws = new PrivacyAwareILoggableWorkspace("workspaceId", null);
            var expected = new Dictionary<string, string>() { { "WorkspaceId", "workspaceId" }, { "UserName", "bob" } };
            var actual = ws.LoggableProperties();
            Assert.AreEqual(expected, actual, string.Join("\n", actual.Select(kvp => $"{kvp.Key}: {kvp.Value}")));
        }

        [Test]
        public void Test_LoggableExtensions_LogSafeExceptionProperties()
        {
            foreach (bool loggablePropsContainMessage in new bool[] { true, false })
            {
                foreach (bool format in new bool[] { true, false })
                {
                    foreach (bool isMessageSafe in new bool[] { true, false })
                    {

                        PrivacyAwareILoggableException ex = InnerExceptions(0, (depth, inner) =>
                        {
                            PrivacyAwareILoggableException ex = new PrivacyAwareILoggableException($"My depth is {depth}", includeMessageInLogSafe: loggablePropsContainMessage, inner: inner);
                            if (format) ex.Format();
                            return ex;
                        });
                        var vars = $"loggablePropsContainMessage: {loggablePropsContainMessage}, format: {format}, isMessageSafe: {isMessageSafe}";
                        var props = ex.LogSafeExceptionProperties(isMessageSafe: isMessageSafe);

                        if (isMessageSafe || loggablePropsContainMessage)
                        {
                            Assert.AreEqual(ex.Message, props["Message"], vars);
                            Assert.IsFalse(props.ContainsKey("MessageFormat"), vars);
                        }
                        else if (format)
                        {
                            Assert.AreEqual(ex.GetMessageFormat(), props["MessageFormat"], vars);
                        }
                        else
                        {
                            Assert.AreEqual($"[REDACT: {ex.GetType()}]", props["Message"], vars);
                        }
                        Assert.AreEqual("bob", props["other_field"], vars);
                    }
                }
            }
        }

        [Test]
        public void Test_LoggableExtensions_ToLogSafeExceptionString()
        {
            string httpStatusMessage = "Authentication Error";
            foreach (int exceptionDepth in Enumerable.Range(1, 10))
            {
                foreach (bool format in new bool[] { true, false })
                {
                    StorageException ex = InnerExceptions(exceptionDepth - 1, (depth, inner) =>
                    {
                        StorageException ex = StorageException($"My depth is {depth}", httpStatusMessage, inner);
                        if (format) ex.Format();
                        return ex;
                    });
                    foreach (int printDepth in Enumerable.Range(0, exceptionDepth + 1))
                    {
                        foreach (bool isMessageSafe in new bool[] { true, false })
                        {
                            foreach (bool includeBase in new bool[] { true, false })
                            {
                                var vars = $"exceptionDepth: {exceptionDepth}, format: {format}, printDepth: {printDepth}, " +
                                    $"isMessageSafe: {isMessageSafe}, includeBase: {includeBase}";
                                System.Diagnostics.Debug.WriteLine(vars);
                                var str = ex.ToLogSafeExceptionString(depth: printDepth, isMessageSafe: isMessageSafe, includeBase: includeBase);

                                int depth = GetDepth(str);
                                if (printDepth + 1 < exceptionDepth)
                                {
                                    Assert.AreEqual(printDepth + 1 + (includeBase ? 1 : 0), depth, vars);
                                }
                                else
                                {
                                    Assert.AreEqual(exceptionDepth, depth, vars);
                                }
                                if (isMessageSafe)
                                {
                                    Assert.That(str.Contains(ex.Message), vars);
                                }
                                Exception baseEx = ex.GetBaseException();
                                if (format)
                                {
                                    Assert.That(str.Contains(ex.GetMessageFormat()), vars);
                                    if (includeBase)
                                    {
                                        Assert.That(str.Contains(baseEx.GetMessageFormat()), vars);
                                    }
                                }
                                else if (baseEx != ex)
                                {
                                    Assert.That(!str.Contains(baseEx.Message), vars);
                                }
                                Assert.That(str.Contains(httpStatusMessage), vars);
                            }
                        }
                    }
                }
            }
        }

        public static int GetDepth(string logSafeExceptionString)
        {
            Regex rx = new Regex(@"\s*at .* in .*", RegexOptions.Compiled | RegexOptions.IgnoreCase);
            MatchCollection match = rx.Matches(logSafeExceptionString);
            return match.Count;
        }

    }
}
