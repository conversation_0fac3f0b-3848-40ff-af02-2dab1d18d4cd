﻿using System.Linq;
using Microsoft.RelInfra.Instrumentation.Logging.Test.Mocks;
using NUnit.Framework;

namespace Microsoft.RelInfra.Instrumentation.Logging.Test.Logging
{
    [TestFixture]
    internal class CommonLoggerTests
    {

        [Test]
        public void BasicTest()
        {
            const string errorTest = "test1error";
            const string warningTest = "test {0}-{1}";
            object[] warningParams = {"1", "b"};
            const string warningResult = "test 1-b";
            const string infoTest = "1234";
            const string debugTest = "debug";
            const string debugTest2 = "debug2";

            var logger = new MockCommonLogger();
            logger.LogError(errorTest);
            logger.LogWarning(warningTest, warningParams);
            logger.LogInfo(infoTest);
            logger.LogDebug(debugTest);
            logger.LogDebug(debugTest2);

            Assert.AreEqual(1, logger.ErrorList.Count);
            Assert.AreEqual(1, logger.WarningList.Count);
            Assert.AreEqual(1, logger.InfoList.Count);
            Assert.AreEqual(2, logger.DebugList.Count);

            Assert.AreEqual(errorTest, logger.ErrorList[0]);
            Assert.AreEqual(warningResult, logger.WarningList[0]);
            Assert.AreEqual(infoTest, logger.InfoList[0]);
            Assert.AreEqual(debugTest, logger.DebugList[0]);
            Assert.AreEqual(debugTest2, logger.DebugList[1]);
        }

        [Test]
        public void MultiLoggerTest()
        {
            const string testBoth = "TestBoth";
            const string test1 = "Test1";
            const string test2 = "Test2";

            var logger1 = new MockCommonLogger();
            var logger2 = new MockCommonLogger();

            var multiLogger = new MultiLogger(new ICommonLogger[] {logger1, logger2});
            var multiLogger1 = new MultiLogger(new ICommonLogger[] {logger1});
            var multiLogger2 = new MultiLogger(new ICommonLogger[] {logger2});

            multiLogger.LogDebug(testBoth);
            multiLogger1.LogDebug(test1);
            multiLogger2.LogDebug(test2);

            Assert.AreEqual(2, logger1.DebugList.Count());
            Assert.AreEqual(2, logger2.DebugList.Count());
            Assert.AreEqual(0, logger1.ErrorList.Count());
            Assert.AreEqual(0, logger2.ErrorList.Count());
            Assert.AreEqual(0, logger1.WarningList.Count());
            Assert.AreEqual(0, logger2.WarningList.Count());
            Assert.AreEqual(0, logger1.InfoList.Count());
            Assert.AreEqual(0, logger2.InfoList.Count());

            Assert.AreEqual(testBoth, logger1.DebugList[0]);
            Assert.AreEqual(test1, logger1.DebugList[1]);
            Assert.AreEqual(testBoth, logger2.DebugList[0]);
            Assert.AreEqual(test2, logger2.DebugList[1]);
        }
    }
}
