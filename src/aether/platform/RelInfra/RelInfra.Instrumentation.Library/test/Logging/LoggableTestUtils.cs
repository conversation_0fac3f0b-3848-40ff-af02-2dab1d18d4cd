﻿using Microsoft.AIPlatform.Telemetry.Contracts.Logging.PrivacyAware;
using Microsoft.WindowsAzure.Storage;
using System;
using System.Collections.Generic;
using System.Net;
using System.Reflection;

namespace Microsoft.RelInfra.Instrumentation.Logging
{
    public static class LoggableTestUtils
    {
        public static StorageException StorageException(string message, string httpStatusMessage, Exception inner = null)
        {
            RequestResult requestResult = new RequestResult();
            typeof(RequestResult)
                .GetProperty("HttpStatusMessage")
                .SetValue(requestResult, httpStatusMessage, BindingFlags.NonPublic | BindingFlags.Instance, null, null, null);

            return new StorageException(requestResult, message, inner);
        }

        public static T InnerExceptions<T>(int numInners, Func<int, Exception, T> exceptionGenerator) where T : Exception
        {
            T inner = default;
            numInners += 1; // for the top-level exception
            for (int i = 0; i < numInners; i++)
            {
                try
                {
                    throw exceptionGenerator(i, inner);
                }
                catch (T e)
                {
                    inner = e;
                }
            }
            return inner;
        }

        public class PrivacyAwareException : Exception
        {
            private bool _throwing;
            private HttpStatusCode? _httpStatusCode;

            [PrivacyClassification(PrivacyType.SystemMetadata)]
            public HttpStatusCode? HttpStatusCode
            {
                get
                {
                    if (_throwing)
                    {
                        throw new ArgumentException();
                    }
                    return _httpStatusCode;
                }
            }

            [PrivacyClassification(PrivacyType.CustomerContent)]
            public string SuperSecret { get; private set; } = "Super secret thing you shouldn't see";

            public PrivacyAwareException(string message, HttpStatusCode? httpStatusCode = null, Exception inner = null, bool throwing = false) : base(message, inner)
            {
                _throwing = throwing;
                _httpStatusCode = httpStatusCode;
            }
        }

        public class ILoggableException : Exception, ILoggable
        {
            private bool _throwing;

            public ILoggableException(string message, Exception inner = null, bool throwing = false) : base(message, inner)
            {
                _throwing = throwing;
            }

            public Dictionary<string, string> LogSafeValues()
            {
                if (_throwing)
                {
                    throw new ArgumentException();
                }
                return new Dictionary<string, string>() { { nameof(Message), Message }, { "null_guy", null } };
            }
        }

        public class PrivacyAwareILoggableException : Exception, ILoggable
        {
            private bool _includeMessageInLogSafe;

            [PrivacyClassification(PrivacyType.SystemMetadata)]
            public HttpStatusCode? HttpStatusCode { get; private set; }

            [PrivacyClassification(PrivacyType.CustomerContent)]
            public string SuperSecret { get; private set; } = "Super secret thing you shouldn't see";

            public PrivacyAwareILoggableException(string message, HttpStatusCode? httpStatusCode = null, bool includeMessageInLogSafe = true, Exception inner = null) : base(message, inner)
            {
                HttpStatusCode = httpStatusCode;
                _includeMessageInLogSafe = includeMessageInLogSafe;
            }

            public Dictionary<string, string> LogSafeValues()
            {
                var dict = _includeMessageInLogSafe ? new Dictionary<string, string>() { { nameof(Message), Message } } : new Dictionary<string, string>();
                dict.Add(nameof(HttpStatusCode), HttpStatusCode.ToString());
                dict.Add("other_field", "bob");
                return dict;
            }
        }

        public class ILoggableWorkspace : ILoggable
        {
            private bool _throwing;

            public string WorkspaceId { get; private set; }
            public string WorkspaceName { get; private set; }


            public ILoggableWorkspace(string workspaceId, string workspaceName, bool throwing = false)
            {
                _throwing = throwing;
                WorkspaceId = workspaceId;
                WorkspaceName = workspaceName;
            }

            public Dictionary<string, string> LogSafeValues()
            {
                if (_throwing)
                {
                    throw new ArgumentException();
                }
                return new Dictionary<string, string>() { { nameof(WorkspaceId), WorkspaceId }, { nameof(WorkspaceName), WorkspaceName }, { "null_guy", null } };
            }
        }

        public class PrivacyAwareWorkspace
        {
            private bool _throwing;
            private string _workspaceId;
            private string _workspaceName;

            [PrivacyClassification(PrivacyType.SystemMetadata)]
            public string WorkspaceId
            {
                get
                {
                    if (_throwing)
                    {
                        throw new ArgumentException();
                    }
                    return _workspaceId;
                }
            }
            [PrivacyClassification(PrivacyType.SystemMetadata)]
            public string WorkspaceName
            {
                get
                {
                    if (_throwing)
                    {
                        throw new ArgumentException();
                    }
                    return _workspaceName;
                }
            }
            [PrivacyClassification(PrivacyType.CustomerContent)]
            public string SuperSecret { get; private set; } = "Super secret thing you shouldn't see";


            public PrivacyAwareWorkspace(string workspaceId, string workspaceName, bool throwing = false)
            {
                _throwing = throwing;
                _workspaceId = workspaceId;
                _workspaceName = workspaceName;
            }
        }

        public class PrivacyAwareILoggableWorkspace : ILoggable
        {
            private bool _throwing;
            private string _workspaceId;
            private string _workspaceName;

            [PrivacyClassification(PrivacyType.SystemMetadata)]
            public string WorkspaceId
            {
                get
                {
                    if (_throwing)
                    {
                        throw new ArgumentException();
                    }
                    return _workspaceId;
                }
            }
            [PrivacyClassification(PrivacyType.SystemMetadata)]
            public string WorkspaceName
            {
                get
                {
                    if (_throwing)
                    {
                        throw new ArgumentException();
                    }
                    return _workspaceName;
                }
            }
            [PrivacyClassification(PrivacyType.CustomerContent)]
            public string SuperSecret { get; private set; } = "Super secret thing you shouldn't see";


            public PrivacyAwareILoggableWorkspace(string workspaceId, string workspaceName, bool throwing = false)
            {
                _throwing = throwing;
                _workspaceId = workspaceId;
                _workspaceName = workspaceName;
            }

            public Dictionary<string, string> LogSafeValues()
            {
                if (_throwing)
                {
                    throw new ArgumentException();
                }
                return new Dictionary<string, string>() { { "UserName", "bob" }, { "null_field", null } };
            }
        }
    }
}
