﻿using Microsoft.AIPlatform.Telemetry.Scrubbing;
using Microsoft.AIPlatform.Telemetry.Startup.Contracts;
using Microsoft.RelInfra.Instrumentation.Exceptions;
using Microsoft.RelInfra.Instrumentation.Logging;
using Microsoft.WindowsAzure.Storage;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using static Microsoft.RelInfra.Instrumentation.Logging.LoggableTestUtils;

namespace Microsoft.RelInfra.Instrumentation.Test.Logging
{
    public class CommonLoggerMakeLoggableTests
    {

        [SetUp]
        public void Init()
        {
            CommonLogger._scrubbingConfig = new AmlTelemetryConfiguration().Scrubbing;
            
            foreach (var item in new HashSet<string>(){ "exception", "workspace" })
            {
                CommonLogger._scrubbingConfig.CustomDimensionScrubbers[item] = PropertyBehavior.Block;
            }
        }

        [Test]
        public void Test_CommonLogger_MakeLoggable_Simple()
        {
            var (msg, args) = CommonLogger.MakeLoggable("called {function}", new object[] { "MyFunction" });
            Assert.AreEqual("called {function}", msg);
            Assert.AreEqual(new object[] { "MyFunction" }, args);

            (msg, args) = CommonLogger.MakeLoggable("called {function} with param {param}", new object[] { "MyFunction", 1 });
            Assert.AreEqual("called {function} with param {param}", msg);
            Assert.AreEqual(new object[] { "MyFunction", 1 }, args);
        }

        [Test]
        public void Test_CommonLogger_MakeLoggable_SimpleException()
        {
            var ex = new ArgumentException("hey!", "badParam");
            var (msg, args) = CommonLogger.MakeLoggable("found an exception: {safe_exception}", new object[] { ex });
            Dictionary<string, string> expected = new Dictionary<string, string>() {
                { "type", ex.GetType().ToString() },
                { "ParamName", ex.ParamName },
                { "Message", ex.Message },
            };
            Assert.AreEqual($"found an exception: {MakeFormat(expected.Keys)}", msg);
            Assert.AreEqual(expected.Values.ToArray(), args);

            (msg, args) = CommonLogger.MakeLoggable("found an exception: {exception}", new object[] { ex });
            expected["Message"] = "hey! (Parameter 'badParam')";
            Assert.AreEqual($"found an exception: {MakeFormat(expected.Keys)}", msg);
            Assert.AreEqual(expected.Values.ToArray(), args);

            ex.Format();
            (msg, args) = CommonLogger.MakeLoggable("found an exception: {exception}", new object[] { ex });
            expected = new Dictionary<string, string>() {
                { "type", ex.GetType().ToString() },
                { "ParamName", ex.ParamName },
                { "Message", ex.Message },
            };
            Assert.AreEqual($"found an exception: {MakeFormat(expected.Keys)}", msg);
            Assert.AreEqual(expected.Values.ToArray(), args);
        }

        [Test]
        public void Test_CommonLogger_MakeLoggable_StackTrace()
        {

            StorageException ex = InnerExceptions(0, (depth, inner) => StorageException(depth.ToString(), "Authentication Error", inner));
            var (msg, args) = CommonLogger.MakeLoggable("found an exception: {safe_exception}", new object[] { ex });
            Dictionary<string, string> expected = new Dictionary<string, string>() {
                { "type", ex.GetType().ToString() },
                { "HttpStatusMessage", ex.RequestInformation.HttpStatusMessage },
                { "Message", ex.Message },
                { "stack_trace", ex.StackTrace },
            };
            Assert.AreEqual($"found an exception: {MakeFormat(expected.Keys)}", msg);
            Assert.AreEqual(expected.Values.ToArray(), args);

            (msg, args) = CommonLogger.MakeLoggable("found an exception: {exception}", new object[] { ex });
            expected["Message"] = $"[REDACT: {ex.GetType()}]";
            Assert.AreEqual($"found an exception: {MakeFormat(expected.Keys)}", msg);
            Assert.AreEqual(expected.Values.ToArray(), args);

            ex.Format();
            (msg, args) = CommonLogger.MakeLoggable("found an exception: {exception}", new object[] { ex });
            expected = new Dictionary<string, string>() {
                { "type", ex.GetType().ToString() },
                { "HttpStatusMessage", ex.RequestInformation.HttpStatusMessage },
                { "MessageFormat", ex.GetMessageFormat() },
                { "stack_trace", ex.StackTrace },
            };
            Assert.AreEqual($"found an exception: {MakeFormat(expected.Keys)}", msg);
            Assert.AreEqual(expected.Values.ToArray(), args);
        }

        [Test]
        public void Test_CommonLogger_MakeLoggable_PrivacyAwareException()
        {
            PrivacyAwareException ex = InnerExceptions(0, (depth, inner) => new PrivacyAwareException(depth.ToString(), HttpStatusCode.BadRequest, inner));
            var (msg, args) = CommonLogger.MakeLoggable("found an exception: {safe_exception}", new object[] { ex });
            Dictionary<string, string> expected = new Dictionary<string, string>() {
                { "type", ex.GetType().ToString() },
                { "HttpStatusCode", ex.HttpStatusCode.ToString() },
                { "Message", ex.Message },
                { "stack_trace", ex.StackTrace },
            };
            Assert.AreEqual($"found an exception: {MakeFormat(expected.Keys)}", msg);
            Assert.AreEqual(expected.Values.ToArray(), args);

            (msg, args) = CommonLogger.MakeLoggable("found an exception: {exception}", new object[] { ex });
            expected["Message"] = $"[REDACT: {ex.GetType()}]";
            Assert.AreEqual($"found an exception: {MakeFormat(expected.Keys)}", msg);
            Assert.AreEqual(expected.Values.ToArray(), args);

            ex.Format();
            (msg, args) = CommonLogger.MakeLoggable("found an exception: {exception}", new object[] { ex });
            expected = new Dictionary<string, string>() {
                { "type", ex.GetType().ToString() },
                { "HttpStatusCode", ex.HttpStatusCode.ToString() },
                { "MessageFormat", ex.GetMessageFormat() },
                { "stack_trace", ex.StackTrace },
            };
            Assert.AreEqual($"found an exception: {MakeFormat(expected.Keys)}", msg);
            Assert.AreEqual(expected.Values.ToArray(), args);

            // make sure we ignore PrivacyAware fields that are null
            ex = InnerExceptions(0, (depth, inner) => new PrivacyAwareException(depth.ToString(), null, inner));
            (msg, args) = CommonLogger.MakeLoggable("found an exception: {safe_exception}", new object[] { ex });
            expected = new Dictionary<string, string>() {
                { "type", ex.GetType().ToString() },
                { "Message", ex.Message },
                { "stack_trace", ex.StackTrace },
            };
            Assert.AreEqual($"found an exception: {MakeFormat(expected.Keys)}", msg);
            Assert.AreEqual(expected.Values.ToArray(), args);

            (msg, args) = CommonLogger.MakeLoggable("found an exception: {exception}", new object[] { ex });
            expected["Message"] = $"[REDACT: {ex.GetType()}]";
            Assert.AreEqual($"found an exception: {MakeFormat(expected.Keys)}", msg);
            Assert.AreEqual(expected.Values.ToArray(), args);
        }

        [Test]
        public void Test_CommonLogger_MakeLoggable_ILoggableException()
        {
            ILoggableException ex = InnerExceptions(0, (depth, inner) => new ILoggableException(depth.ToString(), inner));
            var (msg, args) = CommonLogger.MakeLoggable("found an exception: {safe_exception}", new object[] { ex });
            Dictionary<string, string> expected = new Dictionary<string, string>() {
                { "type", ex.GetType().ToString() },
                { "Message", ex.Message },
                { "stack_trace", ex.StackTrace },
            };
            Assert.AreEqual($"found an exception: {MakeFormat(expected.Keys)}", msg);
            Assert.AreEqual(expected.Values.ToArray(), args);

            (msg, args) = CommonLogger.MakeLoggable("found an exception: {exception}", new object[] { ex });
            expected = new Dictionary<string, string>() {
                { "type", ex.GetType().ToString() },
                { "Message", ex.Message },
                { "stack_trace", ex.StackTrace },
            };
            Assert.AreEqual($"found an exception: {MakeFormat(expected.Keys)}", msg);
            Assert.AreEqual(expected.Values.ToArray(), args);

            ex.Format();
            (msg, args) = CommonLogger.MakeLoggable("found an exception: {exception}", new object[] { ex });
            expected = new Dictionary<string, string>() {
                { "type", ex.GetType().ToString() },
                { "Message", ex.Message },
                { "stack_trace", ex.StackTrace },
            };
            Assert.AreEqual($"found an exception: {MakeFormat(expected.Keys)}", msg);
            Assert.AreEqual(expected.Values.ToArray(), args);

            // ILoggableException.LogSafeValues() contains a null value so we automatically test for that
        }

        [Test]
        public void Test_CommonLogger_MakeLoggable_PrivacyAwareILoggable()
        {
            PrivacyAwareILoggableException ex = InnerExceptions(0, (depth, inner) => new PrivacyAwareILoggableException(depth.ToString(), HttpStatusCode.BadRequest, inner: inner));
            var (msg, args) = CommonLogger.MakeLoggable("found an exception: {safe_exception}", new object[] { ex });
            Dictionary<string, string> expected = new Dictionary<string, string>() {
                { "type", ex.GetType().ToString() },
                { "HttpStatusCode", ex.HttpStatusCode.ToString() },
                { "Message", ex.Message },
                { "other_field", "bob" },
                { "stack_trace", ex.StackTrace },
            };
            Assert.AreEqual($"found an exception: {MakeFormat(expected.Keys)}", msg);
            Assert.AreEqual(expected.Values.ToArray(), args);

            (msg, args) = CommonLogger.MakeLoggable("found an exception: {exception}", new object[] { ex });
            expected = new Dictionary<string, string>() {
                { "type", ex.GetType().ToString() },
                { "HttpStatusCode", ex.HttpStatusCode.ToString() },
                { "Message", ex.Message },
                { "other_field", "bob" },
                { "stack_trace", ex.StackTrace },
            };
            Assert.AreEqual($"found an exception: {MakeFormat(expected.Keys)}", msg);
            Assert.AreEqual(expected.Values.ToArray(), args);

            ex.Format();
            (msg, args) = CommonLogger.MakeLoggable("found an exception: {exception}", new object[] { ex });
            expected = new Dictionary<string, string>() {
                { "type", ex.GetType().ToString() },
                { "HttpStatusCode", ex.HttpStatusCode.ToString() },
                { "Message", ex.Message },
                { "other_field", "bob" },
                { "stack_trace", ex.StackTrace },
            };
            Assert.AreEqual($"found an exception: {MakeFormat(expected.Keys)}", msg);
            Assert.AreEqual(expected.Values.ToArray(), args);
        }

        [Test]
        public void Test_CommonLogger_MakeLoggable_PrivacyAwareLogging_Throwing()
        {

            PrivacyAwareException ex = InnerExceptions(0, (depth, inner) => new PrivacyAwareException(depth.ToString(), HttpStatusCode.BadRequest, inner, throwing: true));
            var (msg, args) = CommonLogger.MakeLoggable("found an exception: {safe_exception}", new object[] { ex });
            Dictionary<string, string> expected = new Dictionary<string, string>() {
                { "type", ex.GetType().ToString() },
                { "Message", ex.Message },
                { "stack_trace", ex.StackTrace },
            };
            Assert.AreEqual($"found an exception: {MakeFormat(expected.Keys)}", msg);
            Assert.AreEqual(expected.Values.ToArray(), args);

            (msg, args) = CommonLogger.MakeLoggable("found an exception: {exception}", new object[] { ex });
            expected["Message"] = $"[REDACT: {ex.GetType()}]";
            Assert.AreEqual($"found an exception: {MakeFormat(expected.Keys)}", msg);
            Assert.AreEqual(expected.Values.ToArray(), args);

            ex.Format();
            (msg, args) = CommonLogger.MakeLoggable("found an exception: {exception}", new object[] { ex });
            expected = new Dictionary<string, string>() {
                { "type", ex.GetType().ToString() },
                { "MessageFormat", ex.GetMessageFormat() },
                { "stack_trace", ex.StackTrace },
            };
            Assert.AreEqual($"found an exception: {MakeFormat(expected.Keys)}", msg);
            Assert.AreEqual(expected.Values.ToArray(), args);
        }

        [Test]
        public void Test_CommonLogger_MakeLoggable_ILoggableException_Throwing()
        {
            ILoggableException ex = InnerExceptions(0, (depth, inner) => new ILoggableException(depth.ToString(), inner, throwing: true));
            var (msg, args) = CommonLogger.MakeLoggable("found an exception: {safe_exception}", new object[] { ex });
            Dictionary<string, string> expected = new Dictionary<string, string>() {
                { "type", ex.GetType().ToString() },
                { "Message", ex.Message },
                { "stack_trace", ex.StackTrace },
            };
            Assert.AreEqual($"found an exception: {MakeFormat(expected.Keys)}", msg);
            Assert.AreEqual(expected.Values.ToArray(), args);

            (msg, args) = CommonLogger.MakeLoggable("found an exception: {exception}", new object[] { ex });
            expected["Message"] = $"[REDACT: {ex.GetType()}]";
            Assert.AreEqual($"found an exception: {MakeFormat(expected.Keys)}", msg);
            Assert.AreEqual(expected.Values.ToArray(), args);

            ex.Format();
            (msg, args) = CommonLogger.MakeLoggable("found an exception: {exception}", new object[] { ex });
            expected = new Dictionary<string, string>() {
                { "type", ex.GetType().ToString() },
                { "MessageFormat", ex.GetMessageFormat() },
                { "stack_trace", ex.StackTrace },
            };
            Assert.AreEqual($"found an exception: {MakeFormat(expected.Keys)}", msg);
            Assert.AreEqual(expected.Values.ToArray(), args);
        }

        [Test]
        public void Test_CommonLogger_MakeLoggable_ILoggable()
        {
            ILoggableWorkspace ws = new ILoggableWorkspace("workspaceId", "workspaceName");
            var (msg, args) = CommonLogger.MakeLoggable("connection to workspace: {safe_workspace}", new object[] { ws });
            Dictionary<string, string> expected = new Dictionary<string, string>() {
                { "type", ws.GetType().ToString() },
                { "WorkspaceId", ws.WorkspaceId },
                { "WorkspaceName", ws.WorkspaceName },
                { "safe_workspace", ws.ToString() },
            };
            Assert.AreEqual($"connection to workspace: {MakeFormat(expected.Keys)}", msg);
            Assert.AreEqual(expected.Values.ToArray(), args);

            (msg, args) = CommonLogger.MakeLoggable("connection to workspace: {workspace}", new object[] { ws });
            expected = new Dictionary<string, string>() {
                { "type", ws.GetType().ToString() },
                { "WorkspaceId", ws.WorkspaceId },
                { "WorkspaceName", ws.WorkspaceName },
            };
            Assert.AreEqual($"connection to workspace: {MakeFormat(expected.Keys)}", msg);
            Assert.AreEqual(expected.Values.ToArray(), args);
        }

        [Test]
        public void Test_CommonLogger_MakeLoggable_ILoggable_Throwing()
        {
            ILoggableWorkspace ws = new ILoggableWorkspace("workspaceId", "workspaceName", throwing: true);
            var (msg, args) = CommonLogger.MakeLoggable("connection to workspace: {safe_workspace}", new object[] { ws });
            Assert.AreEqual("connection to workspace: {safe_workspace}", msg);
            Assert.AreEqual(new object[] { ws }, args);

            (msg, args) = CommonLogger.MakeLoggable("connection to workspace: {workspace}", new object[] { ws });
            Assert.AreEqual("connection to workspace: {workspace}", msg);
            Assert.AreEqual(new object[] { ws }, args);
        }


        [Test]
        public void Test_CommonLogger_MakeLoggable_PrivacyAware()
        {
            PrivacyAwareWorkspace ws = new PrivacyAwareWorkspace("workspaceId", "workspaceName");
            var (msg, args) = CommonLogger.MakeLoggable("connection to workspace: {safe_workspace}", new object[] { ws });
            Dictionary<string, string> expected = new Dictionary<string, string>() {
                { "type", ws.GetType().ToString() },
                { "WorkspaceId", ws.WorkspaceId },
                { "WorkspaceName", ws.WorkspaceName },
                { "safe_workspace", ws.ToString() },
            };
            Assert.AreEqual($"connection to workspace: {MakeFormat(expected.Keys)}", msg);
            Assert.AreEqual(expected.Values.ToArray(), args);

            (msg, args) = CommonLogger.MakeLoggable("connection to workspace: {workspace}", new object[] { ws });
            expected = new Dictionary<string, string>() {
                { "type", ws.GetType().ToString() },
                { "WorkspaceId", ws.WorkspaceId },
                { "WorkspaceName", ws.WorkspaceName },
            };
            Assert.AreEqual($"connection to workspace: {MakeFormat(expected.Keys)}", msg);
            Assert.AreEqual(expected.Values.ToArray(), args);
        }

        [Test]
        public void Test_CommonLogger_MakeLoggable_PrivacyAware_Throwing()
        {
            PrivacyAwareWorkspace ws = new PrivacyAwareWorkspace("workspaceId", "workspaceName", throwing: true);
            var (msg, args) = CommonLogger.MakeLoggable("connection to workspace: {safe_workspace}", new object[] { ws });
            Assert.AreEqual("connection to workspace: {safe_workspace}", msg);
            Assert.AreEqual(new object[] { ws }, args);

            (msg, args) = CommonLogger.MakeLoggable("connection to workspace: {workspace}", new object[] { ws });
            Assert.AreEqual("connection to workspace: {workspace}", msg);
            Assert.AreEqual(new object[] { ws }, args);
        }

        [Test]
        public void Test_CommonLogger_MakeLoggable_NoLoggableProperties()
        {
            PrivacyAwareWorkspace ws = new PrivacyAwareWorkspace(null, null);
            var (msg, args) = CommonLogger.MakeLoggable("connection to workspace: {safe_workspace}", new object[] { ws });
            Assert.AreEqual("connection to workspace: {safe_workspace}", msg);
            Assert.AreEqual(new object[] { ws }, args);

            (msg, args) = CommonLogger.MakeLoggable("connection to workspace: {workspace}", new object[] { ws });
            Assert.AreEqual("connection to workspace: {workspace}", msg);
            Assert.AreEqual(new object[] { ws }, args);
        }

        [Test]
        public void Test_CommonLogger_MakeLoggable_WithInner()
        {
            StorageException ex = InnerExceptions(1, (depth, inner) => StorageException(depth.ToString(), "Authentication Error", inner));
            var (msg, args) = CommonLogger.MakeLoggable("found an exception: {safe_exception}", new object[] { ex });
            Dictionary<string, string> expected = new Dictionary<string, string>() {
                { "type", ex.GetTypes() },
                { "HttpStatusMessage", ex.RequestInformation.HttpStatusMessage },
                { "Message", ex.Message },
                { "stack_trace", ex.StackTrace },
                { "inner_details", ex.InnerException.ToLogSafeExceptionString() },
            };
            Assert.AreEqual($"found an exception: {MakeFormat(expected.Keys)}", msg);
            Assert.AreEqual(expected.Values.ToArray(), args);

            (msg, args) = CommonLogger.MakeLoggable("found an exception: {exception}", new object[] { ex });
            expected["Message"] = $"[REDACT: {ex.GetType()}]";
            Assert.AreEqual($"found an exception: {MakeFormat(expected.Keys)}", msg);
            Assert.AreEqual(expected.Values.ToArray(), args);

            ex.Format();
            (msg, args) = CommonLogger.MakeLoggable("found an exception: {exception}", new object[] { ex });
            expected = new Dictionary<string, string>() {
                { "type", ex.GetTypes() },
                { "HttpStatusMessage", ex.RequestInformation.HttpStatusMessage },
                { "MessageFormat", ex.GetMessageFormat() },
                { "stack_trace", ex.StackTrace },
                { "inner_details", ex.InnerException.ToLogSafeExceptionString() },
            };
            Assert.AreEqual($"found an exception: {MakeFormat(expected.Keys)}", msg);
            Assert.AreEqual(expected.Values.ToArray(), args);
        }

        [Test]
        public void Test_CommonLogger_MakeLoggable_InjectingFormat()
        {
            string format = "this is a format: {value}";
            StorageException ex = InnerExceptions(0, (depth, inner) => StorageException(depth.ToString(), "Authentication Error", inner));
            var (message, args) = CommonLogger.MakeLoggable($"Format: {format} - Error: {{exception}}", args: new object[] { ex });

            // formats being injected into the message format doesn't work well for MakeLoggable, make sure it falls back to just returning the message/args as-is
            Assert.AreEqual($"Format: {format} - Error: {{exception}}", message);
            Assert.AreEqual(new object[] { ex }, args);
        }

        [Test]
        public void Test_CommonLogger_MakeLoggable_InterpolatedTraceId()
        {
            var traceId = "my-trace-id";
            StorageException ex = InnerExceptions(0, (depth, inner) => StorageException(depth.ToString(), "Authentication Error", inner));
            // this is to emulate this valid use-case: CommonLogger.LogEntityError("{interpolated_trace_id}", "oh noooooo, we hit an error: {exception}", traceId, ex);
            var (message, args) = CommonLogger.MakeLoggable("oh noooooo, we hit an error: {exception}", args: new object[] { traceId, ex });

            var expected = new Dictionary<string, string>() {
                { "type", ex.GetType().ToString() },
                { "HttpStatusMessage", ex.RequestInformation.HttpStatusMessage },
                { "Message", $"[REDACT: {ex.GetType()}]" },
                { "stack_trace", ex.StackTrace },
            };
            Assert.AreEqual($"oh noooooo, we hit an error: {MakeFormat(expected.Keys)}", message);
            var expectedArgs = new List<object>() { traceId };
            expectedArgs.AddRange(expected.Values);
            Assert.AreEqual(expectedArgs, args);
        }

        private string MakeFormat(IEnumerable<string> paramNames)
        {
            return $"[\n{string.Join(";\n", paramNames.Select(paramName => $"{paramName}: {{{paramName}}}"))}\n]";
        }
    }
}
