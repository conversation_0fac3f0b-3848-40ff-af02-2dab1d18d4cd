﻿using System;
using System.Threading.Tasks;
using System.Collections.Generic;

namespace Microsoft.RelInfra.Instrumentation.Logging.Test.Mocks
{
    internal class MockCommonLogger : ICommonLogger
    {
        public List<string> ErrorList { get; private set; }
        public List<string> WarningList { get; private set; }
        public List<string> InfoList { get; private set; }
        public List<string> DebugList { get; private set; }

        public MockCommonLogger()
        {
            ErrorList = new List<string>();
            WarningList = new List<string>();
            InfoList = new List<string>();
            DebugList = new List<string>();
        }

        public void LogError(string message, params object[] vars)
        {
            ErrorList.Add(string.Format(message, vars));
        }

        public void LogError(string message)
        {
            ErrorList.Add(message);
        }

        public void LogWarning(string message, params object[] vars)
        {
            WarningList.Add(string.Format(message, vars));
        }

        public void LogWarning(string message)
        {
            WarningList.Add(message);
        }

        public void LogInfo(string message, params object[] vars)
        {
            InfoList.Add(string.Format(message, vars));
        }

        public void LogInfo(string message)
        {
            InfoList.Add(message);
        }

        public void LogDebug(string message, params object[] vars)
        {
            DebugList.Add(string.Format(message, vars));
        }

        public void LogDebug(string message)
        {
            DebugList.Add(message);
        }

        public void LogEntityError(string entityId, string message, params object[] args)
        {
            ErrorList.Add(string.Format("[{0}] {1}", entityId, string.Format(message, args)));
        }

        public void LogEntityWarning(string entityId, string message, params object[] args)
        {
            WarningList.Add(string.Format("[{0}] {1}", entityId, string.Format(message, args)));
        }

        public void LogEntityInfo(string entityId, string message, params object[] args)
        {
            InfoList.Add(string.Format("[{0}] {1}", entityId, string.Format(message, args)));
        }

        public void LogEntityDebug(string entityId, string message, params object[] args)
        {
            DebugList.Add(string.Format("[{0}] {1}", entityId, string.Format(message, args)));
        }


        public void LogEntityError(string entityId, string message)
        {
            ErrorList.Add(string.Format("[{0}] {1}", entityId, message));
        }

        public void LogEntityWarning(string entityId, string message)
        {
            WarningList.Add(string.Format("[{0}] {1}", entityId, message));
        }

        public void LogEntityInfo(string entityId, string message)
        {
            InfoList.Add(string.Format("[{0}] {1}", entityId, message));
        }

        public void LogEntityDebug(string entityId, string message)
        {
            DebugList.Add(string.Format("[{0}] {1}", entityId, message));
        }

        public void LogMetric(string metric, long value, Dictionary<string, string> metricData = null)
        {
            throw new NotImplementedException();
        }

        public void Flush()
        {
        }
        
        public Task FlushAsync()
        {
            Flush();
            return Task.FromResult(true);
        }

        public void Dispose()
        {
        }
    }
}
