﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ResolveAssemblyWarnOrErrorOnTargetArchitectureMismatch>None</ResolveAssemblyWarnOrErrorOnTargetArchitectureMismatch>
    <PackageVersion>8.1.106</PackageVersion>
    <PackageDescription>Various instrumentation methods - logging, metric collection, etc for Relevance Infrastructure teams</PackageDescription>
    <PackageId>RelInfra.Instrumentation.Library</PackageId>
    <QInstrumentForCoverage>false</QInstrumentForCoverage>
  </PropertyGroup>
  <PropertyGroup>
    <NoWarn>1701;1702;NU5104</NoWarn>
  </PropertyGroup>
  <Import Project="..\..\..\nuget.props" />
  <ItemGroup>
    <PackageReference Include="JetBrains.Annotations" />
    <PackageReference Include="Microsoft.AIPlatform.Telemetry" />
    <PackageReference Include="Microsoft.Azure.KeyVault.Core" />
    <PackageReference Include="Microsoft.Extensions.Logging.Console" />
    <PackageReference Include="Newtonsoft.Json" />
    <PackageReference Include="WindowsAzure.Storage" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="$(SrcRoot)\Common\WebApi\Common.WebApi.csproj" />
  </ItemGroup>
</Project>
