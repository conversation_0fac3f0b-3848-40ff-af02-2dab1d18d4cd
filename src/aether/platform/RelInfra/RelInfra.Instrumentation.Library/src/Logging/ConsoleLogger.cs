﻿using Microsoft.Extensions.Logging;
using System;
using MsLoggingILogger = Microsoft.Extensions.Logging.ILogger;

namespace Microsoft.RelInfra.Instrumentation.Logging
{
    public class ConsoleLogger : MsLoggingILogger
    {
        public IDisposable BeginScope<TState>(TState state)
        {
            return null;
        }

        public bool IsEnabled(Microsoft.Extensions.Logging.LogLevel logLevel)
        {
            return true;
        }

        public void Log<TState>(Microsoft.Extensions.Logging.LogLevel logLevel, EventId eventId, TState state, Exception exception, Func<TState, Exception, string> formatter)
        {

            string message = string.Empty;

            if (formatter != null)
            {
                message = formatter(state, exception);
            }
            else
            {
                message = state?.ToString() ?? "";
            }

            Console.WriteLine(message);
        }
    }
}
