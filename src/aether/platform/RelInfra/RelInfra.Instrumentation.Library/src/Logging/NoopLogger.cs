﻿using System.Collections.Generic;
using System.Threading.Tasks;

namespace Microsoft.RelInfra.Instrumentation.Logging
{
    /// <summary>
    /// NOOP: dummy logger that doesn't do anything with the log messages
    /// </summary>
    public class NoopLogger : ICommonLogger
    {
        public void LogError(string message, params object[] args){}
        public void LogError(string message){}
        public void LogWarning(string message, params object[] args) { }
        public void LogWarning(string message){}
        public void LogInfo(string message, params object[] args){}
        public void LogInfo(string message){}
        public void LogDebug(string message, params object[] args) { }
        public void LogDebug(string message){}
        public void LogEntityError(string entityId, string message, params object[] args){}
        public void LogEntityWarning(string entityId, string message, params object[] args){}
        public void LogEntityInfo(string entityId, string message, params object[] args){}
        public void LogEntityDebug(string entityId, string message, params object[] args){}
        public void LogEntityError(string entityId, string message) { }
        public void LogEntityWarning(string entityId, string message) { }
        public void LogEntityInfo(string entityId, string message) { }
        public void LogEntityDebug(string entityId, string message) { }
        public void LogMetric(string metric, long value, Dictionary<string, string> metricData = null) { }
        public void Flush(){}
        public Task FlushAsync() { return Task.FromResult(true);}
        public void Dispose(){}
    }
}
