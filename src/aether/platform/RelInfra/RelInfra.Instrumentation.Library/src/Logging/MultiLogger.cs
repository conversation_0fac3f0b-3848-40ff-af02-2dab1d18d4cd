﻿using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Microsoft.RelInfra.Instrumentation.Logging
{
    /// <summary>
    /// This logger allows logging to multiple loggers with one call.
    /// </summary>
    public class MultiLogger : ICommonLogger
    {
        private readonly List<ICommonLogger> _loggers; 

        public MultiLogger(IEnumerable<ICommonLogger> loggers)
        {
            _loggers = loggers.ToList();
        }

        public void LogError(string message, params object[] args)
        {
            _loggers.ForEach(l => l.<PERSON>(message, args));
        }

        public void LogError(string message)
        {
            _loggers.ForEach(l => l.<PERSON>g<PERSON>(message));
        }

        public void LogWarning(string message, params object[] args)
        {
            _loggers.ForEach(l => l.Log<PERSON>arning(message, args));
        }

        public void LogWarning(string message)
        {
            _loggers.ForEach(l => l.<PERSON>(message));
        }

        public void LogInfo(string message, params object[] args)
        {
            _loggers.ForEach(l => l.LogInfo(message, args));
        }

        public void LogInfo(string message)
        {
            _loggers.ForEach(l => l.LogInfo(message));
        }

        public void LogDebug(string message, params object[] args)
        {
            _loggers.ForEach(l => l.LogDebug(message, args));
        }

        public void LogDebug(string message)
        {
            _loggers.ForEach(l => l.LogDebug(message));
        }

        public void LogEntityError(string entityId, string message, params object[] args)
        {
            _loggers.ForEach(l => l.LogEntityError(entityId, message, args));
        }

        public void LogEntityWarning(string entityId, string message, params object[] args)
        {
            _loggers.ForEach(l => l.LogEntityWarning(entityId, message, args));
        }

        public void LogEntityInfo(string entityId, string message, params object[] args)
        {
            _loggers.ForEach(l => l.LogEntityInfo(entityId, message, args));
        }

        public void LogEntityDebug(string entityId, string message, params object[] args)
        {
            _loggers.ForEach(l => l.LogEntityDebug(entityId, message, args));
        }

        public void LogEntityError(string entityId, string message)
        {
            _loggers.ForEach(l => l.LogEntityError(entityId, message));
        }

        public void LogEntityWarning(string entityId, string message)
        {
            _loggers.ForEach(l => l.LogEntityWarning(entityId, message));
        }

        public void LogEntityInfo(string entityId, string message)
        {
            _loggers.ForEach(l => l.LogEntityInfo(entityId, message));
        }

        public void LogEntityDebug(string entityId, string message)
        {
            _loggers.ForEach(l => l.LogEntityDebug(entityId, message));
        }

        public void LogMetric(string metric, long value, Dictionary<string, string> metricData = null)
        {
            _loggers.ForEach(l => l.LogMetric(metric, value, metricData));
        }

        public void Flush()
        {
            FlushAsync().Wait();
        }

        public virtual async Task FlushAsync()
        {
            await Task.WhenAll(_loggers.Select(logger => logger.FlushAsync()));
        }

        public void Dispose()
        {
            _loggers.ForEach(l => l.Dispose());
        }
    }
}
