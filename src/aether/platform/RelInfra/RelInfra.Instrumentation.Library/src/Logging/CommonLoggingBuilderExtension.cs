﻿using Microsoft.Extensions.DependencyInjection;
using MsLogging = Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging;
using Microsoft.RelInfra.Instrumentation.Logging.LoggerConversion;

namespace Microsoft.RelInfra.Instrumentation.Logging
{
    public static class CommonLoggingBuilderExtension
    {
        public static ILoggingBuilder AddCommonLogger(this ILoggingBuilder builder, MsLogging.LogLevel minLogLevel = MsLogging.LogLevel.Warning)
        {
            builder.Services.AddSingleton<ILoggerProvider, CommonLoggerProvider>((serviceProvider) => new CommonLoggerProvider(minLogLevel));

            // only log failed requests in CommonLogger
            // after applying following filters, message from specified category will be logged if it's equal or above the specified loglevel
            builder.AddFilter<CommonLoggerProvider>("Microsoft.AspNetCore.Hosting.Internal.WebHost", Microsoft.Extensions.Logging.LogLevel.Warning);
            builder.AddFilter<CommonLoggerProvider>("Microsoft.AspNetCore.Mvc.Internal.ControllerActionInvoker", Microsoft.Extensions.Logging.LogLevel.Warning);

            return builder;
        }
    }
}
