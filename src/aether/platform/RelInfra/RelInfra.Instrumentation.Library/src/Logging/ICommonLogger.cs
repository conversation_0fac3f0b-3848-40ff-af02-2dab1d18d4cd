﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using JetBrains.Annotations;

namespace Microsoft.RelInfra.Instrumentation.Logging
{
    /// <summary>
    /// This is the logging interface that 
    /// all services will use to create and store logs.
    /// </summary>
    public interface ICommonLogger : IDisposable
    {
        [StringFormatMethod("message")]
        void LogError(string message, params object[] args);

        void LogError(string message);

        [StringFormatMethod("message")]
        void LogWarning(string message, params object[] args);

        void LogWarning(string message);

        [StringFormatMethod("message")]
        void LogInfo(string message, params object[] args);

        void LogInfo(string message);

        [StringFormatMethod("message")]
        void LogDebug(string message, params object[] args);

        void LogDebug(string message);

        [StringFormatMethod("message")]
        void LogEntityError(string entityId, string message, params object[] args);

        [StringFormatMethod("message")]
        void LogEntityWarning(string entityId, string message, params object[] args);

        [StringFormatMethod("message")]
        void LogEntityInfo(string entityId, string message, params object[] args);

        [StringFormatMethod("message")]
        void LogEntityDebug(string entityId, string message, params object[] args);

        void LogEntityError(string entityId, string message);

        void LogEntityWarning(string entityId, string message);

        void LogEntityInfo(string entityId, string message);

        void LogEntityDebug(string entityId, string message);

        void LogMetric(string metric, long value, Dictionary<string, string> metricData = null);

        void Flush();

        Task FlushAsync();
    }
}
