﻿using System.Collections.Generic;
using System.Linq;

namespace Microsoft.RelInfra.Instrumentation.Logging
{
    public class MultiRelInfraLogger : ILogger
    {
        private readonly List<ILogger> _loggers;

        public MultiRelInfraLogger(params ILogger[] loggers)
        {
            _loggers = loggers.ToList();
        }

        public void LogDebug(string message)
        {
            _loggers.ForEach(l => l.Log<PERSON>ug(message));
        }

        public void LogDebug(string message, params object[] vars)
        {
            _loggers.ForEach(l => l.Log<PERSON>ebug(message, vars));
        }

        public void LogEntityDebug(string traceId, string message)
        {
            _loggers.ForEach(l => l.Log<PERSON>ntityDebug(traceId, message));
        }

        public void LogEntityDebug(string traceId, string message, params object[] args)
        {
            _loggers.ForEach(l => l.LogEntityDebug(traceId, message, args));
        }

        public void LogEntityDebug(string[] entities, string message)
        {
            _loggers.ForEach(l => l.Log<PERSON>ebug(entities, message));
        }

        public void LogEntityDebug(string[] traceIds, string message, params object[] args)
        {
            _loggers.ForEach(l => l.LogEntityDebug(traceIds, message, args));
        }

        public void LogEntityDebug2(string traceId, int sourceCodeLine, string sourceCodeFile, string sourceCodeMethod, string message, params object[] args)
        {
            _loggers.ForEach(l => l.LogEntityDebug2(traceId, sourceCodeLine, sourceCodeFile, sourceCodeMethod, message, args));
        }

        public void LogEntityDebug2(string[] traceIds, int sourceCodeLine, string sourceCodeFile, string sourceCodeMethod, string message, params object[] args)
        {
            _loggers.ForEach(l => l.LogEntityDebug2(traceIds, sourceCodeLine, sourceCodeFile, sourceCodeMethod, message, args));
        }

        public void LogEntityError(string traceId, string message)
        {
            _loggers.ForEach(l => l.LogEntityError(traceId, message));
        }

        public void LogEntityError(string traceId, string message, params object[] args)
        {
            _loggers.ForEach(l => l.LogEntityError(traceId, message, args));
        }

        public void LogEntityError(string[] entities, string message)
        {
            _loggers.ForEach(l => l.LogEntityError(entities, message));
        }

        public void LogEntityError(string[] traceIds, string message, params object[] args)
        {
            _loggers.ForEach(l => l.LogEntityError(traceIds, message, args));
        }

        public void LogEntityError2(string traceId, int sourceCodeLine, string sourceCodeFile, string sourceCodeMethod, string message, params object[] args)
        {
            _loggers.ForEach(l => l.LogEntityError2(traceId, sourceCodeLine, sourceCodeFile, sourceCodeMethod, message, args));
        }

        public void LogEntityError2(string[] traceIds, int sourceCodeLine, string sourceCodeFile, string sourceCodeMethod, string message, params object[] args)
        {
            _loggers.ForEach(l => l.LogEntityError2(traceIds, sourceCodeLine, sourceCodeFile, sourceCodeMethod, message, args));
        }

        public void LogEntityInfo(string traceId, string message)
        {
            _loggers.ForEach(l => l.LogEntityInfo(traceId, message));
        }

        public void LogEntityInfo(string traceId, string message, params object[] args)
        {
            _loggers.ForEach(l => l.LogEntityInfo(traceId, message, args));
        }

        public void LogEntityInfo(string[] entities, string message)
        {
            _loggers.ForEach(l => l.LogEntityInfo(entities, message));
        }

        public void LogEntityInfo(string[] traceIds, string message, params object[] args)
        {
            _loggers.ForEach(l => l.LogEntityInfo(traceIds, message, args));
        }

        public void LogEntityInfo2(string traceId, int sourceCodeLine, string sourceCodeFile, string sourceCodeMethod, string message, params object[] args)
        {
            _loggers.ForEach(l => l.LogEntityInfo2(traceId, sourceCodeLine, sourceCodeFile, sourceCodeMethod, message, args));
        }

        public void LogEntityInfo2(string[] traceIds, int sourceCodeLine, string sourceCodeFile, string sourceCodeMethod, string message, params object[] args)
        {
            _loggers.ForEach(l => l.LogEntityInfo2(traceIds, sourceCodeLine, sourceCodeFile, sourceCodeMethod, message, args));
        }

        public void LogEntityWarning(string traceId, string message)
        {
            _loggers.ForEach(l => l.LogEntityWarning(traceId, message));
        }

        public void LogEntityWarning(string traceId, string message, params object[] args)
        {
            _loggers.ForEach(l => l.LogEntityWarning(traceId, message, args));
        }

        public void LogEntityWarning(string[] entities, string message)
        {
            _loggers.ForEach(l => l.LogEntityWarning(entities, message));
        }

        public void LogEntityWarning(string[] traceIds, string message, params object[] args)
        {
            _loggers.ForEach(l => l.LogEntityWarning(traceIds, message, args));
        }

        public void LogEntityWarning2(string traceId, int sourceCodeLine, string sourceCodeFile, string sourceCodeMethod, string message, params object[] args)
        {
            _loggers.ForEach(l => l.LogEntityWarning2(traceId, sourceCodeLine, sourceCodeFile, sourceCodeMethod, message, args));
        }

        public void LogEntityWarning2(string[] traceIds, int sourceCodeLine, string sourceCodeFile, string sourceCodeMethod, string message, params object[] args)
        {
            _loggers.ForEach(l => l.LogEntityWarning2(traceIds, sourceCodeLine, sourceCodeFile, sourceCodeMethod, message, args));
        }

        public void LogError(string message)
        {
            _loggers.ForEach(l => l.LogError(message));
        }

        public void LogError(string message, params object[] vars)
        {
            _loggers.ForEach(l => l.LogError(message, vars));
        }

        public void LogInfo(string message)
        {
            _loggers.ForEach(l => l.LogInfo(message));
        }

        public void LogInfo(string message, params object[] vars)
        {
            _loggers.ForEach(l => l.LogInfo(message, vars));
        }

        public void LogWarning(string message)
        {
            _loggers.ForEach(l => l.LogWarning(message));
        }

        public void LogWarning(string message, params object[] vars)
        {
            _loggers.ForEach(l => l.LogWarning(message, vars));
        }
    }
}
