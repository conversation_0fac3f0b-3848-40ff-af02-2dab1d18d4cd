﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;

namespace Microsoft.RelInfra.Instrumentation.Logging
{
    public static class LoggingEventHelper
    {
        public static string FilterNoisyAsyncMethods(string message)
        {
            message = message.Replace("--- End of stack trace from previous location where exception was thrown ---", "")
                             .Replace("at System.Runtime.CompilerServices.TaskAwaiter.ThrowForNonSuccess(Task task)", "")
                             .Replace("at System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)", "")
                             .Replace("at System.Runtime.CompilerServices.TaskAwaiter`1.GetResult()", "")
                             .Replace("at System.Runtime.CompilerServices.TaskAwaiter.GetResult()", "")
                             .Replace("at System.Threading.Tasks.Task`1.GetResultCore(Boolean waitCompletionNotification)", "");

            IEnumerable<string> lines = message.Split(new[] { Environment.NewLine }, StringSplitOptions.RemoveEmptyEntries)
                                               .Where(line => !String.IsNullOrWhiteSpace(line));

            return string.Join(Environment.NewLine, lines);
        }
        
        public static string RemoveUnallowedCharacters(string original)
        {
            if (original == null)
            {
                return null;
            }

            original = original.Normalize();

            // remove invalid xml character
            // From xml spec valid chars: 
            // #x9 | #xA | #xD | [#x20-#xD7FF] | [#xE000-#xFFFD] | [#x10000-#x10FFFF]     
            // any Unicode character, excluding the surrogate blocks, FFFE, and FFFF. 
            const string pattern = @"[^\x09\x0A\x0D\x20-\uD7FF\uE000-\uFFFD\u10000-u10FFFF]";
            return Regex.Replace(original, pattern, string.Empty);
        }
    }
}
