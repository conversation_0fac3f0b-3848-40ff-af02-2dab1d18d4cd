﻿using Microsoft.AIPlatform.Telemetry.Logging.PrivacyAware;
using Microsoft.RelInfra.Instrumentation.Exceptions;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Microsoft.RelInfra.Instrumentation.Logging
{
    public static class LoggableExtensions
    {
        private static IExceptionLoggingPolicy _exceptionLoggingPolicy = new BaseExceptionLoggingPolicy();

        public static void InitializeForTest(IExceptionLoggingPolicy exceptionLoggingPolicy = null)
        {
            _exceptionLoggingPolicy = new AllowExceptionLoggingPolicy(exceptionLoggingPolicy ?? new BaseExceptionLoggingPolicy());
        }

        /// <summary>
        /// Initialize the extensions with an <see cref="IExceptionLoggingPolicy"/>. A default is provided if initialization has
        /// not occured, but may not include all the exception types your service encounters.
        /// </summary>
        /// <param name="exceptionLoggingPolicy"></param>
        /// <param name="failSilently"></param>
        public static void Initialize(IExceptionLoggingPolicy exceptionLoggingPolicy)
        {
            if (exceptionLoggingPolicy != null)
            {
                _exceptionLoggingPolicy = exceptionLoggingPolicy;
            }
        }

        /// <summary>
        /// Returns properties on the object that are loggable. Will be empty unless object contains
        /// <see cref="PrivacyClassificationAttribute"/>(s) (preferred), implements <see cref="ILoggable"/>,
        /// or is an <see cref="Exception"/> and has a mapping in the <see cref="IExceptionLoggingPolicy"/>
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        public static Dictionary<string, string> LoggableProperties(this object obj)
        {
            Dictionary<string, string> loggableProperties = new Dictionary<string, string>();
            try
            {
                // PrivacyAwareLogging: this will just ignore properties that throw an error
                AddAll(loggableProperties, obj.ToLogSafeValues().ToDictionary(kvp => kvp.Key, kvp => kvp.Value?.ToString()));
            }
            catch (Exception) { }
            try
            {
                if (obj is ILoggable loggable)
                {
                    AddAll(loggableProperties, loggable.LogSafeValues());
                }
            }
            catch (Exception) { }
            try
            {
                if (obj is Exception ex)
                {
                    AddAll(loggableProperties, _exceptionLoggingPolicy.LoggableProperties(ex));
                }
            }
            catch (Exception) { }
            try
            {
                if (obj is Exception ex)
                {
                    if (!loggableProperties.ContainsKey(BaseExceptionLoggingPolicy.MessageStr) && !loggableProperties.ContainsKey(BaseExceptionLoggingPolicy.MessageFormatStr) && ex.IsFormattable())
                    {
                        // the message format is required to be log-safe and gives the logs way more context
                        loggableProperties.Add(BaseExceptionLoggingPolicy.MessageFormatStr, ex.GetMessageFormat());
                    }
                    return loggableProperties;
                }
            }
            catch (Exception) { }
            return loggableProperties;
        }

        internal static Dictionary<string, string> LogSafeExceptionProperties(this Exception exception, bool isMessageSafe = false)
        {
            Dictionary<string, string> loggableProperties = LoggableProperties(exception);
            if (isMessageSafe)
            {
                if (!loggableProperties.ContainsKey(BaseExceptionLoggingPolicy.MessageStr))
                {
                    loggableProperties[BaseExceptionLoggingPolicy.MessageStr] = exception.Message;
                }
                // message format will just clutter since message is already present so we remove it
                loggableProperties.Remove(BaseExceptionLoggingPolicy.MessageFormatStr);
            }
            else if (!loggableProperties.ContainsKey(BaseExceptionLoggingPolicy.MessageStr) && !loggableProperties.ContainsKey(BaseExceptionLoggingPolicy.MessageFormatStr))
            {
                // make it clear to engineers that the exception had to be redacted and to support log searching for redacted logs
                loggableProperties.Add(BaseExceptionLoggingPolicy.MessageStr, Redact(exception));
            }

            return loggableProperties;
        }

        internal static string LoggablePropertiesToString<T>(Dictionary<string, T> loggableProperties)
        {
            return $"[\n{string.Join(";\n", loggableProperties.Where(kvp => kvp.Value != null).Select(kvp => $"{kvp.Key}: {kvp.Value}"))}\n]";
        }

        internal static string Redact(object obj, bool includeType = true)
        {
            if (includeType)
            {
                /*
                 * we want to return REDACT and the type so it's easier to investigate (type is better than nothing)
                 * AND because this will allow us to identify highly redacted types that we may be able to add to the
                 * exceptionLoggingPolicy.
                 */
                return $"[REDACT: {obj.GetType()}]";
            }
            return "[REDACT]";
        }

        /// <summary>
        /// Copy of <see cref="Exception.ToString()"/>, but using <see cref="LogSafeExceptionProperties(Exception, bool)"/> instead of <see cref="Exception.Message"/>
        /// </summary>
        /// <param name="e"></param>
        /// <param name="isMessageSafe">if true will include the top-level exception's message, but not inner exception messages</param>
        /// <param name="depth">the zero-based depth to print (e.g. the default depth = 0 will print the exception message)</param>
        /// <param name="includeBase">if true will return a string that includes exceptions up to the given depth + the base exception (skipping any between depth to base)</param>
        /// <returns></returns>
        public static string ToLogSafeExceptionString(this Exception e, int depth = 0, bool isMessageSafe = false, bool includeBase = true)
        {
            if (depth < 0) return null;

            // type will be included twice when redaction is applied, but this will allow us to easily search logs for highly redacted exception types
            string message = LoggablePropertiesToString(e.LogSafeExceptionProperties(isMessageSafe: isMessageSafe));
            string s;

            if (message == null || message.Length <= 0)
            {
                s = e.GetType().ToString();
            }
            else
            {
                s = e.GetType().ToString() + ": " + message;
            }

            if (e.InnerException != null)
            {
                // intentionally don't pass isMessageSafe any further as we can only assume that safety applies to the top-level exception
                string innerStr = e.InnerException.ToLogSafeExceptionString(depth: depth - 1, includeBase: includeBase);
                if (includeBase && innerStr == null)
                {
                    Exception baseEx = e.GetBaseException();
                    var prefix = baseEx != e ? "(skipping to base exception) " : "";
                    innerStr = $"{prefix}{baseEx.ToLogSafeExceptionString()}";
                }
                s += " ---> " + innerStr + Environment.NewLine + "   " +
                    "--- End of stack trace from previous location where exception was thrown ---";// Environment.GetResourceString("Exception_EndOfInnerExceptionStack");
            }

            if (e.StackTrace != null)
            {
                s += Environment.NewLine + e.StackTrace;
            }

            return s;
        }

        internal static Dictionary<K, V> AddAll<Dict, K, V>(Dictionary<K, V> dict, Dict other, bool overrideExisting = true) where Dict : IDictionary<K, V>
        {
            foreach (var kvp in other)
            {
                if (kvp.Value != null)
                {
                    if (overrideExisting || !dict.ContainsKey(kvp.Key))
                    {
                        dict[kvp.Key] = kvp.Value;
                    }
                }
            }
            return dict;
        }
    }
}
