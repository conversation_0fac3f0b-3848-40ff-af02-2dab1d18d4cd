﻿using System.Collections.Generic;

namespace Microsoft.RelInfra.Instrumentation.Logging
{
    public interface ILoggable
    {
        /// <summary>
        /// Get the log-safe values/properties/fields from the object. If logging with <see cref="CommonLogger"/>
        /// then the keys in this dictionary will be customDimensions_{key} column for query in geneva/kusto.
        /// </summary>
        /// <returns>the log-safe values/properties/fields of the object</returns>
        Dictionary<string, string> LogSafeValues();
    }
}
