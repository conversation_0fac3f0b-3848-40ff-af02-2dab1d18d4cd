﻿using Microsoft.AIPlatform.Telemetry.Contracts.Logging.PrivacyAware;
using Microsoft.RelInfra.Instrumentation.Exceptions;
using Microsoft.Extensions.Logging.ApplicationInsights;
using Microsoft.AIPlatform.Telemetry.Startup.Contracts.Scrubbing;
using System;
using System.Collections.Generic;
using Microsoft.Extensions.Logging;

namespace Microsoft.RelInfra.Instrumentation.Logging
{
    public static class K8SCommonLogger
    {
        // <summary>
        /// Init logger using the AML Telemetry Library (ATL)
        /// </summary>
        /// <param name="serviceName"></param>
        /// <param name="logSourceId"></param>
        /// <param name="scrubbingConfig"></param>
        /// <param name="exceptionLoggingPolicy"></param>
        public static void InitWithAtl(string serviceName, ILoggerFactory loggerFactory, ScrubbingConfig scrubbingConfig, IExceptionLoggingPolicy exceptionLoggingPolicy)
        {
            CommonLogger.InitWithAtlLogger(
                serviceName: serviceName,
                loggerFactory: loggerFactory,
                scrubbingConfig: scrubbingConfig,
                exceptionLoggingPolicy: exceptionLoggingPolicy);
        }
    }
}
