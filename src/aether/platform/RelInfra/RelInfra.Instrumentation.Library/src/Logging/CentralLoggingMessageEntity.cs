﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;

namespace Microsoft.RelInfra.Instrumentation.Logging
{
    // TODO: <PERSON><PERSON><PERSON>, NabeelS : make this entity immutable 
    [DataContract]
    public class CentralLoggingMessageBase
    {
        [DataMember]
        public DateTime TimeStamp { get; set; }

        [DataMember]
        public string ServiceName { get; set; }

        [DataMember]
        public string MachineName { get; set; }

        [DataMember]
        public string TraceId { get; set; }

        public virtual string FormatAsTabString()
        {
            return string.Join("\t",
                TimeStamp.ToString("O"),
                SanitizeString(ServiceName),
                SanitizeString(MachineName),
                SanitizeString(TraceId));
        }

        protected static string SanitizeString(string msg)
        {
            return msg.Replace("\t", "=TAB=")
                      .Replace("\n", "=N=")
                      .Replace("\r", "=R=");
        }
    }

    [DataContract]
    internal class CentralLoggingMessageEntity : CentralLoggingMessageBase
    {
        [DataMember]
        public string LogLevel { get; set; }

        [DataMember]
        public string Message { get; set; }

        [DataMember]
        public int SourceCodeLine { get; set; }

        [DataMember]
        public string SourceCodeFile { get; set; }

        [DataMember]
        public string SourceCodeMethod { get; set; }

        public override string FormatAsTabString()
        {
            return string.Join("\t",
                    TimeStamp.ToString("O"),
                    SanitizeString(ServiceName),
                    SanitizeString(MachineName),
                    SanitizeString(LogLevel),
                    SanitizeString(TraceId),
                    SanitizeString(Message));
        }

        public override string ToString()
        {
            return String.Format("[{0}] {1}", TraceId, Message);
        }
    }
}
