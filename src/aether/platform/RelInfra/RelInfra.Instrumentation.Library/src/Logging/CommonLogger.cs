﻿// <copyright file="CommonLogger.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using JetBrains.Annotations;
using Microsoft.AIPlatform.Telemetry.Startup.Contracts;
using Microsoft.AIPlatform.Telemetry.Startup.Contracts.Scrubbing;
using Microsoft.Extensions.Logging;
using Microsoft.RelInfra.Instrumentation.Exceptions;
using Microsoft.RelInfra.Instrumentation.Logging.LoggerConversion;
using Newtonsoft.Json;

namespace Microsoft.RelInfra.Instrumentation.Logging
{
    /// <summary>
    /// The main static logging class. 
    /// This is basically a simple static wrapper for the logger
    /// </summary>
    public static class CommonLogger
    {
        #region Configuration/Initialization

        // TODO-dehyatt: get rid of ILogger (6 references) all together and just use ICommonLogger...
        private static ILogger _instance = new MsLoggerToRelInfraLogger(
                logger: new ConsoleLogger(),
                serviceName: "Service");
        internal static ScrubbingConfig _scrubbingConfig = new AmlTelemetryConfiguration().Scrubbing;
        private static AsyncLocal<ICollection<KeyValuePair<string, object>>> _customFields = new AsyncLocal<ICollection<KeyValuePair<string, object>>> ();

        /// <summary>
        /// Init logger using app settings file with Log4NetLogger
        /// </summary>
        /// <param name="serviceName"></param>
        public static void InitForTests(string serviceName)
        {
            _instance = new MsLoggerToRelInfraLogger(
                logger: new ConsoleLogger(),
                serviceName: serviceName);
            LoggableExtensions.InitializeForTest();
            FormattableExceptionExtensions.FailSilently = false;
        }

        public static void InitWithAtlLogger(string serviceName, ILoggerFactory loggerFactory, ScrubbingConfig scrubbingConfig, IExceptionLoggingPolicy exceptionLoggingPolicy = null)
        {
            _instance = new MsLoggerToRelInfraLogger(
                logger: loggerFactory.CreateLogger(serviceName),
                serviceName: serviceName);
            LoggableExtensions.Initialize(exceptionLoggingPolicy: exceptionLoggingPolicy);
            _scrubbingConfig = scrubbingConfig;
        }

        public static void SetCustomFields(ICollection<KeyValuePair<string, object>> customFields)
        {
            _customFields.Value = customFields;
        }

        public static void AddCustomFields(KeyValuePair<string, object> customField)
        {
            _customFields.Value.Add(customField);
        }

        #endregion

        public static ILogger Instance { get => _instance; }

        #region Logging functions

        [StringFormatMethod("message")]
        public static void LogError(string message, params object[] args)
        {
            try
            {
                var (newMessage, newArgs) = MakeLoggable(message, args);
                var (updatedMessage, updatedArgs) = AddCustomFields(newMessage, newArgs);
                _instance.LogError(updatedMessage, updatedArgs);
            }
            catch (Exception ex)
            {
                _instance.LogError($"Logging with format string {message}, failed due to exception: {ex.ToString()}"); // swallow logging exceptions
            }
        }

        [StringFormatMethod("message")]
        public static void LogWarning(string message, params object[] args)
        {
            try
            {
                var (newMessage, newArgs) = MakeLoggable(message, args);
                var (updatedMessage, updatedArgs) = AddCustomFields(newMessage, newArgs);
                _instance.LogWarning(updatedMessage, updatedArgs);
            }
            catch (Exception ex)
            {
                _instance.LogError($"Logging with format string {message}, failed due to exception: {ex.ToString()}"); // swallow logging exceptions
            }
        }

        [StringFormatMethod("message")]
        public static void LogInfo(string message, params object[] args)
        {
            try
            {
                var (newMessage, newArgs) = MakeLoggable(message, args);
                var (updatedMessage, updatedArgs) = AddCustomFields(newMessage, newArgs);
                _instance.LogInfo(updatedMessage, updatedArgs);
            }
            catch (Exception ex)
            {
                _instance.LogError($"Logging with format string {message}, failed due to exception: {ex.ToString()}"); // swallow logging exceptions
            }
        }

        [StringFormatMethod("message")]
        public static void LogDebug(string message, params object[] args)
        {
            try
            {
                var (newMessage, newArgs) = MakeLoggable(message, args);
                var (updatedMessage, updatedArgs) = AddCustomFields(newMessage, newArgs);
                _instance.LogDebug(updatedMessage, updatedArgs);
            }
            catch (Exception ex)
            {
                _instance.LogError($"Logging with format string {message}, failed due to exception: {ex.ToString()}"); // swallow logging exceptions
            }
        }

        [StringFormatMethod("message")]
        public static void LogEntityError(string traceId, string message, params object[] args)
        {
            try
            {
                var (newMessage, newArgs) = MakeLoggable(message, args);
                var (updatedMessage, updatedArgs) = AddCustomFields(newMessage, newArgs);
                _instance.LogEntityError(traceId, updatedMessage, updatedArgs);
            }
            catch (Exception ex)
            {
                _instance.LogError($"Logging for Id {traceId}, with format string {message}, failed due to exception: {ex.ToString()}"); // swallow logging exceptions
            }
        }

        [StringFormatMethod("message")]
        public static void LogEntityWarning(string traceId, string message, params object[] args)
        {
            try
            {
                var (newMessage, newArgs) = MakeLoggable(message, args);
                var (updatedMessage, updatedArgs) = AddCustomFields(newMessage, newArgs);
                _instance.LogEntityWarning(traceId, updatedMessage, updatedArgs);
            }
            catch (Exception ex)
            {
                _instance.LogError($"Logging for Id {traceId}, with format string {message}, failed due to exception: {ex.ToString()}"); // swallow logging exceptions
            }
        }

        [StringFormatMethod("message")]
        public static void LogEntityInfo(string traceId, string message, params object[] args)
        {
            try
            {
                var (newMessage, newArgs) = MakeLoggable(message, args);
                var (updatedMessage, updatedArgs) = AddCustomFields(newMessage, newArgs);
                _instance.LogEntityInfo(traceId, updatedMessage, updatedArgs);
            }
            catch (Exception ex)
            {
                _instance.LogError($"Logging for Id {traceId}, with format string {message}, failed due to exception: {ex.ToString()}"); // swallow logging exceptions
            }
        }

        [StringFormatMethod("message")]
        public static void LogEntityDebug(string traceId, string message, params object[] args)
        {
            try
            {
                var (newMessage, newArgs) = MakeLoggable(message, args);
                var (updatedMessage, updatedArgs) = AddCustomFields(newMessage, newArgs);
                _instance.LogEntityDebug(traceId, updatedMessage, updatedArgs);
            }
            catch (Exception ex)
            {
                _instance.LogError($"Logging for Id {traceId}, with format string {message}, failed due to exception: {ex.ToString()}"); // swallow logging exceptions
            }
        }

        [StringFormatMethod("message")]
        public static void LogEntityError(string[] traceIds, string message, params object[] args)
        {
            try
            {
                var (newMessage, newArgs) = MakeLoggable(message, args);
                var (updatedMessage, updatedArgs) = AddCustomFields(newMessage, newArgs);
                _instance.LogEntityError(traceIds, updatedMessage, updatedArgs);
            }
            catch (Exception ex)
            {
                _instance.LogError($"Logging for one or more of Ids {string.Join(",", traceIds)}, with format string {message}, failed due to exception: {ex.ToString()}"); // swallow logging exceptions
            }
        }

        [StringFormatMethod("message")]
        public static void LogEntityWarning(string[] traceIds, string message, params object[] args)
        {
            try
            {
                var (newMessage, newArgs) = MakeLoggable(message, args);
                var (updatedMessage, updatedArgs) = AddCustomFields(newMessage, newArgs);
                _instance.LogEntityWarning(traceIds, updatedMessage, updatedArgs);
            }
            catch (Exception ex)
            {
                _instance.LogError($"Logging for one or more of Ids {string.Join(",", traceIds)}, with format string {message}, failed due to exception: {ex.ToString()}"); // swallow logging exceptions
            }
        }

        [StringFormatMethod("message")]
        public static void LogEntityInfo(string[] traceIds, string message, params object[] args)
        {
            try
            {
                var (newMessage, newArgs) = MakeLoggable(message, args);
                var (updatedMessage, updatedArgs) = AddCustomFields(newMessage, newArgs);
                _instance.LogEntityInfo(traceIds, updatedMessage, updatedArgs);
            }
            catch (Exception ex)
            {
                _instance.LogError($"Logging for one or more of Ids {string.Join(",", traceIds)}, with format string {message}, failed due to exception: {ex.ToString()}"); // swallow logging exceptions
            }
        }

        [StringFormatMethod("message")]
        public static void LogEntityDebug(string[] traceIds, string message, params object[] args)
        {
            try
            {
                var (newMessage, newArgs) = MakeLoggable(message, args);
                var (updatedMessage, updatedArgs) = AddCustomFields(newMessage, newArgs);
                _instance.LogEntityDebug(traceIds, updatedMessage, updatedArgs);
            }
            catch (Exception ex)
            {
                _instance.LogError($"Logging for one or more of Ids {string.Join(",", traceIds)}, with format string {message}, failed due to exception: {ex.ToString()}"); // swallow logging exceptions
            }
        }

        public static void LogError(string message)
        {
            try
            {
                var (updatedMessage, updatedArgs) = AddCustomFields(message, new object[0]);
                _instance.LogError("{0}", updatedMessage, updatedArgs);
            }
            catch (Exception ex)
            {
                _instance.LogError($"Logging with format string {message}, failed due to exception: {ex.ToString()}"); // swallow logging exceptions
            }
        }

        public static void LogWarning(string message)
        {
            try
            {
                var (updatedMessage, updatedArgs) = AddCustomFields(message, new object[0]);
                _instance.LogWarning(updatedMessage, updatedArgs);
            }
            catch (Exception ex)
            {
                _instance.LogError($"Logging with format string {message}, failed due to exception: {ex.ToString()}"); // swallow logging exceptions
            }
        }

        public static void LogInfo(string message)
        {
            try
            {
                var (updatedMessage, updatedArgs) = AddCustomFields(message, new object[0]);
                _instance.LogInfo(updatedMessage, updatedArgs);
            }
            catch (Exception ex)
            {
                _instance.LogError($"Logging with format string {message}, failed due to exception: {ex.ToString()}"); // swallow logging exceptions
            }
        }

        public static void LogDebug(string message)
        {
            try
            {
                var (updatedMessage, updatedArgs) = AddCustomFields(message, new object[0]);
                _instance.LogDebug(updatedMessage, updatedArgs);
            }
            catch (Exception ex)
            {
                _instance.LogError($"Logging with format string {message}, failed due to exception: {ex.ToString()}"); // swallow logging exceptions
            }
        }

        public static void LogEntityError(string traceId, string message)
        {
            try
            {
                var (updatedMessage, updatedArgs) = AddCustomFields(message, new object[0]);
                _instance.LogEntityError(traceId, updatedMessage, updatedArgs);
            }
            catch (Exception ex)
            {
                _instance.LogError($"Logging for Id {traceId}, with format string {message}, failed due to exception: {ex.ToString()}"); // swallow logging exceptions
            }
        }

        public static void LogEntityWarning(string traceId, string message)
        {
            try
            {
                var (updatedMessage, updatedArgs) = AddCustomFields(message, new object[0]);
                _instance.LogEntityWarning(traceId, updatedMessage, updatedArgs);
            }
            catch (Exception ex)
            {
                _instance.LogError($"Logging for Id {traceId}, with format string {message}, failed due to exception: {ex.ToString()}"); // swallow logging exceptions
            }
        }

        public static void LogEntityInfo(string traceId, string message)
        {
            try
            {
                var (updatedMessage, updatedArgs) = AddCustomFields(message, new object[0]);
                _instance.LogEntityInfo(traceId, updatedMessage, updatedArgs);
            }
            catch (Exception ex)
            {
                _instance.LogError($"Logging for Id {traceId}, with format string {message}, failed due to exception: {ex.ToString()}"); // swallow logging exceptions
            }
        }

        public static void LogEntityDebug(string traceId, string message)
        {
            try
            {
                var (updatedMessage, updatedArgs) = AddCustomFields(message, new object[0]);
                _instance.LogEntityDebug(traceId, updatedMessage, updatedArgs);
            }
            catch (Exception ex)
            {
                _instance.LogError($"Logging for Id {traceId}, with format string {message}, failed due to exception: {ex.ToString()}"); // swallow logging exceptions
            }
        }

        public static void LogEntityError(string[] entities, string message)
        {
            try
            {
                var (updatedMessage, updatedArgs) = AddCustomFields(message, new object[0]);
                _instance.LogEntityError(entities, updatedMessage, updatedArgs);
            }
            catch (Exception ex)
            {
                _instance.LogError($"Logging for one or more of Ids {string.Join(",", entities)}, with format string {message}, failed due to exception: {ex.ToString()}"); // swallow logging exceptions
            }
        }

        public static void LogEntityWarning(string[] entities, string message)
        {
            try
            {
                var (updatedMessage, updatedArgs) = AddCustomFields(message, new object[0]);
                _instance.LogEntityWarning(entities, updatedMessage, updatedArgs);
            }
            catch (Exception ex)
            {
                _instance.LogError($"Logging for one or more of Ids {string.Join(",", entities)}, with format string {message}, failed due to exception: {ex.ToString()}"); // swallow logging exceptions
            }
        }

        public static void LogEntityInfo(string[] entities, string message)
        {
            try
            {
                var (updatedMessage, updatedArgs) = AddCustomFields(message, new object[0]);
                _instance.LogEntityInfo(entities, updatedMessage, updatedArgs);
            }
            catch (Exception ex)
            {
                _instance.LogError($"Logging for one or more of Ids {string.Join(",", entities)}, with format string {message}, failed due to exception: {ex.ToString()}"); // swallow logging exceptions
            }
        }

        public static void LogEntityDebug(string[] entities, string message)
        {
            try
            {
                var (updatedMessage, updatedArgs) = AddCustomFields(message, new object[0]);
                _instance.LogEntityDebug(entities, message, updatedArgs);
            }
            catch (Exception ex)
            {
                _instance.LogError($"Logging for one or more of Ids {string.Join(",", entities)}, with format string {message}, failed due to exception: {ex.ToString()}"); // swallow logging exceptions
            }
        }

        public static void LogEntityError2(string traceId, string message,
            [System.Runtime.CompilerServices.CallerMemberName] string memberName = "",
            [System.Runtime.CompilerServices.CallerFilePath] string sourceFilePath = "",
            [System.Runtime.CompilerServices.CallerLineNumber] int sourceLineNumber = 0,
            object[] args = null)
        {
            try
            {
                var (updatedMessage, updatedArgs) = AddCustomFields(message, args);
                _instance.LogEntityError2(traceId,
                    sourceCodeLine: sourceLineNumber,
                    sourceCodeFile: sourceFilePath,
                    sourceCodeMethod: memberName,
                    message: updatedMessage,
                    args: updatedArgs ?? new object[0]);
            }
            catch (Exception ex)
            {
                _instance.LogError($"Logging for one or more of Ids {traceId} in file {sourceFilePath} at line {sourceLineNumber}, with format string {message}, failed due to exception: {ex.ToString()}"); // swallow logging exceptions
            }
        }

        public static void LogEntityWarning2(string traceId, string message,
            [System.Runtime.CompilerServices.CallerMemberName] string memberName = "",
            [System.Runtime.CompilerServices.CallerFilePath] string sourceFilePath = "",
            [System.Runtime.CompilerServices.CallerLineNumber] int sourceLineNumber = 0,
            object[] args = null)
        {
            try
            {
                var (updatedMessage, updatedArgs) = AddCustomFields(message, args);
                _instance.LogEntityWarning2(traceId,
                    sourceCodeLine: sourceLineNumber,
                    sourceCodeFile: sourceFilePath,
                    sourceCodeMethod: memberName,
                    message: updatedMessage,
                    args: updatedArgs ?? new object[0]);
            }
            catch (Exception ex)
            {
                _instance.LogError($"Logging for one or more of Ids {traceId} in file {sourceFilePath} at line {sourceLineNumber}, with format string {message}, failed due to exception: {ex.ToString()}"); // swallow logging exceptions
            }
        }

        public static void LogEntityInfo2(string traceId, string message,
            [System.Runtime.CompilerServices.CallerMemberName] string memberName = "",
            [System.Runtime.CompilerServices.CallerFilePath] string sourceFilePath = "",
            [System.Runtime.CompilerServices.CallerLineNumber] int sourceLineNumber = 0,
            object[] args = null)
        {
            try
            {
                var (updatedMessage, updatedArgs) = AddCustomFields(message, args);
                _instance.LogEntityInfo2(traceId,
                    sourceCodeLine: sourceLineNumber,
                    sourceCodeFile: sourceFilePath,
                    sourceCodeMethod: memberName,
                    message: updatedMessage,
                    args: updatedArgs ?? new object[0]);
            }
            catch (Exception ex)
            {
                _instance.LogError($"Logging for one or more of Ids {traceId} in file {sourceFilePath} at line {sourceLineNumber}, with format string {message}, failed due to exception: {ex.ToString()}"); // swallow logging exceptions
            }
        }

        public static void LogEntityDebug2(string traceId, string message,
            [System.Runtime.CompilerServices.CallerMemberName] string memberName = "",
            [System.Runtime.CompilerServices.CallerFilePath] string sourceFilePath = "",
            [System.Runtime.CompilerServices.CallerLineNumber] int sourceLineNumber = 0,
            object[] args = null)
        {
            try
            {
                var (updatedMessage, updatedArgs) = AddCustomFields(message, args);
                _instance.LogEntityDebug2(traceId,
                    sourceCodeLine: sourceLineNumber,
                    sourceCodeFile: sourceFilePath,
                    sourceCodeMethod: memberName,
                    message: updatedMessage,
                    args: updatedArgs ?? new object[0]);
            }
            catch (Exception ex)
            {
                _instance.LogError($"Logging for one or more of Ids {traceId} in file {sourceFilePath} at line {sourceLineNumber}, with format string {message}, failed due to exception: {ex.ToString()}"); // swallow logging exceptions
            }
        }

        public static void LogEntityError2(string[] traceIds, string message,
            [System.Runtime.CompilerServices.CallerMemberName] string memberName = "",
            [System.Runtime.CompilerServices.CallerFilePath] string sourceFilePath = "",
            [System.Runtime.CompilerServices.CallerLineNumber] int sourceLineNumber = 0,
            object[] args = null)
        {
            try
            {
                var (updatedMessage, updatedArgs) = AddCustomFields(message, args);
                _instance.LogEntityError2(traceIds,
                    sourceCodeLine: sourceLineNumber,
                    sourceCodeFile: sourceFilePath,
                    sourceCodeMethod: memberName,
                    message: updatedMessage,
                    args: updatedArgs ?? new object[0]);
            }
            catch (Exception ex)
            {
                _instance.LogError($"Logging for one or more of Ids {traceIds} in file {sourceFilePath} at line {sourceLineNumber}, with format string {message}, failed due to exception: {ex.ToString()}"); // swallow logging exceptions
            }
        }

        public static void LogEntityWarning2(string[] traceIds, string message,
            [System.Runtime.CompilerServices.CallerMemberName] string memberName = "",
            [System.Runtime.CompilerServices.CallerFilePath] string sourceFilePath = "",
            [System.Runtime.CompilerServices.CallerLineNumber] int sourceLineNumber = 0,
            object[] args = null)
        {
            try
            {
                var (updatedMessage, updatedArgs) = AddCustomFields(message, args);
                _instance.LogEntityWarning2(traceIds,
                    sourceCodeLine: sourceLineNumber,
                    sourceCodeFile: sourceFilePath,
                    sourceCodeMethod: memberName,
                    message: updatedMessage,
                    args: updatedArgs ?? new object[0]);
            }
            catch (Exception ex)
            {
                _instance.LogError($"Logging for one or more of Ids {traceIds} in file {sourceFilePath} at line {sourceLineNumber}, with format string {message}, failed due to exception: {ex.ToString()}"); // swallow logging exceptions
            }
        }

        public static void LogEntityInfo2(string[] traceIds, string message,
            [System.Runtime.CompilerServices.CallerMemberName] string memberName = "",
            [System.Runtime.CompilerServices.CallerFilePath] string sourceFilePath = "",
            [System.Runtime.CompilerServices.CallerLineNumber] int sourceLineNumber = 0,
            object[] args = null)
        {
            try
            {
                var (updatedMessage, updatedArgs) = AddCustomFields(message, args);
                _instance.LogEntityInfo2(traceIds,
                    sourceCodeLine: sourceLineNumber,
                    sourceCodeFile: sourceFilePath,
                    sourceCodeMethod: memberName,
                    message: updatedMessage,
                    args: updatedArgs ?? new object[0]);
            }
            catch (Exception ex)
            {
                _instance.LogError($"Logging for one or more of Ids {traceIds} in file {sourceFilePath} at line {sourceLineNumber}, with format string {message}, failed due to exception: {ex.ToString()}"); // swallow logging exceptions
            }
        }

        public static void LogEntityDebug2(string[] traceIds, string message,
            [System.Runtime.CompilerServices.CallerMemberName] string memberName = "",
            [System.Runtime.CompilerServices.CallerFilePath] string sourceFilePath = "",
            [System.Runtime.CompilerServices.CallerLineNumber] int sourceLineNumber = 0,
            object[] args = null)
        {
            try
            {
                var (updatedMessage, updatedArgs) = AddCustomFields(message, args);
                _instance.LogEntityDebug2(traceIds,
                    sourceCodeLine: sourceLineNumber,
                    sourceCodeFile: sourceFilePath,
                    sourceCodeMethod: memberName,
                    message: updatedMessage,
                    args: updatedArgs ?? new object[0]);
            }
            catch (Exception ex)
            {
                _instance.LogError($"Logging for one or more of Ids {traceIds} in file {sourceFilePath} at line {sourceLineNumber}, with format string {message}, failed due to exception: {ex.ToString()}"); // swallow logging exceptions
            }
        }

        public static void LogMetric(string metric, long value, Dictionary<string, string> metricData = null)
        {
            string metricInfo = metricData == null ? "NULL" : JsonConvert.SerializeObject(metricData);
            LogEntityInfo(traceId: "LogMetrics", message: $"Metric:{metric} Value:{value} MetricData:{metricInfo}");
        }

        public static void LogMetric(string metric, long value, string metricDataKey, string metricDataValue)
        {
            LogEntityInfo(traceId: "LogMetrics", message: $"Metric:{metric} Value:{value} DataKey:{metricDataKey} MetricDataValue:{metricDataValue}");
        }

        /// <summary>
        /// Start event for logging latency
        /// </summary>
        /// <param name="metric">Metric name</param>
        /// <param name="metricId">Metric instance id, if null it's generated by the method</param>
        /// <param name="metricData">Additional data to save with a metric</param>
        /// <returns></returns>
        public static void BeginLogLatency(string metric, string metricId = null, Dictionary<string, string> metricData = null)
        {
            string metricInfo = metricData == null ? "NULL" : JsonConvert.SerializeObject(metricData);
            LogEntityInfo(traceId: "LogMetrics_BeginLatency", message: $"Metric:{metric} MetricId:{metricId} MetricData:{metricInfo}");
        }

        /// <summary>
        /// End event for logging latency
        /// </summary>
        /// <param name="metricId">Metric instance id returned by BeginLogLatency</param>
        /// <param name="tolerateBeginAbsence">If this flag is set to true then central logging service will treat begin absence as a normal behaviour</param>
        public static void EndLogLatency(string metricId)
        {
            LogEntityInfo(traceId: "LogMetrics_EndLatency", message: $"MetricId:{metricId}");
        }

        #endregion

        /// <summary>
        /// Extract loggable properties from objects passed in as args (specifically exceptions)
        /// </summary>
        /// <param name="message"></param>
        /// <param name="args"></param>
        /// <param name="exceptionDepth"></param>
        /// <returns></returns>
        // intentionally made args NOT params so the method signature is clear (and any optional params don't get accidentally pulled into the args)
        internal static (string, object[]) MakeLoggable(string message, object[] args, int exceptionDepth = 0)
        {
            try
            {
                var formatter = new LogValuesFormatter(message);
                /* the traceId can contain a format so sometimes we have to start reading args mid-way through. E.g.:
                 * CommonLogger.LogEntityError("{interpolated_trace_id}", "we hit an error: {exception}", traceId, ex);
                 */
                var start = args.Length > formatter.ValueNames.Count ? args.Length - formatter.ValueNames.Count : 0;
                var len = args.Length > formatter.ValueNames.Count ? formatter.ValueNames.Count : args.Length;
                var newArgNames = new object[len];
                var newArgs = new List<object>(args.Length);
                // skip over args that should go into the traceId
                for (var i = 0; i < start; i++)
                {
                    newArgs.Add(args[i]);
                }
                for (var i = 0; i < len; i++)
                {
                    // skip over args already added in above loop
                    var arg = args[start + i];
                    try
                    {
                        // if this value would be scrubbed then attempt to convert it to loggable properties and include those in the log
                        var isSafe = !_scrubbingConfig.CustomDimensionScrubbers.ContainsKey(formatter.ValueNames[i]);
                        Dictionary<string, string> loggableProperties = GetLoggableProperties(arg, isSafe, exceptionDepth);
                        if (loggableProperties.Count != 0)
                        {
                            var argType = arg.GetType().ToString();
                            if (arg is Exception ex)
                            {
                                argType = ex.GetTypes();
                            }

                            // we do it this way so "type" will be the first entry in loggableProperties
                            var firstProps = new Dictionary<string, string>() { { "type", argType } };
                            loggableProperties = LoggableExtensions.AddAll(firstProps, loggableProperties);

                            // if the object is safe then we include the original value's ToString() in addition to any loggable properties.
                            // NOTE: doing this with Exceptions is too dangerous (there is a lot of possibly customer content in exception messages)
                            if (isSafe && !(arg is Exception))
                            {
                                loggableProperties.Add(formatter.ValueNames[i], arg.ToString());
                            }

                            // add the loggable properties as args so they are included in individual kusto fields
                            newArgNames[i] = $"[\n{string.Join(";\n", loggableProperties.Select(kvp => $"{kvp.Key}: {{{kvp.Key}}}"))}\n]";
                            foreach (string val in loggableProperties.Values)
                            {
                                newArgs.Add(val);
                            }
                        }
                        else
                        {
                            // if the value has no loggable properties or getting the loggable properties failed, then just pass the value through as-is
                            newArgNames[i] = $"{{{formatter.ValueNames[i]}}}";
                            newArgs.Add(arg);
                        }
                    }
                    catch (Exception)
                    {
                        _instance.LogWarning("logging error for message, falling back to base-logic: param={param}, message={message}", formatter.ValueNames[i], message);
                        // if the value has no loggable properties or getting the loggable properties failed, then just pass the value through as-is
                        newArgNames[i] = $"{{{formatter.ValueNames[i]}}}";
                        newArgs.Add(arg);
                    }
                }
                return (formatter.Format(newArgNames), newArgs.ToArray());
            }
            catch (Exception)
            {
                _instance.LogWarning("logging error for message, falling back to base-logic: {message}", message);
                return (message, args);
            }
        }

        private static Dictionary<string, string> GetLoggableProperties(object paramVal, bool isSafe = false, int exceptionDepth = 0)
        {
            Dictionary<string, string> loggableProperties;
            if (paramVal is Exception ex)
            {
                loggableProperties = ex.LogSafeExceptionProperties(isMessageSafe: isSafe);
                if (ex.StackTrace != null)
                {
                    loggableProperties["stack_trace"] = ex.StackTrace;
                }
                if (ex.InnerException != null)
                {
                    /*
                     * Default exceptionDepth is 0 so we make sure we log the base exception. If exceptionDepth >= 1 then the base
                     * exception will automatically be included in ToLogSafeExceptionString. 
                     */
                    if (exceptionDepth < 1)
                    {
                        loggableProperties["inner_details"] = ex.GetBaseException().ToLogSafeExceptionString(depth: 0);
                    }
                    else
                    {
                        loggableProperties["inner_details"] = ex.InnerException.ToLogSafeExceptionString(depth: exceptionDepth - 1);
                    }
                }
            }
            else
            {
                loggableProperties = paramVal.LoggableProperties();
            }
            return loggableProperties;
        }

        private static (string, object[]) AddCustomFields(string message, object[] args)
        {
            if (_customFields.Value == null || message == null)
            { 
                return (message, args);
            }

            var newArgs = new List<object>(args ?? new object[0]);
            var messageSuffix = new StringBuilder();

            foreach (var field in _customFields.Value)
            {
                messageSuffix.Append(" [" + field.Key + "={" + field.Key + "}]");
                newArgs.Add(field.Value);
            }

            var newMessage = message + messageSuffix;

            return (newMessage, newArgs.ToArray());
        }

    }
}

