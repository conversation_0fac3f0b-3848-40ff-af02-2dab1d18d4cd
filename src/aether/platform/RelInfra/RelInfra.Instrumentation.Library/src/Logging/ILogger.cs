﻿using System.Collections.Generic;

namespace Microsoft.RelInfra.Instrumentation.Logging
{
    public interface ILogger
    {
        void LogEntityDebug(string traceId, string message);
        void LogEntityDebug(string traceId, string message, params object[] args);
        void LogEntityDebug2(string traceId, int sourceCodeLine, string sourceCodeFile, string sourceCodeMethod, string message, params object[] args);
        void LogEntityDebug(string[] entities, string message);
        void LogEntityDebug(string[] traceIds, string message, params object[] args);
        void LogEntityDebug2(string[] traceIds, int sourceCodeLine, string sourceCodeFile, string sourceCodeMethod, string message, params object[] args);

        void LogEntityError(string traceId, string message);
        void LogEntityError(string traceId, string message, params object[] args);
        void LogEntityError2(string traceId, int sourceCodeLine, string sourceCodeFile, string sourceCodeMethod, string message, params object[] args);
        void LogEntityError(string[] entities, string message);
        void LogEntityError(string[] traceIds, string message, params object[] args);
        void LogEntityError2(string[] traceIds, int sourceCodeLine, string sourceCodeFile, string sourceCodeMethod, string message, params object[] args);

        void LogEntityInfo(string traceId, string message);
        void LogEntityInfo(string traceId, string message, params object[] args);
        void LogEntityInfo2(string traceId, int sourceCodeLine, string sourceCodeFile, string sourceCodeMethod, string message, params object[] args);
        void LogEntityInfo(string[] entities, string message);
        void LogEntityInfo(string[] traceIds, string message, params object[] args);
        void LogEntityInfo2(string[] traceIds, int sourceCodeLine, string sourceCodeFile, string sourceCodeMethod, string message, params object[] args);

        void LogEntityWarning(string traceId, string message);
        void LogEntityWarning(string traceId, string message, params object[] args);
        void LogEntityWarning2(string traceId, int sourceCodeLine, string sourceCodeFile, string sourceCodeMethod, string message, params object[] args);
        void LogEntityWarning(string[] entities, string message);
        void LogEntityWarning(string[] traceIds, string message, params object[] args);
        void LogEntityWarning2(string[] traceIds, int sourceCodeLine, string sourceCodeFile, string sourceCodeMethod, string message, params object[] args);

        void LogDebug(string message);
        void LogDebug(string message, params object[] vars);

        void LogError(string message);
        void LogError(string message, params object[] vars);

        void LogInfo(string message);
        void LogInfo(string message, params object[] vars);


        void LogWarning(string message);
        void LogWarning(string message, params object[] vars);
    }
}
