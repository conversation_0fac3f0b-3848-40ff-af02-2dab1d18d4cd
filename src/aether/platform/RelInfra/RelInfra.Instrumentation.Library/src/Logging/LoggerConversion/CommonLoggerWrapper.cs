﻿using System.Collections.Generic;
using System.Threading.Tasks;

namespace Microsoft.RelInfra.Instrumentation.Logging.LoggerConversion
{
    public class CommonLoggerWrapper : ICommonLogger, ILogger
    {
        public void Dispose()
        {
        }

        public void LogError(string message, params object[] args)
        {
            CommonLogger.LogError(message, args);
        }

        public void LogError(string message)
        {
            CommonLogger.LogError(message);
        }

        public void LogWarning(string message, params object[] args)
        {
            CommonLogger.LogWarning(message, args);
        }

        public void LogWarning(string message)
        {
            CommonLogger.LogWarning(message);
        }

        public void LogInfo(string message, params object[] args)
        {
            CommonLogger.LogInfo(message, args);
        }

        public void LogInfo(string message)
        {
            CommonLogger.LogInfo(message);
        }

        public void LogDebug(string message, params object[] args)
        {
            CommonLogger.LogDebug(message, args);
        }

        public void LogDebug(string message)
        {
            CommonLogger.LogDebug(message);
        }

        public void LogEntityError(string entityId, string message, params object[] args)
        {
            CommonLogger.LogEntityError(entityId, message, args);
        }

        public void LogEntityWarning(string entityId, string message, params object[] args)
        {
            CommonLogger.LogEntityWarning(entityId, message, args);
        }

        public void LogEntityInfo(string entityId, string message, params object[] args)
        {
            CommonLogger.LogEntityInfo(entityId, message, args);
        }

        public void LogEntityDebug(string entityId, string message, params object[] args)
        {
            CommonLogger.LogEntityDebug(entityId, message, args);
        }

        public void LogEntityError(string entityId, string message)
        {
            CommonLogger.LogEntityError(entityId, message);
        }

        public void LogEntityWarning(string entityId, string message)
        {
            CommonLogger.LogEntityWarning(entityId, message);
        }

        public void LogEntityInfo(string entityId, string message)
        {
            CommonLogger.LogEntityInfo(entityId, message);
        }

        public void LogEntityDebug(string entityId, string message)
        {
            CommonLogger.LogEntityDebug(entityId, message);
        }

        public void LogMetric(string metric, long value, Dictionary<string, string> metricData = null)
        {
            CommonLogger.LogMetric(metric, value, metricData);
        }

        public void LogEntityDebug2(string traceId, int sourceCodeLine, string sourceCodeFile, string sourceCodeMethod, string message, params object[] args)
        {
            CommonLogger.LogEntityDebug2(traceId, message: message, memberName: sourceCodeMethod, sourceFilePath: sourceCodeFile, sourceLineNumber: sourceCodeLine, args: args);
        }

        public void LogEntityDebug(string[] entities, string message)
        {
            CommonLogger.LogEntityDebug(entities, message);
        }

        public void LogEntityDebug(string[] traceIds, string message, params object[] args)
        {
            CommonLogger.LogEntityDebug(traceIds, message, args);
        }

        public void LogEntityDebug2(string[] traceIds, int sourceCodeLine, string sourceCodeFile, string sourceCodeMethod, string message, params object[] args)
        {
            CommonLogger.LogEntityDebug2(traceIds, message: message, memberName: sourceCodeMethod, sourceFilePath: sourceCodeFile, sourceLineNumber: sourceCodeLine, args: args);
        }

        public void LogEntityError2(string traceId, int sourceCodeLine, string sourceCodeFile, string sourceCodeMethod, string message, params object[] args)
        {
            CommonLogger.LogEntityError2(traceId, message: message, memberName: sourceCodeMethod, sourceFilePath: sourceCodeFile, sourceLineNumber: sourceCodeLine, args: args);
        }

        public void LogEntityError2(string[] traceIds, int sourceCodeLine, string sourceCodeFile, string sourceCodeMethod, string message, params object[] args)
        {
            CommonLogger.LogEntityError2(traceIds, message: message, memberName: sourceCodeMethod, sourceFilePath: sourceCodeFile, sourceLineNumber: sourceCodeLine, args: args);
        }

        public void LogEntityError(string[] entities, string message)
        {
            CommonLogger.LogEntityError(entities, message);
        }

        public void LogEntityError(string[] traceIds, string message, params object[] args)
        {
            CommonLogger.LogEntityError(traceIds,  message,  args);
        }

        public void LogEntityInfo2(string traceId, int sourceCodeLine, string sourceCodeFile, string sourceCodeMethod, string message, params object[] args)
        {
            CommonLogger.LogEntityInfo2(traceId, message: message, memberName: sourceCodeMethod, sourceFilePath: sourceCodeFile, sourceLineNumber: sourceCodeLine, args: args);
        }

        public void LogEntityInfo(string[] entities, string message)
        {
            CommonLogger.LogEntityInfo(entities, message);
        }

        public void LogEntityInfo(string[] traceIds, string message, params object[] args)
        {
            CommonLogger.LogEntityInfo(traceIds, message, args);
        }

        public void LogEntityInfo2(string[] traceIds, int sourceCodeLine, string sourceCodeFile, string sourceCodeMethod, string message, params object[] args)
        {
            CommonLogger.LogEntityInfo2(traceIds, message: message, memberName: sourceCodeMethod, sourceFilePath: sourceCodeFile, sourceLineNumber: sourceCodeLine, args: args);
        }

        public void LogEntityWarning2(string traceId, int sourceCodeLine, string sourceCodeFile, string sourceCodeMethod, string message, params object[] args)
        {
            CommonLogger.LogEntityWarning2(traceId, message: message, memberName: sourceCodeMethod, sourceFilePath: sourceCodeFile, sourceLineNumber: sourceCodeLine, args: args);
        }

        public void LogEntityWarning(string[] entities, string message)
        {
            CommonLogger.LogEntityWarning(entities, message);
        }

        public void LogEntityWarning(string[] traceIds, string message, params object[] args)
        {
            CommonLogger.LogEntityWarning(traceIds, message, args);
        }

        public void LogEntityWarning2(string[] traceIds, int sourceCodeLine, string sourceCodeFile, string sourceCodeMethod, string message, params object[] args)
        {
            CommonLogger.LogEntityWarning2(traceIds, message: message, memberName: sourceCodeMethod, sourceFilePath: sourceCodeFile, sourceLineNumber: sourceCodeLine, args: args);
        }

        public void Flush()
        {
        }

        public Task FlushAsync()
        {
            return Task.FromResult(true);
        }

    }
}
