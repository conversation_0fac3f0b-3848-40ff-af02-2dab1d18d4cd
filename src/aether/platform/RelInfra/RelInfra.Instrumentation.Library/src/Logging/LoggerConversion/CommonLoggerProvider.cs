﻿using Microsoft.Extensions.Logging;
using MsLogging = Microsoft.Extensions.Logging;


namespace Microsoft.RelInfra.Instrumentation.Logging.LoggerConversion
{
    /// <summary>
    ///    This logger provider is used to route logs from asp.net into CommonLogger
    /// </summary>
    public class CommonLoggerProvider : ILoggerProvider
    {
        private readonly RelInfraLoggerToMsLogger _logger;

        public CommonLoggerProvider(MsLogging.LogLevel minLogLevel = MsLogging.LogLevel.Warning)
        {
            _logger = new RelInfraLoggerToMsLogger(new CommonLoggerWrapper(), minLogLevel);
        }

        public MsLogging.ILogger CreateLogger(string categoryName)
        {
            return _logger;
        }

        public void Dispose() { }
    }
}
