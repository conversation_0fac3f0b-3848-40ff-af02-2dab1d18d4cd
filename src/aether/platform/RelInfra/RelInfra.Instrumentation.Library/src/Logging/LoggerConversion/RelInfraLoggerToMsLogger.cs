﻿using Microsoft.Extensions.Logging;
using System;
using MsLogging = Microsoft.Extensions.Logging;

namespace Microsoft.RelInfra.Instrumentation.Logging.LoggerConversion
{
    // Common Logger wrapper for ASP.Net Core
    public class RelInfraLoggerToMsLogger : MsLogging.ILogger
    {
        private readonly ILogger _logger;
        private readonly MsLogging.LogLevel _minLogLevel;

        public RelInfraLoggerToMsLogger(ILogger logger, MsLogging.LogLevel minLogLevel)
        {
            _minLogLevel = minLogLevel;
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public IDisposable BeginScope<TState>(TState state)
        {
            return null;
        }

        public bool IsEnabled(MsLogging.LogLevel logLevel)
        {
            return logLevel >= _minLogLevel;
        }

        public void Log<TState>(MsLogging.LogLevel logLevel, EventId eventId, TState state, Exception exception, Func<TState, Exception, string> formatter)
        {
            if (!IsEnabled(logLevel))
            {
                return;
            }

            string message = formatter(state, exception);

            switch (logLevel)
            {
                case MsLogging.LogLevel.Critical:
                    // no logging critical in commonlogger, use logError instead
                    _logger.LogError(message);
                    break;

                case MsLogging.LogLevel.Error:
                    _logger.LogError(message);
                    break;

                case MsLogging.LogLevel.Warning:
                    _logger.LogWarning(message);
                    break;

                case MsLogging.LogLevel.Information:
                    _logger.LogInfo(message);
                    break;

                case MsLogging.LogLevel.Debug:
                    _logger.LogDebug(message);
                    break;

                default:
                    // ignoring LogLevel of Trace and None
                    break;
            }
        }
    }
}
