﻿using JetBrains.Annotations;
using Microsoft.Extensions.Logging;
using System;
using MsLoggingILogger = Microsoft.Extensions.Logging.ILogger;

namespace Microsoft.RelInfra.Instrumentation.Logging.LoggerConversion
{
    internal class MsLoggerToRelInfraLogger : ILogger
    {
        public MsLoggerToRelInfraLogger(MsLoggingILogger logger, string serviceName)
        {
            _logger = logger;
            _serviceName = serviceName;
        }

        [StringFormatMethod("message")]
        public void LogError(string message, params object[] vars)
        {
            _logger.LogError(message, vars);
        }

        [StringFormatMethod("message")]
        public void LogWarning(string message, params object[] vars)
        {
            _logger.LogWarning(message, vars);
        }

        [StringFormatMethod("message")]
        public void LogInfo(string message, params object[] vars)
        {
            _logger.LogInformation(message, vars);
        }

        [StringFormatMethod("message")]
        public void LogDebug(string message, params object[] vars)
        {
            _logger.LogDebug(message, vars);
        }

        [StringFormatMethod("message")]
        public void LogEntityError2(string traceId, int sourceCodeLine, string sourceCodeFile, string sourceCodeMethod, string message, params object[] args)
        {
            CentralLoggingMessageEntity centralLoggingMessageEntity =
                CreateMessageEntity(traceId: traceId,
                                    level: LogLevel.Error,
                                    sourceCodeLine: sourceCodeLine,
                                    sourceCodeFile: sourceCodeFile,
                                    sourceCodeMethod: sourceCodeMethod,
                                    message: message);

            LogError(centralLoggingMessageEntity.ToString(), args);
        }

        [StringFormatMethod("message")]
        public void LogEntityWarning2(string traceId, int sourceCodeLine, string sourceCodeFile, string sourceCodeMethod, string message, params object[] args)
        {
            CentralLoggingMessageEntity centralLoggingMessageEntity =
                CreateMessageEntity(traceId: traceId,
                                    level: LogLevel.Warning,
                                    sourceCodeLine: sourceCodeLine,
                                    sourceCodeFile: sourceCodeFile,
                                    sourceCodeMethod: sourceCodeMethod,
                                    message: message);

            LogWarning(centralLoggingMessageEntity.ToString(), args);
        }

        [StringFormatMethod("message")]
        public void LogEntityInfo2(string traceId, int sourceCodeLine, string sourceCodeFile, string sourceCodeMethod, string message, params object[] args)
        {
            CentralLoggingMessageEntity centralLoggingMessageEntity =
                CreateMessageEntity(traceId: traceId,
                                    level: LogLevel.Info,
                                    sourceCodeLine: sourceCodeLine,
                                    sourceCodeFile: sourceCodeFile,
                                    sourceCodeMethod: sourceCodeMethod,
                                    message: message);

            LogInfo(centralLoggingMessageEntity.ToString(), args);
        }

        [StringFormatMethod("message")]
        public void LogEntityDebug2(string traceId, int sourceCodeLine, string sourceCodeFile, string sourceCodeMethod, string message, params object[] args)
        {
            CentralLoggingMessageEntity centralLoggingMessageEntity =
                CreateMessageEntity(traceId: traceId,
                                    level: LogLevel.Debug,
                                    sourceCodeLine: sourceCodeLine,
                                    sourceCodeFile: sourceCodeFile,
                                    sourceCodeMethod: sourceCodeMethod,
                                    message: message);

            LogDebug(centralLoggingMessageEntity.ToString(), args);
        }

        [StringFormatMethod("message")]
        public void LogEntityError(string traceId, string message, params object[] args)
        {
            LogEntityError2(traceId, sourceCodeLine: 0, sourceCodeFile: null, sourceCodeMethod: null, message: message, args: args);
        }

        [StringFormatMethod("message")]
        public void LogEntityWarning(string traceId, string message, params object[] args)
        {
            LogEntityWarning2(traceId, sourceCodeLine: 0, sourceCodeFile: null, sourceCodeMethod: null, message: message, args: args);
        }

        [StringFormatMethod("message")]
        public void LogEntityInfo(string traceId, string message, params object[] args)
        {
            LogEntityInfo2(traceId, sourceCodeLine: 0, sourceCodeFile: null, sourceCodeMethod: null, message: message, args: args);
        }

        [StringFormatMethod("message")]
        public void LogEntityDebug(string traceId, string message, params object[] args)
        {
            LogEntityDebug2(traceId, sourceCodeLine: 0, sourceCodeFile: null, sourceCodeMethod: null, message: message, args: args);
        }

        [StringFormatMethod("message")]
        public void LogEntityError(string[] traceIds, string message, params object[] args)
        {
            LogEntityError2(traceIds, sourceCodeLine: 0, sourceCodeFile: null, sourceCodeMethod: null, message: message, args: args);
        }

        [StringFormatMethod("message")]
        public void LogEntityWarning(string[] traceIds, string message, params object[] args)
        {
            LogEntityWarning2(traceIds, sourceCodeLine: 0, sourceCodeFile: null, sourceCodeMethod: null, message: message, args: args);
        }

        [StringFormatMethod("message")]
        public void LogEntityInfo(string[] traceIds, string message, params object[] args)
        {
            LogEntityInfo2(traceIds, sourceCodeLine: 0, sourceCodeFile: null, sourceCodeMethod: null, message: message, args: args);
        }

        [StringFormatMethod("message")]
        public void LogEntityDebug(string[] traceIds, string message, params object[] args)
        {
            LogEntityDebug2(traceIds, sourceCodeLine: 0, sourceCodeFile: null, sourceCodeMethod: null, message: message, args: args);
        }

        [StringFormatMethod("message")]
        public void LogEntityError2(string[] traceIds, int sourceCodeLine, string sourceCodeFile, string sourceCodeMethod, string message, params object[] args)
        {
            foreach (string traceId in traceIds)
            {
                LogEntityError2(traceId, sourceCodeLine, sourceCodeFile, sourceCodeMethod, message, args);
            }
        }

        [StringFormatMethod("message")]
        public void LogEntityWarning2(string[] traceIds, int sourceCodeLine, string sourceCodeFile, string sourceCodeMethod, string message, params object[] args)
        {
            foreach (string traceId in traceIds)
            {
                LogEntityWarning2(traceId, sourceCodeLine, sourceCodeFile, sourceCodeMethod, message, args);
            }
        }
        [StringFormatMethod("message")]
        public void LogEntityInfo2(string[] traceIds, int sourceCodeLine, string sourceCodeFile, string sourceCodeMethod, string message, params object[] args)
        {
            foreach (string traceId in traceIds)
            {
                LogEntityInfo2(traceId, sourceCodeLine, sourceCodeFile, sourceCodeMethod, message, args);
            }
        }
        [StringFormatMethod("message")]
        public void LogEntityDebug2(string[] traceIds, int sourceCodeLine, string sourceCodeFile, string sourceCodeMethod, string message, params object[] args)
        {
            foreach (string traceId in traceIds)
            {
                LogEntityDebug2(traceId, sourceCodeLine, sourceCodeFile, sourceCodeMethod, message, args);
            }
        }
        public void LogError(string message)
        {
            LogError(message, vars: new object[0]);
        }

        public void LogWarning(string message)
        {
            LogWarning(message, vars: new object[0]);
        }

        public void LogInfo(string message)
        {
            LogInfo(message, vars: new object[0]);
        }

        public void LogDebug(string message)
        {
            LogDebug(message, vars: new object[0]);
        }

        public void LogEntityError(string traceId, string message)
        {
            LogEntityError2(traceId, sourceCodeLine: 0, sourceCodeFile: null, sourceCodeMethod: null, message: message);
        }

        public void LogEntityWarning(string traceId, string message)
        {
            LogEntityWarning2(traceId, sourceCodeLine: 0, sourceCodeFile: null, sourceCodeMethod: null, message: message);
        }

        public void LogEntityInfo(string traceId, string message)
        {
            LogEntityInfo2(traceId, sourceCodeLine: 0, sourceCodeFile: null, sourceCodeMethod: null, message: message);
        }

        public void LogEntityDebug(string traceId, string message)
        {
            LogEntityDebug2(traceId, sourceCodeLine: 0, sourceCodeFile: null, sourceCodeMethod: null, message: message);
        }

        public void LogEntityError(string[] entities, string message)
        {
            LogEntityError(entities, message, args: new object[0]);
        }

        public void LogEntityWarning(string[] entities, string message)
        {
            LogEntityWarning(entities, message, args: new object[0]);
        }

        public void LogEntityInfo(string[] entities, string message)
        {
            LogEntityInfo(entities, message, args: new object[0]);
        }

        public void LogEntityDebug(string[] entities, string message)
        {
            LogEntityDebug(entities, message, args: new object[0]);
        }

        private CentralLoggingMessageEntity CreateMessageEntity(string traceId, LogLevel level, int sourceCodeLine, string sourceCodeFile, string sourceCodeMethod, string message)
        {
            return new CentralLoggingMessageEntity
            {
                TraceId = traceId,
                LogLevel = level.ToString(),
                Message = message,
                MachineName = null,
                ServiceName = _serviceName,
                TimeStamp = DateTime.UtcNow,
                SourceCodeLine = sourceCodeLine,
                SourceCodeFile = sourceCodeFile,
                SourceCodeMethod = sourceCodeMethod
            };
        }

        private readonly MsLoggingILogger _logger;
        private readonly string _serviceName;
    }
}
