﻿using System.Collections.Generic;

namespace Microsoft.RelInfra.Instrumentation
{
    public class NumberCounter : BaseCounter
    {
        public NumberCounter(ICounter counter, string instanceName, IDictionary<string, string> customDimensions = null)
            : base(counter, instanceName, customDimensions)
        {
        }

        public new bool Set(long val)
        {
            return base.Set(val);
        }
   }
}
