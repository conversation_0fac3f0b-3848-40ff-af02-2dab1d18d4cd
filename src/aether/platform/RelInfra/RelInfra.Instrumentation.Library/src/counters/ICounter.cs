﻿using System;
using System.Collections.Generic;

namespace Microsoft.RelInfra.Instrumentation
{
    public interface ICounter : IDisposable
    {
        string Name { get; }

        CounterType CounterType { get; }

        bool Decrement(string instanceName = null, IDictionary<string, string> customDimensions = null);

        bool DecrementBy(long value, string instanceName = null, IDictionary<string, string> customDimensions = null);

        bool Increment(string instanceName = null, IDictionary<string, string> customDimensions = null);

        bool IncrementBy(long value, string instanceName = null, IDictionary<string, string> customDimensions = null);

        bool Set(long value, string instanceName = null, IDictionary<string, string> customDimensions = null);

        long Get(string instanceName = null);
    }
}