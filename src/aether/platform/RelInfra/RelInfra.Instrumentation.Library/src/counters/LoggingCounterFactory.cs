﻿using System.Collections.Generic;

namespace Microsoft.RelInfra.Instrumentation
{
    public class LoggingCounterFactory : ICounterFactory
    {
        public ICounter CreateNumberCounter(string sectionName, string counterName)
        {
            return new LoggingCounter(counterName, CounterType.Number);
        }

        public ICounter CreatePercentilesCounter(string sectionName, string counterName)
        {
            return new LoggingCounter(counterName, CounterType.Number_Percentiles);
        }

        public ICounter CreateRateCounter(string sectionName, string counterName)
        {
            return new LoggingCounter(counterName, CounterType.Rate);
        }
    }
}
