﻿using System;

namespace Microsoft.RelInfra.Instrumentation
{
    /// <summary>
    /// This class wraps the CounteManager class with an interface that relies on enumerators
    /// Enum1 and Enum2 are concatenated for the counter name
    /// Enum3 is the instance name
    /// </summary>
    /// <typeparam name="TEnum1"></typeparam>
    /// <typeparam name="TEnum2"></typeparam>
    /// <typeparam name="TEnum3"></typeparam>
    public class EnumCounterManager<TEnum1, TEnum2, TEnum3> : CounterManager 
    {
        public EnumCounterManager(string serviceName, ICounterFactory factory)
            : base(serviceName, factory)
        { 
            // check that all types given are enums
            if (!typeof(TEnum1).IsEnum || !typeof(TEnum2).IsEnum || !typeof(TEnum3).IsEnum)
            {
                throw new InvalidCastException("EnumCounterManager requires Enum Types only");
            }
        }

        public LatencyCounter GetLatencyCounter(TEnum1 dim1, TEnum2 dim2, TEnum3 dim3)
        {
            return base.GetLatencyCounter(GetCounterName(dim1, dim2), GetInstanceName(dim3));
        }

        public RateCounter GetRateCounter(TEnum1 dim1, TEnum2 dim2, TEnum3 dim3)
        {
            return base.GetRateCounter(GetCounterName(dim1, dim2), GetInstanceName(dim3));
        }

        public NumberCounter GetNumberCounter(TEnum1 dim1, TEnum2 dim2, TEnum3 dim3)
        {
            return base.GetNumberCounter(GetCounterName(dim1, dim2), GetInstanceName(dim3));
        }

        private string GetCounterName(TEnum1 dim1, TEnum2 dim2)
        {
            return string.Format("{0}{1}", dim1, dim2);
        }

        private string GetInstanceName(TEnum3 dim3)
        {
            return dim3.ToString();
        }
    }

    /// <summary>
    /// This is a 2-Enum version of the above class
    /// Enum1 is the counter name
    /// Enum2 is the instance name
    /// </summary>
    /// <typeparam name="TEnum1"></typeparam>
    /// <typeparam name="TEnum2"></typeparam>
    public class EnumCounterManager<TEnum1, TEnum2> : CounterManager
    {
        public EnumCounterManager(string serviceName, ICounterFactory factory)
            : base(serviceName, factory)
        {
            // check that all types given are enums
            if (!typeof(TEnum1).IsEnum || !typeof(TEnum2).IsEnum)
            {
                throw new InvalidCastException("EnumCounterManager requires Enum Types only");
            }
        }

        public LatencyCounter GetLatencyCounter(TEnum1 dim1, TEnum2 dim2)
        {
            return base.GetLatencyCounter(GetCounterName(dim1), GetInstanceName(dim2));
        }

        public RateCounter GetRateCounter(TEnum1 dim1, TEnum2 dim2)
        {
            return base.GetRateCounter(GetCounterName(dim1), GetInstanceName(dim2));
        }

        public NumberCounter GetNumberCounter(TEnum1 dim1, TEnum2 dim2)
        {
            return base.GetNumberCounter(GetCounterName(dim1), GetInstanceName(dim2));
        }

        private string GetCounterName(TEnum1 dim1)
        {
            return dim1.ToString();
        }

        private string GetInstanceName(TEnum2 dim2)
        {
            return dim2.ToString();
        }
    }
}
