﻿using System.Threading.Tasks;

namespace Microsoft.RelInfra.Instrumentation.Test.Mocks
{
    public class CounterCreatorMock : ICounterManagerCreator
    {
        public Task<CounterManager> CreateAsync(string serviceName)
        {
            return Task.FromResult(new CounterManager("MockManager", new MockCounterFactory()));
        }

        public Task<CounterManager> CreateCommonCounterAsync(string serviceName)
        {
            return Task.FromResult(new CounterManager("MockManager", new MockCounterFactory()));
        }
    }
}