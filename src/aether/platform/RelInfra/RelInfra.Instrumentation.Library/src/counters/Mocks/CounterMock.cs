﻿using System.Collections.Generic;

namespace Microsoft.RelInfra.Instrumentation.Test.Mocks
{
    public class MockCounter : ICounter
    {
        public MockCounter(string sectionName, string counterName, CounterType aetherCounterType)
        {
            Section = sectionName;
            Name = counterName;
            CounterType = aetherCounterType;
            CurrentValue = 0;
        }

        public long CurrentValue { get; private set; }

        public void Dispose()
        {
        }

        public string Section { get; private set; }
        public string Name { get; private set; }
        public CounterType CounterType { get; private set; }

        public bool Decrement(string instanceName = null, IDictionary<string, string> customDimensions = null)
        {
            return DecrementBy(1, instanceName);
        }

        public bool DecrementBy(long value, string instanceName = null, IDictionary<string, string> customDimensions = null)
        {
            CurrentValue -= value;
            return true;
        }

        public bool Increment(string instanceName = null, IDictionary<string, string> customDimensions = null)
        {
            return IncrementBy(1, instanceName);
        }

        public bool IncrementBy(long value, string instanceName = null, IDictionary<string, string> customDimensions = null)
        {
            CurrentValue += value;
            return true;
        }

        public bool Set(long value, string instanceName = null, IDictionary<string, string> customDimensions = null)
        {
            CurrentValue = value;
            return true;
        }

        public long Get(string instanceName = null)
        {
            return CurrentValue;
        }
    }
}