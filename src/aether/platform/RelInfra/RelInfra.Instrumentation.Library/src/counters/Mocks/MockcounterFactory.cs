﻿using System.Collections.Generic;

namespace Microsoft.RelInfra.Instrumentation.Test.Mocks
{
    public class MockCounterFactory : ICounterFactory
    {
        public MockCounter LastCreatedCounter { get; private set; }

        public ICounter CreateNumberCounter(string sectionName, string counterName)
        {
            LastCreatedCounter = new MockCounter(sectionName, counterName, CounterType.Number);
            return LastCreatedCounter;
        }

        public ICounter CreatePercentilesCounter(string sectionName, string counterName)
        {
            LastCreatedCounter = new MockCounter(sectionName, counterName, CounterType.Number_Percentiles);
            return LastCreatedCounter;
        }

        public ICounter CreateRateCounter(string sectionName, string counterName)
        {
            LastCreatedCounter = new MockCounter(sectionName, counterName, CounterType.Rate);
            return LastCreatedCounter;
        }
    }
}
