﻿using System.Collections.Generic;

namespace Microsoft.RelInfra.Instrumentation
{
    public class RateCounter : BaseCounter
    {
        public RateCounter(ICounter counter, string instanceName, IDictionary<string, string> customDimensions = null)
            : base(counter, instanceName, customDimensions)
        {
        }

        public new bool Increment(long val = 1)
        {
            return base.Increment(val);
        }
    }
}
