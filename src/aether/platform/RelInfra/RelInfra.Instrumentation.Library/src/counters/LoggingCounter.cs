﻿using System.Collections.Generic;
using Microsoft.RelInfra.Instrumentation.Logging;

namespace Microsoft.RelInfra.Instrumentation
{
    public class LoggingCounter : ICounter
    {
        public LoggingCounter(string name, CounterType type)
        {
            Name = name;
            CounterType = type;
        }

        public string Name { get; private set; }

        public CounterType CounterType { get; private set; }

        public void Dispose()
        {
        }

        public bool Decrement(string instanceName = null, IDictionary<string, string> customDimensions = null)
        {
            CommonLogger.LogMetric(GetFullName(instanceName), -1);
            return true;
        }

        public bool DecrementBy(long value, string instanceName = null, IDictionary<string, string> customDimensions = null)
        {
            CommonLogger.LogMetric(GetFullName(instanceName), -1 * value);
            return true;
        }

        public bool Increment(string instanceName = null, IDictionary<string, string> customDimensions = null)
        {
            CommonLogger.LogMetric(GetFullName(instanceName), 1);
            return true;
        }

        public bool IncrementBy(long value, string instanceName = null, IDictionary<string, string> customDimensions = null)
        {
            CommonLogger.LogMetric(GetFullName(instanceName), value);
            return true;
        }

        public bool Set(long value, string instanceName = null, IDictionary<string, string> customDimensions = null)
        {
            CommonLogger.LogMetric(GetFullName(instanceName), value);
            return true;
        }

        public long Get(string instanceName = null)
        {
            return 0;
        }

        private string GetFullName(string instanceName)
        {
            return instanceName == null
                ? Name
                : string.Format("{0}.{1}", Name, instanceName);
        }
    }
}
