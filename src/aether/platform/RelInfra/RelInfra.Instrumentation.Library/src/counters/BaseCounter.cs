﻿using System.Collections.Generic;

namespace Microsoft.RelInfra.Instrumentation
{
    public class BaseCounter
    {
        private ICounter _counter;
        protected readonly string InstanceName;
        protected readonly IDictionary<string, string> CustomDimensions;

        protected BaseCounter(ICounter counter, string instanceName, IDictionary<string, string> customDimensions = null)
        {
            _counter = counter;
            InstanceName = instanceName;
            CustomDimensions = customDimensions;
        }

        public string Name => _counter.Name;
        
        public long Get()
        {
            return _counter.Get(InstanceName);
        }

        protected bool Set(long val)
        {
            return _counter.Set(val, InstanceName, CustomDimensions);
        }

        protected bool Increment(long val = 1)
        {
            return _counter.IncrementBy(val, InstanceName, CustomDimensions);
        }
    }
}
