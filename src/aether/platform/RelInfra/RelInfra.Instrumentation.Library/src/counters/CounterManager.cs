﻿using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Microsoft.RelInfra.Instrumentation
{
    /// <summary>
    /// This is the main class used for managing counters
    /// It ensures that the same counter / instance combination only results in the creation of one instance.
    /// </summary>
    public class CounterManager
    {
        /// <summary>
        /// This is used to lookup 
        /// </summary>
        private readonly ConcurrentDictionary<string, ICounter> _counterCache = new ConcurrentDictionary<string, ICounter>();

        private readonly ConcurrentDictionary<string, BaseCounter> _instanceCache = new ConcurrentDictionary<string, BaseCounter>();

        public readonly string ServiceName;

        private readonly ICounterFactory _factory;

        public CounterManager(string serviceName, ICounterFactory factory)
        {
            ServiceName = serviceName;
            _factory = factory;
        }

        public virtual LatencyCounter GetLatencyCounter(string counterName, string instanceName = null, IDictionary<string, string> customDimensions = null)
        {
            return (LatencyCounter)GetCounterInstance(counterName, instanceName, InternalCounterType.Latency, customDimensions);
        }

        public CentralLatencyCounter GetCentralLatencyCounter(string counterName, string instanceName = null, IDictionary<string, string> customDimensions = null)
        {
            return (CentralLatencyCounter)GetCounterInstance(counterName, instanceName, InternalCounterType.CentralLatency, customDimensions);
        }

        public virtual NumberCounter GetNumberCounter(string counterName, string instanceName = null, IDictionary<string, string> customDimensions = null)
        {
            return (NumberCounter)GetCounterInstance(counterName, instanceName, InternalCounterType.Number, customDimensions);
        }

        public virtual RateCounter GetRateCounter(string counterName, string instanceName = null, IDictionary<string, string> customDimensions = null)
        {
            return (RateCounter)GetCounterInstance(counterName, instanceName, InternalCounterType.Rate, customDimensions);
        }

        private string SerializeCustomDimensions(IDictionary<string, string> customDimensions)
        {
            var sortedDimension = new SortedDictionary<string, string>(customDimensions);
            var dimensionStr = new StringBuilder();
            foreach (var kvp in sortedDimension)
            {
                dimensionStr.Append($"{kvp.Key}: {kvp.Value}");
                dimensionStr.Append(",");
            }

            dimensionStr.Length --;
            return dimensionStr.ToString();
        }

        private BaseCounter GetCounterInstance(string counterName, string instanceName, InternalCounterType type, IDictionary<string, string> customDimensions)
        {
            string cacheKey = customDimensions != null && customDimensions.Any() ? String.Format("{0}-{1}-{2}", counterName, instanceName, SerializeCustomDimensions(customDimensions)) : String.Format("{0}-{1}", counterName, instanceName);

            BaseCounter counter;
            if (_instanceCache.TryGetValue(cacheKey, out counter))
            {
                return counter;
            }

            lock (_instanceCache)
            {
                // ConcurrentDictionary ensures that internal collection of key/value pairs would be updated only once,
                // but addValueFactory (CreateInstance) might be called multiple times
                // http://msdn.microsoft.com/en-us/library/ee378677(v=vs.110).aspx
                return _instanceCache.GetOrAdd(cacheKey, n => CreateInstance(counterName, instanceName, type, customDimensions));
            }
        }

        private BaseCounter CreateInstance(string counterName, string instanceName, InternalCounterType type, IDictionary<string, string> customDimensions)
        {
            ICounter counter = _counterCache.GetOrAdd(counterName,
                n => CreateCounter(counterName, type));

            if (counter.CounterType != CounterTypeToFlag(type))
            {
                throw new CounterException("Mistmatch in requested type and already created counter");
            }
            
            switch (type)
            {
                case InternalCounterType.Number:
                    return new NumberCounter(counter, instanceName, customDimensions);
                case InternalCounterType.Rate:
                    return new RateCounter(counter, instanceName, customDimensions);
                case InternalCounterType.Latency:
                    return new LatencyCounter(counter, instanceName, customDimensions);
                case InternalCounterType.CentralLatency:
                    return new CentralLatencyCounter(counter, counterName, instanceName, customDimensions);
                default:
                    throw new Exception("Invalid type encountered in CounterManager:CreateInstance");
            }
        }

        private ICounter CreateCounter(string counterName, InternalCounterType type)
        {
            switch (type)
            {
                case InternalCounterType.Number:
                    return _factory.CreateNumberCounter(ServiceName, counterName);
                case InternalCounterType.Latency:
                case InternalCounterType.CentralLatency:
                    return _factory.CreatePercentilesCounter(ServiceName, counterName);
                case InternalCounterType.Rate:
                    return _factory.CreateRateCounter(ServiceName, counterName);
                default:
                    throw new Exception("Invalid counter type encountereed in CounterManager:CreateCounter");
            }
        }

        private CounterType CounterTypeToFlag(InternalCounterType type)
        {
            switch (type)
            {
                case InternalCounterType.Number:
                    return CounterType.Number;
                case InternalCounterType.Latency:
                    return CounterType.Number_Percentiles;
                case InternalCounterType.CentralLatency:
                    return CounterType.Number_Percentiles;
                case InternalCounterType.Rate:
                    return CounterType.Rate;
                default:
                    throw new Exception("Invalid counter type encountereed in CounterManager:CreateCounter");
            }
        }

        /// <summary>
        /// This enum is used internally to create the right underlying counter type
        /// </summary>
        private enum InternalCounterType
        {
            Rate,
            Number,
            Latency,
            CentralLatency
        }

    }
}
