using System.Collections.Generic;
using Microsoft.RelInfra.Instrumentation.Logging;

namespace Microsoft.RelInfra.Instrumentation
{
    public class CentralLatencyCounter : LatencyCounter
    {
        private readonly string _metricName;
        public CentralLatencyCounter(ICounter counter, string counterName, string instanceName, IDictionary<string, string> customDimensions = null)
            : base(counter, instanceName, customDimensions)
        {
            if (InstanceName != null)
            {
                _metricName = string.Format("{0}.{1}", counterName, InstanceName);
            }
            else
            {
                _metricName = counterName;
            }
        }

        public override bool Set(long time)
        {
            CommonLogger.LogMetric(_metricName, time);
            return base.Set(time);
        }
    }
}