﻿using Microsoft.RelInfra.Instrumentation.Logging;
using System;
using System.Diagnostics;

namespace Microsoft.RelInfra.Instrumentation
{
    /// <summary>
    /// This class allows you to time a block of code simply by putting it in a using statement
    /// </summary>
    public class DisposableTimer : IDisposable
    {
        public TimeSpan Elapsed
        {
            get { return _stopWatch.Elapsed; }
        }

        private Stopwatch _stopWatch;
        private LatencyCounter _counter;

        public DisposableTimer(LatencyCounter counter)
        {
            _counter = counter;
            _stopWatch = Stopwatch.StartNew();
        }

        private bool _disposed = false;

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            // Check to see if Dispose has already been called. 
            if (!_disposed && disposing)
            {
                if (_counter != null)
                {
                    var duration = _stopWatch.ElapsedMilliseconds;
                    _counter.Set(duration);
                    CommonLogger.LogInfo($"{_counter.Name}: {duration} ms.");
                }
                _disposed = true;
            }
        }

        ~DisposableTimer()
        {
            Dispose(false);
        }

    }
}
