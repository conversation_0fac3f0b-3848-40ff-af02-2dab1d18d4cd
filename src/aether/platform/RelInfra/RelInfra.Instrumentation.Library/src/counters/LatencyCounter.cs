﻿using System.Collections.Generic;

namespace Microsoft.RelInfra.Instrumentation
{
    /// <summary>
    /// Timer class used to measure
    /// </summary>
    public class LatencyCounter : BaseCounter
    {
        public LatencyCounter(ICounter counter, string instanceName, IDictionary<string, string> customDimensions = null)
            : base(counter, instanceName, customDimensions)
        {
        }

        public virtual new bool Set(long time)
        {
            return base.Set(time);
        }

        public DisposableTimer StartCounter()
        {
            return new DisposableTimer(this);
        }
    }
}
