﻿using System;
using System.Collections.Concurrent;
using System.Diagnostics;

namespace Microsoft.RelInfra.Instrumentation
{
    public static class Counters
    {
        private static readonly ConcurrentDictionary<string, ICounter> CountersDictionary =
            new ConcurrentDictionary<string, ICounter>();

        public static ICounter GetCounter(string counterName)
        {
            if (String.IsNullOrWhiteSpace(counterName))
            {
                Trace.TraceError("Counter name can't be empty or null");
                throw new ArgumentException("Counter name can't be empty or null", "counterName");
            }

            ICounter counter;

            if (CountersDictionary.TryGetValue(counterName, out counter))
            {
                return counter;
            }

            Trace.TraceWarning("Counters: counter [{0}] not found", counterName);

            return null;
        }

        public static void AddCounter(ICounter counter)
        {
            if (counter == null)
            {
                Trace.TraceError("Counter can't be null");
                throw new ArgumentNullException("counter", "Counter can't be null");
            }

            if (String.IsNullOrWhiteSpace(counter.Name))
            {
                Trace.TraceError("Counter name can't be empty");
                throw new ArgumentException("Counter name can't be empty");
            }

            if (!CountersDictionary.TryAdd(counter.Name, counter))
            {
                Trace.TraceError("Error while adding a counter [{0}]", counter.Name);
                throw new CounterException(string.Format("Can't add a counter [{0}].", counter.Name));
            }
        }

        public static bool Decrement(string counterName, string instanceName = null)
        {
            ICounter counter = GetCounter(counterName);

            return counter != null && counter.Decrement(instanceName);
        }

        public static bool DecrementBy(string counterName, long value, string instanceName = null)
        {
            ICounter counter = GetCounter(counterName);

            return counter != null && counter.DecrementBy(value, instanceName);
        }

        public static bool Increment(string counterName, string instanceName = null)
        {
            ICounter counter = GetCounter(counterName);

            return counter != null && counter.Increment(instanceName);
        }

        public static bool IncrementBy(string counterName, long counterValue, string instanceName = null)
        {
            ICounter counter = GetCounter(counterName);

            return counter != null && counter.IncrementBy(counterValue, instanceName);
        }

        public static bool Set(string counterName, long counterValue, string instanceName = null)
        {
            ICounter counter = GetCounter(counterName);

            return counter != null && counter.Set(counterValue, instanceName);
        }
    }
}