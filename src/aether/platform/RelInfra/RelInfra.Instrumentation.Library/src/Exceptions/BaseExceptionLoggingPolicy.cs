﻿using System;
using System.IO;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Net.Http;
using System.Net.Sockets;
using Microsoft.Rest;
using Microsoft.MachineLearning.Common.Core;
using Azure;

namespace Microsoft.RelInfra.Instrumentation.Exceptions
{
    /// <summary>
    /// Policy for determining if an exception can be logged.
    /// <para>NOTE: this is for exceptions we don't author. If we author the exception, then please use PrivacyAwareLogging tags or implement ILoggable instead.</para>
    /// </summary>
    public interface IExceptionLoggingPolicy
    {
        /// <summary>
        /// Return the loggable properties of the given exception.
        /// </summary>
        /// <param name="e"></param>
        /// <returns></returns>
        Dictionary<string, string> LoggableProperties(Exception e);
    }

    /// <summary>
    /// An <see cref="IExceptionLoggingPolicy"/> that allows all exceptions to be logged (for testing/onebox deployments)
    /// </summary>
    public class AllowExceptionLoggingPolicy : IExceptionLoggingPolicy
    {
        private IExceptionLoggingPolicy _delagate;

        public AllowExceptionLoggingPolicy(IExceptionLoggingPolicy delegatePolicy)
        {
            _delagate = delegatePolicy;
        }

        public Dictionary<string, string> LoggableProperties(Exception e)
        {
            // use all the loggable properties of the delegate, but make sure the message is present (instead of just the format)
            Dictionary<string, string> loggableProperties = _delagate.LoggableProperties(e);
            loggableProperties[BaseExceptionLoggingPolicy.MessageStr] = e.Message;
            loggableProperties.Remove(BaseExceptionLoggingPolicy.MessageFormatStr);
            return loggableProperties;
        }
    }

    /// <inheritdoc/>
    public class BaseExceptionLoggingPolicy : IExceptionLoggingPolicy
    {
        public static readonly string MessageStr = "Message";
        public static readonly string MessageFormatStr = "MessageFormat";

        /*
         * =============================================================================================
         * NOTE: if you can't add exceptions to this class (due to it not being present in this
         * project's dependencies), then you can add it to it's subclass RelStorageExceptionLoggingPolicy
         * =============================================================================================
         */

        /*
         * Used this query to find high-value exceptions to add:
         * UnionOfAllLogsWithTest("ServiceLogs")
         * | where message contains "error type: "
         * | extend errorType = extract_all(@"(?i)Error type: ([A-Za-z0-9.]+)", message)
         * | where errorType != ""
         * | mv-expand errorType
         * | project errorType
         * | summarize count() by tostring(errorTy pe)
         */

        /// <summary>
        /// Maps an exception to it's loggable properties
        /// </summary>
        protected virtual ReadOnlyDictionary<Type, Func<Exception, Dictionary<string, string>>> LoggablePropertiesDict => _loggableProperties;

        private static readonly ReadOnlyDictionary<Type, Func<Exception, Dictionary<string, string>>> _loggableProperties = new ReadOnlyDictionary<Type, Func<Exception, Dictionary<string, string>>>(new Dictionary<Type, Func<Exception, Dictionary<string, string>>>()
        {
            // NOTE: if an exception type isn't in this map, but it's super-type is, then we will use the super-types mapping
            // NOTE: this is for exceptions we don't author. If we author the exception, then please use PrivacyAwareLogging tags or implement ILoggable instead.
            { typeof(ArgumentNullException), ex =>  {
                var argEx = ex as ArgumentNullException;
                return  new Dictionary<string, string>() { { nameof(argEx.ParamName), argEx.ParamName }, { MessageStr, argEx.Message } };
            }},
            { typeof(ArgumentException), ex =>  {
                var argEx = ex as ArgumentException;
                return  new Dictionary<string, string>() { { nameof(argEx.ParamName), argEx.ParamName }, { MessageStr, argEx.Message } };
            }},
            { typeof(ArgumentOutOfRangeException), ex =>  {
                var argEx = ex as ArgumentOutOfRangeException;
                return  new Dictionary<string, string>() { { nameof(argEx.ParamName), argEx.ParamName }, { MessageStr, argEx.Message } };
            }},
            { typeof(NullReferenceException), ex =>  {
                var nullRefEx = ex as NullReferenceException;
                return  new Dictionary<string, string>() { { MessageStr, nullRefEx.Message } };
            }},
            { typeof(IOException), ex =>  {
                var ioEx = ex as IOException;
                return  new Dictionary<string, string>() { { MessageStr, ioEx.Message } };
            }},
            { typeof(ServiceInvocationException), ex =>  {
                var siEx = ex as ServiceInvocationException;
                return  new Dictionary<string, string>() { { MessageStr, siEx.Message } };
            }},
            { typeof(HttpRequestException), ex =>  {
                var httpRequestEx = ex as HttpRequestException;
                var props = new Dictionary<string, string>()
                {
                    { MessageStr, httpRequestEx.Message }
                };
                if (httpRequestEx.StatusCode != null)
                {
                    props.Add(nameof(httpRequestEx.StatusCode), httpRequestEx.StatusCode.ToString());
                }
                return props;
            }},
            { typeof(HttpOperationException), ex =>  {
                var httpOperationEx = ex as HttpOperationException;
                var props = new Dictionary<string, string>()
                {
                    { MessageStr, httpOperationEx.Message }
                };
                if (httpOperationEx.Response?.StatusCode != null)
                {
                    props.Add(nameof(httpOperationEx.Response.StatusCode), httpOperationEx.Response.StatusCode.ToString());
                }
                return props;
            }},
            { typeof(WindowsAzure.Storage.StorageException), ex => {
                var storageEx = ex as WindowsAzure.Storage.StorageException;
                Dictionary<string, string> props = new Dictionary<string, string>();
                if (storageEx.RequestInformation != null && storageEx.RequestInformation.HttpStatusCode != 0)
                {
                    props.Add(nameof(storageEx.RequestInformation.HttpStatusCode), storageEx.RequestInformation.HttpStatusCode.ToString());
                }

                string statusMessage = storageEx.RequestInformation?.HttpStatusMessage;
                if (statusMessage != null)
                {
                    props.Add(nameof(storageEx.RequestInformation.HttpStatusMessage), statusMessage);
                    // if authentication/authorization error then just print the status message, otherwise print status message + full error message
                    if (!statusMessage.ToLower().Contains("authorization") && !statusMessage.ToLower().Contains("authentication")) {
                        props.Add(MessageStr, ex.Message);
                    }
                }
                return props;
            }},
            { typeof(RequestFailedException), ex => {
                var requestEx = ex as RequestFailedException;
                Dictionary<string, string> props = new Dictionary<string, string>();
                if (requestEx.Status != 0)
                {
                    props.Add(nameof(requestEx.Status), requestEx.Status.ToString());
                }

                string errorCode = requestEx.ErrorCode;
                if (errorCode != null)
                {
                    props.Add(nameof(requestEx.ErrorCode), errorCode);
                    // if authentication/authorization error then just print the status message, otherwise print status message + full error message
                    if (!errorCode.ToLower().Contains("authorization") && !errorCode.ToLower().Contains("authentication")) {
                        props.Add(MessageStr, ex.Message);
                    }
                }
                return props;
            }},
            { typeof(SocketException), ex =>  {
                var socketEx = ex as SocketException;
                var props = new Dictionary<string, string>()
                {
                    { nameof(socketEx.SocketErrorCode), socketEx.SocketErrorCode.ToString() },
                    { MessageStr, socketEx.Message }
                };
                if (socketEx.ErrorCode != 0)
                {
                    props.Add(nameof(socketEx.ErrorCode), socketEx.ErrorCode.ToString());
                }
                return props;
            }},

        });

        /// <summary>
        /// For use by sub-classes so they can override <see cref="LoggablePropertiesDict"/> and let this class do the rest
        /// </summary>
        /// <param name="otherLoggableProperties"></param>
        /// <param name="myLoggableProperties"></param>
        /// <returns></returns>
        protected static ReadOnlyDictionary<Type, Func<Exception, Dictionary<string, string>>> BuildLoggableProperties(
            ReadOnlyDictionary<Type, Func<Exception, Dictionary<string, string>>> otherLoggableProperties,
            Dictionary<Type, Func<Exception, Dictionary<string, string>>> myLoggableProperties)
        {
            AddAll(myLoggableProperties, otherLoggableProperties, overrideExisting: false);
            return new ReadOnlyDictionary<Type, Func<Exception, Dictionary<string, string>>>(myLoggableProperties);
        }

        /// <summary>
        /// Return the loggable properties of the given exception.
        /// </summary>
        /// <param name="e"></param>
        /// <returns></returns>
        public virtual Dictionary<string, string> LoggableProperties(Exception ex)
        {
            try
            {
                // NOTE: LoggablePropertiesDict is protected, so sub-classes can just set it and this function will work as expected
                return LoggablePropertiesInternal(ex, LoggablePropertiesDict);
            }
            catch (Exception)
            {
                return new Dictionary<string, string>();
            }
        }

        protected Dictionary<string, string> LoggablePropertiesInternal(Exception e, IDictionary<Type, Func<Exception, Dictionary<string, string>>> loggablePropertiesDict)
        {
            Dictionary<string, string> loggableProperties = new Dictionary<string, string>();
            Type type = e.GetType();
            // iterate up the exception's type heirarchy until the type heirarchy ends
            while (type != null && typeof(Exception).IsAssignableFrom(type))
            {
                Func<Exception, Dictionary<string, string>> loggablePropertiesFunc;
                loggablePropertiesFunc = loggablePropertiesDict.TryGetValue(type, out loggablePropertiesFunc) ? loggablePropertiesFunc : null;
                if (loggablePropertiesFunc != null)
                {
                    Dictionary<string, string> moreLoggableProperties = loggablePropertiesFunc(e);
                    if (moreLoggableProperties != null)
                    {
                        AddAll(loggableProperties, moreLoggableProperties, overrideExisting: false);
                    }
                }
                type = type.BaseType;
            }
            return loggableProperties;
        }

        private static Dictionary<K, V> AddAll<Dict, K, V>(Dictionary<K, V> dict, Dict other, bool overrideExisting = true) where Dict : IDictionary<K, V>
        {
            foreach (var kvp in other)
            {
                if (overrideExisting || !dict.ContainsKey(kvp.Key))
                {
                    dict[kvp.Key] = kvp.Value;
                }
            }
            return dict;
        }
    }
}
