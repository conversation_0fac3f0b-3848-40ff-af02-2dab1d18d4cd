﻿using System;
using System.Linq;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Threading.Tasks;
using Microsoft.RelInfra.Common.RetryPolicy;
using Microsoft.RelInfra.Storage.Exceptions;

namespace Microsoft.RelInfra.Storage.Mocks
{
    public class MockFileStorage : IFileStorage
    {
        public MockFileStorage()
        {
            Directories = new Dictionary<string, List<AetherStreamInfo>>();
            Streams = new Dictionary<string, AetherStreamInfo>();
        }

        public static ConcurrentDictionary<string, string> Content = new ConcurrentDictionary<string, string>();
        public Dictionary<string, List<AetherStreamInfo>> Directories { get; }
        public Dictionary<string, AetherStreamInfo> Streams { get; }

        public async Task<bool> AppendLineAsync(string path, string line)
        {
            await Task.Run(() =>
            {
                Content.AddOrUpdate(path, line + "\n", (k, v) => v + line + "\n");
            });
            return true;
        }

        public async Task<bool> CopyFileAsync(string sourcePath, string destPath, System.Threading.CancellationToken cts)
        {
            await Task.Run(() =>
            {
                Content[destPath] = Content[sourcePath];
            }, cts);
            return true;
        }

        public async Task<bool> DeleteFileAsync(string path)
        {
            if (!await FileExistsAsync(path))
            {
                return true;
            }

            return await Task.FromResult(Content.TryRemove(path, out _));
        }

        public async Task<bool> FileExistsAsync(string path)
        {
            return await Task.FromResult(Content.ContainsKey(path));
        }

        public async Task<bool> FileExistsAsync(string path, bool allowIncomplete)
        {
            return await Task.FromResult(Content.ContainsKey(path));
        }

        public Task<bool> IsDirectoryAsync(string path)
        {
            return Task.FromResult(Directories.ContainsKey(path));
        }

        public Task<IEnumerable<string>> ListDirectoryAsync(string path, bool recursive, uint fileCountLimit = 20000)
        {
            if (!Directories.ContainsKey(path))
            {
                throw new KeyNotFoundException();
            }

            if (Directories[path].Count > fileCountLimit)
            {
                throw new ArgumentException($"Folder is too big. it contains more the {fileCountLimit} streams.");
            }
            return Task.FromResult(Directories[path].Select(s => s.StreamName));
        }

        public async Task<bool> CreateFileAsync(string path, string content)
        {
            return await Task.FromResult(Content.TryAdd(path, content));
        }

        public async Task<long> GetFileLengthAsync(string path)
        {
            return await Task.Run(() => Content[path].Length);
        }

        public async Task<long> GetDirectorySizeAsync(string path)
        {
            return await GetFileLengthAsync(path);
        }

        public async Task<Stream> ReadStreamAsync(string path, long offset, long length)
        {
            return await Task.FromResult(Content.ContainsKey(path) ? new MemoryStream(Encoding.UTF8.GetBytes(Content[path])) : null);
        }

        public IRelInfraRetryPolicy RetryPolicy
        {
            get
            {
                throw new NotImplementedException();
            }
            set
            {
                throw new NotImplementedException();
            }
        }

        public Task<Storable<T>> PutAsync<T>(string containerName, string resourceId, Storable<T> resource)
        {
            throw new NotImplementedException();
        }

        public Task<IEnumerable<Storable<T>>> BatchPutAsync<T>(string containerName, IEnumerable<Storable<T>> resources)
        {
            throw new NotImplementedException();
        }

        public Task<Storable<T>> GetAsync<T>(string containerName, string resourceId)
        {
            throw new NotImplementedException();
        }

        public Task<IEnumerable<Storable<T>>> BatchGetAsync<T>(string containerName, IEnumerable<string> resourceIds)
        {
            throw new NotImplementedException();
        }

        public Task<Storable<T>> UpdateAsync<T>(string containerName, string resourceId, Storable<T> resource)
        {
            throw new NotImplementedException();
        }

        public Task<IEnumerable<Storable<T>>> BatchUpdateAsync<T>(string containerName, IEnumerable<Storable<T>> resources)
        {
            throw new NotImplementedException();
        }

        public Task DeleteAsync(string containerName, string resourceId)
        {
            throw new NotImplementedException();
        }

        public Task DeleteAsync(string containerName, string resourceId, string etag)
        {
            throw new NotImplementedException();
        }

        public Task BatchDeleteAsync(string containerName, IEnumerable<string> resourceIds)
        {
            throw new NotImplementedException();
        }

        public Task<bool> ExistsAsync(string containerName, string resourceId)
        {
            throw new NotImplementedException();
        }

        public Task<Storable<T>> AddAsync<T>(string containerName, string resourceId, Storable<T> resource)
        {
            throw new NotImplementedException();
        }

        public Task<bool> TryPutAsync<T>(string containerName, string resourceId, Storable<T> resource)
        {
            throw new NotImplementedException();
        }

        public Task<bool> TryBatchPutAsync<T>(string containerName, IEnumerable<Storable<T>> resources)
        {
            throw new NotImplementedException();
        }

        public Task<Storable<T>> TryGetAsync<T>(string containerName, string resourceId)
        {
            throw new NotImplementedException();
        }

        public Task<bool> TryUpdateAsync<T>(string containerName, string resourceId, Storable<T> resource)
        {
            throw new NotImplementedException();
        }

        public Task<bool> TryDeleteAsync(string containerName, string resourceId)
        {
            throw new NotImplementedException();
        }

        public async Task<bool> AppendAsync(string path, string text, bool compression)
        {
            await Task.Run(() => {
                               Content.AddOrUpdate(path, text, (k, v) => v + text);
                           });
            return true;
        }

        public async Task<bool> CreateFileAsync(string path, string content, TimeSpan expirationTime)
        {
            return await CreateFileAsync(path, content);
        }

        public Task CreateDirectoryAsync(string path)
        {
            throw new NotImplementedException();
        }

        public Task<string> ReadAllTextAsync(string path)
        {
            string value;
            if (Content.TryGetValue(path, out value))
            {
                return Task.FromResult(value);
            }

            throw new RelInfraStorageException(string.Format("Can't find file {0}", path), StorageError.NotFound);
        }
    }
}
