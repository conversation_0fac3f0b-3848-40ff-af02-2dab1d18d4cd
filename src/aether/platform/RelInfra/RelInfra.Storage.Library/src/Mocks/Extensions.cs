﻿using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Reflection;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;

namespace Microsoft.RelInfra.Storage.Test.Mocks
{
    internal static class Extensions
    {
        public static object Merge(this object entity, object newEntity)
        {
            if (IsAllowedNonExpandoType(entity, newEntity))
            {
                return newEntity;
            }

            var returnEntity = JsonConvert.DeserializeObject<ExpandoObject>(JsonConvert.SerializeObject(entity), new ExpandoObjectConverter());
            var returnEntityAsDictionary = returnEntity as IDictionary<string, object>;

            PropertyInfo[] properties = newEntity.GetType().GetProperties();
            foreach (PropertyInfo propInfo in properties)
            {
                if (propInfo.GetValue(newEntity, null) != null)
                {
                    returnEntityAsDictionary[propInfo.Name] = propInfo.GetValue(newEntity, null);
                }
            }

            return returnEntity;
        }

        private static bool IsAllowedNonExpandoType(object entity, object newEntity)
        {
            var hashSets = new HashSet<Type>()
            {
                typeof(string)
            };

            return entity.GetType() == newEntity.GetType() && hashSets.Contains(entity.GetType());
        }
    }
}