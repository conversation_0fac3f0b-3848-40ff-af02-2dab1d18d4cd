﻿using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Microsoft.Data.Edm;
using Microsoft.Data.Edm.Library;
using Microsoft.Data.OData.Query;
using Microsoft.Data.OData.Query.SemanticAst;
using Microsoft.RelInfra.Common.RetryPolicy;
using Microsoft.RelInfra.Storage.Exceptions;
using Newtonsoft.Json;

namespace Microsoft.RelInfra.Storage.Test.Mocks
{
    /// <summary>
    ///    Avoid this class, it is has static stated shared among instances
    /// </summary>
    public class AzureTableStorageStaticMock : ITableStorage
    {
        internal class AzureTableEntity
        {
            public AzureTableEntity(string pk, string rk)
            {
                PartitionKey = pk;
                RowKey = rk;
                ETag = DateTime.UtcNow.Ticks.ToString(CultureInfo.InvariantCulture);
            }

            public string PartitionKey { get; set; }
            public string RowKey { get; set; }

            public string SerializedEntity { get; set; }

            public string ETag { get; set; }
        }

        private static readonly ConcurrentDictionary<string, AzureTableEntity> Storage = new ConcurrentDictionary<string, AzureTableEntity>();

        public static IEnumerable<string> Tables { get { return Storage.Keys.Select(k => k.Split(':')[0]).Distinct(); } }

        public string SanitizeKey(string key)
        {
            // Explicitly disallowed chars in Azure table key fields
            // http://msdn.microsoft.com/en-us/library/dd179338.aspx
            string sanitizedKey = key.Replace('/', '-')
                .Replace('\\', '-')
                .Replace('#', '-')
                .Replace('?', '-');

            // Not documented but fail in Azure table
            return sanitizedKey.Replace('\'', '-');
        }

        public IEnumerable<string> Partitions
        {
            get { return Storage.Keys.Where(IsInTable).Select(k => k.Split(':')[1]).Distinct(); }
        }

        static readonly EdmModel _edmModel = new EdmModel();

        private readonly string _tableName;

        public AzureTableStorageStaticMock(string tableName)
        {
            _tableName = tableName;
        }

        static string GetStorageKey(string tableName, string pk, string rk)
        {
            return String.Format("{0}:{1}:{2}", tableName, pk, rk);
        }

        public IRelInfraRetryPolicy RetryPolicy { get; set; }

        public async Task<bool> ExistsAsync(string containerName, string resourceId)
        {
            AzureTableEntity entity;
            return await Task.FromResult(Storage.TryGetValue(GetStorageKey(_tableName, containerName, resourceId), out entity));
        }

        public async Task<Storable<T>> AddAsync<T>(string containerName, string resourceId, Storable<T> resource)
        {
            var entity = new AzureTableEntity(containerName, resourceId)
            {
                SerializedEntity = JsonConvert.SerializeObject(resource.Entity),
                ETag = Guid.NewGuid().ToString()
            };

            string key = GetStorageKey(_tableName, containerName, resourceId);
            Storable<T> result;
            lock (_tableName)
            {
                if (!Storage.TryAdd(key, entity))
                {
                    throw new RelInfraStorageException(StorageError.Conflict);
                }

                AzureTableEntity tableEntity = Storage[key];
                result = CreateStorable<T>(containerName, resourceId, tableEntity);
            }
            return await Task.FromResult(result);
        }

        public async Task<bool> TryPutAndIgnoreEtagAsync<T>(string containerName, string resourceId, Storable<T> resource)
        {
            try
            {
                Storable<T> s = await PutAndIgnoreEtagAsync<T>(containerName, resourceId, resource);
                return s != null;
            }
            catch
            {
                return false;
            }
        }

        public Task<Storable<T>> PutAndIgnoreEtagAsync<T>(string containerName, string resourceId, Storable<T> resource)
        {
            var entity = new AzureTableEntity(containerName, resourceId)
            {
                SerializedEntity = JsonConvert.SerializeObject(resource.Entity),
                ETag = Guid.NewGuid().ToString()
            };
            AzureTableEntity tableEntity = Storage.AddOrUpdate(GetStorageKey(_tableName, containerName, resourceId), s => entity,
                (key, te) =>
                {
                    var obj = JsonConvert.DeserializeObject(te.SerializedEntity);
                    entity.SerializedEntity = JsonConvert.SerializeObject(obj.Merge(resource.Entity));
                    return entity;
                });

            return Task.FromResult(CreateStorable<T>(containerName, resourceId, tableEntity));
        }

        public async Task<bool> TryBatchPutAsync<T>(string containerName, IEnumerable<Storable<T>> resources)
        {
            bool isSucceeded = true;
            foreach (var res in resources)
            {
                isSucceeded &= await TryPutAndIgnoreEtagAsync(containerName, res.Id, res);
            }

            return isSucceeded;
        }

        public async Task<IEnumerable<Storable<T>>> BatchPutAsync<T>(string containerName, IEnumerable<Storable<T>> resources)
        {
            return await Task.WhenAll(resources.Select(res => PutAndIgnoreEtagAsync(containerName, res.Id, res)).ToList());
        }

        public async Task<Storable<T>> TryGetAsync<T>(string containerName, string resourceId)
        {
            try
            {
                return await GetAsync<T>(containerName, resourceId);
            }
            catch (Exception)
            {
                return null;
            }
        }

        public async Task<Storable<T>> GetAsync<T>(string containerName, string resourceId)
        {
            Storable<T> result;
            lock (_tableName)
            {
                AzureTableEntity entity;
                if (!Storage.TryGetValue(GetStorageKey(_tableName, containerName, resourceId), out entity))
                {
                    throw new RelInfraStorageException(StorageError.NotFound);
                }

                result = CreateStorable<T>(containerName, resourceId, entity);
            }
            return await Task.FromResult(result);
        }

        public async Task<IEnumerable<Storable<T>>> BatchGetAsync<T>(string containerName, IEnumerable<string> resourceIds)
        {
            return await Task.WhenAll(resourceIds.Select(id => GetAsync<T>(containerName, id)));
        }

        public async Task<bool> TryUpdateAsync<T>(string containerName, string resourceId, Storable<T> resource)
        {
            try
            {
                await UpdateAsync(containerName, resourceId, resource);
                return true;
            }
            catch (RelInfraStorageException)
            {
                return false;
            }
        }

        public async Task<Storable<T>> UpdateAsync<T>(string containerName, string resourceId, Storable<T> resource)
        {
            if (String.IsNullOrWhiteSpace(resource.Etag))
            {
                throw new ArgumentException("Update operation requires Etag", "resource");
            }

            Storable<T> result;
            lock (_tableName)
            {
                AzureTableEntity oldValue;
                var storageKey = GetStorageKey(_tableName, containerName, resourceId);
                if (!Storage.TryGetValue(storageKey, out oldValue))
                {
                    throw new RelInfraStorageException(StorageError.NotFound);
                }

                if (resource.Etag != "*" && oldValue.ETag != resource.Etag)
                {
                    throw new RelInfraStorageException(StorageError.PreconditionFailed);
                }

                var obj = JsonConvert.DeserializeObject(oldValue.SerializedEntity);


                var newValue = new AzureTableEntity(containerName, resourceId)
                {
                    SerializedEntity = JsonConvert.SerializeObject(obj.Merge(resource.Entity)),
                    ETag = Guid.NewGuid().ToString()
                };

                if (!Storage.TryUpdate(storageKey, newValue, oldValue))
                {
                    throw new RelInfraStorageException(StorageError.UndefinedError);
                }

                result = CreateStorable<T>(containerName, resourceId, newValue);
            }

            return await Task.FromResult(result);
        }

        public async Task<IEnumerable<Storable<T>>> BatchUpdateAsync<T>(string containerName, IEnumerable<Storable<T>> resources)
        {
            return await Task.WhenAll(resources.Select(res => UpdateAsync(containerName, res.Id, res)));
        }

        public async Task<bool> TryDeleteAsync(string containerName, string resourceId)
        {
            try
            {
                await DeleteAsync(containerName, resourceId);
            }
            catch (Exception)
            {
                return false;
            }
            return true;
        }

        public async Task DeleteAsync(string containerName, string resourceId)
        {
            AzureTableEntity entity;
            if (!Storage.TryRemove(GetStorageKey(_tableName, containerName, resourceId), out entity))
            {
                throw new RelInfraStorageException(StorageError.NotFound);
            }

            await Task.Run(() => { });
        }

        public async Task DeleteAsync(string containerName, string resourceId, string etag)
        {
            lock (_tableName)
            {
                AzureTableEntity entity;
                if (Storage.TryGetValue(GetStorageKey(_tableName, containerName, resourceId), out entity) && etag != "*" && etag != entity.ETag)
                {
                    throw new RelInfraStorageException(StorageError.PreconditionFailed);
                }
                Storage.TryRemove(GetStorageKey(_tableName, containerName, resourceId), out entity);
            }

            await Task.Run(() => { });
        }

        public async Task BatchDeleteAsync(string containerName, IEnumerable<string> resourceIds)
        {
            foreach (string resourceId in resourceIds.Where(resourceId => !Storage.ContainsKey(GetStorageKey(_tableName, containerName, resourceId))))
            {
                throw new RelInfraStorageException("Resource with id [" + resourceId + "] not found", StorageError.NotFound);
            }

            await Task.WhenAll(resourceIds.Select(id => DeleteAsync(containerName, id)));
        }

        private static EdmEntityType BuildEdmEntityType(Type type)
        {
            var entityType = new EdmEntityType("AetherTest", type.Name);

            IEdmStructuralProperty pk = entityType.AddStructuralProperty("PartitionKey", EdmCoreModel.Instance.GetString(false));
            IEdmStructuralProperty rk = entityType.AddStructuralProperty("RowKey", EdmCoreModel.Instance.GetString(false));
            entityType.AddKeys(new [] {pk, rk});

            foreach (var propInfo in type.GetProperties().Where(pi => pi.PropertyType == typeof(string)))
            {
                // support string properties for now
                entityType.AddStructuralProperty(propInfo.Name, EdmCoreModel.Instance.GetString(false));
                if (propInfo.GetCustomAttributes(typeof(CompressObjectAttribute)).FirstOrDefault() != null)
                {
                    entityType.AddStructuralProperty($"{propInfo.Name}{CompressObjectHelper.CompressedSuffix}", EdmCoreModel.Instance.GetString(false));
                }
            }
            return entityType;
        }

        /// <summary>
        /// this method might be slow, so it should NEVER be used in production
        /// it's ok for tests only
        ///
        /// * Supports only string properties!!!
        /// * doesn't support negation unary operator
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="pk"></param>
        /// <param name="rk"></param>
        /// <param name="entity"></param>
        /// <param name="queryNode"></param>
        /// <returns></returns>
        private static bool SatisfiesCondition<T>(string pk, string rk, T entity, QueryNode queryNode)
        {
            if (queryNode.Kind == QueryNodeKind.UnaryOperator)
            {
                if (((UnaryOperatorNode) queryNode).OperatorKind != UnaryOperatorKind.Not)
                {
                    throw new Exception("Only NOT operator supported, but was " + queryNode.Kind);
                }
                return !SatisfiesCondition(pk, rk, entity, ((UnaryOperatorNode) queryNode).Operand);
            }

            BinaryOperatorNode node = (BinaryOperatorNode) queryNode;

            if (node.OperatorKind == BinaryOperatorKind.And)
            {
                return SatisfiesCondition(pk, rk, entity, node.Left)
                       && SatisfiesCondition(pk, rk, entity, node.Right);
            }

            if (node.OperatorKind == BinaryOperatorKind.Or)
            {
                return SatisfiesCondition(pk, rk, entity, node.Left)
                       || SatisfiesCondition(pk, rk, entity, node.Right);
            }

            if (node.Left.Kind != QueryNodeKind.Convert)
            {
                throw new Exception("Expected ConvertNode, but was " + node.Left.Kind);
            }

            if (node.Right.Kind != QueryNodeKind.Constant)
            {
                throw new Exception("Expected ConstantNode, but was " + node.Right.Kind);
            }

            string propName = ((SingleValuePropertyAccessNode) ((ConvertNode) node.Left).Source).Property.Name;
            string valueToCompare = (string)((ConstantNode) node.Right).Value;

            string actualValue;
            if (propName == "PartitionKey")
            {
                actualValue = pk;
            }
            else if (propName == "RowKey")
            {
                actualValue = rk;
            }
            else
            {
                actualValue = typeof(T).GetProperty(propName) != null ? (string)typeof(T).GetProperty(propName).GetValue(entity) : null;
            }

            switch (node.OperatorKind)
            {
                case BinaryOperatorKind.Equal:
                    return String.Compare(actualValue, valueToCompare, StringComparison.OrdinalIgnoreCase) == 0;
                case BinaryOperatorKind.NotEqual:
                    return String.Compare(actualValue, valueToCompare, StringComparison.OrdinalIgnoreCase) != 0;
                case BinaryOperatorKind.GreaterThan:
                    return String.Compare(actualValue, valueToCompare, StringComparison.OrdinalIgnoreCase) > 0;
                case BinaryOperatorKind.GreaterThanOrEqual:
                    return String.Compare(actualValue, valueToCompare, StringComparison.OrdinalIgnoreCase) >= 0;
                case BinaryOperatorKind.LessThan:
                    return String.Compare(actualValue, valueToCompare, StringComparison.OrdinalIgnoreCase) < 0;
                case BinaryOperatorKind.LessThanOrEqual:
                    return String.Compare(actualValue, valueToCompare, StringComparison.OrdinalIgnoreCase) <= 0;
                default:
                    throw new ArgumentOutOfRangeException();
            }
        }

        public async Task<IEnumerable<Storable<T>>> ExecuteQueryAsync<T>(string query, int? takeCount)
        {
            IEnumerable<Storable<T>> results;
            if (string.IsNullOrWhiteSpace(query))
            {
                results = await GetAllAsync<T>();
            }
            else
            {
                // EdmModel is not multithreaded, so use lock here
                EdmEntityType type;
                lock (_edmModel)
                {
                    if ((type = (EdmEntityType)_edmModel.SchemaElements.FirstOrDefault(t => t.Name == typeof(T).Name)) == null)
                    {
                        // build type
                        type = BuildEdmEntityType(typeof(T));
                        _edmModel.AddElement(type);
                    }
                }
                // create AST
                var filter = ODataUriParser.ParseFilter(query, _edmModel, type);

                results = 
                    Storage
                    .Where(IsInTable)
                    .Where(
                        pair =>
                            {
                                T entity = JsonConvert.DeserializeObject<T>(pair.Value.SerializedEntity);
                                return SatisfiesCondition(
                                    pair.Value.PartitionKey,
                                    pair.Value.RowKey,
                                    entity,
                                    filter.Expression);
                            })
                    .Select(pair => CreateStorable<T>(pair.Value.PartitionKey, pair.Value.RowKey, pair.Value));
            }

            if (takeCount != null)
            {
                results = results.Take(takeCount.Value);
            }

            return await Task.FromResult(results);
        }

        public async Task<QueryResults<T>> GetPartitionWithCountinuationTokenAsync<T>(string partitionKey, IEnumerable<string> columns, string continuationToken = null, int? takeCount = null)
        {
            return await GetPartitionWithCountinuationTokenAsync<T>(partitionKey: partitionKey,
                                                                    continuationToken: continuationToken,
                                                                    takeCount: takeCount);
        }

        public async Task<IEnumerable<Storable<T>>> GetPartitionAsync<T>(string partitionKey, int? takeCount)
        {
            IEnumerable<AzureTableEntity> entities = Storage
                .Where(pair => IsInTable(pair.Key) && pair.Value.PartitionKey == partitionKey)
                .OrderBy(pair => pair.Key).Select(pair => pair.Value);
            return await Task.FromResult(entities.Select(item => CreateStorable<T>(partitionKey, item.RowKey, item)));
        }

        public async Task<IEnumerable<Storable<T>>> GetAllAsync<T>(int? takeCount = null)
        {
            IEnumerable<AzureTableEntity> entities = Storage
                .Where(IsInTable)
                .OrderBy(pair => pair.Key).Select(pair => pair.Value);
            return await Task.FromResult(entities.Select(item => CreateStorable<T>(item.PartitionKey, item.RowKey, item)));
        }

        public Task<bool> TableExistsAsync()
        {
            return Task.FromResult(Storage.Keys.Any(IsInTable));
        }

        public Task CreateIfNotExistsAsync()
        {
            Object o = null;
            return Task.Run(() => GC.KeepAlive(o));
        }

        public Task DeleteIfExistsAsync()
        {
            foreach (string key in Storage.Keys.Where(IsInTable).ToList())
            {
                AzureTableEntity e;
                Storage.TryRemove(key, out e);
            }
            Object o = null;
            return Task.Run(() => GC.KeepAlive(o));
        }

        public static void ClearStorage()
        {
            Storage.Clear();
        }

        public string TableName
        {
            get { return _tableName; }
        }

        public async Task<QueryResults<T>> GetPartitionWithCountinuationTokenAsync<T>(string partitionKey, string continuationToken, int? takeCount = null)
        {
            IEnumerable<AzureTableEntity> entities = Storage
                .Where(pair => IsInTable(pair) && pair.Value.PartitionKey == partitionKey)
                .OrderBy(pair => pair.Key).Select(pair => pair.Value);

            QueryResults<T> res = new QueryResults<T>(
                continuationToken: null,
                results: entities.Select(item => CreateStorable<T>(partitionKey, item.RowKey, item)));
            return await Task.FromResult(res);
        }

        public async Task<QueryResults<T>> GetAllWithContinuationTokenAsync<T>(string continuationToken = null, int? takeCount = null)
        {
            QueryResults<T> results = new QueryResults<T>(
                continuationToken: null,
                results: await ExecuteQueryAsync<T>(string.Empty, takeCount));

            return results;
        }

        public async Task<int> GetRowCountAsync<T>(string partitionKey)
        {
            return await Task.FromResult(Storage.Keys.Count(key => IsInPartition(key, partitionKey)));
        }

        public async Task InsertOrReplaceAsync<T>(IEnumerable<Storable<T>> entities)
        {
            foreach (var storable in entities)
            {
                var entity = new AzureTableEntity(storable.ContainerId, storable.Id)
                {
                    SerializedEntity = JsonConvert.SerializeObject(storable.Entity),
                    ETag = Guid.NewGuid().ToString()
                };
                AzureTableEntity tableEntity = Storage.AddOrUpdate(GetStorageKey(_tableName, storable.ContainerId, storable.Id), s => entity,
                    (key, te) =>
                    {
                        if (storable.Etag == "*" || storable.Etag == te.ETag)
                        {
                            entity.SerializedEntity = JsonConvert.SerializeObject(storable.Entity);
                            return entity;
                        }

                        return te;
                    });
            }
            await Task.FromResult(true);
        }

        public Task InitializeAsync(string containerName)
        {
            return Task.Delay(0);
        }

        private static Storable<T> CreateStorable<T>(string container, string id, AzureTableEntity entity)
        {
            return new Storable<T>(JsonConvert.DeserializeObject<T>(entity.SerializedEntity))
                       {
                           ContainerId = container,
                           Id = id,
                           Etag = entity.ETag,
                       };
        } 

        private bool IsInTable(KeyValuePair<string, AzureTableEntity> item)
        {
            return IsInTable(item.Key);
        }

        private bool IsInTable(string key)
        {
            return key.StartsWith(_tableName + ":");
        }

        private bool IsInPartition(string key, string partition)
        {
            return key.StartsWith($"{_tableName}:{partition}");
        }
    }
}
