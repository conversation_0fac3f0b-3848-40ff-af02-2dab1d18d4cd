﻿using Microsoft.RelInfra.Storage.Exceptions;
using System;
using System.Collections.Concurrent;
using System.Diagnostics;
using System.Threading;
using System.Threading.Tasks;

namespace Microsoft.RelInfra.Storage.Test.Mocks
{
    public class AzureBlobLeasableContainerStorageMock : ILeasableStorage
    {
        class LeaseEntity
        {
            public LeaseEntity(string leaseId, DateTime expirationDateTime, TimeSpan leaseTimeSpan)
            {
                LeaseId = leaseId;
                ExpireDateTime = expirationDateTime;
                LeaseTimeSpan = leaseTimeSpan;
            }

            internal string LeaseId { get; set; }
            internal DateTime ExpireDateTime { get; set; }
            internal TimeSpan LeaseTimeSpan { get; set; }
        }
        private readonly ConcurrentDictionary<string, LeaseEntity> _locks = new ConcurrentDictionary<string, LeaseEntity>();
        private readonly object _leaseSync = new object();
        private readonly string _containerName;

        public AzureBlobLeasableContainerStorageMock(string containerName)
        {
            _containerName = containerName;
        }

        private string GetStorageKey(string containerName, string resourceId)
        {
            return $"{containerName}/{resourceId}";
        }

        public async Task<string> AcquireLeaseAsync(string blobName, TimeSpan? leaseTime, string proposedLeaseId = null, CancellationToken cancellationToken = default(CancellationToken))
        {
            string key = GetStorageKey(_containerName, blobName);

            if (leaseTime == null)
            {
                leaseTime = TimeSpan.FromSeconds(30);
            }

            if (String.IsNullOrEmpty(proposedLeaseId))
            {
                proposedLeaseId = Guid.NewGuid().ToString();
            }

            lock (_leaseSync)
            {
                LeaseEntity storedLease = _locks.GetOrAdd(key,
                    new LeaseEntity(proposedLeaseId, DateTime.Now.Add(leaseTime.Value), leaseTime.Value));
                if (storedLease.LeaseId != proposedLeaseId && storedLease.ExpireDateTime > DateTime.Now)
                {
                    Trace.WriteLine($"LEASE Conflict {_containerName} {blobName}");
                    throw new RelInfraStorageException(StorageError.Conflict);
                }
                _locks[key] = new LeaseEntity(proposedLeaseId, DateTime.Now.Add(leaseTime.Value), leaseTime.Value);
            }

            Trace.WriteLine($"LEASE Acquired  {_containerName} {blobName} {proposedLeaseId}");

            return await Task.FromResult(proposedLeaseId);
        }

        public async Task RenewLeaseAsync(string blobName, string leaseId, CancellationToken cancellationToken = default(CancellationToken))
        {
            await Task.Run(() =>
            {
                string key = GetStorageKey(_containerName, blobName);
                lock (_leaseSync)
                {
                    LeaseEntity lease;
                    if (!_locks.TryGetValue(key, out lease))
                    {
                        Trace.WriteLine($"LEASE NotFound  {_containerName} {blobName} {leaseId}");

                        throw new RelInfraStorageException(StorageError.NotFound);
                    }

                    if (lease.LeaseId != leaseId)
                    {
                        Trace.WriteLine($"LEASE NotFound, wrong leaseId  {_containerName} {blobName} {leaseId}");
                        throw new RelInfraStorageException(StorageError.NotFound);
                    }
                    Trace.WriteLine($"LEASE Renewed  {_containerName} {blobName} {leaseId}");
                    _locks[key] = new LeaseEntity(leaseId, DateTime.Now.Add(lease.LeaseTimeSpan), lease.LeaseTimeSpan);
                }
            });
        }

        public async Task ReleaseLeaseAsync(string blobName, string leaseId, CancellationToken cancellationToken = default(CancellationToken))
        {
            await Task.Run(() =>
            {
                string key = GetStorageKey(_containerName, blobName);
                lock (_leaseSync)
                {
                    LeaseEntity dummy;
                    if (_locks.TryRemove(key, out dummy))
                    {
                        Trace.WriteLine($"LEASE Released {_containerName} {blobName} {leaseId}");
                    }
                    else {
                        Trace.WriteLine($"LEASE Not Released {_containerName} {blobName} {leaseId}");
                    }
                    
                }
            });
        }

        public Task InitializeBlobAsync(string blobName)
        {
            throw new NotImplementedException();
        }

        public Task DeleteAsync(string resourceId)
        {
            return ReleaseLeaseAsync(blobName: resourceId, leaseId: "UNKNOWN");
        }
    }
}
