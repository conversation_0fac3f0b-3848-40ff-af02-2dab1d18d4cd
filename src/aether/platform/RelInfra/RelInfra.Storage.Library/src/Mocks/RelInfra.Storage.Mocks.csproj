<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <RootNamespace>Microsoft.RelInfra.Storage.Mocks</RootNamespace>
    <AssemblyName>Microsoft.RelInfra.Storage.Mocks</AssemblyName>
    <TargetFramework>net6.0</TargetFramework>
    <ResolveAssemblyWarnOrErrorOnTargetArchitectureMismatch>None</ResolveAssemblyWarnOrErrorOnTargetArchitectureMismatch>
    <PackageVersion>7.1.1</PackageVersion>
    <PackageDescription>Various storage utilities and helper classes - Azure, Cosmos and File storage for Relevance Infrastructure teams</PackageDescription>
    <PackageId>RelInfra.Storage.Mocks.Library</PackageId>
    <QInstrumentForCoverage>false</QInstrumentForCoverage>
  </PropertyGroup>
  <Import Project="..\..\..\..\nuget.props" />
  <ItemGroup>
    <ProjectReference Include="..\..\..\RelInfra.Common.Library\src\Common\RelInfra.Common.csproj" />
    <ProjectReference Include="..\..\..\RelInfra.Instrumentation.Library\src\Microsoft.RelInfra.Instrumentation.csproj" />
    <ProjectReference Include="..\Storage\RelInfra.Storage.csproj" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="WindowsAzure.Storage" />
    <PackageReference Include="Microsoft.Data.Edm" />
    <PackageReference Include="Microsoft.Data.OData" />
    <PackageReference Include="coverlet.collector" />
  </ItemGroup>
</Project>
