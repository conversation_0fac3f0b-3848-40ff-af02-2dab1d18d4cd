﻿using System.Collections.Generic;
using Microsoft.RelInfra.Instrumentation;

namespace Microsoft.RelInfra.Storage.Test.Mocks
{
    public class TableStorageFactoryMock : ITableStorageFactory
    {
        private readonly Dictionary<string, Dictionary<string, ITableStorage>> _tables = new Dictionary<string, Dictionary<string, ITableStorage>>();

        public ITableStorage GetTable(string connectionString, string tableName, CounterManager counterManager)
        {
            if (!_tables.ContainsKey(connectionString))
            {
                _tables[connectionString] = new Dictionary<string, ITableStorage>();
            }

            if (!_tables[connectionString].ContainsKey(tableName))
            {
                _tables[connectionString][tableName] = new AzureTableStorageStaticMock(tableName);
            }

            return _tables[connectionString][tableName];
        }
    }
}