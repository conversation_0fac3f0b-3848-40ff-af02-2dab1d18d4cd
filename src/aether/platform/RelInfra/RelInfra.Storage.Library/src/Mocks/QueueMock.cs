﻿using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Globalization;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.RelInfra.Common.RetryPolicy;
using Microsoft.RelInfra.Common.Serialization;
using Microsoft.WindowsAzure.Storage;

namespace Microsoft.RelInfra.Storage.Test.Mocks
{
    // TODO: make it concurrent
    public class QueueMock<T> : IQueue<T>
    {
        public string QueueName { get; set; }

        public class MockQueueEntry
        {
            public string Message { get; private set; }
            private DateTime _visibleAfter;
            public string Etag { get; private set; }
            public int DequeueCount { get; set; }

            public TimeSpan LeaseTime { get; set; }

            public void AddLeaseTime(TimeSpan timeSpan)
            {
                _visibleAfter = DateTime.Now + timeSpan;
                LeaseTime = timeSpan;
            }

            public bool IsVisible
            {
                get { return DateTime.Now >= _visibleAfter; }
            }

            public MockQueueEntry(string message, string etag, TimeSpan visibleAfter)
            {
                Message = message;
                LeaseTime = visibleAfter;
                _visibleAfter = DateTime.Now + visibleAfter;
                Etag = etag;
                DequeueCount = 0;
            }
        }

        public QueueMock(string queueName, Func<Exception> throwOnCrudFactory = null, int throwOnCrudRemaining = 0)
        {
            QueueName = queueName;
            _throwOnCrudFactory = throwOnCrudFactory;
            _throwOnCrudRemaining = throwOnCrudRemaining;
        }

        public readonly ConcurrentDictionary<string, MockQueueEntry> Queue = new ConcurrentDictionary<string, MockQueueEntry>();

        public Task InitializeQueueAsync()
        {
            return Task.Delay(0);
        }

        private readonly SemaphoreSlim _readLock = new SemaphoreSlim(1);

        public void SetCrudExceptional(Func<Exception> throwOnCrudFactory, int throwOnCrudRemaining)
        {
            _throwOnCrudFactory = throwOnCrudFactory;
            Interlocked.Exchange(ref _throwOnCrudRemaining, throwOnCrudRemaining);
        }

        public async Task<Storable<T>> GetAsync(TimeSpan leaseTime)
        {
            ThrowOnCrudIfSpecified();
            try
            {
                await _readLock.WaitAsync();

                KeyValuePair<string, MockQueueEntry> firstVisible = Queue.FirstOrDefault(pair => pair.Value.IsVisible);
                if (firstVisible.Key != null)
                {
                    MockQueueEntry mockQueueEntry;
                    if (Queue.TryGetValue(firstVisible.Key, out mockQueueEntry))
                    {
                        var message = SerializationHelpers.DeserializeEntity<Storable<T>>(mockQueueEntry.Message);
                        mockQueueEntry.AddLeaseTime(leaseTime);
                        mockQueueEntry.DequeueCount++;
                        return await Task.FromResult(message);
                    }
                }

                return null;
            }
            finally
            {
                _readLock.Release();
            }
        }

        public async Task<IEnumerable<Storable<T>>> GetMessagesAsync(int messageCount, TimeSpan leaseTime)
        {
            ThrowOnCrudIfSpecified();
            try
            {
                await _readLock.WaitAsync();
                List<MockQueueEntry> visibleMessages = Queue.Values.Where(msg => msg.IsVisible).Take(messageCount).ToList();

                var messages = new List<Storable<T>>();
                foreach (MockQueueEntry mockQueueEntry in visibleMessages)
                {
                    messages.Add(SerializationHelpers.DeserializeEntity<Storable<T>>(mockQueueEntry.Message));
                    mockQueueEntry.AddLeaseTime(leaseTime);
                    mockQueueEntry.DequeueCount++;
                }

                if (messages.Count == 0)
                {
                    return await Task.FromResult(new List<Storable<T>>());
                }

                return await Task.FromResult(messages);
            }
            finally
            {
                _readLock.Release();
            }
        }

        public IRelInfraRetryPolicy RetryPolicy { get; set; }

        public async Task<Storable<T>> GetAsync()
        {
            const int defaultLeaseTime = 30;
            return await GetAsync(TimeSpan.FromTicks(defaultLeaseTime));
        }

        public Task PutAsync(Storable<T> message)
        {
            return PutAsync(message, visibilityDelay: TimeSpan.Zero);
        }

        public Task PutAsync(Storable<T> message, TimeSpan visibilityDelay)
        {
            ThrowOnCrudIfSpecified();
            var temp = SerializationHelpers.DeserializeEntity<Storable<T>>(SerializationHelpers.SerializeEntity(message));
            temp.Id = String.IsNullOrWhiteSpace(temp.Id) ? CreateQueueItemId(TimeSpan.FromSeconds(0)) : temp.Id;
            temp.Etag = String.IsNullOrWhiteSpace(temp.Etag) ? Guid.NewGuid().ToString() : temp.Etag;

            Queue.TryAdd(temp.Id, new MockQueueEntry(SerializationHelpers.SerializeEntity(temp), temp.Etag, visibilityDelay));

            return Task.FromResult(true);
        }

        public Task PutAsync(Storable<T> message, TimeSpan? visibilityDelay, TimeSpan? timeToLive)
        {
            if (timeToLive.HasValue)
            {
                throw new NotImplementedException();
            }

            if (visibilityDelay.HasValue)
            {
                return PutAsync(message, visibilityDelay.Value);
            }
            else
            {
                return PutAsync(message);
            }
        }

        private string CreateQueueItemId(TimeSpan leaseTime)
        {
            string timeStamp = (DateTime.UtcNow + leaseTime).ToString("O");
            return string.Format("{0}_{1}", timeStamp, Guid.NewGuid());
        }

        public async Task<Storable<T>> UpdateMessageAsync(Storable<T> message, TimeSpan leaseTime,
            MessageUpdateFlags flags)
        {
            ThrowOnCrudIfSpecified();
            var temp = SerializationHelpers.DeserializeEntity<Storable<T>>(SerializationHelpers.SerializeEntity(message));

            MockQueueEntry mockQueueEntry;
            if (Queue.TryGetValue(temp.Id, out mockQueueEntry))
            {
                if (mockQueueEntry.Etag == temp.Etag)
                {
                    temp.Etag = DateTime.Now.Ticks.ToString(CultureInfo.InvariantCulture);
                    string newSerializedMessage = SerializationHelpers.SerializeEntity(temp);
                    var messageUpdate = new MockQueueEntry(newSerializedMessage, temp.Etag, leaseTime);
                    if (Queue.TryUpdate(temp.Id, messageUpdate, mockQueueEntry))
                    {
                        return await Task.FromResult(temp);
                    }
                }
            }

            throw new StorageException(res: new RequestResult() { HttpStatusCode = 404 }, message: string.Format("Message {0} not found", temp.Id), inner: null);
        }

        public async Task<bool> DeleteAsync(Storable<T> message)
        {
            ThrowOnCrudIfSpecified();
            return await DeleteAsync(message.Id, message.Etag);
        }

        public async Task<bool> DeleteAsync(string messageId, string etag)
        {
            ThrowOnCrudIfSpecified();
            MockQueueEntry mockQueueEntry;
            if (Queue.TryGetValue(messageId, out mockQueueEntry))
            {
                if (mockQueueEntry.Etag == etag)
                {
                    Trace.WriteLine(string.Format("Deleting {0} {1} {2}", messageId, mockQueueEntry.Etag, mockQueueEntry.Message));
                    MockQueueEntry removedMessage;
                    return Queue.TryRemove(messageId, out removedMessage);
                }
                else 
                {
                    Trace.WriteLine(string.Format("Can't delete, wrong etag. MessageId: {0} ClaimedEtag: {1} StoredEtag: {2}  {3}", messageId, etag, mockQueueEntry.Etag, mockQueueEntry.Message));
                }
            }
            else
            {
                Trace.WriteLine(string.Format("Can't delete, message not found. MessageId: {0} ClaimedEtag: {1}", messageId, etag));
            }

            return await Task.FromResult(false);
        }

        public int GetDequeueCount(Storable<T> message)
        {
            return 0;
        }

        public TimeSpan GetMessageLifetime(Storable<T> message)
        {
            return TimeSpan.Zero;
        }

        public void Clear()
        {
            Queue.Clear();
        }

        public async Task<int> GetQueueLengthAsync()
        {
            return await Task.FromResult(Queue.Keys.Count);
        }

        public async Task<IEnumerable<string>> ListAllQueueNamesAsync(string prefix)
        {
            return await Task.FromResult(new List<string>());
        }

        private Func<Exception> _throwOnCrudFactory;
        private int _throwOnCrudRemaining;

        private void ThrowOnCrudIfSpecified()
        {
            if (_throwOnCrudFactory != null &&
                Interlocked.Decrement(ref _throwOnCrudRemaining) >= 0)
            {
                throw _throwOnCrudFactory();
            }
        }
    }
}
