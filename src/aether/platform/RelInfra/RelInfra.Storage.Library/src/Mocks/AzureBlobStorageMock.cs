﻿using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.RelInfra.Common.RetryPolicy;
using Microsoft.RelInfra.Common.Serialization;
using Microsoft.RelInfra.Storage.Exceptions;

namespace Microsoft.RelInfra.Storage.Test.Mocks
{
    public class AzureBlobStorageMock : IAzureBlobStorage, IRelInfraStreamableStorage
    {
        private readonly ConcurrentDictionary<string, Storable<string>> _storage = new ConcurrentDictionary<string, Storable<string>>();
        private readonly ConcurrentDictionary<string, Dictionary<string, string>> _metadata = new ConcurrentDictionary<string, Dictionary<string, string>>();
        
        public IRelInfraRetryPolicy RetryPolicy { get; set; }

        private readonly object _leaseSync = new object();

        public Task<bool> ExistsAsync(string containerName, string resourceId)
        {
            string key = GetStorageKey(containerName, resourceId);
            return Task.FromResult(_storage.ContainsKey(key));
        }

        public async Task<Storable<T>> AddAsync<T>(string containerName, string resourceId, Storable<T> resource)
        {
            string key = GetStorageKey(containerName, resourceId);

            await Task.Run(() =>
            {
                lock (_leaseSync)
                {
                    if (_storage.ContainsKey(key))
                    {
                        throw new RelInfraStorageException(StorageError.Conflict);
                    }
                    // azure blob storage generates Etag, so do the same in mock
                    resource.Etag = Guid.NewGuid().ToString();
                    _storage.TryAdd(key, SerializeEntityFromStorage(resource, containerName, resourceId));
                }
            });
            return resource;
        }

        public async Task<bool> TryPutAndIgnoreEtagAsync<T>(string containerName, string resourceId, Storable<T> resource)
        {
            string key = GetStorageKey(containerName, resourceId);

            return await Task.Run(() =>
            {
                // azure blob storage generates Etag, so do the same in mock
                resource.Etag = Guid.NewGuid().ToString();
                lock (_leaseSync)
                {
                    _storage[key] = SerializeEntityFromStorage(resource, containerName, resourceId);
                }

                return true;
            });
        }

        public async Task<Storable<T>> PutAndIgnoreEtagAsync<T>(string containerName, string resourceId, Storable<T> resource)
        {
            return await PutAndIgnoreEtagAsync(containerName, resourceId, resource, CancellationToken.None);
        }

        public async Task<Storable<T>> PutAndIgnoreEtagAsync<T>(string containerName, string resourceId, Storable<T> resource, CancellationToken cancellationToken)
        {
            if (await TryPutAndIgnoreEtagAsync(containerName, resourceId, resource))
            {
                return resource;
            }
            return null;
        }

        public Task<Storable<T>> PutWithETagAsync<T>(string containerName, string resourceId, Storable<T> resource)
        {
            string key = GetStorageKey(containerName, resourceId);
            lock (_leaseSync)
            {
                if (_storage.ContainsKey(key))
                {
                    if (_storage[key].Etag != resource.Etag && resource.Etag != "*" && resource.Etag != null)
                    {
                        throw new RelInfraStorageException(StorageError.PreconditionFailed);
                    }
                }
                resource.Etag = Guid.NewGuid().ToString();
                _storage[key] = SerializeEntityFromStorage(resource, containerName, resourceId);
                return Task.FromResult(resource);
            }
        }

        public Task<bool> TryBatchPutAsync<T>(string containerName, IEnumerable<Storable<T>> resources)
        {
            throw new NotImplementedException();
        }

        public Task<IEnumerable<Storable<T>>> BatchPutAsync<T>(string containerName, IEnumerable<Storable<T>> resources)
        {
            throw new NotImplementedException();
        }

        public Task<Storable<T>> TryGetAsync<T>(string containerName, string resourceId)
        {
            return TryGetAsync<T>(containerName, resourceId, CancellationToken.None);
        }

        public Task<Storable<T>> TryGetAsync<T>(string containerName, string resourceId, CancellationToken cancellationToken)
        {
            string key = GetStorageKey(containerName, resourceId);

            lock (_leaseSync)
            {
                return Task.FromResult(_storage.ContainsKey(key) ? DeserializeEntityFromStorage<T>(_storage[key]) : null);
            }
        }

        public async Task<Storable<T>> GetAsync<T>(string containerName, string resourceId)
        {
            return await GetAsync<T>(containerName, resourceId, CancellationToken.None);
        }

        public async Task<Storable<T>> GetAsync<T>(string containerName, string resourceId, CancellationToken cancellationToken)
        {
            Storable<T> storable = await TryGetAsync<T>(containerName, resourceId);
            if (storable == null)
            {
                string message = $"{typeof(T).Name} with {resourceId} not found in {containerName}";
                throw new RelInfraStorageException(message, StorageError.NotFound);
            }
            return storable;
        }

        public async Task<IEnumerable<Storable<T>>> BatchGetAsync<T>(string containerName, IEnumerable<string> resourceIds)
        {
            return await Task.WhenAll(resourceIds.Select(id => GetAsync<T>(containerName, id)));
        }

        public Task<bool> TryUpdateAsync<T>(string containerName, string resourceId, Storable<T> resource)
        {
            string key = GetStorageKey(containerName, resourceId);

            lock (_leaseSync)
            {
                if (resource.Etag != "*" && _storage[key].Etag != resource.Etag)
                {
                    return Task.FromResult(false);
                }
                _storage[key] = SerializeEntityFromStorage(resource, containerName, resourceId);
                return Task.FromResult(true);
            }
        }

        public async Task<Storable<T>> UpdateAsync<T>(string containerName, string resourceId, Storable<T> resource)
        {
            if (await TryUpdateAsync(containerName, resourceId, resource))
            {
                return resource;
            }
            throw new RelInfraStorageException(StorageError.PreconditionFailed);
        }

        public async Task<IEnumerable<Storable<T>>> BatchUpdateAsync<T>(string containerName, IEnumerable<Storable<T>> resources)
        {
            return await Task.WhenAll(resources.Select(res => UpdateAsync(containerName, res.Id, res)));
        }

        public Task<bool> TryDeleteAsync(string containerName, string resourceId)
        {
            lock (_leaseSync)
            {
                Storable<string> storable;
                return Task.FromResult(_storage.TryRemove(GetStorageKey(containerName, resourceId), out storable));
            }
        }

        public Task InitializeAsync(string containerName, string blobName)
        {
            throw new NotImplementedException();
        }

        public Task DeleteAsync(string containerName, string resourceId)
        {
            lock (_leaseSync)
            {
                Storable<string> storable;
                _storage.TryRemove(GetStorageKey(containerName, resourceId), out storable);
                return Task.FromResult(true);
            }
        }

        public Task DeleteAsync(string containerName, string resourceId, string etag)
        {
            lock (_leaseSync)
            {
                Storable<string> storable;
                _storage.TryRemove(GetStorageKey(containerName, resourceId), out storable);
                return Task.FromResult(true);
            }
        }

        public async Task BatchDeleteAsync(string containerName, IEnumerable<string> resourceIds)
        {
            await Task.WhenAll(resourceIds.Select(id => DeleteAsync(containerName, id)));
        }

        private string GetStorageKey(string containerName, string resourceId)
        {
            return $"{containerName}/{resourceId}";
        }

        public Task<Stream> GetStreamAsync(string containerName, string resourceId)
        {
            if (String.IsNullOrWhiteSpace(containerName))
            {
                throw new ArgumentException("containerName can't be null or empty", nameof(containerName));
            }

            if (String.IsNullOrWhiteSpace(resourceId))
            {
                throw new ArgumentException("resourceId can't be null or empty", nameof(resourceId));
            }

            string key = GetStorageKey(containerName, resourceId);
            lock (_leaseSync)
            {
                return Task.FromResult(_storage.ContainsKey(key) ? (Stream)new MemoryStream(Encoding.UTF8.GetBytes(_storage[key].Entity)) : null);
            }
        }

        public Task UploadStreamAsync(string containerName, string resourceId, Stream stream)
        {
            return TryUploadStreamAsync(containerName, resourceId, stream);
        }

        public virtual Task<bool> TryUploadStreamAsync(string containerName, string resourceId, Stream stream)
        {
            if (String.IsNullOrWhiteSpace(containerName))
            {
                throw new ArgumentException("containerName can't be null or empty", nameof(containerName));
            }

            if (String.IsNullOrWhiteSpace(resourceId))
            {
                throw new ArgumentException("resourceId can't be null or empty", nameof(resourceId));
            }

            string key = GetStorageKey(containerName, resourceId);

            lock (_leaseSync)
            {
                if (_storage.ContainsKey(key))
                {
                    return Task.FromResult(false);
                }

                var sr = new StreamReader(stream);
                _storage.TryAdd(key, new Storable<string>(sr.ReadToEnd()));

                return Task.FromResult(true);
            }
        }

        public Task AppendStreamAsync(string containerName, string resourceId, Stream dataStream)
        {
            if (String.IsNullOrWhiteSpace(containerName))
            {
                throw new ArgumentException("containerName can't be null or empty", nameof(containerName));
            }

            if (String.IsNullOrWhiteSpace(resourceId))
            {
                throw new ArgumentException("resourceId can't be null or empty", nameof(resourceId));
            }

            string key = GetStorageKey(containerName, resourceId);

            lock (_leaseSync)
            {
                var sr = new StreamReader(dataStream);

                if (_storage.ContainsKey(key))
                {
                    _storage[key].Entity += sr.ReadToEnd();
                }
                else
                {
                    _storage.TryAdd(key, new Storable<string>(sr.ReadToEnd()));
                }

                return Task.FromResult(true);
            }
        }

        public Task InitializeAsync(string containerName)
        {
            return Task.Delay(0);
        }

        public Task<Storable<string>> AppendAsync<T>(string containerName, string resourceId, T resource)
        {
            string key = GetStorageKey(containerName, resourceId);
            Storable<string> newValue;

            lock (_leaseSync)
            {
                if (_storage.ContainsKey(key))
                {
                    newValue = new Storable<string>(_storage[key].Entity);
                    newValue.Entity += resource.ToString();
                    _storage.TryUpdate(key, newValue, _storage[key]);
                }
                else
                {
                    newValue = new Storable<string>(resource.ToString());
                    _storage.TryAdd(key, newValue);
                }
            }
            return Task.FromResult(newValue);
        }

        public async Task<Storable<T>> GetBlobEntityAsync<T>(string containerName, string resourceId)
        {
            return await GetBlobEntityAsync<T>(containerName, resourceId, CancellationToken.None);
        }

        public async Task<Storable<T>> GetBlobEntityAsync<T>(string containerName, string resourceId, CancellationToken cancellationToken)
        {
            Storable<string> blobStringContent = await GetBlobStringAsync(containerName: containerName, resourceId: resourceId, cancellationToken: cancellationToken);
            return new Storable<T>
            {
                Id = blobStringContent.Id,
                ContainerId = blobStringContent.ContainerId,
                Etag = blobStringContent.Etag,
                Entity = SerializationHelpers.DeserializeEntity<T>(blobStringContent.Entity)
            };
        }

        public Task<Storable<string>> GetBlobStringAsync(string containerName, string resourceId)
        {
            return GetBlobStringAsync(containerName, resourceId, CancellationToken.None);
        }

        public Task<Storable<string>> GetBlobStringAsync(string containerName, string resourceId, CancellationToken cancellationToken)
        {
            string key = GetStorageKey(containerName, resourceId);
            lock (_leaseSync)
            {
                if (!_storage.ContainsKey(key))
                {
                    throw new RelInfraStorageException(error: StorageError.NotFound);
                }

                return Task.FromResult(new Storable<string>
                {
                    Id = resourceId,
                    ContainerId = containerName,
                    Etag = _storage[key].Etag,
                    Entity = _storage[key].Entity,
                });
            }
        }

        public Uri GetBlobUri(string containerName, string blobName, DateTimeOffset? expiryDate = null)
        {
            return new Uri("http://azureblobmock.com/" + GetStorageKey(containerName, blobName));
        }

        public Uri GetContainerReadUri(string containerName, DateTimeOffset? expiryDate = null)
        {
            return new Uri("http://azureblobmock.com/" + GetStorageKey(containerName, resourceId: ""));
        }

        public Uri GetWritableBlobUri(string containerName, string blobName, DateTimeOffset expiryDate)
        {
            return new Uri("http://azureblobmock.com/" + GetStorageKey(containerName, blobName));
        }

        public async Task<bool> TryUploadStreamWithTypeAsync(string containerName, string resourceId, Stream stream, string contentType)
        {
            return await TryUploadStreamAsync(containerName, resourceId, stream);
        }

        public Task<IEnumerable<string>> ListDirectoryAsync(string containerName, string path, int fileCountLimit, CancellationToken cancellationToken, string prefix)
        {
            lock (_leaseSync)
            {
                string rootPath = GetStorageKey(containerName, path);
                return Task.FromResult(_storage.Keys.Where(key => key.StartsWith(rootPath)).Where(key => prefix == null || key.StartsWith(prefix)).Take(fileCountLimit));
            }
        }

        public Task<QueryResults> ListDirectoryWithContinuationTokenAsync(string containerName, string path, string continuationToken, int takeCount, CancellationToken cancellationToken, bool useFlatBlobListing = true)
        {
            lock (_leaseSync)
            {
                string rootPath = GetStorageKey(containerName, path);
                int skip = String.IsNullOrEmpty(continuationToken) ? 0 : Int32.Parse(continuationToken);
                List<string> results = _storage.Keys.Where(key => key.StartsWith(rootPath)).Skip(skip).Take(takeCount).ToList();
                continuationToken = results.Count < takeCount ? null : (skip + takeCount).ToString();
                return Task.FromResult(new QueryResults(continuationToken: continuationToken, results: results));
            }
        }

        public Task DownloadToFileAsync(string containerName, string blobName, string fileLocation, CancellationToken token)
        {
            return Task.FromResult(Type.Missing);
        }

        public Task UploadFileAsync(string containerName, string blobName, string fileLocation, CancellationToken token)
        {
            return Task.FromResult(Type.Missing);
        }

        public Task<long> GetBlobSizeAsync(string containerName, string resourceId)
        {
            if (String.IsNullOrWhiteSpace(containerName))
            {
                throw new ArgumentException("containerName can't be null or empty", nameof(containerName));
            }

            if (String.IsNullOrWhiteSpace(resourceId))
            {
                throw new ArgumentException("resourceId can't be null or empty", nameof(resourceId));
            }

            string key = GetStorageKey(containerName, resourceId);
            lock (_leaseSync)
            {
                return Task.FromResult(_storage.ContainsKey(key) ? _storage[key].Entity.Length : 0L);
            }
        }

        private Storable<string> SerializeEntityFromStorage<T>(Storable<T> entity, string containerName, string resourceId)
        {
            return new Storable<string>
            {
                Id = resourceId,
                ContainerId = containerName,
                Etag = entity.Etag,
                Entity = SerializationHelpers.SerializeEntity(entity.Entity)
            };
        }

        private Storable<T> DeserializeEntityFromStorage<T>(Storable<string> entityFromStorage)
        {
            return new Storable<T>
            {
                Id = entityFromStorage.Id,
                ContainerId = entityFromStorage.ContainerId,
                Etag = entityFromStorage.Etag,
                Entity = SerializationHelpers.DeserializeEntity<T>(entityFromStorage.Entity)
            };
        }

        public async Task SetBlobMetadataAsync(string containerName, string resourceId, string metadataKey, string metadataValue)
        {
            string key = GetStorageKey(containerName, resourceId);

            await Task.Run(() =>
            {
                lock (_leaseSync)
                {
                    if (!_storage.ContainsKey(key))
                    {
                        throw new RelInfraStorageException(StorageError.NotFound);
                    }

                    if (!_metadata.ContainsKey(key))
                    {
                        _metadata[key] = new Dictionary<string, string>();
                    }

                    _metadata[key].Add(metadataKey, metadataValue);
                }
            });
        }

        public Task<string> GetBlobMetadataAsync(string containerName, string resourceId, string metadataKey)
        {
            string key = GetStorageKey(containerName, resourceId);

            lock (_leaseSync)
            {
                if (!_storage.ContainsKey(key))
                {
                    throw new RelInfraStorageException(StorageError.NotFound);
                }

                if (!_metadata.ContainsKey(key))
                {
                    _metadata[key] = new Dictionary<string, string>();
                }

                return Task.FromResult(_metadata[key].ContainsKey(metadataKey) ? _metadata[key][metadataKey] : null);
            }
        }

        public Task RenewLeaseAsync(string containerName, string blobName, string leaseId, CancellationToken cancellation = default(CancellationToken))
        {
            throw new NotImplementedException();
        }

        public Task<string> AcquireLeaseAsync(string containerName, string blobName, TimeSpan? leaseTime, string proposedLeaseId, CancellationToken cancellationToken = default(CancellationToken))
        {
            throw new NotImplementedException();
        }

        public Task<bool> ResourceExistsAsync(string containerName, string blobName)
        {
            throw new NotImplementedException();
        }

        public Task ReleaseLeaseAsync(string containerName, string blobName, string leaseId, CancellationToken cancellationToken = default(CancellationToken))
        {
            throw new NotImplementedException();
        }

        public Uri GetContainerWritableUri(string containerName, DateTimeOffset? expiryDate = null)
        {
            return new Uri("http://azureblobmock.com/" + GetStorageKey(containerName, string.Empty));
        }

        public Task DeleteContainerAsync(string containerName)
        {
            return Task.FromResult(0);
        }
    }
}
