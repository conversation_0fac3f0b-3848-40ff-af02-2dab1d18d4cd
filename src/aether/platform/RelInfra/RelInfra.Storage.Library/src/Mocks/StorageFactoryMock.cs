﻿using System;
using System.Collections.Generic;

namespace Microsoft.RelInfra.Storage.Test.Mocks
{
    /// <summary>
    ///    Avoid this class, it generates storages sharing static states among them
    /// </summary>
    public class StorageFactoryStaticMock : IStorageFactory
    {
        private readonly bool _reuseContainers;
        private readonly Dictionary<KeyValuePair<StorageTypes, string>, IStorage> _storages;
        private readonly Dictionary<ITableStorage, object> _storageQueues;
        private object _noStorageQueue;

        public StorageFactoryStaticMock(bool reuseContainers = false)
        {
            _reuseContainers = reuseContainers;
            _storages = new Dictionary<KeyValuePair<StorageTypes, string>, IStorage>();
            _storageQueues = new Dictionary<ITableStorage, object>();
        }

        public IStorage GetStorage(StorageTypes storageType)
        {
            return GetStorage(storageType, String.Empty);
        }

        public IStorage GetStorage(StorageTypes storageType, string storageName, string connectionString)
        {
            return GetStorage(storageType, storageName);
        }

        public IStorage GetStorage(StorageTypes storageType, string storageName)
        {
            var key = new KeyValuePair<StorageTypes, string>(storageType, storageName);
            if (_reuseContainers && _storages.ContainsKey(key))
            {
                return _storages[key];
            }

            IStorage storage = null;
            switch (storageType)
            {
                case StorageTypes.AzureBlob:
                    storage = new AzureBlobStorageMock();
                    break;
                case StorageTypes.AzureTables:
                    storage = new AzureTableStorageStaticMock(storageName);
                    break;
                default:
                    throw new ArgumentOutOfRangeException("storageType");
            }
            if (_reuseContainers)
            {
                _storages[key] = storage;
            }

            return storage;
        }

        public IQueue<T> GetQueue<T>(string queueName)
        {
            if (_reuseContainers)
            {
                if (_noStorageQueue != null)
                {
                    return (IQueue<T>)_noStorageQueue;
                }
            }
            var queue = new QueueMock<T>(queueName);
            if (_reuseContainers)
            {
                _noStorageQueue = queue;
            }
            return queue;
        }
    }
}
