using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography.X509Certificates;
using System.Threading;
using System.Threading.Tasks;

namespace Microsoft.RelInfra.Storage.Mocks.Cosmos
{
    public class MockCosmosStorage : MockFileStorage, ICosmosStorage
    {
        public void Setup( X509Certificate2 certificate)
        {
        }

        public Task<string[]> GetVcAccessSecurityGroupAsync(string vcName, bool excludeCertificates = true)
        {
            throw new NotImplementedException();
        }

        public Task<IEnumerable<AetherStreamInfo>> ListDirectoryInfoAsync(string path, bool recursive, uint streamCountLimit = 20000)
        {
            if (!Directories.ContainsKey(path))
            {
                throw new KeyNotFoundException();
            }

            if (Directories[path].Count > streamCountLimit)
            {
                throw new ArgumentException($"Folder is too big. it contains more the {streamCountLimit} streams.");
            }

            return Task.FromResult(Directories[path].AsEnumerable());
        }

        public Task<AetherStreamInfo> GetStreamInfoAsync(string path)
        {
            if (Streams.ContainsKey(path))
            {
                return Task.FromResult(Streams[path]);
            }
            throw new KeyNotFoundException();
        }

        public Task DownloadFileAsync(string cosmosFilePath, string localFilePath, bool overWriteIfExists)
        {
            throw new NotImplementedException();
        }

        public Task<bool> UploadFileAsync(string sourceFile, string destinationStream, bool respectLineBoundary, TimeSpan expirationTime, bool overwriteIfExists, CancellationToken cancellationToken)
        {
            throw new NotImplementedException();
        }

        public Task ConcatenateStreamAsync(string sourcePath, string destPath, CancellationToken token)
        {
            throw new NotImplementedException();
        }

        public async Task<IEnumerable<AetherStreamInfo>> GetStreamSetInfoAsync(string path, bool sparseStreamSet)
        {
            return await Task.FromResult(new List<AetherStreamInfo>());
        }

        public async Task<bool> SetStreamExpirationTimeAsync(string streamPath, TimeSpan expirationTime)
        {
            return await Task.FromResult(true);
        }
    }
}
