﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Xml;
using Microsoft.RelInfra.Common.RetryPolicy;
using Microsoft.RelInfra.Instrumentation;
using Microsoft.RelInfra.Instrumentation.Exceptions;
using Microsoft.RelInfra.Instrumentation.Logging;
using Microsoft.RelInfra.Storage.Exceptions;
using Microsoft.RelInfra.Storage.Utils;
using Microsoft.WindowsAzure.Storage;
using Microsoft.WindowsAzure.Storage.RetryPolicies;
using Microsoft.WindowsAzure.Storage.Shared.Protocol;
using Microsoft.WindowsAzure.Storage.Table;

namespace Microsoft.RelInfra.Storage
{
    //  TODO <PERSON>ez add dedicated counters for etag mismatch

    public class AzureTableStorage : ITableStorage
    {
        // Based on https://docs.microsoft.com/en-us/rest/api/storageservices/Understanding-the-Table-Service-Data-Model?redirectedfrom=MSDN#characters-disallowed-in-key-fields
        public static readonly Regex DisallowedCharsInTableKeys = new Regex(@"[\\\\#%+/?\u0000-\u001F\u007F-\u009F]");

        // azure tables has limit on number of entities in batch, so we do batches in size of 100
        const short AzureBatchLimit = 100;

        private readonly CloudTableClient _tableClient;
        private readonly CloudTable _table;
        private readonly OptionalCounters _counters;
        
        private IRelInfraRetryPolicy _retryPolicy;
        public IRelInfraRetryPolicy RetryPolicy
        {
            get => _retryPolicy;
            set
            {
                _retryPolicy = value;
                _tableClient.DefaultRequestOptions.RetryPolicy = new AzureRetryPolicy(value, new ExponentialRetry(), additionalInfo: $"{_table.Name}");
            }
        }

        public IRetryPolicy AzureRetryPolicy
        {
            get => _tableClient.DefaultRequestOptions.RetryPolicy;
            set => _tableClient.DefaultRequestOptions.RetryPolicy = value;
        }

        public string TableName => _table.Name;

        public AzureTableStorage(string connectionString, string tableName, CounterManager counterManager, double defaultMaxExecutionTimeInSeconds = 180)
            : this(CloudStorageAccount.Parse(connectionString), tableName, counterManager, defaultMaxExecutionTimeInSeconds)
        {
        }

        public AzureTableStorage(CloudStorageAccount storageAccount, string tableName, CounterManager counterManager, double defaultMaxExecutionTimeInSeconds = 180)
        {
            _tableClient = storageAccount.CreateCloudTableClient();
            _tableClient.DefaultRequestOptions.MaximumExecutionTime = TimeSpan.FromSeconds(defaultMaxExecutionTimeInSeconds);
            _counters = new OptionalCounters(counterManager, tableName);

            _table = _tableClient.GetTableReference(tableName);

            CommonLogger.LogEntityInfo(nameof(AzureTableStorage), $"Construct client with connection string. AccountName: [{storageAccount.Credentials?.AccountName}]. TableName: [{tableName}].");
        }

        public async Task<bool> TableExistsAsync()
        {
            return await _table.ExistsAsync();
        }

        public async Task CreateIfNotExistsAsync()
        {
            _counters.IncrementRateCounter("AzureTable CreateIfNotExistsRate");
            using (_counters.CreateDisposableTimer("AzureTable CreateIfNotExistsLatency"))
            {
                try
                {
                    await _table.CreateIfNotExistsAsync();
                }
                catch (StorageException ex)
                {
                    _counters.IncrementRateCounter("AzureTable CreateIfNotExistsFailureRate");
                    string log = $"Failed to Check/Create table: {_table.Name}";
                    throw new RelInfraStorageException(log, ex, StorageErrorHelper.GetError(ex.RequestInformation.HttpStatusCode)).Format();
                }
            }
        }

        public async Task InitializeAsync(string containerName)
        {
            await CreateIfNotExistsAsync();
        }

        public async Task DeleteIfExistsAsync()
        {
            _counters.IncrementRateCounter("AzureTable DeleteIfExistsRate");
            using (_counters.CreateDisposableTimer("AzureTable DeleteIfExistsLatency"))
            {
                try
                {
                    await _table.DeleteIfExistsAsync();
                }
                catch (StorageException ex)
                {
                    _counters.IncrementRateCounter("AzureTable DeleteIfExistsFailureRate");
                    string log = $"Failed to delete table: {_table.Name}";
                    throw new RelInfraStorageException(log, ex, StorageErrorHelper.GetError(ex.RequestInformation.HttpStatusCode)).Format();
                }
            }
        }

        public async Task<bool> ExistsAsync(string containerName, string resourceId)
        {
            _counters.IncrementRateCounter("AzureTable ExistsRate");
            using (_counters.CreateDisposableTimer("AzureTable ExistsLatency"))
            {
                if (String.IsNullOrWhiteSpace(containerName))
                {
                    _counters.IncrementRateCounter("AzureTable LibraryExceptionRate");
                    throw new ArgumentException("containerName can't be null or empty", nameof(containerName));
                }

                if (String.IsNullOrWhiteSpace(resourceId))
                {
                    _counters.IncrementRateCounter("AzureTable LibraryExceptionRate");
                    throw new ArgumentException("resourceId can't be null or empty", nameof(resourceId));
                }

                try
                {
                    var retrieveOperation = TableOperation.Retrieve(containerName, resourceId);
                    TableResult result = await _table.ExecuteAsync(retrieveOperation);

                    return result.HttpStatusCode == (int) HttpStatusCode.OK;
                }
                catch (StorageException ex)
                {
                    _counters.IncrementRateCounter("AzureTable ExistsFailureRate");
                    string log =
                        $"Could not get entity with partKey={containerName} and rowKey={resourceId} from azure table {_table.Name}";
                    Trace.TraceError($"{log}, ex: {ex}");

                    throw new RelInfraStorageException(log, ex, StorageErrorHelper.GetError(ex.RequestInformation.HttpStatusCode)).Format();
                }
            }
        }

        public async Task<Storable<T>> AddAsync<T>(string containerName, string resourceId, Storable<T> resource)
        {
            _counters.IncrementRateCounter("AzureTable AddRate");
            using (_counters.CreateDisposableTimer("AzureTable AddLatency"))
            {
                if (String.IsNullOrWhiteSpace(containerName))
                {
                    _counters.IncrementRateCounter("AzureTable LibraryExceptionRate");
                    throw new ArgumentException("containerName can't be null or empty", nameof(containerName));
                }

                if (String.IsNullOrWhiteSpace(resourceId))
                {
                    _counters.IncrementRateCounter("AzureTable LibraryExceptionRate");
                    throw new ArgumentException("resourceId can't be null or empty", nameof(resourceId));
                }

                try
                {
                    TableOperation insertOperation = 
                        TableOperation.Insert((new AzureTableEntity<T>(containerName, resourceId, resource.Entity, resource.Name)).GetTableEntity());

                    TableResult result = await _table.ExecuteAsync(insertOperation);

                    resource.Id = resourceId;
                    resource.Etag = result.Etag;

                    return CreateStorable<T>(resourceId, containerName, result);
                }
                catch (StorageException ex)
                {
                    _counters.IncrementRateCounter("AzureTable AddFailureRate");
                    string log = $"Could not write entity with partKey={containerName} and rowKey={resourceId} to azure table {_table.Name}";
                    Trace.TraceError($"{log}, ex: {ex}");
                    throw new RelInfraStorageException(log, ex, StorageErrorHelper.GetError(ex.RequestInformation.HttpStatusCode)).Format();
                }
            }
        }

        public async Task<bool> TryPutAndIgnoreEtagAsync<T>(string containerName, string resourceId, Storable<T> resource)
        {
            try
            {
                await PutAndIgnoreEtagAsync(containerName, resourceId, resource);
                return true;
            }
            catch (RelInfraStorageException)
            {
                return false;
            }
        }

        public async Task<Storable<T>> PutAndIgnoreEtagAsync<T>(string containerName, string resourceId, Storable<T> resource)
        {
            _counters.IncrementRateCounter("AzureTable PutRate");
            using (_counters.CreateDisposableTimer("AzureTable PutLatency"))
            {
                if (String.IsNullOrWhiteSpace(containerName))
                {
                    _counters.IncrementRateCounter("AzureTable LibraryExceptionRate");
                    throw new ArgumentException("containerName can't be null or empty", nameof(containerName));
                }

                if (String.IsNullOrWhiteSpace(resourceId))
                {
                    _counters.IncrementRateCounter("AzureTable LibraryExceptionRate");
                    throw new ArgumentException("resourceId can't be null or empty", nameof(resourceId));
                }

                try
                {
                    TableOperation insertOrMergeOperation =
                        TableOperation.InsertOrMerge((new AzureTableEntity<T>(containerName, resourceId, resource.Entity, resource.Name)).GetTableEntity());

                    TableResult result = await _table.ExecuteAsync(insertOrMergeOperation);

                    resource.Id = resourceId;
                    resource.Etag = result.Etag;

                    return CreateStorable<T>(resourceId, containerName, result);
                }
                catch (StorageException ex)
                {
                    _counters.IncrementRateCounter("AzureTable PutFailureRate");
                    string log =
                        $"Could not write entity with partKey={containerName} and rowKey={resourceId} to azure table {_table.Name}";
                    Trace.TraceError($"{log}, ex: {ex}");

                    throw new RelInfraStorageException(log, ex, StorageErrorHelper.GetError(ex.RequestInformation.HttpStatusCode)).Format();
                }
            }
        }

        public async Task<bool> TryBatchPutAsync<T>(string containerName, IEnumerable<Storable<T>> resources)
        {
            try
            {
                await BatchPutAsync<T>(containerName, resources);
            }
            catch (RelInfraStorageException)
            {
                return false;
            }

            return true;
        }

        public async Task<IEnumerable<Storable<T>>> BatchPutAsync<T>(string containerName, IEnumerable<Storable<T>> resources)
        {
            _counters.IncrementRateCounter("AzureTable BatchPutRate");
            using (_counters.CreateDisposableTimer("AzureTable BatchPutLatency"))
            {
                try
                {
                    var results = new List<Storable<T>>();
                    foreach (IEnumerable<Storable<T>> listToProcess in resources.Chunk(AzureBatchLimit))
                    {
                        // TODO: try to avoid using .ToList() in this case
                        List<Storable<T>> entityList = listToProcess.ToList();
                        IEnumerable<Storable<T>> batchResults = await BatchPutAsyncInternal<T>(containerName, entityList);
                        if (batchResults == null)
                        {
                            string log = $"Batch insertion partKey={containerName} to azure table {_table.Name} fails: operation returned null";
                            throw new RelInfraStorageException(log).Format();
                        }
                        results.AddRange(batchResults);
                    }

                    _counters.SetNumberCounter("AzureTable EntitiesPerBatchPut", results.Count);
                    return results;
                }
                catch (RelInfraStorageException)
                {
                    _counters.IncrementRateCounter("AzureTable BatchPutFailureRate");
                    throw;
                }
            }
        }

        internal static IEnumerable<IEnumerable<Storable<T>>> BatchStorables<T>(IEnumerable<Storable<T>> storables, int batchSize)
        {
            // group by partition key
            foreach (IGrouping<string, Storable<T>> group in storables.GroupBy(s => s.ContainerId))
            {
                // within each group, produce batches
                foreach (IEnumerable<Storable<T>> batch in group.Chunk(batchSize))
                {
                    yield return batch;
                }
            }
        } 

        private async Task<IEnumerable<Storable<T>>> BatchPutAsyncInternal<T>(string containerName, IList<Storable<T>> resources)
        {
            if (String.IsNullOrWhiteSpace(containerName))
            {
                _counters.IncrementRateCounter("AzureTable LibraryExceptionRate");
                throw new ArgumentException("containerName can't be null or empty", nameof(containerName));
            }

            bool needDivide = false;
            var results = new List<Storable<T>>();

            try
            {
                TableBatchOperation batchOperation = new TableBatchOperation();

                foreach (var resource in resources)
                {
                    batchOperation.InsertOrMerge((new AzureTableEntity<T>(containerName, resource.Id, resource.Entity, resource.Name)).GetTableEntity());
                }

                IList<TableResult> tableResults = await _table.ExecuteBatchAsync(batchOperation);
                if (tableResults == null)
                {
                    return null;
                }

                foreach (var result in resources.Join(
                    tableResults,
                    res => res.Id,
                    tr => ((DynamicTableEntity)tr.Result).RowKey,
                    (storable, result) => new { Resource = storable, ETag = result.Etag }))
                {
                    result.Resource.Etag = result.ETag;
                    results.Add(result.Resource);
                }

            }
            catch (StorageException ex)
            {
                string log;

                if (IsInvalidInputErrorCode(ex.RequestInformation)
                    && resources.Count() != resources.GroupBy(res => res.Id).Count())
                {
                    log = $"Batch insertion partKey={containerName} to azure table {_table.Name} failed: resource ids for batch operation must be unique, dupes are [{String.Join(", ", resources.GroupBy(res => res.Id).Where(group => @group.Count() > 1).Select(group => @group.Key))}]";
                }
                else
                {
                    log =
                        $"Could not do batch insertion partKey={containerName} to azure table {_table.Name}, ex: {ex}";
                }
                Trace.TraceError(log);
                if (IsRequestBodyTooLargeErrorCode(ex.RequestInformation) && resources.Count > 1)
                {
                    needDivide = true;
                }
                else
                {
                    throw new RelInfraStorageException(log, ex, StorageErrorHelper.GetError(ex.RequestInformation.HttpStatusCode)).Format();
                }
            }

            if (needDivide)
            {
                foreach (IEnumerable<Storable<T>> halfBatch in resources.Chunk(resources.Count / 2))
                {
                    results.AddRange(await BatchPutAsyncInternal(containerName, halfBatch.ToList()));
                }
            }

            return results;
        }

        public async Task<Storable<T>> TryGetAsync<T>(string containerName, string resourceId)
        {
            try
            {
                return await GetAsync<T>(containerName, resourceId);
            }
            catch (RelInfraStorageException)
            {
                return null;
            }
        }

        public async Task<Storable<T>> GetAsync<T>(string containerName, string resourceId)
        {
            _counters.IncrementRateCounter("AzureTable GetRate");
            using (_counters.CreateDisposableTimer("AzureTable GetLatency"))
            {

                if (String.IsNullOrWhiteSpace(containerName))
                {
                    _counters.IncrementRateCounter("AzureTable LibraryExceptionRate");
                    throw new ArgumentException("containerName can't be null or empty", nameof(containerName));
                }

                if (String.IsNullOrWhiteSpace(resourceId))
                {
                    _counters.IncrementRateCounter("AzureTable LibraryExceptionRate");
                    throw new ArgumentException("resourceId can't be null or empty", nameof(resourceId));
                }

                try
                {
                    string pkFilter = TableQuery.GenerateFilterCondition("PartitionKey", QueryComparisons.Equal, containerName);
                    string rkFilter = TableQuery.GenerateFilterCondition("RowKey", QueryComparisons.Equal, resourceId);

                    //in netstandard the query has to be tempated to get a tempated return.
                    //https://github.com/Azure/azure-storage-net/issues/416
                    var tableQuery = new TableQuery<DynamicTableEntity>
                    {
                        FilterString = TableQuery.CombineFilters(pkFilter, TableOperators.And, rkFilter),
                        SelectColumns = AzureTableEntity<T>.GetColumnNames()
                    };
                    
                    TableQuerySegment<DynamicTableEntity> tqs = await _table.ExecuteQuerySegmentedAsync(tableQuery, null);

                    if (tqs?.Results == null)
                    {
                        throw new RelInfraStorageException($"Quering table pk={containerName}, rk={resourceId} ended up with null results").Format();
                    }

                    DynamicTableEntity dte = tqs.Results.FirstOrDefault();
                    if (dte == null)
                    {
                        throw new RelInfraStorageException($"Failed to get {containerName} {resourceId} . Error: NotFound", StorageError.NotFound).Format();
                    }

                    return new Storable<T>
                    {
                        Id = dte.RowKey,
                        ContainerId = containerName,
                        Etag = dte.ETag,
                        Entity = (new AzureTableEntity<T>(dte)).ResolveEntity()
                    };
                }
                catch (RelInfraStorageException)
                {
                    _counters.IncrementRateCounter("AzureTable GetFailureRate");
                    throw;
                }
                catch (StorageException ex)
                {
                    _counters.IncrementRateCounter("AzureTable GetFailureRate");
                    string log =
                        $"Could not get entity with partKey={containerName} and rowKey={resourceId} from azure table {_table.Name}";
                    Trace.TraceError($"{log}, ex: {ex}");

                    throw new RelInfraStorageException(log, ex,
                        StorageErrorHelper.GetError(ex.RequestInformation.HttpStatusCode)).Format();
                }

            }
        }

        public async Task<IEnumerable<Storable<T>>> BatchGetAsync<T>(string containerName, IEnumerable<string> resourceIds)
        {
            _counters.IncrementRateCounter("AzureTable BatchGetRate");
            using (_counters.CreateDisposableTimer("AzureTable BatchGetLatency"))
            {
                if (String.IsNullOrWhiteSpace(containerName))
                {
                    _counters.IncrementRateCounter("AzureTable LibraryExceptionRate");
                    throw new ArgumentException("containerName can't be null or empty", nameof(containerName));
                }

                try
                {
                    List<Task<Storable<T>>> taskList = resourceIds.Select(id => GetAsync<T>(containerName, id)).ToList();
                    _counters.SetNumberCounter("AzureTable EntitiesPerBatchGet", taskList.Count);

                    await Task.WhenAll(taskList);

                    return taskList.Select(task => task.Result).ToList();
                }
                catch (RelInfraStorageException)
                {
                    _counters.IncrementRateCounter("AzureTable BatchGetFailureRate");
                    throw;
                }
            }
        }

        private static Storable<T> CreateStorable<T>(string resourceId, string containerName, TableResult result)
        {
            var tableEntity = new AzureTableEntity<T>((DynamicTableEntity) result.Result);

            return new Storable<T>
                {
                    Id = resourceId,
                    ContainerId = containerName,
                    Etag = result.Etag,
                    Entity = tableEntity.ResolveEntity()
                };
        }

        public async Task<bool> TryUpdateAsync<T>(string containerName, string resourceId, Storable<T> resource)
        {
            try
            {
                return null != await UpdateAsync<T>(containerName, resourceId, resource);
            }
            catch (RelInfraStorageException)
            {
                return false;
            }
        }

        public async Task<Storable<T>> UpdateAsync<T>(string containerName, string resourceId, Storable<T> resource)
        {
            _counters.IncrementRateCounter("AzureTable UpdateRate");
            using (_counters.CreateDisposableTimer("AzureTable UpdateLatency"))
            {
                if (String.IsNullOrWhiteSpace(containerName))
                {
                    _counters.IncrementRateCounter("AzureTable LibraryExceptionRate");
                    throw new ArgumentException("containerName can't be null or empty", nameof(containerName));
                }

                if (String.IsNullOrWhiteSpace(resourceId))
                {
                    _counters.IncrementRateCounter("AzureTable LibraryExceptionRate");
                    throw new ArgumentException("resourceId can't be null or empty", nameof(resourceId));
                }

                try
                {
                    var entity = new AzureTableEntity<T>(containerName, resourceId, resource.Entity, resource.Name);
                    entity.GetTableEntity().ETag = resource.Etag;

                    TableOperation mergeOperation = TableOperation.Merge(entity.GetTableEntity());
                    await _table.ExecuteAsync(mergeOperation);

                    return await GetAsync<T>(containerName, resourceId);
                }
                catch (StorageException ex)
                {
                    _counters.IncrementRateCounter("AzureTable UpdateFailureRate");
                    string log =
                        $"Could not update entity with partKey={containerName} and rowKey={resourceId} from azure table {_table.Name}";
                    StorageError error = StorageErrorHelper.GetError(ex.RequestInformation.HttpStatusCode);
                    if (error == StorageError.PreconditionFailed)
                    {
                        Trace.TraceInformation($"{log}, PreconditionFailed (Etag mismatch), ex: {ex}");
                    }
                    else
                    {
                        Trace.TraceError($"{log}, ex: {ex}");
                    }
                    throw new RelInfraStorageException(log, ex, error).Format();
                }
            }
        }

        public async Task InsertOrReplaceAsync<T>(IEnumerable<Storable<T>> entities)
        {
            _counters.IncrementRateCounter("AzureTable InsertOrReplaceRate");
            using (_counters.CreateDisposableTimer("AzureTable InsertOrReplaceLatency"))
            {
                try
                {
                    foreach (IEnumerable<Storable<T>> batch in BatchStorables(entities, AzureBatchLimit))
                    {
                        IEnumerable<TableOperation> operations = batch.Select(e => CreateInsertOrReplaceOperation(e.ContainerId, e));
                        TableBatchOperation batchOperation = CreateBatchOperation(operations);
                        await _table.ExecuteBatchAsync(batchOperation);
                    }
                }
                catch 
                {
                    _counters.IncrementRateCounter("AzureTable InsertOrReplaceFailureRate");
                    throw;
                }
            }
        }

        private static TableOperation CreateInsertOrReplaceOperation<T>(string containerName, Storable<T> entity)
        {
            var azureEntity = new AzureTableEntity<T>(containerName, entity.Id, entity.Entity, entity.Name);
            var tableEntity = azureEntity.GetTableEntity();
            tableEntity.ETag = entity.Etag;
            return TableOperation.InsertOrReplace(tableEntity);
        }

        private static TableBatchOperation CreateBatchOperation(IEnumerable<TableOperation> operations)
        {
            TableBatchOperation batchOperation = new TableBatchOperation();
            foreach (TableOperation operation in operations)
            {
                batchOperation.Add(operation);
            }

            return batchOperation;
        }

        public async Task<IEnumerable<Storable<T>>> BatchUpdateAsync<T>(string containerName, IEnumerable<Storable<T>> resources)
        {
            _counters.IncrementRateCounter("AzureTable BatchUpdateRate");
            using (_counters.CreateDisposableTimer("AzureTable BatchUpdateLatency"))
            {
                if (String.IsNullOrWhiteSpace(containerName))
                {
                    _counters.IncrementRateCounter("AzureTable LibraryExceptionRate");
                    throw new ArgumentException("containerName can't be null or empty", nameof(containerName));
                }

                try
                {
                    var results = new List<Storable<T>>();
                    foreach (var listToProcess in resources.Chunk(AzureBatchLimit))
                    {
                        IEnumerable<Storable<T>> batchResults = await BatchUpdateInternalAsync<T>(containerName, listToProcess.ToList());
                        results.AddRange(batchResults);
                    }

                    _counters.SetNumberCounter("AzureTable EntitiesPerBatchUpdate", results.Count);

                    return results;
                }
                catch (RelInfraStorageException)
                {
                    _counters.IncrementRateCounter("AzureTable BatchUpdateFailureRate");
                    throw;
                }
            }
        }

        private async Task<IEnumerable<Storable<T>>> BatchUpdateInternalAsync<T>(string containerName, IList<Storable<T>> resources)
        {
            bool needDivide = false;
            var results = new List<Storable<T>>();

            try
            {
                var batchOperation = new TableBatchOperation();

                foreach (var resource in resources)
                {
                    var entity = new AzureTableEntity<T>(containerName, resource.Id, resource.Entity, resource.Name);
                    entity.GetTableEntity().ETag = resource.Etag;
                    batchOperation.Merge(entity.GetTableEntity());
                }

                IList<TableResult> tableResults = await _table.ExecuteBatchAsync(batchOperation);
                if (tableResults == null)
                {
                    return null;
                }

                foreach (var result in resources.Join(
                    tableResults,
                    res => res.Id,
                    tr => ((DynamicTableEntity)tr.Result).RowKey,
                    (storable, result) => new { Resource = storable, ETag = result.Etag }))
                {
                    result.Resource.Etag = result.ETag;
                    results.Add(result.Resource);
                }

                return results;
            }
            catch (StorageException ex)
            {
                string log;

                if( IsInvalidInputErrorCode(ex.RequestInformation)
                    && resources.Count() != resources.GroupBy(res => res.Id).Count())
                {
                    log =
                        $"Batch update partKey={containerName} to azure table {_table.Name} failed: resource ids for batch operation must be unique";
                }
                else
                {
                    log = $"Could not do batch update partKey={containerName} to azure table {_table.Name}, ex: {ex}";
                }
                Trace.TraceError(log);
                if (IsRequestBodyTooLargeErrorCode(ex.RequestInformation) && resources.Count > 1)
                {
                    needDivide = true;
                }
                else
                {
                    throw new RelInfraStorageException(log, ex, StorageErrorHelper.GetError(ex.RequestInformation.HttpStatusCode)).Format();
                }
            }

            if (needDivide)
            {
                foreach (IEnumerable<Storable<T>> halfBatch in resources.Chunk(resources.Count / 2))
                {
                    results.AddRange(await BatchUpdateInternalAsync(containerName, halfBatch.ToList()));
                }
            }

            return results;
        }

        private bool IsInvalidInputErrorCode(RequestResult result)
        {
            return result != null
                && result.ExtendedErrorInformation != null
                && result.ExtendedErrorInformation.ErrorCode == StorageErrorCodeStrings.InvalidInput;
        }

        private bool IsRequestBodyTooLargeErrorCode(RequestResult result)
        {
            return result != null
                && result.ExtendedErrorInformation != null
                && result.ExtendedErrorInformation.ErrorCode == StorageErrorCodeStrings.RequestBodyTooLarge;
        }

        public async Task<bool> TryDeleteAsync(string containerName, string resourceId)
        {
            try
            {
                await DeleteAsync(containerName, resourceId);
                return true;
            }
            catch (RelInfraStorageException)
            {
                return false;
            }
        }

        public async Task DeleteAsync(string containerName, string resourceId)
        {
            await DeleteAsync(containerName, resourceId, "*");
        }

        public async Task DeleteAsync(string containerName, string resourceId, string etag)
        {
            _counters.IncrementRateCounter("AzureTable DeleteRate");
            using (_counters.CreateDisposableTimer("AzureTable DeleteLatency"))
            {
                if (String.IsNullOrWhiteSpace(containerName))
                {
                    _counters.IncrementRateCounter("AzureTable LibraryExceptionRate");
                    throw new ArgumentException("containerName can't be null or empty", nameof(containerName));
                }

                if (String.IsNullOrWhiteSpace(resourceId))
                {
                    _counters.IncrementRateCounter("AzureTable LibraryExceptionRate");
                    throw new ArgumentException("resourceId can't be null or empty", nameof(resourceId));
                }

                try
                {
                    TableOperation deleteOperation = TableOperation.Delete(new DynamicTableEntity(containerName, resourceId) { ETag = etag });
                    await _table.ExecuteAsync(deleteOperation);
                }
                catch (StorageException ex)
                {
                    _counters.IncrementRateCounter("AzureTable DeleteFailureRate");
                    string log =
                        $"Could not delete entity with partKey={containerName} and rowKey={resourceId} from azure table {_table.Name}";
                    Trace.TraceError($"{log}, ex: {ex}");

                    throw new RelInfraStorageException(log, ex, StorageErrorHelper.GetError(ex.RequestInformation.HttpStatusCode)).Format();
                }
            }
        }

        public async Task BatchDeleteAsync(string containerName, IEnumerable<string> resourceIds)
        {
            _counters.IncrementRateCounter("AzureTable BatchDeleteRate");
            using (_counters.CreateDisposableTimer("AzureTable BatchDeleteLatency"))
            {
                if (String.IsNullOrWhiteSpace(containerName))
                {
                    _counters.IncrementRateCounter("AzureTable LibraryExceptionRate");
                    throw new ArgumentException("containerName can't be null or empty", nameof(containerName));
                }

                try
                {
                    long totalCount = 0;
                    foreach (IEnumerable<string> listToProcess in resourceIds.Chunk(AzureBatchLimit))
                    {
                        List<string> entitiesList = listToProcess.ToList();
                        totalCount += entitiesList.Count;
                        await BatchDeleteInternalAsync(containerName, entitiesList);
                    }
                    _counters.SetNumberCounter("AzureTable EntitiesPerBatchDelete", totalCount);

                }
                catch (RelInfraStorageException)
                {
                    _counters.IncrementRateCounter("AzureTable BatchDeleteFailureRate");
                    throw;
                }
            }
        }

        private async Task BatchDeleteInternalAsync(string containerName, IList<string> resourceIds)
        {
            try
            {
                var batchOperation = new TableBatchOperation();
                foreach (var id in resourceIds)
                {
                    batchOperation.Delete(new DynamicTableEntity(containerName, id) {ETag = "*"});
                }

                await _table.ExecuteBatchAsync(batchOperation);
            }
            catch (StorageException ex)
            {
                string log;

                if (IsInvalidInputErrorCode(ex.RequestInformation)
                    && resourceIds.Count != resourceIds.GroupBy(id => id).Count())
                {
                    log =
                        $"Batch delete partKey={containerName} in azure table {_table.Name} failed: resource ids for batch operation must be unique";
                }
                else
                {
                    log = $"Could not do batch delete partKey={containerName} in azure table {_table.Name}, ex: {ex}";
                }
                Trace.TraceError(log);
                throw new RelInfraStorageException(log, ex, StorageErrorHelper.GetError(ex.RequestInformation.HttpStatusCode)).Format();
            }
        }

        public async Task<IEnumerable<Storable<T>>> ExecuteQueryAsync<T>(string query, int? takeCount = null)
        {
            _counters.IncrementRateCounter("AzureTable ExecuteQueryRate");
            using (_counters.CreateDisposableTimer("AzureTable ExecuteQueryLatency"))
            {
                try
                {
                    var rangeQuery = new TableQuery<DynamicTableEntity> {FilterString = query};

                    TableContinuationToken token = null;

                    var entities = new List<DynamicTableEntity>();
                    do
                    {
                        rangeQuery.TakeCount = takeCount;
                        TableQuerySegment<DynamicTableEntity> tqs;
                        try
                        {
                            tqs = await _table.ExecuteQuerySegmentedAsync(rangeQuery, token);
                        }
                        catch (StorageException ex)
                        {
                            string log = $"Failed to execute query: {query}";
                            throw new RelInfraStorageException(log, ex, StorageErrorHelper.GetError(ex.RequestInformation.HttpStatusCode)).Format();
                        }

                        entities.AddRange(tqs.Results);

                        if (takeCount != null)
                        {
                            takeCount -= tqs.Results.Count;
                        }

                        if (takeCount == null || takeCount > 0)
                        {
                            token = tqs.ContinuationToken;
                        }
                        else
                        {
                            token = null;
                        }
                    }
                    while (token != null);

                    return entities.Select(entity =>
                        new Storable<T>
                        {
                            Id = entity.RowKey,
                            ContainerId = entity.PartitionKey,
                            Etag = entity.ETag,
                            Entity = (new AzureTableEntity<T>(entity)).ResolveEntity()
                        }
                        );
                }
                catch (RelInfraStorageException)
                {
                    _counters.IncrementRateCounter("AzureTable ExecuteQueryFailureRate");
                    throw;
                }
            }
        }

        //we can't read or write version or type because they're internal. Good news is they seemd like they were always constant. 
        //https://github.com/Azure/azure-storage-net/blob/68c3ee55a3a6f62a0159cea58005d3fe027312a8/Lib/Common/Table/TableContinuationToken.cs
        private TableContinuationToken ReadXml(XmlReader reader)
        {
            var token = new TableContinuationToken();
            // Read the xml root node
            reader.MoveToContent();
            reader.ReadStartElement();

            // Read the additional xml node that will be present if an XmlSerializer was used to serialize this token
            reader.MoveToContent();
            if (reader.Name == Constants.ContinuationConstants.ContinuationTopElement)
            {
                reader.ReadStartElement();
            }

            // Read the ContinuationToken content
            while (reader.IsStartElement())
            {
                switch (reader.Name)
                {
                    case Constants.ContinuationConstants.VersionElement:
                        //token.Version = reader.ReadElementContentAsString();
                        break;

                    case Constants.ContinuationConstants.TypeElement:
                        //token.Type = reader.ReadElementContentAsString();
                        break;

                    case Constants.ContinuationConstants.TargetLocationElement:
                        string targetLocation = reader.ReadElementContentAsString();
                        if (Enum.TryParse(targetLocation, out StorageLocation location))
                        {
                            token.TargetLocation = location;
                        }
                        else if (!string.IsNullOrEmpty(targetLocation)) 
                        {
                            throw new XmlException("unexpected location "+ targetLocation);
                        }

                        break;

                    case Constants.ContinuationConstants.NextPartitionKeyElement:
                        token.NextPartitionKey = reader.ReadElementContentAsString();
                        break;

                    case Constants.ContinuationConstants.NextRowKeyElement:
                        token.NextRowKey = reader.ReadElementContentAsString();
                        break;

                    case Constants.ContinuationConstants.NextTableNameElement:
                        token.NextTableName = reader.ReadElementContentAsString();
                        break;

                    default:
                        throw new XmlException("unexpected element " + reader.Name);
                }
            }
            return token;
        }

        private void WriteXml(TableContinuationToken token, XmlWriter writer)
        {
            if (writer == null)
            {
                throw new ArgumentNullException(nameof(writer));
            }

            writer.WriteStartElement(Constants.ContinuationConstants.ContinuationTopElement);

            //writer.WriteElementString(Constants.ContinuationConstants.VersionElement, this.Version);

            //writer.WriteElementString(Constants.ContinuationConstants.TypeElement, this.Type);

            if (token.NextPartitionKey != null)
            {
                writer.WriteElementString(Constants.ContinuationConstants.NextPartitionKeyElement, token.NextPartitionKey);
            }

            if (token.NextRowKey != null)
            {
                writer.WriteElementString(Constants.ContinuationConstants.NextRowKeyElement, token.NextRowKey);
            }

            if (token.NextTableName != null)
            {
                writer.WriteElementString(Constants.ContinuationConstants.NextTableNameElement, token.NextTableName);
            }

            writer.WriteElementString(Constants.ContinuationConstants.TargetLocationElement, token.TargetLocation.ToString());

            writer.WriteEndElement(); // End ContinuationToken
        }

        // Issue: the takeCount will not take effect if the takeCount is greater than the limit that max entity number in one page.
        // Please handle the results count on your client side, when you need use takeCount.
        public async Task<QueryResults<T>> ExecuteQueryAsync<T>(string partitionKey, string query, IEnumerable<string> selectColumns, string continuationToken, int? takeCount = null)
        {
            _counters.IncrementRateCounter("AzureTable ExecuteQueryRate");
            using (_counters.CreateDisposableTimer("AzureTable ExecuteQueryLatency"))
            {
                try
                {
                    var rangeQuery = new TableQuery<DynamicTableEntity>
                    {
                        FilterString = query,
                        TakeCount = takeCount,
                        SelectColumns = selectColumns?.ToList()
                    };

                    if (!string.IsNullOrEmpty(partitionKey))
                    {
                        rangeQuery = rangeQuery.Where(TableQuery.GenerateFilterCondition("PartitionKey", QueryComparisons.Equal, partitionKey));
                    }

                    TableContinuationToken token = null;
                    if (continuationToken != null)
                    {
                        token = ReadXml(new XmlTextReader(new StringReader(continuationToken)));
                    }

                    TableQuerySegment<DynamicTableEntity> tqs;
                    try
                    {
                        tqs = await _table.ExecuteQuerySegmentedAsync(rangeQuery, token);
                    }
                    catch (StorageException ex)
                    {
                        string log = $"Failed to execute query: {query}";
                        throw new RelInfraStorageException(log, ex, StorageErrorHelper.GetError(ex.RequestInformation.HttpStatusCode)).Format();
                    }

                    string newContinuationToken = null;

                    if (tqs.ContinuationToken != null)
                    {
                        var sb = new StringBuilder();
                        WriteXml(tqs.ContinuationToken, new XmlTextWriter(new StringWriter(sb)));
                        newContinuationToken = sb.ToString();
                    }

                    var results = tqs.Results.Select(entity => new Storable<T>
                    {
                        Id = entity.RowKey,
                        ContainerId = entity.PartitionKey,
                        Etag = entity.ETag,
                        Entity = new AzureTableEntity<T>(entity).ResolveEntity()
                    });
                    return new QueryResults<T>(continuationToken: newContinuationToken, results: results);
                }
                catch (RelInfraStorageException)
                {
                    _counters.IncrementRateCounter("AzureTable ExecuteQueryFailureRate");
                    throw;
                }
            }
        }

        public async Task<IEnumerable<Storable<T>>> GetPartitionAsync<T>(string partitionKey, int? takeCount = null)
        {
            _counters.IncrementRateCounter("AzureTable GetPartitionRate");
            using (_counters.CreateDisposableTimer("AzureTable GetPartitionLatency"))
            {
                try
                {
                    string query = $"(PartitionKey eq '{partitionKey}')";
                    return await ExecuteQueryAsync<T>(query, takeCount);
                }
                catch (RelInfraStorageException)
                {
                    _counters.IncrementRateCounter("AzureTable GetPartitionFailureRate");
                    throw;
                }
            }
        }

        public async Task<IEnumerable<Storable<T>>> GetAllAsync<T>(int? takeCount = null)
        {
            _counters.IncrementRateCounter("AzureTable GetAllRate");
            using (_counters.CreateDisposableTimer("AzureTable GetAllLatency"))
            {
                try
                {
                    return await ExecuteQueryAsync<T>(query: null, takeCount: takeCount);
                }
                catch (RelInfraStorageException)
                {
                    _counters.IncrementRateCounter("AzureTable GetAllFailureRate");
                    throw;
                }
            }
        }

        public async Task<QueryResults<T>> GetAllWithContinuationTokenAsync<T>(string continuationToken = null, int? takeCount = null) 
        {
            _counters.IncrementRateCounter("AzureTable GetAllWithContinuationTokenRate");
            using (_counters.CreateDisposableTimer("AzureTable GetAllWithContinuationTokenLatency"))
            {
                try
                {
                    return await ExecuteQueryAsync<T>(
                        query: null,
                        selectColumns: null,
                        continuationToken: continuationToken,
                        takeCount: takeCount,
                        partitionKey: null);
                }
                catch (RelInfraStorageException)
                {
                    _counters.IncrementRateCounter("AzureTable GetAllWithContinuationTokenFailureRate");
                    throw;
                }
            }
        }

        public async Task<QueryResults<T>> GetPartitionWithCountinuationTokenAsync<T>(string partitionKey, string continuationToken = null, int? takeCount = null)
        {
            return await GetPartitionWithCountinuationTokenAsync<T>(partitionKey: partitionKey, 
                                                                    columns: null, 
                                                                    continuationToken: continuationToken, 
                                                                    takeCount: takeCount);
        }

        public async Task<QueryResults<T>> GetPartitionWithCountinuationTokenAsync<T>(string partitionKey, IEnumerable<string> columns, string continuationToken = null, int? takeCount = null)
        {
            _counters.IncrementRateCounter("AzureTable GetPartitionWithCountinuationTokenRate");
            using (_counters.CreateDisposableTimer("AzureTable GetPartitionWithCountinuationTokenLatency"))
            {
                try
                {
                    string query = $"(PartitionKey eq '{partitionKey}')";
                    return await ExecuteQueryAsync<T>(query: query, selectColumns: columns, continuationToken: continuationToken, takeCount: takeCount, partitionKey: null);
                }
                catch (RelInfraStorageException)
                {
                    _counters.IncrementRateCounter("AzureTable GetPartitionWithCountinuationTokenFailureRate");
                    throw;
                }
            }
        }

        public string SanitizeKey(string key)
        {
            // Explicitly disallowed chars in Azure table key fields
            // http://msdn.microsoft.com/en-us/library/dd179338.aspx
            string sanitizedKey = key.Replace('/', '-')
                .Replace('\\', '-')
                .Replace('#', '-')
                .Replace('?', '-');

            // Not documented but fail in Azure table
            return sanitizedKey.Replace('\'', '-');
        }

        public async Task<int> GetRowCountAsync<T>(string partitionKey)
        {
            string continuationToken = null;
            int numberOfRows = 0;
            do
            {
                var queryResults = (await ExecuteQueryAsync<T>(query: $"(PartitionKey eq '{partitionKey}')", selectColumns: new List<string> { "PartitionKey" }, continuationToken: continuationToken, partitionKey: null));
                continuationToken = queryResults.ContinuationToken;
                numberOfRows += queryResults.Results.Count();
            } while (continuationToken != null);
            
            return numberOfRows;
        }
    }
}
