﻿namespace Microsoft.RelInfra.Storage
{
    public class Storable<T> : IStorable
    {
        public Storable()
        {
        }

        public string Id { get; set; }

        public string Name { get; set; }

        public string ContainerId { get; set; }

        public string Etag { get; set; }
        
        public object AuxData { get; set; }

        public T Entity { get; set; }

        public Storable(T entity)
        {
            Entity = entity;
        }
    }
}
