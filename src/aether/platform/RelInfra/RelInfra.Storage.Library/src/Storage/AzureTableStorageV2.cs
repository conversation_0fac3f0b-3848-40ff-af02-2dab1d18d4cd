﻿// <copyright file="AzureTableStorageV2.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Azure;
using Azure.Core;
using Azure.Data.Tables;
using Microsoft.RelInfra.Common.RetryPolicy;
using Microsoft.RelInfra.Instrumentation;
using Microsoft.RelInfra.Instrumentation.Exceptions;
using Microsoft.RelInfra.Instrumentation.Logging;
using Microsoft.RelInfra.Storage.Exceptions;
using Microsoft.RelInfra.Storage.Utils;

namespace Microsoft.RelInfra.Storage
{
    public class AzureTableStorageV2 : ITableStorage
    {
        // Based on https://docs.microsoft.com/en-us/rest/api/storageservices/Understanding-the-Table-Service-Data-Model?redirectedfrom=MSDN#characters-disallowed-in-key-fields
        public static readonly Regex DisallowedCharsInTableKeys = new Regex(@"[\\\\#%+/?\u0000-\u001F\u007F-\u009F]");

        // azure tables has limit on number of entities in batch, so we do batches in size of 100
        const short AzureBatchLimit = 100;

        private readonly TableServiceClient _tableServiceClient;
        private readonly TableClient _tableClient;
        private readonly OptionalCounters _counters;

        public string TableName => _tableClient.Name;

        public IRelInfraRetryPolicy RetryPolicy
        { 
            get => throw new NotImplementedException("This interface is deprecated in AzureTableStorageV2, please use TableClientOptions instead");
            set => throw new NotImplementedException("This interface is deprecated in AzureTableStorageV2, please use TableClientOptions instead");
        }

        public AzureTableStorageV2(Uri storageAccountUri, TokenCredential credential, string tableName, CounterManager counterManager, TableClientOptions options = default)
        {
            _tableServiceClient = new TableServiceClient(storageAccountUri, credential, options);
            _tableClient = _tableServiceClient.GetTableClient(tableName);
            _counters = new OptionalCounters(counterManager, tableName);
        }

        public AzureTableStorageV2(string connectionString, string tableName, CounterManager counterManager, TableClientOptions options = default)
        {
            _tableServiceClient = new TableServiceClient(connectionString, options);
            _tableClient = _tableServiceClient.GetTableClient(tableName);
            _counters = new OptionalCounters(counterManager, tableName);

            CommonLogger.LogEntityInfo(nameof(AzureTableStorageV2), $"Construct client with connection string. AccountName: [{_tableServiceClient.AccountName}]. TableName: [{tableName}].");
        }

        public AzureTableStorageV2(Uri storageAccountUri, TokenCredential credential, string tableName, CounterManager counterManager, double defaultMaxExecutionTimeInSeconds)
        {
            var options = new TableClientOptions();
            options.Retry.NetworkTimeout = TimeSpan.FromSeconds(defaultMaxExecutionTimeInSeconds);

            _tableServiceClient = new TableServiceClient(storageAccountUri, credential, options);
            _tableClient = _tableServiceClient.GetTableClient(tableName);
            _counters = new OptionalCounters(counterManager, tableName);
        }

        public AzureTableStorageV2(string connectionString, string tableName, CounterManager counterManager, double defaultMaxExecutionTimeInSeconds)
        {
            var options = new TableClientOptions();
            options.Retry.NetworkTimeout = TimeSpan.FromSeconds(defaultMaxExecutionTimeInSeconds);

            _tableServiceClient = new TableServiceClient(connectionString, options);
            _tableClient = _tableServiceClient.GetTableClient(tableName);
            _counters = new OptionalCounters(counterManager, tableName);

            CommonLogger.LogEntityInfo(nameof(AzureTableStorageV2), $"Construct client with connection string. AccountName: [{_tableServiceClient.AccountName}]. TableName: [{tableName}].");
        }

        public async Task<Storable<T>> AddAsync<T>(string containerName, string resourceId, Storable<T> resource)
        {
            _counters.IncrementRateCounter("AzureTableV2 AddRate");
            using (_counters.CreateDisposableTimer("AzureTableV2 AddLatency"))
            {
                if (String.IsNullOrWhiteSpace(containerName))
                {
                    _counters.IncrementRateCounter("AzureTableV2 LibraryExceptionRate");
                    throw new ArgumentException("containerName can't be null or empty", nameof(containerName));
                }

                if (String.IsNullOrWhiteSpace(resourceId))
                {
                    _counters.IncrementRateCounter("AzureTableV2 LibraryExceptionRate");
                    throw new ArgumentException("resourceId can't be null or empty", nameof(resourceId));
                }

                try
                {
                    var azureDataTableEntity = new AzureTableEntityV2<T>(containerName, resourceId, resource.Entity, resource.Name);
                    await _tableClient.AddEntityAsync(azureDataTableEntity.GetTableEntity());

                    return await GetAsync<T>(containerName, resourceId);
                }
                catch (RequestFailedException ex)
                {
                    _counters.IncrementRateCounter("AzureTableV2 AddFailureRate");
                    var log = $"Could not write entity with partKey={containerName} and rowKey={resourceId} to azure table {_tableClient.Name}";
                    Trace.TraceError($"{log}, ex: {ex}");

                    throw new RelInfraStorageException(log, ex, StorageErrorHelper.GetError(ex.Status)).Format();
                }
            }
        }

        public async Task BatchDeleteAsync(string containerName, IEnumerable<string> resourceIds)
        {
            _counters.IncrementRateCounter("AzureTableV2 BatchDeleteRate");
            using (_counters.CreateDisposableTimer("AzureTableV2 BatchDeleteLatency"))
            {
                if (String.IsNullOrWhiteSpace(containerName))
                {
                    _counters.IncrementRateCounter("AzureTableV2 LibraryExceptionRate");
                    throw new ArgumentException("containerName can't be null or empty", nameof(containerName));
                }

                try
                {
                    long totalCount = 0;
                    foreach (IEnumerable<string> listToProcess in resourceIds.Chunk(AzureBatchLimit))
                    {
                        List<string> entitiesList = listToProcess.ToList();
                        totalCount += entitiesList.Count;
                        await BatchDeleteInternalAsync(containerName, entitiesList);
                    }
                    _counters.SetNumberCounter("AzureTableV2 EntitiesPerBatchDelete", totalCount);

                }
                catch (RelInfraStorageException)
                {
                    _counters.IncrementRateCounter("AzureTableV2 BatchDeleteFailureRate");
                    throw;
                }
            }
        }

        public async Task<IEnumerable<Storable<T>>> BatchGetAsync<T>(string containerName, IEnumerable<string> resourceIds)
        {
            _counters.IncrementRateCounter("AzureTableV2 BatchGetRate");
            using (_counters.CreateDisposableTimer("AzureTableV2 BatchGetLatency"))
            {
                if (String.IsNullOrWhiteSpace(containerName))
                {
                    _counters.IncrementRateCounter("AzureTableV2 LibraryExceptionRate");
                    throw new ArgumentException("containerName can't be null or empty", nameof(containerName));
                }

                try
                {
                    List<Task<Storable<T>>> taskList = resourceIds.Select(id => GetAsync<T>(containerName, id)).ToList();
                    _counters.SetNumberCounter("AzureTableV2 EntitiesPerBatchGet", taskList.Count);

                    await Task.WhenAll(taskList);

                    return taskList.Select(task => task.Result).ToList();
                }
                catch (RelInfraStorageException)
                {
                    _counters.IncrementRateCounter("AzureTableV2 BatchGetFailureRate");
                    throw;
                }
            }
        }

        public async Task<IEnumerable<Storable<T>>> BatchPutAsync<T>(string containerName, IEnumerable<Storable<T>> resources)
        {
            _counters.IncrementRateCounter("AzureTableV2 BatchPutRate");
            using (_counters.CreateDisposableTimer("AzureTableV2 BatchPutLatency"))
            {
                try
                {
                    var results = new List<Storable<T>>();
                    foreach (IEnumerable<Storable<T>> listToProcess in resources.Chunk(AzureBatchLimit))
                    {
                        List<Storable<T>> entityList = listToProcess.ToList();
                        IEnumerable<Storable<T>> batchResults = await BatchPutAsyncInternal<T>(containerName, entityList);
                        if (batchResults == null)
                        {
                            var log = $"Batch insertion partKey={containerName} to azure table {_tableClient.Name} fails: operation returned null";
                            throw new RelInfraStorageException(log).Format();
                        }
                        results.AddRange(batchResults);
                    }

                    _counters.SetNumberCounter("AzureTableV2 EntitiesPerBatchPut", results.Count);
                    return results;
                }
                catch (RelInfraStorageException)
                {
                    _counters.IncrementRateCounter("AzureTableV2 BatchPutFailureRate");
                    throw;
                }
            }
        }

        public async Task<IEnumerable<Storable<T>>> BatchUpdateAsync<T>(string containerName, IEnumerable<Storable<T>> resources)
        {
            _counters.IncrementRateCounter("AzureTableV2 BatchUpdateRate");
            using (_counters.CreateDisposableTimer("AzureTableV2 BatchUpdateLatency"))
            {
                if (String.IsNullOrWhiteSpace(containerName))
                {
                    _counters.IncrementRateCounter("AzureTableV2 LibraryExceptionRate");
                    throw new ArgumentException("containerName can't be null or empty", nameof(containerName));
                }

                try
                {
                    var results = new List<Storable<T>>();
                    foreach (var listToProcess in resources.Chunk(AzureBatchLimit))
                    {
                        IEnumerable<Storable<T>> batchResults = await BatchUpdateInternalAsync<T>(containerName, listToProcess.ToList());
                        results.AddRange(batchResults);
                    }

                    _counters.SetNumberCounter("AzureTableV2 EntitiesPerBatchUpdate", results.Count);

                    return results;
                }
                catch (RelInfraStorageException)
                {
                    _counters.IncrementRateCounter("AzureTableV2 BatchUpdateFailureRate");
                    throw;
                }
            }
        }

        public async Task CreateIfNotExistsAsync()
        {
            _counters.IncrementRateCounter("AzureTableV2 CreateIfNotExistsRate");
            using (_counters.CreateDisposableTimer("AzureTableV2 CreateIfNotExistsLatency"))
            {
                try
                {
                    await _tableClient.CreateIfNotExistsAsync();
                }
                catch (RequestFailedException ex)
                {
                    _counters.IncrementRateCounter("AzureTableV2 CreateIfNotExistsFailureRate");
                    var log = $"Failed to Check/Create table: {_tableClient.Name}";

                    throw new RelInfraStorageException(log, ex, StorageErrorHelper.GetError(ex.Status)).Format();
                }
            }
        }

        public async Task DeleteAsync(string containerName, string resourceId)
        {
            await DeleteAsync(containerName, resourceId, string.Empty);
        }

        public async Task DeleteAsync(string containerName, string resourceId, string etag)
        {
            _counters.IncrementRateCounter("AzureTableV2 DeleteRate");
            using (_counters.CreateDisposableTimer("AzureTableV2 DeleteLatency"))
            {
                if (String.IsNullOrWhiteSpace(containerName))
                {
                    _counters.IncrementRateCounter("AzureTableV2 LibraryExceptionRate");
                    throw new ArgumentException("containerName can't be null or empty", nameof(containerName));
                }

                if (String.IsNullOrWhiteSpace(resourceId))
                {
                    _counters.IncrementRateCounter("AzureTableV2 LibraryExceptionRate");
                    throw new ArgumentException("resourceId can't be null or empty", nameof(resourceId));
                }

                try
                {
                    if (string.IsNullOrEmpty(etag))
                    {
                        await _tableClient.DeleteEntityAsync(containerName, resourceId);
                    }
                    else
                    {
                        await _tableClient.DeleteEntityAsync(containerName, resourceId, new ETag(etag));
                    }
                }
                catch (RequestFailedException ex)
                {
                    _counters.IncrementRateCounter("AzureTableV2 DeleteFailureRate");
                    var log = $"Could not delete entity with partKey={containerName} and rowKey={resourceId} from azure table {_tableClient.Name}";
                    Trace.TraceError($"{log}, ex: {ex}");

                    throw new RelInfraStorageException(log, ex, StorageErrorHelper.GetError(ex.Status)).Format();
                }
            }
        }

        public async Task DeleteIfExistsAsync()
        {
            _counters.IncrementRateCounter("AzureTableV2 DeleteIfExistsRate");
            using (_counters.CreateDisposableTimer("AzureTableV2 DeleteIfExistsLatency"))
            {
                try
                {
                    await _tableClient.DeleteAsync();
                }
                catch (RequestFailedException ex)
                {
                    _counters.IncrementRateCounter("AzureTableV2 DeleteIfExistsFailureRate");
                    var log = $"Failed to delete table: {_tableClient.Name}";
                    throw new RelInfraStorageException(log, ex, StorageErrorHelper.GetError(ex.Status)).Format();
                }
            }
        }

        public async Task<IEnumerable<Storable<T>>> ExecuteQueryAsync<T>(string query, int? takeCount = null)
        {
            _counters.IncrementRateCounter("AzureTableV2 ExecuteQueryRate");
            using (_counters.CreateDisposableTimer("AzureTableV2 ExecuteQueryLatency"))
            {
                try
                {
                    AsyncPageable<TableEntity> queryResults = _tableClient.QueryAsync<TableEntity>(filter: query);
                    var entities = new List<TableEntity>();
                    await foreach (var tableEntity in queryResults)
                    {
                        if (takeCount == null || takeCount > 0)
                        {
                            entities.Add(tableEntity);
                            if (takeCount != null)
                            {
                                takeCount--;
                            }
                        }
                        else
                        {
                            break;
                        }
                    }

                    return entities.Select(entity =>
                        new Storable<T>
                        {
                            Id = entity.RowKey,
                            ContainerId = entity.PartitionKey,
                            Etag = entity.ETag.ToString(),
                            Entity = (new AzureTableEntityV2<T>(entity)).ResolveEntity()
                        });
                }
                catch (RequestFailedException ex)
                {
                    _counters.IncrementRateCounter("AzureTableV2 ExecuteQueryFailureRate");

                    throw new RelInfraStorageException(ex.Message, ex, StorageErrorHelper.GetError(ex.Status)).Format();
                }
            }
        }

        public async Task<bool> ExistsAsync(string containerName, string resourceId)
        {
            _counters.IncrementRateCounter("AzureTableV2 ExistsRate");
            using (_counters.CreateDisposableTimer("AzureTableV2 ExistsLatency"))
            {
                if (String.IsNullOrWhiteSpace(containerName))
                {
                    _counters.IncrementRateCounter("AzureTableV2 LibraryExceptionRate");
                    throw new ArgumentException("containerName can't be null or empty", nameof(containerName));
                }

                if (String.IsNullOrWhiteSpace(resourceId))
                {
                    _counters.IncrementRateCounter("AzureTableV2 LibraryExceptionRate");
                    throw new ArgumentException("resourceId can't be null or empty", nameof(resourceId));
                }

                try
                {
                    AsyncPageable<TableEntity> queryResults = _tableClient.QueryAsync<TableEntity>(
                        e => e.PartitionKey == containerName && e.RowKey == resourceId,
                        select: new List<string> { "PartitionKey" }); // Only select PartitionKey column to reduce payload

                    // Check first entity if exist
                    await using (IAsyncEnumerator<TableEntity> enumerator = queryResults.GetAsyncEnumerator())
                    {
                        await enumerator.MoveNextAsync();
                        var entity = enumerator.Current;

                        return entity != null;
                    }
                }
                catch (RequestFailedException ex)
                {
                    _counters.IncrementRateCounter("AzureTableV2 ExistsFailureRate");
                    var log = $"Could not check entity exist with partKey={containerName} and rowKey={resourceId} from azure table {_tableClient.Name}";
                    Trace.TraceError($"{log}, ex: {ex}");

                    throw new RelInfraStorageException(log, ex, StorageErrorHelper.GetError(ex.Status)).Format();
                }
            }
        }

        public async Task<IEnumerable<Storable<T>>> GetAllAsync<T>(int? takeCount = null)
        {
            _counters.IncrementRateCounter("AzureTableV2 GetAllRate");
            using (_counters.CreateDisposableTimer("AzureTableV2 GetAllLatency"))
            {
                try
                {
                    return await ExecuteQueryAsync<T>(query: null, takeCount: takeCount);
                }
                catch (RelInfraStorageException)
                {
                    _counters.IncrementRateCounter("AzureTableV2 GetAllFailureRate");
                    throw;
                }
            }
        }

        public async Task<QueryResults<T>> GetAllWithContinuationTokenAsync<T>(string continuationToken = null, int? takeCount = null)
        {
            _counters.IncrementRateCounter("AzureTableV2 GetAllWithContinuationTokenRate");
            using (_counters.CreateDisposableTimer("AzureTableV2 GetAllWithContinuationTokenLatency"))
            {
                try
                {
                    return await ExecuteQueryInternalAsync<T>(
                        query: null,
                        selectColumns: null,
                        continuationToken: continuationToken,
                        takeCount: takeCount,
                        partitionKey: null);
                }
                catch (RelInfraStorageException)
                {
                    _counters.IncrementRateCounter("AzureTableV2 GetAllWithContinuationTokenFailureRate");

                    throw;
                }
            }
        }

        public async Task<Storable<T>> GetAsync<T>(string containerName, string resourceId)
        {
            _counters.IncrementRateCounter("AzureTableV2 GetRate");
            using (_counters.CreateDisposableTimer("AzureTableV2 GetLatency"))
            {

                if (String.IsNullOrWhiteSpace(containerName))
                {
                    _counters.IncrementRateCounter("AzureTableV2 LibraryExceptionRate");
                    throw new ArgumentException("containerName can't be null or empty", nameof(containerName));
                }

                if (String.IsNullOrWhiteSpace(resourceId))
                {
                    _counters.IncrementRateCounter("AzureTableV2 LibraryExceptionRate");
                    throw new ArgumentException("resourceId can't be null or empty", nameof(resourceId));
                }

                try
                {
                    var entity = await _tableClient.GetEntityAsync<TableEntity>(containerName, resourceId);

                    return new Storable<T>
                    {
                        Id = resourceId,
                        ContainerId = containerName,
                        Etag = entity.Value.ETag.ToString(),
                        Entity = (new AzureTableEntityV2<T>(entity.Value)).ResolveEntity()
                    };
                }
                catch (RequestFailedException ex)
                {
                    _counters.IncrementRateCounter("AzureTableV2 GetFailureRate");
                    var log = $"Could not get entity with partKey={containerName} and rowKey={resourceId} from azure table {_tableClient.Name}";
                    Trace.TraceError($"{log}, ex: {ex}");

                    throw new RelInfraStorageException(log, ex, StorageErrorHelper.GetError(ex.Status)).Format();
                }
            }
        }

        public async Task<IEnumerable<Storable<T>>> GetPartitionAsync<T>(string partitionKey, int? takeCount = null)
        {
            _counters.IncrementRateCounter("AzureTableV2 GetPartitionRate");
            using (_counters.CreateDisposableTimer("AzureTableV2 GetPartitionLatency"))
            {
                try
                {
                    string query = $"(PartitionKey eq '{partitionKey}')";
                    return await ExecuteQueryAsync<T>(query, takeCount);
                }
                catch (RelInfraStorageException)
                {
                    _counters.IncrementRateCounter("AzureTableV2 GetPartitionFailureRate");
                    throw;
                }
            }
        }

        public async Task<QueryResults<T>> GetPartitionWithCountinuationTokenAsync<T>(string partitionKey, string continuationToken, int? takeCount = null)
        {
            return await GetPartitionWithCountinuationTokenAsync<T>(
                partitionKey: partitionKey,
                columns: null,
                continuationToken: continuationToken,
                takeCount: takeCount);
        }

        public async Task<QueryResults<T>> GetPartitionWithCountinuationTokenAsync<T>(string partitionKey, IEnumerable<string> columns, string continuationToken = null, int? takeCount = null)
        {
            _counters.IncrementRateCounter("AzureTableV2 GetPartitionWithCountinuationTokenRate");
            using (_counters.CreateDisposableTimer("AzureTableV2 GetPartitionWithCountinuationTokenLatency"))
            {
                try
                {
                    string query = $"(PartitionKey eq '{partitionKey}')";

                    return await ExecuteQueryInternalAsync<T>(query: query, selectColumns: columns, continuationToken: continuationToken, takeCount: takeCount, partitionKey: null);
                }
                catch (RelInfraStorageException)
                {
                    _counters.IncrementRateCounter("AzureTableV2 GetPartitionWithCountinuationTokenFailureRate");

                    throw;
                }
            }
        }

        public async Task<int> GetRowCountAsync<T>(string partitionKey)
        {
           
            var numberOfRows = 0;
            AsyncPageable<TableEntity> queryResults = _tableClient.QueryAsync<TableEntity>(
                       e => e.PartitionKey == partitionKey,
                       select: new List<string> { "PartitionKey" }); // Only select PartitionKey column to reduce payload

            await foreach (var entity in queryResults)
            {
                numberOfRows++;
            }

            return numberOfRows;
        }

        public async Task InitializeAsync(string containerName)
        {
            await CreateIfNotExistsAsync();
        }

        public async Task InsertOrReplaceAsync<T>(IEnumerable<Storable<T>> entities)
        {
            _counters.IncrementRateCounter("AzureTableV2 InsertOrReplaceRate");
            using (_counters.CreateDisposableTimer("AzureTableV2 InsertOrReplaceLatency"))
            {
                try
                {
                    foreach (var batch in BatchStorables(entities, AzureBatchLimit))
                    {
                        List<TableTransactionAction> actions = batch.Select(entity => CreateInsertOrReplaceTransactionAction(entity.ContainerId, entity)).ToList();
                        Response<IReadOnlyList<Response>> batchResult = await _tableClient.SubmitTransactionAsync(actions);
                    }
                }
                catch (InvalidOperationException ex)
                {
                    throw new RelInfraStorageException(ex.Message, ex, StorageError.BadRequest).Format();
                }
                catch (RequestFailedException ex)
                {
                    _counters.IncrementRateCounter("AzureTableV2 InsertOrReplaceFailureRate");

                    throw new RelInfraStorageException(ex.Message, ex, StorageErrorHelper.GetError(ex.Status)).Format();
                }
                catch
                {
                    _counters.IncrementRateCounter("AzureTableV2 InsertOrReplaceFailureRate");

                    throw;
                }
            }
        }

        public async Task<Storable<T>> PutAndIgnoreEtagAsync<T>(string containerName, string resourceId, Storable<T> resource)
        {
            _counters.IncrementRateCounter("AzureTableV2 PutRate");
            using (_counters.CreateDisposableTimer("AzureTableV2 PutLatency"))
            {
                if (String.IsNullOrWhiteSpace(containerName))
                {
                    _counters.IncrementRateCounter("AzureTableV2 LibraryExceptionRate");
                    throw new ArgumentException("containerName can't be null or empty", nameof(containerName));
                }

                if (String.IsNullOrWhiteSpace(resourceId))
                {
                    _counters.IncrementRateCounter("AzureTableV2 LibraryExceptionRate");
                    throw new ArgumentException("resourceId can't be null or empty", nameof(resourceId));
                }

                try
                {
                    var azureDataTableEntity = new AzureTableEntityV2<T>(containerName, resourceId, resource.Entity, resource.Name);
                    await _tableClient.UpsertEntityAsync(azureDataTableEntity.GetTableEntity(), TableUpdateMode.Merge);

                    return await GetAsync<T>(containerName, resourceId);
                }
                catch (RequestFailedException ex)
                {
                    _counters.IncrementRateCounter("AzureTableV2 PutFailureRate");
                    var log = $"Could not write entity with partKey={containerName} and rowKey={resourceId} to azure table {_tableClient.Name}";
                    Trace.TraceError($"{log}, ex: {ex}");

                    throw new RelInfraStorageException(log, ex, StorageErrorHelper.GetError(ex.Status)).Format();
                }
            }
        }

        public string SanitizeKey(string key)
        {
            // Explicitly disallowed chars in Azure table key fields
            // http://msdn.microsoft.com/en-us/library/dd179338.aspx
            string sanitizedKey = key.Replace('/', '-')
                .Replace('\\', '-')
                .Replace('#', '-')
                .Replace('?', '-');

            // Not documented but fail in Azure table
            return sanitizedKey.Replace('\'', '-');
        }

        public async Task<bool> TableExistsAsync()
        {
            var exists = false;
            await foreach (var tbl in _tableServiceClient.QueryAsync(t => t.Name == TableName))
            {
                exists = true;
            }

            return exists;
        }

        public async Task<bool> TryBatchPutAsync<T>(string containerName, IEnumerable<Storable<T>> resources)
        {
            try
            {
                await BatchPutAsync<T>(containerName, resources);
            }
            catch (RelInfraStorageException)
            {
                return false;
            }

            return true;
        }

        public async Task<bool> TryDeleteAsync(string containerName, string resourceId)
        {
            try
            {
                await DeleteAsync(containerName, resourceId);

                return true;
            }
            catch (RelInfraStorageException)
            {
                return false;
            }
        }

        public async Task<Storable<T>> TryGetAsync<T>(string containerName, string resourceId)
        {
            try
            {
                return await GetAsync<T>(containerName, resourceId);
            }
            catch (RelInfraStorageException)
            {
                return null;
            }
        }

        public async Task<bool> TryPutAndIgnoreEtagAsync<T>(string containerName, string resourceId, Storable<T> resource)
        {
            try
            {
                await PutAndIgnoreEtagAsync(containerName, resourceId, resource);

                return true;
            }
            catch (RelInfraStorageException)
            {
                return false;
            }
        }

        public async Task<bool> TryUpdateAsync<T>(string containerName, string resourceId, Storable<T> resource)
        {
            try
            {
                return null != await UpdateAsync<T>(containerName, resourceId, resource);
            }
            catch (RelInfraStorageException)
            {
                return false;
            }
        }

        public async Task<Storable<T>> UpdateAsync<T>(string containerName, string resourceId, Storable<T> resource)
        {
            _counters.IncrementRateCounter("AzureTableV2 UpdateRate");
            using (_counters.CreateDisposableTimer("AzureTableV2 UpdateLatency"))
            {
                if (String.IsNullOrWhiteSpace(containerName))
                {
                    _counters.IncrementRateCounter("AzureTableV2 LibraryExceptionRate");
                    throw new ArgumentException("containerName can't be null or empty", nameof(containerName));
                }

                if (String.IsNullOrWhiteSpace(resourceId))
                {
                    _counters.IncrementRateCounter("AzureTableV2 LibraryExceptionRate");
                    throw new ArgumentException("resourceId can't be null or empty", nameof(resourceId));
                }

                try
                {
                    var tableEntity = new AzureTableEntityV2<T>(containerName, resourceId, resource.Entity, resource.Name).GetTableEntity();
                    tableEntity.ETag = new ETag(resource.Etag);
                    await _tableClient.UpdateEntityAsync(tableEntity, ifMatch: tableEntity.ETag, mode: TableUpdateMode.Merge);

                    return await GetAsync<T>(containerName, resourceId);
                }
                catch (RequestFailedException ex)
                {
                    _counters.IncrementRateCounter("AzureTableV2 UpdateFailureRate");
                    var log = $"Could not update entity with partKey={containerName} and rowKey={resourceId} from azure table {_tableClient.Name}";
                    StorageError error = StorageErrorHelper.GetError(ex.Status);
                    if (error == StorageError.PreconditionFailed)
                    {
                        Trace.TraceInformation($"{log}, PreconditionFailed (Etag mismatch), ex: {ex}");
                    }
                    else
                    {
                        Trace.TraceError($"{log}, ex: {ex}");
                    }
                    throw new RelInfraStorageException(log, ex, error).Format();
                }
            }
        }

        private async Task BatchDeleteInternalAsync(string containerName, IList<string> resourceIds)
        {
            try
            {
                var batch = new List<TableTransactionAction>();
                foreach (var id in resourceIds)
                {
                    batch.Add(new TableTransactionAction(TableTransactionActionType.Delete, new TableEntity(containerName, id)));
                }

                Response<IReadOnlyList<Response>> batchResult = await _tableClient.SubmitTransactionAsync(batch);
            }
            catch (InvalidOperationException ex)
            {
                throw new RelInfraStorageException(ex.Message, ex, StorageError.BadRequest).Format();
            }
            catch (RequestFailedException ex)
            {
                var log = $"Could not do batch delete partKey={containerName} in azure table {_tableClient.Name}, ex: {ex}";
                Trace.TraceError(log);

                throw new RelInfraStorageException(log, ex, StorageErrorHelper.GetError(ex.Status)).Format();
            }
        }

        private async Task<IEnumerable<Storable<T>>> BatchPutAsyncInternal<T>(string containerName, IList<Storable<T>> resources)
        {
            if (String.IsNullOrWhiteSpace(containerName))
            {
                _counters.IncrementRateCounter("AzureTableV2 LibraryExceptionRate");
                throw new ArgumentException("containerName can't be null or empty", nameof(containerName));
            }

            bool needDivide = false;
            var results = new List<Storable<T>>();

            try
            {
                var batch = new List<TableTransactionAction>();
                foreach (var resource in resources)
                {
                    batch.Add(new TableTransactionAction(TableTransactionActionType.UpsertMerge, (new AzureTableEntityV2<T>(containerName, resource.Id, resource.Entity, resource.Name)).GetTableEntity()));
                }

                Response<IReadOnlyList<Response>> batchResult = await _tableClient.SubmitTransactionAsync(batch);
                var resourceIds = resources.Select(r => r.Id).ToList().Distinct();

                results = (await BatchGetAsync<T>(containerName, resourceIds)).ToList();
            }
            catch (InvalidOperationException ex)
            {
                throw new RelInfraStorageException(ex.Message, ex, StorageError.BadRequest).Format();
            }
            catch (RequestFailedException ex)
            {
                string log;

                if (IsInvalidInputErrorCode(ex)
                    && resources.Count() != resources.GroupBy(res => res.Id).Count())
                {
                    log = $"Batch insertion partKey={containerName} to azure table {_tableClient.Name} failed. Possible reason is that " +
                        $"resource ids for batch operation must be unique, dupes are [{String.Join(", ", resources.GroupBy(res => res.Id).Where(group => @group.Count() > 1).Select(group => @group.Key))}]";
                }
                else
                {
                    log = $"Could not do batch insertion partKey={containerName} to azure table {_tableClient.Name}, ex: {ex}";
                }
                Trace.TraceError(log);
                if (IsRequestBodyTooLargeErrorCode(ex) && resources.Count > 1)
                {
                    needDivide = true;
                }
                else
                {
                    throw new RelInfraStorageException(log, ex, StorageErrorHelper.GetError(ex.Status)).Format();
                }
            }

            if (needDivide)
            {
                foreach (IEnumerable<Storable<T>> halfBatch in resources.Chunk(resources.Count / 2))
                {
                    results.AddRange(await BatchPutAsyncInternal(containerName, halfBatch.ToList()));
                }
            }

            return results;
        }

        internal async Task<IEnumerable<Storable<T>>> BatchUpdateInternalAsync<T>(string containerName, IList<Storable<T>> resources)
        {
            bool needDivide = false;
            var results = new List<Storable<T>>();

            try
            {
                var batch = new List<TableTransactionAction>();
                foreach (var resource in resources)
                {
                    var tableEntity = (new AzureTableEntityV2<T>(containerName, resource.Id, resource.Entity, resource.Name)).GetTableEntity();
                    tableEntity.ETag = new ETag(resource.Etag);
                    batch.Add(new TableTransactionAction(TableTransactionActionType.UpdateMerge, tableEntity, tableEntity.ETag));
                }

                Response<IReadOnlyList<Response>> batchResult = await _tableClient.SubmitTransactionAsync(batch);
                var resourceIds = resources.Select(r => r.Id).ToList().Distinct();

                results = (await BatchGetAsync<T>(containerName, resourceIds)).ToList();
            }
            catch (InvalidOperationException ex)
            {
                throw new RelInfraStorageException(ex.Message, ex, StorageError.BadRequest).Format();
            }
            catch (RequestFailedException ex)
            {
                string log;

                if (IsInvalidInputErrorCode(ex)
                    && resources.Count() != resources.GroupBy(res => res.Id).Count())
                {
                    log = $"Batch update partKey={containerName} to azure table {_tableClient.Name} failed: resource ids for batch operation must be unique";
                }
                else
                {
                    log = $"Could not do batch update partKey={containerName} to azure table {_tableClient.Name}, ex: {ex}";
                }
                Trace.TraceError(log);
                if (IsRequestBodyTooLargeErrorCode(ex) && resources.Count > 1)
                {
                    needDivide = true;
                }
                else
                {
                    throw new RelInfraStorageException(log, ex, StorageErrorHelper.GetError(ex.Status)).Format();
                }
            }

            if (needDivide)
            {
                foreach (IEnumerable<Storable<T>> halfBatch in resources.Chunk(resources.Count / 2))
                {
                    results.AddRange(await BatchUpdateInternalAsync(containerName, halfBatch.ToList()));
                }
            }

            return results;
        }

        private static TableTransactionAction CreateInsertOrReplaceTransactionAction<T>(string containerName, Storable<T> entity)
        {
            var tableEntity = new AzureTableEntityV2<T>(containerName, entity.Id, entity.Entity, entity.Name);
            var transaction = new TableTransactionAction(TableTransactionActionType.UpsertReplace, tableEntity.GetTableEntity());

            return transaction;
        }

        internal static IEnumerable<IEnumerable<Storable<T>>> BatchStorables<T>(IEnumerable<Storable<T>> storables, int batchSize)
        {
            // group by partition key
            foreach (IGrouping<string, Storable<T>> group in storables.GroupBy(s => s.ContainerId))
            {
                // within each group, produce batches
                foreach (IEnumerable<Storable<T>> batch in group.Chunk(batchSize))
                {
                    yield return batch;
                }
            }
        }

        private bool IsInvalidInputErrorCode(RequestFailedException requestFailedException)
        {
            return string.Equals(requestFailedException.ErrorCode, "InvalidDuplicateRow", StringComparison.OrdinalIgnoreCase) ||
                string.Equals(requestFailedException.ErrorCode, "InvalidInput", StringComparison.OrdinalIgnoreCase);
        }

        private bool IsRequestBodyTooLargeErrorCode(RequestFailedException requestFailedException)
        {
            return requestFailedException.Status == (int) HttpStatusCode.RequestEntityTooLarge;
        }


        // Note!!! the takeCount will not take effect if the takeCount is greater than the limit of max entity number in one page.
        // Please handle the results count on your client side, when you need use takeCount.
        internal async Task<QueryResults<T>> ExecuteQueryInternalAsync<T>(string partitionKey, string query, IEnumerable<string> selectColumns, string continuationToken, int? takeCount = null)
        {
            _counters.IncrementRateCounter("AzureTableV2 ExecuteQueryRate");
            using (_counters.CreateDisposableTimer("AzureTableV2 ExecuteQueryLatency"))
            {
                try
                {
                    if (!string.IsNullOrEmpty(partitionKey))
                    {
                        var filterPartitionKey = TableClient.CreateQueryFilter<TableEntity>(e => e.PartitionKey == partitionKey);

                        // Combine the two filter strings with an 'and'
                        query = $"({query}) and ({filterPartitionKey})";
                    }

                    AsyncPageable<TableEntity> queryResults = _tableClient.QueryAsync<TableEntity>(
                        filter: query,
                        maxPerPage: takeCount,
                        select: selectColumns);


                    await using (IAsyncEnumerator<Page<TableEntity>> enumerator = queryResults.AsPages(continuationToken).GetAsyncEnumerator())
                    {
                        await enumerator.MoveNextAsync();
                        var page = enumerator.Current;
                        var token = page.ContinuationToken;

                        var results = page.Values.Select(entity => new Storable<T>
                        {
                            Id = entity.RowKey,
                            ContainerId = entity.PartitionKey,
                            Etag = entity.ETag.ToString(),
                            Entity = new AzureTableEntityV2<T>(entity).ResolveEntity()
                        });

                        return new QueryResults<T>(continuationToken: token, results: results);
                    }
                }
                catch (RequestFailedException ex)
                {
                    _counters.IncrementRateCounter("AzureTableV2 ExecuteQueryFailureRate");
                    string log = $"Failed to execute query: {query}";
                    throw new RelInfraStorageException(log, ex, StorageErrorHelper.GetError(ex.Status)).Format();

                    throw;
                }
            }
        }
    }
}
