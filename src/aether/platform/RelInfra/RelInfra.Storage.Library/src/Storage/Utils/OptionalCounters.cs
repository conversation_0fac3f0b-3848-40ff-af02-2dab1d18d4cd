﻿using System;
using Microsoft.RelInfra.Instrumentation;

namespace Microsoft.RelInfra.Storage.Utils
{
    public class OptionalCounters
    {
        private readonly string _tableName;
        private readonly CounterManager _counterManager;

        public OptionalCounters(CounterManager counterManager, string tableName)
        {
            _counterManager = counterManager;
            _tableName = tableName;
        }

        public IDisposable CreateDisposableTimer(string counterName)
        {
            if (_counterManager != null)
            {
                return new DisposableTimer(_counterManager.GetLatencyCounter(counterName, _tableName));
            }
            return null;
        }

        public void IncrementRateCounter(string counterName)
        {
            if (_counterManager != null)
            {
                _counterManager.GetRateCounter(counterName, _tableName).Increment();
            }
        }

        public void SetNumberCounter(string counterName, long value)
        {
            if (_counterManager != null)
            {
                _counterManager.GetNumberCounter(counterName, _tableName).Set(value);
            }
        }

        public void IncrementRateCounterNoTableName(string counterName)
        {
            if (_counterManager != null)
            {
                _counterManager.GetRateCounter(counterName).Increment();
            }
        }
    }
}
