﻿using System;
using System.Reflection;
using System.Text;
using Azure.Storage.Queues.Models;
using Microsoft.RelInfra.Instrumentation.Logging;
using Microsoft.WindowsAzure.Storage.Queue;
using Newtonsoft.Json;

namespace Microsoft.RelInfra.Storage
{
    public static class AzureQueueMigrationExtensions
    {
        /// <summary>
        /// This method is to make compatible for old SDK(v9) to retrieve message inserted by new SDK(v12).
        ///     1) For the message inserted with v9 SDK, 'message.AsString' can return normal message text.
        ///     2) For the message inserted with v12 SDK, messageType = Base64Encoded but RawString = messageText.
        /// So use reflection to access 'RawString' (private property) and return message text.
        /// </summary>
        public static string GetMessageContent(this CloudQueueMessage message)
        {
            try
            {
                return message.AsString;
            }
            catch (FormatException ex) when (ex.Message.Contains("The input is not a valid Base-64 string", StringComparison.InvariantCulture))
            {
                CommonLogger.LogWarning("CloudQueueMessage content failed to parse with FormatException. Try to get content from RawString. {Error}.", ex);
                var propertyInfo = message.GetType().GetProperty("RawString", BindingFlags.NonPublic | BindingFlags.Instance);
                return propertyInfo.GetValue(message).ToString();
            }
        }

        /// <summary>
        /// This method is to make compatible for new SDK(v12) to retrieve message inserted by old SDK(v9).
        ///     1) For the message inserted with v12 SDK, 'message.Body?.ToString()' can return normal message text.
        ///     2) For the message inserted with v9 SDK, it will be encoded as Base64 string.
        /// So use Convert to get normal message text.
        /// </summary>
        public static string GetMessageContent(this QueueMessage message)
        {
            string messageBody = message.Body?.ToString();
            try
            {
                var deserialized = JsonConvert.DeserializeObject(messageBody);
                // Message is normal text which can be deserialized.
                return messageBody;
            }
            catch (JsonException ex)
            {
                CommonLogger.LogWarning("QueueMessage content failed to parse with JsonException. Try to get content from Base64 string. {Error}.", ex);
                byte[] byteArray = Convert.FromBase64String(messageBody);
                return Encoding.UTF8.GetString(byteArray, 0, byteArray.Length);
            }
        }
    }
}
