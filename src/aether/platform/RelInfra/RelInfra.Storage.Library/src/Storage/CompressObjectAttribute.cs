﻿using System;

namespace Microsoft.RelInfra.Storage
{
    /// <summary>
    /// Apply CompressObjectAttribute to string type or other user-defined type field to compress raw field value if neccessary.
    /// After compress the result type would be byte[] and in azure table storage it would be shown as Binary type.
    /// Pay attention: this attribute doesn't apply to field which is byte[] type.
    /// </summary>
    public class CompressObjectAttribute : Attribute
    {
    }
}
