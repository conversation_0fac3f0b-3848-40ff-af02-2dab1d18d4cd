﻿// <copyright file="AzureTableEntityV2.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Reflection;
using Azure.Data.Tables;
using Microsoft.RelInfra.Common.Serialization;
using Microsoft.RelInfra.Storage.Exceptions;
using Microsoft.RelInfra.Instrumentation.Logging;

namespace Microsoft.RelInfra.Storage
{
    internal sealed class AzureTableEntityV2<T>
    {
        private const string PropertyNameForSimpleEntity = "entity";
        // For azure table string type property, the maximum number of characters supported is about 32 K or less.
        private const int MaxStringFieldSize = 32 * 1024;
        // For azure table byte[] type property, the maximum size supported is about 64 K or less.
        private const int MaxByteArrayFieldSize = 64 * 1024;

        private const string TooLargeAzureTableDataMessageFormat = "Input data is too big to fit into Azure table key={0}, row={1}, property={2}, length={3}, propertyNumSections={4}, maxFieldSize={5}";

        private readonly TableEntity _tableEntity;
        private T _entity;

        private static readonly HashSet<Type> s_builtInTypes = new HashSet<Type>
        {
            typeof (string),
            typeof (int),
            typeof (long),
            typeof (double),
            typeof (bool),
            typeof (DateTime),
            typeof (DateTimeOffset),
            typeof (Guid)
        };

        internal static List<EntityPropertyInfo> TypeProperties;

        static AzureTableEntityV2()
        {
            TypeProperties = new List<EntityPropertyInfo>();
            if (!s_builtInTypes.Contains(typeof (T)))
            {
                foreach (var propertyInfo in typeof(T).GetProperties().Where(p => p.CanRead && p.CanWrite))
                {
                    object attribute;
                    var isCompressObject = propertyInfo.GetCustomAttributes(typeof(CompressObjectAttribute)).FirstOrDefault() != null;
                    if ((attribute = propertyInfo.GetCustomAttributes(typeof(LargeObjectExAttribute)).FirstOrDefault()) != null)
                    {
                        TypeProperties.Add(new EntityPropertyInfo
                        {
                            PropertyInfo = propertyInfo,
                            IsLargeObject = true,
                            IsCompressObject = isCompressObject,
                            UseDirectNameAsFirstColumn = true,
                            NumSections = ((LargeObjectExAttribute)attribute).NumSections
                        });
                    }
                    else if ((attribute = propertyInfo.GetCustomAttributes(typeof(LargeObjectAttribute)).FirstOrDefault()) != null)
                    {
                        TypeProperties.Add(new EntityPropertyInfo
                        {
                            PropertyInfo = propertyInfo,
                            IsLargeObject = true,
                            IsCompressObject = isCompressObject,
                            UseDirectNameAsFirstColumn = false,
                            NumSections = ((LargeObjectAttribute)attribute).NumSections
                        });
                    }
                    else
                    {
                        TypeProperties.Add(new EntityPropertyInfo
                        {
                            PropertyInfo = propertyInfo,
                            IsLargeObject = false,
                            IsCompressObject = isCompressObject,
                            UseDirectNameAsFirstColumn = false,
                            NumSections = 0
                        });
                    }
                }
            }
        }

        public AzureTableEntityV2(TableEntity tableEntity)
        {
            _tableEntity = tableEntity;
            _entity = default(T);
        }

        public AzureTableEntityV2(string partitionKey, string rowKey, T entity, string referenceName)
        {
            if (!IsValidTableKey(partitionKey) || !IsValidTableKey(rowKey))
            {
                throw new InvalidTableKeyException(
                    message: $@"Invalid Characters found, rowKey={rowKey}, partitionKey={partitionKey}. Special Characters like / \ # @ | are not allowed.",
                    paramName: "TableKey",
                    source: referenceName);
            }

            _tableEntity = new TableEntity { PartitionKey = partitionKey, RowKey = rowKey };
            _entity = default(T);

            if (s_builtInTypes.Contains(typeof(T)))
            {
                _tableEntity.Add(PropertyNameForSimpleEntity, CreateEntityPropertyFromObject(entity));
            }
            else
            {
                foreach (var entityProperty in TypeProperties)
                {
                    object value = entityProperty.PropertyInfo.GetValue(entity);
                    if (entityProperty.IsLargeObject)
                    {
                        StoreLargeObjectInDTE(_tableEntity, entityProperty, value, referenceName);
                    }
                    else if (entityProperty.IsCompressObject)
                    {
                        StoreCompressObjectInDTE(_tableEntity, entityProperty, value, referenceName);
                    }
                    else
                    {
                        if (value != null)
                        {
                            _tableEntity.Add(entityProperty.PropertyInfo.Name, CreateEntityPropertyFromObject(value));
                        }
                    }
                }
            }
        }

        public TableEntity GetTableEntity()
        {
            return _tableEntity;
        }

        public T ResolveEntity()
        {
            if (_entity != null && !_entity.Equals(default(T)))
            {
                return _entity;
            }

            T obj = default(T);

            if (s_builtInTypes.Contains(typeof(T)))
            {
                if (_tableEntity.TryGetValue(PropertyNameForSimpleEntity, out var prop))
                {
                    return (T)ResolveEntityPropertyForType(prop, typeof(T));
                }
            }
            else
            {
                obj = (T)Activator.CreateInstance(typeof(T));
                foreach (var entityProperty in TypeProperties)
                {
                    if (entityProperty.IsLargeObject)
                    {
                        entityProperty.PropertyInfo.SetValue(obj, ReadLargeObjectFromDTE(_tableEntity, entityProperty));
                    }
                    else if (entityProperty.IsCompressObject)
                    {
                        entityProperty.PropertyInfo.SetValue(obj, ReadCompressObjectFromDTE(_tableEntity, entityProperty));
                    }
                    else
                    {
                        if (_tableEntity.TryGetValue(entityProperty.PropertyInfo.Name, out var prop))
                        {
                            entityProperty.PropertyInfo.SetValue(obj, ResolveEntityPropertyForType(prop, entityProperty.PropertyInfo.PropertyType));
                        }
                        else
                        {
                            entityProperty.PropertyInfo.SetValue(obj, null);
                        }
                    }
                }
            }

            _entity = obj;
            return obj;
        }

        public static IList<string> GetColumnNames()
        {
            return TypeProperties
                .Where(prop => !prop.IsLargeObject).Select(prop => prop.PropertyInfo.Name)
                .Concat(TypeProperties.Where(prop => prop.IsLargeObject).SelectMany(prop =>
                        Enumerable.Range(1, prop.NumSections).Select(i =>prop.PropertyInfo.Name + GetColumnSuffixForLargeObject(i - 1, prop))))
                .ToList();
        }

        private static string GetColumnSuffixForLargeObject(int columnIndex, EntityPropertyInfo entityProperty)
        {
            if (entityProperty.UseDirectNameAsFirstColumn)
            {
                return columnIndex == 0 ? "" : columnIndex.ToString();
            }
            return (columnIndex + 1).ToString();
        }

        private static object ReadLargeObjectFromDTE(TableEntity tableEntity, EntityPropertyInfo entityProperty)
        {
            var serializedObject = String.Empty;
            var compressedBytesList = new List<byte>();

            for (int i = 0; i < entityProperty.NumSections; i++)
            {
                if (tableEntity.TryGetValue(entityProperty.PropertyInfo.Name + GetColumnSuffixForLargeObject(i, entityProperty), out var prop))
                {
                    if (entityProperty.IsCompressObject && (prop.GetType() == typeof(byte[])))
                    {
                        compressedBytesList.AddRange(prop as byte[]);
                    }
                    else
                    {
                        serializedObject += prop;
                    }
                }
            }

            // If object is stored as Binary type in azure table, it means object is compressed and should be decompressed first.
            if (compressedBytesList.Count > 0)
            {
                var stopWatch = Stopwatch.StartNew();
                serializedObject = CompressObjectHelper.Decompress(compressedBytesList.ToArray());
            }

            return !String.IsNullOrEmpty(serializedObject) ? SerializationHelpers.DeserializeEntity(serializedObject, entityProperty.PropertyInfo.PropertyType) : null;
        }

        private static object ReadCompressObjectFromDTE(TableEntity tableEntity, EntityPropertyInfo entityProperty)
        {
            var serializedObject = String.Empty;

            if (tableEntity.TryGetValue(entityProperty.PropertyInfo.Name, out var prop))
            {
                // For compress object if it is stored as Binary type in azure table, it means it has been compressed when stored it.
                if (prop.GetType() == typeof(byte[]))
                {
                    var stopWatch = Stopwatch.StartNew();
                    var propBinary = prop as byte[];
                    serializedObject = CompressObjectHelper.Decompress(propBinary);
                }
                else
                {
                    serializedObject = prop as string;
                }
            }
            else
            {
                return null;
            }

            // Do not need to deserialize string type value to keep consistent with StoreCompressObjectInDTE logic.
            // string type value is not serialized when storing to table.
            return entityProperty.PropertyInfo.PropertyType == typeof(string) ?
                serializedObject :
                !String.IsNullOrEmpty(serializedObject) ? SerializationHelpers.DeserializeEntity(serializedObject, entityProperty.PropertyInfo.PropertyType) : null;
        }

        private static void StoreLargeObjectInDTE(TableEntity tableEntity, EntityPropertyInfo entityProperty, object value, string referenceName)
        {
            var serializedObject = SerializationHelpers.SerializeEntity(value);

            if (serializedObject.Length > entityProperty.NumSections * MaxStringFieldSize)
            {
                if (!entityProperty.IsCompressObject)
                {
                    throw new TooLargeAzureTableDataException(
                        message: String.Format(
                            TooLargeAzureTableDataMessageFormat,
                            tableEntity.PartitionKey, tableEntity.RowKey, entityProperty.PropertyInfo.Name, serializedObject.Length, entityProperty.NumSections, MaxStringFieldSize),
                        paramName: entityProperty.PropertyInfo.Name,
                        entityName: referenceName);
                }

                // Compress serialized object.
                var stopWatch = Stopwatch.StartNew();
                var compressedBytes = CompressObjectHelper.Compress(serializedObject);
                CommonLogger.LogEntityInfo(tableEntity.RowKey, $"StoreLargeObjectInDTE: have applied compress strategy, before compress serializedObject.Length={serializedObject?.Length}, after compress compressedBytes.Length={compressedBytes.Length}. Azure table key={tableEntity.PartitionKey}, property={entityProperty.PropertyInfo.Name}, propertyNumSections={entityProperty.NumSections}, entityProperty.PropertyInfo.PropertyType={entityProperty.PropertyInfo.PropertyType}, entityProperty.IsCompressObject={entityProperty.IsCompressObject}, TimeElapsedTotalMilliseconds={stopWatch.Elapsed.TotalMilliseconds}");

                if (compressedBytes.Length > entityProperty.NumSections * MaxByteArrayFieldSize)
                {
                    throw new TooLargeAzureTableDataException(
                        message: String.Format(
                            TooLargeAzureTableDataMessageFormat,
                            tableEntity.PartitionKey, tableEntity.RowKey, entityProperty.PropertyInfo.Name, compressedBytes.Length, entityProperty.NumSections, MaxByteArrayFieldSize),
                        paramName: entityProperty.PropertyInfo.Name,
                        entityName: referenceName);
                }

                var compressedBytesChunks = SplitLargeBytesToChunks(compressedBytes, entityProperty.NumSections);
                for (int i = 0; i < entityProperty.NumSections; i++)
                {
                    tableEntity.Add(entityProperty.PropertyInfo.Name + GetColumnSuffixForLargeObject(i, entityProperty),
                        compressedBytesChunks[i] != null ? compressedBytesChunks[i].ToArray() : new byte[0]);
                }
            }
            else
            {
                CommonLogger.LogEntityInfo(tableEntity.RowKey, $"StoreLargeObjectInDTE: have not applied compress strategy, serializedObject.Length={serializedObject?.Length}. Azure table key={tableEntity.PartitionKey}, property={entityProperty.PropertyInfo.Name}, propertyNumSections={entityProperty.NumSections}, entityProperty.PropertyInfo.PropertyType={entityProperty.PropertyInfo.PropertyType}, entityProperty.IsCompressObject={entityProperty.IsCompressObject}");

                var serializedObjectChunks = SplitLargeStringToChunks(serializedObject, entityProperty.NumSections);
                for (int i = 0; i < entityProperty.NumSections; i++)
                {
                    tableEntity.Add(entityProperty.PropertyInfo.Name + GetColumnSuffixForLargeObject(i, entityProperty),
                        serializedObjectChunks[i] != null ? CreateEntityPropertyFromObject(serializedObjectChunks[i]) : String.Empty);
                }
            }
        }

        private static void StoreCompressObjectInDTE(TableEntity tableEntity, EntityPropertyInfo entityProperty, object value, string referenceName)
        {
            if (value == null)
            {
                return;
            }

            // Do not serialize string type value since we may use table query to filter table entities by its value (like AscendantNodeList).
            // For example, if a raw string value is empty string, after serialization it would be added with more quotation marks, which may effect table query result.
            var serializedObject = entityProperty.PropertyInfo.PropertyType == typeof(string) ? (string)value : SerializationHelpers.SerializeEntity(value);

            // Compress serialized object if its length exceeds max string size limit.
            if (serializedObject.Length > MaxStringFieldSize)
            {
                var stopWatch = Stopwatch.StartNew();
                var compressedBytes = CompressObjectHelper.Compress(serializedObject);
                CommonLogger.LogEntityInfo(tableEntity.RowKey, $"StoreCompressObjectInDTE: have applied compress strategy, before compress serializedObject.Length={serializedObject?.Length}, after compress compressedBytes.Length={compressedBytes.Length}. Azure table key={tableEntity.PartitionKey}, property={entityProperty.PropertyInfo.Name}, propertyNumSections={entityProperty.NumSections}, entityProperty.PropertyInfo.PropertyType={entityProperty.PropertyInfo.PropertyType}, entityProperty.IsCompressObject={entityProperty.IsCompressObject}, TimeElapsedTotalMilliseconds={stopWatch.Elapsed.TotalMilliseconds}");

                if (compressedBytes.Length > MaxByteArrayFieldSize)
                {
                    throw new TooLargeAzureTableDataException(
                        message: String.Format(
                            TooLargeAzureTableDataMessageFormat,
                            tableEntity.PartitionKey, tableEntity.RowKey, entityProperty.PropertyInfo.Name, compressedBytes.Length, entityProperty.NumSections, MaxByteArrayFieldSize),
                        paramName: entityProperty.PropertyInfo.Name,
                        entityName: referenceName);
                }

                tableEntity.Add(entityProperty.PropertyInfo.Name, compressedBytes);
                // Add a property '{PropertyName}IsCompressed' for compressed column.
                // This column would be used when filter table entities by table query on CompressObject value directly.
                tableEntity.Add(entityProperty.PropertyInfo.Name + CompressObjectHelper.CompressedSuffix, "true");
            }
            else
            {
                CommonLogger.LogEntityInfo(tableEntity.RowKey, $"StoreCompressObjectInDTE: have not applied compress strategy, serializedObject.Length={serializedObject?.Length}. Azure table key={tableEntity.PartitionKey}, property={entityProperty.PropertyInfo.Name}, propertyNumSections={entityProperty.NumSections}, entityProperty.PropertyInfo.PropertyType={entityProperty.PropertyInfo.PropertyType}, entityProperty.IsCompressObject={entityProperty.IsCompressObject}");
                tableEntity.Add(entityProperty.PropertyInfo.Name, CreateEntityPropertyFromObject(serializedObject));
                // If value is not compressed, add false flag to table.
                tableEntity.Add(entityProperty.PropertyInfo.Name + CompressObjectHelper.CompressedSuffix, "false");
            }
        }

        //not clear why this became internal in dotnetcore.
        public static readonly DateTimeOffset MinDateTime = new DateTimeOffset(1601, 1, 1, 0, 0, 0, TimeSpan.Zero);

        /// <summary>
        /// For migration from AzureTableStorage to AzureTableStorageV2.
        /// ie: if an entity is added by AzureTableStorageV2 and retrieved by AzureTableStorage.
        /// Can be deleted after the migration has completed (No data is retrieved by AzureTableStorage).
        /// </summary>
        private static object CreateEntityPropertyFromObject(object value)
        {
            if (value is string)
            {
                return value;
            }
            if (value is bool)
            {
                return value;
            }
            if (value is DateTime)
            {
                var valueToStore = (DateTime)value;
                if (valueToStore < MinDateTime.DateTime)
                {
                    valueToStore = MinDateTime.DateTime;
                }
                return valueToStore;
            }
            if (value is DateTimeOffset)
            {
                var valueToStore = (DateTimeOffset)value;
                if (valueToStore < MinDateTime)
                {
                    valueToStore = MinDateTime;
                }
                return valueToStore;
            }
            if (value is double)
            {
                return value;
            }
            if (value is Guid)
            {
                return value;
            }
            if (value is int)
            {
                return value;
            }
            if (value is long)
            {
                return value;
            }

            try
            {
                return SerializationHelpers.SerializeEntity(value);
            }
            catch (Exception ex)
            {
                Trace.TraceError("Failed to deserialize input value. value: {0}. Exception: {1}", value, ex);
            }

            return value;
        }

        /// <summary>
        /// For migration from AzureTableStorage to AzureTableStorageV2.
        /// ie: if an entity is added by AzureTableStorage and retrieved by AzureTableStorageV2.
        /// Can be deleted after the migration has completed (No data is added by AzureTableStorage).
        /// </summary>
        private object ResolveEntityPropertyForType(object obj, Type objType)
        {
            try
            {
                // if the field is null Azure SDK returns String property type by default
                if (obj == null)
                {
                    return null;
                }
                if (objType == typeof(string))
                {
                    return obj;
                }
                if (objType == typeof(int))
                {
                    return obj;
                }
                if (objType == typeof(int?))
                {
                    return obj;
                }
                if (objType == typeof(long))
                {
                    return obj;
                }
                if (objType == typeof(long?))
                {
                    return obj;
                }
                if (objType == typeof(double))
                {
                    return obj;
                }
                if (objType == typeof(double?))
                {
                    return obj;
                }
                if (objType == typeof(bool))
                {
                    return obj;
                }
                if (objType == typeof(bool?))
                {
                    return obj;
                }
                if (objType == typeof(DateTime))
                {
                    return ResolveDateTimeType(obj, objType);
                }
                if (objType == typeof(DateTime?))
                {
                    return ResolveDateTimeType(obj, objType);
                }
                if (objType == typeof(DateTimeOffset))
                {
                    if (obj is DateTimeOffset && (DateTimeOffset) obj <=MinDateTime)
                    {
                        return DateTimeOffset.MinValue;
                    }

                    return obj;
                }
                if (objType == typeof(DateTimeOffset?))
                {
                    if (obj is DateTimeOffset && (DateTimeOffset)obj <= MinDateTime)
                    {
                        return null;
                    }

                    return obj;
                }
                if (objType == typeof(Guid))
                {
                    return obj;
                }
                if (objType == typeof(Guid?))
                {
                    return obj;
                }
                if (obj is string)
                {
                    // try to deserialize object
                    return SerializationHelpers.DeserializeEntity((string) obj, objType);
                }
            }
            catch (Exception e)
            {
                Trace.TraceError("Could not resolve build in type {0}, ex: {1}", typeof(T), e.Message);
            }

            return default(T);
        }

        private static object ResolveDateTimeType(object obj, Type t)
        {
            if (obj == null) return null;
            if (obj is DateTime) return obj;
            if (obj is DateTimeOffset)
            {
                if ((DateTimeOffset)obj <= MinDateTime)
                {
                    if (t == typeof(DateTime)) return DateTime.MinValue;
                    if (t == typeof(DateTime?)) return null;
                }

                return (object)ReturnDateTimeWithKindFilledIn((DateTimeOffset)obj);
            }

            Trace.TraceWarning($"objType is {t}. But obj type is {obj.GetType()}.");
            return obj;
        }

        private static DateTime ReturnDateTimeWithKindFilledIn(DateTimeOffset offset)
        {
            if (offset.Offset == TimeSpan.Zero)
            {
                return new DateTime(offset.DateTime.Ticks, DateTimeKind.Utc);
            }
            if (offset.LocalDateTime.Ticks == offset.DateTime.Ticks)
            {
                return new DateTime(offset.DateTime.Ticks, DateTimeKind.Local);
            }
            return offset.DateTime;
        }

        private static bool IsValidTableKey(string key)
        {
            return !AzureTableStorage.DisallowedCharsInTableKeys.IsMatch(key);
        }

        private static string[] SplitLargeStringToChunks(string serializedObject, int targetChunkNumber)
        {
            var serializedObjectChunks = new string[targetChunkNumber];

            var fullChunkNumber = serializedObject.Length / MaxStringFieldSize;
            for (int i = 0; i < fullChunkNumber; i++)
            {
                serializedObjectChunks[i] = serializedObject.Substring(i * MaxStringFieldSize, MaxStringFieldSize);
            }

            if (fullChunkNumber < targetChunkNumber)
            {
                serializedObjectChunks[fullChunkNumber] = serializedObject.Substring(fullChunkNumber * MaxStringFieldSize, serializedObject.Length % MaxStringFieldSize);
            }

            return serializedObjectChunks;
        }

        private static IEnumerable<byte>[] SplitLargeBytesToChunks(byte[] compressedBytes, int targetChunkNumber)
        {
            var compressedBytesChunks = new IEnumerable<byte>[targetChunkNumber];

            var fullChunkNumber = compressedBytes.Length / MaxByteArrayFieldSize;
            for (int i = 0; i < fullChunkNumber; i++)
            {
                compressedBytesChunks[i] = compressedBytes.Skip(i * MaxByteArrayFieldSize).Take(MaxByteArrayFieldSize);
            }

            if (fullChunkNumber < targetChunkNumber)
            {
                compressedBytesChunks[fullChunkNumber] = compressedBytes.Skip(fullChunkNumber * MaxByteArrayFieldSize);
            }

            return compressedBytesChunks;
        }
    }
}
