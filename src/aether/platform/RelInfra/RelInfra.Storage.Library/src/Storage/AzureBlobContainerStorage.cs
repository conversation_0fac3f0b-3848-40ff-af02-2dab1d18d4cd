﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.RelInfra.Common.RetryPolicy;
using Microsoft.RelInfra.Instrumentation;

namespace Microsoft.RelInfra.Storage
{
    public class AzureBlobContainerStorage : IAzureBlobContainerStorage
    {
        public const int PageBlobPageSize = AzureBlobStorage.PageBlobPageSize;
        private readonly string _containerName;
        private readonly IAzureBlobStorage _blobStorage;

        public IRelInfraRetryPolicy RetryPolicy
        {
            get { return _blobStorage.RetryPolicy; }
            set { _blobStorage.RetryPolicy = value; }
        }

        public AzureBlobContainerStorage(string connectionString, string containerName, int numRetries, TimeSpan intervalBetweenRetries, CounterManager counterManager)
            : this(containerName: containerName,
                   blobStorage: new AzureBlobStorage(connectionString: connectionString, numRetries: numRetries, intervalBetweenRetries: intervalBetweenRetries, counterManager: counterManager))
        {
        }

        public AzureBlobContainerStorage(IAzureBlobStorage blobStorage, string containerName)
        {
            _containerName = containerName ?? throw new ArgumentNullException(paramName: nameof(_containerName));
            _blobStorage = blobStorage;
        }

        public Task<string> AcquireLeaseAsync(string blobName, TimeSpan? leaseTime, string proposedLeaseId, CancellationToken cancellationToken)
        {
            return _blobStorage.AcquireLeaseAsync(containerName: _containerName, blobName: blobName, leaseTime: leaseTime, proposedLeaseId: proposedLeaseId, cancellationToken: cancellationToken);
        }

        public Task DeleteAsync(string resourceId)
        {
            return _blobStorage.DeleteAsync(containerName: _containerName, resourceId: resourceId);
        }

        public Task<bool> ResourceExistsAsync(string blobName)
        {
            return _blobStorage.ResourceExistsAsync(containerName: _containerName, blobName: blobName);
        }

        public Task TryPutAndIgnoreEtagAsync(string blobName, Storable<string> storable)
        {
            return _blobStorage.TryPutAndIgnoreEtagAsync(containerName: _containerName, resourceId: blobName, resource: storable);
        }

        public async Task InitializeAsync()
        {
            await _blobStorage.InitializeAsync(_containerName);
        }

        public Uri GetBlobUri(string blobName, DateTimeOffset? expiryDate = null)
        {
            return _blobStorage.GetBlobUri(containerName: _containerName, blobName: blobName, expiryDate: expiryDate);
        }

        public Task<Storable<T>> GetAsync<T>(string resourceId)
        {
            return _blobStorage.GetAsync<T>(containerName: _containerName, resourceId: resourceId);
        }

        public Task ReleaseLeaseAsync(string blobName, string leaseId, CancellationToken cancellationToken)
        {
            return _blobStorage.ReleaseLeaseAsync(containerName: _containerName, blobName: blobName, leaseId: leaseId, cancellationToken);
        }

        public Task RenewLeaseAsync(string blobName, string leaseId, CancellationToken cancellationToken)
        {
            return _blobStorage.RenewLeaseAsync(containerName: _containerName, blobName: blobName, leaseId: leaseId, cancellationToken: cancellationToken);
        }

        public Task<Storable<T>> TryGetAsync<T>(string resourceId, CancellationToken cancellationToken)
        {
            return _blobStorage.TryGetAsync<T>(containerName: _containerName, resourceId: resourceId, cancellationToken: cancellationToken);
        }

        public Task<IEnumerable<Storable<T>>> BatchGetAsync<T>(IEnumerable<string> resourceIds)
        {
            return _blobStorage.BatchGetAsync<T>(containerName:_containerName, resourceIds: resourceIds);
        }

        public Task<Storable<string>> AppendAsync<T>(string resourceId, T resource)
        {
            return _blobStorage.AppendAsync(containerName: _containerName, resourceId: resourceId, resource: resource);
        }

        public Task<Storable<T>> GetBlobEntityAsync<T>(string resourceId)
        {
            return _blobStorage.GetBlobEntityAsync<T>(containerName: _containerName, resourceId: resourceId);
        }

        public Task<Storable<T>> GetBlobEntityAsync<T>(string resourceId, CancellationToken cancellationToken)
        {
            return _blobStorage.GetBlobEntityAsync<T>(containerName: _containerName, resourceId: resourceId, cancellationToken: cancellationToken);
        }

        public Task<string> GetBlobMetadataAsync(string resourceId, string metadataKey)
        {
            return _blobStorage.GetBlobMetadataAsync(containerName: _containerName, resourceId: resourceId, metadataKey: metadataKey);
        }

        public Task SetBlobMetadataAsync(string resourceId, string metadataKey, string metadataValue)
        {
            return _blobStorage.SetBlobMetadataAsync(containerName: _containerName, resourceId: resourceId, metadataKey: metadataKey, metadataValue: metadataValue);
        }

        public Task<Storable<string>> GetBlobStringAsync(string resourceId)
        {
            return _blobStorage.GetBlobStringAsync(containerName: _containerName, resourceId: resourceId);
        }

        public Task<Storable<string>> GetBlobStringAsync(string resourceId, CancellationToken cancellationToken)
        {
            return _blobStorage.GetBlobStringAsync(containerName: _containerName, resourceId: resourceId, cancellationToken: cancellationToken);
        }

        public Uri GetContainerReadUri(DateTimeOffset? expiryDate = null)
        {
            return _blobStorage.GetContainerReadUri(containerName: _containerName, expiryDate: expiryDate);
        }

        public Uri GetWritableBlobUri(string blobName, DateTimeOffset expiryDate)
        {
            return _blobStorage.GetWritableBlobUri(containerName:_containerName, blobName: blobName, expiryDate: expiryDate);
        }

        public Task<bool> TryUploadStreamWithTypeAsync(string resourceId, Stream stream, string contentType)
        {
            return _blobStorage.TryUploadStreamWithTypeAsync(containerName: _containerName, 
                                                             resourceId: resourceId, 
                                                             stream: stream, 
                                                             contentType: contentType);
        }

        public Task<IEnumerable<string>> ListDirectoryAsync(string path, int fileCountLimit, CancellationToken cancellationToken, string prefixFilter)
        {
            return _blobStorage.ListDirectoryAsync(containerName: _containerName, 
                                                   path: path,
                                                   fileCountLimit: fileCountLimit,
                                                   cancellationToken: cancellationToken, 
                                                   prefixFilter: prefixFilter);
        }

        public Task<QueryResults> ListDirectoryWithContinuationTokenAsync(string path, string continuationToken, int takeCount, CancellationToken cancellationToken, bool useFlatBlobListing = true)
        {
            return _blobStorage.ListDirectoryWithContinuationTokenAsync(containerName: _containerName, 
                                                                        path:path, 
                                                                        continuationToken: continuationToken,
                                                                        cancellationToken:cancellationToken,
                                                                        takeCount: takeCount,
                                                                        useFlatBlobListing: useFlatBlobListing);
        }

        public Task<Storable<T>> PutWithETagAsync<T>(string resourceId, Storable<T> resource)
        {
            return _blobStorage.PutWithETagAsync(containerName: _containerName, resourceId: resourceId, resource: resource);
        }

        public Task DownloadToFileAsync(string blobName, string fileLocation, CancellationToken cancellationToken)
        {
            return _blobStorage.DownloadToFileAsync(containerName: _containerName, 
                                                    blobName: blobName, 
                                                    fileLocation: fileLocation, 
                                                    cancellationToken: cancellationToken);
        }

        public Task UploadFileAsync(string blobName, string fileLocation, CancellationToken cancellationToken)
        {
            return _blobStorage.UploadFileAsync(containerName: _containerName, blobName: blobName, fileLocation: fileLocation, cancellationToken: cancellationToken);
        }

        public Task<long> GetBlobSizeAsync(string resourceId)
        {
            return _blobStorage.GetBlobSizeAsync(containerName: _containerName, resourceId: resourceId);
        }

        public Task<Storable<T>> PutAndIgnoreEtagAsync<T>(string resourceId, Storable<T> resource)
        {
            return _blobStorage.PutAndIgnoreEtagAsync(containerName: _containerName, resourceId: resourceId, resource: resource);
        }

        public Task<bool> TryDeleteAsync(string resourceId)
        {
            return _blobStorage.TryDeleteAsync(containerName: _containerName, resourceId: resourceId);
        }
    }
}
