﻿using Microsoft.RelInfra.Common.RetryPolicy;
using Microsoft.RelInfra.Common.Serialization;
using Microsoft.RelInfra.Extensions;
using Microsoft.RelInfra.Instrumentation;
using Microsoft.RelInfra.Instrumentation.Exceptions;
using Microsoft.RelInfra.Instrumentation.Logging;
using Microsoft.RelInfra.Storage.Exceptions;
using Microsoft.RelInfra.Storage.Utils;
using Microsoft.WindowsAzure.Storage;
using Microsoft.WindowsAzure.Storage.Auth;
using Microsoft.WindowsAzure.Storage.Blob;
using Microsoft.WindowsAzure.Storage.RetryPolicies;
using Microsoft.WindowsAzure.Storage.Shared.Protocol;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Xml;

namespace Microsoft.RelInfra.Storage
{
    public class AzureBlobStorage : IRelInfraStreamableStorage, IAzureBlobStorage
    {
        public const int PageBlobPageSize = 512;
        public const int FourMbytes = 4 * 1024 * 1024;
        private readonly CloudBlobClient _blobClient;
        private readonly OptionalCounters _counters;
        private const int DefaultBlobTimeoutInSeconds = 10;

        public IRelInfraRetryPolicy RetryPolicy { get; set; }
        public string ContainerName { get; private set; }

        public IRetryPolicy AzureRetryPolicy
        {
            get { return _blobClient.DefaultRequestOptions.RetryPolicy; }
            set { _blobClient.DefaultRequestOptions.RetryPolicy = value; }
        }

        public AzureBlobStorage(string connectionString, int numRetries, TimeSpan intervalBetweenRetries)
            : this(connectionString: connectionString, numRetries: numRetries, intervalBetweenRetries: intervalBetweenRetries, counterManager: null)
        {
        }

        public AzureBlobStorage(string connectionString, int numRetries, TimeSpan intervalBetweenRetries, CounterManager counterManager)
        {
            CloudStorageAccount storageAccount = CloudStorageAccount.Parse(connectionString);
            _blobClient = storageAccount.CreateCloudBlobClient();

            _counters = new OptionalCounters(counterManager, _blobClient.Credentials.AccountName);

            RetryPolicy = new LinearRetryPolicy(intervalBetweenRetries, numRetries);

            CommonLogger.LogEntityInfo(nameof(AzureBlobStorage), $"Construct client with connection string. AccountName: [{storageAccount.Credentials?.AccountName}].");
        }

        public AzureBlobStorage(StorageCredentials credentials, int numRetries, TimeSpan intervalBetweenRetries, CounterManager counterManager)
        {
            CloudStorageAccount storageAccount = new CloudStorageAccount(storageCredentials: credentials, useHttps: true);
            _blobClient = storageAccount.CreateCloudBlobClient();

            _counters = new OptionalCounters(counterManager, _blobClient.Credentials.AccountName);

            RetryPolicy = new LinearRetryPolicy(intervalBetweenRetries, numRetries);
        }

        public AzureBlobStorage(CloudStorageAccount storageAccount, int numRetries, TimeSpan intervalBetweenRetries, CounterManager counterManager)
        {
            _blobClient = storageAccount.CreateCloudBlobClient();
            _counters = new OptionalCounters(counterManager, _blobClient.Credentials.AccountName);
            RetryPolicy = new LinearRetryPolicy(intervalBetweenRetries, numRetries);
        }

        public async Task InitializeAsync(string containerName)
        {
            _counters.IncrementRateCounter("AzureBlob InitializeContainerRate");
            using (_counters.CreateDisposableTimer("AzureBlob InitializeContainerLatency"))
            {
                try
                {

                    if (String.IsNullOrWhiteSpace(containerName))
                    {
                        throw new ArgumentException("containerName can't be null or empty", nameof(containerName));
                    }

                    CloudBlobContainer container = _blobClient.GetContainerReference(containerName);
                    await container.CreateIfNotExistsAsync();
                    ContainerName = containerName;
                }
                catch
                {
                    _counters.IncrementRateCounter("AzureBlob InitializeContainerFailureRate");
                    throw;
                }
            }
        }

        public void InitializeForAlreadyExistingContainer(string containerName)
        {
            ContainerName = containerName;
        }

        public Uri GetBlobUri(string containerName, string blobName, DateTimeOffset? expiryDate = null)
        {
            if (String.IsNullOrWhiteSpace(containerName))
            {
                throw new ArgumentException("containerName can't be null or empty", nameof(containerName));
            }

            if (String.IsNullOrWhiteSpace(blobName))
            {
                throw new ArgumentException("blobName can't be null or empty", nameof(blobName));
            }

            CloudBlobContainer container = _blobClient.GetContainerReference(containerName);
            CloudBlob blob = container.GetBlobReference(blobName);

            Uri blobUri;
            if (expiryDate != null)
            {
                var sasToken = blob.GetSharedAccessSignature(new SharedAccessBlobPolicy
                {
                    Permissions = SharedAccessBlobPermissions.Read,
                    SharedAccessExpiryTime = expiryDate
                });

                blobUri = new Uri(blob.Uri, sasToken);
            }
            else
            {
                blobUri = blob.Uri;
            }

            return blobUri;
        }

        public Uri GetContainerReadUri(string containerName, DateTimeOffset? expiryDate = null)
        {
            if (String.IsNullOrWhiteSpace(containerName))
            {
                throw new ArgumentException("containerName can't be null or empty", nameof(containerName));
            }

            CloudBlobContainer container = _blobClient.GetContainerReference(containerName);

            Uri containerReadUri;
            if (expiryDate != null)
            {
                var sasToken = container.GetSharedAccessSignature(new SharedAccessBlobPolicy
                {
                    Permissions = SharedAccessBlobPermissions.Read | SharedAccessBlobPermissions.List,
                    SharedAccessExpiryTime = expiryDate
                });

                containerReadUri = new Uri(container.Uri, sasToken);
            }
            else
            {
                containerReadUri = container.Uri;
            }

            return containerReadUri;
        }

        public Uri GetContainerWritableUri(string containerName, DateTimeOffset? expiryDate = null)
        {
            if (String.IsNullOrWhiteSpace(containerName))
            {
                throw new ArgumentException("containerName can't be null or empty", nameof(containerName));
            }

            CloudBlobContainer container = _blobClient.GetContainerReference(containerName);

            Uri containerWriteUri;
            if (expiryDate != null)
            {
                var sasToken = container.GetSharedAccessSignature(new SharedAccessBlobPolicy
                {
                    Permissions = SharedAccessBlobPermissions.Write | SharedAccessBlobPermissions.List | SharedAccessBlobPermissions.Read,
                    SharedAccessExpiryTime = expiryDate
                });

                containerWriteUri = new Uri(container.Uri, sasToken);
            }
            else
            {
                containerWriteUri = container.Uri;
            }

            return containerWriteUri;
        }

        public Uri GetWritableBlobUri(string containerName, string blobName, DateTimeOffset expiryDate)
        {
            if (String.IsNullOrWhiteSpace(containerName))
            {
                throw new ArgumentException("containerName can't be null or empty", nameof(containerName));
            }

            if (String.IsNullOrWhiteSpace(blobName))
            {
                throw new ArgumentException("blobName can't be null or empty", nameof(blobName));
            }

            CloudBlobContainer container = _blobClient.GetContainerReference(containerName);
            CloudBlob blob = container.GetBlobReference(blobName);

            var sasToken = blob.GetSharedAccessSignature(new SharedAccessBlobPolicy
            {
                Permissions = SharedAccessBlobPermissions.Read | SharedAccessBlobPermissions.Write,
                SharedAccessExpiryTime = expiryDate
            });

            return new Uri(blob.Uri, sasToken);
        }

        public async Task<bool> ExistsAsync(string containerName, string resourceId)
        {
            return await ExistsAsync(containerName, resourceId, CancellationToken.None);
        }

        public async Task<bool> ExistsAsync(string containerName, string resourceId, CancellationToken token)
        {
            _counters.IncrementRateCounter("AzureBlob ExistsRate");
            using (_counters.CreateDisposableTimer("AzureBlob ExistsLatency"))
            {
                try
                {
                    CloudBlobContainer container = _blobClient.GetContainerReference(containerName);
                    CloudBlob blob = container.GetBlobReference(resourceId);
                    var options = new BlobRequestOptions() { RetryPolicy = new AzureRetryPolicy(RetryPolicy, additionalInfo: $"{containerName}") };
                    var ctx = new OperationContext();
                    return await blob.ExistsAsync(options, ctx, token);
                }
                catch (Exception ex) //aggregate exception?
                {
                    string log =
                        $"Failed to check entity existance with id {resourceId} to blob in container {containerName}";
                    Trace.TraceError("{0}, exception: {1}", log, ex);
                    _counters.IncrementRateCounter("AzureBlob ExistsFailureRate");
                    if (ex is StorageException storageEx)
                    {
                        throw new RelInfraStorageException(log, storageEx, StorageErrorHelper.GetError(storageEx.RequestInformation.HttpStatusCode)).Format();
                    }
                    else if (ex is TaskCanceledException)
                    {
                        throw new TimeoutException("cancellation timeout and retry limit reached", ex);
                    }
                    else
                    {
                        throw;
                    }
                }
            }
        }

        public async Task<bool> CanAccessAsync()
        {
            return await CanAccessAsync(CancellationToken.None);
        }


        public async Task<bool> CanAccessAsync(CancellationToken token)
        {
            try
            {
                await ExistsAsync("test", "test", token);
            }
            catch (Exception ex) when (ex is RelInfraStorageException || ex is TimeoutException)
            {
                return false;
            }

            return true;
        }

        public async Task<Storable<T>> AddAsync<T>(string containerName, string resourceId, Storable<T> resource)
        {
            return await AddAsync(containerName, resourceId, resource, CancellationToken.None);
        }

        public async Task<Storable<T>> AddAsync<T>(string containerName, string resourceId, Storable<T> resource, CancellationToken cancellationToken)
        {
            _counters.IncrementRateCounter("AzureBlob AddRate");
            using (_counters.CreateDisposableTimer("AzureBlob AddLatency"))
            {
                try
                {
                    CloudBlobContainer container = _blobClient.GetContainerReference(containerName);

                    CloudBlockBlob blob = container.GetBlockBlobReference(resourceId);
                    using (var ms = new MemoryStream(Encoding.UTF8.GetBytes(SerializationHelpers.SerializeEntity(resource.Entity))))
                    {
                        AccessCondition ac = AccessCondition.GenerateIfNoneMatchCondition("*");
                        var options = new BlobRequestOptions() { RetryPolicy = new AzureRetryPolicy(RetryPolicy, additionalInfo: $"{containerName}") };
                        var ctx = new OperationContext();

                        await blob.UploadFromStreamAsync(ms, ac, options, ctx, cancellationToken);
                        resource.Id = resourceId;
                        resource.Etag = blob.Properties.ETag;

                        return resource;
                    }
                }
                catch (StorageException ex)
                {
                    string log = $"Failed to upload entity with id {resourceId} to blob in container {containerName}";
                    Trace.TraceError("{0}, exception: {1}", log, ex);
                    _counters.IncrementRateCounter("AzureBlob AddFailureRate");

                    throw new RelInfraStorageException(log, ex, StorageErrorHelper.GetError(ex.RequestInformation.HttpStatusCode)).Format();
                }
            }
        }

        /// <summary>
        /// creates a page blob, throws if it already exists. actual size created for the blob
        /// will be rounded up to the nearest modulo of 512.
        /// </summary>
        /// <param name="containerName"></param>
        /// <param name="resourceId"></param>
        /// <param name="size"></param>
        /// <returns></returns>
        public async Task AddPageBlobAsync(string containerName, string resourceId, long size)
        {
            _counters.IncrementRateCounter("AzureBlob AddPageBlobRate");
            using (_counters.CreateDisposableTimer("AzureBlob AddPageBlobLatency"))
            {
                try
                {
                    CloudBlobContainer container = _blobClient.GetContainerReference(containerName);

                    CloudPageBlob blob = container.GetPageBlobReference(resourceId);

                    AccessCondition notExistsAccessCondition = AccessCondition.GenerateIfNotExistsCondition();
                    BlobRequestOptions requestOptions = new BlobRequestOptions { RetryPolicy = new AzureRetryPolicy(RetryPolicy, additionalInfo: $"{containerName}") };

                    await blob.CreateAsync(size: RoundUpToPageBlobSize(size), accessCondition: notExistsAccessCondition,
                        options: requestOptions, operationContext: null);
                }
                catch (StorageException ex)
                {
                    string log = $"Failed to create page blob with id {resourceId} to blob in container {containerName}";
                    Trace.TraceError("{0}, exception: {1}", log, ex);
                    _counters.IncrementRateCounter("AzureBlob AddPageBlobFailureRate");

                    throw new RelInfraStorageException(log, ex, StorageErrorHelper.GetError(ex.RequestInformation.HttpStatusCode)).Format();
                }
            }
        }

        public async Task ResizePageBlobAsync(string containerName, string resourceId, long size)
        {
            _counters.IncrementRateCounter("AzureBlob ResizePageBlobRate");
            using (_counters.CreateDisposableTimer("AzureBlob ResizePageBlobLatency"))
            {
                try
                {
                    CloudBlobContainer container = _blobClient.GetContainerReference(containerName);

                    CloudPageBlob blob = container.GetPageBlobReference(resourceId);

                    AccessCondition ac = AccessCondition.GenerateEmptyCondition();
                    BlobRequestOptions requestOptions = new BlobRequestOptions { RetryPolicy = new AzureRetryPolicy(RetryPolicy, additionalInfo: $"{containerName}") };

                    await blob.ResizeAsync(size: RoundUpToPageBlobSize(size), accessCondition: ac,
                        options: requestOptions, operationContext: null);
                }
                catch (StorageException ex)
                {
                    string log = $"Failed to resize page blob with id {resourceId} to blob in container {containerName}";
                    Trace.TraceError("{0}, exception: {1}", log, ex);
                    _counters.IncrementRateCounter("AzureBlob ResizePageBlobFailureRate");

                    throw new RelInfraStorageException(log, ex, StorageErrorHelper.GetError(ex.RequestInformation.HttpStatusCode)).Format();
                }
            }
        }

        /// <summary>
        /// upload the entire buffer to the blob, starting position for the blob is blobOffset
        /// buffer size must be modulo of 512
        /// </summary>
        /// <param name="containerName"></param>
        /// <param name="resourceId"></param>
        /// <param name="buffer"></param>
        /// <param name="blobOffset"></param>
        /// <param name="bufferOffset"></param>
        /// <param name="length"></param>
        /// <returns></returns>
        public async Task PutPageBlobPageAsync(string containerName, string resourceId, byte[] buffer, long blobOffset, int bufferOffset, int length)
        {
            if (length % PageBlobPageSize > 0)
            {
                throw new ArgumentException("putpage buffer size must be modulo of PageBlobPageSize(512 bytes)");
            }

            _counters.IncrementRateCounter("AzureBlob PutPageRate");
            using (_counters.CreateDisposableTimer("AzureBlob PutPageLatency"))
            {
                try
                {
                    CloudBlobContainer container = _blobClient.GetContainerReference(containerName);

                    CloudPageBlob blob = container.GetPageBlobReference(resourceId);

                    int bufferEnd = bufferOffset + length;

                    while (bufferOffset < bufferEnd)
                    {
                        int sizeToTransfer = Math.Min(FourMbytes, bufferEnd - bufferOffset);
                        using (MemoryStream memoryStream = new MemoryStream(buffer,
                            index: bufferOffset, count: sizeToTransfer, writable: false, publiclyVisible: false))
                        {
                            await blob.WritePagesAsync(memoryStream, blobOffset, null);
                            blobOffset += sizeToTransfer;
                            bufferOffset += sizeToTransfer;
                        }
                    }
                }
                catch (StorageException ex)
                {
                    string log = $"Failed to put page blob with id {resourceId} to blob in container {containerName}, offset {blobOffset}";
                    Trace.TraceError("{0}, exception: {1}", log, ex);
                    _counters.IncrementRateCounter("AzureBlob AddFailureRate");

                    throw new RelInfraStorageException(log, ex, StorageErrorHelper.GetError(ex.RequestInformation.HttpStatusCode)).Format();

                }
            }
        }

        // page blob size must be modulo of 512
        public static long RoundUpToPageBlobSize(long size)
        {
            return (size + PageBlobPageSize - 1) & ~(PageBlobPageSize - 1);
        }

        public async Task<Storable<string>> AppendAsync<T>(string containerName, string resourceId, T resource)
        {
            string resourceAsString = resource.ToString();
            using (var resourceAsStream = new MemoryStream(Encoding.UTF8.GetBytes(resourceAsString)))
            {
                var eTag = await AppendStreamInternalAsync(containerName, resourceId, resourceAsStream);
                return new Storable<string>
                {
                    Id = resourceId,
                    ContainerId = containerName,
                    Etag = eTag,
                    Entity = resourceAsString
                };
            }
        }

        public virtual async Task AppendStreamAsync(string containerName, string resourceId, Stream dataStream)
        {
            await AppendStreamInternalAsync(containerName, resourceId, dataStream);
        }

        private async Task<string> AppendStreamInternalAsync(string containerName, string resourceId, Stream dataStream)
        {
            _counters.IncrementRateCounter("AzureBlob AppendRate");
            using (_counters.CreateDisposableTimer("AzureBlob AppendLatency"))
            {
                try
                {
                    CloudBlobContainer container = _blobClient.GetContainerReference(containerName);
                    CloudAppendBlob blob = container.GetAppendBlobReference(resourceId);
                    BlobRequestOptions requestOptions = new BlobRequestOptions { RetryPolicy = new AzureRetryPolicy(RetryPolicy, additionalInfo: $"{containerName}") };
                    try
                    {
                        AccessCondition notExistsAccessCondition = AccessCondition.GenerateIfNotExistsCondition();
                        using (var cancellationTokenSource = new CancellationTokenSource(TimeSpan.FromSeconds(DefaultBlobTimeoutInSeconds)))
                        {
                            await blob.CreateOrReplaceAsync(notExistsAccessCondition, requestOptions, new OperationContext(), cancellationTokenSource.Token);
                        }
                    }
                    catch (StorageException)
                    {
                        // swallow storage exception as it expected if the blob already exists
                    }

                    AccessCondition emptyAccessCondition = AccessCondition.GenerateEmptyCondition();

                    // AppendBlockAsync doesn't like empty stream so we need to read off chunks and Upload
                    const int oneMb = 1024 * 1024;
                    byte[] buffer = new byte[oneMb];
                    int count;
                    do
                    {
                        using (var cancellationTokenSource = new CancellationTokenSource(TimeSpan.FromSeconds(DefaultBlobTimeoutInSeconds)))
                        {
                            count = await dataStream.ReadAsync(buffer: buffer, offset: 0, count: oneMb, cancellationTokenSource.Token);
                        }

                        if (count > 0)
                        {
                            using (MemoryStream ms = new MemoryStream())
                            {
                                await ms.WriteAsync(buffer: buffer, offset: 0, count: count);
                                ms.Position = 0;
                                await
                                    blob.AppendBlockAsync(blockData: ms, contentMD5: null,
                                        accessCondition: emptyAccessCondition, options: requestOptions,
                                        operationContext: new OperationContext(), cancellationToken: CancellationToken.None);
                            }
                        }
                    } while (dataStream.CanRead && count > 0);

                    return blob.Properties?.ETag;
                }
                catch (StorageException ex)
                {
                    string log = $"Failed to append entity with id {resourceId} to blob in container {containerName}";
                    Trace.TraceError("{0}, exception: {1}", log, ex);
                    _counters.IncrementRateCounter("AzureBlob AppendFailureRate");

                    throw new RelInfraStorageException(log, ex, StorageErrorHelper.GetError(ex.RequestInformation.HttpStatusCode)).Format();
                }
            }
        }

        /// <summary>
        ///     DO NOT USE, IT DOESNT CHECK ETAG. WILL BE DEPRECATED
        ///     Use PutWithEtagSync();
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="containerName"></param>
        /// <param name="resourceId"></param>
        /// <param name="resource"></param>
        /// <returns></returns>
        public async Task<bool> TryPutAndIgnoreEtagAsync<T>(string containerName, string resourceId, Storable<T> resource)
        {
            try
            {
                await PutAndIgnoreEtagAsync(containerName, resourceId, resource);
            }
            catch (RelInfraStorageException)
            {
                return false;
            }
            return true;
        }

        /// <summary>
        ///     DO NOT USE, IT DOESNT CHECK ETAG. WILL BE DEPRECATED
        ///     Use PutWithEtagAsync();
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="containerName"></param>
        /// <param name="resourceId"></param>
        /// <param name="resource"></param>
        /// <returns></returns>
        public async Task<Storable<T>> PutAndIgnoreEtagAsync<T>(string containerName, string resourceId, Storable<T> resource)
        {
            using (var cancellationTokenSource = new CancellationTokenSource(TimeSpan.FromSeconds(DefaultBlobTimeoutInSeconds)))
            {
                return await PutAndIgnoreEtagAsync(containerName, resourceId, resource, cancellationTokenSource.Token);
            }
        }

        public async Task<Storable<T>> PutAndIgnoreEtagAsync<T>(string containerName, string resourceId, Storable<T> resource, CancellationToken cancellationToken)
        {
            _counters.IncrementRateCounter("AzureBlob PutRate");
            using (_counters.CreateDisposableTimer("AzureBlob PutLatency"))
            {
                try
                {
                    CloudBlobContainer container = _blobClient.GetContainerReference(containerName);
                    CloudBlockBlob blob = container.GetBlockBlobReference(resourceId);
                    BlobRequestOptions requestOptions = new BlobRequestOptions { RetryPolicy = new AzureRetryPolicy(RetryPolicy, additionalInfo: $"{containerName}") };
                    using (var ms = new MemoryStream(Encoding.UTF8.GetBytes(SerializationHelpers.SerializeEntity(resource.Entity))))
                    {
                        await blob.UploadFromStreamAsync(ms, accessCondition: null, options: requestOptions, operationContext: null, cancellationToken: cancellationToken);
                        resource.Id = resourceId;
                        resource.Etag = blob.Properties.ETag;

                        return resource;
                    }
                }
                catch (StorageException ex)
                {
                    string log = $"Failed to upload entity with id {resourceId} to blob in container {containerName}";
                    Trace.TraceError("{0}, exception: {1}", log, ex);
                    _counters.IncrementRateCounter("AzureBlob PutFailureRate");

                    throw new RelInfraStorageException(log, ex, StorageErrorHelper.GetError(ex.RequestInformation.HttpStatusCode)).Format();
                }
            }
        }

        public async Task<Storable<T>> PutWithETagAsync<T>(string containerName, string resourceId, Storable<T> resource)
        {
            _counters.IncrementRateCounter("AzureBlob PutWithETagRate");
            using (_counters.CreateDisposableTimer("AzureBlob PutWithETagLatency"))
            {
                try
                {
                    CloudBlobContainer container = _blobClient.GetContainerReference(containerName);
                    CloudBlockBlob blob = container.GetBlockBlobReference(resourceId);
                    var buffer = Encoding.UTF8.GetBytes(SerializationHelpers.SerializeEntity(resource.Entity));

                    using (var cancellationTokenSource = new CancellationTokenSource(TimeSpan.FromSeconds(DefaultBlobTimeoutInSeconds)))
                    {
                        await blob.UploadFromByteArrayAsync(buffer: buffer,
                                                        index: 0,
                                                        count: buffer.Length,
                                                        accessCondition: AccessCondition.GenerateIfMatchCondition(resource.Etag),
                                                        options: new BlobRequestOptions { RetryPolicy = new AzureRetryPolicy(RetryPolicy, additionalInfo: $"{containerName}") },
                                                        operationContext: null,
                                                        cancellationToken: cancellationTokenSource.Token);
                    }

                    resource.Id = resourceId;
                    resource.Etag = blob.Properties.ETag;
                    return resource;
                }
                catch (StorageException ex)
                {
                    string log = $"Failed to upload entity with id {resourceId} to blob in container {containerName}";
                    Trace.TraceError("{0}, exception: {1}", log, ex);
                    _counters.IncrementRateCounter("AzureBlob PutWithETagFailureRate");

                    throw new RelInfraStorageException(log, ex, StorageErrorHelper.GetError(ex.RequestInformation.HttpStatusCode)).Format();
                }
            }
        }

        public Task<bool> TryBatchPutAsync<T>(string containerName, IEnumerable<Storable<T>> resources)
        {
            throw new NotImplementedException();
        }

        public Task<IEnumerable<Storable<T>>> BatchPutAsync<T>(string containerName, IEnumerable<Storable<T>> resources)
        {
            throw new NotImplementedException();
        }

        /// <summary>
        /// write a block inside a block blob. blockId need to be base64 encoded
        /// all blockIds for the same blob must have equal length
        /// </summary>
        /// <param name="containerName"></param>
        /// <param name="resourceId"></param>
        /// <param name="blockId"></param>
        /// <param name="blockData"></param>
        /// <returns></returns>
        public async Task PutBlockAsync(string containerName, string resourceId, string blockId, Stream blockData)
        {
            _counters.IncrementRateCounter("AzureBlob PutBlockRate");
            using (_counters.CreateDisposableTimer("AzureBlob PutBlockLatency"))
            {
                try
                {
                    CloudBlobContainer container = _blobClient.GetContainerReference(containerName);
                    CloudBlockBlob blob = container.GetBlockBlobReference(resourceId);
                    await blob.PutBlockAsync(blockId, blockData, null);
                }
                catch (StorageException ex)
                {
                    string log = $"Failed to put block with id {resourceId} blockId {blockId} to blob in container {containerName}";
                    Trace.TraceError("{0}, exception: {1}", log, ex);
                    _counters.IncrementRateCounter("AzureBlob PutBlockFailureRate");

                    throw new RelInfraStorageException(log, ex, StorageErrorHelper.GetError(ex.RequestInformation.HttpStatusCode)).Format();
                }
            }
        }

        /// <summary>
        /// commits list of blockIds for a block blob
        /// </summary>
        /// <param name="containerName"></param>
        /// <param name="resourceId"></param>
        /// <param name="blockList"></param>
        /// <param name="etag"></param>
        /// <returns></returns>
        public async Task PutBlockListAsync(string containerName, string resourceId, IEnumerable<string> blockList, string etag)
        {
            _counters.IncrementRateCounter("AzureBlob PutBlockListRate");
            using (_counters.CreateDisposableTimer("AzureBlob PutBlockListLatency"))
            {
                try
                {
                    CloudBlobContainer container = _blobClient.GetContainerReference(containerName);
                    CloudBlockBlob blob = container.GetBlockBlobReference(resourceId);
                    BlobRequestOptions requestOptions = new BlobRequestOptions { RetryPolicy = new AzureRetryPolicy(RetryPolicy, additionalInfo: $"{containerName}") };
                    AccessCondition ac = etag == "*"
                                ? AccessCondition.GenerateEmptyCondition()
                                : AccessCondition.GenerateIfMatchCondition(etag);

                    await blob.PutBlockListAsync(blockList, ac, requestOptions, null);
                }
                catch (StorageException ex)
                {
                    string log = $"Failed to putblocklist with id {resourceId} to blob in container {containerName}";
                    Trace.TraceError("{0}, exception: {1}", log, ex);
                    _counters.IncrementRateCounter("AzureBlob PutBlockListFailureRate");

                    throw new RelInfraStorageException(log, ex, StorageErrorHelper.GetError(ex.RequestInformation.HttpStatusCode)).Format();
                }
            }
        }

        /// <summary>
        ///  get the list of blocks in Azure block blob
        /// </summary>
        /// <param name="containerName"></param>
        /// <param name="resourceId"></param>
        /// <param name="committedOnly"></param>
        /// <returns></returns>
        public async Task<IEnumerable<string>> GetBlockListAsync(string containerName, string resourceId, bool committedOnly)
        {
            _counters.IncrementRateCounter("AzureBlob GetBlockListRate");
            using (_counters.CreateDisposableTimer("AzureBlob GetBlockListLatency"))
            {
                try
                {
                    CloudBlobContainer container = _blobClient.GetContainerReference(containerName);
                    CloudBlockBlob blob = container.GetBlockBlobReference(resourceId);
                    BlobRequestOptions requestOptions = new BlobRequestOptions { RetryPolicy = new AzureRetryPolicy(RetryPolicy, additionalInfo: $"{containerName}") };

                    IEnumerable<ListBlockItem> blockList = await blob.DownloadBlockListAsync(committedOnly ? BlockListingFilter.Committed : BlockListingFilter.All,
                        AccessCondition.GenerateEmptyCondition(), requestOptions, null);
                    return blockList.Select(b => b.Name);
                }
                catch (StorageException ex)
                {
                    string log = $"Failed to getblocklist with id {resourceId} to blob in container {containerName}";
                    Trace.TraceError("{0}, exception: {1}", log, ex);
                    _counters.IncrementRateCounter("AzureBlob GetBlockListFailureRate");

                    throw new RelInfraStorageException(log, ex, StorageErrorHelper.GetError(ex.RequestInformation.HttpStatusCode)).Format();
                }
            }
        }

        /// <summary>
        /// read blob from blobOffset, applies to all types of blobs. 
        /// </summary>
        /// <param name="containerName"></param>
        /// <param name="resourceId"></param>
        /// <param name="blobOffset"></param>
        /// <param name="length"></param>
        /// <returns></returns>
        public async Task<byte[]> ReadBlobRangeAsync(string containerName, string resourceId, long blobOffset, int length)
        {
            _counters.IncrementRateCounter("AzureBlob ReadRangeRate");
            using (_counters.CreateDisposableTimer("AzureBlob ReadRangeLatency"))
            {
                try
                {
                    CloudBlobContainer container = _blobClient.GetContainerReference(containerName);

                    CloudBlob blob = container.GetBlobReference(resourceId);

                    // get the total size of the blob, DownloadRange request can't pass in length longer
                    // than its actual size
                    await blob.FetchAttributesAsync();
                    length = (int)Math.Min(length, blob.Properties.Length - blobOffset);

                    byte[] buffer = new byte[length];

                    int totalBytesRead = 0;
                    int numBytesRead = 1;
                    while (numBytesRead > 0 && totalBytesRead < length)
                    {
                        using (MemoryStream ms = new MemoryStream())
                        {
                            int chunkSize = Math.Min(FourMbytes, length - totalBytesRead);

                            await blob.DownloadRangeToStreamAsync(ms, blobOffset, chunkSize);
                            ms.Seek(0, SeekOrigin.Begin);
                            numBytesRead = await ms.ReadAsync(buffer, totalBytesRead, chunkSize);
                            blobOffset += numBytesRead;
                            totalBytesRead += numBytesRead;
                        }
                    }

                    return buffer;
                }
                catch (StorageException ex)
                {
                    string log = $"Failed to read page blob with id {resourceId} to blob in container {containerName}, offset {blobOffset}";
                    Trace.TraceError("{0}, exception: {1}", log, ex);
                    _counters.IncrementRateCounter("AzureBlob ReadRangeFailureRate");

                    throw new RelInfraStorageException(log, ex, StorageErrorHelper.GetError(ex.RequestInformation.HttpStatusCode)).Format();
                }
            }
        }

        public async Task<Storable<T>> TryGetAsync<T>(string containerName, string resourceId)
        {
            return await TryGetAsync<T>(containerName, resourceId, cancellationToken: CancellationToken.None);
        }

        public async Task<Storable<T>> TryGetAsync<T>(string containerName, string resourceId, CancellationToken cancellationToken)
        {
            try
            {
                return await GetAsync<T>(containerName, resourceId, cancellationToken);
            }
            catch (RelInfraStorageException)
            {
                return null;
            }
        }

        public async Task<Storable<T>> GetAsync<T>(string containerName, string resourceId)
        {
            using (var cancellationTokenSource = new CancellationTokenSource(TimeSpan.FromSeconds(DefaultBlobTimeoutInSeconds)))
            {
                return await GetAsync<T>(containerName, resourceId, cancellationToken: cancellationTokenSource.Token);
            }
        }

        public async Task<Storable<T>> GetAsync<T>(string containerName, string resourceId, CancellationToken cancellationToken)
        {
            _counters.IncrementRateCounter("AzureBlob GetRate");
            using (_counters.CreateDisposableTimer("AzureBlob GetLatency"))
            {
                try
                {
                    // By default we assume block blob is requested
                    return await GetBlobEntityAsync<T>(containerName, resourceId, cancellationToken);
                }
                catch (Exception)
                {
                    _counters.IncrementRateCounter("AzureBlob GetFailureRate");
                    throw;
                }
            }
        }

        public async Task<Storable<T>> GetBlobEntityAsync<T>(string containerName, string resourceId)
        {
            using (var cancellationTokenSource = new CancellationTokenSource(TimeSpan.FromSeconds(DefaultBlobTimeoutInSeconds)))
            {
                return await GetBlobEntityAsync<T>(containerName, resourceId, cancellationTokenSource.Token);
            }
        }

        public async Task<Storable<T>> GetBlobEntityAsync<T>(string containerName, string resourceId, CancellationToken cancellationToken)
        {
            Storable<string> blobStringContent = await GetBlobStringAsync(containerName: containerName, resourceId: resourceId, cancellationToken: cancellationToken);
            return new Storable<T>
            {
                Id = blobStringContent.Id,
                ContainerId = blobStringContent.ContainerId,
                Etag = blobStringContent.Etag,
                Entity = SerializationHelpers.DeserializeEntity<T>(blobStringContent.Entity)
            };
        }

        public async Task<Storable<string>> GetBlobStringAsync(string containerName, string resourceId)
        {
            return await GetBlobStringAsync(containerName, resourceId, CancellationToken.None);
        }

        public async Task<Storable<string>> GetBlobStringAsync(string containerName, string resourceId, CancellationToken cancellationToken)
        {
            _counters.IncrementRateCounter("AzureBlob GetBlobStringRate");
            using (_counters.CreateDisposableTimer("AzureBlob GetBlobStringLatency"))
            {

                CloudBlobContainer container = _blobClient.GetContainerReference(containerName);
                CloudBlob blob = container.GetBlobReference(resourceId);
                try
                {
                    using (MemoryStream ms = new MemoryStream())
                    {
                        BlobRequestOptions requestOptions = new BlobRequestOptions
                        {
                            RetryPolicy = new AzureRetryPolicy(RetryPolicy, additionalInfo: $"{containerName}")
                        };

                        await blob.DownloadToStreamAsync(target: ms, accessCondition: null, options: requestOptions, operationContext: null, cancellationToken: cancellationToken);

                        return new Storable<string>
                        {
                            Id = resourceId,
                            ContainerId = containerName,
                            Etag = blob.Properties.ETag,
                            Entity = Encoding.UTF8.GetString(ms.ToArray())
                        };
                    }
                }
                catch (StorageException ex)
                {
                    string log = $"Failed to get entity with id {resourceId} from blob in container {containerName} with request ID: {ex.RequestInformation.ServiceRequestID}.";
                    Trace.TraceError("{0}, exception: {1}", log, ex);
                    _counters.IncrementRateCounter("AzureBlob GetBlobStringFailureRate");

                    throw new RelInfraStorageException(log, ex, StorageErrorHelper.GetError(ex.RequestInformation.HttpStatusCode)).Format();
                }
            }
        }

        public async Task<IEnumerable<Storable<T>>> BatchGetAsync<T>(string containerName, IEnumerable<string> resourceIds)
        {
            _counters.IncrementRateCounter("AzureBlob BatchGetRate");
            using (_counters.CreateDisposableTimer("AzureBlob BatchGetLatency"))
            {
                List<string> resources = resourceIds.ToList();
                _counters.SetNumberCounter("AzureBlob BatchGetNumber", resources.Count);
                try
                {
                    return await Task.WhenAll(resources.Select(id => GetAsync<T>(containerName, id)));
                }
                catch
                {
                    _counters.IncrementRateCounter("AzureBlob BatchGetFailureRate");
                    throw;
                }
            }
        }

        public async Task<long> GetBlobSizeAsync(string containerName, string resourceId)
        {
            _counters.IncrementRateCounter("AzureBlob GetBlobSizeRate");
            using (_counters.CreateDisposableTimer("AzureBlob GetBlobSizeLatency"))
            {

                CloudBlobContainer container = _blobClient.GetContainerReference(containerName);
                CloudBlob blob = container.GetBlobReference(resourceId);
                try
                {
                    await blob.FetchAttributesAsync();

                    return blob.Properties.Length;
                }
                catch (StorageException ex)
                {
                    string log = $"Failed to get entity size with id {resourceId} to blob in container {containerName}";
                    Trace.TraceError("{0}, exception: {1}", log, ex);
                    _counters.IncrementRateCounter("AzureBlob GetBlobSizeFailureRate");

                    throw new RelInfraStorageException(log, ex, StorageErrorHelper.GetError(ex.RequestInformation.HttpStatusCode)).Format();
                }
            }
        }


        public async Task<IDictionary<string, long>> BatchGetBlockBlobSegmentSize(string containerName, string resourceId, IEnumerable<string> blockIds)
        {
            _counters.IncrementRateCounter("AzureBlob GetBlockSizeRate");
            using (_counters.CreateDisposableTimer("AzureBlob GetBlockSizeLatency"))
            {
                try
                {
                    CloudBlobContainer container = _blobClient.GetContainerReference(containerName);
                    CloudBlockBlob blob = container.GetBlockBlobReference(resourceId);
                    BlobRequestOptions requestOptions = new BlobRequestOptions { RetryPolicy = new AzureRetryPolicy(RetryPolicy, additionalInfo: $"{containerName}") };

                    IDictionary<string, long> blockList = (await blob.DownloadBlockListAsync(BlockListingFilter.All,
                        AccessCondition.GenerateEmptyCondition(), requestOptions, operationContext: null)).ToDictionary(block => block.Name, block => block.Length);

                    Dictionary<string, long> blockIdSizeDictionary = new Dictionary<string, long>();
                    foreach (string blockIdToFind in blockIds)
                    {
                        if (blockList.ContainsKey(blockIdToFind))
                        {
                            blockIdSizeDictionary.Add(blockIdToFind, blockList[blockIdToFind]);
                        }
                        else
                        {
                            throw new RelInfraStorageException($"block {blockIdToFind} does not exist in block blob {resourceId} in container {containerName}").Format();
                        }
                    }

                    return blockIdSizeDictionary;
                }
                catch (StorageException ex)
                {
                    string blockIdsString = string.Join(",", blockIds);
                    string log = $"Failed to getblocksize with id {resourceId} blockids {blockIdsString} to blob in container {containerName}";
                    Trace.TraceError("{0}, exception: {1}", log, ex);
                    _counters.IncrementRateCounter("AzureBlob GetBlockListFailureRate");

                    throw new RelInfraStorageException(log, ex, StorageErrorHelper.GetError(ex.RequestInformation.HttpStatusCode)).Format();
                }
            }
        }

        public async Task<bool> TryUpdateAsync<T>(string containerName, string resourceId, Storable<T> resource)
        {
            try
            {
                await UpdateAsync(containerName, resourceId, resource);
            }
            catch (RelInfraStorageException)
            {
                return false;
            }
            return true;
        }

        public async Task<Storable<T>> UpdateAsync<T>(string containerName, string resourceId, Storable<T> resource)
        {
            _counters.IncrementRateCounter("AzureBlob UpdateRate");
            using (_counters.CreateDisposableTimer("AzureBlob UpdateLatency"))
            {
                try
                {
                    if (String.IsNullOrWhiteSpace(resource.Etag))
                    {
                        throw new ArgumentException("etag for resource can't be null or empty", nameof(resource));
                    }

                    try
                    {
                        CloudBlobContainer container = _blobClient.GetContainerReference(containerName);
                        CloudBlockBlob blob = container.GetBlockBlobReference(resourceId);

                        using (var ms = new MemoryStream(Encoding.UTF8.GetBytes(SerializationHelpers.SerializeEntity(resource.Entity))))
                        {
                            AccessCondition ac = resource.Etag == "*"
                                ? AccessCondition.GenerateEmptyCondition()
                                : AccessCondition.GenerateIfMatchCondition(resource.Etag);
                            BlobRequestOptions options = new BlobRequestOptions() { RetryPolicy = new AzureRetryPolicy(RetryPolicy, additionalInfo: $"{containerName}") };

                            var ctx = new OperationContext();
                            await blob.UploadFromStreamAsync(ms, ac, options, ctx);

                            return new Storable<T>
                            {
                                Id = resourceId,
                                ContainerId = containerName,
                                Etag = blob.Properties.ETag,
                                Entity = SerializationHelpers.DeserializeEntity<T>(Encoding.UTF8.GetString(ms.ToArray()))
                            };
                        }
                    }
                    catch (StorageException ex)
                    {
                        string log =
                            $"Failed to update entity with id {resourceId} to blob in container {containerName}";
                        Trace.TraceError("{0}, exception: {1}", log, ex);

                        throw new RelInfraStorageException(log, ex, StorageErrorHelper.GetError(ex.RequestInformation.HttpStatusCode)).Format();
                    }
                }
                catch
                {
                    _counters.IncrementRateCounter("AzureBlob UpdateFailureRate");
                    throw;
                }
            }
        }

        public async Task<IEnumerable<Storable<T>>> BatchUpdateAsync<T>(string containerName, IEnumerable<Storable<T>> resourceIds)
        {
            _counters.IncrementRateCounter("AzureBlob BatchUpdateRate");
            using (_counters.CreateDisposableTimer("AzureBlob BatchUpdateLatency"))
            {
                List<Storable<T>> resources = resourceIds.ToList();
                _counters.SetNumberCounter("AzureBlob BatchUpdateNumber", resources.Count);
                try
                {
                    return await Task.WhenAll(resources.Select(res => UpdateAsync(containerName, res.Id, res)));
                }
                catch
                {
                    _counters.IncrementRateCounter("AzureBlob BatchUpdateFailureRate");
                    throw;
                }
            }
        }

        public async Task<bool> TryDeleteAsync(string containerName, string resourceId)
        {
            try
            {
                await DeleteAsync(containerName, resourceId);
            }
            catch (RelInfraStorageException)
            {
                return false;
            }
            return true;
        }

        public async Task DeleteAsync(string containerName, string resourceId)
        {
            await DeleteAsync(containerName, resourceId, "*");
        }

        public async Task DeleteAsync(string containerName, string resourceId, string etag)
        {
            _counters.IncrementRateCounter("AzureBlob DeleteRate");
            using (_counters.CreateDisposableTimer("AzureBlob DeleteLatency"))
            {
                try
                {
                    CloudBlobContainer container = _blobClient.GetContainerReference(containerName);
                    CloudBlob blob = container.GetBlobReference(resourceId);

                    BlobRequestOptions options = new BlobRequestOptions() { RetryPolicy = new AzureRetryPolicy(RetryPolicy, additionalInfo: $"{containerName}") };
                    OperationContext ctx = new OperationContext();

                    AccessCondition ac = etag == "*"
                        ? AccessCondition.GenerateEmptyCondition()
                        : AccessCondition.GenerateIfMatchCondition(etag);

                    using (var cancellationTokenSource = new CancellationTokenSource(TimeSpan.FromSeconds(DefaultBlobTimeoutInSeconds)))
                    {
                        await blob.DeleteAsync(DeleteSnapshotsOption.None, ac, options, ctx, cancellationTokenSource.Token);
                    }
                }
                catch (StorageException ex)
                {
                    string log = $"Failed to delete blob with id {resourceId} from container {containerName}";
                    Trace.TraceError("{0}, exception: {1}", log, ex);
                    _counters.IncrementRateCounter("AzureBlob DeleteFailureRate");

                    throw new RelInfraStorageException(log, ex, StorageErrorHelper.GetError(ex.RequestInformation.HttpStatusCode)).Format();
                }
            }
        }

        public async Task DeleteContainerAsync(string containerName)
        {
            _counters.IncrementRateCounter("AzureBlob DeleteContainerRate");
            using (_counters.CreateDisposableTimer("AzureBlob DeleteContainerLatency"))
            {
                try
                {
                    CloudBlobContainer container = _blobClient.GetContainerReference(containerName);
                    await container.DeleteAsync();
                }
                catch (StorageException ex)
                {
                    string log = $"Failed to delete blob container {containerName}";
                    Trace.TraceError("{0}, exception: {1}", log, ex);
                    _counters.IncrementRateCounter("AzureBlob DeleteContainerFailureRate");

                    throw new RelInfraStorageException(log, ex, StorageErrorHelper.GetError(ex.RequestInformation.HttpStatusCode)).Format();
                }
            }
        }

        public async Task BatchDeleteAsync(string containerName, IEnumerable<string> resourceIds)
        {
            _counters.IncrementRateCounter("AzureBlob BatchDeleteRate");
            using (_counters.CreateDisposableTimer("AzureBlob BatchDeleteLatency"))
            {
                List<string> resources = resourceIds.ToList();
                _counters.SetNumberCounter("AzureBlob BatchDeleteNumber", resources.Count);
                try
                {
                    await resources.ForEachAsync(20, async id => { await DeleteAsync(containerName, id); });
                }
                catch
                {
                    _counters.IncrementRateCounter("AzureBlob BatchDeleteFailureRate");
                    throw;
                }
            }
        }

        public async Task<IEnumerable<string>> ListDirectoryAsync(string containerName, string path, int fileCountLimit, CancellationToken cancellationToken, string prefixFilter)
        {
            const int maxFilesPerIteration = 100;
            _counters.IncrementRateCounter("AzureBlob ListDirectoryRate");
            using (_counters.CreateDisposableTimer("AzureBlob ListDirectoryLatency"))
            {
                try
                {
                    CloudBlobContainer container = _blobClient.GetContainerReference(containerName);
                    BlobContinuationToken continuationToken = null;
                    OperationContext operationContext = new OperationContext();

                    List<string> blobs = new List<string>();
                    do
                    {
                        BlobResultSegment resultsSegment = await container.ListBlobsSegmentedAsync(prefix: path,
                            useFlatBlobListing: true, // if true, blobs are returned, otherwise only folders
                            blobListingDetails: BlobListingDetails.None,
                            maxResults: maxFilesPerIteration,
                            currentToken: continuationToken,
                            options: new BlobRequestOptions { RetryPolicy = new AzureRetryPolicy(RetryPolicy, additionalInfo: $"{containerName}") },
                            operationContext: operationContext,
                            cancellationToken: cancellationToken);
                        continuationToken = resultsSegment.ContinuationToken;
                        var results = resultsSegment.Results
                                                    .Select(blob => blob.Uri.AbsolutePath)
                                                    .Where(absPath => prefixFilter == null || absPath.StartsWith(prefixFilter));
                        blobs.AddRange(results);
                        if (blobs.Count > fileCountLimit)
                        {
                            throw new TooManyFilesException($"Got too many files when enumerating {containerName}, path {path}. Limit {fileCountLimit}");
                        }
                    } while (continuationToken != null);

                    return blobs;
                }
                catch (StorageException ex)
                {
                    string log = $"Failed to list blobs in container {containerName} with prefix {path}";
                    Trace.TraceError("{0}, exception: {1}", log, ex);
                    _counters.IncrementRateCounter("AzureBlob ListDirectoryFailureRate");

                    throw new RelInfraStorageException(log, ex, StorageErrorHelper.GetError(ex.RequestInformation.HttpStatusCode)).Format();
                }
            }
        }

        public async Task<QueryResults> ListDirectoryWithContinuationTokenAsync(string containerName, string path, string continuationToken, int takeCount, CancellationToken cancellationToken, bool useFlatBlobListing = true)
        {
            QueryResults<IListBlobItem> results = await ListDirectoryBlobsWithContinuationTokenAsync(containerName, path, continuationToken, takeCount, cancellationToken, useFlatBlobListing);
            return new QueryResults(
                continuationToken: results.ContinuationToken,
                results: results.Results.Select(b => b.Entity.Uri.AbsolutePath));
        }

        public virtual async Task<QueryResults<IListBlobItem>> ListDirectoryBlobsWithContinuationTokenAsync(string containerName, string path, string continuationToken, int takeCount, CancellationToken cancellationToken, bool useFlatBlobListing = true)
        {
            const int maxFilesPerIteration = 100;
            _counters.IncrementRateCounter("AzureBlob ListDirectoryWithContinuationRate");
            using (_counters.CreateDisposableTimer("AzureBlob ListDirectoryWithContinuationLatency"))
            {
                try
                {
                    CloudBlobContainer container = _blobClient.GetContainerReference(containerName);
                    BlobContinuationToken blobContinuationToken = ReadContinuationToken(continuationToken);
                    OperationContext operationContext = new OperationContext();

                    List<Storable<IListBlobItem>> blobs = new List<Storable<IListBlobItem>>();
                    do
                    {
                        int maxResults = Math.Min(maxFilesPerIteration, takeCount - blobs.Count);
                        BlobResultSegment resultsSegment = await container.ListBlobsSegmentedAsync(prefix: path,
                            useFlatBlobListing: useFlatBlobListing, // if true, blobs are returned, otherwise only folders
                            blobListingDetails: BlobListingDetails.None,
                            maxResults: maxResults,
                            currentToken: blobContinuationToken,
                            options: new BlobRequestOptions { RetryPolicy = new AzureRetryPolicy(RetryPolicy, additionalInfo: $"{containerName}") },
                            operationContext: operationContext,
                            cancellationToken: cancellationToken);
                        blobContinuationToken = resultsSegment.ContinuationToken;

                        blobs.AddRange(resultsSegment.Results.Select(b => new Storable<IListBlobItem>()
                        {
                            ContainerId = b.Container.Name,
                            Id = b.Uri.LocalPath.Substring(b.Container.Name.Length + 2), // remove "/container/" at the beginning
                            Entity = b,
                            Etag = (b as CloudBlob)?.Properties.ETag
                        }));
                    } while (blobContinuationToken != null && blobs.Count < takeCount);

                    return new QueryResults<IListBlobItem>(
                        continuationToken: WriteContinuationToken(blobContinuationToken), results: blobs);
                }
                catch (StorageException ex)
                {
                    string log = $"Failed to list blobs in container {containerName} with prefix {path}";
                    Trace.TraceError("{0}, exception: {1}", log, ex);
                    _counters.IncrementRateCounter("AzureBlob ListDirectoryWithContinuationFailureRate");

                    throw new RelInfraStorageException(log, ex, StorageErrorHelper.GetError(ex.RequestInformation.HttpStatusCode)).Format();
                }
            }
        }

        //https://github.com/Azure/azure-storage-net/blob/68c3ee55a3a6f62a0159cea58005d3fe027312a8/Lib/Common/Blob/BlobContinuationToken.cs
        private static BlobContinuationToken ReadXml(XmlReader reader)
        {
            var token = new BlobContinuationToken();

            // Read the xml root node
            reader.MoveToContent();
            reader.ReadStartElement();

            // Read the additional xml node that will be present if an XmlSerializer was used to serialize this token
            reader.MoveToContent();
            if (reader.Name == Constants.ContinuationConstants.ContinuationTopElement)
            {
                reader.ReadStartElement();
            }

            // Read the ContinuationToken content
            while (reader.IsStartElement())
            {
                switch (reader.Name)
                {
                    case Constants.ContinuationConstants.VersionElement:
                        //this.Version = reader.ReadElementContentAsString();
                        break;

                    case Constants.ContinuationConstants.NextMarkerElement:
                        token.NextMarker = reader.ReadElementContentAsString();
                        break;

                    case Constants.ContinuationConstants.TargetLocationElement:
                        string targetLocation = reader.ReadElementContentAsString();
                        StorageLocation location;
                        if (Enum.TryParse(targetLocation, out location))
                        {
                            token.TargetLocation = location;
                        }
                        else if (!string.IsNullOrEmpty(targetLocation))
                        {
                            throw new XmlException("unexpected location: " + targetLocation);
                        }

                        break;

                    case Constants.ContinuationConstants.TypeElement:
                        //this.Type = reader.ReadElementContentAsString();
                        break;

                    default:
                        throw new XmlException("unexpected element: " + reader.Name);
                }
            }
            return token;
        }

        public static void WriteXml(BlobContinuationToken token, XmlWriter writer)
        {

            writer.WriteStartElement(Constants.ContinuationConstants.ContinuationTopElement);

            //writer.WriteElementString(Constants.ContinuationConstants.VersionElement, this.Version);

            //writer.WriteElementString(Constants.ContinuationConstants.TypeElement, this.Type);

            if (token.NextMarker != null)
            {
                writer.WriteElementString(Constants.ContinuationConstants.NextMarkerElement, token.NextMarker);
            }

            writer.WriteElementString(Constants.ContinuationConstants.TargetLocationElement, token.TargetLocation.ToString());

            writer.WriteEndElement(); // End ContinuationToken
        }

        private static BlobContinuationToken ReadContinuationToken(string continuationToken)
        {
            if (continuationToken == null)
            {
                return null;
            }

            using (var xmlReader = XmlReader.Create(new StringReader(continuationToken)))
            {
                return ReadXml(xmlReader);
            }
        }

        private static string WriteContinuationToken(BlobContinuationToken blobContinuationToken)
        {
            if (blobContinuationToken == null)
            {
                return null;
            }

            using (var stringWriter = new StringWriter())
            {
                using (var xmlWriter = XmlWriter.Create(stringWriter))
                {
                    WriteXml(blobContinuationToken, xmlWriter);
                }
                return stringWriter.ToString();
            }
        }

        public async Task<bool> ResourceExistsAsync(string containerName, string resourceId)
        {
            if (String.IsNullOrWhiteSpace(containerName))
            {
                throw new ArgumentException("containerName can't be null or empty", nameof(containerName));
            }

            if (String.IsNullOrWhiteSpace(resourceId))
            {
                throw new ArgumentException("resourceId can't be null or empty", nameof(resourceId));
            }

            BlobRequestOptions options = new BlobRequestOptions() { RetryPolicy = new AzureRetryPolicy(RetryPolicy, additionalInfo: $"{containerName}") };

            CloudBlobContainer container = _blobClient.GetContainerReference(containerName);
            CloudBlob blob = container.GetBlobReference(resourceId);
            BlobRequestOptions requestOptions = new BlobRequestOptions { RetryPolicy = new AzureRetryPolicy(RetryPolicy, additionalInfo: $"{containerName}") };
            return await blob.ExistsAsync(options: requestOptions, operationContext: null);
        }

        public async Task InitializeAsync(string containerName, string blobName)
        {
            _counters.IncrementRateCounter("AzureBlob InitializeBlobRate");
            using (_counters.CreateDisposableTimer("AzureBlob InitializeLatency"))
            {
                try
                {
                    //
                    // Create container if it doesn't exist
                    //
                    await InitializeAsync(containerName);

                    //
                    // Create blob if it doesn't exist
                    //
                    bool blobExists = await ResourceExistsAsync(containerName, blobName);

                    if (!blobExists)
                    {
                        Exception raisedException = null;
                        try
                        {
                            await TryPutAndIgnoreEtagAsync(containerName, blobName, new Storable<string>("CreateLease"));
                        }
                        catch (Exception ex)
                        {
                            raisedException = ex;
                        }

                        // If something went wrong, let's check if blob was created or not. If it was created, exception will be swallowed.
                        if (raisedException != null)
                        {
                            bool blobCreated = await ResourceExistsAsync(containerName, blobName);
                            if (!blobCreated)
                            {
                                throw raisedException;
                            }
                        }
                    }
                }
                catch
                {
                    _counters.IncrementRateCounter("AzureBlob InitializeBlobFailureRate");
                    throw;
                }
            }
        }

        public async Task<string> AcquireLeaseAsync(string containerName, string blobName, TimeSpan? leaseTime, string proposedLeaseId, CancellationToken cancellationToken = default(CancellationToken))
        {
            _counters.IncrementRateCounter("AzureBlob AcquireLeaseRate");
            using (_counters.CreateDisposableTimer("AzureBlob AcquireLeaseLatency"))
            {
                try
                {
                    if (String.IsNullOrWhiteSpace(containerName))
                    {
                        throw new ArgumentException("containerName can't be null or empty", nameof(containerName));
                    }

                    if (String.IsNullOrWhiteSpace(blobName))
                    {
                        throw new ArgumentException("blobName can't be null or empty", nameof(blobName));
                    }

                    try
                    {
                        var options = new BlobRequestOptions { RetryPolicy = new AzureRetryPolicy(RetryPolicy, additionalInfo: $"{containerName}") };

                        CloudBlobContainer container = _blobClient.GetContainerReference(containerName);
                        CloudBlob blob = container.GetBlobReference(blobName);

                        var ctx = new OperationContext();
                        return await blob.AcquireLeaseAsync(leaseTime, proposedLeaseId, null, options, ctx, cancellationToken);
                    }
                    catch (StorageException ex)
                    {
                        string log = $"Failed to acquire a lease, container name = {containerName}, blob {blobName}, code = {ex.RequestInformation.HttpStatusCode}";
                        if (ex.RequestInformation.HttpStatusCode != (int)HttpStatusCode.Conflict)
                        {
                            Trace.TraceError("{0}, exception: {1}", log, ex);
                        }

                        throw new RelInfraStorageException(log, ex, StorageErrorHelper.GetError(ex.RequestInformation.HttpStatusCode)).Format();
                    }
                }
                catch
                {
                    _counters.IncrementRateCounter("AzureBlob AcquireLeaseFailureRate");
                    throw;
                }
            }
        }

        public async Task RenewLeaseAsync(string containerName, string blobName, string leaseId, CancellationToken cancellationToken = default(CancellationToken))
        {
            _counters.IncrementRateCounter("AzureBlob RenewLeaseRate");
            using (_counters.CreateDisposableTimer("AzureBlob RenewLeaseLatency"))
            {
                try
                {
                    if (String.IsNullOrWhiteSpace(containerName))
                    {
                        throw new ArgumentException("containerName can't be null or empty", nameof(containerName));
                    }

                    if (String.IsNullOrWhiteSpace(blobName))
                    {
                        throw new ArgumentException("blobName can't be null or empty", nameof(blobName));
                    }

                    try
                    {
                        var options = new BlobRequestOptions { RetryPolicy = new AzureRetryPolicy(RetryPolicy, additionalInfo: $"{containerName}") };

                        CloudBlobContainer container = _blobClient.GetContainerReference(containerName);
                        CloudBlob blob = container.GetBlobReference(blobName);

                        var ctx = new OperationContext();
                        await blob.RenewLeaseAsync(AccessCondition.GenerateLeaseCondition(leaseId), options, ctx, cancellationToken);
                    }
                    catch (StorageException ex)
                    {
                        string log =
                            $"Failed to renew a lease, container name = {containerName}, blob {blobName}, leaseId {leaseId}";
                        Trace.TraceError("{0}, exception: {1}", log, ex);

                        throw new RelInfraStorageException(log, ex, StorageErrorHelper.GetError(ex.RequestInformation.HttpStatusCode)).Format();
                    }
                }
                catch
                {
                    _counters.IncrementRateCounter("AzureBlob RenewLeaseFailureRate");
                    throw;
                }
            }
        }

        public async Task ReleaseLeaseAsync(string containerName, string blobName, string leaseId, CancellationToken cancellationToken = default(CancellationToken))
        {
            _counters.IncrementRateCounter("AzureBlob ReleaseLeaseRate");
            using (_counters.CreateDisposableTimer("AzureBlob ReleaseLeaseLatency"))
            {
                try
                {
                    if (String.IsNullOrWhiteSpace(containerName))
                    {
                        throw new ArgumentException("containerName can't be null or empty", nameof(containerName));
                    }

                    if (String.IsNullOrWhiteSpace(blobName))
                    {
                        throw new ArgumentException("blobName can't be null or empty", nameof(blobName));
                    }

                    try
                    {
                        var options = new BlobRequestOptions { RetryPolicy = new AzureRetryPolicy(RetryPolicy, additionalInfo: $"{containerName}") };

                        CloudBlobContainer container = _blobClient.GetContainerReference(containerName);
                        CloudBlob blob = container.GetBlobReference(blobName);

                        var accessCondition = AccessCondition.GenerateLeaseCondition(leaseId);
                        var ctx = new OperationContext();
                        await blob.ReleaseLeaseAsync(accessCondition, options, ctx, cancellationToken);

                    }
                    catch (StorageException ex)
                    {
                        string log =
                            $"Failed to release a lease, container name = {containerName}, blob {blobName}, leaseId {leaseId}";
                        Trace.TraceError("{0}, exception: {1}", log, ex);

                        throw new RelInfraStorageException(log, ex, StorageErrorHelper.GetError(ex.RequestInformation.HttpStatusCode)).Format();
                    }
                }
                catch
                {
                    _counters.IncrementRateCounter("AzureBlob ReleaseLeaseFailureRate");
                    throw;
                }
            }
        }

        public virtual async Task<Stream> GetStreamAsync(string containerName, string resourceId)
        {
            _counters.IncrementRateCounter("AzureBlob GetStreamRate");
            using (_counters.CreateDisposableTimer("AzureBlob GetStreamLatency"))
            {
                try
                {
                    if (String.IsNullOrWhiteSpace(containerName))
                    {
                        throw new ArgumentException("containerName can't be null or empty", nameof(containerName));
                    }

                    if (String.IsNullOrWhiteSpace(resourceId))
                    {
                        throw new ArgumentException("resourceId can't be null or empty", nameof(resourceId));
                    }

                    CloudBlobContainer container = _blobClient.GetContainerReference(containerName);
                    CloudBlob blob = container.GetBlobReference(resourceId);
                    BlobRequestOptions requestOptions = new BlobRequestOptions { RetryPolicy = new AzureRetryPolicy(RetryPolicy, additionalInfo: $"{containerName}") };

                    return await blob.OpenReadAsync(accessCondition: null, options: requestOptions, operationContext: null);
                }
                catch
                {
                    _counters.IncrementRateCounter("AzureBlob GetStreamFailureRate");
                    throw;
                }
            }
        }

        public async Task UploadStreamAsync(string containerName, string resourceId, Stream stream)
        {
            await UploadStreamWithTypeAsync(containerName, resourceId, stream, null);
        }

        public async Task<bool> TryUploadStreamAsync(string containerName, string resourceId, Stream stream)
        {
            return await TryUploadStreamWithTypeAsync(containerName, resourceId, stream, null);
        }

        public async Task<bool> TryUploadStreamWithTypeAsync(string containerName, string resourceId, Stream stream, string contentType)
        {
            try
            {
                await UploadStreamWithTypeAsync(containerName, resourceId, stream, contentType);
                return true;
            }
            catch (StorageException)
            {
                return false;
            }
        }

        public virtual async Task UploadStreamWithTypeAsync(string containerName, string resourceId, Stream stream, string contentType)
        {
            _counters.IncrementRateCounter("AzureBlob UploadStreamRate");
            using (_counters.CreateDisposableTimer("AzureBlob UploadStreamLatency"))
            {
                try
                {
                    if (String.IsNullOrWhiteSpace(containerName))
                    {
                        throw new ArgumentException("containerName can't be null or empty", nameof(containerName));
                    }

                    if (String.IsNullOrWhiteSpace(resourceId))
                    {
                        throw new ArgumentException("resourceId can't be null or empty", nameof(resourceId));
                    }

                    try
                    {
                        CloudBlobContainer container = _blobClient.GetContainerReference(containerName);
                        CloudBlockBlob blob = container.GetBlockBlobReference(resourceId);
                        if (!string.IsNullOrWhiteSpace(contentType))
                        {
                            blob.Properties.ContentType = contentType;
                        }
                        BlobRequestOptions requestOptions = new BlobRequestOptions { RetryPolicy = new AzureRetryPolicy(RetryPolicy, additionalInfo: $"{containerName}") };

                        await blob.UploadFromStreamAsync(stream, accessCondition: null, options: requestOptions, operationContext: null);
                    }
                    catch (StorageException ex) //aggregate exception?
                    {
                        Trace.TraceError("Failed to upload entity with id {0} to blob in container {1}, exception: {2}", resourceId, containerName, ex);
                        throw;
                    }
                }
                catch
                {
                    _counters.IncrementRateCounter("AzureBlob UploadStreamFailureRate");
                    throw;
                }
            }
        }

        public virtual async Task DownloadToFileAsync(string containerName, string blobName, string fileLocation, CancellationToken cancellationToken)
        {
            _counters.IncrementRateCounter("AzureBlob DownloadToFileRate");
            using (_counters.CreateDisposableTimer("AzureBlob DownloadToFileLatency"))
            {
                try
                {
                    if (String.IsNullOrWhiteSpace(containerName))
                    {
                        throw new ArgumentException("containerName can't be null or empty", nameof(containerName));
                    }

                    if (String.IsNullOrWhiteSpace(blobName))
                    {
                        throw new ArgumentException("blobName can't be null or empty", nameof(blobName));
                    }

                    if (String.IsNullOrWhiteSpace(fileLocation))
                    {
                        throw new ArgumentException("fileLocation can't be null or empty", nameof(fileLocation));
                    }

                    try
                    {
                        CloudBlobContainer container = _blobClient.GetContainerReference(containerName);
                        CloudBlockBlob blob = container.GetBlockBlobReference(blobName);
                        var options = new BlobRequestOptions() { RetryPolicy = new AzureRetryPolicy(RetryPolicy, additionalInfo: $"{containerName}") };
                        await blob.DownloadToFileAsync(path: fileLocation, mode: FileMode.Create, accessCondition: null, options: options, operationContext: null, cancellationToken: cancellationToken);
                    }
                    catch (StorageException ex)
                    {
                        Trace.TraceError("Failed to download to blob {0} in container {1}, exception: {2}", blobName, containerName, ex);

                        throw;
                    }
                }
                catch
                {
                    _counters.IncrementRateCounter("AzureBlob DownloadToFileFailureRate");
                    throw;
                }
            }
        }

        public async Task UploadFileAsync(string containerName, string blobName, string fileLocation, CancellationToken cancellationToken)
        {
            _counters.IncrementRateCounter("AzureBlob UploadFileRate");
            using (_counters.CreateDisposableTimer("AzureBlob UploadFileLatency"))
            {
                try
                {
                    if (String.IsNullOrWhiteSpace(containerName))
                    {
                        throw new ArgumentException("containerName can't be null or empty", nameof(containerName));
                    }

                    if (String.IsNullOrWhiteSpace(blobName))
                    {
                        throw new ArgumentException("blobName can't be null or empty", nameof(blobName));
                    }

                    if (String.IsNullOrWhiteSpace(fileLocation))
                    {
                        throw new ArgumentException("fileLocation can't be null or empty", nameof(fileLocation));
                    }

                    try
                    {
                        CloudBlobContainer container = _blobClient.GetContainerReference(containerName);
                        CloudBlockBlob blob = container.GetBlockBlobReference(blobName);
                        var options = new BlobRequestOptions() { RetryPolicy = new AzureRetryPolicy(RetryPolicy, additionalInfo: $"{containerName}") };
                        var ac = AccessCondition.GenerateEmptyCondition(); // Overwrite existing blob
                        await blob.UploadFromFileAsync(path: fileLocation, accessCondition: ac, options: options, operationContext: null, cancellationToken: cancellationToken);
                    }
                    catch (StorageException ex)
                    {
                        Trace.TraceError("Failed to upload blob {0} in container {1}, exception: {2}", blobName, containerName, ex);

                        throw;
                    }
                }
                catch
                {
                    _counters.IncrementRateCounter("AzureBlob UploadFileFailureRate");
                    throw;
                }
            }
        }


        public async Task<string> StartCopyAsync(string srcContainerName, string sourceResourceId, string destContainerName, string destinationResourceId)
        {
            _counters.IncrementRateCounter("AzureBlob StartCopyRate");
            using (_counters.CreateDisposableTimer("AzureBlob StartCopyLatency"))
            {
                try
                {
                    CloudBlobContainer destContainer = _blobClient.GetContainerReference(destContainerName);
                    CloudBlob destblob = destContainer.GetBlobReference(destinationResourceId);
                    CloudBlobContainer srcContainer = _blobClient.GetContainerReference(srcContainerName);
                    CloudBlob srcBlob = srcContainer.GetBlobReference(sourceResourceId);
                    var options = new BlobRequestOptions() { RetryPolicy = new AzureRetryPolicy(RetryPolicy, additionalInfo: $"{srcContainerName}:{destContainerName}") };
                    return await destblob.StartCopyAsync(srcBlob.Uri, AccessCondition.GenerateEmptyCondition(), AccessCondition.GenerateEmptyCondition(), options, operationContext: null);
                }
                catch (StorageException ex)
                {
                    string log = $"Failed to start copy blob in container {srcContainerName} resourceId {sourceResourceId} to container {destContainerName} resourceId {destinationResourceId}";
                    Trace.TraceError("{0}, exception: {1}", log, ex);
                    _counters.IncrementRateCounter("AzureBlob StartCopyFailureRate");

                    throw new RelInfraStorageException(log, ex, StorageErrorHelper.GetError(ex.RequestInformation.HttpStatusCode)).Format();
                }
            }
        }

        public async Task<CopyState> GetCopyStateAsync(string containerName, string resourceId)
        {
            _counters.IncrementRateCounter("AzureBlob GetCopyStateRate");
            using (_counters.CreateDisposableTimer("AzureBlob GetCopyStateLatency"))
            {
                try
                {
                    CloudBlobContainer container = _blobClient.GetContainerReference(containerName);
                    CloudBlob blob = container.GetBlobReference(resourceId);

                    await blob.FetchAttributesAsync();

                    return blob.CopyState;
                }
                catch (StorageException ex)
                {
                    string log = $"Failed to get copy state blob in container {containerName} resourceId {resourceId}";
                    Trace.TraceError("{0}, exception: {1}", log, ex);
                    _counters.IncrementRateCounter("AzureBlob GetCopyStateFailureRate");

                    throw new RelInfraStorageException(log, ex, StorageErrorHelper.GetError(ex.RequestInformation.HttpStatusCode)).Format();
                }
            }
        }

        public async Task<string> GetBlobMetadataAsync(string containerName, string resourceId, string metadataKey)
        {
            _counters.IncrementRateCounter("AzureBlob GetMetadataRate");

            using (_counters.CreateDisposableTimer("AzureBlob GetMetadataLatency"))
            {
                try
                {
                    CloudBlobContainer container = _blobClient.GetContainerReference(containerName);
                    CloudBlob blob = container.GetBlobReference(resourceId);
                    BlobRequestOptions requestOptions = new BlobRequestOptions { RetryPolicy = new AzureRetryPolicy(RetryPolicy, additionalInfo: $"{containerName}") };

                    await blob.FetchAttributesAsync(accessCondition: null, options: requestOptions, operationContext: null);

                    return (blob.Metadata != null && blob.Metadata.ContainsKey(metadataKey)) ? blob.Metadata[metadataKey] : null;
                }
                catch (StorageException ex)
                {
                    string log =
                        $"Failed to check entity metadata with id {resourceId} to blob in container {containerName}";
                    Trace.TraceError("{0}, exception: {1}", log, ex);
                    _counters.IncrementRateCounter("AzureBlob GetMetadataFailureRate");

                    throw new RelInfraStorageException(log, ex, StorageErrorHelper.GetError(ex.RequestInformation.HttpStatusCode)).Format();
                }
            }
        }

        public async Task SetBlobMetadataAsync(string containerName, string resourceId, string metadataKey, string metadataValue)
        {
            _counters.IncrementRateCounter("AzureBlob SetMetadataRate");

            using (_counters.CreateDisposableTimer("AzureBlob SetMetadataLatency"))
            {
                try
                {
                    CloudBlobContainer container = _blobClient.GetContainerReference(containerName);
                    CloudBlob blob = container.GetBlobReference(resourceId);

                    blob.Metadata.Add(metadataKey, metadataValue);

                    AccessCondition ac = AccessCondition.GenerateEmptyCondition();
                    BlobRequestOptions requestOptions = new BlobRequestOptions { RetryPolicy = new AzureRetryPolicy(RetryPolicy, additionalInfo: $"{containerName}") };

                    await blob.SetMetadataAsync(accessCondition: ac, options: requestOptions, operationContext: null);
                }
                catch (StorageException ex)
                {
                    string log =
                        $"Failed to set entity metadata with id {resourceId} to blob in container {containerName}";
                    Trace.TraceError("{0}, exception: {1}", log, ex);
                    _counters.IncrementRateCounter("AzureBlob SetMetadataFailureRate");

                    throw new RelInfraStorageException(log, ex, StorageErrorHelper.GetError(ex.RequestInformation.HttpStatusCode)).Format();
                }
            }
        }
    }
}
