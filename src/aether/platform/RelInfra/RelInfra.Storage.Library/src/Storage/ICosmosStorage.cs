﻿using System;
using System.Collections.Generic;
using System.Security.Cryptography.X509Certificates;
using System.Threading;
using System.Threading.Tasks;

namespace Microsoft.RelInfra.Storage
{
    public interface ICosmosStorage : IFileStorage
    {
        Task<IEnumerable<AetherStreamInfo>> ListDirectoryInfoAsync(string path, bool recursive, uint streamCountLimit = 20000);

        Task<AetherStreamInfo> GetStreamInfoAsync(string path);

        Task DownloadFileAsync(string cosmosFilePath, string localFilePath, bool overwrite = false);

        Task<bool> UploadFileAsync(string sourceFile, string destinationStream, bool respectLineBoundary, TimeSpan expirationTime, bool overwriteIfExists, CancellationToken cancellationToken);

        Task ConcatenateStreamAsync(string sourcePath, string destPath, CancellationToken token);

        Task<IEnumerable<AetherStreamInfo>> GetStreamSetInfoAsync(string path, bool sparseStreamSet);

        Task<bool> SetStreamExpirationTimeAsync(string streamPath, TimeSpan expirationTime);

        void Setup(X509Certificate2 certificate);

        Task<string[]> GetVcAccessSecurityGroupAsync(string vcName, bool excludeCertificates = true);
    }
}