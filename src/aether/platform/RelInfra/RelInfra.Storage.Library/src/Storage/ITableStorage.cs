﻿using System.Collections.Generic;
using System.Threading.Tasks;

namespace Microsoft.RelInfra.Storage
{
    public interface ITableStorage : IQueryableStorage
    {
        string TableName { get; }

        string SanitizeKey(string key);

        Task DeleteIfExistsAsync();

        Task<bool> TableExistsAsync();

        Task InsertOrReplaceAsync<T>(IEnumerable<Storable<T>> entities);

        Task<int> GetRowCountAsync<T>(string partitionKey);
    }
}
