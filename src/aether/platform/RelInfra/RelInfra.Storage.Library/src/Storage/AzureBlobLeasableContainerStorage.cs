﻿using Microsoft.RelInfra.Instrumentation;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace Microsoft.RelInfra.Storage
{
    public class AzureBlobLeasableContainerStorage : ILeasableStorage
    {
        private readonly IAzureBlobContainerStorage _blobContainer;
        
        public AzureBlobLeasableContainerStorage(string connectionString, int numRetries, TimeSpan intervalBetweenRetries, 
            CounterManager counterManager, string containerName)
            : this(leasableStorage: new AzureBlobStorage(connectionString: connectionString,
                                                    numRetries: numRetries,
                                                    intervalBetweenRetries: intervalBetweenRetries,
                                                    counterManager: counterManager),
                  containerName: containerName)
        {
        }

        public AzureBlobLeasableContainerStorage(IAzureBlobStorage leasableStorage, string containerName)
        {
            _blobContainer = new AzureBlobContainerStorage(leasableStorage, containerName: containerName);
        }
        public AzureBlobLeasableContainerStorage(IAzureBlobContainerStorage containerStorage)
        {
            _blobContainer = containerStorage;
        }
        public Task<string> AcquireLeaseAsync(string blobName, TimeSpan? leaseTime, string proposedLeaseId = null, CancellationToken cancellationToken = default(CancellationToken))
        {
            return _blobContainer.AcquireLeaseAsync(blobName: blobName,
                                                   leaseTime: leaseTime,
                                                   proposedLeaseId: proposedLeaseId,
                                                   cancellationToken: cancellationToken);
        }

        public Task DeleteAsync(string blobName)
        {
            return _blobContainer.DeleteAsync(resourceId: blobName);
        }

        public async Task InitializeBlobAsync(string blobName)
        {
            //
            // Create container if it doesn't exist
            //
            await _blobContainer.InitializeAsync();

            //
            // Create blob if it doesn't exist
            //
            bool blobExists = await _blobContainer.ResourceExistsAsync(blobName);

            if (!blobExists)
            {
                Exception raisedException = null;
                try
                {
                    await _blobContainer.TryPutAndIgnoreEtagAsync(blobName, new Storable<string>("CreateLease"));
                }
                catch (Exception ex)
                {
                    raisedException = ex;
                }

                // If something went wrong, let's check if blob was created or not. If it was created, exception will be swallowed.
                if (raisedException != null)
                {
                    bool blobCreated = await _blobContainer.ResourceExistsAsync(blobName);
                    if (!blobCreated)
                    {
                        throw raisedException;
                    }
                }
            }
        }

        public Task ReleaseLeaseAsync(string blobName, string leaseId, CancellationToken cancellationToken = default(CancellationToken))
        {
            return _blobContainer.ReleaseLeaseAsync(blobName: blobName,
                                                    leaseId: leaseId,
                                                    cancellationToken: cancellationToken);
        }

        public Task RenewLeaseAsync(string blobName, string leaseId, CancellationToken cancellationToken = default(CancellationToken))
        {
            return _blobContainer.RenewLeaseAsync(blobName: blobName,
                                                leaseId: leaseId,
                                                cancellationToken: cancellationToken);
        }
    }
}
