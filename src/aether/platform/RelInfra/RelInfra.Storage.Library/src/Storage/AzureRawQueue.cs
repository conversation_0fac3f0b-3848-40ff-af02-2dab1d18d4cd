﻿using Microsoft.WindowsAzure.Storage.Queue;

namespace Microsoft.RelInfra.Storage
{
    public class AzureRawQueue : AzureQueue<string>, IRawQueue
    {
        public AzureRawQueue(string connectionString, string queueName) : base(connectionString, queueName)
        {
        }

        override protected Storable<string> CloudMessageToEntity(CloudQueueMessage cloudMessage)
        {
            return new Storable<string>
            {
                Id = cloudMessage.Id,
                ContainerId = QueueName,
                Etag = cloudMessage.PopReceipt,
                AuxData = cloudMessage,
                Entity = cloudMessage.AsString
            };
        }

        override protected string EntityToCloudMessageContent(Storable<string> storableEntity)
        {
            return storableEntity.Entity;
        }
    }
}
