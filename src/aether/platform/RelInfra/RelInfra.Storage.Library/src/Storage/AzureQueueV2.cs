﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;
using Azure;
using Azure.Core;
using Azure.Storage;
using Azure.Storage.Queues;
using Azure.Storage.Queues.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.RelInfra.Common.RetryPolicy;
using Microsoft.RelInfra.Common.Serialization;
using Microsoft.RelInfra.Instrumentation.Logging;
using Newtonsoft.Json;

namespace Microsoft.RelInfra.Storage
{
    /// <summary>
    /// IQueue implementation based on new SDK(Azure.Storage.Queues) compared to
    /// <see cref="AzureQueue{T}"/> which is based on old deprecated SDK(Microsoft.WindowsAzure.Storage.Queue).
    /// It's a requirement to use WorkloadIdentity to initialize storage clients,
    /// so we need to upgrade to latest SDK which supports WorkloadIdentityCredential(extends <see cref="TokenCredential"/>).
    /// </summary>
    public class AzureQueueV2<T> : IQueueWithMedatata<T>
    {
        private const int MaxExecutionTimeInSeconds = 60;
        private static readonly TimeSpan MaximumVisibilityTimeout = TimeSpan.FromDays(7.0);

        private readonly QueueClient _queueClient;
        private readonly QueueServiceClient _queueServiceClient;

        public string QueueName => _queueClient.Name;

        // Defined in interface IQueue but not used.
        public IRelInfraRetryPolicy RetryPolicy { get; set; }

        public AzureQueueV2(
            Uri serviceUri,
            string queueName,
            TokenCredential credential,
            QueueClientOptions options = default)
        {
            if (serviceUri == null)
            {
                throw new ArgumentException("Service URI can't be null.", nameof(serviceUri));
            }

            if (string.IsNullOrWhiteSpace(queueName))
            {
                throw new ArgumentException("QueueName can't be null or empty", nameof(queueName));
            }

            _queueServiceClient = new QueueServiceClient(serviceUri, credential, options);
            _queueClient = _queueServiceClient.GetQueueClient(queueName);
        }

        public AzureQueueV2(
            Uri serviceUri,
            string queueName,
            AzureSasCredential credential,
            QueueClientOptions options = default)
        {
            if (serviceUri == null)
            {
                throw new ArgumentException("Service URI can't be null.", nameof(serviceUri));
            }

            if (string.IsNullOrWhiteSpace(queueName))
            {
                throw new ArgumentException("QueueName can't be null or empty", nameof(queueName));
            }

            _queueServiceClient = new QueueServiceClient(serviceUri, credential, options);
            _queueClient = _queueServiceClient.GetQueueClient(queueName);
        }

        public AzureQueueV2(
            Uri serviceUri,
            string queueName,
            StorageSharedKeyCredential credential,
            QueueClientOptions options = default)
        {
            if (serviceUri == null)
            {
                throw new ArgumentException("Service URI can't be null.", nameof(serviceUri));
            }

            if (string.IsNullOrWhiteSpace(queueName))
            {
                throw new ArgumentException("QueueName can't be null or empty", nameof(queueName));
            }

            _queueServiceClient = new QueueServiceClient(serviceUri, credential, options);
            _queueClient = _queueServiceClient.GetQueueClient(queueName);
        }

        /// <summary>
        /// Prefer constructor which uses <see cref="TokenCredential"/> instead of connection string.
        /// </summary>
        public AzureQueueV2(
            string connectionString,
            string queueName,
            QueueClientOptions options = default)
        {
            if (string.IsNullOrWhiteSpace(queueName))
            {
                throw new ArgumentException("QueueName can't be null or empty", nameof(queueName));
            }

            _queueServiceClient = new QueueServiceClient(connectionString, options);
            _queueClient = _queueServiceClient.GetQueueClient(queueName);

            CommonLogger.LogEntityInfo(nameof(AzureQueueV2<T>), $"Construct client with connection string. AccountName: [{_queueServiceClient.AccountName}]. QueueName: [{queueName}].");
        }

        public async Task InitializeQueueAsync()
        {
            await _queueClient.CreateIfNotExistsAsync().ConfigureAwait(false);
        }

        public async Task<Storable<T>> GetAsync()
        {
            return await GetAsync(MaximumVisibilityTimeout);
        }

        public async Task<Storable<T>> GetAsync(TimeSpan leaseTime)
        {
            using (var cts = new CancellationTokenSource(TimeSpan.FromSeconds(MaxExecutionTimeInSeconds)))
            {
                QueueMessage message = await _queueClient.ReceiveMessageAsync(leaseTime, cts.Token).ConfigureAwait(false);
                return message == null ? null : QueueMessageToEntity(message);
            }
        }

        public async Task<IEnumerable<Storable<T>>> GetMessagesAsync(int messageCount, TimeSpan leaseTime)
        {
            if (messageCount <= 0)
            {
                throw new ArgumentException("MessageCount must be positive value", nameof(messageCount));
            }

            // Azure queue doesn't return more than 32 messages with one call,
            // so if we want to pull larger number, we need to send several batch requests.
            var batches = new List<int>();
            int numBatches = messageCount / 32;
            for (int i = 0; i < numBatches; ++i)
            {
                batches.Add(32);
            }

            if (messageCount % 32 != 0)
            {
                batches.Add(messageCount % 32);
            }

            // send multiple requests
            IEnumerable<Task<IEnumerable<Storable<T>>>> taskList = batches.Select(batchSize => GetMessagesInternalAsync(batchSize, leaseTime)).ToList();
            await Task.WhenAll(taskList);
            return taskList.SelectMany(task => task.Result);
        }

        public async Task PutAsync(Storable<T> message)
        {
            if (message == null)
            {
                throw new ArgumentNullException(nameof(message));
            }

            string queueMessage = EntityToQueueMessageContent(message);
            using (var cts = new CancellationTokenSource(TimeSpan.FromSeconds(MaxExecutionTimeInSeconds)))
            {
                await _queueClient.SendMessageAsync(messageText: queueMessage, cancellationToken: cts.Token).ConfigureAwait(false);
            }
        }

        public async Task PutAsync(Storable<T> message, TimeSpan visibilityDelay)
        {
            if (message == null)
            {
                throw new ArgumentNullException(nameof(message));
            }

            string queueMessage = EntityToQueueMessageContent(message);
            using (var cts = new CancellationTokenSource(TimeSpan.FromSeconds(MaxExecutionTimeInSeconds)))
            {
                await _queueClient.SendMessageAsync(messageText: queueMessage, visibilityTimeout: visibilityDelay, cancellationToken: cts.Token).ConfigureAwait(false);
            }
        }

        public async Task PutAsync(Storable<T> message, TimeSpan? visibilityDelay, TimeSpan? timeToLive)
        {
            if (message == null)
            {
                throw new ArgumentNullException(nameof(message));
            }

            string queueMessage = EntityToQueueMessageContent(message);
            using (var cts = new CancellationTokenSource(TimeSpan.FromSeconds(MaxExecutionTimeInSeconds)))
            {
                await _queueClient.SendMessageAsync(messageText: queueMessage, visibilityTimeout: visibilityDelay, timeToLive: timeToLive, cancellationToken: cts.Token).ConfigureAwait(false);
            }
        }

        public async Task<Storable<T>> UpdateMessageAsync(Storable<T> message, TimeSpan leaseTime, MessageUpdateFlags flags)
        {
            if (message == null)
            {
                throw new ArgumentNullException(nameof(message));
            }

            if (message.AuxData == null)
            {
                throw new ArgumentException("AuxData can't be null", nameof(message.AuxData));
            }

            var queueMessage = message.AuxData as QueueMessage;
            if (queueMessage == null)
            {
                throw new ArgumentException("Argument is not a QueueMessage", nameof(message));
            }

            string updatedContent = EntityToQueueMessageContent(message);
            Response<UpdateReceipt> updatedResponse = null;
            using (var cts = new CancellationTokenSource(TimeSpan.FromSeconds(MaxExecutionTimeInSeconds)))
            {
                if (flags == MessageUpdateFlags.UpdateVisibility)
                {
                    updatedResponse = await _queueClient.UpdateMessageAsync(
                        messageId: queueMessage.MessageId,
                        popReceipt: queueMessage.PopReceipt,
                        visibilityTimeout: leaseTime,
                        cancellationToken: cts.Token).ConfigureAwait(false);
                }
                else
                {
                    updatedResponse = await _queueClient.UpdateMessageAsync(
                        messageId: queueMessage.MessageId,
                        popReceipt: queueMessage.PopReceipt,
                        messageText: updatedContent,
                        cancellationToken: cts.Token).ConfigureAwait(false);
                }
            }

            if (updatedResponse.HasValue)
            {
                UpdateQueueMessageWithUpdateReceipt(queueMessage, updatedResponse.Value);
            }

            return QueueMessageToEntity(queueMessage);
        }

        public async Task<bool> DeleteAsync(Storable<T> message)
        {
            if (message.AuxData is QueueMessage queueMessage)
            {
                return await DeleteAsync(queueMessage.MessageId, queueMessage.PopReceipt).ConfigureAwait(false);
            }
            else
            {
                throw new ArgumentException("Argument is not a QueueMessage", nameof(message));
            }
        }

        public async Task<bool> DeleteAsync(string messageId, string popReceipt)
        {
            if (string.IsNullOrWhiteSpace(messageId))
            {
                throw new ArgumentException("MessageId can't be null or empty", nameof(messageId));
            }

            if (string.IsNullOrWhiteSpace(popReceipt))
            {
                throw new ArgumentException("PopReceipt can't be null or empty", nameof(popReceipt));
            }

            try
            {
                using (var cts = new CancellationTokenSource(TimeSpan.FromSeconds(MaxExecutionTimeInSeconds)))
                {
                    var response = await _queueClient.DeleteMessageAsync(messageId, popReceipt, cts.Token).ConfigureAwait(false);
                }
            }
            catch (RequestFailedException e)
            {
                // If message is already deleted, return true;
                if (e.Status != StatusCodes.Status404NotFound)
                {
                    return false;
                }
            }

            return true;
        }

        public async Task<int> GetQueueLengthAsync()
        {
            using (var cts = new CancellationTokenSource(TimeSpan.FromSeconds(MaxExecutionTimeInSeconds)))
            {
                var propertiesResponse = await _queueClient.GetPropertiesAsync(cts.Token).ConfigureAwait(false);
                return propertiesResponse?.Value?.ApproximateMessagesCount ?? 0;
            }
        }

        public int GetDequeueCount(Storable<T> message)
        {
            if (message == null)
            {
                throw new ArgumentNullException(nameof(message));
            }

            if (message.AuxData is QueueMessage queueMessage)
            {
                return (int)queueMessage.DequeueCount;
            }
            else
            {
                throw new ArgumentException("Argument is not a QueueMessage", nameof(message));
            }
        }

        public TimeSpan GetMessageLifetime(Storable<T> message)
        {
            if (message == null)
            {
                throw new ArgumentNullException(nameof(message));
            }

            if (message.AuxData is QueueMessage queueMessage)
            {
                return queueMessage.InsertedOn == null
                    ? TimeSpan.Zero
                    : DateTime.UtcNow.Subtract(queueMessage.InsertedOn.Value.UtcDateTime);
            }
            else
            {
                throw new ArgumentException("Argument is not a QueueMessage", nameof(message));
            }
        }

        public async Task<IEnumerable<string>> ListAllQueueNamesAsync(string queueNamePrefix)
        {
            List<string> queueNames = new List<string>();
            var pageableQueues = _queueServiceClient.GetQueuesAsync(prefix: queueNamePrefix).ConfigureAwait(false);
            await foreach (var queue in pageableQueues)
            {
                queueNames.Add(queue.Name);
            }

            return queueNames;
        }

        public async Task<IDictionary<string, string>> GetMetadataAsync()
        {
            using (var cts = new CancellationTokenSource(TimeSpan.FromSeconds(MaxExecutionTimeInSeconds)))
            {
                var propertiesResponse = await _queueClient.GetPropertiesAsync(cts.Token).ConfigureAwait(false);
                return propertiesResponse?.Value?.Metadata;
            }
        }

        public async Task SetMetadataAsync(IDictionary<string, string> metadata)
        {
            using (var cts = new CancellationTokenSource(TimeSpan.FromSeconds(MaxExecutionTimeInSeconds)))
            {
                await _queueClient.SetMetadataAsync(metadata, cts.Token).ConfigureAwait(false);
            }
        }

        virtual protected Storable<T> QueueMessageToEntity(QueueMessage queueMessage)
        {
            var storableEntity = new Storable<T>
            {
                Id = queueMessage.MessageId,
                ContainerId = QueueName,
                Etag = queueMessage.PopReceipt,
                AuxData = queueMessage,
            };

            try
            {
                string messageContent = queueMessage.GetMessageContent();
                storableEntity.Entity = SerializationHelpers.DeserializeEntity<T>(messageContent);
            }
            catch (JsonReaderException e)
            {
                Trace.TraceError("Failed to deserialize message with id {0} from queue, ex: {1}", queueMessage.MessageId, e);
            }

            return storableEntity;
        }

        virtual protected string EntityToQueueMessageContent(Storable<T> storableEntity)
        {
            return SerializationHelpers.SerializeEntity(storableEntity.Entity);
        }

        private async Task<IEnumerable<Storable<T>>> GetMessagesInternalAsync(int messageCount, TimeSpan leaseTime)
        {
            using (var cts = new CancellationTokenSource(TimeSpan.FromSeconds(MaxExecutionTimeInSeconds)))
            {
                var messageResponse = (await _queueClient.ReceiveMessagesAsync(messageCount, leaseTime, cts.Token).ConfigureAwait(false));
                return messageResponse.Value.Select(QueueMessageToEntity);
            }
        }

        /// <summary>
        /// The PopReceipt/NextVisibleOn of QueueMessage may be changed with UpdateMessageAsync(to update content or visibility).
        /// And we need correct PopReceipt to be able to delete it.
        /// However, the PopReceipt/NextVisibleOn field is "internal set", so we need to use reflection to update it.
        /// </summary>
        private static void UpdateQueueMessageWithUpdateReceipt(QueueMessage queueMessage, UpdateReceipt updateReceipt)
        {
            var popReceiptProperty = queueMessage.GetType().GetProperty(nameof(queueMessage.PopReceipt), BindingFlags.Instance | BindingFlags.Public);
            if (popReceiptProperty != null && popReceiptProperty.CanWrite)
            {
                popReceiptProperty.SetValue(queueMessage, updateReceipt.PopReceipt);
            }
            else
            {
                Trace.TraceError("Fail to update QueueMessage {0} PopReceipt to {1}.", queueMessage.MessageId, updateReceipt.PopReceipt);
            }

            var nextVisibleOnProperty = queueMessage.GetType().GetProperty(nameof(queueMessage.NextVisibleOn), BindingFlags.Instance | BindingFlags.Public);
            if (nextVisibleOnProperty != null && nextVisibleOnProperty.CanWrite)
            {
                nextVisibleOnProperty.SetValue(queueMessage, updateReceipt.NextVisibleOn);
            }
            else
            {
                Trace.TraceError("Fail to update QueueMessage {0} NextVisibleOn to {1}.", queueMessage.MessageId, updateReceipt.NextVisibleOn);
            }
        }
    }
}
