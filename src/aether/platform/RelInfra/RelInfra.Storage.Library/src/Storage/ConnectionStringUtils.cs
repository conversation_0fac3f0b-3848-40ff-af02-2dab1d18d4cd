﻿using Microsoft.WindowsAzure.Storage;
using Microsoft.WindowsAzure.Storage.Auth;

namespace Microsoft.RelInfra.Storage
{
    public class ConnectionStringUtils
    {
        public static string GetTableStorageUri(string connectionString)
        {
            CloudStorageAccount storageAccount = CloudStorageAccount.Parse(connectionString);
            return storageAccount.TableStorageUri.PrimaryUri.AbsoluteUri;
        }

        public static string GetQueueStorageUri(string connectionString)
        {
            CloudStorageAccount storageAccount = CloudStorageAccount.Parse(connectionString);
            return storageAccount.QueueStorageUri.PrimaryUri.AbsoluteUri;
        }

        public static string GetBlobStorageUri(string connectionString)
        {
            CloudStorageAccount storageAccount = CloudStorageAccount.Parse(connectionString);
            return storageAccount.BlobStorageUri.PrimaryUri.AbsoluteUri;
        }

        public static string GetConnectionString(string accountName, string accountKey)
        {
            return new CloudStorageAccount(new StorageCredentials(accountName, accountKey), useHttps: true).ToString(exportSecrets: true);
        }
    }
}