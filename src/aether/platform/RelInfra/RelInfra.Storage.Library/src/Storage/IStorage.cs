﻿using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.RelInfra.Common.RetryPolicy;

namespace Microsoft.RelInfra.Storage
{
    public interface IStorage
    {
        IRelInfraRetryPolicy RetryPolicy { get; set; }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="containerName">Container name to which the resource is added (can be PartitionKey for Azure tables)</param>
        /// <param name="resourceId">Id of the given resource (can be RowKey for tables, or stream name for Cosmos storage)</param>
        /// <returns></returns>
        Task<bool> ExistsAsync(string containerName, string resourceId);

        /// <summary>
        /// Asynchronously add resource with given Id to the specified container in underlying storage
        /// </summary>
        /// <typeparam name="T">Type of object associated with resource</typeparam>
        /// <param name="containerName">Container name to which the resource is added (can be PartitionKey for Azure tables)</param>
        /// <param name="resourceId">Id of the given resource (can be RowKey for tables, or stream name for Cosmos storage)</param>
        /// <param name="resource"></param>
        Task<Storable<T>> AddAsync<T>(string containerName, string resourceId, Storable<T> resource);

        /// <summary>
        /// Puts resource with given Id to the specified container in underlying storage
        /// </summary>
        /// <typeparam name="T">Type of object associated with resource</typeparam>
        /// <param name="containerName">Container name to which the resource is added (can be PartitionKey for Azure tables)</param>
        /// <param name="resourceId">Id of the given resource (can be RowKey for tables, or stream name for Cosmos storage)</param>
        /// <param name="resource"></param>
        /// <returns>true if added successfully, false otherwise</returns>
        Task<bool> TryPutAndIgnoreEtagAsync<T>(string containerName, string resourceId, Storable<T> resource);

        /// <summary>
        /// Puts resource with given Id to the specified container in underlying storage
        /// </summary>
        /// <typeparam name="T">Type of object associated with resource</typeparam>
        /// <param name="containerName">Container name to which the resource is added (can be PartitionKey for Azure tables)</param>
        /// <param name="resourceId">Id of the given resource (can be RowKey for tables, or stream name for Cosmos storage)</param>
        /// <param name="resource"></param>
        Task<Storable<T>> PutAndIgnoreEtagAsync<T>(string containerName, string resourceId, Storable<T> resource);

        /// <summary>
        /// Executes batch put operation to the specified container in underlying storage
        /// </summary>
        /// <typeparam name="T">Type of object associated with resource</typeparam>
        /// <param name="containerName">Container name to which the resource is added (can be PartitionKey for Azure tables)</param>
        /// <param name="resources"></param>
        /// <returns>true if added successfully, false otherwise</returns>
        Task<bool> TryBatchPutAsync<T>(string containerName, IEnumerable<Storable<T>> resources);

        /// <summary>
        /// Executes batch put operation to the specified container in underlying storage
        /// </summary>
        /// <typeparam name="T">Type of object associated with resource</typeparam>
        /// <param name="containerName">Container name to which the resource is added (can be PartitionKey for Azure tables)</param>
        /// <param name="resources"></param>
        /// <returns>List of instance of <typeparamref name="T"/></returns>
        Task<IEnumerable<Storable<T>>> BatchPutAsync<T>(string containerName, IEnumerable<Storable<T>> resources);

        /// <summary>
        /// Asynchronously retrieves the resource with given Id from the specified container in underlying storage.
        /// Returns null if it fails to get item.
        /// </summary>
        /// <typeparam name="T">Type of object associated with resource</typeparam>
        /// <param name="containerName">Container name from which the resource is retrieved (can be PartitionKey for Azure tables)</param>
        /// <param name="resourceId">Id of the given resource (can be RowKey for tables, or stream name for Cosmos storage)</param>
        /// <returns>An instance of <typeparamref name="T"/> or default(T) if the specified resource was not found.</returns>
        Task<Storable<T>> TryGetAsync<T>(string containerName, string resourceId);

        /// <summary>
        /// Asynchronously retrieves the resource with given Id from the specified container in underlying storage.
        /// Throws RelInfraStorageException if it fails.
        /// </summary>
        /// <typeparam name="T">Type of object associated with resource</typeparam>
        /// <param name="containerName">Container name from which the resource is retrieved (can be PartitionKey for Azure tables)</param>
        /// <param name="resourceId">Id of the given resource (can be RowKey for tables, or stream name for Cosmos storage)</param>
        /// <returns>An instance of <typeparamref name="T"/></returns>
        Task<Storable<T>> GetAsync<T>(string containerName, string resourceId);

        /// <summary>
        /// Asynchronously retrieves resources with given Ids from the specified container in underlying storage.
        /// Throws RelInfraStorageException if it fails.
        /// </summary>
        /// <typeparam name="T">Type of object associated with resource</typeparam>
        /// <param name="containerName">Container name from which the resource is retrieved (can be PartitionKey for Azure tables)</param>
        /// <param name="resourceIds">Collection of id of resource to retrieve (can be RowKey for tables, or stream name for Cosmos storage)</param>
        /// <returns>List of instance of <typeparamref name="T"/></returns>
        Task<IEnumerable<Storable<T>>> BatchGetAsync<T>(string containerName, IEnumerable<string> resourceIds);

        /// <summary>
        /// Asynchronously update resource with given Id in the specified container in underlying storage.
        /// </summary>
        /// <typeparam name="T">Type of object associated with resource</typeparam>
        /// <param name="containerName">Container name to which the resource is added (can be PartitionKey for Azure tables)</param>
        /// <param name="resourceId">Id of the given resource (can be RowKey for tables, or stream name for Cosmos storage)</param>
        /// <param name="resource"></param>
        /// <returns>true if added successfully, false otherwise</returns>
        Task<bool> TryUpdateAsync<T>(string containerName, string resourceId, Storable<T> resource);

        /// <summary>
        /// Asynchronously update resource with given Id in the specified container in underlying storage.
        /// Throws RelInfraStorageException if it fails.
        /// </summary>
        /// <typeparam name="T">Type of object associated with resource</typeparam>
        /// <param name="containerName">Container name to which the resource is added (can be PartitionKey for Azure tables)</param>
        /// <param name="resourceId">Id of the given resource (can be RowKey for tables, or stream name for Cosmos storage)</param>
        /// <param name="resource"></param>
        /// <returns>An instance of <typeparamref name="T"/></returns>
        Task<Storable<T>> UpdateAsync<T>(string containerName, string resourceId, Storable<T> resource);

        /// <summary>
        /// Asynchronously updates given resources in the specified container in underlying storage.
        /// Throws RelInfraStorageException if it fails.
        /// </summary>
        /// <typeparam name="T">Type of object associated with resource</typeparam>
        /// <param name="containerName">Container name to which the resource is added (can be PartitionKey for Azure tables)</param>
        /// <param name="resources"></param>
        /// <returns>List of instance of <typeparamref name="T"/></returns>
        Task<IEnumerable<Storable<T>>> BatchUpdateAsync<T>(string containerName, IEnumerable<Storable<T>> resources);

        /// <summary>
        /// Asynchronously delete resource with given Id in the specified container in underlying storage.
        /// </summary>
        /// <param name="containerName">Container name from which the resource is deleted (can be PartitionKey for Azure tables)</param>
        /// <param name="resourceId">Id of the given resource (can be RowKey for tables, or stream name for Cosmos storage)</param>
        /// <returns>true if added successfully, false otherwise</returns>
        Task<bool> TryDeleteAsync(string containerName, string resourceId);

        /// <summary>
        /// Asynchronously delete resource with given Id in the specified container in underlying storage.
        /// Throws RelInfraStorageException if it fails.
        /// </summary>
        /// <param name="containerName">Container name from which the resource is deleted (can be PartitionKey for Azure tables)</param>
        /// <param name="resourceId">Id of the given resource (can be RowKey for tables, or stream name for Cosmos storage)</param>
        Task DeleteAsync(string containerName, string resourceId);

        /// <summary>
        /// Asynchronously delete resource with given Id in the specified container in underlying storage.
        /// Throws RelInfraStorageException if it fails.
        /// </summary>
        /// <param name="containerName">Container name from which the resource is deleted (can be PartitionKey for Azure tables)</param>
        /// <param name="resourceId">Id of the given resource (can be RowKey for tables, or stream name for Cosmos storage)</param>
        /// <param name="etag"></param>
        Task DeleteAsync(string containerName, string resourceId, string etag);

        /// <summary>
        /// Asynchronously delete many resources with given Ids in the specified container in underlying storage.
        /// Throws RelInfraStorageException if it fails.
        /// </summary>
        /// <param name="containerName">Container name from which the resource is deleted (can be PartitionKey for Azure tables)</param>
        /// <param name="resourceIds">Collection of Ids of the given resources (can be RowKey for tables, or stream name for Cosmos storage)</param>
        Task BatchDeleteAsync(string containerName, IEnumerable<string> resourceIds);

        /// <summary>
        /// Initialize Storage with given name [name is ignored for Tables, DocumentDb]
        /// </summary>
        /// <param name="containerName">Container name which we initialize</param>
        Task InitializeAsync(string containerName);
    }
}
