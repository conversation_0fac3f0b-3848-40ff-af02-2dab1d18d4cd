﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.RelInfra.Common.RetryPolicy;
using Microsoft.RelInfra.Common.Serialization;
using Microsoft.RelInfra.Instrumentation.Logging;
using Microsoft.WindowsAzure.Storage;
using Microsoft.WindowsAzure.Storage.Queue;
using Microsoft.WindowsAzure.Storage.RetryPolicies;
using Newtonsoft.Json;

namespace Microsoft.RelInfra.Storage
{
    public class AzureQueue<T> : IQueueWithMedatata<T>
    {
        public string QueueName { get; private set; }
        private readonly CloudQueueClient _queueClient;
        private const int MaxExecutionTimeInSeconds = 60;

        private IRelInfraRetryPolicy _retryPolicy;
        public IRelInfraRetryPolicy RetryPolicy
        {
            get { return _retryPolicy; }
            set
            {
                _retryPolicy = value;
                _queueClient.DefaultRequestOptions.RetryPolicy = new AzureRetryPolicy(value, new ExponentialRetry(), additionalInfo: QueueName);
            }
        }

        public IRetryPolicy AzureRetryPolicy
        {
            get { return _queueClient.DefaultRequestOptions.RetryPolicy; }
            set { _queueClient.DefaultRequestOptions.RetryPolicy = value; }
        }

        public AzureQueue(string connectionString, string queueName)
            : this(CloudStorageAccount.Parse(connectionString), queueName)
        {
        }

        public AzureQueue(CloudStorageAccount cloudStorageAccount, string queueName)
        {
            if (String.IsNullOrWhiteSpace(queueName))
            {
                throw new ArgumentException("queueName can't be null or empty", "queueName");
            }

            QueueName = queueName;
            _queueClient = cloudStorageAccount.CreateCloudQueueClient();
            _queueClient.DefaultRequestOptions.MaximumExecutionTime = TimeSpan.FromSeconds(MaxExecutionTimeInSeconds);

            CommonLogger.LogEntityInfo(nameof(AzureQueue<T>), $"Construct client with connection string. AccountName: [{_queueClient.Credentials?.AccountName}]. QueueName: [{queueName}].");
        }

        public async Task InitializeQueueAsync()
        {
            CloudQueue queue = _queueClient.GetQueueReference(QueueName);
            await queue.CreateIfNotExistsAsync();
        }

        public async Task<Storable<T>> GetAsync()
        {
            return await GetAsync(CloudQueueMessage.MaxVisibilityTimeout);
        }

        public async Task<Storable<T>> GetAsync(TimeSpan leaseTime)
        {
            CloudQueue queue = _queueClient.GetQueueReference(QueueName);

            OperationContext ctx = new OperationContext();
            using (CancellationTokenSource cancellationTokenSource = new CancellationTokenSource(TimeSpan.FromSeconds(MaxExecutionTimeInSeconds)))
            {
                CloudQueueMessage message = await queue.GetMessageAsync(leaseTime, null, ctx);
                return message == null ? null : CloudMessageToEntity(message);
            }
        }

        public async Task<IEnumerable<Storable<T>>> GetMessagesAsync(int messageCount, TimeSpan leaseTime)
        {
            if (messageCount <= 0)
            {
                throw new ArgumentException("messageCount must be positive value", "messageCount");
            }

            // azure doesn't return more than 32 messages with one call,
            // so if we want to pull larger number, we need to send several batch requests to Azure

            // calculate batches here
            var batches = new List<int>();
            int numBatches = messageCount / 32;
            for (int i = 0; i < numBatches; ++i)
            {
                batches.Add(32);
            }

            if (messageCount % 32 != 0)
            {
                batches.Add(messageCount % 32);
            }

            // send multiple requests
            IEnumerable<Task<IEnumerable<Storable<T>>>> taskList = batches.Select(batchSize => GetMessagesInternalAsync(batchSize, leaseTime)).ToList();
            await Task.WhenAll(taskList);
            return taskList.SelectMany(task => task.Result);
        }

        private async Task<IEnumerable<Storable<T>>> GetMessagesInternalAsync(
            int messageCount,
            TimeSpan leaseTime)
        {
            CloudQueue queue = _queueClient.GetQueueReference(QueueName);

            var ctx = new OperationContext();
            using (CancellationTokenSource cancellationTokenSource = new CancellationTokenSource(TimeSpan.FromSeconds(MaxExecutionTimeInSeconds)))
            {
                IEnumerable<CloudQueueMessage> messages = await queue.GetMessagesAsync(messageCount, leaseTime, null, ctx, cancellationTokenSource.Token);
                return messages.Select(CloudMessageToEntity);
            }
        }

        public async Task PutAsync(Storable<T> message)
        {
            if (message == null)
            {
                throw new ArgumentNullException("message");
            }

            CloudQueue queue = _queueClient.GetQueueReference(QueueName);

            CloudQueueMessage cloudMessage = new CloudQueueMessage(EntityToCloudMessageContent(message));
            using (CancellationTokenSource cancellationTokenSource = new CancellationTokenSource(TimeSpan.FromSeconds(MaxExecutionTimeInSeconds)))
            {
                await queue.AddMessageAsync(cloudMessage, timeToLive:null, initialVisibilityDelay: null, options: null, operationContext: null, cancellationToken: cancellationTokenSource.Token);
            }
        }

        public async Task PutAsync(Storable<T> message, TimeSpan visibilityDelay)
        {
            if (message == null)
            {
                throw new ArgumentNullException(nameof(message));
            }

            CloudQueueMessage cloudMessage = new CloudQueueMessage(EntityToCloudMessageContent(message));
            CloudQueue queue = _queueClient.GetQueueReference(QueueName);
            using (CancellationTokenSource cancellationTokenSource = new CancellationTokenSource(TimeSpan.FromSeconds(MaxExecutionTimeInSeconds)))
            {
                await queue.AddMessageAsync(cloudMessage, null, visibilityDelay, null, null, cancellationTokenSource.Token);
            }
        }

        public async Task PutAsync(Storable<T> message, TimeSpan? visibilityDelay, TimeSpan? timeToLive)
        {
            if (message == null)
            {
                throw new ArgumentNullException(nameof(message));
            }

            CloudQueue queue = _queueClient.GetQueueReference(QueueName);

            CloudQueueMessage cloudMessage = new CloudQueueMessage(EntityToCloudMessageContent(message));
            using (CancellationTokenSource cancellationTokenSource = new CancellationTokenSource(TimeSpan.FromSeconds(MaxExecutionTimeInSeconds)))
            {
                await queue.AddMessageAsync(cloudMessage, timeToLive, visibilityDelay, null, new OperationContext(), cancellationTokenSource.Token).ConfigureAwait(false);
            }
        }

        public async Task<Storable<T>> UpdateMessageAsync(Storable<T> message, TimeSpan leaseTime, MessageUpdateFlags flags)
        {
            if (String.IsNullOrWhiteSpace(QueueName))
            {
                throw new ArgumentException("queueName can't be null or empty", "queueName");
            }

            if (message == null)
            {
                throw new ArgumentNullException("message", "message can't be null");
            }

            if (message.AuxData == null)
            {
                throw new ArgumentException("AuxData can't be null", "message");
            }

            CloudQueue queue = _queueClient.GetQueueReference(QueueName);

            CloudQueueMessage cloudMessage = (CloudQueueMessage)message.AuxData;
            cloudMessage.SetMessageContent(EntityToCloudMessageContent(message));

            using (CancellationTokenSource cancellationTokenSource = new CancellationTokenSource(TimeSpan.FromSeconds(MaxExecutionTimeInSeconds)))
            {
                await queue.UpdateMessageAsync(cloudMessage, leaseTime, (MessageUpdateFields)flags, options: null, operationContext: null, cancellationTokenSource.Token);
            }

            return CloudMessageToEntity(cloudMessage);
        }

        public async Task<bool> DeleteAsync(Storable<T> message)
        {
            bool success = false;
            // TODO yanrez 20160615
            // Looks like in *some* cases _queueMessage.Etag is not updated, but _queueMessage.AuxData.PopReceipt is. At the same time it's unclear if ETag can be just never used here, therefore we attempt to delete based on both values.
            // if delete is called with wrong etag/popreceipt, it simply returns and never throws.
            // This flow MUST be reviewed and fixed
            object auxData = message.AuxData;
            if (auxData is CloudQueueMessage)
            {
                success |= await DeleteAsync(message.Id, ((CloudQueueMessage)auxData).PopReceipt);
            }
            success |= await DeleteAsync(message.Id, message.Etag);

            return success;
        }

        public async Task<bool> DeleteAsync(string messageId, string etag)
        {
            if (String.IsNullOrWhiteSpace(messageId))
            {
                throw new ArgumentException("messageId can't be null or empty", "messageId");
            }

            if (String.IsNullOrWhiteSpace(etag))
            {
                throw new ArgumentException("etag can't be null or empty", "etag");
            }

            try
            {
                CloudQueue queue = _queueClient.GetQueueReference(QueueName);
                var ctx = new OperationContext();
                using (CancellationTokenSource cancellationTokenSource = new CancellationTokenSource(TimeSpan.FromSeconds(MaxExecutionTimeInSeconds)))
                {
                    await queue.DeleteMessageAsync(messageId, etag, null, ctx, cancellationTokenSource.Token);
                }
            }
            catch (StorageException ex) //shoudl this catch aggregate exceoption too?
            {
                if (ex.RequestInformation.HttpStatusCode != (int)HttpStatusCode.NotFound)
                {
                    Trace.TraceError("Failed to delete message from Azure queue, message id: {0}, exception: {1}",
                       messageId, ex);
                    return false;
                }
            }

            return true;
        }

        virtual protected Storable<T> CloudMessageToEntity(CloudQueueMessage cloudMessage)
        {
            var storableEntity = new Storable<T>
                {
                    Id = cloudMessage.Id,
                    ContainerId = QueueName,
                    Etag = cloudMessage.PopReceipt,
                    AuxData = cloudMessage
                };
            try
            {
                string messageContent = cloudMessage.GetMessageContent();
                storableEntity.Entity = SerializationHelpers.DeserializeEntity<T>(messageContent);
            }
            catch (JsonReaderException e)
            {
                Trace.TraceError("Failed to deserialize message with id {0} from queue, ex: {1}", cloudMessage.Id, e);
            }

            return storableEntity;
        }

        virtual protected string EntityToCloudMessageContent(Storable<T> storableEntity)
        {
            return SerializationHelpers.SerializeEntity(storableEntity.Entity);
        }

        public int GetDequeueCount(Storable<T> storableObj)
        {
            if (storableObj == null)
            {
                throw new ArgumentNullException("storableObj", "storableObj can't be null");
            }

            var cloudQueueMessage = storableObj.AuxData as CloudQueueMessage;
            if (cloudQueueMessage == null)
            {
                throw new ArgumentException("Argument is not an azure queue message");
            }

            return cloudQueueMessage.DequeueCount;
        }

        public async Task<int> GetQueueLengthAsync()
        {
            CloudQueue queue = _queueClient.GetQueueReference(QueueName);
            using (CancellationTokenSource cancellationTokenSource = new CancellationTokenSource(TimeSpan.FromSeconds(MaxExecutionTimeInSeconds)))
            {
                await queue.FetchAttributesAsync(options: null, operationContext: null, cancellationToken: cancellationTokenSource.Token);
            }
            return queue.ApproximateMessageCount ?? 0;
        }

        public TimeSpan GetMessageLifetime(Storable<T> message)
        {
            if (message == null)
            {
                throw new ArgumentNullException("message", "message can't be null");
            }

            var cloudQueueMessage = message.AuxData as CloudQueueMessage;
            if (cloudQueueMessage == null)
            {
                throw new ArgumentException("Argument is not an azure queue message");
            }

            return cloudQueueMessage.InsertionTime == null
                ? TimeSpan.Zero
                : DateTime.UtcNow.Subtract(cloudQueueMessage.InsertionTime.Value.UtcDateTime);
        }

        public async Task<IEnumerable<string>> ListAllQueueNamesAsync(string queueNamePrefix)
        {
            QueueContinuationToken token = null;

            List<string> queueNames = new List<string>();

            do
            {
                QueueResultSegment segment = await  _queueClient.ListQueuesSegmentedAsync(queueNamePrefix, token);
                queueNames.AddRange(segment.Results.Select(queue => queue.Name));
                token = segment.ContinuationToken;
            } while (token != null);

            return queueNames;
        }

        public async Task<IDictionary<string, string>> GetMetadataAsync()
        {
            CloudQueue queue = _queueClient.GetQueueReference(QueueName);
            using (CancellationTokenSource cancellationTokenSource = new CancellationTokenSource(TimeSpan.FromSeconds(MaxExecutionTimeInSeconds)))
            {
                await queue.FetchAttributesAsync(options: null, operationContext: null, cancellationToken: cancellationTokenSource.Token);
            }
            return new Dictionary<string, string>(queue.Metadata);
        }

        public async Task SetMetadataAsync(IDictionary<string, string> metadata)
        {
            CloudQueue queue = _queueClient.GetQueueReference(QueueName);
            queue.Metadata.Clear();
            foreach (KeyValuePair<string, string> pair in metadata)
            {
                queue.Metadata.Add(pair);
            }
            using (CancellationTokenSource cancellationTokenSource = new CancellationTokenSource(TimeSpan.FromSeconds(MaxExecutionTimeInSeconds)))
            {
                await queue.SetMetadataAsync(options: null, operationContext: null, cancellationToken: cancellationTokenSource.Token);
            }
        }
    }
}
