﻿using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace Microsoft.RelInfra.Storage
{
    public interface IDbStorage
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="containerName">Container name to which the resource is added (can be PartitionKey for Azure tables)</param>
        /// <param name="resourceId">Id of the given resource (can be Row<PERSON><PERSON> for tables, or stream name for Cosmos storage)</param>
        /// <returns></returns>
        Task<bool> ExistsAsync(string containerName, string resourceId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Asynchronously retrieves the resource with given Id from the specified container in underlying storage.
        /// Returns null if it fails to get item.
        /// </summary>
        /// <typeparam name="T">Type of object associated with resource</typeparam>
        /// <param name="containerName">Container name from which the resource is retrieved (can be PartitionKey for Azure tables)</param>
        /// <param name="resourceId">Id of the given resource (can be <PERSON><PERSON><PERSON> for tables, or stream name for Cosmos storage)</param>
        /// <returns>An instance of <typeparamref name="T"/> or default(T) if the specified resource was not found.</returns>
        Task<Storable<T>> TryGetAsync<T>(string containerName, string resourceId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Asynchronously retrieves the resource with given Id from the specified container in underlying storage.
        /// Throws RelInfraStorageException if it fails.
        /// </summary>
        /// <typeparam name="T">Type of object associated with resource</typeparam>
        /// <param name="containerName">Container name from which the resource is retrieved (can be PartitionKey for Azure tables)</param>
        /// <param name="resourceId">Id of the given resource (can be RowKey for tables, or stream name for Cosmos storage)</param>
        /// <returns>An instance of <typeparamref name="T"/></returns>
        Task<Storable<T>> GetAsync<T>(string containerName, string resourceId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Asynchronously retrieves resources with given Ids from the specified container in underlying storage.
        /// Throws RelInfraStorageException if it fails.
        /// </summary>
        /// <typeparam name="T">Type of object associated with resource</typeparam>
        /// <param name="containerName">Container name from which the resource is retrieved (can be PartitionKey for Azure tables)</param>
        /// <param name="resourceIds">Collection of id of resource to retrieve (can be RowKey for tables, or stream name for Cosmos storage)</param>
        /// <returns>List of instance of <typeparamref name="T"/></returns>
        Task<IEnumerable<Storable<T>>> BatchGetAsync<T>(string containerName, IEnumerable<string> resourceIds, CancellationToken cancellationToken = default);
        /// <summary>
        /// Asynchronously delete resource with given Id in the specified container in underlying storage.
        /// </summary>
        /// <param name="containerName">Container name from which the resource is deleted (can be PartitionKey for Azure tables)</param>
        /// <param name="resourceId">Id of the given resource (can be RowKey for tables, or stream name for Cosmos storage)</param>
        /// <returns>true if added successfully, false otherwise</returns>
        Task<bool> TryDeleteAsync(string containerName, string resourceId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Asynchronously delete resource with given Id in the specified container in underlying storage.
        /// Throws RelInfraStorageException if it fails.
        /// </summary>
        /// <param name="containerName">Container name from which the resource is deleted (can be PartitionKey for Azure tables)</param>
        /// <param name="resourceId">Id of the given resource (can be RowKey for tables, or stream name for Cosmos storage)</param>
        Task DeleteAsync(string containerName, string resourceId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Asynchronously delete resource with given Id in the specified container in underlying storage.
        /// Throws RelInfraStorageException if it fails.
        /// </summary>
        /// <param name="containerName">Container name from which the resource is deleted (can be PartitionKey for Azure tables)</param>
        /// <param name="resourceId">Id of the given resource (can be RowKey for tables, or stream name for Cosmos storage)</param>
        /// <param name="etag"></param>
        Task DeleteAsync(string containerName, string resourceId, string etag, CancellationToken cancellationToken = default);

        /// <summary>
        /// Asynchronously delete many resources with given Ids in the specified container in underlying storage.
        /// Throws RelInfraStorageException if it fails.
        /// </summary>
        /// <param name="containerName">Container name from which the resource is deleted (can be PartitionKey for Azure tables)</param>
        /// <param name="resourceIds">Collection of Ids of the given resources (can be RowKey for tables, or stream name for Cosmos storage)</param>
        Task BatchDeleteAsync(string containerName, IEnumerable<string> resourceIds, CancellationToken cancellationToken = default);

        /// <summary>
        /// Initialize Storage with given name [name is ignored for Tables, DocumentDb]
        /// </summary>
        /// <param name="containerName">Container name which we initialize</param>
        Task InitializeAsync(string containerName);

        /// <summary>
        /// Puts resource with given Id to the specified container in underlying storage
        /// </summary>
        /// <typeparam name="T">Type of object associated with resource</typeparam>
        /// <param name="resource"></param>
        Task<Storable<T>> PutAsync<T>(Storable<T> resource, CancellationToken cancellationToken = default) where T : ICosmosDbEntity;

        /// <summary>
        /// Puts resource with given Id to the specified container in underlying storage
        /// </summary>
        /// <typeparam name="T">Type of object associated with resource</typeparam>
        /// <param name="resource"></param>
        /// <returns>true if added successfully, false otherwise</returns>
        Task<bool> TryPutAsync<T>(Storable<T> resource, CancellationToken cancellationToken = default) where T : ICosmosDbEntity;

        Task<Storable<T>> AddAsync<T>(T resource, CancellationToken cancellationToken = default) where T : ICosmosDbEntity;
        Task<Storable<T>> UpdateAsync<T>(Storable<T> resource, CancellationToken cancellationToken = default) where T : ICosmosDbEntity;

        Task<IEnumerable<Storable<T>>> ExecuteQueryAsync<T>(string query, int? takeCount = null, CancellationToken cancellationToken = default);
        Task<QueryResults<T>> ExecuteQueryAsync<T>(string query, string continuationToken, int? takeCount = null, CancellationToken cancellationToken = default);

        Task<QueryResults<T>> ExecuteQueryNonStorableAsync<T>(string query, int? takeCount = null, List<KeyValuePair<string, string>> parameters = null, CancellationToken cancellationToken = default);

        Task<QueryResults<T>> GetPartitionWithCountinuationTokenAsync<T>(string partitionKey, string continuationToken, int? takeCount = null, CancellationToken cancellationToken = default);
        Task<QueryResults<T>> GetPartitionWithCountinuationTokenAsync<T>(string partitionKey, IEnumerable<string> columns, string continuationToken = null, int? takeCount = null, CancellationToken cancellationToken = default);
        Task<IEnumerable<Storable<T>>> GetPartitionAsync<T>(string partitionKey, int? takeCount = null, CancellationToken cancellationToken = default);
        Task<IEnumerable<Storable<T>>> GetAllAsync<T>(int? takeCount = null, CancellationToken cancellationToken = default);
        Task<QueryResults<T>> GetAllWithContinuationTokenAsync<T>(string continuationToken = null, int? takeCount = null, CancellationToken cancellationToken = default);
        Task<Storable<T>> UpdateWithTTLAsync<T>(Storable<T> resource, int? ttl) where T: ICosmosDbEntity;
    }
}
