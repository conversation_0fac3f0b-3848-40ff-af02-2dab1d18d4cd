﻿using System.IO.Compression;
using System.IO;
using System.Text;

namespace Microsoft.RelInfra.Storage
{
    public static class CompressObjectHelper
    {
        public const string CompressedSuffix = "IsCompressed";

        public static byte[] Compress(string str)
        {
            byte[] bytes = Encoding.UTF8.GetBytes(str);
            using (var memoryStream = new MemoryStream())
            {
                using (var brotliStream = new BrotliStream(memoryStream, CompressionLevel.Optimal))
                {
                    brotliStream.Write(bytes, 0, bytes.Length);
                }

                return memoryStream.ToArray();
            }
        }

        public static string Decompress(byte[] bytes)
        {
            using (var memoryStream = new MemoryStream(bytes))
            {
                using (var outputStream = new MemoryStream())
                {
                    using (var decompressStream = new BrotliStream(memoryStream, CompressionMode.Decompress))
                    {
                        decompressStream.CopyTo(outputStream);
                    }

                    return Encoding.UTF8.GetString(outputStream.ToArray());
                }
            }
        }

        // For CompressObject like AscendantNodeList, if its column raw value size exceeds max limit, it would be compressed.
        // After compression, type of AscendantNodeList in table entity would be Binary instead of String.
        // Table query "not(AscendantNodeList gt '')" is used for filter null or empty string for String type value, but it doesn't work for Binary type value.
        // Add "not(AscendantNodeListIsCompressed eq 'true')" to handle Binary case. Since if value is compressed, it wouldn't be null or empty string.
        public static string GenerateIsNullOrEmptyStringFilterQuery(string columnName)
        {
            return $"not({columnName} gt '') and not({columnName}{CompressedSuffix} eq 'true')";
        }
    }
}
