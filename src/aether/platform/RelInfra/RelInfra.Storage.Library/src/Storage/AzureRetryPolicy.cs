﻿using System;
using Microsoft.RelInfra.Common.RetryPolicy;
using Microsoft.RelInfra.Instrumentation.Logging;
using Microsoft.WindowsAzure.Storage;
using Microsoft.WindowsAzure.Storage.RetryPolicies;

namespace Microsoft.RelInfra.Storage
{
    internal class AzureRetryPolicy : IRetryPolicy
    {
        private readonly IRelInfraRetryPolicy _relInfraRetryPolicy;
        private readonly IRetryPolicy _azureRetryPolicy;
        private readonly string _additionalInfo;

        public AzureRetryPolicy(IRelInfraRetryPolicy relInfraRetryPolicy, string additionalInfo)
        {
            _relInfraRetryPolicy = (IRelInfraRetryPolicy)relInfraRetryPolicy.Clone();
            _additionalInfo = additionalInfo;
        }

        public AzureRetryPolicy(IRetryPolicy azureRetryPolicy, string additionalInfo)
        {
            _azureRetryPolicy = azureRetryPolicy.CreateInstance();
            _additionalInfo = additionalInfo;
        }

        public AzureRetryPolicy(IRelInfraRetryPolicy relInfraRetryPolicy, IRetryPolicy azureRetryPolicy, string additionalInfo)
        {
            _relInfraRetryPolicy = (IRelInfraRetryPolicy)relInfraRetryPolicy.Clone();
            _azureRetryPolicy = azureRetryPolicy.CreateInstance();
            _additionalInfo = additionalInfo;
        }

        public IRetryPolicy CreateInstance()
        {
            if (_azureRetryPolicy == null)
            {
                return new AzureRetryPolicy(_relInfraRetryPolicy, _additionalInfo);
            }

            if (_relInfraRetryPolicy == null)
            {
                return new AzureRetryPolicy(_azureRetryPolicy, _additionalInfo);
            }

            return new AzureRetryPolicy(_relInfraRetryPolicy, _azureRetryPolicy, _additionalInfo);
        }

        public bool ShouldRetry(int currentRetryCount, int statusCode, Exception lastException, out TimeSpan retryInterval,
                                OperationContext operationContext)
        {
            CommonLogger.LogInfo($"AzureRetryPolicy.ShouldRetry: meta[{_additionalInfo}],count[{currentRetryCount}],statuscode[{statusCode}],exType[{lastException?.GetType()?.Name}]");

            retryInterval = TimeSpan.Zero;
            if (_relInfraRetryPolicy != null && _relInfraRetryPolicy.ShouldRetry(currentRetryCount, statusCode, lastException, out retryInterval))
            {
                return true;
            }

            return _azureRetryPolicy != null 
                && _azureRetryPolicy.ShouldRetry(currentRetryCount, statusCode, lastException, out retryInterval, operationContext);
        }
    }
}
