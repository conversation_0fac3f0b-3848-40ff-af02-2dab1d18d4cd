﻿using System;
using System.IO;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.RelInfra.Instrumentation;
using Microsoft.RelInfra.Instrumentation.Logging;
using Microsoft.RelInfra.Instrumentation.Exceptions;
using Microsoft.RelInfra.Storage.Exceptions;
using Microsoft.WindowsAzure.Storage;
using Microsoft.WindowsAzure.Storage.Blob;
using Microsoft.WindowsAzure.Storage.RetryPolicies;
using System.Collections.Generic;
using System.Linq;
using Microsoft.RelInfra.Common.RetryExecution;
using Microsoft.RelInfra.Common.RetryPolicy;

namespace Microsoft.RelInfra.Storage
{
    public interface IAzureSasUriClient
    {
        Task UploadToWritableUriAppendBlobAsync(string writableSasUri, string message);
        Task UploadToWritableUriAppendBlobAsync(string writableSasUri, Stream stream, CancellationToken cancellationToken);
        Task UploadToWritableUriBlockBlobAsync(string writableSasUri, string message);
        Task UploadToWritableUriFromFileBlockBlobAsync(string writableSasUri, string filePath, CancellationToken cancellationToken);
        Task UploadToWritableUriBlockBlobAsync(string writableSasUri, Stream stream, CancellationToken cancellationToken);
        Task<Stream> GetStreamAsync(string readableUri);

        Task AppendToWritableUriAsync(string writableSasUri, string message);

    }

    public class AzureSasUriClient : IAzureSasUriClient
    {
        private readonly CounterManager _counterManager;
        private readonly TimeSpan _intervalBetweenRetries = TimeSpan.FromSeconds(5);
        private readonly int _numberOfRetries = 100;
        private const int MaxBytesToAppend = 67108864; // 64 MB

        public AzureSasUriClient(CounterManager counterManager)
        {
            _counterManager = counterManager;
        }

        public async Task UploadToWritableUriBlockBlobAsync(string writableSasUri, string message)
        {
            try
            {
                using (var msWrite = new MemoryStream(Encoding.UTF8.GetBytes(message)))
                {
                    await UploadToWritableUriBlockBlobAsync(writableSasUri, msWrite, CancellationToken.None);
                }
            }
            catch (StorageException exc)
            {
                string redactedSasUri = RedactSasUri(writableSasUri);
                CommonLogger.LogEntityError("{redacted_sas_uri}",
                                            "Failed uploading to Azure. Message: {truncate_message}, error: {exception_message}, stack trace: {stack_trace}", redactedSasUri,
                                            TruncateMessage(message), exc.Message, exc.StackTrace);
                throw;
            }
        }

        public async Task UploadToWritableUriFromFileBlockBlobAsync(string writableSasUri, string filePath, CancellationToken cancellationToken)
        {
            try
            {
                using (Stream stream = File.OpenRead(filePath))
                {
                    await UploadToWritableUriBlockBlobAsync(writableSasUri, stream, cancellationToken);
                }
            }
            catch (Exception e)
            {
                string redactedSasUri = RedactSasUri(writableSasUri);
                CommonLogger.LogEntityError("{redacted_sas_uri}", "Failed uploading file {user_file_path} to Azure, error: {exception_message}, stack trace: {stack_trace}", redactedSasUri, filePath, e.Message, e.StackTrace);
                throw;
            }
        }

        public virtual async Task UploadToWritableUriBlockBlobAsync(string writableSasUri, Stream stream, CancellationToken cancellationToken)
        {
            try
            {
                if (!string.IsNullOrEmpty(writableSasUri))
                {
                    _counterManager.GetRateCounter("AzureSasUriClient UploadToWritableUriBlockBlobRate").Increment();
                    using (_counterManager.GetLatencyCounter("AzureSasUriClient UploadToWritableUriBlockBlobLatency").StartCounter())
                    {
                        CloudBlockBlob writeAccessUriBlob = new CloudBlockBlob(new Uri(writableSasUri));
                        AccessCondition accessCondition = AccessCondition.GenerateEmptyCondition();
                        BlobRequestOptions requestOptions = new BlobRequestOptions
                        {
                            RetryPolicy = new LinearRetry(_intervalBetweenRetries, _numberOfRetries)
                        };

                        await writeAccessUriBlob.UploadFromStreamAsync(stream, accessCondition, requestOptions, new OperationContext());
                    }
                }
            }
            catch (StorageException exc)
            {
                _counterManager.GetRateCounter("AzureSasUriClient UploadToWritableUriBlockBlobFailureRate").Increment();
                throw new RelInfraStorageException("UploadToWritableUriBlockBlobAsync failed", exc, StorageErrorHelper.GetError(exc.RequestInformation.HttpStatusCode)).Format();
            }
        }

        public async Task UploadToWritableUriAppendBlobAsync(string writableSasUri, string message)
        {
            try
            {
                using (var msWrite = new MemoryStream(Encoding.UTF8.GetBytes(message)))
                {
                    await UploadToWritableUriAppendBlobAsync(writableSasUri, msWrite, CancellationToken.None);
                }
            }
            catch (StorageException exc)
            {
                string redactedSasUri = RedactSasUri(writableSasUri);
                CommonLogger.LogEntityError("{redacted_sas_uri}",
                                            "Failed uploading to Azure. Message: {truncate_message}, error: {exception_message}, stack trace: {stack_trace}", redactedSasUri,
                                            TruncateMessage(message), exc.Message, exc.StackTrace);
                throw;
            }
        }
        
        public async Task UploadToWritableUriAppendBlobAsync(string writableSasUri, Stream stream, CancellationToken cancellationToken)
        {
            try
            {
                if (!string.IsNullOrEmpty(writableSasUri))
                {
                    _counterManager.GetRateCounter("AzureSasUriClient UploadToWritableUriRate").Increment();
                    using (_counterManager.GetLatencyCounter("AzureSasUriClient UploadToWritableUriLatency").StartCounter())
                    {
                        CloudAppendBlob writeAccessUriBlob = new CloudAppendBlob(new Uri(writableSasUri));
                        AccessCondition accessCondition = AccessCondition.GenerateEmptyCondition();
                        BlobRequestOptions requestOptions = new BlobRequestOptions
                        {
                            RetryPolicy = new LinearRetry(_intervalBetweenRetries, _numberOfRetries)
                        };

                        await writeAccessUriBlob.UploadFromStreamAsync(stream, accessCondition, requestOptions, new OperationContext());
                    }
                }
            }
            catch (StorageException exc)
            {
                _counterManager.GetRateCounter("AzureSasUriClient UploadToWritableUriFailureRate").Increment();
                throw new RelInfraStorageException("UploadToWritableUriAsync failed", exc, StorageErrorHelper.GetError(exc.RequestInformation.HttpStatusCode)).Format();
            }
        }

        public virtual async Task CreateIfNotExistsAsync(string writableSasUri)
        {
            CloudAppendBlob writeAccessUriBlob = new CloudAppendBlob(new Uri(writableSasUri));
            if (!await writeAccessUriBlob.ExistsAsync())
            {
                await writeAccessUriBlob.UploadTextAsync(string.Empty);
            }
        }

        public virtual async Task AppendToWritableUriAsync(string writableSasUri, Stream stream)
        {
            try
            {
                if (string.IsNullOrEmpty(writableSasUri))
                {
                    return;
                }
                _counterManager.GetRateCounter("AzureSasUriClient AppendToWritableUriRate").Increment();
                using (
                    _counterManager.GetLatencyCounter("AzureSasUriClient AppendToWritableUriLatency").StartCounter()
                    )
                {
                    AccessCondition accessCondition = AccessCondition.GenerateEmptyCondition();
                    BlobRequestOptions requestOptions = new BlobRequestOptions
                    {
                        RetryPolicy = new ExponentialRetry(_intervalBetweenRetries, _numberOfRetries)
                    };

                    CloudAppendBlob writeAccessUriBlob =
                                    new CloudAppendBlob(new Uri(writableSasUri));

                    // AppendBlob (and BlockBlob) can not append data beyond a limit so we need to break our message in blocks.
                    long msgByteLength = stream.Length;
                    _counterManager.GetNumberCounter("AzureSasUriClient AppendToWritableUriMessageSizeBytes")
                        .Set(msgByteLength);
                    long copied = 0;
                    while (msgByteLength > copied)
                    {
                        var length = Math.Min(MaxBytesToAppend, msgByteLength - copied);
                                                       
                        await RetryExecutionHelper.ExecuteFuncWithRetryAsync(action: async () =>
                            {
                                await
                                    writeAccessUriBlob.AppendFromStreamAsync(source: stream,
                                    length: length,
                                    accessCondition: accessCondition, options: requestOptions,
                                        operationContext: new OperationContext());
                            },
                            shouldRetryException: ex =>
                            {
                                if (ex.GetType() == typeof(StorageException) &&
                                    ((StorageException)ex).RequestInformation.HttpStatusCode == 412
                                ) //The append position condition specified was not met
                            {
                                    string redactedSasUri = RedactSasUri(writableSasUri);
                                    CommonLogger.LogEntityInfo("{redacted_sas_uri}", "Retry due to exception: {exception_message}, stack trace: {stack_trace}", redactedSasUri, ex.Message, ex.StackTrace);
                                    return true;
                                }

                                return false;
                            },
                            retryPolicy: new LinearRetryPolicy(retryIntervalinSec: TimeSpan.FromSeconds(5),
                                maxAttempts: 5),
                            token: CancellationToken.None
                        );

                        copied += length;
                        if (copied < msgByteLength)
                        {
                            await Task.Delay(TimeSpan.FromSeconds(1));
                            // Sleeping for a second to ensure we don't cross throttling limits (https://docs.microsoft.com/en-us/azure/storage/storage-scalability-targets)
                        }
                    }
                }
            }
            catch (StorageException exc)
            {
                _counterManager.GetRateCounter("AzureSasUriClient AppendToWritableUriFailureRate").Increment();
                throw new RelInfraStorageException("AppendToWritableUriAsync failed", exc, StorageErrorHelper.GetError(exc.RequestInformation.HttpStatusCode)).Format();
            }
        }

        public async Task AppendToWritableUriAsync(string writableSasUri, string message)
        {
            try
            {
                if (!string.IsNullOrEmpty(writableSasUri))
                {
                    _counterManager.GetRateCounter("AzureSasUriClient AppendToWritableUriRate").Increment();
                    using (
                        _counterManager.GetLatencyCounter("AzureSasUriClient AppendToWritableUriLatency").StartCounter()
                        )
                    {
                        AccessCondition accessCondition = AccessCondition.GenerateEmptyCondition();
                        BlobRequestOptions requestOptions = new BlobRequestOptions
                        {
                            RetryPolicy = new ExponentialRetry(_intervalBetweenRetries, _numberOfRetries)
                        };

                        // AppendBlob (and BlockBlob) can not append data beyond a limit so we need to break our message in blocks.
                        byte[] msgBytes = Encoding.UTF8.GetBytes(message);
                        int msgByteLength = Encoding.UTF8.GetByteCount(message);
                        _counterManager.GetNumberCounter("AzureSasUriClient AppendToWritableUriMessageSizeBytes")
                            .Set(msgByteLength);
                        int startIndex = 0;
                        do
                        {
                            var index = startIndex;
                            await RetryExecutionHelper.ExecuteFuncWithRetryAsync(action: async () =>
                                {
                                    CloudAppendBlob writeAccessUriBlob =
                                        new CloudAppendBlob(new Uri(writableSasUri));
                                    await
                                        writeAccessUriBlob.AppendFromByteArrayAsync(buffer: msgBytes,
                                            index: index,
                                            count: Math.Min(MaxBytesToAppend, (msgByteLength - index)),
                                            accessCondition: accessCondition, options: requestOptions,
                                            operationContext: new OperationContext());

                                    await Task.Delay(TimeSpan.FromSeconds(1));
                                    // Sleeping for a second to ensure we don't cross throttling limits (https://docs.microsoft.com/en-us/azure/storage/storage-scalability-targets)
                                },
                                shouldRetryException: ex =>
                                {
                                    if (ex.GetType() == typeof(StorageException) &&
                                        ((StorageException) ex).RequestInformation.HttpStatusCode == 412
                                    ) //The append position condition specified was not met
                                    {
                                        string redactedSasUri = RedactSasUri(writableSasUri);
                                        CommonLogger.LogEntityInfo("{redacted_sas_uri}", "Retry due to exception: {exception_message}, stack trace: {stack_trace}", redactedSasUri, ex.Message, ex.StackTrace);
                                        return true;
                                    }

                                    return false;
                                },
                                retryPolicy: new LinearRetryPolicy(retryIntervalinSec: TimeSpan.FromSeconds(5),
                                    maxAttempts: 5),
                                token: CancellationToken.None
                            );
                            startIndex += Math.Min(MaxBytesToAppend, (msgByteLength - index));
                        } while (startIndex < msgByteLength);
                    }
                }
            }
            catch (StorageException exc)
            {
                _counterManager.GetRateCounter("AzureSasUriClient AppendToWritableUriFailureRate").Increment();
                string redactedSasUri = RedactSasUri(writableSasUri);
                CommonLogger.LogEntityError("{redacted_sas_uri}",
                                            "Failed appending to Azure. Message: {truncate_message}, error: {exception_message}, stack trace: {stack_trace}",
                                            redactedSasUri, TruncateMessage(message), exc.Message, exc.StackTrace);
                throw new RelInfraStorageException("AppendToWritableUriAsync failed", exc, StorageErrorHelper.GetError(exc.RequestInformation.HttpStatusCode)).Format();
            }
        }

        public virtual async Task<Stream> GetStreamAsync(string readableUri)
        {
            if (string.IsNullOrEmpty(readableUri))
            {
                throw new ArgumentException("readableUri can not be null");
            }

            try
            {
                _counterManager.GetRateCounter("AzureSasUriClient GetStreamRate").Increment();
                using (_counterManager.GetLatencyCounter("AzureSasUriClient GetStreamLatency").StartCounter())
                {
                    CloudBlob readAccessUriBlob = new CloudBlob(new Uri(readableUri));
                    BlobRequestOptions requestOptions = new BlobRequestOptions
                    {
                        RetryPolicy = new ExponentialRetry(_intervalBetweenRetries, _numberOfRetries)
                    };

                    return await readAccessUriBlob.OpenReadAsync(options: requestOptions, accessCondition: null, operationContext: null);
                }                
            }
            catch (StorageException ex)
            {
                _counterManager.GetRateCounter("AzureSasUriClient GetStreamAsyncFailureRate").Increment();
                string redactedSasUri = RedactSasUri(readableUri);
                CommonLogger.LogEntityError("{redacted_sas_uri}",
                                            "Failed to get stream asynchronously.\n" +
                                            "HttpStatusCode: {http_status_code}: {error_code_helper}\n" +
                                            "Exception Message: {storage_ex_msg},\n" +
                                            "Stack Trace: {stack_trace}",
                                            redactedSasUri,
                                            ex.RequestInformation.HttpStatusCode, StorageErrorHelper.GetError(ex.RequestInformation.HttpStatusCode),
                                            ex.Message, 
                                            ex.StackTrace);
                throw new RelInfraStorageException("GetStreamAsync failed", ex, StorageErrorHelper.GetError(ex.RequestInformation.HttpStatusCode)).Format();
            }
        }
        
        private string TruncateMessage(string message)
        {
            return message?.Substring(0, Math.Min(512, message.Length));
        }

        private string RedactSasUri(string sasUri)
        {
            if (string.IsNullOrEmpty(sasUri))
            {
                return sasUri;
            }

            try
            {
                var uriBuilder = new UriBuilder(sasUri)
                {
                    Query = string.Empty
                };

                return uriBuilder.ToString();
            }
            catch (Exception)
            {
                return string.Empty;
            }
        }
    }
}
