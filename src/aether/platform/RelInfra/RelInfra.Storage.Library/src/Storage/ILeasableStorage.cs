﻿using System;
using System.Threading;
using System.Threading.Tasks;

namespace Microsoft.RelInfra.Storage
{
    /// <summary>
    ///  Storage capable to issue leases
    /// </summary>
    public interface ILeasableStorage
    {
        /// <summary>
        ///    Initialize storage
        /// </summary>
        /// <param name="blobName"></param>
        /// <returns></returns>
        Task InitializeBlobAsync(string blobName);

        /// <summary>
        ///    Delete storage
        /// </summary>
        /// <param name="blobName"></param>
        /// <returns></returns>
        Task DeleteAsync(string blobName);

        /// <summary>
        /// Acquires a lease
        /// 
        /// </summary>
        /// <param name="blobName"></param>
        /// <param name="leaseTime">A TimeSpan representing the span of time for which to acquire the lease,
        ///             which will be rounded down to seconds. If null, an infinite lease will be acquired. If not null, this must be
        ///             greater than zero.</param>
        /// <param name="proposedLeaseId">A string representing the proposed lease ID for the new lease, or null if no lease ID is proposed.</param>
        /// <returns>
        /// The ID of the acquired lease.
        /// </returns>
        Task<string> AcquireLeaseAsync(string blobName, TimeSpan? leaseTime, string proposedLeaseId = null, CancellationToken cancellationToken = default(CancellationToken));


        /// <summary>
        /// 
        /// </summary>
        /// <param name="blobName"></param>
        /// <param name="leaseId">Lease to renew</param>
        Task RenewLeaseAsync(string blobName, string leaseId, CancellationToken cancellationToken = default(CancellationToken));

        /// <summary>
        /// Releases the lease on this blob.
        /// 
        /// </summary>
        /// <param name="blobName"></param>
        /// <param name="leaseId">Lease to release</param>
        Task ReleaseLeaseAsync(string blobName, string leaseId, CancellationToken cancellationToken = default(CancellationToken));
    }
}
