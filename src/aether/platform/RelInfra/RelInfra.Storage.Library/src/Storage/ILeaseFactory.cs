﻿using System;
using System.Runtime.Serialization;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.RelInfra.Instrumentation.Exceptions;
using Microsoft.RelInfra.Storage.Exceptions;

namespace Microsoft.RelInfra.Storage
{
    [DataContract]
    public class LockableEntity
    {
        [DataMember]
        public DateTime Time { get; set; }
    }

    public abstract class LeaseToken<T> : IDisposable
    {
        protected Storable<T> Token { get; set; }
        public abstract void Release();

        /// <summary>
        ///  how long to hold the lease after token is disposed
        ///  </summary>
        public abstract int HoldAfterDisposeSec { get; set; }

        protected LeaseToken(Storable<T> token)
        {
            Token = token;
        }

        public abstract void Dispose();
    }

    public interface ILeaseFactory<T>
    {
        Task<LeaseToken<T>> AcquireLeaseAsync(string key, int timeoutSec = 60);
        Task<LeaseToken<T>> AcquireLeaseAsync(string key, T defaultEntity, int timeoutSec = 60);
    }

    internal class StorageLeaseToken<T> : LeaseToken<T> where T : LockableEntity, new()
    {
        private Task _leaseThread;
        private readonly CancellationTokenSource _cts;
        private readonly StorageLeaseFactory<T> _leaseFactory;
        private readonly int _timeoutSec;
        private bool _isDisposed;

        internal StorageLeaseToken(StorageLeaseFactory<T> leaseFactory, Storable<T> token,
            int timeoutSec) : base(token)
        {
            _cts = new CancellationTokenSource();
            _leaseFactory = leaseFactory;
            _timeoutSec = timeoutSec;
            _leaseThread = Task.Run(async () => await LeaseThread(_cts.Token));
            // collect exception
            _leaseThread.ContinueWith(t => { _cts.Dispose(); Exception ex = t.Exception; });
            HoldAfterDisposeSec = 0;
        }

        private async Task LeaseThread(CancellationToken cancellationToken)
        {
            do
            {
                try
                {
                    await Task.Delay(TimeSpan.FromSeconds(_timeoutSec/2), cancellationToken);
                }
                catch (TaskCanceledException)
                {
                    // just swallow exception so that it doesn't go to parent thread
                }

                Token =
                    await
                        _leaseFactory.RenewLeaseAsync(Token,
                            cancellationToken.IsCancellationRequested ? HoldAfterDisposeSec : _timeoutSec);
                // at this point we should never fail
                if (Token == null)
                {
                    throw new RelInfraStorageException("Can't renew the lease. token: {lease_token}").Format(Token);
                }
            } while (!cancellationToken.IsCancellationRequested);
        }

        public override void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        private void Dispose(bool disposing)
        {
            if (!_isDisposed && disposing)
            {
                Release();
                _isDisposed = true;
            }
        }

        public override void Release()
        {
            if (_leaseThread == null)
            {
                return;
            }

            _cts.Cancel();
            _leaseThread = null;
        }

        public override int HoldAfterDisposeSec { get; set; }
    }

    public class StorageLeaseFactory<T> : ILeaseFactory<T> where T : LockableEntity, new()
    {
        private readonly IStorage _storage;
        private readonly string _containterName;

        public StorageLeaseFactory(IStorage storage, string containterName)
        {
            _containterName = containterName;
            _storage = storage;
        }

        public async Task<LeaseToken<T>> AcquireLeaseAsync(string key, T defaultEntity, int timeoutSec = 60)
        {
            Storable<T> result = await _storage.TryGetAsync<T>(_containterName, key);
            if (result == null)
            {
                var newToken = new Storable<T>(defaultEntity)
                {
                    Id = key,
                    Entity = new T
                    {
                        Time = DateTime.UtcNow + TimeSpan.FromSeconds(timeoutSec)
                    }
                };

                try
                {
                    result = await _storage.AddAsync(_containterName, key, newToken);
                }
                catch (RelInfraStorageException)
                {
                }

                if (result != null)
                {
                    return new StorageLeaseToken<T>(this, result, timeoutSec);
                }
                
                result = await _storage.TryGetAsync<T>(_containterName, key);
                if (result == null)
                {
                    throw new RelInfraStorageException("Can't neither create nor get storage record").Format();
                }
            }

            if ((result.Entity.Time - DateTime.UtcNow).TotalSeconds > 0)
            {
                // it's in the future: someone is holding the lock
                return null;
            }

            result.Entity = defaultEntity;
            result = await RenewLeaseAsync(result, timeoutSec);
            return result == null ? null : new StorageLeaseToken<T>(this, result, timeoutSec);
       }

        public async Task<LeaseToken<T>> AcquireLeaseAsync(string key, int timeoutSec = 60)
        {
            // either way we don't want to preserve in table original value
            return await AcquireLeaseAsync(key, new T(), timeoutSec);
        }

        // returns storable object if we renewed lease, null otherwise
        internal async Task<Storable<T>> RenewLeaseAsync(Storable<T> token, int timeoutSec)
        {
            token.Entity.Time = DateTime.UtcNow + TimeSpan.FromSeconds(timeoutSec);

            try
            {
                var result = await _storage.UpdateAsync(_containterName, token.Id, token);
                return result;
            }
            catch (RelInfraStorageException)
            {
                return null;
            }
        }
    }
}
