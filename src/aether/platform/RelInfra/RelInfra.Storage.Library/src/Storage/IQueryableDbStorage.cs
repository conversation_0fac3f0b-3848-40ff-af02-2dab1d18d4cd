﻿using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace Microsoft.RelInfra.Storage
{
    public interface IQueryableDbStorage
    {
        /// <summary>
        /// Check whether the resource with given Id exists in the specified container of underlying storage.
        /// </summary>
        /// <param name="containerName">Container name to which the resource is added (can be PartitionKey for Azure tables)</param>
        /// <param name="resourceId">Id of the given resource (can be Row<PERSON><PERSON> for tables, or stream name for Cosmos storage)</param>
        /// <returns></returns>
        Task<bool> ExistsAsync(string containerName, string resourceId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Asynchronously add resource with given Id to the specified container in underlying storage
        /// </summary>
        /// <typeparam name="T">Type of object associated with resource</typeparam>
        /// <param name="containerName">Container name to which the resource is added (can be PartitionKey for Azure tables)</param>
        /// <param name="resourceId">Id of the given resource (can be Row<PERSON><PERSON> for tables, or stream name for Cosmos storage)</param>
        /// <param name="resource"></param>
        Task<Storable<T>> AddAsync<T>(string containerName, string resourceId, Storable<T> resource, CancellationToken cancellationToken = default);

        /// <summary>
        /// Asynchronously retrieves the resource with given Id from the specified container in underlying storage.
        /// Returns null if it fails to get item.
        /// </summary>
        /// <param name="containerName"></param>
        /// <param name="id"></param>
        /// <param name="cancellationToken"></param>
        Task<Storable<T>> GetAsync<T>(string containerName, string id, CancellationToken cancellationToken = default);

        /// <summary>
        /// Asynchronously retrieves the resource with given Id from the specified container in underlying storage.
        /// Returns null if it fails to get item.
        /// </summary>
        /// <param name="containerName"></param>
        /// <param name="resourceId"></param>
        /// <param name="cancellationToken"></param>
        Task<Storable<T>> TryGetAsync<T>(string containerName, string resourceId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Asynchronously delete resource with given Id in the specified container in underlying storage.
        /// </summary>
        /// <param name="containerName">Container name from which the resource is deleted (can be PartitionKey for Azure tables)</param>
        /// <param name="resourceId">Id of the given resource (can be RowKey for tables, or stream name for Cosmos storage)</param>
        /// <returns>true if deleted successfully, false otherwise</returns>
        Task<bool> TryDeleteAsync(string containerName, string resourceId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Asynchronously delete resource with given Id in the specified container in underlying storage.
        /// Throws RelInfraStorageException if it fails.
        /// </summary>
        /// <param name="containerName">Container name from which the resource is deleted (can be PartitionKey for Azure tables)</param>
        /// <param name="resourceId">Id of the given resource (can be RowKey for tables, or stream name for Cosmos storage)</param>
        Task DeleteAsync(string containerName, string resourceId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Asynchronously delete resource with given Id in the specified container in underlying storage.
        /// Throws RelInfraStorageException if it fails.
        /// </summary>
        /// <param name="containerName">Container name from which the resource is deleted (can be PartitionKey for Azure tables)</param>
        /// <param name="resourceId">Id of the given resource (can be RowKey for tables, or stream name for Cosmos storage)</param>
        /// <param name="etag"></param>
        Task DeleteAsync(string containerName, string resourceId, string etag, CancellationToken cancellationToken = default);

        /// <summary>
        /// Puts resource with given Id to the specified container in underlying storage
        /// </summary>
        /// <typeparam name="T">Type of object associated with resource</typeparam>
        /// <param name="containerName">Container name to which the resource is added (can be PartitionKey for Azure tables)</param>
        /// <param name="resourceId">Id of the given resource (can be RowKey for tables, or stream name for Cosmos storage)</param>
        /// <param name="resource"></param>
        Task<Storable<T>> PutAsync<T>(string containerName, string resourceId, Storable<T> resource, CancellationToken cancellation = default);

        /// <summary>
        /// Puts resource with given Id to the specified container in underlying storage
        /// </summary>
        /// <typeparam name="T">Type of object associated with resource</typeparam>
        /// <param name="containerName">Container name to which the resource is added (can be PartitionKey for Azure tables)</param>
        /// <param name="resourceId">Id of the given resource (can be RowKey for tables, or stream name for Cosmos storage)</param>
        /// <param name="resource"></param>
        /// <returns>true if added successfully, false otherwise</returns>
        Task<bool> TryPutAsync<T>(string containerName, string resourceId, Storable<T> resource, CancellationToken cancellationToken = default);

        Task<Storable<T>> UpdateAsync<T>(string containerName, string id, Storable<T> resource, CancellationToken cancellationToken = default);
        Task<bool> TryUpdateAsync<T>(string containerName, string id, Storable<T> resource, CancellationToken cancellationToken = default);
        Task<bool> TryBatchPutAsync<T>(string containerName, IEnumerable<Storable<T>> resources, CancellationToken cancellationToken = default);

        Task<IEnumerable<Storable<T>>> ExecuteQueryAsync<T>(string query, int? takeCount = null, CancellationToken cancellationToken = default);
        Task<QueryResults<T>> ExecuteQueryAsync<T>(string query, string continuationToken, int? takeCount = null, CancellationToken cancellationToken = default);
        Task<QueryResults<T>> ExecuteQueryAsync<T>(string partitionKey, string query, string continuationToken, int? takeCount = null, CancellationToken cancellationToken = default);
        Task<QueryResults<T>> GetPartitionWithCountinuationTokenAsync<T>(string partitionKey, string continuationToken, int? takeCount = null, CancellationToken cancellationToken = default);
        Task<QueryResults<T>> GetPartitionWithCountinuationTokenAsync<T>(string partitionKey, IEnumerable<string> columns, string continuationToken = null, int? takeCount = null, CancellationToken cancellationToken=default);
        Task<IEnumerable<Storable<T>>> GetPartitionAsync<T>(string partitionKey, int? takeCount = null, CancellationToken cancellationToken = default);
        Task<IEnumerable<Storable<T>>> GetAllAsync<T>(int? takeCount = null, CancellationToken cancellationToken = default);
        Task CreateIfNotExistsAsync();
        Task<QueryResults<T>> GetAllWithContinuationTokenAsync<T>(string continuationToken = null, int? takeCount = null, CancellationToken cancellationToken = default);
        Task<QueryResults<T>> ExecuteQueryNonStorableAsync<T>(string partitionKey, string query, string continuationToken, int? takeCount = null, List<KeyValuePair<string, string>> parameters = null, CancellationToken cancellationToken = default);

        Task<T> ExecuteStoredProcedureAsync<T>(string partitionKey, string storedProcedure, params dynamic[] procedureParams);
    }
}
