﻿using System;
using Microsoft.RelInfra.Instrumentation;
using Microsoft.WindowsAzure.Storage;

namespace Microsoft.RelInfra.Storage
{
    public class StorageFactory : IStorageFactory
    {
        private readonly CloudStorageAccount _cloudStorageAccount;
        private readonly CounterManager _counterManager;
        private readonly int _numberOfRetries;
        private readonly TimeSpan _intervalBetweenRetries;

        public static readonly int DefaultNumberOfRetries = 5;
        public static readonly TimeSpan DefaultIntervalBetweenRetries = TimeSpan.FromSeconds(2);

        public StorageFactory() :
            this(cloudStorageAccount: null, counterManager: null)
        {
        }

        public StorageFactory(string azureConnectionString, CounterManager counterManager = null)
            : this(CloudStorageAccount.Parse(azureConnectionString),
                   counterManager: counterManager)
        {
        }

        public StorageFactory(CloudStorageAccount cloudStorageAccount, CounterManager counterManager = null)
            : this(cloudStorageAccount: cloudStorageAccount,
                   counterManager: counterManager,
                   numberOfRetries: DefaultNumberOfRetries,
                   intervalBetweenRetries: DefaultIntervalBetweenRetries)
        {
        }

        protected StorageFactory(
            CloudStorageAccount cloudStorageAccount,
            CounterManager counterManager, 
            int numberOfRetries, 
            TimeSpan intervalBetweenRetries)
        {
            _cloudStorageAccount = cloudStorageAccount;
            _counterManager = counterManager;
            _numberOfRetries = numberOfRetries;
            _intervalBetweenRetries = intervalBetweenRetries;
        }        

        public IStorage GetStorage(StorageTypes storageType)
        {
            return GetStorage(storageType, String.Empty);
        }

        public IStorage GetStorage(StorageTypes storageType, string storageName)
        {
            return GetStorage(storageType: storageType, storageName: storageName, _cloudStorageAccount);
        }

        public IStorage GetStorage(StorageTypes storageType, string storageName, string azureConnectionString)
        {
            if (string.IsNullOrEmpty(azureConnectionString))
            {
                throw new ArgumentNullException(nameof(azureConnectionString));
            }

            return GetStorage(storageType, storageName, CloudStorageAccount.Parse(azureConnectionString));
        }

        public IStorage GetStorage(StorageTypes storageType, string storageName, CloudStorageAccount cloudStorageAccount)
        {
            if (cloudStorageAccount == null)
            {
                throw new ArgumentNullException(nameof(cloudStorageAccount));
            }

            switch (storageType)
            {
                case StorageTypes.AzureTables:
                    return new AzureTableStorage(cloudStorageAccount,
                                                 tableName: storageName,
                                                 counterManager: _counterManager);
                case StorageTypes.AzureBlob:
                    return new AzureBlobStorage(cloudStorageAccount,
                                                numRetries: _numberOfRetries,
                                                intervalBetweenRetries: _intervalBetweenRetries,
                                                counterManager: _counterManager);

                default:
                    throw new ArgumentOutOfRangeException(nameof(storageType));
            }
        }

        public IQueue<T> GetQueue<T>(string queueName)
        {
            return new AzureQueue<T>(_cloudStorageAccount, queueName);
        }
    }
}
