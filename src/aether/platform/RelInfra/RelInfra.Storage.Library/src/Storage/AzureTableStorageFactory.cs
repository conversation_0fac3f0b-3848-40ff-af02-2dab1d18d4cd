using Microsoft.RelInfra.Instrumentation;
using Microsoft.WindowsAzure.Storage;

namespace Microsoft.RelInfra.Storage
{
    public interface ITableStorageFactory
    {
        ITableStorage GetTable(string connectionString, string tableName, CounterManager counterManager);
    }

    public class AzureTableStorageFactory : ITableStorageFactory
    {
        public ITableStorage GetTable(string connectionString, string tableName, CounterManager counterManager)
        {
            return new AzureTableStorage(connectionString: connectionString, tableName: tableName, counterManager: counterManager)
            {
                RetryPolicy = new ServerBusyRetryPolicy()
            };
        }

        public ITableStorage GetTable(CloudStorageAccount storageAccount, string tableName, CounterManager counterManager)
        {
            return new AzureTableStorage(storageAccount: storageAccount, tableName: tableName, counterManager: counterManager)
            {
                RetryPolicy = new ServerBusyRetryPolicy()
            };
        }
    }
}