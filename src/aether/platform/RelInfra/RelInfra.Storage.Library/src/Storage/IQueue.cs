﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.RelInfra.Common.RetryPolicy;

namespace Microsoft.RelInfra.Storage
{
    [Flags]
    public enum MessageUpdateFlags
    {
        UpdateVisibility = 1,
        UpdateContent = 2,
    }

    public interface IQueue<T>
    {
        IRelInfraRetryPolicy RetryPolicy { get; set; }
        string QueueName { get; }

        /// <summary>
        /// Asynchronously retrieves message from queue
        /// </summary>
        /// <typeparam name="T">Payload associated with message</typeparam>
        /// <returns>An instance of <typeparamref name="T"/> or default(T) if there are no messages in a queue</returns>
        Task<Storable<T>> GetAsync();

        /// <summary>
        /// Asynchronously retrieves message from queue for specific lease time
        /// </summary>
        /// <typeparam name="T">Payload associated with message</typeparam>
        /// <param name="leaseTime">Specific lease time for the message, during this time the message is not visible in the queue</param>
        /// <returns>An instance of <typeparamref name="T"/> or default(T) if there are no messages in a queue</returns>
        Task<Storable<T>> GetAsync(TimeSpan leaseTime);

        /// <summary>
        /// Asynchronously retrieves given number of messages from queue for specific lease time
        /// </summary>
        /// <typeparam name="T">Payload associated with message</typeparam>
        /// <param name="messageCount">Number of messages to retrieve</param>
        /// <param name="leaseTime">Specific lease time for the message, during this time the message is not visible in the queue</param>
        /// <returns>An IEnumerable of Storable<typeparamref name="T"/> or empty IEnumerable if there are no messages in a queue</returns>
        Task<IEnumerable<Storable<T>>> GetMessagesAsync(int messageCount, TimeSpan leaseTime);

        /// <summary>
        /// Asynchronously adds message to queue with given name
        /// </summary>
        /// <typeparam name="T">Payload associated with message</typeparam>
        /// <param name="message">An instance of <typeparamref name="T"/></param>
        Task PutAsync(Storable<T> message);

        /// <summary>
        /// Asynchronously adds message to queue with given name, and hide from processing for visibilityDelay
        /// </summary>
        /// <typeparam name="T">Payload associated with message</typeparam>
        /// <param name="message">An instance of <typeparamref name="T"/></param>
        /// <param name="visibilityDelay">Message would be hidden from queue processing</param>
        Task PutAsync(Storable<T> message, TimeSpan visibilityDelay);

        /// <summary>
        /// Asynchronously adds message to queue with given name, and hide from processing for visibilityDelay
        /// </summary>
        /// <typeparam name="T">Payload associated with message</typeparam>
        /// <param name="message">An instance of <typeparamref name="T"/></param>
        /// <param name="visibilityDelay">Message would be hidden from queue processing</param>
        /// <param name="timeToLive">Message will be removed after specified time</param>
        Task PutAsync(Storable<T> message, TimeSpan? visibilityDelay, TimeSpan? timeToLive);

        /// <summary>
        /// Asynchronously updates message in a queue
        /// </summary>
        /// <typeparam name="T">Payload associated with message</typeparam>
        /// <param name="message">An instance of <typeparamref name="T"/></param>
        /// <param name="leaseTime">new lease time</param>
        /// <param name="flags">indicates whether to update the lease time, message contents, or both</param>
        Task<Storable<T>> UpdateMessageAsync(Storable<T> message, TimeSpan leaseTime, MessageUpdateFlags flags);

        /// <summary>
        /// Asynchronously deletes message to queue with given name
        /// </summary>
        /// <param name="messageId">Message Id</param>
        /// <param name="etag">To check that message lease hasn't been expired</param>
        Task<bool> DeleteAsync(string messageId, string etag);

        /// <summary>
        /// Asynchronously deletes message to queue with given name
        /// </summary>
        /// /// <typeparam name="T">Payload associated with message</typeparam>
        /// <param name="message">An instance of <typeparamref name="T"/></param>
        Task<bool> DeleteAsync(Storable<T> message);

        /// <summary>
        /// Returns dequeue count for a message
        /// </summary>
        /// <typeparam name="T">Payload associated with message</typeparam>
        /// <param name="message">An instance of <typeparamref name="T"/></param>
        /// <returns>Returns dequeue count for a message</returns>
        int GetDequeueCount(Storable<T> message);

        /// <summary>
        /// Return life time of message
        /// </summary>
        /// <typeparam name="T">Payload associated with message</typeparam>
        /// <param name="message">An instance of <typeparamref name="T"/></param>
        /// <returns>Return life time of message</returns>
        TimeSpan GetMessageLifetime(Storable<T> message);

        /// <summary>
        /// Returns an approximation of the amount of messages currently in the queue.
        /// Will return 0 if the queue hasn't been initialized.
        /// </summary>
        /// <returns>An estimation of the amount of messages in the queue.</returns>
        Task<int> GetQueueLengthAsync();

        /// <summary>
        /// Initialize Queue with given name
        /// </summary>
        Task InitializeQueueAsync();

        /// <summary>
        /// List queues in account with prefix in name
        /// </summary>
        /// <param name="prefix"></param>
        /// <returns></returns>
        Task<IEnumerable<string>> ListAllQueueNamesAsync(string prefix);
    }
}
