﻿using Azure;
using Azure.Core;
using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Models;
using Azure.Storage.Blobs.Specialized;
using Azure.Storage.Sas;
using Microsoft.MachineLearning.Common.Core.Extensions;
using Microsoft.RelInfra.Common.RetryPolicy;
using Microsoft.RelInfra.Common.Serialization;
using Microsoft.RelInfra.Extensions;
using Microsoft.RelInfra.Instrumentation;
using Microsoft.RelInfra.Instrumentation.Exceptions;
using Microsoft.RelInfra.Instrumentation.Logging;
using Microsoft.RelInfra.Storage.Exceptions;
using Microsoft.RelInfra.Storage.Utils;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Microsoft.RelInfra.Storage
{
    public class AzureBlobStorageV2 : IRelInfraStreamableStorage, IAzureBlobStorage
    {
        public const int PageBlobPageSize = 512;
        public const int FourMbytes = 4 * 1024 * 1024;
        private readonly BlobServiceClient _blobServiceClient;
        private readonly BlobClientOptions _clientOptions;
        private readonly OptionalCounters _counters;
        private const int DefaultBlobTimeoutInSeconds = 10;

        public IRelInfraRetryPolicy RetryPolicy { get; set; }
        public string ContainerName { get; private set; }

        public AzureBlobStorageV2(string connectionString, int numRetries, TimeSpan intervalBetweenRetries)
            : this(connectionString: connectionString, numRetries: numRetries, intervalBetweenRetries: intervalBetweenRetries, counterManager: null)
        {
        }

        public AzureBlobStorageV2(string connectionString, int numRetries, TimeSpan intervalBetweenRetries, CounterManager counterManager)
        {
            _clientOptions = new BlobClientOptions();
            _clientOptions.Retry.MaxRetries = numRetries;
            _clientOptions.Retry.Delay = intervalBetweenRetries;
            _clientOptions.Retry.Mode = RetryMode.Fixed;

            _blobServiceClient = new BlobServiceClient(connectionString, _clientOptions);

            _counters = new OptionalCounters(counterManager, _blobServiceClient.AccountName);

            CommonLogger.LogEntityInfo(nameof(AzureBlobStorageV2), $"Construct client with connection string. AccountName: [{_blobServiceClient.AccountName}].");
        }

        public AzureBlobStorageV2(Uri serviceUri, TokenCredential credential, int numRetries, TimeSpan intervalBetweenRetries, CounterManager counterManager)
        {
            _clientOptions = new BlobClientOptions();
            _clientOptions.Retry.MaxRetries = numRetries;
            _clientOptions.Retry.Delay = intervalBetweenRetries;
            _clientOptions.Retry.Mode = RetryMode.Fixed;

            _blobServiceClient = new BlobServiceClient(serviceUri, credential, _clientOptions);

            _counters = new OptionalCounters(counterManager, _blobServiceClient.AccountName);
        }

        public async Task InitializeAsync(string containerName)
        {
            _counters.IncrementRateCounter("AzureBlob InitializeContainerRate");
            using (_counters.CreateDisposableTimer("AzureBlob InitializeContainerLatency"))
            {
                try
                {

                    if (string.IsNullOrWhiteSpace(containerName))
                    {
                        throw new ArgumentException("containerName can't be null or empty", nameof(containerName));
                    }

                    var container = _blobServiceClient.GetBlobContainerClient(containerName);
                    await container.CreateIfNotExistsAsync();
                    ContainerName = containerName;
                }
                catch
                {
                    _counters.IncrementRateCounter("AzureBlob InitializeContainerFailureRate");
                    throw;
                }
            }
        }

        public void InitializeForAlreadyExistingContainer(string containerName)
        {
            ContainerName = containerName;
        }

        public Uri GetBlobUri(string containerName, string blobName, DateTimeOffset? expiryDate = null)
        {
            if (string.IsNullOrWhiteSpace(containerName))
            {
                throw new ArgumentException("containerName can't be null or empty", nameof(containerName));
            }

            if (string.IsNullOrWhiteSpace(blobName))
            {
                throw new ArgumentException("blobName can't be null or empty", nameof(blobName));
            }

            var container = _blobServiceClient.GetBlobContainerClient(containerName);
            var blob = container.GetBlobClient(blobName);

            Uri blobUri;
            if (expiryDate != null)
            {
                blobUri = GenerateBlobSasUri(blob, (DateTimeOffset)expiryDate, BlobSasPermissions.Read);
            }
            else
            {
                blobUri = blob.Uri;
            }

            return blobUri;
        }

        public Uri GetContainerReadUri(string containerName, DateTimeOffset? expiryDate = null)
        {
            if (string.IsNullOrWhiteSpace(containerName))
            {
                throw new ArgumentException("containerName can't be null or empty", nameof(containerName));
            }

            var container = _blobServiceClient.GetBlobContainerClient(containerName);

            Uri containerReadUri;
            if (expiryDate != null)
            {
                containerReadUri = GenerateContainerSasUri(
                    container: container,
                    expiryDate: (DateTimeOffset)expiryDate,
                    permissions: BlobContainerSasPermissions.Read | BlobContainerSasPermissions.List);
            }
            else
            {
                containerReadUri = container.Uri;
            }

            return containerReadUri;
        }

        public Uri GetContainerWritableUri(string containerName, DateTimeOffset? expiryDate = null)
        {
            if (string.IsNullOrWhiteSpace(containerName))
            {
                throw new ArgumentException("containerName can't be null or empty", nameof(containerName));
            }

            var container = _blobServiceClient.GetBlobContainerClient(containerName);

            Uri containerWriteUri;
            if (expiryDate != null)
            {
                containerWriteUri = GenerateContainerSasUri(
                    container: container,
                    expiryDate: (DateTimeOffset)expiryDate,
                    permissions: BlobContainerSasPermissions.Read | BlobContainerSasPermissions.List | BlobContainerSasPermissions.Write);
            }
            else
            {
                containerWriteUri = container.Uri;
            }

            return containerWriteUri;
        }

        public Uri GetWritableBlobUri(string containerName, string blobName, DateTimeOffset expiryDate)
        {
            if (string.IsNullOrWhiteSpace(containerName))
            {
                throw new ArgumentException("containerName can't be null or empty", nameof(containerName));
            }

            if (string.IsNullOrWhiteSpace(blobName))
            {
                throw new ArgumentException("blobName can't be null or empty", nameof(blobName));
            }

            var container = _blobServiceClient.GetBlobContainerClient(containerName);
            var blob = container.GetBlobClient(blobName);

            var blobUri = GenerateBlobSasUri(blob, expiryDate, BlobSasPermissions.Read | BlobSasPermissions.Write);
            return blobUri;
        }

        public async Task<bool> ExistsAsync(string containerName, string resourceId)
        {
            return await ExistsAsync(containerName, resourceId, CancellationToken.None);
        }

        public async Task<bool> ExistsAsync(string containerName, string resourceId, CancellationToken token)
        {
            _counters.IncrementRateCounter("AzureBlob ExistsRate");
            using (_counters.CreateDisposableTimer("AzureBlob ExistsLatency"))
            {
                try
                {
                    var container = _blobServiceClient.GetBlobContainerClient(containerName);
                    var blob = container.GetBlobClient(resourceId);
                    return await blob.ExistsAsync(token);
                }
                catch (Exception ex) //aggregate exception?
                {
                    string log =
                        $"Failed to check entity existance with id {resourceId} to blob in container {containerName}";
                    Trace.TraceError("{0}, exception: {1}", log, ex);
                    _counters.IncrementRateCounter("AzureBlob ExistsFailureRate");
                    if (ex is RequestFailedException storageEx)
                    {
                        throw new RelInfraStorageException(log, storageEx, StorageErrorHelper.GetError(storageEx.Status)).Format();
                    }
                    else if (ex is TaskCanceledException)
                    {
                        throw new TimeoutException("cancellation timeout and retry limit reached", ex);
                    }
                    else
                    {
                        throw;
                    }
                }
            }
        }

        public async Task<bool> CanAccessAsync()
        {
            return await CanAccessAsync(CancellationToken.None);
        }


        public async Task<bool> CanAccessAsync(CancellationToken token)
        {
            try
            {
                await ExistsAsync("test", "test", token);
            }
            catch (Exception ex) when (ex is RelInfraStorageException || ex is TimeoutException)
            {
                return false;
            }

            return true;
        }

        public async Task<Storable<T>> AddAsync<T>(string containerName, string resourceId, Storable<T> resource)
        {
            return await AddAsync(containerName, resourceId, resource, CancellationToken.None);
        }

        private async Task<Storable<T>> AddAsync<T>(string containerName, string resourceId, Storable<T> resource, CancellationToken cancellationToken)
        {
            _counters.IncrementRateCounter("AzureBlob AddRate");
            using (_counters.CreateDisposableTimer("AzureBlob AddLatency"))
            {
                try
                {
                    var container = _blobServiceClient.GetBlobContainerClient(containerName);
                    var blob = container.GetBlobClient(resourceId);
                    using var ms = new MemoryStream(Encoding.UTF8.GetBytes(SerializationHelpers.SerializeEntity(resource.Entity)));
                    await blob.UploadAsync(ms, false, cancellationToken);

                    resource.Id = resourceId;
                    resource.Etag = blob.GetProperties(cancellationToken: cancellationToken).Value.ETag.ToString();

                    return resource;
                }
                catch (RequestFailedException ex)
                {
                    string log = $"Failed to upload entity with id {resourceId} to blob in container {containerName}";
                    Trace.TraceError("{0}, exception: {1}", log, ex);
                    _counters.IncrementRateCounter("AzureBlob AddFailureRate");

                    throw new RelInfraStorageException(log, ex, StorageErrorHelper.GetError(ex.Status)).Format();
                }
            }
        }

        /// <summary>
        /// creates a page blob, throws if it already exists. actual size created for the blob
        /// will be rounded up to the nearest modulo of 512.
        /// </summary>
        /// <param name="containerName"></param>
        /// <param name="resourceId"></param>
        /// <param name="size"></param>
        /// <returns></returns>
        public async Task AddPageBlobAsync(string containerName, string resourceId, long size)
        {
            _counters.IncrementRateCounter("AzureBlob AddPageBlobRate");
            using (_counters.CreateDisposableTimer("AzureBlob AddPageBlobLatency"))
            {
                try
                {
                    var container = _blobServiceClient.GetBlobContainerClient(containerName);
                    var pageBlobClient = container.GetPageBlobClient(resourceId);

                    await pageBlobClient.CreateIfNotExistsAsync(RoundUpToPageBlobSize(size));
                }
                catch (RequestFailedException ex)
                {
                    string log = $"Failed to create page blob with id {resourceId} to blob in container {containerName}";
                    Trace.TraceError("{0}, exception: {1}", log, ex);
                    _counters.IncrementRateCounter("AzureBlob AddPageBlobFailureRate");

                    throw new RelInfraStorageException(log, ex, StorageErrorHelper.GetError(ex.Status)).Format();
                }
            }
        }

        public async Task ResizePageBlobAsync(string containerName, string resourceId, long size)
        {
            _counters.IncrementRateCounter("AzureBlob ResizePageBlobRate");
            using (_counters.CreateDisposableTimer("AzureBlob ResizePageBlobLatency"))
            {
                try
                {
                    var container = _blobServiceClient.GetBlobContainerClient(containerName);
                    var pageBlobClient = container.GetPageBlobClient(resourceId);

                    await pageBlobClient.ResizeAsync(RoundUpToPageBlobSize(size));
                }
                catch (RequestFailedException ex)
                {
                    string log = $"Failed to resize page blob with id {resourceId} to blob in container {containerName}";
                    Trace.TraceError("{0}, exception: {1}", log, ex);
                    _counters.IncrementRateCounter("AzureBlob ResizePageBlobFailureRate");

                    throw new RelInfraStorageException(log, ex, StorageErrorHelper.GetError(ex.Status)).Format();
                }
            }
        }

        /// <summary>
        /// upload the entire buffer to the blob, starting position for the blob is blobOffset
        /// buffer size must be modulo of 512
        /// </summary>
        /// <param name="containerName"></param>
        /// <param name="resourceId"></param>
        /// <param name="buffer"></param>
        /// <param name="blobOffset"></param>
        /// <param name="bufferOffset"></param>
        /// <param name="length"></param>
        /// <returns></returns>
        public async Task PutPageBlobPageAsync(string containerName, string resourceId, byte[] buffer, long blobOffset, int bufferOffset, int length)
        {
            if (length % PageBlobPageSize > 0)
            {
                throw new ArgumentException("putpage buffer size must be modulo of PageBlobPageSize(512 bytes)");
            }

            _counters.IncrementRateCounter("AzureBlob PutPageRate");
            using (_counters.CreateDisposableTimer("AzureBlob PutPageLatency"))
            {
                try
                {
                    var container = _blobServiceClient.GetBlobContainerClient(containerName);
                    var pageBlobClient = container.GetPageBlobClient(resourceId);

                    int bufferEnd = bufferOffset + length;
                    while (bufferOffset < bufferEnd)
                    {
                        int sizeToTransfer = Math.Min(FourMbytes, bufferEnd - bufferOffset);
                        using MemoryStream memoryStream = new MemoryStream(buffer,
                            index: bufferOffset, count: sizeToTransfer, writable: false, publiclyVisible: false);
                        await pageBlobClient.UploadPagesAsync(memoryStream, blobOffset);
                        blobOffset += sizeToTransfer;
                        bufferOffset += sizeToTransfer;
                    }
                }
                catch (RequestFailedException ex)
                {
                    string log = $"Failed to put page blob with id {resourceId} to blob in container {containerName}, offset {blobOffset}";
                    Trace.TraceError("{0}, exception: {1}", log, ex);
                    _counters.IncrementRateCounter("AzureBlob AddFailureRate");

                    throw new RelInfraStorageException(log, ex, StorageErrorHelper.GetError(ex.Status)).Format();
                }
            }
        }

        // page blob size must be modulo of 512
        public static long RoundUpToPageBlobSize(long size)
        {
            return (size + PageBlobPageSize - 1) & ~(PageBlobPageSize - 1);
        }

        public async Task<Storable<string>> AppendAsync<T>(string containerName, string resourceId, T resource)
        {
            string resourceAsString = resource.ToString();
            using var resourceAsStream = new MemoryStream(Encoding.UTF8.GetBytes(resourceAsString));
            var eTag = await AppendStreamInternalAsync(containerName, resourceId, resourceAsStream);
            return new Storable<string>
            {
                Id = resourceId,
                ContainerId = containerName,
                Etag = eTag,
                Entity = resourceAsString
            };
        }

        public virtual async Task AppendStreamAsync(string containerName, string resourceId, Stream dataStream)
        {
            await AppendStreamInternalAsync(containerName, resourceId, dataStream);
        }

        private async Task<string> AppendStreamInternalAsync(string containerName, string resourceId, Stream dataStream)
        {
            _counters.IncrementRateCounter("AzureBlob AppendRate");
            using (_counters.CreateDisposableTimer("AzureBlob AppendLatency"))
            {
                try
                {
                    var container = _blobServiceClient.GetBlobContainerClient(containerName);
                    var appendBlobClient = container.GetAppendBlobClient(resourceId);

                    // Create the append blob if it does not exist.
                    using (var cancellationTokenSource = new CancellationTokenSource(TimeSpan.FromSeconds(DefaultBlobTimeoutInSeconds)))
                    {
                        await appendBlobClient.CreateIfNotExistsAsync(cancellationToken: cancellationTokenSource.Token);
                    }

                    // AppendBlockAsync doesn't like empty stream so we need to read off chunks and Upload
                    const int oneMb = 1024 * 1024;
                    byte[] buffer = new byte[oneMb];
                    int count;
                    do
                    {
                        using (var cancellationTokenSource = new CancellationTokenSource(TimeSpan.FromSeconds(DefaultBlobTimeoutInSeconds)))
                        {
                            count = await dataStream.ReadAsync(buffer: buffer, offset: 0, count: oneMb, cancellationTokenSource.Token);
                        }

                        if (count > 0)
                        {
                            using MemoryStream ms = new MemoryStream();
                            await ms.WriteAsync(buffer: buffer, offset: 0, count: count);
                            ms.Position = 0;
                            await appendBlobClient.AppendBlockAsync(ms);
                        }
                    } while (dataStream.CanRead && count > 0);

                    return appendBlobClient.GetProperties().Value.ETag.ToString();
                }
                catch (RequestFailedException ex)
                {
                    string log = $"Failed to append entity with id {resourceId} to blob in container {containerName}";
                    Trace.TraceError("{0}, exception: {1}", log, ex);
                    _counters.IncrementRateCounter("AzureBlob AppendFailureRate");

                    throw new RelInfraStorageException(log, ex, StorageErrorHelper.GetError(ex.Status)).Format();
                }
            }
        }

        /// <summary>
        ///     DO NOT USE, IT DOESNT CHECK ETAG. WILL BE DEPRECATED
        ///     Use PutWithEtagSync();
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="containerName"></param>
        /// <param name="resourceId"></param>
        /// <param name="resource"></param>
        /// <returns></returns>
        public async Task<bool> TryPutAndIgnoreEtagAsync<T>(string containerName, string resourceId, Storable<T> resource)
        {
            try
            {
                await PutAndIgnoreEtagAsync(containerName, resourceId, resource);
            }
            catch (RelInfraStorageException)
            {
                return false;
            }
            return true;
        }

        /// <summary>
        ///     DO NOT USE, IT DOESNT CHECK ETAG. WILL BE DEPRECATED
        ///     Use PutWithEtagAsync();
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="containerName"></param>
        /// <param name="resourceId"></param>
        /// <param name="resource"></param>
        /// <returns></returns>
        public async Task<Storable<T>> PutAndIgnoreEtagAsync<T>(string containerName, string resourceId, Storable<T> resource)
        {
            using var cancellationTokenSource = new CancellationTokenSource(TimeSpan.FromSeconds(DefaultBlobTimeoutInSeconds));
            return await PutAndIgnoreEtagAsync(containerName, resourceId, resource, cancellationTokenSource.Token);
        }

        public async Task<Storable<T>> PutAndIgnoreEtagAsync<T>(string containerName, string resourceId, Storable<T> resource, CancellationToken cancellationToken)
        {
            _counters.IncrementRateCounter("AzureBlob PutRate");
            using (_counters.CreateDisposableTimer("AzureBlob PutLatency"))
            {
                try
                {
                    var container = _blobServiceClient.GetBlobContainerClient(containerName);
                    var blob = container.GetBlobClient(resourceId);
                    using var ms = new MemoryStream(Encoding.UTF8.GetBytes(SerializationHelpers.SerializeEntity(resource.Entity)));
                    await blob.UploadAsync(ms, true, cancellationToken);
                    resource.Id = resourceId;
                    resource.Etag = blob.GetProperties(cancellationToken: cancellationToken).Value.ETag.ToString();

                    return resource;
                }
                catch (RequestFailedException ex)
                {
                    string log = $"Failed to upload entity with id {resourceId} to blob in container {containerName}";
                    Trace.TraceError("{0}, exception: {1}", log, ex);
                    _counters.IncrementRateCounter("AzureBlob PutFailureRate");

                    throw new RelInfraStorageException(log, ex, StorageErrorHelper.GetError(ex.Status)).Format();
                }
            }
        }

        public async Task<Storable<T>> PutWithETagAsync<T>(string containerName, string resourceId, Storable<T> resource)
        {
            _counters.IncrementRateCounter("AzureBlob PutWithETagRate");
            using (_counters.CreateDisposableTimer("AzureBlob PutWithETagLatency"))
            {
                try
                {
                    var container = _blobServiceClient.GetBlobContainerClient(containerName);
                    var blob = container.GetBlobClient(resourceId);
                    var buffer = Encoding.UTF8.GetBytes(SerializationHelpers.SerializeEntity(resource.Entity));

                    var options = new BlobUploadOptions()
                    {
                        Conditions = new BlobRequestConditions()
                        {
                            IfMatch = new ETag(resource.Etag)
                        }
                    };

                    using (var cancellationTokenSource = new CancellationTokenSource(TimeSpan.FromSeconds(DefaultBlobTimeoutInSeconds)))
                    {
                        await blob.UploadAsync(new BinaryData(buffer), options, cancellationTokenSource.Token);
                    }

                    resource.Id = resourceId;
                    resource.Etag = blob.GetProperties().Value.ETag.ToString();
                    return resource;
                }
                catch (RequestFailedException ex)
                {
                    string log = $"Failed to upload entity with id {resourceId} to blob in container {containerName}";
                    Trace.TraceError("{0}, exception: {1}", log, ex);
                    _counters.IncrementRateCounter("AzureBlob PutWithETagFailureRate");

                    throw new RelInfraStorageException(log, ex, StorageErrorHelper.GetError(ex.Status)).Format();
                }
            }
        }

        public Task<bool> TryBatchPutAsync<T>(string containerName, IEnumerable<Storable<T>> resources)
        {
            throw new NotImplementedException();
        }

        public Task<IEnumerable<Storable<T>>> BatchPutAsync<T>(string containerName, IEnumerable<Storable<T>> resources)
        {
            throw new NotImplementedException();
        }

        /// <summary>
        /// write a block inside a block blob. blockId need to be base64 encoded
        /// all blockIds for the same blob must have equal length
        /// </summary>
        /// <param name="containerName"></param>
        /// <param name="resourceId"></param>
        /// <param name="blockId"></param>
        /// <param name="blockData"></param>
        /// <returns></returns>
        public async Task PutBlockAsync(string containerName, string resourceId, string blockId, Stream blockData)
        {
            _counters.IncrementRateCounter("AzureBlob PutBlockRate");
            using (_counters.CreateDisposableTimer("AzureBlob PutBlockLatency"))
            {
                try
                {
                    var container = _blobServiceClient.GetBlobContainerClient(containerName);
                    var blockBlobClient = container.GetBlockBlobClient(resourceId);
                    await blockBlobClient.StageBlockAsync(blockId, blockData);
                }
                catch (RequestFailedException ex)
                {
                    string log = $"Failed to put block with id {resourceId} blockId {blockId} to blob in container {containerName}";
                    Trace.TraceError("{0}, exception: {1}", log, ex);
                    _counters.IncrementRateCounter("AzureBlob PutBlockFailureRate");

                    throw new RelInfraStorageException(log, ex, StorageErrorHelper.GetError(ex.Status)).Format();
                }
            }
        }

        /// <summary>
        /// commits list of blockIds for a block blob
        /// </summary>
        /// <param name="containerName"></param>
        /// <param name="resourceId"></param>
        /// <param name="blockList"></param>
        /// <param name="etag"></param>
        /// <returns></returns>
        public async Task PutBlockListAsync(string containerName, string resourceId, IEnumerable<string> blockList, string etag)
        {
            _counters.IncrementRateCounter("AzureBlob PutBlockListRate");
            using (_counters.CreateDisposableTimer("AzureBlob PutBlockListLatency"))
            {
                try
                {
                    var container = _blobServiceClient.GetBlobContainerClient(containerName);
                    var blockBlobClient = container.GetBlockBlobClient(resourceId);

                    BlobRequestConditions conditions = etag == "*"
                        ? null
                        : new BlobRequestConditions() { IfMatch = new ETag(etag) };
                    var options = new CommitBlockListOptions() { Conditions = conditions };

                    await blockBlobClient.CommitBlockListAsync(blockList, options);
                }
                catch (RequestFailedException ex)
                {
                    string log = $"Failed to putblocklist with id {resourceId} to blob in container {containerName}";
                    Trace.TraceError("{0}, exception: {1}", log, ex);
                    _counters.IncrementRateCounter("AzureBlob PutBlockListFailureRate");

                    throw new RelInfraStorageException(log, ex, StorageErrorHelper.GetError(ex.Status)).Format();
                }
            }
        }

        /// <summary>
        ///  get the list of blocks in Azure block blob
        /// </summary>
        /// <param name="containerName"></param>
        /// <param name="resourceId"></param>
        /// <param name="committedOnly"></param>
        /// <returns></returns>
        public async Task<IEnumerable<string>> GetBlockListAsync(string containerName, string resourceId, bool committedOnly)
        {
            _counters.IncrementRateCounter("AzureBlob GetBlockListRate");
            using (_counters.CreateDisposableTimer("AzureBlob GetBlockListLatency"))
            {
                try
                {
                    var container = _blobServiceClient.GetBlobContainerClient(containerName);
                    var blockBlobClient = container.GetBlockBlobClient(resourceId);

                    BlockList blockList = await blockBlobClient.GetBlockListAsync(committedOnly ? BlockListTypes.Committed : BlockListTypes.All);
                    var committed = blockList.CommittedBlocks?.Select(b => b.Name) ?? Enumerable.Empty<string>();
                    var uncommitted = blockList.UncommittedBlocks?.Select(b => b.Name) ?? Enumerable.Empty<string>();
                    return committed.Concat(uncommitted);
                }
                catch (RequestFailedException ex)
                {
                    string log = $"Failed to getblocklist with id {resourceId} to blob in container {containerName}";
                    Trace.TraceError("{0}, exception: {1}", log, ex);
                    _counters.IncrementRateCounter("AzureBlob GetBlockListFailureRate");

                    throw new RelInfraStorageException(log, ex, StorageErrorHelper.GetError(ex.Status)).Format();
                }
            }
        }

        /// <summary>
        /// read blob from blobOffset, applies to all types of blobs. 
        /// </summary>
        /// <param name="containerName"></param>
        /// <param name="resourceId"></param>
        /// <param name="blobOffset"></param>
        /// <param name="length"></param>
        /// <returns></returns>
        public async Task<byte[]> ReadBlobRangeAsync(string containerName, string resourceId, long blobOffset, int length)
        {
            _counters.IncrementRateCounter("AzureBlob ReadRangeRate");
            using (_counters.CreateDisposableTimer("AzureBlob ReadRangeLatency"))
            {
                try
                {
                    var container = _blobServiceClient.GetBlobContainerClient(containerName);
                    var blob = container.GetBlobClient(resourceId);

                    BlobProperties properties = await blob.GetPropertiesAsync();
                    length = (int)Math.Min(length, properties.ContentLength - blobOffset);

                    using BlobDownloadStreamingResult downloadResult = await blob.DownloadStreamingAsync(
                        new BlobDownloadOptions { Range = new HttpRange(blobOffset, length) });
                    using MemoryStream memoryStream = new MemoryStream();
                    downloadResult.Content.CopyTo(memoryStream);
                    return memoryStream.ToArray();
                }
                catch (RequestFailedException ex)
                {
                    string log = $"Failed to read page blob with id {resourceId} to blob in container {containerName}, offset {blobOffset}";
                    Trace.TraceError("{0}, exception: {1}", log, ex);
                    _counters.IncrementRateCounter("AzureBlob ReadRangeFailureRate");

                    throw new RelInfraStorageException(log, ex, StorageErrorHelper.GetError(ex.Status)).Format();
                }
            }
        }

        public async Task<Storable<T>> TryGetAsync<T>(string containerName, string resourceId)
        {
            return await TryGetAsync<T>(containerName, resourceId, cancellationToken: CancellationToken.None);
        }

        public async Task<Storable<T>> TryGetAsync<T>(string containerName, string resourceId, CancellationToken cancellationToken)
        {
            try
            {
                return await GetAsync<T>(containerName, resourceId, cancellationToken);
            }
            catch (RelInfraStorageException)
            {
                return null;
            }
        }

        public async Task<Storable<T>> GetAsync<T>(string containerName, string resourceId)
        {
            using var cancellationTokenSource = new CancellationTokenSource(TimeSpan.FromSeconds(DefaultBlobTimeoutInSeconds));
            return await GetAsync<T>(containerName, resourceId, cancellationToken: cancellationTokenSource.Token);
        }

        public async Task<Storable<T>> GetAsync<T>(string containerName, string resourceId, CancellationToken cancellationToken)
        {
            _counters.IncrementRateCounter("AzureBlob GetRate");
            using (_counters.CreateDisposableTimer("AzureBlob GetLatency"))
            {
                try
                {
                    // By default we assume block blob is requested
                    return await GetBlobEntityAsync<T>(containerName, resourceId, cancellationToken);
                }
                catch (Exception)
                {
                    _counters.IncrementRateCounter("AzureBlob GetFailureRate");
                    throw;
                }
            }
        }

        public async Task<Storable<T>> GetBlobEntityAsync<T>(string containerName, string resourceId)
        {
            using var cancellationTokenSource = new CancellationTokenSource(TimeSpan.FromSeconds(DefaultBlobTimeoutInSeconds));
            return await GetBlobEntityAsync<T>(containerName, resourceId, cancellationTokenSource.Token);
        }

        public async Task<Storable<T>> GetBlobEntityAsync<T>(string containerName, string resourceId, CancellationToken cancellationToken)
        {
            Storable<string> blobStringContent = await GetBlobStringAsync(containerName: containerName, resourceId: resourceId, cancellationToken: cancellationToken);
            return new Storable<T>
            {
                Id = blobStringContent.Id,
                ContainerId = blobStringContent.ContainerId,
                Etag = blobStringContent.Etag,
                Entity = SerializationHelpers.DeserializeEntity<T>(blobStringContent.Entity)
            };
        }

        public async Task<Storable<string>> GetBlobStringAsync(string containerName, string resourceId)
        {
            return await GetBlobStringAsync(containerName, resourceId, CancellationToken.None);
        }

        public async Task<Storable<string>> GetBlobStringAsync(string containerName, string resourceId, CancellationToken cancellationToken)
        {
            _counters.IncrementRateCounter("AzureBlob GetBlobStringRate");
            using (_counters.CreateDisposableTimer("AzureBlob GetBlobStringLatency"))
            {
                var container = _blobServiceClient.GetBlobContainerClient(containerName);
                var blob = container.GetBlobClient(resourceId);

                try
                {
                    using MemoryStream ms = new MemoryStream();

                    // Download the blob data to the memory stream
                    await blob.DownloadToAsync(ms, cancellationToken);

                    // Get the blob properties, such as ETag
                    BlobProperties properties = await blob.GetPropertiesAsync(cancellationToken: cancellationToken);

                    return new Storable<string>
                    {
                        Id = resourceId,
                        ContainerId = containerName,
                        Etag = properties.ETag.ToString(),
                        Entity = Encoding.UTF8.GetString(ms.ToArray())
                    };
                }
                catch (RequestFailedException ex)
                {
                    string log = $"Failed to get entity with id {resourceId} from blob in container {containerName} with request ID: "; //{ex.RequestInformation.ServiceRequestID}.";
                    Trace.TraceError("{0}, exception: {1}", log, ex);
                    _counters.IncrementRateCounter("AzureBlob GetBlobStringFailureRate");

                    throw new RelInfraStorageException(log, ex, StorageErrorHelper.GetError(ex.Status)).Format();
                }
            }
        }

        public async Task<IEnumerable<Storable<T>>> BatchGetAsync<T>(string containerName, IEnumerable<string> resourceIds)
        {
            _counters.IncrementRateCounter("AzureBlob BatchGetRate");
            using (_counters.CreateDisposableTimer("AzureBlob BatchGetLatency"))
            {
                List<string> resources = resourceIds.ToList();
                _counters.SetNumberCounter("AzureBlob BatchGetNumber", resources.Count);
                try
                {
                    return await Task.WhenAll(resources.Select(id => GetAsync<T>(containerName, id)));
                }
                catch
                {
                    _counters.IncrementRateCounter("AzureBlob BatchGetFailureRate");
                    throw;
                }
            }
        }

        public async Task<long> GetBlobSizeAsync(string containerName, string resourceId)
        {
            _counters.IncrementRateCounter("AzureBlob GetBlobSizeRate");
            using (_counters.CreateDisposableTimer("AzureBlob GetBlobSizeLatency"))
            {
                var container = _blobServiceClient.GetBlobContainerClient(containerName);
                var blob = container.GetBlobClient(resourceId);
                try
                {
                    // Get the blob properties, such as Size
                    BlobProperties properties = await blob.GetPropertiesAsync();

                    return properties.ContentLength;
                }
                catch (RequestFailedException ex)
                {
                    string log = $"Failed to get entity size with id {resourceId} to blob in container {containerName}";
                    Trace.TraceError("{0}, exception: {1}", log, ex);
                    _counters.IncrementRateCounter("AzureBlob GetBlobSizeFailureRate");

                    throw new RelInfraStorageException(log, ex, StorageErrorHelper.GetError(ex.Status)).Format();
                }
            }
        }


        public async Task<IDictionary<string, long>> BatchGetBlockBlobSegmentSize(string containerName, string resourceId, IEnumerable<string> blockIds)
        {
            _counters.IncrementRateCounter("AzureBlob GetBlockSizeRate");
            using (_counters.CreateDisposableTimer("AzureBlob GetBlockSizeLatency"))
            {
                try
                {
                    var container = _blobServiceClient.GetBlobContainerClient(containerName);
                    var blockBlobClient = container.GetBlockBlobClient(resourceId);

                    BlockList blockList = await blockBlobClient.GetBlockListAsync();
                    IDictionary<string, long> blockListDictionary = blockList.CommittedBlocks.Concat(blockList.UncommittedBlocks).ToDictionary(block => block.Name, block => block.SizeLong);

                    // Create a dictionary that maps the block IDs to their sizes
                    Dictionary<string, long> blockIdSizeDictionary = new Dictionary<string, long>();
                    foreach (string blockIdToFind in blockIds)
                    {
                        if (blockListDictionary.ContainsKey(blockIdToFind))
                        {
                            blockIdSizeDictionary.Add(blockIdToFind, blockListDictionary[blockIdToFind]);
                        }
                        else
                        {
                            throw new RelInfraStorageException($"block {blockIdToFind} does not exist in block blob {resourceId} in container {containerName}").Format();
                        }
                    }

                    return blockIdSizeDictionary;
                }
                catch (RequestFailedException ex)
                {
                    string blockIdsString = string.Join(",", blockIds);
                    string log = $"Failed to getblocksize with id {resourceId} blockids {blockIdsString} to blob in container {containerName}";
                    Trace.TraceError("{0}, exception: {1}", log, ex);
                    _counters.IncrementRateCounter("AzureBlob GetBlockListFailureRate");

                    throw new RelInfraStorageException(log, ex, StorageErrorHelper.GetError(ex.Status)).Format();
                }
            }
        }

        public async Task<bool> TryUpdateAsync<T>(string containerName, string resourceId, Storable<T> resource)
        {
            try
            {
                await UpdateAsync(containerName, resourceId, resource);
            }
            catch (RelInfraStorageException)
            {
                return false;
            }
            return true;
        }

        public async Task<Storable<T>> UpdateAsync<T>(string containerName, string resourceId, Storable<T> resource)
        {
            _counters.IncrementRateCounter("AzureBlob UpdateRate");
            using (_counters.CreateDisposableTimer("AzureBlob UpdateLatency"))
            {
                try
                {
                    if (string.IsNullOrWhiteSpace(resource.Etag))
                    {
                        throw new ArgumentException("etag for resource can't be null or empty", nameof(resource));
                    }

                    try
                    {
                        var container = _blobServiceClient.GetBlobContainerClient(containerName);
                        var blob = container.GetBlobClient(resourceId);

                        using var ms = new MemoryStream(Encoding.UTF8.GetBytes(SerializationHelpers.SerializeEntity(resource.Entity)));
                        BlobRequestConditions conditions = resource.Etag == "*"
                            ? null
                            : new BlobRequestConditions() { IfMatch = new ETag(resource.Etag) };
                        BlobUploadOptions options = new BlobUploadOptions() { Conditions = conditions };
                        var response = await blob.UploadAsync(ms, options);

                        return new Storable<T>
                        {
                            Id = resourceId,
                            ContainerId = containerName,
                            Etag = response.Value.ETag.ToString(),
                            Entity = SerializationHelpers.DeserializeEntity<T>(Encoding.UTF8.GetString(ms.ToArray()))
                        };
                    }
                    catch (RequestFailedException ex)
                    {
                        string log =
                            $"Failed to update entity with id {resourceId} to blob in container {containerName}";
                        Trace.TraceError("{0}, exception: {1}", log, ex);

                        throw new RelInfraStorageException(log, ex, StorageErrorHelper.GetError(ex.Status)).Format();
                    }
                }
                catch
                {
                    _counters.IncrementRateCounter("AzureBlob UpdateFailureRate");
                    throw;
                }
            }
        }

        public async Task<IEnumerable<Storable<T>>> BatchUpdateAsync<T>(string containerName, IEnumerable<Storable<T>> resourceIds)
        {
            _counters.IncrementRateCounter("AzureBlob BatchUpdateRate");
            using (_counters.CreateDisposableTimer("AzureBlob BatchUpdateLatency"))
            {
                List<Storable<T>> resources = resourceIds.ToList();
                _counters.SetNumberCounter("AzureBlob BatchUpdateNumber", resources.Count);
                try
                {
                    return await Task.WhenAll(resources.Select(res => UpdateAsync(containerName, res.Id, res)));
                }
                catch
                {
                    _counters.IncrementRateCounter("AzureBlob BatchUpdateFailureRate");
                    throw;
                }
            }
        }

        public async Task<bool> TryDeleteAsync(string containerName, string resourceId)
        {
            try
            {
                await DeleteAsync(containerName, resourceId);
            }
            catch (RelInfraStorageException)
            {
                return false;
            }
            return true;
        }

        public async Task DeleteAsync(string containerName, string resourceId)
        {
            await DeleteAsync(containerName, resourceId, "*");
        }

        public async Task DeleteAsync(string containerName, string resourceId, string etag)
        {
            _counters.IncrementRateCounter("AzureBlob DeleteRate");
            using (_counters.CreateDisposableTimer("AzureBlob DeleteLatency"))
            {
                try
                {
                    var container = _blobServiceClient.GetBlobContainerClient(containerName);
                    var blob = container.GetBlobClient(resourceId);

                    BlobRequestConditions conditions = etag == "*"
                        ? null
                        : new BlobRequestConditions() { IfMatch = new ETag(etag) };

                    using var cancellationTokenSource = new CancellationTokenSource(TimeSpan.FromSeconds(DefaultBlobTimeoutInSeconds));
                    await blob.DeleteAsync(DeleteSnapshotsOption.None, conditions, cancellationTokenSource.Token);
                }
                catch (RequestFailedException ex)
                {
                    string log = $"Failed to delete blob with id {resourceId} from container {containerName}";
                    Trace.TraceError("{0}, exception: {1}", log, ex);
                    _counters.IncrementRateCounter("AzureBlob DeleteFailureRate");

                    throw new RelInfraStorageException(log, ex, StorageErrorHelper.GetError(ex.Status)).Format();
                }
            }
        }

        public async Task DeleteContainerAsync(string containerName)
        {
            _counters.IncrementRateCounter("AzureBlob DeleteContainerRate");
            using (_counters.CreateDisposableTimer("AzureBlob DeleteContainerLatency"))
            {
                try
                {
                    var container = _blobServiceClient.GetBlobContainerClient(containerName);
                    await container.DeleteAsync();
                }
                catch (RequestFailedException ex)
                {
                    string log = $"Failed to delete blob container {containerName}";
                    Trace.TraceError("{0}, exception: {1}", log, ex);
                    _counters.IncrementRateCounter("AzureBlob DeleteContainerFailureRate");

                    throw new RelInfraStorageException(log, ex, StorageErrorHelper.GetError(ex.Status)).Format();
                }
            }
        }

        public async Task BatchDeleteAsync(string containerName, IEnumerable<string> resourceIds)
        {
            _counters.IncrementRateCounter("AzureBlob BatchDeleteRate");
            using (_counters.CreateDisposableTimer("AzureBlob BatchDeleteLatency"))
            {
                List<string> resources = resourceIds.ToList();
                _counters.SetNumberCounter("AzureBlob BatchDeleteNumber", resources.Count);
                try
                {
                    await resources.ForEachAsync(20, async id => { await DeleteAsync(containerName, id); });
                }
                catch
                {
                    _counters.IncrementRateCounter("AzureBlob BatchDeleteFailureRate");
                    throw;
                }
            }
        }

        public async Task<IEnumerable<string>> ListDirectoryAsync(string containerName, string path, int fileCountLimit, CancellationToken cancellationToken, string prefixFilter)
        {
            const int maxFilesPerIteration = 100;
            _counters.IncrementRateCounter("AzureBlob ListDirectoryRate");
            using (_counters.CreateDisposableTimer("AzureBlob ListDirectoryLatency"))
            {
                try
                {
                    var container = _blobServiceClient.GetBlobContainerClient(containerName);

                    var resultSegment = container.GetBlobsAsync(prefix: path, cancellationToken: cancellationToken)
                        .AsPages(default, maxFilesPerIteration);

                    var blobs = new List<string>();

                    // Enumerate the blobs returned for each page.
                    await foreach (Page<BlobItem> blobPage in resultSegment)
                    {
                        foreach (var blobItem in blobPage.Values)
                        {
                            var absPath = container.Uri.AppendPath(blobItem.Name).AbsolutePath;
                            if (prefixFilter == null || absPath.StartsWith(prefixFilter))
                            {
                                blobs.Add(absPath);
                                if (blobs.Count > fileCountLimit)
                                {
                                    throw new TooManyFilesException($"Got too many files when enumerating {containerName}, path {path}. Limit {fileCountLimit}");
                                }
                            }
                        }
                    }

                    return blobs;
                }
                catch (RequestFailedException ex)
                {
                    string log = $"Failed to list blobs in container {containerName} with prefix {path}";
                    Trace.TraceError("{0}, exception: {1}", log, ex);
                    _counters.IncrementRateCounter("AzureBlob ListDirectoryFailureRate");

                    throw new RelInfraStorageException(log, ex, StorageErrorHelper.GetError(ex.Status)).Format();
                }
            }
        }

        public async Task<QueryResults> ListDirectoryWithContinuationTokenAsync(string containerName, string path, string continuationToken, int takeCount, CancellationToken cancellationToken, bool useFlatBlobListing = true)
        {
            QueryResults<BlobItem> results = await ListDirectoryBlobsWithContinuationTokenAsync(containerName, path, continuationToken, takeCount, cancellationToken, useFlatBlobListing);
            return new QueryResults(
                continuationToken: results.ContinuationToken,
                results: results.Results.Select(b => b.Entity.Name));
        }

        public virtual async Task<QueryResults<BlobItem>> ListDirectoryBlobsWithContinuationTokenAsync(string containerName, string path, string continuationToken, int takeCount, CancellationToken cancellationToken, bool useFlatBlobListing = true)
        {
            const int maxFilesPerIteration = 100;
            _counters.IncrementRateCounter("AzureBlob ListDirectoryWithContinuationRate");
            using (_counters.CreateDisposableTimer("AzureBlob ListDirectoryWithContinuationLatency"))
            {
                try
                {
                    var container = _blobServiceClient.GetBlobContainerClient(containerName);
                    var blobContinuationToken = continuationToken;

                    List<Storable<BlobItem>> blobs = new List<Storable<BlobItem>>();
                    do
                    {
                        int maxResults = Math.Min(maxFilesPerIteration, takeCount - blobs.Count);
                        AsyncPageable<BlobItem> resultsPageable = container.GetBlobsAsync(prefix: path, cancellationToken: cancellationToken);

                        await foreach (Page<BlobItem> resultsPage in resultsPageable.AsPages(continuationToken: blobContinuationToken, pageSizeHint: maxResults))
                        {
                            blobContinuationToken = resultsPage.ContinuationToken;

                            blobs.AddRange(resultsPage.Values.Select(b => new Storable<BlobItem>()
                            {
                                ContainerId = container.Name,
                                Id = b.Name, // no need to remove "/container/" at the beginning
                                Entity = b,
                                Etag = b.Properties.ETag.ToString()
                            }));
                        }
                    } while (blobContinuationToken != null && blobs.Count < takeCount);

                    return new QueryResults<BlobItem>(continuationToken: blobContinuationToken, results: blobs);
                }
                catch (RequestFailedException ex)
                {
                    string log = $"Failed to list blobs in container {containerName} with prefix {path}";
                    Trace.TraceError("{0}, exception: {1}", log, ex);
                    _counters.IncrementRateCounter("AzureBlob ListDirectoryWithContinuationFailureRate");

                    throw new RelInfraStorageException(log, ex, StorageErrorHelper.GetError(ex.Status)).Format();
                }
            }
        }

        public async Task<bool> ResourceExistsAsync(string containerName, string resourceId)
        {
            if (string.IsNullOrWhiteSpace(containerName))
            {
                throw new ArgumentException("containerName can't be null or empty", nameof(containerName));
            }

            if (string.IsNullOrWhiteSpace(resourceId))
            {
                throw new ArgumentException("resourceId can't be null or empty", nameof(resourceId));
            }

            var container = _blobServiceClient.GetBlobContainerClient(containerName);
            var blob = container.GetBlobClient(resourceId);

            return await blob.ExistsAsync();
        }

        public async Task InitializeAsync(string containerName, string blobName)
        {
            _counters.IncrementRateCounter("AzureBlob InitializeBlobRate");
            using (_counters.CreateDisposableTimer("AzureBlob InitializeLatency"))
            {
                try
                {
                    //
                    // Create container if it doesn't exist
                    //
                    await InitializeAsync(containerName);

                    //
                    // Create blob if it doesn't exist
                    //
                    bool blobExists = await ResourceExistsAsync(containerName, blobName);

                    if (!blobExists)
                    {
                        Exception raisedException = null;
                        try
                        {
                            await TryPutAndIgnoreEtagAsync(containerName, blobName, new Storable<string>("CreateLease"));
                        }
                        catch (Exception ex)
                        {
                            raisedException = ex;
                        }

                        // If something went wrong, let's check if blob was created or not. If it was created, exception will be swallowed.
                        if (raisedException != null)
                        {
                            bool blobCreated = await ResourceExistsAsync(containerName, blobName);
                            if (!blobCreated)
                            {
                                throw raisedException;
                            }
                        }
                    }
                }
                catch
                {
                    _counters.IncrementRateCounter("AzureBlob InitializeBlobFailureRate");
                    throw;
                }
            }
        }

        public async Task<string> AcquireLeaseAsync(string containerName, string blobName, TimeSpan? leaseTime, string proposedLeaseId, CancellationToken cancellationToken = default(CancellationToken))
        {
            _counters.IncrementRateCounter("AzureBlob AcquireLeaseRate");
            using (_counters.CreateDisposableTimer("AzureBlob AcquireLeaseLatency"))
            {
                try
                {
                    if (string.IsNullOrWhiteSpace(containerName))
                    {
                        throw new ArgumentException("containerName can't be null or empty", nameof(containerName));
                    }

                    if (string.IsNullOrWhiteSpace(blobName))
                    {
                        throw new ArgumentException("blobName can't be null or empty", nameof(blobName));
                    }

                    try
                    {
                        var container = _blobServiceClient.GetBlobContainerClient(containerName);
                        var blob = container.GetBlobClient(blobName);

                        // Create a lease client for the blob
                        var leaseClient = blob.GetBlobLeaseClient(proposedLeaseId);

                        var leaseResponse = await leaseClient.AcquireAsync(duration: leaseTime ?? BlobLeaseClient.InfiniteLeaseDuration, cancellationToken: cancellationToken);
                        return leaseResponse.Value.LeaseId;
                    }
                    catch (RequestFailedException ex)
                    {
                        string log = $"Failed to acquire a lease, container name = {containerName}, blob {blobName}, code = {ex.Status}";
                        if (ex.Status != (int)HttpStatusCode.Conflict)
                        {
                            Trace.TraceError("{0}, exception: {1}", log, ex);
                        }

                        throw new RelInfraStorageException(log, ex, StorageErrorHelper.GetError(ex.Status)).Format();
                    }
                }
                catch
                {
                    _counters.IncrementRateCounter("AzureBlob AcquireLeaseFailureRate");
                    throw;
                }
            }
        }

        public async Task RenewLeaseAsync(string containerName, string blobName, string leaseId, CancellationToken cancellationToken = default(CancellationToken))
        {
            _counters.IncrementRateCounter("AzureBlob RenewLeaseRate");
            using (_counters.CreateDisposableTimer("AzureBlob RenewLeaseLatency"))
            {
                try
                {
                    if (string.IsNullOrWhiteSpace(containerName))
                    {
                        throw new ArgumentException("containerName can't be null or empty", nameof(containerName));
                    }

                    if (string.IsNullOrWhiteSpace(blobName))
                    {
                        throw new ArgumentException("blobName can't be null or empty", nameof(blobName));
                    }

                    try
                    {
                        var container = _blobServiceClient.GetBlobContainerClient(containerName);
                        var blob = container.GetBlobClient(blobName);

                        // Create a lease client for the blob
                        var leaseClient = blob.GetBlobLeaseClient(leaseId);

                        await leaseClient.RenewAsync(cancellationToken: cancellationToken);
                    }
                    catch (RequestFailedException ex)
                    {
                        string log =
                            $"Failed to renew a lease, container name = {containerName}, blob {blobName}, leaseId {leaseId}";
                        Trace.TraceError("{0}, exception: {1}", log, ex);

                        throw new RelInfraStorageException(log, ex, StorageErrorHelper.GetError(ex.Status)).Format();
                    }
                }
                catch
                {
                    _counters.IncrementRateCounter("AzureBlob RenewLeaseFailureRate");
                    throw;
                }
            }
        }

        public async Task ReleaseLeaseAsync(string containerName, string blobName, string leaseId, CancellationToken cancellationToken = default(CancellationToken))
        {
            _counters.IncrementRateCounter("AzureBlob ReleaseLeaseRate");
            using (_counters.CreateDisposableTimer("AzureBlob ReleaseLeaseLatency"))
            {
                try
                {
                    if (string.IsNullOrWhiteSpace(containerName))
                    {
                        throw new ArgumentException("containerName can't be null or empty", nameof(containerName));
                    }

                    if (string.IsNullOrWhiteSpace(blobName))
                    {
                        throw new ArgumentException("blobName can't be null or empty", nameof(blobName));
                    }

                    try
                    {
                        var container = _blobServiceClient.GetBlobContainerClient(containerName);
                        var blob = container.GetBlobClient(blobName);

                        // Create a lease client for the blob
                        var leaseClient = blob.GetBlobLeaseClient(leaseId);

                        await leaseClient.ReleaseAsync(cancellationToken: cancellationToken);

                    }
                    catch (RequestFailedException ex)
                    {
                        string log =
                            $"Failed to release a lease, container name = {containerName}, blob {blobName}, leaseId {leaseId}";
                        Trace.TraceError("{0}, exception: {1}", log, ex);

                        throw new RelInfraStorageException(log, ex, StorageErrorHelper.GetError(ex.Status)).Format();
                    }
                }
                catch
                {
                    _counters.IncrementRateCounter("AzureBlob ReleaseLeaseFailureRate");
                    throw;
                }
            }
        }

        public virtual async Task<Stream> GetStreamAsync(string containerName, string resourceId)
        {
            _counters.IncrementRateCounter("AzureBlob GetStreamRate");
            using (_counters.CreateDisposableTimer("AzureBlob GetStreamLatency"))
            {
                try
                {
                    if (string.IsNullOrWhiteSpace(containerName))
                    {
                        throw new ArgumentException("containerName can't be null or empty", nameof(containerName));
                    }

                    if (string.IsNullOrWhiteSpace(resourceId))
                    {
                        throw new ArgumentException("resourceId can't be null or empty", nameof(resourceId));
                    }

                    var container = _blobServiceClient.GetBlobContainerClient(containerName);
                    var blob = container.GetBlobClient(resourceId);

                    return await blob.OpenReadAsync();
                }
                catch
                {
                    _counters.IncrementRateCounter("AzureBlob GetStreamFailureRate");
                    throw;
                }
            }
        }

        public async Task UploadStreamAsync(string containerName, string resourceId, Stream stream)
        {
            await UploadStreamWithTypeAsync(containerName, resourceId, stream, null);
        }

        public async Task<bool> TryUploadStreamAsync(string containerName, string resourceId, Stream stream)
        {
            return await TryUploadStreamWithTypeAsync(containerName, resourceId, stream, null);
        }

        public async Task<bool> TryUploadStreamWithTypeAsync(string containerName, string resourceId, Stream stream, string contentType)
        {
            try
            {
                await UploadStreamWithTypeAsync(containerName, resourceId, stream, contentType);
                return true;
            }
            catch (RequestFailedException)
            {
                return false;
            }
        }

        public virtual async Task UploadStreamWithTypeAsync(string containerName, string resourceId, Stream stream, string contentType)
        {
            _counters.IncrementRateCounter("AzureBlob UploadStreamRate");
            using (_counters.CreateDisposableTimer("AzureBlob UploadStreamLatency"))
            {
                try
                {
                    if (string.IsNullOrWhiteSpace(containerName))
                    {
                        throw new ArgumentException("containerName can't be null or empty", nameof(containerName));
                    }

                    if (string.IsNullOrWhiteSpace(resourceId))
                    {
                        throw new ArgumentException("resourceId can't be null or empty", nameof(resourceId));
                    }

                    try
                    {
                        var container = _blobServiceClient.GetBlobContainerClient(containerName);
                        var blob = container.GetBlobClient(resourceId);

                        await blob.UploadAsync(stream, string.IsNullOrWhiteSpace(contentType) ? null : new BlobHttpHeaders { ContentType = contentType });
                    }
                    catch (RequestFailedException ex) //aggregate exception?
                    {
                        Trace.TraceError("Failed to upload entity with id {0} to blob in container {1}, exception: {2}", resourceId, containerName, ex);
                        throw;
                    }
                }
                catch
                {
                    _counters.IncrementRateCounter("AzureBlob UploadStreamFailureRate");
                    throw;
                }
            }
        }

        public virtual async Task DownloadToFileAsync(string containerName, string blobName, string fileLocation, CancellationToken cancellationToken)
        {
            _counters.IncrementRateCounter("AzureBlob DownloadToFileRate");
            using (_counters.CreateDisposableTimer("AzureBlob DownloadToFileLatency"))
            {
                try
                {
                    if (string.IsNullOrWhiteSpace(containerName))
                    {
                        throw new ArgumentException("containerName can't be null or empty", nameof(containerName));
                    }

                    if (string.IsNullOrWhiteSpace(blobName))
                    {
                        throw new ArgumentException("blobName can't be null or empty", nameof(blobName));
                    }

                    if (string.IsNullOrWhiteSpace(fileLocation))
                    {
                        throw new ArgumentException("fileLocation can't be null or empty", nameof(fileLocation));
                    }

                    try
                    {
                        var container = _blobServiceClient.GetBlobContainerClient(containerName);
                        var blob = container.GetBlobClient(blobName);

                        // Create a file stream with create mode
                        using var fileStream = new FileStream(fileLocation, FileMode.Create);
                        await blob.DownloadToAsync(fileStream, cancellationToken: cancellationToken);
                    }
                    catch (RequestFailedException ex)
                    {
                        Trace.TraceError("Failed to download to blob {0} in container {1}, exception: {2}", blobName, containerName, ex);

                        throw;
                    }
                }
                catch
                {
                    _counters.IncrementRateCounter("AzureBlob DownloadToFileFailureRate");
                    throw;
                }
            }
        }

        public async Task UploadFileAsync(string containerName, string blobName, string fileLocation, CancellationToken cancellationToken)
        {
            _counters.IncrementRateCounter("AzureBlob UploadFileRate");
            using (_counters.CreateDisposableTimer("AzureBlob UploadFileLatency"))
            {
                try
                {
                    if (string.IsNullOrWhiteSpace(containerName))
                    {
                        throw new ArgumentException("containerName can't be null or empty", nameof(containerName));
                    }

                    if (string.IsNullOrWhiteSpace(blobName))
                    {
                        throw new ArgumentException("blobName can't be null or empty", nameof(blobName));
                    }

                    if (string.IsNullOrWhiteSpace(fileLocation))
                    {
                        throw new ArgumentException("fileLocation can't be null or empty", nameof(fileLocation));
                    }

                    try
                    {
                        var container = _blobServiceClient.GetBlobContainerClient(containerName);
                        var blob = container.GetBlobClient(blobName);

                        await blob.UploadAsync(fileLocation, true, cancellationToken);
                    }
                    catch (RequestFailedException ex)
                    {
                        Trace.TraceError("Failed to upload blob {0} in container {1}, exception: {2}", blobName, containerName, ex);

                        throw;
                    }
                }
                catch
                {
                    _counters.IncrementRateCounter("AzureBlob UploadFileFailureRate");
                    throw;
                }
            }
        }


        public async Task StartCopyAsync(string srcContainerName, string sourceResourceId, string destContainerName, string destinationResourceId)
        {
            _counters.IncrementRateCounter("AzureBlob StartCopyRate");
            using (_counters.CreateDisposableTimer("AzureBlob StartCopyLatency"))
            {
                try
                {
                    var srcContainer = _blobServiceClient.GetBlobContainerClient(srcContainerName);
                    var srcBlob = srcContainer.GetBlobClient(sourceResourceId);
                    var destContainer = _blobServiceClient.GetBlobContainerClient(destContainerName);
                    var destBlob = srcContainer.GetBlobClient(destinationResourceId);

                    await destBlob.StartCopyFromUriAsync(srcBlob.Uri);
                }
                catch (RequestFailedException ex)
                {
                    string log = $"Failed to start copy blob in container {srcContainerName} resourceId {sourceResourceId} to container {destContainerName} resourceId {destinationResourceId}";
                    Trace.TraceError("{0}, exception: {1}", log, ex);
                    _counters.IncrementRateCounter("AzureBlob StartCopyFailureRate");

                    throw new RelInfraStorageException(log, ex, StorageErrorHelper.GetError(ex.Status)).Format();
                }
            }
        }

        public async Task<CopyStatus> GetCopyStateAsync(string containerName, string resourceId)
        {
            _counters.IncrementRateCounter("AzureBlob GetCopyStateRate");
            using (_counters.CreateDisposableTimer("AzureBlob GetCopyStateLatency"))
            {
                try
                {
                    var container = _blobServiceClient.GetBlobContainerClient(containerName);
                    var blob = container.GetBlobClient(resourceId);

                    var properties = await blob.GetPropertiesAsync();

                    return properties.Value.CopyStatus;
                }
                catch (RequestFailedException ex)
                {
                    string log = $"Failed to get copy state blob in container {containerName} resourceId {resourceId}";
                    Trace.TraceError("{0}, exception: {1}", log, ex);
                    _counters.IncrementRateCounter("AzureBlob GetCopyStateFailureRate");

                    throw new RelInfraStorageException(log, ex, StorageErrorHelper.GetError(ex.Status)).Format();
                }
            }
        }

        public async Task<string> GetBlobMetadataAsync(string containerName, string resourceId, string metadataKey)
        {
            _counters.IncrementRateCounter("AzureBlob GetMetadataRate");

            using (_counters.CreateDisposableTimer("AzureBlob GetMetadataLatency"))
            {
                try
                {
                    var container = _blobServiceClient.GetBlobContainerClient(containerName);
                    var blob = container.GetBlobClient(resourceId);

                    BlobProperties properties = await blob.GetPropertiesAsync();

                    return (properties.Metadata != null && properties.Metadata.ContainsKey(metadataKey)) ? properties.Metadata[metadataKey] : null;
                }
                catch (RequestFailedException ex)
                {
                    string log =
                        $"Failed to check entity metadata with id {resourceId} to blob in container {containerName}";
                    Trace.TraceError("{0}, exception: {1}", log, ex);
                    _counters.IncrementRateCounter("AzureBlob GetMetadataFailureRate");

                    throw new RelInfraStorageException(log, ex, StorageErrorHelper.GetError(ex.Status)).Format();
                }
            }
        }

        public async Task SetBlobMetadataAsync(string containerName, string resourceId, string metadataKey, string metadataValue)
        {
            _counters.IncrementRateCounter("AzureBlob SetMetadataRate");

            using (_counters.CreateDisposableTimer("AzureBlob SetMetadataLatency"))
            {
                try
                {
                    var container = _blobServiceClient.GetBlobContainerClient(containerName);
                    var blob = container.GetBlobClient(resourceId);

                    // Create metadata dictionary and add key-value pair
                    var metadata = new Dictionary<string, string>
                    {
                        { metadataKey, metadataValue }
                    };

                    await blob.SetMetadataAsync(metadata);
                }
                catch (RequestFailedException ex)
                {
                    string log =
                        $"Failed to set entity metadata with id {resourceId} to blob in container {containerName}";
                    Trace.TraceError("{0}, exception: {1}", log, ex);
                    _counters.IncrementRateCounter("AzureBlob SetMetadataFailureRate");

                    throw new RelInfraStorageException(log, ex, StorageErrorHelper.GetError(ex.Status)).Format();
                }
            }
        }

        private Uri GenerateBlobSasUri(BlobClient blob, DateTimeOffset expiryDate, BlobSasPermissions permissions)
        {
            var userDelegationKey = _blobServiceClient.GetUserDelegationKey(DateTimeOffset.UtcNow, expiryDate.ToUniversalTime());
            var sasBuilder = new BlobSasBuilder()
            {
                BlobContainerName = blob.BlobContainerName,
                BlobName = blob.Name,
                Resource = "b", // "b" for blob, "c" for container
                StartsOn = DateTimeOffset.UtcNow,
                ExpiresOn = expiryDate.ToUniversalTime(),
            };

            sasBuilder.SetPermissions(permissions);
            var blobUriBuilder = new BlobUriBuilder(blob.Uri)
            {
                Sas = sasBuilder.ToSasQueryParameters(userDelegationKey, _blobServiceClient.AccountName)
            };

            return blobUriBuilder.ToUri();
        }

        private Uri GenerateContainerSasUri(BlobContainerClient container, DateTimeOffset expiryDate, BlobContainerSasPermissions permissions)
        {
            var userDelegationKey = _blobServiceClient.GetUserDelegationKey(DateTimeOffset.UtcNow, expiryDate.ToUniversalTime());
            var sasBuilder = new BlobSasBuilder()
            {
                BlobContainerName = container.Name,
                Resource = "c", // "b" for blob, "c" for container
                StartsOn = DateTimeOffset.UtcNow,
                ExpiresOn = expiryDate.ToUniversalTime(),
            };

            sasBuilder.SetPermissions(permissions);
            var blobUriBuilder = new BlobUriBuilder(container.Uri)
            {
                Sas = sasBuilder.ToSasQueryParameters(userDelegationKey, _blobServiceClient.AccountName)
            };

            return blobUriBuilder.ToUri();
        }
    }
}
