using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.RelInfra.Common;
using Microsoft.RelInfra.Common.RetryPolicy;
using Microsoft.RelInfra.Common.Serialization;
using Microsoft.RelInfra.Extensions;
using Microsoft.RelInfra.Instrumentation;
using Microsoft.RelInfra.Storage.Utils;

namespace Microsoft.RelInfra.Storage
{
    [SuppressMessage("ReSharper", "CA1001")] // https://stackoverflow.com/questions/********/do-httpclient-and-httpclienthandler-have-to-be-disposed
    public class AzureBlobStorage2: IRelInfraStreamableStorage, IAzureBlobStorage
    {
        private readonly string _azureStorageAccountName;
        
        public static string ContainerUrl(string storageAccount, string container) => $"https://{storageAccount}.blob.core.windows.net/{container}";
        public static string BlobUrl(string storageAccount, string container, string blob) => $"{ContainerUrl(storageAccount, container)}/{blob}";
        
        private readonly HttpClient _httpStorageClient; // won't use WindowsAzure.Storage due to (at least) ugly AAD support
        
        private class AzureBlobStorage2HttpResponseValidator : IAetherHttpResponseValidator
        {
            private readonly AetherHttpResponseValidator _aetherHttpResponseValidator = new AetherHttpResponseValidator();
            
            public async Task ValidateAsync(HttpResponseMessage response)
            {
                HttpStatusCode code = response.StatusCode;
                if (code == HttpStatusCode.NotFound || code == HttpStatusCode.Created || code == HttpStatusCode.Conflict)
                {
                    return;
                }

                await _aetherHttpResponseValidator.ValidateAsync(response);
            }
        }
        
        public AzureBlobStorage2(Func<Task<string>> getTokenAsync, string azureStorageAccountName, CounterManager counterManager, IRelInfraRetryPolicy retryPolicy)
        {
            RetryPolicy = retryPolicy;
            _azureStorageAccountName = azureStorageAccountName;
            var counters = new OptionalCounters(counterManager, azureStorageAccountName);
            
            _httpStorageClient = new HttpClient(new AetherHttpClientHandler(
                getTokenAsync, 
                incrementRateCounter: counters.IncrementRateCounter, 
                getLatencyCounter: counters.CreateDisposableTimer, 
                retryPolicy, 
                counterGroupName: "AzureBlob",
                responseValidator: new AzureBlobStorage2HttpResponseValidator(), 
                logTraceId: null));
            
            _httpStorageClient.DefaultRequestHeaders.Add("x-ms-version", "2017-11-09");
        }

        public IRelInfraRetryPolicy RetryPolicy { get; set; }
        
        // https://docs.microsoft.com/en-us/rest/api/storageservices/get-blob-metadata
        public async Task<bool> ExistsAsync(string containerName, string resourceId)
        {
            using (var request = new HttpRequestMessage(HttpMethod.Get, $"{BlobUrl(_azureStorageAccountName, containerName, resourceId)}?comp=metadata"))
            using (HttpResponseMessage response = await _httpStorageClient.SendAsync(request))
            {
                if (!response.IsSuccessStatusCode)
                {
                    if (response.StatusCode == HttpStatusCode.NotFound)
                    {
                        return false;
                    }

                    await response.EnsureSuccessStatusCodeAsync();
                }
                
                await response.Content.ReadAsByteArrayAsync();
                return response.StatusCode == HttpStatusCode.OK;
            }
        }

        public async Task<Storable<T>> AddAsync<T>(string containerName, string resourceId, Storable<T> resource)
        {
            if (resource.Etag != null)
            {
                throw new ArgumentException("Etag should be null.");
            }
            
            await PutInternalAsync(containerName, resourceId, resource.Entity, new KeyValuePair<string, string>("if-none-match", "*"), CancellationToken.None);
            return null;
        }

        public Task<bool> TryPutAndIgnoreEtagAsync<T>(string containerName, string resourceId, Storable<T> resource)
        {
            throw new NotImplementedException();
        }

        public async Task<Storable<T>> PutAndIgnoreEtagAsync<T>(string containerName, string resourceId, Storable<T> resource)
        {
            return await PutAndIgnoreEtagAsync<T>(containerName, resourceId, resource, CancellationToken.None);
        }

        public async Task<Storable<T>> PutAndIgnoreEtagAsync<T>(string containerName, string resourceId, Storable<T> resource, CancellationToken cancellationToken)
        {
            await PutInternalAsync(containerName, resourceId, resource.Entity, new KeyValuePair<string, string>("if-match", ""), cancellationToken);
            return null;
        }

        public Task<bool> TryBatchPutAsync<T>(string containerName, IEnumerable<Storable<T>> resources)
        {
            throw new NotImplementedException();
        }

        public Task<IEnumerable<Storable<T>>> BatchPutAsync<T>(string containerName, IEnumerable<Storable<T>> resources)
        {
            throw new NotImplementedException();
        }

        public Task<Storable<T>> TryGetAsync<T>(string containerName, string resourceId)
        {
            throw new NotImplementedException();
        }

        public async Task<Storable<T>> GetAsync<T>(string containerName, string resourceId)
        {
            return await GetAsync<T>(containerName, resourceId, CancellationToken.None);
        }

        public async Task<Storable<T>> GetAsync<T>(string containerName, string resourceId, CancellationToken cancellationToken)
        {
            Storable<string> stringStorable = await GetBlobStringAsync(containerName, resourceId, cancellationToken);
            return new Storable<T>
            {
                Entity = SerializationHelpers.DeserializeEntity<T>(stringStorable.Entity), 
                Etag = stringStorable.Etag
            };
        }

        public Task<IEnumerable<Storable<T>>> BatchGetAsync<T>(string containerName, IEnumerable<string> resourceIds)
        {
            throw new NotImplementedException();
        }

        public Task<bool> TryUpdateAsync<T>(string containerName, string resourceId, Storable<T> resource)
        {
            throw new NotImplementedException();
        }

        private void EnsureValidEtag(string etag)
        {
            if (string.IsNullOrEmpty(etag) || etag.Length < 2)
            {
                throw new ArgumentException($"Valid Etag required, got '{etag}'.");
            }
        }
        
        public async Task<Storable<T>> UpdateAsync<T>(string containerName, string resourceId, Storable<T> resource)
        {
            EnsureValidEtag(resource.Etag);
            await PutInternalAsync(containerName, resourceId, resource.Entity, new KeyValuePair<string, string>("if-match", resource.Etag), CancellationToken.None);
            return null;
        }
        
        // https://docs.microsoft.com/en-us/rest/api/storageservices/put-blob
        private async Task PutInternalAsync<T>(string containerName, string resourceId, T entity, KeyValuePair<string,string> header, CancellationToken cancellationToken)
        {
            using (var request = new HttpRequestMessage(HttpMethod.Put,BlobUrl(_azureStorageAccountName, containerName, resourceId)))
            {
                request.Content = new StringContent(SerializationHelpers.SerializeEntity(entity), Encoding.UTF8, "application/json");  // CodeQL [SM00430] Since the code does not handle server responses, and the expected data type is JSON rather than HTML, it is unnecessary to sanitize the requests.
                request.Headers.Add("x-ms-date", DateTime.UtcNow.ToString("R", CultureInfo.InvariantCulture));
                request.Headers.Add("x-ms-blob-type", "BlockBlob");
                request.Headers.Add(header.Key, header.Value);

                using (HttpResponseMessage response = await _httpStorageClient.SendAsync(request, cancellationToken))
                {
                    await response.EnsureSuccessStatusCodeAsync();
                }
            }
        } 

        public Task<IEnumerable<Storable<T>>> BatchUpdateAsync<T>(string containerName, IEnumerable<Storable<T>> resources)
        {
            throw new NotImplementedException();
        }

        public Task<bool> TryDeleteAsync(string containerName, string resourceId)
        {
            throw new NotImplementedException();
        }

        public Task DeleteAsync(string containerName, string resourceId)
        {
            throw new NotImplementedException();
        }

        public Task DeleteAsync(string containerName, string resourceId, string etag)
        {
            throw new NotImplementedException();
        }

        public Task BatchDeleteAsync(string containerName, IEnumerable<string> resourceIds)
        {
            throw new NotImplementedException();
        }

        // https://docs.microsoft.com/en-us/rest/api/storageservices/create-container
        public async Task InitializeAsync(string containerName)
        {
            using (var request = new HttpRequestMessage(HttpMethod.Put,$"{ContainerUrl(_azureStorageAccountName, containerName)}/?restype=container"))
            {
                request.Headers.Add("x-ms-date", DateTime.UtcNow.ToString("R", CultureInfo.InvariantCulture));
                request.Headers.Add("if-none-match", "*");

                using (HttpResponseMessage response = await _httpStorageClient.SendAsync(request))
                {
                    if (response.StatusCode == HttpStatusCode.Created || response.StatusCode == HttpStatusCode.Conflict)
                    {
                        return;
                    }

                    await response.EnsureSuccessStatusCodeAsync();
                }
            }
        }

        public Task<Stream> GetStreamAsync(string containerName, string resourceId)
        {
            throw new NotImplementedException();
        }

        public Task UploadStreamAsync(string containerName, string resourceId, Stream stream)
        {
            throw new NotImplementedException();
        }

        public Task<bool> TryUploadStreamAsync(string containerName, string resourceId, Stream stream)
        {
            throw new NotImplementedException();
        }

        public Task AppendStreamAsync(string containerName, string resourceId, Stream dataStream)
        {
            throw new NotImplementedException();
        }

        public Task<Storable<T>> TryGetAsync<T>(string containerName, string resourceId, CancellationToken cancellationToken)
        {
            throw new NotImplementedException();
        }

        public Task<Storable<string>> AppendAsync<T>(string containerName, string resourceId, T resource)
        {
            throw new NotImplementedException();
        }

        public Task<Storable<T>> GetBlobEntityAsync<T>(string containerName, string resourceId)
        {
            throw new NotImplementedException();
        }

        public Task<Storable<T>> GetBlobEntityAsync<T>(string containerName, string resourceId, CancellationToken cancellationToken)
        {
            throw new NotImplementedException();
        }

        // https://docs.microsoft.com/en-us/rest/api/storageservices/get-blob-metadata
        public async Task<string> GetBlobMetadataAsync(string containerName, string resourceId, string metadataKey)
        {
            using (var request = new HttpRequestMessage(HttpMethod.Get, $"{BlobUrl(_azureStorageAccountName, containerName, resourceId)}?comp=metadata"))
            using (HttpResponseMessage response = await _httpStorageClient.SendAsync(request))
            {
                await response.EnsureSuccessStatusCodeAsync();
                await response.Content.ReadAsByteArrayAsync();
                return response.Headers.GetValues(metadataKey).FirstOrDefault();
            }
        }

        public Task SetBlobMetadataAsync(string containerName, string resourceId, string metadataKey, string metadataValue)
        {
            throw new NotImplementedException();
        }

        // https://docs.microsoft.com/en-us/rest/api/storageservices/get-blob
        public async Task<Storable<string>> GetBlobStringAsync(string containerName, string resourceId)
        {
            return await GetBlobStringAsync(containerName, resourceId, CancellationToken.None);
        }

        public async Task<Storable<string>> GetBlobStringAsync(string containerName, string resourceId, CancellationToken cancellationToken)
        {
            using (var request = new HttpRequestMessage(HttpMethod.Get, BlobUrl(_azureStorageAccountName, containerName, resourceId)))
            using (HttpResponseMessage response = await _httpStorageClient.SendAsync(request, cancellationToken))
            {
                await response.EnsureSuccessStatusCodeAsync();
                using (var streamReader = new StreamReader(await response.Content.ReadAsStreamAsync()))
                {
                    return new Storable<string>
                    {
                        Entity = await streamReader.ReadToEndAsync(),
                        Etag = response.Headers.ETag.Tag
                    };
                }
            }
        }

        public Uri GetBlobUri(string containerName, string blobName, DateTimeOffset? expiryDate = null)
        {
            throw new NotImplementedException();
        }

        public Uri GetContainerReadUri(string containerName, DateTimeOffset? expiryDate = null)
        {
            throw new NotImplementedException();
        }

        public Uri GetWritableBlobUri(string containerName, string blobName, DateTimeOffset expiryDate)
        {
            throw new NotImplementedException();
        }

        public Task<bool> TryUploadStreamWithTypeAsync(string containerName, string resourceId, Stream stream, string contentType)
        {
            throw new NotImplementedException();
        }

        public Task<IEnumerable<string>> ListDirectoryAsync(string containerName, string path, int fileCountLimit, CancellationToken cancellationToken, string prefixFilter)
        {
            throw new NotImplementedException();
        }

        public Task<QueryResults> ListDirectoryWithContinuationTokenAsync(string containerName, string path, string continuationToken, int takeCount, CancellationToken cancellationToken,
            bool useFlatBlobListing = true)
        {
            throw new NotImplementedException();
        }

        public async Task<Storable<T>> PutWithETagAsync<T>(string containerName, string resourceId, Storable<T> resource)
        {
            EnsureValidEtag(resource.Etag);
            await PutInternalAsync(containerName, resourceId, resource.Entity, new KeyValuePair<string, string>("if-match", resource.Etag), CancellationToken.None);
            return null;
        }

        public Task DownloadToFileAsync(string containerName, string blobName, string fileLocation, CancellationToken cancellationToken)
        {
            throw new NotImplementedException();
        }

        public Task UploadFileAsync(string containerName, string blobName, string fileLocation, CancellationToken cancellationToken)
        {
            throw new NotImplementedException();
        }

        public Task<long> GetBlobSizeAsync(string containerName, string resourceId)
        {
            throw new NotImplementedException();
        }

        public Task RenewLeaseAsync(string containerName, string blobName, string leaseId)
        {
            throw new NotImplementedException();
        }

        public Task RenewLeaseAsync(string containerName, string blobName, string leaseId, CancellationToken cancellationToken)
        {
            throw new NotImplementedException();
        }

        public Task<string> AcquireLeaseAsync(string containerName, string blobName, TimeSpan? leaseTime, string proposedLeaseId)
        {
            throw new NotImplementedException();
        }

        public Task<string> AcquireLeaseAsync(string containerName, string blobName, TimeSpan? leaseTime, string proposedLeaseId, CancellationToken cancellationToken)
        {
            throw new NotImplementedException();
        }

        public Task<bool> ResourceExistsAsync(string containerName, string blobName)
        {
            throw new NotImplementedException();
        }

        public Task ReleaseLeaseAsync(string containerName, string blobName, string leaseId)
        {
            throw new NotImplementedException();
        }
        public Task ReleaseLeaseAsync(string containerName, string blobName, string leaseId, CancellationToken cancellationToken)
        {
            throw new NotImplementedException();
        }

        public Uri GetContainerWritableUri(string containerName, DateTimeOffset? expiryDate = null)
        {
            throw new NotImplementedException();
        }

        public Task DeleteContainerAsync(string containerName)
        {
            throw new NotImplementedException();
        }
    }
}