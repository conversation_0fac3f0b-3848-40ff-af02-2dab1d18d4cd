﻿using System.Collections.Generic;
using System.Threading.Tasks;

namespace Microsoft.RelInfra.Storage
{
    public interface IQueryableStorage : IStorage
    {
        Task<IEnumerable<Storable<T>>> ExecuteQueryAsync<T>(string query, int? takeCount = null);

        Task<QueryResults<T>> GetPartitionWithCountinuationTokenAsync<T>(string partitionKey, string continuationToken, int? takeCount = null);

        Task<QueryResults<T>> GetPartitionWithCountinuationTokenAsync<T>(string partitionKey, IEnumerable<string> columns, string continuationToken = null, int? takeCount = null);

        Task<IEnumerable<Storable<T>>> GetPartitionAsync<T>(string partitionKey, int? takeCount = null);

        Task<IEnumerable<Storable<T>>> GetAllAsync<T>(int? takeCount = null);

        Task CreateIfNotExistsAsync();

        Task<QueryResults<T>> GetAllWithContinuationTokenAsync<T>(string continuationToken = null, int? takeCount = null);
    }
}
