﻿using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.RelInfra.Common.RetryExecution;
using Microsoft.RelInfra.Common.RetryExecution.Policy;
using Microsoft.RelInfra.Extensions;
using Microsoft.RelInfra.Instrumentation.Logging;
using Microsoft.RelInfra.Storage.Exceptions;

namespace Microsoft.RelInfra.Storage
{
    public class AzureAutoRenewLease
    {
        private Timer _updateTimer;
        private readonly ILeasableStorage _storage;
        private readonly string _resourceId;
        private string _leaseId;
        private readonly TimeSpan _leaseTime;
        private readonly SemaphoreSlim _semaphore = new SemaphoreSlim(1, 1);
        private const int DefaultTimeOutInSeconds = 3;
        private readonly Microsoft.RelInfra.Common.RetryExecution.Policy.IRetryPolicy _defaultRetryPolicy;

        public AzureAutoRenewLease(ILeasableStorage storage, string resourceId, TimeSpan leaseTime)
        {
            _storage = storage;
            _leaseTime = leaseTime;
            _resourceId = resourceId;
            
            if (_leaseTime < TimeSpan.FromSeconds(15) || _leaseTime > TimeSpan.FromSeconds(60))
            {
                throw new ArgumentException($"Lease time should be between 15 and 60 seconds, but was {_leaseTime}. Check out https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/Lease-Blob");
            }

            _defaultRetryPolicy = new LinearRetryPolicy(TimeSpan.FromMilliseconds(1), 1);
        }

        public async Task<bool> TryAcquireLeaseAsync()
        { 
            try
            {
                return await AcquireLeaseAsync(retryPolicy: _defaultRetryPolicy, shouldRetryException: DefaultShouldRetryException);
            }
            catch 
            {
                return false;
            }
        }

        public async Task<bool> TryAcquireLeaseAsync(int timeOutInSeconds = DefaultTimeOutInSeconds)
        {
            try
            {
                return await AcquireLeaseAsync(_defaultRetryPolicy, DefaultShouldRetryException, timeOutInSeconds);
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> TryAcquireLeaseAsync(IRetryPolicy retryPolicy, Func<Exception, bool> shouldRetryException)
        {
            try
            {
                return await AcquireLeaseAsync(retryPolicy: retryPolicy, shouldRetryException: shouldRetryException);
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> AcquireLeaseAsync(int timeOutInSeconds = DefaultTimeOutInSeconds)
        {
            return await AcquireLeaseAsync(_defaultRetryPolicy, DefaultShouldRetryException, timeOutInSeconds);
        }

        public async Task<bool> AcquireLeaseAsync(IRetryPolicy retryPolicy, Func<Exception, bool> shouldRetryException, int timeOutInSeconds = DefaultTimeOutInSeconds)
        {
            CommonLogger.LogEntityInfo(_resourceId, "Calling AcquireLeaseAsync, _leaseId={lease_id}", _leaseId);

            bool isInitialized = false;
            try
            {
                await RetryExecutionHelper.ExecuteFuncWithRetryAsync(
                    function: async () =>
                    {
                        using (var cancellationTokenSource = new CancellationTokenSource(TimeSpan.FromSeconds(timeOutInSeconds)))
                        {
                            _leaseId = await _storage.AcquireLeaseAsync(_resourceId, _leaseTime, cancellationToken: cancellationTokenSource.Token);
                            CommonLogger.LogEntityInfo(_resourceId, "Acquired lease, _leaseId={lease_id}, _leaseTime={lease_time}", _leaseId, _leaseTime);
                            isInitialized = true;
                        }

                        return true;
                    },
                    shouldRetryException: shouldRetryException,
                    retryPolicy: retryPolicy,
                    token: CancellationToken.None);
            }
            catch (RelInfraStorageException ex) when (ex.Error == StorageError.NotFound)
            {
                // If AcquireLeaseAsync return 404, we need to initialize it first (create a blob)
                isInitialized = false;
            }
            catch (RelInfraStorageException ex) when (ex.Error == StorageError.Conflict)
            {
                // Swallow the conflict error, because the same resource may be under processing
                CommonLogger.LogEntityWarning(_resourceId, "failed to acquire lease because it's already in use. {safe_exception_message}, exception: {exception}", ex.Message, ex);

                return false;
            }
            catch (Exception e)
            {
                CommonLogger.LogEntityError(_resourceId, "failed to acquire lease {safe_exception_message}, exception: {exception}", e.Message, e);

                throw;
            }

            if (!isInitialized)
            {
                try
                {
                    CommonLogger.LogEntityInfo(_resourceId, "Initializing lease storage for resource [{resource_id}]", _resourceId);
                    await _storage.InitializeBlobAsync(_resourceId);
                }
                catch (Exception ex)
                {
                    CommonLogger.LogEntityError(_resourceId, "Failed to initialized lease storage for resource [{resource_id}], error: {safe_exception_message}, stack trace: {stack_trace}", _resourceId, ex.Message, ex.StackTrace);

                    throw;
                }

                try
                {
                    await RetryExecutionHelper.ExecuteFuncWithRetryAsync(
                        function: async () =>
                        {
                            using (var cancellationTokenSource = new CancellationTokenSource(TimeSpan.FromSeconds(timeOutInSeconds)))
                            {
                                _leaseId = await _storage.AcquireLeaseAsync(_resourceId, _leaseTime, cancellationToken: cancellationTokenSource.Token);
                                CommonLogger.LogEntityInfo(_resourceId, "Acquired lease, _leaseId={lease_id}, _leaseTime={lease_time}", _leaseId, _leaseTime);
                            }

                            return true;
                        },
                        shouldRetryException: shouldRetryException,
                        retryPolicy: retryPolicy,
                        token: CancellationToken.None);
                }
                catch (RelInfraStorageException ex) when (ex.Error == StorageError.Conflict)
                {
                    CommonLogger.LogEntityWarning(_resourceId, "failed to acquire lease because it's already in use. {safe_exception_message}, exception: {exception}", ex.Message, ex);

                    return false;
                }
                catch (Exception e)
                {
                    CommonLogger.LogEntityError(_resourceId, "failed to acquire lease {safe_exception_message}, exception: {exception}", e.Message, e);

                    throw;
                }
            }

            _updateTimer = new Timer(RenewLeaseCycleAsync, null, TimeSpan.FromSeconds(0.6 * _leaseTime.TotalSeconds), TimeSpan.FromSeconds(0.6 * _leaseTime.TotalSeconds));

            return true;
        }

        /// <summary>
        /// Releases lease if lease was aquired, otherwise it does nothing.
        /// </summary>
        /// <returns></returns>
        public async Task ReleaseLeaseAsync()
        {
            CommonLogger.LogEntityInfo(_resourceId, "Calling ReleaseLeaseAsync, _leaseId={lease_id}", _leaseId);

            _updateTimer?.Dispose();
            _updateTimer = null;

            if (!string.IsNullOrEmpty(_leaseId))
            {
                using (var cancellationTokenSource = new CancellationTokenSource(TimeSpan.FromSeconds(DefaultTimeOutInSeconds)))
                {
                    await _storage.ReleaseLeaseAsync(_resourceId, _leaseId, cancellationTokenSource.Token);
                    CommonLogger.LogEntityInfo(_resourceId, "Released lease, _leaseId={lease_id}", _leaseId);
                }
            }
        }

        public bool ShouldRetryConflictException(Exception ex)
        {
            return !(ex is RelInfraStorageException) || (ex is RelInfraStorageException relex && relex.Error == StorageError.Conflict);
        }

        private async void RenewLeaseCycleAsync(object state)
        {
            CommonLogger.LogEntityInfo(_resourceId, "Calling RenewLeaseCycleAsync, _leaseId={lease_id}", _leaseId);

            try
            {
                await _semaphore.WaitAsync();
                if (_updateTimer == null)
                {
                    return;
                }

                if (!string.IsNullOrEmpty(_leaseId))
                {
                    using (var cancellationTokenSource = new CancellationTokenSource(TimeSpan.FromSeconds(DefaultTimeOutInSeconds)))
                    {
                        await _storage.RenewLeaseAsync(_resourceId, _leaseId, cancellationTokenSource.Token);
                        CommonLogger.LogEntityInfo(_resourceId, "Renewed lease, _leaseId={lease_id}", _leaseId);
                    }
                }
            }
            catch (Exception ex)
            {
                CommonLogger.LogEntityWarning(_resourceId, "Exception in RenewLeaseCycle (leaseId={lease_id}): error: {exception_message}, stack trace: {stack_trace}", _leaseId, ex.Message, ex.StackTrace);
                _leaseId = null;
            }
            finally
            {
                _semaphore.Release();
            }
        }

        private bool DefaultShouldRetryException(Exception ex)
        {
            return !(ex is RelInfraStorageException);
        }
    }
}
