﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Reflection;
using Microsoft.RelInfra.Common.Serialization;
using Microsoft.RelInfra.Storage.Exceptions;
using Microsoft.WindowsAzure.Storage.Table;
using Microsoft.RelInfra.Instrumentation.Logging;

namespace Microsoft.RelInfra.Storage
{
    internal class EntityPropertyInfo
    {
        /// <summary>
        /// property info retrived by reflection
        /// </summary>
        public PropertyInfo PropertyInfo { get; set; }

        /// <summary>
        /// should we treat property as large object and divide it into multiple fields
        /// </summary>
        public bool IsLargeObject { get; set; }

        /// <summary>
        /// should we treat property as compress object and apply compress strategy to it if raw property value size exceeds column max limit
        /// </summary>
        public bool IsCompressObject { get; set; }

        /// <summary>
        /// in IsLargeObject is true this columns are named X1, X2, X3 ... with this filed set to true columns are named X, X1, X2, X3 
        /// </summary>
        public bool UseDirectNameAsFirstColumn { get; set; }

        /// <summary>
        /// Number of fields allocated for large object
        /// </summary>
        public int NumSections { get; set; }
    }

    internal sealed class AzureTableEntity<T>
    {
        private const string PropertyNameForSimpleEntity = "entity";
        // For azure table string type property, the maximum number of characters supported is about 32 K or less.
        private const int MaxStringFieldSize = 32 * 1024;
        // For azure table byte[] type property, the maximum size supported is about 64 K or less.
        private const int MaxByteArrayFieldSize = 64 * 1024;

        private const string TooLargeAzureTableDataMessageFormat = "Input data is too big to fit into Azure table key={0}, row={1}, property={2}, length={3}, propertyNumSections={4}, maxFieldSize={5}";

        private readonly DynamicTableEntity _dte;
        private T _entity;

        private static readonly HashSet<Type> s_builtInTypes = new HashSet<Type>
        {
            typeof (string),
            typeof (int),
            typeof (long),
            typeof (double),
            typeof (bool),
            typeof (DateTime),
            typeof (DateTimeOffset),
            typeof (Guid)
        };

        internal static List<EntityPropertyInfo> TypeProperties;

        static AzureTableEntity()
        {
            TypeProperties = new List<EntityPropertyInfo>();
            if (!s_builtInTypes.Contains(typeof (T)))
            {
                foreach (var propertyInfo in typeof(T).GetProperties().Where(p => p.CanRead && p.CanWrite))
                {
                    object attribute;
                    var isCompressObject = propertyInfo.GetCustomAttributes(typeof(CompressObjectAttribute)).FirstOrDefault() != null;
                    if ((attribute = propertyInfo.GetCustomAttributes(typeof(LargeObjectExAttribute)).FirstOrDefault()) != null)
                    {
                        TypeProperties.Add(new EntityPropertyInfo
                        {
                            PropertyInfo = propertyInfo,
                            IsLargeObject = true,
                            IsCompressObject = isCompressObject,
                            UseDirectNameAsFirstColumn = true,
                            NumSections = ((LargeObjectExAttribute)attribute).NumSections
                        });
                    }
                    else if ((attribute = propertyInfo.GetCustomAttributes(typeof(LargeObjectAttribute)).FirstOrDefault()) != null)
                    {
                        TypeProperties.Add(new EntityPropertyInfo
                        {
                            PropertyInfo = propertyInfo,
                            IsLargeObject = true,
                            IsCompressObject = isCompressObject,
                            UseDirectNameAsFirstColumn = false,
                            NumSections = ((LargeObjectAttribute)attribute).NumSections
                        });
                    }
                    else
                    {
                        TypeProperties.Add(new EntityPropertyInfo
                        {
                            PropertyInfo = propertyInfo,
                            IsLargeObject = false,
                            IsCompressObject = isCompressObject,
                            UseDirectNameAsFirstColumn = false,
                            NumSections = 0
                        });
                    }
                }
            }
        }

        public AzureTableEntity(DynamicTableEntity dte)
        {
            _dte = dte;
            _entity = default(T);
        }

        public AzureTableEntity(string partitionKey, string rowKey, T entity, string referenceName)
        {
            if (!IsValidTableKey(partitionKey) || !IsValidTableKey(rowKey))
            {
                throw new InvalidTableKeyException(
                    message: $@"Invalid Characters found, rowKey={rowKey}, partitionKey={partitionKey}. Special Characters like / \ # @ | are not allowed.",
                    paramName: "TableKey",
                    source: referenceName);
            }

            _dte = new DynamicTableEntity { PartitionKey = partitionKey, RowKey = rowKey };
            _entity = default(T);

            if (s_builtInTypes.Contains(typeof(T)))
            {
                _dte.Properties.Add(PropertyNameForSimpleEntity, CreateEntityPropertyFromObject(entity));
            }
            else
            {
                foreach (var entityProperty in TypeProperties)
                {
                    object value = entityProperty.PropertyInfo.GetValue(entity);
                    if (entityProperty.IsLargeObject)
                    {
                        StoreLargeObjectInDTE(_dte, entityProperty, value, referenceName);
                    }
                    else if (entityProperty.IsCompressObject)
                    {
                        StoreCompressObjectInDTE(_dte, entityProperty, value, referenceName);
                    }
                    else
                    {
                        if (value != null)
                        {
                            _dte.Properties.Add(entityProperty.PropertyInfo.Name, CreateEntityPropertyFromObject(value));
                        }
                    }
                }
            }
        }

        public DynamicTableEntity GetTableEntity()
        {
            return _dte;
        }

        public T ResolveEntity()
        {
            if (_entity != null && !_entity.Equals(default(T)))
            {
                return _entity;
            }

            T obj = default(T);

            if (s_builtInTypes.Contains(typeof(T)))
            {
                EntityProperty prop;
                if (_dte.Properties.TryGetValue(PropertyNameForSimpleEntity, out prop))
                {
                    return (T)ResolveEntityPropertyForType(prop, typeof(T));
                }
            }
            else
            {
                obj = (T)Activator.CreateInstance(typeof(T));
                foreach (var entityProperty in TypeProperties)
                {
                    if (entityProperty.IsLargeObject)
                    {
                        entityProperty.PropertyInfo.SetValue(obj, ReadLargeObjectFromDTE(_dte, entityProperty));
                    }
                    else if (entityProperty.IsCompressObject)
                    {
                        entityProperty.PropertyInfo.SetValue(obj, ReadCompressObjectFromDTE(_dte, entityProperty));
                    }
                    else
                    {
                        EntityProperty prop;
                        if (_dte.Properties.TryGetValue(entityProperty.PropertyInfo.Name, out prop))
                        {
                            entityProperty.PropertyInfo.SetValue(obj, ResolveEntityPropertyForType(prop, entityProperty.PropertyInfo.PropertyType));
                        }
                        else
                        {
                            entityProperty.PropertyInfo.SetValue(obj, null);
                        }
                    }
                }
            }

            _entity = obj;
            return obj;
        }

        public static IList<string> GetColumnNames()
        {
            return TypeProperties
                .Where(prop => !prop.IsLargeObject).Select(prop => prop.PropertyInfo.Name)
                .Concat(TypeProperties.Where(prop => prop.IsLargeObject).SelectMany(prop =>
                        Enumerable.Range(1, prop.NumSections).Select(i =>prop.PropertyInfo.Name + GetColumnSuffixForLargeObject(i - 1, prop))))
                .ToList();
        }

        private static string GetColumnSuffixForLargeObject(int columnIndex, EntityPropertyInfo entityProperty)
        {
            if (entityProperty.UseDirectNameAsFirstColumn)
            {
                return columnIndex == 0 ? "" : columnIndex.ToString();
            }
            return (columnIndex + 1).ToString();
        }

        private static object ReadLargeObjectFromDTE(DynamicTableEntity dte, EntityPropertyInfo entityProperty)
        {
            var serializedObject = String.Empty;
            var compressedBytesList = new List<byte>();

            for (int i = 0; i < entityProperty.NumSections; i++)
            {
                EntityProperty prop;
                if (dte.Properties.TryGetValue(entityProperty.PropertyInfo.Name + GetColumnSuffixForLargeObject(i, entityProperty), out prop))
                {
                    if (entityProperty.IsCompressObject && prop.PropertyType == EdmType.Binary)
                    {
                        compressedBytesList.AddRange(prop.BinaryValue);
                    }
                    else
                    {
                        serializedObject += prop.StringValue;
                    }
                }
            }

            // If object is stored as Binary type in azure table, it means object is compressed and should be decompressed first.
            if (compressedBytesList.Count > 0)
            {
                var stopWatch = Stopwatch.StartNew();
                serializedObject = CompressObjectHelper.Decompress(compressedBytesList.ToArray());
            }

            return !String.IsNullOrEmpty(serializedObject) ? SerializationHelpers.DeserializeEntity(serializedObject, entityProperty.PropertyInfo.PropertyType) : null;
        }

        private static object ReadCompressObjectFromDTE(DynamicTableEntity dte, EntityPropertyInfo entityProperty)
        {
            EntityProperty prop;
            var serializedObject = String.Empty;

            if (dte.Properties.TryGetValue(entityProperty.PropertyInfo.Name, out prop))
            {
                // For compress object if it is stored as Binary type in azure table, it means it has been compressed when stored it.
                if (prop.PropertyType == EdmType.Binary)
                {
                    var stopWatch = Stopwatch.StartNew();
                    serializedObject = CompressObjectHelper.Decompress(prop.BinaryValue);
                }
                else
                {
                    serializedObject = prop.StringValue;
                }
            }
            else
            {
                return null;
            }

            // Do not need to deserialize string type value to keep consistent with StoreCompressObjectInDTE logic.
            // string type value is not serialized when storing to table.
            return entityProperty.PropertyInfo.PropertyType == typeof(string) ?
                serializedObject :
                !String.IsNullOrEmpty(serializedObject) ? SerializationHelpers.DeserializeEntity(serializedObject, entityProperty.PropertyInfo.PropertyType) : null;
        }

        private static void StoreLargeObjectInDTE(DynamicTableEntity dte, EntityPropertyInfo entityProperty, object value, string referenceName)
        {
            var serializedObject = SerializationHelpers.SerializeEntity(value);

            if (serializedObject.Length > entityProperty.NumSections * MaxStringFieldSize)
            {
                if (!entityProperty.IsCompressObject)
                {
                    throw new TooLargeAzureTableDataException(
                        message: String.Format(
                            TooLargeAzureTableDataMessageFormat,
                            dte.PartitionKey, dte.RowKey, entityProperty.PropertyInfo.Name, serializedObject.Length, entityProperty.NumSections, MaxStringFieldSize),
                        paramName: entityProperty.PropertyInfo.Name,
                        entityName: referenceName);
                }

                // Compress serialized object.
                var stopWatch = Stopwatch.StartNew();
                var compressedBytes = CompressObjectHelper.Compress(serializedObject);
                CommonLogger.LogEntityInfo(dte.RowKey, $"StoreLargeObjectInDTE: have applied compress strategy, before compress serializedObject.Length={serializedObject?.Length}, after compress compressedBytes.Length={compressedBytes.Length}. Azure table key={dte.PartitionKey}, property={entityProperty.PropertyInfo.Name}, propertyNumSections={entityProperty.NumSections}, entityProperty.PropertyInfo.PropertyType={entityProperty.PropertyInfo.PropertyType}, entityProperty.IsCompressObject={entityProperty.IsCompressObject}, TimeElapsedTotalMilliseconds={stopWatch.Elapsed.TotalMilliseconds}");

                if (compressedBytes.Length > entityProperty.NumSections * MaxByteArrayFieldSize)
                {
                    throw new TooLargeAzureTableDataException(
                        message: String.Format(
                            TooLargeAzureTableDataMessageFormat,
                            dte.PartitionKey, dte.RowKey, entityProperty.PropertyInfo.Name, compressedBytes.Length, entityProperty.NumSections, MaxByteArrayFieldSize),
                        paramName: entityProperty.PropertyInfo.Name,
                        entityName: referenceName);
                }

                var compressedBytesChunks = SplitLargeBytesToChunks(compressedBytes, entityProperty.NumSections);
                for (int i = 0; i < entityProperty.NumSections; i++)
                {
                    dte.Properties.Add(entityProperty.PropertyInfo.Name + GetColumnSuffixForLargeObject(i, entityProperty),
                        compressedBytesChunks[i] != null ? new EntityProperty(compressedBytesChunks[i].ToArray()) : new EntityProperty(new byte[0]));
                }
            }
            else
            {
                CommonLogger.LogEntityInfo(dte.RowKey, $"StoreLargeObjectInDTE: have not applied compress strategy, serializedObject.Length={serializedObject?.Length}. Azure table key={dte.PartitionKey}, property={entityProperty.PropertyInfo.Name}, propertyNumSections={entityProperty.NumSections}, entityProperty.PropertyInfo.PropertyType={entityProperty.PropertyInfo.PropertyType}, entityProperty.IsCompressObject={entityProperty.IsCompressObject}");

                var serializedObjectChunks = SplitLargeStringToChunks(serializedObject, entityProperty.NumSections);
                for (int i = 0; i < entityProperty.NumSections; i++)
                {
                    dte.Properties.Add(entityProperty.PropertyInfo.Name + GetColumnSuffixForLargeObject(i, entityProperty),
                        serializedObjectChunks[i] != null ? CreateEntityPropertyFromObject(serializedObjectChunks[i]) : new EntityProperty(String.Empty));
                }
            }
        }

        private static void StoreCompressObjectInDTE(DynamicTableEntity dte, EntityPropertyInfo entityProperty, object value, string referenceName)
        {
            if (value == null)
            {
                return;
            }

            // Do not serialize string type value since we may use table query to filter table entities by its value (like AscendantNodeList).
            // For example, if a raw string value is empty string, after serialization it would be added with more quotation marks, which may effect table query result.
            var serializedObject = entityProperty.PropertyInfo.PropertyType == typeof(string) ? (string)value : SerializationHelpers.SerializeEntity(value);

            // Compress serialized object if its length exceeds max string size limit.
            if (serializedObject.Length > MaxStringFieldSize)
            {
                var stopWatch = Stopwatch.StartNew();
                var compressedBytes = CompressObjectHelper.Compress(serializedObject);
                CommonLogger.LogEntityInfo(dte.RowKey, $"StoreCompressObjectInDTE: have applied compress strategy, before compress serializedObject.Length={serializedObject?.Length}, after compress compressedBytes.Length={compressedBytes.Length}. Azure table key={dte.PartitionKey}, property={entityProperty.PropertyInfo.Name}, propertyNumSections={entityProperty.NumSections}, entityProperty.PropertyInfo.PropertyType={entityProperty.PropertyInfo.PropertyType}, entityProperty.IsCompressObject={entityProperty.IsCompressObject}, TimeElapsedTotalMilliseconds={stopWatch.Elapsed.TotalMilliseconds}");

                if (compressedBytes.Length > MaxByteArrayFieldSize)
                {
                    throw new TooLargeAzureTableDataException(
                        message: String.Format(
                            TooLargeAzureTableDataMessageFormat,
                            dte.PartitionKey, dte.RowKey, entityProperty.PropertyInfo.Name, compressedBytes.Length, entityProperty.NumSections, MaxByteArrayFieldSize),
                        paramName: entityProperty.PropertyInfo.Name,
                        entityName: referenceName);
                }

                dte.Properties.Add(entityProperty.PropertyInfo.Name, new EntityProperty(compressedBytes));
                // Add a property '{PropertyName}IsCompressed' for compressed column.
                // This column would be used when filter table entities by table query on CompressObject value directly.
                dte.Properties.Add(entityProperty.PropertyInfo.Name + CompressObjectHelper.CompressedSuffix, new EntityProperty("true"));
            }
            else
            {
                CommonLogger.LogEntityInfo(dte.RowKey, $"StoreCompressObjectInDTE: have not applied compress strategy, serializedObject.Length={serializedObject?.Length}. Azure table key={dte.PartitionKey}, property={entityProperty.PropertyInfo.Name}, propertyNumSections={entityProperty.NumSections}, entityProperty.PropertyInfo.PropertyType={entityProperty.PropertyInfo.PropertyType}, entityProperty.IsCompressObject={entityProperty.IsCompressObject}");
                dte.Properties.Add(entityProperty.PropertyInfo.Name, CreateEntityPropertyFromObject(serializedObject));
                // If value is not compressed, add false flag to table.
                dte.Properties.Add(entityProperty.PropertyInfo.Name + CompressObjectHelper.CompressedSuffix, new EntityProperty("false"));
            }
        }

        //not clear why this became internal in dotnetcore.
        public static readonly DateTimeOffset MinDateTime = new DateTimeOffset(1601, 1, 1, 0, 0, 0, TimeSpan.Zero);


        private static EntityProperty CreateEntityPropertyFromObject(object value)
        {
            if (value is string)
            {
                return new EntityProperty((string)value);
            }
            if (value is bool)
            {
                return new EntityProperty((bool?)value);
            }
            if (value is DateTime)
            {
                var valueToStore = (DateTime)value;
                if (valueToStore < MinDateTime.DateTime)
                {
                    valueToStore = MinDateTime.DateTime;
                }
                return new EntityProperty(valueToStore);
            }
            if (value is DateTimeOffset)
            {
                var valueToStore = (DateTimeOffset)value;
                if (valueToStore < MinDateTime)
                {
                    valueToStore = MinDateTime;
                }
                return new EntityProperty(valueToStore);
            }
            if (value is double)
            {
                return new EntityProperty(new double?((double)value));
            }
            if (value is Guid)
            {
                return new EntityProperty(new Guid?((Guid)value));
            }
            if (value is int)
            {
                return new EntityProperty(new int?((int)value));
            }
            if (value is long)
            {
                return new EntityProperty(new long?((long)value));
            }

            return new EntityProperty(SerializationHelpers.SerializeEntity(value));
        }

        private object ResolveEntityPropertyForType(EntityProperty property, Type objType)
        {
            try
            {
                // if the field is null Azure SDK returns String property type by default
                if (property.PropertyType == EdmType.String && property.StringValue == null)
                {
                    return null;
                }
                if (objType == typeof(string))
                {
                    return (object)property.StringValue;
                }
                if (objType == typeof(int))
                {
                    return (object)(property.Int32Value.HasValue ? property.Int32Value.Value : default(int));
                }
                if (objType == typeof(int?))
                {
                    return (object)(property.Int32Value.HasValue ? (int?)property.Int32Value.Value : null);
                }
                if (objType == typeof(long))
                {
                    return (object)(property.Int64Value.HasValue ? property.Int64Value.Value : default(long));
                }
                if (objType == typeof(long?))
                {
                    return (object)(property.Int64Value.HasValue ? (long?)property.Int64Value.Value : null);
                }
                if (objType == typeof(double))
                {
                    return (object)(property.DoubleValue.HasValue ? property.DoubleValue.Value : default(double));
                }
                if (objType == typeof(double?))
                {
                    return (object)(property.DoubleValue.HasValue ? (double?)property.DoubleValue.Value : null);
                }
                if (objType == typeof(bool))
                {
                    return (object)(property.BooleanValue.HasValue ? property.BooleanValue.Value : default(bool));
                }
                if (objType == typeof(bool?))
                {
                    return (object)(property.BooleanValue.HasValue ? (bool?)property.BooleanValue.Value : null);
                }
                if (objType == typeof(DateTime))
                {
                    if (!property.DateTimeOffsetValue.HasValue || property.DateTimeOffsetValue.Value <= MinDateTime)
                    {
                        return DateTime.MinValue;
                    }

                    return (object) ReturnDateTimeWithKindFilledIn(property.DateTimeOffsetValue.Value);
                }
                if (objType == typeof(DateTime?))
                {
                    if (!property.DateTimeOffsetValue.HasValue || property.DateTimeOffsetValue.Value <= MinDateTime)
                    {
                        return null;
                    }

                    return (object) ReturnDateTimeWithKindFilledIn(property.DateTimeOffsetValue.Value);
                }
                if (objType == typeof(DateTimeOffset))
                {
                    if (!property.DateTimeOffsetValue.HasValue || property.DateTimeOffsetValue.Value <= MinDateTime)
                    {
                        return DateTimeOffset.MinValue;
                    }

                    return property.DateTimeOffsetValue.Value;
                }
                if (objType == typeof(DateTimeOffset?))
                {
                    if (!property.DateTimeOffsetValue.HasValue || property.DateTimeOffsetValue.Value <= MinDateTime)
                    {
                        return null;
                    }

                    return property.DateTimeOffsetValue;
                }
                if (objType == typeof(Guid))
                {
                    return (object)(property.GuidValue.HasValue ? property.GuidValue.Value : default(Guid));
                }
                if (objType == typeof(Guid?))
                {
                    return (object)(property.GuidValue.HasValue ? (Guid?)property.GuidValue.Value : null);
                }
                if (property.PropertyType == EdmType.String)
                {
                    // try to deserialize object
                    return SerializationHelpers.DeserializeEntity(property.StringValue, objType);
                }
            }
            catch (Exception e)
            {
                Trace.TraceError("Could not resolve build in type {0}, ex: {1}", typeof(T), e.Message);
            }

            return default(T);
        }

        private static DateTime ReturnDateTimeWithKindFilledIn(DateTimeOffset offset)
        {
            if (offset.Offset == TimeSpan.Zero)
            {
                return new DateTime(offset.DateTime.Ticks, DateTimeKind.Utc);
            }
            if (offset.LocalDateTime.Ticks == offset.DateTime.Ticks)
            {
                return new DateTime(offset.DateTime.Ticks, DateTimeKind.Local);
            }
            return offset.DateTime;
        }

        private static bool IsValidTableKey(string key)
        {
            return !AzureTableStorage.DisallowedCharsInTableKeys.IsMatch(key);
        }

        private static string[] SplitLargeStringToChunks(string serializedObject, int targetChunkNumber)
        {
            var serializedObjectChunks = new string[targetChunkNumber];

            var fullChunkNumber = serializedObject.Length / MaxStringFieldSize;
            for (int i = 0; i < fullChunkNumber; i++)
            {
                serializedObjectChunks[i] = serializedObject.Substring(i * MaxStringFieldSize, MaxStringFieldSize);
            }

            if (fullChunkNumber < targetChunkNumber)
            {
                serializedObjectChunks[fullChunkNumber] = serializedObject.Substring(fullChunkNumber * MaxStringFieldSize, serializedObject.Length % MaxStringFieldSize);
            }

            return serializedObjectChunks;
        }

        private static IEnumerable<byte>[] SplitLargeBytesToChunks(byte[] compressedBytes, int targetChunkNumber)
        {
            var compressedBytesChunks = new IEnumerable<byte>[targetChunkNumber];

            var fullChunkNumber = compressedBytes.Length / MaxByteArrayFieldSize;
            for (int i = 0; i < fullChunkNumber; i++)
            {
                compressedBytesChunks[i] = compressedBytes.Skip(i * MaxByteArrayFieldSize).Take(MaxByteArrayFieldSize);
            }

            if (fullChunkNumber < targetChunkNumber)
            {
                compressedBytesChunks[fullChunkNumber] = compressedBytes.Skip(fullChunkNumber * MaxByteArrayFieldSize);
            }

            return compressedBytesChunks;
        }
    }
}
