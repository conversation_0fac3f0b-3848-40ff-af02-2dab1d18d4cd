﻿using System;
using System.Net;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.RelInfra.Instrumentation.Logging;
using Microsoft.WindowsAzure.Storage;

namespace Microsoft.RelInfra.Storage
{
    public class QueueMessageLifetimeManager<T> : IQueueMessageLifetimeManager<T>, IDisposable
    {
        private static readonly TimeSpan ReleaseMessageTimeSpan = TimeSpan.FromSeconds(15);

        private readonly IQueue<T> _queue;
        private Storable<T> _queueMessage;
        private readonly TimeSpan _leaseTime;
        private readonly TimeSpan _releaseMessageTime;

        private Timer _updateTimer;
        private readonly SemaphoreSlim _semaphore = new SemaphoreSlim(initialCount: 1, maxCount: 1);

        // once message is deleted, it can't be used in CheckAndUpdateExpirationAsync()
        private volatile bool _isMessageStillValid;

        public T MessageContent => _queueMessage.Entity;

        public QueueMessageLifetimeManager(IQueue<T> queue, Storable<T> queueMessage, TimeSpan leaseTime, TimeSpan? releaseMessageTime = null)
        {
            _queue = queue;
            _queueMessage = queueMessage;
            _leaseTime = leaseTime;
            _releaseMessageTime = releaseMessageTime ?? ReleaseMessageTimeSpan;
            _updateTimer = new Timer(
                callback: UpdateMessageCycle,
                state: null, 
                dueTime: TimeSpan.FromSeconds(0.6 * _leaseTime.TotalSeconds), 
                period: TimeSpan.FromSeconds(0.6 * _leaseTime.TotalSeconds));
            _isMessageStillValid = true;
        }

        public async Task CheckAndUpdateExpirationAsync()
        {
            if (!_isMessageStillValid)
            {
                return;
            }

            if (_queue.GetMessageLifetime(_queueMessage) > TimeSpan.FromDays(5))
            {
                await StopUpdateCycleAsync();
                try
                {
                    CommonLogger.LogEntityInfo(_queue.QueueName, "Replacing queue message for {queue_message}", _queueMessage.Entity);
                    await _queue.PutAsync(_queueMessage);
                    await DeleteInternalAsync();
                }
                catch (Exception ex)
                {
                    CommonLogger.LogEntityWarning(_queue.QueueName, "Failed to update queue message expiration time, error: {exception}", ex);
                }
            }
        }

        public async Task ReleaseMessageAsync()
        {
            await StopUpdateCycleAsync();
            _queueMessage = await _queue.UpdateMessageAsync(_queueMessage, _releaseMessageTime, MessageUpdateFlags.UpdateVisibility);
        }

        public async Task UpdateMessageVisibilityAsync(TimeSpan leaseTime)
        {
            _queueMessage = await _queue.UpdateMessageAsync(_queueMessage, leaseTime, MessageUpdateFlags.UpdateVisibility);
        }

        public async Task DeleteAsync()
        {
            await StopUpdateCycleAsync();
            await DeleteInternalAsync();
        }

        private async Task DeleteInternalAsync()
        {
            _isMessageStillValid = false;
            await _queue.DeleteAsync(_queueMessage);
        }

        private async Task StopUpdateCycleAsync()
        {
            try
            {
                await _semaphore.WaitAsync();
                StopUpdateCycleUnsafe();
            }
            finally
            {
                _semaphore.Release();
            }
        }
        
        /// <summary>
        /// It requires external locking!
        /// </summary>
        private void StopUpdateCycleUnsafe()
        {
            if (_updateTimer != null)
            {
                _updateTimer.Dispose();
                _updateTimer = null;
            }
        }

        private async void UpdateMessageCycle(object state)
        {
            try
            {
                await _semaphore.WaitAsync();
                if (_updateTimer == null)
                {
                    return;
                }
                _queueMessage = await _queue.UpdateMessageAsync(_queueMessage, _leaseTime, MessageUpdateFlags.UpdateVisibility);
            }
            catch (Exception ex)
            {
                CommonLogger.LogEntityWarning(
                    traceId: _queue.QueueName, 
                    message: "Exception in LeaseTimer delegate (queue message is {queue_message}), error: {exception_message}, stack trace: {stack_trace}", _queueMessage.Entity, ex.Message, ex.StackTrace);

                if (ShouldStopUpdateCycle(ex))
                {
                    StopUpdateCycleUnsafe();
                    CommonLogger.LogEntityWarning(_queue.QueueName, "Update cycle stopped for queue message {queue_message}", _queueMessage.Entity);
                }
            }
            finally
            {
                _semaphore.Release();
            }
        }
        
        private static bool ShouldStopUpdateCycle(Exception e)
        {
            return e is StorageException 
                   && e.InnerException is WebException webException 
                   && webException.Response is HttpWebResponse httpWebResponse
                   && httpWebResponse.StatusCode == HttpStatusCode.NotFound;
        }

        #region IDisposable implementation

        private bool _isDisposed = false;

        public void Dispose()
        {
            Dispose(true);
        }

        private void Dispose(bool disposing)
        {
            if (!_isDisposed && disposing)
            {
                // free managed resources
                if (_updateTimer != null)
                {
                    _updateTimer.Dispose();
                    _updateTimer = null;
                }
                _isDisposed = true;
            }
        }
        #endregion IDisposable

    }
}
