﻿using System;
using System.Collections.Generic;
using Microsoft.AIPlatform.WorkloadIdentity;
using Microsoft.RelInfra.Instrumentation;

namespace Microsoft.RelInfra.Storage
{
    public class StorageFactoryV2 : IStorageFactory
    {
        private readonly IWorkloadIdentityProvider _workloadIdentityProvider;
        private readonly string _connectionString;
        private readonly CounterManager _counterManager;
        private readonly int _numberOfRetries;
        private readonly TimeSpan _intervalBetweenRetries;
        private readonly string _storageAccountName;
        private readonly IDictionary<StorageTypes, string> _storageEndpoints;

        public static readonly int DefaultNumberOfRetries = 5;
        public static readonly TimeSpan DefaultIntervalBetweenRetries = TimeSpan.FromSeconds(2);

        public StorageFactoryV2() :
            this(azureConnectionString: null, counterManager: null)
        {
        }

        public StorageFactoryV2(
            IWorkloadIdentityProvider workloadIdentityProvider,
            string storageAccountName,
            IDictionary<StorageTypes, string> storageEndpoints)
            : this(workloadIdentityProvider,
                   storageAccountName: storageAccountName,
                   storageEndpoints: storageEndpoints,
                   counterManager: null)
        {
        }

        public StorageFactoryV2(
            IWorkloadIdentityProvider workloadIdentityProvider,
            string storageAccountName,
            IDictionary<StorageTypes, string> storageEndpoints,
            CounterManager counterManager = null)
            : this(workloadIdentityProvider,
                   storageAccountName: storageAccountName,
                   storageEndpoints: storageEndpoints,
                   connectionString: null,
                   counterManager: counterManager,
                   numberOfRetries: DefaultNumberOfRetries,
                   intervalBetweenRetries: DefaultIntervalBetweenRetries)
        {
        }

        public StorageFactoryV2(string azureConnectionString, CounterManager counterManager = null)
            : this(workloadIdentityProvider: null,
                   storageAccountName: null,
                   storageEndpoints: null,
                   connectionString: azureConnectionString,
                   counterManager: counterManager,
                   numberOfRetries: DefaultNumberOfRetries,
                   intervalBetweenRetries: DefaultIntervalBetweenRetries)
        {
        }

        protected StorageFactoryV2(
            IWorkloadIdentityProvider workloadIdentityProvider,
            string storageAccountName,
            IDictionary<StorageTypes, string> storageEndpoints,
            string connectionString,
            CounterManager counterManager, 
            int numberOfRetries, 
            TimeSpan intervalBetweenRetries)
        {
            _workloadIdentityProvider = workloadIdentityProvider;
            _storageAccountName = storageAccountName;
            _storageEndpoints = storageEndpoints;
            _connectionString = connectionString;
            _counterManager = counterManager;
            _numberOfRetries = numberOfRetries;
            _intervalBetweenRetries = intervalBetweenRetries;
        }

        public IStorage GetStorage(StorageTypes storageType)
        {
            return GetStorage(storageType, String.Empty);
        }

        public IStorage GetStorage(StorageTypes storageType, string storageName)
        {
            return GetStorageInternal(storageType, storageName, _connectionString);
        }

        public IStorage GetStorage(StorageTypes storageType, string storageName, string azureConnectionString)
        {
            if (string.IsNullOrEmpty(azureConnectionString))
            {
                throw new ArgumentNullException(nameof(azureConnectionString));
            }

            return GetStorageInternal(storageType, storageName, azureConnectionString);
        }

        private IStorage GetStorageInternal(StorageTypes storageType, string storageName, string azureConnectionString)
        {
            if (_workloadIdentityProvider?.IsAvailable == true)
            {
                var uri = new Uri($"https://{_storageAccountName}{_storageEndpoints[storageType]}");
                switch (storageType)
                {
                    case StorageTypes.AzureTables:
                        return new AzureTableStorageV2(
                            storageAccountUri: uri,
                            credential: _workloadIdentityProvider.Credential,
                            tableName: storageName,
                            counterManager: _counterManager);
                    case StorageTypes.AzureBlob:
                        return new AzureBlobStorageV2(
                            serviceUri: uri,
                            credential: _workloadIdentityProvider.Credential,
                            numRetries: _numberOfRetries,
                            intervalBetweenRetries: _intervalBetweenRetries,
                            counterManager: _counterManager);

                    default:
                        throw new ArgumentOutOfRangeException(nameof(storageType));
                }
            }
            else
            {
                switch (storageType)
                {
                    case StorageTypes.AzureTables:
                        return new AzureTableStorageV2(
                            connectionString: azureConnectionString,
                            tableName: storageName,
                            counterManager: _counterManager);
                    case StorageTypes.AzureBlob:
                        return new AzureBlobStorageV2(
                            connectionString: azureConnectionString,
                            numRetries: _numberOfRetries,
                            intervalBetweenRetries: _intervalBetweenRetries,
                            counterManager: _counterManager);

                    default:
                        throw new ArgumentOutOfRangeException(nameof(storageType));
                }
            }
        }

        public IQueue<T> GetQueue<T>(string queueName)
        {
            if (_workloadIdentityProvider?.IsAvailable == true)
            {
                var uri = new Uri($"https://{_storageAccountName}{_storageEndpoints[StorageTypes.AzureQueue]}");

                return new AzureQueueV2<T>(
                    serviceUri: uri,
                    queueName: queueName,
                    credential: _workloadIdentityProvider.Credential);
            }
            else
            {
                return new AzureQueueV2<T>(_connectionString, queueName);
            }
        }
    }
}
