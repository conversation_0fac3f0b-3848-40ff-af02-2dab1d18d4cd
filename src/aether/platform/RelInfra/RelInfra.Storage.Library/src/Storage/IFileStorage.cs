﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Threading;
using System.Threading.Tasks;

namespace Microsoft.RelInfra.Storage
{
    public interface IFileStorage
    {
        Task<bool> CopyFileAsync(string sourcePath, string destPath, CancellationToken cts);
        Task<bool> DeleteFileAsync(string path);
        Task<bool> FileExistsAsync(string path);
        Task<bool> FileExistsAsync(string path, bool allowIncomplete);
        Task<bool> IsDirectoryAsync(string path);
        Task<IEnumerable<string>> ListDirectoryAsync(string path, bool recursive, uint fileCountLimit = 20000);
        Task<bool> CreateFileAsync(string path, string content);
        Task<bool> AppendLineAsync(string path, string line);
        Task<long> GetFileLengthAsync(string path);
        Task<long> GetDirectorySizeAsync(string path);
        Task<Stream> ReadStreamAsync(string path, long offset, long length);
        Task<bool> AppendAsync(string path, string text, bool compression);
        Task<bool> CreateFileAsync(string path, string content, TimeSpan expirationTime);
        Task CreateDirectoryAsync(string path);
        Task<string> ReadAllTextAsync(string path);
    }
}