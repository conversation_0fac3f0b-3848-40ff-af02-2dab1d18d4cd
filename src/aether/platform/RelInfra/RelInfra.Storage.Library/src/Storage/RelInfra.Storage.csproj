<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <RootNamespace>Microsoft.RelInfra.Storage</RootNamespace>
    <AssemblyName>Microsoft.RelInfra.Storage</AssemblyName>
    <TargetFramework>net6.0</TargetFramework>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <ResolveAssemblyWarnOrErrorOnTargetArchitectureMismatch>None</ResolveAssemblyWarnOrErrorOnTargetArchitectureMismatch>
    <PackageVersion>7.1.62</PackageVersion>
    <PackageDescription>Various storage utilities and helper classes - Azure, Cosmos and File storage for Relevance Infrastructure teams</PackageDescription>
    <PackageId>RelInfra.Storage.Library</PackageId>
    <QInstrumentForCoverage>false</QInstrumentForCoverage>
  </PropertyGroup>
  <Import Project="..\..\..\..\nuget.props" />
  <ItemGroup>
    <ProjectReference Include="..\..\..\RelInfra.Common.Library\src\Common\RelInfra.Common.csproj" />
    <ProjectReference Include="..\..\..\RelInfra.Common.Library\src\Extensions\RelInfra.Extensions.csproj" />
    <ProjectReference Include="..\..\..\RelInfra.Instrumentation.Library\src\Microsoft.RelInfra.Instrumentation.csproj" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="WindowsAzure.Storage" />
    <PackageReference Include="Azure.Storage.Blobs" />
    <PackageReference Include="Azure.Storage.Queues" />
    <PackageReference Include="Azure.Data.Tables" />
  </ItemGroup>
</Project>
