﻿using System;
using System.Linq;
using System.Threading.Tasks;

namespace Microsoft.RelInfra.Storage
{
    public class TableStorageReplicator<T>
    {
        private readonly ITableStorage _source;
        private readonly ITableStorage _destination;

        public TableStorageReplicator(ITableStorage source, ITableStorage destination)
        {
            _source = source;
            _destination = destination;
        }

        public bool DeleteDestinationBeforeCopy { get; set; }

        public async Task<long> ReplicateAsync()
        {
            return await ReplicateAsync(null);
        }

        public async Task<long> ReplicateAsync(IProgress<long> progress)
        {
            // delete destination if requested
            if (DeleteDestinationBeforeCopy)
            {
                await _destination.DeleteIfExistsAsync();
            }

            // create destination
            await _destination.CreateIfNotExistsAsync();

            string continuationToken = null;
            QueryResults<T> results;
            long replicationCount = 0;
            do
            {
                // fetch next batch from source
                results = await _source.GetAllWithContinuationTokenAsync<T>(continuationToken);
                continuationToken = results.ContinuationToken;

                // persist batch to destination
                await _destination.InsertOrReplaceAsync(results.Results.Select(r =>
                {
                    r.Etag = "*";
                    return r;
                }));
                long lastCount = results.Results.Count();
                replicationCount += lastCount;
                if (progress != null)
                {
                    progress.Report(lastCount);
                }
            }
            while (continuationToken != null);

            return replicationCount;
        }
    }
}
