﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Threading;
using System.Threading.Tasks;

namespace Microsoft.RelInfra.Storage
{
    public interface IAzureBlobContainerStorage
    {
        /// <summary>
        ///   Initialize container
        /// </summary>
        /// <returns></returns>
        Task InitializeAsync();
        
        /// <summary>
        /// Asynchronously retrieves the resource with given Id from the container in underlying storage.
        /// Returns null if it fails to get item.
        /// </summary>
        /// <param name="resourceId"></param>
        /// <param name="cancellationToken"></param>
        Task<Storable<T>> TryGetAsync<T>(string resourceId, CancellationToken cancellationToken);

        /// <summary>
        ///    Get blob with given resourceId
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="resourceId"></param>
        /// <returns></returns>
        Task<Storable<T>> GetAsync<T>(string resourceId);

        /// <summary>
        /// Asynchronously retrieves resources with given Ids from the specified container in underlying storage.
        /// Throws RelInfraStorageException if it fails.
        /// </summary>
        /// <typeparam name="T">Type of object associated with resource</typeparam>
        /// <param name="resourceIds">Collection of id of resource to retrieve (can be RowKey for tables, or stream name for Cosmos storage)</param>
        /// <returns>List of instance of <typeparamref name="T"/></returns>
        Task<IEnumerable<Storable<T>>> BatchGetAsync<T>(IEnumerable<string> resourceIds);

        /// <summary>
        /// Asynchronously append resource as a string to a given resource Id to the container in underlying storage
        /// </summary>
        /// <param name="resourceId">Id of the resource to which the resource as a string is appended</param>
        /// <param name="resource"></param>
        Task<Storable<string>> AppendAsync<T>(string resourceId, T resource);

        /// <summary>
        /// GetBlobEntityAsync<string> run Newton soft json parser to read string which is more strict than just using string directly using GetBlobStringAsync. 
        /// Real example: There is a blob with few KBs of zero bytes, which broke GetBlobEntityAsync<string> but GetBlobStringAsync() is working fine
        /// </summary>
        /// <param name="resourceId">Id of the resource to which the resource as a string is appended</param>
        Task<Storable<T>> GetBlobEntityAsync<T>(string resourceId);

        /// <summary>
        /// GetBlobEntityAsync<string> run Newton soft json parser to read string which is more strict than just using string directly using GetBlobStringAsync. 
        /// Real example: There is a blob with few KBs of zero bytes, which broke GetBlobEntityAsync<string> but GetBlobStringAsync() is working fine
        /// </summary>
        /// <param name="resourceId">Id of the resource to which the resource as a string is appended</param>
        /// <param name="cancellationToken"></param>
        Task<Storable<T>> GetBlobEntityAsync<T>(string resourceId, CancellationToken cancellationToken);

        /// <summary>
        /// Return the metadata value associated with this blob with the given key, or null if that metadata does not exist
        /// </summary>
        /// <param name="resourceId">Id of the resource</param>
        /// <param name="metadataKey">The key to fetch the value for</param>
        Task<string> GetBlobMetadataAsync(string resourceId, string metadataKey);

        /// <summary>
        /// Add the given (key, value) metadata to this blob
        /// </summary>
        /// <param name="resourceId">Id of the resource</param>
        /// <param name="metadataKey">The key to fetch the value for</param>
        /// <param name="metadataValue">The value to fetch the value for</param>
        Task SetBlobMetadataAsync(string resourceId, string metadataKey, string metadataValue);

        /// <summary>
        /// GetBlobStringAsync() reads string from blob and doesn't attempt to deserialize it
        /// </summary>
        /// <param name="resourceId">Id of the resource to which the resource as a string is appended</param>
        Task<Storable<string>> GetBlobStringAsync(string resourceId);

        /// <summary>
        /// GetBlobStringAsync() reads string from blob and doesn't attempt to deserialize it
        /// </summary>
        /// <param name="resourceId">Id of the resource to which the resource as a string is appended</param>
        /// <param name="cancellationToken"></param>
        Task<Storable<string>> GetBlobStringAsync(string resourceId, CancellationToken cancellationToken);

        /// <summary>
        /// GetBlobUri() returns Uri to blob in underlying storage
        /// </summary>
        /// <param name="blobName">Blob name in container</param>
        /// <param name="expiryDate">Expiration date of the resulting Uri. If specified then Uri creates with Read-Only access</param>
        Uri GetBlobUri(string blobName, DateTimeOffset? expiryDate = null);

        /// <summary>
        /// GetContainerReadUri() returns Uri to container in underlying storage. 
        /// </summary>
        /// <param name="expiryDate">Expiration date of the resulting Uri. If specified then Uri creates with Read & List Only access</param>
        Uri GetContainerReadUri(DateTimeOffset? expiryDate = null);

        /// <summary>
        /// Returns a SAS Uri of the blob that has read/write permissions.
        /// </summary>
        /// <param name="blobName"></param>
        /// <param name="expiryDate"></param>
        /// <returns></returns>
        Uri GetWritableBlobUri(string blobName, DateTimeOffset expiryDate);

        /// <summary>
        /// Asynchronously uploads the content of the resource with given Id to container in underlying storage
        /// </summary>
        /// <param name="resourceId">Id of the given resource</param>
        /// <param name="stream">Stream to upload</param>
        /// <param name="contentType">content type of the blob</param>
        /// <returns>true if added successfully, false otherwise</returns>
        Task<bool> TryUploadStreamWithTypeAsync(string resourceId, Stream stream, string contentType);

        /// <summary>
        ///    Enumerates all blobs with given path prefix
        /// </summary>
        /// <param name="path"></param>
        /// <param name="fileCountLimit"></param>
        /// <param name="cancellationToken"></param>
        /// <param name="prefixFilter">If null, no filter applied. Otherwise output collection is filtered based on prefix</param>
        /// <returns></returns>
        Task<IEnumerable<string>> ListDirectoryAsync(string path, int fileCountLimit, CancellationToken cancellationToken, string prefixFilter);

        /// <summary>
        ///    Enumerates all blobs with given path prefix
        /// </summary>
        /// <param name="path"></param>
        /// <param name="continuationToken"></param>
        /// <param name="takeCount"></param>
        /// <param name="cancellationToken"></param>
        /// <param name="useFlatBlobListing"></param>
        /// <returns></returns>
        Task<QueryResults> ListDirectoryWithContinuationTokenAsync(string path, string continuationToken, int takeCount, CancellationToken cancellationToken, bool useFlatBlobListing = true);

        /// <summary>
        ///    Does PUT with etag check
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="resourceId"></param>
        /// <param name="resource"></param>
        /// <returns></returns>
        Task<Storable<T>> PutWithETagAsync<T>(string resourceId, Storable<T> resource);

        /// <summary>
        ///    Tries to download blob to file location
        /// </summary>
        /// <param name="blobName"></param>
        /// <param name="fileLocation"></param>
        /// <param name="cancellationToken"></param>
        Task DownloadToFileAsync(string blobName, string fileLocation, CancellationToken cancellationToken);

        /// <summary>
        ///    Tries to upload file to blob
        /// </summary>
        /// <param name="blobName"></param>
        /// <param name="fileLocation"></param>
        /// <param name="cancellationToken"></param>
        Task UploadFileAsync(string blobName, string fileLocation, CancellationToken cancellationToken);

        /// <summary>
        /// Gets Blob size in bytes
        /// </summary>
        Task<long> GetBlobSizeAsync(string resourceId);

        /// <summary>
        ///   Put resource into blob container
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="resourceId"></param>
        /// <param name="resource"></param>
        /// <returns></returns>
        Task<Storable<T>> PutAndIgnoreEtagAsync<T>(string resourceId, Storable<T> resource);

        /// <summary>
        /// Asynchronously delete resource with given Id in the specified container in underlying storage.
        /// </summary>
        /// <param name="resourceId">Id of the given resource (can be RowKey for tables, or stream name for Cosmos storage)</param>
        /// <returns>true if added successfully, false otherwise</returns>
        Task<bool> TryDeleteAsync(string resourceId);
        
        Task TryPutAndIgnoreEtagAsync(string blobName, Storable<string> storable);


        /// <summary>
        /// 
        /// </summary>
        /// <param name="blobName"></param>
        /// <param name="leaseId">Lease to renew</param>
        Task RenewLeaseAsync(string blobName, string leaseId, CancellationToken cancellationToken);

        /// <summary>
        /// Acquires a lease
        /// 
        /// </summary>
        /// <param name="blobName"></param>
        /// <param name="leaseTime">A TimeSpan representing the span of time for which to acquire the lease,
        ///             which will be rounded down to seconds. If null, an infinite lease will be acquired. If not null, this must be
        ///             greater than zero.</param>
        /// <param name="proposedLeaseId">A string representing the proposed lease ID for the new lease, or null if no lease ID is proposed.</param>
        /// <returns>
        /// The ID of the acquired lease.
        /// </returns>
        Task<string> AcquireLeaseAsync(string blobName, TimeSpan? leaseTime, string proposedLeaseId, CancellationToken cancellationToken);

        Task<bool> ResourceExistsAsync(string blobName);

        /// <summary>
        /// Releases the lease on this blob.
        /// 
        /// </summary>
        /// <param name="blobName"></param>
        /// <param name="leaseId">Lease to release</param>
        Task ReleaseLeaseAsync(string blobName, string leaseId, CancellationToken cancellationToken);

        Task DeleteAsync(string resourceId);

    }
}
