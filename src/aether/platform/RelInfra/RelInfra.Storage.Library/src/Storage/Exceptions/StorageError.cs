﻿using System.Net;

namespace Microsoft.RelInfra.Storage.Exceptions
{
    public enum StorageError
    {
        UndefinedError,
        NotFound,
        Conflict,
        PreconditionFailed,
        TooManyRequests,
        Forbidden,
        Unauthorized,
        ServiceUnavailable,
        RequestEntityTooLarge,
        BadRequest,
    }

    public class StorageErrorHelper
    {
        public static StorageError GetError(int httpStatusCode)
        {
            switch (httpStatusCode)
            {
                case (int)HttpStatusCode.NotFound:
                    return StorageError.NotFound;

                case (int)HttpStatusCode.Conflict:
                    return StorageError.Conflict;

                case (int)HttpStatusCode.PreconditionFailed:
                    return StorageError.PreconditionFailed;

                case 429:
                    return StorageError.TooManyRequests;

                case (int)HttpStatusCode.Forbidden:
                    return StorageError.Forbidden;

                case (int)HttpStatusCode.Unauthorized:
                    return StorageError.Unauthorized;

                case (int)HttpStatusCode.ServiceUnavailable:
                    return StorageError.ServiceUnavailable;

                case (int)HttpStatusCode.RequestEntityTooLarge:
                    return StorageError.RequestEntityTooLarge;

                case (int)HttpStatusCode.BadRequest:
                    return StorageError.BadRequest;

                default:
                    return StorageError.UndefinedError;
            }
        }

        public static HttpStatusCode GetHttpStatusCode(StorageError error)
        {
            switch (error)
            {
                case StorageError.NotFound:
                    return HttpStatusCode.NotFound;

                case StorageError.Conflict:
                    return HttpStatusCode.Conflict;

                case StorageError.PreconditionFailed:
                    return HttpStatusCode.PreconditionFailed;

                case StorageError.TooManyRequests:
                    return (HttpStatusCode)429;

                case StorageError.Forbidden:
                    return HttpStatusCode.Forbidden;

                case StorageError.Unauthorized:
                    return HttpStatusCode.Unauthorized;

                case StorageError.ServiceUnavailable:
                    return HttpStatusCode.ServiceUnavailable;

                case StorageError.RequestEntityTooLarge:
                    return HttpStatusCode.RequestEntityTooLarge;

                case StorageError.BadRequest:
                    return HttpStatusCode.BadRequest;

                default:
                    return HttpStatusCode.InternalServerError;
            }
        }
    }
}
