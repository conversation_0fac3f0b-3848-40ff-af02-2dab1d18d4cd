﻿using Microsoft.RelInfra.Common.Exceptions;
using Microsoft.RelInfra.Extensions;
using System;
using System.Net;
using Microsoft.WindowsAzure.Storage;


namespace Microsoft.RelInfra.Storage.Exceptions
{
    /// <summary>
    /// Supports creating exception interpreters to map exception -> status code
    /// </summary>
    public class StorageExceptionHandler : BaseExceptionHandler
    {
        /// <inheritdoc/>
        public override HttpStatusCode GetStatusCode(Exception exception)
        {
            HttpStatusCode statusCode = base.GetStatusCode(exception);
            if (statusCode == DefaultStatusCode)
            {
                switch (exception)
                {
                    case EtagMismatchException _:
                        return HttpStatusCode.Conflict;
                    case RelInfraStorageException relInfraStorageException:
                        return StorageErrorHelper.GetHttpStatusCode(relInfraStorageException.Error);
                    case StorageException storageException:
                        // For StorageException, if we could find the status code from the request information, we return such status code;
                        // Otherwise we return the default status code.
                        var exStatusCode = storageException.RequestInformation?.HttpStatusCode;
                        return Enum.IsDefined(typeof(HttpStatusCode), exStatusCode) ? (HttpStatusCode)exStatusCode : statusCode;
                }
            }
            return statusCode;
        }

        public override Exception FlattenException(Exception exception)
        {
            exception = base.FlattenException(exception);
            exception = TryParseTooManyRequests(exception);
            return exception;
        }


        public static Exception TryParseTooManyRequests(Exception exception)
        {
            var tooManyRequestsException = GetInnerExceptionByType<TooManyRequestsException>(exception);
            if (tooManyRequestsException != null)
            {
                // Here we throw this exception since CustomErrorResponseExceptionFilter leverages vienna ExceptionInterpretationSelector to extract RetryAfter from this exception and set to the final http response.
                return new MachineLearning.Common.WebApi.Exceptions.TooManyRequestsException((int)tooManyRequestsException.RetryAfterSeconds, exception);
            }

            return exception;
        }

        private static T GetInnerExceptionByType<T>(Exception exception) where T: Exception
        {
            while (exception != null && !(exception is T))
            {
                exception = exception.InnerException;
            }

            return exception as T;
        }
    }
}
