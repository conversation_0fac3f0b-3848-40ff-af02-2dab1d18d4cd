﻿using Microsoft.AIPlatform.Telemetry.Contracts.Logging.PrivacyAware;
using System;

namespace Microsoft.RelInfra.Storage.Exceptions
{
    public class RelInfraStorageException : Exception
    {
        [PrivacyClassification(PrivacyType.SystemMetadata)]
        public StorageError Error { get; set; }

        public RelInfraStorageException(StorageError error = StorageError.UndefinedError)
        {
            Error = error;
        }

        public RelInfraStorageException(string message, StorageError error = StorageError.UndefinedError)
            : base(message)
        {
            Error = error;
        }

        public RelInfraStorageException(string message, Exception innerException, StorageError error = StorageError.UndefinedError)
            : base(message, innerException)
        {
            Error = error;
        }
    }
}
