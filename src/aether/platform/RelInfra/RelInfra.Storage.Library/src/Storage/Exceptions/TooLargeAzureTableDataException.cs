﻿using System;

namespace Microsoft.RelInfra.Storage.Exceptions
{
    public class TooLargeAzureTableDataException: ArgumentException
    {
        public string EntityName { get; set; }

        public TooLargeAzureTableDataException(string message, string paramName, string entityName)
            : base(message, paramName)
        {
            EntityName = entityName;
        }

        public TooLargeAzureTableDataException(string message, string paramName, string entityName, Exception innerException)
            : base(message, paramName, innerException)
        {
            EntityName = entityName;
        }
    }
}
