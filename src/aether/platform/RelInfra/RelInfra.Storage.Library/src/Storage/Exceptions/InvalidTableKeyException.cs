﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Microsoft.RelInfra.Storage.Exceptions
{
    public class InvalidTableKeyException : ArgumentException
    {
        public InvalidTableKeyException(string message, string paramName, string source)
            : base(message, paramName)
        {
            Source = source;
        }

        public InvalidTableKeyException(string message, string paramName, string source, Exception innerException)
            : base(message, paramName, innerException)
        {
            Source = source;
        }
    }
}
