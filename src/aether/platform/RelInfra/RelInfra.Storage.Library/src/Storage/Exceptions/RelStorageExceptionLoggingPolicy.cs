﻿using Microsoft.RelInfra.Common.Exceptions;
using Microsoft.RelInfra.Extensions;
using Microsoft.Rest;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;

namespace Microsoft.RelInfra.Storage.Exceptions
{
    /// <inheritdoc/>
    public class RelStorageExceptionLoggingPolicy : RelCommonExceptionLoggingPolicy
    {
        /*
         * =============================================================================================
         * NOTE: if you can add exceptions to the base class (without adding a lot of extra dependencies),
         * then please do it there so the change can be more widely used. Similarly, if you can't add the
         * excpetion to this class, then you can check this classes subclass (if present).
         * =============================================================================================
         */

        /// <inheritdoc/>
        protected override ReadOnlyDictionary<Type, Func<Exception, Dictionary<string, string>>> LoggablePropertiesDict { get; }

        public RelStorageExceptionLoggingPolicy()
        {
            Dictionary<Type, Func<Exception, Dictionary<string, string>>> loggableProperties = new Dictionary<Type, Func<Exception, Dictionary<string, string>>>()
            {
                // NOTE: if an exception type isn't in this map, but it's super-type is, then we will use the super-types mapping
                // NOTE: if values of the dict returned by these functions are null then they will be automatically ignored from the final string (see PropertiesToString)
                { typeof(ValidationException), ex =>  {
                    var valEx = ex as ValidationException;
                    return  new Dictionary<string, string>() { { "Rule", valEx.Rule }, { "Target", valEx.Target } };
                }}
            };
            LoggablePropertiesDict = BuildLoggableProperties(base.LoggablePropertiesDict, loggableProperties);
        }
    }
}
