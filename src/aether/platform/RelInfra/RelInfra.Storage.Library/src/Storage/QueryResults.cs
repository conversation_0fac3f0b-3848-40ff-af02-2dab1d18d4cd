﻿using System.Collections.Generic;

namespace Microsoft.RelInfra.Storage
{
    public class QueryResults<T>
    {
        public QueryResults(string continuationToken, IEnumerable<Storable<T>> results)
        {
            IsStorable = true;
            ContinuationToken = continuationToken;
            Results = results;
        }

        public QueryResults(string continuationToken, IEnumerable<T> results)
        {
            IsStorable = false;
            ContinuationToken = continuationToken;
            NonStorableResults = results;
        }

        public bool IsStorable { get; }
        public string ContinuationToken { get; }
        public IEnumerable<Storable<T>> Results { get; }
        public IEnumerable<T> NonStorableResults { get; }
    }

    public class QueryResults
    {
        public QueryResults(string continuationToken, IEnumerable<string> results)
        {
            ContinuationToken = continuationToken;
            Results = results;
        }
        public string ContinuationToken { get; }
        public IEnumerable<string> Results { get; }
    }
}
