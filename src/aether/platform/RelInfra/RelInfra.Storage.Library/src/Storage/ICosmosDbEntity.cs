﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Text;

namespace Microsoft.RelInfra.Storage
{
    // all documents must provide case sensitive "id" property in order to be added into a cosmos db collection.
    // in order to keep most of our contracts untouched, I did a trick in AzureCosmosDbCollection implementation,
    // I converted incoming contracts into an expando object and added "id" and "PartitionKey" fields. 
    // To avoid the serialization/deserialization time spent on the padding, use AzureCosmosDbCollectionDbApi with ICosmosDbEntity, 
    // the documents are expected provide "id" by itself, and partitionkey definition.
    public interface ICosmosDbEntity
    {
        [JsonProperty(PropertyName = "id")]
        string Id { get; set; }
        string GetPartitionKey();
    }
}
