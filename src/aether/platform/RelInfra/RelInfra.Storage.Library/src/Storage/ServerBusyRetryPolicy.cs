﻿using System;
using Microsoft.RelInfra.Common.RetryPolicy;
using Microsoft.WindowsAzure.Storage;
using Microsoft.WindowsAzure.Storage.Shared.Protocol;

namespace Microsoft.RelInfra.Storage
{
    public sealed class ServerBusyRetryPolicy : ExponentialBackoffRetryPolicy
    {
        public override bool ShouldRetry(int currentRetryCount, int statusCode, Exception lastException, out TimeSpan retryInterval)
        {
            retryInterval = TimeSpan.Zero;

            if (lastException is AggregateException)
            {
                lastException = lastException.GetBaseException();
            }

            var serverException = lastException as StorageException;
            if (serverException != null)
            {
                if (serverException.RequestInformation != null &&
                    serverException.RequestInformation.ExtendedErrorInformation != null &&
                    serverException.RequestInformation.ExtendedErrorInformation.ErrorCode == StorageErrorCodeStrings.ServerBusy)
                {
                    return base.ShouldRetry(currentRetryCount, statusCode, lastException, out retryInterval);
                }
            }
            return false;
        }

        public override object Clone()
        {
            return new ServerBusyRetryPolicy();
        }
    }
}
