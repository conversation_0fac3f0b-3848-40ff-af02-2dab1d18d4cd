﻿using System.IO;
using System.Threading.Tasks;

namespace Microsoft.RelInfra.Storage
{
    public interface IStreamableStorage
    {
        /// <summary>
        /// Asynchronously retrieves the content of the resource with given Id from the specified container in underlying storage
        /// </summary>
        /// <param name="containerName">Container name from which the resource is retrieved </param>
        /// <param name="resourceId">Id of the given resource</param>
        /// <returns>Stream</returns>
        Task<Stream> GetStreamAsync(string containerName, string resourceId);

        /// <summary>
        /// Asynchronously uploads the content of the resource with given Id to the specified container in underlying storage
        /// </summary>
        /// <param name="containerName">Container name from which the resource is retrieved</param>
        /// <param name="resourceId">Id of the given resource</param>
        /// <param name="stream">Stream to upload</param>
        /// <returns>true if added successfully, false otherwise</returns>
        Task UploadStreamAsync(string containerName, string resourceId, Stream stream);

        /// <summary>
        /// Asynchronously uploads the content of the resource with given Id to the specified container in underlying storage
        /// </summary>
        /// <param name="containerName">Container name from which the resource is retrieved</param>
        /// <param name="resourceId">Id of the given resource</param>
        /// <param name="stream">Stream to upload</param>
        /// <returns>true if added successfully, false otherwise</returns>
        Task<bool> TryUploadStreamAsync(string containerName, string resourceId, Stream stream);

        /// <summary>
        /// Asynchronously appends the content of the resource with given Id to the specified container in underlying storage. Throws exception on failure.
        /// </summary>
        /// <param name="containerName">Container name from which the resource is retrieved</param>
        /// <param name="resourceId">Id of the given resource</param>
        /// <param name="dataStream">Stream to upload</param>
        /// <returns>Returns void but throws exception on failure</returns>
        Task AppendStreamAsync(string containerName, string resourceId, Stream dataStream);
    }
}
