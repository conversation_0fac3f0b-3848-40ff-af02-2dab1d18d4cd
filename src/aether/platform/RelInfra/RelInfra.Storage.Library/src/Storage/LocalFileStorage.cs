﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.RelInfra.Common.Exceptions;
using Microsoft.RelInfra.Common.RetryPolicy;
using Microsoft.RelInfra.Common.Serialization;
using Microsoft.RelInfra.Instrumentation.Exceptions;
using Microsoft.RelInfra.Storage.Exceptions;

namespace Microsoft.RelInfra.Storage
{
    public class LocalFileStorage : IFileStorage
    {
        public IRelInfraRetryPolicy RetryPolicy{ get ; set; }

        private const int DefaultMaxAttempts = 3;
        private readonly string _basePath;

        public LocalFileStorage(string basePath, IRelInfraRetryPolicy retryPolicy = null)
        {
            _basePath = basePath;
            RetryPolicy = retryPolicy;
            if (RetryPolicy == null)
            {
                RetryPolicy = new FileRetryPolicy(maxAttempts: DefaultMaxAttempts);
            }
        }

        public Task<bool> ExistsAsync(string containerName, string resourceId)
        {
            string path = CreateFilePath(containerName: containerName, resourceId: resourceId);
            return Task.FromResult(File.Exists(path));
        }

        private string CreateFilePath(string containerName, string resourceId)
        {
            string containerPath = Path.Combine(_basePath, containerName);
            if (String.IsNullOrEmpty(resourceId))
            {
                return containerPath;
            }
                
            return Path.Combine(containerPath, resourceId);
        }

        public async Task<Storable<T>> AddAsync<T>(string containerName, string resourceId, Storable<T> resource)
        {
            string path = CreateFilePath(containerName: containerName, resourceId: resourceId);
            string serializedObj = SerializationHelpers.SerializeEntity(resource.Entity);
            await RetryExecution.ExecuteWithRetryAsync(
                (filePath, serializedRes) =>
                {
                    Directory.CreateDirectory(CreateFilePath(containerName, resourceId: null));
                    if (File.Exists(filePath))
                    {
                        throw new RelInfraStorageException("File {user_file} already exists", StorageError.Conflict).Format(filePath);
                    }
                    File.WriteAllText(path: filePath, contents: serializedObj);
                    return Task.FromResult(File.Exists(filePath));
                },
                path, serializedObj,
                (IOException ex) =>
                {
                    Trace.TraceError("Cannot write resource to file {0} : {1}", path, ex);
                    throw new RelInfraStorageException("Cannot write resource to file {user_file}", ex).Format(path);
                },
                RetryPolicy);
            return resource;
        }

        public Task<bool> TryPutAsync<T>(string containerName, string resourceId, Storable<T> resource)
        {
            throw new NotImplementedException();
        }

        public Task<Storable<T>> PutAsync<T>(string containerName, string resourceId, Storable<T> resource)
        {
            throw new NotImplementedException();
        }

        public Task<bool> TryBatchPutAsync<T>(string containerName, IEnumerable<Storable<T>> resources)
        {
            throw new NotImplementedException();
        }

        public Task<IEnumerable<Storable<T>>> BatchPutAsync<T>(string containerName, IEnumerable<Storable<T>> resources)
        {
            throw new NotImplementedException();
        }

        public Task<Storable<T>> TryGetAsync<T>(string containerName, string resourceId)
        {
            throw new NotImplementedException();
        }

        public async Task<Storable<T>> GetAsync<T>(string containerName, string resourceId)
        {
            string path = CreateFilePath(containerName: containerName, resourceId: resourceId);
            return new Storable<T>
            {
                Id = resourceId,
                ContainerId = containerName,
                Entity = SerializationHelpers.DeserializeEntity<T>(await ReadAllTextAsync(path))
            };
        }

        public Task<IEnumerable<Storable<T>>> BatchGetAsync<T>(string containerName, IEnumerable<string> resourceIds)
        {
            throw new NotImplementedException();
        }

        public Task<bool> TryUpdateAsync<T>(string containerName, string resourceId, Storable<T> resource)
        {
            throw new NotImplementedException();
        }

        public Task<Storable<T>> UpdateAsync<T>(string containerName, string resourceId, Storable<T> resource)
        {
            throw new NotImplementedException();
        }

        public Task<IEnumerable<Storable<T>>> BatchUpdateAsync<T>(string containerName, IEnumerable<Storable<T>> resources)
        {
            throw new NotImplementedException();
        }

        public Task<bool> TryDeleteAsync(string containerName, string resourceId)
        {
            throw new NotImplementedException();
        }

        public async Task DeleteAsync(string containerName, string resourceId)
        {
            string path = CreateFilePath(containerName: containerName, resourceId: resourceId);
            await DeleteFileAsync(path);
        }

        public Task DeleteAsync(string containerName, string resourceId, string etag)
        {
            throw new NotImplementedException();
        }

        public Task BatchDeleteAsync(string containerName, IEnumerable<string> resourceIds)
        {
            throw new NotImplementedException();
        }

        public Task InitializeAsync(string containerName)
        {
            throw new NotImplementedException();
        }

        public async Task<bool> CopyFileAsync(string sourcePath, string destPath, CancellationToken cts)
        {
            await CreateParentDirectoryIfNeededAsync(destPath);
            File.Copy(sourcePath, destPath, overwrite: false);
            return true;
        }

        public Task<bool> DeleteFileAsync(string path)
        {
            File.Delete(path);
            return Task.FromResult(true);
        }

        public Task<bool> FileExistsAsync(string path)
        {
            return Task.FromResult(File.Exists(path));
        }

        public Task<bool> FileExistsAsync(string path, bool allowIncomplete)
        {
            return Task.FromResult(File.Exists(path));
        }

        public Task<bool> IsDirectoryAsync(string path)
        {
            throw new NotImplementedException();
        }

        public async Task<IEnumerable<string>> ListDirectoryAsync(string path, bool recursive, uint fileCountLimit = 20000)
        {
            var files = new List<string>();
            await ListDirectoryInternalAsync(path, files: files, recursive: recursive, fileCountLimit: fileCountLimit, currentRecursionDepth: 0);
            return files;
        }

        private async Task ListDirectoryInternalAsync(string path, List<string> files,  bool recursive, uint fileCountLimit, uint currentRecursionDepth)
        {
            const int maxRecursionDepth = 300;
            if (++currentRecursionDepth > maxRecursionDepth)
            {
                throw new TooDeepDirectoryStructureException($"Exceeded limit of recursion depth for path {path}");
            }

            var dirInfo = new DirectoryInfo(path);
            if (recursive)
            {
                foreach (var fsEntry in dirInfo.EnumerateDirectories())
                {
                    await ListDirectoryInternalAsync(path: fsEntry.FullName, 
                                                     files: files, 
                                                     recursive: recursive, 
                                                     fileCountLimit: fileCountLimit, 
                                                     currentRecursionDepth: currentRecursionDepth);
                }
            }

            files.AddRange(dirInfo.EnumerateFiles().Select(file => file.FullName));
            if (files.Count > fileCountLimit)
            {
                throw new TooManyFilesException($"Exceeded limit of files to enumerate (limit is {fileCountLimit}) at folder {path}");
            }
        }

        private async Task CreateParentDirectoryIfNeededAsync(string filePath)
        {
            int fileNameStart = filePath.LastIndexOf("\\");
            if (fileNameStart >= 0)
            {
                await CreateDirectoryAsync(path: filePath.Substring(0, fileNameStart));
            }
        }

        public async Task<bool> CreateFileAsync(string path, string content)
        {
            await CreateParentDirectoryIfNeededAsync(path);
            File.WriteAllText(path: path, contents: content);
            return true; 
        }

        public Task CreateDirectoryAsync(string path)
        {
            Directory.CreateDirectory(path);
            return Task.FromResult(true);
        }

        public async Task<string> ReadAllTextAsync(string path)
        {
            using (var sr = new StreamReader(await ReadStreamAsync(path, offset: 0, length: -1)))
            {
                return await sr.ReadToEndAsync();
            }
        }

        public async Task<bool> AppendLineAsync(string path, string line)
        {
            return await AppendAsync(path, line + Environment.NewLine, compression: false);
        }

        public Task<long> GetFileLengthAsync(string path)
        {
            throw new NotImplementedException();
        }

        public Task<long> GetDirectorySizeAsync(string path)
        {
            throw new NotImplementedException();
        }

        public Task<Stream> ReadStreamAsync(string path, long offset, long length)
        {
            var stream = File.OpenRead(path);
            stream.Seek(offset, origin: SeekOrigin.Begin);
            return Task.FromResult((Stream)stream);
        }

        public Task<bool> AppendAsync(string path, string text, bool compression)
        {
            File.AppendAllText(path: path, contents: text);
            return Task.FromResult(true);
        }

        public Task<bool> CreateFileAsync(string path, string content, TimeSpan expirationTime)
        {
            throw new NotImplementedException();
        }
    }
}