﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Runtime.Serialization;
using System.Threading.Tasks;
using Microsoft.Aether.TokenProvider;
using Microsoft.RelInfra.Storage.Exceptions;
using Microsoft.WindowsAzure.Storage.Table;
using NUnit.Framework;

namespace Microsoft.RelInfra.Storage.Test
{
    [TestFixture]
    [Category("QTestSkip")]
    public class AzureTableStorageTest : BaseStorageTests
    {
        public AzureTableStorageTest()
            : base(TestSettings.PartitionName)
        {
        }

        private AzureTableStorage AzureTable => (AzureTableStorage)_storage;

        [SetUp]
        public void TestSetup()
        {
            _storage = new AzureTableStorage(
                connectionString: AzureCredHelper.GetDevAzureConnectionString(),
                tableName: TestSettings.AzureTableName,
                counterManager: null);
            ((AzureTableStorage)_storage).CreateIfNotExistsAsync().Wait();
        }

        [Test, Explicit]
        public async Task GetWithTakeCountSetting()
        {
            ITableStorage table = (ITableStorage)_storage;
            var residues = (await table.GetPartitionAsync<TestEntity>(TestSettings.PartitionName)).ToList();

            if (residues.Count() != 2010)
            {
                await Task.WhenAll(residues.Select<Storable<TestEntity>, Task<bool>>(async storable => await _storage.TryDeleteAsync(TestSettings.PartitionName, storable.Id)));

                List<Storable<TestEntity>> list = new List<Storable<TestEntity>>();

                for (int j = 0; j < 20; j++)
                {
                    for (int i = 0; i < 100; i++)
                    {
                        list.Add(new Storable<TestEntity>(new TestEntity() {Prop1 = "Entity" + (i + j)})
                            {
                                Id = Guid.NewGuid().ToString()
                            });
                    }
                    Assert.IsTrue(await table.TryBatchPutAsync(TestSettings.PartitionName, list));
                }
                for (int k = 2000; k < 2010; k++)
                {
                    list.Add(new Storable<TestEntity>(new TestEntity() {Prop1 = "Entity" + k})
                        {
                            Id = Guid.NewGuid().ToString()
                        });
                }
                Assert.IsTrue(await table.TryBatchPutAsync(TestSettings.PartitionName, list));
            }

            var e2001 = await table.GetPartitionAsync<string>(TestSettings.PartitionName, 2001);
            Assert.AreEqual(2001, e2001.Count());
            var e1250 = await table.GetPartitionAsync<string>(TestSettings.PartitionName, 1250);
            Assert.AreEqual(1250, e1250.Count());
            var e12 = await table.GetPartitionAsync<string>(TestSettings.PartitionName, 12);
            Assert.AreEqual(12, e12.Count());
            var eAll = await table.GetPartitionAsync<string>(TestSettings.PartitionName);
            Assert.AreEqual(2010, eAll.Count());
        }

        [Test]
        public async Task Test_BatchAddResource()
        {
            string[] objs = new[] { "test_batch1", "test_batch2" };

            IEnumerable<Storable<string>> resources = objs.Select(o => new Storable<string>()
                {
                    Id = Guid.NewGuid().ToString(),
                    Entity = o
                }).ToList();

            Assert.IsTrue(await _storage.TryBatchPutAsync(TestSettings.PartitionName, resources));

            // try to add them again (should return true, we don't want to throw exceptions if some entities exist)
            Assert.IsTrue(await _storage.TryBatchPutAsync(TestSettings.PartitionName, resources));

            foreach (var res in resources)
            {
                await _storage.TryDeleteAsync(TestSettings.PartitionName, res.Id);
            }
        }

        [Test]
        public async Task Test_AddComplexEntity()
        {
            var entity = new TestEntity {Prop1 = null, Prop2 = 10};
            var resource = new Storable<TestEntity>(entity);
            resource.Id = Guid.NewGuid().ToString();

            Assert.IsTrue(await _storage.TryPutAndIgnoreEtagAsync(TestSettings.PartitionName, resource.Id, resource));

            Storable<TestEntity> storedResource = await _storage.TryGetAsync<TestEntity>(TestSettings.PartitionName, resource.Id);

            Assert.AreEqual(resource.Entity.Prop1, storedResource.Entity.Prop1);
            Assert.AreEqual(resource.Entity.Prop2, storedResource.Entity.Prop2);
            Assert.AreEqual(TestSettings.PartitionName, storedResource.ContainerId);

            await _storage.TryDeleteAsync(TestSettings.PartitionName, resource.Id);
        }

        [Test]
        public async Task TestMergeBehaviourForPut()
        {
            string id = Guid.NewGuid().ToString();
            await _storage.PutAndIgnoreEtagAsync(TestSettings.PartitionName, id, new Storable<TestObject>
            {
                Id = id,
                Entity = new TestObject { Id = id, Name = "TestName", CreatedDate = DateTime.Now }
            });

            await _storage.PutAndIgnoreEtagAsync(TestSettings.PartitionName, id, new Storable<TestObject2>
            {
                Id = id,
                Entity = new TestObject2 { Id = id, Name2 = "TestName2" }
            });

            Storable<TestObject> obj = await _storage.GetAsync<TestObject>(TestSettings.PartitionName, id);
            Storable<TestObject2> obj2 = await _storage.GetAsync<TestObject2>(TestSettings.PartitionName, id);

            Assert.AreEqual(id, obj.Entity.Id);
            Assert.AreEqual("TestName", obj.Entity.Name);
            Assert.AreEqual(TestSettings.PartitionName, obj.ContainerId);

            Assert.AreEqual(id, obj2.Entity.Id);
            Assert.AreEqual("TestName2", obj2.Entity.Name2);
            Assert.AreEqual(TestSettings.PartitionName, obj2.ContainerId);

            await _storage.TryDeleteAsync(TestSettings.PartitionName, id);
        }

        [Test]
        public async Task TestMergeBehaviourForUpdate()
        {
            string id = Guid.NewGuid().ToString();
            Trace.WriteLine(id);

            await _storage.PutAndIgnoreEtagAsync(TestSettings.PartitionName, id, new Storable<TestObject>
            {
                Id = id,
                Entity = new TestObject { Id = id, Name = "TestName", CreatedDate = DateTime.Now }
            });

            await _storage.UpdateAsync(TestSettings.PartitionName, id, new Storable<TestObject2>
            {
                Id = id,
                Entity = new TestObject2 { Id = id, Name2 = "TestName2" },
                Etag = "*"
            });

            Storable<TestObject> obj = await _storage.GetAsync<TestObject>(TestSettings.PartitionName, id);
            Storable<TestObject2> obj2 = await _storage.GetAsync<TestObject2>(TestSettings.PartitionName, id);

            Assert.AreEqual(id, obj.Entity.Id);
            Assert.AreEqual("TestName", obj.Entity.Name);
            Assert.AreEqual(TestSettings.PartitionName, obj.ContainerId);

            Assert.AreEqual(id, obj2.Entity.Id);
            Assert.AreEqual("TestName2", obj2.Entity.Name2);
            Assert.AreEqual(TestSettings.PartitionName, obj2.ContainerId);

            await _storage.TryDeleteAsync(TestSettings.PartitionName, id);
        }

        [Test]
        public async Task TestUpdateBehaviour()
        {
            string id = Guid.NewGuid().ToString();
            Trace.WriteLine(id);

            Storable<TestObject> obj = await _storage.PutAndIgnoreEtagAsync(TestSettings.PartitionName, id, new Storable<TestObject>
            {
                Id = id,
                Entity = new TestObject { Id = id, Name = "TestName", CreatedDate = DateTime.Now }
            });

            Storable<TestObject> obj2 = await _storage.UpdateAsync(TestSettings.PartitionName, id, new Storable<TestObject>
            {
                Id = id,
                Entity = new TestObject { Id = id, Name = null, CreatedDate = DateTime.Now.AddMinutes(1)},
                Etag = "*"
            });

            Assert.AreEqual(id, obj.Id);
            Assert.AreEqual("TestName", obj.Entity.Name);
            Assert.AreEqual(TestSettings.PartitionName, obj.ContainerId);

            Assert.AreEqual(id, obj2.Id);
            Assert.AreEqual("TestName", obj2.Entity.Name);
            Assert.AreEqual(TestSettings.PartitionName, obj2.ContainerId);

            await _storage.TryDeleteAsync(TestSettings.PartitionName, id);
        }

        [Test]
        public void TestUpdateNonExistingRecordBehaviourAsync()
        {
            string id = Guid.NewGuid().ToString();
            Trace.WriteLine(id);

            Assert.ThrowsAsync<RelInfraStorageException>(async () => await _storage.UpdateAsync(TestSettings.PartitionName, id, new Storable<TestObject>
            {
                Id = id,
                Entity = new TestObject { Id = id, Name = null, CreatedDate = DateTime.Now.AddMinutes(1) },
                Etag = "*"
            }));
        }

        public async Task TestBatchGetAsync()
        {
            var entity = new TestEntity { Prop1 = null, Prop2 = 10 };

            var resource = new Storable<TestEntity>(entity) {Id = Guid.NewGuid().ToString()};
            Assert.IsTrue(await _storage.TryPutAndIgnoreEtagAsync(TestSettings.PartitionName, resource.Id, resource));

            var resource1 = new Storable<TestEntity>(entity) {Id = Guid.NewGuid().ToString()};
            Assert.IsTrue(await _storage.TryPutAndIgnoreEtagAsync(TestSettings.PartitionName, resource1.Id, resource1));

            var resource2 = new Storable<TestEntity>(entity) {Id = Guid.NewGuid().ToString()};
            Assert.IsTrue(await _storage.TryPutAndIgnoreEtagAsync(TestSettings.PartitionName, resource2.Id, resource2));

            IEnumerable<Storable<TestEntity>> storedResources =
                await _storage.BatchGetAsync<TestEntity>(TestSettings.PartitionName,
                    new[] {resource.Id, resource1.Id, resource2.Id});

            Assert.IsTrue(storedResources.All(r => r.ContainerId == TestSettings.PartitionName));

            await _storage.BatchDeleteAsync(TestSettings.PartitionName, new[] {resource.Id, resource1.Id, resource2.Id});
        }

        [Test]
        public async Task TestBatchDelete()
        {
            var entity = new TestEntity { Prop1 = null, Prop2 = 10 };

            var resource = new Storable<TestEntity>(entity) { Id = Guid.NewGuid().ToString() };
            Assert.IsTrue(await _storage.TryPutAndIgnoreEtagAsync(TestSettings.PartitionName, resource.Id, resource));

            var resource1 = new Storable<TestEntity>(entity) { Id = Guid.NewGuid().ToString() };
            Assert.IsTrue(await _storage.TryPutAndIgnoreEtagAsync(TestSettings.PartitionName, resource1.Id, resource1));

            var resource2 = new Storable<TestEntity>(entity) { Id = Guid.NewGuid().ToString() };
            Assert.IsTrue(await _storage.TryPutAndIgnoreEtagAsync(TestSettings.PartitionName, resource2.Id, resource2));

            await _storage.BatchDeleteAsync(TestSettings.PartitionName, new[] { resource.Id, resource1.Id, resource2.Id });
        }

        [Test]
        public async Task TestBatchUpdate()
        {
            var entity = new TestEntity { Prop1 = null, Prop2 = 10 };

            var resource1 = new Storable<TestEntity>(entity) { Id = Guid.NewGuid().ToString() };
            Assert.IsTrue(await _storage.TryPutAndIgnoreEtagAsync(TestSettings.PartitionName, resource1.Id, resource1));

            var resource2 = new Storable<TestEntity>(entity) { Id = Guid.NewGuid().ToString() };
            Assert.IsTrue(await _storage.TryPutAndIgnoreEtagAsync(TestSettings.PartitionName, resource2.Id, resource2));

            var updatedResource = new Storable<TestEntity>()
            {
                Id = resource2.Id,
                Entity = new TestEntity { Prop1 = null, Prop2 = 11 },
                Etag = resource2.Etag
            };
            await _storage.UpdateAsync(TestSettings.PartitionName, updatedResource.Id, updatedResource);

            resource1.Entity.Prop2 = 20;
            resource2.Entity.Prop2 = 20;

            bool isPreconditionFailed = false;
            try
            {
                await _storage.BatchUpdateAsync(TestSettings.PartitionName, new[] { resource1, resource2 });
            }
            catch (RelInfraStorageException ex)
            {
                isPreconditionFailed = ex.Error == StorageError.PreconditionFailed;
            }
            Assert.IsTrue(isPreconditionFailed);
        }

        [Test]
        public async Task TestBatchOversizeUpdate()
        {
            string prop = new string('a', 5 * 32 * 1024 - 5); // 5* 32k max for a column
            var entity = new TestEntityWithLargeObject { LargeString = prop };
            List<Storable<TestEntityWithLargeObject>> resources = new List<Storable<TestEntityWithLargeObject>>();
            Enumerable.Range(0, 100).ToList().ForEach(i => resources.Add(new Storable<TestEntityWithLargeObject>(entity) { Id = Guid.NewGuid().ToString() }));
            
            var results = await _storage.BatchPutAsync(TestSettings.PartitionName, resources);

            foreach (Storable<TestEntityWithLargeObject> resource in resources)
            {
                Assert.IsTrue(results.Any(result => result.Id == resource.Id));
            }

            var updateResults = await _storage.BatchUpdateAsync(TestSettings.PartitionName, results);
            foreach (Storable<TestEntityWithLargeObject> resource in resources)
            {
                Assert.IsTrue(updateResults.Any(result => result.Id == resource.Id));
            }
        }

        [Test]
        public async Task TestGetNullable()
        {
            var entity = new TestEntityNullable { Prop1 = "test", NullableProp1 = DateTime.UtcNow, NullableProp2 = null};

            var resource = new Storable<TestEntityNullable>(entity) { Id = Guid.NewGuid().ToString() };
            Assert.IsTrue(await _storage.TryPutAndIgnoreEtagAsync(TestSettings.PartitionName, resource.Id, resource));

            Storable<TestEntityNullable> storedResource =
                await _storage.GetAsync<TestEntityNullable>(TestSettings.PartitionName, resource.Id);

            Assert.AreEqual(resource.Id, storedResource.Id);
            Assert.AreEqual(entity.Prop1, storedResource.Entity.Prop1);
            Assert.AreEqual(entity.NullableProp1, storedResource.Entity.NullableProp1);
            Assert.AreEqual(entity.NullableProp2, storedResource.Entity.NullableProp2);

            await _storage.DeleteAsync(TestSettings.PartitionName, resource.Id);
        }

        [Test]
        public async Task TestExists()
        {
            var entity = new TestEntityNullable { Prop1 = "test", NullableProp1 = DateTime.UtcNow, NullableProp2 = null };

            var resource = new Storable<TestEntityNullable>(entity) { Id = Guid.NewGuid().ToString() };
            Assert.IsTrue(await _storage.TryPutAndIgnoreEtagAsync(TestSettings.PartitionName, resource.Id, resource));

            Assert.IsTrue(await _storage.ExistsAsync(TestSettings.PartitionName, resource.Id));
            Assert.IsFalse(await _storage.ExistsAsync(TestSettings.PartitionName, Guid.NewGuid().ToString()));

            await _storage.DeleteAsync(TestSettings.PartitionName, resource.Id);
        }

        [Test]
        public async Task TestLobAsync()
        {
            string id = Guid.NewGuid().ToString();
            string largeString = AzureTableEntityTest.RandomString(50000);

            await _storage.PutAndIgnoreEtagAsync(TestSettings.PartitionName, id, new Storable<TestEntityWithLargeObject>
            {
                Id = id,
                Entity = new TestEntityWithLargeObject { PropertyInt = 1, LargeString = largeString }
            });

            TestEntityWithLargeObject obj = (await _storage.GetAsync<TestEntityWithLargeObject>(TestSettings.PartitionName, id)).Entity;
            Assert.AreEqual(largeString, obj.LargeString);

            await _storage.PutAndIgnoreEtagAsync(TestSettings.PartitionName, id, new Storable<TestEntityWithLargeObject>
            {
                Id = id,
                Entity = new TestEntityWithLargeObject { PropertyInt = 1, LargeString = "Short string" }
            });
            obj = (await _storage.GetAsync<TestEntityWithLargeObject>(TestSettings.PartitionName, id)).Entity;
            Assert.AreEqual("Short string", obj.LargeString);

            await _storage.TryDeleteAsync(TestSettings.PartitionName, id);
        }

        [Test]
        public async Task TestInsertOrReplaceAsync()
        {
            // test multiple partitions to validate batching
            const string partitionUnderTest1 = "96e6caf1-ad0d-498b-a492-dcb4ca359d5a";
            const string partitionUnderTest2 = "5f233fb4-a70d-4eaa-b973-6b78e2447666";
            await DeleteOldTestData(partitionUnderTest1);
            await DeleteOldTestData(partitionUnderTest2);

            // create new entities
            TestEntity oldReplaceEntity = new TestEntity() { Prop1 = "Value1", Prop2 = 1 };
            string replaceEntityId = Guid.NewGuid().ToString();
            await AzureTable.AddAsync(partitionUnderTest1, replaceEntityId, new Storable<TestEntity>(oldReplaceEntity));

            // perform insert/replace
            TestEntity newReplaceEntity = new TestEntity() { Prop1 = "Value2", Prop2 = 2 };
            Storable<TestEntity> replaceStorable = new Storable<TestEntity>(newReplaceEntity) { ContainerId = partitionUnderTest1, Id = replaceEntityId };
            TestEntity insertEntity = new TestEntity() { Prop1 = "Insert", Prop2 = 3 };
            string insertEntityId = Guid.NewGuid().ToString();
            Storable<TestEntity> insertStorable = new Storable<TestEntity>(insertEntity) { ContainerId = partitionUnderTest1, Id = insertEntityId };
            TestEntity insertEntity2 = new TestEntity() { Prop1 = "Insert2", Prop2 = 4 };
            string insertEntityId2 = Guid.NewGuid().ToString();
            Storable<TestEntity> insertStorable2 = new Storable<TestEntity>(insertEntity2) { ContainerId = partitionUnderTest2, Id = insertEntityId2 };

            await AzureTable.InsertOrReplaceAsync(new[] { insertStorable, insertStorable2, replaceStorable });

            // validate success
            Storable<TestEntity> fetchedReplaceEntity = await AzureTable.GetAsync<TestEntity>(partitionUnderTest1, replaceEntityId);
            Assert.AreEqual(newReplaceEntity.Prop1, fetchedReplaceEntity.Entity.Prop1);
            Assert.AreEqual(newReplaceEntity.Prop2, fetchedReplaceEntity.Entity.Prop2);
            Assert.AreEqual(partitionUnderTest1, fetchedReplaceEntity.ContainerId);

            Storable<TestEntity> fetchedInsertEntity = await AzureTable.GetAsync<TestEntity>(partitionUnderTest1, insertEntityId);
            Assert.AreEqual(insertEntity.Prop1, fetchedInsertEntity.Entity.Prop1);
            Assert.AreEqual(insertEntity.Prop2, fetchedInsertEntity.Entity.Prop2);
            Assert.AreEqual(partitionUnderTest1, fetchedInsertEntity.ContainerId);

            Storable<TestEntity> fetchedInsertEntity2 = await AzureTable.GetAsync<TestEntity>(partitionUnderTest2, insertEntityId2);
            Assert.AreEqual(insertEntity2.Prop1, fetchedInsertEntity2.Entity.Prop1);
            Assert.AreEqual(insertEntity2.Prop2, fetchedInsertEntity2.Entity.Prop2);
            Assert.AreEqual(partitionUnderTest2, fetchedInsertEntity2.ContainerId);
        }

        [Test]
        public async Task TestInsertOrReplaceAsyncLargeTable()
        {
            const string partitionUnderTest = "13c056c5-4bc7-4636-9a5a-fc4d23227422";
            await DeleteOldTestData(partitionUnderTest);

            // initialize test entities
            string rowUnderTest = Guid.NewGuid().ToString();
            IEnumerable<Storable<TestEntity>> entities = 
                Enumerable.Range(0, 400)
                .Select(i => new TestEntity() { Prop1 = rowUnderTest + i, Prop2 = i })
                .Select(e => new Storable<TestEntity>(e) { ContainerId = partitionUnderTest, Id = e.Prop1 });
            await AzureTable.InsertOrReplaceAsync(entities);

            // validate count of newly inserted rows
            string insertedEntityFilter = TableQuery.CombineFilters(
                TableQuery.GenerateFilterCondition("PartitionKey", QueryComparisons.Equal, partitionUnderTest),
                TableOperators.And,
                TableQuery.GenerateFilterConditionForDate("Timestamp", QueryComparisons.GreaterThan, DateTimeOffset.UtcNow.Subtract(TimeSpan.FromMinutes(1))));

            Assert.AreEqual(400, (await AzureTable.ExecuteQueryAsync<TestEntity>(insertedEntityFilter)).Count(e => e.Id.StartsWith(rowUnderTest)));
        }

        // Issue: the takeCount will not take effect if the takeCount is greater than the limit that max entity number in one page
        [Test, Explicit]
        public async Task TestGetAllWithContinuationTokenAsync()
        {
            // without takecount
            int withoutTakeCount = 0;
            string continuationToken = null;
            do
            {
                QueryResults<TestEntity> results = await AzureTable.GetAllWithContinuationTokenAsync<TestEntity>(continuationToken);
                continuationToken = results.ContinuationToken;
                withoutTakeCount += results.Results.Count();
            }
            while (continuationToken != null);

            Assert.Greater(withoutTakeCount, 0);

            // with takecount
            int withTakeCount = 0;
            int? firstTakeCount = null;
            do
            {
                QueryResults<TestEntity> results = await AzureTable.GetAllWithContinuationTokenAsync<TestEntity>(continuationToken, 78);
                continuationToken = results.ContinuationToken;
                withTakeCount += results.Results.Count();
                if (firstTakeCount == null)
                {
                    firstTakeCount = withTakeCount;
                }
            }
            while (continuationToken != null);

            Assert.AreEqual(78, firstTakeCount.Value);
            Assert.Greater(withTakeCount, 0);
        }

        [Test]
        public void TestBatchStorables()
        {
            List<Storable<TestEntity>> storables = 
                Enumerable.Range(0, 10).Select(CreateTestEntity).Select(e => new Storable<TestEntity>(e) { ContainerId = "1", Id = e.Prop2.ToString() })
                .Union(Enumerable.Range(0, 10).Select(CreateTestEntity).Select(e => new Storable<TestEntity>(e) { ContainerId = "2", Id = e.Prop2.ToString() }))
                .ToList();

            List<List<Storable<TestEntity>>> batchedStorables = AzureTableStorage.BatchStorables(storables, 5).Select(b => b.ToList()).ToList();
            Assert.AreEqual(4, batchedStorables.Count); // number of batches
            Assert.IsTrue(batchedStorables.All(b => b.Count == 5)); // batch size
            Assert.IsTrue(batchedStorables.All(b => b.All(s => s.ContainerId == b.First().ContainerId))); // batch coherence
        }

        private static TestEntity CreateTestEntity(int identifier)
        {
            return new TestEntity()
                       {
                           Prop1 = "PropValue" + identifier,
                           Prop2 = identifier
                       };
        }

        private async Task DeleteOldTestData(string partitionId)
        {
            string oldEntityFilter = TableQuery.CombineFilters(
                TableQuery.GenerateFilterConditionForDate("Timestamp", QueryComparisons.LessThan, DateTimeOffset.UtcNow.Subtract(TimeSpan.FromDays(1))),
                TableOperators.And,
                TableQuery.GenerateFilterCondition("PartitionKey", QueryComparisons.Equal, partitionId));

            foreach (Storable<TestEntity> entity in (await AzureTable.ExecuteQueryAsync<TestEntity>(oldEntityFilter)))
            {
                await AzureTable.TryDeleteAsync(entity.ContainerId, entity.Id);
            }
        }

        public class TestEntity
        {
            public string Prop1 { get; set; }
            public int Prop2 { get; set; }
        }

        public class TestEntityNullable
        {
            public string Prop1 { get; set; }
            public DateTime? NullableProp1 { get; set; }
            public DateTime? NullableProp2 { get; set; }
        }

        [DataContract]
        public class TestObject
        {
            [DataMember]
            public string Id { get; set; }
            [DataMember]
            public string Name { get; set; }
            [DataMember]
            public DateTime CreatedDate { get; set; }
        }

        [DataContract]
        public class TestObject2
        {
            [DataMember]
            public string Id { get; set; }
            [DataMember]
            public string Name2 { get; set; }
        }

        [DataContract]
        public class TestEntityWithLargeObject
        {
            [DataMember]
            public int PropertyInt { get; set; }

            [DataMember]
            [LargeObject(5)]
            public string LargeString { get; set; }
        }
    }
}
