﻿using System;
using System.Threading.Tasks;
using Microsoft.RelInfra.Storage.Exceptions;
using NUnit.Framework;

namespace Microsoft.RelInfra.Storage.Test
{
    [TestFixture]
    public abstract class BaseStorageTests
    {
        internal IStorage _storage;
        private readonly string _containerName;
        protected BaseStorageTests(string containerName)
        {
            _containerName = containerName;
        }

        [Test]
        public async Task Test_AddResource()
        {
            Storable<string> storableObj = await CreateTestEntity();
            Assert.IsNotNull(storableObj);
            Assert.That(storableObj.Etag, Is.Not.Null.And.Not.Empty);

            await _storage.TryDeleteAsync(_containerName, storableObj.Id);
        }

        [Test]
        public async Task Test_GetResource()
        {
            Storable<string> storableObj = await CreateTestEntity();
            Assert.IsNotNull(storableObj);

            Storable<string> objFromBlob = await _storage.TryGetAsync<string>(_containerName, storableObj.Id);
            Assert.AreEqual(storableObj.Entity, objFromBlob.Entity);
            Assert.AreEqual(_containerName, objFromBlob.ContainerId);

            await _storage.TryDeleteAsync(_containerName, storableObj.Id);
        }

        [Test]
        public async Task Test_GetNonExistingResource()
        {
            bool isNotFoundException = false;
            try
            {
                await _storage.GetAsync<string>(_containerName, Guid.NewGuid().ToString());
            }
            catch (RelInfraStorageException ex)
            {
                isNotFoundException = ex.Error == StorageError.NotFound;
            }
            Assert.IsTrue(isNotFoundException);
        }

        [Test]
        public async Task Test_UpdateResource()
        {
            Storable<string> storableObj = await CreateTestEntity();
            Assert.IsNotNull(storableObj);
            Assert.That(storableObj.Etag, Is.Not.Null.And.Not.Empty);

            var storableObj2 = new Storable<string> { Entity = "test string2", Etag = storableObj.Etag };
            Assert.IsTrue(await _storage.TryUpdateAsync(_containerName, storableObj.Id, storableObj2));

            Storable<string> objFromBlob = await _storage.TryGetAsync<string>(_containerName, storableObj.Id);
            Assert.AreEqual(storableObj2.Entity, objFromBlob.Entity);
            Assert.AreEqual(_containerName, objFromBlob.ContainerId);

            await _storage.TryDeleteAsync(_containerName, storableObj.Id);
        }

        [Test]
        public async Task Test_UpdateChangedResource()
        {
            Storable<string> storableObj = await CreateTestEntity();
            Assert.IsNotNull(storableObj);
            Assert.That(storableObj.Etag, Is.Not.Null.And.Not.Empty);

            var storableObj2 = new Storable<string> { Entity = "test string2", Etag = storableObj.Etag };
            Assert.IsTrue(await _storage.TryUpdateAsync(_containerName, storableObj.Id, storableObj2));

            // should fail because it was already updated
            storableObj.Entity = "test string3";
            Assert.IsFalse(await _storage.TryUpdateAsync(_containerName, storableObj.Id, storableObj));

            await _storage.TryDeleteAsync(_containerName, storableObj.Id);
        }

        [Test]
        public async Task Test_DeleteResource()
        {
            Storable<string> storableObj = await CreateTestEntity();
            Assert.IsNotNull(storableObj);

            Assert.IsTrue(await _storage.TryDeleteAsync(_containerName, storableObj.Id));
        }

        private async Task<Storable<string>> CreateTestEntity()
        {
            string message = "test string";
            string resourceId = Guid.NewGuid().ToString();

            var storableObj = new Storable<string> { Entity = message };

            if (!await _storage.TryPutAndIgnoreEtagAsync(_containerName, resourceId, storableObj))
            {
                return null;
            }

            return storableObj;
        }

    }
}
