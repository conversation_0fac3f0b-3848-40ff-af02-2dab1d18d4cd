﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Azure.Identity;
using Azure.Storage.Queues;
using NUnit.Framework;

namespace Microsoft.RelInfra.Storage.Test
{
    [TestFixture]
    [Category("QTestSkip")]
    public class AzureQueueV2Test
    {
        private const string AccountName = "<[no-checkin] replace-with-accessible-storage-account>";
        private const string QueueName = "queuev2test";

        private readonly Uri ServiceUri = new Uri($"https://{AccountName}.queue.core.windows.net");
        private readonly TimeSpan _leaseTime = TimeSpan.FromSeconds(5);
        private readonly Storable<string> _defaultMessageEntity = new Storable<string> { Entity = "test message" };

        private AzureQueueV2<string> _azureQueue;

        [OneTimeSetUp]
        public void SetUp()
        {
            // Can be replaced with WorkloadIdentityCredential which also extends TokenCredential.
            var credential = new DefaultAzureCredential();
            _azureQueue = new AzureQueueV2<string>(ServiceUri, QueueName, credential);
            _azureQueue.InitializeQueueAsync().Wait();
        }

        [SetUp]
        public async Task TestSetup()
        {
            // make sure queue is empty before tests.
            await Task.Delay(_leaseTime);
            Storable<string> entity;
            while ((entity = await _azureQueue.GetAsync(_leaseTime)) != null)
            {
                await _azureQueue.DeleteAsync(entity);
            }
        }

        [Test]
        public async Task Test_AddMessageAsync()
        {
            await _azureQueue.PutAsync(_defaultMessageEntity);

            var message = await _azureQueue.GetAsync(_leaseTime);
            Assert.IsNotNull(message);
            Assert.That(message.Id, Is.Not.Null.And.Not.Empty);
            Assert.That(message.Etag, Is.Not.Null.And.Not.Empty);
            Assert.AreEqual(QueueName, message.ContainerId);
            Assert.AreEqual(_defaultMessageEntity.Entity, message.Entity);

            // Fetch again, message is not visible because of leaseTime.
            message = await _azureQueue.GetAsync(_leaseTime);
            Assert.IsNull(message);

            // Message is visible after lease time.
            await Task.Delay(_leaseTime);
            message = await _azureQueue.GetAsync(_leaseTime);
            Assert.IsNotNull(message);
            Assert.That(message.Id, Is.Not.Null.And.Not.Empty);
            Assert.That(message.Etag, Is.Not.Null.And.Not.Empty);
            Assert.AreEqual(QueueName, message.ContainerId);
            Assert.AreEqual(_defaultMessageEntity.Entity, message.Entity);

            await _azureQueue.DeleteAsync(message);
        }

        [Test]
        public async Task Test_AddMessageWithVisibilityDelayAsync()
        {
            var visibilityDelay = TimeSpan.FromSeconds(5);
            await _azureQueue.PutAsync(_defaultMessageEntity, visibilityDelay);

            // Message is not visible yet.
            var message = await _azureQueue.GetAsync(_leaseTime);
            Assert.IsNull(message);

            await Task.Delay(visibilityDelay);
            // Message is visible.
            message = await _azureQueue.GetAsync(_leaseTime);
            Assert.IsNotNull(message);
            Assert.That(message.Id, Is.Not.Null.And.Not.Empty);
            Assert.That(message.Etag, Is.Not.Null.And.Not.Empty);
            Assert.AreEqual(QueueName, message.ContainerId);
            Assert.AreEqual(_defaultMessageEntity.Entity, message.Entity);

            await _azureQueue.DeleteAsync(message);
        }

        [Test]
        public async Task Test_AddMessageWithTTLAsync()
        {
            var visibilityDelay = TimeSpan.FromSeconds(1);
            var minimumTTL = TimeSpan.FromMinutes(5);
            await _azureQueue.PutAsync(_defaultMessageEntity, visibilityDelay, minimumTTL);

            await Task.Delay(visibilityDelay);
            var message = await _azureQueue.GetAsync(_leaseTime);
            Assert.IsNotNull(message);

            // Message is expired and deleted.
            await Task.Delay(minimumTTL);
            message = await _azureQueue.GetAsync(_leaseTime);
            Assert.IsNull(message);
        }

        [Test]
        public async Task Test_GetMessagesAsync()
        {
            int numberOfMessages = 40;

            var queueMessages = await _azureQueue.GetMessagesAsync(numberOfMessages, _leaseTime);
            Assert.IsEmpty(queueMessages);

            for (int i = 0; i < numberOfMessages; i++)
            {
                await _azureQueue.PutAsync(new Storable<string> { Entity = $"test message {i}" });
            }

            var messages = await _azureQueue.GetMessagesAsync(numberOfMessages, _leaseTime);
            Assert.AreEqual(numberOfMessages, messages.Count());

            for (int i = 0; i < numberOfMessages; i++)
            {
                Assert.IsNotNull(messages.FirstOrDefault(s => s.Entity.Equals($"test message {i}")));
            }

            foreach (Storable<string> storable in messages)
            {
                await _azureQueue.DeleteAsync(storable.Id, storable.Etag);
            }
        }

        [Test]
        public async Task Test_UpdateMessageAsync()
        {
            await _azureQueue.PutAsync(_defaultMessageEntity);

            var message = await _azureQueue.GetAsync(_leaseTime);
            Assert.IsNotNull(message);
            Assert.AreEqual(_defaultMessageEntity.Entity, message.Entity);

            string updatedMessage = "test message updated";
            message.Entity = updatedMessage;
            await _azureQueue.UpdateMessageAsync(message, _leaseTime, MessageUpdateFlags.UpdateContent);

            // Message content is updated.
            await Task.Delay(_leaseTime);
            message = await _azureQueue.GetAsync(_leaseTime);
            Assert.IsNotNull(message);
            Assert.AreEqual(updatedMessage, message.Entity);

            var newVisibilityDelay = _leaseTime * 2;
            await _azureQueue.UpdateMessageAsync(message, newVisibilityDelay, MessageUpdateFlags.UpdateVisibility);
            // Message visibility is updated to 2 * _leaseTime, delay _leaseTime won't get message.
            await Task.Delay(_leaseTime);
            message = await _azureQueue.GetAsync(_leaseTime);
            Assert.IsNull(message);

            // Delay for 2 * _leaseTime, can get message.
            await Task.Delay(_leaseTime);
            message = await _azureQueue.GetAsync(_leaseTime);
            Assert.IsNotNull(message);
            Assert.AreEqual(updatedMessage, message.Entity);

            await _azureQueue.DeleteAsync(message);
        }

        [Test]
        public async Task Test_DeleteMessageAsync()
        {
            await _azureQueue.PutAsync(_defaultMessageEntity);
            var message = await _azureQueue.GetAsync(_leaseTime);
            Assert.IsNotNull(message);

            Assert.IsTrue(await _azureQueue.DeleteAsync(message.Id, message.Etag));
            // Return true if the message has already been deleted.
            Assert.IsTrue(await _azureQueue.DeleteAsync(message));
        }

        [Test]
        public async Task Test_GetQueueLengthAsync()
        {
            // Empty queue.
            Assert.AreEqual(0, await _azureQueue.GetQueueLengthAsync());

            await _azureQueue.PutAsync(_defaultMessageEntity);
            Assert.AreEqual(1, await _azureQueue.GetQueueLengthAsync());

            // Get the message but not delete it.
            var message = await _azureQueue.GetAsync(_leaseTime);
            Assert.IsNotNull(message);
            Assert.AreEqual(1, await _azureQueue.GetQueueLengthAsync());

            // Delete the message. Empty queue.
            await _azureQueue.DeleteAsync(message);
            Assert.AreEqual(0, await _azureQueue.GetQueueLengthAsync());

            int numberOfMessages = 10;
            for (var i = 0; i < numberOfMessages; i++)
            {
                await _azureQueue.PutAsync(new Storable<string> { Entity = $"test message {i}" });
            }
            Assert.AreEqual(numberOfMessages, await _azureQueue.GetQueueLengthAsync());
        }

        [Test]
        public async Task Test_GetDequeueCount()
        {
            await _azureQueue.PutAsync(_defaultMessageEntity);
            var message = await _azureQueue.GetAsync(_leaseTime);
            Assert.IsNotNull(message);
            Assert.AreEqual(1, _azureQueue.GetDequeueCount(message));

            await Task.Delay(_leaseTime);
            message = await _azureQueue.GetAsync(_leaseTime);
            Assert.IsNotNull(message);
            Assert.AreEqual(2, _azureQueue.GetDequeueCount(message));
        }

        [Test]
        public async Task Test_GetMessageLifetime()
        {
            var visibilityDelay = TimeSpan.FromSeconds(1);
            var ttl = TimeSpan.FromMinutes(10);
            await _azureQueue.PutAsync(_defaultMessageEntity, visibilityDelay, ttl);

            await Task.Delay(visibilityDelay);
            var message = await _azureQueue.GetAsync(_leaseTime);
            Assert.IsNotNull(message);
            // Message lifetime is between 0 and ttl.
            Assert.Less(TimeSpan.Zero, _azureQueue.GetMessageLifetime(message));
            Assert.Less(_azureQueue.GetMessageLifetime(message), ttl);
        }

        [Test]
        public async Task Test_ListAllQueueNamesAsync()
        {
            var queueNames = await _azureQueue.ListAllQueueNamesAsync(QueueName);
            Assert.AreEqual(1, queueNames.Count());
            Assert.AreEqual(QueueName, queueNames.First());

            string notExistQueueNamePrefix = $"notexist{QueueName}";
            queueNames = await _azureQueue.ListAllQueueNamesAsync(notExistQueueNamePrefix);
            Assert.AreEqual(0, queueNames.Count());

            // Create a new queue.
            string newQueueName = $"{QueueName}new";
            var queueServiceUri = new Uri($"https://{AccountName}.queue.core.windows.net");
            var queueServiceClient = new QueueServiceClient(queueServiceUri, new DefaultAzureCredential());
            await queueServiceClient.CreateQueueAsync(newQueueName);

            queueNames = await _azureQueue.ListAllQueueNamesAsync(QueueName);
            Assert.AreEqual(2, queueNames.Count());
            Assert.IsNotNull(queueNames.FirstOrDefault(n => n == QueueName));
            Assert.IsNotNull(queueNames.FirstOrDefault(n => n == newQueueName));

            await queueServiceClient.DeleteQueueAsync(newQueueName);
        }

        [Test]
        public async Task Test_GetAndSetQueueMetadataAsync()
        {
            var metadata = await _azureQueue.GetMetadataAsync();
            Assert.IsEmpty(metadata);

            var newMetadata = new Dictionary<string, string> { { "key1", "value1" } };
            await _azureQueue.SetMetadataAsync(newMetadata);
            metadata = await _azureQueue.GetMetadataAsync();
            Assert.AreEqual(1, metadata.Count());
            Assert.IsTrue(metadata.ContainsKey("key1"));
            Assert.IsTrue(metadata["key1"] == "value1");

            // Clear metadata.
            await _azureQueue.SetMetadataAsync(new Dictionary<string, string>());
            metadata = await _azureQueue.GetMetadataAsync();
            Assert.IsEmpty(metadata);
        }
    }
}
