﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.RelInfra.Common.RetryExecution.Policy;
using Microsoft.RelInfra.Storage.Exceptions;
using Microsoft.RelInfra.Storage.Test.Mocks;
using Moq;
using NUnit.Framework;

namespace Microsoft.RelInfra.Storage.Test
{
    [TestFixture]
    class AzureLeaseTest
    {
        [Test]
        public async Task TestAcquireLeaseAsync()
        {
            var lockStorage = new Mock<ILeasableStorage>();

            lockStorage.SetupSequence(mock => mock.AcquireLeaseAsync(It.IsAny<string>(), It.IsAny<TimeSpan>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .Returns(Task.FromResult("dummy")) //succesful run
                // Conflict exception won't retry since use default retry policy
                .Throws(new RelInfraStorageException(StorageError.Conflict))//fail run 1
                // Argument exception will retry one time since use default retry policy, so we should mock AcquireLeaseAsync twice
                .Throws(new ArgumentException("command"))//exception run 1
                .Throws(new ArgumentException("command"));//exception run 1

            var leaseprovider = new AzureAutoRenewLease(lockStorage.Object, "DumyResourceId", new TimeSpan(0, 0, 50));
            Assert.IsTrue(await leaseprovider.AcquireLeaseAsync());
            Assert.IsFalse(await leaseprovider.AcquireLeaseAsync());
            Func<Task<bool>> action = async () => await leaseprovider.AcquireLeaseAsync();

            Assert.That(action,
                Throws.TypeOf<ArgumentException>().With.Message.Contains("command"));
        }

        [Test]
        public async Task TestTryAcquireLeaseAsyncWithRetryPolicy()
        {
            var lockStorage = new Mock<ILeasableStorage>();

            var retrypolice = new LinearRetryPolicy(TimeSpan.Parse("0:0:0.100", CultureInfo.InvariantCulture), 2);

            lockStorage.SetupSequence(mock => mock.AcquireLeaseAsync(It.IsAny<string>(), It.IsAny<TimeSpan>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .Throws(new RelInfraStorageException(StorageError.Conflict))
                .Returns(Task.FromResult("dummy")) //succesful run after two time retry
                .Throws(new RelInfraStorageException(StorageError.Unauthorized)) //failed run, no retry
                .Returns(Task.FromResult("dummy")) //succesful run
                .Throws(new ArgumentException("command"))//
                .Returns(Task.FromResult("dummy")); //succesful run after one non-RelInfraStorageException exception retry

            var leaseprovider = new AzureAutoRenewLease(lockStorage.Object, "DumyResourceId", new TimeSpan(0, 0, 50));

            Assert.IsTrue(await leaseprovider.TryAcquireLeaseAsync(retrypolice, leaseprovider.ShouldRetryConflictException));
            Assert.IsFalse(await leaseprovider.TryAcquireLeaseAsync(retrypolice, leaseprovider.ShouldRetryConflictException));
            Assert.IsTrue(await leaseprovider.TryAcquireLeaseAsync(retrypolice, leaseprovider.ShouldRetryConflictException));
            Assert.IsTrue(await leaseprovider.TryAcquireLeaseAsync(retrypolice, leaseprovider.ShouldRetryConflictException));
        }

        [Test]
        public async Task TestTryAcquireLeaseAsync()
        {
            var lockStorage = new Mock<ILeasableStorage>();

            lockStorage.SetupSequence(mock => mock.AcquireLeaseAsync(It.IsAny<string>(), It.IsAny<TimeSpan>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .Throws(new RelInfraStorageException(StorageError.Conflict)) //failed run, no retry
                .Returns(Task.FromResult("dummy")) //succesful run
                .Throws(new ArgumentException("command"))
                .Returns(Task.FromResult("dummy")); //succesful run after one retry

            var leaseprovider = new AzureAutoRenewLease(lockStorage.Object, "DumyResourceId", new TimeSpan(0, 0, 50));

            Assert.IsFalse(await leaseprovider.TryAcquireLeaseAsync());
            Assert.IsTrue(await leaseprovider.TryAcquireLeaseAsync());
            Assert.IsTrue(await leaseprovider.TryAcquireLeaseAsync());
        }
    }
}
