﻿using System;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using NUnit.Framework;

namespace Microsoft.RelInfra.Storage.Test
{
    [TestFixture]
    class LocalFileStorageTest
    {
        [Test]
        public async Task LocalFilesTestHappyPathAsync()
        {
            string rootFolder = ".testplace";
            var fs = new LocalFileStorage(basePath: rootFolder);

            if (Directory.Exists(rootFolder))
            {
                foreach (var file in await fs.ListDirectoryAsync(rootFolder, recursive: true, fileCountLimit: 1000))
                {
                    await fs.DeleteFileAsync(file);
                }
            }

            //
            //   IStorage
            //
            Assert.IsFalse(await fs.ExistsAsync("container1", "resource1"));

            var originalTime = DateTime.Now;
            await fs.AddAsync("container1", "resource1", new Storable<DateTime>(originalTime));
            Storable<DateTime> readTime = await fs.GetAsync<DateTime>("container1", "resource1");

            Assert.AreEqual(originalTime, readTime.Entity);
            await fs.DeleteAsync("container1", "resource1");

            //
            //   IFileStorage
            //
            String testContent = "This is test content";
            string path1 = rootFolder + @"\TestFolder\1\test.txt";
            string path2 = rootFolder + @"\TestFolder\2\test.txt";
            await fs.CreateFileAsync(path1, testContent);
            using (var sr = new StreamReader(await fs.ReadStreamAsync(path1, offset: 0, length: -1)))
            {
                Assert.AreEqual(testContent, sr.ReadToEnd());
            }

            await fs.CopyFileAsync(sourcePath: path1, destPath: path2, cts: CancellationToken.None);
            await fs.AppendAsync(path: path2, text: "newText", compression: false);
            await fs.AppendLineAsync(path: path2, line: "anotherline");
            using (var sr = new StreamReader(await fs.ReadStreamAsync(path2, offset: 0, length: -1)))
            {
                Assert.AreEqual(testContent + "newText" + "anotherline\r\n", sr.ReadToEnd());
            }


            var files = (await fs.ListDirectoryAsync(Path.Combine(rootFolder, "TestFolder"), recursive: true, fileCountLimit: 100)).ToList();
            Assert.AreEqual(2, files.Count);
            Assert.IsTrue(files.Any(file => file.EndsWith(@"\TestFolder\1\test.txt")));
            Assert.IsTrue(files.Any(file => file.EndsWith(@"\TestFolder\2\test.txt")));
            await fs.DeleteFileAsync(path1);
            await fs.DeleteFileAsync(path2);
        }
    }
}
