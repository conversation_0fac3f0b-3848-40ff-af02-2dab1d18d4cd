﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputPath>$(BaseTargetDir)\unittest\aether\AetherBackend\Microsoft.RelInfra.Storage.Test</OutputPath>
    <RootNamespace>Microsoft.RelInfra.Storage.Test</RootNamespace>
    <AssemblyName>Microsoft.RelInfra.Storage.Test</AssemblyName>
    <TargetFramework>net6.0</TargetFramework>
    <ResolveAssemblyWarnOrErrorOnTargetArchitectureMismatch>None</ResolveAssemblyWarnOrErrorOnTargetArchitectureMismatch>
  </PropertyGroup>
  <Import Project="..\..\..\..\QTest.props" />
  <ItemGroup>
    <PackageReference Include="Microsoft.Data.Edm" />
    <PackageReference Include="Microsoft.Data.OData" />
    <PackageReference Include="NUnit" />
    <PackageReference Include="NUnit3TestAdapter" />
    <PackageReference Include="WindowsAzure.Storage" />
    <PackageReference Include="Azure.Identity" />
    <PackageReference Include="Azure.Security.KeyVault.Secrets" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" />
    <PackageReference Include="coverlet.collector" />
    <PackageReference Include="Microsoft.IdentityModel.Authorization.Azure" NoWarn="NU1701" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\..\..\backendV2\shared\Microsoft.Aether.BackendCommon\Microsoft.Aether.BackendCommon.csproj" />
    <ProjectReference Include="..\..\..\..\backendV2\shared\Microsoft.Aether.TokenProvider\Microsoft.Aether.TokenProvider.csproj" />
    <ProjectReference Include="..\..\..\..\backendV2\shared\Relinfra.Storage.CosmosDb\Relinfra.Storage.CosmosDb.csproj" />
    <ProjectReference Include="..\..\..\RelInfra.Common.Library\src\Common\RelInfra.Common.csproj" />
    <ProjectReference Include="..\..\..\RelInfra.Instrumentation.Library\src\Microsoft.RelInfra.Instrumentation.csproj" />
    <ProjectReference Include="..\..\src\Mocks\RelInfra.Storage.Mocks.csproj" />
    <ProjectReference Include="..\..\src\Storage\RelInfra.Storage.csproj" />
  </ItemGroup>
</Project>
