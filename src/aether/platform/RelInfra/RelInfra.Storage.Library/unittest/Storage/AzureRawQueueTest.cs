﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Aether.TokenProvider;
using Microsoft.WindowsAzure.Storage;
using NUnit.Framework;

namespace Microsoft.RelInfra.Storage.Test
{
    [TestFixture]
    [Category("QTestSkip")]
    public class AzureRawQueueTest
    {
        private const string QueueName = "queuetest";

        private AzureRawQueue _azureQueue;

        [SetUp]
        public void TestSetup()
        {
            _azureQueue = new AzureRawQueue(
                connectionString: AzureCredHelper.GetDevAzureConnectionString(),
                queueName: QueueName);

            // make sure queue is empty before tests
            Storable<string> entity;
            while ((entity = _azureQueue.GetAsync().Result) != null)
            {
                _azureQueue.DeleteAsync(entity.Id, entity.Etag).Wait();
            }
        }

        [Test]
        public async Task Test_AddMessage()
        {
            Storable<string> storableObj = await AddTestMessageToQueue("test message", TimeSpan.FromSeconds(10));
            Assert.IsNotNull(storableObj);
            Assert.That(storableObj.Id, Is.Not.Null.And.Not.Empty);
            Assert.That(storableObj.Etag, Is.Not.Null.And.Not.Empty);
            Assert.AreEqual(QueueName, storableObj.ContainerId);

            await _azureQueue.DeleteAsync(storableObj.Id, storableObj.Etag);
        }

        [Test]
        public async Task Test_GetMessage()
        {
            Random rnd = new Random();
            string msg = "test message " + rnd.Next();
            Storable<string> storableObj = await AddTestMessageToQueue(msg, TimeSpan.FromSeconds(5));
            Assert.IsNotNull(storableObj);
            Assert.That(storableObj.Id, Is.Not.Null.And.Not.Empty);
            Assert.That(storableObj.Etag, Is.Not.Null.And.Not.Empty);
            Assert.AreEqual(QueueName, storableObj.ContainerId);

            // message shouldn't be visible in a queue because leaseTime is 5 sec
            storableObj = await _azureQueue.GetAsync();
            Assert.IsTrue(storableObj == null || storableObj.Entity != msg);

            await Task.Delay(5000);

            // message should be visible in a queue now
            storableObj = await _azureQueue.GetAsync();
            Assert.IsTrue(storableObj != null && storableObj.Entity == msg);

            await _azureQueue.DeleteAsync(storableObj.Id, storableObj.Etag);
        }

        [Test]
        public async Task Test_GetMessages()
        {
            TimeSpan leaseTime = TimeSpan.FromSeconds(5);

            IEnumerable<Storable<string>> testEmptyQueue = await _azureQueue.GetMessagesAsync(5, TimeSpan.FromSeconds(30));
            Assert.IsEmpty(testEmptyQueue);

            // put 3 message into the queue
            await _azureQueue.PutAsync(new Storable<string> { Entity = "Test1" });
            await _azureQueue.PutAsync(new Storable<string> { Entity = "Test2" });
            await _azureQueue.PutAsync(new Storable<string> { Entity = "Test3" });

            IEnumerable<Storable<string>> messages = (await _azureQueue.GetMessagesAsync(5, TimeSpan.FromSeconds(30))).ToList();

            Assert.AreEqual(3, messages.Count());
            Assert.IsNotNull(messages.FirstOrDefault(s => s.Entity == "Test1"));
            Assert.IsNotNull(messages.FirstOrDefault(s => s.Entity == "Test2"));
            Assert.IsNotNull(messages.FirstOrDefault(s => s.Entity == "Test3"));
            Assert.IsTrue(messages.All(m => m.ContainerId == QueueName));
        }

        [Test]
        public async Task Test_GetLargeAmountOfMessages()
        {
            const int numberOfMessages = 40;

            IEnumerable<Storable<string>> testEmptyQueue = await _azureQueue.GetMessagesAsync(numberOfMessages, TimeSpan.FromSeconds(30));
            Assert.IsEmpty(testEmptyQueue);

            // put 3 message into the queue
            for (int i = 0; i < numberOfMessages; i++)
            {
                await _azureQueue.PutAsync(new Storable<string> { Entity = "Test" + i });
            }
            IEnumerable<Storable<string>> messages = (await _azureQueue.GetMessagesAsync(numberOfMessages, TimeSpan.FromSeconds(30))).ToList();

            Assert.AreEqual(numberOfMessages, messages.Count());
            for (int i = 0; i < numberOfMessages; i++)
            {
                Assert.IsNotNull(messages.FirstOrDefault(s => s.Entity == "Test" + i));
            }

            foreach (Storable<string> storable in messages)
            {
                await _azureQueue.DeleteAsync(storable.Id, storable.Etag);
            }
        }

        [Test]
        public async Task Test_DeleteMessage()
        {
            Random rnd = new Random();
            string msg = "test message " + rnd.Next();
            Storable<string> storableObj = await AddTestMessageToQueue(msg, TimeSpan.FromSeconds(10));
            Assert.IsNotNull(storableObj);
            Assert.That(storableObj.Id, Is.Not.Null.And.Not.Empty);
            Assert.That(storableObj.Etag, Is.Not.Null.And.Not.Empty);

            Assert.IsTrue(await _azureQueue.DeleteAsync(storableObj.Id, storableObj.Etag));
            // if the message has already been deleted DeleteAsync returns true
            Assert.IsTrue(await _azureQueue.DeleteAsync(storableObj.Id, storableObj.Etag));
        }

        [Test]
        public async Task Test_UpdateMessage()
        {
            Random rnd = new Random();
            string msg = "test message " + rnd.Next();
            Storable<string> storableObj = await AddTestMessageToQueue(msg, TimeSpan.FromSeconds(10));
            Assert.IsNotNull(storableObj);
            Assert.That(storableObj.Id, Is.Not.Null.And.Not.Empty);
            Assert.That(storableObj.Etag, Is.Not.Null.And.Not.Empty);

            var updatedObj = await _azureQueue.UpdateMessageAsync(storableObj, leaseTime: TimeSpan.FromSeconds(20), flags: MessageUpdateFlags.UpdateVisibility);
            Assert.IsTrue(await _azureQueue.DeleteAsync(updatedObj.Id, updatedObj.Etag));

            StorageException lastException = null;
            try
            {
                await _azureQueue.UpdateMessageAsync(storableObj, leaseTime: TimeSpan.FromSeconds(20), flags: MessageUpdateFlags.UpdateVisibility);
            }
            catch (StorageException ex)
            {
                lastException = ex;
            }

            Assert.AreEqual(404, lastException.RequestInformation.HttpStatusCode);
        }

        private async Task<Storable<string>> AddTestMessageToQueue(string message, TimeSpan leaseTime)
        {
            var storableObj = new Storable<string> { Entity = message };

            await _azureQueue.PutAsync(storableObj);

            return await _azureQueue.GetAsync(leaseTime);
        }

    }
}
