﻿using System;
using Microsoft.RelInfra.Common.RetryPolicy;
using Microsoft.WindowsAzure.Storage.RetryPolicies;
using NUnit.Framework;

namespace Microsoft.RelInfra.Storage.Test
{
    [TestFixture]
    class AzureRetryPolicyTest
    {
        [Test]
        public void TestWrapRelInfraRetry()
        {
            var retryPolicy = new AzureRetryPolicy(new TestRetryFor503Policy(), additionalInfo: $"storageDiagMeta");
            var use = retryPolicy.CreateInstance();

            Assert.False(use.ShouldRetry(currentRetryCount: 0, statusCode: 0, new Exception(), out var retry, operationContext: null));
            Assert.True(use.ShouldRetry(currentRetryCount: 0, statusCode: 503, new Exception(), out retry, operationContext: null));
        }

        [Test]
        public void TestWrapCompositeRetry()
        {
            var retryPolicy = new AzureRetryPolicy(new TestRetryFor503Policy(), new ExponentialRetry(), additionalInfo: $"storageDiagMeta");
            var use = retryPolicy.CreateInstance();

            Assert.True(use.ShouldRetry(currentRetryCount: 0, statusCode: 0, new Exception(), out var retry, operationContext: null));
            Assert.True(use.ShouldRetry(currentRetryCount: 0, statusCode: 503, new Exception(), out retry, operationContext: null));
        }

        sealed class TestRetryFor503Policy : ExponentialBackoffRetryPolicy
        {
            public override bool ShouldRetry(int currentRetryCount, int statusCode, Exception lastException, out TimeSpan retryInterval)
            {
                retryInterval = TimeSpan.Zero;
                return statusCode == 503 ? base.ShouldRetry(currentRetryCount, out retryInterval) : false;
            }

            public override object Clone()
            {
                return new TestRetryFor503Policy();
            }
        }
    }
}
