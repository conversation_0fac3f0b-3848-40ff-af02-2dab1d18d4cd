﻿using System;
using System.Threading.Tasks;
using Azure.Identity;
using NUnit.Framework;

namespace Microsoft.RelInfra.Storage.Test
{
    [TestFixture]
    [Category("QTestSkip")]
    public class AzureQueueMigrationCompatibilityTest
    {
        private const string AccountName = "<[no-checkin] replace-with-accessible-storage-account>";
        private const string QueueStringName = "queuestringtest";
        private const string QueueEntityName = "queueentitytest";
        private const string ConnectionString = "<[no-checkin] replace-with-storage-account-connection-string>";

        private readonly Uri ServiceUri = new Uri($"https://{AccountName}.queue.core.windows.net");
        private readonly TimeSpan _leaseTime = TimeSpan.FromSeconds(5);
        private readonly Storable<string> _defaultMessageString = new Storable<string> { Entity = "test message" };
        private readonly Storable<QueueTestEntity> _defaultMessageEntity = new Storable<QueueTestEntity> { Entity = new QueueTestEntity { Name = "name", Count = 1 } };

        private AzureQueue<string> _azureStringQueue;    // Old SDK client
        private AzureQueueV2<string> _azureStringQueueV2; // New SDK client

        private AzureQueue<QueueTestEntity> _azureEntityQueue;    // Old SDK client
        private AzureQueueV2<QueueTestEntity> _azureEntityQueueV2; // New SDK client

        [OneTimeSetUp]
        public void SetUp()
        {
            _azureStringQueue = new AzureQueue<string>(ConnectionString, QueueStringName);
            _azureStringQueue.InitializeQueueAsync().Wait();
            _azureEntityQueue = new AzureQueue<QueueTestEntity>(ConnectionString, QueueEntityName);
            _azureEntityQueue.InitializeQueueAsync().Wait();

            var credential = new DefaultAzureCredential();
            _azureStringQueueV2 = new AzureQueueV2<string>(ServiceUri, QueueStringName, credential);
            _azureEntityQueueV2 = new AzureQueueV2<QueueTestEntity>(ServiceUri, QueueEntityName, credential);
            _azureEntityQueueV2.InitializeQueueAsync().Wait();
        }

        [SetUp]
        public async Task TestSetup()
        {
            // make sure queue is empty before tests.
            await Task.Delay(_leaseTime);

            Storable<string> message;
            while ((message = await _azureStringQueueV2.GetAsync(_leaseTime)) != null)
            {
                await _azureStringQueueV2.DeleteAsync(message);
            }

            Storable<QueueTestEntity> entity;
            while ((entity = await _azureEntityQueueV2.GetAsync(_leaseTime)) != null)
            {
                await _azureEntityQueueV2.DeleteAsync(entity);
            }
        }

        [Test]
        public async Task Test_AddStringMessageWithOldSdkAndGetWithNewSdkAsync()
        {
            // Add with old sdk
            await _azureStringQueue.PutAsync(_defaultMessageString);
            // Get with new sdk
            var message = await _azureStringQueueV2.GetAsync(_leaseTime);

            Assert.IsNotNull(message);
            Assert.That(message.Id, Is.Not.Null.And.Not.Empty);
            Assert.That(message.Etag, Is.Not.Null.And.Not.Empty);
            Assert.AreEqual(QueueStringName, message.ContainerId);
            Assert.AreEqual(_defaultMessageString.Entity, message.Entity);

            // Delete with old sdk
            await _azureStringQueue.DeleteAsync(message.Id, message.Etag);
            await Task.Delay(_leaseTime);
            // Get with new sdk
            message = await _azureStringQueueV2.GetAsync(_leaseTime);
            Assert.IsNull(message);
        }

        [Test]
        public async Task Test_AddStringMessageWithNewSdkAndGetWithOldSdkAsync()
        {
            // Add with new sdk
            await _azureStringQueueV2.PutAsync(_defaultMessageString);
            // Get with old sdk
            var message = await _azureStringQueue.GetAsync(_leaseTime);

            Assert.IsNotNull(message);
            Assert.That(message.Id, Is.Not.Null.And.Not.Empty);
            Assert.That(message.Etag, Is.Not.Null.And.Not.Empty);
            Assert.AreEqual(QueueStringName, message.ContainerId);
            Assert.AreEqual(_defaultMessageString.Entity, message.Entity);

            // Delete with new sdk
            await _azureStringQueueV2.DeleteAsync(message.Id, message.Etag);
            await Task.Delay(_leaseTime);
            // Get with old sdk
            message = await _azureStringQueue.GetAsync(_leaseTime);
            Assert.IsNull(message);
        }

        [Test]
        public async Task Test_AddEntityMessageWithOldSdkAndGetWithNewSdkAsync()
        {
            // Add with old sdk
            await _azureEntityQueue.PutAsync(_defaultMessageEntity);
            // Get with new sdk
            var message = await _azureEntityQueueV2.GetAsync(_leaseTime);

            Assert.IsNotNull(message);
            Assert.That(message.Id, Is.Not.Null.And.Not.Empty);
            Assert.That(message.Etag, Is.Not.Null.And.Not.Empty);
            Assert.AreEqual(QueueEntityName, message.ContainerId);
            Assert.AreEqual(_defaultMessageEntity.Entity.Name, message.Entity.Name);
            Assert.AreEqual(_defaultMessageEntity.Entity.Count, message.Entity.Count);

            // Delete with old sdk
            await _azureEntityQueue.DeleteAsync(message.Id, message.Etag);
            await Task.Delay(_leaseTime);
            // Get with new sdk
            message = await _azureEntityQueueV2.GetAsync(_leaseTime);
            Assert.IsNull(message);
        }

        [Test]
        public async Task Test_AddEntityMessageWithNewSdkAndGetWithOldSdkAsync()
        {
            // Add with new sdk
            await _azureEntityQueueV2.PutAsync(_defaultMessageEntity);
            // Get with old sdk
            var message = await _azureEntityQueue.GetAsync(_leaseTime);

            Assert.IsNotNull(message);
            Assert.That(message.Id, Is.Not.Null.And.Not.Empty);
            Assert.That(message.Etag, Is.Not.Null.And.Not.Empty);
            Assert.AreEqual(QueueEntityName, message.ContainerId);
            Assert.AreEqual(_defaultMessageEntity.Entity.Name, message.Entity.Name);
            Assert.AreEqual(_defaultMessageEntity.Entity.Count, message.Entity.Count);

            // Delete with new sdk
            await _azureEntityQueueV2.DeleteAsync(message.Id, message.Etag);
            await Task.Delay(_leaseTime);
            // Get with old sdk
            message = await _azureEntityQueue.GetAsync(_leaseTime);
            Assert.IsNull(message);
        }

        [Test]
        public async Task Test_UpdateMessageWithOldSdkAndDelete()
        {
            // Add with new sdk
            await _azureStringQueueV2.PutAsync(_defaultMessageString);
            // Get with old sdk
            var message = await _azureStringQueue.GetAsync(_leaseTime);
            // Update message visibility with old sdk
            var updatedMessage = await _azureStringQueue.UpdateMessageAsync(message, _leaseTime, MessageUpdateFlags.UpdateVisibility);
            // Message PopReceipt/ETag is changed
            Assert.AreNotEqual(message.Etag, updatedMessage.Etag);

            // Delete with old sdk
            await _azureStringQueue.DeleteAsync(updatedMessage);
            await Task.Delay(_leaseTime);
            message = await _azureStringQueue.GetAsync(_leaseTime);
            Assert.IsNull(message);
        }

        [Test]
        public async Task Test_UpdateMessageWithNewSdkAndDelete()
        {
            // Add with old sdk
            await _azureStringQueue.PutAsync(_defaultMessageString);
            // Get with new sdk
            var message = await _azureStringQueueV2.GetAsync(_leaseTime);
            // Update message visibility with new sdk
            var updatedMessage = await _azureStringQueueV2.UpdateMessageAsync(message, _leaseTime, MessageUpdateFlags.UpdateVisibility);
            // Message PopReceipt/ETag is changed
            Assert.AreNotEqual(message.Etag, updatedMessage.Etag);

            // Delete with new sdk
            await _azureStringQueueV2.DeleteAsync(updatedMessage);
            await Task.Delay(_leaseTime);
            message = await _azureStringQueueV2.GetAsync(_leaseTime);
            Assert.IsNull(message);
        }
    }

    internal class QueueTestEntity
    {
        public string Name { get; set; }

        public int Count { get; set; }
    }
}
