﻿using System;
using System.IO;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Aether.TokenProvider;
using Microsoft.RelInfra.Instrumentation;
using Microsoft.RelInfra.Instrumentation.Test.Mocks;
using NUnit.Framework;

namespace Microsoft.RelInfra.Storage.Test
{
    [TestFixture]
    [Category("QTestSkip")]
    public class AzureSasUriClientTest : BaseStorageTests
    {
        private AzureBlobStorage _blobStorage;
        private AzureSasUriClient _sasUriClient;

        public AzureSasUriClientTest()
            : base(TestSettings.BlobName)
        {
        }

        [SetUp]
        public void TestSetup()
        {
            _blobStorage = new AzureBlobStorage(
                connectionString: AzureCredHelper.GetDevAzureConnectionString(),
                numRetries: 5,
                intervalBetweenRetries: new TimeSpan(0, 0, 0, 10),
                counterManager: null);
            _storage = _blobStorage;
            _blobStorage.InitializeAsync(TestSettings.BlobName).Wait();
        }

        [Test]
        public async Task TestUploadToWritableUriAsync()
        {
            var resourceId = Guid.NewGuid().ToString();
            string testMessage0 = string.Empty;
            string testMessage1 = "test string1";
            string testMessage2 = "test string2";

            using (var testStream0 = new MemoryStream(Encoding.UTF8.GetBytes(testMessage0)))
            {
                await _blobStorage.AppendStreamAsync(TestSettings.BlobName, resourceId, testStream0);
            }

            Uri writableSasUri = _blobStorage.GetWritableBlobUri(TestSettings.BlobName, resourceId,
                DateTimeOffset.UtcNow.AddSeconds(60));

            _sasUriClient = new AzureSasUriClient(new CounterManager("test", new MockCounterFactory()));
            await _sasUriClient.UploadToWritableUriAppendBlobAsync(writableSasUri.AbsoluteUri, testMessage1);
            Storable<string> objFromBlob = await _blobStorage.GetBlobStringAsync(TestSettings.BlobName, resourceId);
            Assert.AreEqual(testMessage1, objFromBlob.Entity);

            await _sasUriClient.UploadToWritableUriAppendBlobAsync(writableSasUri.AbsoluteUri, testMessage2);
            objFromBlob = await _blobStorage.GetBlobStringAsync(TestSettings.BlobName, resourceId);
            Assert.AreEqual(testMessage2, objFromBlob.Entity);

            await _storage.TryDeleteAsync(TestSettings.BlobName, resourceId);
        }

        [Test]
        public async Task TestUploadToWritableUriFromFileAsync()
        {
            var resourceId = Guid.NewGuid().ToString();
            string tempFile = resourceId;
            string testMessage = "test string";
            File.WriteAllText(tempFile, testMessage);
            
            Uri writableSasUri = _blobStorage.GetWritableBlobUri(TestSettings.BlobName, resourceId,
                DateTimeOffset.UtcNow.AddMinutes(5));

            _sasUriClient = new AzureSasUriClient(new CounterManager("test", new MockCounterFactory()));
            await _sasUriClient.UploadToWritableUriFromFileBlockBlobAsync(writableSasUri.AbsoluteUri, tempFile, CancellationToken.None);
            Uri blobReadableUri = _blobStorage.GetBlobUri(TestSettings.BlobName, resourceId, DateTimeOffset.Now.AddHours(1));
            string content;
            using (Stream stream = await _sasUriClient.GetStreamAsync(blobReadableUri.AbsoluteUri))
            {
                using (StreamReader reader = new StreamReader(stream))
                {
                    content = reader.ReadToEnd();
                }
            }

            Assert.AreEqual(testMessage, content);
            
            await _storage.TryDeleteAsync(TestSettings.BlobName, resourceId);
            File.Delete(tempFile);
        }
        

        [Test]
        public async Task TestAppendToWritableUriAsync()
        {
            var resourceId = Guid.NewGuid().ToString();
            string testMessage0 = string.Empty;
            string testMessage1 = "test string1";
            string testMessage2 = "test string2";

            using (var testStream0 = new MemoryStream(Encoding.UTF8.GetBytes(testMessage0)))
            {
                await _blobStorage.AppendStreamAsync(TestSettings.BlobName, resourceId, testStream0);
            }

            Uri writableSasUri = _blobStorage.GetWritableBlobUri(TestSettings.BlobName, resourceId,
                DateTimeOffset.UtcNow.AddSeconds(60));

            _sasUriClient = new AzureSasUriClient(new CounterManager("test", new MockCounterFactory()));
            await _sasUriClient.AppendToWritableUriAsync(writableSasUri.AbsoluteUri, testMessage1);
            Storable<string> objFromBlob = await _blobStorage.GetBlobStringAsync(TestSettings.BlobName, resourceId);
            Assert.AreEqual(testMessage1, objFromBlob.Entity);

            await _sasUriClient.AppendToWritableUriAsync(writableSasUri.AbsoluteUri, testMessage2);
            objFromBlob = await _blobStorage.GetBlobStringAsync(TestSettings.BlobName, resourceId);
            Assert.AreEqual(testMessage1 + testMessage2, objFromBlob.Entity);

            await _storage.TryDeleteAsync(TestSettings.BlobName, resourceId);
        }
    }
}
