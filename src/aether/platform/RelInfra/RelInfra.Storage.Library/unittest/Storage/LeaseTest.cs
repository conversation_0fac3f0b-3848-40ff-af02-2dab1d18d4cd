﻿using System;
using System.Runtime.Serialization;
using System.Threading.Tasks;
using Microsoft.RelInfra.Storage.Test.Mocks;
using NUnit.Framework;

namespace Microsoft.RelInfra.Storage.Test
{
    [DataContract]
    public class HealthCheckerLeaseToken : LockableEntity
    {
        [DataMember] public string Machine { get; set; }

        public HealthCheckerLeaseToken()
        {
            Machine = "default";
        }
    }

    [TestFixture]
    class LeaseTest
    {
        [Test]
        public async Task TestContention()
        {
            var storage = new AzureTableStorageStaticMock("TestTable");
            var leaseFactory = new StorageLeaseFactory<HealthCheckerLeaseToken>(storage, "TestTable");

            {
                var defautlTokenContent = new HealthCheckerLeaseToken()
                {
                    Machine = "TechMachine"
                };
                
                LeaseToken<HealthCheckerLeaseToken> leaseSuccess = await leaseFactory.AcquireLeaseAsync("Token", defautlTokenContent, 30000);
                Assert.NotNull(leaseSuccess);

                using (leaseSuccess)
                {
                    var leaseFail = await leaseFactory.AcquireLeaseAsync("Token", 30000);
                    Assert.IsNull(leaseFail);
                }

                await Task.Delay(TimeSpan.FromSeconds(1));

                LeaseToken<HealthCheckerLeaseToken> leaseSuccess2 = await leaseFactory.AcquireLeaseAsync("Token", 30000);
                Assert.NotNull(leaseSuccess2);
                leaseSuccess2.Release();
            }

            await Task.Delay(TimeSpan.FromSeconds(1));

            var leaseSuccess3 = await leaseFactory.AcquireLeaseAsync("Token", 30000);
            Assert.NotNull(leaseSuccess3);
        }

        [Test]
        public async Task TestLeaseRenewal()
        {
            var defautlTokenContent = new HealthCheckerLeaseToken()
            {
                Machine = "TechMachine"
            };
                
            var storage = new AzureTableStorageStaticMock("TestTable1");
            var leaseFactory = new StorageLeaseFactory<HealthCheckerLeaseToken>(storage, "TestTable1");

            {
                LeaseToken<HealthCheckerLeaseToken> leaseSuccess = await leaseFactory.AcquireLeaseAsync("Token", 4);
                Assert.NotNull(leaseSuccess);

                using (leaseSuccess)
                {
                    var leaseFail = await leaseFactory.AcquireLeaseAsync("Token", 30000);
                    Assert.IsNull(leaseFail);

                    // sleep several updates of the lease
                    await Task.Delay(TimeSpan.FromSeconds(20));

                    leaseFail = await leaseFactory.AcquireLeaseAsync("Token", 30000);
                    Assert.IsNull(leaseFail);
                }
            }
        }
    }
}
