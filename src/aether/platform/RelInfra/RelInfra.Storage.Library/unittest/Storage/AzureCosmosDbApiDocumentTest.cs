﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using Microsoft.Aether.TokenProvider;
using Microsoft.Azure.Cosmos;
using Microsoft.RelInfra.Instrumentation;
using Microsoft.RelInfra.Instrumentation.Test.Mocks;
using Microsoft.RelInfra.Storage.Exceptions;
using Newtonsoft.Json;
using NUnit.Framework;
using Relinfra.Storage.CosmosDb;

namespace Microsoft.RelInfra.Storage.Test
{
    [TestFixture]
    [Category("QTestSkip")]
    public class AzureCosmosDbApiDocumentTest
    {
        private string _collectionId;
        private AzureCosmosDbContainerApi _container;

        private class TestAddress
        {
            public string State { get; set; }
            public List<int> ZipCode { get; set; }
        }

        private class TestPersonDocument : ICosmosDbEntity
        {
            [JsonProperty(PropertyName = "id")]
            public string Id { get; set; }

            public string PartitionKey
            {
                get;set;
            }

            public string GetPartitionKey()
            {
                return PartitionKey;
            }


            public TestAddress Address { get; set; }
            public string Value { get; set; }

            public bool BoolValue { get; set; }

            public DateTime? TimeValue { get; set; }

        }

        public void TestSetup()
        {
            _collectionId = "unittest";
            _container = new AzureCosmosDbContainerApi(
                endpointUrl: TestSettings.CosmosDbTestUri,
                authToken: AzureCredHelper.GetDevCosmosDbKey(),
                databaseName: "unittest2",
                containerName: _collectionId,
                partitionKeyPath: "/PartitionKey",
                connectionMode: Azure.Cosmos.ConnectionMode.Gateway,
                counterManager: new CounterManager("test", new MockCounterFactory()));
            _container.InitializeAsync().Wait();
        }

        [Test]
        public async Task BasicTest()
        {
            TestSetup();

            TestPersonDocument doc = CreateTestDoc();

            var created = await _container.AddAsync(doc);

            var readDoc = await _container.GetAsync<TestPersonDocument>(created.ContainerId, created.Id);
            Assert.AreEqual(readDoc.ContainerId, doc.PartitionKey);
            Assert.AreEqual(readDoc.Entity.PartitionKey, doc.PartitionKey);
            Assert.AreEqual(readDoc.Entity.Value, doc.Value);
            Assert.AreEqual(readDoc.Id, doc.Id);
            Assert.AreEqual(readDoc.Entity.Id, doc.Id);
            Assert.AreEqual(readDoc.Entity.BoolValue, false);
            Assert.AreEqual(readDoc.Entity.TimeValue, null);

            created.Entity.Value = "2";
            var updated = await _container.UpdateAsync(created);
            Assert.AreEqual(updated.Entity.Value, created.Entity.Value);
        }

        private TestPersonDocument CreateTestDoc()
        {
            var doc = new TestPersonDocument()
            {
                Id = Guid.NewGuid().ToString(),
                PartitionKey = Guid.NewGuid().ToString(),
                Value = "1",
                Address = new TestAddress() { ZipCode = new List<int> { 123, 456 }, State = "wa" }
            };

            return doc;
        }

        [Test]
        public async Task TestUpdateWithOutdatedEtag()
        {
            TestSetup();
            TestPersonDocument doc = CreateTestDoc();

            var created = await _container.AddAsync(doc);
            await _container.UpdateAsync(created);
            var e = Assert.ThrowsAsync<RelInfraStorageException>(async() => await _container.UpdateAsync(created));
            Assert.AreEqual(((CosmosException)e.InnerException).StatusCode, HttpStatusCode.PreconditionFailed);
        }

        [Test]
        public async Task TestAddExistingDocument()
        {
            TestSetup();
            TestPersonDocument doc = CreateTestDoc();

            await _container.AddAsync(doc);

            var e = Assert.ThrowsAsync<RelInfraStorageException>(async () => await _container.AddAsync(doc));
            Assert.AreEqual(((CosmosException)e.InnerException).StatusCode, HttpStatusCode.Conflict);
        }

        [Test]
        public void TestUpdateNonExistent()
        {
            TestSetup();
            TestPersonDocument doc = CreateTestDoc();

            var e = Assert.ThrowsAsync<RelInfraStorageException>(async () => await _container.UpdateAsync(new Storable<TestPersonDocument>(doc)
            {
                ContainerId = doc.PartitionKey,
                Id = doc.Id
            }));
            Assert.AreEqual(e.Error, StorageError.NotFound);
        }

        [Test]
        public async Task TestPut()
        {
            TestSetup();
            TestPersonDocument doc = CreateTestDoc();
            var updated = await _container.PutAsync(new Storable<TestPersonDocument>(doc)
            {
                ContainerId = doc.PartitionKey,
                Id = doc.Id
            });
            updated.Entity.Value = "3";
            await _container.PutAsync(updated);

        }


        [Test]
        public async Task TestGetPartition()
        {
            TestSetup();
            TestPersonDocument doc = CreateTestDoc();
            TestPersonDocument doc2 = CreateTestDoc();
            string partitionKey = doc.PartitionKey;
            doc2.PartitionKey = partitionKey;
            var insertedDoc1 = await _container.AddAsync(doc);
            var insertedDoc2 = await _container.AddAsync(doc2);
            var readDocs = await _container.GetPartitionAsync<TestPersonDocument>(partitionKey);
            Assert.AreEqual(readDocs.Count(), 2);
        }

        [Test]
        public async Task QueryTest()
        {
            TestSetup();
            int zipCode = Guid.NewGuid().GetHashCode();
            TestPersonDocument doc1 = CreateTestDoc();
            TestPersonDocument doc2 = CreateTestDoc();
            TestPersonDocument doc3 = CreateTestDoc();
            doc1.Value = "2";
            doc1.Address.ZipCode.Add(zipCode);
            doc2.Address.ZipCode.Add(zipCode);
            doc3.Address.ZipCode = new List<int> { 999 };

            string state = Guid.NewGuid().ToString();
            doc1.Address.State = state;
            doc2.Address.State = state;
            doc3.Address.State = state;

            Console.WriteLine($"doc1 id: {doc1.Id}");
            Console.WriteLine($"doc2 id: {doc2.Id}");
            Console.WriteLine($"doc3 id: {doc3.Id}");
            await _container.AddAsync(doc1);
            await _container.AddAsync(doc2);
            await _container.AddAsync(doc3);

            var results = await _container.ExecuteQueryAsync<TestPersonDocument>($"SELECT * FROM d WHERE ARRAY_CONTAINS(d.Address.ZipCode, {zipCode})");
            Assert.AreEqual(2, results.Count());
            Assert.IsTrue(results.Any(r => r.Entity.Id == doc1.Id && r.Id == doc1.Id && r.ContainerId == doc1.PartitionKey));
            Assert.IsTrue(results.Any(r => r.Entity.Id == doc2.Id && r.Id == doc2.Id && r.ContainerId == doc2.PartitionKey));

            results = await _container.ExecuteQueryAsync<TestPersonDocument>($"SELECT * FROM d WHERE LOWER(d.Address.State) = '{state}'");
            Assert.AreEqual(3, results.Count());
            Assert.IsTrue(results.Any(r => r.Entity.Id == doc1.Id && r.Id == doc1.Id));
            Assert.IsTrue(results.Any(r => r.Entity.Id == doc2.Id && r.Id == doc2.Id));
            Assert.IsTrue(results.Any(r => r.Entity.Id == doc3.Id && r.Id == doc3.Id));

            results = await _container.ExecuteQueryAsync<TestPersonDocument>($"SELECT * FROM d WHERE LOWER(d.Address.State) = '{state}'", 2);
            Assert.AreEqual(2, results.Count());
        }

    }
}
