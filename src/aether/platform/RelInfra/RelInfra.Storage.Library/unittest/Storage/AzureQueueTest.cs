﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Aether.TokenProvider;
using Microsoft.WindowsAzure.Storage;
using NUnit.Framework;

namespace Microsoft.RelInfra.Storage.Test
{
    [TestFixture]
    [Category("QTestSkip")]
    class AzureQueueTest
    {
        private const string QueueName = "queuetest";

        private AzureQueue<string> _azureQueue;

        [SetUp]
        public void TestSetup()
        {
            _azureQueue = new AzureQueue<string>(
                connectionString: AzureCredHelper.GetDevAzureConnectionString(),
                queueName: QueueName);
            
            // make sure queue is empty before tests
            Storable<string> entity;
            while ((entity = _azureQueue.GetAsync().Result) != null)
            {
                _azureQueue.DeleteAsync(entity.Id, entity.Etag).Wait();
            }
        }

        [Test]
        public async Task Test_QueueManager()
        {
            var storableObj = new Storable<string> { Entity = "test message from yan" };
            // make sure queue is empty before tests
            Storable<string> entity;
            while ((entity = await _azureQueue.GetAsync()) != null)
            {
                await _azureQueue.DeleteAsync(entity.Id, entity.Etag);
            }

            await _azureQueue.PutAsync(storableObj);

            Storable<string> msg = await _azureQueue.GetAsync(TimeSpan.FromSeconds(30));

            await _azureQueue.UpdateMessageAsync(msg, leaseTime: TimeSpan.FromSeconds(10), flags: MessageUpdateFlags.UpdateVisibility);
            await _azureQueue.UpdateMessageAsync(msg, leaseTime: TimeSpan.FromSeconds(10), flags: MessageUpdateFlags.UpdateVisibility);
            await _azureQueue.UpdateMessageAsync(msg, leaseTime: TimeSpan.FromSeconds(10), flags: MessageUpdateFlags.UpdateVisibility);
            await _azureQueue.UpdateMessageAsync(msg, leaseTime: TimeSpan.FromSeconds(10), flags: MessageUpdateFlags.UpdateVisibility);

            using (var mng = new QueueMessageLifetimeManager<string>(_azureQueue, msg, TimeSpan.FromSeconds(30)))
            {
                while (true)
                {
                    Thread.Sleep(TimeSpan.FromSeconds(1));
                    await mng.CheckAndUpdateExpirationAsync();
                }
            }
        }

        [Test]
        public async Task Test_AddMessage()
        {
            Storable<string> storableObj = await PutAndGetTestMessageToQueueAsync("test message", TimeSpan.FromSeconds(10));
            Assert.IsNotNull(storableObj);
            Assert.That(storableObj.Id, Is.Not.Null.And.Not.Empty);
            Assert.That(storableObj.Etag, Is.Not.Null.And.Not.Empty);
            Assert.AreEqual(QueueName, storableObj.ContainerId);

            await _azureQueue.DeleteAsync(storableObj.Id, storableObj.Etag);
        }

        [Test]
        public async Task Test_GetMessage()
        {
            Random rnd = new Random();
            string msg = "test message " + rnd.Next();
            Storable<string> storableObj = await PutAndGetTestMessageToQueueAsync(msg, TimeSpan.FromSeconds(5));
            Assert.IsNotNull(storableObj);
            Assert.That(storableObj.Id, Is.Not.Null.And.Not.Empty);
            Assert.That(storableObj.Etag, Is.Not.Null.And.Not.Empty);
            Assert.AreEqual(QueueName, storableObj.ContainerId);

            // message shouldn't be visible in a queue because leaseTime is 5 sec
            storableObj = await _azureQueue.GetAsync();
            Assert.IsTrue(storableObj == null || storableObj.Entity != msg);

            await Task.Delay(5000);

            // message should be visible in a queue now
            storableObj = await _azureQueue.GetAsync();
            Assert.IsTrue(storableObj != null && storableObj.Entity == msg);

            await _azureQueue.DeleteAsync(storableObj.Id, storableObj.Etag);
        }

        [Test]
        public async Task Test_GetMessages()
        {
            IEnumerable<Storable<string>> testEmptyQueue = await _azureQueue.GetMessagesAsync(5, TimeSpan.FromSeconds(30));
            Assert.IsEmpty(testEmptyQueue);

            // put 3 message into the queue
            await _azureQueue.PutAsync(new Storable<string> { Entity = "Test1" });
            await _azureQueue.PutAsync(new Storable<string> { Entity = "Test2" });
            await _azureQueue.PutAsync(new Storable<string> { Entity = "Test3" });

            IEnumerable<Storable<string>> messages = (await _azureQueue.GetMessagesAsync(5, TimeSpan.FromSeconds(30))).ToList();

            Assert.AreEqual(3, messages.Count());
            Assert.IsNotNull(messages.FirstOrDefault(s => s.Entity == "Test1"));
            Assert.IsNotNull(messages.FirstOrDefault(s => s.Entity == "Test2"));
            Assert.IsNotNull(messages.FirstOrDefault(s => s.Entity == "Test3"));
            Assert.IsTrue(messages.All(m => m.ContainerId == QueueName));
        }

        [Test]
        public async Task Test_GetLargeAmountOfMessages()
        {
            const int numberOfMessages = 40;

            IEnumerable<Storable<string>> testEmptyQueue = await _azureQueue.GetMessagesAsync(numberOfMessages, TimeSpan.FromSeconds(30));
            Assert.IsEmpty(testEmptyQueue);

            // put 3 message into the queue
            for (int i = 0; i < numberOfMessages; i++)
            {
                await _azureQueue.PutAsync(new Storable<string> { Entity = "Test" + i });
            }
            IEnumerable<Storable<string>> messages = (await _azureQueue.GetMessagesAsync(numberOfMessages, TimeSpan.FromSeconds(30))).ToList();

            Assert.AreEqual(numberOfMessages, messages.Count());
            for (int i = 0; i < numberOfMessages; i++)
            {
                Assert.IsNotNull(messages.FirstOrDefault(s => s.Entity == "Test" + i));
            }

            foreach (Storable<string> storable in messages)
            {
                await _azureQueue.DeleteAsync(storable.Id, storable.Etag);
            }
        }

        [Test]
        public async Task Test_DeleteMessage()
        {
            Random rnd = new Random();
            string msg = "test message " + rnd.Next();
            Storable<string> storableObj = await PutAndGetTestMessageToQueueAsync(msg, TimeSpan.FromSeconds(10));
            Assert.IsNotNull(storableObj);
            Assert.That(storableObj.Id, Is.Not.Null.And.Not.Empty);
            Assert.That(storableObj.Etag, Is.Not.Null.And.Not.Empty);

            Assert.IsTrue(await _azureQueue.DeleteAsync(storableObj.Id, storableObj.Etag));
            // if the message has already been deleted DeleteAsync returns true
            Assert.IsTrue(await _azureQueue.DeleteAsync(storableObj.Id, storableObj.Etag));
        }

        [Test]
        public async Task Test_UpdateMessage()
        {
            Random rnd = new Random();
            string msg = "test message " + rnd.Next();
            Storable<string> storableObj = await PutAndGetTestMessageToQueueAsync(msg, TimeSpan.FromSeconds(10));
            Assert.IsNotNull(storableObj);
            Assert.That(storableObj.Id, Is.Not.Null.And.Not.Empty);
            Assert.That(storableObj.Etag, Is.Not.Null.And.Not.Empty);

            var updatedObj = await _azureQueue.UpdateMessageAsync(storableObj, leaseTime: TimeSpan.FromSeconds(20), flags: MessageUpdateFlags.UpdateVisibility);
            Assert.IsTrue(await _azureQueue.DeleteAsync(updatedObj.Id, updatedObj.Etag));

            StorageException lastException = null;
            try
            {
                await _azureQueue.UpdateMessageAsync(storableObj, leaseTime: TimeSpan.FromSeconds(20), flags: MessageUpdateFlags.UpdateVisibility);
            }
            catch (StorageException ex)
            {
                lastException = ex;
            }

            Assert.AreEqual(404, lastException.RequestInformation.HttpStatusCode);
        }

        [Test]
        public async Task TestAzureQueue_GetQueueLength()
        {
            // Test an empty queue.
            Assert.AreEqual(0, await _azureQueue.GetQueueLengthAsync());

            // Test one message in the queue.
            await PutTestMessageToQueueAsync("test message");
            Assert.AreEqual(1, await _azureQueue.GetQueueLengthAsync());

            // Test getting the message from the queue but not deleting it.
            var message = await _azureQueue.GetAsync();
            Assert.IsNotNull(message);
            Assert.AreEqual(1, await _azureQueue.GetQueueLengthAsync());

            // Test deleting the message from the queue.
            await _azureQueue.DeleteAsync(message);
            Assert.AreEqual(0, await _azureQueue.GetQueueLengthAsync());

            // Test ten messages added to the queue.
            for (var i = 0; i < 10; i++)
            {
                await PutTestMessageToQueueAsync($"test message {i}");
            }
            Assert.AreEqual(10, await _azureQueue.GetQueueLengthAsync());
        }

        private async Task PutTestMessageToQueueAsync(string message)
        {
            var storableObj = new Storable<string> { Entity = message };
            await _azureQueue.PutAsync(storableObj);
        }

        private async Task<Storable<string>> PutAndGetTestMessageToQueueAsync(string message, TimeSpan leaseTime)
        {
            await PutTestMessageToQueueAsync(message);
            return await _azureQueue.GetAsync(leaseTime);
        }
    }
}