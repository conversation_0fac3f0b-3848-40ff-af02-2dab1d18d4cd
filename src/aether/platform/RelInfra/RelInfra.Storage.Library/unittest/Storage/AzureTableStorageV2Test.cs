// <copyright file="AzureTableStorageV2Test.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Aether.TokenProvider;
using Microsoft.RelInfra.Storage.Exceptions;
using Microsoft.WindowsAzure.Storage.Table;
using NUnit.Framework;
using static Microsoft.RelInfra.Storage.Test.AzureTableStorageTest;

namespace Microsoft.RelInfra.Storage.Test
{
    [TestFixture]
    [Category("QTestSkip")]
    public class AzureTableStorageV2Test
    {
        private AzureTableStorageV2 _storageV2;
        private AzureTableStorageV2 _storageV2GetAllTest;

        [OneTimeSetUp]
        public void SetUp()
        {
            var connectionString = AzureCredHelper.GetDevAzureConnectionString();
            _storageV2 = new AzureTableStorageV2(
                connectionString: connectionString,
                tableName: TestSettings.AzureTableName,
                counterManager: null);

            _storageV2GetAllTest = new AzureTableStorageV2(
                connectionString: connectionString,
                tableName: TestSettings.AzureGetAllTestTableName,
                counterManager: null);
        }

#region Same tests with AzureTableStorageTest.cs, but with V2.
        [Test, Explicit]
        public async Task GetWithTakeCountSetting()
        {
            var residues = (await _storageV2.GetPartitionAsync<TestEntity>(TestSettings.PartitionName)).ToList();

            if (residues.Count() != 2010)
            {
                await Task.WhenAll(residues.Select<Storable<TestEntity>, Task<bool>>(async storable => await _storageV2.TryDeleteAsync(TestSettings.PartitionName, storable.Id)));

                List<Storable<TestEntity>> list = new List<Storable<TestEntity>>();

                for (int j = 0; j < 20; j++)
                {
                    for (int i = 0; i < 100; i++)
                    {
                        list.Add(new Storable<TestEntity>(new TestEntity() { Prop1 = "Entity" + (i + j) })
                        {
                            Id = Guid.NewGuid().ToString()
                        });
                    }
                    Assert.IsTrue(await _storageV2.TryBatchPutAsync(TestSettings.PartitionName, list));
                }
                for (int k = 2000; k < 2010; k++)
                {
                    list.Add(new Storable<TestEntity>(new TestEntity() { Prop1 = "Entity" + k })
                    {
                        Id = Guid.NewGuid().ToString()
                    });
                }
                Assert.IsTrue(await _storageV2.TryBatchPutAsync(TestSettings.PartitionName, list));
            }

            var e2001 = await _storageV2.GetPartitionAsync<string>(TestSettings.PartitionName, 2001);
            Assert.AreEqual(2001, e2001.Count());
            var e1250 = await _storageV2.GetPartitionAsync<string>(TestSettings.PartitionName, 1250);
            Assert.AreEqual(1250, e1250.Count());
            var e12 = await _storageV2.GetPartitionAsync<string>(TestSettings.PartitionName, 12);
            Assert.AreEqual(12, e12.Count());
            var eAll = await _storageV2.GetPartitionAsync<string>(TestSettings.PartitionName);
            Assert.AreEqual(2010, eAll.Count());
        }

        [Test]
        public async Task Test_BatchAddResource()
        {
            string[] objs = new[] { "test_batch1", "test_batch2" };

            IEnumerable<Storable<string>> resources = objs.Select(o => new Storable<string>()
            {
                Id = Guid.NewGuid().ToString(),
                Entity = o
            }).ToList();

            Assert.IsTrue(await _storageV2.TryBatchPutAsync(TestSettings.PartitionName, resources));

            // try to add them again (should return true, we don't want to throw exceptions if some entities exist)
            Assert.IsTrue(await _storageV2.TryBatchPutAsync(TestSettings.PartitionName, resources));

            foreach (var res in resources)
            {
                await _storageV2.TryDeleteAsync(TestSettings.PartitionName, res.Id);
            }
        }

        [Test]
        public async Task Test_AddComplexEntity()
        {
            var entity = new TestEntity { Prop1 = null, Prop2 = 10 };
            var resource = new Storable<TestEntity>(entity);
            resource.Id = Guid.NewGuid().ToString();

            Assert.IsTrue(await _storageV2.TryPutAndIgnoreEtagAsync(TestSettings.PartitionName, resource.Id, resource));

            Storable<TestEntity> storedResource = await _storageV2.TryGetAsync<TestEntity>(TestSettings.PartitionName, resource.Id);

            Assert.AreEqual(resource.Entity.Prop1, storedResource.Entity.Prop1);
            Assert.AreEqual(resource.Entity.Prop2, storedResource.Entity.Prop2);
            Assert.AreEqual(TestSettings.PartitionName, storedResource.ContainerId);

            await _storageV2.TryDeleteAsync(TestSettings.PartitionName, resource.Id);
        }

        [Test]
        public async Task TestMergeBehaviourForPut()
        {
            string id = Guid.NewGuid().ToString();
            await _storageV2.PutAndIgnoreEtagAsync(TestSettings.PartitionName, id, new Storable<TestObject>
            {
                Id = id,
                Entity = new TestObject { Id = id, Name = "TestName", CreatedDate = DateTime.Now }
            });

            await _storageV2.PutAndIgnoreEtagAsync(TestSettings.PartitionName, id, new Storable<TestObject2>
            {
                Id = id,
                Entity = new TestObject2 { Id = id, Name2 = "TestName2" }
            });

            Storable<TestObject> obj = await _storageV2.GetAsync<TestObject>(TestSettings.PartitionName, id);
            Storable<TestObject2> obj2 = await _storageV2.GetAsync<TestObject2>(TestSettings.PartitionName, id);

            Assert.AreEqual(id, obj.Entity.Id);
            Assert.AreEqual("TestName", obj.Entity.Name);
            Assert.AreEqual(TestSettings.PartitionName, obj.ContainerId);

            Assert.AreEqual(id, obj2.Entity.Id);
            Assert.AreEqual("TestName2", obj2.Entity.Name2);
            Assert.AreEqual(TestSettings.PartitionName, obj2.ContainerId);

            await _storageV2.TryDeleteAsync(TestSettings.PartitionName, id);
        }

        [Test]
        public async Task TestMergeBehaviourForUpdate()
        {
            string id = Guid.NewGuid().ToString();
            Trace.WriteLine(id);

            await _storageV2.PutAndIgnoreEtagAsync(TestSettings.PartitionName, id, new Storable<TestObject>
            {
                Id = id,
                Entity = new TestObject { Id = id, Name = "TestName", CreatedDate = DateTime.Now }
            });

            await _storageV2.UpdateAsync(TestSettings.PartitionName, id, new Storable<TestObject2>
            {
                Id = id,
                Entity = new TestObject2 { Id = id, Name2 = "TestName2" },
                Etag = "*"
            });

            Storable<TestObject> obj = await _storageV2.GetAsync<TestObject>(TestSettings.PartitionName, id);
            Storable<TestObject2> obj2 = await _storageV2.GetAsync<TestObject2>(TestSettings.PartitionName, id);

            Assert.AreEqual(id, obj.Entity.Id);
            Assert.AreEqual("TestName", obj.Entity.Name);
            Assert.AreEqual(TestSettings.PartitionName, obj.ContainerId);

            Assert.AreEqual(id, obj2.Entity.Id);
            Assert.AreEqual("TestName2", obj2.Entity.Name2);
            Assert.AreEqual(TestSettings.PartitionName, obj2.ContainerId);

            await _storageV2.TryDeleteAsync(TestSettings.PartitionName, id);
        }

        [Test]
        public async Task TestUpdateBehaviour()
        {
            string id = Guid.NewGuid().ToString();
            Trace.WriteLine(id);

            Storable<TestObject> obj = await _storageV2.PutAndIgnoreEtagAsync(TestSettings.PartitionName, id, new Storable<TestObject>
            {
                Id = id,
                Entity = new TestObject { Id = id, Name = "TestName", CreatedDate = DateTime.Now }
            });

            Storable<TestObject> obj2 = await _storageV2.UpdateAsync(TestSettings.PartitionName, id, new Storable<TestObject>
            {
                Id = id,
                Entity = new TestObject { Id = id, Name = null, CreatedDate = DateTime.Now.AddMinutes(1) },
                Etag = "*"
            });

            Assert.AreEqual(id, obj.Id);
            Assert.AreEqual("TestName", obj.Entity.Name);
            Assert.AreEqual(TestSettings.PartitionName, obj.ContainerId);

            Assert.AreEqual(id, obj2.Id);
            Assert.AreEqual("TestName", obj2.Entity.Name);
            Assert.AreEqual(TestSettings.PartitionName, obj2.ContainerId);

            await _storageV2.TryDeleteAsync(TestSettings.PartitionName, id);
        }

        [Test]
        public void TestUpdateNonExistingRecordBehaviourAsync()
        {
            string id = Guid.NewGuid().ToString();
            Trace.WriteLine(id);

            Assert.ThrowsAsync<RelInfraStorageException>(async () => await _storageV2.UpdateAsync(TestSettings.PartitionName, id, new Storable<TestObject>
            {
                Id = id,
                Entity = new TestObject { Id = id, Name = null, CreatedDate = DateTime.Now.AddMinutes(1) },
                Etag = "*"
            }));
        }

        [Test]
        public async Task TestBatchGetAsync()
        {
            var entity = new TestEntity { Prop1 = null, Prop2 = 10 };

            var resource = new Storable<TestEntity>(entity) { Id = Guid.NewGuid().ToString() };
            Assert.IsTrue(await _storageV2.TryPutAndIgnoreEtagAsync(TestSettings.PartitionName, resource.Id, resource));

            var resource1 = new Storable<TestEntity>(entity) { Id = Guid.NewGuid().ToString() };
            Assert.IsTrue(await _storageV2.TryPutAndIgnoreEtagAsync(TestSettings.PartitionName, resource1.Id, resource1));

            var resource2 = new Storable<TestEntity>(entity) { Id = Guid.NewGuid().ToString() };
            Assert.IsTrue(await _storageV2.TryPutAndIgnoreEtagAsync(TestSettings.PartitionName, resource2.Id, resource2));

            IEnumerable<Storable<TestEntity>> storedResources =
                await _storageV2.BatchGetAsync<TestEntity>(TestSettings.PartitionName,
                    new[] { resource.Id, resource1.Id, resource2.Id });

            Assert.IsTrue(storedResources.All(r => r.ContainerId == TestSettings.PartitionName));

            await _storageV2.BatchDeleteAsync(TestSettings.PartitionName, new[] { resource.Id, resource1.Id, resource2.Id });
        }

        [Test]
        public async Task TestBatchDelete()
        {
            var entity = new TestEntity { Prop1 = null, Prop2 = 10 };

            var resource = new Storable<TestEntity>(entity) { Id = Guid.NewGuid().ToString() };
            Assert.IsTrue(await _storageV2.TryPutAndIgnoreEtagAsync(TestSettings.PartitionName, resource.Id, resource));

            var resource1 = new Storable<TestEntity>(entity) { Id = Guid.NewGuid().ToString() };
            Assert.IsTrue(await _storageV2.TryPutAndIgnoreEtagAsync(TestSettings.PartitionName, resource1.Id, resource1));

            var resource2 = new Storable<TestEntity>(entity) { Id = Guid.NewGuid().ToString() };
            Assert.IsTrue(await _storageV2.TryPutAndIgnoreEtagAsync(TestSettings.PartitionName, resource2.Id, resource2));

            await _storageV2.BatchDeleteAsync(TestSettings.PartitionName, new[] { resource.Id, resource1.Id, resource2.Id });
        }

        [Test]
        public async Task TestBatchUpdateEtagMismatch()
        {
            var entity = new TestEntity { Prop1 = null, Prop2 = 10 };

            var resource1 = new Storable<TestEntity>(entity) { Id = Guid.NewGuid().ToString() };
            Assert.IsTrue(await _storageV2.TryPutAndIgnoreEtagAsync(TestSettings.PartitionName, resource1.Id, resource1));

            var resource2 = new Storable<TestEntity>(entity) { Id = Guid.NewGuid().ToString() };
            Assert.IsTrue(await _storageV2.TryPutAndIgnoreEtagAsync(TestSettings.PartitionName, resource2.Id, resource2));

            var resource2Get = await _storageV2.GetAsync<TestEntity>(TestSettings.PartitionName, resource2.Id);

            var updatedResource = new Storable<TestEntity>()
            {
                Id = resource2.Id,
                Entity = new TestEntity { Prop1 = null, Prop2 = 11 },
                Etag = resource2Get.Etag,
            };
            await _storageV2.UpdateAsync(TestSettings.PartitionName, updatedResource.Id, updatedResource);

            resource1.Entity.Prop2 = 20;
            resource2.Entity.Prop2 = 20;
            resource2.Etag = resource2Get.Etag;

            bool isPreconditionFailed = false;
            try
            {
                await _storageV2.BatchUpdateAsync(TestSettings.PartitionName, new[] { resource1, resource2 });
            }
            catch (RelInfraStorageException ex)
            {
                isPreconditionFailed = ex.Error == StorageError.PreconditionFailed;
            }

            Assert.IsTrue(isPreconditionFailed);
        }

        [Test]
        public async Task TestBatchUpdate()
        {
            var entity = new TestEntity { Prop1 = null, Prop2 = 10 };

            var resource1 = new Storable<TestEntity>(entity) { Id = Guid.NewGuid().ToString() };
            Assert.IsTrue(await _storageV2.TryPutAndIgnoreEtagAsync(TestSettings.PartitionName, resource1.Id, resource1));

            var resource2 = new Storable<TestEntity>(entity) { Id = Guid.NewGuid().ToString() };
            Assert.IsTrue(await _storageV2.TryPutAndIgnoreEtagAsync(TestSettings.PartitionName, resource2.Id, resource2));

            var resource2Get = await _storageV2.GetAsync<TestEntity>(TestSettings.PartitionName, resource2.Id);

            var updatedResource = new Storable<TestEntity>()
            {
                Id = resource2.Id,
                Entity = new TestEntity { Prop1 = null, Prop2 = 11 },
                Etag = resource2Get.Etag,
            };

            var resource2Update = await _storageV2.UpdateAsync(TestSettings.PartitionName, updatedResource.Id, updatedResource);
            Assert.AreEqual(updatedResource.Id, resource2Update.Id);
            Assert.AreEqual(11, resource2Update.Entity.Prop2);

            resource1.Entity.Prop2 = 20;
            resource2.Entity.Prop2 = 20;
            resource2.Etag = resource2Update.Etag;

            var updatedResults = await _storageV2.BatchUpdateAsync(TestSettings.PartitionName, new[] { resource1, resource2 });
            Assert.AreEqual(2, updatedResults.Count());

            foreach(var res in updatedResults)
            {
                Assert.AreEqual(20, res.Entity.Prop2);
                Assert.AreNotEqual(resource2.Etag, res.Etag); // Updated so the etag should be different
            }
        }

        [Test]
        public async Task TestBatchUpdateInternalAsync()
        {
            var entity = new TestEntity { Prop1 = null, Prop2 = 10 };

            var resource1 = new Storable<TestEntity>(entity) { Id = Guid.NewGuid().ToString() };
            Assert.IsTrue(await _storageV2.TryPutAndIgnoreEtagAsync(TestSettings.PartitionName, resource1.Id, resource1));

            var resource2 = new Storable<TestEntity>(entity) { Id = Guid.NewGuid().ToString() };
            Assert.IsTrue(await _storageV2.TryPutAndIgnoreEtagAsync(TestSettings.PartitionName, resource2.Id, resource2));

            resource1.Entity.Prop2 = 20;
            resource2.Entity.Prop2 = 20;

            var updatedResults = await _storageV2.BatchUpdateInternalAsync(TestSettings.PartitionName, new[] { resource1, resource2 });
            Assert.AreEqual(2, updatedResults.Count());

            foreach (var res in updatedResults)
            {
                Assert.AreEqual(20, res.Entity.Prop2);
            }
        }

        [Test]
        public void TestBatchUpdateInternalAsync_InvalidOperationException()
        {
            var exceptionDetails = Assert.ThrowsAsync<RelInfraStorageException>(async () => await _storageV2.BatchUpdateInternalAsync(TestSettings.PartitionName, new List<Storable<TestEntity>>()));
            Assert.AreEqual(StorageError.BadRequest, exceptionDetails.Error);
            Assert.IsTrue(exceptionDetails.InnerException is InvalidOperationException);
        }

        [Test]
        public async Task TestBatchOversizeUpdate()
        {
            string prop = new string('a', 5 * 32 * 1024 - 5); // 5* 32k max for a column
            var entity = new TestEntityWithLargeObject { LargeString = prop };
            List<Storable<TestEntityWithLargeObject>> resources = new List<Storable<TestEntityWithLargeObject>>();
            Enumerable.Range(0, 100).ToList().ForEach(i => resources.Add(new Storable<TestEntityWithLargeObject>(entity) { Id = Guid.NewGuid().ToString() }));

            var results = await _storageV2.BatchPutAsync(TestSettings.PartitionName, resources);

            foreach (Storable<TestEntityWithLargeObject> resource in resources)
            {
                Assert.IsTrue(results.Any(result => result.Id == resource.Id));
            }

            var updateResults = await _storageV2.BatchUpdateAsync(TestSettings.PartitionName, results);
            foreach (Storable<TestEntityWithLargeObject> resource in resources)
            {
                Assert.IsTrue(updateResults.Any(result => result.Id == resource.Id));
            }
        }

        [Test]
        public async Task TestGetNullable()
        {
            var entity = new TestEntityNullable { Prop1 = "test", NullableProp1 = DateTime.UtcNow, NullableProp2 = null };

            var resource = new Storable<TestEntityNullable>(entity) { Id = Guid.NewGuid().ToString() };
            Assert.IsTrue(await _storageV2.TryPutAndIgnoreEtagAsync(TestSettings.PartitionName, resource.Id, resource));

            Storable<TestEntityNullable> storedResource =
                await _storageV2.GetAsync<TestEntityNullable>(TestSettings.PartitionName, resource.Id);

            Assert.AreEqual(resource.Id, storedResource.Id);
            Assert.AreEqual(entity.Prop1, storedResource.Entity.Prop1);
            Assert.AreEqual(entity.NullableProp1, storedResource.Entity.NullableProp1);
            Assert.AreEqual(entity.NullableProp2, storedResource.Entity.NullableProp2);

            await _storageV2.DeleteAsync(TestSettings.PartitionName, resource.Id);
        }

        [Test]
        public async Task TestExists()
        {
            var entity = new TestEntityNullable { Prop1 = "test", NullableProp1 = DateTime.UtcNow, NullableProp2 = null };

            var resource = new Storable<TestEntityNullable>(entity) { Id = Guid.NewGuid().ToString() };
            Assert.IsTrue(await _storageV2.TryPutAndIgnoreEtagAsync(TestSettings.PartitionName, resource.Id, resource));

            Assert.IsTrue(await _storageV2.ExistsAsync(TestSettings.PartitionName, resource.Id));
            Assert.IsFalse(await _storageV2.ExistsAsync(TestSettings.PartitionName, Guid.NewGuid().ToString()));

            await _storageV2.DeleteAsync(TestSettings.PartitionName, resource.Id);
        }

        [Test]
        public async Task TestLobAsync()
        {
            string id = Guid.NewGuid().ToString();
            string largeString = AzureTableEntityTest.RandomString(50000);

            await _storageV2.PutAndIgnoreEtagAsync(TestSettings.PartitionName, id, new Storable<TestEntityWithLargeObject>
            {
                Id = id,
                Entity = new TestEntityWithLargeObject { PropertyInt = 1, LargeString = largeString }
            });

            TestEntityWithLargeObject obj = (await _storageV2.GetAsync<TestEntityWithLargeObject>(TestSettings.PartitionName, id)).Entity;
            Assert.AreEqual(largeString, obj.LargeString);

            await _storageV2.PutAndIgnoreEtagAsync(TestSettings.PartitionName, id, new Storable<TestEntityWithLargeObject>
            {
                Id = id,
                Entity = new TestEntityWithLargeObject { PropertyInt = 1, LargeString = "Short string" }
            });
            obj = (await _storageV2.GetAsync<TestEntityWithLargeObject>(TestSettings.PartitionName, id)).Entity;
            Assert.AreEqual("Short string", obj.LargeString);

            await _storageV2.TryDeleteAsync(TestSettings.PartitionName, id);
        }

        [Test]
        public async Task TestInsertOrReplaceAsync()
        {
            // test multiple partitions to validate batching
            const string partitionUnderTest1 = "96e6caf1-ad0d-498b-a492-dcb4ca359d5a";
            const string partitionUnderTest2 = "5f233fb4-a70d-4eaa-b973-6b78e2447666";
            await DeleteOldTestData(partitionUnderTest1);
            await DeleteOldTestData(partitionUnderTest2);

            // create new entities
            TestEntity oldReplaceEntity = new TestEntity() { Prop1 = "Value1", Prop2 = 1 };
            string replaceEntityId = Guid.NewGuid().ToString();
            await _storageV2.AddAsync(partitionUnderTest1, replaceEntityId, new Storable<TestEntity>(oldReplaceEntity));

            // perform insert/replace
            TestEntity newReplaceEntity = new TestEntity() { Prop1 = "Value2", Prop2 = 2 };
            Storable<TestEntity> replaceStorable = new Storable<TestEntity>(newReplaceEntity) { ContainerId = partitionUnderTest1, Id = replaceEntityId };
            TestEntity insertEntity = new TestEntity() { Prop1 = "Insert", Prop2 = 3 };
            string insertEntityId = Guid.NewGuid().ToString();
            Storable<TestEntity> insertStorable = new Storable<TestEntity>(insertEntity) { ContainerId = partitionUnderTest1, Id = insertEntityId };
            TestEntity insertEntity2 = new TestEntity() { Prop1 = "Insert2", Prop2 = 4 };
            string insertEntityId2 = Guid.NewGuid().ToString();
            Storable<TestEntity> insertStorable2 = new Storable<TestEntity>(insertEntity2) { ContainerId = partitionUnderTest2, Id = insertEntityId2 };

            await _storageV2.InsertOrReplaceAsync(new[] { insertStorable, insertStorable2, replaceStorable });

            // validate success
            Storable<TestEntity> fetchedReplaceEntity = await _storageV2.GetAsync<TestEntity>(partitionUnderTest1, replaceEntityId);
            Assert.AreEqual(newReplaceEntity.Prop1, fetchedReplaceEntity.Entity.Prop1);
            Assert.AreEqual(newReplaceEntity.Prop2, fetchedReplaceEntity.Entity.Prop2);
            Assert.AreEqual(partitionUnderTest1, fetchedReplaceEntity.ContainerId);

            Storable<TestEntity> fetchedInsertEntity = await _storageV2.GetAsync<TestEntity>(partitionUnderTest1, insertEntityId);
            Assert.AreEqual(insertEntity.Prop1, fetchedInsertEntity.Entity.Prop1);
            Assert.AreEqual(insertEntity.Prop2, fetchedInsertEntity.Entity.Prop2);
            Assert.AreEqual(partitionUnderTest1, fetchedInsertEntity.ContainerId);

            Storable<TestEntity> fetchedInsertEntity2 = await _storageV2.GetAsync<TestEntity>(partitionUnderTest2, insertEntityId2);
            Assert.AreEqual(insertEntity2.Prop1, fetchedInsertEntity2.Entity.Prop1);
            Assert.AreEqual(insertEntity2.Prop2, fetchedInsertEntity2.Entity.Prop2);
            Assert.AreEqual(partitionUnderTest2, fetchedInsertEntity2.ContainerId);
        }

        [Test]
        public async Task TestInsertOrReplaceAsyncLargeTable()
        {
            const string partitionUnderTest = "13c056c5-4bc7-4636-9a5a-fc4d23227422";
            await DeleteOldTestData(partitionUnderTest);

            // initialize test entities
            string rowUnderTest = Guid.NewGuid().ToString();
            IEnumerable<Storable<TestEntity>> entities =
                Enumerable.Range(0, 400)
                .Select(i => new TestEntity() { Prop1 = rowUnderTest + i, Prop2 = i })
                .Select(e => new Storable<TestEntity>(e) { ContainerId = partitionUnderTest, Id = e.Prop1 });
            await _storageV2.InsertOrReplaceAsync(entities);

            // validate count of newly inserted rows
            string insertedEntityFilter = TableQuery.CombineFilters(
                TableQuery.GenerateFilterCondition("PartitionKey", QueryComparisons.Equal, partitionUnderTest),
                TableOperators.And,
                TableQuery.GenerateFilterConditionForDate("Timestamp", QueryComparisons.GreaterThan, DateTimeOffset.UtcNow.Subtract(TimeSpan.FromMinutes(1))));

            Assert.AreEqual(400, (await _storageV2.ExecuteQueryAsync<TestEntity>(insertedEntityFilter)).Count(e => e.Id.StartsWith(rowUnderTest)));
        }

        [Test]
        public void TestBatchStorables()
        {
            List<Storable<TestEntity>> storables =
                Enumerable.Range(0, 10).Select(CreateTestEntity).Select(e => new Storable<TestEntity>(e) { ContainerId = "1", Id = e.Prop2.ToString() })
                .Union(Enumerable.Range(0, 10).Select(CreateTestEntity).Select(e => new Storable<TestEntity>(e) { ContainerId = "2", Id = e.Prop2.ToString() }))
                .ToList();

            List<List<Storable<TestEntity>>> batchedStorables = AzureTableStorageV2.BatchStorables(storables, 5).Select(b => b.ToList()).ToList();
            Assert.AreEqual(4, batchedStorables.Count); // number of batches
            Assert.IsTrue(batchedStorables.All(b => b.Count == 5)); // batch size
            Assert.IsTrue(batchedStorables.All(b => b.All(s => s.ContainerId == b.First().ContainerId))); // batch coherence
        }

        #endregion

        [Test]
        public async Task Test_AddUpdateAndGet()
        {
            var resourceId = Guid.NewGuid().ToString();
            var resource = new Storable<string> { Id = resourceId, Entity = "test_string" };
            resource = await _storageV2.AddAsync(TestSettings.PartitionName, resourceId, resource);
            resource.Entity = "test_string_2";
            await _storageV2.UpdateAsync(TestSettings.PartitionName, resourceId, resource);

            var resourceGet = await _storageV2.GetAsync<string>(TestSettings.PartitionName, resourceId);
            Assert.AreEqual(resourceId, resourceGet.Id);
            Assert.AreEqual("test_string_2", resourceGet.Entity);
            Assert.That(resourceGet.Etag, Is.Not.Null.And.Not.Empty);
            
            await _storageV2.DeleteAsync(TestSettings.PartitionName, resourceId);
        }

        [Test]
        public async Task Test_AddUpdateAndGetLargeObject()
        {
            var resourceId = Guid.NewGuid().ToString();
            var largeObject = new TestEntityWithLargeObject { LargeString = "LargeString" };
            var resource = new Storable<TestEntityWithLargeObject> { Id = resourceId, Entity = largeObject };
            resource = await _storageV2.AddAsync(TestSettings.PartitionName, resourceId, resource);
            resource.Entity.LargeString = "LargeString_2";
            await _storageV2.UpdateAsync(TestSettings.PartitionName, resourceId, resource);

            var resourceGet = await _storageV2.GetAsync<TestEntityWithLargeObject>(TestSettings.PartitionName, resourceId);
            Assert.AreEqual(resourceId, resourceGet.Id);
            Assert.AreEqual("LargeString_2", resourceGet.Entity.LargeString);
            Assert.That(resourceGet.Etag, Is.Not.Null.And.Not.Empty);

            await _storageV2.DeleteAsync(TestSettings.PartitionName, resourceId);
        }

        [Test]
        public async Task Test_AddAndDelete()
        {
            var resourceId = Guid.NewGuid().ToString();
            var resource = new Storable<string> { Id = resourceId, Entity = "test_string" };
            await _storageV2.AddAsync(TestSettings.PartitionName, resourceId, resource);

            var resourceGet = await _storageV2.GetAsync<string>(TestSettings.PartitionName, resourceId);
            Assert.AreEqual(resourceId, resourceGet.Id);
            Assert.AreEqual("test_string", resourceGet.Entity);
            Assert.That(resourceGet.Etag, Is.Not.Null.And.Not.Empty);

            await _storageV2.DeleteAsync(TestSettings.PartitionName, resourceId);
            try
            {
                await _storageV2.GetAsync<string>(TestSettings.PartitionName, resourceId);
            }
            catch (RelInfraStorageException ex) when (ex.Error == StorageError.NotFound)
            {
                // Test is passed.
                return;
            }

            throw new Exception("Not found exception should be thrown.");
        }

        [Test]
        [Category("StorageV2GetAllTest")]
        public async Task Test_GetAllWithContinuationTokenAsync()
        {
            try
            {
                // the Takecount will be used to limit the number of results returned in each call to the storage.
                // Note!!! the takeCount will not take effect if the takeCount is greater than the limit of max entity number in one page
                var expectedTakecount = 5;

                var entityNumInEachPartition = 10;

                // Just for testing TableExistsAsync and InitializeAsync
                while (!(await _storageV2GetAllTest.TableExistsAsync()))
                {
                    await _storageV2GetAllTest.InitializeAsync("useless string");
                    await Task.Delay(1000);
                }

                // Put data first to get them later
                var partitionKey1 = "partitionKey1_Test_GetAllWithContinuationTokenAsync_WithTakeCount";
                var partitionKey2 = "partitionKey2_Test_GetAllWithContinuationTokenAsync_WithTakeCount";
                List<Storable<TestEntity>> resources1 = Enumerable.Range(0, entityNumInEachPartition).Select(CreateTestEntity).Select(e => new Storable<TestEntity>(e) { Id = e.Prop2.ToString() }).ToList();
                List<Storable<TestEntity>> resources2 = Enumerable.Range(0, entityNumInEachPartition).Select(CreateTestEntity).Select(e => new Storable<TestEntity>(e) { Id = e.Prop2.ToString() }).ToList();
                var putResults1 = (await _storageV2GetAllTest.BatchPutAsync(partitionKey1, resources1)).ToList();
                var putResults2 = (await _storageV2GetAllTest.BatchPutAsync(partitionKey2, resources2)).ToList();

                var resultTakeCount = 0;
                var resultCount = 0;
                string continuationToken = null;
                do
                {
                    QueryResults<TestEntity> results = await _storageV2GetAllTest.GetAllWithContinuationTokenAsync<TestEntity>(continuationToken, expectedTakecount);
                    resultCount += results.Results.Count();
                    if (resultTakeCount == 0)
                    {
                        // Record the first page result count
                        resultTakeCount = resultCount;
                    }
                    continuationToken = results.ContinuationToken;
                }
                while (continuationToken != null);

                // Note!!! the takeCount will not take effect if the takeCount is greater than the limit of max entity number in one page
                Assert.AreEqual(expectedTakecount, resultTakeCount);
                Assert.AreEqual(entityNumInEachPartition * 2, resultCount);

                var resources1Got = putResults1.ToList().OrderBy(e => e.Id).ToList();
                var resources2Got = putResults2.ToList().OrderBy(e => e.Id).ToList();
                for (var i = 0; i < entityNumInEachPartition; i++)
                {
                    Assert.AreEqual(i, resources1Got[i].Entity.Prop2);
                    Assert.AreEqual(i.ToString(), resources1Got[i].Id);

                    Assert.AreEqual(i, resources2Got[i].Entity.Prop2);
                    Assert.AreEqual(i.ToString(), resources2Got[i].Id);
                }
            }
            finally
            {
                // Delete table
                await _storageV2GetAllTest.DeleteIfExistsAsync();
            }
        }

        [Test]
        [Category("StorageV2GetAllTest")]
        public async Task Test_ExecuteQueryInternalAsync()
        {
            try
            {
                var entityNumInEachPartition = 10;
                // Create table
                await _storageV2GetAllTest.CreateIfNotExistsAsync();

                // Put data first to get them later
                var partitionKey1 = "partitionKey1_Test_ExecuteQueryInternalAsync";
                var partitionKey2 = "partitionKey2_Test_ExecuteQueryInternalAsync";
                var rowKeyPrefix = "rowKey_Test_ExecuteQueryInternalAsync";
                List<Storable<TestEntity>> resourcess = Enumerable.Range(0, entityNumInEachPartition).Select(CreateTestEntity).Select(e => new Storable<TestEntity>(e) { Id = e.Prop2.ToString() }).ToList();

                foreach (var resource in resourcess)
                {
                    await _storageV2GetAllTest.AddAsync(partitionKey1, rowKeyPrefix + $"{resource.Id}", resource);
                    await _storageV2GetAllTest.AddAsync(partitionKey2, rowKeyPrefix + $"{resource.Id}", resource);
                }

                var select = new List<string> { "PartitionKey", "RowKey" };

                // Test query partitionKey and rowKey, should return only one entity
                var resultLst = new List<QueryResults<TestEntity>>();
                string continuationToken = null;
                do
                {
                    var currentResult = await _storageV2GetAllTest.ExecuteQueryInternalAsync<TestEntity>(
                        partitionKey: partitionKey1,
                        query: $"RowKey eq '{rowKeyPrefix}0'",
                        selectColumns: select,
                        continuationToken: continuationToken);

                    continuationToken = currentResult.ContinuationToken;
                    resultLst.Add(currentResult);
                } while (continuationToken != null);

                Assert.AreEqual(1, resultLst.Count);
                Assert.AreEqual(1, resultLst.FirstOrDefault().Results.Count());

                // Test query RowKey, should return 2 entities
                resultLst = new List<QueryResults<TestEntity>>();
                continuationToken = null;
                do
                {
                    var currentResult = await _storageV2GetAllTest.ExecuteQueryInternalAsync<TestEntity>(
                        partitionKey: null,
                        query: $"RowKey eq '{rowKeyPrefix}0'",
                        selectColumns: select,
                        continuationToken: continuationToken);

                    continuationToken = currentResult.ContinuationToken;
                    resultLst.Add(currentResult);
                } while (continuationToken != null);

                Assert.AreEqual(1, resultLst.Count);
                Assert.AreEqual(2, resultLst.FirstOrDefault().Results.Count());

                // Test query all entity in one partition, and set takeCount
                resultLst = new List<QueryResults<TestEntity>>();
                continuationToken = null;
                do
                {
                    var currentResult = await _storageV2GetAllTest.ExecuteQueryInternalAsync<TestEntity>(
                        partitionKey: null,
                        query: $"PartitionKey eq '{partitionKey1}'",
                        selectColumns: select,
                        continuationToken: continuationToken,
                        takeCount: 2);

                    continuationToken = currentResult.ContinuationToken;
                    resultLst.Add(currentResult);
                } while (continuationToken != null);

                Assert.AreEqual(5, resultLst.Count);
                foreach (var res in resultLst)
                {
                    Assert.AreEqual(2, res.Results.Count());
                }

                // Test GetPartitionWithCountinuationTokenAsync
                var result = await _storageV2GetAllTest.GetPartitionWithCountinuationTokenAsync<TestEntity>(partitionKey1, null, 2);
                Assert.AreEqual(2, result.Results.Count());
                result = await _storageV2GetAllTest.GetPartitionWithCountinuationTokenAsync<TestEntity>(partitionKey1, select, null, 2);
                Assert.AreEqual(2, result.Results.Count());
            }
            finally
            {
                // Delete table
                await _storageV2GetAllTest.DeleteIfExistsAsync();
            }
        }

        [Test]
        public async Task Test_GetRowCountAsync()
        {
            var partitionKey = "PartitionKey_Test_GetRowCountAsync";
            var resourceId = Guid.NewGuid().ToString();
            var resource = new Storable<string> { Id = resourceId, Entity = "test_string" };
            await _storageV2.AddAsync(partitionKey, resourceId, resource);

            var rowCount = await _storageV2.GetRowCountAsync<string>(partitionKey);
            Assert.AreEqual(1, rowCount);

            var resourceGet = await _storageV2.GetAsync<string>(partitionKey, resourceId);
            Assert.AreEqual(resourceId, resourceGet.Id);
            Assert.AreEqual("test_string", resourceGet.Entity);
            Assert.That(resourceGet.Etag, Is.Not.Null.And.Not.Empty);

            await _storageV2.DeleteAsync(partitionKey, resourceId);
        }

        [Test]
        public async Task TestInvalidInputError()
        {
            var entity = new TestEntity { Prop1 = null, Prop2 = 10 };

            var resource1 = new Storable<TestEntity>(entity) { Id = Guid.NewGuid().ToString() };
            Assert.IsTrue(await _storageV2.TryPutAndIgnoreEtagAsync(TestSettings.PartitionName, resource1.Id, resource1));

            // Test case 1: Duplicate row key in one batch update request.
            resource1.Entity.Prop2 = 20;
            bool isPreconditionFailed = false;
            try
            {   
                await _storageV2.BatchUpdateAsync(TestSettings.PartitionName, new[] { resource1, resource1 });
            }
            catch (RelInfraStorageException ex)
            {
                isPreconditionFailed = ex.Error == StorageError.BadRequest;
            }

            Assert.IsTrue(isPreconditionFailed);


            // Test case 2: Invalid Etag.
            isPreconditionFailed = false;
            resource1.Etag = "Etag";
            try
            {
                await _storageV2.BatchUpdateAsync(TestSettings.PartitionName, new[] { resource1 });
            }
            catch (RelInfraStorageException ex)
            {
                isPreconditionFailed = ex.Error == StorageError.BadRequest;
            }

            Assert.IsTrue(isPreconditionFailed);

        }

        private static TestEntity CreateTestEntity(int identifier)
        {
            return new TestEntity()
            {
                Prop1 = "PropValue" + identifier,
                Prop2 = identifier
            };
        }

        private async Task DeleteOldTestData(string partitionId)
        {
            string oldEntityFilter = TableQuery.CombineFilters(
                TableQuery.GenerateFilterConditionForDate("Timestamp", QueryComparisons.LessThan, DateTimeOffset.UtcNow.Subtract(TimeSpan.FromDays(1))),
                TableOperators.And,
                TableQuery.GenerateFilterCondition("PartitionKey", QueryComparisons.Equal, partitionId));

            foreach (Storable<TestEntity> entity in (await _storageV2.ExecuteQueryAsync<TestEntity>(oldEntityFilter)))
            {
                await _storageV2.TryDeleteAsync(entity.ContainerId, entity.Id);
            }
        }
    }
}
