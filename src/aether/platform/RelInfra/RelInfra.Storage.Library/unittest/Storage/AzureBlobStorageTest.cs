﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Aether.TokenProvider;
using Microsoft.RelInfra.Storage.Exceptions;
using Microsoft.WindowsAzure.Storage;
using Microsoft.WindowsAzure.Storage.Blob;
using NUnit.Framework;

namespace Microsoft.RelInfra.Storage.Test
{
    [TestFixture]
    [Category("QTestSkip")]
    public class AzureBlobStorageTest : BaseStorageTests
    {
        private AzureBlobStorage _blobStorage;
        private AzureBlobLeasableContainerStorage _leasableStorage;
        private string _connectionString = "";

        public AzureBlobStorageTest()
            : base(TestSettings.BlobName)
        {
        }

        [SetUp]
        public void TestSetup()
        {
            _connectionString = AzureCredHelper.GetDevAzureConnectionString();
            _blobStorage = new AzureBlobStorage(_connectionString, 5, new TimeSpan(0, 0, 0, 10), null);
            _leasableStorage = new AzureBlobLeasableContainerStorage(_blobStorage, TestSettings.BlobName);

            _storage = _blobStorage;
            _blobStorage.InitializeAsync(TestSettings.BlobName).Wait();
        }

        [Test]
        public async Task TestBlobLeaseConcurrentAccessAsync()
        {
            string blobName = Guid.NewGuid().ToString();

            await _leasableStorage.InitializeBlobAsync(blobName);
            string leaseId = null;
            try
            {
                leaseId = await _leasableStorage.AcquireLeaseAsync(blobName, TimeSpan.FromSeconds(15));
                Assert.IsNotNull(leaseId);

                Func<Task<string>> action = async () => await _leasableStorage.AcquireLeaseAsync(blobName, TimeSpan.FromSeconds(15));
                Assert.That(action,
                    Throws.TypeOf<RelInfraStorageException>().With.Message.Contains("Failed to acquire a lease"));
            }
            finally
            {
                await _leasableStorage.ReleaseLeaseAsync(blobName, leaseId);
                await _leasableStorage.DeleteAsync(blobName);
            }
        }

        [Test]
        public async Task TestBlobLeaseReleaseAsync()
        {
            string blobName = Guid.NewGuid().ToString();

            await _leasableStorage.InitializeBlobAsync(blobName);

            string leaseId = await _leasableStorage.AcquireLeaseAsync(blobName, TimeSpan.FromSeconds(15));
            await _leasableStorage.ReleaseLeaseAsync(blobName, leaseId);

            leaseId = await _leasableStorage.AcquireLeaseAsync(blobName, TimeSpan.FromSeconds(15));
            await _leasableStorage.ReleaseLeaseAsync(blobName, leaseId);
            await _leasableStorage.DeleteAsync(blobName);
        }

        [Test]
        public async Task TestBlobLeaseRenewAsync()
        {
            string blobName = Guid.NewGuid().ToString();

            await _leasableStorage.InitializeBlobAsync(blobName);

            string leaseId = await _leasableStorage.AcquireLeaseAsync(blobName, TimeSpan.FromSeconds(15));
            await _leasableStorage.RenewLeaseAsync(blobName, leaseId);
            await _leasableStorage.ReleaseLeaseAsync(blobName, leaseId);
            await _leasableStorage.DeleteAsync(blobName);
        }

        [Test]
        public async Task TestBlobUriAsync()
        {
            Storable<string> storableObj = await CreateTestEntity();

            CloudStorageAccount storageAccount = CloudStorageAccount.Parse(_connectionString);
            CloudBlobClient blobClient = storageAccount.CreateCloudBlobClient();
            CloudBlobContainer container = blobClient.GetContainerReference(TestSettings.BlobName);

            CloudAppendBlob appendBlob = container.GetAppendBlobReference(storableObj.Id);
            CloudBlockBlob blockBlob = container.GetBlockBlobReference(storableObj.Id);
            CloudPageBlob pageBlob = container.GetPageBlobReference(storableObj.Id);
            CloudBlob blob = container.GetBlobReference(storableObj.Id);

            Uri blobUri = _blobStorage.GetBlobUri(TestSettings.BlobName, storableObj.Id);

            Assert.AreEqual(appendBlob.Uri, blobUri);
            Assert.AreEqual(blockBlob.Uri, blobUri);
            Assert.AreEqual(pageBlob.Uri, blobUri);
            Assert.AreEqual(blob.Uri, blobUri);

            Uri sharedAccessUri = _blobStorage.GetBlobUri(TestSettings.BlobName, storableObj.Id, DateTimeOffset.Now.AddSeconds(5.0));

            Assert.AreNotEqual(sharedAccessUri, blobUri);
#pragma warning disable SYSLIB0014
            WebClient client = new WebClient();
#pragma warning restore SYSLIB0014
            string testData = client.DownloadString(sharedAccessUri);
            Assert.AreEqual(testData, "\"test string\"");

            // let the link expire
            Thread.Sleep(7000);

            Assert.Throws<WebException>(() => client.DownloadString(sharedAccessUri));

            await _storage.DeleteAsync(TestSettings.BlobName, storableObj.Id);
        }

        [Test]
        public async Task TestWritableBlobUriAsync()
        {
            Storable<string> storableObj = await CreateTestEntity();

            CloudStorageAccount storageAccount = CloudStorageAccount.Parse(_connectionString);
            CloudBlobClient blobClient = storageAccount.CreateCloudBlobClient();
            CloudBlobContainer container = blobClient.GetContainerReference(TestSettings.BlobName);

            CloudAppendBlob appendBlob = container.GetAppendBlobReference(storableObj.Id);
            CloudBlockBlob blockBlob = container.GetBlockBlobReference(storableObj.Id);
            CloudPageBlob pageBlob = container.GetPageBlobReference(storableObj.Id);
            CloudBlob blob = container.GetBlobReference(storableObj.Id);

            var expiryTimeFirst = DateTimeOffset.Now.AddSeconds(5.0);
            var expiryTimeSecond = expiryTimeFirst.AddSeconds(5.0);

            Uri blobUri = _blobStorage.GetWritableBlobUri(TestSettings.BlobName, storableObj.Id, expiryTimeFirst);

            Assert.AreNotEqual(appendBlob.Uri, blobUri);
            Assert.AreNotEqual(blockBlob.Uri, blobUri);
            Assert.AreNotEqual(pageBlob.Uri, blobUri);
            Assert.AreNotEqual(blob.Uri, blobUri);

            Uri sharedAccessUri = _blobStorage.GetWritableBlobUri(TestSettings.BlobName, storableObj.Id, expiryTimeSecond);

            Assert.AreNotEqual(sharedAccessUri, blobUri);

            //Write to the blob
            string sampleText = "Text in writable blob";
            CloudBlockBlob sharedAccessUriBlob = new CloudBlockBlob(sharedAccessUri);
            using (var msWrite = new MemoryStream(Encoding.UTF8.GetBytes(sampleText)))
            {
                //Should not throw
                await sharedAccessUriBlob.UploadFromStreamAsync(msWrite);
            }

            //Read back from blob
            using (var msRead = new MemoryStream())
            {
                await sharedAccessUriBlob.DownloadToStreamAsync(msRead);
                msRead.Position = 0;
                using (StreamReader reader = new StreamReader(msRead, true))
                {
                    string sampleTextReadBack = reader.ReadToEnd();
                    Assert.AreEqual(sampleText, sampleTextReadBack);
                }
            }

            // let the link expire
            Thread.Sleep(11000);

#pragma warning disable SYSLIB0014
            WebClient client = new WebClient();
#pragma warning restore SYSLIB0014
            Assert.Throws<WebException>(() => client.DownloadString(sharedAccessUri));

            await _storage.DeleteAsync(TestSettings.BlobName, storableObj.Id);
        }

        [Test]
        public async Task TestAppendResourceAsync()
        {
            Storable<string> storableObj = await AppendTestEntity();
            Assert.IsNotNull(storableObj);
            Assert.That(storableObj.Etag, Is.Not.Null.And.Not.Empty);

            string testMessage = "test string2";

            await _blobStorage.AppendAsync(TestSettings.BlobName, storableObj.Id, testMessage);

            Storable<string> objFromBlob = await _blobStorage.GetBlobStringAsync(TestSettings.BlobName, storableObj.Id);

            Assert.AreNotEqual(storableObj.Entity, objFromBlob.Entity);
            Assert.AreNotEqual(testMessage, objFromBlob.Entity);
            Assert.AreEqual(storableObj.Entity + testMessage, objFromBlob.Entity);

            await _storage.TryDeleteAsync(TestSettings.BlobName, storableObj.Id);
        }

        [Test]
        public async Task TestAppendStreamAsync()
        {
            var resourceId = Guid.NewGuid().ToString();
            string testMessage0 = string.Empty;
            string testMessage1 = "test string1";
            string testMessage2 = "test string2";

            using (var testStream0 = new MemoryStream(Encoding.UTF8.GetBytes(testMessage0)))
            {
                await _blobStorage.AppendStreamAsync(TestSettings.BlobName, resourceId, testStream0);
            }

            using (var testStream1 = new MemoryStream(Encoding.UTF8.GetBytes(testMessage1)))
            {
                await _blobStorage.AppendStreamAsync(TestSettings.BlobName, resourceId, testStream1);
            }

            using (var testStream2 = new MemoryStream(Encoding.UTF8.GetBytes(testMessage2)))
            {
                await _blobStorage.AppendStreamAsync(TestSettings.BlobName, resourceId, testStream2);
            }

            Storable<string> objFromBlob = await _blobStorage.GetBlobStringAsync(TestSettings.BlobName, resourceId);

            Assert.AreNotEqual(testMessage1, objFromBlob.Entity);
            Assert.AreNotEqual(testMessage2, objFromBlob.Entity);
            Assert.AreEqual(testMessage1 + testMessage2, objFromBlob.Entity);

            // Fetch logs using another API
            string blobString;
            using (var logStream = await _blobStorage.GetStreamAsync(TestSettings.BlobName, resourceId))
            {
                using (var streamReader = new StreamReader(logStream))
                {
                    blobString = streamReader.ReadToEnd();
                }
            }
            Assert.AreEqual(testMessage1 + testMessage2, blobString);

            await _storage.TryDeleteAsync(TestSettings.BlobName, resourceId);
        }

        [Test]
        public async Task TestListDirectoryAsync()
        {
            string root = Guid.NewGuid().ToString();
            await _blobStorage.AppendAsync(TestSettings.BlobName, root + "aa/bb/1", "test");
            await _blobStorage.AppendAsync(TestSettings.BlobName, root + "aa/bb/2", "test");
            await _blobStorage.AppendAsync(TestSettings.BlobName, root + "aa/cc/2", "test");
            await _blobStorage.AppendAsync(TestSettings.BlobName, root + "aa/dd/3", "test");
            await _blobStorage.AppendAsync(TestSettings.BlobName, root + "aaa/gg/4", "test");

            string pathToEnumerate = root + "aa/";
            var blobs = (await _blobStorage.ListDirectoryAsync(
                                                    containerName: TestSettings.BlobName, 
                                                    path: pathToEnumerate, 
                                                    fileCountLimit: 100, 
                                                    cancellationToken: CancellationToken.None,
                                                    prefixFilter: null)).ToList();
            Console.WriteLine("Listed directory {0} in container {1}", pathToEnumerate, TestSettings.BlobName);
            blobs.ForEach(Console.WriteLine);
            Assert.AreEqual(4, blobs.Count);
            Assert.IsTrue(blobs.Any(blob => blob.EndsWith("aa/bb/1")));
            Assert.IsTrue(blobs.Any(blob => blob.EndsWith("aa/bb/2")));
            Assert.IsTrue(blobs.Any(blob => blob.EndsWith("aa/cc/2")));
            Assert.IsTrue(blobs.Any(blob => blob.EndsWith("aa/dd/3")));

            string prefixFilter = $"/{TestSettings.BlobName}/{pathToEnumerate}bb";
            blobs = (await _blobStorage.ListDirectoryAsync(
                                                    containerName: TestSettings.BlobName,
                                                    path: pathToEnumerate,
                                                    fileCountLimit: 100,
                                                    cancellationToken: CancellationToken.None,
                                                    prefixFilter: prefixFilter)).ToList();
            Console.WriteLine("Listed directory {0} in container {1} with filter {2}", pathToEnumerate, TestSettings.BlobName, prefixFilter);
            blobs.ForEach(Console.WriteLine);
            Assert.AreEqual(2, blobs.Count);
            Assert.IsTrue(blobs.Any(blob => blob.EndsWith("aa/bb/1")));
            Assert.IsTrue(blobs.Any(blob => blob.EndsWith("aa/bb/2")));
        }


        [Test]
        public async Task TestListDirectoryTooManyStreamsAsync()
        {
            string root = Guid.NewGuid().ToString();
            await _blobStorage.AppendAsync(TestSettings.BlobName, root + "bb/bb/1", "test");
            await _blobStorage.AppendAsync(TestSettings.BlobName, root + "bb/bb/2", "test");
            await _blobStorage.AppendAsync(TestSettings.BlobName, root + "bb/cc/2", "test");
            await _blobStorage.AppendAsync(TestSettings.BlobName, root + "bb/dd/3", "test");

            Func<Task<IEnumerable<string>>> action = async () => await _blobStorage.ListDirectoryAsync(containerName: TestSettings.BlobName,
                                                  path: root,
                                                  fileCountLimit: 2,
                                                  cancellationToken: CancellationToken.None,
                                                  prefixFilter: null);
            Assert.That(action, Throws.TypeOf<TooManyFilesException>());
        }

        [Test]
        public async Task TestCanAccess()
        {
            Assert.IsTrue(await _blobStorage.CanAccessAsync());
        }

        [Test]
        public void TestExistsAsync_Cancellation()
        {
            Assert.ThrowsAsync<TimeoutException>(async() =>
            {
                using (var cancellationTokenSource = new CancellationTokenSource(TimeSpan.FromMilliseconds(2)))
                {
                    await _blobStorage.ExistsAsync( "test", "test",cancellationTokenSource.Token);
                }
            });  
        }

        [Test]
        public async Task TestCanAccess_CancellationCaught()
        {
            bool canAccess;
            using (var cancellationTokenSource = new CancellationTokenSource(TimeSpan.FromMilliseconds(10)))
            {
                canAccess = await _blobStorage.CanAccessAsync(cancellationTokenSource.Token);
            }
            Assert.IsFalse(canAccess);
        }

        private async Task<Storable<string>> AppendTestEntity()
        {
            string message = "test string";
            string resourceId = Guid.NewGuid().ToString();

            var storableObj = await _blobStorage.AppendAsync(TestSettings.BlobName, resourceId, message);

            return storableObj;
        }

        private async Task<Storable<string>> CreateTestEntity()
        {
            string message = "test string";
            string resourceId = Guid.NewGuid().ToString();

            var storableObj = new Storable<string> { Entity = message };

            if (!await _storage.TryPutAndIgnoreEtagAsync(TestSettings.BlobName, resourceId, storableObj))
            {
                return null;
            }

            return storableObj;
        }

        [Test]
        public async Task PageBlobTest()
        {
            string blobName = Guid.NewGuid().ToString();
            Console.WriteLine(blobName);
            int rounds = 4 + 1;
            byte[] range = new byte[1024 * 1024];
            range[0] = 65;
            range[1] = 66;
            range[511] = 67;
            range[512] = 68;
            range[range.Length - 1] = 69;

            // size should round up to range.Length * rounds
            await _blobStorage.AddPageBlobAsync(TestSettings.BlobName, blobName, range.Length * rounds - 1);
            var taskList = Enumerable.Range(0, rounds).Select(i => _blobStorage.PutPageBlobPageAsync(TestSettings.BlobName, blobName, range, range.Length * i, 0, range.Length));
            await Task.WhenAll(taskList);
            
            var buffer = await _blobStorage.ReadBlobRangeAsync(TestSettings.BlobName, blobName, range.Length, 600);
            Enumerable.Range(0, buffer.Length).ToList().ForEach(i =>
            {
                Assert.AreEqual(range[i], buffer[i]);
            });
            
            buffer = await _blobStorage.ReadBlobRangeAsync(TestSettings.BlobName, blobName, 0, range.Length * rounds + 1);
            Assert.AreEqual(range.Length * rounds, buffer.Length);
            Enumerable.Range(0, 513).ToList().ForEach(i =>
            {
                Assert.AreEqual(range[i], buffer[range.Length * (rounds -1) + i]);
            });

            Storable<string> content = await _blobStorage.GetBlobStringAsync(TestSettings.BlobName, blobName);
            Assert.AreEqual(Encoding.UTF8.GetString(range), content.Entity.Substring(0, range.Length));
            Assert.AreEqual(Encoding.UTF8.GetString(range), content.Entity.Substring(range.Length, range.Length));
        }

        [Test]
        public async Task PutBlockTest()
        {
            string blobName = Guid.NewGuid().ToString();
            string content1 = "content1";
            string content2 = "content2";
            string blockName1 = "AAAAAA==";
            string blockName2 = "BAAAAA==";
            using (var ms = new MemoryStream(Encoding.UTF8.GetBytes(content1)))
            {
                await _blobStorage.PutBlockAsync(TestSettings.BlobName, blobName, blockName1, ms);
            }

            await _blobStorage.PutBlockListAsync(TestSettings.BlobName, blobName, new String[] { blockName1 }, "*");

            var storable = await _blobStorage.GetBlobStringAsync(TestSettings.BlobName, blobName);

            Assert.AreEqual(content1, storable.Entity);

            using (var ms = new MemoryStream(Encoding.UTF8.GetBytes(content2)))
            {
                await _blobStorage.PutBlockAsync(TestSettings.BlobName, blobName, blockName2, ms);
            }

            // committed blocks = 1
            IEnumerable<string> blockNames = await _blobStorage.GetBlockListAsync(TestSettings.BlobName, blobName, true);
            Assert.AreEqual(1, blockNames.Count());

            // uncommited + committed blocks = 2
            blockNames = await _blobStorage.GetBlockListAsync(TestSettings.BlobName, blobName, false);
            Assert.AreEqual(2, blockNames.Count());

            // test PutBlockList with etag="*"
            await _blobStorage.PutBlockListAsync(TestSettings.BlobName, blobName, new String[] { blockName1, blockName2 }, "*");

            storable = await _blobStorage.GetBlobStringAsync(TestSettings.BlobName, blobName);

            Assert.AreEqual(content1 + content2, storable.Entity);

            // test PutBlockList with actual etag, and change order
            await _blobStorage.PutBlockListAsync(TestSettings.BlobName, blobName, new String[] { blockName2 , blockName1 }, storable.Etag);

            storable = await _blobStorage.GetBlobStringAsync(TestSettings.BlobName, blobName);

            Assert.AreEqual(content2 + content1, storable.Entity);

            var buffer = await _blobStorage.ReadBlobRangeAsync(TestSettings.BlobName, blobName, 0, content2.Length);
            Assert.AreEqual(content2, Encoding.UTF8.GetString(buffer));
        }
    }
}
