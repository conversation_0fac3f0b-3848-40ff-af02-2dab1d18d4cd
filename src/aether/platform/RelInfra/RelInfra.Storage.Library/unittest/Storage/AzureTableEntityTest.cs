﻿using System;
using System.Collections.Generic;
using System.Text;
using Microsoft.MachineLearning.Common.Core;
using Microsoft.WindowsAzure.Storage.Table;
using NUnit.Framework;

namespace Microsoft.RelInfra.Storage.Test
{
    [TestFixture]
    public class AzureTableEntityTest
    {
        class ComplexObject
        {
            public int PropertyInt { get; set; }
            public string PropertyString { get; set; }
        }

        class TestEntity
        {
            public int PropertyInt { get; set; }
            public string PropertyString { get; set; }
            public DateTime PropertyDateTime { get; set; }
            public DateTimeOffset PropertyDateTimeOffset { get; set; }

            public int? NullablePropertyInt1 { get; set; }
            public int? NullablePropertyInt2 { get; set; }
            public DateTime? NullablePropertyDateTime { get; set; }
            public DateTimeOffset? NullablePropertyDateTimeOffset { get; set; }
             
            public ComplexObject PropertyObject { get; set; }
        }

        class TestEntityWithLargeObject
        {
            public int PropertyInt { get; set; }

            [LargeObject(5)]
            public ComplexObject PropertyObject { get; set; }

            [LargeObject(5)]
            public string LargeString { get; set; }
        }

        class TestEntityDerivedFromTableEntity : TableEntity
        {
            public int PropertyInt { get; set; }
        }

        class TestEnityWithRegularObject
        {
            public ComplexObject PropertyObject { get; set; }
        }

        class TestEnityWithLargeObjectEx
        {
            [LargeObjectEx(5)]
            public ComplexObject PropertyObject { get; set; }
        }

        class TestEntityWithCompressObject
        {
            [CompressObject]
            public ComplexObject PropertyObject { get; set; }

            [CompressObject]
            public string PropertyString { get; set; }
        }

        class TestEntityWithLargeObjectAndCompressObject
        {
            [LargeObject(2)]
            [CompressObject]
            public ComplexObject PropertyObject { get; set; }

            [LargeObject(2)]
            [CompressObject]
            public string PropertyString { get; set; }
        }

        [Test]
        public void TestDateTimeZoneConversion()
        {
            var utcOriginal = new DateTime(2000, 12, 13, 14, 15, 16, DateTimeKind.Utc);
            EntityProperty prop = new EntityProperty(utcOriginal);

            var localOriginal = utcOriginal.ToLocalTime();

            if (localOriginal.Ticks == utcOriginal.Ticks)
            {
                //If UTC ticks are same with local ticks, skip this test.
                return;
            }          

            var utc = new TestEntity
            {
                PropertyDateTime = utcOriginal,
                NullablePropertyDateTime = utcOriginal.AddMinutes(10),
            };
            var local = new TestEntity
            {
                PropertyDateTime = localOriginal,
                NullablePropertyDateTime = localOriginal.AddMinutes(10),
            };

            var utcEntity = new AzureTableEntity<TestEntity>("part", "utc", utc, "utc");
            var localEntity = new AzureTableEntity<TestEntity>("part", "local", local, "local");

            var actualUtc = utcEntity.ResolveEntity();
            var actualLocal = localEntity.ResolveEntity();
            
            Assert.AreEqual(DateTimeKind.Utc, actualUtc.PropertyDateTime.ToUniversalTime().Kind);
            Assert.AreEqual(DateTimeKind.Local, actualLocal.PropertyDateTime.Kind);
            Assert.AreEqual(utcOriginal, actualUtc.PropertyDateTime);
            Assert.AreEqual(utcOriginal, actualLocal.PropertyDateTime.ToUniversalTime());
            Assert.AreNotEqual(utcOriginal, actualLocal.PropertyDateTime);
        }

        [Test]
        public void TestDynamicTableEntityCreation()
        {
            var te = new TestEntity
            {
                PropertyInt = 1,
                PropertyString = "test string",
                PropertyDateTime = DateTime.UtcNow,
                PropertyDateTimeOffset = DateTimeOffset.UtcNow,

                NullablePropertyDateTime = DateTime.UtcNow.AddMinutes(10),
                NullablePropertyDateTimeOffset = DateTimeOffset.UtcNow.AddMinutes(10),
                NullablePropertyInt1 = 10,
                NullablePropertyInt2 = null,
                PropertyObject = new ComplexObject {PropertyInt = 100, PropertyString = "complextest"}
            };

            var tableEntity = new AzureTableEntity<TestEntity>("test", "test", te, "test string");

            Assert.AreEqual(EdmType.Int32, tableEntity.GetTableEntity().Properties["PropertyInt"].PropertyType);
            Assert.AreEqual((object) 1, tableEntity.GetTableEntity().Properties["PropertyInt"].Int32Value);
            Assert.AreEqual(EdmType.String, tableEntity.GetTableEntity().Properties["PropertyString"].PropertyType);
            Assert.AreEqual("test string", tableEntity.GetTableEntity().Properties["PropertyString"].StringValue);
            Assert.AreEqual(EdmType.DateTime, tableEntity.GetTableEntity().Properties["PropertyDateTime"].PropertyType);
            Assert.AreEqual(te.PropertyDateTime, tableEntity.GetTableEntity().Properties["PropertyDateTime"].DateTimeOffsetValue.Value.DateTime);
            Assert.AreEqual(EdmType.DateTime, tableEntity.GetTableEntity().Properties["PropertyDateTimeOffset"].PropertyType);
            Assert.AreEqual(te.PropertyDateTimeOffset, tableEntity.GetTableEntity().Properties["PropertyDateTimeOffset"].DateTimeOffsetValue.Value);

            Assert.AreEqual(EdmType.DateTime, tableEntity.GetTableEntity().Properties["NullablePropertyDateTime"].PropertyType);
            Assert.AreEqual(te.NullablePropertyDateTime, tableEntity.GetTableEntity().Properties["NullablePropertyDateTime"].DateTimeOffsetValue.Value.DateTime);
            Assert.AreEqual(EdmType.DateTime, tableEntity.GetTableEntity().Properties["NullablePropertyDateTimeOffset"].PropertyType);
            Assert.AreEqual(te.NullablePropertyDateTimeOffset, tableEntity.GetTableEntity().Properties["NullablePropertyDateTimeOffset"].DateTimeOffsetValue.Value);
            Assert.AreEqual(EdmType.Int32, tableEntity.GetTableEntity().Properties["NullablePropertyInt1"].PropertyType);
            Assert.AreEqual((object) 10, tableEntity.GetTableEntity().Properties["NullablePropertyInt1"].Int32Value);

            Assert.AreEqual(EdmType.String, tableEntity.GetTableEntity().Properties["PropertyObject"].PropertyType);
            
            Assert.IsFalse(tableEntity.GetTableEntity().Properties.ContainsKey("NullablePropertyInt2"));
        }

        [Test]
        public void TestResolvingType()
        {
            var expectedEntity = new TestEntity
            {
                PropertyInt = 1,
                PropertyString = "test string",
                PropertyDateTime = DateTime.UtcNow,
                PropertyDateTimeOffset = DateTimeOffset.UtcNow,

                NullablePropertyDateTime = DateTime.UtcNow.AddMinutes(10),
                NullablePropertyDateTimeOffset = DateTimeOffset.UtcNow.AddMinutes(10),
                NullablePropertyInt1 = 10,
                NullablePropertyInt2 = null,
                PropertyObject = new ComplexObject { PropertyInt = 100, PropertyString = "complextest" }
            };

            var tableEntity =
                new AzureTableEntity<TestEntity>((new AzureTableEntity<TestEntity>("test", "test", expectedEntity, "test string")).GetTableEntity());

            var actualEntity = tableEntity.ResolveEntity();

            Assert.AreEqual(expectedEntity.PropertyInt, (int) actualEntity.PropertyInt);
            Assert.AreEqual(expectedEntity.PropertyString, actualEntity.PropertyString);
            Assert.AreEqual(expectedEntity.PropertyDateTime, actualEntity.PropertyDateTime);
            Assert.AreEqual(expectedEntity.PropertyDateTimeOffset, actualEntity.PropertyDateTimeOffset);
            Assert.AreEqual(expectedEntity.NullablePropertyDateTime, actualEntity.NullablePropertyDateTime);
            Assert.AreEqual(expectedEntity.NullablePropertyDateTimeOffset, actualEntity.NullablePropertyDateTimeOffset);
            Assert.AreEqual(expectedEntity.NullablePropertyInt1, actualEntity.NullablePropertyInt1);
            Assert.AreEqual(expectedEntity.NullablePropertyInt2, actualEntity.NullablePropertyInt2);
            Assert.AreEqual(expectedEntity.PropertyObject.PropertyInt, (int) actualEntity.PropertyObject.PropertyInt);
            Assert.AreEqual(expectedEntity.PropertyObject.PropertyString, actualEntity.PropertyObject.PropertyString);
        }

        [Test]
        public void TestLOB()
        {
            var expectedEntity = new TestEntityWithLargeObject
            {
                PropertyInt = 1,
                PropertyObject = new ComplexObject { PropertyInt = 100, PropertyString = RandomString(40000)},
                LargeString = RandomString(50000),
            };
            var tableEntity = new AzureTableEntity<TestEntityWithLargeObject>("test", "test", expectedEntity, "Large String");

            Assert.AreEqual(EdmType.Int32, tableEntity.GetTableEntity().Properties["PropertyInt"].PropertyType);
            Assert.AreEqual((object) 1, tableEntity.GetTableEntity().Properties["PropertyInt"].Int32Value);

            Assert.AreEqual(11, tableEntity.GetTableEntity().Properties.Count);
            Assert.AreEqual(EdmType.String, tableEntity.GetTableEntity().Properties["PropertyObject1"].PropertyType);
            Assert.AreEqual(EdmType.String, tableEntity.GetTableEntity().Properties["PropertyObject2"].PropertyType);
            Assert.AreEqual(EdmType.String, tableEntity.GetTableEntity().Properties["PropertyObject3"].PropertyType);
            Assert.AreEqual(EdmType.String, tableEntity.GetTableEntity().Properties["PropertyObject4"].PropertyType);
            Assert.AreEqual(EdmType.String, tableEntity.GetTableEntity().Properties["PropertyObject5"].PropertyType);

            Assert.AreEqual(EdmType.String, tableEntity.GetTableEntity().Properties["LargeString1"].PropertyType);
            Assert.AreEqual(EdmType.String, tableEntity.GetTableEntity().Properties["LargeString2"].PropertyType);
            Assert.AreEqual(EdmType.String, tableEntity.GetTableEntity().Properties["LargeString3"].PropertyType);
            Assert.AreEqual(EdmType.String, tableEntity.GetTableEntity().Properties["LargeString4"].PropertyType);
            Assert.AreEqual(EdmType.String, tableEntity.GetTableEntity().Properties["LargeString5"].PropertyType);

            Assert.IsTrue(String.IsNullOrEmpty(tableEntity.GetTableEntity().Properties["PropertyObject3"].StringValue));
            Assert.IsTrue(String.IsNullOrEmpty(tableEntity.GetTableEntity().Properties["PropertyObject4"].StringValue));
            Assert.IsTrue(String.IsNullOrEmpty(tableEntity.GetTableEntity().Properties["PropertyObject5"].StringValue));

            var actualEntity = tableEntity.ResolveEntity();
            Assert.AreEqual(expectedEntity.PropertyInt, (int) actualEntity.PropertyInt);
            Assert.AreEqual(expectedEntity.PropertyObject.PropertyInt, (int) actualEntity.PropertyObject.PropertyInt);
            Assert.AreEqual(expectedEntity.PropertyObject.PropertyString, actualEntity.PropertyObject.PropertyString);
        }

        [Test]
        public void TestTableEntityDerivedClass()
        {
            var te = new TestEntityDerivedFromTableEntity
            {
                PartitionKey = "test",
                RowKey = "test",
                PropertyInt = 1,
            };

            var tableEntity = new AzureTableEntity<TestEntityDerivedFromTableEntity>("test", "test", te, "test");

            var minDateTime = new DateTimeOffset(1601, 1, 1, 0, 0, 0, TimeSpan.Zero);
            Assert.AreEqual(EdmType.Int32, tableEntity.GetTableEntity().Properties["PropertyInt"].PropertyType);
            Assert.AreEqual((object)1, tableEntity.GetTableEntity().Properties["PropertyInt"].Int32Value);
            Assert.AreEqual(EdmType.DateTime, tableEntity.GetTableEntity().Properties["Timestamp"].PropertyType);
            DateTimeOffset expectedValue = te.Timestamp > minDateTime ? te.Timestamp : minDateTime;
            Assert.AreEqual(expectedValue, tableEntity.GetTableEntity().Properties["Timestamp"].DateTimeOffsetValue.Value);
        }

        [Test]
        public void TestRegularToLargeExProperty()
        {
            var largeExEntity = new TestEnityWithLargeObjectEx
            {
                PropertyObject = new ComplexObject { PropertyInt = 100, PropertyString = RandomString(40) },
            };
            var largeExTableEntity = new AzureTableEntity<TestEnityWithLargeObjectEx>("test", "test", largeExEntity, "Large Entity");

            Assert.AreEqual(5, largeExTableEntity.GetTableEntity().Properties.Count);
            Assert.AreEqual(EdmType.String, largeExTableEntity.GetTableEntity().Properties["PropertyObject1"].PropertyType);
            Assert.AreEqual(EdmType.String, largeExTableEntity.GetTableEntity().Properties["PropertyObject2"].PropertyType);
            Assert.AreEqual(EdmType.String, largeExTableEntity.GetTableEntity().Properties["PropertyObject3"].PropertyType);
            Assert.AreEqual(EdmType.String, largeExTableEntity.GetTableEntity().Properties["PropertyObject4"].PropertyType);
            Assert.AreEqual(EdmType.String, largeExTableEntity.GetTableEntity().Properties["PropertyObject"].PropertyType);

            Assert.AreEqual(string.Empty, largeExTableEntity.GetTableEntity().Properties["PropertyObject1"].StringValue);
            Assert.AreEqual(string.Empty, largeExTableEntity.GetTableEntity().Properties["PropertyObject2"].StringValue);
            Assert.AreEqual(string.Empty, largeExTableEntity.GetTableEntity().Properties["PropertyObject3"].StringValue);
            Assert.AreEqual(string.Empty, largeExTableEntity.GetTableEntity().Properties["PropertyObject4"].StringValue);

            TestEnityWithLargeObjectEx actualEntity = largeExTableEntity.ResolveEntity();
            Assert.AreEqual(largeExEntity.PropertyObject.PropertyInt, actualEntity.PropertyObject.PropertyInt);
            Assert.AreEqual(largeExEntity.PropertyObject.PropertyString, actualEntity.PropertyObject.PropertyString);

            largeExTableEntity.GetTableEntity().Properties["PropertyObject1"].StringValue = null;
            largeExTableEntity.GetTableEntity().Properties["PropertyObject2"].StringValue = null;
            largeExTableEntity.GetTableEntity().Properties["PropertyObject3"].StringValue = null;
            largeExTableEntity.GetTableEntity().Properties["PropertyObject4"].StringValue = null;

            actualEntity = largeExTableEntity.ResolveEntity();
            Assert.AreEqual(largeExEntity.PropertyObject.PropertyInt, actualEntity.PropertyObject.PropertyInt);
            Assert.AreEqual(largeExEntity.PropertyObject.PropertyString, actualEntity.PropertyObject.PropertyString);

            largeExTableEntity.GetTableEntity().Properties.Remove("PropertyObject1");
            largeExTableEntity.GetTableEntity().Properties.Remove("PropertyObject2");
            largeExTableEntity.GetTableEntity().Properties.Remove("PropertyObject3");
            largeExTableEntity.GetTableEntity().Properties.Remove("PropertyObject4");

            actualEntity = largeExTableEntity.ResolveEntity();
            Assert.AreEqual(largeExEntity.PropertyObject.PropertyInt, actualEntity.PropertyObject.PropertyInt);
            Assert.AreEqual(largeExEntity.PropertyObject.PropertyString, actualEntity.PropertyObject.PropertyString);
        }

        [Test]
        public void TestColumnNames()
        {
            IList<string> regularTableEntityColumnNames = AzureTableEntity<TestEnityWithRegularObject>.GetColumnNames();
            IList<string> largeObjectTableEntityColumnNames = AzureTableEntity<TestEntityWithLargeObject>.GetColumnNames();
            IList<string> largeObjectExTableEntityColumnNames = AzureTableEntity<TestEnityWithLargeObjectEx>.GetColumnNames();

            Assert.AreEqual(1, regularTableEntityColumnNames.Count);
            Assert.IsTrue(largeObjectExTableEntityColumnNames.Contains("PropertyObject"));

            Assert.AreEqual(5, largeObjectExTableEntityColumnNames.Count);
            Assert.IsTrue(largeObjectExTableEntityColumnNames.Contains("PropertyObject1"));
            Assert.IsTrue(largeObjectExTableEntityColumnNames.Contains("PropertyObject2"));
            Assert.IsTrue(largeObjectExTableEntityColumnNames.Contains("PropertyObject3"));
            Assert.IsTrue(largeObjectExTableEntityColumnNames.Contains("PropertyObject4"));
            Assert.IsTrue(largeObjectExTableEntityColumnNames.Contains("PropertyObject"));

            Assert.AreEqual(11, largeObjectTableEntityColumnNames.Count);
            Assert.IsTrue(largeObjectTableEntityColumnNames.Contains("PropertyObject1"));
            Assert.IsTrue(largeObjectTableEntityColumnNames.Contains("PropertyObject2"));
            Assert.IsTrue(largeObjectTableEntityColumnNames.Contains("PropertyObject3"));
            Assert.IsTrue(largeObjectTableEntityColumnNames.Contains("PropertyObject4"));
            Assert.IsTrue(largeObjectTableEntityColumnNames.Contains("PropertyObject5"));

            Assert.IsTrue(largeObjectTableEntityColumnNames.Contains("LargeString1"));
            Assert.IsTrue(largeObjectTableEntityColumnNames.Contains("LargeString2"));
            Assert.IsTrue(largeObjectTableEntityColumnNames.Contains("LargeString3"));
            Assert.IsTrue(largeObjectTableEntityColumnNames.Contains("LargeString4"));
            Assert.IsTrue(largeObjectTableEntityColumnNames.Contains("LargeString5"));

            Assert.IsTrue(largeObjectTableEntityColumnNames.Contains("PropertyInt"));
        }

        [Test]
        public void TestCompressObject()
        {
            // Set environment variable to make IsFlightEnvironment=true.
            // TODO: remove SetEnvironmentVariable after IsFlightEnvironment method removed.
            var envVarNames = new EnvironmentVariableConstantsConfiguration();
            Environment.SetEnvironmentVariable(envVarNames.AspNetCoreEnvironment, "Development");

            // Case 1: Property marked with CompressObject does not exceed size limit. Will not compress it.
            var compressEntity = new TestEntityWithCompressObject
            {
                PropertyObject = new ComplexObject { PropertyInt = 100, PropertyString = RandomString(40) },
                PropertyString = string.Empty,
            };
            var compressTableEntity = new AzureTableEntity<TestEntityWithCompressObject>("test", "test", compressEntity, "Compress Entity");
            Assert.AreEqual(4, compressTableEntity.GetTableEntity().Properties.Count);
            Assert.AreEqual(EdmType.String, compressTableEntity.GetTableEntity().Properties["PropertyObject"].PropertyType);
            Assert.AreEqual(EdmType.String, compressTableEntity.GetTableEntity().Properties["PropertyString"].PropertyType);
            // For empty string, will not add more quotation marks to it.
            Assert.AreEqual(string.Empty, compressTableEntity.GetTableEntity().Properties["PropertyString"].StringValue);
            Assert.AreEqual("false", compressTableEntity.GetTableEntity().Properties[$"PropertyObject{CompressObjectHelper.CompressedSuffix}"].StringValue);
            Assert.AreEqual("false", compressTableEntity.GetTableEntity().Properties[$"PropertyString{CompressObjectHelper.CompressedSuffix}"].StringValue);

            var actualEntity = compressTableEntity.ResolveEntity();
            Assert.AreEqual(compressEntity.PropertyObject.PropertyInt, actualEntity.PropertyObject.PropertyInt);
            Assert.AreEqual(compressEntity.PropertyObject.PropertyString, actualEntity.PropertyObject.PropertyString);
            Assert.AreEqual(compressEntity.PropertyString, actualEntity.PropertyString);

            // Case 2: Property marked with CompressObject exceeds size limit. Will compress it.
            compressEntity = new TestEntityWithCompressObject
            {
                PropertyObject = new ComplexObject { PropertyInt = 100, PropertyString = RandomString(35000) },
                PropertyString = null,
            };
            compressTableEntity = new AzureTableEntity<TestEntityWithCompressObject>("test", "test", compressEntity, "Compress Entity");
            // PropertyString is null so it will not be stored in table entity.
            Assert.AreEqual(2, compressTableEntity.GetTableEntity().Properties.Count);
            Assert.AreEqual(EdmType.Binary, compressTableEntity.GetTableEntity().Properties["PropertyObject"].PropertyType);
            Assert.AreEqual("true", compressTableEntity.GetTableEntity().Properties[$"PropertyObject{CompressObjectHelper.CompressedSuffix}"].StringValue);

            actualEntity = compressTableEntity.ResolveEntity();
            Assert.AreEqual(compressEntity.PropertyObject.PropertyInt, actualEntity.PropertyObject.PropertyInt);
            Assert.AreEqual(compressEntity.PropertyObject.PropertyString, actualEntity.PropertyObject.PropertyString);
            Assert.AreEqual(compressEntity.PropertyString, actualEntity.PropertyString);

            // Case 3: Property marked with CompressObject does not exceed size limit, and its type is string.
            var testPropertyString = "TestPropertyStringValue";
            compressEntity = new TestEntityWithCompressObject
            {
                PropertyObject = null,
                PropertyString = testPropertyString,
            };
            compressTableEntity = new AzureTableEntity<TestEntityWithCompressObject>("test", "test", compressEntity, "Compress Entity");
            Assert.AreEqual(2, compressTableEntity.GetTableEntity().Properties.Count);
            Assert.AreEqual(EdmType.String, compressTableEntity.GetTableEntity().Properties["PropertyString"].PropertyType);
            // For string type value, will not add more quotation marks to it. String value in table entity should be same as it in object.
            Assert.AreEqual(testPropertyString, compressTableEntity.GetTableEntity().Properties["PropertyString"].StringValue);
            Assert.AreEqual("false", compressTableEntity.GetTableEntity().Properties[$"PropertyString{CompressObjectHelper.CompressedSuffix}"].StringValue);

            actualEntity = compressTableEntity.ResolveEntity();
            Assert.AreEqual(compressEntity.PropertyObject, actualEntity.PropertyObject);
            Assert.AreEqual(compressEntity.PropertyString, actualEntity.PropertyString);
        }

        [Test]
        public void TestLargeObjectAndCompressObject()
        {
            // Set environment variable to make IsFlightEnvironment=true.
            // TODO: remove SetEnvironmentVariable after IsFlightEnvironment method removed.
            var envVarNames = new EnvironmentVariableConstantsConfiguration();
            Environment.SetEnvironmentVariable(envVarNames.AspNetCoreEnvironment, "Development");

            // Case 1: Serialized object size exceeds size limit even though store it with 2 peroperties as LargeObject. Will compress it.
            // Empty string will be serialized as '\"\"' and store in table.
            var largeAndCompressEntity = new TestEntityWithLargeObjectAndCompressObject
            {
                PropertyObject = new ComplexObject { PropertyInt = 100, PropertyString = RandomString(80000) },
                PropertyString = string.Empty,
            };
            var largeAndCompressTableEntity = new AzureTableEntity<TestEntityWithLargeObjectAndCompressObject>("test", "test", largeAndCompressEntity, "Large and Compress Entity");
            Assert.AreEqual(4, largeAndCompressTableEntity.GetTableEntity().Properties.Count);
            Assert.AreEqual(EdmType.Binary, largeAndCompressTableEntity.GetTableEntity().Properties["PropertyObject1"].PropertyType);
            Assert.AreEqual(EdmType.Binary, largeAndCompressTableEntity.GetTableEntity().Properties["PropertyObject2"].PropertyType);
            Assert.AreEqual(EdmType.String, largeAndCompressTableEntity.GetTableEntity().Properties["PropertyString1"].PropertyType);
            Assert.AreEqual(EdmType.String, largeAndCompressTableEntity.GetTableEntity().Properties["PropertyString2"].PropertyType);
            Assert.AreEqual("\"\"", largeAndCompressTableEntity.GetTableEntity().Properties["PropertyString1"].StringValue);

            var actualEntity = largeAndCompressTableEntity.ResolveEntity();
            Assert.AreEqual(largeAndCompressEntity.PropertyObject.PropertyInt, actualEntity.PropertyObject.PropertyInt);
            Assert.AreEqual(largeAndCompressEntity.PropertyObject.PropertyString, actualEntity.PropertyObject.PropertyString);
            Assert.AreEqual(largeAndCompressEntity.PropertyString, actualEntity.PropertyString);

            // Case 2: Serialized object size is within size limit. Will not compress it.
            // Null value property will be serialized as 'null' string and store in table.
            largeAndCompressEntity = new TestEntityWithLargeObjectAndCompressObject
            {
                PropertyObject = new ComplexObject { PropertyInt = 100, PropertyString = RandomString(40) },
                PropertyString = null,
            };
            largeAndCompressTableEntity = new AzureTableEntity<TestEntityWithLargeObjectAndCompressObject>("test", "test", largeAndCompressEntity, "Large and Compress Entity");
            Assert.AreEqual(4, largeAndCompressTableEntity.GetTableEntity().Properties.Count);
            Assert.AreEqual(EdmType.String, largeAndCompressTableEntity.GetTableEntity().Properties["PropertyObject1"].PropertyType);
            Assert.AreEqual(EdmType.String, largeAndCompressTableEntity.GetTableEntity().Properties["PropertyObject2"].PropertyType);
            Assert.AreEqual(EdmType.String, largeAndCompressTableEntity.GetTableEntity().Properties["PropertyString1"].PropertyType);
            Assert.AreEqual(EdmType.String, largeAndCompressTableEntity.GetTableEntity().Properties["PropertyString2"].PropertyType);
            Assert.AreEqual("null", largeAndCompressTableEntity.GetTableEntity().Properties["PropertyString1"].StringValue);

            actualEntity = largeAndCompressTableEntity.ResolveEntity();
            Assert.AreEqual(largeAndCompressEntity.PropertyObject.PropertyInt, actualEntity.PropertyObject.PropertyInt);
            Assert.AreEqual(largeAndCompressEntity.PropertyObject.PropertyString, actualEntity.PropertyObject.PropertyString);
            Assert.AreEqual(largeAndCompressEntity.PropertyString, actualEntity.PropertyString);

            // Case 3: Serialized object size is within size limit, and its type is string.
            // Will not compress it and after serialization raw value will be added with more quotation marks.
            var testPropertyString = "TestPropertyStringValue";
            var testPropertyStringSerialized = "\"TestPropertyStringValue\"";
            largeAndCompressEntity = new TestEntityWithLargeObjectAndCompressObject
            {
                PropertyObject = null,
                PropertyString = testPropertyString,
            };
            largeAndCompressTableEntity = new AzureTableEntity<TestEntityWithLargeObjectAndCompressObject>("test", "test", largeAndCompressEntity, "Large and Compress Entity");
            Assert.AreEqual(EdmType.String, largeAndCompressTableEntity.GetTableEntity().Properties["PropertyObject1"].PropertyType);
            Assert.AreEqual(EdmType.String, largeAndCompressTableEntity.GetTableEntity().Properties["PropertyObject2"].PropertyType);
            Assert.AreEqual(EdmType.String, largeAndCompressTableEntity.GetTableEntity().Properties["PropertyString1"].PropertyType);
            Assert.AreEqual(EdmType.String, largeAndCompressTableEntity.GetTableEntity().Properties["PropertyString2"].PropertyType);
            Assert.AreEqual("null", largeAndCompressTableEntity.GetTableEntity().Properties["PropertyObject1"].StringValue);
            Assert.AreEqual(testPropertyStringSerialized, largeAndCompressTableEntity.GetTableEntity().Properties["PropertyString1"].StringValue);

            actualEntity = largeAndCompressTableEntity.ResolveEntity();
            Assert.AreEqual(largeAndCompressEntity.PropertyObject, actualEntity.PropertyObject);
            Assert.AreEqual(largeAndCompressEntity.PropertyString, actualEntity.PropertyString);
        }

        internal static string RandomString(int size)
        {
            var random = new Random();
            var sb = new StringBuilder();
            
            for (int i = 0; i < size; ++i)
            {
                sb.Append((char)random.Next('A', 'z'));
            }

            return sb.ToString();
        }

    }
}
