﻿using Microsoft.Aether.TokenProvider;
using Microsoft.RelInfra.Storage.Exceptions;
using NUnit.Framework;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Microsoft.RelInfra.Storage.Test
{
    [TestFixture]
    [Category("QTestSkip")]
    public class AzureBlobMigrationCompatibilityTest
    {
        private AzureBlobStorage _storage;
        private AzureBlobStorageV2 _storageV2;

        [OneTimeSetUp]
        public void SetUp()
        {
            var connectionString = AzureCredHelper.GetDevAzureConnectionString();
            // Create v1 blob storage.
            _storage = new AzureBlobStorage(
                connectionString: connectionString,
                numRetries: 5,
                intervalBetweenRetries: new TimeSpan(0, 0, 0, 10));

            // Create v2 blob storage.
            _storageV2 = new AzureBlobStorageV2(
                connectionString: connectionString,
                numRetries: 5,
                intervalBetweenRetries: new TimeSpan(0, 0, 0, 10));
        }

        [Test]
        public async Task Test_AddWithV1AndGetWithV2()
        {
            var resourceId = Guid.NewGuid().ToString();
            var resource = new Storable<string> { Id = resourceId, Entity = "test_string" };
            await _storage.AddAsync(TestSettings.BlobName, resourceId, resource);
            var resourceGet = await _storageV2.GetAsync<string>(TestSettings.BlobName, resourceId);

            Assert.AreEqual(resourceId, resourceGet.Id);
            Assert.AreEqual("test_string", resourceGet.Entity);
            Assert.That(resourceGet.Etag, Is.Not.Null.And.Not.Empty);
            await _storage.DeleteAsync(TestSettings.BlobName, resourceId);
        }

        [Test]
        public async Task Test_AddWithV2AndGetWithV1()
        {
            var resourceId = Guid.NewGuid().ToString();
            var resource = new Storable<string> { Id = resourceId, Entity = "test_string" };
            await _storageV2.AddAsync(TestSettings.BlobName, resourceId, resource);

            var resourceGet = await _storage.GetAsync<string>(TestSettings.BlobName, resourceId);
            Assert.AreEqual(resourceId, resourceGet.Id);
            Assert.AreEqual("test_string", resourceGet.Entity);
            Assert.That(resourceGet.Etag, Is.Not.Null.And.Not.Empty);
            await _storage.DeleteAsync(TestSettings.BlobName, resourceId);
        }

        [Test]
        public async Task Test_UpdateWithV1AndUpdateWithV2()
        {
            var resourceId = Guid.NewGuid().ToString();
            var resource = new Storable<string> { Id = resourceId, Entity = "test_string" };
            await _storage.AddAsync(TestSettings.BlobName, resourceId, resource);
            resource.Entity = "test_string_2";
            await _storage.UpdateAsync(TestSettings.BlobName, resourceId, resource);

            var resourceGet = await _storageV2.GetAsync<string>(TestSettings.BlobName, resourceId);
            Assert.AreEqual(resourceId, resourceGet.Id);
            Assert.AreEqual("test_string_2", resourceGet.Entity);
            Assert.That(resourceGet.Etag, Is.Not.Null.And.Not.Empty);
            await _storage.DeleteAsync(TestSettings.BlobName, resourceId);
        }

        [Test]
        public async Task Test_UpdateWithV2AndUpdateWithV1()
        {
            var resourceId = Guid.NewGuid().ToString();
            var resource = new Storable<string> { Id = resourceId, Entity = "test_string" };
            await _storageV2.AddAsync(TestSettings.BlobName, resourceId, resource);
            resource.Entity = "test_string_2";
            await _storageV2.UpdateAsync(TestSettings.BlobName, resourceId, resource);

            var resourceGet = await _storage.GetAsync<string>(TestSettings.BlobName, resourceId);
            Assert.AreEqual(resourceId, resourceGet.Id);
            Assert.AreEqual("test_string_2", resourceGet.Entity);
            Assert.That(resourceGet.Etag, Is.Not.Null.And.Not.Empty);
            await _storage.DeleteAsync(TestSettings.BlobName, resourceId);
        }

        [Test]
        public async Task Test_AddWithV1AndDeleteWithV2()
        {
            var resourceId = Guid.NewGuid().ToString();
            var resource = new Storable<string> { Id = resourceId, Entity = "test_string" };
            await _storage.AddAsync(TestSettings.BlobName, resourceId, resource);
            await _storageV2.DeleteAsync(TestSettings.BlobName, resourceId);

            try
            {
                await _storageV2.GetAsync<string>(TestSettings.BlobName, resourceId);
            }
            catch (RelInfraStorageException ex) when (ex.Error == StorageError.NotFound)
            {
                // Test is passed.
                return;
            }

            throw new Exception("Not found exception should be thrown.");
        }

        [Test]
        public async Task Test_AddWithV2AndDeleteWithV1()
        {
            var resourceId = Guid.NewGuid().ToString();
            var resource = new Storable<string> { Id = resourceId, Entity = "test_string" };
            await _storageV2.AddAsync(TestSettings.BlobName, resourceId, resource);
            await _storage.DeleteAsync(TestSettings.BlobName, resourceId);

            try
            {
                await _storage.GetAsync<string>(TestSettings.BlobName, resourceId);
            }
            catch (RelInfraStorageException ex) when (ex.Error == StorageError.NotFound)
            {
                // Test is passed.
                return;
            }

            throw new Exception("Not found exception should be thrown.");
        }

        [Test]
        public async Task Test_AddDirectoryWithV1AndGetWithV2()
        {
            string root = Guid.NewGuid().ToString();
            await _storage.AppendAsync(TestSettings.BlobName, root + "/dir1/dir2/1", "test_string");
            await _storage.AppendAsync(TestSettings.BlobName, root + "/dir1/dir2/2", "test_string");
            await _storage.AppendAsync(TestSettings.BlobName, root + "/dir1/dir3/3", "test_string");

            string pathToEnumerate = root + "/dir1/";
            var blobs = (await _storageV2.ListDirectoryAsync(
                                                    containerName: TestSettings.BlobName,
                                                    path: pathToEnumerate,
                                                    fileCountLimit: 100,
                                                    cancellationToken: CancellationToken.None,
                                                    prefixFilter: null)).ToList();
            Console.WriteLine("Listed directory {0} in container {1}:", pathToEnumerate, TestSettings.BlobName);
            blobs.ForEach(Console.WriteLine);
            Assert.AreEqual(3, blobs.Count);
            Assert.IsTrue(blobs.Any(blob => blob.EndsWith("dir1/dir2/1")));
            Assert.IsTrue(blobs.Any(blob => blob.EndsWith("dir1/dir2/2")));
            Assert.IsTrue(blobs.Any(blob => blob.EndsWith("dir1/dir3/3")));
        }

        [Test]
        public async Task Test_AddDirectoryWithV2AndGetWithV1()
        {
            string root = Guid.NewGuid().ToString();
            await _storageV2.AppendAsync(TestSettings.BlobName, root + "/dir1/dir2/1", "test_string");
            await _storageV2.AppendAsync(TestSettings.BlobName, root + "/dir1/dir2/2", "test_string");
            await _storageV2.AppendAsync(TestSettings.BlobName, root + "/dir1/dir3/3", "test_string");

            string pathToEnumerate = root + "/dir1/";
            var blobs = (await _storage.ListDirectoryAsync(
                                                    containerName: TestSettings.BlobName,
                                                    path: pathToEnumerate,
                                                    fileCountLimit: 100,
                                                    cancellationToken: CancellationToken.None,
                                                    prefixFilter: null)).ToList();
            Console.WriteLine("Listed directory {0} in container {1}:", pathToEnumerate, TestSettings.BlobName);
            blobs.ForEach(Console.WriteLine);
            Assert.AreEqual(3, blobs.Count);
            Assert.IsTrue(blobs.Any(blob => blob.EndsWith("dir1/dir2/1")));
            Assert.IsTrue(blobs.Any(blob => blob.EndsWith("dir1/dir2/2")));
            Assert.IsTrue(blobs.Any(blob => blob.EndsWith("dir1/dir3/3")));
        }
    }
}
