﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.RelInfra.Storage.Test.Mocks;
using NUnit.Framework;

namespace Microsoft.RelInfra.Storage.Test
{
    [TestFixture]
    public class TableStorageReplicatorTest
    {
        [Test]
        public async Task ReplicateAsyncTest()
        {
            List<Storable<TestEntity>> sourceEntities = new List<Storable<TestEntity>>()
                                                            {
                                                                CreateEntity("Container1", "Entity1", "Value1"),
                                                                CreateEntity("Container1", "Entity2", "Value2"),
                                                                CreateEntity("Container2", "Entity3", "Value3"),
                                                            };
            List<Storable<TestEntity>> destinationEntities = new List<Storable<TestEntity>>()
                                                            {
                                                                CreateEntity("Container1", "Entity1", "DestValue1"),
                                                                CreateEntity("Container3", "Entity4", "Value4"),
                                                            };

            // initialize
            AzureTableStorageStaticMock source = new AzureTableStorageStaticMock("source");
            await source.InsertOrReplaceAsync(sourceEntities);
            AzureTableStorageStaticMock destination = new AzureTableStorageStaticMock("destination");
            await destination.InsertOrReplaceAsync(destinationEntities);
            
            // default (no delete, no progress)
            TableStorageReplicator<TestEntity> replicator = new TableStorageReplicator<TestEntity>(source, destination);
            long totalCount = await replicator.ReplicateAsync();
            Assert.AreEqual(3, totalCount);
            ValidateStorableCollectionEquivalence(sourceEntities, await source.GetAllAsync<TestEntity>()); // source not modified
            List<Storable<TestEntity>> expectedDestination = new List<Storable<TestEntity>>(sourceEntities);
            expectedDestination.Add(destinationEntities[1]);
            ValidateStorableCollectionEquivalence(expectedDestination, await destination.GetAllAsync<TestEntity>()); // destination is insert/update

            // with delete, with progress
            replicator.DeleteDestinationBeforeCopy = true;
            long progressCount = 0;
            ManualResetEventSlim waitEvent = new ManualResetEventSlim(false);
            Progress<long> progress = new Progress<long>(
                count =>
                    {
                        progressCount += count;
                        waitEvent.Set();
                    });
            totalCount = await replicator.ReplicateAsync(progress);
            Assert.AreEqual(3, totalCount);
            waitEvent.Wait(TimeSpan.FromSeconds(1)); // wait for progress callback
            Assert.AreEqual(totalCount, progressCount);
            ValidateStorableCollectionEquivalence(sourceEntities, await source.GetAllAsync<TestEntity>()); // source not modified
            ValidateStorableCollectionEquivalence(sourceEntities, await destination.GetAllAsync<TestEntity>()); // destination is exact copy
        }

        private static Storable<TestEntity> CreateEntity(string containerName, string resourceId, string value)
        {
            return new Storable<TestEntity>(
                new TestEntity() { StringProperty = value })
                       {
                           ContainerId = containerName,
                           Id = resourceId
                       };
        }

        private static void ValidateStorableCollectionEquivalence(
            IEnumerable<Storable<TestEntity>> expected,
            IEnumerable<Storable<TestEntity>> actual)
        {
            Assert.AreEqual(expected.Count(), actual.Count());

            foreach (Storable<TestEntity> expectedStorable in expected)
            {
                Storable<TestEntity> actualStorable =
                    actual.First(a => a.ContainerId == expectedStorable.ContainerId && a.Id == expectedStorable.Id);

                AssertStorableEquivalence(expectedStorable, actualStorable);
            }
        }

        private static void AssertStorableEquivalence(Storable<TestEntity> expected, Storable<TestEntity> actual)
        {
            Assert.AreEqual(expected.ContainerId, actual.ContainerId);
            Assert.AreEqual(expected.Id, actual.Id);
            AssertTestEntityEquivalence(expected.Entity, actual.Entity);
        }

        private static void AssertTestEntityEquivalence(TestEntity expected, TestEntity actual)
        {
            Assert.AreEqual(expected.StringProperty, actual.StringProperty);
        }

        private class TestEntity
        {
            public string StringProperty { get; set; }
        }
    }
}
