﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <configSections>
        <sectionGroup name="userSettings" type="System.Configuration.UserSettingsGroup, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
            <section name="Microsoft.RelInfra.Storage.Test.TestSettings" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" allowExeDefinition="MachineToLocalUser" requirePermission="false" />
        </sectionGroup>
    </configSections>
    <userSettings>
      <Microsoft.RelInfra.Storage.Test.TestSettings>
        <setting name="AzureTableName" serializeAs="String">
          <value>tabletest</value>
        </setting>
        <setting name="PartitionName" serializeAs="String">
          <value>partitionTest</value>
        </setting>
        <setting name="BlobName" serializeAs="String">
          <value>blobtest</value>
        </setting>
        <setting name="CosmosDbTestUri" serializeAs="String">
          <value>https://aethercosmosdbtest.documents.azure.com:443/</value>
        </setting>
      </Microsoft.RelInfra.Storage.Test.TestSettings>
    </userSettings>
</configuration>