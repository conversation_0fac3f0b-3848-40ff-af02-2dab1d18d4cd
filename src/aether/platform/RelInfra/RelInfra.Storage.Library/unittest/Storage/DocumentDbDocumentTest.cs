﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using Microsoft.Aether.TokenProvider;
using Microsoft.Azure.Cosmos;
using Microsoft.Azure.Cosmos.Scripts;
using Microsoft.RelInfra.Instrumentation;
using Microsoft.RelInfra.Instrumentation.Test.Mocks;
using Microsoft.RelInfra.Storage.Exceptions;
using Newtonsoft.Json;
using NUnit.Framework;
using Relinfra.Storage.CosmosDb;

namespace Microsoft.RelInfra.Storage.Test
{
    [TestFixture]
    [Category("QTestSkip")]
    public class DocumentDbDocumentTest
    {
        private string _collectionId;
        private AzureCosmosDbContainer _container;

        private class TestAddress
        {
            public string State { get; set; }
            public List<int> ZipCode { get; set; }
        }

        private class TestPersonDocument
        {
            public string Id { get; set; }

            public string GetPartitionKey()
            {
                return Id;
            }

            public TestAddress Address { get; set; }
            public string Value { get; set; }

            [JsonProperty(PropertyName="ttl")]
            public int TimeToLive { get; set; }

            public bool BoolValue { get; set; }

            public DateTime? TimeValue { get; set; }

        }

        public void TestSetup(bool createPartition)
        {
            _collectionId = "unittest";
            _container = new AzureCosmosDbContainer(
                endpointUrl: TestSettings.CosmosDbTestUri,
                authToken: AzureCredHelper.GetDevCosmosDbKey(),
                databaseName: "unittest",
                containerName: _collectionId,
                createDefaultPartitions: createPartition,
                connectionMode: ConnectionMode.Gateway,
                counterManager: new CounterManager("test", new MockCounterFactory()));
            _container.InitializeAsync().Wait();
        }
        
        [Test]
        public async Task BasicTest()
        {
            TestSetup(true);
        
            TestPersonDocument doc = CreateTestDoc();

            var created = await _container.AddAsync(doc.GetPartitionKey(), doc.Id, new Storable<TestPersonDocument>(doc));

            var readDoc = await _container.GetAsync<TestPersonDocument>(created.ContainerId, created.Id);
            Assert.AreEqual(readDoc.ContainerId, doc.GetPartitionKey());
            Assert.AreEqual(readDoc.Entity.GetPartitionKey(), doc.GetPartitionKey());
            Assert.AreEqual(readDoc.Entity.Value, doc.Value);
            Assert.AreEqual(readDoc.Id, doc.Id);
            Assert.AreEqual(readDoc.Entity.Id, doc.Id);
            Assert.AreEqual(readDoc.Entity.BoolValue, false);
            Assert.AreEqual(readDoc.Entity.TimeValue, null);

            created.Entity.Value = "2";
            var updated = await _container.UpdateAsync(created.ContainerId, created.Id, created);
            Assert.AreEqual(updated.Entity.Value, created.Entity.Value);
        }

        private TestPersonDocument CreateTestDoc()
        {
            var doc = new TestPersonDocument()
            {
                Id = Guid.NewGuid().ToString(),
                Value = "1",
                Address = new TestAddress() { ZipCode = new List<int> { 123, 456 }, State = "wa" },
                TimeToLive = 600
            };

            return doc;
        }

        [Test]
        public async Task TestUpdateWithOutdatedEtag()
        {
            TestSetup(true);
            TestPersonDocument doc = CreateTestDoc();

            var created = await _container.AddAsync(doc.GetPartitionKey(), doc.Id, new Storable<TestPersonDocument>(doc));
            await _container.UpdateAsync(created.ContainerId, created.Id, created);

            var e = Assert.ThrowsAsync<RelInfraStorageException>(async () => await _container.UpdateAsync(created.ContainerId, created.Id, created));
            Assert.AreEqual(((CosmosException)e.InnerException).StatusCode, HttpStatusCode.PreconditionFailed);
        }

        [Test]
        public async Task TestAddExistingDocument()
        {
            TestSetup(true);
            TestPersonDocument doc = CreateTestDoc();

            await _container.AddAsync(doc.GetPartitionKey(), doc.Id, new Storable<TestPersonDocument>(doc));

            var e = Assert.ThrowsAsync<RelInfraStorageException>(async () => await _container.AddAsync(doc.GetPartitionKey(), doc.Id, new Storable<TestPersonDocument>(doc)));
            Assert.AreEqual(((CosmosException)e.InnerException).StatusCode, HttpStatusCode.Conflict);
        }

        [Test]
        public void TestUpdateNonExistent()
        {
            TestSetup(true);
            TestPersonDocument doc = CreateTestDoc();
            var e = Assert.ThrowsAsync<RelInfraStorageException>(async () => await _container.UpdateAsync(doc.GetPartitionKey(), doc.Id, new Storable<TestPersonDocument>(doc)));
            Assert.AreEqual(e.Error, StorageError.NotFound);
        }

        [Test]
        public async Task TestPut()
        {
            TestSetup(true);
            TestPersonDocument doc = CreateTestDoc();
            var updated = await _container.PutAsync(doc.GetPartitionKey(), doc.Id, new Storable<TestPersonDocument>(doc));
            updated.Entity.Value = "3";
            await _container.PutAsync(doc.GetPartitionKey(), doc.Id, updated);

        }


        [Test]
        public async Task TestPutAndIgnoreEtag()
        {
            TestSetup(true);
            TestPersonDocument doc = CreateTestDoc();
            var updated = await _container.AddAsync(doc.GetPartitionKey(), doc.Id, new Storable<TestPersonDocument>(doc));
            updated.Entity.Value = "3";
            updated.Etag = "etag";
            var updated2 = await _container.PutAndIgnoreEtagAsync(doc.GetPartitionKey(), doc.Id, updated);
            Assert.AreEqual(updated.Entity.Value, updated2.Entity.Value);
        }

        [Test]
        public async Task TestBatchUpdate()
        {
            TestSetup(true);
            TestPersonDocument doc = CreateTestDoc();
            TestPersonDocument doc2 = CreateTestDoc();
            string partitionKey = doc.Id;

            var insertedDocs = (await _container.BatchPutAsync(partitionKey, new[] { new Storable<TestPersonDocument>(doc) { Id = doc.Id}, new Storable<TestPersonDocument>(doc2) { Id = doc2.Id} })).ToList();
            insertedDocs[0].Entity.Value = "3";
            insertedDocs[1].Entity.Value = "4";
            var updatedDocs = await _container.BatchUpdateAsync(partitionKey, insertedDocs);
            Assert.IsTrue(updatedDocs.Any(u => u.Entity.Value == insertedDocs[0].Entity.Value));
            Assert.IsTrue(updatedDocs.Any(u => u.Entity.Value == insertedDocs[1].Entity.Value));
        }

        [Test]
        public async Task TestBatchPutAndIgnoreEtag()
        {
            TestSetup(true);
            TestPersonDocument doc = CreateTestDoc();
            TestPersonDocument doc2 = CreateTestDoc();
            string partitionKey = doc.Id;

            var insertedDocs = (await _container.BatchPutAsync(partitionKey, new[] { new Storable<TestPersonDocument>(doc) { Id = doc.Id }, new Storable<TestPersonDocument>(doc2) { Id = doc2.Id } })).ToList();
            doc.Value = "3";
            doc2.Value = "4";
            var updatedDocs = (await _container.BatchPutAsync(partitionKey, new[] { new Storable<TestPersonDocument>(doc) { Id = doc.Id }, new Storable<TestPersonDocument>(doc2) { Id = doc2.Id } })).ToList();
            Assert.IsTrue(updatedDocs.Any(u => u.Entity.Value == doc.Value));
            Assert.IsTrue(updatedDocs.Any(u => u.Entity.Value == doc2.Value));

            var readDocs = (await _container.BatchGetAsync<TestPersonDocument>(partitionKey, new[] { doc.Id,  doc2.Id })).ToList();
            Assert.IsTrue(readDocs.Any(u => u.Entity.Value == doc.Value));
            Assert.IsTrue(readDocs.Any(u => u.Entity.Value == doc2.Value));

        }

        [Test]
        public async Task TestGetPartition()
        {
            TestSetup(true);
            TestPersonDocument doc = CreateTestDoc();
            TestPersonDocument doc2 = CreateTestDoc();
            string partitionKey = doc.Id;
            var insertedDocs = (await _container.BatchPutAsync(partitionKey, new[] { new Storable<TestPersonDocument>(doc) { Id = doc.Id }, new Storable<TestPersonDocument>(doc2) { Id = doc2.Id} })).ToList();
            var readDocs = await _container.GetPartitionAsync<TestPersonDocument>(partitionKey);
            Assert.AreEqual(readDocs.Count(), 2);
        }

        [Test]
        public async Task TestGetPartitionWithContinuation()
        {
            TestSetup(true);
            TestPersonDocument doc = CreateTestDoc();
            TestPersonDocument doc2 = CreateTestDoc();
            string partitionKey = doc.Id;
            var insertedDocs = (await _container.BatchPutAsync(partitionKey, new[] { new Storable<TestPersonDocument>(doc) { Id = doc.Id}, new Storable<TestPersonDocument>(doc2) { Id = doc2.Id} })).ToList();
            QueryResults<TestPersonDocument> queryResults = await _container.GetPartitionWithCountinuationTokenAsync<TestPersonDocument>(partitionKey, continuationToken: null, takeCount: 1);
            Assert.AreEqual(queryResults.Results.Count(), 1);
            Assert.NotNull(queryResults.ContinuationToken);
            var readDoc1 = queryResults.Results.ElementAt(0);
            queryResults = await _container.GetPartitionWithCountinuationTokenAsync<TestPersonDocument>(partitionKey, new string[] { "Address", "Value"}, queryResults.ContinuationToken);
            Assert.AreEqual(queryResults.Results.Count(), 1);
            Assert.IsNull(queryResults.ContinuationToken);
            var readDoc2 = queryResults.Results.ElementAt(0);

            Assert.IsTrue(readDoc1.Id == doc.Id || readDoc1.Id == doc2.Id);
            Assert.IsTrue(readDoc2.Id == doc.Id || readDoc2.Id == doc2.Id);
            Assert.AreNotEqual(readDoc1.Id, readDoc2.Id);
        }

        [Test]
        public async Task GetWithNoPartitionKeyOnPartitionedCollection()
        {
            TestSetup(true);
            TestPersonDocument doc = CreateTestDoc();

            await _container.AddAsync(doc.GetPartitionKey(), doc.Id, new Storable<TestPersonDocument>(doc));
            Assert.ThrowsAsync<InvalidOperationException>(async() => await _container.GetAsync<TestPersonDocument>(partitionKey: null, id: doc.Id));
        }
        
        [Test]
        public async Task QueryTest()
        {
            TestSetup(true);
            int zipCode = Guid.NewGuid().GetHashCode();
            TestPersonDocument doc1 = CreateTestDoc();
            TestPersonDocument doc2 = CreateTestDoc();
            TestPersonDocument doc3 = CreateTestDoc();
            doc1.Value = "2";
            doc1.Address.ZipCode.Add(zipCode);
            doc2.Address.ZipCode.Add(zipCode);
            doc3.Address.ZipCode = new List<int> { 999 };

            string state = Guid.NewGuid().ToString();
            doc1.Address.State = state;
            doc2.Address.State = state;
            doc3.Address.State = state;

            Console.WriteLine($"doc1 id: {doc1.Id}");
            Console.WriteLine($"doc2 id: {doc2.Id}");
            Console.WriteLine($"doc3 id: {doc3.Id}");
            await _container.AddAsync(doc1.GetPartitionKey(), doc1.Id, new Storable<TestPersonDocument>(doc1));
            await _container.AddAsync(doc2.GetPartitionKey(), doc2.Id, new Storable<TestPersonDocument>(doc2));
            await _container.AddAsync(doc3.GetPartitionKey(), doc3.Id, new Storable<TestPersonDocument>(doc3));

            var results = await _container.ExecuteQueryAsync<TestPersonDocument>($"SELECT * FROM d WHERE ARRAY_CONTAINS(d.Address.ZipCode, {zipCode})");
            Assert.AreEqual(2, results.Count());
            Assert.IsTrue(results.Any(r => r.Entity.Id == doc1.Id && r.Id == doc1.Id && r.ContainerId == doc1.GetPartitionKey()));
            Assert.IsTrue(results.Any(r => r.Entity.Id == doc2.Id && r.Id == doc2.Id && r.ContainerId == doc2.GetPartitionKey()));

            results = await _container.ExecuteQueryAsync<TestPersonDocument>($"SELECT * FROM d WHERE LOWER(d.Address.State) = '{state}'");
            Assert.AreEqual(3, results.Count());
            Assert.IsTrue(results.Any(r => r.Entity.Id == doc1.Id && r.Id == doc1.Id));
            Assert.IsTrue(results.Any(r => r.Entity.Id == doc2.Id && r.Id == doc2.Id));
            Assert.IsTrue(results.Any(r => r.Entity.Id == doc3.Id && r.Id == doc3.Id));

            results = await _container.ExecuteQueryAsync<TestPersonDocument>($"SELECT * FROM d WHERE LOWER(d.Address.State) = '{state}'", 2);
            Assert.AreEqual(2, results.Count());
        }
        
        [Test]
        public async Task TestStoredProcedure()
        {
            TestSetup(true);
            StoredProcedureProperties sproc = new StoredProcedureProperties()
            {
                Body = "function simple(prefix) {" +
                       "var collection = getContext().getCollection(); " +
                       "var isAccepted = collection.queryDocuments( " +
                       "collection.getSelfLink(), " +
                       "'SELECT TOP 1 * FROM r', " +
                       "function(err, feed, options) { " +
                       "if (err) throw err; " +
                       "if (!feed || !feed.length) getContext().getResponse().setBody(\"no docs found\"); " +
                       "else getContext().getResponse().setBody(prefix + JSON.stringify(feed));}); " +
                       "if (!isAccepted) throw new Error(\"not accepted.\"); " +
                       "}",
                Id = Guid.NewGuid().ToString()
            };

            try
            {
                Console.WriteLine($"sproc name {sproc.Id}");
                await _container.ReplaceOrCreateStoredProcedureAsync(sproc);

                TestPersonDocument doc1 = CreateTestDoc();
                await _container.AddAsync(doc1.GetPartitionKey(), doc1.Id, new Storable<TestPersonDocument>(doc1));

                string result = await _container.ExecuteStoredProcedureAsync<string>(doc1.GetPartitionKey(), sproc.Id, "test");
                Assert.IsTrue(result.StartsWith("test"));
                await _container.DeleteAsync(doc1.GetPartitionKey(), doc1.Id);

                // test if update works: change body of the sproc and see if it takes effect
                sproc.Body = "function simple(prefix) {" +
                           "var collection = getContext().getCollection(); " +
                           "var isAccepted = collection.queryDocuments( " +
                           "collection.getSelfLink(), " +
                           "'SELECT TOP 1 * FROM r', " +
                           "function(err, feed, options) { " +
                           "if (err) throw err; " +
                           "if (!feed || !feed.length) getContext().getResponse().setBody(\"no docs found\"); " +
                           "else getContext().getResponse().setBody('aaa' + prefix + JSON.stringify(feed));}); " + // note 'aaa' was added here compared to original sproc
                           "if (!isAccepted) throw new Error(\"not accepted.\"); " +
                           "}";
                await _container.ReplaceOrCreateStoredProcedureAsync(sproc);

                TestPersonDocument doc2 = CreateTestDoc();
                await _container.AddAsync(doc1.GetPartitionKey(), doc1.Id, new Storable<TestPersonDocument>(doc1));

                string result2 = await _container.ExecuteStoredProcedureAsync<string>(doc1.GetPartitionKey(), sproc.Id, "test");
                Assert.IsTrue(result2.StartsWith("aaatest"));
            }
            finally
            {
                await _container.DeleteStoredProcedureAsync(sproc.Id);
            }
        }
    }
}
