﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Microsoft.MachineLearning.Common.Core;
using Microsoft.RelInfra.Common.Serialization;
using Microsoft.WindowsAzure.Storage.Table;
using NUnit.Framework;

namespace Microsoft.RelInfra.Storage.Test
{
    [TestFixture]
    public class AzureTableEntityV2Test
    {
        class ComplexObject
        {
            public int PropertyInt { get; set; }
            public string PropertyString { get; set; }
        }

        class TestEntity
        {
            public int PropertyInt { get; set; }
            public string PropertyString { get; set; }
            public DateTime PropertyDateTime { get; set; }
            public DateTimeOffset PropertyDateTimeOffset { get; set; }

            public int? NullablePropertyInt1 { get; set; }
            public int? NullablePropertyInt2 { get; set; }
            public DateTime? NullablePropertyDateTime { get; set; }
            public DateTimeOffset? NullablePropertyDateTimeOffset { get; set; }
             
            public ComplexObject PropertyObject { get; set; }
        }

        class TestEntityWithLargeObject
        {
            public int PropertyInt { get; set; }

            [LargeObject(5)]
            public ComplexObject PropertyObject { get; set; }

            [LargeObject(5)]
            public string LargeString { get; set; }
        }

        class TestEntityDerivedFromTableEntity : TableEntity
        {
            public int PropertyInt { get; set; }
        }

        class TestEnityWithRegularObject
        {
            public ComplexObject PropertyObject { get; set; }
        }

        class TestEnityWithLargeObjectEx
        {
            [LargeObjectEx(5)]
            public ComplexObject PropertyObject { get; set; }
        }

        class TestEntityWithCompressObject
        {
            [CompressObject]
            public ComplexObject PropertyObject { get; set; }

            [CompressObject]
            public string PropertyString { get; set; }
        }

        class TestEntityWithLargeObjectAndCompressObject
        {
            [LargeObject(2)]
            [CompressObject]
            public ComplexObject PropertyObject { get; set; }

            [LargeObject(2)]
            [CompressObject]
            public string PropertyString { get; set; }
        }

        [Test]
        public void TestDateTimeZoneConversion()
        {
            var utcOriginal = new DateTime(2000, 12, 13, 14, 15, 16, DateTimeKind.Utc);
            var localOriginal = utcOriginal.ToLocalTime();

            if (localOriginal.Ticks == utcOriginal.Ticks)
            {
                //If UTC ticks are same with local ticks, skip this test.
                return;
            }          

            var utc = new TestEntity
            {
                PropertyDateTime = utcOriginal,
                NullablePropertyDateTime = utcOriginal.AddMinutes(10),
            };
            var local = new TestEntity
            {
                PropertyDateTime = localOriginal,
                NullablePropertyDateTime = localOriginal.AddMinutes(10),
            };

            var utcEntity = new AzureTableEntityV2<TestEntity>("part", "utc", utc, "utc");
            var localEntity = new AzureTableEntityV2<TestEntity>("part", "local", local, "local");

            var actualUtc = utcEntity.ResolveEntity();
            var actualLocal = localEntity.ResolveEntity();
            
            Assert.AreEqual(DateTimeKind.Utc, actualUtc.PropertyDateTime.ToUniversalTime().Kind);
            Assert.AreEqual(DateTimeKind.Local, actualLocal.PropertyDateTime.Kind);
            Assert.AreEqual(utcOriginal, actualUtc.PropertyDateTime);
            Assert.AreEqual(utcOriginal, actualLocal.PropertyDateTime.ToUniversalTime());
            Assert.AreNotEqual(utcOriginal, actualLocal.PropertyDateTime);
        }

        [Test]
        public void TestTableEntityCreation()
        {
            var te = new TestEntity
            {
                PropertyInt = 1,
                PropertyString = "test string",
                PropertyDateTime = DateTime.UtcNow,
                PropertyDateTimeOffset = DateTimeOffset.UtcNow,

                NullablePropertyDateTime = DateTime.UtcNow.AddMinutes(10),
                NullablePropertyDateTimeOffset = DateTimeOffset.UtcNow.AddMinutes(10),
                NullablePropertyInt1 = 10,
                NullablePropertyInt2 = null,
                PropertyObject = new ComplexObject {PropertyInt = 100, PropertyString = "complextest"}
            };

            var azureTableEntityV2 = new AzureTableEntityV2<TestEntity>("test", "test", te, "test string");
            var tableEntity = azureTableEntityV2.GetTableEntity();

            Assert.AreEqual((object) 1, tableEntity.GetInt32("PropertyInt"));
            Assert.AreEqual("test string", tableEntity.GetString("PropertyString"));
            Assert.AreEqual(te.PropertyDateTime, tableEntity.GetDateTime("PropertyDateTime"));
            Assert.AreEqual(te.PropertyDateTimeOffset, tableEntity.GetDateTimeOffset("PropertyDateTimeOffset"));
            Assert.AreEqual(te.NullablePropertyDateTime, tableEntity.GetDateTime("NullablePropertyDateTime"));
            Assert.AreEqual(te.NullablePropertyDateTimeOffset, tableEntity.GetDateTimeOffset("NullablePropertyDateTimeOffset"));
            Assert.AreEqual((object) 10, tableEntity.GetInt32("NullablePropertyInt1"));
            Assert.IsFalse(tableEntity.ContainsKey("NullablePropertyInt2"));

            var expectedComplexObjectSerialize = SerializationHelpers.SerializeEntity(te.PropertyObject);
            Assert.AreEqual(expectedComplexObjectSerialize, tableEntity.GetString("PropertyObject"));
            
        }

        [Test]
        public void TestResolvingType()
        {
            var expectedEntity = new TestEntity
            {
                PropertyInt = 1,
                PropertyString = "test string",
                PropertyDateTime = DateTime.UtcNow,
                PropertyDateTimeOffset = DateTimeOffset.UtcNow,

                NullablePropertyDateTime = DateTime.UtcNow.AddMinutes(10),
                NullablePropertyDateTimeOffset = DateTimeOffset.UtcNow.AddMinutes(10),
                NullablePropertyInt1 = 10,
                NullablePropertyInt2 = null,
                PropertyObject = new ComplexObject { PropertyInt = 100, PropertyString = "complextest" }
            };

            var tableEntity =
                new AzureTableEntityV2<TestEntity>((new AzureTableEntityV2<TestEntity>("test", "test", expectedEntity, "test string")).GetTableEntity());

            var actualEntity = tableEntity.ResolveEntity();

            Assert.AreEqual(expectedEntity.PropertyInt, (int) actualEntity.PropertyInt);
            Assert.AreEqual(expectedEntity.PropertyString, actualEntity.PropertyString);
            Assert.AreEqual(expectedEntity.PropertyDateTime, actualEntity.PropertyDateTime);
            Assert.AreEqual(expectedEntity.PropertyDateTimeOffset, actualEntity.PropertyDateTimeOffset);
            Assert.AreEqual(expectedEntity.NullablePropertyDateTime, actualEntity.NullablePropertyDateTime);
            Assert.AreEqual(expectedEntity.NullablePropertyDateTimeOffset, actualEntity.NullablePropertyDateTimeOffset);
            Assert.AreEqual(expectedEntity.NullablePropertyInt1, actualEntity.NullablePropertyInt1);
            Assert.AreEqual(expectedEntity.NullablePropertyInt2, actualEntity.NullablePropertyInt2);
            Assert.AreEqual(expectedEntity.PropertyObject.PropertyInt, (int) actualEntity.PropertyObject.PropertyInt);
            Assert.AreEqual(expectedEntity.PropertyObject.PropertyString, actualEntity.PropertyObject.PropertyString);
        }

        [Test]
        public void TestLOB()
        {
            var expectedEntity = new TestEntityWithLargeObject
            {
                PropertyInt = 1,
                PropertyObject = new ComplexObject { PropertyInt = 100, PropertyString = RandomString(40000)},
                LargeString = RandomString(50000),
            };
            var azureTableEntityV2 = new AzureTableEntityV2<TestEntityWithLargeObject>("test", "test", expectedEntity, "Large String");
            var tableEntity = azureTableEntityV2.GetTableEntity();

            Assert.AreEqual((object) 1, tableEntity.GetInt32("PropertyInt"));
            Assert.AreEqual(13, tableEntity.Count);
            Assert.IsTrue(!string.IsNullOrEmpty(tableEntity.GetString("PropertyObject1")));
            Assert.IsTrue(!string.IsNullOrEmpty(tableEntity.GetString("PropertyObject2")));
            Assert.IsTrue(string.IsNullOrEmpty(tableEntity.GetString("PropertyObject3")));
            Assert.IsTrue(string.IsNullOrEmpty(tableEntity.GetString("PropertyObject4")));
            Assert.IsTrue(string.IsNullOrEmpty(tableEntity.GetString("PropertyObject5")));

            Assert.IsTrue(!string.IsNullOrEmpty(tableEntity.GetString("LargeString1")));
            Assert.IsTrue(!string.IsNullOrEmpty(tableEntity.GetString("LargeString2")));
            Assert.IsTrue(string.IsNullOrEmpty(tableEntity.GetString("LargeString3")));
            Assert.IsTrue(string.IsNullOrEmpty(tableEntity.GetString("LargeString4")));
            Assert.IsTrue(string.IsNullOrEmpty(tableEntity.GetString("LargeString5")));

            var actualEntity = azureTableEntityV2.ResolveEntity();
            Assert.AreEqual(expectedEntity.PropertyInt, (int) actualEntity.PropertyInt);
            Assert.AreEqual(expectedEntity.PropertyObject.PropertyInt, (int) actualEntity.PropertyObject.PropertyInt);
            Assert.AreEqual(expectedEntity.PropertyObject.PropertyString, actualEntity.PropertyObject.PropertyString);
        }

        [Test]
        public void TestTableEntityDerivedClass()
        {
            var te = new TestEntityDerivedFromTableEntity
            {
                PartitionKey = "test",
                RowKey = "test",
                PropertyInt = 1,
            };

            var azureTableEntityV2 = new AzureTableEntityV2<TestEntityDerivedFromTableEntity>("test", "test", te, "test");
            var tableEntity = azureTableEntityV2.GetTableEntity();

            var minDateTime = new DateTimeOffset(1601, 1, 1, 0, 0, 0, TimeSpan.Zero);
            Assert.AreEqual(1, tableEntity.GetInt32("PropertyInt"));
            DateTimeOffset expectedValue = te.Timestamp > minDateTime ? te.Timestamp : minDateTime;
            Assert.AreEqual(expectedValue, tableEntity.GetDateTimeOffset("Timestamp"));
        }

        [Test]
        public void TestRegularToLargeExProperty()
        {
            var largeExEntity = new TestEnityWithLargeObjectEx
            {
                PropertyObject = new ComplexObject { PropertyInt = 100, PropertyString = RandomString(40) },
            };
            var largeExTableEntity = new AzureTableEntityV2<TestEnityWithLargeObjectEx>("test", "test", largeExEntity, "Large Entity");

            Assert.AreEqual(7, largeExTableEntity.GetTableEntity().Count);
            Assert.AreEqual(string.Empty, largeExTableEntity.GetTableEntity().GetString("PropertyObject1"));
            Assert.AreEqual(string.Empty, largeExTableEntity.GetTableEntity().GetString("PropertyObject2"));
            Assert.AreEqual(string.Empty, largeExTableEntity.GetTableEntity().GetString("PropertyObject3"));
            Assert.AreEqual(string.Empty, largeExTableEntity.GetTableEntity().GetString("PropertyObject4"));

            TestEnityWithLargeObjectEx actualEntity = largeExTableEntity.ResolveEntity();
            Assert.AreEqual(largeExEntity.PropertyObject.PropertyInt, actualEntity.PropertyObject.PropertyInt);
            Assert.AreEqual(largeExEntity.PropertyObject.PropertyString, actualEntity.PropertyObject.PropertyString);

            largeExTableEntity.GetTableEntity()["PropertyObject1"] = null;
            largeExTableEntity.GetTableEntity()["PropertyObject2"] = null;
            largeExTableEntity.GetTableEntity()["PropertyObject3"] = null;
            largeExTableEntity.GetTableEntity()["PropertyObject4"] = null;

            actualEntity = largeExTableEntity.ResolveEntity();
            Assert.AreEqual(largeExEntity.PropertyObject.PropertyInt, actualEntity.PropertyObject.PropertyInt);
            Assert.AreEqual(largeExEntity.PropertyObject.PropertyString, actualEntity.PropertyObject.PropertyString);

            largeExTableEntity.GetTableEntity().Remove("PropertyObject1");
            largeExTableEntity.GetTableEntity().Remove("PropertyObject2");
            largeExTableEntity.GetTableEntity().Remove("PropertyObject3");
            largeExTableEntity.GetTableEntity().Remove("PropertyObject4");

            actualEntity = largeExTableEntity.ResolveEntity();
            Assert.AreEqual(largeExEntity.PropertyObject.PropertyInt, actualEntity.PropertyObject.PropertyInt);
            Assert.AreEqual(largeExEntity.PropertyObject.PropertyString, actualEntity.PropertyObject.PropertyString);
        }

        [Test]
        public void TestColumnNames()
        {
            IList<string> regularTableEntityColumnNames = AzureTableEntityV2<TestEnityWithRegularObject>.GetColumnNames();
            IList<string> largeObjectTableEntityColumnNames = AzureTableEntityV2<TestEntityWithLargeObject>.GetColumnNames();
            IList<string> largeObjectExTableEntityColumnNames = AzureTableEntityV2<TestEnityWithLargeObjectEx>.GetColumnNames();

            Assert.AreEqual(1, regularTableEntityColumnNames.Count);
            Assert.IsTrue(largeObjectExTableEntityColumnNames.Contains("PropertyObject"));

            Assert.AreEqual(5, largeObjectExTableEntityColumnNames.Count);
            Assert.IsTrue(largeObjectExTableEntityColumnNames.Contains("PropertyObject1"));
            Assert.IsTrue(largeObjectExTableEntityColumnNames.Contains("PropertyObject2"));
            Assert.IsTrue(largeObjectExTableEntityColumnNames.Contains("PropertyObject3"));
            Assert.IsTrue(largeObjectExTableEntityColumnNames.Contains("PropertyObject4"));
            Assert.IsTrue(largeObjectExTableEntityColumnNames.Contains("PropertyObject"));

            Assert.AreEqual(11, largeObjectTableEntityColumnNames.Count);
            Assert.IsTrue(largeObjectTableEntityColumnNames.Contains("PropertyObject1"));
            Assert.IsTrue(largeObjectTableEntityColumnNames.Contains("PropertyObject2"));
            Assert.IsTrue(largeObjectTableEntityColumnNames.Contains("PropertyObject3"));
            Assert.IsTrue(largeObjectTableEntityColumnNames.Contains("PropertyObject4"));
            Assert.IsTrue(largeObjectTableEntityColumnNames.Contains("PropertyObject5"));

            Assert.IsTrue(largeObjectTableEntityColumnNames.Contains("LargeString1"));
            Assert.IsTrue(largeObjectTableEntityColumnNames.Contains("LargeString2"));
            Assert.IsTrue(largeObjectTableEntityColumnNames.Contains("LargeString3"));
            Assert.IsTrue(largeObjectTableEntityColumnNames.Contains("LargeString4"));
            Assert.IsTrue(largeObjectTableEntityColumnNames.Contains("LargeString5"));

            Assert.IsTrue(largeObjectTableEntityColumnNames.Contains("PropertyInt"));
        }

        [Test]
        public void TestCompressObject()
        {
            // Set environment variable to make IsFlightEnvironment=true.
            // TODO: remove SetEnvironmentVariable after IsFlightEnvironment method removed.
            var envVarNames = new EnvironmentVariableConstantsConfiguration();
            Environment.SetEnvironmentVariable(envVarNames.AspNetCoreEnvironment, "Development");

            // Case 1: Property marked with CompressObject does not exceed size limit. Will not compress it.
            var compressEntity = new TestEntityWithCompressObject
            {
                PropertyObject = new ComplexObject { PropertyInt = 100, PropertyString = RandomString(40) },
                PropertyString = string.Empty,
            };
            var compressTableEntity = new AzureTableEntityV2<TestEntityWithCompressObject>("test", "test", compressEntity, "Compress Entity");
            Assert.AreEqual(6, compressTableEntity.GetTableEntity().Count);
            Assert.IsTrue(!string.IsNullOrEmpty(compressTableEntity.GetTableEntity().GetString("PropertyObject")));
            // For empty string, will not add more quotation marks to it.
            Assert.AreEqual(string.Empty, compressTableEntity.GetTableEntity().GetString("PropertyString"));
            Assert.AreEqual("false", compressTableEntity.GetTableEntity().GetString($"PropertyObject{CompressObjectHelper.CompressedSuffix}"));
            Assert.AreEqual("false", compressTableEntity.GetTableEntity().GetString($"PropertyString{CompressObjectHelper.CompressedSuffix}"));

            var actualEntity = compressTableEntity.ResolveEntity();
            Assert.AreEqual(compressEntity.PropertyObject.PropertyInt, actualEntity.PropertyObject.PropertyInt);
            Assert.AreEqual(compressEntity.PropertyObject.PropertyString, actualEntity.PropertyObject.PropertyString);
            Assert.AreEqual(compressEntity.PropertyString, actualEntity.PropertyString);

            // Case 2: Property marked with CompressObject exceeds size limit. Will compress it.
            compressEntity = new TestEntityWithCompressObject
            {
                PropertyObject = new ComplexObject { PropertyInt = 100, PropertyString = RandomString(35000) },
                PropertyString = null,
            };
            compressTableEntity = new AzureTableEntityV2<TestEntityWithCompressObject>("test", "test", compressEntity, "Compress Entity");
            // PropertyString is null so it will not be stored in table entity.
            Assert.AreEqual(4, compressTableEntity.GetTableEntity().Count);
            //Assert.AreEqual(EdmType.Binary, compressTableEntity.GetTableEntity().Properties["PropertyObject"].PropertyType);
            Assert.IsTrue(compressTableEntity.GetTableEntity().GetBinary("PropertyObject").Count() > 0);
            Assert.AreEqual("true", compressTableEntity.GetTableEntity().GetString($"PropertyObject{CompressObjectHelper.CompressedSuffix}"));

            actualEntity = compressTableEntity.ResolveEntity();
            Assert.AreEqual(compressEntity.PropertyObject.PropertyInt, actualEntity.PropertyObject.PropertyInt);
            Assert.AreEqual(compressEntity.PropertyObject.PropertyString, actualEntity.PropertyObject.PropertyString);
            Assert.AreEqual(compressEntity.PropertyString, actualEntity.PropertyString);

            // Case 3: Property marked with CompressObject does not exceed size limit, and its type is string.
            var testPropertyString = "TestPropertyStringValue";
            compressEntity = new TestEntityWithCompressObject
            {
                PropertyObject = null,
                PropertyString = testPropertyString,
            };
            compressTableEntity = new AzureTableEntityV2<TestEntityWithCompressObject>("test", "test", compressEntity, "Compress Entity");
            Assert.AreEqual(4, compressTableEntity.GetTableEntity().Count);
            // For string type value, will not add more quotation marks to it. String value in table entity should be same as it in object.
            Assert.AreEqual(testPropertyString, compressTableEntity.GetTableEntity().GetString("PropertyString"));
            Assert.AreEqual("false", compressTableEntity.GetTableEntity().GetString($"PropertyString{CompressObjectHelper.CompressedSuffix}"));

            actualEntity = compressTableEntity.ResolveEntity();
            Assert.AreEqual(compressEntity.PropertyObject, actualEntity.PropertyObject);
            Assert.AreEqual(compressEntity.PropertyString, actualEntity.PropertyString);
        }

        [Test]
        public void TestLargeObjectAndCompressObject()
        {
            // Set environment variable to make IsFlightEnvironment=true.
            // TODO: remove SetEnvironmentVariable after IsFlightEnvironment method removed.
            var envVarNames = new EnvironmentVariableConstantsConfiguration();
            Environment.SetEnvironmentVariable(envVarNames.AspNetCoreEnvironment, "Development");

            // Case 1: Serialized object size exceeds size limit even though store it with 2 peroperties as LargeObject. Will compress it.
            // Empty string will be serialized as '\"\"' and store in table.
            var largeAndCompressEntity = new TestEntityWithLargeObjectAndCompressObject
            {
                PropertyObject = new ComplexObject { PropertyInt = 100, PropertyString = RandomString(80000) },
                PropertyString = string.Empty,
            };
            var largeAndCompressTableEntity = new AzureTableEntityV2<TestEntityWithLargeObjectAndCompressObject>("test", "test", largeAndCompressEntity, "Large and Compress Entity");
            Assert.AreEqual(6, largeAndCompressTableEntity.GetTableEntity().Count);
            Assert.IsTrue(largeAndCompressTableEntity.GetTableEntity().GetBinary("PropertyObject1").Count() > 0);
            Assert.IsTrue(largeAndCompressTableEntity.GetTableEntity().GetBinary("PropertyObject2").Count() == 0);
            Assert.AreEqual("\"\"", largeAndCompressTableEntity.GetTableEntity().GetString("PropertyString1"));
            Assert.IsTrue(string.IsNullOrEmpty(largeAndCompressTableEntity.GetTableEntity().GetString("PropertyString2")));

            var actualEntity = largeAndCompressTableEntity.ResolveEntity();
            Assert.AreEqual(largeAndCompressEntity.PropertyObject.PropertyInt, actualEntity.PropertyObject.PropertyInt);
            Assert.AreEqual(largeAndCompressEntity.PropertyObject.PropertyString, actualEntity.PropertyObject.PropertyString);
            Assert.AreEqual(largeAndCompressEntity.PropertyString, actualEntity.PropertyString);

            // Case 2: Serialized object size is within size limit. Will not compress it.
            // Null value property will be serialized as 'null' string and store in table.
            largeAndCompressEntity = new TestEntityWithLargeObjectAndCompressObject
            {
                PropertyObject = new ComplexObject { PropertyInt = 100, PropertyString = RandomString(40) },
                PropertyString = null,
            };
            largeAndCompressTableEntity = new AzureTableEntityV2<TestEntityWithLargeObjectAndCompressObject>("test", "test", largeAndCompressEntity, "Large and Compress Entity");
            Assert.AreEqual(6, largeAndCompressTableEntity.GetTableEntity().Count);
            Assert.IsTrue(largeAndCompressTableEntity.GetTableEntity().GetString("PropertyObject1").Count() > 0);
            Assert.IsTrue(string.IsNullOrEmpty(largeAndCompressTableEntity.GetTableEntity().GetString("PropertyObject2")));
            Assert.AreEqual("null", largeAndCompressTableEntity.GetTableEntity().GetString("PropertyString1"));
            Assert.IsTrue(string.IsNullOrEmpty(largeAndCompressTableEntity.GetTableEntity().GetString("PropertyString2")));

            actualEntity = largeAndCompressTableEntity.ResolveEntity();
            Assert.AreEqual(largeAndCompressEntity.PropertyObject.PropertyInt, actualEntity.PropertyObject.PropertyInt);
            Assert.AreEqual(largeAndCompressEntity.PropertyObject.PropertyString, actualEntity.PropertyObject.PropertyString);
            Assert.AreEqual(largeAndCompressEntity.PropertyString, actualEntity.PropertyString);

            // Case 3: Serialized object size is within size limit, and its type is string.
            // Will not compress it and after serialization raw value will be added with more quotation marks.
            var testPropertyString = "TestPropertyStringValue";
            var testPropertyStringSerialized = "\"TestPropertyStringValue\"";
            largeAndCompressEntity = new TestEntityWithLargeObjectAndCompressObject
            {
                PropertyObject = null,
                PropertyString = testPropertyString,
            };
            largeAndCompressTableEntity = new AzureTableEntityV2<TestEntityWithLargeObjectAndCompressObject>("test", "test", largeAndCompressEntity, "Large and Compress Entity");
            Assert.AreEqual("null", largeAndCompressTableEntity.GetTableEntity().GetString("PropertyObject1"));
            Assert.IsTrue(string.IsNullOrEmpty(largeAndCompressTableEntity.GetTableEntity().GetString("PropertyObject2")));
            Assert.AreEqual(testPropertyStringSerialized, largeAndCompressTableEntity.GetTableEntity().GetString("PropertyString1"));
            Assert.IsTrue(string.IsNullOrEmpty(largeAndCompressTableEntity.GetTableEntity().GetString("PropertyString2")));

            actualEntity = largeAndCompressTableEntity.ResolveEntity();
            Assert.AreEqual(largeAndCompressEntity.PropertyObject, actualEntity.PropertyObject);
            Assert.AreEqual(largeAndCompressEntity.PropertyString, actualEntity.PropertyString);
        }

        internal static string RandomString(int size)
        {
            var random = new Random();
            var sb = new StringBuilder();
            
            for (int i = 0; i < size; ++i)
            {
                sb.Append((char)random.Next('A', 'z'));
            }

            return sb.ToString();
        }
    }
}
