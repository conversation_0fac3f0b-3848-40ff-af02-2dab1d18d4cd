﻿using System;
using System.Threading.Tasks;
using Microsoft.Aether.TokenProvider;
using Microsoft.RelInfra.Storage.Exceptions;
using NUnit.Framework;
using static Microsoft.RelInfra.Storage.Test.AzureTableStorageTest;

namespace Microsoft.RelInfra.Storage.Test
{
    [TestFixture]
    [Category("QTestSkip")]
    public class AzureTableMigrationCompatibilityTest
    {
        private AzureTableStorage _storage;
        private AzureTableStorageV2 _storageV2;

        [OneTimeSetUp]
        public void SetUp()
        {
            var connectionString = AzureCredHelper.GetDevAzureConnectionString();
            // Create v1 table storage.
            _storage = new AzureTableStorage(
                connectionString: connectionString,
                tableName: TestSettings.AzureTableName,
                counterManager: null);
            _storage.CreateIfNotExistsAsync().Wait();

            // Create v2 table storage.
            _storageV2 = new AzureTableStorageV2(
                connectionString: connectionString,
                tableName: TestSettings.AzureTableName,
                counterManager: null);
        }

        [Test]
        public async Task Test_AddWithV1AndGetWithV2()
        {
            var resourceId = Guid.NewGuid().ToString();
            var resource = new Storable<string> { Id = resourceId, Entity = "test_string" };
            await _storage.AddAsync(TestSettings.PartitionName, resourceId, resource);
            var resourceGet = await _storageV2.GetAsync<string>(TestSettings.PartitionName, resourceId);

            Assert.AreEqual(resourceId, resourceGet.Id);
            Assert.AreEqual("test_string", resourceGet.Entity);
            Assert.That(resourceGet.Etag, Is.Not.Null.And.Not.Empty);
            await _storage.DeleteAsync(TestSettings.PartitionName, resourceId);
        }

        [Test]
        public async Task Test_AddWithV2AndGetWithV1()
        {
            var resourceId = Guid.NewGuid().ToString();
            var resource = new Storable<string> { Id = resourceId, Entity = "test_string" };
            await _storageV2.AddAsync(TestSettings.PartitionName, resourceId, resource);

            var resourceGet = await _storage.GetAsync<string>(TestSettings.PartitionName, resourceId);
            Assert.AreEqual(resourceId, resourceGet.Id);
            Assert.AreEqual("test_string", resourceGet.Entity);
            Assert.That(resourceGet.Etag, Is.Not.Null.And.Not.Empty);
            await _storage.DeleteAsync(TestSettings.PartitionName, resourceId);
        }

        [Test]
        public async Task Test_AddWithV1AndGetWithV2_CompositeClass()
        {
            var compositeClass = new CompositeClass { CompositeProperty = new CompositeProperty { Number = 10 } };
            var resourceId = Guid.NewGuid().ToString();

            var resource = new Storable<CompositeClass> { Id = resourceId, Entity = compositeClass };
            await _storage.AddAsync(TestSettings.PartitionName, resourceId, resource);
            var resourceGet = await _storageV2.GetAsync<CompositeClass>(TestSettings.PartitionName, resourceId);

            Assert.AreEqual(resourceId, resourceGet.Id);
            Assert.AreEqual(10, resourceGet.Entity.CompositeProperty.Number);
            Assert.That(resourceGet.Etag, Is.Not.Null.And.Not.Empty);
            await _storage.DeleteAsync(TestSettings.PartitionName, resourceId);
        }

        [Test]
        public async Task Test_AddWithV2AndGetWithV1_CompositeClass()
        {
            var compositeClass = new CompositeClass { CompositeProperty = new CompositeProperty { Number = 10 } };
            var resourceId = Guid.NewGuid().ToString();

            var resource = new Storable<CompositeClass> { Id = resourceId, Entity = compositeClass };
            await _storageV2.AddAsync(TestSettings.PartitionName, resourceId, resource);
            var resourceGet = await _storage.GetAsync<CompositeClass>(TestSettings.PartitionName, resourceId);

            Assert.AreEqual(resourceId, resourceGet.Id);
            Assert.AreEqual(10, resourceGet.Entity.CompositeProperty.Number);
            Assert.That(resourceGet.Etag, Is.Not.Null.And.Not.Empty);
            await _storage.DeleteAsync(TestSettings.PartitionName, resourceId);
        }

        [Test]
        public async Task Test_UpdateWithV1AndUpdateWithV2()
        {
            var resourceId = Guid.NewGuid().ToString();
            var resource = new Storable<string> { Id = resourceId, Entity = "test_string" };
            await _storage.AddAsync(TestSettings.PartitionName, resourceId, resource);
            resource.Entity = "test_string_2";
            await _storage.UpdateAsync(TestSettings.PartitionName, resourceId, resource);

            var resourceGet = await _storageV2.GetAsync<string>(TestSettings.PartitionName, resourceId);
            Assert.AreEqual(resourceId, resourceGet.Id);
            Assert.AreEqual("test_string_2", resourceGet.Entity);
            Assert.That(resourceGet.Etag, Is.Not.Null.And.Not.Empty);
            await _storage.DeleteAsync(TestSettings.PartitionName, resourceId);
        }

        [Test]
        public async Task Test_UpdateWithV2AndUpdateWithV1()
        {
            var resourceId = Guid.NewGuid().ToString();
            var resource = new Storable<string> { Id = resourceId, Entity = "test_string" };
            await _storageV2.AddAsync(TestSettings.PartitionName, resourceId, resource);
            resource.Entity = "test_string_2";
            await _storageV2.UpdateAsync(TestSettings.PartitionName, resourceId, resource);

            var resourceGet = await _storage.GetAsync<string>(TestSettings.PartitionName, resourceId);
            Assert.AreEqual(resourceId, resourceGet.Id);
            Assert.AreEqual("test_string_2", resourceGet.Entity);
            Assert.That(resourceGet.Etag, Is.Not.Null.And.Not.Empty);
            await _storage.DeleteAsync(TestSettings.PartitionName, resourceId);
        }

        [Test]
        public async Task Test_LargeObjectAddWithV1UpdateWithV2AndGetWithV1()
        {
            var largeObject = new TestEntityWithLargeObject { LargeString = "LargeString" };
            var resourceId = Guid.NewGuid().ToString();
            var resource = new Storable<TestEntityWithLargeObject> { Id = resourceId, Entity = largeObject };
            resource = await _storage.AddAsync(TestSettings.PartitionName, resourceId, resource);
            resource.Entity.LargeString = "LargeString_2";
            await _storageV2.UpdateAsync(TestSettings.PartitionName, resourceId, resource);

            var resourceGet = await _storage.GetAsync<TestEntityWithLargeObject>(TestSettings.PartitionName, resourceId);
            Assert.AreEqual(resourceId, resourceGet.Id);
            Assert.AreEqual("LargeString_2", resourceGet.Entity.LargeString);
            Assert.That(resourceGet.Etag, Is.Not.Null.And.Not.Empty);
            await _storage.DeleteAsync(TestSettings.PartitionName, resourceId);
        }

        [Test]
        public async Task Test_LargeObjectAddWithV2UpdateWithV1AndGetWithV2()
        {
            var largeObject = new TestEntityWithLargeObject { LargeString = "LargeString" };
            var resourceId = Guid.NewGuid().ToString();
            var resource = new Storable<TestEntityWithLargeObject> { Id = resourceId, Entity = largeObject };
            resource = await _storageV2.AddAsync(TestSettings.PartitionName, resourceId, resource);
            resource.Entity.LargeString = "LargeString_2";
            await _storage.UpdateAsync(TestSettings.PartitionName, resourceId, resource);

            var resourceGet = await _storageV2.GetAsync<TestEntityWithLargeObject>(TestSettings.PartitionName, resourceId);
            Assert.AreEqual(resourceId, resourceGet.Id);
            Assert.AreEqual("LargeString_2", resourceGet.Entity.LargeString);
            Assert.That(resourceGet.Etag, Is.Not.Null.And.Not.Empty);
            await _storage.DeleteAsync(TestSettings.PartitionName, resourceId);
        }

        [Test]
        public async Task Test_NullableObjectAddWithV1UpdateWithV2AndGetWithV1()
        {
            var time = DateTime.UtcNow;
            var entity = new TestEntityNullable { Prop1 = "test", NullableProp1 = time, NullableProp2 = null };
            var resourceId = Guid.NewGuid().ToString();
            var resource = new Storable<TestEntityNullable> { Id = resourceId, Entity = entity };
            resource = await _storage.AddAsync(TestSettings.PartitionName, resourceId, resource);
            resource.Entity.Prop1 = "test_2";
            resource = await _storageV2.UpdateAsync(TestSettings.PartitionName, resourceId, resource);

            var resourceGet = await _storage.GetAsync<TestEntityNullable>(TestSettings.PartitionName, resourceId);
            Assert.AreEqual(resourceId, resourceGet.Id);
            Assert.AreEqual("test_2", resourceGet.Entity.Prop1);
            Assert.AreEqual(time, resourceGet.Entity.NullableProp1);
            Assert.IsNull(resourceGet.Entity.NullableProp2);
            Assert.That(resourceGet.Etag, Is.Not.Null.And.Not.Empty);
            await _storage.DeleteAsync(TestSettings.PartitionName, resourceId);
        }

        [Test]
        public async Task Test_AddWithV1AndDeleteWithV2()
        {
            var resourceId = Guid.NewGuid().ToString();
            var resource = new Storable<string> { Id = resourceId, Entity = "test_string" };
            await _storage.AddAsync(TestSettings.PartitionName, resourceId, resource);
            await _storageV2.DeleteAsync(TestSettings.PartitionName, resourceId);

            try
            {
                await _storageV2.GetAsync<string>(TestSettings.PartitionName, resourceId);
            }
            catch (RelInfraStorageException ex) when (ex.Error == StorageError.NotFound)
            {
                // Test is passed.
                return;
            }

            throw new Exception("Not found exception should be thrown.");
        }

        [Test]
        public async Task Test_AddWithV2AndDeleteWithV1()
        {
            var resourceId = Guid.NewGuid().ToString();
            var resource = new Storable<string> { Id = resourceId, Entity = "test_string" };
            await _storageV2.AddAsync(TestSettings.PartitionName, resourceId, resource);
            await _storage.DeleteAsync(TestSettings.PartitionName, resourceId);

            try
            {
                await _storage.GetAsync<string>(TestSettings.PartitionName, resourceId);
            }
            catch (RelInfraStorageException ex) when (ex.Error == StorageError.NotFound)
            {
                // Test is passed.
                return;
            }

            throw new Exception("Not found exception should be thrown.");
        }

        class CompositeClass
        {
            public CompositeProperty CompositeProperty { get; set; }
        }

        class CompositeProperty
        {
            public int? Number { get; set; }
        }
    }
}
