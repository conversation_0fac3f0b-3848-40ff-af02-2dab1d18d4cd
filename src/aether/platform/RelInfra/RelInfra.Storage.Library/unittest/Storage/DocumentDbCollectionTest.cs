﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Threading.Tasks;
using Microsoft.Aether.TokenProvider;
using Microsoft.Azure.Documents;
using Microsoft.Azure.Documents.Client;
using Microsoft.RelInfra.Instrumentation;
using Microsoft.RelInfra.Instrumentation.Test.Mocks;
using NUnit.Framework;
using Relinfra.Storage.CosmosDb;
using Index = Microsoft.Azure.Documents.Index;

namespace Microsoft.RelInfra.Storage.Test
{
    [TestFixture]
    [Category("QTestSkip")]
    public class DocumentDbCollectionTest
    {
        private string _collectionId;
        private AzureCosmosDbCollection _collection;

        private class TestAddress
        {
            public string State { get; set; }
            public List<int> ZipCode { get; set; }
        }

        private class TestPersonDocument 
        {
            public string Id { get; set; }

            public string GetPartitionKey()
            {
                return (Id.GetHashCode()%2).ToString();
            }

            public TestAddress Address { get; set; }
            public string Value { get; set; }

        }

        public void TestSetup(bool createPartition)
        {
            _collectionId = Guid.NewGuid().ToString();
            _collection = new AzureCosmosDbCollection(
                endpointUrl: TestSettings.CosmosDbTestUri,
                authToken: AzureCredHelper.GetDevCosmosDbKey(),
                databaseName: "unittest",
                collectionName: _collectionId,
                createDefaultPartitions: createPartition,
                counterManager: new CounterManager("test", new MockCounterFactory()),
                protocol: Protocol.Tcp);
            _collection.InitializeAsync().Wait();
        }

        [TearDown]
        public void TestTearDown()
        {
            _collection.DeleteCollectionAsync().Wait();
        }
        
        private TestPersonDocument CreateTestDoc()
        {
            var doc = new TestPersonDocument()
            {
                Id = Guid.NewGuid().ToString(),
                Value = "1",
                Address = new TestAddress() {ZipCode = new List<int>{123, 456}, State = "wa"}
            };

            return doc;
        }
        
        [Test]
        public async Task TestNoPartitionKey()
        {
            TestSetup(false);
            TestPersonDocument doc = CreateTestDoc();
            
            var created = await _collection.AddAsync(doc.GetPartitionKey(), doc.Id, new Storable<TestPersonDocument>(doc));
            var readDoc = await _collection.GetAsync<TestPersonDocument>(null, created.Id);
            Assert.AreEqual(readDoc.Id, doc.Id);
            Assert.AreEqual(readDoc.Entity.Id, doc.Id);
            Assert.AreEqual(readDoc.Entity.Value, doc.Value);

            readDoc.Entity.Value = "2";
            var updated = await _collection.UpdateAsync(readDoc.ContainerId, readDoc.Id, readDoc);
            Assert.AreEqual(updated.Entity.Id, readDoc.Entity.Id);
            Assert.AreEqual(updated.Id, readDoc.Entity.Id);
            Assert.AreEqual(updated.Entity.Value, readDoc.Entity.Value);
        }
        
        [Test]
        public async Task CreateIndexTest()
        {
            TestSetup(true);
            Console.WriteLine($"collection id: {_collectionId}");
            IndexingPolicy policy = new IndexingPolicy();

            policy.IncludedPaths.Add(new IncludedPath() {Path = "/Address/ZipCode/[]/?",
                Indexes = new Collection<Index> {
            new HashIndex(DataType.Number) }
            });
            
            policy.ExcludedPaths.Add(new ExcludedPath() {Path = "/"});

            await _collection.CreateIndexAsync(policy);
            // create twice
            await _collection.CreateIndexAsync(policy);
            TestPersonDocument doc = CreateTestDoc();
            var created = await _collection.AddAsync(doc.GetPartitionKey() , doc.Id, new Storable<TestPersonDocument>(doc));
            created.Entity.Address.ZipCode = new List<int>(){999};
            await _collection.UpdateAsync(created.ContainerId, created.Id, created);
        }
        
    }
}
