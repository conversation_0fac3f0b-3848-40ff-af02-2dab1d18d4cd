using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.WindowsAzure.Storage;
using NUnit.Framework;

namespace Microsoft.RelInfra.Storage.Test.Mocks
{
    [TestFixture]
    public class QueueMockTest
    {
        [Test]
        public async Task TestQueueMock()
        {
            const string testQueueName = "test_queue";
            IQueue<string> queue = new QueueMock<string>(testQueueName);

            // create a test message
            var testMessage = new Storable<string> {Entity = "test"};

            // put the message into the queue
            await queue.PutAsync(testMessage);

            // get it back
            Storable<string> queueMessage = await queue.GetAsync();

            // test if the same
            Assert.AreEqual(testMessage.Entity, queueMessage.Entity);

            // change it
            queueMessage.Entity = "test2";

            // update it in the queue
            Storable<string> updatedMessage = await queue.UpdateMessageAsync(queueMessage, TimeSpan.FromMilliseconds(0), MessageUpdateFlags.UpdateContent);

            // get it back from the queue and verify if updated
            Storable<string> queueMessage2 = await queue.GetAsync();
            Assert.AreEqual(updatedMessage.Entity, queueMessage2.Entity);

            // update it once more - should fail
            queueMessage.Entity = "test3";

            StorageException lastException = null;
            try
            {
                await queue.UpdateMessageAsync(queueMessage, TimeSpan.FromMilliseconds(0), MessageUpdateFlags.UpdateContent);
            }
            catch (StorageException ex)
            {
                lastException = ex;
            }

            Assert.AreEqual(404, lastException.RequestInformation.HttpStatusCode);


            // try to delete outdated message - should fail
            Assert.IsFalse(await queue.DeleteAsync(queueMessage.Id, queueMessage.Etag));

            // delete the updated message
            Assert.IsTrue(await queue.DeleteAsync(updatedMessage.Id, updatedMessage.Etag));
        }

        [Test]
        public async Task TestAtherQueueLeasesMock()
        {
            const string testQueueName = "test_queue";
            IQueue<string> queue = new QueueMock<string>(testQueueName);

            TimeSpan leaseTime = TimeSpan.FromSeconds(5);

            // put 3 message into the queue
            await queue.PutAsync(new Storable<String> {Entity = "Test1"});
            await queue.PutAsync(new Storable<String> {Entity = "Test2"});
            await queue.PutAsync(new Storable<String> {Entity = "Test3"});

            // get message 1
            Storable<string> queueMessage1 = await queue.GetAsync(leaseTime);
            Assert.IsNotNull(queueMessage1);
            // get message 2
            Storable<string> queueMessage2 = await queue.GetAsync(leaseTime);
            Assert.IsNotNull(queueMessage2);
            // get message 3
            Storable<string> queueMessage3 = await queue.GetAsync(leaseTime);
            Assert.IsNotNull(queueMessage3);

            // now there should be no visible messages in the queue
            Storable<string> nullMessage = await queue.GetAsync();
            Assert.IsNull(nullMessage);

            // wait for leaseTime seconds, all 3 messages should be back there
            Thread.Sleep(leaseTime);
            // get message 1
            queueMessage1 = await queue.GetAsync();
            Assert.IsNotNull(queueMessage1);
            // get message 2
            queueMessage2 = await queue.GetAsync();
            Assert.IsNotNull(queueMessage2);
            // get message 3
            queueMessage3 = await queue.GetAsync();
            Assert.IsNotNull(queueMessage3);
        }

        [Test]
        public async Task TestGetMessages()
        {
            const string testQueueName = "test_queue";
            IQueue<string> queue = new QueueMock<string>(testQueueName);

            IEnumerable<Storable<string>> testEmptyQueue = await queue.GetMessagesAsync(5, TimeSpan.FromSeconds(30));
            Assert.IsEmpty(testEmptyQueue);

            // put 3 message into the queue
            await queue.PutAsync(new Storable<string> { Entity = "Test1" });
            await queue.PutAsync(new Storable<string> { Entity = "Test2" });
            await queue.PutAsync(new Storable<string> { Entity = "Test3" });

            IEnumerable<Storable<string>> messages = (await queue.GetMessagesAsync(5, TimeSpan.FromSeconds(30))).ToList();

            Assert.AreEqual(3, messages.Count());
            Assert.IsNotNull(messages.FirstOrDefault(s => s.Entity == "Test1"));
            Assert.IsNotNull(messages.FirstOrDefault(s => s.Entity == "Test2"));
            Assert.IsNotNull(messages.FirstOrDefault(s => s.Entity == "Test3"));
        }
    }
}
