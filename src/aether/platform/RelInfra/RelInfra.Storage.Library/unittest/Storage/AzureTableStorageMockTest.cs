using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Threading.Tasks;
using Microsoft.RelInfra.Storage.Exceptions;
using NUnit.Framework;

namespace Microsoft.RelInfra.Storage.Test.Mocks
{
    [TestFixture]
    class AzureTableStorageMockTest
    {
        [DataContract]
        class TestObject
        {
            [DataMember]
            public string Id { get; set; }
            [DataMember]
            public string Name { get; set; }
            [DataMember]
            public DateTime CreatedDate { get; set; }
        }

        [DataContract]
        class TestObject2
        {
            [DataMember]
            public string Id { get; set; }
            [DataMember]
            public string Name2 { get; set; }
        }

        [Test]
        public async Task TestExecuteQuery()
        {
            var table = new AzureTableStorageStaticMock("testTable");

            for(int i = 0; i < 5; ++i)
            {
                string id = "id" + i;
                await table.PutAndIgnoreEtagAsync("partition1", id, new Storable<TestObject>
                {
                    Id = id, Entity = new TestObject { Id = id, Name = i < 2 ? "Name"+i : "Name", CreatedDate = DateTime.Now }
                });
            }

            for (int i = 0; i < 5; ++i)
            {
                string id = "id" + i;
                await table.PutAndIgnoreEtagAsync("partition2", id, new Storable<TestObject>
                {
                    Id = id,
                    Entity = new TestObject { Id = id, Name = i > 2 ? "Name" + i : String.Empty, CreatedDate = DateTime.Now }
                });
            }

            List<Storable<TestObject>> entities =
                (await table.ExecuteQueryAsync<TestObject>("(PartitionKey eq 'partition1') and (Name eq 'Name')", null))
                .OrderBy(s => s.Entity.Id).ToList();

            Assert.AreEqual(3, entities.Count);
            Assert.AreEqual("id2", entities[0].Entity.Id);
            Assert.AreEqual("id3", entities[1].Entity.Id);
            Assert.AreEqual("id4", entities[2].Entity.Id);
            Assert.IsTrue(entities.All(e => e.ContainerId == "partition1"));

            entities = (await table.ExecuteQueryAsync<TestObject>("(PartitionKey eq 'partition2') and not(Name gt '')", null))
                    .OrderBy(s => s.Id).ToList();

            Assert.AreEqual(3, entities.Count);
            Assert.AreEqual("id0", entities[0].Entity.Id);
            Assert.AreEqual("id1", entities[1].Entity.Id);
            Assert.AreEqual("id2", entities[2].Entity.Id);
            Assert.IsTrue(entities.All(e => e.ContainerId == "partition2"));

            entities = (await table.ExecuteQueryAsync<TestObject>("(PartitionKey eq 'partition1') or (PartitionKey eq 'partition2')", null))
             .OrderBy(s => s.Entity.Id).ToList();

            Assert.AreEqual(10, entities.Count());
        }

        [Test]
        public async Task TestMergeBehaviourForPut()
        {
            var table = new AzureTableStorageStaticMock("testTable");

            const string id = "testId";
            await table.PutAndIgnoreEtagAsync("partition1", id, new Storable<TestObject>
            {
                Id = id,
                Entity = new TestObject { Id = id, Name = "TestName", CreatedDate = DateTime.Now }
            });

            await table.PutAndIgnoreEtagAsync("partition1", id, new Storable<TestObject2>
            {
                Id = id,
                Entity = new TestObject2 { Id = id, Name2 = "TestName2" }
            });

            Storable<TestObject> obj = await table.GetAsync<TestObject>("partition1", id);
            Storable<TestObject2> obj2 = await table.GetAsync<TestObject2>("partition1", id);

            Assert.AreEqual("testId", obj.Entity.Id);
            Assert.AreEqual("TestName", obj.Entity.Name);
            Assert.AreEqual("partition1", obj.ContainerId);

            Assert.AreEqual("testId", obj2.Entity.Id);
            Assert.AreEqual("TestName2", obj2.Entity.Name2);
            Assert.AreEqual("partition1", obj2.ContainerId);
        }

        [Test]
        public async Task TestMergeBehaviourForUpdate()
        {
            var table = new AzureTableStorageStaticMock("testTable");

            string id = Guid.NewGuid().ToString();
            await table.PutAndIgnoreEtagAsync(TestSettings.PartitionName, id, new Storable<TestObject>
            {
                Id = id,
                Entity = new TestObject { Id = id, Name = "TestName", CreatedDate = DateTime.Now }
            });

            await table.UpdateAsync(TestSettings.PartitionName, id, new Storable<TestObject2>
            {
                Id = id,
                Entity = new TestObject2 { Id = id, Name2 = "TestName2" },
                Etag = "*"
            });

            Storable<TestObject> obj = await table.GetAsync<TestObject>(TestSettings.PartitionName, id);
            Storable<TestObject2> obj2 = await table.GetAsync<TestObject2>(TestSettings.PartitionName, id);

            Assert.AreEqual(id, obj.Entity.Id);
            Assert.AreEqual("TestName", obj.Entity.Name);
            Assert.AreEqual(TestSettings.PartitionName, obj.ContainerId);

            Assert.AreEqual(id, obj2.Entity.Id);
            Assert.AreEqual("TestName2", obj2.Entity.Name2);
            Assert.AreEqual(TestSettings.PartitionName, obj2.ContainerId);
        }        
        
        [Test]
        public async Task TestUpdateSuccess()
        {
            var table = new AzureTableStorageStaticMock("testTable");

            string id = Guid.NewGuid().ToString();
            Storable<TestObject> storableOriginal = await table.PutAndIgnoreEtagAsync(TestSettings.PartitionName, id, new Storable<TestObject>
            {
                Id = id,
                Entity = new TestObject { Id = id, Name = "TestName", CreatedDate = DateTime.Now }
            });

            storableOriginal.Entity.Name = "Updated name";
            await table.UpdateAsync(TestSettings.PartitionName, id, storableOriginal);

            Storable<TestObject> obj = await table.GetAsync<TestObject>(TestSettings.PartitionName, id);

            Assert.AreEqual("Updated name", obj.Entity.Name);
            
        }

        [Test]
        public async Task TestUpdateFail()
        {
            var table = new AzureTableStorageStaticMock("testTable");

            string id = Guid.NewGuid().ToString();
            Storable<TestObject> storableOriginal = await table.PutAndIgnoreEtagAsync(TestSettings.PartitionName, id, new Storable<TestObject>
            {
                Id = id,
                Entity = new TestObject { Id = id, Name = "TestName", CreatedDate = DateTime.Now }
            });

            storableOriginal.Entity.Name = "Updated name";
            storableOriginal.Etag = Guid.NewGuid().ToString();
            Assert.ThrowsAsync<RelInfraStorageException>(async () => await table.UpdateAsync(TestSettings.PartitionName, id, storableOriginal));
        }
    }
}
