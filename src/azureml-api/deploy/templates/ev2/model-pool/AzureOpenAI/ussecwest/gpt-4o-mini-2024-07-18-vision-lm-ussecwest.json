{"name": "gpt-4o-mini-2024-07-18-vision-lm-ussecwest", "properties": {"capacity": null, "excessCapacityPolicy": {"policyType": "PriorityOrder", "priorityOrder": {}}, "hostingConfiguration": {"hostingType": "OnlineEndpoint", "deploymentProperties": {}, "deploymentTags": {}, "instanceType": "Singularity.ND24am_A100_v4_SAT-n1", "modelIdentifier": null, "nodeGroupSize": 0}, "metricType": "Count", "modelName": "gpt-4o-mini-2024-07-18-lm", "physicalEndpoints": [{"capacity": null, "endpointId": "/subscriptions/d64068a3-a5e9-48cd-802e-bb1bfe815196/resourceGroups/ussecwest/providers/Microsoft.MachineLearningServices/workspaces/ws-aoai-ussecwest-prod/onlineEndpoints/aoai-sp-4om-0718-vlm-sat-ussecw", "endpointUri": "https://aoai-sp-4om-0718-vlm-sat-ussecw.ussecwest.inference.ml.azure.microsoft.scloud", "instancesPerDeployment": 3, "ensembleModelPoolResource": null, "managementMode": "Attached", "endpointGoalState": {"target": {"version": 0, "totalInstanceCount": 3, "TotalInstanceCount__COMMENT__": "Capacity specified here is only applied during model pool buildout (PUT Create path).", "maxIntermediateInstanceCount": 0, "maxIntermediateDeploymentCheckpoint": null, "shadowTestInstanceCount": 0, "instanceTypes": [{"vmSku": "Singularity.ND24am_A100_v4_SAT-n1", "quotaName": null}], "modelId": "azureml://registries/openai-devault/models/chive/versions/935038", "deploymentProperties": {"AZUREML_OAI_AML_RESOURCE_URI": "https://ml.azure.microsoft.scloud", "AZUREML_OAI_CLIP_ENDPOINT_URL": "https://aoai-sp-4om-0718-vcl-sat-ussecw.ussecwest.inference.ml.azure.microsoft.scloud/v1", "AZUREML_OAI_ENABLE_MULTIMODAL": "true", "AZUREML_OAI_ENDPOINT_CLIENT_ID": "ec470ba0-3898-4f2a-a481-5f148c4143d8", "AZUREML_OAI_FEATURE_ENABLE_EMBEDDING_CACHE": "true", "AZUREML_OAI_INDEX_BUILDER_URL": "ib_dummy_url", "AZUREML_OAI_IS_DATADOG_ENABLED": "false", "AZUREML_OAI_LD_CAPI_ENABLE_STREAM_OPTIONS": "true", "AZUREML_OAI_LD_CAPI_ENABLE_TOOL_CHOICE_REQUIRED": "true", "AZUREML_OAI_LD_PARALLEL_TOOL_CALLS": "true", "AZUREML_OAI_OPENAI_MODEL_HEADER": "cogsvc-openai-model", "AZUREML_OAI_REDIS_CLIENT_ID": "ec470ba0-3898-4f2a-a481-5f148c4143d8", "AZUREML_OAI_REDIS_CLUSTER_HOST_NAME": "gptv-rx-ussecw-redis-4om-a100.redis.cache.cloudapi.microsoft.scloud:6380", "AZUREML_OAI_REDIS_OBJECT_ID": "0338a91c-2fb6-4c4f-9814-0241b2f6915e", "AZUREML_OAI_USAGE_DATA_PRODUCER_IDENTITY": "ec470ba0-3898-4f2a-a481-5f148c4143d8", "AZUREML_OAI_USAGE_EVENTHUB_NAME": "oaibillingeventhubv1", "AZUREML_OAI_USAGE_EVENTHUB_NAMESPACE": "cognitiveopenaieventhubv1-ussecw-prod.servicebus.cloudapi.microsoft.scloud", "AZUREML_OAI_USAGE_LOGGING_ENABLE": "true", "AZUREML_OAI_USAGE_LOGGING_ENABLE_CAPI": "true", "ContentModerationProxyImage": "mir-cmp:default", "engineId": "azureml://registries/openai-devault/models/oai-a100m80n2r1-chivev/versions/1227959", "IsContentModerationEnabled": "true", "IsMDCEnabled": "false", "MDCCaptureHeaders": "Api-Version,apim-product-id,apim-request-id,apim-subscription-id,apim-timestamp,Azure-Resource-Id-Enc,Azure-Resource-Kind,Azure-Resource-Location,Azure-Resource-Sku,openai-end-user,Referer,User-Agent,azureml-collect-request,x-ms-disable-collect-request,x-ms-disable-collect-response,azure-openai-data-collection,OpenAI-Sample-Tag", "MDCEventHubIdentity": "ec470ba0-3898-4f2a-a481-5f148c4143d8", "MDCMaxPayloadSizeMB": "1", "MDCSampleRatePercentage": "100", "MIR_VMAGENT_DEBUG_MIR_ENVOY_CONSISTENT_HASHING_FACTOR": "100", "MIR_VMAGENT_DEBUG_MIR_ENVOY_RETRY_ATTEMPTS": "2", "MIR_VMAGENT_DEBUG_MIR_ENVOY_SOFT_AFFINITY_ENABLED": "true", "ModerationCompletionTimeout": "500", "ModerationPolicy": "nil", "ModerationPromptMinTimeout": "0", "ModerationPromptTimeoutAfterEngine": "500", "ModerationServerUAIClientId": "ec470ba0-3898-4f2a-a481-5f148c4143d8", "ModerationServerUrl": "rai-orchestrator-grpc-prod.ussecwest.inference.ml.azure.microsoft.scloud:443", "RequireDataCollectionHeader": "true"}, "shadowTestProperties": {}, "deploymentTags": {"AML_DeploymentOwnerDriPath": "PROJECTVIENNASERVICES/Model-as-a-Service", "AML_Force_Update": "05/12/2025", "AML_ProductionResource": "true", "MPS_ReplicasPerNode": "4", "RequestSettingsRequestTimeout": "PT29M"}, "livenessProbe": null, "readinessProbe": null, "endpointSettings": {"endpointTags": {"AllowlistedObjectIds": "ae979d6b-3fcb-435d-9d7d-0090e26d1b00,41eb950a-364c-4b7e-8cdc-a67e15b799fe", "AllowlistedObjectIds1": "7fa8a213-d225-4794-bc58-cc7676cf63e8,0338a91c-2fb6-4c4f-9814-0241b2f6915e", "mmp-model-name": "gpt-4o-mini", "mmp-model-version": "2024-07-18-lm", "EnableExternalRateLimiting": "true", "mmp-model-pool": "gpt-4o-mini-2024-07-18-vision-lm-ussecwest", "MDefDep_gpt-4o-mini-2024-07-18-clip": "gpt-4o-mini-2024-07-18-vision-clip-ussecwest"}, "userAssignedIdentity": "/subscriptions/d64068a3-a5e9-48cd-802e-bb1bfe815196/resourcegroups/managed-identities/providers/Microsoft.ManagedIdentity/userAssignedIdentities/mi-custussec-aoai-prod-lmumjq"}}, "reconcileState": "NotStarted", "requestSettings": null, "errorCategory": "None", "errorDetails": null}, "shadowTests": {}, "isEnsembleUnit": false}], "redisCaches": [], "provisioningState": "Unknown", "buildVersion": null, "modelDefinitionId": "gpt-4o-mini-2024-07-18-lm"}}