{"name": "index-builder-1-ussecwest", "properties": {"capacity": null, "excessCapacityPolicy": {"policyType": "PriorityOrder", "priorityOrder": {}}, "hostingConfiguration": {"hostingType": "OnlineEndpoint", "deploymentProperties": {}, "deploymentTags": {}, "instanceType": "Standard_F16s_v2", "modelIdentifier": null, "nodeGroupSize": 0}, "metricType": "Count", "modelName": "index-builder-1", "physicalEndpoints": [{"capacity": null, "endpointId": "/subscriptions/d64068a3-a5e9-48cd-802e-bb1bfe815196/resourceGroups/ussecwest/providers/Microsoft.MachineLearningServices/workspaces/ws-aoai-ussecwest-prod/onlineEndpoints/aoai-sp-idxb-1-ussecw-prod", "endpointUri": "https://aoai-sp-idxb-1-ussecw-prod.ussecwest.inference.ml.azure.microsoft.scloud", "instancesPerDeployment": 3, "ensembleModelPoolResource": null, "managementMode": "Attached", "endpointGoalState": {"target": {"version": 0, "totalInstanceCount": 0, "maxIntermediateInstanceCount": 0, "maxIntermediateDeploymentCheckpoint": null, "shadowTestInstanceCount": 0, "instanceTypes": [{"vmSku": "Standard_F16s_v2", "quotaName": null}], "modelId": "azureml://registries/openai-extensions/models/main-oai-index-builder/versions/1.0.949983.3bba9c23", "deploymentProperties": {"AZUREML_OAI_AML_RESOURCE_URI": "https://ml.azure.microsoft.scloud", "AZUREML_OAI_INDEX_REDIS_CLIENT_ID": "ec470ba0-3898-4f2a-a481-5f148c4143d8", "AZUREML_OAI_INDEX_REDIS_CLUSTER_HOST_NAME": "indexbuilder-ussecw-sat-redis.privatelink.redis.cache.cloudapi.microsoft.scloud:6380", "AZUREML_OAI_INDEX_REDIS_OBJECT_ID": "0338a91c-2fb6-4c4f-9814-0241b2f6915e", "AZUREML_OAI_IS_DATADOG_ENABLED": "false", "AZUREML_OAI_USAGE_DATA_PRODUCER_IDENTITY": "ec470ba0-3898-4f2a-a481-5f148c4143d8", "MIR_VMAGENT_DEBUG_MIR_ENVOY_CONSISTENT_HASHING_FACTOR": "100", "MIR_VMAGENT_DEBUG_MIR_ENVOY_RETRY_ATTEMPTS": "2", "MIR_VMAGENT_DEBUG_MIR_ENVOY_SOFT_AFFINITY_ENABLED": "true"}, "shadowTestProperties": {}, "deploymentTags": {"AML_DeploymentOwnerDriPath": "PROJECTVIENNASERVICES/Model-as-a-Service", "AML_Force_Update": "05/12/2025", "AML_ProductionResource": "true", "RequestSettingsRequestTimeout": "PT29M"}, "livenessProbe": null, "readinessProbe": null, "endpointSettings": {"endpointTags": {"AllowlistedObjectIds": "ae979d6b-3fcb-435d-9d7d-0090e26d1b00,41eb950a-364c-4b7e-8cdc-a67e15b799fe", "AllowlistedObjectIds1": "7fa8a213-d225-4794-bc58-cc7676cf63e8,0338a91c-2fb6-4c4f-9814-0241b2f6915e", "mmp-model-name": "index-builder", "mmp-model-version": "1", "EnableExternalRateLimiting": "true", "mmp-model-pool": "index-builder-1-ussecwest"}, "userAssignedIdentity": "/subscriptions/d64068a3-a5e9-48cd-802e-bb1bfe815196/resourcegroups/managed-identities/providers/Microsoft.ManagedIdentity/userAssignedIdentities/mi-custussec-aoai-prod-lmumjq"}}, "reconcileState": "NotStarted", "requestSettings": null, "errorCategory": "None", "errorDetails": null}, "shadowTests": {}, "isEnsembleUnit": false}], "redisCaches": [], "provisioningState": "Unknown", "buildVersion": null, "modelDefinitionId": "index-builder-1"}}