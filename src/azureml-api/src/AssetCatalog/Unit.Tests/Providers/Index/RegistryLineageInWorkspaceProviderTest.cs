﻿// <copyright file="RegistryLineageInWorkspaceProviderTest.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.MachineLearning.AssetCatalog.Contracts.Errors;
using Microsoft.MachineLearning.AssetCatalog.Services.Providers.Index;
using Microsoft.MachineLearning.AssetCatalog.Services.Providers.Index.Helpers;
using Microsoft.MachineLearning.Common.Core;
using Microsoft.MachineLearning.Index.Contracts;
using Microsoft.MachineLearning.Schema.Contracts;
using Moq;
using Xunit;

namespace Microsoft.MachineLearning.AssetCatalog.EntryPoints.Unit.Tests.Providers.Index
{
    public class RegistryLineageInWorkspaceProviderTest
    {
        private AssetId _assetId = AssetId.Parse("azureml://registries/testFeed/environments/testenv/versions/1");
        private AssetId _notFoundAssetId = AssetId.Parse("azureml://registries/testFeed/environments/testenv/versions/2");
        private AssetId _notRegistryAssetId = AssetId.Parse("azureml://locations/centraluseuap/workspaces/ff00ed23-026a-4b1d-8163-b2f60b973a59/data/vowpaldataset/versions/2");

        [Fact]
        public async Task Test_NotARegistryAssetThrowsException()
        {
            var request = GetAssetIdRequest(_assetId);

            var indexEntitiesRunsFilterControllerMock = new Mock<IIndexEntitiesFilterController<IndexEntity>>();

            var provider = new RegistryLineageInWorkspaceProvider(indexEntitiesRunsFilterControllerMock.Object);

            await Assert.ThrowsAsync<NotRegistryAssetError>(() => provider.GetLineage("subscriptionId", "resourceGroup", "workspace", _notRegistryAssetId, request, EntityType.Datasets)).ConfigureAwait(false);
        }

        [Fact]
        public async Task Test_CanNotFindTheAssetReturnsEmptyResult()
        {
            var request = GetAssetIdRequest(_assetId);

            var indexEntitiesRunsFilterControllerMock = new Mock<IIndexEntitiesFilterController<IndexEntity>>();
            IndexEntitiesResponse<IndexEntity> indexResult = GetAssetIdIndexResult(_assetId);

            indexEntitiesRunsFilterControllerMock.Setup(controller => controller.GetEntities("subscriptionId", "resourceGroup", "workspace", request, default))
                .Returns(Task.FromResult(indexResult));

            var provider = new RegistryLineageInWorkspaceProvider(indexEntitiesRunsFilterControllerMock.Object);

            var lineage = await provider.GetLineage("subscriptionId", "resourceGroup", "workspace", _notFoundAssetId, request, EntityType.Environments).ConfigureAwait(false);

            Assert.Empty(lineage.RelatedAssets);
        }

        [Fact]
        public async Task Test_IndexRequestsMadeWithFiltersAdded()
        {
            var request = GetAssetIdRequest(_assetId);

            var indexEntitiesRunsFilterControllerMock = new Mock<IIndexEntitiesFilterController<IndexEntity>>();
            IndexEntitiesResponse<IndexEntity> assetIdIndexResult = GetAssetIdIndexResult(_assetId);

            indexEntitiesRunsFilterControllerMock.Setup(
                controller => controller.GetEntities(
                    "subscriptionId",
                    "resourceGroup",
                    "workspace",
                    It.Is<IndexEntitiesRequest>(
                        r => CheckFiltersMatch(request.Filters, r.Filters)),
                    default))
                .Returns(Task.FromResult(assetIdIndexResult));

            var provider = new RegistryLineageInWorkspaceProvider(indexEntitiesRunsFilterControllerMock.Object);

            var lineageResult = await provider.GetLineage("subscriptionId", "resourceGroup", "workspace", _assetId, GetProviderRequest(), EntityType.Datasets).ConfigureAwait(false);

            // Assert that the filters passed to the API include the scenario specific filters
            indexEntitiesRunsFilterControllerMock.Verify(
                m => m.GetEntities(
                    "subscriptionId",
                    "resourceGroup",
                    "workspace",
                    It.Is<IndexEntitiesRequest>(
                        r =>
                        r.Filters.Count == 3
                        && r.Filters.Any(f => f.Field.Equals("relationships/assetId"))
                        && r.Filters.Any(f => f.Field.Equals("annotations/archived"))
                        && r.Filters.Any(f => f.Field.Equals("type"))),
                    default),
                Times.Once);
        }

        private static bool CheckFiltersMatch(IReadOnlyCollection<IndexEntitiesRequestFilter> expectedFilters, IReadOnlyCollection<IndexEntitiesRequestFilter> requestFilters)
        {
            if (expectedFilters.Count != requestFilters.Count)
            {
                return false;
            }

            bool matches = true;
            foreach (var filter in expectedFilters)
            {
                var matchedFilter = requestFilters.Where(
                    rf =>
                    rf.Field.Equals(filter.Field)
                    && rf.Values.First().Equals(filter.Values.First())
                    && rf.Operator.Equals(filter.Operator));
                if (!matchedFilter.Count().Equals(1))
                {
                    return false;
                }
            }
            return matches;
        }

        private static IndexEntitiesRequest GetAssetIdRequest(AssetId assetId)
        {
            return new IndexEntitiesRequest
            {
                Filters = FilterHelpers.GetIndexEntityByAssetId(assetId, "environments")
            };
        }

        private static IndexEntitiesRequest GetProviderRequest()
        {
            return new IndexEntitiesRequest
            {
                Filters = new List<IndexEntitiesRequestFilter>
                {
                    new IndexEntitiesRequestFilter
                    {
                        Field = "type",
                        Operator = "eq",
                        Values = new List<string>
                        {
                            "runs"
                        }
                    }
                }
            };
        }

        private static IndexEntitiesResponse<IndexEntity> GetAssetIdIndexResult(AssetId assetId)
        {
            var indexEntity = new IndexEntity
            {
                AssetId = assetId,
                EntityId = new EntityId(
                       "region",
                       "entityContainerId",
                       "type",
                       "objectId"),
                Properties = new IndexProperties
                {
                    CreationContext = new CreationContext
                    {
                        CreatedBy = null,
                        CreatedTime = DateTimeOffset.UtcNow
                    },
                    ExtensionData = new Dictionary<string, Newtonsoft.Json.Linq.JToken>
                    {
                        { "savedDatasetId", "someId" }
                    }
                },
                Annotations = new IndexAnnotations
                {
                    Tags = new Dictionary<string, string> { { "key", "value" } },
                }
            };

            IndexEntitiesResponse<IndexEntity> indexResult = new IndexEntitiesResponse<IndexEntity>();
            indexResult.Value = new IndexEntity[] { indexEntity };
            return indexResult;
        }
    }
}
