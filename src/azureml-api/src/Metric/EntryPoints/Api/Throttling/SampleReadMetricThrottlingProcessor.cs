﻿// <copyright file="SampleReadMetricThrottlingProcessor.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.MachineLearning.Common.Core.Contracts;
using Microsoft.MachineLearning.Common.Core.Extensions;
using Microsoft.MachineLearning.Common.WebApi.Extensions;
using Microsoft.MachineLearning.Common.WebApi.Throttling.ThrottlingV2;
using Microsoft.MachineLearning.Metric.Contracts;
using Microsoft.MachineLearning.Metric.Services;
using Microsoft.MachineLearning.RunHistory.Contracts.Metric;

namespace Microsoft.MachineLearning.Metric.EntryPoints.Api.Throttling
{
    public abstract class BaseSamplingThrottlingContext : IThrottlingProcessor
    {
        public static Task<IReadOnlyCollection<ThrottlingContextForWorkspace>> GetSamplingThrottlingContext(
            List<string> metricNames,
            Guid workspaceId,
            string dcid,
            string limitKey,
            int entityCount = 1)
        {
            if ((metricNames?.Count ?? 0) == 0)
            {
                metricNames.Add(null);
            }
            return Task.FromResult<IReadOnlyCollection<ThrottlingContextForWorkspace>>(new List<ThrottlingContextForWorkspace>()
            {
                new ThrottlingContextForWorkspace
                {
                    WorkspaceId = workspaceId,
                    ThrottlingContextForApplicableLimitKeys = new List<ThrottlingContextForLimitKey>()
                    {
                        new ThrottlingContextForLimitKey
                        {
                            LimitKey = limitKey,
                            ThrottlingContextForEntityContainers = metricNames.Select(m => new ThrottlingContextForEntityContainer
                            {
                                EntityContainer = $"{limitKey}:{workspaceId}:{dcid}:{m?.HashSegment()}",
                                EntityCount = entityCount
                            }).ToList()
                        }
                    }
                }
            });
        }

        public abstract Task<IReadOnlyCollection<ThrottlingContextForWorkspace>> GetThrottlingContextPerWorkspace(ActionExecutingContext context);

        public abstract IReadOnlyCollection<string> GetLimitKeys();

        public abstract bool IsThrottlingApplicable(ActionExecutingContext context);
    }

    public class SampleReadMetricThrottlingProcessor : BaseSamplingThrottlingContext
    {
        public override Task<IReadOnlyCollection<ThrottlingContextForWorkspace>> GetThrottlingContextPerWorkspace(ActionExecutingContext context)
        {
            return GetSamplingThrottlingContext(
                new List<string>() { context.GetTypedArgument<GetSampledMetricRequestDto>().MetricName },
                context.GetTypedArgument<MetricRunContext>().WorkspaceId,
                context.GetTypedArgument<MetricRunContext>().DataContainerId,
                MetricLimitKeyConstants.SampleReadsPerMetricPerWindow);
        }

        public override IReadOnlyCollection<string> GetLimitKeys()
        {
            return new List<string>() { MetricLimitKeyConstants.SampleReadsPerMetricPerWindow };
        }

        public override bool IsThrottlingApplicable(ActionExecutingContext context)
        {
            return context.TryGetTypedArgument<MetricRunContext>(out var ignored) &&
                context.TryGetTypedArgument<GetSampledMetricRequestDto>(out var ignored2);
        }
    }

    public class SamplesReadMetricThrottlingProcessor : BaseSamplingThrottlingContext
    {
        public override Task<IReadOnlyCollection<ThrottlingContextForWorkspace>> GetThrottlingContextPerWorkspace(ActionExecutingContext context)
        {
            var runContext = context.GetTypedArgument<MetricRunContext>();
            var metricIds = context.TryGetTypedArgument<GetMetricSamplesRequestDto>(out var samplesRequestDto) ?
                samplesRequestDto.MetricIds.Select(m => m.Name).ToList() :
                new List<string>();

            return GetSamplingThrottlingContext(
                metricIds,
                context.GetTypedArgument<MetricRunContext>().WorkspaceId,
                context.GetTypedArgument<MetricRunContext>().DataContainerId,
                MetricLimitKeyConstants.SampleReadsPerMetricPerWindow);
        }

        public override IReadOnlyCollection<string> GetLimitKeys()
        {
            return new List<string>() { MetricLimitKeyConstants.SampleReadsPerMetricPerWindow };
        }

        public override bool IsThrottlingApplicable(ActionExecutingContext context)
        {
            return context.TryGetTypedArgument<MetricRunContext>(out var ignored) &&
                context.TryGetTypedArgument<GetMetricSamplesRequestDto>(out var ignored2);
        }
    }

    public class JobAgnosticSamplesReadMetricThrottlingProcessor : BaseSamplingThrottlingContext
    {
        // Per workspace throttling
        public override Task<IReadOnlyCollection<ThrottlingContextForWorkspace>> GetThrottlingContextPerWorkspace(ActionExecutingContext context)
        {
            var workspaceContext = context.GetTypedArgument<WorkspaceContext2>();
            var requestCount = context.TryGetTypedArgument<JobAgnosticMetricReadRequest<GetMetricSamplesRequestDto>>(out var samplesRequestDto) ?
                samplesRequestDto.JobProperties.SelectMany(x =>
                    samplesRequestDto.Requests.SelectMany(x =>
                        x.MetricIds.Select(y => workspaceContext.WorkspaceId.ToString()))).Count() : 1;

            return GetSamplingThrottlingContext(
                new List<string>(),
                workspaceContext.WorkspaceId,
                null,
                MetricLimitKeyConstants.JobAgnosticSampleReadsPerWorkspacePerWindow,
                requestCount);
        }

        public override IReadOnlyCollection<string> GetLimitKeys()
        {
            return new List<string>() { MetricLimitKeyConstants.JobAgnosticSampleReadsPerWorkspacePerWindow };
        }

        public override bool IsThrottlingApplicable(ActionExecutingContext context)
        {
            return context.TryGetTypedArgument<WorkspaceContext2>(out var ignored);
        }
    }
}
