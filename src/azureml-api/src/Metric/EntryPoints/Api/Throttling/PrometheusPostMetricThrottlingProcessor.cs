﻿// <copyright file="PrometheusPostMetricThrottlingProcessor.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.MachineLearning.Common.WebApi.Extensions;
using Microsoft.MachineLearning.Common.WebApi.Throttling.ThrottlingV2;
using Microsoft.MachineLearning.Metric.Common.Utilities;
using Microsoft.MachineLearning.Metric.Services;
using Prometheus;

namespace Microsoft.MachineLearning.Metric.EntryPoints.Api.Throttling
{
    public class PrometheusPostMetricThrottlingAttribute : TypeFilterAttribute
    {
        public PrometheusPostMetricThrottlingAttribute()
            : base(typeof(ThrottlingV2Attribute))
        {
            Arguments = new object[] { new PrometheusPostMetricThrottlingProcessor() };
        }
    }

    public class PrometheusPostMetricThrottlingProcessor : IThrottlingProcessor
    {
        public IReadOnlyCollection<string> GetLimitKeys()
        {
            return new List<string>() { MetricLimitKeyConstants.PrometheusPostMetricPerWorkspacePerWindow };
        }

        public Task<IReadOnlyCollection<ThrottlingContextForWorkspace>> GetThrottlingContextPerWorkspace(ActionExecutingContext context)
        {
            var writeRequest = context.GetTypedArgument<WriteRequest>();
            var timeSeriesGroupedByResourceId = writeRequest.Timeseries
                .Where(t => t.Labels.Select(l => l.Name).Contains(Constants.PrometheusResourceIdConst))
                .GroupBy(t => t.Labels.First(l => l.Name == Constants.PrometheusResourceIdConst).Value);

            return Task.FromResult<IReadOnlyCollection<ThrottlingContextForWorkspace>>(timeSeriesGroupedByResourceId.Select(g =>
            {
                MetricUtilities.TryParseAmlWorkspaceArmScopeFromPrometheusResourceId(g.Key, out var workspaceScope);
                return new ThrottlingContextForWorkspace()
                {
                    WorkspaceReference = workspaceScope == null ? null : new WorkspaceReference(workspaceScope),
                    ThrottlingContextForApplicableLimitKeys = new List<ThrottlingContextForLimitKey>()
                    {
                        new ThrottlingContextForLimitKey()
                        {
                            LimitKey = MetricLimitKeyConstants.PrometheusPostMetricPerWorkspacePerWindow,
                            ThrottlingContextForEntityContainers = new List<ThrottlingContextForEntityContainer>()
                            {
                                new ThrottlingContextForEntityContainer()
                                {
                                    EntityContainer = $"{MetricLimitKeyConstants.PrometheusPostMetricPerWorkspacePerWindow}:{g.Key}",
                                    EntityCount = 1
                                }
                            }
                        }
                    }
                };
               }).ToList());
        }

        public bool IsThrottlingApplicable(ActionExecutingContext context)
        {
            // TODO: Should probably make this assert something about resourceid?
            return context.TryGetTypedArgument<WriteRequest>(out var writeRequest);
        }
    }
}
