﻿// <copyright file="MetricLimitKeyConstants.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Microsoft.MachineLearning.Common.Core;

namespace Microsoft.MachineLearning.Metric.EntryPoints.Api.Throttling
{
    public class MetricLimitKeyConstants
    {
        public const string MetricsPerExperimentPerWindow = "NewMetrics/Experiment/Window";
        public const string MetricsPerExperimentPerWindowValue = MetricsPerExperimentPerWindow + LimitKeyConstants.ThrottlingWindowValueSuffix;
        public const string SampleReadsPerMetricPerWindow = "SampleReads/Metric/Sec";
        public const string SampleReadsPerMetricPerWindowValue = SampleReadsPerMetricPerWindow + LimitKeyConstants.ThrottlingWindowValueSuffix;
        public const string JobAgnosticSampleReadsPerWorkspacePerWindow = "JobAgnosticSampleReads/Workspace/Sec";
        public const string JobAgnosticSampleReadsPerWorkspacePerWindowValue = JobAgnosticSampleReadsPerWorkspacePerWindow + LimitKeyConstants.ThrottlingWindowValueSuffix;
        public const string RangeReadsPerMetricPerWindow = "RangeReads/Metric/Sec";
        public const string RangeReadsPerMetricPerWindowValue = RangeReadsPerMetricPerWindow + LimitKeyConstants.ThrottlingWindowValueSuffix;
        public const string ListMetricsPerRunPerWindow = "ListMetrics/Run/Sec";
        public const string ListMetricsPerRunPerWindowValue = ListMetricsPerRunPerWindow + LimitKeyConstants.ThrottlingWindowValueSuffix;
        public const string WaitOnIngestPerRunPerWindow = "WaitOnIngest/Run/Sec";
        public const string WaitOnIngestPerRunPerWindowValue = WaitOnIngestPerRunPerWindow + LimitKeyConstants.ThrottlingWindowValueSuffix;
        public const string UploadsPerMetricPerWindow = "Uploads/Metric/Sec";
        public const string UploadsPerMetricPerWindowValue = UploadsPerMetricPerWindow + LimitKeyConstants.ThrottlingWindowValueSuffix;
        public const string UploadsInSyncPerMetricPerWindow = "UploadsInSync/Metric/Sec";
        public const string UploadsInSyncPerMetricPerWindowValue = UploadsPerMetricPerWindow + LimitKeyConstants.ThrottlingWindowValueSuffix;
        public const string PrometheusPostMetricPerWorkspacePerWindow = "PrometheusPostMetric/Workspace/Sec";
        public const string PrometheusPostMetricPerWorkspacePerWindowValue = PrometheusPostMetricPerWorkspacePerWindow + LimitKeyConstants.ThrottlingWindowValueSuffix;
        public const string DeleteMetricsByRunIdPerWorkspacePerWindow = "DeleteMetricsByRunId/Workspace/Sec";
        public const string DeleteMetricsByRunIdPerWorkspacePerWindowValue = DeleteMetricsByRunIdPerWorkspacePerWindow + LimitKeyConstants.ThrottlingWindowValueSuffix;
        public const string DeleteMetricsByDataContainerIdPerWorkspacePerWindow = "DeleteMetricsByDataContainerId/Workspace/Sec";
        public const string DeleteMetricsByDataContainerIdPerWorkspacePerWindowValue = DeleteMetricsByDataContainerIdPerWorkspacePerWindow + LimitKeyConstants.ThrottlingWindowValueSuffix;
    }
}
