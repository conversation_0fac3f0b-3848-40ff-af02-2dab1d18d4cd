﻿// <copyright file="UploadMetricThrottlingProcessor.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.MachineLearning.Common.WebApi.Extensions;
using Microsoft.MachineLearning.Common.WebApi.Throttling.ThrottlingV2;
using Microsoft.MachineLearning.Metric.EntryPoints.Api.Filters;
using Microsoft.MachineLearning.Metric.Services;
using Microsoft.MachineLearning.Metric.Services.AzureMonitor;
using Microsoft.MachineLearning.RunHistory.Contracts;

namespace Microsoft.MachineLearning.Metric.EntryPoints.Api.Throttling
{
    public class UploadMetricThrottlingProcessor : IThrottlingProcessor
    {
        private string _limitKey;

        private UploadMetricThrottlingType _uploadMetricThrottlingType;

        public UploadMetricThrottlingProcessor(UploadMetricThrottlingType uploadMetricThrottlingType)
        {
            _uploadMetricThrottlingType = uploadMetricThrottlingType;

            _limitKey = GetLimitKeyForThrottlingType(uploadMetricThrottlingType);
        }

        public Task<IReadOnlyCollection<ThrottlingContextForWorkspace>> GetThrottlingContextPerWorkspace(ActionExecutingContext context)
        {
            // TODO: Switch over to DCID instead of RunId when API makes that switch/DCID gets added to WorkspaceRunDocument

            Guid workspaceId;
            string basePerMetricContainer;

            if (context.TryGetTypedArgument<MetricRunContext>(out var runContext))
            {
                workspaceId = runContext.WorkspaceId;
                basePerMetricContainer = $"{_limitKey}:{workspaceId}:{runContext.RunId}";
            }
            else
            {
                workspaceId = context.GetTypedArgument<ExperimentContext2>().WorkspaceId;
                var runId = context.ActionArguments["runId"];
                basePerMetricContainer = $"{_limitKey}:{workspaceId}:{runId}";
            }

            Dictionary<string, int> counts;
            if (context.TryGetTypedArgument<BatchIMetricV2Dto>(out var batchIMetricV2Dto))
            {
                counts = GetMetricNameToMetricCount(batchIMetricV2Dto, basePerMetricContainer);
            }
            else if (context.TryGetTypedArgument<BatchMetricDto>(out var batchMetricDto))
            {
                counts = GetMetricNameToMetricCount(batchMetricDto, basePerMetricContainer);
            }
            else
            {
                var metricDto = context.GetTypedArgumentOrNull<MetricDto>();
                if (metricDto?.Name != null && !AzureMonitorMetricsSplitter.IsUtilizationMetric(metricDto.Name))
                {
                    counts = new Dictionary<string, int>() { { $"{basePerMetricContainer}:{metricDto.Name}", 1 } };
                }
                else
                {
                    counts = new Dictionary<string, int>();
                }
            }

            return Task.FromResult<IReadOnlyCollection<ThrottlingContextForWorkspace>>(new List<ThrottlingContextForWorkspace>()
            {
                new ThrottlingContextForWorkspace
                {
                    WorkspaceId = workspaceId,
                    ThrottlingContextForApplicableLimitKeys = new List<ThrottlingContextForLimitKey>()
                    {
                        new ThrottlingContextForLimitKey
                        {
                            LimitKey = _limitKey,
                            ThrottlingContextForEntityContainers = counts.Select(x => new ThrottlingContextForEntityContainer
                            {
                                EntityContainer = x.Key,
                                EntityCount = x.Value
                            }).ToList()
                        }
                    }
                }
            });
        }

        public IReadOnlyCollection<string> GetLimitKeys() => new List<string>() { _limitKey };

        public bool IsThrottlingApplicable(ActionExecutingContext context)
        {
            return (context.TryGetTypedArgument<MetricRunContext>(out _) || context.TryGetTypedArgument<ExperimentContext2>(out _))
                && (context.TryGetTypedArgument<BatchIMetricV2Dto>(out _) || context.TryGetTypedArgument<MetricDto>(out _) || context.TryGetTypedArgument<BatchMetricDto>(out _))
                && (ValidateRequestContainsNonCpuGPuUtilizationMetrics(context) || ValidateSyncUpdateRequest(context));
        }

        private bool ValidateSyncUpdateRequest(ActionExecutingContext context)
        {
            if (_uploadMetricThrottlingType != UploadMetricThrottlingType.UploadSync)
            {
                return false;
            }

            if (context.TryGetTypedArgument<BatchIMetricV2Dto>(out var batchIMetricV2Dto))
            {
                var metricsInRequest = batchIMetricV2Dto?.Values?.Count(m => m?.Name != null) ?? 0;
                return metricsInRequest > 0;
            }
            else if (context.TryGetTypedArgument<BatchMetricDto>(out var batchMetricDto))
            {
                var metricsInRequest = batchMetricDto?.Values?.Count(m => m?.Name != null) ?? 0;
                return metricsInRequest > 0;
            }

            var metricDto = context.GetTypedArgumentOrNull<MetricDto>();
            return metricDto?.Name != null;
        }

        private bool ValidateRequestContainsNonCpuGPuUtilizationMetrics(ActionExecutingContext context)
        {
            if (context.TryGetTypedArgument<BatchIMetricV2Dto>(out var batchIMetricV2Dto))
            {
                var metricsCountedTowardsBucket = batchIMetricV2Dto?.Values?.Where(m => m != null && m.Name != null && !AzureMonitorMetricsSplitter.IsUtilizationMetric(m.Name));
                return !(metricsCountedTowardsBucket == null || metricsCountedTowardsBucket.Count() == 0);
            }
            else if (context.TryGetTypedArgument<BatchMetricDto>(out var batchMetricDto))
            {
                var metricsCountedTowardsBucket = batchMetricDto?.Values?.Where(m => m?.Name != null && !AzureMonitorMetricsSplitter.IsUtilizationMetric(m.Name));
                return !(metricsCountedTowardsBucket == null || metricsCountedTowardsBucket.Count() == 0);
            }

            var metricDto = context.GetTypedArgumentOrNull<MetricDto>();
            return !(metricDto?.Name == null || AzureMonitorMetricsSplitter.IsUtilizationMetric(metricDto.Name));
        }

        private Dictionary<string, int> GetMetricNameToMetricCount(BatchIMetricV2Dto batchedDto, string baseContainer)
        {
            var counts = new Dictionary<string, int>();

            var metricsCountedTowardsBucket = batchedDto?.Values
                ?.Where(m => m?.Name != null && (!AzureMonitorMetricsSplitter.IsUtilizationMetric(m.Name)
                                              || _uploadMetricThrottlingType == UploadMetricThrottlingType.UploadSync));

            if (metricsCountedTowardsBucket == null)
            {
                return counts;
            }

            foreach (var metricDto in metricsCountedTowardsBucket)
            {
                var key = $"{baseContainer}:{metricDto.Name}";
                if (counts.ContainsKey(key))
                {
                    counts[key] += 1;
                }
                else
                {
                    counts[key] = 1;
                }
            }

            return counts;
        }

        private Dictionary<string, int> GetMetricNameToMetricCount(BatchMetricDto batchedDto, string baseBucket)
        {
            var counts = new Dictionary<string, int>();

            var metricsCountedTowardsBucket = batchedDto?.Values?.Where(m => m?.Name != null
                                                                          && !AzureMonitorMetricsSplitter.IsUtilizationMetric(m.Name));

            if (metricsCountedTowardsBucket == null)
            {
                return counts;
            }

            foreach (var metricDto in metricsCountedTowardsBucket)
            {
                var key = $"{baseBucket}:{metricDto.Name}";
                if (counts.ContainsKey(key))
                {
                    counts[key] += 1;
                }
                else
                {
                    counts[key] = 1;
                }
            }

            return counts;
        }

        private string GetLimitKeyForThrottlingType(UploadMetricThrottlingType throttlingType)
        {
            switch (throttlingType)
            {
                case UploadMetricThrottlingType.UploadAsync:
                    return MetricLimitKeyConstants.UploadsPerMetricPerWindow;
                case UploadMetricThrottlingType.UploadSync:
                    return MetricLimitKeyConstants.UploadsInSyncPerMetricPerWindow;
                default:
                    throw new NotImplementedException($"Failed to create a throttling processor for throttling type {throttlingType}");
            }
        }
    }
}
