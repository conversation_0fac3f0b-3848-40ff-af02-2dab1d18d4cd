﻿// <copyright file="MetricsIngestThrottlingAttribute.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using Microsoft.AspNetCore.Mvc;
using Microsoft.MachineLearning.Common.WebApi.Throttling.ThrottlingV2;
using Microsoft.MachineLearning.Metric.EntryPoints.Api.Throttling;

namespace Microsoft.MachineLearning.Metric.EntryPoints.Api.Filters
{
    public enum MetricsIngestThrottlingProcessorType
    {
        RunMetricsController_PostRunMetrics,
        MetricController_PostRunMetrics
    }

    public class MetricsIngestThrottlingAttribute : TypeFilterAttribute
    {
        public MetricsIngestThrottlingAttribute(MetricsIngestThrottlingProcessorType processorType)
            : base(typeof(ThrottlingV2Attribute))
        {
            Arguments = new object[] { new MetricsIngestThrottlingProcessor(SelectRequestInspector(processorType)) };
        }

        public IMetricsIngestRequestInspector SelectRequestInspector(MetricsIngestThrottlingProcessorType processorType)
        {
            switch (processorType)
            {
                case MetricsIngestThrottlingProcessorType.RunMetricsController_PostRunMetrics:
                    return new RunMetricsControllerPostRunMetricsRequestInspector();
                case MetricsIngestThrottlingProcessorType.MetricController_PostRunMetrics:
                    return new MetricControllerPostRunMetricsRequestInspector();
                default:
                    throw new NotImplementedException("Failed to select a MetricsIngestThrottlingProcessor");
            }
        }
    }
}
