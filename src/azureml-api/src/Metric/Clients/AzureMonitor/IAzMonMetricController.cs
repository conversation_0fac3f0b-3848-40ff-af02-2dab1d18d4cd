﻿// <copyright file="IAzMonMetricController.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.MachineLearning.Common.WebApi.Client;

namespace Microsoft.MachineLearning.Metric.Clients.AzureMonitor
{
    // https://docs.microsoft.com/en-us/rest/api/monitor/metrics/list#metricunit
    public enum AzMonUnit
    {
        BitsPerSecond,
        ByteSeconds,
        Bytes,
        BytesPerSeconds, // remove once confirmed not needed
        BytesPerSecond,
        Cores,
        Count,
        CountPerSeconds, // remove once confirmed not needed
        CountPerSecond,
        MilliCores,
        MilliSeconds,
        NanoCores,
        Percent,
        Seconds,
        Unspecified
    }

    public enum AzMonAggregationType
    {
        Average,
        Count,
        Maximum,
        Minimum,
        None,
        Total
    }

    public enum AzMonResultType
    {
        Data,
        Metadata
    }

    public interface IAzMonMetricController
    {
        [HttpGet("{resourceUri}/providers/microsoft.insights/metricNamespaces")]
        [ExtraQueryParameter("api-version", "2017-12-01-preview")]
        Task<AzMonMetricNamespaceCollection> ListMetricNamespaces(string resourceUri);

        [HttpGet("{resourceUri}/providers/microsoft.insights/metricDefinitions")]
        [ExtraQueryParameter("api-version", "2018-01-01")]
        Task<AzMonMetricDefinitionCollection> ListMetricDefinitions(string resourceUri, string metricnamespace = null);

        [HttpGet("{resourceUri}/providers/microsoft.insights/metrics")]
        [ExtraQueryParameter("api-version", "2018-01-01")]
        Task<AzMonListMetricResponse> ListMetrics(
            string resourceUri,
            [Bind(Prefix = "$filter")] string filter = null,
            string aggregation = null,
            string interval = null,
            string metricnames = null,
            string metricnamespace = null,
            string orderby = null,
            AzMonResultType? resultType = null,
            string timespan = null,
            int? top = null);
    }

    // https://docs.microsoft.com/en-us/rest/api/monitor/metrics/list#response
    public class AzMonListMetricResponse
    {
        public int Cost { get; set; }

        public string Interval { get; set; }

        public string Namespace { get; set; }

        public string ResourceRegion { get; set; }

        public string Timespan { get; set; }

        public List<AzMonMetric> Value { get; set; } = new List<AzMonMetric>();
    }

    // https://docs.microsoft.com/en-us/rest/api/monitor/metrics/list#metric
    public class AzMonMetric
    {
        public string Id { get; set; }

        public AzMonLocalizableString Name { get; set; }

        public List<AzMonTimeSeriesElement> Timeseries { get; set; } = new List<AzMonTimeSeriesElement>();

        public string Type { get; set; }

        public AzMonUnit Unit { get; set; }
    }

    // https://docs.microsoft.com/en-us/rest/api/monitor/metrics/list#timeserieselement
    public class AzMonTimeSeriesElement
    {
        public List<AzMonMetricValue> Data { get; set; } = new List<AzMonMetricValue>();

        public List<AzMonMetadataValue> MetadataValues { get; set; } = new List<AzMonMetadataValue>();
    }

    // https://docs.microsoft.com/en-us/rest/api/monitor/metrics/list#metadatavalue
    public class AzMonMetadataValue
    {
        public AzMonLocalizableString Name { get; set; }

        public string Value { get; set; }
    }

    // TODO: Figure out which "numbers" are ints and which are floats
    // https://docs.microsoft.com/en-us/rest/api/monitor/metrics/list#metricvalue
    public class AzMonMetricValue
    {
        public double Average { get; set; }

        public double Count { get; set; }

        public double Maximum { get; set; }

        public double Minimum { get; set; }

        public string TimeStamp { get; set; }

        public double Total { get; set; }
    }

    public class AzMonMetricDefinitionCollection
    {
        public List<AzMonMetricDefinition> Value { get; set; } = new List<AzMonMetricDefinition>();
    }

    public class AzMonMetricDefinition
    {
        public List<AzMonLocalizableString> Dimensions { get; set; } = new List<AzMonLocalizableString>();

        public string Id { get; set; }

        public bool IsDimensionRequired { get; set; }

        public List<AzMonMetricAvailability> MetricAvailabilities { get; set; } = new List<AzMonMetricAvailability>();

        public AzMonLocalizableString Name { get; set; }

        public string Namespace { get; set; }

        public AzMonAggregationType PrimaryAggregationType { get; set; }

        public string ResourceId { get; set; }

        public List<string> SupportedAggregationTypes { get; set; } = new List<string>();

        public AzMonUnit Unit { get; set; }
    }

    public class AzMonMetricAvailability
    {
        public string Retention { get; set; }

        public string TimeGrain { get; set; }
    }

    public class AzMonLocalizableString
    {
        public string LocalizedValue { get; set; }

        public string Value { get; set; }
    }

    public class AzMonMetricNamespaceName
    {
        public string MetricNamespaceName { get; set; }
    }

    public class AzMonMetricNamespace
    {
        public string Id { get; set; }

        public string Name { get; set; }

        public AzMonMetricNamespaceName Properties { get; set; }

        public string Type { get; set; }
    }

    public class AzMonMetricNamespaceCollection
    {
        public List<AzMonMetricNamespace> Value { get; set; } = new List<AzMonMetricNamespace>();
    }
}
