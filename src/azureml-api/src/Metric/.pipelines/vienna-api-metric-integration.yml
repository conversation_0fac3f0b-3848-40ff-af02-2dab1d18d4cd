trigger:
  branches:
    include:
    - refs/heads/master
  batch: True
  paths:
    include:
    - /src/azureml-api/src/RunHistory/*
    - /src/azureml-api/src/Metric/*
    - /src/azureml-api/src/nuget.props
    - /src/azureml-api/src/stylecop.analyzers.ruleset
    - /src/NuGet.Config
    - /src/azureml-api/src/nuget-infra.props
    exclude:
    - /src/azureml-api/src/Metric/.pipelines/Vienna-API-MetricService-UnitTests.yml
schedules:
- cron: 0 */6 * * * # every 6h
  branches:
    include:
    - refs/heads/master
extends:
  template: metric-test-base.yml
  parameters:
    subscriptionId: 72c03bf3-4e69-41af-9532-dfcdc3eefef4
    resourceGroupName: metric-service-e2e-tests-rg
    workspaceName: metric-ws-$(Build.BuildId)
    testScript: >-
        set -e


        dotnet --version

        cd bin/Debug


        # CD into the first folder into bin/Debug (so that we can update the netcore verison later)

        cd $(ls -d */|head -n 1)


        dotnet vstest "--ResultsDirectory:$BUILD_ARTIFACTSTAGINGDIRECTORY" "--logger:trx;LogFileName=results.trx" "--Diag:$BUILD_ARTIFACTSTAGINGDIRECTORY/test.log" "--Blame:CollectDump;CollectAlways=true" Microsoft.MachineLearning.Metric.EntryPoints.Tests.dll --TestCaseFilter:"(TestCategory=Integration)"


        # dotnet vstest "--ResultsDirectory:$BUILD_ARTIFACTSTAGINGDIRECTORY" "--logger:trx;LogFileName=results.trx" "--Diag:$BUILD_ARTIFACTSTAGINGDIRECTORY/test.log" "--Blame:CollectDump;CollectAlways=true"


        # dotnet test --logger:trx;LogFileName=results.trx
