# Variable 'DOTNET_SKIP_FIRST_TIME_EXPERIENCE' was defined in the Variables tab
# Variable 'MetricRootDir' was defined in the Variables tab
# Variable 'OwningTeam' was defined in the Variables tab
# Variable 'resourceGroupName' was defined in the Variables tab
# Variable 'subscriptionId' was defined in the Variables tab
# Variable 'workspaceName' was defined in the Variables tab
# Variable Group 'AML Training R&D Build Secrets' was defined in the Variables tab
# Variable Group 'AML INT Variables' was defined in the Variables tab
# Cron Schedules have been converted using UTC Time Zone and may need to be updated for your location
trigger:
  branches:
    include:
    - refs/heads/master
  batch: true
  paths:
    include:
    - /src/azureml-api/src/RunHistory/*
    - /src/azureml-api/src/Metric/*
    - /src/azureml-api/src/nuget.props
    - /src/azureml-api/src/stylecop.analyzers.ruleset
    - /src/NuGet.Config
    - /src/azureml-api/src/nuget-infra.props
schedules:
- cron: 0 */6 * * * # runs daily, every 6h
  branches:
    include:
    - refs/heads/master
name: $(Date:yyyyMMdd)$(Hours)$(Minutes)$(Seconds)
jobs:
- job: Phase_1
  displayName: Phase 1
  timeoutInMinutes: 100
  cancelTimeoutInMinutes: 1
  pool:
    name: vienna-metric-windows-D4V3
  variables:
    DOTNET_SKIP_FIRST_TIME_EXPERIENCE: true
    MetricRootDir: $(Build.SourcesDirectory)/src/azureml-api/src/Metric
  steps:
  - checkout: self
    fetchDepth: 1
    fetchTags: false
  - task: UseDotNet@2
    displayName: Install dotnet runtime 8.0
    inputs:
      workingDirectory: src/azureml-api
      version: 8.0.x
  - task: UseDotNet@2
    displayName: Install dotnet SDK from global.json
    inputs:
      useGlobalJson: true
  - task: NuGetAuthenticate@1
    displayName: NuGet Authenticate
  - task: CmdLine@2
    displayName: Set DOTNET_SYSTEM_GLOBALIZATION_INVARIANT to 0
    continueOnError: True
    inputs:
      script: echo "##vso[task.setvariable variable=DOTNET_SYSTEM_GLOBALIZATION_INVARIANT]0"
  - task: CmdLine@2
    displayName: Dotnet Info
    inputs:
      script: >
        dotnet --info
  - task: DotNetCoreCLI@2
    displayName: dotnet restore
    inputs:
      command: restore
      projects: src/azureml-api/src/Metric/EntryPoints.Tests/Metric.EntryPoints.Tests.csproj
      selectOrConfig: config
      nugetConfigPath: src/NuGet.Config
      externalEndpoints: cc439b27-943e-44ed-a110-d2b76ddbfa06,8fc127a6-c9c6-47ab-9c1d-4d2610d80f68,8466546f-0613-4e61-a062-12c81452f99d
      verbosityRestore: Minimal
      verbosityPack: Detailed
  - task: DotNetCoreCLI@2
    displayName: dotnet build
    inputs:
      projects: >-
        src/azureml-api/src/Metric/EntryPoints.Tests/Metric.EntryPoints.Tests.csproj

        src/azureml-api/src/Metric/Services.Tests/Metric.Services.Tests.csproj
      verbosityRestore: Detailed
      verbosityPack: Detailed
  - task: DotNetCoreCLI@2
    displayName: Run Tests
    inputs:
      command: test
      projects: src/azureml-api/src/Metric/**/*.Tests.csproj
      arguments: '--no-build --no-restore --collect "Code Coverage;Format=Cobertura" --settings $(MetricRootDir)/CodeCoverage.runsettings --filter "TestCategory=Unit" '
      verbosityRestore: Detailed
      verbosityPack: Detailed
  - task: CmdLine@2
    displayName: Merge Cobertura code coverage report
    inputs:
      script: >
        dotnet tool install -g dotnet-reportgenerator-globaltool

        reportgenerator -reports:$(Agent.WorkFolder)/**/*.cobertura.xml -targetdir:$(MetricRootDir)/CodeCoverage -reporttypes:"Cobertura;HtmlInline_AzurePipelines"
  - task: PublishCodeCoverageResults@1
    displayName: Publish code coverage from $(MetricRootDir)/CodeCoverage/Cobertura.xml
    inputs:
      codeCoverageTool: Cobertura
      summaryFileLocation: $(MetricRootDir)/CodeCoverage/Cobertura.xml
  - task: PublishBuildArtifacts@1
    displayName: 'Publish Artifact: logs'
    condition: always()
    inputs:
      ArtifactName: logs
...
