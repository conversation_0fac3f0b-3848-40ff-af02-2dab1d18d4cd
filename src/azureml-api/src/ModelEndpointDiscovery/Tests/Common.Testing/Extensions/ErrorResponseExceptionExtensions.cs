﻿// <copyright file="BaseErrorCodeException.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Common.Core.Contracts;
using Microsoft.MachineLearning.InferenceCloud.Common.DurableTask;
using Newtonsoft.Json;

namespace Microsoft.MachineLearning.ModelEndpointDiscovery.Common.Testing.Extensions
{
    public static class ErrorResponseExceptionExtensions
    {
        public static ErrorResponse GetErrorResponse(this ErrorResponseException errorResponseException)
        {
            if (string.IsNullOrEmpty(errorResponseException.Message))
            {
                return null;
            }

            return JsonConvert.DeserializeObject<ErrorResponse>(errorResponseException.Message);
        }
    }
}
