﻿// <copyright file="FluentAssertionsExtensions.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using FluentAssertions;
using FluentAssertions.Collections;
using FluentAssertions.Specialized;
using Microsoft.MachineLearning.Common.Core.Contracts.Errors;
using Microsoft.MachineLearning.Common.Core.Exceptions;

namespace Microsoft.MachineLearning.ModelEndpointDiscovery.Common.Testing.Extensions
{
    public static class FluentAssertionsExtensions
    {
        public static void BeEquivalentToWithStrictOrdering<TCollection, TExpectation>(this GenericCollectionAssertions<TCollection> assertions, IEnumerable<TExpectation> expectation)
            => assertions.BeEquivalentTo(expectation, options => options.WithStrictOrdering());

        public static AndConstraint<ExceptionAssertions<BaseException>> BeOfErrorDefinition<TError>(this ExceptionAssertions<BaseException> assertions)
            where TError : BaseErrorDefinition
        {
            assertions.Which.Error.Definition.Code.Should().Be(typeof(TError).Name);
            return assertions.NotBeNull();
        }

        public static async Task<AndConstraint<ExceptionAssertions<BaseException>>> BeOfErrorDefinition<TError>(this Task<ExceptionAssertions<BaseException>> assertions)
            where TError : BaseErrorDefinition
        {
            return (await assertions).BeOfErrorDefinition<TError>();
        }
    }
}
