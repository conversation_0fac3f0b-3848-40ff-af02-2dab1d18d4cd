﻿// <copyright file="ILimiter.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Threading.Tasks;

namespace Microsoft.MachineLearning.AccountRP.EntryPoints.Api.Throttling
{
    public interface ILimiter
    {
        public Task<int> GetLimitForSubscriptionAndUserAgent(Guid? subscriptionId, string userAgent, string limitKey);

        public Task<int> GetLimitForSubscription(Guid? subscriptionId, string limitKey);

        public Task<int> GetLimitForUserAgent(string userAgent, string limitKey);
    }
}
