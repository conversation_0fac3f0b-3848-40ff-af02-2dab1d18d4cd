﻿// <copyright file="TelemetryClient.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Text;
using Microsoft.Extensions.Logging;

namespace Microsoft.MachineLearning.ModelMonitor.Common.Telemetry;

/// <inheritdoc />
public class TelemetryClient : ITelemetryClient
{
    private readonly ILogger _logger;

    /// <summary>
    /// Initializes a new instance of the <see cref="TelemetryClient" /> class.
    /// </summary>
    /// <param name="loggerFactory">The logger factory to use to create logger.</param>
    /// <param name="componentName">The name of the component.</param>
    public TelemetryClient(
        ILoggerFactory loggerFactory,
        string componentName)
    {
        _logger = loggerFactory.CreateLogger(componentName);
    }

    /// <inheritdoc />
    public void LogDebug(string message)
        => Log(LogLevel.Debug, message);

    /// <inheritdoc />
    public void LogError(string message, Exception exception = null)
        => Log(
            LogLevel.Error,
            string.Format("{0}, Error: {1}", message, exception?.ToString() ?? string.Empty));

    /// <inheritdoc />
    public void LogInformation(string message)
        => Log(LogLevel.Information, message);

    /// <inheritdoc />
    public void LogWarning(string message)
        => Log(LogLevel.Warning, message);

    private void Log(LogLevel level, string message)
    {
        StringBuilder builder = new StringBuilder();

        builder
            .Append("Message: ")
            .Append(message);

        _logger.Log(level, builder.ToString());
    }
}