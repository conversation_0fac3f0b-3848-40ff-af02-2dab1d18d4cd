﻿// <copyright file="JobInputDataTypeNotSupported.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Microsoft.MachineLearning.Common.Core.Contracts.Errors;
using Microsoft.MachineLearning.ModelMonitor.Common.Core;

namespace Microsoft.MachineLearning.Endpoints.Batch.Common.Core;
public class JobInputDataTypeNotSupported : ArgumentInvalid
{
    private readonly string _dataType;
    private readonly IList<string> _supportedDataTypes = new List<string>();

    public JobInputDataTypeNotSupported(string dataType, IEnumerable<string> supportedDataTypes)
    {
        _dataType = dataType;
    }

    public override string MessageFormat => $"The specified input data type '{_dataType}' is not supported. " +
        $"Supported data types are: [{_supportedDataTypes.ToCommaSeparatedString()}].";
}
