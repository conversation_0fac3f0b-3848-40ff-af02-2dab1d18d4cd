﻿// <copyright file="MissingArgument.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Text;
using Microsoft.MachineLearning.Common.Core.Contracts.Errors;

namespace Microsoft.MachineLearning.ModelMonitor.Common.Core;

public class MissingArgument : ArgumentInvalid
{
    private readonly string _signalName;
    private readonly string _propertyName;

    public MissingArgument(string signalName, string propertyName)
    {
        _propertyName = propertyName;
        _signalName = signalName;
    }

    public override string MessageFormat => GetFormattedErrorMessage();

    private string GetFormattedErrorMessage()
    {
        StringBuilder sb = new StringBuilder();
        sb.Append($"$\"The value for '{_signalName}.{_propertyName}' is missing.\"");
        return sb.ToString();
    }
}
