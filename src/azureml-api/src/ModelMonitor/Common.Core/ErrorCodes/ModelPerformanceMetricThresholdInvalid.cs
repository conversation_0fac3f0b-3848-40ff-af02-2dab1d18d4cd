﻿// <copyright file="ModelPerformanceMetricThresholdInvalid.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Microsoft.MachineLearning.Common.Core;
using Microsoft.MachineLearning.Common.Core.Contracts.Errors;

namespace Microsoft.MachineLearning.ModelMonitor.Common.Core;

public class ModelPerformanceMetricThresholdInvalid : ArgumentInvalid
{
    private readonly string _taskType;
    private readonly string _thresholdMetric;

    public ModelPerformanceMetricThresholdInvalid(string taskType, string thresholdMetric)
    {
        _taskType = Guard.ArgumentNotNull(taskType, nameof(taskType));
        _thresholdMetric = Guard.ArgumentNotNull(thresholdMetric, nameof(thresholdMetric));
    }

    public override string MessageFormat => $"Metric Threshold {_thresholdMetric} for Model Task Type '{_taskType}' is invalid in model performance signal";
}
