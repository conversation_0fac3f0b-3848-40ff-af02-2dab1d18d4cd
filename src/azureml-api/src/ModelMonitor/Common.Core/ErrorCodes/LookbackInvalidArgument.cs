﻿// <copyright file="LookbackInvalidArgument.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Microsoft.MachineLearning.Common.Core.Contracts.Errors;

namespace Microsoft.MachineLearning.ModelMonitor.Common.Core;

public class LookbackInvalidArgument : ArgumentInvalid
{
    private readonly string _signalName;
    private readonly string _propertyName;

    public LookbackInvalidArgument(string signalName, string propertyName)
    {
        _signalName = signalName;
        _propertyName = propertyName;
    }

    public override string MessageFormat => $"The lookback parameter {_propertyName} doesn't support the granularity less than 1 day in '{_signalName}'.";
}
