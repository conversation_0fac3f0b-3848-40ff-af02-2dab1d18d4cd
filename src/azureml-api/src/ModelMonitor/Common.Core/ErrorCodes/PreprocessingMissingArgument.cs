﻿// <copyright file="PreprocessingMissingArgument.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Microsoft.MachineLearning.Common.Core;
using Microsoft.MachineLearning.Common.Core.Contracts.Errors;

namespace Microsoft.MachineLearning.ModelMonitor.Common.Core;

public class PreprocessingMissingArgument : BadArgument
{
    private readonly string _tagKey;
    private readonly string _dataRangeType;

    public PreprocessingMissingArgument(string tagKey, string dataRangeType)
    {
        _tagKey = Guard.ArgumentNotNull(tagKey, nameof(tagKey));
        _dataRangeType = Guard.ArgumentNotNull(dataRangeType, nameof(dataRangeType));
    }

    public override string MessageFormat => $"Preprocessing tag '{_tagKey}' is required for data range type '{_dataRangeType}' but is missing.";
}
