﻿// <copyright file="PreprocessingNotSupportedForMLTable.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Microsoft.MachineLearning.Common.Core.Contracts.Errors;

namespace Microsoft.MachineLearning.ModelMonitor.Common.Core;

public class PreprocessingNotSupportedForMLTable : NotSupported
{
    public override string MessageFormat => "Preprocessing component Id is not supported for 'BaselineData' and 'TargetData' of type 'MLTable'." +
        $"Please try again without the preprocessing component Id.";
}
