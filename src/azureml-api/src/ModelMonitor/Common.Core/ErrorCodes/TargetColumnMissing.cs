﻿// <copyright file="TargetColumnMissing.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Text;
using Microsoft.MachineLearning.Common.Core.Contracts.Errors;

namespace Microsoft.MachineLearning.ModelMonitor.Common.Core;

public class TargetColumnMissing : ArgumentInvalid
{
    private readonly string _signalName;

    public TargetColumnMissing(string signalName)
    {
        _signalName = signalName;
    }

    public override string MessageFormat => GetFormattedErrorMessage();

    private string GetFormattedErrorMessage()
    {
        StringBuilder sb = new StringBuilder();
        sb.Append($"$\"The key 'target_column' is missing in 'Columns' property for production data and reference data in signal '{_signalName}'. " +
            $"Please define the 'target_column' in production data or reference data and try again.\"");
        return sb.ToString();
    }
}
