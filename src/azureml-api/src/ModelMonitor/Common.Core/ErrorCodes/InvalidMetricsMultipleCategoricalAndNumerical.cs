﻿// <copyright file="InvalidMetricsMultipleCategoricalAndNumerical.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Text;
using Microsoft.MachineLearning.Common.Core.Contracts.Errors;

namespace Microsoft.MachineLearning.ModelMonitor.Common.Core;

public class InvalidMetricsMultipleCategoricalAndNumerical : ArgumentInvalid
{
    private readonly string _signalType;

    public InvalidMetricsMultipleCategoricalAndNumerical(string signalType)
    {
        _signalType = signalType;
    }

    public override string MessageFormat => GetFormattedErrorMessage();

    private string GetFormattedErrorMessage()
    {
        StringBuilder sb = new StringBuilder();
        sb.Append($"The signal type '{_signalType}' cannot contain more than one categorical and numerical metric threshold each." +
            $"Please try again with a valid request.");
        return sb.ToString();
    }
}
