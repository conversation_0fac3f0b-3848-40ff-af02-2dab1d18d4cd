﻿// <copyright file="TaskTypeNotSupported.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Microsoft.MachineLearning.Common.Core;
using Microsoft.MachineLearning.Common.Core.Contracts.Errors;

namespace Microsoft.MachineLearning.ModelMonitor.Common.Core;

public class TaskTypeNotSupported : ArgumentInvalid
{
    private readonly string _taskType;

    public TaskTypeNotSupported(string taskType)
    {
        _taskType = Guard.ArgumentNotNull(taskType, nameof(taskType));
    }

    public override string MessageFormat => $"Model Task Type '{_taskType}' is not supported in model performance signal";
}
