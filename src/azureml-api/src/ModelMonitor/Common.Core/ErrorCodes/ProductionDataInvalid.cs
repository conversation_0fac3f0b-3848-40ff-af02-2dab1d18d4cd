﻿// <copyright file="ProductionDataInvalid.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Microsoft.MachineLearning.Common.Core.Contracts.Errors;

namespace Microsoft.MachineLearning.ModelMonitor.Common.Core;

public class ProductionDataInvalid : ArgumentInvalid
{
    private readonly string _signalName;

    public ProductionDataInvalid(string signalName)
    {
        _signalName = signalName;
    }

    public override string MessageFormat => $"Production Data in signal '{_signalName}' is invalid in model performance signal";
}
