﻿// <copyright file="PreprocessingInvalidArgument.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Microsoft.MachineLearning.Common.Core;
using Microsoft.MachineLearning.Common.Core.Contracts.Errors;

namespace Microsoft.MachineLearning.ModelMonitor.Common.Core;

public class PreprocessingInvalidArgument : ArgumentInvalid
{
    private readonly string _tagKey;
    private readonly string _tagValue;

    public PreprocessingInvalidArgument(string tagKey, string tagValue)
    {
        _tagKey = Guard.ArgumentNotNull(tagKey, nameof(tagKey));
        _tagValue = Guard.ArgumentNotNull(tagValue, nameof(tagValue));
    }

    public override string MessageFormat => $"Preprocessing tag '{_tagKey}' is required but the value '{_tagValue}' is invalid.";
}
