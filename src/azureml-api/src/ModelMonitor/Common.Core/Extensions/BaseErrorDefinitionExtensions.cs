﻿// <copyright file="BaseErrorDefinitionExtensions.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Microsoft.MachineLearning.Common.Core.Contracts.Errors;
using Microsoft.MachineLearning.Common.Core.Exceptions;

namespace Microsoft.MachineLearning.ModelMonitor.Common.Core;

public static class BaseErrorDefinitionExtensions
{
    public static Exception ToException(
        this BaseErrorDefinition errorDefinition,
        params object[] messageParams)
        => new BaseException(errorDefinition.ToBaseError(target: null, messageParams));
}
