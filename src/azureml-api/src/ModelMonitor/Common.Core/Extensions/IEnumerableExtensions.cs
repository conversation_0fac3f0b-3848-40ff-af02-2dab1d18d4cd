﻿// <copyright file="IEnumerableExtensions.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

namespace Microsoft.MachineLearning.ModelMonitor.Common.Core;

public static class IEnumerableExtensions
{
    public static string ToCommaSeparatedString<T>(this IEnumerable<T> enumerable, bool addSurroundingSquareBrackets = true)
    {
        ArgumentNullException.ThrowIfNull(enumerable, nameof(enumerable));
        return addSurroundingSquareBrackets ? string.Concat("[", string.Join(",", enumerable), "]") : string.Join(",", enumerable);
    }
}
