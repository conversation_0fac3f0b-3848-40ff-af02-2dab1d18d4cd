﻿// <copyright file="ValidationUtility.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

namespace Microsoft.MachineLearning.ModelMonitor.Common.Core;

public static class ValidationUtility
{
    public static void SignalArgumentNotNull(object obj, string signalName, string propertyName)
    {
        if (obj == null)
        {
            throw new MissingArgument(signalName, propertyName).ToException();
        }
    }

    public static void ArgumentNotNull(object obj, string propertyName)
    {
        if (obj == null)
        {
            throw new MissingArgument(string.Empty, propertyName).ToException();
        }
    }

    public static void TimespanNotDays(TimeSpan timeSpan, string signalName, string propertyName)
    {
        SignalArgumentNotNull(timeSpan, signalName, propertyName);

        if (timeSpan.Hours > 0 || timeSpan.Minutes > 0 || timeSpan.Seconds > 0 || timeSpan.Milliseconds > 0)
        {
            throw new LookbackInvalidArgument(signalName, propertyName).ToException();
        }
    }

    public static void InputDataUriFormat(string uri, string signalName, string propertyName)
    {
        SignalArgumentNotNull(uri, signalName, propertyName);
        if (!Uri.IsWellFormedUriString(uri, UriKind.RelativeOrAbsolute))
        {
            throw new InputDataUriFormatInvalid(uri, signalName).ToException();
        }
        // TODO: add valid uri format validation for input data
        // The following two format is valid:
        // 1. azureml:{datasetName}:{version}
        // 2. /subscriptions/{subId}/resourceGroups/{rgName}/providers/Microsoft.MachineLearningServices
        // /workspaces/{wsName}/data/{datasetName}/versions/{version}"
        // 3. Need to investigate more formats.
    }
}
