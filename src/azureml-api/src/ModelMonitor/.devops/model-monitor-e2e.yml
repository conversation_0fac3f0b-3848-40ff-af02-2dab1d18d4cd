trigger:
  batch: true
  branches:
    include:
    - refs/heads/master

pool:
  vmImage: ubuntu-latest
  demands:
  - sh
  - npm

variables:
  buildPlatform: 'Any CPU'
  buildConfiguration: 'Release'
  ModelMonitorRootDir: '$(Build.SourcesDirectory)/src/azureml-api/src/ModelMonitor'
  appName: 'model-monitor'
  artifactPath: 'src/azureml-api/build'
  isMasterBranch: $[eq(variables['Build.SourceBranch'], 'refs/heads/master')]
  IntegrationTestProject: '$(ModelMonitorRootDir)/Tests/Integration/Integration.Tests.csproj'
  OwningTeam: 'Model Monitor'
  CodeQL.TSAEnabled: true
jobs:
  - job: BuildDrop
    displayName: 'Build Drop'
    timeoutInMinutes: 120
    pool:
      vmImage: "ubuntu-latest"
    steps:
    - checkout: self
      clean: true
      fetchDepth: 1 # Fetch only one commit
      fetchTags: false

    - task: UsePythonVersion@0
      displayName: 'Use Python 3.9'
      inputs:
        versionSpec: 3.9

    - task: UseDotNet@2
      displayName: 'Use .NET sdk'
      inputs:
        useGlobalJson: true
        performMultiLevelLookup: true

    - task: UseDotNet@2
      displayName: 'Use .NET 8.0 sdk'
      inputs:
        packageType: sdk
        version: 8.0.x

    - task: NuGetAuthenticate@1
      displayName: NuGet Authenticate

    - task: PipAuthenticate@1
      displayName: Use Azure Artifacts feed
      inputs:
        artifactFeeds: 'Vienna'

    - task: DotNetCoreCLI@2
      displayName: 'dotnet restore'
      inputs:
        command: restore
        projects: '$(ModelMonitorRootDir)/dirs.proj'
        feedsToUse: config
        nugetConfigPath: src/NuGet.Config
        verbosityRestore: Minimal
        externalFeedCredentials: 'Skype, Bing'

    - task: AzureCLI@1
      displayName: 'dotnet publish'
      inputs:
        azureSubscription: 'INFRA Viennadroptest connection'
        scriptLocation: inlineScript
        inlineScript: |
          az acr login -n viennadroptest
          dotnet publish $(ModelMonitorRootDir)/dirs.proj -c $(buildConfiguration) --no-restore -v m

    - task: AzureCLI@1
      displayName: 'Publish Drop'
      inputs:
        azureSubscription: 'INFRA Viennadroptest connection'
        scriptPath: 'src/azureml-api/scripts/task.sh'
        arguments: 'full_drop --env int-transient --verbose  --app $(appName)'

    - task: AzureCLI@1
      displayName: 'dotnet build integration tests'
      inputs:
        azureSubscription: 'INFRA Viennadroptest connection'
        scriptLocation: inlineScript
        inlineScript: |
          dotnet build $(IntegrationTestProject) -c $(buildConfiguration) --no-restore -v m

    - task: PublishBuildArtifacts@1
      displayName: 'Publish Artifact: drop'
      inputs:
        PathtoPublish: '$(artifactPath)'

    - task: PublishBuildArtifacts@1
      displayName: 'Publish Artifact: integration tests'
      inputs:
        PathtoPublish: '$(ModelMonitorRootDir)/Tests/Integration/bin/Release/net8.0/'
        ArtifactName: 'tests'

    - task: ComponentGovernanceComponentDetection@0
      displayName: Component Detection
      continueOnError: True
      inputs:
        governanceProduct: c0454004-bca7-e811-bce7-00155d7fb5a6
        detectorsToRun: Maven,NuGet,Npm
        sourceScanPath: src/azureml-api/src/ModelMonitor

  - job: DeployTransient
    displayName: Deploy to int-transient
    timeoutInMinutes: 120
    condition: and(succeeded(), eq(variables.isMasterBranch, false))
    dependsOn: BuildDrop
    pool:
      vmImage: 'windows-latest'
    steps:
    - checkout: none
    - task: DownloadPipelineArtifact@2
      displayName: 'Download drop'
      inputs:
        buildType: 'current'
        artifactName: drop
        itemPattern: '**'
        downloadPath: $(artifactPath)
    - task: ExpressV2Internal@1
      displayName: 'Deploy Artifact'
      inputs:
        UseServerMonitorTask: false
        EnableStrictValidation: true
        ValidateOnly: false
        EndpointProviderType: 'Ev2Endpoint'
        ConnectedServiceName: 'Ev2 Infra INT environment deployments'
        ServiceRootLocation: 'LinkedArtifact'
        RolloutSpecType: 'RSPath'
        ServiceRootPath: '$(artifactPath)'
        RolloutSpecPath: '$(artifactPath)/msftkube_shell/int-transient/RolloutSpec.json'
        InlineDynamicBindingOverrides: |
          {
            "$schema": "https://ev2schema.azure.net/schemas/2020-01-01/scopeBindings.json",
            "scopeBindings": [
              {
                "scopeTagName": "msftkube",
                "bindings": [
                  {
                    "find": "__MSFTKUBECMD__",
                    "replaceWith": "--verbose release --env int-transient --app $(appName)"
                  }
                ]
              }
            ]
          }
        OutputRolloutId: 'ROLLOUT_ID'
        OutputServiceGroupName: 'ROLLOUT_SERVICEGROUP_NAME'
        OutputRolloutStatus: 'ROLLOUT_STATUS'

  - job: RunIntegrationTests
    displayName: 'Run Integration Tests'
    timeoutInMinutes: 120
    condition: and(succeeded(), eq(variables.isMasterBranch, false))
    dependsOn: DeployTransient
    pool:
      vmImage: "ubuntu-latest"
    steps:
    - checkout: none

    - task: UsePythonVersion@0
      displayName: 'Use Python 3.9'
      inputs:
        versionSpec: 3.9

    - task: UseDotNet@2
      inputs:
        packageType: 'sdk'
        version: 8.0.x

    - task: NuGetAuthenticate@1
      displayName: NuGet Authenticate

    - task: DownloadPipelineArtifact@2
      displayName: 'Download drop'
      inputs:
        buildType: 'current'
        artifactName: tests
        itemPattern: '**'
        downloadPath: $(artifactPath)/tests

    - task: PythonScript@0
      displayName: 'Set transient endpoint to environment variable'
      inputs:
        scriptPath: '$(artifactPath)/tests/.devops/write-vienna-cluster-uri.py'
        arguments: '$(Build.SourceBranch)'

    - task: CmdLine@2
      displayName: 'Ping int-transient environment - Model Monitor Service'
      inputs:
        script: 'curl $(VIENNA_CLUSTER_URI)model-monitor/v1.0/meta/version --verbose'

    - task: AzureCLI@1
      displayName: 'dotnet test'
      inputs:
        azureSubscription: 'AML - Inferencing R&D Autotest Int(4bf6b28a-452b-4af4-8080-8a196ee0ca4b)'
        scriptLocation: inlineScript
        inlineScript: |
          dotnet test $(artifactPath)/tests/Microsoft.MachineLearning.ModelMonitor.Integration.Tests.dll --no-build --no-restore --logger:trx --results-directory "$(Agent.TempDirectory)"
      env:
        TestHostConfiguration__ExperimentationHostUri: 'https://int.api.azureml-test.ms'
        TestHostConfiguration__ModelMonitorServiceHostUri: $(VIENNA_CLUSTER_URI)

    - task: PublishTestResults@2
      displayName: '[Test] Publish test results'
      condition: succeededOrFailed()
      inputs:
        testRunner: VSTest
        testResultsFiles: '**/*.trx'
        searchFolder: $(Agent.TempDirectory)