import sys

def parseFullBranchName_test():
    assert parseFullBranchName('refs/heads/users/erdagena/TsTests') == 'users-erdagena-tstests.transient.int.api.azureml-test.ms'
    assert parseFullBranchName('refs/heads/users/erdagena/TsTests_2') == 'users-erdagena-tstests-2.transient.int.api.azureml-test.ms'
    assert parseFullBranchName('refs/pull/276011/merge') == 'pull-276011.transient.int.api.azureml-test.ms'
    assert parseFullBranchName('refs/heads/master') == 'https://int.api.azureml-test.ms'

def parseFullBranchName(fullBranchName):
    # 3 options
    # 1. int (for running E2E test pointing to master environment): 
    #       refs/heads/master -> int.experiments.azureml-test.net
    # 2. manual (for running E2E test on transient environment from user branch):
    #       refs/heads/users/erdagena/TsTests -> users-erdagena-tstests.transient.experiments.azureml-test.net
    # 3. from a PR (for running E2E test on transient environment as a PR Gate):
    #       refs/pull/276011/merge -> pull-276011.transient.experiments.azureml-test.net
    intermediateBranchName = ""
    if fullBranchName == 'refs/heads/master':
        # option 1
        return 'int.api.azureml-test.ms'
    elif 'refs/heads/' in fullBranchName:
        # option 2
        intermediateBranchName = fullBranchName.split('refs/heads/')[1]
    else:
        # option 3
        intermediateBranchName = (fullBranchName.split('refs/')[1]).split('/merge')[0]
    out = '-'.join(intermediateBranchName.split('/',)).lower().replace("_","-") + '.transient.int.api.azureml-test.ms'
    return out


if __name__ == "__main__":
    fullBranchName = sys.argv[1]
    url = parseFullBranchName(fullBranchName)
    # sets an output var in pipeline
    print("https://" + url + "/")
    print("##vso[task.setvariable variable=VIENNA_CLUSTER_URI]" + "https://" + url + "/")