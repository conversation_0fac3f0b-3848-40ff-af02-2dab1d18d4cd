trigger: none
pr: none
schedules:
- cron: 0 0 * * *
  branches:
    include:
    - refs/heads/master
  always: true
- cron: 0 8 * * *
  branches:
    include:
    - refs/heads/master
  always: true
- cron: 0 16 * * *
  branches:
    include:
    - refs/heads/master
  always: true
variables:
  Codeql.TSAEnabled:  true
stages:
- stage: __default
  jobs:
  - job: Job_1
    displayName: Agent job 1
    timeoutInMinutes: 120
    cancelTimeoutInMinutes: 1
    pool:
      name: Vienna-1ES-DataCompute-Prep-Cache-Windows
    steps:
    - task: 6d15af64-176c-496d-b583-fd2ae21d4df4@1
      inputs:
        repository: self
        clean: true
        fetchDepth: 1
    - task: PipAuthenticate@1
      inputs:
        artifactFeeds: 'Vienna/Vienna_PublicPackages'
      displayName: Pip Authenticate
    - task: UsePythonVersion@0
      inputs:
        versionSpec: '3.9'
        addToPath: true
        architecture: 'x64'
    - task: AzureCLI@2
      displayName: Install Packages
      inputs:
        azureSubscription: 'AML - Experiences - Build Connection'
        scriptType: 'ps'
        scriptLocation: 'inlineScript'
        inlineScript: 'pip install -r ./requirements.txt'
        workingDirectory: 'src\azureml-api\src\ModelMonitor\E2E.Tests\ModelMonitoring'
    - task: PowerShell@2
      displayName: Pin azure-cli
      enabled: False
      inputs:
        targetType: inline
        script: >-
          #Pin az cli

          python -m pip install azure-cli<2.54.0
    - task: AzureCLI@2
      displayName: Run Integration Test
      env:
        SYSTEM_ACCESSTOKEN: $(System.AccessToken)
      inputs:
        azureSubscription: 'AML - Experiences - Build Connection'
        scriptType: 'ps'
        scriptLocation: 'inlineScript'
        inlineScript: |
          $env:TENANT_ID=$env:tenantId
          $env:AZURE_TENANT_ID=$env:tenantId
          $env:AZURE_CLIENT_ID=$env:servicePrincipalId
          $env:AZURE_CLIENT_SECRET=$env:servicePrincipalKey
          python -m unittest -v momo_e2e_job_script.ModelMonitoringFullSignalTest.test_parallel
        addSpnToEnvironment: true
        workingDirectory: 'src\azureml-api\src\ModelMonitor\E2E.Tests\ModelMonitoring'
    

