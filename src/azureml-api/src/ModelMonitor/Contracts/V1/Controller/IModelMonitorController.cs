﻿// <copyright file="IModelMonitorController.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Microsoft.MachineLearning.Common.WebApi.Client;
using Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts;
using Microsoft.MachineLearning.ModelMonitor.Contracts.Configuration;

namespace Microsoft.MachineLearning.ModelMonitor.Contracts.V1;

[InterfaceRoutePrefix(ApiConstants.ModelMonitorRoute)]
public interface IModelMonitorController
{
    [HttpPost(ApiConstants.ModelMonitorNameParam)]
    Task<ModelMonitorResponse> Create(
        Guid subscriptionId,
        string resourceGroupName,
        string workspaceName,
        string modelMonitorName,
        [FromBody] CreateModelMonitorRequest request,
        CancellationToken cancellationToken = default);

    [HttpPost($"{ApiConstants.ModelMonitorNameParam}/{ApiConstants.CreateMonitor}")]
    Task<PipelineJob> CreateMonitor(
        Guid subscriptionId,
        string resourceGroupName,
        string workspaceName,
        string modelMonitorName,
        [FromBody] CreateModelMonitorRequest request,
        CancellationToken cancellationToken = default);


    [HttpPost($"{ApiConstants.ModelMonitorNameParam}/{ApiConstants.SubmitJob}")]
    Task<string> SubmitJob(Guid subscriptionId, string resourceGroupName, string workspaceName,
        string modelMonitorName, [FromBody] SubmitModelMonitoringJobRequest submitMomoJobRequest,
        CancellationToken cancellationToken = default);
}
