﻿// <copyright file="AssetToSyndicate.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;

namespace Microsoft.MachineLearning.Common.Core.Syndication
{
    public enum AssetSyndicationAction
    {
        Create,
        Update,
        Delete,
        Archive
    }

    public class AssetToSyndicate
    {
        public AssetId AssetId { get; set; }

        public DateTimeOffset? SourceAssetCreationTime { get; set; } = null;

        public AssetSyndicationAction Action { get; set; }

        public Guid TenantId { get; set; }

        public override string ToString()
        {
            if (SourceAssetCreationTime != null)
            {
                return $"[AssetId: {AssetId}, TenantId: {TenantId}, Action: {Action}, SourceAssetCreationTime: {SourceAssetCreationTime}]";
            }
            else
            {
                return $"[AssetId: {AssetId}, TenantId: {TenantId}, Action: {Action}]";
            }
        }
    }
}
