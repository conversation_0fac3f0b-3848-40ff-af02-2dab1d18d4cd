﻿// <copyright file="SetExtensions.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

namespace System.Collections.Generic
{
    public static class SetExtensions
    {
        public static void AddRange<TValue>(
            this ISet<TValue> set,
            IEnumerable<TValue> items)
        {
            foreach (var item in items)
            {
                set.Add(item);
            }
        }
    }
}
