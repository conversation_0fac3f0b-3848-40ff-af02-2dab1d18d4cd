# 1ES Inventory configuration file:
# These files control automatic routing of bot-created issues; if there is an error, please make a PR with corrections
# See https://eng.ms/docs/cloud-ai-platform/ai-platform/ai-platform-ml-platform/project-vienna-services/azure-machine-learning-runbook/operational/common/tsg/inventoryyamlfiles for more information.
#     For any incorrect information in the AML runbook, please update the page as needed.
# See https://aka.ms/inventory-as-code for examples, reference, and how-to guides.

schemaVersion: 0.0.1

# Classification isProduction - Declares whether the code is production or non-production.
# isProduction is required in the root Inventory file (e.g. '/' in the repo). It is optional in subdirectories.
isProduction: true

# Every 1es-inventory.yml file must contain both an 'accountableOwners' and 'routing' section
accountableOwners:
  # Service ID from the Service Tree. d3e80ba4-b9e8-46d7-9d99-8aba603120e5 = 'AzureML - Models'
  service: d3e80ba4-b9e8-46d7-9d99-8aba603120e5

routing:
  # Azure DevOps area path - please update at the greatest-depth Inventory.yml file if incorrect for the current path.
  defaultAreaPath:
    org: msdata
    path: Vienna\AOH
