﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <AssemblyName>Microsoft.MachineLearning.ModelMesh.Shared.Common</AssemblyName>
    <RootNamespace>Microsoft.MachineLearning.ModelMesh.Shared.Common</RootNamespace>
    <PackageId>Microsoft.MachineLearning.ModelMesh.Shared.Common</PackageId>
    <Product>Microsoft.MachineLearning.ModelMesh.Shared.Common</Product>
    <IsPublishable>false</IsPublishable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Hosting" />
    <PackageReference Include="Microsoft.Azure.StackExchangeRedis" />
    <PackageReference Update="Microsoft.IdentityModel.JsonWebTokens" VersionOverriden="8.3.1" />
    <PackageReference Include="RedLock.net" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="$(SrcRoot)\Common\Core\Common.Core.csproj" />
    <ProjectReference Include="$(SrcRoot)\Common\WebApi.Authorization\Common.WebApi.Authorization.csproj" />
  </ItemGroup>

  <PropertyGroup>
    <Nullable>enable</Nullable>
    <LangVersion>latest</LangVersion>
  </PropertyGroup>
</Project>