﻿// <copyright file="LeaderElection.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Threading.Tasks;
using System.Xml;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.MachineLearning.ModelMesh.Shared.Common.Redis;
using RedLockNet;
using RedLockNet.SERedis;


namespace Microsoft.MachineLearning.ModelMesh.Shared.Common.LeaderElection
{
    public class LeaderElection(
        IOptionsMonitor<RedisLeaderElectionConfiguration> leaderElectionConfig,
        ILoggerFactory loggerFactory,
        IRedisConnectionFactory redisConnectionFactory) : ILeaderElection
    {
        protected readonly ILogger _logger = loggerFactory.CreateLogger<LeaderElection>();

        private readonly TimeSpan _leaseDuration = XmlConvert.ToTimeSpan(leaderElectionConfig.CurrentValue.LeaseDuration);

        private RedLockFactory _redLockFactory = RedLockFactory.Create(
            [
                redisConnectionFactory.GetConnectionMultiplexerAsync(leaderElectionConfig.CurrentValue.HostName).GetAwaiter().GetResult()
            ]);
        
        private IRedLock? _leaderLease = null;

        public async Task<bool> IsLeaderAsync()
        {
            if (_leaderLease != null)
            {
                if (_leaderLease.IsAcquired)
                {
                    _logger.LogInformation("This instance is currently already the leader.");
                    return true;
                }
                else
                {
                    _logger.LogInformation("This instance appears to have been the leader, isn't any longer. Weird. (This log message should never happen unless redlock failed to renew the lease)");
                    _leaderLease.Dispose();
                    _leaderLease = null;
                }
            }

            _logger.LogInformation("This instance isn't currently the leader. Attempting to acquire a lease...");

            // Try to acquire a lease. If the lease is acquired, redlock will auto-renew the lease until it is disposed.
            var lease = await _redLockFactory
                .CreateLockAsync(leaderElectionConfig.CurrentValue.LeaseKeyName, _leaseDuration)
                .ConfigureAwait(false);

            if (lease.IsAcquired)
            {
                _logger.LogInformation("This instance has successfully acquired a lease, and is the new leader.");
                _leaderLease = lease;
                return true;
            }
            else
            {
                _logger.LogInformation("This instance has failed to acquire a lease, and is not the leader.");
                lease.Dispose();
                return false;
            }
        }

        public Task ReleaseLeadershipAsync()
        {
            _leaderLease?.Dispose();
            _leaderLease = null;

            return Task.CompletedTask;
        }
    }
}
