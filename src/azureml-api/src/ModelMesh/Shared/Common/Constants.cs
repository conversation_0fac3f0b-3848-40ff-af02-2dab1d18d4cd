﻿namespace Microsoft.MachineLearning.ModelMesh.Shared.Common
{
    public static class Constants
    {
        // Configuration section names
        public const string TokenValidationSection = "TokenValidation";
        public const string TestAadAppSection = "TestAadApp";
        public const string ArmAadSettingsSection = "ArmAadSettings";

        // constant for Demand telementry redis stream key
        public const string DemandTelemetryRedisKey = "ingress-distributor-metrics";

        public const string DemandTelemetryRequestInitiatedKey = "RequestInitiated";

        public const string DemandTelemetryRequestCompletedKey = "RequestCompleted";

        // constant for Load telementry redis stream key
        public const string LoadTelemetryRedisKey = "telemetry_stream.load";

        // constant for reconciler to run the loop every 2.5 minutes.
        public const int DefaultDelayDurationInSeconds = 150;
    }
}
