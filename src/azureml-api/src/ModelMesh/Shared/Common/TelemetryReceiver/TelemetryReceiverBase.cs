﻿using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace Microsoft.MachineLearning.ModelMesh.Shared.Common.TelemetryReceiver
{
    public abstract class TelemetryReceiverBase : BackgroundService
    {
        private readonly ILogger _logger;

        public TelemetryReceiverBase(
            ILoggerFactory loggerFactory)
        {
            _ = loggerFactory ?? throw new ArgumentNullException(nameof(loggerFactory));
            _logger = loggerFactory.CreateLogger(GetType());
        }

        /*
         * Concrete telemetry receiver needs to override this method, 
         * where the receiver grabs a telemetry packet from its affiliated db (i.e. queue, stream, etc.)
         * and passes it to the handler.
         */
        protected abstract Task ContinuouslyHandleFleetTelemetryAsync(CancellationToken stoppingToken);

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            try
            {
                while (!stoppingToken.IsCancellationRequested)
                {
                    try
                    {
                        await ContinuouslyHandleFleetTelemetryAsync(stoppingToken).ConfigureAwait(false);
                    }
                    catch (Exception ex) when (ex is not OperationCanceledException)
                    {
                        _logger.LogError(ex, "Encountered an exception when receiving telemetry.");
                    }
                }
            }
            catch (OperationCanceledException)
            {
                _logger.LogInformation("ContinuouslyHandleFleetTelemetryAsync is stopping...");
            }
        }

        public override async Task StartAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation($"{this.GetType().Name} is starting...");

            await base.StartAsync(cancellationToken);
        }

        public override async Task StopAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation($"{this.GetType().Name} is stopping...");

            await base.StopAsync(stoppingToken);
        }
    }
}
