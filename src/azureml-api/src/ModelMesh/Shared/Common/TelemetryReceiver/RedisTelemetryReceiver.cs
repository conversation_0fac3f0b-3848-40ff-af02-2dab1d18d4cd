﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.Diagnostics;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.MachineLearning.ModelMesh.Shared.Common.Redis;

namespace Microsoft.MachineLearning.ModelMesh.Shared.Common.TelemetryReceiver
{
    public class RedisTelemetryReceiver<TRedisMessage> : TelemetryReceiverBase where TRedisMessage : IRedisMessage, new()
    {
        private readonly ILogger _logger;
        private readonly IEnumerable<RedisQueueConsumerService<TRedisMessage>> _redisQueueConsumerServices;

        protected TimeSpan MinimumIterationDuration = TimeSpan.FromSeconds(2);

        public RedisTelemetryReceiver(
            ILoggerFactory loggerFactory,
            IRedisMessageHandler<TRedisMessage> messageHandler,
            IList<RedisQueueConsumerConfiguration> redisQueueConsumerConfigurations,
            IRedisConnectionFactory redisConnectionFactory)
            : base(loggerFactory)
        {
            _ = loggerFactory ?? throw new ArgumentNullException(nameof(loggerFactory));
            _logger = loggerFactory.CreateLogger(GetType());
            _redisQueueConsumerServices = redisQueueConsumerConfigurations
                .Select(configuration => new RedisQueueConsumerService<TRedisMessage>(loggerFactory, configuration, messageHandler, redisConnectionFactory));
        }

        protected override async Task ContinuouslyHandleFleetTelemetryAsync(CancellationToken stoppingToken)
        {
            await Task.WhenAll(_redisQueueConsumerServices.Select(redisStream => ProcessTelemetryAsync(redisStream, stoppingToken)))
                .ConfigureAwait(false);
        }

        public override async Task StartAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation($"{this.GetType().Name} is starting...");
            // Create consumer group if it does not exist.
            await Task.WhenAll(_redisQueueConsumerServices.Select(redisStream => redisStream.CreateConsumerGroupAsync()))
                .ConfigureAwait(false);
            await base.StartAsync(cancellationToken);
        }

        private async Task ProcessTelemetryAsync(RedisQueueConsumerService<TRedisMessage> redisStream, CancellationToken stoppingToken)
        {
            try
            {
                do
                {
                    var stopwatch = Stopwatch.StartNew();
                    try
                    {
                        await redisStream.ConsumeStreamAsync().ConfigureAwait(false);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error processing telemetry message");
                    }
                    finally
                    {
                        // Run once every `MinimumIterationDuration`, or flat out if each iteration takes longer than that.
                        var deadTime = MinimumIterationDuration - stopwatch.Elapsed;

                        if (deadTime > TimeSpan.Zero)
                        {
                            await Task.Delay(deadTime, stoppingToken)
                                .ConfigureAwait(false);
                        }
                    }
                }
                while (true);
            }
            catch (OperationCanceledException)
            {
                _logger.LogInformation("RedisTelemetryReceiver is stopping.");
            }
        }
    }
}
