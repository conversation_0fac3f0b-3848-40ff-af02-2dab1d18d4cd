﻿// <copyright file="RedisUtils.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Linq;
using StackExchange.Redis;
using System.Collections.Generic;

namespace Microsoft.MachineLearning.ModelMesh.Shared.Common.Redis
{
    public static class RedisUtils
    {
        public static Dictionary<string, string> GetDictionaryEntryValues(StreamEntry entry)
        {
            // Convert entry.Values to jsonstring
            var valueDict = entry.Values.ToDictionary(
                nameValue => nameValue.Name.ToString(),
                nameValue => nameValue.Value.ToString()
            );
            return valueDict;
        }
    }
}
