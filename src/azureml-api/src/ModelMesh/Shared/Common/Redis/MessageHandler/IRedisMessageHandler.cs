﻿using System.Threading.Tasks;
using StackExchange.Redis;

namespace Microsoft.MachineLearning.ModelMesh.Shared.Common.Redis
{
    public interface IRedisMessageHandler<TMessage> where TMessage : IRedisMessage
    {
        /// <summary>
        /// Handles the incoming Redis stream entries. Returns the IDs of successfully processed entries.
        /// </summary>
        public Task<RedisValue[]> HandleAsync(StreamEntry[] entries);
    }
}
