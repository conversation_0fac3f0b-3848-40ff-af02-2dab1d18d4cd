﻿// <copyright file="RedisLeaderElectionConfiguration.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

namespace Microsoft.MachineLearning.ModelMesh.Shared.Common.Redis
{
    public class RedisLeaderElectionConfiguration
    {
        public string ConnectionString { get; set; } = string.Empty;

        public string HostName { get; set; } = string.Empty;

        public string LeaseKeyName { get; set; } = string.Empty;

        public string LeaseDuration { get; set; } = string.Empty;
    }
}
