﻿// <copyright file="RedisConnectionFactory.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Concurrent;
using System.Threading.Tasks;
using Azure.Core;
using Azure.Identity;
using Microsoft.AIPlatform.WorkloadIdentity;
using Microsoft.Extensions.Logging;
using Microsoft.MachineLearning.Common.Core;
using StackExchange.Redis;

namespace Microsoft.MachineLearning.ModelMesh.Shared.Common.Redis
{
    public interface IRedisConnectionFactory
    {
        public Task<ConnectionMultiplexer> GetConnectionMultiplexerAsync(string hostName);

        public Task<IDatabase> GetDatabaseAsync(string hostName);
    }

    public class RedisConnectionFactory : IRedisConnectionFactory
    {
        private readonly IWorkloadIdentityProvider _workloadIdentity;
        private readonly ILogger _logger;

        private readonly ConcurrentDictionary<string, Task<ConnectionMultiplexer>> _cachedConnectionMuxers = new();

        public RedisConnectionFactory(ILoggerFactory loggerFactory,
                        IWorkloadIdentityProvider workloadIdentity)
        {
            _logger = Guard.ArgumentNotNull(loggerFactory, nameof(loggerFactory)).CreateLogger(GetType());
            _workloadIdentity = Guard.ArgumentNotNull(workloadIdentity, nameof(workloadIdentity));
        }

        public Task<ConnectionMultiplexer> GetConnectionMultiplexerAsync(string hostName)
        {
            return _cachedConnectionMuxers
                .GetOrAdd(hostName, async _ =>
                {
                    TokenCredential tokenCredential;
                    if (_workloadIdentity.IsAvailable)
                    {
                        _logger.LogInformation($"WorkloadIdenity is available for Redis: {hostName}");
                        tokenCredential = _workloadIdentity.CreateCredential();
                    }
                    else
                    {
                        _logger.LogInformation($"WorkloadIdenity is not available for Redis: {hostName}. If it is a dev env, we can try to using user's default credential:");
                        tokenCredential = new DefaultAzureCredential();
                    }

                    var configurationOptions = new ConfigurationOptions();
                    try
                    {
                        configurationOptions = await ConfigurationOptions
                            .Parse(hostName)
                            .ConfigureForAzureWithTokenCredentialAsync(tokenCredential);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError($"Failed to config the configurationOptions for redis {hostName}: {ex}");
                        throw;
                    }

                    var connectionMultiplexer = ConnectionMultiplexer.Connect(configurationOptions);
                    return connectionMultiplexer;
                });
        }

        public async Task<IDatabase> GetDatabaseAsync(string hostName)
        {
            var muxer = await GetConnectionMultiplexerAsync(hostName);
            return muxer.GetDatabase();
        }
    }
}
