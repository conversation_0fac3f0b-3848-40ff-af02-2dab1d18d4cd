﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.MachineLearning.Common.Core;
using StackExchange.Redis;

namespace Microsoft.MachineLearning.ModelMesh.Shared.Common.Redis
{
    public interface IRedisQueueConsumerService<TMessage>
    {
        public Task CreateConsumerGroupAsync();

        public Task ConsumeStreamAsync();

    }

    public class RedisQueueConsumerService<TMessage> : IRedisQueueConsumerService<TMessage> where TMessage : IRedisMessage
    {
        private readonly ILogger _logger;
        private readonly RedisKey _redisKey;
        private readonly RedisQueueConsumerConfiguration _consumerConfiguration;
        private readonly IRedisMessageHandler<TMessage> _messageHandler;
        private readonly IRedisConnectionFactory _redisConnectionFactory;


        public RedisQueueConsumerService(
            ILoggerFactory loggerFactory,
            RedisQueueConsumerConfiguration redisQueueConsumerConfiguration,
            IRedisMessageHandler<TMessage> messageHandler,
            IRedisConnectionFactory redisConnectionFactory)
        {
            _logger = Guard.ArgumentNotNull(loggerFactory, nameof(loggerFactory)).CreateLogger(GetType());
            _messageHandler = Guard.ArgumentNotNull(messageHandler, nameof(messageHandler));
            _consumerConfiguration = Guard.ArgumentNotNull(redisQueueConsumerConfiguration, nameof(redisQueueConsumerConfiguration));
            _redisKey = _consumerConfiguration.RedisKey;
            _redisConnectionFactory = Guard.ArgumentNotNull(redisConnectionFactory, nameof(redisConnectionFactory));
        }

        public async Task CreateConsumerGroupAsync()
        {
            try
            {
                var database = await _redisConnectionFactory
                    .GetDatabaseAsync(_consumerConfiguration.HostName)
                    .ConfigureAwait(false);
                if (database == null)
                {
                    _logger.LogError($"redisDatase in {_consumerConfiguration.HostName} is null");
                    return;
                }
                // We only need to create the consumer group per each redis stream per each redis key once
                await database.StreamCreateConsumerGroupAsync(_redisKey, _consumerConfiguration.ConsumerGroupName).ConfigureAwait(false);
            }
            catch (RedisServerException ex)
            {
                if (ex.Message.Contains("BUSYGROUP Consumer Group name already exists"))
                {
                    _logger.LogInformation($"In redis instance {_consumerConfiguration.HostName} and the queue: {_redisKey}, the consumer group {_consumerConfiguration.ConsumerGroupName} already exists, cannot create a new one.");
                    return;
                }
                else
                {
                    throw;
                }
            }
        }


        public async Task ConsumeStreamAsync()
        {
            var database = await _redisConnectionFactory
                .GetDatabaseAsync(_consumerConfiguration.HostName)
                .ConfigureAwait(false);
            if (database == null)
            {
                _logger.LogError($"redisDatase in {_consumerConfiguration.HostName} is null.");
                return;
            }
            _logger.LogInformation($"Get redisDatabase {database} in ConsumeStreamAsync for {typeof(TMessage)}");
            var stopwatch = Stopwatch.StartNew();
            var entries = await database
                .StreamReadGroupAsync(_redisKey, _consumerConfiguration.ConsumerGroupName, RandomConsumer(), ">", _consumerConfiguration.BatchProcessMessageCount)
                .ConfigureAwait(false);
            _logger.LogInformation($"StreamReadGroupAsync successful for {typeof(TMessage)}");
            if (entries.Length > 0)
            {
                try
                {
                    await _messageHandler
                        .HandleAsync(entries)
                        .ConfigureAwait(false);
                }
                finally
                {
                    await database.StreamAcknowledgeAsync(_redisKey, _consumerConfiguration.ConsumerGroupName, [.. entries.Select(entry => entry.Id)]);
                }
            }

            _logger.LogInformation($"Consumed {entries.Length} message(s) from redisKey: {_redisKey} in consumer group {_consumerConfiguration.ConsumerGroupName} in {stopwatch.ElapsedMilliseconds} ms");
        }

        private string RandomConsumer()
        {
            List<string> names = _consumerConfiguration.GetConsumerNames();

            return names.ElementAt(new Random().Next(0, names.Count));
        }

    }
}