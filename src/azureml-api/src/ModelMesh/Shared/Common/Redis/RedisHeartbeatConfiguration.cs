﻿// <copyright file="RedisHeartbeatConfiguration.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

namespace Microsoft.MachineLearning.ModelMesh.Shared.Common.Redis
{
    public class RedisHeartbeatConfiguration
    {
        public string ConnectionString { get; set; } = string.Empty;

        public int TtlInSeconds { get; set; } = 30;

        // The heartbeat refresh rate in milliseconds
        public int IntervalInMilliseconds { get; set; } = 10000;
    }
}
