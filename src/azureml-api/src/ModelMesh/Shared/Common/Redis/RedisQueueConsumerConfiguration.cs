﻿using System;
using System.Collections.Generic;
using System.Linq;


namespace Microsoft.MachineLearning.ModelMesh.Shared.Common.Redis
{
    public enum ConsumeMode
    {
        LIVE,
        CONTINUOUS,
    }

    public class GeoRedisRegionalQueueConfiguration
    {
        // key is the redis name, value is the correspoding RedisQueueConsumerConfiguration in the region
        public Dictionary<string, RedisQueueConsumerConfiguration> LoadTelemetryRegionalQueuesConfig =
            new Dictionary<string, RedisQueueConsumerConfiguration>();

        public Dictionary<string, RedisQueueConsumerConfiguration> DemandTelemetryRegionalQueuesConfig =
            new Dictionary<string, RedisQueueConsumerConfiguration>();
    }


    public class RedisQueueConsumerConfiguration
    {
        // a uniq string for a consumer group name suffix when the configuration is constructed
        private readonly string _consumerGroupNameSuffix = string.Empty;

        public int NumberOfConsumers { get; set; } = 1;

        public long MaxIdleTimeInMillisecond { get; set; } = 30000;

        public int MaxDeliveryCount { get; set; } = 5;

        public string RedisKey { get; set; } = string.Empty;

        public int BatchProcessMessageCount { get; set; } = 1;

        public string ConsumerGroupName => GenerateConsumerGroupName();

        public ConsumeMode ConsumingMode { get; set; }

        public string HostName { get; set; } = string.Empty;

        public RedisQueueConsumerConfiguration()
        {
            _consumerGroupNameSuffix = RandomString(5);
        }

        public List<string> GetConsumerNames()
        {
            // The consumer name is defined by the number of Consumers
            // {ConsumerGroupName}-{i}
            List<string> names = [];
            for (int i = 0; i < this.NumberOfConsumers; i++)
            {
                names.Add($"{ConsumerGroupName}-{i}");
            }
            return names;
        }

        private string GenerateConsumerGroupName()
            => ConsumingMode switch
            {
                // This mode we only get the data after the consumer group is created.
                // It is suitable for heartbeat check.
                ConsumeMode.LIVE => $"{RedisKey}.{HostName}.{_consumerGroupNameSuffix}",
                // This mode we keep the replica on each cluster to be 1.
                // Only one consumer group for each cluster.
                // It is for telemetry data consumer from redis.
                ConsumeMode.CONTINUOUS => $"{RedisKey}.{HostName}.{ClusterId()}",
                _ => throw new InvalidOperationException($"ConsumeMode: {ConsumingMode} is not supported."),
            };

        private string RandomString(int length)
        {
            Random random = new();
            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
            return new string(Enumerable.Repeat(chars, length)
                .Select(s => s[random.Next(s.Length)]).ToArray());
        }

        private string ClusterId()
        {
            var clusterNamespace = Environment.GetEnvironmentVariable("K8S_CLUSTER");
            if (!string.IsNullOrEmpty(clusterNamespace)) 
            {
                return clusterNamespace.Split("-").Last();
            }
            return string.Empty;
        }
    }
}