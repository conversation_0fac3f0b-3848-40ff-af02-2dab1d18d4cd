﻿namespace Microsoft.MachineLearning.ModelMesh.Shared.Common
{
    public class Model
    {
        public string Name { get; set; } = string.Empty;
        public long MaxTokensPerMI { get; set; } = 50000;
        public int BufferPTU { get; set; } = 2;
        public int BufferPaygo { get; set; } = 1;
        public int BufferBatch { get; set; } = 0;

        // Parameterless constructor needed for deserialization
        public Model() { }

        public Model(string name, long maxTokensPerMI = 50000, int bufferPTU = 2, int bufferPaygo = 1, int bufferBatch = 0)
        {
            Name = name;
            MaxTokensPerMI = maxTokensPerMI;
            BufferPTU = bufferPTU;
            BufferPaygo = bufferPaygo;
            BufferBatch = bufferBatch;
        }
    }
}
