﻿// <copyright file="ConfigurationRecord.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Microsoft.MachineLearning.ModelMesh.Shared.Common;

[JsonConverter(typeof(ConfigurationRecordConverter))]
public abstract record ConfigurationRecord;

public record LKG : ConfigurationRecord;

public record SpecificConfiguration(string name, string version) : ConfigurationRecord;

public class ConfigurationRecordConverter : JsonConverter<ConfigurationRecord>
{
    public override ConfigurationRecord ReadJson(JsonReader reader, Type objectType, ConfigurationRecord? existingValue, bool hasExistingValue, JsonSerializer serializer)
    {
        var jo = JObject.Load(reader);
        var type = jo["type"]?.ToString();

        return type switch
        {
            "LKG" => new LKG(),
            "SpecificConfiguration" => new SpecificConfiguration(
                jo["name"]?.ToString() ?? throw new JsonException("Missing 'name'"),
                jo["version"]?.ToString() ?? throw new JsonException("Missing 'version'")
            ),
            _ => throw new JsonException($"Unknown configuration type: {type}")
        };
    }

    public override void WriteJson(JsonWriter writer, ConfigurationRecord? value, JsonSerializer serializer)
    {
        writer.WriteStartObject();

        switch (value)
        {
            case LKG:
                writer.WritePropertyName("type");
                writer.WriteValue("LKG");
                break;

            case SpecificConfiguration sc:
                writer.WritePropertyName("type");
                writer.WriteValue("SpecificConfiguration");

                writer.WritePropertyName("name");
                writer.WriteValue(sc.name);

                writer.WritePropertyName("version");
                writer.WriteValue(sc.version);
                break;

            default:
                throw new JsonException($"Unknown configuration type: {value?.GetType().Name}");
        }

        writer.WriteEndObject();
    }

    public override bool CanRead => true;
    public override bool CanWrite => true;
}
