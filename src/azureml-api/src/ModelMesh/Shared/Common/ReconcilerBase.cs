﻿using System.Collections.Generic;
using System.Diagnostics;
using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.MachineLearning.ModelMesh.Shared.Common.LeaderElection;

namespace Microsoft.MachineLearning.ModelMesh.Shared.Common
{
    public abstract class ReconcilerBase : BackgroundService
    {
        protected ILogger _logger;
        protected ILeaderElection _leaderElection;
        protected int DelayDurationInSeconds { get; set; } = Constants.DefaultDelayDurationInSeconds;

        protected ReconcilerBase(
            ILoggerFactory loggerFactory,
            ILeaderElection leaderElection)
        {
            _logger = loggerFactory.CreateLogger<ReconcilerBase>();
            _leaderElection = leaderElection;
        }

        protected async override Task ExecuteAsync(CancellationToken cancellationToken)
        {
            await ContinuouslyReconcileAsync(cancellationToken).ConfigureAwait(false);
        }

        protected async Task ContinuouslyReconcileAsync(CancellationToken stoppingToken)
        {
            try
            {
                do
                {
                    var stopwatch = Stopwatch.StartNew();
                    try
                    {
                        var isLeader = await _leaderElection.IsLeaderAsync().ConfigureAwait(false);
                        if (isLeader)
                        {
                            await ExecuteReconcilerOnceAsync(stoppingToken)
                                .ConfigureAwait(false);

                            // TODO: make this a metric instead of a log.
                            _logger.LogInformationWithFields("Reconciliation iteration successfully completed.", new Dictionary<string, object>
                            {
                                { "IterationDuration", stopwatch.Elapsed }
                            });
                        }
                        else
                        {
                            _logger.LogInformation("Instance is not the leader, skipping reconciliation.");
                        }
                    }
                    catch (Exception ex) when (ex is not OperationCanceledException)
                    {
                        _logger.LogError(ex, "Encountered an exception during reconciling");
                    }
                    finally
                    {
                        // Run once every 10 seconds, or flat out if each iteration takes longer than that.
                        var deadTime = TimeSpan.FromSeconds(DelayDurationInSeconds) - stopwatch.Elapsed;

                        if (deadTime > TimeSpan.Zero)
                        {
                            await Task.Delay(deadTime, stoppingToken)
                                .ConfigureAwait(false);
                        }
                    }
                }
                while (true);
            }
            catch (OperationCanceledException)
            {
                _logger.LogInformation("Reconciler is stopping.");
            }
        }

        protected abstract Task ExecuteReconcilerOnceAsync(CancellationToken stoppingToken);
    }
}
