﻿
using System;
using Microsoft.MachineLearning.Common.Testing;
using Xunit;

namespace Microsoft.MachineLearning.ModelMesh.Shared.Common.Redis.UnitTests
{
    [Trait(nameof(TestCategory), TestCategory.Unit)]
    public class RedisQueueConsumerConfigurationTests
    {
        [Fact]
        public void ConsumeModeLiveTest()
        {
            // LIVE Mode, the consumgroup name is unique.
            RedisQueueConsumerConfiguration config_1 = new RedisQueueConsumerConfiguration
            {
                HostName = "TestRedisName",
                RedisKey = "TestKey",
                ConsumingMode = ConsumeMode.LIVE,
            };

            RedisQueueConsumerConfiguration config_2 = new RedisQueueConsumerConfiguration
            {
                HostName = "TestRedisName",
                RedisKey = "TestKey",
                ConsumingMode = ConsumeMode.LIVE,
            };

            Assert.Equal(ConsumeMode.LIVE, config_2.ConsumingMode);
            Assert.True(config_1?.ConsumerGroupName.StartsWith("TestKey.TestRedisName"));
            Assert.True(config_2?.ConsumerGroupName.StartsWith("TestKey.TestRedisName"));
            Assert.NotEqual(config_1?.ConsumerGroupName, config_2?.ConsumerGroupName);
        }

        [Fact]
        public void ConsumeModeContinuousTest()
        {
            // CONTINUOUS Mode, the consumgroup name is unique.
            RedisQueueConsumerConfiguration config = new RedisQueueConsumerConfiguration
            {
                HostName = "TestRedisName",
                RedisKey = "TestKey",
                ConsumingMode = ConsumeMode.CONTINUOUS,
            };
            Assert.Equal(ConsumeMode.CONTINUOUS, config.ConsumingMode);
            Assert.Equal("TestKey.TestRedisName.", config.ConsumerGroupName);

            Environment.SetEnvironmentVariable("K8S_CLUSTER", "region-01");
            config = new RedisQueueConsumerConfiguration
            {
                HostName = "TestRedisName",
                RedisKey = "TestKey",
                ConsumingMode = ConsumeMode.CONTINUOUS,
            };
            Assert.Equal(ConsumeMode.CONTINUOUS, config.ConsumingMode);
            Assert.Equal("TestKey.TestRedisName.01", config.ConsumerGroupName);
            // clean up 
            Environment.SetEnvironmentVariable("K8S_CLUSTER", null);
        }
    }
}
