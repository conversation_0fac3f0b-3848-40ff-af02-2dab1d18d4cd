﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <AssemblyName>Microsoft.MachineLearning.ModelMesh.Shared.Common.UnitTests</AssemblyName>
    <RootNamespace>Microsoft.MachineLearning.ModelMesh.Shared.Common.UnitTests</RootNamespace>
    <PackageId>Microsoft.MachineLearning.ModelMesh.Shared.Common.UnitTests</PackageId>
    <Product>Microsoft.MachineLearning.ModelMesh.Shared.Common.UnitTests</Product>
    <IsPublishable>false</IsPublishable>
  </PropertyGroup>

  <PropertyGroup>
    <Nullable>enable</Nullable>
    <LangVersion>latest</LangVersion>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.NET.Test.Sdk" />
    <PackageReference Include="xunit" />
    <PackageReference Include="xunit.runner.visualstudio" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="$(SrcRoot)\Common\Testing\Common.Testing.csproj" />
    <ProjectReference Include="$(SrcRoot)\ModelMesh\Shared\Common\Shared.Common.csproj" />
  </ItemGroup>
</Project>