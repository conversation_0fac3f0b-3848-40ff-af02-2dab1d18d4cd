﻿// <copyright file="ContainerValidator.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using k8s;
using k8s.Models;

namespace Microsoft.MachineLearning.ModelMesh.ModelHost.Services.Validators
{
    public static class SidecarContainerValidator
    {
        public static void ValidateSidecarContainerSpec(string containerSpec, string containerSpecName)
        {
            if (containerSpec.IsNullOrEmpty())
            {
                throw new ArgumentNullException($"{containerSpecName}'s {nameof(containerSpec)}");
            }

            V1Container parsedContainerSpec;
            try
            {
                parsedContainerSpec = KubernetesYaml.Deserialize<V1Container>(containerSpec);
            }
            catch (Exception ex)
            {
                throw new FormatException($"Failed to parse {containerSpecName}.", ex);
            }

            parsedContainerSpec.Validate();

            if (parsedContainerSpec.Image.IsNullOrEmpty())
            {
                throw new ArgumentNullException($"{containerSpecName}'s {nameof(containerSpec)}.Image");
            }

            if (parsedContainerSpec.Command == null)
            {
                throw new ArgumentNullException($"{containerSpecName}'s {nameof(containerSpec)}.Command");
            }

            if (parsedContainerSpec.StartupProbe == null)
            {
                throw new ArgumentNullException($"{containerSpecName}'s {nameof(containerSpec)}.StartupProbe"); 
            }

            if (parsedContainerSpec.LivenessProbe == null)
            {
                throw new ArgumentNullException($"{containerSpecName}'s {nameof(containerSpec)}.LivenessProbe");
            }

            if (parsedContainerSpec.ReadinessProbe == null)
            {
                throw new ArgumentNullException($"{containerSpecName}'s {nameof(containerSpec)}.ReadinessProbe");
            }
        }
    }
}
