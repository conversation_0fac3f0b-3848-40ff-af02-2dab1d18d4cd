﻿// <copyright file="GeneralValidator.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using Microsoft.MachineLearning.ModelMesh.ModelHost.Types.ConfigurationDocuments.Common;
using Microsoft.MachineLearning.ModelMesh.ModelHost.Types.ConfigurationDocuments.ModelInstance;

namespace Microsoft.MachineLearning.ModelMesh.ModelHost.Services.Validators
{
    public class GeneralValidator
    {
        private const int _maxStringLength = 256;
        private const string _nameRegexStr = @"(^[A-Za-z0-9][A-Za-z0-9-_.]*[A-Za-z0-9]$)|^[A-Za-z0-9]$";
        private const string _pathRegexStr = @"^[a-zA-Z0-9./\-_]+$";
        private const string _manifestFilePathStr = @"^[a-zA-Z0-9./:_\-]+$";
        private const string _ipRegexStr = @"^(\d{1,3}\.){3}\d{1,3}$"; // IPv4 only, should ipv6 be supported?
        private const string _modelIdRegexStr = @"^azureml:\/\/registries\/(?<registryName>[^\/]+)\/models\/(?<modelName>[^\/]+)$";
        private const string _blobUriRegexStr = @"^https:\/\/(?<storageAccountName>[a-z0-9]+)\.blob\.core\.windows\.net(?<port>:[0-9]+)?\/(?<containerName>[a-z0-9\-]+)\/(?<blobName>.+)$"; // Matches Azure Blob Storage URIs

        // This needs to conform to the POSIX environment variable name standard, since we eventually construct envvars from the model registry asset names.
        // We can tolerate the first character being a digit (POSIX cannot), because this string is appended to a prefix to derive the final envvar name. 
        private const string _modelRegistryAssetNameRegexStr = @"^[a-zA-Z0-9_]*$";

        private static readonly Regex _nameRegex = new Regex(_nameRegexStr, RegexOptions.Compiled | RegexOptions.IgnoreCase);
        private static readonly Regex _pathRegex = new Regex(_pathRegexStr, RegexOptions.Compiled | RegexOptions.IgnoreCase);
        private static readonly Regex _manifestFilePathRegex = new Regex(_manifestFilePathStr, RegexOptions.Compiled | RegexOptions.IgnoreCase);
        private static readonly Regex _ipRegex = new Regex(_ipRegexStr, RegexOptions.Compiled | RegexOptions.IgnoreCase);
        private static readonly Regex _modelIdRegex = new Regex(_modelIdRegexStr, RegexOptions.Compiled | RegexOptions.IgnoreCase);
        private static readonly Regex _modelRegistryAssetNameRegex = new(_modelRegistryAssetNameRegexStr, RegexOptions.Compiled | RegexOptions.IgnoreCase);
        private static readonly Regex _blobUriRegex = new(_blobUriRegexStr, RegexOptions.Compiled | RegexOptions.IgnoreCase);

        public void ValidateEndpoint(ref List<Exception> exceptions, EndpointConfiguration endpointConfiguration, string propertyName)
        {
            ValidateNaming(ref exceptions, $"{propertyName}.{nameof(endpointConfiguration.Scheme)}", endpointConfiguration.Scheme);
            ValidateRoute(ref exceptions, $"{propertyName}.{nameof(endpointConfiguration.Path)}", endpointConfiguration.Path);
            if (endpointConfiguration.Port <= 0)
            {
                exceptions.Add(new ArgumentException($"{propertyName}.{nameof(endpointConfiguration.Port)} is not valid. actual get: {endpointConfiguration.Port}"));
            }
        }

        public void ValidateInferenceEndpoints(ref List<Exception> exceptions, ICollection<InferenceEndpointConfiguration> endpointConfigurations, string propertyName)
        {
            if (endpointConfigurations == null)
            {
                exceptions.Add(new ArgumentNullException($"{propertyName} cannot be null."));
                return;
            }
            foreach (var endpointConfiguration in endpointConfigurations)
            {
                ValidateEndpoint(ref exceptions, endpointConfiguration, propertyName);
            }
        }

        public void ValidateModelId(ref List<Exception> exceptions, string modelId)
        {
            if (!ValidateString(ref exceptions, nameof(modelId), modelId))
            {
                return;
            }
            if (!string.IsNullOrEmpty(modelId) && !_modelIdRegex.IsMatch(modelId))
            {
                exceptions.Add(new ArgumentException($"Malformed model ID - expected a string that conforms to `{_modelIdRegexStr}`"));
            }

            // TODO to make calls to valify if the modelId exists in the model registry
        }

        public void ValidateModelRegistryAssets(ref List<Exception> exceptions, IDictionary<string, ModelRegistryStorageInfo> modelRegistryAssets)
        {
            if (modelRegistryAssets == null)
            {
                exceptions.Add(new ArgumentNullException("modelRegistryAssets cannot be null."));
                return;
            }

            foreach ((var modelRegistryAssetName, var modelRegistryAsset) in modelRegistryAssets)
            {
                ValidateModelRegistryAssetName(ref exceptions, modelRegistryAssetName);
                ValidateModelRegistryAsset(ref exceptions, modelRegistryAsset);
            }
        }

        private void ValidateModelRegistryAssetName(ref List<Exception> exceptions, string modelRegistryAssetName)
        {
            if (!_modelRegistryAssetNameRegex.IsMatch(modelRegistryAssetName))
            {
                exceptions.Add(new ArgumentException($"{nameof(modelRegistryAssetName)} need to be (alphanumeric, _) only, actual get: {modelRegistryAssetName}."));
            }
        }

        private void ValidateModelRegistryAsset(ref List<Exception> exceptions, ModelRegistryStorageInfo modelRegistryStorageInfo)
        {
            ValidateNaming(ref exceptions, nameof(modelRegistryStorageInfo.RegistryResourceGroupName), modelRegistryStorageInfo.RegistryResourceGroupName);
            ValidateNaming(ref exceptions, nameof(modelRegistryStorageInfo.RegistryStorageAccountName), modelRegistryStorageInfo.RegistryStorageAccountName);
            ValidateNaming(ref exceptions, nameof(modelRegistryStorageInfo.RegistryContainerName), modelRegistryStorageInfo.RegistryContainerName);
            ValidateManifestFilePath(ref exceptions, nameof(modelRegistryStorageInfo.ManifestFilePath), modelRegistryStorageInfo.ManifestFilePath);
            ValidatePath(ref exceptions, nameof(modelRegistryStorageInfo.MountPath), modelRegistryStorageInfo.MountPath);
        }

        public void ValidateUserAssignedIdentity(ref List<Exception> exceptions, UserAssignedIdentity userAssignedIdentity)
        {
            if (userAssignedIdentity == null)
            {
                exceptions.Add(new ArgumentNullException("userAssignedIdentity cannot be null."));
                return;
            }

            // TODO to validate if the UAI is grant with the right permissions
        }

        public void ValidateHotswapEngine(ref List<Exception> issues, string? hotswapEngine)
        {
            // TODO
        }

        public void ValidateCapabilitySpec(ref List<Exception> issues, CapabilitySpec? capabilitySpec)
        {
            if (capabilitySpec != null)
            {
                if (string.IsNullOrWhiteSpace(capabilitySpec.CapabilityName))
                {
                    issues.Add(new ArgumentException($"{nameof(capabilitySpec)}.{nameof(capabilitySpec.CapabilityName)} cannot be null or whitespace."));
                }

                if (string.IsNullOrWhiteSpace(capabilitySpec.GroupId))
                {
                    issues.Add(new ArgumentException($"{nameof(capabilitySpec)}.{nameof(capabilitySpec.GroupId)} cannot be null or whitespace."));
                }

                if (string.IsNullOrWhiteSpace(capabilitySpec.GroupName))
                {
                    issues.Add(new ArgumentException($"{nameof(capabilitySpec)}.{nameof(capabilitySpec.GroupName)} cannot be null or whitespace."));
                }

                if (capabilitySpec.ExpectedCapabilities == null)
                {
                    issues.Add(new ArgumentNullException($"{nameof(capabilitySpec)}.{nameof(capabilitySpec.ExpectedCapabilities)} cannot be null."));
                }
                else
                {
                    var containsCapabilityName = false;
                    foreach (var capability in capabilitySpec.ExpectedCapabilities)
                    {
                        if (capability == capabilitySpec.CapabilityName)
                        {
                            containsCapabilityName = true;
                        }
                        if (string.IsNullOrWhiteSpace(capability))
                        {
                            issues.Add(new ArgumentException($"{nameof(capabilitySpec)}.{nameof(capabilitySpec.ExpectedCapabilities)} contains a null or whitespace capability."));
                        }
                    }

                    if (!containsCapabilityName)
                    {
                        issues.Add(new ArgumentException($"{nameof(capabilitySpec)}.{nameof(capabilitySpec.ExpectedCapabilities)} must contain capability {capabilitySpec.CapabilityName}."));
                    }
                }
            }
        }

        public bool ValidateString(ref List<Exception> exceptions, string propertyName, string propertyValue)
        {
            if (string.IsNullOrWhiteSpace(propertyValue))
            {
                exceptions.Add(new ArgumentNullException($"{propertyName} cannot be null or whitespace."));
                return false;
            }

            if (propertyValue.Length > _maxStringLength)
            {
                exceptions.Add(new ArgumentException($"{propertyName} length need to be within [1, {_maxStringLength}), actual get: {propertyValue.Length}."));
                return false;
            }

            return true;
        }

        public bool ValidateNaming(ref List<Exception> exceptions, string propertyName, string propertyValue)
        {
            if (!ValidateString(ref exceptions, propertyName, propertyValue))
            {
                return false;
            }
            if (!_nameRegex.IsMatch(propertyValue))
            {
                exceptions.Add(new ArgumentException($"{propertyName} need to be (alphanumeric, ., -, _) only, and should start and end with alphanumeric, actual get: {propertyValue}."));
                return false;
            }

            return true;
        }

        public void ValidatePath(ref List<Exception> exceptions, string propertyName, string propertyValue)
        {
            if (!ValidateString(ref exceptions, propertyName, propertyValue))
            {
                return;
            }
            if (!_pathRegex.IsMatch(propertyValue))
            {
                exceptions.Add(new ArgumentException($"{propertyName} need to be valid that includes (alphanumeric, ., -, _, /) only, actual get: {propertyValue}."));
            }
        }

        public void ValidateRoute(ref List<Exception> exceptions, string propertyName, string propertyValue)
        {
            // Routes are allowed to be null/empty.
            if (string.IsNullOrEmpty(propertyValue))
            {
                return;
            }

            // If they are not, validate that they are valid paths.
            if (!_pathRegex.IsMatch(propertyValue))
            {
                exceptions.Add(new ArgumentException($"{propertyName} need to be valid that includes (alphanumeric, ., -, _, /) only, actual get: {propertyValue}."));
            }
        }

        public void ValidateManifestFilePath(ref List<Exception> exceptions, string propertyName, string propertyValue)
        {
            if (!ValidateString(ref exceptions, propertyName, propertyValue))
            {
                return;
            }
            if (!_manifestFilePathRegex.IsMatch(propertyValue))
            {
                exceptions.Add(new ArgumentException($"{propertyName} need to be valid that includes (alphanumeric, ., -, _, /, :) only, actual get: {propertyValue}."));
            }
        }

        public void ValidateIpAddresses(ref List<Exception> exceptions, IList<string> ipAddresses)
        {
            foreach (string ip in ipAddresses)
            {
                if (!_ipRegex.IsMatch(ip))
                {
                    exceptions.Add(new ArgumentException($"IP address {ip} is not valid."));
                }
            }
        }

        public void ValidateBlobUri(ref List<Exception> exceptions, string blobUri)
        {
            if (!ValidateString(ref exceptions, nameof(blobUri), blobUri))
            {
                return;
            }
            if (!_blobUriRegex.IsMatch(blobUri))
            {
                exceptions.Add(new ArgumentException($"Blob URI {blobUri} is not valid."));
            }
        }
    }
}
