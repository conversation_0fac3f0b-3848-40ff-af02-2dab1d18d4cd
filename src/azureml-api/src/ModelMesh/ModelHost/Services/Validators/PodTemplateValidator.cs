﻿// <copyright file="PodSpecValidator.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using k8s;
using k8s.Models;

namespace Microsoft.MachineLearning.ModelMesh.ModelHost.Services.Validators
{
    public static class PodTemplateValidator
    {
        private static string InferenceServerContainerName = "inference-server";
        private static string HttpScheme = "HTTP";
        private static string HttpsScheme = "HTTPS";

        public static void ValidatePodTemplate(string podTemplate, bool expectContainerImages, bool expectInferenceServer = false)
        {
            ArgumentNullException.ThrowIfNullOrEmpty(podTemplate, nameof(podTemplate));
            
            V1PodTemplate parsedPodTemplate;
            try
            {
                parsedPodTemplate = KubernetesYaml.Deserialize<V1PodTemplate>(podTemplate);
            }
            catch (Exception e)
            {
                throw new ArgumentException("Failed to parse pod template yaml", e);
            }

            parsedPodTemplate.Validate();

            ArgumentNullException.ThrowIfNull(parsedPodTemplate.Template, $"{nameof(podTemplate)}.Template");
            ArgumentNullException.ThrowIfNull(parsedPodTemplate.Template.Spec, $"{nameof(podTemplate)}.Template.Spec");
            ArgumentNullException.ThrowIfNull(parsedPodTemplate.Template.Spec.Containers, $"{nameof(podTemplate)}.Template.Spec.Containers");

            if (parsedPodTemplate.Template.Spec.Containers.Count < 1)
            {
                throw new ArgumentException("Pod templates must specify at least one container.");
            }

            var inferenceServerContainerExists = false;
            for (var containerIx = 0; containerIx < parsedPodTemplate.Template.Spec.Containers.Count; containerIx ++)
            {
                var container = parsedPodTemplate.Template.Spec.Containers[containerIx];
                if (expectContainerImages)
                {
                    ArgumentNullException.ThrowIfNull(container.Image, $"{nameof(podTemplate)}.Template.Spec.Containers[{containerIx}].Image");

                    if (string.Equals(container.Name, InferenceServerContainerName))
                    {
                        inferenceServerContainerExists = true;
                    }
                }
                else
                {
                    if (container.Image != null)
                    {
                        throw new ArgumentException($"Expected {nameof(podTemplate)}.Template.Spec.Containers[{containerIx}].Image to be null - container images must be specified via environment assets.");
                    }
                }

                ArgumentNullException.ThrowIfNull(container.Command, $"{nameof(podTemplate)}.Template.Spec.Containers[{containerIx}].Command");
                ArgumentNullException.ThrowIfNull(container.StartupProbe, $"{nameof(podTemplate)}.Template.Spec.Containers[{containerIx}].StartupProbe");

                if (container.StartupProbe.HttpGet == null && container.StartupProbe.Exec == null)
                {
                    throw new ArgumentException($"Expected exactly one of either an HttpGet or Exec configuration in {nameof(podTemplate)}.Template.Spec.Containers[{containerIx}].StartupProbe, found neither.");
                }

                if (container.StartupProbe.HttpGet != null && container.StartupProbe.Exec != null)
                {
                    throw new ArgumentException($"Expected exactly one of either an HttpGet or Exec configuration in {nameof(podTemplate)}.Template.Spec.Containers[{containerIx}].StartupProbe, found both.");
                }

                if (container.StartupProbe.HttpGet != null)
                {
                    ArgumentNullException.ThrowIfNull(container.StartupProbe.HttpGet.Path, $"{nameof(podTemplate)}.Template.Spec.Containers[{containerIx}].StartupProbe.HttpGet.Path");
                    ArgumentNullException.ThrowIfNull(container.StartupProbe.HttpGet.Port, $"{nameof(podTemplate)}.Template.Spec.Containers[{containerIx}].StartupProbe.HttpGet.Port");
                    ArgumentNullException.ThrowIfNull(container.StartupProbe.HttpGet.Scheme, $"{nameof(podTemplate)}.Template.Spec.Containers[{containerIx}].StartupProbe.HttpGet.Scheme");

                    var scheme = container.StartupProbe.HttpGet.Scheme;
                    if (!string.Equals(scheme, HttpScheme, StringComparison.OrdinalIgnoreCase) && !string.Equals(scheme, HttpsScheme, StringComparison.OrdinalIgnoreCase))
                    {
                        throw new ArgumentException($"Expected exactly one of either an Http or Https scheme in {nameof(podTemplate)}.Template.Spec.Containers[{containerIx}].StartupProbe.HttpGet.Scheme, found neither.");
                    }
                }

                if (container.StartupProbe.Grpc != null)
                {
                    throw new ArgumentException($"Grpc configurations in startup probes are not supported: {nameof(podTemplate)}.Template.Spec.Containers[{containerIx}].StartupProbe, specifies a Grpc probe.");
                }

                if (container.StartupProbe.TcpSocket != null)
                {
                    throw new ArgumentException($"TcpSocket configurations in startup probes are not supported: {nameof(podTemplate)}.Template.Spec.Containers[{containerIx}].StartupProbe, specifies a TcpSocket probe.");
                }

                ArgumentNullException.ThrowIfNull(container.ReadinessProbe, $"{nameof(podTemplate)}.Template.Spec.Containers[{containerIx}].ReadinessProbe");
                if (container.ReadinessProbe.HttpGet == null && container.ReadinessProbe.Exec == null)
                {
                    throw new ArgumentException($"Expected exactly one of either an HttpGet or Exec configuration in {nameof(podTemplate)}.Template.Spec.Containers[{containerIx}].ReadinessProbe, found neither.");
                }

                if (container.ReadinessProbe.HttpGet != null && container.ReadinessProbe.Exec != null)
                {
                    throw new ArgumentException($"Expected exactly one of either an HttpGet or Exec configuration in {nameof(podTemplate)}.Template.Spec.Containers[{containerIx}].ReadinessProbe, found both.");
                }

                if (container.ReadinessProbe.HttpGet != null)
                {
                    ArgumentNullException.ThrowIfNull(container.ReadinessProbe.HttpGet.Path, $"{nameof(podTemplate)}.Template.Spec.Containers[{containerIx}].ReadinessProbe.HttpGet.Path");
                    ArgumentNullException.ThrowIfNull(container.ReadinessProbe.HttpGet.Port, $"{nameof(podTemplate)}.Template.Spec.Containers[{containerIx}].ReadinessProbe.HttpGet.Port");
                    ArgumentNullException.ThrowIfNull(container.ReadinessProbe.HttpGet.Scheme, $"{nameof(podTemplate)}.Template.Spec.Containers[{containerIx}].ReadinessProbe.HttpGet.Scheme");

                    var scheme = container.ReadinessProbe.HttpGet.Scheme;
                    if (!string.Equals(scheme, HttpScheme, StringComparison.OrdinalIgnoreCase) && !string.Equals(scheme, HttpsScheme, StringComparison.OrdinalIgnoreCase))
                    {
                        throw new ArgumentException($"Expected exactly one of either an Http or Https scheme in {nameof(podTemplate)}.Template.Spec.Containers[{containerIx}].ReadinessProbe.HttpGet.Scheme, found neither.");
                    }
                }

                if (container.ReadinessProbe.Grpc != null)
                {
                    throw new ArgumentException($"Grpc configurations in readiness probes are not supported: {nameof(podTemplate)}.Template.Spec.Containers[{containerIx}].ReadinessProbe, specifies a Grpc probe.");
                }

                if (container.ReadinessProbe.TcpSocket != null)
                {
                    throw new ArgumentException($"TcpSocket configurations in readiness probes are not supported: {nameof(podTemplate)}.Template.Spec.Containers[{containerIx}].ReadinessProbe, specifies a TcpSocket probe.");
                }

                ArgumentNullException.ThrowIfNull(container.LivenessProbe, $"{nameof(podTemplate)}.Template.Spec.Containers[{containerIx}].LivenessProbe");
                if (container.LivenessProbe.HttpGet == null && container.LivenessProbe.Exec == null)
                {
                    throw new ArgumentException($"Expected exactly one of either an HttpGet or Exec configuration in {nameof(podTemplate)}.Template.Spec.Containers[{containerIx}].LivenessProbe, found neither.");
                }

                if (container.LivenessProbe.HttpGet != null && container.LivenessProbe.Exec != null)
                {
                    throw new ArgumentException($"Expected exactly one of either an HttpGet or Exec configuration in {nameof(podTemplate)}.Template.Spec.Containers[{containerIx}].LivenessProbe, found both.");
                }

                if (container.LivenessProbe.HttpGet != null)
                {
                    ArgumentNullException.ThrowIfNull(container.LivenessProbe.HttpGet.Path, $"{nameof(podTemplate)}.Template.Spec.Containers[{containerIx}].LivenessProbe.HttpGet.Path");
                    ArgumentNullException.ThrowIfNull(container.LivenessProbe.HttpGet.Port, $"{nameof(podTemplate)}.Template.Spec.Containers[{containerIx}].LivenessProbe.HttpGet.Port");
                    ArgumentNullException.ThrowIfNull(container.LivenessProbe.HttpGet.Scheme, $"{nameof(podTemplate)}.Template.Spec.Containers[{containerIx}].LivenessProbe.HttpGet.Scheme");

                    var scheme = container.LivenessProbe.HttpGet.Scheme;
                    if (!string.Equals(scheme, HttpScheme, StringComparison.OrdinalIgnoreCase) && !string.Equals(scheme, HttpsScheme, StringComparison.OrdinalIgnoreCase))
                    {
                        throw new ArgumentException($"Expected exactly one of either an Http or Https scheme in {nameof(podTemplate)}.Template.Spec.Containers[{containerIx}].LivenessProbe.HttpGet.Scheme, found neither.");
                    }
                }

                if (container.LivenessProbe.Grpc != null)
                {
                    throw new ArgumentException($"Grpc configurations in liveness probes are not supported: {nameof(podTemplate)}.Template.Spec.Containers[{containerIx}].LivenessProbe, specifies a Grpc probe.");
                }

                if (container.LivenessProbe.TcpSocket != null)
                {
                    throw new ArgumentException($"TcpSocket configurations in liveness probes are not supported: {nameof(podTemplate)}.Template.Spec.Containers[{containerIx}].LivenessProbe, specifies a TcpSocket probe.");
                }
            }

            if (expectInferenceServer && !inferenceServerContainerExists)
            {
                throw new ArgumentException($"Container inference-server is not found in {nameof(podTemplate)}.Template.Spec.Containers list");
            }
        }
    }
}
