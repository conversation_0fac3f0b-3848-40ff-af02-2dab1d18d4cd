﻿// <copyright file="InstanceTypeValidator.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Azure.Management.Singularity;
using Microsoft.Azure.Management.Singularity.Models;
using Microsoft.MachineLearning.Common.Core.Arm.Contracts;
using Microsoft.MachineLearning.ModelMesh.ModelHost.Services.Clients.DeploymentFabric;
using Microsoft.MachineLearning.ModelMesh.ModelHost.Types.DeploymentFabric;

namespace Microsoft.MachineLearning.ModelMesh.ModelHost.Services.Validators
{
    public class InstanceTypeValidator(
        SingularityClientFactory singularityClientFactory,
        IList<SingularityDeploymentFabricConfiguration> clusterConfigs,
        GeneralValidator generalValidator)
    {
        public async void ValidateInstanceTypeAvalibility(List<Exception> exceptions, string instanceType)
        {
            if (exceptions == null)
            {
                exceptions = new List<Exception>();
            }
            if (!generalValidator.ValidateNaming(ref exceptions, nameof(instanceType), instanceType))
            {
                return;
            }
            // Make calls to quota API to check if the instance Type is supported and has available quota
            var clients = await GetClientAsync().ConfigureAwait(false);
            foreach (var (accountArmId, client) in clients)
            {
                var accountResource = await client.Account.GetWithHttpMessagesAsync(accountArmId.ResourceGroup, accountArmId.ResourceName).ConfigureAwait(false);
                var location = accountResource.Body.Location;

                string? nextPageLink = null;
                var accountQuotaPolicy = new List<AccountQuotaPolicyResourceDescription>();
                do
                {
                    var accountQuota = string.IsNullOrEmpty(nextPageLink) ?
                        await client.AccountQuotaPolicy.ListByAccountAsync(accountArmId.ResourceGroup, accountArmId.ResourceName).ConfigureAwait(false) :
                        await client.AccountQuotaPolicy.ListByAccountNextAsync(nextPageLink).ConfigureAwait(false);

                    accountQuotaPolicy.AddRange(accountQuota);

                    nextPageLink = accountQuota.NextPageLink;
                }
                while (!string.IsNullOrEmpty(nextPageLink));

                foreach (var entry in accountQuotaPolicy)
                {
                    foreach (var item in entry.Quotas)
                    {
                        if (!string.Equals("Premium", item.SlaTier, StringComparison.Ordinal))
                        {
                            continue;
                        }

                        if (!item.Limit.HasValue || !item.Used.HasValue)
                        {
                            continue;
                        }

                        if (item.Limit == 0 && item.Used == 0)
                        {
                            continue;
                        }

                        var instanceTypesDescriptions = await client.InstanceTypeSeries.ListInstanceTypeAsync(location, item.Id).ConfigureAwait(false);

                        foreach (var instanceTypesDescription in instanceTypesDescriptions)
                        {
                            if (instanceTypesDescription.QuotaCount == null || instanceTypesDescription.QuotaCount == 0)
                            {
                                continue;
                            }
                            if (instanceType.Equals(instanceTypesDescription.Name, StringComparison.OrdinalIgnoreCase))
                            {
                                return;
                            }
                        }
                    }
                }
            }

            exceptions.Add(new ArgumentException($"Instance type {instanceType} is not available"));
        }

        private async Task<IEnumerable<(ResourceArmScope, ISingularityManagementClient)>> GetClientAsync()
        {
            return await Task.WhenAll(clusterConfigs
                .Select(async config => (singularityClientFactory.GetArmId(config), await singularityClientFactory.GetSingularityClientAsync(config))));
        }
    }
}
