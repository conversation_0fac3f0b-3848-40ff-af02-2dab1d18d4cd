﻿// <copyright file="ModelDeploymentService.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Azure.Management.Singularity;
using Microsoft.Azure.Management.Singularity.Models;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.MachineLearning.Common.Core.Arm.Contracts;
using Microsoft.MachineLearning.ModelMesh.ModelHost.Contracts.ModelDeployment;
using Microsoft.MachineLearning.ModelMesh.ModelHost.Services.Clients;
using Microsoft.MachineLearning.ModelMesh.ModelHost.Services.Clients.DeploymentFabric;
using Microsoft.MachineLearning.ModelMesh.ModelHost.Services.ECS;
using Microsoft.MachineLearning.ModelMesh.ModelHost.Services.Utilities;
using Microsoft.MachineLearning.ModelMesh.ModelHost.Types.Allocation;
using Microsoft.MachineLearning.ModelMesh.ModelHost.Types.DeploymentDocuments;
using Microsoft.MachineLearning.ModelMesh.ModelHost.Types.DeploymentFabric;
using Microsoft.MachineLearning.ModelMesh.ModelHost.Types.ModelResource;
using Microsoft.MachineLearning.ModelMesh.ModelHost.Types.Redis;
using Microsoft.MachineLearning.ModelMesh.Shared.Common.Redis;
using Microsoft.MachineLearning.ModelRegistry.Contracts;
using Newtonsoft.Json;
using StackExchange.Redis;
using static Microsoft.MachineLearning.ModelMesh.ModelHost.Contracts.Deployment.DataplaneConfigurationDto;
using static Microsoft.MachineLearning.ModelMesh.ModelHost.Contracts.Deployment.ModelConfigurationDto;

namespace Microsoft.MachineLearning.ModelMesh.ModelHost.Services
{
    public interface IModelDeploymentService
    {
        public Task<ModelDefModelResource> CreateOrUpdateModelDeploymentAsync(DeploymentDocument deploymentDocument);

        public Task<List<ModelDeploymentDto>> ListModelDeploymentsAsync(
            string? continuationToken = null);

        public Task<ModelDeploymentDto> GetModelDeploymentAsync(
            string modelDeploymentName,
            Guid ownerSubscriptionId = default);

        public Task DeleteModelDeploymentAsync(
            string modelDeploymentName,
            string? inferenceModelResourceUri = null,
            Guid ownerSubscriptionId = default);

        public Task ModifyModelDeploymentAllocationAsync(
            string modelDeploymentName,
            ModelDeploymentAllocationActionDto modelDeploymentAllocationAction,
            Guid ownerSubscriptionId = default);
    }

    public class ModelDeploymentService : IModelDeploymentService
    {
        private readonly ILogger _logger;
        private readonly SingularityClientFactory _singularityClientFactory;
        private readonly IModelRegistryClient _modelRegistryClient;
        private readonly IIntellectualPropertyPublisherClient _intellectualPropertyPublisherClient;
        private readonly IModelHostConfigurationProvider _modelHostConfigurationProvider;
        private readonly ISingularityDeploymentFabricConfigService _singularityDeploymentFabricConfigService;
        private readonly Task<IDatabase> _redisClientMaaS;
        private readonly Task<IDatabase> _redisClientAOAI;
        private readonly string _region;

        public ModelDeploymentService(
            ILoggerFactory loggerFactory,
            SingularityClientFactory singularityClientFactory,
            IModelRegistryClient modelRegistryClient,
            IIntellectualPropertyPublisherClient intellectualPropertyPublisherClient,
            IOptionsMonitor<ModelHostRedisConnections> redisConnectionConfiguration,
            IModelHostConfigurationProvider modelHostConfigurationProvider,
            ISingularityDeploymentFabricConfigService singularityDeploymentFabricConfigService,
            IRedisConnectionFactory redisConnectionFactory,
            IWebHostEnvironment environment)
        {
            _logger = loggerFactory.CreateLogger<ModelDeploymentService>();
            _singularityClientFactory = singularityClientFactory;
            _modelRegistryClient = modelRegistryClient;
            _intellectualPropertyPublisherClient = intellectualPropertyPublisherClient;
            _redisClientAOAI = redisConnectionFactory.GetDatabaseAsync(redisConnectionConfiguration.CurrentValue.IPPRedisConnections[IPPublisherType.AOAI_IPP].ControlChannel.HostName!);
            _redisClientMaaS = redisConnectionFactory.GetDatabaseAsync(redisConnectionConfiguration.CurrentValue.IPPRedisConnections[IPPublisherType.MaaS_IPP].ControlChannel.HostName!);

            _modelHostConfigurationProvider = modelHostConfigurationProvider;
            _singularityDeploymentFabricConfigService = singularityDeploymentFabricConfigService;

            _region = string.Equals("development", environment.EnvironmentName, StringComparison.OrdinalIgnoreCase) ? "int" : environment.EnvironmentName;
        }

        public async Task<ModelDefModelResource> CreateOrUpdateModelDeploymentAsync(DeploymentDocument deploymentDocument)
        {
            var modelDefConfiguration = (PresetV3)deploymentDocument.DeploymentConfiguration.ModelConfiguration;
            string modelDeploymentName = deploymentDocument.Id;

            _logger.LogInformation(
                $"Creating or updating model deployment {modelDeploymentName} for ownersubscriptionId {deploymentDocument.DeploymentConfiguration.TargetCluster.SubscriptionId} and region {deploymentDocument.DeploymentConfiguration.TargetCluster.Region}");

            var singularityDeploymentFabricConfig = _singularityDeploymentFabricConfigService
                .Find(new ClusterSelector
                {
                    OwnerSubscriptionId = deploymentDocument.DeploymentConfiguration.TargetCluster.SubscriptionId,
                    IsModelPoolOnboarded = true,
                }).Single();

            _logger.LogInformation($"Using Singularity deployment fabric configuration: {singularityDeploymentFabricConfig.ClusterName} for model deployment {modelDeploymentName}." +
                $"Using singularity account {singularityDeploymentFabricConfig.AccountArmId} in tenant {singularityDeploymentFabricConfig.AccountTenantId}");

            ResourceArmScope accountArmScope = _singularityClientFactory.ParseArmId(singularityDeploymentFabricConfig.AccountArmId);

            // 1. Get the Singularity client
            var client = await _singularityClientFactory.GetSingularityClientAsync(singularityDeploymentFabricConfig).ConfigureAwait(false);

            // 2. Get the model preset v3 deployment settings
            var modelPresetDeploymentSettings = await _modelRegistryClient
                .GetDeploymentSettingsV3Async(
                    modelIdentifiers: new ModelSettingsIdentifiers
                    {
                        EngineId = modelDefConfiguration.EngineAssetId,
                        ModelParameters = ModelSettingUtilities.ConvertModelIdToModelParameters(modelDefConfiguration.ModelAssetIds),
                        ModelsTenantId = singularityDeploymentFabricConfig.AccountTenantId,
                    })
                .ConfigureAwait(false);

            if (modelPresetDeploymentSettings == null)
            {
                throw new InvalidOperationException("Model preset deployment v3 settings are not available.");
            }

            // 3. Get the IPP data
            var intellectualPropertyPublisherDto = await _intellectualPropertyPublisherClient
                .GetIntellectualPropertyPublisher(modelPresetDeploymentSettings.SystemData?.IntellectualPropertyPublisher!)
                .ConfigureAwait(false);
            var keyVaultInformation = intellectualPropertyPublisherDto?.KeyVaultInformation;

            // Todo shail Only have this check when the keyvault information is needed
            //if (string.IsNullOrEmpty(keyVaultInformation?.KeyName) || string.IsNullOrEmpty(keyVaultInformation?.KeyVaultUrl))
            //{
            //    throw new InvalidOperationException("Key vault information is not available.");
            //}

            // 4. Fill value sets (To add the system data)
            var systemMetadata = new Types.PresetV3.SystemMetadata
            {
                DeploymentName = modelDeploymentName,
                EndpointName = modelDeploymentName,
                SubscriptionId = accountArmScope.SubscriptionId.ToString(),
                WorkspaceName = "random",
                ResourceGroupName = "random",
                Region = deploymentDocument.DeploymentConfiguration.TargetCluster.Region ?? _region,
                OnlineDeploymentResourceId = $"/s/abc/rg/abc/w/abc/ep/{modelDeploymentName}/dep/{modelDeploymentName}"
            };
            ModelPresetV3Utilities.FillValues(
                modelPresetDeploymentSettings,
                modelDefConfiguration.DeploymentProperties,
                keyVaultInformation,
                systemMetadata);

            // 5. Validate the filled templates (Todo Shail)

            // 6. Create storage cache container 
            string storageCacheContainerName = (modelDeploymentName + "_templates").ToLowerInvariant();
            var storageCacheContainer = await client
                .StorageCache.CreateOrUpdateAsync(accountArmScope.ResourceGroup, accountArmScope.ResourceName, storageCacheContainerName, ModelSettingUtilities.ToSingularityStorageRequest(modelPresetDeploymentSettings!))
                .ConfigureAwait(false);

            // send allocation actions if it is present
            if (deploymentDocument.DeploymentConfiguration.DataplaneConfiguration is InferenceModel)
            {
                // TODO: Remove when deprecating ModelDeploymentDto
                var inferenceModel = (InferenceModel)deploymentDocument.DeploymentConfiguration.DataplaneConfiguration;
                var modelDefAllocation = new ModelDeploymentAllocationActionDto
                {
                    Action = "attach",
                    InferenceModelResourceUri = inferenceModel.IMResourceId,
                    CapacityUnitsPerModality = inferenceModel.CapacityUnitsPerModality?
                        .ToDictionary(kvP => kvP.Key, kvP => new Contracts.ModelDeployment.CapacityUnits
                        {
                            PTU = kvP.Value.PTU,
                            SkuFamily = kvP.Value.SKUFamily ?? string.Empty,
                        }),
                };

                await ModifyModelDeploymentAllocationAsync(
                        modelDeploymentName,
                        modelDefAllocation,
                        deploymentDocument.DeploymentConfiguration.TargetCluster.SubscriptionId)
                    .ConfigureAwait(false);
            }

            // 7. Convert to singularity templated model resource
            var templatedModelResource = ModelSettingUtilities.ToSingularityTemplatedModelResourceDescription(
                _logger,
                modelDeploymentName,
                1,
                1,
                modelDefConfiguration.InstanceType,
                InferenceMode.Online,
                ModelSettingUtilities.GetSlaTier(deploymentDocument.Tags),
                deploymentDocument.Tags,
                modelDefConfiguration.DeploymentProperties,
                storageCacheContainerName,
                modelPresetDeploymentSettings,
                modelDefConfiguration.EngineAssetId,
                modelDefConfiguration.ModelAssetIds.FirstOrDefault(),
                deploymentDocument.DeploymentConfiguration.TargetCluster.Region,
                deploymentDocument.DeploymentConfiguration.TargetCluster.SubscriptionId.ToString());

            bool injectSidecar = true;
            var standardSidecarBundle = default((string configurationName, string configurationVersion));

            // 8. Skip sidecar injection if the model deployment properties mention that.
            if (modelDefConfiguration.DeploymentProperties.TryGetValue(Types.Constants.ModelHostSkipSidecarInjection, out var skipSidecarInjection) &&
                string.Equals(skipSidecarInjection, bool.TrueString, StringComparison.OrdinalIgnoreCase))
            {
                _logger.LogInformation("Skipping sidecar injection for model deployment {ModelDeploymentName}", modelDeploymentName);
                injectSidecar = false;
            }
            else
            {
                // 8.1 Get the sidecar bundle configurations
                var sidecarBundleConfigurations = await _modelHostConfigurationProvider.ProductionConfigurations.GetSidecarBundleConfigurationsAsync().ConfigureAwait(false);

                // 8.2 Get the standard sidecar bundle to be injected
                standardSidecarBundle = sidecarBundleConfigurations.FirstOrDefault(x => x.configurationName.Equals(Types.Constants.ModelHostSidecarInjectionConfigName, StringComparison.OrdinalIgnoreCase));
                if (standardSidecarBundle == default)
                {
                    _logger.LogInformation("Standard sidecar bundle configuration to inject is not available. Skipping sidecar injection for model deployment {ModelDeploymentName}", modelDeploymentName);
                    injectSidecar = false;
                }
                // Check if there was a request to override the sidecar image for testing
                if (modelDefConfiguration.DeploymentProperties.TryGetValue(Types.Constants.ModelHostSidecarImageOverride, out var sidecarImageOverride)
                    && !string.IsNullOrWhiteSpace(sidecarImageOverride))
                {
                    standardSidecarBundle = (Types.Constants.ModelHostSidecarInjectionConfigName, sidecarImageOverride);
                    injectSidecar = true;
                }
            }

            if (injectSidecar)
            {
                if (modelDefConfiguration.DeploymentProperties.TryGetValue(Types.Constants.ModelHostOverrideRegistryReaderIdentityScope, out var scopeValue) &&
                    !string.IsNullOrWhiteSpace(scopeValue) &&
                    modelDefConfiguration.DeploymentProperties.TryGetValue(Types.Constants.ModelHostOverrideRegistryReaderIdentityClientId, out var clientIdValue) &&
                    !string.IsNullOrWhiteSpace(clientIdValue) &&
                    Guid.TryParse(clientIdValue, out var acrClientId))
                {
                    _logger.LogInformation("Overriding model mesh registry reader identity for model deployment {ModelDeploymentName} with client ID {AcrClientId}",
                        modelDeploymentName, acrClientId);

                    singularityDeploymentFabricConfig.ModelMeshRegistryReaderIdentity = new Types.ConfigurationDocuments.Common.UserAssignedIdentity(
                        resourceId: scopeValue,
                        clientId: acrClientId,
                        principalId: Guid.Empty
                    );
                }

                var envVariables = new Dictionary<string, string>
                {
                    { "SINGULARITY_DEPLOYMENT_NAME", modelDeploymentName},
                    { "SINGULARITY_AISC_RESOURCE_ID", $"{singularityDeploymentFabricConfig.AccountArmId}/templatedModels/{modelDeploymentName}"},
                    { "MODEL_INSTANCE_ENV", "dev" },
                    { "RUST_LOG", "debug" },
                    { "InstanceId", modelDeploymentName },
                    { "Identity__ClientId", singularityDeploymentFabricConfig.ModelInstanceIdentity.ClientId.ToString() },
                    { "ProbesEndpoint", "0.0.0.0:8082" },
                    { "ModelConfigSource__Type", "static" },
                    { "ModelConfigSource__RedisConfig__ConnectionString", singularityDeploymentFabricConfig.RedisConnections.ControlChannel.HostName },
                    { "ModelConfigSource__RedisConfig__Channel", AllocationMessageVersionHelper.GetControlInfoKey(modelDeploymentName) },
                    { "ModelConfigSource__RedisConfig__EntryName", Types.Constants.AllocationEntryName },
                };

                templatedModelResource.ValueSets.Add(new ValueSet
                {
                    Id = "modelpool.modelmesh",
                    HasSecrets = false,
                    Values = new Dictionary<string, string>
                    {
                        { "appName", "model-mesh" },
                        { "replicas", "1" },
                        { "image", standardSidecarBundle.configurationVersion! },
                        { "command", "./im_model_instance" },
                        { "environmentVariables", JsonConvert.SerializeObject(envVariables) },
                        { "clientid", singularityDeploymentFabricConfig.ModelMeshRegistryReaderIdentity.ClientId.ToString() },
                        { "acr", standardSidecarBundle.configurationVersion!.Split('/')[0] },
                        { "enabled", "true" },
                        { "port", "8080" }
                    }
                });

                // Add identity to pull the acr image and the model instance identity for that ipp.
                templatedModelResource.Identity.UserAssignedIdentities.TryAdd(singularityDeploymentFabricConfig.ModelMeshRegistryReaderIdentity.ResourceId, new ResourceUserAssignedIdentity());
                templatedModelResource.Identity.UserAssignedIdentities.TryAdd(singularityDeploymentFabricConfig.ModelInstanceIdentity.ResourceId, new ResourceUserAssignedIdentity());
            }

            _logger.LogInformation("Creating or updating model deployment {ModelDeploymentName} in singularity account {AccountArmId}",
                modelDeploymentName, singularityDeploymentFabricConfig.AccountArmId);

            // logging the whole body of the templated model resource for debugging purposes
            _logger.LogInformation("Templated Model Resource: {TemplatedModelResource}", JsonConvert.SerializeObject(templatedModelResource, Formatting.Indented));

            var createdModelResource = await client
                .TemplatedModel
                .CreateOrUpdateAsync(accountArmScope.ResourceGroup, accountArmScope.ResourceName, modelDeploymentName, templatedModelResource)
                .ConfigureAwait(false);

            return new ModelDefModelResource(
                id: createdModelResource.Id,
                name: createdModelResource.Name,
                configurationName: string.Empty,
                configurationVersion: string.Empty,
                provisioningState: createdModelResource.ProvisioningState,
                status: createdModelResource?.DeploymentStatus?.Status ?? "Undefined",
                clusterIp: createdModelResource?.GetHostFqdn() ?? string.Empty,
                fqdn: createdModelResource?.GetHostFqdn() ?? string.Empty)
            {
                Tags = createdModelResource?.Tags ?? new Dictionary<string, string>(),
            };
        }

        public async Task DeleteModelDeploymentAsync(
            string modelDeploymentName,
            string? inferenceModelResourceUri = null,
            Guid ownerSubscriptionId = default)
        {
            try
            {
                _logger.LogInformation("Attempting to delete model deployment {ModelDeploymentName}", modelDeploymentName);

                var singularityConfig = GetSingularityConfig(ownerSubscriptionId);

                var client = await _singularityClientFactory.GetSingularityClientAsync(singularityConfig).ConfigureAwait(false);
                ResourceArmScope accountArmScope = _singularityClientFactory.ParseArmId(singularityConfig.AccountArmId);

                if (!string.IsNullOrEmpty(inferenceModelResourceUri))
                {
                    // Send detach message to the redis stream
                    var detachMessage = new DetachMessage
                    {
                        InferenceModelResourceUri = inferenceModelResourceUri,
                    };

                    var redisClient = await GetRedisClient(singularityConfig.IPPublisherType).ConfigureAwait(false);

                    await AllocationMessageHelper.SendAllocationMessageAsync(modelDeploymentName, detachMessage, redisClient).ConfigureAwait(false);
                    _logger.LogInformation("Sent detach message for model deployment {ModelDeploymentName}", modelDeploymentName);
                    await Task.Delay(3000);
                }

                await client.TemplatedModel.BeginDeleteWithHttpMessagesAsync(accountArmScope.ResourceGroup, accountArmScope.ResourceName, modelDeploymentName).ConfigureAwait(false);
                _logger.LogInformation("Deleted model deployment {ModelDeploymentName}", modelDeploymentName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error deleting model deployment {modelDeploymentName}.");
                throw;
            }
        }

        public async Task<ModelDeploymentDto> GetModelDeploymentAsync(
            string modelDeploymentName,
            Guid ownerSubscriptionId = default)
        {
            _logger.LogInformation("Getting model deployment {ModelDeploymentName}", modelDeploymentName);

            // Get the singularity deployment fabric configuration based on owner subscription ID or AOAI deployment configuration
            var singularityConfig = GetSingularityConfig(ownerSubscriptionId);

            var client = await _singularityClientFactory.GetSingularityClientAsync(singularityConfig).ConfigureAwait(false);
            ResourceArmScope accountArmScope = _singularityClientFactory.ParseArmId(singularityConfig.AccountArmId);
            var templatedModel = await client
                .TemplatedModel
                .GetAsync(accountArmScope.ResourceGroup, accountArmScope.ResourceName, modelDeploymentName)
                .ConfigureAwait(false);
            return templatedModel.ConvertToModelDeploymentDto();
        }

        public async Task<List<ModelDeploymentDto>> ListModelDeploymentsAsync(string? continuationToken = null)
        {
            var templatedModelsList = new List<TemplatedModelResourceDescription>();

            // Find all the ownersubscritionId onboarded in the singularity deployment fabric config service
            var ownerSubscriptionIds = _singularityDeploymentFabricConfigService.GetAllOwnerSubscriptionIds();
            if (ownerSubscriptionIds == null || !ownerSubscriptionIds.Any())
            {
                _logger.LogInformation("No owner subscription IDs found for model deployments.");
                return new List<ModelDeploymentDto>();
            }

            var tasks = new List<Task<List<TemplatedModelResourceDescription>>>();
            var exceptions = new List<Exception>();

            // Call singularity client for each owner subscription ID
            foreach (var ownerSubscriptionId in ownerSubscriptionIds)
            {
                // Get all singularity deployment fabric configs for the owner subscription ID
                var singularityConfigs = _singularityDeploymentFabricConfigService
                    .Find(new ClusterSelector
                    {
                        OwnerSubscriptionId = ownerSubscriptionId,
                        IsModelPoolOnboarded = true,
                    });

                if (singularityConfigs == null || !singularityConfigs.Any())
                {
                    _logger.LogWarning("No singularity deployment fabric configurations found for owner subscription ID {OwnerSubscriptionId}", ownerSubscriptionId);
                    continue;
                }

                // There should be only one singularity deployment fabric config per owner subscription ID
                // Move the call from FindByOwnerSubscriptionId to FindByOwnerSubscriptionIdAndRegion after regional utilities are implemented
                var config = singularityConfigs.First();

                tasks.Add(Task.Run(async () =>
                {
                    var models = new List<TemplatedModelResourceDescription>();
                    try
                    {
                        // Get the singularity client
                        var client = await _singularityClientFactory.GetSingularityClientAsync(config).ConfigureAwait(false);
                        ResourceArmScope accountArmScope = _singularityClientFactory.ParseArmId(config.AccountArmId);
                        string? localContinuationToken = continuationToken;

                        do
                        {
                            var templatedModels = string.IsNullOrEmpty(localContinuationToken) ?
                                await client.TemplatedModel.ListByAccountAsync(accountArmScope.ResourceGroup, accountArmScope.ResourceName).ConfigureAwait(false) :
                                await client.TemplatedModel.ListByAccountNextAsync(localContinuationToken).ConfigureAwait(false);

                            models.AddRange(templatedModels);
                            localContinuationToken = templatedModels.NextPageLink;
                        }
                        while (!string.IsNullOrEmpty(localContinuationToken));

                        _logger.LogInformation("Successfully retrieved model deployments for singularity account {AccountArmId} in region {Region}",
                            config.AccountArmId, config.ClusterRegion);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error retrieving model deployments for singularity account {AccountArmId} in region {Region}",
                            config.AccountArmId, config.ClusterRegion);
                        exceptions.Add(ex);
                    }

                    return models;
                }));
            }

            var results = await Task.WhenAll(tasks);

            // Throw if any task failed
            if (exceptions.Any())
            {
                throw new AggregateException("One or more tasks failed while retrieving model deployments.", exceptions);
            }

            templatedModelsList = results.SelectMany(r => r).ToList();

            // Convert templatedModelsList to ModelDeploymentDto 
            var modelDeployments = templatedModelsList.Select(templatedModel => templatedModel.ConvertToModelDeploymentDto()).ToList();

            return modelDeployments;
        }

        public async Task ModifyModelDeploymentAllocationAsync(
            string modelDeploymentName,
            ModelDeploymentAllocationActionDto modelDeploymentAllocationAction,
            Guid ownerSubscriptionId = default)
        {
            // todo shail check with model pool if the ownerSubscriptionId could be provided during allocation actions
            // until then it will be fetched from the AOAI deployment configuration
            var singularityConfig = GetSingularityConfig(ownerSubscriptionId);

            AllocationMessageBase allocationMessage;
            string actionLower = modelDeploymentAllocationAction.Action.ToLowerInvariant();

            switch (actionLower)
            {
                case "attach":
                    allocationMessage = new AttachMessage
                    {
                        InferenceModelResourceUri = modelDeploymentAllocationAction.InferenceModelResourceUri,
                        CapacityUnitsPerModality = modelDeploymentAllocationAction.CapacityUnitsPerModality?
                            .ToDictionary(
                                kvp => kvp.Key,
                                kvp => new Types.Allocation.CapacityUnits
                                {
                                    PTU = kvp.Value.PTU,
                                    SkuFamily = kvp.Value.SkuFamily
                                })
                    };
                    break;
                case "detach":
                    allocationMessage = new DetachMessage
                    {
                        InferenceModelResourceUri = modelDeploymentAllocationAction.InferenceModelResourceUri,
                    };
                    break;
                default:
                    throw new NotImplementedException($"Allocation action {modelDeploymentAllocationAction.Action} is not implemented.");
            }

            var redisClient = await GetRedisClient(singularityConfig.IPPublisherType).ConfigureAwait(false);

            await AllocationMessageHelper.SendAllocationMessageAsync(modelDeploymentName, allocationMessage, redisClient).ConfigureAwait(false);
        }

        private SingularityDeploymentFabricConfiguration GetSingularityConfig(Guid ownerSubscriptionId = default)
        {
            var config = _singularityDeploymentFabricConfigService
                .Find(new ClusterSelector
                {
                    OwnerSubscriptionId = ownerSubscriptionId,
                    IsModelPoolOnboarded = true,
                }).FirstOrDefault();

            if (config == null)
            {
                throw new InvalidOperationException($"No Singularity deployment fabric configuration found for owner subscription ID {ownerSubscriptionId} or AOAI deployment configuration.");
            }

            _logger.LogInformation("Using Singularity deployment fabric configuration: {ClusterName} for owner subscription ID {OwnerSubscriptionId} and region {Region}",
                config.ClusterName, ownerSubscriptionId, config.ClusterRegion);

            return config;
        }

        private Task<IDatabase> GetRedisClient(IPPublisherType ippType)
        {
            return ippType switch
            {
                IPPublisherType.MaaS_IPP => _redisClientMaaS,
                IPPublisherType.AOAI_IPP => _redisClientAOAI,
                _ => throw new InvalidOperationException($"Unsupported IP publisher type: {ippType}")
            };
        }
    }
}
