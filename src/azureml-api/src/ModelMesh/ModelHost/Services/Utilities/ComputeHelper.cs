﻿// <copyright file="MIRModelUtilities.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using Microsoft.Extensions.Logging;
using Microsoft.MachineLearning.Common.Core.Utilities;

namespace Microsoft.MachineLearning.ModelMesh.ModelHost.Services.Clients.Singularity
{
    public static class ComputeHelper
    {
        private const string SingularitySkuOverridePrefix = "Singularity_SKU_Override#";
        private const string SingularityIBCapacityPlacementOptOutSuffix = "#IBCapacityPlacementOptOut";

        /// <summary>
        /// Override the sku string based on user deployment property. This is for presetv2 deployments.
        /// </summary>
        /// <param name="vmSku">User provided sku.</param>
        /// <param name="skuReference">Sku reference from template.</param>
        /// <param name="skuFromTemplate">Sku if using template only.</param>
        /// <param name="properties">Deployment properties.</param>
        /// <param name="logger">Logger.</param>
        /// <returns>Overidden sku.</returns>
        public static string OverrideSkuBasedOnPropertiesPresetV3(string vmSku, string skuReference, string skuFromTemplate, IDictionary<string, string> properties, ILogger logger)
        {
            if (properties.IsNullOrEmpty())
            {
                return skuFromTemplate;
            }

            var overrideTags = properties.Where(kvp => kvp.Key.StartsWith(SingularitySkuOverridePrefix, ignoreCase: true, CultureInfo.InvariantCulture))
                .ToDictionary(kvp => kvp.Key, kvp => kvp.Value);

            var skuOverrides = new CaseInsensitiveDictionary<string>(overrideTags);
            var skuOverrideKey = SingularitySkuOverridePrefix + vmSku + "#" + skuReference;

            if (skuOverrides.ContainsKey(skuOverrideKey))
            {
                logger.LogInformation($"Override SKU from {vmSku} to {skuOverrides[skuOverrideKey]}");

                return skuOverrides[skuOverrideKey];
            }

            return skuFromTemplate;
        }

        /// <summary>
        /// Override the sku string based on user deployment property. This is for presetv2 deployments.
        /// </summary>
        /// <param name="vmSku">User provided sku.</param>
        /// <param name="skuReference">Sku reference.</param>
        /// <param name="properties">Deployment properties.</param>
        /// <param name="logger">Logger.</param>
        /// <returns>Overidden sku.</returns>
        public static bool GetSkuIBPlacementOptOutFlagBasedOnPropertiesPresetV3(string vmSku, string skuReference, IDictionary<string, string> properties, ILogger logger)
        {
            if (properties.IsNullOrEmpty())
            {
                return false;
            }

            var overrideTags = properties.Where(kvp => kvp.Key.StartsWith(SingularitySkuOverridePrefix, ignoreCase: true, CultureInfo.InvariantCulture))
                .ToDictionary(kvp => kvp.Key, kvp => kvp.Value);

            var skuOverrides = new CaseInsensitiveDictionary<string>(overrideTags);
            var skuOverrideKey = SingularitySkuOverridePrefix + vmSku + "#" + skuReference + SingularityIBCapacityPlacementOptOutSuffix;

            var ibOptOut = GetBooleanFromDictionary(skuOverrides, skuOverrideKey);
            logger.LogInformation($"IB placement opt out for sku {vmSku}: {ibOptOut}");
            return ibOptOut;
        }

        /// <summary>
        /// Override the sku string based on user deployment property. This is for presetv2 deployments.
        /// </summary>
        /// <param name="vmSku">User provided sku.</param>
        /// <param name="properties">Deployment properties.</param>
        /// <param name="logger">Logger.</param>
        /// <returns>Overidden sku.</returns>
        public static string OverrideSkuBasedOnProperties(string vmSku, IDictionary<string, string> properties, ILogger logger)
        {
            if (properties.IsNullOrEmpty())
            {
                return vmSku;
            }

            var overrideTags = properties.Where(kvp => kvp.Key.StartsWith(SingularitySkuOverridePrefix, ignoreCase: true, CultureInfo.InvariantCulture))
                .ToDictionary(kvp => kvp.Key.Substring(SingularitySkuOverridePrefix.Length), kvp => kvp.Value);

            var skuOverrides = new CaseInsensitiveDictionary<string>(overrideTags);

            if (skuOverrides.ContainsKey(vmSku))
            {
                if (logger != null)
                {
                    logger.LogInformation($"Override SKU from {vmSku} to {skuOverrides[vmSku]}");
                }

                return skuOverrides[vmSku];
            }

            return vmSku;
        }

        /// <summary>
        /// Gets the value of the key provided.
        /// </summary>
        /// <param name="properties">these are expected to be properties.</param>
        /// <param name="key">.</param>
        /// <returns>boolean whether a property is enabled.</returns>
        private static bool GetBooleanFromDictionary(IDictionary<string, string> properties, string key)
        {
            return properties == null ? false : properties.TryGetValue(key, out var value) && bool.TryParse(value, out bool isEnabled) && isEnabled;
        }
    }
}