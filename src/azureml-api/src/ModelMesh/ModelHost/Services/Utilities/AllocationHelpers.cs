﻿// <copyright file="AllocationHelpers.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.MachineLearning.ModelMesh.ModelHost.Types.Allocation;
using StackExchange.Redis;

namespace Microsoft.MachineLearning.ModelMesh.ModelHost.Services.Utilities;

public static class AllocationMessageVersionHelper
{
    public static string GetControlInfoKey(string deploymentName)
    {
        return $"allocation.controlInfo.{deploymentName}";
    }

    public static string GetHeartBeatStatusInfoKey(string deploymentName)
    {
        return $"heartbeat.redis.h-{deploymentName}";
    }
}

public static class AllocationMessageHelper
{
    public async static Task<AllocationMessageBase?> GetLastAllocationMessageAsync(
        string deploymentName,
        IDatabase redisClient)
    {
        // Should be equivalent to `XREVRANGE {stream} + - COUNT 1`, and should fetch the latest message in the stream.
        // This is important because the stream trimming operation sets the max length to _approximately_ 1 for performance reasons, so the first message from the start is not guaranteed to be the latest message.
        StreamEntry[] entries = await redisClient
            .StreamRangeAsync(AllocationMessageVersionHelper.GetControlInfoKey(deploymentName), messageOrder: Order.Descending, count: 1)
            .ConfigureAwait(false);

        if (entries.Length > 0)
        {
            var valueDict = entries[0].Values.ToDictionary(
                nameValue => nameValue.Name.ToString(),
                nameValue => nameValue.Value.ToString()
            );

            if (valueDict.TryGetValue(Types.Constants.AllocationEntryName, out var allocationJson) && allocationJson is string)
            {
                try
                {
                    using (JsonDocument doc = JsonDocument.Parse(allocationJson))
                    {
                        string? typeValue = doc.RootElement.GetProperty("action").GetString();

                        switch (typeValue)
                        {
                            case "Attach":
                                var attachMessage = JsonSerializer.Deserialize<AttachMessage>(allocationJson);

                                return attachMessage;
                            case "Detach":
                                var detachMessage = JsonSerializer.Deserialize<DetachMessage>(allocationJson);

                                return detachMessage;
                            default:
                                throw new NotImplementedException($"Unrecogized message type {typeValue}.");
                        }
                    }
                }
                catch (Exception ex)
                {
                    throw new InvalidOperationException($"Failed to deserialize last allocation message: {allocationJson}. Reason: {ex.Message}");
                }
            }
            else
            {
                throw new InvalidOperationException($"The entry key '{Types.Constants.AllocationEntryName}' was not found in the allocation stream.");
            }
        }
        else
        {
            return null;
        }
    }

    public async static Task SendAllocationMessageAsync(
        string deploymentName,
        AllocationMessageBase allocationMessage,
        IDatabase redisClient)
    {
        var message = JsonSerializer.Serialize(allocationMessage);
        await redisClient
            .StreamAddAsync(
                AllocationMessageVersionHelper.GetControlInfoKey(deploymentName),
                [new NameValueEntry(Types.Constants.AllocationEntryName, message)],
                maxLength: 1,
                useApproximateMaxLength: true)
            .ConfigureAwait(false);
    }
}