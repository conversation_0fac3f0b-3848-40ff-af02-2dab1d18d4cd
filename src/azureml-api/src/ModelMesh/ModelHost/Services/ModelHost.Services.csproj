﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <AssemblyName>Microsoft.MachineLearning.ModelMesh.ModelHost.Services</AssemblyName>
    <RootNamespace>Microsoft.MachineLearning.ModelMesh.ModelHost.Services</RootNamespace>
    <PackageId>Microsoft.MachineLearning.ModelMesh.ModelHost.Services</PackageId>
    <Product>Microsoft.MachineLearning.ModelMesh.ModelHost.Services</Product>
    <IsPublishable>false</IsPublishable>
  </PropertyGroup>
  <PropertyGroup>
    <NoWarn>$(NoWarn);NU1605</NoWarn>
  </PropertyGroup>
  <PropertyGroup>
    <Nullable>enable</Nullable>
    <LangVersion>latest</LangVersion>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Azure.Core" />
    <PackageReference Include="Azure.ResourceManager.MachineLearning" />
    <PackageReference Include="Azure.ResourceManager.Network" />
    <PackageReference Include="Azure.ResourceManager.Resources" />
    <PackageReference Include="Azure.ResourceManager.ManagedServiceIdentities" VersionOverride="1.2.3" />
    <PackageReference Include="KubernetesClient" VersionOverride="16.0.2" />
    <PackageReference Include="Microsoft.AIPlatform.WorkloadIdentity" />
    <PackageReference Include="Microsoft.Azure.Management.Singularity" VersionOverride="1.1.31407780" />
    <PackageReference Include="Microsoft.Extensions.Caching.Memory" />
    <PackageReference Include="Microsoft.Skype.ECS.Client" VersionOverride="20.2.0.1460" />
    <PackageReference Include="Microsoft.Skype.ECS.Client.Authentication" VersionOverride="20.2.0.1460" />
    <PackageReference Include="Hangfire.AspNetCore" VersionOverride="1.8.20" />
    <PackageReference Include="Hangfire.MemoryStorage" VersionOverride="1.8.0" />
    <PackageReference Include="Hangfire.AzureCosmosDB" VersionOverride="2.0.8" />
    <PackageReference Include="Hangfire.Redis.StackExchange" VersionOverride="1.12.0" />
    <PackageReference Update="StackExchange.Redis" VersionOverride="2.8.37" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="$(SrcRoot)\Common\Configuration\Common.Configuration.csproj" />
    <ProjectReference Include="$(SrcRoot)\Common\DocumentDb\Common.DocumentDb.csproj" />
    <ProjectReference Include="$(SrcRoot)\Common\WebApi.TeamAccounts\Common.WebApi.TeamAccounts.csproj" />
    <ProjectReference Include="$(SrcRoot)\ModelMesh\ModelHost\Types\ModelHost.Types.csproj" />
    <ProjectReference Include="$(SrcRoot)\ModelMesh\Shared\Common\Shared.Common.csproj" />
    <ProjectReference Include="$(SrcRoot)\ModelMesh\ModelHost\Contracts\ModelHost.Contracts.csproj" />
    <ProjectReference Include="$(SrcRoot)\IntellectualPropertyPublisher\Contracts\IntellectualPropertyPublisher.Contracts.csproj" />
    <ProjectReference Include="$(SrcRoot)\ModelRegistry\Contracts\ModelRegistry.Contracts.csproj" />
  </ItemGroup>
</Project>
