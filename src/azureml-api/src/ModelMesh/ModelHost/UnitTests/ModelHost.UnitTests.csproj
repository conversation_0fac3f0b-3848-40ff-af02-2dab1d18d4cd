<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <AssemblyName>Microsoft.MachineLearning.ModelMesh.ModelHost.UnitTests</AssemblyName>
    <RootNamespace>Microsoft.MachineLearning.ModelMesh.ModelHost.UnitTests</RootNamespace>
    <PackageId>Microsoft.MachineLearning.ModelMesh.ModelHost.UnitTests</PackageId>
    <IsPublishable>false</IsPublishable>
  </PropertyGroup>

  <PropertyGroup>
    <Nullable>enable</Nullable>
    <LangVersion>latest</LangVersion>
  </PropertyGroup>

  <ItemGroup>
    <Content Include="$(SrcRoot)\ModelMesh\ModelHost\EntryPoints\appsettings.Development.json" Link="appsettings.Development.json" CopyToOutputDirectory="PreserveNewest" />
    <Content Include="$(SrcRoot)\ModelMesh\ModelHost\EntryPoints\appsettings.json" Link="appsettings.json" CopyToOutputDirectory="PreserveNewest" />
    <Content Include="$(SrcRoot)\ModelMesh\ModelHost\EntryPoints\appsettings\appsettings.override.json" Link="appsettings.override.json" Condition="Exists('$(SrcRoot)\ModelMesh\ModelHost\EntryPoints\appsettings\appsettings.override.json')" CopyToOutputDirectory="PreserveNewest" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.NET.Test.Sdk" />
    <PackageReference Include="xunit" />
    <PackageReference Include="xunit.runner.visualstudio" />
    <PackageReference Include="Microsoft.AspNetCore.TestHost" />
    <PackageReference Include="FluentAssertions" />
    <PackageReference Include="FakeItEasy" />
    <PackageReference Update="Microsoft.IdentityModel.JsonWebTokens" VersionOverriden="8.3.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="$(SrcRoot)\Common\Testing\Common.Testing.csproj" />
    <ProjectReference Include="$(SrcRoot)\ModelMesh\ModelHost\Types\ModelHost.Types.csproj" />
    <ProjectReference Include="$(SrcRoot)\ModelMesh\ModelHost\Services\ModelHost.Services.csproj" />
    <ProjectReference Include="$(SrcRoot)\ModelMesh\ModelHost\EntryPoints\ModelHost.EntryPoints.csproj" />
  </ItemGroup>
</Project>
