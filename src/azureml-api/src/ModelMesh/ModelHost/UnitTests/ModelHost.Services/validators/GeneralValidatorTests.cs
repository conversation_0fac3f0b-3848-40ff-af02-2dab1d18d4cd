﻿// <copyright file="GeneralValidatorTests.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Text;
using Microsoft.MachineLearning.Common.Testing;
using Microsoft.MachineLearning.ModelMesh.ModelHost.Services.Validators;
using Microsoft.MachineLearning.ModelMesh.ModelHost.Types.ConfigurationDocuments.Common;
using Microsoft.MachineLearning.ModelMesh.ModelHost.Types.ConfigurationDocuments.ModelInstance;
using Xunit;

namespace Microsoft.MachineLearning.ModelMesh.ModelHost.UnitTests.ModelHost.Services.validators
{
    [Trait(nameof(TestCategory), TestCategory.Unit)]
    public class GeneralValidatorTests
    {
        private readonly GeneralValidator _validator;

        public GeneralValidatorTests()
        {
            _validator = new GeneralValidator();
        }

        [Fact]
        public void ValidateEndpointTests()
        {
            var exceptions = new List<Exception>();

            var propertyName = "propertyName";
            var validEndpointConfiguration_1 = new EndpointConfiguration("scheme", "path", 123);
            var validEndpointConfiguration_2 = new EndpointConfiguration("scheme", "", 123);

            _validator.ValidateEndpoint(ref exceptions, validEndpointConfiguration_1, propertyName);
            _validator.ValidateEndpoint(ref exceptions, validEndpointConfiguration_2, propertyName);
            Assert.Empty(exceptions);

            var invalidEndpointConfiguration_1 = new EndpointConfiguration("", "path", 123);
            var invalidEndpointConfiguration_2= new EndpointConfiguration("scheme", "path", -123);
            _validator.ValidateEndpoint(ref exceptions, invalidEndpointConfiguration_1, propertyName);
            _validator.ValidateEndpoint(ref exceptions, invalidEndpointConfiguration_2, propertyName);
            Assert.Equal(2, exceptions.Count);
        }

        [Fact]
        public void ValidateInferenceEndpointsTests()
        {
            var exceptions = new List<Exception>();

            var propertyName = "propertyName";
            var validInferenceEndpointConfigs = new List<InferenceEndpointConfiguration>
            {
                new InferenceEndpointConfiguration("route", "scheme", "path", 123, InferenceContractType.CommonApiEmbeddings),
                new InferenceEndpointConfiguration("", "scheme", "path", 123, InferenceContractType.CommonApiEmbeddings),
                new InferenceEndpointConfiguration("route", "scheme", "", 123, InferenceContractType.CommonApiEmbeddings),
            };
            _validator.ValidateInferenceEndpoints(ref exceptions, validInferenceEndpointConfigs, propertyName);
            Assert.Empty(exceptions);

            var invalidInferenceEndpointConfigs = new List<InferenceEndpointConfiguration>
            {
                new InferenceEndpointConfiguration("route", "", "path", 123, InferenceContractType.CommonApiEmbeddings),
                new InferenceEndpointConfiguration("route", "scheme", "path", -123, InferenceContractType.CommonApiEmbeddings),
            };
            _validator.ValidateInferenceEndpoints(ref exceptions, invalidInferenceEndpointConfigs, propertyName);
            Assert.Equal(2, exceptions.Count);
        }

        [Fact]
        public void ValidateModelIdTests()
        {
            var exceptions = new List<Exception>();

            var validModelId = "azureml://registries/testRegistry/models/testModelName";
            _validator.ValidateModelId(ref exceptions, validModelId);
            Assert.Empty(exceptions);
            var invalidModelId_1 = "";
            _validator.ValidateModelId(ref exceptions, invalidModelId_1);
            Assert.Contains(exceptions, ex => ex.Message.Contains("modelId cannot be null or whitespace."));
            var invalidModelId_2 = "test://registries";
            _validator.ValidateModelId(ref exceptions, invalidModelId_2);
            Assert.Contains(exceptions, ex => ex.Message.Contains("Malformed model ID"));
        }

        [Fact]
        public void ValidateModelRegistryAssetsTests()
        {
            var exceptions = new List<Exception>();

            var validModelRegistryAssets = new Dictionary<string, ModelRegistryStorageInfo>
            {
                ["asset1"] = new ModelRegistryStorageInfo(Guid.NewGuid(), "rg", "storageAccount", "container", "manifestFilePath", "/mount/path", false),
                ["asset2"] = new ModelRegistryStorageInfo(Guid.NewGuid(), "rg", "storageAccount", "container", "manifestFilePath", "/", true),
                ["asset3"] = new ModelRegistryStorageInfo(Guid.NewGuid(), "rg", "storageAccount", "container", "/", "path", true),
            };
            _validator.ValidateModelRegistryAssets(ref exceptions, validModelRegistryAssets);
            Assert.Empty(exceptions);

            var invalidModelRegistryAssets = new Dictionary<string, ModelRegistryStorageInfo>
            {
                ["asset1"] = new ModelRegistryStorageInfo(Guid.NewGuid(), "", "storageAccount", "container", "manifestFilePath", "/mount/path", true),
                ["asset2"] = new ModelRegistryStorageInfo(Guid.NewGuid(), "rg", "", "container", "manifestFilePath", "path", true),
                ["asset3"] = new ModelRegistryStorageInfo(Guid.NewGuid(), "rg", "storageAccount", "", "manifestFilePath", "path", true),
                ["asset4"] = new ModelRegistryStorageInfo(Guid.NewGuid(), "rg", "storageAccount", "container", "", "path", true),
                ["asset5"] = new ModelRegistryStorageInfo(Guid.NewGuid(), "rg", "storageAccount", "container", "manifestFilePath", "", true),
            };
            _validator.ValidateModelRegistryAssets(ref exceptions, invalidModelRegistryAssets);
            Assert.Equal(5, exceptions.Count);
        }

        [Fact]
        public void ValidateUserAssignedIdentityTests()
        {
            var exceptions = new List<Exception>();
            UserAssignedIdentity userAssignedIdentity = new UserAssignedIdentity("resourceId", Guid.NewGuid(), Guid.NewGuid());
            _validator.ValidateUserAssignedIdentity(ref exceptions, userAssignedIdentity);
            Assert.Empty(exceptions);
        }

        [Fact]
        public void ValidateNamingTests()
        {
            var propertyName = "propertyName";
            var validName = "valid.Name_1-1";
            var exceptions = new List<Exception>();
            var result = _validator.ValidateNaming(ref exceptions, propertyName, validName);
            Assert.True(result);
            Assert.Empty(exceptions);

            var invalidName_1 = string.Empty;
            result = _validator.ValidateNaming(ref exceptions, propertyName, invalidName_1);
            Assert.False(result);
            Assert.Single(exceptions);
            Assert.Contains(exceptions, ex => ex.Message.Contains("propertyName cannot be null or whitespace."));
            var invalidName_2 = "invalid-name!"; // Included not supported character
            result = _validator.ValidateNaming(ref exceptions, propertyName, invalidName_2);
            Assert.False(result);
            Assert.Contains(exceptions, ex => ex.Message.Contains("propertyName need to be (alphanumeric, ., -, _) only, and should start and end with alphanumeric,"));
            var invalidName_3 = "-invalid-name"; // Not start with alphanumeric
            result = _validator.ValidateNaming(ref exceptions, propertyName, invalidName_3);
            Assert.False(result);
            var invalidName_4 = "invalid-name."; // Not end with alphanumeric
            result = _validator.ValidateNaming(ref exceptions, propertyName, invalidName_4);
            Assert.False(result);
            Assert.Equal(4, exceptions.Count);
        }

        [Fact]
        public void ValidateStringTests()
        {
            var propertyName = "propertyName";
            var validString = "valid.String_1-1!@";
            var exceptions = new List<Exception>();
            _validator.ValidateString(ref exceptions, propertyName, validString);
            Assert.Empty(exceptions);
            StringBuilder longString = new StringBuilder();
            for (int i = 0; i < 256; i++)
            {
                longString.Append("a");
            }
            _validator.ValidateString(ref exceptions, propertyName, longString.ToString());
            Assert.Empty(exceptions);

            longString.Append("a"); // longer than 256
            _validator.ValidateString(ref exceptions, propertyName, longString.ToString());
            Assert.Single(exceptions);
            Assert.Contains(exceptions, ex => ex.Message.Contains("propertyName length need to be within [1, 256),"));

            var invalidString = string.Empty;
            _validator.ValidateString(ref exceptions, propertyName, invalidString);
            Assert.Contains(exceptions, ex => ex.Message.Contains("propertyName cannot be null or whitespace."));
        }

        [Fact]
        public void ValidatePathTests()
        {
            var propertyName = "propertyName";
            var validPath = "valid/Path1/va-lid/pa_th";
            var exceptions = new List<Exception>();
            _validator.ValidatePath(ref exceptions, propertyName, validPath);
            Assert.Empty(exceptions);

            var invalidPath_1 = string.Empty;
            _validator.ValidatePath(ref exceptions, propertyName, invalidPath_1);
            Assert.Contains(exceptions, ex => ex.Message.Contains("propertyName cannot be null or whitespace."));

            var invalidPath_2 = "/invalid/path@";
            _validator.ValidatePath(ref exceptions, propertyName, invalidPath_2);
            Assert.Contains(exceptions, ex => ex.Message.Contains("propertyName need to be valid that includes (alphanumeric, ., -, _, /) only,"));
        }

        [Fact]
        public void ValidateRouteTests()
        {
            var propertyName = "propertyName";
            var validRoute_1 = "valid/Path.1/va-lid/pa_th";
            var exceptions = new List<Exception>();
            _validator.ValidateRoute(ref exceptions, propertyName, validRoute_1);
            Assert.Empty(exceptions);
            var validRoute_2 = string.Empty;
            _validator.ValidateRoute(ref exceptions, propertyName, validRoute_2);
            Assert.Empty(exceptions);

            var invalidRoute = "/invalid/path:";
            _validator.ValidateRoute(ref exceptions, propertyName, invalidRoute);
            Assert.Contains(exceptions, ex => ex.Message.Contains("propertyName need to be valid that includes (alphanumeric, ., -, _, /) only,"));
        }

        [Fact]
        public void ValidateManifestFilePathTests()
        {
            var propertyName = "propertyName";
            var validManifestFilePath_1 = "valid/Path.1/va-lid/pa_th";
            var exceptions = new List<Exception>();
            _validator.ValidateManifestFilePath(ref exceptions, propertyName, validManifestFilePath_1);
            Assert.Empty(exceptions);

            var validManifestFilePath_2 = "/valid/path:";
            _validator.ValidateManifestFilePath(ref exceptions, propertyName, validManifestFilePath_2);
            Assert.Empty(exceptions);

            var invalidManifestFilePath_1 = string.Empty;
            _validator.ValidateManifestFilePath(ref exceptions, propertyName, invalidManifestFilePath_1);
            Assert.Contains(exceptions, ex => ex.Message.Contains("propertyName cannot be null or whitespace."));

            var invalidManifestFilePath_2 = "/invalid/path@";
            _validator.ValidateManifestFilePath(ref exceptions, propertyName, invalidManifestFilePath_2);
            Assert.Contains(exceptions, ex => ex.Message.Contains("propertyName need to be valid that includes (alphanumeric, ., -, _, /, :) only,"));
        }

        [Fact]
        public void ValidateIpAddressesTests()
        {
            List<string> validIPs = new List<string>
            {
                "127.0.0.1",
                "*************"
            };
            var exceptions = new List<Exception>();
            _validator.ValidateIpAddresses(ref exceptions, validIPs);
            Assert.Empty(exceptions);

            List<string> invalidIPs = new List<string>
            {
                "2001:0DB8:0000:0000:02AA:00FF:C0A8:640A",
                string.Empty,
                "a.b.c.d"
            };
            _validator.ValidateIpAddresses(ref exceptions, invalidIPs);
            Assert.Contains(exceptions, ex => ex.Message.Contains($"IP address a.b.c.d is not valid."));
            Assert.Equal(3, exceptions.Count);
        }
    }
}
