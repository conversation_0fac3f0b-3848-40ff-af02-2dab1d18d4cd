﻿// <copyright file="SidecarContainerValidatorTests.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using k8s.Models;
using k8s;
using Microsoft.MachineLearning.Common.Testing;
using Microsoft.MachineLearning.ModelMesh.ModelHost.Services.Validators;
using Xunit;
using System.Collections.Generic;
using System;

namespace Microsoft.MachineLearning.ModelMesh.ModelHost.UnitTests.ModelHost.Services.validators
{
    [Trait(nameof(TestCategory), TestCategory.Unit)]
    public class SidecarContainerValidatorTests
    {
        [Fact]
        public void ValidateSidecarContainerSpecTests()
        {
            var containerSpecName = "containerSpecName";
            var validHttpGet = new V1HTTPGetAction()
            {
                Scheme = "http",
                Path = "path",
                Port = 123
            };
            var validProbe = new V1Probe() { HttpGet = validHttpGet };

            var validContainer = new V1Container()
            {
                Name = "model_container",
                Image = "test_image",
                Command = new List<string> { "test" },
                StartupProbe = validProbe,
                ReadinessProbe = validProbe,
                LivenessProbe = validProbe,
            };
            var validContainerString = KubernetesYaml.Serialize(validContainer);
            SidecarContainerValidator.ValidateSidecarContainerSpec(validContainerString, containerSpecName);

            Assert.Throws<ArgumentNullException>(() => SidecarContainerValidator.ValidateSidecarContainerSpec(string.Empty, containerSpecName));

            var formatEx = Assert.Throws<FormatException>(() => SidecarContainerValidator.ValidateSidecarContainerSpec("wrong spec", containerSpecName));
            Assert.Contains($"Failed to parse {containerSpecName}.", formatEx.Message);

            var invalidContainer = new V1Container()
            {
                Name = "model_container",
                //Image = "test_image",
                Command = new List<string> { "test" },
                StartupProbe = validProbe,
                ReadinessProbe = validProbe,
                LivenessProbe = validProbe,
            };
            var invalidContainerString = KubernetesYaml.Serialize(invalidContainer);
            var ex = Assert.Throws<ArgumentNullException>(() => SidecarContainerValidator.ValidateSidecarContainerSpec(invalidContainerString, containerSpecName));
            Assert.Contains($"{containerSpecName}'s containerSpec.Image", ex.Message);

            invalidContainer = new V1Container()
            {
                Name = "model_container",
                Image = "test_image",
                Command = new List<string> { "test" },
                StartupProbe = validProbe,
                ReadinessProbe = validProbe,
                LivenessProbe = validProbe,
            };
            invalidContainer.Image = "test";
            invalidContainer.Command = null;
            invalidContainerString = KubernetesYaml.Serialize(invalidContainer);
            ex = Assert.Throws<ArgumentNullException>(() => SidecarContainerValidator.ValidateSidecarContainerSpec(invalidContainerString, containerSpecName));
            Assert.Contains($"{containerSpecName}'s containerSpec.Command", ex.Message);

            invalidContainer.Command = new List<string> { "test" };
            invalidContainer.StartupProbe = null;
            invalidContainerString = KubernetesYaml.Serialize(invalidContainer);
            ex = Assert.Throws<ArgumentNullException>(() => SidecarContainerValidator.ValidateSidecarContainerSpec(invalidContainerString, containerSpecName));
            Assert.Contains($"{containerSpecName}'s containerSpec.StartupProbe", ex.Message);

            invalidContainer.StartupProbe = validProbe;
            invalidContainer.LivenessProbe = null;
            invalidContainerString = KubernetesYaml.Serialize(invalidContainer);
            ex = Assert.Throws<ArgumentNullException>(() => SidecarContainerValidator.ValidateSidecarContainerSpec(invalidContainerString, containerSpecName));
            Assert.Contains($"{containerSpecName}'s containerSpec.LivenessProbe", ex.Message);

            invalidContainer.LivenessProbe = validProbe;
            invalidContainer.ReadinessProbe = null;
            invalidContainerString = KubernetesYaml.Serialize(invalidContainer);
            ex = Assert.Throws<ArgumentNullException>(() => SidecarContainerValidator.ValidateSidecarContainerSpec(invalidContainerString, containerSpecName));
            Assert.Contains($"{containerSpecName}'s containerSpec.ReadinessProbe", ex.Message);
        }
    }
}
