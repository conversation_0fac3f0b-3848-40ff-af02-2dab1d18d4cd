﻿// <copyright file="PodTemplateValidatorTests.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using k8s.Models;
using k8s;
using Microsoft.MachineLearning.Common.Testing;
using Microsoft.MachineLearning.ModelMesh.ModelHost.Services.Validators;
using Xunit;
using System.Collections.Generic;

namespace Microsoft.MachineLearning.ModelMesh.ModelHost.UnitTests.ModelHost.Services.validators
{
    [Trait(nameof(TestCategory), TestCategory.Unit)]
    public class PodTemplateValidatorTests
    {
        private static readonly string ValidPodTemplateString = @"
apiVersion: v1
kind: PodTemplate
metadata:
  name: contoso_instance
  namespace: default
template:
  metadata:
    name: contoso_instance
  spec:
    containers:
    - name: model_container
      image: adramadev.azurecr.io/modelmimic:latest
      command: [\""model_mimic\""]
      env:
      - name: MODEL_MIMIC_BENCHMARK_FILE_PATH
        value: /opt/microsoft/model_mimic/test/profiles/updated_benchmark_results_5s_duration_0.5s_stddev.csv
      - name: MODEL_MIMIC_MODEL_NAME
        value: azureml://registries/meshtest/models/mimic
      - name: MODEL_MIMIC_MODEL_TYPE
        value: /chat/completions
      - name: MODEL_MIMIC_SERVE_PORT
        value: 1234
      startupProbe:
        initialDelaySeconds: 30
        successThreshold: 1
        failureThreshold: 3
        periodSeconds: 30
        timeoutSeconds: 10
        httpGet:
          scheme: http
          path: /health/ready
          port: 1234
      readinessProbe:
        httpGet:
          scheme: http
          path: /health/ready
          port: 5678
      livenessProbe:
        httpGet:
          scheme: http
          path: /health/ready
          port: 1234
    - name: inference-server
      image: adramadev.azurecr.io/modelmimic:latest
      command: [\""model_mimic\""]
      env:
      - name: MODEL_MIMIC_BENCHMARK_FILE_PATH
        value: /opt/microsoft/model_mimic/test/profiles/updated_benchmark_results_5s_duration_0.5s_stddev.csv
      - name: MODEL_MIMIC_MODEL_NAME
        value: azureml://registries/meshtest/models/mimic
      - name: MODEL_MIMIC_MODEL_TYPE
        value: /chat/completions
      - name: MODEL_MIMIC_SERVE_PORT
        value: 1234
      startupProbe:
        initialDelaySeconds: 30
        successThreshold: 1
        failureThreshold: 3
        periodSeconds: 30
        timeoutSeconds: 10
        httpGet:
          scheme: http
          path: /health/ready
          port: 1234
      readinessProbe:
        httpGet:
          scheme: http
          path: /health/ready
          port: 5678
      livenessProbe:
        httpGet:
          scheme: https
          path: /health/ready
          port: 1234
";
        [Fact]
        public void ValidatePodTemplateTests()
        {
            Assert.Throws<ArgumentException>(() => PodTemplateValidator.ValidatePodTemplate(string.Empty, true, true));

            var invalidPodTemplate = "invalid_template deserialized failure";
            Assert.Throws<ArgumentException>(() => PodTemplateValidator.ValidatePodTemplate(invalidPodTemplate, true, true));

            string validPodTemplate = ValidPodTemplateString;
            PodTemplateValidator.ValidatePodTemplate(validPodTemplate, true, true);

            var parsedPodTemplate = ResetPodTemplate();
            parsedPodTemplate.Template.Spec = null;
            invalidPodTemplate= KubernetesYaml.Serialize(parsedPodTemplate);
            Assert.Throws<ArgumentNullException>(() => PodTemplateValidator.ValidatePodTemplate(invalidPodTemplate, true, true));

            parsedPodTemplate.Template = null;
            invalidPodTemplate = KubernetesYaml.Serialize(parsedPodTemplate);
            Assert.Throws<ArgumentNullException>(() => PodTemplateValidator.ValidatePodTemplate(invalidPodTemplate, true, true));

            parsedPodTemplate = ResetPodTemplate();
            parsedPodTemplate.Template.Spec.Containers = new List<V1Container>();
            invalidPodTemplate = KubernetesYaml.Serialize(parsedPodTemplate);
            var ex = Assert.Throws<ArgumentException>(() => PodTemplateValidator.ValidatePodTemplate(invalidPodTemplate, true, true));
            Assert.Contains("Pod templates must specify at least one container.", ex.Message);

            var validHttpGet = new V1HTTPGetAction()
            {
                Scheme = "http",
                Path = "path",
                Port = 123
            };
            var validProbe = new V1Probe() { HttpGet = validHttpGet };

            var container_1 = new V1Container()
            {
                Name = "model_container",
                Image = "test_image",
                Command = new List<string> { "test" },
                StartupProbe = validProbe,
                ReadinessProbe = validProbe,
                LivenessProbe = validProbe,
            };
            var container_2 = new V1Container()
            {
                Name = "inference-server",
                Image = "test_image",
                Command = new List<string> { "test" },
                StartupProbe = validProbe,
                ReadinessProbe = validProbe,
                LivenessProbe = validProbe,
            };

            parsedPodTemplate.Template.Spec.Containers.Add(container_1);
            invalidPodTemplate = KubernetesYaml.Serialize(parsedPodTemplate);

            PodTemplateValidator.ValidatePodTemplate(invalidPodTemplate, true, false);

            ex = Assert.Throws<ArgumentException>(() => PodTemplateValidator.ValidatePodTemplate(invalidPodTemplate, true, true));
            Assert.Contains("Container inference-server is not found in podTemplate.Template.Spec.Containers list", ex.Message);

            parsedPodTemplate.Template.Spec.Containers.Add(container_2);
            invalidPodTemplate = KubernetesYaml.Serialize(parsedPodTemplate);
            PodTemplateValidator.ValidatePodTemplate(invalidPodTemplate, true, true);

            parsedPodTemplate.Template.Spec.Containers = new List<V1Container>() { container_1 };
            ex = Assert.Throws<ArgumentException>(() => PodTemplateValidator.ValidatePodTemplate(invalidPodTemplate, false, false));
            Assert.Contains("Expected podTemplate.Template.Spec.Containers[0].Image to be null", ex.Message);

            container_1.Image = null;
            invalidPodTemplate = KubernetesYaml.Serialize(parsedPodTemplate);
            PodTemplateValidator.ValidatePodTemplate(invalidPodTemplate, false, false);

            ex = Assert.Throws<ArgumentNullException>(() => PodTemplateValidator.ValidatePodTemplate(invalidPodTemplate, true, false));
            Assert.Contains("podTemplate.Template.Spec.Containers[0].Image", ex.Message);

            parsedPodTemplate = ResetPodTemplate();
            container_2.Command = null;
            parsedPodTemplate.Template.Spec.Containers = new List<V1Container>() { container_2 };
            invalidPodTemplate = KubernetesYaml.Serialize(parsedPodTemplate);
            ex = Assert.Throws<ArgumentNullException>(() => PodTemplateValidator.ValidatePodTemplate(invalidPodTemplate, true, true));
            Assert.Contains("podTemplate.Template.Spec.Containers[0].Command", ex.Message);

            container_2.Command = new List<string> { "test" };
            container_2.StartupProbe = null;
            invalidPodTemplate = KubernetesYaml.Serialize(parsedPodTemplate);
            ex = Assert.Throws<ArgumentNullException>(() => PodTemplateValidator.ValidatePodTemplate(invalidPodTemplate, true, true));
            Assert.Contains("podTemplate.Template.Spec.Containers[0].StartupProbe", ex.Message);

            container_2.StartupProbe = new V1Probe();
            invalidPodTemplate = KubernetesYaml.Serialize(parsedPodTemplate);
            ex = Assert.Throws<ArgumentException>(() => PodTemplateValidator.ValidatePodTemplate(invalidPodTemplate, true, true));
            Assert.Contains("Expected exactly one of either an HttpGet or Exec configuration in podTemplate.Template.Spec.Containers[0].StartupProbe, found neither.", ex.Message);

            container_2.StartupProbe = new V1Probe() { HttpGet = validHttpGet, Exec = new V1ExecAction()};
            invalidPodTemplate = KubernetesYaml.Serialize(parsedPodTemplate);
            ex = Assert.Throws<ArgumentException>(() => PodTemplateValidator.ValidatePodTemplate(invalidPodTemplate, true, true));
            Assert.Contains("Expected exactly one of either an HttpGet or Exec configuration in podTemplate.Template.Spec.Containers[0].StartupProbe, found both.", ex.Message);

            var invalidHttpGet = new V1HTTPGetAction() { Port = 1234 };
            container_2.StartupProbe = new V1Probe() { HttpGet = invalidHttpGet };
            invalidPodTemplate = KubernetesYaml.Serialize(parsedPodTemplate);
            ex = Assert.Throws<ArgumentNullException>(() => PodTemplateValidator.ValidatePodTemplate(invalidPodTemplate, true, true));
            Assert.Contains("podTemplate.Template.Spec.Containers[0].StartupProbe.HttpGet.Path", ex.Message);

            invalidHttpGet.Path = "test";
            container_2.StartupProbe = new V1Probe() { HttpGet = invalidHttpGet };
            invalidPodTemplate = KubernetesYaml.Serialize(parsedPodTemplate);
            ex = Assert.Throws<ArgumentNullException>(() => PodTemplateValidator.ValidatePodTemplate(invalidPodTemplate, true, true));
            Assert.Contains("podTemplate.Template.Spec.Containers[0].StartupProbe.HttpGet.Scheme", ex.Message);

            invalidHttpGet.Scheme = "non-http";
            container_2.StartupProbe = new V1Probe() { HttpGet = invalidHttpGet };
            invalidPodTemplate = KubernetesYaml.Serialize(parsedPodTemplate);
            ex = Assert.Throws<ArgumentException>(() => PodTemplateValidator.ValidatePodTemplate(invalidPodTemplate, true, true));
            Assert.Contains("Expected exactly one of either an Http or Https scheme in podTemplate.Template.Spec.Containers[0].StartupProbe.HttpGet.Scheme", ex.Message);

            container_2.StartupProbe = validProbe;
            validProbe.Grpc = new V1GRPCAction();
            invalidPodTemplate = KubernetesYaml.Serialize(parsedPodTemplate);
            ex = Assert.Throws<ArgumentException>(() => PodTemplateValidator.ValidatePodTemplate(invalidPodTemplate, true, true));
            Assert.Contains("Grpc configurations in startup probes are not supported: podTemplate.Template.Spec.Containers[0].StartupProbe", ex.Message);

            validProbe.Grpc = null;
            validProbe.TcpSocket = new V1TCPSocketAction() { Port = 123 };
            invalidPodTemplate = KubernetesYaml.Serialize(parsedPodTemplate);
            ex = Assert.Throws<ArgumentException>(() => PodTemplateValidator.ValidatePodTemplate(invalidPodTemplate, true, true));
            Assert.Contains("TcpSocket configurations in startup probes are not supported: podTemplate.Template.Spec.Containers[0].StartupProbe", ex.Message);

            validProbe.TcpSocket = null;
            container_2.ReadinessProbe = null;
            invalidPodTemplate = KubernetesYaml.Serialize(parsedPodTemplate);
            ex = Assert.Throws<ArgumentNullException>(() => PodTemplateValidator.ValidatePodTemplate(invalidPodTemplate, true, true));
            Assert.Contains("podTemplate.Template.Spec.Containers[0].ReadinessProbe", ex.Message);

            container_2.ReadinessProbe = new V1Probe() { HttpGet = validHttpGet, Exec = new V1ExecAction() };
            invalidPodTemplate = KubernetesYaml.Serialize(parsedPodTemplate);
            ex = Assert.Throws<ArgumentException>(() => PodTemplateValidator.ValidatePodTemplate(invalidPodTemplate, true, true));
            Assert.Contains("Expected exactly one of either an HttpGet or Exec configuration in podTemplate.Template.Spec.Containers[0].ReadinessProbe, found both.", ex.Message);

            invalidHttpGet = new V1HTTPGetAction() { Port = 1234 };
            container_2.ReadinessProbe = new V1Probe() { HttpGet = invalidHttpGet };
            invalidPodTemplate = KubernetesYaml.Serialize(parsedPodTemplate);
            ex = Assert.Throws<ArgumentNullException>(() => PodTemplateValidator.ValidatePodTemplate(invalidPodTemplate, true, true));
            Assert.Contains("podTemplate.Template.Spec.Containers[0].ReadinessProbe.HttpGet.Path", ex.Message);

            invalidHttpGet.Path = "test";
            container_2.ReadinessProbe = new V1Probe() { HttpGet = invalidHttpGet };
            invalidPodTemplate = KubernetesYaml.Serialize(parsedPodTemplate);
            ex = Assert.Throws<ArgumentNullException>(() => PodTemplateValidator.ValidatePodTemplate(invalidPodTemplate, true, true));
            Assert.Contains("podTemplate.Template.Spec.Containers[0].ReadinessProbe.HttpGet.Scheme", ex.Message);

            invalidHttpGet.Path = "test";
            container_2.ReadinessProbe = new V1Probe() { HttpGet = invalidHttpGet };
            invalidPodTemplate = KubernetesYaml.Serialize(parsedPodTemplate);
            ex = Assert.Throws<ArgumentNullException>(() => PodTemplateValidator.ValidatePodTemplate(invalidPodTemplate, true, true));
            Assert.Contains("podTemplate.Template.Spec.Containers[0].ReadinessProbe.HttpGet.Scheme", ex.Message);

            invalidHttpGet.Scheme = "non-http";
            container_2.ReadinessProbe = new V1Probe() { HttpGet = invalidHttpGet };
            invalidPodTemplate = KubernetesYaml.Serialize(parsedPodTemplate);
            ex = Assert.Throws<ArgumentException>(() => PodTemplateValidator.ValidatePodTemplate(invalidPodTemplate, true, true));
            Assert.Contains("Expected exactly one of either an Http or Https scheme in podTemplate.Template.Spec.Containers[0].ReadinessProbe.HttpGet.Scheme", ex.Message);

            var validReadinessProbe = new V1Probe() { HttpGet = validHttpGet };
            container_2.ReadinessProbe = validReadinessProbe;
            validReadinessProbe.Grpc = new V1GRPCAction();
            invalidPodTemplate = KubernetesYaml.Serialize(parsedPodTemplate);
            ex = Assert.Throws<ArgumentException>(() => PodTemplateValidator.ValidatePodTemplate(invalidPodTemplate, true, true));
            Assert.Contains("Grpc configurations in readiness probes are not supported: podTemplate.Template.Spec.Containers[0].ReadinessProbe", ex.Message);

            validReadinessProbe.Grpc = null;
            validReadinessProbe.TcpSocket = new V1TCPSocketAction() { Port = 123 };
            invalidPodTemplate = KubernetesYaml.Serialize(parsedPodTemplate);
            ex = Assert.Throws<ArgumentException>(() => PodTemplateValidator.ValidatePodTemplate(invalidPodTemplate, true, true));
            Assert.Contains("TcpSocket configurations in readiness probes are not supported: podTemplate.Template.Spec.Containers[0].ReadinessProbe", ex.Message);

            validReadinessProbe.TcpSocket = null;
            container_2.ReadinessProbe = validReadinessProbe;
            container_2.LivenessProbe = null;
            invalidPodTemplate = KubernetesYaml.Serialize(parsedPodTemplate);
            ex = Assert.Throws<ArgumentNullException>(() => PodTemplateValidator.ValidatePodTemplate(invalidPodTemplate, true, true));
            Assert.Contains("podTemplate.Template.Spec.Containers[0].LivenessProbe", ex.Message);

            container_2.LivenessProbe = new V1Probe() { HttpGet = validHttpGet, Exec = new V1ExecAction() };
            invalidPodTemplate = KubernetesYaml.Serialize(parsedPodTemplate);
            ex = Assert.Throws<ArgumentException>(() => PodTemplateValidator.ValidatePodTemplate(invalidPodTemplate, true, true));
            Assert.Contains("Expected exactly one of either an HttpGet or Exec configuration in podTemplate.Template.Spec.Containers[0].LivenessProbe, found both.", ex.Message);

            invalidHttpGet = new V1HTTPGetAction() { Port = 1234 };
            container_2.LivenessProbe = new V1Probe() { HttpGet = invalidHttpGet };
            invalidPodTemplate = KubernetesYaml.Serialize(parsedPodTemplate);
            ex = Assert.Throws<ArgumentNullException>(() => PodTemplateValidator.ValidatePodTemplate(invalidPodTemplate, true, true));
            Assert.Contains("podTemplate.Template.Spec.Containers[0].LivenessProbe.HttpGet.Path", ex.Message);

            invalidHttpGet.Path = "test";
            container_2.LivenessProbe = new V1Probe() { HttpGet = invalidHttpGet };
            invalidPodTemplate = KubernetesYaml.Serialize(parsedPodTemplate);
            ex = Assert.Throws<ArgumentNullException>(() => PodTemplateValidator.ValidatePodTemplate(invalidPodTemplate, true, true));
            Assert.Contains("podTemplate.Template.Spec.Containers[0].LivenessProbe.HttpGet.Scheme", ex.Message);

            invalidHttpGet.Path = "test";
            container_2.LivenessProbe = new V1Probe() { HttpGet = invalidHttpGet };
            invalidPodTemplate = KubernetesYaml.Serialize(parsedPodTemplate);
            ex = Assert.Throws<ArgumentNullException>(() => PodTemplateValidator.ValidatePodTemplate(invalidPodTemplate, true, true));
            Assert.Contains("podTemplate.Template.Spec.Containers[0].LivenessProbe.HttpGet.Scheme", ex.Message);

            invalidHttpGet.Scheme = "non-http";
            container_2.LivenessProbe = new V1Probe() { HttpGet = invalidHttpGet };
            invalidPodTemplate = KubernetesYaml.Serialize(parsedPodTemplate);
            ex = Assert.Throws<ArgumentException>(() => PodTemplateValidator.ValidatePodTemplate(invalidPodTemplate, true, true));
            Assert.Contains("Expected exactly one of either an Http or Https scheme in podTemplate.Template.Spec.Containers[0].LivenessProbe.HttpGet.Scheme", ex.Message);


            var validLIvenessProbe = new V1Probe() { HttpGet = validHttpGet };
            container_2.LivenessProbe = validLIvenessProbe;
            validLIvenessProbe.Grpc = new V1GRPCAction();
            invalidPodTemplate = KubernetesYaml.Serialize(parsedPodTemplate);
            ex = Assert.Throws<ArgumentException>(() => PodTemplateValidator.ValidatePodTemplate(invalidPodTemplate, true, true));
            Assert.Contains("Grpc configurations in liveness probes are not supported: podTemplate.Template.Spec.Containers[0].LivenessProbe", ex.Message);

            validLIvenessProbe.Grpc = null;
            validLIvenessProbe.TcpSocket = new V1TCPSocketAction() { Port = 123 };
            invalidPodTemplate = KubernetesYaml.Serialize(parsedPodTemplate);
            ex = Assert.Throws<ArgumentException>(() => PodTemplateValidator.ValidatePodTemplate(invalidPodTemplate, true, true));
            Assert.Contains("TcpSocket configurations in liveness probes are not supported: podTemplate.Template.Spec.Containers[0].LivenessProbe", ex.Message);
        }

        public V1PodTemplate ResetPodTemplate()
        {
            return KubernetesYaml.Deserialize<V1PodTemplate>(ValidPodTemplateString);
        }
    }
}
