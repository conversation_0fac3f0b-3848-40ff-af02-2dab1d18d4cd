﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using FakeItEasy;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.MachineLearning.ModelMesh.ModelHost.Services.ClientInterfaces;
using Microsoft.MachineLearning.ModelMesh.ModelHost.Services.ECS;
using Microsoft.MachineLearning.ModelMesh.ModelHost.Services.Heartbeat;
using Microsoft.MachineLearning.ModelMesh.ModelHost.Services.Reconcilers;
using Microsoft.MachineLearning.ModelMesh.ModelHost.Services.Utilities;
using Microsoft.MachineLearning.ModelMesh.ModelHost.Services.Utilities.ReconciliationWaker;
using Microsoft.MachineLearning.ModelMesh.ModelHost.Types.ConfigurationDocuments.Common;
using Microsoft.MachineLearning.ModelMesh.ModelHost.Types.ConfigurationDocuments.Common.DeploymentParameters;
using Microsoft.MachineLearning.ModelMesh.ModelHost.Types.ConfigurationDocuments.ModelInstance;
using Microsoft.MachineLearning.ModelMesh.ModelHost.Types.ConfigurationDocuments.ModelInstance.DeploymentParameters;
using Microsoft.MachineLearning.ModelMesh.ModelHost.Types.ConfigurationDocuments.Sidecar;
using Microsoft.MachineLearning.ModelMesh.ModelHost.Types.DeploymentFabric;
using Microsoft.MachineLearning.ModelMesh.ModelHost.Types.Heartbeat;
using Microsoft.MachineLearning.ModelMesh.ModelHost.Types.ModelResource;
using Microsoft.MachineLearning.ModelMesh.ModelHost.Types.Redis;
using Microsoft.MachineLearning.ModelMesh.Shared.Common.LeaderElection;
using Microsoft.MachineLearning.ModelMesh.Shared.Common.Redis;
using Newtonsoft.Json.Linq;

using Xunit;

namespace Microsoft.MachineLearning.ModelMesh.ModelHost.UnitTests.ModelHost.Services.Reconcilers;

/// <summary>
/// Unit tests for the <see cref="InstanceDeploymentReconciler"/> class.
/// </summary>
public class InstanceDeploymentReconcilerTests
{
    private readonly ILogger _fakeLogger;
    private readonly ILeaderElection _fakeLeaderElection;
    private readonly IOptionsMonitor<ModelHostRedisConnections> _fakeRedisConnection;
    private readonly IConfigurationDocumentClient<ModelInstanceConfigurationDocument> _fakeModelInstanceConfigurationClient;
    private readonly IConfigurationDocumentClient<SidecarBundleConfigurationDocument> _fakeSidecarBundleConfigurationClient;
    private readonly IDeploymentFabricClientFactory _fakeDeploymentFabricClientFactory;
    private readonly SingularityDeploymentFabricConfiguration _fakeClusterConfig;
    private readonly IHeartbeatUtilities _fakeHeartbeatUtilities;
    private readonly IReconciliationWaker _fakeReconciliationWaker;
    private readonly IInstanceConfigurationResourcesHelper _fakeInstanceConfigurationResourcesHelper;
    private readonly IRedisConnectionFactory _fakeRedisConnectionFactory;

    private readonly TestReconciler _testReconciler;

    private readonly IDeploymentFabricClient _fakeDeploymentFabricClient;
    private readonly IDictionary<string, ModelInstanceModelResource> _fakeDeployedModelInstances;
    private readonly IList<ModelInstanceConfigurationDocument> _fakeModelInstanceConfigurations;
    private readonly IDictionary<(string, string), SidecarBundleConfigurationDocument> _fakeSidecarBundleConfigurations;

    private readonly IModelHostConfigurationProvider _fakeConfigurationProvider;

    /// <summary>
    /// Initializes a new instance of the <see cref="InstanceDeploymentReconcilerTests"/> class.
    /// </summary>
    public InstanceDeploymentReconcilerTests()
    {
        _fakeLogger = A.Fake<ILogger>();
        _fakeLeaderElection = A.Fake<ILeaderElection>();
        _fakeRedisConnection = A.Fake<IOptionsMonitor<ModelHostRedisConnections>>();
        _fakeModelInstanceConfigurationClient = A.Fake<IConfigurationDocumentClient<ModelInstanceConfigurationDocument>>();
        _fakeSidecarBundleConfigurationClient = A.Fake<IConfigurationDocumentClient<SidecarBundleConfigurationDocument>>();
        _fakeDeploymentFabricClientFactory = A.Fake<IDeploymentFabricClientFactory>();
        _fakeHeartbeatUtilities = A.Fake<IHeartbeatUtilities>();
        _fakeReconciliationWaker = A.Fake<IReconciliationWaker>();
        _fakeInstanceConfigurationResourcesHelper = A.Fake<IInstanceConfigurationResourcesHelper>();

        _fakeClusterConfig = new SingularityDeploymentFabricConfiguration { ClusterName = "FakeClusterConfig" };

        _fakeDeploymentFabricClient = A.Fake<IDeploymentFabricClient>();
        A.CallTo(() => _fakeDeploymentFabricClientFactory.GetClient(_fakeClusterConfig)).Returns(_fakeDeploymentFabricClient);

        _fakeDeployedModelInstances = new Dictionary<string, ModelInstanceModelResource>();
        _fakeModelInstanceConfigurations = new List<ModelInstanceConfigurationDocument>();
        _fakeSidecarBundleConfigurations = new Dictionary<(string, string), SidecarBundleConfigurationDocument>();

        _fakeConfigurationProvider = A.Fake<IModelHostConfigurationProvider>();

        _fakeRedisConnectionFactory = A.Fake<IRedisConnectionFactory>();

        _testReconciler = new TestReconciler(
            _fakeLogger,
            _fakeLeaderElection,
            _fakeRedisConnection,
            _fakeModelInstanceConfigurationClient,
            _fakeSidecarBundleConfigurationClient,
            _fakeDeploymentFabricClientFactory,
            _fakeClusterConfig,
            _fakeHeartbeatUtilities,
            _fakeReconciliationWaker,
            _fakeInstanceConfigurationResourcesHelper,
            _fakeConfigurationProvider,
            _fakeRedisConnectionFactory);
    }

    // Define a wrapper class so that we can use the protected "ReconcileOnceAsync" method.
    private class TestReconciler : InstanceDeploymentReconciler
    {
        public TestReconciler(
            ILogger logger,
            ILeaderElection leaderElection,
            IOptionsMonitor<ModelHostRedisConnections> redisConnectionConfiguration,
            IConfigurationDocumentClient<ModelInstanceConfigurationDocument> modelInstanceConfigurationClient,
            IConfigurationDocumentClient<SidecarBundleConfigurationDocument> sidecarBundleConfigurationClient,
            IDeploymentFabricClientFactory deploymentFabricClientFactory,
            SingularityDeploymentFabricConfiguration clusterConfig,
            IHeartbeatUtilities heartbeatUtilities,
            IReconciliationWaker reconciliationWaker,
            IInstanceConfigurationResourcesHelper instanceConfigurationResourcesHelper,
            IModelHostConfigurationProvider configurationProvider,
            IRedisConnectionFactory redisConnectionFactory)
            : base(logger, leaderElection, redisConnectionConfiguration, modelInstanceConfigurationClient, sidecarBundleConfigurationClient, deploymentFabricClientFactory, clusterConfig, heartbeatUtilities, instanceConfigurationResourcesHelper, configurationProvider, redisConnectionFactory)
        {
        }

        public Task TestReconcileOnceAsync(CancellationToken cancellationToken)
        {
            return ReconcileOnceAsync(cancellationToken);
        }
    }

    private class TestCommand()
    {
        public int RequestedInstanceCount { get; set; }
        public int k { get; set; }
        public (int, int) ExpectedInstanceCounts { get; set; }
    }

    /// <summary>
    /// Tests that the <see cref="TestReconciler"/> can start successfully.
    /// </summary>
    [Fact]
    public async Task StartAsync_SuccessAsync()
    {
        // Arrange

        // Act
        var startTask = _testReconciler.StartAsync(CancellationToken.None);

        // Assert
        Assert.NotNull(startTask);
        await startTask;
    }

    [Fact]
    public async Task ReconcileOnceAsync_ShouldNotThrowException()
    {
        // Arrange
        var cancellationToken = new CancellationToken();

        // Act
        var exception = await Record.ExceptionAsync(() => _testReconciler.TestReconcileOnceAsync(cancellationToken));

        // Assert
        Assert.Null(exception);
    }

    [Fact]
    public async Task RollingUpdate_UpdateOneInstance_Success()
    {
        // Arrange
        var cancellationToken = new CancellationToken();

        AddOrUpdateModelInstanceConfiguration(requestedInstanceCount: 1);
        AddModelInstances(
            instanceCount: 1,
            sidecarBundleConfigurationName: "contonso_sidecar",
            sidecarBundleConfigurationVersion: "20250312.0");
        AddOrUpdateSidecarBundleConfiguration(
            isDeployable: true,
            sidecarBundleConfigurationName: "contonso_sidecar",
            sidecarBundleConfigurationVersion: "20250312.1");  // Mark a new sidecar as deployable
        AddOrUpdateSidecarBundleConfiguration(
            isDeployable: false,
            sidecarBundleConfigurationName: "contonso_sidecar",
            sidecarBundleConfigurationVersion: "20250312.0");  // Mark the old sidecar as undeployable -> Signal to update

        ArrangeReconcilerCalls();

        var commands = new List<TestCommand>
        {
            new TestCommand { RequestedInstanceCount = 1, ExpectedInstanceCounts = (1, 0) }, // Initial: 1 stale instance
            new TestCommand { RequestedInstanceCount = 1, ExpectedInstanceCounts = (1, 1) }, // Add 1 fresh
            new TestCommand { RequestedInstanceCount = 1, ExpectedInstanceCounts = (0, 1) }, // Delete 1 fresh
        };

        // Act & Assert
        int step = 0;
        Assert.Equal(commands[step].ExpectedInstanceCounts, GetCurrentInstanceCount());
        while (ShouldContinueReconciliation())
        {
            await _testReconciler.TestReconcileOnceAsync(cancellationToken).ConfigureAwait(false);

            Assert.Equal(commands[++step].ExpectedInstanceCounts, GetCurrentInstanceCount());
            ArrangeReconcilerCalls();
        }

        // Assert
        Assert.Equal(2, step);
    }

    [Fact]
    public async Task RollingUpdate_UpdateTenInstancesWithOneExtraUnit_Success()
    {
        // Arrange
        var cancellationToken = new CancellationToken();

        AddOrUpdateModelInstanceConfiguration(requestedInstanceCount: 10);
        AddModelInstances(
            instanceCount: 10,
            sidecarBundleConfigurationName: "contonso_sidecar",
            sidecarBundleConfigurationVersion: "20250312.0");
        AddOrUpdateSidecarBundleConfiguration(
            isDeployable: false,
            sidecarBundleConfigurationName: "contonso_sidecar",
            sidecarBundleConfigurationVersion: "20250312.0");
        AddOrUpdateSidecarBundleConfiguration(
            isDeployable: true,
            sidecarBundleConfigurationName: "contonso_sidecar",
            sidecarBundleConfigurationVersion: "20250312.1");

        ArrangeReconcilerCalls();

        var commands = new List<TestCommand>
        {
            new TestCommand { RequestedInstanceCount = 10, ExpectedInstanceCounts = (10, 0) }, // Initial state: 10 stale instances
            new TestCommand { RequestedInstanceCount = 10, ExpectedInstanceCounts = (10, 1) }, // Add 1 fresh
            new TestCommand { RequestedInstanceCount = 10, ExpectedInstanceCounts = (9, 1) }, // Delete 1 stale
            new TestCommand { RequestedInstanceCount = 10, ExpectedInstanceCounts = (9, 2) }, // Add 1 fresh
            new TestCommand { RequestedInstanceCount = 10, ExpectedInstanceCounts = (8, 2) }, // Delete 1 stale
            new TestCommand { RequestedInstanceCount = 10, ExpectedInstanceCounts = (8, 3) }, // Add 1 fresh
            new TestCommand { RequestedInstanceCount = 10, ExpectedInstanceCounts = (7, 3) }, // Delete 1 stale
            new TestCommand { RequestedInstanceCount = 10, ExpectedInstanceCounts = (7, 4) }, // Add 1 fresh
            new TestCommand { RequestedInstanceCount = 10, ExpectedInstanceCounts = (6, 4) }, // Delete 1 stale
            new TestCommand { RequestedInstanceCount = 10, ExpectedInstanceCounts = (6, 5) }, // Add 1 fresh
            new TestCommand { RequestedInstanceCount = 10, ExpectedInstanceCounts = (5, 5) }, // Delete 1 stale
            new TestCommand { RequestedInstanceCount = 10, ExpectedInstanceCounts = (5, 6) }, // Add 1 fresh
            new TestCommand { RequestedInstanceCount = 10, ExpectedInstanceCounts = (4, 6) }, // Delete 1 stale
            new TestCommand { RequestedInstanceCount = 10, ExpectedInstanceCounts = (4, 7) }, // Add 1 fresh
            new TestCommand { RequestedInstanceCount = 10, ExpectedInstanceCounts = (3, 7) }, // Delete 1 stale
            new TestCommand { RequestedInstanceCount = 10, ExpectedInstanceCounts = (3, 8) }, // Add 1 fresh
            new TestCommand { RequestedInstanceCount = 10, ExpectedInstanceCounts = (2, 8) }, // Delete 1 stale
            new TestCommand { RequestedInstanceCount = 10, ExpectedInstanceCounts = (2, 9) }, // Add 1 fresh
            new TestCommand { RequestedInstanceCount = 10, ExpectedInstanceCounts = (1, 9) }, // Delete 1 stale
            new TestCommand { RequestedInstanceCount = 10, ExpectedInstanceCounts = (1, 10) }, // Add 1 fresh
            new TestCommand { RequestedInstanceCount = 10, ExpectedInstanceCounts = (0, 10) }, // Delete 1 stale
        };

        // Act & Assert
        int step = 0;
        Assert.Equal(commands[step].ExpectedInstanceCounts, GetCurrentInstanceCount());
        while (ShouldContinueReconciliation())
        {
            await _testReconciler.TestReconcileOnceAsync(cancellationToken).ConfigureAwait(false);

            Assert.Equal(commands[++step].ExpectedInstanceCounts, GetCurrentInstanceCount());

            ArrangeReconcilerCalls();
        }

        // Assert
        Assert.Equal(20, step);
    }

    private void ArrangeReconcilerCalls()
    {
        var latestSidecarBundleConfig = _fakeSidecarBundleConfigurations.Values.Where(sc => sc.IsDeployable).OrderByDescending(sc => sc.Version).First();

        A.CallTo(() => _fakeModelInstanceConfigurationClient.ListConfigurationDocumentsAsync(A<bool>.Ignored)).Returns(_fakeModelInstanceConfigurations);
        A.CallTo(() => _fakeSidecarBundleConfigurationClient.ListConfigurationDocumentsAsync(A<bool>.Ignored)).Returns(_fakeSidecarBundleConfigurations.Values.ToList());

        A.CallTo(() => _fakeDeploymentFabricClient.ListDeployedModelInstancesAsync()).Returns(_fakeDeployedModelInstances.Values.ToList());

        var heartbeatInfo = new HeartbeatInfo()
        {
            Status = "Running",
            ExtraInfo = JObject.FromObject(new Dictionary<string, bool>
            {
                { "routable", true }
            })
        };
        A.CallTo(() => _fakeHeartbeatUtilities.TryGetLatestInfoAsync(A<string>.Ignored, A<string>.Ignored, A<HeartbeatSource>.Ignored)).Returns(heartbeatInfo);

        A.CallTo(() => _fakeInstanceConfigurationResourcesHelper.CountDeployableModelInstances(A<ModelInstanceConfigurationDocument>.Ignored, A<IDictionary<string, (long, long, Dictionary<string, long>)>>.Ignored)).Returns(1);

        A.CallTo(() => _fakeDeploymentFabricClient.DeployModelInstanceAsync(A<string>.Ignored, A<string>.Ignored, A<string?>.Ignored, A<string?>.Ignored, A<string?>.Ignored))
            .Invokes(() => { AddModelInstances(instanceCount: 1, sidecarBundleConfigurationVersion: latestSidecarBundleConfig.Version); });
        A.CallTo(() => _fakeDeploymentFabricClient.UpdateModelInstanceAsync(A<ModelInstanceModelResource>.Ignored))
            .Invokes((ModelInstanceModelResource modelInstance) => { UpdateModelInstance(modelInstance.Name, sidecarBundleConfigurationVersion: latestSidecarBundleConfig.Version); });
        A.CallTo(() => _fakeDeploymentFabricClient.ReleaseModelInstanceAsync(A<string>.Ignored, A<bool>.Ignored))
            .Invokes((string modelInstanceName, bool skipUnload) => { DeleteModelInstance(modelInstanceName); });
    }

    private void AddModelInstances(
        int instanceCount,
        string modelInstanceConfigurationName = "contonso_instance",
        string modelInstanceConfigurationVersion = "20250312.0",
        string sidecarBundleConfigurationName = "contonso_sidecar",
        string sidecarBundleConfigurationVersion = "20250312.0")
    {
        for (int i = 0; i < instanceCount; i++)
        {
            var modelResource = new ModelInstanceModelResource(
                id: $".../models/i-{Guid.NewGuid()}",
                name: $"i-{Guid.NewGuid()}",
                configurationName: modelInstanceConfigurationName,
                configurationVersion: modelInstanceConfigurationVersion,
                sidecarBundleName: sidecarBundleConfigurationName,
                sidecarBundleVersion: sidecarBundleConfigurationVersion,
                provisioningState: "Ready",
                status: "Ready",
                clusterIp: string.Empty,
                fqdn: string.Empty);

            _fakeDeployedModelInstances.Add(modelResource.Name, modelResource);
        }
    }

    private void DeleteModelInstance(string modelInstanceName)
    {
        _fakeDeployedModelInstances.Remove(modelInstanceName);
    }

    private void UpdateModelInstance(
        string modelInstanceName,
        string sidecarBundleConfigurationName = "contonso_sidecar",
        string sidecarBundleConfigurationVersion = "20250312.0")
    {
        var updatedModelResource = new ModelInstanceModelResource(
                id: _fakeDeployedModelInstances[modelInstanceName].Id,
                name: _fakeDeployedModelInstances[modelInstanceName].Name,
                configurationName: _fakeDeployedModelInstances[modelInstanceName].ConfigurationName,
                configurationVersion: _fakeDeployedModelInstances[modelInstanceName].ConfigurationVersion,
                sidecarBundleName: sidecarBundleConfigurationName,
                sidecarBundleVersion: sidecarBundleConfigurationVersion,
                provisioningState: "Ready",
                status: "Undefined",
                clusterIp: string.Empty,
                fqdn: string.Empty);

        _fakeDeployedModelInstances[modelInstanceName] = updatedModelResource;
    }

    private void AddOrUpdateModelInstanceConfiguration(
        uint requestedInstanceCount,
        string modelInstanceConfigurationName = "contonso_instance",
        string modelInstanceConfigurationVersion = "20250312.0")
    {
        _fakeModelInstanceConfigurations.Clear();

        var fakeDeploymentParameters = new ModelInstanceConfigurationDeploymentParameters(
            status: ConfigurationStatus.Registered,
            deployability: ConfigurationDeployability.Deployable,
            minInstances: new Dictionary<string, uint>(),
            maxInstances: new Dictionary<string, uint>(),
            requestedDirectInstances: new Dictionary<string, uint> { { "FakeClusterConfig", requestedInstanceCount } },
            requestedHotswapInstances: new Dictionary<string, uint> (),
            provisionedResources: new ProvisionedResources(new Dictionary<string, string>(), new Dictionary<string, string>()));

        var fakeModelInstanceConfiguration = new ModelInstanceConfigurationDocument(
            name: modelInstanceConfigurationName,
            version: modelInstanceConfigurationVersion,
            publisherName: "TestPublisherName",
            modelId: "TestModelId",
            instanceTemplates: A.Fake<IList<InstanceTemplate>>(),
            deploymentParameters: fakeDeploymentParameters,
            inferenceEndpoints: A.Fake<ICollection<InferenceEndpointConfiguration>>(),
            metricsEndpoint: A.Fake<MetricsEndpointConfiguration>(),
            modelRegistryAssets: A.Fake<IDictionary<string, ModelRegistryStorageInfo>>(),
            hotswapEngine: "FakeHotswapEngine",
            capabilitySpec: null,
            modelInstanceIdentity: A.Fake<UserAssignedIdentity>(),
            tags: null,
            etag: "FakeEtag");

        _fakeModelInstanceConfigurations.Add(fakeModelInstanceConfiguration);
    }

    private void AddOrUpdateSidecarBundleConfiguration(
        bool isDeployable,
        string sidecarBundleConfigurationName = "contonso_sidecar",
        string sidecarBundleConfigurationVersion = "20250312.0")
    {
        var fakeSidecarBundleConfiguration = new SidecarBundleConfigurationDocument(
            name: sidecarBundleConfigurationName,
            version: sidecarBundleConfigurationVersion,
            isDeployable: isDeployable,
            modelInstanceContainerSpec: "fakeModelInstanceContainerSpec",
            modelInstanceArtifactsUri: new Uri("http://fakeModelInstanceArtifactsUri"),
            baseImageIdentifier: "fakeBaseImageIdentifier",
            cmpContainerSpec: null,
            sidecarBundleIdentity: null,
            tags: null,
            etag: null
        );

        if (!_fakeSidecarBundleConfigurations.ContainsKey((sidecarBundleConfigurationName, sidecarBundleConfigurationVersion)))
        {
            _fakeSidecarBundleConfigurations.Add(
                (fakeSidecarBundleConfiguration.Name, fakeSidecarBundleConfiguration.Version),
                fakeSidecarBundleConfiguration);
        }
        else
        {
            _fakeSidecarBundleConfigurations[(sidecarBundleConfigurationName, sidecarBundleConfigurationVersion)] = fakeSidecarBundleConfiguration;
        }
    }

    private bool ShouldContinueReconciliation()
    {
        // If any model instances use stale sidecar, return True to continue
        var res = _fakeDeployedModelInstances.Values.Any(modelInstance =>
        {
            var sidecarBundleConfig = _fakeSidecarBundleConfigurations[(modelInstance.SidecarBundleConfigurationName, modelInstance.SidecarBundleConfigurationVersion)];

            return sidecarBundleConfig.IsDeployable == false;
        });

        return res;
    }

    private (int, int) GetCurrentInstanceCount()
    {
        var deployableSidecarBundleConfigsHashSet = _fakeSidecarBundleConfigurations.Values
            .Where(sc => sc.IsDeployable)
            .Select(sb => (sb.Name, sb.Version))
            .ToHashSet();

        var staleInstanceSet = _fakeDeployedModelInstances.Values
            .Where(instance => !deployableSidecarBundleConfigsHashSet.Contains((instance.SidecarBundleConfigurationName, instance.SidecarBundleConfigurationVersion)))
            .ToList();

        var freshInstanceSet = _fakeDeployedModelInstances.Values.Except(staleInstanceSet).ToList();

        return (staleInstanceSet.Count, freshInstanceSet.Count);
    }
} 
