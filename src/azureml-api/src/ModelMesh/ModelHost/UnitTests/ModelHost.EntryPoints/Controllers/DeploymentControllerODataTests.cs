using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Common.Core.Contracts;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.OData.Query;
using Microsoft.Extensions.Logging;
using Microsoft.MachineLearning.ModelMesh.ModelHost.Contracts.Deployment;
using Microsoft.MachineLearning.ModelMesh.ModelHost.EntryPoints.Api.V2;
using Microsoft.MachineLearning.ModelMesh.ModelHost.Services.Clients.CosmosDb;
using Microsoft.MachineLearning.ModelMesh.ModelHost.Services.DeploymentAgent;
using Microsoft.MachineLearning.ModelMesh.ModelHost.Types.DeploymentDocuments;
using Microsoft.MachineLearning.ModelMesh.ModelHost.Types.DeploymentDocuments.OperationDocuments;
using Microsoft.OData.Edm;
using Microsoft.OData.ModelBuilder;
using Moq;
using Xunit;

namespace Microsoft.MachineLearning.ModelMesh.ModelHost.UnitTests.Controllers
{
    public class DeploymentControllerODataTests
    {
        private readonly Mock<IDeploymentDocumentClient> _deploymentDocumentClientMock;
        private readonly Mock<IDeploymentOperationDocumentClient> _operationDocumentClientMock;
        private readonly Mock<IDeploymentAgentService> _deploymentAgentServiceMock;
        private readonly DeploymentController _controller;
        private readonly IEdmModel _edmModel;

        public DeploymentControllerODataTests()
        {
            _deploymentDocumentClientMock = new Mock<IDeploymentDocumentClient>();
            _operationDocumentClientMock = new Mock<IDeploymentOperationDocumentClient>();
            _deploymentAgentServiceMock = new Mock<IDeploymentAgentService>();
            var logger = new Mock<ILogger<DeploymentController>>();
            
            _controller = new DeploymentController(
                logger.Object,
                _deploymentDocumentClientMock.Object,
                _operationDocumentClientMock.Object,
                _deploymentAgentServiceMock.Object);

            // Setup HTTP context for URL generation
            var httpContext = new DefaultHttpContext();
            httpContext.Request.Scheme = "http";
            httpContext.Request.Host = new HostString("localhost:5000");
            httpContext.Request.PathBase = "/modelmesh/v1.0/modelhost/v2.0";
            httpContext.Request.Path = "/deployments";
            
            _controller.ControllerContext = new ControllerContext
            {
                HttpContext = httpContext
            };

            // Create EDM model for OData
            var builder = new ODataConventionModelBuilder();
            builder.EntitySet<DeploymentResponseDto>("Deployments");
            _edmModel = builder.GetEdmModel();
        }

        [Fact]
        public async Task ListDeploymentsAsync_WithNoQueryOptions_ReturnsAllDeployments()
        {
            // Arrange
            var deployments = CreateTestDeployments(5);
            _deploymentDocumentClientMock
                .Setup(x => x.ListDeploymentDocumentsAsync(null, false))
                .ReturnsAsync(deployments);
            
            SetupDeploymentAgentResponses(deployments);

            var queryOptions = CreateODataQueryOptions<DeploymentResponseDto>(null, null, null, null);

            // Act
            var result = await _controller.ListDeploymentsAsync(queryOptions);

            // Assert
            var paginatedResult = GetPaginatedResult(result);
            
            Assert.NotNull(paginatedResult);
            Assert.Equal(5, paginatedResult.Value.Count);
            Assert.Null(paginatedResult.NextLink);
        }

        [Fact]
        public async Task ListDeploymentsAsync_WithTopParameter_LimitsResults()
        {
            // Arrange
            var deployments = CreateTestDeployments(10);
            _deploymentDocumentClientMock
                .Setup(x => x.ListDeploymentDocumentsAsync(null, false))
                .ReturnsAsync(deployments);
            
            SetupDeploymentAgentResponses(deployments);

            var queryOptions = CreateODataQueryOptions<DeploymentResponseDto>(null, null, 3, null);

            // Act
            var result = await _controller.ListDeploymentsAsync(queryOptions);

            // Assert
            var paginatedResult = GetPaginatedResult(result);
            
            Assert.Equal(3, paginatedResult.Value.Count);
            Assert.NotNull(paginatedResult.NextLink);
            Assert.Contains("$skip=3", paginatedResult.NextLink);
            Assert.Contains("$top=3", paginatedResult.NextLink);
        }

        [Fact]
        public async Task ListDeploymentsAsync_WithSkipParameter_SkipsResults()
        {
            // Arrange
            var deployments = CreateTestDeployments(10);
            _deploymentDocumentClientMock
                .Setup(x => x.ListDeploymentDocumentsAsync(null, false))
                .ReturnsAsync(deployments);
            
            SetupDeploymentAgentResponses(deployments);

            var queryOptions = CreateODataQueryOptions<DeploymentResponseDto>(null, null, 5, 3);

            // Act
            var result = await _controller.ListDeploymentsAsync(queryOptions);

            // Assert
            var paginatedResult = GetPaginatedResult(result);
            
            Assert.Equal(5, paginatedResult.Value.Count);
            Assert.Equal("deployment-3", paginatedResult.Value[0].Id);
            Assert.NotNull(paginatedResult.NextLink);
            Assert.Contains("$skip=8", paginatedResult.NextLink);
        }

        [Fact]
        public async Task ListDeploymentsAsync_ExceedsMaxPageSize_CapsAt100()
        {
            // Arrange
            var deployments = CreateTestDeployments(2000);
            _deploymentDocumentClientMock
                .Setup(x => x.ListDeploymentDocumentsAsync(null, false))
                .ReturnsAsync(deployments);

            var cappedQueryOptions = CreateODataQueryOptions<DeploymentResponseDto>(null, null, 100, null);

            SetupDeploymentAgentResponses(deployments.Take(100));

            // Act
            var result = await _controller.ListDeploymentsAsync(cappedQueryOptions);

            // Assert
            var paginatedResult = GetPaginatedResult(result);

            Assert.Equal(100, paginatedResult.Value.Count); // Capped at max
            Assert.NotNull(paginatedResult.NextLink);
            Assert.Contains("$top=100", paginatedResult.NextLink);
        }

        [Fact]
        public async Task ListDeploymentsAsync_LastPage_NoNextLink()
        {
            // Arrange
            var deployments = CreateTestDeployments(5);
            _deploymentDocumentClientMock
                .Setup(x => x.ListDeploymentDocumentsAsync(null, false))
                .ReturnsAsync(deployments);
            
            SetupDeploymentAgentResponses(deployments);

            var queryOptions = CreateODataQueryOptions<DeploymentResponseDto>(null, null, 10, 0);

            // Act
            var result = await _controller.ListDeploymentsAsync(queryOptions);

            // Assert
            var paginatedResult = GetPaginatedResult(result);
            
            Assert.Equal(5, paginatedResult.Value.Count);
            Assert.Null(paginatedResult.NextLink); // No more pages
        }

        [Fact]
        public async Task ListDeploymentsAsync_WithFilterParameter_FiltersResults()
        {
            // Arrange
            var deployments = CreateTestDeployments(10);
            _deploymentDocumentClientMock
                .Setup(x => x.ListDeploymentDocumentsAsync(null, false))
                .ReturnsAsync(deployments);
            
            SetupDeploymentAgentResponses(deployments);

            var queryOptions = CreateODataQueryOptions<DeploymentResponseDto>("Id eq 'deployment-5'", null, null, null);

            // Act
            var result = await _controller.ListDeploymentsAsync(queryOptions);

            // Assert
            var paginatedResult = GetPaginatedResult(result);
            
            Assert.Single(paginatedResult.Value); // Should only return one matching deployment
            Assert.Equal("deployment-5", paginatedResult.Value[0].Id);
            Assert.Null(paginatedResult.NextLink); // No pagination expected for single result
        }

        // Helper methods
        private List<DeploymentDocument> CreateTestDeployments(int count)
        {
            return Enumerable.Range(0, count)
                .Select(i => new DeploymentDocument(
                    $"deployment-{i}",
                    new DeploymentRequestDto 
                    {
                        DeploymentRing = DeploymentRing.DevTest,
                        SlaTier = SlaTier.Standard,
                        TargetCluster = new TargetClusterDto
                        {
                            SubscriptionId = Guid.NewGuid(),
                            Region = "test-region",
                        },
                        ModelConfiguration = new ModelConfigurationDto.PresetV3
                        {
                            ModelName = $"model-{i}",
                            InstanceType = "Standard",
                            EngineAssetId = "engine-1",
                            ModelAssetIds = ["model-1"],
                            InferenceReadinessEndpoints = [],
                            InferenceEndpoints = [],
                            MetricsEndpoints = []
                        },
                        DataplaneConfiguration = new DataplaneConfigurationDto.None(),
                    },
                    new Dictionary<string, string>(),
                    null))
                .ToList();
        }

        private void SetupDeploymentAgentResponses(IEnumerable<DeploymentDocument> deployments)
        {
            foreach (var deployment in deployments)
            {
                _deploymentAgentServiceMock
                    .Setup(x => x.GetStatusAsync(deployment.Id))
                    .ReturnsAsync((
                        OperationInternalState.Unknown,
                        new List<(string, ProvisioningStatus, DeploymentStatus, HealthStatus, string, string)>
                        {
                            ("resource-1", ProvisioningStatus.Succeeded, DeploymentStatus.Ready, HealthStatus.Healthy, "fake-sidecar-name", "fake-sidecar-version")
                        }));
            }
        }

        private ODataQueryOptions<T> CreateODataQueryOptions<T>(
            string? filter, 
            string? orderBy, 
            int? top, 
            int? skip)
        {
            var request = new DefaultHttpContext().Request;
            
            // Build query string
            var queryParams = new List<string>();
            if (filter != null) queryParams.Add($"$filter={filter}");
            if (orderBy != null) queryParams.Add($"$orderby={orderBy}");
            if (top.HasValue) queryParams.Add($"$top={top}");
            if (skip.HasValue) queryParams.Add($"$skip={skip}");
            
            if (queryParams.Any())
            {
                request.QueryString = new QueryString("?" + string.Join("&", queryParams));
            }

            // Create ODataQueryContext with the EDM model
            var path = new Microsoft.OData.UriParser.ODataPath();
            var context = new ODataQueryContext(_edmModel, typeof(T), path);
            
            // Create ODataQueryOptions with the real context
            return new ODataQueryOptions<T>(context, request);
        }

        private PaginatedResult<DeploymentResponseDto> GetPaginatedResult(ActionResult<PaginatedResult<DeploymentResponseDto>> result)
        {
            var objectResult = Assert.IsType<OkObjectResult>(result.Result);
            return Assert.IsType<PaginatedResult<DeploymentResponseDto>>(objectResult.Value);
        }
    }
}
