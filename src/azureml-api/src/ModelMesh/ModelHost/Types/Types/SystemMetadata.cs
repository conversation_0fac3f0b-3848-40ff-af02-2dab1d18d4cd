﻿// <copyright file="SystemMetadata.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

namespace Microsoft.MachineLearning.ModelMesh.ModelHost.Types.PresetV3
{
    /// <summary>
    /// Represents system metadata.
    /// </summary>
    public class SystemMetadata
    {
        /// <summary>
        /// Gets or sets the subscription ID.
        /// </summary>
        public string SubscriptionId { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the resource group name.
        /// </summary>
        public string ResourceGroupName { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the workspace name.
        /// </summary>
        public string WorkspaceName { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the endpoint name. Used only for MIR Online Endpoints.
        /// </summary>
        public string EndpointName { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the deployment name.
        /// </summary>
        public string DeploymentName { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the online deployment resource ID.
        /// </summary>
        public string OnlineDeploymentResourceId { get; set; } = "randomId";

        /// <summary>
        /// Gets or sets the region.
        /// </summary>
        public string Region { get; set; } = "randomregion";
    }
}
