﻿using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Microsoft.MachineLearning.ModelMesh.ModelHost.Types.Heartbeat
{
    public class HeartbeatInfo
    {
        [JsonProperty("heartbeatSource")]
        public HeartbeatSource Source { get; set; } = HeartbeatSource.Unknown;

        [JsonProperty("id")]
        public string Id { get; set; } = string.Empty;

        [JsonProperty("status")]
        public string Status { get; set; } = string.Empty;

        [JsonProperty("extraInfo")]
        public JToken ExtraInfo { get; set; } = null!;

        public bool IsHealthy()
        {
            return string.Equals("Running", Status, System.StringComparison.OrdinalIgnoreCase) &&
                bool.TryParse(ExtraInfo.Value<string>("routable") ?? "false", out var routable) &&
                routable;
        }
    }

    public class HeartbeatRedisMessage
    {
        [JsonProperty("data")]
        public HeartbeatInfo? Data { get; set; }

        [JsonProperty("timestamp")]
        public long Timestamp { get; set; }
    }
}
