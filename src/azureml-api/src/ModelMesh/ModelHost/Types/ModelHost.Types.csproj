﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <!-- Stuck behind due to dependency Common not being .NET8 yet -->
    <TargetFramework>net8.0</TargetFramework>
    <AssemblyName>Microsoft.MachineLearning.ModelMesh.ModelHost.Types</AssemblyName>
    <RootNamespace>Microsoft.MachineLearning.ModelMesh.ModelHost.Types</RootNamespace>
    <PackageId>Microsoft.MachineLearning.ModelMesh.ModelHost.Types</PackageId>
    <Product>Microsoft.MachineLearning.ModelMesh.ModelHost.Types</Product>
    <IsPublishable>false</IsPublishable>
  </PropertyGroup>

  <PropertyGroup>
    <Nullable>enable</Nullable>
    <LangVersion>latest</LangVersion>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="JsonSubTypes" />
    <PackageReference Include="Microsoft.Azure.Management.Singularity" VersionOverride="1.1.31407780" />
  </ItemGroup>
  
  <ItemGroup>
    <ProjectReference Include="$(SrcRoot)\Common\Core\Common.Core.csproj" />
    <ProjectReference Include="$(SrcRoot)\ModelMesh\Shared\Common\Shared.Common.csproj" />
    <ProjectReference Include="$(SrcRoot)\ModelMesh\ModelHost\Contracts\ModelHost.Contracts.csproj" />
  </ItemGroup>
</Project>
