﻿// <copyright file="ServiceAuthorizationConfiguration.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

namespace Microsoft.MachineLearning.ModelMesh.ModelHost.Types.ServiceAuthorizationConfiguration
{
    public class ServiceAuthorizationConfiguration
    {
        public SecurityGroupIds SecurityGroupIds { get; set; } = new();
    }

    public class SecurityGroupIds
    {
        public string[] OperatorGroups { get; set; } = [];

        public string[] ObserverGroups { get; set; } = [];

        public string[] PrivilegedOperatorGroups { get; set; } = [];
    }
}
