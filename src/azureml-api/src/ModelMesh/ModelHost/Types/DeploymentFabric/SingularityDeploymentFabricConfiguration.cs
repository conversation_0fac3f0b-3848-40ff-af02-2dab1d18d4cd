﻿// <copyright file="SingularityDeploymentFabricConfiguration.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Text.Json.Serialization;
using Microsoft.MachineLearning.ModelMesh.ModelHost.Types.ConfigurationDocuments.Common;
using Microsoft.MachineLearning.ModelMesh.ModelHost.Types.Redis;

namespace Microsoft.MachineLearning.ModelMesh.ModelHost.Types.DeploymentFabric
{
    public partial record SingularityDeploymentFabricConfiguration
    {
        public string DataplaneEnvironment { get; set; } = "prod";

        public Guid OwnerSubscriptionId { get; set; } = Guid.Empty;

        public string ClusterRegion { get; set; } = string.Empty;

        public bool IsModelPoolOnboarded { get; set; } = true;

        public IPPublisherType IPPublisherType { get; set; } = IPPublisherType.Unknown;

        public Guid AccountTenantId { get; set; } = Guid.Empty;

        public string AccountArmId { get; set; } = string.Empty;

        public string PrivateEndpointSubnetArmId { get; set; } = string.Empty;

        public string PrivateEndpointVNetLocation { get; set; } = string.Empty;

        public DistributorConfiguration DistributorConfiguration { get; set; } = new();

        public string[] InferenceModelResourceIds { get; set; } = [];

        public ContentModerationConfiguration ContentModerationConfiguration { get; set; } = new();

        public BillingEventHubConfiguration BillingConfiguration { get; set; } = null!;
    }

    public partial record SingularityDeploymentFabricConfiguration
    {
        [JsonIgnore]
        public string ClusterName { get; set; } = string.Empty;

        [JsonIgnore]
        public UserAssignedIdentity ModelDistributorIdentity { get; set; } = null!;

        [JsonIgnore]
        public UserAssignedIdentity ModelInstanceIdentity { get; set; } = null!;

        [JsonIgnore]
        public UserAssignedIdentity ModelMeshRegistryReaderIdentity { get; set; } = null!;

        [JsonIgnore]
        public IPPRedisConnections RedisConnections { get; set; } = null!;
    }

    public class DistributorConfiguration
    {
        public string InstanceType { get; set; } = string.Empty;

        public string StorageCacheContainerName { get; set; } = string.Empty;

        public string StorageManifestRemotePath { get; set; } = string.Empty;
    }

    public class ContentModerationConfiguration
    {
        public UserAssignedIdentity CMPIdentity { get; set; } = null!;

        public string ModerationServerUrl { get; set; } = null!;
    }

    public class BillingEventHubConfiguration
    {
        required public string HubNamespace { get; init; }

        required public string HubName { get; init; }
    }
}
