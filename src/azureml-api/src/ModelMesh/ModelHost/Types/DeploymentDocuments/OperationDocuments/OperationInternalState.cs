﻿// <copyright file="OperationInternalState.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Collections.Generic;

namespace Microsoft.MachineLearning.ModelMesh.ModelHost.Types.DeploymentDocuments.OperationDocuments;

public enum OperationInternalState
{
    // Creation states
    Creation_AwaitingCreation,
    Creation_CreatingDependencies,
    Creation_CreatingResources,
    Creation_AwaitingReadiness,
    Creation_Success,
    Creation_Failed,
    // Modification states
    Modification_AwaitingModification,
    Modification_Creating,
    Modification_AwaitingReadiness,
    Modification_Deleting,
    Modification_Success,
    Modification_Failed,
    // Deletion states
    Deletion_AwaitingDeletion,
    Deletion_Deleting,
    Deletion_Success,
    Deletion_Failed,
    // Reboot states
    Reboot_AwaitingReboot,
    Reboot_Rebooting_Deleting,
    Reboot_Rebooting_Creating,
    Reboot_AwaitingReadiness,
    Reboot_Success,
    Reboot_Failed,
    // Termination states
    Exit,
    Unknown,
}


public static class OperationInternalStateHelper
{
    private static readonly HashSet<OperationInternalState> TerminalStates = new()
    {
        OperationInternalState.Creation_Success,
        OperationInternalState.Creation_Failed,
        OperationInternalState.Modification_Success,
        OperationInternalState.Modification_Failed,
        OperationInternalState.Deletion_Success,
        OperationInternalState.Deletion_Failed,
        OperationInternalState.Reboot_Success,
        OperationInternalState.Reboot_Failed,
        OperationInternalState.Exit
    };

    public static bool IsTerminal(OperationInternalState state)
    {
        return TerminalStates.Contains(state);
    }
}

