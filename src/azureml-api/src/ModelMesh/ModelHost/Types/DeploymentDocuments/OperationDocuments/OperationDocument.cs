﻿// <copyright file="OperationDocument.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Collections.Generic;
using Microsoft.MachineLearning.ModelMesh.ModelHost.Contracts.Deployment;

namespace Microsoft.MachineLearning.ModelMesh.ModelHost.Types.DeploymentDocuments.OperationDocuments;

public enum OperationType
{
    Creation,
    Modification,
    Deletion,
    Reboot
}

public class ExceptionInfo
{
    public required string Type { get; set; }
    public string? Message { get; set; }
    public string? StackTrace { get; set; }
}

public class OperationDocument(
    string id,
    string deploymentId,
    OperationType operationType,
    DeploymentRequestDto deploymentConfiguration,
    OperationInternalState internalState,
    List<string> modelResourceIds,
    IDictionary<string, string> tags,
    string? etag) : DeploymentDocumentBase(tags, etag)
{
    public string Id { get; } = id;

    public string DeploymentId => deploymentId;

    public string? JobId = null;

    public OperationType Type { get; init; } = operationType;

    public DeploymentRequestDto DeploymentConfiguration { get; set; } = deploymentConfiguration;

    public OperationInternalState InternalState { get; set; } = internalState;

    public List<string> ModelResourceIds { get; set; } = modelResourceIds;

    public ExceptionInfo? Exception { get; set; } = null;

    public override string PartitionKey => "DeploymentOperation";

    public override string DocumentId => Id;
}


