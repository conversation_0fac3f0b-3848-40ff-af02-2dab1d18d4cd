﻿// <copyright file="HangfireConfiguration.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

namespace Microsoft.MachineLearning.ModelMesh.ModelHost.Types.DeploymentDocuments;

public class HangfireConfiguration
{
    public bool UseInMemory { get; set; } = true;

    // CosmosDB settings
    public string? AccountEndpoint { get; set; }
    public string? DatabaseId { get; set; }
    public string? ContainerId { get; set; }  // aka. CollectionId

    // Redis settings
    public string? RedisHostName { get; set; }
}
