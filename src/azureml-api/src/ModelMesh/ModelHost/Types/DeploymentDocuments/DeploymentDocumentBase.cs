﻿// <copyright file="DeploymentDocumentBase.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using Microsoft.MachineLearning.Common.Core;

namespace Microsoft.MachineLearning.ModelMesh.ModelHost.Types.DeploymentDocuments;
public abstract class DeploymentDocumentBase(
    IDictionary<string, string> tags,
    string? etag) : IDocument
{
    public abstract string PartitionKey { get; }

    public abstract string DocumentId { get; }

    public IDictionary<string, string> Tags { get; set; } = tags;

    public string? Etag { get; set; } = etag;

    public DateTimeOffset CreatedTime { get; set; }

    public DateTimeOffset ModifiedTime { get; set; }

    public DateTimeOffset? DeletedTime { get; set; }

    public int? TimeToLiveSeconds { get; set; }
}
