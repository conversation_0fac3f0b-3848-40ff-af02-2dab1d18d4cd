﻿// <copyright file="DeploymentDocument.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Collections.Generic;
using Microsoft.MachineLearning.ModelMesh.ModelHost.Contracts.Deployment;

namespace Microsoft.MachineLearning.ModelMesh.ModelHost.Types.DeploymentDocuments;

public class DeploymentDocument(
    string id,
    DeploymentRequestDto deploymentConfiguration,
    IDictionary<string, string> tags,
    string? etag) : DeploymentDocumentBase(tags, etag)
{
    public string Id { get; } = id;

    public DeploymentRequestDto DeploymentConfiguration { get; set; } = deploymentConfiguration;

    public List<string> ModelResourceIds { get; set; } = [];

    public string? OperationId { get; set; } = null;

    public override string PartitionKey => "Deployment";

    public override string DocumentId => Id;
}
