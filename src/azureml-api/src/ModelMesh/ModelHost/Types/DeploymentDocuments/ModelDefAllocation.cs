﻿// <copyright file="ModelDeploymentAllocationAction.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Collections.Generic;

namespace Microsoft.MachineLearning.ModelMesh.ModelHost.Types.DeploymentDocuments;

public class ModelDefAllocation
{
    public required string Action { get; set; }

    public required string InferenceModelResourceUri { get; set; }

    public IDictionary<string, CapacityUnits>? CapacityUnitsPerModality { get; set; }
}

public class CapacityUnits
{
    /// <summary>
    /// Gets or sets the number of PTU.
    /// </summary>
    public int PTU { get; set; }

    /// <summary>
    /// Gets or sets the SKU family.
    /// </summary>
    public string? SkuFamily { get; set; }
}
