﻿// <copyright file="AppInsightsTelemetrySinkProperties.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

namespace Microsoft.MachineLearning.ModelMesh.ModelHost.Types.Telemetry
{
    public class AppInsightsTelemetrySinkProperties
    {
        private string _connectionString = string.Empty;

        public string ConnectionString
        {
            get
            {
                if (string.IsNullOrEmpty(_connectionString))
                {
                    return $"InstrumentationKey={InstrumentationKey};";
                }
                else
                {
                    return _connectionString;
                }
            }

            set
            {
                _connectionString = value;
            }
        }

        public string InstrumentationKey { get; set; } = string.Empty;

        public string LogLevel { get; set; } = "info";
    }
}
