﻿// <copyright file="ModelHostRedisConfiguration.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>


using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace Microsoft.MachineLearning.ModelMesh.ModelHost.Types.Redis
{
    public class ModelHostRedisConnections
    {
        public RedisConnectionConfiguration LeaderElection { get; set; } = null!;
        public RedisConnectionConfiguration ReconciliationWaker { get; set; } = null!;

        public IDictionary<IPPublisherType, IPPRedisConnections> IPPRedisConnections { get; set; } = null!;
    }

    [JsonConverter(typeof(JsonStringEnumConverter))]
    public enum IPPublisherType
    {
        Unknown = 0,
        MaaS_IPP = 1,
        AOAI_IPP = 2,
    }

    public class IPPRedisConnections
    {
        public RedisConnectionConfiguration Heartbeat { get; set; } = null!;

        public RedisConnectionConfiguration MetricsReporting { get; set; } = null!;

        public RedisConnectionConfiguration ControlChannel { get; set; } = null!;
    }
}
