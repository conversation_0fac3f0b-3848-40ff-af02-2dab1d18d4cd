﻿// <copyright file="HotswapManagerDeploymentParameters.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Collections.Generic;
using Microsoft.MachineLearning.ModelMesh.ModelHost.Types.ConfigurationDocuments.Common.DeploymentParameters;

namespace Microsoft.MachineLearning.ModelMesh.ModelHost.Types.ConfigurationDocuments.HotswapManager.DeploymentParameters
{
    public class HotswapManagerDeploymentParameters(
        ConfigurationDeployability deployability,
        IDictionary<string, uint> minInstances,
        IDictionary<string, uint> maxInstances,
        IDictionary<string, uint> requestedInstances)
    {
        public ConfigurationDeployability Deployability { get; set; } = deployability;

        public IDictionary<string, uint> MinInstances { get; set; } = minInstances;

        public IDictionary<string, uint> MaxInstances { get; set; } = maxInstances;

        public IDictionary<string, uint> RequestedInstances { get; set; } = requestedInstances;

        public static HotswapManagerDeploymentParameters Default()
        {
            return new HotswapManagerDeploymentParameters(
                deployability: ConfigurationDeployability.Undeployable,
                minInstances: new Dictionary<string, uint>(),
                maxInstances: new Dictionary<string, uint>(),
                requestedInstances: new Dictionary<string, uint>());
        }

        /// <summary>
        ///  Returns true if the hotswap manager configuration is "live" - AKA, it is marked as deployable.
        /// </summary>
        /// <returns></returns>
        public bool IsActive()
        {
            return Deployability == ConfigurationDeployability.Deployable;
        }
    }
}
