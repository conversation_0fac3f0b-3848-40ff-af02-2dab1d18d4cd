﻿// <copyright file="HotswapManagerConfigurationDocument.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Collections.Generic;
using Microsoft.MachineLearning.ModelMesh.ModelHost.Types.ConfigurationDocuments.Common;
using Microsoft.MachineLearning.ModelMesh.ModelHost.Types.ConfigurationDocuments.HotswapManager.DeploymentParameters;

namespace Microsoft.MachineLearning.ModelMesh.ModelHost.Types.ConfigurationDocuments.HotswapManager
{
    public class HotswapManagerConfigurationDocument(
        string name,
        string version,
        string instanceType,
        string podTemplate,
        HotswapManagerDeploymentParameters deploymentParameters,
        EndpointConfiguration controlEndpoint,
        string hotswapEngine,
        UserAssignedIdentity hotswapManagerIdentity,
        IDictionary<string, string>? tags,
        string? etag) : ConfigurationDocumentBase(name, version, tags, etag)
    {
        public override string PartitionKey => GetPartitionKey();

        public override string DocumentId => GetDocumentId(Name, Version);

        public string InstanceType { get; set; } = instanceType;

        public string PodTemplate { get; set; } = podTemplate;

        public HotswapManagerDeploymentParameters DeploymentParameters { get; set; } = deploymentParameters;

        public EndpointConfiguration ControlEndpoint { get; set; } = controlEndpoint;

        public string HotswapEngine { get; set; } = hotswapEngine;

        public UserAssignedIdentity HotswapManagerIdentity { get; set; } = hotswapManagerIdentity;

        public static string GetPartitionKey() => Constants.HotswapManagerConfigurationPartitionKey;

        public static string GetDocumentId(string name, string version) => $"{GetPartitionKey()}-{name.ToLowerInvariant()}-{version.ToLowerInvariant()}";
    }
}
