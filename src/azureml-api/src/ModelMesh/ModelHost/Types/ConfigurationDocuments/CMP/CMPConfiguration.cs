﻿// <copyright file="CMPConfiguration.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Reflection;

namespace Microsoft.MachineLearning.ModelMesh.ModelHost.Types.ConfigurationDocuments.CMP
{
    // CMP Configurations reference: https://msdata.visualstudio.com/Vienna/_git/mir-cmp?path=/docs/config.md
    //     with default values set from common values from MaaS deployments
    public class CMPConfiguration()
    {
        // CMP (Container) Configs section

        // Image name and path for CMP. Should use "mir-cmp:default" unless testing
        public string ContentModerationProxyImage { get; set; } = "mir-cmp:default";

        // Port on CMP container to forward requests to
        public int ContentModerationProxyPort { get; set; } = 4999;

        // Client id of the identity to use for calling RAI endpoint
        public string ModerationServerUAIClientId { get; set; } = String.Empty;

        // Flag to use AML specific error message for prompt blocked requests. Used to distinguish error message between AOAI and MaaS models
        public bool UseAmlContentFilterError { get; set; } = true;

        // Model Configs section

        // Hostname of the model provider container
        public string ModelServerHost { get; set; } = "http://localhost";

        // Port on the model provider container to forward requests to. CMP -> model provider container
        public int ModelServerPort { get; set; } = 5000;

        // Moderation Config section

        // Time in millisecond to wait for completion moderation to finish after last byte from model
        public int ModerationCompletionTimeout { get; set; } = 5000;

        // Boolean flag for enabling prompt context mode, which provides RAI with prompt context in completion moderation
        public bool ModerationEnablePromptContext { get; set; } = true;

        // Default maximum length in runes that CMP will supply to RAI as prompt context. If prompt goes over this length the final runes up to this length is supplied as context
        public int ModerationMaxPromptContextLength { get; set; } = 2000;

        // Time to wait in milliseconds before cancelling prompt moderation stream after engine responds with first token byte
        public int ModerationPromptTimeoutAfterEngine { get; set; } = 100;

        // Minimum time to wait in milliseconds since request start time before cancelling prompt moderation stream
        public int ModerationPromptMinTimeout { get; set; } = 100;

        // RAI Config section

        // RAI orchestrator endpoint to call to for moderation requests. Should include the port info. ex) rai-orchestrator-grpc-prod-1.eastus2.inference.ml.azure.com:443
        public string ModerationServerUrl { get; set; } = String.Empty;

        // Default policy id to use if no policy id was provided in the request. Note that "nil" policy id has a special passthrough meaning
        public string ModerationPolicy { get; set; } = "134";

        public IDictionary<string, string> AddCMPConfigurationEnvironmentVariables(IDictionary<string, string> envVars)
        {
            IDictionary<string, string> envVarsCopy = new Dictionary<string, string>(envVars);

            foreach (PropertyInfo property in this.GetType().GetProperties())
            {
                string key = property.Name;
                var value = property.GetValue(this);
                envVarsCopy[key] = value?.ToString() ?? String.Empty;
            }
            return envVarsCopy;
        }
    }
}
