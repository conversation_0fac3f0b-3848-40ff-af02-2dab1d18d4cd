﻿// <copyright file="DistributorDeploymentRecordDocument.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;

namespace Microsoft.MachineLearning.ModelMesh.ModelHost.Types.ConfigurationDocuments.DistributorDeployment
{
    public class DistributorDeploymentRecordDocument(
        string clusterName,
        Guid clusterSubscriptionId,
        string clusterRegion,
        string modelNamespace,
        string deploymentId,
        string distributorName,
        string distributorConfigurationName,
        string distributorConfigurationVersion,
        Uri instanceServiceUri,
        string imResourceId,
        string? etag) : ConfigurationDocumentBase(distributorName, string.Empty, null, etag)
    {
        public override string PartitionKey => GetPartitionKey();

        public override string DocumentId => GetDocumentId(Name);

        public string ClusterName { get; set; } = clusterName;

        public Guid ClusterSubscriptionId { get; set; } = clusterSubscriptionId;

        public string ClusterRegion { get; set; } = clusterRegion;

        public string ModelNamespace { get; set; } = modelNamespace;

        public string DeploymentId { get; } = deploymentId;

        public string DistributorConfigurationName { get; } = distributorConfigurationName;

        public string DistributorConfigurationVersion { get; } = distributorConfigurationVersion;

        public Uri InstanceServiceUri { get; set; } = instanceServiceUri;

        public string IMResourceId { get; set; } = imResourceId;

        public static string GetPartitionKey() => Constants.DistributorDeploymentRecordPartitionKey;

        public static string GetDocumentId(string name) => $"{GetPartitionKey()}-{name.ToLowerInvariant()}";

    }
}
