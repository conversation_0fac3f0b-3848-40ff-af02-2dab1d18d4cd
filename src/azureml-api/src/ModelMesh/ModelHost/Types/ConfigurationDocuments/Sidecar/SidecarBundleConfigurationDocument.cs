﻿// <copyright file="SidecarBundleConfigurationDocument.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using Microsoft.MachineLearning.ModelMesh.ModelHost.Types.ConfigurationDocuments.Common;

namespace Microsoft.MachineLearning.ModelMesh.ModelHost.Types.ConfigurationDocuments.Sidecar;

public class SidecarBundleConfigurationDocument(
    string name,
    string version,
    bool isDeployable,
    string modelInstanceContainerSpec,
    Uri modelInstanceArtifactsUri,
    string baseImageIdentifier,
    string? cmpContainerSpec,
    UserAssignedIdentity? sidecarBundleIdentity,
    IDictionary<string, string>? tags,
    string? etag) : ConfigurationDocumentBase(name, version, tags, etag)
{
    public override string PartitionKey => GetPartitionKey();

    public override string DocumentId => GetDocumentId(Name, Version);

    public bool IsDeployable { get; set; } = isDeployable;

    public string ModelInstanceContainerSpec { get; set; } = modelInstanceContainerSpec;

    public string? CMPContainerSpec { get; set; } = cmpContainerSpec;

    public Uri ModelInstanceArtifactsUri { get; set; } = modelInstanceArtifactsUri;

    public string BaseImageIdentifier { get; set; } = baseImageIdentifier;

    public UserAssignedIdentity? SidecarBundleIdentity { get; set; } = sidecarBundleIdentity;

    public static string GetPartitionKey() => Constants.SidecarBundleConfigurationPartitionKey;

    public static string GetDocumentId(string name, string version) => $"{GetPartitionKey()}-{name.ToLowerInvariant()}-{version.ToLowerInvariant()}";
}
