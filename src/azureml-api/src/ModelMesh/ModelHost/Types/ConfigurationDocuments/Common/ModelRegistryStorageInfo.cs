﻿// <copyright file="ModelRegistryStorageInfo.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;

namespace Microsoft.MachineLearning.ModelMesh.ModelHost.Types.ConfigurationDocuments.Common
{
    public class ModelRegistryStorageInfo(
        Guid registrySubscriptionId,
        string registryResourceGroupName,
        string registryStorageAccountName,
        string registryContainerName,
        string manifestFilePath,
        string mountPath,
        bool requiresPEForRegistryAccess)
    {
        public Guid RegistrySubscriptionId { get; set; } = registrySubscriptionId;

        public string RegistryResourceGroupName { get; set; } = registryResourceGroupName;

        public string RegistryStorageAccountName { get; set; } = registryStorageAccountName;

        public string RegistryContainerName { get; set; } = registryContainerName;

        public string ManifestFilePath { get; set; } = manifestFilePath;

        public string MountPath { get; set; } = mountPath;

        public bool RequiresPEForRegistryAccess { get; set; } = requiresPEForRegistryAccess;
    }
}
