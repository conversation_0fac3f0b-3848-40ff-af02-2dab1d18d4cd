﻿// <copyright file="UserAssignedIdentity.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;

namespace Microsoft.MachineLearning.ModelMesh.ModelHost.Types.ConfigurationDocuments.Common
{
    public class UserAssignedIdentity(
        string resourceId,
        Guid clientId,
        Guid principalId)
    {
        public string ResourceId { get; set; } = resourceId;

        public Guid ClientId { get; set; } = clientId;

        public Guid PrincipalId { get; set; } = principalId;
    }
}
