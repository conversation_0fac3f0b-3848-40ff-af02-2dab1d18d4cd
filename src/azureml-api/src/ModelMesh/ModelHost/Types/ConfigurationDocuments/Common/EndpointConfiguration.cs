﻿// <copyright file="EndpointConfiguration.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>


using System;

namespace Microsoft.MachineLearning.ModelMesh.ModelHost.Types.ConfigurationDocuments.Common
{
    public class EndpointConfiguration
        (string scheme,
        string path,
        int port)
    {
        public string Scheme { get; set; } = scheme;

        public string Path { get; set; } = path;

        public int Port { get; set; } = port;

        public Uri GetEndpointUrl(string host)
        {
            return new($"{Scheme}{Uri.SchemeDelimiter}{host}:{Port}/{Path.TrimStart('/')}".ToLowerInvariant());
        }
    }
}
