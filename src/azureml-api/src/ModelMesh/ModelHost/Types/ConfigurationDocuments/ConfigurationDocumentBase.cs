﻿// <copyright file="ConfigurationDocumentBase.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;

namespace Microsoft.MachineLearning.ModelMesh.ModelHost.Types.ConfigurationDocuments
{
    public abstract class ConfigurationDocumentBase(
        string name,
        string version,
        IDictionary<string, string>? tags,
        string? etag) : IConfigurationDocument
    {
        public abstract string PartitionKey { get; }

        public abstract string DocumentId { get; }

        public string Name { get; set; } = name;

        public string Version { get; set; } = version;

        public IDictionary<string, string>? Tags { get; set; } = tags;

        public string? Etag { get; set; } = etag;

        public DateTimeOffset CreatedTime { get; set; }

        public DateTimeOffset ModifiedTime { get; set; }

        public DateTimeOffset? DeletedTime { get; set; }

        public int? TimeToLiveSeconds { get; set; }
    }
}
