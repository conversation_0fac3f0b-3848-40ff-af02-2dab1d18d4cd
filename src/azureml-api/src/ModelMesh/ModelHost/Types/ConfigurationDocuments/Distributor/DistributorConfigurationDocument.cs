﻿// <copyright file="DistributorConfigurationDocument.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Collections.Generic;
using Microsoft.MachineLearning.ModelMesh.ModelHost.Types.ConfigurationDocuments.Common;

namespace Microsoft.MachineLearning.ModelMesh.ModelHost.Types.ConfigurationDocuments.Distributor
{
    public class DistributorConfigurationDocument(
        string name,
        string version,
        bool isDeployable,
        string? podTemplate,
        string? deploymentTemplateUri,
        EndpointConfiguration instanceEndpoint,
        EndpointConfiguration inferenceEndpoint,
        IDictionary<string, string>? tags,
        string? etag) : ConfigurationDocumentBase(name, version, tags, etag)
    {
        public override string PartitionKey => GetPartitionKey();

        public override string DocumentId => GetDocumentId(Name, Version);

        public string? PodTemplate { get; set; } = podTemplate;

        public string? DeploymentTemplateUri { get; set; } = deploymentTemplateUri;

        public EndpointConfiguration InstanceEndpoint { get; set; } = instanceEndpoint;

        public EndpointConfiguration InferenceEndpoint { get; set; } = inferenceEndpoint;

        public bool IsDeployable { get; set; } = isDeployable;

        public static string GetPartitionKey() => Constants.DistributorConfigurationPartitionKey;

        public static string GetDocumentId(string name, string version) => $"{GetPartitionKey()}-{name.ToLowerInvariant()}-{version.ToLowerInvariant()}";
    }
}
