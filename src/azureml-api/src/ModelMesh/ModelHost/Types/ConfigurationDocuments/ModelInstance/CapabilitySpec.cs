﻿// <copyright file="CapabilitySpec.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Collections.Generic;

namespace Microsoft.MachineLearning.ModelMesh.ModelHost.Types.ConfigurationDocuments.ModelInstance
{
    public class CapabilitySpec(
        string capabilityName,
        string groupId,
        string groupName,
        bool primary,
        IList<string> expectedCapabilities)
    {
        public string CapabilityName { get; set; } = capabilityName;

        public string GroupId { get; set; } = groupId;

        public string GroupName { get; set; } = groupName;

        public bool Primary { get; set; } = primary;

        public IList<string> ExpectedCapabilities { get; set; } = expectedCapabilities;
    }
}
