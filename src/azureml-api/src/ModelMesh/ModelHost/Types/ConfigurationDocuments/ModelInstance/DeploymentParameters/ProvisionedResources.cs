﻿// <copyright file="ProvisionedResources.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Collections.Generic;

namespace Microsoft.MachineLearning.ModelMesh.ModelHost.Types.ConfigurationDocuments.ModelInstance.DeploymentParameters
{
    public class ProvisionedResources(
        IDictionary<string, string> privateEndpointResourceIds,
        IDictionary<string, string> storageCacheContainerResourceIds)
    {
        // key is the assetId, value is the resourceId
        public IDictionary<string, string> PrivateEndpointResourceIds { get; set; } = privateEndpointResourceIds;

        public IDictionary<string, string> StorageCacheContainerResourceIds { get; set; } = storageCacheContainerResourceIds;
    }
}
