﻿// <copyright file="InferenceEndpointConfiguration.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Microsoft.MachineLearning.ModelMesh.ModelHost.Types.ConfigurationDocuments.Common;

namespace Microsoft.MachineLearning.ModelMesh.ModelHost.Types.ConfigurationDocuments.ModelInstance
{
    public class InferenceEndpointConfiguration(
        string advertisedRoute,
        string scheme,
        string path,
        int port,
        InferenceContractType contractType) : EndpointConfiguration(scheme, path, port)
    {
        public string AdvertisedRoute { get; set; } = advertisedRoute;

        public InferenceContractType ContractType { get; set; } = contractType;
    }
}
