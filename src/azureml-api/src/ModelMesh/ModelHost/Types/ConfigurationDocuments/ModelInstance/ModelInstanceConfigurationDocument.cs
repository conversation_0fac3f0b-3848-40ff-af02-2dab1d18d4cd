﻿// <copyright file="ModelInstanceConfigurationDocument.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Microsoft.MachineLearning.ModelMesh.ModelHost.Types.ConfigurationDocuments.Common;
using Microsoft.MachineLearning.ModelMesh.ModelHost.Types.ConfigurationDocuments.ModelInstance.DeploymentParameters;
using System.Collections.Generic;

namespace Microsoft.MachineLearning.ModelMesh.ModelHost.Types.ConfigurationDocuments.ModelInstance
{
    public class ModelInstanceConfigurationDocument(
        string name,
        string version,
        string publisherName,
        string modelId,
        IList<InstanceTemplate> instanceTemplates,
        ModelInstanceConfigurationDeploymentParameters deploymentParameters,
        ICollection<InferenceEndpointConfiguration> inferenceEndpoints,
        MetricsEndpointConfiguration metricsEndpoint,
        IDictionary<string, ModelRegistryStorageInfo> modelRegistryAssets,
        string? hotswapEngine,
        CapabilitySpec? capabilitySpec,
        UserAssignedIdentity modelInstanceIdentity,
        IDictionary<string, string>? tags,
        string? etag) : ConfigurationDocumentBase(name, version, tags, etag)
    {
        public override string PartitionKey => GetPartitionKey();

        public override string DocumentId => GetDocumentId(Name, Version);

        public string PublisherName { get; set; } = publisherName;

        public string ModelId { get; set; } = modelId;

        public IList<InstanceTemplate> InstanceTemplates { get; set; } = instanceTemplates;

        public ModelInstanceConfigurationDeploymentParameters DeploymentParameters { get; set; } = deploymentParameters;

        public ICollection<InferenceEndpointConfiguration> InferenceEndpoints { get; set; } = inferenceEndpoints;

        public MetricsEndpointConfiguration MetricsEndpoint { get; set; } = metricsEndpoint;

        public IDictionary<string, ModelRegistryStorageInfo> ModelRegistryAssets { get; set; } = modelRegistryAssets;

        public string? HotswapEngine { get; set; } = hotswapEngine;

        public CapabilitySpec? CapabilitySpec { get; set; } = capabilitySpec;

        public UserAssignedIdentity ModelInstanceIdentity { get; set; } = modelInstanceIdentity;

        public static string GetPartitionKey() => Constants.ModelInstanceConfigurationPartitionKey;

        public static string GetDocumentId(string name, string version) => $"{GetPartitionKey()}-{name.ToLowerInvariant()}-{version.ToLowerInvariant()}";
    }
}
