﻿// <copyright file="InferenceContractType.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Text.Json.Serialization;

namespace Microsoft.MachineLearning.ModelMesh.ModelHost.Types.ConfigurationDocuments.ModelInstance
{
    [JsonConverter(typeof(JsonStringEnumConverter))]
    public enum InferenceContractType
    {
        CommonApiChatCompletions,
        VllmChatCompletions,
        TrtllmChatCompletions,
        LlgtrtChatCompletions,
        CommonApiEmbeddings,
        PassThrough,
    }
}
