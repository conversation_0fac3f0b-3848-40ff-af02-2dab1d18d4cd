﻿// <copyright file="ModelInstanceConfigurationDeploymentParameters.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Collections.Generic;
using Microsoft.MachineLearning.ModelMesh.ModelHost.Types.ConfigurationDocuments.Common.DeploymentParameters;

namespace Microsoft.MachineLearning.ModelMesh.ModelHost.Types.ConfigurationDocuments.ModelInstance.DeploymentParameters
{
    public class ModelInstanceConfigurationDeploymentParameters(
        ConfigurationStatus status,
        ConfigurationDeployability deployability,
        IDictionary<string, uint> minInstances,
        IDictionary<string, uint> maxInstances,
        IDictionary<string, uint> requestedDirectInstances,
        IDictionary<string, uint> requestedHotswapInstances,
        ProvisionedResources provisionedResources)
    {

        public ConfigurationStatus Status { get; set; } = status;

        public ConfigurationDeployability Deployability { get; set; } = deployability;

        public IDictionary<string, uint> MinInstances { get; set; } = minInstances;

        public IDictionary<string, uint> MaxInstances { get; set; } = maxInstances;

        public IDictionary<string, uint> RequestedDirectInstances { get; set; } = requestedDirectInstances;

        public IDictionary<string, uint> RequestedHotswapInstances { get; set; } = requestedHotswapInstances;

        public ProvisionedResources ProvisionedResources { get; set; } = provisionedResources;

        public static ModelInstanceConfigurationDeploymentParameters Default()
        {
            return new ModelInstanceConfigurationDeploymentParameters(
                status: ConfigurationStatus.Registered,
                deployability: ConfigurationDeployability.Undeployable,
                minInstances: new Dictionary<string, uint>(),
                maxInstances: new Dictionary<string, uint>(),
                requestedDirectInstances: new Dictionary<string, uint>(),
                requestedHotswapInstances: new Dictionary<string, uint>(),
                provisionedResources: new ProvisionedResources(new Dictionary<string, string>(), new Dictionary<string, string>()));
        }

        /// <summary>
        ///  Returns true if the model instance configuration is "live" - AKA, model assets are staged, or the instance is marked as deployable (meaning assets will be staged).
        /// </summary>
        /// <returns></returns>
        public bool IsActive()
        {
            return Status == ConfigurationStatus.Deployed || Deployability == ConfigurationDeployability.Deployable;
        }

        public bool IsReadyForResoucesProvisioning()
        {
            return Status == ConfigurationStatus.Registered && Deployability == ConfigurationDeployability.Deployable;
        }

        public bool IsStagingForResoucesProvisioning()
        {
            return Status == ConfigurationStatus.Staging && Deployability == ConfigurationDeployability.Deployable;
        }

        public bool IsReadyForResoucesCleanup()
        {
            return Status == ConfigurationStatus.Deployed
                && Deployability == ConfigurationDeployability.Undeployable;
        }

        public bool IsStagingForResoucesCleanup()
        {
            return Status == ConfigurationStatus.Staging
                && Deployability == ConfigurationDeployability.Undeployable;
        }
    }
}
