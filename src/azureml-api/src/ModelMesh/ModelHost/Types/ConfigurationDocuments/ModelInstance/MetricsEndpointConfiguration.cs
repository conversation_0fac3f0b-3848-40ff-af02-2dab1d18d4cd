﻿// <copyright file="MetricsEndpointConfiguration.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Microsoft.MachineLearning.ModelMesh.ModelHost.Types.ConfigurationDocuments.Common;

namespace Microsoft.MachineLearning.ModelMesh.ModelHost.Types.ConfigurationDocuments.ModelInstance
{
    public class MetricsEndpointConfiguration(
        string scheme,
        string path,
        int port,
        MetricsContractType contractType) : EndpointConfiguration(scheme, path, port)
    {
        public MetricsContractType ContractType { get; set; } = contractType;
    }
}
