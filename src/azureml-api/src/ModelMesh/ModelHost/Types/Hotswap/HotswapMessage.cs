﻿// <copyright file="HotswapMessageBase.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;
using Microsoft.MachineLearning.ModelMesh.ModelHost.Types.ConfigurationDocuments.ModelInstance;

namespace Microsoft.MachineLearning.ModelMesh.ModelHost.Types.Hotswap;

[JsonConverter(typeof(JsonStringEnumConverter))]
public enum HotswapMessageType
{
    Load,
    Unload
}

[JsonDerivedType(typeof(LoadMessage))]
[JsonDerivedType(typeof(UnloadMessage))]
public abstract class HotswapMessageBase()
{
    [JsonPropertyName("type")]
    public abstract HotswapMessageType MessageType { get; }
}

public class UnloadMessage : HotswapMessageBase
{
    [JsonPropertyName("type")]
    public override HotswapMessageType MessageType => HotswapMessageType.Unload;
}

public class LoadMessage : HotswapMessageBase
{
    [JsonPropertyName("type")]
    public override HotswapMessageType MessageType => HotswapMessageType.Load;

    [JsonPropertyName("modelName")]
    public string ModelName { get; set; } = "default";

    [JsonPropertyName("configName")]
    public string ConfigName { get; set; } = string.Empty;

    [JsonPropertyName("configVersion")]
    public string ConfigVersion { get; set; } = string.Empty;

    [JsonPropertyName("inferenceEndpoints")]
    public ICollection<InferenceEndpoint> InferenceEndpoints { get; set; } = null!;

    [JsonPropertyName("inferenceProbeEndpoints")]
    public ICollection<ProbeEndpoint> InferenceProbeEndpoints { get; set; } = null!;

    [JsonPropertyName("metricsEndpoint")]
    public MetricsEndpoint MetricsEndpoint { get; set; } = null!;

    [JsonPropertyName("metricsReportingIntervalMs")]
    public int MetricsReportingIntervalMs { get; set; } = 3000;

    [JsonPropertyName("hotswap")]
    public HotswapConfig? HotswapConfig { get; set; } = null;
}

public class HotswapConfig()
{
    [JsonPropertyName("server")]
    public Uri Server { get; set; } = null!;

    [JsonPropertyName("serverCommand")]
    public ServerCommand? ServerCommand { get; set; } = null;
}

public class ServerCommand
{
    /// <summary>
    /// A list of symlinks to create before running the server command. The keys are source paths and the values are target paths - `ln -s <key> <value>` or `std::os::unix::fs::symlink("<key>", "<value>")?;`.
    /// </summary>
    [JsonPropertyName("links")]
    public IDictionary<string, string> Links { get; set; } = null!;

    [JsonPropertyName("program")]
    public string Command { get; set; } = null!;

    [JsonPropertyName("args")]
    public IList<string> Arguments { get; set; } = [];

    [JsonPropertyName("environment")]
    public IDictionary<string, string> EnvironmentVariables { get; set; } = null!;
}

public class EndpointBase
{
    [JsonPropertyName("uri")]
    public Uri Endpoint { get; set; } = null!;
}

public class InferenceEndpoint : EndpointBase
{
    [JsonPropertyName("advertisedRoute")]
    public string AdvertisedRoute { get; set; } = null!;

    [JsonPropertyName("contractType")]
    public InferenceContractType ContractType { get; set; }
}

public class MetricsEndpoint : EndpointBase
{
    [JsonPropertyName("contractType")]
    public MetricsContractType ContractType { get; set; }
}

public class ProbeEndpoint : EndpointBase
{
    [JsonPropertyName("successThreshold")]
    public long SuccessThreshold { get; set; }

    [JsonPropertyName("failureThreshold")]
    public long FailureThreshold { get; set; }

    [JsonPropertyName("healthyProbeIntervalSec")]
    public long HealthyProbeIntervalSec { get; set; }

    [JsonPropertyName("unhealthyProbeIntervalSec")]
    public long UnhealthyProbeIntervalSec { get; set; }

    [JsonPropertyName("initialDelaySec")]
    public long InitialDelaySec { get; set; }

    [JsonPropertyName("timeoutSec")]
    public long TimeoutSec { get; set; }
}
