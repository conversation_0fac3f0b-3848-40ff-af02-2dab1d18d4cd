﻿// <copyright file="InstanceIdentityConfiguration.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Collections.Generic;
using Microsoft.MachineLearning.ModelMesh.ModelHost.Types.ConfigurationDocuments.Common;
using Microsoft.MachineLearning.ModelMesh.ModelHost.Types.Redis;

namespace Microsoft.MachineLearning.ModelMesh.ModelHost.Types.InstanceIdentities
{
    public class InstanceIdentityConfiguration
    {
        public IDictionary<IPPublisherType, IdentityConfiguration> InfrastructureIdentities { get; set; } = new Dictionary<IPPublisherType, IdentityConfiguration>();

        public UserAssignedIdentity ModelMeshRegistryReaderIdentity { get; set; } = null!;
    }

    public class IdentityConfiguration
    {
        public string ModelDistributorIdentityArmId { get; set; } = string.Empty;

        public string ModelInstanceIdentityArmId { get; set; } = string.Empty;
    }
}
