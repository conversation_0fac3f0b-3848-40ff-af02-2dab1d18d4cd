﻿// <copyright file="DeploymentModelResource.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Microsoft.Azure.Management.Singularity.Models;

namespace Microsoft.MachineLearning.ModelMesh.ModelHost.Types.DeploymentModelResource;

public class DeploymentModelResource(
    string modelResourceId,
    string modelResourceName,
    string clusterIp,
    string clusterFqdn,
    string provisioningState,
    string status,
    string deploymentType,
    string deploymentVersion,
    string deploymentId,
    ResourceSystemData systemData)
{
    public string Id => modelResourceId;

    public string Name => modelResourceName;

    public string ClusterIP => clusterIp;

    public string ClusterFQDN => clusterFqdn;

    public string ProvisioningState => provisioningState;

    public string Status => status;

    public string DeploymentType => deploymentType;

    public string DeploymentVersion => deploymentVersion;

    public string DeploymentId => deploymentId;

    public ResourceSystemData SystemData => systemData;
}
