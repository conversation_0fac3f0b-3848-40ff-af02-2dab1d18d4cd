﻿// <copyright file="AllocationMessage.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace Microsoft.MachineLearning.ModelMesh.ModelHost.Types.Allocation;

[JsonConverter(typeof(JsonStringEnumConverter))]
public enum AllocationActionType
{
    Attach,
    Detach
}

[JsonDerivedType(typeof(AttachMessage))]
[JsonDerivedType(typeof(DetachMessage))]
public abstract class AllocationMessageBase
{
    [JsonPropertyName("action")]
    public abstract AllocationActionType Action { get; }
}

public class AttachMessage : AllocationMessageBase
{
    [JsonPropertyName("action")]
    public override AllocationActionType Action => AllocationActionType.Attach;

    [JsonPropertyName("inferenceModelResourceUri")]
    public string InferenceModelResourceUri { get; set; } = string.Empty;


    [JsonPropertyName("capacityUnitsPerModality")]
    public IDictionary<string, CapacityUnits>? CapacityUnitsPerModality { get; set; }
}

public class DetachMessage : AllocationMessageBase
{
    [JsonPropertyName("action")]
    public override AllocationActionType Action => AllocationActionType.Detach;

    [JsonPropertyName("inferenceModelResourceUri")]
    public string InferenceModelResourceUri { get; set; } = string.Empty;
}

public class CapacityUnits
{
    [JsonPropertyName("ptu")]
    public int PTU { get; set; }

    [JsonPropertyName("skuFamily")]
    public string? SkuFamily { get; set; }
}
