﻿// <copyright file="Constants.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

namespace Microsoft.MachineLearning.ModelMesh.ModelHost.Types
{
    public class Constants
    {
        public const string AppInsightsSinkPropertiesSection = "AppInsightsSink";

        public const string SingularityAccountsSection = "SingularityAccounts";
        public const string AOAIDeploymentConfigurationSection = "AOAIDeploymentConfiguration";

        public const string HeartbeatStatusRunning = "running";
        public const int HeartbeatTimeoutInSeconds = 60;

        public const string HotswapEntryName = "default";
        public const string AllocationEntryName = "default";

        public const string ModelInstanceConfigurationPartitionKey = "ModelInstanceConfiguration";
        public const string DistributorConfigurationPartitionKey = "DistributorConfiguration";
        public const string HotswapManagerConfigurationPartitionKey = "HotswapManagerConfiguration";
        public const string SidecarBundleConfigurationPartitionKey = "SidecarBundleConfiguration";

        public const string DistributorDeploymentRecordPartitionKey = "DistributorDeploymentRecord";

        public const string ModelHostDeploymentPropertiesPrefix = "MODELHOST_DEPLOYMENT_PROPERTIES_";
        public const string ModelHostDeploymentEngineId = "MODELHOST_DEPLOYMENT_ENGINE_ID";
        public const string ModelHostDeploymentModelId = "MODELHOST_DEPLOYMENT_MODEL_ID";
        public const string ModelHostDeploymentRegion = "MODELHOST_DEPLOYMENT_REGION";
        public const string ModelHostDeploymentSubscriptionId = "MODELHOST_DEPLOYMENT_SUBSCRIPTION_ID";
        public const string ModelHostDeploymentInstanceType = "MODELHOST_DEPLOYMENT_INSTANCE_TYPE";
        public const string ModelHostPrefix = "MODELHOST_DEPLOYMENT_";

        public const string ModelPoolDeploymentGroupTag = "FleetOperatorDeploymentGroupName";
        public const string ModelPoolPreemptableDGPrefix = "preemptable-";
        public const string ModelPoolDeploymentSlaTier = "FleetOperatorSkuTier";

        public const string ModelHostSkipSidecarInjection = "MODELHOST_SKIP_SIDECAR_INJECTION";
        public const string ModelHostSidecarInjectionConfigName = "aoai-model-instance-sidecar";

        public const string ModelHostSidecarImageOverride = "MODELHOST_SIDECAR_IMAGE_OVERRIDE";
        public const string ModelHostOverrideRegistryReaderIdentityScope = "MODELHOST_OVERRIDE_ACR_IDENTITY_SCOPE";
        public const string ModelHostOverrideRegistryReaderIdentityClientId = "MODELHOST_OVERRIDE_ACR_IDENTITY_CLIENT_ID";
    }
}
