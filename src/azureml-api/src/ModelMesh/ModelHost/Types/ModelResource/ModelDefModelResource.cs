﻿// <copyright file="ModelDefModelResource.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

namespace Microsoft.MachineLearning.ModelMesh.ModelHost.Types.ModelResource;

public class ModelDefModelResource(
    string id,
    string name,
    string configurationName,
    string configurationVersion,
    string provisioningState,
    string status,
    string clusterIp,
    string fqdn) : ModelResourceBase(ModelResourceType.ModelDef, id, name, configurationName, configurationVersion, provisioningState, status, clusterIp, fqdn)
{
}
