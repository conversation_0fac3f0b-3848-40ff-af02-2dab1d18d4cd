﻿// <copyright file="ModelInstanceModelResource.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

namespace Microsoft.MachineLearning.ModelMesh.ModelHost.Types.ModelResource;

public class ModelInstanceModelResource(
    string id,
    string name,
    string configurationName,
    string configurationVersion,
    string sidecarBundleName,
    string sidecarBundleVersion,
    string provisioningState,
    string status,
    string clusterIp,
    string fqdn) : ModelResourceBase(ModelResourceType.ModelInstance, id, name, configurationName, configurationVersion, provisioningState, status, clusterIp, fqdn)
{
    public string SidecarBundleConfigurationName => sidecarBundleName;

    public string SidecarBundleConfigurationVersion => sidecarBundleVersion;
}
