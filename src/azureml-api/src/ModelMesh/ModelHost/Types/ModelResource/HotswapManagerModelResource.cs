﻿// <copyright file="HotswapManagerModelResource.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Collections.Generic;

namespace Microsoft.MachineLearning.ModelMesh.ModelHost.Types.ModelResource;

public class HotswapManagerModelResource(
    string id,
    string name,
    string configurationName,
    string configurationVersion,
    string sidecarBundleName,
    string sidecarBundleVersion,
    string provisioningState,
    string status,
    string clusterIp,
    string fqdn,
    string instanceType,
    string hotswapEngine,
    ICollection<string> mountedStorageCacheContainers) : ModelResourceBase(ModelResourceType.HotswapManager, id, name, configurationName, configurationVersion, provisioningState, status, clusterIp, fqdn)
{
    public string InstanceType => instanceType;

    public string HotswapEngine => hotswapEngine;

    public string SidecarBundleConfigurationName => sidecarBundleName;

    public string SidecarBundleConfigurationVersion => sidecarBundleVersion;

    public ICollection<string> MountedStorageCacheContainers => mountedStorageCacheContainers;
}
