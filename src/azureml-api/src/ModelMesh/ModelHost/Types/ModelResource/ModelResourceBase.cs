﻿// <copyright file="ModelResourceBase.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Collections.Generic;

namespace Microsoft.MachineLearning.ModelMesh.ModelHost.Types.ModelResource
{
    public abstract class ModelResourceBase(
        ModelResourceType type,
        string id,
        string name,
        string configurationName,
        string configurationVersion,
        string provisioningState,
        string status,
        string clusterIp,
        string fqdn)
    {
        public ModelResourceType Type => type;

        public string Id => id;

        public string Name => name;

        public string ConfigurationName => configurationName;

        public string ConfigurationVersion => configurationVersion;

        public string ProvisioningState => provisioningState;

        public string Status => status;

        public string ClusterIp => clusterIp;

        public string CluterFQDN => fqdn;

        public IDictionary<string, string> Tags { get; set; } = new Dictionary<string, string>();
    }
}
