using System;
using System.Globalization;
using System.Threading.Tasks;
using Azure.Core;
using Microsoft.Extensions.Options;
using Microsoft.MachineLearning.Common.Core.Configurations;
using Microsoft.MachineLearning.Common.Core.Exceptions;
using Microsoft.MachineLearning.DataStore.Contracts;
using Microsoft.MachineLearning.Pipeline.Contracts;
using Microsoft.MachineLearning.Pipeline.Services.Common.Logging;
using Microsoft.MachineLearning.Pipeline.Services.Common.Utilities;
using Microsoft.MachineLearning.Studio.MiddleTier.Helpers.PromptFlow;
using Microsoft.MachineLearning.Studio.MiddleTier.Interfaces;
using Microsoft.MachineLearning.Studio.MiddleTier.Services.PromptFlow;
using Microsoft.MachineLearning.Studio.MiddleTier.WebApiClient;
using Microsoft.MachineLearning.Studio.MiddleTierCommon.ErrorHandling.Exceptions;
using Microsoft.MachineLearning.Studio.MiddleTierCommon.Utilities;

namespace Microsoft.MachineLearning.Studio.MiddleTier.Helpers
{
    public class BlobStorageProviderFactory
    {
        private readonly IDataStoresManagement _dataStoresManagement;
        private readonly PipelineApiCaller _pipelineApiCaller;
        private readonly IOptionsMonitor<AzureConstantsConfiguration> _azureConstants;
        private bool? getMsiDirectly;
        public BlobStorageProviderFactory(
            IDataStoresManagement dataStoresManagement,
            PipelineApiCaller pipelineApiCaller,
            IOptionsMonitor<AzureConstantsConfiguration> azureConstants)
        {
            _dataStoresManagement = dataStoresManagement;
            _pipelineApiCaller = pipelineApiCaller;
            _azureConstants = azureConstants;
        }

        public bool GetMsiDirectly
        {
            get
            {
                return getMsiDirectly ?? false;
            }

            set
            {
                getMsiDirectly = value;
            }
        }

        #region BlobStorageProvider
        public async Task<BlobStorageProvider> GetSnapshotBlobStorageProviderAsync()
        {
            var result = await GetBlobStorageProviderWithOboTokenFallback(PipelineConstants.DefaultBlobDataStore).ConfigureAwait(false);
            return result.blobStorageProvider;
        }

        public async Task<(BlobStorageProvider blobStorageProvider, DataStoreDto dataStoreDto)> GetBlobStorageProviderWithOboTokenFallback(
            string? dataStoreName = null,
            bool system = false,
            bool? copyModeComponent = null)
        {
            DataStoreDto dataStore;
            BlobStorageProvider blobStorageProvider;
            if (string.IsNullOrEmpty(dataStoreName))
            {
                dataStore = await _dataStoresManagement.GetDefaultDataStoreDto(system: system).ConfigureAwait(false);
            }
            else
            {
                dataStore = await _dataStoresManagement.GetDataStoreDto(dataStoreName, system: system).ConfigureAwait(false);
                if (dataStore == null)
                {
                    Tracer.Informational($"Not found datastore {dataStoreName}");
                    throw new BaseException(new DataStoreNotFound().ToBaseError(target: null, dataStoreName));
                }
            }

            // Datastore with type "AzureSqlDatabase" have no AzureStorageSection
            // Office is using AzureDataLakeGen2 as output datastore, need to allow it
            if (dataStore.DataStoreType != DataStoreType.AzureBlob && dataStore.DataStoreType != DataStoreType.AzureDataLakeGen2)
            {
                Tracer.Warning($"Datastore {dataStore.Name} has type {dataStore.DataStoreType} which is not supported.");
                throw new BaseException(new DataStoreNotSupported().ToBaseError(target: null, dataStore.Name, dataStore.DataStoreType));
            }

            if (dataStore.AzureStorageSection.AreWorkspaceManagedIdentitiesAllowed || dataStore.AzureStorageSection.CredentialType != AzureStorageCredentialTypes.None)
            {
                Tracer.Informational($"Datastore {dataStore.Name} has access to the storage account, will create BlobStorageProvider via datastore");
                Tracer.Informational($"CopyModeComponent has value {copyModeComponent}, GetMsiDirectly has value {GetMsiDirectly} when get datastore info");

                var dataStoreFileInfo = await _pipelineApiCaller.GetDataStoreInfo(dataStore, getMsiDirectly: copyModeComponent ?? GetMsiDirectly).ConfigureAwait(false);
                if (dataStoreFileInfo?.DataStoreEndpointUri != null && !string.IsNullOrWhiteSpace(dataStoreFileInfo?.WorkspaceSaiToken))
                {
                    blobStorageProvider = new BlobStorageProvider(dataStoreFileInfo?.DataStoreEndpointUri, dataStoreFileInfo?.ContainerName, dataStoreFileInfo?.WorkspaceSaiToken);
                }
                else
                {
                    blobStorageProvider = new BlobStorageProvider(dataStoreFileInfo?.ConnectionString, dataStoreFileInfo?.ContainerName);
                }
            }
            else
            {
                Tracer.Informational($"Datastore {dataStore.Name} doesn't have access to the storage account, will create BlobStorageProvider via user obo token.");
                var userOboToken = await _pipelineApiCaller.GetUserToken(_azureConstants.CurrentValue.StorageResourceUri).ConfigureAwait(false);

                if (string.IsNullOrEmpty(userOboToken))
                {
                    throw new BaseException(new BlobStorageForbidden().ToBaseError(target: null, dataStore.AzureStorageSection.AccountName));
                }

                blobStorageProvider = new BlobStorageProvider(
                    new Uri($"https://{dataStore.AzureStorageSection.AccountName}.blob.{dataStore.AzureStorageSection.Endpoint}"),
                    dataStore.AzureStorageSection.ContainerName,
                    userOboToken,
                    storageAuthType: StorageAuthType.UserOboToken);
            }

            return (blobStorageProvider, dataStore);
        }


        public async Task<BlobStorageProvider> GetBlobStorageProviderFromDataUri(string dataUri, IWorkspaceManagement workspaceManagement)
        {
            var dataUriDefinitionInfo = FlowSnapshotHelper.GetDataUriDefinitionInfo(dataUri);
            return await GetBlobStorageProviderAsync(dataUriDefinitionInfo.EndpointUri, dataUriDefinitionInfo.ContainerName, workspaceManagement).ConfigureAwait(false);
        }

        public async Task<BlobStorageProvider> GetBlobStorageProviderAsync(string endpoint, string containerName, IWorkspaceManagement workspaceManagement)
        {
            var msiToken = await GetWorkspaceStorageMsiTokenIgnoreException().ConfigureAwait(false);
            var blobStorageProvider = new BlobStorageProvider(new Uri(endpoint), containerName, token: msiToken);
            bool isAuthorized = true;
            try
            {
                await blobStorageProvider.DownloadBlobToStringAsync(Guid.NewGuid().ToString("N", CultureInfo.InvariantCulture).Substring(0, 8)).ConfigureAwait(false);
                Tracer.Informational($"Create BlobStorageProvider using msitoken.");
            }
            catch (Exception ex)
            {
                if (ex is Azure.Storage.StorageException && (ex as Azure.Storage.StorageException)?.RequestInformation?.HttpStatusCode == 403)
                {
                    isAuthorized = false;
                }
                else if (ex is BaseException && ex.Message.Contains("Unable to authenticate data access to account", StringComparison.InvariantCultureIgnoreCase))
                {
                    isAuthorized = false;
                }
            }

            if (!isAuthorized)
            {
                Tracer.Informational($"Msi not authorized for storage endpoint {endpoint}, will fall back to use account key.");
                var connectionString = await workspaceManagement.GetUserStorageConnectionString().ConfigureAwait(false);
                blobStorageProvider = new BlobStorageProvider(connectionString, containerName);
                var accountName = connectionString.GetAccountNameFromConnectionString();
                var dataUriAccount = new Uri(endpoint).Host.Split('.')[0];
                if (!string.Equals(accountName, dataUriAccount, StringComparison.InvariantCultureIgnoreCase))
                {
                    Tracer.Informational($"DataUri has specified account name {dataUriAccount} which is not workspace account {accountName}");
                    throw new BaseException(new FlowDefinitionDataUriBadAccount().ToBaseError());
                }
            }

            return blobStorageProvider;
        }

        private async Task<string?> GetWorkspaceStorageMsiTokenIgnoreException()
        {
            try
            {
                return await _pipelineApiCaller.GetWorkspaceStorageMsiTokenS2S().ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                Tracer.Warning(ex, $"Meet exception when try to get workspace storage MSI token, details see {ex}");
                return null;
            }
        }

        #endregion BlobStorageProvider

        #region AzureBlobStorageProvider

        public async Task<(AzureBlobStorageProvider azureBlobStorageProvider, DataStoreDto dataStoreDto)> GetAzureBlobStorageProvider(string? dataStoreName = null)
        {
            DataStoreDto dataStore;
            AzureBlobStorageProvider azureBlobStorageProvider;
            if (string.IsNullOrEmpty(dataStoreName))
            {
                dataStore = await _dataStoresManagement.GetDefaultDataStoreDto().ConfigureAwait(false);
            }
            else
            {
                dataStore = await _dataStoresManagement.GetDataStoreDto(dataStoreName).ConfigureAwait(false);
                if (dataStore == null)
                {
                    Tracer.Informational($"Not found datastore {dataStoreName}");
                    throw new BaseException(new DataStoreNotFound().ToBaseError(target: null, dataStoreName));
                }
            }

            // Datastore with type "AzureSqlDatabase" have no AzureStorageSection
            // Office is using AzureDataLakeGen2 as output datastore, need to allow it
            if (dataStore.DataStoreType != DataStoreType.AzureBlob && dataStore.DataStoreType != DataStoreType.AzureDataLakeGen2)
            {
                Tracer.Warning($"Datastore {dataStore.Name} has type {dataStore.DataStoreType} which is not supported.");
                throw new BaseException(new DataStoreNotSupported().ToBaseError(target: null, dataStore.Name, dataStore.DataStoreType));
            }

            Tracer.Informational($"Datastore {dataStore.Name} has access to the storage account, will create AzureBlobStorageProvider via datastore");
            var dataStoreFileInfo = await _pipelineApiCaller.GetDataStoreInfo(dataStore, getMsiDirectly: GetMsiDirectly, throwErrorIfInvalid: true).ConfigureAwait(false);
            if (dataStoreFileInfo?.DataStoreEndpointUri != null && !string.IsNullOrWhiteSpace(dataStoreFileInfo?.WorkspaceSaiToken))
            {
                var tokenCredential = new UserBearerTokenCredential(dataStoreFileInfo.WorkspaceSaiToken);
                azureBlobStorageProvider = new AzureBlobStorageProvider(dataStoreFileInfo.DataStoreEndpointUri.ToString(), dataStoreFileInfo.ContainerName, tokenCredential);
            }
            else
            {
                azureBlobStorageProvider = new AzureBlobStorageProvider(dataStoreFileInfo!.ConnectionString, dataStoreFileInfo!.ContainerName);
            }

            return (azureBlobStorageProvider, dataStore);
        }

        public static AzureBlobStorageProvider GetAzureBlobStorageProvider(string endpoint, string containerName, TokenCredential tokenCredential)
        {
            return new AzureBlobStorageProvider(endpoint, containerName, tokenCredential);
        }

        #endregion AzureBlobStorageProvider
    }
}
