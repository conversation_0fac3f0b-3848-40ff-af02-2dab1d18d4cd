using System.Collections.Generic;
using System.Linq;
using Microsoft.MachineLearning.Pipeline.Contracts;
using Microsoft.MachineLearning.Studio.MiddleTier.Helpers;
using Microsoft.MachineLearning.Studio.MiddleTier.Model.Flighting;
using Microsoft.MachineLearning.Studio.MiddleTier.UnitTests.TestUtilities.Ecs;
using Xunit;

namespace Microsoft.MachineLearning.Studio.MiddleTier.UnitTests.EcsFlightSettingsTests
{
    public class EcsEmbeddedFlightTests : IClassFixture<EcsEmbeddedTestFixture>
    {

        [Fact]
        [Trait(UnitTestCategory.Category, UnitTestCategory.EcsEmbedded)]
        public async void TestTryGetFeaturesNoMatchShouldReturnDefaultAsync()
        {
            // Text return Ecs default config by fake context
            var flightContext = new FlightContext("sub1", "rg1", "ws1");
            var ecsJobFeatureFlags = await ECSFeatureFlagsHelper.GetJobFeatureFlagsFromECSAsync(flightContext).ConfigureAwait(false);
            var ecsComponentFeatureFlags = await ECSFeatureFlagsHelper.GetComponentFeatureFlagsFromECSAsync(flightContext).ConfigureAwait(false);
            Assert.False(ecsJobFeatureFlags.EnableCancelValidation);
            Assert.False(ecsComponentFeatureFlags.EnableOutputToFileBasedOnDataTypeIdWithEnvVar);
            Assert.Equal(DatasetAccessModes.AssetInDpv2WithAssetInDesignerUI, ecsComponentFeatureFlags.DatasetAccessMode);
            Assert.False(ecsComponentFeatureFlags.AllowExportCommonPipelineComponent);
            Assert.False(ecsComponentFeatureFlags.AllowExportRegistryPipelineComponent);
            Assert.False(ecsComponentFeatureFlags.AutoPartitionADLSGen1Output);
            Assert.Null(ecsComponentFeatureFlags.IntellectualPropertyProtectedWorkspaceComponentRegistrationAllowedPublisher);
        }

        [Fact]
        [Trait(UnitTestCategory.Category, UnitTestCategory.EcsEmbedded)]
        public async void TestTryGetFeaturesFullMatchShouldReturnFlightFeaturesForJobAsync()
        {
            var flightContext = new FlightContext("20d3c9e4-625d-45e1-ac8a-def90d3c4a88", "coresearchsingularitywus3", "wxtcssingwus3");
            var ecsJobFeatureFlags = await ECSFeatureFlagsHelper.GetJobFeatureFlagsFromECSAsync(flightContext).ConfigureAwait(false);
            var ecsComponentFeatureFlags = await ECSFeatureFlagsHelper.GetComponentFeatureFlagsFromECSAsync(flightContext).ConfigureAwait(false);
            Assert.True(ecsJobFeatureFlags.EnableCancelValidation);
            Assert.True(ecsComponentFeatureFlags.EnableOutputToFileBasedOnDataTypeIdWithEnvVar);
        }

        #region AppSettings

        // Ensure WebXT Workspace flight setting as expected
        [Theory]
        [InlineData("316a14b3-f94a-4267-a76a-497e589ef0a8", "training", "wxtcstrain")]
        // team/workloads: for dev purpose, tracked by Task 1915287
        // region: westcentralus
        [InlineData("4aaa645c-5ae2-4ae9-a17a-84b9023bc56a", "coresearch-dev-rg", "coresearch-dev")]
        [InlineData("c8e0fe2b-cfc8-4427-95a7-7ad64c76712c", "Underside", "underside-training")]
        // team/workloads: News & feeds CF, tracked by Task 1915287
        // region: westus2
        [InlineData("b6dc87f3-c479-49c8-8cb5-7896da3ff895", "devtestrg", "Sage_CF", false)]
        // Bing Multi-media workspaces
        // team/workloads: MM Relevance WS, tracked by Task 1915287
        // region: westcentralus
        [InlineData("0bda0759-4545-499e-8460-98f94f96ec48", "image-relevance-rg", "image-relevance-ws")]
        [InlineData("48bbc269-ce89-4f6f-9a12-c6f91fcb772d", "bing-mm-staging", "bing-mm-staging-ws")]
        [InlineData("6560575d-fa06-4e7d-95fb-f962e74efd7a", "bing-mm-prd", "bing-mm-aml-prd")]
        [InlineData("ae71ef11-a03f-4b4f-a0e6-ef144727c711", "relatedimagecf", "RelatedImageCFWorkspace")]
        // ensure case-insensitive
        [InlineData("ae71ef11-a03f-4b4f-a0e6-ef144727c711", "relatedimagecf", "relatedimageCFWorkspace")]
        [InlineData("ae71ef11-a03f-4b4f-a0e6-ef144727c711", "relatedimageCF", "relatedimageCFWorkspace")]
        // New workspace added on 2022-08-08, tracked by Task 1915287
        // team/workloads: MM relevance - ranker training WS
        // region: East US
        [InlineData("0bda0759-4545-499e-8460-98f94f96ec48", "image-ranker-training", "imagerankerws-training")]
        // region: westus2
        [InlineData("316a14b3-f94a-4267-a76a-497e589ef0a8", "lightgbm-adls-resource", "LightGBM-ADLS-Training")]
        // team/workloads: MM Related Image WS
        // region: East US
        // This is duplicated with above one, one more case-insensitive case
        [InlineData("ae71ef11-a03f-4b4f-a0e6-ef144727c711", "RelatedImageCF", "RelatedImageCFWorkspace")]
        // team/workloads: CS metric analysis
        // region: westus2
        [InlineData("316a14b3-f94a-4267-a76a-497e589ef0a8", "metrics", "wxtcsmetric")]
        // sub: 96aede12-2f73-41cb-b983-6d11a904839b resource group: webXT name: coresearch-dev (tracked by Task2173123)
        // region: westcentralus
        [InlineData("96aede12-2f73-41cb-b983-6d11a904839b", "webXT", "coresearch-dev")]
        [InlineData("96aede12-2f73-41cb-b983-6d11a904839b", "webXT", "coresearch-dev-new")]
        // team/workloads: Ads Training workload (tracked by Task2233496)
        // region: eastus2
        [InlineData("a1844b4b-434a-4ee9-bad3-9f9eb5a52ff5", "wb-aml", "wb-aml-eastus2")]
        // New workspace added on 2024-01-29, tracked by Task 2916147
        [InlineData("a1844b4b-434a-4ee9-bad3-9f9eb5a52ff5", "wb-aml", "wb-aml-eastus")]
        // New workspace added on 2023-02-10, tracked by Task 2214695
        // region: westus3
        [InlineData("20d3c9e4-625d-45e1-ac8a-def90d3c4a88", "coresearchsingularitywus3", "wxtcssingwus3")]
        // New workspace added on 2023-03-03, tracked by Task 2273865
        [InlineData("6a2f8e42-f7aa-4cb6-b0ef-ddd0003b1ab6", "Babel", "ads-ws-babel-eastus")]
        // New workspace added on 2023-04-23, tracked by Task 2337076
        [InlineData("07c3f5a2-0880-4a37-b678-e29ade7b1036", "Singularity", "wxtcssing")]
        // New workspace added on 2023-09-15, tracked by Task 2689071
        [InlineData("f5dda754-4ed0-46f4-a238-56efe6980b8d", "personalization", "personalization-platform")]
        // New workspace added on 2024-01-29, tracked by Task 2916140
        [InlineData("a53827eb-4d73-4f8d-a5c5-d138cdcddda7", "unifiedadsgpt", "unified-ad-user-prompt")]
        // New workspace added on 2024-01-29, tracked by Task 2916143
        [InlineData("a53827eb-4d73-4f8d-a5c5-d138cdcddda7", "userresponsemodels", "UserResponseModelsAML")]
        [Trait(UnitTestCategory.Category, UnitTestCategory.EcsEmbedded)]
        public async void TestGetFeaturesForWebXTFromAppSettingsAsync(string subscriptionId, string resourceGroup, string workspaceName, bool expectedJobCancelValidateion = true)
        {
            // Ensure WebXT Workspace flight setting as expected
            var flightContext = new FlightContext(subscriptionId, resourceGroup, workspaceName);
            var ecsJobFeatureFlags = await ECSFeatureFlagsHelper.GetJobFeatureFlagsFromECSAsync(flightContext).ConfigureAwait(false);
            var ecsComponentFeatureFlags = await ECSFeatureFlagsHelper.GetComponentFeatureFlagsFromECSAsync(flightContext).ConfigureAwait(false);

            Assert.Equal(expectedJobCancelValidateion, ecsJobFeatureFlags.EnableCancelValidation);
            Assert.True(ecsComponentFeatureFlags.EnableOutputToFileBasedOnDataTypeIdWithEnvVar);
            Assert.Equal(DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI, ecsComponentFeatureFlags.DatasetAccessMode);
            Assert.Null(ecsComponentFeatureFlags.IntellectualPropertyProtectedWorkspaceComponentRegistrationAllowedPublisher);
        }

        // AllowExportPipelineComponentGraph is also checked compared with TestGetFeaturesForWebXTFromAppSettings
        [Theory]
        [InlineData("96aede12-2f73-41cb-b983-6d11a904839b", "sdk", "migration-test-canary")]
        [InlineData("96aede12-2f73-41cb-b983-6d11a904839b", "sdk", "migration-test")]
        [Trait(UnitTestCategory.Category, UnitTestCategory.EcsEmbedded)]
        public async void TestGetFeaturesv2ForWebXTFromAppSettings(string subscriptionId, string resourceGroup, string workspaceName)
        {
            // Ensure WebXT Workspace flight setting as expected
            var flightContext = new FlightContext(subscriptionId, resourceGroup, workspaceName);
            var ecsJobFeatureFlags = await ECSFeatureFlagsHelper.GetJobFeatureFlagsFromECSAsync(flightContext).ConfigureAwait(false);
            var ecsComponentFeatureFlags = await ECSFeatureFlagsHelper.GetComponentFeatureFlagsFromECSAsync(flightContext).ConfigureAwait(false);
            Assert.True(ecsJobFeatureFlags.EnableCancelValidation);
            Assert.True(ecsComponentFeatureFlags.EnableOutputToFileBasedOnDataTypeIdWithEnvVar);
            Assert.Equal(DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI, ecsComponentFeatureFlags.DatasetAccessMode);
            Assert.Null(ecsComponentFeatureFlags.IntellectualPropertyProtectedWorkspaceComponentRegistrationAllowedPublisher);
        }

        // WebXT Service Group 'Search-and-Distribution', added on 2022-11-24
        // ViennaAllTenantsSubscriptions
        // | where SubscriptionType == 'Internal'
        // | distinct SubscriptionId, CustomerName, CustomerGUID, SubscriptionType, OrganizationName, ServiceGroupName, SubscriptionName
        // | where OrganizationName contains 'WebXT' and ServiceGroupName contains 'Search-and-Distribution'
        // | where SubscriptionName  !contains 'COSMOS'
        // | extend subId = toguid(SubscriptionId)
        // | project subId
        // | distinct subId
        [Theory]
        [InlineData("2e4ecd16-bd8e-454e-8e8d-d972ddd58345")]
        [InlineData("4518c9c2-68fb-4e60-bfe6-0266d5b6c6d5")]
        [InlineData("5070cb23-94cb-4b09-b840-4ca59240a9b0")]
        [InlineData("67bd29b4-bef1-4f2a-8849-c5d99e427dfa")]
        [InlineData("69bec6f9-ee0b-4d6b-8192-6d6a15ac5eef")]
        [InlineData("7e81a1eb-078a-4110-ac96-81eb9890c3e2")]
        [InlineData("886eddb8-76e6-4793-887a-2f5f245beaef")]
        [InlineData("8f1b7623-f59a-46fc-befb-53ddc6ef5052")]
        [InlineData("965b0820-25ca-4c20-9687-52967f7584f1")]
        [InlineData("a9c5a4cf-aa47-4fd7-bf25-2030ab69f2c9")]
        [InlineData("b23f290e-3996-4eba-8c6e-13cc5e161082")]
        [InlineData("bf0b6cba-490e-4d9b-9a9a-54f0bd2d1b0f")]
        [InlineData("b145fac9-02be-403d-b143-9920034347eb")]
        [InlineData("c5463472-d7b4-4c64-ac3f-9d3abc70eb3b")]
        [InlineData("b819a867-e7db-4922-8970-eac07e90fe03")]
        [InlineData("c5acb5df-05f2-4ca2-9c8a-c736f8986bd5")]
        [InlineData("bf8ad13c-059c-4991-bf6c-c27b966595f7")]
        [InlineData("f552f242-5c28-43eb-870a-884a3486d96c")]
        [InlineData("0fc2198b-31f7-46f0-9102-235e0a845887")]
        [InlineData("28180df1-bfaf-4a97-85e4-31f63849a285")]
        [InlineData("2b4e5706-b45a-4076-8df0-71018d666ee0")]
        [InlineData("4bbcb42d-b85e-4972-a624-0e79873098b7")]
        [InlineData("5a2c58b7-19bd-4107-946f-47d905ff3289")]
        [InlineData("7eca4e4e-3300-4979-bc0e-7bcc08aab66a")]
        [InlineData("89cf644e-2dd7-4681-8972-a1eb092bbae0")]
        [InlineData("7ff442cd-e068-49ac-ae1d-7defb67769f2")]
        [InlineData("89bcf37c-ee79-4b80-a530-0ed00b267a33")]
        [InlineData("b5beb797-a8c8-414d-a35f-2a028eaaf264")]
        [InlineData("b84de0dd-94e4-4a57-9898-140fa3bd455f")]
        [InlineData("b795a0bb-fb5a-4726-bb51-c29d22f4a1cb")]
        [InlineData("c869a62a-1aca-49f3-bcfe-bc550b92fc9f")]
        [InlineData("c6cc4781-cfe5-40f0-a2c8-41aa96931302")]
        [InlineData("ec2f9fb3-bd99-413e-9c7d-b72548a70c4e")]
        [InlineData("e0038b9a-6f99-4952-baa9-519ae24d61c1")]
        [InlineData("f3ebbda2-3d0f-468d-8e23-31a0796dcac1")]
        [InlineData("f7019b37-fe22-4b86-aea0-03da0be9e2b5")]
        [InlineData("fcb0cf1f-3eac-4785-8341-fd4992b82ce9")]
        [InlineData("1e8fdcb7-b803-4dbd-b426-2f33ab74f529")]
        [InlineData("3064cff0-3f67-4145-8412-0b38d59444fc")]
        [InlineData("29af714e-030b-4ef4-8abb-9714c1bfacf2")]
        [InlineData("2bd90f4e-97bd-45d1-b94d-3f08a9ea001d")]
        [InlineData("65eb1c93-732c-4e81-ab9d-df8393b0935f")]
        [InlineData("70926da3-92a6-447b-86f3-24da87a0778a")]
        [InlineData("724b22f7-71b6-4855-98a7-c3443df58519")]
        [InlineData("92fda365-bc8d-42ba-b835-39b9996003c1")]
        [InlineData("96af2497-6466-4d03-8a21-fd3efbd7d750")]
        [InlineData("a4183c45-a392-47c3-9ae3-b1253cca0a53")]
        [InlineData("a63a8ccc-c8df-4e21-8674-d9873842e682")]
        [InlineData("ae71ef11-a03f-4b4f-a0e6-ef144727c711")]
        [InlineData("a51163f3-88c8-4fac-899b-a7696027951f")]
        [InlineData("ad42779a-f32d-4cf5-84a6-1795cc3e3a12")]
        [InlineData("b42f08e4-41ca-4175-9ff1-8bdc64297a0f")]
        [InlineData("b56a26ed-15b9-471a-a8f0-cc296ea0599b")]
        [InlineData("d91ea109-b62a-4ad0-a99f-086340bbcebb")]
        [InlineData("d89dea55-74dd-40f5-8a51-49bf34478a43")]
        [InlineData("fbc9aeaa-7fbc-411e-8d9e-450e656077d3")]
        [InlineData("06de1d46-54c2-4ddc-be0c-5ae6193a4ef1")]
        [InlineData("0ab3e484-90a2-4244-8abb-f009c38f6ad8")]
        [InlineData("0c64a747-d183-46ee-ad81-2aa83ddfacc8")]
        [InlineData("316a14b3-f94a-4267-a76a-497e589ef0a8")]
        [InlineData("37984b12-2059-4a42-b803-aa7015ec4108")]
        [InlineData("3bdf439a-2046-41c5-a5d8-e0aff859233a")]
        [InlineData("47bf4ccf-7fb2-44a5-a71a-f207bd28d23b")]
        [InlineData("57cbb3cc-b910-438d-8c41-34529844ce2f")]
        [InlineData("59f3599f-e90f-4136-b6e0-5b8667bd846d")]
        [InlineData("59cf2922-5702-47a7-b2c7-4a137c399cc3")]
        [InlineData("67cf92f8-0af0-48a7-a62c-d9555c316c5e")]
        [InlineData("958ccd28-6aca-40dc-9b56-ac39a18f7612")]
        [InlineData("9a052143-207d-424d-9897-6fe4b880cbb6")]
        [InlineData("db1e2310-49ac-469c-95f5-331472723266")]
        [InlineData("f681ff2d-fdf6-4b55-9c52-6993ddba2785")]
        [InlineData("0776bc13-ef31-4971-a30b-a62e57bef183")]
        [InlineData("153a1e11-a59b-43b1-99ee-4b66ee295d9c")]
        [InlineData("1bac5c2b-fd42-4765-b7a6-edfbe2df6a94")]
        [InlineData("27f590c5-f77f-4550-b8ee-4f1705c01fc8")]
        [InlineData("23784ad7-f2b3-443c-af4b-0e00197fdc40")]
        [InlineData("3e7ae222-a1b0-4648-b86e-8a335928d144")]
        [InlineData("4e48fbe9-36ec-4333-927e-f91ef1383ecf")]
        [InlineData("5a696448-23f1-493a-b9ff-32ffa147f4a5")]
        [InlineData("7555e4cf-50d7-47bd-910b-09c62f871214")]
        [InlineData("81e388c8-fa41-410e-b2d2-2d440243e53a")]
        [InlineData("8d72dea4-35b8-4dca-aef0-a5dd2185717a")]
        [InlineData("97f0b560-133b-4d4a-bca0-bd30d09756a3")]
        [InlineData("c8e0fe2b-cfc8-4427-95a7-7ad64c76712c")]
        [InlineData("cbe1438f-03f0-4d05-a31d-ce0ef422cd0b")]
        [InlineData("d95163c7-c782-499f-9350-f0f9c501b051")]
        [InlineData("d14de3b0-dcc9-4977-82ae-4a107d88ee2f")]
        [InlineData("db395c8d-2e8a-4016-8119-ff96d1165454")]
        [InlineData("f7c3c1f1-fa76-400c-9873-e0d143056010")]
        [InlineData("15b01267-7d09-44a2-8464-e6d71b8072e1")]
        [InlineData("38cdad14-2403-4c68-b026-a7f851eefa70")]
        [InlineData("4502a2cf-76ad-4cb1-8521-b79ee3f6af92")]
        [InlineData("757c2310-e253-4977-a8d7-41e62975a6b5")]
        [InlineData("84abd667-26a6-4766-b18b-8a2d90cb1e60")]
        [InlineData("b6fe3c19-982e-48e3-ba33-7237e20ccd0e")]
        [InlineData("d3a6bfc1-d39c-4ec7-b052-ac4240c1d081")]
        [InlineData("e2d012dc-ee7d-41d7-ab71-4f86ce8506a9")]
        [InlineData("f2f8c896-a8a5-41df-ae23-96b32b82c4ad")]
        [InlineData("fea11cf7-2247-45c1-983b-e5d30004439a")]
        [InlineData("16a0eb80-d105-4432-a870-3b6dc4b8d81b")]
        [InlineData("1138a10e-3e89-4274-8baa-ccf894d09c24")]
        [InlineData("0bda0759-4545-499e-8460-98f94f96ec48")]
        [InlineData("19269df8-6537-44af-b200-0c6f319a89fb")]
        [InlineData("24fd56b7-f5b4-4b99-b323-8e03ccbb6f78")]
        [InlineData("30b9766f-d742-4210-b829-879376a283a5")]
        [InlineData("34c2f8e8-4d82-4935-9604-8e19bdda92e3")]
        [InlineData("68ee6869-f8ab-482a-bb01-f0efa8654215")]
        [InlineData("70e8b01c-3605-444f-bd79-b6f06016899c")]
        [InlineData("7515a9eb-fb3b-4a33-b3c7-07fb8ca93610")]
        [InlineData("863a5de8-398b-443b-9320-5044a1692cb2")]
        [InlineData("814b8bcc-5e54-487e-a9fa-27b5740ac7b9")]
        [InlineData("94f1da8b-bd32-4dd4-955a-1d84a93b86b2")]
        [InlineData("b4a7579a-0b39-451a-a51c-2f6a9c19c21f")]
        [InlineData("b974f156-31f9-41cd-b4be-b3ba6c628e22")]
        [InlineData("b9a72d90-25b3-4387-9e26-497182076517")]
        [InlineData("bfe9bce1-0fd6-47a5-bcca-5ed5446788e5")]
        [InlineData("e79d114d-c5da-4574-9cb0-94acafd26ca1")]
        [InlineData("0986d3c2-acd3-4451-ab31-2d58697e8f10")]
        [InlineData("33739cb7-bdd9-48bd-845d-e6e0fd543471")]
        [InlineData("818de531-1af7-4e0b-a9e5-87120ccc7f02")]
        [InlineData("7e3a0b17-dff9-4183-b87a-b453c3873a70")]
        [InlineData("b69c69a2-f06c-4cc1-b10c-d034afdf8504")]
        [InlineData("cf8c8765-6cb9-4f90-8120-8af59ad45130")]
        [InlineData("d474ea31-8b94-4471-b623-607b9d34bf54")]
        [InlineData("d9276047-81a6-48a2-99a3-0b1886c11ac0")]
        [InlineData("0525aa4b-3ba3-44d6-8dd2-d94e3402f0c8")]
        [InlineData("06d8a9c4-c094-456a-9542-ac34b25601c1")]
        [InlineData("fb32091b-dde2-409d-a86c-aa54ee72c607")]
        [InlineData("39bb5f3f-cc3d-46ea-b8d7-ce4ed7df9e01")]
        [InlineData("591b33e2-c5f0-479b-8c0f-0e29041032c6")]
        [InlineData("59b4e86a-901f-48cb-87d5-68faa1328266")]
        [InlineData("9f8b90e0-75d9-4208-b7c4-f60be2e8d51b")]
        [InlineData("b2b15010-5d1f-4952-acca-4b665dcfba05")]
        [InlineData("cad4a5fc-d9d4-4370-ae7b-7fc5de8d35e1")]
        [InlineData("e31c699c-e6aa-47ab-a338-c1f9de0f0471")]
        [InlineData("e44d07b7-18bb-47d9-a9e2-6f829895bef0")]
        [InlineData("07ab33d0-5077-4108-97c0-eeaa21145071")]
        [InlineData("33aac5c6-f920-4543-9af2-f5d93f431f6a")]
        [InlineData("4656d089-082e-4877-89cb-cb91cd9e3491")]
        [InlineData("664baeb4-62de-4994-ac57-77a431448754")]
        [InlineData("7d53fcfb-7270-4cae-8179-5e577b38bc31")]
        [InlineData("7b845e01-492e-46ae-9b1f-d98ffe7188f0")]
        [InlineData("9eed08e5-2bfc-435a-98a0-25995a4c9deb")]
        [InlineData("8ade1b3c-bb7a-4f88-8fb6-f6c3818f0a9d")]
        [InlineData("810cb794-6387-4cb0-afa1-53bd95121b57")]
        [InlineData("a0985ce2-cb79-4c14-bd21-35d56caad17d")]
        [InlineData("c3300425-a1ce-4fed-986e-febe0c09ac43")]
        [InlineData("ceb4b024-9206-4b0e-8d71-4692134c4a99")]
        [InlineData("ed2b77a7-e550-49f0-b630-f87ed8b35c78")]
        [InlineData("ffb634c7-2dce-411f-b9cc-0a928673e923")]
        [InlineData("f27738ce-43ed-4ae4-9793-52afba3b40f1")]
        [InlineData("fd1c8a92-04b5-48cb-9462-1495cd3c3da6")]
        [InlineData("0f327625-812d-409b-bf67-eedd7489bac0")]
        [InlineData("46422f7e-e1e9-4393-8b63-61d0fe9fcddc")]
        [InlineData("704ae067-466a-4093-a2d4-2158f64b4d99")]
        [InlineData("734a7ec5-8307-4205-8837-aad9b39cf154")]
        [InlineData("794fa7c4-e84c-4b9c-8862-72777662e5a5")]
        [InlineData("8c588897-c6ff-40e7-98ea-eb731ba81cc1")]
        [InlineData("a8e1deb2-c508-4884-bc19-ccfb9ca22ce6")]
        [InlineData("a7c57dbb-eb6b-423e-aff3-567e4eb2e560")]
        [InlineData("b11b0811-86b7-482b-ab43-8013ceca2a3d")]
        [InlineData("c4908f30-6290-4d56-a2ea-85bfea337feb")]
        [InlineData("edb5965b-8148-494e-92c9-26d0e5b04c5f")]
        [InlineData("f0c4cdb5-1273-4587-8228-83562c1dc873")]
        [InlineData("1d57c23d-a1ff-443d-8041-51b0c7db0725")]
        [InlineData("633974f2-be09-45e0-9a45-af86de41a767")]
        [InlineData("755d4d6b-a87a-462a-a0f3-217f56d9da01")]
        [InlineData("788fe8cb-bbc1-4472-ab2d-11559338c80c")]
        [InlineData("a04dc3e8-9a91-4d38-a154-9c91bb8fde81")]
        [InlineData("d363392f-8128-4f53-a5b0-1e2b512c80f0")]
        [InlineData("dda9cefc-2aad-434a-a478-78d6a9ca850d")]
        [InlineData("e04d2d67-3842-40e7-a1d4-e7f89abcc4d7")]
        [InlineData("56993b41-8666-4778-919d-130899dbce41")]
        [InlineData("b9aa8acb-0b7e-467d-8156-d1c2bac1588f")]
        [InlineData("c66d633f-f9be-411a-8f54-44ed4db4c177")]
        [InlineData("11077225-4fa0-4ac0-910d-60e99c68be12")]
        [InlineData("11a5beed-309e-452d-8663-0af9e3865f9c")]
        [InlineData("3c8d8f4f-4fac-44e0-afbb-62d462a52b43")]
        [InlineData("60a25d94-d40e-4965-9aeb-d563eaa41a68")]
        [InlineData("65556a96-23fb-438f-a546-dda9457a3633")]
        [InlineData("05bc9f24-9bcb-4555-bfd8-f9535a24dd1d")]
        [InlineData("2aaae0f1-1f37-46cb-bd2e-90c46fb2db4d")]
        [InlineData("aa7329b2-0640-4435-9fee-dbe2cc21adf1")]
        [InlineData("09c63038-9d0e-4e13-8ac5-be337006d07e")]
        [Trait(UnitTestCategory.Category, UnitTestCategory.EcsEmbedded)]
        public async void TestGetFeaturesForWebXTSearchAndDistributionGroupFromAppSettings(string subscriptionId)
        {
            // Ensure WebXT Workspace flight setting as expected (resource group or workspace is not required)
            var flightContext = new FlightContext(subscriptionId, "any", "*");
            var ecsJobFeatureFlags = await ECSFeatureFlagsHelper.GetJobFeatureFlagsFromECSAsync(flightContext).ConfigureAwait(false);
            var ecsComponentFeatureFlags = await ECSFeatureFlagsHelper.GetComponentFeatureFlagsFromECSAsync(flightContext).ConfigureAwait(false);
            Assert.True(ecsJobFeatureFlags.EnableCancelValidation);
            Assert.True(ecsComponentFeatureFlags.EnableOutputToFileBasedOnDataTypeIdWithEnvVar);
            Assert.Equal(DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI, ecsComponentFeatureFlags.DatasetAccessMode);
            Assert.Null(ecsComponentFeatureFlags.IntellectualPropertyProtectedWorkspaceComponentRegistrationAllowedPublisher);
        }

        [Theory]
        [Trait(UnitTestCategory.Category, UnitTestCategory.EcsEmbedded)]
        // Currently only difference with above is DatasetInDpv2WithDatasetInDesignerUI of DatasetAccessMode
        // MT CI workspace with webxt setting
        [InlineData("96aede12-2f73-41cb-b983-6d11a904839b", "pmt_test", "pmt_test_master_webxt")]
        public async void TestGetPreviewFeaturesForWebXTFromAppSettings(string subscriptionId, string resourceGroup, string workspaceName)
        {
            var flightContext = new FlightContext(subscriptionId, resourceGroup, workspaceName);
            var ecsComponentFeatureFlags = await ECSFeatureFlagsHelper.GetComponentFeatureFlagsFromECSAsync(flightContext).ConfigureAwait(false);

            Assert.True(ecsComponentFeatureFlags.EnableOutputToFileBasedOnDataTypeIdWithEnvVar);
            Assert.Equal(DatasetAccessModes.DatasetInDpv2WithDatasetInDesignerUI, ecsComponentFeatureFlags.DatasetAccessMode);
            Assert.Null(ecsComponentFeatureFlags.IntellectualPropertyProtectedWorkspaceComponentRegistrationAllowedPublisher);
        }

        [Theory]
        [Trait(UnitTestCategory.Category, UnitTestCategory.EcsEmbedded)]
        // Disable Asset approach from UI for Subscriptions TSCIENCEGPU used by AI Platform Team
        [InlineData("79f57c16-00fe-48da-87d4-5192e86cd047", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Disable Asset approach from UI for Subscriptions TSCIENCE used by AI Platform Team
        [InlineData("9ec1d932-0f3f-486c-acc6-e7d78b358f9b", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Disable Asset approach from UI for Subscriptions SPEECH INFRASTRUCTURE used by AI Platform Team
        [InlineData("3d75862d-3a43-450d-8046-895947d5abfa", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Disable Asset approach from UI for Subscriptions AMLGPT3 MUMFORD EYES-OFF used by AI Platform Team
        [InlineData("c80f1917-35d7-4d58-a840-63f08bf028c4", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Disable Asset approach from UI for Subscriptions AZURE OAI DEV BRAINWAVE MUMFORD 1PPOOLOCTO used by AI Platform Team
        [InlineData("6dd74f53-a30f-4803-b838-c13a832c0154", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Disable Asset approach from UI for Subscriptions COPILOT GPT3 TRAINING used by AI Platform Team
        [InlineData("74326f65-ea04-4329-b24a-2c60e71ebb49", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Disable Asset approach from UI for Subscriptions PHILLY MIGRATION used by Azure Legacy Team
        [InlineData("06237282-1ddd-4677-9878-5bc19cc0001a", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Disable Asset approach from UI for Subscriptions AMPLIFY AI INTERNAL SUBSCRIPTION used by Customer Success Engineering Team
        [InlineData("c93cf9f1-e764-4d70-b47d-c2a065e64826", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Disable Asset approach from UI for Subscriptions PSE - DEV - LINKEDIN EI [R&D] used by Engineering Team
        [InlineData("c9a251d8-1272-4c0f-8055-8271bbc1d677", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Disable Asset approach from UI for Subscriptions HERON SANDBOX TORUS SUBSCRIPTION 2 used by M365 Core Team
        [InlineData("60d27411-7736-4355-ac95-ac033929fe9d", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Disable Asset approach from UI for Subscriptions MICROSOFT AZURE INTERNAL CONSUMPTION used by M365 Core Team
        [InlineData("2dea9532-70d2-472d-8ebd-6f53149ad551", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Disable Asset approach from UI for Subscriptions HERON SANDBOX TORUS SUBSCRIPTION 1 used by M365 Core Team
        [InlineData("a6d8cf0d-b71e-4ffe-9a03-3e6013fed98a", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Disable Asset approach from UI for Subscriptions HERON RECIFE TORUS SUBSCRIPTION 1 (PROD) used by M365 Core Team
        [InlineData("7e914327-0cdd-41e1-9ef9-183c51a9d79f", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Disable Asset approach from UI for Subscriptions HERON SANDBOX CLOVERPORT EYES-ON TORUS SUBSCRIPTION 1 used by M365 Core Team
        [InlineData("3e02707f-7ddb-4796-ad0f-32f4f4b22757", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Disable Asset approach from UI for Subscriptions HERON RECIFE PRODUCTION 3 used by M365 Core Team
        [InlineData("54d27c16-ab6a-4cb5-9c77-e5e0f55cf78c", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Disable Asset approach from UI for Subscriptions HERON SANDBOX MDP TORUS SUBSCRIPTION 1 used by M365 Core Team
        [InlineData("8987c30c-7019-4ef5-8214-d1e94c0abeb4", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Disable Asset approach from UI for Subscriptions HERON RECIFE PRODUCTION 2 used by M365 Core Team
        [InlineData("4a48b81a-d7ad-41cd-a73b-2bba8ae07d5d", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Disable Asset approach from UI for Subscriptions HERON RECIFE PRODUCTION 4 used by M365 Core Team
        [InlineData("98d476c6-c75b-481c-8c18-f53d91f614b0", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Disable Asset approach from UI for Subscriptions HERON SANDBOX LOFT EYES-ON TORUS SUBSCRIPTION 1 used by M365 Core Team
        [InlineData("95e0ae98-c287-4f51-b23e-4e2317b7e169", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Disable Asset approach from UI for Subscriptions NON-COMPLIANT MSAI+SI EXPERIMENTATION used by M365 Core Team
        [InlineData("ddb33dc4-889c-4fa1-90ce-482d793d6480", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Disable Asset approach from UI for Subscriptions OXO AI PLATFORM MODEL TRAINING SERVICE used by M365 Core Team
        [InlineData("76ff4b31-fd81-49e8-b72f-ba96f421489c", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Disable Asset approach from UI for Subscriptions M365DESIGNER-INT used by OXO Team
        [InlineData("c68c048e-ea77-48c5-b7fe-693ad48fbc5f", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Disable Asset approach from UI for Subscriptions OXO ML INTERNAL CONSUMPTION used by OXO Team
        [InlineData("21f93f70-2331-4d35-a1cf-8c30a3d7a922", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Disable Asset approach from UI for Subscriptions GCRPRODEX2 used by R&I Services Team
        [InlineData("46da6261-2167-4e71-8b0d-f4a45215ce61", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Disable Asset approach from UI for Subscriptions MSR TAPP used by Research Team
        [InlineData("4c6dcd0e-1a79-4f3c-9bf2-c920ac31cead", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Disable Asset approach from UI for Subscriptions PRIVACY IN AI used by Research Team
        [InlineData("e03efd8f-ea1e-4b61-9c49-2d836f3271ec", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Disable Asset approach from UI for Subscriptions STM_PROTOTYPE_DEV used by Security Team
        [InlineData("6d13156d-cc60-485a-9e9e-54857689f99b", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Disable Asset approach from UI for Subscriptions DLTS - TRAINING used by WebXT Team
        [InlineData("20d3c9e4-625d-45e1-ac8a-def90d3c4a88", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Disable Asset approach from UI for Subscriptions RANKER TRAINING used by WebXT Team
        [InlineData("316a14b3-f94a-4267-a76a-497e589ef0a8", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Disable Asset approach from UI for Subscriptions BING MULTIMEDIA RELEVANCE used by WebXT Team
        [InlineData("0bda0759-4545-499e-8460-98f94f96ec48", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Disable Asset approach from UI for Subscriptions WEATHER AND FINANCE INT used by WebXT Team
        [InlineData("009d0e9f-a42a-470e-b315-82496a88cf0f", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Disable Asset approach from UI for Subscriptions AI PLATFORM GPU 2 used by WebXT Team
        [InlineData("16a0eb80-d105-4432-a870-3b6dc4b8d81b", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Disable Asset approach from UI for Subscriptions VISION RESEARCH used by  Team
        [InlineData("103fe10b-22e1-477d-aa81-b7a916e5087d", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Disable Asset approach from UI for Heron Subscriptions
        [InlineData("3b72a77d-605e-4ac0-96de-8e05e2be8625", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Disable Asset approach from UI for Heron Subscriptions
        [InlineData("452339f0-5c88-481f-80ef-1849fcaf05b1", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Disable Asset approach from UI for Heron Subscriptions
        [InlineData("255ac841-aa35-4553-b6ef-792d6f3e9df6", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        public async void TestGetDpv2AssetsFeaturesFor1pSubsUsingUIAndSDK15FromAppSettings(string subscription, DatasetAccessModes datasetAccessModes)
        {
            var flightContext = new FlightContext(subscription, "rg1", "ws1");
            var ecsComponentFeatureFlags = await ECSFeatureFlagsHelper.GetComponentFeatureFlagsFromECSAsync(flightContext).ConfigureAwait(false);

            // Subscriptions Allowlisted for creating IPProtected components
            Assert.Equal(datasetAccessModes, ecsComponentFeatureFlags.DatasetAccessMode);
        }

        // Disable Asset approach from UI for Subscriptions hosting Office Heron Workspaces
        [Theory]
        [Trait(UnitTestCategory.Category, UnitTestCategory.EcsEmbedded)]
        [InlineData("04ef1951-3164-43a0-95be-e1acb3e028ba", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        [InlineData("08047947-f71e-4462-a09d-266e3d34c431", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        [InlineData("27a3c1ec-7827-4d89-93b8-dfbb82e9c56f", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        [InlineData("2dea9532-70d2-472d-8ebd-6f53149ad551", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        [InlineData("3265b6a5-866d-469b-830f-0baacd77d045", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        [InlineData("3e02707f-7ddb-4796-ad0f-32f4f4b22757", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        [InlineData("4a48b81a-d7ad-41cd-a73b-2bba8ae07d5d", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        [InlineData("98d476c6-c75b-481c-8c18-f53d91f614b0", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        [InlineData("54d27c16-ab6a-4cb5-9c77-e5e0f55cf78c", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        [InlineData("60d27411-7736-4355-ac95-ac033929fe9d", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        [InlineData("69907770-0514-44eb-a027-6c3de34eac20", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        [InlineData("7e914327-0cdd-41e1-9ef9-183c51a9d79f", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        [InlineData("86956e13-ab30-4f91-945c-422273bae7bf", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        [InlineData("8987c30c-7019-4ef5-8214-d1e94c0abeb4", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        [InlineData("95e0ae98-c287-4f51-b23e-4e2317b7e169", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        [InlineData("984a981d-484f-4481-af21-a93c585f5fff", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        [InlineData("a52d35ac-368b-4c8c-b2cd-f8ee93e11647", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        [InlineData("e572d888-8602-44bb-8c01-01bea7b82cf9", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        [InlineData("782e7118-7fb0-4c49-9057-65593350e993", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        [InlineData("ece6cab1-a32a-418a-b1ff-dae0e5a8d5d7", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        [InlineData("ecd580b4-55ff-46a9-b6e8-f7fabf1e4f8f", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        [InlineData("22ee5670-a769-4d11-9ba6-246a73123831", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        [InlineData("c9e22707-27bc-4418-9161-d43db1d1ecde", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        [InlineData("f677f10e-671e-4f99-b171-d89427581c8d", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        [InlineData("c36ade26-cd64-46a8-9baa-fa5aeb8b1e5d", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        public async void TestGetDpv2AssetsFeaturesForOfficeHeronWorkspacesFromAppSettings(string subscription, DatasetAccessModes datasetAccessModes)
        {
            var flightContext = new FlightContext(subscription, "rg1", "ws1");
            var ecsComponentFeatureFlags = await ECSFeatureFlagsHelper.GetComponentFeatureFlagsFromECSAsync(flightContext).ConfigureAwait(false);

            Assert.Equal(datasetAccessModes, ecsComponentFeatureFlags.DatasetAccessMode);
        }

        // Disable Asset approach from UI for Subscriptions using OpenAI
        [Theory]
        [Trait(UnitTestCategory.Category, UnitTestCategory.EcsEmbedded)]
        [InlineData("9ec1d932-0f3f-486c-acc6-e7d78b358f9b", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        [InlineData("79f57c16-00fe-48da-87d4-5192e86cd047", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        [InlineData("e037a063-5c6c-4096-8364-e991ae25471c", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        [InlineData("d187f07f-c6c5-4353-8745-57295faec385", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        [InlineData("a6419425-2187-46bc-a99a-95a5e5c0f7a4", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        [InlineData("a3c33843-de54-4272-84af-a2461c03adaf", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        [InlineData("6ecbe6e9-ff96-40d3-9142-4980478d575b", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        [InlineData("6078c7ef-f1e0-4f2f-96ff-ca66a6adea7d", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        [InlineData("189a137a-d264-4603-a353-0afe30194437", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        [InlineData("90f5cfba-1b3c-4195-ab94-0c4807f14ec4", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        [InlineData("4d7404c2-ddeb-48d8-96f1-e6356cd52cd6", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        [InlineData("e8dca4e8-e6e8-49c6-9e1d-7040389c9be9", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        public async void TestGetDpv2AssetsFeaturesForOpenAiWorkspacesFromAppSettings(string subscription, DatasetAccessModes datasetAccessModes)
        {
            var flightContext = new FlightContext(subscription, "rg1", "ws1");
            var ecsComponentFeatureFlags = await ECSFeatureFlagsHelper.GetComponentFeatureFlagsFromECSAsync(flightContext).ConfigureAwait(false);

            // Subscriptions Allowlisted for creating IPProtected components
            Assert.Equal(datasetAccessModes, ecsComponentFeatureFlags.DatasetAccessMode);
        }

        // Disable Asset approach from UI for Subscriptions used by AML DS Team
        [Theory]
        [Trait(UnitTestCategory.Category, UnitTestCategory.EcsEmbedded)]
        [InlineData("48bbc269-ce89-4f6f-9a12-c6f91fcb772d", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        [InlineData("9ec1d932-0f3f-486c-acc6-e7d78b358f9b", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        [InlineData("79f57c16-00fe-48da-87d4-5192e86cd047", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        [InlineData("c80f1917-35d7-4d58-a840-63f08bf028c4", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        [InlineData("9a9d4049-a7e6-4030-8078-eee256272eee", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        [InlineData("77596b5e-11ee-4c1f-9045-8073488e3440", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        [InlineData("e037a063-5c6c-4096-8364-e991ae25471c", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        [InlineData("0bb7e77d-c2c1-4e60-a4b8-453a81256516", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        [InlineData("1268e469-1031-4193-87cf-561394b3a609", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        public async void TestGetDpv2AssetsFeaturesForAmlDsWorkspacesFromAppSettings(string subscription, DatasetAccessModes datasetAccessModes)
        {
            var flightContext = new FlightContext(subscription, "rg1", "ws1");
            var ecsComponentFeatureFlags = await ECSFeatureFlagsHelper.GetComponentFeatureFlagsFromECSAsync(flightContext).ConfigureAwait(false);

            // Subscriptions Allowlisted for creating IPProtected components
            Assert.Equal(datasetAccessModes, ecsComponentFeatureFlags.DatasetAccessMode);
        }

        // Disable Asset approach from UI for Subscriptions used by customer who consume GPT-3 Components
        [Theory]
        [Trait(UnitTestCategory.Category, UnitTestCategory.EcsEmbedded)]
        [InlineData("42ae47bd-b19b-42c1-b0b9-19fd5be9d51b", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        [InlineData("d0c05057-7972-46ff-9bcf-3c932250155e", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        public async void TestGetDpv2AssetsFeaturesForWorkspacesUsingGpt3FromAppSettings(string subscription, DatasetAccessModes datasetAccessModes)
        {
            var flightContext = new FlightContext(subscription, "rg1", "ws1");
            var ecsComponentFeatureFlags = await ECSFeatureFlagsHelper.GetComponentFeatureFlagsFromECSAsync(flightContext).ConfigureAwait(false);
            // Subscriptions Allowlisted for creating IPProtected components
            Assert.Equal(datasetAccessModes, ecsComponentFeatureFlags.DatasetAccessMode);
        }

        [Theory]
        [Trait(UnitTestCategory.Category, UnitTestCategory.EcsEmbedded)]
        [InlineData("20d3c9e4-625d-45e1-ac8a-def90d3c4a88", "RSTrainEastUS_32GB-2-aml", "relevance2-aml")]
        [InlineData("20d3c9e4-625d-45e1-ac8a-def90d3c4a88", "RSTrainEastUS_32GB-2-aml", "smile-ws01-eus")]
        [InlineData("20d3c9e4-625d-45e1-ac8a-def90d3c4a88", "coresearchsingularitywus3", "wxtcssingwus3")]
        [InlineData("07c3f5a2-0880-4a37-b678-e29ade7b1036", "Singularity", "wxtcssing")]
        [InlineData("814b8bcc-5e54-487e-a9fa-27b5740ac7b9", "prometheus", "dv3batch")]
        public async void TestGetFeaturesForWebXTEnabledPartitionShouldUseAssetInDpv2WithDatasetInDesignerUI(string subscriptionId, string resourceGroup, string workspaceName)
        {
            var flightContext = new FlightContext(subscriptionId, resourceGroup, workspaceName);
            var ecsComponentFeatureFlags = await ECSFeatureFlagsHelper.GetComponentFeatureFlagsFromECSAsync(flightContext).ConfigureAwait(false);
            // Ensure WebXT Workspace flight setting as expected
            Assert.Equal(DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI, ecsComponentFeatureFlags.DatasetAccessMode);
        }

        #region Office Subscriptions

        #region DatasetAccessModes

        // Office Subscription list from https://msdata.visualstudio.com/Vienna/_git/vienna?path=/src/aether/platform/Microsoft.MachineLearning.Pipeline.Common/config.json&_a=contents&version=GBmaster
        [Theory]
        [Trait(UnitTestCategory.Category, UnitTestCategory.EcsEmbedded)]
        // Compliant_SUBSCRIPTIONID_Set.EuclidSubstrateAI_DEV
        [InlineData("5441ecdb-f91b-4807-b36b-092d9a711d8f", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Compliant_SUBSCRIPTIONID_Set.EuclidSubstrateAI_04ef1951
        [InlineData("04ef1951-3164-43a0-95be-e1acb3e028ba", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Compliant_SUBSCRIPTIONID_Set.HeronSandboxTorus_AMLEyesOn
        [InlineData("08047947-f71e-4462-a09d-266e3d34c431", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Compliant_SUBSCRIPTIONID_Set.HeronSandboxTorus_Subscription1
        [InlineData("a6d8cf0d-b71e-4ffe-9a03-3e6013fed98a", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Compliant_SUBSCRIPTIONID_Set.HeronSandboxTorus_Subscription1a
        [InlineData("e572d888-8602-44bb-8c01-01bea7b82cf9", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Compliant_SUBSCRIPTIONID_Set.HeronSandboxTorus_Subscription1b
        [InlineData("984a981d-484f-4481-af21-a93c585f5fff", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Compliant_SUBSCRIPTIONID_Set.HeronSandboxTorus_Subscription1c
        [InlineData("86956e13-ab30-4f91-945c-422273bae7bf", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Compliant_SUBSCRIPTIONID_Set.HeronSandboxTorus_Subscription1d
        [InlineData("69907770-0514-44eb-a027-6c3de34eac20", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Compliant_SUBSCRIPTIONID_Set.HeronSandboxTorus_Subscription1e
        [InlineData("27a3c1ec-7827-4d89-93b8-dfbb82e9c56f", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Compliant_SUBSCRIPTIONID_Set.HeronSandboxTorus_Subscription2
        [InlineData("60d27411-7736-4355-ac95-ac033929fe9d", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Compliant_SUBSCRIPTIONID_Set.MicrosoftAzureInternalConsumption
        [InlineData("2dea9532-70d2-472d-8ebd-6f53149ad551", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Compliant_SUBSCRIPTIONID_Set.HeronSandboxTorus_Subscription1f
        [InlineData("a52d35ac-368b-4c8c-b2cd-f8ee93e11647", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Compliant_SUBSCRIPTIONID_Set.HeronSandboxTorus_Subscription1g
        [InlineData("3265b6a5-866d-469b-830f-0baacd77d045", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Compliant_SUBSCRIPTIONID_Set.Heron_Sandbox_LoFT_Eyes-on_Torus_Subscription1
        [InlineData("95e0ae98-c287-4f51-b23e-4e2317b7e169", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Compliant_SUBSCRIPTIONID_Set.Heron_Sandbox_MDP_Eyes-on_Torus_Subscription1
        [InlineData("8987c30c-7019-4ef5-8214-d1e94c0abeb4", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Compliant_SUBSCRIPTIONID_Set.Heron_Sandbox_Cloverport_Eyes-on_Torus_Subscription1
        [InlineData("3e02707f-7ddb-4796-ad0f-32f4f4b22757", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Compliant_SUBSCRIPTIONID_Set.Heron Sandbox_LoFT_Eyes-on_Torus_Subscription2
        [InlineData("65b8b073-c2f2-4c66-aa26-f77a73adc0ac", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Compliant_SUBSCRIPTIONID_Set.Heron_Sandbox_MDP_Eyes-on_Torus_Subscription2
        [InlineData("af1d7d72-11ca-495f-909f-7e91c65d7e22", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Compliant_SUBSCRIPTIONID_Set.Heron_Sandbox_Cloverport_Eyes-on_Torus_Subscription2
        [InlineData("e6a8489b-9f13-4a67-8e86-bedc678bbae0", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Compliant_SUBSCRIPTIONID_Set.Heron_Sandbox_Refice_Torus_Subscription1
        [InlineData("cc86c879-d74d-46ca-a616-0082ce266ed4", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Compliant_SUBSCRIPTIONID_Set.Heron_Sandbox_Refice_Torus_Subscription2
        [InlineData("7e914327-0cdd-41e1-9ef9-183c51a9d79f", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Compliant_SUBSCRIPTIONID_Set.Heron_Recife_Prod_2
        [InlineData("54d27c16-ab6a-4cb5-9c77-e5e0f55cf78c", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Compliant_SUBSCRIPTIONID_Set.Heron_Recife_Prod_3
        [InlineData("4a48b81a-d7ad-41cd-a73b-2bba8ae07d5d", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Compliant_SUBSCRIPTIONID_Set.Heron_Recife_Prod_4
        [InlineData("98d476c6-c75b-481c-8c18-f53d91f614b0", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Compliant_SUBSCRIPTIONID_Set.Heron_Recife_Prod_4
        [InlineData("08b539cd-231e-40f5-9cf1-a2a3884f63d5", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Office_SUBSCRIPTIONID_Set: EuclidHeron_WW_PPE
        [InlineData("050cafaf-e1b4-4d87-917b-c6e77f350957", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Office_SUBSCRIPTIONID_Set: EuclidHeron_WW_PROD
        [InlineData("757c5582-650e-47dc-815a-cd506d8f071a", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Office_SUBSCRIPTIONID_Set: HeronGeneva
        [InlineData("6ef6e336-6637-4591-8e52-4984629911de", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Office_SUBSCRIPTIONID_Set: EuclidSubstrateAI_553d0e20
        [InlineData("553d0e20-193a-4c15-987d-6c3a5cb7753c", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Subscription Name: Heron MDP Torus Subscription 1 (PPE)
        [InlineData("c9e22707-27bc-4418-9161-d43db1d1ecde", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Subscription Name: HERON WHITE-GLOVE PROD 1
        [InlineData("f677f10e-671e-4f99-b171-d89427581c8d", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Subscription Name: HERON WHITE-GLOVE PPE 1
        [InlineData("c36ade26-cd64-46a8-9baa-fa5aeb8b1e5d", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        public async void TestGetDpv2AssetsFeaturesForWorkspacesUsingOfficeTeamSpecialSetInAetherFromAppSettings(string subscription, DatasetAccessModes datasetAccessModes)
        {
            var flightContext = new FlightContext(subscription, "rg1", "ws1");
            var ecsComponentFeatureFlags = await ECSFeatureFlagsHelper.GetComponentFeatureFlagsFromECSAsync(flightContext).ConfigureAwait(false);

            // Subscriptions Allowlisted for creating IPProtected components

            Assert.Equal(datasetAccessModes, ecsComponentFeatureFlags.DatasetAccessMode);
        }

        // Policy Service enabled Subscription list from https://msdata.visualstudio.com/Vienna/_git/vienna?path=/src/aether/platform/backendV2/shared/SharedConfigurationSettings/BBSharedSettings.ini&_a=contents&version=GBmaster
        [Theory]
        [Trait(UnitTestCategory.Category, UnitTestCategory.EcsEmbedded)]
        [InlineData("b8c23406-f9b5-4ccb-8a65-a8cb5dcd6a5a", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        [InlineData("5441ecdb-f91b-4807-b36b-092d9a711d8f", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        [InlineData("2dea9532-70d2-472d-8ebd-6f53149ad551", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        [InlineData("a6d8cf0d-b71e-4ffe-9a03-3e6013fed98a", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        [InlineData("60d27411-7736-4355-ac95-ac033929fe9d", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        [InlineData("4bee225b-b468-40f2-992a-d6e8b4ba2f93", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        [InlineData("dd7c8df5-352b-4962-a67d-e567958e17e8", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        [InlineData("08047947-f71e-4462-a09d-266e3d34c431", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        [InlineData("050CAFAF-E1B4-4D87-917B-C6E77F350957", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        [InlineData("757c5582-650e-47dc-815a-cd506d8f071a", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        [InlineData("57457440-f086-40a2-a08b-78aa0a7e3620", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        [InlineData("65b8b073-c2f2-4c66-aa26-f77a73adc0ac", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        [InlineData("95e0ae98-c287-4f51-b23e-4e2317b7e169", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        [InlineData("cc86c879-d74d-46ca-a616-0082ce266ed4", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        [InlineData("7e914327-0cdd-41e1-9ef9-183c51a9d79f", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        [InlineData("e6a8489b-9f13-4a67-8e86-bedc678bbae0", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        [InlineData("3e02707f-7ddb-4796-ad0f-32f4f4b22757", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        [InlineData("af1d7d72-11ca-495f-909f-7e91c65d7e22", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        [InlineData("8987c30c-7019-4ef5-8214-d1e94c0abeb4", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        [InlineData("4a48b81a-d7ad-41cd-a73b-2bba8ae07d5d", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        [InlineData("98d476c6-c75b-481c-8c18-f53d91f614b0", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        [InlineData("54d27c16-ab6a-4cb5-9c77-e5e0f55cf78c", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        [InlineData("08b539cd-231e-40f5-9cf1-a2a3884f63d5", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        public async void TestGetDpv2AssetsFeaturesForWorkspacesUsingPolicyServiceFromAppSettings(string subscription, DatasetAccessModes datasetAccessModes)
        {
            var flightContext = new FlightContext(subscription, "rg1", "ws1");
            var ecsComponentFeatureFlags = await ECSFeatureFlagsHelper.GetComponentFeatureFlagsFromECSAsync(flightContext).ConfigureAwait(false);

            // Subscriptions Allowlisted for creating IPProtected components


            Assert.Equal(datasetAccessModes, ecsComponentFeatureFlags.DatasetAccessMode);
        }

        // Office Subscriptions from Heron wiki: https://eng.ms/docs/experiences-devices/m365-core/microsoft-search-assistants-intelligence-msai/substrate-intelligence/ai-training-heron/documentation/subscriptions
        [Theory]
        [Trait(UnitTestCategory.Category, UnitTestCategory.EcsEmbedded)]
        // Subscription Name: Euclid Substrate AI -- DEV [INT]
        [InlineData("5441ecdb-f91b-4807-b36b-092d9a711d8f", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Subscription Name: Heron INT Torus [INT]
        [InlineData("1e35525a-9103-45d5-8587-2b3383d8be59", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Subscription Name: Euclid-Heron-WW-PPE [PPE]
        [InlineData("050cafaf-e1b4-4d87-917b-c6e77f350957", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Subscription Name: Euclid-Heron-WW-PROD [PROD]
        [InlineData("757c5582-650e-47dc-815a-cd506d8f071a", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Subscription Name: Heron Geneva []
        [InlineData("6ef6e336-6637-4591-8e52-4984629911de", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Subscription Name: Euclid Substrate AI 04ef1951 [PPE]
        [InlineData("04ef1951-3164-43a0-95be-e1acb3e028ba", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Subscription Name: Euclid Substrate AI 553d0e20 [PPE]
        [InlineData("553d0e20-193a-4c15-987d-6c3a5cb7753c", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Subscription Name: Heron Sandbox Torus Subscription 3 (PROD) [PROD]
        [InlineData("08047947-f71e-4462-a09d-266e3d34c431", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Subscription Name: Heron Sandbox Torus Subscription 1 (PPE) [PPE]
        [InlineData("a6d8cf0d-b71e-4ffe-9a03-3e6013fed98a", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Subscription Name[:PROD]
        [InlineData("e572d888-8602-44bb-8c01-01bea7b82cf9", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Subscription Name[PROD]
        [InlineData("984a981d-484f-4481-af21-a93c585f5fff", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Subscription Name[PROD]
        [InlineData("86956e13-ab30-4f91-945c-422273bae7bf", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Subscription Name[PROD]
        [InlineData("69907770-0514-44eb-a027-6c3de34eac20", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Subscription Name[PROD]
        [InlineData("27a3c1ec-7827-4d89-93b8-dfbb82e9c56f", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Subscription Name[PROD]
        [InlineData("a52d35ac-368b-4c8c-b2cd-f8ee93e11647", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Subscription Name[PROD]
        [InlineData("3265b6a5-866d-469b-830f-0baacd77d045", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Subscription Name: Heron Sandbox Torus Subscription 2 (PROD) [PROD]
        [InlineData("60d27411-7736-4355-ac95-ac033929fe9d", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Subscription Name: Microsoft Azure Internal Consumption (PROD) [PROD]
        [InlineData("2dea9532-70d2-472d-8ebd-6f53149ad551", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Subscription Name: Heron Sandbox LoFT Eyes-on Torus Subscription 1 [PROD]
        [InlineData("95e0ae98-c287-4f51-b23e-4e2317b7e169", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Subscription Name: Heron Sandbox Cloverport Eyes-on Torus Subscription 1 [PROD]
        [InlineData("3e02707f-7ddb-4796-ad0f-32f4f4b22757", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Subscription Name: Heron Sandbox Recife Eyes-on Torus Subscription 1 [PROD]
        [InlineData("7e914327-0cdd-41e1-9ef9-183c51a9d79f", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Subscription Name: Heron Sandbox Recife Eyes-on Torus Subscription 2 [PROD]
        [InlineData("54d27c16-ab6a-4cb5-9c77-e5e0f55cf78c", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Subscription Name: Heron Sandbox Recife Eyes-on Torus Subscription 3 [PROD]
        [InlineData("4a48b81a-d7ad-41cd-a73b-2bba8ae07d5d", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Subscription Name: Heron Sandbox Recife Eyes-on Torus Subscription 4 [PROD]
        [InlineData("98d476c6-c75b-481c-8c18-f53d91f614b0", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Subscription Name: Heron Sandbox MDP Torus Eyes-on Torus Subscription 1 [PROD]
        [InlineData("8987c30c-7019-4ef5-8214-d1e94c0abeb4", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Subscription Name: Heron Sandbox LoFT Eyes-on Torus Subscription 2 [PPE]
        [InlineData("65b8b073-c2f2-4c66-aa26-f77a73adc0ac", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Subscription Name: Heron Sandbox Cloverport Eyes-on Torus Subscription 2 [PPE]
        [InlineData("e6a8489b-9f13-4a67-8e86-bedc678bbae0", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Subscription Name: Heron Sandbox Recife Eyes-on Torus Subscription 2 [PPE]
        [InlineData("cc86c879-d74d-46ca-a616-0082ce266ed4", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Subscription Name: Heron Sandbox MDP Torus Eyes-on Torus Subscription 2 [PPE]
        [InlineData("af1d7d72-11ca-495f-909f-7e91c65d7e22", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        // Subscription Name: Heron Rufous Phase 2 [PROD]
        [InlineData("bf6971b7-84b9-464a-b24f-4b0d3a35d02f", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        public async void TestGetDpv2AssetsFeaturesForOfficeWorkspacesInHeronWikiFromAppSettings(string subscription, DatasetAccessModes datasetAccessModes)
        {
            var flightContext = new FlightContext(subscription, "rg1", "ws1");
            var ecsComponentFeatureFlags = await ECSFeatureFlagsHelper.GetComponentFeatureFlagsFromECSAsync(flightContext).ConfigureAwait(false);

            // Subscriptions Allowlisted for creating IPProtected components


            Assert.Equal(datasetAccessModes, ecsComponentFeatureFlags.DatasetAccessMode);
        }

        #endregion DatasetAccessModes

        #endregion Office Subscriptions



        // Subscriptions Allowlisted for creating IPProtected components
        [Theory]
        [Trait(UnitTestCategory.Category, UnitTestCategory.EcsEmbedded)]
        [InlineData("74326f65-ea04-4329-b24a-2c60e71ebb49", "OpenAI")]
        [InlineData("b8bd4e13-0352-4fe3-a1dc-740d47250b0d", "OpenAI")]
        [InlineData("c80f1917-35d7-4d58-a840-63f08bf028c4", "OpenAI")] // Added on 2022-08-23
        [InlineData("9a9d4049-a7e6-4030-8078-eee256272eee", "OpenAI")] // Azure OAI Dev Mumford Optimization 1PPoolOCTO, Added on 2022-08-26
        [InlineData("b8c23406-f9b5-4ccb-8a65-a8cb5dcd6a5a", "Contoso")]
        public async void TestGetIPProtectedFeaturesFromAppSettings(string subscription, string publisher)
        {
            var flightContext = new FlightContext(subscription, "rg1", "ws1");
            var ecsComponentFeatureFlags = await ECSFeatureFlagsHelper.GetComponentFeatureFlagsFromECSAsync(flightContext).ConfigureAwait(false);

            // Subscriptions Allowlisted for creating IPProtected components
            Assert.True(Enumerable.SequenceEqual(new List<string> { publisher }, ecsComponentFeatureFlags.IntellectualPropertyProtectedWorkspaceComponentRegistrationAllowedPublisher));
            Assert.Equal(DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI, ecsComponentFeatureFlags.DatasetAccessMode);
        }

        // Subscriptions Allowlisted for creating IPProtected components
        [Theory]
        [Trait(UnitTestCategory.Category, UnitTestCategory.EcsEmbedded)]
        [InlineData("e8a709de-1c4d-480b-86ea-f0cfda40e22a", "OpenAI")]
        [InlineData("74326f65-ea04-4329-b24a-2c60e71ebb49", "OpenAI")]
        [InlineData("b8bd4e13-0352-4fe3-a1dc-740d47250b0d", "OpenAI")]
        [InlineData("c80f1917-35d7-4d58-a840-63f08bf028c4", "OpenAI")] // Added on 2022-08-23
        [InlineData("9a9d4049-a7e6-4030-8078-eee256272eee", "OpenAI")] // Azure OAI Dev Mumford Optimization 1PPoolOCTO, Added on 2022-08-26
        [InlineData("7715552a-19e8-4ff7-9782-bf92907063bf", "OpenAI")] // Mumford Devault Dev FineTuning Resources
        [InlineData("ed2cab61-14cc-4fb3-ac23-d72609214cfd", "Contoso")]
        [InlineData("b8c23406-f9b5-4ccb-8a65-a8cb5dcd6a5a", "Contoso")]
        public async void TestGetIPProtectedPublisherFeaturesFromAppSettings(string subscription, string publisher)
        {
            var flightContext = new FlightContext(subscription, "rg1", "ws1");
            var ecsComponentFeatureFlags = await ECSFeatureFlagsHelper.GetComponentFeatureFlagsFromECSAsync(flightContext).ConfigureAwait(false);

            // Subscriptions Allowlisted for creating IPProtected components
            Assert.True(Enumerable.SequenceEqual(new List<string> { publisher }, ecsComponentFeatureFlags.IntellectualPropertyProtectedWorkspaceComponentRegistrationAllowedPublisher));
        }

        // Subscriptions Allowlisted for creating IPProtected components
        [Theory]
        [Trait(UnitTestCategory.Category, UnitTestCategory.EcsEmbedded)]
        [InlineData("7715552a-19e8-4ff7-9782-bf92907063bf", "DefaultResourceGroup-eastus", new string[] { "OpenAI", "OpenAIDevault" })] // Mumford Devault Dev FineTuning Resources
        [InlineData("7715552a-19e8-4ff7-9782-bf92907063bf", "mumford-devault-ft-rg", new string[] { "OpenAI", "OpenAIDevault" })]
        public async void TestGetIPProtectedPublisherFeaturesFromAppSettingsWithRg(string subscription, string resourceGroup, string[] publishers)
        {
            var flightContext = new FlightContext(subscription, resourceGroup, "ws1");
            var ecsComponentFeatureFlags = await ECSFeatureFlagsHelper.GetComponentFeatureFlagsFromECSAsync(flightContext).ConfigureAwait(false);

            // Subscriptions Allowlisted for creating IPProtected components
            Assert.True(Enumerable.SequenceEqual(publishers, ecsComponentFeatureFlags.IntellectualPropertyProtectedWorkspaceComponentRegistrationAllowedPublisher));
        }

        // WS allowed for SMT_IntegrationTest_EUS
        [Theory]
        [Trait(UnitTestCategory.Category, UnitTestCategory.EcsEmbedded)]
        [InlineData("74eccef0-4b8d-4f83-b5f9-fa100d155b22", "SMT_IntegrationTest", "SMT_IntegrationTest_EastUs", new string[] { "OpenAI", "GPT3" })] // Mumford Devault Dev FineTuning Resources
        public async void TestGetIPProtectedPublisherFeaturesFromAppSettingsForSMTEUS(string subscription, string resourceGroup, string workspace, string[] publishers)
        {
            var flightContext = new FlightContext(subscription, resourceGroup, workspace);
            var ecsComponentFeatureFlags = await ECSFeatureFlagsHelper.GetComponentFeatureFlagsFromECSAsync(flightContext).ConfigureAwait(false);

            // Subscriptions Allowlisted for creating IPProtected components
            Assert.True(Enumerable.SequenceEqual(publishers, ecsComponentFeatureFlags.IntellectualPropertyProtectedWorkspaceComponentRegistrationAllowedPublisher));
        }

        // Dev Subscriptions Setting for DPv2 DatasetAccessMode
        [Theory]
        [Trait(UnitTestCategory.Category, UnitTestCategory.EcsEmbedded)]
        [InlineData("96aede12-2f73-41cb-b983-6d11a904839b", DatasetAccessModes.AssetInDpv2WithAssetInDesignerUI)]
        [InlineData("d128f140-94e6-4175-87a7-954b9d27db16", DatasetAccessModes.DatasetInDpv2WithDatasetInDesignerUI)]
        [InlineData("e9b2ec51-5c94-4fa8-809a-dc1e695e4896", DatasetAccessModes.AssetInDpv2WithAssetInDesignerUI)]
        [InlineData("74eccef0-4b8d-4f83-b5f9-fa100d155b22", DatasetAccessModes.AssetInDpv2WithAssetInDesignerUI)]
        [InlineData("4f455bd0-f95a-4b7d-8d08-078611508e0b", DatasetAccessModes.AssetInDpv2WithAssetInDesignerUI)]
        [InlineData("10a1cf3f-9a29-4c5d-aef4-0f6a234190af", DatasetAccessModes.AssetInDpv2WithAssetInDesignerUI)]
        [InlineData("4e7b3094-d7f1-4973-8734-e82a24f1590e", DatasetAccessModes.AssetInDpv2WithAssetInDesignerUI)]
        [InlineData("f8f8b81c-3ebd-427c-b3b7-ebb226c4eea2", DatasetAccessModes.AssetInDpv2WithAssetInDesignerUI)]
        [InlineData("6247b2f5-8f90-43eb-944c-061d3188a248", DatasetAccessModes.AssetInDpv2WithAssetInDesignerUI)]
        [InlineData("589c7ae9-223e-45e3-a191-98433e0821a9", DatasetAccessModes.AssetInDpv2WithAssetInDesignerUI)]
        [InlineData("b8c23406-f9b5-4ccb-8a65-a8cb5dcd6a5a", DatasetAccessModes.AssetInDpv2WithDatasetInDesignerUI)]
        public async void TestGetDpv2AssetsFeaturesFromAppSettings(string subscription, DatasetAccessModes datasetAccessModes)
        {
            var flightContext = new FlightContext(subscription, "rg1", "ws1");
            var ecsComponentFeatureFlags = await ECSFeatureFlagsHelper.GetComponentFeatureFlagsFromECSAsync(flightContext).ConfigureAwait(false);

            Assert.Equal(datasetAccessModes, ecsComponentFeatureFlags.DatasetAccessMode);
        }

        // Enforce using Asset for both DPv2 and UI for resource groups used by DPv2 example CL/CD pipelines, tracked by Task 1915000
        [Theory]
        [Trait(UnitTestCategory.Category, UnitTestCategory.EcsEmbedded)]
        [InlineData("6560575d-fa06-4e7d-95fb-f962e74efd7a", "azureml-examples")]
        [InlineData("6560575d-fa06-4e7d-95fb-f962e74efd7a", "azureml-examples-v2")]
        [InlineData("6560575d-fa06-4e7d-95fb-f962e74efd7a", "azureml-examples-rg")]
        public async void TestGetDatasetAccessModeForPMDpv2SampleWorkspaces(string subscription, string resourceGroup)
        {
            var flightContext = new FlightContext(subscription, resourceGroup, "ws1");
            var ecsComponentFeatureFlags = await ECSFeatureFlagsHelper.GetComponentFeatureFlagsFromECSAsync(flightContext).ConfigureAwait(false);

            // Enforce using Asset for both DPv2 and UI
            Assert.Equal(DatasetAccessModes.AssetInDpv2WithAssetInDesignerUI, ecsComponentFeatureFlags.DatasetAccessMode);
        }

        [Theory]
        [Trait(UnitTestCategory.Category, UnitTestCategory.EcsEmbedded)]
        [InlineData("96aede12-2f73-41cb-b983-6d11a904839b", "rge2etests", "wse2etests")]
        [InlineData("96aede12-2f73-41cb-b983-6d11a904839b", "pmt_test", "pmt_test_master_webxt")]
        [InlineData("316a14b3-f94a-4267-a76a-497e589ef0a8", "training", "wxtcstrain")]
        [InlineData("20d3c9e4-625d-45e1-ac8a-def90d3c4a88", "RSTrainEastUS_32GB-2-aml", "relevance2-aml")]
        [InlineData("20d3c9e4-625d-45e1-ac8a-def90d3c4a88", "RSTrainEastUS_32GB-2-aml", "smile-ws01-eus")]
        [InlineData("20d3c9e4-625d-45e1-ac8a-def90d3c4a88", "coresearchsingularitywus3", "wxtcssingwus3")]
        [InlineData("07c3f5a2-0880-4a37-b678-e29ade7b1036", "Singularity", "wxtcssing")]
        [InlineData("814b8bcc-5e54-487e-a9fa-27b5740ac7b9", "prometheus", "dv3batch")]
        public async void TestGetFeaturesForAutoPartitionADLSGen1OutputFromAppSettings(string subscriptionId, string resourceGroup, string workspaceName)
        {
            var flightContext = new FlightContext(subscriptionId, resourceGroup, workspaceName);
            var ecsComponentFeatureFlags = await ECSFeatureFlagsHelper.GetComponentFeatureFlagsFromECSAsync(flightContext).ConfigureAwait(false);

            Assert.True(ecsComponentFeatureFlags.AutoPartitionADLSGen1Output);
        }
        #endregion

    }
}
