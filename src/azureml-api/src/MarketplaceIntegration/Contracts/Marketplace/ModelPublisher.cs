﻿// <copyright file="ModelPublisher.cs" company="Microsoft Corporation">
// Copyright (c) 2024 Microsoft Corporation. All rights reserved.
// </copyright>

using System.ComponentModel;

namespace Microsoft.MachineLearning.MarketplaceIntegration.Contracts.Marketplace;

public class ModelPublisher
{
    public string PublisherName { get; set; }

    [ReadOnly(true)]
    public string PublisherId { get; set; }

    public Guid ValidAudience { get; set; }

    public Guid WebhookId { get; set; }

    public IList<PlanProperties> Plans { get; set; }
}
