﻿// <copyright file="BatchEndpointHelper.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Globalization;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.MachineLearning.Common.Core.Contracts;
using Microsoft.MachineLearning.Common.Core.Contracts.Errors;
using Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts.Extensions;
using Microsoft.MachineLearning.ManagementFrontEnd.Services.Clients;
using Polly;
using MmsCommon = Microsoft.MachineLearning.ModelManagement.Contracts.Mms.Common;

namespace Microsoft.MachineLearning.ManagementFrontEnd.Services.Helpers
{
    public interface IBatchEndpointHelper
    {
        Task RetryOperationStatus(WorkspaceContext2 workspaceContext, string endpointName, string operationId);
    }

    public class BatchEndpointHelper : IBatchEndpointHelper
    {
        private const string BatchEndpointARMResourceIdTemplate = "/subscriptions/{0}/resourceGroups/{1}/providers/Microsoft.MachineLearningServices/workspaces/{2}/batchEndpoints/{3}";
        private const string BatchDeploymentARMResourceIdTemplate = "/subscriptions/{0}/resourceGroups/{1}/providers/Microsoft.MachineLearningServices/workspaces/{2}/batchEndpoints/{3}/deployments/{4}";

        private readonly ILogger _logger;
        private readonly IModelManagementServiceClient _mmsClient;
        private readonly IAsyncPolicy _retryPolicy;

        public BatchEndpointHelper(
            ILoggerFactory loggerFactory,
            IModelManagementServiceClient mmsClient)
        {
            _logger = loggerFactory.CreateLogger<BatchEndpointHelper>();
            _mmsClient = mmsClient;
            _retryPolicy = Policy
                .Handle<Exception>()
                .WaitAndRetryAsync(
                    10,
                    i => TimeSpan.FromMilliseconds(100 * Math.Pow(2, i)),
                    (e, timeSpan) => _logger.LogWarning(e, $"Retrying in {nameof(BatchEndpointHelper)} after exception. TimeSpan: {timeSpan}, Exception: {e}"));
        }

        public static string GenerateBatchEndpointARMResourceId(WorkspaceContext2 context, string endpointName)
        {
            return string.Format(
               CultureInfo.InvariantCulture,
               BatchEndpointARMResourceIdTemplate,
               context.SubscriptionId,
               context.ResourceGroup,
               context.WorkspaceName,
               endpointName);
        }

        public static string GenerateBatchDeploymentARMResourceId(WorkspaceContext2 context, string endpointName, string deploymentName)
        {
            return string.Format(
               CultureInfo.InvariantCulture,
               BatchDeploymentARMResourceIdTemplate,
               context.SubscriptionId,
               context.ResourceGroup,
               context.WorkspaceName,
               endpointName,
               deploymentName);
        }

        public async Task RetryOperationStatus(WorkspaceContext2 workspaceContext, string entityName, string operationId)
        {
            await _retryPolicy.ExecuteAsync(async () =>
            {
                var operationStatus = await this._mmsClient.GetOperationStatus(workspaceContext, operationId).ConfigureAwait(false);
                _logger.LogWarning($"Retrying to get operation status for entity : {entityName}, status: {operationStatus.State}, operation ID: {operationId}");
                if (operationStatus.State != MmsCommon.AsyncOperationState.Succeeded
                && operationStatus.State != MmsCommon.AsyncOperationState.Failed
                && operationStatus.State != MmsCommon.AsyncOperationState.Cancelled
                && operationStatus.State != MmsCommon.AsyncOperationState.TimedOut)
                {
                    throw new NotReady().ToException(entityName);
                }
                return;
            }).ConfigureAwait(false);
        }
    }
}
