﻿// <copyright file="ComponentDeploymentConstants.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

namespace Microsoft.MachineLearning.ManagementFrontEnd.Services.Helpers
{
    public static class ComponentDeploymentConstants
    {
        public const string ComponentDeploymentKey = "ComponentDeployment";
        public const string ComponentIdKey = $"{ComponentDeploymentKey}.ComponentId";
        public const string ContinueOnStepFailureSetting = "continue_on_step_failure";
        public const string ContinueOnStepFailureSettingsKey = $"{ComponentDeploymentKey}.Settings.{ContinueOnStepFailureSetting}";
        public const string DefaultComputeSetting = "default_compute";
        public const string DefaultComputeSettingsKey = $"{ComponentDeploymentKey}.Settings.{DefaultComputeSetting}";
        public const string DefaultDatastoreSetting = "default_datastore";
        public const string DefaultDatastoreSettingsKey = $"{ComponentDeploymentKey}.Settings.{DefaultDatastoreSetting}";
        public const string DescriptionKey = $"{ComponentDeploymentKey}.Description";
        public const string EndpointComponentDeploymentEnabledKey = $"{ComponentDeploymentKey}.Enabled";
        public const string ExperimentNameKey = $"{ComponentDeploymentKey}.Name";
        public const string ForceRerunSetting = "force_rerun";
        public const string ForceRerunSettingsKey = $"{ComponentDeploymentKey}.Settings.{ForceRerunSetting}";
        public const string TagsKey = $"{ComponentDeploymentKey}.Tags";
    }
}
