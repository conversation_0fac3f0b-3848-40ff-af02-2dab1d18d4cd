﻿// <copyright file="CloneHelper.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Microsoft.MachineLearning.ManagementFrontEnd.Services.Extensions;
using Newtonsoft.Json;

namespace Microsoft.MachineLearning.ManagementFrontEnd.Services.Helpers
{
    public static class CloneHelper
    {
        public static T ObjectClone<T>(T t)
        {
            var settings = new JsonSerializerSettings();
            return JsonConvert.DeserializeObject<T>(JsonConvert.SerializeObject(t), settings.AddCustomConverters());
        }
    }
}
