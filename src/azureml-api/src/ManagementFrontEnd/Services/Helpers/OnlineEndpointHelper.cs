﻿// <copyright file="OnlineEndpointHelper.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Globalization;
using System.Text.RegularExpressions;
using Microsoft.MachineLearning.Common.Core.Contracts;
using Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts;
using Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts.Common;
using Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts.Endpoints;
using Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts.Endpoints.OnlineEndpointDeployment;
using Microsoft.MachineLearning.ModelManagement.Contracts.Mms.WireContracts;

namespace Microsoft.MachineLearning.ManagementFrontEnd.Services.Helpers
{
    public static class OnlineEndpointHelper
    {
        public const string MdcPublicPreviewTagKey = "data_collector.public_preview";
        public const string MdcRollingRateTagKey = "data_collector.rolling_rate";
        public const string MdcCaptureHeadersKey = "data_collector.request_logging.capture_headers";

        public static readonly Regex MdcEnabledTagsRegex = new Regex(
            pattern: @"^data_collector.collections.([^/?]+).enabled$",
            options: RegexOptions.Compiled | RegexOptions.IgnoreCase,
            matchTimeout: TimeSpan.FromSeconds(1));

        private const string OnlineEndpointARMResourceIdTemplate = "/subscriptions/{0}/resourceGroups/{1}/providers/Microsoft.MachineLearningServices/workspaces/{2}/onlineEndpoints/{3}";
        private const string OnlineDeploymentARMResourceIdTemplate = "/subscriptions/{0}/resourceGroups/{1}/providers/Microsoft.MachineLearningServices/workspaces/{2}/onlineEndpoints/{3}/deployments/{4}";

        public static string GetAppendedEndpointValueForDeployment(string endpointName)
        {
            return $"-{endpointName}";
        }

        public static string RemoveEndpointNameFromService(ServiceResponseBase service, string endpointName)
        {
            return service.Name.Replace(OnlineEndpointHelper.GetAppendedEndpointValueForDeployment(endpointName), string.Empty, StringComparison.OrdinalIgnoreCase);
        }

        public static string AppendEndpointNameToDeployment(string deploymentName, string endpointName)
        {
            return $"{deploymentName}{GetAppendedEndpointValueForDeployment(endpointName)}";
        }

        public static EndpointComputeType DetermineEndpointType(string target)
        {
            return string.IsNullOrEmpty(target) ? EndpointComputeType.Managed : EndpointComputeType.K8S;
        }

        public static IDictionary<string, string> AppendPropertiesWithEndpointArmId(IDictionary<string, string> properties, string armId)
        {
            properties ??= new Dictionary<string, string>();
            properties[MfeConstants.AzureMLOnlineEndpointProperty] = armId.ToLowerInvariant();
            return properties;
        }

        public static Microsoft.MachineLearning.InferenceDeployment.WebApi.Contracts.V20211001.Endpoint.RegenerateEndpointKeysRequest ConvertRegenerateEndpointKeysRequest(RegenerateEndpointKeysRequest request)
        {
            var convertedRequest = new Microsoft.MachineLearning.InferenceDeployment.WebApi.Contracts.V20211001.Endpoint.RegenerateEndpointKeysRequest();
            switch (request.KeyType)
            {
                case Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts.Endpoints.KeyType.Primary:
                    convertedRequest.KeyType = InferenceDeployment.WebApi.Contracts.V20211001.Endpoint.KeyType.Primary;
                    break;
                case Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts.Endpoints.KeyType.Secondary:
                    convertedRequest.KeyType = InferenceDeployment.WebApi.Contracts.V20211001.Endpoint.KeyType.Secondary;
                    break;
                default:
                    break;
            }
            convertedRequest.KeyValue = request.KeyValue;
            return convertedRequest;
        }

        public static string GenerateOnlineEndpointARMResourceId(WorkspaceContext2 context, string name)
        {
            return string.Format(
               CultureInfo.InvariantCulture,
               OnlineEndpointARMResourceIdTemplate,
               context.SubscriptionId,
               context.ResourceGroup,
               context.WorkspaceName,
               name);
        }

        public static string GenerateOnlineDeploymentARMResourceId(WorkspaceContext2 context, string endpointName, string name)
        {
            return string.Format(
               CultureInfo.InvariantCulture,
               OnlineDeploymentARMResourceIdTemplate,
               context.SubscriptionId,
               context.ResourceGroup,
               context.WorkspaceName,
               endpointName,
               name);
        }

        public static bool IsMdcEnabled(TrackedResource<OnlineDeployment> onlineDeployment)
        {
            if (onlineDeployment.Properties?.DataCollector?.Collections != null)
            {
                foreach (var col in onlineDeployment.Properties.DataCollector.Collections)
                {
                    if (col.Value.DataCollectionMode == DataCollectionMode.Enabled)
                    {
                        return true;
                    }
                }
            }

            if (onlineDeployment.Tags != null)
            {
                foreach (var item in onlineDeployment.Tags)
                {
                    if (MdcEnabledTagsRegex.IsMatch(item.Key) && "true".Equals(item.Value, StringComparison.OrdinalIgnoreCase))
                    {
                        return true;
                    }
                }
            }

            if (onlineDeployment.Properties?.Properties != null)
            {
                foreach (var item in onlineDeployment.Properties.Properties)
                {
                    if ("IsMDCEnabled".Equals(item.Key, StringComparison.OrdinalIgnoreCase) && "true".Equals(item.Value, StringComparison.OrdinalIgnoreCase))
                    {
                        return true;
                    }
                }
            }

            return false;
        }

        public static bool IsPresetFeedAssetId(string assetId)
        {
            if (assetId == null)
            {
                return false;
            }

            var modelId = assetId;
            if (assetId.Contains('='))
            {
                // Will catch PresetV3 format: modelName=azureml://registries/reg1/models/modelName/versions/1
                var split = assetId.Split("=");

                if (split.Length < 1 || split.Length > 2)
                {
                    throw new ArgumentException($"Invalid preset model ID. Expected: {{modelName}}=azureml://{{insertModelIdAssetPath}}. Actual: {assetId}");
                }
                modelId = split.Length == 1 ? split[0] : split[1];
            }

            return modelId.StartsWith(MfeConstants.FeedModelIdPrefix, StringComparison.OrdinalIgnoreCase); // PresetV1 and PresetV2 format: azureml://registries/reg1/models/modelName/versions/1
        }
    }
}
