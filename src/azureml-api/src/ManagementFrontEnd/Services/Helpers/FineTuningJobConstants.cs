﻿// <copyright file="FineTuningJobConstants.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;

namespace Microsoft.MachineLearning.ManagementFrontEnd.Services
{
    public class FineTuningJobConstants
    {
        public const string LearningRateMultiplier = "learning_rate_multiplier";
        public const string BatchSize = "batch_size";
        public const string NEpochs = "n_epochs";

        public static HashSet<string> DefinedHyperparameters = new HashSet<string>(StringComparer.OrdinalIgnoreCase) { LearningRateMultiplier, BatchSize, NEpochs };
    }
}
