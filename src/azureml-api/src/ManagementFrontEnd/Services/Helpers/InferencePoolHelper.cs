﻿// <copyright file="InferencePoolHelper.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Globalization;
using System.Text.RegularExpressions;
using Microsoft.MachineLearning.Common.Core.Contracts;

namespace Microsoft.MachineLearning.ManagementFrontEnd.Services.Helpers
{
    public static class InferencePoolHelper
    {
        public const string MdcPublicPreviewTagKey = "data_collector.public_preview";
        public const string MdcRollingRateTagKey = "data_collector.rolling_rate";
        public const string MdcCaptureHeadersKey = "data_collector.request_logging.capture_headers";

        public static readonly Regex MdcEnabledTagsRegex = new Regex(
            pattern: @"^data_collector.collections.([^/?]+).enabled$",
            options: RegexOptions.Compiled | RegexOptions.IgnoreCase,
            matchTimeout: TimeSpan.FromSeconds(1));

        private const string InferencePoolARMResourceIdTemplate = "/subscriptions/{0}/resourceGroups/{1}/providers/Microsoft.MachineLearningServices/workspaces/{2}/inferencePools/{3}";
        private const string InferenceGroupARMResourceIdTemplate = "/subscriptions/{0}/resourceGroups/{1}/providers/Microsoft.MachineLearningServices/workspaces/{2}/inferencePools/{3}/groups/{4}";
        private const string InferenceEndpointARMResourceIdTemplate = "/subscriptions/{0}/resourceGroups/{1}/providers/Microsoft.MachineLearningServices/workspaces/{2}/inferencePools/{3}/endpoints/{4}";
        private const string InferenceOperationARMResourceIdTemplate = "/subscriptions/{0}/resourceGroups/{1}/providers/Microsoft.MachineLearningServices/workspaces/{2}/inferenceOperations/{3}";
        private const string InferenceOperationBySubscriptionIdARMResourceIdTemplate = "/subscriptions/{0}/providers/Microsoft.MachineLearningServices/inferenceOperations/{1}";

        public static string GenerateInferencePoolARMResourceId(WorkspaceContext2 context, string poolName)
        {
            return string.Format(
               CultureInfo.InvariantCulture,
               InferencePoolARMResourceIdTemplate,
               context.SubscriptionId,
               context.ResourceGroup,
               context.WorkspaceName,
               poolName);
        }

        public static string GenerateInferenceGroupARMResourceId(WorkspaceContext2 context, string poolName, string groupName)
        {
            return string.Format(
               CultureInfo.InvariantCulture,
               InferenceGroupARMResourceIdTemplate,
               context.SubscriptionId,
               context.ResourceGroup,
               context.WorkspaceName,
               poolName,
               groupName);
        }

        public static string GenerateInferenceEndpointARMResourceId(WorkspaceContext2 context, string poolName, string endpointName)
        {
            return string.Format(
               CultureInfo.InvariantCulture,
               InferenceEndpointARMResourceIdTemplate,
               context.SubscriptionId,
               context.ResourceGroup,
               context.WorkspaceName,
               poolName,
               endpointName);
        }

        public static string GenerateInferenceOperationARMResourceId(WorkspaceContext2 context, string operationId)
        {
            return string.Format(
               CultureInfo.InvariantCulture,
               InferenceOperationARMResourceIdTemplate,
               context.SubscriptionId,
               context.ResourceGroup,
               context.WorkspaceName,
               operationId);
        }

        public static string GenerateInferenceOperationBySubscriptionIdARMResourceId(Guid subscriptionId, string operationId)
        {
            return string.Format(
               CultureInfo.InvariantCulture,
               InferenceOperationBySubscriptionIdARMResourceIdTemplate,
               subscriptionId,
               operationId);
        }
    }
}
