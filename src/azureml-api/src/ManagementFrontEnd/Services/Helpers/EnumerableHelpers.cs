﻿// <copyright file="EnumerableHelpers.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Collections.Generic;
using System.Linq;

namespace Microsoft.MachineLearning.ManagementFrontEnd.Services.Helpers
{
    public static class EnumerableHelpers
    {
        /// <summary>
        /// "Zips" elements from multiple sequences together into arrays of the Nth element from each sequence.
        /// The output sequence is as long as the longest input sequence; shorter sequences have their last element repeated.
        ///
        /// Inputs: { 1, 2, 3 }, { 4, 5, 6, 7, 8 }
        /// Output: { { 1, 4 }, { 2, 5 }, { 3, 6 }, { 3, 7 }, { 3, 8 } }
        /// </summary>
        public static IEnumerable<T[]> ZipLongestRepeatLast<T>(params IEnumerable<T>[] sequences)
        {
            var values = new T[sequences.Length];
            var enumerators = sequences.Select(x => x.GetEnumerator()).ToArray();

            while (true)
            {
                var numFinished = 0;

                for (var i = 0; i < enumerators.Length; i++)
                {
                    if (enumerators[i].MoveNext())
                    {
                        values[i] = enumerators[i].Current;
                    }
                    else
                    {
                        numFinished++;
                    }
                }

                if (numFinished >= enumerators.Length)
                {
                    yield break;
                }

                yield return values.ToArray();
            }
        }
    }
}
