﻿// <copyright file="ObjectPrototyper.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Collections.Immutable;
using System.Linq;
using System.Reflection;
using Microsoft.MachineLearning.ManagementFrontEnd.Services.Extensions;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace Microsoft.MachineLearning.ManagementFrontEnd.Services.Helpers
{
    public class ObjectPrototyper
    {
        private readonly Func<Type, IEnumerable<Type>> _findCompatibleTypes;
        private readonly Func<PropertyInfo, (bool, object, Type[])> _propertyFactory;
        private readonly Func<Type, Type> _resolveType;
        private readonly IDictionary<Type, Func<object>> _typeFactories;
        private readonly object[] _nullValueSequence = new object[] { null };

        /// <summary>
        /// A class for recursively generating instances of objects, via reflection.
        ///
        /// The extension methods taken in the constructor allow using this for different purposes,
        /// like generating randomized instances of contract classes for testing or generating example instances for Swagger.
        /// </summary>
        /// <param name="findCompatibleTypes">Extension point for generating multiple objects using different types (e.g., subclasses).</param>
        /// <param name="propertyFactory">Extension point for overriding behavior on specific properties of AML types.</param>
        /// <param name="typeFactories">Extension point for overriding behavior on specific types (important for non-AML types: int, string, JToken, ...).</param>
        /// <param name="resolveType">Extension point for swapping out one type for another before instantiating it.</param>
        public ObjectPrototyper(
            Func<Type, IEnumerable<Type>> findCompatibleTypes = null,
            Func<PropertyInfo, (bool ShouldOverride, object OverrideValue, Type[] allowedSubtypes)> propertyFactory = null,
            Func<Type, Type> resolveType = null,
            IDictionary<Type, Func<object>> typeFactories = null)
        {
            _findCompatibleTypes = findCompatibleTypes;
            _propertyFactory = propertyFactory;
            _resolveType = resolveType;
            _typeFactories = typeFactories;
        }

        /// <summary>
        /// Generate exactly one object. Will throw if the provided findCompatibleTypes method results in multiple objects being created.
        /// </summary>
        public object CreateObject(Type type)
        {
            return CreateObjects(type, ImmutableHashSet<Type>.Empty, allowedSubtypes: null).Single();
        }

        /// <summary>
        /// Generate a sequence of objects, using the multiple types provided by the findCompatibleTypes method to generate a variety of prototypes.
        /// </summary>
        public IEnumerable<object> CreateObjects(Type type)
        {
            return CreateObjects(type, ImmutableHashSet<Type>.Empty, allowedSubtypes: null);
        }

        private static bool IsAmlDefinedType(Type type)
        {
            return type.Namespace.StartsWith("Microsoft.MachineLearning", StringComparison.Ordinal);
        }

        // Note: The use of IImmutableSet<> instead of ISet<> is important here:
        //       Multiple instances of this method will be running concurrently,
        //       and we need to make sure they don't step on each other's toes.
        private IEnumerable<object> CreateObjects(Type type, IImmutableSet<Type> usedTypes, Type[] allowedSubtypes)
        {
            // Apply overrides.
            if (_resolveType != null)
            {
                type = _resolveType(type) ?? type;
            }

            if (_typeFactories != null && _typeFactories.TryGetValue(type, out var factory))
            {
                return new[] { factory() };
            }

            // Avoid infinite recursion.
            if (usedTypes.Contains(type))
            {
                return new object[] { null };
            }

            usedTypes = usedTypes.Add(type);

            // Create a container object with a value.
            if (type.IsArray)
            {
                return CreateObjects(type.GetElementType(), usedTypes, allowedSubtypes)
                    .Select(obj =>
                    {
                        var array = Array.CreateInstance(type.GetElementType(), 1);
                        array.SetValue(obj, 0);
                        return array;
                    });
            }

            // Create an instance of one of our contracts and fill in each property.
            if (IsAmlDefinedType(type))
            {
                return FindCompatibleTypes(type, allowedSubtypes)
                    .SelectMany(compatibleType =>
                    {
                        // Gather mutually exclusive properties and reformat
                        var mutuallyExclusiveProperties = compatibleType.GetMutuallyExclusiveProperties();
                        var excludedPropertyLists = CreateExcludedPropertyLists(mutuallyExclusiveProperties);

                        // Collect list of objects to return
                        var objects = new List<object>();
                        foreach (var excludedPropertyList in excludedPropertyLists)
                        {
                            // Generate parallel sequences of values to assign to each property. Each enumerable
                            // in the array provides the generated values for one of the properties in the type.
                            //   Property Foo: { 1, 2, 3 }
                            //   Property Bar: { "Hello", "world" }
                            IEnumerable<(PropertyInfo Property, object Value)>[] propertySequences = compatibleType.GetProperties(BindingFlags.Instance | BindingFlags.Public)
                                .Where(x => x.CanWrite && IsAmlDefinedType(x.DeclaringType) && !excludedPropertyList.Contains(x)) // Only fill in properties defined by AML, to avoid messing with inherited properties from system types.
                                .Select(property =>
                                {
                                    var (overridden, overrideValue, allowedSubtypes) = _propertyFactory?.Invoke(property) ?? (false, null, Array.Empty<Type>());
                                    var values = overridden ? new[] { overrideValue } : CreateObjects(property.PropertyType, usedTypes, allowedSubtypes);
                                    if (overridden)
                                    {
                                        // If override is array but target property is not then use value as array directly
                                        // so that it can be used to create keyvalue pairs here=> var propertyValues = values.Select(x => (property, x));
                                        if (overrideValue.GetType().IsArray && !property.PropertyType.IsArray)
                                        {
                                            values = overrideValue as object[];
                                        }
                                    }

                                    var propertyValues = values.Select(x => (property, x));

                                    // If it is nullable type, null should be one of the value.
                                    if (property.PropertyType.IsReferenceOrNullableType())
                                    {
                                        propertyValues = propertyValues.Concat(_nullValueSequence.Select(x => (property, x)));
                                    }

                                    return propertyValues;
                                }).ToArray();

                            // Enums don't have any properties.
                            if (propertySequences.Length == 0)
                            {
                                if (compatibleType.IsEnum)
                                {
                                    // Generate a random enum
                                    var rand = new Random();
                                    var randEnum = rand.Next(Enum.GetNames(compatibleType).Length);
                                    objects.Add(Enum.ToObject(compatibleType, randEnum));
                                }
                                else
                                {
                                    objects.Add(Activator.CreateInstance(compatibleType));
                                }
                                continue;
                            }

                            // Generate objects by taking in one set of property values at a time.
                            // I.e., one value at a time from each sequence.
                            //   { Foo: 1, Bar: "Hello" }
                            //   { Foo: 2, Bar: "world" }
                            //   { Foo: 3, Bar: "world" }
                            objects.AddRange(EnumerableHelpers.ZipLongestRepeatLast(propertySequences)
                                .Select(((PropertyInfo Property, object Value)[] propertyValuesToSet) =>
                                {
                                    var obj = Activator.CreateInstance(compatibleType);
                                    foreach (var (property, value) in propertyValuesToSet)
                                    {
                                        try
                                        {
                                            property?.SetValue(obj, value);
                                        }
                                        catch (TargetInvocationException)
                                        {
                                            // Not writeable -- ignore it.
                                        }
                                    }
                                    return obj;
                                }));
                        }
                        return objects;
                    });
            }

            // Generic container types.
            if (type.IsGenericType)
            {
                var genericType = type.GetGenericTypeDefinition();
                var genericArgs = type.GetGenericArguments();

                if (genericType == typeof(IDictionary<,>) || genericType == typeof(Dictionary<,>))
                {
                    // Generate dictionaries with one key-pair value at a time.
                    var keys = CreateObjects(genericArgs[0], usedTypes, allowedSubtypes);
                    var values = CreateObjects(genericArgs[1], usedTypes, allowedSubtypes);

                    return EnumerableHelpers.ZipLongestRepeatLast(keys, values)
                        .Select(pair =>
                        {
                            var dictionary = (System.Collections.IDictionary)Activator.CreateInstance(typeof(Dictionary<,>).MakeGenericType(genericArgs));
                            dictionary[pair[0]] = pair[1];
                            return dictionary;
                        });
                }

                if (genericType == typeof(IList<>) || genericType == typeof(List<>) || genericType == typeof(IEnumerable<>))
                {
                    // Generate lists with one value at a time.
                    return CreateObjects(genericArgs[0], usedTypes, allowedSubtypes)
                        .Select(obj =>
                        {
                            var list = (System.Collections.IList)Activator.CreateInstance(typeof(List<>).MakeGenericType(genericArgs));
                            list.Add(obj);
                            return list;
                        });
                }

                if (genericType == typeof(Nullable<>))
                {
                    return CreateObjects(genericArgs[0], usedTypes, allowedSubtypes);
                }
            }

            throw new NotImplementedException($"{nameof(ObjectPrototyper)} instance does not have a way to instantiate {type.AssemblyQualifiedName}. Try adding a type factory for it.");
        }

        private IEnumerable<Type> FindCompatibleTypes(Type type, Type[] allowedSubtypes)
        {
            if (allowedSubtypes != null)
            {
                var allowedTypes = allowedSubtypes.Where(x => !x.IsAbstract && x.IsSubclassOf(type));
                return allowedTypes.Any() ? allowedTypes : new[] { type };
            }
            if (_findCompatibleTypes != null)
            {
                // Returning multiple types here allows for prototyping different subclasses.
                return _findCompatibleTypes(type);
            }
            else if (type.IsAbstract)
            {
                // We can't create instances for abstract classes, so pick one of the child classes arbitrarily.
                return new[] { type.Assembly.DefinedTypes.First(x => !x.IsAbstract && x.IsSubclassOf(type)) };
            }
            else
            {
                return new[] { type };
            }
        }

        /// <summary>
        /// Create a lists of properties to exclude from object creation, to avoid mutually exclusive ones.
        /// </summary>
        /// <param name="mutuallyExclusiveProperties">Dictionary returned by GetMutuallyExclusiveProperties</param>
        /// <returns>Lists of properties to exclude</returns>
        /// <remarks>
        /// Turns this dictionary:
        /// <code>
        ///   {{ "group1", { "prop1", "prop2", "prop3" }},
        ///   { "group2", { "prop4", "prop5" }}}
        /// </code>
        /// into this list of lists:
        /// <code>
        ///   [[ "prop2", "prop3", "prop5" ],[ "prop2", "prop3", "prop4" ],[ "prop1", "prop3", "prop5" ],
        ///   [ "prop1", "prop3", "prop4" ],[ "prop1", "prop2", "prop5" ],[ "prop1", "prop2", "prop4" ]]
        /// </code>
        /// </remarks>
        private IEnumerable<IEnumerable<PropertyInfo>> CreateExcludedPropertyLists(Dictionary<string, List<PropertyInfo>> mutuallyExclusiveProperties)
        {
            IEnumerable<IEnumerable<PropertyInfo>> excludedPropertyLists = null;
            foreach (var propertyGroup in mutuallyExclusiveProperties.Values)
            {
                // Create what are effectively Cartesian products
                var toAdd = propertyGroup.Select(outer => propertyGroup.Where(inner => inner != outer));
                excludedPropertyLists = excludedPropertyLists?.SelectMany(x => toAdd.Select(y => x.Concat(y))) ?? toAdd;
            }
            if (excludedPropertyLists == null)
            {
                // Create an empty list to give foreach something to work with
                excludedPropertyLists = new List<List<PropertyInfo>>() { new List<PropertyInfo>() };
            }

            return excludedPropertyLists;
        }
    }
}
