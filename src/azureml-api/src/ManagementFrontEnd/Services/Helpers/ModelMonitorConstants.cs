﻿// <copyright file="ModelMonitorConstants.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Diagnostics.CodeAnalysis;

namespace Microsoft.MachineLearning.ManagementFrontEnd.Services.Helpers
{
    [ExcludeFromCodeCoverage]
    public static class ModelMonitorConstants
    {
        public const string AmlComputeClusterIdPropertyKey = "azureml.monitor.amlcompute.cluster.id";
        public const string TelemetryAttributionKey = "azureml.telemetry.attribution";
        public const string TelemetryAttributionValue = "modelmonitoring";
        public const string MonitoringTargetDeploymentIdKey = "monitoringTargetDeploymentId";
        public const string MonitoringTargetTaskTypeKey = "monitoringTargetTaskType";
        public const string MonitoringTargetModelIdKey = "monitoringTargetModelId";
        public const string ModelPerformanceThresholdsPropertyKey = "azureml.modelmonitor.model_performance_thresholds";
    }
}
