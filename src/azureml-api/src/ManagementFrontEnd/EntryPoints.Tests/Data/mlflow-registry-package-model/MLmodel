artifact_path: model
flavors:
  python_function:
    env: conda.yaml
    loader_module: mlflow.sklearn
    model_path: model.pkl
    python_version: 3.7.11
  sklearn:
    pickled_model: model.pkl
    serialization_format: cloudpickle
    sklearn_version: 0.24.1
run_id: a57c2e43-4dcd-4188-944a-839bcba35331
signature:
  inputs: '[{"name": "sepal_length", "type": "double"}, {"name": "sepal_width", "type":
    "double"}, {"name": "petal_length", "type": "double"}, {"name": "petal_width",
    "type": "double"}]'
  outputs: '[{"type": "tensor", "tensor-spec": {"dtype": "object", "shape": [-1]}}]'
utc_time_created: '2021-09-27 22:56:49.962300'