﻿// <copyright file="ControllerFuzzer.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Common.Core.Extensions;
using FluentAssertions;
using Newtonsoft.Json.Linq;

namespace Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.Fuzzing
{
    /// <summary>
    /// A very simple JSON-based fuzzer.
    /// A fuzzer is a program which injects automatically semi-random data into a program/stack and detect bugs.
    /// </summary>
    public class ControllerFuzzer
    {
        private readonly TestFixtureIsolatedManagementFrontEnd _fixture;
        private readonly Action _configureFakesAction;

        public ControllerFuzzer(TestFixtureIsolatedManagementFrontEnd fixture, Action configureFakesAction)
        {
            _fixture = fixture;
            _configureFakesAction = configureFakesAction;
        }

        public async Task FuzzControllerMethod<TController>(Func<TController, Task> callFunc)
        {
            var request = await CaptureInitialRequest(callFunc).ConfigureAwait(false);
            var body = JToken.Parse(await request.Content.ReadAsStringAsync().ConfigureAwait(false));

            var errors = new List<HttpResponseMessage>();

            foreach (var mutatedBody in FuzzJsonStructure(body))
            {
                var clonedRequest = await request.CloneAsync().ConfigureAwait(false);
                clonedRequest.Content = new StringContent(mutatedBody.ToString(), Encoding.UTF8, "application/json");

                ResetAndConfigureFakes();

                var response = await _fixture.HttpClient.SendAsync(clonedRequest).ConfigureAwait(false);

                // 400s are expected, since most of the requests we're sending will probably be invalid, but any 500 is definitely a bug.
                if (response.StatusCode >= HttpStatusCode.InternalServerError)
                {
                    errors.Add(response);
                }
            }

            if (errors.Count > 0)
            {
                var details = await Task.WhenAll(errors.Select(FuzzingErrorDetails.Extract)).ConfigureAwait(false);
                throw new FuzzingException(details);
            }
        }

        /// <summary>
        /// Generate JSON values to swap in place of <paramref name="token"/>.
        /// </summary>
        private static IEnumerable<JToken> FuzzJsonValue(JToken token)
        {
            yield return token;

            yield return JValue.CreateNull();

            yield return new JValue(string.Empty);
            yield return new JValue(Guid.NewGuid().ToString());

            yield return new JValue(1);
            yield return new JValue(0);
            yield return new JValue(-1);
        }

        /// <summary>
        /// Call MFE normally both to get the starting JSON body and to make sure the original request works.
        /// Having the original request work is important because that increases the amount of coverage
        /// we can get from the very simple kind of fuzzing implemented by this class. If our initial request
        /// gets rejected by a 400 right away, there's no hope of catching bugs further along into the code.
        /// </summary>
        private async Task<HttpRequestMessage> CaptureInitialRequest<TController>(Func<TController, Task> callFunc)
        {
            HttpRequestMessage clonedRequest = null;

            var controller = _fixture.ManagementFrontEndClient.GetController<TController>(beforeRequest: async request =>
            {
                clonedRequest = await request.CloneAsync().ConfigureAwait(false);
            });

            ResetAndConfigureFakes();

            await callFunc(controller).ConfigureAwait(false);

            clonedRequest.Should().NotBeNull("the fuzzing code needs a functional request to start with");

            return clonedRequest;
        }

        /// <summary>
        /// Generate a series of JSON payloads based on <paramref name="token"/>.
        ///
        /// Note: The input is modified in place, although everything is undone by the end of the sequence.
        /// </summary>
        private IEnumerable<JToken> FuzzJsonStructure(JToken token)
        {
            // Fuzz a single node in the tree.
            foreach (var mutatedToken in FuzzJsonValue(token))
            {
                yield return mutatedToken;
            }

            // Recurse.
            switch (token)
            {
                case JArray array:
                    for (var i = 0; i < array.Count; i++)
                    {
                        var child = array[i];

                        foreach (var mutatedChild in FuzzJsonStructure(child))
                        {
                            array[i] = mutatedChild;
                            yield return array;
                        }

                        array[i] = child;
                    }
                    break;

                case JObject obj:
                    foreach (var (key, value) in obj)
                    {
                        foreach (var mutatedValue in FuzzJsonStructure(value))
                        {
                            obj[key] = mutatedValue;
                            yield return obj;
                        }

                        obj[key] = value;
                    }
                    break;
            }
        }

        private void ResetAndConfigureFakes()
        {
            _fixture.ResetFakeS2SClients();
            _configureFakesAction();
        }
    }
}
