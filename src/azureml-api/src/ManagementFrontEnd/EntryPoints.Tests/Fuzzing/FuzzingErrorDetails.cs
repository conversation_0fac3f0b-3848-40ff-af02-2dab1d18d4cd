﻿// <copyright file="FuzzingErrorDetails.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using Common.Core.Contracts;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.Fuzzing
{
    public class FuzzingErrorDetails
    {
        public ErrorResponse ErrorResponse
        {
            get
            {
                try
                {
                    return ResponseJson?.ToObject<ErrorResponse>();
                }
                catch (JsonSerializationException)
                {
                    return null;
                }
            }
        }

        public string ReasonPhrase { get; set; }

        public string RequestBody { get; set; }

        public JToken RequestJson => ParseJsonOrNull(RequestBody);

        public string ResponseBody { get; set; }

        public JToken ResponseJson => ParseJsonOrNull(ResponseBody);

        public HttpStatusCode StatusCode { get; set; }

        public static async Task<FuzzingErrorDetails> Extract(HttpResponseMessage response)
        {
            return new FuzzingErrorDetails
            {
                ReasonPhrase = response.ReasonPhrase,
                RequestBody = await response.RequestMessage.Content.ReadAsStringAsync().ConfigureAwait(false),
                ResponseBody = await response.Content.ReadAsStringAsync().ConfigureAwait(false),
                StatusCode = response.StatusCode,
            };
        }

        private static JToken ParseJsonOrNull(string str)
        {
            try
            {
                return JToken.Parse(str);
            }
            catch (JsonSerializationException)
            {
                return null;
            }
        }
    }
}
