﻿// <copyright file="FuzzingException.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Globalization;
using System.Text;
using Common.Core.Contracts;
using Newtonsoft.Json;

namespace Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.Fuzzing
{
    public class FuzzingException : Exception
    {
        public FuzzingException()
        {
        }

        public FuzzingException(string message)
            : base(message)
        {
        }

        public FuzzingException(string message, Exception innerException)
            : base(message, innerException)
        {
        }

        public FuzzingException(IList<FuzzingErrorDetails> errors)
            : base(GenerateMessage(errors))
        {
            Errors = errors;
        }

        public IList<FuzzingErrorDetails> Errors { get; } = new List<FuzzingErrorDetails>();

        private static string GenerateMessage(IList<FuzzingErrorDetails> errors)
        {
            var message = new StringBuilder();

            message.AppendLine(CultureInfo.InvariantCulture, $"Controller fuzzing produced {errors.Count} error(s).");

            foreach (var details in errors)
            {
                message.AppendLine(" - Request:");
                message.AppendLine(CultureInfo.InvariantCulture, $"     {details.RequestJson?.ToString(Formatting.None) ?? details.RequestBody}");
                message.AppendLine(CultureInfo.InvariantCulture, $"   Response: {(int)details.StatusCode} {details.ReasonPhrase}");
                message.AppendLine(CultureInfo.InvariantCulture, $"     {details.ResponseJson?.ToString(Formatting.None) ?? details.ResponseBody}");

                if (details.ErrorResponse?.Error?.DebugInfo is DebugInfoResponse debugInfo)
                {
                    message.AppendLine("   Debug info:");
                    message.AppendLine(CultureInfo.InvariantCulture, $"     {debugInfo.Type}: {debugInfo.Message}");
                    message.AppendLine(CultureInfo.InvariantCulture, $"     {debugInfo.StackTrace?.Replace(Environment.NewLine, $"{Environment.NewLine}     ", StringComparison.Ordinal)}");
                }
            }

            return message.ToString();
        }
    }
}
