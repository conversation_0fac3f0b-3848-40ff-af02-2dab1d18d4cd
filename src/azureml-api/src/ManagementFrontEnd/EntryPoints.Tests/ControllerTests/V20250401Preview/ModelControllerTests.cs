﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using FluentAssertions;
using Microsoft.Extensions.Options;
using Microsoft.MachineLearning.Common.Core;
using Microsoft.MachineLearning.Common.Testing;
using Microsoft.MachineLearning.Common.WebApi.Client;
using Microsoft.MachineLearning.DataStore.Contracts;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20250401Preview.ArmCommon;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20250401Preview.Assets;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20250401Preview.Controllers;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Api;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.ControllerTests.NonVersionedHelpers;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.Extensions;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.XunitExtensions;
using Xunit;
using Microsoft.MachineLearning.Common.WebApi.TeamAccounts;
using System.Globalization;

namespace Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.ControllerTests.V20250401Preview
{
    [MfeTest(TestCategory.Integration, ScenarioScope.Models)]
    public class E2EModelControllerTests : ModelControllerTests<TestFixtureManagementFrontEnd>, IStaticFixture<TestFixtureManagementFrontEnd>, IAsyncLifetime
    {
        public E2EModelControllerTests(StaticFixture<TestFixtureManagementFrontEnd> fixture)
            : base(fixture, ServiceInvoker.Create<IDataStoreController>(fixture.Instance.HttpClient, new Uri(fixture.Instance.GetHostService<IOptions<ExperimentationHostConfiguration>>().Value.ExperimentationHostUri)))
        {
        }

        public async Task DisposeAsync()
        {
            await CleanUpModels().ConfigureAwait(false);
        }

        public Task InitializeAsync()
        {
            return Task.CompletedTask;
        }
    }

    public abstract class ModelControllerTests<TFixture>
        where TFixture : TestFixtureManagementFrontEnd
    {
        private const int _maxListCount = 20;

        private readonly IDataStoreController _datastoreController;

        private const string _modelTestContainerNamePrefix = "test_mfe";

        public ModelControllerTests(TFixture fixture, IDataStoreController datastoreController)
        {
            _datastoreController = datastoreController;

            Fixture = fixture;

            ContainerController = Fixture.ManagementFrontEndClient.GetController<IModelContainerController>();
            VersionController = Fixture.ManagementFrontEndClient.GetController<IModelVersionController>();
        }

        protected TFixture Fixture { get; }

        protected IModelContainerController ContainerController { get; }

        protected IModelVersionController VersionController { get; }

        [Fact]
        public async void TestModelVersionCreateDelete()
        {
            // Setup.
            var containerName = GenerateContainerName(DateTime.UtcNow);
            var modelVersions = new[] { 40, 15 };

            // Test each operation.
            var createdModels = await TestCreateVersions(containerName, modelVersions).ConfigureAwait(false);
            await TestGetVersionContainer(containerName).ConfigureAwait(false);
            await TestGetLatestVersion(containerName, createdModels.Last()).ConfigureAwait(false);
            await TestGetVersions(containerName, modelVersions, createdModels).ConfigureAwait(false);
            await TestListVersions(containerName, modelVersions, createdModels).ConfigureAwait(false);
            await TestDeleteVersions(containerName, modelVersions).ConfigureAwait(false);
        }

        protected static string GenerateContainerName(DateTime timestamp)
        {
            return $"{_modelTestContainerNamePrefix}_{timestamp:yyyyMMddHHmmss}_{Guid.NewGuid().ToString()[..8]}";
        }

        protected async Task<List<Resource<ModelContainer>>> ListContainers()
        {
            return await ControllerHelpers.List(skipToken => ContainerController.List(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, skipToken), _maxListCount).ToListAsync();
        }

        protected async Task<List<Resource<ModelVersion>>> ListVersions(string name, string orderBy = null, int? top = null, string version = null, string description = null, int? offset = null, string tags = null, string properties = null)
        {
            return await ControllerHelpers.List(skipToken => VersionController.List(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, name, skipToken, orderBy, top, version, description, offset, tags, properties)).ToListAsync();
        }

        protected static Resource<ModelContainer> GenerateContainer()
        {
            return new Resource<ModelContainer>
            {
                Properties = new ModelContainer
                {
                    Description = "description",
                    Tags = new Dictionary<string, string>
                    {
                        { "api-version", "2025-04-01Preview" }
                    }
                },
            };
        }

        protected async Task CleanUpModels()
        {
            var containers = await ContainerController.List(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, count: 200).ConfigureAwait(false);
            var containersToDelete = containers.Value
                .Where(c => c.Name.StartsWith(_modelTestContainerNamePrefix, StringComparison.InvariantCulture) &&
                      (c.Properties.Tags.IsNullOrEmpty() || !c.Properties.Tags.ContainsKey(MfeTestingConstants.ModelDoNotDeleteTagKey)) &&
                      ((TimeSpan)(DateTime.UtcNow - c.SystemData?.CreatedAt)).TotalMinutes > 20)
                .Select(c => c.Name);
            foreach (var containerName in containersToDelete)
            {
                try
                {
                    await ContainerController.Delete(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, containerName).ConfigureAwait(false);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error while cleaning up models. Message: {ex.Message}. Exception: {ex.StackTrace}");
                }
            }
        }

        protected async Task<Resource<ModelVersion>> GenerateVersion()
        {
            // Make sure we have a blob to use as the model artifact.
            var datastore = await _datastoreController.Get(Fixture.SubscriptionId.ToString(), Fixture.ResourceGroupName, Fixture.WorkspaceName, "workspaceblobstore").ConfigureAwait(false);
            var storageConnectionDto = await Fixture.GetStorageConnection().ConfigureAwait(false);
            await BlobHelpers.UploadBlob(storageConnectionDto.ConnectionString, datastore.AzureStorageSection.ContainerName, MfeTestingConstants.ModelVersionBlobPath, "some text").ConfigureAwait(false);
            var path = MfeTestingConstants.ModelVersionBlobPath[..MfeTestingConstants.ModelVersionBlobPath.LastIndexOf('/')];
            await JobHelpers.CreateJob(Fixture, "TestExperiment", "TestJob");
            return new Resource<ModelVersion>
            {
                Properties = new ModelVersion
                {
                    ModelUri = $"azureml://subscriptions/{Fixture.SubscriptionId}/resourceGroups/{Fixture.ResourceGroupName}/workspaces/{Fixture.WorkspaceName}/datastores/workspaceblobstore/paths/{path}",
                    Description = "description",
                    Properties = new Dictionary<string, string> { { "property-name-1", "property-value-1" } },
                    Tags = new Dictionary<string, string> { { "api-version", "2025-04-01Preview" }, { "tag-name-1", "tag-value-1" } },
                    Flavors = new Dictionary<string, FlavorData>() { { "python_function", new FlavorData { Data = new Dictionary<string, string>() { { "loader_module", "test" } } } } },
                    JobName = "TestJob",
                    Datasets = new List<DatasetReference> {
                        new DatasetReference {
                            Id = $"/subscriptions/{Fixture.SubscriptionId}/resourceGroups/{Fixture.ResourceGroupName}/workspaces/{Fixture.WorkspaceName}/data/{Fixture.TestMfeData}/versions/1",
                            Name = "mfe_test_data"
                        }
                    }
                },
            };
        }

        private async Task<IList<Resource<ModelVersion>>> TestCreateVersions(string name, int[] versions, Resource<ModelVersion> request = null)
        {
            // Create the models.
            request ??= await GenerateVersion().ConfigureAwait(false);

            var createdModels = new List<Resource<ModelVersion>>();

            foreach (var version in versions)
            {
                var createdModel = await VersionController.CreateOrUpdate(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, name, version.ToString(CultureInfo.InvariantCulture), request).ConfigureAwait(false);

                createdModels.Add(createdModel);
            }

            return createdModels;
        }

        private async Task TestGetVersionContainer(string name)
        {
            var container = await ContainerController.Get(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, name).ConfigureAwait(false);
            container.Name.Should().Be(name);
        }

        private async Task TestGetVersions(string name, int[] versions, IList<Resource<ModelVersion>> createdModels)
        {
            foreach (var (version, createdModel) in versions.Zip(createdModels))
            {
                var retrievedModel = await VersionController.Get(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, name, version.ToString(CultureInfo.InvariantCulture)).ConfigureAwait(false);
                retrievedModel.Should().BeEquivalentTo(createdModel, options => options.UsingCaseInsensitiveProperties());
            }
        }

        private async Task TestListVersions(string name, int[] versions, IList<Resource<ModelVersion>> createdModels)
        {
            var allVersions = await ListVersions(name).ConfigureAwait(false);

            versions.Length.Should().Be(createdModels.Count);
            foreach (var (version, createdModel) in versions.Zip(createdModels))
            {
                allVersions.First(x => x.Id.EndsWith($"/versions/{version}", StringComparison.InvariantCulture))
                    .Should().BeEquivalentTo(createdModel, options => options.UsingCaseInsensitiveProperties());
            }
        }

        private async Task TestGetLatestVersion(string name, Resource<ModelVersion> expectedModel)
        {
            var retrievedModel = await VersionController.List(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, name: name, orderBy: "createdTime desc", top: 1).ConfigureAwait(false);
            retrievedModel.Value.Count.Should().Be(1);
            retrievedModel.Value.First().Should().BeEquivalentTo(expectedModel, options => options.UsingCaseInsensitiveProperties());
        }

        private async Task TestDeleteVersions(string name, int[] versions)
        {
            foreach (var version in versions)
            {
                var response = await VersionController.Delete(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, name, version.ToString(CultureInfo.InvariantCulture)).ConfigureAwait(false);
                response.StatusCode.Should().Be(HttpStatusCode.OK);

                Func<Task> retrievalTask = () => VersionController.Get(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, name, version.ToString(CultureInfo.InvariantCulture));
                (await retrievalTask.Should().ThrowAsync<ServiceInvocationException>()).Which.Response.StatusCode.Should().Be(HttpStatusCode.NotFound);

                var noContentResponse = await VersionController.Delete(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, name, version.ToString(CultureInfo.InvariantCulture)).ConfigureAwait(false);
                noContentResponse.StatusCode.Should().Be(HttpStatusCode.NoContent);
            }
        }
    }
}
