﻿// <copyright file="FeaturesetContainerControllerTests.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Common.Core.Contracts;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.MachineLearning.Common.Core.Contracts;
using Microsoft.MachineLearning.Common.Testing;
using Microsoft.MachineLearning.Common.WebApi.TeamAccounts;
using Microsoft.MachineLearning.FeatureStore.Contracts.Mfe;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Api;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Api.V20230201Preview.Controllers.Workspaces;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.XunitExtensions;
using Microsoft.MachineLearning.ManagementFrontEnd.Services;
using Microsoft.MachineLearning.ManagementFrontEnd.Services.Configuration;
using Microsoft.MachineLearning.ManagementFrontEnd.Services.Services;
using Moq;
using Xunit;
using FeaturesetContainer = Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20230201Preview.Featurestore.FeaturesetContainer;
using FeatureSetContainer = Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts.FeatureStore.FeaturesetContainer;
using FeaturesetContainerResource = Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20230201Preview.ArmCommon.Resource<Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20230201Preview.Featurestore.FeaturesetContainer>;
using FeatureSetDtoResource = Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts.Resource<Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts.FeatureStore.FeaturesetContainer>;

namespace Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.ControllerTests.V20230201Preview
{
    [Trait(nameof(TestCategory), TestCategory.Unit)]
    [MfeTest(TestCategory.Unit, ScenarioScope.Featuresets)]
    public class FeaturesetContainerControllerTests : IDisposable
    {
        private const string _featureSetName = "myFeatureSet";

        private readonly FeaturesetContainerController _featureSetController;
        private readonly WorkspaceContext2 _workspaceContext;

        public FeaturesetContainerControllerTests()
        {
            Mock<ILoggerFactory> loggerFactory = new Mock<ILoggerFactory>();
            Mock<ILogger> logger = new Mock<ILogger>();
            loggerFactory.Setup(m => m.CreateLogger(It.IsAny<string>())).Returns(logger.Object);

            var hostConfig = new Mock<IOptionsMonitor<ExperimentationHostConfiguration>>();
            hostConfig.Setup(m => m.CurrentValue).Returns(new ExperimentationHostConfiguration());

            var featureStoreConfig = new Mock<IOptionsMonitor<FeaturestoreConfiguration>>();
            featureStoreConfig.Setup(m => m.CurrentValue).Returns(new FeaturestoreConfiguration());

            var mockDto = new FeatureSetDtoResource()
            {
                Name = "Name",
                Properties = new FeatureSetContainer()
                {
                }
            };

            var mockDtoLro = LongRunningOperationResponse.Create<FeatureSetDtoResource>(mockDto);

            var mockDtoList = new PaginatedResult<FeatureSetDtoResource>()
            {
                Value = new List<FeatureSetDtoResource>()
                {
                    mockDto
                }
            };

            var controller = new Mock<IFeatureSetController>();
            controller.Setup(x => x.CreateOrUpdateAsync(
                It.IsAny<Guid>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<FeatureSetDtoResource>())).ReturnsAsync(mockDtoLro);
            controller.Setup(x => x.ShowAsync(
                It.IsAny<Guid>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>())).ReturnsAsync(mockDto);
            controller.Setup(x => x.ListAsync(
                It.IsAny<Guid>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<ListViewType>(),
                It.IsAny<string>(),
                It.IsAny<int>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>())).ReturnsAsync(mockDtoList);
            controller.Setup(x => x.DeleteAsync(
               It.IsAny<Guid>(),
               It.IsAny<string>(),
               It.IsAny<string>(),
               It.IsAny<string>(),
               It.IsAny<bool>())).ReturnsAsync(LongRunningOperationResponse.Create<bool>(true));

            Mock<IS2SClientFactory> s2sClientFactory = new Mock<IS2SClientFactory>();
            s2sClientFactory.Setup(x => x.CreateS2SClient<IFeatureSetController>(
                It.IsAny<string>(),
                It.IsAny<TimeSpan>(),
                false)).Returns(controller.Object);

            IFeaturesetContainerService featureSetService = new FeaturesetContainerService(
                                loggerFactory.Object,
                                s2sClientFactory.Object,
                                hostConfig.Object,
                                featureStoreConfig.Object);

            Mock<IArmContextAccessor> armContextAccessor = new Mock<IArmContextAccessor>();
            armContextAccessor.Setup(x => x.GetResourceUri()).Returns(new Uri("http://localhost"));

            var mapperProfile = new Api.V20230201Preview.MapperProfile();
            var mapperConfig = new MapperConfiguration(config =>
            {
                config.AddProfile(mapperProfile);
            });
            mapperConfig.AssertConfigurationIsValid();
            mapperConfig.CompileMappings();

            var mapper = new Mapper(mapperConfig);

            _featureSetController = new FeaturesetContainerController(
                armContextAccessor.Object,
                mapper,
                featureSetService);

            _workspaceContext = new WorkspaceContext2(
                Guid.NewGuid(),
                "ws",
                "rg",
                Guid.NewGuid(),
                "eastus",
                "tenantid",
                null);
        }

        public void Dispose()
        {
            this.Dispose(true);
            GC.SuppressFinalize(this);
        }

        [Fact]
        public async Task CreateOrUpdateAsyncTest()
        {
            var request = new FeaturesetContainerResource()
            {
                Name = "Name",
                Properties = new FeaturesetContainer()
                {
                }
            };

            var actionResult = await _featureSetController.CreateOrUpdate(
                _workspaceContext,
                _featureSetName,
                request,
                null).ConfigureAwait(false);

            var createdResult = actionResult as CreatedResult;
            var featureSetDto = createdResult.Value as FeaturesetContainerResource;
            Assert.Equal("Name", featureSetDto.Name);
        }

        [Fact]
        public async Task DeleteAsyncTestSuccess()
        {
            var actionResult = await _featureSetController.Delete(
                _workspaceContext,
                _featureSetName,
                null).ConfigureAwait(false);
            var okResult = actionResult as OkObjectResult;
        }

        [Fact]
        public async Task ListTest()
        {
            var actionResult = await _featureSetController.List(
                _workspaceContext,
                null,
                null,
                null,
                Contracts.V20230201Preview.Assets.ListViewType.ActiveOnly).ConfigureAwait(false);

            var okResult = actionResult as OkObjectResult;
            var featureSetDto = okResult.Value as ArmPaginatedResult<FeaturesetContainerResource>;
            Assert.Single(featureSetDto.Value);
            Assert.Equal("Name", featureSetDto.Value.First().Name);
        }

        [Fact]
        public async Task GetEntityTest()
        {
            var actionResult = await _featureSetController.GetEntity(
                _workspaceContext,
                _featureSetName,
                null).ConfigureAwait(false);

            var okResult = actionResult as OkObjectResult;
            var featureSetDto = okResult.Value as FeaturesetContainerResource;
            Assert.Equal("Name", featureSetDto.Name);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (disposing)
            {
                _featureSetController.Dispose();
            }
        }
    }
}