﻿// <copyright file="DataContainerRegistryControllerTests.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using FluentAssertions;
using Microsoft.MachineLearning.Common.Core;
using Microsoft.MachineLearning.Common.Core.Contracts;
using Microsoft.MachineLearning.Common.Testing;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20230201Preview.ArmCommon;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20230201Preview.Assets;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20230201Preview.Controllers;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Api;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.ControllerTests.NonVersionedHelpers;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.XunitExtensions;
using Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts.Common;
using Xunit;
using DataAssetHelpers = Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.ControllerTests.V20230201Preview.Helpers.DataAssetHelpers;

namespace Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.ControllerTests.V20230201Preview;

[MfeTest(TestCategory.Integration, ScenarioScope.Data)]
public class E2EDataContainerRegistryControllerTests : DataContainerRegistryControllerTests<TestFixtureManagementFrontEnd>, IStaticFixture<TestFixtureManagementFrontEnd>
{
    public E2EDataContainerRegistryControllerTests(StaticFixture<TestFixtureManagementFrontEnd> fixture)
        : base(fixture)
    {
    }
}

public abstract class DataContainerRegistryControllerTests<TFixture>
    where TFixture : TestFixtureManagementFrontEnd
{
    private const int _maxListCount = 500;
    private readonly DataAssetHelpers _dataAssetHelpers;
    private readonly V20220501.Helpers.DataAssetHelpers _dataAssetWorkspaceHelpers;

    public DataContainerRegistryControllerTests(TFixture fixture)
    {
        Fixture = fixture;
        _dataAssetWorkspaceHelpers = new V20220501.Helpers.DataAssetHelpers(new WorkspaceContext2(Fixture.WorkspaceId, Fixture.WorkspaceName, Fixture.ResourceGroupName, Fixture.SubscriptionId, "test", "test", new Dictionary<string, string>()), null);
        DataContainerRegistryController = Fixture.ManagementFrontEndClient.GetController<IDataContainerRegistryController>(randomizeCase: false);
        DataContainerController = Fixture.ManagementFrontEndClient.GetController<Contracts.V20220501.Controllers.IDataContainerController>();

        var registryContext = new RegistryContext(Fixture.RegistryName, Fixture.ResourceGroupName, Fixture.SubscriptionId, null, Fixture.TenantId.ToString(), immutableResourceId: Fixture.RegistryId);
        _dataAssetHelpers = new DataAssetHelpers(registryContext, temporaryDataReferenceController: null);
    }

    public IDataContainerRegistryController DataContainerRegistryController { get; }

    public Contracts.V20220501.Controllers.IDataContainerController DataContainerController { get; }

    public TFixture Fixture { get; }

    [Fact]
    public async Task TestRegistryCreateGetUpdateListDelete()
    {
        // create
        var name = $"MfeRegistryDataContainer_{Guid.NewGuid()}";
        var request = _dataAssetHelpers.MakeDataContainerRequest(name);
        var lro = await DataContainerRegistryController.CreateOrUpdate(
            subscriptionId: Fixture.SubscriptionId,
            resourceGroupName: Fixture.ResourceGroupName,
            registryName: Fixture.RegistryName,
            name: name,
            request: request);
        await PollHelper.PollAsyncOperationUntilComplete(lro).ConfigureAwait(false);

        // update
        request.Properties.Description = "updated description";
        request.Properties.Tags = new Dictionary<string, string>() { { "newtag", "verydifferent" } };
        var lro_updated = await DataContainerRegistryController.CreateOrUpdate(
            Fixture.SubscriptionId,
            Fixture.ResourceGroupName,
            Fixture.RegistryName,
            name,
            request).ConfigureAwait(false);
        await PollHelper.PollAsyncOperationUntilComplete(lro_updated).ConfigureAwait(false);

        // get
        var dataContainer = await GetDataContainer(name).ConfigureAwait(false);
        _dataAssetHelpers.CheckCreatedDataContainer(dataContainer, request);

        // Need to look at datatype validation again. There are several dataContainers
        // with invalid dataype of "3" which is causing list to fail.
        // TODO: [MFE] Investigate DataType Validation: https://msdata.visualstudio.com/Vienna/_workitems/edit/2137653
        await EventualConsistencyTesting.RetryUntilAssertionPasses(async () =>
        {
            var dataContainers = await ListDataContainer(null).ConfigureAwait(false);
            dataContainers.Should().HaveCountGreaterThan(0);
        }).ConfigureAwait(false);

        // delete
        var ex = await Assert.ThrowsAsync<ServiceInvocationException>(() => DataContainerRegistryController.Delete(
                Fixture.SubscriptionId,
                Fixture.ResourceGroupName,
                Fixture.RegistryName,
                name)).ConfigureAwait(false);
        Assert.Contains("DataContainers_Registry_Delete", ex.Message, StringComparison.InvariantCulture);
    }

    [Fact]
    public async Task TestRegistryWorkspaceCreateContainer()
    {
        // create container in workspace
        string dataContainerName = Guid.NewGuid().ToString();
        var creationDataContainer = _dataAssetWorkspaceHelpers.MakeDataContainerRequest(dataContainerName, Contracts.V20220501.Assets.DataType.uri_file);

        var workspaceDataContainer = await DataContainerController.CreateOrUpdate(
                                                            Fixture.SubscriptionId,
                                                            Fixture.ResourceGroupName,
                                                            Fixture.WorkspaceName,
                                                            creationDataContainer.Name,
                                                            creationDataContainer);

        // create container in registry
        await _dataAssetHelpers.RegisterDataContainer(dataContainerName, DataContainerRegistryController).ConfigureAwait(false);
        var registryDataContainer = await GetDataContainer(dataContainerName).ConfigureAwait(false);

        // assert
        workspaceDataContainer.Name.Should().BeEquivalentTo(registryDataContainer.Name);
        workspaceDataContainer.Properties.Should().BeEquivalentTo(
                                    registryDataContainer.Properties,
                                    assertionOptions => assertionOptions.Excluding(dataContainer => dataContainer.NextVersion));
        workspaceDataContainer.Type.Should().Be(MfeConstants.ArmFullTypeDataContainer);
        registryDataContainer.Type.Should().Be(Feed.Contracts.AssetType.Data);

        // delete
        var deleteTask = async () => await DataContainerController.Delete(
                                                            Fixture.SubscriptionId,
                                                            Fixture.ResourceGroupName,
                                                            Fixture.WorkspaceName,
                                                            workspaceDataContainer.Name);
        await deleteTask.Should().ThrowAsync<ServiceInvocationException>()
            .Where(e => e.Message.Contains("DataContainers_Workspace_Delete is not supported", StringComparison.InvariantCultureIgnoreCase))
            .ConfigureAwait(false);
    }

    [Fact]
    public async Task TestGetContainerNotExists()
    {
        var name = $"MfeRegistryDataContainer_{Guid.NewGuid()}";
        Func<Task> getContainer = () => GetDataContainer(name);
        await getContainer.Should().ThrowAsync<ServiceInvocationException>().Where(ex => ex.Response.StatusCode == HttpStatusCode.NotFound);
    }

    [Fact]
    public async Task TestGetWorkspaceContainerWithRegistryController()
    {
        // create container in workspace
        var name = Guid.NewGuid().ToString();
        var creationDataContainer = _dataAssetWorkspaceHelpers.MakeDataContainerRequest(name, Contracts.V20220501.Assets.DataType.uri_file);

        var dataContainerCreated = await DataContainerController.CreateOrUpdate(
                                                            Fixture.SubscriptionId,
                                                            Fixture.ResourceGroupName,
                                                            Fixture.WorkspaceName,
                                                            creationDataContainer.Name,
                                                            creationDataContainer);

        // get workspace container with registry controller, should throw
        Func<Task> getWorkspaceContainer = () => DataContainerRegistryController.Get(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, name);
        await getWorkspaceContainer.Should().ThrowAsync<ServiceInvocationException>();
    }

    [Fact]
    public async Task TestGetRegistryContainerWithWorkspaceController()
    {
        // create container in registry
        var name = $"MfeRegistryDataContainer_{Guid.NewGuid()}";
        await _dataAssetHelpers.RegisterDataContainer(name, DataContainerRegistryController).ConfigureAwait(false);

        // get registry container with workspace controller, should throw
        Func<Task> getRegistryContainer = () => DataContainerController.Get(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.RegistryName, name);
        await getRegistryContainer.Should().ThrowAsync<ServiceInvocationException>().Where(ex => ex.Response.StatusCode == HttpStatusCode.NotFound);
    }

    protected async Task<Resource<DataContainer>> GetDataContainer(string containerName)
    {
        return await DataContainerRegistryController.Get(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.RegistryName, containerName);
    }

    protected async Task<List<Resource<DataContainer>>> ListDataContainer(string continuationToken = null)
    {
        return await ControllerHelpers.List(skipToken => DataContainerRegistryController.List(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.RegistryName, continuationToken), _maxListCount).ToListAsync();
    }
}
