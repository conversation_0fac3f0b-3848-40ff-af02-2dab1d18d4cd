// <copyright file="OnlineDeploymentValidatorTests.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

namespace Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.ControllerTests.V20230201Preview;

using System;
using System.Collections.Generic;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.Errors;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20230201Preview.ArmCommon;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20230201Preview.Endpoints.OnlineEndpointDeployment;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20230201Preview.Endpoints.OnlineEndpointDeployment.DeploymentConfigurations;
using Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts.Extensions;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Api.V20230201Preview.Validation;
using Xunit;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.XunitExtensions;
using Microsoft.MachineLearning.Common.Testing;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Api;

[MfeTest(TestCategory.Unit, ScenarioScope.OnlineServices)]
public class OnlineDeploymentValidatorTests
{
    public static IEnumerable<object[]> ValidationData => new List<object[]>
    {
        new object[]
        {
            GetRequest(cpuRequest: "1", cpuLimit: "1", memoryRequest: "1Gi", memoryLimit: "1Gi", gpuRequest: "1", gpuLimit: "1"),
            null,
        },
        new object[]
        {
            GetRequest(cpuRequest: "2b", cpuLimit: "1", memoryRequest: "1Gi", memoryLimit: "1Gi", gpuRequest: "1", gpuLimit: "1"),
            new ContainerResourceRequirementInvalid().ToException("requests.cpu", "2b"),
        },
        new object[]
        {
            GetRequest(cpuRequest: "1", cpuLimit: "1", memoryRequest: string.Empty, memoryLimit: "1Gi", gpuRequest: "1", gpuLimit: "1"),
            new ContainerResourceRequirementInvalid().ToException("requests.memory", string.Empty),
        },
        new object[]
        {
            GetRequest(cpuRequest: "1", cpuLimit: "1", memoryRequest: "1Gi", memoryLimit: "1Gi", gpuRequest: "1", gpuLimit: null),
            new ContainerResourceGPURequirementInvalid().ToException(),
        },
        new object[]
        {
            GetRequest(cpuRequest: "1", cpuLimit: "1", memoryRequest: "1Gi", memoryLimit: "1Gi", gpuRequest: "1", gpuLimit: "2"),
            new ContainerResourceGPURequirementInvalid().ToException(),
        },
        new object[]
        {
            GetRequest(cpuRequest: "1", cpuLimit: "1", memoryRequest: "1Gi", memoryLimit: "1Gi", gpuRequest: "0", gpuLimit: "0"),
            null,
        },
        new object[]
        {
            GetRequest(cpuRequest: "1", cpuLimit: "1", memoryRequest: "1Gi", memoryLimit: "1Gi", gpuRequest: "0", gpuLimit: null),
            null,
        },
        new object[]
        {
            GetRequest(cpuRequest: "1", cpuLimit: "1", memoryRequest: "1Gi", memoryLimit: "1Gi", gpuRequest: null, gpuLimit: null),
            null,
        },
        new object[]
        {
            GetRequest(cpuRequest: "1", cpuLimit: "1", memoryRequest: "1Gi", memoryLimit: "1Gi", gpuRequest: null, gpuLimit: "0"),
            null,
        },
    };

    [Theory]
    [MemberData(nameof(ValidationData))]
    public void Validate(TrackedResource<OnlineDeployment> onlineDeploymentRequest, Exception expected)
    {
        var exception = Record.Exception(() => OnlineDeploymentValidator.Validate(onlineDeploymentRequest));
        if (expected == null)
        {
            Assert.Null(exception);
        }
        else
        {
            Assert.True(exception.GetType() == expected.GetType() && string.Equals(exception?.Message, expected?.Message, StringComparison.InvariantCulture));
        }
    }

    private static TrackedResource<OnlineDeployment> GetRequest(
        string cpuRequest,
        string cpuLimit,
        string memoryRequest,
        string memoryLimit,
        string gpuRequest,
        string gpuLimit)
    {
        var containerResourceRequirements = new ContainerResourceRequirements()
        {
            ContainerResourceRequests = new ContainerResourceSettings()
            {
                Cpu = cpuRequest,
                Memory = memoryRequest,
                Gpu = gpuRequest
            },
            ContainerResourceLimits = new ContainerResourceSettings()
            {
                Cpu = cpuLimit,
                Memory = memoryLimit,
                Gpu = gpuLimit
            }
        };
        return new TrackedResource<OnlineDeployment>
        {
            Properties = new KubernetesOnlineDeployment
            {
                ContainerResourceRequirements = containerResourceRequirements
            }
        };
    }
}