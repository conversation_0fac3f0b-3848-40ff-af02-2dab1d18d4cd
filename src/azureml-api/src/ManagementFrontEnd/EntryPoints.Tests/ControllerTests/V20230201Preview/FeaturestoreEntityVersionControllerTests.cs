﻿// <copyright file="FeaturestoreEntityVersionControllerTests.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Common.Core.Contracts;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.MachineLearning.Common.Core.Contracts;
using Microsoft.MachineLearning.Common.Testing;
using Microsoft.MachineLearning.Common.WebApi.TeamAccounts;
using Microsoft.MachineLearning.FeatureStore.Contracts.Mfe;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Api;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Api.V20230201Preview.Controllers.Workspaces;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.XunitExtensions;
using Microsoft.MachineLearning.ManagementFrontEnd.Services;
using Microsoft.MachineLearning.ManagementFrontEnd.Services.Configuration;
using Microsoft.MachineLearning.ManagementFrontEnd.Services.Services;
using Moq;
using Xunit;
using FeatureEntityVersionDtoResource = Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts.Resource<Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts.FeatureStore.FeaturestoreEntityVersion>;
using FeaturestoreEntityVersion = Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20230201Preview.Featurestore.FeaturestoreEntityVersion;
using FeatureStoreEntityVersion = Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts.FeatureStore.FeaturestoreEntityVersion;
using FeaturestoreEntityVersionResource = Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20230201Preview.ArmCommon.Resource<Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20230201Preview.Featurestore.FeaturestoreEntityVersion>;

namespace Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.ControllerTests.V20230201Preview
{
    [Trait(nameof(TestCategory), TestCategory.Unit)]
    [MfeTest(TestCategory.Unit, ScenarioScope.FeaturestoreEntities)]
    public class FeaturestoreEntityVersionControllerTests : IDisposable
    {
        private const string _featureEntityName = "myFeatureEntity";
        private const string _featureEntityVersion = "1";

        private readonly FeaturestoreEntityVersionController _featurestoreEntityVersionController;
        private readonly WorkspaceContext2 _workspaceContext;

        public FeaturestoreEntityVersionControllerTests()
        {
            Mock<ILoggerFactory> loggerFactory = new Mock<ILoggerFactory>();
            Mock<ILogger> logger = new Mock<ILogger>();
            loggerFactory.Setup(m => m.CreateLogger(It.IsAny<string>())).Returns(logger.Object);

            var hostConfig = new Mock<IOptionsMonitor<ExperimentationHostConfiguration>>();
            hostConfig.Setup(m => m.CurrentValue).Returns(new ExperimentationHostConfiguration());

            var featureStoreConfig = new Mock<IOptionsMonitor<FeaturestoreConfiguration>>();
            featureStoreConfig.Setup(m => m.CurrentValue).Returns(new FeaturestoreConfiguration());

            var mockDto = new FeatureEntityVersionDtoResource()
            {
                Name = "Name",
                Properties = new FeatureStoreEntityVersion()
                {
                    Description = "Description",
                }
            };

            var mockDtoLro = LongRunningOperationResponse.Create<FeatureEntityVersionDtoResource>(mockDto);

            var mockDtoList = new PaginatedResult<FeatureEntityVersionDtoResource>()
            {
                Value = new List<FeatureEntityVersionDtoResource>()
                {
                    mockDto
                }
            };

            var controller = new Mock<IFeatureEntityVersionController>();
            controller.Setup(x => x.CreateOrUpdateAsync(
                It.IsAny<Guid>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<FeatureEntityVersionDtoResource>())).ReturnsAsync(mockDtoLro);
            controller.Setup(x => x.ShowAsync(
                It.IsAny<Guid>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>())).ReturnsAsync(mockDto);
            controller.Setup(x => x.ListAsync(
                It.IsAny<Guid>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<ListViewType>(),
                It.IsAny<string>(),
                It.IsAny<int>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>())).ReturnsAsync(mockDtoList);
            controller.Setup(x => x.DeleteAsync(
               It.IsAny<Guid>(),
               It.IsAny<string>(),
               It.IsAny<string>(),
               It.IsAny<string>(),
               It.IsAny<string>())).ReturnsAsync(LongRunningOperationResponse.Create<bool>(true));

            Mock<IS2SClientFactory> s2sClientFactory = new Mock<IS2SClientFactory>();
            s2sClientFactory.Setup(x => x.CreateS2SClient<IFeatureEntityVersionController>(
                It.IsAny<string>(),
                It.IsAny<TimeSpan>(),
                false)).Returns(controller.Object);

            IFeaturestoreEntityVersionService featureStoreEntityVersionService = new FeaturestoreEntityVersionService(
                                loggerFactory.Object,
                                s2sClientFactory.Object,
                                hostConfig.Object,
                                featureStoreConfig.Object);

            Mock<IArmContextAccessor> armContextAccessor = new Mock<IArmContextAccessor>();
            armContextAccessor.Setup(x => x.GetResourceUri()).Returns(new Uri("http://localhost"));

            var mapperProfile = new Api.V20230201Preview.MapperProfile();
            var mapperConfig = new MapperConfiguration(config =>
            {
                config.AddProfile(mapperProfile);
            });
            mapperConfig.AssertConfigurationIsValid();
            mapperConfig.CompileMappings();

            var mapper = new Mapper(mapperConfig);

            _featurestoreEntityVersionController = new FeaturestoreEntityVersionController(
                armContextAccessor.Object,
                mapper,
                featureStoreEntityVersionService);

            _workspaceContext = new WorkspaceContext2(
                Guid.NewGuid(),
                "ws",
                "rg",
                Guid.NewGuid(),
                "eastus",
                "tenantid",
                null);
        }

        public void Dispose()
        {
            this.Dispose(true);
            GC.SuppressFinalize(this);
        }

        [Fact]
        public async Task CreateOrUpdateAsyncTest()
        {
            var request = new FeaturestoreEntityVersionResource()
            {
                Name = "Name",
                Properties = new FeaturestoreEntityVersion()
                {
                    Description = "Description"
                }
            };

            var actionResult = await _featurestoreEntityVersionController.CreateOrUpdate(
                _workspaceContext,
                _featureEntityName,
                _featureEntityVersion,
                request,
                null).ConfigureAwait(false);

            var createdResult = actionResult as CreatedResult;
            var featureEntityDto = createdResult.Value as FeaturestoreEntityVersionResource;
            Assert.Equal("Name", featureEntityDto.Name);
            Assert.Equal("Description", featureEntityDto.Properties.Description);
        }

        [Fact]
        public async Task DeleteAsyncTestSuccess()
        {
            var actionResult = await _featurestoreEntityVersionController.Delete(
                _workspaceContext,
                _featureEntityName,
                _featureEntityVersion,
                null).ConfigureAwait(false);
            var okResult = actionResult as OkObjectResult;
        }

        [Fact]
        public async Task ListTest()
        {
            var actionResult = await _featurestoreEntityVersionController.List(
                _workspaceContext,
                _featureEntityName,
                null,
                null,
                null,
                Contracts.V20230201Preview.Assets.ListViewType.ActiveOnly).ConfigureAwait(false);

            var okResult = actionResult as OkObjectResult;
            var featureEntityDto = okResult.Value as ArmPaginatedResult<FeaturestoreEntityVersionResource>;
            Assert.Single(featureEntityDto.Value);
            Assert.Equal("Name", featureEntityDto.Value.First().Name);
            Assert.Equal("Description", featureEntityDto.Value.First().Properties.Description);
        }

        [Fact]
        public async Task GetEntityTest()
        {
            var actionResult = await _featurestoreEntityVersionController.GetEntity(
                _workspaceContext,
                _featureEntityName,
                _featureEntityVersion,
                null).ConfigureAwait(false);

            var okResult = actionResult as OkObjectResult;
            var featureEntityDto = okResult.Value as FeaturestoreEntityVersionResource;
            Assert.Equal("Name", featureEntityDto.Name);
            Assert.Equal("Description", featureEntityDto.Properties.Description);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (disposing)
            {
                _featurestoreEntityVersionController.Dispose();
            }
        }
    }
}