﻿// <copyright file="DataVersionRegistryControllerTests.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using FluentAssertions;
using Microsoft.MachineLearning.Common.Core;
using Microsoft.MachineLearning.Common.Core.Contracts;
using Microsoft.MachineLearning.Common.Testing;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20230201Preview.ArmCommon;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20230201Preview.Assets;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20230201Preview.Controllers;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Api;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.ControllerTests.NonVersionedHelpers;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.ControllerTests.V20230201Preview.Helpers;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.XunitExtensions;
using Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts.Common;
using Microsoft.MachineLearning.ManagementFrontEnd.Services.Extensions;
using Microsoft.MachineLearning.ManagementFrontEnd.Services.JsonConverters;
using Newtonsoft.Json;
using Xunit;

using ListViewType = Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20230201Preview.Assets.ListViewType;

namespace Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.ControllerTests.V20230201Preview;

[MfeTest(TestCategory.Integration, ScenarioScope.Data)]
public class E2EDataVersionRegistryControllerTests : DataVersionRegistryControllerTests<TestFixtureManagementFrontEnd>, IStaticFixture<TestFixtureManagementFrontEnd>
{
    public E2EDataVersionRegistryControllerTests(StaticFixture<TestFixtureManagementFrontEnd> fixture)
        : base(fixture)
    {
    }
}

public abstract class DataVersionRegistryControllerTests<TFixture>
    where TFixture : TestFixtureManagementFrontEnd
{
    private const int _maxListCount = 500;
    private readonly DataAssetHelpers _dataAssetHelpers;
    private readonly V20220501.Helpers.DataAssetHelpers _dataAssetWorkspaceHelpers;

    public DataVersionRegistryControllerTests(TFixture fixture)
    {
        Fixture = fixture;
        _dataAssetWorkspaceHelpers = new V20220501.Helpers.DataAssetHelpers(new WorkspaceContext2(Fixture.WorkspaceId, Fixture.WorkspaceName, Fixture.ResourceGroupName, Fixture.SubscriptionId, "test", "test", new Dictionary<string, string>()), null);
        DataContainerRegistryController = Fixture.ManagementFrontEndClient.GetController<IDataContainerRegistryController>(randomizeCase: false);
        DataVersionRegistryController = Fixture.ManagementFrontEndClient.GetController<IDataVersionRegistryController>(randomizeCase: false);
        DataContainerController = Fixture.ManagementFrontEndClient.GetController<Contracts.V20220501.Controllers.IDataContainerController>();
        DataVersionController = Fixture.ManagementFrontEndClient.GetController<Contracts.V20220501.Controllers.IDataVersionController>();

        var serializerSettings = new JsonSerializerSettings().AddFeedConverters();
        var temporaryDataReferenceController = Fixture.ManagementFrontEndClient.GetController<Contracts.V20211001Dataplane.Controllers.ITemporaryDataReferenceController>(randomizeCase: false);
        var registryContext = new RegistryContext(Fixture.RegistryName, Fixture.ResourceGroupName, Fixture.SubscriptionId, "test", Fixture.TenantId.ToString(), immutableResourceId: Fixture.RegistryId);
        _dataAssetHelpers = new DataAssetHelpers(registryContext, temporaryDataReferenceController);
    }

    public IDataContainerRegistryController DataContainerRegistryController { get; }

    public IDataVersionRegistryController DataVersionRegistryController { get; }

    public Contracts.V20220501.Controllers.IDataContainerController DataContainerController { get; }

    public Contracts.V20220501.Controllers.IDataVersionController DataVersionController { get; }

    public TFixture Fixture { get; }

    [Fact]
    public async Task TestRegistryCreateUpdateGetListDelete()
    {
        var name = $"MfeRegistryDataVersion_{Guid.NewGuid()}";
        var version = "156";

        // create
        var createdDataVersion = await _dataAssetHelpers.RegisterDataContainerAndVersion(dataVersionRegistryController: DataVersionRegistryController, dataContainerName: name, dataVersion: version, dataContainerRegistryController: DataContainerRegistryController).ConfigureAwait(false);

        // update
        createdDataVersion.Properties.Description = "a new description";
        var lro_updated = await DataVersionRegistryController.CreateOrUpdate(
            subscriptionId: Fixture.SubscriptionId,
            resourceGroupName: Fixture.ResourceGroupName,
            registryName: Fixture.RegistryName,
            name: name,
            version: version,
            datasetVersion: createdDataVersion).ConfigureAwait(false);
        await PollHelper.PollAsyncOperationUntilComplete(lro_updated).ConfigureAwait(false);

        // list
        await EventualConsistencyTesting.RetryUntilAssertionPasses(async () =>
        {
            var datasetList = await ListDataVersions(name).ConfigureAwait(false);
            datasetList.Count.Should().BeGreaterThan(0);
            _dataAssetHelpers.CheckCreatedDataVersion(datasetList[0], createdDataVersion);
        }).ConfigureAwait(false);

        // delete
        var ex = await Assert.ThrowsAsync<ServiceInvocationException>(() => DataVersionRegistryController.Delete(
            subscriptionId: Fixture.SubscriptionId,
            resourceGroupName: Fixture.ResourceGroupName,
            registryName: Fixture.RegistryName,
            name: name,
            version: version)).ConfigureAwait(false);
        Assert.Contains("DataVersions_Registry_Delete is not supported", ex.Message, StringComparison.InvariantCulture);
    }

    [Fact]
    public async Task TestRegistryWorkspaceCreateVersion()
    {
        var datasetName = $"MfeRegistryDataVersion_{Guid.NewGuid()}";
        var datasetVersion = "592";

        // create version in workspace
        var workspaceDataVersion = await _dataAssetWorkspaceHelpers.RegisterDataVersion(dataVersionController: DataVersionController, datasetName: datasetName, datasetVersion: datasetVersion, dataContainerController: DataContainerController).ConfigureAwait(false);

        // create version in registry
        await _dataAssetHelpers.RegisterDataContainerAndVersion(dataVersionRegistryController: DataVersionRegistryController, dataContainerName: datasetName, dataVersion: datasetVersion, dataContainerRegistryController: DataContainerRegistryController).ConfigureAwait(false);
        var registryDataVersion = await DataVersionRegistryController.Get(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.RegistryName, datasetName, datasetVersion).ConfigureAwait(false);

        // assert
        workspaceDataVersion.Id.Should().NotBeNull();
        registryDataVersion.Id.Should().NotBeNull();
        workspaceDataVersion.Name.Should().BeEquivalentTo(registryDataVersion.Name);
        workspaceDataVersion.Properties.Should().BeEquivalentTo(
                                                    registryDataVersion.Properties,
                                                    options => options.Excluding(prop => prop.DataUri));
        workspaceDataVersion.Type.Should().Be(MfeConstants.ArmFullTypeDataVersion);
        registryDataVersion.Type.Should().Be(Feed.Contracts.AssetType.Data);
    }

    [Fact]
    public async Task TestGetVersionNotExists()
    {
        var name = $"MfeRegistryDataVersion_{Guid.NewGuid()}";
        var version = "333";
        Func<Task> getVersion = () => DataVersionRegistryController.Get(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.RegistryName, name, version);
        await getVersion.Should().ThrowAsync<ServiceInvocationException>().Where(ex => ex.Response.StatusCode == HttpStatusCode.NotFound);
    }

    [Fact]
    public async Task TestCreateVersionWithoutContainer()
    {
        var name = $"MfeRegistryDataVersion_{Guid.NewGuid()}";
        var version = "444";
        var ex = await Assert.ThrowsAsync<ServiceInvocationException>(() => DataVersionRegistryController.CreateOrUpdate(
            subscriptionId: Fixture.SubscriptionId,
            resourceGroupName: Fixture.ResourceGroupName,
            registryName: Fixture.RegistryName,
            name: name,
            version: version,
            datasetVersion: new Resource<DataVersionBase>() { Properties = new UriFileDataVersion { DataUri = "https://myblobstore.blob.core.windows.net/abc/file.txt" } })).ConfigureAwait(false);
    }

    [Fact]
    public async Task TestCreateVersionInWorkspaceWithRegistryController()
    {
        // create workspace container
        var name = $"MfeRegistryDataVersion_{Guid.NewGuid()}";
        var version = "555";
        var createWorkspaceContainer = _dataAssetWorkspaceHelpers.MakeDataContainerRequest(name, Contracts.V20220501.Assets.DataType.uri_file);
        var workspaceDataContainer = await DataContainerController.CreateOrUpdate(
            Fixture.SubscriptionId,
            Fixture.ResourceGroupName,
            Fixture.WorkspaceName,
            name,
            createWorkspaceContainer).ConfigureAwait(false);

        // create version in workspace with registry controller, should throw
        Func<Task> createVersionInWorkspace = () => DataVersionRegistryController.CreateOrUpdate(
            subscriptionId: Fixture.SubscriptionId,
            resourceGroupName: Fixture.ResourceGroupName,
            registryName: Fixture.WorkspaceName,
            name: name,
            version: version,
            datasetVersion: new Resource<DataVersionBase>() { Properties = new UriFileDataVersion { DataUri = "https://myblobstore.blob.core.windows.net/abc/file.txt" } });
        await createVersionInWorkspace.Should().ThrowAsync<ServiceInvocationException>();
    }

    [Fact]
    public async Task TestCreateIdenticalVersions()
    {
        var name = $"MfeRegistryDataVersion_{Guid.NewGuid()}";
        var version = "123";

        // create DataVersion
        var createdDataVersion = await _dataAssetHelpers.RegisterDataContainerAndVersion(dataVersionRegistryController: DataVersionRegistryController, dataContainerName: name, dataVersion: version, dataContainerRegistryController: DataContainerRegistryController).ConfigureAwait(false);

        // create identical DataVersion, should throw
        Func<Task> identicalDataVersion = () => DataVersionRegistryController.CreateOrUpdate(
            subscriptionId: Fixture.SubscriptionId,
            resourceGroupName: Fixture.ResourceGroupName,
            registryName: Fixture.RegistryName,
            name: name,
            version: version,
            datasetVersion: createdDataVersion);
        await identicalDataVersion.Should().ThrowAsync<MfeResponseValidationException>().Where(ex => ex.Response.ReasonPhrase.Contains("Error updating. Data version already exists in registry.", StringComparison.OrdinalIgnoreCase)).ConfigureAwait(false);
    }

    [Fact]
    public async Task TestDataTypeMismatch()
    {
        // create DataContainer with MLTable DataType
        var dataContainerName = $"MfeRegistryDataContainer_{Guid.NewGuid()}";
        await _dataAssetHelpers.RegisterDataContainer(
            dataContainerName: dataContainerName,
            dataContainerRegistryController: DataContainerRegistryController,
            dataType: DataType.mltable).ConfigureAwait(false);

        // create DataVersion with UriFile DataType
        var version = "392";
        var dataVersion = await _dataAssetHelpers.CreateDataVersion(dataContainerName, version).ConfigureAwait(false);
        var dataVersionResource = _dataAssetHelpers.ConvertToArmResourceDto(dataVersion, version);

        Func<Task> createdDataVersion = () => DataVersionRegistryController.CreateOrUpdate(
            subscriptionId: Fixture.SubscriptionId,
            resourceGroupName: Fixture.ResourceGroupName,
            registryName: Fixture.RegistryName,
            name: dataContainerName,
            version: version,
            datasetVersion: dataVersionResource);
        await createdDataVersion.Should().ThrowAsync<ServiceInvocationException>().Where(ex => ex.Response.ReasonPhrase.Contains("The provided type UriFile for this version is incorrect,", StringComparison.OrdinalIgnoreCase)).ConfigureAwait(false);
    }

    [Fact]
    public async Task TestArchiveMismatch()
    {
        // create DataContainer with isArchive = true
        var dataContainerName = $"MfeRegistryDataContainer_{Guid.NewGuid()}";
        await _dataAssetHelpers.RegisterDataContainer(
            dataContainerName: dataContainerName,
            dataContainerRegistryController: DataContainerRegistryController,
            isArchived: true).ConfigureAwait(false);

        // create DataVersion with isArchive = false
        var version = "643";
        var dataVersion = await _dataAssetHelpers.CreateDataVersion(dataContainerName, version, isArchived: false).ConfigureAwait(false);
        var dataVersionResource = _dataAssetHelpers.ConvertToArmResourceDto(dataVersion, version);

        Func<Task> archivedDataVersion = () => DataVersionRegistryController.CreateOrUpdate(
            subscriptionId: Fixture.SubscriptionId,
            resourceGroupName: Fixture.ResourceGroupName,
            registryName: Fixture.RegistryName,
            name: dataContainerName,
            version: version,
            datasetVersion: dataVersionResource);
        await archivedDataVersion.Should().ThrowAsync<ServiceInvocationException>().Where(ex => ex.Response.ReasonPhrase.Contains("Can't create an active version in an archived container.", StringComparison.OrdinalIgnoreCase)).ConfigureAwait(false);

        // create DataVersion with isArchive = true
        dataVersion = await _dataAssetHelpers.CreateDataVersion(dataContainerName, version, isArchived: true).ConfigureAwait(false);
        var dataVersionResource2 = _dataAssetHelpers.ConvertToArmResourceDto(dataVersion, version);

        var response = await DataVersionRegistryController.CreateOrUpdate(
            subscriptionId: Fixture.SubscriptionId,
            resourceGroupName: Fixture.ResourceGroupName,
            registryName: Fixture.RegistryName,
            name: dataContainerName,
            version: version,
            datasetVersion: dataVersionResource2).ConfigureAwait(false);
        await PollHelper.PollAsyncOperationUntilComplete(response).ConfigureAwait(false);

        var getDataVersion = await DataVersionRegistryController.Get(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.RegistryName, dataContainerName, version).ConfigureAwait(false);
        _dataAssetHelpers.CheckCreatedDataVersion(getDataVersion, dataVersionResource2);
    }

    [Fact]
    public async Task TestListScenarios()
    {
        // create
        var dataContainerName = $"MfeRegistryDataContainer_{Guid.NewGuid()}";
        var version1 = "1";
        var version2 = "2";
        var version3 = "3";
        var createdDataVersionResource = await _dataAssetHelpers.RegisterDataContainerAndVersion(dataVersionRegistryController: DataVersionRegistryController, dataContainerName: dataContainerName, dataVersion: version1, dataContainerRegistryController: DataContainerRegistryController).ConfigureAwait(false);

        await _dataAssetHelpers.RegisterDataVersion(dataVersionRegistryController: DataVersionRegistryController, dataContainerName: dataContainerName, dataVersion: version2).ConfigureAwait(false);
        await _dataAssetHelpers.RegisterDataVersion(dataVersionRegistryController: DataVersionRegistryController, dataContainerName: dataContainerName, dataVersion: version3).ConfigureAwait(false);

        // orderBy = createdTime, asc
        await EventualConsistencyTesting.RetryUntilAssertionPasses(async () =>
        {
            var datasetList = await ListDataVersions(dataContainerName, orderBy: "createdtime asc").ConfigureAwait(false);
            datasetList.Count.Should().Be(3);
            datasetList[0].Name.Should().Be("1");
            datasetList[1].Name.Should().Be("2");
            datasetList[2].Name.Should().Be("3");
        }).ConfigureAwait(false);

        // orderBy = createdTime, desc
        await EventualConsistencyTesting.RetryUntilAssertionPasses(async () =>
        {
            var datasetList = await ListDataVersions(dataContainerName, orderBy: "createdtime desc").ConfigureAwait(false);
            datasetList.Count.Should().Be(3);
            datasetList[0].Name.Should().Be("3");
            datasetList[1].Name.Should().Be("2");
            datasetList[2].Name.Should().Be("1");
        }).ConfigureAwait(false);

        // orderBy = createdTime, asc, top = 1
        await EventualConsistencyTesting.RetryUntilAssertionPasses(async () =>
        {
            var datasetList = await ListDataVersions(dataContainerName, orderBy: "createdtime asc", top: 1).ConfigureAwait(false);
            datasetList.Count.Should().Be(1);
            datasetList[0].Name.Should().Be("1");
        }).ConfigureAwait(false);

        var version4 = "4";
        await _dataAssetHelpers.RegisterDataVersion(dataVersionRegistryController: DataVersionRegistryController, dataContainerName: dataContainerName, dataVersion: version4, isArchived: true).ConfigureAwait(false);

        // listType = ArchivedOnly
        await EventualConsistencyTesting.RetryUntilAssertionPasses(async () =>
        {
            var datasetList = await ListDataVersions(dataContainerName, listViewType: ListViewType.ArchivedOnly).ConfigureAwait(false);
            datasetList.Count.Should().Be(1);
            datasetList[0].Name.Should().Be("4");
        }).ConfigureAwait(false);

        // filter by tags
        await EventualConsistencyTesting.RetryUntilAssertionPasses(async () =>
        {
            var datasetList = await ListDataVersions(dataContainerName, tags: "mytag1").ConfigureAwait(false);
            datasetList.Count.Should().Be(3);
        }).ConfigureAwait(false);

        // TODO: Filter by modifiedTime when version update in registries is supported.
    }

    [Fact]
    public async Task TestNextVersionLatestVersion()
    {
        // create version "124"
        var dataContainerName = $"MfeRegistryDataContainer_{Guid.NewGuid()}";
        var createdDataVersionResource = await _dataAssetHelpers.RegisterDataContainerAndVersion(dataVersionRegistryController: DataVersionRegistryController, dataContainerName: dataContainerName, dataVersion: "123", dataContainerRegistryController: DataContainerRegistryController).ConfigureAwait(false);
        var createdDataContainer = await GetDataContainer(dataContainerName).ConfigureAwait(false);
        createdDataContainer.Properties.NextVersion.Should().Be("124");
        createdDataContainer.Properties.LatestVersion.Should().Be("123");

        // create version "1"
        await _dataAssetHelpers.RegisterDataVersion(DataVersionRegistryController, dataContainerName, "1").ConfigureAwait(false);

        var updatedDataContainer = await GetDataContainer(dataContainerName).ConfigureAwait(false);
        updatedDataContainer.Properties.NextVersion.Should().Be("124");
        updatedDataContainer.Properties.LatestVersion.Should().Be("1");

        // create version "124.4"
        await _dataAssetHelpers.RegisterDataVersion(DataVersionRegistryController, dataContainerName, "124.4").ConfigureAwait(false);

        var updatedDataContainer2 = await GetDataContainer(dataContainerName).ConfigureAwait(false);
        updatedDataContainer2.Properties.NextVersion.Should().Be("124");
        updatedDataContainer2.Properties.LatestVersion.Should().Be("124.4");

        // create version "foo"
        await _dataAssetHelpers.RegisterDataVersion(DataVersionRegistryController, dataContainerName, "foo").ConfigureAwait(false);

        var updatedDataContainer3 = await GetDataContainer(dataContainerName).ConfigureAwait(false);
        updatedDataContainer3.Properties.NextVersion.Should().Be("124");
        updatedDataContainer3.Properties.LatestVersion.Should().Be("foo");
    }

    [Fact]
    public async Task TestInvalidDataUri()
    {
        // create DataContainer
        var dataContainerName = $"MfeRegistryDataContainer_{Guid.NewGuid()}";
        await _dataAssetHelpers.RegisterDataContainer(
            dataContainerName: dataContainerName,
            dataContainerRegistryController: DataContainerRegistryController).ConfigureAwait(false);

        // create DataVersion with invalid type
        var version = "643";
        var dataVersion = await _dataAssetHelpers.CreateDataVersion(dataContainerName, version).ConfigureAwait(false);
        dataVersion.DataUri = "something invalid";
        var dataVersionResource = _dataAssetHelpers.ConvertToArmResourceDto(dataVersion, version);

        Func<Task> invalidDataVersion = () => DataVersionRegistryController.CreateOrUpdate(
            subscriptionId: Fixture.SubscriptionId,
            resourceGroupName: Fixture.ResourceGroupName,
            registryName: Fixture.RegistryName,
            name: dataContainerName,
            version: version,
            datasetVersion: dataVersionResource);
        await invalidDataVersion.Should().ThrowAsync<ServiceInvocationException>().Where(ex => ex.Response.ReasonPhrase.Contains("Invalid URI: The format of the URI could not be determined.", StringComparison.OrdinalIgnoreCase)).ConfigureAwait(false);
    }

    // TODO: Uncomment this test when container update in registries is supported.
    /*[Fact]
    public async Task TestArchiveDataContainerAndAllVersions()
    {
        // create DataContainer with isArchive = false
        var dataContainerName = $"MfeRegistryDataContainer_{Guid.NewGuid()}";
        await _dataAssetHelpers.RegisterDataContainer(
            dataContainerName: dataContainerName,
            dataContainerRegistryController: DataContainerRegistryController,
            isArchived: false).ConfigureAwait(false);

        // create DataVersions with isArchive = false
        var version1 = "452";
        await _dataAssetHelpers.RegisterDataVersion(
            dataVersionRegistryController: DataVersionRegistryController,
            datasetName: dataContainerName,
            datasetVersion: version1,
            isArchived: false,
            dataContainerRegistryController: DataContainerRegistryController).ConfigureAwait(false);

        var version2 = "453";
        await _dataAssetHelpers.RegisterDataVersion(
            dataVersionRegistryController: DataVersionRegistryController,
            datasetName: dataContainerName,
            datasetVersion: version2,
            isArchived: false,
            dataContainerRegistryController: DataContainerRegistryController).ConfigureAwait(false);

        // archive container
        await _dataAssetHelpers.RegisterDataContainer(
            dataContainerName: dataContainerName,
            dataContainerRegistryController: DataContainerRegistryController,
            isArchived: true).ConfigureAwait(false);

        // check all DataVersions are archived
        var getVersion1 = await DataVersionRegistryController.Get(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.RegistryName, dataContainerName, version1).ConfigureAwait(false);
        var getVersion2 = await DataVersionRegistryController.Get(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.RegistryName, dataContainerName, version2).ConfigureAwait(false);
        getVersion1.Properties.IsArchived.Should().BeTrue();
        getVersion2.Properties.IsArchived.Should().BeTrue();
    }

    [Fact]
    public async Task TestCreateListManyVersions()
    {
        var name = $"MfeRegistryDataVersion_{Guid.NewGuid()}";
        var version = "163";
        var version2 = "164";

        // create two versions
        await _dataAssetHelpers.RegisterDataVersion(dataVersionRegistryController: DataVersionRegistryController, datasetName: name, datasetVersion: version, dataContainerRegistryController: DataContainerRegistryController).ConfigureAwait(false);
        await _dataAssetHelpers.RegisterDataVersion(dataVersionRegistryController: DataVersionRegistryController, datasetName: name, datasetVersion: version2, dataContainerRegistryController: DataContainerRegistryController).ConfigureAwait(false);

        var dataVersion1 = await DataVersionRegistryController.Get(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.RegistryName, name, version).ConfigureAwait(false);
        var dataVersion2 = await DataVersionRegistryController.Get(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.RegistryName, name, version2).ConfigureAwait(false);

        // assert
        _dataAssetHelpers.CheckCreatedDataVersion(dataVersion1, dataVersion2);

        // list versions
        await EventualConsistencyTesting.RetryUntilAssertionPasses(async () =>
        {
            var datasetList = await ListDataVersions(name).ConfigureAwait(false);
            datasetList.Count.Should().Be(2);
            _dataAssetHelpers.CheckCreatedDataVersion(datasetList[0], datasetList[1]);
        }).ConfigureAwait(false);
    }*/

    protected async Task<List<Resource<DataVersionBase>>> ListDataVersions(string name, string tags = null, string orderBy = null, int? top = null, ListViewType listViewType = ListViewType.ActiveOnly)
    {
        return await ControllerHelpers.List(skipToken => DataVersionRegistryController.List(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.RegistryName, name, orderBy, top, skipToken.EscapeDataStringOrNull(), tags, listViewType), _maxListCount).ToListAsync();
    }

    protected async Task<Resource<DataContainer>> GetDataContainer(string containerName)
    {
        return await DataContainerRegistryController.Get(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.RegistryName, containerName);
    }
}
