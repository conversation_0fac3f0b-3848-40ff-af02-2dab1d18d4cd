﻿// <copyright file="Collections.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using Xunit;

namespace Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.ControllerTests
{
    /// <summary>
    /// For tests that should run with an isolated MFE setup, where calls to other Vienna services are intercepted and faked.
    /// </summary>
    [CollectionDefinition(nameof(ControllerIsolatedTestsCollection))]
    public class ControllerIsolatedTestsCollection : ICollectionFixture<TestFixtureIsolatedManagementFrontEnd>
    {
    }
}
