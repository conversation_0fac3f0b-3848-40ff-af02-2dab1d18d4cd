﻿// <copyright file="ModelControllerTests.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Options;
using Microsoft.MachineLearning.Common.Core;
using Microsoft.MachineLearning.Common.Testing;
using Microsoft.MachineLearning.Common.WebApi.Client;
using Microsoft.MachineLearning.Common.WebApi.TeamAccounts;
using Microsoft.MachineLearning.Dataset.Contracts;
using Microsoft.MachineLearning.DataStore.Contracts;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20221001.ArmCommon;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20221001.Assets;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20221001.Controllers;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Api;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Api.V20221001;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.ControllerTests.NonVersionedHelpers;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.XunitExtensions;
using Xunit;

namespace Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.ControllerTests.V20221001
{
    [MfeTest(TestCategory.Integration, ScenarioScope.Models)]
    public class E2EModelControllerTests : IStaticFixture<TestFixtureManagementFrontEnd>, IAsyncLifetime
    {
        private const string _modelTestContainerNamePrefix = "test_mfe";

        private readonly IModelVersionController _modelVersionController;

        private readonly IModelContainerController _modelContainerController;

        private readonly IDataStoreController _datastoreController;

        private readonly IDatasetsController _datasetsController;

        public E2EModelControllerTests(StaticFixture<TestFixtureManagementFrontEnd> fixture)
        {
            Fixture = fixture;
            _modelVersionController = Fixture.ManagementFrontEndClient.GetController<IModelVersionController>();
            _modelContainerController = Fixture.ManagementFrontEndClient.GetController<IModelContainerController>();
            _datastoreController = ServiceInvoker.Create<IDataStoreController>(
                fixture.Instance.HttpClient,
                new Uri(fixture.Instance.GetHostService<IOptions<ExperimentationHostConfiguration>>().Value.ExperimentationHostUri));
            _datasetsController = ServiceInvoker.Create<IDatasetsController>(
                fixture.Instance.HttpClient,
                new Uri(fixture.Instance.GetHostService<IOptions<ExperimentationHostConfiguration>>().Value.DatasetHostUri));
        }

        public TestFixtureManagementFrontEnd Fixture { get; }

        public static async Task<Resource<ModelVersion>> GenerateVersion(IDataStoreController datastoreController, TestFixtureManagementFrontEnd fixture, ModelType modelType = ModelType.custom_model)
        {
            // Make sure we have a blob to use as the model artifact.
            var datastore = await datastoreController.Get(fixture.SubscriptionId.ToString(), fixture.ResourceGroupName, fixture.WorkspaceName, "workspaceblobstore").ConfigureAwait(false);
            var storageConnectionDto = await fixture.GetStorageConnection().ConfigureAwait(false);
            await JobHelpers.CreateJob(fixture, "TestExperiment", "TestJob");
            var path = string.Empty;
            switch (modelType)
            {
                case ModelType.mlflow_model:
                    await BlobHelpers.UploadBlobFromLocalFilepath(storageConnectionDto.ConnectionString, datastore.AzureStorageSection.ContainerName, MfeTestingConstants.ModelVersionMlflowBlobPath, "Data/mlflowModel").ConfigureAwait(false);
                    path = MfeTestingConstants.ModelVersionMlflowBlobPath;
                    break;
                default:
                    await BlobHelpers.UploadBlob(storageConnectionDto.ConnectionString, datastore.AzureStorageSection.ContainerName, MfeTestingConstants.ModelVersionBlobPath, "some text").ConfigureAwait(false);
                    path = MfeTestingConstants.ModelVersionBlobPath[..MfeTestingConstants.ModelVersionBlobPath.LastIndexOf('/')];
                    break;
            }

            return new Resource<ModelVersion>
            {
                Properties = new ModelVersion
                {
                    Description = "description",
                    Flavors = new Dictionary<string, FlavorData>() { { "python_function", new FlavorData { Data = new Dictionary<string, string>() { { "loader_module", "test" } } } } },
                    ModelType = modelType.ToString(),
                    ModelUri = $"azureml://subscriptions/{fixture.SubscriptionId}/resourceGroups/{fixture.ResourceGroupName}/workspaces/{fixture.WorkspaceName}/datastores/workspaceblobstore/paths/{path}",
                    Properties = new Dictionary<string, string> { { "property-name-1", "property-value-1" } },
                    Tags = new Dictionary<string, string> { { "tag-name-1", "tag-value-1" } },
                    JobName = "TestJob"
                },
            };
        }

        public static string GenerateContainerName(DateTime timestamp)
        {
            return $"{_modelTestContainerNamePrefix}_{timestamp:yyyyMMddHHmmss}_{Guid.NewGuid().ToString()[..8]}";
        }

        [Fact]
        public async Task CreateModelVersionUsingJobOutputs()
        {
            var name = GenerateContainerName(DateTime.UtcNow);
            var version = "123";
            var jobName = "modelregistryintegtestfileuploadnamedoutputdata02";
            var request = GenerateModelVersion(new Uri($"azureml://jobs/{jobName}/outputs/hello_output/paths/helloworld.txt"), jobName);

            var modelVersion = await _modelVersionController.CreateOrUpdate(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, name, version, request).ConfigureAwait(false);
            Assert.Equal(request.Properties.Flavors.Count, modelVersion.Properties.Flavors.Count);
            Assert.True(request.Properties.Flavors.Keys.SequenceEqual(modelVersion.Properties.Flavors.Keys));
            Assert.Equal(request.Properties.ModelType, modelVersion.Properties.ModelType);
            Assert.Equal(request.Properties.JobName, modelVersion.Properties.JobName);
            Assert.Equal(request.Properties.IsArchived, modelVersion.Properties.IsArchived);
            Assert.Equal(request.Properties.IsAnonymous, modelVersion.Properties.IsAnonymous);
            Assert.Equal(request.Properties.Properties.Count, modelVersion.Properties.Properties.Count);
            Assert.True(request.Properties.Properties.Keys.SequenceEqual(modelVersion.Properties.Properties.Keys));
            Assert.True(request.Properties.Properties.Values.SequenceEqual(modelVersion.Properties.Properties.Values));
            Assert.Equal(request.Properties.Tags.Count, modelVersion.Properties.Tags.Count);
            Assert.True(request.Properties.Tags.Keys.SequenceEqual(modelVersion.Properties.Tags.Keys));
            Assert.True(request.Properties.Tags.Values.SequenceEqual(modelVersion.Properties.Tags.Values));

            await _modelVersionController.Delete(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, name, version).ConfigureAwait(false);
        }

        [Theory(DisplayName = "CreateModelVersionWithModelTypeTest")]
        [InlineData(ModelType.custom_model)]
        [InlineData(ModelType.mlflow_model)]
        [InlineData(ModelType.triton_model)]
        public async Task CreateModelVersionWithModelTypeTest(ModelType modelType)
        {
            var name = GenerateContainerName(DateTime.UtcNow);
            var version = "123";
            var request = await GenerateVersion(_datastoreController, Fixture, modelType).ConfigureAwait(false);

            var modelVersion = await _modelVersionController.CreateOrUpdate(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, name, version, request).ConfigureAwait(false);
            Assert.Equal(request.Properties.Flavors.Count, modelVersion.Properties.Flavors.Count);
            Assert.True(request.Properties.Flavors.Keys.SequenceEqual(modelVersion.Properties.Flavors.Keys));
            Assert.Equal(request.Properties.ModelType, modelVersion.Properties.ModelType);
            Assert.Equal(request.Properties.JobName, modelVersion.Properties.JobName);
            Assert.Equal(request.Properties.IsArchived, modelVersion.Properties.IsArchived);
            Assert.Equal(request.Properties.IsAnonymous, modelVersion.Properties.IsAnonymous);
            Assert.Equal(request.Properties.Properties.Count, modelVersion.Properties.Properties.Count);
            Assert.True(request.Properties.Properties.Keys.SequenceEqual(modelVersion.Properties.Properties.Keys));
            Assert.True(request.Properties.Properties.Values.SequenceEqual(modelVersion.Properties.Properties.Values));
            Assert.Equal(request.Properties.Tags.Count, modelVersion.Properties.Tags.Count);
            Assert.True(request.Properties.Tags.Keys.SequenceEqual(modelVersion.Properties.Tags.Keys));
            Assert.True(request.Properties.Tags.Values.SequenceEqual(modelVersion.Properties.Tags.Values));

            // ModelUri is modified by design if the blob container is not AzureML.
            // Assert.Equal(request.Properties.ModelUri, modelVersion.Properties.ModelUri);

            await _modelVersionController.Delete(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, name, version).ConfigureAwait(false);
        }

        [Fact(DisplayName = "CreateModelVersionPresetTest")]
        public async Task CreateModelVersionPresetTest()
        {
            var name = GenerateContainerName(DateTime.UtcNow);
            var version = "123";
            var request = GenerateModelPresetVersion();

            var modelVersion = await _modelVersionController.CreateOrUpdate(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, name, version, request).ConfigureAwait(false);
            Assert.Equal(request.Properties.Flavors.Count, modelVersion.Properties.Flavors.Count);
            Assert.True(request.Properties.Flavors.Keys.SequenceEqual(modelVersion.Properties.Flavors.Keys));
            Assert.Equal(request.Properties.ModelType, modelVersion.Properties.ModelType);
            Assert.Equal(request.Properties.JobName, modelVersion.Properties.JobName);
            Assert.Equal(request.Properties.IsArchived, modelVersion.Properties.IsArchived);
            Assert.Equal(request.Properties.IsAnonymous, modelVersion.Properties.IsAnonymous);
            Assert.Equal(request.Properties.Properties.Count, modelVersion.Properties.Properties.Count);
            Assert.True(request.Properties.Properties.Keys.SequenceEqual(modelVersion.Properties.Properties.Keys));
            Assert.True(request.Properties.Properties.Values.SequenceEqual(modelVersion.Properties.Properties.Values));
            Assert.Equal(request.Properties.Tags.Count, modelVersion.Properties.Tags.Count);
            Assert.True(request.Properties.Tags.Keys.SequenceEqual(modelVersion.Properties.Tags.Keys));
            Assert.True(request.Properties.Tags.Values.SequenceEqual(modelVersion.Properties.Tags.Values));

            var fetchedModelVersion = await _modelVersionController.Get(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, name, version).ConfigureAwait(false);
            Assert.Equal(request.Properties.ModelType, fetchedModelVersion.Properties.ModelType);
            Assert.Equal(request.Properties.ModelUri, fetchedModelVersion.Properties.ModelUri);
            Assert.Equal(request.Properties.Properties.Count, fetchedModelVersion.Properties.Properties.Count);

            await _modelVersionController.Delete(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, name, version).ConfigureAwait(false);
        }

        [Fact(DisplayName = "CreatePresetFormattedModelVersionTest")]
        public async Task CreatePresetFormattedModelVersionTest()
        {
            var name = GenerateContainerName(DateTime.UtcNow);
            var version = "123";
            var request = await GeneratePresetFormattedModelVersion().ConfigureAwait(false);

            var modelVersion = await _modelVersionController.CreateOrUpdate(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, name, version, request).ConfigureAwait(false);
            Assert.Equal(request.Properties.Flavors.Count, modelVersion.Properties.Flavors.Count);
            Assert.True(request.Properties.Flavors.Keys.SequenceEqual(modelVersion.Properties.Flavors.Keys));
            Assert.Equal(request.Properties.ModelType, modelVersion.Properties.ModelType);
            Assert.Equal(request.Properties.JobName, modelVersion.Properties.JobName);
            Assert.Equal(request.Properties.IsArchived, modelVersion.Properties.IsArchived);
            Assert.Equal(request.Properties.IsAnonymous, modelVersion.Properties.IsAnonymous);
            Assert.Equal(request.Properties.Properties.Count, modelVersion.Properties.Properties.Count);
            Assert.True(request.Properties.Properties.Keys.SequenceEqual(modelVersion.Properties.Properties.Keys));
            Assert.True(request.Properties.Properties.Values.SequenceEqual(modelVersion.Properties.Properties.Values));
            Assert.Equal(request.Properties.Tags.Count, modelVersion.Properties.Tags.Count);
            Assert.True(request.Properties.Tags.Keys.SequenceEqual(modelVersion.Properties.Tags.Keys));
            Assert.True(request.Properties.Tags.Values.SequenceEqual(modelVersion.Properties.Tags.Values));
            Assert.Equal(request.Properties.ModelUri, modelVersion.Properties.ModelUri);

            var fetchedModelVersion = await _modelVersionController.Get(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, name, version).ConfigureAwait(false);
            Assert.Equal(request.Properties.ModelType, fetchedModelVersion.Properties.ModelType);
            Assert.Equal(request.Properties.ModelUri, fetchedModelVersion.Properties.ModelUri);
            Assert.Equal(request.Properties.Properties.Count, fetchedModelVersion.Properties.Properties.Count);

            await _modelVersionController.Delete(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, name, version).ConfigureAwait(false);
        }

        public Task InitializeAsync()
        {
            return Task.CompletedTask;
        }

        public async Task DisposeAsync()
        {
            await CleanUpModels().ConfigureAwait(false);
        }

        protected Resource<ModelVersion> GenerateModelVersion(Uri modelUrl, string jobName)
        {
            return new Resource<ModelVersion>
            {
                Properties = new ModelVersion
                {
                    Description = "description",
                    Flavors = new Dictionary<string, FlavorData>() { { "python_function", new FlavorData { Data = new Dictionary<string, string>() { { "loader_module", "test" } } } } },
                    JobName = jobName,
                    ModelUri = modelUrl.ToString(),
                    ModelType = ModelType.custom_model.ToString(),
                    Properties = new Dictionary<string, string> { { "property-name-1", "property-value-1" } },
                    Tags = new Dictionary<string, string> { { "api-version", "2022-10-01" }, { "tag-name-1", "tag-value-1" } }
                }
            };
        }

        protected Resource<ModelVersion> GenerateModelPresetVersion()
        {
            return new Resource<ModelVersion>()
            {
                Properties = new ModelVersion()
                {
                    Description = "MFE E2E test for eyes-on model preset",
                    ModelType = ModelTypeHidden.presets_model.ToString(),
                    Properties = new Dictionary<string, string>()
                    {
                        { "modelDeploymentConfig", MfeTestingConstants.ModelVersionPresetConfig }
                    },
                    Flavors = new Dictionary<string, FlavorData>() { { "python_function", new FlavorData { Data = new Dictionary<string, string>() { { "loader_module", "test" } } } } },
                    Tags = new Dictionary<string, string> { { "api-version", "2022-10-01" }, { "tag-name-1", "tag-value-1" } },
                }
            };
        }

        protected async Task<Resource<ModelVersion>> GeneratePresetFormattedModelVersion()
        {
            // Get the dataset id
            var dataset = await GetPresetFormattedModelDataset().ConfigureAwait(false);
            Assert.NotNull(dataset?.Latest?.SavedDatasetId);
            var modelUrl = $"azureml://datasets/{dataset.Latest.SavedDatasetId}";

            return new Resource<ModelVersion>()
            {
                Properties = new ModelVersion()
                {
                    Description = "MFE E2E test for preset-formatted eyes-on model",
                    ModelType = ModelTypeHidden.presets_model.ToString(),
                    ModelUri = modelUrl,
                    Properties = new Dictionary<string, string>()
                    {
                        { "engineControllerManifestPath", "LocalUpload/MfeModelVersionTests/directory/engine.json" },
                        { "componentVersion", "1" }
                    },
                    Flavors = new Dictionary<string, FlavorData>() { { "python_function", new FlavorData { Data = new Dictionary<string, string>() { { "loader_module", "test" } } } } },
                    Tags = new Dictionary<string, string> { { "api-version", "2022-10-01" }, { "tag-name-1", "tag-value-1" } },
                }
            };
        }

        protected async Task<DatasetDto> GetPresetFormattedModelDataset()
        {
            // Upload manifest and dummy model weights to blob storage
            var datastore = await _datastoreController.Get(Fixture.SubscriptionId.ToString(), Fixture.ResourceGroupName, Fixture.WorkspaceName, "workspaceblobstore").ConfigureAwait(false);
            var storageConnectionDto = await Fixture.GetStorageConnection().ConfigureAwait(false);
            await BlobHelpers.UploadBlob(storageConnectionDto.ConnectionString, datastore.AzureStorageSection.ContainerName, MfeTestingConstants.ModelVersionBlobPath, "some text").ConfigureAwait(false);
            await BlobHelpers.UploadBlob(storageConnectionDto.ConnectionString, datastore.AzureStorageSection.ContainerName, MfeTestingConstants.ModelVersionPresetFormattedModelEngineControllerManifestPath, "{}").ConfigureAwait(false);

            // Register the dataset
            var datasetRequestDto = new DatasetRequestDto
            {
                Name = MfeTestingConstants.ModelVersionPresetFormattedModelDatasetName,
                Description = "Dataset used by MFE model version tests for preset-formatted models",
                DataPath = new DatasetPathDto
                {
                    DatastoreName = "workspaceblobstore",
                    RelativePath = MfeTestingConstants.ModelVersionBlobPath
                },
                DatasetType = DatasetTypes.FileDataset,
                IsVisible = true
            };

            try
            {
                return await _datasetsController.GetDatasetByName(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, MfeTestingConstants.ModelVersionPresetFormattedModelDatasetName).ConfigureAwait(false);
            }
            catch (ServiceInvocationException e) when (e.Response.StatusCode == System.Net.HttpStatusCode.NotFound)
            {
                return await _datasetsController.RegisterFromDataPath(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, datasetRequestDto).ConfigureAwait(false);
            }
        }

        protected async Task CleanUpModels()
        {
            var containers = await _modelContainerController.List(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, count: 200).ConfigureAwait(false);
            var containersToDelete = containers.Value.ToList()
                .Where(c => c.Name.StartsWith(_modelTestContainerNamePrefix, StringComparison.InvariantCulture) &&
                      (c.Properties.Tags.IsNullOrEmpty() || !c.Properties.Tags.ContainsKey(MfeTestingConstants.ModelDoNotDeleteTagKey)) &&
                      ((TimeSpan)(DateTime.UtcNow - c.SystemData?.CreatedAt)).TotalMinutes > 20)
                .Select(c => c.Name);
            foreach (var containerName in containersToDelete)
            {
                try
                {
                    await _modelContainerController.Delete(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, containerName).ConfigureAwait(false);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error while cleaning up models. Message: {ex.Message}. Exception: {ex.StackTrace}");
                }
            }
        }
    }
}
