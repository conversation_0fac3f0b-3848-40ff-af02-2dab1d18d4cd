﻿// <copyright file="BatchEndpointControllerTests.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Net;
using System.Threading.Tasks;
using FluentAssertions;
using Microsoft.MachineLearning.Common.Core;
using Microsoft.MachineLearning.Common.Testing;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20221001.ArmCommon;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20221001.Controllers;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Api;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.ControllerTests.NonVersionedHelpers;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.ControllerTests.V20221001.Helpers;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.XunitExtensions;
using Xunit;

namespace Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.ControllerTests.V20221001
{
    [MfeTest(TestCategory.Integration, ScenarioScope.BatchServices)]
    public class E2EBatchEndpointControllerTests : IStaticFixture<TestFixtureManagementFrontEnd>, IAsyncLifetime
    {
        private const string Apiversion = "2022-10-01";

        private readonly ICodeVersionController _codeVersionController;
        private readonly IBatchDeploymentController _deploymentController;

        public E2EBatchEndpointControllerTests(StaticFixture<TestFixtureManagementFrontEnd> fixture)
        {
            Fixture = fixture;
            Controller = Fixture.ManagementFrontEndClient.GetController<IBatchEndpointController>();
            _codeVersionController = Fixture.ManagementFrontEndClient.GetController<ICodeVersionController>();
            _deploymentController = Fixture.ManagementFrontEndClient.GetController<IBatchDeploymentController>();
        }

        public IBatchEndpointController Controller { get; }

        public TestFixtureManagementFrontEnd Fixture { get; }

        [Fact]
        public async Task BatchEndpointE2EListSucceeds()
        {
            var count = 10;
            var result = await Controller.List(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, count).ConfigureAwait(false);

            result.Value.Should().NotBeNull();
            result.Value.Count.Should().BeLessOrEqualTo(count);
            result.Value.ForEach(x => x.Properties.Keys.Should().BeNull());
        }

        [Fact]
        public async Task BatchEndpointE2ECreateGetDeleteSucceeds()
        {
            var endpoint = await BatchEndpointHelpers.CreateTestEndpoint(Controller, Fixture).ConfigureAwait(false);
            var result = await Controller.Get(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, endpoint.Name).ConfigureAwait(false);
            result.Should().NotBeNull();
            result.Name.Should().Be(endpoint.Name);
            result.Identity.Should().NotBeNull();
            result.Identity.Type.Should().Be(Contracts.V20221001.ArmCommon.ManagedServiceIdentityType.UserAssigned);
            result.Identity.UserAssignedIdentities.Count.Should().BeGreaterThanOrEqualTo(1);
            result.Properties.Keys.Should().BeNull();
            result.Properties.Properties.Should().ContainKey(MfeTestingConstants.BatchEndpointCreationApiVersion);
            result.Properties.Properties[MfeTestingConstants.BatchEndpointCreationApiVersion].Should().Be(Apiversion);
            await Controller.Delete(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, endpoint.Name).ConfigureAwait(false);
            Func<Task> call = () => TestHelpers.RetryUntilExceptionAsync(
                () => Controller.Get(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, endpoint.Name),
                BatchEndpointTestConstants.RetryIntervalUntilNotFoundAfterResourceDeletion,
                BatchEndpointTestConstants.MaxRetriesUntilNotFoundAfterResourceDeletion);
            (await call.Should().ThrowAsync<ServiceInvocationException>()).And.Response.StatusCode.Should().Be(HttpStatusCode.NotFound);
        }

        [Theory]
        [InlineData("PUT")]
        [InlineData("PATCH")]
        public async Task BatchEndpointE2ECreateUpdateDeleteSucceeds(string updateType)
        {
            var codeVersionTracker = new List<string>();
            string tagKey = "tagKey1";
            string tagValue = "tagValue1";
            var codeAsset = await EndpointHelpers.UploadAndRegisterScoringFile(Fixture, _codeVersionController, codeVersionTracker).ConfigureAwait(false);
            var endpoint = await BatchEndpointHelpers.CreateTestEndpoint(Controller, Fixture).ConfigureAwait(false);
            var deployment = await BatchEndpointHelpers.CreateTestEndpointDeployment(_deploymentController, Fixture, endpoint.Name, codeAsset.Id).ConfigureAwait(false);

            endpoint.Should().NotBeNull();
            endpoint.Properties.Defaults.DeploymentName.Should().BeNull();
            endpoint.Properties.Keys.Should().BeNull();
            endpoint.Properties.Properties.Should().ContainKey(MfeTestingConstants.BatchEndpointCreationApiVersion);
            endpoint.Properties.Properties[MfeTestingConstants.BatchEndpointCreationApiVersion].Should().Be(Apiversion);
            endpoint.Identity.Should().NotBeNull();
            endpoint.Identity.Type.Should().Be(Contracts.V20221001.ArmCommon.ManagedServiceIdentityType.UserAssigned);
            endpoint.Identity.UserAssignedIdentities.Count.Should().BeGreaterThanOrEqualTo(1);
            deployment.Should().NotBeNull();

            var result = await BatchEndpointHelpers.UpdateTestEndpoint(
                Controller,
                Fixture,
                endpoint.Name,
                deployment.Name,
                updateType,
                tags: new CaseInsensitiveDictionary(new Dictionary<string, string> { { tagKey, tagValue } }))
            .ConfigureAwait(false);

            if (updateType == "PUT")
            {
                result.Properties.Defaults.DeploymentName.Should().NotBeNull();
                result.Properties.Defaults.DeploymentName.Should().BeEquivalentTo(deployment.Name);
            }

            result.Properties.Keys.Should().BeNull();
            result.Tags.ContainsKey(tagKey).Should().BeTrue();
            result.Tags[tagKey].Should().Be(tagValue);
            result.Identity.Should().NotBeNull();
            result.Identity.Type.Should().Be(Contracts.V20221001.ArmCommon.ManagedServiceIdentityType.UserAssigned);
            result.Identity.UserAssignedIdentities.Count.Should().BeGreaterThanOrEqualTo(1);

            await Controller.Delete(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, endpoint.Name).ConfigureAwait(false);

            Func<Task> call = () => TestHelpers.RetryUntilExceptionAsync(
                () => Controller.Get(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, endpoint.Name),
                BatchEndpointTestConstants.RetryIntervalUntilNotFoundAfterResourceDeletion,
                BatchEndpointTestConstants.MaxRetriesUntilNotFoundAfterResourceDeletion);
            (await call.Should().ThrowAsync<ServiceInvocationException>()).And.Response.StatusCode.Should().Be(HttpStatusCode.NotFound);
            await EndpointHelpers.CleanupCodeAssets(Fixture, _codeVersionController, codeVersionTracker).ConfigureAwait(false);
        }

        public async Task InitializeAsync()
        {
            await BatchEndpointHelpers.CleanUpEndpoints(Controller, Fixture).ConfigureAwait(false);

        }

        public Task DisposeAsync()
        {
            return Task.CompletedTask;
        }
    }
}
