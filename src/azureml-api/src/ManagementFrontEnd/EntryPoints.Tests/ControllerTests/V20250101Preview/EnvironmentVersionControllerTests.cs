﻿// <copyright file="EnvironmentVersionControllerTests.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using FluentAssertions;
using Microsoft.MachineLearning.Common.Testing;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20250101Preview.ArmCommon;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20250101Preview.Assets;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20250101Preview.Controllers;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Api;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.XunitExtensions;
using Xunit;

namespace Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.ControllerTests.V20250101Preview
{
    [MfeTest(TestCategory.Integration, ScenarioScope.Environments)]
    public class E2EEnvironmentVersionControllerTests : EnvironmentControllerTests<TestFixtureManagementFrontEnd>, IStaticFixture<TestFixtureManagementFrontEnd>
    {
        public E2EEnvironmentVersionControllerTests(StaticFixture<TestFixtureManagementFrontEnd> fixture)
            : base(fixture)
        {
        }
    }

    public abstract class EnvironmentControllerTests<TFixture>
        where TFixture : TestFixtureManagementFrontEnd
    {
        public EnvironmentControllerTests(TFixture fixture)
        {
            Fixture = fixture;

            EnvironmentContainerController = Fixture.ManagementFrontEndClient.GetController<IEnvironmentContainerController>(randomizeCase: false);
            EnvironmentVersionController = Fixture.ManagementFrontEndClient.GetController<IEnvironmentVersionController>(randomizeCase: false);
        }

        public IEnvironmentVersionController EnvironmentVersionController { get; }

        public IEnvironmentContainerController EnvironmentContainerController { get; }

        public TFixture Fixture { get; }

        [Fact]
        public async Task TestGetVersionImageDetails()
        {
            var environmentName = "vulnTest";

            var spec = await EnvironmentVersionController.GetEnvironmentVersion(
                Fixture.SubscriptionId,
                Fixture.ResourceGroupName,
                Fixture.WorkspaceName,
                environmentName,
                "7").ConfigureAwait(false);

            spec.Should().NotBeNull();
            spec.Properties.ImageDetails.VulnerabilityFindings.Should().NotBeNull();
            spec.Properties.ImageDetails.VulnerabilityFindings.TotalFindingsCount.Should().NotBe(0);
            spec.Properties.ImageDetails.VulnerabilityFindings.Data.First().PackageDetails.Should().NotBeNull();
        }

        [Fact]
        public async Task TestListVersionsImageDetails()
        {
            var environmentName = "vulnTest";

            var listVersionsResult = await EnvironmentVersionController.ListEnvironmentVersions(
                Fixture.SubscriptionId,
                Fixture.ResourceGroupName,
                Fixture.WorkspaceName,
                environmentName).ConfigureAwait(false);
            listVersionsResult.Should().NotBeNull();
            listVersionsResult.Value.Should().HaveCountGreaterThan(0);

            var listVersions = listVersionsResult.Value;
            var latestVersion = listVersions.FirstOrDefault(item => item.Name == "7");

            latestVersion.Should().NotBeNull();
            latestVersion.Properties.ImageDetails.VulnerabilityFindings.Should().NotBeNull();
            latestVersion.Properties.ImageDetails.VulnerabilityFindings.TotalFindingsCount.Should().NotBe(0);
            latestVersion.Properties.ImageDetails.VulnerabilityFindings.Data.First().PackageDetails.Should().NotBeNull();
        }

        [Fact]
        public async Task TestGetContainersAndVersions()
        {
            var listEnvironmentsResult = await EnvironmentContainerController.ListEnvironments(
                Fixture.SubscriptionId,
                Fixture.ResourceGroupName,
                Fixture.WorkspaceName).ConfigureAwait(false);
            listEnvironmentsResult.Should().NotBeNull();
            listEnvironmentsResult.Value.Should().HaveCountGreaterThan(0);

            var firstContainer = listEnvironmentsResult.Value.First();

            // the contract is wrong so this fails right now. GetEnvironment should return EnvironmentContainer not EnvironmentVersion

            /*var getEnvironmentResult = await EnvironmentContainerController.GetEnvironment(
                Fixture.SubscriptionId,
                Fixture.ResourceGroupName,
                Fixture.WorkspaceName,
                firstContainer.Name).ConfigureAwait(false);

            // Can't check if firstContainer is equivalent to getEnvironmentResult because some fields have a default value set to timestamp when created
            getEnvironmentResult.Id.Should().BeEquivalentTo(firstContainer.Id);
            getEnvironmentResult.Name.Should().BeEquivalentTo(firstContainer.Name);
            getEnvironmentResult.Type.Should().BeEquivalentTo(firstContainer.Type);
            getEnvironmentResult.Properties.Description.Should().BeEquivalentTo(firstContainer.Properties.Description);
            getEnvironmentResult.Properties.Tags.Should().BeEquivalentTo(firstContainer.Properties.Tags);*/

            var listVersionsResult = await EnvironmentVersionController.ListEnvironmentVersions(
                Fixture.SubscriptionId,
                Fixture.ResourceGroupName,
                Fixture.WorkspaceName,
                firstContainer.Name).ConfigureAwait(false);
            listVersionsResult.Should().NotBeNull();
            listVersionsResult.Value.Should().HaveCountGreaterThan(0);

            var firstVersion = listVersionsResult.Value.First();

            var spec = await EnvironmentVersionController.GetEnvironmentVersion(
                Fixture.SubscriptionId,
                Fixture.ResourceGroupName,
                Fixture.WorkspaceName,
                firstContainer.Name,
                firstVersion.Name).ConfigureAwait(false);

            // Exclude Image because Feed may have copied to multiple different ACRs
            spec.Should().BeEquivalentTo(firstVersion, options => options.Excluding(x => x.Properties.Image));
        }

        [Fact]
        public async Task TestEnvironmentVersionCreateGetUpdateListDelete()
        {
            var name = Guid.NewGuid().ToString();
            var version = Guid.NewGuid().ToString();

            var originalEnvPayload = new Resource<EnvironmentVersion>()
            {
                Properties = new EnvironmentVersion()
                {
                    CondaFile = $"{{'name': '{Guid.NewGuid()}','dependencies': ['python=3.14',{{ 'pip': ['azureml-defaults']}}]}}",
                    Image = "mcr.microsoft.com/azureml/openmpi4.1.0-ubuntu22.04",
                    Description = "originalDescription",
                    Tags = new Dictionary<string, string>()
                    {
                        { "originalKey", "originalValue" }
                    }
                },
            };

            var response = await EnvironmentVersionController.AddOrUpdateEnvironment(
                Fixture.SubscriptionId,
                Fixture.ResourceGroupName,
                Fixture.WorkspaceName,
                name,
                version,
                originalEnvPayload).ConfigureAwait(false);

            var originalEnv = await EnvironmentVersionController.GetEnvironmentVersion(
                Fixture.SubscriptionId,
                Fixture.ResourceGroupName,
                Fixture.WorkspaceName,
                name,
                version).ConfigureAwait(false);

            // Id is the assetId and contains the name/version
            originalEnv.Id.Should().ContainAll(name, version);
            originalEnv.Properties.IsAnonymous.Should().BeFalse();
            originalEnv.SystemData.CreatedAt.Should().BeAfter(DateTime.Now.AddMinutes(-15));
            originalEnv.SystemData.LastModifiedAt.Should().BeAfter(DateTime.Now.AddMinutes(-15));
            originalEnv.Properties.Tags.Should().BeEquivalentTo(originalEnvPayload.Properties.Tags);
            originalEnv.Properties.Description.Should().Be(originalEnvPayload.Properties.Description);

            var updatedEnvPayload = new Resource<EnvironmentVersion>()
            {
                Properties = new EnvironmentVersion()
                {
                    CondaFile = $"{{'name': '{Guid.NewGuid()}','dependencies': ['python=3.14',{{ 'pip': ['azureml-defaults']}}]}}",
                    Image = "mcr.microsoft.com/azureml/openmpi4.1.0-ubuntu22.04",
                    Description = "newDescription",
                    Tags = new Dictionary<string, string>()
                    {
                        { "originalKey", "originalValue" },
                        { "newKey", "newValue" }
                    }
                },
            };
            response = await EnvironmentVersionController.AddOrUpdateEnvironment(
                Fixture.SubscriptionId,
                Fixture.ResourceGroupName,
                Fixture.WorkspaceName,
                name,
                version,
                updatedEnvPayload).ConfigureAwait(false);

            var updatedEnv = await EnvironmentVersionController.GetEnvironmentVersion(
                Fixture.SubscriptionId,
                Fixture.ResourceGroupName,
                Fixture.WorkspaceName,
                name,
                version).ConfigureAwait(false);

            // Id is the assetId and contains the name/version
            updatedEnv.Id.Should().ContainAll(name, version);
            updatedEnv.Properties.IsAnonymous.Should().BeFalse();
            updatedEnv.SystemData.CreatedAt.Should().Be(originalEnv.SystemData.CreatedAt);
            updatedEnv.SystemData.LastModifiedAt.Should().Be(originalEnv.SystemData.LastModifiedAt);
            updatedEnv.Properties.Tags.Should().BeEquivalentTo(updatedEnvPayload.Properties.Tags);
            updatedEnv.Properties.Description.Should().Be(updatedEnvPayload.Properties.Description);

            // We query Index to get the environments and Index is EventuallyConsistent
            await EventualConsistencyTesting.RetryUntilAssertionPasses(
                async () =>
                {
                    var retrievedEnvList = await EnvironmentVersionController.ListEnvironmentVersions(
                        Fixture.SubscriptionId,
                        Fixture.ResourceGroupName,
                        Fixture.WorkspaceName,
                        name).ConfigureAwait(false);
                    retrievedEnvList.Value.Count().Should().Be(1);
                    var retrievedEnvFromList = retrievedEnvList.Value[0];
                    retrievedEnvFromList.Id.Should().ContainAll(name, version);
                    retrievedEnvFromList.Properties.IsAnonymous.Should().BeFalse();
                    retrievedEnvFromList.SystemData.CreatedAt.Should().BeAfter(DateTime.Now.AddMinutes(-15));
                    retrievedEnvFromList.SystemData.LastModifiedAt.Should().BeAfter(DateTime.Now.AddMinutes(-15));
                });
        }

        [Fact]
        public async Task TestEnvironmentContainerNextVersion()
        {
            var name = Guid.NewGuid().ToString();
            var version = "1";

            var envPayload = new Resource<EnvironmentVersion>()
            {
                Properties = new EnvironmentVersion()
                {
                    CondaFile = $"{{'name': '{Guid.NewGuid()}','dependencies': ['python=3.14',{{ 'pip': ['azureml-defaults']}}]}}",
                    Image = "mcr.microsoft.com/azureml/openmpi4.1.0-ubuntu22.04",
                },
            };

            var response = await EnvironmentVersionController.AddOrUpdateEnvironment(
                Fixture.SubscriptionId,
                Fixture.ResourceGroupName,
                Fixture.WorkspaceName,
                name,
                version,
                envPayload).ConfigureAwait(false);

            // the contract is wrong so this fails right now. GetEnvironment should return EnvironmentContainer not EnvironmentVersion
            /*var envContainer = await EnvironmentContainerController.GetEnvironment(
                Fixture.SubscriptionId,
                Fixture.ResourceGroupName,
                Fixture.WorkspaceName,
                name).ConfigureAwait(false);*/
            //envContainer.Properties.NextVersion.Should().Be("2");

            await EventualConsistencyTesting.RetryUntilAssertionPasses(
                async () =>
                {
                    var retrievedContainersList = await EnvironmentContainerController.ListEnvironments(
                        Fixture.SubscriptionId,
                        Fixture.ResourceGroupName,
                        Fixture.WorkspaceName).ConfigureAwait(false);
                    var retrievedContainer = retrievedContainersList.Value.First(x => x.Name == name);
                    retrievedContainer.Properties.NextVersion.Should().Be("2");
                });
        }
    }
}