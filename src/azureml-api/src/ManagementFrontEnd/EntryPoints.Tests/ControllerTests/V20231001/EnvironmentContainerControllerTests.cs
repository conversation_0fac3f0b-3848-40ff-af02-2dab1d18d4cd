﻿// <copyright file="ModelVersionControllerTests.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using Microsoft.MachineLearning.Common.Testing;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20231001;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20231001.ArmCommon;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20231001.Controllers;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Api;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.XunitExtensions;
using Xunit;

namespace Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.ControllerTests.V20231001
{
    [MfeTest(TestCategory.Integration, ScenarioScope.Environments)]
    public class E2EEnvironmentContainerControllerTests : EnvironmentContainerControllerTests<TestFixtureManagementFrontEnd>, IStaticFixture<TestFixtureManagementFrontEnd>
    {
        public E2EEnvironmentContainerControllerTests(StaticFixture<TestFixtureManagementFrontEnd> fixture)
            : base(fixture)
        {
        }
    }

    public abstract class EnvironmentContainerControllerTests<TFixture>
        where TFixture : TestFixtureManagementFrontEnd
    {
        public EnvironmentContainerControllerTests(TFixture fixture)
        {
            Fixture = fixture;

            EnvironmentContainerController = Fixture.ManagementFrontEndClient.GetController<IEnvironmentContainerController>(randomizeCase: false);
        }

        public IEnvironmentContainerController EnvironmentContainerController { get; }

        public TFixture Fixture { get; }

        [Fact]
        public async Task TestEnvironmentContainerWithUnsupportedFields_Throws400()
        {
            var name = $"MfeEnvironmentController_{Guid.NewGuid()}";
            var environmentContainer = new EnvironmentContainer()
            {
                Description = "description",
                IsArchived = false
            };

            var createAction = () => EnvironmentContainerController.CreateOrUpdate(
                Fixture.SubscriptionId,
                Fixture.ResourceGroupName,
                Fixture.WorkspaceName,
                name,
                new Resource<EnvironmentContainer> { Properties = environmentContainer });

            await createAction.Should().ThrowAsync<MfeResponseValidationException>().Where(sie => sie.Response.StatusCode == System.Net.HttpStatusCode.BadRequest).ConfigureAwait(false);

            environmentContainer = new EnvironmentContainer()
            {
                IsArchived = false,
                Properties = new Dictionary<string, string>() { ["thisShould"] = "fail" }
            };

            createAction = () => EnvironmentContainerController.CreateOrUpdate(
                Fixture.SubscriptionId,
                Fixture.ResourceGroupName,
                Fixture.WorkspaceName,
                name,
                new Resource<EnvironmentContainer> { Properties = environmentContainer });

            await createAction.Should().ThrowAsync<MfeResponseValidationException>().Where(sie => sie.Response.StatusCode == System.Net.HttpStatusCode.BadRequest).ConfigureAwait(false);

            environmentContainer = new EnvironmentContainer()
            {
                IsArchived = false,
                Tags = new Dictionary<string, string>() { ["thisShould"] = "fail" }
            };

            createAction = () => EnvironmentContainerController.CreateOrUpdate(
                Fixture.SubscriptionId,
                Fixture.ResourceGroupName,
                Fixture.WorkspaceName,
                name,
                new Resource<EnvironmentContainer> { Properties = environmentContainer });

            await createAction.Should().ThrowAsync<MfeResponseValidationException>().Where(sie => sie.Response.StatusCode == System.Net.HttpStatusCode.BadRequest).ConfigureAwait(false);
        }
    }
}
