﻿// <copyright file="DeploymentControllerTest.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.MachineLearning.Common.Testing;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20221001.Controllers;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20221001.Deployments;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Api;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.XunitExtensions;
using Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts.Assets;
using Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts.Common;
using Newtonsoft.Json.Linq;
using Xunit;

namespace Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.ControllerTests
{
    [MfeTest(TestCategory.Integration, ScenarioScope.Deployments)]
    public class DeploymentControllerTest : IStaticFixture<TestFixtureManagementFrontEnd>
    {
        public DeploymentControllerTest(StaticFixture<TestFixtureManagementFrontEnd> fixture)
        {
            Fixture = fixture;
            DeploymentController = Fixture.ManagementFrontEndClient.GetController<IDeploymentController>();
        }

        public TestFixtureManagementFrontEnd Fixture { get; }

        public IDeploymentController DeploymentController { get; }

        [Theory]
        [InlineData("azureml://registries/testFeed/components/test_366773231359/versions/2", true)]
        [InlineData("azureml://fake/registries/id", false)]
        public async Task TestPreflightRegistryComponents(string id, bool preflightSucceeded)
        {
            var properties = new ComponentVersionV2();
            var spec = new JObject
            {
                { "$schema", "http://azureml/sdk-2-0/AutoMLComponent.json" },
                { "name", "test_366773231359" },
                { "version", "2" },
                { "type", "automl" },
                { "display_name", "AutoML Classification" },
                { "description", "test" },
                { "task", "classification" },
            };
            properties.ComponentSpec = spec;
            var preflightDeployment = new PreflightDeployment
            {
                Resources = new List<PreflightResource>
                {
                    new PreflightResource
                    {
                        Id = id,
                        Name = "2",
                        ApiVersion = "2022-10-01",
                        Properties = JToken.FromObject(properties),
                        SystemData = null,
                        Type = MfeConstants.ArmFullTypeComponentVersion,
                    },
                },
            };
            var result = await DeploymentController.Preflight(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.RegistryName, preflightDeployment).ConfigureAwait(false);

            if (preflightSucceeded)
            {
                Assert.Null(result.Error);
                Assert.Equal(PreflightStatus.Succeeded, result.Status);
            }
            else
            {
                Assert.Single(result.Error.Details);
                Assert.Equal("Invalid asset id azureml://fake/registries/id", result.Error.Details.First().Message);
                Assert.Equal(PreflightStatus.Failed, result.Status);
            }
        }

        [Theory]
        [InlineData("/subscriptions/4bf6b28a-452b-4af4-8080-8a196ee0ca4b/resourceGroups/int-shared-static-test-resources/providers/Microsoft.MachineLearningServices/workspaces/servicestestworkspace/components/bing.relevance.convert2ss/versions/637933938296175147", true)]
        [InlineData("fake/subscriptions/id", false)]
        public async Task TestPreflightWorkspaceComponent(string id, bool preflightSucceeded)
        {
            var properties = new ComponentVersionV2();
            var spec = new JObject
            {
                { "$schema", "https://azuremlschemas.azureedge.net/stable/commandComponent.schema.json" },
                { "name", "test" },
                { "version", "1" },
                { "type", "command" },
                { "display_name", "test" },
                { "description", "test" },
                { "tags", new JObject { { "category", "Component Tutorial" } } },
                { "inputs", null },
                { "outputs", null },
                { "command", "test" },
                { "environment",  "azureml:azureml-sklearn-10-ubuntu2004-py38-cpu@latest" },
                { "code", "test" },
                { "resources", new JObject { { "instance_count", 1 } } }
            };
            properties.ComponentSpec = spec;
            var preflightDeployment = new PreflightDeployment
            {
                Resources = new List<PreflightResource>
                {
                    new PreflightResource
                    {
                        Id = id,
                        Name = "637933938296175147",
                        ApiVersion = "2022-10-01",
                        Properties = JToken.FromObject(properties),
                        SystemData = null,
                        Type = MfeConstants.ArmFullTypeComponentVersion,
                    },
                },
            };
            var result = await DeploymentController.Preflight(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, preflightDeployment).ConfigureAwait(false);

            if (preflightSucceeded)
            {
                Assert.Null(result.Error);
                Assert.Equal(PreflightStatus.Succeeded, result.Status);
            }
            else
            {
                Assert.Single(result.Error.Details);
                Assert.Equal("Invalid asset id fake/subscriptions/id", result.Error.Details.First().Message);
                Assert.Equal(PreflightStatus.Failed, result.Status);
            }
        }

        [Fact]
        public async Task TestPreflightComponentVersionWithResourceProperitesInvalid()
        {
            var preflightDeployment = new PreflightDeployment
            {
                Resources = new List<PreflightResource>
                {
                    new PreflightResource
                    {
                        Id = $"/subscriptions/{Fixture.SubscriptionId}/resourceGroups/{Fixture.ResourceGroupName}/providers/Microsoft.MachineLearningServices/workspaces/{Fixture.WorkspaceName}/components/bing.relevance.convert2ss/versions/637933938296175147",
                        Name = "637933938296175147",
                        ApiVersion = "2022-10-01",
                        // make Properties deserialization failed
                        Properties = JToken.FromObject(new List<string>()),
                        SystemData = null,
                        Type = MfeConstants.ArmFullTypeComponentVersion,
                    },
                },
            };
            var result = await DeploymentController.Preflight(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, preflightDeployment).ConfigureAwait(false);

            Assert.Single(result.Error.Details);
            Assert.Contains("Resource Properties field should be ComponentVersionV2", result.Error.Details.First().Message, StringComparison.InvariantCultureIgnoreCase);
            Assert.Equal(PreflightStatus.Failed, result.Status);
        }
    }
}
