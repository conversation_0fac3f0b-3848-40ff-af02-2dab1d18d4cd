﻿// <copyright file="ReachabilityTests.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Common.Core.Contracts;
using Microsoft.MachineLearning.Common.Core;
using Microsoft.MachineLearning.Common.Core.Contracts.Errors;
using Microsoft.MachineLearning.Common.Testing;
using Microsoft.MachineLearning.Common.WebApi.Client;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Api;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.XunitExtensions;
using Microsoft.MachineLearning.ManagementFrontEnd.Services.Extensions;
using Xunit;

namespace Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.ControllerTests
{
    [MfeTest(TestCategory.Component, ScenarioScope.MfeCommon)]
    public class ReachabilityTests : IStaticFixture<TestFixtureManagementFrontEnd>
    {
        private readonly TestFixtureManagementFrontEnd _fixture;

        public ReachabilityTests(StaticFixture<TestFixtureManagementFrontEnd> fixture)
        {
            _fixture = fixture;
        }

        public static IEnumerable<object[]> ControllerInterfaceTypesWithList => MfeAssemblies.Contracts.DefinedTypes
            .Where(x => x.GetCustomAttribute(typeof(InterfaceRoutePrefixAttribute)) != null)
            .Where(x => x.GetCustomAttribute<ApiVersionParameterAttribute>(true)?.Value != MfeApiVersions.Oct2021DataplanePreview.ToSerializedValue())
            .Where(x => x.GetMethod("List") != null)
            .Where(x => x.GetMethod("List").GetParameters().Count(p => !p.HasDefaultValue) == 3) // Subscription, RG name, WS name.
            .Select(x => new[] { x })
            .ToList();

        /// <summary>
        /// Test general accessibility of the different controllers in each API version.
        ///
        /// We should be able to call List() on any workspace-level resource type without
        /// needing extra per-resource knowledge like what type to use for the request body, etc.
        /// This means we can get some test coverage of routing and deserialization automatically.
        /// </summary>
        [Theory(Skip = "WIP fix https://msdata.visualstudio.com/Vienna/_workitems/edit/2490108")]
        [MemberData(nameof(ControllerInterfaceTypesWithList))]
        public async Task TestControllerIsReachable(Type controllerType)
        {
            var listMethod = controllerType.GetMethod("List");
            var controller = _fixture.ManagementFrontEndClient.GetController(controllerType);

            bool registryController = controllerType.GetCustomAttribute<InterfaceRoutePrefixAttribute>().Template.Contains("/registries/", StringComparison.InvariantCultureIgnoreCase);
            var parameters = new object[] { _fixture.SubscriptionId, _fixture.ResourceGroupName, registryController ? _fixture.RegistryName : _fixture.WorkspaceName }
                .Concat(Enumerable.Repeat(Type.Missing, listMethod.GetParameters().Length - 3))
                .ToArray();

            try
            {
                await ((Task)listMethod.Invoke(controller, parameters)).ConfigureAwait(false);
            }
            catch (MfeResponseValidationException e) when (e.Message?.Contains(nameof(NotImplementedException), StringComparison.Ordinal) == true)
            {
                // Not supporting list is an ARM violation, but getting the right error response means
                // we are at least getting to the controller and back, so to allow for WIP controllers
                // we'll ignore it for this test.
            }
            catch (ServiceInvocationException e) when (
                e.ErrorResponse?.Error?.InnerError?.Code == nameof(NotSupported) ||
                e.ErrorResponse?.Error?.GetAdditionalInfo<InnerErrorResponse>("InnerError").FirstOrDefault()?.Code == nameof(NotSupported))
            {
            }
            catch (MfeResponseValidationException e) when (
                controllerType.Name.Contains("IFeature") &&
                e.Response.StatusCode == System.Net.HttpStatusCode.BadRequest &&
                e.Message?.Contains("Workspace Kind: Default is not supported in current API", StringComparison.OrdinalIgnoreCase) == true)
            {
                // Feature store actions require workspaces with special "Kind"
                // However, the fact that the controller was reached to throw this error is sufficient to validate its reachability
            }
            catch (ServiceInvocationException e) when (
                controllerType.Name.Contains("IInferencePoolController") &&
                e.Response.StatusCode == System.Net.HttpStatusCode.BadRequest)
            {
            }
        }
    }
}
