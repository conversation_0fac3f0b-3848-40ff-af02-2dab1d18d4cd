﻿// <copyright file="ComponentVersionControllerTests.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using Microsoft.Azure.Storage.Blob;
using Microsoft.MachineLearning.Common.Core;
using Microsoft.MachineLearning.Common.Testing;
using Microsoft.MachineLearning.Feed.Contracts;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20221201Preview.ArmCommon;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20221201Preview.Assets;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20221201Preview.Controllers.Registries;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Api;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.ControllerTests.V20221201Preview.Helpers;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.XunitExtensions;
using Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts.Common;
using Xunit;
using ITemporaryDataReferenceController = Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20211001Dataplane.Controllers.ITemporaryDataReferenceController;

namespace Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.ControllerTests.V20221201Preview
{
    [MfeTest(TestCategory.Integration, ScenarioScope.Components)]
    public class E2EComponentVersionControllerTests : ComponentVersionControllerTests<TestFixtureManagementFrontEnd>, IStaticFixture<TestFixtureManagementFrontEnd>
    {
        public E2EComponentVersionControllerTests(StaticFixture<TestFixtureManagementFrontEnd> fixture)
            : base(fixture)
        {
        }
    }

    public abstract class ComponentVersionControllerTests<TFixture>
        where TFixture : TestFixtureManagementFrontEnd
    {
        public ComponentVersionControllerTests(TFixture fixture)
        {
            Fixture = fixture;

            ComponentContainerController = Fixture.ManagementFrontEndClient.GetController<IComponentContainerController>(randomizeCase: false);
            ComponentVersionController = Fixture.ManagementFrontEndClient.GetController<IComponentVersionController>(randomizeCase: false);
            CodeVersionController = Fixture.ManagementFrontEndClient.GetController<ICodeVersionController>(randomizeCase: false);
            TemporaryDataReferenceController = Fixture.ManagementFrontEndClient.GetControllerNoSerializerSettings<ITemporaryDataReferenceController>(randomizeCase: false);
        }

        public IComponentVersionController ComponentVersionController { get; }

        public IComponentContainerController ComponentContainerController { get; }

        public ICodeVersionController CodeVersionController { get; }

        public ITemporaryDataReferenceController TemporaryDataReferenceController { get; }

        public TFixture Fixture { get; }

        [Fact]
        public async Task TestComponentContainerCreateGetUpdateListDelete()
        {
            var name = $"MfeRegistryComponent_{Guid.NewGuid()}";

            // Create compomnent container
            await TestCreateComponentContainer(name, true).ConfigureAwait(false);
            var componentContainer = await ComponentContainerController.Get(
                Fixture.SubscriptionId,
                Fixture.ResourceGroupName,
                Fixture.RegistryName,
                name).ConfigureAwait(false);
            Assert.Equal($"azureml://registries/{Fixture.RegistryName}/components/{name}", componentContainer.Id);
            Assert.Equal(name, componentContainer.Name);
            Assert.Equal(MfeConstants.ArmFullTypeRegistryComponentContainer, componentContainer.Type);
            Assert.False(componentContainer.Properties.IsArchived);

            // Update compomnent container
            componentContainer.Properties.IsArchived = true;
            await TestUpdateComponentContainer(name, componentContainer).ConfigureAwait(false);
            componentContainer = await ComponentContainerController.Get(
                Fixture.SubscriptionId,
                Fixture.ResourceGroupName,
                Fixture.RegistryName,
                name).ConfigureAwait(false);
            Assert.Equal($"azureml://registries/{Fixture.RegistryName}/components/{name}", componentContainer.Id);
            Assert.Equal(name, componentContainer.Name);
            Assert.Equal(MfeConstants.ArmFullTypeRegistryComponentContainer, componentContainer.Type);
            Assert.True(componentContainer.Properties.IsArchived);

            // Create a second compomnent container
            var name2 = $"MfeRegistryComponent_{Guid.NewGuid()}";
            await TestCreateComponentContainer(name2, false).ConfigureAwait(false);
            componentContainer = await ComponentContainerController.Get(
                Fixture.SubscriptionId,
                Fixture.ResourceGroupName,
                Fixture.RegistryName,
                name2).ConfigureAwait(false);
            Assert.Equal($"azureml://registries/{Fixture.RegistryName}/components/{name2}", componentContainer.Id);
            Assert.Equal(name2, componentContainer.Name);
            Assert.Equal(MfeConstants.ArmFullTypeRegistryComponentContainer, componentContainer.Type);
            Assert.False(componentContainer.Properties.IsArchived);

            // List compomnent container
            var componentContainers = await ComponentContainerController.List(
                Fixture.SubscriptionId,
                Fixture.ResourceGroupName,
                Fixture.RegistryName).ConfigureAwait(false);
            Assert.NotNull(componentContainers);
            Assert.True(componentContainers.Value.Count >= 2, "Must contain at least 2 component containers");

            // Delete compomnent container
            var ex = await Assert.ThrowsAsync<ServiceInvocationException>(() => ComponentContainerController.Delete(
                Fixture.SubscriptionId,
                Fixture.ResourceGroupName,
                Fixture.RegistryName,
                name)).ConfigureAwait(false);
            Assert.Contains("ComponentContainer_Registry_DeleteV2 is not supported", ex.Message, StringComparison.InvariantCulture);
        }

        [Fact]
        public async Task TestComponentVersionCreateGetUpdateListDelete()
        {
            var name = $"MfeRegistryComponent_{Guid.NewGuid()}";
            var version = "1";

            // Create component version 1
            await TestCreate(name, version).ConfigureAwait(false);
            var componentVersion = await ComponentVersionController.Get(
                Fixture.SubscriptionId,
                Fixture.ResourceGroupName,
                Fixture.RegistryName,
                name,
                version).ConfigureAwait(false);
            Assert.Equal($"azureml://registries/{Fixture.RegistryName}/components/{name}/versions/{version}", componentVersion.Id);
            Assert.Equal(version, componentVersion.Name);
            Assert.Equal(MfeConstants.ArmFullTypeRegistryComponentVersion, componentVersion.Type);
            Assert.False(componentVersion.Properties.IsArchived);
            Assert.Equal(new Dictionary<string, string> { { "tags1", "tags1" } }, componentVersion.Properties.Tags);
            Assert.Equal(new Dictionary<string, string> { { "properties1", "properties1" } }, componentVersion.Properties.Properties);

            // Update component version 1
            componentVersion.Properties.IsArchived = true;
            componentVersion.Properties.Tags["tags1"] = "tags1Updated";
            componentVersion.Properties.Properties.Add("properties2", "properties2");
            var response = await ComponentVersionController.CreateOrUpdate(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.RegistryName, name, version, componentVersion).ConfigureAwait(false);
            Assert.True(response.Headers.TryGetValues(MfeConstants.AzureAsyncOperation, out var values));
            var status = await PollHelper.PollAsyncOperationUntilComplete(values.First()).ConfigureAwait(false);
            Assert.Equal(AssetProvisioningState.Succeeded, status);
            componentVersion = await ComponentVersionController.Get(
                            Fixture.SubscriptionId,
                            Fixture.ResourceGroupName,
                            Fixture.RegistryName,
                            name,
                            version).ConfigureAwait(false);
            Assert.Equal($"azureml://registries/{Fixture.RegistryName}/components/{name}/versions/{version}", componentVersion.Id);
            Assert.Equal(version, componentVersion.Name);
            Assert.Equal(MfeConstants.ArmFullTypeRegistryComponentVersion, componentVersion.Type);
            Assert.True(componentVersion.Properties.IsArchived);
            Assert.Equal(new Dictionary<string, string> { { "tags1", "tags1Updated" } }, componentVersion.Properties.Tags);
            Assert.Equal(new Dictionary<string, string> { { "properties1", "properties1" }, { "properties2", "properties2" } }, componentVersion.Properties.Properties);

            // Create component version 2
            await TestCreate(name, "2", true, false).ConfigureAwait(false);
            componentVersion = await ComponentVersionController.Get(
                Fixture.SubscriptionId,
                Fixture.ResourceGroupName,
                Fixture.RegistryName,
                name,
                "2").ConfigureAwait(false);
            Assert.Equal($"azureml://registries/{Fixture.RegistryName}/components/{name}/versions/2", componentVersion.Id);
            Assert.Equal("2", componentVersion.Name);
            Assert.Equal(MfeConstants.ArmFullTypeRegistryComponentVersion, componentVersion.Type);
            Assert.True(componentVersion.Properties.IsArchived);
            Assert.Equal(new Dictionary<string, string> { { "tags1", "tags1" } }, componentVersion.Properties.Tags);
            Assert.Equal(new Dictionary<string, string> { { "properties1", "properties1" } }, componentVersion.Properties.Properties);

            // List component versions
            try
            {
                var componentVersions = await ComponentVersionController.List(
                    Fixture.SubscriptionId,
                    Fixture.ResourceGroupName,
                    Fixture.RegistryName,
                    name,
                    top: 10).ConfigureAwait(false);
            }
            catch (ServiceInvocationException sie)
            {
                Assert.Equal(HttpStatusCode.NotFound, sie.Response.StatusCode);
            }
            // Assert.Equal(2, componentVersions.Value.Count);

            // Delete component version 1
            var ex = await Assert.ThrowsAsync<ServiceInvocationException>(() => ComponentVersionController.Delete(
                Fixture.SubscriptionId,
                Fixture.ResourceGroupName,
                Fixture.RegistryName,
                name,
                version)).ConfigureAwait(false);
            Assert.Contains("ComponentVersionV2_Registry_DeleteV2 is not supported", ex.Message, StringComparison.InvariantCulture);
        }

        private async Task TestCreate(string name, string version, bool isArchived = false, bool pollAsyncOperation = true)
        {
            var environmentId = new AssetReference(
                region: null,
                assetContainerId: Fixture.StaticRegistry,
                type: AssetType.Environments,
                name: Fixture.StaticRegistryEnvironment,
                label: "latest",
                resourceType: AssetContainerTypeOld.Feed,
                assetResourceType: AssetResourceType.Registry).ToString();

            var codeId = await CreateComponentCode(name, version).ConfigureAwait(false);
            var componentVersion = ComponentVersionHelpers.GetNewComponentVersion(name, version, environmentId, codeId, isArchived: isArchived);

            var resourceComponentVersion = ConvertToComponentVersionResource(componentVersion, name, version);

            var response = await ComponentVersionController.CreateOrUpdate(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.RegistryName, name, version, resourceComponentVersion).ConfigureAwait(false);
            if (pollAsyncOperation)
            {
                Assert.True(response.Headers.TryGetValues(MfeConstants.AzureAsyncOperation, out var values));
                var status = await PollHelper.PollAsyncOperationUntilComplete(values.First()).ConfigureAwait(false);
                Assert.Equal(AssetProvisioningState.Succeeded, status);
            }
            else
            {
                componentVersion = await PollHelper.PollGetUntilComplete(
                    () => ComponentVersionController.Get(
                    Fixture.SubscriptionId,
                    Fixture.ResourceGroupName,
                    Fixture.RegistryName,
                    name,
                    version)).ConfigureAwait(false);
                Assert.Equal(AssetProvisioningState.Succeeded, componentVersion.Properties.ProvisioningState);
            }
        }

        private async Task TestCreateComponentContainer(string name, bool pollAsyncOperation = true)
        {
            var response = await ComponentContainerController.CreateOrUpdate(
                Fixture.SubscriptionId,
                Fixture.ResourceGroupName,
                Fixture.RegistryName,
                name,
                new Resource<ComponentContainer>()
                {
                    Properties = new ComponentContainer()
                    {
                        IsArchived = false,
                    }
                }).ConfigureAwait(false);

            if (pollAsyncOperation)
            {
                Assert.True(response.Headers.TryGetValues(MfeConstants.AzureAsyncOperation, out var values));
                var status = await PollHelper.PollAsyncOperationUntilComplete(values.First()).ConfigureAwait(false);
                Assert.Equal(AssetProvisioningState.Succeeded, status);
            }
            else
            {
                var componentContainer = await PollHelper.PollGetUntilComplete(
                    () => ComponentContainerController.Get(
                    Fixture.SubscriptionId,
                    Fixture.ResourceGroupName,
                    Fixture.RegistryName,
                    name)).ConfigureAwait(false);
                Assert.Equal(AssetProvisioningState.Succeeded, componentContainer.Properties.ProvisioningState);
            }
        }

        private async Task TestUpdateComponentContainer(string name, Resource<ComponentContainer> componentContainer)
        {
            var response = await ComponentContainerController.CreateOrUpdate(
                Fixture.SubscriptionId,
                Fixture.ResourceGroupName,
                Fixture.RegistryName,
                name,
                componentContainer).ConfigureAwait(false);

            Assert.True(response.Headers.TryGetValues(MfeConstants.AzureAsyncOperation, out var values));
            var status = await PollHelper.PollAsyncOperationUntilComplete(values.First()).ConfigureAwait(false);
            Assert.Equal(AssetProvisioningState.Succeeded, status);
        }

        private async Task<string> CreateComponentCode(string name, string version)
        {
            var snapshotFeedAssetId = new AssetId(
                region: null,
                assetContainerId: Fixture.RegistryName,
                type: AssetType.Codes,
                name: name,
                version: version,
                resourceType: AssetContainerType.Feed);

            var temporaryDataReferenceResponse = await TemporaryDataReferenceController.CreateOrGetTemporaryDataReference(
                Fixture.SubscriptionId,
                Fixture.ResourceGroupName,
                Fixture.RegistryName,
                name,
                version,
                new Contracts.V20211001Dataplane.Feed.TemporaryDataReferenceRequestDto { AssetId = snapshotFeedAssetId, TemporaryDataReferenceType = TemporaryDataReferenceType.TemporaryBlobReference }).ConfigureAwait(false);

            var feedCredentials = new DataReferenceCredentialDto()
            {
                CredentialType = temporaryDataReferenceResponse.BlobReferenceForConsumption.Credential.CredentialType.ToString(),
                ExtensionData = temporaryDataReferenceResponse.BlobReferenceForConsumption.Credential.ExtensionData
            };

            var containerSas = SASCredentialDto.ConvertFromDataReferenceCredentialDtoOrNull(feedCredentials).SASUri;
            var containerReference = new CloudBlobContainer(containerSas);

            var cloudBlockBlob = containerReference.GetBlockBlobReference($"hello.py");
            await cloudBlockBlob.UploadTextAsync("print(\"hello\")").ConfigureAwait(false);

            string codeUri = temporaryDataReferenceResponse.BlobReferenceForConsumption.BlobUri.ToString();
            await CodeVersionController.CreateOrUpdate(
                Fixture.SubscriptionId,
                Fixture.ResourceGroupName,
                Fixture.RegistryName,
                name,
                version,
                new Resource<CodeVersion>()
                {
                    Properties = new CodeVersion
                    {
                        CodeUri = codeUri,
                    }
                }).ConfigureAwait(false);

            await PollHelper.PollGetUntilComplete(
                () => CodeVersionController.Get(
                Fixture.SubscriptionId,
                Fixture.ResourceGroupName,
                Fixture.RegistryName,
                name,
                version)).ConfigureAwait(false);

            return codeUri;
        }

        private Resource<ComponentVersion> ConvertToComponentVersionResource(Resource<ComponentVersion> componentVersion, string name, string version, SystemData systemData = null)
        {
            var componentVersionResource = new Resource<ComponentVersion>()
            {
                Id = $"/subscriptions/{Fixture.SubscriptionId}/resourceGroups/{Fixture.ResourceGroupName}/providers/{MfeConstants.ArmResourceProvider}/{MfeConstants.ArmTypeRegistries}/{Fixture.RegistryName}/{MfeConstants.ArmTypeComponent}/{name}/{MfeConstants.ArmTypeVersion}/{version}",
                Name = version,
                Type = MfeConstants.ArmFullTypeRegistryComponentVersion,
                Properties = componentVersion.Properties,
                SystemData = systemData ?? new SystemData()
            };
            return componentVersionResource;
        }
    }
}
