﻿// <copyright file="EnvironmentVersionControllerTests.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using FluentAssertions;
using Microsoft.Azure.Storage.Blob;
using Microsoft.MachineLearning.Common.Core;
using Microsoft.MachineLearning.Common.Testing;
using Microsoft.MachineLearning.Feed.Contracts;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20221201Preview.ArmCommon;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20221201Preview.Assets;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20221201Preview.Controllers.Registries;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Api;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.XunitExtensions;
using Xunit;

namespace Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.ControllerTests.V20221201Preview
{
    [MfeTest(TestCategory.Integration, ScenarioScope.Environments)]
    public class E2EEnvironmentVersionControllerTests : EnvironmentControllerTests<TestFixtureManagementFrontEnd>, IStaticFixture<TestFixtureManagementFrontEnd>
    {
        public E2EEnvironmentVersionControllerTests(StaticFixture<TestFixtureManagementFrontEnd> fixture)
            : base(fixture)
        {
        }
    }

    public abstract class EnvironmentControllerTests<TFixture>
        where TFixture : TestFixtureManagementFrontEnd
    {
        public EnvironmentControllerTests(TFixture fixture)
        {
            Fixture = fixture;

            EnvironmentContainerController = Fixture.ManagementFrontEndClient.GetController<IEnvironmentContainerController>(randomizeCase: false);
            EnvironmentVersionController = Fixture.ManagementFrontEndClient.GetController<IEnvironmentVersionController>(randomizeCase: false);
            TemporaryDataReferenceController = Fixture.ManagementFrontEndClient.GetControllerNoSerializerSettings<Contracts.V20211001Dataplane.Controllers.ITemporaryDataReferenceController>(randomizeCase: false);
        }

        public IEnvironmentVersionController EnvironmentVersionController { get; }

        public IEnvironmentContainerController EnvironmentContainerController { get; }

        public Contracts.V20211001Dataplane.Controllers.ITemporaryDataReferenceController TemporaryDataReferenceController { get; }

        public TFixture Fixture { get; }

        [Fact]
        public async Task TestGetContainersAndVersions()
        {
            var listEnvironmentsResult = await EnvironmentContainerController.List(
                Fixture.SubscriptionId,
                Fixture.ResourceGroupName,
                Fixture.RegistryName).ConfigureAwait(false);
            listEnvironmentsResult.Should().NotBeNull();
            listEnvironmentsResult.Value.Should().HaveCountGreaterThan(0);

            var firstContainer = listEnvironmentsResult.Value.First();

            var getEnvironmentResult = await EnvironmentContainerController.Get(
                Fixture.SubscriptionId,
                Fixture.ResourceGroupName,
                Fixture.RegistryName,
                firstContainer.Name).ConfigureAwait(false);

            // Can't check if firstContainer is equivalent to getEnvironmentResult because some fields have a default value set to timestamp when created
            getEnvironmentResult.Id.Should().BeEquivalentTo(firstContainer.Id);
            getEnvironmentResult.Name.Should().BeEquivalentTo(firstContainer.Name);
            getEnvironmentResult.Type.Should().BeEquivalentTo(firstContainer.Type);
            getEnvironmentResult.Properties.Description.Should().BeEquivalentTo(firstContainer.Properties.Description);
            getEnvironmentResult.Properties.Tags.Should().BeEquivalentTo(firstContainer.Properties.Tags);

            var listVersionsResult = await EnvironmentVersionController.List(
                Fixture.SubscriptionId,
                Fixture.ResourceGroupName,
                Fixture.RegistryName,
                firstContainer.Name).ConfigureAwait(false);
            listVersionsResult.Should().NotBeNull();
            listVersionsResult.Value.Should().HaveCountGreaterThan(0);

            var firstVersion = listVersionsResult.Value.First();

            var spec = await EnvironmentVersionController.Get(
                Fixture.SubscriptionId,
                Fixture.ResourceGroupName,
                Fixture.RegistryName,
                firstContainer.Name,
                firstVersion.Name).ConfigureAwait(false);

            // Exclude Image because Feed may have copied to multiple different ACRs
            spec.Should().BeEquivalentTo(firstVersion, options => options.Excluding(x => x.Properties.Image));
        }

        [Fact]
        public async Task TestEnvironmentVersionCreateGetUpdateListDelete()
        {
            var name = Guid.NewGuid().ToString();
            var version = Guid.NewGuid().ToString();

            var originalEnvPayload = new Resource<EnvironmentVersion>()
            {
                Properties = new EnvironmentVersion()
                {
                    CondaFile = $"{{'name': '{Guid.NewGuid()}','dependencies': ['python=3.10',{{ 'pip': ['azureml-defaults']}}]}}",
                    Image = "mcr.microsoft.com/azureml/openmpi4.1.0-ubuntu20.04",
                    Description = "originalDescription",
                    Tags = new Dictionary<string, string>()
                    {
                        { "originalKey", "originalValue" }
                    }
                },
            };

            var response = await EnvironmentVersionController.CreateOrUpdate(
                Fixture.SubscriptionId,
                Fixture.ResourceGroupName,
                Fixture.RegistryName,
                name,
                version,
                originalEnvPayload).ConfigureAwait(false);
            await PollHelper.PollAsyncOperationUntilComplete(response).ConfigureAwait(false);

            var originalEnv = await EnvironmentVersionController.Get(
                Fixture.SubscriptionId,
                Fixture.ResourceGroupName,
                Fixture.RegistryName,
                name,
                version).ConfigureAwait(false);

            // Id is the assetId and contains the name/version
            originalEnv.Id.Should().ContainAll(name, version);
            originalEnv.Properties.IsAnonymous.Should().BeFalse();
            originalEnv.SystemData.CreatedAt.Should().BeAfter(DateTime.Now.AddMinutes(-15));
            originalEnv.SystemData.LastModifiedAt.Should().BeAfter(DateTime.Now.AddMinutes(-15));
            originalEnv.Properties.Tags.Should().BeEquivalentTo(originalEnvPayload.Properties.Tags);
            originalEnv.Properties.Description.Should().Be(originalEnvPayload.Properties.Description);

            var updatedEnvPayload = new Resource<EnvironmentVersion>()
            {
                Properties = new EnvironmentVersion()
                {
                    CondaFile = $"{{'name': '{Guid.NewGuid()}','dependencies': ['python=3.10',{{ 'pip': ['azureml-defaults']}}]}}",
                    Description = "newDescription",
                    Tags = new Dictionary<string, string>()
                    {
                        { "originalKey", "originalValue" },
                        { "newKey", "newValue" }
                    }
                },
            };
            response = await EnvironmentVersionController.CreateOrUpdate(
                Fixture.SubscriptionId,
                Fixture.ResourceGroupName,
                Fixture.RegistryName,
                name,
                version,
                updatedEnvPayload).ConfigureAwait(false);
            await PollHelper.PollAsyncOperationUntilComplete(response).ConfigureAwait(false);

            var updatedEnv = await EnvironmentVersionController.Get(
                Fixture.SubscriptionId,
                Fixture.ResourceGroupName,
                Fixture.RegistryName,
                name,
                version).ConfigureAwait(false);

            // Id is the assetId and contains the name/version
            updatedEnv.Id.Should().ContainAll(name, version);
            updatedEnv.Properties.IsAnonymous.Should().BeFalse();
            updatedEnv.SystemData.CreatedAt.Should().Be(originalEnv.SystemData.CreatedAt);
            updatedEnv.SystemData.LastModifiedAt.Should().Be(originalEnv.SystemData.LastModifiedAt);
            updatedEnv.Properties.Tags.Should().BeEquivalentTo(updatedEnvPayload.Properties.Tags);
            updatedEnv.Properties.Description.Should().Be(updatedEnvPayload.Properties.Description);

            // We query Index to get the environments and Index is EventuallyConsistent
            await EventualConsistencyTesting.RetryUntilAssertionPasses(
                async () =>
                {
                    var retrievedEnvList = await EnvironmentVersionController.List(
                        Fixture.SubscriptionId,
                        Fixture.ResourceGroupName,
                        Fixture.RegistryName,
                        name).ConfigureAwait(false);
                    retrievedEnvList.Value.Count().Should().Be(1);
                    var retrievedEnvFromList = retrievedEnvList.Value[0];
                    retrievedEnvFromList.Id.Should().ContainAll(name, version);
                    retrievedEnvFromList.Properties.IsAnonymous.Should().BeFalse();
                    retrievedEnvFromList.SystemData.CreatedAt.Should().BeAfter(DateTime.Now.AddMinutes(-15));
                    retrievedEnvFromList.SystemData.LastModifiedAt.Should().BeAfter(DateTime.Now.AddMinutes(-15));
                });
        }

        [Fact]
        public async Task TestEnvironmentVersionCreateFromBuildContext()
        {
            var name = Guid.NewGuid().ToString();
            var version = Guid.NewGuid().ToString();

            var temporaryDataReferenceResponseDto = await TemporaryDataReferenceController.CreateOrGetTemporaryDataReference(
                Fixture.SubscriptionId,
                Fixture.ResourceGroupName,
                Fixture.RegistryName,
                name,
                version,
                new Contracts.V20211001Dataplane.Feed.TemporaryDataReferenceRequestDto()
                {
                    AssetId = new AssetId(null, Fixture.RegistryName, AssetType.Environments, name, version, AssetContainerTypeOld.Feed),
                    TemporaryDataReferenceType = TemporaryDataReferenceType.TemporaryBlobReference
                }).ConfigureAwait(false);

            var feedCredentials = temporaryDataReferenceResponseDto.BlobReferenceForConsumption.Credential;
            var sasCredential = Contracts.V20211001Dataplane.Feed.SASCredentialDto.ConvertFromDataReferenceCredentialDtoOrNull(feedCredentials);
            var containerSas = sasCredential.SASUri;
            var containerReference = new CloudBlobContainer(containerSas);

            var dockerfileName = "Dockerfile";
            var cloudBlockBlob = containerReference.GetBlockBlobReference(dockerfileName);
            await cloudBlockBlob.UploadTextAsync("FROM python").ConfigureAwait(false);

            var envVersionToAdd = new Resource<EnvironmentVersion>()
            {
                Properties = new EnvironmentVersion()
                {
                    Build = new Contracts.V20221201Preview.BuildContext()
                    {
                        ContextUri = temporaryDataReferenceResponseDto.BlobReferenceForConsumption.BlobUri.ToString()
                    }
                }
            };

            var response = await EnvironmentVersionController.CreateOrUpdate(
                Fixture.SubscriptionId,
                Fixture.ResourceGroupName,
                Fixture.RegistryName,
                name,
                version,
                envVersionToAdd).ConfigureAwait(false);
            await PollHelper.PollAsyncOperationUntilComplete(response).ConfigureAwait(false);

            var retrievedEnvVersion = await EnvironmentVersionController.Get(
                Fixture.SubscriptionId,
                Fixture.ResourceGroupName,
                Fixture.RegistryName,
                name,
                version).ConfigureAwait(false);

            // Id is the assetId and contains the name/version
            retrievedEnvVersion.Id.Should().ContainAll(name, version);
        }

        [Fact]
        public async Task TestEnvironmentContainerNextVersion()
        {
            var name = Guid.NewGuid().ToString();
            var version = "1";

            var envPayload = new Resource<EnvironmentVersion>()
            {
                Properties = new EnvironmentVersion()
                {
                    CondaFile = $"{{'name': '{Guid.NewGuid()}','dependencies': ['python=3.10',{{ 'pip': ['azureml-defaults']}}]}}",
                    Image = "mcr.microsoft.com/azureml/openmpi4.1.0-ubuntu20.04",
                },
            };

            var response = await EnvironmentVersionController.CreateOrUpdate(
                Fixture.SubscriptionId,
                Fixture.ResourceGroupName,
                Fixture.RegistryName,
                name,
                version,
                envPayload).ConfigureAwait(false);
            await PollHelper.PollAsyncOperationUntilComplete(response).ConfigureAwait(false);

            var envContainer = await EnvironmentContainerController.Get(
                Fixture.SubscriptionId,
                Fixture.ResourceGroupName,
                Fixture.RegistryName,
                name).ConfigureAwait(false);
            envContainer.Properties.NextVersion.Should().Be("2");

            await EventualConsistencyTesting.RetryUntilAssertionPasses(
                async () =>
                {
                    var retrievedContainersList = await EnvironmentContainerController.List(
                        Fixture.SubscriptionId,
                        Fixture.ResourceGroupName,
                        Fixture.RegistryName).ConfigureAwait(false);
                    var retrievedContainer = retrievedContainersList.Value.First(x => x.Name == name);
                    retrievedContainer.Properties.NextVersion.Should().Be("2");
                });
        }
    }
}