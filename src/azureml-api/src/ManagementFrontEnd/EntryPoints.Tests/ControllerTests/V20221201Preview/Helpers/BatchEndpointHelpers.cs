﻿// <copyright file="BatchEndpointHelpers.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20221201Preview;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20221201Preview.ArmCommon;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20221201Preview.Assets;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20221201Preview.Controllers;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20221201Preview.Endpoints;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20221201Preview.Endpoints.BatchInference;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.ControllerTests.NonVersionedHelpers;
using Microsoft.MachineLearning.ManagementFrontEnd.Services;
using Polly;
using Polly.Retry;

namespace Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.ControllerTests.V20221201Preview.Helpers
{
    public static class BatchEndpointHelpers
    {
        private static readonly AsyncRetryPolicy RetryPolicy = Policy
                .Handle<InvalidOperationException>()
                .WaitAndRetryAsync(5, i => TimeSpan.FromSeconds(Math.Pow(4, i)));

        public static async Task<TrackedResource<BatchEndpoint>> CreateTestEndpoint(IBatchEndpointController controller, TestFixtureManagementFrontEnd fixture, Dictionary<string, string> additionalProperties = null)
        {
            var name = TestHelpers.GenerateName();
            var batchEndpoint = new BatchEndpoint()
            {
                Description = "This is a test endpoint",
                AuthMode = EndpointAuthMode.AADToken,
                Properties = additionalProperties
            };

            var testEndpoint = EndpointHelpers.GenerateTestEndpoint<BatchEndpoint>(
                name,
                batchEndpoint,
                "Microsoft.MachineLearningServices/workspaces/batchEndpoints",
                additionalProperties,
                new CaseInsensitiveDictionary() { { "api-version", "*********" } });

            await controller.CreateOrUpdate(
                fixture.SubscriptionId,
                fixture.ResourceGroupName,
                fixture.WorkspaceName,
                testEndpoint.Name,
                testEndpoint).ConfigureAwait(false);

            return await RetryPolicy.ExecuteAsync(() =>
                PollUntilEndpointTerminalState(
                    controller,
                    fixture.SubscriptionId,
                    fixture.ResourceGroupName,
                    fixture.WorkspaceName,
                    name)).ConfigureAwait(false);
        }

        public static async Task<TrackedResource<BatchDeployment>> CreateTestEndpointDeployment(
            IBatchDeploymentController controller,
            TestFixtureManagementFrontEnd fixture,
            string endpointName,
            string codeArtifactId,
            bool isAnonymousModel = false,
            Dictionary<string, string> additionalProperties = null)
        {
            var id = TestHelpers.GenerateName();
            var computeId = await ComputeHelpers.GetComputeId(fixture).ConfigureAwait(false);

            var deployment = new TrackedResource<BatchDeployment>
            {
                Location = "centraluseuap",
                Type = "Microsoft.MachineLearningServices/workspaces/batchEndpoints/deployments",
                Properties = new BatchDeployment()
                {
                    Description = "This is a test deployment",
                    Properties = additionalProperties,
                    Model = new IdAssetReference
                    {
                        // check if this stil applies to new model assetId instead of Id
                        AssetId = EndpointHelpers.GetModelId(fixture, isAnonymousModel)
                    },
                    EnvironmentId = EndpointHelpers.GetEnvironmentId(fixture, MfeTestingConstants.Sklearntestenv, "1"),
                    CodeConfiguration = new CodeConfiguration
                    {
                        CodeId = codeArtifactId,
                        ScoringScript = "score.py"
                    },
                    Compute = computeId,
                    Resources = new DeploymentResourceConfiguration { InstanceCount = 1 },
                    MaxConcurrencyPerInstance = 1,
                    EnvironmentVariables = new Dictionary<string, string>
                    {
                        { "TestVariable", "TestValue" }
                    },
                    RetrySettings = new BatchRetrySettings()
                    {
                        MaxRetries = 1,
                        Timeout = TimeSpan.FromSeconds(1)
                    }
                }
            };

            await controller.CreateOrUpdate(
                fixture.SubscriptionId,
                fixture.ResourceGroupName,
                fixture.WorkspaceName,
                endpointName,
                id,
                deployment).ConfigureAwait(false);

            return await RetryPolicy.ExecuteAsync(() =>
                PollUntilDeploymentTerminalState(
                    controller,
                    fixture.SubscriptionId,
                    fixture.ResourceGroupName,
                    fixture.WorkspaceName,
                    endpointName,
                    id)).ConfigureAwait(false);
        }

        public static async Task<TrackedResource<BatchEndpoint>> UpdateTestEndpoint(
           IBatchEndpointController controller,
           TestFixtureManagementFrontEnd fixture,
           string endpointName,
           string deploymentName,
           string updateType = "PUT",
           CaseInsensitiveDictionary tags = null)
        {
            switch (updateType)
            {
                case "PUT":
                    {
                        var batchEndpoint = new BatchEndpoint()
                        {
                            Defaults = new BatchEndpointDefaults
                            {
                                DeploymentName = deploymentName
                            },
                            AuthMode = EndpointAuthMode.AADToken
                        };

                        var testEndpoint = EndpointHelpers.GenerateTestEndpoint<BatchEndpoint>(
                            endpointName,
                            batchEndpoint,
                            "Microsoft.MachineLearningServices/workspaces/batchEndpoints",
                            additionalProperties: null,
                            tags);

                        await controller.CreateOrUpdate(
                            fixture.SubscriptionId,
                            fixture.ResourceGroupName,
                            fixture.WorkspaceName,
                            endpointName,
                            testEndpoint).ConfigureAwait(false);
                        break;
                    }
                case "PATCH":
                    {
                        var batchEndpoint = new PartialMinimalTrackedResourceWithIdentity { Tags = tags };

                        await controller.Update(
                            fixture.SubscriptionId,
                            fixture.ResourceGroupName,
                            fixture.WorkspaceName,
                            endpointName,
                            batchEndpoint).ConfigureAwait(false);
                        break;
                    }
                default: throw new Exception($"Unexpected update type {updateType} provided.");
            }

            return await RetryPolicy.ExecuteAsync(() =>
                PollUntilEndpointTerminalState(
                    controller,
                    fixture.SubscriptionId,
                    fixture.ResourceGroupName,
                    fixture.WorkspaceName,
                    endpointName)).ConfigureAwait(false);
        }

        public static async Task CleanUpEndpoints(
            IBatchEndpointController controller,
            TestFixtureManagementFrontEnd fixture)
        {
            var result = await controller.List(fixture.SubscriptionId, fixture.ResourceGroupName, fixture.WorkspaceName, 150, null).ConfigureAwait(false);
            var endpointsToClean = result.Value
                .FindAll(endpoint => endpoint.Name.StartsWith("test-mfe") &&
                     (endpoint.SystemData == null ||
                    ((TimeSpan)(DateTime.UtcNow - endpoint.SystemData.CreatedAt)).TotalMinutes > 45))
                .Select(endpoint => endpoint.Name).ToList();
            foreach (var endpoint in endpointsToClean)
            {
                try
                {
                    // Fire and forget the delete call.
#pragma warning disable CS4014 // Because this call is not awaited, execution of the current method continues before the call is completed
                    controller.Delete(
                        fixture.SubscriptionId,
                        fixture.ResourceGroupName,
                        fixture.WorkspaceName,
                        endpoint);
#pragma warning restore CS4014 // Because this call is not awaited, execution of the current method continues before the call is completed
                }
                catch (Exception e)
                {
                    Console.WriteLine($"Error while cleaning up endpoints. Message: '{e.Message}', Exception: {e.StackTrace}");
                }
            }
        }

        public static async Task<TrackedResource<BatchDeployment>> UpdateDeployment(
            IBatchDeploymentController controller,
            TestFixtureManagementFrontEnd fixture,
            string endpointName,
            string name,
            string updateType = "PATCH",
            TrackedResource<BatchDeployment> replacementResource = null,
            CaseInsensitiveDictionary tags = null,
            string updatedDescription = null)
        {
            switch (updateType)
            {
                case "PATCH":
                    {
                        var deployment = new PartialTrackedResource<PartialBatchDeployment>
                        {
                            Properties = new PartialBatchDeployment()
                            {
                                Description = updatedDescription
                            },
                            Tags = tags
                        };

                        await controller.Update(
                            fixture.SubscriptionId,
                            fixture.ResourceGroupName,
                            fixture.WorkspaceName,
                            endpointName,
                            name,
                            deployment).ConfigureAwait(false);
                        break;
                    }

                case "PUT":
                    {
                        replacementResource.Tags = tags;
                        replacementResource.Properties.Description = updatedDescription;

                        await controller.CreateOrUpdate(
                            fixture.SubscriptionId,
                            fixture.ResourceGroupName,
                            fixture.WorkspaceName,
                            endpointName,
                            name,
                            replacementResource).ConfigureAwait(false);
                        break;
                    }

                default: throw new Exception($"Unexpected update type {updateType} provided.");
            }

            return await RetryPolicy.ExecuteAsync(() =>
                PollUntilDeploymentTerminalState(
                    controller,
                    fixture.SubscriptionId,
                    fixture.ResourceGroupName,
                    fixture.WorkspaceName,
                    endpointName,
                    name)).ConfigureAwait(false);
        }

        private static async Task<TrackedResource<BatchEndpoint>> PollUntilEndpointTerminalState(
            IBatchEndpointController controller,
            Guid subscriptionId,
            string resourceGroupName,
            string workspaceName,
            string endpointName)
        {
            var result = await controller.Get(
                    subscriptionId,
                    resourceGroupName,
                    workspaceName,
                    endpointName).ConfigureAwait(false);
            return (result?.Properties?.ProvisioningState.IsTerminalState()).GetValueOrDefault() ? result : throw new InvalidOperationException();
        }

        private static async Task<TrackedResource<BatchDeployment>> PollUntilDeploymentTerminalState(
            IBatchDeploymentController controller,
            Guid subscriptionId,
            string resourceGroupName,
            string workspaceName,
            string endpointName,
            string deploymentName)
        {
            var result = await controller.Get(
                    subscriptionId,
                    resourceGroupName,
                    workspaceName,
                    endpointName,
                    deploymentName).ConfigureAwait(false);
            return (result?.Properties?.ProvisioningState.IsTerminalState()).GetValueOrDefault() ? result : throw new InvalidOperationException();
        }
    }
}
