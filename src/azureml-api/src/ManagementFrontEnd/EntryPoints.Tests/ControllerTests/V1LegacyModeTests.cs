﻿// <copyright file="V1LegacyModeTests.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Reflection;
using System.Threading.Tasks;
using FluentAssertions;
using Microsoft.IdentityModel.S2S.Tokens;
using Microsoft.MachineLearning.Common.Core;
using Microsoft.MachineLearning.Common.Testing;
using Microsoft.MachineLearning.Common.WebApi.Client;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.Errors;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Api;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Api.Filters;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.XunitExtensions;
using Polly;
using Xunit;
using static Microsoft.MachineLearning.Common.WebApi.Client.ServiceInvoker;

namespace Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.ControllerTests
{
    [MfeTest(TestCategory.Component, ScenarioScope.MfeCommon)]
    public class V1LegacyModeTests : IStaticFixture<TestFixtureV1LegacyModeManagementFrontEnd>
    {
        private readonly TestFixtureV1LegacyModeManagementFrontEnd _fixture;

        public V1LegacyModeTests(StaticFixture<TestFixtureV1LegacyModeManagementFrontEnd> fixture)
        {
            _fixture = fixture;
        }

        public static IEnumerable<object[]> ControllerInterfaceTypesWithList => MfeAssemblies.Contracts.DefinedTypes
            .Where(x =>
            {
                var attr = x.GetCustomAttribute<InterfaceRoutePrefixAttribute>();
                return attr != null && !attr.Template.Contains("/registries/", StringComparison.OrdinalIgnoreCase);
            })
            .Where(x => x.GetCustomAttribute<ApiVersionParameterAttribute>(true)?.Value != MfeApiVersions.Oct2021DataplanePreview.ToSerializedValue())
            .Where(x => x.GetMethod("List") != null)
            .Where(x => x.GetMethod("List").GetParameters().Count(p => !p.HasDefaultValue) == 3) // Subscription, RG name, WS name.
            // Exclude controllers that have been excluded from the V1LegacyMode check
            .Where(x => !V1LegacyModeCheckActionFilter.GetIsMethodExcludedFromV1LegacyModeCheck(x.GetMethod("List")))
            .Select(x => new[] { x })
            .ToList();

        public static IEnumerable<object[]> OperationControllerInterfaceTypes => MfeAssemblies.Contracts.DefinedTypes
            .Where(x => x.GetCustomAttribute(typeof(InterfaceRoutePrefixAttribute)) != null)
            .Where(x => x.GetCustomAttribute<ApiVersionParameterAttribute>(true)?.Value != MfeApiVersions.Oct2021DataplanePreview.ToSerializedValue())
            .Where(x => x.GetMethod("PollOperationStatus") != null
                && x.GetMethod("PollOperationResult") != null)
            .Select(x => new[] { x })
            .ToList();

        /// <summary>
        /// Test that MFE rejects requests to the different controllers in each API version
        /// for a workspace with the V1LegacyMode flag set to True.
        ///
        /// We should be able to call List() on any workspace-level resource type without
        /// needing extra per-resource knowledge like what type to use for the request body, etc.
        /// This means we can get some test coverage of routing and deserialization automatically.
        /// </summary>
        [Theory(Skip = "WIP fix https://msdata.visualstudio.com/Vienna/_workitems/edit/2490108")]
        [MemberData(nameof(ControllerInterfaceTypesWithList))]
        public async Task TestMfeRejectsRequestsToWorkspaceWithV1LegacyModeEnabled(Type controllerType)
        {
            var attr = controllerType.GetCustomAttribute(typeof(InterfaceRoutePrefixAttribute));
            var listMethod = controllerType.GetMethod("List");
            var controller = _fixture.ManagementFrontEndClient.GetController(controllerType);

            var parameters = new object[] { _fixture.SubscriptionId, _fixture.ResourceGroupName, _fixture.WorkspaceName }
                .Concat(Enumerable.Repeat(Type.Missing, listMethod.GetParameters().Length - 3))
                .ToArray();

            Func<Task> call = () => (Task)listMethod.Invoke(controller, parameters);
            (await call.Should().ThrowAsync<ServiceInvocationException>()).Which.Should().Match<ServiceInvocationException>(
                e =>
                    e.Response.StatusCode.Equals(HttpStatusCode.BadRequest)
                    && e.ErrorResponse != null
                    && e.ErrorResponse.Error.Message != null
                    && e.ErrorResponse.Error.Message.Equals(new WorkspaceV1LegacyModeEnabledError().MessageFormat, StringComparison.OrdinalIgnoreCase),
                "MFE should reject all workspace-scoped requests for workspaces with the V1LegacyMode flag enabled with a 400 UserError");
        }

        /// <summary>
        /// Test that MFE returns 404 when polling operation status/result for a workspace id
        /// that doesn't exist.
        /// </summary>
        [Theory]
        [MemberData(nameof(OperationControllerInterfaceTypes))]
        public async Task TestMfeOperationControllerReturns404ForMissingWorkspace(Type controllerType)
        {
            // ServiceInvoker does not currently support generic service methods, such as the ones
            // defined in the MFE OperationController. As such, attempting to use the fixture's
            // ManagementFrontEndClient.GetController method to get the OperationController client
            // will fail. Therefore, we need to call it directly with the underlying HttpClient.
            // Consider refactoring to use ServiceInvoker once it supports generic service methods.
            var apiVersionAttribute = controllerType.GetCustomAttributes<ApiVersionParameterAttribute>().FirstOrDefault();
            apiVersionAttribute.Should().NotBeNull();
            var pollOperationStatusMethod = controllerType.GetMethod("PollOperationStatus");
            pollOperationStatusMethod.Should().NotBeNull();
            var methodHttpGetAttribute = pollOperationStatusMethod.GetCustomAttributes<HttpGetAttribute>().FirstOrDefault();
            methodHttpGetAttribute.Should().NotBeNull();
            var methodDetails = new MethodDetails(pollOperationStatusMethod, HttpVerbs.Get, methodHttpGetAttribute.Template, controllerType);
            var route = string.Format(
                $"{methodDetails.Route}?{apiVersionAttribute.Name}={apiVersionAttribute.Value}",
                _fixture.SubscriptionId,
                "centraluseuap",
                $"test:{Guid.Empty}:some-stuff");
            var requestUri = new Uri(route, UriKind.Relative);
            var request = new HttpRequestMessage(HttpMethod.Get, requestUri);

            // Reduce flakiness by retrying on exception
            var retryPolicy = Policy
                .Handle<Exception>()
                .WaitAndRetryAsync(3, i => TimeSpan.FromSeconds(Math.Pow(2, i)));
            var response = await retryPolicy.ExecuteAsync(() => _fixture.HttpClient.SendAsync(request)).ConfigureAwait(false);

            response.IsSuccessStatusCode.Should().BeFalse();
            response.StatusCode.Should().Be(HttpStatusCode.NotFound);
        }
    }
}
