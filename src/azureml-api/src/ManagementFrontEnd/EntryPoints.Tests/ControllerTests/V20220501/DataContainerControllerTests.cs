﻿// <copyright file="DataContainerControllerTests.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using FluentAssertions;
using Microsoft.Extensions.Options;
using Microsoft.MachineLearning.Artifact.Contracts;
using Microsoft.MachineLearning.Common.Core;
using Microsoft.MachineLearning.Common.Core.Contracts;
using Microsoft.MachineLearning.Common.Testing;
using Microsoft.MachineLearning.Common.WebApi.Client;
using Microsoft.MachineLearning.Common.WebApi.TeamAccounts;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20220501.ArmCommon;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20220501.Assets;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20220501.Controllers;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Api;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.ControllerTests.NonVersionedHelpers;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.ControllerTests.V20220501.Helpers;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.XunitExtensions;

using Xunit;

namespace Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.ControllerTests.V20220501;

[MfeTest(TestCategory.Integration, ScenarioScope.Data)]
public class E2EDataContainerControllerTests : DataContainerControllerTests<TestFixtureManagementFrontEnd>, IStaticFixture<TestFixtureManagementFrontEnd>
{
    public E2EDataContainerControllerTests(StaticFixture<TestFixtureManagementFrontEnd> fixture)
        : base(fixture, ServiceInvoker.Create<IArtifactControllerWorkspaceV2Routes>(fixture.Instance.HttpClient, new Uri(fixture.Instance.GetHostService<IOptions<ExperimentationHostConfiguration>>().Value.ExperimentationHostUri)))
    {
    }
}

public abstract class DataContainerControllerTests<TFixture>
    where TFixture : TestFixtureManagementFrontEnd
{
    private const int _maxListCount = 500;
    private readonly DataAssetHelpers _dataAssetHelpers;

    public DataContainerControllerTests(TFixture fixture, IArtifactControllerWorkspaceV2Routes artifactController)
    {
        Fixture = fixture;
        _dataAssetHelpers = new DataAssetHelpers(new WorkspaceContext2(Fixture.WorkspaceId, Fixture.WorkspaceName, Fixture.ResourceGroupName, Fixture.SubscriptionId, "test", "test", new Dictionary<string, string>()), artifactController);
        DataContainerController = Fixture.ManagementFrontEndClient.GetController<IDataContainerController>();
    }

    public IDataContainerController DataContainerController { get; }

    public TFixture Fixture { get; }

    [Fact]
    public async Task TestCreateOrUpdate()
    {
        string dataContainerName = Guid.NewGuid().ToString();
        var creationDataContainer = _dataAssetHelpers.MakeDataContainerRequest(dataContainerName);

        var dataContainerCreated = await DataContainerController.CreateOrUpdate(
                                                            Fixture.SubscriptionId,
                                                            Fixture.ResourceGroupName,
                                                            Fixture.WorkspaceName,
                                                            creationDataContainer.Name,
                                                            creationDataContainer);
        var dataContainer = await GetDataContainer(dataContainerName).ConfigureAwait(false);
        _dataAssetHelpers.CheckCreatedDataContainer(creationDataContainer, dataContainer);

        // creating the same container should return without conflict
        dataContainer = await DataContainerController.CreateOrUpdate(
                                                    Fixture.SubscriptionId,
                                                    Fixture.ResourceGroupName,
                                                    Fixture.WorkspaceName,
                                                    creationDataContainer.Name,
                                                    creationDataContainer);
        var sameDataContainers = await GetDataContainer(dataContainerName).ConfigureAwait(false);
        _dataAssetHelpers.CheckCreatedDataContainer(creationDataContainer, sameDataContainers);

        // calling CreateOrUpdate the with updated details should update the container
        creationDataContainer.Properties.Description = "new_descp_same_container";
        var updatedDataContainers = await DataContainerController.CreateOrUpdate(
                                                    Fixture.SubscriptionId,
                                                    Fixture.ResourceGroupName,
                                                    Fixture.WorkspaceName,
                                                    creationDataContainer.Name,
                                                    creationDataContainer);
        _dataAssetHelpers.CheckCreatedDataContainer(creationDataContainer, updatedDataContainers);
    }

    // Test deleting
    [Fact]
    public async Task TestDeleteContainer()
    {
        string dataContainerName = Guid.NewGuid().ToString();
        var creationDataContainer = _dataAssetHelpers.MakeDataContainerRequest(dataContainerName);
        var dataContainerCreated = await DataContainerController.CreateOrUpdate(
                                                            Fixture.SubscriptionId,
                                                            Fixture.ResourceGroupName,
                                                            Fixture.WorkspaceName,
                                                            creationDataContainer.Name,
                                                            creationDataContainer);
        var deleteTask = async () => await DataContainerController.Delete(
                                                            Fixture.SubscriptionId,
                                                            Fixture.ResourceGroupName,
                                                            Fixture.WorkspaceName,
                                                            creationDataContainer.Name);
        await deleteTask.Should().ThrowAsync<ServiceInvocationException>()
            .Where(e => e.Message.Contains("DataContainers_Workspace_Delete is not supported", StringComparison.InvariantCultureIgnoreCase))
            .ConfigureAwait(false);
    }

    [Fact]
    public async Task TestUpdateInvalidChanges()
    {
        string dataContainerName = Guid.NewGuid().ToString();
        var creationDataContainer = _dataAssetHelpers.MakeDataContainerRequest(dataContainerName, DataType.uri_file);

        var dataContainerCreated = await DataContainerController.CreateOrUpdate(
                                                            Fixture.SubscriptionId,
                                                            Fixture.ResourceGroupName,
                                                            Fixture.WorkspaceName,
                                                            creationDataContainer.Name,
                                                            creationDataContainer);
        var dataContainer = await GetDataContainer(dataContainerName).ConfigureAwait(false);
        _dataAssetHelpers.CheckCreatedDataContainer(creationDataContainer, dataContainer);

        // calling CreateOrUpdate the with updated details that are immutable
        creationDataContainer.Properties.DataType = DataType.mltable;
        var updateTask = async () => await DataContainerController.CreateOrUpdate(
                                                    Fixture.SubscriptionId,
                                                    Fixture.ResourceGroupName,
                                                    Fixture.WorkspaceName,
                                                    creationDataContainer.Name,
                                                    creationDataContainer);

        await updateTask.Should().ThrowAsync<ServiceInvocationException>()
            .Where(e => e.Response.StatusCode == System.Net.HttpStatusCode.BadRequest)
            .Where(e => e.Message.Contains("data container", StringComparison.InvariantCultureIgnoreCase))
            .Where(e => e.Message.Contains("the existing asset's data type cannot be changed. Only tags and description can be updated.", StringComparison.InvariantCultureIgnoreCase))
            .ConfigureAwait(false);
    }

    [Fact]
    public async Task TestListScenarios()
    {
        string dataContainerName = Guid.NewGuid().ToString();
        var creationDataContainer = _dataAssetHelpers.MakeDataContainerRequest(dataContainerName);
        var dataContainer = await DataContainerController.CreateOrUpdate(
                                                            Fixture.SubscriptionId,
                                                            Fixture.ResourceGroupName,
                                                            Fixture.WorkspaceName,
                                                            creationDataContainer.Name,
                                                            creationDataContainer);

        await EventualConsistencyTesting.RetryUntilAssertionPasses(async () =>
        {
            var dataContainers = await ListDataContainer(null).ConfigureAwait(false);
            dataContainers.Should().HaveCountGreaterThan(0);
        }).ConfigureAwait(false);
    }

    protected async Task<Resource<DataContainer>> GetDataContainer(string containerName)
    {
        return await DataContainerController.Get(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, containerName);
    }

    protected async Task<List<Resource<DataContainer>>> ListDataContainer(string continuationToken = null)
    {
        return await ControllerHelpers.List(skipToken => DataContainerController.List(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, continuationToken), _maxListCount).ToListAsync();
    }
}
