﻿// <copyright file="JobControllerTests.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading;
using System.Threading.Tasks;
using FluentAssertions;
using FluentAssertions.Equivalency;
using FluentAssertions.Extensions;
using Microsoft.Extensions.Options;
using Microsoft.MachineLearning.Artifact.Contracts;
using Microsoft.MachineLearning.Common.Core;
using Microsoft.MachineLearning.Common.Testing;
using Microsoft.MachineLearning.Common.WebApi.Client;
using Microsoft.MachineLearning.Common.WebApi.TeamAccounts;
using Microsoft.MachineLearning.DataStore.Contracts;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20220501;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20220501.ArmCommon;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20220501.Assets;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20220501.Controllers;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20220501.Jobs;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Api;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Api.V20220501;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.ControllerTests.NonVersionedHelpers;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.ControllerTests.V20220501.Helpers;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.Extensions;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.Fuzzing;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.XunitExtensions;
using Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts.Common;
using Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Services.MfeResourceArm;
using Microsoft.MachineLearning.ManagementFrontEnd.Services.Converters;
using Microsoft.MachineLearning.RunHistory.Contracts;
using Newtonsoft.Json.Linq;
using Xunit;
using static Microsoft.MachineLearning.Common.Testing.EventualConsistencyTesting;
using JobBase = Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20220501.JobBase;
using ListViewType = Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20220501.Assets.ListViewType;

namespace Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.ControllerTests.V20220501
{
    public class E2EJobControllerTests : JobControllerTests<TestFixtureManagementFrontEnd>, IStaticFixture<TestFixtureManagementFrontEnd>
    {
        private const int _maxListCount = 20;
        private Resource<JobBase> _originalRequest = new Resource<JobBase>();

        public E2EJobControllerTests(StaticFixture<TestFixtureManagementFrontEnd> fixture)
            : base(fixture, ServiceInvoker.Create<IArtifactControllerWorkspaceV2Routes>(fixture.Instance.HttpClient, new Uri(fixture.Instance.GetHostService<IOptions<ExperimentationHostConfiguration>>().Value.ExperimentationHostUri)))
        {
        }

        public static IEnumerable<object[]> JobResourceConfigurationAndDataTestCases()
        {
            yield return new object[] { new ResourceConfiguration { InstanceCount = 1 }, true };
            yield return new object[] { new ResourceConfiguration { InstanceCount = 1 }, false };
            yield return new object[] { new ResourceConfiguration { InstanceCount = 1, Properties = new Dictionary<string, JObject>() { { MfeConstants.ClusterlessJobComputeKey, new JObject { [MfeConstants.ClusterlessJobComputeProperty] = true } } } }, false };
        }

        public static IEnumerable<object[]> JobResourceConfigurationTestCases()
        {
            yield return new object[] { new ResourceConfiguration { InstanceCount = 1, Properties = new Dictionary<string, JObject>() { { MfeConstants.ClusterlessJobComputeKey, new JObject { [MfeConstants.ClusterlessJobComputeProperty] = true } } } } };
        }

        public static IEnumerable<object[]> CommandJobInputUriFileTestingMatrix()
        {
            var pathTypes = new List<string>()
            {
                "uriFileJobInputArmScopePath",
                "uriFileJobInputDatastorePath",
                "uriFileJobInputExternalPath",
                "uriFileJobInputDataArmId"
            };
            var pathTypeReadCommandTuples = new List<(string PathType, string ReadCommand)>();
            foreach (var pathType in pathTypes)
            {
                string readCommand = "echo hello world; " +
                    "echo --inputPath: ${{inputs." + pathType + "}}; " +
                    "head -n 1 ${{inputs." + pathType + "}}; " +
                    "echo; ";
                pathTypeReadCommandTuples.Add((pathType, readCommand));
            }

            // ReadOnlyMount
            foreach (var (pathType, readCommand) in pathTypeReadCommandTuples)
            {
                yield return new object[] { InputDeliveryMode.ReadOnlyMount, pathType, readCommand };
            }

            // ReadWriteMount
            foreach (var (pathType, readCommand) in pathTypeReadCommandTuples)
            {
                // TODO (pelong): figure out if this is expected
                // uriFileJobInputExternalPath and uriFileJobInputDataArmId does not support write even in ReadWriteMount
                if (pathType == "uriFileJobInputExternalPath" || pathType == "uriFileJobInputDataArmId")
                {
                    continue;
                }
                yield return new object[] { InputDeliveryMode.ReadWriteMount, pathType, readCommand };
            }

            // Download
            foreach (var (pathType, readCommand) in pathTypeReadCommandTuples)
            {
                yield return new object[] { InputDeliveryMode.Download, pathType, readCommand };
            }

            // Direct
            foreach (var (pathType, readCommand) in pathTypeReadCommandTuples)
            {
                yield return new object[] { InputDeliveryMode.Direct, pathType, readCommand };
            }

            // JobInputUriFile does not support EvalMount and EvalDownload
        }

        public static IEnumerable<object[]> CommandJobInputUriFolderTestingMatrix()
        {
            var pathTypes = new List<string>()
            {
                "uriFolderJobInputArmScopePath",
                "uriFolderJobInputDatastorePath",
                "uriFolderJobInputExternalPath",
                "uriFolderJobInputDataArmId"
            };
            var pathTypeReadCommandTuples = new List<(string PathType, string ReadCommand)>();
            foreach (var pathType in pathTypes)
            {
                string readCommand = "echo hello world; " +
                    "ls ${{inputs." + pathType + "}};" +
                    "echo --inputPath: ${{inputs." + pathType + "}}/file.txt; " +
                    "head -n 1 ${{inputs." + pathType + "}}/file.txt; " +
                    "echo; ";
                pathTypeReadCommandTuples.Add((pathType, readCommand));
            }

            // ReadOnlyMount
            foreach (var (pathType, readCommand) in pathTypeReadCommandTuples)
            {
                yield return new object[] { InputDeliveryMode.ReadOnlyMount, pathType, readCommand };
            }

            // ReadWriteMount
            foreach (var (pathType, readCommand) in pathTypeReadCommandTuples)
            {
                yield return new object[] { InputDeliveryMode.ReadWriteMount, pathType, readCommand };
            }

            // Download
            foreach (var (pathType, readCommand) in pathTypeReadCommandTuples)
            {
                // TODO: Check to see why not supported
                if (pathType == "uriFolderJobInputDataArmId" || pathType == "uriFolderJobInputExternalPath")
                {
                    continue;
                }
                yield return new object[] { InputDeliveryMode.Download, pathType, readCommand };
            }

            // Direct
            foreach (var (pathType, readCommand) in pathTypeReadCommandTuples)
            {
                yield return new object[] { InputDeliveryMode.Direct, pathType, readCommand };
            }

            // JobInputUriFolder does not support EvalMount and EvalDownload
        }

        public static IEnumerable<object[]> CommandJobInputMLTableTestingMatrix()
        {
            var pathTypes = new List<string>()
            {
                "mLTableJobInputArmScopePath",
                "mLTableJobInputDatastorePath",
                "mLTableJobInputExternalPath",
            };
            var pathTypeReadCommandTuples = new List<(string PathType, string ReadCommand)>();
            foreach (var pathType in pathTypes)
            {
                string readCommand = "echo hello world; " +
                    "ls ${{inputs." + pathType + "}};" +
                    "echo --inputPath: ${{inputs." + pathType + "}}/file.txt; " +
                    "head -n 1 ${{inputs." + pathType + "}}/file.txt; " +
                    "echo; ";
                pathTypeReadCommandTuples.Add((pathType, readCommand));
            }

            // ReadOnlyMount
            foreach (var (pathType, readCommand) in pathTypeReadCommandTuples)
            {
                yield return new object[] { InputDeliveryMode.ReadOnlyMount, pathType, readCommand };
            }

            // ReadWriteMount
            foreach (var (pathType, readCommand) in pathTypeReadCommandTuples)
            {
                yield return new object[] { InputDeliveryMode.ReadWriteMount, pathType, readCommand };
            }

            // Download
            foreach (var (pathType, readCommand) in pathTypeReadCommandTuples)
            {
                // TODO: check to see if not supported
                if (pathType == "mLTableJobInputExternalPath")
                {
                    continue;
                }
                yield return new object[] { InputDeliveryMode.Download, pathType, readCommand };
            }

            // Direct
            foreach (var (pathType, readCommand) in pathTypeReadCommandTuples)
            {
                yield return new object[] { InputDeliveryMode.Direct, pathType, readCommand };
            }

            // TODO: EvalMount and EvalDownload are failing
            ////// EvalMount
            ////foreach (var (pathType, readCommand) in pathTypeReadCommandTuples)
            ////{
            ////    yield return new object[] { InputDeliveryMode.EvalMount, pathType, readCommand };
            ////}

            ////// EvalDownload
            ////foreach (var (pathType, readCommand) in pathTypeReadCommandTuples)
            ////{
            ////    yield return new object[] { InputDeliveryMode.EvalDownload, pathType, readCommand };
            ////}
        }

        public static IEnumerable<object[]> CommandJobInputMLFlowModelTestingMatrix()
        {
            var pathTypes = new List<string>()
            {
                "mLFlowModelJobInputArmScopePath",
                "mLFlowModelJobInputDatastorePath",
            };
            var pathTypeReadCommandTuples = new List<(string PathType, string ReadCommand)>();
            foreach (var pathType in pathTypes)
            {
                string readCommand = "echo hello world; " +
                    "ls ${{inputs." + pathType + "}};" +
                    "echo --inputPath: ${{inputs." + pathType + "}}/file.txt; " +
                    "head -n 1 ${{inputs." + pathType + "}}/file.txt; " +
                    "echo; ";
                pathTypeReadCommandTuples.Add((pathType, readCommand));
            }

            // ReadOnlyMount
            foreach (var (pathType, readCommand) in pathTypeReadCommandTuples)
            {
                yield return new object[] { InputDeliveryMode.ReadOnlyMount, pathType, readCommand };
            }

            // ReadWriteMount
            foreach (var (pathType, readCommand) in pathTypeReadCommandTuples)
            {
                yield return new object[] { InputDeliveryMode.ReadWriteMount, pathType, readCommand };
            }

            // Download
            foreach (var (pathType, readCommand) in pathTypeReadCommandTuples)
            {
                // TODO: check to see if not supported
                if (pathType == "mLTableJobInputExternalPath")
                {
                    continue;
                }
                yield return new object[] { InputDeliveryMode.Download, pathType, readCommand };
            }

            // Direct
            foreach (var (pathType, readCommand) in pathTypeReadCommandTuples)
            {
                yield return new object[] { InputDeliveryMode.Direct, pathType, readCommand };
            }
        }

        public static IEnumerable<object[]> CommandJobOutputUriFileTestingMatrix()
        {
            var uniqueSuffix = $"-{DateTime.UtcNow:yyyyMMddHHmmss}-{Guid.NewGuid().ToString()[..4]}";
            var pathTypes = new List<string>()
            {
                "uriFileJobOutputDatastorePath",
                // TODO: ArmScopePath and Data ARM Id are not supported
                ////"uriFileJobOutputDataArmId",
                ////"uriFileJobOutputArmScopePath",
            };
            var pathTypeReadCommandWriteCommandTuples = new List<(string PathType, string ReadCommand, string WriteCommand)>();
            foreach (var pathType in pathTypes)
            {
                string readCommand = "echo hello world; " +
                    "echo --outputPath: ${{outputs." + pathType + "}}; " +
                    "head -n 1 ${{outputs." + pathType + "}}; " +
                    "echo; ";
                string writeCommand = "echo some new text" + uniqueSuffix + "> ${{outputs." + pathType + "}}; " +
                    $"echo wrote some new text{uniqueSuffix}; " +
                    "head -n 1 ${{outputs." + pathType + "}}; " +
                    "echo; ";
                pathTypeReadCommandWriteCommandTuples.Add((pathType, readCommand, writeCommand));
            }

            // ReadWriteMount
            foreach (var (pathType, readCommand, writeCommand) in pathTypeReadCommandWriteCommandTuples)
            {
                yield return new object[] { OutputDeliveryMode.ReadWriteMount, pathType, readCommand + writeCommand };
            }

            // Upload
            foreach (var (pathType, readCommand, writeCommand) in pathTypeReadCommandWriteCommandTuples)
            {
                yield return new object[] { OutputDeliveryMode.Upload, pathType, readCommand + writeCommand };
            }
        }

        public static IEnumerable<object[]> CommandJobOutputUriFolderTestingMatrix()
        {
            var uniqueSuffix = $"-{DateTime.UtcNow:yyyyMMddHHmmss}-{Guid.NewGuid().ToString()[..4]}";
            var pathTypes = new List<string>()
            {
                "uriFolderJobOutputDatastorePath",
                // TODO: ArmScopePath and Data ARM Id are not supported
                ////"uriFolderJobOutputArmScopePath",
                ////"uriFolderJobOutputDataArmId"
            };
            var pathTypeReadCommandWriteCommandTuples = new List<(string PathType, string ReadCommand, string WriteCommand)>();
            foreach (var pathType in pathTypes)
            {
                string readCommand = "echo hello world; " +
                    "ls ${{outputs." + pathType + "}};" +
                    "echo --outputPath: ${{outputs." + pathType + "}}/file.txt; " +
                    "head -n 1 ${{outputs." + pathType + "}}/file.txt; " +
                    "echo; ";
                string writeCommand = "echo some new text" + uniqueSuffix + "> ${{outputs." + pathType + "}}/file.txt; " +
                    $"echo wrote some new text{uniqueSuffix}; " +
                    "head -n 1 ${{outputs." + pathType + "}}/file.txt; " +
                    "echo; ";
                pathTypeReadCommandWriteCommandTuples.Add((pathType, readCommand, writeCommand));
            }

            // ReadWriteMount
            foreach (var (pathType, readCommand, writeCommand) in pathTypeReadCommandWriteCommandTuples)
            {
                yield return new object[] { OutputDeliveryMode.ReadWriteMount, pathType, readCommand + writeCommand };
            }

            // Upload
            foreach (var (pathType, readCommand, writeCommand) in pathTypeReadCommandWriteCommandTuples)
            {
                yield return new object[] { OutputDeliveryMode.Upload, pathType, readCommand + writeCommand };
            }
        }

        public static IEnumerable<object[]> CommandJobOutputMLTableTestingMatrix()
        {
            var uniqueSuffix = $"-{DateTime.UtcNow:yyyyMMddHHmmss}-{Guid.NewGuid().ToString()[..4]}";
            var pathTypes = new List<string>()
            {
                "mLTableJobOutputDatastorePath",
                // TODO: ArmScopePath is not supported
                ////"mLTableJobOutputArmScopePath",
            };
            var pathTypeReadCommandWriteCommandTuples = new List<(string PathType, string ReadCommand, string WriteCommand)>();
            foreach (var pathType in pathTypes)
            {
                string readCommand = "echo hello world; " +
                    "ls ${{outputs." + pathType + "}};" +
                    "echo --outputPath: ${{outputs." + pathType + "}}/file.txt; " +
                    "head -n 1 ${{outputs." + pathType + "}}/file.txt; " +
                    "echo; ";
                string writeCommand = "echo some new text" + uniqueSuffix + "> ${{outputs." + pathType + "}}/file.txt; " +
                    $"echo wrote some new text{uniqueSuffix}; " +
                    "head -n 1 ${{outputs." + pathType + "}}/file.txt; " +
                    "echo; ";
                pathTypeReadCommandWriteCommandTuples.Add((pathType, readCommand, writeCommand));
            }

            // ReadWriteMount
            foreach (var (pathType, readCommand, writeCommand) in pathTypeReadCommandWriteCommandTuples)
            {
                yield return new object[] { OutputDeliveryMode.ReadWriteMount, pathType, readCommand + writeCommand };
            }

            // Upload
            foreach (var (pathType, readCommand, writeCommand) in pathTypeReadCommandWriteCommandTuples)
            {
                yield return new object[] { OutputDeliveryMode.Upload, pathType, readCommand + writeCommand };
            }
        }

        public static IEnumerable<object[]> CommandJobOutputMLFlowModelTestingMatrix()
        {
            var pathTypes = new List<string>()
            {
                "mLFlowModelJobOutputDatastorePath",
                // TODO: ArmScopePath is not supported
                ////"mLFlowModelJobOutputArmScopePath",
            };
            var pathTypeReadCommandWriteCommandTuples = new List<(string PathType, string ReadCommand)>();
            foreach (var pathType in pathTypes)
            {
                string readCommand = "echo hello world; " +
                    "ls ${{outputs." + pathType + "}};" +
                    "echo --outputPath: ${{outputs." + pathType + "}}/file.txt; " +
                    "head -n 1 ${{outputs." + pathType + "}}/file.txt; " +
                    "echo; ";
                pathTypeReadCommandWriteCommandTuples.Add((pathType, readCommand));
            }

            // ReadWriteMount
            foreach (var (pathType, readCommand) in pathTypeReadCommandWriteCommandTuples)
            {
                yield return new object[] { OutputDeliveryMode.ReadWriteMount, pathType, readCommand };
            }

            // Upload
            foreach (var (pathType, readCommand) in pathTypeReadCommandWriteCommandTuples)
            {
                yield return new object[] { OutputDeliveryMode.Upload, pathType, readCommand };
            }
        }

        #region SweepJob E2E
        [Theory]
        [InlineData(true)]
        [InlineData(false)]
        [MfeTest(TestCategory.Integration, ScenarioScope.Sweep)]
        public async Task TestSweepJobE2E(bool useInput)
        {
            var experimentName = "TestSweepJobE2E";
            var input = CreateLiteralJobInput()
                                .CreateMerged(CreateUriFileJobInput(Fixture))
                                .CreateMerged(CreateUriFolderJobInput(Fixture))
                                .CreateMerged(await CreateArmIdJobInput().ConfigureAwait(false));
            var jobInput = useInput ? input : null;
            var request = await SweepJobHelpers.GetTestSweepJob(Fixture, string.Empty, experimentName, jobInput).ConfigureAwait(false);
            var jobName = TestHelpers.GenerateName();

            var createdSweepJob = await TestCreateSweepJob(jobName, experimentName, request);
            var retrievedJob = await TestGetJob(jobName, createdSweepJob).ConfigureAwait(false);
            if (useInput)
            {
                // Fetch RunDetails and validate presence of expected inputs
                var retrievedInputs = ((SweepJob)retrievedJob.Properties).Inputs;
                retrievedInputs.Keys.Should().BeEquivalentTo(jobInput.Keys, because: "SweepJob.Inputs should have same keys as jobInput");
            }
            await TestCancelJob(jobName).ConfigureAwait(false);
            await TestListJobsFilterType(jobName, retrievedJob, JobType.Sweep, true).ConfigureAwait(false);
            await TestDeleteJob(jobName).ConfigureAwait(false);
        }

        [Theory(Skip = "Causing MFE Sweep Job to fail. The asset ID generation has some issue. TODO: https://dev.azure.com/msdata/Vienna/_workitems/edit/1994396")]
        [InlineData(true)]
        [InlineData(false)]
        [MfeTest(TestCategory.Integration, ScenarioScope.Sweep)]
        public async Task TestSweepJobE2EAssetId(bool useInput)
        {
            var experimentName = "TestSweepJobE2E";
            var input = CreateLiteralJobInput()
                                .CreateMerged(CreateUriFileJobInput(Fixture))
                                .CreateMerged(CreateUriFolderJobInput(Fixture))
                                .CreateMerged(CreateMLTableJobInput(Fixture))
                                .CreateMerged(await CreateArmIdJobInput().ConfigureAwait(false));
            var jobInput = useInput ? input : null;
            var request = await SweepJobHelpers.GetTestSweepJob(Fixture, MfeConstants.DefaultEnvironmentName, experimentName, jobInput, useAssetIdForEnvironment: true).ConfigureAwait(false);
            var jobName = TestHelpers.GenerateName();

            var createdSweepJob = await TestCreateSweepJob(jobName, experimentName, request);
            var retrievedJob = await TestGetJob(jobName, createdSweepJob).ConfigureAwait(false);
            if (useInput)
            {
                // Fetch RunDetails and validate presence of expected inputs
                var retrievedInputs = ((SweepJob)retrievedJob.Properties).Inputs;
                retrievedInputs.Keys.Should().BeEquivalentTo(jobInput.Keys, because: "SweepJob.Inputs should have same keys as jobInput");
            }
            await TestCancelJob(jobName).ConfigureAwait(false);
            await TestListJobsFilterType(jobName, retrievedJob, JobType.Sweep, true).ConfigureAwait(false);
            await TestDeleteJob(jobName).ConfigureAwait(false);
        }

        #endregion
        [Theory(Skip = "Flaky test")]
        [MfeTest(TestCategory.Integration, ScenarioScope.Command)]
        [MemberData(nameof(JobResourceConfigurationAndDataTestCases))]
        public async Task TestCommandJobE2EInput(ResourceConfiguration resourceConfiguration, bool useInput)
        {
            var jobName = TestHelpers.GenerateName();
            var tag = GenerateTag();
            var experimentName = Guid.NewGuid().ToString();
            var jobInput = useInput ? await CreateJobInput(Fixture).ConfigureAwait(false) : null;
            var environmentNameAndVersion = $"{MfeConstants.DefaultEnvironmentName}/versions/{MfeConstants.DefaultEnvironmentVersion}";
            var createdJob = await TestCreateCommandJob(jobName, environmentNameAndVersion, experimentName, resourceConfiguration, jobInput: jobInput, tag: tag).ConfigureAwait(false);
            var retrievedJob = await TestGetJob(jobName, createdJob).ConfigureAwait(false);
            if (useInput)
            {
                var details = await RunHistoryClient.GetRunDetails(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, jobName).ConfigureAwait(false);
                var detailsInputs = details.Inputs;
                var inputKeysToAssertOn = jobInput.Keys.Where(x => x != "literalJobInput");
                detailsInputs.Keys.Should().BeEquivalentTo(inputKeysToAssertOn, because: "RunDetails.Inputs should have same keys as jobInput");

                await TestCancelJob(jobName).ConfigureAwait(false);
                await TestListJobs(jobName, retrievedJob, tag: tag).ConfigureAwait(false);
                await TestDeleteJob(jobName).ConfigureAwait(false);

                // have jobInput use existing assetIds to make sure new assets aren't created when creating a new job
                foreach (var inputData in jobInput.Where(x => x.Key != "literalJobInput"))
                {
                    var input = detailsInputs[inputData.Key];
                    ((AssetJobInput)inputData.Value).Uri = input.AssetId.ToString();
                }
                var jobNameExistingassets = TestHelpers.GenerateName();
                var createdJobExistingAssets = await TestCreateCommandJob(jobNameExistingassets, environmentNameAndVersion, experimentName, resourceConfiguration, jobInput: jobInput, tag: tag).ConfigureAwait(false);
                var retrievedJobExistingAssets = await TestGetJob(jobNameExistingassets, createdJobExistingAssets).ConfigureAwait(false);
                var detailsExistingAssets = await RunHistoryClient.GetRunDetails(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, jobNameExistingassets).ConfigureAwait(false);
                var detailsExistingAssetInputs = detailsExistingAssets.Inputs;
                detailsExistingAssetInputs.Keys.SequenceEqual(detailsInputs.Keys).Should().BeTrue(because: "detailsExistingAssetInputs should have same keys as detailsInputs");
                foreach (var existingInput in detailsExistingAssetInputs)
                {
                    detailsInputs[existingInput.Key].AssetId.Should().Be(
                        existingInput.Value.AssetId,
                        because: $"{existingInput.Key} should not have created a new asset and should have matching AssetIds");
                }
            }
        }

        [Fact(Skip = "Flaky Test.")]
        [MfeTest(TestCategory.Integration, ScenarioScope.Command)]
        public async Task TestCommandJobE2EInputUsingRegistryAssets()
        {
            var jobName = TestHelpers.GenerateName();
            var tag = GenerateTag();
            var experimentName = "registryModelCommandJobE2E";
            var jobInput = GetRegistryAssetJobInput();
            var environmentNameAndVersion = $"{MfeConstants.DefaultEnvironmentName}/versions/{MfeConstants.DefaultEnvironmentVersion}";
            var resourceConfiguration = new ResourceConfiguration { InstanceCount = 1 };
            var createdJob = await TestCreateCommandJob(jobName, environmentNameAndVersion, experimentName, resourceConfiguration, jobInput: jobInput, tag: tag).ConfigureAwait(false);
            var retrievedJob = await TestGetJob(jobName, createdJob).ConfigureAwait(false);

            var details = await RunHistoryClient.GetRunDetails(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, jobName).ConfigureAwait(false);
            var detailsInputs = details.Inputs;
            var inputKeysToAssertOn = jobInput.Keys.Where(x => x != "literalJobInput");
            detailsInputs.Keys.Should().BeEquivalentTo(inputKeysToAssertOn, because: "RunDetails.Inputs should have same keys as jobInput");

            await TestListJobs(jobName, retrievedJob, tag: tag).ConfigureAwait(false);

            // If this test starts to take more time because of Queued job then we can remove this assertion to wait for job to finish, similar to other tests in this file.
            await RetryUntilAssertionPasses(
                async () =>
                {
                    // Sometimes there are other jobs queued, so it take sometime for this job to finish.
                    await Task.Delay(TimeSpan.FromSeconds(10)).ConfigureAwait(false);
                    details = await RunHistoryClient.GetRunDetails(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, jobName).ConfigureAwait(false);
                    details.Status.Should().Be(RunStatus.Completed.ToString());
                }).ConfigureAwait(false);
        }

        [Fact(Skip = "https://msdata.visualstudio.com/Vienna/_workitems/edit/1798423")]
        [MfeTest(TestCategory.Integration, ScenarioScope.Command)]
        public async Task TestCommandJobE2EJobInputUriFile()
        {
            var jobNameCreatedJobTuples = new List<(string jobName, Resource<JobBase> createdJob, InputDeliveryMode inputDeliveryMode, string pathType)>();
            var armIdJobInput = await CreateArmIdJobInput().ConfigureAwait(false);
            foreach (var testCase in CommandJobInputUriFileTestingMatrix())
            {
                var inputDeliveryMode = (InputDeliveryMode)testCase[0];
                var pathType = (string)testCase[1];
                var command = (string)testCase[2];
                var resourceConfiguration = new ResourceConfiguration { InstanceCount = 1 };
                var jobName = string.Join('_', TestHelpers.GenerateName(), inputDeliveryMode.ToString(), pathType);
                var tag = GenerateTag();
                var experimentName = Guid.NewGuid().ToString();

                var path = $"LocalUpload/MfeModelVersionTests/directory/file.txt";
                string uri = pathType switch
                {
                    "uriFileJobInputArmScopePath" => $"azureml://subscriptions/{Fixture.SubscriptionId}/resourcegroups/{Fixture.ResourceGroupName}/workspaces/{Fixture.WorkspaceName}/datastores/workspaceblobstore/paths/{path}",
                    "uriFileJobInputDatastorePath" => $"azureml://datastores/workspaceblobstore/paths/{path}",
                    "uriFileJobInputExternalPath" => "https://azuremlexamples.blob.core.windows.net/datasets/iris.csv",
                    "uriFileJobInputDataArmId" => ((UriFileJobInput)armIdJobInput["uriFileDataArmId"]).Uri,
                    _ => throw new ArgumentException($"Received unexpected pathType {pathType}"),
                };
                var uriFileJobInput = new UriFileJobInput()
                {
                    Uri = uri,
                    Mode = inputDeliveryMode
                };
                var inputDataBindings = new Dictionary<string, JobInput>()
                {
                    { pathType, uriFileJobInput }
                };
                var commandJobRequest = await CommandJobHelpers.GetTestCommandJob(Fixture, MfeConstants.DefaultEnvironmentName, experimentName, resourceConfiguration: resourceConfiguration, jobInput: inputDataBindings, tag: tag, computeName: MfeConstants.CommandJobE2EComputeName).ConfigureAwait(false);
                ((CommandJob)commandJobRequest.Properties).Command = command;
                var createdJob = await TestCreateCommandJob(jobName, MfeConstants.DefaultEnvironmentName, experimentName, resourceConfiguration, commandJobRequest: commandJobRequest, jobInput: inputDataBindings, tag: tag).ConfigureAwait(false);
                jobNameCreatedJobTuples.Add((jobName, createdJob, inputDeliveryMode, pathType));
            }

            foreach (var (jobName, createdJob, inputDeliveryMode, pathType) in jobNameCreatedJobTuples)
            {
                await EventualConsistencyTesting.RetryUntilAssertionPasses(
                   async () =>
                   {
                       var retrievedJob = await TestGetJob(jobName, createdJob).ConfigureAwait(false);
                       var becauseMessage = $"{jobName} should have completed status for inputDeliveryMode: {inputDeliveryMode}, pathType: {pathType}. " +
                           $"Please check the run under sub: {Fixture.SubscriptionId}, rg: {Fixture.ResourceGroupName}, ws: {Fixture.WorkspaceName} for more info.";
                       retrievedJob.Properties.Status.Should().Be(
                           JobStatus.Completed,
                           because: becauseMessage);
                   },
                   policy: EventualConsistencyTesting.ThreeMinutesPolicy).ConfigureAwait(false);
            }
        }

        [Fact(Skip = "https://msdata.visualstudio.com/Vienna/_workitems/edit/1798423")]
        [MfeTest(TestCategory.Integration, ScenarioScope.Command)]
        public async Task TestCommandJobE2EJobOutputUriFile()
        {
            var jobNameCreatedJobTuples = new List<(string jobName, Resource<JobBase> createdJob, OutputDeliveryMode inputDeliveryMode, string pathType)>();
            var armIdJobInput = await CreateArmIdJobInput().ConfigureAwait(false);
            foreach (var testCase in CommandJobOutputUriFileTestingMatrix())
            {
                var outputDeliveryMode = (OutputDeliveryMode)testCase[0];
                var pathType = (string)testCase[1];
                var command = (string)testCase[2];
                var resourceConfiguration = new ResourceConfiguration { InstanceCount = 1 };
                var jobName = string.Join('_', TestHelpers.GenerateName(), outputDeliveryMode.ToString(), pathType);
                var tag = GenerateTag();
                var experimentName = Guid.NewGuid().ToString();

                var path = $"LocalUpload/MfeJobControllerOutputAssetTests/fileUpload-{Guid.NewGuid().ToString()[..4]}.txt";
                string uri = pathType switch
                {
                    "uriFileJobOutputArmScopePath" => $"azureml://subscriptions/{Fixture.SubscriptionId}/resourcegroups/{Fixture.ResourceGroupName}/workspaces/{Fixture.WorkspaceName}/datastores/workspaceblobstore/paths/{path}",
                    "uriFileJobOutputDatastorePath" => $"azureml://datastores/workspaceblobstore/paths/{path}",
                    "uriFileJobOutputExternalPath" => "https://azuremlexamples.blob.core.windows.net/datasets/iris.csv",
                    "uriFileJobOutputDataArmId" => ((UriFileJobInput)armIdJobInput["uriFileDataArmId"]).Uri,
                    _ => throw new ArgumentException($"Received unexpected pathType {pathType}"),
                };
                var uriFileJobOutput = new UriFileJobOutput()
                {
                    Uri = uri,
                    Mode = outputDeliveryMode
                };
                var outputDataBindings = new Dictionary<string, JobOutput>()
                {
                    { pathType, uriFileJobOutput }
                };
                var commandJobRequest = await CommandJobHelpers.GetTestCommandJob(Fixture, MfeConstants.DefaultEnvironmentName, experimentName, resourceConfiguration: resourceConfiguration, jobOutput: outputDataBindings, tag: tag, computeName: MfeConstants.CommandJobE2EComputeName).ConfigureAwait(false);
                ((CommandJob)commandJobRequest.Properties).Command = command;
                var createdJob = await TestCreateCommandJob(jobName, MfeConstants.DefaultEnvironmentName, experimentName, resourceConfiguration, commandJobRequest: commandJobRequest, jobOutput: outputDataBindings, tag: tag).ConfigureAwait(false);
                jobNameCreatedJobTuples.Add((jobName, createdJob, outputDeliveryMode, pathType));
            }

            foreach (var (jobName, createdJob, outputDeliveryMode, pathType) in jobNameCreatedJobTuples)
            {
                await EventualConsistencyTesting.RetryUntilAssertionPasses(
                   async () =>
                   {
                       var retrievedJob = await TestGetJob(jobName, createdJob).ConfigureAwait(false);
                       var becauseMessage = $"{jobName} should have completed status for outputDeliveryMode: {outputDeliveryMode}, pathType: {pathType}. " +
                           $"Please check the run under sub: {Fixture.SubscriptionId}, rg: {Fixture.ResourceGroupName}, ws: {Fixture.WorkspaceName} for more info.";
                       retrievedJob.Properties.Status.Should().Be(
                           JobStatus.Completed,
                           because: becauseMessage);
                   },
                   policy: EventualConsistencyTesting.ThreeMinutesPolicy).ConfigureAwait(false);
            }
        }

        [Fact(Skip = "https://msdata.visualstudio.com/Vienna/_workitems/edit/1798423")]
        [MfeTest(TestCategory.Integration, ScenarioScope.Command)]
        public async Task TestCommandJobE2EJobInputUriFolder()
        {
            var jobNameCreatedJobTuples = new List<(string jobName, Resource<JobBase> createdJob, InputDeliveryMode inputDeliveryMode, string pathType)>();
            var armIdJobInput = await CreateArmIdJobInput().ConfigureAwait(false);
            foreach (var testCase in CommandJobInputUriFolderTestingMatrix())
            {
                var inputDeliveryMode = (InputDeliveryMode)testCase[0];
                var pathType = (string)testCase[1];
                var command = (string)testCase[2];
                var resourceConfiguration = new ResourceConfiguration { InstanceCount = 1 };
                var jobName = string.Join('_', TestHelpers.GenerateName(), inputDeliveryMode.ToString(), pathType);
                var tag = GenerateTag();
                var experimentName = Guid.NewGuid().ToString();

                var path = "LocalUpload/MfeModelVersionTests/directory";
                string uri = pathType switch
                {
                    "uriFolderJobInputArmScopePath" => $"azureml://subscriptions/{Fixture.SubscriptionId}/resourcegroups/{Fixture.ResourceGroupName}/workspaces/{Fixture.WorkspaceName}/datastores/workspaceblobstore/paths/{path}",
                    "uriFolderJobInputDatastorePath" => $"azureml://datastores/workspaceblobstore/paths/{path}",
                    "uriFolderJobInputExternalPath" => "wasbs://<EMAIL>/",
                    "uriFolderJobInputDataArmId" => ((UriFolderJobInput)armIdJobInput["uriFolderDataArmId"]).Uri,
                    _ => throw new ArgumentException($"Received unexpected pathType {pathType}"),
                };
                var uriFolderJobInput = new UriFolderJobInput()
                {
                    Uri = uri,
                    Mode = inputDeliveryMode
                };
                var inputDataBindings = new Dictionary<string, JobInput>()
                {
                    { pathType, uriFolderJobInput }
                };
                var commandJobRequest = await CommandJobHelpers.GetTestCommandJob(Fixture, MfeConstants.DefaultEnvironmentName, experimentName, resourceConfiguration: resourceConfiguration, jobInput: inputDataBindings, tag: tag, computeName: MfeConstants.CommandJobE2EComputeName).ConfigureAwait(false);
                ((CommandJob)commandJobRequest.Properties).Command = command;
                var createdJob = await TestCreateCommandJob(jobName, MfeConstants.DefaultEnvironmentName, experimentName, resourceConfiguration, commandJobRequest: commandJobRequest, jobInput: inputDataBindings, tag: tag).ConfigureAwait(false);
                jobNameCreatedJobTuples.Add((jobName, createdJob, inputDeliveryMode, pathType));
            }

            foreach (var (jobName, createdJob, inputDeliveryMode, pathType) in jobNameCreatedJobTuples)
            {
                await EventualConsistencyTesting.RetryUntilAssertionPasses(
                   async () =>
                   {
                       var retrievedJob = await TestGetJob(jobName, createdJob).ConfigureAwait(false);
                       var becauseMessage = $"{jobName} should have completed status for inputDeliveryMode: {inputDeliveryMode}, pathType: {pathType}. " +
                           $"Please check the run under sub: {Fixture.SubscriptionId}, rg: {Fixture.ResourceGroupName}, ws: {Fixture.WorkspaceName} for more info.";
                       retrievedJob.Properties.Status.Should().Be(
                           JobStatus.Completed,
                           because: becauseMessage);
                   },
                   policy: EventualConsistencyTesting.ThreeMinutesPolicy).ConfigureAwait(false);
            }
        }

        [Fact(Skip = "https://msdata.visualstudio.com/Vienna/_workitems/edit/1798423")]
        [MfeTest(TestCategory.Integration, ScenarioScope.Command)]
        public async Task TestCommandJobE2EJobOutputUriFolder()
        {
            var jobNameCreatedJobTuples = new List<(string jobName, Resource<JobBase> createdJob, OutputDeliveryMode outputDeliveryMode, string pathType)>();
            var armIdJobInput = await CreateArmIdJobInput().ConfigureAwait(false);
            foreach (var testCase in CommandJobOutputUriFolderTestingMatrix())
            {
                var outputDeliveryMode = (OutputDeliveryMode)testCase[0];
                var pathType = (string)testCase[1];
                var command = (string)testCase[2];
                var resourceConfiguration = new ResourceConfiguration { InstanceCount = 1 };
                var jobName = string.Join('_', TestHelpers.GenerateName(), outputDeliveryMode.ToString(), pathType);
                var tag = GenerateTag();
                var experimentName = Guid.NewGuid().ToString();

                var path = $"LocalUpload/MfeJobControllerOutputAssetTests/folderUpload-{Guid.NewGuid().ToString()[..4]}";
                string uri = pathType switch
                {
                    "uriFolderJobOutputArmScopePath" => $"azureml://subscriptions/{Fixture.SubscriptionId}/resourcegroups/{Fixture.ResourceGroupName}/workspaces/{Fixture.WorkspaceName}/datastores/workspaceblobstore/paths/{path}",
                    "uriFolderJobOutputDatastorePath" => $"azureml://datastores/workspaceblobstore/paths/{path}",
                    "uriFolderJobOutputDataArmId" => ((UriFolderJobInput)armIdJobInput["uriFolderDataArmId"]).Uri,
                    _ => throw new ArgumentException($"Received unexpected pathType {pathType}"),
                };
                var uriFolderJoboutput = new UriFolderJobOutput()
                {
                    Uri = uri,
                    Mode = outputDeliveryMode
                };
                var outputDataBindings = new Dictionary<string, JobOutput>()
                {
                    { pathType, uriFolderJoboutput }
                };
                var commandJobRequest = await CommandJobHelpers.GetTestCommandJob(Fixture, MfeConstants.DefaultEnvironmentName, experimentName, resourceConfiguration: resourceConfiguration, jobOutput: outputDataBindings, tag: tag, computeName: MfeConstants.CommandJobE2EComputeName).ConfigureAwait(false);
                ((CommandJob)commandJobRequest.Properties).Command = command;
                var createdJob = await TestCreateCommandJob(jobName, MfeConstants.DefaultEnvironmentName, experimentName, resourceConfiguration, commandJobRequest: commandJobRequest, jobOutput: outputDataBindings, tag: tag).ConfigureAwait(false);
                jobNameCreatedJobTuples.Add((jobName, createdJob, outputDeliveryMode, pathType));
            }

            foreach (var (jobName, createdJob, outputDeliveryMode, pathType) in jobNameCreatedJobTuples)
            {
                await EventualConsistencyTesting.RetryUntilAssertionPasses(
                   async () =>
                   {
                       var retrievedJob = await TestGetJob(jobName, createdJob).ConfigureAwait(false);
                       var becauseMessage = $"{jobName} should have completed status for outputDeliveryMode: {outputDeliveryMode}, pathType: {pathType}. " +
                           $"Please check the run under sub: {Fixture.SubscriptionId}, rg: {Fixture.ResourceGroupName}, ws: {Fixture.WorkspaceName} for more info.";
                       retrievedJob.Properties.Status.Should().Be(
                           JobStatus.Completed,
                           because: becauseMessage);
                   },
                   policy: EventualConsistencyTesting.ThreeMinutesPolicy).ConfigureAwait(false);
            }
        }

        [Fact(Skip = "https://msdata.visualstudio.com/Vienna/_workitems/edit/1798423")]
        [MfeTest(TestCategory.Integration, ScenarioScope.Command)]
        public async Task TestCommandJobE2EJobInputMLTable()
        {
            var jobNameCreatedJobTuples = new List<(string jobName, Resource<JobBase> createdJob, InputDeliveryMode inputDeliveryMode, string pathType)>();
            foreach (var testCase in CommandJobInputMLTableTestingMatrix())
            {
                var inputDeliveryMode = (InputDeliveryMode)testCase[0];
                var pathType = (string)testCase[1];
                var command = (string)testCase[2];
                var resourceConfiguration = new ResourceConfiguration { InstanceCount = 1 };
                var jobName = string.Join('_', TestHelpers.GenerateName(), inputDeliveryMode.ToString(), pathType);
                var tag = GenerateTag();
                var experimentName = Guid.NewGuid().ToString();

                var path = "LocalUpload/MfeMLTableJobInputTests";
                string uri = pathType switch
                {
                    "mLTableJobInputArmScopePath" => $"azureml://subscriptions/{Fixture.SubscriptionId}/resourcegroups/{Fixture.ResourceGroupName}/workspaces/{Fixture.WorkspaceName}/datastores/workspaceblobstore/paths/{path}",
                    "mLTableJobInputDatastorePath" => $"azureml://datastores/workspaceblobstore/paths/{path}",
                    "mLTableJobInputExternalPath" => "wasbs://<EMAIL>/",
                    _ => throw new ArgumentException($"Received unexpected pathType {pathType}"),
                };
                var mLTableJobInput = new MLTableJobInput()
                {
                    Uri = uri,
                    Mode = inputDeliveryMode
                };
                var inputDataBindings = new Dictionary<string, JobInput>()
                {
                    { pathType, mLTableJobInput }
                };
                var commandJobRequest = await CommandJobHelpers.GetTestCommandJob(Fixture, MfeConstants.DefaultEnvironmentName, experimentName, resourceConfiguration: resourceConfiguration, jobInput: inputDataBindings, tag: tag, computeName: MfeConstants.CommandJobE2EComputeName).ConfigureAwait(false);
                ((CommandJob)commandJobRequest.Properties).Command = command;
                var createdJob = await TestCreateCommandJob(jobName, MfeConstants.DefaultEnvironmentName, experimentName, resourceConfiguration, commandJobRequest: commandJobRequest, jobInput: inputDataBindings, tag: tag).ConfigureAwait(false);
                jobNameCreatedJobTuples.Add((jobName, createdJob, inputDeliveryMode, pathType));
            }

            foreach (var (jobName, createdJob, inputDeliveryMode, pathType) in jobNameCreatedJobTuples)
            {
                await EventualConsistencyTesting.RetryUntilAssertionPasses(
                   async () =>
                   {
                       var retrievedJob = await TestGetJob(jobName, createdJob).ConfigureAwait(false);
                       var becauseMessage = $"{jobName} should have completed status for inputDeliveryMode: {inputDeliveryMode}, pathType: {pathType}. " +
                           $"Please check the run under sub: {Fixture.SubscriptionId}, rg: {Fixture.ResourceGroupName}, ws: {Fixture.WorkspaceName} for more info.";
                       retrievedJob.Properties.Status.Should().Be(
                           JobStatus.Completed,
                           because: becauseMessage);
                   },
                   policy: EventualConsistencyTesting.ThreeMinutesPolicy).ConfigureAwait(false);
            }
        }

        [Fact(Skip = "https://msdata.visualstudio.com/Vienna/_workitems/edit/1798423")]
        [MfeTest(TestCategory.Integration, ScenarioScope.Command)]
        public async Task TestCommandJobE2EJobOutputMLTable()
        {
            var jobNameCreatedJobTuples = new List<(string jobName, Resource<JobBase> createdJob, OutputDeliveryMode outputDeliveryMode, string pathType)>();
            foreach (var testCase in CommandJobOutputMLTableTestingMatrix())
            {
                var outputDeliveryMode = (OutputDeliveryMode)testCase[0];
                var pathType = (string)testCase[1];
                var command = (string)testCase[2];
                var resourceConfiguration = new ResourceConfiguration { InstanceCount = 1 };
                var jobName = string.Join('_', TestHelpers.GenerateName(), outputDeliveryMode.ToString(), pathType);
                var tag = GenerateTag();
                var experimentName = Guid.NewGuid().ToString();

                var path = $"LocalUpload/MfeJobControllerOutputAssetTests/mLTableUpload-{Guid.NewGuid().ToString()[..4]}";
                string uri = pathType switch
                {
                    "mLTableJobOutputArmScopePath" => $"azureml://subscriptions/{Fixture.SubscriptionId}/resourcegroups/{Fixture.ResourceGroupName}/workspaces/{Fixture.WorkspaceName}/datastores/workspaceblobstore/paths/{path}",
                    "mLTableJobOutputDatastorePath" => $"azureml://datastores/workspaceblobstore/paths/{path}",
                    _ => throw new ArgumentException($"Received unexpected pathType {pathType}"),
                };
                var mLTableJobInput = new MLTableJobOutput()
                {
                    Uri = uri,
                    Mode = outputDeliveryMode
                };
                var outputDataBindings = new Dictionary<string, JobOutput>()
                {
                    { pathType, mLTableJobInput }
                };
                var commandJobRequest = await CommandJobHelpers.GetTestCommandJob(Fixture, MfeConstants.DefaultEnvironmentName, experimentName, resourceConfiguration: resourceConfiguration, jobOutput: outputDataBindings, tag: tag, computeName: MfeConstants.CommandJobE2EComputeName).ConfigureAwait(false);
                ((CommandJob)commandJobRequest.Properties).Command = command;
                var createdJob = await TestCreateCommandJob(jobName, MfeConstants.DefaultEnvironmentName, experimentName, resourceConfiguration, commandJobRequest: commandJobRequest, jobOutput: outputDataBindings, tag: tag).ConfigureAwait(false);
                jobNameCreatedJobTuples.Add((jobName, createdJob, outputDeliveryMode, pathType));
            }

            foreach (var (jobName, createdJob, outputDeliveryMode, pathType) in jobNameCreatedJobTuples)
            {
                await EventualConsistencyTesting.RetryUntilAssertionPasses(
                   async () =>
                   {
                       var retrievedJob = await TestGetJob(jobName, createdJob).ConfigureAwait(false);
                       var becauseMessage = $"{jobName} should have completed status for outputDeliveryMode: {outputDeliveryMode}, pathType: {pathType}. " +
                           $"Please check the run under sub: {Fixture.SubscriptionId}, rg: {Fixture.ResourceGroupName}, ws: {Fixture.WorkspaceName} for more info.";
                       retrievedJob.Properties.Status.Should().Be(
                           JobStatus.Completed,
                           because: becauseMessage);
                   },
                   policy: EventualConsistencyTesting.ThreeMinutesPolicy).ConfigureAwait(false);
            }
        }

        [Fact(Skip = "https://msdata.visualstudio.com/Vienna/_workitems/edit/1798423")]
        [MfeTest(TestCategory.Integration, ScenarioScope.Command)]
        public async Task TestCommandJobE2EJobInputMLFlowModel()
        {
            var jobNameCreatedJobTuples = new List<(string jobName, Resource<JobBase> createdJob, InputDeliveryMode inputDeliveryMode, string pathType)>();
            foreach (var testCase in CommandJobInputMLFlowModelTestingMatrix())
            {
                var inputDeliveryMode = (InputDeliveryMode)testCase[0];
                var pathType = (string)testCase[1];
                var command = (string)testCase[2];
                var resourceConfiguration = new ResourceConfiguration { InstanceCount = 1 };
                var jobName = string.Join('_', TestHelpers.GenerateName(), inputDeliveryMode.ToString(), pathType);
                var tag = GenerateTag();
                var experimentName = Guid.NewGuid().ToString();

                var path = "LocalUpload/MfeModelVersionTests/directory";
                string uri = pathType switch
                {
                    "mLFlowModelJobInputArmScopePath" => $"azureml://subscriptions/{Fixture.SubscriptionId}/resourcegroups/{Fixture.ResourceGroupName}/workspaces/{Fixture.WorkspaceName}/datastores/workspaceblobstore/paths/{path}",
                    "mLFlowModelJobInputDatastorePath" => $"azureml://datastores/workspaceblobstore/paths/{path}",
                    _ => throw new ArgumentException($"Received unexpected pathType {pathType}"),
                };
                var mLFlowModelJobInput = new MLFlowModelJobInput()
                {
                    Uri = uri,
                    Mode = inputDeliveryMode
                };
                var inputDataBindings = new Dictionary<string, JobInput>()
                {
                    { pathType, mLFlowModelJobInput }
                };
                var commandJobRequest = await CommandJobHelpers.GetTestCommandJob(Fixture, MfeConstants.DefaultEnvironmentName, experimentName, resourceConfiguration: resourceConfiguration, jobInput: inputDataBindings, tag: tag, computeName: MfeConstants.CommandJobE2EComputeName).ConfigureAwait(false);
                ((CommandJob)commandJobRequest.Properties).Command = command;
                var createdJob = await TestCreateCommandJob(jobName, MfeConstants.DefaultEnvironmentName, experimentName, resourceConfiguration, commandJobRequest: commandJobRequest, jobInput: inputDataBindings, tag: tag).ConfigureAwait(false);
                jobNameCreatedJobTuples.Add((jobName, createdJob, inputDeliveryMode, pathType));
            }

            foreach (var (jobName, createdJob, inputDeliveryMode, pathType) in jobNameCreatedJobTuples)
            {
                await EventualConsistencyTesting.RetryUntilAssertionPasses(
                   async () =>
                   {
                       var retrievedJob = await TestGetJob(jobName, createdJob).ConfigureAwait(false);
                       var becauseMessage = $"{jobName} should have completed status for inputDeliveryMode: {inputDeliveryMode}, pathType: {pathType}. " +
                           $"Please check the run under sub: {Fixture.SubscriptionId}, rg: {Fixture.ResourceGroupName}, ws: {Fixture.WorkspaceName} for more info.";
                       retrievedJob.Properties.Status.Should().Be(
                           JobStatus.Completed,
                           because: becauseMessage);
                   },
                   policy: EventualConsistencyTesting.ThreeMinutesPolicy).ConfigureAwait(false);
            }
        }

        [Fact(Skip = "https://msdata.visualstudio.com/Vienna/_workitems/edit/1798423")]
        [MfeTest(TestCategory.Integration, ScenarioScope.Command)]
        public async Task TestCommandJobE2EJobOutputMLFlowModel()
        {
            var jobNameCreatedJobTuples = new List<(string jobName, Resource<JobBase> createdJob, OutputDeliveryMode outputDeliveryMode, string pathType)>();
            foreach (var testCase in CommandJobOutputMLFlowModelTestingMatrix())
            {
                var outputDeliveryMode = (OutputDeliveryMode)testCase[0];
                var pathType = (string)testCase[1];
                var command = (string)testCase[2];
                var resourceConfiguration = new ResourceConfiguration { InstanceCount = 1 };
                var jobName = string.Join('_', TestHelpers.GenerateName(), outputDeliveryMode.ToString(), pathType);
                var tag = GenerateTag();
                var experimentName = Guid.NewGuid().ToString();

                var path = $"LocalUpload/MfeModelVersionTests/directory";
                string uri = pathType switch
                {
                    "mLFlowModelJobOutputArmScopePath" => $"azureml://subscriptions/{Fixture.SubscriptionId}/resourcegroups/{Fixture.ResourceGroupName}/workspaces/{Fixture.WorkspaceName}/datastores/workspaceblobstore/paths/{path}",
                    "mLFlowModelJobOutputDatastorePath" => $"azureml://datastores/workspaceblobstore/paths/{path}",
                    _ => throw new ArgumentException($"Received unexpected pathType {pathType}"),
                };
                var mLFlowModelJobOutput = new MLFlowModelJobOutput()
                {
                    Uri = uri,
                    Mode = outputDeliveryMode
                };
                var outputDataBindings = new Dictionary<string, JobOutput>()
                {
                    { pathType, mLFlowModelJobOutput }
                };
                var commandJobRequest = await CommandJobHelpers.GetTestCommandJob(Fixture, MfeConstants.DefaultEnvironmentName, experimentName, resourceConfiguration: resourceConfiguration, jobOutput: outputDataBindings, tag: tag, computeName: MfeConstants.CommandJobE2EComputeName).ConfigureAwait(false);
                ((CommandJob)commandJobRequest.Properties).Command = command;
                var createdJob = await TestCreateCommandJob(jobName, MfeConstants.DefaultEnvironmentName, experimentName, resourceConfiguration, commandJobRequest: commandJobRequest, jobOutput: outputDataBindings, tag: tag).ConfigureAwait(false);
                jobNameCreatedJobTuples.Add((jobName, createdJob, outputDeliveryMode, pathType));
            }

            foreach (var (jobName, createdJob, outputDeliveryMode, pathType) in jobNameCreatedJobTuples)
            {
                await EventualConsistencyTesting.RetryUntilAssertionPasses(
                   async () =>
                   {
                       var retrievedJob = await TestGetJob(jobName, createdJob).ConfigureAwait(false);
                       var becauseMessage = $"{jobName} should have completed status for outputDeliveryMode: {outputDeliveryMode}, pathType: {pathType}. " +
                           $"Please check the run under sub: {Fixture.SubscriptionId}, rg: {Fixture.ResourceGroupName}, ws: {Fixture.WorkspaceName} for more info.";
                       retrievedJob.Properties.Status.Should().Be(
                           JobStatus.Completed,
                           because: becauseMessage);
                   },
                   policy: EventualConsistencyTesting.ThreeMinutesPolicy).ConfigureAwait(false);
            }
        }

        [Fact]
        [MfeTest(TestCategory.Integration, ScenarioScope.Command)]
        public async Task TestCommandJobE2EInvalidInput()
        {
            var resourceConfiguration = new ResourceConfiguration()
            {
                InstanceCount = 1
            };
            var jobName = TestHelpers.GenerateName();
            var tag = GenerateTag();
            var experimentName = Guid.NewGuid().ToString();
            var jobInput = new Dictionary<string, JobInput>()
            {
                {
                    "inputAssetMalformedAssetUri",
                    new MLFlowModelJobInput()
                    {
                        Uri = "not a valid uri format"
                    }
                }
            };
            var badFunc = () => TestCreateCommandJob(jobName, MfeConstants.DefaultEnvironmentName, experimentName, resourceConfiguration, jobInput: jobInput, tag: tag);
            var exceptionAssertions = await badFunc.Should().ThrowAsync<ServiceInvocationException>().ConfigureAwait(false);
            exceptionAssertions
                .Where(x => x.ErrorResponse.Error.Code == "UserError"
                && x.ErrorResponse.Error.Message.Contains("must be a valid Uri or ArmId", StringComparison.InvariantCultureIgnoreCase));
        }

        [Fact]
        [MfeTest(TestCategory.Integration, ScenarioScope.Command)]
        public async Task TestCommandJobE2EEvalModeIncompatibleInput()
        {
            var resourceConfiguration = new ResourceConfiguration()
            {
                InstanceCount = 1
            };
            var jobName = TestHelpers.GenerateName();
            var tag = GenerateTag();
            var experimentName = Guid.NewGuid().ToString();
            var jobInput = new Dictionary<string, JobInput>()
            {
                {
                    "inputAssetIncompatible",
                    new UriFolderJobInput()
                    {
                        Uri = "https://dprepdata.blob.core.windows.net/demo/Titanic.csv",
                        Mode = InputDeliveryMode.EvalDownload
                    }
                }
            };
            var badFunc = () => TestCreateCommandJob(jobName, MfeConstants.DefaultEnvironmentName, experimentName, resourceConfiguration, jobInput: jobInput, tag: tag);
            var exceptionAssertions = await badFunc.Should().ThrowAsync<ServiceInvocationException>().ConfigureAwait(false);
            exceptionAssertions
                .Where(x => x.ErrorResponse.Error.Code == "UserError"
                && x.ErrorResponse.Error.Message.Equals("Only MLTable is supported for EvalDownload and EvalMount", StringComparison.InvariantCultureIgnoreCase));

            jobInput = new Dictionary<string, JobInput>()
            {
                {
                    "inputAssetIncompatible",
                    new UriFolderJobInput()
                    {
                        Uri = "https://dprepdata.blob.core.windows.net/demo/Titanic.csv",
                        Mode = InputDeliveryMode.EvalMount
                    }
                }
            };
            exceptionAssertions = await badFunc.Should().ThrowAsync<ServiceInvocationException>().ConfigureAwait(false);
            exceptionAssertions
                .Where(x => x.ErrorResponse.Error.Code == "UserError"
                && x.ErrorResponse.Error.Message.Equals("Only MLTable is supported for EvalDownload and EvalMount", StringComparison.InvariantCultureIgnoreCase));
        }

        [Theory]
        [MfeTest(TestCategory.Integration, ScenarioScope.Command)]
        [MemberData(nameof(JobResourceConfigurationTestCases))]
        public async Task TestCommandJobE2EInexistentDatastoreFailsJob(ResourceConfiguration resourceConfiguration)
        {
            var datastoreName = "testdatastore" + Guid.NewGuid().ToString()[..4];
            var jobName = TestHelpers.GenerateName();
            var tag = GenerateTag();
            var experimentName = Guid.NewGuid().ToString();
            var jobOutput = new Dictionary<string, JobOutput>()
            {
                {
                    "outputModelAssetInexistentDatastore",
                    new CustomModelJobOutput()
                    {
                        Uri = string.Format("azureml://datastores/{0}/paths/mymodel", datastoreName),
                        Mode = OutputDeliveryMode.ReadWriteMount,
                    }
                }
            };
            var createdJob = await TestCreateCommandJob(jobName, MfeConstants.DefaultEnvironmentName, experimentName, resourceConfiguration, jobOutput: jobOutput, tag: tag).ConfigureAwait(false);
            var retrievedJob = await TestGetJob(jobName, createdJob, JobStatus.Failed).ConfigureAwait(false);
            await TestListJobs(jobName, retrievedJob, tag: tag).ConfigureAwait(false);
            await TestDeleteJob(jobName).ConfigureAwait(false);
        }

        [Fact(Skip = "Need to be updated after removing MLTable")]
        [MfeTest(TestCategory.Integration, ScenarioScope.Pipeline)]
        public async Task TestPipelineJobE2E()
        {
            var pipelineJobName = TestHelpers.GenerateName();
            var tag = GenerateTag();
            var createdPipelineJob = await TestCreatePipelineJob(pipelineJobName, tag: tag).ConfigureAwait(false);
            var retrievedPipelineJob = await TestGetJob(createdPipelineJob.Name, createdPipelineJob).ConfigureAwait(false);
            await TestListJobs(createdPipelineJob.Name, retrievedPipelineJob, tag: tag).ConfigureAwait(false);
            await TestListJobsFilter(createdPipelineJob.Name, retrievedPipelineJob, "Pipeline", tag, false).ConfigureAwait(false);
            var updatedJob = await TestUpdateJobPropTagsDescription(createdPipelineJob.Name, _originalRequest).ConfigureAwait(false);
            await TestPipelineJobAcrossApiVersions(createdPipelineJob.Name).ConfigureAwait(false);
            await TestCancelJob(updatedJob.Name).ConfigureAwait(false);
            await TestDeleteJob(updatedJob.Name).ConfigureAwait(false);
        }

        #region Static utility methods

        private static void CheckJobEquivalency(Resource<JobBase> expected, Resource<JobBase> actual)
        {
            actual.Should().BeEquivalentTo(expected, options => options.Excluding(x => x.Properties).UsingCaseInsensitiveProperties());
            actual.Properties.Should().BeOfType(expected.Properties.GetType());

            switch (actual.Properties)
            {
                case CommandJob properties:
                    properties.ComputeId.Should().BeEquivalentTo(((CommandJob)expected.Properties).ComputeId);
                    properties.Should().BeEquivalentTo(expected.Properties, options => options.Excluding(x => x.Properties).Excluding(x => x.Status).Excluding(x => x.Tags).Excluding(x => x.Services).Excluding(x => x.ComputeId).UsingCaseInsensitiveProperties());
                    break;

                case SweepJob properties:
                    properties.Should().BeEquivalentTo(expected.Properties, options => options.Excluding(x => x.Properties).Excluding(x => x.Status).Excluding(x => x.Tags).Excluding(x => x.Services).UsingCaseInsensitiveProperties());
                    break;

                case PipelineJob properties:
                    properties.Should().BeEquivalentTo(expected.Properties, options => options.Excluding(x => x.Properties).Excluding(x => x.Status).Excluding(x => x.Tags).Excluding(x => x.Services).UsingCaseInsensitiveProperties());
                    break;

                default:
                    throw new NotImplementedException();
            }
        }
        #endregion

        private static string GenerateTag()
        {
            return $"tag-{DateTime.UtcNow:yyyyMMddHHmmss}-{Guid.NewGuid().ToString()[..4]}";
        }

        #region Sub-test methods under TestSweepJobE2E
        private async Task<Resource<JobBase>> TestCreateSweepJob(string jobName, string experimentName, Resource<JobBase> request)
        {
            var createdJob = await Controller.CreateOrUpdateJob(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, jobName, request).ConfigureAwait(false);

            createdJob.Id.Should().BeEquivalentTo($"/subscriptions/{Fixture.SubscriptionId}/resourceGroups/{Fixture.ResourceGroupName}/providers/Microsoft.MachineLearningServices/workspaces/{Fixture.WorkspaceName}/jobs/{jobName}");
            createdJob.Name.Should().BeEquivalentTo(jobName);
            createdJob.Type.Should().Be("Microsoft.MachineLearningServices/workspaces/jobs");
            createdJob.Properties.Should().BeOfType<SweepJob>();
            createdJob.SystemData.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, 2.Minutes());
            createdJob.SystemData.CreatedBy.Should().NotBeNullOrWhiteSpace();
            createdJob.SystemData.CreatedByType.Should().Be(SystemData.UserType.Application);

            var createdSweepJob = (SweepJob)createdJob.Properties;
            var requestSweepJob = (SweepJob)request.Properties;

            createdSweepJob.ComputeId.Should().BeEquivalentTo(requestSweepJob.ComputeId);
            createdSweepJob.ExperimentName.Should().BeEquivalentTo(experimentName);
            createdSweepJob.Inputs.Should().BeEquivalentTo(requestSweepJob.Inputs, options => options.UsingCaseInsensitiveProperties());
            createdSweepJob.JobType.Should().Be(JobType.Sweep);
            createdSweepJob.Limits.MaxTotalTrials.Should().Be(requestSweepJob.Limits.MaxTotalTrials);
            createdSweepJob.Limits.MaxConcurrentTrials.Should().Be(requestSweepJob.Limits.MaxConcurrentTrials);
            createdSweepJob.Description.Should().BeEquivalentTo(requestSweepJob.Description);
            createdSweepJob.Properties.Should().Contain("sweep-property-name", "sweep-property-value");
            createdSweepJob.Tags.Should().Contain("sweep-tag-name", "sweep-tag-value");
            createdSweepJob.Identity.Should().BeEquivalentTo(requestSweepJob.Identity);
            createdSweepJob.SamplingAlgorithm.SamplingAlgorithmType.Should().Be(requestSweepJob.SamplingAlgorithm.SamplingAlgorithmType);
            Assert.True(JToken.DeepEquals(createdSweepJob.SearchSpace, requestSweepJob.SearchSpace));

            var createdTrial = createdSweepJob.Trial;
            var requestTrial = requestSweepJob.Trial;

            createdTrial.CodeId?.Should().BeEquivalentTo(requestTrial.CodeId);
            createdTrial.Command.Should().Be("echo hello world");
            createdTrial.EnvironmentVariables.Should().Contain("sweep-env-var", "sweep-env-var-value");
            createdTrial.Resources.InstanceCount.Should().Be(requestTrial.Resources.InstanceCount);
            createdTrial.Distribution.Should().BeNull();
            createdTrial.EnvironmentId.ToLowerInvariant().Should().StartWith(requestTrial.EnvironmentId.ToLowerInvariant()); // HyperDrive returns the EnvironmentId with the version

            return createdJob;
        }
        #endregion

        #region Sub-test methods under TestCommandJobE2E
        private async Task<Resource<JobBase>> TestCreateCommandJob(
            string jobName,
            string environmentNameVersion,
            string experimentName,
            ResourceConfiguration resourceConfiguration = null,
            Resource<JobBase> commandJobRequest = null,
            Dictionary<string, JobInput> jobInput = null,
            bool isLocal = false,
            string tag = null,
            Dictionary<string, JobOutput> jobOutput = null)
        {
            var request = commandJobRequest ?? await CommandJobHelpers.GetTestCommandJob(Fixture, environmentNameVersion, experimentName, resourceConfiguration, jobInput, isLocal, tag: tag, jobOutput: jobOutput).ConfigureAwait(false);
            var createdJob = await Controller.CreateOrUpdateJob(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, jobName, request).ConfigureAwait(false);

            createdJob.Id.Should().BeEquivalentTo($"/subscriptions/{Fixture.SubscriptionId}/resourceGroups/{Fixture.ResourceGroupName}/providers/Microsoft.MachineLearningServices/workspaces/{Fixture.WorkspaceName}/jobs/{jobName}");
            createdJob.Name.Should().BeEquivalentTo(jobName);
            createdJob.Type.Should().Be("Microsoft.MachineLearningServices/workspaces/jobs");
            createdJob.Properties.Should().BeOfType<CommandJob>();
            var properties = (CommandJob)createdJob.Properties;

            if (!isLocal)
            {
                createdJob.SystemData.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, 2.Minutes());
                createdJob.SystemData.CreatedBy.Should().NotBeNullOrWhiteSpace();
                createdJob.SystemData.CreatedByType.Should().Be(SystemData.UserType.Application);

                properties.Services?.Should().ContainKeys(JobServiceType.Studio.ToString(), JobServiceType.Tracking.ToString());
            }

            if (CommandJobHelpers.IsClusterless(resourceConfiguration))
            {
                properties.Resources.Properties.Should().ContainKeys(MfeConstants.ClusterlessJobComputeKey);
                properties.ComputeId.Should().BeNull();
            }

            properties.CodeId?.Should().BeEquivalentTo(((CommandJob)request.Properties)?.CodeId);
            properties.Command.Should().StartWith("echo hello world");
            properties.ComputeId.Should().BeEquivalentTo(((CommandJob)request.Properties).ComputeId);
            properties.Resources.InstanceCount.Should().Be(((CommandJob)request.Properties).Resources.InstanceCount);
            properties.EnvironmentId.Should().BeEquivalentTo(((CommandJob)request.Properties).EnvironmentId);
            properties.EnvironmentVariables.Should().BeEquivalentTo(((CommandJob)request.Properties).EnvironmentVariables);
            properties.ExperimentName.Should().Be(experimentName);
            properties.Inputs.Should().BeEquivalentTo(((CommandJob)request.Properties).Inputs, options => options.UsingCaseInsensitiveProperties());
            properties.Distribution.Should().BeNull();
            properties.JobType.Should().Be(JobType.Command);
            properties.Limits?.Timeout.Should().Be(((CommandJob)request.Properties).Limits?.Timeout);
            properties.Properties.Should().Contain("property-name", "property-value");
            properties.Tags.Should().Contain("tag-name", "tag-value");
            properties.Identity.Should().BeEquivalentTo(((CommandJob)request.Properties).Identity);
            properties.Description.Should().BeEquivalentTo(request.Properties.Description);
            properties.Outputs.Should().ContainKey("default");
            if (!string.IsNullOrWhiteSpace(tag))
            {
                properties.Tags.Should().Contain(tag, tag);
            }

            return createdJob;
        }
        #endregion

        #region Sub-test methods under TestPipelineJobE2E
        private async Task<Resource<JobBase>> TestCreatePipelineJob(string jobName, bool archived = false, string tag = null)
        {
            var computeId = await ComputeHelpers.GetComputeId(Fixture).ConfigureAwait(false);
            var componentArmId = await CreateComponent().ConfigureAwait(false);
            var experimentName = Guid.NewGuid().ToString();

            var step1 = JObject.FromObject(new
            {
                computeId = computeId,
                componentId = componentArmId,
                inputs = new
                {
                    int_a = new { type = "Literal", value = "52" },
                    int_b = new { type = "Literal", value = "78" }
                },
                outputs = new
                {
                    out_sum = new { type = "Dataset", mode = "ReadWriteMount" },
                    out_prod = new { type = "Dataset", mode = "ReadWriteMount" }
                }
            });

            InternalSharedServices.Contracts.PipelineJobSettings settings = new InternalSharedServices.Contracts.PipelineJobSettings
            {
                ContinueRunOnStepFailure = false,
                Datastore = new MfeResourceArmScope(Fixture.GetTestWorkspaceContext2(), MfeConstants.ArmTypeDatastore, "workspaceblobstore").ToString(),
                DefaultDatastoreName = "workspaceblobstore"
            };
            _originalRequest = new Resource<JobBase>
            {
                Properties = new PipelineJob()
                {
                    JobType = JobType.Pipeline,
                    ExperimentName = experimentName,
                    ComputeId = computeId,
                    Settings = JObject.FromObject(settings),
                    Inputs = new Dictionary<string, JobInput>
                    {
                        {
                            "sample_input_string",
                            new LiteralJobInput()
                            {
                                JobInputType = JobInputType.literal,
                                Value = "Fake_Hello_Python_World",
                                Description = null
                            }
                        }
                    },
                    Jobs = new Dictionary<string, JObject>
                    {
                        { "step1", step1 }
                    },
                    Outputs = new Dictionary<string, JobOutput>
                    {
                        {
                            "sample_output_data",
                            new UriFolderJobOutput()
                            {
                                JobOutputType = JobOutputType.uri_folder,
                                Mode = OutputDeliveryMode.ReadWriteMount,
                                Description = null
                            }
                        }
                    },
                    IsArchived = archived,
                    Tags = tag == null ? null : new Dictionary<string, string>() { { tag, tag } },
                }
            };

            var createdJob = await Controller.CreateOrUpdateJob(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, jobName, _originalRequest).ConfigureAwait(false);

            createdJob.Id.Should().Contain(MfeConstants.ArmTypeJob);
            createdJob.Name.Should().NotBeNull();
            createdJob.Type.Should().Be("Microsoft.MachineLearningServices/workspaces/jobs");
            createdJob.Properties.Should().BeOfType<PipelineJob>();
            createdJob.SystemData.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, 2.Minutes());
            createdJob.SystemData.CreatedBy.Should().NotBeNullOrWhiteSpace();
            createdJob.SystemData.CreatedByType.Should().Be(SystemData.UserType.Application);

            var properties = (PipelineJob)createdJob.Properties;
            properties.JobType.Should().Be(JobType.Pipeline);
            properties.Settings.Should().NotBeNull();
            properties.ExperimentName.Should().Be(experimentName);
            properties.Outputs.Should().NotBeNull();
            properties.Inputs.Should().NotBeNull();
            // this is intended: you can't created an archived job (only archive an existing job), so even if we pass in archived = true this should be false
            properties.IsArchived.Should().Be(false);
            if (!string.IsNullOrWhiteSpace(tag))
            {
                properties.Tags.Should().Contain(tag, tag);
            }

            if (archived)
            {
                createdJob.Properties.IsArchived = true;
                var updatedJob = await Controller.CreateOrUpdateJob(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, jobName, createdJob).ConfigureAwait(false);
                updatedJob.Properties.IsArchived.Should().BeTrue();
                return updatedJob;
            }

            return createdJob;
        }

        private async Task<string> CreateComponent()
        {
            string name = "111111";
            string version = "123";
            var codeArtifactId = MfeResourceVersionArmScope.ToString(Fixture.GetTestWorkspaceContext2(), MfeConstants.ArmTypeCodeContainer, name, "1"); // await CodeVersionHelpers.CreateCodeVersion(Fixture).ConfigureAwait(false);

            ComponentVersion componentVersion = new ComponentVersion()
            {
                Description = "test component for Pipeline_calculate_Yaml_for_OneNode_ParamInput",
                Tags = new Dictionary<string, string> { { "tags1", "tags1" } },
                Properties = new Dictionary<string, string> { { "properties1", "properties1" } },
            };

            var resourceComponentVersion = new Resource<ComponentVersion> { Properties = componentVersion };

            Resource<ComponentVersion> registeredComponentVersion;
            try
            {
                registeredComponentVersion = await Fixture.ManagementFrontEndClient.GetController<IComponentVersionController>().Get(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, name, version).ConfigureAwait(false);
            }
            catch (ServiceInvocationException e) when (e.Response.StatusCode == HttpStatusCode.NotFound)
            {
                registeredComponentVersion = await Fixture.ManagementFrontEndClient.GetController<IComponentVersionController>().CreateOrUpdate(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, name, version, resourceComponentVersion).ConfigureAwait(false);
            }

            return registeredComponentVersion.Id;
        }

        private async Task<Resource<JobBase>> TestUpdateJobPropTagsDescription(string jobName, Resource<JobBase> initialJob)
        {
            initialJob.Properties.Description = "UpdateJob";
            initialJob.Properties.Tags = initialJob.Properties.Tags ?? new Dictionary<string, string>();
            initialJob.Properties.Tags.Add("tagsUpdateKey", "tagsUpdateValue");
            initialJob.Properties.Properties = initialJob.Properties.Properties ?? new Dictionary<string, string>();
            initialJob.Properties.Properties.Add("propertiesUpdateKey", "propertiesUpdateValue");

            Resource<JobBase> updatedJob = await Controller.CreateOrUpdateJob(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, jobName, initialJob).ConfigureAwait(false);
            updatedJob.Properties.Tags.Should().Contain(initialJob.Properties.Tags);
            updatedJob.Properties.Description.Should().Be(initialJob.Properties.Description);
            updatedJob.Properties.Properties.Should().Contain(initialJob.Properties.Properties);

            return updatedJob;
        }
        #endregion

        #region Shared sub-test methods

        private async Task TestCancelJob(string jobName)
        {
            var allowedStatuses = new HashSet<JobStatus>() { JobStatus.Canceled, JobStatus.CancelRequested };
            var failedStatuses = new HashSet<JobStatus>() { JobStatus.Failed, JobStatus.NotResponding };
            await Controller.Cancel(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, jobName).ConfigureAwait(false);
            var result = await Controller.GetById(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, jobName).ConfigureAwait(false);

            var startPollingTime = DateTime.Now;
            // Polling for 1min at most
            while (result.Properties.Status == JobStatus.Running
                && (DateTime.Now - startPollingTime) < TimeSpan.FromMinutes(1))
            {
                Thread.Sleep(10000); // Sleep 10s every time
                result = await Controller.GetById(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, jobName).ConfigureAwait(false);
            }

            switch (result.Properties)
            {
                case CommandJob properties:
                    properties.Status.Should().Match(m => m.HasValue && allowedStatuses.Contains(m.Value));
                    break;
                case SweepJob properties:
                    properties.Status.Should().Match(m => m.HasValue && allowedStatuses.Contains(m.Value));
                    break;
                case PipelineJob properties:
                    properties.Status.Should().Match(m => m.HasValue && allowedStatuses.Contains(m.Value));
                    break;
                default:
                    throw new NotImplementedException();
            }
        }

        private async Task TestDeleteJob(string jobName)
        {
            var status = await GetJobStatus(jobName).ConfigureAwait(false);
            var isTerminal = Enum.Parse<RunStatus>(status.ToString()).IsTerminal();

            var response = await Controller.Delete(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, jobName).ConfigureAwait(false);
            response.StatusCode.Should().Be(HttpStatusCode.Accepted);
            response.Headers.Location.Should().NotBeNull();

            // Do not poll - just check if the call goes through
            var operationLocation = response.Headers.Location;
            var operationResponse = await Fixture.HttpClient.GetAsync(operationLocation).ConfigureAwait(false);

            // If the run has not terminated yet, then the operation may result in a 409 Conflict.
            if (isTerminal)
            {
                operationResponse.StatusCode.Should().BeOneOf(HttpStatusCode.Accepted, HttpStatusCode.OK);
            }
            else
            {
                operationResponse.StatusCode.Should().BeOneOf(HttpStatusCode.Conflict, HttpStatusCode.Accepted, HttpStatusCode.OK);
            }

            try
            {
                await Controller.GetById(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, jobName).ConfigureAwait(false);
                await Controller.Cancel(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, jobName).ConfigureAwait(false);
            }
            catch (ServiceInvocationException e)
            {
                e.Response.StatusCode.Should().Be(HttpStatusCode.NotFound);
            }
        }

        private async Task<Resource<JobBase>> TestGetJob(string jobName, Resource<JobBase> expectedJob)
        {
            var retrievedJob = await Controller.GetById(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, jobName).ConfigureAwait(false);

            CheckJobEquivalency(expectedJob, retrievedJob);

            return retrievedJob;
        }

        private async Task<Resource<JobBase>> TestGetJob(string jobName, Resource<JobBase> expectedJob, JobStatus expectedStatus)
        {
            var retrievedJob = await Controller.GetById(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, jobName).ConfigureAwait(false);

            await EventualConsistencyTesting.RetryUntilAssertionPasses(async () =>
            {
                var retrievedJob = await Controller.GetById(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, jobName).ConfigureAwait(false);
                retrievedJob.Properties.Status.Should().Be(expectedStatus);

                CheckJobEquivalency(expectedJob, retrievedJob);
            }).ConfigureAwait(false);

            return retrievedJob;
        }

        private async Task TestListJobsFilter(string jobName, Resource<JobBase> expectedJob, string jobtype, string tag, bool noJobsExpected)
        {
            var allJobs = await ControllerHelpers.List(skipToken => Controller.List(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, skipToken, jobtype, tag), _maxListCount).ToListAsync();

            if (noJobsExpected)
            {
                allJobs.Should().HaveCount(0);
            }
            else
            {
                allJobs.Should().NotBeEmpty();
            }

            var listedJob = allJobs.SingleOrDefault(x => x.Name == jobName);
            if (listedJob != null)
            {
                CheckJobEquivalency(expectedJob, listedJob);
            }
        }

        private async Task TestListJobs(string jobName, Resource<JobBase> expectedJob, string tag, ListViewType listViewType = ListViewType.ActiveOnly)
        {
            // tag is required because the test WS is static and so has too many jobs to list if tag isn't provided
            tag = tag ?? throw new ArgumentNullException(nameof(tag));

            // list uses index which is eventually consistent (eventually = usually sub-second, but can be higher due to regional issues, throttling, etc)
            await EventualConsistencyTesting.RetryUntilAssertionPasses(async () =>
            {
                var allJobs = await ControllerHelpers.List(skipToken => Controller.List(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, skipToken, null, tag, listViewType), _maxListCount).ToListAsync();
                var listedJob = allJobs.SingleOrDefault(x => x.Name == jobName);
                listedJob.Should().NotBeNull();

                CheckJobEquivalency(expectedJob, listedJob);
            }).ConfigureAwait(false);
        }

        private async Task<IList<Resource<JobBase>>> TestListJobs(string jobName, Resource<JobBase> expectedJob)
        {
            var allJobs = await ControllerHelpers.List(skipToken => Controller.List(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, skipToken, null, null), _maxListCount).ToListAsync();
            var listedJob = allJobs.SingleOrDefault(x => x.Name == jobName);

            if (listedJob == null)
            {
                allJobs.Should().NotBeEmpty();
            }
            else
            {
                CheckJobEquivalency(expectedJob, listedJob);
            }

            return allJobs;
        }

        private async Task TestListJobsFilterType(string jobName, Resource<JobBase> expectedJob, JobType jobtype, bool jobExpected)
        {
            var allJobs = await ControllerHelpers.List(skipToken => Controller.List(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, skipToken, jobtype.ToString(), null), _maxListCount).ToListAsync();

            if (!jobExpected)
            {
                allJobs.Should().NotBeEmpty();
            }

            foreach (Resource<JobBase> job in allJobs?.EmptyIfNull())
            {
                job.Properties.JobType.Should().Be(jobtype);
            }

            var listedJob = allJobs.SingleOrDefault(x => x.Name == jobName);
            if (listedJob != null)
            {
                CheckJobEquivalency(expectedJob, listedJob);
            }
        }

        private Resource<JobBase> SetJobProperties(Resource<JobBase> job)
        {
            job.Properties.Tags = new Dictionary<string, string>
            {
                { "new-tag-name", "new-tag-value" },
            };
            job.Properties.Description = Guid.NewGuid().ToString();
            return job;
        }

        private async Task<JobStatus> GetJobStatus(string jobName)
        {
            var retrievedJob = await Controller.GetById(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, jobName).ConfigureAwait(false);

            switch (retrievedJob.Properties)
            {
                case CommandJob properties:
                    return properties.Status;
                case SweepJob properties:
                    return properties.Status;
                case PipelineJob properties:
                    return properties.Status;
                default:
                    throw new NotImplementedException();
            }
        }

        private async Task<Dictionary<string, JobInput>> CreateJobInput(TestFixtureManagementFrontEnd fixture)
        {
            // TODO: Many of these uri  inputs don't work, job eventually fails.
            return CreateLiteralJobInput()
                .CreateMerged(CreateUriFileJobInput(fixture))
                .CreateMerged(CreateUriFolderJobInput(fixture))
                .CreateMerged(CreateMLFlowModelJobInput(fixture))
                .CreateMerged(CreateMLTableJobInput(fixture))
                .CreateMerged(await CreateArmIdJobInput().ConfigureAwait(false));
        }

        private Dictionary<string, JobInput> GetRegistryAssetJobInput()
        {
            // Registry Arm Scope: /subscriptions/ad203158-bc5d-4e72-b764-2607833a71dc/resourceGroups/mir-v2-runners-centraluseuap/providers/Microsoft.MachineLearningServices/registries/UnsecureTest-mirv2reg
            // MIR registry E2E tests also use same registry.
            var customModel = new CustomModelJobInput
            {
                Uri = "azureml://registries/UnsecureTest-mirv2reg/models/sklearn_model_with_path/versions/1"
            };

            var uriMount = new Dictionary<string, JobInput>()
            {
                { "customRegistryModel", customModel },
            };

            return CreateLiteralJobInput().CreateMerged(uriMount);
        }

        private Dictionary<string, JobInput> CreateLiteralJobInput()
        {
            var inputDataBinding = new LiteralJobInput()
            {
                Value = "42"
            };

            return new Dictionary<string, JobInput>() { { "literalJobInput", inputDataBinding } };
        }

        private Dictionary<string, JobInput> CreateUriFileJobInput(TestFixtureManagementFrontEnd fixture)
        {
            var path = MfeTestingConstants.ModelVersionBlobPath[..MfeTestingConstants.ModelVersionBlobPath.LastIndexOf('/')];
            var armScopePath = new UriFileJobInput()
            {
                Uri = $"azureml://subscriptions/{fixture.SubscriptionId}/resourcegroups/{fixture.ResourceGroupName}/workspaces/{fixture.WorkspaceName}/datastores/workspaceblobstore/paths/{path}"
            };
            var datastorePath = new UriFileJobInput()
            {
                Uri = $"azureml://datastores/workspaceblobstore/paths/{path}"
            };
            var externalPath = new UriFileJobInput()
            {
                Uri = "https://dprepdata.blob.core.windows.net/demo/Titanic.csv"
            };

            return new Dictionary<string, JobInput>()
            {
                { "uriFileJobInputArmScopePath", armScopePath },
                { "uriFileJobInputDatastorePath", datastorePath },
                { "uriFileJobInputExternalPath", externalPath }
            };
        }

        private Dictionary<string, JobInput> CreateUriFolderJobInput(TestFixtureManagementFrontEnd fixture)
        {
            var path = MfeTestingConstants.ModelVersionBlobPath[..MfeTestingConstants.ModelVersionBlobPath.LastIndexOf('/')];
            var armScopePath = new UriFolderJobInput()
            {
                Uri = $"azureml://subscriptions/{fixture.SubscriptionId}/resourcegroups/{fixture.ResourceGroupName}/workspaces/{fixture.WorkspaceName}/datastores/workspaceblobstore/paths/{path}"
            };
            var datastorePath = new UriFolderJobInput()
            {
                Uri = $"azureml://datastores/workspaceblobstore/paths/{path}"
            };
            var externalPath = new UriFolderJobInput()
            {
                Uri = "https://dprepdata.blob.core.windows.net/demo/Titanic.csv"
            };

            return new Dictionary<string, JobInput>()
            {
                { "uriFolderJobInputArmScopePath", armScopePath },
                { "uriFolderJobInputDatastorePath", datastorePath },
                { "uriFolderJobInputExternalPath", externalPath },
            };
        }

        private Dictionary<string, JobInput> CreateMLTableJobInput(TestFixtureManagementFrontEnd fixture)
        {
            var path = MfeTestingConstants.ModelVersionBlobPath[..MfeTestingConstants.ModelVersionBlobPath.LastIndexOf('/')];
            var armScopePath = new MLTableJobInput()
            {
                Uri = $"azureml://subscriptions/{fixture.SubscriptionId}/resourcegroups/{fixture.ResourceGroupName}/workspaces/{fixture.WorkspaceName}/datastores/workspaceblobstore/paths/{path}"
            };
            var datastorePath = new MLTableJobInput()
            {
                Uri = $"azureml://datastores/workspaceblobstore/paths/{path}"
            };
            var externalPath = new MLTableJobInput()
            {
                Uri = "https://dprepdata.blob.core.windows.net/demo/Titanic.csv"
            };
            return new Dictionary<string, JobInput>()
            {
                { "mLTableJobInputArmScopePath", armScopePath },
                { "mLTableJobInputDatastorePath", datastorePath },
                { "mLTableJobInputExternalPath", externalPath },
            };
        }

        private async Task<Dictionary<string, JobInput>> CreateArmIdJobInput()
        {
            // omitting datasetArmId in test case here as it's covered in Unit Tests in JobConverterTests.TestInputAssetAssetUriArmIdShouldNotThrowValidationError
            // <see cref="JobConverterTests"/>
            var name = E2EModelControllerTests.GenerateContainerName(DateTime.UtcNow);
            var uriFolderName = E2EModelControllerTests.GenerateContainerName(DateTime.UtcNow) + "_uriFolder";
            var uriFileName = E2EModelControllerTests.GenerateContainerName(DateTime.UtcNow) + "_uriFile";

            var version = "123";
            var request = await E2EModelControllerTests.GenerateVersion(DataStoreController, Fixture, ModelType.mlflow_model.ToString()).ConfigureAwait(false);

            var modelVersion = await ModelVersionController.CreateOrUpdate(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, name, version, request).ConfigureAwait(false);

            var uriFileDataContainerRequest = new Resource<DataContainer>()
            {
                Properties = new DataContainer()
                {
                    DataType = DataType.uri_file
                }
            };
            await DataContainerController.CreateOrUpdate(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, uriFileName, uriFileDataContainerRequest).ConfigureAwait(false);
            var uriFileDataRequest = new Resource<DataVersionBase>()
            {
                Properties = new UriFileDataVersion()
                {
                    DataUri = "https://dprepdata.blob.core.windows.net/demo/Titanic.csv",
                }
            };
            var uriFileDataVersion = await DataVersionController.CreateOrUpdate(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, uriFileName, version, uriFileDataRequest).ConfigureAwait(false);

            var uriFolderDataContainerRequest = new Resource<DataContainer>()
            {
                Properties = new DataContainer()
                {
                    DataType = DataType.uri_folder
                }
            };
            await DataContainerController.CreateOrUpdate(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, uriFolderName, uriFolderDataContainerRequest).ConfigureAwait(false);
            var uriFolderDataRequest = new Resource<DataVersionBase>()
            {
                Properties = new UriFolderDataVersion()
                {
                    DataUri = "wasbs://<EMAIL>/",
                }
            };
            var uriFolderDataVersion = await DataVersionController.CreateOrUpdate(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, uriFolderName, version, uriFolderDataRequest).ConfigureAwait(false);
            return new Dictionary<string, JobInput>()
            {
                {
                    "modelArmId",
                    new MLFlowModelJobInput()
                    {
                        Uri = modelVersion.Id
                    }
                },
                {
                    "uriFileDataArmId",
                    new UriFileJobInput()
                    {
                        Uri = uriFileDataVersion.Id
                    }
                },
                {
                    "uriFolderDataArmId",
                    new UriFolderJobInput()
                    {
                        Uri = uriFolderDataVersion.Id
                    }
                },
            };
        }

        // TODO: Add additional paths when Model Registry supports them
        private Dictionary<string, JobInput> CreateMLFlowModelJobInput(TestFixtureManagementFrontEnd fixture)
        {
            var path = MfeTestingConstants.ModelVersionBlobPath[..MfeTestingConstants.ModelVersionBlobPath.LastIndexOf('/')];
            var armScopePath = new MLFlowModelJobInput()
            {
                Uri = $"azureml://subscriptions/{fixture.SubscriptionId}/resourcegroups/{fixture.ResourceGroupName}/workspaces/{fixture.WorkspaceName}/datastores/workspaceblobstore/paths/{path}"
            };
            return new Dictionary<string, JobInput>()
            {
                { "mLFlowModelJobInputArmScopePath", armScopePath }
            };
        }
        #endregion
    }

    [Collection(nameof(ControllerIsolatedTestsCollection))]
    public class IsolatedJobControllerTests : JobControllerTests<TestFixtureIsolatedManagementFrontEnd>
    {
        public IsolatedJobControllerTests(TestFixtureIsolatedManagementFrontEnd fixture)
            : base(fixture, fixture.GetFakeS2SClient<IArtifactControllerWorkspaceV2Routes>())
        {
            fixture.ResetFakeS2SClients();
        }

        [Fact]
        [MfeTest(TestCategory.Component, ScenarioScope.Command)]
        public async Task TestCreateAndUpdateCommandJob()
        {
            var (id, job) = CommandJobHelpers.GetTestMockCommandJob(Fixture);

            // Exclude data bindings from this test
            job.Inputs.Clear();
            job.Outputs?.Clear();

            var (_, modifiedJob) = CommandJobHelpers.GetTestMockCommandJob(Fixture, id, job);
            modifiedJob.Tags = new Dictionary<string, string>(job.Tags);
            modifiedJob.Tags.Add(Guid.NewGuid().ToString(), Guid.NewGuid().ToString());

            CommandJobHelpers.ConfigureFakesForCreateThenUpdate(id, job, modifiedJob, Fixture);

            var result = await Controller.CreateOrUpdateJob(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, id, new Resource<JobBase>() { Properties = job }).ConfigureAwait(false);

            ValidateCommandJob(result, job);

            result = await Controller.CreateOrUpdateJob(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, id, new Resource<JobBase>() { Properties = modifiedJob }).ConfigureAwait(false);

            ValidateCommandJob(result, modifiedJob);
        }

        [Fact]
        [MfeTest(TestCategory.Component, ScenarioScope.Command)]
        public async Task TestCreateAndUpdateCommandJobWitUserIdentity()
        {
            var (id, job) = CommandJobHelpers.GetTestMockCommandJob(Fixture, copyJob: new CommandJob { Identity = new UserIdentity() });
            // Exclude data bindings from this test
            job.Inputs.Clear();
            job.Outputs?.Clear();

            var (_, modifiedJob) = CommandJobHelpers.GetTestMockCommandJob(Fixture, id, job);
            modifiedJob.Tags = new Dictionary<string, string>(job.Tags);
            modifiedJob.Tags.Add(Guid.NewGuid().ToString(), Guid.NewGuid().ToString());

            CommandJobHelpers.ConfigureFakesForCreateThenUpdate(id, job, modifiedJob, Fixture);

            var result = await Controller.CreateOrUpdateJob(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, id, new Resource<JobBase>() { Properties = job }).ConfigureAwait(false);

            ValidateCommandJob(result, job);

            result = await Controller.CreateOrUpdateJob(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, id, new Resource<JobBase>() { Properties = modifiedJob }).ConfigureAwait(false);

            ValidateCommandJob(result, modifiedJob);
        }

        [Fact]
        [MfeTest(TestCategory.Component, ScenarioScope.Command)]
        public async Task TestCommandJobGetValidateRunConfig()
        {
            var (id, job) = CommandJobHelpers.GetTestMockCommandJob(Fixture);

            // Exclude data bindings from this test
            job.Inputs.Clear();
            job.Outputs?.Clear();

            var (_, modifiedJob) = CommandJobHelpers.GetTestMockCommandJob(Fixture, id, job);
            modifiedJob.Tags = new Dictionary<string, string>(job.Tags);
            modifiedJob.Tags.Add(Guid.NewGuid().ToString(), Guid.NewGuid().ToString());

            // purposefully don't return JobSpec to test RunConfig pathway
            CommandJobHelpers.ConfigureFakesForGet(id, job, Fixture);

            var result = await Controller.GetById(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, id).ConfigureAwait(false);

            ValidateCommandJob(result, job);
        }

        [Fact]
        [MfeTest(TestCategory.Component, ScenarioScope.Command)]
        public async Task TestCommandJobGetDatasetObjectConversionFailsGracefully()
        {
            var (id, job) = CommandJobHelpers.GetTestMockCommandJob(Fixture);
            var withInvalidData = true; // test dataset usage
            // Exclude data bindings from this test
            job.Inputs.Clear();
            job.Outputs?.Clear();

            var (_, modifiedJob) = CommandJobHelpers.GetTestMockCommandJob(Fixture, id, job);
            modifiedJob.Tags = new Dictionary<string, string>(job.Tags);
            modifiedJob.Tags.Add(Guid.NewGuid().ToString(), Guid.NewGuid().ToString());

            // configure RunConfig to have Dataset
            CommandJobHelpers.ConfigureFakesForGet(id, job, Fixture, withInvalidData);

            var result = await Controller.GetById(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, id).ConfigureAwait(false);

            ValidateCommandJob(result, job);
        }

        [Fact]
        [MfeTest(TestCategory.Component, ScenarioScope.Command)]
        public async Task TestCreateAndUpdateCommandJobNoEnvironmentIdAsync()
        {
            var (id, job) = CommandJobHelpers.GetTestMockCommandJob(Fixture);

            // Exclude data bindings from this test
            job.Inputs.Clear();
            job.Outputs?.Clear();
            job.EnvironmentId = null;

            Func<Task> func = async () => await Controller.CreateOrUpdateJob(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, id, new Resource<JobBase>() { Properties = job }).ConfigureAwait(false);
            await func.Should().ThrowAsync<ServiceInvocationException>()
                .Where(ex => ex.Response.StatusCode == HttpStatusCode.BadRequest
                    && ex.ErrorResponse.Error.Code == "UserError"
                    && ex.ErrorResponse.Error.Message.Equals("Request is invalid and/or missing fields.", StringComparison.InvariantCultureIgnoreCase)
                    && ex.ErrorResponse.Error.Details.Any(detail => detail.Message.Equals("The EnvironmentId field is required.", StringComparison.InvariantCultureIgnoreCase)));
        }

        [Fact]
        [MfeTest(TestCategory.Component, ScenarioScope.Command)]
        public async Task TestCreateAndUpdateCommandJobNoExperimentName()
        {
            var (id, job) = CommandJobHelpers.GetTestMockCommandJob(Fixture);

            // Exclude data bindings from this test
            job.Inputs.Clear();
            job.Outputs?.Clear();

            var (_, modifiedJob) = CommandJobHelpers.GetTestMockCommandJob(Fixture, id, job);
            modifiedJob.Tags = new Dictionary<string, string>(job.Tags);
            modifiedJob.Tags.Add(Guid.NewGuid().ToString(), Guid.NewGuid().ToString());

            CommandJobHelpers.ConfigureFakesForCreateThenUpdate(id, job, modifiedJob, Fixture);
            job.ExperimentName = null;

            var result = await Controller.CreateOrUpdateJob(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, id, new Resource<JobBase>() { Properties = job }).ConfigureAwait(false);

            job.ExperimentName = MfeConstants.DefaultExperimentName;
            ValidateCommandJob(result, job);

            result = await Controller.CreateOrUpdateJob(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, id, new Resource<JobBase>() { Properties = modifiedJob }).ConfigureAwait(false);

            ValidateCommandJob(result, modifiedJob);
        }

        [Fact]
        [MfeTest(TestCategory.Component, ScenarioScope.Command)]
        public async Task FuzzCreateCommandJob()
        {
            var (id, job) = CommandJobHelpers.GetTestMockCommandJob(Fixture);
            job.Inputs.Clear();

            var fuzzer = new ControllerFuzzer(Fixture, () => CommandJobHelpers.ConfigureFakesForCreateThenUpdate(id, job, job, Fixture));

            await fuzzer.FuzzControllerMethod<IJobController>(controller => controller.CreateOrUpdateJob(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, id, new Resource<JobBase> { Properties = job })).ConfigureAwait(false);
        }

        private void ValidateCommandJob(Resource<JobBase> result, CommandJob expected, string expectedId = null)
        {
            if (expectedId != null)
            {
                result.Id.Should().EndWith(expectedId);
                result.Name.Should().Be(expectedId);
            }
            var commandJob = (CommandJob)result.Properties;
            commandJob.Should().BeEquivalentTo(expected, ExcludeProperties);
            commandJob.Properties.Should().Contain(expected.Properties);
            commandJob.Services[JobServiceType.Studio.ToString()].Should().NotBeNull();
            commandJob.Services[JobServiceType.Tracking.ToString()].Should().NotBeNull();
        }

        private EquivalencyAssertionOptions<CommandJob> ExcludeProperties(EquivalencyAssertionOptions<CommandJob> options)
        {
            options.Excluding(t => t.Status);
            options.Excluding(t => t.Services);
            options.Excluding(t => t.Properties);
            options.Excluding(t => t.Parameters);
            options.Excluding(t => t.Outputs);
            options.UsingCaseInsensitiveProperties();
            return options;
        }
    }

    public abstract class JobControllerTests<TFixture>
        where TFixture : TestFixtureManagementFrontEnd
    {
        public JobControllerTests(TFixture fixture, IArtifactControllerWorkspaceV2Routes artifactController)
        {
            Fixture = fixture;

            Controller = Fixture.ManagementFrontEndClient.GetController<IJobController>(
                beforeRequest: (request) =>
                {
                    request.Headers.Add("x-azureml-token", "fake_aml_token");
                    return Task.CompletedTask;
                });

            CodeVersionController = Fixture.ManagementFrontEndClient.GetController<ICodeVersionController>();

            ArtifactController = artifactController;

            RunHistoryClient = ServiceInvoker.Create<IExperimentAgnosticRunController>(fixture.HttpClient, new Uri(fixture.GetHostService<IOptions<ExperimentationHostConfiguration>>().Value.ExperimentationHostUri));

            ModelVersionController = Fixture.ManagementFrontEndClient.GetController<IModelVersionController>();

            DataVersionController = Fixture.ManagementFrontEndClient.GetController<IDataVersionController>();

            DataContainerController = Fixture.ManagementFrontEndClient.GetController<IDataContainerController>();

            DataStoreController = ServiceInvoker.Create<IDataStoreController>(
                fixture.HttpClient,
                new Uri(fixture.GetHostService<IOptions<ExperimentationHostConfiguration>>().Value.ExperimentationHostUri));
        }

        public IJobController Controller { get; }

        public TFixture Fixture { get; }

        public ISweepJobConverter Converter { get; }

        public IArtifactControllerWorkspaceV2Routes ArtifactController { get; }

        public ICodeVersionController CodeVersionController { get; }

        public IExperimentAgnosticRunController RunHistoryClient { get; }

        protected IModelVersionController ModelVersionController { get; }

        protected IDataVersionController DataVersionController { get; }

        protected IDataContainerController DataContainerController { get; }

        protected IDataStoreController DataStoreController { get; }

        protected async Task TestPipelineJobAcrossApiVersions(string name)
        {
            await CrossVersionHelpers.TestResourceAcrossApiVersions<IJobController>(
                    Fixture,
                    controller => controller.GetById(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, name),
                    version => version == MfeApiVersions.May2022)
                .ConfigureAwait(false);
        }

        protected async Task TestAutoMLJobAcrossApiVersions(string name)
        {
            await CrossVersionHelpers.TestResourceAcrossApiVersions<IJobController>(
                    Fixture,
                    controller => controller.GetById(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, name),
                    version => version >= MfeApiVersions.May2022,
                    tryPut: false) // Invalid compute target in AutoML job response: https://msdata.visualstudio.com/Vienna/_workitems/edit/1152466
                .ConfigureAwait(false);
        }
    }
}
