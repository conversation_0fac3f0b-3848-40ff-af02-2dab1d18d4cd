﻿// <copyright file="DataVersionControllerTests.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using FakeItEasy;
using FluentAssertions;
using FluentAssertions.Extensions;
using Microsoft.Extensions.Options;
using Microsoft.MachineLearning.Artifact.Contracts;
using Microsoft.MachineLearning.Common.Core;
using Microsoft.MachineLearning.Common.Core.Contracts;
using Microsoft.MachineLearning.Common.Testing;
using Microsoft.MachineLearning.Common.WebApi.Client;
using Microsoft.MachineLearning.Common.WebApi.TeamAccounts;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20211001.Assets;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20211001.Controllers;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20220501.ArmCommon;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20220501.Assets;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20220501.Controllers;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Api;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.ControllerTests.NonVersionedHelpers;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.ControllerTests.V20211001.Helpers;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.ControllerTests.V20220501.Helpers;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.XunitExtensions;
using Microsoft.MachineLearning.ManagementFrontEnd.Services.Extensions;
using Xunit;
using ListViewType = Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20220501.Assets.ListViewType;

namespace Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.ControllerTests.V20220501;

[MfeTest(TestCategory.Integration, ScenarioScope.Data)]
public class E2EDataVersionControllerTests : DataVersionControllerTests<TestFixtureManagementFrontEnd>, IStaticFixture<TestFixtureManagementFrontEnd>
{
    public E2EDataVersionControllerTests(StaticFixture<TestFixtureManagementFrontEnd> fixture)
        : base(fixture, ServiceInvoker.Create<IArtifactControllerWorkspaceV2Routes>(fixture.Instance.HttpClient, new Uri(fixture.Instance.GetHostService<IOptions<ExperimentationHostConfiguration>>().Value.ExperimentationHostUri)))
    {
    }
}

public abstract class DataVersionControllerTests<TFixture>
    where TFixture : TestFixtureManagementFrontEnd
{
    private const int _maxListCount = 20;

    private readonly DataAssetHelpers _dataAssetHelpers;

    public DataVersionControllerTests(TFixture fixture, IArtifactControllerWorkspaceV2Routes artifactController)
    {
        Fixture = fixture;
        _dataAssetHelpers = new DataAssetHelpers(new WorkspaceContext2(Fixture.WorkspaceId, Fixture.WorkspaceName, Fixture.ResourceGroupName, Fixture.SubscriptionId, "test", "test", new Dictionary<string, string>()), artifactController);
        DataContainerController = Fixture.ManagementFrontEndClient.GetController<IDataContainerController>();
        DataVersionController = Fixture.ManagementFrontEndClient.GetController<IDataVersionController>();
        DatasetVersionController = Fixture.ManagementFrontEndClient.GetController<IDatasetVersionController>();
    }

    public IDataContainerController DataContainerController { get; }

    public IDataVersionController DataVersionController { get; }

    public IDatasetVersionController DatasetVersionController { get; }

    public TFixture Fixture { get; }

    [Fact]
    public async Task TestDataVersionCreateUpdateGetListDelete()
    {
        string datasetName = Guid.NewGuid().ToString();
        string datasetVersion = "125";
        var tagsAfter = new Dictionary<string, string>() { { "mytag1", "value1" }, { "tag2", "value2" }, { "tag3", "value3" } };

        // includes get
        var createdDataset = await _dataAssetHelpers.RegisterDataVersion(DataVersionController, datasetName, datasetVersion).ConfigureAwait(false);

        // update Tags & Description
        createdDataset.Properties.Description = "Updated: " + createdDataset.Properties.Description;
        createdDataset.Properties.Tags = tagsAfter;
        await UpdateDataVersion(createdDataset, datasetName, datasetVersion).ConfigureAwait(false);

        // list with all Data Types
        string datasetVersion2 = "126";
        // versioned create with Datastore
        var createdDatastore = await _dataAssetHelpers.RegisterDataVersion(DataVersionController, datasetName, datasetVersion2, useDatastore: true).ConfigureAwait(false);

        string notListedDatasetName = "TestMyNameisDifferent" + Guid.NewGuid().ToString();
        var notListedDataset = await _dataAssetHelpers.RegisterDataVersion(DataVersionController, notListedDatasetName, datasetVersion2).ConfigureAwait(false);

        await EventualConsistencyTesting.RetryUntilAssertionPasses(async () =>
        {
            var datasetList = await ListDataVersions(datasetName).ConfigureAwait(false);
            datasetList.Count.Should().BeGreaterOrEqualTo(2);
            datasetList.Should().NotContain(x => x.Id == notListedDataset.Id);

            // get latest
            // TODO: currently the List API doesn't support GetLatest by set-up the count as 1;
            var datasetLatest = await ListDataVersions(datasetName, orderBy: "createdtime desc").ConfigureAwait(false);
            datasetLatest.Count.Should().Be(2);
            datasetLatest.FirstOrDefault().Id.Should().BeEquivalentTo(createdDatastore.Id);
        }).ConfigureAwait(false);

        // Cross-version calls.
        await TestAcrossApiVersions(datasetName, datasetVersion).ConfigureAwait(false);

        // delete
        // TODO: delete has not been implemented in service side
        Func<Task> task = async () => await DataVersionController.Delete(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, datasetName, datasetVersion).ConfigureAwait(false);
        await task.Should().ThrowAsync<ServiceInvocationException>()
            .Where(ex => ex.Response.StatusCode == HttpStatusCode.BadRequest);
    }

    [Fact]
    public async Task TestDataVersionUpdateInvalidChanges()
    {
        string datasetName = Guid.NewGuid().ToString();
        string datasetVersion = "1";
        var createdDataset = await _dataAssetHelpers.RegisterDataVersion(DataVersionController, datasetName, datasetVersion).ConfigureAwait(false);

        createdDataset.Properties = new MLTableData()
        {
            DataUri = "azureml://datastores/workspaceblobstore/paths/data/mltable",
            IsAnonymous = true,
            IsArchived = createdDataset.Properties.IsArchived,
            Description = createdDataset.Properties.Description,
            Tags = createdDataset.Properties.Tags,
            Properties = createdDataset.Properties.Properties,
        };
        var updateTask = async () => await UpdateDataVersion(createdDataset, datasetName, datasetVersion).ConfigureAwait(false);

        await updateTask.Should().ThrowAsync<ServiceInvocationException>()
            .Where(e => e.Response.StatusCode == HttpStatusCode.BadRequest)
            .Where(e => e.Message.Contains("data version", StringComparison.InvariantCultureIgnoreCase))
            .Where(e => e.Message.Contains("the existing asset's data type, data uri, is anonymous cannot be changed. Only tags, description, and isArchived can be updated.", StringComparison.InvariantCultureIgnoreCase))
            .ConfigureAwait(false);
    }

    [Fact]
    public async Task TestDatasetVersionUpdateUseCases()
    {
        // test different update assumptions
        var testWorkspace = new WorkspaceContext2(Fixture.WorkspaceId, Fixture.WorkspaceName, Fixture.ResourceGroupName, Fixture.SubscriptionId, "test", "test", new Dictionary<string, string>());
        string datasetName = Guid.NewGuid().ToString();
        string datasetVersion = "987373";
        string datasetVersion2 = "987374";
        string datasetVersion3 = "987384";

        var createdDataset = await _dataAssetHelpers.RegisterDataVersion(DataVersionController, datasetName, datasetVersion).ConfigureAwait(false);
        // to validate it updates right version
        var createdDataset2 = await _dataAssetHelpers.RegisterDataVersion(DataVersionController, datasetName, datasetVersion2).ConfigureAwait(false);

        // update Tags
        var tagsAfter = new Dictionary<string, string>() { { "tag1", "value1" }, { "tag2", "value2" }, { "tag3", "value3" } };
        createdDataset.Properties.Tags = tagsAfter;
        await UpdateDataVersion(createdDataset, datasetName, datasetVersion).ConfigureAwait(false);

        // update Description
        createdDataset.Properties.Description = "Updated: " + createdDataset.Properties.Description;
        await UpdateDataVersion(createdDataset, datasetName, datasetVersion).ConfigureAwait(false);

        // update Both Tags & Description
        createdDataset.Properties.Description = "Updated: Tags and Description";
        var tagsAfter2 = new Dictionary<string, string>() { { "tag4", "value4" }, { "tag5", "value5" }, { "tag6", "value6" } };
        createdDataset.Properties.Tags = tagsAfter2;
        await UpdateDataVersion(createdDataset, datasetName, datasetVersion).ConfigureAwait(false);

        // validate right version was updated
        var getDatasetUpdated = await DataVersionController.Get(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, datasetName, datasetVersion).ConfigureAwait(false);
        _dataAssetHelpers.DataVersionsAreEqual(createdDataset.Properties, getDatasetUpdated.Properties);
        var getDatasetUpdated2 = await DataVersionController.Get(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, datasetName, datasetVersion2).ConfigureAwait(false);
        _dataAssetHelpers.DataVersionsAreEqual(createdDataset2.Properties, getDatasetUpdated2.Properties);

        // versioning update - can update properties if you provide a new version
        var properties = new Dictionary<string, string>() { { "prop4", "value4" }, { "prop5", "value5" }, { "prop6", "value6" } };
        createdDataset.Properties.Properties = properties;
        await UpdateDataVersion(createdDataset, datasetName, datasetVersion3).ConfigureAwait(false);

        // NO-OP update - should not throw error
        await UpdateDataVersion(createdDataset, datasetName, datasetVersion3).ConfigureAwait(false);

        // TODO: update immutable properties should throw
        // update paths - should throw exception
        // createdDataset.Properties.DataUri = "https://dprepdata.blob.core.windows.net/demo/";

        // should throw exception
        // Func<Task> task = async () => await UpdateDataVersion(createdDataset, datasetName, datasetVersion3).ConfigureAwait(false);
        // (await task.Should().ThrowAsync<Exception>()).Where(m => m.Message.Contains("the existing asset's Paths cannot be changed. Only tags and description can be updated.", StringComparison.OrdinalIgnoreCase));
    }

    [Fact]
    public async Task TestDataVersionList()
    {
        var testWorkspace = new WorkspaceContext2(Fixture.WorkspaceId, Fixture.WorkspaceName, Fixture.ResourceGroupName, Fixture.SubscriptionId, "test", "test", new Dictionary<string, string>());
        var datasetName = Guid.NewGuid().ToString();
        var datasetVersion = "25";
        var datasetVersion2 = "26";

        // List dataVersion not exists will throw 404
        var task = () => ListDataVersions(datasetName);
        await task.Should().ThrowAsync<ServiceInvocationException>()
            .Where(ex => ex.Response.StatusCode == HttpStatusCode.NotFound);

        // List dataVersion after registering one version, should return one version
        var dataVersion1 = await _dataAssetHelpers.RegisterDataVersion(DataVersionController, datasetName, datasetVersion).ConfigureAwait(false);
        await EventualConsistencyTesting.RetryUntilAssertionPasses(async () =>
        {
            var dataVersions = await ListDataVersions(datasetName).ConfigureAwait(false);
            dataVersions.Should().ContainSingle();
        }).ConfigureAwait(false);

        // List dataVersion ordered by createdTime, should return v26 and v25;
        var dataVersion2 = await _dataAssetHelpers.RegisterDataVersion(DataVersionController, datasetName, datasetVersion2).ConfigureAwait(false);
        await EventualConsistencyTesting.RetryUntilAssertionPasses(async () =>
        {
            var dataVersions = await ListDataVersions(datasetName, orderBy: "createdtime desc").ConfigureAwait(false);
            dataVersions.Should().HaveCount(2);
            dataVersions[0].Id.Should().BeEquivalentTo(dataVersion2.Id);
            dataVersions[1].Id.Should().BeEquivalentTo(dataVersion1.Id);
        }).ConfigureAwait(false);

        // update Tags
        var tagsAfter = new Dictionary<string, string>() { { "tag1", "value1" }, { "tag2", "value2" }, { "tag3", "value3" } };
        dataVersion1.Properties.Tags = tagsAfter;
        await UpdateDataVersion(dataVersion1, datasetName, datasetVersion).ConfigureAwait(false);
        await EventualConsistencyTesting.RetryUntilAssertionPasses(async () =>
        {
            // List dataVersion ordered by modifiedTime, should return v25 and v26;
            var dataVersions = await ListDataVersions(datasetName, orderBy: "modifiedTime desc").ConfigureAwait(false);
            dataVersions.Should().HaveCount(2);
            dataVersions[0].Id.Should().BeEquivalentTo(dataVersion1.Id);
            dataVersions[1].Id.Should().BeEquivalentTo(dataVersion2.Id);
        }).ConfigureAwait(false);

        await EventualConsistencyTesting.RetryUntilAssertionPasses(async () =>
        {
            // List dataVersion top=1
            var dataVersions = await ListDataVersions(datasetName, top: 1).ConfigureAwait(false);
            dataVersions.Should().HaveCount(1);

            // List dataVersion tags
            dataVersions = await ListDataVersions(datasetName, tags: "tag1").ConfigureAwait(false);
            dataVersions.Should().HaveCount(1);
            dataVersions = await ListDataVersions(datasetName, tags: "tag1,tag2").ConfigureAwait(false);
            dataVersions.Should().HaveCount(1);
            dataVersions = await ListDataVersions(datasetName, tags: "nonExistingTags").ConfigureAwait(false);
            dataVersions.Should().HaveCount(0);
        }).ConfigureAwait(false);

        // update IsArchived flag
        dataVersion1.Properties.IsArchived = true;
        await UpdateDataVersion(dataVersion1, datasetName, datasetVersion).ConfigureAwait(false);
        await EventualConsistencyTesting.RetryUntilAssertionPasses(async () =>
        {
            var dataVersionsActiveOnly = await ListDataVersions(datasetName).ConfigureAwait(false);
            dataVersionsActiveOnly.Should().HaveCount(1);
            var dataVersionsAll = await ListDataVersions(datasetName, listViewType: ListViewType.All).ConfigureAwait(false);
            dataVersionsAll.Should().HaveCount(2);
            var dataVersionsArchivedOnly = await ListDataVersions(datasetName, listViewType: ListViewType.ArchivedOnly).ConfigureAwait(false);
            dataVersionsArchivedOnly.Should().HaveCount(1);
            dataVersionsArchivedOnly[0].Id.Should().BeEquivalentTo(dataVersion1.Id);
        }).ConfigureAwait(false);
    }

    [Fact]
    public async Task TestDataVersionFromDatastoreCreateGetUpdate()
    {
        var testWorkspace = new WorkspaceContext2(Fixture.WorkspaceId, Fixture.WorkspaceName, Fixture.ResourceGroupName, Fixture.SubscriptionId, "test", "test", new Dictionary<string, string>());
        string datasetName = Guid.NewGuid().ToString();
        string datasetVersion = "123";

        // includes get
        var createdDataset = await _dataAssetHelpers.RegisterDataVersion(DataVersionController, datasetName, datasetVersion, useDatastore: true).ConfigureAwait(false);

        // update
        createdDataset.Properties.Description = "Updated Description";
        createdDataset.Properties.Tags = new Dictionary<string, string>() { { "mytag4", "woah" }, { "mytag5", "cool" }, { "mytag6", "tags" } };
        await UpdateDataVersion(createdDataset, datasetName, datasetVersion).ConfigureAwait(false);
    }

    [Fact]
    public async Task TestGetSetReferencedUris()
    {
        string datasetName = Guid.NewGuid().ToString();
        string datasetVersion = "433";
        string dataUri = "azureml://datastores/some_datastore/paths/data/mydata";
        string expectedDataUri = "azureml://datastores/some_datastore/paths/data/mydata/";

        var inputPublicBlobDataset = new MLTableData()
        {
            Description = "created for testing",
            Properties = _dataAssetHelpers.InitialProperties,
            Tags = _dataAssetHelpers.InitialTags,
            DataUri = dataUri,
            ReferencedUris = new List<string>
            {
                "azureml://datastores/some_datastore/paths/data/mydata",
                "azureml://subscriptions/something/resourcegroups/something/workspaces/something/datastores/something/paths/data/mydata",
                "https://link3",
                "https://link4",
                "https://link5"
            }
        };

        Resource<DataVersionBase> dataVersionResource = _dataAssetHelpers.ConvertToArmResourceDto(inputPublicBlobDataset, datasetName, datasetVersion);
        dataVersionResource.Properties.Tags = dataVersionResource.Properties.Tags;

        var createdDataset = await DataVersionController.CreateOrUpdate(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, datasetName, datasetVersion, dataVersionResource).ConfigureAwait(false);
        inputPublicBlobDataset.DataUri = expectedDataUri;
        _dataAssetHelpers.DataVersionsAreEqual(createdDataset.Properties, inputPublicBlobDataset);

        var getDataset = await DataVersionController.Get(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, datasetName, datasetVersion).ConfigureAwait(false);
        _dataAssetHelpers.DataVersionsAreEqual(getDataset.Properties, inputPublicBlobDataset);
    }

    [Fact]
    public async Task TestGetV1Dataset()
    {
        string datasetName = Guid.NewGuid().ToString();
        string datasetVersion = "125";
        var createdV1Dataset = await DatasetVersionHelpers.CreateAndRegisterDatasetPublicBlob(Fixture, DatasetVersionController, datasetName, datasetVersion).ConfigureAwait(false);
        var v1Dataset = await DataVersionController.Get(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, datasetName, datasetVersion).ConfigureAwait(false);
        CheckV1Dataset(v1Dataset, createdV1Dataset);
    }

    [Fact]
    public async Task TestListV1Dataset()
    {
        string datasetName = Guid.NewGuid().ToString();
        string datasetVersion = "424";
        var createdDataset = await DatasetVersionHelpers.CreateAndRegisterDatasetPublicBlob(Fixture, DatasetVersionController, datasetName, datasetVersion).ConfigureAwait(false);

        await EventualConsistencyTesting.RetryUntilAssertionPasses(async () =>
        {
            var dataVersions = await ListDataVersions(datasetName, orderBy: "createdtime desc").ConfigureAwait(false);
            dataVersions.Count.Should().Be(1);
            CheckV1Dataset(dataVersions[0], createdDataset);
        }).ConfigureAwait(false);
    }

    [Fact]
    public async Task TestUpdateV1Dataset()
    {
        string datasetName = Guid.NewGuid().ToString();
        string datasetVersion = "125";
        await DatasetVersionHelpers.CreateAndRegisterDatasetPublicBlob(Fixture, DatasetVersionController, datasetName, datasetVersion).ConfigureAwait(false);
        var v1Dataset = await DataVersionController.Get(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, datasetName, datasetVersion).ConfigureAwait(false);
        v1Dataset.Properties.Description = "Updated description";
        var updateV1Dataset = () => UpdateDataVersion(v1Dataset, datasetName, datasetVersion);
        await updateV1Dataset.Should().ThrowAsync<ServiceInvocationException>()
                .Where(ex => ex.Response.StatusCode == HttpStatusCode.BadRequest)
                .Where(ex => ex.Response.ReasonPhrase == "can't update v1 data from v2 api")
                .ConfigureAwait(false);
    }

    [Fact]
    public async Task TestCreateV1AndV2SameName()
    {
        // create v1 dataset
        string datasetName = Guid.NewGuid().ToString();
        string datasetVersion = "593";
        var createdV1Dataset = await DatasetVersionHelpers.CreateAndRegisterDatasetPublicBlob(Fixture, DatasetVersionController, datasetName, datasetVersion).ConfigureAwait(false);

        // create v2 dataset with same name should fail
        var createV2Data = () => _dataAssetHelpers.RegisterDataVersion(DataVersionController, datasetName, datasetVersion);
        await createV2Data.Should().ThrowAsync<ServiceInvocationException>()
                .Where(ex => ex.Response.StatusCode == HttpStatusCode.BadRequest)
                .Where(ex => ex.Response.ReasonPhrase.Contains("A data version with this name and version already exists", StringComparison.Ordinal))
                .ConfigureAwait(false);
    }

    [Fact]
    public async Task TestCreateV1VersionInV2Container()
    {
        // create v2 container
        string datasetName = Guid.NewGuid().ToString();
        string datasetVersion = "492";
        var dataContainerRequest = _dataAssetHelpers.MakeDataContainerRequest(datasetName);
        var v2Container = await DataContainerController.CreateOrUpdate(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, datasetName, dataContainerRequest).ConfigureAwait(false);

        // create v1 version in v2 container
        var createV1Version = () => DatasetVersionHelpers.CreateAndRegisterDatasetPublicBlob(Fixture, DatasetVersionController, datasetName, datasetVersion);
        await createV1Version.Should().ThrowAsync<ServiceInvocationException>()
                .Where(ex => ex.Response.StatusCode == HttpStatusCode.NotFound)
                .ConfigureAwait(false);
    }

    protected async Task<List<Resource<DataVersionBase>>> ListDataVersions(string name, string tags = null, string orderBy = null, int? top = null, ListViewType listViewType = ListViewType.ActiveOnly)
    {
        return await ControllerHelpers.List(skipToken => DataVersionController.List(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, name, orderBy, top, skipToken.EscapeDataStringOrNull(), tags, listViewType), _maxListCount).ToListAsync();
    }

    private async Task TestAcrossApiVersions(string name, string version)
    {
        await CrossVersionHelpers.TestResourceAcrossApiVersions<IDataVersionController>(
                Fixture,
                controller => controller.Get(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, name, version),
                version => version == MfeApiVersions.May2022)
            .ConfigureAwait(false);
    }

    private async Task UpdateDataVersion(Resource<DataVersionBase> createdDataset, string datasetName, string datasetVersion)
    {
        var updatedDataset = await DataVersionController.CreateOrUpdate(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, datasetName, datasetVersion, createdDataset).ConfigureAwait(false);
        _dataAssetHelpers.DataVersionsAreEqual(updatedDataset.Properties, createdDataset.Properties);
    }

    private void CheckV1Dataset(Resource<DataVersionBase> actual, Contracts.V20211001.ArmCommon.Resource<DatasetVersion> expected)
    {
        actual.Name.Should().Be(expected.Name);
        actual.SystemData.CreatedBy.Should().NotBeNullOrEmpty();
        actual.SystemData.CreatedByType.Should().Be(SystemData.UserType.Application);
        actual.SystemData.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, 5.Minutes());
        actual.SystemData.LastModifiedAt.Should().BeCloseTo(DateTime.UtcNow, 5.Minutes());
        var actualProperties = new Dictionary<string, string>(actual.Properties.Properties);
        actualProperties.Remove("v1_type");
        actualProperties.Should().BeEquivalentTo(expected.Properties.Properties);
        actual.Properties.Description.Should().Be(expected.Properties.Description);
        actual.Properties.Tags.Should().BeEquivalentTo(expected.Properties.Tags);
        actual.Properties.DataUri.Should().Be(expected.Properties.Paths[0].File);
        actual.Properties.IsAnonymous.Should().Be(expected.Properties.IsAnonymous);
        actual.Properties.IsArchived.Should().Be(expected.Properties.IsArchived);
    }
}