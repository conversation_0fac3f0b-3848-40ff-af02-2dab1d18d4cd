﻿// <copyright file="ModelPackageTests.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Threading.Tasks;
using Microsoft.MachineLearning.Common.Core;
using Microsoft.MachineLearning.Common.Testing;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20230601Preview.Controllers;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20230601Preview.Package;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Api;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.ControllerTests.V20230601Preview.Helpers;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.XunitExtensions;
using Xunit;

namespace Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.ControllerTests.V20230601Preview
{
    [MfeTest(TestCategory.Integration, ScenarioScope.Models)]
    public class ModelPackageTests : IStaticFixture<TestFixtureManagementFrontEnd>
    {
        private readonly IModelVersionController _packageController;

        public ModelPackageTests(
            StaticFixture<TestFixtureManagementFrontEnd> fixture)
        {
            Fixture = fixture;
            _packageController = Fixture.ManagementFrontEndClient.GetController<IModelVersionController>();
        }

        public TestFixtureManagementFrontEnd Fixture { get; }

        [Theory(Skip = "EMS testing some newer approach for image builds, int region is impacted")]
        [InlineData(true)]
        [InlineData(false)]
        public async Task TestModelPackage(bool isNCDFlow)
        {
            string targetEnvironmentName = null;
            string targetEnvironmentVersion = null;

            try
            {
                var (modelDetails, packageRequest) = ModelPackageHelpers.GetPackageRequest(isNCDFlow);

                var lro = await _packageController.Package(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, modelDetails.Item1, modelDetails.Item2, packageRequest).ConfigureAwait(false);
                var pollResult = await lro.PollAsyncOperationUntilComplete().ConfigureAwait(false);
                Assert.NotNull(pollResult);
                Assert.Equal(PackageBuildState.Succeeded, pollResult.BuildState);
                Assert.NotEmpty(pollResult.TargetEnvironmentId);

                if (AssetId.TryParse(pollResult.TargetEnvironmentId, out var asset))
                {
                    targetEnvironmentName = asset.Name;
                    targetEnvironmentVersion = asset.Version;
                }
            }
            finally
            {
                await NonVersionedHelpers.ModelPackageHelpers.CleanUpImageNoThrow(Fixture, targetEnvironmentName, targetEnvironmentVersion).ConfigureAwait(false);
            }
        }
    }
}
