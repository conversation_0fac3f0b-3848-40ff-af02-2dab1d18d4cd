﻿// <copyright file="ModelPackageHelpers.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20230601Preview.ArmCommon;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20230601Preview.Package;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.ControllerTests.V20230601Preview.Helpers
{
    public static class ModelPackageHelpers
    {
        public static async Task<PackageResponse> PollAsyncOperationUntilComplete(
            this HttpResponseMessage responseMessage,
            TimeSpan? delaySeconds = null,
            CancellationToken cancellationToken = default)
        {
            using (var httpClient = new HttpClient())
            {
                while (true)
                {
                    cancellationToken.ThrowIfCancellationRequested();
                    var statusResult = await GetOperationStatus(httpClient, responseMessage, cancellationToken).ConfigureAwait(false);
                    if (IsTerminalState(statusResult.Status))
                    {
                        return await GetOperationResult(httpClient, responseMessage, cancellationToken).ConfigureAwait(false);
                    }

                    cancellationToken.ThrowIfCancellationRequested();

                    await Task.Delay(delaySeconds ?? TimeSpan.FromSeconds(3), cancellationToken).ConfigureAwait(false);
                }
            }
        }

        public static PackageRequest GetRegistryPackageRequest(bool isNcd)
        {
            string environmentName = isNcd ? "mfe-ncd-registry-packaging-test-V20230601Preview" : "mfe-registry-packaging-test-V20230601Preview";
            return isNcd ?
                new PackageRequest()
                {
                    TargetEnvironmentId = $"azureml://locations/centraluseuap/workspaces/4855029c-7583-4434-aa26-4212fb3b02d2/environments/{environmentName}",
                    InferencingServer = new AzureMLOnlineInferencingServer()
                }
                : new PackageRequest() { };
        }

        public static Tuple<Tuple<string, string>, PackageRequest> GetPackageRequest(bool isNcd)
        {
            string modelName = isNcd ? "mlflow-package" : "model-package-test";
            string modelVersion = "1";
            string environmentName = isNcd ? "mfe-ncd-packaging-test-V20230601Preview" : "mfe-packaging-test-V20230601Preview";

            PackageRequest packageRequest = isNcd ?
                new PackageRequest()
                {
                    TargetEnvironmentId = $"azureml://locations/centraluseuap/workspaces/4855029c-7583-4434-aa26-4212fb3b02d2/environments/{environmentName}",
                    InferencingServer = new AzureMLOnlineInferencingServer()
                }
                : new PackageRequest()
                {
                    TargetEnvironmentId = $"azureml://locations/centraluseuap/workspaces/4855029c-7583-4434-aa26-4212fb3b02d2/environments/{environmentName}",
                    InferencingServer = new AzureMLOnlineInferencingServer()
                    {
                        CodeConfiguration = new Contracts.V20230601Preview.CodeConfiguration()
                        {
                            CodeId = "azureml://subscriptions/4bf6b28a-452b-4af4-8080-8a196ee0ca4b/resourceGroups/int-shared-static-test-resources/providers/Microsoft.MachineLearningServices/workspaces/servicestestworkspace/codes/scorescript/versions/1",
                            ScoringScript = "score.py"
                        }
                    },
                    BaseEnvironmentSource = new BaseEnvironmentId()
                    {
                        ResourceId = "azureml://subscriptions/4bf6b28a-452b-4af4-8080-8a196ee0ca4b/resourceGroups/int-shared-static-test-resources/providers/Microsoft.MachineLearningServices/workspaces/servicestestworkspace/environments/model-pkg-env/versions/1"
                    },
                    ModelConfiguration = new ModelConfiguration()
                    {
                        Mode = PackageInputDeliveryMode.Download,
                        MountPath = "./sklearn_regression_model.pkl"
                    },
                    Inputs = new List<ModelPackageInput>()
                     {
                        new ModelPackageInput()
                        {
                            InputType = PackageInputType.UriFolder,
                            Mode = PackageInputDeliveryMode.Copy,
                            MountPath = ".",
                            Path = new PackageInputPathId()
                            {
                                ResourceId = "azureml://subscriptions/4bf6b28a-452b-4af4-8080-8a196ee0ca4b/resourceGroups/int-shared-static-test-resources/providers/Microsoft.MachineLearningServices/workspaces/servicestestworkspace/datastores/model_package_inputs/paths/files/",
                            }
                        },
                        new ModelPackageInput()
                        {
                            InputType = PackageInputType.UriFolder,
                            Mode = PackageInputDeliveryMode.Copy,
                            MountPath = ".",
                            Path = new PackageInputPathId()
                            {
                                ResourceId = "azureml://subscriptions/4bf6b28a-452b-4af4-8080-8a196ee0ca4b/resourceGroups/int-shared-static-test-resources/providers/Microsoft.MachineLearningServices/workspaces/servicestestworkspace/datastores/model_package_inputs/paths/scripts/",
                            }
                        },
                        new ModelPackageInput()
                        {
                            InputType = PackageInputType.UriFile,
                            Mode = PackageInputDeliveryMode.Copy,
                            MountPath = ".",
                            Path = new PackageInputPathId()
                            {
                                ResourceId = "azureml://subscriptions/4bf6b28a-452b-4af4-8080-8a196ee0ca4b/resourceGroups/int-shared-static-test-resources/providers/Microsoft.MachineLearningServices/workspaces/servicestestworkspace/datastores/model_package_inputs/paths/extra.py",
                            }
                        },
                        new ModelPackageInput()
                        {
                            InputType = PackageInputType.UriFolder,
                            Mode = PackageInputDeliveryMode.Download,
                            MountPath = "do_not_copy.txt",
                            Path = new PackageInputPathId()
                            {
                                ResourceId = "azureml://subscriptions/4bf6b28a-452b-4af4-8080-8a196ee0ca4b/resourceGroups/int-shared-static-test-resources/providers/Microsoft.MachineLearningServices/workspaces/servicestestworkspace/datastores/model_package_inputs/paths/do_not_copy.txt",
                            }
                        }
                     }
                };
            return Tuple.Create(Tuple.Create(modelName, modelVersion), packageRequest);
        }

        private static async Task<AzureAsyncOperationResource<PackageBuildState>> GetOperationStatus(HttpClient httpClient, HttpResponseMessage responseMessage, CancellationToken cancellationToken = default)
        {
            var result = new AzureAsyncOperationResource<PackageBuildState>();
            if (responseMessage.Headers.TryGetValues(InternalSharedServices.Contracts.Common.MfeConstants.AzureAsyncOperation, out var values))
            {
                var response = await httpClient.SendAsync(new HttpRequestMessage(HttpMethod.Get, values.First()), cancellationToken).ConfigureAwait(false);
                if (response.StatusCode != HttpStatusCode.OK)
                {
                    throw new Exception($"Unexpected HttpStatusCode {response.StatusCode} when polling Package Status AsyncOperation.");
                }
                var content = await response.Content.ReadAsStringAsync().ConfigureAwait(false);
                result = JsonConvert.DeserializeObject<AzureAsyncOperationResource<PackageBuildState>>(content);
            }
            return result;
        }

        private static async Task<PackageResponse> GetOperationResult(HttpClient httpClient, HttpResponseMessage responseMessage, CancellationToken cancellationToken = default)
        {
            var result = new PackageResponse();
            if (responseMessage.Headers.TryGetValues("Location", out var values))
            {
                var response = await httpClient.SendAsync(new HttpRequestMessage(HttpMethod.Get, values.First()), cancellationToken).ConfigureAwait(false);
                var responseContent = await response.Content.ReadAsStringAsync().ConfigureAwait(false);
                result = ConvertToPackageResponse(responseContent);
            }
            return result;
        }

        private static PackageResponse ConvertToPackageResponse(string content)
        {
            var result = new PackageResponse();
            if (!string.IsNullOrEmpty(content))
            {
                var jObject = JObject.Parse(content);
                if (jObject != null)
                {
                    result.TargetEnvironmentId = GetProperty<string>("targetEnvironmentId", jObject);
                    result.BuildState = Enum.TryParse(typeof(PackageBuildState), GetProperty<string>("buildState", jObject), true, out var buildState) ?
                       (PackageBuildState)buildState : PackageBuildState.NotStarted;
                }
            }

            return result;
        }

        private static T GetProperty<T>(string propertyName, JObject jObject)
        {
            T result = default(T);
            if (jObject != null && !string.IsNullOrEmpty(propertyName))
            {
                result = jObject.ContainsKey(propertyName) ? jObject[propertyName].Value<T>() : default(T);
            }
            return result;
        }

        private static bool IsTerminalState(PackageBuildState state)
        {
            return state == PackageBuildState.Succeeded || state == PackageBuildState.Failed;
        }
    }
}
