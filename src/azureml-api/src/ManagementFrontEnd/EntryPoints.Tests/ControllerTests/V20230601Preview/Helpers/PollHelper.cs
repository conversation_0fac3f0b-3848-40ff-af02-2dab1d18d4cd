﻿// <copyright file="PollHelper.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.MachineLearning.Common.Core;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20230601Preview.ArmCommon;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20230601Preview.Assets;
using Newtonsoft.Json;
using Xunit;

namespace Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.ControllerTests.V20230601Preview.Helpers
{
    public static class PollHelper
    {
        public static async Task<AssetProvisioningState> PollAsyncOperationUntilComplete(
           string location,
           TimeSpan? delaySeconds = null,
           CancellationToken cancellationToken = default)
        {
            var httpClient = new HttpClient();

            while (true)
            {
                cancellationToken.ThrowIfCancellationRequested();

                var response = await httpClient.SendAsync(new HttpRequestMessage(HttpMethod.Get, location), cancellationToken).ConfigureAwait(false);
                if (response.StatusCode != HttpStatusCode.OK)
                {
                    throw new Exception($"Unexpected HttpStatusCode {response.StatusCode} when polling Azure-AsyncOperation.");
                }

                var content = await response.Content.ReadAsStringAsync().ConfigureAwait(false);
                var result = JsonConvert.DeserializeObject<AzureAsyncOperationResource<AssetProvisioningState>>(content);
                if (IsTerminalState(result.Status))
                {
                    return result.Status;
                }

                cancellationToken.ThrowIfCancellationRequested();

                await Task.Delay(delaySeconds ?? TimeSpan.FromSeconds(3), cancellationToken).ConfigureAwait(false);
            }
        }

        public static async Task PollAsyncOperationUntilComplete(
            HttpResponseMessage response,
            AssetProvisioningState expectedState = AssetProvisioningState.Succeeded)
        {
            Assert.True(response.Headers.TryGetValues(InternalSharedServices.Contracts.Common.MfeConstants.AzureAsyncOperation, out var values));
            var status = await PollAsyncOperationUntilComplete(values.First()).ConfigureAwait(false);
            Assert.Equal(expectedState, status);
        }

        public static async Task<Resource<T>> PollGetUntilComplete<T>(
            Func<Task<Resource<T>>> getEntity,
            TimeSpan? delaySeconds = null,
            CancellationToken cancellationToken = default)
            where T : IAsynchronousResource<AssetProvisioningState>
        {
            while (true)
            {
                cancellationToken.ThrowIfCancellationRequested();

                try
                {
                    var entity = await getEntity().ConfigureAwait(false);
                    if (IsTerminalState(entity.Properties.ProvisioningState))
                    {
                        return entity;
                    }
                }
                catch (ServiceInvocationException e) when (e.Response.StatusCode == HttpStatusCode.NotFound)
                {
                }

                cancellationToken.ThrowIfCancellationRequested();

                await Task.Delay(delaySeconds ?? TimeSpan.FromSeconds(3), cancellationToken).ConfigureAwait(false);
            }
        }

        private static bool IsTerminalState(AssetProvisioningState state)
        {
            return state == AssetProvisioningState.Succeeded ||
                   state == AssetProvisioningState.Failed ||
                   state == AssetProvisioningState.Canceled;
        }
    }
}
